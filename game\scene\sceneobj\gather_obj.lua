SPECIAL_GATHER_TYPE =
{
	JINGHUA = 1,				-- 精华采集
	GUILDBATTLE = 2,			-- 公会争霸采集物
	ShenyuanBoss_Box = 3, 		-- 深渊boss国家归属宝箱
	HERBS = 4,					-- 草药
	GUILD_BONFIRE = 5,			-- 仙盟篝火
	CROSS_FISHING = 6,			-- 钓鱼鱼篓
	CAMP_BATTLE = 7,			-- 跨服六界宝箱
	GHOST_FB_EVENT = 8,			-- 捉鬼副本 - 事件采集物
	GHOST_FB_EVENT_FINISH = 9,	-- 抓鬼副本 - 事件完成采集物
	CROSS_FLAG_BATTLE_CONTEND = 10,-- 跨服夺旗战场 - 争夺点
	CROSS_TREASURE_TYPE = 11,	-- 跨服藏宝灵珠
	CROSS_TREASURE_BEAST_TYPE = 12,	-- 跨服幻兽
	ORDEAL_BOX = 13, 			-- 渡劫奖励
}

GATHER_AMIN_STATUS = {
	BEFORE = 1, 				-- 采集前动画状态
	GATHERING = 2, 				-- 采集中动画状态
	END = 3, 					-- 采集后动画状态
}


GatherObj = GatherObj or BaseClass(SceneObj)
local TANGGUO = 123 --糖果
local GUIDE_PET_EGG = 308 --引导宠物蛋
function GatherObj:__init(item_vo)
	self.obj_type = SceneObjType.GatherObj
	self.followui_class = GatherFollow
	self.draw_obj:SetObjType(self.obj_type)
	self:SetObjId(item_vo.obj_id)
	self.rotation_y = 0
	self.gather_status = GATHER_STATUS.NORMAL
	self.need_check_lately = false
	self.gather_name = ""
	self.name_is_flush = false
end

function GatherObj:__delete()
	if self.hide_timer then
		GlobalTimerQuest:CancelQuest(self.hide_timer)
		self.hide_timer = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	if self.stop_gather_handler then
		GlobalEventSystem:UnBind(self.stop_gather_handler)
		self.stop_gather_handler = nil
	end

	if nil ~= self.select_effect then
		self.select_effect:DeleteMe()
		self.select_effect = nil
	end

	if self.tuten_effect_async_loader then
		self.tuten_effect_async_loader:DeleteMe()
		self.tuten_effect_async_loader = nil
	end

	self.gather_status = GATHER_STATUS.NORMAL
	self.gather_name = ""
	self.name_is_flush = false

	if self.check_wangqi_status_fun then
		GlobalEventSystem:UnBind(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.check_wangqi_status_fun)
		self.check_wangqi_status_fun = nil
	end
	self:RemoveShowCatchDelayTimer()
	self:SetSceneWangqiStatus()
end

function GatherObj:Update(now_time, elapse_time)
	SceneObj.Update(self, now_time, elapse_time)

	local need_set = false
	if self.name_is_flush and self.gather_name ~= nil and self.vo ~= nil and ((self.vo.disappear_time ~= nil and self.vo.disappear_time > 0) 
	or (self.vo.can_gather_time ~= nil and self.vo.can_gather_time > 0)) then
		local cur_time = TimeWGCtrl.Instance:GetServerTime()
		if (cur_time < self.vo.disappear_time) or (cur_time < self.vo.can_gather_time) then
			local time = 0 
			if self.vo.disappear_time > 0 then
				time = self.vo.disappear_time - cur_time
			else
				time = self.vo.can_gather_time - cur_time
			end
			
			local time_str = ""
			if time > 3600 then
				time_str = TimeUtil.FormatSecond(time)
			else
				time_str = TimeUtil.FormatSecond(time, 2)
			end

			if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_TYPE then
				local time_text = time_str .. Language.Common.StrCanGather
				self.vo.name = self.gather_name .. '\n' .. ToColorStr(time_text ,COLOR3B.RED)
			else
				local time_text = time_str .. Language.Common.StrGatherDel
				self.vo.name = self.gather_name .. '\n' .. ToColorStr(time_text ,COLOR3B.RED)
			end

			need_set = true
		else
			self.vo.name = self.gather_name
			self.name_is_flush = false
			need_set = true
		end
	end

	if need_set then
		if self.follow_ui ~= nil then
			self.follow_ui:SetName(self.vo.name)
		end
	end
end

function GatherObj:InitInfo()
	SceneObj.InitInfo(self)
	local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[self.vo.gather_id]
	if nil == gather_config then
		print_log("gather_config not find, gather_id:" .. self.vo.gather_id)
		return
	end
	self.gather_config = gather_config

	self.need_check_lately = gather_config.active_gather == 1
	self.vo.name = gather_config.show_name
	if Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN then
		self.vo.name = ToColorStr(self.vo.param2,"#7cffb2") .. "\n" .. gather_config.show_name
	end

	if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_FISHING then
        self.vo.name = self.vo.param2 .. "·" .. self.vo.name
    elseif self.vo.special_gather_type == SPECIAL_GATHER_TYPE.ShenyuanBoss_Box then
        local server_group_seq = self.vo.param 
		-- 改成区服
        if server_group_seq then
            local serve_group_str = self.vo.name .. string.format(Language.Common.ServerIdFormat, server_group_seq)
            self.vo.name = serve_group_str
        end
	elseif self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_TYPE then
		local u_id = RoleWGData.Instance:InCrossGetOriginUid()
		local seq = self.vo.param % 100
		local level = math.floor(self.vo.param5 / 100)
		local cfg = CrossTreasureWGData.Instance:GetTreasureCfgBySeq(seq)
		if cfg and cfg.type ~= 1 and self.vo.param4 ~= u_id then		-- 玩家自身看到的区分炸弹，其他玩家
			seq = 0
			level = 3
		end

		local cfg = CrossTreasureWGData.Instance:GetAllTreasureLevelCfgBySeqLevel(seq, level)
		if cfg then
			local name = string.format("%s%s%s", self.vo.param3, Language.Common.De, ToColorStr(cfg.name, ITEM_COLOR[cfg.color]))
			self.vo.name = name
		end
	elseif self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_BEAST_TYPE then
		local pool_id = self.vo.param4 / 1000
		local seq = self.vo.param4 % 100
		local cfg = CrossTreasureWGData.Instance:GetBeastPoolCfgByIdSeq(pool_id, seq)

		if cfg then
			local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(cfg.beast_id)
			if beast_cfg then
				self.vo.name = string.format(Language.CrossTreasure.BeastGatherTitleTips, beast_cfg.beast_name)
			end
		end
	end
	self.resid = gather_config.resid
	self.scale = gather_config.scale
	self.rotation_y = gather_config.rotation or 0
	self.beauty_res = gather_config.beauty_res or 0
	self.monster_res = gather_config.monster_res or 0

	self:_SetGatherStatus(GATHER_STATUS.NORMAL)

	self.gather_name = self.vo.name
	self.name_is_flush = false
	if self.vo ~= nil and ((self.vo.disappear_time ~= nil and self.vo.disappear_time > 0) 
	or (self.vo.can_gather_time ~= nil and self.vo.can_gather_time > 0)) then
		local cur_time = TimeWGCtrl.Instance:GetServerTime()
		if (cur_time < self.vo.disappear_time) or (cur_time < self.vo.can_gather_time) then
			self.name_is_flush = true
		end
	end

	self.check_wangqi_status_fun = BindTool.Bind(self.CheckSceneWangqiStatus, self)
	GlobalEventSystem:Bind(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.check_wangqi_status_fun)
end

function GatherObj:InitAppearance()
	local scene_type = Scene.Instance:GetSceneType()
	if SceneType.HunYanFb == scene_type and MarryWGData.Instance:GetIsHasGather(self.obj_id) then
		self.resid = MarryGatherId or 0
	end

	if self.beauty_res and self.beauty_res ~= 0 then
		self:ChangeModel(SceneObjPart.Main, ResPath.GetGoddessNotLModel(self.beauty_res))
	elseif self.monster_res ~= 0 then
		self:ChangeModel(SceneObjPart.Main, ResPath.GetMonsterModel(self.monster_res))
	elseif self.resid ~= "" and self.resid ~= 0 then
		if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_BEAST_TYPE then
			self:ChangeModel(SceneObjPart.Main, ResPath.GetBeastsModel(self.resid))
		else
			self:ChangeModel(SceneObjPart.Main, ResPath.GetGatherModel(self.resid))
		end
	end
	if self.scale then
		local transform = self.draw_obj:GetRoot().transform
		transform.localScale = Vector3(self.scale, self.scale, self.scale)
	end

	if self.rotation_y ~= 0 then
		self.draw_obj:Rotate(0, self.rotation_y, 0)
	end

	if self.gather_config and self.gather_config.gather_effect and self.gather_config.gather_effect ~= "" then
		self:ChangeModel(SceneObjPart.Particle, ResPath.GetEnvironmentCommonEffect(self.gather_config.gather_effect))
	end
end


function GatherObj:SetGatherTrigger(index)
	if not index then
		return
	end
	local draw_obj = self:GetDrawObj()
	if draw_obj then
		local part = draw_obj:GetPart(SceneObjPart.Main)
		if part then
			part:SetInteger(ANIMATOR_PARAM.STATUS, index)
			if index == ActionStatus.Die then
				self:SetIsCanClick(false)
				if part:GetObj() and part:GetObj().animator then
					part:GetObj().animator:WaitEvent("die_exit", function ()
						Scene.Instance:DeleteObj(self.obj_id)
					end)
				end
			end
		end
	end
end

function GatherObj:SetGatherParticleTrigger(status)
	if not status then
		return
	end
	local draw_obj = self:GetDrawObj()
	if draw_obj then
		local part = draw_obj:GetPart(SceneObjPart.Particle)
		if part then
			part:SetInteger(ANIMATOR_PARAM.STATUS, status)
		end
	end
end

function GatherObj:SetEffectShow(bundle, asset)
	local transform = self.draw_obj:GetRoot().transform
	local async_loader = AllocAsyncLoader(self, "GatherEffect")
	async_loader:SetIsInQueueLoad(true)
	async_loader:SetIsUseObjPool(true)
	async_loader:SetParent(transform)
	async_loader:Load(bundle, asset)
end

function GatherObj:OnModelLoaded(part, obj)
    SceneObj.OnModelLoaded(self, part, obj)
    if part == SceneObjPart.Main then
    	if TaskGuide.Instance:CheckHideGather(self.vo.gather_id) or (self.gather_config and self.gather_config.visual == 0) then
    		self:VisibleChanged(false)
			local follow_ui = self:GetFollowUi()
			follow_ui:ForceSetVisible(false)
    	end

    	if self.vo.gather_id == GUIDE_PET_EGG and obj.animator then
			obj.animator:CrossFadeInFixedTime(SceneObjAnimator.doudong_1, 0.2)
			local follow_ui = self:GetFollowUi()
			follow_ui:SetNameTextOffY(-50)
			follow_ui:ForceSetVisible(true)
			follow_ui:SetFloatIconAsset("uis/images/others_atlas","arrow_12")
		elseif self.vo.gather_id == TANGGUO then
			local follow_ui = self:GetFollowUi()
			follow_ui:SetNameTextOffY(35)
		end
    end

	if part == SceneObjPart.Particle then
		obj.gameObject:SetActive(true)
		self:SetIsDisableAllAttachEffects(false)
		self:SetGatherParticleTrigger(GATHER_AMIN_STATUS.BEFORE)
	end

	if self.follow_ui ~= nil then
		self.follow_ui:SetName(self.vo.name)
	end
	-- 设置望气
	self:CheckSceneWangqiStatus()
end

function GatherObj:OnEnterScene()
	SceneObj.OnEnterScene(self)
	self:GetFollowUi()
	if Scene.Instance:GetSceneType() == SceneType.KFSHENYUN_FB then
		self.stop_gather_handler = GlobalEventSystem:Bind(ObjectEventType.STOP_GATHER, BindTool.Bind(self.FlushTuTengInfo, self))
		self:FlushTuTengInfo()
	end
	self:PlayAction()
	self:CheckBoxBoss()
	if self.draw_obj then
		self.draw_obj:SetWaterHeight(COMMON_CONSTS.WATER_HEIGHT)
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic then
			local flag = scene_logic:IsCanCheckWaterArea() and true or false
			self.draw_obj:SetCheckWater(flag)
		end

		local secne_type = Scene.Instance:GetSceneType()
		if secne_type == SceneType.HotSpring or secne_type == SceneType.KF_HotSpring then
			self.draw_obj:SetOffset(Vector3(0, 1.51, 0))
		end
	end
end

function GatherObj:GetGatherId()
	return self.vo.gather_id
end

function GatherObj:IsGather()
	return true
end

function GatherObj:PlayAction()
	local draw_obj = self:GetDrawObj()
	if draw_obj then
		local part = draw_obj:GetPart(SceneObjPart.Main)
		if part then
			part:SetTrigger(SceneObjAnimator.Rest)
			if self.time_quest then
				GlobalTimerQuest:CancelQuest(self.time_quest)
			end
			self.time_quest = GlobalTimerQuest:AddDelayTimer(function() self:PlayAction() end, 10)
		end
	end
end

-- 望气展示效果
function GatherObj:CheckSceneWangqiStatus(is_wangqi)
	if self.vo == nil then
		return
	end

	local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[self.vo.gather_id]
	if nil == gather_config then
		print_log("gather_config not find, gather_id:" .. self.vo.gather_id)
		return
	end

	local is_now_wangqi = is_wangqi or Scene.Instance:IsEnterWangQiStatus()

	if gather_config.is_wangqi_active == 1 then
		local u_id = RoleWGData.Instance:InCrossGetOriginUid()
		-- 被采集中不能隐藏
		local is_gather_ing = self.gather_status == GATHER_AMIN_STATUS.GATHERING
		if is_gather_ing or (self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_TYPE and self.vo.param4 == u_id) then
			self:VisibleChanged(true)

			if self.follow_ui ~= nil then
				self.follow_ui:VisibleChanged(true)
			end
		else
			self:VisibleChanged(is_now_wangqi)

			if self.follow_ui ~= nil then
				self.follow_ui:VisibleChanged(is_now_wangqi)
			end
		end
	else
		if self.follow_ui ~= nil then
			self.follow_ui:VisibleChanged(true)
		end
	end

	-- --是否望气显示
	-- if gather_config.is_wangqi_active == 1 then
	-- 	self:VisibleChanged(is_now_wangqi)
	-- end

	-- 是否望气高亮
	if gather_config.is_wangqi_effect == 1 then
		self:SetSceneWangqiStatus(nil, is_now_wangqi)
	end
end

local Interactable_layer = 12
-- 重置物体的layer层
function GatherObj:SetSceneWangqiStatus(is_reset, is_wangqi)
	local draw_obj = self:GetDrawObj()
	local part = draw_obj:GetPart(SceneObjPart.Main)
	if part then
		if is_reset or (not is_wangqi) then
			part:TrySetMainDefaultGameObjectLayer()
		else
			-- local InteractableLayer = UnityEngine.LayerMask.NameToLayer("Interactable")
			part:TrySetMainGameObjectLayer(Interactable_layer)
		end
	end
end

-------------------------重写-------------------------------------------
function GatherObj:CreateFollowUi()
 	local obj_type = 0
 	if Scene.Instance:GetSceneType() == SceneType.KFSHENYUN_FB then
 		obj_type = SceneObjType.Role
 	else
		if self.vo.npc_id then
			obj_type = SceneObjType.Npc
		elseif self.vo.gather_id then
			obj_type = SceneObjType.GatherObj
		end
	end
	
	SceneObj.CreateFollowUi(self, obj_type)
 end

 function GatherObj:FlushTuTengInfo()
 	local obj_cfg = BossWGData.Instance:GetTuTengObj(self.obj_id)
	if next(obj_cfg) then
		if obj_cfg.owner_id > 0 then
			self:GetFollowUi():SetFaceIcon(true,"uis/uires/res/x1ui/boss_atlas","occ")
			self:GetFollowUi():SetSpecialImage(false)
		else
			self:GetFollowUi():SetFaceIcon(true,"uis/uires/res/x1ui/boss_atlas","un_occ")
			self:GetFollowUi():SetSpecialImage(true,"uis/uires/res/x1ui/boss_atlas","click_occupy")
		end
	end
 end

function GatherObj:OnClick()
	SceneObj.OnClick(self)
	if nil == self.select_effect then
		self.select_effect = AllocAsyncLoader(self, "select_effect")
		self.select_effect:SetIsUseObjPool(true)
		self.select_effect:SetParent(self.draw_obj:GetRoot().transform)
		local bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_xuanzhong")
		self.select_effect:Load(bundle, asset, function ( obj )
			if IsNil(obj) then return end
			if self.scale and self.vo.gather_id == TANGGUO then
				local transform = obj.transform
				transform.localScale = Vector3(1 / self.scale, 1 / self.scale, 1 / self.scale)
			end
		end)
	end
	if self.gather_config.need_select_effect > 0 then
		self.select_effect:SetActive(true)
	else
		self.select_effect:SetActive(false)
	end
	--糖果选中特效修改

end

function GatherObj:CancelSelect()
	-- SceneObj.CancelSelect(self)
	if SceneObj.select_obj == self then
		SceneObj.select_obj = nil
	end
	self.is_select = false
	if nil ~= self.select_effect then
		self.select_effect:SetActive(false)
	end
end

function GatherObj:CheckBoxBoss()
	local scene_type = Scene.Instance:GetSceneType()
	if SceneType.SG_BOSS ~= scene_type then
		return
	end

	local gather_id = self.vo and self.vo.gather_id
	if gather_id then
		if BossWGData.Instance:GetIsGatherBoxAngry(gather_id) then
			local angry = BossWGData.Instance:GetGatherBoxAngry(gather_id) or 0
			local follow_ui = self:GetFollowUi()
			if follow_ui then
				local bundle_name, asset_name = ResPath.GetBossAngryIcon()
				follow_ui:SetBoxBossAngry(bundle_name, asset_name, angry)
			end
		end
	end
end

-- 采集开始
function GatherObj:OnStartGather(is_main_role)
	self:_SetGatherStatus(GATHER_STATUS.GATHERING, is_main_role)
	self:UpdateStartGaterStatus(true)
end

-- 采集取消
function GatherObj:OnStopGather(is_main_role)
	self:_SetGatherStatus(GATHER_STATUS.NORMAL, is_main_role)
	self:CheckSceneWangqiStatus()		--采集取消需要检测一下望气显示
	self:RemoveShowCatchDelayTimer()

	if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_BEAST_TYPE then
		if self.follow_ui ~= nil then
			self.follow_ui:ResumeBeastCatchMessage()
		end
	end
end

-- 采集结束
function GatherObj:GatherEnd(is_main_role)
	self:_SetGatherStatus(GATHER_STATUS.END, is_main_role)
	self:CheckSceneWangqiStatus()		--采集完成需要检测一下望气显示
	self:RemoveShowCatchDelayTimer()

	if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_BEAST_TYPE then
		if self.follow_ui ~= nil then
			self.follow_ui:ResumeBeastCatchMessage()
		end
	elseif self.vo.special_gather_type == SPECIAL_GATHER_TYPE.ORDEAL_BOX then  -- 渡劫奖励
		local main_role = Scene.Instance:GetMainRole()
		--是自身的奖励 自己不用给自己发
		if main_role:GetName() == self.vo.param3 then
			return
		end
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local has_key = PlayerPrefsUtil.HasKey("dujie_gather_" .. self.vo.param3 .. open_day)
		
		-- 每个人每天只发一次感谢
		if not has_key then
			PlayerPrefsUtil.SetInt("dujie_gather_" .. self.vo.param3 .. open_day, 1)
			local thanks_msg = DujieWGData.Instance:GetThanksMsg()
			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD,string.format(thanks_msg.desc,self.vo.param3) , CHAT_CONTENT_TYPE.TEXT ,nil, nil, true)
		end
	end

end

function GatherObj:_SetGatherStatus(gather_status, is_main_role)
	if not self.draw_obj or not self.draw_obj:GetRoot() or not self.gather_config then
		return
	end

	if self.gather_status == GATHER_STATUS.END then
		return
	end 

	self.gather_status = gather_status

	if is_main_role then
		if self.gather_status == GATHER_STATUS.NORMAL then
			self:SetGatherParticleTrigger(GATHER_AMIN_STATUS.BEFORE)
		elseif self.gather_status == GATHER_STATUS.GATHERING then
			self:SetGatherParticleTrigger(GATHER_AMIN_STATUS.GATHERING)
			
			if self.gather_config and self.gather_config.gathering_anim_status and self.gather_config.gathering_anim_status ~= "" then
				self:SetGatherTrigger(tonumber(self.gather_config.gathering_anim_status))
			end
		elseif self.gather_status == GATHER_STATUS.END then
			self:SetGatherParticleTrigger(GATHER_AMIN_STATUS.END)
			
			if self.gather_config and self.gather_config.end_anim_status and self.gather_config.end_anim_status ~= "" then
				self:SetGatherTrigger(tonumber(self.gather_config.end_anim_status))
			end
		end
	end
end

 --剩余可采集次数
function GatherObj:GetGatherLeftTimes()
	local max_gather_times = self.vo.max_gather_times or 0
	local has_gather_times = self.vo.has_gather_times or 0
	return max_gather_times - has_gather_times
end

function GatherObj:GetIsNeedCheckLately()
	local can_show_lately = false
	local now_visible = false

	if self.draw_obj then
		now_visible = self.draw_obj:GetObjVisible()
	end

	can_show_lately = self.need_check_lately and now_visible
	local u_id = RoleWGData.Instance:InCrossGetOriginUid()

	if self:CheckIsCanGather() ~= 0 then
		can_show_lately = false
	end

	return can_show_lately
end

-- 检查测是否可以采集
function GatherObj:CheckIsCanGather()
	local u_id = RoleWGData.Instance:InCrossGetOriginUid()
	if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_TYPE then
		local cur_time = TimeWGCtrl.Instance:GetServerTime()

		if cur_time < self.vo.can_gather_time then
			return 1
		end

		local seq = self.vo.param % 100
		local cfg = CrossTreasureWGData.Instance:GetTreasureCfgBySeq(seq)
		local is_bomb = cfg and cfg.type ~= 1

		if IS_ON_CROSSSERVER then
			local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()
			if not base_cfg then
				return 0
			end

			local max_gather_times = base_cfg.gather_times
			local steal_num = CrossTreasureWGData.Instance:GetDailyStealedNum()
			local last_num = max_gather_times - steal_num
			local gather_last_time = math.floor(self.vo.param5 % 100)

			if last_num <= 0 and (not is_bomb) then
				return 3
			elseif gather_last_time <= 1 and (not is_bomb) then
				return 4
			end
		else
			if self.vo.param4 ~= u_id then
				return 2
			else
				if is_bomb then
					return 5
				end
			end
		end
	end

	return 0
end

-- 开始采集状态表现
function GatherObj:UpdateStartGaterStatus(is_reset)
	if self.vo.special_gather_type == SPECIAL_GATHER_TYPE.CROSS_TREASURE_BEAST_TYPE then
		if is_reset then
			self.gather_paragraph_index = 0
		else
			if self.gather_status ~= GATHER_STATUS.GATHERING then
				return
			end
		end
	
		self.gather_paragraph_index = self.gather_paragraph_index + 1
		if self.gather_paragraph_index > 3 then
			return
		end

		local progress, duration = self:GetStartGaterRandom(self.vo.param5 == 1)

		if self.follow_ui ~= nil then
			self.follow_ui:SetBeastCatchMessage(progress / 10, duration)
		end

		self:RemoveShowCatchDelayTimer()
		self.show_catch_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:UpdateStartGaterStatus()
		end, duration)
	end
end

--移除回调
function GatherObj:RemoveShowCatchDelayTimer()
    if self.show_catch_timer then
        GlobalTimerQuest:CancelQuest(self.show_catch_timer)
        self.show_catch_timer = nil
    end
end

-- 获取一段随机数
function GatherObj:GetStartGaterRandom(is_suc)
	local start_index = 6
	local end_index = 7
	local duration = 1.2

	if self.gather_paragraph_index == 2 then
		start_index = 7
		end_index = 9
		duration = 1
	elseif self.gather_paragraph_index == 3 then
		duration = 0.8
		if is_suc then
			return 10, duration
		else
			start_index = 2
			end_index = 4
		end	
	end

	local random_num = math.random()
	return start_index + ((end_index - start_index) * random_num), duration
end