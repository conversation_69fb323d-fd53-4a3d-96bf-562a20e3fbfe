function FuBenPanelView:InitZhuoGuiView()
    local node_num = self.node_list["zhuogui_fb_info_list"].transform.childCount
    for i = 1, node_num do
        local obj = self.node_list["zhuogui_fb_info_list"]:FindObj(i)
        obj.text.text = Language.ZhuoGuiFuBen.ZhuoGuiFuBenShuoming[i]
    end

    local other_cfg = FuBenPanelWGData.Instance:GetZhuoGuiOtherCfg()
    self.node_list["zhuogui_desc"].text.text = other_cfg.fb_des

    self.zhuogui_reward_list = AsyncListView.New(ItemCell, self.node_list["zhuogui_reward_list"])
    self.zhuogui_reward_list:SetStartZeroIndex(true)
    self.zhuogui_reward_list:SetDataList(other_cfg.show_reward_item)

    XUI.AddClickEventListener(self.node_list["btn_zhuogui_rule"], BindTool.Bind(self.OnClickZhuoGuiTips, self))
    XUI.AddClickEventListener(self.node_list["btn_zhuogui_go"], BindTool.Bind(self.OnClickZhuoGuiJion, self))
	XUI.AddClickEventListener(self.node_list["btn_zhuogui_task"], BindTool.Bind(self.OnClickOpenZhuoGuiTask, self))
    XUI.AddClickEventListener(self.node_list["zg_btn"], BindTool.Bind(self.OnClickZGClose, self))
    self.node_list.zg_name.text.text = Language.FuBenPanel.RuleTitle[TabIndex.fubenpanel_zhuogui]
end

function FuBenPanelView:DeleteZhuoGuiView()
    if self.zhuogui_reward_list then
        self.zhuogui_reward_list:DeleteMe()
        self.zhuogui_reward_list = nil
    end
end

function FuBenPanelView:OnClickZGClose()
    self.fb_index = -1
    self.node_list.zhuogui_right_msg.rect:DOAnchorPos(Vector2(500, 0), 0.3)
    ReDelayCall(self, function()
        self:OnTabChangeHandler(0)
    end, 0.3, "zg_tween")
end

function FuBenPanelView:FlushZhuoGuiView()
    local other_cfg = FuBenPanelWGData.Instance:GetZhuoGuiOtherCfg()
    local max_count = other_cfg.max_refresh_event_times
    local cur_count = max_count - FuBenPanelWGData.Instance:GetZhuoGuiEventTimes()
    local str_color = cur_count <= 0 and COLOR3B.RED or COLOR3B.BLUE_TITLE
    self.node_list["zhuogui_desc1"].text.text = ToColorStr(string.format(Language.FuBenPanel.ManHuangTimesStr, cur_count, max_count), str_color)

    local task_remind = FuBenPanelWGData.Instance:GetZhuoGuiTaskRemind()
    self.node_list["btn_zhuogui_remind"]:SetActive(task_remind == 1)
end

function FuBenPanelView:OnClickZhuoGuiTips()
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.FuBenPanel.RuleTitle[TabIndex.fubenpanel_zhuogui])
    role_tip:SetContent(Language.ZhuoGuiFuBen.ZhuoGuiDes)
end

function FuBenPanelView:OnClickZhuoGuiJion()
    -- local status = FuBenPanelWGData.Instance:GetZhuoGuiEventSatus()
    FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_GHOST_GLOBAL_FB)
end

function FuBenPanelView:OnClickOpenZhuoGuiTask()
    FuBenPanelWGCtrl.Instance:OpenZhuoGuiFubenTaskView()
end