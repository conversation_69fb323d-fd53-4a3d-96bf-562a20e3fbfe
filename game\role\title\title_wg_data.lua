-------------------------------------------
--主角称号数据
--------------------------------------------

TitleWGData = TitleWGData or BaseClass()

function TitleWGData:__init()
	if TitleWGData.Instance then
		ErrorLog("[TitleWGData] Attemp to create a singleton twice !")
	end
	TitleWGData.Instance = self

	self.title_id_list = {}
	self.title_used_list = {}
	self.title_type_list = {}
	self.yongyou_id_list = {}
	self.diy_title_name_list = {}
	self.saved_title_list = {}
	self.title_level_list = {}
	self.now_title_id = nil
	self.evil_title = 0
	local all_cfg = ConfigManager.Instance:GetAutoConfig("titleconfig_auto")
	self.title_list_cfg = all_cfg.title_list
	self.title_map = ListToMap(self.title_list_cfg, "title_id")
	self.title_cost_map = ListToMap(self.title_list_cfg, "item_id")
	self.diy_title_map_cfg = ListToMap(all_cfg.custom_title, "title_id")
	self.title_level_list_cfg = ListToMap(all_cfg.title_level, "title_id", "level")
	self.title_level_stuff_cfg = ListToMap(all_cfg.title_level, "stuff_id")
	self.other_cfg = all_cfg.other[1]


	RemindManager.Instance:Register(RemindName.PlayerTitle,BindTool.Bind(self.RoleTitleRemind,self))

	self:RegisterRoleTitleRemindInBag(RemindName.PlayerTitle)
end

function TitleWGData:__delete()
	self.title_type_list = nil
	RemindManager.Instance:UnRegister(RemindName.PlayerTitle)
	TitleWGData.Instance = nil

	self.title_level_list = nil
end

function TitleWGData:RegisterRoleTitleRemindInBag(remind_name)
    local map = {}

    for k,v in pairs(self.title_list_cfg) do
    	local item_id = v.item_id
    	if item_id ~= "" then
    		map[item_id] = true
    	end
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    for k, v in pairs(self.title_level_stuff_cfg) do
    	table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

--同步可用的称号
function TitleWGData:SetTitleIdList(title_id_list)
	self.title_id_list = title_id_list

	self.yongyou_id_list = {}
	for k,v in pairs(title_id_list) do
		self.yongyou_id_list[v] = true
	end
end

--称号等级（已激活得称号才有等级数据）
function TitleWGData:SetTitleLevelList(title_level_list)
	self.title_level_list = title_level_list
end

--获得称号等级
function TitleWGData:GetTitleLevelByTitleId(title_id)
	return self.title_level_list[title_id]
end

function TitleWGData:GetIsHasTitle(title_id)
	return self.yongyou_id_list[title_id] or false
end

function TitleWGData:GetTitleIdList()
	return self.title_id_list
end

-- 设置罪恶图标
function TitleWGData:SetEvilTitle(title_id)
	self.evil_title =  title_id
end

-- 获取罪恶图标
function TitleWGData:GetEvilTitle()
	return self.evil_title
end

--同步使用中的称号
function TitleWGData:SetUsedTitleId(title_used_list)
	if self.evil_title > 0 then
		if nil == self.title_used_list[1] or self.title_used_list[1] ~= self.evil_title then
			local title_count = 0
			for k,v in pairs(title_used_list) do
				if v > 0 then
					title_count = title_count + 1
				end
			end
			if title_count == 3 then
				title_used_list[1] = self.evil_title
			else
				table.insert(title_used_list, 1, self.evil_title)
			end
		end
	end
	self.title_used_list = title_used_list
end

--获取使用中的称号
function TitleWGData:GetUsedTitleId()
	return self.title_used_list
end

function TitleWGData:IsUsedTitle(title_id)
	for k,v in pairs(self.title_used_list) do
		if v == title_id then
			return true
		end
	end
	return false
end

function TitleWGData:GetIsTitle(title_id)
	local cfg = self.GetTitleConfig(title_id)
	return cfg ~= nil
end

--根据称号ID获取称号的配置
function TitleWGData.GetTitleConfig(title_id)
	return TitleWGData.Instance:GetConfig(title_id)
end

function TitleWGData:GetTitleConfigByItemId(item_id)
	return self.title_cost_map[item_id]
end

--根据称号ID获取称号的配置
function TitleWGData:GetConfig(title_id)
	return self.title_map[title_id]
end

function TitleWGData.GetTitleEffId(title_id)
	local config = TitleWGData.GetTitleConfig(title_id)
	if nil ~= config then
		return config.effid
	end
	return 0
end

--获取称号属性加成
function TitleWGData:GetTitleAttribute()
	local attr = {}
	attr.maxhp = 0
	attr.gongji = 0
	attr.fangyu = 0
	attr.fa_fangyu = 0
	attr.pojia = 0

	for k, v in pairs(self.title_id_list) do
		local config = TitleWGData.GetTitleConfig(v)
		if config then
			attr.fangyu = attr.fangyu + config.fangyu
			attr.maxhp = attr.maxhp + config.maxhp
			attr.gongji = attr.gongji + config.gongji
			attr.fa_fangyu = attr.fa_fangyu + config.fa_fangyu
			attr.pojia = attr.pojia + config.pojia
		end
	end

	local attribute = {}
	for k, v in pairs(attr) do
		if 0 ~= v then
			attribute[k] = v
		else
			attribute[k] = 0
		end
	end

	return attribute
end

--获取称号一页的数据
function TitleWGData:GetAllTitleConfig()
	return self.title_list_cfg
end

function TitleWGData:GetShowTitleKeyById(title_id)
	local title_list = self:GetShowTitleCfgList()
	for k,v in pairs(title_list) do
		if v.title_id == title_id then
			return k
		end
	end
end

function TitleWGData:GetShowTitleCfgList()
	local list = {}

	local show_zhanshendian_title_cfg = nil
	local is_added = false

	local show_jh_title_cfg = nil

	for k,v in pairs(self.title_list_cfg) do
		if not is_added then
			if show_zhanshendian_title_cfg == nil then
				show_zhanshendian_title_cfg = v
			end
			if self:IsThisTitleActive(v.title_id) then
				show_zhanshendian_title_cfg = v
			end
		end

		if not TitleWGData.IsWelkinTitle(v.title_id) and not TitleWGData.IsJingLingTitle(v.title_id) then
			if show_zhanshendian_title_cfg ~= nil and not is_added then
				table.insert(list, show_zhanshendian_title_cfg)
				is_added = true
			end

			table.insert(list, v)
		end
	end

	if show_zhanshendian_title_cfg ~= nil and not is_added then
		table.insert(list, show_zhanshendian_title_cfg)
		is_added = true
	end

	-----------------------------------------------------
	local list_2 = {}
	--已激活获得的称号放最前面
	for k,v in pairs(list) do
		if self:IsThisTitleActive(v.title_id) then
			table.insert(list_2,v)
		end
	end
	table.sort(list_2, SortTools.KeyLowerSorter("order"))
	--未激活的 不可激活的不显示
	local list_3 = {}
	for k,v in pairs(list) do
		if not self:IsThisTitleActive(v.title_id) then
			local item_count = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
			if v.is_show == 0 and item_count > 0 then
				table.insert(list_3,v)
			-- elseif v.is_show == 1 then
			-- 	table.insert(list_3,v)
			end
		end
	end
	table.sort(list_3, SortTools.KeyLowerSorter("order"))
	return self:TableAdd(list_2,list_3)
end

function TitleWGData:TableAdd(tab1,tab2)
	for i,v in pairs(tab2) do
		table.insert(tab1,v)
	end
	return tab1
end
--判断这个title_id是不是已经激活
function TitleWGData:IsThisTitleActive(title_id)
	if 0 == #self.title_id_list or IsEmptyTable(self.title_id_list) then
		return false
	end
	for i, v in pairs(self.title_id_list) do
		if v == title_id then
			return true
		end
	end
	return false
end

function TitleWGData:GetTitleAreaplaceNowID()
	if self.now_title_id ~= nil then
		return self.now_title_id
	end
end

function TitleWGData.IsWelkinTitle(title_id)
	return title_id >= 7000 and title_id <= 7999
end

function TitleWGData.IsJingLingTitle(title_id)
	return title_id >= 8000 and title_id <= 8999
end

-- 将灵魄称号从使用列表中取出,并从列表中移除，以用在宠物身上显示
function TitleWGData.GetAndRemoveLingpoTitleFromList(used_title_list)
	if nil == used_title_list then
		return 0
	end

	for i, v in ipairs(used_title_list) do
		local title_cfg = TitleWGData.GetTitleConfig(v)
		if nil ~= title_cfg and TITLE_TYPE.LINGPO == title_cfg.title_type then
			table.remove(used_title_list, i)
			return v
		end
	end

	return 0
end

-- 根据ID计算称号的战斗力
function TitleWGData.GetTitleCapabilityByID(title_id)
	local temp_cfg = TitleWGData.GetTitleConfig(title_id)
	if temp_cfg and next(temp_cfg) ~= nil then
		local temp_attr2 = AttributeMgr.GetAttributteByClass(temp_cfg)
		return AttributeMgr.GetCapability(temp_attr2)
	end
	return 0
end

-- 根据计算称号的战斗力
function TitleWGData.GetTitleCapability(title_cfg)
	if title_cfg and next(title_cfg) ~= nil then
		local temp_attr2 = AttributeMgr.GetAttributteByClass(title_cfg)
		return AttributeMgr.GetCapability(temp_attr2)
	end
	return 0
end

function TitleWGData:SetCurClickData(data)
	self.select_data = data or {}
end

function TitleWGData:GetCurClickData()
	return self.select_data
end

function TitleWGData:GetTitleListDataByType(index)
	if index == 1 then
		self.title_type_list[index] = {}
		local data_id_list = TitleWGData.Instance:GetTitleIdList()
		--首先加入已激活的称号
		for i,v in ipairs(data_id_list) do
			local cfg = TitleWGData.GetTitleConfig(v)
			if cfg then
				table.insert(self.title_type_list[index],cfg)
			end
		end
	else
		-- if self.title_type_list and self.title_type_list[index] then

		-- 	self:SortTitleDataByActive(self.title_type_list[index])
		-- 	return self.title_type_list[index]
		-- end

		self.title_type_list[index] = {}
		for i,v in ipairs(self.title_list_cfg) do
            if v.title_classify == index then
				local item_count = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
				--配置中不显示的称号如果可激活或已激活也显示出来
				if v.is_show == 0 or item_count > 0 or self:GetIsHasTitle(v.title_id) then
                    table.insert(self.title_type_list[index],v)
                end
			end
		end
	end

	self:SortTitleDataByActive(self.title_type_list[index])
	return self.title_type_list[index]
end

--将已激活的称号置顶排序
function TitleWGData:SortTitleDataByActive(sort_tb)
	table.sort(sort_tb, function(a, b)
		if a and b and a.title_id and b.title_id then
			local a_active = TitleWGData.Instance:GetIsHasTitle(a.title_id) and 10000 or 0
			a_active = a_active - a.order * 10
			local b_active = TitleWGData.Instance:GetIsHasTitle(b.title_id) and 10000 or 0
			b_active = b_active - b.order * 10
			return a_active > b_active
		end
	end)
end

function TitleWGData:GetTitleBtnNameByIndex(index)
	local cfg = ConfigManager.Instance:GetAutoConfig("titleconfig_auto").title_classifye
	for i,v in ipairs(cfg) do
		if v.title_classify == index then
			return v.title_classname
		end
	end
end

function TitleWGData:GetTotalAttrKey()
local SuitAttrinfoList = {
	"maxhp",
	"gongji",
	"fangyu",
	"fa_fangyu",
	"pojia",
	"per_maxhp",
	"per_gongji",
	"per_pojia",
	"per_fangyu",
	}

	return SuitAttrinfoList
end

--根据属性类型id 获取 配置
function TitleWGData:GetTitleAttributeCfg(attribute_type_id)
	local cfg_list = ConfigManager.Instance:GetAutoConfig("titleconfig_auto").attribute_type
	if IsEmptyTable(cfg_list) then return nil end
	for k,v in ipairs(cfg_list) do
		if v.type_id == attribute_type_id then
			-- print_error("找到",v)
			return v
		end
	end
end

--筛选出配置里拥有的字段名
function TitleWGData:GetTitleAttrList(attribute_type_id)
	local cfg = self:GetTitleAttributeCfg(attribute_type_id)
	if cfg == nil then return nil end
	local list = {}
	for i=1,COMMON_CONSTS.ATTR_NUM do
		if cfg["attribute"..i] and cfg["attribute"..i] ~= "" then
			--local attr = CommonDataManager.GetAttrStr(cfg["attribute"..i])
			table.insert(list,cfg["attribute"..i])
		else
			break
		end
	end
	return list
end

--外面调用的接口，返回字段名列表
function TitleWGData:FormatAttr()
	local data = self:GetCurClickData()
	local attr_list = self:GetTitleAttrList(data.attribute_type)
	-- print_error(attr_list)
	return attr_list
end

function TitleWGData:SetAllTitleData(protocol)
	self.saved_title_list = protocol.saved_title_list
end

function TitleWGData:GetTitleSaveDataById(title_id)
	for i, v in pairs(self.saved_title_list) do
		if v.title_id == title_id then
			return v
		end
	end
	return nil
end

function TitleWGData:RoleTitleRemind()
	local is_red = 0
	local act_red = self:RoleTitleActRemind()
	if act_red > 0 then
		return 1
	end

	local up_level_red = self:RoleTitleUpLevelRemind()
	if up_level_red > 0 then
		return 1
	end

	return is_red
end

function TitleWGData:RoleTitleActRemind()
	local is_red = 0
	for i = 2, 6 do
		local cell_data_list = self:GetTitleListDataByType(i)
		for k,v in pairs(cell_data_list) do
			local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id == "" and 0 or v.item_id) or 0
			local flag = self:IsThisTitleActive(v.title_id)
			if not flag and item_num > 0 then
				is_red = is_red + 1
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TITLE_ACTIVE, 1, function (callback_param)
					RoleWGCtrl.Instance:OpenRoleTitleView(callback_param)
					return true
				end, v.title_id)
				return is_red, v.title_id
			end
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TITLE_ACTIVE, 0, function ()
		return true
	end)

	return is_red
end

-- 根据选中类型查询红点
function TitleWGData:RoleTitleActRemindByType(index)
	local is_red = 0
	local cell_data_list = self:GetTitleListDataByType(index)
	for k,v in pairs(cell_data_list) do
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id == "" and 0 or v.item_id) or 0
		local flag = self:IsThisTitleActive(v.title_id)
		if not flag and item_num > 0 then
			is_red = is_red + 1
			return is_red, v.title_id
		end
	end

	return is_red
end

function TitleWGData:RoleTitleUpLevelRemind()
	local is_red = 0
	local data_id_list = self:GetTitleListDataByType(1)
	if not IsEmptyTable(data_id_list) then
		for i, v in ipairs(data_id_list) do
			local is_can_up_level = self:RoleTitleIsCanUpLevel(v.title_id)
			if is_can_up_level then
				is_red = is_red + 1 
				return is_red, i
			end
		end
	end

	return is_red
end

function TitleWGData:RoleTitleIsCanUpLevel(title_id)
	local title_level = TitleWGData.Instance:GetTitleLevelByTitleId(title_id)
	if not title_level then
		return false
	end

	local cur_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(title_id, title_level)
	local next_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(title_id, title_level + 1)
	if cur_title_level_cfg and next_title_level_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_title_level_cfg.stuff_id)
		local stuff_count = cur_title_level_cfg.stuff_num
		if item_num >= stuff_count then
			return true
		end
	end

	return false
end

-- 获取称号属性信息
function TitleWGData:GetTitleAttrInfo(info)
	local cur_attr_list = AttributePool.AllocAttribute()
	for k, v in pairs(info) do
		local key = AttributeMgr.GetAttributteKey(k)
		if cur_attr_list[key] ~= nil and v > 0 then
        	cur_attr_list[key] = cur_attr_list[key] + v
        end
	end

	local level_attribute = AttributePool.AllocAttribute()

	local flag = self:IsThisTitleActive(info.title_id)
	if flag then
		local title_level = self:GetTitleLevelByTitleId(info.title_id)
		local cur_title_level_cfg = self:GetTitleLevelCfg(info.title_id, title_level)
		if cur_title_level_cfg then
			for k, v in pairs(cur_title_level_cfg) do
				local key = AttributeMgr.GetAttributteKey(k)
				if level_attribute[key] ~= nil and v > 0 then
		        	level_attribute[key] = level_attribute[key] + v
		        end
			end
		end
	end

	for k,v in pairs(cur_attr_list) do
        if cur_attr_list[k] and level_attribute[k] then
            cur_attr_list[k] = cur_attr_list[k] + level_attribute[k]
        else
            print_error("---哪个属性key不存在---", k)
        end
    end

	return cur_attr_list
end

function TitleWGData:GetOtherCfg()
	return self.other_cfg
end

function TitleWGData:GetTitleLevelCfg(title_id, level)
	return (self.title_level_list_cfg[title_id] or {})[level]
end

--================== 自定义称号 ---------------------------------
function TitleWGData:GetDiyTitleCfg(title_id)
	return self.diy_title_map_cfg[title_id]
end

function TitleWGData:SetDiyTitleInfo(protocol)
	self.diy_title_name_list = protocol.diy_title_name_list
	for title_id, level in pairs(protocol.diy_title_level_list) do
		self.title_level_list[title_id] = level
	end
end

function TitleWGData:SetDiyTitleSingleInfo(protocol)
	self.diy_title_name_list[protocol.title_id] = protocol.diy_title_name
	self.title_level_list[protocol.title_id] = protocol.level
	table.insert(self.title_id_list, protocol.title_id)
end

function TitleWGData:GetDiyTitleName(title_id)
	return self.diy_title_name_list[title_id]
end
