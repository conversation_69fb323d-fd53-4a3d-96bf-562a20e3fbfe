﻿
using DG.Tweening;
using TMPro;
using System;

public static class TextMeshProUGUIExtensions
{
    public static Tweener DoNumberTo(
        this TextMeshProUGUI text, int from, int to, float duration, Action complete)
    {
        Tweener t = DOTween.To(() => from, x => from = x, to, duration);
        t.OnUpdate(() =>
        {
            if (null != text)
            {
                text.text = from.ToString();
            }
        });
        t.OnComplete(() =>
        {
            if (null != complete)
            {
                complete();
            }
        });
        return t;
    }

    public static Tweener DoFloatNumberTo(this TextMeshProUGUI text, float from, float to, float duration, Action complete, Action<float> onUpdate)
    {
        Tweener t = DOTween.To(() => from, x => from = x, to, duration);
        t.OnUpdate(() =>
        {
            if (null != onUpdate)
            {
                onUpdate(from);
            }
        });
        t.OnComplete(() =>
        {
            if (null != complete)
            {
                complete();
            }
        });
        return t;
    }
}
