-- F-副本-特殊逻辑副本.xls
local item_table={
[1]={item_id=22070,num=1,is_bind=1},
[2]={item_id=39785,num=1,is_bind=1},
[3]={item_id=26455,num=1,is_bind=1},
[4]={item_id=26456,num=1,is_bind=1},
[5]={item_id=26457,num=1,is_bind=1},
[6]={item_id=26458,num=1,is_bind=1},
}

return {
fb={
{},
{fb_id=1,start_day=0,end_day=0,scene_id=0,kick_out_time_s=0,fb_time_s=0,boss_pos_x=0,boss_pos_y=0,boss_id=0,pass_item={[0]=item_table[1]},boss_level=1,}
},

fb_meta_table_map={
},
logic={
{},
{param3=0,}
},

logic_meta_table_map={
},
action={
{},
{},
{},
{},
{},
{}
},

action_meta_table_map={
},
fb_default_table={fb_id=0,start_day=9998,end_day=9999,scene_id=653,kick_out_time_s=10,fb_time_s=300,boss_pos_x=50,boss_pos_y=65,boss_id=77,pass_item={[0]=item_table[2],[1]=item_table[3],[2]=item_table[4],[3]=item_table[5],[4]=item_table[6],},boss_level=999,},

logic_default_table={param3=1,},

action_default_table={}

}

