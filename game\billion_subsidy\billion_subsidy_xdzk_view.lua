function BillionSubsidyView:XDZKLoadCallBack()
    if not self.xdzk_shop_list then
        self.xdzk_shop_list = AsyncBaseGrid.New()
        self.xdzk_shop_list:SetStartZeroIndex(false)
        self.xdzk_shop_list:SetSelectCallBack(BindTool.Bind(self.XDZKOnSelectShopItem, self))
        self.xdzk_shop_list:CreateCells(
            {col = 4,
            change_cells_num = 1,
            complement_col_item = true,
            list_view = self.node_list.xdzk_shop_list,
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
            assetName = "ten_billion_subsidy_shop_item",
            itemRender = XDZKShopItem}
        )
    end
end

function BillionSubsidyView:XDZKReleaseCallBack()
    if self.xdzk_shop_list then
        self.xdzk_shop_list:DeleteMe()
        self.xdzk_shop_list = nil
    end
end

function BillionSubsidyView:XDZKShowIndexCallBack()
    local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
    if start_timestamp <= 0 then
        BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.START_TIME_LIMITED_DISCOUNT_SHOP)
    end
end

function BillionSubsidyView:XDZKOnFlush()
    if BillionSubsidyWGData.Instance:GetShopEveryDayRemind(BillionSubsidyWGData.ShopType.XSSD) then
        BillionSubsidyWGData.Instance:SetShopEveryDayRemindData(BillionSubsidyWGData.ShopType.XSSD)
        RemindManager.Instance:Fire(RemindName.BillionSubsidyXDZK)
    end

    local data_list = BillionSubsidyWGData.Instance:GetLimitTimeShopItemGradeData()
    if not data_list then
        return
    end

    -- local data_list = {}
    -- for k, v in pairs(shop_cfg) do
    --     table.insert(data_list, v)
    -- end

    -- table.sort(data_list, function (a, b)
    --     if a.limit_time_discount_duration > 0 and b.limit_time_discount_duration == 0 then
    --         return true
    --     elseif a.limit_time_discount_duration == 0 and b.limit_time_discount_duration > 0 then
    --         return false
    --     else
    --         return a.item_seq < b.item_seq
    --     end
    -- end)

    self.xdzk_shop_list:SetDataList(data_list)
    self.node_list.xdzk_shop_list_scroll_bar:CustomSetActive(#data_list > 8)
end

function BillionSubsidyView:XDZKOnSelectShopItem(item)
    local item_data = item:GetData()
    if IsEmptyTable(item_data) then
        return
    end

    BillionSubsidyWGCtrl.Instance:OpenBuyTipView(BillionSubsidyWGData.ShopType.XSSD, item_data.item_seq)
end

------------------------------- XDZKShopItem 商品item
XDZKShopItem = XDZKShopItem or BaseClass(BYBTShopItem)
function XDZKShopItem:InitParam()
    self.discount_limit_timer_key = "xdzk_discount_limit_timer_key"
    self.shop_type = BillionSubsidyWGData.ShopType.XSSD
end