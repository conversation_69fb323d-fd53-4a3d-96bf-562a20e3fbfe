require("game/cultivation/esoterica_wg_ctrl")
require("game/cultivation/judgment_wg_ctrl")
require("game/cultivation/cultivation_wg_data")
require("game/cultivation/esoterica_wg_data")
require("game/cultivation/cultivation_total_view")
require("game/cultivation/esoterica_view")
require("game/cultivation/esoterica_detail_view")
require("game/cultivation/esoterica_part_view")
require("game/cultivation/esoterica_reslove_view")
require("game/cultivation/cultivation_privilege_view")
require("game/cultivation/cultivation_stage_show_view")
require("game/cultivation/anger_skill_view")
require("game/cultivation/anger_skill_new_view")
require("game/cultivation/esoterica_skill_show_view")
require("game/cultivation/cultivation_get_view")
require("game/cultivation/cultivation_buff_view")
require("game/cultivation/cultivation_preview_view")
require("game/cultivation/cultivation_break_fail_view")
require("game/cultivation/cultivation_break_success_view")
require("game/cultivation/judgment_view")
require("game/cultivation/judgment_ready_view")
require("game/cultivation/equip_target_cultivation_view")
require("game/cultivation/equip_target_attr_view")

require("game/cultivation/charm/charm_holy_seal_view")
require("game/cultivation/charm/charm_lingzhu_view")
require("game/cultivation/charm/charm_attr_view")
require("game/cultivation/charm/charm_compose_view")
require("game/cultivation/charm/charm_suit_overview_view")
require("game/cultivation/charm/charm_lingzhu_tunshi_view")
require("game/cultivation/charm/charm_bag_cell")
require("game/cultivation/charm/charm_wg_data")
require("game/cultivation/charm/charm_wg_ctrl")

require("game/cultivation/cultivation_cross_air_war_view")


CultivationWGCtrl = CultivationWGCtrl or BaseClass(BaseWGCtrl)
function CultivationWGCtrl:__init()
	if CultivationWGCtrl.Instance then
		print_error("[CultivationWGCtrl]:Attempt to create singleton twice!")
	end
	CultivationWGCtrl.Instance = self

	self.view = CultivationView.New(GuideModuleName.CultivationView)
	self.data = CultivationWGData.New()
	
	self.esoterica_part_view = EsotericaPartView.New(GuideModuleName.EsotericaPartView)
	self.esoterica_reslove_view = EsotericaResloveView.New()
	self.privilege_view = CultivationPrivilegeView.New(GuideModuleName.CultivationPrivilegeView)
	self.stage_show_view = CultivationStageShowView.New()
	self.anger_skill_view = AngerSkillView.New(GuideModuleName.AngerSkillView)
	self.esoterica_skill_show_view = EsotericaSkillShowView.New(GuideModuleName.EsotericaSkillShow)
	self.get_view = CultivationGetView.New(GuideModuleName.CultivationGetView)
	self.equip_target_attr_view = EquipTargetAttrView.New()
	self.buff_view = CultivationBuffView.New(GuideModuleName.CultivationBuffView)
	self.judgment_view = JudgmentView.New(GuideModuleName.JudgmentView)
	self.judgment_ready_view = JudgmentReadyView.New(GuideModuleName.JudgmentReadyView)
	self.cultivation_preview_view = CultivationPreviewView.New(GuideModuleName.CultivationPreviewView)
	self.cultivation_break_fail_view = CultivationBreakFailView.New()
	self.cultivation_break_success_view = CultivationBreakSuccessView.New()

	self.esoterica_view = EsotericaView.New(GuideModuleName.EsotericaView)
	self.esoterica_detail_view = EsotericaDetailView.New(GuideModuleName.EsotericaDetailView)

	self.first_enter = true
	self:RegisterProtocol(CSRoleXiuWeiOperate)
	self:RegisterProtocol(SCRoleXiuWeiInfo, "OnSCRoleXiuWeiInfo")
	self:RegisterProtocol(SCRoleXiuWeiBaseInfo, "OnSCRoleXiuWeiBaseInfo")
	self:RegisterProtocol(SCRoleXiuWeiNuqiInfo, "OnSCRoleXiuWeiNuqiInfo")
	self:RegisterProtocol(SCRoleXiuWeiNuqiLevelInfo, "OnSCRoleXiuWeiNuqiLevelInfo")

	self.role_cap_cache = 0

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self:Esoterica__init()
	self:InitCharmCtrl()
end

function CultivationWGCtrl:__delete()
	CultivationWGCtrl.Instance = nil

	self:Esoterica__delete()
	self:DeleteCharmCtrl()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.privilege_view then
		self.privilege_view:DeleteMe()
		self.privilege_view = nil
	end

	if self.stage_show_view then
		self.stage_show_view:DeleteMe()
		self.stage_show_view = nil
	end

	if self.anger_skill_view then
		self.anger_skill_view:DeleteMe()
		self.anger_skill_view = nil
	end

	if self.esoterica_skill_show_view then
		self.esoterica_skill_show_view:DeleteMe()
		self.esoterica_skill_show_view = nil
	end

	if self.get_view then
		self.get_view:DeleteMe()
		self.get_view = nil
	end

	if self.equip_target_attr_view then
		self.equip_target_attr_view:DeleteMe()
		self.equip_target_attr_view = nil
	end

	if self.buff_view then
		self.buff_view:DeleteMe()
		self.buff_view = nil
	end

	if self.judgment_view then
		self.judgment_view:DeleteMe()
		self.judgment_view = nil
	end

	if self.judgment_ready_view then
		self.judgment_ready_view:DeleteMe()
		self.judgment_ready_view = nil
	end
	
	if self.esoterica_part_view then
		self.esoterica_part_view:DeleteMe()
		self.esoterica_part_view = nil
	end

	if self.esoterica_reslove_view then
		self.esoterica_reslove_view:DeleteMe()
		self.esoterica_reslove_view = nil
	end

	if self.cultivation_preview_view then
		self.cultivation_preview_view:DeleteMe()
		self.cultivation_preview_view = nil
	end

	if self.cultivation_break_fail_view then
		self.cultivation_break_fail_view:DeleteMe()
		self.cultivation_break_fail_view = nil
	end

	if self.cultivation_break_success_view then
		self.cultivation_break_success_view:DeleteMe()
		self.cultivation_break_success_view = nil
	end

	if self.esoterica_view then
		self.esoterica_view:DeleteMe()
		self.esoterica_view = nil
	end

	if self.esoterica_detail_view then
		self.esoterica_detail_view:DeleteMe()
		self.esoterica_detail_view = nil
	end
	
	self.first_enter = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	self.role_cap_cache = nil
end

-----------------发送协议，16169------------------------------
function CultivationWGCtrl:SendRoleXiuWeiOperate(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleXiuWeiOperate)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- 修为提升 提升等级(重数)
function CultivationWGCtrl:UpgradeLevel()
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.UPGRADE_LEVEL)
end

-- 升级境界 是否使用道具 param1：1是用，0是不用
function CultivationWGCtrl:UpgradeStage(param1)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.UPGRADE_STAGE,param1)
end

-- 领取境界奖励 param1:stage
function CultivationWGCtrl:ReceiveStageReward(param1)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.GET_STAGE_REWARD,param1 - 1)
end

-- 怒气变身
function CultivationWGCtrl:AngerBecome()
	local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
	CultivationWGCtrl.Instance:OnCultivationSkillEffect(curr_nuqi_type, true)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.NUQI_BIANSHEN)
end

-- 升级怒气技能等级 param1:0人, 1仙, 2魔, param2: 技能下标[0,3]
function CultivationWGCtrl:UpgradeAngerSkill(param1, param2)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.XIUWEI_OPERA_TYPE_NUQI_UPGRADE_SKILL_LEVEL, param1, param2)
end

-- 激活境界怒气类型 param1:0人, 1仙, 2魔
function CultivationWGCtrl:ActiveAngerType(param1)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.ACTIVE_NUQI_TYPE,param1)
end

-- 升级境界怒气buff  类型 param1:0人, 1仙, 2魔
function CultivationWGCtrl:UpgradeAngerBuff(param1)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.XIUWEI_OPERA_TYPE_NUQI_UPGRADE_BUFF_LEVEL,param1)
end

-- 选择境界怒气类型 param1:0人, 1仙, 2魔
function CultivationWGCtrl:ChooseAngerType(param1)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.CHOOSE_NUQI_TYPE,param1)
end

-- 一键使用加经验道具
function CultivationWGCtrl:OneKeyUseExpItem()
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.ONE_KEY_USE_EXP_ITEM)

end

-- 领取特权每日奖励
function CultivationWGCtrl:ReceivePrivilegeReward()
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.EVERY_DAY_REWARD)
end


-- 领取修为经验加成 param1:加成类型 param2:seq
function CultivationWGCtrl:ReceiveCultivationExpAdd(param1,param2)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.GET_EXP_ADD,param1,param2)
end

-- 主动请求数据
function CultivationWGCtrl:RequestData()
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.XIUWEI_OPERA_TYPE_GET_INFO)
end

--主线任务特殊变身param1:0人, 1仙, 2魔
function CultivationWGCtrl:SpecialBianShen(param1)
	self:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.XIUWEI_OPERA_TYPE_SPECIAL_BIANSHEN, param1)
end

------------------------------------------------------------

-----------------接收协议------------------------------
function CultivationWGCtrl:OnSCRoleXiuWeiInfo(protocol)
	local old_xiuwei_level = self.data:GetXiuWeiLevel()
	self.data:SetRoleXiuWeiInfo(protocol)
	RemindManager.Instance:Fire(RemindName.XiuWei)
	RemindManager.Instance:Fire(RemindName.Esoterica)
	RemindManager.Instance:Fire(RemindName.BiZuo)
	RemindManager.Instance:Fire(RemindName.BiZuo_Cultivation)
	RemindManager.Instance:Fire(RemindName.Dujie_Spirit)

	if not self.first_enter then

		local new_xiuwei_level = protocol.level

		if new_xiuwei_level > old_xiuwei_level then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua, is_success = true, pos = Vector2(0, 0)})
			-- SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Cultivation.XiuWeiUpDesc, new_xiuwei_level))
		end
	else
		self.first_enter = false
	end

	self:FlushCultivationView()
	self:FlushAngerView()

	GlobalEventSystem:Fire(OtherEventType.ROLE_SKILL_POWER_CHANGE)
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:SetAttr("xiuwei_stage", protocol.stage)
	end
end



function CultivationWGCtrl:OnSCRoleXiuWeiBaseInfo(protocol)
	local old_xiuwei_level = self.data:GetXiuWeiLevel()
	local old_xiuwei_stage = self.data:GetXiuWeiState()
	local old_lingqi_exp = self.data:GetXiuWeiExp()
	local old_number, old_rate = self.data:GetTotalExpAdd()
	self.data:SetRoleXiuWeiBaseInfo(protocol)
	local new_number, new_rate = self.data:GetTotalExpAdd()
	RemindManager.Instance:Fire(RemindName.XiuWei)
	RemindManager.Instance:Fire(RemindName.BiZuo)
	RemindManager.Instance:Fire(RemindName.BiZuo_Cultivation)
	RemindManager.Instance:Fire(RemindName.Dujie)
	ViewManager.Instance:FlushView(GuideModuleName.DujieView)

	if not self.first_enter then
		local new_xiuwei_level = protocol.level

		if new_xiuwei_level > old_xiuwei_level then
			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(-165, -120)})
			-- SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Cultivation.XiuWeiUpDesc, new_xiuwei_level))
		end

		if new_number > old_number or new_rate > old_rate then
			local change_number = new_number - old_number
			local change_rate = new_rate - old_rate
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Cultivation.XiuWeiAddStr, change_number, change_rate))
		end
	else
		self.first_enter = false
	end

	if old_xiuwei_stage ~= protocol.stage then
		self:FlushCultivationView()
	else
		self:FlushCultivationView()
	end

	-- 主界面弹窗显示
	if old_lingqi_exp < protocol.lingli_exp then
		GlobalEventSystem:Fire(CULTIVATION_CHANGE.ADD_EXP_CHANGE)
	end
end

function CultivationWGCtrl:OnSCRoleXiuWeiNuqiInfo(protocol)
	-- local old_nuqi_level = self.data:GetRoleAllNuqiLevel()
	self.data:SetRoleXiuWeiNuqiInfo(protocol)
	-- local nuqi_type = self.data:CheckNuqiActiveStatus(old_nuqi_level)

	-- if nuqi_type ~= nil then
	-- 	local data = {appe_image_id = nuqi_type, appe_type = ROLE_APPE_TYPE.BIANSHEN_XIUWEI, index_param = nuqi_type}
	-- 	AppearanceWGCtrl.Instance:OnGetNewAppearance(data)
	-- end

	self:FlushAngerView()
	GlobalEventSystem:Fire(OtherEventType.ROLE_SKILL_POWER_CHANGE)
	RemindManager.Instance:Fire(RemindName.Dujie_Spirit)
end

function CultivationWGCtrl:OnSCRoleXiuWeiNuqiLevelInfo(protocol)
	local old_level = self.data:GetAngerLevel(protocol.nuqi_type)
	self.data:SetRoleXiuWeiAngerLevelInfo(protocol)
	local nuqi_upgrade_level = protocol.nuqi_level.nuqi_upgrade_level
	local need_pop = (nuqi_upgrade_level == 1 or nuqi_upgrade_level % 10 == 0) and (old_level ~= nil and old_level ~= nuqi_upgrade_level) and nuqi_upgrade_level < 30

	if need_pop then
		local data = {appe_image_id = protocol.nuqi_type, appe_type = ROLE_APPE_TYPE.BIANSHEN_XIUWEI, index_param = protocol.nuqi_level.nuqi_upgrade_level}
		AppearanceWGCtrl.Instance:OnGetNewAppearance(data)
		-- TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(-165, -120)})
	else
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true})
	end

	self:FlushAngerView()
	RemindManager.Instance:Fire(RemindName.Dujie_Spirit)
end


------------------------------------------------------------

------------------刷新View-----------------------------------

function CultivationWGCtrl:FlushCultivationView()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	if self.view and self.view:IsOpen() then
		--self.view:Flush(TabIndex.esoterica)
	end

	if self.esoterica_detail_view and self.esoterica_detail_view:IsOpen() then
		self.esoterica_detail_view:Flush()
	end

	if self.esoterica_view and self.esoterica_view:IsOpen() then
		self.esoterica_view:Flush()
		self.esoterica_view:FlushEsotericaList()
	end

	if self.privilege_view and self.privilege_view:IsOpen() then
		self.privilege_view:Flush()
	end

	if self.buff_view and self.buff_view:IsOpen() then
		self.buff_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.BiZuo, TabIndex.bizuo_cultivation)

	if self.cultivation_preview_view and self.cultivation_preview_view:IsOpen() then
		self.cultivation_preview_view:Flush()
	end

	-- ViewManager.Instance:FlushView(GuideModuleName.XiuWeiPreviewView)
end

function CultivationWGCtrl:FlushAngerView()
	ViewManager.Instance:FlushView(GuideModuleName.DujieView, 0, "flush_spirit")
	DujieWGCtrl.Instance:FlushDujieSpiritBuffView()
	if self.view and self.view:IsOpen() then
		self.view:Flush(TabIndex.anger_skill)
	end
end

------------------------------------------------------------

-----------------打开view------------------------------
function CultivationWGCtrl:OpenPrivilegeView()
	if self.privilege_view then
		self.privilege_view:Open()
	end
end

function CultivationWGCtrl:OpenStageShowView()
	if self.stage_show_view then
		self.stage_show_view:Open()
	end
end

function CultivationWGCtrl:OpenAngerSkillView()
	if self.anger_skill_view then
		self.anger_skill_view:Open()
	end
end

function CultivationWGCtrl:OpenEsotericaSkillView(data)
	if self.esoterica_skill_show_view then
		if data then
			self.esoterica_skill_show_view:SetShowData(data)
		end
		self.esoterica_skill_show_view:Open()
	end
end

function CultivationWGCtrl:OpenGetView()
	if self.get_view then
		self.get_view:Open()
	end
end

function CultivationWGCtrl:OpenEquipTargetAttrView(suit_index)
	if self.equip_target_attr_view then
		self.equip_target_attr_view:SetData(suit_index)
		self.equip_target_attr_view:Open()
	end
end



function CultivationWGCtrl:OpenBuffView()
	if self.buff_view then
		self.buff_view:Open()
	end
end

function CultivationWGCtrl:OpenJudgmentView()
	if self.judgment_view then
		self.judgment_view:Open()
	end
end

function CultivationWGCtrl:OpenCultivationPreviewView()
	if self.cultivation_preview_view then
		self.cultivation_preview_view:Open()
	end
end

function CultivationWGCtrl:OpenCultivationBreakFailView()
	if self.cultivation_break_fail_view then
		self.cultivation_break_fail_view:Open()
	end
end

function CultivationWGCtrl:OpenCultivationBreakSuccessView()
	if self.cultivation_break_success_view then
		self.cultivation_break_success_view:Open()
	end
end

function CultivationWGCtrl:OpenEsotericaView()
	if self.esoterica_view then
		self.esoterica_view:Open()
	end
end

function CultivationWGCtrl:SetDataAndOpenEsotericaDetailView(index)
	if self.esoterica_detail_view then
		self.esoterica_detail_view:SetSelectIndex(index)
		self.esoterica_detail_view:Open()
	end
end

-----------------------------------------------

function CultivationWGCtrl:SetRoleCapCache()
	self.role_cap_cache = RoleWGData.Instance:GetMainRoleCap()
end

function CultivationWGCtrl:GetRoleCapCache()
	return self.role_cap_cache
end

function CultivationWGCtrl:OnOperateResult(result)
	local is_suc = result == 1
	local cfg
	-- 失败不会刷新数据 要那当前境界的配置去判断
	if is_suc then
		cfg = self.data:GetLastXiuWeiStageCfg()
	else
		cfg = self.data:GetCurXiuWeiStageCfg()
	end

	if cfg and cfg.upgrade_type == 1 then
		self:JudgmentResultShow(is_suc)
	else
		ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView, nil, "tupo_back", {is_suc = is_suc})
	end
end

function CultivationWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or 
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num ~= old_num) then
		if self.data:IsCultivationCostItem(change_item_id) or self.data:IsCultivationExpCostItem(change_item_id) then
			RemindManager.Instance:Fire(RemindName.XiuWei)
			ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
			MainuiWGCtrl.Instance:FlushTaskTopPanel()
		end

		if self.data:IsCultivationExpAddItem(change_item_id) then
			if self.get_view and self.get_view:IsOpen() then
				self.get_view:Flush()
			end
		end

		if self.data:GetIsRoleNuqiActiveItem(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.DujieView)
			-- self.view:Flush(0,"flush_dujie_ing")
			-- RemindManager.Instance:Fire(RemindName.XiuWei)
			-- MainuiWGCtrl.Instance:FlushTaskTopPanel()
			-- if self.anger_skill_view and self.anger_skill_view:IsOpen() then
			-- 	self.anger_skill_view:Flush()
			-- end

			-- if self.view and self.view:IsOpen() then
			-- 	self.view:Flush()
			-- end
		end

		if self.data:CheckIsBuffUplevelStuff(change_item_id) then
			DujieWGCtrl.Instance:FlushDujieSpiritBuffView()
		end
	end

	if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		if self.data:IsCultivationExpAddItem(change_item_id) then
			if self.get_view and self.get_view:IsOpen() then
				self.get_view:Flush()
			end
		end
	end

	self:OnCharmItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
end

function CultivationWGCtrl:OnCharmItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:IsCharmComposeStuff(change_item_id) then
		self.data:CalculationCharmComposeRemind()
		RemindManager.Instance:Fire(RemindName.Charm_Holy_Seal)

		if self.charm_compose_view and self.charm_compose_view:IsOpen() then
			self.charm_compose_view:Flush()
		end
	 end
end

-- 触发元神变身效果
function CultivationWGCtrl:OnCultivationSkillEffect(nuqi_type, is_change, skill_id)
	local nuqi_cfg = CultivationWGData.Instance:GetActiveCfgByType(nuqi_type)
	local combo_skill_img = nil
	if not nuqi_cfg then
		return
	end

	if is_change then
		combo_skill_img = nuqi_cfg.changes_effect
	else
		if skill_id == nuqi_cfg.skill_effect_id then
			combo_skill_img = nuqi_cfg.skill_effect
		end
	end

	if not combo_skill_img then
		return
	end

	local image_str = string.format("%s_%s", "a3", combo_skill_img)
	local skill_bundle, skill_asset = ResPath.GetEffect(combo_skill_img)
	MainuiWGCtrl.Instance:SetBeastBuffIcon(false)
	AddDelayCall(self, function ()
		MainuiWGCtrl.Instance:PlayBeastBuffAni(string.lower(image_str), nil, nil, skill_bundle, skill_asset)
	end, 0.5)
end