local TabType =
{
	Tab_1 = 1, --赛季奖励.
	Tab_2 = 2, --排位奖励.
}

KF3V3SeasonRewardView = KF3V3SeasonRewardView or BaseClass(SafeBaseView)

function KF3V3SeasonRewardView:__init()
	self:SetMaskBg()
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ sizeDelta = Vector2(812, 574) })
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_pvp_season_reward") --赛季奖励
end

function KF3V3SeasonRewardView:LoadCallBack()
	self.is_modal = false

	self.rank_reward_list = AsyncListView.New(KF3V3RankRewardItemRender, self.node_list.rank_reward_list)

	XUI.AddClickEventListener(self.node_list.tab_toggle_1, BindTool.Bind(self.OnClickTab, self, TabType.Tab_1))
	XUI.AddClickEventListener(self.node_list.tab_toggle_2, BindTool.Bind(self.OnClickTab, self, TabType.Tab_2))

	self.node_list.tab_toggle_1.toggle.isOn = true
	self.sr_cur_tab_index = 1
end

function KF3V3SeasonRewardView:ReleaseCallBack()
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
end

function KF3V3SeasonRewardView:OnFlush()
	local list_data = {}
	if self.sr_cur_tab_index == TabType.Tab_1 then
		list_data = KF3V3WGData.Instance:GetSeasonRewardList()
	elseif self.sr_cur_tab_index == TabType.Tab_2 then
		list_data = KF3V3WGData.Instance:GetDuanWeiRewardList()
	end
	self.rank_reward_list:SetDataList(list_data)

	--已参赛text
	local need_joint_count = 0
	local cur_reward_cfg = KF3V3WGData.Instance:GetCurSeasonReward()
	if cur_reward_cfg then
		need_joint_count = cur_reward_cfg.need_match_times -- 领取奖励所需参加次数
	end

	local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
	local joint_count = role_info.season_match_times

	local change_str = string.format(Language.KuafuPVP.RewardchangCount, joint_count, need_joint_count)
	change_str = ToColorStr(change_str, joint_count >= need_joint_count and COLOR3B.GREEN or COLOR3B.PINK)
	self.node_list.join_count.text.text = string.format(Language.KuafuPVP.Rewardchang, change_str)

	--结算时间text
	self.node_list.season_rank_reward_time.text.text = string.format(Language.KuafuPVP.SeasonRankRewardTimeTip,
		TimeUtil.FormatYMDH(role_info.season_rank_reward_time))
end

function KF3V3SeasonRewardView:OnClickTab(index, is_on)
	if is_on then
		self.sr_cur_tab_index = index
		self:Flush()
	end
end

function KF3V3SeasonRewardView:GetSeasonRewardTabType()
	return self.sr_cur_tab_index
end

KF3V3RankRewardItemRender = KF3V3RankRewardItemRender or BaseClass(BaseRender)
function KF3V3RankRewardItemRender:__init()
	self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)

	self.type = 1
end

function KF3V3RankRewardItemRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function KF3V3RankRewardItemRender:OnFlush()
	--奖励物品
	local data_list = {}
	for i = 0, 20 do
		if self.data.reward_item[i] then
			table.insert(data_list, self.data.reward_item[i])
		else
			break
		end
	end

	self.item_list:SetDataList(SortDataByItemColor(data_list))

	self.type = ZhanDuiWGCtrl.Instance:GetSeasonRewardTabType()

	self.node_list.rank_text:SetActive(self.type == TabType.Tab_1)
	self.node_list.rank_group:SetActive(self.type == TabType.Tab_2)

	if self.type == TabType.Tab_1 then
		self.node_list.rank_text.text.text = self.data.rank_text
	elseif self.type == TabType.Tab_2 then
		self.node_list.rank_img.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. self.data.icon))
		self.node_list.rank_img_name.text.text = self.data.duanwei_name
	end
end
