local head_bg_side_res = {
	[0] = "a3_zjm_pipei_kuang1", --蓝
	[1] = "a3_zjm_pipei_kuang2", --红
	[2] = "a3_zjm_pipei_kuang3", --黄
}

ZCRunMsgCenterView = ZCRunMsgCenterView or BaseClass(SafeBaseView)

function ZCRunMsgCenterView:__init()
    self:AddViewResource(0, "uis/view/zc_result_ui_prefab", "zc_kill_msg")
    self.view_layer = UiLayer.PopTop
end

function ZCRunMsgCenterView:__delete()

end

function ZCRunMsgCenterView:ReleaseCallBack()
    self.show_info = nil
    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    if self.tween_end_pos then
        self.tween_end_pos:Kill()
        self.tween_end_pos = nil
    end
    self.tween_pos = nil
    self.load_callback = nil
    self.role_info = nil
end

function ZCRunMsgCenterView:OpenCallBack()

end

function ZCRunMsgCenterView:LoadCallBack()
    self.load_callback = true
    if self.need_show_tween then
        self.need_show_tween = nil
        self:ShowTween()
    end
end

function ZCRunMsgCenterView:ShowIndexCallBack()
    --self:Flush()
end

function ZCRunMsgCenterView:LoadNodeImage(node, image, set_native)
    local bundle, asset = ResPath.GetF2ZCResultImg(image)
    node.image:LoadSprite(bundle, asset, function()
        if set_native then
            node.image:SetNativeSize()
        end
    end)
end

function ZCRunMsgCenterView:OnFlush()
    if not self.role_info then
        return
    end

    if self.role_info.killer_tianshen_index and self.role_info.be_killer_tianshen_index then
        local flush_tianshen_func = function(node_name, tianshen_index)
            local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tianshen_index)
            if tianshen_cfg then
                local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
                self.node_list[node_name].image:LoadSpriteAsync(bundle, asset, function ()
                    self.node_list[node_name]:SetActive(true)
                    self.node_list[node_name].image:SetNativeSize()
                end)
            end
        end

        flush_tianshen_func("head_left", self.role_info.killer_tianshen_index)
        flush_tianshen_func("head_right", self.role_info.be_killer_tianshen_index)
    else
        XUI.UpdateRoleHead(self.node_list["head_left"], self.node_list["left_custom_img"], self.role_info.killer_uid, self.role_info.killer_sex, self.role_info.killer_prof,nil, nil, true)
        if self.role_info.monster_id and self.role_info.monster_id > 0 then
            local boss_image_name
            local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.role_info.monster_id]
            if monster_cfg then
                local asset_name,bundle_name = ResPath.GetBossIcon("wrod_boss_"..monster_cfg.small_icon)
                self.node_list.head_right.image:LoadSprite(asset_name,bundle_name,function ()
                    self.node_list.head_right.image:SetNativeSize()
                end)
            end
        else
            XUI.UpdateRoleHead(self.node_list["head_right"], self.node_list["right_custom_img"], self.role_info.target_uid, self.role_info.target_sex, self.role_info.target_prof, nil, nil, true)
        end
    end

    local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
    local killer_name = self.role_info.killer_name
    local killer_name_list = Split(self.role_info.killer_name, "_")
    if not IsEmptyTable(killer_name_list) then
        if not is_cross_server_stage then
            killer_name = killer_name_list[1]
        else
            if killer_name_list[2] then
                local has_s = string.find(killer_name_list[2],"s")
                if has_s then
                    killer_name = string.format(Language.BiZuo.ServerName_2,killer_name_list[2],killer_name_list[1])
                else
                    killer_name = string.format(Language.BiZuo.ServerName_1,killer_name_list[2],killer_name_list[1])
                end
            else
                killer_name = killer_name_list[1]
            end
        end
    end

    local target_name = self.role_info.target_name
    local target_name_list = Split(self.role_info.target_name, "_")
    if not IsEmptyTable(target_name_list) then
        if not is_cross_server_stage then
            target_name = target_name_list[1]
        else
            if target_name_list[2] and killer_name_list ~= nil and killer_name_list[2] ~= nil then
                local has_s = string.find(killer_name_list[2],"s")
                if has_s then
                    target_name = string.format(Language.BiZuo.ServerName_2,target_name_list[2],target_name_list[1])
                else
                    target_name = string.format(Language.BiZuo.ServerName_1,target_name_list[2],target_name_list[1])
                end
            else
                target_name = target_name_list[1]
            end
        end
    end

    -- 天玑迷城特殊处理
    if self.role_info.killer_side_type then
        local killer_side_type = self.role_info.killer_side_type
        local target_side_type = self.role_info.target_side_type

        self:LoadNodeImage(self.node_list["head_bg_left"], head_bg_side_res[killer_side_type])
        self:LoadNodeImage(self.node_list["head_bg_right"], head_bg_side_res[target_side_type])
    else
        self:LoadNodeImage(self.node_list["head_bg_left"], head_bg_side_res[0])
        self:LoadNodeImage(self.node_list["head_bg_right"], head_bg_side_res[1])
    end

    self.node_list["txt_name_blue"].text.text = killer_name --击杀者.
    self.node_list["txt_name_red"].text.text = target_name  --死人.

    --获取通用的击杀头像框信息
    local kill_msg_cfg = TipWGData.Instance:GetSceneKillMsgCfg(self.role_info.param)
    local kill_image_type = 1
    if kill_msg_cfg then
        if self.role_info.kill_msg then
            self.node_list["img_text"].text.text = self.role_info.kill_msg
        else
            self.node_list["img_text"].text.text = kill_msg_cfg.kill_txt
        end
        kill_image_type = kill_msg_cfg.kill_image_type
    end

    self:LoadNodeImage(self.node_list["img_text_type"], "a3_zjm_kill_type_"..kill_image_type)
end

function ZCRunMsgCenterView:Show(t_info)
    self.role_info = t_info
    self:Open()
    self:ShowTween()
end

function ZCRunMsgCenterView:ShowTween()
    if not self.load_callback then
        self.need_show_tween = true
        return
    end
    if self.show_info then
        GlobalTimerQuest:CancelQuest(self.show_info)

        self:DoEndTween(false, function()
            self:StartTween()
        end)
    else
        self:StartTween()
    end
end

function ZCRunMsgCenterView:DoEndTween(is_close, callback)
    local show_cfg = KuafuYeZhanWangChengWGData.Instance:GetTipsShowInfo()
    local tween_scale_close = show_cfg.tween_scale_close or 0.3

    if self.tween_end_pos then
        self.tween_end_pos:Kill()
        self.tween_end_pos = nil
    end

    self.tween_end_pos = DG.Tweening.DOTween.Sequence()
    self.tween_end_pos:AppendCallback(function()
        local tween = self.node_list["msg"].rect:DOScaleY(0, tween_scale_close)
        tween:SetEase(DG.Tweening.Ease.Linear)
    end)

    self.tween_end_pos:AppendInterval(0.1)
    self.tween_end_pos:AppendCallback(function()
        self.node_list["effect_2"]:SetActive(true)
    end)

    self.tween_end_pos:AppendInterval(0.5)--effect_2的特效时间


    self.tween_end_pos:OnComplete(function()
        if is_close then
            self:Close()
        end

        if callback then
            callback()
        end
    end)
end

function ZCRunMsgCenterView:StartTween()
    self:Flush()
    local show_cfg = KuafuYeZhanWangChengWGData.Instance:GetTipsShowInfo()
    local tween_scale_open = show_cfg.tween_scale_open or 0.2
    local img_kill_time = show_cfg.img_kill_time or 0.3

    self.node_list["msg"].rect.localScale = Vector3(0,0,0)
    self.node_list["img_kill"].image.fillAmount = 0
    self.node_list["effect_1"]:SetActive(false)
    self.node_list["effect_2"]:SetActive(false)

    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    self.tween = DG.Tweening.DOTween.Sequence()
    local tween = self.node_list["msg"].rect:DOScale(1.1, tween_scale_open)
    tween:SetEase(DG.Tweening.Ease.OutBack)
    self.tween:Append(tween)

    self.tween:AppendInterval(tween_scale_open + 0.1)
     self.tween:AppendCallback(function()
        self.node_list["effect_1"]:SetActive(true)
    end)

    self.tween:AppendInterval(0.3)
    self.tween:Append(self.node_list["img_kill"].image:DOFillAmount(1, img_kill_time))

    self.tween:OnComplete(function()
        self.tween:Kill()
        self.tween = nil
    end)

    self.show_info = GlobalTimerQuest:AddDelayTimer(function()
        self:DoEndTween(true, function()
            self.show_info = nil
        end)
    end, 1.6)
end
