require("game/offlinerest/experience_fb/experience_fb_wg_data")
require("game/offlinerest/experience_fb/experience_fb_view")
require("game/offlinerest/experience_fb/experience_fb_scene_view")
require("game/offlinerest/experience_fb/experience_fb_rank_view")
require("game/offlinerest/experience_fb/experience_fb_buff_view")
require("game/offlinerest/experience_fb/experience_fb_card_tips_view")

-- 历练副本
ExperienceFbWgCtrl = ExperienceFbWgCtrl or BaseClass(BaseWGCtrl)
function ExperienceFbWgCtrl:__init()

	if ExperienceFbWgCtrl.Instance ~= nil then
		ErrorLog("[ExperienceFbWgCtrl] Attemp to create a singleton twice !")
	end
	ExperienceFbWgCtrl.Instance = self

	self.view = ExperienceFbView.New(GuideModuleName.ExperienceFbView)
	self.experience_fb_scene_view = ExperienceFBSceneView.New()
	self.experience_fb_rank_view = ExperienceFBRankView.New()
	self.experience_fb_buff_view = ExperienceFBBuffView.New()
	self.experience_fb_card_tips_view = ExperienceFBCardTipsView.New()
	self.data = ExperienceFbWGData.New()

	self:RegisterAllProtocols()
end

function ExperienceFbWgCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	if self.experience_fb_scene_view then
		self.experience_fb_scene_view:DeleteMe()
		self.experience_fb_scene_view = nil
	end

	if self.experience_fb_rank_view then
		self.experience_fb_rank_view:DeleteMe()
		self.experience_fb_rank_view = nil
	end

	if self.experience_fb_buff_view then
		self.experience_fb_buff_view:DeleteMe()
		self.experience_fb_buff_view = nil
	end

	if self.experience_fb_card_tips_view then
		self.experience_fb_card_tips_view:DeleteMe()
		self.experience_fb_card_tips_view = nil
	end

	ExperienceFbWgCtrl.Instance = nil
end

function ExperienceFbWgCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSExpWestOperate) 									-- 	副本相关请求
	self:RegisterProtocol(SCExpWestBaseInfo, "OnSCExpWestBaseInfo")				--	副本信息
	self:RegisterProtocol(SCExpWestSceneInfo, "OnSCExpWestSceneInfo")	  		-- 	副本场景信息
	self:RegisterProtocol(SCExpWestRankInfo, "OnSCExpWestRankInfo")	  			--	副本当前等级的排行信息
	self:RegisterProtocol(SCExpWestCardInfo, "OnSCExpWestCardInfo")	  			--	副本当前的选卡信息
	self:RegisterProtocol(SCExpWestSkillInfo, "OnSCExpWestSkillInfo")	  		--	副本当前的技能信息
	self:RegisterProtocol(SCExpWestPassTimeInfo, "OnSCExpWestPassTimeInfo")	  	--	副本当前的技能信息
end

-- 副本相关请求
function ExperienceFbWgCtrl:SendExpWestOperate(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSExpWestOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 请求副本基础信息
function ExperienceFbWgCtrl:RequestExpWestBaseInfo()
	self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_BASE_INFO)
end

-- 请求挑战关卡
function ExperienceFbWgCtrl:RequestExpWestDare(level)
	self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_DARE, level)
end

-- 请求一键通关
function ExperienceFbWgCtrl:RequestExpWestAutoDare()
	self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_AUTO_DARE)
end

--请求排行信息
function ExperienceFbWgCtrl:RequestExpWestRankInfo(level)
	self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_RANK_INFO, level)
end

--请求选卡
function ExperienceFbWgCtrl:RequestExpWestChooseCard(wave, index)
	self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_CHOOSE_CARD, wave, index)
end

--请求使用技能
function ExperienceFbWgCtrl:RequestExpWestUseSkill()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local main_role_x, main_role_y = main_role:GetLogicPos()
		self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_USE_SKILL, 0, main_role_x, main_role_y)
	end
end

--请求获取当前的时间数据
function ExperienceFbWgCtrl:RequestExpWestLevelTime(level)
	self:SendExpWestOperate(EXP_WEST_OPERATE_TYPE.EXP_WEST_OPERATE_TYPE_TIME_INFO, level)
end

-- 副本基础信息
function ExperienceFbWgCtrl:OnSCExpWestBaseInfo(protocol)
	-- print_error("副本基础信息", protocol)
	self.data:SetExpWestBaseInfo(protocol)
	self.view:Flush()
end

-- 场景信息
function ExperienceFbWgCtrl:OnSCExpWestSceneInfo(protocol)
	self.data:SetExpWestSceneInfo(protocol)
	if protocol.is_end == 1 then
		if GuajiCache.guaji_type == GuajiType.Auto then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		end

		if protocol.is_pass == 1 then
			-- 挑战成功
			local pass_vo = FuBenWGData.CreateCommonPassVo()
			pass_vo.scene_type = FUBEN_TYPE.FB_WUHUN_EXP_WEST
			pass_vo.is_pass = protocol.is_pass
			pass_vo.level = protocol.level
			local pass_level = self.data:GetCurrExpWestPassLevel()
			local cfg = self.data:GetLevelCfgByLevel(protocol.level)
			local next_cfg = self.data:GetLevelCfgByLevel(protocol.level + 1)

			if protocol.level == pass_level then
				pass_vo.reward_list = cfg and cfg.pass_reward_item
			else
				pass_vo.reward_list = {}
			end

			local pass_time = protocol.pass_time or 0
			pass_vo.tip1 = string.format(Language.OfflineRest.ExperienceFbcurrTips, cfg and cfg.stage_name or "")
			pass_vo.tip2 = string.format(Language.OfflineRest.ExperienceFbcurrTips2, TimeUtil.FormatSecond2MS(pass_time))
			FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
		else
			-- 挑战失败
			FuBenWGCtrl.Instance:OpenFuBenLoseView()
		end
	else
		if protocol.wave ~= 1 then
			if GuajiCache.guaji_type ~= GuajiType.Auto then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		end
	end

	self:FlushExperienceFbSceneView(0, "flush_wave")
end

-- 排行信息
function ExperienceFbWgCtrl:OnSCExpWestRankInfo(protocol)
	-- print_error("排行信息", protocol)
	self.data:SetExpWestRankInfo(protocol)
end

-- 选卡信息
function ExperienceFbWgCtrl:OnSCExpWestCardInfo(protocol)
	-- print_error("选卡信息", protocol)
	self.data:SetExpWestCardInfo(protocol)
	self:FlushExperienceFbSceneView(0, "flush_card")
end

-- 技能信息
function ExperienceFbWgCtrl:OnSCExpWestSkillInfo(protocol)
	-- print_error("技能信息", protocol)
	self.data:SetExpWestSkillInfo(protocol)
	self:FlushExperienceFbSceneView(0, "flush_skill")
end

-- 副本通关时间记录
function ExperienceFbWgCtrl:OnSCExpWestPassTimeInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(0, "flush_pass_time", {fb_pass_time = protocol.fb_pass_time})
	end
end

-- 打开界面
function ExperienceFbWgCtrl:OpenExperienceFbView()
	if self.view:IsOpen() then
		self.view:Flush()
	else
		self.view:Open()
	end
end

-- 打开界面
function ExperienceFbWgCtrl:ShowWaveReward(wave_cell)
	if self.view:IsOpen() then
		self.view:ShowWaveReward(wave_cell)
	end
end

-- 打开场景界面
function ExperienceFbWgCtrl:OpenExperienceFbSceneView()
	if self.experience_fb_scene_view:IsOpen() then
		self.experience_fb_scene_view:Flush()
	else
		self.experience_fb_scene_view:Open()
	end
end

function ExperienceFbWgCtrl:GetExperienceFbSceneView()
	return self.experience_fb_scene_view
end

function ExperienceFbWgCtrl:FlushExperienceFbSceneView(index, key, param_t)
	if self.experience_fb_scene_view:IsOpen() then
		self.experience_fb_scene_view:Flush(index, key, param_t)
	end
end

function ExperienceFbWgCtrl:CloseExperienceFbSceneView()
	self.experience_fb_scene_view:Close()
end

-- 打开当前关卡排行榜
function ExperienceFbWgCtrl:OpenExperienceFbRankView(curr_level)
	self.experience_fb_rank_view:SetCurrlevel(curr_level)

	if self.experience_fb_rank_view:IsOpen() then
		self.experience_fb_rank_view:Flush()
	else
		self.experience_fb_rank_view:Open()
	end
end

-- 打开当前关卡buff总览
function ExperienceFbWgCtrl:OpenExperienceFbBuffView(curr_level)
	self.experience_fb_buff_view:SetCurrlevel(curr_level)

	if self.experience_fb_buff_view:IsOpen() then
		self.experience_fb_buff_view:Flush()
	else
		self.experience_fb_buff_view:Open()
	end
end

-- 打开总属性展示
function ExperienceFbWgCtrl:OpenExperienceFbCardTipsView()
	if self.experience_fb_card_tips_view:IsOpen() then
		self.experience_fb_card_tips_view:Flush()
	else
		self.experience_fb_card_tips_view:Open()
	end
end
