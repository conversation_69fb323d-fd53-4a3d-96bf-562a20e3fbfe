-- 品质控制器
require("quality/quality_wg_data")
QualityManager = QualityManager or BaseClass(BaseWGCtrl)

local MaxQualityLevel = 4 				-- 画质品质等级
local AutoQualityLevelBuffer = 4 		-- 自动降低画质的缓冲区数量
local DynamicLimitFps = true  -- 是否开启动态限制帧数

local platform = UnityEngine.Application.platform
if platform == UnityEngine.RuntimePlatform.Android then
	if DeviceTool.IsEmulator() then
		-- 安卓模拟器不开启
		DynamicLimitFps = false
	end
elseif platform == UnityEngine.RuntimePlatform.WindowsPlayer then
	DynamicLimitFps = false
elseif platform == UnityEngine.RuntimePlatform.WindowsEditor or
	platform == UnityEngine.RuntimePlatform.OSXEditor then
	DynamicLimitFps = false
end

function QualityManager:__init()
	if QualityManager.Instance then
		print_error("QualityManager to create singleton twice")
	end
	QualityManager.Instance = self

	self.data = QualityWGData.New()
	self.quality_level = QualityConfig.QualityLevel
	self.part_quality_level = 0
	self.quality_level_offset = 0

	self.quality_policy = QualityPolicy.UnitQuality

	self.rule_list = {}
    self.optimize_rules = {}
    self.part_quality_level_rules = {}
    self.optimize_rule_tbl = {}
    self.part_quality_level_rule_tbl = {}
    self.optimize_rule_index = 0

	self.last_is_low_fps = false
	self.same_fps_times = 0
	self.custom_target_fps = -1
	self.use_high_fps = true
	self.limit_fps = false
	self.total_point = 0

    self:InitSettingShield()
    self:InitSceneOptimeze()
    self:InitPartQualityLevelRule()
    self:InitOtherRulers()
    self:BindAllEvent()

    self.quality_node = QualityConfig.ListenQualityChanged(function()
        self:OnQualityLevelOffsetChanged()
    end)

    self.fps_callback = BindTool.Bind1(self.FpsCallBack, self)
    local fpsSampler = GameRoot.Instance:GetFPSSampler()
	if fpsSampler then
		fpsSampler.FPSEvent = fpsSampler.FPSEvent + self.fps_callback
	end

	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnChangeScene, self))

    Runner.Instance:AddRunObj(self, 8)
end

function QualityManager:__delete()
	QualityManager.Instance = nil
	Runner.Instance:RemoveRunObj(self)
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	local fpsSampler = GameRoot.Instance:GetFPSSampler()
	if fpsSampler then
		fpsSampler.FPSEvent = fpsSampler.FPSEvent - self.fps_callback
	end

	if self.quality_node ~= nil then
	    QualityConfig.UnlistenQualtiy(self.quality_node)
	    self.quality_node = nil
	end

	for k,v in pairs(self.rule_list) do
		v:DeleteMe()
	end

	self.rule_list = {}
	self.optimize_rules = {}
	self.part_quality_level_rules = {}

	self.shield_others_rule = nil
	self.shield_samecamp_rule = nil
	self.shield_monster_rule = nil
	self.shield_role_skill_rule = nil
	self.shield_main_role_skill_rule = nil
	self.shield_camera_shake_rule = nil
	self.shield_radial_blur_rule = nil
	self.shield_follow_title_rule = nil
	self.shield_weather_rule = nil
	self.shield_simple_shadow_rule = nil
	self.shield_projector_shadow_rule = nil
	self.shield_gameobject_attach_shadow_rule = nil
	self.auto_shield_weather_rule = nil
end

function QualityManager:SetQualityPolicy(policy)
	self.quality_policy = policy
end

function QualityManager:CalcQualityLevel()
	local cur_quality_level = QualityConfig.QualityLevel
	self.quality_level = cur_quality_level

	local diff = 2 - cur_quality_level
	local offset = self.quality_level_offset

	-- 优先保证单位品质
	if diff > 0 and self.quality_policy == QualityPolicy.UnitQuality then
		if offset <= diff then
			self.quality_level = cur_quality_level + offset
			offset = 0
		else
			self.quality_level = cur_quality_level + diff
			offset = offset - diff
			if offset > AutoQualityLevelBuffer then
				self.quality_level = self.quality_level + offset - AutoQualityLevelBuffer
				self.quality_level = math.min(self.quality_level, MaxQualityLevel)
			end
		end
	else
		-- 优先保证单位数量
		if offset > AutoQualityLevelBuffer then
			self.quality_level = math.min(cur_quality_level + offset - AutoQualityLevelBuffer, MaxQualityLevel)
		end
	end

	self.part_quality_level = offset
end

function QualityManager:GetQualityLevel()
	return self.quality_level
end

function QualityManager:Update()
	local optimize_rule_count = #self.optimize_rules
	if optimize_rule_count > 0 then
		self.optimize_rule_index = self.optimize_rule_index + 1
		if self.optimize_rule_index > optimize_rule_count then
			self.optimize_rule_index = 1
		end
		local rule = self.optimize_rules[self.optimize_rule_index]
		if rule then
			rule.rule:Update()
		end
	end
end

function QualityManager:InitSettingShield()
	---------------------------------------- 设置面板可以调节的规则 ---------------------------------

	-- 屏蔽其他玩家
	self.shield_others_rule = self:RegisterRule(SettingRule, ShieldObjType.Role, ShieldRuleWeight.Low)
	-- 屏蔽友方玩家
	self.shield_samecamp_rule = self:RegisterRule(SimpleRule, ShieldObjType.Role, ShieldRuleWeight.Low, false, BindTool.Bind1(self.ShieldSameCamp, self))
	-- 屏蔽Monster
	self.shield_monster_rule = self:RegisterRule(SettingRule, ShieldObjType.Monster, ShieldRuleWeight.Low)
	-- 屏蔽其他玩家技能
	self.shield_role_skill_rule = self:RegisterRule(SettingRule, ShieldObjType.RoleSkillEffect, ShieldRuleWeight.Low)
	-- 屏蔽主角技能
	self.shield_main_role_skill_rule = self:RegisterRule(SettingRule, ShieldObjType.MainRoleSkillEffect, ShieldRuleWeight.Low)
	-- 屏蔽震屏
	self.shield_camera_shake_rule = self:RegisterRule(SettingRule, ShieldObjType.MainRoleCameraShake, ShieldRuleWeight.Low)
	-- 技能径向模糊
	self.shield_radial_blur_rule = self:RegisterRule(SettingRule, ShieldObjType.MainRoleRadialBlur, ShieldRuleWeight.Low)
	
	-- 屏蔽称号
	self.shield_follow_title_rule = self:RegisterRule(SettingRule, ShieldObjType.FollowTitle, ShieldRuleWeight.Low)
	-- 屏蔽天气
	self.shield_weather_rule = self:RegisterRule(SettingRule, ShieldObjType.Weather, ShieldRuleWeight.Low)
end

-- 其它一些写死的规则
function QualityManager:InitOtherRulers()
	-- Role屏蔽时自动屏蔽FollowObj
	self:RegisterRule(SimpleRule, ShieldObjType.FollowObj, ShieldRuleWeight.Low, true, BindTool.Bind1(self.ShieldFollowObj, self))
    -- 默认屏蔽其他玩家的震屏效果
	self:RegisterRule(SettingRule, ShieldObjType.RoleCameraShake, ShieldRuleWeight.Low, true)
    -- 默认屏蔽所有MonsterFollowUi
	self:RegisterRule(SettingRule, ShieldObjType.MonsterFollowUI, ShieldRuleWeight.Low, true)
    -- 默认屏蔽所有GatherFollowUi
	self:RegisterRule(SettingRule, ShieldObjType.GatherFollowUI, ShieldRuleWeight.Low, true)
    -- 默认屏蔽所有FollowObjectFollowUi
	self:RegisterRule(SettingRule, ShieldObjType.FollowObjectFollowUI, ShieldRuleWeight.Low, true)
	-- 高配时屏蔽主角SimpleShadow
	-- self.shield_simple_shadow_rule = self:RegisterRule(SettingRule, ShieldObjType.MainRoleShadow, ShieldRuleWeight.Max)
	-- 低配时屏蔽主角ProjectorShadow
	self.shield_projector_shadow_rule = self:RegisterRule(SettingRule, ShieldObjType.ProjectorShadow, ShieldRuleWeight.Max)
	-- 注册GameObjectAttach的屏蔽规则
	self.shield_gameobject_attach_shadow_rule = self:RegisterRule(SimpleRule, ShieldObjType.GameObjectAttach, ShieldRuleWeight.Low, true,
		BindTool.Bind1(self.ShieldGameObjectAttach, self))
	-- 注册自动屏蔽天气规则
	self.auto_shield_weather_rule = self:RegisterRule(SimpleRule, ShieldObjType.Weather, ShieldRuleWeight.Low, true,
		BindTool.Bind1(self.AutoShieldWeather, self))
	-- 注册Role特效LOD规则
	self:RegisterRule(EffectLodRule, ShieldObjType.RoleEffect, ShieldRuleWeight.High, true)

	self.shield_npc_talk_rule = self:RegisterRule(SettingRule, nil, ShieldRuleWeight.Max)

	if self.quality_level == 0 then
		-- self.shield_simple_shadow_rule:Register()
	else
		self.shield_projector_shadow_rule:Register()
	end
end

function QualityManager:BindAllEvent()
	-- 监听设置面板
	self:BindSettingEvent(SettingEventType.SHIELD_OTHERS, self.shield_others_rule)
	self:BindSettingEvent(SettingEventType.SHIELD_SAME_CAMP, self.shield_samecamp_rule)
	self:BindSettingEvent(SettingEventType.SHIELD_ENEMY, self.shield_monster_rule)
	self:BindSettingEvent(SettingEventType.SKILL_EFFECT, self.shield_role_skill_rule)
	self:BindSettingEvent(SettingEventType.SELF_SKILL_EFFECT, self.shield_main_role_skill_rule)
	self:BindSettingEvent(SettingEventType.CLOSE_SHOCK_SCREEN, self.shield_camera_shake_rule)
	self:BindSettingEvent(SettingEventType.CLOSE_SKILL_RADIAL_BLUR, self.shield_radial_blur_rule)
	
	self:BindSettingEvent(SettingEventType.CLOSE_TITLE, self.shield_follow_title_rule)
	self:BindSettingEvent(SettingEventType.CLOSE_WEATHWE, self.shield_weather_rule)

	-- 其它事件
	self:BindGlobalEvent(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.TeamInfoChange, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_REALIVE, BindTool.Bind1(self.RefreshShieldSameCampRule, self))
    self:BindGlobalEvent(SettingEventType.Change_Attack_Mode, BindTool.Bind1(self.RefreshShieldSameCampRule, self))
    self:BindGlobalEvent(ObjectEventType.NPC_TALK, BindTool.Bind1(self.ShiledByNpcTalk, self))
    self:BindGlobalEvent(SettingEventType.CHANGE_FPS, BindTool.Bind1(self.OnTargetFrameRateChanged, self))
end

-- 注册一个屏蔽规则
function QualityManager:RegisterRule(rule_class, shield_obj_type, rule_weight, auto_register, judge_func)
	local rule = rule_class.New(shield_obj_type, rule_weight, judge_func)
	if auto_register then
		rule:Register()
	end
	table.insert(self.rule_list, rule)
	return rule
end

function QualityManager:BindSettingEvent(event_type, shield_rule)
	self:BindGlobalEvent(event_type, function (value)
		if value then
			shield_rule:Register()
		else
			shield_rule:UnRegister()
		end
	end)
end

-- 设置各种类型的最大显示上限
function QualityManager:InitSceneOptimeze()
	for shield_obj_type, _ in pairs(QualityWGData.SceneObjMaxAppearConut) do
		self:RegisterOptimizeRule(shield_obj_type)
	end

	self:ChangeMaxAppearCount(self.quality_level)
end

function QualityManager:InitPartQualityLevelRule()
	for shield_obj_type, max_quality_level in pairs(QualityWGData.PartQualityLevel) do
		self:RegisterPartQualityLevelRule(shield_obj_type, max_quality_level)
	end
end

function QualityManager:RegisterOptimizeRule(shield_obj_type)
	if self.optimize_rule_tbl[shield_obj_type] then
		print_error("RegisterOptimizeRule Twice !!!")
		return
	end
	self.optimize_rule_tbl[shield_obj_type] = true

	local rule = self:RegisterRule(SceneObjOptimizeRule, shield_obj_type, ShieldRuleWeight.High, true)
	table.insert(self.optimize_rules, {shield_obj_type = shield_obj_type, rule = rule})
end

function QualityManager:RegisterPartQualityLevelRule(shield_obj_type, max_quality_level)
	if self.part_quality_level_rule_tbl[shield_obj_type] then
		print_error("RegisterPartQualityLevelRule Twice !!!")
		return
	end
	self.part_quality_level_rule_tbl[shield_obj_type] = true

	local rule = self:RegisterRule(PartQualityLevelRule, shield_obj_type, ShieldRuleWeight.High, true)
	rule:SetMaxQualityLevel(max_quality_level)
	table.insert(self.part_quality_level_rules, rule)
end

-- 根据游戏品质控制显示上限
function QualityManager:ChangeMaxAppearCount(quality_level)
	for _, v in ipairs(self.optimize_rules) do
		local max_appear_count = QualityWGData.Instance:GetMaxAppearCount(v.shield_obj_type, quality_level)
		v.rule:SetMaxAppearCount(max_appear_count)
	end
end

function QualityManager:FpsCallBack(fps)
	-- 画质降低时，fps要求也降低，防止太过容易触发极限画质 min_fps区间(16 ~ 24)
	local min_fps = 24 - self.quality_level_offset
	local is_low_fps = fps < min_fps
	if is_low_fps == self.last_is_low_fps then
		self.same_fps_times = self.same_fps_times + 1
	else
		self.same_fps_times = 1
	end
	self.last_is_low_fps = is_low_fps

	local last_offset = self.quality_level_offset
	if self.same_fps_times >= 3 then
		self.same_fps_times = 0
		if is_low_fps then
			self.quality_level_offset = math.min(self.quality_level_offset + 1, MaxQualityLevel + AutoQualityLevelBuffer)
		else
			self.quality_level_offset = math.max(self.quality_level_offset - 1, 0)
		end
	end

	if last_offset ~= self.quality_level_offset then
		self:OnQualityLevelOffsetChanged()
	end

	if DynamicLimitFps then
		self:CalcTargetFrame(fps)
	end
end

local first_enter_scene = true
function QualityManager:OnChangeScene()
	if self.quality_level_offset ~= 0 then
		self.quality_level_offset = 0
		self:OnQualityLevelOffsetChanged()
	end

	if first_enter_scene then
		first_enter_scene = false
		self.total_point = 0
		self.limit_fps = false
	end
end

function QualityManager:OnQualityLevelOffsetChanged()
	local old_level = self.quality_level
	self:CalcQualityLevel()

	self:OnQualityChanged()
	self:OnPartQualityLevelChanged()

	if old_level ~= self.quality_level and old_level * self.quality_level == 0 and Scene.Instance ~= nil then
		Scene.Instance:UpdateSceneQuality()
	end
end

--  0.高配 1.中配 2.低配 3.超低配
function QualityManager:OnQualityChanged()
    self:ChangeMaxAppearCount(self.quality_level)

    if self.quality_level == 0 then
    	-- self.shield_simple_shadow_rule:Register()
    	self.shield_projector_shadow_rule:UnRegister()
    else
    	self.shield_projector_shadow_rule:Register()
    	-- self.shield_simple_shadow_rule:UnRegister()
    end
    self.shield_gameobject_attach_shadow_rule:RefreshRule()
    self.auto_shield_weather_rule:RefreshRule()
end

function QualityManager:OnPartQualityLevelChanged()
	for _, v in ipairs(self.part_quality_level_rules) do
		v:SetCurQualityLevel(self.part_quality_level)
	end
end

function QualityManager:GetOptimizeRule(shield_obj_type)
	for k,v in pairs(self.optimize_rules) do
		if v.shield_obj_type == shield_obj_type then
			return v.rule
		end
	end
end

function QualityManager:TeamInfoChange()
	self:RefreshShieldSameCampRule()
end

function QualityManager:RefreshShieldSameCampRule()
	self.shield_samecamp_rule:RefreshRule()
end

function QualityManager:ShieldSameCamp(obj)
	return not Scene.Instance:AttackModeIsEnemy(obj)
end

function QualityManager:ShieldFollowObj(obj)
	local shield = true
	local owner_obj = obj:GetOwnerObj()
	if owner_obj and owner_obj.IsRole and owner_obj:IsRole() then
		shield = owner_obj:IsShieldFollowObj()
	end
	return shield
end

-- 挂在SceneObj身上的GameObjeceAttach组件的屏蔽逻辑（Role不再使用这套逻辑）
function QualityManager:ShieldGameObjectAttach(obj)
	local shield = true
	local scene_obj = obj:GetSceneObj()
	if nil ~= scene_obj and not scene_obj:IsDeleted() then
		if scene_obj:IsMonster() and self.quality_level < 2 then
			shield = false
		elseif scene_obj:IsNpc() and self.quality_level < 2 then
			shield = false
		elseif scene_obj:IsGather() and self.quality_level < 1 then
			shield = false
		elseif scene_obj:IsBossStone() and self.quality_level < 1 then
			shield = false
		end
	end

	return shield
end

-- 帧率下降时或者非高画质时自动屏蔽天气
function QualityManager:AutoShieldWeather(obj)
	local shield = self.quality_level_offset > 0 or self.quality_level > 1
	return shield
end

-- NPC对话屏蔽场景对象
function QualityManager:ShiledByNpcTalk(value)
	if Scene and Scene.Instance and self.shield_npc_talk_rule ~= nil then
		local value = Scene.Instance:GetIsNpcTalkLock()
	    local list = Scene.Instance:GetObjList()
	    for k,v in pairs(list) do
	        if v ~= nil then
	        	if value then
	        		v:AddShieldRuleByRule(self.shield_npc_talk_rule)
	        	else
	        		v:RemoveShieldRuleNoDel(self.shield_npc_talk_rule)
	        	end
	        end
	    end
	end
end

function QualityManager:SetCustomTargetFrame(fps)
	self.custom_target_fps = fps
	fps = self:GetFrameRate()
	self:ChangeTargetFrameRate(fps)
end

function QualityManager:ChangeTargetFrameRate(fps)
	if GAME_FPS ~= fps then
		GAME_FPS = fps
		UnityEngine.Application.targetFrameRate = fps
	end
end

-- 是否高帧率模式(设置界面可以控制)
function QualityManager:OnTargetFrameRateChanged(use_high_fps)
	self.use_high_fps = use_high_fps
	self.total_point = 0
	self.limit_fps = false
	local fps = self:GetFrameRate()
	self:ChangeTargetFrameRate(fps)
end

function QualityManager:GetFrameRate()
	if not DEVELOPMENT_BUILD then
		if self.custom_target_fps >= 0 then
			return self.custom_target_fps
		end
	end

	local fps = 60
	if self.limit_fps then
		fps = 30
	elseif not self.use_high_fps then
		if UNITY_IOS then
			fps = 30 --ios必须是30的倍数
		else
			fps = 30
		end
	end

	return fps
end

-- 动态限制帧数，目的是为了尽可能减少发热量
-- 如果帧数长期低于设定值，则说明CPU一直处于高度负荷状态，容易导致发热而降频
function QualityManager:CalcTargetFrame(fps)
	local target_frame = self:GetFrameRate()
	local point = 0
	if fps <= math.floor(target_frame * 0.6) then -- 36
		point = -4
	elseif fps <= math.floor(target_frame * 0.7) then -- 42
		point = -3
	elseif fps <= math.floor(target_frame * 0.8) then -- 48
		point = -2
	elseif fps <= math.floor(target_frame * 0.9) then -- 54
		point = -1
	elseif fps <= math.floor(target_frame * 0.95) then -- 57
		point = 1
	else
		point = 2
	end

	self.total_point = self.total_point + point
	self.total_point = math.min(self.total_point, 200)
	self.total_point = math.max(self.total_point, -100)
	-- 大于100分，取消限制
	if self.total_point >= 100 then
		self.limit_fps = false
	-- 低于-100分，开启限制
	elseif self.total_point <= -100 then
		self.limit_fps = true
	end

	local fps = self:GetFrameRate()
	self:ChangeTargetFrameRate(fps)
end