-- X-星命之门.xls
local item_table={
[1]={item_id=28001,num=1,is_bind=1},
[2]={item_id=28002,num=1,is_bind=1},
[3]={item_id=28003,num=1,is_bind=1},
[4]={item_id=28004,num=1,is_bind=1},
[5]={item_id=28005,num=1,is_bind=1},
[6]={item_id=28006,num=1,is_bind=1},
[7]={item_id=28007,num=1,is_bind=1},
[8]={item_id=28008,num=1,is_bind=1},
[9]={item_id=28009,num=1,is_bind=1},
[10]={item_id=28010,num=1,is_bind=1},
[11]={item_id=28011,num=1,is_bind=1},
[12]={item_id=28012,num=1,is_bind=1},
[13]={item_id=28013,num=1,is_bind=1},
[14]={item_id=28014,num=1,is_bind=1},
[15]={item_id=28015,num=1,is_bind=1},
[16]={item_id=28016,num=1,is_bind=1},
[17]={item_id=28017,num=1,is_bind=1},
[18]={item_id=28018,num=1,is_bind=1},
[19]={item_id=28019,num=1,is_bind=1},
[20]={item_id=28020,num=1,is_bind=1},
[21]={item_id=28021,num=1,is_bind=1},
[22]={item_id=28022,num=1,is_bind=1},
[23]={item_id=28023,num=1,is_bind=1},
[24]={item_id=28024,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
reward_box={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[9],},model_pos="0|28|0",show_box_modelid="3_1_lan",name="凡品魂池",},
{min_draw_count=26,max_draw_count=75,reward_drop_id=10350,reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8],[5]=item_table[9],[6]=item_table[10],[7]=item_table[11],[8]=item_table[12],},model_bundle_name="model/uiother/6_prefab",model_asset_name=6,show_box_modelid="3_2_zi",name="精品魂池",box_level=2,},
{min_draw_count=76,max_draw_count=150,reward_drop_id=10351,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8],[4]=item_table[9],[5]=item_table[10],[6]=item_table[11],[7]=item_table[12],[8]=item_table[13],},model_bundle_name="model/uiother/7_prefab",model_asset_name=7,show_box_modelid="3_3_cheng",name="上品魂池",box_level=3,},
{min_draw_count=151,max_draw_count=300,reward_drop_id=10352,reward_item={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13],[6]=item_table[14],[7]=item_table[15],[8]=item_table[16],},show_box_modelid="3_4_hong",name="极品魂池",box_level=4,},
{min_draw_count=301,max_draw_count=500,reward_drop_id=10353,show_box_modelid="3_5_feng",name="仙品魂池",box_level=5,},
{min_draw_count=501,max_draw_count=800,reward_drop_id=10354,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18],[7]=item_table[19],[8]=item_table[20],},show_box_modelid="3_6_jin",name="神品魂池",box_level=6,},
{min_draw_count=801,max_draw_count=1200,reward_drop_id=10355,name="圣品魂池",box_level=7,},
{min_draw_count=1201,max_draw_count=1700,reward_drop_id=10356,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17],[6]=item_table[18],[7]=item_table[19],[8]=item_table[20],},box_level=8,},
{min_draw_count=1701,max_draw_count=2300,reward_drop_id=10357,box_level=9,},
{min_draw_count=2301,max_draw_count=3000,reward_drop_id=10358,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20],[5]=item_table[21],[6]=item_table[22],[7]=item_table[23],[8]=item_table[24],},box_level=10,}
},

reward_box_meta_table_map={
},
other_default_table={open_day=1,consume_item=39144,},

reward_box_default_table={min_draw_count=1,max_draw_count=25,draw_cost=1000,reward_drop_id=10349,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14],[6]=item_table[15],[7]=item_table[16],[8]=item_table[17],},model_bundle_name="model/uiother/5_prefab",model_asset_name=5,model_scale=12,model_pos="0|0|0",show_box_modelid="3_7_cai",name="无上魂池",box_level=1,}

}

