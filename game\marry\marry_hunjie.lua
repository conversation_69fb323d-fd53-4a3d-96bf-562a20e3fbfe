local VECTOR2_POS = Vector2(570,100)

------------------------------------------------
--#region
function MarryView:InitHunJieView()
	if not self.is_init_hunjie then
		self.is_init_hunjie = true
		self.lbl_prog_exp = self.node_list["lbl_process_text"]
		self.lbl_prog_exp.text.text = "0/0"

		self.alert_window = Alert.New(nil, nil, nil, nil, true)

		self:CreateHeart()
		self:CreateAttrUpGradeCell()

		XUI.AddClickEventListener(self.node_list["btn_hunjie_tips"], BindTool.Bind1(self.OpenHunJieTips, self))

		--一键提升
        XUI.AddClickEventListener(self.node_list["btn_quality_up_auto"], BindTool.Bind1(self.OnAutoUp<PERSON><PERSON><PERSON><PERSON><PERSON>, self))

        XUI.AddClickEventListener(self.node_list.btn_active_hunjie, BindTool.Bind1(self.OnClickActivehunjie, self))

        self.marry_attr_list = {}
        local cur_node = self.node_list.layout_cur_attr_xianlv
        local next_node = self.node_list.layout_next_attr_xianlv
        for i = 1, 4 do
            local info = {}
            info.attr_txt = self:GetNodeList(cur_node, i, "attr_name")
            info.value_txt = self:GetNodeList(cur_node, i, "attr_value")
            info.next_value = self:GetNodeList(next_node, i, "next_value")
            info.img_add = self.node_list["add_img"..i]
            self.marry_attr_list[i] = info
        end
	end
	self.cur_ring_index = 0

end

function MarryView:GetNodeList(parent, index, str)
    local obj = parent.transform:Find(str..index).gameObject
    local item = U3DObject(obj, obj.transform, self)
    return item
end

function MarryView:DeleteHunJieView()

	if nil ~= self.ph_quality_need then
		self.ph_quality_need:DeleteMe()
		self.ph_quality_need = nil
	end

	if nil ~= self.lock_item then
		self.lock_item:DeleteMe()
		self.lock_item = nil
	end

	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if self.hunjie_list then
		self.hunjie_list:DeleteMe()
		self.hunjie_list = nil
	end

	self.heart_list = nil
	self.bg_heart_list = nil
	self.lbl_prog_exp = nil
 	self.qua_suc_eff = nil
 	self.heart_up_eff = nil
 	self.is_init_hunjie = nil
 	self.hunjie_btn_str = nil
end

function MarryView:CreateHeart()
	self.heart_list = {}
	self.bg_heart_list = {}
	for i = 1, 10 do
		local ph_heart = self.node_list["active_star_" .. i]
		local bg_heart = self.node_list["Bg_" .. i]
		self.heart_list[i] = ph_heart
		self.bg_heart_list[i] = bg_heart
	end
end

function MarryView:CreateAttrUpGradeCell()
	local param = self.node_list["ph_quality_need"]
	self.ph_quality_need = ItemCell.New(param)
	self.ph_quality_need:SetNeedItemGetWay(true)
	self.ph_quality_need:SetHideRightDownBgLessNum(-1)

	self.lock_item = ItemCell.New(self.node_list["lock_item"]) --❤中间的物品格子
end
--#endregion	
----------------------------------------------------


-----------------------------------------
--#region刷新逻辑
--背包物品发生改变
function MarryView:HunJieDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if self:IsOpen() and self:IsLoadedIndex(TabIndex.marry_hunjie) and old_num and new_num and new_num > old_num then
		self:SetLoverPanel()
	end
end

--刷新婚戒装备格子
function MarryView:FlushHunJieView()
	if self:IsPlayHunJieAni() then
		return
	end
	self:FlushHunJieSlider()
	self:CreatHunJieBeSkill()
end

--刷新婚戒的表现
function MarryView:FlushHunJieSlider()
	self:ChangeUpQuality()
end

function MarryView:UpdateHunJieNewAttr()
	self:FlushRingName()    --显示婚戒的名字和婚戒图片
	self:FlushHeartSprite() --刷新心
	self:FlushRingAttr()	--刷新属性
	self:SetLoverPanel() 	--更新升级材料
end

function MarryView:OnClickActivehunjie()
    local stuff_num = ItemWGData.Instance:GetItemNumInBagById(MARRY_OTHER_TYPE.ITEM_ID_TXS)
    if stuff_num <= 0 then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = MARRY_OTHER_TYPE.ITEM_ID_TXS})
    else
        local index = ItemWGData.Instance:GetItemIndex(MARRY_OTHER_TYPE.ITEM_ID_TXS)
        if index == -1 then --因为tips可能会改变item_id，，，好蛋疼
            index = ItemWGData.Instance:GetItemIndex(MARRY_OTHER_TYPE.VIRTUAL_TXS_ID)
        end
        local item_cfg = ItemWGData.Instance:GetItemConfig(MARRY_OTHER_TYPE.ITEM_ID_TXS)
        local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
        if equip_index ~= -1 then
             BagWGCtrl.Instance:SendUseItem(index, 1, equip_index)
        end
    end
end

function MarryView:UpdateHunJieOldLevelAttr()
	self:CanPlayBabyEffect(true)
	local data = MarryWGData.Instance
	local equip_info = data:GetRingInfo(self.cur_ring_index)
	local data = MarryWGData.Instance
	local ring_cfg = data:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level) --最新等级
	local old_ring_cfg = data:GetEquipCfgById(self.old_id, self.old_star_level) --最新等级
    if IsEmptyTable(ring_cfg) then
        print_error("ring_cfg = nil", "item_id", equip_info.item_id,"star_level", equip_info.param.star_level)
        return
    end
    if IsEmptyTable(old_ring_cfg) then
        print_error("old_ring_cfg = nil", "item_id", self.old_id,"star_level", self.old_star_level)
        return
    end

	local is_max_level = data:GetEquipIsMax(equip_info.item_id, equip_info.param.star_level)

	local level_add = ring_cfg.level - old_ring_cfg.level
	local add_exp_num = 0
	local total_exp = 0

	for i = 1, level_add do
		add_exp_num = add_exp_num + data:GetEquipCfgByIndex(self.cur_ring_index, old_ring_cfg.level + i - 1).exp
	end
	add_exp_num = add_exp_num - self.old_param2 + equip_info.param.param1
	total_exp = is_max_level and 1 or ring_cfg.exp

	self:PlayHunJieAni(total_exp , is_max_level, level_add, self.old_star_level, equip_info.param.param1, add_exp_num)
end

--质量发生改变
function MarryView:ChangeUpQuality()
	local data = MarryWGData.Instance
	local equip_info = data:GetRingInfo(self.cur_ring_index)
	local boo_ring = self.old_id and self.old_id == equip_info.item_id -- true 表示为当前阶级的戒子
	local boo_next_ring = boo_ring
	if boo_next_ring == false then
		boo_next_ring = self.old_id and (equip_info.item_id - self.old_id == 1) --表示为下一阶级的戒子
	end

	if (boo_next_ring and self.old_star_level and equip_info.param.star_level ~= self.old_star_level) or
	   self.old_param2 and self.old_param2 ~= equip_info.param.param1 then --表示当前等级/星级发生改变
	   self:UpdateHunJieOldLevelAttr()
	else
		--表示什么都没有发生：无需播放动画
		self:UpdateHunJieNewAttr()
		local equip_info = data:GetRingInfo(self.cur_ring_index)
		local ring_cfg = data:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)
		if ring_cfg then
			if MarryWGData.Instance:GetEquipIsMax(equip_info.item_id, equip_info.param.star_level) then
				self.lbl_prog_exp.text.text = "max"
				self:SetHunJieProgressVal(1)
            else
                if equip_info.param.param1 then
                    local persent = equip_info.param.param1 / ring_cfg.exp
                    self.lbl_prog_exp.text.text = equip_info.param.param1 .. "/" .. ring_cfg.exp
                    self:SetHunJieProgressVal(persent)
                end
			end
		else
			self.lbl_prog_exp.text.text = "0/0"
			self:SetHunJieProgressVal(0)
		end
	end

	if equip_info.item_id > 0 and MarryWGData.Instance:IsActiveHunjie() then
		self.old_param2 = equip_info.param.param1
		self.old_id = equip_info.item_id
		self.old_star_level = equip_info.param.star_level
	else
		self.old_param2 = nil
		self.old_id = nil
		self.old_star_level = nil
	end

	local limit_level, is_can_goon = MarryWGData.Instance:GetTongXinSuoLimintLevel()

	if data:GetEquipIsMax(equip_info.item_id, equip_info.param.star_level) then
		self:SetHunJieBtnStr(Language.Common.MaxGrade)
		self:SetHunJieRemindState(false)
		XUI.SetButtonEnabled(self.node_list["btn_quality_up_auto"], false)
	elseif not is_can_goon then
		self:SetHunJieRemindState(false)
		self:SetHunJieBtnStr(Language.Marry.QualityUp)
		XUI.SetButtonEnabled(self.node_list["btn_quality_up_auto"], false)
	elseif self:IsAutoUpEquip() == false then
		self:SetHunJieBtnStr(Language.Marry.QualityUp)
		XUI.SetButtonEnabled(self.node_list["btn_quality_up_auto"], true)
	else
		XUI.SetButtonEnabled(self.node_list["btn_quality_up_auto"], true)
		self:SetHunJieBtnStr(Language.Marry.Stop)
	end
end

-- 升阶材料
function MarryView:SetLoverPanel()
    local up_ring_cfg = self:GetUpQualityCfg()
    if not MarryWGData.Instance:IsActiveHunjie() then --没有激活同心锁
        local item_num = ItemWGData.Instance:GetItemNumInBagById(MARRY_OTHER_TYPE.ITEM_ID_TXS)
        self.ph_quality_need:SetData({item_id = MARRY_OTHER_TYPE.ITEM_ID_TXS})
        self.ph_quality_need:SetRightTopImageTextActive(false)
        local is_enable_num = item_num > 0
        local color = is_enable_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        self.ph_quality_need:SetRightBottomTextVisible(true)
        self.ph_quality_need:SetRightBottomColorText(item_num .. "/" .. 1, color)
        self:SetHunJieRemindState(item_num >= 1)
    else
        --local order = MarryWGData.Instance:GetTongXinSuoOrder()
        --order = order > 12 and 12 or order  --资源不够，12阶之后现在用一个
        --local bundle, asset = ResPath.GetJieHunSuoImg("txs_"..order)
        -- self.node_list.hunjie_suo.image:LoadSprite(bundle, asset, function()
        --     self.node_list.hunjie_suo.image:SetNativeSize()
        -- end)
        -- self.node_list.hunjie_suo.rect.localScale = Vector3(0.8, 0.8, 0.8)
        if up_ring_cfg then
            local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
            local stuff_num = ItemWGData.Instance:GetItemNumInBagById(up_ring_cfg.stuff_id)
            self.ph_quality_need:SetData({item_id = up_ring_cfg.stuff_id, num = stuff_num, is_bind = 0})
            local equip_info = MarryWGData.Instance:GetRingInfo(self.cur_ring_index)
            if nil ~= equip_info or equip_info.item_id > 1 then
                local boo = not MarryWGData.Instance:GetEquipIsMax(equip_info.item_id, equip_info.param.star_level) and stuff_num > 0
                local limit_level, is_can_goon = MarryWGData.Instance:GetTongXinSuoLimintLevel()
                local show_mat_up_remind = MarryWGData.Instance:CanLevelUpRemind() > 0
                self:SetHunJieRemindState(boo and is_can_goon and show_mat_up_remind)
                -- if boo and is_can_goon then
                --     self:UpdateHunJieView()
                -- end
            end
        else
            self.ph_quality_need:SetData()
            self:SetHunJieRemindState(false)
        end
    end

end
--#endregion
-----------------------------------------

--显示婚戒的名字和婚戒图片
function MarryView:FlushRingName()
    local equip_info = MarryWGData.Instance:GetRingInfo(self.cur_ring_index)
	if not equip_info then
		return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_info.item_id)
    local ring_cfg = MarryWGData.Instance:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)
    if item_cfg then
        if ring_cfg then
            self.node_list["lbl_hunjie_name"].text.text = item_cfg.name
            self.node_list["des_jinjie_txt"].text.text = Language.Marry.JinJieExp
            self.node_list.hunjie_slider:SetActive(true)
        else
            self.node_list.hunjie_slider:SetActive(false)
            self.node_list["lbl_hunjie_name"].text.text = Language.Marry.NoHunJie
            self.node_list["des_jinjie_txt"].text.text =  Language.Marry.Hunjiejihuo
        end
		self.node_list["Common_Capability_Style"]:SetActive(true)
	else
		self.node_list["lbl_hunjie_name"].text.text = Language.Marry.NoHunJie
		self.node_list["Common_Capability_Style"]:SetActive(false)
	end
end

--刷新心是否为灰色 就是装备的等级
function MarryView:FlushHeartSprite()
    local equip_info = MarryWGData.Instance:GetRingInfo(self.cur_ring_index)
    for k,v in pairs(self.heart_list) do
        if MarryWGData.Instance:IsActiveHunjie() then
            if k > equip_info.param.star_level then
                v:SetActive(false)
            else
                v:SetActive(true)
            end
        else
            v:SetActive(false)
        end
    end
end

--未激活的情缘属性加成显示
function MarryView:FlushNotActiveAttr()
    local ring_cfg = MarryWGData.Instance:GetEquipCfgById(MARRY_OTHER_TYPE.ITEM_ID_TXS, 0)
    local next_ring_cfg = MarryWGData.Instance:GetEquipCfgById(MARRY_OTHER_TYPE.ITEM_ID_TXS, 1)
    local tb = {}
    for k, v in pairs(ring_cfg) do
        if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k) and next_ring_cfg[k] and next_ring_cfg[k] ~= 0 then
            tb[k] = {}
            tb[k].value = v
            tb[k].value_add = next_ring_cfg[k]
        end
    end
    local i = 1
    for k, v in pairs(tb) do
        local info = self.marry_attr_list[i]
        if info and info.attr_txt then
            info.attr_txt.text.text = Language.Common.AttrName[k]
            info.value_txt.text.text = v.value / 100 .. "%"
            info.next_value.text.text = v.value_add / 100 .. "%"
            if info.img_add then
                info.img_add:SetActive(true)
            end
            i = i + 1
        end
    end

	self.node_list.total_attr_value.text.text = ring_cfg.boost / 100 .. "%"
	self.node_list.total_attr_add.text.text = next_ring_cfg.boost / 100 .. "%"
end

--激活后的情缘属性加成显示
function MarryView:FlushActiveAttr(equip_info)
    local ring_cfg = MarryWGData.Instance:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)
    local next_item_id = equip_info.item_id
    local next_lv = equip_info.param.star_level + 1
    if equip_info.param.star_level == 10 then
        next_item_id = equip_info.item_id + 1
        next_lv = 1
    end
    local next_ring_cfg = MarryWGData.Instance:GetEquipCfgById(next_item_id, next_lv)
    local tb = {}
    for k, v in pairs(ring_cfg) do
        if next_ring_cfg then
            if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k) and next_ring_cfg[k] then
                tb[k] = {}
                tb[k].value = v
                tb[k].value_add = next_ring_cfg[k] - v
            end
        else
            if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k) then
                tb[k] = {}
                tb[k].value = v
                tb[k].value_add = ""
            end
        end
    end
    local i = 1
    for k, v in pairs(tb) do
        local info = self.marry_attr_list[i]
        if info and info.attr_txt then
            info.attr_txt.text.text = Language.Common.AttrName[k]
            info.value_txt.text.text = v.value / 100 .. "%"
            info.next_value.text.text = v.value_add ~= "" and v.value_add / 100 .. "%" or ""
            if info.img_add then
                info.img_add:SetActive(v.value_add ~= "")
            end
            i = i + 1
        end
    end

	self.node_list.total_attr_value.text.text = ring_cfg.boost / 10000 .. "%"
	if next_ring_cfg and next_ring_cfg.boost then
		self.node_list.total_attr_add:CustomSetActive(true)
		self.node_list.total_attr_add.text.text = next_ring_cfg.boost / 10000 .. "%"
	else
		self.node_list.total_attr_add:CustomSetActive(false)
	end
end

--刷新当前选择情缘装备的属性
function MarryView:FlushRingAttr()
    self.node_list.btn_active_hunjie:SetActive(false)
    self.node_list.btn_quality_up_auto:SetActive(true)
	local data = MarryWGData.Instance
    local equip_info = data:GetRingInfo(self.cur_ring_index)
    local ring_cfg = data:GetEquipCfgById(equip_info.item_id, equip_info.param.star_level)

    local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
    local is_married = lover_id > 0

	-- XUI.SetGraphicGrey(self.node_list["title_bg"], not is_married)
    local item_cfg = {}
    item_cfg = ItemWGData.Instance:GetItemConfig(equip_info.item_id)
    local maxhp = item_cfg.hp or 0
	local fangyu = item_cfg.fangyu or 0
	local gongji = item_cfg.attack or 0
	local pojia = item_cfg.pojia or 0
	self.lock_item:SetData({item_id = equip_info.item_id})

	if IsEmptyTable(ring_cfg) then
		self.lbl_prog_exp.text.text= "0/0"
		self:SetHunJieProgressVal(0)						--修改后
		self.node_list["layout_attr"]:SetActive(true)
		self:SetHunJieMaxFlagState(false)
		self:SetHunJieIsCanUpLevel()
        self:SetCapability(0)
        self.node_list["lbl_gongji"].text.text = 0
        self.node_list["lbl_hp"].text.text = 0
        self.node_list["lbl_fangyu"].text.text = 0
        self.node_list["lbl_pojia"].text.text = 0
        self.node_list.btn_active_hunjie:SetActive(true)
        self.node_list.btn_quality_up_auto:SetActive(false)
        self.node_list["lbl_next_gongji"].text.text = gongji
        self.node_list["lbl_next_hp"].text.text = maxhp
        self.node_list["lbl_next_fangyu"].text.text = fangyu
        self.node_list["lbl_next_pojia"].text.text = pojia
        self:FlushNotActiveAttr()
		return
	end

	self.node_list["order_text"].text.text = string.format(Language.Common.Order, MarryWGData.Instance:GetTongXinSuoOrder())
    local nextring_cfg = data:GetEquipCfgByIndex(self.cur_ring_index, ring_cfg.level + 1)
    if equip_info.param.param1 then
        self.lbl_prog_exp.text.text = equip_info.param.param1 .. "/" .. ring_cfg.exp
    end
    self:FlushActiveAttr(equip_info)

	if nil ~= self.hunjie_level_cache and ring_cfg.level > 0 and (ring_cfg.level > self.hunjie_level_cache) then
		self:PlayHunJieAttrInfoUpEffect()
	end
	
	if ring_cfg.level > 0 then
		self.hunjie_level_cache = ring_cfg.level
	end

	self.node_list["layout_attr"]:SetActive(true)
	self.node_list["lbl_gongji"].text.text = math.floor(ring_cfg.gongji + gongji)
	self.node_list["lbl_hp"].text.text = math.floor(ring_cfg.maxhp + maxhp)
	self.node_list["lbl_fangyu"].text.text = math.floor(ring_cfg.fangyu + fangyu)
    self.node_list["lbl_pojia"].text.text = math.floor(ring_cfg.pojia + pojia)
	--婚戒属性
    if nextring_cfg ~= nil and not IsEmptyTable(nextring_cfg) then
		self.node_list["layout_next_hunjie_attr"]:SetActive(true)
		self:SetHunJieMaxFlagState(false)
		self.node_list["lbl_next_gongji"].text.text = nextring_cfg.gongji - ring_cfg.gongji
		self.node_list["lbl_next_hp"].text.text = nextring_cfg.maxhp - ring_cfg.maxhp
		self.node_list["lbl_next_fangyu"].text.text = nextring_cfg.fangyu - ring_cfg.fangyu
        self.node_list["lbl_next_pojia"].text.text = nextring_cfg.pojia - ring_cfg.pojia
	else
		self.node_list["lbl_next_gongji"].text.text = 0
		self.node_list["lbl_next_hp"].text.text = 0
		self.node_list["lbl_next_fangyu"].text.text = 0
		self.node_list["lbl_next_pojia"].text.text = 0
		self.node_list["layout_next_hunjie_attr"]:SetActive(false)
		self:SetHunJieMaxFlagState(true)
	end

    local cfg = AttributeMgr.GetAttributteByClass(ring_cfg)
    if is_married then
        if cfg and next(cfg) ~= nil then
            cfg.gong_ji = math.floor(cfg.gong_ji + gongji)
            cfg.max_hp = math.floor(cfg.max_hp + maxhp)
            cfg.fang_yu = math.floor(cfg.fang_yu + fangyu)
            cfg.po_jia = math.floor(cfg.po_jia + pojia)
            local power_value = math.floor(AttributeMgr.GetCapability(cfg))
            self:SetCapability(power_value)
        end
    else
        if cfg and next(cfg) ~= nil then
            for k, v in pairs(cfg) do
                if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k) then  --没结婚，百分比属性不生效
                    cfg[k] = 0
                end
            end
            local power_value = math.floor(AttributeMgr.GetCapability(cfg))
            self:SetCapability(power_value)
        end
    end
	self:SetHunJieIsCanUpLevel()
end

function MarryView:PlayHunJieAttrInfoUpEffect()
	for i = 1, 4 do
		if self.node_list["hunjie_effect" .. i] then
			local particls = self.node_list["hunjie_effect" .. i]:GetComponentsInChildren(typeof(UnityEngine.ParticleSystem))
				
			if particls.Length > 0 then
				for i = 0, particls.Length - 1 do
					local effect = particls[i]
	
					if effect then
						if effect.isPlaying then
							effect:Stop()
							effect:Clear()
						end
	
						effect:Play()
					end
				end
			end
		end
	end
end

function MarryView:GetUpQualityCfg()
	self.stuff_index = self.stuff_index or 1
	local up_quality_cfg = MarryWGData.Instance:GetQingyuanCfg().uplevel_stuff[self.stuff_index]
	return up_quality_cfg
end

-- 一键进阶请求
function MarryView:OnAutoUpStarHandler(state)
	if self:IsAutoUpEquip() == true or state then --取消自动进阶
		self:ClearAutoUplevel()
		self:SetHunJieBtnStr(Language.Marry.QualityUp)
		return
	end

	local equip_info = MarryWGData.Instance:GetRingInfo(self.cur_ring_index)
	if nil == equip_info or equip_info.item_id < 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoRingTips)
		-- return
	end
	self:TryPlayHunJieProgress()
end

--进阶请求
function MarryView:SendHunJieUpLevelEquip(stuff_id, count)
	MarryWGCtrl.Instance:SendUpLevelEquip(stuff_id, self.cur_ring_index, 1, self:IsAutoUpEquip(), count)
end

--尝试播放进度条
function MarryView:TryPlayHunJieProgress(state)
	local equip_info = MarryWGData.Instance:GetRingInfo(self.cur_ring_index)

	local is_max_level = MarryWGData.Instance:GetEquipIsMax(equip_info.item_id, equip_info.param.star_level)
	local limit_level, is_can_goon = MarryWGData.Instance:GetTongXinSuoLimintLevel()
	if is_max_level or not is_can_goon then
		self:IsPlayHunJieAni(false)
		self:IsAutoUpEquip(false)
		return --已满级
	end

	local up_quality_cfg = self:GetUpQualityCfg()
	local num = ItemWGData.Instance:GetItemNumInBagById(up_quality_cfg.stuff_id)
    if num <= 0 then
		if nil == state then
			-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryErrorTips1)
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = up_quality_cfg.stuff_id})
		end
		self:ClearAutoUplevel()
		self:SetHunJieBtnStr(Language.Marry.QualityUp)
		return
	end
	self:SetHunJieBtnStr(Language.Marry.Stop)

	self:IsAutoUpEquip(true)
	if self:IsPlayHunJieAni() then --正在播放动画
		return
	end
	-- MainuiWGCtrl.Instance:CreateCacheTable() --创建升级请求
	self:SendHunJieUpLevelEquip(up_quality_cfg.stuff_id) --发送请求升级
end

function MarryView:PlayHunJieAni(total_exp, image_is_max_level, level_add, slider_level, exp, add_exp_num)
	if self:IsPlayHunJieAni() then --如果正在播放动画，直接返回 避免重复刷新
		return
	end
	self:IsPlayHunJieAni(true)
	if add_exp_num and add_exp_num > 0 then
		TipWGCtrl.Instance:ShowNumberMsg(string.format(Language.Bag.GetRoleItemTxt2, add_exp_num), 0.1, VECTOR2_POS, nil, nil, nil, UiLayer.PopTop) 		--提升飘字
	end

	local tween_time = 0.3
	if image_is_max_level then --升到满级，播放进度条
		local tween_max = self.node_list["progress_bg"].slider:DOValue(1, tween_time)
		tween_max:SetId('hunjie_slider_tween')
		tween_max:OnComplete(function ()
			if self:IsPlayHunJieAni() then
				self:IsPlayHunJieAni(false)
				self:ClearAutoUplevel()
				self:UpdateHunJieNewAttr()
				self:PlayHunJieEffect()
			end
			MainuiWGCtrl.Instance:DelayShowCachePower(0)
		end)
		return
	end

	if level_add < 1 then
		--TODO
		self:UpdateHunJieNewAttr()
	end

	local add_value = level_add + exp / total_exp

	self.hunjie_fun = function (add_value)
		if self:IsPlayHunJieAni() == false then --强制切换到其他宝宝界面的处理
			--TODO --刷新属性
			print_error("TODO --刷新属性")
			return
		end
		local slider = self.node_list["progress_bg"].slider
		if add_value <= 0 then --结束，如果还在升级状态，继续请求升级
			self:IsPlayHunJieAni(false)
			if self:IsAutoUpEquip() then
				self:TryPlayHunJieProgress(true) --继续升级
			end
			return
		end

		local tween = nil
		if add_value > 1 then
			tween = slider:DOValue(1, tonumber(string.format("%.2f", (1 - slider.value) * tween_time)))
		else
			tween = slider:DOValue(add_value, tonumber(string.format("%.2f", (add_value - slider.value) * tween_time)))
		end
		tween:SetId('hunjie_slider_tween')
		add_value = add_value - 1
		tween:OnComplete(function ()
			if self:IsPlayHunJieAni() == false then
				return
			end

			if add_value >= 0 then
				slider.value = 0
				-- slider_level = slider_level + 1
				self:UpdateHunJieNewAttr()
				self:PlayHunJieEffect()
			end
			if add_value < 1 then --要等特效播放完成后才能调用
				MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
			end
			self.hunjie_fun(add_value)
		end)
	end
	self.hunjie_fun(add_value)
end

--切换时停止动画，请求升级
function MarryView:ChangeHunJieIndex()
	if self:IsPlayHunJieAni() then
		self:IsPlayHunJieAni(false)
		DG.Tweening.DOTween.Kill('hunjie_slider_tween')
		MainuiWGCtrl.Instance:DelayShowCachePower(0)
		self:ClearAutoUplevel()
	end
end

function MarryView:PlayHunJieEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jinjie, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["effect_cell"]})
	--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_Effect_hunjie_bao_aixn_01)
	--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["Effect_hunjie"].transform)
	AudioService.Instance:PlayAdvancedAudio()
end

--点击帮助按钮
function MarryView:OpenHunJieTips()
	MarryView.OpenTips(Language.Marry.HunJieTips, Language.Marry.HunJieTipsTitle)
end

--------------------------------------------------
--#region
--能否激活新戒子判断
function MarryView:IsCanBuy(index)
	local data = MarryWGData.Instance
	local up_level = data:QianYuanEquipmentLevel(index)
	local content = Language.Marry.EquipmentTip
	local equipment_level = data:GetQingYuannEquipmentAllLevel(index)

	if index > 0 then
	 	if not data:CheckHasEquipBySlot(index) then
			if up_level ~= nil and equipment_level ~= nil and equipment_level < up_level then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(content,up_level / 10 + index * 1))
				return false
			end
		else
			return false
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.LockRing)
		return false
	end
	return  true
end

--清空自动升级
function MarryView:ClearAutoUplevel()
	self:IsAutoUpEquip(false)
end

function MarryView:IsAutoUpEquip(state)
	if nil == state then
		return self.is_auto_up
	end
	self.is_auto_up = state
end

--提升暴击特效
function MarryView:ShowLevelUpEffect(effect_id)

end

function MarryView:AttrFloatingText(add)

end

function MarryView:IsPlayHunJieAni( state )
	if nil == state then
		return self.is_play_hunjie_progress_ani
	end
	self.is_play_hunjie_progress_ani = state
end

--红点显示隐藏
function MarryView:SetHunJieRemindState( enable )
	self.node_list.remind:SetActive(enable)
end

--是否为最大级
function MarryView:SetHunJieMaxFlagState( enable )
    --self.node_list["ph_quality_need"]:SetActive(not enable)
    self.node_list["btn_quality_up_auto"]:SetActive(not enable)
	self.node_list["img_maxlevel_flag"]:SetActive(enable)
	--self.node_list["btn_parent"]:SetActive(not enable)
	if enable then
		self.lbl_prog_exp.text.text = "max"
	end
end

--是否可提升
function MarryView:SetHunJieIsCanUpLevel()
	local limit_level, enable = MarryWGData.Instance:GetTongXinSuoLimintLevel()

	self.node_list["uplevel_show"]:SetActive(enable)
	self.node_list["uplevel_hint"]:SetActive( not enable )

	if not enable then
		self.node_list["uplevel_hint"].text.text = string.format(Language.Marry.TongXinSuoLimint, limit_level)
	end
end

function MarryView:SetHunJieProgressVal(num)
	self.node_list["progress_bg"].slider.value = num
end

--战斗力
function MarryView:SetCapability(num)
	if nil == self.node_list["hunjiezhandouli"] then return end
	self.node_list["hunjiezhandouli"].text.text = num
end

function MarryView:ShowLevelHeartUpEffect(heart)

end

function MarryView:SetHunJieHeartGray(heart,is_grey)
	-- XUI.SetGraphicGrey(heart,is_grey)
	heart.image:LoadSprite(ResPath.GetMarryResPath(is_grey))
end

function MarryView:SetHunJieBtnStr( str )
	if self.hunjie_btn_str == str then
		return
	end
	self.hunjie_btn_str = str
	self.node_list["btn_quality_up_auto_text"].text.text = str
end

--婚戒被动技能部分
function MarryView:CreatHunJieBeSkill()
	local data_list = MarryWGData.Instance:GetTongXinSuoSkillCfg()
	if nil == self.hunjie_list then
		self.hunjie_list = AsyncListView.New(HunJieBeSkillItem,self.node_list["skill_list"])
		-- self.hunjie_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSkillItemHandler, self))
	end
	self.hunjie_list:SetDataList(data_list,0)
end

--#endregion
----------------------------------------------------------

HunJieBeSkillItem = HunJieBeSkillItem  or BaseClass(BaseRender)

function HunJieBeSkillItem:__init()

end

function HunJieBeSkillItem:__delete()
	self.is_jihuo = nil
end


function HunJieBeSkillItem:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_skill"], BindTool.Bind(self.OnClick, self))
end

function HunJieBeSkillItem:OnFlush()
	if self.data and self.data.data then
		self.node_list["SkillIcon"].image:LoadSprite(ResPath.GetSkillIconById(self.data.data.skill_image))
		--XUI.SetGraphicGrey(self.node_list["SkillIcon"], self.data.is_jh == 0)
		local skill_level = self.data.is_jh == 0 and 0 or self.data.data.level
		self.node_list.skll_level.text.text = self.data.data.limit_text
		self:SkillActiveShow(self.data.is_jh)
		-- self.node_list["text_bg"]:SetActive(self.data.is_jh == 0)
		self.node_list.mask_bg:SetActive(self.data.is_jh == 0)
		self.node_list.lock:SetActive(self.data.is_jh == 0)

		self.node_list.skll_attr.text.text = DeleteStrSpace(self.data.data.skill_desc)
	end
end

--技能激活表现
function HunJieBeSkillItem:SkillActiveShow(is_jh)
	if not self.is_jihuo then
		self.is_jihuo = is_jh
		return
	end
	if self.is_jihuo ~= is_jh and is_jh == 1 then
		self.is_jihuo = is_jh
		local skill_cfg = {}
		local cur_skill_cfg = MarryWGData.Instance:GetTongXinSuoNextSkillCfg(self.data.data.skill_seq,self.data.data.level)
		skill_cfg["desc"] = cur_skill_cfg.skill_desc or ""
		skill_cfg["icon"] = cur_skill_cfg.skill_image or 0
		skill_cfg["name"] = self.data.data.skill_name or ""

		TipWGCtrl.Instance:ShowGetNewSkillView2(skill_cfg)
	end
end

function HunJieBeSkillItem:OnClick()
	local cfg = self.data.data
	local skill_level = self.data.is_jh == 0 and 0 or cfg.level
	local cur_skill_cfg = MarryWGData.Instance:GetTongXinSuoNextSkillCfg(cfg.skill_seq,skill_level)
	local next_cfg = MarryWGData.Instance:GetTongXinSuoNextSkillCfg(cfg.skill_seq,cfg.level + 1)
	local str = ""

	if self.data.is_jh == 0 then
		str = cfg.limit_text
	elseif next_cfg and cur_skill_cfg then
		str = next_cfg.limit_text
	else
		str = Language.Marry.SkillDesc1
	end
	local bady_text_str = cfg.skill_desc
	local hunjie = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_HUNJIE)

	--默认橙色
	local skill_color = 4
	if hunjie then
		local cfg = ItemWGData.Instance:GetItemConfig(hunjie.item_id)
		skill_color = cfg and cfg.color or 4
	end

	local capability = 0
	local skill_attribute = MarryWGData.Instance:GetBabySkillAddAttr(cfg.param1, cfg.param2)

	if skill_attribute ~= nil then
		capability = AttributeMgr.GetCapability(skill_attribute)
	end

	local show_data = {
		icon = cfg.skill_image,
		top_text = cfg.skill_name,
		body_text = bady_text_str,
		capability = capability,
		hide_level = true,
		is_lock = self.data.is_jh < 1,
		limit_text = str,
		x = 0,
		y = -120,
		set_pos = true,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
	-- local show_data = {
	-- 	is_jh = self.data.is_jh == 1,
	-- 	limit_text = str,
	-- 	level = skill_level,
	-- 	skill_desc = bady_text_str,
	-- 	skill_image = cfg.skill_image,
	-- 	skill_name = cfg.skill_name,
	-- 	skill_type = cfg.type,
	-- 	next_info = next_cfg,
	-- 	skill_color = skill_color,
	-- 	capability = capability,
	-- }

	-- MarryWGCtrl.Instance:OpenSkillTip(show_data)
end

SelectBaseCell = SelectBaseCell or BaseClass(ItemCell)
function SelectBaseCell:OnClick()
	if self:IsSelect() then
		ItemCell.OnClick(self)
		return
	end
	if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

function SelectBaseCell:SetRightBottomText(text, color)
	ItemCell.SetRightBottomText(self, text)
end
