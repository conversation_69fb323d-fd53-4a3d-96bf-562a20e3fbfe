----------------------------------------------------
-- 随机活动-特惠秒杀 -下期预览
----------------------------------------------------
NextTeHuiShopView = NextTeHuiShopView or BaseClass(SafeBaseView)

function NextTeHuiShopView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/", "layout_next_show")
end

function NextTeHuiShopView:__delete()
	
end

function NextTeHuiShopView:ReleaseCallBack()
    if self.act_item_list ~= nil then 
		self.act_item_list:DeleteMe()
		self.act_item_list = nil
	end
end

function NextTeHuiShopView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
	self.node_list.title_view_name.text.text = Language.SecondTitle.TehuiShop
	self:CreateListView()
end

function NextTeHuiShopView:ShowIndexCallBack()
    self:Flush()
end

function NextTeHuiShopView:CreateListView()
	local bundle = "uis/view/tehui_shop_prefab"
	local asset = "ph_next_tehui_render"
	self.act_item_list = AsyncBaseGrid.New()
	self.act_item_list:SetStartZeroIndex(false)
	self.act_item_list:CreateCells({
		col = 2,
		change_cells_num = 1,
		assetName = asset,
		assetBundle = bundle,
		itemRender = NextTeHuiShopItemRender, 
		list_view = self.node_list.ph_next_list
	})
end

function NextTeHuiShopView:OnFlush()
    local data_list = TehuiShopWGData.Instance:GetNextTeHuiShopCfg()
    self.act_item_list:SetDataList(data_list,3)
end

function NextTeHuiShopView:NextUpdateCallBack(elapse_time, total_time)
	local left_time = total_time - elapse_time
	local time_str = TimeUtil.Format2TableDHMS(left_time)
	local str = string.format(Language.Common.TimeStr3, time_str.hour, time_str.min, time_str.s)
	self.node_list.lbl_next_time.text.text = (Language.Common.NextTime..str)
end

function NextTeHuiShopView:NextCompleteCallBack()
	self:Close()
end

-- ItemRender
NextTeHuiShopItemRender = NextTeHuiShopItemRender or BaseClass(BaseRender)
function NextTeHuiShopItemRender:__init()
	self:AddClickEventListener()
end

function NextTeHuiShopItemRender:__delete()
    if self.cell then 
        self.cell:DeleteMe()
        self.cell = nil
    end
end

function NextTeHuiShopItemRender:LoadCallBack()
    self.cell = ItemCell.New(self.node_list.ph_cell_1)
end

function NextTeHuiShopItemRender:OnFlush()
	if self.data == nil then return end
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    if item_cfg == nil then
    	print_error("空配置 item_id为", self.data.reward_item.item_id)
    	return
    end
    self.node_list.lbl_item_name.text.text = (item_cfg.name)
    self.node_list.lbl_item_cost.text.text = (self.data.need_gold)
	self.cell:SetData(self.data.reward_item)

	if self.data.total_count > 0 then
		self.node_list.rich_item_left.text.text = string.format(Language.OpenServer.HotSellNum, self.data.total_count)
	else
		self.node_list.rich_item_left.text.text = ""
	end
	local bought_tiems = TehuiShopWGData.Instance:GetHaveBoughtTimes()
	if bought_tiems and self.data.total_count > 0 then  -- 元宝购买不显示限制购买次数
		local str = self.data.limit_buy .. "/" .. self.data.limit_buy
		self.cell:SetRightBottomColorText(str)
    	self.cell:SetRightBottomTextVisible(true)
	end
	
	self.node_list.lbl_acount_num.text.text = (string.format(Language.OpenServer.EveryDayShopZhe, self.data.discount))
end

function NextTeHuiShopItemRender:CreateSelectEffect()
end

function NextTeHuiShopItemRender:OnClick()
	BaseRender.OnClick(self)
	TipWGCtrl.Instance:OpenItem(self.data.reward_item)
end
