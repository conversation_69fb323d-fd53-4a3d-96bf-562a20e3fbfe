XinFuTeMaiWGData = XinFuTeMaiWGData or BaseClass()

function XinFuTeMaiWGData:__init()
    if XinFuTeMaiWGData.Instance then
        error("[XinFuTeMaiWGData] Attempt to create singleton twice!")
        return
    end
    XinFuTeMaiWGData.Instance = self

    RemindManager.Instance:Register(RemindName.XinFuTeMai, BindTool.Bind(self.CheckRemind, self))
end

function XinFuTeMaiWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.XinFuTeMai)
    if  XinFuTeMaiWGData.Instance ~= nil then
        XinFuTeMaiWGData.Instance = nil
    end
end

function XinFuTeMaiWGData:SetCurOfflineTime(time)
    self.offline_time = time
end

function XinFuTeMaiWGData:GetCurOfflineTime()
    return self.offline_time
end

function XinFuTeMaiWGData:GetCfg()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    --local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("newshop_auto")
    local rand_t = cfg.newshop
    local cur_cfg = nil
    for i, v in pairs(rand_t) do
    	if v.opengame_day == open_day then
    		cur_cfg = v
    		break
    	end
    end
    --如果找不到， 取第一个
    if not cur_cfg then
        cur_cfg = rand_t[1]
    end
    return cur_cfg
end

function XinFuTeMaiWGData:SetNewShopInfo(protocol)
    if not self.temai_info then
        self.temai_info = {}
    end
    self.temai_info.reward_active_flag = protocol.reward_active_flag
    self.temai_info.reward_fetch_flag = protocol.reward_fetch_flag
    self.temai_info.is_open_window = protocol.is_open_window
    self.temai_info.end_time_stamp = protocol.end_time_stamp
end

function XinFuTeMaiWGData:GetTeMaiInfo()
    return self.temai_info
end

function XinFuTeMaiWGData:GetTeMaiIsShouldOpenWindow()
    return self.temai_info.is_open_window
end

function XinFuTeMaiWGData:CheckRemind()
    if not self.temai_info then
        return 0
    end
    if self.temai_info and self.temai_info.reward_active_flag == 1 and self.temai_info.reward_fetch_flag == 0  then
        return 1
    end
    return 0
end