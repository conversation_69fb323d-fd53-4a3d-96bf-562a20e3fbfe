require("game/serveractivity/act_wishing/act_wishing_wg_data")
require("game/serveractivity/act_wishing/act_wishing_view")

ActwishingWGCtrl = ActwishingWGCtrl or BaseClass(BaseWGCtrl)

function ActwishingWGCtrl:__init()

	ActwishingWGCtrl.Instance = self
	self.data = ActwishingWGData.New()
	self.view = ActwishingView.New(GuideModuleName.WishingPool)
	self:RegisterAllProtocols()
end

function ActwishingWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	ActwishingWGCtrl.Instance = nil

end
function ActwishingWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRAWishPoolInfo,"OnRAWishPoolInfo")
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.OpenActivity, self)) --面板监听
end
-- 打开主窗口
function ActwishingWGCtrl:Open()
	self.view:Open()
end
-- 刷新面板
function ActwishingWGCtrl:OnRAWishPoolInfo(protocol)
	self.data:UpdataInfoData(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function ActwishingWGCtrl:OpenActivity()
	local cfg_open = ActwishingWGData.Instance:GetWishPoolPeizhiData()
	if ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL == cfg_open.activity_type then
		if TimeWGCtrl.Instance:GetCurOpenServerDay() == cfg_open.begin_day_idx  then
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.OPEN)
		elseif TimeWGCtrl.Instance:GetCurOpenServerDay() >= cfg_open.end_day_idx then
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.CLOSE)
		end
	end
end
function ActwishingWGCtrl:SendAllInfoReq()
	-- if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL) then
	-- 	return
	-- end
	-- local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].wishpool_item_id)
	-- local param_t = {}
	-- if item_num > 0 then
	-- 	param_t = {
	-- 		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
	-- 		opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_ITEM
	-- 	}
	-- else
	-- 	param_t = {
	-- 		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
	-- 		opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_GOLD
	-- 	}
	-- end
	-- ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL) then
		return
	end
	local is_finish = ActwishingWGData.Instance:GetIsFinish()
	if is_finish == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.XuYuan.HasGetReword)
		return
	end
	ActTreasureWGData.Instance:SetAutoUseGiftFlag(true)
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].wishpool_item_id)
	local param_t = {}
	if item_num <= 0 then
		ShopWGCtrl.Instance:SendShopBuy(other_cfg[1].wishpool_gift, 1, 1, 0)
	end
	param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
			opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_ITEM
		}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end
function ActwishingWGCtrl:SendWishInfoReq()
	-- if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL) then
	-- 	return
	-- end
	-- local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].wishpool_item_id)  --查询背包里物品ID
	-- local param_t = {}
	-- if item_num > 0 then
	-- 	param_t = {
	-- 		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
	-- 		opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_ITEM
	-- 	}
	-- else
	-- 	param_t = {
	-- 		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
	-- 		opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_GOLD
	-- 	}
	-- end
	-- ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)  --计时器
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL) then
		return
	end
	local is_finish = ActwishingWGData.Instance:GetIsFinish()
	if is_finish == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.XuYuan.HasGetReword)
		return
	end
	ActTreasureWGData.Instance:SetAutoUseGiftFlag(true)
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	local wish_pool_process = ActwishingWGData.Instance:GetWishPoolProcess() or 100000
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].wishpool_item_id)  --查询背包里物品ID
	local param_t = {}
	if item_num <= 0 and wish_pool_process < other_cfg[1].wishpool_process_max then
		ShopWGCtrl.Instance:SendShopBuy(other_cfg[1].wishpool_gift, 1, 1, 0)
	end
	param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
			opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_ITEM
		}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)  --计时器
end
