BagQuickUseView = BagQuickUseView or BaseClass(SafeBaseView)

function BagQuickUseView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self.is_modal = true
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -26), sizeDelta = Vector2(814, 522)})
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "bag_quick_use")
	self.itemdata_change_callback = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function BagQuickUseView:__delete()
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
end

function BagQuickUseView:ReleaseCallBack()
	if nil ~= self.quick_use_list then
		self.quick_use_list:DeleteMe()
		self.quick_use_list = nil
	end
end

function BagQuickUseView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Bag.QuickUse
	XUI.AddClickEventListener(self.node_list.use_all_btn,BindTool.Bind(self.OnClickAllUseHandler,self))
	self.quick_use_list = AsyncListView.New(QuickUseRender,self.node_list.ListView)
end

function BagQuickUseView:ShowIndexCallBack()
	self:Flush()
end

function BagQuickUseView:OpenCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.itemdata_change_callback)
end

function BagQuickUseView:CloseCallBack()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemdata_change_callback)
	BagWGCtrl.Instance:SendKnapsackStoragePutInOrder(GameEnum.STORAGER_TYPE_BAG, 0)
end

function BagQuickUseView:OnFlush()
	local bag_data_list = ItemWGData.Instance:GetBagItemDataList()
	local data = {}
	local item_cfg = nil
	--离线挂机卡特殊拦截(id写死)
	local remain_offline_rest_time = OfflineRestWGData.Instance.remain_offline_rest_time
	local hour = math.floor(remain_offline_rest_time / 3600)
	for k,v in pairs(bag_data_list) do
		item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if nil ~= item_cfg and 1 == item_cfg.choose_use 
			and item_cfg.limit_level <= RoleWGData.Instance.role_vo.level 
			and ItemWGData.Instance:GetIsCanUseItem(v.item_id, item_cfg.use_daytimes) then
			if v.item_id == SPECIAL_GUAJICARD.IDONE or v.item_id == SPECIAL_GUAJICARD.IDTWO then
				if hour < 20 then
					table.insert(data, v)
				end
			else
				table.insert(data, v)
			end
		end
	end
	self.quick_use_list:SetDataList(data,3)
end

function BagQuickUseView:ItemDataChangeCallback()
	if ItemWGData.Instance:GetIsHasQuickUseItem() then
		self:Flush()
	else
		self:Close()
	end
end

function BagQuickUseView:OnClickAllUseHandler()
	local bag_data_list = self.quick_use_list:GetDataList()
	for k,v in pairs(bag_data_list) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if nil ~= item_cfg and 1 == item_cfg.choose_use then
			BagWGCtrl.Instance:SendUseItem(v.index, v.num, nil, item_cfg.need_gold)
		end
	end
	self:Close()
end


-------------------------------------------------
-------------------------快速使用Render------------------------
-------------------------------------------------
QuickUseRender = QuickUseRender or BaseClass(BaseRender)

function QuickUseRender:__init()

end

function QuickUseRender:__delete()
	if nil ~= self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function QuickUseRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.use_btn,BindTool.Bind(self.OnClickUseHandler,self))
	self.cell = ItemCell.New()
	self.cell:SetInstanceParent(self.node_list.Item)

end

function QuickUseRender:OnFlush()
	self.cell:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local name = ItemWGData.Instance:GetItemConstName(item_cfg, self.data.num)
	self.node_list.PropName.text.text = ToColorStr(name, ITEM_COLOR[item_cfg.color])
end

function QuickUseRender:OnClickUseHandler()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	BagWGCtrl.Instance:SendUseItem(self.data.index, self.data.num, nil, item_cfg.need_gold)
end

function QuickUseRender:CreateSelectEffect()
end