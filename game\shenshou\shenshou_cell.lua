ShenShouEquipCell = ShenShouEquipCell or BaseClass(ItemCell)

local flush_type =
{
	refresh = 1,
	refresh_all_act_cells = 2,
	update_one_cell = 3,
	update_select_state = 4,
}

function ShenShouEquipCell:__init()
	self:SetButtonComp(true)
	self.is_need = true
end

function ShenShouEquipCell:AddClickEventListener( callback )
	 self.click_callback = callback
	 local fun = function()
	 	self:OnClick()
	 end
	 self.button.button:AddClickListener(fun)
end

function ShenShouEquipCell:__delete()
	self.is_need = true
end

function ShenShouEquipCell:OnFlush()
	if nil == self.data then
		return
	end

	self:ClearAllParts()
	self:SetButtonComp(true)
	self:SetSelectEffect(self.is_select_effect)

	if nil == self.data then
		self.data_from_index = -1
		self.is_lock = false
		return
	end

	local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(self.data.item_id)	-- 获取神兽装备配置
	self.shenshou_equip_cfg = shenshou_equip_cfg
	if nil == shenshou_equip_cfg then
		return
	end

	local bundle_name, asset = ResPath.GetItem(shenshou_equip_cfg.icon_id)
	self:SetItemIcon(bundle_name, asset)

	local color = shenshou_equip_cfg.quality
	if color then
		self:SetQualityIcon("uis/images/common_atlas", "a3_ty_wpk_" .. color)
	end
	self:SetQualityIconVisible(true)

	local star_count = 0
	if self.data.attr_list then
		for k,v in pairs(self.data.attr_list) do
			if v.attr_type > 0 then
				local random_cfg = ShenShouWGData.Instance:GetRandomAttrCfg(shenshou_equip_cfg.quality, v.attr_type) or {}
				if random_cfg.is_star_attr ==1 then
					star_count = star_count + 1
				end
			end
		end
	elseif self.data.param then
		star_count = self.data.param.star_level
	else
		star_count = self.data.star_count or 0
	end

	if shenshou_equip_cfg.is_equip == 1 then
		self:SetLeftTopImg(star_count)

		if shenshou_equip_cfg.soul_ring_sign and shenshou_equip_cfg.soul_ring_sign ~= "" and shenshou_equip_cfg.desc_soul_ring_sign and shenshou_equip_cfg.desc_soul_ring_sign ~= "" then
			self:SetSoulRingSign(shenshou_equip_cfg.soul_ring_sign, shenshou_equip_cfg.desc_soul_ring_sign)
		end
	end

	if self.item_tip_from == ShenShouEquipTip.FROM_EQUIMENT_HECHENG then
		local is_up = false
		if self.data and self.data.param then -- 具体装备
			is_up =	ShenShouWGData.Instance:GetShenShouEquipHaveUpFlag(self.data)
		else--合成目标神兽装备
			is_up = ShenShouWGData.Instance:GetComposeProductUpFlag(self.data)
		end
		
		if is_up then
			self:SetUpFlagIconVisible(is_up,true)
		else
			self:SetUpFlagIconVisible(is_up)
		end
	elseif self.item_tip_from == ShenShouEquipTip.FROM_SHENSHOUBAG and shenshou_equip_cfg.is_equip == 1 and self.is_need then
		-- local cur_shou_id = ShenShouWGCtrl.Instance.shenshou_equip_bag_view.shou_id
		-- local flag = ShenShouWGData.Instance:GetIsBetterShenShouEquip(self.data, cur_shou_id)	----比较是否有较好的升级
		local flag = ShenShouWGData.Instance:GetShenShouEquipHaveUpFlag(self.data)

		if flag == nil then
			self:SetUpFlagIconVisible(false)
		else
			self:SetUpFlagIconVisible(flag, true)
		end
	else
		self:SetUpFlagIconVisible(false)
	end

	self:SetBindIconVisible(0 ~= self.data.is_bind)
	-- self:SetRightTopImageTextActive(self.data.strength_level and self.data.strength_level > 0)
	-- if self.data.strength_level then
	-- 	self:Nodes("right_top_imgnum_txt").text.text = "+" .. self.data.strength_level  -- left_bottom_text
	-- end

	self:SetLeftBottomColorTextVisible(self.data.strength_level and self.data.strength_level > 0)
	if self.data.strength_level then
		self:Nodes("left_bottom_text").text.text = "+" .. self.data.strength_level
	end

	if self.need_default_eff then
		self:SetDefaultEff(true)
	end
end

function ShenShouEquipCell:SetFlagIcon(need)
	self.is_need = need
end

-- 点击格子
function ShenShouEquipCell:OnClick()
	-- if not self.data then return end
	if self.data and self.data.item_id == nil then
		self:SetSelect(false)
	end

	if self.is_lock ~= true then
		-- self:SetNewFlagIconVisible(false)
		if self.is_showtip and self.data and self.data.item_id ~= nil and self.data.item_id > 0 then
			if self.shenshou_equip_cfg.is_equip == 1 then
				ShenShouWGCtrl.Instance:OpenShenShouEquipTip(self.data, self.item_tip_from)
			else
				ShenShouWGCtrl.Instance:OpenShenShouStuffTip(self.data, self.item_tip_from)
			end
		end
		BaseRender.OnClick(self)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(self.lock_reason_tip)
	end
end

function ShenShouEquipCell:OnSelectChange(is_select)
    self:SetSelectEffectSp(is_select)
end

function ShenShouEquipCell:SetRightTopImageText(active)
	self:SetRightTopImageTextActive(active)
end

function ShenShouEquipCell:SetNoInteractable()
	self.button:SetActive(false)
	self.view.image.raycastTarget = false
end

function ShenShouEquipCell:SetUpFlagIconVisible(is_visible, is_equip)
	if is_equip then
		local bundle,asset
		if is_visible then
			self:Nodes("upflag_icon"):SetActive(true)
			bundle,asset = ResPath.GetCommonImages("a3_ty_up_1")
			self:Nodes("upflag_icon").image:LoadSprite(bundle,asset)
		--else
			--bundle,asset = ResPath.GetF2CommonImages("arrow_down_itemcell")
		end

	else
		self:Nodes("upflag_icon"):SetActive(is_visible)
	end
end


--------------------------------------------------------------------------------

ShenShouGrid = ShenShouGrid or BaseClass(AsyncBaseGrid)

local ChooseAll = {
	ExceptQuality = GameEnum.EQUIP_COLOR_RED,
	ExceptStar = 0,
}

function ShenShouGrid:__init()

end

--不可选中
function ShenShouGrid:SetNoSelectState( state )
	self.no_select = state
end

function ShenShouGrid:GetNoSelectState()
	return self.no_select or false
end

function ShenShouGrid:SetAddSelectCellIndex(cell_index)
	self.select_tab[1][cell_index] = true
	self:__Flush(flush_type.update_select_state)
end

-- 选择某个格子回调
function ShenShouGrid:SelectCellHandler(cell)
	self.cur_index = cell:GetIndex()
    local cell_index = self.cur_index
    if self.is_multi_select then
        if IsEmptyTable(cell:GetData()) or cell:GetData().item_id == 0 then
            return
        end
		if not self.select_tab[1][cell_index] then
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
			end

			self.select_tab[1][cell_index] = true
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil

		else
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
			end
			self.select_tab[1][cell_index] = true
		end
	else
		for k, v in pairs(self.select_tab[1]) do
			if cell_index == k then
				return
			end
		end

		self.select_tab[1] = {}
		self.select_tab[1][cell_index] = true
	end

	if nil ~= self.select_callback then
		self.select_callback(cell)
	end

	-- self.list_view.scroller:RefreshActiveCellViews()
	self:RefreshSelectCellState()
end

--让所有格子高亮
function ShenShouGrid:GetAllCellSelect()
	-- local all_cell = {}
	self.select_tab[1] = {}
	for i=1,self.has_data_max_index do
		self.select_tab[1][i] = true
	end
	-- self.list_view.scroller:RefreshActiveCellViews()
	self:RefreshSelectCellState()
end

--所有格子取消高亮
function ShenShouGrid:CancleAllSelectCell()
	self.select_tab[1] = {}
	for i=1,self.has_data_max_index do
		self.select_tab[1][i] = false
	end
	-- self.list_view.scroller:RefreshActiveCellViews()
	self:RefreshSelectCellState()
end

function ShenShouGrid:GetAllCellExceptCondition(select_quality)
	self.cur_multi_select_num = 0
	self.select_tab[1] = {}
	-- local select_quality = RoleWGData.GetRolePlayerPrefsInt("shenshou_equip_qianghua_index")
	for i=1,self.has_data_max_index do
		if self.cell_data_list[i].item_id > 0 then
			if (self.cur_multi_select_num < ESOTERICA_DEFINE.MAX_RESLOVE) then
				if self.cell_data_list[i].is_equip ~= 0 then--装备类型
					if self.cell_data_list[i].item_color <= select_quality then -- ChooseAll.ExceptQuality then
						if select_quality >= 5 then
							if self.cell_data_list[i].star_count <= 2 then
								self.select_tab[1][i] = true
								self.cur_multi_select_num = self.cur_multi_select_num + 1
							end
						else
							self.select_tab[1][i] = true
							self.cur_multi_select_num = self.cur_multi_select_num + 1
						end
					elseif self.cell_data_list[i].star_count < ChooseAll.ExceptStar and
					self.cell_data_list[i].quality == ChooseAll.ExceptQuality then
						self.select_tab[1][i] = true
						self.cur_multi_select_num = self.cur_multi_select_num + 1
					end
				else--兽血水晶类型
					if not IsEmptyTable(self.select_stone_tab) and self.select_stone_tab[i] then
						self.select_tab[1][i] = self.select_stone_tab[i]
						if self.select_tab[1][i] then
							self.cur_multi_select_num = self.cur_multi_select_num + 1
						end
					end
				end
			end
		end
	end
	self:RefreshSelectCellState()
	-- self.list_view.scroller:RefreshActiveCellViews()
end

function ShenShouGrid:SetAllStoneCellSelectState(is_select)
	self.select_stone_tab = {}
	local temp_data = {}
	for i=1,self.has_data_max_index do
		if self.cell_data_list[i].item_id > 0 then
			if self.cell_data_list[i].is_equip == 0 then
				self.select_stone_tab[i] = is_select or false
				temp_data[i] = is_select or false
			else
				temp_data[i] = self.select_tab[1][i] or false
			end
		end
	end
	self.select_tab[1] = temp_data
	self:RefreshSelectCellState()
end

function ShenShouGrid:__delete()
	self.no_select = nil
end

function ShenShouGrid:SetMultiSelectEffect(num)
	self.select_tab[1] = {}
	for i=1,num do
		self.select_tab[1][i] = true
	end
	self:RefreshSelectCellState()
	-- self.list_view.scroller:RefreshActiveCellViews()
end

--获取所有高亮格子
function ShenShouGrid:GetAllSelectCell()
	local all_cell = {}
	for k,v in pairs(self.select_tab[1]) do
		if v then
			if self.start_zero then
				table.insert(all_cell,self.cell_data_list[k - 1])
			else
				table.insert(all_cell,self.cell_data_list[k])
			end
		end
	end
	return all_cell
end

-- 天神分解背包
-- pinjie_num 0为全部
function ShenShouGrid:SetTianShenEquipMeltSelect(pinzhi_color,pinjie_num)
	self.select_tab[1] = {}
	local data, item_cfg = nil, nil
	for i=1,self.has_data_max_index do
		data = self.cell_data_list[i - 1]
		if data and not IsEmptyTable(data) then
			item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			if item_cfg.color <= pinzhi_color and (0 == pinjie_num or data.grade_level <= pinjie_num) then
				self.select_tab[1][i] = true
			elseif data.knapsack_type ~= KNAPSACK_TYPE.TIANSHEN then -- 材料
				self.select_tab[1][i] = true
			end
		end
	end
	self:RefreshSelectCellState()
	-- self.list_view.scroller:RefreshActiveCellViews()
end

-- 仙盟仓库背包
-- pinjie_num 0为全部
function ShenShouGrid:SetGuildCangkuEquipMeltSelect(STAR_LEVEL)
    STAR_LEVEL = STAR_LEVEL or 1 	--星级
	self.select_tab[1] = {}
	local data, item_cfg = nil, nil
	for i = 1, self.has_data_max_index do
		data = self.cell_data_list[i]
		if data and not IsEmptyTable(data) then
            local legend_num = data.param and data.param.star_level or 0
            if legend_num <= STAR_LEVEL then
                self.select_tab[1][i] = true
            end
		end
    end
    self:RefreshSelectCellState()
	-- self.list_view.scroller:RefreshActiveCellViews()
end

function ShenShouGrid:SetFLEQEvolvePopSelect()
	self.select_tab[1] = {}
	local data, item_cfg = nil, nil
	for i = 1, self.has_data_max_index do
		data = self.cell_data_list[i]
		if data and not IsEmptyTable(data) then
			local index = data.index or -1
			local is_select = FairyLandEquipmentWGData.Instance:EvolvePopEquipIsSelect(index)
            if is_select then
                self.select_tab[1][i] = true
            end
		end
    end
    self:RefreshSelectCellState()
end

function ShenShouGrid:SetHolyDarkSkillPopSelect(color)
	self.select_tab[1] = {}
	local data, item_cfg = nil, nil
	local select_num = 0
	for i = 1, self.has_data_max_index do
		data = self.cell_data_list[i]
		if data and not IsEmptyTable(data) then
			local index = data.index or -1
			local is_select = HolyDarkWeaponWGData.Instance:SkillPopEquipIsSelect(index)
			local is_color_select = false
			if select_num < HolyDarkWeaponWGData.DecompsEquip then
				if data.color and data.color <= color then
					is_color_select = true
				end

				is_select = is_select or is_color_select
				if is_select then
					self.select_tab[1][i] = true
					select_num = select_num + 1
				end
			end
		end
    end
    self:RefreshSelectCellState()
end