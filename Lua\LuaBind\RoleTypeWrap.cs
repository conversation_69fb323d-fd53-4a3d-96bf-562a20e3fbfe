﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class RoleTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>(typeof(RoleType));
		<PERSON><PERSON>("Man", get_Man, null);
		<PERSON><PERSON>("Woman", get_Woman, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON>.<PERSON>();
		TypeTraits<RoleType>.Check = CheckType;
		StackTraits<RoleType>.Push = Push;
	}

	static void Push(IntPtr L, RoleType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(RoleType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Man(IntPtr L)
	{
		ToLua.Push(L, RoleType.Man);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Woman(IntPtr L)
	{
		ToLua.Push(L, RoleType.Woman);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		RoleType o = (RoleType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

