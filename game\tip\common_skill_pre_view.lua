CommonSkillPreView = CommonSkillPreView or BaseClass(SafeBaseView)
local VideoPlayer = typeof(UnityEngine.Video.VideoPlayer)

function CommonSkillPreView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_common_pre_skill_show")
end

function CommonSkillPreView:SetShowSkillId(skill_id)
    self.show_skill_id = skill_id
end

function CommonSkillPreView:LoadCallBack(skill_id)
    self.video_player = self.node_list.skill_pre_rawimage:GetComponent(VideoPlayer) 
    self.node_list.video_progress.slider.onValueChanged:AddListener(BindTool.Bind(self.OnVideoProgerssChange, self))

    XUI.AddClickEventListener(self.node_list.btn_video_play, BindTool.Bind(self.OnClickSkillVideoPlay, self))
    XUI.AddClickEventListener(self.node_list.btn_video_pause, BindTool.Bind(self.OnClickSkillVideoPause, self))
    Runner.Instance:AddRunObj(self, 14)
end


function CommonSkillPreView:ReleaseCallBack()
    self.show_skill_id = nil
    self.is_playing = nil
    self.video_clips = nil
    Runner.Instance:RemoveRunObj(self)
end

-- 更新
function CommonSkillPreView:Update(now_time, elapse_time)
    if IsNil(self.video_player) or (not self.video_player.isPlaying) then
        return
    end

    self:FlushCurrPlayVideoProgress()
end

-- 刷新
function CommonSkillPreView:OnFlush(param_t)
    if not self.show_skill_id then
        return
    end


    self:FlushCurrSkillVideo()
end


function CommonSkillPreView:FlushCurrSkillVideo()
    if not self.show_skill_id then
        return
    end

    -- 准备更换视频
    if IsNil(self.video_player) then
        return
    end

    local play_video = function()
        if not IsNil(self.video_player) then
            -- 这里切换clip
            self.video_player.time = 0
            self.video_player:Play()
            self.is_playing = true
            self:FlushCurrPlayStatus()
        end
    end

    if self.video_clips == nil then
        self.video_clips = {}
    end

    if self.video_clips[self.show_skill_id] then
        self.video_player.clip = self.video_clips[self.show_skill_id]
        play_video()
    else
        local bundle, asset = ResPath.GetCommonSpiritSkillVideoPath(self.show_skill_id)

        ResPoolMgr:GetVideoClip(bundle, asset, function (asset)
            if self.video_clips ~= nil then
                self.video_clips[self.show_skill_id] = asset
            end

            if not IsNil(self.video_player) then
                self.video_player.clip = asset
            end
            play_video()
        end)
    end
end

-- 更新进度
function CommonSkillPreView:FlushCurrPlayVideoProgress()
    if IsNil(self.video_player) then
        return
    end

    local curr_time = self.video_player.time
    local total_time = self.video_player.clip.length
    local progress = curr_time / total_time
    self.node_list.video_progress.slider.value = progress
end
        
-- 当前切换进度(播放中不能切换进度)
function CommonSkillPreView:OnVideoProgerssChange(value)
    if IsNil(self.video_player) or self.video_player.isPlaying then
        return
    end

    local total_time = self.video_player.clip.length
    self.video_player.time = total_time * value
end

-- 刷新播放按钮状态
function CommonSkillPreView:FlushCurrPlayStatus()
    if self.node_list and self.node_list.btn_video_play then
        self.node_list.btn_video_play:CustomSetActive(not self.is_playing)
    end

    if self.node_list and self.node_list.btn_video_pause then
        self.node_list.btn_video_pause:CustomSetActive(self.is_playing)
    end
end
-----------------------------------------
-- 播放视频
function CommonSkillPreView:OnClickSkillVideoPlay()
    if IsNil(self.video_player) then
        return
    end

    self.video_player:Play()
    self.is_playing = true
    self:FlushCurrPlayStatus()
end

-- 暂停视频
function CommonSkillPreView:OnClickSkillVideoPause()
    if IsNil(self.video_player) then
        return
    end

    self.video_player:Pause()
    self.is_playing = false
    self:FlushCurrPlayStatus()
end