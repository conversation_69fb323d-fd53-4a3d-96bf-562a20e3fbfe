DailyExpFbSceneLogic = DailyExpFbSceneLogic or BaseClass(CommonFbLogic)

function DailyExpFbSceneLogic:__init()

end

function DailyExpFbSceneLogic:__delete()

end

function DailyExpFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()
end

function DailyExpFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function DailyExpFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	ViewManager.Instance:Close(GuideModuleName.ExpAdditionView)
end

