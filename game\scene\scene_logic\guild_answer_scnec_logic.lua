GuildAnswerSceneLogic = GuildAnswerSceneLogic or BaseClass(CommonFbLogic)

function GuildAnswerSceneLogic:__init()
	self.is_gather = false
	self.scene_effect_obj_list = {}
	self.async_loader_list = {}
end

function GuildAnswerSceneLogic:__delete()
	-- GlobalTimerQuest:CancelQuest(self.show_tips_delay)
	self.is_gather = false
end

function GuildAnswerSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
	end)

	self:SetLeaveFbTip(true)

	ChatWGCtrl.Instance:ShowAnswerContent(true)
	GuildAnswerWGCtrl.Instance:OpenTimeView()

	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
	GuildWGData.Instance:SetHideDaTiRemind(true)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_DaTi)




	-- local main_role = Scene.Instance:GetMainRole()
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
	-- GuildAnswerWGCtrl.Instance:OpenRankView()
	-- -- ChatWGCtrl.Instance:SetCurrSendChannel(CHANNEL_TYPE.GUILD)
	-- ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[CHANNEL_TYPE.GUILD])


	if activity_status then
		if activity_status.next_time > 0 and activity_status.status ~= ACTIVITY_STATUS.STANDY then
			local pass_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_CHUAN_GONG)
			local time
			if pass_status and pass_status.status ~= ACTIVITY_STATUS.STANDY then
				time = pass_status.next_time
			else
				time = activity_status.next_time
			end
			--FuBenWGCtrl.Instance:SetOutFbTime(time,true,true)
		elseif activity_status.next_time > 0 and activity_status.status == ACTIVITY_STATUS.STANDY then
			--FuBenWGCtrl.Instance:SetOutFbTime(activity_status.next_time, true, true)
		end
	end
	--MainuiWGCtrl.Instance:SetTaskActive(false)

	self.act_call_back = BindTool.Bind(self.OnActivityStatus,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_call_back)

	Scene.Instance:SendGetAllObjMoveInfoReq()
	GuildAnswerWGCtrl.Instance:ShowOrHideGuildAnswer(self:GetDatiIsOpen())
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)

	local res_async_loader = AllocResAsyncLoader(self, "guild_answer_name")
    res_async_loader:Load("uis/view/guild_answer_ui_prefab", "guild_answer_name", nil, function(obj)
        self.text_3d_name = ResMgr:Instantiate(obj)
		self.text_3d_name.transform.position = Vector3(131, 297.65, 9.9)
		self.text_3d_name.transform.rotation = Quaternion.Euler(0, 90, 0)
		self.text_3d_name.transform.localScale = Vector3(0.5, 0.5, 1)
		local name = GameVoManager.Instance:GetMainRoleVo().guild_name
		local textMesh = self.text_3d_name:GetComponent(typeof(UnityEngine.TextMesh))
		textMesh.text = name
    end)

    self:CheckSceneMapBlock(Scene.Instance:GetSceneId())

    local posx, posy = GuildAnswerWGData.Instance:GetCenterPos()
    local scene_id = Scene.Instance:GetSceneId()
    if scene_id and posx and posy then
    	GuajiWGCtrl.Instance:MoveToPos(scene_id, posx, posy,0,0,1)
    end

    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) then
    	self:LoadSceneEffect()
    	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
    	local act_time = act_info.next_time
    	FuBenWGCtrl.Instance:SetOutFbTime(act_time,true,true)
    end

    self.gather_complete_event = GlobalEventSystem:Bind(ObjectEventType.COMPLETE_GATHER, BindTool.Bind(self.OnGatherComplete, self))

    local other_cfg = GuildAnswerWGData.Instance:GetGuildQuestionOtherCfg()
	local target_gather_list = {other_cfg.gather_id, other_cfg.gather1_id}
	self.enter_target_gather_id = target_gather_list[math.random(1, #target_gather_list)]
	-- ViewManager.Instance:AddMainUIFuPingChangeList(GuildAnswerWGCtrl.Instance:GetAnswereTaskView())

	local view = GuildAnswerWGCtrl.Instance:GetAnswereTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
end

function GuildAnswerSceneLogic:LoadSceneEffect()
	-- if not self.sence_effect_pos_cfg then
	-- 	self.sence_effect_pos_cfg = {}
	-- 	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").other[1]
	-- 	self.effect_show_time = other_cfg.effect_show_time
	-- 	self.effect_gap_time = other_cfg.effect_gap_time

	-- 	local pos_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").effect_point
	-- 	for k,v in pairs(pos_cfg) do
	-- 		self.sence_effect_pos_cfg[k] = v
	-- 	end
	-- end

	-- if not IsEmptyTable(self.async_loader_list) then
	-- 	for k,v in pairs(self.async_loader_list) do
	-- 		if v then
	-- 			v:Destroy()
	-- 		end
	-- 	end
	-- 	self.async_loader_list = {}
	-- end

	-- local sence_effect_obj_list = {}
	-- local index = 1
	-- for k,v in pairs(self.sence_effect_pos_cfg) do
	-- 	local load_name = "scene_effect_" .. index
	-- 	local async_loader = AllocAsyncLoader(self, load_name)
	-- 	async_loader:SetIsUseObjPool(true)
	-- 	-- async_loader:SetIsOptimizeEffect(false)
	-- 	async_loader:SetParent(G_EffectLayer)
	-- 	async_loader:SetObjAliveTime(self.effect_show_time)
	-- 	self.async_loader_list[#self.async_loader_list + 1] = async_loader
	-- 	async_loader:Load(ResPath.GetGatherModel(7024001), "7024001_effect", function(obj)
	-- 		if IsNil(obj) then
	-- 			return
	-- 		end

	-- 		sence_effect_obj_list[#sence_effect_obj_list + 1] = obj
	-- 		local pos_cfg = self.sence_effect_pos_cfg[#sence_effect_obj_list] or nil
	-- 		if pos_cfg then
	-- 			local world_x, world_y = GameMapHelper.LogicToWorld(pos_cfg.x_point, pos_cfg.y_point)
	-- 			obj.transform.position = u3dpool.vec3(world_x, 30, world_y)
	-- 			obj.gameObject.name = "7024001_effect_" .. #sence_effect_obj_list
	-- 		end
	-- 	end)
	-- 	index = index + 1
	-- end

	-- GlobalTimerQuest:CancelQuest(self.effect_gap_timer)
	-- self.effect_gap_timer = GlobalTimerQuest:AddDelayTimer(function()
	-- 	self:LoadSceneEffect()
	-- end, self.effect_gap_time)

	-- GlobalTimerQuest:CancelQuest(self.effect_destroy_timer)
	-- self.effect_destroy_timer = GlobalTimerQuest:AddDelayTimer(function()
	-- 	if not IsEmptyTable(self.async_loader_list) then
	-- 		for k,v in pairs(self.async_loader_list) do
	-- 			if v then
	-- 				v:Destroy()
	-- 			end
	-- 		end
	-- 		self.async_loader_list = {}
	-- 	end
	-- end, self.effect_show_time)
end

function GuildAnswerSceneLogic:GetDatiIsOpen()
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
end

function GuildAnswerSceneLogic:OnActivityStatus(activity_type, status, next_time, open_type)
	BaseFbLogic.OnActivityStatus(self,activity_type, status, next_time, open_type)
	if ACTIVITY_TYPE.GUILD_ANSWER == activity_type then
		if status == ACTIVITY_STATUS.OPEN then
			local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
			if activity_status then
				if activity_status.next_time > 0 and activity_status.status ~= ACTIVITY_STATUS.STANDY then
					--FuBenWGCtrl.Instance:SetOutFbTime(activity_status.next_time, true,true)
				end
			end
			self.is_gather = false
			GuildAnswerWGCtrl.Instance:ShowOrHideGuildAnswer(true)
			self:LoadSceneEffect()
		else
			GuildAnswerWGCtrl.Instance:ShowOrHideGuildAnswer(false)
			GlobalTimerQuest:CancelQuest(self.effect_gap_timer)
			self.effect_gap_timer = nil
			GlobalTimerQuest:CancelQuest(self.effect_destroy_timer)
			self.effect_destroy_timer = nil
			if not IsEmptyTable(self.async_loader_list) then
				for k,v in pairs(self.async_loader_list) do
					if v then
						v:Destroy()
					end
				end
				self.async_loader_list = {}
			end
		end
	end
end

function GuildAnswerSceneLogic:Update(now_time, elapse_time)
	if TaskWGCtrl.Instance:IsFly() then return end
	CommonFbLogic.Update(self, now_time, elapse_time)
	local gather_list = Scene.Instance:GetGatherList()
	if self.is_gather == false then
		if #gather_list>0 then
				local player_info = GuildAnswerWGData.Instance:GetQuestionPlayerInfo()
				if player_info== nil or player_info.is_gather == nil then return end
				if player_info.is_gather > 0 then return end
				local gather_item = nil
				for k,v in pairs(gather_list)do
					if v:GetVo().gather_id == self.enter_target_gather_id then
						local target_x, target_y = v:GetLogicPos()
						gather_item = v
						self.is_gather = true
						--MainuiWGCtrl.Instance:SetTargetObj(gather_item)
						MoveCache.SetEndType(MoveEndType.Gather)
	        			MoveCache.param1 = v:GetVo().gather_id
	        			MoveCache.target_obj = gather_item
	        			GuajiCache.target_obj_id = v:GetVo().gather_id
						GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(),target_x,target_y,3.5)
					end
				end

		end
	end
end

function GuildAnswerSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_call_back)
	-- if self.before_act_mode ~= nil then
	-- 	MainuiWGCtrl.Instance:SendSetAttackMode(self.before_act_mode)
	-- end
	GuildAnswerWGCtrl.Instance:ShowOrHideGuildAnswer(false)
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	ChatWGCtrl.Instance:ShowAnswerContent(false)
	GuildAnswerWGCtrl.Instance:CloseRankView()
	GuildAnswerWGCtrl.Instance:CloseTimeView()
	FuBenPanelCountDown.Instance:CloseViewHandler()
	ChatWGCtrl.Instance:CloseChatWindow()
	ViewManager.Instance:Close(GuideModuleName.GuildPass)


	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetOtherContents(false)


	GuildAnswerWGData.Instance:ClearInvitemeInfo()
	-- UiInstanceMgr.Instance:CloseAnswerCountDown()
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text2)
	-- MainuiWGCtrl.Instance:ChangeTeamBtnName(Language.Task.task_text4, true, new_scene_type)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
    if self.text_3d_name then
        ResMgr:Destroy(self.text_3d_name)
		self.text_3d_name = nil
    end

    self.sence_effect_pos_cfg = nil
	GlobalTimerQuest:CancelQuest(self.effect_gap_timer)
	self.effect_gap_timer = nil

	GlobalTimerQuest:CancelQuest(self.effect_destroy_timer)
	self.effect_destroy_timer = nil
	if not IsEmptyTable(self.async_loader_list) then
		for k,v in pairs(self.async_loader_list) do
			if v then
				v:Destroy()
			end
		end
		self.async_loader_list = {}
	end

	self:DeleteAllGather1Effect()
	if self.gather_complete_event then
		GlobalEventSystem:UnBind(self.gather_complete_event)
		self.gather_complete_event = nil
	end
	-- ViewManager.Instance:RemoveMainUIFuPingChangeList(GuildAnswerWGCtrl.Instance:GetAnswereTaskView())

	local view = GuildAnswerWGCtrl.Instance:GetAnswereTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
end

function GuildAnswerSceneLogic:GetSceneLogicMoveState()
	local main_role = Scene.Instance:GetMainRole()
	local is_chuan_gong = main_role:IsChuanGong()
	return not is_chuan_gong
end

function GuildAnswerSceneLogic:CheckSceneMapBlock(scene_id)
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end

	self.act_id = act_scene.act_id

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		self.is_map_block = true
		self:SetSceneBlock()

		local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
		if nil == self.role_bubble_timer and map_block_bubble_cfg then
			self.role_bubble_timer = GlobalTimerQuest:AddRunQuest(
				function()
					self:SetRoleBubble(scene_id)
				end, map_block_bubble_cfg.show_time)
		end
	else
		self:RemoveSceneBlock()
	end
end

function GuildAnswerSceneLogic:IsRoleEnemy()
	return false
end

function GuildAnswerSceneLogic:CanAutoRotation()
	return false
end

function GuildAnswerSceneLogic:OnGatherComplete(gather_obj_id)
    local obj = Scene.Instance:GetObj(gather_obj_id)
    if obj and obj:GetType() == SceneObjType.GatherObj then
		local other_cfg = GuildAnswerWGData.Instance:GetGuildQuestionOtherCfg()
    	if obj:GetVo().gather_id == other_cfg.gather1_id then
    		self:ShowGather1Effect()
    	end
    end
end

-- 采集物1采集完成后播放的特效
function GuildAnswerSceneLogic:ShowGather1Effect()
	local other_cfg = GuildAnswerWGData.Instance:GetGuildQuestionOtherCfg()

	-- 特效资源
	local effect_asset_name = other_cfg.gather1_effect
	local effect_bundle_id = tonumber(Split(effect_asset_name, "_")[1])

	-- 特效位置
	local effect_pos = other_cfg.gather1_effect_pos_list
	local pos_list = {}
	local pos_str_list = Split(effect_pos, "|")
	for i,v in ipairs(pos_str_list) do
		local pos_str = Split(v, ",")
		local pos = {}
		pos.x = tonumber(pos_str[1])
		pos.y = tonumber(pos_str[2])
		table.insert(pos_list, pos)
	end

	if self.gather1_effect_loader_list == nil then
		self.gather1_effect_loader_list = {}
	end
	self:DeleteAllGather1Effect()

	for index, pos in ipairs(pos_list) do
		local load_name = "gather1_effect_" .. index
		local async_loader = AllocAsyncLoader(self, load_name)
		async_loader:SetIsUseObjPool(true)
		async_loader:SetParent(G_EffectLayer)
		async_loader:SetObjAliveTime(other_cfg.effect_show_time)
		self.gather1_effect_loader_list[#self.gather1_effect_loader_list + 1] = async_loader
		async_loader:Load(ResPath.GetGatherModel(effect_bundle_id), effect_asset_name, function(obj)
			if IsNil(obj) then
				return
			end
			local world_x, world_y = GameMapHelper.LogicToWorld(pos.x, pos.y)
			obj.transform.position = u3dpool.vec3(world_x, 30, world_y)
			obj.gameObject.name = "effect_asset_name" .. index
		end)
	end

end

function GuildAnswerSceneLogic:DeleteAllGather1Effect()
	if not IsEmptyTable(self.gather1_effect_loader_list) then
		for k,v in pairs(self.gather1_effect_loader_list) do
			if v then
				v:Destroy()
			end
		end
		self.gather1_effect_loader_list = {}
	end
end