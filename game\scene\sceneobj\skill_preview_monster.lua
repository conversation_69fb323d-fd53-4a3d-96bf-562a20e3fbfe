SkillPreviewMonster = SkillPreviewMonster or BaseClass(Monster)
function SkillPreviewMonster:__init(vo)
    self.obj_type = SceneObjType.SkillShower
    self.shield_obj_type = ShieldObjType.SkillShower
    self.save_anim_name = nil
    
    self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.High)
    if SceneObjLODManager.Instance then
        SceneObjLODManager.Instance:Remove(self)
    end
end

function SkillPreviewMonster:DoHurt()
    Monster.DoHurt(self)

    local draw_obj = self:GetDrawObj()
    if draw_obj ~= nil then
        local part = draw_obj:GetPart(SceneObjPart.Main)
        part:PlayBlinkEffect()
    end
end

function SkillPreviewMonster:SimulationAnimatorEvent(anim_name, target_obj)
    self.save_anim_name = anim_name
    self.save_target_obj = target_obj
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if not main_part then
        return
    end
    local source = main_part:GetObj()
    if source then
        self.save_anim_name = nil
        self.save_target_obj = nil
        self.actor_trigger:OnAnimatorEvent(nil, nil, source, target_obj:GetRoot(), anim_name)
    end
end

function SkillPreviewMonster:OnModelLoaded(part, obj)
    Monster.OnModelLoaded(self, part, obj)
    if part == SceneObjPart.Main then
        if self.save_anim_name and self.actor_trigger then
            local anim_name = self.save_anim_name
            self.save_anim_name = nil
            local target_obj = nil
            if self.save_target_obj then
                target_obj = self.save_target_obj:GetRoot()
            end
            self.actor_trigger:OnAnimatorEvent(nil, nil, obj, target_obj, anim_name)
        end
    end
end

function SkillPreviewMonster:ClearActorEffect()
    if self.actor_trigger then
        self.actor_trigger:StopAllEffectPlay()
    end
end

function SkillPreviewMonster:SetSpecialEffectRoot(effect_root)
    if self.actor_trigger then
        self.actor_trigger:SetSpecialEffectRoot(effect_root)
    end
end