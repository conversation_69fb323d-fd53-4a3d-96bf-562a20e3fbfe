GuildBattleRankedSceneLogic = GuildBattleRankedSceneLogic or BaseClass(CommonFbLogic)
local rotation_x_blue = 26.9
local rotation_y_blue = -275.9

local rotation_x_red = 31.8
local rotation_y_red = -77.8

function GuildBattleRankedSceneLogic:__init()
	self.change_appearance = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.ChangeAppearanceFunc, self))
	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
    self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnMonsterDead, self))
    self.be_select_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObj<PERSON>allB<PERSON>, self))

	self.now_time = 0
			-- 时间
	self.novice_check_time = 5
	-- 停止操作到自动任务的时间
	self.stop_auto_time = 0
	self.camera_distance = 12
end

function GuildBattleRankedSceneLogic:__delete()
	if self.tower_dead_effect then
		self.tower_dead_effect:DeleteMe()
		 self.tower_dead_effect = nil
	end
	self.set_mark_1 = nil
	self.set_mark_2 = nil
	self.camera_distance = 0

	if self.change_appearance then
		GlobalEventSystem:UnBind(self.change_appearance)
		self.change_appearance = nil
	end
	if self.create_obj_event then
		 GlobalEventSystem:UnBind(self.create_obj_event)
		 self.create_obj_event = nil
	end
    if self.obj_dead_event then
    	GlobalEventSystem:UnBind(self.obj_dead_event)
    	self.obj_dead_event = nil
    end
    if self.be_select_event then
    	GlobalEventSystem:UnBind(self.be_select_event)
    	self.be_select_event = nil
    end
end

function GuildBattleRankedSceneLogic:Enter(old_scene_type, new_scene_type)
	self.set_mark_1 = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SHIELD_OTHERS)
	self.set_mark_2 = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SHIELD_SAME_CAMP)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_OTHERS, false, false)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_SAME_CAMP, false, false)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self:ChangeKFZhuXieTitle()

	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))
    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
	if old_scene_type ~= new_scene_type then
		GuajiWGCtrl.Instance:StopGuaji()
		-- GuildBattleRankedWGCtrl.Instance:OpenGuildBattleRankedView()
		GuildBattleRankedWGCtrl.Instance:OpenGuildBattleRankedView()
		GuildBattleRankedWGCtrl.Instance:OpenGuildBattleTopView()
		local angle_x, angle_y
		local guilddata = GuildBattleRankedWGCtrl.Instance.data
		local guild_info_list = guilddata:GetGuildBattleSceneGlobalInfo()
		local owner_guild_name = GameVoManager.Instance:GetMainRoleVo().guild_name
		if not IsEmptyTable(guild_info_list) then

			if guild_info_list[0].guild_name == owner_guild_name then
				angle_x = rotation_x_blue
				angle_y = rotation_y_blue
			elseif guild_info_list[1].guild_name == owner_guild_name then
				angle_x = rotation_x_red
				angle_y = rotation_y_red
			end
		end

		if angle_x ~= nil and angle_y ~= nil then
			local scene_id = Scene.Instance:GetSceneId()
			local param_t = {scene_id = scene_id}
			Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, angle_x, angle_y, 12, param_t)
		end
	end
	local main_role = Scene.Instance:GetMainRole()
	self.before_act_mode = main_role:GetVo().attack_mode
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil,-80)

	local common_fuhuo = function()
		self:OnFlyDownEnd()
	end

	local here_fuhuo = function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end

	FuhuoWGCtrl.Instance:SetFuhuoCallback(common_fuhuo,here_fuhuo)
	self.camera_distance = MainCameraFollow.Distance
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	MainuiWGCtrl.Instance:FlushView(0,"recharge_vip")

	GuildWGData.Instance:SetHideGuildZhengBaRemind(true)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)

	GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE)

end

--第一次进入场景随机去到上下两路的塔坐标
local RandomTowerId = {
	[0] = {[1] = 6, [2] = 7},
	[1] = {[1] = 1, [2] = 2},
}
function GuildBattleRankedSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end
	local ran_index = math.floor(math.random(1, 2))

	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local side = role_info_list.side
	local tower_id = RandomTowerId[side] and RandomTowerId[side][ran_index] or 1
	local tower_cfg = GuildBattleRankedWGData.Instance:GetTowerIdCfg(tower_id)
	if tower_cfg then
		local pos_x = tower_cfg.pos_x
		local pos_y = tower_cfg.pos_y
		local scene_id = Scene.Instance:GetSceneId()
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	    end)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,3)
	end
end

function GuildBattleRankedSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		GuildBattleRankedWGCtrl.Instance:CloseGuildBattleRankedView()
		GuildBattleRankedWGCtrl.Instance:CloseGuildBattleTopView()
	end
	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil

	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_OTHERS, self.set_mark_1, false)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_SAME_CAMP, self.set_mark_2, false)
	GuildBattleRankedWGCtrl.Instance:SetIsEnd(nil)
	-- MainuiWGCtrl.Instance:SetTaskActive(true)	--任务栏
	-- MainuiWGCtrl.Instance:SetTaskPanel(true)

	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text2)
	-- MainuiWGCtrl.Instance:ChangeTeamBtnName(Language.Task.task_text7, true, new_scene_type)

	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil,0)

	self:ResumeSelfInfo()
	GuildBattleRankedWGCtrl.Instance:SendGuildBattleBuyMachineReq(-1)
	MainuiWGCtrl.Instance:FlushView(0,"recharge_vip")
end

-- 获取角色仙盟名
function GuildBattleRankedSceneLogic:GetGuildNameBoardText(role_vo)
	local t = {}
	local index = 1
	local guild_name = role_vo.guild_name or ""
	local guild_post = role_vo.guild_post

	if "" == guild_name then return t end
	guild_name = "【" .. guild_name .. "】"
	local authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[guild_post]
	local post_name = authority and authority.post or ""
	local guild_id = RoleWGData.Instance.role_vo.guild_id

	t[index] = {}
	t[index].color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t[index].text = guild_name .. COMMON_CONSTS.POINT
	index = index + 1

	t[index] = {}
	t[index].color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t[index].text = post_name

	return t
end

-- 获取角色名字颜色
function GuildBattleRankedSceneLogic:GetColorName(role_vo)
	local color_name = ""
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local color = role_vo.vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	color_name = role_vo.vo.name
	return color_name, color
end

 function GuildBattleRankedSceneLogic:IsRoleEnemy(target_obj, main_role)
 	 local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
 	 local target_obj_vo = target_obj:GetVo()
 	 if target_obj_vo.role_id == main_role_vo.role_id then
 		 return false
 	end
 	if GuildBattleRankedWGCtrl.Instance:GetIsEnd() then
 		return false, "当前无可攻击目标"
 	end
	return CommonFbLogic.IsRoleEnemy(self, target_obj, main_role)
 end

function GuildBattleRankedSceneLogic:IsMonsterEnemy(target_obj, main_role)
	if GuildBattleRankedWGCtrl.Instance:GetIsEnd() then
		return false, "当前无可攻击目标"
	end
	local guild_data = GuildBattleRankedWGData.Instance
	local role_info_list = guild_data:GetGuildBattleSceneUserInfo()
	local onwer_cfg = guild_data:GetGuildBattleSceneMonsterCfg(role_info_list.side)--自己的side
	local monster_id = target_obj:GetVo().monster_id
	if onwer_cfg == nil then
		return true
	end
	if onwer_cfg.home_monser_id == monster_id or onwer_cfg.tower_monser_id == monster_id then
		return false, Language.Fight.TargetNotAtk
	end

	local other_side = role_info_list.side == 1 and 0 or 1
	local other_monster_cfg = guild_data:GetGuildBattleSceneMonsterCfg(other_side)

	if other_monster_cfg == nil then
		return true
	end
	if other_monster_cfg.tower_monser_id == monster_id then
		return true
	end

	if other_monster_cfg.home_monser_id == monster_id then
		return  self:CanAttackHome()
	end
	return true
end

function GuildBattleRankedSceneLogic:OnObjCreate(obj)
	if not obj then return end
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if not obj:IsMainRole() and obj:IsRole() and obj.vo.guild_id and obj.vo.guild_id == guild_id then
		local follow_ui = obj:GetFollowUi()
		obj:SetHpVisiable(false)
		follow_ui:GetHpBar():AddShieldRule(ShieldRuleWeight.Max, function()
			return true
		end)
	end
end

function GuildBattleRankedSceneLogic:OnSelectObjCallBack(target_obj,select_type)
	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	if role_info_list == nil or role_info_list.side == nil then
		return
	end

	local other_side = role_info_list.side == 1 and 0 or 1
	--print_error(target_obj == nil,target_obj:GetVo() == nil ,target_obj:GetVo())
	if target_obj == nil then
		return
	end

	local vo = target_obj:GetVo()
	if vo ~= nil then
		local monster_id = vo.monster_id
		local other_monster_cfg = GuildBattleRankedWGData.Instance:GetGuildBattleSceneMonsterCfg(other_side)
		if other_monster_cfg.home_monser_id == monster_id and not self:CanAttackHome()then
			local str = string.format(Language.GuildBattleRanked.BattleRankedTowerTips,other_monster_cfg.tower_monser_name,other_monster_cfg.home_monser_name)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end
end

-- 获取挂机打怪的敌人
function GuildBattleRankedSceneLogic:GetGuajiCharacter()
	local target_obj
	local dis = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local is_need_stop = false
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role.vo ~= nil then
		-- 攻城车
  		if main_role.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ then
  			target_obj, dis = self:GetGuiJiTowerMonsterEnemy(true)
  			if target_obj == nil or target_obj:IsDeleted() then
  				is_need_stop = true
  			end
  		else
  			local tower_obj, tower_dis = self:GetGuiJiTowerMonsterEnemy()
  			local enemy_tower = GuildBattleRankedWGData.Instance:GetGuildBattleSceneEnemyMonsterCfg()
  			local is_home = false
  			if tower_obj then
  				is_home = tower_obj.vo and tower_obj.vo.monster_id == enemy_tower.home_monser_id
  			end

  			local x, y = main_role:GetLogicPos()
  			local role_obj, role_dis = 	Scene.Instance:SelectObjHelper(SceneObjType.Role, x, y, dis, SelectType.Enemy)
  			if tower_obj ~= nil and role_obj ~= nil then
  				if is_home then
  					target_obj = tower_obj
  				else
  					if role_dis < tower_dis then
	  					target_obj = role_obj
	  				else
	  					target_obj = tower_obj
	  				end
  				end

  			else
  				if tower_obj ~= nil then
  					target_obj = tower_obj
  				else
  					target_obj = role_obj
  				end
  			end
  		end
	end

	return target_obj, dis, is_need_stop
end

-- 获取挂机打塔的敌人
function GuildBattleRankedSceneLogic:GetGuiJiTowerMonsterEnemy(first_tower)
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = 1000 * 1000
	local target_obj, target_distance = self:SelectMonsterObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, 1000,first_tower)
	return target_obj, target_distance
end

function GuildBattleRankedSceneLogic:SelectMonsterObjHelper(obj_list, x, y, distance_limit, select_type,first_tower)
	local target_obj = nil
	local target_distance = distance_limit
	for _, v in pairs(obj_list) do
		if v:IsCharacter() or v:GetModel():IsVisible() then
			local can_select = true
			local enemy_tower = GuildBattleRankedWGData.Instance:GetGuildBattleSceneEnemyMonsterCfg()

			if not enemy_tower then
			    return nil
			end

			local tower_monser_id_list = Split(enemy_tower.tower_monser_id or "", "|")
			local is_tower = false
			for m,n in pairs(tower_monser_id_list) do
				if v.vo and  v.vo.monster_id and tonumber(n) ==  v.vo.monster_id then
					is_tower = true
					break
				end
			end

			if 1000 == select_type then
				can_select = v.vo and (v.vo.monster_id == enemy_tower.home_monser_id or is_tower)
			elseif SelectType.Friend == select_type then
				can_select = Scene.Instance:IsFriend(v)
			elseif SelectType.Enemy == select_type then
				can_select = Scene.Instance:IsEnemy(v, self.main_role)
			end

			if v.vo and v.vo.monster_id == enemy_tower.home_monser_id and not self:CanAttackHome() then
				can_select = false
			end

			if first_tower then
				local is_home = false
				if v.vo and v.vo.monster_id == enemy_tower.home_monser_id and self:CanAttackHome() then
					is_home = true
				end
				can_select = can_select and (is_tower or is_home)
			end

			if can_select then
				local target_x, target_y = v:GetLogicPos()
				local distance = GameMath.GetDistance(x, y, target_x, target_y, false)
				if distance < target_distance then
					if v:IsInBlock() then
						if nil == target_obj then
							target_obj = v
						end
					else
						target_obj = v
						target_distance = distance
					end
				end
			end
		end
	end

	return target_obj, target_distance
end


-- 获取挂机打怪的位置
function GuildBattleRankedSceneLogic:GetGuajiPos()
	-- print_error("自动挂机逻辑")
	local target_distance = 1000 * 1000
	local target_x,target_y = nil, nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local enemy_tower = GuildBattleRankedWGData.Instance:GetGuildBattleSceneEnemyMonsterCfg()
	-- local tower_monser_id_list = Split(enemy_tower.tower_monser_id or "", "|")

	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local side = role_info_list.side
	local enemy_side = side == 0 and 1 or 0
	local tower_hp_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleTowerInfo(enemy_side)
	for k, v in pairs(tower_hp_info_list) do
		if v.side ~= side and v.is_dead == 0 then
			if v.monster_id == enemy_tower.home_monser_id and self:CanAttackHome() then
				local distance = GameMath.GetDistance(x, y, v.pos_x, v.pos_y, false)
				if distance < target_distance then
					target_x = v.pos_x
					target_y = v.pos_y
					target_distance = distance
				end
			elseif BaseSceneLogic.IsAttackMonster(v.monster_id, v.vo) and v.monster_id == enemy_tower.tower_monser_id then
				local distance = GameMath.GetDistance(x, y, v.pos_x, v.pos_y, false)
				if distance < target_distance then
					target_x = v.pos_x
					target_y = v.pos_y
					target_distance = distance
				end
			end
		end
	end
	return target_x,target_y
end

-- 能否攻击水晶
function GuildBattleRankedSceneLogic:CanAttackHome()
	local useinfo = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	if useinfo then
		local count = useinfo.oppo_tower_count
		if count > 0 then
			return false
		end
		return true
	end
	return false
end

--怪物图标在小地图上的显示
function GuildBattleRankedSceneLogic:GetFbSceneMonsterCfg(monsters)
	if monsters then
		local new_list = {}
		local auto_cfg = GuildBattleRankedWGData.Instance:GetGuildBattleAutoCfg()
		local monster_id = auto_cfg.monster_map_position[1].monster_id --收集战械怪物的id 一共有两处
		local enemy_tower = GuildBattleRankedWGData.Instance:GetGuildBattleSceneEnemyMonsterCfg()
		local last_monster_id = 0
		local count = 0
		for k,v in pairs(monsters) do
			if last_monster_id ~= monster_id and v.id == monster_id then
				last_monster_id = monster_id
				local boss_cfg = BossWGData.Instance:GetMonsterInfo(v.id)
				if boss_cfg then
					for i = 1, 2 do
						local info = self:MonsterVo(v, boss_cfg)
						info.x = auto_cfg.monster_map_position[i].x
						info.y = auto_cfg.monster_map_position[i].y
						table.insert(new_list, info)
						count = count + 1
					end
				end
			elseif v.id == enemy_tower.tower_monser_id or v.id == enemy_tower.home_monser_id then
				local boss_cfg = BossWGData.Instance:GetMonsterInfo(v.id)
				if boss_cfg then
					local info = self:MonsterVo(v, boss_cfg)
					table.insert(new_list, info)
					count = count + 1
				end
			end

		end
		if count > 0 then
 			return new_list, count
 		end
	end
	return nil, 0
end

function GuildBattleRankedSceneLogic:MonsterVo(v, cfg )
	local info = {}
	info.id = v.id or 0
	info.x = v.x or 0
	info.y = v.y or 0
	info.name = cfg.name or ""
	info.level = cfg.level or 0
	return info
end

function GuildBattleRankedSceneLogic:GetFbSceneMonsterListCfg()
	return nil, true
end

function GuildBattleRankedSceneLogic:OnMonsterDead(monster_obj)
	if not monster_obj then return end
	if not monster_obj:IsMonster() then return end
	local cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").monster_cfg
	for i,v in ipairs(cfg) do
		if monster_obj.vo.monster_id == v.home_monser_id then
			local draw_obj = monster_obj:GetDrawObj()
			local pos = draw_obj and draw_obj:GetPosition()
			self.tower_dead_effect = AllocAsyncLoader(self,"GuildBattleMonsterDead")
			local b,a = ResPath.GetMiscEffect(v.home_monster_effect)
			self.tower_dead_effect:SetParent(G_EffectLayer)
			self.tower_dead_effect:Load(b,a,function (obj)
				obj.transform.position = pos
			end)
		end
	end
end

function GuildBattleRankedSceneLogic:ChangeKFZhuXieTitle()
	local mark,info = GuildBattleRankedWGCtrl.Instance:GetMarkTitle()
	if mark then return end
	local main_role = Scene.Instance:GetMainRole()
	local vo = main_role and main_role.vo
	if vo then
		self:AddKFZhuXieDelay(vo.used_title_list)
		main_role:SetAttr("used_title_list",{})
	end
	GuildBattleRankedWGCtrl.Instance:SetMarkTitle(true,vo.used_title_list)
end

function GuildBattleRankedSceneLogic:AddKFZhuXieDelay(title)
	self.my_title = title and __TableCopy(title) or self.my_title
end

function GuildBattleRankedSceneLogic:ResumeSelfInfo()
	local mark,info = GuildBattleRankedWGCtrl.Instance:GetMarkTitle()
	GuildBattleRankedWGCtrl.Instance:SetMarkTitle(false,nil)
	if info then
		RoleWGData.Instance:SetAttr("used_title_list", info)
	end
end
function GuildBattleRankedSceneLogic:ChangeAppearanceFunc(obj_id)
	self:ChangeNoneFollowUi(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	role:SetAttr("yzwc_ico", 2 - role.vo.special_param )

end
function GuildBattleRankedSceneLogic:ChangeNoneFollowUi(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	if not role then
		return
	end
	role:SetAttr("yzwc_ico", 0)
end

-- 此场景优先保证单位数量
function GuildBattleRankedSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function GuildBattleRankedSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

----------------------空气墙-------------------------------------------
function GuildBattleRankedSceneLogic:CheckSceneMapBlock(scene_id)
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if is_open then
		self:RemoveSceneBlock()
		return
	end
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_table = os.date("*t", server_time)
	local start_hour,start_min,during_time,_,second_time = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
	local has_pass_time = time_table.hour * 3600 + time_table.min * 60 + time_table.sec

	local delay_sec = start_hour * 3600 - has_pass_time + start_min * 60
	-- local delay_sec_1 = second_time * 60 + 30 -  time_table.min * 60 - time_table.sec
	if delay_sec > 1 and start_min == time_table.min then
		UiInstanceMgr.Instance:DoFBStartDown( delay_sec + server_time ,function ()
			self:RemoveSceneBlock()
		end)
	-- elseif delay_sec_1 > 1 and second_time == time_table.min then
	-- 	UiInstanceMgr.Instance:DoFBStartDown( delay_sec_1 + server_time ,function ()
	-- 		self:RemoveSceneBlock()
	-- 	end)
	else
		return
	end

	self.act_id = act_scene.act_id


	self.is_map_block = true
	self:SetSceneBlock()

 	local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
	if nil == self.role_bubble_timer and map_block_bubble_cfg then
		self.role_bubble_timer = GlobalTimerQuest:AddRunQuest(
			function()
				self:SetRoleBubble(scene_id)
			end, map_block_bubble_cfg.show_time)
	end

end

function GuildBattleRankedSceneLogic:ShowFindLately(gather_obj_id)
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role.vo ~= nil then
	-- 攻城车
		if main_role.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ then
			return true
		end
	end
	return false
end
