NewAppearanceResloveView = NewAppearanceResloveView or BaseClass(SafeBaseView)
function NewAppearanceResloveView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(-16, 8), sizeDelta = Vector2(938, 652)})
	self:AddViewResource(0, "uis/view/new_appearance_ui_prefab", "layout_appearance_melting_view")

	self.item_data_event = BindTool.Bind(self.RoleBagDataChange, self)
end

function NewAppearanceResloveView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewAppearance.ResloveName

    if nil == self.attr_list then
        self.attr_list = {}
        local attr_list_node = self.node_list["attr_list"]
        local attr_num = attr_list_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAttrRender.New(attr_list_node:FindObj("attr_" .. i))
            cell:SetAttrNameNeedSpace(true)
            cell:SetIndex(i)
            self.attr_list[i] = cell
        end
    end

    if not self.item_grid then
        self.item_grid = AppearanceMeltingGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 5,
            cell_count = 40,
            list_view = self.node_list["item_grid"],
            itemRender = MeltEquipCell,
            change_cells_num = 2,
        })
        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

    XUI.AddClickEventListener(self.node_list["btn_reslove"], BindTool.Bind(self.OnClickReslove, self))
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function NewAppearanceResloveView:ReleaseCallBack()
    ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
    if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end

    if self.attr_list then
        for k,v in pairs(self.attr_list) do
            v:DeleteMe()
        end

        self.attr_list = nil
    end
end

function NewAppearanceResloveView:OnBagSelectItemCB(cell)
    self:CalcSelect()
end

function NewAppearanceResloveView:OnFlush()
    local item_list = NewAppearanceWGData.Instance:GetAppearanceResloveBagList()
    self.item_grid:SetDataList(item_list)
    self:FlushView()
end

function NewAppearanceResloveView:CalcSelect()
    local na_data = NewAppearanceWGData.Instance
	local select_list = self.item_grid:GetAllSelectCell()
    local info = na_data:GetAppearanceResloveInfo()
	local max_level = na_data:GetAppearanceResloveMaxLevel()
	local cur_level = info.melt_level

	if IsEmptyTable(select_list) or cur_level == nil or cur_level >= max_level then
		self:FlushView()
		return
	end

    local cur_cfg = na_data:GetAppearanceResloveLevelCfg(cur_level)
	if cur_cfg == nil then
		self:FlushView()
		return
	end

	local add_exp = 0
	local total_add_level = 0
	for k,v in pairs(select_list) do
        local cfg = na_data:GetAppearanceResloveItemCfg(v.item_id)
        if cfg then
            add_exp = add_exp + cfg.add_exp * v.num
        end
	end

	local tab
	local cur_level_max_exp = cur_cfg.need_exp
	local will_add_exp = info.melt_exp + add_exp
	if will_add_exp >= cur_level_max_exp then
		local cfg = nil
		while(true) do
			if will_add_exp - cur_level_max_exp >= 0 then
				will_add_exp = will_add_exp - cur_level_max_exp
				 total_add_level = total_add_level + 1
				 cfg = na_data:GetAppearanceResloveLevelCfg(cur_level + total_add_level)
				 if not cfg then
					 break
				 end

				 cur_level_max_exp = cfg.need_exp
			 else
				 break
			 end
		end
	end

	tab = {melt_level = cur_level + total_add_level, melt_exp = will_add_exp}
	self:FlushView(tab)
end

function NewAppearanceResloveView:FlushView(info)
    local old_info = NewAppearanceWGData.Instance:GetAppearanceResloveInfo()
	info = info or old_info
    if info == nil then
        return
    end

	local max_level = NewAppearanceWGData.Instance:GetAppearanceResloveMaxLevel()
    local cur_level = math.min(info.melt_level, max_level)
    local cur_exp = info.melt_exp
	local cur_level_cfg = NewAppearanceWGData.Instance:GetAppearanceResloveLevelCfg(cur_level)
    local attr_list = EquipWGData.GetSortAttrListByTypeCfg(cur_level_cfg, "attr_id", "attr_value", 1, 5)
	for k,v in ipairs(self.attr_list) do
		v:SetData(attr_list[k])
	end

    local cur_pro_value, need_pro_value, pro_percent = 0, 0, 1
    if cur_level_cfg and ((cur_level < max_level) or (cur_level == max_level and cur_exp < cur_level_cfg.need_exp)) then
        cur_pro_value = cur_exp
        need_pro_value = cur_level_cfg.need_exp
        pro_percent = cur_exp / cur_level_cfg.need_exp
        self.node_list["level_text"].text.text = "Lv." .. cur_level
    else
        cur_pro_value = "-"
        need_pro_value = "-"
        self.node_list["level_text"].text.text = "Lv." .. max_level
    end

    self.node_list["progress_text"].text.text = string.format("%s/%s", cur_pro_value, need_pro_value)
    self.node_list["progress"].slider.value = pro_percent
    self.node_list["up_level_arrow"]:SetActive(cur_level > old_info.melt_level)
end

function NewAppearanceResloveView:OnClickReslove()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.ResloveError)
		return
	end

	local reslove_list = {}
	for k,v in pairs(select_list) do
		reslove_list[#reslove_list + 1] = v
	end

    self.item_grid:CancleAllSelectCell()
	NewAppearanceWGCtrl.Instance:SendAppearanceReslove(reslove_list)
end

function NewAppearanceResloveView:RoleBagDataChange(item_id, index, reason, put_reason, old_num, new_num)
    if NewAppearanceWGData.Instance:GetAppearanceResloveItemCfg(item_id) == nil then
        return
    end

    local is_add = reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	                (reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num)

	if not is_add then
		return
	end

	self:Flush()
end

---------------------------------------------------------
-- AppearanceMeltingGrid
---------------------------------------------------------
AppearanceMeltingGrid = AppearanceMeltingGrid or BaseClass(AsyncBaseGrid)
function AppearanceMeltingGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= 50 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.ResloveLimit)
            return true
        end
    end

    return false
end