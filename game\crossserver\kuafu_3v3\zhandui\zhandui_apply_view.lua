ZhanDuiApplyView = ZhanDuiApplyView or BaseClass(SafeBaseView)

function ZhanDuiApplyView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(10, 8), sizeDelta = Vector2(854, 496)})
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_apply")
end

function ZhanDuiApplyView:ReleaseCallBack()
    if self.apply_list then
        self.apply_list:DeleteMe()
        self.apply_list = nil
    end
    if self.new_apply_event then
        GlobalEventSystem:UnBind(self.new_apply_event)
        self.new_apply_event = nil
    end
    if self.all_apply_event then
        GlobalEventSystem:UnBind(self.all_apply_event)
        self.all_apply_event = nil
    end
end

function ZhanDuiApplyView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleApply
    XUI.AddClickEventListener(self.node_list["btn_today_refuse_check"], BindTool.Bind1(self.OnClickTodayCheck, self))
    self.apply_list = AsyncListView.New(ZhanDuiApplyRender, self.node_list.apply_list)

    self.new_apply_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_New_Apply, BindTool.Bind(self.OnNewApplyCallBack, self))
    self.all_apply_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_All_Apply, BindTool.Bind(self.OnNewApplyCallBack, self))
end

function ZhanDuiApplyView:ShowIndexCallBack()
	self:Flush()
	self.node_list["btn_today_refuse_select"]:SetActive(false)
end

function ZhanDuiApplyView:OnFlush()
    self:FlushList()
end

function ZhanDuiApplyView:CloseCallBack()
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ZHAN_DUI_NEW_APPLT, ZhanDuiWGData.Instance:GetApplyCount(), function ()
		ZhanDuiWGCtrl.Instance:OpenApplyView()
		return true
	end)
    RemindManager.Instance:Fire(RemindName.ZhanDui)
end

function ZhanDuiApplyView:FlushList()
    local data_list = ZhanDuiWGData.Instance:GetApplyList()
    self.node_list.nodata_tips:SetActive(#data_list <= 0)
    self.apply_list:SetDataList(data_list)
end

function ZhanDuiApplyView:GetApplyTodayCheckActive()
	if not self.node_list["btn_today_refuse_select"] then
		return false
	end
	return self.node_list["btn_today_refuse_select"].gameObject.activeSelf
end

function ZhanDuiApplyView:OnClickTodayCheck()
	local is_select = self.node_list["btn_today_refuse_select"].gameObject.activeSelf
	self.node_list["btn_today_refuse_select"]:SetActive(not is_select)
end

function ZhanDuiApplyView:OnNewApplyCallBack()
    if not self:IsOpen() then
        return
    end
    local data_list = ZhanDuiWGData.Instance:GetApplyList()
    self.node_list.nodata_tips:SetActive(#data_list <= 0)
    if #data_list <= 0 then
        self:Close()
        return
    end
    self.apply_list:SetDataList(data_list)
end


ZhanDuiApplyRender = ZhanDuiApplyRender or BaseClass(BaseRender)
function ZhanDuiApplyRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_refuse"], BindTool.Bind(self.OnClickRefuse, self))
    XUI.AddClickEventListener(self.node_list["btn_agree"], BindTool.Bind(self.OnClickAgree, self))
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function ZhanDuiApplyRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function ZhanDuiApplyRender:OnFlush()
    --ZhanDuiWGCtrl.Instance:GetApplyTodayCheckActive()
    local str = string.format(Language.NewTeam.ApplyViewRoleName, self.data.name, self.data.level)
    --print_error(">>>>>>>>>>>> ",self.data.name, self.data.level)
	--EmojiTextUtil.ParseRichText(self.node_list["lbl_role_name"].emoji_text, str, 21, COLOR3B.DEFAULT)

    self.node_list["lbl_role_name"].text.text = self.data.name
    local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.dianfen_img:SetActive(is_vis)
    if is_vis then
        self.node_list.lbl_level.text.text = level
    else
        self.node_list.lbl_level.text.text = "Lv." .. level
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["lbl_role_name"].rect)

	self.node_list.power_right.text.text = self.data.capability
    if self.data.vip_level then   -- 设置VIP等级
        self.node_list.vip_level:SetActive(self.data.vip_level >= 1)
        -- self.node_list.vip_level.image:LoadSprite(ResPath.GetVipIcon("vip"..self.data.vip_level))
        -- self.node_list.vip_level.image:SetNativeSize()
        local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
        self.node_list.vip_level.text.text = is_hide_vip and "V" or "V".. self.data.vip_level
    end
    local data = {}
    data.role_id = self.data.uid
    data.prof = self.data.prof
    data.sex = self.data.sex
    data.fashion_photoframe = self.data.photoframe
    self.head_cell:SetData(data)
end

function ZhanDuiApplyRender:OnClickRefuse()
    local is_today_refuse = ZhanDuiWGCtrl.Instance:GetApplyTodayCheckActive()
    ZhanDuiWGCtrl.Instance:SendApprovalApplyToJoinZhanDui(self.data.uid, 0)
    if is_today_refuse then
        ZhanDuiWGCtrl.Instance:SendTodayRejectApplyJoin(self.data.uid)
    end
    local is_remove_sucess = ZhanDuiWGData.Instance:RemoveApply(self.data.uid)
   --- print_error(is_remove_sucess)
    ZhanDuiWGCtrl.Instance:FlushApplyView()
end
function ZhanDuiApplyRender:OnClickAgree()
    ZhanDuiWGCtrl.Instance:SendApprovalApplyToJoinZhanDui(self.data.uid, 1)
    ZhanDuiWGData.Instance:RemoveApply(self.data.uid)
    ZhanDuiWGCtrl.Instance:FlushApplyView()
end

