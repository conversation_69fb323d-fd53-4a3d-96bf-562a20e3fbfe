﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_QualityConfigWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.QualityConfig), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>RegFunction("OnGameStop", OnGameStop);
		<PERSON><PERSON>unction("GetMaxQualityLevel", GetMaxQualityLevel);
		L.RegFunction("ClearInstance", ClearInstance);
		L.RegFunction("ListenQualityChanged", ListenQualityChanged);
		L.RegFunction("UnlistenQualtiy", UnlistenQualtiy);
		<PERSON><PERSON>RegFunction("GetLevelCount", GetLevelCount);
		<PERSON><PERSON>unction("GetLevel", GetLevel);
		L.RegFunction("SetShadowDisabled", SetShadowDisabled);
		L.RegFunction("New", _CreateNirvana_QualityConfig);
		<PERSON>.<PERSON>Function("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Instance", get_Instance, null);
		<PERSON><PERSON>ar("QualityLevel", get_QualityLevel, set_QualityLevel);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_QualityConfig(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.QualityConfig obj = new Nirvana.QualityConfig();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.QualityConfig.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.QualityConfig.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMaxQualityLevel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = Nirvana.QualityConfig.GetMaxQualityLevel();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearInstance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.QualityConfig.ClearInstance();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenQualityChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
			System.Collections.Generic.LinkedListNode<System.Action> o = Nirvana.QualityConfig.ListenQualityChanged(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnlistenQualtiy(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Collections.Generic.LinkedListNode<System.Action> arg0 = (System.Collections.Generic.LinkedListNode<System.Action>)ToLua.CheckObject(L, 1, typeof(System.Collections.Generic.LinkedListNode<System.Action>));
			Nirvana.QualityConfig.UnlistenQualtiy(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLevelCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.QualityConfig obj = (Nirvana.QualityConfig)ToLua.CheckObject(L, 1, typeof(Nirvana.QualityConfig));
			int o = obj.GetLevelCount();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLevel(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<int>(L, 2))
			{
				Nirvana.QualityConfig obj = (Nirvana.QualityConfig)ToLua.CheckObject(L, 1, typeof(Nirvana.QualityConfig));
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				Nirvana.QualityLevel o = obj.GetLevel(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<string>(L, 2))
			{
				Nirvana.QualityConfig obj = (Nirvana.QualityConfig)ToLua.CheckObject(L, 1, typeof(Nirvana.QualityConfig));
				string arg0 = ToLua.ToString(L, 2);
				Nirvana.QualityLevel o = obj.GetLevel(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.QualityConfig.GetLevel");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetShadowDisabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			Nirvana.QualityConfig.SetShadowDisabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.PushSealed(L, Nirvana.QualityConfig.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_QualityLevel(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, Nirvana.QualityConfig.QualityLevel);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_QualityLevel(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			Nirvana.QualityConfig.QualityLevel = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

