-- 仙侣PK准备战场

LoverPKPrepareSceneLogic = LoverPKPrepareSceneLogic or BaseClass(CommonFbLogic)

function LoverPKPrepareSceneLogic:__init()
end

function LoverPKPrepareSceneLogic:__delete()
    if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
    end

    -- if self.complete_gather then
    --     GlobalEventSystem:UnBind(self.complete_gather)
    --     self.complete_gather = nil
    -- end
end

function LoverPKPrepareSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
        LoverPkWGCtrl.Instance:OpenLoverPKPrepareSceneView()

        local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
        if activity_status then
            MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_status.next_time)
        end
    end)
    
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_KNOCKOUT_MATCH_INFO)
    
	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
    -- self.complete_gather = GlobalEventSystem:Bind(ObjectEventType.COMPLETE_GATHER,BindTool.Bind(self.GatherPickObjEnd, self))
    
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.LoverPK.ConfirmLevelFB)
	ViewManager.Instance:AddMainUIFuPingChangeList(LoverPkWGCtrl.Instance:GetLoverPKPrepareSceneView())
end

function LoverPKPrepareSceneLogic:Out(old_scene_type, new_scene_type)
    CommonFbLogic.Out(self)

    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)

    if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
    end

    -- if self.complete_gather then
    --     GlobalEventSystem:UnBind(self.complete_gather)
    --     self.complete_gather = nil
    -- end

    LoverPkWGCtrl.Instance:CloseLoverPKPrepareSceneView()
    ViewManager.Instance:RemoveMainUIFuPingChangeList(LoverPkWGCtrl.Instance:GetLoverPKPrepareSceneView())
end

function LoverPKPrepareSceneLogic:IsEnemy(target_obj, main_role)
    if target_obj == nil then return false end

    if target_obj:GetType() == SceneObjType.Role then
        return false
    end

    local cfg = LoverPkWGData.Instance:GetKnockoutGuardMonstersCfg()
    if target_obj.vo.monster_id == cfg.monster_id then
        return false
    end

    return true
end

function LoverPKPrepareSceneLogic:OnObjCreate(obj)
    if obj and obj:GetType() == SceneObjType.GatherObj then
        local pick_obj = self:GetPickedObj()
        if pick_obj then
            self:DoGatherPickObj()
            return
        end
    end
end

function LoverPKPrepareSceneLogic:DoGatherPickObj()
    local pick_obj = self:GetPickedObj()
    local main_role = Scene.Instance and Scene.Instance:GetMainRole()
    
    if pick_obj and main_role and not main_role:IsRealDead() and not main_role:GetIsGatherState() then
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
        MoveCache.SetEndType(MoveEndType.Gather)
        MoveCache.target_obj = pick_obj
        GuajiWGCtrl.Instance:MoveToObj(pick_obj, 1)
    end
end

function LoverPKPrepareSceneLogic:GetPickedObj()
    local gather_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
    if IsEmptyTable(gather_list) then
        return nil
    end

    for k, obj in pairs(gather_list) do
        if obj then
            return obj
        end
    end

    return nil
end

-- function LoverPKPrepareSceneLogic:GatherPickObjEnd()
--     GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end