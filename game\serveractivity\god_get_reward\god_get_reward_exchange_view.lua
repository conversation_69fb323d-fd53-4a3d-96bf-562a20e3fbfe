GodGetRewardExchangeView = GodGetRewardExchangeView or BaseClass(SafeBaseView)

function GodGetRewardExchangeView:__init(view_name)
	self.view_name = "GodGetRewardExchangeView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(-7, -6), sizeDelta = Vector2(932, 590)})
	self:AddViewResource(0, "uis/view/ts_duobao_ui_prefab", "layout_ts_duobao_exchange")
	self:SetMaskBg(true, true)
end

function GodGetRewardExchangeView:OpenCallBack()
	self:ReqExchangeInfo()
end

function GodGetRewardExchangeView:LoadCallBack()
	self.data_list_count = 0
	self.node_list.title_view_name.text.text = Language.TSXunBao.ExChangeTitle
	self:InitPanel()
end

function GodGetRewardExchangeView:ReleaseCallBack()
	if self.exchange_grid then
		self.exchange_grid:DeleteMe()
		self.exchange_grid = nil
	end
end

function GodGetRewardExchangeView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "exchange" or k == "all" then
			self:RefreshView()
		end
	end
end

function GodGetRewardExchangeView:ReqExchangeInfo()
	local cur_layer = GodGetRewardWGData.Instance:GetSelectLayer()
	GodGetRewardWGCtrl.Instance:SendOpera(TS_XUNBAO_OPERA_TYPE.EXCHANGE_INFO, cur_layer)
end

function GodGetRewardExchangeView:InitPanel()
	local bundle = "uis/view/ts_duobao_ui_prefab"
	local asset = "exchange_render"
	self.exchange_grid = AsyncBaseGrid.New()
	self.exchange_grid:SetStartZeroIndex(false)
	self.exchange_grid:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["exchange_list"],
				assetBundle = bundle, assetName = asset, itemRender = GodGetRewardExchangeItem})
end

function GodGetRewardExchangeView:RefreshView()
	local cur_layer = GodGetRewardWGData.Instance:GetSelectLayer()
	if self.exchange_grid then
		local data_list = GodGetRewardWGData.Instance:GetExchangeDataList(cur_layer)
		local temp_list = {}
		for i,v in ipairs(data_list) do
			if v.exchange_count < v.cfg.exchange_limit then
				temp_list[i + v.cfg.exchange_consume * 10000] = v
			else
				temp_list[i] = v
			end
		end
		temp_list = SortTableKey(temp_list, true)

		if self.data_list_count ~= #data_list and #data_list > 0 then
			self.exchange_grid:SetDataList(temp_list, 3)
			self.data_list_count = #data_list
		else
			self.exchange_grid:SetDataList(temp_list, 2)
		end
	end
	self.node_list.const_lbl.text.text = GodGetRewardWGData.Instance:GetExchangeScore(cur_layer)
end

-------------------------------------------------------------------

GodGetRewardExchangeItem = GodGetRewardExchangeItem or BaseClass(BaseRender)

function GodGetRewardExchangeItem:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function GodGetRewardExchangeItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.cell_root)
	XUI.AddClickEventListener(self.node_list.exchange_btn, BindTool.Bind(self.OnClickExchangeBtn, self))
end

function GodGetRewardExchangeItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.cfg.exchange_item.item_id)
	if item_cfg then
		self.node_list.item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	end

	local max_num = data.cfg.exchange_limit
	local now_num = data.exchange_count
	self.node_list.exchange_lbl.text.text = string.format(Language.TSXunBao.ExChangeLimit, now_num, max_num)
	self.node_list.all_sell_img:SetActive(now_num >= max_num)
	self.node_list.exchange_btn:SetActive(now_num < max_num)
	self.node_list.exchange_lbl.text.color = Str2C3b(now_num < max_num and COLOR3B.GREEN or COLOR3B.RED)

	self.node_list.const_lbl.text.text = data.cfg.exchange_consume
	self.item_cell:SetData(data.cfg.exchange_item)
end

function GodGetRewardExchangeItem:OnClickExchangeBtn()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	GodGetRewardWGCtrl.Instance:SendOpera(TS_XUNBAO_OPERA_TYPE.EXCHANGE_ITEM, data.cfg.layer, data.cfg.exchange_item.item_id, 1)
end