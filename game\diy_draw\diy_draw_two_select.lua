local SelectTextOutLineColor = {
    [0] = "#ab5939",
    [1] = "#9d6d1a",
    [2] = "#8c45a1",
    [3] = "#4552a1",
	[4] = "#000000",
	[5] = "#000000",
}

DIYDrawTwoSelectView = DIYDrawTwoSelectView or BaseClass(SafeBaseView)
function DIYDrawTwoSelectView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {sizeDelta = Vector2(920,582)})
	self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "diydraw_select_view")
end

function DIYDrawTwoSelectView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ZhouYiYunCheng.SelectViewTitle
	self.change_select_list = AsyncListView.New(DIYDrawTwoSelectBigCell, self.node_list["reward_list"])
    --self.node_list["confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))
end

function DIYDrawTwoSelectView:ReleaseCallBack()

	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end

end

function DIYDrawTwoSelectView:OpenCallBack()
    DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.INFO)
end

function DIYDrawTwoSelectView:OnFlush()
	local choose_list = DIYDrawWGData.Instance:GetDIY2ChooseRewardPoolList()
	if choose_list then
		self.change_select_list:SetDataList(choose_list)
	end
end


DIYDrawTwoSelectBigCell = DIYDrawTwoSelectBigCell or BaseClass(BaseRender)
function DIYDrawTwoSelectBigCell:__init()
	self.big_change_select_list = {}
	self.cur_select_index = -1
	for i = 1, 10 do
		self.big_change_select_list[i] = DIYDrawTwoSelectSmallCell.New(self.node_list["item_group"]:FindObj("select_small_cell_" .. i))
		self.big_change_select_list[i]:SetIndex(i)
		self.big_change_select_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectCallBack, self))
	end
end

function DIYDrawTwoSelectBigCell:__delete()
	if self.big_change_select_list then
		for k, v in pairs(self.big_change_select_list) do
			v:DeleteMe()
		end
		self.big_change_select_list = nil
	end
end

function DIYDrawTwoSelectBigCell:OnSelectCallBack(cell)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

	local index = cell:GetIndex()
	if self.cur_select_index ~= -1 and self.cur_select_index ~= index then
		if self.data ~= nil then
			--print_error(self.data.seq_index, data.seq)
			--发送协议
			DIYDrawWGCtrl.Instance:SendDIYDrawReq(
			ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW,
			OA_DIY_DRAW1_OPERATE_TYPE.CHOOSE,
			self.data.seq_index, data.seq)
		end
		self.cur_select_index = index
	end
	
	for k,v in pairs(self.big_change_select_list) do
		v:FlushSelectHL(data.seq)
	end
end


function DIYDrawTwoSelectBigCell:OnFlush()
	if not self.data then
		return
	end

	for i = 1, 10 do
		if self.data.seq_list[i] then
			self.big_change_select_list[i]:SetData(self.data.seq_list[i])
			self.big_change_select_list[i]:SetActive(true)
		else
			self.big_change_select_list[i]:SetActive(false)
		end
	end

	if self.cur_select_index == -1 then
		self.cur_select_index = 0
	end

	for k,v in pairs(self.big_change_select_list) do
		v:FlushSelectHL(self.data.cur_seq)
	end

	local out_line = self.node_list.select_num:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
	if SelectTextOutLineColor[self.data.seq_index] then
		out_line.effectColor = Str2C3b(SelectTextOutLineColor[self.data.seq_index])
	end

	self.node_list.select_num.text.text = Language.CangBaoShiGuang.ReWaredLevelText[self.data.seq_index]
	local bundle, asset = ResPath.GetDIYDrawImg("a2_jlxz_sel_" .. self.data.seq_index)
	if bundle and asset then
		self.node_list["img"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["img"].image:SetNativeSize()
		end)
	end
end




DIYDrawTwoSelectSmallCell = DIYDrawTwoSelectSmallCell or BaseClass(BaseRender)
function DIYDrawTwoSelectSmallCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
	self.node_list["select_area"].button:AddClickListener(BindTool.Bind(self.OnClickSelect, self))
end

function DIYDrawTwoSelectSmallCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.click_callback = nil
end

function DIYDrawTwoSelectSmallCell:SetCellClickCallBack(call_back)
    self.click_callback = call_back
end

function DIYDrawTwoSelectSmallCell:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data.item)
end

function DIYDrawTwoSelectSmallCell:OnClickSelect()
	if self.click_callback then
		self.click_callback(self)
	end
end

function DIYDrawTwoSelectSmallCell:FlushSelectHL(seq)
	if not self.data then
		return
	end

	self.node_list["select_flag"]:SetActive(self.data.seq == seq)

	if self.data.seq == seq then
		self.node_list["select_area"].image.raycastTarget = false
	else
		self.node_list["select_area"].image.raycastTarget = true
	end
end
