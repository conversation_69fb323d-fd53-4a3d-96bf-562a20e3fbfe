TianShiGodownHillWGData = TianShiGodownHillWGData or BaseClass()

function TianShiGodownHillWGData:__init()
	if TianShiGodownHillWGData.Instance ~= nil then
		ErrorLog("[TianShiGodownHillWGData] attempt to create singleton twice!")
		return
	end

	TianShiGodownHillWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_like_cfg_auto")
    self.hot_level_cfg = ListToMapList(cfg.hot_level, "grade")
    self.hot_level_seq_cfg = ListToMap(cfg.hot_level, "grade", "seq")
    self.other_cfg = cfg.other[1]
    self.model_show_cfg = ListToMap(cfg.model_show, "index", "seq")

	self.grade = -1 -- 档次
    self.like_num = 0 --今日已点赞次数
    self.hot_value = 0 --全服热度
	self.reward_flag = {}

    RemindManager.Instance:Register(RemindName.TianShiGodownHill, BindTool.Bind(self.GetTianShiGodownHillRed, self))
end

function TianShiGodownHillWGData:__delete()
	TianShiGodownHillWGData.Instance = nil
	self.grade = nil
	self.reward_flag = nil
end

function TianShiGodownHillWGData:SetAllInfo(protocol)
    self.like_num = protocol.like_num
    self.grade = protocol.grade
    self.reward_flag = protocol.reward_flag
    self.hot_value = protocol.hot_value
end

--每日最大点赞次数
function TianShiGodownHillWGData:GetDolikeCfgTimes()
    return self.other_cfg.max_like_count
end

--全服热度列表信息
function TianShiGodownHillWGData:GetHotLevelCfg()
    return self.hot_level_cfg[self.grade] or {}
end

--全服热度索引信息
function TianShiGodownHillWGData:GetHotLevelSeqCfg(seq)
    return (self.hot_level_seq_cfg[self.grade] or {})[seq] or {}
end

--宝箱领取状态
function TianShiGodownHillWGData:GetRewardFlag(seq)
    return self.reward_flag[seq] or 0
end

--宝箱数据list
function TianShiGodownHillWGData:GetHotLevelList()
    local cfg = self:GetHotLevelCfg()
    local data_list = {}
    for k, v in pairs(cfg) do
        table.insert(data_list, v)
    end

    return data_list
end

--点赞描述显示
function TianShiGodownHillWGData:GetTodayDolikeTimesStr() 
    local total_times = self:GetDolikeCfgTimes()
    local has_times = total_times - self.like_num
    local times_str = has_times .. "/" .. total_times
    local desc_str1 = string.format(Language.TianShiGodownHill.TodayDolikeTimesStr, times_str)
    local desc_str2 = Language.TianShiGodownHill.TodayDolikeTimesStr2

    --local desc_str = has_times > 0 and desc_str1 or desc_str2
    local desc_str = desc_str1
    return desc_str
end

--今日是否还能点赞
function TianShiGodownHillWGData:GetTodayIsDolike()
    local total_times = self:GetDolikeCfgTimes()
    local has_times = total_times - self.like_num
    return has_times > 0
end

--全服热度值
function TianShiGodownHillWGData:GetAllServerTimes()
    return self.hot_value
end

--主界面红点
function TianShiGodownHillWGData:GetTianShiGodownHillRed()
    local data_list = self:GetHotLevelList()
    if data_list == nil then
        return 0
    end

    for k, v in pairs(data_list) do
        if self:GetHotLevelRed(v.seq) then
            return 1
        end
    end

    return 0
end

--单个宝箱红点
function TianShiGodownHillWGData:GetHotLevelRed(seq)
    local hot_value = self:GetAllServerTimes()
    local seq_cfg = self:GetHotLevelSeqCfg(seq)

    if seq_cfg == nil then
        return false
    end
    
    local reward_flag = self:GetRewardFlag(seq)
    if reward_flag == 1 then
        return false
    end

    return hot_value >= seq_cfg.hot_value
end

--宝箱进度条
function TianShiGodownHillWGData:GetRewardCurProgress(intimacy_value)
	local cur_progress = 0
	local cur_intimacy_value = tonumber(intimacy_value)
	local leiji_data = self:GetHotLevelList()
	if next(leiji_data) == nil or cur_intimacy_value == nil then
		return cur_progress
	end

	local slider_piecewise = {0.13, 0.25, 0.36, 0.5, 0.62, 0.73, 0.85, 1}

	for k, v in pairs(slider_piecewise) do
		local seq = k - 1
		local length = #slider_piecewise
		local cur_need = leiji_data[seq] and leiji_data[seq].hot_value or 0
		local next_need = leiji_data[seq + 1] and leiji_data[seq + 1].hot_value or leiji_data[#leiji_data].hot_value
		local cur_value = slider_piecewise[seq] and slider_piecewise[seq] or 0
		local next_value = slider_piecewise[seq + 1] and slider_piecewise[seq + 1] or slider_piecewise[length]

		if cur_intimacy_value > cur_need and cur_intimacy_value <= next_need then
			cur_progress = (cur_intimacy_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_intimacy_value > leiji_data[#leiji_data].hot_value then
			cur_progress = slider_piecewise[length]
			break
		end
	end
	return cur_progress
end

function TianShiGodownHillWGData:GetModelShowCfgByIndexAndSeq(index, seq)
    return (self.model_show_cfg[index] or {})[seq] or {}
end