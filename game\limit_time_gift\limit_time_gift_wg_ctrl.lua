require("game/limit_time_gift/limit_time_gift_wg_data")
require("game/limit_time_gift/limit_time_gift_view")
require("game/limit_time_gift/limit_time_gift_tip_view")
require("game/limit_time_gift/limit_time_gift_purchase_view")

LimitTimeGiftWGCtrl = LimitTimeGiftWGCtrl or BaseClass(BaseWGCtrl)

function LimitTimeGiftWGCtrl:__init()
	if LimitTimeGiftWGCtrl.Instance then
		error("[LimitTimeGiftWGCtrl]:Attempt to create singleton twice!")
		return
	end
	LimitTimeGiftWGCtrl.Instance = self

	self.data = LimitTimeGiftWGData.New()
	self.view = LimitTimeGiftView.New(GuideModuleName.LimitTimeGift)
	self.tip_view = LimitTimeGiftTipView.New()
	self.limit_time_gift_purchase_view = LimitTimeGiftPurchaseView.New(GuideModuleName.LimitTimeGiftPurchase)
	
	self:RegisterAllProtocals()

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
end

function LimitTimeGiftWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	self.tip_view:DeleteMe()
	self.tip_view = nil

	self.limit_time_gift_purchase_view:DeleteMe()
	self.limit_time_gift_purchase_view = nil

	if CountDownManager.Instance then
		CountDownManager.Instance:RemoveCountDown("limit_time_gift_pass_time_tip")
	end

	LimitTimeGiftWGCtrl.Instance = nil
end

function LimitTimeGiftWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCLimitTimeGiftInfo, "OnSCLimitTimeGiftInfo")
	self:RegisterProtocol(SCLimitTimeChangeGiftInfo, "OnSCLimitTimeChangeGiftInfo")
	self:RegisterProtocol(SCLimitTimeGiftDelay, "OnSCLimitTimeGiftDelay")
	
	self:RegisterProtocol(SCLimitTimeGiftInfo2, "OnSCLimitTimeGiftInfo2")

	self:RegisterProtocol(CSPopupGiftClientReq)
	self:RegisterProtocol(SPopupGiftAllInfo, "OnSPopupGiftAllInfo")
	self:RegisterProtocol(SPopupGiftUpdateInfo, "OnSPopupGiftUpdateInfo")
	
end

function LimitTimeGiftWGCtrl:MainuiOpenCreateCallBack()
	self:CheckActIsOpen()
	self:CheckGiftPassTip()
	self:CheckLimitTimeGiftPurchaseIsOpen()
end

function LimitTimeGiftWGCtrl:CheckActIsOpen()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.LIMIT_TIME_GIFT)
	local role_level = RoleWGData.Instance:GetAttr("level")
	if not act_cfg or role_level < act_cfg.level or role_level > act_cfg.level_max then
		return
	end

	local gift_info_list = self.data:GetGiftInfoList()
	local status = ACTIVITY_STATUS.CLOSE
	local end_time = 0
	if gift_info_list then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		for _,v in pairs(gift_info_list) do
			if v.gift_timestamp > server_time and v.can_buy_num and v.can_buy_num > 0 then
				if v.gift_timestamp < end_time or end_time == 0 then -- 取最短的计时
					end_time = v.gift_timestamp
					status = ACTIVITY_STATUS.OPEN
				end
			end
		end
	end
	-- ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.LIMIT_TIME_GIFT, status, end_time, nil, end_time)
	LimitTimeGiftWGData.Instance:SetActStatus(status)
	if status == ACTIVITY_STATUS.OPEN and end_time > 0 then
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.LIMIT_TIME_GIFT, end_time, act_cfg)
	else
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.LIMIT_TIME_GIFT, false)
	end
end

function LimitTimeGiftWGCtrl:OnSCLimitTimeGiftInfo(protocol)
	self.data:SetGiftInfoList(protocol)
	self.view:Flush(0, "gift_info", {protocol_id = 8300})
	self:CheckActIsOpen()
	RemindManager.Instance:Fire(RemindName.LimitTimeGift)
end

function LimitTimeGiftWGCtrl:OnSCLimitTimeChangeGiftInfo(protocol)
	local need_open = false
	local info_list = protocol.gift_info_list
	for k,v in pairs(info_list) do
		local gift_cfg = LimitTimeGiftWGData.Instance:GetGiftCfgByGiftID(v.gift_id)
		local old_gift_info = LimitTimeGiftWGData.Instance:GetGiftInfoByGiftId(v.gift_id)
		if gift_cfg and gift_cfg.is_eject and gift_cfg.is_eject == 1 and not old_gift_info then
			-- 限时礼包加个触发后是否立即弹到脸上 打开界面
			need_open = true
			break
		end
	end

	self.data:SetChangeGiftInfoList(protocol)
	self.view:Flush(0, "gift_info", {protocol_id = 8302})
	self:CheckActIsOpen()
	RemindManager.Instance:Fire(RemindName.LimitTimeGift)

	if need_open then
		ViewManager.Instance:Open(GuideModuleName.LimitTimeGift)
	end
end

function LimitTimeGiftWGCtrl:OnSCLimitTimeGiftInfo2(protocol)
	self.data:SetChangeGiftInfoList(protocol)
	self.view:Flush(0, "gift_info", {protocol_id = 8304})
	self:CheckActIsOpen()
	RemindManager.Instance:Fire(RemindName.LimitTimeGift)
end

function LimitTimeGiftWGCtrl:OnSCLimitTimeGiftDelay(protocol)
	self:OpenGiftPassTimeTip(protocol.delay_min, true)
end

function LimitTimeGiftWGCtrl:SendBuyGift(opera_type, gift_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLimitTimeGiftReq)
	protocol.opera_type = opera_type or 0
	protocol.gift_id = gift_id or -1
	protocol:EncodeAndSend()
end

function LimitTimeGiftWGCtrl:CheckGiftPassTip()
	local status = LimitTimeGiftWGData.Instance:GetActStatus()
	if status == ACTIVITY_STATUS.CLOSE then
		return
	end
	local gift_info_list = self.data:GetGiftInfoList()
	if not gift_info_list then
		return
	end
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then
		return
	end
	if self.view:IsOpen() or self.tip_view:IsOpen() then
		return
	end

	local need_tip = false
	local count_down_time = 0
	local next_pass_time = 0
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local tip_time = LimitTimeGiftWGData.Instance:GetPassTipTime()
	local sub_time = 0
	local temp_gift_id = nil
	for _,v in pairs(gift_info_list) do
		sub_time = v.gift_timestamp - server_time
		if sub_time > 0 then
			if v.can_buy_num and v.can_buy_num > 0 then
				if sub_time < tip_time then
					temp_gift_id = self.data:GetGiftTrueId(v.gift_id, v.index)
					if self.data:GetGiftRemindByID(temp_gift_id, true) then
						need_tip = true
						if sub_time < count_down_time or count_down_time == 0 then
							count_down_time = sub_time
						end
						self.data:SetGiftRemindByID(temp_gift_id, true)
					end
				elseif sub_time < next_pass_time or next_pass_time == 0 then
					next_pass_time = sub_time
				end
			end
		end
	end

	CountDownManager.Instance:RemoveCountDown("limit_time_gift_pass_time_tip")
	if next_pass_time > 0 then
		CountDownManager.Instance:AddCountDown("limit_time_gift_pass_time_tip", nil, BindTool.Bind(self.CheckGiftPassTip, self), nil, next_pass_time - tip_time)
	end

	if need_tip and count_down_time > 10 then -- 至少10s不然点过去就没了
		self:OpenGiftPassTimeTip(count_down_time, false)
	end
end

function LimitTimeGiftWGCtrl:OpenGiftPassTimeTip(delay_time, is_online)
	local status = self.data:GetActStatus()
	if status ~= ACTIVITY_STATUS.OPEN then
		return
	end

	if not LimitTimeGiftWGData.Instance:HasCanBuyGift() then
		return
	end

	self.tip_view:Flush(0, "gift_info", {delay_time = delay_time, is_online = is_online})
	self.tip_view:Open()
end

---------------------------------------限时直购礼包--------------------------------------------
-- 限时直购礼包请求操作
function LimitTimeGiftWGCtrl:SendPopupGiftClientReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPopupGiftClientReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	--print_error("限时直购礼包请求操作", protocol)
	protocol:EncodeAndSend()
end

-- 限时直购礼包信息
function LimitTimeGiftWGCtrl:OnSPopupGiftAllInfo(protocol)
	--print_error("限时直购礼包信息", protocol)
	self.data:RefreshPopupGiftAllData(protocol)
	self:CheckLimitTimeGiftPurchaseIsOpen()
	self:FlushLimitTimeGiftPurchaseView()
	ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_suit)
end

-- 限时直购礼包信息
function LimitTimeGiftWGCtrl:OnSPopupGiftUpdateInfo(protocol)
	--print_error("限时直购礼包单个信息", protocol)
	self.data:RefreshPopupGiftOneData(protocol)
	self:CheckLimitTimeGiftPurchaseIsOpen()
	self:FlushLimitTimeGiftPurchaseView()
	ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_suit)
end

-- 检测是否需要请求灵玉不足直购操作
function LimitTimeGiftWGCtrl:CheckNoGoldPopupGift()
	local can_send, gift_grade = self.data:CheckNeedNoGoldPopupGift()
	--print_error("检测金币不足礼包开启", can_send, gift_grade)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测是否需要请求道具不足直购操作
function LimitTimeGiftWGCtrl:CheckOpenViewPopupGift(act_id)
	local can_send, gift_grade = self.data:CheckNeedOpenViewPopupGift(act_id)
	--print_error("检测活动礼包开启", act_id, can_send, gift_grade)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测是否需要请求修为道具不足直购操作
function LimitTimeGiftWGCtrl:CheckNeedCultivationPopupGift()
	local can_send, gift_grade = self.data:CheckNeedCultivationPopupGift()
	-- print_error("检测修为道具不足礼包开启", can_send, gift_grade)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测是否需要请求金币不足直购操作
function LimitTimeGiftWGCtrl:CheckNoGoldPopupGift2()
	local can_send, gift_grade = self.data:CheckNeedNoGoldPopupGift2()
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测直购礼包是否满足抽奖道具不足的条件
function LimitTimeGiftWGCtrl:CheckNeedDrawRewardPopupGift(popup_draw_type)
	local can_send, gift_grade = self.data:CheckNeedDrawRewardPopupGift(popup_draw_type)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测装备合鸣是否满足消耗道具不足得条件
function LimitTimeGiftWGCtrl:CheckNeedEquipSuitHarmonyPopupGift(target_gift_grade)
	local can_send, gift_grade = self.data:CheckNeedEquipSuitHarmonyPopupGift(target_gift_grade)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测是否需要请求修为道具不足直购操作
function LimitTimeGiftWGCtrl:CheckNeedFishionGoldPopupGift()
	local can_send, gift_grade = self.data:CheckNeedFishionGoldPopupGift()
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测是否首次抽奖.
function LimitTimeGiftWGCtrl:CheckAfterDrawPopupGift()
	local can_send, gift_grade = self.data:CheckAfterDrawPopupGift()
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

--检测转职失败
function LimitTimeGiftWGCtrl:CheckZhuanZhiPopupGift()
	local can_send, gift_grade = self.data:CheckZhuanZhiPopupGift()
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 打开限时礼包直购
function LimitTimeGiftWGCtrl:OpenLimitTimeGiftPurchaseView()
	self.limit_time_gift_purchase_view:SetJumpStatus()
	if self.limit_time_gift_purchase_view:IsOpen() then
		self.limit_time_gift_purchase_view:Flush()
	else
		self.limit_time_gift_purchase_view:Open()
	end
end

-- 刷新限时礼包直购
function LimitTimeGiftWGCtrl:FlushLimitTimeGiftPurchaseView()
	if self.limit_time_gift_purchase_view:IsOpen() then
		self.limit_time_gift_purchase_view:Flush()
	end
end

-- 关闭限时礼包直购
function LimitTimeGiftWGCtrl:CloseLimitTimeGiftPurchaseView()
	if self.limit_time_gift_purchase_view:IsOpen() then
		self.limit_time_gift_purchase_view:Close()
	end
end

-- 设置活动开启状态
function LimitTimeGiftWGCtrl:CheckLimitTimeGiftPurchaseIsOpen()
	local end_time = self.data:GetPopupGiftEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local is_time_out = server_time > end_time
	local is_can_buy = self.data:CkeckCurrGradeCanBuy()
	local act_type = ACTIVITY_TYPE.LIMIT_TIME_GIFT_PURCHASE

	if end_time > 0 and is_can_buy and (not is_time_out) then
		self:OpenLimitTimeGiftPurchaseView()
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, false)
		ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.OPEN, end_time, 0, 0)
	else
		self:CloseLimitTimeGiftPurchaseView()
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, false)
		ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.CLOSE)
	end
end