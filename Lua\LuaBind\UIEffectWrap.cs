﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UIEffectWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UIEffect), typeof(UnityEngine.UI.MaskableGraphic));
		<PERSON><PERSON>RegFunction("GetTarget", GetTarget);
		<PERSON><PERSON>unction("SetOverrideOrder", SetOverrideOrder);
		<PERSON><PERSON>unction("SetClipRect", SetClipRect);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("IsIgnoreTimeScale", get_IsIgnoreTimeScale, set_IsIgnoreTimeScale);
		<PERSON>.<PERSON>ar("isOffZTest", get_isOffZTest, set_isOffZTest);
		L.Reg<PERSON>ar("orderOffset", get_orderOffset, set_orderOffset);
		L.<PERSON>Class();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIEffect obj = (UIEffect)ToLua.CheckObject<UIEffect>(L, 1);
			UnityEngine.GameObject o = obj.GetTarget();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOverrideOrder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			UIEffect obj = (UIEffect)ToLua.CheckObject<UIEffect>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			int arg3;
			obj.SetOverrideOrder(arg0, arg1, arg2, out arg3);
			LuaDLL.lua_pushinteger(L, arg3);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetClipRect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UIEffect obj = (UIEffect)ToLua.CheckObject<UIEffect>(L, 1);
			UnityEngine.Rect arg0 = StackTraits<UnityEngine.Rect>.Check(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.SetClipRect(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsIgnoreTimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIEffect obj = (UIEffect)o;
			bool ret = obj.IsIgnoreTimeScale;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsIgnoreTimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isOffZTest(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIEffect obj = (UIEffect)o;
			bool ret = obj.isOffZTest;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOffZTest on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_orderOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIEffect obj = (UIEffect)o;
			int ret = obj.orderOffset;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index orderOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsIgnoreTimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIEffect obj = (UIEffect)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsIgnoreTimeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsIgnoreTimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isOffZTest(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIEffect obj = (UIEffect)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isOffZTest = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isOffZTest on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_orderOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIEffect obj = (UIEffect)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.orderOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index orderOffset on a nil value");
		}
	}
}

