DragonTempleWildBuyView = DragonTempleWildBuyView or BaseClass(SafeBaseView)

function DragonTempleWildBuyView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_dragon_wild_buy")
end

function DragonTempleWildBuyView:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.reward_list then
		self.reward_list = AsyncListView.New(DragonRewaedRender, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end

    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.skill_show_btn, BindTool.Bind1(self.OnClickSkillShow, self))
    XUI.AddClickEventListener(self.node_list["left_btn"], BindTool.Bind(self.OnClickPanelShow, self, -1))
    XUI.AddClickEventListener(self.node_list["right_btn"], BindTool.Bind(self.OnClickPanelShow, self, 1))
end

function DragonTempleWildBuyView:ReleaseCallBack()
    self.cur_show_seq = nil
    self.cur_show_data = nil

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function DragonTempleWildBuyView:OnFlush()
    local list = DragonTempleWGData.Instance:GetShowWildBuyList()
    if IsEmptyTable(list) then
        return
    end

    for i, v in ipairs(list) do
        local is_buy = DragonTempleWGData.Instance:GetWildIsBuyFlag(v.seq)
        if not is_buy then
            self.cur_show_seq = i
            break
        end
    end

    self.cur_show_seq =  self.cur_show_seq or 1
    self.cur_show_data = list[self.cur_show_seq]

    self:FlushModel()
    self:FlushPanel()
end

function DragonTempleWildBuyView:FlushPanel()
    if not self.cur_show_data then
        return
    end

    self.reward_list:SetDataList(self.cur_show_data.reward_item)
    local price = RoleWGData.GetPayMoneyStr(self.cur_show_data.price, self.cur_show_data.rmb_type, self.cur_show_data.rmb_seq)
    self.node_list.btn_description.text.text = self.cur_show_data.price_desc
    self.node_list.buy_price.text.text = price --购买价格

    local is_buy = DragonTempleWGData.Instance:GetWildIsBuyFlag(self.cur_show_data.seq)
    XUI.SetButtonEnabled(self.node_list["buy_btn"], not is_buy)

    local list = DragonTempleWGData.Instance:GetShowWildBuyList()
    local bundle, asset = ResPath.GetRawImagesPNG("a2_skill_description_" .. self.cur_show_data.mount_seq + 1)
    self.node_list.skill_description.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.skill_description.raw_image:SetNativeSize()
    end)
    self.node_list["left_btn"]:CustomSetActive(self.cur_show_seq > 1)
   	self.node_list["right_btn"]:CustomSetActive(self.cur_show_seq < #list)
end

function DragonTempleWildBuyView:FlushModel()
    if not self.cur_show_data then
        return
    end

    local display_data = {}
    display_data.should_ani = true
    if self.cur_show_data.model_show_itemid ~= 0 and self.cur_show_data.model_show_itemid ~= "" then
        local split_list = string.split(self.cur_show_data.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = self.cur_show_data.model_show_itemid
        end
    end
    
    display_data.hide_model_block = true
    display_data.bundle_name = self.cur_show_data["model_bundle_name"]
    display_data.asset_name = self.cur_show_data["model_asset_name"]
    local model_show_type = tonumber(self.cur_show_data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

    self.model_display:SetData(display_data)
    local scale = self.cur_show_data["display_scale"]
    Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
    if self.cur_show_data.display_pos and self.cur_show_data.display_pos ~= "" then
        local pos_list = string.split(self.cur_show_data.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.display_model.rect, pos_x, pos_y)

    if self.cur_show_data.rotation and self.cur_show_data.rotation ~= "" then
        local rotation_tab = string.split(self.cur_show_data.rotation,"|")
        self.node_list["display_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
    end
end

function DragonTempleWildBuyView:OnClickBuyBtn()
    if not self.cur_show_data then
        return
    end

    local is_buy = DragonTempleWGData.Instance:GetWildIsBuyFlag(self.cur_show_data.seq)
    if is_buy then
        return
    end

    RechargeWGCtrl.Instance:Recharge(self.cur_show_data.price, self.cur_show_data.rmb_type, self.cur_show_data.rmb_seq)
end

function DragonTempleWildBuyView:OnClickSkillShow()
    if not self.cur_show_data then
        return
    end

    local mount_seq = self.cur_show_data.mount_seq
    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(mount_seq)
    local data = {}
    data.mount_seq = mount_seq
    data.skill_level = mount_skill_level > 0 and mount_skill_level or 1

    self:Close()
    CommonSkillShowCtrl.Instance:SetFightMountSkillViewDataAndOpen(data)
end

function DragonTempleWildBuyView:OnClickPanelShow(dir)
    local list = DragonTempleWGData.Instance:GetShowWildBuyList()
    local max_num = #list
    local select_seq = self.cur_show_seq + dir
    select_seq = math.min(select_seq, max_num)
    select_seq = math.max(1, select_seq)

    self.cur_show_seq = select_seq
    self.cur_show_data = list[select_seq]

    self:FlushModel()
    self:FlushPanel()
end

DragonRewaedRender = DragonRewaedRender or BaseClass(BaseRender)

function DragonRewaedRender:LoadCallBack()
    if not self.reward_item_cell then
        self.reward_item_cell = ItemCell.New(self.node_list["item_pos"])
    end
end

function DragonRewaedRender:__delete()
    if self.reward_item_cell then
        self.reward_item_cell:DeleteMe()
        self.reward_item_cell = nil
    end
end

function DragonRewaedRender:OnFlush()
    if self.data == nil then
        return
    end

    self.reward_item_cell:SetData(self.data)
end