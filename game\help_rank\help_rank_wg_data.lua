HelpRankWGData = HelpRankWGData or BaseClass()

function HelpRankWGData:__init()
	if HelpRankWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[HelpRankWGData] attempt to create singleton twice!")
		return
	end

	HelpRankWGData.Instance = self

	self:InitCfg()
	self.grade = 1
	self.shop_list = {}
	self.all_buy_flag = 0
end

function HelpRankWGData:__delete()
	HelpRankWGData.Instance = nil
	self.grade = nil
	self.shop_list = nil

	RemindManager.Instance:UnRegister(RemindName.HelpRankRed)
end

function HelpRankWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_help_rank_auto")
    self.shop_cfg = ListToMapList(cfg.shop, "grade", "activity_day")
	self.purchase_shop_cfg = ListToMapList(cfg.shop, "grade", "activity_day", "rank_seq")
	self.lingyu_shop_cfg = ListToMapList(cfg.lingyu_shop, "grade", "activity_day", "rank_seq")
	self.score_shop_cfg = ListToMapList(cfg.score_shop, "grade", "activity_day", "rank_seq")
	self.all_buy_cfg =  ListToMapList(cfg.buy_all, "grade", "activity_day")
	RemindManager.Instance:Register(RemindName.HelpRankRed, BindTool.Bind(self.HelpRankRedShow, self))
end

function HelpRankWGData:SetAllInfo(protocol)
	self.grade = protocol.grade
    self.every_day_flag = protocol.every_day_flag
	self.lingyu_shop_list = protocol.lingyu_shop_list
	self.score_shop_list = protocol.score_shop_list
	self.shop_list = protocol.shop_list
	self.all_buy_flag = protocol.all_buy_flag or self.all_buy_flag
end

function HelpRankWGData:GetFreeRewardFlag()
	if self.every_day_flag then
		return self.every_day_flag 
	end
	return 0
end

-- 首次才有红点
function HelpRankWGData:GetHelpRankOpenFlag()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
    local set_key = "help_rank_open_flag" .. role_id
	return PlayerPrefsUtil.GetInt(set_key) == 1
end

function HelpRankWGData:SetHelpRankOpenFlag()
	if self:GetHelpRankOpenFlag() then
		return
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
    local set_key = "help_rank_open_flag" .. role_id
	PlayerPrefsUtil.SetInt(set_key, 1)
end

function HelpRankWGData:GetLingYuShopBuyData(rank_seq)
	return self.lingyu_shop_list[rank_seq] or {}
end

-- 获取积分商店商品购买次数
function HelpRankWGData:GetScoreShopBuyCountBySeq(rank_seq, seq)
	return (self.score_shop_list[rank_seq] or {})[seq] or 0
end

-- 获取直购商店商品购买次数
function HelpRankWGData:GetShopBuyCountBySeq(rank_seq, seq)
	return (self.shop_list[rank_seq] or {})[seq] or 0
end

function HelpRankWGData:GetCurAllBuyShopCfg(rank_seq)
	local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local cur_day_cfg = self.all_buy_cfg[self.grade] and self.all_buy_cfg[self.grade][cur_day] or {}
	--local show_cfg = cur_day_cfg and cur_day_cfg[#cur_day_cfg] or {}
	if not IsEmptyTable(cur_day_cfg) then
		for i, v in ipairs(cur_day_cfg) do
			if v.rank_seq == rank_seq then
				return v
			end
		end
	end
	return {}
end

function HelpRankWGData:GetCurShopIsAllBuy()
	local all_buy = self.all_buy_flag == 1
	-- local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	-- local cur_day_cfg = self.shop_cfg[self.grade] and self.shop_cfg[self.grade][cur_day] or {}
	-- if not IsEmptyTable(cur_day_cfg) then
	-- 	for i, v in ipairs(cur_day_cfg) do
	-- 		local buy_count = HelpRankWGData.Instance:GetShopBuyCountBySeq(v.rank_seq, v.seq)
	-- 		local is_not_buy = buy_count < v.buy_limit
	-- 		if is_not_buy then
	-- 			all_buy = false
	-- 			break
	-- 		end	
	-- 	end
	-- end

	return all_buy
end

function HelpRankWGData:GetCurShopTitle()
	local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local cur_day_cfg = self.shop_cfg[self.grade] and self.shop_cfg[self.grade][cur_day] or {}
	return cur_day_cfg[1] and cur_day_cfg[1].rank_show_image or ""
end
-------------------------------------------------------------
--灵玉商店
function HelpRankWGData:GetCurLingYuShopCfg()
	local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local cur_day_cfg = self.lingyu_shop_cfg[self.grade] and self.lingyu_shop_cfg[self.grade][cur_day] or {}
	return cur_day_cfg
end

--直购商店
function HelpRankWGData:GetCurPurchaseShopCfg()
	local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local cur_day_cfg = self.purchase_shop_cfg[self.grade] and self.purchase_shop_cfg[self.grade][cur_day] or {}
	return cur_day_cfg
end

--积分商店
function HelpRankWGData:GetCurScoreShopCfg()
	local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local cur_day_cfg = self.score_shop_cfg[self.grade] and self.score_shop_cfg[self.grade][cur_day] or {}
	return cur_day_cfg
end

--灵玉商店
function HelpRankWGData:GetCurLingYuShopCfgByRankSeq(rank_seq)
	local cur_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	local cur_day_cfg = self.lingyu_shop_cfg[self.grade] and self.lingyu_shop_cfg[self.grade][cur_day] or {}
	return cur_day_cfg[rank_seq] or {}
end

-- 全部商店[废弃]
function HelpRankWGData:GetAllShopCfg()
	--local lingyu_shop_cfg = self:GetCurLingYuShopCfg()
	local purchase_shop_cfg = self:GetCurPurchaseShopCfg()
	if IsEmptyTable(purchase_shop_cfg) then
		return
	end
	--[[
	for i, v in ipairs(lingyu_shop_cfg) do
		v.sort_value = v[1].rank_seq * i * 1000 + 2  --灵玉商店排在直购后面
		table.insert(show_all_cfg, v)
	end
	for i, v in ipairs(purchase_shop_cfg) do
		v.sort_value = v[1].rank_seq * i * 1000 + 1  --灵玉商店排在直购后面
		table.insert(show_all_cfg, v)
	end
	table.sort(show_all_cfg, SortTools.KeyLowerSorter("sort_value"))
	return show_all_cfg or {}
	]]
	
	local show_cfg = {}
	for i, v in pairs(purchase_shop_cfg) do
		show_cfg[i] = {}
		show_cfg[i].purchase_shop_cfg = v
		show_cfg[i].lingyu_shop_cfg = self:GetCurLingYuShopCfgByRankSeq(v[1].rank_seq)
	end
	return show_cfg or {}
end

-- 全部商店(新)
function HelpRankWGData:GetAllShopCfgGroup()
	local purchase_shop_cfg = self:GetCurPurchaseShopCfg()
	local lingyu_shop_cfg = self:GetCurLingYuShopCfg()
	local score_shop_cfg = self:GetCurScoreShopCfg()

	local shop_cfg_group = {}
	local merge_cfg = function(shop_cfg)
		for i, v in ipairs(shop_cfg) do
			local rank_seq = v[1].rank_seq
			if not shop_cfg_group[rank_seq] then
				shop_cfg_group[rank_seq] = {}
				shop_cfg_group[rank_seq].rank_seq = rank_seq
				shop_cfg_group[rank_seq].rank_name = v[1].rank_name
			end
			local data = self:GetDataByCfg(v)
			shop_cfg_group[rank_seq][v[1].type] = data
		end
	end

	-- 只保留直购
	merge_cfg(purchase_shop_cfg)
	-- merge_cfg(lingyu_shop_cfg)
	-- merge_cfg(score_shop_cfg)

	--排序
	local shop_cfg = {}
	for k, v in pairs(shop_cfg_group) do
		table.insert(shop_cfg, v)
	end
	table.sort(shop_cfg, SortTools.KeyLowerSorter("rank_seq"))
	return shop_cfg or {}
end

function HelpRankWGData:GetDataByCfg(cfg)
	local data_list = {}
	for k, v in pairs(cfg) do
		local data = {}
		local is_buy = false

		if v.type == HELP_RANK_SHOP_TYPE.LINGYU then
			local lingyu_buy_data = HelpRankWGData.Instance:GetLingYuShopBuyData(v.rank_seq)
			is_buy = v.seq < lingyu_buy_data.seq -- 是否已购买
		elseif v.type == HELP_RANK_SHOP_TYPE.PURCHASE then
			local buy_count = HelpRankWGData.Instance:GetShopBuyCountBySeq(v.rank_seq, v.seq)
			is_buy = buy_count >= v.buy_limit
		elseif v.type == HELP_RANK_SHOP_TYPE.SCORE then
			local buy_count = HelpRankWGData.Instance:GetScoreShopBuyCountBySeq(v.rank_seq, v.seq)
			is_buy = buy_count >= v.buy_limit
		end

		data.cfg = v
		data.is_buy = is_buy
		data.sort = is_buy and (100 + k) or k
		table.insert(data_list, data)
	end

	table.sort(data_list, SortTools.KeyLowerSorter("sort"))
	return data_list
end

-------------------------------------------------------------

function HelpRankWGData:HelpRankRedShow()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)

	if not self:GetHelpRankOpenFlag() and is_open then
		return 1
	else
		return 0
	end
end