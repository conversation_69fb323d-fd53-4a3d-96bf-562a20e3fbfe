require("game/serveractivity/we_get_married/we_getmarried_wg_data")

------------咱们结婚吧----------------
WeGetMarriedWGCtrl = WeGetMarriedWGCtrl or BaseClass(BaseWGCtrl)

function WeGetMarriedWGCtrl:__init()
	if WeGetMarriedWGCtrl.Instance ~= nil then
		Error<PERSON><PERSON>("[WeGetMarriedWGCtrl] attempt to create singleton twice!")
		return
	end
	WeGetMarriedWGCtrl.Instance = self
	self.data = WeGetMarriedWGData.New()
	-- self.view = WeGetMarriedView.New()

	self:RegisterAllProtocols()
end

function WeGetMarriedWGCtrl:__delete()
	-- if nil ~= self.view then
	-- 	self.view:DeleteMe()
	-- 	self.view = nil
	-- end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	WeGetMarriedWGCtrl.Instance = nil
end

function WeGetMarriedWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossRADingjiWeddingFetchReward)										 -- 领取奖励请求
	self:RegisterProtocol(SCCrossRADingjiWeddingInfo, "OnCrossRADingjiWeddingInfo")					 -- 领取奖励标记
end

function WeGetMarriedWGCtrl:Open()
	-- self.view:Open()
	RankWGCtrl.Instance:SendCrossGetRankListReq(4)
end

function WeGetMarriedWGCtrl:OnCrossRADingjiWeddingInfo(protocol)
	self.data:SetRewardFlag(protocol.reward_flag)
	-- self.view:Flush()
end

function WeGetMarriedWGCtrl:SetRankListData(data)
	self.data:SetRankListData(data)
	-- self.view:Flush()
end

function WeGetMarriedWGCtrl:SendCrossRADingjiWeddingFetchReward()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossRADingjiWeddingFetchReward)
	protocol:EncodeAndSend()
end
