-- F-副本-采集.xls

return {
dungeon={
{},
{dungeon_id=2,}
},

dungeon_meta_table_map={
},
gather_refresh_cfg={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{dungeon_id=2,},
{},
{},
{},
{},
{},
{},
{},
{}
},

gather_refresh_cfg_meta_table_map={
[33]=32,	-- depth:1
[34]=33,	-- depth:2
[38]=34,	-- depth:3
[36]=38,	-- depth:4
[37]=36,	-- depth:5
[31]=37,	-- depth:6
[35]=31,	-- depth:7
[30]=35,	-- depth:8
[26]=30,	-- depth:9
[28]=26,	-- depth:10
[27]=28,	-- depth:11
[25]=27,	-- depth:12
[24]=25,	-- depth:13
[23]=24,	-- depth:14
[22]=23,	-- depth:15
[21]=22,	-- depth:16
[39]=21,	-- depth:17
[29]=39,	-- depth:18
[40]=29,	-- depth:19
},
level={
{},
{level=2,num=2400,},
{level=3,num=1600,},
{level=4,num=800,},
{level=5,num=0,},
{dungeon_id=2,},
{dungeon_id=2,},
{dungeon_id=2,},
{dungeon_id=2,},
{dungeon_id=2,}
},

level_meta_table_map={
[7]=2,	-- depth:1
[8]=3,	-- depth:1
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
dungeon_default_table={dungeon_id=1,scene_id=115,taskdone_num=15000,tip="收集灵石，交给太乙真人",tip_2="特大灵石出现啦！快去找找吧！",commit_countdown=30,commit_npc=1065,gather_radius=20,gather_range=50,item_name="灵石",commit_btn="上交",bundle="actors/gather/7221_prefab",asset=7221001,},

gather_refresh_cfg_default_table={dungeon_id=1,},

level_default_table={dungeon_id=1,level=1,num=3200,}

}

