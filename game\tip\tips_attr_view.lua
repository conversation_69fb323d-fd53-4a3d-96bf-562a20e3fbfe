TipsAttrView = TipsAttrView or BaseClass(SafeBaseView)

function TipsAttrView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function TipsAttrView:ReleaseCallBack()
	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end
		self.attr_list = nil
	end
end

--[[
local tips_data = {
	title_text,                        --名字.
	attr_data = { attr_str, attr_value }, --属性数据.
	is_need_space,                     --是否需要空格(默认需要).
	is_need_maohao,                    --是否需要冒号.
	prefix_text,                       --是否需要前缀.
	add_attr_data =                    --自定义加成属性.
	{
		{
			attr_str_id,
			attr_str,
			need_per
		},
		{
			attr_str_id,
			attr_str,
			need_per
		},
	}
}
--]]
function TipsAttrView:SetData(tips_data)
	if not tips_data then return end
	self.data = tips_data

	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function TipsAttrView:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}
		local parent_node = self.node_list["attr_list"]
		local attr_num = parent_node.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAttrRender.New(parent_node:FindObj("attr_" .. i))
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(self.data.is_need_space or true)
			cell:SetAttrNeedMaoHao(self.data.is_need_maohao or false)
			if self.data.prefix_text then
				cell:SetAttrValuePrefix(self.data.prefix_text)
			end
			self.attr_list[i] = cell
		end
	end
end

function TipsAttrView:OnFlush()
	self.node_list.attr_title_name.text.text = self.data.title_text or ""
	local has_attr_data = not IsEmptyTable(self.data.attr_data)

	self.node_list.attr_scroll:CustomSetActive(has_attr_data)
	self.node_list.img_no_record:CustomSetActive(not has_attr_data)

	if has_attr_data then
		local attr_data = self.data.attr_data
		local add_attr_data = self.data.add_attr_data
		for k, v in pairs(self.attr_list) do
			if add_attr_data then
				for k2, v2 in pairs(add_attr_data) do
					if attr_data[k] and attr_data[k].attr_str == v2.attr_str_id then
						v:SetNeedPer(v2.need_per or false)
						v:SetData(attr_data[k])
						v:ResetName(v2.attr_str)
					else
						v:SetData(attr_data[k])
					end
				end
			else
				v:SetData(attr_data[k])
			end
		end

		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["attr_scroll"].rect)
	end
end
