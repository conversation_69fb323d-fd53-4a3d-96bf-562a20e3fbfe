-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local Base = require "lib/resmanager/loader_base"
local libbit = require "bit"
local BundleCache = require "lib/resmanager/bundle_cache"
local develop_mode = require("editor/develop_mode")

local UnityApplication = UnityEngine.Application
local UnityGameObject = UnityEngine.GameObject
local UnityDestroy = UnityGameObject.Destroy
local UnityDownloadHandlerAssetBundle = UnityEngine.Networking.DownloadHandlerAssetBundle
local TypeUnityGameObject = typeof(UnityEngine.GameObject)
local UnityLoadSceneSync = UnityEngine.SceneManagement.SceneManager.LoadScene
local UnityLoadSceneAsync = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync
local SysFile = System.IO.File

local _sformat = string.format
local _tinsert = table.insert
local MAX_INSTANTIATE_COUNT = 4

local M = ResUtil.create_child_mt(Base)

-- 判断bundle_name是否lua的bundle
local function IsLuaAssetBundle(bundle_name)
	local lua_asset_bundles = {"^lua/.*", "^luajit/.*"}
	for i,v in ipairs(lua_asset_bundles) do
		if string.match(bundle_name, v) then
			return true
		end
	end

	return false
end

function M:_init()
	Base._init(self)

	self.v_lua_manifest_info = {bundleInfos = {}}
	self.v_manifest_info = {bundleInfos = {}}
	self.v_goid_prefab_map = {}
	self.v_goid_go_monitors = {}
	self.v_goid_go_monit_time = 0

	self.load_priority_type_list = {
		ResLoadPriority.sync,
		ResLoadPriority.faster_async_high,
		ResLoadPriority.faster_async_mid,
		ResLoadPriority.faster_async_low,
		ResLoadPriority.high,
		ResLoadPriority.mid,
		ResLoadPriority.low,
		ResLoadPriority.steal
	}
	-- 在剩余的可分配数量里能够分配到的比率，为1时则能分配到全部，只要大于0都会分配到至少1个，为0时则在没有任何加载时会配到1个
	self.load_priority_count_list = {1, 1, 1, 1, 0.9, 0.7, 0.1, 0}

	self.v_instantiate_count = 0
	self.v_priority_instantiate_queue = {}
	self.v_log_list = {}

	-- 初始化不同优先级下的列表
	for i,v in ipairs(self.load_priority_type_list) do
		self.v_priority_instantiate_queue[v] = {}
	end
end

function M:Update(time, delta_time)
	Base.Update(self, time, delta_time)

	self:_UpdateInstantiate()
	AssetBundleMgr:Update()
	DownloaderMgr:Update()
	self:MonitorGameObjLive(time)
end

function M:IsBundleMode()
	return true
end

function M:GetPrefab(instance_id)
	return self.v_goid_prefab_map[instance_id]
end

function M:OnHotUpdateLuaComplete()
	local src = ResUtil.GetCachePath("LuaAssetBundle/Temp/LuaAssetBundle.lua", nil)
	local dest = ResUtil.GetCachePath("LuaAssetBundle/LuaAssetBundle.lua", nil)
	local need_restart = self:NeedRestart(src, dest)

	if SysFile.Exists(src) then
		SysFile.Copy(src, dest, true)
		SysFile.Delete(src)
	end

	return need_restart
end

function M:NeedRestart(src, dest)
	-- 缓存中不存在时，读取包体里面的做对比
	if not SysFile.Exists(dest) then
		local manifest = self:GetPackageLuaManifest()
		if nil == manifest then
			print_error("[GetPackageLuaManifest] manifest is nil")
			return false
		else
			return manifest.manifestHashCode ~= self.v_lua_manifest_info.manifestHashCode
		end
	end

	local text = SysFile.ReadAllText(dest)
	if nil == text then
		return true
	end

	local data = loadstring(text)
	if nil == data then
		return true
	end

	local manifest = data()
	if nil == manifest then
		return true
	end

	return manifest.manifestHashCode ~= self.v_lua_manifest_info.manifestHashCode
end

function M:GetPackageLuaManifest()
	local text = ResUtil.LoadApkFileHelper("LuaAssetBundle/LuaAssetBundle.lua")
	if nil == text then
		return nil
	end

	local data = loadstring(text)
	if nil == data then
		return nil
	end

	local manifest = data()
	if nil == manifest then
		return nil
	end

	return manifest
end

function M:LoadRemoteLuaManifest(callback)
	local remote_path = self:GetRemotePath("LuaAssetBundle/LuaAssetBundle.zip", self.v_asset_lua_version) or ""
	local cache_path = ResUtil.GetCachePath("LuaAssetBundle/Temp/LuaAssetBundle.zip")
	print_log("[BundleLoader] remote lua manifest path:", remote_path)
	_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadRemoteLuaManifest, %s", remote_path))
	DownloaderMgr:CreateFileDownloader(remote_path, nil,
		function(err, request)
			-- 下载失败重试
			if nil ~= err then
				_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadRemoteLuaManifest fail, %s", remote_path))
				callback(err)
				return
			end

			-- 下载成功
			_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadRemoteLuaManifest succ, %s", remote_path))

			local is_unzip_succ = false
			local is_load_succ = false
			local is_version_match = false
			local is_project_match = false
			-- ReportManager:Step(Report.STEP_LUA_MANIFEST_UNZIP)

			-- 下载成功后解压文件到目录，并且加载
			local temp_dir = ResUtil.GetCachePath("LuaAssetBundle/Temp", nil)
			ZipUtils.UnZip(cache_path, temp_dir,
				function ()
					is_unzip_succ = true
					_tinsert(self.v_log_list, "[BundleLoader] LoadRemoteLuaManifest, unzip succ")

					-- 因为Unzip接口的原因，zipName是加密过的，而里面的name是没加密的，这里做个拷贝
					-- if ResUtil.is_ios_encrypt_asset then
					-- 	local src = ResUtil.GetCachePath("LuaAssetBundle/LuaAssetBundle.lua")
					-- 	local dest = ResUtil.GetCachePath("LuaAssetBundle/Temp/LuaAssetBundle.lua")
					-- 	SysFile.Copy(src, dest, true)
					-- end

					is_load_succ = self:LoadLocalLuaManifest("LuaAssetBundle/Temp/LuaAssetBundle.lua")
					if is_load_succ then
						is_version_match = self:CheckVersion(self.v_lua_manifest_info.manifestHashCode)

						if is_version_match then
							is_project_match = self:CheckProjectName(self.v_lua_manifest_info.projectName)
						end
					end
				end)

			-- 检查不通过，则将标记error，外部处理
			if not is_unzip_succ then
				err = _sformat("[BundleLoader] Big Bug!!! LoadRemoteLuaManifest, unzip fail, %s, %s", cache_path, temp_dir)
			elseif not is_load_succ then
				err = "[BundleLoader] Big Bug!!! LoadRemoteLuaManifest, load local manifest fail"
			elseif not is_version_match then
				err = "[BundleLoader] Big Bug!!! LoadRemoteLuaManifest, version is not match"
			elseif not is_project_match then
				err = "[BundleLoader] Big Bug!!! LoadRemoteLuaManifest, project_name is not match"
			end

 			-- 输出错误日志
			if nil ~= err then
				print_error(err)
				self:PrintLogList()
			end

			callback(err)

		end, cache_path)
end

-- 下载LuaManifest
-- 在线热更新专用接口
function M:RuntimeLoadRemoteLuaManifest(callback)
	local remote_path = self:GetRuntimeRemotePath("LuaAssetBundle/LuaAssetBundle.zip", self.v_asset_lua_version)
	local cache_path = ResUtil.GetCachePath("LuaAssetBundle/Temp/LuaAssetBundle.zip")
	print_log("[BundleLoader] remote lua manifest path:", remote_path)
	DownloaderMgr:CreateFileDownloader(remote_path, nil,
		function(err, request)
			if nil == err then
				-- ReportManager:Step(Report.STEP_LUA_MANIFEST_UNZIP)
				local temp_dir = ResUtil.GetCachePath("LuaAssetBundle/Temp", nil)
				ZipUtils.UnZip(cache_path, temp_dir,
					function ()
					-- 因为Unzip接口的原因，zipName是加密过的，而里面的name是没加密的，这里做个拷贝
						-- if ResUtil.is_ios_encrypt_asset then
						-- 	local src = temp_dir .. ResUtil.GetCachePath("LuaAssetBundle.lua")
						-- 	local dest = ResUtil.GetCachePath("LuaAssetBundle/Temp/LuaAssetBundle.lua")
						-- 	SysFile.Copy(src, dest, true)
						-- end

						self:LoadLocalLuaManifest("LuaAssetBundle/Temp/LuaAssetBundle.lua")
					end)
			end
			callback(err, request)
		end, cache_path)
end

function M:LoadRemoteManifest(name, callback)
	local name_zip = name .. ".zip"
	local name_lua = name .. ".lua"

	local remote_path = self:GetRemotePath(name_zip, self.v_asset_version) or ""
	local cache_path = ResUtil.GetCachePath(name_zip)

	print_log("[BundleLoader] load remote manifest:", remote_path)
	_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadRemoteManifest, %s", remote_path))
	DownloaderMgr:CreateFileDownloader(remote_path, nil,
		function(err, request)
			if nil ~= err then
				_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadRemoteManifest fail, %s", remote_path))
				callback(err)
				return
			end

			local is_unzip_succ = false
			local is_load_succ = false
			local is_version_match = false
			local is_project_match = false

			-- ReportManager:Step(Report.STEP_MANIFEST_UNZIP)
			_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadRemoteManifest succ, %s", remote_path))
			ZipUtils.UnZip(cache_path, ResUtil.GetBaseCachePath(),
				function ()
					is_unzip_succ = true
					_tinsert(self.v_log_list, "[BundleLoader] LoadRemoteManifest, unzip succ")

					-- 因为Unzip接口的原因，zipName是加密过的，而里面的name是没加密的，这里做个拷贝(把没加密的名字改成加密后的名字)
					-- if ResUtil.is_ios_encrypt_asset then
					-- 	local en_name_lua = ResUtil.GetCachePath(name_lua)
					-- 	local src = ResUtil.GetBaseCachePath() .. "/" .. en_name_lua
					-- 	local dest = en_name_lua
					-- 	SysFile.Copy(src, dest, true)
					-- end

					is_load_succ = self:LoadLocalManifest(name_lua)
					if is_load_succ then
						is_version_match = self:CheckVersion(self.v_manifest_info.manifestHashCode)

						if is_version_match then
							is_project_match = self:CheckProjectName(self.v_manifest_info.projectName)
						end
					end
				end)

			-- 检查不通过，则将标记error，外部处理
			if not is_unzip_succ then
				err = _sformat("[BundleLoader] Big Bug!!! LoadRemoteManifest, unzip fail, %s, %s", cache_path, ResUtil.GetBaseCachePath())
			elseif not is_load_succ then
				err = "[BundleLoader] Big Bug!!! LoadRemoteManifest, load local manifest fail"
			elseif not is_version_match then
				err = "[BundleLoader] Big Bug!!! LoadRemoteManifest, version is not match"
			elseif not is_project_match then
				err = "[BundleLoader] Big Bug!!! LoadRemoteManifest, project_name is not match"
			end

			-- 输出错误日志
 			if nil ~= err then
				print_error(err)
				self:PrintLogList()
			end

			callback(err)
		end, cache_path)
end

-- 注意：增加日志观察加载下来的资源版本号跟php传过来的是否一致
-- (连内网服肯定是不一致的，因为query.php的版本号没有运维去手动更改)
function M:CheckVersion(version)
	if UnityEngine.Debug.isDebugBuild or "" == self.v_asset_version then
		return true
	end

	if nil == self.v_asset_version then
		_tinsert(self.v_log_list, "manifest version is not same, asset_version is nil")
		return false
	end

	if nil == version then
		_tinsert(self.v_log_list, "manifest version is not same, version is nil")
		return false
	end

	if nil == string.find(self.v_asset_version, version) then
		_tinsert(self.v_log_list, string.format("[BundleLoader] Big Bug!!!, manifest version is not same!, %s, %s", self.v_asset_version, version))
		return false
	end

	return true
end

-- 观察下载下来的资源project_name是否与包体内一致
-- 例如，国服安卓资源，project_name="uc01#cn#android"
function M:CheckProjectName(project_name)
	local package_project_name = self:GetPackageProjectName()
	if nil == package_project_name or "" == package_project_name then
		return true
	end

	if nil == project_name or "" == project_name then
		_tinsert(self.v_log_list, "[BundleLoader] Big Bug!!!, project_name is nil")
		return false
	end

	if project_name ~= package_project_name then
		_tinsert(self.v_log_list, string.format("[BundleLoader] Big Bug!!!, project_name is not same!, %s, %s",
			package_project_name, project_name))

		return false
	end

	return true
end

-- 拿到包体里面的project_name
local packageProjectName = nil
function M:GetPackageProjectName()
	if nil ~= packageProjectName then
		return packageProjectName
	end

	local text = ResUtil.is_android_encrypt_asset
				and StreamingAssets.EncryptReadAllText("AssetBundle/" .. ResUtil.GetStreamingFileEncryptPath("AssetBundle.lua"), ResUtil.GetStreamingEncryptKey())
				or StreamingAssets.ReadAllText("AssetBundle/AssetBundle.lua")
	if nil == text then
		return
	end

	local data = loadstring(text)
	if nil == data then
		return
	end

	local manifest = data()
	if nil == manifest then
		return
	end

	packageProjectName = manifest.projectName or ""

	return packageProjectName
end

function M:PrintLogList()
	local log_str = ""
	for i,v in ipairs(self.v_log_list) do
		log_str = log_str .. v .. "\n"
	end
	print_error(log_str)
end

function M:LoadLocalLuaManifest(lua_manifest_file_name)
	print_log("[BundleLoader] load local lua manifest:", lua_manifest_file_name)
	lua_manifest_file_name = lua_manifest_file_name or ""
	local text = ResUtil.LoadFileHelper(lua_manifest_file_name)
	if nil == text then
		_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalLuaManifest fail, loadfile fail, %s", lua_manifest_file_name))
		return false
	end

	local data = loadstring(text)
	if nil == data then
		_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalLuaManifest, loadstring fail, %s", lua_manifest_file_name))
		return false
	end

	local manifest = data()
	if nil == manifest then
		_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalLuaManifest, execute fail, %s", lua_manifest_file_name))
		return false
	end

	self.v_lua_manifest_info = manifest
	_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalLuaManifest succ, %s", self.v_lua_manifest_info.manifestHashCode or ""))
	return true
end

function M:LoadLocalManifest(manifest_file_name)
	print_log("[BundleLoader] load local manifest:", manifest_file_name)
	manifest_file_name = manifest_file_name or ""
	local text = ResUtil.LoadFileHelper(manifest_file_name)
	if nil == text then
		_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalManifest fail, loadfile fail, %s", manifest_file_name))
		return false
	end

	local data = loadstring(text)
	if nil == data then
		_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalManifest fail, loadstring fail, %s", manifest_file_name))
		return false
	end

	local manifest = data()
	if nil == manifest then
		_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalManifest, execute fail, %s", manifest_file_name))
		return false
	end

	self.v_manifest_info = manifest
	_tinsert(self.v_log_list, _sformat("[BundleLoader] LoadLocalManifest succ, %s", self.v_manifest_info.manifestHashCode or ""))
	return true
end

function M:GetAllLuaManifestBundles()
	return self.v_lua_manifest_info.bundleInfos
end

function M:GetLuaBundleHash(bundle_name)
	if self.v_lua_manifest_info.bundleInfos[bundle_name] then
		return self.v_lua_manifest_info.bundleInfos[bundle_name].hash
	end
end

function M:GetLuaBundleSize(bundle_name)
	if self.v_lua_manifest_info.bundleInfos[bundle_name] then
		return self.v_lua_manifest_info.bundleInfos[bundle_name].size
	end
	return 0
end

function M:GetLuaHashCode()
	return self.v_lua_manifest_info.manifestHashCode
end

function M:GetAllManifestBundles()
	return self.v_manifest_info.bundleInfos
end

function M:GetBundleDeps(bundle_name)
	if self.v_manifest_info.bundleInfos[bundle_name] then
		return self.v_manifest_info.bundleInfos[bundle_name].deps
	end
end

function M:GetBundleHash(bundle_name)
	if self.v_manifest_info.bundleInfos[bundle_name] then
		return self.v_manifest_info.bundleInfos[bundle_name].hash
	end
end

function M:GetBundleCRC(bundle_name)
	if self.v_manifest_info.bundleInfos[bundle_name] then
		return self.v_manifest_info.bundleInfos[bundle_name].crc or 0
	end
	return 0
end

function M:GetBundleSize(bundle_name)
	if self.v_manifest_info.bundleInfos[bundle_name] then
		return self.v_manifest_info.bundleInfos[bundle_name].size
	end
end

function M:GetHashCode()
	if nil ~= self.v_manifest_info.manifestHashCode and nil ~= self.v_lua_manifest_info.manifestHashCode then
		return self.v_manifest_info.manifestHashCode .. self.v_lua_manifest_info.manifestHashCode
	end

	return ""
end

-- 异步加载unity场景
function M:LoadUnitySceneAsync(bundle_name, asset_name, load_mode, cb)
	local need_downloads, need_loads = self:ClacLoadBundleDepends(bundle_name, asset_name)
	if nil == need_downloads or nil == need_loads then
		cb(nil, cbdata, bundle_name)
		return
	end

	if ResUtil.memory_debug then
		BundleCache:CacheBundleRefDetail(bundle_name, "[Asset]" .. asset_name)
		for _, v in pairs(need_loads) do
			BundleCache:CacheBundleRefDetail(v, bundle_name)
		end
	end

	-- 下载AB
	AssetBundleMgr:DownLoadBundles(need_downloads, function (is_succ)
		if not is_succ then
			cb(nil)
			return
		end

		BundleCache:LockBundles(need_loads)

		-- 同步加载AB
		AssetBundleMgr:LoadMultiBundlesAsync(need_loads, function(is_succ)
			if not is_succ then
				BundleCache:UnLockBundles(need_loads)
				cb(nil)
				return
			end

			-- 加载场景
			local loadscene_op = UnityLoadSceneAsync(asset_name, load_mode)
			if nil == loadscene_op then
				print_error("[BundleLoader] unity scene is not exists", bundle_name, asset_name)
				BundleCache:UnLockBundles(need_loads)
				cb(nil)
				return
			end

			BundleCache:UnLockBundles(need_loads)
			self:UseBundle(bundle_name)
			cb(loadscene_op)
		end, ResLoadPriority.high)
	end, ResLoadPriority.high)
end

-- 同步加载unity场景
function M:LoadUnitySceneSync(bundle_name, asset_name, load_mode, cb)
	local need_downloads, need_loads = self:ClacLoadBundleDepends(bundle_name, asset_name)
	if nil == need_downloads or nil == need_loads then
		cb(false, cbdata, bundle_name)
		return
	end

	if ResUtil.memory_debug then
		BundleCache:CacheBundleRefDetail(bundle_name, "[Asset]" .. asset_name)
		for _, v in pairs(need_loads) do
			BundleCache:CacheBundleRefDetail(v, bundle_name)
		end
	end

	-- 下载AB
	AssetBundleMgr:DownLoadBundles(need_downloads, function(is_succ)
		if not is_succ then
			cb(false)
			return
		end

		BundleCache:LockBundles(need_loads)
		-- 同步加载AB
		AssetBundleMgr:LoadMultiBundlesSync(need_loads, function(is_succ)
			if not is_succ then
				BundleCache:UnLockBundles(need_loads)
				cb(false)
				return
			end

			-- 加载场景
			UnityLoadSceneSync(asset_name, load_mode)
			BundleCache:UnLockBundles(need_loads)
			self:UseBundle(bundle_name)
			cb(true)
		end)
	end, ResLoadPriority.sync)
end

local function LoadAssetCallBack(res, cbdata)
	local self = cbdata[1]
	local bundle_name = cbdata[3]
	local cb = cbdata[5]
	local up_cbdata = cbdata[6]
	local need_loads = cbdata[7]
	self:_ReleaseDownLoadBundlesCBData(cbdata)

	BundleCache:UnLockBundles(need_loads)
	cb(res, up_cbdata, bundle_name)
end

local function LoadMultiBundlesCallBack(is_succ, cbdata)
	local self = cbdata[1]
	local asset_type = cbdata[2]
	local bundle_name = cbdata[3]
	local asset_name = cbdata[4]
	local cb = cbdata[5]
	local up_cbdata = cbdata[6]
	local need_loads = cbdata[7]
	local load_priority = cbdata[8]
	local is_async = cbdata[9]

	if not is_succ then
		BundleCache:UnLockBundles(need_loads)
		cb(nil, up_cbdata, bundle_name)
		self:_ReleaseDownLoadBundlesCBData(cbdata)
		return
	end

	-- 加载Asset
	local load_func
	if is_async then
		load_func = AssetBundleMgr.LoadAssetAsync
	else
		load_func = AssetBundleMgr.LoadAssetSync
	end

	load_func(AssetBundleMgr, bundle_name, asset_name, LoadAssetCallBack, asset_type, load_priority, cbdata)
end

local function DownLoadBundlesCallBack(is_succ, cbdata)
	local self = cbdata[1]
	local bundle_name = cbdata[3]
	local cb = cbdata[5]
	local up_cbdata = cbdata[6]
	local need_loads = cbdata[7]
	local load_priority = cbdata[8]
	local is_async = cbdata[9]

	if not is_succ then
		cb(nil, up_cbdata, bundle_name)
		self:_ReleaseDownLoadBundlesCBData(cbdata)
		return
	end

	BundleCache:LockBundles(need_loads)
	local load_func
	-- 加载AB
	if load_priority >= ResLoadPriority.faster_async_low and load_priority <= ResLoadPriority.sync then
		load_func = AssetBundleMgr.LoadMultiBundlesSync
	else
		load_func = AssetBundleMgr.LoadMultiBundlesAsync
	end
	load_func(AssetBundleMgr, need_loads, LoadMultiBundlesCallBack, load_priority, cbdata)
end

-- 异步加载资源(请不要直接调用该方法,通过资源池来调)
function M:__LoadObjectAsync(bundle_name, asset_name, asset_type, cb, up_cbdata, load_priority)
	local need_downloads, need_loads = self:ClacLoadBundleDepends(bundle_name, asset_name)
	if nil == need_downloads or nil == need_loads then
		cb(nil, up_cbdata, bundle_name)
		return
	end

	if ResUtil.memory_debug then
		BundleCache:CacheBundleRefDetail(bundle_name, "[Asset]" .. asset_name)
		for _, v in pairs(need_loads) do
			BundleCache:CacheBundleRefDetail(v, bundle_name)
		end
	end

	local cbdata = self:_GetDownLoadBundlesCBData()
	cbdata[1] = self
	cbdata[2] = asset_type
	cbdata[3] = bundle_name
	cbdata[4] = asset_name
	cbdata[5] = cb
	cbdata[6] = up_cbdata
	cbdata[7] = need_loads
	cbdata[8] = load_priority
	cbdata[9] = true

	-- 下载AB
	AssetBundleMgr:DownLoadBundles(need_downloads, DownLoadBundlesCallBack, load_priority, cbdata)
end

-- 同步加载资源(请不要直接调用该方法,通过资源池来调)
function M:__LoadObjectSync(bundle_name, asset_name, asset_type, cb, up_cbdata)
	local need_downloads, need_loads = self:ClacLoadBundleDepends(bundle_name, asset_name)
	if nil == need_downloads or nil == need_loads then
		cb(nil, up_cbdata, bundle_name)
		return
	end

	if ResUtil.memory_debug then
		BundleCache:CacheBundleRefDetail(bundle_name, "[Asset]" .. asset_name)
		for _, v in pairs(need_loads) do
			BundleCache:CacheBundleRefDetail(v, bundle_name)
		end
	end

	local cbdata = self:_GetDownLoadBundlesCBData()
	cbdata[1] = self
	cbdata[2] = asset_type
	cbdata[3] = bundle_name
	cbdata[4] = asset_name
	cbdata[5] = cb
	cbdata[6] = up_cbdata
	cbdata[7] = need_loads
	cbdata[8] = ResLoadPriority.sync
	cbdata[9] = false

	-- 下载AB
	AssetBundleMgr:DownLoadBundles(need_downloads, DownLoadBundlesCallBack, ResLoadPriority.sync, cbdata)
end

-- 异步加载Prefab并实例化GameObject
function M:LoadGameobjAsync(bundle_name, asset_name, cb, parent, cbdata, load_priority)
	self:_LoadGameobj(bundle_name, asset_name, cb, cbdata, true, parent, load_priority)
end

-- 同步加载Prefab并实例化GameObject
function M:LoadGameobjSync(bundle_name, asset_name, cb, parent, cbdata)
	self:_LoadGameobj(bundle_name, asset_name, cb, cbdata, false, parent, ResLoadPriority.sync)
end

local function GetPrefabCallBack(prefab, cbdata)
	local self = cbdata[1]
	local cb = cbdata[2]
	local up_cbdata = cbdata[3]
	local bundle_name = cbdata[4]
	local asset_name = cbdata[5]
	local parent = cbdata[6]
	local is_async = cbdata[7]
	local load_priority = cbdata[8]
	self:_ReleaseGetPrefabCBData(cbdata)

	if ResUtil.log_debug then
		ResUtil.Log("[BundleLoader] async load gameobject complete", bundle_name, asset_name, prefab)
	end

	if nil == prefab then
		cb(nil, up_cbdata)
		return
	end

	if load_priority >= ResLoadPriority.faster_async_low and load_priority <= ResLoadPriority.sync then
		self:_Instantiate(prefab, parent, cb, up_cbdata, bundle_name, asset_name)
	else
		local t = self:_GetInstantiateTable()
		t[1] = cb
		t[2] = up_cbdata
		t[3] = prefab
		t[4] = bundle_name
		t[5] = asset_name
		t[6] = parent
		_tinsert(self.v_priority_instantiate_queue[load_priority], t)
	end
end

function M:_LoadGameobj(bundle_name, asset_name, cb, up_cbdata, is_async, parent, load_priority)
	if ResUtil.log_debug then
		ResUtil.Log("[BundleLoader] start async load gameobject", bundle_name, asset_name)
	end

	if nil == load_priority or load_priority <= ResLoadPriority.min or load_priority >= ResLoadPriority.max then
		print_error("[BundleLoader] _LoadGameobj, load_priority is invalid")
		return
	end

	local cbdata = self:_GetPrefabCBData()
	cbdata[1] = self
	cbdata[2] = cb
	cbdata[3] = up_cbdata
	cbdata[4] = bundle_name
	cbdata[5] = asset_name
	cbdata[6] = parent
	cbdata[7] = is_async
	cbdata[8] = load_priority
	ResPoolMgr:GetPrefab(bundle_name, asset_name, GetPrefabCallBack, is_async, cbdata, load_priority)
end

function M:_UpdateInstantiate()
	local max_instantiate_count = AssetBundleMgr:IsLowLoad() and MAX_INSTANTIATE_COUNT or MAX_INSTANTIATE_COUNT * 2
	local remain_can_instantiate_count = math.max(max_instantiate_count - self.v_instantiate_count, 1)
	for i, v in ipairs(self.load_priority_type_list) do
		local allocate_can_instantiate_count = math.ceil(remain_can_instantiate_count * self.load_priority_count_list[i])
		if 0 == allocate_can_instantiate_count and 0 == self.v_instantiate_count then
			allocate_can_instantiate_count = 1
		end
		remain_can_instantiate_count = remain_can_instantiate_count - self:_InstantiateInPriority(v, allocate_can_instantiate_count)
	end

	self.v_instantiate_count = 0
end

function M:_InstantiateInPriority(load_priority, instantiate_count)
	if instantiate_count < 0 then
		print_error("[BundleLoader] _InstantiateInPriority big bug, instantiate_count less 0", instantiate_count)
		instantiate_count = 0
	end

	local queue = self.v_priority_instantiate_queue[load_priority]
	instantiate_count = math.min(instantiate_count, #queue)
	for i=1, instantiate_count do
		local t = table.remove(queue, 1)
		self:_Instantiate(t[3], t[6], t[1], t[2], t[4], t[5])
		self:_ReleaseInstantiateTable(t)
	end

	return instantiate_count
end

function M:_Instantiate(prefab, parent, cb, cbdata, bundle_name, asset_name)
	self.v_instantiate_count = self.v_instantiate_count + 1
	if nil ~= prefab then
		xpcall(function()
			local gameobj = ResMgr:Instantiate(prefab, true, parent)
			local instance_id = gameobj:GetInstanceID()
			self.v_goid_go_monitors[instance_id] = gameobj
			self.v_goid_prefab_map[instance_id] = prefab
			cb(gameobj, cbdata)
		end,
		function(errmsg)
		    print_error(debug.traceback(tostring(errmsg)), "LUA ERROR")
		    print_error(string.format("[BundleLoader]Instantiate Prefab Error, bundle_name:%s, asset_name:%s", bundle_name, asset_name))
		    return false
		end)
	else
		cb(nil, cbdata)
	end
end

-- 计算加载AB需要依赖加载的文件
function M:ClacLoadBundleDepends(bundle_name, asset_name)
	local deps = self:GetBundleDeps(bundle_name)
	if not deps then
		print_error("[BundleLoader] not found dependency：", bundle_name, asset_name)
		return nil, nil
	end

	local bundle_hash = self:GetBundleHash(bundle_name)
	if nil == bundle_hash then
		print_error("[BundleLoader] not exists in manifest: ", bundle_name, asset_name)
		return nil, nil
	end

	local need_downloads = {}
	local need_loads = {bundle_name}
	if not ResUtil.IsFileExist(bundle_name, bundle_hash) then
		need_downloads = {bundle_name}
	end

	for _, dep in ipairs(deps) do
		local hash = self.v_manifest_info.bundleInfos[dep].hash
		if not ResUtil.IsFileExist(dep, hash) then
			_tinsert(need_loads, dep)
			_tinsert(need_downloads, dep)
		else
			_tinsert(need_loads, dep)
		end
	end

	return need_downloads, need_loads
end

-- 监测obj的是否已被称除，逻辑层往往在因为父节点移除而没有调Destroy的方法
-- 此时应该从缓存列表中移除记录
function M:MonitorGameObjLive(time)
	if time < self.v_goid_go_monit_time then
		return
	end

	self.v_goid_go_monit_time = time + 1

	local die_goids = ResUtil.GetTable()
	local die_num = 0
	local monitor_count = 0
	for k, v in pairs(self.v_goid_go_monitors) do
		monitor_count = monitor_count + 1
		if v:Equals(nil) then
			die_num = die_num + 1
			die_goids[die_num] = k
			_tinsert(die_goids, k)
		end
	end

	for i=1, die_num do
		self:ReleaseInObjId(die_goids[i])
		ResPoolMgr:OnGameObjIllegalDestroy(die_goids[i])
		die_goids[i] = nil
	end

	ResUtil.ReleaseTable(die_goids)

	if die_num > 0 then
		print(string.format("[BundleLoader] monitor_count=%s, die_gameobj_count=%s", monitor_count, die_num))
	end
end

function M:Destroy(gameobj, release_policy)
	-- 监测加载完成
	if develop_mode:IsDeveloper() and not IsNil(gameobj) then
		develop_mode:OnLuaCall("destroy_gameobj", gameobj)
	end

	self:__Destroy(gameobj, release_policy)
end

function M:__Destroy(gameobj, release_policy)
	if IsNil(gameobj) then
		return
	end

	if nil ~= ResPoolMgr and ResPoolMgr:IsInGameObjPool(gameobj:GetInstanceID(), gameobj) then
		print_error("[BundleLoader] big bug, destroy pool gameobject")
		return
	end

	self:ReleaseInObjId(gameobj:GetInstanceID(), release_policy)
	UnityDestroy(gameobj)
end

function M:ReleaseInObjId(instance_id, release_policy)
	if nil ~= self.v_goid_prefab_map[instance_id] then
		ResPoolMgr:Release(self.v_goid_prefab_map[instance_id], release_policy)
		self.v_goid_go_monitors[instance_id] = nil
		self.v_goid_prefab_map[instance_id] = nil
	end
end

function M:UseBundle(bundle_name)
	BundleCache:OnUseBundle(bundle_name)
end

function M:ReleaseBundle(bundle_name)
	BundleCache:OnUnUseBundle(bundle_name)
end

function M:IsCanSafeUseBundle(bundle_name)
	if not BundleCache:IsBundlRefing(bundle_name) then
		return false
	end

	local deps = self:GetBundleDeps(bundle_name)
	if nil ~= deps then
		for _, dep in ipairs(deps) do
			if not BundleCache:IsBundlRefing(dep) then
				return false
			end
		end
	end

	return true
end

function M:UpdateBundle(bundle_name, update_callback, complete_callback, check_hash)
	local bundle_path = ""
	local bundle_hash = ""
	if IsLuaAssetBundle(bundle_name) then
		bundle_path = "LuaAssetBundle/" .. bundle_name
		bundle_hash = self:GetLuaBundleHash(bundle_name)
	else
		bundle_path = bundle_name
		bundle_hash = self:GetBundleHash(bundle_name)
	end
	local remote_path = self:GetRemotePath(bundle_path, bundle_hash)
	local cache_path = ResUtil.GetCachePath(bundle_path, bundle_hash)

	DownloaderMgr:CreateFileDownloader(remote_path, update_callback, complete_callback, cache_path, bundle_name, bundle_hash, check_hash)
end

-- 下载Bundle
-- 在线热更新专用接口
function M:RuntimeUpdateBundle(bundle_name, update_callback, complete_callback)
	local bundle_path = ""
	local bundle_hash = ""
	if IsLuaAssetBundle(bundle_name) then
		bundle_path = "LuaAssetBundle/" .. bundle_name
		bundle_hash = self:GetLuaBundleHash(bundle_name)
	else
		bundle_path = bundle_name
		bundle_hash = self:GetBundleHash(bundle_name)
	end
	local remote_path = self:GetRuntimeRemotePath(bundle_path, bundle_hash)
	local cache_path = ResUtil.GetCachePath(bundle_path, bundle_hash)

	DownloaderMgr:CreateFileDownloader(remote_path, update_callback, complete_callback, cache_path, bundle_name)
end

function M:UnloadScene(bundle_name)
	self:ReleaseBundle(bundle_name)
end

function M:GetBundlesWithoutCached(bundle_name)
	local ret = {}

	if not ResUtil.IsFileExist(bundle_name, self:GetBundleHash(bundle_name)) then
		ret[bundle_name] = true
	end

	local deps = self:GetBundleDeps(bundle_name)
	if deps then
		for _, dep in ipairs(deps) do
			if not ResUtil.IsFileExist(dep, self:GetBundleHash(dep)) then
				ret[dep] = true
			end
		end
	end

	return ret
end

-- function M:LoadAssetBundle(bundle_name, is_async, callback, cbdata)
-- 	if is_async then
-- 		self:LoadAssetBundleAsync(bundle_name, callback, cbdata)
-- 	else
-- 		self:LoadAssetBundleSync(bundle_name, callback, cbdata)
-- 	end
-- end

-- function M:UnLoadAssetBundle(bundle_name)
-- 	self:ReleaseBundle(bundle_name)
-- end

-- function M:LoadAssetBundleSync(bundle_name, cb, cbdata)
-- 	local need_downloads, need_loads = self:ClacLoadBundleDepends(bundle_name)
-- 	if nil == need_downloads or nil == need_loads then
-- 		cb(nil, cbdata, bundle_name)
-- 		return
-- 	end

-- 	-- 下载AB
-- 	AssetBundleMgr:DownLoadBundles(need_downloads, function(is_succ)
-- 		if not is_succ then
-- 			cb(nil, cbdata, bundle_name)
-- 			return
-- 		end

-- 		BundleCache:LockBundles(need_loads)
-- 		-- 同步加载AB
-- 		AssetBundleMgr:LoadMultiBundlesSync(need_loads, function(is_succ)
-- 			if not is_succ then
-- 				BundleCache:UnLockBundles(need_loads)
-- 				cb(nil, cbdata, bundle_name)
-- 				return
-- 			end

-- 			self:UseBundle(bundle_name)
-- 			local bundle = BundleCache:GetCacheRes(bundle_name)
-- 			BundleCache:UnLockBundles(need_loads)
-- 			cb(bundle.asset_bundle, cbdata, bundle_name)
-- 		end)
-- 	end, ResLoadPriority.sync)
-- end

local InstantiateTableList = {}
function M:_GetInstantiateTable()
    local t = table.remove(InstantiateTableList)
    if nil == t then
    	--[1]cb[2]cbdata[3]prefab[4]bundle_name[5]asset_name[6]parent
        t = {true, true, true, true, true, true}
    end

    return t
end

function M:_ReleaseInstantiateTable(t)
    t[1] = true
    t[2] = true
    t[3] = true
    t[4] = true
    t[5] = true
    t[6] = true
    table.insert(InstantiateTableList, t)
end

local GetPrefabCBDataList = {}
function M:_GetPrefabCBData()
    local t = table.remove(GetPrefabCBDataList)
    if nil == t then
        -- [1]self[2]cb[3]up_cbdata[4]bundle_name[5]asset_name[6]parent[7]is_async[8]load_priority
        t = {true, true, true, true, true, true, true, true}
    end

    return t
end

function M:_ReleaseGetPrefabCBData(t)
    t[2] = true
    t[3] = true
    t[6] = true
    table.insert(GetPrefabCBDataList, t)
end

local DownLoadBundlesCBDataList = {}
function M:_GetDownLoadBundlesCBData()
    local t = table.remove(DownLoadBundlesCBDataList)
    if nil == t then
        -- [1]self[2]asset_type[3]bundle_name[4]asset_name[5]cb[6]up_cbdata[7]need_loads[8]load_priority[9]is_async
        t = {true, true, true, true, true, true, true, true, true}
    end

    return t
end

function M:_ReleaseDownLoadBundlesCBData(t)
    t[4] = true
    t[5] = true
    t[6] = true
    table.insert(DownLoadBundlesCBDataList, t)
end

function M:GetDebugGameObjCount(t)
	t.gameobj_count = 0
	for _, v in pairs(self.v_goid_prefab_map) do
		t.gameobj_count = t.gameobj_count + 1
	end
end

function M:GetAllGameObjectInstanceID()
	local list = {}
	local index = 0
	for instance_id,_ in pairs(self.v_goid_prefab_map) do
		list[index] = instance_id
		index = index + 1
	end

	return list
end

return M

