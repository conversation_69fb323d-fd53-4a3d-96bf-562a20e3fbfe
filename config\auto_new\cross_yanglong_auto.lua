-- K-跨服养龙寺.xls
local item_table={
[1]={item_id=48471,num=1,is_bind=1},
[2]={item_id=48071,num=5,is_bind=1},
[3]={item_id=39989,num=2,is_bind=1},
[4]={item_id=46584,num=3,is_bind=1},
[5]={item_id=56316,num=5,is_bind=1},
[6]={item_id=26048,num=1,is_bind=1},
[7]={item_id=48470,num=1,is_bind=1},
[8]={item_id=48071,num=3,is_bind=1},
[9]={item_id=39989,num=1,is_bind=1},
[10]={item_id=46584,num=2,is_bind=1},
[11]={item_id=56316,num=3,is_bind=1},
[12]={item_id=26047,num=1,is_bind=1},
[13]={item_id=48071,num=2,is_bind=1},
[14]={item_id=46584,num=1,is_bind=1},
[15]={item_id=56316,num=2,is_bind=1},
[16]={item_id=39784,num=1,is_bind=1},
[17]={item_id=56316,num=1,is_bind=1},
[18]={item_id=26046,num=1,is_bind=1},
[19]={item_id=26177,num=85,is_bind=1},
[20]={item_id=38955,num=1,is_bind=1},
[21]={item_id=26177,num=70,is_bind=1},
[22]={item_id=38956,num=1,is_bind=1},
[23]={item_id=26177,num=55,is_bind=1},
[24]={item_id=26177,num=40,is_bind=1},
[25]={item_id=44058,num=1,is_bind=1},
[26]={item_id=44059,num=1,is_bind=1},
[27]={item_id=37603,num=1,is_bind=1},
[28]={item_id=26943,num=1,is_bind=1},
[29]={item_id=22013,num=1,is_bind=1},
[30]={item_id=48505,num=1,is_bind=1},
[31]={item_id=48506,num=1,is_bind=1},
[32]={item_id=37602,num=1,is_bind=1},
[33]={item_id=37701,num=1,is_bind=1},
[34]={item_id=46579,num=1,is_bind=1},
[35]={item_id=46580,num=1,is_bind=1},
[36]={item_id=46581,num=1,is_bind=1},
[37]={item_id=48145,num=1,is_bind=1},
[38]={item_id=48146,num=1,is_bind=1},
[39]={item_id=46572,num=1,is_bind=1},
[40]={item_id=46573,num=1,is_bind=1},
[41]={item_id=46574,num=1,is_bind=1},
[42]={item_id=46575,num=1,is_bind=1},
[43]={item_id=46576,num=1,is_bind=1},
[44]={item_id=46582,num=1,is_bind=1},
[45]={item_id=46583,num=1,is_bind=1},
[46]={item_id=44173,num=1,is_bind=1},
[47]={item_id=44174,num=1,is_bind=1},
[48]={item_id=44073,num=1,is_bind=1},
[49]={item_id=44074,num=1,is_bind=1},
[50]={item_id=48071,num=1,is_bind=1},
[51]={item_id=26177,num=100,is_bind=1},
[52]={item_id=38954,num=1,is_bind=1},
[53]={item_id=37704,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
refresh_time={
{},
{seq=1,refresh_time=1430,},
{seq=2,refresh_time=1730,},
{seq=3,refresh_time=2230,}
},

refresh_time_meta_table_map={
},
boss={
{monster_id=3001,},
{seq=1,monster_pos="30,167",},
{seq=2,monster_pos="104,74",},
{seq=3,monster_pos="246,123",},
{seq=4,monster_pos="175,19",}
},

boss_meta_table_map={
},
hurt_cap={
{},
{index=1,min_world_level=301,max_world_level=400,},
{index=2,min_world_level=401,max_world_level=500,},
{index=3,min_world_level=501,max_world_level=550,},
{index=4,min_world_level=551,max_world_level=600,},
{index=5,min_world_level=601,max_world_level=650,},
{index=6,min_world_level=651,max_world_level=700,},
{index=7,min_world_level=701,max_world_level=750,},
{index=8,min_world_level=751,max_world_level=800,},
{index=9,min_world_level=801,max_world_level=850,},
{index=10,min_world_level=851,max_world_level=900,},
{index=11,min_world_level=901,max_world_level=950,},
{index=12,min_world_level=951,max_world_level=1000,},
{index=13,min_world_level=1001,max_world_level=1050,},
{index=14,min_world_level=1051,max_world_level=1100,},
{index=15,min_world_level=1101,max_world_level=1150,},
{index=16,min_world_level=1151,max_world_level=1200,},
{index=17,min_world_level=1201,max_world_level=99999,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,}
},

hurt_cap_meta_table_map={
[70]=16,	-- depth:1
[69]=15,	-- depth:1
[68]=14,	-- depth:1
[67]=13,	-- depth:1
[66]=12,	-- depth:1
[63]=9,	-- depth:1
[64]=10,	-- depth:1
[62]=8,	-- depth:1
[61]=7,	-- depth:1
[60]=6,	-- depth:1
[59]=5,	-- depth:1
[71]=17,	-- depth:1
[57]=3,	-- depth:1
[65]=11,	-- depth:1
[58]=4,	-- depth:1
[78]=60,	-- depth:2
[74]=2,	-- depth:1
[75]=57,	-- depth:2
[76]=58,	-- depth:2
[77]=59,	-- depth:2
[79]=61,	-- depth:2
[80]=62,	-- depth:2
[56]=74,	-- depth:2
[82]=64,	-- depth:2
[83]=65,	-- depth:2
[84]=66,	-- depth:2
[85]=67,	-- depth:2
[86]=68,	-- depth:2
[87]=69,	-- depth:2
[88]=70,	-- depth:2
[72]=18,	-- depth:1
[81]=63,	-- depth:2
[45]=81,	-- depth:3
[53]=71,	-- depth:2
[33]=87,	-- depth:3
[32]=86,	-- depth:3
[31]=85,	-- depth:3
[30]=84,	-- depth:3
[29]=83,	-- depth:3
[28]=82,	-- depth:3
[34]=88,	-- depth:3
[27]=45,	-- depth:4
[25]=79,	-- depth:3
[24]=78,	-- depth:3
[23]=77,	-- depth:3
[22]=76,	-- depth:3
[21]=75,	-- depth:3
[20]=56,	-- depth:3
[26]=80,	-- depth:3
[54]=72,	-- depth:2
[35]=53,	-- depth:3
[38]=20,	-- depth:4
[52]=34,	-- depth:4
[51]=33,	-- depth:4
[50]=32,	-- depth:4
[49]=31,	-- depth:4
[48]=30,	-- depth:4
[47]=29,	-- depth:4
[36]=54,	-- depth:3
[46]=28,	-- depth:4
[44]=26,	-- depth:4
[43]=25,	-- depth:4
[42]=24,	-- depth:4
[41]=23,	-- depth:4
[40]=22,	-- depth:4
[39]=21,	-- depth:4
[89]=35,	-- depth:4
[90]=36,	-- depth:4
},
reward={
{reward_item={[0]={item_id=27814,num=3,is_bind=139784,param0=5,param1=1},[1]=item_table[1],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4],[5]=item_table[5],[6]=item_table[6]},not_jieyi_reward_item={[0]={item_id=27814,num=3,is_bind=139784,param0=5,param1=1},[1]=item_table[1],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4],[5]=item_table[5],[6]=item_table[6]},kill_get_rank_score=500,},
{min_rank=2,max_rank=3,reward_item={[0]={item_id=27814,num=2,is_bind=139784,param0=3,param1=1},[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11],[6]=item_table[12]},not_jieyi_reward_item={[0]={item_id=27814,num=2,is_bind=139784,param0=3,param1=1},[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11],[6]=item_table[12]},kill_get_rank_score=300,icon=2,},
{min_rank=4,max_rank=10,reward_item={[0]={item_id=27814,num=1,is_bind=139784,param0=1,param1=1},[1]=item_table[7],[2]=item_table[13],[3]=item_table[9],[4]=item_table[14],[5]=item_table[15],[6]=item_table[12]},not_jieyi_reward_item={[0]={item_id=27814,num=1,is_bind=139784,param0=1,param1=1},[1]=item_table[7],[2]=item_table[13],[3]=item_table[9],[4]=item_table[14],[5]=item_table[15],[6]=item_table[12]},icon=3,},
{seq=1,kill_get_rank_score=200,name="BOSS2",},
{seq=1,min_rank=2,max_rank=3,reward_item={[0]=item_table[16],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[17],[5]=item_table[18]},not_jieyi_reward_item={[0]=item_table[16],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[17],[5]=item_table[18]},name="BOSS2",icon=2,},
{seq=1,min_rank=4,max_rank=10,reward_item={[0]=item_table[16],[1]=item_table[7],[2]=item_table[13],[3]=item_table[9],[4]=item_table[17],[5]=item_table[18]},not_jieyi_reward_item={[0]=item_table[16],[1]=item_table[7],[2]=item_table[13],[3]=item_table[9],[4]=item_table[17],[5]=item_table[18]},kill_get_rank_score=50,name="BOSS2",icon=3,},
{seq=2,name="BOSS3",},
{seq=2,name="BOSS3",},
{seq=2,name="BOSS3",},
{seq=3,name="BOSS4",},
{seq=3,name="BOSS4",},
{seq=3,name="BOSS4",},
{seq=4,name="BOSS5",},
{seq=4,name="BOSS5",},
{seq=4,name="BOSS5",}
},

reward_meta_table_map={
[7]=4,	-- depth:1
[10]=4,	-- depth:1
[13]=4,	-- depth:1
[8]=5,	-- depth:1
[11]=5,	-- depth:1
[14]=5,	-- depth:1
[9]=6,	-- depth:1
[12]=6,	-- depth:1
[15]=6,	-- depth:1
},
rank_reward={
{title_id=9007,},
{min_rank=2,max_rank=2,title_id=9008,reward_item={[0]=item_table[19]},reward_show_item={[0]=item_table[20],[1]=item_table[19]},},
{min_rank=3,max_rank=3,title_id=9009,reward_item={[0]=item_table[21]},reward_show_item={[0]=item_table[22],[1]=item_table[21]},},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[23]},reward_show_item={[0]=item_table[23]},},
{min_rank=11,max_rank=50,reward_item={[0]=item_table[24]},reward_show_item={[0]=item_table[24]},}
},

rank_reward_meta_table_map={
},
convert_grade={
{},
{min_day=13,max_day=33,grade=2,},
{min_day=34,max_day=54,grade=3,},
{min_day=55,max_day=75,grade=4,},
{min_day=76,max_day=96,grade=5,},
{min_day=97,max_day=117,grade=6,},
{min_day=118,max_day=138,grade=7,},
{min_day=139,max_day=159,grade=8,},
{min_day=160,max_day=180,grade=9,},
{min_day=181,max_day=201,grade=10,},
{min_day=202,max_day=222,grade=11,},
{min_day=223,max_day=243,grade=12,},
{min_day=244,max_day=9999,grade=13,}
},

convert_grade_meta_table_map={
},
convert={
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,item=item_table[25],},
{seq=5,item=item_table[26],},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,seq=20,},
{grade=1,seq=21,},
{stuff_num_1=120,},
{seq=1,item=item_table[27],},
{seq=2,times_limit=2,item=item_table[28],stuff_num_1=10,},
{seq=3,times_limit=2,item=item_table[29],},
{seq=4,times_limit=5,item=item_table[30],},
{seq=5,item=item_table[31],},
{seq=6,item=item_table[32],},
{seq=7,item=item_table[33],stuff_num_1=100,},
{seq=8,times_limit=8,item=item_table[34],stuff_num_1=10,},
{seq=9,times_limit=8,item=item_table[35],stuff_num_1=8,},
{seq=10,times_limit=6,item=item_table[36],stuff_num_1=40,},
{seq=11,times_limit=5,item=item_table[37],stuff_num_1=8,},
{seq=12,item=item_table[38],},
{seq=13,times_limit=6,item=item_table[39],stuff_num_1=35,},
{seq=14,item=item_table[40],},
{seq=15,times_limit=8,item=item_table[41],stuff_num_1=45,},
{seq=16,item=item_table[42],},
{seq=17,times_limit=4,item=item_table[43],},
{seq=18,times_limit=10,item=item_table[44],stuff_num_1=42,},
{seq=19,times_limit=12,item=item_table[45],stuff_num_1=40,},
{seq=20,times_limit=2,item=item_table[46],stuff_num_1=25,},
{seq=21,item=item_table[47],stuff_num_1=55,},
{seq=22,times_limit=3,item=item_table[48],stuff_num_1=25,},
{seq=23,item=item_table[49],stuff_num_1=65,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,},
{grade=13,}
},

convert_meta_table_map={
[143]=23,	-- depth:1
[167]=143,	-- depth:2
[239]=167,	-- depth:3
[263]=239,	-- depth:4
[287]=263,	-- depth:5
[71]=287,	-- depth:6
[47]=71,	-- depth:7
[95]=47,	-- depth:8
[215]=95,	-- depth:9
[191]=215,	-- depth:10
[119]=191,	-- depth:11
[1]=119,	-- depth:12
[29]=30,	-- depth:1
[24]=23,	-- depth:1
[28]=27,	-- depth:1
[146]=26,	-- depth:1
[192]=24,	-- depth:2
[102]=30,	-- depth:1
[190]=46,	-- depth:1
[112]=40,	-- depth:1
[188]=44,	-- depth:1
[184]=112,	-- depth:2
[174]=102,	-- depth:2
[116]=188,	-- depth:2
[118]=190,	-- depth:2
[173]=29,	-- depth:2
[120]=192,	-- depth:3
[172]=28,	-- depth:2
[171]=27,	-- depth:1
[170]=146,	-- depth:2
[122]=170,	-- depth:3
[168]=120,	-- depth:4
[123]=171,	-- depth:2
[144]=168,	-- depth:5
[148]=172,	-- depth:3
[142]=118,	-- depth:3
[140]=116,	-- depth:3
[194]=122,	-- depth:4
[136]=184,	-- depth:3
[147]=123,	-- depth:3
[126]=174,	-- depth:3
[150]=126,	-- depth:4
[160]=136,	-- depth:4
[164]=140,	-- depth:4
[166]=142,	-- depth:4
[125]=173,	-- depth:3
[124]=148,	-- depth:4
[149]=125,	-- depth:4
[195]=147,	-- depth:4
[216]=144,	-- depth:6
[197]=149,	-- depth:5
[264]=216,	-- depth:7
[266]=194,	-- depth:5
[267]=195,	-- depth:5
[268]=124,	-- depth:5
[269]=197,	-- depth:6
[270]=150,	-- depth:5
[280]=160,	-- depth:5
[284]=164,	-- depth:5
[286]=166,	-- depth:5
[288]=264,	-- depth:8
[290]=266,	-- depth:6
[291]=267,	-- depth:6
[292]=268,	-- depth:6
[293]=269,	-- depth:7
[294]=270,	-- depth:6
[304]=280,	-- depth:6
[308]=284,	-- depth:6
[262]=286,	-- depth:6
[260]=308,	-- depth:7
[256]=304,	-- depth:7
[246]=294,	-- depth:7
[198]=246,	-- depth:8
[208]=256,	-- depth:8
[212]=260,	-- depth:8
[214]=262,	-- depth:7
[101]=293,	-- depth:8
[218]=290,	-- depth:7
[219]=291,	-- depth:7
[220]=292,	-- depth:7
[196]=220,	-- depth:8
[221]=101,	-- depth:9
[232]=208,	-- depth:9
[236]=212,	-- depth:9
[238]=214,	-- depth:8
[240]=288,	-- depth:9
[242]=218,	-- depth:8
[243]=219,	-- depth:8
[244]=196,	-- depth:9
[245]=221,	-- depth:10
[222]=198,	-- depth:9
[100]=244,	-- depth:10
[310]=238,	-- depth:9
[98]=242,	-- depth:9
[76]=100,	-- depth:11
[18]=232,	-- depth:10
[52]=76,	-- depth:12
[22]=46,	-- depth:1
[75]=243,	-- depth:9
[74]=98,	-- depth:10
[72]=240,	-- depth:10
[70]=310,	-- depth:10
[68]=236,	-- depth:10
[35]=34,	-- depth:1
[37]=36,	-- depth:1
[64]=18,	-- depth:11
[39]=38,	-- depth:1
[48]=72,	-- depth:11
[99]=75,	-- depth:10
[54]=222,	-- depth:10
[50]=74,	-- depth:11
[51]=99,	-- depth:11
[77]=245,	-- depth:11
[78]=54,	-- depth:11
[53]=77,	-- depth:12
[88]=64,	-- depth:12
[96]=48,	-- depth:12
[94]=70,	-- depth:11
[8]=78,	-- depth:12
[7]=53,	-- depth:13
[5]=27,	-- depth:1
[4]=50,	-- depth:12
[6]=5,	-- depth:2
[92]=68,	-- depth:11
[2]=96,	-- depth:13
[241]=25,	-- depth:1
[233]=41,	-- depth:1
[296]=32,	-- depth:1
[297]=33,	-- depth:1
[298]=34,	-- depth:1
[237]=45,	-- depth:1
[299]=35,	-- depth:2
[235]=43,	-- depth:1
[234]=42,	-- depth:1
[295]=31,	-- depth:1
[13]=299,	-- depth:3
[300]=36,	-- depth:1
[230]=38,	-- depth:1
[307]=235,	-- depth:2
[306]=234,	-- depth:2
[217]=241,	-- depth:2
[49]=217,	-- depth:3
[305]=233,	-- depth:2
[3]=49,	-- depth:4
[303]=39,	-- depth:2
[231]=303,	-- depth:3
[302]=230,	-- depth:2
[223]=295,	-- depth:2
[224]=296,	-- depth:2
[225]=297,	-- depth:2
[226]=298,	-- depth:2
[227]=13,	-- depth:4
[228]=300,	-- depth:2
[229]=37,	-- depth:2
[301]=229,	-- depth:3
[247]=223,	-- depth:3
[249]=225,	-- depth:3
[281]=305,	-- depth:3
[283]=307,	-- depth:3
[282]=306,	-- depth:3
[17]=231,	-- depth:4
[16]=302,	-- depth:3
[15]=301,	-- depth:4
[14]=228,	-- depth:3
[271]=247,	-- depth:4
[272]=224,	-- depth:3
[273]=249,	-- depth:4
[274]=226,	-- depth:3
[275]=227,	-- depth:5
[276]=14,	-- depth:4
[277]=15,	-- depth:5
[278]=16,	-- depth:4
[279]=17,	-- depth:5
[12]=274,	-- depth:4
[248]=272,	-- depth:4
[285]=237,	-- depth:2
[19]=281,	-- depth:4
[250]=12,	-- depth:5
[251]=275,	-- depth:6
[252]=276,	-- depth:5
[253]=277,	-- depth:6
[254]=278,	-- depth:5
[255]=279,	-- depth:6
[289]=3,	-- depth:5
[257]=19,	-- depth:5
[9]=271,	-- depth:5
[258]=282,	-- depth:4
[259]=283,	-- depth:4
[10]=248,	-- depth:5
[11]=273,	-- depth:5
[21]=45,	-- depth:1
[20]=258,	-- depth:5
[265]=289,	-- depth:6
[261]=285,	-- depth:3
[205]=253,	-- depth:7
[211]=259,	-- depth:5
[131]=251,	-- depth:7
[132]=252,	-- depth:6
[133]=205,	-- depth:8
[134]=254,	-- depth:6
[135]=255,	-- depth:7
[85]=133,	-- depth:9
[137]=257,	-- depth:6
[138]=20,	-- depth:6
[139]=211,	-- depth:6
[130]=250,	-- depth:6
[84]=132,	-- depth:7
[83]=131,	-- depth:8
[82]=130,	-- depth:7
[81]=11,	-- depth:6
[145]=265,	-- depth:7
[80]=10,	-- depth:6
[79]=9,	-- depth:6
[151]=79,	-- depth:7
[152]=80,	-- depth:7
[153]=81,	-- depth:7
[141]=261,	-- depth:4
[129]=153,	-- depth:8
[128]=152,	-- depth:8
[127]=151,	-- depth:8
[97]=145,	-- depth:8
[103]=127,	-- depth:9
[104]=128,	-- depth:9
[105]=129,	-- depth:9
[106]=82,	-- depth:8
[107]=83,	-- depth:9
[108]=84,	-- depth:8
[109]=85,	-- depth:10
[110]=134,	-- depth:7
[111]=135,	-- depth:8
[113]=137,	-- depth:7
[114]=138,	-- depth:7
[115]=139,	-- depth:7
[117]=141,	-- depth:5
[93]=117,	-- depth:6
[91]=115,	-- depth:8
[121]=97,	-- depth:9
[90]=114,	-- depth:8
[89]=113,	-- depth:8
[87]=111,	-- depth:9
[86]=110,	-- depth:8
[154]=106,	-- depth:9
[213]=93,	-- depth:7
[309]=213,	-- depth:8
[157]=109,	-- depth:11
[189]=309,	-- depth:9
[62]=86,	-- depth:9
[61]=157,	-- depth:12
[60]=108,	-- depth:9
[193]=121,	-- depth:10
[59]=107,	-- depth:10
[58]=154,	-- depth:10
[57]=105,	-- depth:10
[56]=104,	-- depth:10
[63]=87,	-- depth:10
[55]=103,	-- depth:10
[200]=56,	-- depth:11
[201]=57,	-- depth:11
[202]=58,	-- depth:11
[203]=59,	-- depth:11
[204]=60,	-- depth:10
[206]=62,	-- depth:10
[207]=63,	-- depth:11
[209]=89,	-- depth:9
[210]=90,	-- depth:9
[199]=55,	-- depth:11
[187]=91,	-- depth:9
[186]=210,	-- depth:10
[185]=209,	-- depth:10
[158]=206,	-- depth:11
[159]=207,	-- depth:12
[161]=185,	-- depth:11
[162]=186,	-- depth:11
[163]=187,	-- depth:10
[165]=189,	-- depth:10
[73]=193,	-- depth:11
[169]=73,	-- depth:12
[69]=165,	-- depth:11
[67]=163,	-- depth:11
[66]=162,	-- depth:12
[65]=161,	-- depth:12
[175]=199,	-- depth:12
[176]=200,	-- depth:12
[177]=201,	-- depth:12
[178]=202,	-- depth:12
[179]=203,	-- depth:12
[180]=204,	-- depth:11
[181]=61,	-- depth:13
[182]=158,	-- depth:12
[183]=159,	-- depth:13
[156]=180,	-- depth:12
[155]=179,	-- depth:13
},
combine_cap={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

combine_cap_meta_table_map={
},
other_default_table={open_level=9999,scene_id=3100,be_kill_reduce_per=1000,show_reward_item={[0]={item_id=27814,num=1,is_bind=139784,param0=1,param1=1},[1]=item_table[1],[2]=item_table[7],[3]=item_table[50],[4]=item_table[9],[5]=item_table[14],[6]=item_table[17],[7]=item_table[6],[8]=item_table[12],[9]=item_table[18]},cap_rank_count=20,force_peace_cross_world_level=150,kill_role_get_rank_score_daily_limit=2000,kill_role_get_rank_score=20,show_model=3001,display_pos="-150|-300|0",display_scale=0.2,display_rotation="0|-45|0",show_boss_head_id=3001,enter_time="1130,1230|1430,1530|1730,1830|2230,2330",},

refresh_time_default_table={seq=0,refresh_time=1130,},

boss_default_table={seq=0,monster_id=3000,monster_pos="61,241",},

hurt_cap_default_table={seq=0,index=0,min_world_level=1,max_world_level=300,},

reward_default_table={seq=0,min_rank=1,max_rank=1,reward_item={[0]={item_id=27814,num=1,is_bind=139784,param0=3,param1=1},[1]=item_table[7],[2]=item_table[2],[3]=item_table[3],[4]=item_table[14],[5]=item_table[15],[6]=item_table[12]},not_jieyi_reward_item={[0]={item_id=27814,num=1,is_bind=139784,param0=3,param1=1},[1]=item_table[7],[2]=item_table[2],[3]=item_table[3],[4]=item_table[14],[5]=item_table[15],[6]=item_table[12]},kill_get_rank_score=100,name="BOSS1",icon=1,},

rank_reward_default_table={min_rank=1,max_rank=1,title_id=0,reward_item={[0]=item_table[51]},reward_show_item={[0]=item_table[52],[1]=item_table[51]},},

convert_grade_default_table={min_day=1,max_day=12,grade=1,},

convert_default_table={grade=2,seq=0,times_limit=1,item=item_table[53],stuff_id_1=26177,stuff_num_1=5,stuff_id_2=0,stuff_num_2=0,stuff_id_3=0,stuff_num_3=0,},

combine_cap_default_table={}

}

