FadeView = FadeView or BaseClass(SafeBaseView)

function FadeView:__init()
	self.ui_config = {nil,"FadeView"}
	self.open_tween = nil
	self.close_tween = nil
	self.active_close = false
	self.view_layer = UiLayer.Guide
end

function FadeView:__delete()

end

function FadeView:ReleaseCallBack()
	self.black_imge = nil
end

function FadeView:LoadCallBack()
	self.black_imge = U3DObject(GameObject.New("BlackImage"), nil, self)
	local black_imge_transform = self.black_imge.transform
	black_imge_transform:SetParent(self.root_node.transform, false)
	self.black_imge.gameObject:AddComponent(typeof(UnityEngine.UI.Image))
	self.black_imge.image.color = Color.New(1, 1, 1, 1)
	self.black_imge.image.raycastTarget = false
	local rect = self.black_imge.rect
	rect.anchorMin = Vector2(0, 0)
	rect.anchorMax = Vector2(1, 1)
	rect.anchoredPosition3D = Vector3(0, 0, 0)
	rect.sizeDelta = Vector2(0, 0)
	GuajiWGCtrl.Instance:StopGuaji(false)
end

function FadeView:ShowIndexCallBack()
	-- local time = 1 or index
	self.black_imge.image.color = Color.New(0, 0, 0, 1)
	local tween = self.black_imge.image:DOColor(Color.New(0, 0, 0, 0), 3)
	tween:OnComplete(function ()
		self:Close()
	end)
end

function FadeView:CloseCallBack()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end