-------------------------------------------------------------------------------------------------------------------
--升级宝石提示
-------------------------------------------------------------------------------------------------------------------
EquipmentBaoShiUpgradeView = EquipmentBaoShiUpgradeView or BaseClass(SafeBaseView)
function EquipmentBaoShiUpgradeView:__init()
	self:SetMaskBg(true)

	self:LoadConfig()
end

function EquipmentBaoShiUpgradeView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_baoshi_upgrade_tips")
end

function EquipmentBaoShiUpgradeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Equip.GoToUpGradeBaoShi
	self.left_cell = ItemCell.New(self.node_list.ph_cell_1)
	self.right_cell = ItemCell.New(self.node_list.ph_cell_2)

	self.stone_list_view = AsyncListView.New(EquipBlockRender, self.node_list["have_stone_list"])
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.Close, self))
	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnUpgradeClick,self))

	EquipmentWGCtrl.Instance:InitBaoShiUpGradeAlertTips(BindTool.Bind(self.AlertOkFnc, self))
end

function EquipmentBaoShiUpgradeView:ReleaseCallBack()
	if self.left_cell then
		self.left_cell:DeleteMe()
		self.left_cell = nil
	end

	if self.right_cell then
		self.right_cell:DeleteMe()
		self.right_cell = nil
	end

	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end

	self.enough_price = nil
	self.still_need_price = nil
	self.need_stone_desc = nil
end

function EquipmentBaoShiUpgradeView:ShowIndexCallBack()
end

function EquipmentBaoShiUpgradeView:AlertOkFnc()
	if self.enough_price then
		EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.UP, self.item_index, self.select_index, 1)
		self:Close()
	else
		self:Close()
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function EquipmentBaoShiUpgradeView:SetData(item_index, select_index)
	self.item_index = item_index      -- equip_body_index
	self.select_index = select_index  -- slot_index
end

--设置价格
function EquipmentBaoShiUpgradeView:SetPrice( price )
	self.node_list.lbl_price.text.text = price
end

function EquipmentBaoShiUpgradeView:SetLeftCellData( item_id )
	local data_instance = EquipmentWGData.Instance
	local name_str, attr_str = data_instance:GetBaoShiNatrue(item_id)
	self.node_list.lbl_name_1.text.text = name_str
	self.node_list.lbl_attr_1.text.text = attr_str
	self.left_cell:SetData({item_id = item_id})
end

function EquipmentBaoShiUpgradeView:SetRightCellData( item_id )
	local data_instance = EquipmentWGData.Instance
	local name_str, attr_str = data_instance:GetBaoShiNatrue(item_id)
	self.node_list.lbl_name_2.text.text = name_str
	self.node_list.lbl_attr_2.text.text = attr_str
	self.right_cell:SetData({item_id = item_id})
end

function EquipmentBaoShiUpgradeView:OnFlush()
	local equipdata_instance = EquipmentWGData.Instance

	local old_item_id = equipdata_instance:GetBaoShiItemIdBySelectIndex(self.item_index, self.select_index)
	local old_item_cfg = equipdata_instance:GetBaoShiLevelUpCfgByOldId(old_item_id)
	local new_item_id = old_item_cfg and old_item_cfg.new_stone_item_id or old_item_id
	self.still_need_price = equipdata_instance:GetBaoShiUpgradePrice(self.item_index, self.select_index)
	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	local bind_gold = 0--GameVoManager.Instance:GetMainRoleVo().bind_gold
	self.enough_price = role_gold + bind_gold >= self.still_need_price

	local need_stone_desc = equipdata_instance:CalcUpgradeNeedStoneStr(old_item_id)
	local cost_baoshi = ""
	if need_stone_desc then
		local and_str = self.still_need_price > 0 and Language.Equip.And or ""
		cost_baoshi = and_str .. need_stone_desc
	end

	local tips_str = ""
	if self.still_need_price > 0 then
		tips_str = string.format(Language.Equip.TipsContent1, self.still_need_price, cost_baoshi)
	else
		tips_str = string.format(Language.Equip.TipsContent0, cost_baoshi)
	end
	EquipmentWGCtrl.Instance:SetBaoShiUpGradeAlertTips(self.still_need_price > 0, tips_str)

	self:SetPrice(self.still_need_price) --设置升级价格
	self:SetLeftCellData(old_item_id)
	self:SetRightCellData(new_item_id)

	local stone_list = EquipmentWGData.Instance:GetInBagSameTypeStoneList(self.item_index, self.select_index)
	if self.stone_list_view then
		self.stone_list_view:SetDataList(stone_list)
		self.stone_list_view:CancelSelect()
	end

	self.node_list.stonetip:SetActive(IsEmptyTable(stone_list))
end

function EquipmentBaoShiUpgradeView:OnUpgradeClick()
	EquipmentWGCtrl.Instance:OpenBaoShiUpGradeAlertTips(self.still_need_price > 0)
end

-------------------------------------------------------------------------------------------------------------------
EquipBlockRender = EquipBlockRender or BaseClass(BaseRender)
function EquipBlockRender:__init()
	self.show_item = ItemCell.New(self.node_list.item_node)
end

function EquipBlockRender:__delete()
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function EquipBlockRender:OnFlush()
	self.show_item:SetData(self.data)
end
