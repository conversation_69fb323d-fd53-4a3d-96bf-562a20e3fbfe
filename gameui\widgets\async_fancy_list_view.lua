local flush_type =
{
	refresh = 1,
	refresh_all_act_cells = 2,
	select_index = 3,
	flush_cell = 4,
	flush_cell_data = 5,
}

local flush_type_list = {}
for k,v in pairs(flush_type) do
	flush_type_list[v] = k
end

---
---基类 不可直接使用
---
AsyncFancyListView = AsyncFancyListView or BaseClass()

function AsyncFancyListView:__init(item_render, list_view)
	if nil == item_render or nil == list_view then
		print_error("[AsyncFancyListView] 请指定正确的参数, item_render, list_view")
	end

	self.data_list = nil
	self.cell_list = {}
	self.list_view = list_view
	self.item_render = item_render
	self.select_callback = nil

	self.start_zero = false 							-- 是否从0开始
	self.default_select_index = 1
	self.cur_select_index = nil
	self.jump_to_select_cell_index = nil
	self.flush_param_t = {}
	self.on_new_cell_call_back = nil
	self.is_delay_flush = true
	self.is_show_item_special_title_ui = false
end

function AsyncFancyListView:__delete()
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.data_list = nil
	self.cell_list = {}
	self.select_callback = nil
	self.refresh_callback = nil
	self.is_delay_flush = true
	self.is_show_item_special_title_ui = false
end

---------------------------------------------------------------------------------------
-------------------------------------- Fancy版接口 --------------------------------------
---------------------------------------------------------------------------------------


-- 设置选中回调函数
function AsyncFancyListView:SetSelectCallBack(select_callback)
	self.select_callback = select_callback
end

---------------------------- 跳转 ----------------------------
-- 注意:使用非整数时 SelectPrevCell和SelectNextCell显示会有问题,但是可以使用SelectCell

-- 选择上一个 需要重写
function AsyncFancyListView:SelectPrevCell()
end

-- 选择下一个 需要重写
function AsyncFancyListView:SelectNextCell()
end

-- 跳转到指定index 需要重写
function AsyncFancyListView:SelectCell(index)
end

-- 跳转到指定index
function AsyncFancyListView:JumpToIndex(index)
	-- self:SelectCell(index)
	self.list_view.fancy_scroller:JumpTo(index)
end

-- 跳转到指定进度附近的index
-- percent:[0-1]
function AsyncFancyListView:JumptToIndexByPrecent(percent)
	local index = math.floor(self:GetListViewNumbers() * percent)
	self:SelectCell(index)
end

-- 跳转到指定进度
-- percent:[0-1]
-- 不能和Prev和Next混用 这两个是根据Content的index判断的,该接口不会更新index
function AsyncFancyListView:JumptToPrecent(percent)
	self:__Flush(flush_type.refresh, {1, self:GetListViewNumbers() * percent})
end

-- 跳转到顶部
function AsyncFancyListView:JumpToTop()
	self:__Flush(flush_type.refresh, {1, 0})
end

--获得当前选择的index
function AsyncFancyListView:GetSelectIndex()
	if nil ~= self.jump_to_select_cell_index then
		return self.jump_to_select_cell_index
	end

	return self.cur_select_index
end

-- 设置默认选中index
function AsyncFancyListView:SetDefaultSelectIndex(index)
	self.default_select_index = index
end

-- 设置当前选中index，有回调
function AsyncFancyListView:SelectIndex(index)
	if nil == self.data_list[index] then
		return
	end

	self.jump_to_select_cell_index = index
	self.cur_select_index = index
	self:__Flush(flush_type.select_index, {})
end

---------------------------- 数据 ----------------------------

-- 数据开始下标是否0开始
function AsyncFancyListView:SetStartZeroIndex(bool)
	self.start_zero = bool
	self.default_select_index = bool and 0 or self.default_select_index
end

-- 设置数据源(异步刷新数据 ) 需要重写
function AsyncFancyListView:SetDataList(data_list)
end

-- 获得数据源
function AsyncFancyListView:GetDataList()
	return self.data_list
end

--获得格子数(数据长度)
function AsyncFancyListView:GetListViewNumbers()
	local length = 0
	if not IsEmptyTable(self.data_list) then
		length = self.start_zero and #self.data_list + 1 or #self.data_list
	end

	return length
end

--获取当前选中的格子的数据
function AsyncFancyListView:GetCurItemData()
	return self.data_list[self:GetSelectIndex()]
end

-- 获取index对应的数据
function AsyncFancyListView:GetCellData(cell_index)
	return self.data_list[cell_index]
end

---------------------------- 刷新\回调 ----------------------------

-- 当选中某个index时
function AsyncFancyListView:__OnSelectIndex(cell_index, is_default, is_click)
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end
	if self.limit_select_func and self.limit_select_func() then
		return
	end

	for k, v in pairs(self.cell_list) do
		v:SetSelectIndex(cell_index)
	end

	if nil ~= self.select_callback and nil ~= cell_index then
		local item = self:GetItemAt(cell_index)
		if nil ~= item then
			self.select_callback(item, cell_index, is_default, is_click)
		end
	end
end

--cell选择回调
function AsyncFancyListView:OnSelectIndex(cell_index)
	if not self.start_zero then
		cell_index = cell_index + 1
	end
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end
	if self.limit_select_func and self.limit_select_func() then
		return
	end

	self.cur_select_index = cell_index
	self:__OnSelectIndex(self.cur_select_index, false, true)
end

--刷新格子
function AsyncFancyListView:__RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]

	if not self.start_zero then
		cell_index = cell_index + 1
	end

	if not item_cell then
		item_cell = self.item_render.New(cell.gameObject)
		self.cell_list[cell] = item_cell

		local toggle = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Toggle))
		local button = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Button))

		if toggle then
			item_cell:SetToggleGroup(self.list_view.toggle_group)
			toggle:AddClickListener(BindTool.Bind(self.ListEventCallback, self, item_cell))
		elseif button then
			button:AddClickListener(BindTool.Bind(self.ListEventCallback, self, item_cell))
		end

		if self.on_new_cell_call_back ~= nil then
			self.on_new_cell_call_back(item_cell)
		end

		if self.is_show_item_special_title_ui then
			item_cell:IsShowSpecialTitleUI(true)
		end
	end

	local item_data = self.data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetData(item_data)
	item_cell:SetSelectIndex(self.cur_select_index)

	if self.refresh_callback then
		self.refresh_callback(item_cell, cell_index)
	end

	-- self:__TrySelectIndex(cell_index)
end


--list事件回调
function AsyncFancyListView:ListEventCallback(item_cell)
	local cell_index = item_cell:GetIndex()
	if self:IsLimitSelectByIndex(cell_index) then
		return
	end

	if self.limit_select_func and self.limit_select_func() then
		return
	end

	self.cur_select_index = cell_index
	self:__OnSelectIndex(self.cur_select_index, false, true)
end

---------------------------- 其他 ----------------------------

-- 设置限制选中方法 需要是否限制返回值
function AsyncFancyListView:SetLimitSelectFunc(func)
	self.limit_select_func = func
end

-- 限制选中接口（可重写）
function AsyncFancyListView:IsLimitSelectByIndex(cell_index)
	return false
end

-- 获得某个索引下的item (cell复用导致index错乱 慎用！)
-- item_render 是或继承ItemCell的 【不要用】  （item_cell 会对data进行改变
function AsyncFancyListView:GetItemAt(cell_index)
	for k, v in pairs(self.cell_list) do
		if v:GetIndex() == cell_index and v:GetData() == self.data_list[cell_index] then
			return v
		end
	end
	return nil
end


---------------------------------------------------------------------------------------

-------------------------------------- 原list_view旧接口(不一定有效和可以优化) ------------------------------ 
---------------------------------------------------------------------------------------


-- List嵌套list的时候，如果里面的List是物品，在延迟调用的情况下会闪，暂时没查到问题，先临时处理,加个标记忽略延时
function AsyncFancyListView:SetIsDelayFlush(value)
	self.is_delay_flush = value
end



-- 因为__RefreshListViewCells的刷新并不一定是在调了reload后就同步
-- 比如scroll有时是在缓动到指定位置后再调__RefreshListViewCells
function AsyncFancyListView:__TrySelectIndex(cell_index)
	local is_select_cell_index = false
	if nil ~= self.jump_to_select_cell_index then
		is_select_cell_index = cell_index == self.jump_to_select_cell_index
	elseif nil ~= self.default_select_index then
		is_select_cell_index = cell_index == self.default_select_index
	end

	if not is_select_cell_index then
		return
	end

	local is_default = false
	if nil ~= self.jump_to_select_cell_index then
		self.cur_select_index = self.jump_to_select_cell_index
	elseif nil ~= self.default_select_index then
		self.cur_select_index = self.default_select_index
		is_default = true
	end

	self.jump_to_select_cell_index = nil
	self.default_select_index = nil
	self:__OnSelectIndex(self.cur_select_index, is_default, false)
end








-- 当面板能看到全部item的时候才用
-- 仅用于引导那(此接口有风险，不要再用。兼容旧代码)
function AsyncFancyListView:GetAllItems()
	local show_item_list = {}
	for k, v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy then
			show_item_list[v:GetIndex()] = v
		end
	end
	return show_item_list
end

-- 获得根据index排序后的celllist, is_sub降序排列
function AsyncFancyListView:GetSortCellList(is_sub)
	local temp_list = {}
	local seq = self.start_zero and 1 or 0
	for k,v in pairs(self.cell_list) do
		temp_list[v:GetIndex() + seq] = v
	end

	return SortTableKey(temp_list, is_sub)
end

-- 清除所有item
function AsyncFancyListView:RemoveAllItem()
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

-- 取消选中(兼容旧代码)
function AsyncFancyListView:CancelSelect()
	self.jump_to_select_cell_index = nil
	self.cur_select_index = nil
	self:__Flush(flush_type.select_index, {})
end

-- 刷新某个格子
function AsyncFancyListView:FlushCell(index, key, value_t)
	self:__Flush(flush_type.flush_cell, {index, key, value_t})
end

-- 刷新某个格子数据数据（兼容旧代码，不再建议用）
function AsyncFancyListView:FlushCellData(index, cell_data)
	self:__Flush(flush_type.flush_cell_data, {index, cell_data})
end

-- 设置回调
function AsyncFancyListView:SetCellSizeDel(callback)
	if not self.list_view then return end
	local list_delegate = self.list_view.list_simple_delegate
	list_delegate.CellSizeDel = callback
end

--list刷新回调
function AsyncFancyListView:SetRefreshCallback(refresh_callback)
	self.refresh_callback = refresh_callback
end


function AsyncFancyListView:__DoRefresh(refresh_type, percent)
	if IsNil(self.list_view.fancy_scroller) then
		return
	end

	-- 因为设置的是animtor的进度,并且需要更新所有创建出来的item,所以不区分refresh_type
	self.list_view.fancy_scroller:UpdatePosition(percent or 0)

	self:__CheckRefreshCall()
	-- print_error("###############__DoRefresh>>>>>>>>>>>>", refresh_type, percent, self.cur_select_index, self.jump_to_select_cell_index)
end

function AsyncFancyListView:__CheckRefreshCall()
	local now_frame = UnityEngine.Time.frameCount
	self.last_refresh_frame = self.last_refresh_frame or now_frame
	self.refresh_times_in_frame = self.refresh_times_in_frame or 0
	if self.last_refresh_frame ~= UnityEngine.Time.frameCount then
		self.last_refresh_frame = now_frame
		self.refresh_times_in_frame = 1
	else
		self.refresh_times_in_frame = self.refresh_times_in_frame + 1
		if self.refresh_times_in_frame > 1 then
			print_error(string.format("[AsyncFancyListView] 在1帧里执行了%s次__DoRefresh，请整理代或使用异步接口", self.refresh_times_in_frame))
		end
	end
end

function AsyncFancyListView:__DoFlushCell(index, key, value_t)
	local cell = self:GetItemAt(index)
	if nil ~= cell then
		cell:Flush(key, value_t)
	end
end

function AsyncFancyListView:__DoFlushCellData(index, data)
	local cell = self:GetItemAt(index)
	if nil ~= cell then
		cell:SetData(data)
	end
end

function AsyncFancyListView:__Flush(key, value)
	self.flush_param_t[key] = value
	if self.is_delay_flush then
		TryDelayCall(self, function ()
			self:__OnFlush()
		end, 0, "flush")
	else
		self:__OnFlush()
	end
end

function AsyncFancyListView:__OnFlush()
	for i,_ in ipairs(flush_type_list) do
		local v = self.flush_param_t[i]
		if nil ~= v then
			if i == flush_type.refresh then
				self:__DoRefresh(v[1], v[2])
				self.flush_param_t[flush_type.refresh_all_act_cells] = nil
				self.flush_param_t[flush_type.select_index] = nil
				self.flush_param_t[flush_type.flush_cell] = nil
				self.flush_param_t[flush_type.flush_cell_data] = nil
			end

			if i == flush_type.refresh_all_act_cells then
				self:__DoRefresh(2, 0)
			end

			if i == flush_type.select_index then
				self:__OnSelectIndex(self.cur_select_index, false, false)
			end

			if i == flush_type.flush_cell then
				self:__DoFlushCell(v[1], v[2], v[3])
			end

			if i == flush_type.flush_cell_data then
				self:__DoFlushCellData(v[1], v[2])
			end
		end
	end

	self.flush_param_t = {}
end

function AsyncFancyListView:SetCreateCellCallBack(callback)
	self.on_new_cell_call_back = callback
end

--ItemCell专用，设置特殊title ui
function AsyncFancyListView:SetIsShowItemSpecialTitleUI(is_show_item_special_title_ui)
	self.is_show_item_special_title_ui = is_show_item_special_title_ui
end

function AsyncFancyListView:RefreshActiveCellViews()
	self:__Flush(flush_type.refresh_all_act_cells, {})
end

