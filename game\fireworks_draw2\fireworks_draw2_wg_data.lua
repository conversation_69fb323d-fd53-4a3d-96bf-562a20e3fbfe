FireWorksDrawSecondWGData = FireWorksDrawSecondWGData or BaseClass()

function FireWorksDrawSecondWGData:__init()
	if FireWorksDrawSecondWGData.Instance then 
		ErrorLog("[FireWorksDrawSecondWGData] Attemp to create a singleton twice !")
	end

	FireWorksDrawSecondWGData.Instance = self

    --烟花抽奖2
    local fireworks_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_fireworks_draw2_auto")
	self.fireworks_mode_cfg = fireworks_cfg.mode --抽奖模式
	self.fireworks_reward_pool_cfg = ListToMapList(fireworks_cfg.reward_pool, "grade", "activity_day") --奖池
	self.fireworsk_gailv_cfg = ListToMapList(fireworks_cfg.item_random_desc, "grade", "activity_day") --概率展示
	self.fireworks_baodi_cfg = ListToMapList(fireworks_cfg.baodi, "grade", "activity_day") --保底奖池
	self.fireworks_other_cfg = fireworks_cfg.other --抽奖道具

    self.fireworks_result_info = {}
	RemindManager.Instance:Register(RemindName.FireWorksDrawSecond, BindTool.Bind(self.GetFireWorksDrawRed, self)) --烟花抽奖2

    --背包道具红点 -烟花抽奖
    self:RegisterFireworksRemindInBag(RemindName.FireWorksDrawSecond)
end


function FireWorksDrawSecondWGData:__delete()
	FireWorksDrawSecondWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.FireWorksDrawSecond)
end

function FireWorksDrawSecondWGData:SetFireWorksInfo(protocol)
	self.fireworks_grade = protocol.grade
	self.fireworks_times = protocol.draw_times
end

--抽奖次数
function FireWorksDrawSecondWGData:GetCurDrawTimes()
    return self.fireworks_times or 0
end


function FireWorksDrawSecondWGData:GetFireWorksGrade()
	return self.fireworks_grade or -1
end

function FireWorksDrawSecondWGData:SetFireWorksResultInfo(protocol)
	self.fireworks_result_info.mode = protocol.mode
	self.fireworks_result_info.count = protocol.count
	self.fireworks_result_info.record_list = protocol.record_list
end

--烟花抽奖结果信息
function FireWorksDrawSecondWGData:GetFireWorksResultInfo()
	return self.fireworks_result_info
end

--抽奖模式
function FireWorksDrawSecondWGData:GetFireWorksDrawConsumeCfg()
    return self.fireworks_mode_cfg
end

--抽奖道具
function FireWorksDrawSecondWGData:GetFireWorksDrawItem()
	return self.fireworks_other_cfg[1]
end

--抽奖道具list
function FireWorksDrawSecondWGData:GetFireWorksItemDataChangeList()
    if not self.fireworks_item_change_list then
        self.fireworks_item_change_list = {}
		table.insert(self.fireworks_item_change_list, self.fireworks_other_cfg[1].cost_item_id)
    end
    return self.fireworks_item_change_list
end

--主界面红点
function FireWorksDrawSecondWGData:GetFireWorksDrawRed()
    --背包有道具
    if self:GetFireWorksBagItem() then
        return 1
    end

    return 0
end

--是否有道具
function FireWorksDrawSecondWGData:GetFireWorksBagItem()
    local item_list = self:GetFireWorksItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--获取抽奖的选项
function FireWorksDrawSecondWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

--抽奖概率展示
function FireWorksDrawSecondWGData:GetFireWorksProbabilityInfo()
	local grade = self:GetFireWorksGrade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2)
	--print_error(grade, act_day)
	return self.fireworsk_gailv_cfg[grade] and (self.fireworsk_gailv_cfg[grade][act_day] or {}) or {}
end

--保底抽奖次数配置
function FireWorksDrawSecondWGData:GetFireWorksBaoDiDrawTimesCfg()
	local grade = self:GetFireWorksGrade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2)
	--print_error(grade, act_day)
	return self.fireworks_baodi_cfg[grade] and (self.fireworks_baodi_cfg[grade][act_day] or {}) or {}
end

--保底抽奖次数显示
function FireWorksDrawSecondWGData:GetFireWorksBaoDiDrawTimes()
	local cfg = self:GetFireWorksBaoDiDrawTimesCfg()
	local draw_times = self:GetCurDrawTimes()
	local cfg_times = cfg[#cfg] and cfg[#cfg].times or 0

	if draw_times >= cfg_times then
		return -1
	end

	return cfg_times - draw_times
end

--珍稀奖励展示配置
function FireWorksDrawSecondWGData:GetFireWorksGradeInfo(grade, act_day)
	return self.fireworks_reward_pool_cfg[grade] and (self.fireworks_reward_pool_cfg[grade][act_day] or {}) or {}
end

--奖励展示
function FireWorksDrawSecondWGData:GetFireWorksRewardShowCfg()
	local show_list = {}
	local grade = self:GetFireWorksGrade()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2)
	--print_error(grade, day)
	local data_list = self:GetFireWorksGradeInfo(grade, day)
	if data_list == nil then
		return
	end

	for k, v in pairs(data_list) do
		if v.is_rare == 1 then --珍稀奖励
			table.insert(show_list, v)
		end
	end

	return show_list
end

--背包道具改变红点
function FireWorksDrawSecondWGData:RegisterFireworksRemindInBag(remind_name)
    local check_list = self:GetFireWorksItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end