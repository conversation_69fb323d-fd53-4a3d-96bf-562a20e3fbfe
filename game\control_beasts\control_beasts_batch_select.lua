-- 灵宠选择同星选择界面
ControlBeastsBatchSelect = ControlBeastsBatchSelect or BaseClass(SafeBaseView)

function ControlBeastsBatchSelect:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {vector2 = Vector2(0, -2), sizeDelta = Vector2(706, 484)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_batch_select")
    self.view_name = "ControlBeastsBatchSelect"
    self:SetMaskBg(true)
end

function ControlBeastsBatchSelect:ReleaseCallBack()
	if nil ~= self.batch_item_grid then
		self.batch_item_grid:DeleteMe()
    end
    self.batch_item_grid = nil
    self.show_data = nil
    self.ok_callback = nil
    self.need_num = nil
    self.cur_select_num = nil
end

function ControlBeastsBatchSelect:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName2
    
    self.batch_item_grid = BeastBatchGrid.New(self)
    self.batch_item_grid:SetStartZeroIndex(false)
    self.batch_item_grid:SetIsShowTips(false)
    self.batch_item_grid:SetNoSelectState(false)
    self.batch_item_grid:SetIsMultiSelect(true)                       --  ,change_cells_num = 2
    self.batch_item_grid:CreateCells({col = 8 ,change_cells_num = 2, cell_count = 32,
    list_view = self.node_list["ph_item_grid"], itemRender = BeastBatchSelectRender})
    self.batch_item_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))

    self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure, self))
    self.node_list["btn_one_key"].button:AddClickListener(BindTool.Bind(self.OnClickOneKey, self))
end

function ControlBeastsBatchSelect:SetOkBack(callback)
	self.ok_callback = callback
end

function ControlBeastsBatchSelect:SelectShenShouBagCellCallBack(cell)
	self:FlushAddNum()
end

function ControlBeastsBatchSelect:SetData(show_data)
    self.show_data = show_data

    if self:IsLoaded() then
        self:Flush()
    end
end

function ControlBeastsBatchSelect:OnFlush()
    self:FlushDataList()
    self:FlushAddNum()
end

-- 检测是否为已选择的特殊灵兽
function ControlBeastsBatchSelect:CheckisSpecial(bag_id, is_egg)
    if self.show_data and self.show_data.special_list then
        for _, special_data in ipairs(self.show_data.special_list) do
            if special_data.bag_id == bag_id and special_data.is_egg == is_egg then
                return true
            end
        end
    end

    return false
end

-- 检测是否为已选择的星级
function ControlBeastsBatchSelect:CheckisSameStar(bag_id, is_egg)
    if self.show_data and self.show_data.same_list then
        for _, special_data in ipairs(self.show_data.same_list) do
            if special_data.bag_id == bag_id and special_data.is_egg == is_egg then
                return true
            end
        end
    end

    return false
end

-- 获取其他已选中的列表
function ControlBeastsBatchSelect:GetNowSelectTable(list, select_table, aim_element)
    for element, star_data in pairs(list) do
        if star_data ~= nil and element ~= aim_element then
            for satr, star_list in pairs(star_data) do
                for _, beasts_data in pairs(star_list) do
                    if beasts_data.bag_id ~= nil then
                        select_table[beasts_data.bag_id] = true
                    elseif beasts_data.is_card then
                        if not select_table.select_card then
                            select_table.select_card = 0 
                        else
                            select_table.select_card = select_table.select_card + 1
                        end
                    end
                end
            end
        end
    end
end

-- 获取已选中的列表
function ControlBeastsBatchSelect:GetNowChooseTable(list, choose_table, aim_element)
    for element, star_data in pairs(list) do
        if star_data ~= nil and element == aim_element then
            for satr, star_list in pairs(star_data) do
                for _, beasts_data in pairs(star_list) do
                    if beasts_data.bag_id ~= nil then
                        choose_table[beasts_data.bag_id] = true
                    elseif beasts_data.is_card then
                        if not choose_table.select_card then choose_table.select_card = {} end

                        if not choose_table.select_card[satr] then
                            choose_table.select_card[satr] = 1 
                        else
                            choose_table.select_card[satr] = choose_table.select_card[satr] + 1
                        end
                    end
                end
            end
        end
    end
end

-- 刷新列表
function ControlBeastsBatchSelect:FlushDataList()
    if not self.show_data then
        return
    end

    -- 已选择的列表，不能重复选取
    local select_list = {}
    local choose_table = {}

    if self.show_data.special_list then
        for _, special_data in ipairs(self.show_data.special_list) do
            if self.show_data.is_special then
                choose_table[special_data.bag_id] = true
            else
                select_list[special_data.bag_id] = true
            end
        end
    end

    if self.show_data.same_list then
        if self.show_data.is_special then
            local list = self.show_data.same_list[BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT] or {}
            self:GetNowSelectTable(list, select_list, nil)

            local list = self.show_data.same_list[BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR] or {}
            self:GetNowSelectTable(list, select_list, nil)
        elseif self.show_data.is_element then
            local list = self.show_data.same_list[BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT] or {}
            self:GetNowSelectTable(list, select_list, self.show_data.element)
            self:GetNowChooseTable(list, choose_table, self.show_data.element)

            local list = self.show_data.same_list[BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR] or {}
            self:GetNowSelectTable(list, select_list, nil)
        else
            local list = self.show_data.same_list[BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT] or {}
            self:GetNowSelectTable(list, select_list, nil)

            local list = self.show_data.same_list[BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR] or {}
            self:GetNowChooseTable(list, choose_table, BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT)
        end
    end

    local grid_list = {}
    local aim_beast_id = self.show_data.need_beast_id

    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.show_data.need_beast_id)
    if aim_cfg == nil then
        aim_beast_id = self.show_data.aim_beast_id
    end

    local beasts_list = ControlBeastsWGData.Instance:GetCanSelectBeastsList(
        aim_beast_id, 
        self.show_data.is_special, 
        self.show_data.is_element, 
        self.show_data.element,
        self.show_data.star_num,
        false, 
        select_list
    )

    for _, beasts_data in ipairs(beasts_list) do
        if beasts_data and beasts_data.beast_data and beasts_data.beast_data.server_data and beasts_data.beast_data.bag_id ~= self.show_data.aim_bag_id then
            table.insert(grid_list, beasts_data)
        end
    end

    -- for _, beasts_egg_data in ipairs(beasts_egg_list) do
    --     if beasts_egg_data and beasts_egg_data.beast_data and beasts_egg_data.beast_data.server_data then
    --         local cur_data = beasts_egg_data.beast_data
    --         if self.show_data.is_special then
    --             table.insert(grid_list, beasts_egg_data)
    --         else
    --             if not self:CheckisSpecial(cur_data.bag_id, beasts_egg_data.is_egg) then
    --                 table.insert(grid_list, beasts_egg_data)
    --             end
    --         end
    --     end
    -- end

    for index, grid_data in ipairs(grid_list) do
        if grid_data then
            local cur_data = grid_data.beast_data
            if choose_table[cur_data.bag_id] then
                self.batch_item_grid.select_tab[1][index] = true --选中之前选择的
            end
        end
    end

    if not self.show_data.is_special then
        if self.show_data.star_num ~= nil then
            local use_card_data = ControlBeastsWGData.Instance:GetBeastsComposeUseCard(self.show_data.star_num)

            if use_card_data then
                local use_card_id = use_card_data.item_id
                local use_card_num = ItemWGData.Instance:GetItemNumInBagById(use_card_id)

                if select_list.select_card ~= nil and choose_table.select_card and choose_table.select_card[self.show_data.star_num] then  -- 去掉已选择的万能卡
                    use_card_num = use_card_num - choose_table.select_card[self.show_data.star_num]
                end

                local used_card_num = choose_table.select_card and choose_table.select_card[self.show_data.star_num] or 0

                if use_card_num > 0 then
                    for i = 1, use_card_num do
                        local card_data = {}
                        card_data.item_id = use_card_id
                        card_data.is_card = true
                        table.insert(grid_list, card_data)

                        if i <= used_card_num then
                            self.batch_item_grid.select_tab[1][#grid_list] = true --选中已选择的万能卡
                        end
                    end
                end
            end
        end
    end


    local new_data = {}
    new_data.is_plus = true
    table.insert(grid_list, new_data)

    if #grid_list <= 1 then
        self.batch_item_grid:CancleAllSelectCell()
    end

    self.batch_item_grid:SetDataList(grid_list)
    self.node_list["ph_item_grid"].scroll_rect.verticalNormalizedPosition = 1
end

-- 刷新数量
function ControlBeastsBatchSelect:FlushAddNum()
    if not self.show_data then
        return
    end

    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.show_data.need_beast_id)
    if aim_cfg == nil then
        aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.show_data.aim_beast_id)
    end

    local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(self.show_data.aim_beast_id)
    local str = ""

    if (not starup_beast_ids) or (not aim_cfg) then
        return
    end

    if self.show_data.is_special then
        for i, data in ipairs(starup_beast_ids) do
            if data.beast_id ~= nil and data.beast_id == self.show_data.need_beast_id then
                self.need_num = data.num or 0
                break
            end
        end

        str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[aim_cfg.beast_color], 
        self.need_num, string.format(Language.ContralBeasts.SelectTips2, aim_cfg.beast_star, aim_cfg.beast_name))
    elseif self.show_data.is_element then
        local need_star = 0
        for i, data in ipairs(starup_beast_ids) do
            if data.element ~= nil and data.element == self.show_data.element and data.star == self.show_data.star_num then
                self.need_num = data.num or 0
                need_star = data.star or 0
                break
            end
        end

        str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[aim_cfg.beast_color], 
        self.need_num, string.format(Language.ContralBeasts.SelectTips1, need_star, Language.ContralBeasts.GMTypeList[self.show_data.element]))
    else
        local need_star = 0
        for i, data in ipairs(starup_beast_ids) do
            if data.element == nil and data.star ~= nil and data.star == self.show_data.star_num then
                self.need_num = data.num or 0
                need_star = self.show_data.star_num or 0
                break
            end
        end

        str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[aim_cfg.beast_color], 
        self.need_num, string.format(Language.ContralBeasts.SelectTips1, self.show_data.star_num, ""))
    end
    self.node_list.tip_text.text.text = str

    local selected_cells = self.batch_item_grid:GetAllSelectCell()
    self.cur_select_num = #selected_cells
    local color = self.cur_select_num == self.need_num and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.tip_text.text.text = string.format("%s%s", str, string.format(Language.MountPetEquip.HadSelect, color, self.cur_select_num, self.need_num)) 
end

-- 获取数量
function ControlBeastsBatchSelect:GetNeedNum()
    return self.need_num
end

-- 一键选取
function ControlBeastsBatchSelect:OnClickOneKey()
    if not self.need_num then
        if self.show_data then
            local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.show_data.need_beast_id)
            if aim_cfg == nil then
                aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.show_data.aim_beast_id)
            end
   
            local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(self.show_data.aim_beast_id)
            if starup_beast_ids and aim_cfg then
                if self.show_data.is_special then
                    for i, data in ipairs(starup_beast_ids) do
                        if data.beast_id ~= nil and data.beast_id == self.show_data.need_beast_id then
                            self.need_num = data.num or 0
                            break
                        end
                    end
                elseif self.show_data.is_element then
                    local need_star = 0
                    for i, data in ipairs(starup_beast_ids) do
                        if data.element ~= nil and data.element == self.show_data.element and data.star == self.show_data.star_num then
                            self.need_num = data.num or 0
                            break
                        end
                    end
                else
                    local need_star = 0
                    for i, data in ipairs(starup_beast_ids) do
                        if data.star ~= nil and data.star == self.show_data.star_num then
                            self.need_num = data.num or 0
                            break
                        end
                    end
                end
            else
                self.need_num = 0
            end
        else
            self.need_num = 0
        end
    end

    local item_list = self.batch_item_grid:GetDataList()
    local cur_count = #item_list - 1                        --去掉显示加号的数据
    if cur_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.UpstarNotEnough)
        return
    end

    if self.need_num > cur_count then
        --数量不够
        self.batch_item_grid:SetMultiSelectEffect(cur_count)
    else
        self.batch_item_grid:SetMultiSelectEffect(self.need_num)
    end
    self:FlushAddNum()
end

-- 关闭回调
function ControlBeastsBatchSelect:CloseCallBack()
    if self.batch_item_grid then
        self.batch_item_grid:CancleAllSelectCell()
    end
end

-- 选择完成
function ControlBeastsBatchSelect:OnClickSure()
    local data_list = self.batch_item_grid:GetAllSelectCell()
    if self.ok_callback then
        self.ok_callback(self.show_data.is_special, self.show_data.is_element, self.show_data.element, self.show_data.star_num, data_list)
    end

    self:Close()
end

-------------------------------------------------------------------------------------------------

BeastBatchSelectRender = BeastBatchSelectRender or BaseClass(ItemCell)
function BeastBatchSelectRender:__init()
	self:UseNewSelectEffect(true)
end

function BeastBatchSelectRender:OnClick()
    self:UseNewSelectEffect(true)
    if self.data then
        if self.data.is_plus then
            local pos = self:GetPos()
            -- print_error("展示获取途径")
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.SelectTips3)
        else
            ItemCell.OnClick(self)
        end
    end
end

function BeastBatchSelectRender:CheckIsBestColor()
    if self.data and self.data.aim_bag_id then
        
    end

    return false
end

function BeastBatchSelectRender:GetPos()
    if nil ==  self.view then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
	if nil == main_view or not main_view:IsOpen() then
		return
	end
    local parent_rect = main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x
    y = local_position_tbl.y
    return Vector2(x + 220, y)
end


function BeastBatchSelectRender:OnFlush()
    if IsEmptyTable(self.data) then
        self:Reset()
		return
    end
    self:SetIgnoreDataToSelect(self.data.is_plus)
    ItemCell.OnFlush(self)
    if self.data.is_plus then
        self:Reset()
        local bundle, asset = ResPath.GetCommonImages("a2_ty_jia")
        self:SetItemIcon(bundle, asset)
        self:SetButtonComp(true)
        self:SetEffectRootEnable(false)
        ItemCell.SetSelectEffect(self, false)
    end

    -- if not self.data.is_egg and self.data.beast_data then
    --     local server_data = self.data.beast_data.server_data
    --     if server_data then
    --         local flair_score, score_index = ControlBeastsWGData.Instance:GetFlairScoreByServerData(server_data)
    --         ItemCell.SetBeastFlairScoreIcon(self, score_index)
    --         ItemCell.SetRightTopImageText(self, server_data.beast_level)
    --     end
    -- end
end

function BeastBatchSelectRender:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return 
    end
    self:UseNewSelectEffect(true)
	ItemCell.SetSelectEffect(self, is_select)	
end



BeastBatchGrid = BeastBatchGrid or BaseClass(ShenShouGrid)

function BeastBatchGrid:__init(parent)
    self.parent_view = parent
    AsyncBaseGrid.__init(self)
end


function BeastBatchGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
    self.cell_list = {}
    self.parent_view = nil
end

function BeastBatchGrid:SetOtherFullSelectTips(other_tips)
    self.other_full_select_tips = other_tips
end

-- 选择某个格子回调
function BeastBatchGrid:SelectCellHandler(cell)
    local select_num = #self:GetAllSelectCell()
	self.cur_index = cell:GetIndex()
    local cell_index = self.cur_index
    if self.is_multi_select then
        if IsEmptyTable(cell:GetData()) or cell:GetData().item_id == 0 then
            return
        end
        if not self.select_tab[1][cell_index] then
            if select_num == self.parent_view:GetNeedNum() then
                if self.other_full_select_tips and self.other_full_select_tips ~= "" then
                    SysMsgWGCtrl.Instance:ErrorRemind(self.other_full_select_tips)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.HadSelectEnough)
                end
             
                return
            end

            if self.parent_view.GridCellSelectLimit and self.parent_view:GridCellSelectLimit(cell) then
                return
            end

			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
			end
			self.select_tab[1][cell_index] = true
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil
		else
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
            end
            if select_num == self.parent_view:GetNeedNum() then
                if self.other_full_select_tips and self.other_full_select_tips ~= "" then
                    SysMsgWGCtrl.Instance:ErrorRemind(self.other_full_select_tips)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.HadSelectEnough)
                end
                return
            end

            if self.parent_view.GridCellSelectLimit and self.parent_view:GridCellSelectLimit(cell) then
                return
            end
			self.select_tab[1][cell_index] = true
		end
    else
		for k, v in pairs(self.select_tab[1]) do
			if cell_index == k then
				return
			end
		end

		self.select_tab[1] = {}
		self.select_tab[1][cell_index] = true
	end

	if nil ~= self.select_callback then
		self.select_callback(cell)
	end

	-- self.list_view.scroller:RefreshActiveCellViews()
	self:RefreshSelectCellState()
end

function BeastBatchGrid:SetMultiSelectEffect(num)
	self.select_tab[1] = {}
	for i=1,num do
		self.select_tab[1][i] = true
	end
	self:RefreshSelectCellState()
end