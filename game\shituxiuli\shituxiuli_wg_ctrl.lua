require("game/shituxiuli/shituxiuli_wg_data")
require("game/shituxiuli/shituxiuli_view")
require("game/shituxiuli/shituxiuli_help_view")

ShiTuXiuLiWGCtrl = ShiTuXiuLiWGCtrl or BaseClass(BaseWGCtrl)

CS_TEACHERSTUDENT_TYPE =
{
	CS_TEACHERSTUDENT_TYPE_REQ_STUDY = 0,				-- 拜师
	CS_TEACHERSTUDENT_TYPE_ACCEPT_STUDENT = 1,			-- 收徒
	CS_TEACHERSTUDENT_TYPE_TEACHER_LOGIN_REWARD = 2,	-- 领取师傅登陆奖励
	CS_TEACHERSTUDENT_TYPE_REQ_HELP = 3,				-- 请求协助
	CS_TEACHERSTUDENT_TYPE_STUDY_TASK_REWARD = 4,		-- 师傅邻取学徒任务奖励 taskid
	CS_TEACHERSTUDENT_TYPE_STUDY_LOGIN_REWARD = 5,		-- 学徒领取登陆奖励
	CS_TEACHERSTUDENT_TYPE_WORLD_STUDENT_INFO = 6,		-- 世界拜师信息
	CS_TEACHERSTUDENT_TYPE_WORLD_SUCCESS_NODE_INFO = 7,	-- 世界拜师成功信息
	CS_TEACHERSTUDENT_TYPE_STUDENT_VIP_TITLE_REWARD = 8,-- 领取徒弟称号奖励
	CS_TEACHERSTUDENT_TYPE_INFO = 9,					-- 信息请求
	CS_TEACHERSTUDENT_TYPE_ACCEPT_HELP = 10,			-- 师傅回应协助 taskid
	CS_TEACHERSTUDENT_TYPE_NOTIFY_ACP_STUDENT = 11,		-- 广播收徒
	CS_TEACHERSTUDENT_TYPE_WORLD_TEACHER_INFO = 12,		-- 世界收徒信息
}

function ShiTuXiuLiWGCtrl:__init()
	if ShiTuXiuLiWGCtrl.Instance then
		error("[ShiTuXiuLiWGCtrl]:Attempt to create singleton twice!")
		return
	end
	ShiTuXiuLiWGCtrl.Instance = self

	self.data = ShiTuXiuLiWGData.New()
	self.view = ShiTuXiuLiView.New(GuideModuleName.ShiTuXiuLi)
	self.help_view = ShiTuXiuLiHelpView.New()

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChange, self))
	self:RegisterAllProtocals()
end

function ShiTuXiuLiWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	ShiTuXiuLiWGCtrl.Instance = nil
end

function ShiTuXiuLiWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSTeacherStudentOpera)
	self:RegisterProtocol(SCTeacherStudentBaseInfo, "ReceiveSCTeacherStudentBaseInfo")
	self:RegisterProtocol(SCTSStudentTaskInfo, "ReceiveSCTSStudentTaskInfo")
	self:RegisterProtocol(SCTSStudentHelpTaskInfo, "ReceiveSCTSStudentHelpTaskInfo")
	self:RegisterProtocol(SCTSAllStudentInfo, "ReceiveSCTSAllStudentInfo")
	self:RegisterProtocol(SCTSAllSuccessNodeInfo, "ReceiveSCTSAllSuccessNodeInfo")
	self:RegisterProtocol(SCTSAllTeacherInfo, "OnSCTSAllTeacherInfo")
end

-- 8240
function ShiTuXiuLiWGCtrl:RequestShiTuXiuLi(req_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeacherStudentOpera)
	protocol.opera_type = req_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- 8241 师傅和徒弟的基础信息
function ShiTuXiuLiWGCtrl:ReceiveSCTeacherStudentBaseInfo(protocol)
	self.data:SetTeacherStudentBaseInfo(protocol)
	self.view:Flush(0, "base_info", {protocol_id = 8241})
	self:CheckActIsOpen()
	RemindManager.Instance:Fire(RemindName.ShiTuXiuLiMain)
	self:CheckNeedCloseAct()
end

-- 8242 徒弟任务信息 这个刷的很频繁
function ShiTuXiuLiWGCtrl:ReceiveSCTSStudentTaskInfo(protocol)
	self.data:SetStudentTaskInfo(protocol)
	self.view:Flush(0, "task_info", {protocol_id = 8242})
	RemindManager.Instance:Fire(RemindName.ShiTuXiuLiMain)
	self:CheckNeedCloseAct()
end

-- 8243 师傅和徒弟的求助任务信息
function ShiTuXiuLiWGCtrl:ReceiveSCTSStudentHelpTaskInfo(protocol)
	self.data:SetStudentHelpInfo(protocol)
	self.help_view:Flush(0, "help_info", {protocol_id = 8243})
	self:CheckStudentHelp()
end

-- 8244 所有求师信息
function ShiTuXiuLiWGCtrl:ReceiveSCTSAllStudentInfo(protocol)
	self.data:SetAllStudentInfo(protocol)
	self.view:Flush(0, "student_info", {protocol_id = 8244})
	RemindManager.Instance:Fire(RemindName.ShiTuXiuLiMain)
end

-- 8245 成功结拜传闻
function ShiTuXiuLiWGCtrl:ReceiveSCTSAllSuccessNodeInfo(protocol)
	self.data:SetAllSuccessNodeInfo(protocol)
	self.view:Flush(0, "jiebai_info", {protocol_id = 8245})
end

function ShiTuXiuLiWGCtrl:MainuiOpenCreateCallBack()
	self:CheckActIsOpen()
end

function ShiTuXiuLiWGCtrl:DayChange()
	self:CheckActIsOpen()
	ShiTuXiuLiWGCtrl.Instance:RequestShiTuXiuLi(CS_TEACHERSTUDENT_TYPE.CS_TEACHERSTUDENT_TYPE_WORLD_STUDENT_INFO) -- 上线请求一次求师列表用来显示红点
end

function ShiTuXiuLiWGCtrl:CheckActIsOpen()
	local base_info = self.data:GetTeacherStudentBaseInfo()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.SHITUXIULI)
	if not MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) and base_info and base_info.close_view_flag == 0 and not ShiTuXiuLiWGData.Instance:ActCanClose() then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHITUXIULI, ACTIVITY_STATUS.OPEN)
	else
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHITUXIULI, ACTIVITY_STATUS.CLOSE)
	end
end

function ShiTuXiuLiWGCtrl:CheckStudentHelp()
	local help_info = self.data:GetStudentHelpInfo()
	local student_help_info = help_info and help_info.student_help_info
	if not student_help_info or #student_help_info == 0 then
		return
	end
	local need_tip = false
	for i=1,#student_help_info do
		if student_help_info[i].teacher_is_help == 0 then
			need_tip = true
			break
		end
	end
	if not need_tip or self.help_view:IsOpen() then
		return
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STUDENT_HELP, 10, function ()
			self.help_view:Open()
		end)
end

function ShiTuXiuLiWGCtrl:CheckNeedCloseAct()
	if ShiTuXiuLiWGData.Instance:ActCanClose() then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHITUXIULI, ACTIVITY_STATUS.CLOSE)	
	end
end

function ShiTuXiuLiWGCtrl:OpenShiTuView(index)
	if not self.view:IsOpen() then
		self.view:SetShowDefenseIndex(index)
		self.view:Open()
	end
end

function ShiTuXiuLiWGCtrl:OnSCTSAllTeacherInfo(protocol)
	self.data:SaveAllTeacherInfo(protocol)
	self.view:Flush(0, "new_teacher_msg")
end