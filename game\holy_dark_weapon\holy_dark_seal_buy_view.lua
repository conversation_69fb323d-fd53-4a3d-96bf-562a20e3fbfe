HolyDarkSealBuyView = HolyDarkSealBuyView or BaseClass(SafeBaseView)
function HolyDarkSealBuyView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_holy_dark_shop_view")
end

function HolyDarkSealBuyView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
end

function HolyDarkSealBuyView:ReleaseCallBack()
	self.seal_data = nil
end

function HolyDarkSealBuyView:SetDataAndOpen(seal_data)
	self.seal_data = seal_data
    self:Open()
end

function HolyDarkSealBuyView:OnFlush()
	if not self.seal_data then
		return
	end

	self:FlushPanelView()
end

function HolyDarkSealBuyView:FlushPanelView()
	local bundle, asset = ResPath.GetRawImagesPNG("a2_cbg_daoju_" .. self.seal_data.seal_index)
    self.node_list.icon.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list.icon.raw_image:SetNativeSize()
    end)

    -- local bundle1, asset1 = ResPath.GetRawImagesPNG("a2_cbg_bd" .. self.seal_data.seal_index)
    -- self.node_list.bg.raw_image:LoadSprite(bundle1, asset1, function ()
    --     self.node_list.bg.raw_image:SetNativeSize()
    -- end)

    self.node_list.desc_1.text.text = self.seal_data.desc1
    self.node_list.desc_2.text.text = self.seal_data.desc2
    local attr_list, base_capability = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.seal_data, "attr_id", "attr_value")
  	self.node_list["arrtr_1"].text.text = attr_list[1].attr_name .. "    " .. ToColorStr("+" .. attr_list[1].value_str, COLOR3B.L_GREEN)
   
    local seal_state = HolyDarkWeaponWGData.Instance:GetSealDataBySealIndex(self.seal_data.relic_seq, self.seal_data.seal_index) --是否激活
    if seal_state then
    	self.node_list.price_text.text.text = Language.HolyDarkWeapon.SealIsAct
    else
	  	local price = RoleWGData.GetPayMoneyStr(self.seal_data.price, self.seal_data.rmb_type, self.seal_data.rmb_seq)	
		self.node_list.price_text.text.text = price --购买价格
    end

    self.node_list.cap_value.text.text = base_capability
end

function HolyDarkSealBuyView:OnClickBuy()
	if not self.seal_data then
		return
	end

	local seal_state = HolyDarkWeaponWGData.Instance:GetSealDataBySealIndex(self.seal_data.relic_seq, self.seal_data.seal_index) --是否激活
	if seal_state then
		TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.SealIsAct)
		return
	end

	RechargeWGCtrl.Instance:Recharge(self.seal_data.price, self.seal_data.rmb_type, self.seal_data.rmb_seq)
end