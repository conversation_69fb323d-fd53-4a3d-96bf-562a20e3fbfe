YiYuanHaoLiView = YiYuanHaoLiView or BaseClass(SafeBaseView)
function YiYuanHaoLiView:__init()
	self:AddViewResource(0, "uis/view/yiyuanhaoli_ui_prefab", "layout_yiyuanhaoli")
	self:SetMaskBg(false, true)
end

function YiYuanHaoLiView:ReleaseCallBack()
	-- if self.reward_item_list then
	-- 	self.reward_item_list:DeleteMe()
	-- 	self.reward_item_list = nil
	-- end

	if self.reward_item_list and #self.reward_item_list > 0 then
        for i, cell in ipairs(self.reward_item_list) do
            cell:DeleteMe()
        end

        self.reward_item_list = nil
    end
end

function YiYuanHaoLiView:LoadCallBack()
	-- if not self.reward_item_list then	
	-- 	self.reward_item_list = AsyncListView.New(YiYuanHaoLiRender, self.node_list["reward_item_list"])
	-- end

	if not self.reward_item_list then
        self.reward_item_list = {}
        for i = 1, 6 do
			local cell = YiYuanHaoLiRender.New(self.node_list.reward_item_list:FindObj("reward_item" .. i))
			self.reward_item_list[i] = cell
        end
    end

	self.node_list["txt_desc"].text.text = Language.YiYuanHaoLi.TitleDesc
	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind1(self.OnClickBuyBtn, self))
end

function YiYuanHaoLiView:Close()
	-- local is_on_s_btn = MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn()
	-- if is_on_s_btn == false then
	-- 	MainuiWGCtrl.Instance:PlayTopButtonTween(true, nil, true)
	-- end
	if self:IsLoaded() then
		local root_obj = self.node_list["tween_root"]
		local start_pos = root_obj.transform.anchoredPosition
		local act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.YIYUANHAOLI_ACTIVITY)
		if act_btn then
			local act_btn_pos = act_btn:GetIconPos()
			local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
		    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, act_btn_pos)
		    local parent_node_rect = root_obj.transform
		    local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
		    local end_pos = Vector2(local_bullet_start_pos_tbl.x,local_bullet_start_pos_tbl.y)
			UITween.MoveToScaleAndShowPanel(root_obj,start_pos,end_pos,0,nil,nil,function ()
				SafeBaseView.Close(self)
			end)
		else
			SafeBaseView.Close(self)
		end
	else
		SafeBaseView.Close(self)
	end
end

function YiYuanHaoLiView:ShowIndexCallBack()
	self.node_list["tween_root"].transform.localScale = Vector3.one
	self.node_list["tween_root"].transform.localPosition = Vector3.zero
end

function YiYuanHaoLiView:OnFlush()
	--奖励列表
	local reward_list = YiYuanHaoLiWGData.Instance:GetYiYuanHaoLiRewardList()
	--self.reward_item_list:SetDataList(reward_list)
	for i = 1, 6 do
		if reward_list and reward_list[i] then
			self.node_list.reward_item_list:FindObj("reward_item" .. i):SetActive(true)
			self.reward_item_list[i]:SetData(reward_list[i])
		else
			self.node_list.reward_item_list:FindObj("reward_item" .. i):SetActive(false)
		end
	end

	--按钮设置
	local is_buy = YiYuanHaoLiWGData.Instance:GetYiYuanHaoLiIsBuy()
	--self.node_list["effect"]:SetActive(not is_buy)
	-- self.node_list["is_get_btn"]:SetActive(is_buy)
	XUI.SetGraphicGrey(self.node_list["buy_btn"], is_buy)
end

function YiYuanHaoLiView:OnClickBuyBtn()
	if YiYuanHaoLiWGData.Instance:GetYiYuanHaoLiIsBuy() then--已购买
		return
	end
	local cfg = YiYuanHaoLiWGData.Instance:GetYiYuanHaoLiCfg()
	if IsEmptyTable(cfg) then
		return
	end

	RechargeWGCtrl.Instance:Recharge(cfg.rmb_price, cfg.rmb_type, cfg.rmb_seq)
end

----------------------------------------------------------------------------
YiYuanHaoLiRender = YiYuanHaoLiRender or BaseClass(BaseRender)

function YiYuanHaoLiRender:__init()

end

function YiYuanHaoLiRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell"])
	end
end

function YiYuanHaoLiRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end


function YiYuanHaoLiRender:OnFlush()
	if not self.data or self.data.item_id == 0 then return end

	self.item_cell:SetQualityIconVisible(false)
	self.item_cell:SetCellBgEnabled(false)
	self.item_cell:SetShowCualityBg(false)
	self.item_cell:SetData(self.data)
	self.item_cell:SetEffectRootEnable(false)
	self.item_cell:SetBindIconVisible(false)

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		self.node_list.name.text.text = item_cfg.name
	end
end