-- 帮派战
function GuildView:InitWARattleView()
	-- print_error("GuildView.InitWARattleView")
	self.node_list.btn_zhanchang.button:AddClickListener(BindTool.Bind(self.OnClick<PERSON>han<PERSON><PERSON>, self))
	--self.node_list.btn_shendian.button:AddClickListener(BindTool.Bind(self.OnClickShenDian, self))
	self.node_list.btn_tip_1.button:AddClickListener(BindTool.Bind(self.TipsWar, self))
	self.node_list.btn_shendian_reward.button:AddClickListener(BindTool.Bind(self.ClickXMZZhanBao, self))
	self.node_list.btn_guize.button:AddClickListener(BindTool.Bind(self.ClickGuiZeBtn, self))
	
	XUI.AddClickEventListener(self.node_list["btn_duizheng"], BindTool.Bind(self.ClickDuiZheng,self))
	--XUI.AddClickEventListener(self.node_list["fenpei_btn"], BindTool.Bind(self.ClickFenPei,self))
	--XUI.AddClickEventListener(self.node_list["pingji_lock"], BindTool.Bind(self.ClickPingJiLock,self))
	XUI.AddClickEventListener(self.node_list["xmz_zhanbao_btn"], BindTool.Bind(self.GoFenpei,self))

	self:UpdateWarPanelTime()
	self.flush_effect_bind = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateWarPanelTime, self), 1) --每1秒
	self.reward_item_list_2 = AsyncListView.New(RewardPreviousCell,self.node_list.ph_reward_rank_list_2)
	local other_cfg = GuildBattleRankedWGData.Instance:GetGuildWarReward()
	if not IsEmptyTable(other_cfg) then
		local data2 = other_cfg.show_reward_item
		local data2_1 = {}
		local bazhu_fashion_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shizhuang_index") or 1
        local bazhu_shenbing_index = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("bazhu_shenbing_index") or 1
		local title_other_cfg = TitleWGData.Instance:GetOtherCfg()
		local title_id = title_other_cfg and title_other_cfg.xianmengzhan_first or 0
		local title_cfg = TitleWGData.Instance:GetConfig(title_id)
		
		local fashion_item_id = NewAppearanceWGData.Instance:GetFashionItemId(1, bazhu_fashion_index)
		if fashion_item_id > 0 then
			local item = {item_id = fashion_item_id,num = 1,is_bind = 1,has_top = true}
			table.insert(data2_1,item)
		end

		local shenbing_item_id = NewAppearanceWGData.Instance:GetFashionItemId(6, bazhu_shenbing_index)
		if shenbing_item_id > 0 then
			local item = {item_id = shenbing_item_id, num = 1,is_bind = 1,has_top = true}
			table.insert(data2_1,item)
		end
		if title_cfg then
			local item = {item_id = title_cfg.item_id,num = 1,is_bind = 1,has_top = true}
			table.insert(data2_1,item)
		end
		for i=0,#data2 do
			table.insert(data2_1,data2[i])
		end
		self.reward_item_list_2:SetDataList(data2_1)
	end

	self:InitGuildWarProgressList()

	local show_time0, show_time1, show_time2, show_time3 = GuildBattleRankedWGData.Instance:GetShowOpenTime()
	self.node_list["xmz_profess_text_0"].text.text = string.format(Language.Guild.XmzProfessText0, show_time0)
	self.node_list["xmz_profess_text_1"].text.text = string.format(Language.Guild.XmzProfessText1, show_time1)
	self.node_list["xmz_profess_text_2"].text.text = string.format(Language.Guild.XmzProfessText2, show_time2)
	self.node_list["xmz_profess_text_3"].text.text = string.format(Language.Guild.XmzProfessText3, show_time3)
end

function GuildView:InitGuildWarProgressList()
	local list_data = GuildWGData.Instance:GetKfMatchTeamList() or {}
	local function check_has_info(data_list)
		for k=1,#data_list do
			if data_list[k].guild_id > 0 then
				return true
			end
		end
	end
	local num = 0
	for i=1,#list_data do
		local data = list_data[i]
		if data and check_has_info(data) then
			num = num + 1
		end
	end

	if num > 0 then 
		local res_async_loader = AllocResAsyncLoader(self, "ph_guild_war_progress_item_final")
		res_async_loader:Load("uis/view/guild_ui_prefab", "ph_guild_war_progress_item_final", nil,
			function(new_obj)
				local parent_root = self.node_list.ph_guild_war_progress_list.transform
				local progress_list = {}
				for i = 1, num do
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(parent_root, false)
					progress_list[i] = GuildWarProgressItemRender.New(obj)
					progress_list[i]:SetIndex(i)
				end
				self.guild_war_progress_list = progress_list
				self:SetGuildWarProgressList()
		end)
	end

	-- if num > 1 then
	-- 	local res_async_loader = AllocResAsyncLoader(self, "ph_guild_war_progress_item_final")
	-- 	res_async_loader:Load("uis/view/guild_ui_prefab", "ph_guild_war_progress_item_final", nil,
	-- 		function(new_obj)
	-- 			local parent_root = self.node_list.ph_guild_war_progress_list.transform
	-- 			local progress_list = {}
	-- 			for i = 1, 2 do
	-- 				local obj = ResMgr:Instantiate(new_obj)
	-- 				obj.transform:SetParent(parent_root, false)
	-- 				progress_list[i] = GuildWarProgressItemRender.New(obj)
	-- 				progress_list[i]:SetIndex(i)
	-- 			end
	-- 			self.guild_war_progress_list = progress_list
	-- 			self:SetGuildWarProgressList()
	-- 		end)
	-- else
	-- 	local res_async_loader = AllocResAsyncLoader(self, "ph_guild_war_progress_item_final")
	-- 	res_async_loader:Load("uis/view/guild_ui_prefab", "ph_guild_war_progress_item_final", nil,
	-- 		function(new_obj)
	-- 			local parent_root = self.node_list.ph_guild_war_progress_list.transform
	-- 			local progress_list = {}
	-- 			local obj = ResMgr:Instantiate(new_obj)
	-- 			obj.transform:SetParent(parent_root, false)
	-- 			progress_list[1] = GuildWarProgressItemRender.New(obj)
	-- 			progress_list[1]:SetIndex(1)

	-- 			self.guild_war_progress_list = progress_list
	-- 			self:SetGuildWarProgressList()
	-- 		end)
	-- end
end

function GuildView:GoFenpei()
	ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiRewardView(1)
end

function GuildView:ClickFenPei()
	GuildBattleRankedWGData.Instance:SetFenPeiFirstEffect(true)
	self.node_list["fenpei_effect"]:SetActive(false)
	ViewManager.Instance:Open(GuideModuleName.GuildWarFeiPeiView)
end

function GuildView:ClickDuiZheng()
	ViewManager.Instance:Open(GuideModuleName.GuildWarDuiZhengView)
end

function GuildView:ClickGuiZeBtn()
	ViewManager.Instance:Open(GuideModuleName.GuildWarRuleView)
end

function GuildView:ClickPingJiLock()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.GuildNotPingJi)
end

function GuildView:DeleteGuildWar()
	if self.reward_item_list then
		for k,v in ipairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end

	if nil ~= self.guild_war_progress_list then
		for k,v in pairs(self.guild_war_progress_list) do
			v:DeleteMe()
		end
		self.guild_war_progress_list = nil
	end

	if self.reward_item_list_2 then
		self.reward_item_list_2:DeleteMe()
		self.reward_item_list_2 = nil
	end
	if self.flush_effect_bind then
		GlobalTimerQuest:CancelQuest(self.flush_effect_bind)
		self.flush_effect_bind = nil
	end
end

function GuildView:ShowGuildWarCallBack()

end

function GuildView:UpdateWarPanelTime()
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	-- print_error("activity_info =", activity_info)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local start_hour, start_min, 
			during_time, start_hour_1, 
			start_min_1, all_second, 
			round_time_1, round_time_2 = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()


	local cross_open_cfg = BiZuoWGData.Instance:GetCrossActivityOpenTimeCfg(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local limit_day = cross_open_cfg and cross_open_cfg.certain_open_openserver_day

	local time_day = os.date("%w", server_time)
	local num = limit_day - open_day

	if limit_day > open_day then
		self.node_list.autoname_title_show_1:SetActive(true)
		self.node_list.autoname_title_show_1.text.text = string.format(Language.Guild.GuildWarDesc_4,num)--num.."天后活动开启"--string.format(Language.Guild.GuildBattlePrevious_1,limit_day - open_day)
	else
		local open_weekday_list = Split(cross_open_cfg.open_weekday,",")
		local is_open_day = false
		local act_open_day = 7
		local t_day = tonumber(time_day) == 0 and 7 or tonumber(time_day)
		for k,v in pairs(open_weekday_list) do
			local day = tonumber(v) == 0 and 7 or tonumber(v)
			if t_day <= day then
				act_open_day = tonumber(v) == 0 and 7 or tonumber(v)
				if t_day == day then
					is_open_day = true
				end
				break
			end
		end

		if is_open_day or (limit_day - open_day) == 0 then
			local open_time = GuildBattleRankedWGData.Instance:GetGuildWarOpenTime()
			local close_time = GuildBattleRankedWGData.Instance:GetGuildWarOpenEndTime()
			open_time = open_time - server_time
			close_time = close_time - server_time
			local hour = math.floor(open_time / 3600)
			local minute = math.floor(open_time / 60)
			local second = math.floor(open_time % 60)
			local time_str = ""
			if hour == 0 then
				time_str = string.format("%02d:%02d", minute, second)
			elseif hour >= 24 then
				time_str = string.format(Language.Common.ShowTime2, math.floor(hour / 24))
			else
				time_str = string.format(Language.Common.ShowTime3, hour)
			end
			self.node_list.autoname_title_show_1:SetActive(true)
			if open_time > 0 then
				self.node_list.autoname_title_show_1.text.text = string.format(Language.Guild.GuildWarDesc_6,time_str)--time_str.."后活动开启"
			elseif close_time > 0 then
				self.node_list.autoname_title_show_1.text.text = Language.Guild.GuildWarLunCi4
			else
				self.node_list.autoname_title_show_1.text.text = Language.Guild.GuildWarLunCi3
			end

			local show_papertime = GuildBattleRankedWGData.Instance:GetShowPaperTimetamp()
			if server_time >= show_papertime and not self.flush_show_papertime then
				self.flush_show_papertime = true
				self:OnFlushWar()
			end
		else
			self.node_list.autoname_title_show_1:SetActive(true)
			num = act_open_day - t_day
			self.node_list.autoname_title_show_1.text.text = string.format(Language.Guild.GuildWarDesc_4,num)--num.."天后活动开启"--string.format(Language.Guild.GuildBattlePrevious_1,limit_day - open_day)
		end
	end

	local war_progress_text = ""
	local slider_value = 0

	local next_match_start_time = GuildWGData.Instance:GetKfXmzNextMatchStartTime()
	local m_curr_match_end_time = GuildWGData.Instance:GetKfXmzCurrMatchEndTime()

	local lerp_pass_time = 0

	local cur_state = GuildWGData.Instance:GetCurGuildWarState()

	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		local activity_open_pre_time = math.floor(activity_info.next_time - server_time)
		if activity_open_pre_time > 0 then
			local str = TimeUtil.MSTime(activity_open_pre_time)
			war_progress_text = string.format(Language.Guild.GuildWarOpenNextTime,str) --"仙盟争霸第一轮"..str.."后开启"
		end
	elseif activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then

		if cur_state == GuildWGData.GuildWarState.OneState then		-- 第一场进行中
			lerp_pass_time = math.floor(m_curr_match_end_time - server_time)
			if lerp_pass_time > 0 then
				local last_time = TimeUtil.MSTime(lerp_pass_time)
				war_progress_text = string.format(Language.Guild.GuildWarRound_2,last_time) -- last_time.."后第一轮结束"
				slider_value = ((round_time_1 - lerp_pass_time) / round_time_1)
			end
		elseif cur_state == GuildWGData.GuildWarState.XiuSaiState then	--休赛期
			lerp_pass_time = math.floor(next_match_start_time - server_time)
			if lerp_pass_time > 0 then
				local last_time = TimeUtil.MSTime(lerp_pass_time)
				war_progress_text = string.format(Language.Guild.GuildWarRound_2_2,last_time)--last_time.."后第二轮开启"
				slider_value = 1 + ((during_time - lerp_pass_time) / during_time)
			end
		elseif cur_state == GuildWGData.GuildWarState.TwoState then	--第二场进行中
			lerp_pass_time = math.floor(m_curr_match_end_time - server_time)
			if lerp_pass_time > 0 then
				local last_time = TimeUtil.MSTime(lerp_pass_time)
				war_progress_text = string.format(Language.Guild.GuildWarRound_2_1,last_time)-- last_time.."后第二轮结束"
				slider_value = 2 + ((round_time_2 - lerp_pass_time) / round_time_2)
			end
		elseif cur_state == GuildWGData.GuildWarState.EndState then
			war_progress_text = Language.Guild.GuildWarOver
			slider_value = 3
		end
	else
		slider_value = 0
	end

	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		for i=1, 3 do
			self.node_list["nomal_" .. i]:SetActive(not (i == cur_state))
			self.node_list["highlighted_" .. i]:SetActive(i == cur_state)
		end
	end

	self.node_list.lbl_guild_war_progress_text_bg:SetActive(war_progress_text ~= "")
	self.node_list.lbl_guild_war_progress_text_1.text.text = war_progress_text
end

function GuildView:OnFlushWar()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local show_papertime = GuildBattleRankedWGData.Instance:GetShowPaperTimetamp()
	local time_day = os.date("%w", server_time)
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_standy_open = false
	
	if activity_info then
		is_standy_open = activity_info.status == ACTIVITY_STATUS.STANDY or activity_info.status == ACTIVITY_STATUS.OPEN
	end

	self.node_list.layout_guild_war_rank:SetActive(not is_standy_open)
	self.node_list.layout_guild_war_progress:SetActive(is_standy_open)

	local list_data = GuildWGData.Instance:GetKfMatchTeamList()
	if nil == list_data then
		return 
	end
	self:SetGuildWarProgressList()
	
	self.node_list.Guild_Red_Remind:SetActive(self:RedIsCanShow())
	local uid = ZhuZaiShenDianWGData.Instance:GetMengZhuUid()
	--self.node_list["ZhuZaiShenDian_Remind"]:SetActive(ZhuZaiShenDianWGData.Instance:GetRewardNum() == 1 and uid > 0)
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local plat_type = RoleWGData.Instance.role_vo.plat_type
	local guild_name = RoleWGData.Instance.role_vo.guild_name
	local my_guild_side = GuildWGData.Instance:GetKfMyGuildSide()
	local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
	local is_guild_war = GuildWGData.Instance:GetMyCanJoinGuildWar()

	local battle_fight_state = GuildWGData.Instance:GetKfGuildBattleFightState()

	if is_standy_open then
		if battle_fight_state == GUILD_KF_BATTLE_MATCH_STATE.HALF_FINAL_GAME then	
			self.node_list["war_progress_text"].text.text = Language.Guild.GuildWarLunCi1
		elseif battle_fight_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME then
			self.node_list["war_progress_text"].text.text = Language.Guild.GuildWarLunCi2
		elseif battle_fight_state == GUILD_KF_BATTLE_MATCH_STATE.END_GAME then	
			self.node_list["war_progress_text"].text.text = Language.Guild.GuildWarLunCi3
		end
	end
end

function GuildView:SetGuildWarProgressList()
	if not self.guild_war_progress_list then
		return
	end

	local list_data = GuildWGData.Instance:GetKfMatchTeamList() or {}
	local progress_list = self.guild_war_progress_list
	local function check_has_info(data_list)
		for k=1,#data_list do
			if data_list[k].guild_id > 0 then
				return true
			end
		end
	end

	for i=1,#progress_list do
		local data = list_data[i]
		if data and check_has_info(data) then
			progress_list[i]:SetData(data)
			progress_list[i]:SetVisible(true)
		else
			progress_list[i]:SetVisible(false)
		end
	end
end

function GuildView:RedIsCanShow()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN) then
		local guild_id = RoleWGData.Instance.role_vo.guild_id -- 自己的帮派id
		local is_show1 = GuildWGData.Instance:GetGuildIsCanCanJiaZhengBa()
		local is_enter_zhengba_scene = GuildWGData.Instance:GetHideGuildZhengBaRemind()
		local is_over = GuildWGData.Instance:GetMyGuildWarIsOver()
		return is_show1 and not is_enter_zhengba_scene and not is_over
	end
	return false
end

function GuildView:TipsWar()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.BattleTips)
		role_tip:SetContent(Language.Guild.BattleTipContent, nil, nil, nil, true)
	end
end

function GuildView:OnClickShenDian()
	ZhuZaiShenDianWGCtrl.Instance:Open()
end

function GuildView:OnClickZhanChang()
	-- local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local can_open = act_info and (act_info.status == ACTIVITY_STATUS.OPEN or act_info.status == ACTIVITY_STATUS.STANDY) or false
	if not can_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
		return
	end

	local cur_state = GuildWGData.Instance:GetCurGuildWarState()
	if cur_state == GuildWGData.GuildWarState.EndState then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.XmzEnd)
		return
	end

	local is_over = GuildWGData.Instance:GetMyGuildWarIsOver()
	if is_over then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.MyGuildWarIsOver)
		return
	end

	local is_guild_war = GuildWGData.Instance:GetMyCanJoinGuildWar()
	if not is_guild_war then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.CanNotWar)
		return
	end

	local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
	if my_guild_opponent_guild and my_guild_opponent_guild.guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.LunKongTip)
		return
	end

	GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_REQ_ENTER_FB)
end

function GuildView:ClickXMZZhanBao()
	ViewManager.Instance:Open(GuideModuleName.GuildWarZhanBaoView)
end

-------------------------------------- GuildWarProgressItemRender --------------------------------------
GuildWarProgressItemRender = GuildWarProgressItemRender or BaseClass(BaseRender)

local Index2Str = {
	[1] = "一",
	[2] = "二"
}

function GuildWarProgressItemRender:__init()
end

function GuildWarProgressItemRender:__delete()
end

function GuildWarProgressItemRender:CreateChild()
	BaseRender.CreateChild(self)
end

function GuildWarProgressItemRender:OnFlush()
	local data = self:GetData()
	if data == nil then return end

	--self.node_list.img_guild_war_progress_item_name.image:LoadSprite(ResPath.GetGuildSystemImage("saiqu_" .. self.index))
	--self.node_list.img_guild_war_progress_item_name.image:SetNativeSize()

	self.node_list.guild_war_progress_text.text.text = string.format(Language.Guild.GuildWarPart, Index2Str[self.index])

	local own_guild_id = RoleWGData.Instance:GetRoleVo().guild_id
	local own_plat_type = RoleWGData.Instance.role_vo.plat_type
	local act_state = GuildWGData.Instance:GetKfGuildBattleFightState()

	local guild_datas = data
	local cur_state = GuildWGData.Instance:GetCurGuildWarState()

	if act_state == GUILD_KF_BATTLE_MATCH_STATE.HALF_FINAL_GAME then
		guild_datas = data
	elseif act_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME then
		guild_datas = GuildWGData.Instance:GetKfGuildWarFinalGameInfo(self.index)
	elseif act_state == GUILD_KF_BATTLE_MATCH_STATE.END_GAME then
		guild_datas = GuildWGData.Instance:GetKfGuildWarFinalGameInfo(self.index)
	end

	local show_second_win_list = GuildWGData.Instance:GetGuildWarShowSecondWinData(self.index)
	local show_champion_win_list = GuildWGData.Instance:GetGuildWarShowChampionWinData(self.index)

	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
	for i=1,4 do
		if guild_datas[i] then
			local plat_type = guild_datas[i].plat_type
			local guild_id = guild_datas[i].guild_id

			local show_win_img = false
			local show_win = false
			if cur_state == GuildWGData.GuildWarState.OneState then 		--第一轮 
				local is_win = GuildWGData.Instance:KfGuildWarIsSecondeWin(self.index,plat_type,guild_id)
				show_win = show_second_win_list[i]
				show_win_img = is_win and guild_id > 0
			elseif cur_state == GuildWGData.GuildWarState.XiuSaiState then   --休赛期
				show_win = false
				show_win_img = false
			elseif cur_state == GuildWGData.GuildWarState.TwoState or cur_state == GuildWGData.GuildWarState.EndState then	-- 第二轮
				local is_win = GuildWGData.Instance:KfGuildWarIsChampionWin(self.index,plat_type,guild_id)
				show_win = show_champion_win_list[i]
				show_win_img = is_win
			end
			self.node_list["guild_win_img"..i]:SetActive(show_win)
			local win_img_ab = show_win_img and "a2_xm_zb_sl" or "a2_xm_zb_sb"
			self.node_list["guild_win_img"..i].image:LoadSprite(ResPath.GetGuildSystemImage(win_img_ab))

			local guild_name_color = guild_datas[i].guild_id == own_guild_id and guild_datas[i].plat_type == own_plat_type and "#75fba4" or COLOR3B.WHITE
			local server_str = ""
			if is_cross_server_stage then
				server_str = string.format(Language.Common.ServerIdFormat, guild_datas[i].server_id)
			end
			local is_all_nil = self:GetCurGuildIsAllNil(guild_datas,i)
			if is_all_nil then
				self.node_list["lbl_guild_name_"..i].text.text = Language.KuafuGuildBattle.KfNoOccupy
			else
				local guild_name = ToColorStr(server_str..guild_datas[i].guild_name,guild_name_color)
				self.node_list["lbl_guild_name_"..i].text.text = guild_datas[i].guild_name ~= "" and guild_name or Language.Guild.GuideWarProgressWuDuiShou
			end

		else
			self.node_list["lbl_guild_name_"..i].text.text = Language.Guild.GuideWarProgressWuDuiShou
		end
	end
end

--判断这组对阵是否都轮空 两个轮空显示虚位以待
function GuildWarProgressItemRender:GetCurGuildIsAllNil(guild_datas,index)
	local other_index = index % 2 == 0 and index - 1 or index + 1
	local my_data = guild_datas[index]
	local other_data = guild_datas[other_index]
	if my_data and other_data and (my_data.guild_id > 0 or other_data.guild_id > 0) then
		return false
	end
	return true
end

-------------------------------------------------------------------------------------------------------------------------

RewardPreviousCell = RewardPreviousCell or BaseClass(BaseRender)

function RewardPreviousCell:__init()
	if not self.base_cell then
		self.base_cell = ItemCell.New(self.node_list["pos"])
	end
end

function RewardPreviousCell:LoadCallBack()

end

function RewardPreviousCell:ReleaseCallBack()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function RewardPreviousCell:OnFlush()
	if nil == self.data then return end
	local param0 = self.data.param0 ~= nil and self.data.param0 or 0
	local impression = self.data.param1 ~= nil and self.data.param1 or 0

	local data = {
		item_id = self.data.item_id,
		num = self.data.num,
		is_bind = self.data.is_bind,
		param0 = param0,
		param = {impression = impression},
	}
	self.base_cell:SetData(data)
	if self.node_list["top_img"] then
		local has_top = self.data.has_top or false
		self.node_list["top_img"]:SetActive(has_top)
	end
end
