local SelectTextOutLineColor = {
    [0] = "#ab5939",
    [1] = "#9d6d1a",
    [2] = "#8c45a1",
    [3] = "#4552a1",
	[4] = "#000000",
	[5] = "#000000",
}

DIYDrawOneSelectView = DIYDrawOneSelectView or BaseClass(SafeBaseView)
function DIYDrawOneSelectView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel_2", {sizeDelta = Vector2(818, 578)})
	self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "diydraw_select_view")
end

function DIYDrawOneSelectView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.DIYDraw.SelectViewTitle
	self.change_select_list = AsyncListView.New(DIYDrawOneSelectBigCell, self.node_list["reward_list"])
    --self.node_list["confirm"].button:AddClickListener(BindTool.Bind(self.OnClickConfirm,self))
end

function DIYDrawOneSelectView:ReleaseCallBack()

	if self.change_select_list then
		self.change_select_list:DeleteMe()
		self.change_select_list = nil
	end

end

function DIYDrawOneSelectView:OpenCallBack()
    DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.INFO)
end

function DIYDrawOneSelectView:OnFlush()
	local choose_list = DIYDrawWGData.Instance:GetChooseRewardPoolList()
	if choose_list then
		self.change_select_list:SetDataList(choose_list)
	end
end


DIYDrawOneSelectBigCell = DIYDrawOneSelectBigCell or BaseClass(BaseRender)
function DIYDrawOneSelectBigCell:__init()
	self.big_change_select_list = {}
	self.cur_select_index = -1
	for i = 1, 10 do
		self.big_change_select_list[i] = DIYDrawOneSelectSmallCell.New(self.node_list["item_group"]:FindObj("select_small_cell_" .. i))
		self.big_change_select_list[i]:SetIndex(i)
		self.big_change_select_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectCallBack, self))
	end
end

function DIYDrawOneSelectBigCell:__delete()
	if self.big_change_select_list then
		for k, v in pairs(self.big_change_select_list) do
			v:DeleteMe()
		end
		self.big_change_select_list = nil
	end
end

function DIYDrawOneSelectBigCell:OnSelectCallBack(cell)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

	local index = cell:GetIndex()
	if self.cur_select_index ~= -1 and self.cur_select_index ~= index then
		if self.data ~= nil then
			--print_error(self.data.seq_index, data.seq)
			--发送协议
			DIYDrawWGCtrl.Instance:SendDIYDrawReq(
			ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW,
			OA_DIY_DRAW1_OPERATE_TYPE.CHOOSE,
			self.data.seq_index, data.seq)
		end
		self.cur_select_index = index
	end
	
	for k,v in pairs(self.big_change_select_list) do
		v:FlushSelectHL(data.seq)
	end
end


function DIYDrawOneSelectBigCell:OnFlush()
	if not self.data then
		return
	end

	for i = 1, 10 do
		if self.data.seq_list[i] then
			self.big_change_select_list[i]:SetData(self.data.seq_list[i])
			self.big_change_select_list[i]:SetActive(true)
		else
			self.big_change_select_list[i]:SetActive(false)
		end
	end

	if self.cur_select_index == -1 then
		self.cur_select_index = 0
	end

	for k,v in pairs(self.big_change_select_list) do
		v:FlushSelectHL(self.data.cur_seq)
	end

	local out_line = self.node_list.select_num:GetOrAddComponent(typeof(UnityEngine.UI.Outline))
	if SelectTextOutLineColor[self.data.seq_index] then
		out_line.effectColor = Str2C3b(SelectTextOutLineColor[self.data.seq_index])
	end

	self.node_list.select_num.text.text = Language.CangBaoShiGuang.ReWaredLevelText[self.data.seq_index]
	local bundle, asset = ResPath.GetDIYDrawImg("X_xhd_di_" .. self.data.seq_index)
	if bundle and asset then
		self.node_list["img"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["img"].image:SetNativeSize()
		end)
	end
end




DIYDrawOneSelectSmallCell = DIYDrawOneSelectSmallCell or BaseClass(BaseRender)
function DIYDrawOneSelectSmallCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_pos"])
	self.node_list["select_area"].button:AddClickListener(BindTool.Bind(self.OnClickSelect, self))
end

function DIYDrawOneSelectSmallCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.click_callback = nil
end

function DIYDrawOneSelectSmallCell:SetCellClickCallBack(call_back)
    self.click_callback = call_back
end

function DIYDrawOneSelectSmallCell:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data.item)
end

function DIYDrawOneSelectSmallCell:OnClickSelect()
	if self.click_callback then
		self.click_callback(self)
	end
end

function DIYDrawOneSelectSmallCell:FlushSelectHL(seq)
	if not self.data then
		return
	end

	self.node_list["select_flag"]:SetActive(self.data.seq == seq)

	if self.data.seq == seq then
		self.node_list["select_area"].image.raycastTarget = false
	else
		self.node_list["select_area"].image.raycastTarget = true
	end
end