XianLingGuZhenNiuDan = XianLingGuZhenNiuDan or BaseClass(SafeBaseView)

function XianLingGuZhenNiuDan:__init()
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_niudan")
    self:SetMaskBg()
    self.full_screen = true
	self.mask_alpha = MASK_BG_ALPHA_TYPE.One 					-- MaskBg透明度
    self.info = nil
end

function XianLingGuZhenNiuDan:ReleaseCallBack()
    self.info = nil
    self.niudan_spine_graphic = nil
end

function XianLingGuZhenNiuDan:SetData(info)
	self.info = info
end

function XianLingGuZhenNiuDan:CloseCallBack()
    if self.node_list and self.node_list['spine_root'] then
        self.node_list['spine_root']:SetActive(true)
    end
end

function XianLingGuZhenNiuDan:LoadCallBack()
	self.node_list["notice_btn"].button:AddClickListener(BindTool.Bind(self.OnClicNotNotice, self))

    if not self.niudan_spine_graphic then
		self.niudan_spine_graphic = self.node_list["niudan_spine"].gameObject:GetComponent("SkeletonGraphic")
	end
end

function XianLingGuZhenNiuDan:ShowIndexCallBack()
    self:PlayAnim()
end

function XianLingGuZhenNiuDan:PlayAnim()
    local info = self.info
    if self.niudan_spine_graphic then
        self.niudan_spine_graphic.AnimationState:SetAnimation(0, "click", false)
        self.node_list['spine_root']:SetActive(true)
        self.node_list['draw_num_root']:SetActive(false)
        ReDelayCall(self, function()
            self.node_list['spine_root']:SetActive(false)
            self.node_list['draw_num_root']:SetActive(true)
            self.node_list["draw_num_txt"].tmp.text = info.draw_number or ''

            local player_name = info and info.name and info.name ~= "" and info.name or Language.XianLingGuZhen.NiuDanNoWonnerPlayer
            self.node_list["player_name"].tmp.text = player_name
            ReDelayCall(self, function()
                XianLingGuZhenWGCtrl.Instance:OpenBigReward(info)
                self:Close()
            end, 2, "XianLingGuZhenNiuDanShowReward")
        end, 3.5, "XianLingGuZhenNiuDan")
    end
end

function XianLingGuZhenNiuDan:OnClicNotNotice()
	local cur_state = XianLingGuZhenWGData.Instance:GetNolongerTipFlag()
	XianLingGuZhenWGData.Instance:SetNoLongerTipFlag(not cur_state)
	self:FlushNoLonger()
end

function XianLingGuZhenNiuDan:OnFlush()
    self:FlushNoLonger()
end

function XianLingGuZhenNiuDan:FlushNoLonger()
	local no_longer = XianLingGuZhenWGData.Instance:GetNolongerTipFlag()
	self.node_list["no_tip_flag"]:SetActive(no_longer)

    local spcial_data = XianLingGuZhenWGData.Instance:GetSpcialRewardCfg(self.info.reward_pool_seq, 0)
    if IsEmptyTable(spcial_data) then
        return
    end

    local title_img_bundle, title_img_asset = ResPath.GetRawImagesPNG(spcial_data.tab_bg)
    self.node_list["title_img"].raw_image:LoadSprite(title_img_bundle, title_img_asset, function()
        self.node_list["title_img"].raw_image:SetNativeSize()
    end)
end