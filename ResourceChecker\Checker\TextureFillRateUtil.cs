﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TextureFillRateUtil
{
    // 是否是象素填充的矩形区
    public static Rect GetTextureFillRect(Texture2D texture, int cehckMaxDepth = 10)
    {
        if (null == texture)
        {
            return new Rect();
        }

        Rect fillRect = new Rect(0, 0, texture.width, texture.height);
        if (texture.format != TextureFormat.DXT5)
        {
            return fillRect;
        }

        var pixels = texture.GetRawTextureData();

        int childRectW = (int)fillRect.width / cehckMaxDepth;
        int childRectH = (int)fillRect.height / cehckMaxDepth;
        if (childRectW < 3 || childRectH < 3)
        {
            childRectW = 3;
            childRectH = 3;
        }

        List<Rect> checkRectList = new List<Rect>();
        bool isHHited = false;
        // 先检查水平区域，左右两列往内找
        while (!isHHited && fillRect.width > childRectW * 2)
        {
            checkRectList.Clear();
            CalcCheckRects(fillRect, checkRectList, true, childRectW, childRectH);
            for (int i = 0; i < checkRectList.Count; i++)
            {
                if (CheckRectIsFillFixel(pixels, checkRectList[i], texture.width, texture.height))
                {
                    isHHited = true;
                    break;
                }
            }

            // 没有命中继续缩小，直到命中颜色填充区
            if (!isHHited)
            {
                fillRect.x = fillRect.x + childRectW;
                fillRect.width = fillRect.width - childRectW * 2;
            }
        }

        // 再检查上下区域
        bool isVHited = false;
        while (!isVHited && fillRect.height > childRectH)
        {
            checkRectList.Clear();
            CalcCheckRects(fillRect, checkRectList, false, childRectW, childRectH);
            for (int i = 0; i < checkRectList.Count; i++)
            {
                if (CheckRectIsFillFixel(pixels, checkRectList[i], texture.width, texture.height))
                {
                    isVHited = true;
                    break;
                }
            }

            // 没有命中继续缩小，直到命中颜色填充区
            if (!isVHited)
            {
                fillRect.y = fillRect.y + childRectW;
                fillRect.height = fillRect.height - childRectH * 2;
            }
        }

        return fillRect;
    }

    // 把剩余区域切割出需要检查的区域
    //-------
    //|         |
    //-------
    private static void CalcCheckRects(Rect rect, List<Rect> childRectList, bool isH, int childRectW, int childRectH)
    {
        int colNum = (int)rect.width / childRectW;
        int rowNum = (int)rect.height / childRectH;

        // 切割出左右两列或上下两行
        for (int m = 0; m < 2; m++)
        {
            int num = isH ? rowNum : colNum;
            for (int i = 0; i < num; i++)
            {
                Rect childRect = new Rect();
                childRect.width = childRectW;
                childRect.height = childRectH;
                if (isH) //左右两列
                {
                    childRect.x = (m == 0 ? rect.x : rect.xMax - childRectW);
                    childRect.y = rect.y + i * childRectH;
                }
                else // 上下两行
                {
                    childRect.x = rect.x + i * childRectW;
                    childRect.y = (m == 0 ? rect.y : rect.yMax - childRectH);
                }
                childRectList.Add(childRect);
            }
        }
    }

    // 切割出如下图
    //-------
    //|         |
    //-------
    private static void SplitCheckAroundRect(Rect rect, List<Rect> childRectList, int childRectWidth, int childRectHeight, bool isH)
    {
        int colNum = (int)(rect.width / childRectWidth);
        int rowNum = (int)(rect.height / childRectHeight);

        for (int i = 0; i < colNum; i++)
        {
            for (int m = 0; m < rowNum; m++)
            {
                if (i >= 1 && i <= colNum - 1 && m >= 1 && m <= rowNum - 1)
                {
                    continue;
                }

                Rect childRect = new Rect();
                childRect.x = rect.x + i * childRectWidth;
                childRect.y = rect.y + m * childRectHeight;
                childRect.width = childRectWidth - 1;
                childRect.height = childRectHeight - 1;
                childRectList.Add(childRect);
            }
        }
    }

    private static bool CheckRectIsFillFixel(byte[] pixels, Rect rect, int texW, int texH)
    {
        if (ChectPointIsFillFixel(pixels, (int)rect.x, (int)(rect.y), texW, texH)
            || ChectPointIsFillFixel(pixels, (int)rect.x, (int)(rect.yMax), texW, texH)
            || ChectPointIsFillFixel(pixels, (int)rect.xMax, (int)(rect.yMax), texW, texH)
            || ChectPointIsFillFixel(pixels, (int)rect.xMax, (int)(rect.y), texW, texH)
            || ChectPointIsFillFixel(pixels, (int)rect.x, (int)(rect.y + rect.height * 0.5), texW, texH)
            || ChectPointIsFillFixel(pixels, (int)rect.xMax, (int)(rect.y + rect.height * 0.5), texW, texH)
            || ChectPointIsFillFixel(pixels, (int)(rect.x + rect.width * 0.5), (int)rect.y, texW, texH)
            || ChectPointIsFillFixel(pixels, (int)(rect.x + rect.width * 0.5), (int)rect.yMax, texW, texH)
            || ChectPointIsFillFixel(pixels, (int)(rect.x + rect.width * 0.5), (int)(rect.y + rect.height * 0.5), texW, texH)
            )
        {
            return true;
        }

        return false;
    }

    private static bool ChectPointIsFillFixel(byte[] pixels, int x, int y, int texW, int texH)
    {
        if (x >= texW) x = texW - 1;
        if (y >= texH) y = texH - 1;
        // 4*4个象素为一块进行压缩，DXT5一个块的字节数为16个字节，分别 前8个字节为color, 后8个字节alpha
        // 8个字节alpha，64位。依次是8位 最高alpha,=》8位最低alpha=》16个3位索引
        // 8个字节为color，64位。依次是16位最高色 =》16位最低色=》16个2位索引（16个象素根据索引对最低和最高做线性插值）
        int block = 4;
        int blockByte = 16;
        int blockX = x / block;
        int blockY = y / block;
        int colBlockNum = texW / block;
        int curBlock = blockX + blockY * colBlockNum;
        int sIndex = curBlock * blockByte;
        byte alpha1 = pixels[sIndex];
        byte alpha2 = pixels[sIndex + 1];
        ushort color1 = System.BitConverter.ToUInt16(pixels, sIndex + 8);
        ushort color2 = System.BitConverter.ToUInt16(pixels, sIndex + 8 + 2);
     //   if (0 != color1 * alpha1 || 0 != color2 * alpha2)
        if (alpha1 * color1 > 5 || alpha2 * color2 > 5)
        {
            return true;
        }

        return false;
    }
}
