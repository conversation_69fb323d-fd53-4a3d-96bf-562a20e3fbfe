VipZeroBuyRender = VipZeroBuyRender or BaseClass(BaseRender)

function VipZeroBuyRender:__init(instance)

	local bundle_name, asset_name = "uis/view/main_ui_prefab", "tip_vip_zero_buy"
	self:LoadAsset(bundle_name, asset_name, instance.transform)
end

function VipZeroBuyRender:LoadCallBack()
	if self.node_list.click_btn then
		self.node_list.click_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetBtn, self))
	end
	self.scene_change_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind1(self.OnFlush, self))
	self.view.transform:SetSiblingIndex(1)
end

function VipZeroBuyRender:ReleaseCallBack()
	GlobalEventSystem:UnBind(self.scene_change_complete_event)
	GlobalEventSystem:UnBind(self.pass_day_event)
end

function VipZeroBuyRender:OnFlush()
	if not RechargeWGData.Instance:HasVipZeroBuy() then
		self:SetVisible(false)
		return
	end

	local info_list = RechargeWGData.Instance:GetVipZeroBuyInfo()
	local desc = ""
	if info_list then
		-- **策划需求没有买的时候也显示返回倒计时
		if info_list.reward_flag == 0 and info_list.buy_time > 0 then
			desc = Language.LingYuanLiBao.KeLingQu
		elseif info_list.return_flag == 0 then
			local need_day = VipWGData.Instance:GetVIPZeroBuyCfg("return_time") or 0
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			if open_day > 0 then
				if open_day >= need_day then
					desc = Language.Recharge.ZeroBuyFanLiNow
				else
					desc = string.format(Language.Recharge.ZeroBuyFanLiTip, need_day - open_day)
				end
			end
		end
	end

	if self.node_list then
		self.node_list.tip_desc.text.text = desc
	end
	self:OnSceneChangeComplete()
end

function VipZeroBuyRender:OnSceneChangeComplete(old_scene_type, new_scene_type)
	if self.node_list and self.node_list["content"] then
		local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
		local is_open = RechargeWGData.Instance:HasVipZeroBuy()
		if fb_scene_cfg and fb_scene_cfg.is_show_free_vip == 0 or not is_open then
			self:SetVisible(false)
		else
			self:SetVisible(true)
		end
	end
end

function VipZeroBuyRender:OnClickGetBtn()
	local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
	ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
end