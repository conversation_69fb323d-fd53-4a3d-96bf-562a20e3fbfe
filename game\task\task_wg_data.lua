--------------------------------------------------------------
--任务数据管理
--------------------------------------------------------------
TASK_LIST_CHANGE_REASON_CANACCEPT = 0
TASK_LIST_CHANGE_REASON_ACCEPTED = 1
TASK_LIST_CHANGE_REASON_COMPLETED = 2
TaskWGData = TaskWGData or BaseClass(BaseWGCtrl)

TaskWGData.INJURED_NPC = { --受伤npc

}

TaskWGData.BIND_NPC = 4005001 --被捆绑npc

TASK_WEATHER_INDEX = {
	GUANGSHU = 1, 									-- 光束
	XIAYU = 3,										-- 下雨
	XIAXUE = 2,										-- 下雪
	DALEI = 4,										-- 打雷
}

TaskStatusType = {
	None = 0,
	Type1 = 1, -- 接受
	Type2 = 2, -- 完成
	Type3 = 3, -- 提交
	Type4 = 4, -- 更新
}

TaskBubbleType = {
	Type1 = 1, -- 任务
	Type2 = 2, -- 范围
	Type3 = 3, -- 任务+范围
}

TASK_NOTE_TYPE = 
{
	NORMAL = 1,
	FINAL = 2,
}


TaskWGData.PRE_SCENE_TASK = 450
TaskWGData.FOLLOW_NPC = nil
TaskWGData.FOLLOW_TASK = nil
TaskWGData.GUIDE_BOSS_TASK = 980
TaskWGData.TREE_TASK = 430
TaskWGData.VIRTUAL_RI = 50000

local UnitySysInfo = UnityEngine.SystemInfo

function TaskWGData:__init()
	if TaskWGData.Instance then
		ErrorLog("[TaskWGData] Attemp to create a singleton twice !")
	end
	TaskWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto")
	self.tasklist_auto = cfg
	self.task_cfg_list = cfg.task_list
	self.npc_cfg_list = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list
	local task_other_cfg = cfg.other[1]
	self.guild_task_week_limit = task_other_cfg.guild_task_week_limit
	self.change_res = cfg.change_res
	self.change_mount_res = cfg.change_mount_res
	self.push_cfg = cfg.push
	self.guild_task_mythicallist = ListToMap(self.tasklist_auto.guild_task_mythicallist, "society_level")
	self.xinshou_view_ctrl = ListToMap(self.tasklist_auto.xinshou_view_ctrl, "scene_type")
	self.task_sort = ListToMap(self.tasklist_auto.task_sort, "task_type")
	self.task_call_info = self.tasklist_auto.task_call[1]
	self.task_monster_cfg = ListToMap(cfg.task_monster, "task_id", "call_monster_id")


	

	self.newer_cfg = ConfigManager.Instance:GetAutoConfig("newer_cfg_auto")
	self.youli_other_cfg = self.newer_cfg.youli[1]
	local injured_npc = Split(task_other_cfg.injured_npc_id, "#")
	local injured_npc_task = Split(task_other_cfg.injured_npc_task, "#")
	for k,v in pairs(injured_npc) do
		TaskWGData.INJURED_NPC[tonumber(v)] = tonumber(injured_npc_task[k] or "")
	end

	self.weather_effect_cfg = ListToMapList(cfg.weather_effect, "scene_id")

	local other_first = self:GetOtherInfo().other_first
	self.other_first = StrToTable(other_first)

	local tree_task_info = self:GetOtherInfo().tree_task_info
	self.tree_task_info = StrToTable(tree_task_info)

	local first_des = self:GetOtherInfo().first_des
	self.first_des = StrToTable(first_des)

	local hide_cfg = cfg.hide_npc
	self.hide_npc_cfg = self:ExplainHideCfg(hide_cfg)

	hide_cfg = cfg.hide_gather
	self.hide_gather_cfg = self:ExplainHideCfg(hide_cfg)

	self.rest_cfg = ConfigManager.Instance:GetAutoConfig("rest_auto")
	self.rest_reward_list = ListToMap(self.rest_cfg.rest_reward, "level")

	self.role_level_reward_list = ListToMap(ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list, "level")

	local bounty_cfg = ConfigManager.Instance:GetAutoConfig("bounty_cfg_auto")
	if bounty_cfg then
		self.bounty_base_cfg = bounty_cfg.other[1]
		self.bounty_list_cfg = bounty_cfg.bounty_list
		self.bounty_task_cfg = ListToMap(bounty_cfg.bounty_task, "task_id")
		self.bounty_task_type_list_cfg =  ListToMapList(bounty_cfg.bounty_task, "task_type") 
		self.bounty_task_note_cfg = bounty_cfg.bounty_task_note
		self.bounty_finale_open_cfg = ListToMap(bounty_cfg.bounty_finale_open, "task_id")
		self:BountyCfgInfoInit()
	end

	self.task_cfg_list_dic = {}						-- 任务配置列表，以任务类型为key, cfg列表为值

	self.task_accepted_info_list = {}
	self.task_completed_id_list = {}
	self.task_can_accept_id_list = {}

	self.task_config_list = {} --任务列表配置（类型区分）

	self.notify_data_change_callback_list = {}
	self.notify_datalist_change_callback_list = {}
	self.task_guild_info = {} --帮派任务信息
	self.task_bounty_info = {}	-- 悬赏任务信息

	-- 任务列表缓存
	self.task_list_cache = {}

	self.first_husong_task_cfg = nil
	self.first_camp_task_cfg = nil

	self.shang_rand_monst_id = 0  --悬赏任务ID 临时存储,防止一直刷怪的地点

	self.task_prompt_list= {17011, 17012, 17013, 17014, 17016}

	self.old_treasure_num = {}
	self.min_level_t = {}
	-- 需要显示特效的任务
	self.show_effect_task = 0
	-- 之前指引过的任务列表
	self.show_effect_task_list = {}
	-- 当前正在进行的任务
	self.curr_task_id = 0
	-- 65级以后主线飞到目标场景再寻路
	self.fly_task_level = 65
	-- 返回普通场景后显示弹框
	self.show_task_type_tips = nil
	-- 显示日常奖励列表
	self.show_daily_task_list = nil
	-- 批量完成仙盟，额外奖励次数
	self.show_task_guild_num = 0
	-- 任务完成进度时，要滚动到最顶
	self.roll_flag = false
	self:CalcTaskIdListDic()
	-- 杀怪伪掉落任务id
	self.kill_monster_task_id_list = {}

	self.next_zhuxian_task_id_is_dirty = true

	self.attr_event = BindTool.Bind(self.LevelToOpenView, self)
	RoleWGData.Instance:AddListener(RoleWGData.ATTR_EVENT, self.attr_event)

	--接赏金（日常）任务场景id
	self.daily_task_scene_id = ConfigManager.Instance:GetAutoConfig("tasklist_auto").other[1].daily_scene

	self:InitZhuTaskSortList()
end

function TaskWGData:__delete()
	TaskWGData.Instance = nil
	self.notify_data_change_callback_list = nil
	self.notify_datalist_change_callback_list = nil

	if self.attr_event then
		RoleWGData.Instance:RemoveListener(RoleWGData.ATTR_EVENT, self.attr_event)
		self.attr_event = nil
	end

	for k, v in pairs(self.push_cfg) do
		if CountDownManager.Instance:HasCountDown("task_push" .. v.view_url) then         
			CountDownManager.Instance:RemoveCountDown("task_push" .. v.view_url)
		end
	end
end

function TaskWGData:GetTaskGuildWeekMaxCount()
	return self.guild_task_week_limit or GameEnum.TASK_GUILD_WEEK_MAX_COUNT
end

function TaskWGData:CalcTaskIdListDic()
	for _, v in pairs(self.task_cfg_list) do
		if nil == self.task_cfg_list_dic[v.task_type] then
			self.task_cfg_list_dic[v.task_type] = {}
		end
		table.insert(self.task_cfg_list_dic[v.task_type], v)
	end
end

function TaskWGData:GetFirstDes()
	return self.first_des or {}
end

-- 策划说任务ID可能相同，但是物品ID一定不同
function TaskWGData:GetKillMonsterTaskId(item_id)
	local task_id = nil
	if item_id == nil then
		return task_id
	end

	return self.kill_monster_task_id_list[item_id]
end

function TaskWGData:SetKillMonsterTaskId(item_id, task_id)
	if item_id ~= nil and task_id ~= nil then
		self.kill_monster_task_id_list[item_id] = task_id
	end
end

-- get set
function TaskWGData:RollFlag(value)
	if nil == value then
		return self.roll_flag
	end
	self.roll_flag = value
end

-- get set
function TaskWGData:ShowTaskTypeTips(value)
	if nil == value then
		return self.show_task_type_tips
	end
	self.show_task_type_tips = value
end

-- get set
function TaskWGData:ShowTaskGuildNum(value)
	if nil == value then
		return self.show_task_guild_num
	end
	self.show_task_guild_num = value
end

-- get set
function TaskWGData:ShowDailyTaskList(value)
	if nil == value then
		return self.show_daily_task_list
	end
	self.show_daily_task_list = value
end

-- 根据任务id获取种树任务信息
function TaskWGData:GetTreeTask(task_id)
	for k,v in pairs(self.tree_task_info) do
		if v.task_id == task_id then
			return v
		end
	end
	return nil
end

--根据采集物获取种树任务
function TaskWGData:GetGatherIdTree(gather_id)
	for k,v in pairs(self.tree_task_info) do
		if v.gather_id == gather_id then
			return v
		end
	end
	return nil
end

function TaskWGData:GetShuTaskState(task_id)
	local is_completed = self:GetTaskIsCompleted(task_id)
	local task_cfg = self:GetTaskConfig(task_id)
	local current_count = self:GetProgressNum(task_id)
	if is_completed then
		return is_completed
	elseif task_cfg and current_count == task_cfg.c_param2 then
		return true
	end
	return false
end

-- 获得任务进程的完成数量
function TaskWGData:GetProgressNum(task_id)
	return self.task_accepted_info_list[task_id] and self.task_accepted_info_list[task_id].progress_num or 0
end

function TaskWGData:GetTaskOtherFirst()
	return self.other_first
end

function TaskWGData:IsInOtherFirst(task_id)
	if not self.other_first then
		return false
	end

    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
    if not task_cfg then
    	return false
    end

	local list = self.other_first[task_cfg.task_type]
    if not list then
    	return false
    end

    if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
        list = list.task_id
    elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then
        list = list.level
    end

    if IsEmptyTable(list) then
    	return false
    end

    local flag = false
    for k,v in pairs(list) do
        if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
            if v == task_cfg.task_id then
                flag = true
                break
            end
        elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then
            if v == GameVoManager.Instance:GetMainRoleVo().level then
                flag = true
                break
            end
        end
    end

    return flag
end

function TaskWGData:CurrTaskId(value)
	if nil == value then
		return self.curr_task_id
	end

	local old_task_id = self.curr_task_id
	self.curr_task_id = value
end

function TaskWGData:CurrTaskIsOtherFirst()
    return false
end

function TaskWGData:ShowEffectTask(value)
	if nil == value then
		return self.show_effect_task
	end

	self.show_effect_task = value
end

function TaskWGData:GetShowEffectTaskListById(task_id)
	return self.show_effect_task_list[task_id]
end

function TaskWGData:SetShowEffectTaskListById(task_id)
	self.show_effect_task_list[task_id] = true
end

--设置已接受任务数据列表1801
function TaskWGData:SetTaskAcceptedInfoList(info_list)
	if info_list == nil then
		return
	end

	self.task_accepted_info_list = info_list
	for k,v in pairs(info_list) do
		self:CheckFollowNpc(v.task_id, v.is_complete)
	end

	self:ClearTaskListCache()
	self:SetNextZhuTaskConfigDirty()

	for k,v in pairs(self.notify_datalist_change_callback_list) do
		v(TASK_LIST_CHANGE_REASON_ACCEPTED)
	end
end

function TaskWGData:GetTaskAcceptedInfoList()
	return self.task_accepted_info_list
end

--获取已接的日常任务配置
function TaskWGData:GetTaskTypeRiCfg()
	for k,v in pairs(self.task_accepted_info_list) do
 		local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
 		if task_cfg then
 			if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
 				return task_cfg
 			end
 		end
	end
end

--获取已接的仙盟任务配置
function TaskWGData:GetTaskXianMengCfg()
	for k,v in pairs(self.task_accepted_info_list) do
 		local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
 		if task_cfg then
 			if task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
 				return task_cfg
 			end
 		end
	end
end

--获取悬赏任务配置
function TaskWGData:GetShangTaskCfg()
	for k,v in pairs(self.task_accepted_info_list) do
 		local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
 		if task_cfg then
 			if task_cfg.task_type == GameEnum.TASK_TYPE_SHANG then
 				return task_cfg
 			end
 		end
 	end
end


function TaskWGData:GetTaskTypeMinLevel(task_type)
	if self.min_level_t[task_type] then
		return self.min_level_t[task_type]
	end
	local lv = RoleWGData.GetRoleMaxLevel()
	for k,v in pairs(self.task_cfg_list_dic[task_type] or {}) do
		if v.min_level < lv then
			lv = v.min_level
		end
	end
	self.min_level_t[task_type] = lv
	return lv
end

--------------------------------转职----------------------------------
--获取可接的转职任务配置
function TaskWGData:GetKeJieZhuanZhiCfg()
	for k,v in pairs(self.task_can_accept_id_list) do
 		local task_cfg = TaskWGData.Instance:GetTaskConfig(k)--(获得任务配置数据,k是id)
 		if task_cfg then
 			if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
 				return task_cfg
 			end
 		end
	end
end

--获取已接的转职任务配置
function TaskWGData:GetTaskZhuanZhiCfg()
	for k,v in pairs(self.task_accepted_info_list) do
 		local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
 		if task_cfg then
 			if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
 				return task_cfg
 			end
 		end
	end
end

-- 获取有可提交转职任务
function TaskWGData:GetZhuanZhiCommitId()
	for k,v in pairs(self.task_accepted_info_list) do
		local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
		if task_cfg then
 			if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN and TaskWGData.Instance:GetTaskStatus(v.task_id) == GameEnum.TASK_STATUS_COMMIT then
 				return v.task_id
 			end
 		end
	end
end
------------------------------------------------------------------

-- 获取已接护送任务配置  --写死了任务ID
function TaskWGData:GetHuSongTaskCfg()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(24001)
	if task_cfg then
		if task_cfg.task_type == GameEnum.TASK_TYPE_HU then
			return task_cfg
		end
	end
end

--设置完成任务id列表
function TaskWGData:SetTaskCompletedIdList(id_list)
	if id_list == nil then
		return
	end

	self.task_completed_id_list = id_list
	self:ClearTaskListCache()
	self:SetNextZhuTaskConfigDirty()

	for k,v in pairs(self.notify_datalist_change_callback_list) do
		v(TASK_LIST_CHANGE_REASON_COMPLETED)
	end
end

function TaskWGData:GetTaskCompletedList()
	return self.task_completed_id_list
end

function TaskWGData:ClearSameTypeTask(task_id)
	local task_cfg, acc_task_cfg = nil, nil
	task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then
		return
	end

	if task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHU and
		task_cfg.task_type ~= GameEnum.TASK_TYPE_RI and
		task_cfg.task_type ~= GameEnum.TASK_TYPE_MENG then
		--转职任务现在可多个任务一起进行
		-- task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHUAN then
		return
	end

	for k,v in pairs(self.task_accepted_info_list) do
		acc_task_cfg = TaskWGData.Instance:GetTaskConfig(k)
		if not acc_task_cfg then
			return
		end

		if task_cfg.task_type == acc_task_cfg.task_type then
			self.task_accepted_info_list[k] = nil
		end
	end
end

--设置可接受任务id列表
function TaskWGData:SetTaskCapAcceptedIdList(id_list)
	if not id_list then
		return
	end

	self.task_can_accept_id_list = id_list
	for k,v in pairs(id_list) do
		self:ClearSameTypeTask(k)
	end

	for k,v in pairs(self.task_can_accept_id_list) do
		self:ClearSameTypeTask(k)
	end

	self:ClearTaskListCache()
	self:SetNextZhuTaskConfigDirty()

	for k,v in pairs(self.notify_datalist_change_callback_list) do
		v(TASK_LIST_CHANGE_REASON_CANACCEPT)
	end

	-- for k,v in pairs(self.task_can_accept_id_list) do
		-- local task_cfg = TaskWGData.Instance:GetTaskConfig(k)
		-- 转职任务从任务表挪到了转职表
		-- if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
		--     TransFerWGCtrl.Instance:ChangeTaskState()
		-- end
	-- end
end

function TaskWGData:GetTaskCapAcceptedIdList()
	return self.task_can_accept_id_list
end

function TaskWGData:IsCanAcceptTask(task_id)
	return self.task_can_accept_id_list[task_id] ~= nil
end

--改变已接任务列表里的单个数据1802
function TaskWGData:ChangeTaskAccepted(data)
	if data == nil or data.task_id == nil then
		return
	end

	--一转的时候提交
	local task_info = nil
	local change_task_id = 0
	local task_cfg = TaskWGData.Instance:GetTaskConfig(data.task_id)
	if not task_cfg then
		return
	end

	if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
		DebugLog("转职任务    改变已接任务列表里的单个数据>>>>>>>>>>")
		PrintTable(data)
	end

	if data.reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then		--信息改变
		if 1 == data.is_complete then
            self:RollFlag(true)
            GlobalEventSystem:FireNextFrame(OtherEventType.TASK_CHANGE, TASK_EVENT_TYPE.CAN_COMMIT, data.task_id)
        end
		self:ClearSameTypeTask(data.task_id)
		task_info = self.task_accepted_info_list[data.task_id] or {}
	elseif data.reason == GameEnum.DATALIST_CHANGE_REASON_ADD then		--接取
		-- 新手任务上报，客户端上报类型，判断只有主线任务才上报
		if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
			local condition = -1
			local task_name = ""
			if task_cfg then
				condition = task_cfg.condition
				task_name = task_cfg.task_name
			end

			local take_time = -1
			self.take_task_time = self.take_task_time or -1
			if Status.NowTime - self.take_task_time < Status.NowTime then
				take_time = Status.NowTime - self.take_task_time
			end

			local role_level = GameVoManager.Instance:GetMainRoleVo().level
			-- 上报枚举ID, 贵族ID, 任务ID，行为，时长，等级，任务名字
			-- ReportManager:Step(Report.STEP_REPORT_TASK, "-", data.task_id, condition, take_time, role_level, task_name, UnitySysInfo.deviceModel)
			-- 上报完后 重新计算新的任务时间
			self.take_task_time = Status.NowTime
		end

		self:ClearSameTypeTask(data.task_id)
		task_info = {}
		GlobalEventSystem:FireNextFrame(OtherEventType.TASK_CHANGE, TASK_EVENT_TYPE.ACCEPTED, data.task_id)
	elseif data.reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then	--移除
		change_task_id = data.task_id
		if task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHU and
			task_cfg.task_type ~= GameEnum.TASK_TYPE_RI and
			task_cfg.task_type ~= GameEnum.TASK_TYPE_MENG then
			if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
				DebugLog("转职任务移除的地方or主线任务>>>>>>>>>>",data.task_id)
				PrintTable(data)
			end
			self.task_accepted_info_list[data.task_id] = nil
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then
			self.task_accepted_info_list[data.task_id] = nil
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
			local guild_info = TaskWGData.Instance:GetGuildInfo()
			if RoleWGData.Instance.role_vo.guild_id <= 0 or guild_info.complete_task_count >= TaskWGData.Instance:GetTaskGuildWeekMaxCount() - 1 then
				self.task_accepted_info_list[data.task_id] = nil
			end
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
			local next_task = self:GetNextTaskCfgById(task_cfg.task_type, task_cfg.task_id)

			-- 主线任务卡住策划要自动打开一下快速提升面板
			if next_task then
				if next_task.min_level > RoleWGData.Instance:GetRoleInfo().level then
					TaskWGCtrl.Instance:OpenTaskGainExpTipView()
				end
			end

			if not next_task or next_task.min_level > RoleWGData.Instance:GetRoleInfo().level or self:GetTaskIsCompleted(next_task.task_id) then
				self.task_accepted_info_list[data.task_id] = nil
			end
		end

		if task_cfg.c_param4 == 1 or task_cfg.c_param4 == 2 then
			local reward_t = Split(task_cfg.c_param5, ",")
			local item_cfg = ItemWGData.Instance:GetItemConfig(tonumber(reward_t[1]))
			if item_cfg then
				local str = task_cfg.c_param4 == 1 and Language.Task.BuyTaskTips or Language.Task.CommitItemTaskTips
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(str, ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])))
			end
		end

		if TaskWGData.FOLLOW_NPC then
			self:CheckFollowNpc(data.task_id, 0)
		end
	end

	if task_info ~= nil then
		change_task_id = data.task_id
		task_info.task_id = data.task_id
		task_info.accept_time = data.accept_time
		task_info.progress_num = data.param
		task_info.task_ver = 0
		task_info.task_condition = 0
		task_info.is_complete = data.is_complete
		self.task_accepted_info_list[data.task_id] = task_info
		self:CheckFollowNpc(task_info.task_id, task_info.is_complete)
	else
		if self.task_accepted_info_list ~= nil and self.task_accepted_info_list[data.task_id] ~= nil and data.reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
			self.task_accepted_info_list[data.task_id].is_remove = true
		end
	end

	self:ClearTaskListCache()
	self:SetNextZhuTaskConfigDirty()

	-- local reason_str = {[0]="更新", "添加", "移除"}
	-- print_error("----任务状态改变---", change_task_id, reason_str[data.reason])
	
	for k,v in pairs(self.notify_data_change_callback_list) do  		--任务有变化，通知观察者，带消息体
		v(change_task_id, data.reason)
	end
	--[[2024.11.25点亮任务通过16436协议更新,这里注释
	--20181210刷新转职界面 
	-- if data.task_id == 29019 or data.task_id == 29012 then
	-- 	TransFerWGCtrl.Instance.view:FlushDataShow()
	-- end

    -- RemindManager.Instance:Fire(RemindName.TransFer_Five)--三转界面按钮
    -- RemindManager.Instance:Fire(RemindName.TransFer)--转职主界面按钮
	]]

    --仙盟建设
    --321001---320000
	if data.task_id >= GUILD_BUILD_TASK_OTHER_TYPE.GUILD_BUILD_TASK_MINID and data.task_id <= GUILD_BUILD_TASK_OTHER_TYPE.GUILD_BUILD_TASK_MAXID then
		GuildWGCtrl.Instance:FlushGuildBuildTaskView()
		GuildWGData.Instance:OpenGuildBuildView()
	end

	self:TaskToOpenView(data)
end

function TaskWGData:QuickAllTaskCompleted(task_type)
	local task_cfg = nil
	for k,v in pairs(self.task_accepted_info_list) do
		task_cfg = TaskWGData.Instance:GetTaskConfig(k)
		if not task_cfg then
			return
		end

		if task_type == task_cfg.task_type then
			self.task_accepted_info_list[k] = nil
		end
	end

	for k,v in pairs(self.task_can_accept_id_list) do
 		task_cfg = TaskWGData.Instance:GetTaskConfig(k)
 		if not task_cfg then
 			return
 		end

		if task_type == task_cfg.task_type then
			self.task_accepted_info_list[k] = nil
		end
	end

	TaskWGCtrl.Instance:UpdateTaskPanelShow()
end

--改变任务完成列表的数据
function TaskWGData:ChangeTaskCompleted(task_id)
	if task_id == nil then
		return
	end

	self.task_completed_id_list[task_id] = 1
	self:ClearTaskListCache()
	self:SetNextZhuTaskConfigDirty()

	for k,v in pairs(self.notify_data_change_callback_list) do
		v(task_id)
	end

	GlobalEventSystem:Fire(OtherEventType.TASK_CHANGE, TASK_EVENT_TYPE.COMPLETED, task_id)
end

--获得空闲时段自动去执行的任务
function TaskWGData:GetAutoTaskId(task_type)
	task_type = task_type or GameEnum.TASK_TYPE_ZHU
	local list = self:GetTaskListIdByType(task_type)
	if #list >= 1 then
		return list[1]
	end
	return 0
end

-- 判断主线任务是否因为等级被卡主住
function TaskWGData:GetZhuTaskIsBlockByLevel(task_cfg)
    task_cfg = task_cfg or self:GetTaskTypeZhuCfg()
    if task_cfg then
        return RoleWGData.Instance.role_vo.level < task_cfg.min_level
    end
    return false
end

-- 判断下一个主线任务是否因为等级被卡住
function TaskWGData:GetNextZhuTaskIsBlockByLevel(task_cfg)
	local next_task_cfg = self:GetNextZhuTaskConfigById(task_cfg.task_id)
	if not next_task_cfg then
		return false
	end
	return self:GetZhuTaskIsBlockByLevel(next_task_cfg)
end

-- 获取当前赏金任务是否可提交
function TaskWGData:GetShangJinIsCanCommitById(task_id)
    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
    if task_cfg then
        if task_cfg.task_type == GameEnum.TASK_TYPE_RI and TaskWGData.Instance:GetTaskStatus(task_id) == GameEnum.TASK_STATUS_COMMIT then
            return true
        end
    end
    return false
end

--获取当前显示的主线任务配置
function TaskWGData:GetTaskTypeZhuCfg()
    for k,v in pairs(TaskFollow.Instance:SortTask()) do
        local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
        if task_cfg then
            if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
                return task_cfg
            end
        end
    end
end


--获得任务数据
function TaskWGData:GetTaskInfo(task_id)
	if task_id == nil then
		return nil
	end

	return self.task_accepted_info_list[task_id]
end

--根据类型获得任务列表
-- check_can_show 检测是否需要显示任务
function TaskWGData:GetTaskListIdByType(task_type, check_can_show)
	check_can_show = check_can_show or false
	if task_type ~= GameEnum.TASK_TYPE_ZHU then
		return self:_GetTaskListIdByType(task_type, check_can_show)
	else
		if self.task_list_cache[task_type] and self.task_list_cache[task_type][check_can_show] then
			local list = self.task_list_cache[task_type][check_can_show]
			--这里是为了处理假任务(任务id:1070)
			if task_type == GameEnum.TASK_TYPE_ZHU and list[1] and "number" ~= type(list[1]) and nil ~= list[1].task_id then
				return {[1] = list[1].task_id}
			end
			return list
		else
			local list = self:_GetTaskListIdByType(task_type, check_can_show)
			self.task_list_cache[task_type] = self.task_list_cache[task_type] or {}
			self.task_list_cache[task_type][check_can_show] = list
			return list
		end
	end
end

function TaskWGData:ClearTaskListCache()
	self.task_list_cache = {}
end

function TaskWGData:_GetTaskListIdByType(task_type, check_can_show)
	local task_id_list = {}
	local is_can_show = true
	if task_type == GameEnum.TASK_TYPE_RI then
		local ri_cfg = DailyWGData.Instance:GetTaskTuMoData()
		if ri_cfg and ri_cfg.commit_times > 20 then
			return task_id_list
		end
	end
	--仙盟建设
	if task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
		local guild_build_task_cfg = self:GetTaskGuildBuildCfg()
		if guild_build_task_cfg and not IsEmptyTable(guild_build_task_cfg) then
			task_id_list[#task_id_list + 1] = guild_build_task_cfg.task_id
			return task_id_list
		end
	end
	if task_type == GameEnum.TASK_TYPE_ZHUAN then
		local task_id = TransFerWGData.Instance:GetTaskTransferFbCfgTaskId()
		if task_id and task_id~=0 then
			task_id_list[#task_id_list + 1] = task_id
			return task_id_list
		end
	end

	local task_cfg = nil
	for k,v in pairs(self.task_accepted_info_list) do
		task_cfg = self:GetTaskConfig(v.task_id)
		if task_cfg and task_cfg.task_type == task_type then
			task_id_list[#task_id_list + 1] = v.task_id
		end
	end
	for k,v in pairs(self.task_can_accept_id_list) do
		task_cfg = self:GetTaskConfig(k)
		is_can_show = true
		if check_can_show then
			is_can_show = self:IsCanShowTask(k)
		end
		if task_cfg and task_cfg.task_type == task_type and is_can_show then
			task_id_list[#task_id_list + 1] = k
		end
	end

	return task_id_list
end

--获得任务配置数据
function TaskWGData:GetTaskConfig(task_id)
	if task_id == nil then
		return nil
	end

	return self.task_cfg_list[task_id]
end

--根据任务类型获得第一个任务配置
function TaskWGData:GetFirstTaskConfigByType(task_type)
	for k,v in pairs(self.task_cfg_list_dic[task_type] or {}) do
		if 0 == v.camp or v.camp == RoleWGData.Instance.role_vo.camp then
			return v
		end
	end
	return nil
end

-- 获取最小id的任务
function TaskWGData:GetFirstTaskCfgByType(task_type)
	if not self.first_task_cfg then
		self.first_task_cfg = {}
	end
	if self.first_task_cfg[task_type] then
		return self.first_task_cfg[task_type]
	end

	local task_cfg = nil
	for k,v in pairs(self.task_cfg_list_dic[task_type] or {}) do
		if not task_cfg then
			task_cfg = v
		else
			local w_1 = self:GetCompareWeight(task_cfg.task_id, task_type)
			local w_2 = self:GetCompareWeight(v.task_id, task_type)
			if w_1 > w_2 then
				task_cfg = v
			end
		end
	end

	if not task_cfg then
		print_error("There is no task of this type:::", task_type)
	end
	self.first_task_cfg[task_type] = task_cfg
	return self.first_task_cfg[task_type]
end

-- 转职任务是否（区分五转）第一个
function TaskWGData:IsFirstTask(task_id)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then
		print_error("There is no task of this type:::", task_id)
		return false
	end

	if not self.is_first_task then
		self.is_first_task = {}
	end
	if self.is_first_task[task_cfg.min_level] then
		return self.is_first_task[task_cfg.min_level].task_id == task_cfg.task_id
	end

	local cfg = nil
	for k,v in pairs(self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHUAN]) do
		if task_cfg.min_level == v.min_level and
			(not cfg or cfg.task_id > v.task_id) then
			cfg = v
		end
	end
	self.is_first_task[task_cfg.min_level] = cfg
	if self.is_first_task[task_cfg.min_level] then
		return self.is_first_task[task_cfg.min_level].task_id == task_cfg.task_id
	end
	return false
end

-- 增加一个类似激活的任务功能
-- 日常，仙盟，转职，第一个任务，可接状态不需要显示
function TaskWGData:IsCanShowTask(task_id)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if task_cfg then
		if task_cfg.task_type ~= GameEnum.TASK_TYPE_RI and
		task_cfg.task_type ~= GameEnum.TASK_TYPE_MENG and
		task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHUAN then
		return true
	end

	local task_data, task_status = nil, nil
	task_status = TaskWGData.Instance:GetTaskStatus(task_id)
	if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
		task_data = DailyWGData.Instance:GetTaskTuMoData()
		if task_data.commit_times <= 1 and GameEnum.TASK_STATUS_CAN_ACCEPT == task_status then
			return false
		end
	elseif task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
		local task_data = TaskWGData.Instance:GetGuildInfo()
        if IsEmptyTable(task_data) or
        	(0 == task_data.complete_task_count and GameEnum.TASK_STATUS_CAN_ACCEPT == task_status )then
            return false
        end
	elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
		local is_first = self:IsFirstTask(task_id)
		if is_first and GameEnum.TASK_STATUS_CAN_ACCEPT == task_status then
			return false
		end
	end
	end

	return true
end

--获得一个已经激活的任务
--在可接列表，已接列表
function TaskWGData:GetOneActiveTask(task_type)
	local task_cfg = nil
	for k,v in pairs(self.task_can_accept_id_list) do
		task_cfg = self:GetTaskConfig(k)
		if task_cfg ~= nil and task_cfg.task_type == task_type then
			return task_cfg
		end
	end
	for k,v in pairs(self.task_accepted_info_list) do
		task_cfg = self:GetTaskConfig(v.task_id)
		if task_cfg ~= nil and task_cfg.task_type == task_type then
			return task_cfg
		end
	end

	return nil
end

--获得第一个转职任务
function TaskWGData:GetFirstZhuanzhiTaskConfig()
	local task_id = nil
	local task_cfg = nil
	for k,v in pairs(self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHUAN] or {}) do
		if task_id == nil or task_id > v.task_id then
			task_id = v.task_id
		end

		if not self:GetTaskIsCompleted(v.task_id) and not self:GetTaskIsAccepted(v.task_id) and (task_cfg == nil or task_cfg.task_id > v.task_id) then
			task_cfg = v
		end
	end

	if task_cfg and task_cfg.task_id == task_id then
		return task_cfg
	end

	return nil
end

function TaskWGData:SetNextZhuTaskConfigDirty()
	self.next_zhuxian_task_id_is_dirty = true
end

function TaskWGData:CalNextZhuTaskConfig()
	self.next_zhuxian_task_id = nil

	if self.zhu_task_sort_list ~= nil and next(self.zhu_task_sort_list) ~= nil then
		for k,v in pairs(self.zhu_task_sort_list) do
			local cfg = self:GetTaskConfig(v)
			if cfg ~= nil and not self:GetTaskIsCompleted(cfg.task_id) and not self:GetTaskIsAccepted(cfg.task_id) then
				if self:GetTaskIsCompleted(cfg.pretaskid) or self:GetTaskIsAccepted(cfg.pretaskid) then
					self.next_zhuxian_task_id = cfg
				end
			end
		end
	else
		for k,v in pairs(self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHU] or {}) do
			if not self:GetTaskIsCompleted(v.task_id) and not self:GetTaskIsAccepted(v.task_id) then
				if self:GetTaskIsCompleted(v.pretaskid) or self:GetTaskIsAccepted(v.pretaskid) then
					self.next_zhuxian_task_id = v
				end
            end
		end
	end
end

function TaskWGData:IsShowXiuzhenRoadTask()
    local task_id = GameEnum.XiuZhenRoad_Fake_Task_id
    local cfg = self:GetTaskConfig(task_id)
    if not IsEmptyTable(cfg) then
        local Role_lv = RoleWGData.Instance:GetRoleLevel()
        if self:GetTaskIsCompleted(cfg.pretaskid) and Role_lv > cfg.min_level and Role_lv <= cfg.max_level then
            local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
            local xiuzhen_cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(cur_level + 1)
            if not xiuzhen_cfg then --通关全部关卡
                return false
            end
            if xiuzhen_cfg and xiuzhen_cfg.open_level > Role_lv then
                return true
            end
            return true
        end
    end
    return false
end

function TaskWGData:GetXiuzhenRoadTask()
    local task_id = GameEnum.XiuZhenRoad_Fake_Task_id
    local cfg = self:GetTaskConfig(task_id)
    return cfg
end

function TaskWGData:IsShowHuSongSpcTask()
    local cfg = self:GetHuSongSpcTask()
    if not IsEmptyTable(cfg) then
        local Role_lv = RoleWGData.Instance:GetRoleLevel()
        if cfg.pretaskid and cfg.pretaskid ~= "" and self:GetTaskIsCompleted(cfg.pretaskid) then
        	return false
        end
        if Role_lv > cfg.min_level and Role_lv <= cfg.max_level then
            local need_time = cfg.c_param2 or 3--需要护送美女完成次数
            local complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) or 0 --完成次数
            return complete_times < need_time
        end
    end
    return false
end

function TaskWGData:GetHuSongSpcTask()
    local task_id = GameEnum.HUSONG_SPC_TASK_ID
    local cfg = self:GetTaskConfig(task_id)
    return cfg
end

function TaskWGData:IsShowAssignTask()
    local cfg = self:GetAssignTask()
    if not IsEmptyTable(cfg) then
        return AssignmentWGData.Instance:GetHasAssignTask()
    end
    return false
end

function TaskWGData:GetAssignTask()
    local task_id = GameEnum.ASSIGN_TASK_ID
    local cfg = self:GetTaskConfig(task_id)
    return cfg
end

--获得下一个主线任务
function TaskWGData:GetNextZhuTaskConfig()
	if self.next_zhuxian_task_id_is_dirty then
		self:CalNextZhuTaskConfig()
		self.next_zhuxian_task_id_is_dirty = false
	end

	return self.next_zhuxian_task_id
end

-- 通过主线id获得下一个主线
function TaskWGData:GetNextZhuTaskConfigById(task_id)
	local list = self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHU]
	if nil == list or nil == task_id then
		return nil
	end

	local task_cfg = self:GetTaskConfig(task_id)
	if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
		for _, v in ipairs(list) do
			if v.pretaskid == task_id then
				return v
			end
		end
	end

	return nil
end

--获得某个任务的当前状态
function TaskWGData:GetTaskStatus(task_id)
	if task_id ~= nil then
		if self.task_can_accept_id_list[task_id] ~= nil then
			return GameEnum.TASK_STATUS_CAN_ACCEPT
		end

		if self.task_accepted_info_list[task_id] ~= nil then
			if self.task_accepted_info_list[task_id].is_complete ~= 0 then
				return GameEnum.TASK_STATUS_COMMIT
			else
				if self:NeedChangeStatus(task_id) then
					return GameEnum.TASK_STATUS_COMMIT
				end

				return GameEnum.TASK_STATUS_ACCEPT_PROCESS
			end
		end

		if task_id == TaskWGData.VIRTUAL_RI then
			return GameEnum.TASK_STATUS_CAN_ACCEPT
		end
	end

	return GameEnum.TASK_STATUS_NONE
end

--仙盟及建设任务类型6强制修改状态
function TaskWGData:NeedChangeStatus(task_id)
	local task_cfg = self:GetTaskConfig(task_id)

	if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_6 then
		return true
	end

	return false
end

function TaskWGData:PrintTask(task_id)
end


--获得npc身上当前的任务状态
function TaskWGData:GetNpcTaskStatus(npc_id)
	local task_cfg = self:GetNpcOneExitsTask(npc_id)
	if task_cfg ~= nil and task_cfg.task_id ~= nil then
		return self:GetTaskStatus(task_cfg.task_id)
	end
	return GameEnum.TASK_STATUS_NONE
end

--获得已接任务的怪物身上当前的任务状态
function TaskWGData:GetMonsterTaskStatus(monster_id)
	for k,v in pairs(self.task_accepted_info_list) do
		local task_cfg = self:GetTaskConfig(v.task_id)

		if task_cfg ~= nil and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 and task_cfg.c_param1 == monster_id then
			return self:GetTaskStatus(task_cfg.task_id)
		end
	end
	return GameEnum.TASK_STATUS_NONE
end

--获得是否是指定的任务目标
function TaskWGData:GetIsTaskTarget(target, task_cfg)
	if target == nil or task_cfg == nil then
		return false
	end

	local target_type = target:GetType()
	local target_cfg_id = 0
	if target_type == SceneObjType.Npc and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_0 then
		target_cfg_id = target:GetNpcId()
	elseif target_type == SceneObjType.Monster and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 and not target:IsDead() then
		target_cfg_id = target:GetVo().monster_id
	elseif target_type == SceneObjType.GatherObj and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_2 then
		target_cfg_id = target:GetVo().gather_id
	end

	if target_cfg_id ~= 0 and task_cfg.c_param1 == target_cfg_id then
		return true
	else
		return false
	end
end

--获得NPC身上的一个现有任务
function TaskWGData:GetNpcOneExitsTask(npc_id)
	local task_list = {}
	for k,v in pairs(self.task_accepted_info_list) do
		local task_cfg = self:GetTaskConfig(v.task_id)
		if task_cfg and type(task_cfg.commit_npc) == "table" and task_cfg.commit_npc.id == npc_id then
			task_list[#task_list + 1] = task_cfg
		end
	end
	for k,v in pairs(self.task_can_accept_id_list) do
		local task_cfg = self:GetTaskConfig(k)
		if task_cfg and type(task_cfg.accept_npc) == "table" and task_cfg.accept_npc.id == npc_id then
			task_list[#task_list + 1] = task_cfg
		end
	end


	local function SortFun(a, b)
		local w_1 = self:GetCompareWeight(a.task_id, a.task_type)
		local w_2 = self:GetCompareWeight(b.task_id, b.task_type)
		if w_1 >= w_2 then
			return false
		end
		return true
	end

	if #task_list ~= 0 then
		table.sort(task_list, SortFun)
	end

	-- 仙盟建设任务排最后
	local xian_men_task = nil
	--取最优
	for k,v in ipairs(task_list) do
		local status = self:GetTaskStatus(v.task_id)
		if status == GameEnum.TASK_STATUS_CAN_ACCEPT or status == GameEnum.TASK_STATUS_COMMIT then
			if v.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
				xian_men_task = v
			else
				return v
			end
		end
	end

	return xian_men_task or task_list[1]
end

--获得NPC身上的一个经验任务副本配置
function TaskWGData:GetExpTaskFbcfg(npc_id)
	local task_cfg = nil
	for k,v in pairs(self.task_accepted_info_list) do
		task_cfg = self:GetTaskConfig(v.task_id)
		if task_cfg
		and (task_cfg.task_type == GameEnum.TASK_TYPE_RI or task_cfg.task_type == GameEnum.TASK_TYPE_MENG
			 or task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD or task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN)
		and (task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 or task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_21)
		and not self:GetTaskIsCanCommint(v.task_id)
		and task_cfg.accept_npc.id == npc_id then
			return task_cfg
		end
	end
	return nil
end

-- 获取npc身上的一个转职任务副本配置
function TaskWGData:GetTransferTaskFbcfg(npc_id)
	local task_cfg = nil
	for k,v in pairs(self.task_accepted_info_list) do
		task_cfg = self:GetTaskConfig(v.task_id)
		if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN
		and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and not self:GetTaskIsCanCommint(v.task_id)
		and type(task_cfg.accept_npc) == "table" and task_cfg.accept_npc.id == npc_id then
			return task_cfg
		end
	end
	return nil
end

--获得npc的奖励描述
function TaskWGData:GetNpcRewardDesc(task_id)
	local reward_desc = ""
	local task_cfg = self:GetTaskConfig(task_id)
	if task_cfg == nil then
		return reward_desc
	end
	if task_cfg.exp ~= nil and task_cfg.exp ~= "" and task_cfg.exp > 0 then
		reward_desc = reward_desc .. string.format(Language.Task.reward_list[1],task_cfg.exp)
	end
	if task_cfg.coin_bind ~= nil and task_cfg.coin_bind ~= "" and task_cfg.coin_bind > 0 then
		reward_desc = "	" .. reward_desc .. string.format(Language.Task.reward_list[2],task_cfg.coin_bind)
	end
	if task_cfg.xianhun ~= nil and task_cfg.xianhun ~= "" and task_cfg.xianhun > 0 then
		reward_desc = "	" .. reward_desc .. string.format(Language.Task.reward_list[3],task_cfg.xianhun)
	end
	if task_cfg.yuanli ~= nil and task_cfg.yuanli ~= "" and task_cfg.yuanli > 0 then
		reward_desc = "	" .. reward_desc .. string.format(Language.Task.reward_list[4],task_cfg.yuanli)
	end
	return reward_desc
end

function TaskWGData:GetNotifyCallBackNum()
	local num = 0
	for _, v in pairs(self.notify_datalist_change_callback_list) do
		num = num + 1
	end

	for _, v in pairs(self.notify_data_change_callback_list) do
		num = num + 1
	end

	return num
end

function TaskWGData:NotifyDataChangeCallBack(callback, notify_datalist)
	if nil == callback then
		return
	end

	if notify_datalist == true then
		for _,v in ipairs(self.notify_datalist_change_callback_list) do
			if v == callback then
				return
			end
		end

		table.insert(self.notify_datalist_change_callback_list, callback)
		if #self.notify_datalist_change_callback_list >= 30 then
			print_error(string.format("监听任务List数据的地方多达%d条，请检查！",#self.notify_datalist_change_callback_list))
		end
	else
		for _,v in ipairs(self.notify_data_change_callback_list) do
			if v == callback then
				return
			end
		end

		table.insert(self.notify_data_change_callback_list, callback)
		if #self.notify_data_change_callback_list >= 30 then
			print_error(string.format("监听任务数据的地方多达%d条，请检查！",#self.notify_data_change_callback_list))
		end
	end
end


--移除绑定回调
function TaskWGData:UnNotifyDataChangeCallBack(callback)
	if callback == nil then
		return
	end

	for i,v in ipairs(self.notify_data_change_callback_list) do
		if v == callback then
			table.remove(self.notify_data_change_callback_list, i)
			break
		end
	end

	for i,v in ipairs(self.notify_datalist_change_callback_list) do
		if v == callback then
			table.remove(self.notify_datalist_change_callback_list, i)
			break
		end
	end
end

-- 获取任务场景ID 如果target_obj.scene没有 就取commit_npc.scene
function TaskWGData:GetSceneId(task_id)
	local config = self:GetTaskConfig(task_id)
	local scene_id = 0
	if config then
		if config.target_obj and config.target_obj[1] then
			scene_id = config.target_obj[1].scene
		elseif config.commit_npc then
			scene_id = config.commit_npc.scene
		end
	end
	return scene_id
end

function TaskWGData:GetSceneConfig(task_id)
    local scene_id = TaskWGData.Instance:GetSceneId(task_id)
    if scene_id ~= 0 then
   		return ConfigManager.Instance:GetSceneConfig(scene_id)
   	end
   	return nil
end

--任务是否已经接受
function TaskWGData:GetTaskIsAccepted(task_id)
	return self.task_accepted_info_list[task_id] ~= nil
end

--获得某个任务是否已完成过
function TaskWGData:GetTaskIsCompleted(task_id)
	if self.task_completed_id_list[task_id] ~= nil then
		return true
	end
	return false
end

--获得某个任务是否可提交
function TaskWGData:GetTaskIsCanCommint(task_id)
	return self:GetTaskStatus(task_id) == GameEnum.TASK_STATUS_COMMIT
end

--获得任务奖励配置
function TaskWGData:GetTaskRewardCfg(task_type)
	local level = RoleWGData.Instance.role_vo.level
	if task_type == GameEnum.TASK_TYPE_RI then
		local daily_task_reward_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").daily_task_reward
		for i, v in pairs(daily_task_reward_cfg) do
			if level >= v.level and level <= v.level_max then
				return v
			end
		end
		return nil
	end

	if task_type == GameEnum.TASK_TYPE_MENG then
		local guild_task_reward = ConfigManager.Instance:GetAutoConfig("tasklist_auto").guild_task_reward
		for k,v in pairs(guild_task_reward) do
			if v.level <= level and level <= v.level_max then
				return v
			end
		end
	end

	if task_type == GameEnum.TASK_TYPE_HU then
		local husong_reward_cfg = YunbiaoWGData.Instance:GetCurExitTaskRewardCfg()
		if husong_reward_cfg ~= nil then
			return {exp = husong_reward_cfg[1], xianhun = husong_reward_cfg[2]}
		end
	end

	return nil
end

function TaskWGData:GetVirtualGuajiTask()
	local mainrole_level = GameVoManager.Instance:GetMainRoleVo().level
	local cur_cfg = self.rest_reward_list[mainrole_level]

	if nil == cur_cfg then
		return nil
	end
    return nil
	-- return {
	-- 	task_id = 999999,
	-- 	task_type = GameEnum.TASK_TYPE_GUAJI,
	-- 	scene_id = cur_cfg["hang_monster_id_scene_id"],
	-- 	monster_id = cur_cfg["hang_monster_id"],
	-- 	x = cur_cfg["hang_monster_id_pos_x"],
	-- 	y = cur_cfg["hang_monster_id_pos_y"],
	-- }
end

function TaskWGData:GetVirtualLingTask()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("jinghuahusong_auto").other[1]
	return {
		task_id = 9999999,
		task_type = GameEnum.TASK_TYPE_LING,
		commit_npc = other_cfg.commit_npcid
	}
end

--获得任务剩余次数
function TaskWGData:GetTaskRemainTimes(task_type)
	if task_type == GameEnum.TASK_TYPE_HU then
		local buytimes = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT) --已购买次数
		local complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) --完成次数
		return math.max(YunbiaoWGData.Instance:GetTotalFreeHusonTimes() + buytimes - complete_times, 0)
	end

	if task_type == GameEnum.TASK_TYPE_MENG then
		local complete_num = self.task_guild_info.complete_task_count or 0
		return math.max(TaskWGData.Instance:GetTaskGuildWeekMaxCount() - complete_num, 0)
	end

	if task_type == GameEnum.TASK_TYPE_RI then
		local complete_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_COMMIT_DAILY_TASK_COUNT)
		return math.max(COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT - complete_num, 0)
	end

	if task_type == GameEnum.TASK_TYPE_CAMP then
		local complete_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_CAMP_TASK_COMPLETE_COUNT)
		return math.max(COMMON_CONSTS.TASK_CAMP_DAY_MAX_COUNT - complete_num, 0)
	end

	return 0
end

--是否可以接取护送任务(只考虑对角色的限制条件)
function TaskWGData:GetIsCanAcceptHusongTask()
	local flag = self:GetTaskIsCompleted(YunbiaoWGData.Instance:GetYubiaoPreTaskId())
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.HusongTask,true)
	if not is_open then
		flag = false
	end
	return flag
end

--是否可以接取仙盟任务(只考虑对角色的限制条件)
function TaskWGData:GetIsCanAcceptGuildTask()
	if RoleWGData.Instance.role_vo.guild_id == 0 then
		return false
	end

	if not self:GetTaskIsCompleted(COMMON_CONSTS.TASK_GUILD_PRVE_TASK) then
		return false
	end

	return true
end

function TaskWGData:GetTaskBtnEffect(task_id)
	-- 1.宠物升级   17309
	-- 2.添加好友   17410
	-- 3.加入队伍   17411
	-- 4.装备回收   17413
	-- 5.池塘养鱼   17529
	local status = self:GetTaskStatus(task_id)
	local role_level = RoleWGData.Instance.role_vo.level
	if status ~= GameEnum.TASK_STATUS_ACCEPT_PROCESS or role_level > 200 then
		return false
	else
		return true
	end
end

--是否已经过了新手期
function TaskWGData:IsShowXinshouFollow()
	return not self:GetTaskIsCompleted(COMMON_CONSTS.XINSHOUFLOOOW_ID)
end

-- 获取角色对话配置
function TaskWGData.GetRoleTalkCfg(task_id)
	local roletalk_cfg = ConfigManager.Instance:GetAutoConfig("roletalk_auto").play
	for k,v in pairs(roletalk_cfg) do
		if task_id == v.task_id then
			return v
		end
	end
	return nil
end

-- 帮派信息返回
function TaskWGData:BackGuildInfo(protocol)
	self.task_guild_info = protocol.task_guild_info
end

-- 获取帮派信息
function TaskWGData:GetGuildInfo()
	return self.task_guild_info or {}
end

-- 悬赏任务返回
function TaskWGData:BackSCBountyInfo(protocol)
	self.task_bounty_info = {}
	self.task_bounty_info = protocol.bountyTaskInfo
end

--获取悬赏任务信息
function TaskWGData:GetBountyInfo()
	return self.task_bounty_info or {}
end

function TaskWGData:GetOtherInfo()
	return ConfigManager.Instance:GetAutoConfig("tasklist_auto").other[1] or {}
end

function TaskWGData:GetRoleLevelReward(level)
	return self.role_level_reward_list[level]
end

-- 获取日常任务奖励
function TaskWGData:GetDailyReward(is_comple)
	local daily_task_reward = ConfigManager.Instance:GetAutoConfig("tasklist_auto").daily_task_reward

	if daily_task_reward == nil or next(daily_task_reward) == nil then
		return
	end

	local temp_level = RoleWGData.Instance.role_vo.level
	local level_reward_cfg = self:GetRoleLevelReward(temp_level)

	local temp_tab = {}
	for k,v in pairs(daily_task_reward) do
		if temp_level >= v.level and temp_level <= v.level_max then
			temp_tab = v
			break
		end
	end

	local temp_reward = {}
	temp_reward = TableCopy(temp_tab.reward_item) or {}
	table.insert(temp_reward, 1, temp_reward[0])

	-- 经验
	local exp = level_reward_cfg and level_reward_cfg.kill_monster_exp or 0
	local exp_daily_task = temp_tab.role_exp or 1
	local ext_tab = {
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_SAMLL_EXP,
		num = exp * exp_daily_task,
		is_bind = 1
	}

	if ext_tab.num and ext_tab.num > 0 then
		table.insert(temp_reward, ext_tab)
	end
	-- 绑元
	local bind_tab = {
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN,
		num = is_comple == true and temp_tab.complete_all_daily_reward_coin or temp_tab.bind_coin,
		is_bind = 1
	}
	if bind_tab.num > 0 then
		table.insert(temp_reward, bind_tab)
	end

	return temp_reward or {}
end

function TaskWGData:GetMengReward(is_comple)
	local task_data = TaskWGData.Instance:GetGuildInfo()
	local is_show_ten = (task_data.complete_task_count % 10) == 9

	local temp_level = RoleWGData.Instance.role_vo.level
	local guild_task_reward = ConfigManager.Instance:GetAutoConfig("tasklist_auto").guild_task_reward
	local guild_level = GuildDataConst.GUILDVO.guild_level
	local guild_task_mythicallist = self.guild_task_mythicallist[guild_level]
	if not guild_task_reward or IsEmptyTable(guild_task_reward) then
		return
	end

	local temp_tab = {}
	for k,v in pairs(guild_task_reward) do
		if temp_level >= v.level and temp_level <= v.level_max then
			temp_tab = v
			break
		end
	end

	local temp_reward = {}
	if is_show_ten then
		for k,v in pairs(temp_tab.finish_reward_item) do
			temp_reward[#temp_reward + 1] = TableCopy(v)
		end
	else
		for k,v in pairs(temp_tab.reward_item) do
			temp_reward[#temp_reward + 1] = TableCopy(v)
		end
	end

	local my_yunshi = GuildWGData.Instance:GetMyYunShiInfo()
	local curr_cfg = GuildWGData.Instance:GetLuckyCfg(my_yunshi.lucky_color)
	if curr_cfg then
		for k,v in pairs(temp_reward) do
			v.num = v.num + math.floor(v.num * curr_cfg.task_factor / 100)
		end
	end

	local level_reward_cfg = self:GetRoleLevelReward(temp_level)

	-- 经验
	local exp = level_reward_cfg and level_reward_cfg.kill_monster_exp or 0
	local ext_tab = {
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_SAMLL_EXP,
		num = is_show_ten == true and temp_tab.finish_exp*exp or temp_tab.exp*exp,
		is_bind = 0
	}

	if ext_tab.num > 0 then
		table.insert(temp_reward,ext_tab)
	end

	-- 铜币
	local coin_tab = {
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN,
		num = is_show_ten == true and temp_tab.finish_coin or temp_tab.coin,
		is_bind = 1
	}
	if coin_tab.num > 0 then
		table.insert(temp_reward,coin_tab)
	end

	local gongxian_tab = {
		item_id = COMMON_CONSTS.GongXianId,
		num = is_show_ten == true and temp_tab.finish_gongxian or temp_tab.gongxian,
		is_bind = 1
	}
	if gongxian_tab.num > 0 then
		table.insert(temp_reward,gongxian_tab)
	end

	-- 任务奖励建设度
	local guild_exp_tab = {
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_90735,
		num = is_show_ten == true and temp_tab.finish_guild_exp or temp_tab.monocycle_guild_exp,
		is_bind = 1
	}
	if guild_exp_tab.num > 0 then
		table.insert(temp_reward,guild_exp_tab)
	end

	if guild_task_mythicallist then
		-- 贡品
		local tab = {
			item_id = COMMON_CONSTS.VIRTUAL_ITEM_90736,
			num = is_show_ten == true and guild_task_mythicallist.finish_gongpin or guild_task_mythicallist.shenshogongpin,
			is_bind = 1
		}
		if tab.num > 0 then
			table.insert(temp_reward,tab)
		end
	end

	return temp_reward or {}
end

--获取悬赏奖励
function TaskWGData:GetShangReward(is_comple)
	local shang_task_reward = ConfigManager.Instance:GetAutoConfig("tasklist_auto").bounty_task_reward

	if shang_task_reward == nil or next(shang_task_reward) == nil then
		return
	end

	local temp_level = RoleWGData.Instance.role_vo.level
	local temp_tab = {}
	for k,v in pairs(shang_task_reward) do
		if temp_level >= v.min_level and temp_level <= v.max_level then
			temp_tab = v
			break
		end
	end
	local temp_reward = {}
	if is_comple then  	--全部
		temp_reward = TableCopy(temp_tab.reward_item) or {}
	else 				--单环
		temp_reward = TableCopy(temp_tab.single_reward_item) or {}
	end
	table.insert(temp_reward, 1, temp_reward[0])
	-- 经验
	local ext_tab = {
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_SAMLL_EXP,
		num = is_comple == true and temp_tab.exp or temp_tab.single_exp,
		is_bind = 1
	}
	if is_comple == false then
		table.insert(temp_reward,ext_tab)
	end
	return temp_reward
end

--获取悬赏任务怪物配置
function TaskWGData:GetMonstTargetCfgList(monst_id)
	local monst_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").monst_level
	for k,v in pairs(monst_cfg) do
		if v.monst_id == monst_id then
			return v
		end
	end
end

-- 开始做规定的任务
function TaskWGData:DoTask(task_id)
	local task_info = self:GetTaskInfo(task_id)
	local task_cfg = self:GetTaskConfig(task_id)
	if not task_info or not task_cfg then
		return
	end

	TaskWGCtrl.Instance:OperateFollowTask(task_cfg)
end

-- 挂机自动寻怪
function TaskWGData:GuaJiGetMonster()
	local main_role_level = GameVoManager.Instance:GetMainRoleVo().level
	local points_cfg = self.rest_reward_list[main_role_level]
	if points_cfg and next(points_cfg) ~= nil then
		Scene.Instance:GetSceneLogic():FlyToPos(points_cfg["pre_pos_x"], points_cfg["pre_pos_y"],
		points_cfg["pre_scene_id"], SceneObjType.Monster, false)
	end
end

-- 策划说，只显示对应等级的装备
function TaskWGData:GetConsumeEquipment(level, color)
	local equipment_list = {}
	local prof = RoleWGData.Instance:GetRoleProf() or 0
	for k,v in pairs(ItemWGData.Instance:GetBagItemDataList()) do
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)

		if item_cfg and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
		and v.param
		and v.param.star_level <= 2
		and item_cfg.color == color
		-- and item_cfg.color <= GameEnum.ITEM_COLOR_ORANGE
		and item_cfg.order >= level then
			equipment_list[#equipment_list + 1] = v
		end
	end
	return equipment_list
end

function TaskWGData:GetZhuanzhiCfg()
	local zhuanzhi_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").zhaunzhi_task_list
	return zhuanzhi_cfg
end

function TaskWGData:GetZhuanzhiJieDuan(zhuan_num)
	local zhuanzhi_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").zhaunzhi_task_list
	local task_list = zhuanzhi_cfg[zhuan_num]
	local index = 0
	for i = task_list.first_task, task_list.end_task do
		index = index + 1
		if TaskWGData.Instance:GetTaskStatus(i) ~= GameEnum.TASK_STATUS_NONE then
			return index
		end
	end
	return 0
end

function TaskWGData:HasContentedTask(task_id, condition)
	if condition == 1 then
		return self:GetTaskIsAccepted(task_id) or self:GetTaskIsCompleted(task_id)
	elseif condition == 2 then
		return self:GetTaskIsCanCommint(task_id) or self:GetTaskIsCompleted(task_id)
	elseif condition == 3 then
		return self:GetTaskIsCompleted(task_id)
	end
	return false
end

function TaskWGData:GetGainTipBtnList()
	local cfg = ConfigManager.Instance:GetAutoConfig("task_level_guide_auto")
	local role_level = RoleWGData.Instance.role_vo.level
	local btn_data_list = {}
	local id = 0
	local add_flag = false
	for k,v in pairs(cfg.level_guide) do
		if role_level >= v.level_min and role_level <= v.level_max then
			local t = Split(v.sort, "|")
			local t1
			for k1,v1 in pairs(t) do
				t1 = Split(v1, ",")
				id = tonumber(t1[1])
				table.insert(btn_data_list, {cfg = cfg.guide_way[tonumber(t1[1])], tuijian = tonumber(t1[2])})
			end
			break
		end
	end

	for i, v in ipairs(btn_data_list) do
		if v.cfg.fun_name ~= "" and not FunOpen.Instance:GetFunIsOpened(v.cfg.fun_name) then
			v.need_remove = true
		else
			-- 赏金任务
			if v.cfg.id == UP_LV_GUIDE_ID.RI_CHANG then
				v.need_remove = TaskWGData.Instance:GetBountyAcceptLimit()
				-- v.need_remove = DailyWGData.Instance:GetIsAllCommitTask()
				-- v.need_remove = BiZuoWGData.Instance:GetIsComplete(BIZUO_TYPE.RI_CHANG)
			-- 经验祈福
			elseif v.cfg.id == UP_LV_GUIDE_ID.QIFU then
				local _, _, exp_buy_times = QiFuWGData.Instance:GetAllCount()
				v.need_remove = exp_buy_times <= 0
            elseif v.cfg.id == UP_LV_GUIDE_ID.FRXZ then
                --print_error("FuBenPanelWGData.Instance:IsShowTianXianRedPoint()",FuBenPanelWGData.Instance:IsShowTianXianRedPoint())
				v.need_remove = FuBenPanelWGData.Instance:IsShowTianXianRedPoint() == 0
			elseif v.cfg.id == UP_LV_GUIDE_ID.DJXZ then
				local other_cfg = WuJinJiTanWGData.GetOtherCfg()
				local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
				local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
				local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
				local enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
				local now_count = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and VipPower.Instance:GetParam(VipPowerId.linghunguangchang_buy_times)
				or VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)

				-- 普通船票
				local total_item_num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM)
				if VipWGData.Instance:GetRoleVipLevel() >= 4 then
					-- VIP船票
					local vip_item_num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM_VIP)
					total_item_num = total_item_num + vip_item_num
				end

				v.need_remove = enter_dj_times < 1 and now_count <= yet_buy_time and (total_item_num <= 0)

			-- 竞技场 （有挑战次数 出现推荐 ； 没挑战次数 不显示推荐 ；）
			elseif v.cfg.id == UP_LV_GUIDE_ID.ACT_JJC then
				local feild_info = Field1v1WGData.Instance:GetUserinfo()
				if feild_info then
					local tiaozhan_times = Field1v1WGData.Instance:GetResidueTiaoZhanNum()
					v.need_remove = tiaozhan_times <= 0
				end

			-- 仙盟建设（当玩家完成今日建设任务，途径隐藏）
			elseif v.cfg.id == UP_LV_GUIDE_ID.GUILD_BUILD then
				v.need_remove = GuildWGData.Instance:GetBuildTaskIsAllFinish()

			-- 海底废墟（当玩家买完可购买次数且用完，途径隐藏；通过vip提升增加了次数，又重新出现）
			elseif v.cfg.id == UP_LV_GUIDE_ID.EQUIP_FUBEN then
				local free_times = TeamEquipFbWGData.Instance:GetHighTeamEquipFbOther().everyday_times 	-- 免费次数
				local can_buy_times = TeamEquipFbWGData.Instance:GetCanBuyTimes() 						-- VIP次数
				local enter_times = TeamEquipFbWGData.Instance:GetHTEFbEnteredTimes() 					-- 进入次数
				v.need_remove = enter_times >= (free_times + can_buy_times)

			-- 首充（玩家完成了首充后隐藏）
			elseif v.cfg.id == UP_LV_GUIDE_ID.FIRST_RECHARGE then
                v.need_remove = RechargeWGData.Instance:GetHistoryRecharge() > 0--充值任意金额就隐藏
            elseif v.cfg.id == UP_LV_GUIDE_ID.WORLD_BOSS then
                local cur_times, _ = BossWGData.Instance:GetWorldBossTimes()
				v.need_remove = cur_times <= 0 and FunOpen.Instance:GetFunIsOpened(FunName.BossVip) == false
			elseif v.cfg.id == UP_LV_GUIDE_ID.HuSong then
				if YunbiaoWGData.Instance:GetSurplusTimes() <= 0 then
					v.need_remove = true
				end
			end
		end
	end
	local btn_list = {}
	for i, v in ipairs(btn_data_list) do
		if not v.need_remove then
			table.insert(btn_list, v)
		end
    end
    --策划让配置配置推荐的没次数了之后，不是配置推荐的要显示成推荐的
    local has_tuijian = false
    for i, v in ipairs(btn_list) do
		if v.tuijian == 1 then
            has_tuijian = true
            break
		end
    end
    if not has_tuijian then
        for i, v in ipairs(btn_list) do
            if v.tuijian == 0 then
                v.tuijian = 1
            end
        end
    else
        table.sort(btn_list, SortTools.KeyUpperSorter("tuijian"))
    end
	return btn_list
end

function TaskWGData:GetGainCapaTipBtnList()
	local cfg = ConfigManager.Instance:GetAutoConfig("task_level_guide_auto")
	local role_level = RoleWGData.Instance.role_vo.level
	local btn_data_list = {}
	local id = 0
	local add_flag = false
	for k,v in pairs(cfg.attack_guide) do
		if role_level >= v.level_min and role_level <= v.level_max then
			local t = Split(v.sort, "|")
			local t1
			for k1,v1 in pairs(t) do
				t1 = Split(v1, ",")
				id = tonumber(t1[1])
				table.insert(btn_data_list, {cfg = cfg.guide_way[tonumber(t1[1])], tuijian = tonumber(t1[2])})
			end
			break
		end
	end

	for i, v in ipairs(btn_data_list) do
		if v.cfg.fun_name ~= "" and not FunOpen.Instance:GetFunIsOpened(v.cfg.fun_name) then
			v.need_remove = true
		else
			-- 赏金任务
			if v.cfg.id == UP_LV_GUIDE_ID.RI_CHANG then
				v.need_remove = DailyWGData.Instance:GetIsAllCommitTask()
				-- v.need_remove = BiZuoWGData.Instance:GetIsComplete(BIZUO_TYPE.RI_CHANG)
			-- 经验祈福
			elseif v.cfg.id == UP_LV_GUIDE_ID.QIFU then
				local _, _, exp_buy_times = QiFuWGData.Instance:GetAllCount()
				v.need_remove = exp_buy_times <= 0
            elseif v.cfg.id == UP_LV_GUIDE_ID.FRXZ then
                --print_error("FuBenPanelWGData.Instance:IsShowTianXianRedPoint()",FuBenPanelWGData.Instance:IsShowTianXianRedPoint())
				v.need_remove = FuBenPanelWGData.Instance:IsShowTianXianRedPoint() == 0
			elseif v.cfg.id == UP_LV_GUIDE_ID.DJXZ then
				local other_cfg = WuJinJiTanWGData.GetOtherCfg()
				local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
				local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
				local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
				local enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
				local now_count = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and VipPower.Instance:GetParam(VipPowerId.linghunguangchang_buy_times)
				or VipPower.Instance:GetParam(VipPowerId.exp_fb_buy_times)

				-- 普通船票
				local total_item_num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM)
				if VipWGData.Instance:GetRoleVipLevel() >= 4 then
					-- VIP船票
					local vip_item_num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.EXP_COUNT_ITEM_VIP)
					total_item_num = total_item_num + vip_item_num
				end

				v.need_remove = enter_dj_times < 1 and now_count <= yet_buy_time and (total_item_num <= 0)

			-- 竞技场 （有挑战次数 出现推荐 ； 没挑战次数 不显示推荐 ；）
			elseif v.cfg.id == UP_LV_GUIDE_ID.ACT_JJC then
				local feild_info = Field1v1WGData.Instance:GetUserinfo()
				if feild_info then
					local tiaozhan_times = Field1v1WGData.Instance:GetResidueTiaoZhanNum()
					v.need_remove = tiaozhan_times <= 0
				end

			-- 仙盟建设（当玩家完成今日建设任务，途径隐藏）
			elseif v.cfg.id == UP_LV_GUIDE_ID.GUILD_BUILD then
				v.need_remove = GuildWGData.Instance:GetBuildTaskIsAllFinish()

			-- 海底废墟（当玩家买完可购买次数且用完，途径隐藏；通过vip提升增加了次数，又重新出现）
			elseif v.cfg.id == UP_LV_GUIDE_ID.EQUIP_FUBEN then
				local free_times = TeamEquipFbWGData.Instance:GetHighTeamEquipFbOther().everyday_times 	-- 免费次数
				local can_buy_times = TeamEquipFbWGData.Instance:GetCanBuyTimes() 						-- VIP次数
				local enter_times = TeamEquipFbWGData.Instance:GetHTEFbEnteredTimes() 					-- 进入次数
				v.need_remove = enter_times >= (free_times + can_buy_times)

			-- 首充（玩家完成了首充后隐藏）
			elseif v.cfg.id == UP_LV_GUIDE_ID.FIRST_RECHARGE then
                v.need_remove = RechargeWGData.Instance:GetHistoryRecharge() > 0--充值任意金额就隐藏
            elseif v.cfg.id == UP_LV_GUIDE_ID.WORLD_BOSS then
                local cur_times, _ = BossWGData.Instance:GetWorldBossTimes()
				v.need_remove = cur_times <= 0 and FunOpen.Instance:GetFunIsOpened(FunName.BossVip) == false
			end
		end
	end
	local btn_list = {}
	for i, v in ipairs(btn_data_list) do
		if not v.need_remove then
			table.insert(btn_list, v)
		end
	end

	return btn_list
end

function TaskWGData:GetTaskPromptList()
	return self.task_prompt_list
end

function TaskWGData:GetTaskPromptIsOpen()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	return role_level >= 50 and role_level <= 200
end

--获得任务配置数据
function TaskWGData:GetNPCConfig(npc_id)
	if npc_id == nil then
		return nil
	end

	return self.npc_cfg_list[npc_id]
end

function TaskWGData:GetNPCRange(npc_id)
	local cfg = self.npc_cfg_list[npc_id]
	return (cfg and type(cfg.range) == "number") and cfg.range or COMMON_CONSTS.TASK_NPC_MAX_RANGE
end

function TaskWGData:GetTaskProDesById(task_id)
	local task_cfg = self:GetTaskConfig(task_id)
	local task_status = self:GetTaskStatus(task_id)
	local task_info = self:GetTaskInfo(task_id)
	local desc = ""
	if not task_cfg then
		return desc
	end

    if task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
        desc = task_cfg.accept_desc
    elseif task_status == GameEnum.TASK_STATUS_COMMIT then
    	if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
    		desc = Language.Guild.GuildBuildRewardLq
    	else
    		desc = task_cfg.commit_desc
    	end
    elseif task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
        desc = task_cfg.progress_desc
        if nil ~= task_info then
            local per = task_info.progress_num .. "/" .. task_cfg.c_param2
            desc = XmlUtil.RelaceTagContent(desc, "per", per)
        end
    end

    return desc
end

--获取task任务奖励
function TaskWGData:GetTaskCellReward(task_id)
	self.task_cell_reward_list = self.task_cell_reward_list or {}
	local reward_item = self.task_cell_reward_list[task_id]
	if reward_item then
		return reward_item
	end

	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()
	local id = string.format("showitem_%s_%s", sex, prof)
	if task_cfg and task_cfg[id] then
		local list = Split(task_cfg[id], ",")
		local item_id = tonumber(list[1])
		if item_id and item_id > 0 then
			reward_item = {item_id = item_id, num = tonumber(list[2]) or 1}
		end
	end

	self.task_cell_reward_list[task_id] = reward_item
	return reward_item
end

function TaskWGData:GetTaskReward(task_id)
	local reward_list
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)

	if task_cfg then
		if task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
			reward_list = TaskWGData.Instance:GetMengReward(false)
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then
			reward_list = TaskWGData.Instance:GetDailyReward(false)
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
			reward_list = GuildWGData.Instance:GetGuildBuildTaskReward(task_id)
		else
			reward_list = {}
			local index = 1
			if task_cfg.exp ~= nil and task_cfg.exp > 0 then
				reward_list[index] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_SAMLL_EXP, num = task_cfg.exp, is_bind= 0}
				index = index + 1
			end
	
			if task_cfg.coin_bind ~= nil and task_cfg.coin_bind > 0 then
				reward_list[index] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = task_cfg.coin_bind, is_bind = 0}
				index = index + 1
			end
	
			if task_cfg.silverticket ~= nil and task_cfg.silverticket > 0 then
				reward_list[index] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD, num = task_cfg.silverticket, is_bind=0}
				index = index+1
			end
	
			if task_cfg.shengwang ~= nil and task_cfg.shengwang > 0 then
				reward_list[index] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, num = task_cfg.shengwang, is_bind = 0}
				index = index+1
			end
	
			if task_cfg.zhenqi ~= nil and task_cfg.zhenqi > 0 then
				reward_list[index] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_ZHENGQI, num = task_cfg.zhenqi, is_bind = 0}
				index = index+1
			end
	
			local prof = RoleWGData.Instance:GetRoleProf()
			local sex = RoleWGData.Instance:GetRoleSex()
			local id = string.format("prof_list_%s_%s", sex, prof)
			if next(task_cfg.item_list) ~= nil then
				reward_list[index] = task_cfg.item_list[0]
				index = index + 1
			elseif next(task_cfg[id]) ~= nil then
				reward_list[index] = task_cfg[id][0]
				index = index + 1
			end
		end
	end

	return reward_list
end

function TaskWGData:GetLastTaskByType(task_type)
	if not self.last_task_id then
		self.last_task_id = {}
	end

	if self.last_task_id[task_type] then
		return self.last_task_id[task_type]
	end

	local task_cfg
	for k,v in pairs(self.task_cfg_list_dic[task_type] or {}) do
		if not task_cfg or task_cfg.task_id < v.task_id then
			task_cfg = v
		end
	end

	self.last_task_id[task_type] = task_cfg
	return self.last_task_id[task_type]
end

function TaskWGData:ExplainHideCfg(hide_cfg)
	local hide_list = {}
	local tab = nil
	for k,v in pairs(hide_cfg) do
		tab = string.split(v.sections, "|")
		for i=1,#tab do
			tab[i] = string.split(tab[i], ";")
			for m=1,#tab[i] do
				tab[i][m] = string.split(tab[i][m], ",")
				tab[i][m][1] = tonumber(tab[i][m][1])
				tab[i][m][2] = tonumber(tab[i][m][2])
			end
		end
		hide_list[k] = tab
	end

	return hide_list
end

function TaskWGData:GetNPCHideCfg(npc_id)
	return self.hide_npc_cfg[npc_id]
end

function TaskWGData:GetGatherHideCfg(gather_id)
	return self.hide_gather_cfg[gather_id]
end

-- npc 是否显示
function TaskWGData:IsNPCVisible(npc_id)
	if not self.hide_npc_cfg[npc_id] then
		return true
	end

	local flag = self:CheckTargetVisible(self.hide_npc_cfg[npc_id])
	return flag
end

-- 采集物 是否显示
function TaskWGData:IsGatherVisible(gather_id)
	if not self.hide_gather_cfg[gather_id] or not TaskGuide.Instance:InitTask() then
		return true
	end

	local flag = self:CheckTargetVisible(self.hide_gather_cfg[gather_id])
	return flag
end

function TaskWGData:CheckTargetVisible(hide_list)
	if not hide_list then
		return true
	end

	local task_id = nil
	local task_list = self:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
	if 0 < #task_list then
		task_id = task_list[1]
	else
		local next_task = self:GetNextZhuTaskConfig()
		if next_task then
			task_id = next_task.task_id
		end
	end

	if not task_id then
		return false
	end

	local status = self:GetTaskStatusType(task_id)
	local tab = nil
	for i=1,#hide_list do
		tab = hide_list[i]

		if not IsEmptyTable(tab) then
			local one_one_task_id = (tab[1] or {})[1] or -1
			local one_two_task_id = (tab[1] or {})[2] or -1
			local two_one_task_id = (tab[2] or {})[1] or -1
			local two_two_task_id = (tab[2] or {})[2] or -1

			if not tab[2] then
				if one_one_task_id < task_id then
					return true
				elseif one_one_task_id == task_id then
					return one_two_task_id <= status
				end
			elseif one_one_task_id < task_id and task_id < two_one_task_id then
				return true
			elseif one_one_task_id == task_id then
				return one_two_task_id <= status
			elseif two_one_task_id == task_id then
				return status <= two_two_task_id
			end
		end
	end

	return false
end

function TaskWGData:GetTaskStatusType(task_id)
	local status = self:GetTaskStatus(task_id)
	if status == GameEnum.TASK_STATUS_FINISH then
		return TaskStatusType.Type3
	elseif status == GameEnum.TASK_STATUS_COMMIT then
		return TaskStatusType.Type2
	elseif status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
		return TaskStatusType.Type1
	end
	return TaskStatusType.None
end

-- 小地图不显示的NPC
function TaskWGData:GetAllHideNpcList()
	if self.all_hide_npc then
		return self.all_hide_npc
	end

	local task_cfg = self:GetLastTaskByType(GameEnum.TASK_TYPE_ZHU)
	if not task_cfg then
		self.all_hide_npc = {}
    	return self.all_hide_npc
	end

	local list = Split(task_cfg.hide_npc, "#")
	local len = #list
    for i=1,len do
        list[i] = tonumber(list[i])
    end

    self.all_hide_npc = list
    return self.all_hide_npc
end

function TaskWGData:ShowWeatherEff(task_id)
	local scene_id = Scene.Instance:GetSceneId()
	if self.weather_effect_cfg and self.weather_effect_cfg[scene_id] then
		for k,v in pairs(self.weather_effect_cfg[scene_id]) do
			if v.open_task_id <= task_id and (v.close_task_id == "" or v.close_task_id >= task_id or v.close_task_id == -1) then
				return true, v.bundle, v.asset, v.voice
			end
		end
	end
	return false
end

function TaskWGData:CheckFollowNpc(task_id, is_complete)
	local cfg = self.task_cfg_list[task_id]
	if cfg == nil or cfg.accept_npc == nil or type(cfg.accept_npc) ~= "table" or cfg.accept_npc.id == nil then
		return
	end

	if cfg.npc_action == 1 then
		local npc_id = is_complete ~= 0 and cfg.accept_npc.id or nil
		if TaskWGData.FOLLOW_NPC ~= npc_id then
			TaskWGData.FOLLOW_NPC = npc_id
			TaskWGData.FOLLOW_TASK = task_id
			GlobalEventSystem:Fire(ObjectEventType.FOLLOW_NPC_CHANGE, TaskWGData.FOLLOW_NPC)
		end
	end
end

-- 获取下一下任务
function TaskWGData:GetNextTaskCfgById(task_type, task_id)
	local list = self.task_cfg_list_dic[task_type]
	if IsEmptyTable(list) then
		return nil
	end

	for k,v in pairs(list) do
		if v.pretaskid == task_id then
			return v
		end
	end

	return nil
end


--------------------------------仙盟建设任务----------------------------------
--获取已接的仙盟建设任务配置
function TaskWGData:GetTaskGuildBuildCfg()
	--可接取
	if self.task_can_accept_id_list then
		for k,v in pairs(self.task_can_accept_id_list) do
			local task_cfg = self:GetTaskConfig(k)
			if task_cfg then
				if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
					return task_cfg
				end
			end
		end
	end

	--已接取
	if self.task_accepted_info_list then
		for k,v in pairs(self.task_accepted_info_list) do
			local task_cfg = self:GetTaskConfig(v.task_id)
			if task_cfg then
				if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
					return task_cfg
				end
			end
		end
	end
end

-- 获取任务排序权重
function TaskWGData:GetTaskSortWeight(task_id, sections_id)
	local task_cfg = self:GetTaskConfig(task_id)
	local weight = 0
	if not task_cfg then
		return weight
	end

	local sort_cfg = nil
	if task_cfg.task_type == GameEnum.TASK_TYPE_ZHI then
		sort_cfg = self.task_sort[task_cfg.taskgroup]
	else
		sort_cfg = self.task_sort[task_cfg.task_type]
	end

	if not sort_cfg then
		return weight
	end

	weight = sort_cfg["s" .. sections_id] or weight
	return weight
end

-- 需要剔除的任务
function TaskWGData:RejectTask(task_cfg)
	if task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
		return 0 == GuildDataConst.GUILDVO.guild_id
	end

	return false
end

function TaskWGData:CheckRiChangRedPoint()
	if not FunOpen.Instance:GetFunIsOpened("daily_task") then
		return false
	end

	--屏蔽每日悬赏任务红点
	-- local task_data = DailyWGData.Instance:GetTaskTuMoData()
	-- if task_data == nil or DailyWGData.Instance:GetIsAcceptTask() then
	-- 	return false
	-- end

	-- if not self:GetBountyAcceptLimit() then
	-- 	return true
	-- end

	-- if self:CheckRiChangNoteRedPoint() then
	-- 	return true
	-- end

	return false
end

function TaskWGData:CheckRiChangNoteRedPoint()
	local note_list = TaskWGData.Instance:GetBountyTaskNoteList()
	for k, v in pairs(note_list) do
		if v.task_note_id then
			local _, note_is_lock = self:GetTaskNoteList(v.task_note_id)
			local is_flag = self:GetBountyNoteRewardStatus(v.task_note_id) == 1
			local is_can_get = (not is_flag) and (not note_is_lock)

			if is_can_get then
				return true
			end
		end
	end

	return false
end

-- 获取变身路径
function TaskWGData:ChangeResInfo(id)
	local res_info = self.change_res[id]
	if res_info then
		return res_info.res, res_info.id
	end
end

-- 获取特殊任务时的高级坐骑
function TaskWGData:ChangeMountResInfo(id)
	local res_info = self.change_mount_res[id]
	if res_info then
		return res_info.res, res_info.id
	end
end

function TaskWGData:TaskToOpenView(task_data)
	local status = 0
	if GameEnum.DATALIST_CHANGE_REASON_ADD == task_data.reason then
		status = TaskStatusType.Type1
	elseif GameEnum.DATALIST_CHANGE_REASON_UPDATE == task_data.reason then
		if 1 == task_data.is_complete then
			status = TaskStatusType.Type2
		else
			status = TaskStatusType.Type4
		end
	elseif GameEnum.DATALIST_CHANGE_REASON_REMOVE == task_data.reason then
		status = TaskStatusType.Type3
	end

    for k, v in pairs(self.push_cfg) do
		if 1 == v.type and task_data.task_id == v.param1 and status == v.param2 and self:CheckView(v.view_url) then
			local open_key = v.open_key
			if open_key ~= nil and open_key == "" then
				open_key = nil
			end

			ViewManager.Instance:Open(v.view_url, v.open_param, open_key)
			ViewManager.Instance:FlushView(v.view_url, v.open_param, "task_open")
			if v.shut_time > 0 then
				self:SetDelayCloseView(v.view_url, v.shut_time)
			end

			return
		end
	end
end

function TaskWGData:CheckView(view_url)
	if view_url == GuideModuleName.FirstRechargeView then
		if RechargeWGData.Instance:GetIsFirstRecharge() then
			return false
		end
	end

	return true
end

function TaskWGData:SetDelayCloseView(close_view_name, shut_time)
	if CountDownManager.Instance:HasCountDown("task_push" .. close_view_name) then         
		CountDownManager.Instance:RemoveCountDown("task_push" .. close_view_name)
    end

	CountDownManager.Instance:AddCountDown("task_push" .. close_view_name, 
		function(elapse_time, total_time)

		end, 

		function()
			ViewManager.Instance:Close(close_view_name)
		end,

		nil, shut_time, 1)
end

function TaskWGData:LevelToOpenView(key, value, old_value)
	if key ~= "level" or 0 == old_value or old_value >= value then
		return
	end

	for k, v in pairs(self.push_cfg) do
		if 2 == v.type and value == v.param1 and self:CheckView(v.view_url) then
			ViewManager.Instance:Open(v.view_url)
			return
		end
	end
end

--判断当前任务是否已经完成并且提交
function TaskWGData:GetCurTaskState(scene_type)
	local cfg = self.xinshou_view_ctrl[scene_type]
	if nil == cfg then
		return true
	end

	local is_finish = TaskWGData.Instance:GetTaskIsCompleted(cfg.task_id)
	if not is_finish then
		return false
	end

	return true
end

-- 是否可接赏金任务
function TaskWGData:IsCanAcceptShangjing()
	local is_accpet_task = DailyWGData.Instance:GetIsAcceptTask()
	-- return 1 == task_data.commit_times and 0 == task_data.task_id
	return not is_accpet_task
end

-- ignore 忽略是否新手
local shangjin_npc_cfg
function TaskWGData:DoShangJinTask(ignore_xs)
	-- local call_back = function ()
	-- 	ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
	-- end
	if TaskWGData.Instance:IsCanAcceptShangjing() then
		if nil == shangjin_npc_cfg then

			local scene_config = ConfigManager.Instance:GetSceneConfig(self.daily_task_scene_id)
			for k,v in pairs(scene_config.npcs) do
				if v.id == GameEnum.ShangJin_Npc then
					shangjin_npc_cfg = v
					break
				end
			end
		end

		if shangjin_npc_cfg then
			MoveCache.SetEndType(MoveEndType.ClickNpc)
			MoveCache.param1 = shangjin_npc_cfg.id
			local range = TaskWGData.Instance:GetNPCRange(shangjin_npc_cfg.id)
			GuajiWGCtrl.Instance:MoveToPos(self.daily_task_scene_id, shangjin_npc_cfg.x, shangjin_npc_cfg.y, range, false, nil, nil, nil, 1)
		end
	else
		TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, ignore_xs)
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
    end
end

function TaskWGData:RemberCurAuToTaskType(task_type)
	self.rember_cur_task_type = task_type
end

function TaskWGData:GetCurAuToTaskType()
	return self.rember_cur_task_type or -1
end

--日常（赏金任务是否全部完成,最终的宝箱奖励不在其中，只是任务奖励）
function TaskWGData:DailyTaskIsFinsh()
	local task_data = DailyWGData.Instance:GetTaskTuMoData()
	if nil == task_data then
		return false
	end

	local commit_times = task_data.commit_times or 0
	if commit_times > COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT then
		return true
	end

	return false
end

function TaskWGData:DailyTaskIsOpen()
	local task_other_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").other[1]
	if nil == task_other_cfg then
		return false
	end

	local daily_pretask = task_other_cfg.daily_pretask or 0
	local status = self:GetTaskIsCompleted(daily_pretask)

	if status then
		return true
	end

	return false
end

function TaskWGData:GetTaskOpen(task_type)
	if task_type == GameEnum.TASK_TYPE_RI then
		return self:DailyTaskIsOpen()
	end

	return true
end

function TaskWGData:GetTaskCameraType(camera_condition)
	local task_status = GameEnum.TASK_STATUS_NONE
	if camera_condition == nil then
		return task_status
	end

	if camera_condition == TASK_CAMERA_TYPE.CAN_ACCEPT then
		task_status = GameEnum.TASK_STATUS_CAN_ACCEPT
	elseif camera_condition == TASK_CAMERA_TYPE.CAN_COMMIT then
		task_status = GameEnum.TASK_STATUS_COMMIT
	elseif camera_condition == TASK_CAMERA_TYPE.FINISH then
		task_status = GameEnum.TASK_STATUS_FINISH
	end

	return task_status
end

function TaskWGData:GetCurAutoTaskInfo(task_type)
	local task_accepted_info_list = self:GetTaskAcceptedInfoList()
	local task_can_accept_id_list = self:GetTaskCapAcceptedIdList()

	if task_accepted_info_list then
		for k,v in pairs(task_accepted_info_list) do
			local task_cfg = self:GetTaskConfig(v.task_id)
			if task_cfg then
				if task_cfg.task_type == task_type then
					return task_cfg
				end
			end
		end
	end

	if task_can_accept_id_list then
		for k,v in pairs(task_can_accept_id_list) do
			local task_cfg = self:GetTaskConfig(k)
			if task_cfg then
				if task_cfg.task_type == task_type then
					return task_cfg
				end
			end
		end
	end
end

function TaskWGData:GetShangRandMonstID()
	return self.shang_rand_monst_id
end

function TaskWGData:SetShangRandMonstID(id)
	self.shang_rand_monst_id = id
end

function TaskWGData:InitZhuTaskSortList()
	self.zhu_task_sort_list = {}
	self.zhu_task_weight_list = {}
	local first_task = self.task_cfg_list[10]
	local sort_value = 1

	if first_task ~= nil then
		local cur_task_id = first_task.task_id
		local last_task_id = nil
		local pretask_dic = {}
		local task_dic = {}
		for k,v in pairs(self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHU]) do
			if not (v.pretaskid == nil or v.pretaskid == "" or v.pretaskid == 0)then
				if pretask_dic[v.pretaskid] == nil then
					pretask_dic[v.pretaskid] = v.task_id
				end
			end
		end

		for k,v in pairs(self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHU]) do
			if pretask_dic[v.task_id] == nil and first_task.task_id ~= v.task_id then
				last_task_id = v.task_id
				break
			end
		end

		if last_task_id ~= nil then
			sort_value = #self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHU]
			for i = 1, #self.task_cfg_list_dic[GameEnum.TASK_TYPE_ZHU] do
				local cfg = self.task_cfg_list[last_task_id]
				if cfg ~= nil then
					self.zhu_task_sort_list[sort_value] = last_task_id
					sort_value = sort_value - 1
					self.zhu_task_weight_list[last_task_id] = sort_value

					last_task_id = cfg.pretaskid
				end
			end
		end
	end
end

function TaskWGData:GetCompareWeight(task_id, task_type)
	local w = task_id
	if task_type ~= nil and task_type == GameEnum.TASK_TYPE_ZHU and task_id ~= nil then
		w = self.zhu_task_weight_list[task_id] or 0
	end

	return w
end

-- 用于每天第一次的赏金任务，仅用来引导玩家去赏金栏
function TaskWGData:GetVirtualRiTask()
	if not FunOpen.Instance:GetFunIsOpened("daily_task") then
		return
	end

	local task_data = DailyWGData.Instance:GetTaskTuMoData()
	if task_data == nil or DailyWGData.Instance:GetIsAcceptTask() or self:GetBountyAcceptLimit() then
		return
	end

	local tab = self:GetTaskConfig(TaskWGData.VIRTUAL_RI)
	return tab
end

function TaskWGData:GetCurLevelGuaJiMonster(lv)
	local monst_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").monst_level

	for _, v in pairs(monst_cfg) do
		if v.min_level <= lv and v.max_level >= lv then
			return v
		end
	end
end

function TaskWGData:GetTaskListCache()
	return self.task_list_cache
end

function TaskWGData:GetCurrTaskId()
	return self.curr_task_id
end

function TaskWGData:OneKeyBtnIsOpen()
	return RoleWGData.Instance:GetRoleLevel() >= TaskWGData.Instance:GetOtherInfo().daily_task_start
end

function TaskWGData:GetTaskCallInfoCfg()
	return self.task_call_info
end

------------------------悬赏任务Start---------------------------
-- 拆分一下悬赏单任务列表
function TaskWGData:BountyCfgInfoInit()
	self.bounty_task_list_chche = {}
	self.bounty_final_task_list_chche = {}

	if self.bounty_list_cfg then
		for k, v in pairs(self.bounty_list_cfg) do
			if v and v.bounty_id then
				self.bounty_task_list_chche[v.bounty_id] = {}
				self.bounty_final_task_list_chche[v.bounty_id] = {}
				local task_str_list = Split(v.task_list, "|")
				local finale_task_str_list = Split(v.finale_task_list, "|")
				
				for index, task_str in ipairs(task_str_list) do
					local value = tonumber(task_str) or 0
					table.insert(self.bounty_task_list_chche[v.bounty_id], value)
				end
	
				for index, task_str in ipairs(finale_task_str_list) do
					local value = tonumber(task_str) or 0
					table.insert(self.bounty_final_task_list_chche[v.bounty_id], value)
				end
			end
		end
	end

	self.bounty_story_list_chche = {}
	self.bounty_final_story_list_chche = {}

	if self.bounty_list_cfg then
		for k, v in pairs(self.bounty_task_note_cfg) do
			if v and v.task_note_id then
				self.bounty_story_list_chche[v.task_note_id] = {}
				self.bounty_final_story_list_chche[v.task_note_id] = {}

				local task_type_str_list = Split(v.task_type_list, "|")
				local finale_task_str_list = Split(v.finale_task_list, "|")
	
				for index, type_str in ipairs(task_type_str_list) do
					local value = tonumber(type_str) or 0
					table.insert(self.bounty_story_list_chche[v.task_note_id], value)
				end
	
				for index, finale_task_str in ipairs(finale_task_str_list) do
					self.bounty_final_story_list_chche[v.task_note_id][index] = {}
					local finale_task_str_list2 = Split(finale_task_str, ",")

					for i, str in ipairs(finale_task_str_list2) do
						local value_2 = tonumber(str) or 0
						table.insert(self.bounty_final_story_list_chche[v.task_note_id][index], value_2)
					end
				end
			end
		end
	end
end

-- 获取悬赏榜单基础配置表
function TaskWGData:GetBountyBaseCfg()
	return self.bounty_base_cfg
end

-- 获取当前的悬赏榜单
function TaskWGData:GetBountyCfgById(bounty_id)
	local empty = {}
	return (self.bounty_list_cfg or empty)[bounty_id]
end

-- 获取当前的悬赏榜单的任务列表
function TaskWGData:GetBountyTaskListCfgById(bounty_id)
	local empty = {}
	return (self.bounty_task_list_chche or empty)[bounty_id]
end

-- 获取当前的悬赏榜单的结局任务列表
function TaskWGData:GetBountyFinalTaskListCfgById(bounty_id)
	local empty = {}
	return (self.bounty_final_task_list_chche or empty)[bounty_id]
end

-- 获取当前的手记的普通列表类型
function TaskWGData:GetBountyStoryTaskListCfgById(task_note_id)
	local empty = {}
	return (self.bounty_story_list_chche or empty)[task_note_id]
end

-- 获取当前的手记的结局列表类型
function TaskWGData:GetBountyStoryFinalTaskListCfgById(task_note_id)
	local empty = {}
	return (self.bounty_final_story_list_chche or empty)[task_note_id]
end

-- 获取当前的悬赏榜单任务
function TaskWGData:GetBountyTaskCfgByTaskId(task_id)
	local empty = {}
	return (self.bounty_task_cfg or empty)[task_id]
end

-- 获取当前类型的悬赏任务列表根据类型
function TaskWGData:GetBountyTaskListByType(task_type)
	local empty = {}
	return (self.bounty_task_type_list_cfg or empty)[task_type]
end
-- 获取当前的悬赏手记
function TaskWGData:GetBountyTaskNoteCfgByNoteId(task_note_id)
	local empty = {}
	return (self.bounty_task_note_cfg or empty)[task_note_id]
end

-- 获取所有的悬赏手记列表
function TaskWGData:GetBountyTaskNoteList()
	return self.bounty_task_note_cfg 
end

-- 获取任务的配置
function TaskWGData:GetBountyFinaleOpenByTask(task_id)
	local empty = {}
	return (self.bounty_finale_open_cfg or empty)[task_id]
end

-- 设置悬赏榜单信息
function TaskWGData:SetBountyListInfo(protocol)
	self.bounty_list_info = protocol
end

-- 获取当前的悬赏榜单
function TaskWGData:GetBountyList()
	local empty = {}
	return (self.bounty_list_info or empty).show_bounty_list_id
end

-- 获取当前的接受的悬赏id
function TaskWGData:GetCurrAcceptBountyId()
	local empty = {}
	return (self.bounty_list_info or empty).cur_accept_bounty_id or 0
end

-- 获取当前接取的悬赏单
function TaskWGData:GetBountyListInfo()
	return self.bounty_list_info
end

-- 获取当前的手记奖励是否领取
function TaskWGData:GetBountyNoteRewardStatus(note_id)
	local info = self:GetBountyListInfo()
	local empty = {}
	local bounty_note_reward_flag = (info or empty).bounty_note_reward_flag

	return (bounty_note_reward_flag or empty)[note_id] or 0
end

-- 获取当前悬赏单一键完成的金额
function TaskWGData:GetBountyQuickFinishSpend(bounty_id)
	local num = 0
	local bounty_list_info = self:GetBountyListInfo()
	local task_list = self:GetBountyTaskListCfgById(bounty_id)
	
	if (not bounty_list_info) or (not task_list) or #task_list <= 0 then
		return num
	end

	local star_index = bounty_list_info.cur_execulte_task_seq + 1
	-- 当前普通任务一键花费
	for i = star_index, #task_list do
		local task_id = task_list[i]
		local cfg = self:GetBountyTaskCfgByTaskId(task_id)
		local quick_finish_spend = cfg and cfg.quick_finish_spend or 0
		num = num + quick_finish_spend
	end

	-- 获取结局一键完成花费
	local cfg = self:GetBountyTaskCfgByTaskId(bounty_list_info.cur_finale_task_id)
	local quick_finish_spend = cfg and cfg.quick_finish_spend or 0
	num = num + quick_finish_spend

	return num
end

-- 获取当前可以接取的悬赏榜单任务
function TaskWGData:GetCurrBountyTask(is_update)
	local bounty_list_info = self:GetBountyListInfo()
	local bounty_id = bounty_list_info.cur_accept_bounty_id

	if (not bounty_list_info) or bounty_id == -1 then
		return nil
	end

	local task_list = self:GetBountyTaskListCfgById(bounty_id)
	if (not task_list) or #task_list <= 0 then
		return nil
	end

	local star_index = bounty_list_info.cur_execulte_task_seq + 1
	if is_update and self.update_index == star_index then
		return nil
	end

	if is_update then
		self.update_index = star_index
	end
	
	if star_index > #task_list then
		return bounty_list_info.cur_finale_task_id
	end
	return task_list[star_index]
end

-- 是否接取了悬赏单
function TaskWGData:IsAcceptBounty()
	local curr_bounty_id = self:GetCurrAcceptBountyId()
	return curr_bounty_id > -1
end

-- 获取任务的解锁状态
function TaskWGData:BountyTaskIsLock(task_id)
	local final_task_cfg = self:GetBountyFinaleOpenByTask(task_id)
	if final_task_cfg == nil then
		print_error("任务的解锁状态", task_id)
	end

	local seq = final_task_cfg and final_task_cfg.seq or 0
	local bounty_id = final_task_cfg and final_task_cfg.bounty_id or 0
	local info = self:GetBountyListInfo()
	local empty = {}

	local bount_final_flag = ((info or empty).bounty_info or empty)[bounty_id]
	return bount_final_flag[seq] == 0
end

-- 获取任务的解锁状态
function TaskWGData:GetBountyFinalTaskNum(bounty_id)
	local final_task_list = self:GetBountyFinalTaskListCfgById(bounty_id)
	local unlock_num = 0

	for i, task_id in ipairs(final_task_list) do
		local is_lock = self:BountyTaskIsLock(task_id)

		if not is_lock then
			unlock_num = unlock_num + 1
		end
	end

	return unlock_num, #final_task_list
end

-- 获取任务是否已达接取上限
function TaskWGData:GetBountyAcceptLimit()
	local base_cfg = self:GetBountyBaseCfg()
	local info = TaskWGData.Instance:GetBountyListInfo()
	local bounty_accept_limit = base_cfg and base_cfg.bounty_accept_limit or 0
	local today_accept_bounty_num = info and info.today_accept_bounty_num or 0
	local last_accept_num = bounty_accept_limit - today_accept_bounty_num

	return last_accept_num <= 0
end

-- 获取手记的剧情列表
function TaskWGData:GetTaskNoteList(task_note_id)
	local normal_list = self:GetBountyStoryTaskListCfgById(task_note_id)
	local final_list = self:GetBountyStoryFinalTaskListCfgById(task_note_id)
	local note_is_lock = false
	local note_is_active = false
	local story_list = {}

	local normal_is_lock = function(task_type, task_list)
		local type_list = task_list
		local normal_task_id = nil

		if task_type ~= nil then
			type_list = self:GetBountyTaskListByType(task_type)
		end

		if not type_list then
			return true, normal_task_id
		end

		for i, v in ipairs(type_list) do
			normal_task_id = v

			if task_type ~= nil then
				normal_task_id = v.task_id
			end

			local is_lock = self:BountyTaskIsLock(normal_task_id)
			if not is_lock then
				return false, normal_task_id
			end
		end

		return true, normal_task_id
	end

	for i, v in ipairs(normal_list) do
		local data = {}
		data.story_type = TASK_NOTE_TYPE.NORMAL
		local is_lock, task_id = normal_is_lock(v)
		data.params = task_id
		data.is_lock = is_lock

		if data.is_lock then
			note_is_lock = data.is_lock
		else
			note_is_active = true
		end

		table.insert(story_list, data)
	end

	for i, v in ipairs(final_list) do
		local data = {}
		data.story_type = TASK_NOTE_TYPE.FINAL
		local is_lock, task_id = normal_is_lock(nil, v)
		data.params = task_id
		data.is_lock = is_lock

		if data.is_lock then
			note_is_lock = data.is_lock
		else
			note_is_active = true
		end

		table.insert(story_list, data)
	end

	return story_list, note_is_lock, note_is_active
end
------------------------悬赏任务End---------------------------

function TaskWGData:GetTaskMonsterCfg(task_id, monster_id)
	return (self.task_monster_cfg[task_id] or {})[monster_id]
end