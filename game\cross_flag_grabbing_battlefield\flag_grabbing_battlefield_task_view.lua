FlagGrabbingBattleFieldTaskView = FlagGrabbingBattleFieldTaskView or BaseClass(SafeBaseView)

function FlagGrabbingBattleFieldTaskView:__init()
	self.view_layer = UiLayer.MainUIHigh
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_task_view")
end

function FlagGrabbingBattleFieldTaskView:LoadCallBack()
    if not self.fgb_boss_list then
		self.fgb_boss_list = AsyncListView.New(FGBAreaItemRender, self.node_list.fgb_boss_list)
		self.fgb_boss_list:SetStartZeroIndex(true)
	end

	if not self.fgb_task_reward_list then
		self.fgb_task_reward_list = AsyncListView.New(FGBTaskRewardItemRender, self.node_list.fgb_task_reward_list)
	end

	XUI.AddClickEventListener(self.node_list.btn_fgb_rank_list, BindTool.Bind(self.OnClickFGBRankList, self))
	XUI.AddClickEventListener(self.node_list.btn_reward_list, BindTool.Bind(self.OnClickRewardShow, self))

	self:InitCallBack()
end

function FlagGrabbingBattleFieldTaskView:ReleaseCallBack()
	if self.fgb_boss_list then
		self.fgb_boss_list:DeleteMe()
		self.fgb_boss_list = nil
	end

	if self.fgb_task_reward_list then
		self.fgb_task_reward_list:DeleteMe()
		self.fgb_task_reward_list = nil
	end
end

function FlagGrabbingBattleFieldTaskView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.node_list["fp_move_left_node"].transform:SetParent(parent.gameObject.transform)
	self.node_list["fp_move_left_node"].transform.localPosition = Vector3.zero
	self.node_list["fp_move_left_node"].transform.localScale = Vector3.one
end

function FlagGrabbingBattleFieldTaskView:CloseCallBack()
    if self.node_list["fp_move_left_node"] then
        self.node_list["fp_move_left_node"]:SetActive(false)
        self.node_list["fp_move_left_node"].transform:SetParent(self.root_node_transform, false)
    end
end

function FlagGrabbingBattleFieldTaskView:OnFlush()
    local point_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendList()
	self.fgb_boss_list:SetDataList(point_list)

	local reward_list = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBTaskRewardDataList()
	self.fgb_task_reward_list:SetDataList(reward_list)
	self.node_list.fgb_task_pannel_desc.text.text = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBTaskScoreAddDesc()
end

function FlagGrabbingBattleFieldTaskView:OnClickFGBRankList()
	CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBRankView()
end

function FlagGrabbingBattleFieldTaskView:OnClickRewardShow()
    CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBRewardShowView()
end

----------------------------------FGBAreaItemRender--------------------------------------
FGBAreaItemRender = FGBAreaItemRender or BaseClass(BaseRender)

function FGBAreaItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGoArea, self))
end

function FGBAreaItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.area_morale.text.text = string.format(Language.FlagGrabbingBattlefield.FGBTaskScoreAddPerDesc, self.data.score_added_per / 10000)

	local seq = self.data.seq
	local data_info = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendInfoBySeq(seq)

	if not IsEmptyTable(data_info) then
		local left_progress = (data_info.camp_value_list or {})[0] or 0
		local right_progress = (data_info.camp_value_list or {})[1] or 0

		local team_name = Language.FlagGrabbingBattlefield.FGBAreaPointNomalName[2]
		if left_progress ~= right_progress then
			local team_id = left_progress > right_progress and 0 or 1
			team_name = Language.FlagGrabbingBattlefield.FGBAreaPointNomalName[team_id]
		end

		self.node_list.area_name.text.text = string.format(Language.FlagGrabbingBattlefield.FGBTaskAreaName, self.data.contend_name, team_name)
		self.node_list["left_process"].slider.value = left_progress / 10
		self.node_list["right_process"].slider.value = right_progress / 10
	end
end

function FGBAreaItemRender:OnClickGoArea()
	CrossFlagGrabbingBattleFieldWGCtrl.Instance:FGBGoToArea(self.data)
end

----------------------------------FGBTaskRewardItemRender--------------------------------------
FGBTaskRewardItemRender = FGBTaskRewardItemRender or BaseClass(BaseRender)

function FGBTaskRewardItemRender:LoadCallBack()
	if not self.task_reward_list then
		self.task_reward_list = AsyncListView.New(ItemCell, self.node_list.task_reward_list)
		self.task_reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_more_reward, BindTool.Bind(self.OnClickMoreReward, self))
end

function FGBTaskRewardItemRender:__delete()
	if self.task_reward_list then
		self.task_reward_list:DeleteMe()
		self.task_reward_list = nil
	end
end

function FGBTaskRewardItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.task_reward_list:SetDataList(self.data.reward_cfg)
	local is_score = self.data.is_score
	local score_desc = is_score and Language.FlagGrabbingBattlefield.ScoreTaskItemDesc or Language.FlagGrabbingBattlefield.KillTaskItemDesc
	-- local target_desc = is_score and Language.FlagGrabbingBattlefield.ScoreTaskItemTargetDesc or Language.FlagGrabbingBattlefield.SkilTaskItemTargetDesc
	self.node_list.task_reward_name.text.text = string.format(score_desc, self.data.cur_num, self.data.target_num)
end

function FGBTaskRewardItemRender:OnClickMoreReward()
	if IsEmptyTable(self.data) then
		return
	end

	CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBPersonRewardView(self.data.is_score)
end