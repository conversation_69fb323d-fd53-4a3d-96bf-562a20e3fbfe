﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPro_TMP_SpriteAssetWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(TMPro.TMP_SpriteAsset), typeof(TMPro.TMP_Asset));
		<PERSON><PERSON>RegFunction("UpdateLookupTables", UpdateLookupTables);
		<PERSON><PERSON>Function("GetSpriteIndexFromHashcode", GetSpriteIndexFromHashcode);
		<PERSON><PERSON>Function("GetSpriteIndexFromUnicode", GetSpriteIndexFromUnicode);
		<PERSON>.RegFunction("GetSpriteIndexFromName", GetSpriteIndexFromName);
		<PERSON><PERSON>Function("SearchForSpriteByUnicode", SearchForSpriteByUnicode);
		L.RegFunction("SearchForSpriteByHashCode", SearchForSpriteByHashCode);
		<PERSON><PERSON>RegFunction("SortGlyphTable", SortGlyphTable);
		<PERSON><PERSON>unction("New", _CreateTMPro_TMP_SpriteAsset);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		L.RegVar("spriteSheet", get_spriteSheet, set_spriteSheet);
		L.RegVar("spriteInfoList", get_spriteInfoList, set_spriteInfoList);
		L.RegVar("fallbackSpriteAssets", get_fallbackSpriteAssets, set_fallbackSpriteAssets);
		L.RegVar("version", get_version, null);
		L.RegVar("faceInfo", get_faceInfo, null);
		L.RegVar("spriteCharacterTable", get_spriteCharacterTable, null);
		L.RegVar("spriteCharacterLookupTable", get_spriteCharacterLookupTable, null);
		L.RegVar("spriteGlyphTable", get_spriteGlyphTable, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateTMPro_TMP_SpriteAsset(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				TMPro.TMP_SpriteAsset obj = new TMPro.TMP_SpriteAsset();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: TMPro.TMP_SpriteAsset.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateLookupTables(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			obj.UpdateLookupTables();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSpriteIndexFromHashcode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int o = obj.GetSpriteIndexFromHashcode(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSpriteIndexFromUnicode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 2);
			int o = obj.GetSpriteIndexFromUnicode(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSpriteIndexFromName(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			int o = obj.GetSpriteIndexFromName(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SearchForSpriteByUnicode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			TMPro.TMP_SpriteAsset arg0 = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			uint arg1 = (uint)LuaDLL.luaL_checknumber(L, 2);
			bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
			int arg3;
			TMPro.TMP_SpriteAsset o = TMPro.TMP_SpriteAsset.SearchForSpriteByUnicode(arg0, arg1, arg2, out arg3);
			ToLua.Push(L, o);
			LuaDLL.lua_pushinteger(L, arg3);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SearchForSpriteByHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			TMPro.TMP_SpriteAsset arg0 = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
			int arg3;
			TMPro.TMP_SpriteAsset o = TMPro.TMP_SpriteAsset.SearchForSpriteByHashCode(arg0, arg1, arg2, out arg3);
			ToLua.Push(L, o);
			LuaDLL.lua_pushinteger(L, arg3);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SortGlyphTable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)ToLua.CheckObject<TMPro.TMP_SpriteAsset>(L, 1);
			obj.SortGlyphTable();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteSheet(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			UnityEngine.Texture ret = obj.spriteSheet;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteSheet on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteInfoList(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.List<TMPro.TMP_Sprite> ret = obj.spriteInfoList;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteInfoList on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fallbackSpriteAssets(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.List<TMPro.TMP_SpriteAsset> ret = obj.fallbackSpriteAssets;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fallbackSpriteAssets on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_version(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			string ret = obj.version;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index version on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_faceInfo(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			UnityEngine.TextCore.FaceInfo ret = obj.faceInfo;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index faceInfo on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteCharacterTable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.List<TMPro.TMP_SpriteCharacter> ret = obj.spriteCharacterTable;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteCharacterTable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteCharacterLookupTable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.Dictionary<uint,TMPro.TMP_SpriteCharacter> ret = obj.spriteCharacterLookupTable;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteCharacterLookupTable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteGlyphTable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.List<TMPro.TMP_SpriteGlyph> ret = obj.spriteGlyphTable;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteGlyphTable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_spriteSheet(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.spriteSheet = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteSheet on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_spriteInfoList(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.List<TMPro.TMP_Sprite> arg0 = (System.Collections.Generic.List<TMPro.TMP_Sprite>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<TMPro.TMP_Sprite>));
			obj.spriteInfoList = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteInfoList on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fallbackSpriteAssets(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_SpriteAsset obj = (TMPro.TMP_SpriteAsset)o;
			System.Collections.Generic.List<TMPro.TMP_SpriteAsset> arg0 = (System.Collections.Generic.List<TMPro.TMP_SpriteAsset>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<TMPro.TMP_SpriteAsset>));
			obj.fallbackSpriteAssets = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fallbackSpriteAssets on a nil value");
		}
	}
}

