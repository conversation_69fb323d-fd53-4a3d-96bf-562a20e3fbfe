require("game/scene/scene_config")
require("game/scene/scene_wg_data")
require("game/scene/scene_protocal")
require("game/scene/scene_effect")
require("game/scene/scene_camera")
require("game/scene/boss_camera")
require("game/scene/scene_obj_lod_mgr")
require("game/scene/widget/guide_arrow")
require("game/scene/loading/scene_loading")
require("game/scene/loading/subpackage")
require("game/scene/loading/predownload")
require("game/scene/loading/freedownload")
require("game/scene/scene_logic/scene_logic")
require("game/scene/follow_ui/follow_ui")
require("game/scene/follow_ui/under_follow_ui")
require("game/scene/follow_ui/character_follow")
require("game/scene/follow_ui/answer_circle_follow")
require("game/scene/follow_ui/pet_follow")
require("game/scene/follow_ui/xunyou_follow_ui")
require("game/scene/follow_ui/role_follow")
require("game/scene/follow_ui/monster_follow")
require("game/scene/follow_ui/duck_race_follow")
require("game/scene/follow_ui/boss_follow")
require("game/scene/follow_ui/npc_follow")
require("game/scene/follow_ui/gather_follow")
require("game/scene/follow_ui/fallitem_follow")
require("game/scene/follow_ui/follow_object_follow")
require("game/scene/follow_ui/main_role_follow")
require("game/scene/follow_ui/beast_follow")
require("game/scene/follow_ui/task_callinfo_follow")
require("game/scene/sceneobj/shadow_obj")
require("game/scene/sceneobj/scene_obj")
require("game/scene/sceneobj/character")
require("game/scene/sceneobj/role")
require("game/scene/sceneobj/skill_shower")
require("game/scene/sceneobj/skill_shower_monster")
require("game/scene/sceneobj/main_role")
require("game/scene/sceneobj/follow_obj")
require("game/scene/sceneobj/monster")
require("game/scene/sceneobj/boss")
require("game/scene/sceneobj/tower")
require("game/scene/sceneobj/door")
require("game/scene/sceneobj/jump_point")
require("game/scene/sceneobj/effect_obj")
require("game/scene/sceneobj/fall_item")
require("game/scene/sceneobj/gather_obj")
require("game/scene/sceneobj/npc")
require("game/scene/sceneobj/walk_npc")
require("game/scene/sceneobj/wuhun_obj")
require("game/scene/sceneobj/truck_obj")
require("game/scene/sceneobj/map_move_obj")
require("game/scene/sceneobj/guard_obj")
require("game/scene/sceneobj/statue")
-- require("game/scene/sceneobj/spirit_obj")
require("game/scene/sceneobj/soulboy_obj")
require("game/scene/sceneobj/boss_stone_obj")
require("game/scene/sceneobj/defense_tower_obj")
require("game/scene/sceneobj/trigger_obj")
require("game/scene/sceneobj/event_obj")
require("game/scene/sceneobj/ming_ren_role")
require("game/scene/sceneobj/boat_obj")
require("game/scene/sceneobj/pet_obj")
require("game/scene/sceneobj/lingchong_obj")
require("game/scene/sceneobj/beauty_obj")
require("game/scene/sceneobj/follow_npc")
require("game/scene/sceneobj/mingjiang_obj")
require("game/scene/sceneobj/test_role")
require("game/scene/sceneobj/couple_halo_obj")
require("game/scene/sceneobj/beast_obj")
require("game/scene/sceneobj/task_callinfo_obj")
require("game/scene/sceneobj/shuangsheng_tianshen_obj")
require("game/scene/sceneobj/action_config/role_action_config")
require("game/scene/sceneobj/action_config/shuangsheng_tianshen_action_config")
require("game/scene/sceneobj/action_config/tianshen_boss_action_config")
require("game/scene/sceneobj/action_config/monster_action_config")
require("game/scene/sceneobj/action_config/pet_action_config")
require("game/scene/sceneobj/action_config/other_action_config")
require("game/scene/sceneobj/action_config/wuhun_action_config")
require("game/scene/sceneobj/action_config/zuoqi_action_config")
require("game/scene/sceneobj/action_config/yushou_action_config")
require("game/scene/sceneobj/action_config/gundam_action_config")
require("game/scene/sceneobj/marry_obj")
require("game/scene/sceneobj/follow_mingjiang")
require("game/scene/sceneobj/city_owner_statue")
require("game/scene/sceneobj/xiaotianquan_obj")
require("game/scene/sceneobj/qiongqi_laixi_obj")
require("game/scene/sceneobj/answer_circle")
require("game/scene/sceneobj/skill_preview_role")
require("game/scene/sceneobj/skill_preview_monster")
require("game/scene/sceneobj/skill_preview_lingchong")
-- require("game/scene/sceneobj/dog_obj")

local TypeUnitySprite = typeof(UnityEngine.Sprite)
local SceneManager = UnityEngine.SceneManagement.SceneManager

SceneObjLayer = GameObject.Find("GameRoot/SceneObjLayer")
UI3DModelLight = GameObject.Find("GameRoot/UI3DModelLight")
local HeroLight = nil
local AudioClipT = typeof(UnityEngine.AudioClip)
local MoveableObjectT = typeof(MoveableObject)
local AudioSourceT = typeof(UnityEngine.AudioSource)
local NeedCheckBundle = true
local NewPlayTestSceneId = 1000
if UNITY_EDITOR or IS_LOCLA_WINDOWS_DEBUG_EXE then
    NeedCheckBundle = false
end

local SPECIAL_DOOR = {
    TASK_CHAIN = 99999,
    MAIN_COUNTRY = 99998,
}

Scene = Scene or BaseClass(BaseWGCtrl)

Scene.CurShowSceneBundle = ""
Scene.CurShowSceneAsset = ""

function Scene:__init()
    if Scene.Instance then
        print_error("[Scene] Attempt to create singleton twice!")
        return
    end
    Scene.Instance = self

    self.data = SceneWGData.New()
    self.subpackage = SubPackage.New()
    self.predownload = PreDownload.New()
    self.freedownload = FreeDownload.New()
    self.scene_obj_lod = SceneObjLODManager.New()

    self.start_loading_time = nil
    self.is_scene_visible = true
    self.scene_loading = SceneLoading.New()

    self.guide_arrow = nil

    self.main_role = MainRole.New(GameVoManager.Instance:GetMainRoleVo())
    self.obj_list = {}
    self.obj_group_list = {}

    self.delay_handle_funcs = {}
    -- 场景移动对象
    self.obj_move_info_list = {}
    self.ui_scene_controller_list = {}
    self.ui_scene_config_index = {}
    self.main_role_pos_x = 0
    self.main_role_pos_y = 0
    self.last_check_fall_item_time = 0
    self.next_can_reduce_mem_time = 0
    self.enter_scene_count = 0
    self.act_scene_id = 0

    self.is_first_enter_scene = false
    self.is_change_sceneing = false -- 是否在切换场景中（前端请求切场景时，服务器会有0.333s的延迟）,可以防止切场景时的一切错误操作
    self.is_scene_loading_state_enter = false

    self.act_scene_id = 0
    self.scene_logic = nil
    -- 重置切换场景标记的时间 10s
    self.reset_total_time = 10
    self.reset_change_sceneing_time = 0

    -- 预加载副本场景管理（支持多个副本）
    self.preloaded_fuben_scenes = {} -- 格式: {bundle_name = {asset_name = {...}, ...}, ...}
    self.preloaded_fuben_scene_info = {} -- 详细信息: {{bundle_name, asset_name, priority, is_showing}, ...}
    self.current_showing_fuben_scene = nil -- 当前显示的预加载副本场景 {bundle_name, asset_name}

    self:RegisterAllProtocols()-- 注册所有需要响应的协议
    self:RegisterAllEvents()-- 注册所有需要监听的事件
    self:InitHandleVolumeController()
    -- 场景特效
    self.effect_list = {}

    -- 温泉皮艇
    self.boat_list = {}

    -- 夫妻光环
    self.couple_halo_obj_list = {}

    self.shield_npc_id_list = {}
    -- 客户端模拟掉落id
    self.fall_objId_list = {}

    -- 动作同步值
    self.sync_action_normalized = {}

    -- 掉落目标怪特效
    self.drop_effect_list = {}

    -- self.click_handler = BindTool.Bind(self.OnSceneObjClick, self)
    -- EasyTouch.On_SimpleTap = EasyTouch.On_SimpleTap + self.click_handler

    -- 监听游戏设置改变
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_MOVE_END, BindTool.Bind(self.PlayerMoveEnd, self))
    self:BindGlobalEvent(ObjectEventType.CG_EVENT_END, BindTool.Bind(self.OnCgEnd, self))
    self:BindGlobalEvent(ObjectEventType.SHIELD_OTHER_SCENEOBJ_FOLLOWUI_IN_DEPTH_OF_FIELD, BindTool.Bind(self.ShieldOtherSceneObjFollowUIInDepthOfField, self))
    
    Runner.Instance:AddRunObj(self, 6)
    self.effect_cd = 0

    self.scene_obj_lod:SetQualityLevel(QualityConfig.QualityLevel)
    self.quality_node = QualityConfig.ListenQualityChanged(function()
        self:OnQualityChanged()
    end)

    self.is_npc_talk_lock = false
    self.fog_render_setting = {}

    local fb_config = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto")
    self.scene_fog_cfg = ListToMapList(fb_config.scene_fog, "scene_id")

    local ui_scene_config = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto")
    self.ui_scene_controller_cfg = ListToMap(ui_scene_config.ui_scene_controller, "index")
    self.ui_scene_cfg = ui_scene_config.ui_scene
    -- ui_scene_index到asset_name的映射缓存
    self.ui_scene_index_to_asset_name = {}
    if self.ui_scene_cfg then
        for k, v in pairs(self.ui_scene_cfg) do
            if v.type and v.asset_name then
                self.ui_scene_index_to_asset_name[v.type] = v.asset_name
            end
        end
    end

    self.hight_fog_obj = nil
    self.hight_fog_is_init = false

    self.is_in_skill_rang = false
    self.skill_rang_info = nil

    self.hero_light_active = true


    self.rang_special_scale = 0
    self.ui_probe_size = nil
    self.ui_probe = nil
    self.yy_wave_effect = nil

    self.wait_check_gather_list = nil
    self.lately_gather_obj_id = nil
    self.is_ignore_lately_check = false

    self.scene_volume_controller = nil
    self.scene_show_volume = nil

    self.scene_day_night = nil
    self.scene_day_night_moment_index = nil
    self.pick_item_id_list = nil
    self.spirit_change_player = nil

    self.is_report_mem = false
    self.low_memory_report = BindTool.Bind(self.OnLowMemoryCheck, self)
    UnityEngine.Application.lowMemory = UnityEngine.Application.lowMemory + self.low_memory_report

    self:InitSceneCameraLua()
    self:InitSceneEffectLua()
end

function Scene:__delete()
    self:DeleteScenceCameraLua()
    self:DeleteScenceEffectLua()
    self:DeleteMainRole()
    self.freedownload:DeleteMe()
    self.predownload:DeleteMe()
    self.subpackage:DeleteMe()
    self.data:DeleteMe()

    self.scene_loading:DeleteMe()

    if self.quality_node ~= nil then
        QualityConfig.UnlistenQualtiy(self.quality_node)
        self.quality_node = nil
    end

    if nil ~= self.clickHandle and nil ~= ClickManager.Instance then
        ClickManager.Instance:UnlistenClickGround(self.clickHandle)
        self.clickHandle = nil
    end
	self.hide_follow_ui_obj_list = {}
    self.diao_qiao = nil
    EasyTouch.On_SimpleTap = EasyTouch.On_SimpleTap - self.click_handler
    self:DelateAllObj(false)
    self:ClearScene()
    Scene.Instance = nil
    Runner.Instance:RemoveRunObj(self)

    self:DelSceneEffect()
    self:DelSceneSound()
    self.scene_obj_lod:DeleteMe()
    self:ClearAllDropEffect()
    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end
    self.fog_render_setting = {}
    self.bg_dome = nil

    if self.probe_loader ~= nil then
    	self.probe_loader:Destroy()
    end

    self.probe_loader = nil

    self.hight_fog_obj = nil

    self.skill_rang_transform = nil
    self.skill_rang_render = nil
    self.skill_rang_render_t = nil
    self.skill_dir_square_render = nil
    self.skill_dir_square_t = nil
    self.hero_light_avtive = false

    self.rang_special_scale = 0

    self.hight_fog_is_init = false
    self.ui_scene_controller_list = nil
    self.ui_scene_config_index = nil
    self.ui_scene_index_to_asset_name = nil

    -- 清理预加载副本场景状态
    self.preloaded_fuben_scenes = {}
    self.preloaded_fuben_scene_info = {}
    self.current_showing_fuben_scene = nil

    if self.skill_rang_loader ~= nil then
        self.skill_rang_loader:Destroy()
    end
    self.skill_rang_loader = nil

    self.ui_probe_size = nil
    self.ui_probe = nil
    self.yy_wave_effect = nil

    if self.low_memory_report ~= nil then
        UnityEngine.Application.lowMemory = UnityEngine.Application.lowMemory - self.low_memory_report
        self.low_memory_report = nil
    end

    self.is_report_mem = false
    self.scene_volume_controller = nil
    self.scene_show_volume = nil
    self.scene_day_night = nil
    self.scene_day_night_moment_index = nil
    self.spirit_change_player = nil
end

function Scene:UpdateSceneLodLevel()
    if self.scene_obj_lod then
        self.scene_obj_lod:SetQualityLevel(QualityConfig.QualityLevel)
    end
end

function Scene:UpdateWaterPlanar()
    SceneOptimizeMgr.UpdatePlanarReflectionShow(QualityConfig.QualityLevel)
end

function Scene:UpdateSceneCullingFactor()
    if CgManager.Instance:IsCgIng() then
        -- 主城cg不剔除。暂时写死吧，如果太多了就配表。。。
        if self:GetSceneId() == 1003 then
            SceneOptimizeMgr.SetCullEnable(false, true)
        else
            SceneOptimizeMgr.SetCullEnable(true)
            SceneOptimizeMgr.SetCullDistanceFactor(1.5)
        end
    else
        SceneOptimizeMgr.SetCullEnable(true)
        if QualityConfig.QualityLevel == 0 then
            SceneOptimizeMgr.SetCullDistanceFactor(1)
        elseif QualityConfig.QualityLevel == 1 then
            SceneOptimizeMgr.SetCullDistanceFactor(0.95)
        elseif QualityConfig.QualityLevel == 2 then
            SceneOptimizeMgr.SetCullDistanceFactor(0.85)
        elseif QualityConfig.QualityLevel == 3 then
            SceneOptimizeMgr.SetCullDistanceFactor(0.8)
        end
    end
end

--  0.高配 1.中配 2.低配 3.超低配
function Scene:OnQualityChanged()
    self:UpdateSceneLodLevel()
    self:UpdateSceneCullingFactor()
    self:UpdateWaterPlanar()
    self:UpdateSceneQuality()

    for k,v in pairs(self.obj_list) do
        v:UpdateMaterialQuality()
    end
end

function Scene:DelSceneEffect()
    if nil ~= self.effect_loader_list then
        for k,v in pairs(self.effect_loader_list) do
            v:DeleteMe()
        end
        self.effect_loader_list = nil
    end
end

function Scene:DelSceneSound()
    if nil ~= self.sound_obj_list then
        for k,v in pairs(self.sound_obj_list) do
            ResMgr:Destroy(v)
        end
        self.sound_obj_list = nil
    end
end

function Scene:ClearScene()
    self.scene_config = nil
    self:DelateAllObj(true)

    self.obj_list = {}
    self.obj_group_list = {}
    self.boat_list = {}
    self.fall_objId_list = {}
    self.hide_follow_ui_obj_list = {}

    self:DeleteAllMoveObj()
    self:DelGuideArrow()
    self:DelSceneEffect()
    self:DelSceneSound()
    if FuBenWGData.Instance then
        FuBenWGData.Instance:ClearFbSceneLogicInfo()
    end

    if nil ~= self.scene_logic then
        self.scene_logic:DeleteMe()
        self.scene_logic = nil
    end

    self.hight_fog_obj = nil

    if GatherBar.Instance then
        GatherBar.Instance:Close()
    end

    self.sky_box = nil

    if FightWGData.Instance then
        FightWGData.Instance:ClearAllOtherRoleEffectList()
    end

    ChangeSkin.ClearBoneArray()
end

-- function Scene:OnSceneObjClick(guesture)
--     if MainuiWGCtrl.Instance.view:GetIsJoystick() then return end
--     if not IsNil(MainCamera) then
--         if self.main_role == nil or self.main_role:CantPlayerDoMove() then
--             return
--         end
--         local screen_pos = Vector3(guesture.position.x, guesture.position.y, 0)
--         local ray = MainCamera:ScreenPointToRay(screen_pos)
--         local hits = UnityEngine.Physics.RaycastAll(ray)
--         for i = 0, hits.Length - 1 do
--             local id = hits[i].transform.parent.transform.parent.transform:GetInstanceID()--获取根物体的InstanceId
--             local client_obj = self.instanceid_list[id]
--             if client_obj then
--                 local obj = self:GetObj(client_obj)
--                 if obj and obj:IsRole() and not obj:IsMainRole() and not obj:IsPerformer() then
--                     local bool, str = self:IsEnemy(obj)
--                     if not bool and str ~= nil then
--                         SysMsgWGCtrl.Instance:ErrorRemind(str)
--                         return
--                     end
--                 end
--                 if not obj:IsDefenseTower() and not self:IsCheckOnClick()
--                     and (self.scene_logic and self.scene_logic:CharacterCanSelect(obj)) then
--                     local go = obj.draw_obj:GetPart(SceneObjPart.Main)
--                     go:OnClickListener()
--                 end
--             end
--         end
--     end
-- end
-- function Scene:IsCheckOnClick()
--     if BossWGData.IsBossScene(self:GetSceneType()) or self:GetSceneType() == SceneType.HotSpring then
--         return true
--     end
--     return false
-- end

function Scene:DelateAllObj(is_exclude_main_role)
    local temp_obj_list = {} -- 防止obj在for里移除时非法对obj_list进行操作导致迭代器失效
    for k,v in pairs(self.obj_list) do
        temp_obj_list[k] = v
    end

    for k,v in pairs(temp_obj_list) do
        local is_del = true
        if nil == self.obj_list[k] then
            is_del = false
        end

        if is_exclude_main_role and v == self.main_role then
            is_del = false
        end

        if is_del then
            Trycall(function ()
                self:Fire(ObjectEventType.OBJ_DELETE, v)
            end)
            self.obj_list[k] = nil
            v:DeleteMe()
        end
    end
end

function Scene:SceneObjLoadSceneComplete()
    local temp_obj_list = {}
    for k,v in pairs(self.obj_list) do
        temp_obj_list[k] = v
    end

    for k,v in pairs(temp_obj_list) do
        v:OnLoadSceneComplete()
    end
end

function Scene:DeleteAllMoveObj()
    for k, v in pairs(self.obj_move_info_list) do
        MapMoveObj.Release(v)
    end
    self.obj_move_info_list = {}
end

function Scene:RegisterAllEvents()
    self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnChangeScene, self))
end

function Scene:Update(now_time, elapse_time)
    if nil ~= self.scene_logic then
        self.scene_logic:Update(now_time, elapse_time)
    end

    -- 在objUpdate中可能对obj_list进行移除操作
    local async_npc = {}
    local temp_obj_list = TablePool.Get()
    local list_num = 0
    for k,v in pairs(self.obj_list) do
        if not v:IsDeleted() then
            list_num = list_num + 1
            temp_obj_list[list_num] = v

            -- NPC动作同步 可以优化到需要这个值的时候再去拿 不在Update里
            if v:IsNpc() then
                local sync_index = v:IsSyncAction()
                if sync_index ~= 0 and not async_npc[sync_index] then
                    async_npc[sync_index] = temp_obj_list[list_num]
                    self.sync_action_normalized[sync_index] = async_npc[sync_index]:GetAnimatorNormalized()
                    if self.sync_action_normalized[sync_index] == -1 then
                        async_npc[sync_index] = nil
                    end
                end

            end
        end
    end

    for i = 1, list_num do
        if not temp_obj_list[i]:IsDeleted() then
            temp_obj_list[i]:Update(now_time, elapse_time)
        end
    end

    if now_time >= self.last_check_fall_item_time + PickCache.interval then
        self.last_check_fall_item_time = now_time
        self:PickAllFallItem()
    end

    -- 调用延时函数
    if next(self.delay_handle_funcs) then
        local delay_funcs = self.delay_handle_funcs
        self.delay_handle_funcs = {}
        for _, v in pairs(delay_funcs) do
            v()
        end
    end

    if self:IsSceneLoading() then
        return
    end

    self.scene_obj_lod:Update(now_time, elapse_time)

    local pos_x, pos_y
    if self.main_role then
        pos_x, pos_y = self.main_role:GetLogicPos()
    end

    if pos_x and pos_y and (self.main_role_pos_x ~= pos_x or self.main_role_pos_y ~= pos_y) then
        self.main_role_pos_x, self.main_role_pos_y = pos_x, pos_y
        self:CheckClientObj()
        self:CheckJump()
        FightRobertManager.Instance:CheckRobots()
    end

    if self.is_change_sceneing and self.reset_change_sceneing_time >= now_time then
        self:IsChangeSceneIng(false)
    end

    self:ClearSimulationFall(now_time, elapse_time)
    self:UpdateDropEffect(now_time, elapse_time)
end

--
function Scene:IsChangeSceneIng(value)
    if nil == value then
        return self.is_change_sceneing or self:IsSceneLoading()
    end

    self.is_change_sceneing = value
    if self.is_change_sceneing then
        self.reset_change_sceneing_time = Status.NowTime + self.reset_total_time
    end
end

function Scene:IsSceneLoading()
    return self.scene_loading:IsSceneLoading()
end

function Scene:IsEnterScene()
    return nil ~= self.is_enter_scene
end

function Scene:ResetIsEnterScene()
    self.is_enter_scene = nil
end

function Scene:OpenSceneLoading(scene_id)
    if scene_id then
        self.scene_loading:SetShowLoading(scene_id)
    end
    self.scene_loading:Open()
end

function Scene:IsFirstEnterScene()
    return self.is_first_enter_scene
end

function Scene:IsSceneLoadingStateEnter()
    return self.is_scene_loading_state_enter
end

function Scene:OnChangeScene(scene_id)
    local scene_config = ConfigManager.Instance:GetSceneConfig(scene_id)
    if nil == scene_config then
        print_error("scene_config not find, scene_id:" .. scene_id)
        return
    end

    if not self.old_scene_id then
        self.old_scene_id = -1
    else
        self.old_scene_id = self:GetSceneId()
    end

    TaskWGCtrl.Instance:CancleWait()
    ViewManager.Instance:Close(GuideModuleName.TaskDialog)
    self.is_first_enter_scene = nil == self.scene_config
    self.old_scene_type = nil ~= self.scene_config and self.scene_config.scene_type or -1
    local cfg_scene_type = scene_config.scene_type

    self.is_scene_loading_state_enter = true
    if self.scene_logic ~= nil then
        if not self:IsSceneLoading() then
            if CgManager.Instance:IsCgIng() then
                CgManager.Instance:OnPlayEnd()
            end
            CameraShake.CancelShake()
        end

        Trycall(function ()
            self.scene_logic:Out(self.old_scene_type, cfg_scene_type)
            self:CheckAreaCamera()
        end)
    end

    local is_same_asset = false
    if self.scene_config ~= nil and
        (
        (self.old_scene_type == cfg_scene_type and self.old_scene_type ~= 0)
        or self.old_scene_type == SceneType.FBCT_NEWPLAYERFB
        or cfg_scene_type == SceneType.FBCT_NEWPLAYERFB
        or cfg_scene_type == SceneType.TIANSHEN_JIANLIN
        or cfg_scene_type == SceneType.WORLD_TREASURE_JIANLIN
        or cfg_scene_type == SceneType.XINGTIANLAIXI_FB
        or cfg_scene_type == SceneType.MOWU_JIANLIN
        or cfg_scene_type == SceneType.Kf_PvP_Prepare
        or cfg_scene_type == SceneType.Kf_PVP
        or cfg_scene_type == SceneType.TianShen3v3Prepare
        or cfg_scene_type == SceneType.TianShen3v3
        or cfg_scene_type == SceneType.WorldsNO1Prepare
        or cfg_scene_type == SceneType.WorldsNO1
        or (cfg_scene_type == SceneType.KF_DUCK_RACE and self.old_scene_type == SceneType.KF_HotSpring)
        or OperationTaskChainWGData.Instance:IsTaskChainScene() or KFOneVOneWGData.Instance:IsOneVOneScene()
        or (self.old_scene_type == cfg_scene_type and self.old_scene_type == SceneType.Common and self.old_scene_id ~= 0)
        )
        and self.scene_config.bundle_name == scene_config.bundle_name and
        self.scene_config.asset_name == scene_config.asset_name then
        is_same_asset = true
        self:IsChangeSceneIng(false)
    end

    local is_pre_fuben_scene = false
    if ScenePreload.CheckAndOperaPreScene(scene_config.bundle_name, scene_config.asset_name) then
        is_pre_fuben_scene = true
        self:IsChangeSceneIng(false)
    end

    -- 跨服中转场景，不属于预加载部分时, 清除预加载的副本场景信息
    -- 不然快速切到主场景和跨服场景资源相同时，因为逻辑执行问题，导致主场景资源被销毁，而跨服场景资源因为信息状态未更新，导致未加载
    if scene_id == 4600 and not is_pre_fuben_scene then
        self:ClearPreloadedFuBenSceneInfo()
    end

    Trycall(function ()
        self:ClearScene()
    end)

    self.scene_config = scene_config
    GameMapHelper.SetOrigin(self.scene_config.origin_x, self.scene_config.origin_y)
    MoveableObject.SetLogicMap(self.scene_config.origin_x, self.scene_config.origin_y, Config.SCENE_TILE_WIDTH, Config.SCENE_TILE_HEIGHT)

    self.scene_logic = SceneLogic.Create(self.scene_config.scene_type)

    AStarFindWay:Init(self.scene_config.mask, self.scene_config.width, self.scene_config.height)
    RoadFindWay.Instance:Init(self.scene_config.scene_way_points)

    --根据场景显示配置 判断是否需要下坐骑
    local fb_cfg_type = self:GetCurFbSceneCfg()
    if fb_cfg_type and fb_cfg_type.pb_mount == 1 then
        -- 水波纹副本，不等协议返回，强制下坐骑
        if is_same_asset or is_pre_fuben_scene then
            local main_role = Scene.Instance:GetMainRole()
            if main_role then
                main_role:SetAttr("mount_appeid", 0)
            end
        end

        MountWGCtrl.Instance:SendMountGoonReq(0)
    end

    if not is_same_asset and not is_pre_fuben_scene then
        self:StartLoadScene(scene_id)
    else
        if not self.scene_loading:IsOpen() then
            if is_same_asset or is_pre_fuben_scene then
                TaskWGCtrl.Instance:IsFly(false)
                self:LoadSameScene(scene_id, self.scene_config.scene_type, false, true)
            end

            if is_pre_fuben_scene then
                ViewManager.Instance:Close(GuideModuleName.FuBenPanel)
                ScenePreload.CheckAndOperaPreScene(scene_config.bundle_name, scene_config.asset_name, true)
            end
        end
    end

    if nil == self.is_enter_scene then
        self.is_enter_scene = true
        RoleWGCtrl.Instance:SendReqAllInfo()
        -- 初始化收费聊天(方法里已经做了判断是否开启收费语音的)
        AudioService.Instance:InitFeesAudio()
    else
        self:CreateMainRole()
    end

    -- 屏蔽在线奖励
    -- MainUIWGCtrl.Instance.view:Flush("on_line")

    -- 进入场景30秒后才开始检查内存泄漏
    if self:IsFirstEnterScene() then
        if G_IsDeveloper or GLOBAL_DELETE_ME_CHECK_SWITCH then
            GlobalTimerQuest:AddDelayTimer(function ()
                CheckMem:Start()
            end, 30)
        end
    end
end

function Scene:LoadSameScene(scene_id, scene_type, is_simulation, is_ignore_load)
    self.start_loading_time = Status.NowTime

    self.act_scene_id = scene_id
    if not IS_ON_CROSSSERVER or KFOneVOneWGData.Instance:IsOneVOneScene() or scene_type == SceneType.Kf_PVP or scene_type == SceneType.Kf_PvP_Prepare 
        or scene_type == SceneType.WorldsNO1 or scene_type == SceneType.WorldsNO1Prepare or scene_type == SceneType.CROSS_LAND_WAR then
        self.scene_loading:ShowWaterEffect()
    end

    self:OnLoadSceneMainComplete(scene_id, nil, is_simulation, is_ignore_load)
    self:OnLoadEnd(scene_id,true)
    GlobalTimerQuest:AddDelayTimer(function()
        self.scene_loading:ResetPostEffects()
     end, 1.5)
end

function Scene:ShowWaterEffect()
    if not IS_ON_CROSSSERVER or KFOneVOneWGData.Instance:IsOneVOneScene() then
        self.scene_loading:ShowWaterEffect()
    end
end

-- 打开加载条加载场景
function Scene:StartLoadScene(scene_id)
    -- 加载场景时关闭限速
    AutoControlDownload = false

    if not self:IsSceneLoading() and (self.act_scene_id == scene_id) then
        self:OnLoadSceneMainComplete(scene_id)
        self:OnLoadSceneDetailComplete(scene_id)
        TaskWGCtrl.Instance:ResetJumpState()
        return
    end
    if self.act_scene_id ~= scene_id then
        TaskWGCtrl.Instance:IsFly(true)
    end

    if NeedCheckBundle then
        NeedCheckBundle = false
        local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
        local level = main_role_vo.level or 0
        -- 只下载120级之前的资源
        level = math.min(level, 120)
        self.scene_loading:StartCheckBundles(level, scene_id)
    else
        self.scene_loading:StartCheckBundles(0, scene_id)
    end

    self.scene_loading:SetShowLoading(scene_id)
    AssetBundleMgr:ReqHighLoad()
    self.scene_loading:SetStartLoadingCallback(BindTool.Bind(self.OnLoadStart, self))
    self.scene_loading:Start(scene_id, BindTool.Bind(self.OnMainLoadEnd, self), BindTool.Bind(self.OnLoadEnd, self))
end

-- 显示加载界面（形式上）
function Scene:ShowLoadingView()
    self.scene_loading:PlayLoadingAni()
end

-- 加载开始
function Scene:OnLoadStart(scene_id)
    print("[Scene] OnLoadStart ", scene_id)
    self.start_loading_time = Status.NowTime
    -- ReportManager:Step(Report.STEP_CHANGE_SCENE_BEGIN, scene_id)

    ViewManager.Instance:Close(GuideModuleName.Login)
    if LoginWGCtrl.Instance then
        LoginWGCtrl.Instance:ClearScenes()
    end

    -- AudioManager.PlayAndForget("audios/sfxs/npcvoice/shared", "mute_voice") -- 播放npc对话静音
    AudioManager.PlayAndForget("audios/sfxs/uis", "MuteUIVoice") -- 播放ui静音
    self.freedownload:Stop()
end

function Scene:OnMainLoadEnd(scene_id)
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.sceneChange, self.act_scene_id, scene_id)
    self.act_scene_id = scene_id
    self:IsChangeSceneIng(false)
    self:OnMainLoadEndToCamera(scene_id)
    
    --self:CreateSceneVolume()
    self:OnLoadSceneMainComplete(scene_id)
    self:InitSkillRangTool()
    self:InitSceneVolume()

    --[[if YunbiaoWGCtrl.Instance:GetIsClickHuSong() then
        GlobalEventSystem:Fire(ObjectEventType.FLY_TO_HUSONG_NPC)
    end--]]
end

-- 加载结束
function Scene:OnLoadEnd(scene_id,is_same)
    local loading_time = Status.NowTime - self.start_loading_time
    print("[Scene] OnLoadEnd ", scene_id, loading_time)
    AssetBundleMgr:ReqLowLoad()

    -- 下载package_0(新手资源)
    if GAME_ASSETBUNDLE then
        SubPackage.Instance:StartDownloadPackage(0)
        -- 开启限速
        SubPackage.Instance:LimitPackageSpeed(0, true)
    end

    self:OnLoadEndToCamera(scene_id, is_same)

    self.act_scene_id = scene_id
    self:OnLoadSceneDetailComplete(scene_id)
    self.freedownload:Start()
    TaskWGCtrl.Instance:PlayYouliCg()
end

function Scene:OnLoadSceneMainComplete(scene_id, scene_type, is_simulation, is_ignore_load)
    -- 进入场景之后开启限速
    AutoControlDownload = true

    if IsNil(MainCamera) then
        self:OnMainLoadEnd(scene_id)
        return
    end

    self:OnLoadSceneMainCompleteToCamera(scene_id, scene_type, is_simulation)

    Trycall(function ( ... )
        self:SceneObjLoadSceneComplete()
    end)

    -- 特殊操作：隐藏场景obj
    local hide_group = FuBenWGData.Instance:GetSceneNeedHideObjGroup(scene_id)
    if hide_group >= 0 then
        self:TrySetSceneStaticObjActive(hide_group, false)
    -- 复原
    else
        if self.scene_static_obj_hide_cache then
            hide_group = self.scene_static_obj_hide_cache[ScenePreload.main_scene_asset_name]
            if hide_group then
                self:TrySetSceneStaticObjActive(hide_group, true)
            end

            self.scene_static_obj_hide_cache = nil
        end
    end

    -- 创建场景特效
    for k, v in pairs(self.effect_list) do
        if not IsNil(v) then
            ResMgr:Destroy(v)
            v = nil
        end
    end

    -- 创建场景特效
    if nil ~= self.scene_config.effects then
        self.effect_loader_list = {}
        for k, v in pairs(self.scene_config.effects) do
            self.effect_loader_list[k] = AllocAsyncLoader(self, "scene_effect" .. k)
            self.effect_loader_list[k]:SetIsUseObjPool(true)
            self.effect_loader_list[k]:SetIsInQueueLoad(true)
            self.effect_loader_list[k]:SetParent(G_SceneObjLayer)
            self.effect_loader_list[k]:Load(v.bundle, v.asset, function(prefab)
                if IsNil(prefab) then
                    print_error("Scene effects lost!", self.scene_config.name, self.scene_config.id, v.bundle, v.asset)
                    return
                end

                local moveable_obj = prefab:GetOrAddComponent(MoveableObjectT)
                if moveable_obj then
                    local wx, wy = GameMapHelper.LogicToWorld(v.x, v.y)
                    moveable_obj:SetPosition(Vector3(wx, 0, wy))
                    moveable_obj:SetOffset(Vector3(v.offset[1], v.offset[2], v.offset[3]))
                end

                prefab.transform.localEulerAngles = Vector3(v.rotation[1], v.rotation[2], v.rotation[3])
                if v.scale then
                    prefab.transform.localScale = Vector3(v.scale[1], v.scale[2], v.scale[3])
                end
            end)
        end
    end

    -- 创建音效 目前使用背景音量
    if nil ~= self.scene_config.sounds then
        self.sound_obj_list = {}
        local bundle, asset, loader
        for k, v in pairs(self.scene_config.sounds) do
            bundle, asset = ResPath.GetOtherVoiceRes(v.clip)
            loader = AllocResAsyncLoader(self, "scene_sound" .. k)

            if loader then
                local extension = v.extension or ".wav"
                loader:Load(bundle, asset .. extension, AudioClipT,
                function(obj)
                    if IsNil(obj) then
                        return
                    end
                    local audio_mixer_group = AudioService.Instance:GetAudioMixerGroup("Master/Music")

                    self.sound_obj_list[k] = GameObject.New("Sound" .. asset)
                    self.sound_obj_list[k].transform:SetParent(G_SceneObjLayer, false)
                    local moveable_obj = self.sound_obj_list[k]:GetOrAddComponent(MoveableObjectT)
                    if moveable_obj then
                        local wx, wy = GameMapHelper.LogicToWorld(v.x, v.y)
                        moveable_obj:SetPosition(Vector3(wx, 0, wy))
                    end
                    local audio_source = self.sound_obj_list[k]:GetOrAddComponent(AudioSourceT)
                    if audio_source then
                        audio_source.clip = obj
                        if audio_mixer_group then
                            audio_source.outputAudioMixerGroup = audio_mixer_group
                        end
                        audio_source.mute = v.mute == 1
                        audio_source.bypassEffects = v.bypassE == 1
                        audio_source.bypassListenerEffects = v.bypassLE == 1
                        audio_source.bypassReverbZones = v.bypassRZ == 1
                        audio_source.playOnAwake = v.playOnAwake == 1
                        audio_source.loop = v.loop == 1
                        audio_source.priority = v.priority
                        audio_source.volume = v.volume
                        audio_source.pitch = v.pitch
                        audio_source.panStereo = v.panStereo
                        audio_source.spatialBlend = v.spatialBlend
                        audio_source.reverbZoneMix = v.reverbZoneMix
                        audio_source.dopplerLevel = v.dopplerLevel
                        audio_source.spread = v.spread
                        audio_source.minDistance = v.minDistance
                        audio_source.maxDistance = v.maxDistance
                        if v.rolloffMode == 1 then
                            audio_source.rolloffMode = UnityEngine.AudioRolloffMode.Linear
                        elseif v.rolloffMode == 2 then
                            audio_source.rolloffMode = UnityEngine.AudioRolloffMode.Custom
                        end
                        audio_source:Play()
                    end
                end)
            end
        end
    end

    -- 创建npc和传送门
    self:CreateNpcList()
    -- self:CreatMingRenList()
    self:CreateDoorList()
    self:UpdateSceneCullingFactor()
    self:CreateCityOnwerStatue()
    self:CreateLaiXiDoorList()
    self:UpdateSceneQuality()
    self:SetHeroLightState(true)
    self:UpdateWaterPlanar()
    self:SetSceneDayNight()

    self.is_in_door = self:GetIsInDoor()

    local main_node = GameObject.Find("Main")
    if main_node and main_node.gameObject then
        HeroLight = main_node.gameObject.transform:Find("Hero light")
    end

    if CharacterShadows.CharacterShadowManager.Instance ~= nil then
        CharacterShadows.CharacterShadowManager.Instance.Light = HeroLight
    end

    local new_scene_type = scene_type or self.scene_config.scene_type
    local old_scene_type = scene_type or self.old_scene_type

    Trycall(function ( ... )
        self.scene_logic:Enter(old_scene_type, new_scene_type, is_ignore_load)
    end)

    self.is_scene_loading_state_enter = false
    ViewManager.Instance:OpenWaitSceneLoadedView()
    self:Fire(SceneEventType.SCENE_LOADING_STATE_QUIT, self.old_scene_type, new_scene_type)
    -- 策划要求屏蔽地板点击逻辑
    -- if nil ~= ClickManager.Instance then
    --     if nil ~= self.clickHandle and nil ~= ClickManager.Instance then
    --         ClickManager.Instance:UnlistenClickGround(self.clickHandle)
    --         self.clickHandle = nil
    --     end

    --     self.clickHandle = ClickManager.Instance:ListenClickGround(function(hit)
    --         -- 当前场景无法移动
    --         if self.main_role:CantPlayerDoMove(true) then
    --             return
    --         end

    --         if MainuiWGCtrl.Instance and MainuiWGCtrl.Instance:GetView() and MainuiWGCtrl.Instance:GetView():GetIsJoystick() then
    --             return
    --         end

    --         if CgManager ~= nil and CgManager.Instance ~= nil then
    --             if CgManager.Instance:IsCgIng() or CgManager.Instance:IsLimit() then
    --                 return
    --             end
    --         end

    --         if IS_DEBUG_BUILD then
    --             if TaskGuide.Instance:CanAutoAllTask() then
    --                 print_log("内网日志：点击地面导致自动任务取消")
    --             end
    --         end

    --         TaskGuide.Instance:CanAutoAllTask(false)
    --         local logic = Scene.Instance:GetSceneLogic()
    --         if logic and not logic:CanCancleAutoGuaji() then
    --             self:Fire(MainUIEventType.SHOW_OR_HIDE_SHRINK_BUTTON, false)
    --             TipWGCtrl.Instance:ShowSystemMsg(Language.Rune.CanNotCancleGuaji)
    --             return
    --         end

    --         -- 玩家不能点击地面
    --         if logic and not logic:IsCanClickGround() then
    --             return
    --         end

    --         self:Fire(MainUIEventType.PORTRAIT_TOGGLE_CHANGE, false, true)


    --         -- 国战项目屏蔽点击地面取消挂机提示功能
    --         -- if (GuajiCache.guaji_type ~= GuajiType.None or MoveCache.is_valid or AtkCache.is_valid)
    --         -- and (self.last_click_ground_time == nil or Status.NowTime - self.last_click_ground_time > 5) then
    --         -- self.last_click_ground_time = Status.NowTime
    --         -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ClickGoundAgainStopAuto)
    --         -- return
    --         -- end
    --         -- self.last_click_ground_time = Status.NowTime

    --         YunbiaoWGCtrl.Instance:SetIsClickHuSongOnBizuo(false)
    --         BootyBayWGCtrl.Instance:SetAutoWaBaoFlag(false)
    --         GuildBattleRankedWGCtrl.Instance:ClearWorshipData()
    --         -- 点击到地面，移动
    --         self:Fire(MainUIEventType.SHOW_OR_HIDE_SHRINK_BUTTON, false)
    --         self:Fire(LayerEventType.SCENE_CLICK_FLOOR)
    --         MainuiWGCtrl.Instance:SetToggleMenuIsOn(false)

    --         local x, y = GameMapHelper.WorldToLogic(hit.point.x, hit.point.z)
    --         --幫派禁地自己实现了点击地板行走
    --         if new_scene_type ~= SceneType.JIUSHEDAO_FB then
    --             self.main_role:DoMoveByClick(x, y, 0, function ()--进入BOSS地图注释
    --                 GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)
    --                 GuajiWGCtrl.Instance:ClearAllOperate()
    --             end)
    --         end
    --         -- GuajiWGCtrl.Instance:StopGuaji()

    --         -- local asset_name = AStarFindWay:IsBlock(x, y) and "Movement_Unwalkable" or "dianjidimian"
    --         if not AStarFindWay:IsBlock(x, y) and MainCamera then
    --             local bundle, asset_name = ResPath.GetEffectUi("UI_dianji")
    --             EffectManager.Instance:PlayControlEffect(self, bundle, asset_name, Vector3(hit.point.x, hit.point.y + 0.25, hit.point.z), nil, nil, 1.5)
    --         end
    --     end)
    -- else
    --     print_log("This scene does not has ClickManager.")
    -- end
end

function Scene:OnLoadSceneDetailComplete(scene_id)
    self:Fire(SceneEventType.SCENE_ALL_LOAD_COMPLETE)
end

function Scene:ClearAllOperate(click_obj)
    -- MammonBlessWGCtrl.Instance:ClearOperateCache()-- 停止自动采集宝箱
    -- FightWGCtrl.Instance:ClearOperateCache()

    --添加答题跟随榜首标示改变
    -- if self.scene_logic:GetSceneType() == SceneType.KF_AnswerFb and self.scene_logic.bool_is_Follow == true then
    -- self.scene_logic.bool_is_Follow = false
    -- end
end
----------------------------------------------------
-- Get begin
----------------------------------------------------
function Scene:GetMainRole()
    return self.main_role
end

function Scene:GetSceneId()
    return self.scene_config and self.scene_config.id or 0
end

function Scene:GetOldSceneId()
    return self.old_scene_id
end

function Scene:GetSceneForbidPk()
    if nil == self.scene_config then
        return false
    end
    return self.scene_config.is_forbid_pk and self.scene_config.is_forbid_pk == 1
end

function Scene:GetSceneBroadcast()
    if nil == self.scene_config then
        return false
    end
    return self.scene_config.scene_broadcast and self.scene_config.scene_broadcast == 1
end

function Scene:GetSceneTownPos()
    if nil == self.scene_config then
        return 0, 0
    end
    return self.scene_config.scenex or 0, self.scene_config.sceney or 0
end

function Scene:GetSceneName()
    return self.scene_config and self.scene_config.name or ""
end

function Scene:GetSceneLogic()
    return self.scene_logic
end

function Scene:GetSceneType()
    return self.scene_config and self.scene_config.scene_type or 0
end

function Scene:GetSceneConfig()
    return self.scene_config
end

function Scene:GetSceneDoorCfgById(door_id)
    for k,v in pairs(self.scene_config.doors) do
        if v.id == door_id then
            return v
        end
    end
    return nil
end

function Scene:GetObj(obj_id)
    return self.obj_list[obj_id]
end

function Scene:GetMonstObjByMonstID(monster_id)
    for k, v in pairs(self.obj_list) do
        if v.obj_type ~= nil and v.obj_type == SceneObjType.Monster then
            if v:GetMonsterId() == monster_id then
                return v
            end
        end
    end
end

function Scene:ChangeShieldBeastObjStatus()
    for k, v in pairs(self.obj_list) do
        if v and v:IsRole() then
            v:ChangeBeast()
        end
    end
end

function Scene:GetObjByTypeAndKey(obj_type, obj_key)
    if nil ~= self.obj_group_list[obj_type] then
        return self.obj_group_list[obj_type][obj_key]
    end
    return nil
end

function Scene:GetNpcByNpcId(npc_id)
    return self:GetObjByTypeAndKey(SceneObjType.Npc, npc_id) or self:GetFakeNpcByNpcId(npc_id)
end

function Scene:GetFakeNpcByNpcId(npc_id)
    return self:GetObjByTypeAndKey(SceneObjType.FakeNpc, npc_id)
end

function Scene:GetGatherByGatherId(gather_id)
    for k, v in pairs(self:GetObjListByType(SceneObjType.GatherObj)) do
        if v:GetGatherId() == gather_id then
            return v
        end
    end

    return nil
end

function Scene:GetGatherByGatherIdAndPosInfo(gather_id, x, y)
    for k, v in pairs(self:GetObjListByType(SceneObjType.GatherObj)) do
        local pos_x, pos_y = v:GetLogicPos()
        if v:GetGatherId() == gather_id and math.abs(pos_x - x) <= 1 and math.abs(pos_y - y) <= 1 then
            return v
        end
    end
    return nil
end

function Scene:GetObjList()
    return self.obj_list
end

local empty_table = {}
function Scene:GetObjListByType(obj_type)
    return self.obj_group_list[obj_type] or empty_table
end

function Scene:GetRoleList()
    return self.obj_group_list[SceneObjType.Role] or empty_table
end

function Scene:GetMingRenList()
    return self.obj_group_list[SceneObjType.MingRen] or empty_table
end

function Scene:GetMonsterList()
    return self.obj_group_list[SceneObjType.Monster] or empty_table
end

function Scene:GetNpcList()
    return self.obj_group_list[SceneObjType.Npc] or empty_table
end

function Scene:GetJumpPointList()
    return self.obj_group_list[SceneObjType.JumpPoint] or empty_table
end

function Scene:GetFakeNpcList()
    return self.obj_group_list[SceneObjType.FakeNpc] or empty_table
end

function Scene:GetSpiritList()
    return self.obj_group_list[SceneObjType.SpriteObj] or empty_table
end

--添加获取方法
function Scene:GetGatherList()
    return self.obj_group_list[SceneObjType.GatherObj] or empty_table
end

-- 根据采集物id获取采集物配置
function Scene:GetGatherCfgByGatherId(gather_id)
    return ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[gather_id]
end

function Scene:GetRoleByObjId(obj_id)
    local obj = self.obj_list[obj_id]
    if nil ~= obj and obj:IsRole() then
        return obj
    end
    return nil
end

function Scene:GetRoleByRoleId(role_id)
    -- body
    local role_list = self:GetRoleList()
    for k,v in pairs(role_list) do
        if v:GetVo() and v:GetVo().role_id == role_id then
            return v
        end
    end
    return nil
end

function Scene:GetRoleByOrginalId(origin_uid)
    local role_list = self:GetRoleList()
    for k,v in pairs(role_list) do
        if v:GetVo() and v:GetVo().origin_uid == origin_uid then
            return v
        end
    end
    return nil
end

function Scene:GetRoleByUUID(uuid)
    local role_list = self:GetRoleList()
    for k,v in pairs(role_list) do
        if v:GetVo() and v:GetVo().uuid == uuid then
            return v
        end
    end
    return nil
end

function Scene:GetRoleByUUIDStr(uuid_str)
    local plat_type, uid = LLStrToInt(uuid_str)
    local role_list = self:GetRoleList()
    for k,v in pairs(role_list) do
        if v:GetVo() and v:GetVo().role_id == uid and v:GetVo().merge_plat_type == plat_type then
            return v
        end
    end
    return nil
end

function Scene:GetRoleByOrginalUUIDStr(uuid_str)
    local plat_type, uid = LLStrToInt(uuid_str)
    local role_list = self:GetRoleList()
    for k,v in pairs(role_list) do
        if v:GetVo() and v:GetVo().origin_uid == uid and v:GetVo().merge_plat_type == plat_type then
            return v
        end
    end
    return nil
end

function Scene:GetObjectByObjId(obj_id)
    return self.obj_list[obj_id]
end

function Scene:GetObjByUId(uid)
    for k, v in pairs(self.obj_list) do
        if v.vo.role_id == uid then
            return v
        end
    end
end

function Scene:GetObjByOriId(ori_id)
    for k, v in pairs(self.obj_list) do
        if v.vo.origin_uid == ori_id then
            return v
        end
    end
end


--之前通过monster_id,但是现在boss召唤可能召唤出相同的monster_id ,所以建议使用GetObj接口
function Scene:GetMonsterByMonsterId(monster_id)
    local monster_list = self:GetMonsterList()
    for k, v in pairs(monster_list) do
        if v.vo.monster_id == monster_id then
            return v
        end
    end
end

function Scene:GetObjMoveInfoList()
    return self.obj_move_info_list
end

function Scene:GetMapRoleInfoList()
    return self.map_role_info_list
end

function Scene:GetSceneAssetName()
    return self.scene_config.asset_name
end

----------------------------------------------------
-- Get end
----------------------------------------------------

----------------------------------------------------
-- Create begin
----------------------------------------------------
function Scene:CreateMainRole()
    -- 快登未创建 LoginWGCtrl
    if LoginWGCtrl.Instance then
        LoginWGCtrl.Instance:CheckMainRoleInSpecialStatus()
    end

    self:DeleteMainRole()
    local vo = GameVoManager.Instance:GetMainRoleVo()
    if nil == vo then
        print_log("Scene:CreateMainRole vo nil")
        return nil
    end

    self.main_role = self:CreateObj(vo, SceneObjType.MainRole)
	-- 修复玩家处于特殊行为，角色被再创建问题
    self.main_role:FixBeforeCreateMainRoleSpecialAction()
    -- RobertManager.Instance:OnMainRoleCreate()

    GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_CREATE)

    return self.main_role
end

-- 其他地方禁止调用
function Scene:DeleteMainRole()
    if self.main_role then
        self:OnMainRoleDeteleToCamera()

        for k, v in pairs(self.obj_list) do
            if v:IsMainRole() then
                self.obj_list[k] = nil
                break
            end
        end

        self.main_role:DeleteMe()
        self.main_role = nil
    end
end

function Scene:CreateRole(vo)
    return self:CreateObj(vo, SceneObjType.Role)
end

function Scene:CreateMonster(vo)
    return self:CreateObj(vo, SceneObjType.Monster)
end

function Scene:CreatePet(vo)
    return self:CreateObj(vo, SceneObjType.Pet)
end

function Scene:CreateBeast(vo)
    return self:CreateObj(vo, SceneObjType.BeastObj)
end

function Scene:CreateTaskCallInfo(vo)
    return self:CreateObj(vo, SceneObjType.TaskCallInfoObj)
end

function Scene:CreateWuHun(vo)
    return self:CreateObj(vo, SceneObjType.WuHunZhenShen)
end

function Scene:CreateShuangShengTianShen(vo)
    return self:CreateObj(vo, SceneObjType.ShuangShengTianShen)
end

function Scene:CreateDoor(vo)
    self:CreateObj(vo, SceneObjType.Door)
end

function Scene:CreateLaixiDoor(vo)
    self:CreateObj(vo, SceneObjType.QiongQiLaiXi)
end

function Scene:CreateJumpPoint(vo)
    self:CreateObj(vo, SceneObjType.JumpPoint)
end

function Scene:CreateEffectObj(vo)
    return self:CreateObj(vo, SceneObjType.EffectObj)
end

function Scene:CreateFallItem(vo)
    self:CreateObj(vo, SceneObjType.FallItem)
    self:PlayDropEffect(vo)
end

function Scene:PlayDropEffect(vo)
    local key = "key" .. vo.monster_id
    local monster = Scene.Instance:GetObj(vo.obj_id) --之前通过monster_id,但是现在boss召唤可能召唤出相同的monster_id
    local monster_1 = Scene.Instance:GetMonsterByMonsterId(vo.monster_id)
    if self.drop_effect_list[key] or not monster or not monster_1 then
        return
    end

    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[vo.monster_id]

    if monster_cfg.type ~= MONSTER_TYPE.BOSS then
        return
    end

    local effect_loader = AllocAsyncLoader(self, key)
    if effect_loader then
        effect_loader:SetParent(SceneObjLayer.transform)
        effect_loader:SetIsUseObjPool(true)
        effect_loader:SetLocalPosition(monster:GetRoot().transform.position)
    
        local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_diaoluo")
        effect_loader:Load(bundle_name, asset_name)
        self.drop_effect_list[key] = {eff_loader = effect_loader, time = Status.NowTime + 7}
    end

    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.DropItem1001))
end

function Scene:UpdateDropEffect(now_time, elapse_time)
    -- body
    for k,v in pairs(self.drop_effect_list) do
        if v.time <= now_time then
            v.eff_loader:Destroy()
            self.drop_effect_list[k] = nil
        end
    end
end

function Scene:ClearAllDropEffect()
    -- body
    for k,v in pairs(self.drop_effect_list) do
        v.eff_loader:Destroy()
    end
    self.drop_effect_list = nil
end

function Scene:CreateZhuaGuiNpc(vo)
    self:CreateObj(vo, SceneObjType.EventObj)
end

function Scene:CreateGatherObj(vo)
    local bool = TaskWGData.Instance:IsGatherVisible(vo.gather_id)
    if not bool then return end

    return self:CreateObj(vo, SceneObjType.GatherObj)
end

function Scene:CreateMarryObj(vo)
    return self:CreateObj(vo, SceneObjType.MarryObj)
end

function Scene:CreateNpc(vo)
    self:CreateObj(vo, SceneObjType.Npc)
end

function Scene:CreateFakeNpc(vo)
    self:CreateObj(vo, SceneObjType.FakeNpc)
end

function Scene:CreateTruckObj(vo)
    return self:CreateObj(vo, SceneObjType.TruckObj)
end

function Scene:CreateSpiritObj(vo)
    return self:CreateObj(vo, SceneObjType.SpriteObj)
end

function Scene:CreateGuardObj(vo)
    return self:CreateObj(vo,SceneObjType.GuardObj)
end

function Scene:CreateGoddessObj(vo)
    return self:CreateObj(vo, SceneObjType.GoddessObj)
end

function Scene:CreateSoulBoyObj(vo)
    return self:CreateObj(vo, SceneObjType.SoulBoyObj)
end

function Scene:CreateFollowMingJiang(vo)
    return self:CreateObj(vo, SceneObjType.FollowMingJiang)
end

function Scene:CreateXiaoTianQuan(vo)
    return self:CreateObj(vo, SceneObjType.XiaoTianQuan)
end

function Scene:CreateBeautyObj(vo)
    return self:CreateObj(vo, SceneObjType.BeautyObj)
end

function Scene:CreateFollowNpc(vo)
    return self:CreateObj(vo, SceneObjType.FollowNpc)
end

function Scene:CreateMingJiangObj(vo)
    return self:CreateObj(vo, SceneObjType.MingJiangObj)
end

-- function Scene:CreateDogObj(vo)
--     return self:CreateObj(vo, SceneObjType.DogObj)
-- end

function Scene:CreateTriggerObj(vo)
    return self:CreateObj(vo, SceneObjType.Trigger)
end

-- 根据角色创建Truck
function Scene:CreateTruckObjByRole(role)
    local truck_obj = nil
    local role_vo = role:GetVo()
    if role_vo.husong_color > 0 and role_vo.husong_taskid > 0 then
        local truck_vo = GameVoManager.Instance:CreateVo(TruckObjVo)
        truck_vo.pos_x, truck_vo.pos_y = role:GetLogicPos()
        truck_vo.pos_x = truck_vo.pos_x + 2
        truck_vo.truck_color = role_vo.husong_color
        truck_vo.owner_role_id = role_vo.role_id
        truck_vo.owner_obj_id = role_vo.obj_id
        truck_vo.hp = 100
        truck_vo.move_speed = role:GetVo().move_speed
        truck_vo.owner_obj = role
        truck_obj = self:CreateTruckObj(truck_vo)
    end
    return truck_obj
end

-- -- 根据角色创建Spirit
-- function Scene:CreateSpiritObjByRole(role)
--     local spirit_obj = nil
--     local role_vo = role:GetVo()
--     if role_vo.used_sprite_id and role_vo.used_sprite_id > 0 then
--         local spirit_vo = GameVoManager.Instance:CreateVo(SpriteObjVo)
--         spirit_vo.pos_x, spirit_vo.pos_y = role:GetLogicPos()
--         spirit_vo.pos_x = spirit_vo.pos_x + 5
--         spirit_vo.name = role_vo.name.."精灵"

--         spirit_vo.owner_role_id = role_vo.role_id
--         spirit_vo.owner_obj_id = role_vo.obj_id
--         spirit_vo.used_sprite_id = role_vo.used_sprite_id
--         spirit_vo.move_speed = role:GetVo().move_speed
--         spirit_vo.spirit_name = role_vo.sprite_name
--         spirit_vo.hp = 100
--         spirit_vo.owner_obj = role
--         spirit_obj = self:CreateSpiritObj(spirit_vo)
--     end
--     return spirit_obj
-- end

function Scene:CreateGoddessObjByRole(role)
    local soulboy_obj = nil
    local role_vo = role:GetVo()
    local goddess_vo = GameVoManager.Instance:CreateVo(GoddessObjVo)
    goddess_vo.pos_x, goddess_vo.pos_y = role:GetLogicPos()
    goddess_vo.pos_x = goddess_vo.pos_x + 2
    goddess_vo.owner_role_id = role_vo.role_id
    goddess_vo.owner_obj_id = role_vo.obj_id
    goddess_vo.hp = 100
    goddess_vo.move_speed = role_vo.move_speed
    goddess_vo.use_xiannv_id = role_vo.use_xiannv_id
    goddess_vo.goddess_wing_id = role_vo.appearance.shenyi_used_imageid
    goddess_vo.goddess_shen_gong_id = role_vo.appearance.shengong_used_imageid
    goddess_vo.xiannv_huanhua_id = role_vo.xiannv_huanhua_id
    local xiannv_name = role_vo.xiannv_name
    goddess_vo.name = xiannv_name
    goddess_vo.owner_obj = role
    soulboy_obj = self:CreateGoddessObj(goddess_vo)
    return soulboy_obj
end

function Scene:CreateSoulBoyObjByRole(role)
    local soulboy_obj = nil
    local role_vo = role:GetVo()
    local sb_vo = GameVoManager.Instance:CreateVo(SoulBoyObjVo)
    sb_vo.pos_x, sb_vo.pos_y = role:GetLogicPos()
    sb_vo.pos_x = sb_vo.pos_x + 2
    sb_vo.owner_role_id = role_vo.role_id
    sb_vo.owner_obj_id = role_vo.obj_id
    sb_vo.move_speed = role_vo.move_speed
    sb_vo.soulboy_lt_id = role_vo.soulboy_lt_id
    sb_vo.soulboy_lg_id = role_vo.soulboy_lg_id
    sb_vo.name = NewAppearanceWGData.Instance:GetQiChongNameByAppeId(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, role_vo.soulboy_lt_id)
    sb_vo.owner_is_mainrole = role:IsMainRole()
    sb_vo.owner_obj = role
    soulboy_obj = self:CreateSoulBoyObj(sb_vo)
    return soulboy_obj
end

function Scene:CreateFollowMingJiangByRole(role)
    local follow_mj = nil
    local role_vo = role:GetVo()
    local mj_vo = GameVoManager.Instance:CreateVo(FollowMingjiangVo)
    mj_vo.pos_x, mj_vo.pos_y = role:GetLogicPos()
    mj_vo.pos_x = mj_vo.pos_x - 4
    mj_vo.pos_y = mj_vo.pos_y + 4
    mj_vo.owner_role_id = role_vo.role_id
    mj_vo.owner_obj_id = role_vo.obj_id
    mj_vo.move_speed = role_vo.move_speed
    mj_vo.owner_is_mainrole = role:IsMainRole()
    mj_vo.owner_obj = role
    mj_vo.soulboy_lt_id = role_vo.soulboy_lt_id
    mj_vo.name = TianShenWGData.Instance:GetTianshennameByAppeId(role_vo.soulboy_lt_id)
    follow_mj = self:CreateFollowMingJiang(mj_vo)
    return follow_mj
end

function Scene:CreateXiaoTianQuanByRole(role)
    local obj = nil
    local role_vo = role:GetVo()
    local vo = GameVoManager.Instance:CreateVo(XiaoTianQuanVo)
    -- local enemy_obj = GuajiCache.target_obj
    -- if enemy_obj then
    --     vo.pos_x, vo.pos_y = enemy_obj:GetLogicPos()
    -- else
        vo.pos_x, vo.pos_y = role:GetLogicPos()
    -- end
    vo.pos_x = vo.pos_x + 2
    vo.owner_role_id = role_vo.role_id
    vo.owner_obj_id = role_vo.obj_id
    vo.move_speed = role_vo.move_speed
    vo.xiaotianquan_id = role_vo.xiaotianquan_id
    obj = self:CreateXiaoTianQuan(vo)
    return obj
end

function Scene:CreateGuardObjByRole(role)
    local guard_obj = nil
    local role_vo = role:GetVo()
    local gv = GameVoManager.Instance:CreateVo(GuardObjVo)
    gv.pos_x, gv.pos_y = role:GetLogicPos()
    gv.pos_y = gv.pos_y + 2
    gv.owner_role_id = role_vo.role_id
    gv.owner_obj_id = role_vo.obj_id
    gv.move_speed = role_vo.move_speed
    gv.guard_id = role_vo.guard_id
    gv.owner_obj = role
    guard_obj = self:CreateGuardObj(gv)
    return guard_obj
end

function Scene:CreatePetObjByRole(role)
    local pet_obj = nil
    local role_vo = role:GetVo()
    local pet_vo = GameVoManager.Instance:CreateVo(PetObjVo)
    pet_vo.role_vo = role_vo
    pet_vo.pos_x, pet_vo.pos_y = role:GetLogicPos()
    pet_vo.pos_x = pet_vo.pos_x + 2
    pet_vo.lingchong_appeid = role_vo.lingchong_appeid
    pet_vo.owner_objid = role_vo.obj_id
    pet_vo.owner_obj_name = role_vo.name
    pet_vo.move_speed = role_vo.move_speed
    pet_vo.hp = 100
    pet_vo.max_hp = 100
    pet_vo.owner_obj = role
    pet_obj = self:CreatePet(pet_vo)
    return pet_obj
end

function Scene:CreateBeastObjByRole(role, beast_id)
    local beast_obj = nil
    local role_vo = role:GetVo()
    local beast_vo = GameVoManager.Instance:CreateVo(BeastObjVo)
    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
    beast_vo.role_vo = role_vo
    beast_vo.obj_name = aim_cfg and aim_cfg.beast_name or ""
    beast_vo.beast_id = beast_id
    beast_vo.beast_skin = role_vo.beast_skin
    beast_vo.pos_x, beast_vo.pos_y = role:GetLogicPos()
    beast_vo.pos_x = beast_vo.pos_x + 2
    beast_vo.owner_objid = role_vo.obj_id
    beast_vo.owner_obj_name = role_vo.name
    beast_vo.move_speed = role_vo.move_speed
    beast_vo.hp = 100
    beast_vo.max_hp = 100
    beast_vo.owner_obj = role
    beast_obj = self:CreateBeast(beast_vo)
    return beast_obj
end

function Scene:CreateTaskCallInfoObjObjByRole(role, task_callinfo_id)
    local task_callinfo_obj = nil
    local role_vo = role:GetVo()
    local task_callinfo_vo = GameVoManager.Instance:CreateVo(TaskCallInfoObjVo)
    local cfg = TaskWGData.Instance:GetTaskCallInfoCfg()
    local name = cfg and cfg.name

    task_callinfo_vo.role_vo = role_vo
    task_callinfo_vo.obj_name = name
    task_callinfo_vo.task_callinfo_id = task_callinfo_id
    task_callinfo_vo.pos_x, task_callinfo_vo.pos_y = role:GetLogicPos()
    task_callinfo_vo.pos_x = task_callinfo_vo.pos_x + 2
    task_callinfo_vo.owner_objid = role_vo.obj_id
    task_callinfo_vo.owner_obj_name = role_vo.name
    task_callinfo_vo.move_speed = role_vo.move_speed
    task_callinfo_vo.hp = 100
    task_callinfo_vo.max_hp = 100
    task_callinfo_vo.owner_obj = role
    task_callinfo_obj = self:CreateTaskCallInfo(task_callinfo_vo)
    return task_callinfo_obj
end
function Scene:CreateBeautyObjByRole(role)
    local beauty_obj = nil
    local role_vo = role:GetVo()
    local beauty_vo = GameVoManager.Instance:CreateVo(BeautyObjVo)
    beauty_vo.pos_x, beauty_vo.pos_y = role:GetLogicPos()
    beauty_vo.pos_x = beauty_vo.pos_x + 2
    beauty_vo.owner_role_id = role_vo.role_id
    beauty_vo.owner_obj_id = role_vo.obj_id
    beauty_vo.hp = 100
    beauty_vo.move_speed = role_vo.move_speed
    beauty_vo.husong_color = role_vo.husong_color
    beauty_vo.husong_taskid = role_vo.husong_taskid
    beauty_vo.name = ""
    beauty_vo.jingling_guanghuan_img_id = role_vo.jingling_guanghuan_img_id
    beauty_vo.owner_obj = role
    beauty_vo.owner_is_mainrole = role:IsMainRole()
    beauty_obj = self:CreateBeautyObj(beauty_vo)
    return beauty_obj
end

function Scene:CreateFollowNpcByRole(role, npc_id)
    local follow_npc = nil
    local role_vo = role:GetVo()
    local follow_npc_vo = GameVoManager.Instance:CreateVo(FollowNpcVo)
    local npc = Scene.Instance:GetNpcByNpcId(npc_id)
    if npc then
        follow_npc_vo.pos_x, follow_npc_vo.pos_y = npc:GetLogicPos()
        npc:ForceSetVisible(false)
    else
        follow_npc_vo.pos_x, follow_npc_vo.pos_y = role:GetLogicPos()
        follow_npc_vo.pos_x = follow_npc_vo.pos_x + 2
    end
    follow_npc_vo.owner_role_id = role_vo.role_id
    follow_npc_vo.owner_obj_id = role_vo.obj_id
    follow_npc_vo.hp = 100
    follow_npc_vo.move_speed = role_vo.move_speed
    follow_npc_vo.npc_id = npc_id
    follow_npc_vo.owner_obj = role
    follow_npc = self:CreateFollowNpc(follow_npc_vo)
    return follow_npc
end

function Scene:CreateWuHunByRole(role)
    local wuhun_obj = nil
    local role_vo = role:GetVo()
    local wuhun_vo = GameVoManager.Instance:CreateVo(WuHunObjVo)
    wuhun_vo.role_vo = role_vo
    wuhun_vo.pos_x, wuhun_vo.pos_y = role:GetLogicPos()
    wuhun_vo.pos_x = wuhun_vo.pos_x - 2
    wuhun_vo.wuhun_id = role_vo.wuhun_id
    wuhun_vo.wuhun_lv = role_vo.wuhun_lv
    wuhun_vo.owner_obj_id = role_vo.obj_id
    wuhun_vo.owner_obj_name = role_vo.name
    wuhun_vo.move_speed = role_vo.move_speed
    wuhun_vo.hp = 100
    wuhun_vo.max_hp = 100
    wuhun_vo.owner_obj = role
    wuhun_obj = self:CreateWuHun(wuhun_vo)
    return wuhun_obj
end

function Scene:CreateShuangShengTianShenByRole(role)
    local shuangsheng_obj = nil
    local role_vo = role:GetVo()
    local shuangsheng_vo = GameVoManager.Instance:CreateVo(ShuangShengTianShenObjVo)
    shuangsheng_vo.role_vo = role_vo
    shuangsheng_vo.pos_x, shuangsheng_vo.pos_y = role:GetLogicPos()
    shuangsheng_vo.pos_x = shuangsheng_vo.pos_x - 2
    shuangsheng_vo.shaungsheng_tianshen_aura_id = role_vo.shaungsheng_tianshen_aura_id
    shuangsheng_vo.owner_obj_id = role_vo.obj_id
    shuangsheng_vo.owner_obj_name = role_vo.name
    shuangsheng_vo.move_speed = role_vo.move_speed
    shuangsheng_vo.hp = 100
    shuangsheng_vo.max_hp = 100
    shuangsheng_vo.owner_obj = role
    shuangsheng_obj = self:CreateShuangShengTianShen(shuangsheng_vo)
    return shuangsheng_obj
end

-- 创建NPC列表
function Scene:CreateNpcList()
    if CgManager.Instance:IsCgIng() then
        return
    end

    local bool = false
    if nil ~= self.scene_config.npcs then
        for k, v in pairs(self.scene_config.npcs) do

            bool = TaskWGData.Instance:IsNPCVisible(v.id)
            local visible_distance = v.is_walking == 1 and 180 or 45

            --任务链采集雪球不是行走npc策划又要看见，所以要特殊处理一下
            if self:GetSceneType() == SceneType.CROSS_TASK_CHAIN_CAI_JI then
                visible_distance = 180
            end

            if bool and math.abs(v.x - self.main_role_pos_x) <= visible_distance and math.abs(v.y - self.main_role_pos_y) <= visible_distance then
                if nil == self:GetObjByTypeAndKey(SceneObjType.Npc, v.id) then
                    local vo = GameVoManager.Instance:CreateVo(NpcVo)
                    vo.pos_x = v.x
                    vo.pos_y = v.y
                    vo.npc_id = v.id
                    vo.rotation_y = v.rotation_y
                    vo.is_walking = v.is_walking
                    vo.paths = v.paths
                    self:CreateNpc(vo)

                    -- 屏弊npc只是不显示，仍然创建，避免影响新手任务
                    if nil ~= self.shield_npc_id_list[v.id] then
                        self:ShieldNpc(v.id)
                    end
                end
            else
                self:DeleteObjByTypeAndKey(SceneObjType.Npc, v.id)
            end
        end
    end
end

-- 创建传送门列表
function Scene:CreateDoorList()
    if nil ~= self.scene_config.doors then
        for k, v in pairs(self.scene_config.doors) do
            if v.type ~= SceneDoorType.INVISIBLE then
                if math.abs(v.x - self.main_role_pos_x) <= 60 and math.abs(v.y - self.main_role_pos_y) <= 60 then
                    if nil == self:GetObjByTypeAndKey(SceneObjType.Door, v.id) then
                        local vo = GameVoManager.Instance:CreateVo(DoorVo)
                        vo.name = "door" .. v.id
                        vo.pos_x = v.x
                        vo.pos_y = v.y
                        vo.door_id = v.id
                        vo.offset = v.offset
                        vo.rotation = v.rotation

                        local target_config = ConfigManager.Instance:GetSceneConfig(v.target_scene_id)
                        if target_config ~= nil then
                            vo.target_name = target_config.name
                        end

                        self:CreateDoor(vo)
                    end
                else
                    self:DeleteObjByTypeAndKey(SceneObjType.Door, v.id)
                end
            end
        end
    end
end

--创建来袭活动传送门(根据活动开启显示)
function Scene:CreateLaiXiDoorList()
    if QuanMinBeiZhanWGData.Instance:IsInXingTianLaiXiActivity() or ActXianQiJieFengWGData.Instance:IsInXingTianLaiXiActivity() then
        local cfg_key = "randact_xingtianlaixi_cfg_auto"
        if ActXianQiJieFengWGData.Instance:IsInXingTianLaiXiActivity() then
            cfg_key = "randact_xingtianlaixi2_cfg_auto"
        end

        local other_cfg = ConfigManager.Instance:GetAutoConfig(cfg_key).other
        if other_cfg and other_cfg[1].scene_id == self:GetSceneId() then
            local cfg = ConfigManager.Instance:GetAutoConfig(cfg_key).monster_refresh_pos
            if nil ~= cfg then
                for k,v in pairs(cfg) do
                    if math.abs(v.pos_x - self.main_role_pos_x) <= 60 and math.abs(v.pos_y - self.main_role_pos_y) <= 60 then
                        if nil == self:GetObjByTypeAndKey(SceneObjType.QiongQiLaiXi, v.door_id) then
                            local vo = GameVoManager.Instance:CreateVo(DoorVo)
                            vo.name = "door" .. v.door_id
                            vo.pos_x = v.pos_x
                            vo.pos_y = v.pos_y
                            vo.door_id = v.door_id
                            vo.target_name = v.door_name
                            vo.effect_name = v.effect_name
                            vo.offset = {0,4,0}
                            vo.rotation = {0,0,0}
                            self:CreateLaixiDoor(vo)
                        end
                    else
                        self:DeleteObjByTypeAndKey(SceneObjType.QiongQiLaiXi, v.door_id)
                    end
                end
            end
        end
    end
end

--创建任务链场景传动门
function Scene:CreateTaskChainDoor()
    local door_id = SPECIAL_DOOR.TASK_CHAIN
    if self:GetSceneType() ~= SceneType.CROSS_TASK_CHAIN then
        self:DeleteObjByTypeAndKey(SceneObjType.Door, door_id)
        return
    end

    local act_info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
    if act_info == nil or act_info.task_idx == -1 then
        self:DeleteObjByTypeAndKey(SceneObjType.Door, door_id)
        return
    end

    local is_need_create = false
    local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(act_info.task_chain_id)

    if task_chain_cfg == nil then
        self:DeleteObjByTypeAndKey(SceneObjType.Door, door_id)
        return
    end

    -- local end_tab = Split(task_chain_cfg.npc_end_pos, "|")
    local door_pos
    -- if end_tab ~= nil and next(end_tab) ~= nil then
    --     door_pos = {
    --         x = tonumber(end_tab[1]),
    --         y = tonumber(end_tab[2])
    --     }
    -- end
    local door_name = ""
    local task_tab = Split(task_chain_cfg.task_ids, "|")
    if task_tab ~= nil and next(task_tab) ~= nil and task_tab[act_info.task_idx + 1] ~= nil then
        --开服任务连和运营任务连做区分
        local task_cfg = OperationTaskChainWGData.Instance:GetTaskChainTaskCfg(tonumber(task_tab[act_info.task_idx + 1]))
        if task_cfg ~= nil then
            local end_tab = Split(task_cfg.npc_end_pos, "|")
            if end_tab ~= nil and next(end_tab) ~= nil then
                door_pos = {
                    x = tonumber(end_tab[1]),
                    y = tonumber(end_tab[2])
                }
            end

            door_name = task_cfg.task_name
        end
    end
    if door_pos == nil then
        self:DeleteObjByTypeAndKey(SceneObjType.Door, door_id)
        return
    end

    local is_can_look = math.abs(door_pos.x - self.main_role_pos_x) <= 60 and math.abs(door_pos.y - self.main_role_pos_y) <= 60

    local old_door = self:GetObjByTypeAndKey(SceneObjType.Door, door_id)
    if old_door ~= nil and not old_door:IsDeleted() then
        local old_x, old_y = old_door:GetLogicPos()
        if old_x ~= door_pos.x or old_y ~= door_pos.y then
            if is_can_look then
                old_door:SetLogicPos(door_pos.x, door_pos.y)
                old_door:SetDoorName(door_name)
            else
                self:DeleteObjByTypeAndKey(SceneObjType.Door, door_id)
            end
        end
    else
        self:DeleteObjByTypeAndKey(SceneObjType.Door, door_id)
        if is_can_look then
            local vo = GameVoManager.Instance:CreateVo(DoorVo)
            vo.name = "door" .. door_id
            vo.pos_x = door_pos.x
            vo.pos_y = door_pos.y
            vo.door_id = door_id
            vo.target_name = door_name
            vo.target_scene_id = nil
            vo.offset = {0,0,0}
            vo.rotation = {0,0,0}
            local door = self:CreateDoor(vo)
        end
    end
end

-- 创建跳跃点列表
function Scene:CreateJumpPointList()
    local scene_logic = self:GetSceneLogic()
    if nil ~= self.scene_config.jumppoints and scene_logic and scene_logic:IsShowJumpPoint() then
        -- local jumppoints = {}
        for k, v in pairs(self.scene_config.jumppoints) do
            if math.abs(v.x - self.main_role_pos_x) <= 300 and math.abs(v.y - self.main_role_pos_y) <= 300 then
                if nil == self:GetObjByTypeAndKey(SceneObjType.JumpPoint, v.id) then
                    local vo = GameVoManager.Instance:CreateVo(JumpPointVo)
                    vo.name = "jumppoint" .. v.id
                    vo.pos_x = v.x
                    vo.pos_y = v.y
                    vo.range = v.range
                    vo.id = v.id
                    vo.target_id = v.target_id
                    vo.jump_type = v.jump_type
                    vo.air_craft_id = v.air_craft_id
                    vo.is_show = v.is_show
                    vo.jump_speed = v.jump_speed
                    vo.jump_act = v.jump_act
                    vo.jump_tong_bu = v.jump_tong_bu
                    vo.jump_time = v.jump_time
                    vo.offset = v.offset
                    vo.camera_fov = v.camera_fov
                    vo.camera_rotation = v.camera_rotation
                    vo.play_cg = v.play_cg or 0
                    vo.cgs = v.cgs or {}
                    -- jumppoints[v.id] = vo
                    self:CreateJumpPoint(vo)
                end
            else
                self:DeleteObjByTypeAndKey(SceneObjType.JumpPoint, v.id)
            end
        end
    else
        local jump_point_list = self:GetJumpPointList()
        for k,v in pairs(jump_point_list) do
            self:DeleteObjByTypeAndKey(SceneObjType.JumpPoint, v.vo.id)
        end
    end
end

-- 创建温泉皮艇
function Scene:CreateBoatByCouple(boy_obj_id, girl_obj_id, boy_obj, action_type)
    local boy_boat_obj_id = self.boat_list[boy_obj_id]
    local girl_boat_obj_id = self.boat_list[girl_obj_id]
    if nil ~= boy_boat_obj_id and nil ~= girl_boat_obj_id and boy_boat_obj_id == girl_boat_obj_id then
        return
    end
    self:DeleteBoatByRole(boy_obj_id)
    self:DeleteBoatByRole(girl_obj_id)
    local vo = GameVoManager.Instance:CreateVo(BoatObjVo)
    vo.boy_obj_id = boy_obj_id
    vo.girl_obj_id = girl_obj_id
    vo.action_type = action_type
    if nil ~= boy_obj then
        vo.pos_x, vo.pos_y = boy_obj:GetLogicPos()
    end
    local boat_obj = self:CreateObj(vo, SceneObjType.BoatObj)
    self.boat_list[boy_obj_id] = boat_obj:GetObjId()
    self.boat_list[girl_obj_id] = boat_obj:GetObjId()
end

-- 删除温泉皮艇
function Scene:DeleteBoatByRole(role_obj_id)
    local boat_obj_id = self.boat_list[role_obj_id]
    if nil ~= boat_obj_id then
        local boat_obj = self:GetObjectByObjId(boat_obj_id)
        if nil ~= boat_obj then
            local boy_obj_id = boat_obj.vo.boy_obj_id
            local girl_obj_id = boat_obj.vo.girl_obj_id
            if nil ~= boy_obj_id then
                self.boat_list[boy_obj_id] = nil
            end
            if nil ~= girl_obj_id then
                self.boat_list[girl_obj_id] = nil
            end
        end
        self:DeleteObj(boat_obj_id, 0)
    end
end

function Scene:GetBoatByRole(role_obj_id)
    local boat_obj_id = self.boat_list[role_obj_id]
    if nil ~= boat_obj_id then
        return self:GetObjectByObjId(boat_obj_id)
    end
end

local client_obj_id_inc = 0x10000
function Scene:GetSceneClientId()
    client_obj_id_inc = client_obj_id_inc + 1
    return client_obj_id_inc
end

function Scene:CreateObj(vo, obj_type)
    -- if obj_type ~= SceneObjType.MainRole and obj_type ~= SceneObjType.Door and obj_type ~= SceneObjType.JumpPoint then
    --     return
    -- end

    if vo.obj_id < 0 then
        vo.obj_id = self:GetSceneClientId()
    end

    local old_obj = self.obj_list[vo.obj_id]
    if old_obj then
        print_error("[Scene] BigBug, obj exits in obj_list", old_obj:GetType(), vo.name, obj_type)
        return nil
    end

    local obj = nil
    if obj_type == SceneObjType.Role and self:GetSceneId() ~= NewPlayTestSceneId then
        obj = Role.New(vo)
    elseif obj_type == SceneObjType.MainRole then
        obj = MainRole.New(vo)
    elseif obj_type == SceneObjType.Monster then
        local monster_id = vo.monster_id or 0
        local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
        if cfg and cfg.type == MONSTER_TYPE.BOSS then
            obj = Boss.New(vo)
        else
            obj = Monster.New(vo)
        end
    elseif obj_type == SceneObjType.Pet then
        -- obj = Pet.New(vo)
        obj = LingChong.New(vo)
    elseif obj_type == SceneObjType.Door then
        obj = Door.New(vo)
    elseif obj_type == SceneObjType.QiongQiLaiXi then
        obj = QiongQiLaiXiObj.New(vo)
    elseif obj_type == SceneObjType.JumpPoint then
        obj = JumpPoint.New(vo)
    elseif obj_type == SceneObjType.EffectObj then
        obj = EffectObj.New(vo)
    elseif obj_type == SceneObjType.FallItem then
        obj = FallItem.New(vo)
    elseif obj_type == SceneObjType.GatherObj then
        obj = GatherObj.New(vo)
    elseif obj_type == SceneObjType.TruckObj then
        obj = TruckObj.New(vo)
    elseif obj_type == SceneObjType.Npc then
        if vo.is_walking and vo.is_walking == 1 then
            obj = WalkNpc.New(vo)
        else
            obj = Npc.New(vo)
        end
    elseif obj_type == SceneObjType.FakeNpc then
        obj = Npc.New(vo)
    elseif obj_type == SceneObjType.FollowNpc then
        obj = FollowNpc.New(vo)
    elseif obj_type == SceneObjType.SpriteObj then
        obj = SpiritObj.New(vo)
    elseif obj_type == SceneObjType.GoddessObj then
        obj = Goddess.New(vo)
    elseif obj_type == SceneObjType.SoulBoyObj then
        obj = SoulBoyObj.New(vo)
    elseif obj_type == SceneObjType.FollowMingJiang then
        obj = FollowMingJiang.New(vo)
    elseif obj_type == SceneObjType.XiaoTianQuan then
        obj = XiaoTianQuan.New(vo)
    elseif obj_type == SceneObjType.EventObj then
        obj = EventObj.New(vo)
    elseif obj_type == SceneObjType.MarryObj then
        obj = MarryObj.New(vo)
    elseif obj_type == SceneObjType.Trigger then
        obj = TriggerObj.New(vo)
    elseif obj_type == SceneObjType.DefenseTowerObj then
        obj = DefenseTowerObj.New(vo)
    elseif obj_type == SceneObjType.MingRen then
        obj = MingRenRole.New(vo)
    elseif obj_type == SceneObjType.BoatObj then
        obj = BoatObj.New(vo)
    elseif obj_type == SceneObjType.BeautyObj then
        obj = Beauty.New(vo)
    elseif obj_type == SceneObjType.MingJiangObj then
        obj = MingJiangObj.New(vo)
    elseif obj_type == SceneObjType.CoupleHaloObj then
        obj = CoupleHaloObj.New(vo)
    elseif obj_type == SceneObjType.BossStoneObj then
        obj = BossStoneObj.New(vo)
    elseif obj_type == SceneObjType.GuardObj then
        obj = GuardObj.New(vo)
    elseif obj_type == SceneObjType.TestRole then
        obj = TestRole.New(vo)
    -- elseif obj_type == SceneObjType.DogObj then
    --     obj = DogObj.New(vo)
    elseif obj_type == SceneObjType.Statue then
        obj = Statue.New(vo)
    elseif obj_type == SceneObjType.CityOwnerStatue then
        obj = CityOwnerStatue.New(vo)
    elseif obj_type == SceneObjType.AnswerCircle then
        obj = AnswerCircle.New(vo)
    elseif obj_type == SceneObjType.WuHunZhenShen then
        obj = WuHunObj.New(vo)
    elseif obj_type == SceneObjType.BeastObj then
        obj = BeastObj.New(vo)
    elseif obj_type == SceneObjType.ShuangShengTianShen then
        obj = ShuangShengTianShenObj.New(vo)
    elseif obj_type == SceneObjType.TaskCallInfoObj then
        obj = TaskCallInfoObj.New(vo)
    end

    if obj == nil then return end
    obj.draw_obj:SetObjType(obj_type)
    obj:Init(self)
    self.obj_list[vo.obj_id] = obj

    local obj_key = obj:GetObjKey()
    if obj_key then
        if nil == self.obj_group_list[obj_type] then
            self.obj_group_list[obj_type] = {}
        end

        self:DeleteObjByTypeAndKey(obj_type, obj_key)
        self.obj_group_list[obj_type][obj_key] = obj
    end

    if not IsEmptyTable(self.hide_follow_ui_obj_list) then
        local follow_ui = obj:GetFollowUi()
        if follow_ui and follow_ui:GetVisiable() then
            obj:ForceSetFollowUIVisible(false)
            self.hide_follow_ui_obj_list[vo.obj_id] = obj
        end
    end

    GlobalEventSystem:Fire(ObjectEventType.OBJ_CREATE, obj)
    return obj
end
----------------------------------------------------
-- Create end
----------------------------------------------------

----------------------------------------------------
-- Delete begin
----------------------------------------------------
function Scene:DeleteObjByTypeAndKey(obj_type, obj_key)
    if nil ~= self.obj_group_list[obj_type] then
        local obj = self.obj_group_list[obj_type][obj_key]
        if nil ~= obj then
            self:DeleteObj(obj:GetObjId(), 0)
        end
    end
end

function Scene:DeleteObjsByType(obj_type)
    local t = self.obj_group_list[obj_type]
    if nil ~= t then
        for _, v in pairs(t) do
            self:DeleteObj(v:GetObjId(), 0)
        end
    end
end

function Scene:DeleteObj(obj_id, delay_time)
    self:DelObjHelper(obj_id, delay_time or 0)
end

function Scene:DelObjHelper(obj_id, delay_time)
    local del_obj = self.obj_list[obj_id]
    if del_obj == nil then
        return
    end

    if del_obj == self.main_role then
        print_error("[Scene] Big Bug, DelObjHelper", del_obj:GetObjId())
        return
    end

    self.obj_list[obj_id] = nil
    if del_obj:GetObjKey() ~= nil and self.obj_group_list[del_obj:GetType()] ~= nil then
        self.obj_group_list[del_obj:GetType()][del_obj:GetObjKey()] = nil
    else
    	if del_obj:GetType() ~= nil and del_obj:GetType() == SceneObjType.Role then
    		print_error("[Scnen] DelObjHelper BigBug", self:GetSceneId(), del_obj:GetObjKey())
    	end
    end

    self:Fire(ObjectEventType.OBJ_DELETE, del_obj)

    if delay_time > 0 and not del_obj:IsTower() then
        GlobalTimerQuest:AddDelayTimer(function()
            -- print("DelObjHelper",obj_id,del_obj)
            del_obj:DeleteMe()
        end, delay_time)
    else
        del_obj:DeleteMe()
    end

    if not IsEmptyTable(self.hide_follow_ui_obj_list) and self.hide_follow_ui_obj_list[obj_id] ~= nil then
        self.hide_follow_ui_obj_list[obj_id] = nil
    end
end

function Scene:DeleteEffect(monster_id)
    -- body
    local list = self.obj_group_list[SceneObjType.EffectObj]
    if not list or IsEmptyTable(list) then return end

    for k,v in pairs(list) do
        if v:GetVo().summoner_obj_id == monster_id or v:GetVo().deliverer_obj_id == monster_id then
            FightWGCtrl.Instance:LostMonster(v:GetVo().summoner_obj_id, v:GetVo().deliverer_obj_id)
            self:DeleteObj(v:GetObjId(), 0)
        end
    end
end

----------------------------------------------------
-- Delete end
----------------------------------------------------

-- 是否友方
function Scene:IsFriend(target_obj)
    return self.scene_logic:IsFriend(target_obj, self.main_role)
end

-- 是否敌方
function Scene:IsEnemy(target_obj, ignore_table)
    return self.scene_logic:IsEnemy(target_obj, self.main_role, ignore_table)
end

function Scene:HpColor(target_obj,ignore_table)
    return self.scene_logic:HpColor(target_obj, self.main_role)
end

-- 选取最近的对象
function Scene:SelectObjHelper(obj_type, x, y, distance_limit, select_type, ignore_table)
    if self:GetSceneType() == SceneType.GuildBoss and GuildBossWGData.Instance:IsBossWuDi() then
        local target_obj = self:SelectMinDisMonster(GuildBossWGData.Instance:GetMonsterId())
        if target_obj then
            return target_obj, distance_limit
        end
    end

    local obj_list = self:GetObjListByType(obj_type)
    local target_obj = nil
    local target_distance = distance_limit
    local target_x, target_y, distance = 0, 0, 0
    local can_select = true
    local target_obj_reserved = nil
    local target_distance_reserved = distance_limit

    for _, v in pairs(obj_list) do
        if v:IsCharacter() and not v:IsPerformer() then
            can_select = true
            if SelectType.Friend == select_type and not v:IsMainRole() then
                can_select = self.scene_logic:IsFriend(v, self.main_role)
            elseif SelectType.Enemy == select_type then
                can_select = self.scene_logic:IsEnemy(v, self.main_role, ignore_table)
            end

            if can_select then
                target_x, target_y = v:GetLogicPos()
                local r_x, r_y = v:GetRealPos()
                distance = GameMath.GetDistance(x, y, target_x, target_y, false)

                local isBlock = AStarFindWay:IsBlock(target_x, target_y)
                -- 优先寻找非障碍区的
                if not isBlock then
                    if distance < target_distance then
                        target_obj = v
                        target_distance = distance
                    end
                else
                    if distance < target_distance_reserved then
                        target_obj_reserved = v
                        target_distance_reserved = distance
                    end
                end
            end
        end
    end

    if nil == target_obj then
        return target_obj_reserved, target_distance_reserved
    end

    return target_obj, target_distance
end

--选择指定id的最近的怪物   召唤出来的boss 因为monster_id一样,需要用monster_key来做区分
function Scene:SelectMinDisMonster(monster_id, distance_limit,monster_key)
    local target_obj = nil
    local target_distance = distance_limit or 50
    target_distance = target_distance * target_distance
    local target_x, target_y, distance = 0, 0, 0
    local main_role_x, main_role_y = self.main_role:GetLogicPos()

    local monster_obj_list = self:GetMonsterList()
    for _, v in pairs(monster_obj_list) do
        if v:GetMonsterId() == monster_id and not v:IsRealDead() and (monster_key == nil or monster_key == 0 or (monster_key == v:GetMonsterKey())) then
            target_x, target_y = v:GetLogicPos()
            distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
            if distance < target_distance then
                if v:IsInBlock() then
                    target_obj = target_obj or v
                else
                    target_obj = v
                    target_distance = distance
                end
            end
        end
    end
    return target_obj
end

--选择指定id的最近的采集物
function Scene:SelectMinDisGather(gather_id, distance_limit)
    local target_obj = nil
    distance_limit = distance_limit or 50
    local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[gather_id]
    if gather_config then
        distance_limit = gather_config.distance_limit or 100
    end
    local target_distance = distance_limit
    target_distance = target_distance * target_distance
    local target_x, target_y, distance = 0, 0, 0
    local main_role_x, main_role_y = self.main_role:GetLogicPos()

    local gather_obj_list = self:GetObjListByType(SceneObjType.GatherObj)
    for _, v in pairs(gather_obj_list) do
        if v:GetGatherId() == gather_id and not v:IsDeleted() then
            target_x, target_y = v:GetLogicPos()
            distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
            if distance < target_distance then
                if v:IsInBlock() then
                    target_obj = target_obj or v
                else
                    target_obj = v
                    target_distance = distance
                end
            end
        end
    end
    return target_obj
end

-- 拾取所有物品
-- local others_item_tips_time = 0
-- local bag_full_tips_time = 0
function Scene:PickAllFallItem()
    if PickCache.last_time > Status.NowTime then
        return
    end

    if not self.main_role or self.main_role:IsRealDead() then
        return
    end

    local fall_item_list = self:GetObjListByType(SceneObjType.FallItem)
    if not next(fall_item_list) and PickCache.pop_reward then    -- 不存在掉落物且检测时间已到则弹出恭喜获得
        if self.pick_item_id_list and #self.pick_item_id_list > 0 and Status.NowTime >= PickCache.pop_reward then
            if self:GetSceneType() == SceneType.VIP_BOSS or
                self:GetSceneType() == SceneType.KF_BOSS or
                self:GetSceneType() == SceneType.WorldBoss or
                self:GetSceneType() == SceneType.DABAO_BOSS or
                self:GetSceneType() == SceneType.Shenyuan_boss or
                self:GetSceneType() == SceneType.MJ_BOSS or
                self:GetSceneType() == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS or
                self:GetSceneType() == SceneType.MJ_KF_BOSS or
                self:GetSceneType() == SceneType.CROSS_AIR_WAR or
                self:GetSceneType() == SceneType.CROSS_LAND_WAR or
                CrossTreasureWGData.Instance:IsServerTreasureBeastStatusBoss() then
                Scene.ChcekShowScenePickItemList(self.pick_item_id_list)
            end

            self.pick_item_id_list = nil
        end
        return
    end

    local empty_num = ItemWGData.Instance:GetEmptyNum()
    --[[
    local stuff_empty_num = ItemWGData.Instance:GetStuffBagEmptyNum()
    local auto_sell = SettingWGData.Instance:GetGuajiDataTable(SETTING_TYPE.GUAJI_SCALE)-- 自动出售装备
    local auto_ronglian = SettingWGData.Instance:GetGuajiDataTable(SETTING_TYPE.GUAJI_RONGLIAN)-- 自动熔炼装备
    if empty_num < GameEnum.BAG_CELL_NUM_LIMIT then
        local item_list = ItemWGData.Instance:GetBagItemDataList()
        for k,v in pairs(item_list)do
            local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
            if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
                if auto_sell and item_cfg.color <= 2 then
                    BagWGCtrl.Instance:SendDiscardItem(v.index, v.num, v.item_id, 1, 0)
                    break
                end
            end
        end
    end
    --]]
    local item_objid_list = {}
    -- local stuff_item_objid_list = {}

    local pick_item_num = 0
    -- local pick_stuff_num = 0
    -- local has_others_item = false
    local x, y = self.main_role:GetLogicPos()
    local is_pb_shouhu = self:GetCurFbSceneCfg().pb_shouhu
    local can_auto = EquipWGData.Instance:CanAutoPick(is_pb_shouhu == 1)

    local xiaogui_x, xiaogui_y = 0, 0
    if can_auto then
        local guard_obj = self.main_role:GetGuardObj()
        if guard_obj and not guard_obj:IsDeleted() and guard_obj:GetVo() ~= nil then
            local real_pos_x, real_pos_y = guard_obj:GetRealPos()
            xiaogui_x, xiaogui_y =GameMapHelper.WorldToLogic(real_pos_x, real_pos_y)
        end
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local scene_logic = self:GetSceneLogic()
    for k, v in pairs(fall_item_list) do
        local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.vo.item_id)
        if not v:IsPicked() then --个人塔防里的buff需要把 item_cfg and 这个判断条件去掉了，要是这里出什么bug就加上去
            local dis = v:GetAutoPickupMaxDis()
            dis = can_auto and 10 or dis  --策划需求 守护小鬼自动拾取需要跑到物品位置里 把小鬼拾取的范围加大
            dis = dis * dis
            local tx = v:GetVo().pos_x
            local ty = v:GetVo().pos_y
            local act_flag = ServerActivityWGData.Instance:GetIsCanAutoPick(v:GetVo().monster_id, self:GetSceneId())
            if (v:IsInBlock()
                or scene_logic:CanAutoPick()
                or GameMath.GetDistance(x, y, tx, ty, false) <= dis)
                or (can_auto and GameMath.GetDistance(xiaogui_x, xiaogui_y, tx, ty, false) <= dis and act_flag) then
                if v:GetVo().owner_role_id <= 0 or v:GetVo().owner_role_id == self.main_role:GetRoleId()
                    or (v:GetVo().lose_owner_time > 0 and v:GetVo().lose_owner_time <= server_time) then
                    -- 红包、绑定元宝之类的    去除的判断  有这个判断不能捡他人1分钟后的物品  v:GetVo().owner_role_id <= 0 or (v:GetVo().owner_role_id == self.main_role:GetRoleId() and
                    if v:GetVo().is_buff_falling == 1 or item_cfg and self:GetGuajiDataTable(v:IsEquip(), item_cfg) and server_time >= v:GetVo().drop_time + 2 then   --[[Status.NowTime >= v:GetVo().create_time + 1 and]]
                        pick_item_num = pick_item_num + 1
                        if v:IsCanPickItem() then
                            v:RecordIsPicked()
                            table.insert(item_objid_list, v:GetObjId())
                            -- if v:GetVo().monster_id  > 0 then
                            --     local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v:GetVo().monster_id]
                            --     if monster_cfg and monster_cfg.type == MONSTER_TYPE.BOSS then
                            --         break
                            --     end
                            -- end

                            if not (can_auto and act_flag) then --非自动捡取每次捡一个
                                break
                            end
                        end
                    else
                        if v.others_tips_time == nil or v.others_tips_time < Status.NowTime then
                            v.others_tips_time = Status.NowTime + 10
                            -- has_others_item = true
                        end
                    end
                -- elseif v:IsEquip() and (v:GetVo().owner_role_id <= 0 or v:GetVo().owner_role_id == self.main_role:GetRoleId()) and
                --     Status.NowTime >= v:GetVo().create_time + 2 and
                --     ((auto_pick_color[item_cfg.color]) or v:GetVo().is_buff_falling == 1) then
                --     -- 自己的物品   auto_pick_item and item_cfg.color > auto_pick_color
                --     v:RecordIsPicked()
                --     table.insert(item_objid_list, v:GetObjId())
                --     if not v:IsCoin() then
                --         pick_item_num = pick_item_num + 1
                --     end
                -- elseif not v:IsEquip() and (v:GetVo().owner_role_id <= 0 or v:GetVo().owner_role_id == self.main_role:GetRoleId()) and

                --     Status.NowTime >= v:GetVo().create_time + 2 and
                --     pick_other and not v:IsVirtualItem() then
                --     --其他物品
                --     pick_item_num = pick_item_num + 1
                --     v:RecordIsPicked()
                --     table.insert(item_objid_list, v:GetObjId())
                elseif v:GetVo().owner_role_id > 0 or v:GetVo().owner_role_id ~= self.main_role:GetRoleId() then
                    if not self.timer_quest then
                        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FallItemsNoOccupyTips)
                        self.timer_quest = GlobalTimerQuest:AddDelayTimer(function()
                            GlobalTimerQuest:CancelQuest(self.timer_quest)
                            self.timer_quest = nil
                        end, 2)
                    end
                end
            end

            if ((empty_num == 0 and empty_num < pick_item_num) or
                (empty_num > 0 and empty_num <= pick_item_num)) then
                break
            end
        end
    end
    --判断猎鲲逻辑
    if self:GetSceneType() == SceneType.CROSS_LIEKUN then
        local fall_obj = nil
        local fall_vo = nil
        local remove_list = {}
        for i,v in ipairs(item_objid_list) do
            fall_obj = self:GetObjectByObjId(v)
            fall_vo = fall_obj:GetVo()
            if fall_obj and fall_vo then
                if not self.scene_logic:CheckIsOurGuildFall(fall_vo) then
                    table.insert(remove_list,i)
                end
            end
        end
        if next(remove_list) and self.scene_logic:GetFlushTipFlag() then
            self.scene_logic:SetFlushTipFlag(false)
            table.remove(remove_list,1)
        end

        for i=#item_objid_list,1,-1 do
            for j,v in ipairs(remove_list) do
                if v == i then
                    table.remove(item_objid_list,v)
                end
            end
        end
    end
    -- if 0 == empty_num and #item_objid_list > 0 and bag_full_tips_time < Status.NowTime then
    --     local scene_type = Scene.Instance:GetSceneType()
    --     if not Auto_Pick_SceneType[scene_type] then
    --         bag_full_tips_time = Status.NowTime + 2
    --     end
    --     RoleBagWGCtrl.Instance:OpenRoleBagClean(item_objid_list)
    --     return
    -- end
    -- if has_others_item and others_item_tips_time < Status.NowTime and #item_objid_list == 0 then
    --     others_item_tips_time = Status.NowTime + 1
    --     -- TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NotMyItem)
    --     SysMsgWGCtrl.ErrorRemind(Language.Common.NotMyItem)
    -- end
    local stuff_objid_list = {}
    local not_stuff_bjid_list = {}
    local buff_bjid_list = {}
    for k,v in pairs(item_objid_list) do
        local obj = self:GetObjectByObjId(v)
        local vo = obj:GetVo()
        if obj and vo then
           local cfg = ItemWGData.Instance:GetItemConfig(vo.item_id)
            if vo.is_buff_falling == 1 then
                table.insert(buff_bjid_list,v)
            elseif cfg then
                if cfg.is_stuff and cfg.is_stuff == 0 then
                    table.insert(not_stuff_bjid_list,v)
                else
                    table.insert(stuff_objid_list,v)
                end

                if can_auto then
                    if not self.pick_item_id_list then
                        self.pick_item_id_list = {}
                    end

                    table.insert(self.pick_item_id_list, {item_id = vo.item_id, param = {star_level = vo.star_num}})
                end
            end
        end
    end

    if #buff_bjid_list > 0 then
         Scene.ScenePickItem(buff_bjid_list)
    end
    if #not_stuff_bjid_list > 0 then --and empty_num > 0
        Scene.ScenePickItem(not_stuff_bjid_list)
    end
    if #stuff_objid_list > 0 then --and stuff_empty_num > 0
        Scene.ScenePickItem(stuff_objid_list)
    end

    if self.pick_item_id_list and #self.pick_item_id_list > 1 and can_auto then
        PickCache.pop_reward = Status.NowTime + 1
    end

    --  if #item_objid_list > 0 and empty_num > 0 then
    --     Scene.ScenePickItem(item_objid_list)
    -- end
end

-- 检测是存在掉落物未拾取
function Scene:CheckFinishPick()
	local is_all_pick = false
	local fall_item_list = self:GetObjListByType(SceneObjType.FallItem)
	if not IsEmptyTable(fall_item_list) then
		for _,v in pairs(fall_item_list) do
			if v.vo.is_buff_falling ~= 1 and (not v:IsPicked()) then
				is_all_pick = true
				break
			end
		end
	end

	return is_all_pick
end

function Scene:GetGuajiDataTable(is_equip, item_cfg)
    -- local guaji_data = SettingWGData.Instance
    -- local pick_color_green = guaji_data:GetGuajiDataTable(SETTING_TYPE.GUAJI_PICKUP_GREEN)-- 自动拾取绿色装备
    -- local pick_color_blue = guaji_data:GetGuajiDataTable(SETTING_TYPE.GUAJI_PICKUP_BLUE)-- 自动拾取蓝色装备
    -- local pick_color_purple = guaji_data:GetGuajiDataTable(SETTING_TYPE.GUAJI_PICKUP_PURPLE)-- 自动拾取紫色装备
    -- local pick_color_orange = guaji_data:GetGuajiDataTable(SETTING_TYPE.GUAJI_PICKUP_ORANGE) -- 自动拾取橙色以上装备
    -- local pick_coin = guaji_data:GetGuajiDataTable(SETTING_TYPE.GUAJI_PICKUP_COIN)-- 自动拾取金币
    -- local pick_other = guaji_data:GetGuajiDataTable(SETTING_TYPE.GUAJI_PICKUP_OTHER)-- 自动拾取其它道具
    -- local auto_pick_color = {pick_color_green, pick_color_blue, pick_color_purple, pick_color_orange, pick_color_orange, pick_color_orange}
    -- if not is_equip and pick_coin and item_cfg.use_type and item_cfg.use_type == 2 then --自动拾取金币
    --     return true
    -- end

    -- if is_equip and item_cfg.color and auto_pick_color[item_cfg.color] then
    --     return true
    -- end
    -- if not is_equip and pick_other and (not item_cfg.use_type or item_cfg.use_type ~= 2) then
    --     return true
    -- end
    -- return false

    return true
end

-- 寻找跳跃点
function Scene:FindJumpPoint(x, y)
    local temp_table = {}
    local jump_obj_list = self:GetObjListByType(SceneObjType.JumpPoint)
    for k, v in pairs(jump_obj_list) do
        if v.vo.target_id ~= 0 and v.vo.range > 0 then
            local vx, vy = v:GetLogicPos()
            local point_distance = GameMath.GetDistance(x, y, vx, vy, false)
            if point_distance <= v.vo.range then
                table.insert(temp_table, v)
            end
        end
    end
    return temp_table
end

-- 寻找跳跃点
function Scene:GetJumpPointVO(point_id)
    if self.scene_config and self.scene_config.jumppoints then
        for k,v in pairs(self.scene_config.jumppoints) do
            if v.id == point_id then
                return v
            end
        end
    end
end


function Scene:FindJumpPointForSingle(role_pos_x, role_pos_y)
    local jump_point = nil
    local jump_obj_list = self:GetObjListByType(SceneObjType.JumpPoint)
    local min_distance = 99999999
    for k, v in pairs(jump_obj_list) do
        if v ~= nil and v.vo ~= nil then
            local range = v.vo.range
            if v.vo.target_id ~= 0 and range > 0 then
                local vx, vy = v:GetLogicPos()
                local point_distance = GameMath.GetDistance(role_pos_x, role_pos_y, vx, vy, false)
                if point_distance <= range * range and point_distance < min_distance then
                    min_distance = point_distance
                    jump_point = v
                end
            end
        end
    end
    return jump_point
end

-- 跳跃到目的地
function Scene:JumpTo(vo, to_point)
    local SHOW_JUMP_ACTION = self.scene_logic:NeedJump()
    if SHOW_JUMP_ACTION or (vo.play_cg and vo.play_cg == 1 and not IsLowMemSystem) then
        local target_point = self:GetObjByTypeAndKey(SceneObjType.JumpPoint, to_point.vo.target_id)
        self.main_role:JumpTo(vo, to_point, target_point, function()
            if not to_point or not to_point.vo then
                return
            end

            if not to_point:IsDeleted() and to_point.vo.target_id and to_point.vo.target_id ~= 0 and to_point.vo.target_id ~= -1 then
                if target_point then
                    -- 延迟到下一帧执行
                    CountDown.Instance:AddCountDown(0.01, 0.01, function()
                        self:JumpTo(to_point.vo, target_point)
                    end)
                end
                return
            end

            -- 只需要在最后一个跳跃点完成时同步位置
            local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
            Scene.SendSyncJump(self:GetSceneId(), to_point.vo.pos_x, to_point.vo.pos_y, scene_key)
            Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
            self.main_role:SetJump(false)
            self.main_role:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
            -- self.main_role.vo.move_mode = MOVE_MODE.MOVE_MODE_NORMAL
            self:Fire(OtherEventType.JUMP_STATE_CHANGE, false)
            self.main_role:UpdateMount()
            -- self:ContinueHuSong()
        end)
    elseif not CgManager.Instance:IsCgIng() then
        local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
        for i = 1, 10 do
            local next_target = self:GetObjByTypeAndKey(SceneObjType.JumpPoint, to_point.vo.target_id)
            if next_target then
                to_point = next_target
            else
                break
            end
        end

        self.main_role:ToJumpPath(to_point.vo.range)
        Scene.SendSyncJump(self:GetSceneId(), to_point.vo.pos_x, to_point.vo.pos_y, scene_key)
    end
end

function Scene:CheckJump()
    if TaskWGCtrl.Instance:IsFly() then
        return false
    end

    if not self.scene_logic:GetCanJump() then
        return false
    end

    if self.main_role:IsJump() then
        return false
    end

    if self.main_role:IsQingGong() then
        return false
    end

    if self.main_role:IsMitsurugi() then
        return false
    end

    if CgManager.Instance:IsCgIng() then
        return false
    end

    if self.main_role.vo.husong_taskid > 0 then
        -- SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.CanNotJump)
        return false
    end

    local x, y = self.main_role:GetLogicPos()
    local jump_obj = self:FindJumpPointForSingle(x, y)
    if jump_obj == nil or not jump_obj:GetIsAvailable() then
        return false
    end

    local first_point_vo = jump_obj.vo
    if first_point_vo == nil then
        return false
    end

    if first_point_vo.id == self.main_role.vo.jumping_id then
        return false
    end

    local target_point = self:GetObjByTypeAndKey(SceneObjType.JumpPoint, first_point_vo.target_id)
    if not target_point then
        return false
    end

    self:ForceCancleSkillRang()
    self:JumpTo(first_point_vo, target_point)

    return true
end

function Scene:GetIsInDoor()
    local door_obj_list = self:GetObjListByType(SceneObjType.Door)
    for k, v in pairs(door_obj_list) do
        local door_x, door_y = v:GetLogicPos()
        if GameMath.GetDistance(self.main_role_pos_x, self.main_role_pos_y, door_x, door_y, false) < 4 * 4 then
            return true,v
        end
    end
    return false
end

function Scene:OnCgEnd()
   GlobalTimerQuest:AddDelayTimer(function ( ... )
       self:CheckClientObj()
   end, 0)
end

function Scene:CheckClientObj()
    if nil == self.scene_config then
        return
    end

    self:CreateNpcList()
    self:CreateDoorList()
    self:CreateJumpPointList()
    -- self:CreatMingRenList()
    self:CreateCityOnwerStatue()
    self:CreateLaiXiDoorList()
    self:CreateTaskChainDoor()
end

-- 创建城主雕像列表
function Scene:CreateCityOnwerStatue()
    local pos_x, pos_y = 201, 104
    local pos_x2, pos_y2 = 154, 104
    local dis_x = math.abs(pos_x - self.main_role_pos_x)
    local dis_y = math.abs(pos_y - self.main_role_pos_y)

    if nil ~= self.city_statue then
        if not self.city_statue:IsDeleted() then
            self:DeleteObj(self.city_statue:GetObjId(), 0)
        end
        self.city_statue = nil
    end

    dis_x = math.abs(pos_x2 - self.main_role_pos_x)
    dis_y = math.abs(pos_y2 - self.main_role_pos_y)
    if nil ~= self.city_statue2 then
        if not self.city_statue2:IsDeleted() then
           self:DeleteObj(self.city_statue2:GetObjId(), 0)
       end
       self.city_statue2 = nil
   end
end

function Scene:PlayerMoveEnd()
    self:CheckChangeScene()
    self:ImmediatelyCheckLatelyGather()
end

function Scene:CheckChangeScene()
    local _, door_obj = self:GetIsInDoor()
    if nil ~= door_obj and false == self.is_in_door and not self.main_role:IsQingGong() and not self.main_role:IsMitsurugi() then
        self.is_in_door = true
        self.main_role:ChangeToCommonState()
        local door_id = door_obj:GetDoorId()
        -- 离开副本
        if door_obj:GetDoorType() == SceneDoorType.FUBEN then
            FuBenWGCtrl.Instance:SendLeaveFB()
        else
            --传送目标场景限制等级判断
            local door_cfg = self:GetSceneDoorCfgById(door_id)
            if door_cfg then
                local target_scene_id = door_cfg.target_scene_id
                if target_scene_id and not TaskWGCtrl.Instance:IsCanGoToScene(target_scene_id, true) then
                    return
                end
            end

            local call_back = function ()
                local flag, str = self:CheckCanChangeScene()
                if not flag then
                    if str then
                        TipWGCtrl.Instance:ShowSystemMsg(str)
                    end
                    return
                end
                if self:GetSceneType() == SceneType.CROSS_LIEKUN then
                    if GuildWGData.Instance:GetCrossLieKunDoorActive() then
                        -- self:IsChangeSceneIng(true)
                        self.SendTransportReq(door_id)
                    end
                elseif self:GetSceneType() == SceneType.CROSS_TASK_CHAIN then
                    local info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
                    if info ~= nil then
                        OperationTaskChainWGCtrl.Instance:SendTaskChainGoToTaskScene(info.task_idx)
                    end
                else
                    local flag = self:GetIsInDoor()
                    if not flag then return end
                    self:IsChangeSceneIng(true)

                    local logic_callback = self.scene_logic:GetDoorNeedAloneLogic(door_id)
                    if logic_callback then
                        logic_callback()
                    else
                        self.SendTransportReq(door_id)
                    end
                end
            end

            if TaskWGCtrl.Instance:IsFly() then --正在飞行中
                TaskWGCtrl.Instance:AddFlyUpList(call_back)
            else
                call_back()
            end
        end
    else
        self.is_in_door = (nil ~= door_obj)
    end
end

function Scene:CheckCanChangeScene()
    -- body
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
        if 0 < main_role:GetTaskToHeighMount() then
            return false, Language.Task.task_mount02
        end
    
        if 0 < main_role:GetTaskToBianShen() then
            return false, Language.Task.task_bianshen02
        end
    
        if main_role:IsFightStateByRole() then
            return false, Language.Fight.FightDesc01
        end
    end

    return true
end

function Scene:GetCurFbSceneCfg()
    local fb_config = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto")
    return fb_config.fb_scene_cfg_list[self:GetSceneType()] or
    fb_config.fb_scene_cfg_list[SceneType.Common]
end

--@scene_id 传送点所在场景，@to_scene_id 传送要去的场景
function Scene:GetSceneDoorPos(scene_id, to_scene_id)
    local scene = ConfigManager.Instance:GetSceneConfig(scene_id)
    if scene ~= nil then
        for i, j in pairs(scene.doors) do
            if j.target_scene_id == to_scene_id then
                --如果 目标场景的传送目标点 存在，则返回。否则返回 传送门 自身的位置.
                if j.door_target_x and j.door_target_y then
                    return j.door_target_x, j.door_target_y
                else
                    return j.x, j.y
                end
            end
        end
    end
    return nil, nil
end

-- 根据npc_id获取该npc所在的场景信息 @{scene, x, y, id}
-- 弃用
function Scene:GetSceneNpcInfo(npc_cfg_id)
    local scene_npc_cfg = nil
    local scene_id = 0
    for k, v in pairs(Config_scenelist) do
        if v.sceneType == SceneType.Common then
            local scene_cfg = ConfigManager.Instance:GetSceneConfig(v.id)
            if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
                for i, j in pairs(scene_cfg.npcs) do
                    if j.id == npc_cfg_id then
                        scene_npc_cfg = j
                        scene_id = v.id
                        break
                    end
                end
            end
            if scene_npc_cfg ~= nil then
                break
            end
        end
    end
    if scene_npc_cfg ~= nil then
        local info = {}
        info.scene = scene_id
        info.x = scene_npc_cfg.x
        info.y = scene_npc_cfg.y
        info.id = npc_cfg_id
        return info
    end
end

-- 在不同模式是否敌方
function Scene:AttackModeIsEnemy(target_obj, shield_others)
    return self.scene_logic:AttackModeIsEnemy(target_obj, self.main_role, shield_others)
end

-- 激活引导箭头指向某点
function Scene:ActGuideArrowTo(x, y)
    if nil == self.guide_arrow then
        self.guide_arrow = GuideArrow.New()
    end
    self.guide_arrow:SetMoveArrowTo(x, y)
end

function Scene:DelGuideArrow()
    if nil ~= self.guide_arrow then
        self.guide_arrow:DeleteMe()
        self.guide_arrow = nil
    end
end

function Scene:ShieldNpc(npc_id, ui_show)
    local npc_obj = Scene.Instance:GetNpcByNpcId(npc_id)
    if nil ~= npc_obj then
        npc_obj:ForceSetVisible(false)
        if ui_show then
            npc_obj:GetFollowUi():ForceSetVisible(false)
        end
    end
    self.shield_npc_id_list[npc_id] = true
end

function Scene:UnShieldNpc(npc_id)
    local npc_obj = Scene.Instance:GetNpcByNpcId(npc_id)
    if nil ~= npc_obj then
        npc_obj:CancelForceSetVisible()
        npc_obj:GetFollowUi():CancelForceSetVisible()
    end
    self.shield_npc_id_list[npc_id] = nil
end

function Scene:GetSceneMosterList()
    return self.scene_config and self.scene_config.monsters or nil
end

function Scene:DeleteCoupleHaloObj(role_obj_id)
    local couple_halo_obj = self.couple_halo_obj_list[role_obj_id]
    if couple_halo_obj then
        local vo = couple_halo_obj:GetVo()
        local target_1_role_id = vo.target_1_role_id
        local target_2_role_id = vo.target_2_role_id

        local couple_halo_obj_id = couple_halo_obj:GetObjId()
        self:DeleteObj(couple_halo_obj_id, 0)

        self.couple_halo_obj_list[target_1_role_id] = nil
        self.couple_halo_obj_list[target_2_role_id] = nil
    end
end

function Scene:GetBossStoneList()
    return self:GetObjListByType(SceneObjType.BossStoneObj)
end

function Scene:ContinueHuSong()
   -- local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    -- if main_role_vo.husong_color <= 1 then
    --     return
    -- end
    if YunbiaoWGCtrl.Instance:GetIsClickHuSongOnBizuo() then
        -- YunbiaoWGData.Instance:MoveToHuShongNpc()
        local task_id = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").other[1].task_id
        if task_id then
            local role = self:GetMainRole()
            local role_x,role_y = role:GetLogicPos()
            local data = TaskWGData.Instance:GetTaskConfig(task_id).accept_npc
            if role_x == data.x and role_y == data.y then
                YunbiaoWGCtrl.Instance:OpenWindow()
            else
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                    YunbiaoWGCtrl.Instance:OpenWindow()
                end)
                GuajiWGCtrl.Instance:MoveToPos(data.scene, data.x, data.y)
            end
        end
    end
end

local ENABLE_FLY_SCENE = {
    [1001] = true, [1002] = true, [1003] = true, [1004] = true, [1005] = true,
    [1006] = true, [1007] = true, [1008] = true, [1009] = true,
}
-- 此场景是否可以使用轻功
function Scene:IsQingGongScene()
    return nil ~= ENABLE_FLY_SCENE[self.act_scene_id]
end

function Scene:SetEnterSceneCount()
    self.enter_scene_count = self.enter_scene_count + 1
end

function Scene:GetEnterSceneCount()
    return self.enter_scene_count
end

function Scene:SetHideGatherList()
    -- body
    local bool = false
    local gather_list = self:GetGatherList()
    for k,v in pairs(gather_list) do
        bool = TaskWGData.Instance:IsGatherVisible(v:GetGatherId())
        if not bool then
            self:DeleteObjByTypeAndKey(SceneObjType.GatherObj, v:GetObjKey())
        elseif TaskGuide.Instance:IsHideGatherId(v:GetGatherId()) then
            bool = not TaskGuide.Instance:CheckHideGather(v:GetGatherId())
            if bool ~= v:GetVisiable() then
                v:VisibleChanged(bool)
                if TaskGuide.Instance.can_auto_all_task then
                    TaskGuide.Instance:CanAutoAllTask(false)
                    GlobalTimerQuest:AddDelayTimer(function ()
                       TaskGuide.Instance:CanAutoAllTask(true)
                    end, 3)
                end
            end
        end
    end
end

function Scene:IsShowSwitchServerModeScene()
    local scene_type = self:GetSceneType()
    for k,v in pairs(ShowSwitchServerModeSceneType) do
        if v == scene_type then
            return true
        end
    end
    return false
end

function Scene:IsShowSwitchGuildModeScene()
    local scene_type = self:GetSceneType()
    for k,v in pairs(ShowSwitchGuildModeSceneType) do
        if v == scene_type then
            return true
        end
    end
    return false
end

function Scene:SelectRandGatherByDis(gather_id, distance_limit)
    local target_distance = distance_limit or 50
    target_distance = target_distance * target_distance
    local obj_list = {}

    local gather_obj_list = self:GetObjListByType(SceneObjType.GatherObj)
    local target_x, target_y = 0, 0
    local distance = nil
    local main_role_x, main_role_y = self.main_role:GetLogicPos()
    local near_obj = nil
    for _, v in pairs(gather_obj_list) do
        if not v:IsDeleted() then
            local is_check = false
            if gather_id ~= nil and v:GetGatherId() == gather_id then
                is_check = true
            elseif gather_id == nil then
                is_check = true
            end

            if is_check then
                target_x, target_y = v:GetLogicPos()
                local obj_dis = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
                if obj_dis < target_distance then
                    table.insert(obj_list, v)
                end

                if distance == nil or distance > obj_dis then
                    distance = obj_dis
                    near_obj = v
                end
            end
        end
    end

    if #obj_list == 0 then
        return near_obj
    else
        return obj_list[GameMath.Rand(1, #obj_list)]
    end
end

--就近采集物
function Scene:SelectNearGather(distance_limit)
    local target_obj = nil
    local target_distance = distance_limit or 50
    target_distance = target_distance * target_distance
    local target_x, target_y, distance = 0, 0, 0
    local main_role_x, main_role_y = self.main_role:GetLogicPos()

    local gather_obj_list = self:GetObjListByType(SceneObjType.GatherObj)
    for _, v in pairs(gather_obj_list) do
        if not v:IsDeleted() then
            target_x, target_y = v:GetLogicPos()
            distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
            if distance < target_distance then
                if v:IsInBlock() then
                    target_obj = target_obj or v
                else
                    target_obj = v
                    target_distance = distance
                end
            end
        end
    end
    return target_obj
end

--就近采集物
function Scene:SelectNearGatherById(distance_limit, id)
    local target_obj = nil
    local target_distance = distance_limit or 50
    if id == nil then
        return target_obj
    end

    target_distance = target_distance * target_distance
    local target_x, target_y, distance = 0, 0, 0
    local main_role_x, main_role_y = self.main_role:GetLogicPos()

    local gather_obj_list = self:GetObjListByType(SceneObjType.GatherObj)
    for _, v in pairs(gather_obj_list) do
        if not v:IsDeleted() and v:GetGatherId() == id then
            target_x, target_y = v:GetLogicPos()
            distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
            if distance < target_distance then
                if v:IsInBlock() then
                    target_obj = target_obj or v
                else
                    target_obj = v
                    target_distance = distance
                end
            end
        end
    end
    return target_obj
end

--就近选择怪物或者非boss小怪
function Scene:SelectNearMonster(distance_limit, no_boss)
    local target_obj = nil
    local target_distance = distance_limit or 50
    target_distance = target_distance * target_distance
    local target_x, target_y, distance = 0, 0, 0
    local main_role_x, main_role_y = self.main_role:GetLogicPos()

    local monster_obj_list = self:GetMonsterList()
    for _, v in pairs(monster_obj_list) do
        if not no_boss or not v:IsBoss() then
            if not v:IsRealDead() then
                target_x, target_y = v:GetLogicPos()
                distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
                if distance < target_distance then
                    if v:IsInBlock() then
                        target_obj = target_obj or v
                    else
                        target_obj = v
                        target_distance = distance
                    end
                end
            end
        end
    end
    return target_obj
end

--就近选择怪物或者非boss小怪, limit_pos,limit_range 用来限制选中的怪是在指定范围内的
-- is_select_enemy标志用来选中可以打的，is_def用于在指定了范围内都不存在怪的时候，选择一个distance_limit范围内的目标
function Scene:SelectNearMonsterByLimit(distance_limit, no_boss, limit_pos, limit_range, is_select_enemy, is_def, is_near)
    local target_obj = nil
    local target_distance = distance_limit or 50
    target_distance = target_distance * target_distance
    local target_x, target_y, distance = 0, 0, 0
    local main_role_x, main_role_y = self.main_role:GetLogicPos()
    local logic = self:GetSceneLogic()
    local def_obj = nil
    local def_dis = 0
    local can_select_def = true

    local monster_obj_list = self:GetMonsterList()
    for _, v in pairs(monster_obj_list) do
        if not no_boss or not v:IsBoss() then
            if not v:IsRealDead() then
                target_x, target_y = v:GetLogicPos()
                local check_dis = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
                if check_dis < target_distance then
                    local is_need_check = true
                    if is_select_enemy and logic ~= nil then
                        is_need_check = logic:IsEnemy(v)
                    end

                    if is_need_check then
                        if limit_pos ~= nil and limit_range ~= nil then
                            local limit_dis = GameMath.GetDistance(limit_pos.x, limit_pos.y, target_x, target_y, false)
                            if limit_dis < limit_range * limit_range then
                                if target_obj == nil or can_select_def then
                                    target_obj = v
                                    distance = check_dis
                                elseif (is_near and distance > check_dis) or not is_near then
                                    target_obj = v
                                    distance = check_dis
                                end

                                can_select_def = false
                            end
                        end

                        local a = GameMath.GetDistance(limit_pos.x, limit_pos.y, target_x, target_y, true)

                        if is_def and target_obj == nil and can_select_def then
                            target_obj = v
                            distance = check_dis
                        elseif is_def and is_near and distance > check_dis and can_select_def then
                            target_obj = v
                            distance = check_dis
                        end
                    end
                end
            end
        end
    end

    return target_obj
end

function Scene:SimulationFall(coin, num, item_id, hide_type)
    local main_role = Scene.Instance:GetMainRole()
    if not main_role or not main_role:GetAttackTarget() then
        return
    end

    local attack_target = main_role:GetAttackTarget()
    if nil == attack_target then
        return
    end
    if nil == attack_target.checks then
        attack_target.checks = 1
        print_log("error, Scene:SimulationFall  checks is nil")
    end
    if nil == attack_target.obj_scale then
        attack_target.obj_scale = 1
        print_log("error, Scene:SimulationFall  obj_scale is nil")
    end

    local local_num = 1
    local t_x, t_y = attack_target:GetLogicPos()
    local min_range = 1 + (attack_target.checks * attack_target.obj_scale / 2)
    local max_range = min_range + local_num + math.max(1, math.floor(num / local_num + 0.5))
    local pos_list = AStarFindWay:GetRandomVaildXYList(t_x, t_y, min_range, max_range, local_num)
    item_id = item_id or COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO

    local fallitem_vo, item_obj
    local delay_time = 2
    for i = 1, #pos_list do
        fallitem_vo = GameVoManager.Instance:CreateVo(FallItemVo)
        fallitem_vo.item_id = item_id
        fallitem_vo.pos_x = pos_list[i].x
        fallitem_vo.pos_y = pos_list[i].y
        fallitem_vo.hide_type = hide_type
        fallitem_vo.is_client_fall = true
        fallitem_vo.client_name = math.floor(coin / local_num) .. Language.Common.Ticket
        Scene.Instance:CreateFallItem(fallitem_vo)

        item_obj = {obj_id=fallitem_vo.obj_id, time=Status.NowTime + delay_time, item_id = fallitem_vo.item_id, index = i}
        table.insert(self.fall_objId_list, item_obj)

        delay_time = delay_time + 0.1
    end
end

function Scene:ClearSimulationFall(now_time, elapse_time)
    -- body
    local item_data = nil
    for k,v in pairs(self.fall_objId_list) do
        if v.time <= now_time then
            Scene.Instance:DeleteObj(v.obj_id)
            table.remove(self.fall_objId_list, k)
            TaskWGCtrl.Instance:CheckKillMonsterTask(v.item_id)

            if 0 == v.index % 5 then
                item_data = {}
                item_data.index = 0
                item_data.item_id = v.item_id
                item_data.num = 1
                item_data.is_bind = 1
                item_data.invalid_time = 0
                item_data.is_client_Simulation = true
                TipWGCtrl.Instance:ShowGetItem(item_data)
            end
            break
        end
    end
end

function Scene:GetIsNpcTalkLock()
    return self.is_npc_talk_lock
end

function Scene:SimulationSceneLoad()
    local scene_logic = self:GetSceneLogic()
    if scene_logic ~= nil then
        local scene_type = self:GetSceneType()
        scene_logic:Out(scene_type, scene_type)

        self:LoadSameScene(self:GetSceneId(), scene_type, true)
    end
end



--------------------------------技能指示器-------------------------------------------------------------------------
function Scene:InitSkillRangTool()
    if self.skill_rang_transform == nil then
        local root = GameObject.Find("GameRoot")
        if IsNil(root) then
            print_error("InitSkillRangTool Error")
            return
        end

        self.skill_rang_loader = AllocAsyncLoader(self, "skill_rang_obj")
        self.skill_rang_loader:SetParent(root.transform)
        self.skill_rang_loader:Load("misc_prefab", "SkillRangObj",
        function (obj)
            if obj == nil then
                return
            end

            self.skill_rang_transform = obj.transform
            self.skill_rang_render = self.skill_rang_transform:Find("Rang"):GetComponent(typeof(UnityEngine.SpriteRenderer))
            self.skill_rang_render_t = self.skill_rang_render.transform
            self.skill_dir_square_render = self.skill_rang_transform:Find("DirSquare"):GetComponent(typeof(UnityEngine.SpriteRenderer))
            self.skill_dir_square_t = self.skill_dir_square_render.transform

            Transform.SetLocalPositionXYZ(self.skill_rang_transform, 99999, 99999, 99999)
        end)
    end
end

function Scene:GetRealRange(range)
    return range * Config.SCENE_TILE_WIDTH + Config.SCENE_TILE_WIDTH / 2
end

local circle_size = 0.335
local circle_skill_size = 0.165
local square_size = 0.2
local sector_size = 0.55
local sector_off = {
    [30] = 0.63,
    [45] = 0.63,
    [60] = 0.63,
    [90] = 0.6,
    [120] = 0.58,
    [150] = 0.55,
}
function Scene:ShowSkillRangBySkillId(skill_id, r_size, ro, target_pos)
    if IsNil(self.skill_rang_transform) then
        self:ForceCancleSkillRang()
        return
    end

    if IsNil(MainCamera) and IsNil(MainCameraFollow) then
        self:ForceCancleSkillRang()
        return
    end

    if not self:MainRoleIsVaild() then
        self:ForceCancleSkillRang()
        return
    end

    if skill_id == nil then
        self:ForceCancleSkillRang()
        return
    end

    local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if cfg == nil then
        self:ForceCancleSkillRang()
        return
    end

    local range_type = cfg.range_type
    self.skill_rang_info = cfg
    self.is_in_skill_rang = true
    self:UpdateSkillPos()
    self.skill_rang_transform.localRotation = Quaternion.Euler(0, 0, 0)

    local skill_rang = 0
    local r1 = cfg.attack_range
    local r2 = cfg.attack_range2
    self.rang_special_scale = 0

    if self.main_role ~= nil and self.main_role.vo ~= nil and SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == self.main_role.vo.special_appearance then
       local effect_list = FightWGData.Instance:GetMainRoleShowEffect()
        if effect_list ~= nil then
            for i = 1, #effect_list do
                if effect_list[i].info ~= nil and effect_list[i].info.buff_type == BUFF_TYPE.EBT_TYPE_BIGGER then
                    if effect_list[i].merge_layer == 0 then
                        self.rang_special_scale = 1
                    else
                        self.rang_special_scale = effect_list[i].info.merge_layer * 1
                    end
                    break
                end
            end
        end
    end


    if range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        r1 = self.rang_special_scale + r1
    else
        r1 = self.rang_special_scale + r1
        r2 = self.rang_special_scale + r2
    end

    if range_type == ATTACK_RANGE_TYPE.SQUARE_ME then
        self.skill_rang_render.enabled = false
        skill_rang = math.max(r1, r2)
        self:DrawSquare(true, r1, r2)
    elseif range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
        self.skill_rang_render.enabled = true
        skill_rang = math.max(r1, r2)
        self:DrawSquare(false, r1, r2)
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_ME then
        self.skill_rang_render.enabled = true
        skill_rang = math.max(r1, r2)
        self:DrawCircular(true, r2)
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
        self.skill_rang_render.enabled = true
        skill_rang = math.max(r1, r2)
        self:DrawCircular(false, r2)
    elseif range_type == ATTACK_RANGE_TYPE.RECTANGLE_ME then
        self.skill_rang_render.enabled = false
        skill_rang = math.max(r1, r2)
        self:DrawSquare(true, r1, r2)
    elseif range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        self.skill_rang_render.enabled = true
        skill_rang = r1
        self:DrawSector(r1, ro, sector_off[cfg.attack_range2] or 0.6)
    end

    self:FlushSkillRangImgState(false)
    local max_r = skill_rang * circle_size
    Transform.SetLocalScaleXYZ(self.skill_rang_render_t, max_r, max_r, max_r)
    self:FlushSkillRang(cfg.skill_id, r_size, ro, target_pos)
end

function Scene:GetObjIsInSkillRange(skill_id, obj, play_x, play_y)
    local is_in = false
    if skill_id == nil or obj == nil or obj:IsDeleted() or self.main_role == nil or self.main_role:IsDeleted() or play_x == nil or play_y == nil then
        return is_in
    end

    local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if cfg == nil then
        return is_in
    end

    local checks = 1
    if obj:IsMonster() then
        local monster_c = obj:GetChecksModel()
        if monster_c ~= nil then
            checks = monster_c or 1
        end
    end

    local r1 = cfg.attack_range             -- 范围参数1（矩形宽度/圆形半径等）
    local r2 = cfg.attack_range2            -- 范围参数2（矩形高度/扇形角度等）
    local range_type = cfg.range_type

    local rang_special_scale = 0
    local t_x, t_y = obj:GetLogicPos()
    local m_x, m_y = self.main_role:GetLogicPos()

    if self.main_role ~= nil and self.main_role.vo ~= nil and SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == self.main_role.vo.special_appearance then
       local effect_list = FightWGData.Instance:GetMainRoleShowEffect()
        if effect_list ~= nil then
            for i = 1, #effect_list do
                if effect_list[i].info ~= nil and effect_list[i].info.buff_type == BUFF_TYPE.EBT_TYPE_BIGGER then
                    if effect_list[i].merge_layer == 0 then
                        rang_special_scale = 1
                    else
                        rang_special_scale = effect_list[i].info.merge_layer * 1
                    end
                    break
                end
            end
        end
    end


    if range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        r1 = rang_special_scale + r1
    else
        r1 = rang_special_scale + r1
        r2 = rang_special_scale + r2
    end

    if range_type == ATTACK_RANGE_TYPE.SQUARE_ME then
        is_in = GameMath.IsInRect(t_x, t_y, play_x - 0.5 * r1, play_y - 0.5 * r1, r1, r1)
    elseif range_type == ATTACK_RANGE_TYPE.RECTANGLE_ME or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
        -- is_in = GameMath.IsInRect(t_x, t_y, play_x - 0.5 * r2, play_y - 0.5 * r1, r2, r1)
        local fenzi_w = math.abs((m_y - play_y) * t_x + (play_x - m_x) * t_y + m_x * play_y - play_x * m_y)
        local fenmu_w = math.sqrt(math.pow(m_y - play_y, 2) + math.pow(play_x - m_x, 2))

        if fenmu_w ~= 0 then
            local d_w = fenzi_w / fenmu_w
            if d_w <= r2 * 0.5 + checks then
                -- 长，也是求点到直线的距离，过矩形中心点的横着的中轴线
                local k = (play_y - m_y) * 1.0 / (play_x - m_x);
                local tmp_k = -1 / k;
                local center_x = (play_x + m_x) * 0.5;
                local center_y = (play_y + m_y) * 0.5;
                local b = center_y - tmp_k * center_x;
                local A = tmp_k;
                local B = -1;
                local C = b;
                local fenzi_h = math.abs(A * t_x + B * t_y + C);
                local fenmu_h = math.sqrt(math.pow(A, 2) + math.pow(B, 2));
                if fenmu_h ~= 0 then
                    local d_h = fenzi_h / fenmu_h
                    if d_h <= r1 * 0.5 + checks then
                        is_in = true
                    end
                end
            end
        end
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_ME then
        local len = u3dpool.v2Length(u3dpool.v2Sub({x = t_x, y = t_y}, {x = play_x, y = play_y}), false)
        local check_r = r1 + checks
        is_in = len <= check_r * check_r

    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
        local len = u3dpool.v2Length(u3dpool.v2Sub({x = t_x, y = t_y}, {x = play_x, y = play_y}), false)
        local check_r = r2 + checks
        is_in = len <= check_r * check_r
    elseif range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET then
        local check_w = r2 + checks
        local check_h = r1 + checks
        is_in = GameMath.IsInRect(t_x, t_y, play_x - 0.5 * check_w, play_y - 0.5 * check_h, check_w, check_h)
    elseif range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        local dist_sq = u3dpool.v2Length(u3dpool.v2Sub({x = t_x, y = t_y}, {x = m_x, y = m_y}), false)
        if dist_sq > (r1 + checks)^2 then return false end

        -- 向量方向检查
        local play_dir_length_sq = u3dpool.v2Length(u3dpool.v2Sub({x = play_x, y = play_y}, {x = m_x, y = m_y}), false)
        if play_dir_length_sq == 0 then return true end -- 施法点与自身重合时视为全方向

        local dot = u3dpool.v3Dot( {x = play_x - m_x, y = play_y - m_y, z = 0}, {x = t_x - m_x, y = t_y - m_y, z = 0})
        if dot < 0 then return false end -- 角度超过90度

        -- 计算角度
        local cos_theta = dot / (math.sqrt(play_dir_length_sq * dist_sq))
        local angle_rad = math.acos(math.min(math.max(cos_theta, -1), 1)) -- 确保acos参数有效
        is_in = math.deg(angle_rad) <= r2 * 0.5
    end

    -- print_error("---攻击对象是否在范围内--", is_in, range_type)
    return is_in
end

function Scene:FlushSkillRang(skill_id, r_size, ro, target_pos)
    if IsNil(self.skill_rang_transform) then
        self:ForceCancleSkillRang()
        return
    end

    if IsNil(MainCamera) and IsNil(MainCameraFollow) then
        self:ForceCancleSkillRang()
        return
    end

    if not self:MainRoleIsVaild() then
        self:ForceCancleSkillRang()
        return
    end

    if self.skill_rang_info == nil then
        self:ForceCancleSkillRang()
        return
    end

    if skill_id == nil or r_size == nil or ro == nil then
        self:ForceCancleSkillRang()
        return
    end

    if skill_id ~= self.skill_rang_info.skill_id then
        self:ForceCancleSkillRang()
        return
    end

    self.skill_ro = ro
    local range_type = self.skill_rang_info.range_type
    local r1 = self.skill_rang_info.attack_range
    local r2 = self.skill_rang_info.attack_range2

    if range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        r1 = self.rang_special_scale + r1
    else
        r1 = self.rang_special_scale + r1
        r2 = self.rang_special_scale + r2
    end

    if range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
        self:FlushSquare(r1, r2, ro)
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
        self:FlushCircular(r1, r2, r_size, target_pos)
    elseif range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        self:FlushSector(r1, ro)
    end
end

function Scene:DrawSquare(is_my, h, w)
    local angle = 0
    if w < h then
        angle = 90
    end

    Transform.SetLocalPositionXYZ(self.skill_dir_square_t, 0, 1, 0)
    self.skill_dir_square_t.localRotation = Quaternion.Euler(-90, angle, 0)
    local s_w = w * 0.88889
    local s_h = h * 0.47367
    local z_value = is_my and 0 or 0 - h * 0.5 - 1
    Transform.SetLocalScaleXYZ(self.skill_dir_square_t, s_w, s_h, 1)
    self.skill_rang_transform:Rotate(Vector3(0, MainCameraFollow.transform.rotation.eulerAngles.y, 0))
    Transform.SetLocalPositionXYZ(self.skill_dir_square_t, 0, 1, z_value)
    self.skill_dir_square_pos = self.skill_dir_square_t.localPosition
end

function Scene:FlushSquare(w, h, ro)
    ro = ro + 90
    local pos = self.skill_dir_square_pos
    local r_cos_x = math.cos(math.rad(ro))
    local r_sin_y = math.sin(math.rad(ro))
    local roate_x = pos.x * r_cos_x - pos.z * r_sin_y
    local roate_y = pos.x * r_sin_y + pos.z * r_cos_x
    self.skill_dir_square_t.localPosition = Vector3(roate_x, pos.y, roate_y)
    self.skill_dir_square_t.localRotation = Quaternion.Euler(-90, 360 - ro, 0)
end

function Scene:DrawCircular(is_my, r)
    local show_r = is_my and 0 or r
    local max_r = show_r * circle_size * 2.35
    Transform.SetLocalScaleXYZ(self.skill_dir_square_t, max_r, max_r, max_r)
    Transform.SetLocalPositionXYZ(self.skill_dir_square_t, 0, 1, 0)
    self.skill_dir_square_pos = self.skill_dir_square_t.localPosition
    self.skill_dir_square_t.localRotation = Quaternion.Euler(-90, 0, 0)
    self.skill_rang_transform:Rotate(Vector3(0, MainCameraFollow.transform.rotation.eulerAngles.y, 0))
end

function Scene:FlushCircular(skill_rang, r, r_size, target_pos)
    if target_pos ~= nil then
        local m_pos = self.main_role:GetDrawObj():GetPosition()
        local len = u3dpool.v3Length(u3dpool.v3Sub(m_pos, target_pos), false)
        if len > skill_rang * skill_rang then
            -- local dir = u3dpool.v3Normalize(u3dpool.v3Sub(target_pos, m_pos))
            local l = self.skill_rang_transform:InverseTransformPoint(target_pos)
            local dir = u3dpool.v3Normalize(u3dpool.v3Sub(l, self.skill_dir_square_pos))
            self.skill_dir_square_t.localPosition = Vector3(self.skill_dir_square_pos.x + dir.x * skill_rang, self.skill_dir_square_pos.y, self.skill_dir_square_pos.z + dir.z * skill_rang)
        else
            local l = self.skill_rang_transform:InverseTransformPoint(target_pos)
            self.skill_dir_square_t.localPosition = Vector3(l.x, self.skill_dir_square_pos.y, l.z)
        end
    else
        local x_len = skill_rang * r_size.x - 0.25
        local y_len = skill_rang * r_size.y - 0.25
        self.skill_dir_square_t.localPosition = Vector3(self.skill_dir_square_pos.x + x_len, self.skill_dir_square_pos.y, self.skill_dir_square_pos.z + y_len)
    end
end

function Scene:DrawRectangle()
end

function Scene:DrawSector(r, angle, off)
    Transform.SetLocalPositionXYZ(self.skill_dir_square_t, 0, 1, 0)
    self.skill_dir_square_render.transform.localRotation = Quaternion.Euler(-90, angle, 0)
    local size = r * sector_size
    Transform.SetLocalScaleXYZ(self.skill_dir_square_t, size, size, size)
    self.skill_rang_transform:Rotate(Vector3(0, MainCameraFollow.transform.rotation.eulerAngles.y, 0))
    Transform.SetLocalPositionXYZ(self.skill_dir_square_t, 0, 1, 0 - r * off)
    self.skill_dir_square_pos = self.skill_dir_square_t.localPosition
end

function Scene:FlushSector(r, ro)
    ro = ro + 90
    local pos = self.skill_dir_square_pos
    local r_cos_x = math.cos(math.rad(ro))
    local r_sin_y = math.sin(math.rad(ro))
    local roate_x = pos.x * r_cos_x - pos.z * r_sin_y
    local roate_y = pos.x * r_sin_y + pos.z * r_cos_x
    self.skill_dir_square_t.localPosition = Vector3(roate_x, pos.y, roate_y)
    self.skill_dir_square_t.localRotation = Quaternion.Euler(-90, 360 - ro, 0)
end

function Scene:GetSkillRangeHighOffset()
    return 0
end

function Scene:UpdateSkillRang(skill_id, len, ro, target_pos)
    -- if IsNil(self.skill_rang_transform) then
    --     self:ForceCancleSkillRang()
    --     return
    -- end

    if IsNil(MainCamera) and IsNil(MainCameraFollow) then
        self:ForceCancleSkillRang()
        return
    end

    if self.skill_rang_transform == nil then
        self:InitSkillRangTool()
    end

    if not self.is_in_skill_rang then
        self:ShowSkillRangBySkillId(skill_id, len, ro, target_pos)
    else
        self:FlushSkillRang(skill_id, len, ro, target_pos)
    end
end

function Scene:HideSkillRnagTool()
    self.is_in_skill_rang = false
    self.skill_rang_info = nil
    self.skill_ro = 0
    self.rang_special_scale = 0

    if not IsNil(self.skill_rang_transform) then
        Transform.SetLocalPositionXYZ(self.skill_rang_transform, 99999, 99999, 99999)
    end
end

function Scene:ForceCancleSkillRang()
    self:HideSkillRnagTool()
    MainuiWGCtrl.Instance:SetSkillForceCancle()
end

function Scene:UpdateSkillPos()
    if not self.is_in_skill_rang then
        return
    end

    if IsNil(self.skill_rang_transform) then
        self:ForceCancleSkillRang()
        return
    end

    if IsNil(MainCamera) and IsNil(MainCameraFollow) then
        self:ForceCancleSkillRang()
        return
    end

    if not self:MainRoleIsVaild() then
        self:ForceCancleSkillRang()
        return
    end


    local draw_obj = self.main_role:GetDrawObj()
    if draw_obj ~= nil then
        local attach_obj
        if self.main_role:IsRidingFightMount() then
            attach_obj = draw_obj:GetPart(SceneObjPart.Mount)
        else
            attach_obj = draw_obj:GetPart(SceneObjPart.Main)
        end

        if attach_obj ~= nil and attach_obj:GetObj() ~= nil then
            local pos = attach_obj:GetObj().transform.position
            self.skill_rang_transform.position = Vector3(pos.x, pos.y + self:GetSkillRangeHighOffset(), pos.z)
        end
    end
end

function Scene:MainRoleIsVaild()
    if self.main_role == nil or self.main_role:IsDeleted() then
        return false
    end

    return true
end

function Scene:GetSkillPos()
    local x = nil
    local y = nil

    if IsNil(self.skill_rang_transform) then
        return x, y
    end

    if not self.is_in_skill_rang then
        return x, y
    end

    if not self:MainRoleIsVaild() then
        return x, y
    end

    if self.skill_rang_info == nil then
        return x, y
    end

    self.skill_ro = self.skill_ro or 0
    local range_type = self.skill_rang_info.range_type
    local r1 = self.skill_rang_info.attack_range
    local r2 = self.skill_rang_info.attack_range2

    if range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        r1 = self.rang_special_scale + r1
    else
        r1 = self.rang_special_scale + r1
        r2 = self.rang_special_scale + r2
    end

    local r_x, r_y = self.main_role:GetLogicPos()

    if range_type == ATTACK_RANGE_TYPE.SQUARE_ME then
        x, y = r_x, r_y
    elseif range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
        local s_pos = self.skill_dir_square_t.position
        local calc_s_pos = Vector3(s_pos.x, 0, s_pos.z)
        local draw_obj = self.main_role:GetDrawObj()
        if draw_obj ~= nil then
            local r_pos = draw_obj:GetPosition()
            local calc_r_pos = Vector3(r_pos.x, 0, r_pos.z)
            local dir = u3dpool.v3Normalize(u3dpool.v3Sub(calc_s_pos, calc_r_pos))
            -- x, y = GameMapHelper.WorldToLogic(s_pos.x, s_pos.z)

            x, y = GameMapHelper.WorldToLogic(r_pos.x + dir.x * r1, r_pos.z + dir.z * r1 - 0.5)
        end
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_ME then
        x, y = r_x, r_y
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
        local s_pos = self.skill_dir_square_t.position
        x, y = GameMapHelper.WorldToLogic(s_pos.x, s_pos.z)
    elseif range_type == ATTACK_RANGE_TYPE.RECTANGLE_ME then
        x, y = r_x, r_y
    elseif range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        local s_pos = self.skill_dir_square_t.position
        local calc_s_pos = Vector3(s_pos.x, 0, s_pos.z)
        local draw_obj = self.main_role:GetDrawObj()
        if draw_obj ~= nil then
            local r_pos = draw_obj:GetPosition()
            local calc_r_pos = Vector3(r_pos.x, 0, r_pos.z)
            local dir = u3dpool.v3Normalize(u3dpool.v3Sub(calc_s_pos, calc_r_pos))
            -- x, y = GameMapHelper.WorldToLogic(s_pos.x, s_pos.z)
            x, y = GameMapHelper.WorldToLogic(r_pos.x + dir.x * r1 - 0.5, r_pos.z + dir.z * r1 - 0.5)
        end
    end
    return x, y
end

function Scene:GetSkillRangImgStr(is_cancle, img)
    local str = is_cancle and "_cancle" or ""
    return img .. str
end

function Scene:FlushSkillRangImgState(is_cancle)
    if IsNil(self.skill_rang_transform) then
        return
    end

    if not self.is_in_skill_rang then
        return
    end

    if not self:MainRoleIsVaild() then
        return
    end

    if self.skill_rang_info == nil then
        return
    end

    local bundle_name, asset_name
    local range_type = self.skill_rang_info.range_type
    if range_type == ATTACK_RANGE_TYPE.SQUARE_ME then
        bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_zhixian"))
    elseif range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG then
        bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_zhixian"))
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_ME then
        bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_circle"))
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
        bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_circle"))
    elseif range_type == ATTACK_RANGE_TYPE.RECTANGLE_ME then
        bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_zhixian"))
    elseif range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_sector" .. self.skill_rang_info.attack_range2))
    end

    if bundle_name ~= nil and asset_name ~= nil then
        local loader = AllocResAsyncLoader(self, "SkillRangImage")
        if loader then
            loader:Load(bundle_name, asset_name, TypeUnitySprite, function (sprite)
                if nil ~= sprite and nil ~= self.skill_dir_square_render and not IsNil(self.skill_dir_square_render) then
                    self.skill_dir_square_render.sprite = sprite
                end
            end) 
        end
    end

    bundle_name, asset_name = ResPath.GetNoPackPNG(self:GetSkillRangImgStr(is_cancle, "a2_bg_circular"))
    if bundle_name ~= nil and asset_name ~= nil then
        local loader = AllocResAsyncLoader(self, "SkillRangImageBg")
        if loader then
            loader:Load(bundle_name, asset_name, TypeUnitySprite, function (sprite)
                if nil ~= sprite and nil ~= self.skill_rang_render and not IsNil(self.skill_rang_render) then
                    self.skill_rang_render.sprite = sprite
                end
            end)
        end
    end
end

function Scene:TransformationSkillPos(skill_id, r_size, ro, target_pos)
    local skill_pos = nil
    if not self:MainRoleIsVaild() then
        return skill_pos
    end

    if IsNil(MainCameraFollow) then
        return skill_pos
    end

    if skill_id == nil or r_size == nil or ro == nil then
        return skill_pos
    end

    local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if cfg == nil then
        return skill_pos
    end

    local range_type = cfg.range_type
    local r1 = cfg.attack_range
    local r2 = cfg.attack_range2
    local m_x, m_y = self.main_role:GetLogicPos()

    if range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        r1 = self.rang_special_scale + r1
    else
        r1 = self.rang_special_scale + r1
        r2 = self.rang_special_scale + r2
    end

    local camera_ro = MainCameraFollow.transform.rotation.eulerAngles.y
    if range_type == ATTACK_RANGE_TYPE.SQUARE_TARGET or range_type == ATTACK_RANGE_TYPE.SQUARE_CHONG_FENG or range_type == ATTACK_RANGE_TYPE.SECTOR_ME then
        if target_pos ~= nil then
            local m_pos = self.main_role:GetDrawObj():GetPosition()
            local dir = u3dpool.v3Normalize(u3dpool.v3Sub(target_pos, m_pos))
            local roate_x = m_pos.x + r1 * dir.x
            local roate_y = m_pos.z + r1 * dir.z
            roate_x, roate_y = GameMapHelper.WorldToLogic(roate_x, roate_y - 0.5)
            skill_pos = {x = roate_x, y = roate_y}
        else
            ro = ro - camera_ro
            local m_pos = self.main_role:GetDrawObj():GetPosition()
            local r_cos_x = math.cos(math.rad(ro))
            local r_sin_y = math.sin(math.rad(ro))
            local roate_x = m_pos.x + r1 * r_cos_x
            local roate_y = m_pos.z + r1 * r_sin_y

            roate_x, roate_y = GameMapHelper.WorldToLogic(roate_x, roate_y - 0.5)
            skill_pos = {x = roate_x, y = roate_y}
        end
    elseif range_type == ATTACK_RANGE_TYPE.CIRCULAR_TARGET then
        if target_pos ~= nil then
            local m_pos = self.main_role:GetDrawObj():GetPosition()
            local m_len = u3dpool.v3Length(u3dpool.v3Sub(m_pos, target_pos), false)
            if m_len > r1 * r1 then
                local dir = u3dpool.v3Normalize(u3dpool.v3Sub(target_pos, m_pos))
                local roate_x = m_pos.x + r1 * dir.x
                local roate_y = m_pos.z + r1 * dir.z
                roate_x, roate_y = GameMapHelper.WorldToLogic(roate_x, roate_y - 0.5)
                skill_pos = {x = roate_x, y = roate_y}
            else
                skill_pos = {x = target_pos.x, y = target_pos.y}
            end
        else
            local m_pos = self.main_role:GetDrawObj():GetPosition()
            local x_len = r1 * r_size.x - 0.25
            local y_len = r1 * r_size.y - 0.25
            local roate_x, roate_y = GameMapHelper.WorldToLogic(m_pos.x + x_len, m_pos.y + y_len - 0.5)
            skill_pos = {x = roate_x, y = roate_y}
        end
    else
        skill_pos = {x = m_x, y = m_y}
    end

    return skill_pos
end


























-- 加载面板是否正在开启
function Scene:LoadingViewIsOpen()
    return self.scene_loading and self.scene_loading:IsOpen() or false
end

-- 根据场景id和采集物id获得采集物位置
function Scene:GetGatherPosCfg(scene_id, gather_id)
    local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    for i,v in ipairs(scene_cfg.gathers) do
        if gather_id == v.id then
            return v
        end
    end
end

function Scene:SetSkyBoxActive(active)
    local sky_box = self.sky_box
    if nil == sky_box then
        sky_box = sky_box or GameObject.Find("Main/SkyBox")
                        or GameObject.Find("Main/Sky Box")
                        or GameObject.Find("Main/SkyBoxs")
    end
    if sky_box ~= nil and not IsNil(sky_box.gameObject) then
        self.sky_box = sky_box
        sky_box.gameObject:SetActive(active)
    end
end

function Scene:CreateMainCityOnwerStatue()
    if self.city_statue then
        self.city_statue:UpdateStatue()
    end
end

function Scene:CreateMainCityOnwerCoupleStatue()
    if self.city_statue2 then
        self.city_statue2:UpdateStatue()
    end
end




-----------检测最近有没有采集物------------------------
-- 目前一个场景采集物没那么多，先不弄分帧
local CHECK_LATELY_GATHER_DIS = 12
local CHECK_LATELY_ONE_NUM = 10
local CHECK_LAYELY_ONE_MAX = 40
function Scene:SetIsIgnoreLatelyCheck(value)
    self.is_ignore_lately_check = value
    if value then
        self:ResetCheckLatelyGather()
        MainuiWGCtrl.Instance:SetClickFindLatelyGatherBtnShow(false)
    end
end

-- 在人物停止移动的时候，立刻检测一次
function Scene:ImmediatelyCheckLatelyGather()
    if GatherBar.Instance:IsOpen() then
        return
    end

    self:ResetCheckLatelyGather()
    self:CheckWaitGatherList()
end

function Scene:TryFindLatelyGather()
    local lately_obj_id = self.lately_gather_obj_id
    self:ResetCheckLatelyGather()
    if lately_obj_id == nil then
        return
    end

    local gather_list = self:GetGatherList()
    if gather_list == nil then
        return
    end

    local obj = gather_list[lately_obj_id]
    if obj == nil or obj:IsDeleted() then
        return
    end

    local gather_id = obj:GetGatherId()
    if gather_id == nil then
        return
    end

    local range = 2
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.XianMengzhan then
        range = 5
    elseif scene_type == SceneType.HunYanFb then--策划婚礼需求——点击已采的采集物时寻路至下一个未采的采集物，直至采集次数上限——2023.6.30
        if MarryWGData.Instance:IsCanGetTargetGatheObj(lately_obj_id) then
            return
        end
    end

    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
    local x, y = obj:GetLogicPos()
    MoveCache.SetEndType(MoveEndType.GatherById)
    MoveCache.param1 = gather_id
    GuajiWGCtrl.Instance:MoveToPos(self:GetSceneId(), x, y, range)
end

function Scene:ResetCheckLatelyGather(is_all)
    self.wait_check_gather_list = nil
    self.lately_gather_obj_id = nil
    self.need_check_lately_gather = false
    if is_all then
        self.is_ignore_lately_check = false
    end
end

-- function Scene:CheckWaitGatherList()
--     if self.wait_check_gather_list ~= nil then
--         self:StepCheckWaitGather()
--     else
--         self:ResetCheckLatelyGather()
--         local gather_list = self:GetGatherList()
--         if gather_list ~= nil then
--             self.wait_check_gather_list = {}
--             local num = 0
--             for k,v in pairs(gather_list) do
--                 if v ~= nil and not v:IsDeleted() and num < CHECK_LAYELY_ONE_MAX then
--                     local x, y = v:GetLogicPos()
--                     self.wait_check_gather_list[k] = {x = x, y = y, obj_id = k}
--                     num = num + 1
--                 end
--             end
--         end
--     end
-- end

function Scene:CheckWaitGatherList()
    if self.is_ignore_lately_check then
        return
    end

    local dis = nil
    local lately_obj_id = nil
    self:ResetCheckLatelyGather()
    local main_role = self:GetMainRole()
    if main_role == nil or main_role:IsDeleted() then
        return
    end

    local m_x, m_y = main_role:GetLogicPos()
    local gather_list = self:GetGatherList()
    local num = 0
    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic == nil then
        return
    end

    local scene_logic_check_dis = scene_logic:GetCheckLatelyGatherDis()
    local checks_gather_dis = scene_logic_check_dis > 0 and scene_logic_check_dis or CHECK_LATELY_GATHER_DIS
    if gather_list ~= nil then
        for k,v in pairs(gather_list) do
            if num < CHECK_LAYELY_ONE_MAX then
                if v ~= nil and not v:IsDeleted() and v:GetIsNeedCheckLately() then
                    local g_x, g_y = v:GetLogicPos()
                    local cur_dis = GameMath.GetDistance(g_x, g_y, m_x, m_y, false)
                    if v ~= nil and cur_dis <= checks_gather_dis then
                        if dis == nil or dis > cur_dis then
                            dis = cur_dis
                            lately_obj_id = k
                        end
                    end
                end

                num = num + 1
            else
                break
            end
        end
    end

    self.lately_gather_obj_id = lately_obj_id
    if GatherBar.Instance:IsOpen() then
        return
    end

    local can_show = false
    if scene_logic ~= nil then
        can_show = scene_logic:ShowFindLately(lately_obj_id)
    end

    MainuiWGCtrl.Instance:SetClickFindLatelyGatherBtnShow(can_show and lately_obj_id ~= nil)
end

-- function Scene:StepCheckWaitGather()
--     local main_role = self:GetMainRole()
--     if main_role == nil or main_role:IsDeleted() then
--         self:ResetCheckLatelyGather()
--         return
--     end

--     local gather_list = self:GetGatherList()
--     if gather_list == nil then
--         self:ResetCheckLatelyGather()
--         return
--     end

--     if self.wait_check_gather_list == nil then
--         self:ResetCheckLatelyGather()
--         return
--     end

--     local m_x, m_y = main_role:GetLogicPos()
--     local wait_list = {}
--     local dis = nil
--     local lately_obj_id = nil
--     local check_num = 0
--     local check_list = {}
--     for k,v in pairs(self.wait_check_gather_list) do
--         if CHECK_LATELY_ONE_NUM > check_num then
--             if gather_list[k] ~= nil and not gather_list[k]:IsDeleted() then
--                 local cur_dis = GameMath.GetDistance(v.x, v.y, m_x, m_y, false)
--                 if v ~= nil and cur_dis <= CHECK_LATELY_GATHER_DIS then
--                     if dis == nil or dis > cur_dis then
--                         dis = cur_dis
--                         lately_obj_id = k
--                     end
--                 end
--             end

--             check_list[k] = true
--             check_num = check_num + 1
--         else
--             break
--         end
--     end

--     for k,v in pairs(check_list) do
--         self.wait_check_gather_list[k] = nil
--     end

--     self:ResetCheckLatelyGather()
--     if lately_obj_id ~= nil then
--         self.lately_gather_obj_id = lately_obj_id
--     else
--         self.wait_check_gather_list = wait_list
--     end
-- end

function Scene:RemoveInVaildGatherByWaitList(obj_id)
    -- if self.wait_check_gather_list ~= nil and self.wait_check_gather_list[obj_id] ~= nil then
    --     self.wait_check_gather_list[obj_id] = nil
    -- end

    if self.lately_gather_obj_id ~= nil and self.lately_gather_obj_id == obj_id then
        self:ImmediatelyCheckLatelyGather()
    end
end

--在大跨服场景
function Scene:IsInBigCrossScene()
    return self:GetSceneType() == SceneType.XianJie_Boss
end

--在需要跨服组队的场景
function Scene:GetIsOpenCrossViewByScene(scene_type)
    scene_type = scene_type or self:GetSceneType()
    return scene_type == SceneType.XianJie_Boss
end


function Scene:OnLowMemoryCheck()
    if self.is_report_mem then
        return
    end

    local mem = collectgarbage("count") / 1000
    if mem < 250 then
        return
    end

    self.is_report_mem = true
    local t = {
        res_count = 0,                  -- 资源数
        res_pool_count = 0,             -- 资源池
        gameobj_cache_count = 0,        -- 池GO
        gameobj_pool_count = 0,         -- GO池数
        gameobj_count = 0,              -- GO数
        time_quest_count = 0,           ---定时器个数
        itemlist_change_count = 0,      -- 物品监听数量1
        item_change_count = 0,          -- 物品监听数量2
        attr_listen_count = 0,          -- 属性监听数量
        event_count = 0,                -- 事件监听数量
        lua_obj_count = 0               -- lua对象数量
    }

    ResPoolMgr:GetPoolDebugInfo(t)
    ResMgr:GetDebugGameObjCount(t)
    GlobalTimerQuest:GetQuestCount(t)
    ItemWGData.Instance:GetDebugNotifyChangeCount(t)
    RoleWGData.Instance:GetDeubgListenCount(t)
    GlobalEventSystem:GetDebugEventCount(t)
    BundleCache:GetBundleCount(t)
    GetDebugLuaObjCount(t)

    -- local str_attr = "\n资源数量: " .. t.res_count .. "\n\n资源池: " .. t.res_pool_count .. "\n\nGO池: " .. t.gameobj_cache_count
    --     .. "\n\n池中GO数量: " .. t.gameobj_pool_count .. "\n\nGO数: " .. t.gameobj_count .. "\n\n定时器个数: " .. t.time_quest_count
    --     .. "\n\nbunlde数量: " .. t.bundle_count .. "\n\n物品监听数量: " .. t.item_change_count .. "\n\n属性监听数量: " .. t.attr_listen_count
    --     .. "\n\n事件监听数量: " .. t.event_count .. "\n\nlua对象数量: " .. t.lua_obj_count .. "\n\n当前lua内存：" .. mem

    -- print_error("OnLowMemoryCheck", str_attr)
end













------------------------场景相关品质管理-----------------------------------------------------------
local SCENE_EFFECT_INFO = {
    [SCENE_QUALITY_TYPE.BLOOM] = true,
    [SCENE_QUALITY_TYPE.WATER_PLANAR] = false,
    [SCENE_QUALITY_TYPE.WAVE] = false,
    [SCENE_QUALITY_TYPE.DEPTH] = false,
}

function Scene:UpdateSceneQuality(effect_type, is_show)
    if effect_type ~= nil and SCENE_EFFECT_INFO[effect_type] ~= nil then
        SCENE_EFFECT_INFO[effect_type] = is_show
    end

    local quality_manage_level = QualityManager.Instance:GetQualityLevel()
    if QualityConfig.QualityLevel ~= 0 or quality_manage_level ~= 0 then
        SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.WATER_PLANAR] = false
        SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.BLOOM] = false

        if QualityConfig.QualityLevel > 1 then
            SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.DEPTH] = false
        end
    end

    if effect_type ~= nil then
        if effect_type == SCENE_QUALITY_TYPE.BLOOM then
            self:UpdateVolumeBloom()
        elseif effect_type == SCENE_QUALITY_TYPE.WAVE then
            self:UpdateYYWave()
        elseif effect_type == SCENE_QUALITY_TYPE.DEPTH then
            if ViewManager.Instance ~= nil and ViewManager.Instance:IsOpen(GuideModuleName.ScreenShotView) then
                SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.DEPTH] = false
            end
            
            self:UpdateVolumeDepth()
        end
    else
        self:UpdateVolumeBloom()
    end
end

----[[后处理
function Scene:InitSceneVolume()
    self.scene_volume_controller = nil
    local scene_volume
    local main_node = GameObject.Find("Main")
    if main_node and main_node.gameObject then
        scene_volume = main_node.gameObject.transform:Find("Volume")
    end

    if scene_volume then
        self.scene_volume_controller = scene_volume:GetComponent(typeof(VolumeController))
    end
    
    -- GlobalEventSystem:Fire(SceneEventType.SCENE_VOLUME_CREATE_COMPLETE)
    self:UpdateVolumeBloom()
end

function Scene:InitHandleVolumeController()
    self.handle_volume_controller = nil
    local volume_root = GameObject.Find("GameRoot/HandleVolume")
    if volume_root then
        self.handle_volume_controller = volume_root.gameObject:GetComponent(typeof(VolumeController))
    end
end

-- 辉光
function Scene:UpdateVolumeBloom()
    if not IsNil(self.scene_volume_controller) then
        local enabled = SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.BLOOM]
        self.scene_volume_controller:SetBloomEnabled(enabled)
    end
end

-- 景深
function Scene:UpdateVolumeDepth()
    if not IsNil(self.handle_volume_controller) then
        local enabled = SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.DEPTH]
        self.handle_volume_controller:SetDepthOfFieldEnabled(enabled)
    end
end

-- 径向模糊 playPos为世界坐标
function Scene:DoRadialBlurByWorldPos(playPos, riseTime, holdTime, fallTime, strength)
    if not IsNil(self.handle_volume_controller) then
        self.handle_volume_controller:DoRadialBlur(playPos, riseTime, holdTime, fallTime, strength)
    end
end

function Scene:ShieldOtherSceneObjFollowUIInDepthOfField(is_shield)
    if is_shield then
        self.hide_follow_ui_obj_list = {}
        for k, v in pairs(self.obj_list) do
            local follow_ui = v:GetFollowUi()
            if v.obj_type ~= SceneObjType.JumpPoint and follow_ui and follow_ui:GetVisiable() then
                v:ForceSetFollowUIVisible(false)
                self.hide_follow_ui_obj_list[k] = v
            end
        end
    else
        if self.hide_follow_ui_obj_list then
            for k, v in pairs(self.hide_follow_ui_obj_list) do
                v:ForceSetFollowUIVisible(true)
            end
			self.hide_follow_ui_obj_list = {}
        end
    end
end

-- 运动模糊
function Scene:UpdateVolumeMotionBlur(type)
    if IsNil(self.handle_volume_controller) then
        return
    end

    type = type or MotionBlurStage.None
    if type == MotionBlurStage.Slight then
        self.handle_volume_controller:SetNewMotionBlurDist(0.3)
        self.handle_volume_controller:SetNewMotionBlurStrength(0)
        self.handle_volume_controller:DoNewMotionBlurStrength(1.5, 0.5)
	elseif type == MotionBlurStage.SpeedUp then
        self.handle_volume_controller:SetNewMotionBlurDist(0.3)
        self.handle_volume_controller:DoNewMotionBlurStrength(2.5, 0.5)
	elseif type == MotionBlurStage.Spurt then
        self.handle_volume_controller:SetNewMotionBlurDist(0.3)
        self.handle_volume_controller:DoNewMotionBlurStrength(3.5, 0.5)
    elseif type == MotionBlurStage.SpeedCut then
        self.handle_volume_controller:SetNewMotionBlurDist(0.3)
        self.handle_volume_controller:DoNewMotionBlurStrength(0, 0.5)
    else
        self.handle_volume_controller:SetNewMotionBlurDist(0)
        self.handle_volume_controller:SetNewMotionBlurStrength(0)
	end

    local enbale = type ~= MotionBlurStage.None
    self.handle_volume_controller:SetNewMotionBlurEnabled(enbale)
end


-- 水波纹特效
function Scene:GetYYWaveEffect()
    return self.yy_wave_effect
end

function Scene:UpdateYYWave()
    if IsNil(MainCamera) then
        return
    end

    if IsNil(self.yy_wave_effect) then
        return
    end

    self.yy_wave_effect.enabled = SCENE_EFFECT_INFO[SCENE_QUALITY_TYPE.WAVE]
end

-- 全屏大界面默认开启，关掉场景PP(加载全屏和创角全屏不做计数)
function Scene:ChangeSceneVolumeState(is_show)
    if self.scene_show_volume == is_show then
        return
    end

    if self.urp_scene_camera_data then
        self.scene_show_volume = is_show
        self.urp_scene_camera_data.renderPostProcessing = is_show
    end

    if not IsNil(self.scene_volume_controller) then
        self.scene_volume_controller:SetVolumeEnable(is_show)
    end
end
-- 后处理 end ]]

function Scene:SetHeroLightState(is_active)
    if IsNil(HeroLight) then
        local main_node = GameObject.Find("Main")
        if main_node and main_node.gameObject then
            HeroLight = main_node.gameObject.transform:Find("Hero light")
            if not IsNil(HeroLight) then
                self.hero_light_active = HeroLight.gameObject.activeInHierarchy
            end
        end
    end

    if not IsNil(HeroLight) and self.hero_light_active ~= is_active then
        HeroLight.gameObject:SetActive(is_active)
        self.hero_light_active = is_active
    end
end

----[[切换时辰
-- 设置场景的白天黑夜组件
function Scene:SetSceneDayNight()
    local day_night_obj
    local main_node = GameObject.Find("Main")
    if main_node and main_node.gameObject then
        day_night_obj = main_node.gameObject.transform:Find("DayNight")
    end

    if not IsNil(day_night_obj) then
        self.scene_day_night = day_night_obj:GetComponent(typeof(DayNight.DayNightFeature))
    end

    if not self.scene_day_night_moment_index then
        self.scene_day_night_moment_index = 0
    else
        self:ChangeSceneDayNightMoment(self.scene_day_night_moment_index)
    end
end

-- 切换当前的时辰
function Scene:ChangeSceneDayNightMoment(moment_index)
    if IsNil(self.scene_day_night) then
        return
    end

    if moment_index < 0 or moment_index > 3 then
        return
    end

    if self.scene_day_night.currentMomentIndex == moment_index then
        return
    end

    self.scene_day_night_moment_index = moment_index
    self.scene_day_night:SetMoment(moment_index)
end
-- 切换时辰 end ]]



-- [[场景展示切换
function Scene:SetSceneNodeData(asset_name, other_data)
    if IsEmptyTable(other_data) then
        return
    end

    local scene = SceneManager.GetSceneByName(asset_name)
    if not IsNil(scene) and scene:IsValid() then
        local root_objects = scene:GetRootGameObjects()
        local scene_main_node = nil
        for i = 0, root_objects.Length - 1 do
            if root_objects[i].name == "Main" then
                scene_main_node = root_objects[i]
                break
            end
        end

        if not IsNil(scene_main_node) then
            if not IsEmptyTable(other_data) then
                for k,v in pairs(other_data) do
                    if k == "config_index" then
                        self:SetUISceneControllerConfigIndexBySceneAssetName(asset_name, v)
                    end
                end
            end
        end
    end
end

-- 场景节点显示隐藏控制
-- @param bundle_name: 场景bundle名称
-- @param asset_name: 场景asset名称  
-- @param is_show: 是否显示
-- @param handle_colliders: 是否处理Colliders节点（主场景和副本场景为true，UI场景为false）
function Scene:SetSceneIsShow(bundle_name, asset_name, is_show, handle_colliders)
    -- print_error("场景节点显示隐藏控制 SetSceneIsShow", bundle_name, asset_name, is_show, handle_colliders)
    local scene = SceneManager.GetSceneByName(asset_name)
    if not IsNil(scene) and scene:IsValid() then
        local root_objects = scene:GetRootGameObjects()
        local scene_main_node = nil
        local colliders_node = nil
        for i = 0, root_objects.Length - 1 do
            if root_objects[i].name == "Main" then
                scene_main_node = root_objects[i]
            end

            if root_objects[i].name == "Colliders" then
                colliders_node = root_objects[i]
            end
        end

        -- 处理Main节点
        if not IsNil(scene_main_node) then
            scene_main_node:SetActive(is_show)
            local comp = scene_main_node:GetComponent(typeof(UISceneController))
            if comp and not self.ui_scene_controller_list[asset_name] then
                self.ui_scene_controller_list[asset_name] = comp
            end
        end

        -- 只有行走场景间切换需要处理Colliders节点，UI场景和行走场景切换不需要处理，以免玩家掉落深渊
        if handle_colliders then
            if not IsNil(colliders_node) then
                colliders_node:SetActive(is_show)
            end

            -- 行走场景间切换时，同步修改当前使用的场景小地图相机
            if is_show and not IsNil(scene_main_node) then
                local mini_camera = scene_main_node.transform:Find("MinimapCamera")
                if not IsNil(mini_camera) then
                    ScenePreload.mini_camera = mini_camera:GetComponent(typeof(TopViewMapCamera))
                end
            end
        end

        if is_show then
            Scene.CurShowSceneBundle = bundle_name
            Scene.CurShowSceneAsset = asset_name
            -- 检查场景是否已加载再设置为活动场景
            if scene.isLoaded then
                -- 确保场景可以设置为活动场景
                local success, error_msg = pcall(function()
                    SceneManager.SetActiveScene(scene)
                end)
                if not success then
                    print_error("SetSceneIsShow: 设置活动场景失败", asset_name, error_msg)
                end
            else
                print_error("SetSceneIsShow: 场景未加载，无法设置为活动场景", asset_name, "所在场景id:", Self:GetSceneId(), "玩家等级:", RoleWGData.Instance:GetRoleLevel())
            end
        end
    end
end

function Scene:GetSceneMiniMapCamera()
    return ScenePreload.mini_camera
end

function Scene:ClearUISceneControllerList()
    self.ui_scene_controller_list = {}
    self.ui_scene_config_index = {}
end

-- 设置UI场景控制 配置index by ui_scene_type
function Scene:SetUISceneControllerConfigIndexByType(ui_scene_type, cfg_index)
    local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene or {}
    local us_cfg = cfg[ui_scene_type]
    if not us_cfg then
        return
    end

    self:SetUISceneControllerConfigIndexBySceneAssetName(us_cfg.asset_name, cfg_index)
end

-- 设置UI场景控制 配置index by asset_name
function Scene:SetUISceneControllerConfigIndexBySceneAssetName(asset_name, cfg_index)
    if self.ui_scene_config_index[asset_name] == cfg_index then
        return
    end

    local comp = self.ui_scene_controller_list[asset_name]
    if comp then
        self.ui_scene_config_index[asset_name] = cfg_index
        local success, error_msg = pcall(function()
            comp:SetConfiguration(cfg_index)
        end)

        if not success then
            print_error("设置UI场景配置异常: ", asset_name, cfg_index, error_msg)
        end
    end
end

-- 可行走场景间的切换调用
function Scene:ChangeSceneShow(bundle_name, asset_name, is_show, other_data)
    if bundle_name == nil or asset_name == nil then return end
    if is_show and asset_name == Scene.CurShowSceneAsset then
        self:SetSceneNodeData(asset_name, other_data)
        return
    end

    -- 判断是否需要处理Colliders节点（主场景和预加载副本场景需要，UI场景不需要）
    local need_handle_colliders = self:IsMainOrFuBenScene(bundle_name, asset_name)

    if is_show then
        -- 隐藏当前显示的场景，如果当前场景是主场景或副本场景，也要处理Colliders
        local current_need_handle_colliders = self:IsMainOrFuBenScene(Scene.CurShowSceneBundle, Scene.CurShowSceneAsset)
        self:SetSceneIsShow(Scene.CurShowSceneBundle, Scene.CurShowSceneAsset, false, current_need_handle_colliders)

        -- 如果前一个场景是副本场景，清除副本场景的显示状态
        local scene_info = self:FindPreloadedFuBenScene(Scene.CurShowSceneBundle, Scene.CurShowSceneAsset)
        if scene_info then
            scene_info.is_showing = false
            self.current_showing_fuben_scene = nil
        end
    end

    self:SetSceneIsShow(bundle_name, asset_name, is_show, need_handle_colliders)
    self:SetSceneNodeData(asset_name, other_data)
    
    -- 如果是预加载的副本场景，更新状态
    local scene_info = self:FindPreloadedFuBenScene(bundle_name, asset_name)
    if scene_info then
        scene_info.is_showing = is_show
        if is_show then
            self.current_showing_fuben_scene = {bundle_name = bundle_name, asset_name = asset_name}
        end
    end

    -- 主场景显示，清除预加载副本场景的显示状态（处理情况：处于副本场景，打开UI场景，然后过渡到主场景）
    if is_show and bundle_name == ScenePreload.main_scene_bundle_name and asset_name == ScenePreload.main_scene_asset_name then
        for k,v in pairs(self.preloaded_fuben_scene_info) do
            if v.is_showing then
                local scene_info = self:FindPreloadedFuBenScene(v.bundle_name, v.asset_name)
                if scene_info then
                    scene_info.is_showing = false
                    self.current_showing_fuben_scene = nil
                end
            end
        end
    end
end

-- 显示当前可行走场景（用于隐藏UI场景后恢复可行走场景显示）
function Scene:ResetWalkableSceneShow()
    local bundle_name, asset_name = self:GetCurrentWalkableScene()
    
    if bundle_name and asset_name then
        self:ChangeSceneShow(bundle_name, asset_name, true, nil)
    end
end

-- 隐藏当前可行走场景的Main节点（用于显示UI场景时，保持Colliders节点避免玩家掉落）
function Scene:HideWalkableSceneShow()
    local bundle_name, asset_name = self:GetCurrentWalkableScene()
    
    if bundle_name and asset_name then
        self:SetSceneIsShow(bundle_name, asset_name, false, false)
    end
end

-- UI场景专用切换方法（只操作Main节点，不影响Colliders节点）
function Scene:ChangeUISceneShow(bundle_name, asset_name, is_show, other_data)
	if bundle_name == nil or asset_name == nil then return end
	if is_show and asset_name == Scene.CurShowSceneAsset then
		self:SetSceneNodeData(asset_name, other_data)
		return
	end

	if is_show then
		-- UI场景切换时，当前场景的Colliders节点保持不变
		self:SetSceneIsShow(Scene.CurShowSceneBundle, Scene.CurShowSceneAsset, false, false)
	end

	-- UI场景只处理Main节点，不处理Colliders节点
	self:SetSceneIsShow(bundle_name, asset_name, is_show, false)
	self:SetSceneNodeData(asset_name, other_data)
end

--[[ui场景材质切换]]

-- 通过ui_scene_index获取asset_name
function Scene:GetAssetNameByUISceneIndex(ui_scene_index)
    if not ui_scene_index then
        print_error("[Scene] GetAssetNameByUISceneIndex: ui_scene_index is nil")
        return nil
    end
    
    return self.ui_scene_index_to_asset_name[ui_scene_index]
end

-- 通用材质设置函数
function Scene:SetMaterialToRenderer(renderer, material)
    if not renderer then
        print_error("[Scene] SetMaterialToRenderer: renderer is nil")
        return false
    end
    
    if not material then
        print_error("[Scene] SetMaterialToRenderer: material is nil")
        return false
    end
    
    local material_array = {}
    table.insert(material_array, material)
    renderer.sharedMaterials = material_array
    return true
end

-- 通用特效设置函数
function Scene:SetEffectToGameObject(game_object_attach, effect_bundle_name, effect_asset_name)
    if not game_object_attach then
        print_error("[Scene] SetEffectToGameObject: game_object_attach is nil")
        return false
    end

    if not game_object_attach.gameObject then
        print_error("[Scene] SetEffectToGameObject: game_object_attach.gameObject is nil")
        return false
    end
    
    if not effect_bundle_name or effect_bundle_name == "" then
        print_error("[Scene] SetEffectToGameObject: effect_bundle_name is invalid")
        return false
    end
    
    if not effect_asset_name or effect_asset_name == "" then
        print_error("[Scene] SetEffectToGameObject: effect_asset_name is invalid")
        return false
    end
    
    game_object_attach.BundleName = effect_bundle_name
    game_object_attach.AssetName = effect_asset_name
    game_object_attach.gameObject:SetActive(false)
    game_object_attach.gameObject:SetActive(true)
    return true
end

-- 切换UI场景控制器配置，用于改变UI场景的材质、特效等显示效果
-- @param ui_scene_index number UI场景索引，用于标识具体的UI场景类型，通过此索引可获取对应的场景资源名称
-- @param cfg_index number 配置索引，用于设置场景控制器的具体配置项，影响角色灯光数据等场景显示参数
-- @param index number UI场景控制器配置表的索引，对应ui_scene_controller_cfg表中的具体配置项，包含水面材质、背景材质、角色数据、特效等配置信息
function Scene:ChangeUISceneController(ui_scene_index, cfg_index, index)
    -- 参数有效性检查
    if not ui_scene_index then
        print_error("[Scene] ChangeUISceneController: ui_scene_index is nil")
        return
    end
    
    if not index then
        print_error("[Scene] ChangeUISceneController: index is nil")
        return
    end
    
    local ui_scene_cfg = self.ui_scene_controller_cfg[index]
    if not ui_scene_cfg then
        print_error("[Scene] ChangeUISceneController: ui_scene_cfg not found for index: ", tostring(index))
        return
    end
    
    -- 水面材质
    if ui_scene_cfg.water_mat_name and ui_scene_cfg.water_mat_name ~= "" then
        ResPoolMgr:GetMaterial("misc/material", ui_scene_cfg.water_mat_name, BindTool.Bind(self.OnLoadCompleteWater, self, ui_scene_index, cfg_index, index), false)
    end
    
    -- 背景板材质
    if ui_scene_cfg.bg_panel_mat_name and ui_scene_cfg.bg_panel_mat_name ~= "" then
        ResPoolMgr:GetMaterial("misc/material", ui_scene_cfg.bg_panel_mat_name, BindTool.Bind(self.OnLoadCompleteBgPanel, self, ui_scene_index, cfg_index, index), false)
    end
    
    -- 角色灯光数据
    if ui_scene_cfg.character_asset_name and ui_scene_cfg.character_asset_name ~= "" then
        ResPoolMgr:GetCharacterData("misc/characterdata", ui_scene_cfg.character_asset_name, BindTool.Bind(self.OnLoadCompleteCharacterData, self, ui_scene_index, cfg_index, index), false)
    end

    -- 通用特效
    if ui_scene_cfg.effect_common_bundle_name and ui_scene_cfg.effect_common_bundle_name ~= "" and 
       ui_scene_cfg.effect_common_asset_name and ui_scene_cfg.effect_common_asset_name ~= "" then
        self:ChangeUISceneEffect(ui_scene_index, cfg_index, index, ui_scene_cfg.effect_common_bundle_name, ui_scene_cfg.effect_common_asset_name)
    end
    
    -- 通用烟雾特效
    if ui_scene_cfg.effect_bundle_name and ui_scene_cfg.effect_bundle_name ~= "" and 
       ui_scene_cfg.effect_asset_name and ui_scene_cfg.effect_asset_name ~= "" then
        self:ChangeUISceneEffectSmoke(ui_scene_index, cfg_index, index, ui_scene_cfg.effect_bundle_name, ui_scene_cfg.effect_asset_name)
    end
end

function Scene:OnLoadCompleteWater(ui_scene_index, cfg_index, index, material)
    if not material then
        print_error("[Scene] OnLoadCompleteWater: material is nil")
        return
    end
    
    local asset_name = self:GetAssetNameByUISceneIndex(ui_scene_index)
    if asset_name then
        self:ChangeUISceneWaterMaterial(asset_name, cfg_index, index, material)
    end
end

function Scene:ChangeUISceneWaterMaterial(asset_name, cfg_index, index, material)
    if not asset_name then
        print_error("[Scene] ChangeUISceneWaterMaterial: asset_name is nil")
        return
    end
    
    local comp = self.ui_scene_controller_list[asset_name]
    if IsNil(comp) then
        print_error("[Scene] ChangeUISceneWaterMaterial: component not found for asset_name: ", tostring(cfg_index), tostring(index))
        return
    end
    
    local mesh_renderer = comp:GetWaterCommonMeahRender()
    if mesh_renderer then
        self:SetMaterialToRenderer(mesh_renderer, material)
    else
        print_error("[Scene] ChangeUISceneWaterMaterial: mesh_renderer is nil for asset_name: ", tostring(cfg_index), tostring(index))
    end
end

function Scene:OnLoadCompleteBgPanel(ui_scene_index, cfg_index, index, material)
    if not material then
        print_error("[Scene] OnLoadCompleteBgPanel: material is nil")
        return
    end
    
    local asset_name = self:GetAssetNameByUISceneIndex(ui_scene_index)
    if asset_name then
        self:ChangeUISceneBgPanelMaterial(asset_name, cfg_index, index, material)
    end
end

function Scene:ChangeUISceneBgPanelMaterial(asset_name, cfg_index, index, material)
    if not asset_name then
        print_error("[Scene] ChangeUISceneBgPanelMaterial: asset_name is nil")
        return
    end
    
    local comp = self.ui_scene_controller_list[asset_name]
    if IsNil(comp) then
        print_error("[Scene] ChangeUISceneBgPanelMaterial: component not found for asset_name: ", tostring(cfg_index), tostring(index))
        return
    end
    
    local mesh_renderer = comp:GetBgPanelCommonMeahRender()
    if mesh_renderer then
        self:SetMaterialToRenderer(mesh_renderer, material)
    else
        print_error("[Scene] ChangeUISceneBgPanelMaterial: mesh_renderer is nil for asset_name: ", tostring(cfg_index), tostring(index))
    end
end

function Scene:ChangeUISceneEffect(ui_scene_index, cfg_index, index, effect_bundle_name, effect_asset_name)
    local asset_name = self:GetAssetNameByUISceneIndex(ui_scene_index)
    if not asset_name then
        return
    end

    local comp = self.ui_scene_controller_list[asset_name]
    if IsNil(comp) then
        print_error("[Scene] ChangeUISceneEffect: component not found for asset_name: ", tostring(cfg_index), tostring(index), asset_name)
        return
    end
    
    local game_object_attach = comp:GetEffectCommonGameObjectAttach()
    if game_object_attach then
        self:SetEffectToGameObject(game_object_attach, effect_bundle_name, effect_asset_name)
    else
        print_error("[Scene] ChangeUISceneEffect: game_object_attach is nil for asset_name: ", tostring(cfg_index), tostring(index), tostring(comp.currentConfigIndex))
    end
end

function Scene:ChangeUISceneEffectSmoke(ui_scene_index, cfg_index, index, effect_bundle_name, effect_asset_name)
    local asset_name = self:GetAssetNameByUISceneIndex(ui_scene_index)
    if not asset_name then
        return
    end

    local comp = self.ui_scene_controller_list[asset_name]
    if IsNil(comp) then
        print_error("[Scene] ChangeUISceneEffectSmoke: component not found for asset_name: ", tostring(cfg_index), tostring(index), asset_name)
        return
    end
    
    local game_object_attach = comp:GetEffectCommonSmokeGameObjectAttach()
    if game_object_attach then
        self:SetEffectToGameObject(game_object_attach, effect_bundle_name, effect_asset_name)
    else
        print_error("[Scene] ChangeUISceneEffectSmoke: game_object_attach is nil for asset_name: ", tostring(cfg_index), tostring(index), tostring(comp.currentConfigIndex))
    end
end

function Scene:OnLoadCompleteCharacterData(ui_scene_index, cfg_index, index, character_data)
    if not character_data then
        print_error("[Scene] OnLoadCompleteCharacterData: character_data is nil")
        return
    end
    
    local asset_name = self:GetAssetNameByUISceneIndex(ui_scene_index)
    if asset_name then
        self:ChangeUISceneCharacterData(asset_name, cfg_index, index, character_data)
    end
end

function Scene:ChangeUISceneCharacterData(asset_name, cfg_index, index, character_data)
    if not asset_name then
        print_error("[Scene] ChangeUISceneCharacterData: asset_name is nil")
        return
    end
    
    if not character_data then
        print_error("[Scene] ChangeUISceneCharacterData: character_data is nil")
        return
    end

    local comp = self.ui_scene_controller_list[asset_name]
    if IsNil(comp) then
        print_error("[Scene] ChangeUISceneCharacterData: component not found for asset_name: ", tostring(cfg_index), tostring(index))
        return
    end
    
    local configuration = comp:GetSceneConfiguration(cfg_index)
    if configuration then
        configuration.characterData = character_data
    else
        print_error("[Scene] ChangeUISceneCharacterData: configuration not found for cfg_index: ", tostring(cfg_index), tostring(index))
    end
end

function Scene:GetUIEffectsTransform(asset_name)
    local comp = self.ui_scene_controller_list[asset_name]
    if comp then
        return comp:GetEffectsTransform()
    end

    return nil
end
--]]

-- 修改UI场景摄像机 作为 UI背景景深摄像机
function Scene:ChangeBGBlurCamera()
    local camera = nil
    if Scene.CurShowSceneAsset ~= ScenePreload.main_scene_asset_name then
        local scene = SceneManager.GetSceneByName(Scene.CurShowSceneAsset)
        if not IsNil(scene) and scene:IsValid() then
            local root_objects = scene:GetRootGameObjects()
            local scene_main_node = nil
            for i = 0, root_objects.Length - 1 do
                if root_objects[i].name == "Main" then
                    scene_main_node = root_objects[i]
                    break
                end
            end

            if not IsNil(scene_main_node) then
                local camera_trans = scene_main_node.transform:Find("Camera")
                if not IsNil(camera_trans) then
                    camera = camera_trans:GetComponent(typeof(UnityEngine.Camera))
                end
            end
        end
    end

    MainCameraSnapshot:SetShotCamera(camera)
end


-- 场景展示切换 end]]

-- 预加载副本场景状态管理
-- 添加预加载副本场景到列表
function Scene:AddPreloadedFuBenScene(bundle_name, asset_name, priority)
	-- 创建场景信息对象
	local scene_info = {
		bundle_name = bundle_name,
		asset_name = asset_name,
		priority = priority or 1,
		is_showing = false,
	}
	
	-- 添加到详细信息列表
	table.insert(self.preloaded_fuben_scene_info, scene_info)
	
	-- 创建快速查找结构
	if not self.preloaded_fuben_scenes[bundle_name] then
		self.preloaded_fuben_scenes[bundle_name] = {}
	end
	self.preloaded_fuben_scenes[bundle_name][asset_name] = scene_info
end

-- 查找预加载副本场景
function Scene:FindPreloadedFuBenScene(bundle_name, asset_name)
	if self.preloaded_fuben_scenes[bundle_name] then
		return self.preloaded_fuben_scenes[bundle_name][asset_name]
	end
	return nil
end

-- 移除预加载副本场景
function Scene:RemovePreloadedFuBenScene(bundle_name, asset_name)
	-- 从快速查找结构中移除
	if self.preloaded_fuben_scenes[bundle_name] then
		self.preloaded_fuben_scenes[bundle_name][asset_name] = nil
		-- 如果该bundle_name下没有其他场景了，删除bundle_name条目
		if next(self.preloaded_fuben_scenes[bundle_name]) == nil then
			self.preloaded_fuben_scenes[bundle_name] = nil
		end
	end
	
	-- 从详细信息列表中移除
	for i = #self.preloaded_fuben_scene_info, 1, -1 do
		local scene_info = self.preloaded_fuben_scene_info[i]
		if scene_info.bundle_name == bundle_name and scene_info.asset_name == asset_name then
			table.remove(self.preloaded_fuben_scene_info, i)
			break
		end
	end
	
	-- 如果移除的是当前显示的场景，清空当前显示标记
	if self.current_showing_fuben_scene and 
	   self.current_showing_fuben_scene.bundle_name == bundle_name and
	   self.current_showing_fuben_scene.asset_name == asset_name then
		self.current_showing_fuben_scene = nil
	end
end

-- 判断场景类型，决定是否需要处理Colliders节点
function Scene:IsMainOrFuBenScene(bundle_name, asset_name)
	-- 主场景
	if bundle_name == ScenePreload.main_scene_bundle_name and asset_name == ScenePreload.main_scene_asset_name then
		return true
	end
	
	-- 预加载的副本场景
	local scene_info = self:FindPreloadedFuBenScene(bundle_name, asset_name)
	if scene_info then
		return true
	end
	
	return false
end

function Scene:ClearPreloadedFuBenSceneInfo()
	self.preloaded_fuben_scenes = {}
	self.preloaded_fuben_scene_info = {}
	self.current_showing_fuben_scene = nil
end

-- 获取指定的预加载副本场景信息
function Scene:GetPreloadedFuBenSceneInfo(bundle_name, asset_name)
	if bundle_name and asset_name then
		local scene_info = self:FindPreloadedFuBenScene(bundle_name, asset_name)
		return scene_info and scene_info.bundle_name, scene_info and scene_info.asset_name
	end

	return nil, nil
end

-- 检查是否有任何预加载的副本场景
function Scene:HasAnyPreloadedFuBenScene()
	return #self.preloaded_fuben_scene_info > 0
end

-- 检查指定场景是否已预加载
function Scene:IsPreloadedFuBenSceneLoaded(bundle_name, asset_name)
	if bundle_name and asset_name then
		return self:FindPreloadedFuBenScene(bundle_name, asset_name) ~= nil
	end

    return false
end

-- 检查是否正在显示任何预加载的副本场景
function Scene:IsShowingPreloadedFuBenScene()
	return self.current_showing_fuben_scene ~= nil
end

-- 获取当前显示的预加载副本场景
function Scene:GetCurrentShowingFuBenScene()
	return self.current_showing_fuben_scene
end

-- 获取当前可行走场景信息（主场景或预加载的副本场景）
function Scene:GetCurrentWalkableScene()
	if self:IsShowingPreloadedFuBenScene() then
		-- 当前显示的是预加载的副本场景
		local current_scene = self:GetCurrentShowingFuBenScene()
		return current_scene.bundle_name, current_scene.asset_name
	else
		-- 当前显示的是主场景
		return ScenePreload.main_scene_bundle_name, ScenePreload.main_scene_asset_name
	end
end

-- 显示指定的预加载副本场景
function Scene:ShowPreloadedFuBenScene(bundle_name, asset_name)
	local scene_info = self:FindPreloadedFuBenScene(bundle_name, asset_name)
	if not scene_info then
		return false
	end
	
	-- 如果已经在显示这个场景
	if self.current_showing_fuben_scene and 
	   self.current_showing_fuben_scene.bundle_name == bundle_name and
	   self.current_showing_fuben_scene.asset_name == asset_name then
		return true
	end
	
	self:ChangeSceneShow(bundle_name, asset_name, true, nil)
	
	return true
end

-- 隐藏所有预加载的副本场景，显示主场景
function Scene:HideAllPreloadedFuBenScenes()
	if not self:IsShowingPreloadedFuBenScene() then
		return false
	end
	
	self:ChangeSceneShow(ScenePreload.main_scene_bundle_name, ScenePreload.main_scene_asset_name, true, nil)
	
	return true
end

-- 隐藏指定的预加载副本场景
function Scene:HidePreloadedFuBenScene(bundle_name, asset_name)
	local scene_info = self:FindPreloadedFuBenScene(bundle_name, asset_name)
	if not scene_info or not scene_info.is_showing then
		return false
	end
	
	-- 如果这是当前显示的场景，切换回主场景
	if self.current_showing_fuben_scene and 
	   self.current_showing_fuben_scene.bundle_name == bundle_name and
	   self.current_showing_fuben_scene.asset_name == asset_name then
		self:ChangeSceneShow(ScenePreload.main_scene_bundle_name, ScenePreload.main_scene_asset_name, true, nil)
	else
		-- 只是隐藏场景，不切换到主场景
		self:ChangeSceneShow(bundle_name, asset_name, false, nil)
	end
	
	return true
end

-- 场景固定物的显隐设置
function Scene:TrySetSceneStaticObjActive(hide_group, is_active)
    local scene_asset_name = ScenePreload.main_scene_asset_name
    local scene = SceneManager.GetSceneByName(scene_asset_name)
    if not IsNil(scene) and scene:IsValid() then
        local root_objects = scene:GetRootGameObjects()
        local scene_main_node = nil
        for i = 0, root_objects.Length - 1 do
            if root_objects[i].name == "Main" then
                scene_main_node = root_objects[i]
                break
            end
        end

        if not IsNil(scene_main_node) then
            local comp = scene_main_node:GetComponent(typeof(SceneForcedOperateList))
            if comp then
                comp:SetObjectActive(hide_group, is_active)
                if not is_active then
                    self.scene_static_obj_hide_cache = {[scene_asset_name] = hide_group}
                end
            end
        end
    end
end

function Scene:GetUISceneCfg(type)
    if self.ui_scene_cfg then
        return self.ui_scene_cfg[type]
    end
    return nil
end


function Scene:GetSyncAcitonNormalized(sync_index)
	return self.sync_action_normalized[sync_index] or 0
end

-- [[元神变身修改 start
function Scene:GetSpiritChangePlayer()
    if self.spirit_change_player == nil or IsNil(self.spirit_change_player) then
        local game_root = GameObject.Find("GameRoot")
        if game_root then
            self.spirit_change_player = game_root.gameObject:GetComponent(typeof(SpiritChangePlayer))
        end
    end

    return self.spirit_change_player
end

-- 设置领域特效
function Scene:SetScreenEffectZoneEffect(trans)
    local player = self:GetSpiritChangePlayer()
    if player == nil or IsNil(player) then
        return
    end

    player:SetScreenEffectZoneEffect(trans)
end

-- 展示领域特效
function Scene:PlayScreenEffectZone(role, fade_in, fade_out)
    local player = self:GetSpiritChangePlayer()
    if player == nil or IsNil(player) then
        return
    end

    if rawget(getmetatable(player), "CanPlayScreenEffectZone") ~= nil then
        player:PlayScreenEffectZone(role, fade_in, fade_out)
    end
end

-- 关闭领域特效
function Scene:StopScreenEffectZone()
    local player = self:GetSpiritChangePlayer()
    if player == nil or IsNil(player) then
        return
    end

    player:ContinueScreenEffectZone()
end

-- 展示望气
function Scene:ShowWangQi(trans, fade_in)
    local player = self:GetSpiritChangePlayer()
    if player == nil or IsNil(player) then
        return
    end

    self.is_show_wangqi = true
    GlobalEventSystem:Fire(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.is_show_wangqi)
    player:ShowWangQi(trans, fade_in)
end

-- 关闭望气
function Scene:CloseWangQi(fade_out)
    local player = self:GetSpiritChangePlayer()
    if player == nil or IsNil(player) then
        return
    end

    if not self.is_show_wangqi then
        return
    end

    self.is_show_wangqi = false
    GlobalEventSystem:Fire(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.is_show_wangqi)
    player:CloseWangQi(fade_out)
end

function Scene:IsEnterWangQiStatus()
    return self.is_show_wangqi
end
-- 元神变身修改 end]]