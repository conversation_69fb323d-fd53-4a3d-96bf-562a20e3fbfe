-- 新服首充优势提醒
NewServerRechargeTip = NewServerRechargeTip or BaseClass(BaseRender)

local floor = math.floor
local modf = math.modf
local format = string.format

function NewServerRechargeTip:DoLoad(parent)
	self:LoadAsset("uis/view/main_ui_prefab", "layout_new_server_recharge_tip", parent.transform)
end

function NewServerRechargeTip:__delete()
	if self.eq_model then
		self.eq_model:DeleteMe()
		self.eq_model = nil
	end

	--CountDownManager.Instance:RemoveCountDown("new_server_recharge_tip_count_down")
end

function NewServerRechargeTip:LoadCallBack()
	self:InitParam()
	self:InitListener()
	self:InitModel()
end

function NewServerRechargeTip:InitParam()
	self.eq_model = nil
end

function NewServerRechargeTip:InitListener()
	XUI.AddClickEventListener(self.node_list.click_img, BindTool.Bind(self.OnClickGetBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.Close, self))
end

function NewServerRechargeTip:InitModel()
	if not self.eq_model then
        self.eq_model = OperationActRender.New(self.node_list["model_root"])
        self.eq_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
end

function NewServerRechargeTip:FlushModel()
	local show_cfg = ServerActivityWGData.Instance:GetSCAnimCfg(1, 1)
	if IsEmptyTable(show_cfg) then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if show_cfg.model_show_itemid ~= 0 and show_cfg.model_show_itemid ~= "" then
		local split_list = string.split(show_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_cfg.model_show_itemid
		end
	end
	display_data.bundle_name = show_cfg["model_bundle_name"]
    display_data.asset_name = show_cfg["model_asset_name"]
    local model_show_type = tonumber(show_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = show_cfg["model_show_itemid"]})
    end

    self.eq_model:SetData(display_data)
end

function NewServerRechargeTip:OnFlush()
	--self:FlushCountDown()
	self:FlushModel()
end

function NewServerRechargeTip:OnClickGetBtn()
	ViewManager.Instance:Open(GuideModuleName.FirstRechargeView)
end

function NewServerRechargeTip:Close()
	self:SetVisible(false)
end

function NewServerRechargeTip:FlushCountDown()
	CountDownManager.Instance:RemoveCountDown("new_server_recharge_tip_count_down")
	local online_time = TimeWGCtrl.Instance:GetOnlineTimes()
	local continue_time = 30 * 60
	local count_down_time = continue_time - online_time 

	if count_down_time > 0 then
		self:UpdateCountDown(0, count_down_time)
		CountDownManager.Instance:AddCountDown(
			"new_server_recharge_tip_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			function ()
			self:SetVisible(false)
			end,
			nil,
			count_down_time,
			0.02
		)
	else
		self:SetVisible(false)
	end
end

function NewServerRechargeTip:UpdateCountDown(elapse_time, total_time)
	local time = total_time - elapse_time
	local min = floor((time / 60) % 60)
	local s = floor(time % 60)
	local ss,ms = modf(time)
	ms = floor(ms * 100)
	self.node_list.count_down_lbl.text.text = string.format(Language.Recharge.RechargeTipCountDown, min, s, ms)
end