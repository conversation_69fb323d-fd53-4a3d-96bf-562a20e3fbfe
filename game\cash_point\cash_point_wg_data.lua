CashPointWGData = CashPointWGData or BaseClass()

function CashPointWGData:__init()
	if CashPointWGData.Instance ~= nil then
		<PERSON>rrorL<PERSON>("[CashPointWGData] attempt to create singleton twice!")
		return
	end

	CashPointWGData.Instance = self
end

function CashPointWGData:__delete()
	CashPointWGData.Instance = nil
end

function CashPointWGData:GetJumpPath()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	return other_cfg.virtual_cash_point_open_panel
end
