LayoutZeroBuyWGData = LayoutZeroBuyWGData or BaseClass()

function LayoutZeroBuyWGData:__init()
	if LayoutZeroBuyWGData.Instance then
		ErrorLog("[LayoutZeroBuyWGData] attempt to create singleton twice!")
		return
	end

	LayoutZeroBuyWGData.Instance = self
	self.zerobuy_cfg = ConfigManager.Instance:GetAutoConfig("zerobuy_cfg_auto")
	self.zerobuy_reward_cfg = ListToMap(self.zerobuy_cfg.reward, "ID")
	self.zero_buy_info_list = {}
	self.zero_buy_log_list = {}
	RemindManager.Instance:Register(RemindName.ZeroBuy, BindTool.Bind(self.ZeroBuyRmind, self))
	self.is_frist_open = 1
	self.special_flag = false
	self.has_open_zero_buy_flag = false
end

function LayoutZeroBuyWGData:__delete()
	LayoutZeroBuyWGData.Instance = nil
	self.zerobuy_cfg = nil
	self.is_frist_open = nil
	self.special_flag = nil
	RemindManager.Instance:UnRegister(RemindName.ZeroBuy)
	self.has_open_zero_buy_flag = false
end

function LayoutZeroBuyWGData:SetProtocol(protocol)
	self.zero_buy_info_list.open_flag = protocol.open_flag
	self.zero_buy_info_list.info_list = protocol.info_list
end

function LayoutZeroBuyWGData:GetZeroBuyCfgOpenDay(seq)
	return self.zerobuy_reward_cfg[seq]
end

function LayoutZeroBuyWGData:GetZeroBuyOtherCfg()
	return self.zerobuy_cfg.other[1]
end

function LayoutZeroBuyWGData:GetZeroBuyData()
	local data_lsit = {}
	local num = 0
	if not self.zero_buy_info_list.info_list then
		return data_lsit, num
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	for i,v in ipairs(self.zero_buy_info_list.info_list) do
		local temp_list = {}
		if v.state ~= 4 then --没被领取过
			local cfg = self:GetZeroBuyCfgOpenDay(v.seq)
			if cfg and  role_level >= cfg.show_level then
				temp_list.cfg = cfg
				temp_list.state = v.state
                temp_list.new_flag = v.new_flag
                temp_list.money_type = v.money_type
                temp_list.buy_cost = v.buy_cost
				temp_list.timestamp = v.timestamp
				temp_list.id = v.seq 
				temp_list.special_sign = cfg.special_sign
				temp_list.sort = cfg.special_sign == 1 and v.seq or (1000 + v.seq)
				num = num + 1
				table.insert(data_lsit,temp_list)
				if v.seq == 5 and self.is_frist_open == 1 and v.state == 2 then
					self.special_flag = true
				end
			end
		end
	end

	table.sort(data_lsit, SortTools.KeyLowerSorter("sort"))

	return data_lsit, num
end

function LayoutZeroBuyWGData:GetZeroBuyFlag()
	return self.special_flag
end

function LayoutZeroBuyWGData:SetZeroBuyFlag(flag)
	self.special_flag = flag
end

function LayoutZeroBuyWGData:GetAllZeroBuyData()
	local data_lsit = {}
	if not self.zero_buy_info_list.info_list then
		return data_lsit
	end

	for i,v in ipairs(self.zero_buy_info_list.info_list) do
		local temp_list = {}
		local cfg = self:GetZeroBuyCfgOpenDay(v.seq)
		if cfg then
			-- temp_list = __TableCopy(cfg)
            temp_list.cfg = cfg
            temp_list.state = v.state
            temp_list.new_flag = v.new_flag
            temp_list.money_type = v.money_type
            temp_list.buy_cost = v.buy_cost
            temp_list.timestamp = v.timestamp
			table.insert(data_lsit,temp_list)
		end
	end
	return data_lsit
end

function LayoutZeroBuyWGData:GetZeroBuyDataByID(id)
	local data_lsit = self:GetAllZeroBuyData()
	if not data_lsit then
		return
	end

	for i,v in ipairs(data_lsit) do

		if v.cfg.ID == id then
			return v
		end
	end
end

function LayoutZeroBuyWGData:ZeroBuyRmind()
	if self:ZeroBuyRmindLogic1() == 1 then
		return 1
	end

	if self:ZeroBuyRmindLogic2() == 1 then
		return 1
	end

	if self:GetZeroBuyFlag() then
		return 1
	end

	return 0
end

function LayoutZeroBuyWGData:SetHasOpenZeroBuyFlag(flag)
	self.has_open_zero_buy_flag = flag
end

function LayoutZeroBuyWGData:ZeroBuyRmindLogic1()
	local is_remind = 0
	local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")
	local data_lsit = LayoutZeroBuyWGData.Instance:GetZeroBuyData()
	local bangyu_remind = false--绑玉类型未购买判断
	for i,v in ipairs(data_lsit) do
		if v.state == 3 and TimeWGCtrl.Instance:GetServerTime() > v.timestamp then
			is_remind = is_remind + 1
			break
		end
		--1.绑定仙玉足够购买,,有未购买时
		--2.首次登陆,有未购买时
		bangyu_remind = v.cfg and v.cfg.money_type == 2 and v.cfg.buy_cost <= bind_gold
		if v.state == 2 and (bangyu_remind or not self.has_open_zero_buy_flag) then
			is_remind = is_remind + 1
			break
		end
	end
	if is_remind > 0 then
		GlobalEventSystem:Fire(ACTIVITY_BTN_EVENT.ZEROBUY_BTN_CHANGE, ZEROBUY_ACT_BTN.SHOW_NEW, false)
	else
		local is_show_new = 0
		for k,v in pairs(data_lsit) do
			if v.new_flag == 1 then
				is_show_new = is_show_new + 1
				break
			end
		end
		GlobalEventSystem:Fire(ACTIVITY_BTN_EVENT.ZEROBUY_BTN_CHANGE, ZEROBUY_ACT_BTN.SHOW_NEW, is_show_new > 0)
	end

	return is_remind
end


function LayoutZeroBuyWGData:ZeroBuyRmindLogic2()
	if self.is_frist_open == 0 then
		return 0
	end

	for i,v in ipairs(self.zero_buy_info_list.info_list) do
		if v.state == 2 then
			GlobalEventSystem:Fire(ACTIVITY_BTN_EVENT.ZEROBUY_BTN_CHANGE, ZEROBUY_ACT_BTN.SHOW_NEW, false)
			return 1
		end
	end

	return 0
end

--绑定仙玉足够购买0元购的第一个道具时，0元购增加一个红点提示，图标也增加一个红点提示
function LayoutZeroBuyWGData:ZeroBuyRmindLogic3()
	local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")

end

function LayoutZeroBuyWGData:SetIsFristOpen(is_frist_open)
	self.is_frist_open = is_frist_open
end

function LayoutZeroBuyWGData:GetCanFetchIndex(data_list)
	if not data_list then
		return
	end

	for i,v in ipairs(data_list) do
		if v.state == 3 and TimeWGCtrl.Instance:GetServerTime() > v.timestamp then
			return i
		end
	end

	for i,v in ipairs(data_list) do
		if v.state == 2 then
			return i
		end
	end
	for i,v in ipairs(data_list) do
		if v.state == 3 and TimeWGCtrl.Instance:GetServerTime() < v.timestamp then
			return i
		end
	end
end

function LayoutZeroBuyWGData:SetBuyLogProtocol(protocol)
	self.zero_buy_log_list = protocol.log_list
end

function LayoutZeroBuyWGData:GetZeroBuyLogList()
	if not self.zero_buy_log_list then
		return
	end

	local log_list = {}
	for i,v in ipairs(self.zero_buy_log_list) do
		local cfg = self:GetZeroBuyCfgOpenDay(v.seq)
		if cfg then
			local temp_list = {}
			temp_list.cfg = cfg
			temp_list.role_name = v.name
			temp_list.fetch_state = v.state
			temp_list.money_type = v.money_type
			temp_list.buy_count = v.buy_count
			table.insert(log_list, temp_list)
		end
	end
	return log_list
end


function LayoutZeroBuyWGData:GetItemInWhichID(item_id)
	for k,v in pairs(self.zerobuy_reward_cfg) do
		for t,q in pairs(v.com_reward) do
			if q.item_id == item_id then
				return k
			end
		end
	end 
	return -1
end

function LayoutZeroBuyWGData:GetZeroBuyDataOpenByID(id)
	if not self.zero_buy_info_list.info_list then 
		return false
	end

	for i,v in ipairs(self.zero_buy_info_list.info_list) do
		if v.state ~= 4 and v.seq == id then
			return true
		end
	end
	return false
end

--0:代表该活动应该关闭(活动状态:关)
--1:代表该活动--> 正在进行 / 已超过活动日期,但有奖励可领取 (活动状态:开)
function LayoutZeroBuyWGData:CheckZeroOpenState()
	--local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    --local close_day = self:GetZeroBuyCloseDay()
    --local act_is_close = false
    local all_item_down = true

	-- if open_day >= close_day then
	-- 	act_is_close = true
	-- end

	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local data_lsit = self:GetZeroBuyData()
	if not IsEmptyTable(data_lsit) then
		for i,v in ipairs(data_lsit) do
            if v.state == 3 then
                all_item_down = false
                break
			end
		end
	end
	local act_num = all_item_down and 0 or 1
	return act_num
end

function LayoutZeroBuyWGData:GetZeroBuyCloseDay()
	if not self.close_day then
		local close_day = -1
		if not IsEmptyTable(self.zerobuy_reward_cfg) then
			for k, v in pairs(self.zerobuy_reward_cfg) do
				if v.down_opengame_day and v.down_opengame_day > close_day then
					close_day = v.down_opengame_day
				end
			end
		end
		if close_day <= 0 then
			close_day = 6
		end
		self.close_day = close_day
	end
	return self.close_day
end