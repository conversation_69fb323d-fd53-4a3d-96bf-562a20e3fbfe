﻿using Nirvana;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.SceneManagement;

[CustomEditor(typeof(SceneOptimize))]
public class SceneOptimizeEditor : Editor
{
    private static CameraCullingDisType cullDisType = CameraCullingDisType.MAX;
    private static bool isShowShadowLayer = false;

    public override void OnInspectorGUI()
    {
        this.DrawDefaultInspector();

        if (GUILayout.Button("一键优化"))
        {
            var optimize = (SceneOptimize)this.target;
            optimize.QuickOptimize();
            optimize.AutoSetOcclusion();
            AssetDatabase.SaveAssets();
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
        }

        if (GUILayout.Button("一键烘焙"))
        {
            if (Lightmapping.isRunning)
            {
                EditorUtility.DisplayDialog("提示", "正在烘焙中，请稍后再试...", "返回");
                return;
            }

            if (EditorUtility.DisplayDialog("提示", "是否开始烘焙", "是", "否"))
            {
                ShowModelShadowCast();

                Lightmapping.bakeCompleted -= BakeComplete;
                Lightmapping.bakeCompleted += BakeComplete;

                Lightmapping.BakeAsync(); 
            }
        }

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("一键开启场景CastShadows"))
        {
            ShowModelShadowCast();
        }
        if (GUILayout.Button("一键关闭场景CastShadows"))
        {
            HideModelShadowCast();
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("一键添加昼夜系统"))
        {
            AddDayNightSystem();
        }
        //if (GUILayout.Button("一键删除昼夜系统"))
        //{
        //    RemoveDayNightSystem();
        //}
        GUILayout.EndHorizontal();
        
        if (GUILayout.Button("一键设置静态"))
        {
            SetStatic();
         }

        if (Application.isPlaying)
        {
            if (GUILayout.Button("同步LOD光照贴图"))
            {
                var optimize = (SceneOptimize)this.target;
                optimize.SyncLODLightMap();
            }
        }
        else
        {
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("一键生成LOD"))
            {
                var optimize = (SceneOptimize)this.target;
                CreateSceneLODs();
                AssetDatabase.SaveAssets();
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }

            if (GUILayout.Button("一键删除LOD"))
            {
                var optimize = (SceneOptimize)this.target;
                CleanSceneLODs();
                AssetDatabase.SaveAssets();
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }

            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("一键隐藏LOD"))
            {
                var optimize = (SceneOptimize)this.target;
                HideSceneLODs();
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }

            if (GUILayout.Button("一键显示LOD"))
            {
                var optimize = (SceneOptimize)this.target;
                ShowSceneLODs();
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }

            if (GUILayout.Button("一键Occlusion"))
            {
                var optimize = (SceneOptimize)this.target;
                optimize.AutoSetOcclusion();
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }
            GUILayout.EndHorizontal();
        }

        CameraCullingDisType oldCullDisType = cullDisType;
        cullDisType = (CameraCullingDisType)EditorGUILayout.EnumPopup("ShowCullType", cullDisType);
        if (oldCullDisType != cullDisType)
        {
            ShowCullType();
        }

        (this.target as SceneOptimize).isIgnoreEffectCulling = EditorGUILayout.ToggleLeft("忽略场景特效剔除", (this.target as SceneOptimize).isIgnoreEffectCulling);

        bool _isShowShadowLayer = EditorGUILayout.ToggleLeft("显示接收阴影层", isShowShadowLayer);
        if (isShowShadowLayer != _isShowShadowLayer)
        {
            isShowShadowLayer = _isShowShadowLayer;
            ShowShadowLayer();
        }
        
        (this.target as SceneOptimize).isCullEnable = EditorGUILayout.ToggleLeft("开启距离剔除", (this.target as SceneOptimize).isCullEnable);

        (this.target as SceneOptimize).isAllowHighPerformance = EditorGUILayout.ToggleLeft("允许使用高耗性能技术（如个人副本，慎用!!! 请负责!!！）", (this.target as SceneOptimize).isAllowHighPerformance);

        if (Application.isPlaying)
        {
            this.ShowQualityAct();
        }
    }

    private void ShowCullType()
    {
        var sceneOptimize = (SceneOptimize)this.target;
        Dictionary<CameraCullingDisType, List<Transform>> layerObjects =  sceneOptimize.GetFilterCullingObjects();

        foreach (var kv in layerObjects)
        {
            List<Transform> list = kv.Value;
            foreach (var transform in list)
            {
                if (kv.Key == cullDisType || cullDisType == CameraCullingDisType.MAX)
                {
                    transform.gameObject.SetActive(true);
                }
                else
                {
                    transform.gameObject.SetActive(false);
                }
            }
        }
    }

    private void ShowShadowLayer()
    {
        var sceneOptimize = (SceneOptimize)this.target;
        Dictionary<CameraCullingDisType, List<Transform>> layerObjects = sceneOptimize.GetFilterCullingObjects();

        foreach (var kv in layerObjects)
        {
            List<Transform> list = kv.Value;
            foreach (var transform in list)
            {
                if (!isShowShadowLayer)
                {
                    transform.gameObject.SetActive(true);
                    continue;
                }

                transform.gameObject.SetActive(transform.gameObject.layer == GameLayers.ReceiveShadow);
            }
        }
    }

    private void ShowQualityAct()
    {
        string path = "Assets/Game/Misc/QualityConfig.asset";
        var qualityConfig = AssetDatabase.LoadAssetAtPath<QualityConfig>(path);
        if (qualityConfig == null)
        {
            Debug.LogError("找不到QualityConfig");
            return;
        }

        var levelCount = qualityConfig.GetLevelCount();
        var qualityMenu = new string[levelCount];
        for (int i = 0; i < levelCount; ++i)
        {
            var level = qualityConfig.GetLevel(i);
            qualityMenu[i] = level.Name;
        }

        var currentLevel = QualityConfig.QualityLevel;
        EditorGUI.BeginChangeCheck();
        currentLevel = EditorGUILayout.Popup("Select Quality:", currentLevel, qualityMenu);
        if (EditorGUI.EndChangeCheck())
        {
            QualityConfig.QualityLevel = currentLevel;
            //QualityConfig.SetOverrideShadowQuality(currentLevel, (int)ShadowQuality.Disable);
           
            if (currentLevel == 0)
            {
                SceneOptimizeMgr.SetCullDistanceFactor(1);
            }
            if (currentLevel == 1)
            {
                SceneOptimizeMgr.SetCullDistanceFactor(0.95f);
            }
            if (currentLevel == 2)
            {
                SceneOptimizeMgr.SetCullDistanceFactor(0.85f);
            }
            if (currentLevel == 3)
            {
                SceneOptimizeMgr.SetCullDistanceFactor(0.8f);
            }
        }

        EditorGUILayout.Space();
    }

    private void CreateSceneLODs()
    {
        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，生成LOD失败");
            return;
        }

        SceneOptimize optimize = target as SceneOptimize;
        optimize.updateDelegate = Update;
        AutomaticSceneLODTool.CreateLODs(rootObj);
    }

    private void CleanSceneLODs()
    {
        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，删除LOD失败");
            return;
        }

        AutomaticSceneLODTool.CleanLODs(rootObj);
    }

    private void HideSceneLODs()
    {
        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，隐藏LOD失败");
            return;
        }

        AutomaticSceneLODTool.ShowOrHideLOD(rootObj, false);
    }

    private void ShowSceneLODs()
    {
        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，显示LOD失败");
            return;
        }

        AutomaticSceneLODTool.ShowOrHideLOD(rootObj, true);
    }
    
    private void SetStatic()
    {
        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，设置Static失败");
            return;
        }

        HashSet<Transform> cantStaticSet = new HashSet<Transform>();
        
        LODGroup[] groups = rootObj.GetComponentsInChildren<LODGroup>(true);
        for (int i = 0; i < groups.Length; i++)
        {
            LODGroup lodGroup = groups[i];
            LOD[] lods = lodGroup.GetLODs();
            for (int j = 1; j < lods.Length; j++)
            {
                LOD lod = lods[j];
                for (int k = 0; k < lod.renderers.Length; k++)
                {
                    var renderer = lod.renderers[k];
                    if (renderer)
                        cantStaticSet.Add(renderer.transform);
                    else
                        Debug.LogError($"LODGroup {lodGroup.name} 存在空引用，请处理", lodGroup);
                }
            }
        }

        StaticEditorFlags flags = StaticEditorFlags.ContributeGI;

        Transform[] tfs = rootObj.transform.GetComponentsInChildren<Transform>(true);
        foreach (var transform in tfs)
        {
            if (cantStaticSet.Contains(transform))
            {
                GameObjectUtility.SetStaticEditorFlags(transform.gameObject, 0); 
            }
            else
            {
                GameObjectUtility.SetStaticEditorFlags(transform.gameObject, flags); 
            }
        }
    }

    private void BakeComplete()
    {
        HideModelShadowCast();
        Lightmapping.bakeCompleted -= BakeComplete;
    }

    private void ShowModelShadowCast()
    {
        GameObject ModelObj = GameObject.Find("Main/Models");
        if (null == ModelObj)
        {
            Debug.LogError("没有配置Main/Models，请检查");
            return;
        }

        MeshRenderer[] meshRenderers = ModelObj.GetComponentsInChildren<MeshRenderer>();
        for (int i = 0; i < meshRenderers.Length; i++)
        {
            MeshRenderer meshRenderer = meshRenderers[i];
            if (meshRenderer.gameObject.layer != GameLayers.DisableCastShadow && !meshRenderer.gameObject.name.EndsWith("_ShadowsOnly"))
            {
                meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.On;
            }
        }

        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
        AssetDatabase.SaveAssets();
    }

    private void HideModelShadowCast()
    {
        GameObject ModelObj = GameObject.Find("Main/Models");
        if (null == ModelObj)
        {
            Debug.LogError("没有配置Main/Models，请检查");
            return;
        }

        MeshRenderer[] meshRenderers = ModelObj.GetComponentsInChildren<MeshRenderer>();
        for (int i = 0; i < meshRenderers.Length; i++)
        {
            meshRenderers[i].shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
        }
        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
        AssetDatabase.SaveAssets();
    }

    private void AddDayNightSystem()
    {
        GameObject mainObj = GameObject.Find("Main");
        if (null == mainObj)
        {
            Debug.LogError("没有找到Main，请检查");
            return;
        }
        GameObject heroLightObj = GameObject.Find("Main/Hero light");
        if (null == heroLightObj)
        {
            heroLightObj = GameObject.Find("Main/Herolight");
            if (null == heroLightObj)
            {
                Debug.LogError("没有找到Main/Hero light，请检查");
                return;
            }
            heroLightObj.name = "Hero light";
        }
        Light heroLight = heroLightObj.GetOrAddComponent<Light>();    
        
        GameObject srpSkyBoxObj = GameObject.Find("Main/SrpSkybox");
        if (null == srpSkyBoxObj)
        {
            srpSkyBoxObj = GameObject.Instantiate(AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Game/Misc/UrpEngineMono/Prefab/SrpSkybox.prefab"));
            srpSkyBoxObj.transform.parent = mainObj.transform;
            srpSkyBoxObj.name = "SrpSkybox";
        }
        SrpSkyboxMono srpSkyboxMono = srpSkyBoxObj.GetOrAddComponent<SrpSkyboxMono>();

        //处理Lighting-Environment里面的Skybox Material
        string path = AssetDatabase.GetAssetPath(RenderSettings.skybox.GetInstanceID());
        if (!string.IsNullOrEmpty(path))
        {
            Material defaultSkyboxMat = AssetDatabase.LoadAssetAtPath<Material>(path);
            defaultSkyboxMat.shader = Shader.Find("WeatherSystem/CubemapCf");
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    private void RemoveDayNightSystem()
    {

    }

    private void Update()
    {
        AutomaticSceneLODTool.Update();
    }

    [MenuItem("Assets/美术专用/Editor生成正式场景文件")]
    private static void GenerateSceneEditor2Release()
    {
        if (null == Selection.activeObject || Selection.instanceIDs.Length == 0)
        {
            EditorUtility.DisplayDialog("提示", "请选择后缀为_Main_Editor的场景文件操作", "确定");
            return;
        }

        string[] deleteSceneObjName = new string[] { "Lights", "Light", "Scene Driver", "SceneDriver" };

        foreach (var instanceID in Selection.instanceIDs)
        {
            string scene_editor_asset = AssetDatabase.GetAssetPath(instanceID);
            if (!scene_editor_asset.Replace("\\", "/").EndsWith("_Main_Editor.unity"))
            {
                continue;
            }

            string scene_asset = scene_editor_asset.Replace("\\", "/").Replace("_Main_Editor.unity", "_Main.unity");
            AssetDatabase.CopyAsset(scene_editor_asset, scene_asset);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Scene new_scene = EditorSceneManager.OpenScene(scene_asset);
            if (new_scene != null)
            {
                GameObject mainNode = null;
                GameObject[] gameObjects = new_scene.GetRootGameObjects();
                foreach (GameObject node in gameObjects)
                {
                    if (node.name == "Main" && node.transform.parent == null)
                    {
                        mainNode = node;
                        break;
                    }
                }

                if (null != mainNode)
                {
                    DestroyImmediate(mainNode.GetComponent<SceneOptimize>());

                    foreach (string delObjName in deleteSceneObjName)
                    {
                        Transform transform = mainNode.transform.Find(delObjName);
                        if (null != transform)
                        {
                            DestroyImmediate(transform.gameObject);
                        }
                    }
                }
                EditorSceneManager.MarkSceneDirty(new_scene);
                EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo();
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
}