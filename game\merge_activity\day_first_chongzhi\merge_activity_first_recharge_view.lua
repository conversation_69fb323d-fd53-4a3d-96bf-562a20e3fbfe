--合服活动 每日首充
function MergeActivityView:InitDayShouChongView()
	self.shouchong_reward_list1 = nil
	self.shouchong_reward_list2 = nil
end

function MergeActivityView:LoadIndexCallBackDayShouChongView()
	if not self.shouchong_reward_list1 then
		self.shouchong_reward_list1 = AsyncListView.New(ItemCell, self.node_list["first_reward_list1"])
	end

	if not self.shouchong_reward_list2 then
		self.shouchong_reward_list2 = AsyncListView.New(ItemCell, self.node_list["first_reward_list2"])
	end

	self:DTYYOnFlushShouChongView()
	XUI.AddClickEventListener(self.node_list.first_btn_receive, BindTool.Bind(self.OnShouChongBtnReceiveClickHnadler,self))
	XUI.AddClickEventListener(self.node_list.first_btn_recharge, BindTool.Bind(self.OnShouChongBtnRecharge<PERSON>lick<PERSON>nadler,self))
end

function MergeActivityView:OpenIndexCallBackShouChongView()
	MergeFirstRechargeWGCtrl.Instance:SendDayShouChongReq(MERGE_ACTIVITY_SHOUCHONG_TYPE.TYPE_INFO)
end

function MergeActivityView:DTYYReleaseShouChongView()
	if self.shouchong_reward_list1 then
		self.shouchong_reward_list1:DeleteMe()
		self.shouchong_reward_list1 = nil
	end

	if self.shouchong_reward_list2 then
		self.shouchong_reward_list2:DeleteMe()
		self.shouchong_reward_list2 = nil
	end
end


function MergeActivityView:DTYYOnFlushShouChongView()
	local all_reward_state = MergeFirstRechargeWGData.Instance:GetAllRewardState()
	local btn_desc
	self.node_list.first_btn_redpoint:SetActive(false)
	self.node_list.first_btn_receive:SetActive(true)
	--self.node_list.first_dlq_effect:SetActive(false)
    --self.node_list.first_lq_effect:SetActive(false)
    self.node_list.first_btn_ylq:SetActive(false)
	--可领取
	if all_reward_state == MERGE_ACTIVITY_FIRST_RECHARGE.KLQ then
		--不置灰
		XUI.SetGraphicGrey(self.node_list.first_btn_receive, false)
		self.node_list.first_btn_redpoint:SetActive(true)
		--self.node_list.first_lq_effect:SetActive(true)
		btn_desc = Language.MergeFirstRecharge.Btn_1
	--已领取状态
	elseif all_reward_state == MERGE_ACTIVITY_FIRST_RECHARGE.YLQ then
		self.node_list.first_btn_receive:SetActive(false)
		self.node_list.first_btn_ylq:SetActive(true)

		-- local is_active_last_day = MergeActivityWGData.Instance:IsActivityLastDay(TabIndex.merge_activity_2109)
		--最后一天显示已领取（领取按钮隐藏）
		-- if is_active_last_day then
		-- 	self.node_list.first_btn_receive:SetActive(false)
        --     btn_desc = Language.MergeFirstRecharge.Btn_3
        --     self.node_list.first_btn_ylq:SetActive(true)
		-- else
		-- --明日可领取（领取按钮置灰）
		-- 	btn_desc = Language.MergeFirstRecharge.Btn_2
		-- 	XUI.SetGraphicGrey(self.node_list.first_btn_receive, true)
		-- end
	--不可领取状态显示特效
	else
		--self.node_list.first_dlq_effect:SetActive(true)
		XUI.SetGraphicGrey(self.node_list.first_btn_receive, false)
		btn_desc = Language.MergeFirstRecharge.Btn_4
	end

	local reward_state1, reward_state2 = MergeFirstRechargeWGData.Instance:GetRewardState()
	local num = MergeFirstRechargeWGData.Instance:GetChongZhiRmbNum()
	local open_day = MergeFirstRechargeWGData.Instance:GetFirstDayRechargeOpenDay()
	local data_list = MergeFirstRechargeWGData.Instance:GetShouChongRewardCfgByDay(open_day)
	local str, index, color
	if num < data_list[1].recharge then
		color = COLOR3B.C3
		index = 1
	elseif num >= data_list[2].recharge then
		color = COLOR3B.C2
		index = 2
		num = data_list[2].recharge
	elseif num >= data_list[1].recharge and reward_state1 == MERGE_ACTIVITY_FIRST_RECHARGE.KLQ then
		color = COLOR3B.C2
		index = 1
	elseif reward_state1 == MERGE_ACTIVITY_FIRST_RECHARGE.YLQ then
		color = COLOR3B.C3
		index = 2
	end

	str = ToColorStr(num * RECHARGE_BILI, color) .. "/" .. data_list[index].recharge * RECHARGE_BILI
	self.node_list.first_btn_label.text.text = btn_desc
	self.node_list.first_recharge_bili.text.text = str
	self:DTYYFlushShouChongList()
end

function MergeActivityView:FirstRechargeSortDataList(data_list)
	if data_list and not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
			if v.item_id and v.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)       
                v.color = item_cfg and item_cfg.color or 1
			else
                v.color = 0
            end
		end
		table.sort(data_list, SortTools.KeyUpperSorters("color"))
    end
    return data_list
end

function MergeActivityView:DTYYFlushShouChongList()
	local open_day = MergeFirstRechargeWGData.Instance:GetFirstDayRechargeOpenDay()
	local data_list = MergeFirstRechargeWGData.Instance:GetShouChongRewardCfgByDay(open_day)
	local reward_state1, reward_state2 = MergeFirstRechargeWGData.Instance:GetRewardState()
	if data_list ~= {} then
		local list = SortTableKey(data_list[1].reward_item)
        list = self:FirstRechargeSortDataList(list)

		self.shouchong_reward_list1:SetRefreshCallback(function(item_cell, cell_index)
			if item_cell then
				item_cell:SetLingQuVisible(reward_state1 == MERGE_ACTIVITY_FIRST_RECHARGE.YLQ)
				item_cell:SetRedPointEff(reward_state1 == MERGE_ACTIVITY_FIRST_RECHARGE.KLQ)
			end
		end)

		self.shouchong_reward_list1:SetDataList(list)

		local list2 = SortTableKey(data_list[2].reward_item)
        list2 = self:FirstRechargeSortDataList(list2)

		self.shouchong_reward_list2:SetRefreshCallback(function(item_cell, cell_index)
			if item_cell then
				item_cell:SetLingQuVisible(reward_state2 == MERGE_ACTIVITY_FIRST_RECHARGE.YLQ)
				item_cell:SetRedPointEff(reward_state2 == MERGE_ACTIVITY_FIRST_RECHARGE.KLQ)
			end
		end)
		self.shouchong_reward_list2:SetDataList(list2)

		self.node_list.first_desc_title1.text.text = string.format(Language.MergeFirstRecharge.DescTitle, data_list[1].recharge)
		self.node_list.first_desc_title2.text.text = string.format(Language.MergeFirstRecharge.DescTitle, data_list[2].recharge)
	end
end

--点击领奖按钮
function MergeActivityView:OnShouChongBtnReceiveClickHnadler()
	local gift_state = MergeFirstRechargeWGData.Instance:GetAllRewardState()
	if gift_state == MERGE_ACTIVITY_FIRST_RECHARGE.KLQ then
		local empty_num = ItemWGData.Instance:GetEmptyNum()
		local stuff_empty_num = ItemWGData.Instance:GetStuffBagEmptyNum()
		if empty_num == 0 or stuff_empty_num == 0 then
			RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)  --背包已满
			return
		end 

		--可领取发领取协议（活动号，领取）
		MergeFirstRechargeWGCtrl.Instance:SendDayShouChongReq(MERGE_ACTIVITY_SHOUCHONG_TYPE.TYPE_RECEIVE, 0)
	elseif gift_state == MERGE_ACTIVITY_FIRST_RECHARGE.YLQ then
		--已领取飘窗
		TipWGCtrl.Instance:ShowSystemMsg(Language.MergeFirstRecharge.ReChargeStr3)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_rapidly)
	end
end

function MergeActivityView:OnShouChongBtnRechargeClickHnadler()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

--RuleTip 和 tips
function MergeActivityView:FlushRulerTipAndTipsInfo()
	self:SetRuleInfo(Language.MergeFirstRecharge.TipsActivityHintShow, Language.MergeFirstRecharge.TipsActivityHint)
	self:SetOutsideRuleTips(Language.MergeFirstRecharge.FirstRechargeState)
end