require("game/operation_activity/operation_happy_kuanghuan/operation_happy_kuanghuan_wg_data")

OperationHappyKuangHuanWGCtrl = OperationHappyKuangHuanWGCtrl or BaseClass(BaseWGCtrl)
function OperationHappyKuangHuanWGCtrl:__init()
	if OperationHappyKuangHuanWGCtrl.Instance ~= nil then
		print("[OperationHappyKuangHuanWGCtrl]error:create a singleton twice")
	end

	OperationHappyKuangHuanWGCtrl.Instance = self
	self.data = OperationHappyKuangHuanWGData.New()
	self:RegisterAllProtocols()

	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_happy_carnival_auto", BindTool.Bind(self.UpdataConfig, self))
end

function OperationHappyKuangHuanWGCtrl:__delete()
	OperationHappyKuangHuanWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil

end

function OperationHappyKuangHuanWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAHappyCanivalInfo, "OnSCOAHappyCanivalInfo")
end

function OperationHappyKuangHuanWGCtrl:OnSCOAHappyCanivalInfo(protocol)
	self.data:SetHappyKhInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_happy_kuanghuan)
	RemindManager.Instance:Fire(RemindName.OperationHappyKuangHuan)
end

function OperationHappyKuangHuanWGCtrl:SendReq(opera_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
	print_log("#sendReq#", ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN, opera_type, protocol.param_1)
end


function OperationHappyKuangHuanWGCtrl:UpdataConfig()
	self.data:UpdataConfig()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_happy_kuanghuan)
end