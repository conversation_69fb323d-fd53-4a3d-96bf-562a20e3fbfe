-- 跨服红包天降 发红包界面
RedPacketRainTipsView = RedPacketRainTipsView or BaseClass(SafeBaseView)

function RedPacketRainTipsView:__init()
	self.is_safe_area_adapter = true
    self.view_style = ViewStyle.Half
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/red_packet_rain_ui_prefab", "layout_red_packet_rain_tips")

end

function RedPacketRainTipsView:ReleaseCallBack()
    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
        self.role_head_cell = nil
    end
end

function RedPacketRainTipsView:LoadCallBack()
    self:InitRoleHeadCell()
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickBtnGo, self))
    
end

function RedPacketRainTipsView:ShowIndexCallBack()

end

function RedPacketRainTipsView:OnFlush()
    local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo()

    self.node_list.text_desc.text.text = string.format(Language.RedPacketRain.SendRole2, activity_info.role_info.role_name)


    --头像
    local data = {}
	data.role_id = activity_info.role_info.role_id
	data.prof = activity_info.role_info.prof
	data.sex = activity_info.role_info.sex
	data.is_show_main = true

	self.role_head_cell:SetData(data)
end

function RedPacketRainTipsView:OnClickBtnGo()
    GuajiWGCtrl.Instance:StopGuaji()
    TaskGuide.Instance:CanAutoAllTask(false)
    RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER)
    self:Close()
end

function RedPacketRainTipsView:InitRoleHeadCell()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end
end




