-- 仙侣PK战场
LoverPKSceneLogic = LoverPKSceneLogic or BaseClass(CommonFbLogic)

function LoverPKSceneLogic:__init()
end

function LoverPKSceneLogic:__delete()
    if self.close_loading_view_event then
        GlobalEventSystem:UnBind(self.close_loading_view_event)
        self.close_loading_view_event = nil
    end
end

function LoverPKSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
        LoverPkWGCtrl.Instance:OpenLoverPkFightSceneView()
    end)

    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end

    MainuiWGCtrl.Instance:SetBtnLevel(false)
    ViewManager.Instance:AddMainUIFuPingChangeList(LoverPkWGCtrl.Instance:GetLoverPkFightSceneView())
end

function LoverPKSceneLogic:Out(old_scene_type, new_scene_type)
    CommonFbLogic.Out(self)

    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
    LoverPkWGCtrl.Instance:CloseLoverPkFightSceneView()

    if self.close_loading_view_event then
        GlobalEventSystem:UnBind(self.close_loading_view_event)
        self.close_loading_view_event = nil
    end

    ViewManager.Instance:RemoveMainUIFuPingChangeList(LoverPkWGCtrl.Instance:GetLoverPkFightSceneView())
end

function LoverPKSceneLogic:CloseLoadingCallBack()
    local lover_data = LoverPkWGData.Instance

    local stand_time = lover_data:GetOtherCfgDataByAttrName("wait_start_sec")
    UiInstanceMgr.Instance:DoFBStartDown(stand_time + TimeWGCtrl.Instance:GetServerTime(), function ()
        LoverPkWGCtrl.Instance:FightSceneViewShowCountDown()

        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
        -- GuajiWGCtrl.Instance:SetMoveToPosCallBack(move_to_pos_call_back )
        local scene_id = Scene.Instance:GetSceneId()
        local pox_x, pos_y = lover_data:GetOtherCfgDataByAttrName("pos_1_x"), lover_data:GetOtherCfgDataByAttrName("pos_1_y")
        GuajiWGCtrl.Instance:MoveToPos(scene_id, pox_x, pos_y, 1)
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end)
end

function LoverPKSceneLogic:IsRoleEnemy(target_obj, main_role)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local target_obj_vo = target_obj:GetVo()

	if target_obj_vo.role_id == main_role_vo.role_id then
		return false
   	end

	local team_id = LoverPkWGData.Instance:GetMyMatchSide()
    -- print_error("敌人判断", target_obj_vo.special_param ~= team_id, target_obj_vo.special_param , team_id)
	if target_obj_vo.special_param == team_id then
		return false
	end

	return true
end

function LoverPKSceneLogic:GetGuajiCharacter()
	local target_obj = self:GetRole()
	if target_obj ~= nil then
		GuajiCache.target_obj = target_obj
		return target_obj, COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE, false
	end
end

function LoverPKSceneLogic:GetRole()
	local main_role = Scene.Instance:GetMainRole()
	local role_list = Scene.Instance:GetRoleList()
	local limit_distance = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local target_obj = nil
	local main_x, main_y = main_role:GetLogicPos()

	for k,v in pairs(role_list) do
		if not v:IsMainRole() and self:IsRoleEnemy(v, main_role) and v:IsDead() == false then
			local x, y = v:GetLogicPos()
			local distance = GameMath.GetDistance(main_x, main_y, x, y, false)
			if distance < limit_distance then
				target_obj = v
				limit_distance = distance
			end
		end
	end

	if target_obj ~= nil then
		return target_obj
	end
end

function LoverPKSceneLogic:GetGuajiSelectObjDistance()
	return 2000
end

function LoverPKSceneLogic:IsEnemyVisiblePriortiy()
	return true
end