﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UIGlassBlurCtrlWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UIGlassBlurCtrl), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetShotCamera", SetShotCamera);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ar("onRenderTextureSetup", get_onRenderTextureSetup, set_onRenderTextureSetup);
		<PERSON><PERSON>unction("OnRenderTextureSetup", UIGlassBlurCtrl_OnRenderTextureSetup);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetShotCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIGlassBlurCtrl obj = (UIGlassBlurCtrl)ToLua.CheckObject<UIGlassBlurCtrl>(L, 1);
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.SetShotCamera(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onRenderTextureSetup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIGlassBlurCtrl obj = (UIGlassBlurCtrl)o;
			UIGlassBlurCtrl.OnRenderTextureSetup ret = obj.onRenderTextureSetup;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onRenderTextureSetup on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onRenderTextureSetup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIGlassBlurCtrl obj = (UIGlassBlurCtrl)o;
			UIGlassBlurCtrl.OnRenderTextureSetup arg0 = (UIGlassBlurCtrl.OnRenderTextureSetup)ToLua.CheckDelegate<UIGlassBlurCtrl.OnRenderTextureSetup>(L, 2);
			obj.onRenderTextureSetup = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onRenderTextureSetup on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UIGlassBlurCtrl_OnRenderTextureSetup(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<UIGlassBlurCtrl.OnRenderTextureSetup>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<UIGlassBlurCtrl.OnRenderTextureSetup>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

