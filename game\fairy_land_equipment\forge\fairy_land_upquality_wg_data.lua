local this = FairyLandEquipmentWGData

function this:InitUpqualityData()
	RemindManager.Instance:Register(RemindName.FLEF_UpQuality, BindTool.Bind(self.IsShowUpqualityRedPoint, self))

	local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
	self.gb_equip_upcolor_cfg = ListToMap(main_cfg.gb_equip_upcolor, "slot", "src_color")	--圣装进阶

	self:RegisterUpqualityRemindInBag(RemindName.FLEF_UpQuality)
end

function this:DeleteUpqualityData()
	this.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.FLEF_UpQuality)
end

function this:RegisterUpqualityRemindInBag(remind_name)
	self.upquality_remind_item_id_map = {}
    local cfg = self:GetGBEquipUpcolorCfg(0, 1)
	if cfg and cfg.stuff_id > 0 then
		self.upquality_remind_item_id_map[cfg.stuff_id] = true
	end

    local item_id_list = {}
    for k,v in pairs(self.upquality_remind_item_id_map) do
        table.insert(item_id_list, k)
    end

    if not IsEmptyTable(item_id_list) then
	    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

function this:GetIsUpqualityRemindItem(id)
	if self.upquality_remind_item_id_map and self.upquality_remind_item_id_map[id] then
		return true
	end

	return false
end

function this:GetUpqualityJumpPart(slot)
	local equip_list = self:GetHolyEquipWearList(slot)
	local start_index = XIANJIE_EQUIP_TYPE.TEJIE

    if IsEmptyTable(equip_list) then
    	return start_index
    end

	local first_had_data_part
	for part = start_index, XIANJIE_EQUIP_TYPE.XIANYIN do
		local data = equip_list[part]
		if data and data.is_wear then
			if not first_had_data_part then
				first_had_data_part = part
			end

			if self:GetUpqualitySlotPartRemind(slot, part) == 1 then
				return part
			end
		end
	end

	return first_had_data_part or XIANJIE_EQUIP_TYPE.TEJIE
end

function this:IsShowUpqualityRedPoint()
	local show_list = self:GetGodBodyList()
    if IsEmptyTable(show_list) then
    	return 0
    end

    for k, v in pairs(show_list) do
    	local slot = v:GetSlotIndex()
    	local is_act = v:GetIsAct()
        if not is_act then 
        	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_UPQUALITY, 0)
        	return 0
        end

    	local remind = self:GetUpqualitySlotRemind(slot)
		if remind == 1 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_UPQUALITY, 1, function ()
				ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView,
					TabIndex.fl_eq_forge_upquality, nil, {to_ui_name = slot})
				return true
			end)
			return 1
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_UPQUALITY, 0)
	return 0
end

function this:GetUpqualitySlotRemind(slot)
	for i = XIANJIE_EQUIP_TYPE.TEJIE, XIANJIE_EQUIP_TYPE.XIANYIN do
		local remind = self:GetUpqualitySlotPartRemind(slot, i)
		if remind == 1 then
			return 1
		end
	end

	return 0
end

function this:GetUpqualitySlotPartRemind(slot, part)
	local cur_equip = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	local cur_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)
	if IsEmptyTable(cur_equip) or not cur_equip.is_wear or not cur_spe_part then
		return 0
	end

	local cur_color = cur_equip and cur_equip.color or 1
	local upcolor_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpcolorCfg(slot, cur_color)
	local stuff_id = upcolor_cfg and upcolor_cfg.stuff_id or 0
	local stuff_num = upcolor_cfg and upcolor_cfg.stuff_num or 1
	local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	local next_color = upcolor_cfg and upcolor_cfg.des_color or 0
	local next_equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemMapCfg(slot, part, next_color)
	if not IsEmptyTable(next_equip_cfg) then
		if has_stuff_num >= stuff_num then
			return 1
		end
	end

	return 0
end

function this:GetGBEquipUpcolorCfg(slot,src_color)
	return self.gb_equip_upcolor_cfg and self.gb_equip_upcolor_cfg[slot] and self.gb_equip_upcolor_cfg[slot][src_color]
end

function this:GetUpqualityuSlotEquipAttr(slot, part)
	local attr_list = {}
	local equip_info = self:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(equip_info) then
		return attr_list
	end

	local equip_cfg = self:GetHolyEquipItemCfg(equip_info.item_id)
	local cur_color = equip_cfg and equip_cfg.color or 1
	local upcolor_cfg = self:GetGBEquipUpcolorCfg(slot, cur_color)
	local next_color = upcolor_cfg and upcolor_cfg.des_color or 1
	local next_cfg = self:GetHolyEquipItemMapCfg(slot, part, next_color)

	attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(equip_cfg, next_cfg)
	return attr_list
end
