﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	/// <summary>
	/// 统一的任务调度器（主线程执行）。
	/// 
	/// 能力：
	/// - 每帧回调（frameTasks）
	/// - 线程间投递（postTasks）：其他线程可安全调用 PostTask，Update 中统一取出执行
	/// - 下一帧延迟（nextFrameTasks）
	/// - 指定时延的延迟任务（delayTasks）
	/// 
	/// 注意：该组件以单例方式常驻场景，不会在场景切换中销毁。
	/// </summary>
	[DisallowMultipleComponent]
	public sealed class Scheduler : MonoBehaviour
	{
		private Scheduler()
		{
		}

		/// <summary>
		/// 获取或创建单例实例。
		/// </summary>
		private static Scheduler Instance
		{
			get
			{
				Scheduler.CheckInstance();
				return Scheduler.instance;
			}
		}

		/// <summary>
		/// 清空所有调度队列与临时容器。
		/// </summary>
		public static void Clear()
		{
			Scheduler.frameTasks.Clear();
			Scheduler.executing.Clear();
			Scheduler.postTasks.Clear();
			Scheduler.nextFrameTasks.Clear();
			Scheduler.delayTasks.Clear();
		}

		/// <summary>
		/// 添加一个每帧回调监听。
		/// </summary>
		public static LinkedListNode<Action> AddFrameListener(Action action)
		{
			return Scheduler.frameTasks.AddLast(action);
		}

		/// <summary>
		/// 移除一个每帧回调监听。
		/// </summary>
		public static void RemoveFrameListener(LinkedListNode<Action> handle)
		{
			Scheduler.frameTasks.Remove(handle);
		}

		/// <summary>
		/// 在主线程运行一个协程。
		/// </summary>
		public static Coroutine RunCoroutine(IEnumerator coroutine)
		{
			return Scheduler.Instance.StartCoroutine(coroutine);
		}

		/// <summary>
		/// 停止一个由调度器启动的协程。
		/// </summary>
		public static void StopShcCoroutine(Coroutine coroutine)
		{
			Scheduler.Instance.StopCoroutine(coroutine);
		}

		/// <summary>
		/// 跨线程安全投递一个任务到主线程，将在下一次 Update 中执行。
		/// </summary>
		public static void PostTask(Action task)
		{
			List<Action> list = Scheduler.postTasks;
			lock (list)
			{
				Scheduler.postTasks.Add(task);
			}
		}

		/// <summary>
		/// 推迟到下一帧执行的任务。
		/// </summary>
		public static void Delay(Action task)
		{
			Scheduler.nextFrameTasks.Add(task);
		}

		/// <summary>
		/// 延迟指定时间（秒）后执行的任务。
		/// </summary>
		public static void Delay(Action task, float time)
		{
			Scheduler.DelayTime delayTime = default(Scheduler.DelayTime);
			delayTime.Task = task;
			delayTime.Time = Time.realtimeSinceStartup + time;
			Scheduler.delayTasks.Add(delayTime);
		}

		[RuntimeInitializeOnLoadMethod]
		private static void CheckInstance()
		{
			bool flag = Scheduler.instance == null && Application.isPlaying;
			if (flag)
			{
				GameObject gameObject = new GameObject("Scheduler", new Type[] { typeof(Scheduler) });
				Object.DontDestroyOnLoad(gameObject);
				Scheduler.instance = gameObject.GetComponent<Scheduler>();
			}
		}

		private void Awake()
		{
			Object.DontDestroyOnLoad(this);
		}

		private void OnDestroy()
		{
			Scheduler.frameTasks.Clear();
			Scheduler.executing.Clear();
			Scheduler.postTasks.Clear();
			Scheduler.nextFrameTasks.Clear();
			Scheduler.delayTasks.Clear();
		}

		private void Update()
		{
			// 逐帧执行 frameTasks（可在执行时被移除，使用安全遍历）
			LinkedListNode<Action> next;
			for (LinkedListNode<Action> linkedListNode = Scheduler.frameTasks.First; linkedListNode != null; linkedListNode = next)
			{
				next = linkedListNode.Next;
				Action value = linkedListNode.Value;
				try
				{
					value();
				}
				catch (Exception ex)
				{
					Scheduler.logger.LogError(ex.ToString());
				}
			}
			// 将 postTasks（可能来自其他线程）转移到 executing
			List<Action> list = Scheduler.postTasks;
			lock (list)
			{
				bool flag2 = Scheduler.postTasks.Count > 0;
				if (flag2)
				{
					for (int i = 0; i < Scheduler.postTasks.Count; i++)
					{
						Scheduler.executing.Add(Scheduler.postTasks[i]);
					}
					Scheduler.postTasks.Clear();
				}
			}
			// 将下一帧队列转移到 executing
			bool flag3 = Scheduler.nextFrameTasks.Count > 0;
			if (flag3)
			{
				for (int j = 0; j < Scheduler.nextFrameTasks.Count; j++)
				{
					Scheduler.executing.Add(Scheduler.nextFrameTasks[j]);
				}
				Scheduler.nextFrameTasks.Clear();
			}
			// 检查到期的延迟任务，转移到 executing
			bool flag4 = Scheduler.delayTasks.Count > 0;
			if (flag4)
			{
				float now = Time.realtimeSinceStartup;
				Scheduler.delayTasks.RemoveAll(delegate(Scheduler.DelayTime task)
				{
					bool flag5 = now >= task.Time;
					bool flag6;
					if (flag5)
					{
						Scheduler.executing.Add(task.Task);
						flag6 = true;
					}
					else
					{
						flag6 = false;
					}
					return flag6;
				});
			}
			this.Executing();
		}

		private void Executing()
		{
			for (int i = 0; i < Scheduler.executing.Count; i++)
			{
				Action action = Scheduler.executing[i];
				try
				{
					action();
				}
				catch (Exception ex)
				{
					Scheduler.logger.LogError(ex.ToString());
				}
			}
			Scheduler.executing.Clear();
		}

		private static Scheduler instance; // 单例实例

		private static Logger logger = LogSystem.GetLogger("Scheduler"); // 分类日志

		private static LinkedList<Action> frameTasks = new LinkedList<Action>(); // 每帧回调列表

		private static List<Action> executing = new List<Action>(); // 当前帧将要执行的任务

		private static List<Action> postTasks = new List<Action>(); // 跨线程投递的任务列表（需加锁）

		private static List<Action> nextFrameTasks = new List<Action>(); // 下一帧执行的任务

		private static List<Scheduler.DelayTime> delayTasks = new List<Scheduler.DelayTime>(); // 定时执行任务

		private struct DelayTime
		{
			public float Time; // 触发时间（绝对时间，realtimeSinceStartup）

			public Action Task; // 对应的任务
		}
	}
}
