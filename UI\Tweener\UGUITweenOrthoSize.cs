﻿using UnityEngine;

[AddComponentMenu("UGUI/Tween/UGUI Tween Orthographic Size")]
[RequireComponent(typeof(Camera))]
public class UGUITweenOrthoSize : UGUITweener
{

	public float from = 1f;
	public float to = 1f;

	Camera mCam;

	#if UNITY_4_3 || UNITY_4_5 || UNITY_4_6 || UNITY_4_7
	public Camera cachedCamera { get { if (mCam == null) mCam = camera; return mCam; } }
	#else
	public Camera cachedCamera { get { if (mCam == null) mCam = GetComponent<Camera>(); return mCam; } }
	#endif

	[System.Obsolete("Use 'value' instead")]
	public float orthoSize { get { return this.value; } set { this.value = value; } }

	public float value
	{
		get { return cachedCamera.orthographicSize; }
		set { cachedCamera.orthographicSize = value; }
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = from * (1f - factor) + to * factor; }

	static public UGUITweenOrthoSize Begin (GameObject go, float duration, float to)
	{
		UGUITweenOrthoSize comp = UGUITweener.Begin<UGUITweenOrthoSize>(go, duration);
		comp.from = comp.value;
		comp.to = to;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	public override void SetStartToCurrentValue () { from = value; }
	public override void SetEndToCurrentValue () { to = value; }
}
