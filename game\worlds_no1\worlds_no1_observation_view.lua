-- 天下第一观战面板
WorldsNO1ObservationView = WorldsNO1ObservationView or BaseClass(SafeBaseView)
function WorldsNO1ObservationView:__init()
	self.is_safe_area_adapter = true
	self.open_tween = nil
	self.close_tween = nil
	self.active_close = false
	self.blocks_raycasts = true
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:SetMaskBg(false, false)
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1_observation")
end

function WorldsNO1ObservationView:__delete()
end

function WorldsNO1ObservationView:ReleaseCallBack()
	self.end_info = nil
	if self.left_role_list then
		self.left_role_list:DeleteMe()
		self.left_role_list = nil
	end
	if self.right_role_list then
		self.right_role_list:DeleteMe()
		self.right_role_list = nil
	end
	self.last_push_time = nil
end

function WorldsNO1ObservationView:LoadCallBack()
	self.left_role_list = AsyncListView.New(WorldsNO1ObservationRoleItem, self.node_list["left_role_list"])
	self.left_list_original_x = self.node_list["left_role_list"].rect.anchoredPosition.x
	self.left_is_shrink = false
	self.right_role_list = AsyncListView.New(WorldsNO1ObservationRoleItem, self.node_list["right_role_list"])
	self.right_is_shrink = false
	self.right_list_original_x = self.node_list["right_role_list"].rect.anchoredPosition.x
	self.node_list["barrage_toggle"].toggle:AddValueChangedListener(BindTool.Bind(self.OnBarrageToggleChange, self)) 	-- 弹幕开关
	self.node_list["push_btn"].button:AddClickListener(BindTool.Bind(self.OnClickPush, self))
	self.node_list["left_btn"].button:AddClickListener(BindTool.Bind(self.OnClickLeft, self))
	self.node_list["right_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRight, self))
	self.last_push_time = 0
end

function WorldsNO1ObservationView:OpenCallBack()
	-- MainuiWGCtrl.Instance:SetMianUIPlayerState(false)
	-- MainuiWGCtrl.Instance:HideOrShowTaskParent(false)
	-- MainuiWGCtrl.Instance:SetJoystickShowState(false)
	-- MainuiWGCtrl.Instance:SetZuoqiBtnShowState(false)
	-- MainuiWGCtrl.Instance:SetRightBottomActive(false)
end

function WorldsNO1ObservationView:CloseCallBack()
	-- MainuiWGCtrl.Instance:SetMianUIPlayerState(true)
	-- MainuiWGCtrl.Instance:HideOrShowTaskParent(true)
	-- MainuiWGCtrl.Instance:SetJoystickShowState(true)
	-- MainuiWGCtrl.Instance:SetZuoqiBtnShowState(true)
	-- MainuiWGCtrl.Instance:SetRightBottomActive(true)
end

function WorldsNO1ObservationView:OnFlush()
	local scene_role_info_list = WorldsNO1WGData.Instance:GetObservationalSceneRoleListInfo()
	local left_info_list = {}
	local right_info_list = {}
	for i, v in ipairs(scene_role_info_list) do
		if i > math.floor(COMMON_CONSTS.WORLDS_NO1_ROLE_COUNT_MAX / 2) then
			table.insert(right_info_list, v)
		else
			table.insert(left_info_list, v)
		end
	end
	self.left_role_list:SetDataList(left_info_list)
	self.node_list["left_btn"]:SetActive(not IsEmptyTable(left_info_list))
	self.right_role_list:SetDataList(right_info_list)
	self.node_list["right_btn"]:SetActive(not IsEmptyTable(right_info_list))
end

-- 点击退出按钮
function WorldsNO1ObservationView:OnClickOutBtn()
	self:Close()
end

-- 弹幕开关
function WorldsNO1ObservationView:OnBarrageToggleChange(is_on)
	if not is_on then
		ViewManager.Instance:Close(GuideModuleName.BarrageView)
	end
	WorldsNO1WGData.Instance:SetShowBarrage(is_on)
end

-- 点击推送弹幕
local cold_down = 10
function WorldsNO1ObservationView:OnClickPush()
	if TimeWGCtrl.Instance:GetServerTime() > self.last_push_time + cold_down then
		local str = self.node_list["barrage_input"].input_field.text
		str = ChatWGData.Instance:FormattingMsg(str)
		str = ChatFilter.Instance:Filter(str)
		WorldsNO1WGCtrl.Instance:SendWorldsNO1Barrage(str)
		self.last_push_time = TimeWGCtrl.Instance:GetServerTime()
		self.node_list["barrage_input"].input_field.text = ""
	else
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.WorldsNO1.BarrageColdDown, math.floor(self.last_push_time + cold_down - TimeWGCtrl.Instance:GetServerTime())))
	end
end

-- 左边按钮
function WorldsNO1ObservationView:OnClickLeft()
	local delta = self.left_is_shrink and 0 or -500
	self.node_list["left_role_list"].rect:DOAnchorPosX(self.left_list_original_x + delta, 0.5)
	self.left_is_shrink = not self.left_is_shrink
end

-- 右边按钮
function WorldsNO1ObservationView:OnClickRight()
	local delta = self.right_is_shrink and 0 or 500
	self.node_list["right_role_list"].rect:DOAnchorPosX(self.right_list_original_x + delta, 0.5)
	self.right_is_shrink = not self.right_is_shrink
end

-----------------------WorldsNO1ObservationRoleItem------------
WorldsNO1ObservationRoleItem = WorldsNO1ObservationRoleItem or BaseClass(BaseRender)

function WorldsNO1ObservationRoleItem:__init()
	self.head = BaseHeadCell.New(self.node_list["head"])
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClickBtn, self)) 
end

function WorldsNO1ObservationRoleItem:__delete()
	if self.head then
		self.head:DeleteMe()
		self.head = nil
	end
end

function WorldsNO1ObservationRoleItem:OnFlush()
	if self.data == nil then return end
	-- 角色名
	self.node_list["role_name"].text.text = self.data.role_name

	-- 积分
	self.node_list["score"].text.text = string.format(Language.WorldsNO1.ObservationScore, self.data.score) 

	-- 血条
	self.node_list["hp"].slider.value = self.data.hp_percent

	-- 剩余复活次数
	self.node_list["relive_amount"].text.text = self.data.relive_count

	-- 是否已淘汰
	self.node_list["fall_label"]:SetActive(self.data.is_out)

	BrowseWGCtrl.Instance:SendGlobalQueryRoleInfo(self.data.plat_type, self.data.uid, nil, function (protocol_vo)
		if not self.node_list or not self.head or not self.data then
			return
		end
		-- 头像
		local data = {}
		data.role_id = protocol_vo.role_id
		data.prof = protocol_vo.prof
		data.sex = protocol_vo.sex
		data.server_id = protocol_vo.server_id
		self.head:SetData(data)
		self.head:SetGray(self.data.is_out)
	end)
	self.head:SetGray(self.data.is_out)

	self.node_list["hl"]:SetActive(self.data.uuid_str == WorldsNO1WGData.Instance:GetObservationUUIDStr())
end

-- 点击观战
function WorldsNO1ObservationRoleItem:OnClickBtn()
	WorldsNO1WGCtrl.Instance:SendObservate(self.data.uuid_str)
end
