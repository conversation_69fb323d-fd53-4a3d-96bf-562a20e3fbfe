--开服情缘活动
require("game/serveractivity/act_perfertqingren/act_perfertqingren_wg_data")
require("game/serveractivity/act_perfertqingren/act_city_info_score_view")
require("game/serveractivity/act_perfertqingren/act_profess_love_rank_view")
require("game/serveractivity/act_subview/qingyuan_mission_panel")

ServerActivityWGCtrl = ServerActivityWGCtrl or BaseClass(BaseWGCtrl)
function ServerActivityWGCtrl:InitQingyuanActivity()
    self.qingyuan_data = ActivePerfertQingrenWGData.New()
    self.city_info_score_reward_view = CityInfoRewardView.New()
    self.profess_love_rank_view = ProfessLovelRankView.New()
	self.qingyuan_mission_view = QingYuanMissionView.New()

    self:RegisterQingyuanActivityAllProtocols()
end

function ServerActivityWGCtrl:RegisterQingyuanActivityAllProtocols()
	 self:RegisterProtocol(SCLovingCityInfo, "OnSCLovingCityInfo")
	 self:RegisterProtocol(SCCoupleRankListAck,"OnSCCoupleRankListAck")
	 self:RegisterProtocol(CSGetCoupleRankList)
	 --爱的表白信息返回
     self:RegisterProtocol(SCRaProfessForLoveInfo,"OnSCRaProfessForLoveInfo")  --表白信息8884
     self:RegisterProtocol(SCRaProfessForLoveRankInfo,"OnSCRaProfessForLoveRankInfo")  --表白排行信息8879
	 --甜言蜜语
	 self:RegisterProtocol(CSGetProfessInfo) --8881
	 self:RegisterProtocol(SCPersonProfessInfo,"OnSCPersonProfessInfo") --个人表白信息8880
	 self:RegisterProtocol(SCGlobalProfessAllInfo,"OnSCGlobalProfessAllInfo") --公共表白全部信息8883
end

function ServerActivityWGCtrl:DeleteQingyuanActivity()
	if self.qingyuan_data then
		self.qingyuan_data:DeleteMe()
		self.qingyuan_data = nil
	end

	if self.qingyuan_mission_view then
		self.qingyuan_mission_view:DeleteMe()
		self.qingyuan_mission_view = nil
	end
end

function ServerActivityWGCtrl:OpenCityInfoRewardView()
	self.city_info_score_reward_view:Open()
end

function ServerActivityWGCtrl:FlushCityInfoRewardView()
    if self.city_info_score_reward_view:IsOpen() then
        self.city_info_score_reward_view:Flush()
    end
end

function ServerActivityWGCtrl:OpenProfessRankView()
	self.profess_love_rank_view:Open()
end

function ServerActivityWGCtrl:FlushProfessRankView()
    if self.profess_love_rank_view:IsOpen() then
        self.profess_love_rank_view:Flush()
    end
end

--排行信息
function ServerActivityWGCtrl:OnSCRaProfessForLoveRankInfo(protocol)
    self.qingyuan_data:SetProfessLoveRankInfo(protocol)
    self:FlushProfessRankView()
    --刷新爱的表白界面
    ViewManager.Instance:FlushView(GuideModuleName.ServerActivityTabView, TabIndex.act_lovers)
end

function ServerActivityWGCtrl:OnSCLovingCityInfo(protocol)
    self.qingyuan_data:SetLoveCityInfo(protocol)
    --刷新情缘任务相关
	if nil ~= self.qingyuan_mission_view and self.qingyuan_mission_view:IsOpen() then
		self.qingyuan_mission_view:Flush()
	end
    ViewManager.Instance:FlushView(GuideModuleName.ServerActivityTabView, TabIndex.city_love)


    self:FlushCityInfoRewardView()
	GlobalEventSystem:Fire(GLOBELEVENT_OPENSERVER_ACTIVITY.UPDATE_BTN_LIST)
	RemindManager.Instance:Fire(RemindName.KFCityLove)
end

function ServerActivityWGCtrl:SendActLoverReq(act_type,index, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = act_type--ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY
	protocol.opera_type = index or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

--爱的表白排行请求
function ServerActivityWGCtrl:SendReqBiaoBai(rank_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetCoupleRankList)
	--protocol.rand_activity_type = act_type--ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY
	protocol.rank_type = 0
	protocol:EncodeAndSend()
end

function ServerActivityWGCtrl:OnSCCoupleRankListAck(protocol)
    self.qingyuan_data:SetLoveBiaoBaiRank(protocol)
end

function ServerActivityWGCtrl:CSGetProfessInfo(opera_type,param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetProfessInfo)
	protocol.req_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end
--个人表白信息
function ServerActivityWGCtrl:OnSCPersonProfessInfo(protocol)
	self.qingyuan_data:SetPersonalInfo(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.ProfessWallView)
end

--公共表报全部信息
function ServerActivityWGCtrl:OnSCGlobalProfessAllInfo(protocol)
	self.qingyuan_data:SetAllProfessInfo(protocol)
	local info = {}
	info.items = self.qingyuan_data:GetAllProfessInfo()
	ViewManager.Instance:FlushView(GuideModuleName.ProfessWallView)
end

function ServerActivityWGCtrl:TianYanMiYuRankInfo(rank_type,protocol)
	ActivePerfertQingrenWGData.Instance:SetTianYanMiYuRankInfo(protocol.rank_type,protocol)
end

---
function ServerActivityWGCtrl:OnSCRaProfessForLoveInfo(protocol)
    for k, v in pairs(protocol.first_rank_list) do
        if v.role_id ~= 0 then
            AvatarManager.Instance:SetAvatarKey(v.role_id, v.avatar_key_big, v.avatar_key_small)
        end
    end
    self.qingyuan_data:SetProfessForLoveInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.ServerActivityTabView, TabIndex.act_lovers)
end

-----------------情缘任务-----------------
function ServerActivityWGCtrl:OpenQingYuanMissionView()
	if not self.qingyuan_mission_view:IsOpen() then
		self.qingyuan_mission_view:Open()
	end
end