FirstRechargeView = FirstRechargeView or BaseClass(SafeBaseView)

local SPECIAL_REWARD_NUM = 2 --特殊奖励最大数量
local MAX_REWARD_NUM = 5 --普通奖励最大数量
local FIRST_RECHARGE = 1 --首充档次
local MIDDLE_NUM = 4 --需要居中改变位置时的奖励数量

local FirstRechargeTabFunName = {
	[1] = FunName.first_recharge_shouchong,
	[2] = FunName.first_recharge_zhigou,
}

local BtnState = {
	None = 0,
	WaitOneDay = 1,
	WaitTwoDay = 2,
	CanGet = 3,
	Finish = 4,
}

function FirstRechargeView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
	self:SetMaskBgAlpha(180/255)
	self.view_name = GuideModuleName.FirstRechargeView
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "layout_first_recharge")
end

function FirstRechargeView:Open()
	if IS_AUDIT_VERSION then
		return
	end

	SafeBaseView.Open(self)
end

function FirstRechargeView:LoadCallBack()
	self.select_grade = 1         -- 选择的档次
	self.select_day = 1		   -- 选择的天数
	self.is_flush = false
	self.is_cur_flush = false

	if not self.shouchong_role_model then
        self.shouchong_role_model = OperationActRender.New(self.node_list["sc_display"])
        self.shouchong_role_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	for i = 1, MAX_C_Z_DAY do
		self.node_list["rc_day_toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickDayToggle, self, i))
	end

	if not self.special_reward_list then
		self.special_reward_list = {}

		for i = 1, SPECIAL_REWARD_NUM do
			self.special_reward_list[i] = FirstRechargeSpecialRewardRender.New(self.node_list["special_reward" .. i])
		end
	end

	if not self.reward_list then
		self.reward_list = {}
		for i = 1, MAX_REWARD_NUM do
			self.reward_list[i] = FirstRechargeItemCellRender.New(self.node_list.Container:FindObj("sc_item_cell" .. i))
		end
	end

	local cfg = ServerActivityWGData.Instance:GetSCXCTypeCfg(MAX_C_Z_SIZE)
	if cfg then
		self.node_list["get_all_desc"].text.text = string.format(Language.Recharge.FirstCharge_get_all[cfg.condition_type], cfg.condition_value)
	end

	self.node_list.desc_top_ads.text.text = Language.Recharge.FirstCharge_TopDesc
	XUI.AddClickEventListener(self.node_list.goto_recharge_btn, BindTool.Bind(self.OnClickGotoRechargeBtn, self))
	XUI.AddClickEventListener(self.node_list.get_reward_btn, BindTool.Bind(self.OnClickGetRewardBtn, self))
	self:CreateToggleList()
end

function FirstRechargeView:CreateToggleList()
	local grade_list = {}

	for i = 1, MAX_C_Z_SIZE do
		local item = FirstRechargeGradeItem.New()
		item:SetIndex(i)
		item:SetInstance(self.node_list["rc_toggle_" .. i])
		item:AddClickEventListener(BindTool.Bind(self.OnClickGradeToggle, self), true)
		grade_list[i] = item
	end

	self.rc_grade_list = grade_list
end

function FirstRechargeView:OnClickGradeToggle(toggle)
	if toggle:GetIndex() == 4 then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.FirstRechargeView, 0, BURIED_EVENT_PARAM.openView)
	end
	if self.select_grade ~= toggle:GetIndex() or (self.is_flush and not self.is_cur_flush) then
		self.select_grade = toggle:GetIndex()
		self:JumpRedDayToggle()
		self:RefreshView()
		--self:PlayAnim()
		self:PlayAnim2()
		self:PlayAnim3()
	elseif self.is_cur_flush then
		self:JumpRedDayToggle()
		self:RefreshView()
	end

	self.is_flush = false
	self.is_cur_flush = false
end

function FirstRechargeView:JumpRedDayToggle()
	local is_remind, day = ServerActivityWGData.Instance:GetSCRemind(self.select_grade)
	if is_remind then
		self.node_list["rc_day_toggle_" .. day].toggle.isOn = true
	else
		self.node_list["rc_day_toggle_" .. 1].toggle.isOn = true
	end
end

function FirstRechargeView:PlayAnim()
	self.node_list.special_reward_list:SetActive(false)
	self.node_list.reward_list:SetActive(false)
    ReDelayCall(self, function()
		self.node_list.special_reward_list:SetActive(true)
		self.node_list.reward_list:SetActive(true)
		UITween.AlphaShow(GuideModuleName.FirstRechargeView, self.node_list.special_reward_list, 0.1, 1, 0.4, DG.Tweening.Ease.Linear)
		UITween.AlphaShow(GuideModuleName.FirstRechargeView, self.node_list.reward_list, 0.1, 1, 0.4, DG.Tweening.Ease.Linear)
		self:Flush()
    end, 0.1, "FirstRechargeView")

	UITween.CleanAllMoveToShowPanel(GuideModuleName.FirstRechargeView)
	UITween.MoveToShowPanel(GuideModuleName.FirstRechargeView, self.node_list.special_reward_list, Vector2(272, 18), Vector2(272, -12), 0.4, DG.Tweening.Ease.Linear)
	UITween.MoveToShowPanel(GuideModuleName.FirstRechargeView, self.node_list.reward_list, Vector2(270, -86), Vector2(270, -126), 0.4, DG.Tweening.Ease.Linear)
end

function FirstRechargeView:PlayAnim2()
	-- local is_first = self.select_grade == FIRST_RECHARGE
	-- local node = is_first and self.node_list.first_title_img or self.node_list.title_img
	local node = self.node_list.title_img
	node:SetActive(false)
    ReDelayCall(self, function()
		node:SetActive(true)
		UITween.AlphaShow(GuideModuleName.FirstRechargeView, node, 0, 1, 0.4, DG.Tweening.Ease.Linear)
    end, 0.1, "FirstRechargeView2")

	UITween.ScaleShowPanel(node, u3dpool.vec3(1.3, 1.3, 1.3), 0.4)
end

function FirstRechargeView:PlayAnim3()
	local node = self.node_list.title_des_img1
	node:SetActive(false)
    ReDelayCall(self, function()
		node:SetActive(true)
		UITween.AlphaShow(GuideModuleName.FirstRechargeView, node, 0, 1, 0.4, DG.Tweening.Ease.Linear)
		UITween.ScaleShowPanel(node, u3dpool.vec3(1.3, 1.3, 1.3), 0.4)
    end, 0.3, "FirstRechargeView3")
end

function FirstRechargeView:ReleaseCallBack()
	if self.rc_grade_list then
		for _,v in pairs(self.rc_grade_list) do
			v:DeleteMe()
		end
		self.rc_grade_list = nil
	end

	if self.shouchong_role_model then
		self.shouchong_role_model:DeleteMe()
		self.shouchong_role_model = nil
	end

	if self.special_reward_list then
		for k, v in pairs(self.special_reward_list) do
			v:DeleteMe()
		end

		self.special_reward_list = nil
	end

	if self.reward_list then
		for k, v in pairs(self.reward_list) do
			v:DeleteMe()
		end

		self.reward_list = nil
	end

	-- self:CheckFirstTabClose()
	-- self:ReleseRechargeView()
end

--检测关闭
function FirstRechargeView:CheckFirstTabClose()
	--首充判断
	if FunOpen.Instance:GetFunIsOpened(FirstRechargeTabFunName[1]) and ServerActivityWGData.Instance and ServerActivityWGData.Instance:CheckAllSCRewardIsGet() then
		FunOpen.Instance:ForceCloseFunByName(FunName.first_recharge_shouchong)
		local sc_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.FIRST_CHONGZHI)
		local sc_act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.FIRST_CHONGZHI)
		if sc_btn and sc_act_cfg then
			local act_name_res = "btn_zhigou_name"
			sc_btn:Flush("SetSprite", {sc_act_cfg.res_name, act_name_res, sc_act_cfg.bg_res})
		end
	end

	--直购判断
	-- if FunOpen.Instance:GetFunIsOpened(FirstRechargeTabFunName[2]) and FirstRechargeRebateData.Instance and FirstRechargeRebateData.Instance:CheckAllRebateRewardIsGet() then
	-- 	FunOpen.Instance:ForceCloseFunByName(FunName.first_recharge_zhigou)
	-- end
end

function FirstRechargeView:ShowIndexCallBack()
	self:SelectGrade()
end

function FirstRechargeView:CloseCallBack()
	--self:PlayFlyTween()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.FirstRechargeView, 0, BURIED_EVENT_PARAM.closeView)
end

function FirstRechargeView:PlayFlyTween()
	local role_id = RoleWGData.Instance:GetUUIDStr()
	local result = PlayerPrefsUtil.GetInt("FirstChongzhiMark" .. role_id)
	if result ~= 1 then
		TipWGCtrl.Instance:OpenEasyFlyView()
		TipWGCtrl.Instance:FlushEasyFlyView(0, "act_icon", {act_type = ACTIVITY_TYPE.FIRST_CHONGZHI})
		PlayerPrefsUtil.SetInt("FirstChongzhiMark" .. role_id, 1)
	end
end

function FirstRechargeView:OnFlush(param_t)
	-- 领取完奖励后再默认选下
	if param_t.act_info then
		self.is_cur_flush = true
		self:SelectGrade()
	end

	--self:FlushBtnState()
	self:FlushGradeToggleRemind()

	for grade = 1, MAX_C_Z_SIZE - 1 do
		local gear_flag = ServerActivityWGData.Instance:GetRCGearFlag(grade)
		self.node_list["rc_toggle_" .. (grade + 1)]:SetActive(gear_flag)
	end
end

function FirstRechargeView:SelectGrade()
	local select_grade = self:GetDefaultSelect()

	if self.rc_grade_list then
		local tog = self.rc_grade_list[select_grade]

		if tog.view.toggle.isOn == true then
			self.is_flush = true
			self:OnClickGradeToggle(tog)
		else
			tog:SetToggelIsOn(true)
		end
	end
end

function FirstRechargeView:GetDefaultSelect()
	-- 优先红点可领取的
	for grade = 1, MAX_C_Z_SIZE do
		local is_remind, day = ServerActivityWGData.Instance:GetSCRemind(grade)
		if is_remind then
			return grade, day
		end
	end

	-- 当天可领取的奖励领取完后，默认跳到尚未充值的那档的第一天
	for grade = 1, MAX_C_Z_SIZE do
		local gear_flag = ServerActivityWGData.Instance:GetRCGearFlag(grade)
		if not gear_flag then
			return grade, 1
		end
	end

	-- 如果都充值过且当天全部领取完，则跳到第某档明日可领取
	for grade = 1, MAX_C_Z_SIZE do
		-- local chongzhi_type = ServerActivityWGData.Instance:GetSCXCChongZhiType(grade)
		local gear_flag = ServerActivityWGData.Instance:GetRCGearFlag(grade)
		if gear_flag then
			local flag_list = ServerActivityWGData.Instance:GetRewardFlagList(grade)
			if flag_list then
				for day = 1, MAX_C_Z_DAY do
					if flag_list.reward_flag[day - 1] == 0 then
						return grade, day
					end
				end
			end
			if grade == MAX_C_Z_SIZE then -- 到了最后一档了
				return MAX_C_Z_SIZE, MAX_C_Z_DAY
			end
		end
	end

	return self.select_grade, self.select_day
end

function FirstRechargeView:GetSelectDay(grade)
	local flag_list = ServerActivityWGData.Instance:GetRewardFlagList(grade)

	if flag_list then
		for day = 1, MAX_C_Z_DAY do
			if flag_list.reward_flag[day - 1] == 0 then
				return day
			end
		end
	end

	return MAX_C_Z_DAY
end

function FirstRechargeView:RefreshView()
	--self.select_day = self:GetSelectDay(self.select_grade)

	self:FlushGradeImage()
	self:FlushLeftPanel()
	self:FlushRewardList()
	self:FlushBtnState()
	self:FlushDayToggleRemind()
	self:FlushDesc()
end

function FirstRechargeView:FlushDesc()
	local cfg = ServerActivityWGData.Instance:GetSCXCTypeCfg(self.select_grade)
	if cfg then
		self.node_list["get_all_desc"]:SetActive(cfg.show_type == 1)
	end
end

function FirstRechargeView:FlushGradeImage()
	local is_first = self.select_grade == FIRST_RECHARGE
	-- self.node_list.first_title_img:SetActive(is_first)
	-- self.node_list.title_img:SetActive(not is_first)

	--if not is_first then
	local bundle, asset = ResPath.GetRawImagesPNG("a3_sc_title" .. self.select_grade)
	self.node_list.title_img.raw_image:LoadSprite(bundle, asset, function ()
		self.node_list.title_img.raw_image:SetNativeSize()
	end)
	--end

	bundle, asset = ResPath.GetRawImagesPNG("a3_sc_gg" .. self.select_grade)
	self.node_list.title_des_img1.raw_image:LoadSprite(bundle, asset, function ()
		self.node_list.title_des_img1.raw_image:SetNativeSize()
	end)

	-- bundle, asset = ResPath.GetFirstRechargeImg("a3_sc_bq" .. self.select_grade - 1)
	-- self.node_list.title_des_img2.image:LoadSprite(bundle, asset, function ()
	-- 	self.node_list.title_des_img2.image:SetNativeSize()
	-- end)
end

function FirstRechargeView:FlushLeftPanel()
	local sc_anim_cfg = ServerActivityWGData.Instance:GetSCAnimCfg(self.select_grade, self.select_day)
	if not sc_anim_cfg then
		return
	end

	self:FlushShowModel(sc_anim_cfg)
	if sc_anim_cfg.capability ~= "" then
		self.node_list.cap_value.text.text = sc_anim_cfg.capability or 0
		self.node_list.cap_root:SetActive(true)
	else
		self.node_list.cap_root:SetActive(false)
	end
end

function FirstRechargeView:FlushShowModel(show_cfg)
	if IsEmptyTable(show_cfg) then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if show_cfg.model_show_itemid ~= 0 and show_cfg.model_show_itemid ~= "" then
		local split_list = string.split(show_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_cfg.model_show_itemid
		end
	end
	display_data.bundle_name = show_cfg["model_bundle_name"]
    display_data.asset_name = show_cfg["model_asset_name"]
    local model_show_type = tonumber(show_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = show_cfg["model_show_itemid"]})
    end

	if show_cfg.rotation and show_cfg.rotation ~= "" then
		local rotation_tab = string.split(show_cfg.rotation,"|")
		display_data.model_adjust_root_local_rotation = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end

	if show_cfg.display_scale and show_cfg.display_scale ~= "" then
		display_data.model_adjust_root_local_scale = show_cfg.display_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L
    self.shouchong_role_model:SetData(display_data)
	local pos_x, pos_y = 0, 0
	if show_cfg.display_pos and show_cfg ~= "" then
		local pos_list = string.split(show_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.sc_display.rect, pos_x, pos_y)
end

function FirstRechargeView:FlushRewardList()
	local prof = RoleWGData.Instance:GetRoleProf()

	local reward_list, special_reward_list = ServerActivityWGData.Instance:GetSCXCRewardCfg(self.select_grade, self.select_day, prof)
	local reward_flag = ServerActivityWGData.Instance:GetSCRewardFlagByGradeDay(self.select_grade, self.select_day)
	local is_get_flag = reward_flag == 4
	local reward_num = 0

	for i = 1, SPECIAL_REWARD_NUM do
		if special_reward_list[i] then
			local data = {}
			data.is_get_flag = is_get_flag
			data.item_data = special_reward_list[i]
			data.reward_flag = reward_flag
			self.node_list["special_reward" .. i]:SetActive(true)
			self.special_reward_list[i]:SetData(data)
			reward_num = reward_num + 1
		else
			self.node_list["special_reward" .. i]:SetActive(false)
		end
	end

	for i = 1, MAX_REWARD_NUM do
		if reward_list[i] then
			local data = {}
			data.is_get_flag = is_get_flag
			data.item_data = reward_list[i]
			data.reward_flag = reward_flag
			self.node_list.Container:FindObj("sc_item_cell" .. i):SetActive(true)
			self.reward_list[i]:SetData(data)
			reward_num = reward_num + 1
		else
			self.node_list.Container:FindObj("sc_item_cell" .. i):SetActive(false)
		end
	end

	local rect_transform1 = self.node_list.reward_list.rect
	if reward_num == MIDDLE_NUM then
		RectTransform.SetAnchoredPositionXY(self.node_list.all_reward.rect, 326, -70)
		RectTransform.SetSizeDeltaXY(rect_transform1, 178, rect_transform1.sizeDelta.y)
	else
		RectTransform.SetAnchoredPositionXY(self.node_list.all_reward.rect, 283, -70)
		RectTransform.SetSizeDeltaXY(rect_transform1, 270, rect_transform1.sizeDelta.y)
	end
end

function FirstRechargeView:FlushBtnState()
	local reward_flag = ServerActivityWGData.Instance:GetSCRewardFlagByGradeDay(self.select_grade, self.select_day)
	local btn_name = Language.Recharge.FirstCharge_Receive[reward_flag] or Language.Recharge.FirstCharge_Receive[0]
	self.node_list.get_reward_text.text.text = btn_name

	if reward_flag == BtnState.None then
		local btn_text
		local data = ServerActivityWGData.Instance:GetSCXCTypeCfg(self.select_grade)
		if data and data.condition_type ~= 1 then
			btn_text = RoleWGData.GetPayMoneyStr(data.condition_value, data.rmb_type, data.rmb_seq)
		else
			btn_text = Language.Recharge.GoReCharge
		end

		self.node_list["goto_recharge_text"].text.text = btn_text
	end

	self.node_list.wait_day_btn:SetActive(reward_flag == BtnState.WaitOneDay or reward_flag == BtnState.WaitTwoDay)
	self.node_list.goto_recharge_btn:SetActive(reward_flag == BtnState.None)
	self.node_list.get_reward_btn:SetActive(reward_flag == BtnState.CanGet)
	self.node_list.get_red_point:SetActive(reward_flag == BtnState.CanGet)
	self.node_list.finish_img:SetActive(reward_flag == BtnState.Finish)
end

function FirstRechargeView:OnClickGotoRechargeBtn()
	local cfg = ServerActivityWGData.Instance:GetSCXCTypeCfg(self.select_grade)
	if not cfg then
		return
	end

	if cfg.condition_type == 1 then
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
	else
		RechargeWGCtrl.Instance:Recharge(cfg.condition_value, cfg.rmb_type, cfg.rmb_seq)
	end
end

function FirstRechargeView:OnClickDayToggle(index, is_on)
	if self.select_day ~= index and is_on then
		self.select_day = index
		self:FlushLeftPanel()
		self:FlushRewardList()
		self:FlushBtnState()
		--self:PlayAnim()
	end
end

function FirstRechargeView:OnClickGetRewardBtn()
	-- local reward_flag = ServerActivityWGData.Instance:GetSCRewardFlagByGradeDay(self.select_grade, self.select_day)
	-- if reward_flag == BtnState.WaitOneDay then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.FirstCharge_Tomorrow)
	-- 	return
	-- elseif reward_flag == BtnState.WaitTwoDay then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.FirstCharge_AfterTom)
	-- 	return
	-- end

	local chongzhi_type = ServerActivityWGData.Instance:GetSCXCChongZhiType(self.select_grade)
	ServerActivityWGCtrl.Instance:SendSCSCXCActReceive(chongzhi_type, self.select_day)
end

function FirstRechargeView:FlushGradeToggleRemind()
	if self.rc_grade_list then
		for _,v in pairs(self.rc_grade_list) do
			v:FlushRemind()
		end
	end
end

function FirstRechargeView:FlushDayToggleRemind()
	for i = 1, MAX_C_Z_DAY do
		local reward_flag = ServerActivityWGData.Instance:GetSCRewardFlagByGradeDay(self.select_grade, i)
		self.node_list["rc_day_toggle_" .. i]:FindObj("red_point"):CustomSetActive(reward_flag == BtnState.CanGet)
	end
end

--------------------------FirstRechargeGradeItem--------------------------
FirstRechargeGradeItem = FirstRechargeGradeItem or BaseClass(BaseRender)

function FirstRechargeGradeItem:SetToggelIsOn(_bool)
	self.view.toggle.isOn = _bool
end

function FirstRechargeGradeItem:LoadCallBack()
	local index = self:GetIndex()
	local data = ServerActivityWGData.Instance:GetSCXCTypeCfg(index)
	local str = data and data.icon_name or ""


	self.node_list.label1.text.text = Language.Recharge.FirstCharge_Desc
	self.node_list.label_hl1.text.text = Language.Recharge.FirstCharge_Desc
	self.node_list.label2.text.text = str
	self.node_list.label_hl2.text.text = str
	-- self:FlushActive()
	self:FlushRemind()
end

function FirstRechargeGradeItem:FlushActive()
	local index = self:GetIndex()
	local gear_flag = ServerActivityWGData.Instance:GetRCGearFlag(index - 1)
	local is_open = index <= 1 or gear_flag
	self.node_list.toggle_root:SetActive(is_open)
	self.view.toggle.interactable = is_open
end

function FirstRechargeGradeItem:FlushRemind()
	if self.node_list and self.node_list.red_point then
		local grade = self:GetIndex()
		local is_remind = ServerActivityWGData.Instance:GetSCRemind(grade)
		self.node_list.red_point:SetActive(is_remind)
	end
end

--------------------------------------------FirstRechargeSpecialRewardRender---------------------------------------
FirstRechargeSpecialRewardRender = FirstRechargeSpecialRewardRender or BaseClass(BaseRender)

function FirstRechargeSpecialRewardRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = FirstRechargeItemCellRender.New(self.node_list.item_cell)
	end
end

function FirstRechargeSpecialRewardRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FirstRechargeSpecialRewardRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData(self.data)
	-- if self.data.item_data.txt and self.data.item_data.txt ~= "" then
	-- 	self.node_list.text_bg:SetActive(true)
	-- 	self.node_list.text.text.text = self.data.item_data.txt
	-- else
	-- 	self.node_list.text_bg:SetActive(false)
	-- end
end

--------------------------FirstRechargeItemCellRender-----------------------------
FirstRechargeItemCellRender = FirstRechargeItemCellRender or BaseClass(BaseRender)

function FirstRechargeItemCellRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_root)
		self.item_cell:UseNewSelectEffect(true)
	end
end

function FirstRechargeItemCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FirstRechargeItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetFlushCallBack(function ()
		self.item_cell:ResetSelectEffect()
		self.item_cell:SetSelectEffect(self.data.is_get_flag)
		self.item_cell:SetRedPointEff(self.data.reward_flag == BtnState.CanGet)
	end)
	self.item_cell:SetData(self.data.item_data)

	if self.data.jiaobiao and self.data.jiaobiao ~= "" then
		self.node_list.jiaobiao_lbl.text.text = self.data.jiaobiao
		self.node_list.jiaobiao_img:SetActive(true)
	else
		self.node_list.jiaobiao_img:SetActive(false)
	end
end