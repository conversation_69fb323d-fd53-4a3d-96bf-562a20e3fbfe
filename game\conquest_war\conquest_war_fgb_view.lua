-- 冠绝征战 (原冰火夺旗)
function ConquestWarView:LoadIndexCallBackFGBView()
    self:FlushFGBEnterBtnState()

    if not self.fgb_reward_list then
		self.fgb_reward_list = AsyncListView.New(<PERSON><PERSON><PERSON><PERSON>, self.node_list.fgb_reward_list)
        self.fgb_reward_list:SetStartZeroIndex(true)
	end

    local reward_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBWinRewardList()
    self.fgb_reward_list:SetDataList(reward_cfg)
    XUI.AddClickEventListener(self.node_list.go_fgb_btn, BindTool.Bind1(self.OnClickGoToFGB, self))
    XUI.AddClickEventListener(self.node_list.btn_details_tujie, BindTool.Bind(self.OnClickDetailsTuJie, self))
    XUI.AddClickEventListener(self.node_list.btn_reward_show, BindTool.Bind(self.OnClickRewardShow, self))
    self.fgb_activity_change_callback = BindTool.Bind(self.OnFGBActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.fgb_activity_change_callback)
end

function ConquestWarView:ReleaseFGBView()
    if self.fgb_boss_model then
        self.fgb_boss_model:DeleteMe()
        self.fgb_boss_model = nil
    end

    if CountDownManager.Instance:HasCountDown("fgb_time_count_dowm") then
		CountDownManager.Instance:RemoveCountDown("fgb_time_count_dowm")
	end

    if CountDownManager.Instance:HasCountDown("fgb_match_time") then
		CountDownManager.Instance:RemoveCountDown("fgb_match_time")
	end

    if self.fgb_reward_list then
        self.fgb_reward_list:DeleteMe()
		self.fgb_reward_list = nil
	end

    if self.fgb_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.fgb_activity_change_callback)
		self.fgb_activity_change_callback = nil
	end
end

function ConquestWarView:FGBShowIndexCallBack()
    self:FGBCountDown()
end

function ConquestWarView:OnClickDetailsTuJie()
    CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBExplainView(false, false)
end

function ConquestWarView:OnClickRewardShow()
    CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBRewardShowView()
end

function ConquestWarView:OnFlushFGBView(param_t, index)
    if CountDownManager.Instance:HasCountDown("fgb_match_time") then
        CountDownManager.Instance:RemoveCountDown("fgb_match_time")
    end

    local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
    if IsEmptyTable(data) then
        return
    end

    self.node_list.title.text.text = data.activity_title
    self.node_list.open_time_text.text.text = string.format(Language.FlagGrabbingBattlefield.ActivityTime1, data.time_1, data.time_2)
    self.node_list.desc.text.text = data.activity_illustrate

    local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
    local fgb_state = CrossFlagGrabbingBattleFieldWGData.Instance:GetMyFGBState()
    local is_matching = fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHING
    local match_suc = fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHSUC

    if act_open then
        if is_matching then
            self:FGBMatching()
        elseif match_suc then
            --self.node_list.go_fgb_btn_text.text.text = Language.FlagGrabbingBattlefield.EnterActivity
        else
            --self.node_list.go_fgb_btn_text.text.text = Language.FlagGrabbingBattlefield.StartMatching
        end
        self.node_list.go_fgb_wait_flag_text.text.text = Language.FlagGrabbingBattlefield.FGBIsMatchingNow
    else
        --self.node_list.go_fgb_btn_text.text.text = Language.FlagGrabbingBattlefield.StartMatching
        self.node_list.go_fgb_wait_flag_text.text.text = Language.FlagGrabbingBattlefield.NotOpenDesc
    end

    self.node_list.go_fgb_wait_flag:CustomSetActive((is_matching and act_open) or not act_open)
    self.node_list.go_fgb_btn_remind:CustomSetActive(match_suc and act_open)
end

function ConquestWarView:FGBMatching()
    local match_end_time, match_weait_time = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBMatchingTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local time_diff = match_weait_time - (match_end_time - server_time)
    time_diff = time_diff >= 0 and time_diff or 0

    if match_end_time > server_time then
        --self.node_list.go_fgb_btn_text.text.text = string.format(Language.Common.TimeStr8, time_diff)

        self.node_list.go_fgb_wait_flag_text.text.text = string.format(Language.FlagGrabbingBattlefield.FGBIsMatchingNowWaitTime, string.format(Language.Common.TimeStr8, time_diff))

        CountDownManager.Instance:AddCountDown("fgb_match_time",
            function (elapse_time, total_time)
                -- if self.node_list.go_fgb_btn_text then
                --     self.node_list.go_fgb_btn_text.text.text = string.format(Language.Common.TimeStr8, (elapse_time + time_diff))
                -- end

                if self.node_list.go_fgb_wait_flag_text then
                    self.node_list.go_fgb_wait_flag_text.text.text = string.format(Language.FlagGrabbingBattlefield.FGBIsMatchingNowWaitTime, string.format(Language.Common.TimeStr8, (elapse_time + time_diff)))
                end
            end,
            function ()
                -- if self.node_list.go_fgb_btn_text then
                --     self.node_list.go_fgb_btn_text.text.text = Language.FlagGrabbingBattlefield.FGBIsMatchingNow
                -- end

                if self.node_list.go_fgb_wait_flag_text then
                    self.node_list.go_fgb_wait_flag_text.text.text = Language.FlagGrabbingBattlefield.FGBIsMatchingNow
                end
            end,
            match_end_time,
        nil, 1)
    end
end

function ConquestWarView:FGBCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
	if not IsEmptyTable(activity_data) then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local invalid_time = 0
        local desc_str = Language.FlagGrabbingBattlefield.FGBActEndTime
        if activity_data.status == ACTIVITY_STATUS.STANDY then
            invalid_time = activity_data.start_time
            desc_str = Language.FlagGrabbingBattlefield.FGBActStartTime
        elseif activity_data.status == ACTIVITY_STATUS.OPEN then
            invalid_time = activity_data.end_time
        end

        if invalid_time > server_time then
            local time_str = string.format(desc_str, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))

            --self.node_list.fgb_act_time.text.text = time_str
            --self.node_list.fgb_act_time_bg:CustomSetActive(true)
            CountDownManager.Instance:AddCountDown("fgb_time_count_dowm",
                function (elapse_time, total_time)
                    local valid_time = total_time - elapse_time
                    if valid_time > 0 and self.node_list.fgb_act_time then
                        self.login_act_date = TimeUtil.FormatUnixTime2Date()
                        local time_str = string.format(desc_str, TimeUtil.FormatSecondDHM8(valid_time))
                        --self.node_list.fgb_act_time.text.text = time_str
                    end
                end,
                function ()
                    --if self.node_list.fgb_act_time then
                        --self.node_list.fgb_act_time.text.text = ""
                        --self.node_list.fgb_act_time_bg:CustomSetActive(false)
                    --end
                end,
                invalid_time,
                nil, 1)
        else
            --self.node_list.fgb_act_time_bg:CustomSetActive(false)
        end
    else
        --self.node_list.fgb_act_time_bg:CustomSetActive(false)
	end
end

function ConquestWarView:OnClickGoToFGB()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
    local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)

    if not is_open then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.NotOpenAct))
        return
    end

	local open_level = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBOpenLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < open_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FlagGrabbingBattlefield.LevelLimitJoin, RoleWGData.GetLevelString2(open_level)))
		return
	end

    local fgb_state = CrossFlagGrabbingBattleFieldWGData.Instance:GetMyFGBState()
    if fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHING then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FlagGrabbingBattlefield.FGBIsMatchingNow)
    elseif fgb_state == CROSS_FLAG_BATTLE_STATE_TYPE.MATCHSUC then
        CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD)
    else
        CrossFlagGrabbingBattleFieldWGCtrl.Instance:OnCSCrossFlagBattleOperate(CROSS_FLAG_BATTLE_OPERATE_TYPE.MATCH)
    end
end

function ConquestWarView:OnFGBActChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD and self:IsOpen() then
        self:FlushFGBEnterBtnState()

        -- 处理后台强开/强关 导致匹配房间状态信息没及时下发问题
        if status == ACTIVITY_STATUS.OPEN then
            CrossFlagGrabbingBattleFieldWGCtrl.Instance:OnCSCrossFlagBattleOperate(CROSS_FLAG_BATTLE_OPERATE_TYPE.ROOM_INFO)
        else
            self:Flush()
        end
    end
end

function ConquestWarView:FlushFGBEnterBtnState()
    if self.node_list["go_fgb_btn"] then
        --XUI.SetGraphicGrey(self.node_list["go_fgb_btn"], not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD))
        self.node_list["go_fgb_btn"]:SetActive(ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_FLAG_GRABBING_BATTLEFIELD))
    end
end