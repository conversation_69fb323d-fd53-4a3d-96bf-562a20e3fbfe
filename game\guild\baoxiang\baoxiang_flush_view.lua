BaoXiangFlushView = BaoXiangFlushView or BaseClass(SafeBaseView)

function BaoXiangFlushView:__init()
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangFlushView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(30, 34), sizeDelta = Vector2(770, 504)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_flush_view")
end

function BaoXiangFlushView:__delete()

end

function BaoXiangFlushView:ReleaseCallBack()
	if self.flush_list then
		self.flush_list:DeleteMe()
		self.flush_list = nil
	end
end

function BaoXiangFlushView:LoadCallBack()
	self.flush_list = AsyncListView.New(BaoXiangFlushItem,self.node_list["flush_list"])
end

function BaoXiangFlushView:OnFlush()
	local be_req_list_data = GuildBaoXiangWGData.Instance:GetBeReqHelpRefreshUidList()
	self.flush_list:SetDataList(be_req_list_data)

	-- local refresh_num = GuildBaoXiangWGData.Instance:GetDailyTreaureHelpRefreshTimes()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	local refresh_num = info.help_refresh_times or 0
	local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()
	local has_num = other_cfg.help_refresh_times - refresh_num
	self.node_list["has_num"].text.text = string.format(Language.BiZuoBaoXiang.HasNumStr,has_num,other_cfg.help_refresh_times)
end

--------------------BaoXiangFlushItem-----------
BaoXiangFlushItem = BaoXiangFlushItem or BaseClass(BaseRender)

function BaoXiangFlushItem:__init()
	XUI.AddClickEventListener(self.node_list["refuse_btn"], BindTool.Bind(self.ClickRefuseBtn,self))
	XUI.AddClickEventListener(self.node_list["flush_btn"], BindTool.Bind(self.ClickFlushBtn,self))
end

function BaoXiangFlushItem:__delete()
	
end

function BaoXiangFlushItem:OnFlush()
	if not self.data then return end
	local uid = self.data.uid
	local uid_info = GuildBaoXiangWGData.Instance:GetAllItemInfoListByUid(uid)
	local click_refresh_uid = GuildBaoXiangWGData.Instance:GetClickHelpRefreshUid(uid)
	if not IsEmptyTable(uid_info) then
		self.node_list["name_text"].text.text = uid_info.name
		local quality = uid_info.quality
		local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
		local baoxiang_name = treasure_cfg and treasure_cfg.name or ""
		local color = treasure_cfg and treasure_cfg.common_color or 2
		self.node_list["baoxiang_text"].text.text = ToColorStr(baoxiang_name,ITEM_COLOR[color])
		local max_quality = GuildBaoXiangWGData.Instance:GetMaxQuality()
		local has_help = GuildBaoXiangWGData.Instance:GetMyHelpUidListByUid(uid)
		if click_refresh_uid then
			self.node_list["refuse_btn"]:SetActive(false)
			self.node_list["flush_btn"]:SetActive(false)
			self.node_list["has_refresh"]:SetActive(true)
		else
			self.node_list["max_quality"]:SetActive(quality >= max_quality and not has_help)
			self.node_list["refuse_btn"]:SetActive(quality < max_quality and not has_help)
			self.node_list["flush_btn"]:SetActive(quality < max_quality and not has_help)
			self.node_list["has_refresh"]:SetActive(has_help)
		end
	end

end

function BaoXiangFlushItem:ClickRefuseBtn()
	if not self.data then return end
	local uid = self.data.uid
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_ACK,uid,1)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.RefuseSucc)
	GuildBaoXiangWGData.Instance:RemoveBeReqHelpRefreshUid(uid)
	ViewManager.Instance:FlushView(GuideModuleName.BaoXiangFlushView)
end

function BaoXiangFlushItem:ClickFlushBtn()
	local has_fortune = QifuYunShiWGData.Instance:GetToDayHasFortune()
	if not has_fortune then
		local ok_fun = function ()
			ViewManager.Instance:Open(GuideModuleName.QIFU,TabIndex.qifu_yunshi)
		end
		TipWGCtrl.Instance:OpenAlertTips(Language.BiZuoBaoXiang.NotFortuneTips,ok_fun)
		return
	end

	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	local refresh_num = info.help_refresh_times or 0
	local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()
	local has_num = other_cfg.help_refresh_times - refresh_num
	if has_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.NotRefreshTimes)
		return
	end

	if not self.data then return end
	local uid = self.data.uid
	self.node_list["refuse_btn"]:SetActive(false)
	self.node_list["flush_btn"]:SetActive(false)
	self.node_list["has_refresh"]:SetActive(true)
	-- TryDelayCall(self, function ()
		GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH,uid)
		GuildBaoXiangWGData.Instance:SetHelpRefreshUid(uid,true)
	-- end, 1.5, "send_daily_treasure_delay")
	GuildBaoXiangWGData.Instance:SetClickHelpRefreshUid(uid,true)

end