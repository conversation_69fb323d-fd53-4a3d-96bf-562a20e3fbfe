--动态运营活动 限时累充
function MergeActivityView:InitLeiChongView()
	self.leichong_reward_list = nil
	self.jindu_list = nil
end

function MergeActivityView:LoadIndexCallBackLeiChongView()
	if not self.total_model_display then
        self.total_model_display = OperationActRender.New(self.node_list["total_display_model"])
        self.total_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	self:DTYYLeiChongLoadImage()
	self:DTYYFlushLeiChongView()
end

function MergeActivityView:DTYYReleaseLeiChongView()
	if MergeLeiChongRechargeWGData.Instance then
		MergeLeiChongRechargeWGData.Instance:ResetLeiChongModelIndex()
	end

	if self.leichong_reward_list then
		self.leichong_reward_list:DeleteMe()
		self.leichong_reward_list = nil
    end

	if self.total_model_display then
        self.total_model_display:DeleteMe()
        self.total_model_display = nil
	end

    if self.leichong_zhanshi_list then
        for k,v in pairs(self.leichong_zhanshi_list) do
            v:DeleteMe()
        end
		self.leichong_zhanshi_list = nil
    end

	self.jindu_list = nil
    -- if CountDownManager.Instance:HasCountDown("LeiChongCountDown") then
    --     CountDownManager.Instance:RemoveCountDown("LeiChongCountDown")
    -- end
end

function MergeActivityView:DTYYLeiChongLoadImage()
	local model_cfg = MergeLeiChongRechargeWGData.Instance:GetModelInfo()
	if model_cfg == nil then
		return
    end
	self:FlushFashionModelLeiChong(model_cfg)
end

function MergeActivityView:DTYYFlushLeiChongView()
	 --刷新累充list
	self:DTYYFlushLeiChongList()
	local cur_xianyu = MergeLeiChongRechargeWGData.Instance:GetOwnXianYu()
	local slide_percent = MergeLeiChongRechargeWGData.Instance:GetLeiChongProgress(cur_xianyu, self.jindu_list)
	self.node_list["leichong_value"].text.text = cur_xianyu
	self.node_list["leichong_slide"].slider.value = slide_percent

	for i = 1, 4 do
		self.node_list["lingqu_bg_" .. i]:SetActive(cur_xianyu >= tonumber(self.jindu_list[i]))
	end

	self:DTYYLeiChongFlushModel()
    --获取当前档需要充值的仙玉
	-- local need_value, cur_stage_value ,pre_stage_value = MergeLeiChongRechargeWGData.Instance:GetNeedRechargeXianYU()
	-- local slide_percent = 0

	-- if need_value == 0 then
	-- 	slide_percent = 1
	-- else
	-- 	local cur_stage_value_pre = cur_stage_value > 0 and cur_stage_value or 1
	-- 	slide_percent = cur_xianyu / cur_stage_value_pre
	-- end
	
	--self.node_list["total_recharge_value"].text.text = need_value
	--self.node_list["total_slide_value"].text.text = string.format("%d/%d",cur_xianyu, cur_stage_value)
	--self.node_list["total_slide"].slider.value = slide_percent 
	-- self.node_list["total_finish"]:SetActive(MergeLeiChongRechargeWGData.Instance:IsRechargeTargetFinish())
	-- self.node_list["total_leichong"]:SetActive(not MergeLeiChongRechargeWGData.Instance:IsRechargeTargetFinish())
end

function MergeActivityView:OpenIndexCallBackLeiChong()
	MergeLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(MERGE_XIANSHILEICHONG_TYPE.TYPE_INFO)
end

function MergeActivityView:DTYYLeiChongFlushModel()
	local model_cfg = MergeLeiChongRechargeWGData.Instance:GetLeiChongModelCfg()
	if IsEmptyTable(model_cfg) then
		return
	end

	local display_data = {}
	display_data.not_show_active = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg["model_bundle_name"]
    display_data.asset_name = model_cfg["model_asset_name"]
    local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	if model_cfg.model_rot and model_cfg.model_rot ~= "" then
		local rot_list = string.split(model_cfg.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L
    self.total_model_display:SetData(display_data)

    local pos_x, pos_y = 0, 0
	if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPositionXY(self.node_list.total_display_model.rect, pos_x, pos_y)
end

function MergeActivityView:FlushFashionModelLeiChong(param_cfg)
	if IsEmptyTable(param_cfg) then
		return
	end
	local data = {}
	data.reward_show = param_cfg.reward_item
	if param_cfg.jindu_limit ~= nil then
		self.jindu_list = Split(param_cfg.jindu_limit, "|")
		for i = 1, 4 do
			self.node_list["num_cout_" .. i].text.text = tonumber(self.jindu_list[i])
		end
	end

    if param_cfg.reward_position and param_cfg.reward_position ~= nil then
		local show_pos_str_list = Split(param_cfg.reward_position, "#")
		data.reward_show_pos = {}
		for i, pos_str in ipairs(show_pos_str_list) do
			local pos = Split(pos_str, "|")
			data.reward_show_pos[i] = Vector2(tonumber(pos[1]), tonumber(pos[2]))
        end
    end

    if not self.leichong_zhanshi_list then
        self.leichong_zhanshi_list = {}
        for i = 1, 4 do
            if self.node_list["merge_leichong_cell_"..i] then
                self.leichong_zhanshi_list[i] = ItemCell.New(self.node_list["merge_leichong_cell_"..i])
            end
        end
    end
    local idx = 1
    if self.leichong_zhanshi_list then
        for k, v in pairs(data.reward_show) do
            if self.leichong_zhanshi_list[idx] then
                self.leichong_zhanshi_list[idx]:SetData(v)
                --self.node_list["merge_leichong_cell_" .. idx].rect.anchoredPosition = data.reward_show_pos[idx]
                idx = idx + 1
            end
        end
    end
    -- if self.need_leichong_tween then
    --     UITween.CleanAlphaShow(GuideModuleName.MergeActivityView)
    --     self.need_leichong_tween = false
    --     for i = 1, #data.reward_show + 1 do --配置是从0开始的
    --         UITween.FakeHideShow(self.node_list["merge_leichong_cell_"..i])
    --     end
    --     for i = 1, #data.reward_show + 1 do
    --         ReDelayCall(self, function()
    --             if self.node_list and self.node_list["merge_leichong_cell_"..i] then
    --                 UITween.AlphaShow(GuideModuleName.MergeActivityView, self.node_list["merge_leichong_cell_"..i], 0.3, 1, 0.3)
    --                 local end_position = self.node_list["merge_leichong_cell_"..i].transform.anchoredPosition
    --                 self.node_list["merge_leichong_cell_"..i].transform.anchoredPosition = Vector2(0, 0)
    --                 self.node_list["merge_leichong_cell_"..i].transform:DOAnchorPos(end_position, 0.3)
    --             end
    --         end,  0.2 * i , "merge_leichong_cell_" .. i)
    --     end
    -- end
end

function MergeActivityView:DTYYFlushLeiChongList()
	if not self.leichong_reward_list then
		self.leichong_reward_list = AsyncListView.New(MergeLeiChongRender, self.node_list["total_reward_list"])
	end

	local list = MergeLeiChongRechargeWGData.Instance:GetLeiChongRewardList()
	self.leichong_reward_list:SetDataList(list)
end

function MergeActivityView:OnFlushLeiChongTipsAndRule()
	self:SetRuleInfo(Language.MergeLeiChongRecharge.TipsActivityHintShow, Language.MergeLeiChongRecharge.TipsActivityHint)
    self:SetOutsideRuleTips(Language.MergeLeiChongRecharge.LeiChongRechargeState)
    --self.need_leichong_tween = true
    self:DTYYLeiChongLoadImage()
    --self:SetLeiChongCountDown()
end

-- function MergeActivityView:SetLeiChongCountDown(index, end_time)
-- 	local activity_type = MergeActivityWGData.Instance:GetCurSelectActivityType(TabIndex.merge_activity_2110)
-- 	local act_end_time = MergeActivityWGData.Instance:GetActivityInValidTime(activity_type)

-- 	if CountDownManager.Instance:HasCountDown("LeiChongCountDown") then
--         CountDownManager.Instance:RemoveCountDown("LeiChongCountDown")
--     end

--     if act_end_time ~= 0 then
-- 		self.node_list.merge_leichong_time_txt:SetActive(true)
--         local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
--         if time > 0 then
--             CountDownManager.Instance:AddCountDown("LeiChongCountDown",
--                     BindTool.Bind(self.LeichongCommonUpdateTime, self),
--                     BindTool.Bind(self.LeichongCommonCompleteTime, self), nil, time, 1)
--         end
--     end
-- end

-- function MergeActivityView:LeichongCommonUpdateTime(elapse_time, total_time)
-- 	local temp_seconds = GameMath.Round(total_time - elapse_time)
--     local str = TimeUtil.FormatDToHAndMS(temp_seconds)
--     if self.node_list and self.node_list.merge_leichong_time_txt then
--         self.node_list.merge_leichong_time_txt.text.text = str
--     end
-- end

-- function MergeActivityView:LeichongCommonCompleteTime(elapse_time, total_time)
--     if self.node_list and self.node_list.merge_leichong_time_txt then
--         self.node_list.merge_leichong_time_txt.text.text = "00:00:00"
--     end
-- end


----------------------------- MergeLeiChongRender ----------------------
MergeLeiChongRender = MergeLeiChongRender or BaseClass(BaseRender)
function MergeLeiChongRender:__init()
	
end

function MergeLeiChongRender:LoadCallBack()
	self.item_list = {}
	self.btn_lingqu = self.node_list["btn_lingqu"]
	self.btn_recharge = self.node_list["btn_recharge"]
	self.btn_lingqu.button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	self.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickRechargeHnadler, self))
end

function MergeLeiChongRender:ReleaseCallBack()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			v:DeleteMe()
		end
		self.item_list = nil
		self.btn_lingqu = nil
		self.btn_recharge = nil
	end
end

function MergeLeiChongRender:OnFlush()
	if not self.data then
		return
	end 

	--是否显示返利
	local is_show = self.data.cfg.special_frame == SPECIAL_FRAME.CAN_SHOW
	--self.node_list["total_fanlidi"]:SetActive(is_show)
	--self.node_list["jinshukuang"]:SetActive(is_show)
	--self.node_list["biaoqian"]:SetActive(is_show)
	if is_show then
		--self.node_list["total_fanli_txt"].text.text = self.data.cfg.special_content
		--self.node_list["biaoqian_num"].text.text = self.data.cfg.reward_item[0].num
	end

	local cur_xianyu = MergeLeiChongRechargeWGData.Instance:GetOwnXianYu()
	local color = cur_xianyu >= self.data.cfg.stage_value and COLOR3B.L_GREEN or COLOR3B.L_RED  --<font color='#ffff00'>10万铜币</font>
	local str = string.format("%d/%d", cur_xianyu, self.data.cfg.stage_value)
	self.node_list["recharge_value"].text.text = ToColorStr(str, color)
	--self.node_list["recharge_tip"].text.text = string.format(Language.Activity.LeiChongRecharge, NumberToChinaNumber(self.data.cfg.ID))
	self.node_list["btn_recharge"]:SetActive(cur_xianyu < self.data.cfg.stage_value and self.data.receive_state == 0)
	self.node_list["btn_lingqu"]:SetActive(cur_xianyu >= self.data.cfg.stage_value and self.data.receive_state == 1)
	self.node_list["btn_yilingqu"]:SetActive(self.data.receive_state == 2)

	if self.data.cfg.reward_item then
		for k, v in pairs(self.item_list) do
			v:SetData(nil)
		end

		for i=1,7 do
			if self.node_list["ph_cell_".. i] then
				self.node_list["ph_cell_".. i]:SetActive(self.data.cfg.reward_item[i -1] ~= nil)
			end
		end

		for k, v in pairs(self.data.cfg.reward_item) do
			if not self.item_list[k+1] then
				if self.node_list["ph_cell_"..(k+1)] then
					self.item_list[k+1] = ItemCell.New(self.node_list["ph_cell_"..k+1])
				end
			end
			self.item_list[k+1]:SetData(v)
			self.item_list[k+1]:SetRedPointEff(cur_xianyu >= self.data.cfg.stage_value and self.data.receive_state == 1)
			self.item_list[k+1]:SetLingQuVisible(self.data.receive_state == 2)
		end

	end
end

function MergeLeiChongRender:OnClickRewardHnadler(sender)
	MergeLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(MERGE_XIANSHILEICHONG_TYPE.TYPE_DRAW,self.data.cfg.ID)
end

function MergeLeiChongRender:OnClickRechargeHnadler(sender)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end