﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_LightBakingOutputWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(UnityEngine.LightBakingOutput), null);
		<PERSON><PERSON>RegFunction("New", _CreateUnityEngine_LightBakingOutput);
		<PERSON><PERSON>un<PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("probeOcclusionLightIndex", get_probeOcclusionLightIndex, set_probeOcclusionLightIndex);
		<PERSON><PERSON>("occlusionMaskChannel", get_occlusionMaskChannel, set_occlusionMaskChannel);
		<PERSON><PERSON>("lightmapBakeType", get_lightmapBakeType, set_lightmapBakeType);
		L<PERSON>("mixedLightingMode", get_mixedLightingMode, set_mixedLightingMode);
		<PERSON><PERSON>("isBaked", get_isBaked, set_isBaked);
		L<PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_LightBakingOutput(IntPtr L)
	{
		UnityEngine.LightBakingOutput obj = new UnityEngine.LightBakingOutput();
		ToLua.PushValue(L, obj);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_probeOcclusionLightIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			int ret = obj.probeOcclusionLightIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index probeOcclusionLightIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_occlusionMaskChannel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			int ret = obj.occlusionMaskChannel;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index occlusionMaskChannel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lightmapBakeType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			UnityEngine.LightmapBakeType ret = obj.lightmapBakeType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lightmapBakeType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mixedLightingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			UnityEngine.MixedLightingMode ret = obj.mixedLightingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mixedLightingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isBaked(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			bool ret = obj.isBaked;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isBaked on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_probeOcclusionLightIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.probeOcclusionLightIndex = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index probeOcclusionLightIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_occlusionMaskChannel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.occlusionMaskChannel = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index occlusionMaskChannel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lightmapBakeType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			UnityEngine.LightmapBakeType arg0 = (UnityEngine.LightmapBakeType)ToLua.CheckObject(L, 2, typeof(UnityEngine.LightmapBakeType));
			obj.lightmapBakeType = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lightmapBakeType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_mixedLightingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			UnityEngine.MixedLightingMode arg0 = (UnityEngine.MixedLightingMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.MixedLightingMode));
			obj.mixedLightingMode = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mixedLightingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isBaked(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LightBakingOutput obj = (UnityEngine.LightBakingOutput)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isBaked = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isBaked on a nil value");
		}
	}
}

