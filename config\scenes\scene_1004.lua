return {
    id = 1004,
    name = "临仙城",
    scene_type = 0,
    bundle_name = "scenes/map/a3_yw_xuedi_main",
    asset_name = "A3_YW_XueDi_Main",
    width = 508,
    height = 381,
    origin_x = 10,
    origin_y = 27,
    levellimit = 50,
    is_forbid_pk = 0,
    skip_loading = 0,
    show_weather = 0,
    scene_broadcast = 0,
    scenex = 94,
    sceney = 278,
    npcs = {
		{id=10401, x=102, y=256, rotation_y = 330, is_walking = 0, paths = {}},
		{id=10402, x=97, y=189, rotation_y = 350, is_walking = 0, paths = {}},
		{id=10403, x=87, y=179, rotation_y = 21.99999, is_walking = 0, paths = {}},
		{id=10404, x=187, y=118, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10405, x=187, y=108, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10406, x=246, y=112, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10407, x=333, y=113, rotation_y = 270, is_walking = 0, paths = {}},
		{id=10408, x=332, y=165, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10409, x=328, y=183, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10410, x=332, y=216, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10411, x=298, y=300, rotation_y = 157.2, is_walking = 0, paths = {}},
		{id=10412, x=305, y=321, rotation_y = 195, is_walking = 0, paths = {}},
		{id=10413, x=316, y=109, rotation_y = 296, is_walking = 0, paths = {}},
		{id=10414, x=79, y=183, rotation_y = 30.00001, is_walking = 0, paths = {}},
		{id=10415, x=336, y=166, rotation_y = 186, is_walking = 0, paths = {}},
		{id=10416, x=330, y=209, rotation_y = 221, is_walking = 0, paths = {}},
		{id=10417, x=323, y=217, rotation_y = 214, is_walking = 0, paths = {}},
		{id=10418, x=314, y=318, rotation_y = 0, is_walking = 0, paths = {}},
		{id=10420, x=135, y=117, rotation_y = 270, is_walking = 0, paths = {}},
    },
    monsters = {
		{id=10402, x=58, y=139},
		{id=10402, x=55, y=134},
		{id=10402, x=51, y=143},
		{id=10402, x=61, y=135},
		{id=10402, x=55, y=144},
		{id=10402, x=52, y=140},
		{id=10402, x=61, y=141},
		{id=10402, x=64, y=137},
    },
    doors = {
		{id=1004, type=0, level=0, target_scene_id=1003, target_door_id=1005, offset={0, 0, 0}, rotation={0, 0, 0}, x=92, y=288, door_target_x=139, door_target_y=237},
		{id=1006, type=0, level=0, target_scene_id=1005, target_door_id=1009, offset={0, 0, 0}, rotation={0, 0, 0}, x=335, y=296, door_target_x=240, door_target_y=282},
		{id=10002, type=0, level=0, target_scene_id=1000, target_door_id=10000, offset={0, 0, 0}, rotation={0, 0, 0}, x=287, y=319, door_target_x=111, door_target_y=35},
    },
    gathers = {
		{id=1301, x=81, y=178, disappear_after_gather=0},
		{id=1302, x=328, y=217, disappear_after_gather=0},
		{id=1303, x=313, y=329, disappear_after_gather=0},
		{id=1304, x=310, y=322, disappear_after_gather=0},
		{id=1305, x=149, y=112, disappear_after_gather=0},
		{id=1307, x=172, y=112, disappear_after_gather=0},
		{id=1308, x=250, y=112, disappear_after_gather=0},
		{id=1309, x=309, y=324, disappear_after_gather=0},
		{id=1310, x=292, y=324, disappear_after_gather=0},
		{id=1801, x=317, y=315, disappear_after_gather=0},
    },
    jumppoints = {
		{id=0, target_id=1, range=5, x=328, y=225, jump_type=1, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=1, target_id=-1, range=5, x=312, y=282, jump_type=0, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=2, target_id=3, range=5, x=320, y=271, jump_type=1, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=3, target_id=-1, range=5, x=328, y=215, jump_type=0, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
    },
    fences = {
    },
    effects = {
    },
    sounds = {
    },
    scene_way_points = {
		[0] = {id=0, target_id={1, },x=94, y=277},
		[1] = {id=1, target_id={0, 2, },x=98, y=255},
		[2] = {id=2, target_id={1, 3, },x=100, y=227},
		[3] = {id=3, target_id={2, 4, },x=94, y=209},
		[4] = {id=4, target_id={3, 5, },x=100, y=194},
		[5] = {id=5, target_id={4, 6, },x=82, y=178},
		[6] = {id=6, target_id={5, 7, },x=55, y=138},
		[7] = {id=7, target_id={6, 8, },x=58, y=129},
		[8] = {id=8, target_id={7, 9, },x=76, y=113},
		[9] = {id=9, target_id={8, 10, },x=328, y=113},
		[10] = {id=10, target_id={9, 11, },x=328, y=293},
		[11] = {id=11, target_id={10, },x=305, y=318},
	},
    mask = "XQAAQAAAAOf8l/8ASV3Alx8OkLTskt/Brm8/ENrN97SWmr9Gjf8PiBCeED+mt0FaiFt523naBOh/JhHoPRZktVH98T0CyH9OhDY108QdcdTia5CQ4NsaYcuuP0Hz50QhfifYSgxq+D30gO0M138YEL08T6qVE7YgpTpXo1Iy/0ktJpf6DAZOPk+k33/CUYibXJ5qD12kDsn9Nk45iq5jNY9Zro70BRwUtgfnbPMAz6bGlYDx/h5rZtS1DAhuoxLV95SXfNUNF0QRZ3acYGjRSktdrmgmUO1a9Pb4om4yRMy4EFFrw+dSN0AsSqYVNDg8xV+gPMQ+T4NM63WjN1VrT0L6V4Cq0D2n8XROPGN/1kcrnWbyn9dwane+ocEk3eFJWpeHlnUY901I9sMA0HriRGKblai5dnBQ4U39O5Evh2mxoLvXvkgiR0CKlpdZAImIpKPAG6DmvBYSvdf4g9M86bbSo6gi4e/yoTivSPllqGswLHZtKF3gdKVSBwiTL/b4J31CzBGiZl2zGuYPkSl/SQP1HDTUsLhMqeA9uqnEGXtIzjvb2RuADuWJjovxOMw8XExyb7KxOE+lfbjTvCe0L0IZnQYArvqvyEhSYG8t6EzLMvvoedvORVMBMzCALXrXPqhpX+gknojIwoeAcbLE9Z/SKRcvLV2A9VLBIYSbGTB7X1WARj4qM2PBGbZkYAKKy5Tt5nEf3/jSa/drX41FHlox8NMvXXjHI2kFRG+GrhU0/6EuI+0ZiO1a4kmyw95L6HwTzhLSNFjPCvnN/Bo9AYM0fcdoxR2c4fQISEwLYoiABzz8EecDMKnZkwMQVa/woxhdqyfmY7vwITMYbG7B5islhdM9fpvPd5bOagET2ETWGRGzpK7T7oOWF+hvAFy6GC5UwgDtIVidNFJqzprcZ6+9wHvIxvNcP5YWmaldPtgrSBiYiV4dEXDHdGqqDOhcpHJyJHxvJhzQkfJxm3nXf2C0Jj6RimcFANPPP/XCNf1lFz+gA83SK0fhurXsqlvxapQRXj9Bz2DtCf+st/UKhEc5R//fMQ27wRL0ZfyW+L2fq7C72F2uhWR0qeZHSv+YNGtbFunVuGxCzdhV7NhdqX4DP3jNx9qx3NMXyzez8tOn8aaLBaCyhug6K/1xXeIQiZmQwmItCb8Nzm9NMci1NPC7WqIVYBBp6YNLuRRyrb4zJHL020qvgJ+Ir03pnxJ6Xy5uEf9Hfw1a1N7XOykSeh6ypvlho0JyGFn1nb7mFVtOKaqU2SPa+LvwgTxN7rw62J+7Y+GkpjvKiNZTGTVERzBbIB+oqvkCXsMIjRdjqMMdP8BOsLAX/HaXLjKmVH11LnCTKoGz5r8fjOBINNWbsF+B+0vdfxND2YgNgQkdNsxuEzCjdDpbmsdO+PqeDVueI9E6n1RjwiF2+dYJFRGPmigBQPQpBAcctaXh5/C83PS/nfPVyxr49t8ZXopO6vQXrKCnJ84TisfIs95GiBkk4HhE4SlYSHok4sfC4BGgOqqXsdsi5ODa4MKHcKD6bfW30yybitvrDfQwAZFSHiBvaFCoeySrYJDpU5r8JxSU3UPXmAdUKYjobnyV6X93ptWv7WgK5w0Zc/Jkb9k0FQiBO/YP07frX0BiV8ybEG7ZfOGaht2PBz2peJ9yX///ZY+9AA==",
}