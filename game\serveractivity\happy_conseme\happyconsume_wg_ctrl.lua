require("game/serveractivity/happy_conseme/happyconsume_wg_data")
require("game/serveractivity/happy_conseme/happyconsume_view")

-- 充值扭蛋
HappyConsumeWGCtrl = HappyConsumeWGCtrl or BaseClass(BaseWGCtrl)

function HappyConsumeWGCtrl:__init()
	if HappyConsumeWGCtrl.Instance then
        error("[HappyConsumeWGCtrl]:Attempt to create singleton twice!")
	end
	HappyConsumeWGCtrl.Instance = self

	self.data = HappyConsumeWGData.New()
	self.view = HappyConsumeView.New()

	self:RegisterAllProtocals()

	-- Remind.Instance:RegisterOneRemind(RemindId.happyconsume, BindTool.Bind1(self.CheckHappyConsume, self))
end

function HappyConsumeWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	HappyConsumeWGCtrl.Instance = nil
end

function HappyConsumeWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCHappyConsumeInfo, "OnSCHappyConsumeInfo")

	self:BindGlobalEvent(LoginEventType.LOADING_COMPLETED, BindTool.Bind1(self.SendAllInfoReq, self))
end

function HappyConsumeWGCtrl:SendAllInfoReq()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_HAPPY_CUNSUME) then
		return
	end

	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_HAPPY_CUNSUME,
		opera_type = RA_HAPPY_CONSUME_REQ_TYPE.RA_HAPPY_CONSUME_REQ_TYPE_REQ_INFO
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function HappyConsumeWGCtrl:OnSCHappyConsumeInfo(protocol)
	self.data:SetTotalJiFen(protocol.happy_consume_count,protocol.activity_open_day)
	self.data:SetComsumeExchangeFlag(protocol.happy_consume_exchange_flag)
	self.view:Flush()
	-- Remind.Instance:DoRemind(RemindId.happyconsume)
end


function HappyConsumeWGCtrl:Open(tab_index)
	self.view:Open(tab_index)
end

function HappyConsumeWGCtrl:CheckHappyConsume()
	return self.data:GetHappyConsumeCNum()
end
