-- K-跨服云购.xls
local item_table={
[1]={item_id=27521,num=1,is_bind=1},
[2]={item_id=27536,num=1,is_bind=1},
[3]={item_id=26361,num=1,is_bind=1},
[4]={item_id=26362,num=1,is_bind=1},
[5]={item_id=27533,num=1,is_bind=1},
[6]={item_id=27534,num=1,is_bind=1},
[7]={item_id=27535,num=1,is_bind=1},
[8]={item_id=22576,num=1,is_bind=1},
[9]={item_id=27518,num=1,is_bind=1},
[10]={item_id=27519,num=1,is_bind=1},
[11]={item_id=27520,num=1,is_bind=1},
[12]={item_id=22012,num=1,is_bind=1},
[13]={item_id=26344,num=1,is_bind=1},
[14]={item_id=26340,num=1,is_bind=1},
[15]={item_id=26978,num=1,is_bind=1},
[16]={item_id=22611,num=1,is_bind=1},
[17]={item_id=31002,num=1,is_bind=1},
[18]={item_id=31003,num=1,is_bind=1},
[19]={item_id=31004,num=1,is_bind=1},
[20]={item_id=31005,num=1,is_bind=1},
[21]={item_id=31006,num=1,is_bind=1},
[22]={item_id=31007,num=1,is_bind=1},
[23]={item_id=31008,num=1,is_bind=1},
[24]={item_id=31009,num=1,is_bind=1},
[25]={item_id=31010,num=1,is_bind=1},
[26]={item_id=31011,num=1,is_bind=1},
[27]={item_id=31012,num=1,is_bind=1},
[28]={item_id=31013,num=1,is_bind=1},
[29]={item_id=31014,num=1,is_bind=1},
[30]={item_id=31015,num=1,is_bind=1},
[31]={item_id=31016,num=1,is_bind=1},
[32]={item_id=31017,num=1,is_bind=1},
[33]={item_id=31018,num=1,is_bind=1},
[34]={item_id=22005,num=1,is_bind=1},
[35]={item_id=26363,num=1,is_bind=1},
[36]={item_id=31001,num=1,is_bind=1},
}
return {
other={
{}},

cross_cloud_random_reward={
{weight=25,},
{seq=1,weight=25,reward_item=item_table[1],},
{seq=2,weight=900,reward_item=item_table[2],},
{seq=3,weight=300,reward_item=item_table[3],},
{seq=4,weight=150,reward_item=item_table[4],},
{seq=5,weight=100,reward_item=item_table[5],},
{seq=6,weight=900,reward_item=item_table[6],},
{seq=7,weight=300,reward_item=item_table[7],},
{seq=8,weight=150,reward_item=item_table[8],},
{seq=9,weight=167,reward_item=item_table[9],},
{seq=10,weight=75,reward_item=item_table[10],},
{seq=11,weight=75,reward_item=item_table[11],},
{seq=12,weight=400,reward_item=item_table[12],},
{seq=13,reward_item=item_table[13],},
{seq=14,reward_item=item_table[14],},
{seq=15,reward_item=item_table[15],},
{seq=16,weight=600,reward_item=item_table[16],},
{shop_type=1,weight=25,},
{shop_type=1,seq=1,weight=25,reward_item=item_table[1],},
{shop_type=1,seq=2,weight=900,reward_item=item_table[2],},
{shop_type=1,seq=3,weight=300,reward_item=item_table[3],},
{shop_type=1,seq=4,weight=150,reward_item=item_table[4],},
{shop_type=1,seq=5,weight=100,reward_item=item_table[5],},
{shop_type=1,seq=6,weight=900,reward_item=item_table[6],},
{shop_type=1,seq=7,weight=300,reward_item=item_table[7],},
{shop_type=1,seq=8,weight=150,reward_item=item_table[8],},
{shop_type=1,seq=9,weight=167,reward_item=item_table[9],},
{shop_type=1,seq=10,weight=75,reward_item=item_table[10],},
{shop_type=1,seq=11,weight=75,reward_item=item_table[11],},
{shop_type=1,seq=12,weight=400,reward_item=item_table[12],},
{shop_type=1,seq=13,reward_item=item_table[13],},
{shop_type=1,seq=14,reward_item=item_table[14],},
{shop_type=1,seq=15,reward_item=item_table[15],},
{shop_type=1,seq=16,weight=600,reward_item=item_table[16],}},

cross_cloud_prize={
{},
{seq=1,reward_item=item_table[17],reward_name="云购大奖2",},
{seq=2,reward_item=item_table[18],reward_name="云购大奖3",},
{seq=3,reward_item=item_table[19],reward_name="云购大奖4",},
{seq=4,reward_item=item_table[20],reward_name="云购大奖5",},
{seq=5,reward_item=item_table[21],reward_name="云购大奖6",},
{seq=6,reward_item=item_table[22],reward_name="云购大奖7",},
{seq=7,reward_item=item_table[23],reward_name="云购大奖8",},
{seq=8,reward_item=item_table[24],reward_name="云购大奖9",},
{shop_type=1,reward_item=item_table[25],reward_name="云购大奖I",},
{shop_type=1,seq=1,reward_item=item_table[26],reward_name="云购大奖II",},
{shop_type=1,seq=2,reward_item=item_table[27],reward_name="云购大奖III",},
{shop_type=1,seq=3,reward_item=item_table[28],reward_name="云购大奖IV",},
{shop_type=1,seq=4,reward_item=item_table[29],reward_name="云购大奖V",},
{shop_type=1,seq=5,reward_item=item_table[30],reward_name="云购大奖VI",},
{shop_type=1,seq=6,reward_item=item_table[31],reward_name="云购大奖VII",},
{shop_type=1,seq=7,reward_item=item_table[32],reward_name="云购大奖VIII",},
{shop_type=1,seq=8,reward_item=item_table[33],reward_name="云购大奖IX",}},

cross_cloud_prize_num={
{},
{buy_times=50,draw_num=2,},
{buy_times=90,draw_num=3,},
{buy_times=140,draw_num=4,},
{buy_times=200,draw_num=5,},
{shop_type=1,},
{shop_type=1,buy_times=50,draw_num=2,},
{shop_type=1,buy_times=90,draw_num=3,},
{shop_type=1,buy_times=140,draw_num=4,},
{shop_type=1,buy_times=200,draw_num=5,}},

cross_cloud_times_limit={
{buy_times=15,},
{vip_level=1,buy_times=20,},
{vip_level=2,buy_times=20,},
{vip_level=3,buy_times=20,},
{vip_level=4,buy_times=35,},
{vip_level=5,buy_times=40,},
{vip_level=6,},
{vip_level=7,},
{vip_level=8,},
{vip_level=9,},
{vip_level=10,},
{vip_level=11,},
{vip_level=12,},
{shop_type=1,buy_times=15,},
{shop_type=1,vip_level=1,buy_times=20,},
{shop_type=1,vip_level=2,buy_times=20,},
{shop_type=1,vip_level=3,buy_times=20,},
{shop_type=1,vip_level=4,buy_times=35,},
{shop_type=1,vip_level=5,buy_times=40,},
{shop_type=1,vip_level=6,},
{shop_type=1,vip_level=7,},
{shop_type=1,vip_level=8,},
{shop_type=1,vip_level=9,},
{shop_type=1,vip_level=10,},
{shop_type=1,vip_level=11,},
{shop_type=1,vip_level=12,}},

quick_buy={
{},
{num_each_time=5,buy_cost=50,},
{num_each_time=10,buy_cost=100,},
{num_each_time=20,buy_cost=200,},
{shop_type=1,},
{shop_type=1,num_each_time=5,buy_cost=50,},
{shop_type=1,num_each_time=10,buy_cost=100,},
{shop_type=1,num_each_time=20,buy_cost=200,}},

other_default_table={draw_time=2100,cross_cloud_price_gold=10,cross_cloud_price_bind_gold=10,buy_reward=item_table[34],no_limit_time=1200,},

cross_cloud_random_reward_default_table={shop_type=0,seq=0,weight=2200,reward_item=item_table[35],},

cross_cloud_prize_default_table={shop_type=0,seq=0,reward_item=item_table[36],reward_name="云购大奖1",reward_num=200,},

cross_cloud_prize_num_default_table={shop_type=0,buy_times=1,draw_num=1,},

cross_cloud_times_limit_default_table={shop_type=0,vip_level=0,buy_times=45,},

quick_buy_default_table={shop_type=0,num_each_time=1,buy_cost=10,}

}

