require("game/cash_point/cash_point_wg_data")
require("game/cash_point/cash_point_view")

CashPointWGCtrl = CashPointWGCtrl or BaseClass(BaseWGCtrl)

function CashPointWGCtrl:__init()
	if CashPointWGCtrl.Instance then
		<PERSON>rror<PERSON><PERSON>("[CashPointWGCtrl] attempt to create singleton twice!")
		return
	end

	CashPointWGCtrl.Instance = self
	self.data = CashPointWGData.New()
    self.view = CashPointView.New(GuideModuleName.CashPointView)

	self.cash_point_change = BindTool.Bind1(self.FlushCashPoint, self)
	RoleWGData.Instance:NotifyAttrChange(self.cash_point_change, {"cash_point"})
end

function CashPointWGCtrl:__delete()
	CashPointWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.cash_point_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.cash_point_change)
		self.cash_point_change = nil
	end
end

function CashPointWGCtrl:FlushCashPoint()
	if self.view:IsOpen() then
		self.view:FlushCashPointNum()
	end
end
