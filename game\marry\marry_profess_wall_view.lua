local SELF_MODEL_POS_X = -3.0
local LOVE_MODEL_POS_X = 0.5

function MarryView:InitProfessWallView()
	XUI.AddClickEventListener(self.node_list["btn_select_profess"], BindTool.Bind(self.OpenSelectProfess, self)) --我要表白
	XUI.AddClickEventListener(self.node_list["btn_goto_confession"], BindTool.Bind(self.ClickGotoConfession, self)) --表白记录
	XUI.AddClickEventListener(self.node_list["ben_profess_help"], BindTool.Bind(self.OpenHelp, self))
end

function MarryView:DeleteProfessWallView()
	if self.pw_self_model then
		self.pw_self_model:DeleteMe()
		self.pw_self_model = nil
	end

	if self.pw_love_model then
		self.pw_love_model:DeleteMe()
		self.pw_love_model = nil
	end
end

--打开面板
function MarryView:ShowProfessIndexCallBack()
	MarryWGCtrl.Instance:SendProfessWallReq(PROFESS_WALL_REQ_TYPE.PROFESS_WALL_REQ_LEVEL_INFO)
	self.init_progess = true

	--模型
	self:FlushDisplay()
end

function MarryView:CloseView()
	self.is_auto = false
end

function MarryView:FlushDisplay()
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local pos_x = main_role_vo.sex == GameEnum.MALE and LOVE_MODEL_POS_X or SELF_MODEL_POS_X
	if not self.pw_self_model then
		local trans = {}
		trans[1] = main_role_vo.sex == GameEnum.MALE and self.node_list["profess_display_left"] or self.node_list["profess_display_right"]

		-- self.pw_self_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = trans[1],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = true,
		-- }
		
		-- self.pw_self_model:SetRenderTexUI3DModel(display_data)
		-- -- self.pw_self_model:SetUI3DModel( trans[1].transform,  trans[1].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		-- self:AddUiRoleModel(self.pw_self_model)

		-- 模型展示
		if trans[1] then
			self.pw_self_model = RoleModel.New()
			self.pw_self_model:SetUISceneModel(trans[1].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
			self:AddUiRoleModel(self.pw_self_model, TabIndex.marry_profess_wall)
		end
	else
		self.pw_self_model:PlayLastAction()
	end

	local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource)
    if self.pw_self_model then
		local extra_role_model_data = {
            prof = main_role_vo.prof,
            sex = main_role_vo.sex,
        }
        self.pw_self_model:SetRoleResid(res_id, nil, extra_role_model_data)
        self.pw_self_model:SetWeaponModelFakeRemove()
        self.pw_self_model:SetUSAdjustmentNodeLocalPosition(pos_x, 0, 0)
    end

	if main_role_vo.lover_uid > 0 then
		BrowseWGCtrl.Instance:BrowRoelInfo(main_role_vo.lover_uid, BindTool.Bind1(self.PWLoverModelShow, self))
	elseif self.pw_love_model then
		self.pw_love_model:SetVisible(false)
    end

	if self.pw_love_model then
		self.pw_love_model:PlayLastAction()
	end
    
    self:FlushProposeRemind()
end

function MarryView:FlushProposeRemind()
    if self.node_list.propose_redpoint then
        self.node_list.propose_redpoint:SetActive(MarryWGData.Instance:GetProposeRemind() == 1)
    end
end

function MarryView:PWLoverModelShow(protocol)
    if not self.node_list or not self.node_list.profess_display_left then
        return
    end
	if not protocol then
		print_error("protocol error！！")
		return
	end

	MarryWGData.Instance:SetLoverInfo2(protocol)
	local role_base_info_ack = MarryWGData.Instance:GetLoverInfo2()
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local d_face_res = role_base_info_ack.appearance.default_face_res_id
    local d_hair_res = role_base_info_ack.appearance.default_hair_res_id
    local d_body_res = role_base_info_ack.appearance.default_body_res_id
    local trans = {}
    trans[1] = main_role_vo.sex ~= GameEnum.MALE and self.node_list["profess_display_left"] or self.node_list["profess_display_right"]
	local pos_x = main_role_vo.sex ~= GameEnum.MALE and LOVE_MODEL_POS_X or SELF_MODEL_POS_X
	if not self.pw_love_model then
		-- self.pw_love_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = trans[1],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = true,
		-- }
		
		-- self.pw_love_model:SetRenderTexUI3DModel(display_data)
		-- -- self.pw_love_model:SetUI3DModel(trans[1].transform, trans[1].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		-- self:AddUiRoleModel(self.pw_love_model)

		-- 模型展示
		self.pw_love_model = RoleModel.New()
		self.pw_love_model:SetUISceneModel(trans[1].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.pw_love_model, TabIndex.marry_profess_wall)
	else
		--self.pw_love_model:SetVisible(true)
		self.pw_love_model:PlayLastAction()
	end

	local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource)
    if self.pw_love_model then
		local extra_role_model_data = {
			prof = role_base_info_ack.prof,
			sex = role_base_info_ack.sex,
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
		}

        self.pw_love_model:SetRoleResid(res_id, nil, extra_role_model_data)
        self.pw_love_model:SetWeaponModelFakeRemove()
        self.pw_love_model:SetUSAdjustmentNodeLocalPosition(pos_x, 0, 0)
    end
    -- local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_weapon = true, ignore_halo = true,}
	-- self.pw_love_model:SetModelResInfo(role_base_info_ack, ignore_table)
	-- self.pw_love_model:SetUSAdjustmentNodeLocalPosition(pos_x, 0, 0)
	-- self.pw_love_model:SetWeaponModelFakeRemove()
end

function MarryView:FlushProfessWallView()
	self:FlushLeftContent()
	self:FlushRightContent()
	self:SetBiaoBaiLevel()
end

--设置我的属性展示
function MarryView:FlushSelfAttr()
	--获取服务器数据
	local profess_level_info = MarryWGData.Instance:GetProfessLevelInfo()
	if profess_level_info == nil then
		return
	end

	local curren_level = profess_level_info.my_grade
	--获取当前等级
	local  profess_level_cfg_info = MarryWGData.Instance:GetProfessCfgByLevel(curren_level)
	local  profess_level_cfg_info1 = MarryWGData.Instance:GetProfessCfgByLevel(curren_level + 1)
	if nil == profess_level_cfg_info then
		return
	end

	local all_per_attr = 1 --+ MarryWGData.Instance:GetMarryAllLoveAttr() / 100
	profess_level_cfg_info = AttributeMgr.GetAttributteByClass(profess_level_cfg_info)
	--设置当前属性
	profess_level_cfg_info.gong_ji = math.floor(profess_level_cfg_info.gong_ji * all_per_attr)
	profess_level_cfg_info.max_hp = math.floor(profess_level_cfg_info.max_hp * all_per_attr)
	profess_level_cfg_info.fang_yu = math.floor(profess_level_cfg_info.fang_yu * all_per_attr)
	self.node_list["pw_gongji_value"].text.text = profess_level_cfg_info.gong_ji
	self.node_list["pw_hp_value"].text.text     = profess_level_cfg_info.max_hp
	self.node_list["pw_fangyu_value"].text.text = profess_level_cfg_info.fang_yu
	self.node_list["pw_mingzhong_value"].text.text = profess_level_cfg_info.po_jia
	self.node_list["layout_next_attr"]:SetActive(false)
	if profess_level_cfg_info1 then
		self.node_list["layout_next_attr"]:SetActive(true)
		profess_level_cfg_info1 = AttributeMgr.GetAttributteByClass(profess_level_cfg_info1)
		profess_level_cfg_info1.gong_ji = math.floor(profess_level_cfg_info1.gong_ji * all_per_attr)
		profess_level_cfg_info1.max_hp = math.floor(profess_level_cfg_info1.max_hp * all_per_attr)
		profess_level_cfg_info1.fang_yu = math.floor(profess_level_cfg_info1.fang_yu * all_per_attr)
		profess_level_cfg_info1.po_jia = math.floor(profess_level_cfg_info1.po_jia * all_per_attr)
		self.node_list["pw_gongji_add"].text.text = profess_level_cfg_info1.gong_ji - profess_level_cfg_info.gong_ji
		self.node_list["pw_hp_add"].text.text     = profess_level_cfg_info1.max_hp - profess_level_cfg_info.max_hp
		self.node_list["pw_fangyu_add"].text.text = profess_level_cfg_info1.fang_yu - profess_level_cfg_info.fang_yu
		self.node_list["pw_mingzhong_add"].text.text = profess_level_cfg_info1.po_jia - profess_level_cfg_info.po_jia
	end


	local main_vo = GameVoManager.Instance:GetMainRoleVo()

	if main_vo.lover_uid <= 0 then
		--没结婚
		local capability = AttributeMgr.GetCapability(profess_level_cfg_info)
		self.node_list["pw_zhandouli"].text.text = capability
		return
	end

	--获取伴侣的属性列表
	local lover_profess_level_cfg_info = MarryWGData.Instance:GetProfessCfgByLevel(profess_level_info.other_grade)

	--获取额外增加的属性列表
	local add_attr_info = {}
	if nil ~= lover_profess_level_cfg_info then
		--获取其他数据
		local profess_other_cfg = MarryWGData.Instance:GetOtherProfessCfg()
		-- if profess_level_info.all_have_shizhuang == 0 then
		add_attr_info = AttributeMgr.MulAttribute(AttributeMgr.GetAttributteByClass(lover_profess_level_cfg_info), profess_other_cfg.att_add_per / 10000)
		-- else
		-- 	add_attr_info = AttributeMgr.GetAttributteByClass(lover_profess_level_cfg_info)
		-- end
	end
	--设置额外增加的属性列表
	local add_hp = math.floor(add_attr_info.max_hp or 0)
	local add_gongji = math.floor(add_attr_info.gong_ji or 0)
	local add_fangyu = math.floor(add_attr_info.fang_yu or 0)
	--local add_fangyu = math.floor(add_attr_info.fang_yu or 0)
	local add_po_jia = math.floor(add_attr_info.po_jia or 0)
	self.node_list["pw_hp_value_2"].text.text = add_hp	-- string.format(Language.Marry.PW_TAAddAttr, add_hp)
	self.node_list["pw_gongji_value_1"].text.text = add_gongji -- string.format(Language.Marry.PW_TAAddAttr, add_gongji)
	self.node_list["pw_fangyu_value_3"].text.text = add_fangyu --string.format(Language.Marry.PW_TAAddAttr, add_fangyu)
	-- att_add_per
	local cfg = MarryWGData.Instance:GetOtherProfessCfg()
	local add_attr = cfg.att_add_per
	self.node_list["banlv_per"].text.text = (add_attr / 100) .. "%"
	self.node_list["pw_pojia_value"].text.text = add_po_jia

	local add_capability = AttributeMgr.GetCapability(add_attr_info)
	local capability = AttributeMgr.GetCapability(profess_level_cfg_info)
	--设置战斗力（需要算上伴侣额外增加的战斗力）
	self.node_list["pw_zhandouli"].text.text = add_capability + capability

	if nil ~= self.curren_level_cache and (curren_level > 0) and (curren_level > self.curren_level_cache) then
		for i = 1, 4 do
			self:PlayProfessAttrInfoUpEffect(i)
		end
	end

	if curren_level > 0 then
		self.curren_level_cache = curren_level
	end
end

function MarryView:PlayProfessAttrInfoUpEffect(index)
	if self.node_list["attr_value_upeffect" .. index] then
		local particls = self.node_list["attr_value_upeffect" .. index]:GetComponentsInChildren(typeof(UnityEngine.ParticleSystem))
			
		if particls.Length > 0 then
			for i = 0, particls.Length - 1 do
				local effect = particls[i]

				if effect then
					if effect.isPlaying then
						effect:Stop()
						effect:Clear()
					end

					effect:Play()
				end
			end
		end
	end
end

function MarryView:SetBiaoBaiLevel()
	local level = 0
	local profess_level_info = MarryWGData.Instance:GetProfessLevelInfo()
	if profess_level_info then
		level = profess_level_info.my_grade
	end
	self.node_list["biaobai_level"].text.text = string.format(Language.Marry.BiaoBaiLevelShow,level)
end
--刷新伴侣的相关面板
function MarryView:FlushLeftContent()
	local info = MarryWGData.Instance:GetProfessLevelInfo()
	if info == nil then return end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

    -- local my_lv_txt = self.node_list["pw_ta_level"]
    -- local other_txt = self.node_list["pw_my_level"]
    -- if main_role_vo.sex == GameEnum.MALE then
    --     my_lv_txt = self.node_list["pw_my_level"]
    --     other_txt = self.node_list["pw_ta_level"]
    -- end
	-- my_lv_txt.text.text = string.format(Language.Marry.PW_SelfLevel, info.my_grade or 0)
	self.node_list["banlv_per_group"]:SetActive(main_role_vo.lover_uid > 0)
	self.node_list["no_banlv"]:SetActive(main_role_vo.lover_uid <= 0)
	if main_role_vo.lover_uid > 0 then
		self.node_list["pw_gigure_man_bg"]:SetActive(false)
		self.node_list["pw_gigure_lady_bg"]:SetActive(false)
		-- other_txt.text.text = string.format(Language.Marry.PW_OtherLevel, info.other_grade or 0)
	else
		if main_role_vo.sex == GameEnum.MALE then
			self.node_list["pw_gigure_man_bg"]:SetActive(false)
			self.node_list["pw_gigure_lady_bg"]:SetActive(true)
		else
			self.node_list["pw_gigure_man_bg"]:SetActive(true)
			self.node_list["pw_gigure_lady_bg"]:SetActive(false)
		end
		-- other_txt.text.text = Language.Marry.NoLover
	end

end

--刷新我的相关面板
function MarryView:FlushRightContent()
	self:FlushSelfAttr()
	self:RefreshProgress()
end

--刷新进度条
function MarryView:RefreshProgress()
	local profess_level_info = MarryWGData.Instance:GetProfessLevelInfo()
	if nil == profess_level_info then
		return
	end
	local curren_level = profess_level_info.my_grade
	local next_profess_level_info = MarryWGData.Instance:GetProfessCfgByLevel(curren_level + 1)
	if nil == next_profess_level_info then
		self.node_list["pw_ProgressBG"].slider.value = 1
		self.node_list["pw_text_progress"].text.text = Language.Skill.YiManJi
		return
	end

	local profess_level_cfg_info = MarryWGData.Instance:GetProfessCfgByLevel(curren_level)
	if nil == profess_level_cfg_info then
		return
	end
	local need_exp = profess_level_cfg_info.exp
	local now_exp = profess_level_info.my_exp
	local bili = now_exp / need_exp
	self.node_list["pw_ProgressBG"].slider.value = bili
	self.node_list["pw_text_progress"].text.text = string.format("%s/%s", now_exp, need_exp)
end

--表白记录
function MarryView:ClickGotoConfession()
	ViewManager.Instance:Open(GuideModuleName.ProfessWallView, "profess_wall_all")
end

--我要表白
function MarryView:OpenSelectProfess()
	ProfessWallWGData.Instance:SetDefaultInfo(nil,true)
	ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end

function MarryView:OpenHelp()
	MarryView.OpenTips(Language.Marry.PW_Tips, Language.Marry.PW_Tips_Title)
end
