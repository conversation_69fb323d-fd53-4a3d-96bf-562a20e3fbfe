local MONNEY_TYPE = {
	[1] = "a3_huobi_xianyu",
	[2] = "a3_huobi_bangyu",
}

-- local LIST_VIEW_HIGHT = {
-- 	[1] = Vector2(1050, 362),			--3*3
-- 	[2] = Vector2(1050, 530),			--3*4
-- }

function FestivalActivityView:InitHeFuMiaoSha()

end

function FestivalActivityView:LoadIndexCallBackHeFuMiaoSha()
	self.select_type = 1
	self.is_need_slider = true
	FestivalMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)

	if nil == self.miaosha_list_view then
		local bundle, asset = "uis/view/festival_activity_ui/miaosha_ui_prefab", "festival_miaosha_cell"
		self.miaosha_list_view = AsyncBaseGrid.New()
		self.miaosha_list_view:CreateCells({col = 3, itemRender = FestivalMiaoShaCell,
			list_view = self.node_list.miaosha_list_view, assetBundle = bundle, assetName = asset, change_cells_num = 1})
		self.miaosha_list_view:SetStartZeroIndex(false)
	end

	if not self.top_btn_list then
		self.top_btn_list = {}

		for i=1,2 do
			self.top_btn_list[i] = self.node_list["miaosha_top_btn_" .. i]
			self.top_btn_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTopToggle, self, i))
		end

		self.top_btn_list[self.select_type].toggle.isOn = true
	end

	if not self.gift_list then
		self.gift_list = {}

		for i=1,5 do
			self.gift_list[i] = ItemCell.New(self.node_list["miaosha_item_cell_pos_" .. i])
			self.gift_list[i]:SetClickCallBack(BindTool.Bind(self.OnclickGiftItem, self, i))
			self.gift_list[i]:SetIsShowTips(false)
			self.gift_list[i]:SetRightBottomTextVisible(true)
		end
	end

	self:SetMiaoShaCommonImg()

	--8.1 策划说把秒杀提醒屏蔽
	self.node_list.miaosha_tips_toggle:SetActive(false)
	self.node_list.miaosha_tips_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTipsToggle, self))

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local value = PlayerPrefsUtil.GetInt("festival_miaosha_toggle_" .. role_id)
	self.node_list.miaosha_tips_toggle.toggle.isOn = (value == 0)

	self.node_list.miaosha_tips_btn.button:AddClickListener(BindTool.Bind(self.OnMiaoShaClickTipsBtn, self))

end

function FestivalActivityView:OnMiaoShaClickTipsBtn()
	-- local theme_cfg = HeFuMiaoshaWGData.Instance:GetThemeCfgByTabIndex(TabIndex.merge_activity_2128)
	-- if theme_cfg then
	-- 	TipWGCtrl.Instance:SetRuleContent(theme_cfg.rule_desc, theme_cfg.tab_name)
	-- end
end

function FestivalActivityView:SetMiaoShaCommonImg()
	local text_color = FestivalActivityWGData.Instance:GetMiaoShaTextColor()
	self.node_list["miaosha_silder_text"].text.text = string.format(Language.FestivalMiaoShaDesc.Silder_text, text_color)

	local miaosha_raw_bg_bundle, miaosha_raw_bg_asset = ResPath.GetFestivalRawImages("xsms") --dlyl_big_bg
    self.node_list["miaosha_raw_bg"].raw_image:LoadSprite(miaosha_raw_bg_bundle, miaosha_raw_bg_asset, function ()
        self.node_list["miaosha_raw_bg"].raw_image:SetNativeSize()
    end)


	local miaosha_slider_bg_bundle, miaosha_slider_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_jd")
    self.node_list["miaosha_slider_bg"].image:LoadSprite(miaosha_slider_bg_bundle, miaosha_slider_bg_asset, function ()
    end)

	local miaosha_slider_fill_bundle, miaosha_slider_fill_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_jd_2")
    self.node_list["miaosha_slider_fill"].image:LoadSprite(miaosha_slider_fill_bundle, miaosha_slider_fill_asset, function ()
    end)

	local miaosha_slider_desc_bg_bundle, miaosha_slider_desc_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_jf")
    self.node_list["miaosha_slider_desc_bg"].image:LoadSprite(miaosha_slider_desc_bg_bundle, miaosha_slider_desc_bg_asset, function ()
        self.node_list["miaosha_slider_desc_bg"].image:SetNativeSize()
    end)

	local miaosha_silder_text_bg_bundel, miaosha_silder_text_bg__asset = ResPath.GetFestivalActImages("a2_jrkh_xltb_anmc")
    self.node_list["miaosha_silder_text_bg"].image:LoadSprite(miaosha_silder_text_bg_bundel, miaosha_silder_text_bg__asset, function ()
    end)

	local miaosha_top_btn_1_bundle, miaosha_top_btn_1_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_yq_1")
    self.node_list["miaosha_top_bg_1"].image:LoadSprite(miaosha_top_btn_1_bundle, miaosha_top_btn_1_asset, function ()
        self.node_list["miaosha_top_bg_1"].image:SetNativeSize()
    end)

	local miaosha_toggle_hl_1_bundle, miaosha_toggle_hl_1_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_yq")
    self.node_list["miaosha_toggle_hl_1"].image:LoadSprite(miaosha_toggle_hl_1_bundle, miaosha_toggle_hl_1_asset, function ()
        self.node_list["miaosha_toggle_hl_1"].image:SetNativeSize()
    end)

	local miaosha_top_btn_2_bundle, miaosha_top_btn_2_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_yq_1")
    self.node_list["miaosha_top_bg_2"].image:LoadSprite(miaosha_top_btn_2_bundle, miaosha_top_btn_2_asset, function ()
        self.node_list["miaosha_top_bg_2"].image:SetNativeSize()
    end)

	local miaosha_toggle_hl_2_bundle, miaosha_toggle_hl_2_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_yq")
    self.node_list["miaosha_toggle_hl_2"].image:LoadSprite(miaosha_toggle_hl_2_bundle, miaosha_toggle_hl_2_asset, function ()
        self.node_list["miaosha_toggle_hl_2"].image:SetNativeSize()
    end)

	local miaosha_list_view_bg_bundle, miaosha_list_view_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_di1")
	self.node_list.miaosha_list_view_bg.image:LoadSprite(miaosha_list_view_bg_bundle, miaosha_list_view_bg_asset)

	for i = 1, 5 do
		local miaosha_item_bg_bundel, miaosha_item_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_jf_3")
		local miaosha_value_bg_bundel, miaosha_value_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_jf_1")
		self.node_list["miaosha_item_bg_"..i].image:LoadSprite(miaosha_item_bg_bundel, miaosha_item_bg_asset, function ()
			self.node_list["miaosha_item_bg_"..i].image:SetNativeSize()
		end)
		self.node_list["miaosha_value_bg_"..i].image:LoadSprite(miaosha_value_bg_bundel, miaosha_value_bg_asset, function ()
		end)
	end
end

function FestivalActivityView:DeleteHeFuMiaoSha()
	if self.miaosha_list_view then
		self.miaosha_list_view:DeleteMe()
		self.miaosha_list_view = nil
	end

	if self.gift_list then
		for k,v in pairs(self.gift_list) do
			v:DeleteMe()
		end
		self.gift_list = nil
	end

	self.top_btn_list = nil
	self.select_type = nil
	self.is_need_slider = nil
	self.is_clicked_miaosha = false

	self:ClearMiaoShaCountDown()
end

function FestivalActivityView:ShowIndexCallHeFuMiaoSha()
	self.is_clicked_miaosha = true
	for i=1,3 do
		local data = FestivalMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(i)
		if data.shop_id ~= 0 then
			local shop_lib_cfg = FestivalMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.select_type = i
                self.top_btn_list[self.select_type].toggle.isOn = true
                FestivalMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)
				break
			end
		end
	end
end

function FestivalActivityView:FlushHeFuMiaoSha()
    local cfg = FestivalMiaoshaWGData.Instance:GetOtherCfg()
    self:SetOutsideRuleTips(cfg.buttom_des or "")
	self:SetRuleInfo(cfg.activity_des, Language.FestivalMiaoShaDesc.TipsTitle)
	self.miaosha_data = FestivalMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(self.select_type)
	self:FlushBottomSlider()
	self:RefreshListView()
	self:FlushTopBtn()

	-- if self.select_type == 1 then
	-- 	self.node_list["tab_buttons"].transform.anchoredPosition = u3dpool.vec3(566, 37, 0)
	-- 	self.node_list["miaosha_list_view"].transform.anchoredPosition = u3dpool.vec3(83, -60, 0)
	-- elseif self.select_type == 2 then
	-- 	self.node_list["tab_buttons"].transform.anchoredPosition = u3dpool.vec3(557, 82, 0)
	-- 	self.node_list["miaosha_list_view"].transform.anchoredPosition = u3dpool.vec3(72, -30, 0)
	-- end


end

--刷新listview
function FestivalActivityView:RefreshListView()
	--self.node_list.miaosha_mask_bg:SetActive(false)
	--self.node_list.miaosha_time_text:SetActive(true)
	local data_list = FestivalMiaoshaWGData.Instance:GetHeFuMiaoShaListData(self.select_type)
	-- self.node_list["miaosha_act_view"]:SetActive(true)
	-- self.node_list["miaosha_time_view"]:SetActive(false)
	-- 备货期间不展示进度条、展示虚拟物品、展示遮罩
	-- if IsEmptyTable(data_list) then
	-- 	-- self.node_list.miaosha_list_view:SetActive(false)
	-- 	-- self.node_list.miaosha_bottom_slider:SetActive(false)
	-- 	-- self.node_list.miaosha_time_text:SetActive(false)
	-- 	self.node_list["miaosha_act_view"]:SetActive(false)
	-- 	self.node_list["miaosha_time_view"]:SetActive(true)
	-- 	return
	-- end

	-- self.node_list.miaosha_list_view:SetActive(true)
	-- if self.is_need_slider then
	-- 	self.node_list.miaosha_list_view.rect.sizeDelta = LIST_VIEW_HIGHT[1]
	-- else
	-- 	self.node_list.miaosha_list_view.rect.sizeDelta = LIST_VIEW_HIGHT[2]
	-- end

	self.miaosha_list_view:SetDataList(data_list)
end

-- local list_progress = {0.16, 0.36, 0.58, 0.79, 1}
local list_progress = {0, 0.25, 0.5, 0.75, 1}

--刷新进度条
function FestivalActivityView:FlushBottomSlider()
	if not self.miaosha_data then
		return
	end

	local shop_lib_cfg = FestivalMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.miaosha_data.shop_id)
	if not shop_lib_cfg then
		--self.node_list.miaosha_bottom_slider:SetActive(false)
		return
	end

	-- 赠送额度为0不展示进度条
	if IsEmptyTable(shop_lib_cfg.gift_item) then
		--self.node_list.miaosha_bottom_slider:SetActive(false)
		self.is_need_slider = false
		return
	end
    local gift_quota_list_str = Split(shop_lib_cfg.gift_quota, ",")
    local gift_quota_list = {}
    for k, v in pairs(gift_quota_list_str) do
        gift_quota_list[k] = tonumber(v)
    end
	self.is_need_slider = true
	self.node_list.miaosha_bottom_slider:SetActive(true)
    local progress_idx = 0
    if self.miaosha_data.total_quota == 0 then
        self.node_list["miaosha_slider"].slider.value = list_progress[1]
    elseif self.miaosha_data.total_quota >= gift_quota_list[#gift_quota_list] then
        self.node_list["miaosha_slider"].slider.value = 1
    else
        for k, v in pairs(gift_quota_list) do
            if self.miaosha_data.total_quota < v then
                progress_idx = k - 1
                break
            end
        end
        local start = gift_quota_list[progress_idx] or 0
        local start_pro = list_progress[progress_idx -1] or 0
        local add_pergress = (list_progress[progress_idx] - start_pro) * (self.miaosha_data.total_quota - start) / (gift_quota_list[progress_idx + 1] -  start)
        self.node_list["miaosha_slider"].slider.value = list_progress[progress_idx] + add_pergress
    end

	for i=1,5 do
		-- 物品
		if self.gift_list[i] and shop_lib_cfg.gift_item[i - 1] then
			self.gift_list[i]:SetData(shop_lib_cfg.gift_item[i - 1])
		end
		-- 额度
		if gift_quota_list[i] then
			local gift_quota = tonumber(gift_quota_list[i])
			self.node_list["miaosha_value_" .. i].text.text = gift_quota

			if self.miaosha_data.total_quota >= gift_quota and self.miaosha_data.quota_reward_tag[i] == 0 then
				--可领取未领取
				self.node_list["miaosha_effect_" .. i]:SetActive(true)
				self.node_list["miaosha_item_remind_" .. i]:SetActive(true)
				self.gift_list[i]:SetLingQuVisible(false)
				XUI.SetGraphicGrey(self.node_list["miaosha_item_bg_"..i], false)
			elseif self.miaosha_data.total_quota >= gift_quota and self.miaosha_data.quota_reward_tag[i] == 1 then
				--已领取
				self.node_list["miaosha_effect_" .. i]:SetActive(false)
				self.node_list["miaosha_item_remind_" .. i]:SetActive(false)
				self.gift_list[i]:SetLingQuVisible(true)
				XUI.SetGraphicGrey(self.node_list["miaosha_item_bg_"..i], true)
			else
				self.node_list["miaosha_effect_" .. i]:SetActive(false)
				self.node_list["miaosha_item_remind_" .. i]:SetActive(false)
				self.gift_list[i]:SetLingQuVisible(false)
				XUI.SetGraphicGrey(self.node_list["miaosha_item_bg_"..i], false)
			end
		end
	end

	local other_cfg = FestivalMiaoshaWGData.Instance:GetOtherCfg()

	if other_cfg then
		self.node_list.miaosha_money_count.text.text = other_cfg.price
	end
	-- self.node_list.miaosha_raw_bg_2:SetActive(self.is_need_slider)
	self.node_list.miaosha_total_value.text.text = self.miaosha_data.total_quota

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2)

	if not activity_info or activity_info.end_time == -1 then
		return
	end

	self:SetActRemainTime(TabIndex.festival_activity_2128, activity_info.end_time)

end


--专属类型切换
function FestivalActivityView:OnClickTopToggle(index, is_on)
	if is_on then
		if self.select_type == index then
			return
		end
		self.select_type = index
		FestivalMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)
		self:FlushHeFuMiaoSha()
	end
end

--刷新顶部按钮
function FestivalActivityView:FlushTopBtn()
	self:ClearMiaoShaCountDown()

	if not self.miaosha_data then
		return
	end

	for i=1,2 do
		local data = FestivalMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(i)
		if data.shop_id ~= 0 then
			local shop_lib_cfg = FestivalMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.node_list["miaosha_top_btn_" .. i]:SetActive(true)
			else
				self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
			end
		else
			self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
		end

		self.node_list["miaosha_remind_" .. i]:SetActive(FestivalMiaoshaWGData.Instance:GetMiaoShaRemindByType(i) == 1)
	end

	local time = self.miaosha_data.refresh_time - TimeWGCtrl.Instance:GetServerTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2)

	if not activity_info or activity_info.end_time == -1 then
		--self.node_list.miaosha_time_text.text.text = ""
		return
	end

	local end_time = activity_info.end_time - TimeWGCtrl.Instance:GetServerTime()

	--8.1 策划说把刷新时间屏蔽
	if time > 0 and time <= end_time then
		CountDownManager.Instance:AddCountDown("festival_miaosha_countdown", BindTool.Bind1(self.MiaoShaChangeTime, self), BindTool.Bind1(self.MiaoShaCompleteTime, self), nil, time, 1)
		self:MiaoShaChangeTime(0, time)
	else
		self.node_list.miaosha_time_text.text.text = ""
	end
end

--计时器
function FestivalActivityView:MiaoShaChangeTime(elapse_time, total_time)
	local time = total_time - elapse_time

	if time > 0 then
		if self.node_list.miaosha_time_text then
			-- self.node_list.miaosha_time_text.text.text = string.format(Language.FestivalMiaoShaDesc.RefreshTime, TimeUtil.FormatSecond(time, 3))
			self.node_list.miaosha_time_text.text.text = TimeUtil.FormatSecond(time, 3)
		end

		-- local time_list = TimeUtil.Format2TableDHM2(time)

		-- if self.node_list.miaosha_time_text_2 then
		-- 	self.node_list.miaosha_time_text_2.text.text = string.format("%02d：%02d：%02d", time_list.hour, time_list.min, time_list.sec)
		-- end
	else
		--self.node_list.miaosha_time_text.text.text = ""
	end
end

--计时完成
function FestivalActivityView:MiaoShaCompleteTime()
	self:ClearMiaoShaCountDown()
	self:MiaoShaChangeTime(0, 0)
end

--清除计时器
function FestivalActivityView:ClearMiaoShaCountDown()
	if CountDownManager.Instance:HasCountDown("festival_miaosha_countdown") then
		CountDownManager.Instance:RemoveCountDown("festival_miaosha_countdown")
	end
end

-- 秒杀提醒勾选
function FestivalActivityView:OnClickTipsToggle(is_on)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local value = (is_on == true) and 0 or 1
	PlayerPrefsUtil.SetInt("hefu_miaosha_toggle_" .. role_id, value)
end

-- 累计奖励点击事件
function FestivalActivityView:OnclickGiftItem(index)
	if not self.miaosha_data then
		return
	end

	if not self.miaosha_data.quota_reward_tag[index] then
		return
	end

	local shop_lib_cfg = FestivalMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.miaosha_data.shop_id)

	if not shop_lib_cfg then
		return
	end

	if shop_lib_cfg.gift_item == 0 or shop_lib_cfg.gift_quota == 0 then
		return
	end

	local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")

	if not gift_quota_list[index] then
		return
	end

	if self.miaosha_data.total_quota >= tonumber(gift_quota_list[index]) and self.miaosha_data.quota_reward_tag[index] == 0 then
		--请求
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_RECEIVE_GIFT, self.select_type, index - 1)

		if shop_lib_cfg and shop_lib_cfg.gift_item[index - 1] then
			local reward_list = {[1] = shop_lib_cfg.gift_item[index - 1]}
			TipWGCtrl.Instance:ShowGetReward(nil, reward_list, false)
		end
	else
		--提示
		TipWGCtrl.Instance:OpenItem(shop_lib_cfg.gift_item[index - 1])
	end

end

-- function FestivalActivityView:OnClickTipsBtn()
-- 	local theme_cfg = FestivalMiaoshaWGData.Instance:GetThemeCfgByTabIndex(TabIndex.festival_activity_2128)
-- 	if theme_cfg then
-- 		TipWGCtrl.Instance:SetRuleContent(theme_cfg.rule_desc, theme_cfg.tab_name)
-- 	end
-- end

-----------------------------------------------------------FestivalMiaoShaCell-------------------------------------------------------------------------------

FestivalMiaoShaCell = FestivalMiaoShaCell or BaseClass(BaseGridRender)

function FestivalMiaoShaCell:LoadCallBack()
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))

	self.node_list["buy_btn"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_btn_huang"))
	self.item_cell = ItemCell.New(self.node_list.item_cell)
	local miaosha_cell_bg_bundle, miaosha_cell_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_dlsd_di")
    self.node_list["miaosha_cell_bg"].image:LoadSprite(miaosha_cell_bg_bundle, miaosha_cell_bg_asset, function ()
        self.node_list["miaosha_cell_bg"].image:SetNativeSize()
    end)
end

function FestivalMiaoShaCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FestivalMiaoShaCell:OnFlush()
	if not self.data then
		return
	end
	local select_type = FestivalMiaoshaWGData.Instance:GetMiaoShaSelectType()

	local item_lib_cfg = FestivalMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
	if not item_lib_cfg then
		return
	end

	self.node_list.original_price_value.text.text = item_lib_cfg.original_price
	--self.node_list.original_money_icon.image:LoadSprite(ResPath.GetF2CommonIcon(MONNEY_TYPE[select_type]))
	local bundel ,asset = ResPath.GetCommonIcon(MONNEY_TYPE[select_type])
	self.node_list.original_money_icon.image:LoadSprite(bundel ,asset, function ()
		self.node_list.original_money_icon.image:SetNativeSize()
	end)

	self.node_list.current_money_icon.image:LoadSprite(bundel ,asset, function ()
		self.node_list.current_money_icon.image:SetNativeSize()
	end)

	--self.node_list.cur_money_icon.image:LoadSprite(ResPath.GetF2CommonIcon(MONNEY_TYPE[select_type]))
	self.node_list.cur_money_icon.image:LoadSprite(bundel ,asset, function ()
		self.node_list.cur_money_icon.image:SetNativeSize()	
	end)

	local top_bg_bundle, top_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_xsms_zk")
    self.node_list["top_bg"].image:LoadSprite(top_bg_bundle, top_bg_asset, function ()
        self.node_list["top_bg"].image:SetNativeSize()
    end)

    self.node_list.cur_price_value.text.text = item_lib_cfg.current_price
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_lib_cfg.item_id) 
    if IsEmptyTable(item_cfg) then
        print_error("秒杀取不到物品配置 item_id =", item_lib_cfg.item_id)
    end
	self.item_cell:SetData({item_id = item_lib_cfg.item_id, num = 1, is_bind = item_lib_cfg.is_bind})
	local common_color = FestivalActivityWGData.Instance:GetCommonColor()
	self.node_list["item_name"].text.text = ToColorStr(item_cfg.name, common_color)

	local discount_color = FestivalActivityWGData.Instance:GetDiscountColor()
	self.node_list.discount_desc.text.text = string.format(Language.FestivalMiaoShaDesc.DisCount, discount_color, NumberToChinaNumber(item_lib_cfg.discount))

	local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if vip_level < item_lib_cfg.vip_level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		self.node_list.limit_desc:SetActive(true)
		self.node_list.limit_desc_bg:SetActive(true)
		self.node_list.limit_desc.text.text = string.format(Language.FestivalMiaoShaDesc.LimitDesc1, item_lib_cfg.vip_level_limit)
	elseif role_level < item_lib_cfg.level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		self.node_list.limit_desc:SetActive(true)
		self.node_list.limit_desc_bg:SetActive(true)
		self.node_list.limit_desc.text.text = string.format(Language.FestivalMiaoShaDesc.LimitDesc2, item_lib_cfg.level_limit)
	else
		local can_buy = self.data.last_num > 0 and self.data.bought_times < item_lib_cfg.buy_limit_value
		self.node_list.limit_desc:SetActive(false)
		self.node_list.limit_desc_bg:SetActive(false)
		self.node_list.buy_btn:SetActive(can_buy)
		self.node_list.sellout_flag:SetActive(not can_buy)

		self.node_list.current_money_icon:SetActive(can_buy)
		self.node_list.buy_btn_text.text.text = can_buy and string.format(Language.FestivalMiaoShaDesc.BtnDesc1, item_lib_cfg.current_price) or Language.FestivalMiaoShaDesc.BtnDesc2
	end

	-- self.node_list.surplus_desc:SetActive(item_lib_cfg.is_show == 1)
	-- local res_name = item_lib_cfg.is_show == 1 and "miaosha_img_2" or "miaosha_img_6"
	-- local bundle = "uis/view/festival_activity_ui/hefu_miaosha_ui/images_atlas"
	-- self.node_list.top_bg.image:LoadSprite(bundle, res_name, function ()
	-- 	self.node_list.top_bg.image:SetNativeSize()
	-- end)

	local str = string.format(Language.FestivalMiaoShaDesc.PresonCount, self.data.bought_times, item_lib_cfg.buy_limit_value)
	self.node_list.limit_bg:SetActive(item_lib_cfg.buy_limit_type == 1 or item_lib_cfg.buy_limit_type == 5)

	local color = self.data.bought_times >= item_lib_cfg.buy_limit_value and COLOR3B.RED or COLOR3B.WHITE

	self.node_list.limit_count_desc.text.text = ToColorStr(str, color)
	self.item_cell:SetBindIconVisible(false)
	self.item_cell:SetRightBottomTextVisible(false)
	-- self.item_cell:SetRightDownCellBVisible(false)


	self.node_list.top_bg:SetActive(item_lib_cfg.discount > 0)
end

function FestivalMiaoShaCell:OnClickBuyBtn()
	local item_lib_cfg = FestivalMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
	if not item_lib_cfg then
		return
	end
   
    local buy_func = function(num)
        local select_type = FestivalMiaoshaWGData.Instance:GetMiaoShaSelectType()
        local hefu_miaosha_data = FestivalMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(select_type)
        if not hefu_miaosha_data then
            return
        end
        local item_lib_cfg = FestivalMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
        if not item_lib_cfg then
            return
        end
        local state = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2)
        if not state then
            TipWGCtrl.Instance:ShowSystemMsg(Language.FestivalMiaoShaDesc.ActCloseTips)
            return
        end

        local shop_lib_cfg = FestivalMiaoshaWGData.Instance:GetShopLibConfigByType(select_type, hefu_miaosha_data.shop_id)
        if not shop_lib_cfg then
            return
        end
        
        local cur_price = item_lib_cfg.current_price
        local select_type = FestivalMiaoshaWGData.Instance:GetMiaoShaSelectType()

        if select_type == 2 then --绑玉
            local gold = RoleWGData.Instance:GetRoleInfo().gold
            local bind_gold = RoleWGData.Instance:GetRoleInfo().bind_gold
            if bind_gold < cur_price and (bind_gold + gold) >= cur_price then
                local ok_func = function()
                    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
                end
                TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
                return
            end
        end
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2, FA_TIMED_SPIKE_OPERA_TYPE.FA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
    end
    if item_lib_cfg.buy_limit_value - self.data.bought_times > 1 then
        --通用的批量购买
        HeFuMiaoShaWGCtrl.Instance:OpenBatchBuyView(item_lib_cfg.item_id, 1, 1,  item_lib_cfg.buy_limit_value - self.data.bought_times, buy_func)
    else
        buy_func(1)
    end
end