------------------------------------------------------------
--装备对比tip
------------------------------------------------------------
RoleEquipTip = RoleEquipTip or BaseClass(SafeBaseView)

function RoleEquipTip:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_tipview")
    self.view_layer = UiLayer.Pop
    self.view_name = "RoleEquipTip"
end

function RoleEquipTip:__delete()

end

function RoleEquipTip:ReleaseCallBack()
	if self.equip_tips then
		self.equip_tips:DeleteMe()
		self.equip_tips = nil
	end

	if self.equip_compare_tips then
		self.equip_compare_tips:DeleteMe()
		self.equip_compare_tips = nil
	end

	if self.test_base_tip_1 then
		self.test_base_tip_1:DeleteMe()
		self.test_base_tip_1 = nil
	end

	if self.test_base_tip_2 then
		self.test_base_tip_2:DeleteMe()
		self.test_base_tip_2 = nil
	end
end

function RoleEquipTip:LoadCallBack()
	self.equip_tips = TipsEquipComparePanel.New(self.node_list["EquipTip"])
	self.equip_tips:SetIndex(1)
	self.equip_tips.is_mine = true

	self.equip_compare_tips = TipsEquipComparePanel.New(self.node_list["EquipCompareTip"])
	self.equip_compare_tips:SetIndex(2)

	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function RoleEquipTip:ShowIndexCallBack()
	if self.data_cache then
		self:SetData(self.data_cache.data, self.data_cache.from_view, self.data_cache.param_t, self.data_cache.close_call_back, self.data_cache.gift_id, self.data_cache.is_check_item, self.data_cache.btn_callback_event)
		self.data_cache = nil
	end
end

function RoleEquipTip:CloseCallBack()

end

function RoleEquipTip:OnFlush(param_t)
	self.equip_tips:OnFlush(param_t)
	self.equip_compare_tips:OnFlush(param_t)
end

function RoleEquipTip:SetData(data, from_view, param_t, close_call_back, gift_id, is_check_item, btn_callback_event)
	if not data then
		return
	end

	if self:IsOpen() and self:IsLoaded() then
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
		if item_cfg == nil then
			return
		end

		local my_data
		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
			local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(data.item_id)
			my_data = FightSoulWGData.Instance:GetWearBoneDataByParam(fight_soul_type, bone_part, suit_type)
		elseif from_view == ItemTip.FROM_TIANSHEN_SHENSHI_BAG then
			my_data = TianShenWGData.Instance:GetTianShenEquip(nil, TianShenWGData.Equip_Pos[item_cfg.sub_type])
		elseif from_view == ItemTip.FROM_HOLY_EQUIP_CONTRAST then		-- 圣装
			my_data = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(data.slot, data.part)
		elseif from_view == ItemTip.BEAST_ALCHEMY_EQUIP_BAG then		-- 幻兽内丹装备
			my_data = ControlBeastsCultivateWGData.Instance:AssembleBeastEquipByFightSlot(data.fight_slot, data.equip_slot, data.equip_slot_lv)
		else
			local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
			my_data = EquipWGData.Instance:GetGridData(equip_body_index)
		end

		-- 特殊处理，查看其他玩家装备，与自己装备对比(UI调换)
		if from_view == ItemTip.FROME_BROWSE_ROLE or from_view == ItemTip.FORM_ROBOT_SHOW_INFO
		 	or from_view == ItemTip.FROM_EQUIMENT_HECHENG then
			self.node_list["EquipTip"].transform.anchoredPosition = Vector2(196, 0)
			self.node_list["EquipCompareTip"].transform.anchoredPosition = Vector2(-196, 0)
		else
			self.node_list["EquipTip"].transform.anchoredPosition = Vector2(-196, 0)
			self.node_list["EquipCompareTip"].transform.anchoredPosition = Vector2(196, 0)
		end

		self.equip_compare_tips:SetData(data, from_view, param_t, close_call_back, gift_id, is_check_item, true, btn_callback_event, my_data)
		if my_data then
			if from_view == ItemTip.FROM_QiChong then
				self.equip_tips:SetData(my_data, from_view, nil, nil, gift_id, is_check_item, false, btn_callback_event)
			elseif from_view == ItemTip.BEAST_ALCHEMY_EQUIP_BAG then		-- 幻兽内丹装备
				self.equip_tips:SetData(my_data, ItemTip.BEAST_ALCHEMY_EQUIP_BAG, nil, nil, nil, false, false, nil)
			else
				self.equip_tips:SetData(my_data, ItemTip.EQUIPMENT_CONTRAST, nil, nil, gift_id, is_check_item, false, btn_callback_event)
			end
		end
	else
		self.data_cache = {data = data, from_view = from_view, param_t = param_t, close_call_back = close_call_back,
							gift_id = gift_id, is_check_item = is_check_item, btn_callback_event = btn_callback_event}
		self:Open()
		self:Flush()
	end
end


TipsEquipComparePanel = TipsEquipComparePanel or BaseClass()

local ATTR_BASE = 1 					-- 基础属性
local ATTR_PINK_SUIT = 2				-- 粉装属性
local ATTR_GOD_SUIT = 3					-- 神装属性
local ATTR_MORE_COLOR = 4				-- 幻彩属性
local ATTR_LEGEND = 5					-- 传奇属性 仙品属性
local ATTR_SHENGPIN = 6 				-- 升品属性
local ATTR_STONE = 7					-- 宝石
local ATTR_STONE_JL = 8 				-- 宝石精炼
local ATTR_SUIT = 9						-- 装备套装
local ATTR_LINGYUE = 10					-- 灵玉
local ATTP_YULING = 11                  -- 御灵
local ATTP_ZHUSHEN = 12					-- 铸神
local ATTP_XILIAN = 13					-- 装备洗炼

local ATTR_XIANMENG_CANGKU = 20			-- 仙盟仓库属性
local REMIND_HOW_COMPOSE = 21			-- 装备合成公式
local ATTR_EQUIP_SKILL = 22				-- 装备技能

local ATTR_STRENGTHEN = 30				-- 装备强化属性 天神专属
local ATTR_JIPING = 31					-- 装备极品属性 天神专属


function TipsEquipComparePanel:__init(instance, parent)
	self.base_tips = BaseTip.New(instance)
	self.label_t = Language.Tip.ButtonLabel
	self.shenping_base_pingfen_per = 0
end

function TipsEquipComparePanel:__delete()
	if self.base_tips then
		self.base_tips:DeleteMe()
		self.base_tips = nil
	end
	if self.num_keypad then
		self.num_keypad:DeleteMe()
		self.num_keypad = nil
	end
	self.shenping_base_pingfen_per = 0
	self.target_data = nil
	self:ClearXiaoguiCD()
end

function TipsEquipComparePanel:ClearXiaoguiCD()
	if self.xiaogui_countdown_quest then
		GlobalTimerQuest:CancelQuest(self.xiaogui_countdown_quest)
		self.xiaogui_countdown_quest = nil
	end
end

function TipsEquipComparePanel:OnFlush(param_t)
	if self.data == nil then
		return
	end
	self.base_tips:Reset()
	self:ShowOperationState()
	self:ShowTipContent()
end

function TipsEquipComparePanel:SetIndex(index)
	self.base_tips:SetIndex(index)
end

--设置显示弹出Tip的相关属性显示
function TipsEquipComparePanel:SetData(data, from_view, param_t, close_call_back, gift_id, is_check_item, is_compare, btn_callback_event, target_data)
	if not data then
		return
	end
	if type(data) == "string" then
		self.data = CommonStruct.ItemDataWrapper()
		self.data.item_id = data
	else
		self.data = __TableCopy(data)
		self.data.item_id = self.data.item_id or self.data.id
		if self.data.param == nil then
			self.data.param = CommonStruct.ItemParamData()
		end
	end
	self.is_check_item = is_check_item
	self.from_view = from_view or ItemTip.FROM_BAG
	self.handle_param_t = param_t or {}
	self.is_compare = is_compare
	self.btn_callback_event = btn_callback_event
	self.target_data = target_data
end

function TipsEquipComparePanel:ShowOperationState()
	if self.is_mine then
		return
	end

	local handle_types = TipWGData.Instance:GetOperationLabelByType(self.data, self.from_view)
	local btn_info_list = {}

	if not IsEmptyTable(handle_types) then
		for i, v in ipairs(handle_types) do
			local temp = {}
			temp.btn_name = self.label_t[v]
			temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, v)
			if v == ItemTip.HANDLE_SALE or v == ItemTip.HANDLE_EQUIP_DECOMPOSE then
				btn_info_list[#btn_info_list + 1] = temp
			else
				table.insert(btn_info_list, 1, temp)
			end
		end
	end

	if self.btn_callback_event then
		for i,v in ipairs(self.btn_callback_event) do
			local temp = {}
			temp.btn_name = v.btn_text

			if v.btn_click == nil then
				temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, ItemTip.FROM_CUSTOM_BTN + i)	
			else
				temp.btn_click = v.btn_click
			end

			if v.show_red then
				temp.btn_red = v.show_red
			end
			btn_info_list[#btn_info_list + 1] = temp
		end
	end

	if #btn_info_list > 0 then
		self.base_tips:SetBtnsClick(btn_info_list)
	end
end

function TipsEquipComparePanel:ShowTipContent()
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	local item_cell = self.base_tips:GetItemCell()
	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		item_cell:SetItemTipFrom(ItemTip.FROM_TIANSHEN_SHENSHI_BAG)
	elseif self.is_compare then
		if item_cfg.sub_type  == GameEnum.E_TYPE_SIXIANG_BONE then
			item_cell:SetItemTipFrom(ItemTip.FROM_FIGHT_SOUL_BONE)
		elseif big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			item_cell:SetItemTipFrom(ItemTip.FROM_BAG)
		elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
			item_cell:SetItemTipFrom(ItemTip.FROM_HOLY_EQUIP_BAG)
		end
	end

	item_cell:SetIsShowTips(false)
	item_cell:SetData(self.data)

	local force_show_top_bg, show_long_eff = false, false
	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then	--装备
		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
			self:ParseFightSoulBone(item_cfg, self.data)
		elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then	-- 圣装
			self:ParseHolyEquip(item_cfg, self.data)

			local he_item_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(self.data.item_id)
			--仙界装备的特殊部位:显示顶部盘龙特效
			if he_item_cfg and FairyLandEquipmentWGData.Instance:GetIsSpecialType(he_item_cfg.part) then
				force_show_top_bg = true
				show_long_eff = true
			end
		else
			self:ParseEquip(item_cfg)
		end
	elseif self.from_view == ItemTip.BEAST_ALCHEMY_EQUIP_BAG then		-- 幻兽内丹装备
		ItemTip.ParseBeastAlchemyEquipItem(self, item_cfg, self.data)
	end

	if self.is_mine then
		self.base_tips:SetTopLeftIcon("a1_biaoqian_zbz")
	end
	self.base_tips:SetTopColorBg(item_cfg.color or 0, force_show_top_bg)
	self.base_tips:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	if show_long_eff then
		self.base_tips:SetTopLongEffectShow(item_cfg.color or 0, force_show_top_bg)
	end

	-- tip面板特效
	local tips_effect_name = item_cfg.tips_effect_name
	if tips_effect_name and tips_effect_name ~= "" then
		local bundle_name, asset_name = ResPath.GetEffectUi(tips_effect_name)
		self:SetEquipTipsPanelEffectShow(bundle_name, asset_name)
	elseif item_cfg.color >= GameEnum.ITEM_COLOR_SHINING_GOLD then
		local bundle_name, asset_name = ResPath.GetWuPinKuangEffectUi(TIPS_KUANG_QUALITY_EFFECT[item_cfg.color])
		self:SetEquipTipsPanelEffectShow(bundle_name, asset_name)
	end

	self.base_tips:SetMarketPanel({price = self.data.total_price, item_id = self.data.item_id}) 				-- 设置市场售价
	self:ShowItemName(self.data)
end

function TipsEquipComparePanel:ShowItemName(data)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg == nil then
		return
	end

	-- 查看玩家
	local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO

	local str = item_cfg.name
	if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
			if self.is_mine then
				local is_wear, fs_slot, bone_part = FightSoulWGData.Instance:GetBoneBagIndexIsWear(data.bag_index)
				if is_wear then
					local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(fs_slot, bone_part)
					local strength_level = part_data and part_data.slot_level or 0
					if strength_level > 0 then
						str = str .. " +" .. strength_level
					end
				end
			end
		elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
			if data.is_wear then
				local fle_data = FairyLandEquipmentWGData.Instance
				local item_id = data.item_id
				local he_data = fle_data:HolyEquipItemData(item_id)
				local strength_level = is_get_browse_data and 0 or fle_data:GetPartStrengthenLevel(he_data.slot, he_data.part)
				if strength_level > 0 then
					str = str .. " +" .. strength_level
				end
			end
		else
			-- 加上升品的名字
			if self.data.index and not is_get_browse_data and self.data.frombody then
				local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(self.data.index) or 0
				if star_count > 0 then
					local shengpin_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, self.data.index)
					if shengpin_cfg and shengpin_cfg.quality_grade and shengpin_cfg.quality_grade > 0 then
						str = str .. Language.Equip.shengPin_itemTip[shengpin_cfg.quality_grade + 1]
					end
				end
			end
			-- 真炼名字：真·name
			if 1 == self.data.param.is_refine then
				str = Language.Compose.ZhenLianName .. str
			end

			-- local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			-- local param_suit_index = self.data.param.suit_index or -1
			-- if param_suit_index >= 0 and self.is_mine then
			-- 	str = "【" .. Language.Equip.TabSub3[param_suit_index + 1] .. "】" .. str
			-- elseif param_suit_index >= 0 and is_get_browse_data then
			-- 	str = "【" .. Language.Equip.TabSub3[param_suit_index + 1] .. "】" .. str
			-- end

			local strength_level = 0
			if self.data.frombody and not is_get_browse_data then
				local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
				strength_level = EquipmentWGData.Instance:GetStrengthLevelByIndex(equip_body_index)
			elseif self.data.param ~= nil and self.data.param.strengthen_level ~= nil then
				strength_level = self.data.param.strengthen_level
			end

			if strength_level > 0 and (self.data.frombody or is_get_browse_data) then
				str = str .. " +" .. strength_level
			end
		end
	end

	self.base_tips:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
end

-- 解析四象魂骨tips
function TipsEquipComparePanel:ParseFightSoulBone(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end
	self.common_pingfen_num = 0
	self.comp_pingfen_num = 0

	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.F2Tip.TypeSiXiangBone)
	self.base_tips:SetEquipSocre(rich_type)
	local limit_sex = item_cfg.limit_sex or 0
	local rich_prof = string.format(Language.Tip.ZhuangBeiProf, TIPS_COLOR.SOCRE,
								Language.Common.ProfName[limit_sex][item_cfg.limit_prof])
	self.base_tips:SetSyntheticalSocre(rich_prof)

	local item_id = data.item_id
	local star = data.star
	local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(item_id)

	-- 基础属性
	local attr_list
	local attr_cfg = FightSoulWGData.Instance:GetBoneAttrCfg(bone_part, item_cfg.color, star)
	local is_wear, fs_slot, bone_part = FightSoulWGData.Instance:GetBoneBagIndexIsWear(data.bag_index)
	if is_wear and self.is_mine then
		local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(fs_slot, bone_part)
		local strength_level = part_data and part_data.slot_level or 0
		if strength_level > 0 then
			local strength_cfg = FightSoulWGData.Instance:GetBoneStrengthCfg(bone_part, strength_level)
			attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(attr_cfg, strength_cfg)
		else
			attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
		end
	else
		attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
	end


	local temp_list = {}
	local base_num = 0
	local uplevel_str = ""
	local is_per, per_value, per_str
	for k, v in pairs(attr_list) do
		if v.attr_value > 0 then
			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str)
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			per_value = is_per and v.attr_value / 100 or v.attr_value
			per_str = is_per and "%" or ""
			temp.attr_value = per_value .. per_str
			uplevel_str = ""
			if v.attr_next_value and v.attr_next_value > 0 then
				uplevel_str = string.format("（%s+%s）", Language.Tip.StrengthText, v.attr_next_value)

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.comp_pingfen_num = self.comp_pingfen_num + v.attr_next_value * base_num
			end
			temp.add_str = uplevel_str

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			table.insert(temp_list, temp)
		end
	end
	if not IsEmptyTable(temp_list) then
		self.base_tips:SetBaseAttribute(temp_list)
	end

	-- 套装
	local suit_data, equip_score = FightSoulWGData.Instance:GetBoneSuitShowData(fight_soul_type, suit_type, item_cfg.color)
	if not IsEmptyTable(suit_data) then
		self.base_tips:SetFightSoulSuitAttribute({fs_type = fight_soul_type, suit_type = suit_type, color = item_cfg.color, suit_data = suit_data})
		self.comp_pingfen_num = self.comp_pingfen_num + suit_data.suit_score
	end

	self.comp_pingfen_num = self.comp_pingfen_num + self.common_pingfen_num
	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 合成配置
	local show_tips, process_list = ItemShowWGData.Instance:GetFightSoulBoneComposeProcessData(data)
	if show_tips then
		local title_str = string.format(Language.F2Tip.ComposeTitleDesc, item_cfg.name)
		local sixiang_cap = 0--AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(attr_cfg))
		local compose_info = {title_desc = title_str, equip_list = process_list,
							bottom_desc = Language.F2Tip.ComposeBottomDesc3,
							capability = sixiang_cap, show_add = false,}
		self.base_tips:SetEquipTipsComposePanel(compose_info)
	end

	-- 获取途径
	-- self:ShowItemGetDesc(item_cfg)
end

-- 解析圣装
function TipsEquipComparePanel:ParseHolyEquip(item_cfg, data)
	if IsEmptyTable(item_cfg) or IsEmptyTable(data) then
		return
	end
	-- 查看玩家
	local is_browse = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO

	local fle_data = FairyLandEquipmentWGData.Instance
	local item_id = data.item_id
	local he_data = fle_data:HolyEquipItemData(item_id)
	local slot = he_data.slot
	local part = he_data.part
	local is_wear = data.is_wear

	--标题
	local rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE,
									Language.F2Tip.TypeHolyEquip)
	self.base_tips:SetEquipSocre(rich_type)
	local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
	local fle_str = Language.FairyLandEquipment
	local part_str = string.format(fle_str.EquipTipsDesc, slot_cfg.short_name, fle_str.PartName[part])
	local rich_part = string.format(Language.Tip.ZhuangBeiPart, TIPS_COLOR.SOCRE, part_str)
	self.base_tips:SetSyntheticalSocre(rich_part)

	self.common_pingfen_num = 0		-- 装备评分：基础属性 + 特殊属性
	self.comp_pingfen_num = 0		-- 综合评分：基础属性 + 特殊属性 + 强化属性 +仙品属性 + 进化属性

	-- 基础属性
	local attr_list
	local attr_cfg = fle_data:GetHolyEquipItemCfg(item_id)
	if is_wear then
		--强化属性
		local strength_level = is_browse and 0 or fle_data:GetPartStrengthenLevel(slot, part)
		if strength_level > 0 then
			local strength_cfg = fle_data:GetFLEStrengthenCfgBySlotPartLv(slot, part, strength_level)
			attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(attr_cfg, strength_cfg)
		else
			attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
		end
	else
		attr_list = EquipWGData.GetSortAttrListByCfg(attr_cfg)
	end

	local temp_list = {}
	local base_num = 0
	local uplevel_str = ""
	local is_per, per_value, per_str
	for k, v in pairs(attr_list) do
		if v.attr_value > 0 then
			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str)
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str)
			per_value = is_per and v.attr_value / 100 or v.attr_value
			per_str = is_per and "%" or ""
			temp.attr_value = per_value .. per_str
			uplevel_str = ""
			if v.attr_next_value and v.attr_next_value > 0 then
				uplevel_str = string.format("（%s+%s）", Language.Tip.StrengthText, v.attr_next_value)

				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
				self.comp_pingfen_num = self.comp_pingfen_num + v.attr_next_value * base_num
			end
			temp.add_str = uplevel_str

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num
			self.comp_pingfen_num = self.comp_pingfen_num + v.attr_value * base_num

			table.insert(temp_list, temp)
		end
	end

	if not IsEmptyTable(temp_list) then
		self.base_tips:SetBaseAttribute(temp_list)
	end
	-- 特殊属性

	-- 星级属性
	local rand_attr_list, rand_attr_score
	if is_wear then
		if is_browse then

		else
			rand_attr_list, rand_attr_score = fle_data:GetPartRandAttrAndScore(slot, part)
		end
	end

	if not IsEmptyTable(rand_attr_list) then
		local rand_info_list = {}
		rand_info_list.attr_list = rand_attr_list
		local grade = is_browse and 0 or fle_data:GetPartGrade(slot, part)
		local grade_str = string.format(Language.FairyLandEquipment.EvolveGradeTipDesc, grade)
		rand_info_list.title_name = Language.Tip.StarAttr .. " " .. grade_str
		self.base_tips:SetFLEFEvolveAttribute(rand_info_list)
		self.comp_pingfen_num = self.comp_pingfen_num + rand_attr_score
	end

	-- 装备评分
	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)

	-- 合成配置
	local show_tips, process_list = ItemShowWGData.Instance:GetHolyEquipColorProcessData(he_data)
	if show_tips then
		local part_name = Language.FairyLandEquipment.PartName[part]
		local title_str = string.format(Language.FairyLandEquipment.EquipColorTitleDesc, slot_cfg.short_name, part_name)
		local compose_info = {title_desc = title_str, equip_list = process_list, show_add = false,}
		self.base_tips:SetEquipTipsComposePanel(compose_info)
	end

	-- 获取途径
	-- self:ShowItemGetDesc(item_cfg)
end

--解析装备tips
function TipsEquipComparePanel:ParseEquip(item_cfg)
	self.common_pingfen_num = 0
	self.comp_pingfen_num = 0
	if self.data == nil or item_cfg == nil then
		return
	end

	local attribute = {}
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
	local data_param = self.data.param
	self:ParseAttribute(attribute, item_cfg)

	if self.from_view == ItemTip.EQUIPMENT_CONTRAST or self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
		self:InitDataParam(self.data, item_cfg, equip_part)
	end

	for i, v in pairs(attribute) do
		local list = attribute[i]
		if ATTR_STONE == i then
			self:SetAttrStone()
		elseif ATTR_SHENGPIN == i then    					--升品属性
			self:SetAttrShengPin(list, item_cfg, equip_part)
		elseif ATTR_STONE_JL == i and self.is_mine and data_param.stone_baptize_level and data_param.stone_baptize_level > 0 then
			self:ShowAttrStoneJl(equip_body_index)
		elseif ATTR_LEGEND == i then 					 -- 仙品属性
			self:SetXianPinAttrInfo()
		elseif ATTR_SUIT == i and data_param.self_suit_open and  data_param.suit_open_num and data_param.suit_open_num > 0
		and (self.from_view == ItemTip.EQUIPMENT_CONTRAST or self.from_view == ItemTip.FROME_BROWSE_ROLE) then
			self:SetAttrSuit(item_cfg, equip_body_index)
		elseif ATTR_BASE == i then
			self:SetBaseAttrInfo(list)
		elseif ATTR_STRENGTHEN == i then
			self:ShowAttrStrengthen(item_cfg)
		elseif ATTR_JIPING == i then
			self:SetAttrJiPing(item_cfg)
		elseif ATTR_PINK_SUIT == i then
			self:SetEquipSpecialAttr(item_cfg, EQUIP_SPECIAL_ATTR_TYPE.PINK)
		elseif ATTR_GOD_SUIT == i then
			self:SetEquipSpecialAttr(item_cfg, EQUIP_SPECIAL_ATTR_TYPE.GOLD)
		elseif ATTR_MORE_COLOR == i then
			self:SetEquipSpecialAttr(item_cfg, EQUIP_SPECIAL_ATTR_TYPE.COLOR)
		elseif ATTR_XIANMENG_CANGKU == i then
			self:SetXianMengCangKuAttr(self.data)
		elseif REMIND_HOW_COMPOSE == i then
			self:ShowEquipTipsCompose()
		elseif ATTR_EQUIP_SKILL == i then
			self:SetEquipSkillShow()
		elseif ATTR_LINGYUE == i then
			self:SetAttrLingYu()
		elseif ATTP_YULING == i then
			self:SetAttrYuLing(equip_part)
		elseif ATTP_ZHUSHEN == i then		--铸神
			self:SetAttrZhuShen(equip_part) 
		elseif ATTP_XILIAN == i then
			self:SetAttrXilian(item_cfg, equip_body_index)
		end
	end

	self:SetEquipNormalInfo()
	self:SetEquipTitleNormalInfo()
end

function TipsEquipComparePanel:SetEquipTitleNormalInfo()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		return
	end

	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local sex = RoleWGData.Instance:GetRoleSex()
	local prof = RoleWGData.Instance:GetRoleProf()

	local rich_part_text = ""

	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		rich_part_text = string.format(Language.Tip.Order, data.grade_level) .. Language.TianShen.EquipNameList[TianShenWGData.Equip_Pos[item_cfg.sub_type]]
	elseif HiddenWeaponWGData.IsHiddenWeapon(item_cfg.sub_type) then
		rich_part_text = HiddenWeaponWGData.Instance:GetEquipPartStr(item_cfg.id)
	else
		local sex_str = Language.Common.SexName[item_cfg.limit_sex] or ""
		rich_part_text = string.format(Language.Tip.Order, item_cfg.order) .. sex_str .. Language.Stone[equip_part]
	end

	local limit_sex = item_cfg.limit_sex or 0
	local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_part, item_cfg.order)
	local rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
	local is_limit_zhuanzhi_prof = item_cfg.is_limit_zhuanzhi_prof and tonumber(item_cfg.is_limit_zhuanzhi_prof) or -1

	local zhuan_str = ""
	if item_cfg.is_zhizun == 1 then
		rich_type_text = Language.Common.ProfName[limit_sex][item_cfg.limit_prof]
	elseif mix_limit_prof > 0 and item_cfg.limit_prof ~= 5 and is_limit_zhuanzhi_prof ~= 0 then
		--2025.6.9 策划要求屏蔽转数.
		-- zhuan_str = string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
		rich_type_text = Language.Common.ProfName[limit_sex][mix_limit_prof * 10 + item_cfg.limit_prof] .. zhuan_str
	elseif mix_limit_prof > 0 and item_cfg.limit_prof == 5 and is_limit_zhuanzhi_prof == 1 then
		-- zhuan_str = string.format(Language.F2Tip.Zhuan, CommonDataManager.GetDaXie(mix_limit_prof))
		rich_type_text = rich_type_text .. zhuan_str
	end

	local sex_color = TIPS_COLOR.SOCRE
	local prof_color = TIPS_COLOR.SOCRE
	if self.from_view ~= ItemTip.FROME_BROWSE_ROLE and self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
		local zhuanzhi_num = RoleWGData.Instance:GetZhuanZhiNumber()
		local sex_limit = item_cfg.limit_sex ~= sex and item_cfg.limit_sex ~= GameEnum.SEX_NOLIMIT
		local prof_limit = item_cfg.limit_prof ~= prof and item_cfg.limit_prof ~= GameEnum.ROLE_PROF_NOLIMIT
		local prof_level_limit = zhuanzhi_num < mix_limit_prof
		sex_color = sex_limit and COLOR3B.D_RED or sex_color
		prof_color = (prof_limit or (prof_level_limit and is_limit_zhuanzhi_prof == 1)) and COLOR3B.D_RED or prof_color
	end

	self.base_tips:SetEquipSocre(string.format(Language.Tip.ZhuangBeiLeiXing_1, sex_color, rich_part_text))
	self.base_tips:SetSyntheticalSocre(string.format(Language.Tip.ZhuangBeiProf, prof_color, rich_type_text))
end

function TipsEquipComparePanel:SetEquipNormalInfo()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)

	local normal_attr_info = {}
	local str_limit_level = RoleWGData.GetLevelString(item_cfg.limit_level)
	local str_cultivation_limit = CultivationWGData.Instance:GetEquipLevelLimitStr()
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = str_limit_level..str_cultivation_limit}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)
end

function TipsEquipComparePanel:OperationClickHandler(tag)
	if self.data == nil then
		return
	end

	self.handle_type = tag
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg == nil then
		return
	end

	if self.handle_type == ItemTip.QUICK_ADDITEM then
		SysMsgWGCtrl.SendGmCommand("additem", self.data.item_id .. " 10 0")
	elseif self.handle_type == ItemTip.HANDLE_EQUIP then		--装备
		local is_need_alert, alert_content = NewYinJiJiChengWGData.Instance:CheckIsNeedAlertWhenEquipLess(self.target_data, self.data)
		if ItemWGData.GetIsXiaogGui(self.data.item_id) then
			BagWGCtrl.Instance:SendUseItem(self.data.index, 1)
		elseif ItemWGData.GetIsFabao(self.data.item_id) then
			EquipmentWGCtrl.Instance:SendEquipCrossEquipOpera(1, self.data.index)
		elseif EquipmentWGData.Instance:CheckIsActiveSuitEquip(self.data.item_id) then --判断对应的部位是否是套装
			local open_tip = EquipmentWGData.Instance:CheckIsLimitTips(self.data.item_id, self.data.index)
			local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)

			local function func2()
				if equip_index ~= -1 then
					BagWGCtrl.Instance:SendUseItem(self.data.index, 1, equip_index, item_cfg.need_gold)
				end
			end

			local function func()
				if is_need_alert then
					TipWGCtrl.Instance:OpenAlertTips(alert_content, func2)
				else
					func2()
				end
			end

			if open_tip then
				EquipmentWGCtrl.Instance:OpenSuitTip(self.data.index, RONG_LIAN_CONTENT_TYPE.EQUIP, func)
			else
				func()
			end
		elseif is_need_alert then --判断对应的部位是否是套装
			local function func2()
				local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
				if equip_index ~= -1 then
					BagWGCtrl.Instance:SendUseItem(self.data.index, 1, equip_index, item_cfg.need_gold)
				end
			end
			TipWGCtrl.Instance:OpenAlertTips(alert_content, func2)
		else
			local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			if equip_index ~= -1 then
			 	BagWGCtrl.Instance:SendUseItem(self.data.index, 1, equip_index, item_cfg.need_gold)
			end
		end
	elseif self.handle_type == ItemTip.HANDLE_COMPOSE then		--合成
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
			-- local can_compose, tab_str, compose_cfg
			-- local is_xqsc, product_data = ComposeWGData.Instance:GetIsXQSCStuff(self.data)

			-- if is_xqsc then
			-- 	tab_str = "other_xianqi_stuff"
			-- 	FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_str,
			-- 					{to_ui_name = 0, open_param = product_data.sub_type, to_ui_param = product_data.child_type})
			-- else
			-- 	can_compose, tab_str, compose_cfg = EquipmentWGData.Instance:GetEquipIsCanCompose(self.data)
			-- end

			local can_compose, tab_str, compose_cfg = EquipmentWGData.Instance:GetEquipIsCanCompose(self.data)

			if can_compose then
				local open_param, to_ui_param
				local acc_list = EquipmentWGData.Instance:GetEquinHeChengAccordionDataList(compose_cfg.title_index)
				if not IsEmptyTable(acc_list) then
					for acc_k, acc_v in ipairs(acc_list) do
						if acc_v.name_type == compose_cfg.type and acc_v.star_level == compose_cfg.compose_equip_best_attr_num then
							open_param = acc_k
							for child_k, child_v in ipairs(acc_v.child) do
								if child_v.order == compose_cfg.order then
									to_ui_param = child_k
									break
								end
							end
						end
					end

					if equip_part == GameEnum.EQUIP_INDEX_XIANJIE or equip_part == GameEnum.EQUIP_INDEX_XIANZHUO then
						FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_str, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param, jump_to_equip_data = self.data})
					else
						FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_str, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param})
					end
				end
			end
		else
			FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, nil, {is_stuff = true, item_id = self.data.item_id})
		end
	elseif self.handle_type == ItemTip.HANDLE_EXCHANGE then		--兑换
		if self.from_view == ItemTip.FROM_STORGE_ON_GUILD_STORGE	then--新增关于帮派仓库兑换的逻辑判断
			local star_level = self.data.star_level or 0
			local need_price = GuildCangKuWGData.Instance:GetStorageAccessScore(item_cfg.order,item_cfg.color,star_level)
			local has_score = GuildCangKuWGData.Instance:GetStorgeScore()
			if need_price > has_score then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.CangKuScoreNot)
				return
			end
			GuildWGCtrl.Instance:SendStorgetOutItem(self.data.index, 1, self.data.item_id)
		end
	elseif self.handle_type == ItemTip.HANDLE_SALE then
		self:OnOpenPopNum()

	elseif self.handle_type == ItemTip.HANDLE_STORGE then
		-- 跨服中暂时无法操作
		if IS_ON_CROSSSERVER then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
			return
		end
		RoleBagWGCtrl.Instance.view:HandleItemTipCallBack(self.data, self.handle_type, self.handle_param_t)

	elseif self.handle_type == ItemTip.HANDLE_BACK_BAG or self.handle_type == ItemTip.HANDLE_TAKEOFF then
		-- 跨服中暂时无法操作
		if IS_ON_CROSSSERVER then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
			return
		end
		if self.from_view == ItemTip.FROM_SJ_JC_OFF then
			EquipmentWGCtrl.Instance:TakeOffSjJcCell(self.handle_param_t)
		elseif ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) and self.handle_type ~= ItemTip.HANDLE_BACK_BAG then
			MarryWGCtrl.Instance:SendTakeOffEquip()
		else
			if self.handle_type == ItemTip.HANDLE_BACK_BAG then
				if self.from_view == ItemTip.FROM_STORGE_ON_BAG_STORGE then
		   			RoleBagWGCtrl.Instance.view:HandleItemTipCallBack(self.data, self.handle_type, self.handle_param_t)
		   		end
			elseif self.handle_type == ItemTip.HANDLE_TAKEOFF then
				if ItemWGData.GetIsFabao(self.data.item_id) then
					EquipmentWGCtrl.Instance:SendEquipCrossEquipOpera(0, 0)
				elseif ItemWGData.GetIsXiaogGui(self.data.item_id) then
					RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_TAKEOFF)
			   	end
		   	end
		end
 	elseif self.handle_type == ItemTip.HANDLE_USE then			--使用
 		local prof = RoleWGData.Instance:GetRoleProf() or 0
 		if item_cfg.limit_prof and item_cfg.limit_prof ~= 5 and item_cfg.limit_prof ~= prof then
 			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ProfDif)
 			return
 		end
 		if RoleWGData.Instance.role_vo.level >= item_cfg.limit_level then
	 		if item_cfg.click_use == 1 then
	 			--背包,仓库,扩展格子，特殊处理
	 			if item_cfg.id == 26914 or item_cfg.id == 26915 then
	 				local storage_type = (item_cfg.id == 26914) and GameEnum.STORAGER_TYPE_BAG or GameEnum.STORAGER_TYPE_STORAGER
	 				local type_name = (storage_type == GameEnum.STORAGER_TYPE_BAG) and Language.Role.BeiBao or Language.Role.CangKu
					local item_num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id) or 0
					local can_open_num, need_number, old_need_num = RoleBagWGData.Instance:GetCanOpenHowManySlot(storage_type, item_num)

					if can_open_num < 0 then
						self:Close()
						SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Role.NoMoreSlot, type_name))
						return
					elseif can_open_num < 1 then
						self:Close()
						SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Role.NeedOpenSlotItem, type_name, need_number, item_cfg.name))
						return
					end

					local label_str = string.format(Language.Role.IfOpenSlotWithItem, old_need_num, item_cfg.name, can_open_num, type_name)
					local ok_func = function()
						BagWGCtrl.Instance:SendKnapsackStorageExtendGridNum(storage_type, can_open_num, 0)
						self:Close()
					end
					local cancel_func = function()
						self:Close()
					end
					TipWGCtrl.Instance:OpenAlertTips(label_str, ok_func, cancel_func)
					return
	 			end

	 			if item_cfg.max_open_num and item_cfg.max_open_num > 0 then
	 				local select_gift_data = ItemWGData.Instance:GetItemListInGift(self.data.item_id)
	 				local gift_item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	 				if gift_item_cfg and gift_item_cfg.star_describe == 1 then
	 					select_gift_data = ItemWGData.Instance:GetGiftConfig(self.data.item_id)
	 				end

	 				FunctionChooseWGCtrl.Instance:SetSelectGifData(self.data.index, item_cfg.max_open_num, select_gift_data, self.data.item_id)
	 			else
 					BagWGCtrl.Instance:SendUseItem(self.data.index, self.handle_param_t.num, self.data.sub_type, item_cfg.need_gold)
	 			end

	 			if item_cfg.open_panel ~= "" then
	 				self:OpenPanelByName(item_cfg.open_panel)
	 			end
	 		elseif item_cfg.click_use == 2 and (item_cfg.ignore_num == nil or item_cfg.ignore_num == 0)then					--批量使用
	 			if ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index) == 1 then
	 				BagWGCtrl.Instance:SendUseItem(self.data.index, self.handle_param_t.num, self.data.sub_type, item_cfg.need_gold)
	 			else
					if ItemWGData.Instance:GetIsCanUseItem(self.data.item_id, item_cfg.use_daytimes) then
	 					OfflineRestWGCtrl.Instance:OpenUserOfflineView(self.data.item_id)
	 				else
						BagWGCtrl.Instance:SendUseItem(self.data.index, GameMath.Round(1), 0, item_cfg.need_gold)
	 				end
	 			end
	 		elseif item_cfg.click_use == 2 and item_cfg.ignore_num == 1 then
	 			BagWGCtrl.Instance:SendUseItem(self.data.index, self.data.num)
	 		elseif item_cfg.click_use == 0 and item_cfg.open_panel ~= "" then
	 			self:OpenPanelByName(item_cfg.open_panel)
	 		end
	 	else
	 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.UseLevelLimit)
	 	end

 	elseif self.handle_type == ItemTip.BAOXIANG_QUCHU then				--从寻宝仓库取出

 	elseif self.handle_type == ItemTip.JINGLING_BAOXIANG_QUCHU then		--从精灵寻宝仓库取出

	elseif self.handle_type == ItemTip.SHICHANG_CHEHUI then
		TipWGCtrl.Instance:OpenAlertTips(Language.Market.AlerTips, BindTool.Bind2(MarketWGCtrl.Instance.SendRemoveGoods, MarketWGCtrl.Instance, self.data.auction_index))
	elseif self.handle_type == ItemTip.SHICHANG_GOUMAI then
		if self.from_view == ItemTip.FROM_PANIC_ITEM then
			ServerActivityWGCtrl.Instance:SendRAPanicBuyOperaReq(RA_PANICBUY_OPERA_TYPE.RA_PANICBUY_OPERA_BUY, self.handle_param_t.seq)
			return
		end
		MarketWGCtrl.Instance:OpenBuyMarketGoodsAlert(self.data.auction_index, self.data.has_password, self.data.total_price, self.data.item_id)
	elseif self.handle_type == ItemTip.RONGHE then
		if ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) then
			local qingyuan_equip = MarryWGData.Instance:GetEquipCfgById(self.data.item_id) or {}
			if nil ~= qingyuan_equip then
				MarryWGCtrl.Instance:SendQingyuanUpLevel(self.data.item_id, qingyuan_equip.slot_idx)
			end
		end
	elseif self.handle_type == ItemTip.HANDLE_FORGE then
		local param_t = nil
		if self.from_view == ItemTip.FROM_BAG_EQUIP then
			param_t = {item_index = self.data.index}
			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_strength", param_t)
		end
	elseif self.handle_type == ItemTip.HANDLE_RECOVER then
		if(self.from_view == ItemTip.FROM_BAG_ON_BAG_SALE and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_JINGLING) then
			self:OnOpenPopNum()
		elseif item_cfg.sub_type == GameEnum.EQUIP_TYPE_JINGLING then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		elseif ItemWGData.Instance:GetIsQingyuanEquip(self.data.item_id) then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		elseif item_cfg.recycltype == 2 and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		else
			BagWGCtrl.Instance:SendDiscardItem(self.data.index, self.data.num, self.data.item_id, self.data.num, 1)
		end
	elseif self.handle_type == ItemTip.HANDLE_RONGLIAN then
		-- RoleBagWGCtrl.Instance:OpenMeltView()
		ViewManager.Instance:Open(GuideModuleName.RoleBagViewMeltingView)
	elseif self.handle_type == ItemTip.HANDLE_GONGXIAN then--帮派仓库界面 从背包中贡献
		if self.from_view == ItemTip.FROM_BAG_ON_GUILD_STORGE then
			self:OnOpenPopNum()
		else
			FunOpen.Instance:OpenViewByName(GuideModuleName.GuildView, "guild_cangku")
		end
	elseif self.handle_type == ItemTip.HANDLE_TAKEON then
		if self.from_view == ItemTip.FROM_SJ_JC_ON then
			EquipmentWGCtrl.Instance:TakeOnSjJcCell(self.data, self.handle_param_t)
		end
	elseif self.handle_type == ItemTip.HANDLE_QINGYUSN then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_jiehun")
	elseif self.handle_type == ItemTip.HANDLE_EQUIP_DECOMPOSE then
		self:OnOpenPopNum()
	elseif self.handle_type == ItemTip.HANDLE_XUFEI_PUTON then
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.data.item_id)
		local is_use_bind_gold = 1
		local bind_gold = RoleWGData.Instance.role_info.bind_gold
		if (ItemWGData.GetIsUseBindGold(self.data.item_id) == 1 and bind_gold < xiaogui_cfg.bind_gold_price) or ItemWGData.GetIsUseBindGold(self.data.item_id) == 0 then
			is_use_bind_gold = 0
		end
		RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_PUTON, 0, is_use_bind_gold)
	elseif self.handle_type == ItemTip.HANDLE_XUFEI_INBAG then
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.data.item_id)
		local is_use_bind_gold = 1
		local bind_gold = RoleWGData.Instance.role_info.bind_gold
		if (ItemWGData.GetIsUseBindGold(self.data.item_id) == 1 and bind_gold < xiaogui_cfg.bind_gold_price) or ItemWGData.GetIsUseBindGold(self.data.item_id) == 0 then
			is_use_bind_gold = 0
		end
		RoleWGCtrl.Instance:SendImpGuardOperaReq(IMP_GUARD_REQ_TYPE.IMP_GUARD_REQ_TYPE_RENEW_KNAPSACK, self.data.index, is_use_bind_gold)
	elseif self.handle_type == ItemTip.HANDLE_BAOSHI_INLAY then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_baoshi")
	elseif self.handle_type == ItemTip.HANDLE_LINGYU_INLAY then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_lingyu")
	elseif self.handle_type >= ItemTip.FROM_CUSTOM_BTN then
		local btn_key = self.handle_type - ItemTip.FROM_CUSTOM_BTN
		if self.btn_callback_event[btn_key] ~= nil and self.btn_callback_event[btn_key].callback ~= nil then
			self.btn_callback_event[btn_key].callback()
		end
	elseif self.handle_type == ItemTip.SHICHANG_WORLD_SELL then
		-- 上架拍卖
		MarketWGCtrl.Instance:OpenMarketTipItemView(self.data)
	end
	TipWGCtrl.Instance:CloseContrastItemTip()
end

function TipsEquipComparePanel:OpenPanelByName(panel_name)
	if self.data and self.data.item_id == COMMON_CONSTS.GuildTanheItemId and RoleWGData.Instance.role_vo.guild_id == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		return
	end
	local item_id = nil
	if self.data then
		item_id = self.data.item_id
	end
	FunOpen.Instance:OpenViewNameByCfg(panel_name, item_id)
end

-- 打开数字键盘
function TipsEquipComparePanel:OnOpenPopNum()
	if self.data == nil then
		return
	end

	local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	if pop_num_view then
		local maxnum = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index)
		if maxnum == 1 then  --数量为1时不弹
			self:OnOKCallBack(maxnum)
		else
			pop_num_view:Open()
			pop_num_view:SetText(maxnum)
			pop_num_view:SetMaxValue(maxnum)
		end
	end
end

-- 数字键盘确定按钮回调
function TipsEquipComparePanel:OnOKCallBack(num)
	if self.data == nil then return end
	self.item_num = tonumber(num)
	local maxnum = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index)
	if self.item_num > maxnum then
		self.item_num = maxnum
	elseif self.item_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ChongXinShuRu)
		return
	end
	self.handle_param_t.num = self.item_num

	local item_cfg,big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)

	if self.handle_type == ItemTip.HANDLE_USE then
		if OfflineRestWGData.Instance:IsOverstep(self.data.item_id, self.handle_param_t.num) then
			OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(self.data.item_id, self.handle_param_t.num)
		else
			BagWGCtrl.Instance:SendUseItem(self.data.index, self.handle_param_t.num, self.data.sub_type, item_cfg.need_gold)
		end

	elseif  self.handle_type == ItemTip.HANDLE_SALE then
		if item_cfg and item_cfg.is_rare and item_cfg.is_rare == 1 then

			local ok_func = function()
				local item_num = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index, self.data.item_id)
				BagWGCtrl.Instance:SendDiscardItem(self.data.index, num, self.data.item_id, item_num, 0)
			end
			TipWGCtrl.Instance:OpenAlertTips(Language.Tip.IsRare, ok_func)
			return
		end
		local item_num = ItemWGData.Instance:GetItemNumInBagByIndex(self.data.index, self.data.item_id)
		BagWGCtrl.Instance:SendDiscardItem(self.data.index, num, self.data.item_id, item_num, 0)
	elseif self.from_view == ItemTip.FROM_BAG then
		if self.handle_type == ItemTip.HANDLE_EQUIP_DECOMPOSE then
			if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
				local legend_num = self.data.param and self.data.param.star_level or 0
				local decompose_cfg = EquipmentWGData.Instance:GetEquipDecomposeByID(item_cfg.id, legend_num)
				if not decompose_cfg then
					return
				end

				local desc = EquipmentWGData.Instance:GetEquipDeComposeDescByItemid(item_cfg.id, legend_num)
				local index_list = {}
				index_list[1] = self.data.is_bind
				local index_list = {}
				index_list[1] =self.data.index
				local ok_func = function()
					EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_DECOMPOSE, self.data.item_id, decompose_cfg.decompose_equip_best_attr_num, index_list)
				end
				TipWGCtrl.Instance:OpenAlertTips(desc, ok_func)
				return
			end

			local tab_index = ItemWGData.ComposeGetTypeById(self.data.item_id)
			FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_index * 1000, {is_stuff = true, item_id = self.data.item_id})
		end
 	elseif self.from_view == ItemTip.FROM_BAG_ON_BAG_SALE_SHITU then
 		MasterWGCtrl.Instance:HandleItemTipCallBack(self.data, self.handle_type, self.handle_param_t)
	end
end

function TipsEquipComparePanel:SetAttrStone()
	local data = self.data
	local data_param = data.param
	local baoshi_t = data_param.baoshi_t or {}
	if self.from_view == ItemTip.EQUIPMENT_CONTRAST then
		local baoshi_info = EquipmentWGData.Instance:GetStoneInfo()
		baoshi_t = baoshi_info[data.index] or {}
	end

	local function get_attr_list(stone_cfg, baoshi_color)
		local attr_list = {}
		for j = 1, GameEnum.EQUIP_BAOSHI_ATTR_NUM do
			local type = stone_cfg["attr_type" .. j]
			local value = stone_cfg["attr_val" .. j]
			if type and value and type > 0 and value > 0 then
				local temp = {}
				local name_str = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
				temp.attr_str = ToColorStr(name_str, baoshi_color)
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
				local value_str = ""
				if is_per then
					value_str = string.format("+%.2f%%", value / 100)
				else
					value_str = "+" .. value
				end
				temp.attr_num = ToColorStr(value_str, baoshi_color)
				attr_list[j] = temp
				-- 宝石属性评分
				local num = TipWGData.Instance:GetXianPingSpecialAttrByOrder(type, value)
				self.comp_pingfen_num = self.comp_pingfen_num + num
			else
				break
			end
		end

		return attr_list
	end

	local stone_info_list = {}
	local baoshi_item_id = 0
	for i = 1, GameEnum.MAX_STONE_COUNT do
		local temp = {}
		local slot_data = baoshi_t[i - 1]
		baoshi_item_id = slot_data and slot_data.item_id or 0
		if baoshi_item_id > 0 then
			local baoshi_cfg = ItemWGData.Instance:GetItemConfig(baoshi_item_id)
			local stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(baoshi_item_id)
			if baoshi_cfg and stone_cfg then
				local baoshi_color = ITEM_COLOR[baoshi_cfg.color]
				temp.name = ToColorStr(baoshi_cfg.name, baoshi_color)
				temp.icon = "a2_bsi_" .. stone_cfg.stone_type
				temp.attr_list = get_attr_list(stone_cfg, baoshi_color)
				temp.sort = i
			end
		else
			local is_open = slot_data and slot_data.is_open == 1
			temp.is_lock = not is_open
			temp.sort = is_open and (i + 100) or (i + 1000)
		end
		stone_info_list[i] = temp
	end

	table.sort(stone_info_list, SortTools.KeyLowerSorter("sort"))
	self.base_tips:SetStoneAttribute(stone_info_list)
end

-- 御灵
function TipsEquipComparePanel:SetAttrYuLing(equip_index)
	local data = self.data
	local yuling_data = data.yuling or {}
	local yuling_info_list = {}
	local can_show_yuling = false

	for i = GameEnum.EQUIP_INDEX_TOUKUI, GameEnum.EQUIP_INDEX_XIANZHUO do
		local temp = {}
		local equip_yuling_item_data = yuling_data[i] or {}
		local is_unlock = equip_yuling_item_data and equip_yuling_item_data.is_unlock or 0
		local is_open = is_unlock == 1
		if is_open then
			temp.is_lock = not is_open
			temp.icon = "a2_fl_bs_" .. equip_yuling_item_data.show_icon
			temp.sort = i
			can_show_yuling = true
		else
			temp.is_lock = not is_open
			temp.sort = is_open and (i + 100) or (i + 1000)
		end

		yuling_info_list[i] = temp
	end

	if not IsEmptyTable(yuling_data) then
		yuling_info_list.add_per = yuling_data.equip_part_add_per or 0
		local equip_attr_data = EquipmentWGData.Instance:GetEquipCapAndAttrList(equip_index)
		yuling_info_list.attr_list = not IsEmptyTable(equip_attr_data) and equip_attr_data.attr_list or {}
	else
		yuling_info_list.attr_list = {}
		yuling_info_list.add_per = 0
	end

	if can_show_yuling then
		table.sort(yuling_info_list, SortTools.KeyLowerSorter("sort"))
		self.base_tips:SetYuLingAttribute(yuling_info_list)
	end
end

--铸神
function TipsEquipComparePanel:SetAttrZhuShen(equip_index)
	local equip_level = EquipmentWGData.Instance:GetZhuShenEquipPartInfo(equip_index)
	if self.from_view == ItemTip.FROM_BAG then
		equip_level = 0
	end

	local equip_attr_cfg = EquipmentWGData.Instance:GetZhuShenEquipAttrList(equip_index, equip_level)
	local base_num = 0
	local attribute = AttributePool.AllocAttribute()
	
	if not IsEmptyTable(equip_attr_cfg) then
		local strong_list = {}
		for k,v in pairs(equip_attr_cfg) do
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_str, true)
				local temp = {
					attr_name = attr_name,
					attr_value = v.attr_value
				}
				strong_list[#strong_list + 1] = temp

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_str) or 0
			self.common_pingfen_num = self.common_pingfen_num + v.attr_value * base_num

			attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
		end

		local star_info_list = {}
		star_info_list.attr_list = strong_list
		star_info_list.title_name = Language.EquipmentZhuShen.ZhuHunTipsAttr
		self.base_tips:SetZhuShenAttribute(star_info_list)
	end
end

--灵玉属性
function TipsEquipComparePanel:SetAttrLingYu()
	local data = self.data
	local data_param = data.param
	local lingyu_t = data_param.lingyu_t or {}
	if self.from_view == ItemTip.EQUIPMENT_CONTRAST then
		-- local lingyu_info = EquipmentLingYuWGData.Instance:GetLingYuInfo()
		-- lingyu_t = lingyu_info[data.index] or {}
		lingyu_t = EquipmentLingYuWGData.Instance:GetLingYuInfoListByIndex(data.index)
	end

	local function get_attr_list(lingyu_cfg, lingyu_color)
		local attr_list = {}
		for j = 1, GameEnum.EQUIP_LINGYU_ATTR_NUM do
			local type = lingyu_cfg["attr_type" .. j]
			local value = lingyu_cfg["attr_val" .. j]
			if type and value and type > 0 and value > 0 then
				local temp = {}
				local name_str = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
				temp.attr_str = ToColorStr(name_str, lingyu_color)
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
				local value_str = ""
				if is_per then
					value_str = string.format("+%.2f%%", value / 100)
				else
					value_str = "+" .. value
				end
				temp.attr_num = ToColorStr(value_str, lingyu_color)
				attr_list[j] = temp
				-- 灵玉属性评分
				local num = TipWGData.Instance:GetXianPingSpecialAttrByOrder(type, value)
				self.comp_pingfen_num = self.comp_pingfen_num + num
			else
				break
			end
		end

		return attr_list
	end

	local lingyu_info_list = {}
	local lingyu_item_id = 0
	local can_show_lingyu = false

	for i = 1, GameEnum.MAX_LINGYU_COUNT do
		local temp = {}
		local slot_data = lingyu_t[i - 1]
		lingyu_item_id = slot_data and slot_data.item_id or 0
		if lingyu_item_id > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(lingyu_item_id)
			local lingyu_cfg = EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(lingyu_item_id)
			if item_cfg and lingyu_cfg then
				local lingyu_color = ITEM_COLOR[item_cfg.color]
				temp.name = ToColorStr(item_cfg.name, lingyu_color)
				temp.icon = LINGYU_TYPE_ICON[lingyu_cfg.lingyu_type]
				temp.attr_list = get_attr_list(lingyu_cfg, lingyu_color)
				temp.sort = i
				can_show_lingyu = true
			end
		else
			local is_open = slot_data and slot_data.is_open == 1
			temp.is_lock = not is_open
			temp.sort = is_open and (i + 100) or (i + 1000)
		end
		lingyu_info_list[i] = temp
	end

	if can_show_lingyu and not IsEmptyTable(lingyu_info_list) then
		table.sort(lingyu_info_list, SortTools.KeyLowerSorter("sort"))
		self.base_tips:SetLingYuAttribute(lingyu_info_list)
	end
end

function TipsEquipComparePanel:ShowAttrStoneJl(equip_body_index)
	local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(equip_body_index)
	local refine_cfg = EquipmentWGData.Instance:GetStoneRefineByLevel(refine_level)
	if refine_cfg then
		local info = {}
		info.title = Language.F2Tip.BSJLTitle
		info.desc = string.format(Language.Equipment.TipsRefineUpDesc, refine_cfg.add_attribute_pct / 100)
		self.comp_pingfen_num = self.comp_pingfen_num + EquipmentWGData.Instance:GetBSJLScoreByPart(equip_body_index)
		self.base_tips:SetBSJLAttribute(info)
	end
end

function TipsEquipComparePanel:SetAttrShengPin(list, item_cfg, equip_part)
	local info_list = {}
	local star_count = EquipmentWGData.Instance:GetNewEquipShengPinStarInfoByPart(equip_part) or 0
	if star_count > 0 and self.data.frombody then
		local attr_list = {}
		local shengpin_cfg = EquipmentWGData.Instance:GetNewEquipShengPinCfg(star_count, equip_part)
		local text_color = ITEM_COLOR[shengpin_cfg.star_color or 0]
		local quality_text = Language.Equip.ShengPin[shengpin_cfg.quality_grade + 1]
		for i,v in ipairs(ShengPinSortAttrList) do
			if shengpin_cfg[v] and shengpin_cfg[v] > 0 then
				local temp = {}
				temp.attr_name = Language.Common.AttrNameList2[v]
				temp.attr_value = ""
				if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
					temp.attr_value = string.format("%s%%", shengpin_cfg[v] / 100)
				else
					temp.attr_value = shengpin_cfg[v]
				end
				temp.attr_value = temp.attr_value .. string.format(Language.Equip.ShengPinLimitText6, quality_text, star_count % 10)
				temp.attr_name = ToColorStr(temp.attr_name, text_color)
				temp.attr_value = ToColorStr(temp.attr_value, text_color)
				attr_list[#attr_list + 1] = temp
			end
		end

		if shengpin_cfg.quality_grade > 0 then
			local _, attr_per, attr_type = EquipmentWGData.Instance:GetNewShengPinAttrCfgByQuality(equip_part, shengpin_cfg.quality_grade)
			if attr_per > 0 then
				local temp = {}
				local attr_type_text = attr_type == "base_attr_per" and Language.Tip.CommonAttr or Language.Common.TipsAttrNameList[attr_type] or ""
				temp.attr_name = string.format("%s%s", Language.Stone[equip_part], attr_type_text)
				temp.attr_value = string.format("%s%%（%s）", attr_per * 0.01, quality_text)
				temp.attr_name = ToColorStr(temp.attr_name, text_color)
				temp.attr_value = ToColorStr(temp.attr_value, text_color)
				attr_list[#attr_list + 1] = temp
			end
		end
		info_list.attr_list = attr_list
		info_list.title_name = Language.F2Tip.ShenPinAttrTitle
	end

	self.comp_pingfen_num = self.comp_pingfen_num + self.shenping_base_pingfen_per
	if not IsEmptyTable(info_list) then
		self.base_tips:SetShengPinAttribute(info_list)
	end
end

--是否显示精练属性
function TipsEquipComparePanel:CanShowEquipStoneJLAttr()
	return self.from_view == ItemTip.FROM_BAG_EQUIP
			or self.from_view == ItemTip.FROM_EQUIPMENT
            or self.from_view == ItemTip.FROME_BROWSE_ROLE
            or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
			or self.from_view == ItemTip.FROM_EQUIMENT_HECHENG
			or self.from_view == ItemTip.EQUIPMENT_CONTRAST
end

function TipsEquipComparePanel:SetXianPinAttrInfo()
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    local legend_attr_list
    local legend_num = 0
	local is_exhibition = false 				-- 是否活动界面展示属性

	if self.data.param.xianpin_type_list then
        if self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
            legend_num = self.data.param.star_level or 0
            legend_attr_list = EquipWGData.GetPreviewLegendAttr(self.data, legend_num)
        else
            local show_baptize_add = self:CanShowEquipStoneJLAttr()
            legend_attr_list = EquipWGData.GetLegendAttr(self.data, show_baptize_add)
            legend_num = #legend_attr_list
        end
	else
		if self.from_view == ItemTip.FROM_FULILOGIN then
            legend_num = self.data.star_level or 0
		else
			legend_num = self.data.param.star_level or 0
		end

		legend_attr_list = EquipWGData.GetPreviewLegendAttr(self.data, legend_num)
		is_exhibition = true
	end

	local info_list = {}
	local attr_list = {}
	for i,v in ipairs(legend_attr_list) do
			local temp = {}
			local per_value = 1
			local per_str = ""
			if v.is_per and v.is_per ~= "" and v.is_per == 1 then
				per_value = 0.01
				per_str = "%"
			end

	        if is_exhibition then
	            if self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
	                local orgin_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.orgin_value, v.is_star_attr)
	                local add_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.value, v.is_star_attr)
	                self.common_pingfen_num = self.common_pingfen_num + orgin_pingfen_num
	                self.comp_pingfen_num = self.comp_pingfen_num + add_pingfen_num
	                local color = ITEM_COLOR[v.baptize_quality] or ITEM_COLOR[0]
	                if v.value and v.value > 0 then
	                    local to_ceil = math.ceil(v.value)
	                    to_ceil = to_ceil * per_value
	                    local to_show_num = math.floor(to_ceil * 100)
	                    to_show_num = to_show_num * 0.01

	                    temp.label = ToColorStr(string.format("%s   %s%s", v.desc, to_show_num, per_str), color)
	                else
	                    temp.label = ToColorStr(v.desc, color)
	                end

				info_list.title_name = Language.Tip.XianShuXing
	            else
	                local desc1 = v.must_get and Language.Role.EquipDesc3 or Language.Role.EquipDesc1
	                local desc1_color = v.must_get and COLOR3B.D_GLOD or COLOR3B.D_BLUE
	                local desc2 = string.format("<color=%s>%s</color>  <color=%s>%s</color>", TIPS_COLOR.ATTR_NAME, v.desc, TIPS_COLOR.ATTR_VALUE, v.value * per_value)
	                temp.label = ToColorStr(desc1, desc1_color)  .. "    " .. desc2
	                info_list.title_name = string.format(Language.Tip.RandXianShuXing, legend_num)
	            end
			else
				local orgin_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.orgin_value, v.is_star_attr)
				local add_pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.value, v.is_star_attr)
				self.common_pingfen_num = self.common_pingfen_num + orgin_pingfen_num
				self.comp_pingfen_num = self.comp_pingfen_num + add_pingfen_num
				local color = ITEM_COLOR[v.baptize_quality] or ITEM_COLOR[0]
				if v.value and v.value > 0 then
					local to_ceil = math.ceil(v.value)
					to_ceil = to_ceil * per_value
					local to_show_num = math.floor(to_ceil * 100)
					to_show_num = to_show_num * 0.01

					temp.label = ToColorStr(string.format("%s   %s%s", v.desc, to_show_num, per_str), color)
				else
					temp.label = ToColorStr(v.desc, color)
				end

				info_list.title_name = Language.Tip.XianShuXing
			end

			attr_list[i] = temp
		end

	if #attr_list > 0 then
		info_list.attr_list = attr_list
		self.base_tips:SetXianpinAttribute(info_list)
	end
end

function TipsEquipComparePanel:SetAttrSuit(item_cfg, equip_body_index)
	local info_list = {}
	local data_param = self.data.param

	if data_param.self_suit_open and data_param.suit_open_num > 0 then
		local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
		local list_data = EquipmentWGData.Instance:GetEquipmenSuitStoneAttr(item_cfg, equip_body_index, is_get_browse_data)

		if not IsEmptyTable(list_data) then
			local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
			local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
			local complete_suit_num = EquipmentWGData.Instance:GetEquipmenSuitAllActiveNum(equip_type)
			local suit_title = EquipmentWGData.Instance:GetEquipmenSuitTitleName(equip_body_seq, equip_type)
			info_list.suit_title = string.format("%s [ %d/%d ]", suit_title, data_param.suit_open_num, complete_suit_num)

			local suit_attr_list = {}
			for i, data in ipairs(list_data) do
				-- 计算评分
				local temp_list = {}
				local is_open = data.is_open
				local attr_color = is_open and COLOR3B.D_GREEN or COLOR3B.GRAY

				for k, v in ipairs(data.attr_list) do
					local temp = {}
					temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_type)
					temp.attr_value = ""
					if not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_type) then
						temp.attr_value = "+" .. v.value
						if is_open then
							local base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_type)
							self.comp_pingfen_num = self.comp_pingfen_num + base_num * v.value
						end
					else
						temp.attr_value = string.format("%.2f%%", v.value / 100)
						if is_open then
							self.comp_pingfen_num = self.comp_pingfen_num + TipWGData.Instance:GetXianPingSpecialAttrByOrder(v.attr_type, v.value)
						end
					end
					temp.attr_name = ToColorStr(temp.attr_name, attr_color)
					temp.attr_value = ToColorStr(temp.attr_value, attr_color)
					temp_list[k] = temp
				end

				local suit_name = string.format("%d%s", data.same_order_num, Language.Equip.SuitNumCompany)
				suit_attr_list[i] = {suit_name = ToColorStr(suit_name, attr_color), attr_list = temp_list}
			end

			info_list.suit_attr_list = suit_attr_list
		end
	end

	if not IsEmptyTable(info_list) then
		self.base_tips:SetXianqSuitAttribute(info_list)
	end

	-- local info_list = {}
	-- local data_param = self.data.param

	-- if data_param.suit_open_num > 0 then
	-- 	local is_get_browse_data = self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO
	-- 	local list_data = EquipmentWGData.Instance:GetEquipmenSuitStoneAttr(item_cfg, equip_body_index, is_get_browse_data)

	-- 	if not IsEmptyTable(list_data) then
	-- 		local suit_index_name = Language.Equip.TabSub3[data_param.suit_index + 1]
	-- 		local complete_suit_num = EquipmentWGData.Instance:GetEquipmenSuitALLNum(equip_index)
	-- 		local suit_title = ""
	-- 		if EquipmentWGData.GetEquipSuitTypeByPartType(equip_index) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 			suit_title = Language.Equip.XianQiSuit
	-- 		else
	-- 			suit_title = EquipmentWGData.Instance:GetEquipmenSuitName(item_cfg.order)
	-- 		end
	-- 		info_list.suit_title = string.format("%s [ %s %d/%d ]", suit_index_name, suit_title, data_param.suit_open_num, complete_suit_num)
	-- 		local suit_attr_list = {}
	-- 		for i, data in ipairs(list_data) do
	-- 			-- 计算评分
	-- 			local temp_list = {}
	-- 			local is_open = data.is_open
	-- 			local attr_color = is_open and COLOR3B.D_GREEN or COLOR3B.GRAY

	-- 			for k, v in ipairs(data.attr_list) do
	-- 				local temp = {}
	-- 				temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v.attr_type)
	-- 				temp.attr_value = ""
	-- 				if not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_type) then
	-- 					temp.attr_value = "+" .. v.value
	-- 					if is_open then
	-- 						local base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v.attr_type)
	-- 						self.comp_pingfen_num = self.comp_pingfen_num + base_num * v.value
	-- 					end
	-- 				else
	-- 					temp.attr_value = string.format("%.2f%%", v.value / 100)
	-- 					if is_open then
	-- 						self.comp_pingfen_num = self.comp_pingfen_num + TipWGData.Instance:GetXianPingSpecialAttrByOrder(v.attr_type, v.value)
	-- 					end
	-- 				end
	-- 				temp.attr_name = ToColorStr(temp.attr_name, attr_color)
	-- 				temp.attr_value = ToColorStr(temp.attr_value, attr_color)
	-- 				temp_list[k] = temp
	-- 			end
	-- 			local suit_name = string.format("%d%s", data.same_order_num, Language.Equip.SuitNumCompany)
	-- 			suit_attr_list[i] = {suit_name = ToColorStr(suit_name, attr_color), attr_list = temp_list}
	-- 		end
	-- 		info_list.suit_attr_list = suit_attr_list
	-- 	end
	-- end
	-- if not IsEmptyTable(info_list) then
	-- 	self.base_tips:SetXianqSuitAttribute(info_list)
	-- end
end

function TipsEquipComparePanel:SetBaseAttrInfo(list)
	local data = self.data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local data_param = data.param
	local strength_cfg = {}
	if data_param.strengthen_level and data_param.strengthen_level > 0 then
		strength_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_part, data_param.strengthen_level)
		strength_cfg = AttributeMgr.GetAttributteByClass(strength_cfg)
	elseif TianShenWGData.Equip_Pos[item_cfg.sub_type] and 1 <= data.grade_level then
		strength_cfg = TianShenWGData.Instance:GetEquipUpgradeAttr(item_cfg.sub_type, item_cfg.color, data.star_level, data.grade_level)
		strength_cfg = AttributeMgr.GetAttributteByClass(strength_cfg)
	end

	local attr_tab = {}
	for k, v in pairs(list) do
		if type(v) == "number" and v > 0 then
			local value_str = ""
			if k == "ming_zhong" or k == "shan_bi" then
				value_str = v / 100 .. "%"
			elseif k == "per_pofang" or k == "per_mianshang" or k == "per_baoji" then
				value_str = v * 100 .. "%"
			else
				value_str = math.floor(v)
			end
			local strength_str = ""
			if strength_cfg[k] and strength_cfg[k] > 0 then
				strength_str = string.format("（%s+%d）", Language.Tip.StrengthText, strength_cfg[k])
			end

			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k)
			temp.attr_value = value_str
			temp.add_str = strength_str
			attr_tab[k] = temp

			local base_num = 0
			if strength_cfg[k] and strength_cfg[k] > 0 then
				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
				self.comp_pingfen_num = self.comp_pingfen_num + base_num * strength_cfg[k]
			end

			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
			local p_num = v * base_num
			self.common_pingfen_num = self.common_pingfen_num + p_num
			self.comp_pingfen_num = self.comp_pingfen_num + p_num
		end
	end

	local attr_list = {}
	local attr_index = AttributeMgr.GetAttrList()
	for i,v in ipairs(attr_index) do
		if attr_tab[v] then
			table.insert(attr_list,attr_tab[v])
		end
	end
	if #attr_list > 0 then
		self.base_tips:SetBaseAttribute(attr_list)
	end
end

function TipsEquipComparePanel:ShowAttrStrengthen(item_cfg)
	local strange_attr = TianShenWGData.Instance:GetEquipStrangeAttr(item_cfg.sub_type, self.data.stren_level)
	if IsEmptyTable(strange_attr) then
		return
	end
	local attr_list = {}
	for k,v in pairs(strange_attr) do
		local attr_name = Language.Common.TipsAttrNameList[k]
		if attr_name and v > 0 then
			local temp = {}
			temp.attr_name = attr_name
			temp.attr_value = v
			attr_list[#attr_list + 1] = temp
		end
	end
	local strange_attr_butte = AttributeMgr.GetAttributteByClass(strange_attr)
	for k,v in pairs(strange_attr_butte) do
		if v > 0 then
			local base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
			self.comp_pingfen_num = self.comp_pingfen_num + v * base_num
		end
	end
	if #attr_list > 0 then
		local info_list = {}
		info_list.title_name = string.format(Language.TianShen.TianShenTips1, strange_attr.strength_level or 0)
		info_list.attr_list = attr_list
		self.base_tips:SetStrongAttribute(info_list)
	end
end

function TipsEquipComparePanel:SetAttrJiPing(item_cfg)
	local star_level = self.data.star_level
	local grade_level = self.data.grade_level
	local jiping_cfg = TianShenWGData.Instance:GetEquipUpgradeAcuraAttr(item_cfg.sub_type, item_cfg.color, star_level, grade_level)
	if not jiping_cfg then
		return
	end
	local attr_type = nil
	local attr_list = {}
	for i,v in ipairs(self.data.xianpin_list) do
		if v.attr_id > 0 then
			local temp = {}
			attr_type = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_id)
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(v.attr_id)
			local is_pre = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_type)
			if attr_type and is_pre then
				temp.attr_value = string.format("+%.2f%%", v.attr_value / 100)
			else
				temp.attr_value = "+" .. v.attr_value
			end
			temp.attr_value = ToColorStr(temp.attr_value, COLOR3B.D_PURPLE)
			if grade_level > 1 then
				local add_value = jiping_cfg[attr_type]
				if is_pre then
					add_value = add_value / 100 .. "%"
				end
				temp.attr_value = string.format("%s<color=%s>（%d%s+%s）</color>", temp.attr_value, COLOR3B.D_GREEN, grade_level, Language.Common.Jie, add_value)
			end
			attr_list[i] = temp
			-- 评分
			local num = TipWGData.Instance:GetXianPingSpecialAttrByOrder(v.attr_id, v.attr_value)
			self.comp_pingfen_num = self.comp_pingfen_num + num
			local num2 = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_type)
			if num2 and jiping_cfg[attr_type] then
				self.comp_pingfen_num = self.comp_pingfen_num + num2 * jiping_cfg[attr_type]
			end
		end
	end
	if #attr_list > 0 then
		local info_list = {}
		info_list.title_name = Language.F2Tip.JiPinShuXing
		info_list.attr_list = attr_list
		self.base_tips:SetShengPinAttribute(info_list)
	end
end

function TipsEquipComparePanel:SetEquipSpecialAttr(item_cfg, attr_type)
	local equip_id = self.data.item_id
	local n_cfg = EquipmentWGData.Instance:GetEquipSpecialAttrCfgByIdAndType(equip_id, attr_type)
	if not n_cfg or not item_cfg then
		return
	end

	local color_list = {COLOR3B.D_PINK, COLOR3B.D_GLOD, COLOR3B.D_DAZZLING}
	local attr_list = {}
	for i = 1, 10 do
		local attr_id = n_cfg["special_type" .. i]
		local attr_value = n_cfg["special_val" .. i]
		if attr_id and attr_value and attr_value > 0 then
			local temp = {}
			local pingfen = TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_id, attr_value)
			self.common_pingfen_num = self.common_pingfen_num + pingfen
			self.comp_pingfen_num = self.comp_pingfen_num + pingfen

			local color = color_list[attr_type] or color_list[1]-- ITEM_COLOR[item_cfg.color]
			local name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_id, true)
			temp.attr_name = ToColorStr(name, color)
			local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
			attr_value = is_per and (attr_value * 0.01 .. "%") or attr_value
			temp.attr_value = ToColorStr(attr_value, color)
			attr_list[#attr_list + 1] = temp
		end
	end

	if #attr_list > 0 then
		local info_list = {}
		if attr_type == EQUIP_SPECIAL_ATTR_TYPE.PINK then
			info_list.title_name = Language.Tip.PinkSuitAttr
			info_list.attr_list = attr_list
			self.base_tips:SetPinkAttribute(info_list)

		elseif attr_type == EQUIP_SPECIAL_ATTR_TYPE.GOLD then
			info_list.title_name = Language.Tip.GodSuitAttr
			info_list.attr_list = attr_list
			self.base_tips:SetGodAttribute(info_list)

		elseif attr_type == EQUIP_SPECIAL_ATTR_TYPE.COLOR then
			info_list.title_name = Language.Tip.MoreColorAttr
			info_list.attr_list = attr_list
			self.base_tips:SetMoreColorAttribute(info_list)
		end
	end
end

function TipsEquipComparePanel:SetXianMengCangKuAttr(data)
	self.base_tips:SetXianMengCangKuPanel(data)
end

function TipsEquipComparePanel:ShowEquipTipsCompose()
	local data = self.data
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg == nil or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return
	end

	local show_tips, process_list, show_add = ItemShowWGData.Instance:GetEquipComposeProcessData(data)
	if not show_tips then
		return
	end
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local order_str = item_cfg.order--CommonDataManager.GetDaXie(item_cfg.order)
	local part_str = Language.Stone[equip_part] or ""
	local str = string.format(Language.F2Tip.TipsComposeTitle, order_str, part_str)

	local info = {title_desc = str, equip_list = process_list,
				bottom_desc = Language.F2Tip.ComposeBottomDesc1,
				show_add = show_add}
	self.base_tips:SetEquipTipsComposePanel(info)
end

function TipsEquipComparePanel:SetEquipSkillShow()
	local skill_cfg = EquipmentWGData.Instance:GetEquipKillCfgByItemId(self.data.item_id)
	if IsEmptyTable(skill_cfg) then
		return
	end

	local next_need_order = 0
	local next_skill_desc = ""
	local next_desc_tilte = ""
	local cur_desc_title = Language.F2Tip.SkillEffectTitle
	local cur_skill_desc = skill_cfg.desc

	if self.data.frombody then
		local max_order_equi_id = EquipWGData.Instance:GetMaxOrderPartEquip(self.data.item_id)

		if max_order_equi_id == self.data.item_id then
			cur_desc_title = Language.F2Tip.CurSkillEffectTitle
			cur_skill_desc = ToColorStr(cur_skill_desc, COLOR3B.GREEN)

			local next_skill_cfg = EquipmentWGData.Instance:GetEquipKillCfgByItemId(skill_cfg.next_equip_id)
			if next_skill_cfg then
				next_skill_desc = next_skill_cfg.desc
				local next_item_cfg = ItemWGData.Instance:GetItemConfig(skill_cfg.next_equip_id)
				if next_item_cfg then
					next_need_order = next_item_cfg.order
					next_desc_tilte = string.format(Language.F2Tip.EquipSkillTitleDesc, next_need_order, Language.Common.ColorName4[next_item_cfg.color])
				end
			end
		end
	end

	local info = {skill_name = skill_cfg.name,
					skill_icon_id = skill_cfg.icon,
					cur_skill_desc = cur_skill_desc,
					cur_desc_title = cur_desc_title,
					have_next = next_need_order > 0,
					next_desc_tilte = next_desc_tilte,
					next_skill_desc = next_skill_desc,
					show_special_kuang = false}

	self.base_tips:SetEquipSkill(info)
end

function TipsEquipComparePanel:ParseAttribute(attribute, item_cfg)
	local base_attr_cfg = item_cfg
	-- 天神基础属性在天神表里取
	if item_cfg and TianShenWGData.Equip_Pos[item_cfg.sub_type] then
		base_attr_cfg = TianShenWGData.Instance:GetEquipBasicsAttr(item_cfg.id, self.data.star_level)
	end

	local base_attri_butte = AttributeMgr.GetAttributteByClass(base_attr_cfg) --根据职业获取属性, true, item_cfg.limit_prof
	attribute[ATTR_BASE] = base_attri_butte

	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		attribute[ATTR_STRENGTHEN] = base_attri_butte
		attribute[ATTR_JIPING] = base_attri_butte
	else
		attribute[ATTR_STONE] = base_attri_butte
		attribute[ATTR_STONE_JL] = base_attri_butte
		attribute[ATTR_LEGEND] = base_attri_butte
		attribute[ATTR_SUIT] = base_attri_butte

		attribute[ATTR_SHENGPIN] = base_attri_butte
		attribute[ATTR_PINK_SUIT] = base_attri_butte
		attribute[ATTR_GOD_SUIT] = base_attri_butte
		attribute[ATTR_MORE_COLOR] = base_attri_butte
		attribute[REMIND_HOW_COMPOSE] = base_attri_butte
		attribute[ATTR_EQUIP_SKILL] = base_attri_butte
		attribute[ATTR_LINGYUE] = base_attri_butte
		attribute[ATTP_YULING] = base_attri_butte
		attribute[ATTP_ZHUSHEN] = base_attri_butte
		attribute[ATTP_XILIAN] = base_attri_butte
	end

	if self.from_view == ItemTip.FROM_STORGE_ON_GUILD_STORGE then
		attribute[ATTR_XIANMENG_CANGKU] = base_attri_butte
	end

end

function TipsEquipComparePanel:InitDataParam(data, item_cfg, equip_part)
	if TianShenWGData.Instance:IsTianShenEquip(item_cfg.sub_type) then
		return
	end

	if data.frombody then
		local param = data.param

		if self.from_view == ItemTip.FROME_BROWSE_ROLE or self.from_view == ItemTip.FORM_ROBOT_SHOW_INFO then
			param.suit_open_num = BrowseWGData.Instance:GetEquipmenSuitOpenNum(data)
			param.self_suit_open = BrowseWGData.Instance:GetEquipmenSuitOpenFlag(equip_part) > 0
		else
			if self.is_mine then
				local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
				local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
				local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
				param.strengthen_level = EquipmentWGData.Instance:GetStrengthLevelByIndex(equip_body_index) --强度等级？
				-- param.suit_index = EquipmentWGData.Instance:GetSuitInfoByPartType(equip_part) --套装部位
				-- local suit_data = {
				-- 	item_cfg = item_cfg,
				-- 	suit_index = param.suit_index,
				-- }
				param.suit_open_num = EquipmentWGData.Instance:GetSuitActiveSameEquipNum(equip_body_seq, equip_type)--获取套装开启数量
				param.baoshi_t = EquipmentWGData.Instance:GetStoneInfoListByIndex(equip_body_index) or {} --根据装备部位获取宝石数据
				param.lingyu_t = EquipmentLingYuWGData.Instance:GetLingYuInfoListByIndex(equip_body_index) or {} --根据装备部位获取灵玉数据
				-- param.baptize_list = EquipmentWGData.Instance:GetEquipBaptizeAttrAdditionList(equip_part) --获取精炼属性
				local refine_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(equip_body_index) --宝石完善程度
				param.stone_baptize_level = refine_level
			end
		end
	end
end

function TipsEquipComparePanel:SetAttrXilian(item_cfg, equip_body_index)
	local attr_list = {}

	if self.data.frombody then
		local slot_info = EquipmentWGData.Instance:GetBaptizeSlotInfoByEquipBodyIndex(equip_body_index)

		if not IsEmptyTable(slot_info) then
			for k, v in pairs(slot_info) do
				if v.is_buy  == 1 then
					local attr_name = EquipmentWGData.Instance:GetAttrName(v.word_type)
					local is_per = EquipmentWGData.Instance:GetAttrIsPer(v.word_type)
					local per_desc = is_per and "%" or ""
					local attr_value = is_per and v.attr_value * 0.01 .. "%" or v.attr_value
					-- local min_value = is_per and v.min_value * 0.01 .. "%" or v.min_value
					-- local max_value = is_per and v.max_value * 0.01 .. "%" or v.max_value

					table.insert(attr_list, {attr_name = attr_name, attr_value = attr_value})
				end
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		self.base_tips:SetXilianAttribute(attr_list)
	end
end
