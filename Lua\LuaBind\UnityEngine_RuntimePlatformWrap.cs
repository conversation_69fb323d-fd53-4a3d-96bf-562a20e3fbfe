﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_RuntimePlatformWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(UnityEngine.RuntimePlatform));
		<PERSON><PERSON>("OSXEditor", get_OSXEditor, null);
		<PERSON><PERSON>("OSXPlayer", get_OSXPlayer, null);
		<PERSON><PERSON>("WindowsPlayer", get_WindowsPlayer, null);
		<PERSON><PERSON>("WindowsEditor", get_WindowsEditor, null);
		<PERSON><PERSON>("IPhonePlayer", get_IPhonePlayer, null);
		<PERSON><PERSON>("Android", get_Android, null);
		<PERSON><PERSON>("LinuxPlayer", get_LinuxPlayer, null);
		<PERSON><PERSON>("LinuxEditor", get_LinuxEditor, null);
		<PERSON><PERSON>("WebGLPlayer", get_WebGLPlayer, null);
		<PERSON><PERSON>("WSAPlayerX86", get_WSAPlayerX86, null);
		<PERSON><PERSON>("WSAPlayerX64", get_WSAPlayerX64, null);
		<PERSON><PERSON>("WSAPlayerARM", get_WSAPlayerARM, null);
		<PERSON><PERSON>("PS4", get_PS4, null);
		<PERSON><PERSON>("XboxOne", get_XboxOne, null);
		L.RegVar("tvOS", get_tvOS, null);
		L.RegVar("Switch", get_Switch, null);
		L.RegVar("Lumin", get_Lumin, null);
		L.RegVar("Stadia", get_Stadia, null);
		L.RegVar("CloudRendering", get_CloudRendering, null);
		L.RegVar("GameCoreXboxSeries", get_GameCoreXboxSeries, null);
		L.RegVar("GameCoreXboxOne", get_GameCoreXboxOne, null);
		L.RegVar("PS5", get_PS5, null);
		L.RegVar("EmbeddedLinuxArm64", get_EmbeddedLinuxArm64, null);
		L.RegVar("EmbeddedLinuxArm32", get_EmbeddedLinuxArm32, null);
		L.RegVar("EmbeddedLinuxX64", get_EmbeddedLinuxX64, null);
		L.RegVar("EmbeddedLinuxX86", get_EmbeddedLinuxX86, null);
		L.RegVar("LinuxServer", get_LinuxServer, null);
		L.RegVar("WindowsServer", get_WindowsServer, null);
		L.RegVar("OSXServer", get_OSXServer, null);
		L.RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.RuntimePlatform>.Check = CheckType;
		StackTraits<UnityEngine.RuntimePlatform>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.RuntimePlatform arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.RuntimePlatform), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OSXEditor(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.OSXEditor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OSXPlayer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.OSXPlayer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WindowsPlayer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WindowsPlayer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WindowsEditor(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WindowsEditor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IPhonePlayer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.IPhonePlayer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Android(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.Android);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LinuxPlayer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.LinuxPlayer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LinuxEditor(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.LinuxEditor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WebGLPlayer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WebGLPlayer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WSAPlayerX86(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WSAPlayerX86);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WSAPlayerX64(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WSAPlayerX64);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WSAPlayerARM(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WSAPlayerARM);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PS4(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.PS4);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_XboxOne(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.XboxOne);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_tvOS(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.tvOS);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Switch(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.Switch);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Lumin(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.Lumin);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Stadia(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.Stadia);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CloudRendering(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.CloudRendering);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_GameCoreXboxSeries(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.GameCoreXboxSeries);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_GameCoreXboxOne(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.GameCoreXboxOne);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PS5(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.PS5);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EmbeddedLinuxArm64(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.EmbeddedLinuxArm64);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EmbeddedLinuxArm32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.EmbeddedLinuxArm32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EmbeddedLinuxX64(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.EmbeddedLinuxX64);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EmbeddedLinuxX86(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.EmbeddedLinuxX86);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LinuxServer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.LinuxServer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WindowsServer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.WindowsServer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OSXServer(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.RuntimePlatform.OSXServer);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.RuntimePlatform o = (UnityEngine.RuntimePlatform)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

