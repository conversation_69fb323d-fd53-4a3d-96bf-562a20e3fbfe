BaoXiangGetRewardView = BaoXiangGetRewardView or BaseClass(SafeBaseView)

function BaoXiangGetRewardView:__init()
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangGetRewardView:LoadConfig()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_get_reward_view")
end

function BaoXiangGetRewardView:__delete()

end

function BaoXiangGetRewardView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BaoXiangGetRewardView:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
	XUI.AddClickEventListener(self.node_list["ok_btn"], BindTool.Bind(self.ClickOKBtn,self))
end

function BaoXiangGetRewardView:ShowIndexCallBack()

end

function BaoXiangGetRewardView:OnFlush()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local quality = info.quality
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	if IsEmptyTable(treasure_cfg) then return end
	local reward_item = treasure_cfg.reward_item
	local reward_data = {}
	for i=0,#reward_item do
		table.insert(reward_data,reward_item[i])
	end
	table.sort( reward_data,function (a,b)
		if a and b then
			local item_a = ItemWGData.Instance:GetItemConfig(a.item_id)
			local item_b = ItemWGData.Instance:GetItemConfig(b.item_id)
			return item_a.color > item_b.color
		end
	end )
	self.reward_list:SetDataList(reward_data)
end

function BaoXiangGetRewardView:ClickOKBtn()
	self:Close()
end