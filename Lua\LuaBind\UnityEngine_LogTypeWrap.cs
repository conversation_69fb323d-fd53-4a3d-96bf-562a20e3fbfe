﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_LogTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>num(typeof(UnityEngine.LogType));
		<PERSON><PERSON>("Error", get_Error, null);
		<PERSON><PERSON>("Assert", get_Assert, null);
		<PERSON><PERSON>("Warning", get_Warning, null);
		<PERSON><PERSON>("Log", get_Log, null);
		<PERSON><PERSON>("Exception", get_Exception, null);
		<PERSON><PERSON>unction("IntToEnum", IntToEnum);
		<PERSON>.EndEnum();
		TypeTraits<UnityEngine.LogType>.Check = CheckType;
		StackTraits<UnityEngine.LogType>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.LogType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.LogType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Error(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LogType.Error);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Assert(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LogType.Assert);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Warning(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LogType.Warning);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Log(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LogType.Log);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Exception(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LogType.Exception);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.LogType o = (UnityEngine.LogType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

