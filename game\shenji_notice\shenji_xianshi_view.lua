-- time : 2020/7/1
ShenJiXianShiView = ShenJiXianShiView or BaseClass(SafeBaseView)

function ShenJiXianShiView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/shenji_notice_prefab", "shenji_notice_shenjixianshi")
    self.reward_count = 5
    self.reward_list = {}
end

function ShenJiXianShiView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("ShenJiXianShiView_CountDown") then
        CountDownManager.Instance:RemoveCountDown("ShenJiXianShiView_CountDown")
    end

    if self.shenji_task_event then
        GlobalEventSystem:UnBind(self.shenji_task_event)
        self.shenji_task_event = nil
    end

    --for i, v in pairs(self.role_model_list) do
    --    v:DeleteMe()
    --end
    --self.role_model_list = {}

    if self.weapon_model then
        self.weapon_model:DeleteMe()
        self.weapon_model = nil
    end

    if self.weapon_model1 then
        self.weapon_model1:DeleteMe()
        self.weapon_model1 = nil
    end

    for i, v in pairs(self.reward_list) do
        v:DeleteMe()
    end
    self.reward_list = {}

    if self.xianshi_bg_tweener then
        self.xianshi_bg_tweener:Kill()
        self.xianshi_bg_tweener = nil
    end
    if self.title_tweener then
        self.title_tweener:Kill()
        self.title_tweener = nil
    end
    if self.img_title_left_tweener then
        self.img_title_left_tweener:Kill()
        self.img_title_left_tweener = nil
    end
    if self.img_title_right_tweener then
        self.img_title_right_tweener:Kill()
        self.img_title_right_tweener = nil
    end
    if self.model_2_self_tweener then
        self.model_2_self_tweener:Kill()
        self.model_2_self_tweener = nil
    end
    if self.model_2_self_tweener2 then
        self.model_2_self_tweener2:Kill()
        self.model_2_self_tweener2 = nil
    end

    if self.model_1_self_tweener then
        self.model_1_self_tweener:Kill()
        self.model_1_self_tweener = nil
    end

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
end

function ShenJiXianShiView:LoadCallBack()
    for i = 1, self.reward_count do
        self.reward_list[i] = ShenJiXianShiItemRender.New(self.node_list["reward_" .. i])
    end

    self.weapon_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["RoleDisplay_2"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.S,
        can_drag = true,
    }
    
    self.weapon_model:SetRenderTexUI3DModel(display_data)
    -- self.weapon_model:SetUI3DModel(
    --     self.node_list["RoleDisplay_2"].transform,
    --     self.node_list["ph_display_left_2"].event_trigger_listener,
    --     0,
    --     false,
    --     MODEL_CAMERA_TYPE.BASE
    -- )
    -- self:AddUiRoleModel(self.weapon_model)

    self.weapon_model1 = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["RoleDisplay_1"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.S,
        can_drag = true,
    }
    
    self.weapon_model1:SetRenderTexUI3DModel(display_data)
    -- self.weapon_model1:SetUI3DModel(
    --     self.node_list["RoleDisplay_1"].transform,
    --     self.node_list["ph_display_left_1"].event_trigger_listener,
    --     0,
    --     false,
    --     MODEL_CAMERA_TYPE.BASE
    -- )

    self.shenji_task_event = GlobalEventSystem:Bind(ShenJiEventType.ShenJiNotice_TaskInfoChange,
        BindTool.Bind1(self.OnTaskInfoChange, self))

    local tween_info = UITween_CONSTS.ShenJiNotice.XianShi
    --self.sequence = DG.Tweening.DOTween.Sequence()
    --self.sequence:AppendCallback(function()
    --    self.model_2_self_tweener = self.node_list["model_2_self_tween"].transform:DOLocalRotate(Vector3(0, tween_info.ModelSelf_RotateLeftAngle, 0), tween_info.ModelSelf_RotateTime):SetEase(DG.Tweening.Ease.Linear)
    --end)
    --self.sequence:AppendInterval(tween_info.ModelSelf_FloatTime)
    --self.sequence:AppendCallback(function()
    --    self.model_2_self_tweener2 = self.node_list["model_2_self_tween"].transform:DOLocalRotate(Vector3(0, tween_info.ModelSelf_RotateRightAngle, 0), tween_info.ModelSelf_RotateTime):SetEase(DG.Tweening.Ease.Linear)
    --end)
    --self.sequence:SetLoops(-1, DG.Tweening.LoopType.Yoyo)

    self.node_list["model_2_self_tween"].transform.localRotation = Quaternion.Euler(0,
        tween_info.ModelSelf_RotateLeftAngle, 0)
    self.model_2_self_tweener2 = self.node_list["model_2_self_tween"].transform
        :DOLocalRotate(Vector3(0, tween_info.ModelSelf_RotateRightAngle, 0), tween_info.ModelSelf_RotateTime)
        :SetEase(DG.Tweening.Ease.Linear)
        :SetLoops(-1, DG.Tweening.LoopType.Yoyo)

    local tween_info = UITween_CONSTS.ShenJiNotice.XianShi
    self.model_1_self_tweener = self.node_list["model_1_self_tween"].transform:DOAnchorPosY(
    tween_info.ModelSelf_FloatDistance, tween_info.ModelSelf_FloatTime):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
end

function ShenJiXianShiView:ShowIndexCallBack()
    local tween_info = UITween_CONSTS.ShenJiNotice.XianShi
    --底板
    if self.xianshi_bg_tweener then
        self.xianshi_bg_tweener:Kill()
        self.xianshi_bg_tweener = nil
    end
    self.node_list.xianshi_bg_panel.transform.localScale = Vector3(tween_info.Bg_StartScale, tween_info.Bg_StartScale,
        tween_info.Bg_StartScale)
    self.xianshi_bg_tweener = self.node_list.xianshi_bg_panel.transform:DOScale(tween_info.Bg_EndScale,
        tween_info.Bg_ScaleTime):SetEase(DG.Tweening.Ease.InQuad)

    --标题
    if self.title_tweener then
        self.title_tweener:Kill()
        self.title_tweener = nil
    end
    self.node_list.shenji_title.transform.localScale = Vector3(tween_info.Title_StartScale, tween_info.Title_StartScale,
        tween_info.Title_StartScale)
    ReDelayCall(self, function()
        self.title_tweener = self.node_list.shenji_title.transform:DOScale(tween_info.Title_EndScale,
            tween_info.Title_ScaleTime):SetEase(DG.Tweening.Ease.InQuad)
    end, tween_info.Title_DelayTime, "Title_DelayTime")

    --云
    --    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView,self.node_list["decorate_1"].gameObject)
    --    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView,self.node_list["decorate_1"].gameObject)
    --    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView,self.node_list["decorate_1"].gameObject)
    --    self.node_list["decorate_1"].gameObject:SetActive(false)
    --    self.node_list["decorate_2"].gameObject:SetActive(false)
    --    self.node_list["decorate_3"].gameObject:SetActive(false)
    --    ReDelayCall(self, function()
    --        self.node_list["decorate_1"].gameObject:SetActive(true)
    --        self.node_list["decorate_2"].gameObject:SetActive(true)
    --        self.node_list["decorate_3"].gameObject:SetActive(true)
    --        UITween.MoveAlphaShowPanelByEndPos(GuideModuleName.ShenJiXianShiView,self.node_list["decorate_1"].gameObject, Vector2(-130.65, -150), Vector2(-130.65, -95.35), tween_info.Cloud_MoveTime)
    --        UITween.MoveAlphaShowPanelByEndPos(GuideModuleName.ShenJiXianShiView,self.node_list["decorate_2"].gameObject, Vector2(586, -81.75), Vector2(469.65, -81.75), tween_info.Cloud_MoveTime)
    --        UITween.MoveAlphaShowPanelByEndPos(GuideModuleName.ShenJiXianShiView,self.node_list["decorate_3"].gameObject, Vector2(-536.3, 107.25), Vector2(-435.15, 107.25), tween_info.Cloud_MoveTime)
    -- end, tween_info.Cloud_DelayTime, "Cloud_DelayTime")

    --宣传语
    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView, self.node_list["xuanchuanyu"].gameObject)
    UITween.CanvasGroup(self.node_list["xuanchuanyu"].gameObject).alpha = 0
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.ShenJiXianShiView, self.node_list["xuanchuanyu"].gameObject,
            tween_info.XuanChuanYu_StartAlpha, tween_info.XuanChuanYu_EndAlpha, tween_info.XuanChuanYu_AlphaTime,
            DG.Tweening.Ease.OutCirc)
        --宣传语 Scale字
        self.node_list["img_title_left"].transform.localScale = Vector3(tween_info.XuanChuanYu_StartScale,
            tween_info.XuanChuanYu_StartScale, tween_info.XuanChuanYu_StartScale)
        self.node_list["img_title_right"].transform.localScale = Vector3(tween_info.XuanChuanYu_StartScale,
            tween_info.XuanChuanYu_StartScale, tween_info.XuanChuanYu_StartScale)
    end, tween_info.XuanChuanYu_AlphaDelayTime, "XuanChuanYu_AlphaDelayTime")

    if self.img_title_left_tweener then
        self.img_title_left_tweener:Kill()
        self.img_title_left_tweener = nil
    end
    if self.img_title_right_tweener then
        self.img_title_right_tweener:Kill()
        self.img_title_right_tweener = nil
    end
    ReDelayCall(self, function()
        self.img_title_left_tweener = self.node_list["img_title_left"].transform:DOScale(tween_info.XuanChuanYu_EndScale,
            tween_info.XuanChuanYu_ScaleTime)
        self.img_title_right_tweener = self.node_list["img_title_right"].transform:DOScale(
        tween_info.XuanChuanYu_EndScale, tween_info.XuanChuanYu_ScaleTime)
    end, tween_info.XuanChuanYu_ZhenHanDelayTime, "XuanChuanYu_ZhenHanDelayTime")

    --右侧模型
    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView, self.node_list["model_2"].gameObject)
    UITween.MoveAlphaShowPanelByEndPos(GuideModuleName.ShenJiXianShiView, self.node_list["model_2"].gameObject,
        Vector2(-120.7, 316), Vector2(-120.7, 110), tween_info.Model_MoveTime)

    --左侧模型
    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView, self.node_list["model_1"].gameObject)
    UITween.MoveAlphaShowPanelByEndPos(GuideModuleName.ShenJiXianShiView, self.node_list["model_1"].gameObject,
        Vector2(-59.28, -138), Vector2(-247, -182), tween_info.Model_MoveTime)

    --其他信息淡入
    UITween.KillMoveAlphaTween(GuideModuleName.ShenJiXianShiView, self.node_list["alpha_root"].gameObject)
    UITween.CanvasGroup(self.node_list["alpha_root"].gameObject).alpha = 0
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.ShenJiXianShiView, self.node_list["alpha_root"].gameObject,
            tween_info.OtherInfo_StartAlpha, tween_info.OtherInfo_EndAlpha, tween_info.OtherInfo_AlphaTime,
            DG.Tweening.Ease.OutCirc)
    end, tween_info.OtherInfo_DelayTime, "OtherInfo_DelayTime")
end

function ShenJiXianShiView:OpenCallBack()
    -- ShenJiNoticeWGCtrl.Instance:OpenXianShiWeaponView()
end

function ShenJiXianShiView:CloseCallBack()
    ShenJiNoticeWGCtrl.Instance:CloseXianShiWeaponView()
end

function ShenJiXianShiView:OnFlush()
    local data_list = ShenJiNoticeWGData.Instance:GetJiangShiDataList()
    for i = 1, self.reward_count do
        self.reward_list[i]:SetData(data_list[i])
    end

    --模型
    -- local resource_cfg = ShenJiNoticeWGData.Instance:GetXianShiBtnResourceCfg()
    local resource_cfg = XianQiTeDianWGData.Instance:GetLingJiaBtnShowCfg(-1)
    local model_str_table = Split(resource_cfg.model, "#")
    local bundle, asset
    bundle, asset = ResPath.GetShenJiModel(model_str_table[2])
    self.weapon_model:SetMainAsset(bundle, asset)


    --模型
    -- local resource_cfg = ShenJiNoticeWGData.Instance:GetXianShiBtnResourceCfg()
    local resource_cfg = XianQiTeDianWGData.Instance:GetLingJiaBtnShowCfg(-1)
    local model_str_table = Split(resource_cfg.model, "#")
    local bundle, asset
    bundle, asset = ResPath.GetShenJiModel(model_str_table[1])
    self.weapon_model1:SetMainAsset(bundle, asset)

    --倒计时
    -- local open_time = ShenJiNoticeWGData.Instance:GetNowSpecialSaleStartTime()
    local open_time = ShenJiNoticeWGData.Instance:GetFunOpenStartTime()
    if CountDownManager.Instance:HasCountDown("ShenJiXianShiView_CountDown") then
        CountDownManager.Instance:RemoveCountDown("ShenJiXianShiView_CountDown")
    end

    local time = open_time - TimeWGCtrl.Instance:GetServerTime()

    if time > 0 then
        self.node_list.bg_number:SetActive(true)
        self.node_list.img_title_left_container.rect.anchoredPosition = Vector2(-135.97, 4.5)

        self:CommonUpdateTime(0, time)
        CountDownManager.Instance:AddCountDown("ShenJiXianShiView_CountDown",
            BindTool.Bind(self.CommonUpdateTime, self),
            BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
    else
        self.node_list.bg_number:SetActive(false)
        self.node_list.img_title_left_container.rect.anchoredPosition = Vector2(-50, 4.5)
    end
end

function ShenJiXianShiView:ClacTimeShow(time)
    if time > 0 then
        local time_tab = TimeUtil.Format2TableDHM2(time)
        if time_tab.day >= 1 then
            return string.format(Language.Common.ShowTime2, time_tab.day)
        elseif time_tab.day < 1 and time_tab.hour >= 1 then
            return string.format(Language.ShenJiNotice.EndTimeHour, time_tab.hour)
        elseif time_tab.hour < 1 then
            return string.format("%02d:%02d", time_tab.min, time_tab.sec)
        end
    else
        return string.format("%02d:%02d:%02d", 0, 0, 0)
    end
end

function ShenJiXianShiView:CommonUpdateTime(elapse_time, total_time)
    local temp_seconds = GameMath.Round(total_time - elapse_time)
    local str = self:ClacTimeShow(temp_seconds)
    if self.node_list and self.node_list.Text_time_number then
        self.node_list.Text_time_number.text.text = str
    end
end

function ShenJiXianShiView:CommonCompleteTime(elapse_time, total_time)
    if self.node_list and self.node_list.Text_time_number then
        self.node_list.Text_time_number.text.text = ""
    end
end

function ShenJiXianShiView:OnTaskInfoChange()
    self:Flush()
end

ShenJiXianShiItemRender = ShenJiXianShiItemRender or BaseClass(BaseRender)
function ShenJiXianShiItemRender:LoadCallBack()
    self.item_list = AsyncListView.New(ShenJiXianShiRewardItem, self.node_list.items_list)

    XUI.AddClickEventListener(self.node_list.btn_fetch, BindTool.Bind1(self.OnClickFetch, self))
end

function ShenJiXianShiItemRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function ShenJiXianShiItemRender:OnFlush()
    if not self.data then
        return
    end
    local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

    --物品列表
    self.item_list:SetDataList(self.data.item_list)

    --等级
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local color = role_level >= self.data.need_role_level and COLOR3B.D_GREEN or COLOR3B.D_RED
    local isv, level = RoleWGData.Instance:GetDianFengLevel(role_level)
    self.node_list.dianfeng_icon:SetActive(isv)
    self.node_list.text_level.text.text = string.format(Language.ShenJiNotice.LevelCondition, color, level,
        self.data.need_role_level)


    --region 按钮状态相关
    local task = ShenJiNoticeWGData.Instance:GetTaskById(self.data.task_id)
    if not task then
        return
    end

    local has_fetch = task.task_status == SHEN_JI_YU_GAO_TASK_STATUS.HAS_GET_REWARD
    local level_condition = role_level >= self.data.need_role_level
    local can_fetch = task.task_status == SHEN_JI_YU_GAO_TASK_STATUS.COMPLETE and
    open_server_day >= self.data.need_server_open_day and level_condition

    --按钮文本
    local btn_text = ""

    if not has_fetch then
        if open_server_day >= self.data.need_server_open_day then
            btn_text = Language.ShenJiNotice.CurCanFetch
        else
            local lerp_day = self.data.need_server_open_day - open_server_day
            if lerp_day == 1 then
                btn_text = Language.ShenJiNotice.TomorrowCanFetch
            elseif lerp_day == 2 then
                btn_text = Language.ShenJiNotice.TheDayAfterTomorrowCanFetch
            elseif lerp_day > 2 then
                btn_text = string.format(Language.ShenJiNotice.XDayCanFetch, lerp_day)
            else
                --print_error("当前开服天数：",open_server_day, "需要开服天数：", self.data.need_server_open_day)
            end
        end
        if not level_condition then
            btn_text = Language.Common.HaveNotEnterLevel
        end
    else
        btn_text = Language.ShenJiNotice.HasFetch
    end

    self.node_list.btn_yiling:SetActive(has_fetch)
    self.node_list.btn_fetch:SetActive(not has_fetch)
    if not has_fetch then
        self.node_list.btn_fetch_text.text.text = btn_text
        --按钮置灰状态
        local btn_gray_state = not level_condition

        XUI.SetGraphicGrey(self.node_list.btn_fetch, btn_gray_state)
    end

    self.node_list.red_point:SetActive(can_fetch)
    --endregion
end

function ShenJiXianShiItemRender:OnClickFetch()
    local task = ShenJiNoticeWGData.Instance:GetTaskById(self.data.task_id)
    local has_fetch = task.task_status == SHEN_JI_YU_GAO_TASK_STATUS.HAS_GET_REWARD
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local level_condition = role_level >= self.data.need_role_level

    if not level_condition then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiNotice.FetchTip0)
        return
    end

    if has_fetch then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiNotice.FetchTip1)
        return
    end

    local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if open_server_day >= self.data.need_server_open_day then
        -- 发送协议
        ShenJiNoticeWGCtrl.Instance:RequestFetchTaskReward(self.data.task_id)
    else
        local lerp_day = self.data.need_server_open_day - open_server_day
        if lerp_day == 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiNotice.FetchTip2)
        elseif lerp_day == 2 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiNotice.FetchTip3)
        elseif lerp_day > 2 then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenJiNotice.FetchTip4, lerp_day))
        end
    end
end

ShenJiXianShiRewardItem = ShenJiXianShiRewardItem or BaseClass(BaseRender)
function ShenJiXianShiRewardItem:LoadCallBack()
    self.item = ShenJiXianShiRewardItemCell.New(self.node_list.pos)
end

function ShenJiXianShiRewardItem:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function ShenJiXianShiRewardItem:OnFlush()
    if not self.data then
        return
    end

    self.item:SetData(self.data)
end

ShenJiXianShiRewardItemCell = ShenJiXianShiRewardItemCell or BaseClass(ItemCell)
function ShenJiXianShiRewardItemCell:OnClick()
    if not self.data then
        return
    end

    local equip_cfg = HiddenWeaponWGData.Instance:GetEquipCfgById(self.data.item_id)
    if equip_cfg then
        local data = ShenJiNoticeWGData.Instance:GetVirtualItemInfo(self.data.item_id)
        --虚拟神机装备
        TipWGCtrl.Instance:OpenItem(data)
    else
        ItemCell.OnClick(self)
    end
end
