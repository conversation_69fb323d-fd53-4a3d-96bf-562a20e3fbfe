HolyHeavenlyDomainShopView = HolyHeavenlyDomainShopView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainShopView:__init()
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_shop")
end

function HolyHeavenlyDomainShopView:LoadCallBack()
    if not self.shop_list then
		self.shop_list = AsyncBaseGrid.New()
		local bundle = "uis/view/holy_heavenly_domain_ui_prefab"
		local asset = "hhd_shop_list_cell"
		self.shop_list:CreateCells({col = 5, change_cells_num = 1, list_view = self.node_list.shop_list,
			assetBundle = bundle, assetName = asset, itemRender = HHDShopItemRender})
		self.shop_list:SetStartZeroIndex(false)
	end

	XUI.AddClickEventListener(self.node_list.lucky_item_icon, BindTool.Bind(self.OnClickLuckyItem, self))
end

function HolyHeavenlyDomainShopView:ShowIndexCallBack()
end

function HolyHeavenlyDomainShopView:ReleaseCallBack()
    if self.shop_list then
        self.shop_list:DeleteMe()
        self.shop_list = nil
    end
end

function HolyHeavenlyDomainShopView:OnFlush()
	local data_list = self:SortShopItemData()
    self.node_list.not_shop_data:CustomSetActive(IsEmptyTable(data_list))
	self.shop_list:SetDataList(data_list)

    local convert_cost_item = HolyHeavenlyDomainWGData.Instance:GetConvertShowItemId()
	if nil ~= convert_cost_item then
		local bundel, asset = ResPath.GetItem(convert_cost_item)

		self.node_list.lucky_item_icon.image:LoadSprite(bundel, asset, function()
			self.node_list.lucky_item_icon.image:SetNativeSize()
		end)
		self.node_list.lucky_item_num.text.text = ItemWGData.Instance:GetItemNumInBagById(convert_cost_item)
	end
end

function HolyHeavenlyDomainShopView:SortShopItemData()
	local data_list = {}
    local cfg = HolyHeavenlyDomainWGData.Instance:GetAllConvertCfg()

	if not IsEmptyTable(cfg) then
		for k, v in pairs(cfg) do
			local data = {}
			data.cfg = v
			local item_count = ItemWGData.Instance:GetItemNumInBagById(v.stuff_id_1)
			local exchange_time = HolyHeavenlyDomainWGData.Instance:GetConvertTimeBySeq(v.seq)
			local is_times_limit = v.times_limit ~= 0
			local has_enough_stuff = item_count >= v.stuff_num_1
			local time_enough = not is_times_limit or (is_times_limit and (exchange_time < v.times_limit))
			local can_exchange = has_enough_stuff and time_enough
			local sort_index = can_exchange and 1000 or (time_enough and 500 or 1)
			data.sort_index = sort_index
			data.seq = v.seq
			data.can_exchange = can_exchange
			data.time_enough = time_enough
			data.is_times_limit = is_times_limit
			data.exchange_time = exchange_time
			data.has_enough_stuff = has_enough_stuff
			table.insert(data_list, data)
		end

		table.sort(data_list, function (a, b)
			if a.sort_index > b.sort_index then
				return true
			elseif a.sort_index == b.sort_index then
				return a.seq < b.seq
			else
				return false
			end
		end)
	end

	return data_list
end

function HolyHeavenlyDomainShopView:OnClickLuckyItem()
	local convert_cost_item = HolyHeavenlyDomainWGData.Instance:GetConvertShowItemId()
	if nil ~= convert_cost_item then
		TipWGCtrl.Instance:OpenItem({item_id = convert_cost_item})
	end
end

---------------------------HHDShopItemRender----------------------------
HHDShopItemRender = HHDShopItemRender or BaseClass(BaseRender)

function HHDShopItemRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function HHDShopItemRender:LoadCallBack()
	if not self.item then
		self.item = ItemCell.New(self.node_list.cell_pos)
	end

	XUI.AddClickEventListener(self.node_list.convert_btn, BindTool.Bind1(self.OnClickConvertBtn, self))
	XUI.AddClickEventListener(self.node_list.stuff_icon, BindTool.Bind1(self.OnClickStuffIconBtn, self))              
end

function HHDShopItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local data = self.data
	local exchange_time = data.exchange_time
	local item_count = ItemWGData.Instance:GetItemNumInBagById(data.cfg.stuff_id_1)
	local can_exchange = data.can_exchange
	local time_enough = data.time_enough
	local is_times_limit = data.is_times_limit
	local times_limit = data.cfg.times_limit
	local limit_buy_str = ToColorStr((times_limit - exchange_time) .. "/" .. times_limit, time_enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
	self.node_list.limit_count_desc.text.text = is_times_limit and limit_buy_str or Language.HolyHeavenlyDomain.NotLimitBuy
	XUI.SetGraphicGrey(self.node_list.convert_btn, not can_exchange)
	self.node_list.convert_btn_text.text.text = time_enough and Language.HolyHeavenlyDomain.ShopBuy or Language.HolyHeavenlyDomain.ShopSellOut

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.cfg.item.item_id)
	if not IsEmptyTable(item_cfg) then
		self.item:SetData(data.cfg.item)
		self.node_list.cell_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR_DARK[item_cfg.color])
	end

	local stuff_cfg = ItemWGData.Instance:GetItemConfig(data.cfg.stuff_id_1)
	if not IsEmptyTable(stuff_cfg) then
		local bundel, asset = ResPath.GetItem(stuff_cfg.icon_id)
		self.node_list.stuff_icon.image:LoadSprite(bundel, asset, function()
			self.node_list.stuff_icon.image:SetNativeSize()
		end)
	end

	local has_enough_stuff = data.has_enough_stuff
	local stuff_str = ToColorStr(item_count .. "/" .. data.cfg.stuff_num_1, has_enough_stuff and COLOR3B.D_GREEN or COLOR3B.D_RED)
	self.node_list.stuff_num.text.text = stuff_str
end

function HHDShopItemRender:OnClickConvertBtn()
	if IsEmptyTable(self.data) then
		return
	end

	if self.data.can_exchange then
		HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.CONVERT, self.data.cfg.seq)
	else
		if self.data.has_enough_stuff then
			TipWGCtrl.Instance:ShowSystemMsg(Language.HolyHeavenlyDomain.MaxExchangeTime)
		else
			TipWGCtrl.Instance:OpenItem({item_id = self.data.cfg.stuff_id_1})
		end
	end
end

function HHDShopItemRender:OnClickStuffIconBtn()
	if IsEmptyTable(self.data) then
		return
	end

	TipWGCtrl.Instance:OpenItem({item_id = self.data.cfg.stuff_id_1})
end