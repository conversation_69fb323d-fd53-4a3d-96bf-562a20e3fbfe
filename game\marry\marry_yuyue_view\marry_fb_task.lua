MarryFbTaskView = MarryFbTaskView or BaseClass(SafeBaseView)

function MarryFbTaskView:__init()
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_fb_txt_tips")
	
	
	self.active_close = false
	self.anim_state = true
	self.end_time = 2
end

function MarryFbTaskView:__delete()

end

function MarryFbTaskView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("Marry_fb_task_timer") then
		CountDownManager.Instance:RemoveCountDown("Marry_fb_task_timer")
	end

	if self.task_cells_1 then
		for k,v in pairs(self.task_cells_1) do
			v:DeleteMe()
		end
		self.task_cells_1 = nil
	end

	if self.task_cells_2 then
		for k,v in pairs(self.task_cells_2) do
			v:DeleteMe()
		end
		self.task_cells_2 = nil
	end

	if self.data_node then
		local obj = self.data_node.node_list.layout_fb_task.gameObject
		self.data_node:DeleteMe()
		ResMgr:Destroy(obj)
		self.data_node = nil
	end

	self.need_flush = false
	self.load_finish = false
	self.anim_state = true
end

function MarryFbTaskView:LoadCallBack()
	self.state_cache = 1
	local callback = function ()
		ResMgr:LoadGameobjSync("uis/view/marry_ui_prefab", "layout_fb_task",
			function (obj)
				local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
				parent:SetActive(true)
				obj.transform:SetParent(parent.transform,false)
				obj = U3DObject(obj)
				self.data_node = MarryFbTaskRender.New(obj)

				self:LoadTaskCallBack()
				self.load_finish = true
				if self.need_flush then
					self.data_node.node_list["layout_fb_task"]:SetActive(true)
					self:Flush()
					self.need_flush = false
				end
			end)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
end

function MarryFbTaskView:LoadTaskCallBack()
	-- self.data_node.node_list.btn_anim:SetActive(false)
	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	self.task_cells_1 = {}
	local num = #other_cfg.lovers_fb_item1
	for i = 0, num do
		self.task_cells_1[i] = ItemCell.New(self.data_node.node_list["ph_list_2"])
		-- self.task_cells_1[i].root_node.rect.localScale = Vector3(0.8,0.8,0.8)
	end

	self.task_cells_2 = {}
	local num_2 = #other_cfg.lovers_fb_item2
	for i = 0, num_2 do
		self.task_cells_2[i] = ItemCell.New(self.data_node.node_list["ph_list_1"])
		-- self.task_cells_2[i].root_node.rect.localScale = Vector3(0.8,0.8,0.8)
	end
	for k,v in pairs(self.task_cells_1) do
		v:SetData(other_cfg.lovers_fb_item1[k])
		-- v.text_bottom_text.text.fontSize = 25
	end

	for k,v in pairs(self.task_cells_2) do
		v:SetData(other_cfg.lovers_fb_item2[k])
		-- v.text_bottom_text.text.fontSize = 25
	end

    -- local w1 = self.data_node.node_list.ph_list_1_parent.rect.sizeDelta.x
    -- local w2 = self.data_node.node_list.ph_list_1.rect.sizeDelta.x
    -- local w3 = self.data_node.node_list.ph_list_2_parent.rect.sizeDelta.x
    -- local w4 = self.data_node.node_list.ph_list_2.rect.sizeDelta.x
    -- self.data_node.node_list.ph_list_1_parent.scroll_rect.enabled = w1 < w2
    -- self.data_node.node_list.ph_list_2_parent.scroll_rect.enabled = w3 < w4
	-- self.pos1 = self.data_node.node_list.down_pos_1.rect.anchoredPosition
	-- self.pos2 = self.data_node.node_list.down_pos_2.rect.anchoredPosition

	-- self.data_node.node_list.btn_anim.button:AddClickListener(BindTool.Bind2(self.OnClickAnimHandler, self))

	--单人进入文本操作
	local is_single_enter = MarryWGData.Instance:IsSingleEnterFB()
	self.data_node.node_list["txt_down_tips"].text.text = is_single_enter and Language.Marry.QingYuanSingleEnterDownTip or Language.Marry.QingYuanTeamEnterDownTip

	self:Flush()
end

function MarryFbTaskView:OnClickAnimHandler()
	local pos_y,scale_y
	if self.anim_state then
		pos_y = self.pos1.y
		scale_y = 1
	else
		pos_y = self.pos2.y
		scale_y = -1
	end
	-- self.data_node.node_list.btn_anim.rect.localScale = Vector3(1,scale_y,1)
	self.anim_state = not self.anim_state
	local tweenY = self.data_node.node_list.down_content.rect:DOAnchorPosY(pos_y, 0.3)
	tweenY:SetEase(DG.Tweening.Ease.Linear)
end

function MarryFbTaskView:ShowIndexCallBack()
	if self.close_callback_use and self.load_finish then
		self.data_node.node_list["layout_fb_task"]:SetActive(true)
	end
end

function MarryFbTaskView:CloseCallBack()
	self.close_callback_use = true
	if self.data_node then
		self.data_node.node_list["layout_fb_task"]:SetActive(false)
	end
end

function MarryFbTaskView:OnFlush()
	if not self.load_finish then
		self.need_flush = true
		return
	end
	local scene_info = MarryWGData.Instance:GetQyFbSceneInfo()
	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	if scene_info and not IsEmptyTable(scene_info) and other_cfg then
		local str = string.format(Language.Marry.MarryFbTask, other_cfg.lovers_fb_boss_name, scene_info.kill_boss_num)
		self.data_node.node_list.rich_boss_name.text.text = str
		--self.data_node.node_list.lbl_monster_num.text.text = (scene_info.kill_monster_num .. "/" .. other_cfg.lovers_fb_total_monster)
	end

	-- local state = scene_info.kill_monster_num >= MarryWGData.Instance:GetMarryFbCfg()[1].num
	-- if state ~= self.state_cache then
	-- 	self.state_cache = state
	-- 	if state then
	-- 		self.node_list["img_word_tip"]:SetActive(true)
	-- 		if CountDownManager.Instance:HasCountDown("Marry_fb_task_timer") then
	-- 			CountDownManager.Instance:RemoveCountDown("Marry_fb_task_timer")
	-- 		end

	-- 		CountDownManager.Instance:AddCountDown("Marry_fb_task_timer", 
	-- 		nil, BindTool.Bind1(self.EndUpdateTime, self), nil, self.end_time, 1)
	-- 	else
	-- 		-- self.node_list["img_word_tip"]:SetActive(state)
	-- 	end
	-- end
end

function MarryFbTaskView:EndUpdateTime()
	-- self.node_list["img_word_tip"]:SetActive(false)
end
------------------------------------------------------------------------------------------------


MarryFbTaskRender = MarryFbTaskRender or BaseClass(BaseRender)

function MarryFbTaskRender:__init()

end

function MarryFbTaskRender:__delete()

end

function MarryFbTaskRender:LoadCallBack()

end

function MarryFbTaskRender:OnFlush()

end