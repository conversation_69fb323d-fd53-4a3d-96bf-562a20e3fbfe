﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ParticleSystemUtil
{
    // 计算粒子在生命周期内总共的最大大小
    public static float CaleParticleMaxTotalSize(List<ParticleSystem>particelList)
    {
        float[] startTimes = new float[particelList.Count];
        float[] endTimes = new float[particelList.Count];
        float playTime = 0;
        for (int i = 0; i < particelList.Count; i++)
        {
            var particle = particelList[i];
            var main = particle.main;
            startTimes[i] = main.startDelay.constantMax;
            endTimes[i] = startTimes[i] + main.startLifetime.constantMax;
            playTime = Mathf.Max(endTimes[i]);
        }

        float sampleCount = 10;  // 采样次数
        float curTime = 0.0f;
        float maxTotalSize = 0.0f;
        while (curTime < playTime)
        {
            float evaluateTotalSize = 0;
            for (int i = 0; i < particelList.Count; i++)
            {
                if (curTime >= startTimes[i] && curTime <= endTimes[i])
                {
                    float emitPassTime = curTime - startTimes[i];  // emitPassTime为0时，表示粒子开始发射
                    float emmitCount = CalcParticleCountInPerSecond(particelList[i]); // 每秒存在的粒子个数
                    evaluateTotalSize += EvaluateParticlePlanSize(particelList[i], emitPassTime) * emmitCount;
                }
            }

            maxTotalSize = Mathf.Max(maxTotalSize, evaluateTotalSize);
            curTime += playTime / sampleCount;
        }

        return maxTotalSize;
    }

    // 采样粒子在某个时间点的大小
    // emitPassTime是粒子发射出后的经过的时间，不是粒子系统的播放时间
    public static float EvaluateParticlePlanSize(ParticleSystem particle, float emitPassTime)
    {
        float particleSize = Mathf.Max(particle.main.startSize.constantMax, particle.main.startSize.constantMin);
        if (particle.sizeOverLifetime.enabled)
        {
            var timeSize = particle.sizeOverLifetime.size;
            AnimationCurve curve = timeSize.curveMax;
            var scale = curve.Evaluate(emitPassTime);
            particleSize *= scale;
        }

    //    Debug.LogErrorFormat("EvaluateParticleSize {0} {1} {2} {3}", particle.name, emitPassTime, particleSize, CalcParticleCountInPerSecond(particle));

        return particleSize;
    }

    // 获得单个粒子面片的最大面积。
    public static float CalcParticlePlanMaxSize(ParticleSystem particle)
    {
        float particleSize = Mathf.Max(particle.main.startSize.constantMax, particle.main.startSize.constantMin);
        if (particle.sizeOverLifetime.enabled)
        {
            var timeSize = particle.sizeOverLifetime.size;
            AnimationCurve curve = timeSize.curveMax;
            float maxScale = 0.0f;
            for (int m = 0; m < curve.keys.Length; m++)
            {
                if (curve.keys[m].value > maxScale)
                {
                    maxScale = curve.keys[m].value;
                }
            }
            particleSize *= maxScale;
        }

        return particleSize;
    }

    // 获得每秒平均粒子数
    public static int CalcParticleCountInPerSecond(ParticleSystem particle)
    {
        float particleCount = 0;
        if (particle.emission.enabled)
        {
            if (particle.emission.burstCount > 0)
            {
                particleCount = particle.emission.GetBurst(0).maxCount;
            }
            else
            {
                if (particle.emission.rateOverTime.mode == ParticleSystemCurveMode.Constant)
                {
                    particleCount = particle.emission.rateOverTime.constant;
                }
                else
                {
                    particleCount = (particle.emission.rateOverTime.constantMax + particle.emission.rateOverTime.constantMin) / 2; //每秒发射N个粒子
                }
            }

            particleCount = (int)Mathf.Ceil(CalcParitcleAvgLifeTime(particle) * particleCount);
        }

        particleCount = Mathf.Min(particleCount, particle.main.maxParticles);

        return (int)particleCount;
    }

    // 获得粒子的平均存活时间
    public static float CalcParitcleAvgLifeTime(ParticleSystem particle)
    {
        if (particle.main.startLifetime.mode == ParticleSystemCurveMode.Constant)
        {
            return particle.main.startLifetime.constant;
        }
        else
        {
            return (particle.main.startLifetime.constantMax + particle.main.startLifetime.constantMin) / 2;
        }
    }
}
