YunbiaoAlert = YunbiaoAlert or BaseClass(Alert)


function YunbiaoAlert:__init(str, ok_func, cancel_func, close_func, has_checkbox, is_show_action, is_maskbg_button_click)
	self.ui_config = {"uis/view/dialog_ui_prefab", "DialogUi"}
	self.config_tab = {
		{"layout_husong_dialog", {0}},
	}
end

function YunbiaoAlert:__delete()

end

function YunbiaoAlert:LoadCallBack()
	self.rich_dialog = self.node_list["rich_dialog"]
	self.image_bg = self.node_list["img9_autoname_2"]
	self:SetLableString(self.content_str)
	self:SetTextLineSpace(self.dialog_line_spacing)

	self.node_list["btn_OK"]:FindObj("Text").text.text = self.ok_btn_text
	self.node_list["btn_cancel"]:FindObj("Text").text.text = self.cancel_btn_text
	self.node_list["layout_nolonger_tips"]:SetActive(self.has_checkbox)
	self.node_list["label_no_longer"].text.text = self.checkbox_tip_text

	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))

	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind1(self.OnClickCancel, self))

	self.node_list["img_nohint_hook"]:SetActive(self.check_box_default_select)
	self.node_list["btn_nohint_checkbox"].button:AddClickListener(BindTool.Bind1(self.OnClickCheckBox, self))

	self.load_complete = true
	if self.is_should_load_call then
		self.is_should_load_call = nil
		self:AutoSetAlignment()
		self:SetAlignment(self.alignment_type)
		self:AutoSetLabelRectWidth()
	end
end