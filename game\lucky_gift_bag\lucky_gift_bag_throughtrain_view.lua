LuckyGiftBagThroughTrainView = LuckyGiftBagThroughTrainView or BaseClass(SafeBaseView)

function LuckyGiftBagThroughTrainView:__init()
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_through_train_view")
	self:SetMaskBg(true, true)
end

function LuckyGiftBagThroughTrainView:ReleaseCallBack()
    if self.through_train_list then
        self.through_train_list:DeleteMe()
        self.through_train_list = nil
    end
end

function LuckyGiftBagThroughTrainView:LoadCallBack()
	if nil == self.through_train_list then
		self.through_train_list = AsyncListView.New(LuckyGiftBagThroughTrainRender, self.node_list["through_train_list"])
	end
end

function LuckyGiftBagThroughTrainView:OnFlush()
    local data_list = LuckyGiftBagWgData.Instance:GetThroughTrainDataList()
    local no_data = IsEmptyTable(data_list)
    self.node_list["nodata"]:SetActive(no_data)

    if not no_data then
        self.through_train_list:SetDataList(data_list)
    end
end

------------------------------------------------Item-------------------------------------
LuckyGiftBagThroughTrainRender = LuckyGiftBagThroughTrainRender or BaseClass(BaseRender)
function LuckyGiftBagThroughTrainRender:__init()
    if not self.show_reward_item then
        self.show_reward_item = ItemCell.New(self.node_list.main_item_pos) 
    end

    if not self.through_train_reward_list then
        self.through_train_reward_list = AsyncBaseGrid.New()
		self.through_train_reward_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.through_train_reward_list})
		self.through_train_reward_list:SetStartZeroIndex(true) 
    end

    XUI.AddClickEventListener(self.node_list.through_train_ibtn_bug, BindTool.Bind1(self.OnClickThroughTrainBtn, self))
end

function LuckyGiftBagThroughTrainRender:__delete()
    if self.show_reward_item then
        self.show_reward_item:DeleteMe()
        self.show_reward_item = nil
    end

    if  self.through_train_reward_list then
        self.through_train_reward_list:DeleteMe()
        self.through_train_reward_list = nil
    end
end

function LuckyGiftBagThroughTrainRender:OnFlush()
    if not self:GetData() then
        return
    end

    local data = self:GetData()
    local price_str = RoleWGData.GetPayMoneyStr(data.rmb_price, data.rmb_type, data.rmb_seq)
    self.node_list.cost.text.text = price_str
    self.show_reward_item:SetData(data.show_reward_item)
    self.through_train_reward_list:SetDataList(data.reward_item)

    local buy_state = LuckyGiftBagWgData.Instance:GetThroughTrainTaskFlag(data.seq)
    XUI.SetButtonEnabled(self.node_list.through_train_ibtn_bug, not buy_state)
    self.node_list.through_train_btn_text.text.text = buy_state and Language.LuckyGiftBag.BtnStatePurchased or Language.LuckyGiftBag.BtnStateBugNow
end

function LuckyGiftBagThroughTrainRender:OnClickThroughTrainBtn()
    if not self:GetData() then
        return
    end

    local data = self:GetData()
    RechargeWGCtrl.Instance:Recharge(data.rmb_price, data.rmb_type, data.seq)
end