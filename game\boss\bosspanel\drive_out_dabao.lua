--------------------------------------------------
-- 打宝boss满提出地图
--------------------------------------------------
DabaoBossDriveView = DabaoBossDriveView or BaseClass(SafeBaseView)

function DabaoBossDriveView:__init()
	self.is_modal = true
	self.is_any_click_close = false

	-- self.texture_path_list[1] = "res/xui/boss.png"
	--self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_drive_view")
end

function DabaoBossDriveView:__delete()

end

function DabaoBossDriveView:ReleaseCallBack()
    CountDownManager.Instance:RemoveCountDown("drive_count")
end

function DabaoBossDriveView:LoadCallBack()
	self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind1(self.OnClickOK,self))
end

function DabaoBossDriveView:ShowIndexCallBack()
	self:Flush()
end

function DabaoBossDriveView:setFromBossType(boss_type)
	self.boss_type = boss_type
end

function DabaoBossDriveView:OnFlush()
	local str = ""
	if self.boss_type == SceneType.KFSHENYUN_FB then
		self.node_list["img_tip"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("boss_timedown_4"))
		str = Language.Boss.DriveSY
	elseif self.boss_type == SceneType.SG_BOSS then
        str = Language.Boss.DriveSG
    elseif self.boss_type == SceneType.XianJie_Boss then
		str = Language.XianJieBoss.DriveXianjie
	else
		str = Language.Boss.DriveDB
	end
	self.node_list.img_drive_word.text.text = str

	if self.boss_type ~= SceneType.KFSHENYUN_FB then
	    CountDownManager.Instance:AddCountDown("drive_count",
			BindTool.Bind1(self.UpdateCountDownTime, self),
			BindTool.Bind(self.CompleteCountDownTime, self, true),
			nil, 6, 0.5)
	else
		self.node_list["Txt_ok"].text.text = Language.Common.Confirm
	end
end

function DabaoBossDriveView:UpdateCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	self.node_list["Txt_ok"].text.text = Language.Common.Confirm.."("..last_time..")"
end

function DabaoBossDriveView:CompleteCountDownTime(is_auto_fuhuo)
	self:Close()
end

function DabaoBossDriveView:OnClickOK()
	CountDownManager.Instance:RemoveCountDown("drive_count")
	self:Close()
	FuBenWGCtrl.Instance:SendLeaveFB()
end
