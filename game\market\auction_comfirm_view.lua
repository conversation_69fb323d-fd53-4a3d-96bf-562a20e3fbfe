
--竞价确认面板
----------------------------------
AuctionComfirmView = AuctionComfirmView or BaseClass(SafeBaseView)

local normal_bg_width = 364

function AuctionComfirmView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "AuctionComfirmView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(598, 420)})
	self:AddViewResource(0, "uis/view/market_ui_prefab", "layout_auction_comfirm_view")

	self.total_price = 0 --总出价格
	self.total_step = 0 --总档位
end

function AuctionComfirmView:ReleaseCallBack()
	if self.alert then
		self.alert:DeleteMe()
	end
end

function AuctionComfirmView:SetInfo(info)
	self.data = info
	if not self.data then return end
	self.cur_cfg = MarketWGData.Instance:GetNewAuctionCfgByType(self.data.type, self.data.item_id)
end

function AuctionComfirmView:LoadCallBack()
	--self:SetSecondView(Vector2(591, 344))
	self.node_list.cancel_btn.button:AddClickListener(BindTool.Bind(self.Close, self))--取消
	self.node_list.comfirm_btn.button:AddClickListener(BindTool.Bind(self.OnOkBtnClick, self))--确定

	-- self.node_list.tips_btn.button:AddClickListener(BindTool.Bind(self.OnTipsBtnClick, self))--显示Tips

	self.node_list["slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))

	XUI.AddClickEventListener(self.node_list["btn_sub"], BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list["btn_add"], BindTool.Bind1(self.OnClickAdd, self))
end

function AuctionComfirmView:OnFlush()
	if not self.data or not self.cur_cfg then return end

	-- local my_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	-- self.is_auto_auction = self.data.auto_bid_uid == my_role_id --是否自动竞价

	--当前出价
	local cur_price = self.data.auction_price == 0 and self.cur_cfg.initial_price or self.data.auction_price
	--最大值 = 一口价
	self.max_auction_cost = self.cur_cfg.one_price
	--判断是否为此商品的首次出价
	if self.data.auction_times > 0 then
		self.min_auction_cost = cur_price + self.cur_cfg.bid_price_add --最低出价
	else
		self.min_auction_cost = cur_price
	end

	if self.min_auction_cost > self.max_auction_cost then
		self.min_auction_cost = self.max_auction_cost
	end

	self.total_price = self.min_auction_cost
	self.total_step = (self.max_auction_cost - self.min_auction_cost) / self.cur_cfg.bid_price_add
	-- print_error("self.total_step", self.total_step, self.max_auction_cost, self.min_auction_cost, self.cur_cfg.bid_price_add)
	self.cur_step = 0 --当前档位
	self.node_list["slider"].slider.value = 0

	local num_str = string.format("%s-%s", self.min_auction_cost, self.max_auction_cost)
	self.node_list.text_range_1.text.text = string.format(Language.Market.Auction_Price_Range, num_str)
	self:FlushTextShow()
end

function AuctionComfirmView:OnOkBtnClick()
	if not self.data or not self.cur_cfg then return end
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	if self.total_price >= self.max_auction_cost then
		if main_role_vo.gold < self.cur_cfg.one_price then
			VipWGCtrl.Instance:OpenTipNoGold()
			return--仙玉不足
		end

		if nil == self.alert then
			self.alert = Alert.New()
		end
		local show_str = string.format(Language.Market.Auction_Confirm_One_Price, self.cur_cfg.one_price)
		self.alert:SetLableString(show_str)
		self.alert:SetOkFunc(function()
			--一口价购买
			local is_corss_server = self.data.fix_type == AUCTION_TYPE.Guild_Battle
			MarketWGCtrl.Instance:SendCSNewAuctionOperate(is_corss_server, NEW_AUCTION_OPERATE_TYPE.BID, nil, 1, nil, self.data.index)
			self:Close()
		end)
		self.alert:Open()
		return
	end

	if main_role_vo.gold < self.total_price then
		VipWGCtrl.Instance:OpenTipNoGold()
		return--仙玉不足
	end

	local is_corss_server = self.data.fix_type == AUCTION_TYPE.Guild_Battle
	MarketWGCtrl.Instance:SendCSNewAuctionOperate(is_corss_server, NEW_AUCTION_OPERATE_TYPE.BID, self.total_price, 0, nil, self.data.index)
	self:Close()
end

function AuctionComfirmView:OnSoundValueChange(value)
	self.cur_step = GameMath.Round(value * self.total_step)
	self.total_price = (self.cur_cfg.bid_price_add * self.cur_step) + self.min_auction_cost
	self:FlushTextShow()
end

function AuctionComfirmView:OnClickSub()
	self.cur_step = self.cur_step - 1
	if self.cur_step <= 0 then
		self.cur_step = 0
	end
	self.node_list["slider"].slider.value = self.cur_step / self.total_step
end

function AuctionComfirmView:OnClickAdd()
	self.cur_step = self.cur_step + 1
	if self.cur_step >= self.total_step then
		self.cur_step = self.total_step
	end
	self.node_list["slider"].slider.value = self.cur_step / self.total_step
end

function AuctionComfirmView:FlushTextShow()
	self.node_list["text_show_title"].text.text = string.format(Language.Market.Auction_Confirm_Title, self.total_price)
	self.node_list["text_cur_price"].text.text = self.total_price
end

--Tips
-- function AuctionComfirmView:OnTipsBtnClick()
-- 	RuleTip.Instance:SetContent(Language.Market.Auction_Long_Tips, Language.Market.Auction_Tips_Title)
-- end
