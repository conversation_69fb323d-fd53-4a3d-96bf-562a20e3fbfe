--boss个人首杀奖励红包面板
BossFirstKillRedPacket = BossFirstKillRedPacket or BaseClass(SafeBaseView)

function BossFirstKillRedPacket:__init()
    self.view_layer = UiLayer.Pop
    self.view_name = "BossFirstKillRedPacket"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "person_firstkill_red_tips")
	self:SetMaskBg(true)
end

function BossFirstKillRedPacket:SendGetRedpacket()
    if self.right_view_tween then
        self.right_view_tween:Kill()
        self.right_view_tween = nil
    end

    self.right_view_tween = DG.Tweening.DOTween.Sequence()
    self.is_dotween = true
    self.right_view_tween:Append(self.node_list.right_view.rect:DOScale(Vector3(0.7,0.7,0.7), 0.1):SetEase(DG.Tweening.Ease.OutCubic))
    self.right_view_tween:Append(self.node_list.right_view.rect:DOScale(Vector3(1,1,1), 0.1):SetEase(DG.Tweening.Ease.OutCubic))
    self.right_view_tween:OnComplete(function()
        self.is_dotween = false
        self.node_list.right_view:SetActive(false)
        self.node_list.left_view:SetActive(true)
        -- if self.is_get then --是否领取了 
        --     self:PlayEffect(self.data.person_firstkill_reward)      
        -- end
    end)
    BossWGCtrl.Instance:SendFirstKillOpera(BossWGData.CS_BOSS_FIRST_KILL_TYPE.CS_BOSS_FIRST_KILL_TYPE_FETCHPERSON, self.boss_id, self.boss_type)
end

function BossFirstKillRedPacket:LoadCallBack()
    self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.Close, self))
    self.node_list.btn_open.button:AddClickListener(BindTool.Bind(self.SendGetRedpacket, self))
    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["moneybar_pos"].transform)
	end
end

function BossFirstKillRedPacket:ShowIndexCallBack()
    self:SendGetRedpacket()  -- 需求  打开界面自动领取红包
end

function BossFirstKillRedPacket:ReleaseCallBack()
    if self.head1 then
		self.head1:DeleteMe()
		self.head1 = nil
	end
    if self.head2 then
		self.head2:DeleteMe()
		self.head2 = nil
    end

    if self.right_view_tween then
        self.right_view_tween:Kill()
        self.right_view_tween = nil
    end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
    end
    
    if self.delay_play then
        GlobalTimerQuest:CancelQuest(self.delay_play)
        self.delay_play = nil
    end
end

function BossFirstKillRedPacket:SetBossIdData(boss_id, boss_type)
    self.boss_id = boss_id
    self.boss_type = boss_type
    self:Open()
end

function BossFirstKillRedPacket:OnFlush()
    local role_vo = GameVoManager.Instance:GetMainRoleVo()
    self.node_list.lbl_role_name.text.text = role_vo.name
    self.node_list.lbl_role_name2.text.text = role_vo.name
    if not self.boss_id then
        return
    end
    self.data = BossWGData.Instance:GetBossInfoByBossId(self.boss_id)
   
    if not self.data then
        return
    end
    self.node_list.reward_num.text.text = self.data.person_firstkill_reward
    self.head1 = BaseHeadCell.New(self.node_list.img_head)
    self.head2 = BaseHeadCell.New(self.node_list.img_head2)
    
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = 0}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true
    self.head1:SetData(data)
    self.head2:SetData(data)
    self.node_list.lbl_boss_name1.text.text = self.data.boss_name
    self.node_list.lbl_boss_name.text.text = self.data.boss_name
    local _, isnot_get = BossWGData.Instance:GetIsPersonFirstKilledByBossId(self.boss_id)
    self.is_get = not isnot_get
    if not self.is_dotween then
        self.node_list.right_view:SetActive(isnot_get)
        self.node_list.left_view:SetActive(not isnot_get)
    end
    -- if self.is_get then --是否领取了
    --     if not self.is_dotween then
    --         self:PlayEffect(self.data.person_firstkill_reward)
    --     end
    -- end
end

function BossFirstKillRedPacket:PlayEffect(num)
    FightWGCtrl.Instance:DoMoneyEffect(GameEnum.NEW_MONEY_BAR.BIND_XIANYU, self.node_list.start_pos.rect)
    self.delay_play = GlobalTimerQuest:AddDelayTimer(function ()
    self:PlayEffectByRechargeSecond(num)
    end, 0.1)
end

function BossFirstKillRedPacket:PlayEffectByRechargeSecond(effect_count)
    local view_name = "BossFirstKillRedPacket"
    TipWGCtrl.Instance:DestroyFlyEffectByViewName(view_name)
    if self.node_list.moneybar_pos then
        local start_pos = self.node_list.start_pos
        local end_pos = self.node_list.end_pos
        local effect_count = effect_count or 20
        local eff = MONEY_BAR_EFFECT[GameEnum.NEW_MONEY_BAR.BIND_XIANYU]
        if nil == eff or nil == eff.FLY then return end
        local bundle, asset = ResPath.GetUIEffect(eff.FLY)
        TipWGCtrl.Instance:ShowFlyEffectManager(view_name, bundle, asset, start_pos, end_pos, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, effect_count, 150, nil, false, true)
    end
end