RoleWGData = RoleWGData or BaseClass()

function RoleWGData:InitSkillUpData()
    RemindManager.Instance:Register(RemindName.SkillUpLevel, BindTool.Bind(self.GetSkillUplevelRemind, self))
    RemindManager.Instance:Register(RemindName.SkillUpLevel_Inside, BindTool.Bind(self.GetSkillUplevelRemind, self, true))
    RemindManager.Instance:Register(RemindName.SkillAwake, BindTool.Bind(self.GetSkillAwakeRemind, self))
    RemindManager.Instance:Register(RemindName.SkillUpGrade, BindTool.Bind(self.GetSkillUpGradeRemind, self))

    self:RegisterSkillAwakeRemindInBag(RemindName.SkillAwake)
end

function RoleWGData:DeleteSkillUpData()
    --if self.item_data_change then
    --    ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
    --    self.item_data_change = nil
    --end
    RemindManager.Instance:UnRegister(RemindName.SkillUpLevel)
    RemindManager.Instance:UnRegister(RemindName.SkillUpLevel_Inside)
    RemindManager.Instance:UnRegister(RemindName.SkillAwake)
    RemindManager.Instance:UnRegister(RemindName.SkillUpGrade)
   --[[ RemindManager.Instance:UnRegister(RemindName.SkillAwake)
    --]]
end

function RoleWGData:RegisterSkillAwakeRemindInBag(remind_name)
    self.skill_awake_itemid_map = {}

    local career_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").skill_awken
    for k,v in pairs(career_cfg) do
        self.skill_awake_itemid_map[v.consume_stuff_id1] = true
        self.skill_awake_itemid_map[v.consume_stuff_id2] = true
    end

    local item_id_list = {}
    for k,v in pairs(self.skill_awake_itemid_map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function RoleWGData:GetIsSkillAwakeItem(item_id)
    return self.skill_awake_itemid_map[item_id]
end

function RoleWGData:GetSkillUplevelRemind(is_inside_remind)
    if not is_inside_remind then
        local remind_flag_type = self:PrimaryUpLogic()
        if remind_flag_type == RemindWGData.RemindSkillFlagType.Type_2 then
            self:MainInvateTip(MAINUI_TIP_TYPE.SKILL_UPLEVEL, TabIndex.skill_zhudong, true)
            return 1
        end
    else
        local skill_list = SkillWGData.Instance:GetNowSkillInfo()
        for i, v in pairs(skill_list) do
            local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
            if skill_info then
                if self:GetSkillCanUpLevelBySkillId(v.skill_id, skill_info) then
                    return 1
                end
            end
        end
    end

    self:MainInvateTip(MAINUI_TIP_TYPE.SKILL_UPLEVEL)
    return 0
end

function RoleWGData:MainInvateTip(show_tip, tab_index, result)
    if result then
        MainuiWGCtrl.Instance:InvateTip(show_tip, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.SkillView, tab_index)
            return true
        end)
    else
        MainuiWGCtrl.Instance:InvateTip(show_tip,0)
    end
end

function RoleWGData:GetSkillAwakeRemind()
    local skill_list = SkillWGData.Instance:GetNowSkillInfo()
    for i, v in pairs(skill_list) do
        local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
        if skill_info then
            if self:GetSkillCanBreakBySkillId(v.skill_id, skill_info) then
                self:MainInvateTip(MAINUI_TIP_TYPE.SKILL_AWAKE, TabIndex.skill_awake, true)
                return 1
            end
        end
    end
    self:MainInvateTip(MAINUI_TIP_TYPE.SKILL_AWAKE)
    return 0
end

function RoleWGData:GetSkillCanUpLevelBySkillId(skill_id, skill_info)
    local role_info = RoleWGData.Instance:GetRoleInfo()
    skill_info = skill_info or SkillWGData.Instance:GetSkillInfoById(skill_id)
    if not skill_info then
        return false
    end
    local next_level_cfg = SkillWGData.Instance:GetSkillLevelCfgByLevel(skill_id, skill_info.level + 1)
    if next_level_cfg == nil then
        return false
    end
    if next_level_cfg.role_level > role_info.level then
        return false
    end
    if next_level_cfg.consume_coin > role_info.coin then
        return false
    end
    return true
end

function RoleWGData:GetSkillCanBreakBySkillId(skill_id, skill_info)
    local is_open = FunOpen.Instance:GetFunIsOpened("skill_awake")
    if not is_open then
        return false
    end
    skill_info = skill_info or SkillWGData.Instance:GetSkillInfoById(skill_id)
    if not skill_info or skill_info.awake_level == nil then
        return false
    end
    local next_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(skill_id, skill_info.awake_level + 1)
    if next_break_cfg == nil then
        return false
    end
    for i = 1, 10 do
		local id = next_break_cfg["consume_stuff_id" .. i]
		if id then
			local need_num = next_break_cfg["consume_count" .. i]
			if need_num > 0 then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(id)
				if has_num < need_num then
					return false
				end
			end
		else
			break
		end
	end
    return true
end

function RoleWGData:GetSkillUpGradeRemind()
    local skill_grade = SkillWGData.Instance:GetCurUpGradeInfo()
    if skill_grade == nil then
        self:MainInvateTip(MAINUI_TIP_TYPE.SKILL_UPGRADE)
        return 0
    end
    local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
    local skill_upgrade_limit_cfg = ConfigManager.Instance:GetAutoConfig("career_config_auto").skill_upgrade_limit
    for i, v in pairs(skill_upgrade_limit_cfg) do
        if prof_zhuan == v.zhuan_level_limit then
            self:MainInvateTip(MAINUI_TIP_TYPE.SKILL_UPGRADE, TabIndex.skill_upgrade, skill_grade < v.grade)
            return skill_grade < v.grade and 1 or 0
        end
    end

    return 0
end

function RoleWGData:GetSKillGradeEffectByType(select_type)
    local str
    if select_type == 1 then
        str = "UI_huoyan"
    elseif select_type == 2 then
        str = "UI_bingxue"
    else
        str = "UI_leidian"
    end
    local bundle = "effects2/prefab/ui/%s_prefab"
    bundle = string.format(bundle, string.lower(str))
    local asset = str .. ".prefab"
    return bundle, asset
end

function RoleWGData:GetAllSkillList()
    self.skill_data_list = SkillWGData.Instance:GetSkillListByType(1)
    return self.skill_data_list
end

function RoleWGData:GetSkillCfgById(skill_id)
    if not self.skill_data_list then
        return
    end
    for i, v in pairs(self.skill_data_list) do
        if v.skill_id == skill_id then
            return v
        end
    end
end

function RoleWGData:GetRedSkillSelectId()
    local skill_list = SkillWGData.Instance:GetNowSkillInfo()
     for i, v in pairs(skill_list) do
        local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
        if skill_info then
            if self:GetSkillCanUpLevelBySkillId(v.skill_id, skill_info) then
                return i
            end

            if self:GetSkillCanBreakBySkillId(v.skill_id, skill_info) then
                return i
            end
        end
    end
    return 1
end

function RoleWGData:GetSkillShowRed(skill_id, skill_info, tab_index)
    if tab_index == TabIndex.skill_zhudong then
        return self:GetSkillCanUpLevelBySkillId(skill_id, skill_info)
    else
        return self:GetSkillCanBreakBySkillId(skill_id, skill_info)
    end
    -- if self:GetSkillCanUpLevelBySkillId(skill_id, skill_info) or self:GetSkillCanBreakBySkillId(skill_id, skill_info) then
    --     return true
    -- end
    --return false
end

function RoleWGData:GetSkillUplevelInsideRemind()
    local skill_list = SkillWGData.Instance:GetNowSkillInfo()
    for i, v in pairs(skill_list) do
        local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
        if skill_info then
            if self:GetSkillCanUpLevelBySkillId(v.skill_id, skill_info) then
                return true
            end
        end
    end
    return false
end
-- 初级材料特殊判断
function RoleWGData:PrimaryUpLogic()
    local remind_cfg = RemindWGData.Instance:GetSkillUpCfg()
    if not remind_cfg then
        return RemindWGData.RemindSkillFlagType.Type_1
    end

    local skill_list = SkillWGData.Instance:GetNowSkillInfo()
    if IsEmptyTable(skill_list) then
        return RemindWGData.RemindSkillFlagType.Type_1
    end

    local skill_level_list = {}
    for i,v in ipairs(skill_list) do
        local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
        if skill_info then
            local skill_cfg = {}
            skill_cfg.skill_id = v.skill_id
            skill_cfg.level = skill_info.level + 1
            table.insert(skill_level_list,skill_cfg)
        end
    end

    if IsEmptyTable(skill_level_list) then
        return RemindWGData.RemindSkillFlagType.Type_1
    end

    local fun = function (skill_level_list)
        local skill_index
        local skill_level = 10000
        for i,v in ipairs(skill_level_list) do
            if v.level < skill_level then
                skill_level = v.level
                skill_index = i
            end
        end
        return skill_index
    end

    local remind_num = remind_cfg.level
    local totle_coin = RoleWGData.Instance:GetRoleInfo().coin
    local role_level = RoleWGData.Instance:GetRoleInfo().level
    local can_up_num = 0
    local skill_index
    for i=1,remind_num do
        skill_index = fun(skill_level_list)
        if skill_index then
            local next_level_cfg = SkillWGData.Instance:GetSkillLevelCfgByLevel(skill_level_list[skill_index].skill_id, skill_level_list[skill_index].level + 1)
            if next_level_cfg and totle_coin >= next_level_cfg.consume_coin and role_level >= next_level_cfg.role_level then
                totle_coin = totle_coin - next_level_cfg.consume_coin
                skill_level_list[skill_index].level = skill_level_list[skill_index].level + 1
                can_up_num = can_up_num + 1
            end
        end
    end

    if can_up_num >= remind_num then
        return RemindWGData.RemindSkillFlagType.Type_2
    end

    return RemindWGData.RemindSkillFlagType.Type_1
end

function RoleWGData:CheckEnoughSkillUp(skill_id)
    local skill_info = SkillWGData.Instance:GetSkillInfoById(skill_id)
    if not skill_info then
        return false
    end
    local next_level_cfg = SkillWGData.Instance:GetSkillLevelCfgByLevel(skill_id, skill_info and skill_info.level+1)
    if next_level_cfg == nil then
        return false
    end

    local role_info = RoleWGData.Instance:GetRoleInfo()
    if next_level_cfg.role_level > role_info.level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Skill.LevelNotEnough)
        return false
    end

    if next_level_cfg.consume_coin > role_info.coin then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoCoin)
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_COIN})
        return false
    end

    return true
end