WuHunSkillEffectView = WuHunSkillEffectView or BaseClass(SafeBaseView)

function WuHunSkillEffectView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)
	self.view_layer = UiLayer.SkillFloatWord
    self.view_name = "WuHunSkillEffectView"
	local bundle_name = "uis/view/wuhunzhenshen_prefab"
    self:AddViewResource(0, bundle_name, "layout_tianshen_wuhun_skillshow")

	self.skill_show_list = {}
	self.is_play = false
	self.show_effect_timer = nil
	self.effect_view_type = 0
	self.hit_number = 0		--击破数
	self.obj = nil
end

function WuHunSkillEffectView:__delete()
end

function WuHunSkillEffectView:OpenCallBack()
	if self.is_view_loaded and (not self.is_play) then
		self:StartShowSkillEffet()
	end
end

function WuHunSkillEffectView:CloseCallBack()
	self.is_play = false
	self.hit_number = 0
	self.skill_show_list = {}

	if self.obj then
		self.obj:SetActive(false)
    end

	self:RemoveEffectDelayTimer()
end

-- 0 主界面    1 技能展示界面
function WuHunSkillEffectView:SetNoAddMainUINode(effect_type)
	self.effect_view_type = effect_type or 0
end

function WuHunSkillEffectView:LoadCallBack()
	if not self.is_play then
		self:StartShowSkillEffet()
	end

	self.node_list.hit_number:SetActive(self.effect_view_type == 0)
	self.node_list.po:SetActive(self.effect_view_type == 0)
	-- MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.ChangeAddNode, self))
end

function WuHunSkillEffectView:ChangeAddNode()
	if self.node_list["skillshow_panel"] then
		self.obj = self.node_list["skillshow_panel"].gameObject
		if self.effect_view_type == 0 then
			local mainui_ctrl = MainuiWGCtrl.Instance
			local parent = mainui_ctrl:GetMainWuHunUIEffectRoot()
			self.obj.transform:SetParent(parent.gameObject.transform, false)
		else
			self.obj = self.node_list["skillshow_panel"].gameObject
			self.obj.transform:SetParent(self.root_node_transform, false)
		end
	end
end

function WuHunSkillEffectView:ReleaseCallBack()
	self.is_play = false
	self.hit_number = 0
	self.skill_show_list = {}

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	self:RemoveEffectDelayTimer()
end

function WuHunSkillEffectView:AddEffetData(wuhun_id)
	local data = {}
	data.wuhun_id = wuhun_id
	local wuhun_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(wuhun_id)
	if wuhun_cfg then
		data.asset = wuhun_cfg.asset
		data.bundle = wuhun_cfg.bundle
		data.effect_time = wuhun_cfg.effect_time
		data.subtitles = wuhun_cfg.subtitles
	end

	table.insert(self.skill_show_list, data)

	if self.is_view_loaded and (not self.is_play ) then
		self:StartShowSkillEffet()
	end
end

function WuHunSkillEffectView:StartShowSkillEffet()
	self.is_play = true
	if self.obj then
		self.obj:SetActive(true)
    end

	if #self.skill_show_list >= 1 then
		local data = table.remove(self.skill_show_list, 1)
		self:ShowSkillEffet(data)
	else
		self:Close()
	end
end

function WuHunSkillEffectView:ShowSkillEffet(effect_data)
	if effect_data == nil then
		--继续
		self:StartShowSkillEffet()
		return
	end

	if effect_data.bundle == nil or effect_data.bundle == "" or effect_data.asset == nil or effect_data.asset == "" then
		--继续
		self:StartShowSkillEffet()
		return
	end

	---这里显示技能名称啥的
	if effect_data.subtitles ~= nil then
		for i = 1, 4 do
			local str = string.format("a3_wuhun_effect_type_%d_%d", effect_data.subtitles, i)
			local node = self.node_list[string.format("skill_effect_image_0%d", i)]
			
			if node then
				node:CustomSetActive(true)
				node.canvas_group.alpha = 0
				node.raw_image:LoadSprite(ResPath.GetWuHunSkillTexture(str))
			end
		end
	end
	-- local tex_bundle_name, tex_asset_name = ResPath.GetWuHunSkillTexture(effect_data.subtitles)
	-- self.node_list.skill_bg:CustomSetActive(true)
	-- self.node_list.skill_bg.raw_image:LoadSprite(tex_bundle_name, tex_asset_name)
    ---UI特效
	local bundle_name = effect_data.bundle
	local asset_name = effect_data.asset
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_root"].transform, 2.8)
end

--移除回调
function WuHunSkillEffectView:RemoveEffectDelayTimer()
    if self.show_effect_timer then
        GlobalTimerQuest:CancelQuest(self.show_effect_timer)
        self.show_effect_timer = nil
    end
end

function WuHunSkillEffectView:OnFlush(param_t, index)
	self:RefreshHitNumber()
end

--延迟两秒关闭
function WuHunSkillEffectView:TriggerClose()
	-- self:Close()
	self.is_play = false
	self:RemoveEffectDelayTimer()

	if self.is_view_loaded and self.node_list then
		for i = 1, 4 do
			local node = self.node_list[string.format("skill_effect_image_0%d", i)]
			
			if node then
				node:CustomSetActive(false)
			end
		end
	end

	self.show_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 1)
end


function WuHunSkillEffectView:NotifyHit()
	self.hit_number = self.hit_number + 1
	self:RefreshHitNumber()
end

function WuHunSkillEffectView:RefreshHitNumber()
	if self.is_view_loaded and self.is_play then
		self.node_list.hit_number.text.text = tostring(self.hit_number)
		UITween.ScaleShowPanel(self.node_list["hit_number"], u3dpool.vec3(1.5, 1.5, 1.5), 0.3, nil ,nil, u3dpool.vec3(1, 1, 1))
	end
end

