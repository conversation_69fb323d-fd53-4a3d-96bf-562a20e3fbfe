-- 日常系统
ActivityMainView = ActivityMainView or BaseClass(SafeBaseView)

function ActivityMainView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/activity_ui_prefab", "ActivityMainPanel")
end

function ActivityMainView:__delete()

end

function ActivityMainView:ReleaseCallBack()
	if self.notify_flush then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.notify_flush)
		self.notify_flush = nil
	end

	if self.page_list then
		self.page_list:DeleteMe()
		self.page_list = nil
	end

	if self.btn_cell_list then
		for i, v in ipairs(self.btn_cell_list) do
			v:DeleteMe()
		end
		self.btn_cell_list = {}
	end

	self.open_activity = {}
	ActivityWGData.Instance:SetHideCallBack(nil)
end

function ActivityMainView:LoadCallBack()
	self.notify_flush = BindTool.Bind(self.GetAllActivityStates,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.notify_flush)
	self.mask_bg.image.color = Color.New(0,0,0,0)
	self.open_activity = {}
	self.btn_cell_list = {}

	local list_delegate = self.node_list.list_view.page_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.BtnGetNumberOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.BtnReFreshCell, self)
	ActivityWGData.Instance:SetHideCallBack(BindTool.Bind(self.GetAllActivityStates,self))
	self:GetAllActivityStates()
end

function ActivityMainView:GetAllActivityStates()
	if not self:IsOpen() then return end
	self.open_activity = ActivityWGData.Instance:GetHideActivityInfo()

	self.node_list.list_view.list_view:Reload(function ()
		self.node_list["list_view"].list_page_scroll2:JumpToPage(0)
	end)
end

--获取表中的第n个数据
function ActivityMainView:GetInfoByIndex( index )
	local i = 1
	for k,v in pairs(self.open_activity) do
		if i == index then
			return v
		end
		i = i + 1
	end
	return nil
end

function ActivityMainView:BtnGetNumberOfCells()
	-- return BTN_MAX_NUM
	return GetTableLen(self.open_activity)
end

function ActivityMainView:BtnReFreshCell(index,cellObj)
	index = index + 1

	-- local page = math.floor(index / BTN_PAGE_COUNT)
	-- local cur_colunm = math.floor(index / BTN_ROW) + 1 - page * BTN_COL
	-- local cur_row = math.floor(index % BTN_ROW) + 1
	-- local grid_index = (cur_row - 1) * BTN_COL - 1 + cur_colunm  + page * BTN_ROW * BTN_COL

	local data = self:GetInfoByIndex(index)
	if data then
	local act_cfg = data.act_cfg
		if act_cfg then
			local cell = self.btn_cell_list[cellObj]
			if not cell then
				cell = ActivityMainBtn.New(cellObj)
				cell:HideSelf(false)
				self.btn_cell_list[cellObj] = cell
			end
			cell:SetSprite(act_cfg.res_name, act_cfg.act_name_res)
			cell:SetData(act_cfg.act_type)
			cell:AddClickEventListener(BindTool.Bind(self.OnClickActIcon,self,act_cfg.act_type))
			cell:HideSelf(true)
		else
			local cell = self.btn_cell_list[cellObj]
			if cell then
				cell:HideSelf(false)
			end
		end
	else
		local cell = self.btn_cell_list[cellObj]
		if cell then
			cell:HideSelf(false)
		end
	end

end

--按钮点击事件
function ActivityMainView:OnClickActIcon( act_type )
	if not act_type then
		print_error("can not find the param of act_type")
		return
	end
	--完美情人入后有变
	if act_type==ACTIVITY_TYPE.RAND_ACTIVITY_PERFECTLOVER then
		if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY) then
			print_error('当前开服活动未开启,请不要用GM打开该活动')
			return
		end
		--自定义按钮索引
		ServerActivityWGCtrl.Instance.act_openserver_view:OnClickBntIndex(3)
		ServerActivityWGCtrl.Instance.act_openserver_view:Open()
		return
	end
	ActivityWGCtrl.Instance:OpenPopView(act_type)
end

----------------------------------------------------button-------------------------------------
ActivityMainBtn = ActivityMainBtn or BaseClass(BaseRender)

function ActivityMainBtn:SetSprite( name )
	self.node_list.Icon.image:LoadSprite(ResPath.GetF2MainUIImage(name))
end

--添加按钮点击事件
function ActivityMainBtn:AddClickEventListener( callback )
	XUI.AddClickEventListener(self.node_list.ActivityButtonView,callback)
end

function ActivityMainBtn:SetBtnEnable( enable )
	self.node_list.ActivityButtonView.canvas_group.blocksRaycasts = enable
	self.node_list.ActivityButtonView.button.interactable = enable
end

--设置按钮名字
function ActivityMainBtn:SetBtnName( str )
	self.node_list.Text.text.text = str
end

function ActivityMainBtn:HideSelf(enable)
	self.node_list.ActivityButtonView:SetActive(enable)
end
