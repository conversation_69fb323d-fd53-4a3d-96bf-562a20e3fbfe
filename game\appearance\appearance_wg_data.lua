AppearanceWGData = AppearanceWGData or BaseClass()

function AppearanceWGData:__init()
    if nil ~= AppearanceWGData.Instance then
        ErrorLog("[AppearanceWGData]:Attempt to create singleton twice!")
    end

    AppearanceWGData.Instance = self
    self.record_open_new_view_state = false
end

function AppearanceWGData:__delete()
    AppearanceWGData.Instance = nil
end

function AppearanceWGData:GetCurDressFashionResId()
    local info = GameVoManager.Instance:GetMainRoleVo()
    local shizhuang_part_list = info.shizhuang_part_list
    if shizhuang_part_list then
        return shizhuang_part_list[2].use_index, shizhuang_part_list[1].use_index
    end
    return 0, 0
end

function AppearanceWGData:GetCurAppearanceShenBing()
    local info = GameVoManager.Instance:GetMainRoleVo()
    if info and info.appearance.shenwu_appeid then
        return info.appearance.shenwu_appeid
    end
end

--获取当前角色的模型id
function AppearanceWGData:GetRoleResId(is_get_defult)
    local info = GameVoManager.Instance:GetMainRoleVo()
    if not info then return end

    local prof = info.prof
    local sex = info.sex
    if not prof or not sex then return end

    local role_res_id = 0
    local weapon_res_id = 0
    -- 先查找时装的武器和衣服
    local appearance = info.appearance
    if appearance == nil then
        local shizhuang_part_list = info.shizhuang_part_list
        if shizhuang_part_list then
            appearance = {fashion_body = shizhuang_part_list[2].use_index, fashion_wuqi = shizhuang_part_list[1].use_index}
        end
    end

    if appearance ~= nil then
        if nil ~= info.shenwu_appeid and 0 ~= info.shenwu_appeid then
            weapon_res_id = info.shenwu_appeid
        elseif appearance.fashion_wuqi ~= 0 then
            weapon_res_id = info.fashion_wuqi
        end

        weapon_res_id = weapon_res_id ~= 0
                                        and RoleWGData.GetFashionWeaponId(nil, nil, weapon_res_id)
                                        or RoleWGData.GetJobWeaponId(sex, prof)
        if appearance.fashion_body ~= 0 then
            role_res_id = ResPath.GetFashionModelId(prof,appearance.fashion_body)
        end
    end

    -- 最后查找职业表
    if role_res_id == 0 or is_get_defult then
        role_res_id = RoleWGData.GetJobModelId(sex, prof)
    end

    if weapon_res_id == 0 then
        weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
    end

    if role_res_id == 0 or is_get_defult then
        role_res_id = 1101001
    end
    if weapon_res_id == 0 then
        weapon_res_id = 950100101
    end

    return role_res_id, weapon_res_id
end

--获取灵童基础武器模型ID
function AppearanceWGData.GetLingTongBaseWeaponModelID()
    local upgrade_config = ConfigManager.Instance:GetAutoConfig("upgrade_auto").image
    if upgrade_config ~= nil then
        for k,v in pairs(upgrade_config) do
            if v.type == 3 then
                return v.appe_image_id
            end
        end
    end
    return 0
end

--获取时装衣服模型+武器
function AppearanceWGData.GetFashionBodyResIdByResViewId(res_view_id, sex, prof)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    sex = sex or main_role_vo.sex
    prof = prof or main_role_vo.prof
    local res_id = ResPath.GetFashionModelId(prof, res_view_id)
    local weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, res_view_id)
    return res_id, weapon_res_id
end

--获取时装模型中武器模型的资源
function AppearanceWGData.GetFashionWeaponIdByResViewId(res_view_id, sex, prof)
    local weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, res_view_id)
    return weapon_res_id
end

--时装相关 恭喜获得界面通过模型id获取配置
function AppearanceWGData:GetFashionCfgByTypeAndImageId(body_show_type, index)
    local part_type = ROLE_APPE_2_SHIZHUANG_TYPE[body_show_type]
    local attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(part_type, index)
    if attr_cfg then
        local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index)
        local grade_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(part_type, index, 1)

        return attr_cfg, grade_cfg, (fashion_cfg and fashion_cfg.name or "")
    end

    return nil, nil, ""
end

-- 恭喜获得数据
--根据形象和类型获取数据
function AppearanceWGData:GetNewAppearanceCfg(body_show_type, appe_image_id, index_param)
    if body_show_type == nil then
        print_error("can not receive the model type")
        return
    end

    local param = self:CheckNormalModel(body_show_type, appe_image_id)
    if param then
        return param
    end

    param = {}
    local is_new_attr_type = false
    local attr_cfg = {}
    local cap_cfg = nil
    local grade_cfg = {}
    local item_id = 0
    param.other_asset = {} -- 保存其它部件
    param.appe_image_id = appe_image_id
    param.type = body_show_type
    if body_show_type == ROLE_APPE_TYPE.LINGCHONG then                         -- 灵宠特殊形象
        param.path = ResPath.GetPetModel
        attr_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByAppeId(appe_image_id)
        item_id = attr_cfg and attr_cfg.active_item_id or 0
        param.name = attr_cfg and attr_cfg.image_name or ""
        if attr_cfg then
            local map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, attr_cfg.image_id, 1)
            cap_cfg = map_attr_cfg
        end
    elseif body_show_type == ROLE_APPE_TYPE.MOUNT then                         -- 坐骑特殊形象
        param.path = ResPath.GetMountModel
        attr_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(index_param)
        item_id = attr_cfg and attr_cfg.active_item_id or 0
        param.name = attr_cfg and attr_cfg.image_name or ""
        if attr_cfg then
            local map_attr_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, attr_cfg.image_id, 1)
            cap_cfg = map_attr_cfg
        end
    elseif body_show_type == ROLE_APPE_TYPE.HUAKUN then                         -- 化鲲
        param.path = ResPath.GetMountModel
        local kun_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(appe_image_id)
        if not IsEmptyTable(kun_cfg) then
            param.appe_image_id = kun_cfg.active_id
            param.name = kun_cfg.name
            local level_cfg = NewAppearanceWGData.Instance:GetKunUpGradeCfg(appe_image_id, 1)
	        local star_cfg = NewAppearanceWGData.Instance:GetKunUpStarAttrCfg(appe_image_id, 1)
            local mix_list = AttributeMgr.AddAttributeAttr(star_cfg, level_cfg)
            cap_cfg = mix_list 
            item_id = kun_cfg and kun_cfg.active_need_item_id or 0
        end
        attr_cfg = NewAppearanceWGData.Instance:GetKunUpStarAttrCfg(appe_image_id, 1)
        if not IsEmptyTable(attr_cfg) then
            attr_cfg.index = kun_cfg.id
        end
    elseif body_show_type == ROLE_APPE_TYPE.WING then                          -- 羽翼特殊形象
        param.path = ResPath.GetWingModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.FABAO then                         -- 法宝
        param.path = ResPath.GetFaBaoModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.SHENBING then                      -- 武器/神兵
        param.path = ResPath.GetRoleModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.LINGGONG then                      -- 灵弓
        param.path = ResPath.GetSoulBoyWeaponModel2
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.JIANZHEN then                         -- 剑阵
        param.path = ResPath.GetJianZhenModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.FASHION then                       -- 时装
        param.path = ResPath.GetRoleModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.FOOT then                          -- 足迹
        param.path = ResPath.GetRoleModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.HALO then                          -- 光环
        param.path = ResPath.GetHaloModel
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.FACE then                          -- 脸
        param.path = ResPath.GetRoleModel
        param.other_asset.mask_res_id  = appe_image_id
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.YAO then                          -- 腰
        param.path = ResPath.GetRoleModel
        param.other_asset.belt_res_id  = appe_image_id
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.WEI then                          -- 尾
        param.path = ResPath.GetRoleModel
        param.other_asset.weiba_res_id  = appe_image_id
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.HAND then                          -- 手
        param.path = ResPath.GetRoleModel
        param.other_asset.shouhuan_res_id  = appe_image_id
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.PHOTO then                          -- 相框
        param.path = ResPath.GetPhotoFrame
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.BUBBLE then                          -- 气泡
        param.path = ResPath.GetBubble
        attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(body_show_type, index_param)
        cap_cfg = grade_cfg
        item_id = attr_cfg and attr_cfg.stuff_id or 0
    elseif body_show_type == ROLE_APPE_TYPE.LINGCHONG_NORMAL then              -- 普通形象灵宠
        param.path = ResPath.GetPetModel
        attr_cfg = NewAppearanceWGData.Instance:GetLingChongBaseUpStarCfgByAppeId(appe_image_id, 1)
        cap_cfg = attr_cfg
        if attr_cfg then
            param.name = attr_cfg.lingchong_name
        end
    elseif body_show_type == ROLE_APPE_TYPE.MOUNT_NORMAL then                    -- 进阶坐骑
        param.path = ResPath.GetMountModel
        attr_cfg = NewAppearanceWGData.Instance:GetMountBaseUpStarCfgByAppeId(appe_image_id, 1)
        cap_cfg = attr_cfg
        if attr_cfg then
            param.name = attr_cfg.mount_name
        end

    elseif body_show_type == ROLE_APPE_TYPE.BABY then                          -- 宝宝
        param.path = ResPath.GetHaiZiModel
        attr_cfg = MarryWGData.Instance:GetBabyCfgByImageId(appe_image_id)
        if attr_cfg then
            param.name = attr_cfg.baby_name
            cap_cfg = MarryWGData.Instance:GetBabyAttrCfg(attr_cfg.type, attr_cfg.id, 0)
            item_id = attr_cfg and attr_cfg.item_id or 0
        end
    elseif body_show_type == ROLE_APPE_TYPE.BIANSHEN then                           -- 变身
        param.path = ResPath.GetBianShenModel
        attr_cfg = TianShenWGData.Instance:GetImageModelByAppeId(appe_image_id)
        if attr_cfg then
            param.name = attr_cfg.bianshen_name
            param.bianshen_name_res = attr_cfg.bianshen_name_res
            local bundle,asset = TianShenWGData.Instance:GetTianShenWeapon(attr_cfg.index)

            param.other_asset.bundle = bundle
            param.other_asset.asset = asset
            local ts_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(attr_cfg.index)
            item_id = ts_item_cfg and ts_item_cfg.act_item_id or 0
        end
        -- is_new_attr_type = true

    elseif body_show_type == ROLE_APPE_TYPE.IMP then                           -- 小鬼
        param.path = ResPath.GetGuardModel
        attr_cfg = EquipmentWGData.GetXiaoGuiCfg(appe_image_id)
        if attr_cfg then
            param.appe_image_id = attr_cfg.appe_image_id
            local item_cfg = ItemWGData.Instance:GetItemConfig(attr_cfg.item_id)
            if item_cfg then
                param.name = item_cfg.name
            end

            cap_cfg = attr_cfg
            param.appe_image_id = attr_cfg.appe_image_id
        end

    elseif body_show_type == ROLE_APPE_TYPE.TIANSHENSHENQI then                           -- 天神神器
        param.path = ResPath.GetTianShenShenQiPath
        local cfg = TianShenWGData.Instance:GetShenQiByIndex(appe_image_id)
        if cfg then
            param.appe_image_id = cfg.res_id1
            attr_cfg = TianShenWGData.Instance:GetShenQiStrengthCfg(appe_image_id, 0)
            param.name = cfg.name
            param.position = cfg.get_position
            param.rotation = cfg.set_rotation
            param.scale = cfg.set_scale
            item_id = cfg.stuff_id or 0
        end
    elseif body_show_type == ROLE_APPE_TYPE.TIANSHENSHENQIHUANHUA then                           -- 天神神器幻化
        param.path = ResPath.GetTianShenShenQiFashionPath
        local cfg = TianShenWGData.Instance:GetWaiGuanCfg(appe_image_id)
        if cfg then
            param.appe_image_id = cfg.res_id1
            --attr_cfg = TianShenWGData.Instance:GetWaiGuanCfg(appe_image_id)
            attr_cfg = cfg
            param.name = cfg.name
            param.position = cfg.facade_getposition
            param.rotation = cfg.facade_getrotation
            param.scale = cfg.facade_getscale
            item_id = cfg.stuff_id or 0
        end
    elseif body_show_type == ROLE_APPE_TYPE.TITLE then                           -- 称号
        param.path = ResPath.GetEffectUi
        local title_cfg = TitleWGData.GetTitleConfig(appe_image_id)
        if title_cfg then
            grade_cfg = title_cfg
            param.name = title_cfg.name
            item_id = title_cfg.item_id or 0
        end
    elseif body_show_type == ROLE_APPE_TYPE.RELIC_TYPE then                       -- 圣器
        local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(appe_image_id)
        param.name = relic_cfg.name
        param.path = ResPath.GetRelicModel
    elseif body_show_type == ROLE_APPE_TYPE.GOD_BODY then                       -- 神体
        param.path = ResPath.GetFairyLandGodBodyModel
        param.name = string.format(Language.FairyLandEquipment.GodBody2, CommonDataManager.GetDaXie(appe_image_id + 1))
    elseif body_show_type == ROLE_APPE_TYPE.ZHIZUN then
        attr_cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(appe_image_id)
        local attr_list = SupremeFieldsWGData.Instance:FootLightGetNewAttrList(appe_image_id)
        param.name = attr_cfg.name
        param.zhizun_type_index  = appe_image_id
        cap_cfg = attr_list
    elseif body_show_type == ROLE_APPE_TYPE.CANGMING then
        attr_cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(appe_image_id)
        local attr_list = FiveElementsWGData.Instance:WaistLightGetNewAttrList(appe_image_id)
        param.name = attr_cfg.name
        cap_cfg = attr_list
    elseif body_show_type == ROLE_APPE_TYPE.WU_HUN_ZHEN_SHEN then
        param.path = ResPath.GetWuHunModel
        local cur_cfg = WuHunWGData.Instance:GetWuHunLevelCfg(index_param, 0)
        local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(index_param)
        param.name = active_cfg and active_cfg.wh_name or ""
        local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, nil, "attr_id", "attr_value", 1, 5)
        attr_list = WuHunWGData.Instance:ConvertList(attr_list)
        cap_cfg = attr_list
    elseif body_show_type == ROLE_APPE_TYPE.NEW_FIGHT_MOUNT then
        param.path = ResPath.GetMountModel
        local mount_cfg = NewFightMountWGData.Instance:GetUpgradeLevelCfg(appe_image_id, 1)
        local item_cfg = ItemWGData.Instance:GetItemConfig(mount_cfg.cost_item_id)
        if item_cfg then
            param.name = item_cfg.name
        end

        local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(mount_cfg, nil, "attr_id", "attr_value", 1, 5)
        attr_list = EquipWGData.GetConvertAttrList(attr_list)
        cap_cfg = attr_list
    elseif body_show_type == ROLE_APPE_TYPE.BEAST then
        param.path = ResPath.GetBeastsModel
        param.beast_data = index_param
        if index_param and index_param.server_data then
            local item_cfg = ItemWGData.Instance:GetItemConfig(index_param.server_data.beast_id)
            if item_cfg then
                param.name = item_cfg.name
            end
        end

        local cfg = ControlBeastsWGData.Instance:GetBeastLevelCfgByLevel(1)
        local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cfg, nil, "attr_id", "attr_value", 1, 6)
        attr_list = EquipWGData.GetConvertAttrList(attr_list)
        cap_cfg = attr_list 
    elseif body_show_type == ROLE_APPE_TYPE.GOD_OR_DEMON then
        local appe_image_cfg = YinianMagicWGData.Instance:GetAppeImageCfg(appe_image_id)
        local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(appe_image_cfg.type, appe_image_cfg.min_grade) 
        local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, nil, "attr_id", "attr_value", 1, 5)
        cap_cfg = attr_list
    elseif body_show_type == ROLE_APPE_TYPE.BIANSHEN_XIUWEI then
        local type_name = Language.NewAppearance.NuQiType[appe_image_id]
        param.name = string.format(Language.NewAppearance.NuQiTypeName, type_name)
    elseif body_show_type == ROLE_APPE_TYPE.BEAST_SKIN then
        param.path = ResPath.GetBeastsModel
        cap_cfg = ControlBeastsWGData.Instance:GetSkinAttrList(index_param, 1)
        local skin_cfg = ControlBeastsWGData.Instance:GetBeastsSkinCfgBySeq(index_param)
        if skin_cfg then
            param.name = skin_cfg.skin_name
        end
    elseif body_show_type == ROLE_APPE_TYPE.THUNDER_MANA then
        local suit_cfg =  ThunderManaWGData.Instance:GetThunderSuitCfg(appe_image_id, index_param)
        if suit_cfg then
            param.path = ResPath.GetHaloModel
            param.thunder_suit_cfg = suit_cfg
           
            attr_cfg, grade_cfg, param.name = self:GetFashionCfgByTypeAndImageId(ROLE_APPE_TYPE.HALO, suit_cfg.halo_index)
            local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(ROLE_APPE_2_SHIZHUANG_TYPE[ROLE_APPE_TYPE.HALO], suit_cfg.halo_index)
            param.appe_image_id = fashion_cfg and fashion_cfg.resouce or 0
            cap_cfg = grade_cfg
            item_id = attr_cfg and attr_cfg.stuff_id or 0
        end
    elseif body_show_type == ROLE_APPE_TYPE.ESOTERICA then
        local esoterica_cfg = CultivationWGData.Instance:GetEsotericaCfg(appe_image_id)

        if esoterica_cfg then
            param.name = esoterica_cfg.name
            param.esoterica_cfg = esoterica_cfg
        end
    end

    param.cfg = attr_cfg
    param.cap_cfg = cap_cfg or attr_cfg                     -- 用于显示战力的配置
    param.grade_cfg = grade_cfg or {}
    param.is_new_attr_type = is_new_attr_type   --是否为新的属性表示方法：attr_type_1, attr_value_1
    param.item_id = item_id
    param.index_param = index_param
    return param
end

function AppearanceWGData:CheckNormalModel(body_show_type, appe_image_id)
    local param = {}
    param.other_asset = {} -- 保存其它部件
    param.type = body_show_type
    param.appe_image_id = appe_image_id
    local image_cfg = ConfigManager.Instance:GetAutoConfig("upgrade_auto").image
    local prof = RoleWGData.Instance:GetRoleProf()
    for k,v in pairs(image_cfg) do
        if v.model_type == body_show_type and v.appe_image_id == appe_image_id and (v.prof == 5 or v.prof == prof) then
            param.name = v.weapon_name
            if v.type == ADVANCED_DATA_TYPE.WING then
                param.path = ResPath.GetWingModel
                param.cap_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(v.type, 1)
            elseif v.type == ADVANCED_DATA_TYPE.FABAO then
                param.path = ResPath.GetFaBaoModel
                param.cap_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(v.type, 1)
            elseif v.type == ADVANCED_DATA_TYPE.SHENWU then
                param.path = ResPath.GetRoleModel
                param.cap_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(v.type, 1)
            elseif v.type == ADVANCED_DATA_TYPE.JIANZHEN then
                param.path = ResPath.GetJianZhenModel
                param.cap_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(v.type, 1)
            end

            param.special_type = v.type
            param.cfg = v

            return param
        end
    end
    return nil
end

--通过item_id判断是不是属于时装/灵宠/幻装模块
function AppearanceWGData.GetIsAppearanceByItemId(item_id)
    local part_type = NewAppearanceWGData.Instance:GetFashionTypeByItemId(item_id)
    if part_type >= 0 then
        return true
    end

    local qc_type = NewAppearanceWGData.Instance:GetQiChongTypeByItemId(item_id)
    if qc_type >= 0 then
        return true
    end

    local baby_type = MarryWGData.Instance:GetBabyTypeItemId(item_id)
    if baby_type >= 0 then
        return true
    end

    return false
end

function AppearanceWGData:SetOpenGetNewViewState(bool)
    self.record_open_new_view_state = bool
end

function AppearanceWGData:GetOpenGetNewViewState()
    return self.record_open_new_view_state
end