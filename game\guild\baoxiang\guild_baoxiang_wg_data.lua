------------每日宝箱------------------
GuildBaoXiangWGData = GuildBaoXiangWGData or BaseClass()

function GuildBaoXiangWGData:__init()
	if GuildBaoXiangWGData.Instance then
		ErrorLog("[GuildBaoXiangWGData]:Attempt to create singleton twice!")
	end
	GuildBaoXiangWGData.Instance = self

	local daily_treasure_cfg = ConfigManager.Instance:GetAutoConfig("daily_treasure_cfg_auto")
	self.other_cfg = daily_treasure_cfg.other[1]
	self.treasure_cfg = ListToMap(daily_treasure_cfg.treasure,"quality")

	RemindManager.Instance:Register(RemindName.BiZuoBaoXiangRemind, BindTool.Bind(self.IsShowBaoXiangRemind, self))--祈福

	self.treaure_all_item_info_list = {}
	self.treaure_all_uid_item_info_list = {}
	self.req_help_refresh_uid_list = {}
	self.be_req_help_refresh_uid_list = {}
	self.req_help_refresh_uid_list_id = {}
	self.be_req_help_refresh_uid_lis_id = {}
	self.baoxiang_refresh_data = {}
	self.baoxiang_thank_data = {}
    self.daily_treasure_info = {}
    self.daily_treasure_record_info_list = {}
	self.baoxiang_first_login = true
end

function GuildBaoXiangWGData:__delete()
	GuildBaoXiangWGData.Instance = nil
end

function GuildBaoXiangWGData:IsShowBaoXiangRemind()
	local info = self:GetDailyTreasureInfo()
	local is_open = FunOpen.Instance:GetFunIsOpened("guild_baoxiang")
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local guild_id = role_vo.guild_id
	if info and info.open_tag == 0 and is_open and guild_id > 0 and self.baoxiang_first_login then
		self.baoxiang_first_login = false
		return 1
	else
		return 0
	end
end

function GuildBaoXiangWGData:SetBaoXiangRemind(value)
	local is_open = FunOpen.Instance:GetFunIsOpened("guild_baoxiang")
	if not is_open then return end
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_vo = RoleWGData.Instance.role_vo
	local key = "guild_baoxiang_remind_key" .. day .. role_vo.role_id
	local value = PlayerPrefsUtil.SetInt(key,value)
	RemindManager.Instance:Fire(RemindName.BiZuoBaoXiangRemind)
end

function GuildBaoXiangWGData:GetBaoXiangRemind()
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_vo = RoleWGData.Instance.role_vo
	local key = "guild_baoxiang_remind_key" .. day .. role_vo.role_id
	local value = PlayerPrefsUtil.GetInt(key)
	if value == 0 then
		return true
	end
	return false
end

function GuildBaoXiangWGData:GetOtherCfg()
	return self.other_cfg
end

function GuildBaoXiangWGData:GetTreasureCfg()
	return self.treasure_cfg
end

function GuildBaoXiangWGData:GetTreasureCfgByQuality(quality)
	return self.treasure_cfg[quality]
end

function GuildBaoXiangWGData:GetMaxQuality()
	local cfg = self.treasure_cfg[#self.treasure_cfg]
	return cfg and cfg.quality or 4
end

--每日宝箱帮助刷新次数
function GuildBaoXiangWGData:SetDailyTreaureHelpRefreshTimes(timer)
	self.daily_treaure_help_refresh_tiems = timer
end

function GuildBaoXiangWGData:GetDailyTreaureHelpRefreshTimes()
	return self.daily_treaure_help_refresh_tiems or 0
end

function GuildBaoXiangWGData:SetSCDailyTreasureInfo(protocol)
	self.daily_treasure_info = {}
	self.daily_treasure_info.quality = protocol.quality
	self.daily_treasure_info.help_tag = protocol.help_tag
	self.daily_treasure_info.password_bit = protocol.password_bit
	self.daily_treasure_info.open_tag = protocol.open_tag
	self.daily_treasure_info.help_refresh_times = protocol.help_refresh_times
	self.daily_treasure_info.unlock_tag = protocol.unlock_tag
	self.daily_treasure_info.help_uid_list = protocol.help_uid_list
	self.daily_treasure_info.be_help_uid_list = protocol.be_help_uid_list
	local password = protocol.password
	local treasure_cfg = self:GetTreasureCfgByQuality(protocol.quality)
	local cfg_password_bit = treasure_cfg and treasure_cfg.password_bit or 5
	local password_list = {}
	local num = 1
	for i= cfg_password_bit, 1, -1 do
		password_list[num] = IntgerAnalyze(password, i)
		num = num + 1
	end
	self.daily_treasure_info.password = password
	self.daily_treasure_info.password_list = password_list
end

function GuildBaoXiangWGData:SetSCDailyTreasureAllyItemInfo(protocol)
	self.treaure_all_item_info_list = protocol.item_list
	self.treaure_all_uid_item_info_list = protocol.uid_item_list
end

function GuildBaoXiangWGData:SetSCDailyTreasureAllyItemUpdate(protocol)
	local item = protocol.item
	if item.uid <= 0 or item.name == "" then 
		local log = "单个数据变化时下发空的数据:" .. TableToChatStr(item,1) .. "\n"
		self:AddPrintLog(log)
		return 
	end
	local uid = item.uid
	local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local is_main_role = uid == main_role_id
	if is_main_role then return end
	local is_has = false
	if not IsEmptyTable(self.treaure_all_item_info_list) then 
		for k,v in pairs(self.treaure_all_item_info_list) do
			if v.uid == uid then
				self.treaure_all_item_info_list[k] = item
				is_has = true
				break
			end
		end
	end
	if not is_has then
		self.treaure_all_item_info_list[#self.treaure_all_item_info_list+1] = item
	end
	self.treaure_all_uid_item_info_list[uid] = item
end

function GuildBaoXiangWGData:SetSCDailyTreasureRecordInfo(protocol)
	self.daily_treasure_record_info_list = protocol.item_list
	table.sort(self.daily_treasure_record_info_list, SortTools.KeyUpperSorters("record_time"))
end

function GuildBaoXiangWGData:SetSCDailyTreasureRecordAdd(protocol)
	local item = protocol.item

	table.insert(self.daily_treasure_record_info_list, item)
	table.sort(self.daily_treasure_record_info_list, SortTools.KeyUpperSorters("record_time"))
end

function GuildBaoXiangWGData:SetSCDailyTreasureAllyItemRemove(protocol)
	-- local uid = protocol.uid
	-- local is_main_role = RoleWGData.Instance:GetIsMainRole(uid)
	-- if is_main_role then return end
end

function GuildBaoXiangWGData:SetSCDailyTreasureReqHelpRefreshInfo(protocol)
	self.req_help_refresh_uid_list = protocol.req_uid_list
	self.req_help_refresh_uid_list_id = protocol.req_uid_list_id
end

function GuildBaoXiangWGData:SetSCDailyTreasureReqHelpRefreshUpdate(protocol)
	local is_add = protocol.is_add
	local uid = protocol.uid
	if is_add == 1 then
		if not self.req_help_refresh_uid_list_id[uid] then
			table.insert(self.req_help_refresh_uid_list,uid)
			self.req_help_refresh_uid_list_id[uid] = uid
		end
	else
		for k,v in ipairs(self.req_help_refresh_uid_list) do
			if v == uid then
				table.remove(self.req_help_refresh_uid_list,k)
			end
		end
		self.req_help_refresh_uid_list_id[uid] = nil
	end
end

function GuildBaoXiangWGData:SetSCDailyTreasureBeReqHelpRefreshInfo(protocol)
	self.be_req_help_refresh_uid_list = protocol.be_req_uid_list
	self.be_req_help_refresh_uid_lis_id = protocol.be_req_uid_list_id
end

function GuildBaoXiangWGData:SetSCDailyTreasureBeReqHelpRefreshUpdate(protocol)
	local is_add = protocol.is_add
	local uid = protocol.uid
	if is_add == 1 then
		if not self.be_req_help_refresh_uid_lis_id[uid] then
			table.insert(self.be_req_help_refresh_uid_list,uid)
			self.be_req_help_refresh_uid_lis_id[uid] = uid
		end
	else

		for k,v in ipairs(self.be_req_help_refresh_uid_list) do
			if v == uid then
				table.remove(self.be_req_help_refresh_uid_list,k)
			end
		end
		self.be_req_help_refresh_uid_lis_id[uid] = nil
	end
end

function GuildBaoXiangWGData:RemoveBeReqHelpRefreshUid(uid)
	for k,v in ipairs(self.be_req_help_refresh_uid_list) do
		if v == uid then
			table.remove(self.be_req_help_refresh_uid_list,k)
		end
	end
	self.be_req_help_refresh_uid_lis_id[uid] = nil
end

--退出仙盟求助信息清掉
function GuildBaoXiangWGData:ClearBeReqHelpRefresData()
	self.be_req_help_refresh_uid_lis_id = {}
	self.be_req_help_refresh_uid_list = {}
end

function GuildBaoXiangWGData:GetDailyTreasureInfo()
	return self.daily_treasure_info
end

function GuildBaoXiangWGData:GetDailyAllItemInfoList()
	return self.treaure_all_item_info_list
end


function GuildBaoXiangWGData:GetShowDailyAllItemInfoList()
	local data_list = {}
	local be_help_uid_list = self.daily_treasure_info.be_help_uid_list or {}
	local other_cfg = self:GetOtherCfg()
	local max_num = other_cfg.help_refresh_times
	for k,v in pairs(self.treaure_all_item_info_list) do
		local has_num = max_num - v.help_refresh_times
		local has_help = be_help_uid_list[v.uid] and true or false		--已帮我刷新
		local has_num_flag = has_num > 0 and 1 or 0
		local can_help = has_num > 0 and not has_help and 1 or 0
		local has_id = GuildBaoXiangWGData.Instance:GetReqHelpRefreshUidListId(v.uid)
		local yiqiuzhu = (has_id ~= nil and has_num > 0 and not has_help) and 0 or 1
		if v.fortune_type > 0 then
			local data = {}
			data.info = v 
			data.fortune_type = v.fortune_type
			data.has_num = has_num
			data.has_num_flag = has_num_flag
			data.has_help = has_help
			data.can_help = can_help
			data.yiqiuzhu = yiqiuzhu
			table.insert(data_list,data)
		end
	end
	table.sort(data_list, SortTools.KeyUpperSorters("has_num_flag","can_help","yiqiuzhu","fortune_type","has_num"))
	return data_list
end

function GuildBaoXiangWGData:GetMyHelpUidListByUid(uid)
	local help_uid_list = self.daily_treasure_info.help_uid_list or {}
	return help_uid_list[uid] and true or false
end

function GuildBaoXiangWGData:GetMyBeHelpUidListByUid(uid)
	local be_help_uid_list = self.daily_treasure_info.be_help_uid_list or {}
	return be_help_uid_list[uid] and true or false
end

function GuildBaoXiangWGData:GetDailyTreasureRecordInfoList()

	return self.daily_treasure_record_info_list
end

function GuildBaoXiangWGData:GetAllItemInfoListByUid(uid)
	return self.treaure_all_uid_item_info_list[uid]
end

function GuildBaoXiangWGData:GetReqHelpRefreshUidList()
	return self.req_help_refresh_uid_list
end

function GuildBaoXiangWGData:GetBeReqHelpRefreshUidList()
	local data_list = {}
	for k,v in pairs(self.be_req_help_refresh_uid_list) do
		local uid_info = GuildBaoXiangWGData.Instance:GetAllItemInfoListByUid(v)
		if uid_info then
			local data = {}
			data.uid = v
			data.quality = uid_info.quality
			data.is_refresh = GuildBaoXiangWGData.Instance:GetMyHelpUidListByUid(v) and 1 or 0
			table.insert(data_list,data)
		end
	end

	table.sort(data_list,SortTools.KeyLowerSorters("is_refresh","quality"))
	return data_list
end


function GuildBaoXiangWGData:GetCanBeReqHelpRefreshUidList()
	local data_list = {}
	local max_quality = GuildBaoXiangWGData.Instance:GetMaxQuality()
	for k,v in pairs(self.be_req_help_refresh_uid_list) do
		local uid_info = GuildBaoXiangWGData.Instance:GetAllItemInfoListByUid(v)
		local is_refresh = GuildBaoXiangWGData.Instance:GetMyHelpUidListByUid(v)
		if uid_info and uid_info.quality < max_quality and not is_refresh then
			table.insert(data_list,v)
		end
	end
	return data_list
end

function GuildBaoXiangWGData:GetBeReqHelpRefreshUidListId(uid)
	return self.be_req_help_refresh_uid_lis_id[uid]
end

function GuildBaoXiangWGData:GetReqHelpRefreshUidListId(uid)
	return self.req_help_refresh_uid_list_id[uid]
end

--判断是否已领取了宝箱
function GuildBaoXiangWGData:GetHasOpenTreasure()
	local quality = 0
	if not IsEmptyTable(self.daily_treasure_info) then
		quality = self.daily_treasure_info.quality or 0
	end
	return quality >0
end

---动画跳过标记
function GuildBaoXiangWGData:SetIgnoireAnimFlag(flag)
	self.ignoire_anim_flag = flag
end

function GuildBaoXiangWGData:GetIgnoireAnimFlag()
	return self.ignoire_anim_flag
end

function GuildBaoXiangWGData:SetNeedPlayAnim(flag)
	self.need_paly_anim = flag
end

function GuildBaoXiangWGData:GetNeedPlayAnim()
	return self.need_paly_anim
end

--保存输入的密码
function GuildBaoXiangWGData:SetInputCode(code)
	self.input_code = code
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = "baoxiang_code_key" .. day .. uuid_str
	if code then
		PlayerPrefsUtil.SetString(key,code)
	else
		PlayerPrefsUtil.DeleteKey("baoxiang_code_key" .. day .. uuid_str)
	end
end

function GuildBaoXiangWGData:GetInputCode()
	if self.input_code then
		return self.input_code
	else
		local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local uuid_str = RoleWGData.Instance:GetUUIDStr()
		local key = "baoxiang_code_key" .. day .. uuid_str
		local code = PlayerPrefsUtil.GetString(key)
		if code ~= "" then
			self.input_code = code
		end

		return self.input_code
	end
	
end

function GuildBaoXiangWGData:ClearInputCode()
	self.input_code = nil
end

--保存服务端密码
function GuildBaoXiangWGData:SetPasswordCode(code)
	self.box_password_code = code
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = "box_password_key" .. day .. uuid_str
	PlayerPrefsUtil.SetString(key,code)
end

function GuildBaoXiangWGData:GetPasswordCode()
	if self.box_password_code then
		return self.box_password_code
	else
		local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local uuid_str = RoleWGData.Instance:GetUUIDStr()
		local key = "box_password_key" .. day .. uuid_str
		local code = PlayerPrefsUtil.GetString(key)
		if code ~= "" then
			self.box_password_code = code
		end
		return self.box_password_code
	end
end

function GuildBaoXiangWGData:ClearPasswordCode()
	self.box_password_code = nil
end

function GuildBaoXiangWGData:SetHelpRefreshUid(uid,flag)
	if not self.help_refresh_uid then
		self.help_refresh_uid = {}
	end
	self.help_refresh_uid[uid] = flag
end

function GuildBaoXiangWGData:GetHelpRefreshUid(uid)
	return self.help_refresh_uid and self.help_refresh_uid[uid] or false
end

function GuildBaoXiangWGData:SetSendFlushBtnTime(time)
	self.send_flush_btn_time = time
end

function GuildBaoXiangWGData:GetSendFlushBtnTime()
	return self.send_flush_btn_time or 0
end

function GuildBaoXiangWGData:SetSendFlushChatBtnTime(time)
	self.send_flush_chat_btn_time = time
end

function GuildBaoXiangWGData:GetSendFlushChatBtnTime()
	return self.send_flush_chat_btn_time or 0
end

function GuildBaoXiangWGData:AddBaoXiangRefreshData(data)
	table.insert(self.baoxiang_refresh_data,data)
end

function GuildBaoXiangWGData:GetBaoXiangRefreshData()
	if not IsEmptyTable(self.baoxiang_refresh_data) then
		local data = table.remove(self.baoxiang_refresh_data,1)
		return data
	end
end

function GuildBaoXiangWGData:AddBaoXiangThankData(protocol)
	local data = {}
	data.name = protocol.name
	data.uid = protocol.uid
	data.quality = protocol.quality
	table.insert(self.baoxiang_thank_data,data)
end

function GuildBaoXiangWGData:GetBaoXiangThankData()
	if not IsEmptyTable(self.baoxiang_thank_data) then
		local data = table.remove(self.baoxiang_thank_data,1)
		return data
	end
end

function GuildBaoXiangWGData:SetClickHelpRefreshUid(uid,flag)
	if not self.click_help_refresh_uid then
		self.click_help_refresh_uid = {}
	end
	self.click_help_refresh_uid[uid] = flag
end

function GuildBaoXiangWGData:GetClickHelpRefreshUid(uid)
	return self.click_help_refresh_uid and self.click_help_refresh_uid[uid] or false
end

function GuildBaoXiangWGData:AddTallMyCodeTime(time)
	self.tall_my_code_time = time
end

function GuildBaoXiangWGData:GetTallMyCodeTime()
	return self.tall_my_code_time or 0
end

function GuildBaoXiangWGData:GetPrintLog()
	local tab_str = "GuildBaoXiangWGData:GetPrintLog\n"
	local info_list = self.treaure_all_item_info_list
	local treaure_all_uid_item_info_list = self.treaure_all_uid_item_info_list
	tab_str = tab_str .. "treaure_all_item_info_list:".."\n"
	if not IsEmptyTable(info_list) then
		for k,v in pairs(info_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	tab_str = tab_str .. "treaure_all_uid_item_info_list:".."\n"
	if not IsEmptyTable(treaure_all_uid_item_info_list) then
		for k,v in pairs(treaure_all_uid_item_info_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()

	tab_str = tab_str .. "GetDailyTreasureInfo:".."\n"
	if not IsEmptyTable(info) then
		for k,v in pairs(info) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	local add_print_log_list = self.add_print_log_list
	tab_str = tab_str .. "添加的日志信息:".."\n"
	if not IsEmptyTable(add_print_log_list) then
		for k,v in pairs(add_print_log_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	local data_list = self:GetCanBeReqHelpRefreshUidList()
	tab_str = tab_str .. "请求刷新列表:".."\n"
	if not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end

	return tab_str
end

function GuildBaoXiangWGData:AddPrintLog(str)
	if not self.add_print_log_list then
		self.add_print_log_list = {}
	end
	table.insert(self.add_print_log_list,str)
end

function GuildBaoXiangWGData:GetQiuItemData()
	local qiu_item_data = {}
	for i = 1, 4 do
		local data = {}
		data.num = i
		table.insert(qiu_item_data, data)
	end
	return qiu_item_data
end

