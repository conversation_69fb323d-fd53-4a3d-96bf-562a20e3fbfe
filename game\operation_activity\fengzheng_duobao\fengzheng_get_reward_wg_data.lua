FZGetRewardWGData = FZGetRewardWGData or BaseClass()

function FZGetRewardWGData:__init()
    if FZGetRewardWGData.Instance then
		error("[FZGetRewardWGData] Attempt to create singleton twice!")
		return
	end
	FZGetRewardWGData.Instance = self
    self.info = {}
    self.record = {}
    self.result_flag = {}
    self.result = {}
    self.pool_id = {}
    self.round_index = {}
    self.pool_list = {}
    self.select_layer = nil
    self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind(self.DayChange, self, true))
    OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG, {[1] = OPERATION_EVENT_TYPE.LEVEL, [2] = OPERATION_EVENT_TYPE.DAY},
        BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.ShowRemind, self))
    RemindManager.Instance:Register(RemindName.FZGetReward,BindTool.Bind(self.ShowRemind,self))
    self:LoadFZCfg()
    self:RegisterRewardRemindInBag(RemindName.FZGetReward)
end


function FZGetRewardWGData:LoadFZCfg()
   self.fz_cfg = ConfigManager.Instance:GetAutoConfig("oa_fengzhengduobao_auto")
   --配置参数
   self.fz_param_cfg = self.fz_cfg.config_param
   --活动档位配置
   self.fz_dangwei_cfg = self.fz_cfg.grade
   --奖池配置
   self.fz_reward_pool_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("oa_fengzhengduobao_auto").reward_pool_id, "reward_pool_id","reward_id")
   self.fz_reward_best_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("oa_fengzhengduobao_auto").reward_pool_id, "reward_pool_id","is_best_reward")
   --抽奖消耗
   self.fz_consume_cfg = self.fz_cfg.consume
   --界面配置
   self.fz_interface_cfg = self.fz_cfg.interface

end

--获取当前奖池有几档
function FZGetRewardWGData:GetDangWeiLayerNum()
    local grade = self:GetInterfaceByServerDay()
    local x = 0
    for k,v in pairs(self.fz_dangwei_cfg) do
        if v.grade == grade then
            x = x + 1
        end
    end
    return x
end

function FZGetRewardWGData:SetCurLayer(layer)
    self.select_layer = layer
end

--根据grade和layer获取档位信息
function FZGetRewardWGData:GetDangWeiCfgByGradeAndlayer(layer)
    local grade = self:GetInterfaceByServerDay()
    local cfg = {}
    for k,v in pairs(self.fz_dangwei_cfg) do
        if v.grade == grade and v.layer == layer then
            return v
        end
    end
    return cfg
end

--根据grade获取档位信息
function FZGetRewardWGData:GetDangWeiCfgByGrade()
    local grade = self:GetInterfaceByServerDay()
    local cfg = {}
    for k,v in pairs(self.fz_dangwei_cfg) do
        if v.grade == grade then
           table.insert(cfg,v)
        end
    end
    return cfg
end


--根据grade获取界面配置
function FZGetRewardWGData:GetViewCfgByInterface(index)
    local interface
    if index then
        interface = index
    else
        interface = self:GetDangWeiCfgByGradeAndlayer(self.select_layer).interface
    end
    local cfg = {}
    for k,v in pairs(self.fz_interface_cfg) do
        if v.interface == interface then
            return v
        end
    end
    return cfg
end

--根据服务器开服天数
function FZGetRewardWGData:GetInterfaceByServerDay()
    local open_day = self:GetFZopenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
    for k,v in pairs(self.fz_param_cfg) do
        if open_day >= v.start_server_day and open_day <= v.end_server_day and v.week_index == week then
            return v.grade
        end
    end
    return 0
end


function FZGetRewardWGData:GetOpenLevelByServerDay()
    local open_day = self:GetFZopenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
    for k,v in pairs(self.fz_param_cfg) do
        if open_day >= v.start_server_day and open_day <= v.end_server_day and v.week_index == week then
            return v.open_level
        end
    end
    return 9999 --默认返回9999，说明取不到配置，取不到配置默认不开启
end

function FZGetRewardWGData:GetFZopenServerDay()
    local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG)
    return open_day
end


function FZGetRewardWGData:__delete()
    if self.pass_day_event then
        GlobalEventSystem:UnBind(self.pass_day_event)
        self.pass_day_event = nil
    end
    RemindManager.Instance:UnRegister(RemindName.FZGetReward)
	FZGetRewardWGData.Instance = nil
    self.select_layer = nil
end

function FZGetRewardWGData:RegisterRewardRemindInBag(remind_name)
    local list = self:GetChangeFlushItemList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, list, nil)
end

function FZGetRewardWGData:GetChangeFlushItemList()
    local map = {}
    local item_id_list = {}
    local fz_reward_pool = self:GetDangWeiCfgByGrade()
    if fz_reward_pool and not IsEmptyTable(fz_reward_pool) then
        for k, v in pairs(fz_reward_pool) do
            map[v.consume_item] = true
        end
        for i, j in pairs(map) do
            table.insert(item_id_list, i)
        end
    end
    return item_id_list
end

function FZGetRewardWGData:ShowRemind()
    local fz_reward_pool = self:GetDangWeiCfgByGrade()
    if fz_reward_pool and not IsEmptyTable(fz_reward_pool) then
    	local layer_num = #fz_reward_pool
    	for i = 1, layer_num do
            if self:IsShowBtnDrawRedByLayer(i) then
                return 1
            end
    	end
    end
    return 0
end

function FZGetRewardWGData:IsShowBtnDrawRedByLayer(layer)
    if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG) then
        return false
    end
    if not layer then
        return false
    end
    local is_empty = self:GetIsDrawEmptyByLayer(layer)
    if is_empty then
        return self:HasNextPool(layer)
    else
        local cur_draw_info = self:GetCurDrawInfoByLayer(layer)
        if cur_draw_info and cur_draw_info.draw_consume_item_count and cur_draw_info.draw_consume_item_id then
            local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
            local lerp_num = cur_draw_info.draw_consume_item_count - num
            if lerp_num <= 0 then
                return true
            end
        end
    end
end


function FZGetRewardWGData:GetIsDrawEmptyByLayer(layer)
    local list = self:GetCurRewardListByLayer(layer) or {}
    local num = self:GetLayerRewardNumByLayer(layer)
    return #list <= num
end

--是否开启
function FZGetRewardWGData:GetActCanOpen()

    local vo = GameVoManager.Instance:GetMainRoleVo()
    local open_level = self:GetOpenLevelByServerDay()
    if vo.level >= open_level then
        return true
    end
    return false
end

function FZGetRewardWGData:DayChange()
    local layer_cfg = self:GetDangWeiCfgByGrade()
    local layer_length = #layer_cfg
    for i = 1, layer_length do
        self["cur_round_" .. i] = nil
        self["reward_id_list_" .. i] = nil
    end
end

function FZGetRewardWGData:ClearLayerCache(layer)
    self.pool_list[layer] = {}
end

function FZGetRewardWGData:SetLayerInfo(protocol)
    local protocol_layer = protocol.layer + 1
    self.info[protocol_layer] = protocol.draw_reward_id_list
    self.result_flag[protocol_layer] = protocol.draw_result_flag
    self.round_index[protocol_layer] = protocol.round_index
    self.pool_id[protocol_layer] = protocol.reward_pool_id
end

function FZGetRewardWGData:SetRecordInfo(protocol)
    self.record = protocol.draw_record
end

function FZGetRewardWGData:SetResultInfo(protocol)
    self.result.layer = protocol.layer + 1
    self.result.slot = protocol.hit_slot
    self.result.is_one_key = protocol.is_one_key == 1
end

function FZGetRewardWGData:GetResultInfo()
    return self.result
end

function FZGetRewardWGData:GetPoolIdByLayer(layer)
    return self.pool_id[layer]
end

function FZGetRewardWGData:GetRoundIndexIdByLayer(layer)
    return self.round_index[layer]
end

function FZGetRewardWGData:UpdateRecordCount()
    self.near_record_time = TimeWGCtrl.Instance:GetServerTime()
    RoleWGData.SetRolePlayerPrefsInt("fz_get_record_tiem", self.near_record_time)
    self:SetNewRecordCount(0)
end

function FZGetRewardWGData:CalNewRecordNum()
    self.near_record_time = RoleWGData.GetRolePlayerPrefsInt("fz_get_record_tiem")
    local count = 0
    for i, v in pairs(self.record) do
        if v.timestamp > self.near_record_time then
            count = count + 1
        end
    end
    self:SetNewRecordCount(count)
end

function FZGetRewardWGData:SetNewRecordCount(count)
    self.new_record_count = count
end

function FZGetRewardWGData:GetNewRecordCount()
    return self.new_record_count or 0
end

function FZGetRewardWGData:GetResultFlagByLayer(layer)
    return self.result_flag[layer]
end

function FZGetRewardWGData:HasNextPool(layer)
    local pool_id = self:GetPoolIdByLayer(layer)
    if pool_id == -1 then
        return false
    end
    --当前奖池有几个周期
    local round_count = self:GetCurLayerRound(layer)
    local reward_pool_id_list = self:GetRewardPoolIdByLayerAndCount(layer, round_count)
    if nil == reward_pool_id_list then
        return false
    end

    local str_id = reward_pool_id_list[#reward_pool_id_list]
    return tonumber(str_id) ~= pool_id --是不是最后一个
end

function FZGetRewardWGData:GetLayerInfoByLayer(layer)
    if layer == nil then
        return
    end
    local list = self.info[layer] or {}
    if IsEmptyTable(self.pool_list[layer]) then
        self.pool_list[layer] = {}
        for i, v in ipairs(list) do
            if v > 0 then
                table.insert(self.pool_list[layer], v)
            end
        end
    end
    return self.pool_list[layer]
end

function FZGetRewardWGData:IsHasData(layer)
    if self.info and self.info[layer] then
        return true
    end
    return false
end

function FZGetRewardWGData:GetRecordInfo()
    local list = {}
    for i, v in ipairs(self.record) do
        if v.reward_id > 0 then
            table.insert(list, v)
        else
            break
        end
    end
    return list
end

-- 总抽取次数
function FZGetRewardWGData:GetLayerRewardNumByLayer(layer)
    return bit:d2b1n(self.result_flag[layer] or 0)
end

function FZGetRewardWGData:GetActRoundTimeByLayer(layer)
    if not layer then
        return
    end
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG)
    if act_info == nil then
        return 
    end
    local cfg = self:GetDangWeiCfgByGradeAndlayer(layer)
    local round_time_list = self:GetNowCountTimeByLayer(layer)
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local pass_time = now_time - act_info.start_time
    for i = 1, #round_time_list do
        pass_time = pass_time - round_time_list[i] * 3600
        if pass_time < 0 then
            return -pass_time, i
        end
    end
    return 0, cfg.round_count
end

function FZGetRewardWGData:GetNowCountTimeByLayer(layer)
    local cfg = self:GetDangWeiCfgByGradeAndlayer(layer)
    local round_time_list = Split(cfg.round_continue_time, "|")
    return round_time_list
end

function FZGetRewardWGData:GetCurLayerRound(layer)
    if not layer then 
        return
    end
    if not self["cur_round_" .. layer] then
        local _, cur_round = self:GetActRoundTimeByLayer(layer)
        self["cur_round_" .. layer] = cur_round
    end
    return self["cur_round_" .. layer]
end

-- 各周期内奖池id
function FZGetRewardWGData:GetRewardPoolIdByLayerAndCount(layer, round_count)
    local cfg = self:GetDangWeiCfgByGrade()
    if not cfg then
        return
    end
    local pool_list
    for i, v in pairs(cfg) do
        if v.layer == layer then
            --添加容错判空，虽然不知道怎么错的
            if v.reward_pool_id then
                pool_list = Split(v.reward_pool_id, ",")
            end
            break
        end
    end
    if nil == pool_list then
        return nil
    end
    if pool_list[round_count] then
        return Split(pool_list[round_count], ",")
    end
end

function FZGetRewardWGData:GetCurRewardListByLayer(layer)
    return self:GetLayerInfoByLayer(layer)
end


--抽奖消耗
function FZGetRewardWGData:GetCurDrawInfoByLayer(layer)
    local cur_pool_id = self:GetPoolIdByLayer(layer)
    local dangwei_cfg = self:GetDangWeiCfgByGradeAndlayer(layer)
    -- 当前已抽取次数
    local cur_draw_times = self:GetLayerRewardNumByLayer(layer)
    local consume_id = self:GetConSumeIdByPoolID(layer, cur_pool_id)
    local info = {}
    info.draw_consume_item_id = dangwei_cfg.consume_item
    info.complement_num = dangwei_cfg.complement_num
    info.one_key = dangwei_cfg.one_key
    for k, v in pairs(self.fz_consume_cfg) do
        if v.consume_id == consume_id and v.num_min > cur_draw_times then
            info.draw_consume_item_count = v.consume_item
            break
        end
    end
    return info
end

function FZGetRewardWGData:GetConSumeIdByPoolID(layer, consume_id)
    local now_count = self:GetRoundIndexIdByLayer(layer)
    now_count = now_count + 1
    local dangwei_cfg = self:GetDangWeiCfgByGradeAndlayer(layer)
    if IsEmptyTable(dangwei_cfg) then return 0 end
    local pool_id = dangwei_cfg.reward_pool_id
    local pool_id_table = Split(pool_id, "|")

    local need_pool_table = Split(pool_id_table[now_count], ",")

    local consume_id = dangwei_cfg.consume_id
    local consume_id_table = Split(consume_id, "|")
    local consume_id_table = Split(consume_id_table[now_count], ",")
     
    local cur_pool_id = self:GetPoolIdByLayer(layer)
    local pos
    for k,v in pairs(need_pool_table) do
        if tonumber(v) == cur_pool_id then
            pos = k
            break
        end
    end
    for k, v in pairs(consume_id_table) do
        if k == pos then
            return tonumber(v)
        end
    end
end

function FZGetRewardWGData:GetRewardInfoByRewardID(id)
    local cur_pool_id = self:GetPoolIdByLayer(self.select_layer)
    return self.fz_reward_pool_cfg[cur_pool_id][id]
end

function FZGetRewardWGData:GetBestRewardInfoByNone()
    local cur_pool_id = self:GetPoolIdByLayer(self.select_layer)
    return self.fz_reward_best_cfg[cur_pool_id][1]
end

-- 获取当前抽奖结果
function FZGetRewardWGData:GetCurGetList(layer, draw_all_flag)
    local result = self:GetResultInfo()
    local data_list = {}
    if result.is_one_key or draw_all_flag then
        local reward_id_list = self:GetCurRewardListByLayer(layer or result.layer)
        for i, v in ipairs(reward_id_list) do
            local state = self:GetRewardIsShowByLayerAndId(layer or result.layer, i)
            if state then
                local reward_info = self:GetRewardInfoByRewardID(v)
                table.insert(data_list,reward_info.reward_item)
            end
        end
        
    else
        local reward_info = self:GetRewardByLayerAndSlot(result.layer, result.slot)
        table.insert(data_list,reward_info.reward_item)
    end

    return data_list
end


function FZGetRewardWGData:GetRewardByLayerAndSlot(layer, slot)
    local reward_list = self:GetLayerInfoByLayer(layer)
    local id = reward_list[slot + 1]
    local reward_info = self:GetRewardInfoByRewardID(id)
    return reward_info
end

-- 获取大奖信息
function FZGetRewardWGData:GetBestRewardInfo(layer)
    local reward_id_list = self:GetCurRewardListByLayer(layer)
    local reward_info
    for i, v in ipairs(reward_id_list) do
        reward_info = self:GetRewardInfoByRewardID(v)
        if reward_info.is_best_reward == 1 then
            return reward_info
        end
    end

    return {}
end

-- 获取大奖是否被抽取
function FZGetRewardWGData:GetBestRewardHasGet(layer, reward_id_list)
    local state
    local reward_info
    for i, v in ipairs(reward_id_list) do
        state = self:GetRewardIsShowByLayerAndId(layer, i)
        reward_info = self:GetRewardInfoByRewardID(v)
        if state and reward_info.is_best_reward == 1 then
            return false
        end
    end

    return true
end

--红点
function FZGetRewardWGData:IsShowBtnDrawRedByLayerView(layer)
    if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG) then
        return false
    end
    if not layer then
        return false
    end
    local is_empty = self:GetIsDrawEmptyByLayer(layer)
    if is_empty then
        return false
    else
        local cur_draw_info = self:GetCurDrawInfoByLayer(layer)
         if cur_draw_info and cur_draw_info.draw_consume_item_count and cur_draw_info.draw_consume_item_id then
            local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
            local lerp_num = cur_draw_info.draw_consume_item_count - num
            if lerp_num <= 0 then
                return true
            end
        end
    end
end

--获取大奖是否还存在
function FZGetRewardWGData:GetBigRewardExistByLayer(layer)
    local list = self:GetLayerInfoByLayer(layer)
    local cfg
    for i, v in pairs(list) do
        cfg = self:GetRewardInfoByRewardID(v)
        local is_show = self:GetRewardIsShowByLayerAndId(layer, i)
        if cfg.is_best_reward == 1 and is_show then
            return true
        end
    end
    return false
end

-- 当前奖励是否没抽取
function FZGetRewardWGData:GetRewardIsShowByLayerAndId(layer, index)
    local flag = self.result_flag[layer] or 0
    return bit:_and(flag, bit:_lshift(1, index - 1)) == 0
end

function FZGetRewardWGData:GetAnimDelayTime()
    local cfg = self:GetViewCfgByInterface()
    return cfg.tips_delay or 3
end

function FZGetRewardWGData:GetAnimRoleTime()
    local cfg = self:GetViewCfgByInterface()
    return cfg.role_anim_time or 0
end

function FZGetRewardWGData:GetAnimAngleSpeed()
     local cfg = self:GetViewCfgByInterface()
    return cfg.angle_speed or 50
end

function FZGetRewardWGData:GetAnimArrowSpeed()
     local cfg = self:GetViewCfgByInterface()
    return cfg.arrow_speed or 500
end

function FZGetRewardWGData:GetAnimKiteTime()
     local cfg = self:GetViewCfgByInterface()
    return cfg.kite_time or 1
end

--活动已经开启的时间
function FZGetRewardWGData:GetActOpenTime()
    local time = 0
    local act_info = self:GetActOpenTimeCuo()
    if act_info == nil then
        return time
    end
    if act_info.status == ACTIVITY_STATUS.CLOSE then
        return time
    end
    local day_value = 60 * 60 * 24
    local time_tab = os.date("*t", act_info.start_time)
    local open_time = act_info.start_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    time = server_time - open_time
    return time
end

function FZGetRewardWGData:GetActOpenTimeCuo()
    return ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG)
end
