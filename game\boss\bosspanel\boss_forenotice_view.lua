--------------------------------------------------
-- boss刷新提醒
--------------------------------------------------
BossForenoticeView = BossForenoticeView or BaseClass(SafeBaseView)

local FLAG_NAME = {
    [1] = "a3_zjm_bq",
    [2] = "a3_zjm_bq1",
}

function BossForenoticeView:__init()
	self.is_modal = true

	self.view_layer = UiLayer.Pop
	self.view_name = "BossForenoticeView"
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_forenotice1")
end

function BossForenoticeView:__delete()
end

function BossForenoticeView:ReleaseCallBack()
	self.boss_info = {}

	--[[
	if self.reward_show_list then
		self.reward_show_list:DeleteMe()
		self.reward_show_list = nil
	end
	]]
end

function BossForenoticeView:LoadCallBack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(self.ClickHandler, self))

	--[[ 策划要求隐藏极品掉落
	if not self.reward_show_list then
		self.reward_show_list = AsyncListView.New(ItemCell, self.node_list.reward_show_list)
	end
	]]
end

function BossForenoticeView:ShowIndexCallBack()
	self:Flush()
end

function BossForenoticeView:OnFlush()
	local cfg = BossWGData.Instance:GetMonsterInfo(self.boss_info.boss_id)
	if cfg then
		if self.node_list["boss_name_txt"] then
			self.node_list["boss_name_txt"].text.text = cfg.name
		end

		if self.node_list["img_boss_head"] then
			local bundle,asset = ResPath.GetBossIcon("wrod_boss_".. cfg.small_icon)
			self.node_list["img_boss_head"].image:LoadSprite(bundle,asset,function()
				self.node_list["img_boss_head"].image:SetNativeSize()
			end)
		end
	end
	
	local boss_info = BossWGData.Instance:GetBossAllInfoByBossId(self.boss_info.boss_id)
	if boss_info == nil then return end

	self:FlushRewardShowDataList(boss_info)

	if Config_scenelist[boss_info.scene_id] then
		self.node_list["lbl_title"].text.text = Config_scenelist[boss_info.scene_id].name
    end

    if TreasureBossWGData.Instance:IsTreasureBoss(self.boss_info.boss_id) then
    	local jie_shu = TreasureBossWGData.Instance:GetTreasureBossClass(self.boss_info.boss_id)
        self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(jie_shu))
    elseif XianJieBossWGData.Instance:GetBossCfgById(self.boss_info.boss_id) then
        local cfg = XianJieBossWGData.Instance:GetBossCfgById(self.boss_info.boss_id)
        local slor_str = NumberToChinaNumber(cfg.slot_limit + 1)
        local page_str2 = XianJieBossWGData.Instance:GetPageStr(cfg.slot_page_limit, true)
        self.node_list.jieshu_text.text.text = string.format(Language.XianJieBoss.SlotSNoPoint, slor_str, page_str2)
    else
    	self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(cfg.boss_jieshu))
    end

    self:FlushBossImgBG()
end

function BossForenoticeView:FlushBossImgBG()
    if self.boss_info then
        local bg_type = 1 --1 白色 2 紫色 3 金色
        local flag_type = 1 -- 1和平 2 危险
		local scene_type = BossWGData.SceneType[self.boss_info.boss_type]
        if scene_type == SceneType.WorldBoss
		or scene_type == SceneType.VIP_BOSS
		or scene_type == SceneType.DABAO_BOSS then
            bg_type = 1
        	flag_type = 1
        elseif scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS
		or scene_type == SceneType.Shenyuan_boss then
            bg_type = 3
            flag_type = 2
        elseif scene_type == SceneType.KF_BOSS
		or scene_type == SceneType.XianJie_Boss then
            bg_type = 2
            flag_type = 2
        end

		--美术指出了一张底，策划xzp让只用原图
        local flag_name = FLAG_NAME[flag_type]
        local bundle1, asset1 = ResPath.GetBossUI(flag_name)
        self.node_list.flag_img.image:LoadSprite(bundle1, asset1) --左上角flag
		self.node_list.boss_status_text.text.text = Language.BossFlagStatus[flag_type]

        local bundle2, asset2 = ResPath.GetBossUI("a3_zjm_bst_" .. self.boss_info.boss_type)
        self.node_list.name_flag_img.image:LoadSprite(bundle2, asset2)
    end
end

function BossForenoticeView:SetShowData(data)
	self.boss_info = data
	local boss_info = BossWGData.Instance:GetBossAllInfoByBossId(self.boss_info.boss_id)
    if boss_info == nil then
        print_error("取不到boss配置 boss_id =", self.boss_info.boss_id, "boss_type", self.boss_info.boss_type)
		self:Close()
        return
    end
	self.boss_info.big_icon = boss_info.big_icon
	self.boss_info.boss_level = boss_info.boss_level
	self.boss_info.boss_name = boss_info.boss_name
	-- self.boss_info.boss_type =
	self:Flush()
end

function BossForenoticeView:JudgeNeedCloseView(boss_id)
    if self:IsOpen() and not IsEmptyTable(self.boss_info) and self.boss_info.boss_id == boss_id then
        self:Close()
    end
end

function BossForenoticeView:FlushRewardShowDataList(boss_info)
	local data_list = {}
	local zero_index = false

	local scene_type = BossWGData.SceneType[self.boss_info.boss_type]
	if scene_type == SceneType.WorldBoss or scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.KF_BOSS then
		if boss_info and boss_info.drop_item_list then
			zero_index = true
			data_list = boss_info.drop_item_list
		end
	elseif scene_type == SceneType.SG_BOSS then

	elseif scene_type == SceneType.MJ_BOSS then

	elseif scene_type == SceneType.TIANSHEN_JIANLIN or scene_type == SceneType.WORLD_TREASURE_JIANLIN then

	elseif scene_type == SceneType.Shenyuan_boss then
		if boss_info and boss_info.drop_item_list1 then
			local list = {}
			local data
			for i, v in pairs(boss_info.drop_item_list1) do
				local str = Split(v, ":")
				data = {
					item_id = tonumber(str[1]),
					num = tonumber(str[2]),
					is_bind = tonumber(str[3]),
				}
				table.insert(list, data)
			end

			data_list = list
		end
	elseif scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
		local treasure_boss_info = TreasureBossWGData.Instance:GetBossDropShowData(self.boss_info.boss_id)
		data_list = treasure_boss_info and treasure_boss_info.drop_item_list or {}
	end

	--[[ 策划要求隐藏极品掉落
	local has_drop_item = not IsEmptyTable(data_list)

	if has_drop_item then
		self.reward_show_list:SetStartZeroIndex(zero_index)
		self.reward_show_list:SetDataList(data_list)
	
		-- self.node_list.head_root.transform.localPosition = Vector3(-58, 57, 0)
		-- self.node_list.flag_img.transform.localPosition = Vector3(-58, 27, 0)
		-- self.node_list.jieshu_text.transform.localPosition = Vector3(26, 70, 0)
		-- self.node_list.center_name.transform.localPosition = Vector3(0, 57, 0)
		-- self.node_list.name_flag_img.transform.localPosition = Vector3(-105, 11, 0)
		-- self.node_list.boss_name_txt.transform.localPosition = Vector3(39, -12, 0)
	-- else
	-- 	self.node_list.head_root.transform.localPosition = Vector3(0, 57, 0)
	-- 	self.node_list.flag_img.transform.localPosition = Vector3(0, 27, 0)
	-- 	self.node_list.jieshu_text.transform.localPosition = Vector3(-22, -10, 0)
	-- 	self.node_list.center_name.transform.localPosition = Vector3(0, -23, 0)
	-- 	self.node_list.name_flag_img.transform.localPosition = Vector3(-90, 0, 0)
	-- 	self.node_list.boss_name_txt.transform.localPosition = Vector3(-9, -11, 0)
	end

	self.node_list.flag_drop:CustomSetActive(has_drop_item)	
	]]
end

function BossForenoticeView:ClickHandler()
	local boss_cfg = BossWGData.Instance:GetBossAllInfoByBossId(self.boss_info.boss_id)
	if nil == boss_cfg then
		self:Close()
		return
	end

	if self:IsSameScene(boss_cfg.scene_id) then
		Scene.Instance:ClearAllOperate()

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = self.boss_info.boss_id
		MoveCache.param1 = self.boss_info.boss_id
		local range = BossWGData.Instance:GetMonsterRangeByid(self.boss_info.boss_id)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)

		GuajiWGCtrl.Instance:MoveToPos(
			Scene.Instance:GetSceneId(),
			boss_cfg.x_pos or boss_cfg.flush_pos_x,
			boss_cfg.y_pos or boss_cfg.flush_pos_y,
			range)
	else
		 local main_role = Scene.Instance:GetMainRole()
		 if main_role:IsFightState() and Scene.Instance:GetSceneType() ~= SceneType.Common then
		 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.FightState)
		 	self:Close()
		 	return
		 end

		local layer, view_index, index
		if self.boss_info.scene_type == SceneType.DABAO_BOSS then
			layer = boss_cfg.layer
			view_index = TabIndex.boss_dabao
			index = boss_cfg.boss_view_index
		elseif self.boss_info.scene_type == SceneType.SG_BOSS then
			layer = boss_cfg.layer + 1--layer从零开始
			view_index = TabIndex.worserv_boss_sgyj
            index = boss_cfg.boss_index + 3--boss_index从零开始，+采集+隐藏
        elseif self.boss_info.scene_type == SceneType.XianJie_Boss then
            layer = boss_cfg.layer
			view_index = TabIndex.xianjie_boss
            index = boss_cfg.boss_index
		end

		if layer ~= nil or index ~= nil then
			ViewManager.Instance:Close(GuideModuleName.Boss)
			ViewManager.Instance:Close(GuideModuleName.WorldServer)
			BossWGData.Instance:SetBossTuJianIndex(view_index, layer, index, self.boss_info.scene_type)
			if self.boss_info.scene_type == SceneType.KF_BOSS or self.boss_info.scene_type == SceneType.SG_BOSS or
				self.boss_info.scene_type == SceneType.HONG_MENG_SHEN_YU or self.boss_info.scene_type == SceneType.XianJie_Boss or
				self.boss_info.scene_type == SceneType.SHENGYU_BOSS then
				ViewManager.Instance:Open(GuideModuleName.WorldServer, view_index)
			else
				ViewManager.Instance:Open(GuideModuleName.Boss, view_index)
			end
			self:Close()
			return
		end

		if BossWGData.SceneType[self.boss_info.boss_type] == SceneType.WorldBoss then
			BossWGData.Instance:SetCurSelectBossID(BossViewIndex.WorldBoss, boss_cfg.layer, self.boss_info.boss_id)
			BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ENTER, boss_cfg.layer)

			-- elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.DABAO_BOSS then
			-- 	-- BossWGData.Instance:SetCurSelectBossID(BossViewIndex.DabaoBoss, boss_cfg.layer, self.boss_info.boss_id)
			-- 	-- BossWGCtrl.Instance:SendDongKuBossReq(BossView.ReqType.ENTER, boss_cfg.layer, self.boss_info.boss_id)

        elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.VIP_BOSS then
            BossWGData.Instance:SetCurSelectBossID(BossViewIndex.VipBoss, boss_cfg.layer, self.boss_info.boss_id)
			BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.ENTER, self.boss_info.level, self.boss_info.boss_id)
			BossWGCtrl.Instance:OnEnterVipBoss()
			-- 	-- 

        elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.KF_BOSS then
            BossWGData.Instance:SetCurSelectBossID(BossViewIndex.KF_BOSS, boss_cfg.layer, self.boss_info.boss_id)
			BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.KFBoss, self.boss_info.level, self.boss_info.boss_id)
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_BOSS, self.boss_info.level)

			--elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.SG_BOSS then
			-- 	BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.SGBOSS, self.boss_info.level, self.boss_info.boss_id)
			-- 	BossWGCtrl.Instance:SendShangGuBossReq(BossView.SgReqType.ENTER, boss_cfg.layer, self.boss_info.boss_id)

			-- elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.MJ_BOSS then
			-- 	-- local layer = BossWGData.Instance:GetBossLayerById(self.boss_info.boss_id)
			-- 	BossWGData.Instance:SetCurSelectBossID(BossViewIndex.MiJingBoss, boss_cfg.layer, self.boss_info.boss_id)
			-- 	BossWGCtrl.Instance:SendMiJingBossReq(BossView.SgReqType.ENTER, layer, self.boss_info.boss_id)
			-- end
		elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.TIANSHEN_JIANLIN then
			local boss_id ,v = TianshenRoadWGData.Instance:GetBossId()
			if v ~= nil then
				EmojiTextUtil.FlyToPos(v.scene_id, v.boss_posx, v.boss_posy)
			end
		elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.WORLD_TREASURE_JIANLIN then
			local boss_id ,v = TianshenRoadWGData.Instance:GetBossId()
			if v ~= nil then
				EmojiTextUtil.FlyToPos(v.scene_id, v.boss_posx, v.boss_posy)
			end
        elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.Shenyuan_boss then
			BossWGCtrl.Instance:SendShenYuanOpera(WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE.SHENYUAN_BOSS_REQ_TYPE_ENTER, boss_cfg.seq)
            -- ViewManager.Instance:Open(GuideModuleName.WorldServer, "world_new_shenyuan_boss")
		elseif self.boss_info.scene_index ~= nil then
			BossWGCtrl.Instance:SendHMSYEnterReq(self.boss_info.scene_id, self.boss_info.scene_index)
		elseif BossWGData.SceneType[self.boss_info.boss_type] == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
			TreasureHuntWGData.Instance:SetOpenSelectEffect(true)
			local boss_cfg = TreasureBossWGData.Instance:GetTreasureDataByBossId(self.boss_info.boss_id)
			ViewManager.Instance:Open(GuideModuleName.TreasureHunt, TreasureHuntView.TableIndexByMode[boss_cfg.chestshop_type])
			--BossWGCtrl.Instance:SendTreasureBossOpera(TREASURE_BOSS_REQ_TYPE.REQ_TYPE_ENTER,boss_cfg.seq)
		end
	end

	self:Close()
end

function BossForenoticeView:IsSameScene(scene_id)
	if scene_id == Scene.Instance:GetSceneId() then
		if Scene.Instance:GetSceneType() == SceneType.HONG_MENG_SHEN_YU then
			local scene_index = BossWGData.Instance:GetCurHMSYSceneIndex()
			return self.boss_info.scene_index ~= nil and self.boss_info.scene_index == scene_index
		else
			return true
		end
	end

	return false
end