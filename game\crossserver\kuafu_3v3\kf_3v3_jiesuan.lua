--3v3结算界面
KF3V3JieSuanView = KF3V3JieSuanView or BaseClass(SafeBaseView)
function KF3V3JieSuanView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_3v3_jiesuan")
end

function KF3V3JieSuanView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("KF3V3JieSuanView_close_timer") then
        CountDownManager.Instance:RemoveCountDown("KF3V3JieSuanView_close_timer")
    end

    if self.dw_tweener then
        self.dw_tweener:Kill()
        self.dw_tweener = nil
    end

    if self.blue_player_list then
        for i, v in ipairs(self.blue_player_list) do
            v:DeleteMe()
        end
        self.blue_player_list = nil
    end

    if self.red_player_list then
        for i, v in ipairs(self.red_player_list) do
            v:DeleteMe()
        end
        self.red_player_list = nil
    end

    if self.btn_text_countdown then
        GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
        self.btn_text_countdown = nil
    end

    self:CancelSequenceTween()
    self.data = nil
    self.btn_time = 0
end

function KF3V3JieSuanView:LoadCallBack()
    self.blue_player_list = {}
    for i = 1, 3 do
        self.blue_player_list[i] = KF3V3JSPlayerInfoRender.New(self.node_list["info" .. i])
    end

    self.red_player_list = {}
    for i = 4, 6 do
        self.red_player_list[i - 3] = KF3V3JSPlayerInfoRender.New(self.node_list["info" .. i])
    end

    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickQuit, self))
end

function KF3V3JieSuanView:SetData(data)
    self.data = data
end

function KF3V3JieSuanView:ShowIndexCallBack()
    self:Flush()
end

function KF3V3JieSuanView:SetBtnTextTimer(total_time)
    self.btn_time = total_time + 1
    if self.btn_text_countdown then
        GlobalTimerQuest:CancelQuest(self.btn_text_countdown)
        self.btn_text_countdown = nil
    end
    local remain_time = total_time
    self:UpdateBtnText()
    self.btn_text_countdown = GlobalTimerQuest:AddTimesTimer(BindTool.Bind(self.UpdateBtnText, self), 1, remain_time)
end

function KF3V3JieSuanView:UpdateBtnText()
    self.btn_time = self.btn_time - 1
    if self.node_list.close_time_text then
        self.node_list.close_time_text.text.text = string.format(Language.KuafuPVP.CloseJiesuanTime, self.btn_time)
    end
    if self.btn_time <= 1 then
        GlobalTimerQuest:AddDelayTimer(function()
            if self:IsOpen() then
                self:Close()
            end
        end, 1)
    end
end

function KF3V3JieSuanView:CloseCallBack()
    if Scene.Instance:GetSceneType() == SceneType.Kf_PVP then
        Field1v1WGCtrl.Instance:LeaveZhanChangScene()
    end
end

function KF3V3JieSuanView:OnFlush()
    if not self.data then return end
    self:SetBtnTextTimer(10)
    --是否胜利
    local is_win = self.data.is_win == 1

    if is_win then
        AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ShengLi, nil, true))
    end

    self.node_list.victory:SetActive(is_win)
    self.node_list.lose:SetActive(not is_win)

    local zhandui_old_score = self.data.zhandui_old_score
    local zhandui_new_score = self.data.zhandui_new_score
    local zhandui_old_duanwei_cfg, old_next_score, _, cfg_index = KF3V3WGData.Instance:GetDuanWeiCfg(zhandui_old_score)
    local zhandui_new_duanwei_cfg, new_next_score = KF3V3WGData.Instance:GetDuanWeiCfg(zhandui_new_score)

    local old_progress = (zhandui_old_score - zhandui_old_duanwei_cfg.score) / old_next_score
    local new_progress = (zhandui_new_score - zhandui_new_duanwei_cfg.score) / new_next_score
    local star_change_count = zhandui_new_duanwei_cfg.star_num - zhandui_old_duanwei_cfg.star_num
    local is_add_score = (zhandui_new_score - zhandui_old_score) > 0
    local tween_time = 2
    local once_tween_time = tween_time / (star_change_count + 1)
    once_tween_time = tonumber(string.format("%.1f", once_tween_time))
    new_progress = tonumber(string.format("%.4f", new_progress))

    --star_change_count = 0   times = 1
    --star_change_count = 1   times = 2
    --star_change_count = 2   times = 3
    --star_change_count = 3   times = 4
    --第一次
    if star_change_count == 0 then
        self.node_list.dw_progress.slider.value = old_progress
        self.node_list.dw_progress.slider:DOValue(new_progress, tween_time):OnUpdate(function()
            --local value = self.node_list.dw_progress.image.fillAmount
            --self.node_list.rotate_light.rect.localRotation = Quaternion.Euler(0, 0, (is_add_score and -1 or 1) * 360 * value)
        end):OnComplete(function()
            -- self:FlushDuanWei(nil, zhandui_new_score)
        end)
        self:FlushDuanWei(nil, zhandui_new_score)
    elseif star_change_count == 1 then
        --头和尾不完整
        self.node_list.dw_progress.slider:DOValue(is_add_score and 1 or 0, tween_time / 2):OnComplete(function()
            self.node_list.dw_progress.slider.value = is_add_score and 0 or 1
            self.node_list.dw_progress.slider:DOValue(new_progress, tween_time / 2):OnComplete(function()
                self:FlushDuanWei(nil, zhandui_new_score)
                --大段位发生变化
                if zhandui_new_duanwei_cfg.grade ~= zhandui_old_duanwei_cfg.grade then
                    if zhandui_new_duanwei_cfg.grade > zhandui_old_duanwei_cfg.grade then
                        --升段位
                        local data = {}
                        data.data_type = EnumDuanweiChangeType.Up
                        data.old_score = zhandui_old_score
                        data.new_score = zhandui_new_score
                        self:ShowDuanWeiChangeView(data)
                    else
                        --降段位
                        local data = {}
                        data.data_type = EnumDuanweiChangeType.Down
                        data.old_score = zhandui_old_score
                        data.new_score = zhandui_new_score
                        self:ShowDuanWeiChangeView(data)
                    end
                end
            end):OnUpdate(function()
                --local value = self.node_list.dw_progress.image.fillAmount
                --self.node_list.rotate_light.rect.localRotation = Quaternion.Euler(0, 0, (is_add_score and -1 or 1) * 360 * value)
            end)
        end):OnUpdate(function()
            --local value = self.node_list.dw_progress.image.fillAmount
            --self.node_list.rotate_light.rect.localRotation = Quaternion.Euler(0, 0, (is_add_score and -1 or 1) * 360 * value)
        end)
    elseif star_change_count >= 2 then
        --头和尾不完整，中间的完整一次
        self:CancelSequenceTween()
        local sequence = DG.Tweening.DOTween.Sequence()
        local total_count = star_change_count + 1

        for i = 1, total_count do
            if i == 1 then
                sequence:AppendCallback(function()
                    self.node_list.dw_progress.slider.value = old_progress
                    self.dw_tweener = self.node_list.dw_progress.slider:DOValue(is_add_score and 1 or 0, once_tween_time)
                        :OnUpdate(function()
                            --local value = self.node_list.dw_progress.image.fillAmount
                            --self.node_list.rotate_light.rect.localRotation = Quaternion.Euler(0, 0, (is_add_score and -1 or 1) * 360 * value)
                        end):OnComplete(function()
                            if is_add_score then
                                cfg_index = cfg_index + 1
                            else
                                cfg_index = cfg_index - 1
                            end
                            local cfg = KF3V3WGData.Instance:GetDuanWeiCfgByIndex(cfg_index)
                            self:FlushDuanWei(cfg)
                        end)
                end)
                sequence:AppendInterval(once_tween_time + 0.2)
            elseif i == total_count then
                sequence:AppendCallback(function()
                    if self.dw_tweener then
                        self.dw_tweener:Kill()
                        self.dw_tweener = nil
                    end
                    self.node_list.dw_progress.slider.value = is_add_score and 0 or 1
                    self.dw_tweener = self.node_list.dw_progress.slider:DOValue(new_progress, once_tween_time)
                        :OnComplete(function()
                            self:FlushDuanWei(nil, zhandui_new_score)
                            --大段位发生变化
                            if zhandui_new_duanwei_cfg.grade ~= zhandui_old_duanwei_cfg.grade then
                                if zhandui_new_duanwei_cfg.grade > zhandui_old_duanwei_cfg.grade then
                                    --升段位
                                    local data = {}
                                    data.data_type = EnumDuanweiChangeType.Up
                                    data.old_score = zhandui_old_score
                                    data.new_score = zhandui_new_score
                                    self:ShowDuanWeiChangeView(data)
                                else
                                    --降段位
                                    local data = {}
                                    data.data_type = EnumDuanweiChangeType.Down
                                    data.old_score = zhandui_old_score
                                    data.new_score = zhandui_new_score
                                    self:ShowDuanWeiChangeView(data)
                                end
                            end
                        end):OnUpdate(function()
                            --local value = self.node_list.dw_progress.image.fillAmount
                            -- self.node_list.rotate_light.rect.localRotation = Quaternion.Euler(0, 0, (is_add_score and -1 or 1) * 360 * value)
                        end)
                end)
                sequence:AppendInterval(once_tween_time + 0.2)
            else
                sequence:AppendCallback(function()
                    self.node_list.dw_progress.slider.value = is_add_score and 0 or 1
                    self.dw_tweener = self.node_list.dw_progress.slider:DOValue(is_add_score and 1 or 0, once_tween_time)
                        :OnUpdate(function()
                            --local value = self.node_list.dw_progress.image.fillAmount
                            --self.node_list.rotate_light.rect.localRotation = Quaternion.Euler(0, 0, (is_add_score and -1 or 1) * 360 * value)
                        end):OnComplete(function()
                            if is_add_score then
                                cfg_index = cfg_index + 1
                            else
                                cfg_index = cfg_index - 1
                            end
                            local cfg = KF3V3WGData.Instance:GetDuanWeiCfgByIndex(cfg_index)
                            self:FlushDuanWei(cfg)
                        end)
                end)
                sequence:AppendInterval(once_tween_time + 0.2)
            end

            self.sequence_tween = sequence
        end
    end

    --积分变化文本
    local score_lerp = zhandui_new_score - zhandui_old_score
    self.node_list.total_add_right.text.text = score_lerp > 0 and string.format("+%s", score_lerp) or score_lerp
    self.node_list.my_score_right.text.text = self.data.my_add_score > 0 and string.format("+%s", self.data.my_add_score) or
        self.data.my_add_score

    --玩家信息
    --先发0 ， 1 ， 红的先
    for i = 1, 3 do
        self.blue_player_list[i]:SetData(self.data.side_list[2][i])
    end
    for i = 1, 3 do
        self.red_player_list[i]:SetData(self.data.side_list[1][i])
    end
    --获取自己的数据
    local my_data = {}
    local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
    for i, v in pairs(self.data.side_list) do
        for k, player_info in pairs(v) do
            if my_uid == player_info.uid then
                my_data = player_info
            end
        end
    end
    --物品奖励
    local data_list = KF3V3WGData.Instance:GetPKItemList(is_win, my_data.uuid == self.data.mvp_uuid, my_data.match_times,
        my_data.mvp_times)
    self.node_list.reward_text:SetActive(#data_list > 0)
    if #data_list > 0 and data_list[1].num then
        self.node_list.reward_text.text.text = string.format(Language.KuafuPVP.KFPVPJieSuanReward, data_list[1].num)
    end
    self.node_list.today_tip:SetActive(#data_list <= 0 and is_win)
    if #data_list <= 0 then
        local cfg = KF3V3WGData.Instance:GetPKRewardCfg()
        local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
        if role_info and role_info.today_match_times and role_info.today_match_times >= cfg.score_reward_time_limit then --15场
            self.node_list.today_tip.text.text = string.format(Language.KuafuPVP.TodayRewardTips2,
                cfg.score_reward_time_limit)
        else --10场
            self.node_list.today_tip.text.text = string.format(Language.KuafuPVP.TodayRewardTips, cfg.reward_time_limit)
        end
    end

    local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.zhandui_old_score) --self.data.zhandui_new_score
    for i = 1, 5 do
        self.node_list["star" .. i]:SetActive(i <= grade_cfg.star)
    end
    self.node_list.score.text.text = (self.data.zhandui_old_score - grade_cfg.score) .. "/" .. next_score

    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local grade_cfg, next_score, is_max = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhandui_info.score)
    self.node_list.duanwei.text.text = grade_cfg.tier_name
end

function KF3V3JieSuanView:CancelSequenceTween()
    if self.sequence_tween then
        self.sequence_tween:Kill()
        self.sequence_tween = nil
    end
end

function KF3V3JieSuanView:OnClickQuit()
    self:Close()
end

function KF3V3JieSuanView:ShowDuanWeiChangeView(data)
    KF3V3WGCtrl.Instance:OpenDuanWeiChangeView(data)
end

function KF3V3JieSuanView:FlushDuanWei(cfg, score_param)
    if score_param then
        local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(score_param)
        for i = 1, 5 do
            self.node_list["star" .. i]:SetActive(i <= grade_cfg.star)
        end
        self.node_list.score.text.text = (self.data.zhandui_new_score - grade_cfg.score) .. "/" .. next_score
    elseif cfg then
        for i = 1, 5 do
            self.node_list["star" .. i]:SetActive(i <= cfg.star)
        end
        self.node_list.score.text.text = cfg.score .. "/" .. cfg.score
    else
        local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.zhandui_new_score) --self.data.zhandui_new_score
        for i = 1, 5 do
            self.node_list["star" .. i]:SetActive(i <= grade_cfg.star)
        end
        self.node_list.score.text.text = (self.data.zhandui_new_score - grade_cfg.score) .. "/" .. next_score
    end

    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local grade_cfg, next_score, is_max = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhan_dui_info.score)
    self.node_list.duanwei_icon.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. grade_cfg.grade))
end

KF3V3JSPlayerInfoRender = KF3V3JSPlayerInfoRender or BaseClass(BaseRender)
function KF3V3JSPlayerInfoRender:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function KF3V3JSPlayerInfoRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function KF3V3JSPlayerInfoRender:OnFlush()
    if self.data.uid ~= 0 then
        local data = {}
        local uid = self.data.uid
        --高位表示平台类型 平台类型为0表示是机器人
        if self.data.uuid and self.data.uuid.temp_high == 0 then
            uid = 0
        end
        data.role_id = uid
        data.prof = self.data.prof
        data.sex = self.data.sex
        data.fashion_photoframe = self.data.photoframe
        self.head_cell:SetImgBg(true)
        self.head_cell:SetData(data)

        self.node_list.kill_num.text.text = string.format(Language.KuafuPVP.Kill_desc, self.data.kill_times)
        --local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
        local my_uuid = RoleWGData.Instance:GetUUid()
        local is_me = my_uuid == self.data.uuid --my_uid == self.data.uid
        local color = "#EDC7B0"
        local play_name = self.data.name
        local play_name_list = Split(self.data.name, "_")
        if not IsEmptyTable(play_name_list) then
            if play_name_list[2] then
                play_name = string.format(Language.KuafuPVP.ServerName_1, play_name_list[2], play_name_list[1])
            else
                play_name = play_name_list[1]
                local main_vo = RoleWGData.Instance:GetRoleVo()
                local server_str = "s" .. main_vo.origin_server_id
                play_name = string.format(Language.KuafuPVP.ServerName_1, server_str, play_name_list[1])
            end
        end
        --self.node_list.role_name.text.text = ToColorStr(play_name, is_me and "#EA8B26" or color)
        self.node_list.role_name.text.text = play_name
        --self.node_list.zhandui_name.text.text = ToColorStr(self.data.zhandui_name, is_me and "#C5A9FF" or color)
        self.node_list.zhandui_name.text.text = self.data.zhandui_name
        --self.node_list.self_bg:SetActive(is_me)
        local jiesuan_info = KF3V3WGData.Instance:GetJieSuanInfo()
        local is_mvp = self.data.uuid ~= 0 and jiesuan_info.mvp_uuid == self.data.uuid
        local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
        self.node_list.dianfen_img:SetActive(is_vis)
        if is_vis then
            self.node_list.level.text.text = level
        else
            self.node_list.level.text.text = level .. "级"
        end

        --显示MVP图标
        self.node_list.mvp_icon:SetActive(false)
        if jiesuan_info.mvp_uuid ~= 0 and is_mvp then
            self.node_list.mvp_icon:SetActive(true)
        end
    else
        self.head_cell:SetImgBg(false)
        self.node_list.kill_num.text.text = ""
        self.node_list.level.text.text = ""
        self.node_list.zhandui_name.text.text = ""
        self.node_list.role_name.text.text = ""
        self.node_list.dianfen_img:SetActive(false)
        self.node_list.mvp_icon:SetActive(false)
    end
    self.head_cell:SetGray(false)
end

KF3V3JieSuanItemCell = KF3V3JieSuanItemCell or BaseClass(BaseRender)
function KF3V3JieSuanItemCell:__init()
    self.cell = ItemCell.New(self.node_list.pos)
end

function KF3V3JieSuanItemCell:__delete()
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end
end

function KF3V3JieSuanItemCell:OnFlush()
    self.cell:SetData(self.data)
end
