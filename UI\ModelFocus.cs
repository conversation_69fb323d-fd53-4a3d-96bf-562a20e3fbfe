using HedgehogTeam.EasyTouch;
using UnityEngine;

public class ModelFocus : MonoBehaviour
{
    //到达位置和大小
    public Vector3 to_scale = Vector3.one;
    public Vector3 to_pos = Vector3.zero;
    //开始位置和大小
    public Vector3 from_scale = Vector3.one;
    public Vector3 from_pos = Vector3.zero;
    //目标
    public Transform aim_trans;
    //是否在执行动画中，不可操作
    private bool is_scale_anim = false;
    private bool is_pos_anim = false;
    //是否不能进行聚焦
    public bool is_not_pinch = false;
    //运动总时长
    [Header("从最小值运动到最大值的总时长")]
    public float all_move_time;

    // 初始位置和大小
    private Vector3 init_pos;
    private Vector3 init_scale;
    private float mDuration = 0f;
    private float mAmountPerDelta = 1000f;
    private float scale_factor = 0f;
    private float pos_factor = 0f;
    private bool is_for_ward_anim = false;

    private Vector3 last_form_scale = Vector3.one;
    private Vector3 last_form_pos = Vector3.zero;

    private void Start()
    {
        EasyTouch.On_Pinch += this.OnPinch;

        if (aim_trans != null)
        {
            init_pos = aim_trans.transform.localPosition;
            init_scale = aim_trans.transform.localScale;
        }
    }

    private void OnDestroy()
    {
        EasyTouch.On_Pinch -= this.OnPinch;
    }

    public Vector3 pos_value
    {
        get
        {
            return aim_trans.transform.localPosition;
        }
        set
        {
            aim_trans.transform.localPosition = value;
        }
    }

    public Vector3 scale_value
    {
        get
        {
            return aim_trans.localScale;
        }
        set
        {
            aim_trans.localScale = value;
        }
    }

    public float amountPerDelta
    {
        get
        {
            if (all_move_time == 0f) return 1000f;

            if (mDuration != all_move_time)
            {
                mDuration = all_move_time;
                mAmountPerDelta = Mathf.Abs(1f / all_move_time) * Mathf.Sign(mAmountPerDelta);
            }
            return mAmountPerDelta;
        }
    }

    public void ResetToInit()
    {
        if (this.init_pos != null && this.init_scale != null && aim_trans != null)
        {
            aim_trans.transform.localPosition = this.init_pos;
            aim_trans.transform.localScale = this.init_scale;
        }
    }

    // 变化更改
    private void DoScalePosChange(float scale_factor, float pos_factor)
    {
        if (this.aim_trans == null)
        {
            return;
        }

        pos_value = from_pos * (1f - pos_factor) + to_pos * pos_factor;
        scale_value = from_scale * (1f - scale_factor) + to_scale * scale_factor;
    }

    void Update()
    {
        if (!is_scale_anim && !is_pos_anim)
        {
            if (is_for_ward_anim)
            {
                from_pos = last_form_pos;
                from_scale = last_form_scale;
                is_for_ward_anim = false;
            }

            return;
        }

        float delta = Time.deltaTime;
        scale_factor += (all_move_time == 0f) ? 1f : amountPerDelta * delta;
        scale_factor = scale_factor <= 1 ? scale_factor : 1;
        scale_factor = scale_factor >= 0 ? scale_factor : 0;

        pos_factor += (all_move_time == 0f) ? 1f : amountPerDelta * delta;
        pos_factor = pos_factor <= 1 ? pos_factor : 1;
        pos_factor = pos_factor >= 0 ? pos_factor : 0;

        if (all_move_time == 0f || scale_factor == 1f || scale_factor == 0f)
        {
            is_scale_anim = false;
        }

        if (all_move_time == 0f || pos_factor == 1f || pos_factor == 0f)
        {
            is_pos_anim = false;
        }

        DoScalePosChange(scale_factor, pos_factor);
    }

    //private float scale_factor = 0f;
    //private float pos_factor = 0f;

    private void OnPinch(Gesture gesture)
    {
        if (is_scale_anim || is_not_pinch || is_pos_anim || this.aim_trans == null || !this.isActiveAndEnabled)
        {
            return;
        }

        float temp_factor = 0;
        temp_factor += gesture.deltaPinch * (0.003f);
        scale_factor += temp_factor;
        pos_factor += temp_factor;

        scale_factor = scale_factor <= 1 ? scale_factor : 1;
        scale_factor = scale_factor >= 0 ? scale_factor : 0;

        pos_factor = pos_factor <= 1 ? pos_factor : 1;
        pos_factor = pos_factor >= 0 ? pos_factor : 0;
        DoScalePosChange(scale_factor, pos_factor);
    }

    // 设置聚焦移动
    public void ModelFocusPlayForward(bool forward)
    {
        if (is_scale_anim || is_pos_anim || this.aim_trans == null)
        {
            return;
        }

        is_for_ward_anim = forward;
        mAmountPerDelta = Mathf.Abs(amountPerDelta);
        if (!forward) mAmountPerDelta = -mAmountPerDelta;
        is_pos_anim = true;
        is_scale_anim = true;

        if (is_for_ward_anim)
        {
            last_form_scale = from_scale;
            last_form_pos = from_pos;
            from_pos = this.aim_trans.transform.localPosition;
            from_scale = this.aim_trans.transform.localScale;
        }

        float pos_dis = Vector3.Distance(from_pos, to_pos);
        float sca_dis = Vector3.Distance(from_scale, to_scale);
        float now_pos_dis = Vector3.Distance(this.aim_trans.localPosition, to_pos);
        float now_sca_dis = Vector3.Distance(this.aim_trans.localScale, to_scale);

        scale_factor = 1 - (now_sca_dis / sca_dis);
        pos_factor = 1 - (now_pos_dis / pos_dis);
    }

    // 设置聚焦参数
    public void SetModelFocusParameter(Transform aim_trans, Vector3 to_pos, Vector3 from_pos, Vector3 to_scale, Vector3 from_scale, float all_move_time)
    {
        this.aim_trans = aim_trans;
        this.to_pos = to_pos;
        this.to_scale = to_scale;
        this.from_pos = from_pos;
        this.from_scale = from_scale;
        this.all_move_time = all_move_time;
    }
    //设置是否不能进行双指聚焦
    public void ModelFocusStatus(bool is_not_pinch = false)
    {
        this.is_not_pinch = is_not_pinch;
    }

    public void RemoveModelFocus()
    {
        Object.Destroy(this);
    }
}