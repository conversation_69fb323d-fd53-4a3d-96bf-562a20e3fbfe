-- 一元秒杀

function BillionSubsidyView:ReleaseYiYuanView()
    self.cur_show_model_seq = nil

    if self.yiyuan_acc_award_list then
        self.yiyuan_acc_award_list:DeleteMe()
        self.yiyuan_acc_award_list = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end
end

function BillionSubsidyView:LoadYiYuanView()
    if not self.yiyuan_acc_award_list then
        self.yiyuan_acc_award_list = AsyncListView.New(YiYuanAccAwardItem, self.node_list.yiyuan_acc_award_list)
        self.yiyuan_acc_award_list:SetRefreshCallback(BindTool.Bind(self.YiYuanListRefreshCallback, self))
    end

	if not self.show_model then
        self.show_model = OperationActRender.New(self.node_list.yiyuan_model_node)
        self.show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    XUI.AddClickEventListener(self.node_list.yiyuan_btn_buy, BindTool.Bind(self.OnBuy, self))
end


function BillionSubsidyView:ShowIndexYiYuanView()
    BillionSubsidyWGData.Instance:SetYiYuanDayFirstFlag(0)
    RemindManager.Instance:Fire(RemindName.BillionSubsidyYYHD)
end

function BillionSubsidyView:FlushYiYuanView()
    local cfg =  BillionSubsidyWGData.Instance:GetYiYuanCfg()
    if not cfg then
        return
    end

	if self.node_list.yiyuan_title then
		bundle, asset = ResPath.GetRawImagesPNG(cfg.title)
		self.node_list.yiyuan_title.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.yiyuan_title.raw_image:SetNativeSize()
		end)
    end
    
    if self.node_list.yiyuan_time then
        local is_buy = BillionSubsidyWGData.Instance:IsYiYuanBuy()
        local str = is_buy and Language.BillionSubsidy.YiYuanTime or Language.BillionSubsidy.YiYuanBuyTip
        self.node_list.yiyuan_time.text.text = string.format(str, BillionSubsidyWGData.Instance:GetYiYuanAwardDay())
    end

    if self.node_list.yiyuan_desc then
        local max_day = BillionSubsidyWGData.Instance:GetYiYuanAccAwardMax()
        self.node_list.yiyuan_desc.text.text = string.format(Language.BillionSubsidy.YiYuanAccTips, max_day, cfg.model_name)
    end
    
    self.node_list.yiyuan_price.text.text = string.format(Language.BillionSubsidy.YiYuanBuyBtn, cfg.price)
    local is_buy = BillionSubsidyWGData.Instance:IsYiYuanBuy()
    self.node_list.yiyuan_btn_buy:CustomSetActive(not is_buy)

    self:YiYuanFlushAccLogin()
    self:YiYuanShowModel()  
end

function BillionSubsidyView:YiYuanFlushAccLogin()
    local award_list =  BillionSubsidyWGData.Instance:GetYiYuanAccAward()
    local all_day = #award_list

    self.yiyuan_acc_award_list:SetDataList(award_list)
end

function BillionSubsidyView:YiYuanShowModel()
    local cur_show_model_cfg = BillionSubsidyWGData.Instance:GetYiYuanCfg()
    if not cur_show_model_cfg or (cur_show_model_cfg and self.cur_show_model_seq == cur_show_model_cfg.grade)  then 
        return 
    end

    self.cur_show_model_seq = cur_show_model_cfg.grade

    local display_data = {}
	display_data.should_ani = true
	display_data.can_drag = false
	if cur_show_model_cfg.model_show_itemid ~= 0 and cur_show_model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(cur_show_model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = cur_show_model_cfg.model_show_itemid
        end
        
        display_data.model_click_func = function ()
            TipWGCtrl.Instance:OpenItem({item_id = cur_show_model_cfg["model_show_itemid"]})
        end
	end
	
	display_data.bundle_name = cur_show_model_cfg["model_bundle_name"]
    display_data.asset_name = cur_show_model_cfg["model_asset_name"]
    local model_show_type = tonumber(cur_show_model_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	local scale = 1
	if cur_show_model_cfg.display_scale and cur_show_model_cfg.display_scale ~= "" then
        scale = cur_show_model_cfg["display_scale"]
        display_data.model_adjust_root_local_scale = scale
	end
	if cur_show_model_cfg.display_pos and cur_show_model_cfg.display_pos ~= "" then
		local pos = string.split(cur_show_model_cfg.display_pos, "|")
        display_data.model_adjust_root_local_position =  Vector3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
    end

	if cur_show_model_cfg.display_rotation and cur_show_model_cfg.display_rotation ~= "" then
		local rot = string.split(cur_show_model_cfg.display_rotation,"|")
        display_data.role_rotation = u3dpool.vec3(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
    end

    
	self.show_model:SetData(display_data)
end

function BillionSubsidyView:YiYuanListRefreshCallback(item_cell, cell_index)
    local max_num = BillionSubsidyWGData.Instance:GetYiYuanAccAwardMax()
    -- 可以显示7个
	self.node_list.yiyuan_btn_tag:CustomSetActive(max_num > 7 and cell_index < max_num)
end

function BillionSubsidyView:OnBuy()
    local cfg =  BillionSubsidyWGData.Instance:GetYiYuanCfg()
    if not cfg then
        return
    end
    -- print_error("cfg.price, cfg.rmb_type, cfg.rmb_seq = ", cfg.price, cfg.rmb_type, cfg.rmb_seq)
    RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.rmb_seq)
end

----------------------------------------- YiYuanAccAwardItem ---------------------------------------
YiYuanAccAwardItem = YiYuanAccAwardItem or BaseClass(BaseRender)

function YiYuanAccAwardItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function YiYuanAccAwardItem:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item)
        self.item_cell:SetCellBgEnabled(false)
        self.item_cell:SetIsUseRoundQualityBg(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnGet, self))
end

function YiYuanAccAwardItem:OnFlush()
    local data = self:GetData()
    if not IsEmptyTable(data) then
    end
    
    local day = data.day
    local is_first = day == 1
    local is_last = day == BillionSubsidyWGData.Instance:GetYiYuanAccAwardMax()
    local acc_login = BillionSubsidyWGData.Instance:GetYiYuanLoginDay()
    local can_get = acc_login >= day
    local is_got = BillionSubsidyWGData.Instance:IsYiYuanGetAward(day)
    self.node_list.left:CustomSetActive(not is_first)
    self.node_list.right:CustomSetActive(not is_last)
    self.node_list.left_light:CustomSetActive(not is_first and can_get)
    self.node_list.right_light:CustomSetActive(not is_last and (acc_login >= day + 1))
    self.node_list.tag_light:CustomSetActive(can_get)
    local desc = string.format(Language.BillionSubsidy.YiYuanDay, day)
    if is_got then
        desc = Language.BillionSubsidy.YiYuanGot
    elseif can_get then 
        desc = Language.BillionSubsidy.YiYuanCanGet
    end
    self.node_list.desc.text.text = desc
    
    self.node_list.btn_get:CustomSetActive(can_get and not is_got)

    local show_award = data.reward_item[0]
    self.item_cell:SetData(show_award)
    self.item_cell:SetQualityIconVisible(not is_last)
    self.item_cell:SetLingQuVisible(is_got)
    self.node_list.item_bg:CustomSetActive(is_last)
end

function YiYuanAccAwardItem:OnGet()
    BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.ONE_YUAN_REWARD)
end