function GameAssistantView:LoadAssistantWant()
    -- 活跃获得
	if not self.want_item_list then
		self.want_item_list = AsyncListView.New(Assistant<PERSON>ian<PERSON>u<PERSON><PERSON>, self.node_list.want_item_list)
	end

	if not self.want_rank_list then
		self.want_rank_list = AsyncListView.New(AssistantWantRankRender, self.node_list.want_rank_list)
	end

	if not self.assistant_want_show_alpha then
		self.assistant_want_show_alpha = self.node_list.assistant_want_show_root:GetOrAddComponent(typeof(UGUITweenAlpha))
	end

	if not self.assistant_want_rank_alpha then
		self.assistant_want_rank_alpha = self.node_list.want_rank_list_root:GetOrAddComponent(typeof(UGUITweenAlpha))
	end

    XUI.AddClickEventListener(self.node_list.want_btn_go_path, BindTool.Bind(self.OnClickWantGoTo, self))
end


function GameAssistantView:ReleaseAssistantWant()
    if self.want_item_list then
        self.want_item_list:DeleteMe()
        self.want_item_list = nil
    end

	if self.want_rank_list then
        self.want_rank_list:DeleteMe()
        self.want_rank_list = nil
    end

	self.assistant_want_show_alpha = nil
	self.assistant_want_rank_alpha = nil
end

function GameAssistantView:CloseAssistantWant()

end

function GameAssistantView:OpenAssistantWant()
	RankWGCtrl.Instance:SendRankReq(RankKind.Person, PersonRankType.Level, false)   -- 請求排行榜信息
	RankWGCtrl.Instance:SendRankReq(RankKind.Person, PersonRankType.Equip, false)   -- 請求排行榜信息
end

function GameAssistantView:ShowAssistantWant()
	
end

-- 刷新界面
function GameAssistantView:FlushAssistantWant(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
            self:FlushAssistantWantMessage()
		end
	end
end

function GameAssistantView:FlushAssistantWantMessage()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local list = nil
	local rank_kind = nil
	local rank_type = nil

	if self.show_index == TabIndex.assistant_want_exp then
        list = GameAssistantWGData.Instance:GetExpCfgListByDay(cur_day)
		rank_kind = RankKind.Person
		rank_type = PersonRankType.Level
		self.node_list.title_desc_txt.text.text = Language.GameAssistant.AssistantExp
		self.node_list.rank_list_type_txt.text.text = Language.GameAssistant.AssistantLvRankTitle
    else
        list = GameAssistantWGData.Instance:GetEquipCfgListByDay(cur_day)
		rank_kind = RankKind.Person
		rank_type = PersonRankType.Equip
		self.node_list.title_desc_txt.text.text = Language.GameAssistant.AssistantEquip
		self.node_list.rank_list_type_txt.text.text = Language.GameAssistant.AssistantEquipRankTitle
    end

    self.want_item_list:SetDataList(list)
	local data_list, _ = RankWGData.Instance:GetRankListAndMyStrengthInfomation(rank_kind, rank_type, true)
	self.want_rank_list:SetDataList(data_list)
	self.assistant_want_show_alpha:ResetToBeginning()
	self.assistant_want_rank_alpha:ResetToBeginning()

	local cur_rank_data = RankWGData.Instance:GetSelfValueData(rank_kind, rank_type)
	local cur_self_rank = cur_rank_data.self_rank
	if cur_rank_data and cur_self_rank and cur_self_rank > 0 and cur_self_rank <= MAX_RANK_NUM then
		self.node_list.my_rank_value.text.text = string.format(Language.Rank.RankValue1, cur_self_rank)
	else
		self.node_list.my_rank_value.text.text = Language.Rank.NoRank
	end
end

function GameAssistantView:OnClickWantGoTo()
	ViewManager.Instance:Open(GuideModuleName.Rank) 
end
-----------------------------------------------------------------------------
AssistantWantRankRender = AssistantWantRankRender or BaseClass(BaseRender)
function AssistantWantRankRender:OnFlush()
	if not self.data then
		return
	end

	local rank_index = self.data.rank_index
	local is_top_three = rank_index <= 3
	self.node_list.rank_image:CustomSetActive(is_top_three)
	self.node_list.rank_text:CustomSetActive(not is_top_three)

	self.node_list.rank_bg:CustomSetActive(is_top_three)
	self.node_list.rank_bg_1:CustomSetActive(not is_top_three)

	if is_top_three then
		self.node_list.rank_image.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank_index))
		self.node_list.rank_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_zqxz_di_"..rank_index))
	else
		self.node_list.rank_text.text.text = tostring(rank_index)
	end

	if self.data.vip_level then   -- 设置VIP等级
		local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
		self.node_list.vip_text:SetActive(self.data.vip_level >= 1)
		self.node_list.vip_text.text.text = is_hide_vip and "v" or "v".. self.data.vip_level
	else
		self.node_list.vip_text:SetActive(false)
	end

	local color = self.data.rank_index <= 3 and "#fff8bb" or "#fff8bb"
	self.node_list.rank_name.text.text = ToColorStr(self.data.user_name or self.data.guild_name, color)
	local zhiye_text, zhanli_text = RankWGData.Instance:GetRankItemStrengthInfomation(self.data)
	self.node_list.job_name.text.text = ToColorStr(zhiye_text, color)
	self.node_list.rank_value.text.text = ToColorStr(zhanli_text, color)
end

