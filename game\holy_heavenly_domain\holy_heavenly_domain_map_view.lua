HolyHeavenlyDomainMapView = HolyHeavenlyDomainMapView or BaseClass(SafeBaseView)

local NAV_TWEEN_SIZEDETAIL_X = 148
local NAV_TWEEN_SIZEDETAIL_Y = 700

function HolyHeavenlyDomainMapView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full

    self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_map")
    self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_map_top")
end

function HolyHeavenlyDomainMapView:LoadCallBack()
    self.node_list.city_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self))
    local tayget_y = self.node_list.city_tog.toggle.isOn and NAV_TWEEN_SIZEDETAIL_Y or 0
    self.node_list.right_nav_tween_root.rect.sizeDelta = u3dpool.vec2(NAV_TWEEN_SIZEDETAIL_X, tayget_y)
    self.node_list.btn_close_city_list:CustomSetActive(tayget_y > 0)

    if not self.citi_nav_list then
        self.citi_nav_list = AsyncListView.New(HDDCityNavListItemRender, self.node_list.citi_nav_list)
        self.citi_nav_list:SetDefaultSelectIndex(nil)
        self.citi_nav_list:SetSelectCallBack(BindTool.Bind1(self.OnClickCityItemCalllBack, self))
    end

    if not self.map_list then
        self.map_list = {}
        local res_async_loader = AllocResAsyncLoader(self, "holy_heavenly_domain_country_item")
        if res_async_loader then
            res_async_loader:Load("uis/view/holy_heavenly_domain_ui_prefab", "hhd_country_item", nil, function(obj)
                for i = 0, 33 do
                    local root = ResMgr:Instantiate(obj)
                    self.map_list[i] = HDDCounrtyItemRender.New(root)
                    self.map_list[i]:SetInstanceParent(self.node_list["Point" .. i].transform)
                end

                self:Flush()
            end)
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_privilege, BindTool.Bind(self.OnClickPrivilege, self))
    XUI.AddClickEventListener(self.node_list.btn_shop, BindTool.Bind(self.OnClickShop, self))
    XUI.AddClickEventListener(self.node_list.btn_person_rank, BindTool.Bind(self.OnClickPersonRank, self))
    XUI.AddClickEventListener(self.node_list.btn_country_rank, BindTool.Bind(self.OnClickCountryRank, self))
    XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind(self.OnClickRank, self))
    XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind(self.OnClickReward, self))
    XUI.AddClickEventListener(self.node_list.btn_xianli, BindTool.Bind(self.OnClickXianLi, self))
    XUI.AddClickEventListener(self.node_list.btn_close_city_list, BindTool.Bind(self.OnClickCloseCityList, self))
    XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickAdd, self))
    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTip, self))

    self.node_list.title_view_name.text.text = Language.HolyHeavenlyDomain.MapViewTitle
end

function HolyHeavenlyDomainMapView:ReleaseCallBack()
    if self.citi_nav_list then
        self.citi_nav_list:DeleteMe()
        self.citi_nav_list = nil
    end

    if self.map_list then
        for k, v in pairs(self.map_list) do
            v:DeleteMe()
        end

        self.map_list = nil
    end

    self:CancleQueue()
end

function HolyHeavenlyDomainMapView:CloseCallBack()
    self:CancleQueue()
end

function HolyHeavenlyDomainMapView:ShowIndexCallBack()
    self:AutoSendRequest()
end

function HolyHeavenlyDomainMapView:AutoSendRequest()
    self:SendGetInfo()
    self:CancleQueue()

    self.quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.SendGetInfo, self), 10)
end

function HolyHeavenlyDomainMapView:CancleQueue()
    if self.quest ~= nil then
        GlobalTimerQuest:CancelQuest(self.quest)
        self.quest = nil
    end
end

function HolyHeavenlyDomainMapView:SendGetInfo()
    local city_table = {}
    local city_data = HolyHeavenlyDomainWGData.Instance:GetMapCitiDataList()
    for k, v in pairs(city_data) do
        if v.type ~= 4 then
            city_table[v.seq] = 1 -- 32位转int 方法计算从1开始的所以 + 1
        end
    end

    HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainGetCitiInfoOperate(city_table)
    HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.ROOM_INFO) --房间信息
end

function HolyHeavenlyDomainMapView:OnFlush()
    local shop_remind = HolyHeavenlyDomainWGData.Instance:GetShopRemind()
    self.node_list.btn_shop_remind:CustomSetActive(shop_remind == 1)
    local remind = HolyHeavenlyDomainWGData.Instance:GetScoreRewardRemind()
    self.node_list.btn_reward_remind:CustomSetActive(remind == 1)
    local big_citi_num, mid_city_num, small_city_num, _, siege_efficiency = HolyHeavenlyDomainWGData.Instance
    :GetMapTopInfo()
    self.node_list.gelou_num.text.text = big_citi_num
    self.node_list.yuan_num.text.text = mid_city_num
    self.node_list.gong_num.text.text = small_city_num
    self.node_list.cheng_num.text.text = siege_efficiency
    self.citi_nav_list:SetDataList(HolyHeavenlyDomainWGData.Instance:GetMapCitiDataList())
    local power, max_power = HolyHeavenlyDomainWGData.Instance:GetMapXianLiValue()
    self.node_list.btn_xianli_value.text.text = power .. "/" .. max_power
    self.node_list.map_cost_value.text.text = HolyHeavenlyDomainWGData.Instance:GetScore()

    self:FlushLine()
end

function HolyHeavenlyDomainMapView:FlushLine()
    local line_tab = {}
    local vo = GameVoManager.Instance:GetMainRoleVo()
    local data_list = HolyHeavenlyDomainWGData.Instance:GetMapCitiDataList()

    if self.map_list ~= nil and data_list ~= nil and vo ~= nil then
        local my_sid = RoleWGData.Instance:GetOriginalUSIDStr()

        for i = 0, #self.map_list do
            local data = data_list[i]
            local is_open = false

            if data ~= nil then
                local is_main_city = data.type == 4

                if is_main_city then
                    local camp = data.relegation_camp
                    local server_id = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(camp)
                    local my_server_id = RoleWGData.Instance:GetOriginalUSIDStr()
                    is_open = server_id and server_id == my_server_id or false
                else
                    local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(data.seq)
                    if not IsEmptyTable(city_info) then
                        local owner_usid = city_info.owner_usid
                        is_open = owner_usid and my_sid == owner_usid
                    end
                end

                local c_index = data.seq
                local t = HolyHeavenlyDomainWGData.Instance:GetCityConnectCityCache(c_index)

                for i = 1, #t do
                    local near_index = tonumber(t[i]) or 0
                    line_tab[c_index] = line_tab[c_index] or {}
                    line_tab[near_index] = line_tab[near_index] or {}
                    line_tab[c_index][near_index] = line_tab[c_index][near_index] or 0
                    line_tab[near_index][c_index] = line_tab[near_index][c_index] or 0

                    if line_tab[c_index][near_index] == 0 and is_open then
                        line_tab[c_index][near_index] = 1
                    end

                    if line_tab[near_index][c_index] == 0 and is_open then
                        line_tab[near_index][c_index] = 1
                    end
                end

                if self.map_list[i] then
                    self.map_list[i]:SetData(data)
                end
            end
        end
    end

    local key = ""
    local open_line_bundle, open_line_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_yqxianxz")
    local close_line_bundle, close_line_asset = ResPath.GetHolyHeavenlyDomainImg("a2_stsy_yqxianfag")
    local load_line_bundle, load_line_asset
    for k, v in pairs(line_tab) do
        for k1, v1 in pairs(v) do
            key = string.format("line_%s_%s", k, k1)
            if self.node_list[key] ~= nil then
                load_line_bundle = v1 == 0 and close_line_bundle or open_line_bundle
                load_line_asset = v1 == 0 and close_line_asset or open_line_asset
                self.node_list[key].image:LoadSprite(load_line_bundle, load_line_asset)
            end
        end
    end
end

function HolyHeavenlyDomainMapView:OnClickPrivilege()
    ViewManager.Instance:Open(GuideModuleName.HolyHeavenlyDomainPrivilegeView)
end

function HolyHeavenlyDomainMapView:OnClickShop()
    ViewManager.Instance:Open(GuideModuleName.HolyHeavenlyDomainShopView)
end

function HolyHeavenlyDomainMapView:OnClickPersonRank()
    HolyHeavenlyDomainWGCtrl.Instance:OpenRankView(true)
end

function HolyHeavenlyDomainMapView:OnClickCountryRank()
    HolyHeavenlyDomainWGCtrl.Instance:OpenRankView(false)
end

function HolyHeavenlyDomainMapView:OnClickRank()
    ViewManager.Instance:Open(GuideModuleName.HolyHeavenlyDomainView, TabIndex.holy_heavenly_domain_rank)
end

function HolyHeavenlyDomainMapView:OnClickReward()
    HolyHeavenlyDomainWGCtrl.Instance:OpenWarRewardView()
end

function HolyHeavenlyDomainMapView:OnClickXianLi()
    RuleTip.Instance:SetContent(Language.HolyHeavenlyDomain.XianLiContent, Language.HolyHeavenlyDomain.XianLiTitle)
end

function HolyHeavenlyDomainMapView:OnClickCloseCityList()
    if self.node_list.city_tog.toggle.isOn then
        self.node_list.city_tog.toggle.isOn = false
    end
end

function HolyHeavenlyDomainMapView:OnClickAdd()
    ViewManager.Instance:Open(GuideModuleName.HolyHeavenlyDomainPrivilegeView)
end

function HolyHeavenlyDomainMapView:OnClickTip()
    RuleTip.Instance:SetContent(Language.HolyHeavenlyDomain.MapTipContent, Language.HolyHeavenlyDomain.MapTipTitle)
end

function HolyHeavenlyDomainMapView:OnClickCityItemCalllBack(item)
    if not item or IsEmptyTable(item.data) then return end
    HolyHeavenlyDomainWGCtrl.Instance:OpenCountryTip(item.data)
end

function HolyHeavenlyDomainMapView:OnTogValueChange(is_on)
    if nil == is_on then return end
    local tayget_y = is_on and NAV_TWEEN_SIZEDETAIL_Y or 0
    self.node_list.right_nav_tween_root.rect:DOSizeDelta(Vector2(NAV_TWEEN_SIZEDETAIL_X, tayget_y), 0.5):OnComplete(function()
        if is_on then
            self.node_list.btn_close_city_list:CustomSetActive(is_on)
        end
    end)

    if not is_on then
        self.node_list.btn_close_city_list:CustomSetActive(is_on)
    end
end

------------------------------HDDCityNavListItemRender-----------------------
HDDCityNavListItemRender = HDDCityNavListItemRender or BaseClass(BaseRender)

function HDDCityNavListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.view.toggle.isOn = false
    self.node_list.text_name.text.text = self.data.city_name
    self.node_list.text_name_hl.text.text = self.data.city_name
end

------------------------------HDDCounrtyItemRender---------------------------
HDDCounrtyItemRender = HDDCounrtyItemRender or BaseClass(BaseRender)

function HDDCounrtyItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.Country, BindTool.Bind(self.OnClickCountry, self))
end

function HDDCounrtyItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_main_city = self.data.type == 4
    local city_icon_type = self.data.city_icon_type

    local relegation_camp = 16
    -- 主城无法被攻占
    if is_main_city then
        self.node_list.remind:CustomSetActive(false)
        local camp = self.data.relegation_camp
        local owner_usid = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(camp)
        relegation_camp = HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(owner_usid)
        self.node_list.root_pro:CustomSetActive(false)
    else
        local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(self.data.seq)
        if not IsEmptyTable(city_info) then
            self:FlushSlider(city_info)
            local owner_usid = city_info.owner_usid
            relegation_camp = HolyHeavenlyDomainWGData.Instance:GetCountryIdByServerUsid(owner_usid)
        else
            self.node_list.root_pro:CustomSetActive(false)
            self.node_list.desc_progress.text.text = ""
        end
    end

    local bundle, asset = ResPath.GetHolyHeavenlyDomainCountryImg(relegation_camp, city_icon_type)
    self.node_list.Country.image:LoadSprite(bundle, asset, function()
        self.node_list.Country.image:SetNativeSize()
    end)

    local pos_y = city_icon_type == 1 and -80 or -66
    RectTransform.SetAnchoredPositionXY(self.node_list.Country.rect, 0, pos_y)
    self.node_list.desc_country_name.text.text = self.data.city_name

    local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(relegation_camp)
    local no_camp = IsEmptyTable(camp_cfg)
    local relegation_camp_name = no_camp and Language.HolyHeavenlyDomain.Neutral or camp_cfg.camp_name
    local name_color = no_camp and HolyHeavenlyDomainCityNameColor[16] or
    HolyHeavenlyDomainCityNameColor[relegation_camp]
    local my_camp_seq = HolyHeavenlyDomainWGData.Instance:GetMyCampSeq()
    local is_my_server = not no_camp and my_camp_seq == relegation_camp
    local camp_name = is_my_server and ToColorStr(Language.HolyHeavenlyDomain.MyServer, name_color) or
    ToColorStr(relegation_camp_name, name_color)
    self.node_list.desc_server_name.text.text = camp_name

    local city_seq = self.data.seq
    local city_info = HolyHeavenlyDomainWGData.Instance:GetCityInfoBySeq(self.data.seq)
    local my_server = RoleWGData.Instance:GetOriginalUSIDStr()
    local owner_usid = city_info.owner_usid
    local capture_reward_flag = HolyHeavenlyDomainWGData.Instance:GetCaptureRewardFlagBySeq(city_seq) == 0
    local remind = owner_usid == my_server and capture_reward_flag
    self.node_list.remind:CustomSetActive(remind)

    local can_show_pro = HolyHeavenlyDomainWGData.Instance:IsCityCanSetTarget(city_seq, true)
    self.node_list.desc_progress:CustomSetActive(can_show_pro)
    self.node_list.root_pro:CustomSetActive(can_show_pro)

    -- 集合标记
    local is_assemble_city = HolyHeavenlyDomainWGData.Instance:GetAssembleCityFlagBySeq(city_seq)
    self.node_list.flag_jijie:CustomSetActive(is_assemble_city)

    -- 目标标记
    local is_target_city = HolyHeavenlyDomainWGData.Instance:IsConveneCity(city_seq)
    self.node_list.flag_target:CustomSetActive(is_target_city)
end

function HDDCounrtyItemRender:FlushSlider(city_info)
    local server_siege_list = city_info.server_siege_list
    local my_server = RoleWGData.Instance:GetOriginalUSIDStr()

    if IsEmptyTable(server_siege_list) then
        self.node_list.root_pro:CustomSetActive(false)
        self.node_list.desc_progress.text.text = "0%"
        return
    end

    local total_value = 0
    local slider_table = {}
    local atk_value = 0   -- 攻方
    local defen_value = 0 -- 守方

    local owner_usid = city_info.owner_usid
    for k, v in pairs(server_siege_list) do
        if v > 0 then
            total_value = total_value + v
            slider_table[k] = v

            local server_usid = HolyHeavenlyDomainWGData.Instance:GetCampServerIdByCampSeq(k)

            if server_usid ~= owner_usid then
                atk_value = atk_value + v
            else
                defen_value = defen_value + v
            end
        end
    end

    local target_angle = 0
    for i = 0, 15 do
        local index = i
        local value = 0

        if slider_table[index] and total_value > 0 then
            value = slider_table[index] / total_value
        else
            value = 0
        end

        local sider_index = index + 1
        self.node_list["img_pro_" .. sider_index].transform.localEulerAngles = Vector3(0, 0, target_angle)
        self.node_list["img_pro_" .. sider_index].image.fillAmount = value

        local angle = value * 360
        target_angle = target_angle + angle
    end

    local is_my_city = my_server == owner_usid
    local progress = 0

    if atk_value >= defen_value and atk_value > 0 then
        progress = 1
    elseif atk_value > 0 and defen_value > 0 then
        progress = atk_value / total_value
        progress = progress >= 1 and 1 or progress
    end

    local progress_str = (progress <= 0 or progress >= 1) and progress * 100 .. "%" or
    string.format("%.2f", progress * 100) .. "%"
    local desc_progress_sign = is_my_city and Language.HolyHeavenlyDomain.DefensiveDesc or
    Language.HolyHeavenlyDomain.AttackDesc
    local color = is_my_city and COLOR3B.RED or COLOR3B.GREEN
    local desc_progress = ToColorStr(string.format(desc_progress_sign, progress_str), color)
    self.node_list.desc_progress.text.text = desc_progress
    self.node_list.root_pro:CustomSetActive(true)
end

function HDDCounrtyItemRender:OnClickCountry()
    local is_mate_root = HolyHeavenlyDomainWGData.Instance:IsMateRoot()

    if not is_mate_root then
        HolyHeavenlyDomainWGCtrl.Instance:OpenCountryTip(self.data)
        return
    end

    local my_camp_id = HolyHeavenlyDomainWGData.Instance:GetMyCampSeq()
    if my_camp_id < 0 then
        HolyHeavenlyDomainWGCtrl.Instance:OpenCountryTip(self.data)
        return
    end

    local is_in_the_offseason = HolyHeavenlyDomainWGData.Instance:IsInTheOffSeason()

    if is_in_the_offseason then
        HolyHeavenlyDomainWGCtrl.Instance:OpenCountryTip(self.data)
        return
    end

    HolyHeavenlyDomainWGCtrl.Instance:OpenCountryTip(self.data)
    -- if HolyHeavenlyDomainWGData.Instance:IsSetConveneCity() then
    --     HolyHeavenlyDomainWGCtrl.Instance:OpenCountryTip(self.data)
    -- else
    --     HolyHeavenlyDomainWGCtrl.Instance:OpenSetTargetView()
    -- end
end
