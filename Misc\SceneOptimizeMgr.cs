﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class SceneOptimizeMgr
{
    private static bool isEnableGPuInstancing = true;

    private static int s_maxVertexTotalCountInSameMesh = 0;  //10000 - 30000
    private static int s_maxVertexCountInOneMesh = 5000;
    // 当A 个数*A顶点数 > maxVertexCountInSameMesh时将不合批 （0将不考虑该规则）
    // 当A顶点数>maxVertexCountInOneMesh时将不合批 （0将不考虑该规则）
    public static void SetStaticBatchThreadshold(int maxVertexTotalCountInSameMesh, int maxVertexCountInOneMesh)
    {
        s_maxVertexTotalCountInSameMesh = maxVertexTotalCountInSameMesh;
        s_maxVertexCountInOneMesh = maxVertexCountInOneMesh;
    }

    public static void StaticBatch()
    {
        Scene scene = SceneManager.GetActiveScene();
        string sceneName = scene.IsValid() ? scene.name : string.Empty;

        if (!sceneName.EndsWith("_Main"))
        {
            Debug.LogErrorFormat("场景名字请以_Main结尾, {0}", sceneName);
        }

        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize)
        {
            Debug.LogErrorFormat("没有找到SceneOptimize，将引发严重性能问题, {0}", sceneName);
            return;
        }

        optimize.StaticBatch(s_maxVertexTotalCountInSameMesh, s_maxVertexCountInOneMesh);
    }

    public static void SyncLODLightMap()
    {
        Scene scene = SceneManager.GetActiveScene();
        string sceneName = scene.IsValid() ? scene.name : string.Empty;

        if (!sceneName.EndsWith("_Main"))
        {
            Debug.LogErrorFormat("场景名字请以_Main结尾, {0}", sceneName);
        }

        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize)
        {
            Debug.LogErrorFormat("没有找到SceneOptimize，将引发严重性能问题, {0}", sceneName);
            return;
        }

        optimize.SyncLODLightMap();
    }

    private static SceneOptimize GetSceneOptimizer()
    {
        GameObject gameObj = GameObject.Find("Main/Models");
        if (null == gameObj) return null;

        SceneOptimize optimize = gameObj.transform.GetComponentInParent<SceneOptimize>();
        if (null == optimize) return null;

        return optimize;
    }

    public static void SetCenterPoint(Transform centerPoint)
    {
        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize) return;

        optimize.SetCenterPoint(centerPoint);
    }

    public static void SetCullDistances(float[] layerDistances)
    {
        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize) return;

        optimize.SetCullingDistances(layerDistances);
    }

    public static void SetCullDistanceFactor(float factor)
    {
        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize) return;

        optimize.SetCullingDistanceFactor(factor);
    }

    public static bool GetIsEnableGPUInstancing()
    {
        if (!SystemInfo.supportsInstancing)
        {
            return false;
        }

        return isEnableGPuInstancing;
    }

    public static void SetIsEnableGPUInstancing(bool _isEnableGPuInstancing)
    {
        isEnableGPuInstancing = _isEnableGPuInstancing;
    }


    public static void SetCullEnable(bool enable, bool immediately = false)
    {
        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize) return;

        optimize.SetCullEnable(enable, immediately);
    }

    public static void UpdatePlanarReflectionShow(int materialQuality)
    {
        if (materialQuality < 0 || materialQuality > 3) return;

        SceneOptimize optimize = GetSceneOptimizer();
        if (null == optimize) return;

        optimize.UpdatePlanarReflectionShow(materialQuality);
    }
}
