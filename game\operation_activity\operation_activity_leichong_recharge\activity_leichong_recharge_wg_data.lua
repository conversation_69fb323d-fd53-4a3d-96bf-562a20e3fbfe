OperationLeiChongRechargeWGData = OperationLeiChongRechargeWGData or BaseClass()
OperationLeiChongRechargeWGData.ConfigPath= "config/auto_new/operation_activity_limit_total_recharge_auto"
function OperationLeiChongRechargeWGData:__init()
	if OperationLeiChongRechargeWGData.Instance then
		ErrorLog("[OperationLeiChongRechargeWGData] Attemp to create a singleton twice !")
	end
	OperationLeiChongRechargeWGData.Instance = self

	self:InitLeiChongRechargeData()
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE, {[1] = OPERATION_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowLCRechargeRedPoint, self))
	RemindManager.Instance:Register(RemindName.OperationTotalRecharge, BindTool.Bind(self.IsShowLCRechargeRedPoint, self))

	self.cur_model_index = -1
end

function OperationLeiChongRechargeWGData:__delete()
	OperationLeiChongRechargeWGData.Instance = nil 
	RemindManager.Instance:UnRegister(RemindName.OperationTotalRecharge)
end

function OperationLeiChongRechargeWGData:SetLeiChongRechargeData(protocol)
	if self.LeiChongData == nil then
		self.LeiChongData = {}
	end

	self.LeiChongData.cur_xianyu = protocol.cur_xianyu
	self.LeiChongData.lc_open_serverday = protocol.lc_open_serverday
	self.LeiChongData.dangwei_info = protocol.dangwei_info
end

function OperationLeiChongRechargeWGData:InitLeiChongRechargeData()
	self.operation_leichong_recharge_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_limit_total_recharge_auto")
	self.leichong_reward_cfg = self.operation_leichong_recharge_cfg.reward
	self.interface_cfg = self.operation_leichong_recharge_cfg.interface
	self.leichong_param_cfg = self.operation_leichong_recharge_cfg.config_param
	self.leichong_model_cfg = ListToMap(self.operation_leichong_recharge_cfg.model, "grade", "id")
end

--获取累充奖励排行数据
function OperationLeiChongRechargeWGData:GetLeiChongRewardList()
	local list = {}
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		local data = {}
		data.cfg = v
		data.ID = v.ID
		data.receive_state = self:GetLeiChongReceiveState(v.ID)
		if data.receive_state == TianShenRoadRewardState.KLQ then
			data.sort = 0
		elseif data.receive_state == TianShenRoadRewardState.BKL then
			data.sort = 1
		else
			data.sort = 2
		end
		table.insert(list, data)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "ID"))
	return list
end

--获取领取状态
function OperationLeiChongRechargeWGData:GetLeiChongReceiveState(index)
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil and self.LeiChongData.dangwei_info[index] ~= nil then
		return self.LeiChongData.dangwei_info[index]
	else
		return 0
	end
end

--当前充值仙玉
function OperationLeiChongRechargeWGData:GetOwnXianYu()
	if self.LeiChongData ~= nil then
		return self.LeiChongData.cur_xianyu
	end
	return 0
end

--活动开启时服务器开服天数
function OperationLeiChongRechargeWGData:GetOpenServerdayByProtocol()
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE)
	return open_day
end

function OperationLeiChongRechargeWGData:GetLeiChongRewardCfg()
	local grade = self:GetGradeByServerDay()
	local reward_cfg = {}
	for i,v in ipairs(self.leichong_reward_cfg) do
		if grade == v.grade then
			table.insert(reward_cfg, v)
		end
	end
	return reward_cfg
end

function OperationLeiChongRechargeWGData:GetLeiChongModelCfg()
	local old_model_index = self.cur_model_index
	local cur_model_cfg = {}
	local grade = self:GetGradeByServerDay()
	local cur_index = 0

	if self.LeiChongData == nil then
		return {}
	end

	for k, v in pairs(self.LeiChongData.dangwei_info) do
		if v ~= 2 then
			cur_index = k
			break
		end
	end

	local model_cfg = self.leichong_model_cfg[grade] or {}
	local list = SortTableKey(model_cfg)
	for k, v in pairs(list) do
		if cur_index <= v.id then
			self.cur_model_index = grade .. v.id
			cur_model_cfg = v
			break
		end
	end

	if IsEmptyTable(cur_model_cfg) or cur_index == 0 then
		cur_model_cfg = list[#list]
		self.cur_model_index = grade .. list[#list].id
	end

	if old_model_index == self.cur_model_index then
		return {}
	end

	return cur_model_cfg
end

function OperationLeiChongRechargeWGData:ResetLeiChongModelIndex()
	self.cur_model_index = -1
end

--获取当前档需要充值的仙玉
function OperationLeiChongRechargeWGData:GetNeedRechargeXianYU()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		if cur_xianyu < v.stage_value then
			local pre_stage_value = 0
			if i > 1 then
				pre_stage_value = cfg[i-1].stage_value
			end
			return v.stage_value - cur_xianyu, v.stage_value, pre_stage_value
		end
	end
	return 0, cfg[#cfg].stage_value, cfg[#cfg].stage_value
end

--是否已达成
function OperationLeiChongRechargeWGData:IsRechargeTargetFinish()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	return cur_xianyu >= cfg[#cfg].stage_value
end

--根据服务器开服天数获得活动配置grade
function OperationLeiChongRechargeWGData:GetGradeByServerDay()
    local open_day = self:GetOpenServerdayByProtocol()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几   
	for k,v in pairs(self.leichong_param_cfg) do
		if open_day >= v.start_server_day and open_day <= v.end_server_day and week == v.week_index then
			return v.grade
		end
	end
	return 0
end

--根据服务器开服天数获得界面配置
function OperationLeiChongRechargeWGData:GetInterfaceByServerDay()
    local open_day =  self:GetOpenServerdayByProtocol()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
	for k,v in pairs(self.leichong_param_cfg) do
		if open_day >= v.start_server_day and open_day <= v.end_server_day and week == v.week_index then
			return v.interface
		end
	end
	return -1
end

--根据界面配置获取界面信息
function OperationLeiChongRechargeWGData:GetShouChongViewCfgByInterface()
	local interface = self:GetInterfaceByServerDay()
	for k,v in pairs(self.interface_cfg) do
		if v.interface == interface then
			return v
		end
	end
end

function OperationLeiChongRechargeWGData:GetLeiChongProgress(leichong_value, cfg_list)  --进度条
	local cur_progress = 0
	local cfg = cfg_list
	if next(cfg) == nil or leichong_value == nil then
		return cur_progress
	end
	local progress_list = {0.2, 0.4, 0.7, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list	
		local cur_need = tonumber(cfg[seq]) or 0
		local next_need = tonumber(cfg[seq + 1]) or tonumber(cfg[#cfg])
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		
		if leichong_value > cur_need and leichong_value <= next_need then
			cur_progress = (leichong_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif leichong_value > tonumber(cfg[#cfg]) then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end

--活动结束时间
function OperationLeiChongRechargeWGData:GetActEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE)
	if activity_info and activity_info.end_time then
		return activity_info.end_time
	end
	return -1
end

function OperationLeiChongRechargeWGData:IsShowLCRechargeRedPoint()
	local state = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE)
	if not state then
		return 0
	end

	if not self:GetActCanOpen() then
		return 0
	end
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil then
		for k,v in ipairs(self.LeiChongData.dangwei_info) do
			if v == TianShenRoadRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end


--根据服务器开服天数获得活动开启等级
function OperationLeiChongRechargeWGData:GetOpenLevelByServerDay()
	local return_level = RoleWGData.GetRoleMaxLevel()
	local open_day =  self:GetOpenServerdayByProtocol()
	if open_day ~= -1 then
		for k,v in pairs(self.leichong_param_cfg) do
			if open_day >= v.start_server_day and open_day <= v.end_server_day then
				return v.open_level
			end
		end
	end
	return return_level
end

function OperationLeiChongRechargeWGData:GetActCanOpen()
    local interface = self:GetInterfaceByServerDay()
    if interface == -1 then
        return false
    end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local open_level = self:GetOpenLevelByServerDay()
	if open_level then
		if vo.level >= open_level then
			return true
		end
		return false
	end
end

