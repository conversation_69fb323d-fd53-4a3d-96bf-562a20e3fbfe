TaskChainGuardLogic = TaskChainGuardLogic or BaseClass(CommonFbLogic)
function TaskChainGuardLogic:__init()
	self.role_pos_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.MainRolePosChange,self))
	self.role_guaji_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.MainRoleGuaJiChange,self))
	self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE,BindTool.Bind(self.ObjCreate,self))


	self.is_check_pos = false
	self.is_set_model = false
	self.guaji_pos_x = nil
	self.guaji_pos_y = nil
end

function TaskChainGuardLogic:__delete()
	if self.role_pos_event then
		GlobalEventSystem:UnBind(self.role_pos_event)
		self.role_pos_event = nil
	end

	if self.role_guaji_event then
		GlobalEventSystem:UnBind(self.role_guaji_event)
		self.role_guaji_event = nil
	end

	if self.obj_create_event then
		GlobalEventSystem:UnBind(self.obj_create_event)
		self.obj_create_event = nil
	end

	self.is_check_pos = false
	self.is_set_model = false
end

function TaskChainGuardLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()
	self.is_check_pos = false
	self.is_set_model = true

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local cfg = OperationTaskChainWGData.Instance:GetGuardDungeonInfo()
	if cfg then
		local pos_tab = Split(cfg.shouhu_ret_pos, "|")
		self.guaji_pos_x = tonumber(pos_tab[1])
		self.guaji_pos_y = tonumber(pos_tab[2])
	end

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
end

function TaskChainGuardLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	OperationTaskChainWGCtrl.Instance:ResetInfoView()
	OperationTaskChainWGData.Instance:ResetGuardInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()
	self.is_check_pos = false
	self.is_set_model = false

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end
end

function TaskChainGuardLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TaskChainGuardLogic:GetGuajiCharacter()
	local is_need_stop = true
	local target_obj = nil
	local main_role = Scene.Instance:GetMainRole()

	if main_role ~= nil and not self.is_check_pos then
		target_obj = self:GetMonster()
	end

	return target_obj, nil, is_need_stop
end

function TaskChainGuardLogic:GetMonster()
	local distance_limit = 1000000
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	local cfg = OperationTaskChainWGData.Instance:GetGuardDungeonInfo()
	if main_role ~= nil and cfg ~= nil then
		local shouhu_pos_str = cfg.shouhu_pos
		local pos_tab = Split(shouhu_pos_str, "|")
		if pos_tab ~= nil and next(pos_tab) ~= nil then
			local pos = {x = tonumber(pos_tab[1]), y = tonumber(pos_tab[2])}
			obj = Scene.Instance:SelectNearMonsterByLimit(distance_limit, nil, pos, cfg.shouhu_radius, true, true, true)
		end
	end
	
	return obj
end

-- 怪物是否是敌人
function TaskChainGuardLogic:IsMonsterEnemy(target_obj, main_role)
	local is_enemy = false
	local info = OperationTaskChainWGData.Instance:GetGuardInfo()
	if info ~= nil and target_obj ~= nil and not target_obj:IsDeleted() then
		if info.shouhu_objid ~= target_obj:GetObjId() then
			is_enemy = true
		end
	end

	return is_enemy
end

function TaskChainGuardLogic:GetGuajiPos()
	return self.guaji_pos_x, self.guaji_pos_y
end

function TaskChainGuardLogic:CanGetMoveObj()
	return false
end

function TaskChainGuardLogic:OnObjBloodChange(obj_id)
	if obj_id ~= nil and not self.is_check_pos then
		local info = OperationTaskChainWGData.Instance:GetGuardInfo()
		local cfg = OperationTaskChainWGData.Instance:GetGuardDungeonInfo()
		local main_role = Scene.Instance:GetMainRole()
		local obj = Scene.Instance:GetObj(obj_id)
		if info ~= nil and cfg ~= nil and main_role ~= nil and obj_id == info.shouhu_objid then
			if GuajiCache.guaji_type ~= GuajiType.None and GuajiCache.guaji_type ~= GuajiType.Temporary then
				local shouhu_pos_str = cfg.shouhu_pos
				local pos_tab = Split(shouhu_pos_str, "|")
				if pos_tab ~= nil and next(pos_tab) ~= nil then
					local pos = {x = tonumber(pos_tab[1]), y = tonumber(pos_tab[2])}
					local m_x, m_y = main_role:GetLogicPos()
					local my_dis = GameMath.GetDistance(pos.x, pos.y, m_x, m_y)
					if cfg.shouhu_radius * cfg.shouhu_radius < my_dis then
						self.is_check_pos = true
						GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
						local move_pos = {x = pos.x + math.random(1, cfg.shouhu_radius), y = pos.y + math.random(1, cfg.shouhu_radius)}
						local function call()
							GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
						end

						GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), move_pos.x, move_pos.y, 1, nil, nil, nil, call)
					end
				end
			end
		end
	end
end

function TaskChainGuardLogic:MainRolePosChange(x, y)
	if not self.is_check_pos or GuajiCache.guaji_type == GuajiType.None or GuajiCache.guaji_type == GuajiType.Temporary then
		return
	end

	local cfg = OperationTaskChainWGData.Instance:GetGuardDungeonInfo()
	local main_role = Scene.Instance:GetMainRole()
	if cfg ~= nil and main_role ~= nil then
		local shouhu_pos_str = cfg.shouhu_pos
		local pos_tab = Split(shouhu_pos_str, "|")
		if pos_tab ~= nil and next(pos_tab) ~= nil then
			local pos = {x = tonumber(pos_tab[1]), y = tonumber(pos_tab[2])}
			local m_x, m_y = main_role:GetLogicPos()
			local my_dis = GameMath.GetDistance(pos.x, pos.y, m_x, m_y)
			if cfg.shouhu_radius * cfg.shouhu_radius >= my_dis then
				self.is_check_pos = false
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
				main_role:StopMove()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		else
			self.is_check_pos = false
		end
	else
		self.is_check_pos = false
	end
end

function TaskChainGuardLogic:MainRoleGuaJiChange(guaji_type)
	if guaji_type ~= nil or guaji_type == GuajiType.None or guaji_type == GuajiType.Temporary then
		self.is_check_pos = false
	end
end

function TaskChainGuardLogic:CheckObjIsIgnoreSelect(target_obj, select_type)
	if SceneWGData:TargetSelectIsScene(select_type) then
		if target_obj ~= nil and not target_obj:IsDeleted() and target_obj:IsMonster() then
			return not self:IsEnemy(target_obj)
		else
			return false
		end
	else
		return false
	end
end

function TaskChainGuardLogic:IsNeedCheckModel()
	return self.is_set_model
end

function TaskChainGuardLogic:SetIsNeedCheckModel(value)
	self.is_set_model = value
end

function TaskChainGuardLogic:ObjCreate(obj)
	if not self:IsNeedCheckModel() then
		return
	end

	if obj ~= nil and not obj:IsDeleted() and obj:IsMonster() then
		local info = OperationTaskChainWGData.Instance:GetGuardInfo()
		if info ~= nil then
			if info.shouhu_objid ~= obj:GetObjId() then
				local follow_ui = obj:GetFollowUi()
				if follow_ui ~= nil then
					self:SetIsNeedCheckModel(false)

					follow_ui:ForceSetVisible(true)
					follow_ui:SetHpVisiable(true)

					local bundle_str = nil
					local vo = obj:GetVo()
					if vo.hp > 0 then
						local max_hp = vo.max_hp == 0 and 1 or vo.max_hp
						local hp = vo.hp / max_hp
						bundle_str = OperationTaskChainWGData.Instance:GetGuardBubbleStr(hp)
					end

					OperationTaskChainWGCtrl.Instance:CheckGuardBubble(info.shouhu_objid, bundle_str)
				end
			end
		end
	end
end