TipsEasyFlyView = TipsEasyFlyView or BaseClass(SafeBaseView)

function TipsEasyFlyView:__init(view_name)
	self.view_name = "TipsEasyFlyView"
	self.view_layer = UiLayer.Pop
	self.can_do_fade = false
	self:AddViewResource(0, "uis/view/tips/easyflyicon_prefab", "tips_easy_fly_icon")
end

function TipsEasyFlyView:__delete()
	
end

function TipsEasyFlyView:LoadCallBack()
	self:InitPanel()
end

function TipsEasyFlyView:ReleaseCallBack()
	CountDownManager.Instance:RemoveCountDown("tips_easy_fly_view")
end

function TipsEasyFlyView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "act_icon" then
			self:FlushActIcon(v)
		end
	end
end

function TipsEasyFlyView:InitPanel()
	CountDownManager.Instance:AddCountDown("tips_easy_fly_view", nil, function ()
			self:Close()
		end, nil, 10, 1)
end

function TipsEasyFlyView:FlushActIcon(param)
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(param.act_type)
	local main_ui_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(param.act_type)
	if not act_cfg or not main_ui_act_btn then
		self:Close()
		return
	end

	local icon_res = act_cfg.res_name
	local name_res = act_cfg.act_name_res
	if act_cfg.act_type == ACTIVITY_TYPE.FIRST_CHONGZHI then--首充入口特殊处理
		-- local icon_list = {"a2_zjm_icon_sc1", "a1_btn_zc", "a1_btn_hc"}
    	-- local sc_index = self:GetFirstRechargeBtnResIndex()
		-- icon_res = "a2_zjm_icon_sc1"--icon_list[sc_index]
	end

	local bundle_1,asset_1 = ResPath.GetF2MainUIImage(icon_res)
	self.node_list.act_img.image:LoadSprite(bundle_1, asset_1, function()
 		self.node_list.act_img.image:SetNativeSize()
 	end)
	
	if name_res and name_res ~= "" then
		local bundle_2,asset_2 = ResPath.GetF2MainUIImage(name_res)
		self.node_list.act_name.image:LoadSprite(bundle_2, asset_2, function()
			self.node_list.act_name.image:SetNativeSize()
		end)
	end

	main_ui_act_btn:SetCanvasGroupVal(0)
	local btn_pos = main_ui_act_btn:GetIconPos()
	local target_pos = TipWGCtrl.Instance:TurnBaseCellPos(btn_pos, self:GetRootNode())
	local tween_move = self.node_list.panel_root.rect:DOAnchorPos(target_pos, 1.5)
	tween_move:OnComplete(function ()
		self:Close()
		main_ui_act_btn:SetCanvasGroupVal(1)
	end)
end

function TipsEasyFlyView:GetFirstRechargeBtnResIndex()
	local max_stage = 3
	local cur_show_index = 1
	for i = max_stage, 1, -1 do
		if ServerActivityWGData.Instance:GetRCGearFlag(i - 1) then
			cur_show_index = i
			break
		end 
	end
	return cur_show_index
end