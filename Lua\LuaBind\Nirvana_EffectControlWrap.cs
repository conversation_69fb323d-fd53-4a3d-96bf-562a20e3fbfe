﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_EffectControlWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.EffectControl), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("WaitF<PERSON>h", WaitFinsh);
		<PERSON><PERSON>unction("WaitFadout", WaitFadout);
		<PERSON><PERSON>Function("Play", Play);
		L.RegFunction("Pause", Pause);
		L<PERSON>RegFunction("Resume", Resume);
		L.RegFunction("Stop", Stop);
		<PERSON>.RegFunction("Reset", Reset);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		L.<PERSON>ar("IsNoScalable", get_IsNoScalable, null);
		L.RegVar("IsLooping", get_IsLooping, set_IsLooping);
		<PERSON><PERSON>ar("IsPaused", get_IsPaused, null);
		<PERSON><PERSON>("IsStopped", get_IsStopped, null);
		<PERSON><PERSON>("Duration", get_Duration, set_Duration);
		<PERSON><PERSON>("Fadeout", get_Fadeout, set_Fadeout);
		L.RegVar("PlaybackSpeed", get_PlaybackSpeed, set_PlaybackSpeed);
		L.RegVar("FadeoutEvent", get_FadeoutEvent, set_FadeoutEvent);
		L.RegVar("FinishEvent", get_FinishEvent, set_FinishEvent);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WaitFinsh(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.WaitFinsh(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WaitFadout(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.WaitFadout(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			obj.Play();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Pause(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			obj.Pause();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Resume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			obj.Resume();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			obj.Stop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsNoScalable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			bool ret = obj.IsNoScalable;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsNoScalable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsLooping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			bool ret = obj.IsLooping;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsLooping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsPaused(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			bool ret = obj.IsPaused;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsPaused on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsStopped(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			bool ret = obj.IsStopped;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsStopped on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			float ret = obj.Duration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Fadeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			float ret = obj.Fadeout;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Fadeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PlaybackSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			float ret = obj.PlaybackSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PlaybackSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FadeoutEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FinishEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsLooping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsLooping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsLooping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Duration = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Fadeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Fadeout = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Fadeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PlaybackSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EffectControl obj = (Nirvana.EffectControl)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.PlaybackSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PlaybackSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FadeoutEvent(IntPtr L)
	{
		try
		{
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EffectControl.FadeoutEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EffectControl'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.FadeoutEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.FadeoutEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FinishEvent(IntPtr L)
	{
		try
		{
			Nirvana.EffectControl obj = (Nirvana.EffectControl)ToLua.CheckObject(L, 1, typeof(Nirvana.EffectControl));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EffectControl.FinishEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EffectControl'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.FinishEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.FinishEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

