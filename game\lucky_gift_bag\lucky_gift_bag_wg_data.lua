LuckyGiftBagWgData = LuckyGiftBagWgData or BaseClass()

function LuckyGiftBagWgData:__init()
	if LuckyGiftBagWgData.Instance then
		Error<PERSON><PERSON>("[LuckyGiftBagWgData] attempt to create singleton twice!")
		return
	end
	LuckyGiftBagWgData.Instance =self

	self.lucky_gift_bag_config = ConfigManager.Instance:GetAutoConfig("cross_lucky_gift_auto")
	self.other_cfg = self.lucky_gift_bag_config.other
	self.open_day_cfg = ListToMap(self.lucky_gift_bag_config.open_day, "grade")
	self.reward_pool_cfg = ListToMapList(self.lucky_gift_bag_config.reward_pool, "grade", "seq")
	self.grade_cfg = ListToMapList(self.lucky_gift_bag_config.grade, "grade", "seq", "mode")
	self.rate_show = ListToMap(self.lucky_gift_bag_config.rate_show, "grade")
	self.lucky_grade_reward = ListToMapList(self.lucky_gift_bag_config.lucky_grade_reward, "grade")
	self.throughtrain_cfg = ListToMapList(self.lucky_gift_bag_config.rmb_buy, "grade")
	self.lucky_double_cfg = ListToMap(self.lucky_gift_bag_config.lucky_double, "grade")

	self.lucky_value = 0
	self.daily_reward_flag = 1 
	self.rate_record_list_item = {}
	self.lucky_gift_buyResultView_info = {}
	self.current_grade = 1
	self.get_rate_count = 0
	self.comfirm_select_grade = 1
	self.comfirm_select_seq = 1
	self.before_prchase_mode = 1
	self.current_lucky_double_grade = 0
	self.chongzhi_add_lucky = 0
	self.rank_info = {}
	self.lucky_grade_reward_flag = {}
	self.rmb_buy_flag = {}
	RemindManager.Instance:Register(RemindName.LuckyGiftBagDailyReward, BindTool.Bind(self.GetDailyRewardRemind, self))
end

function LuckyGiftBagWgData:__delete()
	LuckyGiftBagWgData.Instance = nil
	self.daily_reward_flag = nil
	self.lucky_value = nil
	self.rate_record_list_item = nil
	self.lucky_gift_buyResultView_info = nil
	self.current_grade = nil
	self.get_rate_count = nil
	self.comfirm_select_grade = nil
	self.comfirm_select_seq = nil
	self.comfirm_gift_name = nil
	self.before_prchase_mode = nil
	self.rank_info = nil

	self.grade_cfg = nil
	self.reward_pool_cfg = nil
	self.open_day_cfg = nil
	self.other_cfg = nil
	self.lucky_gift_bag_config = nil
	self.lucky_grade_reward_flag = nil
	self.lucky_grade_reward = nil
	RemindManager.Instance:UnRegister(RemindName.LuckyGiftBagDailyReward)
end

function LuckyGiftBagWgData:GetLickyValue()
	return self.lucky_value or 0
end

function LuckyGiftBagWgData:GetBuyResultInfo()
	return self.lucky_gift_buyResultView_info
end

-- 后端发下来 0是可领取  1是已领取
function LuckyGiftBagWgData:GetDailyRewardRemind()
	if self.daily_reward_flag then
		return self.daily_reward_flag == 0 and 1 or 0
	end
	return 0
end

function LuckyGiftBagWgData:GetDailyRewardFlag()
	return self.daily_reward_flag
end

function LuckyGiftBagWgData:GetRateRecordList()
	return self.rate_record_list_item
end

function LuckyGiftBagWgData:GetOtherCfg()
	return self.other_cfg and self.other_cfg[1]  or {}
end

function LuckyGiftBagWgData:GetCurrentRateShow()
	local current_grede = self.current_grade or 1
	return self.rate_show[current_grede] or {}
end

function LuckyGiftBagWgData:GetOpenDayInfo()
	return self.open_day_cfg[self.current_grade]
end

function LuckyGiftBagWgData:GetRewardProgress()
	if not IsEmptyTable(self.open_day_cfg) and not IsEmptyTable(self.open_day_cfg[self.current_grade]) then
		return self.get_rate_count, self.open_day_cfg[self.current_grade].rate_count
	end

	return nil, nil
end

function LuckyGiftBagWgData:GetRewardPoolInfo()
	return self.reward_pool_cfg[self.current_grade] or {}
end

function LuckyGiftBagWgData:GetCurRewardPoolInfo()
	return self.lucky_grade_reward[self.current_grade] or {}
end

function LuckyGiftBagWgData:GetRewardPoolItemDataList(grade, seq)
	local item_data = {}
	local rare_liat = {}
	local allDataList = self.reward_pool_cfg[grade][seq]

	for k, v in pairs(allDataList) do
		if v.item then
			if v.is_rare == 0 then
				table.insert(item_data, v.item)
			else
				table.insert(rare_liat, 1, v.item)
			end
		end
	end

	item_data = SortDataByItemColor(item_data)

	--策划要求大奖显示在前面
	for k, v in pairs(rare_liat) do
		table.insert(item_data, 1, v)
	end

	return item_data
end

function LuckyGiftBagWgData:GetGradeCfgInfo(grade, seq)
	return self.grade_cfg and self.grade_cfg[grade] and self.grade_cfg[grade][seq] or {}
end

function LuckyGiftBagWgData:GetBeforePrchaseMode()
	return self.before_prchase_mode
end

function LuckyGiftBagWgData:GetRankData()
	return self.rank_info
end

function LuckyGiftBagWgData:GetLuckyGradeRewardInfo()
	local data_list = {}
	local value_difference = {}
	local cur_reward_list = self:GetCurRewardPoolInfo()

	for k, v in pairs(cur_reward_list) do
		local data = {}
		data.reward_info = v
		data.reward_flag = self.lucky_grade_reward_flag[32 - v.seq] or 0
		data.is_final_reward = false
		table.insert(data_list, data)
		local last_need_lucky_value = cur_reward_list[k - 1] and cur_reward_list[k - 1].need_lucky or 0
		local lucky_value_difference = v.need_lucky - last_need_lucky_value
		table.insert(value_difference, {lucky_value_grade = lucky_value_difference >= 0 and lucky_value_difference or 0 })
	end

	local slider_value = self:GetLuckyGradeRewardProgressValue(data_list, #data_list, value_difference)
	local start_reward_data = data_list[1] or {}
	local final_reward_data = table.remove(data_list) or {}
	return data_list, start_reward_data, final_reward_data, slider_value
end

function LuckyGiftBagWgData:GetLuckyGradeRewardProgressValue(data_list, count, value_difference)
	local slider_value = 0

	if IsEmptyTable(data_list) then
		return slider_value
	end

	-- 档位所占slider比例
	local all_slider_item_count = 3 * (count - 1)
	local first_item_pop = 1 --第一小段占一小格
	local dinal_item_pop = 2 --最后一段占两小格
	local per_item_pop = 3
	local per_slider_value =  1 / all_slider_item_count
	local lucky_value = self.lucky_value
	local lucky_value_grade = 1

	if lucky_value <= 0 then
		return 0
	elseif lucky_value > data_list[count].reward_info.need_lucky then
		return 1
	end

	-- 计算所在区间
	for k, v in ipairs(data_list) do
		if lucky_value <= v.reward_info.need_lucky then
			lucky_value_grade = k
			break
		end
	end

	if lucky_value_grade == 1 then
		slider_value = per_slider_value * (lucky_value / data_list[1].reward_info.need_lucky)
	elseif lucky_value_grade == #data_list then
		local last_lucky_need_value = lucky_value - data_list[count - 1].reward_info.need_lucky
		local front_value = per_slider_value * (all_slider_item_count - dinal_item_pop)
		local current_grade_diff = value_difference[lucky_value_grade].lucky_value_grade
		slider_value = front_value + per_slider_value * dinal_item_pop * last_lucky_need_value / current_grade_diff
	else
		local last_lucky_need_value = lucky_value - data_list[lucky_value_grade - 1].reward_info.need_lucky
		local current_grade_diff = value_difference[lucky_value_grade].lucky_value_grade
		slider_value = per_slider_value * (1 + per_item_pop * (lucky_value_grade - 2 +  last_lucky_need_value / current_grade_diff))
	end
	
	return slider_value
end

function LuckyGiftBagWgData:GetThroughTrainDataList()
	return self.throughtrain_cfg[self.current_grade] or {}
end

function LuckyGiftBagWgData:GetThroughTrainTaskFlag(seq)
	return (self.rmb_buy_flag[32 - seq] or 0) == 1
end

function LuckyGiftBagWgData:GetCurrentLuckyDoubleGradeAndCfg()
	local current_double_cfg = self.lucky_double_cfg[self.current_lucky_double_grade]
	local current_add_lucky_per = not IsEmptyTable(current_double_cfg) and current_double_cfg.lucky_per or 1
	local cfg = self.lucky_double_cfg[self.current_lucky_double_grade + 1]
	local current_cfg = not IsEmptyTable(cfg) and cfg or {}
	return self.current_lucky_double_grade, current_cfg, self.lucky_double_cfg[#self.lucky_double_cfg], current_add_lucky_per
end

function LuckyGiftBagWgData:GetCurrentLuckyDoubleAddPer()
	local per = 1

	if self.current_lucky_double_grade <= 0 then
		return per
	elseif not IsEmptyTable(self.lucky_double_cfg[self.current_lucky_double_grade]) then
		per = self.lucky_double_cfg[self.current_lucky_double_grade].lucky_per
	else
		per = self.lucky_double_cfg[#self.lucky_double_cfg].lucky_per
	end

	return per
end

function LuckyGiftBagWgData:GetRechargeAddLuckyValye()
	return self.chongzhi_add_lucky, self.other_cfg[1].chongzhi_add_lucky_max
end

------------------------------------------Set-------------------------------------------------
function LuckyGiftBagWgData:SetCurrentSelectInfo(grade, seq, gift_name)
	self.comfirm_select_grade = grade
	self.comfirm_select_seq = seq
	self.comfirm_gift_name = gift_name
end

function LuckyGiftBagWgData:GetCurrentSelectInfo()
	return self.comfirm_select_grade, self.comfirm_select_seq, self.comfirm_gift_name
end

function LuckyGiftBagWgData:AddRateRecordListItem(protocol)
	if self.rate_record_list_item then
		table.insert(self.rate_record_list_item, protocol.rate_record_list)
	end
end

function LuckyGiftBagWgData:SetRateRecordListItem(protocol)
	self.rate_record_list_item = protocol.rate_record_list
end

function LuckyGiftBagWgData:SetLuckyGiftBaseInfo(protocol)
	self.current_grade = protocol.grade or 1
	self.get_rate_count = protocol.get_rate_count or 0
end

function LuckyGiftBagWgData:SetResultInfo(protocol)
	local reward_data = {}
	reward_data.grade = protocol.grade
	reward_data.seq = protocol.seq
	reward_data.mode = protocol.mode
	reward_data.add_lucky = protocol.add_lucky
	reward_data.item_count = protocol.item_count
	reward_data.item_list = protocol.item_list
	reward_data.show_mode = 1
	self.lucky_gift_buyResultView_info = reward_data
end

-- 每日奖励
function LuckyGiftBagWgData:SetDailyRewardInfo()
	local item_list = SortDataByItemColor(self.other_cfg[1].daily_reward_item)
	local reward_data = {}
	reward_data.item_count = #item_list
	reward_data.item_list = item_list
	reward_data.show_mode = 2
	self.lucky_gift_buyResultView_info = reward_data
end

function LuckyGiftBagWgData:SetLuckyGiftInfo(protocol)
	self.lucky_value = protocol.lucky_value or 0
	self.daily_reward_flag = protocol.daily_reward_flag or 1
	self.lucky_grade_reward_flag = bit:d2b(protocol.lucky_grade_reward_flag or 0)
	self.rmb_buy_flag =  bit:d2b(protocol.rmb_buy_flag or 0)
	self.current_lucky_double_grade = protocol.lucky_double_grade
	self.chongzhi_add_lucky = protocol.chongzhi_add_lucky
end

function LuckyGiftBagWgData:SetBeforePrchaseMode(prchase_mode)
	self.before_prchase_mode = prchase_mode
end

function LuckyGiftBagWgData:SetRankData(protocol)
	local rank_info = {}
	rank_info.count = protocol.count
	rank_info.rank_item_list = protocol.rank_item_list
	self.rank_info = rank_info
end