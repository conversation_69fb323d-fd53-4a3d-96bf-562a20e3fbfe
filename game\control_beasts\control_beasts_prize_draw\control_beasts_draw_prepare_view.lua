ControlBeastsDrawPrepareView = ControlBeastsDrawPrepareView or BaseClass(SafeBaseView)

local EFFECT_DELAY_TIME = 0.7
local EFFECT_DURATION_TIME = 3

local FLY_QUALITY_EFFECT = {
	[1] = "UI_huanshou_xg_%s_lv",
	[2] = "UI_huanshou_xg_%s_lan",
	[3] = "UI_huanshou_xg_%s_zi",
	[4] = "UI_huanshou_xg_%s_huang",
	[5] = "UI_huanshou_xg_%s_hong",
	[6] = "UI_huanshou_xg_%s_fen", -- fen 缺的品质后面补
	[7] = "UI_huanshou_xg_%s_jin",	-- jin 缺的品质后面补
	[8] = "UI_huanshou_xg_%s_cai",	-- hc 缺的品质后面补
}

function ControlBeastsDrawPrepareView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Window
	self.view_layer = UiLayer.PopTop
	self.is_safe_area_adapter = true
	self.view_cache_time = 0

	local common_bundle = "uis/view/common_panel_prefab"
	--self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "control_beasts_draw_prepare_view")
end

function ControlBeastsDrawPrepareView:ReleaseCallBack()
	self.event_listener = nil
	self.bell_skeleton = nil
	self.effect_fly_list = nil

	if self.get_guide_ui_event then
        FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ControlBeastsDrawPrepareView, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end
end

function ControlBeastsDrawPrepareView:LoadCallBack()
	-- local bundle, asset = ResPath.GetRawImagesPNG("a3_hs_cj_bj2")
	-- if self.node_list.RawImage_tongyong then
	-- 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end

	if not self.effect_fly_list then
		self.effect_fly_list = {}
		local node_num = self.node_list.effect_fly_root.transform.childCount
		for i = 1, node_num do
			self.effect_fly_list[i] = self.node_list.effect_fly_root:FindObj("effect_fly_" .. i)
		end
	end

	self.bell_skeleton = self.node_list["spine_bell"].gameObject:GetComponent("SkeletonGraphic")
	self.node_list["txt_draw_prepare_tips"].text.text = Language.ContralBeasts.BeastsDrawTips
	XUI.AddClickEventListener(self.node_list["btn_click_mask"], BindTool.Bind(self.StartPlayDraw, self))
    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ControlBeastsDrawPrepareView, self.get_guide_ui_event)

	self:PlayShowUp()
end

function ControlBeastsDrawPrepareView:CloseCallBack(param_t)
	self.is_play_draw = nil
end

function ControlBeastsDrawPrepareView:SetDataAndOpen(data)
	self.draw_data = data
	self.is_play_draw = false
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
		self:ResetState()
	end
end

function ControlBeastsDrawPrepareView:OnFlush(param_t)
	if not self.draw_data or not self.draw_data.result_item_list then
		return
	end

	local list_info = self.draw_data.result_item_list
	list_info = ControlBeastsPrizeDrawWGData.Instance:SortItem(list_info)
	for i, v in ipairs(self.effect_fly_list) do
		local data = list_info[i]
		v:SetActive(data ~= nil)

		if data then
			local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(data.item_id)
			if not cfg then
				cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			end
			local color = cfg and (cfg.beast_color or cfg.color) or 1
			bundle, asset = ResPath.GetEffectUi(string.format(FLY_QUALITY_EFFECT[color], i))
			v:ChangeAsset(bundle, asset)
		end
	end
end

function ControlBeastsDrawPrepareView:ResetState()
	if self.bell_skeleton ~= nil and (not IsNil(self.bell_skeleton)) then
		self.bell_skeleton.AnimationState:SetAnimation(0, "idle", true)
	end

	if self.node_list ~= nil and self.node_list.bell_eff then
		self.node_list["bell_eff"]:SetActive(true)
	end

	if self.node_list ~= nil and self.node_list.draw_tips then
		self.node_list["draw_tips"].canvas_group.alpha = 1
	end

	if self.node_list ~= nil and self.node_list.draw_tips then
		self.node_list["effect_fly_root"]:SetActive(false)
	end
end

function ControlBeastsDrawPrepareView:PlayShowUp()
	local bundle, asset = ResPath.GetEffect(Ui_Effect.UI_huanshou_qiehuan)
	EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["effect_root"].transform, 2)
	self.node_list["spine_bell"].canvas_group.alpha = 0
	self.node_list["draw_tips"].canvas_group.alpha = 0
	self.node_list["RawImage_hsbg"].canvas_group.alpha = 0

	self.node_list["spine_bell"].canvas_group:DoAlpha(0.002, 1, 0.5):SetEase(DG.Tweening.Ease.InSine):SetDelay(0.5):OnComplete(function()
		self.node_list.btn_click_mask:CustomSetActive(true)
	end)
	self.node_list["draw_tips"].canvas_group:DoAlpha(0, 1, 0.5):SetEase(DG.Tweening.Ease.InSine):SetDelay(0.5)
	self.node_list["RawImage_hsbg"].canvas_group:DoAlpha(0, 1, 0.5):SetEase(DG.Tweening.Ease.InSine):SetDelay(0.35)

	Transform.SetLocalScaleXYZ(self.node_list["spine_root"].transform, 0.8, 0.8, 0.8)
	self.node_list["spine_root"].rect:DOScale(1, 0.4):SetEase(DG.Tweening.Ease.InSine):SetDelay(0.5)
end

function ControlBeastsDrawPrepareView:StartPlayDraw()
	if self.is_play_draw then
		return
	end
	self.is_play_draw = true
	
	local bundle, asset = ResPath.GetEffect(Ui_Effect.UI_zhendizhan_shuibowen)
	EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["effect_root"].transform, 1)
	self.bell_skeleton.AnimationState:SetAnimation(0, "click", false)
	self.node_list["spine_bell"].canvas_group:DoAlpha(1, 0, 0.8):SetEase(DG.Tweening.Ease.InSine)
	self.node_list["draw_tips"].canvas_group:DoAlpha(1, 0, 0.2):SetEase(DG.Tweening.Ease.InSine)

	ReDelayCall(self, function ()
		self.node_list["bell_eff"]:SetActive(false)
		bundle, asset = ResPath.GetEffect(Ui_Effect.UI_huanshou_choujiang)
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["effect_root"].transform, EFFECT_DURATION_TIME)
		self.node_list["effect_fly_root"]:SetActive(true)
	end, EFFECT_DELAY_TIME, "control_beasts_draw_play_effect")

	ReDelayCall(self, function ()
		if self.draw_data then
			self.draw_data.callback()
		end
	end, EFFECT_DELAY_TIME + EFFECT_DURATION_TIME, "control_beasts_draw_over")

	-- 防止未被关闭
	ReDelayCall(self, BindTool.Bind(self.Close, self), 5, "control_beasts_draw_view_close")
end

function ControlBeastsDrawPrepareView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "spine_bell_guide_root" then
        local jump_fun = function()
            self:StartPlayDraw()
        end
        return self.node_list[ui_name], jump_fun
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end