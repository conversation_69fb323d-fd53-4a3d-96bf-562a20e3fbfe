LandWarFbPersonView = LandWarFbPersonView or BaseClass(SafeBaseView)

function LandWarFbPersonView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg()

    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_land_war_fb_view")
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_top_panel")
end

function LandWarFbPersonView:__delete()
end

function LandWarFbPersonView:OpenCallBack()

end

function LandWarFbPersonView:CloseCallBack()

end

function LandWarFbPersonView:LoadCallBack()
    self.load_complete = false
    self:InitMapList()

    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTipBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_gameplay_description, BindTool.Bind(self.OnClickGamePlayDescriptionBtn, self))

    self.node_list.desc_kf_time.text.text = Language.LandWarFbPersonView.OpenDayDesc
    self.node_list.desc_stage_description.text.text = Language.LandWarFbPersonView.TiYanDesc

    local bundle, asset = ResPath.GetPositionalWarfareImg("a3_zdz_nd_3")
    self.node_list.difficulity_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.difficulity_icon.image:SetNativeSize()
    end)

    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.LandWarFbPersonView, self.get_guide_ui_event)
end

function LandWarFbPersonView:ReleaseCallBack()
    if self.map_list then
        if self.map_list.base_city_map then
            for i, u in pairs(self.map_list.base_city_map) do
                u:DeleteMe()
            end

            self.map_list.base_city_map = nil
        end

        if self.map_list.normal_city_num then
            for i, u in pairs(self.map_list.normal_city_num) do
                u:DeleteMe()
            end 

            self.map_list.normal_city_num = nil
        end

        self.map_list = nil
    end

    if self.map_show_pos_tween then
        self.map_show_pos_tween:Kill()
        self.map_show_pos_tween = nil
    end

    self.load_complete = nil

    if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.LandWarFbPersonView, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
    end
end

function LandWarFbPersonView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == "nor_point_1" then
        return self.node_list["nor_point_1"], BindTool.Bind1(self.OnClickGuideCountry, self)
    elseif ui_name == "nor_point_2" then
        return self.node_list["nor_point_2"], BindTool.Bind1(self.OnClickGuideCountry, self)
    elseif ui_name == "spe_guide_1" then
        return self.node_list["spe_guide_1"], BindTool.Bind1(self.OnClickGuideCountry, self)
    elseif ui_name == "spe_guide_2" then
        return self.node_list["spe_guide_2"], BindTool.Bind1(self.OnClickGuideReward, self)
    elseif ui_name == "btn_close_window" then
        return self.node_list["btn_close_window"],  BindTool.Bind1(self.OnClickGuideClose, self)
    end

    return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function LandWarFbPersonView:ShowIndexCallBack()
    if self.map_show_pos_tween then
        self.map_show_pos_tween:Kill()
        self.map_show_pos_tween = nil
    end
    
    self.map_show_pos_tween = self.node_list["map_content"].rect:DOAnchorPos(Vector2(193, -228), 0.5)
end

function LandWarFbPersonView:InitMapList()
    if not self.map_list then
        self.map_list = {}

        local res_async_loader = AllocResAsyncLoader(self, "land_war_fb_city_item")
        if res_async_loader then
            res_async_loader:Load("uis/view/positional_warfare_ui_prefab", "land_war_fb_city_item", nil, function(obj)              
                local base_city_map = {}
                for i = 0, 3 do
                    local root = ResMgr:Instantiate(obj)
                    base_city_map[i] = LandWarFbCityCell.New(root)
                    base_city_map[i]:SetInstanceParent(self.node_list["base_point_" .. i].transform)
                end

                self.map_list.base_city_map = base_city_map

                local nor_city_map = {}
                for i = 0, 8 do
                    local root = ResMgr:Instantiate(obj)
                    nor_city_map[i] = LandWarFbCityCell.New(root)
                    nor_city_map[i]:SetInstanceParent(self.node_list["nor_point_" .. i].transform)
                end

                self.map_list.nor_city_map = nor_city_map
                
                self.load_complete = true
                self:Flush()
            end)
        end
    end
end

function LandWarFbPersonView:OnFlush()
    if not self.load_complete then
        return
    end

    self:SetMapData()
end

function LandWarFbPersonView:SetMapData()
    local base_city_data, nor_city_data = LandWarFbPersonWGData.Instance:GetCityCfgDataInfo()

    if not IsEmptyTable(base_city_data) then
        for k, v in pairs(base_city_data) do
            self.map_list.base_city_map[v.land_seq]:SetData(v)
        end
    end

    if not IsEmptyTable(nor_city_data) then
        for k, v in pairs(nor_city_data) do
            self.map_list.nor_city_map[v.seq]:SetData(v)
        end
    end

    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    local line_name_1 = ""
    local line_name_2 = ""
    if cur_stage == LandWarFbPersonWGData.STAGE.ZREO then
        line_name_1 = "a3_zdz_line_3_3_1_3"
        line_name_2 = "a3_zdz_line_8_3_1"
    elseif cur_stage == LandWarFbPersonWGData.STAGE.ONE then
        line_name_1 = "a3_zdz_line_3_3_1_2"
        line_name_2 = "a3_zdz_line_8_3_1_3"
    elseif cur_stage == LandWarFbPersonWGData.STAGE.TWO then
        line_name_1 = "a3_zdz_line_3_3_1_3"
        line_name_2 = "a3_zdz_line_8_3_1"
    elseif cur_stage == LandWarFbPersonWGData.STAGE.THREE then
        line_name_1 = "a3_zdz_line_3_3_1_2"
        line_name_2 = "a3_zdz_line_8_3_1_2"
    else
    
    end

    local load_line_bundle_1, load_line_asset_1 = ResPath.GetRawImagesPNG(line_name_1)
    local load_line_bundle_2, load_line_asset_2 = ResPath.GetRawImagesPNG(line_name_2)

    self.node_list["line_1"].raw_image:LoadSprite(load_line_bundle_1, load_line_asset_1)
    self.node_list["line_2"].raw_image:LoadSprite(load_line_bundle_2, load_line_asset_2)
end

function LandWarFbPersonView:OnClickTipBtn()
    RuleTip.Instance:SetContent(Language.LandWarFbPersonView.RuleContent, Language.LandWarFbPersonView.RuleTitle)
end

function LandWarFbPersonView:OnClickGamePlayDescriptionBtn()
    LandWarFbPersonWGCtrl.Instance:OpenGamePlayDescriptionView()
end

function LandWarFbPersonView:OnClickGuideCountry()
    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    local base_city_data, nor_city_data = LandWarFbPersonWGData.Instance:GetCityCfgDataInfo()
    if cur_stage >= LandWarFbPersonWGData.STAGE.THREE then
        TipWGCtrl.Instance:ShowSystemMsg(Language.LandWarFbPersonView.ErrorStr[1])
        return
    end

    if not nor_city_data then
        return
    end

    if cur_stage == LandWarFbPersonWGData.STAGE.ZREO then
        if nor_city_data[1] then
            LandWarFbPersonWGCtrl.Instance:OpenBossInfonView(nor_city_data[1])
            return
        end
    elseif cur_stage == LandWarFbPersonWGData.STAGE.ONE then
        if nor_city_data[2]then
            LandWarFbPersonWGCtrl.Instance:OpenBossInfonView(nor_city_data[2])
            return
        end
    elseif cur_stage == LandWarFbPersonWGData.STAGE.TWO then
        if nor_city_data[1]then
            LandWarFbPersonWGCtrl.Instance:OpenBossInfonView(nor_city_data[1])
            return
        end
    end
end

function LandWarFbPersonView:OnClickGuideReward()
    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    -- local pass_reward_flag = LandWarFbPersonWGData.Instance:GetPassRewardIsGet()
    local is_get_all_pass_reward = LandWarFbPersonWGData.Instance:IsGetAllPassReward()
    -- if cur_stage == LandWarFbPersonWGData.STAGE.THREE and not pass_reward_flag then
    if cur_stage == LandWarFbPersonWGData.STAGE.THREE and not is_get_all_pass_reward then
        LandWarFbPersonWGCtrl.Instance:SendLandWarFBOperate(LAND_WAR_FB_OPERATE_TYPE.FETCH_PASS_REWARD, 1)
        -- local data_list = LandWarFbPersonWGData.Instance:GetCityLandRewardCfg()

        -- for k, v in pairs(data_list) do
        --     if not LandWarFbPersonWGData.Instance:GetPassRewardIsGetBySeq(v.seq) then
        --         LandWarFbPersonWGCtrl.Instance:SendLandWarFBOperate(LAND_WAR_FB_OPERATE_TYPE.FETCH_PASS_REWARD, v.seq)  
        --     end
        -- end
    end
end

function LandWarFbPersonView:OnClickGuideClose()
    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.SCENE_TYPE_LAND_WAR_FB and cur_stage == LandWarFbPersonWGData.STAGE.THREE then
        FuBenWGCtrl.Instance:SendLeaveFB()
    end

    self:Close()
end
------------------------------LandWarFbCityCell---------------------------
LandWarFbCityCell = LandWarFbCityCell or BaseClass(BaseRender)

function LandWarFbCityCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.country_click, BindTool.Bind(self.OnClickCountry, self))
    XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind(self.OnClickReward, self))
end

function LandWarFbCityCell:__delete()
    if self.shake_tween then
        self.shake_tween:Kill()
        self.shake_tween = nil
    end
end

function LandWarFbCityCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_camp_point = nil ~= self.data.camp
    local point_bundle, point_asset
    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    -- local pass_reward_flag = LandWarFbPersonWGData.Instance:GetPassRewardIsGet()
    if is_camp_point then
        local sign_bundle, sign_asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt1_" .. self.data.camp_sign)
        self.node_list.country_sign.image:LoadSprite(sign_bundle, sign_asset, function()
            self.node_list.country_sign.image:SetNativeSize()
        end)

        point_bundle, point_asset = ResPath.GetRawImagesPNG("a3_zdz_ct_bg" .. self.data.camp_sign)
        self.node_list.desc_country_name.text.text = self.data.camp_name
    else
        point_bundle, point_asset = ResPath.GetRawImagesPNG("a3_zdz_jz_" .. self.data.land_icon)
        self.node_list.desc_country_name.text.text = self.data.land_name

        if self.data.seq == 0 then
            self.node_list.city_owner_bg:CustomSetActive(true)
            self.node_list.desc_city_owner_name.text.text = Language.LandWarFbPersonView.CampName[0]

            local bundle, asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt_1")
            self.node_list.city_owner_icon.image:LoadSprite(bundle, asset, function ()
                self.node_list.city_owner_icon.image:SetNativeSize()
            end)
            self.node_list.desc_server_name.text.text = string.format(Language.LandWarFbPersonView.NumberOfBoss, 0)
        elseif self.data.seq == 3 then  
            self.node_list.city_owner_bg:CustomSetActive(true)
            self.node_list.desc_city_owner_name.text.text = Language.LandWarFbPersonView.CampName[3]

            local bundle, asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt_4")
            self.node_list.city_owner_icon.image:LoadSprite(bundle, asset, function ()
                self.node_list.city_owner_icon.image:SetNativeSize()
            end)

            self.node_list.desc_server_name.text.text = string.format(Language.LandWarFbPersonView.NumberOfBoss, 0)
        elseif self.data.seq == 1 then
            local num = 0
            local show_city_owner_bg = false
            local city_owner_name = ""
            local owner_icon_name = ""
            if cur_stage == LandWarFbPersonWGData.STAGE.ZREO or cur_stage == LandWarFbPersonWGData.STAGE.TWO then
                local monster_list = LandWarFbPersonWGData.Instance:GetCurStageMonsterListData(cur_stage + 1)
                if not IsEmptyTable(monster_list) then
                    for k, v in pairs(monster_list) do
                        local active = LandWarFbPersonWGData.Instance:GetBossActive(v.seq)
                        if active then
                            num = num + 1
                        end
                    end
                end

                if cur_stage == LandWarFbPersonWGData.STAGE.TWO then
                    show_city_owner_bg = true
                    city_owner_name = Language.LandWarFbPersonView.CampName[1]
                    owner_icon_name = "a3_zdz_tt_2"
                end
            elseif cur_stage == LandWarFbPersonWGData.STAGE.ONE or cur_stage == LandWarFbPersonWGData.STAGE.THREE then
                show_city_owner_bg = true
                city_owner_name = Language.LandWarFbPersonView.CampName[0]
                owner_icon_name = "a3_zdz_tt_1"
            end

            self.node_list.desc_server_name.text.text = string.format(Language.LandWarFbPersonView.NumberOfBoss, num)
            self.node_list.city_owner_bg:CustomSetActive(show_city_owner_bg)
            if show_city_owner_bg then
                self.node_list.desc_city_owner_name.text.text = city_owner_name
                local bundle, asset = ResPath.GetPositionalWarfareImg(owner_icon_name)
                self.node_list.city_owner_icon.image:LoadSprite(bundle, asset, function ()
                    self.node_list.city_owner_icon.image:SetNativeSize()
                end)
            end
        elseif self.data.seq == 2 then   
            local num = 0
            local show_city_owner_bg = false
            local city_owner_name = ""
            local owner_icon_name = ""

            if cur_stage == LandWarFbPersonWGData.STAGE.ZREO then

            elseif cur_stage == LandWarFbPersonWGData.STAGE.ONE then
                local monster_list = LandWarFbPersonWGData.Instance:GetCurStageMonsterListData(cur_stage + 1)
                if not IsEmptyTable(monster_list) then
                    for k, v in pairs(monster_list) do
                        local active = LandWarFbPersonWGData.Instance:GetBossActive(v.seq)
                        if active then
                            num = num + 1
                        end
                    end
                end
            elseif cur_stage == LandWarFbPersonWGData.STAGE.TWO or cur_stage == LandWarFbPersonWGData.STAGE.THREE then
                show_city_owner_bg = true
                city_owner_name = Language.LandWarFbPersonView.CampName[0]
                owner_icon_name = "a3_zdz_tt_1"
            end

            self.node_list.desc_server_name.text.text = string.format(Language.LandWarFbPersonView.NumberOfBoss, num)
            self.node_list.city_owner_bg:CustomSetActive(show_city_owner_bg)
            self.node_list.spe_tip_1:CustomSetActive(cur_stage == LandWarFbPersonWGData.STAGE.TWO)
            if show_city_owner_bg then
                self.node_list.desc_city_owner_name.text.text = city_owner_name
                local bundle, asset = ResPath.GetPositionalWarfareImg(owner_icon_name)
                self.node_list.city_owner_icon.image:LoadSprite(bundle, asset, function ()
                    self.node_list.city_owner_icon.image:SetNativeSize()
                end)
            end
        elseif self.data.seq == 4 then 
            self.node_list.desc_server_name.text.text = string.format(Language.LandWarFbPersonView.NumberOfBoss, 0)
            self.node_list.city_owner_bg:CustomSetActive(true)
            self.node_list.desc_city_owner_name.text.text = Language.LandWarFbPersonView.CampName[1]
            local bundle, asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt_2")
            self.node_list.city_owner_icon.image:LoadSprite(bundle, asset, function ()
                self.node_list.city_owner_icon.image:SetNativeSize()
            end)
        else
            self.node_list.desc_server_name.text.text = string.format(Language.LandWarFbPersonView.NumberOfBoss, 0)
            self.node_list.city_owner_bg:CustomSetActive(false)
        end
    end

    if self.shake_tween then
        self.shake_tween:Restart()
    else
        if self.shake_tween then
            self.shake_tween:Kill()
            self.shake_tween = nil
        end
        
        self.shake_tween = DG.Tweening.DOTween.Sequence()
        UITween.ShakeAnimi(self.node_list.btn_reward.transform, self.shake_tween)
    end

    local has_reward_land = (self.data.seq == 2 or self.data.seq == 1 or self.data.seq == 0)
    local is_get_reward = LandWarFbPersonWGData.Instance:GetPassRewardIsGetBySeq(self.data.seq)
    self.node_list.btn_reward:SetActive(has_reward_land and cur_stage == LandWarFbPersonWGData.STAGE.THREE and (not is_get_reward))

    
    self.node_list.country_icon.raw_image:LoadSprite(point_bundle, point_asset, function()
        self.node_list.country_icon.raw_image:SetNativeSize()
    end)
    self.node_list.country_sign:CustomSetActive(is_camp_point)
    self.node_list.info:CustomSetActive(not is_camp_point)

end

function LandWarFbCityCell:OnClickCountry()
    if IsEmptyTable(self.data) then
        return
    end

    local is_camp_point = nil ~= self.data.camp

    if is_camp_point then
        return
    end

    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    if cur_stage >= LandWarFbPersonWGData.STAGE.THREE then
        TipWGCtrl.Instance:ShowSystemMsg(Language.LandWarFbPersonView.ErrorStr[1])
        return
    end
    local land_cfg = LandWarFbPersonWGData.Instance:GetCurStageLandCfg(cur_stage + 1)
    if cur_stage == LandWarFbPersonWGData.STAGE.ZREO then
        if self.data.seq == 1 then
            LandWarFbPersonWGCtrl.Instance:OpenBossInfonView(self.data)
            return
        end
    elseif cur_stage == LandWarFbPersonWGData.STAGE.ONE then
        if self.data.seq == 2 then
            LandWarFbPersonWGCtrl.Instance:OpenBossInfonView(self.data)
            return
        end
    elseif cur_stage == LandWarFbPersonWGData.STAGE.TWO then
        if self.data.seq == 1 then
            LandWarFbPersonWGCtrl.Instance:OpenBossInfonView(self.data)
            return
        end
    end

    TipWGCtrl.Instance:ShowSystemMsg(Language.LandWarFbPersonView.ErrorStr[0])
end

function LandWarFbCityCell:OnClickReward()
    if IsEmptyTable(self.data) then
        return
    end

    LandWarFbPersonWGCtrl.Instance:SendLandWarFBOperate(LAND_WAR_FB_OPERATE_TYPE.FETCH_PASS_REWARD, self.data.seq)  
end