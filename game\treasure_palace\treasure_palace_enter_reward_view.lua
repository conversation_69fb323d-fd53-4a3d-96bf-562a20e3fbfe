function TreasurePalaceView:ZBKEnterRewardLoadIndexCallBack()
    if not self.enter_game_reward_list then
        self.enter_game_reward_list = AsyncListView.New(ItemCell, self.node_list.enter_game_reward_list)
        self.enter_game_reward_list:SetStartZeroIndex(true)
    end

    if not self.abd_enter_reward_model then
        self.abd_enter_reward_model = OperationActRender.New(self.node_list["abd_enter_reward_model"])
        self.abd_enter_reward_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    XUI.AddClickEventListener(self.node_list.btn_enter_game, BindTool.Bind(self.OnClickEnterReward, self))
end

function TreasurePalaceView:ZBKEnterRewardReleaseCallBack()
    if self.enter_game_reward_list then
        self.enter_game_reward_list:DeleteMe()
        self.enter_game_reward_list = nil
    end

    if self.abd_enter_reward_model then
        self.abd_enter_reward_model:DeleteMe()
        self.abd_enter_reward_model = nil
    end
end

function TreasurePalaceView:ZBKEnterRewardShowIndexCallBack()
    
end

function TreasurePalaceView:ZBKEnterRewardOnFlush()
    local other_cfg = TreasurePalaceData.Instance:GetOtherCfg()

    if not IsEmptyTable(other_cfg) then
        self.enter_game_reward_list:SetDataList(other_cfg.reward_item)
    end

    local is_get_flag = TreasurePalaceData.Instance:IsGetEnterReward()
    self.node_list.btn_enter_game_remind:CustomSetActive(not is_get_flag)
    XUI.SetButtonEnabled(self.node_list.btn_enter_game, not is_get_flag)
    local btn_state_index = is_get_flag and 1 or 0
    self.node_list.desc_btn_enter_game.text.text = Language.TreasurePalace.EnterRewardBtnState[btn_state_index]

    self:FlushEnterRewardModel()
end

function TreasurePalaceView:OnClickEnterReward()
    local is_get_flag = TreasurePalaceData.Instance:IsGetEnterReward()

    if not is_get_flag then
        TreasurePalaceCtrl.Instance:SendZhenBaoDianClientReq(ZHENBAODIAN_OPERA_TYPE.GET_ENTER_GAME_REWARD)
    end
end

function TreasurePalaceView:FlushEnterRewardModel()
    local model_cfg = TreasurePalaceData.Instance:GetOtherCfg()

    if IsEmptyTable(model_cfg) then
		return
	end

    local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1

	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	-- display_data.model_click_func = function ()
	-- 	TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	-- end

	self.abd_enter_reward_model:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.abd_enter_reward_model.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list.abd_enter_reward_model.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list.abd_enter_reward_model.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end