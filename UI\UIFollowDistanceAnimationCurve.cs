﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(
        fileName = "AnimationCurve",
        menuName = "UIFollow/AnimationCurve")]
public class UIFollowDistanceAnimationCurve : ScriptableObject {
    [SerializeField]
    private AnimationCurve curve = new AnimationCurve(new Keyframe(0f, 1f), new Keyframe(1f, 0f));

    public AnimationCurve Curve
    {
        get { return curve; }
    }
}
