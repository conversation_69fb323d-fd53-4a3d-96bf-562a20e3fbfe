function MengLingView:InitMengLingSuitPanel()
    if not self.ml_suit_item_cell_list then
        self.ml_suit_item_cell_list = {}

        for i = 1, 3 do
            self.ml_suit_item_cell_list[i] = MengLingSuitItemCellRender.New(self.node_list["suit_item_cell" .. i])
        end
    end

    for i = 1, 2 do
        self.node_list["suit_tog" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickMlSuitTogHandle, self, i))

        local suit_data = MengLingWGData.Instance:GetMengLingSuitCfgBySuit(i)
        local suit_name = suit_data and suit_data.suit_name or ""
        suit_name = string.format(Language.MengLing.MengLingSuitNameDesc, suit_name)
        self.node_list["suit_tog_name" .. i].text.text = suit_name
        self.node_list["suit_tog_hl_name" .. i].text.text = suit_name
    end
end

function MengLingView:ReleaseMengLingSuitPanel()
    if self.ml_suit_item_cell_list then
        for k, v in pairs(self.ml_suit_item_cell_list) do
            v:DeleteMe()
        end

        self.ml_suit_item_cell_list = nil
    end

    self.ml_suit_select_type = nil
end

function MengLingView:OnFlushMengLingSuitPanel()
    if nil == self.ml_suit_select_type then
        self.ml_suit_select_type = 1
        self.node_list["suit_tog" .. self.ml_suit_select_type].toggle.isOn = true
    end

    -- 刷新属性展示
    local suit_data_list = MengLingWGData.Instance:GetMengLingSuitAttrCfg(self.select_equip_suit_seq, self.ml_suit_select_type)
    for i = 1, 3 do
        self.ml_suit_item_cell_list[i]:SetData(suit_data_list[i])
    end
end

function MengLingView:OnClickMlSuitTogHandle(tog_index, is_on)
    if is_on then
        self.ml_suit_select_type = tog_index
        self:OnFlushMengLingSuitPanel()
    end
end

-------------------------------------MengLingSuitItemCellRender---------------------------------------
MengLingSuitItemCellRender = MengLingSuitItemCellRender or BaseClass(BaseRender)

function MengLingSuitItemCellRender:LoadCallBack()
    if not self.attr_list then
        self.attr_list = {}

        for i = 1, 7 do
            self.attr_list[i] = CommonAttrRender.New(self.node_list["attr_" .. i])
            self.attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end
end

function MengLingSuitItemCellRender:__delete()
    if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end

        self.attr_list = nil
    end
end

function MengLingSuitItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local suit_active_data = self.data.suit_active_data
    local suit_default_data = self.data.suit_default_data
    local suit_min_data = self.data.suit_min_data

    local active = not IsEmptyTable(suit_active_data)
    -- self.node_list.no_active:CustomSetActive(not active)
    -- self.node_list.common_attr_list:CustomSetActive(active)
    self.node_list.no_active:CustomSetActive(false)
    self.node_list.common_attr_list:CustomSetActive(true)

    local target_data = active and suit_active_data or suit_default_data

    local has_num = MengLingWGData.Instance:GetMengLingSuitCacheNum(target_data.seq, target_data.suit, target_data.color)
    local num_str = has_num .. "/" .. target_data.need_num

    if has_num < target_data.need_num then
        num_str = ToColorStr(num_str, COLOR3B.D_RED)
    end

    self.node_list.num.text.text = string.format(Language.MengLing.MengLingSuitActiveNum, num_str)

    local bg_color = active and target_data.color or 9

    local bg_bundle, bg_asset = ResPath.GetMengLingImg("a3_lq_di" .. bg_color)
    self.node_list.color_bg.image:LoadSprite(bg_bundle, bg_asset)

    self.node_list.flag_no_active:CustomSetActive(not active)
    self.node_list.color:CustomSetActive(active)

    if active then
        local suit_cfg = MengLingWGData.Instance:GetGradeCfgBySeq(target_data.seq)
        self.node_list.color.text.text = string.format(Language.MengLing.MengLingSuitActiveName2, (suit_cfg.seq + 1))
    end

    local attr_info_data
    if active then
        attr_info_data = MengLingWGData.Instance:GetSuitAttrList(target_data.seq, target_data.suit, target_data.need_num, target_data.color)
    else
        attr_info_data = EquipWGData.GetSortAttrListByTypeCfg(suit_min_data, "attr_id", "attr_value", 1, 7)
    end

    for i = 1, 7 do
        self.attr_list[i]:SetData(attr_info_data[i])
    end

    XUI.SetGraphicGrey(self.node_list.common_attr_list, not active)
end