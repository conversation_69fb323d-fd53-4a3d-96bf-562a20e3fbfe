﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SceneEnvironment_EnvironmentDataWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(SceneEnvironment.EnvironmentData), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>unction("New", _CreateSceneEnvironment_EnvironmentData);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L<PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("lightmapColor", get_lightmapColor, set_lightmapColor);
		<PERSON><PERSON>("reflectionColor", get_reflectionColor, set_reflectionColor);
		<PERSON><PERSON>("plantLightColor", get_plantLightColor, set_plantLightColor);
		<PERSON><PERSON>("plantEnvColor", get_plantEnvColor, set_plantEnvColor);
		<PERSON><PERSON>("maxMetallic", get_maxMetallic, set_maxMetallic);
		<PERSON><PERSON>("maxOcclusion", get_maxOcclusion, set_maxOcclusion);
		<PERSON><PERSON>("bakedLightInstensity", get_bakedLightInstensity, set_bakedLightInstensity);
		L.RegVar("emissionInstensity", get_emissionInstensity, set_emissionInstensity);
		L.RegVar("shadowIntensity", get_shadowIntensity, set_shadowIntensity);
		L.RegVar("reflectionTex", get_reflectionTex, set_reflectionTex);
		L.RegVar("enableRain", get_enableRain, set_enableRain);
		L.RegVar("rainTex", get_rainTex, set_rainTex);
		L.RegVar("rainSurfaceWaterDensity", get_rainSurfaceWaterDensity, set_rainSurfaceWaterDensity);
		L.RegVar("rainSurfaceRoughness", get_rainSurfaceRoughness, set_rainSurfaceRoughness);
		L.RegVar("rainIntensity", get_rainIntensity, set_rainIntensity);
		L.RegVar("rainSurfaceWaterSpeed", get_rainSurfaceWaterSpeed, set_rainSurfaceWaterSpeed);
		L.RegVar("enableRainRipple", get_enableRainRipple, set_enableRainRipple);
		L.RegVar("rainRippleTex", get_rainRippleTex, set_rainRippleTex);
		L.RegVar("rainRippleDensity", get_rainRippleDensity, set_rainRippleDensity);
		L.RegVar("rainRippleIntensity", get_rainRippleIntensity, set_rainRippleIntensity);
		L.RegVar("rainRippleFrequency", get_rainRippleFrequency, set_rainRippleFrequency);
		L.RegVar("enableCaustics", get_enableCaustics, set_enableCaustics);
		L.RegVar("causticsTex", get_causticsTex, set_causticsTex);
		L.RegVar("causticsColor", get_causticsColor, set_causticsColor);
		L.RegVar("causticsDistance", get_causticsDistance, set_causticsDistance);
		L.RegVar("causticsFadeFactor", get_causticsFadeFactor, set_causticsFadeFactor);
		L.RegVar("causticsSpeedTilling1", get_causticsSpeedTilling1, set_causticsSpeedTilling1);
		L.RegVar("causticsSpeedTilling2", get_causticsSpeedTilling2, set_causticsSpeedTilling2);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateSceneEnvironment_EnvironmentData(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				SceneEnvironment.EnvironmentData obj = new SceneEnvironment.EnvironmentData();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: SceneEnvironment.EnvironmentData.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lightmapColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color ret = obj.lightmapColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lightmapColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reflectionColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color ret = obj.reflectionColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reflectionColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_plantLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color ret = obj.plantLightColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index plantLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_plantEnvColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color ret = obj.plantEnvColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index plantEnvColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxMetallic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.maxMetallic;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxMetallic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxOcclusion(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.maxOcclusion;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxOcclusion on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bakedLightInstensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.bakedLightInstensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bakedLightInstensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_emissionInstensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.emissionInstensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index emissionInstensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.shadowIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reflectionTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Cubemap ret = obj.reflectionTex;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reflectionTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableRain(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			bool ret = obj.enableRain;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableRain on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Texture2D ret = obj.rainTex;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainSurfaceWaterDensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.rainSurfaceWaterDensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainSurfaceWaterDensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainSurfaceRoughness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.rainSurfaceRoughness;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainSurfaceRoughness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.rainIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainSurfaceWaterSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Vector4 ret = obj.rainSurfaceWaterSpeed;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainSurfaceWaterSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableRainRipple(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			bool ret = obj.enableRainRipple;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableRainRipple on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainRippleTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Texture2D ret = obj.rainRippleTex;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainRippleDensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.rainRippleDensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleDensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainRippleIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.rainRippleIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rainRippleFrequency(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.rainRippleFrequency;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleFrequency on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableCaustics(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			bool ret = obj.enableCaustics;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableCaustics on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_causticsTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Texture2D ret = obj.causticsTex;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_causticsColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color ret = obj.causticsColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_causticsDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.causticsDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_causticsFadeFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float ret = obj.causticsFadeFactor;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsFadeFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_causticsSpeedTilling1(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Vector4 ret = obj.causticsSpeedTilling1;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsSpeedTilling1 on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_causticsSpeedTilling2(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Vector4 ret = obj.causticsSpeedTilling2;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsSpeedTilling2 on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lightmapColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.lightmapColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lightmapColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_reflectionColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.reflectionColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reflectionColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_plantLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.plantLightColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index plantLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_plantEnvColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.plantEnvColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index plantEnvColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxMetallic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.maxMetallic = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxMetallic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxOcclusion(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.maxOcclusion = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxOcclusion on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bakedLightInstensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.bakedLightInstensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bakedLightInstensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_emissionInstensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.emissionInstensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index emissionInstensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.shadowIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_reflectionTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Cubemap arg0 = (UnityEngine.Cubemap)ToLua.CheckObject(L, 2, typeof(UnityEngine.Cubemap));
			obj.reflectionTex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reflectionTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableRain(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableRain = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableRain on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 2, typeof(UnityEngine.Texture2D));
			obj.rainTex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainSurfaceWaterDensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.rainSurfaceWaterDensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainSurfaceWaterDensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainSurfaceRoughness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.rainSurfaceRoughness = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainSurfaceRoughness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.rainIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainSurfaceWaterSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Vector4 arg0 = ToLua.ToVector4(L, 2);
			obj.rainSurfaceWaterSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainSurfaceWaterSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableRainRipple(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableRainRipple = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableRainRipple on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainRippleTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 2, typeof(UnityEngine.Texture2D));
			obj.rainRippleTex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainRippleDensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.rainRippleDensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleDensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainRippleIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.rainRippleIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rainRippleFrequency(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.rainRippleFrequency = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rainRippleFrequency on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableCaustics(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableCaustics = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableCaustics on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_causticsTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 2, typeof(UnityEngine.Texture2D));
			obj.causticsTex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_causticsColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.causticsColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_causticsDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.causticsDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_causticsFadeFactor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.causticsFadeFactor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsFadeFactor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_causticsSpeedTilling1(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Vector4 arg0 = ToLua.ToVector4(L, 2);
			obj.causticsSpeedTilling1 = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsSpeedTilling1 on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_causticsSpeedTilling2(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneEnvironment.EnvironmentData obj = (SceneEnvironment.EnvironmentData)o;
			UnityEngine.Vector4 arg0 = ToLua.ToVector4(L, 2);
			obj.causticsSpeedTilling2 = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index causticsSpeedTilling2 on a nil value");
		}
	}
}

