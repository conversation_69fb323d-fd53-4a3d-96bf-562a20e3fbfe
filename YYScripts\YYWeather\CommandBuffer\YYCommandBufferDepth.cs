﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public partial class YYCommandBufferManager
{
    

    //  //////////////////////////////////

    private void UpdateDepthDownsampler()
    {
        if (DownsampleDepthMaterial != null)
        {
            RenderTexture tmp = HalfDepthBuffer;
            UpdateDepthDownsampler(ref tmp, 2);
            HalfDepthBuffer = tmp;

            Shader.SetGlobalTexture(WMS._CameraDepthTextureHalf, HalfDepthBuffer);
        }
    }

    private RenderTargetIdentifier UpdateDepthDownsampler(ref RenderTexture tex, int scale)
    {
        RenderTextureDescriptor desc = YYFullScreenEffectUtil.GetRenderTextureDescriptor(scale, 0, 1, RenderTextureFormat.RFloat);

        if (tex == null || tex.width != desc.width || tex.height != desc.height)
        {
            YYFullScreenEffectUtil.DestroyRenderTexture(ref tex);
            tex = YYFullScreenEffectUtil.CreateRenderTexture(desc, FilterMode.Point, TextureWrapMode.Clamp);
            tex.name = "WeatherMakerDepthTexture_" + scale;
        }

        return tex;
    }

    //  ////////////////////////////////
    private void AttachDepthCommandBuffer(Camera camera)
    {
        if (DownsampleDepthMaterial != null)// && !WeatherMakerScript.ShouldIgnoreCamera(this, camera, false))
        {
            CreateAndAddDepthCommandBuffer(camera);
        }
    }

    private const string depthCommandBufferName = "WeatherMakerDepthDownsample";
    private CommandBuffer depthCommandBuffer;

    private void CreateAndAddDepthCommandBuffer(Camera camera)
    {
        

        return;
        if (HalfDepthBuffer == null)
        {
            return;
        }
        else if (camera.depthTextureMode == DepthTextureMode.None)
        {
            camera.depthTextureMode = DepthTextureMode.Depth;
        }

        depthCommandBuffer = (depthCommandBuffer == null ? new CommandBuffer { name = "WeatherMakerDownsampleDepth" } : depthCommandBuffer);
        camera.RemoveCommandBuffer(CameraEvent.AfterDepthTexture, depthCommandBuffer);
        camera.RemoveCommandBuffer(CameraEvent.BeforeReflections, depthCommandBuffer);
        depthCommandBuffer.Clear();

        depthCommandBuffer.SetGlobalFloat(WMS._DownsampleDepthScale, 2.0f);
        depthCommandBuffer.Blit(BuiltinRenderTextureType.CameraTarget, HalfDepthBuffer, DownsampleDepthMaterial, 1);

        camera.AddCommandBuffer(CameraEvent.AfterDepthTexture, depthCommandBuffer);


    }

}
