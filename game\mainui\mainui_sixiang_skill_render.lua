MainUISiXiangSkillRender = MainUISiXiangSkillRender or BaseClass(BaseRender)
function MainUISiXiangSkillRender:__init()
	if self.view.event_trigger_listener then
		self.view.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnPointerUp, self))
		self.view.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrag, self))
		self.view.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnPointerDown, self))
		self.view.event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.OnPointerExit, self))
	end
	self.data = MainUISiXiangSkillRender.CreateSkillVo()
end

function MainUISiXiangSkillRender.CreateSkillVo()
	return {
		is_lock = true,
		skill_id = 0,
		duan_atk = 0,
		skill_index = 0,
		cell_index = 0,
		is_temp_show = false,
	}
end

function MainUISiXiangSkillRender:__delete()
	self.old_sixiang_skill_grey = nil
end

function MainUISiXiangSkillRender:SetParent(parent)
	self.parent = parent
end

function MainUISiXiangSkillRender:SetPointerUpCallBack(call_back)
	self.pointer_up_cb = call_back
end

function MainUISiXiangSkillRender:SetDragCallBack(call_back)
	self.drag_cb = call_back
end

function MainUISiXiangSkillRender:SetPointerDownCallBack(call_back)
	self.pointer_down_cb = call_back
end

function MainUISiXiangSkillRender:SetPointerExitCallBack(call_back)
	self.pointer_exit_cb = call_back
end

function MainUISiXiangSkillRender:SetClickSkillCallBack(call_back)
	self.click_skill_cb = call_back
end

function MainUISiXiangSkillRender:OnPointerUp()
	if self.pointer_up_cb then
		self.pointer_up_cb(self.data)
	end
end

function MainUISiXiangSkillRender:OnDrag(eventData)
	if self.drag_cb then
		self.drag_cb(self.data, eventData)
	end
end

function MainUISiXiangSkillRender:OnPointerDown()
	if self.pointer_down_cb then
		self.pointer_down_cb(self.data)
	end
end

function MainUISiXiangSkillRender:OnPointerExit()
	if self.pointer_exit_cb then
		self.pointer_exit_cb(self.data)
	end
end

function MainUISiXiangSkillRender:SetData(data)
	if IsEmptyTable(data) then
		return
	end

	self.data.skill_id = data.skill_id
	self.data.skill_index = data.index
	self:Flush()
end

function MainUISiXiangSkillRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	-- 技能类型
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(self.data.skill_id)
	if self.node_list.skill_type ~= nil then	-- 加个容错
		if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
			self.node_list.skill_type:SetActive(true)
			self.node_list.skill_type.text.text = Language.Skill.CommonSkillType[skill_type_tab[1]] or ""
		else
			self.node_list.skill_type:SetActive(false)
		end
	end

	self.view:SetActive(true)
	self:FlushProgress()
end

function MainUISiXiangSkillRender:ChangeIconAsset(bundle, asset)
	self.node_list.sx_skill_effect:ChangeAsset(bundle, asset, false)
end

function MainUISiXiangSkillRender:ChangeIconShow(s_bundle, s_asset)
		self.node_list.sx_skill_icon.image:LoadSprite(s_bundle, s_asset)
end

function MainUISiXiangSkillRender:ChangeSuitShow(suit_index)
	local _, slot = FightSoulWGData.Instance:GetTypeIsWear(suit_index)
	local suit_data = FightSoulWGData.Instance:GetAutoSkillActSuitDataBySlot(slot)
	local auto_skill_level = suit_data and suit_data.suit_type or 0
	if auto_skill_level ~= self.old_sixiang_suit_type then
	    for i = 1, FIGHT_SOUL_NUM.MAX_SUIT_NUM do
	        local icon_node = self.node_list["sx_skill_level_icon" .. i]
	        if icon_node then
	            local res = auto_skill_level >= i and "sx_gai_jingsezhu" or "sx_gai_huisezhu"
	            local bundle, asset = ResPath.GetMainUIIcon(res)
	            icon_node.image:LoadSprite(bundle, asset, function()
	                icon_node.image:SetNativeSize()
	            end)
	        end
	    end

		self.old_sixiang_suit_type = auto_skill_level
	end
end

function MainUISiXiangSkillRender:FlushProgress()
	local cur_nuqi, need_nuqi, is_enough = FightSoulWGData.Instance:GetSkillNuQiData()
	self.node_list.sx_nuqi_progress.slider.value = cur_nuqi / need_nuqi
	self.node_list.sx_skill_effect:SetActive(is_enough)
	if self.old_sixiang_skill_grey == nil or self.old_sixiang_skill_grey ~= is_enough then
		XUI.SetGraphicGrey(self.node_list.sx_skill_icon, not is_enough)
		self.old_sixiang_skill_grey = is_enough
	end

	if self.old_role_cur_nuqi and self.old_role_cur_nuqi == need_nuqi and cur_nuqi < self.old_role_cur_nuqi then
		self:DoSiXiangBtnBaoDianEffect()
	end

	if not self.old_role_cur_nuqi then
		self.old_role_cur_nuqi = cur_nuqi
	elseif self.old_role_cur_nuqi < cur_nuqi then
		self.old_role_cur_nuqi = cur_nuqi
		if self.parent then
			self.parent:PlayNuQiAddEffect()
		end
	else
		self.old_role_cur_nuqi = cur_nuqi
	end
end

function MainUISiXiangSkillRender:DoSiXiangBtnBaoDianEffect()
	if self.node_list.baodian_eff and self.node_list.baodian_eff:GetActive() then
		local bundle_name1, asset_name1 = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
		EffectManager.Instance:PlayAtTransform(bundle_name1, asset_name1, self.node_list.baodian_eff.transform, 0.5)
	end
end


