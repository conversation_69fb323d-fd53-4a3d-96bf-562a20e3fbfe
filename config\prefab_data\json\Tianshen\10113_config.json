{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10113_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10113/10113_skill1_prefab", "AssetName": "10113_skill1", "AssetGUID": "208c78cfea97fde47824d56c6586c630", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "buff_down", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.5, "offsetPosY": 0.0, "offsetPosZ": -2.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10113_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10113/10113_skill2_prefab", "AssetName": "10113_skill2", "AssetGUID": "74cc8caeb87404f4bb0d31d308d2e0e2", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10113_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10113/10113_skill3_prefab", "AssetName": "10113_skill3", "AssetGUID": "4731501728be0f648b458d38ca2d58e1", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "buff_down", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.5, "offsetPosY": 0.0, "offsetPosZ": -8.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10113_attack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10113/10113_attack_prefab", "AssetName": "10113_attack", "AssetGUID": "d5ae484f1752ede46a0a700904ae8f90", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10113_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10113/10113_skill3_prefab", "AssetName": "10113_skill3", "AssetGUID": "4731501728be0f648b458d38ca2d58e1", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "hurt_root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": -10.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10113", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "7371e8c3800951b41b55a26899b80039", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10113", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "1c258806a5d53ed4db57658027c30f35", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10113", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "dc5b16a03511c394ebc1f83dbb043e6c", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10113", "AssetName": "MingJiangattack1", "AssetGUID": "40c4c883dc6d22f44aa88ac05e0ee466", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10113", "AssetName": "MingJiangattack3", "AssetGUID": "8cd1ec25e05b56e4cac75e4d0bacb7ee", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10113", "AssetName": "MingJiangattack2", "AssetGUID": "9a79c2441336cc54d98935863a7a6ec4", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack3", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack3-1", "eventName": "attack3/begin", "numberOfShakes": 5, "distance": 0.1, "speed": 500.0, "delay": 1.3, "decay": 0.0}, {"CameraShakeBtnName": "attack1", "eventName": "attack1/begin", "numberOfShakes": 5, "distance": 0.1, "speed": 500.0, "delay": 0.4, "decay": 0.0}, {"CameraShakeBtnName": "attack3-2", "eventName": "attack3/begin", "numberOfShakes": 3, "distance": 0.1, "speed": 400.0, "delay": 0.3, "decay": 0.0}, {"CameraShakeBtnName": "attack2-1", "eventName": "attack2/begin", "numberOfShakes": 10, "distance": 0.05, "speed": 600.0, "delay": 1.5, "decay": 0.0}], "radialBlurs": []}}