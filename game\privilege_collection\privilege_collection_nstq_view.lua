function PrivilegeCollectionView:NSTQLoadIndecCallBack()
    self.cur_select_index = 1
    self.is_jump_flag = true

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    if not self.privilege_card_list then
        self.privilege_card_list = AsyncListView.New(PRICOLNSTQItemCellRender, self.node_list.privilege_card_list)
        self.privilege_card_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectPrivilegeCard, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_buy_limit, BindTool.Bind(self.OnClickBuyLimit, self))
    XUI.AddClickEventListener(self.node_list.btn_nstq_jump, BindTool.Bind(self.OnClickNSTQJumpBtn, self))
end

function PrivilegeCollectionView:NSTQShowIndecCallBack()
    self.is_jump_flag = true
end

function PrivilegeCollectionView:NSTQReleaseCallBack()
    if self.privilege_card_list then
        self.privilege_card_list:DeleteMe()
        self.privilege_card_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function GoddessBlessingView:SetJumpFlag()
    self.is_jump_flag = true
end

function PrivilegeCollectionView:NSTQOnFlush()
    self:FlushView()
end

function PrivilegeCollectionView:OnSelectPrivilegeCard(cell)
    if not cell or IsEmptyTable(cell.data) then
		return
	end

    local data = cell.data
    if self.cur_select_index == data.seq then
        return
    end

    self.cur_select_index = data.seq
    local rmb_cfg = PrivilegeCollectionWGData.Instance:GetRmbCfg()
    local from_value = self.node_list.privilege_card_list.scroll_rect.horizontalNormalizedPosition

    local index = cell.index
    if index <= 1 then
        index = 0
    end

    local target_value = index / #rmb_cfg
    UITween.DoScrollRectHorizontalPosition(self.node_list.privilege_card_list, from_value, target_value, 0.5, nil, nil)

    self:FlushView()
end

function PrivilegeCollectionView:FlushView()
    local rmb_cfg = PrivilegeCollectionWGData.Instance:GetRmbCfg()
    local cur_select_rmb_cfg = PrivilegeCollectionWGData.Instance:GetRmbCfgBySeq(self.cur_select_index)
    local buy_state = PrivilegeCollectionWGData.Instance:GetRmbBuyStateBySeq(self.cur_select_index)

    self.privilege_card_list:SetDataList(rmb_cfg)
    if self.is_jump_flag then
        self.is_jump_flag = false
        local default_index = PrivilegeCollectionWGData.Instance:GetDefaultSelectIndex()
        self.privilege_card_list:JumpToIndex(default_index, 3)
    end

    for i = 1, 3 do
        if cur_select_rmb_cfg["desc" .. i] and cur_select_rmb_cfg["desc" .. i] ~= "" then
            self.node_list["reward_desc" .. i]:SetActive(true)
            self.node_list["reward_desc" .. i].text.text = cur_select_rmb_cfg["desc" .. i]
        else
            self.node_list["reward_desc" .. i]:SetActive(false)
        end
    end

    self.reward_list:SetDataList(cur_select_rmb_cfg.reward_item)

    local price_str = RoleWGData.GetPayMoneyStr(cur_select_rmb_cfg.price, cur_select_rmb_cfg.rmb_type, cur_select_rmb_cfg.rmb_seq)
	self.node_list.btn_buy_text.text.text = string.format(Language.PrivilegeCollection.BuyDesc, price_str)

    self.node_list.btn_buy:SetActive(buy_state == GODDESS_BLESSING_BUY_TYPE.CAN_BUY)
    self.node_list.btn_buy_limit:SetActive(buy_state == GODDESS_BLESSING_BUY_TYPE.CAN_NOT_BUY)
    self.node_list.buy_flag:SetActive(buy_state == GODDESS_BLESSING_BUY_TYPE.HAVE_BUY)
end

function PrivilegeCollectionView:OnClickBuyBtn()
    local cur_select_rmb_cfg = PrivilegeCollectionWGData.Instance:GetRmbCfgBySeq(self.cur_select_index)
    RechargeWGCtrl.Instance:Recharge(cur_select_rmb_cfg.price, cur_select_rmb_cfg.rmb_type, cur_select_rmb_cfg.rmb_seq)
end

function PrivilegeCollectionView:OnClickBuyLimit()
    local rmb_seq = PrivilegeCollectionWGData.Instance:GetRmbBuySeq()
    self.privilege_card_list:JumpToIndex(rmb_seq + 1, 3)
end

function PrivilegeCollectionView:OnClickNSTQJumpBtn()
    ViewManager.Instance:Open(GuideModuleName.RechargeVolumeView)
end

----------------------------------特权卡item-----------------------
PRICOLNSTQItemCellRender = PRICOLNSTQItemCellRender or BaseClass(BaseRender)

function PRICOLNSTQItemCellRender:__delete()
    if self.scale_tween then
        self.scale_tween:Kill()
        self.scale_tween = nil
    end
end

function PRICOLNSTQItemCellRender:OnFlush()
    if not self.data then return end

    self.node_list.name.text.text = self.data.name
    local bundle, asset = ResPath.GetRawImagesPNG(self.data.bg)
    self.node_list.role_bg.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["role_bg"].raw_image:SetNativeSize()
    end)
end

function PRICOLNSTQItemCellRender:OnSelectChange(is_select)
    self.node_list.select_img:SetActive(is_select)
    self.node_list.role_bg.raw_image.color = is_select and StrToColor(COLOR3B.WHITE) or StrToColor(COLOR3B.GRAY)

    local scale_vec3 = is_select and Vector3(1.15, 1.15, 1.15) or Vector3(0.95, 0.95, 0.95)

    if self.scale_tween then
        self.scale_tween:Kill()
        self.scale_tween = nil
    end

    if is_select then
        self.scale_tween = self.node_list.content.rect:DOScale(scale_vec3, 0.3)
    else
        self.node_list.content.rect.localScale = scale_vec3
    end
end