﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ConsoleRuntimeGUI
{
    private Rect windowRect;
    private bool isConsoleShow = false;
    private string GM = "";

    public void OnGUI()
    {   
        RuntimeGUIMgr.Instance.GetCurUseSVNTime();

        GUILayout.BeginVertical();
        string curUseSvnTime = RuntimeGUIMgr.Instance.GetCurUseSVNTime();
        GUILayout.Label(curUseSvnTime, RuntimeGUIStyle.GreenLabel);

        string newSvnTime = RuntimeGUIMgr.Instance.GetNewSVNTime();
        if (curUseSvnTime != newSvnTime)
        {
            GUILayout.Label(newSvnTime, RuntimeGUIStyle.RedLabel);
        }
        GUILayout.EndVertical();
        
        isConsoleShow = GUI.Toggle(new Rect((Screen.width - 30) * 0.5f, 30, 120, 23), isConsoleShow, "显示控制台");
        if (isConsoleShow)
        {
            windowRect = new Rect((Screen.width - 700) * 0.5f, (Screen.height - 300) * 0.5f, 700, 300);
            windowRect = GUI.Window(0, windowRect, ShowConsole, "控制台");
            RuntimeGUIMgr.Instance.GetGUIBlock().ShowRect(windowRect, 1);
        }
        else
        {
            RuntimeGUIMgr.Instance.GetGUIBlock().HideRect();
        }
    }

    private void ShowConsole(int windowid)
    {
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("重新登陆", GUILayout.Height(40), GUILayout.Height(40)))
        {
            GameRoot.Instance.Restart();
        }

        GUILayout.Space(10);
        GM = GUILayout.TextField(GM, GUILayout.MinWidth(1), GUILayout.Height(40));
        if (GUILayout.Button("发送命令", GUILayout.Width(100), GUILayout.Height(40)))
        {
            GameRoot.Instance.ExecuteGm(GM);
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Gmlist"))
        {
            SetGMbarStr("/jy_cmd gmlist 4");
        }
        if (GUILayout.Button("GmAdditem"))
        {
            SetGMbarStr("/jy_gm additem:101 1 0");
        }
        if (GUILayout.Button("GmActiveNextState"))
        {
            SetGMbarStr("/jy_gm activitynextstate:5");
        }
        if (GUILayout.Button("GM获得"))
        {
            SetGMbarStr("/jy_cmd quickitem");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("清空货币"))
        {
            SetGMbarStr("/jy_gm decmoney:9999999999");
        }
        if (GUILayout.Button("设置VIP"))
        {
            SetGMbarStr("/jy_gm setvip:12");
        }
        if (GUILayout.Button("加钱"))
        {
            SetGMbarStr("/jy_gm addmoney:9999999999");
        }
        if (GUILayout.Button("充值"))
        {
            SetGMbarStr("/jy_gm addchongzhi:9999999999");
        }
        if (GUILayout.Button("清空背包"))
        {
            SetGMbarStr("/jy_gm clearbag:");
        }
        if (GUILayout.Button("增加攻击力"))
        {
            SetGMbarStr("/jy_gm changegongji:9999999999");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("转职"))
        {
            SetGMbarStr("/jy_gm zhuanzhi:");
        }
        if (GUILayout.Button("热执行代码"))
        {
            SetGMbarStr("/jy_cmd InsertLua ");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("加等级"))
        {
            SetGMbarStr("/jy_gm setrolelevel:300");
        }
        if (GUILayout.Button("加科技点"))
        {
            SetGMbarStr("/jy_gm xingchen:1 1000");
        }
        if (GUILayout.Button("加科技币"))
        {
            SetGMbarStr("/jy_gm xingchen:2 1000");
        }
        if (GUILayout.Button("resetdaycount"))
        {
            SetGMbarStr("/jy_gm resetdaycount:");
        }
        if (GUILayout.Button("addday"))
        {
            SetGMbarStr("/jy_gm addday:");
        }
        if (GUILayout.Button("服务器热更新"))
        {
            SetGMbarStr("/jy_gm hotupdate:");
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("男职业一键获取15阶装备"))
        {
            ZhuanZhi();

            SetGMbarStr("/jy_gm additem:189 1 0");
            SetGMbarStr("/jy_gm additem:1189 1 0");
            SetGMbarStr("/jy_gm additem:2189 1 0");
            SetGMbarStr("/jy_gm additem:3189 1 0");
            SetGMbarStr("/jy_gm additem:4189 1 0");
            SetGMbarStr("/jy_gm additem:5189 1 0");
            SetGMbarStr("/jy_gm additem:6189 1 0");
            SetGMbarStr("/jy_gm additem:7189 1 0");
            SetGMbarStr("/jy_gm additem:8189 1 0");
            SetGMbarStr("/jy_gm additem:9189 1 0");
        }

        if (GUILayout.Button("女职业一键获取15阶装备"))
        {
            ZhuanZhi();

            SetGMbarStr("/jy_gm additem:389 1 0");
            SetGMbarStr("/jy_gm additem:1389 1 0");
            SetGMbarStr("/jy_gm additem:2389 1 0");
            SetGMbarStr("/jy_gm additem:3389 1 0");
            SetGMbarStr("/jy_gm additem:4389 1 0");
            SetGMbarStr("/jy_gm additem:5389 1 0");
            SetGMbarStr("/jy_gm additem:6189 1 0");
            SetGMbarStr("/jy_gm additem:7189 1 0");
            SetGMbarStr("/jy_gm additem:8189 1 0");
            SetGMbarStr("/jy_gm additem:9189 1 0");
        }
        if (GUILayout.Button("跳任务"))
        {
            SetGMbarStr("/jy_gm jumptotrunk:1880");
        }

        if (GUILayout.Button("添加6件神兽装备"))
        {
            SetGMbarStr("/jy_gm additem:22544 1 0");
            SetGMbarStr("/jy_gm additem:22550 1 0");
            SetGMbarStr("/jy_gm additem:22556 1 0");
            SetGMbarStr("/jy_gm additem:22562 1 0");
            SetGMbarStr("/jy_gm additem:22568 1 0");
            SetGMbarStr("/jy_gm additem:23626 1 0");
        }
        GUILayout.EndHorizontal();

        // 工具栏
        GUILayout.Space(10);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("跑新手资源"))
        {
            RuntimeGUIMgr.Instance.ShowResourceRecorderWindow();
        }
        GUILayout.EndHorizontal();
    }

    private void ZhuanZhi()
    {
        SetGMbarStr("/jy_cmd gmlist 4");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
        SetGMbarStr("/jy_gm zhuanzhi:");
    }

    public void SetGMbarStr(string gmContent)
    {
        GM = gmContent;
    }

}
