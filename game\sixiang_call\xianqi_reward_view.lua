XianQiRewardView = XianQiRewardView or BaseClass(SafeBaseView)

function XianQiRewardView:__init(view_name)
	self.view_name = "XianQiRewardView"
	self.view_layer = UiLayer.Pop
	--self.mask_alpha = MASK_BG_ALPHA_TYPE.Normal
	self:AddViewResource(0, "uis/view/shenji_tianci_ui_prefab", "layout_xianqi_reward")
	self:SetMaskBg(false, true)
end

function XianQiRewardView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
	self:InitRewardList()
end

function XianQiRewardView:ReleaseCallBack()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
	if self.once_summon_item then
		self.once_summon_item:DeleteMe()
		self.once_summon_item = nil
	end
end

function XianQiRewardView:CloseCallBack()
	self:StopTween()
end

function XianQiRewardView:OpenCallBack()
	
end

function XianQiRewardView:OnFlush(param_t)
	if param_t.xianqi_reward then
		-- self:ChangeMaskAlpha()
		self:RefreshView()
		if self.is_shilian then
			self:PlayTenRewardTween(true)
		else
			self:PlayOneRewardTween(true)
		end
	-- elseif param_t.star_xianqi_tween then
	-- 	--self:SetRootBgScale(true)
	-- 	if self.is_shilian then
	-- 		self:PlayTenRewardTween(true)
	-- 	else
	-- 		self:PlayOneRewardTween(true)
	-- 	end
	-- 	self.is_xianqi_view_anim_reset = false
	-- elseif param_t.end_xianqi_tween then
	-- 	--self:SetRootBgScale(true)
	-- 	self.is_xianqi_view_anim_reset = true
	end
end

function XianQiRewardView:InitParam()
	self.is_play_tween = false
	self.is_xianqi_view_anim_reset = false
	self.reward_item_list = nil
	self.play_tween_index = 0
	self.is_shilian = false
end

function XianQiRewardView:InitPanel()
	self.once_summon_item = XianQiRewardItem.New()
	self.once_summon_item:DoLoad(self.node_list.once_item_root)
end

function XianQiRewardView:InitListener()
	XUI.AddClickEventListener(self.node_list.sure_btn, BindTool.Bind1(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list["draw_btn"], BindTool.Bind1(self.OnClickDrawBtn, self))
end

function XianQiRewardView:InitRewardList()
	local res_async_loader = AllocResAsyncLoader(self, "xianqi_reward_item")
	res_async_loader:Load("uis/view/shenji_tianci_ui_prefab", "xianqi_reward_item", nil,
		function(new_obj)
			if IsNil(new_obj) then
				return
			end

			local item_root = self.node_list.reward_root.transform
			local layout_group = self.node_list.reward_root.grid_layout_group
			local cell_x = layout_group.cellSize.x
			local cell_y = layout_group.cellSize.y
			local spacing_x = layout_group.spacing.x
			local spacing_y = layout_group.spacing.y

			local item_list = {}
			for i=1,10 do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(item_root, false)
				item_list[i] = XianQiRewardItem.New(obj)
				item_list[i]:SetIndex(i)
				item_list[i]:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
			end

			self.reward_item_list = item_list
			self:RefreshView()
		end)
end

function XianQiRewardView:OnClickSureBtn()
	if self.is_play_tween then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.WaitAnimStop)
		return
	end
	self:Close()
end

function XianQiRewardView:OnClickDrawBtn()
	--检测跳过动画
	if not SiXiangCallWGCtrl.Instance:IsSkipZhenLianAnim() then
		--转盘动画未复位/抽奖动画进行中
		if self.is_play_tween then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.XQZLAnimPlaying)
			return
		end
	end

	----[[ 再抽一次
	local xianqi_info = SiXiangCallWGData.Instance:GetCacheXianQiType()
	if xianqi_info then
		if xianqi_info.draw_type == MACHINE_DRAW_TYPE.USE_MULTI_ITEM then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(xianqi_info.item_id)
			local can_get = has_num >= xianqi_info.need_num
			if can_get then
				SiXiangCallWGCtrl.Instance:TryToXianQiZhenLian(xianqi_info.item_id, xianqi_info.need_num, xianqi_info.draw_type)
			else
				self:Close()
			end
		else
			SiXiangCallWGCtrl.Instance:TryToXianQiZhenLian(xianqi_info.item_id, xianqi_info.need_num, xianqi_info.draw_type)
		end
	end
	--]]
end

function XianQiRewardView:RefreshView()
	local reward_list = SiXiangCallWGData.Instance:GetMachineDrawReward()
	if #reward_list > 1 then
		self:ShowTenReward(reward_list)
		self:PlayTenRewardTween()
	elseif #reward_list == 1 then
		self:ShowOneReward(reward_list[1])
		self:PlayOneRewardTween()
	end

	self.is_shilian = #reward_list > 1
	self.node_list.once_content:SetActive(#reward_list == 1)
	self.node_list.reward_root:SetActive(#reward_list > 1)
	--self.node_list.draw_btn_lbl.text.text = Language.SiXiangCall.RefineSure

	--self:SetRootBgScale(false)

	----[[ 再抽一次
	local xianqi_info = SiXiangCallWGData.Instance:GetCacheXianQiType()
	if xianqi_info then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(xianqi_info.item_id)
		local can_get = has_num >= xianqi_info.need_num

		if xianqi_info.draw_type == MACHINE_DRAW_TYPE.SINGLE then
			self.node_list.draw_btn_lbl.text.text = string.format(Language.SiXiangCall.XQZLRefineAgain, 1)
		elseif xianqi_info.draw_type == MACHINE_DRAW_TYPE.MULTI then
			self.node_list.draw_btn_lbl.text.text = string.format(Language.SiXiangCall.XQZLRefineAgain, 10)
		elseif xianqi_info.draw_type == MACHINE_DRAW_TYPE.USE_MULTI_ITEM and can_get then
			self.node_list.draw_btn_lbl.text.text = string.format(Language.SiXiangCall.XQZLRefineAgain, 10)
		-- else
		-- 	self.node_list.draw_btn_lbl.text.text = Language.SiXiangCall.RefineSure
		end
	end
	--]]

	self:FlushCostInfoShow()
end

-- function XianQiRewardView:SetRootBgScale(is_show)
-- 	local is_skip = SiXiangCallWGCtrl.Instance:IsSkipZhenLianAnim()
-- 	if is_skip then

-- 		--self.node_list["xq_big_mask_bg"].transform:SetLocalScale(1, 1, 1)
-- 		self.node_list.root_bg.transform:SetLocalScale(1, 1, 1)
-- 		return
-- 	end
-- 	if is_show then
-- 		--self.node_list["xq_big_mask_bg"].transform:SetLocalScale(1, 1, 1)
-- 		self.node_list.root_bg.transform:SetLocalScale(1, 1, 1)
-- 	else
-- 		--self.node_list["xq_big_mask_bg"].transform:SetLocalScale(0, 0, 0)
-- 		self.node_list.root_bg.transform:SetLocalScale(0, 0, 0)
-- 	end
-- end

-- 单抽奖励展示
function XianQiRewardView:ShowOneReward(reward_data)
	self.once_summon_item:SetData(reward_data)
end

function XianQiRewardView:PlayOneRewardTween(is_play)
	local is_skip = SiXiangCallWGCtrl.Instance:IsSkipZhenLianAnim()
	if is_skip then
		self.once_summon_item:RestItem()
		self.once_summon_item:PlayYoyoTween()
	elseif is_play then
		self.once_summon_item:ReadyPlayTween()
		self.once_summon_item:PlayFlyTween(true)
	end
end

---[[ 十连抽动画播放
function XianQiRewardView:ShowTenReward(reward_list)
	if not IsEmptyTable(self.reward_item_list) then
		local item_list = self.reward_item_list
		for i=1,#item_list do
			item_list[i]:SetData(reward_list[i])
		end
	end
end

function XianQiRewardView:PlayTenRewardTween(is_play)
	local is_skip = SiXiangCallWGCtrl.Instance:IsSkipZhenLianAnim()
	local item_list = self.reward_item_list
	if is_skip then
		self:StopTween()
	elseif is_play and not self.is_play_tween then
		for i=1,#item_list do
			item_list[i]:ReadyPlayTween()
		end
		self.reward_item_list[1]:PlayFlyTween(true)
		self.play_tween_index = 1
		self.is_play_tween = true
	end
end

function XianQiRewardView:NextGetRewardTween()
	if self.is_play_tween then
		local index = self.play_tween_index + 1
		self.play_tween_index = index
		if self.reward_item_list[index] then
			self.reward_item_list[index]:PlayFlyTween(true)
		else
			self.is_play_tween = false
		end
	end
	--self:FlushOneKeyBtnState()
end

function XianQiRewardView:StopTween()
	if not IsEmptyTable(self.reward_item_list) then
		local item_list = self.reward_item_list
		for i=1,#item_list do
			item_list[i]:RestItem()
			item_list[i]:PlayYoyoTween()
		end
	end
	self.is_play_tween = false
	--self:FlushOneKeyBtnState()
end

-- function XianQiRewardView:ChangeMaskAlpha(is_finish)
-- 	local is_skip = SiXiangCallWGCtrl.Instance:IsSkipZhenLianAnim()
-- 	if is_skip then
-- 		self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
-- 	else
-- 		if is_finish then
-- 			self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
-- 		else
-- 			self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
-- 		end
-- 	end
-- end

-- function XianQiRewardView:FlushOneKeyBtnState()
-- 	-- local is_skip = SiXiangCallWGCtrl.Instance:IsSkipZhenLianAnim()
-- 	-- local xianqi_info = SiXiangCallWGData.Instance:GetCacheXianQiType()
-- 	-- local is_single = false
-- 	-- if xianqi_info then
-- 	-- 	is_single = xianqi_info.draw_type == MACHINE_DRAW_TYPE.SINGLE
-- 	-- end
-- 	-- if self.node_list["btn_one_key_open"] then
-- 	-- 	self.node_list["btn_one_key_open"]:SetActive(not is_skip and not is_single and self.is_play_tween)
-- 	-- end
-- end

--刷新货币信息
function XianQiRewardView:FlushCostInfoShow()
	local xianqi_info = SiXiangCallWGData.Instance:GetCacheXianQiType()
	if not xianqi_info then
		return 
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(xianqi_info.item_id)
	local str = string.format("%d/%d", has_num, xianqi_info.need_num)
	local str_color = has_num >= xianqi_info.need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    local bundle, asset = ResPath.GetItem(xianqi_info.item_id)

    self.node_list["const_img"].image:LoadSprite(bundle, asset)
    self.node_list["const_lbl"].text.text = ToColorStr(str, str_color)

    --折扣
    local show_discount = false
    local special_item_id = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("multi_lotto_consume_special")
	local xqzl_multi_item = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("multi_lotto_consume")
    if xqzl_multi_item and xqzl_multi_item.item_id ~= special_item_id and xianqi_info.draw_type ~= MACHINE_DRAW_TYPE.SINGLE
    	and xianqi_info.draw_type ~= MACHINE_DRAW_TYPE.USE_MULTI_ITEM then
        local multi_lotto = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("multi_lotto")
        if multi_lotto then
            local discount = math.floor(xqzl_multi_item.num / multi_lotto * 10)
            show_discount = discount < 10
            self.node_list["discount_lbl"].text.text = string.format(Language.SiXiangCall.DiscountText, discount)
        end
    end
	self.node_list["discount_bg"]:SetActive(show_discount)
end
--]]

--------------------------------------------------------------------------------------

XianQiRewardItem = XianQiRewardItem or BaseClass(XianQiZhenLianItem)

function XianQiRewardItem:__init()
	self.center_pos_x = 0
	self.center_pos_y = 0
	self.m_tween = nil
	self.m_yoyo_tween = nil
	self.is_best_reward = false
end

function XianQiRewardItem:__delete()
	self:StopItemTween()
end

function XianQiRewardItem:DoLoad(parent)
	self:LoadAsset("uis/view/shenji_tianci_ui_prefab", "xianqi_reward_item", parent.transform)
end

function XianQiRewardItem:LoadCallBack()
	self.model_pos_x, self.model_pos_y = RectTransform.GetAnchoredPositionXY(self.node_list.reward_model_root.rect)
	self:InitSummonItem()
end

function XianQiRewardItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)
	self:SetItemID(data.reward_item.item_id)

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.reward_item.item_id)
	if not item_cfg then
		return
	end

	self.is_best_reward = item_cfg.color >= SiXiangCallWGData.Instance.xianqi_best_reward_level
	self:FlushItem()
	self:FlushItemEffect(item_cfg.color)
	self.node_list.reward_model_root:SetActive(true)
end

function XianQiRewardItem:FlushItem()

	local xq_cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(self:GetItemID())
	if not xq_cfg then
		return
	end

	self:SetItemStar(xq_cfg.base_star)
	self:FlushItemName()
    self:FlushItemBg()
	self:FlushItemIcon()
	self:FlushItemStar()
	--self:SetZhenXiFlag(xq_cfg.special_flag == 1)
end

function XianQiRewardItem:FlushItemIcon()
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.save_item_id)
    if item_cfg and self.node_list.item_icon then
        local bundle,asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list.item_icon.image:LoadSprite(bundle, asset)
    end
end

function XianQiRewardItem:SetZhenXiFlag(is_zhenxi)
	if self.node_list["zhenxi_flag"] then
		self.node_list["zhenxi_flag"]:SetActive(is_zhenxi)
	end
end

function XianQiRewardItem:FlushItemBg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.save_item_id)
    local color = item_cfg and item_cfg.color or 1
    if self.node_list.item_bg then
        local bundle,asset = ResPath.GetCommon("a2_sc_btn_bg_" .. color)
        self.node_list.item_bg.image:LoadSprite(bundle, asset)
    end 
    if self.node_list.item_effect then
        local asset_name = BaseCell_Ui_Circle_Effect[color]
        local bundle,asset = ResPath.GetWuPinKuangEffectUi(asset_name)
        self.node_list.item_effect:ChangeAsset(bundle, asset)
    end
end

function XianQiRewardItem:FlushItemEffect(color)
	local eff_id = TIAN_SHEN_SHEN_SHI_MODEL_EFFECT[color]
	local bundle, asset = ResPath.GetEffectUi(eff_id)
	self.node_list.effect_root:ChangeAsset(bundle, asset)

	if self.is_best_reward then
		local bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_zhuanpanka_gj)
		self.node_list.best_effect_root:ChangeAsset(bundle, asset)
	end
end

-- 十连时播
function XianQiRewardItem:PlayFlyTween(need_move)
	if self.m_tween then
		return
	end

	self.node_list.reward_model_root.transform:SetLocalScale(0.5, 0.5, 0.5)
	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = need_move and self.node_list.tween_root.rect:DOAnchorPos(u3dpool.vec3(0, 0, 0), 0.2)
	local tween_alpha = self.node_list.tween_root.canvas_group:DoAlpha(0, 1, 0.2)

	--[[if self.is_best_reward then
		local tween_scale_big = self.node_list.tween_root.transform:DOScale(1.5, 0.5)
		tween_scale_big:SetEase(DG.Tweening.Ease.OutCubic)
		local tween_scale_min = self.node_list.tween_root.transform:DOScale(1, 0.2)
		local root_pos = self.node_list.reward_model_root.transform.position
		local tween_z_big = self.node_list.tween_root.transform:DOLocalMove(u3dpool.vec3(root_pos.x, root_pos.y, -50), 0.5)
		local tween_z_min = self.node_list.tween_root.transform:DOLocalMove(u3dpool.vec3(root_pos.x, root_pos.y, 0), 0.5)


		tween_sequence:Append(tween_scale_big)
		tween_sequence:Join(tween_alpha)
		tween_sequence:Join(tween_z_big)
		tween_sequence:InsertCallback(0.2, function ()
			self.node_list.best_effect_root:SetActive(true)
		end)
		tween_sequence:AppendInterval(1)
		tween_sequence:Append(tween_scale_min)
		tween_sequence:Join(tween_z_min)
		if need_move then
			tween_sequence:Append(tween_move)
		end
		tween_sequence:AppendCallback(function ()
			self.node_list.effect_root:SetActive(true)
		end)
	else--]]
		local tween_scale_big = self.node_list.tween_root.transform:DOScale(0.7, 0.1)
		local tween_scale_min = self.node_list.tween_root.transform:DOScale(1, 0.1)

		tween_sequence:Append(tween_alpha)
		if need_move then
			tween_sequence:Join(tween_move)
		end
		tween_sequence:Append(tween_scale_big)
		tween_sequence:Append(tween_scale_min)
		tween_sequence:InsertCallback(0.2, function ()
			self.node_list.effect_root:SetActive(true)
		end)
	--end

	tween_sequence:OnComplete(function ()
		self.m_tween:Kill()
		self.m_tween = nil
		self:PlayYoyoTween()
		SiXiangCallWGCtrl.Instance:PlayXQZLNextRewardTween()
	end)

	self.m_tween = tween_sequence
end

function XianQiRewardItem:PlayYoyoTween()
	if self.m_yoyo_tween then
		return
	end
	self.m_yoyo_tween = self.node_list.reward_model_root.transform:DOAnchorPosY(self.model_pos_y + 10, 1)
	self.m_yoyo_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
end

-- 算下在布局组件中间的偏移点(先只考虑10个两行的情况)
function XianQiRewardItem:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
	local index = self:GetIndex()
	local row = 5											-- 多少个一行
	local row_num = math.ceil(index / row)					-- 行数
	local center_index = row / 2 + row * (row_num - 1) 		-- 这一行的中间数
	local center_row_num = 1
	local center_x = (cell_x + spacing_x) * (center_index - index + 0.5)
	local center_y = -(cell_y + spacing_y) * (center_row_num - row_num + 0.5)
	self.center_pos_x = center_x
	self.center_pos_y = center_y
end

function XianQiRewardItem:ReadyPlayTween()
	self:StopItemTween()

	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, self.center_pos_x, self.center_pos_y)
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_model_root.rect, self.model_pos_x, self.model_pos_y)

	self.node_list.tween_root.canvas_group.alpha = 0
	self.node_list.effect_root:SetActive(false)
	self.node_list.best_effect_root:SetActive(false)
	self.node_list.reward_model_root.transform:SetLocalScale(0, 0, 0)
end

function XianQiRewardItem:RestItem()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 0, 0)
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_model_root.rect, self.model_pos_x, self.model_pos_y)
	self.node_list.tween_root.canvas_group.alpha = 1
	self.node_list.tween_root.transform:SetLocalScale(1, 1, 1)
	self.node_list.reward_model_root.transform:SetLocalScale(0.5, 0.5, 0.5)
	self.node_list.effect_root:SetActive(false)
	self.node_list.best_effect_root:SetActive(false)
	self:StopItemTween()
end

function XianQiRewardItem:StopItemTween()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
	if self.m_yoyo_tween then
		self.m_yoyo_tween:Kill()
		self.m_yoyo_tween = nil
	end
end