FlowerRankWGData = FlowerRankWGData or BaseClass(BaseWGCtrl)

function FlowerRankWGData:__init()
	if FlowerRankWGData.Instance then
		Error<PERSON><PERSON>("[FlowerRankWGData] attempt to create singleton twice!")  
		return
	end
	FlowerRankWGData.Instance = self
	self.rank_list = {} --魅力榜数据
	self.role_meili = 0 --角色魅力值
	self.role_rank_index = 0 --角色排名 
end

function FlowerRankWGData:__delete()
	FlowerRankWGData.Instance = nil
end

function FlowerRankWGData:SetFlowerRankList(data_list)
	self.rank_list = data_list
end

function FlowerRankWGData:GetFlowerRankList()
	return self.rank_list
end

--获取第一名
function FlowerRankWGData:GetRankOne()
	local rank_one = {}
	for i,v in pairs(self.rank_list) do 
		if v.rank_index == 1 then 
			rank_one = v
		end
	end
	return rank_one
end

--获取魅力榜(移除第一名)
function FlowerRankWGData:GetFlowerRankItemList()
	local list = __TableCopy(self.rank_list)
	local list2 = {}
	if next(list) ~= nil then 
		for i,v in pairs(list) do 
			if v.rank_index == 1 then 
				table.remove(list,i)
				break
			end
		end
		for i = 1 ,table.maxn(list) do 
			list2[i-1] = list[i]
		end 
	end
	return list2 
end

--获取服务器名字
function FlowerRankWGData:GetServerName(data)
	local server_list = GLOBAL_CONFIG.server_info.server_list	-- 区服		
	local cur_plat_type = RoleWGData.Instance.role_vo.plat_type 	
	local name = 0
	if cur_plat_type == data.plat_type then
		for k,v in pairs(server_list) do
			if v.id == data.server_id then
				name = v.name
				name = string.gsub(name, "%(.-%)", "")
				name = string.gsub(name, "（.-）", "")
			end
		end
	else
		name = Language.Common.WaiYu.."_s"..self.data.server_id
	end
	return name
end

--获取魅力榜(后十名)
function FlowerRankWGData:GetButtomRankList()
	local index = 11 --从第11名开始
	local list = __TableCopy(self.rank_list)
	local list2 = {}
	if table.maxn(list) >= index then 
		for k = index,table.maxn(list) do
			list2[k-10] = list[k]
		end
	end
	return list2 
end

--获取奖励列表
function FlowerRankWGData:GetRankAward()
	local list = ServerActivityWGData.Instance:GetCrossRandActivityConfig()
	local level = RoleWGData.Instance.role_vo.level
	local tab = {}
	for k,v in pairs(list.flower_rank_reward) do
		if v.role_level <= level and v.level_max >= level then
			table.insert(tab,v)
		end
	end
	return tab
end

--设置活动数据
function FlowerRankWGData:SetActivityData(protocol)
	self.role_meili = protocol.role_meili
end

--获取角色魅力
function FlowerRankWGData:GetRoleMeili()
	return self.role_meili
end
--获取角色排名
function FlowerRankWGData:GetRoleRankIndex()
	local role_vo =  GameVoManager.Instance:GetMainRoleVo()
	for i,v in pairs(self.rank_list) do 
		if v.user_id == role_vo.role_id then 
			self.role_rank_index = v.rank_index
			break
		end
	end
	return self.role_rank_index 
end






