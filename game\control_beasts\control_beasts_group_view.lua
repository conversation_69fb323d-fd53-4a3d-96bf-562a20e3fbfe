-- 灵宠选择同星选择界面
ControlBeastsGroupView = ControlBeastsGroupView or BaseClass(SafeBaseView)

function ControlBeastsGroupView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {sizeDelta = Vector2(838, 578)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_group")
    self.view_name = "ControlBeastsGroupView"
    self:SetMaskBg(true)
end

function ControlBeastsGroupView:ReleaseCallBack()
	if self.skill_list then
		self.skill_list:DeleteMe()
		self.skill_list = nil
	end

	self.cur_skill_skill_data = nil
	self.cur_skill_skill_index = nil
	self.jump_select_skill_seq = nil
end

function ControlBeastsGroupView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName4 or ""

	if not self.skill_list then
		self.skill_list = AsyncListView.New(BeststsSkillGroupItemRender, self.node_list.skill_list_view)
		self.skill_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSkillCB, self))
	end

	XUI.AddClickEventListener(self.node_list.skill_yulan_btn, BindTool.Bind2(self.GroupSkillYuLan, self))
end

function ControlBeastsGroupView:OnSelectSkillCB(beasts_item, cell_index, is_default, is_click)
    if nil == beasts_item or nil == beasts_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = beasts_item:GetIndex()
	if self.cur_skill_skill_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

    self.is_force_refresh = false
	self.cur_skill_skill_data = beasts_item.data
	self.cur_skill_skill_index = cell_index

	self:FlushSkillMessage()
end

function ControlBeastsGroupView:OnFlush()
    local group_skill_list = ControlBeastsWGData.Instance:GetCurBattleGroupSkillList()


	if self.jump_select_skill_seq == nil then
        -- 这里筛选出一个优先选择的
        self.jump_select_skill_seq = 0
    end

    if self.skill_list ~= nil then
        self.skill_list:SetDataList(group_skill_list)
        if self.jump_select_skill_seq then
            if self.jump_select_skill_seq == 0 then
                self.skill_list:JumpToIndex(1, 2)
            elseif self.jump_select_skill_seq ~= 100 then
                self.skill_list:JumpToIndex(self.jump_select_skill_seq, 2)
                self.jump_select_skill_seq = 100
            end
        end
    end
end

function ControlBeastsGroupView:FlushSkillMessage()
    if not self.cur_skill_skill_data then
        return
    end
	
	local skill_type_str = "a2_ys_di_dw%d"
	local real_skill_type_str = string.format(skill_type_str, self.cur_skill_skill_data.beast_group_id)
	self.node_list.skill_type.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(real_skill_type_str))


	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(self.cur_skill_skill_data.group_skill_id, 3)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.skill_desc.text.text = client_cfg.description
	end

	local skill_name_str = "a2_ys_group_%d"
	self.node_list.skill_name_icon.image:LoadSprite(ResPath.GetControlBeastsImg(string.format(skill_name_str, self.cur_skill_skill_data.group_skill_name_icon)))
	local color = self.cur_skill_skill_data.is_unlock and COLOR3B.D_GREEN or COLOR3B.D_RED
	local dec = string.format(Language.ContralBeasts.GroupSkillTips1, self.cur_skill_skill_data.need_num, 
								Language.ContralBeasts.BttleTypeGroup[self.cur_skill_skill_data.need_type])
	self.node_list.skill_name_condition.text.text = ToColorStr(dec, color)
end

function ControlBeastsGroupView:GroupSkillYuLan()
	if not self.cur_skill_skill_data then
        return
    end

	CommonSkillShowCtrl.Instance:SetBeastSkillViewDataAndOpen({beast_group_id = self.cur_skill_skill_data.beast_group_id})
end

----------------------------------灵兽组合技能item-----------------------
BeststsSkillGroupItemRender = BeststsSkillGroupItemRender or BaseClass(BaseRender)
function BeststsSkillGroupItemRender:OnFlush()
    if not self.data then
        return
    end

	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.group_skill_id)
	local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(self.data.group_skill_id)
	if client_cfg and beast_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.lbl_name.text.text = beast_cfg.skill_name
		-- self.node_list.desc.text.text = client_cfg.description
	end
end

function BeststsSkillGroupItemRender:OnSelectChange(is_select)
	self.node_list.select_bg:CustomSetActive(is_select)
	self.node_list.normal_bg:CustomSetActive(not is_select)
end