ExChangePanelView = ExChangePanelView or BaseClass(SafeBaseView)

function ExChangePanelView:__init()
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "exchange_panel")
	self:SetMaskBg(true, true)
end

function ExChangePanelView:__delete()
end

function ExChangePanelView:ReleaseCallBack()
end

function ExChangePanelView:LoadCallBack()
	self.node_list.sub_btn.button:AddClickListener(BindTool.Bind1(self.OnClickSub, self))
	self.node_list.add_btn.button:AddClickListener(BindTool.Bind1(self.OnClickAdd, self))
	self.node_list.exchange_btn.button:AddClickListener(BindTool.Bind1(self.OnClickExchange, self))
	self.node_list.mask_btn.button:AddClickListener(BindTool.Bind1(self.OnClickMask, self))
	self.node_list.num_bg.button:AddClickListener(BindTool.Bind1(self.OnClickNumBg, self))
	self.cur_times = 0
end

function ExChangePanelView:ShowIndexCallBack()
	self.cur_times = self.max_times
	self:Flush()
end

function ExChangePanelView:SetData(data, max_times, exchange_func)
	self.data = data
	self.max_times = max_times
	self.exchange_func = exchange_func
end

function ExChangePanelView:OnFlush()
	local color = self.cur_times > self.max_times and COLOR3B.RED or COLOR3B.WHITE
	self.node_list.num.text.text = ToColorStr(self.cur_times, color)
end

function ExChangePanelView:OnClickSub()
	if self.cur_times <= 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.MinValue1)
		return
	end
	self.cur_times = self.cur_times - 1
	self:Flush()
end

function ExChangePanelView:OnClickAdd()
	if self.cur_times >= self.max_times then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.MaxValue)
		return
	end

	self.cur_times = self.cur_times + 1
	self:Flush()
end

function ExChangePanelView:OnClickExchange()
	if self.exchange_func then
		self.exchange_func(self.cur_times)
	end
	self:Close()
end

function ExChangePanelView:OnClickMask()
	self:Close()
end

function ExChangePanelView:OnClickNumBg()
	local data = {}

	data.item_num = self.cur_times or 0
	data.max = self.max_times or 0
	data.node = self.node_list.num

	data.ok_func = function ()
		self.cur_times = tonumber(self.node_list.num.text.text)
		self:Flush()
	end

	ViewManager.Instance:Open(GuideModuleName.SpecialKeypadView, 0, "keypad", data)
end