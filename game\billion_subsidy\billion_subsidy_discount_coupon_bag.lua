BillionSubsidyDCBag = BillionSubsidyDCBag or BaseClass(SafeBaseView)

local MAX_VISIABLE_ROW = 2 --最多可视区域行数

function BillionSubsidyDCBag:__init()
	self:SetMaskBg(true)
    local bundle_name = "uis/view/billion_subsidy_ui_prefab"
    self:AddViewResource(0, bundle_name, "layout_billion_subsidy_discount_coupon_bag")
end

function BillionSubsidyDCBag:LoadCallBack()
	if nil == self.discount_coupon_select_grid then
		local bundle, asset = "uis/view/billion_subsidy_ui_prefab", "discount_coupon_select_cell"
		self.discount_coupon_select_grid = AsyncBaseGrid.New()
		self.discount_coupon_select_grid:CreateCells({col = 4, itemRender = BillionSubsidyDiscountCouponSelectCell,
			list_view = self.node_list.discount_coupon_select_grid, assetBundle = bundle, assetName = asset, change_cells_num = 1})
		self.discount_coupon_select_grid:SetStartZeroIndex(false)
        self.discount_coupon_select_grid:SetSelectCallBack(BindTool.Bind(self.SelectDiscountCouponCallBack, self))
	end

    if not self.grid_scroller_fun then
		self.grid_scroller_fun = BindTool.Bind(self.ShopScrollerEndScrolled, self)
		self.node_list.discount_coupon_select_grid.scroller.scrollerEndScrolled = self.grid_scroller_fun
	end
end

function BillionSubsidyDCBag:ReleaseCallBack()
	if self.discount_coupon_select_grid then
		self.discount_coupon_select_grid:DeleteMe()
		self.discount_coupon_select_grid = nil
	end

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.grid_scroller_fun = nil
end

function BillionSubsidyDCBag:CloseCallBack()

end

function BillionSubsidyDCBag:ShowIndexCallBack()
    RectTransform.SetLocalScale(self.node_list.content.rect, 1)
    RectTransform.SetAnchoredPositionXY(self.node_list.content.rect, 0, 0)
end

--tab页签的箭头显示.
function BillionSubsidyDCBag:ShopScrollerEndScrolled()
	local val = self.node_list.discount_coupon_select_grid.scroll_rect.verticalNormalizedPosition
	local cell_row = self.discount_coupon_select_grid:GetListViewNumbers()
	self.node_list.guide_img:SetActive(val ~= 0 and cell_row > MAX_VISIABLE_ROW and val > 0.1)
end

function BillionSubsidyDCBag:SelectDiscountCouponCallBack(cell)
    local data = cell:GetData()
	if nil == data then
		return
	end

    local other_cfg = BillionSubsidyWGData.Instance:GetOtherCfg()
    if data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        TipWGCtrl.Instance:OpenItem({item_id = other_cfg.free_ticket_click_item_id})
    elseif data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON then
        TipWGCtrl.Instance:OpenItem({item_id = other_cfg.full_discount_ticket_click_item_id})
    end
    -- BillionSubsidyWGData.Instance:SetSelectDCData(data)
    -- BillionSubsidyWGCtrl.Instance:FlushCurShowIndex()
    -- self:PlayAni()
end

function BillionSubsidyDCBag:PlayAni()
	local move_tween_1 = self.node_list.content.rect:DOAnchorPos(Vector2(591, 265), 0.4)
	local scale_tween_1 = self.node_list.content.rect:DOScale(Vector3(0.05, 0.05, 0.05), 0.4)

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:Append(scale_tween_1)
	self.sequence:Join(move_tween_1)
	self.sequence:OnComplete(function ()
		self:Close()
	end)
end

function BillionSubsidyDCBag:OnFlush()
    local show_index =  BillionSubsidyWGCtrl.Instance:GetCurShowIndex()
    local dc_all_data_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList(show_index)
    self.discount_coupon_select_grid:SetDataList(dc_all_data_list)
    local is_empty = IsEmptyTable(dc_all_data_list)
    self.node_list.blank_tips:SetActive(is_empty)
    self.node_list.discount_coupon_select_grid:SetActive(not is_empty)
end

BillionSubsidyDiscountCouponSelectCell = BillionSubsidyDiscountCouponSelectCell or BaseClass(BaseRender)
function BillionSubsidyDiscountCouponSelectCell:LoadCallBack()

end

function BillionSubsidyDiscountCouponSelectCell:__delete()

end

function BillionSubsidyDiscountCouponSelectCell:OnFlush()
	if self.data == nil then
		return
	end

    if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        self.node_list.free_discount_coupon_content:SetActive(true)
        self.node_list.other_discount_coupon_content:SetActive(false)
        self.node_list.limit_use_text.text.text = Language.BillionSubsidy.FreeDCQuotaLimit2
    else
        self.node_list.free_discount_coupon_content:SetActive(false)
        self.node_list.other_discount_coupon_content:SetActive(true)
        self.node_list.name.text.text = Language.BillionSubsidy.DCNameList[self.data.type]
        self.node_list.save_money_text.text.text = string.format(Language.BillionSubsidy.DCReduceQuota, self.data.reduce_quota)
        if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
            self.node_list.limit_use_text.text.text = Language.BillionSubsidy.DCNoQuotaLimit
        else
            self.node_list.limit_use_text.text.text = string.format(Language.BillionSubsidy.DCQuotaLimit, self.data.quota_limit)
        end
    end
end