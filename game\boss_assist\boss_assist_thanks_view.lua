
BossAssistThanksView = BossAssistThanksView or BaseClass(SafeBaseView)
function BossAssistThanksView:__init()
	self.is_use_mask = true
	self.default_index = 1

	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_thanks")
	self.select_index = 1
end

function BossAssistThanksView:__delete()
end

function BossAssistThanksView:ReleaseCallBack()
	if self.role_list then
		self.role_list:DeleteMe()
		self.role_list = nil
	end

	if self.reward_m then
		self.reward_m:DeleteMe()
		self.reward_m = nil
	end

	if self.reward_f then
		self.reward_f:DeleteMe()
		self.reward_f = nil
	end

	if self.btn_list then
		self.btn_list:DeleteMe()
		self.btn_list = nil
	end
end

function BossAssistThanksView:LoadCallBack(index, loaded_times)
	if loaded_times <= 1 then
		self.role_list = AsyncListView.New(AssistThanksRoleRender, self.node_list.role_list)
		self.role_list:JumpToTop()

		local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_assist_auto").other[1]

		self.reward_m = ItemCell.New(self.node_list.ph_reward_m)
		self.reward_m:SetData(other_cfg.owner_thank_reward_item)
		self.reward_f = ItemCell.New(self.node_list.ph_reward_f)
		self.reward_f:SetData(other_cfg.assist_thank_reward_item)

		self.btn_list = AsyncListView.New(AssistThanksDecRender, self.node_list["ph_btnlist"])
		self.btn_list:SetDataList(Language.BossAssist.AssistThankDec)
		self.btn_list:SetSelectCallBack(BindTool.Bind1(self.SelectBtnListCallBack, self))
		self.btn_list:SetDefaultSelectIndex(1)

		XUI.AddClickEventListener(self.node_list.btn_auto_add, BindTool.Bind(self.AutoAddFriend, self))
		XUI.AddClickEventListener(self.node_list.btn_thanks, BindTool.Bind(self.EnterThanks, self))
		XUI.AddClickEventListener(self.node_list.chat_input, BindTool.Bind(self.OpenDecList, self))
		XUI.AddClickEventListener(self.node_list.btn_close_list, BindTool.Bind(self.CloseDecList, self))
	end
end

function BossAssistThanksView:ShowIndexCallBack(index)
	self:Flush(index)
end

function BossAssistThanksView:OpenDecList()
	self.node_list.layout_choosebtn:SetActive(true)
end

function BossAssistThanksView:CloseDecList()
	self.node_list.layout_choosebtn:SetActive(false)
end

function BossAssistThanksView:OpenCallBack()

end

function BossAssistThanksView:CloseCallBack()

end

function BossAssistThanksView:SelectBtnListCallBack(cell, index, is_default)
	if is_default or nil == cell or nil == cell.data then return end
	if self.select_index ~= cell.index then
		self.select_index = cell.index
		self:FlushDec()
		self:CloseDecList()
	end
end

function BossAssistThanksView:AutoAddFriend()
	local thank_info = BossAssistWGData.Instance:GetGuildAssistThankRoleListInfo()
	for k,v in pairs(thank_info.thank_role_info_list) do
		if not SocietyWGData.Instance:CheckIsFriend(v.role_id) then
			SocietyWGCtrl.Instance:AddFriend(v.role_id,1)
		end
	end
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyFriendSuccess)
end

function BossAssistThanksView:EnterThanks()
	local thank_info = BossAssistWGData.Instance:GetGuildAssistThankRoleListInfo()
	BossAssistWGCtrl.SendGuildAssistThankOthers(self.select_index, thank_info.target_boss_scene_id, thank_info.target_boss_id)
	self:Close()
end


function BossAssistThanksView:OnFlush(param_t, index)
	local info = BossAssistWGData.Instance:GetGuildAssistThankRoleListInfo()
	self.role_list:SetDataList(info.thank_role_info_list)
	self:FlushDec()
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(info.target_boss_scene_id)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[info.target_boss_id]
	if monster_cfg and scene_cfg then
		self.node_list.boss_name.text.text = string.format(Language.BossAssist.ThankViewBossNameText, scene_cfg.name, monster_cfg.name .. "lv." .. monster_cfg.level)
	end
	local assist_info = BossAssistWGData.Instance:GetAssistInfo()
	local times_max =  ConfigManager.Instance:GetAutoConfig("guild_assist_auto").other[1].thank_my_reward_times_max
	local has_reward = assist_info.day_thank_reward_times < times_max

	self.reward_m:MakeGray(not has_reward)
	self.node_list.my_reward:SetActive(has_reward)
	self.node_list.yilinwan:SetActive(not has_reward)
end

function BossAssistThanksView:FlushDec()
	self.node_list.thank_dec.text.text = Language.BossAssist.AssistThankDec[self.select_index]
end

----------------------------------------------------------------------------
-- AssistThanksRoleRender
----------------------------------------------------------------------------
AssistThanksRoleRender = AssistThanksRoleRender or BaseClass(BaseRender)
function AssistThanksRoleRender:__init()

end

function AssistThanksRoleRender:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function AssistThanksRoleRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_add_friend, BindTool.Bind(self.AddFriend, self))
	self.head_cell = BaseHeadCell.New(self.node_list["head"])
end

function AssistThanksRoleRender:OnFlush()
	self.node_list.role_name.text.text = self.data.role_name

	local is_df, level = RoleWGData.Instance:GetDianFengLevel(self.data.role_level)
	self.node_list["img_feixian"]:SetActive(is_df)
	self.node_list.level.text.text = level

	self.node_list.btn_add_friend:SetActive(not SocietyWGData.Instance:CheckIsFriend(self.data.role_id))
	self.head_cell:SetData(self.data)
end

function AssistThanksRoleRender:AddFriend()
	SocietyWGCtrl.Instance:AddFriend(self.data.role_id, 0)
end

----------------------------------------------------------------------------
-- AssistThanksDecRender
----------------------------------------------------------------------------
AssistThanksDecRender = AssistThanksDecRender or BaseClass(BaseRender)
function AssistThanksDecRender:__init()
	XUI.AddClickEventListener(self.view, BindTool.Bind1(self.ClickHandler, self))
end

function AssistThanksDecRender:LoadCallBack()

end

function AssistThanksDecRender:OnFlush()
	if not self.data then return end
	self.node_list.text_shaixuan_name.text.text = self.data
	self.node_list.select_bg:SetActive(self.is_select)
end

-- 选择状态改变
function AssistThanksDecRender:OnSelectChange(is_select)
	if self.node_list.select_bg then
		self.node_list.select_bg:SetActive(is_select)
	end
end

function AssistThanksDecRender:ClickHandler()
	if self.click_callback then
		self.click_callback(self)
	end
end

function AssistThanksDecRender:CreateSelectEffect()
end
