function FairyLandEquipmentView:InitGodBodyView()
    if nil == self.gb_uplevel_attr_list then
        self.gb_uplevel_attr_list = {}
        local attr_num = self.node_list.god_body_attr_part.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(self.node_list.god_body_attr_part:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.gb_uplevel_attr_list[i] = cell
        end
    end

    if nil == self.gb_uplevel_icon_list then
        self.gb_uplevel_icon_list = {}
        --local gouyu_num = self.node_list.gouyu_list1.transform.childCount
        for i = 1, 9 do
            local cell = GBGouYuIconRender.New(self.node_list["gouyu_" .. i])
            cell:SetIndex(i)
            self.gb_uplevel_icon_list[i] = cell
        end
    end

    if nil == self.gb_skill_list then
        self.gb_skill_list = {}
        --local skill_num = self.node_list.god_body_skill_list.transform.childCount
        for i = 1, 8 do
            local cell = GodBodySkillRender.New(self.node_list["skill_" .. i])
            cell:SetIndex(i)
            self.gb_skill_list[i] = cell
        end
    end

    XUI.AddClickEventListener(self.node_list.uplevel_skill, BindTool.Bind(self.ClickUplevelSkill, self))
    XUI.AddClickEventListener(self.node_list.upgrade_skill, BindTool.Bind(self.ClickUpgradeSkill, self))
    XUI.AddClickEventListener(self.node_list.btn_god_body_uplevel, BindTool.Bind(self.ClickGodBodyUplevel, self))
    --XUI.AddClickEventListener(self.node_list.upgrade_stuff_icon, BindTool.Bind(self.ClickUpgradeStuff, self))
end

function FairyLandEquipmentView:DeleteGodBodyView()
    self:CleanUpLevelCalcCD()

    if self.gb_uplevel_attr_list then
        for k,v in pairs(self.gb_uplevel_attr_list) do
            v:DeleteMe()
        end
        self.gb_uplevel_attr_list = nil
    end

    if self.gb_uplevel_icon_list then
        for k,v in pairs(self.gb_uplevel_icon_list) do
            v:DeleteMe()
        end
        self.gb_uplevel_icon_list = nil
    end

    if self.gb_skill_list then
        for k,v in pairs(self.gb_skill_list) do
            v:DeleteMe()
        end
        self.gb_skill_list = nil
    end

    if self.buy_remind_alert then
        self.buy_remind_alert:DeleteMe()
        self.buy_remind_alert = nil
    end

    self.is_god_body_new_slot = nil
end

function FairyLandEquipmentView:CleanUpLevelCalcCD()
    if CountDown.Instance:HasCountDown(self.uplevel_cd_quest) then
        CountDown.Instance:RemoveCountDown(self.uplevel_cd_quest)
        self.uplevel_cd_quest = nil
    end
end

function FairyLandEquipmentView:FlushGodBodyView()
    local gb_data = self:GetCurSelectSlotData()
    if gb_data == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local slot = gb_data:GetSlotIndex()
    local level = gb_data:GetLevel()
    local grade = gb_data:GetGrade()
    local is_max_level = gb_data:GetIsMaxLevel()

    -- 等级
    local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
    local level_cfg = fle_data:GetGBUpLevelCfg(slot, level)
    local next_level_cfg = fle_data:GetGBUpLevelCfg(slot, level + 1)
    if level_cfg then
        self.node_list.god_body_level.text.text = level_cfg.name
        local index = math.floor(level / 10)
        local bundle, asset = ResPath.GetRawImagesPNG("a3_ys_bq" .. index)
        self.node_list.god_body_name_bg.raw_image:LoadSprite(bundle, asset, function()
            self.node_list.god_body_name_bg.raw_image:SetNativeSize()
        end)

        bundle, asset = ResPath.GetFairyLandEquipImages("a3_ys_wb" .. index)
        self.node_list.level_img.image:LoadSprite(bundle, asset, function()
            self.node_list.level_img.image:SetNativeSize()
        end)
    end

    if next_level_cfg then
        self.node_list.next_god_body_level.text.text = next_level_cfg.name
    end

    if slot_cfg then
        self.node_list.model_name_text.text.text = slot_cfg.jieling_name
    end

    -- 勾玉
    local show_num = level % 10
    --self.node_list.gouyu_list1:SetActive(show_num < 4)
    --self.node_list.gouyu_list2:SetActive(show_num >= 4)

    for k,v in pairs(self.gb_uplevel_icon_list) do
        v:SetIsNewSlotData(self.is_god_body_new_slot)
        v:SetData(gb_data)
    end
    self.is_god_body_new_slot = false

    -- 技能
    local skill_list = fle_data:GetGoBSkillShowList(slot)
    for k,v in ipairs(self.gb_skill_list) do
        if #skill_list >= k then
            self.node_list["skill_" .. k]:SetActive(true)
            v:SetData(skill_list[k])
        else
            self.node_list["skill_" .. k]:SetActive(false)
        end
    end

    -- 属性
    local attr_lsit = fle_data:GetUpLevelShowAttr(slot, level)
    for k,v in pairs(self.gb_uplevel_attr_list) do
        v:SetRealHideNext(is_max_level)
        v:SetData(attr_lsit[k])
    end

    -- 战力
    self.node_list.god_body_cap_value.text.text = fle_data:GetGodBodyCapability(slot)

    --修炼技能
    local buy_seq = gb_data:GetRightBuySeq()
    local uplevel_buy = gb_data:GetIsBuyUplevelRight()
    local uplevel_cfg = fle_data:GetGBUplevelRightCfg(buy_seq)
    self.node_list["uplevel_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(uplevel_cfg.skill_id))
    self.node_list.uplevel_skill_icon.image:SetNativeSize()
    self.node_list.uplevel_skill_state.text.text = uplevel_buy and Language.FairyLandEquipment.YiJiHuo or Language.FairyLandEquipment.WeiJiHuo
    XUI.SetGraphicGrey(self.node_list["uplevel_skill_bg"], not uplevel_buy)

    --渡劫技能
    local upgrade_buy = gb_data:GetIsBuyUpgradeRight()
    local upgrade_cfg = fle_data:GetGBUpgradeRightCfg(buy_seq)
    self.node_list["upgrade_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(upgrade_cfg.skill_id))
    self.node_list.upgrade_skill_icon.image:SetNativeSize()
    self.node_list.upgrade_skill_state.text.text = upgrade_buy and Language.FairyLandEquipment.YiJiHuo or Language.FairyLandEquipment.WeiJiHuo
    XUI.SetGraphicGrey(self.node_list["upgrade_skill_bg"], not upgrade_buy)

    self.node_list.god_body_uplevel_nomax:SetActive(not is_max_level)
    self.node_list.god_body_uplevel_ismax:SetActive(is_max_level)
    self.node_list.god_body_level_arrow:SetActive(not is_max_level)
    self.node_list.next_god_body_level:SetActive(not is_max_level)

    self:FlushGodBodyBtnPart()
end

function FairyLandEquipmentView:FlushGodBodyBtnPart()
    local gb_data = self:GetCurSelectSlotData()
    if gb_data == nil then
        return
    end

    local is_max_level = gb_data:GetIsMaxLevel()
    if is_max_level then
        return
    end

    self:CleanUpLevelCalcCD()
    local fle_data = FairyLandEquipmentWGData.Instance
    local fle_str = Language.FairyLandEquipment
    local slot = gb_data:GetSlotIndex()
    local level = gb_data:GetLevel()
    local grade = gb_data:GetGrade()

    local grade_cfg = fle_data:GetGBUpGradeCfg(slot, grade)
    local cur_max_up_level = grade_cfg and grade_cfg.up_max_level or 0
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = gb_data:GetUplevelEndTime()
    local rest_time = end_time - now_time

    local show_remind = false
    local show_uplevel_part = true
    local show_upGrade_part = false
    local show_right_cd = false
    local show_uplevel_finish_text = false
    local show_uplevel_quick = false
    local btn_text = 1
    local is_full_progress = true

    -- 修炼中
    if rest_time > 0 and level < cur_max_up_level then
        btn_text = 2
        is_full_progress = false
        self:FlushUpLevelCDText(rest_time)
        self.uplevel_cd_quest = CountDown.Instance:AddCountDown(rest_time, 1,
                                BindTool.Bind(self.CalcUpLevelCD, self),
                                function()
                                    self:FlushGodBodyBtnPart()
                                    -- 确保购买修炼特权，时间到了能自动升级
                                    if gb_data:GetIsBuyUplevelRight() then
                                        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BODY_UPLEVEL, slot)
                                    else
                                        self:FlushViewByIndex()
                                        RemindManager.Instance:Fire(RemindName.FLE_GodBody)
                                    end
                                end)
        show_uplevel_quick = true

        -- 修炼特权
        local buy_seq = gb_data:GetRightBuySeq()
        local is_buy_uplevle_right = fle_data:GetGodBodyUplevelRightIsBuy(buy_seq)
        if is_buy_uplevle_right then
            show_right_cd = true
            local level_cfg = fle_data:GetGBUpLevelCfg(slot, level)
            local uplevel_time = level_cfg and level_cfg.uplevel_time or 0
            local right_cfg = fle_data:GetGBUplevelRightCfg(buy_seq)
            local right_per = right_cfg and right_cfg.up_time_per or 0
            local cut_time = uplevel_time * right_per * 0.0001
            local show_cut_time = ""
            local cut_time_min = math.ceil(cut_time / 60)
            if cut_time_min == 1 and cut_time < 60 then
                show_cut_time = string.format(fle_str.CutShowTime3, math.ceil(cut_time))
            elseif cut_time_min > 0 then
                show_cut_time = string.format(fle_str.CutShowTime2, cut_time_min)
            end

            local right_per_str = right_per > 0 and string.format(fle_str.RightPerStr2, show_cut_time) or ""
            self.node_list.uplevel_right_cd.text.text = right_per_str
        end

    -- 渡劫阶段
    elseif level == cur_max_up_level then
        show_uplevel_part = false
        show_upGrade_part = true
        btn_text = 3
        if grade_cfg then
            -- 成功率
            local success_per_str = string.format("<color=#99ffbb>%s%%</color> ", grade_cfg.succ_per * 0.01)
            local buy_seq = gb_data:GetRightBuySeq()
            local is_buy_upgrade_right = fle_data:GetGodBodyUpgradeRightIsBuy(buy_seq)
            local right_cfg = fle_data:GetGBUpgradeRightCfg(buy_seq)
            local right_per = (right_cfg and is_buy_upgrade_right) and right_cfg.dujie_add_per or 0
            local right_per_str = right_per > 0 and string.format(fle_str.RightPerStr, right_per * 0.01) or ""
            success_per_str = success_per_str .. right_per_str
            self.node_list.upgrade_success_per.text.text = success_per_str

            -- 祝福值
            local bless_value = gb_data:GetBlessValue()
            local need_bless_value = fle_data:GetGBUpGradeNeedBless()
            local enough_bless_value = bless_value >= need_bless_value
            self.node_list.upgrade_bless_slider.slider.value = bless_value / need_bless_value
            self.node_list.upgrade_bless_value.text.text = string.format("%s / %s", bless_value, need_bless_value)
            self.node_list.upgrade_remind_tips.text.text = enough_bless_value and fle_str.EnoughBlessStr or fle_str.NoEnoughBlessStr

            -- 渡劫材料
            local item_cfg = ItemWGData.Instance:GetItemConfig(grade_cfg.stuff_id)
            if item_cfg then
                self.node_list.upgrade_stuff_icon:SetActive(false)
                local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
                self.node_list.upgrade_stuff_icon.image:LoadSprite(bundle, asset, function()
                    self.node_list.upgrade_stuff_icon:SetActive(true)
                end)
            end

            local num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.stuff_id)
            local is_have_stuff = num >= grade_cfg.stuff_num
            local str_color = is_have_stuff and COLOR3B.L_GREEN or COLOR3B.L_RED
            local item_str = string.format("%s/%s", ToColorStr(num, str_color), grade_cfg.stuff_num)
            self.node_list.upgrade_stuff.text.text = item_str

            show_remind = enough_bless_value or is_have_stuff
        end

    -- 刚激活需手动点
    elseif level == 0 and end_time == 0 then
        show_remind = true
    else
        show_uplevel_finish_text = true
        show_remind = true
    end

    --self.node_list.uplevel_finish_img:SetActive(show_uplevel_finish_text)
    self.node_list.uplevel_right_cd:SetActive(show_right_cd)
    self.node_list.uplevel_quick_part:SetActive(show_uplevel_quick)
    self.node_list.uplevel_part:SetActive(show_uplevel_part)
    self.node_list.upgrade_part:SetActive(show_upGrade_part)
    self.node_list.btn_god_body_uplevel_img:SetActive(not show_upGrade_part)
    self.node_list.upgrade_stuff_part:SetActive(show_upGrade_part)
    --self.node_list.btn_god_body_upgrade:SetActive(show_upGrade_part)
    self.node_list.gb_img_slider:SetActive(not show_upGrade_part)
    self.node_list.uplevel_right_cd_part:SetActive(btn_text == 2)
    self.node_list.uplevel_cd_finish_text:SetActive(btn_text == 1)

    self.node_list.btn_god_body_uplevel_img.text.text = fle_str.UpLevelBtnStr[btn_text] or ""
    self.node_list.btn_god_body_uplevel_remind:SetActive(show_remind)
    if is_full_progress then
        self.node_list.gb_img_slider.slider.value = 1
    end
end

function FairyLandEquipmentView:PlayDuJieEffect()
    local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanshen_shuiwen_kuosan")
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["dujie_effect_pos"].transform)
end

function FairyLandEquipmentView:CalcUpLevelCD(elapse_time, total_time)
    self:FlushUpLevelCDText(total_time - elapse_time)
end

function FairyLandEquipmentView:FlushUpLevelCDText(rest_time)
    local fle_str = Language.FairyLandEquipment
    if self.node_list.uplevel_cd_text then
        self.node_list.uplevel_cd_text.text.text = string.format(fle_str.UplevelTimeDesc, TimeUtil.FormatSecondDHM9(rest_time))
    end

    local gb_data = self:GetCurSelectSlotData()
    if gb_data then
        if self.node_list.gb_img_slider then
            local need_time = gb_data:GetUplevelNeedTime()
            self.node_list.gb_img_slider.slider.value = (need_time - rest_time) / need_time
        end

        if self.node_list.uplevel_quick_text then
            local cost_bind_gold = FairyLandEquipmentWGData.Instance:CalcUpLevelCost(
                                    gb_data:GetUplevelEndTime(), gb_data:GetSlotIndex(), gb_data:GetLevel())
            self.node_list.uplevel_quick_text.text.text = cost_bind_gold--string.format(fle_str.UplevelQuickStr, cost_bind_gold)

            if nil ~= self.buy_remind_alert and self.buy_remind_alert:IsOpen() then
                self.buy_remind_alert:SetLableString(string.format(fle_str.UpLevelQuickStr, cost_bind_gold))
            end
        end
    end
end

function FairyLandEquipmentView:ClickGodBodyUplevel()
    local gb_data = self:GetCurSelectSlotData()
    if gb_data == nil then
        return
    end

    local is_max_level = gb_data:GetIsMaxLevel()
    if is_max_level then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local fle_str = Language.FairyLandEquipment
    local slot = gb_data:GetSlotIndex()
    local level = gb_data:GetLevel()
    local grade = gb_data:GetGrade()

    local grade_cfg = fle_data:GetGBUpGradeCfg(slot, grade)
    local cur_max_up_level = grade_cfg and grade_cfg.up_max_level or 0
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = gb_data:GetUplevelEndTime()
    local rest_time = end_time - now_time

    -- 修炼中
    if rest_time > 0 and level < cur_max_up_level then
        if nil == self.buy_remind_alert then
            self.buy_remind_alert = Alert.New(nil, nil, nil, nil, true)
            self.buy_remind_alert:SetCheckBoxDefaultSelect(false)
        end

        local cost_bind_gold = fle_data:CalcUpLevelCost(
                                        gb_data:GetUplevelEndTime(), slot, level)
        self.buy_remind_alert:SetLableString(string.format(fle_str.UpLevelQuickStr, cost_bind_gold))
        self.buy_remind_alert:SetOkFunc(function()
            FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BODY_COST_UPLEVEL, slot)
        end)

        self.buy_remind_alert:Open()
    -- 渡劫阶段
    elseif level == cur_max_up_level then
        if grade_cfg then
            -- 祝福值
            local bless_value = gb_data:GetBlessValue()
            local need_bless_value = fle_data:GetGBUpGradeNeedBless()
            local enough_bless_value = bless_value >= need_bless_value

            -- 渡劫材料
            local num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.stuff_id)
            local is_have_stuff = num >= grade_cfg.stuff_num
            if not enough_bless_value and not is_have_stuff then
                local item_cfg = ItemWGData.Instance:GetItemConfig(grade_cfg.stuff_id)
                if item_cfg then
                    local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
                    self:SetGBUpGradeStuffBuyDefNum(grade_cfg)
                    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = grade_cfg.stuff_id})
                end
                return
            end

            -- 前往渡劫
            --渡劫的战力延迟飘字
            --MainuiWGData.Instance:CreateCacheTable()
            FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BODY_UPGRADE, slot)
        end
    -- 刚激活需手动点
    elseif level == 0 and end_time == 0 then
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BREAK_JING_MAI, slot)
    else
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BODY_UPLEVEL, slot)
    end
end

-- function FairyLandEquipmentView:ClickUpgradeStuff()
--     local gb_data = self:GetCurSelectSlotData()
--     if gb_data == nil then
--         return
--     end

--     local fle_data = FairyLandEquipmentWGData.Instance
--     local slot = gb_data:GetSlotIndex()
--     local grade = gb_data:GetGrade()
--     local grade_cfg = fle_data:GetGBUpGradeCfg(slot, grade)
--     if grade_cfg then
--         self:SetGBUpGradeStuffBuyDefNum(grade_cfg)
--         TipWGCtrl.Instance:OpenItemTipGetWay({item_id = grade_cfg.stuff_id})
--     end
-- end

function FairyLandEquipmentView:SetGBUpGradeStuffBuyDefNum(grade_cfg)
    if IsEmptyTable(grade_cfg) then return end
    local num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.stuff_id)
    local lerp_num = grade_cfg.stuff_num - num
    if lerp_num > 0 then
        local buy_list = TipWGData.Instance:GetShopBuyList(grade_cfg.stuff_id)
        if not IsEmptyTable(buy_list) then
            local buy_data = buy_list[1]
            local price_type = buy_data.price_type
            local price = buy_data.price
            local has_money = 0
            local def_num = 1
            local money_type = MoneyType.BangYu
            if price_type == Shop_Money_Type.Type2 then
                money_type = MoneyType.BangYu
            elseif price_type == Shop_Money_Type.Type1 then
                money_type = MoneyType.XianYu
            end
            has_money = RoleWGData.Instance:GetMoney(money_type)
            if has_money >= lerp_num * price then
                def_num = lerp_num
            end
            TipWGData.Instance:SetDefShowBuyCount(def_num)
        end
    end
end

function FairyLandEquipmentView:ClickUplevelSkill()
    local gb_data = self:GetCurSelectSlotData()
    if gb_data == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local buy_seq = gb_data:GetRightBuySeq()
    local uplevel_cfg = fle_data:GetGBUplevelRightCfg(buy_seq)
    local is_buy_uplevel = gb_data:GetIsBuyUplevelRight()
    if is_buy_uplevel then
        local limit_text = ToColorStr(Language.MultiMount.HasActive, COLOR3B.D_GREEN)
        local show_data = {
            icon = uplevel_cfg.skill_id,
            top_text = uplevel_cfg.name,                    -- 技能名
            top_text_color = COLOR3B.D_GREEN,
            body_text = uplevel_cfg.skill_desc,                         -- 当前等级技能描述
            capability = fle_data:GetGodBodyUplevelRightCapability(gb_data:GetSlotIndex()),
            x = 0,
            y = 0,
            set_pos2 = true,
            limit_text = limit_text,                    -- （底部）限制描述
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    else
        FairyLandEquipmentWGCtrl.Instance:OpenGodBodyRightView(GOD_BODY_ENUM.UPLEVEL_RIGHT, gb_data:GetSlotIndex())
    end 
end

function FairyLandEquipmentView:ClickUpgradeSkill()
    local gb_data = self:GetCurSelectSlotData()
    if gb_data == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local buy_seq = gb_data:GetRightBuySeq()
    local upgrade_cfg = fle_data:GetGBUpgradeRightCfg(buy_seq)
    local is_buy_upgrade = gb_data:GetIsBuyUpgradeRight()
    if is_buy_upgrade then
        local limit_text = ToColorStr(Language.MultiMount.HasActive, COLOR3B.D_GREEN)
        local show_data = {
            icon = upgrade_cfg.skill_id,
            top_text = upgrade_cfg.name,                    -- 技能名
            top_text_color = COLOR3B.D_GREEN,
            body_text = upgrade_cfg.skill_desc,                         -- 当前等级技能描述
            capability = fle_data:GetGodBodyUpgradeRightCapability(gb_data:GetSlotIndex()),
            x = 0,
            y = 0,
            set_pos2 = true,
            limit_text = limit_text,                    -- （底部）限制描述
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    else
        FairyLandEquipmentWGCtrl.Instance:OpenGodBodyRightView(GOD_BODY_ENUM.UPGRADE_RIGHT, gb_data:GetSlotIndex())
    end
end
