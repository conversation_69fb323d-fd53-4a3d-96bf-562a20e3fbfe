--铭文寻宝

local Slider_Vlaue = {
    [1] = 1,
    [2] = 0.4,
    [3] = 0.6,
    [4] = 0.8,
    [5] = 0,
}

function TreasureHuntView:InitMingwenView()
    self.node_list.btn_one.button:AddClickListener(BindTool.Bind(self.OnClickMingwenTreasure, self, 1))
    self.node_list.btn_ten.button:AddClickListener(BindTool.Bind(self.OnClickMingwenTreasure, self, 2))
    self.node_list.btn_fuwen_bag.button:AddClickListener(BindTool.Bind(self.OnClickMingwenBag, self))
    self.node_list.btn_fuwen_convert.button:AddClickListener(BindTool.Bind(self.OnClickMinwenConvert, self))
    self.node_list.key_bg_1.button:AddClickListener(BindTool.Bind(self.OnClickMingwenKey, self))
    self.node_list.key_bg_2.button:AddClickListener(BindTool.Bind(self.OnClick<PERSON><PERSON><PERSON><PERSON>, self))
    self.node_list.mingwen_record_btn.button:AddClickListener(BindTool.Bind(self.OpenRecord, self))
    self.node_list.mingwen_gailv.button:AddClickListener(BindTool.Bind(self.OnClickGaiLvBtn, self))
    self.node_list.mingwen_yulan.button:AddClickListener(BindTool.Bind(self.OnClickMingWenYuLanBtn, self))
    self.node_list.btn_open_onesword_frostbite.button:AddClickListener(BindTool.Bind(self.OnClickOpenOneSwordFrostbiteBtn, self))

    TreasureHuntWGData.Instance:SetOldWeekIndex(0)
end

function TreasureHuntView:OpenRecord()
    TreasureHuntWGCtrl.Instance:DoMingWenOpera(TreasureHuntWGData.POSY_OPERATOR_TYPE.POSY_SHOP_RECORD_INFO)
    TreasureHuntWGCtrl.Instance:OpenMingwenRecordView()
end

function TreasureHuntView:OnClickMingwenKey()
    local mingwen_mode = TreasureHuntWGData.Instance:GetMingwenModeCfg()
    local consume_id = mingwen_mode[1].stuff_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = consume_id})
end

function TreasureHuntView:CreateMingWenReward()
    if self.mingwen_reward_list == nil then
        self.mingwen_reward_list = {}
    end
    for i = 1, 5 do
        if self.node_list["week_reward"..i] then
            self.mingwen_reward_list[i] = MingWenRewardItem.New(self.node_list["week_reward"..i])
        end
    end
end

--铭纹释放
function TreasureHuntView:ReleaseMingwen()
    if self.mingwen_reward_list then
        for k,v in ipairs(self.mingwen_reward_list) do
            if v then
                v:DeleteMe()
            end
        end
        self.mingwen_reward_list = nil
    end

    if self.show_item_list then
        for k, v in pairs(self.show_item_list) do
            v:DeleteMe()
        end

        self.show_item_list = nil
    end

    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

    if self.move_scroll then
        self.move_scroll:Kill()
        self.move_scroll = nil
    end

    self.parent_scroll_rect = nil

end

--铭纹背包
function TreasureHuntView:OnClickMingwenBag()
    MingWenWGCtrl.Instance:OpenMingWenBag(-1, true)
end

--铭纹兑换
function TreasureHuntView:OnClickMinwenConvert()
    FunOpen.Instance:OpenViewByName(GuideModuleName.MingWenView, TabIndex.ming_wen_dui_huan)
    TreasureHuntWGData.Instance:SetTreasureHuntTab(4)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
    --self:Close()
end

--寻宝请求
function TreasureHuntView:OnClickMingwenTreasure(index)
    --p2 0免费，1不免费
    TreasureHuntWGCtrl.Instance:OnClickMingwenBtn(index)
end

function TreasureHuntView:FlushShowBigItem()
    local other_info = TreasureHuntWGData.Instance:GetMingwenOtherCfg()

    if not self.show_item_list then
        self.show_item_list = {}

        for i = 1, 2 do
            self.show_item_list[i] = MingWenItemRender.New(self.node_list["mingwen_cell_pos" .. i])
            self.show_item_list[i]:SetData({item_id = other_info["show_best_item" .. i]})

            if other_info["show_pos" .. i] and other_info["show_pos" .. i] ~= "" then
                local pos_list = string.split(other_info["show_pos" .. i], "|")
                local pos_x = tonumber(pos_list[1]) or 0
                local pos_y = tonumber(pos_list[2]) or 0
                local pos_z = tonumber(pos_list[3]) or 0

                RectTransform.SetAnchoredPositionXY(self.node_list["mingwen_cell" .. i].rect, pos_x, pos_y)
            end

            if other_info["show_rot" .. i] and other_info["show_rot" .. i] ~= "" then
                local rot_list = string.split(other_info["show_rot" .. i], "|")
                local rot_x = tonumber(rot_list[1]) or 0
                local rot_y = tonumber(rot_list[2]) or 0
                local rot_z = tonumber(rot_list[3]) or 0

                self.node_list["mingwen_cell" .. i].transform.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)
            end

            if other_info["show_scale" .. i] and other_info["show_scale" .. i] ~= "" then
                local scale = other_info["show_scale" .. i]
                Transform.SetLocalScaleXYZ(self.node_list["mingwen_cell" .. i].transform, scale, scale, scale)
            end
        end
    end

    -- for i = 1, 7 do
    --     self.show_item_list[i]:SetShowCualityBg(false)
    --     self.show_item_list[i]:SetData({item_id = other_info["show_best_item" .. i]})
    --     self.show_item_list[i]:SetEffectRootEnable(false)
    --     self.show_item_list[i]:SetCellBgEnabled(false)
    --     self.show_item_list[i]:SetBindIconVisible(false)
    -- end
end

function TreasureHuntView:FlushMingwenView()
    self:FlushShowBigItem()

    --周奖励
    if self.mingwen_reward_list == nil then
        self:CreateMingWenReward()
    end
    local week_info = TreasureHuntWGData.Instance:GetMingwenWeek()
    for k, v in ipairs(self.mingwen_reward_list) do
        v:SetData(week_info[k])
    end
    local cur_index = 1
    for i,v in ipairs(week_info) do
        cur_index = i
        if v.is_get == 0 then
            break
        end
    end
    local base_info = TreasureHuntWGData.Instance:GetMingWenInfo()
    local old_index = TreasureHuntWGData.Instance:GetOldWeekIndex()
    if cur_index ~= old_index then
        TreasureHuntWGData.Instance:SetOldWeekIndex(cur_index)
        self.node_list.week_scroll.scroll_rect.verticalNormalizedPosition = Slider_Vlaue[cur_index]
    end
    local slider_value = TreasureHuntWGData.Instance:GetCurProgress(base_info.week_count)
    self.node_list.week_reward_bg.slider.value = slider_value
    -- self.node_list.cur_week_times.text.text = string.format(Language.TreasureHunt.CurMingWenWeekTimes, base_info.week_count)
    self.node_list.cur_week_times.text.text = base_info.week_count
    local mingwen_mode = TreasureHuntWGData.Instance:GetMingwenModeCfg()
    local consume_id = mingwen_mode[1].stuff_id
    --self.node_list["week_scroll"].scroll_rect.verticalNormalizedPosition
    local item_cfg = ItemWGData.Instance:GetItemConfig(consume_id)
    -- for i = 1, 2 do
    --     self.node_list["key_icon"..i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    --     local has_num = ItemWGData.Instance:GetItemNumInBagById(mingwen_mode[i].stuff_id) --拥有的数量
    --     local color = has_num >= mingwen_mode[i].stuff_num and COLOR3B.D_GREEN or COLOR3B.WHITE
    --     self.node_list["consume"..i].text.text = ToColorStr(has_num .. "/" .. mingwen_mode[i].stuff_num, color)
    --     self.node_list["mingwen_text_up"..i].text.text = string.format(Language.TreasureHunt.MingWenTreasure,  mingwen_mode[i].count)
    -- end

    for i = 1, 2 do
        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list["key_icon"..i].image:LoadSprite(bundle, asset, function()
            self.node_list["key_icon"..i].image:SetNativeSize()
        end)
        local has_num = ItemWGData.Instance:GetItemNumInBagById(mingwen_mode[i].stuff_id) --拥有的数量
        -- 铭文寻宝vip特权处理
        local cfg_need_stuff = mingwen_mode[i].stuff_num
        local mingwen_vip_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(mingwen_mode[i].mode, true)
        local need_num = not IsEmptyTable(mingwen_vip_cfg) and mingwen_vip_cfg.vip_stuff_num or cfg_need_stuff
        local color = has_num >= need_num and COLOR3B.DEFAULT_NUM or "#ff9393"
        self.node_list["consume"..i].text.text = ToColorStr(has_num .. "/" .. need_num, color)
        self.node_list["mingwen_text_up"..i].text.text = string.format(Language.TreasureHunt.MingWenTreasure,  mingwen_mode[i].count)

        if not IsEmptyTable(mingwen_vip_cfg) and self.node_list.mingwen_zhekou_text then
            self.node_list.mingwen_zhekou_text.text.text = mingwen_vip_cfg.zhekou
        end
    end
    
    self.time = base_info.free_timestamp
    self:RefreshRemainTime()
    if self.refresh_event == nil and self.time ~= 0 and self.time > TimeWGCtrl.Instance:GetServerTime() then
        self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),1)
    end
    self.node_list["btn_fuwen_exchange_remind"]:SetActive(TreasureHuntWGData.Instance:ConvertRemind())
    self:FlushMingwenBtnRemind(2)

    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE)
	local remind = OneSwordFrostbiteWGData.Instance:GetOneSwordFrostbiteRemind()
	self.node_list["btn_open_onesword_frostbite"]:SetActive(is_open)
	self.node_list["btn_open_onesword_frostbite_remind"]:SetActive(remind == 1)
end

function TreasureHuntView:FlushMingwenBtnRemind(index)
    for i = 1, index do
        self.node_list["btn_mingwen_remind"..i]:SetActive(TreasureHuntWGData.Instance:GetViewMingWenBtnRemind(i) == 1)
    end
end

function TreasureHuntView:RefreshRemainTime()
    local time = TreasureHuntWGData.Instance:GetNextFreeTime()
    local mingwen_mode = TreasureHuntWGData.Instance:GetMingwenModeCfg()
	local state = time > 0
    if state then
        if self.node_list.free_time1 then
            self.node_list.free_time1.text.text = string.format(Language.TreasureHunt.FreeTimeDes,TimeUtil.FormatSecond(time, 3))
        end
        
        self.node_list["mingwen_text_up1"].text.text = string.format(Language.TreasureHunt.MingWenTreasure, mingwen_mode[1].count)
        -- self.node_list.key_bg_1:SetActive(true)
        self.node_list.key_bg_1_1:CustomSetActive(true)
    else
        if self.node_list.free_time1 then
            self.node_list.free_time1.text.text = Language.TreasureHunt.FreeTime
        end

        self.node_list["mingwen_text_up1"].text.text = string.format(Language.TreasureHunt.MingWenTreasure1, mingwen_mode[1].count)
        -- self.node_list.key_bg_1:SetActive(false)
        self.node_list.key_bg_1_1:CustomSetActive(false)
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end

    end
    self:FlushMingwenBtnRemind(1)
end

function TreasureHuntView:OnClickMingWenYuLanBtn()
    TreasureHuntWGCtrl.Instance:OpenMingWenYuLanView()
end

function TreasureHuntView:OnClickOpenOneSwordFrostbiteBtn()
	ViewManager.Instance:Open(GuideModuleName.OneSwordFrostbiteView)
end