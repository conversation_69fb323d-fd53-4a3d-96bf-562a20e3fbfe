require("game/hot_spring/hot_spring_wg_data")
require("game/hot_spring/hot_spring_info")
require("game/hot_spring/hot_spring_skill_view")
require("game/hot_spring/hot_spring_progress_view")
require("game/hot_spring/hot_spring_gather_timer")
require("game/hot_spring/hot_spring_role_list_view")

-- 温泉控制器
HotSpringWGCtrl = HotSpringWGCtrl or BaseClass(BaseWGCtrl)

function HotSpringWGCtrl:__init()
	if HotSpringWGCtrl.Instance ~= nil then
		ErrorLog("[HotSpringWGCtrl] Attemp to create a singleton twice !")
	end
	HotSpringWGCtrl.Instance = self
	self.data = HotSpringWGData.New()
	self.fb_info = HotSpringInfo.New()
	-- self.sanxiao_view = HotSpringSanXiaoView.New()
	self.skill_view = HotSpringSkillView.New()
	self.progress_view = HotSpringProgressView.New()
	self.gather_timer = HotSpringGatherTimer.New()
    self.role_list_view = HotSpringRoleListView.New()
	self.activity_call_back = BindTool.Bind(self.ActivityChangeCallback, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_call_back)

    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))

	self:RegisterAllProtocols()
	self.cur_score = 0
	self.is_move_end = false


end

function HotSpringWGCtrl:__delete()
	if self.fb_info ~= nil then
		self.fb_info:DeleteMe()
		self.fb_info = nil
	end

	if self.activity_call_back then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_call_back)
		self.activity_call_back = nil
	end

	-- if self.sanxiao_view ~= nil then
	-- 	self.sanxiao_view:DeleteMe()
	-- 	self.sanxiao_view = nil
	-- end

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.skill_view ~= nil then
		self.skill_view:DeleteMe()
		self.skill_view = nil
    end
    
    if self.role_list_view ~= nil then
		self.role_list_view:DeleteMe()
		self.role_list_view = nil
	end
    
	if self.progress_view ~= nil then
		self.progress_view:DeleteMe()
		self.progress_view = nil
	end

	if self.gather_timer ~= nil then
		self.gather_timer:DeleteMe()
		self.gather_timer = nil
	end

	if self.vip_change_event then
		GlobalEventSystem:UnBind(self.vip_change_event)
		self.vip_change_event = nil
	end

	HotSpringWGCtrl.Instance = nil
end

function HotSpringWGCtrl:RegisterAllProtocols()
	-- -- 注册接收到的协议
	self:RegisterProtocol(SCHotspringPlayerInfo, "OnHotspringPlayerInfo")				-- 温泉玩家信息
	self:RegisterProtocol(SCHotspringActionBroadcast, "OnHotspringActionBroadcast")		-- 温泉动作广播
	self:RegisterProtocol(SCHotspringShuangxiuInfo, "OnHotspringShuangxiuInfo")			-- 温泉双修信息 温泉双修信息通知（包含增量通知）
	self:RegisterProtocol(CSHotspringAction)											-- 温泉动作请求

	self:RegisterProtocol(SCHotspringMonsterInfo, "OnHotspringMonsterInfo")
	self:RegisterProtocol(CSHotspringRefreshMonster)

	self:RegisterProtocol(KFSCHotspringPlayerInfo, "OnHotspringPlayerInfo")				-- 温泉玩家信息
	self:RegisterProtocol(KFSCHotspringActionBroadcast, "OnHotspringActionBroadcast")	-- 温泉动作广播
	self:RegisterProtocol(KFSCHotspringShuangxiuInfo, "OnHotspringShuangxiuInfo")		-- 温泉双修信息 温泉双修信息通知（包含增量通知）
	self:RegisterProtocol(KFCSHotspringAction)											-- 温泉动作请求

	self:RegisterProtocol(SCHotSpringGatherInfo,"OnSCHotSpringGatherInfo") 				-- 温泉采集物刷新提示
	self:RegisterProtocol(KFSCHotSpringGatherInfo,"OnSCHotSpringGatherInfo") 			-- 温泉采集物刷新提示

	self:RegisterProtocol(KFSCHotspringMonsterInfo, "OnHotspringMonsterInfo")
	self:RegisterProtocol(KFCSHotspringRefreshMonster)
end

function HotSpringWGCtrl:ActivityChangeCallback(activity_type, status, next_status_switch_time, open_type)
	if activity_type == ACTIVITY_TYPE.HOTSPRING or activity_type == ACTIVITY_TYPE.KF_HOTSPRING then
		if status == ACTIVITY_STATUS.CLOSE then
			HotSpringWGData.Instance:ClearHotSpringCache()
		end
	end
end

-- 打开副本左侧面板
function HotSpringWGCtrl:FbInfoOpen()
	self.fb_info:Open()
end

-- 关闭副本左侧面板
function HotSpringWGCtrl:FbInfoClose()
	self.fb_info:Close()
end

-- 打开技能面板
function HotSpringWGCtrl:SkillViewOpen()
	self.skill_view:Open()
end

-- 关闭技能面板
function HotSpringWGCtrl:SkillViewClose()
	self.skill_view:Close()
end

function HotSpringWGCtrl:OpenRoleListView()
	self.role_list_view:Open()
end

function HotSpringWGCtrl:CloseRoleListView()
	self.role_list_view:Close()
end


-- 打开副本左侧面板
function HotSpringWGCtrl:SanXiaoViewOpen()
	-- self.sanxiao_view:Open()
end


function HotSpringWGCtrl:ShowAction(boo)
	if self.fb_info then
		self.fb_info:ShowAction(boo)
	end
end

function HotSpringWGCtrl:HideTaskHandler()
	self.fb_info:HideTaskHandler()
end

function HotSpringWGCtrl:OnHotspringPlayerInfo(protocol)
	--print_error(protocol)
	self.data:SetHotspringPlayerInfo(protocol)
	self.fb_info:Flush()
	self.skill_view:Flush()

end

function HotSpringWGCtrl:OnHotspringActionBroadcast(protocol)
	local role_actor = Scene.Instance:GetObjectByObjId(protocol.actor_id)
	if role_actor then
		role_actor.vo.shuangxiu_attack_state = 1
		role_actor.vo.shuangxiu_action_type = protocol.action_type
		role_actor.vo.shuangxiu_attack_target_id = protocol.target_id
	end

	local role_target = Scene.Instance:GetObjectByObjId(protocol.target_id)
	if role_target then
		role_target.vo.shuangxiu_attack_state = 0
		role_target.vo.shuangxiu_action_type = protocol.action_type
	end

	self.actor_id = protocol.actor_id
	self.target_id = protocol.target_id

	-- 目标者
	local role_2 = Scene.Instance:GetRoleByObjId(protocol.target_id)
	if role_2 then
		local callback2 = function ()
			if protocol.action_type == 2 then
				--不需要变身
				-- role_2:SetAttr("special_appearance",SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING)
				--雪球
				local follow_ui = role_2:GetFollowUi()
				if follow_ui then
					follow_ui:ChangeBubble(Language.HotSpring.AttackTips1_2)
				end
				self.fb_info:HideFace()
			else
				local follow_ui = role_2:GetFollowUi()
				if follow_ui then
					local str = HotSpringWGData.Instance:GetRamdomBePoShuiStr() or Language.HotSpring.AttackTips2_2
					follow_ui:ChangeBubble(str)
				end
				self.fb_info:HideFace()
			end
		end

		role_2:DoHotSpringAttack(callback2)
	end

	-- 发起者 xxxx
	local role_1 = Scene.Instance:GetRoleByObjId(protocol.actor_id)
	if role_1 then
	    --朝向目标
	    if role_2 then
	    	local x,y = role_2:GetLogicPos()
	    	role_1:SetDirectionByXY(x,y)
	    end

		local callback1 = function ()
				--扔雪球
			if protocol.action_type == 2 then
				local follow_ui = role_1:GetFollowUi()
				if follow_ui then
					follow_ui:ChangeBubble(string.format(Language.HotSpring.AttackTips1_1,role_2.vo.name))
				end
				self.fb_info:HideFace()
			else
				--泼热水
				local follow_ui = role_1:GetFollowUi()
				if follow_ui then
					follow_ui:ChangeBubble(Language.HotSpring.AttackTips2_1)
				end
				self.fb_info:HideFace()
			end

			--打断双修
		 	-- self:SendHotspringAction(protocol.target_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_DISMISS_SHUANGXIU)
		end

		role_1:DoHotSpringAttack(callback1, role_2)
	end
end

function HotSpringWGCtrl:HideFace()
	if not self.actor_id or not self.target_id then  return end
	-- 发起者
	local role_1 = Scene.Instance:GetRoleByObjId(self.actor_id)
	if role_1 then
		local follow_ui = role_1:GetFollowUi()
		if follow_ui then
			follow_ui:HideBubble()
		end
	end
	-- 目标者
	local role_2 = Scene.Instance:GetRoleByObjId(self.target_id)
	if role_2 then
		local follow_ui = role_2:GetFollowUi()
		if follow_ui then
			follow_ui:HideBubble()
		end
	end
end

function HotSpringWGCtrl:OnHotspringShuangxiuInfo(protocol)
	self.data:SetHotspringShuangxiuInfo(protocol)
	self:SetShuangXiuInfo(protocol.is_cancel_shuangxiu, protocol.shuangxiu_list)
	self.fb_info:Flush()
end

function HotSpringWGCtrl:SetShuangXiuInfo(is_cancel_shuangxiu, shuangxiu_list)
	 -- print_error("获得新信息！！",is_cancel_shuangxiu,shuangxiu_list)
	for k, v in pairs(shuangxiu_list) do
		if v.action_type == HOTSPRING_BOAT_TYPE.SHUANGXIU then
			--新增双修
			self:SetShuangXiuRole(v)
		elseif v.action_type == HOTSPRING_BOAT_TYPE.ANMO then
			--新增按摩
			self:SetAnmoRole(v)
		elseif v.action_type == HOTSPRING_BOAT_TYPE.NONE then
			--取消当前动作
			self:CancelCurAction(v)
		end
	end
end

function HotSpringWGCtrl:CancelCurAction(role_data)
	local role_1 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id1)
	local role_2 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id2)

	--取消按摩, 如果是按摩直接返回
	if role_1 and role_1:IsRole() and role_1:GetAnmoState() == ANMO_TYPE.IS_ANMO then
		self:CancelAnMoRole(role_data)
		return
	end
	--取消双修
	if role_1 and role_1:IsRole() and role_1:ShuangXiuState() == SHUANGXIU_TYPE.IS_SHUANGXIU then
		self:CalcelShuangXiuRole(role_data)
	end
end

--更新按摩角色
function HotSpringWGCtrl:SetAnmoRole(role_data)
	local role_1 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id1)
	local role_2 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id2)

	if (role_1 and role_1:IsMainRole()) or (role_2 and role_2:IsMainRole()) then
		self:OpenProgressView()
	end
	--print_error(role_1:IsMainRole(), role_2:IsMainRole())
	if role_1 and role_1:IsRole() then
		role_1:SetAnmoState(ANMO_TYPE.IS_ANMO)
		role_1:SetAnmoPartnerId(role_data.role_obj_id2)
		role_1:SetIsAnmoSender(true)
		--发起按摩的人需要显示气泡
		role_1:GetFollowUi():ChangeBubble(Language.HotSpring.AttackTips3_1)
        --print_error("协议下发更新发起人", role_1:GetObjId())
		role_1:UpdateBoat()
	end

	if role_2 and role_2:IsRole() then
		--print_error("协议下发更新接收人", role_2:GetObjId())
		role_2:SetAnmoState(ANMO_TYPE.IS_ANMO)
		role_2:SetAnmoPartnerId(role_data.role_obj_id1)
		role_2:SetIsAnmoSender(false)
		role_2:UpdateBoat()
	end
end

--取消按摩
function HotSpringWGCtrl:CancelAnMoRole(role_data)
	local role_1 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id1)
	local role_2 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id2)

	if role_1 and role_1:IsRole() then
		role_1:SetAnmoState(ANMO_TYPE.NO_ANMO)
		role_1:SetAnmoPartnerId(-1)
		--发起按摩的人需要隐藏气泡
		role_1:GetFollowUi():HideBubble()
		role_1:UpdateBoat()
	end

	if role_2 and role_2:IsRole() then
		role_2:SetAnmoState(ANMO_TYPE.NO_ANMO)
		role_2:SetAnmoPartnerId(-1)
		role_2:UpdateBoat()
	end
	--GlobalEventSystem:Fire(HotSpring.ANMO_STATE_CHANGE)
end

--更新双修角色
function HotSpringWGCtrl:SetShuangXiuRole(role_data)
	local role_1 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id1)
	local role_2 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id2)
	if role_1 == nil or role_2 == nil then
		return
	end

	local is_cross = Scene.Instance:GetSceneType() == SceneType.KF_HotSpring
	local cfg = ConfigManager.Instance:GetAutoConfig(is_cross and "cross_hotspring_auto" or "hotspring_auto").other[1]
	local higher_boat_vip_level = cfg and cfg.higher_boat_vip_level or 6
	local is_use_vip_boat = role_1:GetVo().vip_level >= higher_boat_vip_level or role_2:GetVo().vip_level >= higher_boat_vip_level

	if role_1 then
		role_1:SetIsVipBoat(is_use_vip_boat)
		role_1:ShuangXiuState(SHUANGXIU_TYPE.IS_SHUANGXIU)
		role_1:ShuangXiuIdentity(1)
		role_1:SetShuangxiuPartner(role_data.role_obj_id2)
		role_1:UpdateBoat()
	end

	if role_2 then
		role_2:SetIsVipBoat(is_use_vip_boat)
		role_2:ShuangXiuState(SHUANGXIU_TYPE.IS_SHUANGXIU)
		role_2:ShuangXiuIdentity(0)
		role_2:SetShuangxiuPartner(role_data.role_obj_id1)
		role_2:UpdateBoat()
	end
end

--取消双修
function HotSpringWGCtrl:CalcelShuangXiuRole(role_data)
	local role_1 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id1)
	local role_2 = Scene.Instance:GetObjectByObjId(role_data.role_obj_id2)

	if role_1 and role_1:IsRole() then
		role_1:ShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU)
		role_1:ShuangXiuIdentity(-1)
		role_1:SetShuangxiuPartner(-1)
		role_1:UpdateBoat()
	end

	if role_2 and role_2:IsRole() then
		role_2:ShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU)
		role_2:ShuangXiuIdentity(-1)
		role_2:SetShuangxiuPartner(-1)
		role_2:UpdateBoat()
	end
	GlobalEventSystem:Fire(HotSpring.SHUANGXIU_STATE)
end

--采集物刷新提示
function HotSpringWGCtrl:OnSCHotSpringGatherInfo( protocol )
	self.data:GatherRefreshRemainTime(protocol.gather_refresh_remain_time)
	self:FlushGatherTimerView()
	--if FuBenPanelWGCtrl.Instance:GetIsCountDowning() then
	--	return
	--end
	--
	--local act_info
	--if Scene.Instance:GetSceneType() == SceneType.HotSpring then
	--	act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HOTSPRING)
	--elseif Scene.Instance:GetSceneType() == SceneType.KF_HotSpring then
	--	act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HOTSPRING)
	--end
	--
	--
	--if act_info == nil or act_info.next_time - protocol.gather_refresh_remain_time < 10 then
	--	return
	--end
	--if self.fb_info then
	--	self.fb_info:CompontentCountDown()
	--end
end

function HotSpringWGCtrl:OnRoleEnter(obj_id)
	if SceneType.HotSpring ~= Scene.Instance:GetSceneType() or SceneType.KF_HotSpring ~= Scene.Instance:GetSceneType() then
		return
	end

	local shuangxiu_list = self.data:GetShuangXiuAllList()
	local shuangxiu_item = nil
	for k, v in pairs(shuangxiu_list) do
		if v.role_obj_id1 == obj_id or v.role_obj_id2 == obj_id then
			shuangxiu_item = v
			break
		end
	end

	if nil ~= shuangxiu_item then
		local role_1 = Scene.Instance:GetObjectByObjId(shuangxiu_item.role_obj_id1)
		local role_2 = Scene.Instance:GetObjectByObjId(shuangxiu_item.role_obj_id2)

		if nil ~= role_1 and nil ~= role_2 then
			self.data:SetShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU, {shuangxiu_item})
			-- self:SetShuangXiuInfo(0, {shuangxiu_item})
		end
	end
	self:SetAllRoleAppearance()
end

function HotSpringWGCtrl:OnHotspringMonsterInfo(protocol)
	self.data:SetHotspringMonsterInfo(protocol)
end

-- 温泉动作请求
function HotSpringWGCtrl:SendHotspringAction(target_id, action_type)
	--if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.HOTSPRING) then   --跨服温泉合本服不要同时开 不然GG
	--	local protocol = ProtocolPool.Instance:GetProtocol(CSHotspringAction)
	--	protocol.target_id = target_id or 0
	--	protocol.action_type = action_type or 0
	--	protocol:EncodeAndSend()
	--elseif ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_HOTSPRING) then
	--	local protocol = ProtocolPool.Instance:GetProtocol(KFCSHotspringAction)
	--	protocol.target_id = target_id or 0
	--	protocol.action_type = action_type or 0
	--	protocol:EncodeAndSend()
	--end

	if Scene.Instance:GetSceneType() == SceneType.HotSpring then
		local protocol = ProtocolPool.Instance:GetProtocol(CSHotspringAction)
		protocol.target_id = target_id or 0
		protocol.action_type = action_type or 0
		protocol:EncodeAndSend()
	elseif Scene.Instance:GetSceneType() == SceneType.KF_HotSpring then
		local protocol = ProtocolPool.Instance:GetProtocol(KFCSHotspringAction)
		protocol.target_id = target_id or 0
		protocol.action_type = action_type or 0
		protocol:EncodeAndSend()
	end
end

function HotSpringWGCtrl:SendGameReq(req_type, monster_index)		--请求领取迷宫积分奖励
	local protocol = ProtocolPool.Instance:GetProtocol(CSHotspringRefreshMonster)
	protocol.is_start_game_req = req_type
	protocol.monster_index = monster_index or 0
	protocol:EncodeAndSend()
end

function HotSpringWGCtrl:SendKillMonsterReq(monster_index)
	-- self.sanxiao_view:SendKillMonsterReq(monster_index)
end
function HotSpringWGCtrl:SetAllRoleAppearance()
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		if nil ~= main_role and main_role:IsRole() then
			main_role:SetAttr("special_appearance",SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON)
		end
	end

	local role_list = Scene.Instance:GetRoleList()
	for k ,v in pairs(role_list) do
		if nil ~= role_list  then
			v:SetAttr("special_appearance",SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON)
		end
	end
end

function HotSpringWGCtrl:OpenProgressView()
	self.progress_view:Open()
end

function HotSpringWGCtrl:CloseProgressView()
	if self.progress_view:IsOpen() then
		self.progress_view:Close()
	end
end

function HotSpringWGCtrl:OpenGatherTimerView()
	self.gather_timer:Open()
end

function HotSpringWGCtrl:FlushGatherTimerView()
	self.gather_timer:Flush()
end

function HotSpringWGCtrl:CloseGatherTimerView()
	if self.gather_timer:IsOpen() then
		self.gather_timer:Close()
	end
end

function HotSpringWGCtrl:OnVipInfoChange()
	if self.fb_info:IsOpen() then
		self.fb_info:OnVipInfoChange()
	end
end
