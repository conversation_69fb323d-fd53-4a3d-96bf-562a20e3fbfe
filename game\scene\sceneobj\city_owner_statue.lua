StatueType = {
    <PERSON><PERSON><PERSON> = 0, --城主
    ChengZhuFuRen = 1, --城主夫人
    KF3V3GuanJun1 = 2, --跨服3v3冠军雕像1
    KF3V3GuanJun2 = 3, --跨服3v3冠军雕像2
    KF3V3GuanJun3 = 4, --跨服3v3冠军雕像3
    HolyBeastCallWorship = 7, -- 圣天神域膜拜雕像
}

-- 圣天神域雕像id
HolyBeastCallWorshipType = {
    Worship1 = 1,
    Worship2 = 2,
    Worship3 = 3,
    Worship4 = 4,
    Worship5 = 5,
}

--雕像
CityOwnerStatue = CityOwnerStatue or BaseClass(SceneObj)
local STATUE_SCALE = 4
function CityOwnerStatue:__init(vo)
    self.obj_type = SceneObjType.CityOwnerStatue
    self.draw_obj:SetObjType(self.obj_type)
    self:SetObjId(vo.obj_id)
    if vo.statue_type == StatueType.<PERSON><PERSON><PERSON> then
        vo.name = Language.Guide.CityOwnerName[1]
    elseif vo.statue_type == StatueType.ChengZhuFuRen then
        vo.name = Language.Guide.CityOwnerName[2]
    elseif vo.statue_type == StatueType.KF3V3GuanJun1 or vo.statue_type == StatueType.KF3V3GuanJun2 or vo.statue_type == StatueType.KF3V3GuanJun3 then
        vo.name = Language.KuafuPVP.StatueDefaultName
    elseif vo.statue_type == StatueType.HolyBeastCallWorship then
        vo.name = Language.HolyHeavenlyDomain.StatueDefaultName
    end
    --vo.name = vo.is_cz and Language.Guide.CityOwnerName[1] or Language.Guide.CityOwnerName[2]
    self.is_cz = vo.is_cz
    self.load_priority = 5
end

function CityOwnerStatue:__delete()
    self.statue_text = nil
end

function CityOwnerStatue:InitAppearance()
    -- local follow_ui = self:GetFollowUi()
    -- if follow_ui then
    --     follow_ui:SetHpVisiable(false)
    --     follow_ui:ForceSetVisible(true)
    -- end
    self:UpdateStatue()
end

function CityOwnerStatue:UpdateStatue()
    if IsEmptyTable(self.vo) then
        return
    end
    
    local info
    if self.vo.statue_type == StatueType.ChengZhu then
        info = ZhuZaiShenDianWGData.Instance:GetCityOwnerInfo()
    elseif self.vo.statue_type == StatueType.ChengZhuFuRen then
        info = ZhuZaiShenDianWGData.Instance:GetCityOwnerLover()
    elseif self.vo.statue_type == StatueType.KF3V3GuanJun1 then
        info = KF3V3WGData.Instance:GetStatueInfoByIndex(1)
    elseif self.vo.statue_type == StatueType.KF3V3GuanJun2 then
        info = KF3V3WGData.Instance:GetStatueInfoByIndex(2)
    elseif self.vo.statue_type == StatueType.KF3V3GuanJun3 then
        info = KF3V3WGData.Instance:GetStatueInfoByIndex(3)
    elseif self.vo.statue_type == StatueType.HolyBeastCallWorship then
        local worship = self.vo.worship
        local data = HolyHeavenlyDomainWGData.Instance:GetWorshipVoByIndex(worship)
        local role_id = (data.uuid or {}).temp_low or -1

        if role_id > 0 then
            BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
                info = __TableCopy(protocol)
                self.info = info

                if info then
                    self:UpdateAppearance(info)
                    self:UpdateMainModel(info)
                else
                    self:UpdateDefaulMainModel()
                end

                self:UpdateStatueText()
                self:ForceSetFollowUIVisible(false)
            end)
        else
            self:UpdateDefaulMainModel()
            self:UpdateStatueText()
            self:ForceSetFollowUIVisible(false)
        end
        
        return
    end

    self.info = info
    if info then
        self:UpdateAppearance(info)
        self:UpdateMainModel(info)
    else
        self:UpdateDefaulMainModel()
    end

    self:UpdateStatueText()
    self:ForceSetFollowUIVisible(false)
end

function CityOwnerStatue:UpdateStatueText()
    if not self.statue_text then
        self.statue_text = {}
        local async_loader = AllocAsyncLoader(self, "root_loader")

        if async_loader then
            async_loader:SetIsUseObjPool(true)
        async_loader:SetIsInQueueLoad(true)
        async_loader:SetParent(G_SceneObjLayer)
        local ass, bun = ResPath.GetGatherModel("7230100")
        async_loader:Load(ass, bun,
        function (gameobj)
            if IsNil(gameobj) then
                return
            end

            gameobj.transform.parent = self.draw_obj.root_transform
            self.statue_text.obj = U3DObject(gameobj)

            if self.vo.statue_type == StatueType.HolyBeastCallWorship then
                self.statue_text.obj.text_mesh.fontSize = 32

                if self.info and self.info.role_name then
                    self.statue_text.obj.text_mesh.text = self.info.role_name
                else
                    self.statue_text.obj.text_mesh.text = Language.HolyHeavenlyDomain.StatueDefaultName
                end
            end

            if self.vo.statue_type == StatueType.HolyBeastCallWorship then
                local pos_cfg = HolyHeavenlyDomainWGData.Instance:GetWorShipCfgByIndex(self.vo.worship)

                if not IsEmptyTable(pos_cfg) then
                    local scale = Vector3.one * pos_cfg.name_scale
                    local rotation_tab = string.split(pos_cfg.name_rotation,"|")
                    local position_tab = string.split(pos_cfg.name_pos,"|")
                    gameobj.transform.localPosition = Vector3(position_tab[1], position_tab[2], position_tab[3])
                    gameobj.transform.localRotation =  Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
                    gameobj.transform.localScale = scale
                end
            end
        end)
        end
    elseif self.statue_text.obj then
        if self.vo.statue_type == StatueType.HolyBeastCallWorship then
            self.statue_text.obj.text_mesh.fontSize = 32
            if self.info and self.info.role_name then
                self.statue_text.obj.text_mesh.text = self.info.role_name
            else
                self.statue_text.obj.text_mesh.text = Language.HolyHeavenlyDomain.StatueDefaultName
            end
        else
            self.statue_text.obj.text_mesh.text = self.info and self.info.role_name or self.vo.name
        end
    end
end

function CityOwnerStatue:OnModelLoaded(part, obj, obj_class)
    if part == SceneObjPart.Main then
        local scale = Vector3(1,1,1)
        local off_y = Vector3(0, 0, 0)
        if self.info then
            scale = Vector3(STATUE_SCALE,STATUE_SCALE,STATUE_SCALE)
            -- self.draw_obj:SetIsGray(false)
            --self.draw_obj:GetPart(SceneObjPart.Main):SetFloat("idle_speed", 0)
            off_y = Vector3(0, 1.4, 0)
        end

        if self.vo.statue_type == StatueType.HolyBeastCallWorship then
            off_y = Vector3.zero

            if self.info then
                self.draw_obj:SetIsGray(false)
            else
                self.draw_obj:SetIsGray(true)
                scale = Vector3(STATUE_SCALE,STATUE_SCALE,STATUE_SCALE)
            end
        end

        obj.transform.localScale = scale
        local rotation = nil
        if self.vo.statue_type == StatueType.ChengZhu then
            rotation = Quaternion.Euler(0, 0, 0)
        elseif self.vo.statue_type == StatueType.ChengZhuFuRen then
            rotation = Quaternion.Euler(0, 180, 0)
        elseif self.vo.statue_type == StatueType.HolyBeastCallWorship then
            local pos_cfg = HolyHeavenlyDomainWGData.Instance:GetWorShipCfgByIndex(self.vo.worship)

            if pos_cfg and pos_cfg.rotation then
                local rotation_tab = string.split(pos_cfg.rotation,"|")
                rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
            end
        else
            rotation = Quaternion.Euler(0, 0, 0)
        end

        obj.transform.localRotation = rotation
        obj.transform.localPosition = off_y
        if self.info then
            if self.weapon_res_id > 0 then
                self:ChangeModel(SceneObjPart.Weapon, ResPath.GetWeaponModelRes(self.weapon_res_id))
            else
                self:RemoveModel(SceneObjPart.Weapon)
            end

            if self.qilinbi_res_id > 0 then
                self:ChangeModel(SceneObjPart.QiLinBi, ResPath.GetQilinBiModel(self.qilinbi_res_id, self.info.sex))
            else
                self:RemoveModel(SceneObjPart.QiLinBi)
            end

            if self.waist_res_id > 0 then
                self:ChangeModel(SceneObjPart.Waist, ResPath.GetBeltModel(self.waist_res_id))
            else
                self:RemoveModel(SceneObjPart.Waist)
            end

            if self.mask_res_id > 0 then
                self:ChangeModel(SceneObjPart.Mask, ResPath.GetMaskModel(self.mask_res_id))
            else
                self:RemoveModel(SceneObjPart.Mask)
            end
            -- if self.shouhuan_res_id > 0 then
            --     self:ChangeModel(SceneObjPart.ShouHuan, ResPath.GetShouhuanModel(self.shouhuan_res_id))
            -- else
            --     self:RemoveModel(SceneObjPart.ShouHuan)
            -- end
            if self.tail_res_id > 0 then
                self:ChangeModel(SceneObjPart.Tail, ResPath.GetWeibaModel(self.tail_res_id))
            else
                self:RemoveModel(SceneObjPart.Tail)
            end

            if self.wing_res_id > 0 then
                self:ChangeModel(SceneObjPart.Wing, ResPath.GetWingModel(self.wing_res_id))
            else
                self:RemoveModel(SceneObjPart.Wing)
            end
        else
            self:RemoveModel(SceneObjPart.Weapon)
            self:RemoveModel(SceneObjPart.QiLinBi)
            self:RemoveModel(SceneObjPart.Waist)
            self:RemoveModel(SceneObjPart.Mask)
            -- self:RemoveModel(SceneObjPart.ShouHuan)
            self:RemoveModel(SceneObjPart.Tail)
            self:RemoveModel(SceneObjPart.Wing)
        end
    else
        local scale = Vector3(STATUE_SCALE, STATUE_SCALE, STATUE_SCALE)

        if self.vo.statue_type == StatueType.HolyBeastCallWorship then
            local rotation = nil
            local pos_cfg = HolyHeavenlyDomainWGData.Instance:GetWorShipCfgByIndex(self.vo.worship)
            
            if pos_cfg and pos_cfg.rotation then
                local rotation_tab = string.split(pos_cfg.rotation,"|")
                rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
            end

            obj.transform.localRotation = rotation
        end

        obj.transform.localScale = scale
        self.draw_obj:GetPart(part):SetIsGray(true)
        self.draw_obj:GetPart(part):SetFloat("idle_speed", 0)

        if part == SceneObjPart.Wing and obj_class ~= nil then
            obj_class:SetBool(ANIMATOR_PARAM.REST, false)
            obj_class:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
        end
    end
end

function CityOwnerStatue:UpdateDefaulMainModel()
    if self.vo.statue_type == StatueType.ChengZhu then
        self.role_res_id = "7230001"
    elseif self.vo.statue_type == StatueType.ChengZhuFuRen then
        self.role_res_id = "7231001"
    else
        self.role_res_id = "7230001"
    end

    if self.vo.statue_type == StatueType.HolyBeastCallWorship then
        if self.vo.worship == HolyBeastCallWorshipType.Worship1 then
            local role_res_id = RoleWGData.GetJobModelId(0, 1)
            self:ChangeModel(SceneObjPart.Main, ResPath.GetRoleModel(role_res_id))
        elseif self.vo.worship == HolyBeastCallWorshipType.Worship2 then
            local role_res_id = RoleWGData.GetJobModelId(1, 1)
            self:ChangeModel(SceneObjPart.Main, ResPath.GetRoleModel(role_res_id))
        elseif self.vo.worship == HolyBeastCallWorshipType.Worship3 then
            local role_res_id = RoleWGData.GetJobModelId(0, 1)
            self:ChangeModel(SceneObjPart.Main, ResPath.GetRoleModel(role_res_id))
        elseif self.vo.worship == HolyBeastCallWorshipType.Worship4 then
            local role_res_id = RoleWGData.GetJobModelId(1, 1)
            self:ChangeModel(SceneObjPart.Main, ResPath.GetRoleModel(role_res_id))
        elseif self.vo.worship == HolyBeastCallWorshipType.Worship5 then
            local role_res_id = RoleWGData.GetJobModelId(1, 1)
            self:ChangeModel(SceneObjPart.Main, ResPath.GetRoleModel(role_res_id))
        end
        
        return
    end

    self:ChangeModel(SceneObjPart.Main, ResPath.GetGatherModel(self.role_res_id))
end

function CityOwnerStatue:UpdateMainModel(info)
    -- local call_back = function ()
    --     self:UpdataMainModelInfo()
    -- end
    -- self:ChangeModel(SceneObjPart.Main, ResPath.GetRoleModel(RoleWGData.GetJobModelId(info.sex, info.prof)),call_back)
    self:UpdataMainModelInfo()
end

function CityOwnerStatue:UpdataMainModelInfo()
    if self.role_res_id ~= 0 then
        local callback = function()

        end

        local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.info.sex, self.info.prof))
        local body_res, face_res, hair_res = self:GetModelPartRes()
        local extra_model_data = {
            role_body_res = body_res,
            role_face_res = face_res,
            role_hair_res = hair_res,
        }
        self:ChangeMainPartModel(role_bundle, role_name, callback, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data)
    end
end

function CityOwnerStatue:ChangeMainPartModel(bundle, asset, callback, sync_anim_type, draw_model_type, extra_model_data)
    self:ChangeObjWaitSyncAnimType(sync_anim_type)
    self:ChangeModel(SceneObjPart.Main, bundle, asset, callback, draw_model_type, extra_model_data)
end


function CityOwnerStatue:GetModelPartRes()
    --根据选择职业获取默认模型资源id
    local body_res, face_res, hair_res
    if self.info.appearance ~= nil and self.info.appearance.fashion_body ~= 0 then
        face_res, hair_res, body_res = NewAppearanceWGData.Instance:GetRolePartResByResId(self.info.appearance.fashion_body, self.info.sex, self.info.prof)
    else
        body_res = RoleWGData.GetDefRoleModelRes(self.info.sex, self.info.prof, ROLE_SKIN_TYPE.BODY)
        face_res = RoleWGData.GetDefRoleModelRes(self.info.sex, self.info.prof, ROLE_SKIN_TYPE.FACE)
        hair_res = RoleWGData.GetDefRoleModelRes(self.info.sex, self.info.prof, ROLE_SKIN_TYPE.HAIR)
    end
    return body_res, face_res, hair_res
end

function CityOwnerStatue:IsCityOwnerStatue()
    return true
end

function CityOwnerStatue:UpdateAppearance(info)
    local prof = info.prof
    local sex = info.sex
    if nil == prof or nil == sex then
        return
    end

    --清空缓存
    self.role_res_id = 0
    self.weapon_res_id = 0
    self.wing_res_id = 0
    self.halo_res_id = 0
    self.zhibao_res_id = 0
    self.mantle_res_id = 0
    self.fazhen_res_id = 0
    self.qilinbi_res_id = 0
    self.waist_res_id = 0
    self.mask_res_id = 0
    self.shouhuan_res_id = 0
    self.tail_res_id = 0
    self.jianzhen_res_id = 0

    local wing_index = 0
    local halo_index = 0
    local zhibao_index = 0
    local mantle_index = 0
    local fazhen_index = 0
    -- 先查找时装的武器和衣服
    local appearance = info.appearance
    if appearance == nil then
        local shizhuang_part_list = info.shizhuang_part_list
        if shizhuang_part_list then
            appearance = {fashion_body = shizhuang_part_list[2].use_index, fashion_wuqi = shizhuang_part_list[1].use_index}
        end
    else
        wing_index = appearance.wing_used_imageid or 0
        halo_index = appearance.halo_used_imageid or 0
        zhibao_index = appearance.zhibao_used_imageid or 0
        mantle_index = appearance.shenyi_used_imageid or 0
        fazhen_index = appearance.fazhen_image_id or 0
    end
    -- print_error('appearance------------------',appearance)
    if appearance ~= nil then
        -- if not ignore_weapon then
        if nil ~= appearance.shenwu_appeid and 0 ~= appearance.shenwu_appeid then
            self.weapon_res_id = RoleWGData.GetFashionWeaponId(info.sex, prof, appearance.shenwu_appeid)
        elseif nil ~= appearance.fashion_wuqi and 0 ~= appearance.fashion_wuqi then
            self.weapon_res_id = RoleWGData.GetFashionWeaponId(info.sex, prof, appearance.fashion_wuqi)
        else
            self.weapon_res_id = RoleWGData.GetJobWeaponId(info.sex, prof)
        end
        -- end
        
        if appearance.fashion_body ~= 0 then
            self.role_res_id = ResPath.GetFashionModelId(prof,appearance.fashion_body)
        end

        if appearance.qilinbi and appearance.qilinbi >= 0 then
            self.qilinbi_res_id = appearance.qilinbi
        end
        if appearance.waist and appearance.waist >= 0 then
            self.waist_res_id = appearance.waist
        end
        if appearance.mask and appearance.mask >= 0 then
            self.mask_res_id = appearance.mask
        end
        if appearance.shouhuan and appearance.shouhuan >= 0 then
            self.shouhuan_res_id = appearance.shouhuan
        end
        if appearance.tail and appearance.tail >= 0 then
            self.tail_res_id = appearance.tail
        end
        if appearance.jianzhen and appearance.jianzhen >= 0 then
            self.jianzhen_res_id = appearance.jianzhen
        end
    end

    -- 查找翅膀
    if wing_index == 0 then
        if info.wing_info then
            wing_index = info.wing_info.used_imageid or 0
        end
    end

    if appearance and appearance.wing_appeid then
        self.wing_res_id = appearance.wing_appeid
    elseif info.wing_appeid then
        self.wing_res_id = info.wing_appeid
    end

    -- 最后查找职业表
    if self.role_res_id == 0 then
        self.role_res_id = RoleWGData.GetJobModelId(sex, prof)
    end

    if self.weapon_res_id == 0 then
        self.weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
    end
end