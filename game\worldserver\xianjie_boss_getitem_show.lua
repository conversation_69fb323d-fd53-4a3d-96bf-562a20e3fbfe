XianJieBossGetItemShow = XianJieBossGetItemShow or BaseClass(SafeBaseView)

function XianJieBossGetItemShow:__init()
	self.view_layer = UiLayer.Pop
    self.view_name = "XianJieBossGetItemShow"
	self:AddViewResource(0, "uis/view/tips/getitem_prefab", "GetItemTip")
	self.item_list = {}
	self.team_list = {}
end

function Xian<PERSON>ieBossGetItemShow:__delete()
end

function XianJieBossGetItemShow:LoadCallBack()
end

function XianJieBossGetItemShow:ReleaseCallBack()
	self.team_list = {}
	for i,v in ipairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = {}
end

function XianJieBossGetItemShow:ShowIndexCallBack()
	if not self.show_num_tips then
		self.show_num_tips = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RunShow, self), 0.5)
    end
end

function XianJieBossGetItemShow:CloseCallBack()
	if self.show_num_tips then
		GlobalTimerQuest:CancelQuest(self.show_num_tips)
		self.show_num_tips = nil
	end
	self.team_list = {}
end

function XianJieBossGetItemShow:RunShow()
	if IsNil(self:GetRootNode()) then
		if self.show_num_tips then
			GlobalTimerQuest:CancelQuest(self.show_num_tips)
			self.show_num_tips = nil
		end
		return
	end

	if #self.team_list <= 0 then
		local has_play = false
		for k,v in pairs(self.item_list) do
			if v.playing_ani then
				has_play = true
				break
			end
		end
		if not has_play then
			if self.show_num_tips then
				GlobalTimerQuest:CancelQuest(self.show_num_tips)
				self.show_num_tips = nil
			end
			self:Close()
		end
	else
		local item
		for i,v in ipairs(self.item_list) do
			if not v.playing_ani then
				item = v
			end
		end

		if not item then
			item = XianJieBossGetItemIcon.New()
			item:SetParntNode(self.node_list.root_obj.rect)
			item:LoadAsset("uis/view/boss_ui_prefab", 'ItemImage', self.node_list.root_obj.transform)
			table.insert(self.item_list, item)
		end

		local item_data = table.remove(self.team_list, 1)
		item:SetData(item_data)
	end
end

function XianJieBossGetItemShow:Show(item_data)
	if not self:IsOpen() then
		self:Open()
    end
	table.insert(self.team_list, item_data)
end


XianJieBossGetItemIcon = XianJieBossGetItemIcon or BaseClass(BaseRender)
function XianJieBossGetItemIcon:__init()
	self.playing_ani = false
end

function XianJieBossGetItemIcon:__delete()
	self.playing_ani = nil
	self.item_id = nil
	self.parent_node_rect = nil
end

function XianJieBossGetItemIcon:ResetCanvasGroup()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	if self.canvas_group then
		self.canvas_group.alpha = 1
		self.canvas_group.interactable = true
		self.canvas_group.blocksRaycasts = true
	end

	self.view:SetActive(false)
	self.playing_ani = false
end

function XianJieBossGetItemIcon:LoadCallBack()
    self.canvas_group = self.view.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
    if self.item_data then
        self:SetData(self.item_data)
    end
end

function XianJieBossGetItemIcon:SetData(item_data)
	self.item_data = item_data
	if not item_data or not self.node_list or not self.view then
		return
	end

	self.pos = {555, 61}
	local btn_node = MainuiWGCtrl.Instance:GetBtnRoleBagView()
	if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
		btn_node = BossWGCtrl.Instance:GetXianjieEquipBtnNode(item_data.item_id)
	end

	if btn_node then
	-- --获取指引按钮的屏幕坐标
		local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
		local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, btn_node.rect.position)
		local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
		self.pos = {local_bullet_start_pos_tbl.x, local_bullet_start_pos_tbl.y}
	end

	local chip_cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByItemId(item_data.item_id)
    if chip_cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
		if item_cfg then
			local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list.item_icon.image:LoadSprite(bundle, asset, function ()
				self.node_list.item_icon.image:SetNativeSize()
			end)
		end

        self.view:SetActive(true)
		self.view.rect.anchoredPosition = Vector2(150, -200)
		self.view.rect.localScale = Vector3(0.2, 0.2, 0.2)
		self.playing_ani = true
		self:PlayAni()
    else
		self:ResetCanvasGroup()
	end
end

function XianJieBossGetItemIcon:PlayAni()
	self.canvas_group.alpha = 1
	self.canvas_group.interactable = false
	self.canvas_group.blocksRaycasts = false
	local move_tween_1 = self.view.rect:DOAnchorPos(Vector2(150, -50), 0.6)
	local move_tween_2 = self.view.rect:DOAnchorPos(Vector2(self.pos[1], self.pos[2]), 1.3)

	local scale_tween_1 = self.view.rect:DOScale(Vector3(0.8, 0.8, 0.8), 0.5)
	local scale_tween_2 = self.view.rect:DOScale(Vector3(0, 0, 0), 0.3)

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	self.sequence = DG.Tweening.DOTween.Sequence()
	self.sequence:Append(move_tween_1)
	self.sequence:Join(scale_tween_1)

	self.sequence:AppendInterval(0.2)
	self.sequence:AppendCallback(function ()
		self.canvas_group:DoAlpha(1, 0.5, 0.5)
	end)

	self.sequence:Append(move_tween_2)
	self.sequence:Append(scale_tween_2)
	self.sequence:OnComplete(function ()
		self.canvas_group.interactable = true
		self.canvas_group.blocksRaycasts = true
		self.view:SetActive(false)
		self.playing_ani = false
        if self.item_data and self.item_data.anim_complete_fun then
			self.item_data.anim_complete_fun()
		end
	end)
end

function XianJieBossGetItemIcon:SetParntNode(node)
	self.parent_node_rect = node
end
