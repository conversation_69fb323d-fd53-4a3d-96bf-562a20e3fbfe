MarryBaoXiaView = MarryBaoXiaView or BaseClass(SafeBaseView)

local OPERATE_BTN_STATE = {
    buy = 1,  -- 购买
    get = 2,  -- 领取契约奖励与每日日奖励
    qiyue = 3, -- 契约奖励
}

function MarryBaoXiaView:__init()
	self:SetMaskBg(true,true)
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_baoxia_view")
end

function MarryBaoXiaView:LoadCallBack()
    self.insurance_alert = Alert.New(nil, nil, nil, nil, false)
    XUI.AddClickEventListener(self.node_list["btn_day_get"], BindTool.Bind1(self.OnClickGetDayReward, self))
    XUI.AddClickEventListener(self.node_list["btn_buytolover"], BindTool.Bind1(self.OnClickBuyToLover, self))
    XUI.AddClickEventListener(self.node_list["btn_loverreward"], BindTool.Bind1(self.OnClickGetLoverDayReward, self))

	if not MarryWGData.Instance.is_remind_red and RoleWGData.Instance:GetDayFirstLoginFlag() then
		RemindManager.Instance:Fire(RemindName.Marry_BaoXia)
		MarryWGData.Instance.is_remind_red = true
	end

	self:SetTongGuanShow()
end

function MarryBaoXiaView:ReleaseCallBack()
    if nil ~= self.baoxia_cell_list then
		for k,v in pairs(self.baoxia_cell_list) do
			v:DeleteMe()
		end
		self.baoxia_cell_list = nil
	end
	if nil ~= self.tongguan_cell_list then
		for k,v in pairs(self.tongguan_cell_list) do
			v:DeleteMe()
		end
		self.tongguan_cell_list = nil
	end

	if nil ~= self.baoxia_role then
	 	self.baoxia_role:DeleteMe()
	 	self.baoxia_role = nil
	end

	if nil ~= self.insurance_alert then
	 	self.insurance_alert:DeleteMe()
	 	self.insurance_alert = nil
	end

    self.set_baoxia_resid = false
	self.lovel_box_state = nil
	self.cur_day = nil --记录进入时候的天数
end

function MarryBaoXiaView:OnFlush()
    -- self:SetBaoXiaRole()
    self:FlushBaoXiaView()
end

function MarryBaoXiaView:SetItemShow(day)
	if nil ~= self.cur_day and self.cur_day == day then
		return
	end
	local reward_list = MarryWGData.Instance:GetBaoXiaRewardInfo(day)
	if nil == self.baoxia_cell_list then
		self.baoxia_cell_list = {}
		for i = 0, 3 do
			self.baoxia_cell_list[i] = ItemCell.New(self.node_list["ph_day_cell"..i])
            local data_index = i % 2
			if reward_list[data_index] then
				self.baoxia_cell_list[i]:SetData(reward_list[data_index])
			end
		end
	end
end

function MarryBaoXiaView:SetTongGuanShow()
	if nil == self.tongguan_cell_list then
		self.tongguan_cell_list = {}
		for i = 0, 1 do
			self.tongguan_cell_list[i] = ItemCell.New(self.node_list["ph_kaitong_cell"..i])
		end
	end
	local bind_gold,bind_gold_id,reward_item = MarryWGData.Instance:GetBaoXiaRewardBindGold()
	self.tongguan_cell_list[0]:SetData({item_id = bind_gold_id,num = bind_gold})
	self.tongguan_cell_list[1]:SetData(reward_item[0])
end

function MarryBaoXiaView:FlushBaoXiaView()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local love_box_bug = MarryWGData.Instance:GetLoveboxBuyRet() -- 自己
	local love_box_lover = MarryWGData.Instance:GetLoveboxBuyLoverRet()  -- 对方
	local buy_fetch = MarryWGData.Instance:GetLoveboxBuyFetchReward()
	local day_fetch = MarryWGData.Instance:GetLoveboxDayFetchReward()
	local day_lover_fetch = MarryWGData.Instance:GetLoveboxLoverDayFetchReward()

	local buy_times = MarryWGData.Instance:GetLoveboxBuyTimeRet()
	local buylover_times = MarryWGData.Instance:GetLoveboxBuyLoverTimeRet()
	local flush_time = MarryWGData.Instance:GetMarryBaoXiaFlushTime()

    if lover_id > 0 then

        self.node_list["rich_buylover_passdays"].tmp.text = Language.Marry.BuyBaoXiaTip
        local color1 = love_box_lover > 0 and "#ffffff" or "#f2e6e6"
        if love_box_lover > 0 then
			local day =  math.ceil(buylover_times/86400) > 30 and 30 or math.ceil(buylover_times/86400)
			self.node_list["rich_buylover_passdays"].tmp.text = ToColorStr(string.format(Language.Marry.BuyLoverPassDays, 31 - day), COLOR3B.L_ORANGE)
            local str = day_lover_fetch == 0 and Language.Marry.MarryBntLq1 or string.format(Language.Marry.TomorrowZeroFlush,flush_time)
			self.node_list["btn_loverreward_text"].tmp.text = str
		else
			self.node_list["rich_buylover_passdays"].tmp.text = Language.Marry.BuyBaoXiaTip
			self.node_list["btn_loverreward_text"].tmp.text = Language.Marry.LoverNoBuy
		end
    else

		self.node_list["rich_buylover_passdays"].tmp.text = Language.Marry.BuyBaoXiaTip
	end

	if love_box_bug > 0 then
		local cur_day = math.ceil(buy_times/86400) > 30 and 30 or math.ceil(buy_times/86400)
		local day_count = 31 - cur_day
		self.node_list["rich_buy_passdays"].tmp.text = string.format(Language.Marry.BuyPassDays, day_count)
		self.node_list["time_bg"]:SetActive(true)
		self:SetItemShow(cur_day - 1)

		-- local single_digit_day = day_count % 10
		-- local ten_digit_day = math.floor(day_count / 10)

		-- local bundel, asset = ResPath.GetJieHunImg(tostring(single_digit_day))
		-- self.node_list["single_digit_day"].image:LoadSprite(bundel, asset)

		self.node_list["init_day_count"]:SetActive(false)
		--self.node_list["shengyu_day_count"]:SetActive(true)
		self.node_list["day_count_text"].tmp.text = day_count
		-- if ten_digit_day > 0 then
		-- 	bundel, asset = ResPath.GetJieHunImg(tostring(ten_digit_day))
		-- 	self.node_list["ten_digit_day"].image:LoadSprite(bundel, asset)
		-- 	self.node_list["ten_digit_day"]:SetActive(true)
		-- else
		-- 	self.node_list["ten_digit_day"]:SetActive(false)
		-- end
	else
		--self.node_list["init_day_count"]:SetActive(true)
		self.node_list["shengyu_day_count"]:SetActive(false)
		self.node_list["time_bg"]:SetActive(false)
		self.node_list["rich_buy_passdays"].tmp.text = ""
		self:SetItemShow(0)
	end

	local flush_time = MarryWGData.Instance:GetMarryBaoXiaFlushTime()
	local str_desc = love_box_lover > 0 and Language.Marry.BaoXiaDes or Language.Marry.BaoXiaDes2
	--local str1 = string.format(Language.Marry.MyBaoXiaHint,flush_time)
	self.node_list["lbl_baoxia_desc"].tmp.text = str_desc

	for i = 0, 1 do
		self.tongguan_cell_list[i]:SetLingQuVisible(buy_fetch == 1)
		self.baoxia_cell_list[i]:SetLingQuVisible(day_fetch == 1)
	end

	for i = 2, 3 do
		self.baoxia_cell_list[i]:SetLingQuVisible(day_lover_fetch == 1)
	end

	if day_fetch == 0 then
		self.node_list["btn_day_get_text"].tmp.text = Language.Marry.LingQu
	else
		self.node_list["btn_day_get_text"].tmp.text = string.format(Language.Marry.TomorrowZeroFlush,flush_time)
	end

	if 1 ~= love_box_bug then
		-- self.node_list["btn_buy_get_text"].tmp.text = Language.Marry.NoBuy
		self.node_list["btn_day_get_text"].tmp.text = Language.Marry.NoBuy
	end

	if love_box_bug == 1 and 30 * 86400 > buy_times then
		if self.lovel_box_state == true then
			self:PlayBaoXiaUiEffect()
			self.lovel_box_state = false
		end
		self.node_list["btn_buytolover_text"].text.text = Language.Common.AlreadyPurchase
	else
		if nil == self.lovel_box_state then
			self.lovel_box_state = true
		end
		local need_gold = MarryWGData.Instance:GetBaoXiaInfo()
		self.node_list["btn_buytolover_text"].text.text = string.format(Language.Marry.GiveBuy, need_gold)
	end
    --local img_ = buy_fetch == 0 and love_box_bug == 1 and "jh_ann" or "jh_ann2"
    --self.node_list["btn_buy_get"].image:LoadSprite(ResPath.GetJieHunImg(img_))
    --local img_1 = day_fetch == 0 and love_box_bug == 1 and "jh_ann" or "jh_ann2"
    --self.node_list["btn_day_get"].image:LoadSprite(ResPath.GetJieHunImg(img_1))
	-- XUI.SetButtonEnabled(self.node_list["btn_buy_get"], buy_fetch == 0 and love_box_bug == 1)
	-- XUI.SetButtonEnabled(self.node_list["btn_day_get"], day_fetch == 0 and love_box_bug == 1)

	-- self.node_list.btn_buy_get_remind:SetActive(buy_fetch == 0 and love_box_bug == 1)
	self.node_list.btn_day_get_remind:SetActive((day_fetch == 0 or buy_fetch == 0) and love_box_bug == 1)
	self.node_list.btn_loverreward_remind:SetActive(day_lover_fetch == 0 and love_box_lover == 1)

	--XUI.SetGraphicGrey(self.node_list["btn_buytolover"],lover_id <= 0 and love_box_bug == 0)
    --XUI.SetButtonEnabled(self.node_list["btn_loverreward"], love_box_lover == 1 and day_lover_fetch == 0)
    --local img_str1 = lover_id <= 0 and love_box_bug == 0
	--XUI.SetButtonEnabled(self.node_list["btn_buytolover"], img_str1)

	-- self.node_list["btn_lingqu_reward_parent"]:SetActive(love_box_bug == 1)
	-- self.node_list["btn_buytolover"]:SetActive(love_box_bug ~= 1)
	-- self.node_list["btn_buytolover_effect"]:SetActive(love_box_bug ~= 1 and lover_id > 0)

    local operate_btn_state = OPERATE_BTN_STATE.get

    -- 对方买了宝匣 我能领取
    if lover_id > 0 and love_box_lover == 1 and day_lover_fetch == 0 then
        operate_btn_state = OPERATE_BTN_STATE.qiyue
    elseif love_box_bug == 1 and (buy_fetch == 0 or day_fetch == 0) then
        -- 我能领取
        operate_btn_state = OPERATE_BTN_STATE.get
    elseif love_box_bug ~= 1 then
        -- 没买锲约
        operate_btn_state = OPERATE_BTN_STATE.buy
    elseif (love_box_bug == 1 and buy_fetch == 1 and day_fetch == 1) and day_lover_fetch == 0 then
        -- 买了契约，领取了， 没领取 契约
        operate_btn_state = OPERATE_BTN_STATE.qiyue
    end

    self.node_list.btn_buytolover:CustomSetActive(operate_btn_state == OPERATE_BTN_STATE.buy)
    self.node_list.btn_day_get:CustomSetActive(operate_btn_state == OPERATE_BTN_STATE.get)
    self.node_list.btn_loverreward:CustomSetActive(operate_btn_state == OPERATE_BTN_STATE.qiyue)

    local img_str = love_box_lover == 1 and day_lover_fetch == 0
    XUI.SetButtonEnabled(self.node_list["btn_loverreward"], img_str)
	XUI.SetButtonEnabled(self.node_list["btn_day_get"], (day_fetch == 0 or buy_fetch == 0) and love_box_bug == 1)
end

-- 领取每日礼包
function MarryBaoXiaView:OnClickGetDayReward()
	-- MarryWGCtrl.Instance:SendLoveboxFetchReward(self.get_everyday.type)
	MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_SELF_DAILY)
    MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_OPEN)
end

-- 领取购买礼包
-- function MarryBaoXiaView:OnClickGetBuyReward()
-- 	-- MarryWGCtrl.Instance:SendLoveboxFetchReward(self.get_bug.type)
-- 	MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_OPEN)
-- end

-- 领取伴侣的礼包
function MarryBaoXiaView:OnClickGetLoverDayReward()
	MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_LOVER_DAILY)
end

-- 购买宝匣
function MarryBaoXiaView:OnClickBuyToLover()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local love_box_bug = MarryWGData.Instance:GetLoveboxBuyRet() -- 自己
	if lover_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoMarryBuyBaoXia)
		return
	elseif love_box_bug > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.AllreadyBuyBaoXia)
		return
	end
	local need_gold = MarryWGData.Instance:GetBaoXiaInfo()
	local str = string.format(Language.Marry.BuyBaoXia, need_gold)
	self.insurance_alert:SetLableString(str)
	self.insurance_alert:SetOkFunc(BindTool.Bind1(self.SetBuyBaoXiaAlert, self))
	self.insurance_alert:Open()
end

function MarryBaoXiaView:SetBuyBaoXiaAlert(num)
	MarryWGCtrl.Instance:SendLoveboxBuyReq()
end

function MarryBaoXiaView:OnClickRequestBuy()
	MarryWGCtrl.Instance:SendLoveboxBuyReq(1)
end

--播放购买成功特效
function MarryBaoXiaView:PlayBaoXiaUiEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_goumai, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["Effect_baoxia"]})
	--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_goumaichenggong)
	--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["Effect_baoxia"].transform)
end
