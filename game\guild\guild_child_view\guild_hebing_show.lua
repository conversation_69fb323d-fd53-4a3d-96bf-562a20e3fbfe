GuildHeBingShow = GuildHeBingShow or BaseClass(SafeBaseView)

-- 初始化战盟列表界面
function GuildHeBingShow:__init()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_hebing_view")
end

function GuildHeBingShow:ReleaseCallBack()
end

function GuildHeBingShow:LoadCallBack()
	local layout_guild_hebing_view = self.node_list.layout_guild_hebing_view
	self.btn_guild_heping_show_yes = layout_guild_hebing_view.btn_guild_heping_show_yes
	self.btn_guild_heping_show_no = layout_guild_hebing_view.btn_guild_heping_show_no
	self.btn_guild_heping_show_yes.button:AddClickListener(BindTool.Bind1(self.OnClickToAgreeMerge, self))
	self.btn_guild_heping_show_no.button:AddClickListener(BindTool.Bind1(self.OnClickClose, self))
	self:Flush()
end

-- 创建列表控件
function GuildHeBingShow:OnFlush(param_t, index)
	local target_guild_id, target_guild_name = GuildWGData.Instance:GetGuildCombineReq()
	if target_guild_id == nil then self:Close() return end
	GuildDataConst.GUILD_IOPEN.FlushGuildListShow = true
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, target_guild_id)
end

function GuildHeBingShow:FlushGuildListShow(guildvo)
	if nil == guildvo then
		return
	end
	print_error(guildvo)
	self.node_list.lbl_xianmeng_name.text.text = guildvo.guild_name
	self.node_list.lbl_mengzhu_name.text.text = guildvo.tuanzhang_name

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local color_level = "853d07"
	local color_capability = "853d07"
	if guildvo.applyfor_need_capability > role_vo.capability then
		color_capability = "853d07"
	end
	if guildvo.applyfor_need_level > role_vo.level then
		color_level = "853d07"
	end

	local text = string.format(Language.Guild.Autopass, color_level, guildvo.applyfor_need_level, color_capability, guildvo.applyfor_need_capability)
	self.node_list.rich_guild_requirement.text.text = text
	local exp_str = ""
	if guildvo.guild_exp == nil or guildvo.guild_max_exp == nil then
		exp_str = "-/-"
	else
		exp_str = string.format(Language.Guild.XX_XX, guildvo.guild_exp, guildvo.guild_max_exp)
	end
	print_error(exp_str)
	self.node_list.rich_build_val.text.text = exp_str
	local member_str = string.format(Language.Guild.XX_XX, guildvo.cur_member_count, guildvo.max_member_count)
	self.node_list.rich_member_val.text.text = member_str
	self.node_list.rich_guild_bulletin.text.text = guildvo.guild_notice

	self.node_list.lbl_list_guild_level.text.text = guildvo.guild_level
end

-- 同意合并仙盟
function GuildHeBingShow:OnClickToAgreeMerge()
	local target_guild_id, target_guild_name = GuildWGData.Instance:GetGuildCombineReq()
	GuildWGCtrl.Instance:SendGuildMergeAck(target_guild_id, 1)
	self:Close()
end

function GuildHeBingShow:OnClickClose()
	local target_guild_id, target_guild_name = GuildWGData.Instance:GetGuildCombineReq()
	GuildWGCtrl.Instance:SendGuildMergeAck(target_guild_id, 0)
	self:Close()
end