﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweenRotation))]
public class TweenRotationEditor : UITweenerEditor {

	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(120f);

		UGUITweenRotation tw = target as UGUITweenRotation;
		GUI.changed = false;

		Vector3 from = EditorGUILayout.Vector3Field("From", tw.from);
		Vector3 to = EditorGUILayout.Vector3Field("To", tw.to);
		var quat = EditorGUILayout.Toggle("Quaternion", tw.quaternionLerp);

		if (GUI.changed)
		{
			UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
			tw.from = from;
			tw.to = to;
			tw.quaternionLerp = quat;
			UGUITweenEditorTools.SetDirty(tw);
		}

		DrawCommonProperties();
	}
}
