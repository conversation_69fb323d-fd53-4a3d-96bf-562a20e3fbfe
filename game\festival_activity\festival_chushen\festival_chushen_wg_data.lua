FestivalChuShenWGData = FestivalChuShenWGData or BaseClass()

function FestivalChuShenWGData:__init()
	if FestivalChuShenWGData.Instance then
		ErrorLog("[FestivalChuShenWGData] Attemp to create a singleton twice !")
	end

	FestivalChuShenWGData.Instance = self
	FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIANCAICHUSHEN_2, {[1] = OPERATION_EVENT_TYPE.LEVEL},
															BindTool.Bind(self.ChunShenOpenLimit, self), BindTool.Bind(self.IsShowChuShenRedPoint, self))
	RemindManager.Instance:Register(RemindName.Festival_ChuShen, BindTool.Bind(self.IsShowChuShenRedPoint, self))


	-- self.chushen_item_data_event = BindTool.Bind1(self.ChuShenItemDataChange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.chushen_item_data_event)

	self.ani_mark = 0
	self.chushen_rank_data_list = {}
	self.chushen_add_list = {}
	self.menu_list = {}
	self.fa_index = 1
	self.unlock_all = 0

	self:LoadConfig()
end

function FestivalChuShenWGData:LoadConfig()
	self.chushen_cfg = ConfigManager.Instance:GetAutoConfig("festival_tiancaichushen_auto")
	self.food_list = ListToMapList(self.chushen_cfg.show_food, "oa_index")
	self.food_menu_list = ListToMapList(self.chushen_cfg.food_menu, "oa_index")
	self.interface = ListToMap(self.chushen_cfg.interface, "oa_index")
	self.food_map_cfg = ListToMap(self.chushen_cfg.show_food, "oa_index", "item_id")
end

function FestivalChuShenWGData:__delete()
	FestivalChuShenWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.Festival_ChuShen)
	self.chushen_rank_data_list = nil
	self.fa_index = nil
	self.unlock_all = nil
	self.menu_list = nil
	self.chushen_add_list = nil
	self.ani_mark = nil

	-- if self.chushen_item_data_event then
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.chushen_item_data_event)
	-- 	self.chushen_item_data_event = nil
	-- end
end

--厨神排行榜
function FestivalChuShenWGData:ChuShenRankData(protocol)
	for k,v in pairs(protocol.rank_list) do
		AvatarManager.Instance:SetAvatarKey(v.uid, v.avatar_key_big, v.avatar_key_small)
	end
	self.chushen_rank_data_list = protocol.rank_list
end

function FestivalChuShenWGData:GetChuShenRankData()
	return self.chushen_rank_data_list or {}
end

function FestivalChuShenWGData:IsOtherRoleCreat(menu_id)
	if IsEmptyTable(self.chushen_rank_data_list) then
		return false
	end

	for k,v in pairs(self.chushen_rank_data_list) do
		if v.menu_id == menu_id then
			return true
		end
	end
	return false
end

--厨神信息
function FestivalChuShenWGData:ChuShenActInfoData(protocol)
	self.fa_index = protocol.fa_index				--当前配置索引
	self.unlock_all = protocol.unlock_all			--全部菜谱的解锁标记 0 == 未解锁 1 == 已解锁未领取 2 == 已解锁已领取
	self.ani_mark = protocol.ani_mark
end

--获取厨神信息
function FestivalChuShenWGData:GetChuShenActInfoData()
	return self.fa_index, self.unlock_all, self.ani_mark 
end

function FestivalChuShenWGData:GetPreviousRedPoint()
	return self.unlock_all == 1, self.unlock_all == 2
end

--天才厨神菜谱信息返回
function FestivalChuShenWGData:ChuShenFoodInfoData(protocol)
	self.menu_list = protocol.menu_list
	self:ClearCurFoodMenuList()
	self:GetCurFoodMenuList()
end

--活动关闭重置所有活动数据
function FestivalChuShenWGData:ResertAllActInfo()
	self.menu_list = {}
	self.fa_index = 1				--当前配置索引
	self.unlock_all = 0			--全部菜谱的解锁标记 0 == 未解锁 1 == 已解锁未领取 2 == 已解锁已领取
	self.ani_mark = 0
	self.chushen_rank_data_list = {}
end

function FestivalChuShenWGData:GetChuShenFoodInfoData()
	return self.menu_list
end

function FestivalChuShenWGData:GetChuShenFoodInfoById(id)
	return self.menu_list[id]
end

function FestivalChuShenWGData:CheckMenuIsUnlock(id)
	local temp_info = self:GetChuShenFoodInfoById(id)
	if IsEmptyTable(temp_info) or nil == id then
		return false,nil
	end

	return temp_info.state == 1 , temp_info
end

function FestivalChuShenWGData:GetActivityCanOpenDay()
	local is_open = false
    local open_day = FestivalActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIANCAICHUSHEN_2)
	for k,v in pairs(self:GetChuShenParamCfg()) do
		if v.start_server_day <= open_day and open_day <= v.end_server_day then
			is_open = true
		end
	end
	
	return is_open
end
--

function FestivalChuShenWGData:ChunShenOpenLimit()
    if not self:GetActivityCanOpenDay() then
        return false
    end

	local cfg = self.chushen_cfg.config_param
	local vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(cfg) do
		if v.oa_index == self.fa_index then
			return vo.level >= v.level_limit, v
		end
	end

	return false, nil
end

function FestivalChuShenWGData:IsShowChuShenRedPoint()
	local is_open = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIANCAICHUSHEN_2)
	if not is_open then
		return 0
	end

	if self.unlock_all == 1 then
		return 1
	end

	local menu_red = self:IsShowFoodMenuRedPoint()
	if menu_red == 1 then
		return 1
	end

	-- local shicai_red = self:IsShowShiCaiRedPoint()
	-- if shicai_red == 1 then
	-- 	return 1
	-- end

	return 0
end

function FestivalChuShenWGData:IsShowFoodMenuRedPoint()
	for k,v in pairs(self.menu_list) do
		if v.state == 1 and v.is_remind == 1 then
			local base_cfg = self:GetOneMenuCfg(v.id)
			if base_cfg and base_cfg.limit_count > v.use_count then
				local is_enough = self:OperateChuShenMenuList(base_cfg)
				if is_enough then
					return 1
				end
			end
		end
	end
	return 0
end

function FestivalChuShenWGData:IsShowShiCaiRedPoint()
	local food_id_cfg, menu_list = FestivalChuShenWGData.Instance:GetMenuListCfg()
	if not IsEmptyTable(food_id_cfg) then
		local count = 0
		local can_cook = false
		for k,v in pairs(food_id_cfg) do
			local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
			if num > 0 then
				count = count + 1
				if count >= 3 then
					can_cook = true
					break
				end
			end
		end

		if can_cook then
			for k,v in pairs(menu_list) do
				local is_open,_ = self:CheckMenuIsUnlock(v.id)
				if is_open then
					local info = self:GetChuShenFoodInfoById(v.id)
					if info and info.state == 1 and info.is_remind == 1 and v.limit_count > info.use_count then
						local is_enough = self:OperateChuShenMenuList(v)
						if is_enough then
							return 1
						end
					end
				else
					local is_enough = self:OperateChuShenMenuList(v)
					if is_enough then
						return 1
					end
				end
			end
		end
	end
	return 0
end

function FestivalChuShenWGData:OperateChuShenMenuList(check_data)
 	local data_list = check_data.consume_item
	local temp_list = {}
 	if #data_list <= 1 then
 		local is_niu_za = self:ChuShenAutoMenu()
 		return is_niu_za or false
 	end
 	
 	for i=1,3 do
 		local num = ItemWGData.Instance:GetItemNumInBagById(data_list[i -1].item_id)
 		temp_list[i] = {}
	 	temp_list[i].item_id = data_list[i -1].item_id

 		if i == 1 then
	 		temp_list[i].num = num >= data_list[0].num and num or 0
	 	elseif i == 2 then
			if data_list[1].item_id == data_list[0].item_id then
				local all_num = data_list[1].num + data_list[0].num
				temp_list[i].num = num >= all_num and num or 0
			else
				temp_list[i].num = num >= data_list[1].num and num or 0
			end

	 	else
	 		if (data_list[2].item_id == data_list[0].item_id and data_list[2].item_id ~= data_list[1].item_id) 
	 			or  (data_list[2].item_id == data_list[1].item_id and data_list[2].item_id ~= data_list[0].item_id)  then
	 			local all_num = data_list[1].num + data_list[0].num
				temp_list[i].num = num >= all_num and num or 0
			elseif data_list[2].item_id == data_list[0].item_id and data_list[2].item_id == data_list[1].item_id then
				local all_num = data_list[1].num + data_list[0].num + data_list[2].num 
				temp_list[i].num = num >= all_num and num or 0
			else
				temp_list[i].num = num >= data_list[0].num and num or 0
			end
	 	end
 	end

 	local num_count = 0
 	for k,v in pairs(temp_list) do
 		if v.num > 0 then
 			num_count = num_count + 1
 		end
 	end

 	return num_count >= 3
 end

--牛杂红点 只要满足1付费2个免费就成立
 function FestivalChuShenWGData:ChuShenAutoMenu()
	local fufei_id_cfg, menu_list = FestivalChuShenWGData.Instance:GetMenuListCfg()
	local fufei_num = 0
	local auto_shicai = {}
	local free_material_num = 0
	for k,v in pairs(fufei_id_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if v.is_special ~= 1 then
			free_material_num = free_material_num + num
		else
			fufei_num = fufei_num + num
		end
	end

	if fufei_num > 1 and free_material_num > 2 then
		return true
	end
	return false
end


function FestivalChuShenWGData:GetMenuListCfg()
	return self.food_list[self.fa_index] or {}, self.food_menu_list[self.fa_index] or {}
end

function FestivalChuShenWGData:GetCurFoodMenuList()
	if self.cur_food_menu_list == nil then
		local menu_list_cfg = self.food_menu_list[self.fa_index] or {}
		self.cur_food_menu_list = {}
		if not IsEmptyTable(menu_list_cfg) then
			for k,v in pairs(menu_list_cfg) do
				local data = {}
				data.cfg = v
				local is_lock,info = self:CheckMenuIsUnlock(v.id)
				data.state = is_lock and 1 or 0
				data.info = info
				table.insert(self.cur_food_menu_list,data)
			end
		end
		if not IsEmptyTable(self.cur_food_menu_list) then
			table.sort(self.cur_food_menu_list,function (a,b)
				if a and b then
					if a.state == b.state then
						if a.cfg and b.cfg and a.cfg.menu_quality and b.cfg.menu_quality then
							return a.cfg.menu_quality > b.cfg.menu_quality
						end
					else
						return a.state > b.state
					end
				else
					return false
				end

			end)
		end
	end
	return self.cur_food_menu_list
end

function FestivalChuShenWGData:ClearCurFoodMenuList()
	self.cur_food_menu_list = nil
end

function FestivalChuShenWGData:GetPartiAsesst(color)
 	local parti_name = BaseCell_Ui_Circle_Effect[color]
 	if parti_name then
 		return ResPath.GetWuPinKuangEffectUi(parti_name)
 	end
end

--获取食谱配置
function FestivalChuShenWGData:GetOneMenuCfg(id)
	local menu_list = self.food_menu_list[self.fa_index]
	if menu_list then
		for k,v in pairs(menu_list) do
			if v.id == id then
				return v
			end
		end
	end
end

--厨神配置other
function FestivalChuShenWGData:GetChuShenOtherCfg()
	return self.interface[self.fa_index]
end

function FestivalChuShenWGData:GetChuShenParamCfg()
	return self.chushen_cfg.config_param or {}
end
--
function FestivalChuShenWGData:SetChuShenSelectData(data_list)
	self.chushen_add_list = data_list
end

function FestivalChuShenWGData:SetPanelAddMark(is_add)
	self.add_flag = is_add
end

function FestivalChuShenWGData:GetPanelAddMark()
	return self.add_flag or false
end

function FestivalChuShenWGData:GetChuShenSelectData()
	local real_num = 0
	for k,v in pairs(self.chushen_add_list) do
		if v then
			real_num = real_num + 1
		end
	end
	return self.chushen_add_list or {}, real_num
end

function FestivalChuShenWGData:GetLastNum(item_id)
	local num = 0
	if self.chushen_add_list.id and self.chushen_add_list.id > 0 then
		return num
	end

	for k,v in pairs(self.chushen_add_list) do
		if v.item_id == item_id then
			num = num +1
		end
	end
	return num
end

function FestivalChuShenWGData:ResultData(protocol)
	self.cook_type = protocol.cook_type
	self.menu_id = protocol.id
	self.result_reward_list = protocol.reware_list
	self.unlock_reware_list = protocol.unlock_reware_list
end 

function FestivalChuShenWGData:GetResultData()
	return self.cook_type, self.menu_id
end

function FestivalChuShenWGData:GetAllUnlockNum()
	local num = 0
	for k,v in pairs(self.menu_list) do
		if v.state == 1 then
			num = num + 1
		end
	end
	return num
end

function FestivalChuShenWGData:ChatHelp(id, role_id)
	if not id and id <= 0 then
		return 
	end

	local is_open = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIANCAICHUSHEN_2)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FestivalChuShen.ChuShenHelActNoOpen)
		return
	end

	local level_limit = self:ChunShenOpenLimit()
	if not level_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FestivalChuShen.ChuShenHelAnwserLevelLimit)
		return
	end

	-- local vo = GameVoManager.Instance:GetMainRoleVo()
	-- if role_id == vo.role_id then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.FestivalChuShen.ChuShenHelAnwserNoActive)
	-- 	return
	-- end

	local temp_info = self:GetChuShenFoodInfoById(id)
	if not IsEmptyTable(temp_info) and temp_info.state == 1 then
		local cfg = self:GetOneMenuCfg(id)
		if cfg and cfg.menu_name then
			local color = ITEM_COLOR[cfg.menu_quality]
			local content = string.format(Language.FestivalChuShen.ChuShenHelAnwser, cfg.menu_quality, cfg.menu_name, cfg.menu_quality, cfg.food_name) 
			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, content, CHAT_CONTENT_TYPE.TEXT, nil, nil, true)
			return
		end
	end

	SysMsgWGCtrl.Instance:ErrorRemind(Language.FestivalChuShen.ChuShenHelAnwserNoActive)
end

function FestivalChuShenWGData:ChunShenChuanWen()
	local level_limit = self:ChunShenOpenLimit()
	return level_limit or false
end

--5个食材监听改变
function FestivalChuShenWGData:ChuShenItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local cfg = (self.food_map_cfg[self.fa_index] or {})[change_item_id]
	if cfg == nil then
		return
	end

	local is_open = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIANCAICHUSHEN_2)
	if not is_open then
		return 
	end

	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2268, "ChuShenItemChange")
	RemindManager.Instance:Fire(RemindName.Festival_ChuShen)
end



--本地保存今天提醒状态（用于remind局部单日提醒）
function FestivalChuShenWGData:ChuShenUnlockRemindMark()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()  
	local name = uid.."FA_ChuShen"
	PlayerPrefsUtil.SetString(name, cur_day)
end

--今日是否提醒过
function FestivalChuShenWGData:ChuShenRemindTodayBtn()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid.."FA_ChuShen"
	local remind_day = PlayerPrefsUtil.GetString(name) or 0
	return tonumber(remind_day) ~= cur_day
end
