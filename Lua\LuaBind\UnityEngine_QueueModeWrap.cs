﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_QueueModeWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>gin<PERSON>num(typeof(UnityEngine.QueueMode));
		<PERSON><PERSON>("CompleteOthers", get_CompleteOthers, null);
		<PERSON><PERSON>("PlayNow", get_PlayNow, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.QueueMode>.Check = CheckType;
		StackTraits<UnityEngine.QueueMode>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.QueueMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.QueueMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CompleteOthers(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.QueueMode.CompleteOthers);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PlayNow(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.QueueMode.PlayNow);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.QueueMode o = (UnityEngine.QueueMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

