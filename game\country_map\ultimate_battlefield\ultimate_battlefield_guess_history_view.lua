UltimateBattlefieldGuessHistoryView = UltimateBattlefieldGuessHistoryView or BaseClass(SafeBaseView)

local ULTIATE_ROUND_TYPE = {
    ROUND_ONE = 0,
    ROUND_TWO = 1,
    ROUND_THREE = 2,
    ROUND_FOUR = 3,
    ROUND_FIVE = 4,
}

--0红1蓝
local ULTIATE_CAMP = {
    CAMP_RED = 0,
    CAMP_BLUE = 1,
}

function UltimateBattlefieldGuessHistoryView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg()
    self.default_index = TabIndex.ultimate_battlefield_guess_history_one
    self.is_safe_area_adapter = true
    local bundle_name = "uis/view/country_map_ui/ultimate_battlefield_prefab"

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
    self:AddViewResource({TabIndex.ultimate_battlefield_guess_history_one, 
                        TabIndex.ultimate_battlefield_guess_history_two,
                        TabIndex.ultimate_battlefield_guess_history_three,
                        TabIndex.ultimate_battlefield_guess_history_four,
                        TabIndex.ultimate_battlefield_guess_history_five}, bundle_name, "layout_ultimate_guess_preview")
    -- self:AddViewResource(TabIndex.ultimate_battlefield_guess_history_two, bundle_name, "layout_ultimate_guess_preview")
    -- self:AddViewResource(TabIndex.ultimate_battlefield_guess_history_three, bundle_name, "layout_ultimate_guess_preview")
    -- self:AddViewResource(TabIndex.ultimate_battlefield_guess_history_four, bundle_name, "layout_ultimate_guess_preview")
    -- self:AddViewResource(TabIndex.ultimate_battlefield_guess_history_five, bundle_name, "layout_ultimate_guess_preview")
    self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function UltimateBattlefieldGuessHistoryView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.UltimateBattlefield.TitleTalentName1

    local bundle, asset = ResPath.GetRawImagesPNG("a2_zjzc_bj_2")
    if self.node_list.RawImage_tongyong then
        self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end

    local table_group = {}
    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if (not rank_info) or rank_info.stage == -1 then
        return
    end

    for i = 0, rank_info.stage do
        table.insert(table_group, string.format(Language.UltimateBattlefield.RoundStr, NumberToChinaNumber(i + 1)))
    end    

    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list,"VerticalTabbarUltimateTalent")
		self.tabbar:Init(table_group, nil, nil, nil, nil)
        -- 标签选择回调绑定
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
	end
end

function UltimateBattlefieldGuessHistoryView:LoadIndexCallBack(index)
    if index ~= 0 then
        self:InitGuessRender()
    end
end

-- 初始化Render
function UltimateBattlefieldGuessHistoryView:InitGuessRender()
    if not self.shower_1v31 then
        self.shower_1v31 = GuessDisplayRender.New(self.node_list.shower_1v31)
    end

    if not self.shower_2v30 then
        self.shower_2v30 = {}
        for i = 1, 2 do
            self.shower_2v30[i] = GuessDisplayRender.New(self.node_list[string.format("shower_2v30_%d", i)])
        end
    end

    if not self.shower_4v28 then
        self.shower_4v28 = {}
        for i = 1, 4 do
            self.shower_4v28[i] = GuessDisplayRender.New(self.node_list[string.format("shower_4v28_%d", i)])
        end
    end

    if not self.attack_status_8v24_grid then
        self.attack_status_8v24_grid = AsyncBaseGrid.New()
        self.attack_status_8v24_grid:SetStartZeroIndex(false)
        self.attack_status_8v24_grid:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.attack_status_8v24_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHisHeadRender})
    end

    if not self.attack_status_16v16_grid then
        self.attack_status_16v16_grid = AsyncBaseGrid.New()
        self.attack_status_16v16_grid:SetStartZeroIndex(false)
        self.attack_status_16v16_grid:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.attack_status_16v16_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHisHeadRender})
    end

    if not self.defense_status_16v16_grid then
        self.defense_status_16v16_grid = AsyncBaseGrid.New()
        self.defense_status_16v16_grid:SetStartZeroIndex(false)
        self.defense_status_16v16_grid:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.defense_status_16v16_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHisHeadRender})
    end

    if not self.defense_status_other_grid then
        self.defense_status_other_grid = AsyncBaseGrid.New()
        self.defense_status_other_grid:SetStartZeroIndex(false)
        self.defense_status_other_grid:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.defense_status_other_grid,
                assetBundle = "uis/view/country_map_ui/ultimate_battlefield_prefab", assetName = "layout_ultimate_guess_item", 
                itemRender = GuessHisHeadRender})
    end
end

-- 释放Render
function UltimateBattlefieldGuessHistoryView:ReleaseGuessRender()
    if self.shower_1v31 then
        self.shower_1v31:DeleteMe()
        self.shower_1v31 = nil
    end

    if self.shower_2v30 and #self.shower_2v30 > 0 then
		for _, cell in ipairs(self.shower_2v30) do
			cell:DeleteMe()
			cell = nil
		end

		self.shower_2v30 = nil
	end

    if self.shower_4v28 and #self.shower_4v28 > 0 then
		for _, cell in ipairs(self.shower_4v28) do
			cell:DeleteMe()
			cell = nil
		end

		self.shower_4v28 = nil
	end

    if self.attack_status_8v24_grid then
        self.attack_status_8v24_grid:DeleteMe()
        self.attack_status_8v24_grid = nil
    end

    if self.attack_status_16v16_grid then
        self.attack_status_16v16_grid:DeleteMe()
        self.attack_status_16v16_grid = nil
    end

    if self.defense_status_16v16_grid then
        self.defense_status_16v16_grid:DeleteMe()
        self.defense_status_16v16_grid = nil
    end

    if self.defense_status_other_grid then
        self.defense_status_other_grid:DeleteMe()
        self.defense_status_other_grid = nil
    end
end

function UltimateBattlefieldGuessHistoryView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self:ReleaseGuessRender()

    self.curr_stage = nil
end

--- 设置数据
function UltimateBattlefieldGuessHistoryView:SetCurrStage(stage)
    self.curr_stage = stage
end

function UltimateBattlefieldGuessHistoryView:OnFlush(param_t, index)
    self:FlushStage(index)

    if index ~= 0 then
        self:FlushStatus()
        self:RefreshTitleMessage()
        self:RefreshGuessRender()
    end
end

function UltimateBattlefieldGuessHistoryView:FlushStage(index)
    if index == TabIndex.ultimate_battlefield_guess_history_one then
        self:SetCurrStage(ULTIATE_ROUND_TYPE.ROUND_ONE)
	elseif index == TabIndex.ultimate_battlefield_guess_history_two then
        self:SetCurrStage(ULTIATE_ROUND_TYPE.ROUND_TWO)
    elseif index == TabIndex.ultimate_battlefield_guess_history_three then
        self:SetCurrStage(ULTIATE_ROUND_TYPE.ROUND_THREE)
    elseif index == TabIndex.ultimate_battlefield_guess_history_four then
        self:SetCurrStage(ULTIATE_ROUND_TYPE.ROUND_FOUR)
    elseif index == TabIndex.ultimate_battlefield_guess_history_five then
        self:SetCurrStage(ULTIATE_ROUND_TYPE.ROUND_FIVE)
    end
end

-- 刷新当前的展示节点
function UltimateBattlefieldGuessHistoryView:FlushStatus()
    if not self.curr_stage then
        return
    end

    self.node_list.attack_status_1v31:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FIVE)
    self.node_list.attack_status_2v30:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FOUR)
    self.node_list.attack_status_4v28:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_THREE)
    self.node_list.attack_status_8v24:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_TWO)
    self.node_list.attack_status_16v16:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE)

    self.node_list.defense_status_16v16:CustomSetActive(self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE)
    self.node_list.defense_status_other:CustomSetActive(self.curr_stage ~= ULTIATE_ROUND_TYPE.ROUND_ONE)
end

-- 刷新头部信息
function UltimateBattlefieldGuessHistoryView:RefreshTitleMessage()
    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if not rank_info then
        return
    end

    if not self.curr_stage then
        return
    end

    local all_person_num = #rank_info.rank_item_list
    local red_guess_num = 0
    local blue_guess_num = 0
    local left_score = 0
    local right_score = 0

    for _, rank_item in ipairs(rank_info.rank_item_list) do
        if rank_item then    --- 检测是否竞猜
            if rank_item.stage_guess_flag then
                local is_guess = rank_item.stage_guess_flag[self.curr_stage] == 1
                if is_guess then
                    local is_red = rank_item.stage_guess_camp_flag[self.curr_stage] == 0
                    if is_red then
                        red_guess_num = red_guess_num + 1
                    else
                        blue_guess_num = blue_guess_num + 1
                    end
                else
                    local is_red = rank_item.history_camp_flag[self.curr_stage] == 0
                    if is_red then
                        red_guess_num = red_guess_num + 1
                    else
                        blue_guess_num = blue_guess_num + 1
                    end
                end
            end

            if rank_item.history_camp_flag and rank_item.history_camp_flag[self.curr_stage] == ULTIATE_CAMP.CAMP_RED then
                left_score = left_score + rank_item.score
            else
                right_score = right_score + rank_item.score
            end
        end
    end

    local left_ratio = red_guess_num / all_person_num
    self.node_list.left_team_number.text.text = self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE and "" or tostring(left_score)
    self.node_list.left_team_ratio.text.text = string.format(Language.UltimateBattlefield.GuessRatio, string.format("%d%%", math.ceil(left_ratio * 100))) 
    self.node_list.left_team_ratio_slider.slider.value = left_ratio
    local right_ratio = blue_guess_num / all_person_num
    self.node_list.right_team_number.text.text = self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE and "" or tostring(right_score)
    self.node_list.right_team_ratio.text.text = string.format(Language.UltimateBattlefield.GuessRatio, string.format("%d%%", math.floor(right_ratio * 100))) 
    self.node_list.right_team_ratio_slider.slider.value = right_ratio
    
    self.node_list.left_guessed:CustomSetActive(false)
    self.node_list.right_guessed:CustomSetActive(false)

    local left_winner = rank_info.stage_win_flag and rank_info.stage_win_flag[self.curr_stage] == ULTIATE_CAMP.CAMP_RED or false
    self.node_list.left_winner:CustomSetActive(left_winner)
    self.node_list.right_winner:CustomSetActive(not left_winner)
    
    local my_rank_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
    if my_rank_info then
        local is_guess = my_rank_info.stage_guess_flag[self.curr_stage] == 1
        self.node_list.attack_guess_btn:CustomSetActive(false)      --- 历史展示不能竞猜
        self.node_list.defense_guess_btn:CustomSetActive(false)     --- 历史展示不能竞猜

        if is_guess then
            local is_red = my_rank_info.stage_guess_camp_flag[self.curr_stage] == 0
            self.node_list.left_guessed:CustomSetActive(is_red and (not left_winner))
            self.node_list.right_guessed:CustomSetActive(not is_red and left_winner)
        end
    end
end

-- 刷新竞猜数据
function UltimateBattlefieldGuessHistoryView:RefreshGuessRender()
    if not self.curr_stage then
        return
    end

    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if not rank_info then
        return
    end

    local attack_data = {}
    local defense_data = {}

    for _, rank_item in ipairs(rank_info.rank_item_list) do
        if rank_item and rank_item.history_camp_flag then    --- 检测是否竞猜
            local show_data = {}
            show_data.stage = self.curr_stage
            show_data.camp = rank_item.history_camp_flag[self.curr_stage]
            show_data.main_data = rank_item
            show_data.score = rank_item.score

            if rank_item.history_camp_flag[self.curr_stage] == ULTIATE_CAMP.CAMP_RED then
                table.insert(attack_data, show_data)
            else
                table.insert(defense_data, show_data)
            end
        end
    end

    table.sort(attack_data, SortTools.KeyUpperSorter("score"))
    table.sort(defense_data, SortTools.KeyUpperSorter("score"))

    if self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_ONE then
        self:RefreshGuessTypeOne(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_TWO then
        self:RefreshGuessTypeTwo(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_THREE then
        self:RefreshGuessTypeThree(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FOUR then
        self:RefreshGuessTypeFour(attack_data, defense_data)
    elseif self.curr_stage == ULTIATE_ROUND_TYPE.ROUND_FIVE then
        self:RefreshGuessTypeFive(attack_data, defense_data)
    end
end

-- 刷新第一回合（16v16）
function UltimateBattlefieldGuessHistoryView:RefreshGuessTypeOne(attack_data, defense_data)
    self.attack_status_16v16_grid:SetDataList(attack_data)
    self.defense_status_16v16_grid:SetDataList(defense_data)
end

-- 刷新第二回合（8v24）
function UltimateBattlefieldGuessHistoryView:RefreshGuessTypeTwo(attack_data, defense_data)
    self.attack_status_8v24_grid:SetDataList(attack_data)
    self.defense_status_other_grid:SetDataList(defense_data)
end

-- 刷新第三回合（4v28）
function UltimateBattlefieldGuessHistoryView:RefreshGuessTypeThree(attack_data, defense_data)
    if self.shower_4v28 and attack_data then
        for i, shower_cell in ipairs(self.shower_4v28) do
            if shower_cell and attack_data[i] and attack_data[i].main_data then
                shower_cell:SetData(attack_data[i].main_data)
            end
        end
    end

    self.defense_status_other_grid:SetDataList(defense_data)
end

-- 刷新第四回合（2v30）
function UltimateBattlefieldGuessHistoryView:RefreshGuessTypeFour(attack_data, defense_data)
    if self.shower_2v30 and attack_data then
        for i, shower_cell in ipairs(self.shower_2v30) do
          if shower_cell and attack_data[i] and attack_data[i].main_data then
                shower_cell:SetData(attack_data[i].main_data)
            end
        end
    end

    self.defense_status_other_grid:SetDataList(defense_data)
end

-- 刷新第五回合（1v31）
function UltimateBattlefieldGuessHistoryView:RefreshGuessTypeFive(attack_data, defense_data)
    if attack_data and attack_data[1] and attack_data[1].main_data then
        self.shower_1v31:SetData(attack_data[1].main_data)
    end

    self.defense_status_other_grid:SetDataList(defense_data)
end


---------------------竞猜头像对象-----------------------
GuessHisHeadRender = GuessHisHeadRender or BaseClass(BaseRender)
function GuessHisHeadRender:LoadCallBack()
    if self.head == nil then
		self.head = BaseHeadCell.New(self.node_list.player_head)
	end
end

function GuessHisHeadRender:__delete()
    if self.head then
        self.head:DeleteMe()
        self.head = nil
    end
end

function GuessHisHeadRender:OnFlush()
    if not self.data then return end

    local main_data = self.data.main_data
    local is_self = UltimateBattlefieldWGData.Instance:CheckIsSelf(main_data.uuid)
    local name_str = main_data.name
    local plat_type = main_data.usid.temp_high
    local server_id = main_data.usid.temp_low
    local usid_str = string.format("[s%s]", server_id)
    local score_str = main_data.score == 0 and "" or main_data.score

    local camp = self.data.camp or 0
    local bg_str = string.format("a2_zjzc_qz_%d", camp + 1)
    local bg_di_str = string.format("a2_zjzc_qzmc_%d", camp + 1)

    if is_self then
        bg_str = string.format("a2_zjzc_qz_%d", 3)
        bg_di_str = string.format("a2_zjzc_qzmc_%d", 3)

        name_str = ToColorStr(name_str, COLOR3B.GREEN)
        usid_str = ToColorStr(usid_str, COLOR3B.GREEN)
        score_str = ToColorStr(score_str, COLOR3B.GREEN)
    end

    self.node_list.player_bg.image:LoadSprite(ResPath.GetCountryUltimateImg(bg_str))
    self.node_list.player_name_di.image:LoadSprite(ResPath.GetCountryUltimateImg(bg_di_str))

	self.head:SetData({role_id = main_data.uuid.temp_low, prof = main_data.prof, sex = main_data.sex})
    self.node_list.player_name.text.text = name_str
    self.node_list.player_server.text.text = usid_str
    self.node_list.player_score.text.text = score_str
end