TransferGodAndDemonsView = TransferGodAndDemonsView or BaseClass(SafeBaseView)

function TransferGodAndDemonsView:__init()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_god_and_demons")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function TransferGodAndDemonsView:__delete()

end

function TransferGodAndDemonsView:ReleaseCallBack()
	
end

function TransferGodAndDemonsView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesJPG("a3_js_zz_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	XUI.AddClickEventListener(self.node_list["btn_type_0"], BindTool.Bind2(self.ClickTypeBtn,self, 0))
	XUI.AddClickEventListener(self.node_list["btn_type_1"], BindTool.Bind2(self.ClickTypeBtn,self, 1))
end

function TransferGodAndDemonsView:OnFlush()
	

end


function TransferGodAndDemonsView:ClickTypeBtn(type)
    TransFerWGCtrl.Instance:OpenSelectTipsView(type)
end
