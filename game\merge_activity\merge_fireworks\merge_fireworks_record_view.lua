MergeFireworksRecord = MergeFireworksRecord or BaseClass(SafeBaseView)

function MergeFireworksRecord:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {vector2 = Vector2(0, 0), sizeDelta = Vector2(850,500)})
    self:AddViewResource(0, "uis/view/merge_activity_ui/fireworks_ui_prefab", "layout_merge_fireworks_record")
	self:SetMaskBg(true, true)
end

function MergeFireworksRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
    self.data_list = nil
end

function MergeFireworksRecord:ShowIndexCallBack()
    MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
    MergeFireworksWGData.Instance:UpdateRecordCount()
end

function MergeFireworksRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.OAFish.RewardListTitle
    self.record_list = AsyncListView.New(MergeFireworksRecordRender, self.node_list["record_list"])
    self.record_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize,self))
end

local LINE_SPACING = 20
function MergeFireworksRecord:ChangeCellSize(data_index)
    local data = self.data_list and self.data_list[data_index + 1] 
    if not data then return 0 end

    local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',data.item_id)
        return 0
    end
    local str = string.format(Language.MergeFireworks.Record, data.role_name, ITEM_COLOR[cfg.color], cfg.name, data.num)

    self.node_list["TestText"].text.text = str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function MergeFireworksRecord:OnFlush()
    local data = MergeFireworksWGData.Instance:GetRecordInfo() or {}
    self.data_list = data
    self.record_list:SetDataList(data, 3)
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data))
end

function MergeFireworksRecord:CloseCallBack()
    MergeFireworksWGData.Instance:UpdateRecordCount()
    ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2246, "all", {[2] = "record"})
end
-------------------------------------------------------------------------------------
MergeFireworksRecordRender = MergeFireworksRecordRender or BaseClass(BaseRender)
function MergeFireworksRecordRender:OnFlush()
    if not self.data then
        return
    end

    --self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.draw_time)
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',self.data.item_id)
        return
    end
    local str = string.format(Language.MergeFireworks.Record, self.data.role_name, ITEM_COLOR[cfg.color], cfg.name, self.data.num)
    self.node_list["info"].text.text = str
end