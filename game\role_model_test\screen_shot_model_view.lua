ScreenShotModelView = ScreenShotModelView or BaseClass(SafeBaseView)

-- 按顺序排好
ScreenShotModelView.MODEL_TYPE = {
    ALL = 1,
    FASHION = 2,
    WING = 3,
    FABAO = 4,
    SHENBING = 5,
    HALO = 6,
    TIANSHEN = 7,
    TIANSHEN_WUQI = 8,
    BOSS = 9,
    NPC = 10,
    MOUNT = 11,
    HUAKUN = 12,
    LINGCHONG = 13,
    BABY = 14,
    IMP = 15,
    WUHUN = 16,
    YUSHOU = 17,
    GATHER = 18,
    TIANHUN_SHENQI = 19,
    UI_OTHER = 20,
    BEISHI = 21,
    EQUIPDROP = 22,
    FOOTLIGHT = 23,
    MAX = 23,
}

ScreenShotModelView.MODEL_TYPE_NAME = {
    [ScreenShotModelView.MODEL_TYPE.ALL] = "全部",
    [ScreenShotModelView.MODEL_TYPE.FASHION] = "时装",
    [ScreenShotModelView.MODEL_TYPE.SHENBING] = "武器",
    [ScreenShotModelView.MODEL_TYPE.WING] = "翅膀",
    [ScreenShotModelView.MODEL_TYPE.FABAO] = "法宝",
    [ScreenShotModelView.MODEL_TYPE.HALO] = "光环",
    [ScreenShotModelView.MODEL_TYPE.TIANSHEN] = "天神",
    [ScreenShotModelView.MODEL_TYPE.TIANSHEN_WUQI] = "天神武器",
    [ScreenShotModelView.MODEL_TYPE.BOSS] = "BOSS",
    [ScreenShotModelView.MODEL_TYPE.NPC] = "NPC",
    [ScreenShotModelView.MODEL_TYPE.MOUNT] = "坐骑",
    [ScreenShotModelView.MODEL_TYPE.LINGCHONG] = "灵宠",
    [ScreenShotModelView.MODEL_TYPE.HUAKUN] = "鲲",
    [ScreenShotModelView.MODEL_TYPE.BABY] = "仙娃",
    [ScreenShotModelView.MODEL_TYPE.IMP] = "守护",
    [ScreenShotModelView.MODEL_TYPE.WUHUN] = "武魂",
    [ScreenShotModelView.MODEL_TYPE.YUSHOU] = "御兽",
    [ScreenShotModelView.MODEL_TYPE.GATHER] = "采集物",
    [ScreenShotModelView.MODEL_TYPE.TIANHUN_SHENQI] = "天魂神器",
    [ScreenShotModelView.MODEL_TYPE.UI_OTHER] = "UI其他",
    [ScreenShotModelView.MODEL_TYPE.BEISHI] = "背饰",
    [ScreenShotModelView.MODEL_TYPE.EQUIPDROP] = "珍惜装备",
    [ScreenShotModelView.MODEL_TYPE.FOOTLIGHT] = "领域",
}

function ScreenShotModelView:__init()
	self.view_cache_time = 0
    self.full_screen = true
    self.view_layer = UiLayer.PopTop
	self:AddViewResource(0, "uis/view/role_model_test_ui_prefab", "layout_screen_shot")
end

function ScreenShotModelView:__delete()
end

function ScreenShotModelView:OpenCallBack()
    ClientCmdWGCtrl.Instance:ShowFPS("off")
end

function ScreenShotModelView:CloseCallBack()
    CancleAllDelayCall(self)
    ClientCmdWGCtrl.Instance:ShowFPS("on")
end

function ScreenShotModelView:ReleaseCallBack()
    if self.display then
        self.display:DeleteMe()
        self.display = nil
    end

    if self.title_list then
        self.title_list:DeleteMe()
        self.title_list = nil
    end

    if self.path_list then
        self.path_list:DeleteMe()
        self.path_list = nil
    end
end

local project_name = "A3"
function ScreenShotModelView:LoadCallBack()
    self.display = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["display"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.XXL,
        can_drag = true,
    }
    
    self.display:SetRenderTexUI3DModel(display_data)
	-- self.display:SetUI3DModel(self.node_list["display"].transform, self.node_list["tigger_listen"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)

    self.title_select_index = 0
    self.title_list = AsyncListView.New(ScreenShotModeTitleRender, self.node_list.title_list)
    self.title_list:SetSelectCallBack(BindTool.Bind(self.OnClickTitleBtn, self))

    self.path_select_index = 0
    self.path_list = AsyncListView.New(ScreenShotModelListRender, self.node_list.btn_list)
    self.path_list:SetSelectCallBack(BindTool.Bind1(self.OnClickPath, self))

    XUI.AddClickEventListener(self.node_list.Close, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_shot_single, BindTool.Bind(self.OnSingleScreenShot, self))
    XUI.AddClickEventListener(self.node_list.btn_shot_auto, BindTool.Bind(self.OnAutoScreenShot, self))
    XUI.AddClickEventListener(self.node_list.btn_stop, BindTool.Bind(self.OnClickStop, self))
    
    self.node_list.savepath_input.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnSavePathInputFieldEndEdit, self))
    self.node_list.savepath_input.input_field.text = "D:/Screenshot/" .. project_name



    self.node_list.pos_y.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnPosYInputFieldEndEdit, self))
    self.node_list.rot_y.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnRotYInputFieldEndEdit, self))
    self.node_list.sacle.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnScaleInputFieldEndEdit, self))
end

function ScreenShotModelView:OnSavePathInputFieldEndEdit()
end

function ScreenShotModelView:OnSingleScreenShot()
    if not self.path_select_data then return end
    local folder_name = self.path_select_data.folder_name
    local file_name = self.path_select_data.res_id

    UITween.FakeHideShow(self.node_list.ui, 0)
    self.node_list.btn_stop:SetActive(false)

    TryDelayCall(self, function ()
        TipWGCtrl.Instance.system_scroll_view:Close()
        
        local tips_root = GameObject.Find("GameRoot/UILayer/TipsSystemCanvas")
        if not IsNil(tips_root) then
            tips_root:SetActive(false)
        end

        self:OnScreenShot(folder_name, file_name)
    end, 1, "OnSingleScreenShotStart")

    TryDelayCall(self, function ()
        UITween.FakeToShow(self.node_list.ui)
        local tips_root = GameObject.Find("GameRoot/UILayer/TipsSystemCanvas")
        if not IsNil(tips_root) then
            tips_root:SetActive(true)
        end
        TipWGCtrl.Instance:ShowSystemMsg("已截屏", 1)

    end, 1.5, "OnSingleScreenShotComplete")
end

function ScreenShotModelView:OnAutoScreenShot()
    if not self.path_select_index or not self.cur_path_max_num then
        return
    end

    if self.path_select_index > self.cur_path_max_num then
        return
    end

    UITween.FakeHideShow(self.node_list.ui, 0)
    self.node_list.btn_stop:SetActive(true)
    TipWGCtrl.Instance.system_scroll_view:Close()
        
    local tips_root = GameObject.Find("GameRoot/UILayer/TipsSystemCanvas")
    if not IsNil(tips_root) then
        tips_root:SetActive(false)
    end

    TryDelayCall(self, function ()
        self:StartAutoScreenShot()
    end, 1, "OnAutoScreenShotStart")
    
end

function ScreenShotModelView:StartAutoScreenShot()
    if not self.path_select_data then
        self:OnAutoScreenShotComplete()
        TipWGCtrl.Instance:ShowSystemMsg("无数据，停止截屏", 1)
        return
    end

    local wait_time = 1
    if self.path_select_data.is_fabao then
        wait_time = 4
    end

    self:OnScreenShot(self.path_select_data.folder_name, self.path_select_data.res_id)
    TryDelayCall(self, function ()
        if self.path_select_index == self.cur_path_max_num then
            self:OnAutoScreenShotComplete()
            TipWGCtrl.Instance:ShowSystemMsg("自动结束，停止截屏", 1)
            return
        end

        if self.path_list then
            self.path_list:JumpToIndex(self.path_select_index + 1, 20)
        end

        TryDelayCall(self, function ()
            self:StartAutoScreenShot()
        end, wait_time, "OnAutoScreenShotJumpComplete")
    end, 0.5, "OnAutoScreenShotJump")
end

function ScreenShotModelView:OnClickStop()
    CancleAllDelayCall(self)
    self:OnAutoScreenShotComplete()
    TipWGCtrl.Instance:ShowSystemMsg("因点击屏幕，停止截屏", 1)
end

function ScreenShotModelView:OnAutoScreenShotComplete()
    self.node_list.btn_stop:SetActive(false)
    UITween.FakeToShow(self.node_list.ui)
    local tips_root = GameObject.Find("GameRoot/UILayer/TipsSystemCanvas")
    if not IsNil(tips_root) then
        tips_root:SetActive(true)
    end
end

function ScreenShotModelView:OnScreenShot(folder_name, file_name)
    local str = self.node_list.savepath_input.input_field.text

	UtilU3d.ScreenshotByPng(function (texture)
        UtilU3d.SaveScreenshot(texture, string.format("%s/%s/%s.png", str, folder_name, file_name), nil, true)
    end)
end

function ScreenShotModelView:OnFlush()
    self.title_list:SetDataList(ScreenShotModelView.MODEL_TYPE_NAME)
end

function ScreenShotModelView:OnClickTitleBtn(cell)
    -- print_error("--类型点击--", cell.index, cell.data)
    if self.title_select_index == cell.index then
        return
    end

    self.path_select_index = 0
    self.title_select_index = cell.index
    local show_list = ScreenShotModelData.Instance:GetModelShowList(self.title_select_index)
    self.cur_path_max_num = #show_list
    self.path_list:SetDataList(show_list)
    self.path_list:SelectIndex(1)
end

function ScreenShotModelView:OnClickPath(cell)
    -- print_error("--path点击--", cell.index, cell.data)
    if self.path_select_index == cell.index then
        return
    end

    self.path_select_index = cell.index
    self.path_select_data = cell.data
    self:FlushModel(cell.data)
    self.node_list.model_res_text.text.text = string.format("%s %s %s", project_name, cell.data.type_name or "", cell.data.res_id)
end

function ScreenShotModelView:FlushModel(data)
    local bundle, asset
    local pos, rot, scale = nil, nil, nil
    if data.pos_y then
        pos = Vector3(0, data.pos_y, 0)
    end

    if data.rot_y then
        rot = Vector3(0, data.rot_y, 0)
    end

    if data.scale then
        scale = Vector3(data.scale, data.scale, data.scale)
    end

    self.display:RemoveAllModel()
    self.display:ClearCustomDisplayTranfromData()
    self.display:SetPositionAndRotation(pos, rot, scale)
    if data.is_show_role then
        local default_face = RoleWGData.Instance:GetSinglePartDefaultID(data.sex, data.prof, ROLE_SKIN_TYPE.FACE)
        local extra_role_model_data = {
            prof = data.prof,
            sex = data.sex,
            d_face_res = default_face,
            no_need_do_anim = true,
        }
        self.display:SetRoleResid(data.res_id, nil, extra_role_model_data)
        self.display:SetWeaponModelFakeRemove()

    elseif data.is_tianshen then
        self.display:SetTianShenModel(data.res_id, data.tianshen_index)

    elseif data.respath_func ~= nil then
        bundle, asset = data.respath_func(data.res_id)
        self.display:SetMainAsset(bundle, asset)

    elseif data.bundle and data.asset then
        self.display:SetMainAsset(data.bundle, data.asset)
    end
    self.bundle = bundle
    self.asset = asset
    self:FlushModelTransform()
end

function ScreenShotModelView:FlushModelTransform()
    local _, setting = self.display:GetModelCameraSetting(self.display.camera_type, self.display.camera_setting_key, self.bundle, self.asset)
	-- self.pos_x = setting.position[1]
	-- self.pos_y = setting.position[2]
	-- self.rota_x = setting.rotation[1]
	-- self.rota_y = setting.rotation[2]
	-- self.rota_z = setting.rotation[3]
	-- self.scale = setting.scale

    self.node_list.pos_y.input_field.text = setting.position[2]
    self.node_list.rot_y.input_field.text = setting.rotation[2]
    self.node_list.sacle.input_field.text = setting.scale
end

function ScreenShotModelView:OnPosYInputFieldEndEdit()
    local model_pos_node = self.display.model_pos_node
    local num = self.node_list.pos_y.input_field.text
    num = tonumber(num)
    if not num then
        self.node_list.pos_y.input_field.text = model_pos_node.localPosition.y
        return
    end

    self.display.model_pos_node.localPosition = Vector3(model_pos_node.localPosition.x, num, model_pos_node.localPosition.z)
end

function ScreenShotModelView:OnRotYInputFieldEndEdit()
    local model_pos_node = self.display.model_pos_node
    local num = self.node_list.rot_y.input_field.text
    num = tonumber(num)
    if not num then
        self.node_list.rot_y.input_field.text = model_pos_node.localRotation.eulerAngles.y
        return
    end

    self.display.model_pos_node.localRotation = Quaternion.Euler(model_pos_node.localRotation.x, num, model_pos_node.localRotation.z)
end

function ScreenShotModelView:OnScaleInputFieldEndEdit()
    local model_pos_node = self.display.model_pos_node
    local num = self.node_list.sacle.input_field.text
    num = tonumber(num)
    if not num then
        self.node_list.sacle.input_field.text = model_pos_node.localScale.x
        return
    end

    self.display.model_pos_node.localScale = Vector3(num, num, num)
end

--===============================================================================================
ScreenShotModeTitleRender = ScreenShotModeTitleRender or BaseClass(BaseRender)
function ScreenShotModeTitleRender:OnFlush()
	self.node_list["select_text"].text.text = self.data
    self.node_list["normal_text"].text.text = self.data
end


function ScreenShotModeTitleRender:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
    self.node_list.normal:SetActive(not is_select)
end



ScreenShotModelListRender = ScreenShotModelListRender or BaseClass(BaseRender)
function ScreenShotModelListRender:OnFlush()
	self.node_list["btn_name"].text.text = self.data.res_id
end


function ScreenShotModelListRender:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
end 