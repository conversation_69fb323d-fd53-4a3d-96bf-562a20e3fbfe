return {
	["other"]={
		{player_relive_time_s=3,is_open=0,scene_id=6601,day_max_get_score_times=25,},},
	["skill_default_table"]={is_need_target=1,index=1,skill_id=252,},
	["monster_info"]={
		{},
		{},
		{},
		{},
		{},},
	["create_pos"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["monster_info_default_table"]={},
	["fence_default_table"]={fence_id=1,x2=217,y2=180,y1=170,x1=40,},
	["activity_open_time"]={
		{},},
	["fence"]={
		{fence_id=0,x2=62,y1=167,},
		{x2=42,y2=36,y1=23,x1=26,},
		{fence_id=2,x2=209,y2=35,y1=20,x1=185,},
		{fence_id=3,y2=184,x1=195,},},
	["skill"]={
		{index=0,skill_id=250,},
		{skill_id=251,},
		{is_need_target=0,index=2,},
		{index=3,skill_id=253,},},
	["create_pos_default_table"]={},
}
