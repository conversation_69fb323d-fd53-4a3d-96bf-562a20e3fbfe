RoleEquipBodyTuPoView = RoleEquipBodyTuPoView or BaseClass(SafeBaseView)

function RoleEquipBodyTuPoView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/euqip_body_ui_prefab", "layout_equip_body_info_view")
end

function RoleEquipBodyTuPoView:LoadCallBack()
    if not self.extra_show_item_list then
        self.extra_show_item_list = AsyncListView.New(ItemCell, self.node_list.extra_show_item_list)
        self.extra_show_item_list:SetStartZeroIndex(true)
    end

    if not self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
        self.reward_item_list:SetStartZeroIndex(true)
    end

    if not self.model_display then
        self.model_display = RoleModel.New()
        local display_data = {
			parent_node = self.node_list["model_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.model_display:SetRenderTexUI3DModel(display_data)
        -- self.model_display:SetUI3DModel(self.node_list["model_display"].transform, self.node_list["model_display"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    
        local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true}	
		-- self.model_display:SetModelScale(Vector3(0.5, 0.5, 0.5))	
		self.model_display:SetModelResInfo(role_vo, special_status_table, nil)
		self.model_display:SetWingResid(0)
    end

    XUI.AddClickEventListener(self.node_list["btn_equip_body"], BindTool.Bind(self.OnClickEquipBodyBtn, self))
end

function RoleEquipBodyTuPoView:SetDataAndOpen(data)
    self.show_data = data

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function RoleEquipBodyTuPoView:CloseCallBack()
    self:CloseAllTween()
end

function RoleEquipBodyTuPoView:ReleaseCallBack()
    if self.extra_show_item_list then
        self.extra_show_item_list:DeleteMe()
        self.extra_show_item_list = nil
    end

    if self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end
end

function RoleEquipBodyTuPoView:OnFlush(param_t, index)
    if IsEmptyTable(self.show_data) then
        return 
    end

    for k,v in pairs(param_t) do
		if k == "has_active_equip_body" then
            self:DoEquipBodyTuPoAnim()
        else
            self:OnFlushView()
        end
    end
end

function RoleEquipBodyTuPoView:OnFlushView()
    self.extra_show_item_list:SetDataList(self.show_data.equip_preview)
    self.reward_item_list:SetDataList(self.show_data.reward_item)
    self.node_list.desc_tip.text.text = self.show_data.txt

    local has_pre_limit = self.show_data.need_active_seq >= 0
    self.node_list.desc_pre_limit:CustomSetActive(has_pre_limit)

    if has_pre_limit then
        local pre_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(self.show_data.need_active_seq)
        self.node_list.desc_pre_limit.text.text = string.format(Language.RoleEquipBody.UnLockEquipBodyDesc[1], pre_cfg.name)
    end

    local has_level_limit = self.show_data.need_level > 0
    self.node_list.desc_level_limit:CustomSetActive(has_level_limit)

    if has_level_limit then
        local role_level = RoleWGData.Instance:GetAttr('level')
        local color = role_level >= self.show_data.need_level and COLOR3B.GREEN or COLOR3B.RED
        -- local level_str = ToColorStr(role_level .. "/" .. self.show_data.need_level , color)
        self.node_list.desc_level_limit.text.text = string.format(Language.RoleEquipBody.UnLockEquipBodyDesc[2], color, role_level, self.show_data.need_level)
    end

    local has_zhuanzhi_limit = self.show_data.need_zhuanzhi > 0
    self.node_list.desc_zhuanzhi_limit:CustomSetActive(has_zhuanzhi_limit)

    if has_zhuanzhi_limit then
        local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
        local color = role_zhuan >= self.show_data.need_zhuanzhi and COLOR3B.GREEN or COLOR3B.RED
        -- local zhuanzhi_str = ToColorStr(role_zhuan .. "/" .. self.show_data.need_zhuanzhi , color)
        self.node_list.desc_zhuanzhi_limit.text.text = string.format(Language.RoleEquipBody.UnLockEquipBodyDesc[3], color, role_zhuan, self.show_data.need_zhuanzhi)
    end

    local tupo_remind = EquipBodyWGData.Instance:IsCanEquipBodyUnLock(self.show_data.seq)
    self.node_list.btn_equip_body_remind:CustomSetActive(tupo_remind)
end

function RoleEquipBodyTuPoView:OnClickEquipBodyBtn()
    local tupo_remind = EquipBodyWGData.Instance:IsCanEquipBodyUnLock(self.show_data.seq)

    if tupo_remind then
        EquipBodyWGCtrl.Instance:SendEquipBodyOperate(EQUIP_BODY_OPERATE_TYPE.BREAK_THROUGH, self.show_data.seq)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.NotAchieveCondition)
    end
end

function RoleEquipBodyTuPoView:DoEquipBodyTuPoAnim()
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_tupo, is_success = true, pos = Vector2(0, 0)})

    -- self:CloseAllTween()

	-- self.move_tween = self.node_list.right_body.rect:DOAnchorPosX(0, 0.3):OnComplete(function ()
    --     TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_tupo, is_success = true, pos = Vector2(0, 0)})
    --     self.scale_tween = self.node_list.right_body.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)

    --     ReDelayCall(self, function ()
    --         self:Close()
    --     end, 1.5, "RoleEquipBodyView_TuPo")
    -- end)
end

function RoleEquipBodyTuPoView:CloseAllTween()
    if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end

    if self.scale_tween then
        self.scale_tween:Kill()
        self.scale_tween = nil
    end
end