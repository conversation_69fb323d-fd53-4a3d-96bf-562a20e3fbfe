CangJinExchangeView = CangJinExchangeView or BaseClass(SafeBaseView)

function CangJinExchangeView:__init()
    self.view_layer = UiLayer.Pop
    self.view_style = ViewStyle.Half

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/cangjin_shop_prefab", "layout_shop_exchange_bg")
    self:AddViewResource(TabIndex.cangjin_exchange_privilege, "uis/view/cangjin_shop_prefab", "layout_exchange_privilege")
    self:AddViewResource(TabIndex.cangjin_exchange_shop, "uis/view/cangjin_shop_prefab", "layout_exchange_limit_shop")
    self:AddViewResource(TabIndex.cangjin_exchange_suit, "uis/view/cangjin_shop_prefab", "layout_exchange_suit")
    self:AddViewResource(TabIndex.cangjin_exchange_exchange, "uis/view/cangjin_shop_prefab", "layout_exchange_exchange")
    -- self:AddViewResource(TabIndex.cangjin_exchange_tequan, "uis/view/cangjin_shop_prefab", "layout_exchange_tequan")
    self:AddViewResource(0, "uis/view/cangjin_shop_prefab", "VerticalTabbar")

    self.default_index = TabIndex.cangjin_exchange_privilege
    self.tab_sub = {}
    self.remind_tab = {
        { RemindName.PrivilegedGuidance },
        nil,
        nil,
        { RemindName.CangJinExchangeLimitShop },
        -- {RemindName.CangJinExchangeTeQuan},
    }
end

function CangJinExchangeView:ReleaseCallBack()
    if nil ~= self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    self:ReleaseCallBackSuit()
    self:ReleaseCallBackShop()
    self:ReleaseCallBackTeQuan()
    self:ReleaseCallBackExchange()
    self:ReleaseCallBackPrivilege()
end

function CangJinExchangeView:OpenCallBack()
    LongXiWGCtrl.Instance:SendLongXiRequest(LONGXI_OPERATE_TYPE.LONGXI_OPERATE_TYPE_ITEM_INFO)
end

function CangJinExchangeView:LoadCallBack()
    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:Init(Language.CangJinShopView.ExchangeTabGrop, self.tab_sub, "uis/view/cangjin_shop_prefab", nil,
            self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
        FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.CangJinExchangeView, self.tabbar)
    end

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(CangJinShopWGData.Instance:GetVirtualItemRechargeScore())
    if item_cfg then
        self.node_list.score_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    XUI.AddClickEventListener(self.node_list.score_icon, BindTool.Bind(self.OnClickScoreIcon, self))
end

function CangJinExchangeView:ShowIndexCallBack(index)
    if index == TabIndex.cangjin_exchange_suit then

    elseif index == TabIndex.cangjin_exchange_tequan then

    elseif index == TabIndex.cangjin_exchange_shop then

    elseif index == TabIndex.cangjin_exchange_privilege then

    elseif index == TabIndex.cangjin_exchange_exchange then
        self:ExchangeShowIndexCallBack()
    end

    if nil ~= self.last_show_index and index ~= self.last_show_index then
        if self.last_show_index == TabIndex.cangjin_exchange_suit then

        elseif self.last_show_index == TabIndex.cangjin_exchange_tequan then

        elseif self.last_show_index == TabIndex.cangjin_exchange_shop then

        elseif self.last_show_index == TabIndex.cangjin_exchange_privilege then
            LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN, false)
            LongXiWGData.Instance:SetAutoUpLevelStateByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL, false)
        elseif self.last_show_index == TabIndex.cangjin_exchange_exchange then

        end
    end

    self.last_show_index = index
end

function CangJinExchangeView:CloseCallBack()

end

function CangJinExchangeView:LoadIndexCallBack(index)
    if index == TabIndex.cangjin_exchange_suit then
        self:LoadIndexCallBackSuit()
    elseif index == TabIndex.cangjin_exchange_tequan then
        self:LoadIndexCallBackTeQuan()
    elseif index == TabIndex.cangjin_exchange_shop then
        self:LoadIndexCallBackShop()
    elseif index == TabIndex.cangjin_exchange_privilege then
        self:LoadIndexCallBackPrivilege()
    elseif index == TabIndex.cangjin_exchange_exchange then
        self:LoadIndexCallBackExchange()
    end
end

function CangJinExchangeView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if k == "all" then
            if index == TabIndex.cangjin_exchange_suit then
                self:OnFlushSuit()
            elseif index == TabIndex.cangjin_exchange_tequan then
                self:OnFlushTeQuan()
            elseif index == TabIndex.cangjin_exchange_shop then
                self:OnFlushShop()
            elseif index == TabIndex.cangjin_exchange_privilege then
                self:OnFlushPrivilege(param_t, index)
            elseif index == TabIndex.cangjin_exchange_exchange then
                self:OnFlushExchange()
            end
        elseif k == "jump_to_privilege" then
            self:OnFlushPrivilege(param_t, index)
        end
    end

    self:OnFlushOtherInfo()
end

function CangJinExchangeView:OnFlushOtherInfo()
    local cur_score = CangJinShopWGData.Instance:GetCurScore()
    self.node_list["cur_score"].text.text = CommonDataManager.ConverExpByThousand(cur_score)
end

function CangJinExchangeView:OnClickScoreIcon()
    local item_id = CangJinShopWGData.Instance:GetVirtualItemRechargeScore()

    if item_id and item_id > 0 then
        TipWGCtrl.Instance:OpenItem({ item_id = item_id })
    end
end
