

local URL_SEND_CHECK_NUM = ""
local TICK_SEND_CHECK_NUM = "send_check_num"
local TICK_TIME = 60
local PRE_TIME_LEY = "phone_bind_pre_time"
--layout_vipgift
function WelfareView:InitPhoneBind()
	self.on_click_time = 0
	self.node_list["go_bind_btn"].button:AddClickListener(BindTool.Bind(self.OnPhoneBind, self))
	self.node_list["get_reward_btn"].button:AddClickListener(BindTool.Bind(self.OnPhoneBindGetReward, self))
	self.node_list["conform_send_btn"].button:AddClickListener(BindTool.Bind(self.OnClickConfirmBind,self))
	self.node_list["request_code"].button:AddClickListener(BindTool.Bind(self.OnClickRequestCode,self))

	self.node_list["input_phone"].input_field.onValueChanged:AddListener(BindTool.Bind(self.OnPhoneInputChange, self))
	self.node_list["input_code"].input_field.onValueChanged:AddListener(BindTool.Bind(self.OnCodeInputChange, self))

	self.bind_phone_type = WelfareWGData.Instance:GetBindPhoneOpenType()

	if self.bind_phone_type == BIND_PHONE_TYPE.SDK_BIND then
		self.node_list["sdk_bg"]:SetActive(true)
		self.node_list["sdk_bind_panel"]:SetActive(true)

		self.node_list["yunying_bg"]:SetActive(false)
		self.node_list["yunying_bind_panel"]:SetActive(false)

		self.node_list["rewared_group"].transform.localPosition = Vector3(250, -50, 0)
	elseif self.bind_phone_type == BIND_PHONE_TYPE.YUN_YING_BIND then
		self.node_list["sdk_bg"]:SetActive(false)
		self.node_list["sdk_bind_panel"]:SetActive(false)

		self.node_list["yunying_bg"]:SetActive(true)
		self.node_list["yunying_bind_panel"]:SetActive(true)

		self.node_list["rewared_group"].transform.localPosition = Vector3(195, -161, 0)
	end

	self.phone_bind_reward_list = nil
	self:RefreshPhoneBind()
	self:PhoneBindShowReward()
	WelfareWGData.Instance:HaveOpenPhoneView()
	RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
end

function WelfareView:OnPhoneBind()
	--请求SDK界面打开
	AgentAdapter.Instance:ShowPhoneBindView()
end


function WelfareView:RefreshPhoneBind()
	local is_binded = WelfareWGData.Instance:IsPhoneBinded()
	--local is_can_get_reward = WelfareWGCtrl.Instance:IsPhoneBindCanGetReward()
	local is_get_reward =  WelfareWGData.Instance:GetIsBindPhoneReward()
	if not self.node_list["img_red_point"] then
		return
	end

	self.node_list["img_red_point"]:SetActive(not is_get_reward and is_binded)
	self.node_list["get_reward_btn"]:SetActive(not is_get_reward and is_binded)
	self.node_list["go_bind_btn"]:SetActive(not is_binded)
	--self.node_list["txt_succ_bind_tip"]:SetActive(is_binded)
	self.node_list["had_ged_reward"]:SetActive(is_binded and is_get_reward)
	self.node_list["had_ged_reward2"]:SetActive(is_binded and is_get_reward)
end

function WelfareView:OnPhoneBindGetReward()
	WelfareWGCtrl.Instance:SendPhoneRewardRequest()
end


function WelfareView:PhoneBindShowReward()
	if not self.phone_bind_reward_list then
		local reward_cfg = WelfareWGData.Instance:GetBindPhoneRewared()
		local list_reward = {}
		local nLen = #reward_cfg

		for i=0, nLen do
			table.insert(list_reward, reward_cfg[i])
		end
		self.phone_bind_reward_list = AsyncListView.New(PhoneBindRewardCell, self.node_list["bp_update_item"])
		self.phone_bind_reward_list:SetDataList(list_reward)
	end

end

function WelfareView:DescoryPhoneBind()
	if self.phone_bind_reward_list then
		self.phone_bind_reward_list:DeleteMe()
		self.phone_bind_reward_list = nil
	end
	self:RemoveCountDownTimer()
end

function WelfareView:OnClickConfirmBind()
	local phone_name = self.node_list["input_phone"].input_field.text
	local s, e = string.find(phone_name, "^(1[3-9][0-9])%d%d%d%d%d%d%d%d$")
	if s == nil or e == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.ShuRuShouJiHaoMa)
		return
	end
	WelfareWGData.Instance:SetBindPhoneNumber(phone_name)
	
	local code = self.node_list["input_code"].input_field.text
	if code == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.ShuRuYanZhenMa)
		return
	end
	if phone_name ~= "" and code ~= "" then
		WelfareWGCtrl.Instance:SendBindPhone(PHONE_REQUEST_TYPE.BIND, phone_name, code)
	end
end

function WelfareView:ChangeButtonEnable()
	local check_max_time = WelfareWGData.Instance:GetVerifyCheckMaxTime()
	if check_max_time <= Status.NowTime then
		WelfareWGData.Instance:SetVerifyCheckMaxTime(Status.NowTime + 59)
	end
	self:FlushCountDownTime()
end

-- 刷新验证码倒计时
function WelfareView:FlushCountDownTime()
	if not self.node_list or not self.node_list["request_code_text"] then return end
	local is_bind_phone = WelfareWGData.Instance:GetBindPhoneState()
	if is_bind_phone == 1 then
		self.node_list["request_code_text"].text.text = Language.Welfare.HuoQuYanZhenMa
		XUI.SetButtonEnabled(self.node_list["request_code"], false)
		return
	end
	local check_max_time = WelfareWGData.Instance:GetVerifyCheckMaxTime()
	if check_max_time > Status.NowTime then
		XUI.SetButtonEnabled(self.node_list["request_code"], false)
		self:RemoveCountDownTimer()
		local count_down_time = check_max_time - Status.NowTime
		self:OnDelayTimeCallBack(0, count_down_time)
		self.count_down = CountDown.Instance:AddCountDown(
			count_down_time,
			1,
			BindTool.Bind(self.OnDelayTimeCallBack, self),
			BindTool.Bind(self.CompleteTimerCallback, self))
	end
end

function WelfareView:OnDelayTimeCallBack(elapse_time, total_time)
	if not self.node_list then
		return
	end
	if total_time > elapse_time then
		self.node_list["request_code_text"].text.text = string.format(Language.Welfare.ChongShi, math.floor(total_time - elapse_time))
	else
		self.node_list["request_code_text"].text.text = Language.Welfare.HuoQuYanZhenMa
	end
end

function WelfareView:CompleteTimerCallback()
	self:RemoveCountDownTimer()
	if not self.node_list then
		return
	end
	self.node_list["request_code_text"].text.text = Language.Welfare.HuoQuYanZhenMa
	XUI.SetButtonEnabled(self.node_list["request_code"], true)
end

function WelfareView:RemoveCountDownTimer()
	if self.count_down ~= nil then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function WelfareView:OnClickRequestCode()
	local phone_name = self.node_list["input_phone"].input_field.text
	local s, e = string.find(phone_name, "^(1[3-9][0-9])%d%d%d%d%d%d%d%d$")
	if s == nil or e == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.ShuRuShouJiHaoMa)
		return
	end

	WelfareWGData.Instance:SetBindPhoneNumber(phone_name)
	if self.on_click_time <= Status.NowTime then
		self.on_click_time = Status.NowTime + 3
		WelfareWGCtrl.Instance:SendBindPhone(PHONE_REQUEST_TYPE.GET_CODE, phone_name)
	end
end

function WelfareView:FlushBindPhone()
	self:FlushCountDownTime()
end


function WelfareView:OnPhoneInputChange()
	
end

function WelfareView:OnCodeInputChange()
	
end


-------------------------------------------------------------------------

PhoneBindRewardCell = PhoneBindRewardCell or BaseClass(BaseRender)
function PhoneBindRewardCell:__init()
	
end

function PhoneBindRewardCell:LoadCallBack()

end

function PhoneBindRewardCell:ReleaseCallBack()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function PhoneBindRewardCell:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function PhoneBindRewardCell:OnFlush()
	if not self.data then
		return
	end 

	self.cell = ItemCell.New(self.node_list["cell"])
	self.cell:SetData(self.data)
end