﻿using System;
using System.Linq;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.UI;

namespace Nirvana
{
	[ExecuteInEditMode]
	[AddComponentMenu("Nirvana/UI/Control/UI3D Display 10086")]
	public sealed class UI3DDisplay10086 : MonoBehaviour, IDragHandler, IEventSystemHandler
	{
		public Transform OffsetRoot
		{
			get
			{
				return this.offsetRoot;
			}
		}

		public Camera DisplayCamera
		{
			get
			{
				return this.displayCamera;
			}
			set
			{
				this.displayCamera = value;
			}
		}

		public void SetDragSpeed(int dragSpeed)
		{
			this.dragSpeed = (float)dragSpeed;
		}

		public void DisplayPerspectiveWithOffset(GameObject displayObject, Vector3 offset, bool highQuality = false)
		{
			this.DisplayInternal(displayObject, offset, 30f, 0.5f, 30f, highQuality);
		}

        public void Display(GameObject displayObject, Vector3 offset = default,
                               float fieldOfView = 30f, float nearClipPlane = 0.5f, float farClipPlane = 30f, bool highQuality = false)
        {
            this.DisplayInternal(displayObject, offset,
								fieldOfView, nearClipPlane, farClipPlane, highQuality);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="displayObject">显示的对象</param>
        /// <param name="offset">显示对象的位置偏移</param>
        /// <param name="fieldOfView">相机的视野角度（仅在透视模式下有效）</param>
        /// <param name="nearClipPlane">相机的近裁剪平面大小</param>
        /// <param name="farClipPlane">相机的远裁剪平面大小</param>
        private void DisplayInternal(GameObject displayObject, Vector3 offset,
									float fieldOfView, float nearClipPlane, float farClipPlane, bool highQuality)
        {
	        
			if (displayObject == null)
			{
				Debug.LogError("Display object cannot be null");
				return;
			}
			
			if (this.displayImage == null)
			{
				Debug.LogError("Display image is not assigned");
				return;
			}
			
			if (this.displayCamera == null)
			{
				Debug.LogError("Display camera is not assigned");
				return;
			}

			UniversalAdditionalCameraData cameraData = this.displayCamera.GetComponent<UniversalAdditionalCameraData>();
			if (cameraData != null)
			{
				cameraData.EnableClipSpaceRemap = enableClipSpaceRemap;
				cameraData.volumeLayerMask = 1 << (highQuality ? LayerMask.NameToLayer("UI3D") : LayerMask.NameToLayer("UI"));
			}
			else
			{
				enableClipSpaceRemap = false;
				// Debug.LogWarning("UniversalAdditionalCameraData component not found on display camera");
			}
			
			this.displayObject = displayObject;
			this.displayCamera.gameObject.SetActive(true);
			RegisterUpdateAction(OnUpdateSnapUICamera);
            
            if (this.displayTexture == null)
			{
				if (this.GetTemporaryTexture(out this.displayTexture))
				{
                    this.displayImage.enabled = true;
                    this.displayImage.texture = this.displayTexture;
                    this.displayCamera.enabled = true;
                    this.displayCamera.targetTexture = this.displayTexture;
                }
                else
				{
					this.displayImage.enabled = false;
					this.displayCamera.enabled = false;
				}
			}

			if (this.displayCameraCtrl == null)
			{
				this.displayCameraCtrl = this.displayCamera.GetOrAddComponent<UI3DDisplayCamera10086>();
			}
			
			if (this.displayCameraCtrl != null)
			{
				this.displayCameraCtrl.DisplayObject = displayObject;
			}

            this.displayCamera.fieldOfView = fieldOfView;
            this.displayCamera.nearClipPlane = nearClipPlane;
            this.displayCamera.farClipPlane = farClipPlane;
            this.displayCamera.name = displayObject.GetInstanceID().ToString();

            Transform displayObjectTransform = displayObject.transform;
            if (displayObjectTransform == null)
            {
                Debug.LogError("Display object transform is null");
                return;
            }
            
            if (this.posNodeRoot != null)
			{
                displayObjectTransform.SetParent(this.posNodeRoot, true);
            }
			else
			{
                displayObjectTransform.SetParent(base.transform, true);
            }
            displayObjectTransform.localPosition = Vector3.zero;
            displayObjectTransform.localRotation = Quaternion.identity;
            displayObjectTransform.localScale = Vector3.one;

            if (this.offsetRoot != null)
			{
                this.offsetRoot.transform.localPosition = Vector3.zero + offset;
                
                Transform parentTransform = this.offsetRoot.transform.parent;
                if (parentTransform != null)
                {
                    Vector3 lossyScale = parentTransform.lossyScale;
                    if (lossyScale.x != 0) 
                    {
                        this.offsetRoot.transform.localScale = new Vector3(1 / lossyScale.x, 1 / lossyScale.x, 1 / lossyScale.x);
                    }
                }
            }
		}

		public void ClearDisplay()
		{
			if (this.displayCameraCtrl != null)
			{
				this.displayCameraCtrl.DisplayObject = null;
			}
			this.displayObject = null;
		}

        public void SetPosNodeLocalPosition(Vector3 localPosition)
        {
            if (this.posNodeRoot != null && localPosition != null)
            {
                this.posNodeLocalPosition = localPosition;
                this.posNodeRoot.transform.localPosition = this.posNodeLocalPosition;
            }
        }

        public void SetPosNodeRotation(Quaternion rotation)
		{
            this.posNodeRotation = rotation;
			if (this.posNodeRoot != null && rotation != null)
			{
				Quaternion quaternion = rotation;
				Vector3 axis = quaternion * Vector3.up;
				quaternion *= Quaternion.AngleAxis(this.dragRotation, axis);
                this.posNodeRoot.transform.localRotation = quaternion;
			}
		}

		public void SetPosNodeLocalScale(Vector3 scale)
		{
			if (this.posNodeRoot != null && scale != null)
			{
                this.posNodeLocalScale = scale;
                this.posNodeRoot.transform.localScale = this.posNodeLocalScale;
			}
		}

		public void OnDrag(PointerEventData eventData)
		{
			if (this.displayObject != null && this.dragSpeed > 0f)
			{
				float x = eventData.delta.x;
				float num = -this.dragSpeed * x * Time.deltaTime;
				this.dragRotation += num;
				Transform transform = this.displayObject.transform;
				Quaternion quaternion = Quaternion.AngleAxis(this.dragRotation, Vector3.up);
				transform.localRotation = quaternion;
			}
		}
        public void ResetRotation()
        {
            this.dragRotation = 0f;
            if (this.displayObject != null)
            {
                Transform transform = this.displayObject.transform;
                Quaternion quaternion = Quaternion.AngleAxis(this.dragRotation, Vector3.up);
                transform.localRotation = quaternion;
            }
        }

        private void Awake()
		{
			if (this.displayImage == null)
			{
				this.displayImage = base.GetComponent<RawImage>();
			}

			if (this.displayImage != null && this.displayObject == null)
			{
				this.displayImage.enabled = false;
			}

			if (this.displayCamera != null && this.displayObject == null)
			{
				this.displayCamera.enabled = false;
				this.displayCameraCtrl = this.displayCamera.GetOrAddComponent<UI3DDisplayCamera10086>();
			}

			var uiCameraObj = GameObject.Find("UICamera");
			if (uiCameraObj != null)
				uiCamera = uiCameraObj.GetComponent<Camera>();
		}

		private void OnEnable()
		{
			if (this.displayObject != null && this.displayTexture == null)
			{
				if (this.displayImage != null)
				{
					if (this.GetTemporaryTexture(out this.displayTexture))
					{
						this.displayImage.texture = this.displayTexture;
						this.displayCamera.targetTexture = this.displayTexture;
						this.displayImage.enabled = true;
						this.displayCamera.enabled = true;
					}
					else
					{
						this.displayCamera.enabled = false;
						this.displayImage.enabled = false;
					}
				}

				if (this.displayCamera != null)
				{
					this.displayCamera.targetTexture = this.displayTexture;
					this.displayCamera.enabled = true;
				}
				
			}
			
			RegisterUpdateAction(OnUpdateSnapUICamera);

			RenderPipelineManager.beginCameraRendering += OnBeginCameraRender;
		}

		private void OnDisable()
		{
			if (this.displayTexture != null)
			{
                this.ReleaseTexture();

				if (this.displayImage != null)
				{
					this.displayImage.texture = null;
					this.displayImage.enabled = false;
				}

				if (this.displayCamera != null)
				{
					this.displayCamera.targetTexture = null;
					this.displayCamera.enabled = false;
				}
			}

			if (uiGlassBlurCtrl != null)
			{
				uiGlassBlurCtrl.enabled = false;
			}
			
			RenderPipelineManager.beginCameraRendering -= OnBeginCameraRender;
		}

		private void OnDestroy()
		{
			if (this.displayTexture != null)
			{
				this.ReleaseTexture();
				
				if (this.displayImage != null)
				{
					this.displayImage.texture = null;
				}
				
				if (this.displayCamera != null)
				{
					this.displayCamera.targetTexture = null;
					this.displayCamera.enabled = false;
				}
			}

			if (this.displayCameraCtrl != null)
			{
				this.displayCameraCtrl.DisplayObject = null;
			}
		}

		private void LateUpdate()
		{
			onUpdate?.Invoke();
			
			if (this.enableClipSpaceRemap && this.displayImage != null && this.displayCamera != null && this.imageOldPos != this.displayImage.rectTransform.position)
			{
				this.ReleaseTexture();
				if(this.GetTemporaryTexture(out this.displayTexture))
				{
					this.displayImage.texture = this.displayTexture;
					this.displayCamera.targetTexture = this.displayTexture;
					this.displayImage.enabled = true;
					this.displayCamera.enabled = true;
				}
				else
				{
					this.displayImage.enabled = false;
					this.displayCamera.enabled = false;
				}
				
			}
		}

        public void SetDisplayTextureResolution(int width = 0, int height = 0)
        {
			if (this.displayImage == null || width == 0 || height == 0)
				return;

			this.resolutionSetup = false;
            this.displayImage.rectTransform.sizeDelta = new Vector2(width, height);

            if (this.displayObject == null && this.displayTexture != null && this.displayImage != null && this.displayCamera != null)
            {
                this.ReleaseTexture();
                if (this.GetTemporaryTexture(out this.displayTexture))
                {
	                this.displayImage.texture = this.displayTexture;
	                this.displayCamera.targetTexture = this.displayTexture;
	                this.displayImage.enabled = true;
	                this.displayCamera.enabled = true;
                }
                else
                {
	                this.displayImage.enabled = false;
	                this.displayCamera.enabled = false;
                }
            }
        }

        // 设置显示纹理的分辨率
        private void SetupDisplayTextureResolution()
		{
			bool flag = this.resolutionSetup || this.displayImage == null;
			if (!flag)
			{
				this.resolutionSetup = true;
				Rect rect = this.displayImage.rectTransform.rect;
				Vector2 scale = this.displayImage.rectTransform.lossyScale;
				if (this.displayImage.canvas != null && this.displayImage.canvas.rootCanvas != null)
				{
                    RectTransform rootCanvasRect = this.displayImage.canvas.rootCanvas.GetComponent<RectTransform>();
                    if (rootCanvasRect != null)
                    {
                        Vector2 rootCanvasScale = rootCanvasRect.lossyScale;
                        this.resolutionX = Mathf.CeilToInt(rect.width * scale.x / rootCanvasScale.x);
                        this.resolutionY = Mathf.CeilToInt(rect.height * scale.y / rootCanvasScale.y);
                    }
                    else
                    {
                        // Debug.LogWarning("Root canvas RectTransform is null");
                        this.resolutionX = Mathf.CeilToInt(rect.width * scale.x);
                        this.resolutionY = Mathf.CeilToInt(rect.height * scale.y);
                    }
                }
                else
                {
                    // Debug.LogWarning("Canvas or root canvas is null, using fallback resolution");
                    this.resolutionX = Mathf.CeilToInt(rect.width * scale.x);
                    this.resolutionY = Mathf.CeilToInt(rect.height * scale.y);
                }
			}
		}

        //修正参数比
        private void SetupDisplayCorrectResolution()
        {
			if(this.displayImage == null || this.displayCamera == null)
			{
				return;
			}

            if (!this.correctresolutionSetup)
            {
                this.correctresolutionSetup = true;

                if(enableClipSpaceRemap)
                {
	                bool isGLESDevice = SystemInfo.graphicsDeviceType == GraphicsDeviceType.OpenGLES2 || SystemInfo.graphicsDeviceType == GraphicsDeviceType.OpenGLES3;
	                
	                Vector2 screenSize = ScreenSize;
	                Vector3[] worldCorners = new Vector3[4];
	                this.displayImage.rectTransform.GetWorldCorners(worldCorners);

	                Camera worldCamera = this.displayImage.canvas != null ? this.displayImage.canvas.worldCamera : null;
	                Vector2 lb_pos = RectTransformUtility.WorldToScreenPoint(worldCamera, worldCorners[0]);
	                Vector2 rt_pos = RectTransformUtility.WorldToScreenPoint(worldCamera, worldCorners[2]);
	                Vector2 lb_clamped = new Vector2(Mathf.Clamp(lb_pos.x, 0, screenSize.x), Mathf.Clamp(lb_pos.y, 0, screenSize.y));
	                Vector2 rt_clamped = new Vector2(Mathf.Clamp(rt_pos.x, 0, screenSize.x), Mathf.Clamp(rt_pos.y, 0, screenSize.y));
	                
	                Vector2 imageSize = new Vector2(rt_pos.x - lb_pos.x, rt_pos.y - lb_pos.y);
	                Vector2 visiableSize = new Vector2(Mathf.Max(Mathf.Abs(rt_clamped.x - lb_clamped.x), 1), Mathf.Max(Mathf.Abs(rt_clamped.y - lb_clamped.y), 1));
	                Vector2 visiableRatio = new Vector2(visiableSize.x / imageSize.x, visiableSize.y / imageSize.y);
	                
	                if (isGLESDevice)
	                {
		                this.displayCamera.aspect = imageSize.x / imageSize.y;
		                clipSpaceRemapParams = new Vector4(
			                1 / visiableRatio.x,
			                (lb_pos.x - lb_clamped.x) / imageSize.x,
			                1 / visiableRatio.y,
			                (lb_pos.y - lb_clamped.y) / imageSize.y);
		                this.displayImage.uvRect = new Rect(
			                new Vector2((lb_pos.x - lb_clamped.x) / imageSize.x / visiableRatio.x, (lb_pos.y - lb_clamped.y) / imageSize.y / visiableRatio.y), 
			                new Vector2(1 / visiableRatio.x, 1 / visiableRatio.y));
	                }
	                else
	                {
		                this.displayCamera.aspect = imageSize.x / imageSize.y;
		                clipSpaceRemapParams = new Vector4(
			                1 / visiableRatio.x,
			                (lb_pos.x - lb_clamped.x) / imageSize.x,
			                1 / visiableRatio.y,
			                (rt_clamped.y - rt_pos.y) / imageSize.y);
		                this.displayImage.uvRect = new Rect(
			                new Vector2((lb_pos.x - lb_clamped.x) / imageSize.x / visiableRatio.x, (lb_pos.y - lb_clamped.y) / imageSize.y / visiableRatio.y), 
			                new Vector2(1 / visiableRatio.x, 1 / visiableRatio.y));
	                }
	                
	                this.resolutionX = Mathf.CeilToInt(visiableSize.x);
	                this.resolutionY = Mathf.CeilToInt(visiableSize.y);

	                this.imageOldPos = this.displayImage.rectTransform.position;
                }
                else
                {
	                /*
					float tex_x = Mathf.Clamp(ScreenSize.x, DesignResolution.x, MaxResolution.x);
					float tex_y = Mathf.Clamp(ScreenSize.y, DesignResolution.y, MaxResolution.y);
					float prop_x = tex_x / DesignResolution.x;
					float prop_y = tex_y / DesignResolution.y;
					float prop_final = Mathf.Max(prop_x, prop_y);
					this.resolutionX = Mathf.CeilToInt(this.resolutionX * prop_final);
					this.resolutionY = Mathf.CeilToInt(this.resolutionY * prop_final);
					*/
                
	                float ratio = Mathf.Clamp(ScreenSize.y, DesignResolution.y, MaxResolution.y) / DesignResolution.y; 
	                this.resolutionX = Mathf.CeilToInt(this.resolutionX * ratio);
	                this.resolutionY = Mathf.CeilToInt(this.resolutionY * ratio);
	                this.displayImage.uvRect = new Rect(0, 0, 1, 1);
                }

            }
        }

        private bool GetTemporaryTexture(out RenderTexture temporary)
		{
			this.SetupDisplayTextureResolution();
            this.SetupDisplayCorrectResolution();
            if (this.resolutionX == 1 || this.resolutionY == 1)
            {
	            temporary = null;
	            return false;
            }
            temporary = RenderTexture.GetTemporary(this.resolutionX, this.resolutionY, 2, GraphicsFormat.R16G16B16A16_SFloat);
			temporary.autoGenerateMips = false;
			temporary.filterMode = FilterMode.Point;
			return true;
		}

		private void ReleaseTexture()
		{
			if (null != this.displayTexture)
			{
				RenderTexture.ReleaseTemporary(this.displayTexture);
				this.displayTexture = null;
            }
			this.resolutionSetup = false;
			this.correctresolutionSetup = false;
		}

		// 在组件属性更改时调用，用于验证和更新组件状态
		private void OnValidate()
		{
			bool flag = this.displayTexture != null && this.displayTexture.width != this.resolutionX && this.displayTexture.height != this.resolutionY;
			if (flag)
			{
				this.ReleaseTexture();
				if (this.GetTemporaryTexture(out this.displayTexture))
				{
					if (this.displayImage != null)
					{
						this.displayImage.texture = this.displayTexture;
						this.displayImage.enabled = true;
					}
					
					if (this.displayCamera != null)
					{
						this.displayCamera.targetTexture = this.displayTexture;
						this.displayCamera.enabled = true;
					}
				}
				else
				{
					if (this.displayCamera != null)
						this.displayCamera.enabled = false;
					if (this.displayImage != null)
						this.displayImage.enabled = false;
				}
			}
		}

		private void OnBeginCameraRender(ScriptableRenderContext context, Camera camera)
		{
			if (camera == this.displayCamera)
			{
				if (enableClipSpaceRemap)
				{
					CommandBuffer cmd = CommandBufferPool.Get();
					cmd.SetGlobalVector("_ClipSpaceRemapParams", clipSpaceRemapParams);
					context.ExecuteCommandBuffer(cmd);
					CommandBufferPool.Release(cmd);
				}
			}
		}
		
		private void OnUpdateSnapUICamera()
		{
			this.backgroundRenderer.enabled = false;
			
			if (this.displayBlurUIBackground)
			{
				SnapUICamera();
			}
			else
			{
				if (this.uiGlassBlurCtrl != null)
					this.uiGlassBlurCtrl.enabled = false;
			}
			
			RemoveUpdateAction(OnUpdateSnapUICamera);
		}
		
		private void SnapUICamera()
		{
			if (uiGlassBlurCtrl != null)
			{
				uiGlassBlurCtrl.SetShotCamera(uiCamera);
				uiGlassBlurCtrl.onRenderTextureSetup += (Texture tex) => this.backgroundRenderer.material.mainTexture = tex;
				uiGlassBlurCtrl.enabled = true;
				displayCamera.gameObject.SetActive(false);
				RegisterUpdateAction(OnPostSnapUICamera);
			}
		}

		private void OnPostSnapUICamera()
		{
			this.displayCamera.gameObject.SetActive(true);
			this.backgroundRenderer.enabled = true;
			RemoveUpdateAction(OnPostSnapUICamera);
		}

		private void RegisterUpdateAction(Action call)
		{
			//防止重复添加
			if (onUpdate == null || !onUpdate.GetInvocationList().Contains(call))
			{
				onUpdate += call;
			}
		}

		private void RemoveUpdateAction(Action call)
		{
			onUpdate -= call;
		}

		[SerializeField]
		[Tooltip("展示的rawImage.")]
        private RawImage displayImage;

		[SerializeField]
		[Tooltip("展示的camera.")]
		private Camera displayCamera;

        [SerializeField]
		[Tooltip("偏移的父节点.")]
		private Transform offsetRoot;

        [SerializeField]
        [Tooltip("展示物的父节点.")]
        private Transform posNodeRoot;
        
        [SerializeField]
        [Tooltip("拖拽速度.")]
        private float dragSpeed = 10f;
        private float dragRotation;
        
        [SerializeField]
        [Tooltip("屏幕裁剪.")]
        private bool enableClipSpaceRemap = false;
        
        [SerializeField]
        [Tooltip("显示模糊UI背板.")]
        public bool displayBlurUIBackground = false;

        [SerializeField] 
        private Renderer backgroundRenderer;
        
        [SerializeField] 
        private UIGlassBlurCtrl uiGlassBlurCtrl;

        private Vector3 posNodeLocalPosition;
        private Quaternion posNodeRotation;
        private Vector3 posNodeLocalScale = Vector3.one;
        
        private UI3DDisplayCamera10086 displayCameraCtrl;
		private RenderTexture displayTexture;
		private GameObject displayObject;
		
		private int resolutionX = 512;
		private int resolutionY = 512;
		private bool resolutionSetup = false;
        private bool correctresolutionSetup = false;
        private Vector4 clipSpaceRemapParams;
        private Vector3 imageOldPos;
        private Action onUpdate;
        public Camera uiCamera;

        private Vector2 ScreenSize
        {
	        get
	        {
		        if (uiCamera != null)
			        return new Vector2(uiCamera.pixelWidth, uiCamera.pixelHeight);
		        else
			        return new Vector2(Screen.width, Screen.height);
	        }
        }
        private static Vector2 DesignResolution = new Vector2(1334, 768f);
		private static Vector2 MaxResolution = new Vector2(1920, 1080f);
	}
}
