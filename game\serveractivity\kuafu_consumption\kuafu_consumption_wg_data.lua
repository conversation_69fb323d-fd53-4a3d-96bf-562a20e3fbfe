
KuafuConsumptionRankWGData = KuafuConsumptionRankWGData or BaseClass()

function KuafuConsumptionRankWGData:__init()
	if KuafuConsumptionRankWGData.Instance ~= nil then
		Error<PERSON><PERSON>("[KuafuConsumptionRankWGData] attempt to create singleton twice!")
		return
	end
	KuafuConsumptionRankWGData.Instance = self

end

function KuafuConsumptionRankWGData:__delete()
	KuafuConsumptionRankWGData.Instance = nil
end

------------跨服充值排行榜--------------------
function KuafuConsumptionRankWGData:SetCrossRAXiaofeiRankAck(protocol)
	self.rank_count = protocol.rank_count or 0
	self.rank_list = protocol.rank_list or {}

	table.sort(self.rank_list, function(a, b)
		return a.total_consume > b.total_consume
	end)
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local server_id = vo.server_id
	local name = vo.name
	for k,v in pairs(self.rank_list) do
		if v.mvp_server_id == server_id and v.mvp_name == name then
			self.rank_num = k
			break
		end
	end
end

function KuafuConsumptionRankWGData:GetSelfRankNum()
	return self.rank_num
end

function KuafuConsumptionRankWGData:GetCrossRAConsumeRankInfo()
	return self.rank_count or 0, self.rank_list or {}
end

----------自己消费值数--------------------
function KuafuConsumptionRankWGData:SetCrossRAChongzhiRankChongzhiInfo(protocol)
	self.total_consume = protocol.total_consume
end

function KuafuConsumptionRankWGData:GetCrossRAXiaofeiRankXiaofeiInfo()
	return self.total_consume or 0
end