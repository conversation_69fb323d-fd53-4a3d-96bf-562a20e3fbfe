---------------
--Boss猎人
---------------
function ServerActivityTabView:BossHunterReleaseCallBack()
	if self.boss_hunter_list then
		self.boss_hunter_list:DeleteMe()
		self.boss_hunter_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("open_boss_hunter")
end

function ServerActivityTabView:BossHunterLoadCallBack()
	self.node_list.bh_version_act_des.text.text = Language.OpenServer.OpenBossHunterDesc
	self.node_list.bh_guild_gongxian.button:AddClickListener(BindTool.Bind(self.BossHunterOnClickOpenRankView, self))
	self.node_list.bh_goto_kill_boss_btn.button:AddClickListener(BindTool.Bind(self.BossHunterOnClickGotoKillBossBtn, self))
	self.boss_hunter_list = AsyncListView.New(ActBossHunter<PERSON>temR<PERSON>, self.node_list.bh_ph_list)
	self:<PERSON><PERSON>unter<PERSON>lushCountDownTime()
end

function ServerActivityTabView:BossHunterShowCallBack()
	ServerActivityWGCtrl.Instance:SendOpenGameBossHunterReq(OGA_BOSS_HUNTER_REQ.OGA_BOSS_HUNTER_REQ_QUERY_GUILD_RANK)
end

function ServerActivityTabView:BossHunterOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" or (k == "act_info" and v.protocol_id == 2730) then
			self:BossHunterRefreshView()
		end
	end
end

function ServerActivityTabView:BossHunterOnClickOpenRankView()
	ServerActivityWGCtrl.Instance:OpenOpenBossHunterRankView()
end

function ServerActivityTabView:BossHunterOnClickGotoKillBossBtn()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.WorldBoss or scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.VIP_BOSS or scene_type == SceneType.DABAO_BOSS then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.CurSceneNoTurn)
		ServerActivityWGCtrl.Instance:CloseOpenServer2View()
		return
	end
	FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, TabIndex.boss_world)
end

function ServerActivityTabView:BossHunterFlushCountDownTime()
	CountDownManager.Instance:RemoveCountDown("open_boss_hunter")
	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.BOSS_LIEREN)
	if count_down_time > 0 then
		self.node_list.bh_version_act_time.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"open_boss_hunter",
			BindTool.Bind(self.BossHunterUpdateCountDown, self),
			BindTool.Bind(self.BossHunterFlushCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.bh_version_act_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.bh_version_act_time.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:BossHunterUpdateCountDown(elapse_time, total_time)
	self.node_list.bh_version_act_time.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

function ServerActivityTabView:BossHunterRefreshView()
	local data_list = ServerActivityWGData.Instance:GetBossHunterInfo()
	if IsEmptyTable(data_list) then
		return
	end

	local act_cfg_list = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	local boss_hunter_cfg_list = act_cfg_list and act_cfg_list.boss_hunter_reward or {}
	local rank_list = data_list.rank_list or {}
	local temp_list = nil
	if #rank_list < #boss_hunter_cfg_list then
		temp_list = {}
		for i=1,#boss_hunter_cfg_list do
			temp_list[i] = rank_list[i] or {}
		end
	end
	self.boss_hunter_list:SetDataList(temp_list or rank_list)

	local name = data_list.role_guild_name == "" and Language.Common.ZanWu or data_list.role_guild_name
	self.node_list.bh_lbl_role_guild.text.text = name
	self.node_list.bh_lbl_role_score.text.text = data_list.role_boss_score 
	local guild_name = data_list.role_tuan_zhang_name
	local fu_name_1 = data_list.role_fu_tuanzhang_name[1]
	local fu_name_2 = data_list.role_fu_tuanzhang_name[2]
	guild_name = guild_name ~= "" and guild_name or Language.OpenServer.XuWeiYiDai
	fu_name_1 = fu_name_1 ~= "" and fu_name_1 or Language.OpenServer.XuWeiYiDai
	fu_name_2 = fu_name_2 ~= "" and fu_name_2 or Language.OpenServer.XuWeiYiDai
	self.node_list.bh_lbl_master_name.text.text = string.format(Language.OpenServer.MengZhu, guild_name)
	self.node_list.bh_lbl_fumeng_name_1.text.text = string.format(Language.OpenServer.FuMengZhu, fu_name_1)
	self.node_list.bh_lbl_fumeng_name_2.text.text = string.format(Language.OpenServer.FuMengZhu, fu_name_2)

	local rank = Language.Common.ZanWu
	for i,v in ipairs(data_list.rank_list) do
		if v.guild_name == data_list.role_guild_name then
			rank = i
		end
	end
	self.node_list.bh_my_guild_rank.text.text = rank
end

--------------------------------ActBossHunterItemRender-----------------------------
ActBossHunterItemRender = ActBossHunterItemRender or BaseClass(BaseRender)

function ActBossHunterItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ActBossHunterItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.ph_cell)
end

function ActBossHunterItemRender:OnFlush()
	local data = self:GetData()
	local index = self:GetIndex()

	self.node_list.lbl_rank.text.text = index
	if index <= 3 then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. index))
	end
	self.node_list.img_rank:SetActive(index <= 3)

	local rank_bind_gold = ServerActivityWGData.Instance:GetBossHunterJiangLi(index)
	if rank_bind_gold > 0 then
		self.item_cell:SetData({item_id = 65534, num = rank_bind_gold, is_bind = 1})
	end
	self.item_cell:SetActive(rank_bind_gold > 0)

	if IsEmptyTable(data) then
		self.node_list.lbl_guild_name.text.text = Language.OpenServer.XuWeiYiDai
		self.node_list.lbl_score.text.text = "0"
		self.node_list.lbl_name.text.text = string.format(Language.OpenServer.MengZhu, Language.OpenServer.XuWeiYiDai)
		self.node_list.lbl_fu_name_1.text.text = string.format(Language.OpenServer.FuMengZhu, Language.OpenServer.XuWeiYiDai)
		self.node_list.lbl_fu_name_2.text.text = string.format(Language.OpenServer.FuMengZhu, Language.OpenServer.XuWeiYiDai)
		return
	end

	self.node_list.lbl_guild_name.text.text = data.guild_name or ""
	self.node_list.lbl_score.text.text = data.boss_score or ""
	self.node_list.lbl_name.text.text = string.format(Language.OpenServer.MengZhu, data.tuan_zhang_name or "")

	local fu_name_1 = data.fu_tuanzhang_name[1] ~= "" and data.fu_tuanzhang_name[1] or Language.Common.ZanWu
	local fu_name_2 = data.fu_tuanzhang_name[2] ~= "" and data.fu_tuanzhang_name[2] or Language.Common.ZanWu
	self.node_list.lbl_fu_name_1.text.text = string.format(Language.OpenServer.FuMengZhu, fu_name_1)
	self.node_list.lbl_fu_name_2.text.text = string.format(Language.OpenServer.FuMengZhu, fu_name_2)
end