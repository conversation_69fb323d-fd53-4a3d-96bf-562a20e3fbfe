AchievementWGData = AchievementWGData or BaseClass()
BossBtnNum = 5

function AchievementWGData:__init()
	if AchievementWGData.Instance ~= nil then
		<PERSON>rror<PERSON>og("[AchievementWGData] attempt to create singleton twice!")
		return
	end
	AchievementWGData.Instance = self

	self.achieve_info_cfg_list = ConfigManager.Instance:GetAutoConfig("achievement_cfg_auto").achieve_info
	self.achieve_info_map = ListToMap(self.achieve_info_cfg_list, "id")
	self.achieve_type_map = ListToMapList(self.achieve_info_cfg_list, "type_sort","client_sort")

	--成就
	self.achievement_len = 0
	self.accordion_tab = {}
	self.total_achievement_tab = {}
	self.achievement_tab = {}
	self.achievement_name = {}
	self:SetAccordionTable()
	self.tip_data = {}
	self.remind_tab = {}
	self.can_get_item_tab = {}

	--成就界面是否可以跳转
	self.achievement_is_skip = true
	self.item_list = {}

	RemindManager.Instance:Register(RemindName.Achievement, BindTool.Bind(self.IsShowAchievementParentRedPoint, self))--成就主界面按钮
	RemindManager.Instance:Register(RemindName.AchievementTotal, BindTool.Bind(self.IsShowAchievementTotalRedPoint, self))--成就总览
	RemindManager.Instance:Register(RemindName.AchievementParentTable, BindTool.Bind(self.IsShowAchievementRedPoint, self))--成就侧边按钮

	RoleWGData.Instance:NotifyAttrChange(BindTool.Bind1(self.RoleLevelChange, self), {"level"})
end

function AchievementWGData:__delete()
	AchievementWGData.Instance = nil
	self.achievement_is_skip = nil
	self.item_list = nil
	RemindManager.Instance:UnRegister(RemindName.Achievement)
	RemindManager.Instance:UnRegister(RemindName.AchievementTotal)
	RemindManager.Instance:UnRegister(RemindName.AchievementParentTable)--成就侧边按钮
end

function AchievementWGData:RoleLevelChange(attr_name,value)
	if attr_name == "level" then
		local tab = self.remind_tab
		if not tab then return end
		local role_level = value
		for i, v in ipairs(self.total_achievement_tab) do
			-- v.has_active = tab[v.id].has_active
			-- v.has_fatch = tab[v.id].has_fatch
			if v.has_active >= 1 and v.has_fatch ~= 1 and role_level >= v.display_level then
				v.remind_tip = 1
			end
		end


		for k,v in ipairs(self.achievement_name)do
			v.active_achievement = 0
		end
		for i1 = 1, #self.achievement_tab do
			for i2 = 1, #self.achievement_tab[i1] do
				for k,v in ipairs(self.achievement_tab[i1][i2])do
					self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip = 0
				end
			end
		end

		for i1 = 1, #self.achievement_tab do
			for i2 = 1, #self.achievement_tab[i1] do
				for i3, v in ipairs(self.achievement_tab[i1][i2]) do
					-- v.has_active = tab[v.id].has_active
					-- v.has_fatch = tab[v.id].has_fatch

					if tab[v.id].has_active ~= 0 and tab[v.id].has_fatch ~= 0 then
						self.achievement_name[i1].active_achievement = v.chengjiu + self.achievement_name[i1].active_achievement
					end
					if v.has_active >= 1 and v.has_fatch ~= 1 and role_level >= v.display_level then
						self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip = 1 + self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip

						local tab = {}
						tab.type_sort = v.type_sort
						tab.client_sort = v.client_sort
						self.can_get_item_tab[v.id] = tab
					end
					if v.has_active == 1 and role_level >= v.display_level and v.already_active == 0  then
						v.already_active = 1
						self:SetAchievementTipData(v)
					end
					if v.has_fatch == 1 and role_level >= v.display_level then
						v.already_fatch = 1
					else
						v.already_fatch = 0
					end
				end
			end
		end
	end
end

-----------------成就-------------------------
function AchievementWGData:SetAllAchievementInfo(protocol)
	self.protocol = protocol
	local tab = {}
	local achieve_has_active_flag = {}
	local achieve_has_fatch_reward_flag = {}

	for i = 1, 128 do
		achieve_has_active_flag = bit:d2b(protocol.achieve_has_active_flag[i], achieve_has_active_flag)
		achieve_has_fatch_reward_flag = bit:d2b(protocol.achieve_has_fatch_reward_flag[i], achieve_has_fatch_reward_flag)

		if 8 * (i - 1) > self.achievement_len then
			break
		end

		for j = 8 * (i - 1), 7 + 8 * (i - 1) do
			tab[j] = {}
			tab[j].has_active = achieve_has_active_flag[25 - 8 * (i - 1) + j]
			tab[j].has_fatch = achieve_has_fatch_reward_flag[25 - 8 * (i - 1) + j]
		end
	end
	self.remind_tab = tab

	local role_level = RoleWGData.Instance:GetAttr('level')
	for i, v in ipairs(self.total_achievement_tab) do
		v.has_active = tab[v.id].has_active
		v.has_fatch = tab[v.id].has_fatch
		if v.has_active >= 1 and v.has_fatch ~= 1 and role_level >= v.display_level then
			v.remind_tip = 1
		end
	end

	for k,v in ipairs(self.achievement_name)do
		v.active_achievement = 0
	end
	for i1 = 1, #self.achievement_tab do
		for i2 = 1, #self.achievement_tab[i1] do
			for k,v in ipairs(self.achievement_tab[i1][i2])do
				self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip = 0
				-- self.accordion_tab[v.type_sort].child[v.client_sort].param3 = 0
			end
		end
	end

	for i1 = 1, #self.achievement_tab do
		for i2 = 1, #self.achievement_tab[i1] do
			for i3, v in ipairs(self.achievement_tab[i1][i2]) do
				v.has_active = tab[v.id].has_active
				v.has_fatch = tab[v.id].has_fatch
				if v.has_active ~= 0 and v.has_fatch ~= 0 and not v.is_add then
					v.is_add = true
					self.accordion_tab[v.type_sort].child[v.client_sort].param3 = 1 + self.accordion_tab[v.type_sort].child[v.client_sort].param3
				end
				if v.progress_show >= 0 then
					v.progress_value = protocol.achieve_progress_value[v.progress_show]
				end
				-- if tab[v.id].has_active > 0 then
				if tab[v.id].has_active ~= 0 and tab[v.id].has_fatch ~= 0 then
					self.achievement_name[i1].active_achievement = v.chengjiu + self.achievement_name[i1].active_achievement
				end
				if v.has_active >= 1 and v.has_fatch ~= 1 and role_level >= v.display_level then
					self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip = 1 + self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip

					local tab = {}
					-- tab.id = v.id
					tab.type_sort = v.type_sort
					tab.client_sort = v.client_sort
					self.can_get_item_tab[v.id] = tab
				end
				if v.has_active == 1 and role_level >= v.display_level then
					v.already_active = 1
				else
					v.already_active = 0
				end
				if v.has_fatch == 1 and role_level >= v.display_level then
					v.already_fatch = 1
				else
					v.already_fatch = 0
				end
			end
		end
	end
	self.spe_progress_list = protocol.spe_progress_list
end

function AchievementWGData:GetSpeProgressValueById(id)
	return self.spe_progress_list and self.spe_progress_list[id]
end

function AchievementWGData:ChangeAchievementInfo(protocol)
	if protocol.item_count <= 0 then return end
		local role_level = RoleWGData.Instance:GetAttr('level')
		for i, v in ipairs(protocol.item_list) do
			local achievement_cfg = self:GetAchieveInfoCfgById(v.achievement_id)
			if achievement_cfg ~= nil and self.achievement_tab[achievement_cfg.type_sort] ~= nil and self.achievement_tab[achievement_cfg.type_sort][achievement_cfg.client_sort] ~= nil then
				local v3 = nil

				for _, child in ipairs(self.achievement_tab[achievement_cfg.type_sort][achievement_cfg.client_sort])do
					if child.id == v.achievement_id then
						v3 = child
						break
					end
				end
				if v3 ~= nil then
					if AchievementWGCtrl.Instance.view:IsOpen() then
						if v3.has_active ~= v.has_active or v3.has_fatch ~= v.has_fetch_reward then
							self.achievement_is_skip = true
						else
							self.achievement_is_skip = false
						end
					end
					v3.has_active = v.has_active
					v3.has_fatch = v.has_fetch_reward
					if v3.has_active ~= 0 and v3.has_fatch ~= 0 and not v3.is_add then
						v3.is_add = true
						self.accordion_tab[v3.type_sort].child[v3.client_sort].param3 = 1 + self.accordion_tab[v3.type_sort].child[v3.client_sort].param3
					end
					if v3.progress_show >= 0 then
						v3.progress_value = v.progress_value
					end
					if (v3.has_fatch >= 1 or v3.has_active >= 1) and self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip > 0 then
						self.is_flush_accordion = true
					end

					if v3.has_active >= 1 and v3.has_fatch ~= 1 and v3.already_active == 0 and role_level >=v3.display_level then
						v3.already_active = 1
						-- self.achievement_name[v3.type_sort].active_achievement = v3.chengjiu + self.achievement_name[v3.type_sort].active_achievement

						--成就完成提示
						self:SetAchievementTipData(v3)

						self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip = 1 + self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip

						local tab = {}
						-- tab.id = v3.id
						tab.type_sort = v3.type_sort
						tab.client_sort = v3.client_sort
						self.can_get_item_tab[v3.id] = tab
					elseif v3.has_active >= 1 and v3.has_fatch >= 1 and v3.already_fatch == 0 and role_level >=v3.display_level then
						v3.already_fatch = 1
						self.achievement_name[v3.type_sort].active_achievement = v3.chengjiu + self.achievement_name[v3.type_sort].active_achievement
						self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip =
						(self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip - 1) < 0 and 0 or (self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip - 1)

						self.can_get_item_tab[v3.id] = nil
					end
					if (v3.has_fatch >= 1 or v3.has_active >= 1) and role_level >=v3.display_level and self.accordion_tab[v3.type_sort].child[v3.client_sort].remind_tip > 0 then
						self.is_flush_accordion = true
					end
				end
			else
				for i2, v2 in ipairs(self.total_achievement_tab) do
					if v.achievement_id == v2.id then
						if AchievementWGCtrl.Instance.view:IsOpen() then
							if v2.has_active ~= v.has_active or v2.has_fatch ~= v.has_fetch_reward then
								self.achievement_is_skip = true
							else
								self.achievement_is_skip = false
							end
						end
						v2.has_active = v.has_active
						v2.has_fatch = v.has_fetch_reward
						if v2.has_active >= 1 and v2.has_fatch ~= 1 and role_level >=v2.display_level then
							v2.remind_tip =	1
						elseif v2.has_active >= 1 and v2.has_fatch >= 1 then
							v2.remind_tip =	0
						end
					end
				end
			end

			if achievement_cfg and achievement_cfg.is_tmp_pro == 1 and self.spe_progress_list then
				self.spe_progress_list[v.achievement_id] = v.progress_value
			end
			if self.remind_tab[v.achievement_id] == nil then
				self.remind_tab[v.achievement_id] = {}
			end
			self.remind_tab[v.achievement_id].has_active = v.has_active
			self.remind_tab[v.achievement_id].has_fatch = v.has_fetch_reward
	end
end

function AchievementWGData:AccordionStructTable(length)
	local achi_tab = {}

	local function create()
		return {
			child = {},
			type_str = nil,
			type_sort = 0
		}
	end

	for i = 1, length do
		local tab = create()
		table.insert(achi_tab, tab)
	end

	return achi_tab
end

--成就分类
function AchievementWGData:SetAccordionTable()
	local cfg = self.achieve_info_cfg_list
	self.sort_len = ConfigManager.Instance:GetAutoConfig("achievement_cfg_auto").other[1].max_type
	self.achievement_len = cfg[#cfg].id
	self.accordion_tab = self:AccordionStructTable(self.sort_len - 1)

	local tab
	for i, v in ipairs(cfg) do
		tab = {}
		tab.id = v.id
		tab.type_sort = v.type_sort
		tab.client_sort = v.client_sort
		tab.sub_type_str = v.sub_type_str
		tab.client_desc = v.client_desc
		tab.chengjiu = v.chengjiu
		tab.param1 = v.param1
		tab.progress_show = v.progress_show
		tab.reward_item = v.reward_item
		tab.man_reward_item = v.man_reward_item
		tab.woman_reward_item = v.woman_reward_item
		tab.client_show = v.client_show
		tab.display_level = v.display_level
		tab.bind_gold = v.bind_gold
		tab.sliver_ticket = v.sliver_ticket
		tab.gold = v.gold
		tab.coin = v.coin
		tab.prestige = v.prestige
		tab.is_add = false
		if v.type_sort ~= self.sort_len then
			--目录列表
			if self.accordion_tab[v.type_sort].child[v.client_sort] == nil then
				self.accordion_tab[v.type_sort].child[v.client_sort] = {}
				self.accordion_tab[v.type_sort].child[v.client_sort].client_type_str = v.client_type_str
				self.accordion_tab[v.type_sort].child[v.client_sort].client_sort = v.client_sort
				self.accordion_tab[v.type_sort].child[v.client_sort].remind_tip = 0
				self.accordion_tab[v.type_sort].child[v.client_sort].param3 = 0
				self.accordion_tab[v.type_sort].child[v.client_sort].param4 = v.param4
			end
			if self.accordion_tab[v.type_sort].type_str == nil then
				self.accordion_tab[v.type_sort].type_str = v.type_str
			end
			if self.accordion_tab[v.type_sort].type_sort == 0 then
				self.accordion_tab[v.type_sort].type_sort = v.type_sort
			end
			--成就名字和成就点
			if self.achievement_name[v.type_sort] == nil then
				self.achievement_name[v.type_sort] = {}
				self.achievement_name[v.type_sort].name = v.type_str
				self.achievement_name[v.type_sort].achievement = 0
				self.achievement_name[v.type_sort].active_achievement = 0
			end
			self.achievement_name[v.type_sort].achievement = self.achievement_name[v.type_sort].achievement + v.chengjiu
			--成就表
			if not self.achievement_tab[v.type_sort] then
				self.achievement_tab[v.type_sort] = {}
			end
			if not self.achievement_tab[v.type_sort][v.client_sort] then
				self.achievement_tab[v.type_sort][v.client_sort] = {}
			end
			table.insert(self.achievement_tab[v.type_sort][v.client_sort], tab)
		else
			--总成就表
			tab.remind_tip = 0
			table.insert(self.total_achievement_tab, tab)
		end
	end
end

function AchievementWGData:GetAchieveInfoCfgById(active_id)
	return self.achieve_info_map[active_id]
end

function AchievementWGData:GetAchieveListCfgByIndex(index,small_index)
	return self.achieve_type_map[index] and self.achieve_type_map[index][small_index]
end

--获取成就目录表
function AchievementWGData:GetAccordionTable()
	return self.accordion_tab
end
--成就主界面数据
function AchievementWGData:GetAchievementName()
	return self.achievement_name
end

--做一个总成就提示
function AchievementWGData:GetAllAchievement()
	local name_tab = self:GetAchievementName()
	local total_achievement = 0
	local total_active_achievement = 0
	for i = 1, #name_tab do
		total_achievement = total_achievement + name_tab[i].achievement
		total_active_achievement = total_active_achievement + name_tab[i].active_achievement
	end
	return total_active_achievement
end
--主成就点数列表
function AchievementWGData:GetTotalAchievementTab()
	local total_active_achievement = 0
	for i,v in ipairs(self.achievement_name) do
	 	total_active_achievement = total_active_achievement + v.active_achievement
	end
	for i = 1, #self.total_achievement_tab do
		self.total_achievement_tab[i].progress_value = total_active_achievement
	end
end
--成就列表回调
function AchievementWGData:GetAchievementTable(is_total, type_sort, client_sort)
	if is_total then
		self:GetTotalAchievementTab()
		return self.total_achievement_tab
	else
		local data_list = {}
		local info_list = self.achievement_tab[type_sort][client_sort]
		local cfg_list = self:GetAchieveListCfgByIndex(type_sort,client_sort)
		if not cfg_list then return data_list end

		local role_level = RoleWGData.Instance:GetAttr('level')
		for i,v in ipairs(info_list) do
			if cfg_list[i] and cfg_list[i].display_level <= role_level then
				table.insert(data_list,v)
			end
		end
		return data_list
	end
end

function AchievementWGData:GetIsFlushAccordion()
	return self.is_flush_accordion
end
--刷新Accordion之后将其设置为false
function AchievementWGData:SetIsFlushAccordion()
	self.is_flush_accordion = false
end
--开启等级
function AchievementWGData.GetAchievementOpenLevel()
	local cfg = ConfigManager.Instance:GetAutoConfig("achievement_cfg_auto").other[1]
	return cfg.level
end
--可以领取奖励的成就
function AchievementWGData:GetAchievementCanGetItem()
	return self.can_get_item_tab
end

----------------成就提示
function AchievementWGData:SetAchievementTipData(data)
	table.insert(self.tip_data, data)
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("achievement")
	if is_open then
		AchievementWGCtrl.Instance:OpenTipView(data)
	end
end

function AchievementWGData:GetAchievementTipData()
	return self.tip_data
end

function AchievementWGData:SetTipViewToOpenViewType(type_sort, client_sort, is_tip)
	self.to_view_type_sort = type_sort
	self.to_view_client_sort = client_sort
	self.is_tip_to_view = is_tip
end

function AchievementWGData:GetTipViewToOpenViewType(type_sort, client_sort)
	return self.to_view_type_sort, self.to_view_client_sort
end

function AchievementWGData:GetIsTipViewToOpenViewType()
	return self.is_tip_to_view
end

--添加红点展示
--成就主界面红点.
function AchievementWGData:IsShowAchievementParentRedPoint()
	local accordion_tab = self:GetAccordionTable()
	for _, acc_info in pairs(accordion_tab) do
		if acc_info and acc_info.child then
			for k,v in pairs(acc_info.child) do
				if v.remind_tip > 0 then
					AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,TabIndex.achievement_achieve,1)
					return 1--可领取的任务个数>0
				end
			end
		end
	end

	AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,nil,0)
	return 0
end

--成就侧边
function AchievementWGData:IsShowAchievementRedPoint()
	local data = self:GetAchievementTable(true)
	local all_achievent = self:GetAllAchievement()
	local function remind_fun()
		for i,v in ipairs(data) do
			if v.has_active ~= 0 and v.has_fatch == 0 and all_achievent >= v.param1 then
				AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,TabIndex.achievement_achieve_totle,1)
				return 1
			end
		end
		AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,nil,0)
		return 0
	end
	if remind_fun() == 1 then
		AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,TabIndex.achievement_achieve_totle,1)
		return 1
	end


	local accordion_tab = self:GetAccordionTable()
	for _, acc_info in pairs(accordion_tab) do
		if acc_info and acc_info.child then
			for k,v in pairs(acc_info.child) do
				if v.remind_tip > 0 then
					AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,TabIndex.achievement_achieve,1)
					return 1--可领取的任务个数>0
				end
			end
		end
	end

	AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT,nil,0)
	return 0
end

--成就总览红点.
function AchievementWGData:IsShowAchievementTotalRedPoint()
	local all_achievent = self:GetAllAchievement()
	local data = self:GetAchievementTable(true)
	for i, v in ipairs(data) do
		if v.has_active ~= 0 and v.has_fatch == 0 and all_achievent >= v.param1 then
			AchievementWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.ACHIEVENT, TabIndex.achievement_achieve_totle, 1)
			return 1
		end
	end
	return 0
end