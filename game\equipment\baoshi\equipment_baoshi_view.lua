function EquipmentView:InitBaoShiView()
	-- 装备列表
	if not self.equip_bs_list then
		self.equip_bs_list = AsyncListView.New(EquipBaoShiItem<PERSON>ender, self.node_list["ph_bs_list_view"])
		self.equip_bs_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectEquipBaoShiItemHandler, self))
	end

	-- 宝石
	if not self.bs_slot_list then
		self.bs_slot_list = {}
		for i = 1, GameEnum.MAX_STONE_COUNT do
			self.bs_slot_list[i] = BSXQStoneItem.New(self.node_list["baoshi_part"]:FindObj("baoshi_" .. i))
			self.bs_slot_list[i]:SetIndex(i - 1)
		end
	end

	--肉身
	if not self.bsxq_equip_body_list then
		self.bsxq_equip_body_list = AsyncListView.New(BSXQEquipBodyListCellRender, self.node_list.bsxq_equip_body_list)
		self.bsxq_equip_body_list:SetStartZeroIndex(false)
		self.bsxq_equip_body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBSXQEquipBodyHandler, self))
		self.bsxq_equip_body_list:SetEndScrolledCallBack(BindTool.Bind(self.BSXQEquipBodyListSetEndScrollCallBack, self))
	end

	if not self.equip_bs_item_cell then
		self.equip_bs_item_cell = ItemCell.New(self.node_list["baoshi_eq_item"])
		self.equip_bs_item_cell:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end

	self.bs_select_equip_data = nil
	self.bs_old_progress_value = nil
	self.bs_select_equip_part = 0     -- 装备0-375下标
	self.bs_select_equip_index = -1

	self.bsxq_jump_equip_body_seq = -1
	self.bsxq_jump_equip_body_equip_data = {}
	self.bsxq_need_equip_body_tween = true

	XUI.AddClickEventListener(self.node_list["btn_yupo_add"], BindTool.Bind1(self.OnBtnBaoShiAddHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_bs_onekey_inlay"], BindTool.Bind1(self.OnBtnBaoShiOneKeyInlay, self))
	XUI.AddClickEventListener(self.node_list["btn_bsxq_equip_body"], BindTool.Bind1(self.OnClickBSXQEquipBodyBtn, self))
end

function EquipmentView:BaoShiDeleteMe()
	if self.equip_bs_list then
		self.equip_bs_list:DeleteMe()
		self.equip_bs_list = nil
	end

	if self.bs_slot_list then
		for k,v in pairs(self.bs_slot_list) do
			v:DeleteMe()
		end
		self.bs_slot_list = nil
	end

	if self.equip_bs_item_cell then
		self.equip_bs_item_cell:DeleteMe()
		self.equip_bs_item_cell = nil
	end

	if self.bsxq_equip_body_list then
		self.bsxq_equip_body_list:DeleteMe()
		self.bsxq_equip_body_list = nil
	end

	self.bs_select_equip_data = nil
	self.bs_select_equip_part = nil
	self.bs_old_progress_value = nil
	self.bsxq_jump_equip_body_seq = nil
	self.bsxq_jump_equip_body_equip_data = nil
	self.bsxq_need_equip_body_tween = nil

	EquipmentWGCtrl.Instance:DeleteBaoShiUpGradeAlertTips()
end

function EquipmentView:BaoShiViewShowIndexCallBack()
	self.bsxq_select_equip_body_seq = nil
	self.bsxq_select_equip_body_index = nil
end

-- 刷新宝石界面
function EquipmentView:FlushEquipBaoShiView(jump_data)
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	self.bsxq_equip_body_list:SetDataList(total_equip_body_data_list)

	local body_seq = jump_data and jump_data.open_param
	local jump_to_body_index = nil
	if body_seq then
		body_seq = tonumber(body_seq)
		-- 任务跳转，如果已解锁并已穿戴，直接跳到指定肉身
		local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(body_seq)
		if is_unlock and is_wear_equip then
			for k, v in pairs(total_equip_body_data_list) do
				if v.seq == body_seq then
					jump_to_body_index = k
					break
				end
			end
		end
	end

	if not jump_to_body_index then
		jump_to_body_index = self:GetBSXQSelectEquipBodySeq(total_equip_body_data_list)
	end
	self.bsxq_equip_body_list:JumpToIndex(jump_to_body_index)
end

function EquipmentView:EquipBaoShiChangeToTargetEquipBody(data)
	if IsEmptyTable(data) then
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	if IsEmptyTable(total_equip_body_data_list) then
		return
	end

	self.bsxq_jump_equip_body_seq = data.equip_body_seq
	self.bsxq_jump_equip_body_equip_data = data.selct_part_data

	for k, v in pairs(total_equip_body_data_list) do
		if v.seq == self.bsxq_jump_equip_body_seq then
			if self.bsxq_equip_body_list then
				self.bsxq_equip_body_list:JumpToIndex(k)
				self.bsxq_jump_equip_body_seq = -1
			end

			break
		end
	end
end

function EquipmentView:GetBSXQSelectEquipBodySeq(total_equip_body_data_list)
	-- if self.bsxq_select_equip_body_seq then
	-- 	return self.bsxq_select_equip_body_index
	-- end

	if self.bsxq_select_equip_body_seq then
		if EquipmentWGData.Instance:GetEquipBodyBSXQRemind(self.bsxq_select_equip_body_seq) > 0 then
			return self.bsxq_select_equip_body_index
		end
	end

	local default_seq = -1
	local default_index = -1
	if not IsEmptyTable(total_equip_body_data_list) then
		for i = #total_equip_body_data_list, 1, -1 do
			local data = total_equip_body_data_list[i]

			if EquipmentWGData.Instance:GetEquipBodyBSXQRemind(data.seq) > 0 then
				return i
			end

			if default_seq < 0 or data.seq > default_seq then
				local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(data.seq)
				
				if is_unlock and is_wear_equip then
					default_seq = data.seq
					default_index = i
				end
			end
		end
	end

	return self.bsxq_select_equip_body_index or default_index
end

-- function EquipmentView:GetBSXQSelectEquipBodySeq(total_equip_body_data_list)
-- 	if self.bsxq_select_equip_body_seq then
-- 		if EquipmentWGData.Instance:GetEquipBodyBSXQRemind(self.bsxq_select_equip_body_seq) then
-- 			return self.bsxq_select_equip_body_index
-- 		end
-- 	end

-- 	if not IsEmptyTable(total_equip_body_data_list) then
-- 		for k, v in pairs(total_equip_body_data_list) do
-- 			if EquipmentWGData.Instance:GetEquipBodyBSXQRemind(v.seq) > 0 then
-- 				return k
-- 			end
-- 		end
-- 	end

-- 	return self.bsxq_select_equip_body_index or 1
-- end

-- 选中肉身
function EquipmentView:OnSelectBSXQEquipBodyHandler(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	local bsxq_seq_change = self.bsxq_select_equip_body_seq ~= data.seq
	self.bsxq_select_equip_body_seq = data.seq
	self.bsxq_select_equip_body_index = item.index
	self.bsxq_select_equip_body_data = data
	self:FlushEquipBaoShiListDataSource(bsxq_seq_change)

	self:FlushBaoShiSlider()
	local active_remind = EquipmentWGData.Instance:GetEquipStoneActiveRemind(self.bsxq_select_equip_body_seq)
    self.node_list["btn_bs_add_remind"]:SetActive(active_remind > 0)
	self.node_list["btn_bs_add_effect"]:SetActive(active_remind > 0)
end

-- 刷列表数据源
function EquipmentView:FlushEquipBaoShiListDataSource(bsxq_seq_change)
	local show_equip_data_list = EquipmentWGData.Instance:GetBSXQShowEquipList(self.bsxq_select_equip_body_seq)
	self.equip_bs_list:SetDataList(show_equip_data_list)

	-- if not IsEmptyTable(self.bsxq_jump_equip_body_equip_data) then
	-- 	local select_index = -1
	-- 	for k, v in pairs(show_equip_data_list) do
	-- 		if v.index == self.bsxq_jump_equip_body_equip_data.index then
	-- 			select_index = k
	-- 			break
	-- 		end
	-- 	end

	-- 	if select_index >= 0 then
	-- 		self.equip_bs_list:JumpToIndex(select_index)
	-- 		return
	-- 	end
	-- end

	local default_select_index = self:CalculateDefaultIndex(show_equip_data_list, bsxq_seq_change)
	self.equip_bs_list:JumpToIndex(default_select_index)
end

-- 得到第一个有红点的装备在listview里的下标
function EquipmentView:CalculateDefaultIndex(equip_list, bsxq_seq_change)
	if not bsxq_seq_change and self.bs_select_equip_index > 0 then
		local equip_remind = EquipmentWGData.Instance:GetEquipBodyBSXQEquipRemind(self.bs_select_equip_part)

		if equip_remind > 0 then
			return self.bs_select_equip_index
		end
	end

	local default_index = -1
	for k, v in pairs(equip_list) do
		if default_index < 0 then
			default_index = k
		end

		local equip_remind = EquipmentWGData.Instance:GetEquipBodyBSXQEquipRemind(v.index)

		if equip_remind > 0 then
			return k
		end
	end

	if self.bs_select_equip_index >= 0 and not IsEmptyTable(equip_list[self.bs_select_equip_index]) then
		return self.bs_select_equip_index
	else
		return default_index
	end
end

-- 选择装备列表项回调
function EquipmentView:OnSelectEquipBaoShiItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if item.data.index ~= self.bs_select_equip_part then
		self.node_list["effect_root_baoshi"]:SetActive(false)
		self.bs_old_progress_value = nil

		if self.bs_slot_list then
			for k, v in pairs(self.bs_slot_list) do
				v:StopInalyTween()
			end
		end
	end

	self.bs_select_equip_data = item.data
	self.bs_select_equip_part = item.data.index
	self.bs_select_equip_index = item.index

	self:FlushBSEquipImg()
	self:FlushEquipBaoShiSlotList()
end

-- 中间显示装备
function EquipmentView:FlushBSEquipImg()
	if self.bs_select_equip_data then
		self.equip_bs_item_cell:SetData(self.bs_select_equip_data)
	end
end

-- 刷新六个宝石格子数据
function EquipmentView:FlushEquipBaoShiSlotList()
	-- local stone_info = EquipmentWGData.Instance:GetStoneInfoListByIndex(self.bs_select_equip_part)

	for k, v in pairs(self.bs_slot_list) do
		v:SetCurEquipPart(self.bs_select_equip_part, k)
		v:SetData(EquipmentWGData.Instance:GetBaoShiDataBySelectIndex(self.bs_select_equip_part, k - 1))
	end

	-- 一键镶嵌红点
	local is_have_batter = false
	for slot = 0, GameEnum.MAX_STONE_COUNT - 1 do
		is_have_batter = EquipmentWGData.Instance:GetStoneSlotHaveBatterState(self.bs_select_equip_part, slot)
		if is_have_batter then
			break
		end
	end

	local equip_is_all_inlay = EquipmentWGData.Instance:GetEquipPartIsAllInlayStone(self.bs_select_equip_part)
	local string_index = equip_is_all_inlay and 2 or 1
	self.node_list.btn_bs_onekey_inlay_text.text.text = Language.Equipment.BaoShiAndLingYuBtnStr[string_index]

	local can_up = EquipmentWGData.Instance:GetEquipPartCanUpStone(self.bs_select_equip_part)
	self.node_list["btn_bs_onekey_inlay_remind"]:CustomSetActive(is_have_batter or can_up)
end

-- 刷列表进度条
function EquipmentView:FlushBaoShiSlider()
	local next_total_cfg
	local next_level, active_level
	local cur_progress
	local tem_ratio = "<color=%s>(%s/%s)</color>"

	local baoshi_total_level = EquipmentWGData.Instance:GetTotalStoneLevel(self.bsxq_select_equip_body_seq)
	active_level = EquipmentWGData.Instance:GetStoneActiveLevel(self.bsxq_select_equip_body_seq)

	next_total_cfg = EquipmentWGData.Instance:GetBaoShiTotalStoneCfg(self.bsxq_select_equip_body_seq, active_level, true)
	if(next_total_cfg) then
		local is_enough = (next_total_cfg.total_stone - baoshi_total_level) <= 0
		local color = is_enough and "#99ffbb" or "#ff9292"
		cur_progress = baoshi_total_level / next_total_cfg.total_stone
		if(cur_progress < 1) then
			next_level = string.format(tem_ratio, color, baoshi_total_level, next_total_cfg.total_stone)
		else
			next_level = Language.Equip.JiaChengCanAct
			cur_progress = 1
		end
	else
		next_level = Language.Equip.JiaChengIsMax
		cur_progress = 1
	end

	if self.bs_old_progress_value == nil then
		self.node_list.bs_slider.slider.value = cur_progress
	else
		self.node_list.bs_slider.slider:DOValue(cur_progress, 0.5)
	end

	self.bs_old_progress_value = cur_progress
	self.node_list.bs_next_level.text.text = next_level
end

function EquipmentView:OnBtnBaoShiAddHandler()
	RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STONE_TIP, self.bsxq_select_equip_body_seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

function EquipmentView:OnClickBSXQEquipBodyBtn()
	EquipmentWGCtrl.Instance:OpenBSXQOverviewView()
end

function EquipmentView:OpenBSShowEquipTips()
	if self.bs_select_equip_data then
		TipWGCtrl.Instance:OpenItem(self.bs_select_equip_data, ItemTip.FROM_EQUIPMENT)
	end
end

-- 镶嵌成功特效
function EquipmentView:ShowBaoshiInlayEffect(equip_part, slot_index)
	slot_index = slot_index or 0

	if self.show_index == TabIndex.equipment_baoshi and self.bs_select_equip_part == equip_part then
		if self.bs_slot_list and self.bs_slot_list[slot_index + 1] then
			self.bs_slot_list[slot_index + 1]:DoInalyTween()
		end
	end

	if self.show_index == TabIndex.equipment_baoshi_jl and self.bsjl_select_equip_part == equip_part then
		if self.bsjl_slot_list and self.bsjl_slot_list[slot_index + 1] then
			self.bsjl_slot_list[slot_index + 1]:DoInalyTween()
		end
	end
end

-- 升级成功特效
function EquipmentView:PlayerStoneUpgradeEffect()
	local effect_root
	if self.node_list["effect_root_baoshi"] and self.show_index == TabIndex.equipment_baoshi then
		effect_root = self.node_list["effect_root_baoshi"]
	elseif self.node_list["bsjl_result_effect"] and self.show_index == TabIndex.equipment_baoshi_jl then
		effect_root = self.node_list["bsjl_result_effect"]
	end

	if effect_root then
		effect_root:SetActive(true)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
							is_success = true, pos = Vector2(0, 0), parent_node = effect_root})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

function EquipmentView:OnBtnBaoShiOneKeyInlay()
	-- local order = EquipmentWGData.EquipBodySeqAndPartToEquipBodyIndex(self.bsxq_select_equip_body_seq, self.bs_select_equip_part)
	local equip_is_all_inlay = EquipmentWGData.Instance:GetEquipPartIsAllInlayStone(self.bs_select_equip_part)
	local list = EquipmentWGData.Instance:GetAllBaoShiOneKeyInlay(self.bsxq_select_equip_body_seq)

	if not equip_is_all_inlay then
		if not IsEmptyTable(list) then
			-- 需要先请求一次全升级
			EquipmentWGCtrl.Instance:SendStoneOneKeyInlay(list)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Equipment.InlayBSNoBetterTips)
		end
	else
		local can_up = EquipmentWGData.Instance:GetEquipPartCanUpStone(self.bs_select_equip_part)

		if can_up then
			if not IsEmptyTable(list) then
				EquipmentWGCtrl.Instance:SendStoneOneKeyInlay(list)
				return
			end

			EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.AUTO_LEVEL_UP, self.bs_select_equip_part)
		else
			-- 当前已达最大等级
			local min_level_index = EquipmentWGData.Instance:GetEquipPartMinLevelStone(self.bs_select_equip_part)

			if min_level_index > -1 then
				EquipmentWGCtrl.Instance:BaoShiUpGradeOpen(self.bs_select_equip_part, min_level_index)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Equipment.InlayBSNoBetterTips)
			end
		end
	end

	-- if not IsEmptyTable(list) then
	-- 	-- 需要先请求一次全升级
	-- 	EquipmentWGCtrl.Instance:SendStoneOneKeyInlay(list)
	-- else
	-- 	if not equip_is_all_inlay then
	-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.Equipment.InlayBSNoBetterTips)
	-- 	end
	-- end

	-- if equip_is_all_inlay then
	-- 	local can_up = EquipmentWGData.Instance:GetEquipPartCanUpStone(self.bs_select_equip_part)

	-- 	if can_up then
	-- 		EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.AUTO_LEVEL_UP, self.bs_select_equip_part)
	-- 	else
	-- 		-- 当前已达最大等级
	-- 		local min_level_index = EquipmentWGData.Instance:GetEquipPartMinLevelStone(self.bs_select_equip_part)
	-- 		if min_level_index > -1 then
	-- 			EquipmentWGCtrl.Instance:BaoShiUpGradeOpen(self.bs_select_equip_part, min_level_index)
	-- 		else
	-- 			TipWGCtrl.Instance:ShowSystemMsg(Language.Equipment.InlayBSNoBetterTips)
	-- 		end
	-- 	end
	-- end
end

function EquipmentView:BSXQEquipBodyListSetEndScrollCallBack()
	if self.bsxq_need_equip_body_tween then
		self.bsxq_need_equip_body_tween = false

		local tween_info = UITween_CONSTS.EquipBody

		local cell_list = self.bsxq_equip_body_list:GetAllItems()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end
end

------------------------------------------BSXQEquipBodyListCellRender------------------------------------------
BSXQEquipBodyListCellRender = BSXQEquipBodyListCellRender or BaseClass(BaseRender)

function BSXQEquipBodyListCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickTipBtn, self))
end

function BSXQEquipBodyListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
	self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
		self.node_list.icon_hl.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name

	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not is_unlocak and not is_wear_equip)
	self.node_list.no_equip_tip:CustomSetActive(is_unlocak and not is_wear_equip)
	self.node_list.btn_tip:CustomSetActive(not can_duanzao)

	local remind = EquipmentWGData.Instance:GetEquipBodyBSXQRemind(self.data.seq) > 0
	self.node_list.remind:CustomSetActive(remind)
end

function BSXQEquipBodyListCellRender:OnSelectChange(is_select)
	local is_special = self.data.type == 1
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special)
end

function BSXQEquipBodyListCellRender:OnClickTipBtn()
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)

	if not can_duanzao then
		if not is_unlocak then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLockCanDuanZao)
		elseif not is_wear_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquipCanDuanZao)
		end
	end
end

function BSXQEquipBodyListCellRender:PlayItemTween()
	UITween.FakeHideShow(self.node_list.root)
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
	self.cell_delay_key = "BSXQEquipBodyItemCellRender" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.root then
			UITween.FakeToShow(self.node_list.root)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end