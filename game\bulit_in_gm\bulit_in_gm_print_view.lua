local print_sub_type = {
    info = 1,
    act = 2,
    protocol = 3,
    print = 4,
}

local print_sub_type_name = {
    "信息",
    "活动",
    "协议",
    "打印",
}

local print_act_type = {
    all = 0,            -- 全部
    cur = 1,            -- 当前
}

local print_protocol_info_type = {
    all = 0,            -- 全部
    filtrate = 1,       -- 筛选（只打印）
    eliminate = 2,      -- 剔除
}

local print_protocol_diff_type = {
    all = 0,            -- 全部
    sc = 1,
    cs = 2,
}

function BulitInGMView:ReleaseBulitInPrintView()
    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

    self.act_search_act_id = nil
    self.print_all_act_list_data = nil
    self.cur_print_select_index = 0
    self.cur_print_select_act = 0
    self.cur_print_select_act_big_type = -1
    self.cur_ptl_type = print_protocol_info_type.all
    self.ptl_all_list = {}
    self.ptl_all_sc_list = {}
    self.ptl_all_cs_list = {}
    
    self.ptl_filtrate_list = {}
    self.ptl_filtrate_ptl_type_list = {}
    self.ptl_filtrate_sc_list = {}
    self.ptl_filtrate_cs_list = {}

    self.ptl_eliminate_list = {}
    self.ptl_eliminate_ptl_type_list = {}
    self.ptl_eliminate_sc_list = {}
    self.ptl_eliminate_cs_list = {}

    if self.print_sub_list then
		self.print_sub_list:DeleteMe()
		self.print_sub_list = nil
	end

    if self.print_act_list then
		self.print_act_list:DeleteMe()
		self.print_act_list = nil
	end

    if self.print_log_list then
        self.print_log_list:DeleteMe()
        self.print_log_list = nil
    end
end

function BulitInGMView:InitBulitInPrintView()
    self.cur_print_select_index = 0
    self.print_sub_list = AsyncListView.New(PrintSubListRender, self.node_list.print_sub_list)
    self.print_sub_list:SetSelectCallBack(BindTool.Bind(self.OnSelectPrintSubHandler, self))

    self.print_all_act_list_data = nil
    self.cur_print_select_act_big_type = -1
    self.cur_print_select_act = 0
    self.print_act_list = AsyncListView.New(PrintActListRender, self.node_list.act_list)
    self.print_act_list:SetSelectCallBack(BindTool.Bind(self.OnSelectPrintActHandler, self))
    XUI.AddClickEventListener(self.node_list.act_toggle_all, BindTool.Bind(self.OnClickPrintActToggle, self, print_act_type.all))
    XUI.AddClickEventListener(self.node_list.act_toggle_cur, BindTool.Bind(self.OnClickPrintActToggle, self, print_act_type.cur))
    XUI.AddClickEventListener(self.node_list.btn_act_change_act, BindTool.Bind(self.OnClickPrintChangeAct, self))
    self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
    self.act_search_act_id = nil
    self.node_list["act_search_input_field"].input_field.onEndEdit:AddListener(BindTool.Bind(self.OnActInputFieldEndEdit, self))


    ----[[协议
    self.cur_ptl_type = print_protocol_info_type.all
    self.cur_ptl_diff = print_protocol_diff_type.all
    self.ptl_all_list = {}
    self.ptl_all_sc_list = {}
    self.ptl_all_cs_list = {}
    
    self.ptl_filtrate_list = {}
    self.ptl_filtrate_ptl_type_list = {}
    self.ptl_filtrate_sc_list = {}
    self.ptl_filtrate_cs_list = {}

    self.ptl_eliminate_list = {}
    self.ptl_eliminate_ptl_type_list = {}
    self.ptl_eliminate_sc_list = {}
    self.ptl_eliminate_cs_list = {}

    XUI.AddClickEventListener(self.node_list.switch_ptl_filtrate, BindTool.Bind(self.OnClickPrintPtlSwitch, self, print_protocol_info_type.filtrate))
    XUI.AddClickEventListener(self.node_list.switch_ptl_eliminate, BindTool.Bind(self.OnClickPrintPtlSwitch, self, print_protocol_info_type.eliminate))
    XUI.AddClickEventListener(self.node_list.btn_ptl_clean, BindTool.Bind(self.OnClickPrintPtlClean, self))
    XUI.AddClickEventListener(self.node_list.ptl_toggle_all, BindTool.Bind(self.OnClickPrintPtlDiff, self, print_protocol_diff_type.all))
    XUI.AddClickEventListener(self.node_list.ptl_toggle_sc, BindTool.Bind(self.OnClickPrintPtlDiff, self, print_protocol_diff_type.sc))
    XUI.AddClickEventListener(self.node_list.ptl_toggle_cs, BindTool.Bind(self.OnClickPrintPtlDiff, self, print_protocol_diff_type.cs))
    self.node_list.ptl_toggle_all.toggle.isOn = true

    self.node_list["ptl_eliminate_input_field"].input_field.text = "1104,1150,9001,9051,10413"
    self.node_list["ptl_filtrate_input_field"].input_field.onEndEdit:AddListener(BindTool.Bind(self.OnPtlInputFieldEndEdit, self, print_protocol_info_type.filtrate))
    self.node_list["ptl_eliminate_input_field"].input_field.onEndEdit:AddListener(BindTool.Bind(self.OnPtlInputFieldEndEdit, self, print_protocol_info_type.eliminate))
    --]]


    self.print_log_list = AsyncListView.New(PrintLogListRender, self.node_list.print_log_list)
    XUI.AddClickEventListener(self.node_list.btn_print_log, BindTool.Bind(self.OnClickPrintLog, self))
    
    -- XUI.AddClickEventListener(self.node_list.btn_gm_send, BindTool.Bind(self.OnClickGMSend, self))
    -- XUI.AddClickEventListener(self.node_list.btn_gm_send_close, BindTool.Bind(self.OnClickGMSendClose, self))
end

function BulitInGMView:FlushBulitInPrintView()
    if self.cur_print_select_index == 0 then
        self.print_sub_list:SetDataList(print_sub_type_name)
    else
        self:FlushBulitInPrintTypeView()
    end
end

function BulitInGMView:OnSelectPrintSubHandler(item)
    if not item or not item.data then
        return
    end

    if self.cur_print_select_index == item.index then
        return
    end

    self.cur_print_select_index = item.index
    if self.cur_print_select_index == print_sub_type.protocol then
        BaseProtocolStruct.PRINT = true
    end

    self:FlushBulitInPrintTypeView()
end

function BulitInGMView:FlushBulitInPrintTypeView()
    for i = 1, #print_sub_type_name do
        self.node_list["print_part_" .. i]:SetActive(self.cur_print_select_index == i)
    end
    
    if self.cur_print_select_index == print_sub_type.info then
        self:FlushPrintCommonInfo()
    elseif self.cur_print_select_index == print_sub_type.act then
        self:FlushPrintActInfo()
    elseif self.cur_print_select_index == print_sub_type.protocol then
        self:FlushPrintProtocol()
    elseif self.cur_print_select_index == print_sub_type.print then
        self:FlushPrintLog()
    end
end

-- 信息
function BulitInGMView:FlushPrintCommonInfo()
    local role_data = RoleWGData.Instance
    local role_vo = role_data:GetRoleVo()
    local role_name = role_vo.name
    local origin_server_id = role_data:GetOriginServerId()
    local role_id = role_data:InCrossGetOriginUid()
    local role_level = role_vo.level
    local vip_level = role_vo.vip_level or 0
    local role_sex = role_data:GetRoleSex()
    local role_prof = role_data:GetRoleProf()
    local agent_id = CHANNEL_AGENT_ID
    local str = string.format("姓名：%s     服ID：%s     角色id：%s     渠道：%s\n职业：%s     性别：%s       等级：%s     VIP：%s\n\n",
                role_name, origin_server_id, role_id, agent_id,
                Language.Common.ProfName[role_sex][role_prof] or "", Language.Common.SexName[role_sex], role_level, vip_level)

    local gold = role_vo.gold
    local bind_gold = role_vo.bind_gold
    local coin = role_vo.coin
    str = str .. string.format("仙玉：%s      绑玉：%s     铜币：%s\n",
                gold, bind_gold, coin)

    local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
    local history_recharge = RechargeWGData.Instance:GetHistoryRecharge()
    local history_recharge_count = RechargeWGData.Instance:GetHistoryRechargeCount()
    local real_chongzhi_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()
    str = str .. string.format("历史充值：%s     历史充值次数：%s       今日充值：%s    真实充值：%s\n",
                history_recharge, history_recharge_count, today_recharge, real_chongzhi_rmb)

    local cash_point_num = role_vo.cash_point
    local recharge_volume_num = role_vo.recharge_volume
    local replace_coin_num = RechargeWGData.Instance:GetReplaceCoinNum()
    str = str .. string.format("充值券：%s     现金点：%s       代币：%s\n\n",
                recharge_volume_num, cash_point_num, replace_coin_num)

	local real_start_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    str = str .. string.format("创角时间：%s    开服时间：%s     开服第%d天\n\n",
                TimeUtil.FormatYMDHMS(role_vo.create_timestamp), TimeUtil.FormatYMD(real_start_time), open_server_day)

    local scene_id = Scene.Instance:GetSceneId()
    local scene_type = Scene.Instance:GetSceneType()
    str = str .. string.format("当前所在场景id：%s    场景类型：%s   玩家当前是否在跨服场景：%s\n\n",
                scene_id, scene_type, IS_ON_CROSSSERVER)

    self.node_list.common_print_text.text.text = str
end

----[[ 活动
function BulitInGMView:OnClickPrintActToggle(act_big_type, is_on)
    if not is_on then
        return
    end

    self.cur_print_select_act_big_type = act_big_type
    local act_list = {}
    if act_big_type == print_act_type.all then
        if not self.print_all_act_list_data then
            local all_act_list = {}
            local cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto").daily
            for k, v in pairs(cfg) do
                table.insert(all_act_list, {act_type = v.act_type})
            end

            SortTools.SortAsc(all_act_list, "act_type")
            self.print_all_act_list_data = all_act_list
        end

        if self.act_search_act_id then
            for k,v in pairs(self.print_all_act_list_data) do
                if v.act_type == self.act_search_act_id then
                    table.insert(act_list, v)
                end
            end
        else
            act_list = self.print_all_act_list_data
        end
    else
        local cur_all_act = ActivityWGData.Instance:GetActivityStatus()
        for k,v in pairs(cur_all_act) do
            if ((self.act_search_act_id and self.act_search_act_id == k) or (not self.act_search_act_id))
            and v.status ~= ACTIVITY_STATUS.CLOSE then
                table.insert(act_list, {act_type = k})
            end
        end

        SortTools.SortAsc(act_list, "act_type")
    end

    self.cur_print_select_act = 0
    self.print_act_list:SetDataList(act_list)
    if #act_list > 0 then
        self.print_act_list:JumpToIndex(1)
    else
        self.node_list.act_str.text.text = "无数据"
    end
end

function BulitInGMView:OnSelectPrintActHandler(item)
    if not item or not item.data then
        return
    end

    if self.cur_print_select_act == item.data.act_type then
        return
    end

    self.cur_print_select_act = item.data.act_type
    self:FlushPrintActInfo()
end

function BulitInGMView:FlushPrintActInfo()
    if self.cur_print_select_act_big_type == -1 then
        self.node_list.act_toggle_all.toggle.isOn = true
        return
    end

    if not self.cur_print_select_act or self.cur_print_select_act <= 0 then
        self.node_list.act_str.text.text = "无数据"
        return
    end

    self.node_list.btn_act_change_act:SetActive(self.cur_print_select_act < 20000)
    -- 
    local act_name = ActivityWGData.Instance:GetActivityName(self.cur_print_select_act)
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(self.cur_print_select_act)
    if not act_info then
        local str = string.format("活动名：%s\n活动号：%s\n状态：<color=#f97878>无数据</color>",
        act_name, self.cur_print_select_act)

        self.node_list.act_str.text.text = str
        return
    end

    local status_str = ""
    if act_info.status == ACTIVITY_STATUS.CLOSE then
        status_str = "<color=#f97878>未开启</color>"
        local str = string.format("活动名：%s\n活动号：%s\n状态：%s",
        act_name, self.cur_print_select_act, status_str)
        self.node_list.act_str.text.text = str
        return
    elseif act_info.status == ACTIVITY_STATUS.STANDY then
        status_str = "<color=#9c790f>准备中</color>"
    elseif act_info.status == ACTIVITY_STATUS.OPEN then
        status_str = "<color=#99ffbb>已开启</color>"
    end

    local rest_time, end_time = ActivityWGData.Instance:GetActivityResidueTime(self.cur_print_select_act)
    local str = string.format("活动名：%s\n活动号：%s\n状态：%s\n活动开启时间：%s\n活动结束时间：%s\n活动剩余时间：%s\n活动开启天数：%s天\n活动开启在开服天数：%s天",
            act_name, self.cur_print_select_act, status_str, TimeUtil.FormatYMD(act_info.start_time), TimeUtil.FormatYMD(end_time),
            TimeUtil.FormatSecondDHM9(rest_time), ActivityWGData.Instance:GetActivityCurOpenday(self.cur_print_select_act),
            ActivityWGData.Instance:GetActivityOpenInServerDay(self.cur_print_select_act)
        )
    
    self.node_list.act_str.text.text = str
end

function BulitInGMView:OnClickPrintChangeAct()
    if not self.cur_print_select_act or self.cur_print_select_act <= 0 then
        return
    end

    if self.cur_print_select_act >= 20000 then
        TipWGCtrl.Instance:ShowSystemMsg("不可设置前端自定义的活动号")
        return
    end

    SysMsgWGCtrl.SendGmCommand("activitynextstate", self.cur_print_select_act)
end

-- 活动改变
function BulitInGMView:OnActivityChange(activity_type, status, next_time, open_type)
    if self.cur_print_select_index ~= print_sub_type.act or activity_type ~= self.cur_print_select_act then
        return
    end

    self:FlushPrintActInfo()
end

function BulitInGMView:OnActInputFieldEndEdit()
    local act_id_str = self.node_list["act_search_input_field"].input_field.text
    local act_id = tonumber(act_id_str)

    if act_id_str ~= "" and not act_id then
        self.node_list["act_search_input_field"].input_field.text = self.act_search_act_id or ""
        return
    end

    self.act_search_act_id = act_id
    self:OnClickPrintActToggle(self.cur_print_select_act_big_type, true)
end

-- 活动end]]

----[[ 协议
function BulitInGMView:OnClickPrintPtlDiff(print_ptl_diff, is_on)
    if not is_on then
        return
    end

    if self.cur_ptl_diff == print_ptl_diff then
        return
    end

    self.cur_ptl_diff = print_ptl_diff
    self:FlushPrintProtocol()
end

function BulitInGMView:OnClickPrintPtlSwitch(print_ptl_info_type, is_on)
    if not is_on and self.cur_ptl_type ~= print_protocol_info_type.all then
        self.cur_ptl_type = print_protocol_info_type.all
    elseif is_on then
        self.cur_ptl_type = print_ptl_info_type
    end

    self:SplitPtlStr(self.cur_ptl_type)
    TryDelayCall(self, function ()
        self:FlushPrintProtocol()
    end, 0.5, "bulit_gm_print_ptl_switch")
end

function BulitInGMView:SplitPtlStr(ptl_type)
    if ptl_type == print_protocol_info_type.all then
        return
    end

    local cahce_tab
    local ptl_str
    if ptl_type == print_protocol_info_type.filtrate then
        self.ptl_filtrate_ptl_type_list = {}
        cahce_tab = self.ptl_filtrate_ptl_type_list
        ptl_str = self.node_list["ptl_filtrate_input_field"].input_field.text
    elseif ptl_type == print_protocol_info_type.eliminate then
        self.ptl_eliminate_ptl_type_list = {}
        cahce_tab = self.ptl_eliminate_ptl_type_list
        ptl_str = self.node_list["ptl_eliminate_input_field"].input_field.text
    end

    if not ptl_str or not cahce_tab then
        return
    end

    local str_list = Split(ptl_str, ",")
    for k,v in pairs(str_list) do
        local msg_type = tonumber(v) or 0
        if msg_type > 0 then
            cahce_tab[msg_type] = true
        end
    end

    if ptl_type == print_protocol_info_type.filtrate and self.cur_ptl_type == print_protocol_info_type.filtrate then
        self.ptl_filtrate_list = {}
        self.ptl_filtrate_sc_list = {}
        self.ptl_filtrate_cs_list = {}
        for k,v in ipairs(self.ptl_all_list) do
            if self.ptl_filtrate_ptl_type_list[v.msg_type] then
                table.insert(self.ptl_filtrate_list, v)
            end
        end

        for k,v in ipairs(self.ptl_all_sc_list) do
            if self.ptl_filtrate_ptl_type_list[v.msg_type] then
                table.insert(self.ptl_filtrate_sc_list, v)
            end
        end

        for k,v in ipairs(self.ptl_all_cs_list) do
            if self.ptl_filtrate_ptl_type_list[v.msg_type] then
                table.insert(self.ptl_filtrate_cs_list, v)
            end
        end

    elseif ptl_type == print_protocol_info_type.eliminate and self.cur_ptl_type == print_protocol_info_type.eliminate then
        self.ptl_eliminate_list = {}
        self.ptl_eliminate_sc_list = {}
        self.ptl_eliminate_cs_list = {}
        for k,v in ipairs(self.ptl_all_list) do
            if not self.ptl_eliminate_ptl_type_list[v.msg_type] then
                table.insert(self.ptl_eliminate_list, v)
            end
        end

        for k,v in ipairs(self.ptl_all_sc_list) do
            if not self.ptl_eliminate_ptl_type_list[v.msg_type] then
                table.insert(self.ptl_eliminate_sc_list, v)
            end
        end

        for k,v in ipairs(self.ptl_all_cs_list) do
            if not self.ptl_eliminate_ptl_type_list[v.msg_type] then
                table.insert(self.ptl_eliminate_cs_list, v)
            end
        end
    end
end

function BulitInGMView:OnPtlInputFieldEndEdit(info_type)
    self:SplitPtlStr(info_type)
    if info_type == print_protocol_info_type.all then
        return
    end

    self:FlushPrintProtocol()
end

function BulitInGMView.CacheProtocolMsg(list, str)
    if not list then
        return
    end

    if #list >= 30 then
        table.remove(list, 1)
        table.insert(list, #list + 1, str)
    else
        table.insert(list, str)
    end
end

function BulitInGMView:AddProtocolMsg(msg_type, op_type, time)
    local a , b = math.modf(time)
    local time_str = os.date("%X", a)
    b = math.floor(b * 1000)
    time_str = string.format("%s.%s", time_str, b)

    local is_sc = op_type == 0
    local str
    if is_sc then
        str = string.format("<color=#00ff00>---- 接受协议，协议号: %s</color>\n", msg_type)
    else
        str = string.format("<color=#ffac1c>---- 发送协议，协议号: %s</color>\n", msg_type)
    end

    local info_str = string.format("%s %s", time_str, str)
    local data = {
        msg_type = tonumber(msg_type),
        op_type = op_type,
        str = info_str,
    }
    BulitInGMView.CacheProtocolMsg(self.ptl_all_list, data)
    if is_sc then
        BulitInGMView.CacheProtocolMsg(self.ptl_all_sc_list, data)
    else
        BulitInGMView.CacheProtocolMsg(self.ptl_all_cs_list, data)
    end

    if self.ptl_filtrate_ptl_type_list[msg_type] then
        BulitInGMView.CacheProtocolMsg(self.ptl_filtrate_list, data)
        if is_sc then
            BulitInGMView.CacheProtocolMsg(self.ptl_filtrate_sc_list, data)
        else
            BulitInGMView.CacheProtocolMsg(self.ptl_filtrate_cs_list, data)
        end
    end

    if not self.ptl_eliminate_ptl_type_list[msg_type] then
        BulitInGMView.CacheProtocolMsg(self.ptl_eliminate_list, data)
        if is_sc then
            BulitInGMView.CacheProtocolMsg(self.ptl_eliminate_sc_list, data)
        else
            BulitInGMView.CacheProtocolMsg(self.ptl_eliminate_cs_list, data)
        end
    end

    -- 大类型是否刷新
    local is_need_flush = false
    if self.cur_ptl_type == print_protocol_info_type.all then
        is_need_flush = true
    elseif self.cur_ptl_type == print_protocol_info_type.filtrate then
        if self.ptl_filtrate_ptl_type_list[msg_type] then
            is_need_flush = true
        end
    elseif self.cur_ptl_type == print_protocol_info_type.eliminate then
        if not self.ptl_eliminate_ptl_type_list[msg_type] then
            is_need_flush = true
        end
    end

    -- 小类是否刷新
    if is_need_flush then
        if self.cur_ptl_diff == print_protocol_diff_type.all then
            is_need_flush = true
        elseif self.cur_ptl_diff == print_protocol_diff_type.sc then
            is_need_flush = is_sc
        elseif self.cur_ptl_diff == print_protocol_diff_type.cs then
            is_need_flush = not is_sc
        end
    end

    if is_need_flush then
        self:FlushPrintProtocol()
    end
end

function BulitInGMView:OnClickPrintPtlClean()
    self.ptl_all_list = {}
    self.ptl_all_sc_list = {}
    self.ptl_all_cs_list = {}
    
    self.ptl_filtrate_list = {}
    self.ptl_filtrate_sc_list = {}
    self.ptl_filtrate_cs_list = {}

    self.ptl_eliminate_list = {}
    self.ptl_eliminate_sc_list = {}
    self.ptl_eliminate_cs_list = {}
    self:FlushPrintProtocol()
end

function BulitInGMView:FlushPrintProtocol()
    local cur_list
    if self.cur_ptl_type == print_protocol_info_type.all then
        if self.cur_ptl_diff == print_protocol_diff_type.all then
            cur_list = self.ptl_all_list
        elseif self.cur_ptl_diff == print_protocol_diff_type.sc then
            cur_list = self.ptl_all_sc_list
        elseif self.cur_ptl_diff == print_protocol_diff_type.cs then
            cur_list = self.ptl_all_cs_list
        end
    elseif self.cur_ptl_type == print_protocol_info_type.filtrate then
        if self.cur_ptl_diff == print_protocol_diff_type.all then
            cur_list = self.ptl_filtrate_list
        elseif self.cur_ptl_diff == print_protocol_diff_type.sc then
            cur_list = self.ptl_filtrate_sc_list
        elseif self.cur_ptl_diff == print_protocol_diff_type.cs then
            cur_list = self.ptl_filtrate_cs_list
        end
    elseif self.cur_ptl_type == print_protocol_info_type.eliminate then
        if self.cur_ptl_diff == print_protocol_diff_type.all then
            cur_list = self.ptl_eliminate_list
        elseif self.cur_ptl_diff == print_protocol_diff_type.sc then
            cur_list = self.ptl_eliminate_sc_list
        elseif self.cur_ptl_diff == print_protocol_diff_type.cs then
            cur_list = self.ptl_eliminate_cs_list
        end
    end

    if not cur_list then
        return
    end

    local str_tab = {}
    for k,v in ipairs(cur_list) do
        table.insert(str_tab, v.str)
    end

    local str = table.concat(str_tab)
    self.node_list.ptl_show_text.text.text = str
end
-- 协议end]]


----[[ 日志
function BulitInGMView:FlushPrintLog()
    local log_list = BulitInGMData.Instance:GetNeedPrintLogList()
    self.print_log_list:SetDataList(log_list)
end

function BulitInGMView:OnClickPrintLog()
    local str = ""
    local log_list = BulitInGMData.Instance:GetNeedPrintLogList()
    for k,v in ipairs(log_list) do
        if v.is_print and v.print_str ~= "" then
            str = str .. string.format("【打印%s】\n", k)
            str = str .. self:ParsePrintLogStr(v.print_str) .. "\n\n"
        end
    end

    self.node_list.log_show_text.text.text = str
end

local class_type_table = {}
local function GetClassType(class_str)
	local type = class_type_table[class_str]
	if type == nil then
		for k,v in pairs(_G) do
			if k == class_str then
				class_type_table[class_str] = v
				type = v
			end
		end
	end

	return type
end

function BulitInGMView:ParsePrintLogStr(str)
    if not str or str == "" then
        return "无字符串"
    end

    local class_type = string.match(str, "%a+", 1)
    if not class_type then
        return "字符串解析异常"
    end

    local func = GetClassType(class_type)
    if func == nil then
        return "全局找不到类名"
    end

    local result = self:DoParsePrintLogStr(str, func, string.len(class_type) + 1)
    local result_type = type(result)
    if result_type == "function" then
        return "数据打印异常，结果为function类型"

    elseif result_type == "userdata" then
        return "数据打印异常，结果为userdata类型"

    elseif result_type == "thread" then
        return "数据打印异常，结果为thread类型"
    end

    local result_str = "error"
    if result_type == "table" then
        result_str = TableToStr(result, 1)
    else
        result_str = tostring(result)
    end

    return result_str
end

local colon_str = ":"
local dot_str = "."
local parenthese_str = "()"
function BulitInGMView:DoParsePrintLogStr(str, func, start_index)
    local param_start_index, param_end_index = string.find(str, "[a-zA-Z_]+", start_index)
    -- 吊毛lua不能使用|,实现或,所以拆开判断
    local symbol_start_index, symbol_end_index = string.find(str, "[%.:]", start_index)
    local parenthese_str_start_index, parenthese_str_end_index = string.find(str, "[%(%)]+", start_index)

    if not symbol_start_index and not parenthese_str_start_index then
        return func
    end

    local first_symbol_str
    if symbol_start_index and ((not parenthese_str_start_index) or (parenthese_str_start_index and symbol_start_index < parenthese_str_start_index)) then
        if param_start_index and param_start_index < symbol_start_index then
            return "字符串解析异常"
        else
            first_symbol_str = string.match(str, "[%.:]", start_index)
        end
    elseif parenthese_str_start_index and ((not symbol_start_index) or (symbol_start_index and parenthese_str_start_index < symbol_start_index)) then
        if param_start_index and param_start_index < parenthese_str_start_index then
            return "字符串解析异常"
        else
            first_symbol_str = string.match(str, "[%(%)]+", start_index)
        end
    else
        return "字符串解析异常，找不到符号"
    end

    local param_str = string.match(str, "[a-zA-Z_]+", start_index)
    if first_symbol_str == colon_str then
        if symbol_end_index + 1 ~= param_start_index then
            return "字符串解析错误，：后异常"
        end

        -- 占不解析（）带参
        if not parenthese_str_start_index or (parenthese_str_start_index ~= param_end_index + 1) or (parenthese_str_start_index + 1 ~= parenthese_str_end_index) then
            return "字符串解析错误，（）异常"
        end

        if func[param_str] == nil then
            return "nil"
        end

        func = func[param_str](func)
        return self:DoParsePrintLogStr(str, func, parenthese_str_end_index + 1)

    elseif first_symbol_str == dot_str then
        if symbol_end_index + 1 ~= param_start_index then
            return "字符串解析错误， .后异常"
        end

        func = func[param_str]
        if func == nil then
            return "nil"
        end

        return self:DoParsePrintLogStr(str, func, param_end_index + 1)

    elseif first_symbol_str == parenthese_str then
        if param_start_index and symbol_end_index + 2 == param_start_index then
            return "字符串解析错误， （）后异常"
        end

        func = func()
        return self:DoParsePrintLogStr(str, func, symbol_end_index + 1)
    end
end
-- 打印end]]

--[[
    

]]










PrintSubListRender = PrintSubListRender or BaseClass(BaseRender)
function PrintSubListRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["text_n"].text.text = self.data
    self.node_list["text_hl"].text.text = self.data
end

function PrintSubListRender:OnSelectChange(is_select)
    self.node_list["img_n"]:SetActive(not is_select)
    self.node_list["img_hl"]:SetActive(is_select)
    self.node_list["text_n"]:SetActive(not is_select)
    self.node_list["text_hl"]:SetActive(is_select)
end




PrintActListRender = PrintActListRender or BaseClass(BaseRender)
function PrintActListRender:OnFlush()
    if self.data == nil then
        return
    end

    local name = ActivityWGData.Instance:GetActivityName(self.data.act_type)
    self.node_list["text_n"].text.text = name
    self.node_list["text_hl"].text.text = name
end

function PrintActListRender:OnSelectChange(is_select)
    self.node_list["img_n"]:SetActive(not is_select)
    self.node_list["img_hl"]:SetActive(is_select)
    self.node_list["text_n"]:SetActive(not is_select)
    self.node_list["text_hl"]:SetActive(is_select)
end











PrintLogListRender = PrintLogListRender or BaseClass(BaseRender)
function PrintLogListRender:__init()
    XUI.AddClickEventListener(self.node_list.switch, BindTool.Bind(self.OnClickPrintToggle, self))
    self.node_list["input_field"].input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldEndEdit, self))
end

function PrintLogListRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.switch.toggle.isOn = self.data.is_print
    self.node_list["input_field"].input_field.text = self.data.print_str
end

function PrintLogListRender:OnClickPrintToggle(is_on)
    self.data.is_print = is_on
end

function PrintLogListRender:OnInputFieldEndEdit()
    self.data.print_str = self.node_list["input_field"].input_field.text
end