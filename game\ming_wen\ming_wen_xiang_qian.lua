MingWenView = MingWenView or BaseClass(SafeBaseView)

local MAX_TOGGLE_NUM = 10 --最大页签数量

function MingWenView:InitXiangQian() --初始化 镶嵌页面
	self.max_tab_num = 0
	self.load_cell_complete_count = 0
	self.xq_cell_list = {}
	self.select_big_index = 1
	self.select_small_index = -1
	self.cur_select_index = -1
	self.cur_select_slot_index = -1

	if not self.mingwen_list then
        self.mingwen_list = {}
        for i = 1, 4 do
            self.mingwen_list[i] = MingWenItemRender.New(self.node_list.mingwen_item_list:FindObj("mingwen_item_cell" .. i))
			self.mingwen_list[i]:SetIsOnlyShow(false)
			self.mingwen_list[i]:SetIsShowItemTip(false)
			self.mingwen_list[i]:SetIndex(i)
			self.mingwen_list[i]:SetClickCallBack(BindTool.Bind(self.XiangQianSelectCallBack, self, self.mingwen_list[i], true))
        end
    end

	self:CreateBigList()

	self.node_list["btn_rune_insert"].button:AddClickListener(BindTool.Bind(self.RuneXiangQian,self))  --铭文 镶嵌 or 替换按钮
	self.node_list["btn_rune_upgrade"].button:AddClickListener(BindTool.Bind(self.RuneUpGrade,self))   --铭文升级按钮
	--self.node_list["rune_gobtn"].button:AddClickListener(BindTool.Bind(self.OpenGetRune,self))         --前往副本解锁槽位
	self.node_list["rune_library"].button:AddClickListener(BindTool.Bind(self.OpenPosyLibrary,self))   --打开铭文总览
	self.node_list["btn_open_bag"].button:AddClickListener(BindTool.Bind(self.OpenPosyBag,self))
	self.node_list["btn_open_attr"].button:AddClickListener(BindTool.Bind(self.OpenPosyAllAttr,self))
	self.node_list["btn_rune_gotoget"].button:AddClickListener(BindTool.Bind(self.OpenGetRune,self))   --前往修真路获取
	self.node_list["btn_hunt"].button:AddClickListener(BindTool.Bind(self.OpenGetRune,self))   --前往修真路获取
	self.node_list["btn_blcs"].button:AddClickListener(BindTool.Bind(self.OpenBLCSView,self))   
	self.node_list["cost_image1"].button:AddClickListener(BindTool.Bind(self.ClickExp,self))   --点击经验图标
	self.node_list["cost_image2"].button:AddClickListener(BindTool.Bind(self.ClickExp2,self))   --点击经验图标
	self.node_list["btn_open_onesword_frostbite"].button:AddClickListener(BindTool.Bind(self.OpenOneSwordFrostbite,self))   --点击经验图标
end

function MingWenView:CreateBigList()
	self.xq_accordion_list = {}

	self.max_tab_num = MingWenWGData.Instance:GetCurToggleNum()

	for i = 1, MAX_TOGGLE_NUM do
		if i <= self.max_tab_num then
			self.xq_accordion_list[i] = {}
			self.node_list["Content"]:FindObj("SelectBtn" .. i):SetActive(true)
			self.xq_accordion_list[i].btn = self.node_list["Content"]:FindObj("SelectBtn" .. i)
			self.xq_accordion_list[i].list = self.node_list["Content"]:FindObj("List" .. i)
			self.xq_accordion_list[i].btn.accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickXQExpandHandler, self, i))

			local cfg = MingWenWGData.Instance:GetBigTabCfgByIndex(i)
			if cfg then
				self.node_list["btn_text" .. i].text.text = cfg.tab_name_1
				self.node_list["btn_text_hl" .. i].text.text = cfg.tab_name_1
			end

			self:InstantItem(i)
		else
			self.node_list["Content"]:FindObj("SelectBtn" .. i):SetActive(false)
		end
	end
end

function MingWenView:InstantItem(index)
	local xq_list = MingWenWGData.Instance:GetSmallToggleData(index)
	if not self.xq_cell_list[index] then
		local res_async_loader = AllocResAsyncLoader(self, "mwxq_accordion_item" .. index)

	 	res_async_loader:Load("uis/view/ming_wen_ui_prefab", "mwxq_accordion_item", nil,
	 		function(new_obj)
			    local item_vo = {}
			    for k, v in pairs(xq_list) do
			        local obj = ResMgr:Instantiate(new_obj)
			        local obj_transform = obj.transform
			        obj_transform:SetParent(self.xq_accordion_list[index].list.transform, false)
			        local item_render = XQListItemRender.New(obj)
			        item_render.parent_view = self
			        item_render:SetIndex(k)
			        item_render.view.button:AddClickListener(BindTool.Bind(self.SelectXQCellCallBack, self, item_render))
			        item_render:SetData(v)
			        item_vo[k] = item_render
			    end

				self.xq_cell_list[index] = item_vo
				self.load_cell_complete_count = self.load_cell_complete_count + 1

			    if self.load_cell_complete_count == self.max_tab_num then
					-- 第一次打开界面进行跳转
					for k,v in ipairs(self.xq_accordion_list) do
						if (MingWenWGData.Instance:GetBigTabtRedByIndex(k)) then
							self.xq_accordion_list[k].btn.toggle.isOn = true
							return
						end
					end
					self.xq_accordion_list[1].btn.toggle.isOn = true
				end
			end)
	end
end

function MingWenView:ShowFlushCell(index)
	if self.load_cell_complete_count < self.max_tab_num then
		return
	end

	local xq_list = MingWenWGData.Instance:GetSmallToggleData(index)
	if self.xq_cell_list[index] then
		for k, v in pairs(self.xq_cell_list[index]) do
			if nil == xq_list[k] then
				break
			end
			v:SetData(xq_list[k])
		end
	end
end

function MingWenView:FlushXiangQian()
	self:ShowFlushCell(self.select_big_index)
	self:FlushBigTabRed()
	self:FlushXiangQianList()
	--self:FLushXiangQianCellHL()
	self:FlushXiangQianBottom()
end

function MingWenView:FlushBigTabRed()
	for k, v in ipairs(self.xq_accordion_list) do
		if v.btn:FindObj("img_read") then
			v.btn:FindObj("img_read"):SetActive(MingWenWGData.Instance:GetBigTabtRedByIndex(k))
		end
	end
end

function MingWenView:OnClickXQExpandHandler(idx, is_on)
	self.select_big_index = idx

	if not is_on then
		return
	end

	if IsEmptyTable(((self.xq_cell_list or {})[idx] or {})[1] or {}) then
		return
	end

	self:FlushSmallTabList()
end

--点击大按钮后刷新小按钮并且跳转到对应的小按钮位置
function MingWenView:FlushSmallTabList()
	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end

	self:ShowFlushCell(self.select_big_index)
	local default_select_cell_index = MingWenWGData.Instance:GetJumpTabtRedIndexByIndex(self.select_big_index)
	self:SelectXQCellCallBack(self.xq_cell_list[self.select_big_index][default_select_cell_index])

	--当跳转的索引超过6时会超出屏幕显示位置需要移动位置
	if(default_select_cell_index > 6) then
		self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
			self:CalcScroll(self.xq_cell_list[self.select_big_index][default_select_cell_index])
		end, 0.7)
	end
end

function MingWenView:SelectXQCellCallBack(cell, is_On)
	if nil == cell or nil == cell.data then return end

	--if self.select_small_index == cell.index then return end

	self.select_small_index = cell.index
	self:FlushXQCellHighLight(cell.index)
	local data_list = MingWenWGData.Instance:GetSmallTabListCfgByIndex(self.select_big_index, cell.index)
	if #data_list == 2 then
		self.node_list.mingwen_item_list.horizontal_layout_group.spacing = 128
	else
		self.node_list.mingwen_item_list.horizontal_layout_group.spacing = 18
	end

	for i = 1, 4 do
		if i <= #data_list then
			self.node_list.mingwen_item_list:FindObj("mingwen_item_cell" .. i):SetActive(true)
			self.mingwen_list[i]:SetData(data_list[i])
		else
			self.node_list.mingwen_item_list:FindObj("mingwen_item_cell" .. i):SetActive(false)
			self.mingwen_list[i]:SetData(nil)
		end
	end

	local red_pos = 1
	for i = 1, #data_list do
		if MingWenWGData.Instance:GetIsSlotRedByIndex(data_list[i].slot_index) then
			red_pos = i
			break
		end
	end

	self:XiangQianSelectCallBack(self.mingwen_list[red_pos], false)
end

function MingWenView:FlushXQCellHighLight(index)
	for k, v in ipairs(self.xq_cell_list[self.select_big_index]) do
		v:SetSelect(index)
	end
end

function MingWenView:DeleteXiangQian()
end

--释放铭文镶嵌列表
function MingWenView:ReleaseXiangQian()
	if self.mingwen_list then
        for k,v in pairs(self.mingwen_list) do
            v:DeleteMe()
        end
		self.mingwen_list = nil
	end

	--if self.cur_rune_cell then
	--	self.cur_rune_cell:DeleteMe()
	--	self.cur_rune_cell = nil
	--end

	if self.xq_cell_list then
		for k,v in pairs(self.xq_cell_list) do
			for m,n in pairs(v) do
				n.parent_view = nil
				n:DeleteMe()
			end
		end
	end
	self.xq_cell_list = {}

	self.cur_select_index = -1
	self.cur_select_slot_index = -1
end

--打开副本页面 关闭铭文页面  /  修改为跳转到寻宝界面(2023.12.28)
function MingWenView:OpenGetRune()
	local is_open, tip = FunOpen.Instance:GetFunIsOpenByViewNameAndNumberIndex(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_fuwen)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(tip)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_fuwen)
	end
end

--打开铭文总览页面
function MingWenView:OpenPosyLibrary()
	--在Ctl层 调用 铭文总览view的open方法
	MingWenWGCtrl.Instance:OpenPosyLibrary()
end

function MingWenView:OpenPosyBag()
	--在Ctl层 调用 铭文总览view的open方法
	MingWenWGCtrl.Instance:OpenMingWenBag(-1)
end

function MingWenView:OpenPosyAllAttr()
	--在Ctl层 调用 铭文总览view的open方法
	-- local cap, attr_list = MengLingWGData.Instance:GetMengLingTotalAttrInfo(false)
	local attr_list = MingWenWGData.Instance:GetAllEquipPosyShowAttr()

	local tips_data = {
		title_text = Language.MingWenView.TitleAttrName,
		attr_data = attr_list,   --  { attr_str, attr_value }
	}

	MingWenWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

--点击铭文方法
function MingWenView:XiangQianSelectCallBack(cell, is_click)
	local is_open ,tianxiange_fb_pass_level = MingWenWGData.Instance:GetSlotIndexIsOpen(cell.data.slot_index)
	self.node_list["ButtonGroup"]:SetActive(is_open)
	local is_select = false
	if self.cur_select_slot_index ~= cell.data.slot_index then
		self.cur_select_index = cell.index
		self.cur_select_slot_index = cell.data.slot_index
		--点击铭文后  刷新几个显示
		self:FLushXiangQianCellHL()
		is_select = true
	end

	if is_open then
		local data = MingWenWGData.Instance:GetEquipPosyDataBySlot(cell.data.slot_index)
		if is_select then
			self:FlushXiangQianBottom()
			if is_click then
				if cell.is_run == 1 then
					if IsEmptyTable(data) or data.posy.item_id <= 0 then
						self:XiangQianBestMingWen()
					else
						self:RuneXiangQian()
					end
				elseif cell.is_not_can_equip then
					self:OnClickItemNotCanEquip()
				end
			end
		elseif is_click then --否则就直接打开铭文背包
			if cell.is_not_can_equip then
				self:OnClickItemNotCanEquip()
			elseif cell.is_run == 1 then
				if IsEmptyTable(data) or data.posy.item_id <= 0 then
					self:XiangQianBestMingWen()
				else
					self:RuneXiangQian()
				end
			else
				self:RuneXiangQian()
			end
		end
	elseif is_click then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.MingWenView.PassLevelNotEnough, tianxiange_fb_pass_level))
	end
end

function MingWenView:OnClickItemNotCanEquip()
	local slot_cfg = MingWenWGData.Instance:GetPosySlotCfgBySlotIndex(self.cur_select_slot_index)
	local type = slot_cfg.put_type
	local cfg = MingWenWGData.Instance:GetMingWenBaseCfgByTypeAndQuality(type, 5)
	local cell_data = {}
	cell_data.item_id = cfg.id
	TipWGCtrl.Instance:OpenItem(cell_data)
end

function MingWenView:XiangQianBestMingWen()
	local data = MingWenWGData.Instance:GetBaseBagPosyData(self.cur_select_slot_index, false)
	MingWenWGCtrl.Instance:SendMingWenOperaReq(PPSY_OPERA_TYPE.POSY_OPERATOR_TYPE_PUT_SLOT, self.cur_select_slot_index, data[1].index, MingWenWGData.Instance:GetIsSpecialBySlotIndex(self.cur_select_slot_index))
end

--铭文镶嵌 or 替换按钮方法    都是打开铭文背包
function MingWenView:RuneXiangQian()
	MingWenWGCtrl.Instance:OpenMingWenBag(self.cur_select_slot_index)
end

--铭文升级按钮方法
function MingWenView:RuneUpGrade()
	local is_special = 0

	if MingWenWGData.Instance:GetIsSpecialBySlotIndex(self.cur_select_slot_index) == 1 then
		is_special = 1
	end

	--客户端向服务器进行 操作请求： 铭文升级
	MingWenWGCtrl.Instance:SendMingWenOperaReq(PPSY_OPERA_TYPE.POSY_OPERATOR_TYPE_UP_LEVEL, self.cur_select_slot_index, is_special)
end

function MingWenView:ClickExp()
	local data = {}
	data.item_id = COMMON_CONSTS.VIRTUAL_ITEM_MINGWEN
	TipWGCtrl.Instance:OpenItem(data)
end

function MingWenView:ClickExp2()
	local data = {}
	data.item_id = COMMON_CONSTS.VIRTUAL_ITEM_MINYIN
	TipWGCtrl.Instance:OpenItem(data)
end

function MingWenView:OpenOneSwordFrostbite()
	ViewManager.Instance:Open(GuideModuleName.OneSwordFrostbiteView)
end

--刷新底部界面
function MingWenView:FlushXiangQianBottom()
	--if nil == self.cur_rune_cell then
	--	self.cur_rune_cell = ItemCell.New(self.node_list["xiangqian_cur_rune"])
	--end
	if self.cur_select_slot_index < 0 then
		return
	end
	local is_red = 0
	--如果当前下标 大于了 8  
	is_red = MingWenWGData.Instance:GetSlotIsHasBetterByIndex(self.cur_select_slot_index)
 	self.node_list["btn_insert_red2"]:SetActive(is_red == 1) --替换按钮 红点激活

	local data = MingWenWGData.Instance:GetEquipPosyDataBySlot(self.cur_select_slot_index)
	local exp,xin_jing,min_yin = MingWenWGData.Instance:GetMingWenExpAndXinJing()  --获得 升级需要的

	--not IsEmptyTable(data) and data.posy.type > 0 两者都满足 就返回true   有一个为假 则返回false
	local can_show_right = not IsEmptyTable(data) and data.posy.type > 0

	--btn_rune_upgrade 铭文升级按钮
	--btn_rune_gotoget 前往获取
	self.node_list["btn_rune_gotoget"]:SetActive(not can_show_right) --反之隐藏该面板
	-- 右侧 选中的铭文预览的icon
	--self.node_list["xiangqian_cur_rune"]:SetActive(can_show_right) --can_show_right 为true  激活
	--self.node_list["rune_attr_group"]:SetActive(can_show_right)  -- 铭文预览大图
	--self.node_list["rune_attr_panel"]:SetActive(can_show_right)  -- 当前属性显示
	--self.node_list["rune_blank_tips"]:SetActive(not can_show_right)  --没有铭文 就显示(前往修真路获取的面板)

	--self.node_list["rune_is_max"]:SetActive(false)  --升级  已满级的面板

	--如果是true   以下为   当前选中铭文的  icon显示  属性显示  升级经验显示
	if can_show_right then
		--self.cur_rune_cell:SetData(data.posy)
		self.node_list["btn_rune_insert"]:SetActive(true)
		local cfg = ItemWGData.Instance:GetItemConfig(data.posy.item_id)
		local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(data.posy.item_id)
		if not base_cfg then
			return
		end

		local can_up_grade = base_cfg.is_uplevel == 1 --base_cfg.cost_type > 0

		if can_up_grade then
			-- 旧版是显示等级的  新版的不显示铭文等级了  去掉红色字体
			--local name_str = cfg.name
			--self.node_list["cur_rune_name"].text.text = ToColorStr(name_str, ITEM_COLOR[cfg.color])
			--local lv_str = string.format(Language.MingWenView.MingWenJi, data.posy.level)
			--self.node_list["cur_rune_lv"].text.text = ToColorStr(lv_str, ITEM_COLOR[cfg.color])
			local now_level = MingWenWGData.Instance:GetMingWenLvCfgByLv(data.posy.level)
			local next_level = MingWenWGData.Instance:GetMingWenLvCfgByLv(data.posy.level + 1)
			if not IsEmptyTable(next_level) then
				self.node_list["cost"]:SetActive(true)  --升级消耗 主面板
				local need_exp = now_level["cost_jingyan_"..base_cfg.cost_type]  --升级需要的材料
				local need_min_yin = now_level["cost_mingyin_"..base_cfg.cost_type]
				local need_xinjing = now_level["cost_xinjing_"..base_cfg.cost_type]
				local color1 = exp >= need_exp and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
				local color2 = min_yin >= need_min_yin and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
				local color3 = xin_jing >= need_xinjing and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED

				self.node_list["rune_up_cost1"]:SetActive(true)
				if need_exp > 0 then  --普通铭文  经验值
					self.node_list["rune_up_cost1"].text.text = ToColorStr(exp,color1).."/"..need_exp
					self.node_list["cost_image1"]:SetActive(true)
					self.node_list["cost_image2"]:SetActive(false)
				elseif need_min_yin > 0 then  --特殊铭文1
					self.node_list["rune_up_cost1"].text.text = ToColorStr(min_yin,color2).."/"..need_min_yin
					self.node_list["cost_image1"]:SetActive(false)
					self.node_list["cost_image2"]:SetActive(true)
				else   --特殊铭文2
					self.node_list["cost_image1"]:SetActive(false)
					self.node_list["cost_image2"]:SetActive(false)
					self.node_list["rune_up_cost1"].text.text = ToColorStr(xin_jing,color3).."/"..need_xinjing
				end

				--self.node_list["rune_is_max"]:SetActive(false)  --已满级显示
				self.node_list["btn_rune_upgrade"]:SetActive(true)  --升级按钮
				self.node_list["btn_change_text"].text.text = Language.MingWenView.InsertText2  --镶嵌or替换 按钮 更改文字
			else
				self.node_list["cost"]:SetActive(false)  --升级消耗 主面板
				--self.node_list["rune_is_max"]:SetActive(true)
				self.node_list["btn_rune_upgrade"]:SetActive(false)
				self.node_list["btn_change_text"].text.text = Language.MingWenView.InsertText2
			end
		else
			--local name_str = cfg.name
			--self.node_list["cur_rune_name"].text.text = ToColorStr(name_str,ITEM_COLOR[cfg.color])
			--self.node_list["cur_rune_lv"].text.text = ""
			self.node_list["cost"]:SetActive(false)
			self.node_list["btn_rune_upgrade"]:SetActive(false)
			--self.node_list["rune_is_max"]:SetActive(true)
			self.node_list["btn_change_text"].text.text = Language.MingWenView.InsertText2  --文字改为 替换
		end
	else     --为false  显示  镶嵌  获取等面板	
		--self.node_list["cur_rune_name"].text.text = ""
		--self.node_list["cur_rune_lv"].text.text = ""
		self.node_list["btn_change_text"].text.text = Language.MingWenView.InsertText1  --文字改为 镶嵌
		self.node_list["btn_rune_upgrade"]:SetActive(false)
		--self.node_list["btn_rune_insert"]:SetActive(false)
		self.node_list["cost"]:SetActive(false)
	end

	self.node_list["rune_upgrade_red"]:SetActive(MingWenWGData.Instance:CanBasePosyUpGrade(self.cur_select_slot_index) > 0)

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE)
	local remind = OneSwordFrostbiteWGData.Instance:GetOneSwordFrostbiteRemind()
	self.node_list["btn_open_onesword_frostbite"]:SetActive(is_open)
	self.node_list["btn_open_onesword_frostbite_remind"]:SetActive(remind == 1)
end

----刷新镶嵌列表
function MingWenView:FlushXiangQianList()
	for k, v in pairs(self.mingwen_list) do
		v:Flush()  --执行刷新逻辑
	end
end

--铭文item选中特效激活-刷新
function MingWenView:FLushXiangQianCellHL()
	if self.mingwen_list then  --如果铭文列表不为空
		for k, v in pairs(self.mingwen_list) do  --就进行遍历
			v:FlushHL(self.cur_select_index) --传入当前铭文下标
		end
	end
end

--升级成功
function MingWenView:UpGradeSuccess()
	-- 
	-- 	local bundle_name, asset_name = ResPath.GetEffectUi("UI_SJCG_F2")
	-- 	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["rune_upgrade_pos"].transform, 1)
	-- 
	--rune_upgrade_pos： 铭文升级成功 的特效 生成位置
	if self.node_list["rune_upgrade_pos"] then --不为空判断
		--播放升级成功特效
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
					is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["rune_upgrade_pos"]})
	end

	if self.mingwen_list[self.cur_select_index] then
		--self.mingwen_list[self.cur_select_index]:PlayUpLevelEffect()
	end
end

function MingWenView:OpenBLCSView()
	ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin)
end

--------------------------------------------------------------分割线-----------------------------------------------------------------------------
XQListItemRender = XQListItemRender or BaseClass(BaseRender)
function XQListItemRender:__init()
end

function XQListItemRender:__delete()
end

function XQListItemRender:LoadCallBack()

end

function XQListItemRender:OnFlush()
	if not self.data then return end

	self.node_list["text_name"].text.text = self.data.tab_name_2
	self.node_list["text_name_hl"].text.text = self.data.tab_name_2

	local is_red = MingWenWGData.Instance:GetSmallTabtRedByIndex(self.data.tab_1, self.data.tab_2)
    self.node_list.img_remind:SetActive(is_red)
end

function XQListItemRender:SetSelect(index)
	if self.index == index then
		self.node_list.image_hl:SetActive(true)
		self.node_list.image_nor:SetActive(false)
	else
		self.node_list.image_hl:SetActive(false)
		self.node_list.image_nor:SetActive(true)
	end
end