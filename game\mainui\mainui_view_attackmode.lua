ActtackModeView = ActtackModeView or BaseClass(SafeBaseView)

function ActtackModeView:__init()
	self.view_layer = UiLayer.Pop
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/main_ui_prefab", "AttackModeView")
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
end

function ActtackModeView:__delete()

end

function ActtackModeView:LoadCallBack()
	-- self.node_list["btn_pk_desc"].button:AddClickListener(BindTool.Bind(self.OpenRedNameRule, self))
	self.text_t = {
		[ATTACK_MODE.PEACE] = {node = self.node_list["SwitchPeaceMode"]},
		[ATTACK_MODE.GUILD] = {node = self.node_list["SwitchCompulsiveMode"]},
		[ATTACK_MODE.ALL] = {node = self.node_list["SwitchAllMode"]},
		[ATTACK_MODE.NAMECOLOR] = {node = self.node_list["SwitchServerMode"]},
		[ATTACK_MODE.CAMP] = {node = self.node_list["SwitchZhenYingMode"]},
		[ATTACK_MODE.AREA] = {node = self.node_list["SwitchAttackMode"]},
		[ATTACK_MODE.JIEYI] = {node = self.node_list["SwitchJieYiMode"]},
		[ATTACK_MODE.ROLE_ENEMY] = {node = self.node_list["SwitchRoleEnemyMode"]},
		[ATTACK_MODE.TEAM] = {node = self.node_list["SwitchTeamMode"]},
	}

	for k,v in pairs(self.text_t) do
		v.node.button:AddClickListener(BindTool.Bind(self.SwitchActtackeMode, self, k))
	end

	if IS_ON_CROSSSERVER and Scene.Instance:IsShowSwitchServerModeScene() then
		self.node_list["SwitchServerMode"]:SetActive(true)
	else
		self.node_list["SwitchServerMode"]:SetActive(false)
	end

	self.node_list["SwitchCompulsiveMode"]:SetActive(Scene.Instance:IsShowSwitchGuildModeScene())
	self.node_list["SwitchRoleEnemyMode"]:SetActive(SocietyWGData.Instance:GetIsHasEnemy())
	self.node_list["SwitchJieYiMode"]:SetActive(SwornWGData.Instance:HadSworn())

	local show_team_mode = false
	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	if fb_cfg then
		show_team_mode = fb_cfg.pb_zudui ~= 1
	end
	self.node_list["SwitchTeamMode"]:SetActive(show_team_mode)

	if self.enable_flag_list then
		for k,v in pairs(self.enable_flag_list) do
			self.node_list[k]:SetActive(v)
		end
	end
end

function ActtackModeView:OpenRedNameRule()
	local evil = RoleWGData.Instance:GetAttr("evil") or 0
	evil = string.format(Language.Tip.EvilValue, evil)
	RuleTip.Instance:SetContent(Language.Role.RedNameDetail, Language.Role.RedNameDetailTitle, evil)
end

--攻击模式改变	--和平
function ActtackModeView:SwitchActtackeMode(type_mode)
	if type_mode == GameEnum.ATTACK_MODE_JIEY then
		local had_sworn = SwornWGData.Instance:HadSworn()
		if not had_sworn then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YangLongSi.NoActivity)
			self:Close()
			return
		end
	end

	MainuiWGCtrl.Instance:SendSetAttackMode(type_mode)
	self:Close()
end

function ActtackModeView:SetModeBtnActive(obj_name, enable)
	if self.node_list and self.node_list[obj_name] then
		self.node_list[obj_name]:SetActive(enable)
	end

	if not self.enable_flag_list then
		self.enable_flag_list = {}
	end
	self.enable_flag_list[obj_name] = enable
end