MergeFireworksWGData = MergeFireworksWGData or BaseClass()

MergeFireworksWGData.REWARD_TYPE = {
	BIG = 1,
	NORMAL = 2,
	LOW = 3
}

function MergeFireworksWGData:__init()
	if MergeFireworksWGData.Instance then
		ErrorLog("[MergeFireworksWGData] Attemp to create a singleton twice !")
	end
	MergeFireworksWGData.Instance = self

	self.fws_cfg = ConfigManager.Instance:GetAutoConfig("combineserve_activity_yanhua_shengdian_auto")
	self.param_cfg = ListToMap(self.fws_cfg.config_param, "grade")
    self.grade_cfg = ListToMap(self.fws_cfg.grade, "grade")
    self.consume_cfg = ListToMapList(self.fws_cfg.consume, "consume")
    self.reward_cfg = ListToMap(self.fws_cfg.reward, "reward", "reward_id")
    self.rebate_cfg = ListToMap(self.fws_cfg.rebate, "rebate", "index")
    self.yanhua_cfg = ListToMap(self.fws_cfg.yanhua_show, "grade")
    self.role_sp_guarantee_cfg = ListToMapList(self.fws_cfg.role_sp_guarantee, "reward")
    RemindManager.Instance:Register(RemindName.Merge_Fireworks, BindTool.Bind(self.ShowRed, self))
    MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.MERGE_ACT_FIREWORKS, {[1] = MERGE_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetActIsOpen, self), BindTool.Bind(self.ShowRed, self))
end

function MergeFireworksWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.Merge_Fireworks)
	MergeFireworksWGData.Instance = nil

    self.first_enter_flag = nil
end

function MergeFireworksWGData:ShowRed()
    if not self:GetActIsOpen() then
        return 0
    end

    --背包有道具
    if self:GetEnoughItem() then
        return 1
    end

    --返利
    if self:GetFanliRed() then
        return 1
    end

    return 0
end

--有道具就有红点
function MergeFireworksWGData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--有能领取的返利有红点
function MergeFireworksWGData:GetFanliRed()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local draw_time = self:GetCurDrawTimes()
    for i, v in ipairs(self.rebate_cfg[rebate]) do
        if draw_time >= v.lotto_num then
            if not self:GetFanliHasGet(v.index) then
                return true
            end
        else
            return false
        end
    end
end

--获取活动是否能够开启
function MergeFireworksWGData:GetActIsOpen()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.MERGE_ACT_FIREWORKS)
    if not is_open then
        return false
    end

    local cur_cfg = self:GetCurCfg()
    if not cur_cfg then
        return false
    end

    return RoleWGData.Instance:GetRoleLevel() >= cur_cfg.open_level
end

function MergeFireworksWGData:GetCurCfg()
    return self.param_cfg[self.grade or 1]
end

function MergeFireworksWGData:GetGradeCfg()
    return self.grade_cfg[self.grade or 1]
end

function MergeFireworksWGData:GetConsumeCfg()
    local cfg = self:GetGradeCfg()
    return self.consume_cfg[cfg.consume]
end

function MergeFireworksWGData:GetYanhuaCfg()
    return self.yanhua_cfg[self.grade or 1]
end

function MergeFireworksWGData:GetRewardById(reward, reward_id)
    return self.reward_cfg[reward or 1][reward_id]
end

function MergeFireworksWGData:GetCurRewardById(reward_id)
    local grade_cfg = self:GetGradeCfg()
    local cur_reward_cfg = self.reward_cfg[grade_cfg.reward_bind]
    return cur_reward_cfg[reward_id]
end

function MergeFireworksWGData:GetRebateByIndex(index, rebate)
    if rebate == nil then
        local cfg = self:GetGradeCfg()
        rebate = cfg and cfg.rebate or 1
    end
    return self.rebate_cfg[rebate][index]
end

function MergeFireworksWGData:CheckIsFireMap(x, y)
    if not self.yanhua_pos then
        self:GetYanhuaPos()
    end
    --检测是否在指定区域
    if math.floor((self.yanhua_pos.x - x) * (self.yanhua_pos.x - x)) + math.floor((self.yanhua_pos.y - y) * (self.yanhua_pos.y - y)) <= self.yanhua_range * self.yanhua_range then
        return true
    end
    return false
end

function MergeFireworksWGData:UpdateRecordCount()
    --更新记录时间
    RoleWGData.SetRolePlayerPrefsInt("merge_fireworks_record", TimeWGCtrl.Instance:GetServerTime())
end

function MergeFireworksWGData:GetRecordInfo()
    local record_data = {}

    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            record_data[k] = {}
            record_data[k].item_data = {}
            record_data[k].item_data.item_id = v.item_id
            record_data[k].item_data.num = v.num
            record_data[k].consume_time = v.draw_time
            record_data[k].role_name = v.role_name
        end
    end

    if not IsEmptyTable(record_data) then
        table.sort(record_data, SortTools.KeyUpperSorter("consume_time"))
    end

    return record_data
end

--抽奖道具list
function MergeFireworksWGData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = MergeFireworksWGData.Instance:GetConsumeCfg()
        for i, v in pairs(cfg) do
            table.insert(self.item_data_change_list, v.yanhua_item.item_id)
        end
    end
    return self.item_data_change_list
end

--清除缓存
function MergeFireworksWGData:ClearCache()
    self.item_data_change_list = nil
    self.time_list = nil
end

--当前抽奖次数
function MergeFireworksWGData:GetCurDrawTimes()
    return self.cur_draw_times or 0
end

--当前抽奖次数
function MergeFireworksWGData:GetNextBaoDiTimes()
    return self.next_big_reward_count or 0
end

--[[
function MergeFireworksWGData:GetNextBaodiTimes()
    local cfg = self:GetGradeCfg()
    local reward = cfg.reward_unbind
    local reward_cfg = self.reward_cfg[reward or 1]
    local times_list_tb = {}
    for k, v in pairs(reward_cfg) do
        if v.player_guarantee ~= 0 then
            if not times_list_tb[v.player_guarantee] then
                times_list_tb[v.player_guarantee] = true
            end
        end
    end
    local draw_times = self:GetCurDrawTimes()
    local min = 9999
    if not IsEmptyTable(times_list_tb) then
        for k, v in pairs(times_list_tb) do
            local temp = draw_times % k
            temp = temp == 0 and k or k - temp
            if temp < min then
                min = temp
            end
        end
    end
    return min
end
]]

--返利是否领取
function MergeFireworksWGData:GetFanliHasGet(index)
    return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index - 1)) ~= 0
end

--获取返利显示的格子
function MergeFireworksWGData:GetFanliList(len)
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1

    local list = {}
    local max = #self.rebate_cfg[rebate]
    local len = len or 5
    local t = {}
    for i = 0, len-1 do
        t = {
            data = self.rebate_cfg[rebate][max - i],
            has_get = self:GetFanliHasGet(max - i) and 1 or 0
        }
        table.insert(list, 1, t)
    end

    local flag
    for i = max - len, 1, -1 do
        flag = self:GetFanliHasGet(i)
        if not flag then
            t = {
                data = self.rebate_cfg[rebate][i],
                has_get = 0
            }
            table.insert(list, 1, t)
            table.remove(list, len + 1)
        end
    end

    return list
end

--获取返利显示的格子
function MergeFireworksWGData:GetFanliListNew(len)
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1

    local list = {}

    for i, v in ipairs(self.rebate_cfg[rebate]) do
        t = {
            index = i,
            data = v,
            has_get = self:GetFanliHasGet(i) and 1 or 0
        }
        table.insert(list, t)
    end
    table.sort(list, SortTools.KeyLowerSorters("has_get", "index"))
    return list
end

function MergeFireworksWGData:SortDataList(data_list)
	local list = {}
    if data_list and not IsEmptyTable(data_list) then    
        for k,v in pairs(data_list) do
            local temp = {}
            temp.reward_item = v.reward_item
			if temp.reward_item and temp.reward_item.item_id and temp.reward_item.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(temp.reward_item.item_id)       
                temp.color = item_cfg and item_cfg.color or 1
			else
                temp.color = 0
            end
            list[#list+1] = temp
		end
		table.sort(list, SortTools.KeyUpperSorters("color"))
    end
    return list
end

--绑定奖池和非绑奖池的展示奖励
function MergeFireworksWGData:GetShowCellList()
    local list1 = {}
    local baodi_list = {}
    local grade_cfg = self:GetGradeCfg()
    local index = 0
    local cfg = self.reward_cfg[grade_cfg.reward_bind]
    for i, v in ipairs(cfg) do
        if v.reward_show == 1 then
            table.insert(list1, v)
        elseif v.reward_show == 2 or v.reward_show == 3 then
            table.insert(baodi_list, v)
        end
    end

    list1 = self:SortDataList(list1)
    baodi_list = self:SortDataList(baodi_list)
    -- cfg = self.reward_cfg[grade_cfg.reward_bind]
    -- for i, v in ipairs(cfg) do
    --     if v.reward_show == 1 then
    --         table.insert(list1, v)
    --     elseif v.reward_show == 2 then
    --         table.insert(list2, v)
    --     elseif v.reward_show == 3 then
    --         table.insert(baodi_list, v)
    --     end
    -- end

    -- cfg = self.role_sp_guarantee_cfg[grade_cfg.reward_bind]
    -- for i, v in ipairs(cfg) do
    --     if v.show_icon == 1 then
    --         table.insert(baodi_list, self.reward_cfg[v.reward][v.reward_id])
    --     end
    -- end

    -- cfg = self.role_sp_guarantee_cfg[grade_cfg.reward_unbind]
    -- for i, v in ipairs(cfg) do
    --     if v.is_show == 1 then
    --         table.insert(baodi_list, self.reward_cfg[v.reward][v.reward_id])
    --     end
    -- end

    return list1, baodi_list
end

--获取自选奖励列表
function MergeFireworksWGData:GetSelectBaoDiList()
    local baodi_list = {}
    local grade_cfg = self:GetGradeCfg()
    local cfg = self.reward_cfg[grade_cfg.reward_bind]
    for i, v in ipairs(cfg) do
        if v.reward_show == 1 then --大奖
            table.insert(baodi_list, v)
        end
    end
    return baodi_list
end

function MergeFireworksWGData:GetShowRewardPerviewList()
    local reawrd_list = {}
    local grade_cfg = self:GetGradeCfg()
    local cfg = self.reward_cfg[grade_cfg.reward_bind]

    local title_reward_item_data = {}
    for i = 1, 2 do
        title_reward_item_data[i] = {}
        title_reward_item_data[i].title_text = Language.MergeFireworks.RewardPreviewTitle[i]
        title_reward_item_data[i].reward_item_list = {}
    end

    --玩家已选择保底奖励，显示选择奖励.
    if self:CheckIsSelectedBigReward() then
        for k, item_cfg in pairs(self.reward_id_list) do
            local select_reward_id = item_cfg
            if select_reward_id ~= 0 then
                local cfg = self:GetCurRewardById(select_reward_id)
                if cfg then
                    table.insert(title_reward_item_data[1].reward_item_list, cfg.reward_item)
                end
            end
        end

        for i, v in ipairs(cfg) do
            if v.reward_type ~= 1 then
                table.insert(title_reward_item_data[2].reward_item_list, v.reward_item)
            end
        end
    else--玩家未选择保底奖励，显示所有奖励.
        for i, v in ipairs(cfg) do
            if v.reward_type == 1 then
                table.insert(title_reward_item_data[1].reward_item_list, v.reward_item)
            else
                table.insert(title_reward_item_data[2].reward_item_list, v.reward_item)
            end
        end
    end

    return title_reward_item_data
end

--模型大奖
function MergeFireworksWGData:GetBigReward()
    --大奖配置，other or 遍历
    local grade_cfg = self:GetGradeCfg()
    local reward = Split(grade_cfg.model_item, "|")
    reward[1] = tonumber(reward[1])
    reward[2] = tonumber(reward[2] or OARenderType.RoleModel)
    return reward
end

--是否跳过动画对应的延时
function MergeFireworksWGData:GetDelayTime()
    --是否跳过动画
    if self.is_skip_comic == 1 then
        return 0
    else
        local cache_index = self:CacheOrGetDrawIndex()
        return UITween_CONSTS.MergeFws.EffectDelay[cache_index] or 0
    end
end

--自选奖励信息
function MergeFireworksWGData:SetSelectedRewardList(protocol)
    self.reward_id_list = protocol.reward_id_list
end

--自选奖励信息
function MergeFireworksWGData:GetSelectedRewardList()
    return self.reward_id_list or {}
end

--玩家是否已选择自选奖励.
function MergeFireworksWGData:CheckIsSelectedBigReward()
    local is_select = false
    local list = self:GetSelectedRewardList()
    for k, v in pairs(list) do
        if v ~= 0 then
            is_select = true
            break
        end
    end

    return is_select
end

--日志协议
function MergeFireworksWGData:SetRecord(record_list)
    self.record_list = record_list
    self.new_record_time = self.record_list[1] and self.record_list[1].draw_time or 0
end

--获取最新日志的时间
function MergeFireworksWGData:GetRecordTime()
    return self.new_record_time or 1
end

--协议信息
function MergeFireworksWGData:SetInfo(protocol)
    if self.grade ~= protocol.grade then
        self:ClearCache()
    end
    self.grade = protocol.grade
    self.cur_draw_times = protocol.person_draw_count
    self.fetch_flag = protocol.leiji_reward_fetch_flag
    self.is_skip_comic = protocol.is_skip_comic			--跳过动画？
	self.sp_guarantee_x = protocol.sp_guarantee_x		--特殊保底次数？ 弃用
	self.sp_guarantee_n = protocol.sp_guarantee_n		--特殊保底轮数？
	self.sp_enter_num = protocol.sp_enter_num			--进入保底库次数？

    self.gather_small_count = protocol.gather_small_count
    self.gather_big_count = protocol.gather_big_count
    self.first_enter_flag = protocol.first_enter_flag		    --第一次进入标记
    self.next_big_reward_count = protocol.next_big_reward_count --下次大奖次数
end

--是否第一次进入
function MergeFireworksWGData:GetIsFirstEnter()
    return self.first_enter_flag
end

--是否第一次进入(手动设置防止后端协议更新不及时)
function MergeFireworksWGData:SetIsFirstEnter()
    self.first_enter_flag = 1
end

--是否跳过动画
function MergeFireworksWGData:GetSkipAnim()
    return self.is_skip_comic
end
--获取保底显示
function MergeFireworksWGData:GetSpGuarantee()
    return self.sp_guarantee_x or 0, self.sp_guarantee_n or 0, self.sp_enter_num or 0
end

-- 获取保底次数
function MergeFireworksWGData:GetGuaranteeListCount(reward)
	if not reward then
		return 0
	end

	local num = self.role_sp_guarantee_cfg[reward] and #self.role_sp_guarantee_cfg[reward] or 0
	return num
end

--烟花寻路位置，如果有改变可以在clearcache清除缓存
function MergeFireworksWGData:GetYanhuaPos()
    local cfg = self:GetYanhuaCfg()
    local pos = Split(cfg.yanhua_show_pos, ",")
    self.yanhua_pos = Vector2(tonumber(pos[1]), tonumber(pos[2]))
    self.yanhua_range = cfg.yanhua_show_range
end

--获取烟花汇演的时间配置
function MergeFireworksWGData:GetYanHuaTimeCfg()
    if not self.time_list then
        local list = {}
        local cfg = self:GetYanhuaCfg()
        cfg = Split(cfg.yanhua_show_time, "|")
        for i, v in ipairs(cfg) do
            table.insert(list, v)
        end
        self.time_list = list
    end
    return self.time_list
end

--获取烟花汇演scene_id
function MergeFireworksWGData:GetYanhuaSceneId()
    return self:GetYanhuaCfg().scene_id
end

--获取采集次数
function MergeFireworksWGData:GetGatherTimes()
    return self.gather_small_count, self.gather_big_count
end

--os.date的time_t和XX:XX比较时间差
function MergeFireworksWGData:GetDifTime(now_time, str_time)
    str_time = Split(str_time, ":")
    for i, v in pairs(str_time) do
        v = tonumber(v)
    end

    --在汇演时间（str_time）到汇演时间+duration期间弹窗，否则倒计时
    local hour = str_time[1] - now_time.hour
    local min = str_time[2] - now_time.min
    local sec = 0 - now_time.sec
    return hour * 3600 + min * 60 + sec
end

--[[
--是否汇演地图
function MergeFireworksWGData:CheckPerformArea()
    if Scene.Instance:GetSceneId() ~= self:GetYanhuaCfg().scene_id then
        return false
    end

    --汇演拿不到入侵服的活动信息，因此直接当作安全区处理
    --local time_list = self:GetYanHuaTimeCfg()
    --local time_t = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
    --for i, v in pairs(time_list) do
    --    if self:GetDifTime(time_t, v) < yanhua_cfg.yanhua_show_duration then
    --        return true
    --    end
    --end
    return true
end

--汇演期间不会被拉去打坐，在upate里执行，看看能否优化
function MergeFireworksWGData:IsPerformTimeForUpdate()
    if not self:GetActIsOpen() then
        self.time_t = nil
        return false
    end

    local yanhua_cfg = self:GetYanhuaCfg()
    if Scene.Instance:GetSceneId() ~= yanhua_cfg.scene_id then
        self.time_t = nil
        return false
    end

    if not MergeFireworksWGCtrl.Instance:GetIsFireMap() then
        self.time_t = nil
        return false
    end

    local time_list = self:GetYanHuaTimeCfg()
    if not self.time_t then
        self.time_t = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
    end
    for i, v in pairs(time_list) do
        if self:GetDifTime(self.time_t, v) < yanhua_cfg.yanhua_show_duration then
            return true
        end
    end
    return false
end
]]

--获取奖励对应的配置
function MergeFireworksWGData:CalDrawRewardList(protocol)
    local data_list = {}
    local item_ids = {}
    if not protocol or not protocol.reward_count or protocol.reward_count <= 0 then
		return data_list, item_ids
	end

    local zhenxi_item = nil
    for i, v in ipairs(protocol.reward_list) do
        local cfg = self:GetRewardById(v.reward_pool_id, v.reward_id)
        if cfg then
            local temp = {}
            temp.reward = cfg.reward
            temp.reward_item = cfg.reward_item
            temp.reward_type = cfg.reward_type
            temp.rewrad_rare_show = cfg.rewrad_rare_show
            if cfg.reward_type == 1 then
                table.insert(item_ids, temp.reward_item.item_id)
            else
                table.insert(data_list, temp.reward_item)
            end
        else
            print_error("错误数据, reward_pool_id:", v.reward_pool_id, "reward_id", v.reward_id)
        end
    end
    return data_list, item_ids
end

--获取抽奖的选项
function MergeFireworksWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end


function MergeFireworksWGData:GetItemsProbility()
    local str, temp_str = "", ""
    local item_cfg
    local item_list = self:GetDataList()
    if not IsEmptyTable(item_list) then
        for k, v in pairs(item_list) do
            item_cfg = ItemWGData.Instance:GetItemConfig(v.reward_item.item_id)
            if item_cfg then
                temp_str = ToColorStr(item_cfg.name..": "..v.rewrad_rare_show * 100 .."%", ITEM_COLOR[item_cfg.color])
                str = str.. "\n" ..temp_str
            end
        end
    end
    return str
end

function MergeFireworksWGData:GetDataList(grade_cfg)
	local data_list = {}
	grade_cfg = grade_cfg or self:GetGradeCfg()
	if not grade_cfg then
		return data_list
	end

	local cfg = self.reward_cfg[grade_cfg.reward_bind] or {}
	for i, v in pairs(cfg) do
		if v.reward_show > 0 then
			table.insert(data_list, v)
		end
    end
    table.sort(data_list, function(a, b)
        if a.rewrad_rare_show ~= b.rewrad_rare_show then
            return a.rewrad_rare_show < b.rewrad_rare_show
        end
        local item_cfg1, item_cfg2
        if a and a.reward_item and a.reward_item.item_id then
            item_cfg1 = ItemWGData.Instance:GetItemConfig(a.reward_item.item_id)
        end
        if b and b.reward_item and b.reward_item.item_id then
            item_cfg2 = ItemWGData.Instance:GetItemConfig(b.reward_item.item_id)
        end
        local color1 = item_cfg1 and item_cfg1.color or 0
        local color2 = item_cfg2 and item_cfg2.color or 0
        return color1 > color2
    end)
	return data_list
end