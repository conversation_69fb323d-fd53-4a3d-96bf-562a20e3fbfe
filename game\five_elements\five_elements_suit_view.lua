FiveElementsSuitView = FiveElementsSuitView or BaseClass(SafeBaseView)

function FiveElementsSuitView:__init()
    self.is_safe_area_adapter = true
	self.view_style = ViewStyle.Full
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_suit")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
end

function FiveElementsSuitView:LoadCallBack()
	self.select_suit_cell_data = {}

	if not self.suit_attr_list then
		self.suit_attr_list = AsyncListView.New(SuitArrtItemRender, self.node_list.suit_attr_list)
		self.suit_attr_list:SetStartZeroIndex(false)
	end

	if not self.suit_cell_list then
		self.suit_cell_list = {}

		for i = 0, 4 do
			self.node_list["tog_item" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSuitSelect, self, i))
			self.suit_cell_list[i] = {}

			for j = 0, 6 do
				local render = FiveElementsSuitCellRender.New(self.node_list["suit_panel" .. i]:FindObj("five_item_render" .. j))
				render:SetIndex(j)
				render:SetClickCallBack(BindTool.Bind(self.OnClickSuitCell, self))
				self.suit_cell_list[i][j] = render
			end
		end
	end

	self.big_type = -1
	self.small_type = -1

	self.node_list.title_view_name.text.text = Language.FiveElements.SuitPanelTitie
	XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActiveBtn, self))
end

function FiveElementsSuitView:ReleaseCallBack()
	if self.suit_attr_list then
		self.suit_attr_list:DeleteMe()
		self.suit_attr_list = nil
	end

	self.big_type = nil
	self.small_type = nil
	self.select_suit_cell_data = nil

	if self.suit_cell_list then
		for k, v in pairs(self.suit_cell_list) do
			for i, j in pairs(v) do
				j:DeleteMe()
			end
		end

		self.suit_cell_list = nil
	end
end

function FiveElementsSuitView:OnFlush()
	self:FlushSuitTypeRemind()
	self:SetNavbarSelect()
	self:FlushAttr()
end

function FiveElementsSuitView:FlushSuitTypeRemind()
	local data_list = FiveElementsWGData.Instance:GetSuitInfo()
	 
	for i = 0, 4 do
		self.node_list["tog_item_remind" .. i]:SetActive(data_list[i].remind)
	end
end

function FiveElementsSuitView:SetNavbarSelect()
	self.big_type = FiveElementsWGData.Instance:GetSuitDefauitSelectBigType(self.big_type)

	if not self.node_list["tog_item" .. self.big_type].toggle.isOn then
		self.node_list["tog_item" .. self.big_type].toggle.isOn = true
	else
		self:OnClickSuitSelect(self.big_type, true)
	end
end

function FiveElementsSuitView:OnClickSuitSelect(index, isOn)
    if index == nil or not isOn then
		return
	end

	self.big_type = index
	local small_type = FiveElementsWGData.Instance:GetSuitDefauitSelectSmallType(self.big_type, self.small_type)
	local data_list = FiveElementsWGData.Instance:GetSuitInfo()

	-- local bundle, asset = ResPath.GetRawImagesPNG("a1_wx_ct" .. index)
	-- self.node_list.suit_bg.raw_image:LoadSprite(bundle, asset, function()
    --     self.node_list.suit_bg.raw_image:SetNativeSize()
    -- end)
	
	for j = 0, 6 do
		local data = (data_list[self.big_type] or {})[j + 1] or {}
		local cell_render = (self.suit_cell_list[self.big_type] or {})[j]

		if cell_render then
			cell_render:SetData(data)
		end
	end

	local cell_render = (self.suit_cell_list[self.big_type] or {})[small_type]

	if cell_render then
		self:OnClickSuitCell(cell_render)
	end
end

function FiveElementsSuitView:OnClickSuitCell(item)
	if nil == item or nil == item.data then
		return
	end

	self.small_type = item.index
	self.select_suit_cell_data = item.data
	for j = 0, 6 do
		local cell_render = (self.suit_cell_list[self.big_type] or {})[j]

		if cell_render then
			cell_render:OnSelectChange(j == self.small_type)
		end
	end

	self:FlushAttr()
end

function FiveElementsSuitView:FlushAttr()
	local data_list = FiveElementsWGData.Instance:GetSuitInfo()
	local data = (data_list[self.big_type] or {})[self.small_type + 1] or {}

	if IsEmptyTable(data) then
		return
	end

	self.suit_attr_list:SetDataList(data)
	local state = false

	for k, v in pairs(data) do
		if type(v) == "table" and v.can_active and not v.is_active then
			state = true
			break
		end
	end

	XUI.SetButtonEnabled(self.node_list.btn_active, state)
	self.node_list.btn_active_remind:SetActive(state)
end

function FiveElementsSuitView:OnClickActiveBtn()
	if IsEmptyTable(self.select_suit_cell_data) then
		return
	end

	local can_active_seq = -1
	for k, v in pairs(self.select_suit_cell_data) do
		if type(v) == "table" and v.can_active and not v.is_active then
			can_active_seq = v.seq
			break
		end
	end

	if can_active_seq >= 0 then
		FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_HOLE_SUIT_ACTIVE, self.big_type, can_active_seq)
	end
end

---------------------------------------------套装属性------------------------------------------------
SuitArrtItemRender = SuitArrtItemRender or  BaseClass(BaseRender)

function SuitArrtItemRender:LoadCallBack()
	if not self.attr_list then
		self.attr_list = AsyncListView.New(SuitArrtItemCellRender, self.node_list.attr_list)
		self.attr_list:SetStartZeroIndex(false)
	end
end

function SuitArrtItemRender:ReleaseCallBack()
	if self.attr_list then
		self.attr_list:DeleteMe()
		self.attr_list = nil
	end
end

function SuitArrtItemRender:OnFlush()
	if not self.data then
		return 
	end

	local data = self.data
	local color = data.is_active and COLOR3B.WHITE or COLOR3B.GRAY
	self.node_list.suit_num.text.text = string.format(Language.FiveElements.SuitNum, color, data.need_num)
	local item_data_list = {}

	local attr_data_info = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(data.attr_data, "attr_id", "attr_value")
	for k, v in pairs(attr_data_info) do
		local new_data = {
			attr_name = v.attr_name,
			attr_value = v.value_str,
			is_active = data.is_active,
		}

		table.insert(item_data_list, new_data)
	end

	self.attr_list:SetDataList(item_data_list)
end

--------------------------------------------------SuitArrtItemCellRender----------------------------
SuitArrtItemCellRender = SuitArrtItemCellRender or BaseClass(BaseRender)

function SuitArrtItemCellRender:OnFlush()
	if not self.data then
		return 
	end

	local data = self.data
	self.node_list.attr_item_name.text.text = ToColorStr(data.attr_name, data.is_active and COLOR3B.WHITE or COLOR3B.GRAY)
	self.node_list.attr_item_value.text.text = ToColorStr(data.attr_value, data.is_active and COLOR3B.D_GREEN or COLOR3B.GRAY)
end

----------------------------------FiveElementsSuitCellRender-------------------------------
FiveElementsSuitCellRender = FiveElementsSuitCellRender or BaseClass(BaseRender)

function FiveElementsSuitCellRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.level.text.text = self.data.name or ""
	self.node_list.remind:CustomSetActive(self.data.remind)

	local active = false
	for i = 1, 2 do
		local data = self.data[i]
		if data and data.is_active then
			active = true
			break
		end
	end

	XUI.SetGraphicGrey(self.node_list.bg, not active)
end

function FiveElementsSuitCellRender:OnSelectChange(is_select)
	self.node_list.select:CustomSetActive(is_select)
end