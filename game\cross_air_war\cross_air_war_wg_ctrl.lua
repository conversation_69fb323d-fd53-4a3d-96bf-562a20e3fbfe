
require("game/cross_air_war/cross_air_war_wg_data")
require("game/cross_air_war/cross_air_war_view")
require("game/cross_air_war/cross_air_war_reward_view")
require("game/cross_air_war/cross_air_war_vote_view")
require("game/cross_air_war/cross_air_war_rule_view")
require("game/cross_air_war/cross_air_war_prog_view")
require("game/cross_air_war/cross_air_war_scene_view")
require("game/cross_air_war/cross_air_war_auction_view")
require("game/cross_air_war/cross_air_war_auction_bag_view")
require("game/cross_air_war/cross_air_war_result_view")
require("game/cross_air_war/cross_air_pop_dialog_view")
require("game/cross_air_war/cross_air_war_gather_tips")

CrossAirWarWGCtrl = CrossAirWarWGCtrl or BaseClass(BaseWGCtrl)

-- 跨服空战操作类型
AIR_WAR_OPERATE_TYPE =
{
	TYPE_MONSTER_PERFORM_SKILL			= 1,	--// 怪物释放技能 param1:skill_id
	TYPE_MONSTER_REFRESH 				= 2,	--// 怪物刷新		param1:monster_seq
	TYPE_MONSTER_RANK_REWARD 			= 3,	--// 怪物排名奖励	param1:monster_seq param2:rank
	TYPE_ADD_SCORE 						= 4,	--// 获得积分		param1:add_score
	TYPE_AUCTION_BARRAGE_SUCC = 5,				--// 拍卖弹幕		param1:auction_seq param2:price
	TYPE_AUCTION_BARRAGE_FAIL = 6,				--// 拍卖弹幕		param1:auction_seq
}

function CrossAirWarWGCtrl:__init()
	if CrossAirWarWGCtrl.Instance ~= nil then
		ErrorLog("[CrossAirWarWGCtrl] attempt to create singleton twice!")
		return
	end
	CrossAirWarWGCtrl.Instance = self

	self.data = CrossAirWarWGData.New()
	self.reward_view = CrossAirWarRewardView.New()
	self.boss_vote_view = CrossAirWarVoteView.New()
	self.boss_prog_view = CrossAirWarProgView.New()
	self.air_war_scene_view = CrossAirWarSceneView.New()
	self.air_war_auction_view = CrossAirWarAuctionView.New()
	self.air_war_rule_view = CrossAirWarRuleView.New()
	self.air_war_result_view = CrossAirWarResultView.New()
	self.air_war_auction_bag_view = CrossAirWarAuctionBagView.New()
	self.air_war_pop_dialog_view = CrossAirPopDialogView.New(GuideModuleName.CrossAirPopDialogView)
	self.cross_air_war_gather_tips = CrossAirWarGatherTips.New()

	self:RegisterAllProtocols()
end

function CrossAirWarWGCtrl:__delete()
	CrossAirWarWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.reward_view then
		self.reward_view:DeleteMe()
		self.reward_view = nil
	end

	if self.boss_vote_view then
		self.boss_vote_view:DeleteMe()
		self.boss_vote_view = nil
	end

	if self.boss_prog_view then
		self.boss_prog_view:DeleteMe()
		self.boss_prog_view = nil
	end

	if self.air_war_scene_view then
		self.air_war_scene_view:DeleteMe()
		self.air_war_scene_view = nil
	end

	if self.air_war_auction_view then
		self.air_war_auction_view:DeleteMe()
		self.air_war_auction_view = nil
	end

	if self.air_war_pop_dialog_view then
		self.air_war_pop_dialog_view:DeleteMe()
		self.air_war_pop_dialog_view = nil
	end

	if self.air_war_rule_view then
		self.air_war_rule_view:DeleteMe()
		self.air_war_rule_view = nil
	end

	if self.air_war_result_view then
		self.air_war_result_view:DeleteMe()
		self.air_war_result_view = nil
	end

	if self.air_war_auction_bag_view then
		self.air_war_auction_bag_view:DeleteMe()
		self.air_war_auction_bag_view = nil
	end

	if self.cross_air_war_gather_tips then
		self.cross_air_war_gather_tips:DeleteMe()
		self.cross_air_war_gather_tips = nil
	end
end

function CrossAirWarWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossAirWarOperate)
	self:RegisterProtocol(SCCrossAirWarBaseInfo, "OnSCCrossAirWarBaseInfo")
	self:RegisterProtocol(SCCrossAirWarAuctionInfo, "OnSCCrossAirWarAuctionInfo")
	self:RegisterProtocol(SCCrossAirWarRoleBaseInfo, "OnSCCrossAirWarRoleBaseInfo")
	self:RegisterProtocol(SCCrossAirWarSceneInfo, "OnSCCrossAirWarSceneInfo")
	self:RegisterProtocol(SCCrossAirWarActivityStatus, "OnSCCrossAirWarActivityStatus")
	self:RegisterProtocol(SCCrossAirWarOperateResult, "OnSCCrossAirWarOperateResult")
	self:RegisterProtocol(SCCrossAirWarAuctionItemInfo, "OnSCCrossAirWarAuctionItemInfo")
	self:RegisterProtocol(SCCrossAirWarAuctionItemUpdate, "OnSCCrossAirWarAuctionItemUpdate")
end

function CrossAirWarWGCtrl:SendCSCrossAirWarOperate(operate_type, param1, param2, param3)
	-- print_error("请求活动信息", operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossAirWarOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 切换人仙魔变身
function CrossAirWarWGCtrl:SendCSCrossAirWarOperateChangeImage(index)
	local image_index = index - 1
	self:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.CHANGE_IMAGE, image_index)
end

-- 拍卖出价
function CrossAirWarWGCtrl:SendCSCrossAirWarOperateAuction(index, price)
	local server_index = index - 1
	self:SendCSCrossAirWarOperate(CROSS_AIR_WAR_OPERATE_TYPE.AUCTION, server_index, price)
end
------------------------------------------------------------------------------
------------------------------------------------------------------------------
--- 场景基础信息
function CrossAirWarWGCtrl:OnSCCrossAirWarBaseInfo(protocol)
	-- print_error("场景基础信息", protocol)
	self.data:SetAirWarBaseInfo(protocol)
	self:OnSCCrossAirWarMsgChange()
	self:FlushTaskView()

	-- 刷新主角面评分
	MainuiWGCtrl.Instance:FlushView(0, "mainui_kfkz_score_status")
end

-- 检测信息改变
function CrossAirWarWGCtrl:OnSCCrossAirWarMsgChange()
	local logic = Scene.Instance:GetSceneLogic()
	if logic ~= nil and logic.CheckSceneAirWall ~= nil then
		logic:CheckSceneAirWall()
	end

	if logic ~= nil and logic.CheckGoToMosterPos ~= nil then
		logic:CheckGoToMosterPos()
	end

	if logic ~= nil and logic.CheckSceneStaticObjActive ~= nil then
		logic:CheckSceneStaticObjActive()
	end
end

-- 最终boss拍卖阶段
function CrossAirWarWGCtrl:OnSCCrossAirWarAuctionInfo(protocol)
	-- print_error("拍卖信息", protocol)
	self.data:SetAirWarAuctionInfo(protocol)
	local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	if status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION then
		self:OpenAirWarAuctionView()
	end
end

-- 玩家信息
function CrossAirWarWGCtrl:OnSCCrossAirWarRoleBaseInfo(protocol)
	-- print_error("玩家信息", protocol)
	self.data:SetAirWarRoleBaseInfo(protocol)
end

-- 跨服空战状态信息
function CrossAirWarWGCtrl:OnSCCrossAirWarSceneInfo(protocol)
	-- print_error("状态发生改变", protocol)
	self.data:SetAirWarSceneInfo(protocol)
	local logic = Scene.Instance:GetSceneLogic()

	if protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_MONSTER then
		self:FlushTaskView(0, "flush_aution_status")
	elseif protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_START then
		self.air_war_scene_view:SetWaitGameStartStatus()
		self:FlushTaskView(0, "flush_wait_start_message")
	elseif protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_MONSTER then
		local moster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
		if logic ~= nil and logic.CheckHaveBossEntrance ~= nil and logic:CheckHaveBossEntrance(moster_seq) then
			local guide_list = self.data:GetGuideListCfgByTriggerParam(CROSS_AIR_WAR_GUIDE_TYPE.GUIDE_TYPE_BOSS_ENTRANCE, moster_seq)
			if guide_list ~= nil then
				ViewManager.Instance:Open(GuideModuleName.CrossAirPopDialogView, nil, "all", {data = guide_list})  -- 功能引导表
			end
		end
	elseif protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_END then
		self:CloseAirWarAuctionView()
		self:FlushTaskView(0, "flush_end_fb")
	elseif protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_GATHER then
		self:FlushTaskView(0, "flush_box_time_message")
	elseif protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION then
		self:OpenAirWarAuctionView()
	elseif protocol.m_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_MONSTER_END then
		self:OpenAirWarResultView()	
	end

	self:OnSCCrossAirWarMsgChange()
	if protocol.m_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_START and protocol.m_status ~= CROSS_AIR_WAR_AUCTION_STATUS.STATUS_END then
		self:FlushTaskView(0, "flush_status_tips")
	end

	local moster_seq, is_wait = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	if logic ~= nil and logic.CheckChangeWaitMosterSeq ~= nil and logic:CheckChangeWaitMosterSeq(moster_seq) and is_wait then
		local tips_stage = protocol.stage + 1
		local guide_list = self.data:GetGuideListCfgByTriggerParam(CROSS_AIR_WAR_GUIDE_TYPE.GUIDE_TYPE_CHANGE_STAGE, tips_stage)

		if guide_list ~= nil then
			ViewManager.Instance:Open(GuideModuleName.CrossAirPopDialogView, nil, "all", {data = guide_list})  -- 功能引导表
		end
	end

	-- 刷新拍卖
	MainuiWGCtrl.Instance:FlushView(0, "mainui_kfkz_paimai_status")
end

--更新活动状态（外部广播）
function CrossAirWarWGCtrl:OnSCCrossAirWarActivityStatus(protocol)
	-- print_error("更新活动状态（外部广播）", protocol)
	self.data:SetAirWarActivityStatus(protocol)
end

-- 更新操作结果
function CrossAirWarWGCtrl:OnSCCrossAirWarOperateResult(protocol)
	-- print_error("更新操作结果", protocol)
	if protocol.operate_type == AIR_WAR_OPERATE_TYPE.TYPE_MONSTER_PERFORM_SKILL then
		local guide_list = self.data:GetGuideListCfgByTriggerParam(CROSS_AIR_WAR_GUIDE_TYPE.GUIDE_TYPE_BOSS_SKILL, protocol.param1)
		if guide_list ~= nil then
			ViewManager.Instance:Open(GuideModuleName.CrossAirPopDialogView, nil, "all", {data = guide_list})  -- 功能引导表
		end
	elseif protocol.operate_type == AIR_WAR_OPERATE_TYPE.TYPE_MONSTER_REFRESH then
		local logic = Scene.Instance:GetSceneLogic()
		if logic ~= nil and logic.CheckHaveBossShowBySeq ~= nil then
			logic:CheckHaveBossShowBySeq(protocol.param1)
		end
	elseif protocol.operate_type == AIR_WAR_OPERATE_TYPE.TYPE_ADD_SCORE then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.EternalNight.ScoreStr2, protocol.param1))
	elseif protocol.operate_type == AIR_WAR_OPERATE_TYPE.TYPE_AUCTION_BARRAGE_SUCC then--// 拍卖弹幕		param1:auction_seq param2:price
		self:AddAutionBarrage(true, {round = protocol.param1, seq = protocol.param2})
	elseif protocol.operate_type == AIR_WAR_OPERATE_TYPE.TYPE_AUCTION_BARRAGE_FAIL then--// 拍卖弹幕		param1:auction_seq
		self:AddAutionBarrage(false, {auction_seq = protocol.param1})
	end
end

-- 拍卖物信息
function CrossAirWarWGCtrl:OnSCCrossAirWarAuctionItemInfo(protocol)
	-- print_error("拍卖物信息", protocol)
	self.data:SetAuctionItemInfo(protocol)
	self:FlushAirWarAuctionView()
end

-- 拍卖物更新
function CrossAirWarWGCtrl:OnSCCrossAirWarAuctionItemUpdate(protocol)
	-- print_error("拍卖物更新", protocol)
	self.data:SetSingleAuctionItemInfo(protocol.index, protocol.auction_info)
	self:FlushAirWarAuctionView()
end
------------------------------------------------------------------------------
-- 打开场景任务面板
function CrossAirWarWGCtrl:OpenTaskView()
	if not self.air_war_scene_view:IsOpen() then
		self.air_war_scene_view:Open()
	else
		self.air_war_scene_view:Flush()
	end
end

-- 刷新场景任务面板
function CrossAirWarWGCtrl:FlushTaskView(index, key)
	if self.air_war_scene_view:IsOpen() then
		self.air_war_scene_view:Flush(index, key)
	end
end

-- 关闭场景任务面板
function CrossAirWarWGCtrl:CloseTaskView()
	if self.air_war_scene_view:IsOpen() then
		self.air_war_scene_view:Close()
	end
end

-- 打开拍卖
function CrossAirWarWGCtrl:OpenAirWarAuctionView()
	if not self.air_war_auction_view:IsOpen() then
		self.air_war_auction_view:Open()
	else
		self.air_war_auction_view:Flush()
	end
end

-- 刷新拍卖
function CrossAirWarWGCtrl:FlushAirWarAuctionView(index, key, value)
	if self.air_war_auction_view:IsOpen() then
		self.air_war_auction_view:Flush(index, key, value)
	end
end

-- 刷新拍卖
function CrossAirWarWGCtrl:AddAutionBarrage(is_success, value)
	if self.air_war_auction_view:IsOpen() then
		if is_success then
			self.air_war_auction_view:AddSucAutionBarrage(value)
		else
			self.air_war_auction_view:AddFailAutionBarrage(value)
		end
	end
end


-- 关闭拍卖
function CrossAirWarWGCtrl:CloseAirWarAuctionView()
	if self.air_war_auction_view:IsOpen() then
		self.air_war_auction_view:Close()
	end
end

-- 打开竞拍仓库
function CrossAirWarWGCtrl:OpenAirWarAuctionBagView()

end

-- 打开投票
function CrossAirWarWGCtrl:OpenBossVoteView()
	if not self.boss_vote_view:IsOpen() then
		self.boss_vote_view:Open()
	else
		self.boss_vote_view:Flush()
	end
end

function CrossAirWarWGCtrl:OpenRewardPreview(is_show_person_score)
	self.reward_view:SetOpenData(is_show_person_score)
	if self.reward_view then
		self.reward_view:Open()
	end
end

function CrossAirWarWGCtrl:OpenAirWarProgView()
	if self.boss_prog_view then
		self.boss_prog_view:Open()
	end
end


function CrossAirWarWGCtrl:OpenAirWarSceneView()
	if self.air_war_scene_view then
		self.air_war_scene_view:Open()
	end
end

-- 打开规则
function CrossAirWarWGCtrl:OpenAirWarRuleView()
	if not self.air_war_rule_view:IsOpen() then
		self.air_war_rule_view:Open()
	else
		self.air_war_rule_view:Flush()
	end
end

-- 打开结算结果
function CrossAirWarWGCtrl:OpenAirWarResultView()
	if not self.air_war_result_view:IsOpen() then
		self.air_war_result_view:Open()
	else
		self.air_war_result_view:Flush()
	end
end

-- 打开竞拍背包
function CrossAirWarWGCtrl:OpenAirWarAuctionBagView()
	if not self.air_war_auction_bag_view:IsOpen() then
		self.air_war_auction_bag_view:Open()
	else
		self.air_war_auction_bag_view:Flush()
	end
end

-- 打开竞拍背包
function CrossAirWarWGCtrl:OpenAirWarGatherTips(open_data, call_back)
	self.cross_air_war_gather_tips:SetData(open_data, call_back)
end