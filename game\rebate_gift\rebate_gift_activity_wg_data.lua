RebateGiftActivityWGData = RebateGiftActivityWGData or BaseClass()

RebateGiftActivityWGData.REWARD_TYPE = {
	BIG = 1,
	NORMAL = 2,
	LOW = 3
}

RebateGiftActivityWGData.SKILL_SHOW_TYPE = {
	MOUNT = 1,
	DIYSKILL = 2,
}

function RebateGiftActivityWGData:__init()
	if RebateGiftActivityWGData.Instance ~= nil then
		print_error("[RebateGiftActivityWGData] attempt to create singleton twice!")
		return
	end
	RebateGiftActivityWGData.Instance = self

	--烟花抽奖
    local fireworks_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_fireworks_draw_auto")
	self.fireworks_mode_cfg = fireworks_cfg.mode --抽奖模式
	self.fireworks_reward_pool_cfg = ListToMapList(fireworks_cfg.reward_pool, "grade", "activity_day") --奖池
	self.fireworsk_gailv_cfg = ListToMapList(fireworks_cfg.item_random_desc, "grade", "activity_day") --概率展示
	self.fireworks_baodi_cfg = ListToMapList(fireworks_cfg.baodi, "grade", "activity_day") --保底奖池
	self.fireworks_other_cfg = fireworks_cfg.other --抽奖道具

	--充值立减
	local discount_cfg =  ConfigManager.Instance:GetAutoConfig("operation_activity_recharge_discounts_auto") 
	self.discount_gift_cfg = ListToMapList(discount_cfg.gift, "grade", "activity_day")

	--绝版赠礼
	local extinct_gift_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_extinct_gift_auto")
	self.main_task_cfg = ListToMapList(extinct_gift_cfg.maintask, "grade")					-- 主任务
	self.gift_other_cfg = ListToMap(extinct_gift_cfg.other, "grade")						-- 其他
	self.sub_task_cfg = ListToMapList(extinct_gift_cfg.subtask, "grade", "activity_day")	-- 子任务
	self.sub_task_reward = ListToMapList(extinct_gift_cfg.grade, "grade", "activity_day")	-- 子任务模型
	self.open_day_cfg = extinct_gift_cfg.open_day --开服天数（拿主任务模型展示）

	self.discount_info = {}
	self.fireworks_result_info = {}
	self.extinct_gift_info = {}
	self.cur_main_task_seq = 0 --绝版赠礼今日的主任务类型
	self.select_title_attr_lits = {}

	if self.gift_other_cfg[1] and self.gift_other_cfg[1].select_title_attr_lits then
		self.select_title_attr_lits = Split(self.gift_other_cfg[1].select_title_attr_lits, ",")
	end

	RemindManager.Instance:Register(RemindName.RebateFireWorks, BindTool.Bind(self.GetFireWorksDrawRed, self)) --烟花抽奖
	RemindManager.Instance:Register(RemindName.RebateExtinctGift, BindTool.Bind(self.GetExtinctGiftRed, self)) --绝版赠礼
	RemindManager.Instance:Register(RemindName.RebateDiscount, BindTool.Bind(self.GetDiscountRed, self)) --充值立减

	--背包道具红点 -烟花抽奖
    self:RegisterFireworksRemindInBag(RemindName.RebateFireWorks)
end

function RebateGiftActivityWGData:__delete()
	RebateGiftActivityWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.RebateFireWorks)
	RemindManager.Instance:UnRegister(RemindName.RebateExtinctGift)
	RemindManager.Instance:UnRegister(RemindName.RebateDiscount)
end


-------------------------------绝版赠礼----------------------------
function RebateGiftActivityWGData:SetExtinctGiftInfo(protocol)
	self.extinct_gift_info.grade = protocol.grade
	self.extinct_gift_info.main_long_get = protocol.main_long_get
	self.extinct_gift_info.sub_reward_flag = protocol.sub_reward_flag
	self.extinct_gift_info.main_status_flag = protocol.main_status_flag
	self.extinct_gift_info.sub_all_flag_list = protocol.sub_all_flag_list
end

function RebateGiftActivityWGData:GetExtinctGiftInfo()
	return self.extinct_gift_info
end

function RebateGiftActivityWGData:GetExtinctGrade()
	return self.extinct_gift_info.grade or -1
end

-- 主任务列表配置
function RebateGiftActivityWGData:GetMainTaskCfg()
	local grade = self:GetExtinctGrade()
	return self.main_task_cfg[grade] or {}
end

-- 神谕其他表配置
function RebateGiftActivityWGData:GetGiftOtherCfg()
	local grade = self:GetExtinctGrade()
	return self.gift_other_cfg[grade]
end

-- 子任务列表配置
function RebateGiftActivityWGData:GetSubTaskCfg(act_day)
	local grade = self:GetExtinctGrade()
	return self.sub_task_cfg[grade] and (self.sub_task_cfg[grade][act_day] or {}) or {}
end

-- 主任务激活状态
function RebateGiftActivityWGData:GetMainTaskState(seq)
	if self.extinct_gift_info ~= nil then
		if self.extinct_gift_info.main_status_flag[seq] < 1 then
			return 0
		end

		return 1
	end
end

--子任务奖励领取状态
function RebateGiftActivityWGData:GetSubTaskRewardState(day)
	if self.extinct_gift_info == nil  then
		return 0
	end

	--local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	if self.extinct_gift_info.sub_reward_flag[day] < 1 then
		return 0
	end

	return 1
end

--主任务列表数据
function RebateGiftActivityWGData:GetMainTaskList()
	local data_list = {}
	local main_cfg = self:GetMainTaskCfg()
	if main_cfg == nil then
		return data_list
	end
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)

	for i, v in ipairs(main_cfg) do
		if act_day > 0 then
			if act_day - 1 == v.seq then --今日主任务索引
				self.cur_main_task_seq = v.seq
			end
		end

		local is_get = self:GetMainTaskState(v.seq)
		local data = {
			is_get = is_get,
			seq = v.seq,
			type = v.type,
			param1 = v.param1,
			param2 = v.param2,
			show_icon_ground = v.show_icon_ground,
			mount_scale = v.mount_scale,
			mount_pos = v.mount_pos,
		}
		table.insert(data_list, data)
	end

	return data_list
end

--子任务列表数据
function RebateGiftActivityWGData:GetSubTaskList(day)
	--local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	local sub_task_list = self:GetSubTaskCfg(day)
	local data_list = {}
	if sub_task_list == nil then
		return data_list
	end

	for i, v in ipairs(sub_task_list) do
		if self.extinct_gift_info ~= nil and self.extinct_gift_info.sub_all_flag_list[day] then
			local task_is_get = self.extinct_gift_info.sub_all_flag_list[day][v.seq] > 0 --是否激活
			local data = {
				seq = v.seq,
				type = v.type,
				param1 = v.param1,
				param2 = v.param2,
				show_open_decs = v.show_open_decs,
				open_panel = v.open_panel,
				act_type = v.act_type,
				show_icon_ground = v.show_icon_ground,
				is_get = task_is_get,
			}
			table.insert(data_list, data)
	   end
	end

	return data_list
end

function RebateGiftActivityWGData:GetSubFlowerList(day)
	--local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	local sub_task_list = self:GetSubTaskCfg(day)
	local data_list = {}
	if sub_task_list == nil then
		return data_list
	end

	for i, v in ipairs(sub_task_list) do
		if self.extinct_gift_info ~= nil and self.extinct_gift_info.sub_all_flag_list[day] then
			local data = {
				is_get = self.extinct_gift_info.sub_all_flag_list[day][v.seq],
			}
			table.insert(data_list, data)
	   end
	end

	table.sort(data_list, SortTools.KeyUpperSorter("is_get"))
	return data_list
end

--大奖展示配置
function RebateGiftActivityWGData:GetBigRewardCfg()
	local grade = self:GetExtinctGrade()
	local item_id = 0
	local img_type = 1
	local footlight_type = -1
	for k, v in pairs(self.open_day_cfg) do
		if v.grade == grade then
			item_id = v.maintask_item_list[0].item_id
			img_type = v.show_pic
			footlight_type = v.footlight_type
		end
	end

	return item_id, img_type, footlight_type
end

--天数档次配置
function RebateGiftActivityWGData:GetopenDayGradeCfg()
	local grade = self:GetExtinctGrade()
	return self.open_day_cfg[grade]
end

--子任务奖励展示
function RebateGiftActivityWGData:GetSubTaskRewardCfg(day)
	local grade = self:GetExtinctGrade()
	--local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	if self.sub_task_reward[grade] and self.sub_task_reward[grade][day] then
		return self.sub_task_reward[grade][day]
	end

	return nil
end

--绝版赠礼红点
function RebateGiftActivityWGData:GetExtinctGiftRed()
	local main_red = self:GetMainTaskRed()
	if main_red > 0 then
		return 1
	end

	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	for day = 1, act_day do
		local sub_red = self:GetSubTaskRed(day)
		if sub_red > 0 then
			return 1
		end
	end

	return 0
end

--主活动红点领取
function RebateGiftActivityWGData:GetMainTaskRed()
	local main_cfg = self:GetMainTaskCfg()
	local data_list = self:GetExtinctGiftInfo()
	if main_cfg == nil or data_list == nil then
		return 0
	end
	for i, v in ipairs(main_cfg) do	
		if self:GetMainTaskState(v.seq) < 1 or data_list.main_long_get > 0 then
			return 0
		end
	end

	return 1
end


function RebateGiftActivityWGData:GetJumpRedTab()
	local index
	if self:GetMainTaskRed() then
		index = 0
	end

	local day_list = self:GetSubTaskActDayList()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	for tab_index, day in pairs(day_list) do
		if day <= act_day then
			local sub_red = self:GetSubTaskRed(day)
			if sub_red > 0 then
				return tab_index
			end
		end

		if index == nil then
			index = 0
		end
	end

	return index
end

--子任务领取状态
function RebateGiftActivityWGData:GetSubTaskRed(day)
	local sub_task_list = self:GetSubTaskList(day)
	if sub_task_list == nil then
		return 0
	end
	local sub_get_state = self:GetSubTaskRewardState(day)
	for k, v in pairs(sub_task_list) do
		if not v.is_get or sub_get_state > 0 then
			return 0
		end
	end

	return 1
end

--获得坐骑技能
function RebateGiftActivityWGData:GetBigRewardSkillCfg(data)
	local data_list = {}
	if IsEmptyTable(data) then
        return data_list
    end

	local act_cfg = NewAppearanceWGData.Instance:GetMountActCfgByItemId(data.item_id)
	if not act_cfg then
        return data_list
    end

    local skill_list = NewAppearanceWGData.Instance:GetSpecialMountSkillList(act_cfg.image_id)
	if skill_list[0] then
		data_list = skill_list[0]
	end

	return data_list
end

--获取子任务天数标签显示
function RebateGiftActivityWGData:GetSubTaskActDayTabShow()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	local main_cfg = self:GetMainTaskCfg()
	local show_tab = {}
	for day = 1, #main_cfg do
		  show_tab[day] = act_day >= day and 1 or 0
	end

	return show_tab
end

function RebateGiftActivityWGData:GetSubTaskActDayList()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	local main_cfg = self:GetMainTaskCfg()
	local day_list = {}
	if act_day <= #main_cfg then
		day_list[1] = act_day

		local act_day_list = {}
		for i = 1, #main_cfg do
			table.insert(act_day_list, i)
		end

		if act_day_list[act_day] then
			table.remove(act_day_list, act_day)
		end

		for k, v in pairs(act_day_list) do
			table.insert(day_list, v)
		end
	else
		for i = 1, #main_cfg do
			table.insert(day_list, i)
		end
	end

	return day_list
end

function RebateGiftActivityWGData:GetSubTaskActTabDay(index)
	local day_list = self:GetSubTaskActDayList()
	return day_list[index] or 0
end

function RebateGiftActivityWGData:GetSubTaskDayByIndex(_day)
	local day_list = self:GetSubTaskActDayList()
	for index, day in pairs(day_list) do
		if _day == day then
			return index
		end
	end

	return nil
end

function RebateGiftActivityWGData:GetOneToggleNewData()
	local new_data = {
		is_main = false,
		is_today = false,
		day = 0,
	}

	return new_data
end

function RebateGiftActivityWGData:GetToggleShowList()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT)
	local show_list = {}
	local main_cfg = self:GetMainTaskCfg()

	-- 头两个为特殊的
	if act_day <= #main_cfg then
		for i = 0, 1 do
			local data = self:GetOneToggleNewData()
			data.is_main = i == 0
			data.is_today = i == 1
			data.day = i == 1 and act_day or 0
			show_list[i] = data
		end
	else
		local data = self:GetOneToggleNewData()
		data.is_main = true
		data.day = 0
		show_list[0] = data
	end

	for day = 1, #main_cfg do
		if day < act_day then
			local data = self:GetOneToggleNewData()
			data.day = day
			table.insert(show_list, data)
		end
	end

	return show_list
end

function RebateGiftActivityWGData:GetSkillShowParam()
	local cfg = self:GetopenDayGradeCfg()
	if not cfg then
		return -1, nil
	end

	return cfg.show_skill_type, cfg.show_skill_param
end

---------------------------------------------------------------------------------

-------------------------------充值立减----------------------------
function RebateGiftActivityWGData:SetDiscountInfo(protocol)
	self.discount_info.grade = protocol.grade
	self.discount_info.chongzhi_gold = protocol.chongzhi_gold
	self.discount_info.gift_item_list = protocol.gift_item_list
end

--充值总数
function RebateGiftActivityWGData:GetDiscountChongZhiGold()
	return self.discount_info.chongzhi_gold or 0
end

function RebateGiftActivityWGData:SetDiscountOneInfo(protocol)
	self.discount_gift = protocol.gift_item
end

function RebateGiftActivityWGData:GetDiscountOneInfo()
	return self.discount_gift or {}
end

function RebateGiftActivityWGData:GetDiscountGrade()
	return self.discount_info.grade or -1
end

--进度档位
function RebateGiftActivityWGData:GetDiscountGiftInfo(act_day)
	local grade = self:GetDiscountGrade()
	--print_error("立减grade:", grade)
	return self.discount_gift_cfg[grade] and (self.discount_gift_cfg[grade][act_day] or {}) or {}
end

--宝箱礼包配置
function RebateGiftActivityWGData:GetDiscountBoxList()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS)
	--print_error("立减day:", day)
	local data_list = {}
	if day > 0 then
		data_list = self:GetDiscountGiftInfo(day)
	end

	return data_list
end

--礼包所有数据
function RebateGiftActivityWGData:GetDiscountDataList()
	local box_cfg = self:GetDiscountBoxList()
	local gift_list = self.discount_info.gift_item_list
	local data_list = {}
	if box_cfg == nil or gift_list == nil then
		return data_list
	end

	local cur_gold = self:GetDiscountChongZhiGold()
	for k, v in pairs(box_cfg) do
		local is_lock = cur_gold >= v.need_gold
		local data = {
			seq = v.seq,
			need_gold = v.need_gold,
			base_gold = v.base_gold,
			item_list = v.item_list,
			random_gold = gift_list[k].random_gold,
			is_random = gift_list[k].is_random,
			is_buy = gift_list[k].is_buy,
			is_lock = is_lock,
		}
		table.insert(data_list, data)
	end

	return data_list
end

--宝箱进度条
function RebateGiftActivityWGData:GetDiscountCurProgress(intimacy_value)
	local cur_progress = 0
	local cur_intimacy_value = tonumber(intimacy_value)
	local leiji_data = self:GetDiscountBoxList()
	if next(leiji_data) == nil or cur_intimacy_value == nil then
		return cur_progress
	end

	local slider_piecewise = {0, 0.1, 0.25, 0.42, 0.55, 0.7, 0.85, 1}
	for k, v in pairs(slider_piecewise) do
		local seq = k - 1
		local length = #slider_piecewise
		local cur_need = leiji_data[seq] and leiji_data[seq].need_gold or 0
		local next_need = leiji_data[seq + 1] and leiji_data[seq + 1].need_gold or leiji_data[#leiji_data].need_gold
		local cur_value = slider_piecewise[seq] and slider_piecewise[seq] or 0
		local next_value = slider_piecewise[seq + 1] and slider_piecewise[seq + 1] or slider_piecewise[length]

		if cur_intimacy_value > cur_need and cur_intimacy_value <= next_need then
			cur_progress = (cur_intimacy_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_intimacy_value > leiji_data[#leiji_data].need_gold then
			cur_progress = slider_piecewise[length]
			break
		end
	end
	return cur_progress
end

--充值立减 红点
function RebateGiftActivityWGData:GetDiscountRed()
	local data_list = self:GetDiscountDataList()
	if data_list == nil then
		return 0
	end

	for k, v in pairs(data_list) do
		if v.is_lock then
			if v.is_random < 1 or v.is_buy < 1 then
				return 1
			end
		end
	end

	return 0
end

--是否正在播放数字动画
function RebateGiftActivityWGData:GetNumberAniIsPlay()
	return self.is_play_ani 
end

function RebateGiftActivityWGData:SetNumberAniIsPlay(is_on)
	self.is_play_ani = is_on
end

-------------------------------烟花抽奖----------------------------
function RebateGiftActivityWGData:SetFireWorksInfo(protocol)
	self.fireworks_grade = protocol.grade
	self.fireworks_times = protocol.draw_times
end

--抽奖次数
function RebateGiftActivityWGData:GetCurDrawTimes()
    return self.fireworks_times or 0
end

function RebateGiftActivityWGData:GetFireWorksGrade()
	return self.fireworks_grade or -1
end

function RebateGiftActivityWGData:SetFireWorksResultInfo(protocol)
	self.fireworks_result_info.mode = protocol.mode
	self.fireworks_result_info.count = protocol.count
	self.fireworks_result_info.record_list = protocol.record_list
end

--烟花抽奖结果信息
function RebateGiftActivityWGData:GetFireWorksResultInfo()
	return self.fireworks_result_info
end

--抽奖模式
function RebateGiftActivityWGData:GetFireWorksDrawConsumeCfg()
    return self.fireworks_mode_cfg
end

--抽奖道具
function RebateGiftActivityWGData:GetFireWorksDrawItem()
	return self.fireworks_other_cfg[1]
end

--是否显示保底次数
function RebateGiftActivityWGData:GetFireWorksDrawIsShowBaodiTimes()
	return self.fireworks_other_cfg[1].itemlist_display == 1
end

--抽奖道具list
function RebateGiftActivityWGData:GetFireWorksItemDataChangeList()
    if not self.fireworks_item_change_list then
        self.fireworks_item_change_list = {}
		table.insert(self.fireworks_item_change_list, self.fireworks_other_cfg[1].cost_item_id)
    end
    return self.fireworks_item_change_list
end

--主界面红点
function RebateGiftActivityWGData:GetFireWorksDrawRed()
    --背包有道具
    if self:GetFireWorksBagItem() then
        return 1
    end

    return 0
end

--是否有道具
function RebateGiftActivityWGData:GetFireWorksBagItem()
    local item_list = self:GetFireWorksItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--获取抽奖的选项
function RebateGiftActivityWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

--抽奖概率展示
function RebateGiftActivityWGData:GetFireWorksProbabilityInfo()
	local grade = self:GetFireWorksGrade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW)
	--print_error(grade, act_day)
	return self.fireworsk_gailv_cfg[grade] and (self.fireworsk_gailv_cfg[grade][act_day] or {}) or {}
end

--保底抽奖次数配置
function RebateGiftActivityWGData:GetFireWorksBaoDiDrawTimesCfg()
	local grade = self:GetFireWorksGrade()
	local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW)
	--print_error(grade, act_day)
	return self.fireworks_baodi_cfg[grade] and (self.fireworks_baodi_cfg[grade][act_day] or {}) or {}
end

--保底抽奖次数显示
function RebateGiftActivityWGData:GetFireWorksBaoDiDrawTimes()
	local cfg = self:GetFireWorksBaoDiDrawTimesCfg()
	local draw_times = self:GetCurDrawTimes()
	local cfg_times = cfg[#cfg] and cfg[#cfg].times or 0

	if draw_times >= cfg_times then
		return -1
	end

	return cfg_times - draw_times
end

--珍稀奖励展示配置
function RebateGiftActivityWGData:GetFireWorksGradeInfo(grade, act_day)
	return self.fireworks_reward_pool_cfg[grade] and (self.fireworks_reward_pool_cfg[grade][act_day] or {}) or {}
end

--奖励展示
function RebateGiftActivityWGData:GetFireWorksRewardShowCfg()
	local show_list = {}
	local grade = self:GetFireWorksGrade()
	local day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW)
	--print_error(grade, day)
	local data_list = self:GetFireWorksGradeInfo(grade, day)
	if data_list == nil then
		return
	end

	for k, v in pairs(data_list) do
		if v.is_rare == 1 then --珍稀奖励
			table.insert(show_list, v)
		end
	end

	return show_list
end

--背包道具改变红点
function RebateGiftActivityWGData:RegisterFireworksRemindInBag(remind_name)
    local check_list = self:GetFireWorksItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end

--判断是否为筛选显示的属性.
function RebateGiftActivityWGData:CheckIsSelectTitleAttr(attr_name)
	local attr_name = AttributeMgr.GetAttributteKey(attr_name)

	for key, value in pairs(self.select_title_attr_lits) do
		local temp_name = AttributeMgr.GetAttributteKey(value)
		if attr_name == temp_name then
			return true
		end
	end

	return false
end