-- H-红点逻辑表.xls

return {
material={
{material_id=26349,},
{order=2,material_id=26350,},
{order=3,material_id=26351,},
{type=2,material_id=26346,},
{order=2,material_id=26347,},
{order=3,material_id=26348,},
{type=3,material_id=26352,},
{order=2,material_id=26353,},
{order=3,material_id=26354,},
{type=4,material_id=26370,},
{order=2,material_id=26371,},
{order=3,material_id=26372,},
{type=5,material_id=27611,},
{order=2,material_id=27612,},
{order=3,material_id=27613,},
{type=6,material_id=26376,},
{order=2,material_id=26377,},
{type=7,},
{type=7,material_id=26345,},
{type=8,},
{type=8,}
},

material_meta_table_map={
[19]=2,	-- depth:1
[17]=16,	-- depth:1
[15]=13,	-- depth:1
[11]=10,	-- depth:1
[12]=10,	-- depth:1
[9]=7,	-- depth:1
[8]=7,	-- depth:1
[6]=4,	-- depth:1
[5]=4,	-- depth:1
[14]=13,	-- depth:1
[21]=19,	-- depth:2
},
skill_level={
{},
{section_down=51,section_top=200,},
{section_down=201,section_top=300,},
{section_down=301,section_top=9999,level=5,}
},

skill_level_meta_table_map={
},
material_default_table={type=1,order=1,material_id=26344,number=1,},

skill_level_default_table={section_down=1,section_top=50,level=10,}

}

