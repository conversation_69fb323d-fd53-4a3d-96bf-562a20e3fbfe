--Vip续费界面
VipRenewView = VipRenewView or BaseClass(SafeBaseView)

function VipRenewView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.view_name = "VipRenewView"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(612, 364)})
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_recharge_renew")
end

function VipRenewView:ReleaseCallBack()
	if self.vip_card_view then
		self.vip_card_view:DeleteMe()
		self.vip_card_view = nil
	end
end

function VipRenewView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Recharge.ViewNameRenew
	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.Close, self))
	self:CreateVipCard()
end

function VipRenewView:CreateVipCard()
	local card_cfg_list = VipWGData.Instance:GetVipCardCfg()
	local temp_list = {}
	local card_type = VipWGData.VipCardActiveType.glod
	for _,card in pairs(card_cfg_list) do
		if card.active_condition == card_type then
			temp_list[#temp_list + 1] = card
		end
	end
	temp_list = SortTableKey(temp_list, true)
	self.vip_card_view = AsyncListView.New(VipRenewCardItemRender, self.node_list.ph_item_list)
	self.vip_card_view:SetDataList(temp_list)
end

------------------------------VipRenewCardItemRender-----------------------------

VipRenewCardItemRender = VipRenewCardItemRender or BaseClass(BaseRender)

function VipRenewCardItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.ph_vip_cell)
	self.node_list.btn_buy.button:AddClickListener(BindTool.Bind1(self.OnClickOpenVip, self))
end

function VipRenewCardItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function VipRenewCardItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(data.item_id)
 	self.node_list.lbl_vip_gold.text.text = shop_cfg and shop_cfg.price or '0'
 	self.node_list.lbl_vip_gold_2.text.text = shop_cfg and shop_cfg.price or '0'
 	self.node_list.desc_label.text.text = data.type_desc or ""
 	self.node_list.lbl_item_name.text.text = ToColorStr(data.name, ITEM_COLOR[item_cfg.color])
	self.item_cell:SetData({item_id = data.item_id})
	self.node_list.lbl_vip_gold_2:SetActive(data.seq == 4)
	self.node_list.lbl_vip_gold:SetActive(not (data.seq == 4))
	-- 永久卡特殊处理
	if data.seq == 4 then
		self.node_list.btn_lbl.text.text = Language.Recharge.TurnZeroBuy
		self.node_list.forever_img:SetActive(true)
	end
end

function VipRenewCardItemRender:OnClickOpenVip() --续费VIP
	local data = self:GetData()
	if data then
		if data.seq == 4 then
			RechargeWGCtrl.Instance:Open(TabIndex.recharge_zerobuy)
			VipWGCtrl.Instance:CloseVipRenewView()
			return
		end
		VipWGCtrl.Instance:BuyAndUseVipCard(data.seq)
	end
end