FightSoulBoneStuffSelectView = FightSoulBoneStuffSelectView or BaseClass(SafeBaseView)
function FightSoulBoneStuffSelectView:__init()
	self.view_layer = UiLayer.Pop
	self.view_name = "FightSoulBoneStuffSelectView"
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function FightSoulBoneStuffSelectView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(886, 554)})
	self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_bone_stuff_select_view")
end

function FightSoulBoneStuffSelectView:__delete()

end

function FightSoulBoneStuffSelectView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

    self.stuff_type = nil
    self.select_data = nil
    self.meet_list = nil
	self.to_top_falg = nil
	self.meet_all_list = nil
end

function FightSoulBoneStuffSelectView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FightSoul.SelectStuffTitle

    self.list_view = AsyncListView.New(FightSoulBoneStuffItem, self.node_list["list_view"])
    self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItemCB, self))

    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
	XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.ClickGoto, self))
end

function FightSoulBoneStuffSelectView:SetDataAndOpen(stuff_type, select_data, meet_all_list)
    self.stuff_type = stuff_type
    self.select_data = select_data
	self.meet_all_list = meet_all_list
    self.meet_list = meet_all_list[stuff_type]
	self.to_top_falg = true
    self:Open()
end

function FightSoulBoneStuffSelectView:OnFlush()
    if IsEmptyTable(self.meet_list) then
        self:ChangeShowContent(false)
        return
    end

    local need_num, need_star, need_color = self:GetCurNeedCondition()
    if need_num <= 0 then
        self:ChangeShowContent(false)
        return
    end

    self:ChangeShowContent(true)
    if nil ~= self.list_view then
        self.list_view:SetDataList(self.meet_list)
		if self.to_top_falg then
			self.list_view:JumpToTop()
			self.to_top_falg = false
		end
    end

    local cur_num = self:GetCurSelectNum()
	local quality_str = Language.Common.ColorName[need_color] or ""
	quality_str = ToColorStr(quality_str, ITEM_COLOR[need_color] or COLOR3B.DEFAULT)
	local color_str = cur_num >= need_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
	local type_desc = self.stuff_type == FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
				and Language.FightSoul.StuffTypeDesc[1] or Language.FightSoul.StuffTypeDesc[2]
    self.node_list.need_text.text.text = string.format(Language.FightSoul.StuffNumDesc3,
                                            need_num, need_star, quality_str, type_desc, color_str, cur_num, need_num)
	self.node_list.remind_tips.text.text = Language.FightSoul.StuffSelectDesc2
	self.node_list.btn_goto:SetActive(cur_num < need_num)
end

function FightSoulBoneStuffSelectView:GetCurNeedCondition()
    local need_num, need_star, need_color = 0, 0, 0
    if self.select_data == nil then
        return need_num, need_star, need_color
    end

    local compose_cfg = FightSoulWGData.Instance:GetBoneComposeCfg(self.select_data.color, self.select_data.star)
    if IsEmptyTable(compose_cfg) then
        return need_num, need_star, need_color
    end

    if self.stuff_type == FIGHT_SOUL_STUFF_TYPE.SAME_TYPE then
        need_num = compose_cfg.same_part_num
		need_star = self.select_data.star
		need_color = self.select_data.color
    elseif self.stuff_type == FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 then
        need_num = compose_cfg.same_sixiang_type_num
		need_star = compose_cfg.same_sixiang_type_star
		need_color = compose_cfg.same_sixiang_type_color
    end

    return need_num, need_star, need_color
end

function FightSoulBoneStuffSelectView:GetCurSelectNum()
    local cur_num = 0
    if IsEmptyTable(self.meet_list) then
        return cur_num
    end

    for k,v in pairs(self.meet_list) do
        if v.select_state[self.stuff_type] then
            cur_num = cur_num + 1
        end
    end

    return cur_num
end

function FightSoulBoneStuffSelectView:ChangeShowContent(had_data)
	if not had_data then
		local need_num, need_star, need_color = self:GetCurNeedCondition()
		local quality_str = Language.Common.ColorName[need_color] or ""
		quality_str = ToColorStr(quality_str, ITEM_COLOR[need_color] or COLOR3B.DEFAULT)
		local type_desc = self.stuff_type == FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
						and Language.FightSoul.StuffTypeDesc[1] or Language.FightSoul.StuffTypeDesc[2]
		local color_str = COLOR3B.RED
		self.node_list.no_data_desc.text.text = string.format(Language.FightSoul.StuffNumDesc3,
	                                            need_num, need_star, quality_str, type_desc, color_str, 0, need_num)
	end

    self.node_list.no_data:SetActive(not had_data)
	self.node_list.btn_close:SetActive(not had_data)
	self.node_list.btn_goto:SetActive(not had_data)
	self.node_list.btn_sure:SetActive(had_data)
    self.node_list.had_data:SetActive(had_data)
end

-- 选择列表项回调
function FightSoulBoneStuffSelectView:OnSelectItemCB(item, cell_index, is_default, is_click)
	if not is_click then
		return
	end

	if nil == item or nil == item.data then
		return
	end

    local need_num = self:GetCurNeedCondition()
    local had_num = self:GetCurSelectNum()
    local bag_index = item.data.item_data.bag_index
    if item.data.select_state[self.stuff_type] then
		FightSoulWGData.Instance:RemoveBoneComposeSelectCache(bag_index)
        self:ChangeMeetListData(bag_index, false)
		self:ChangeCacheHeightFlag(false, item.data.item_data.suit_type, bag_index)
    else
        if had_num >= need_num then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.SelectLimit2)
            return
        else
			FightSoulWGData.Instance:AddBoneComposeSelectCache(bag_index)
            self:ChangeMeetListData(bag_index, true)
			self:ChangeCacheHeightFlag(true, item.data.item_data.suit_type, bag_index)
        end
    end

    self:Flush()
    FightSoulWGCtrl.Instance:FlushBoneComposeBagCheckState()
end

function FightSoulBoneStuffSelectView:ChangeCacheHeightFlag(is_add, suit_type, bag_index)
	if self.select_data == nil or IsEmptyTable(self.meet_all_list) then
		return
	end

	local select_suit_type = self.select_data.suit_type
	local height_flag = FightSoulWGData.Instance:GetBoneHeightSelectCache()

	FightSoulWGData.Instance:SetBoneHeightSelectCache(nil)

	local is_had_select = false
	local cahce_table = {suit_type = 0, bag_index = -1}
	for stuff_type,list in ipairs(self.meet_all_list) do
		for k,v in ipairs(list) do
			is_had_select = FightSoulWGData.CheckCurStuffIsSelect(v.select_state)
			if is_had_select and v.item_data.suit_type > select_suit_type and v.item_data.suit_type > cahce_table.suit_type then
				cahce_table = {suit_type = v.item_data.suit_type, bag_index = v.item_data.bag_index}
			end
		end
	end

	if cahce_table.bag_index >= 0 then
		FightSoulWGData.Instance:SetBoneHeightSelectCache(cahce_table.bag_index)
	end
end

function FightSoulBoneStuffSelectView:ChangeMeetListData(bag_index, is_select)
    if IsEmptyTable(self.meet_list) then
        return
    end

    for k,v in pairs(self.meet_list) do
        if bag_index == v.item_data.bag_index then
            self.meet_list[k].select_state[self.stuff_type] = is_select
        end
    end
end

function FightSoulBoneStuffSelectView:ClickGoto()
	if self.select_data ~= nil then
		SiXiangCallWGData.Instance:SetSiXiangSummonType(self.select_data.fight_soul_type)
	end

	FunOpen.Instance:OpenViewByName(GuideModuleName.SiXiangCallView, TabIndex.sixiang_call_sx)
	ViewManager.Instance:FlushView(GuideModuleName.SiXiangCallView, TabIndex.sixiang_call_sx, "summon_type_change")
	self:Close()
end
--------------------------------------------------------------------------------
