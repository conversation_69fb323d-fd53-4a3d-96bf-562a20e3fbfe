-- 新手弹窗对话数据
NovicePopDialogWGData = NovicePopDialogWGData or BaseClass(BaseWGCtrl)
function NovicePopDialogWGData:__init()
	if NovicePopDialogWGData.Instance ~= nil then
		<PERSON>rror<PERSON>og("[NovicePopDialogWGData] attempt to create singleton twice!")
		return
	end
	NovicePopDialogWGData.Instance = self

	self.cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("function_guide_auto").plot_wind, "id")

	-- 已触发列表
	self.triggered_list = {}
end

function NovicePopDialogWGData:__delete()
	NovicePopDialogWGData.Instance = nil
end

function NovicePopDialogWGData:GetConfig()
	return self.cfg
end

function NovicePopDialogWGData:GetConfigById(id)
	return self.cfg[id]
end

-- 当前场景的区域触发配置
function NovicePopDialogWGData:GetEnterAreaTriggerCfg()
	if self.area_cfg then
		return self.area_cfg
	end
	local all_cfg = self:GetConfig()
	self.area_cfg = {}
	for i,v in ipairs(all_cfg) do
		local first_cfg = v[1]
		if first_cfg.trigger_type == NOVICE_TRIGGER_TYPE.ENTER_AREA then
			local data = {}
			data.cfg = v
			local params = Split(first_cfg.trigger_param_1, "|")
			data.scene_id = tonumber(params[1])

			data.pos_list = {}
			local pos_str_list = Split(params[2], ":")
			for i,v in ipairs(pos_str_list) do
				local pos = {}
				local pos_str = Split(v, ",")
				pos.x = tonumber(pos_str[1])
				pos.y = tonumber(pos_str[2])
				table.insert(data.pos_list, pos)
			end

			if #data.pos_list < 3 then
				print_error("G-功能引导-新手弹窗配置有误，区域点数目必须大于等于三个！")
			end

			if not self.area_cfg[data.scene_id] then
				self.area_cfg[data.scene_id] = {}
			end

			table.insert(self.area_cfg[data.scene_id], data)
		end
	end
	return self.area_cfg
end

function NovicePopDialogWGData:GetEnterAreaTriggerCfgBySceneId(scene_id)
	return self:GetEnterAreaTriggerCfg()[scene_id]
end

-- 获得触发类型为接受任务的配置
function NovicePopDialogWGData:GetAcceptTaskTriggerCfg()
	if self.accept_task_trigger_cfg then
		return self.accept_task_trigger_cfg
	end
	local all_cfg = self:GetConfig()
	self.accept_task_trigger_cfg = {}
	for i,v in ipairs(all_cfg) do
		if tonumber(v[1].trigger_type) == NOVICE_TRIGGER_TYPE.ACCEPT_TASK then
			local task_id = tonumber(v[1].trigger_param_1)
			self.accept_task_trigger_cfg[task_id] = v
		end
	end
	return self.accept_task_trigger_cfg
end

function NovicePopDialogWGData:GetAcceptTaskTriggerCfgByTaskId(task_id)
	return self:GetAcceptTaskTriggerCfg()[task_id]
end

-- 获得触发类型为完成任务的配置
function NovicePopDialogWGData:GetCompleteTaskTriggerCfg()
	if self.complete_task_trigger_cfg then
		return self.complete_task_trigger_cfg
	end
	local all_cfg = self:GetConfig()
	self.complete_task_trigger_cfg = {}
	for i,v in ipairs(all_cfg) do
		if tonumber(v[1].trigger_type) == NOVICE_TRIGGER_TYPE.COMPLETE_TASK then
			local task_id = tonumber(v[1].trigger_param_1)
			if task_id then
				self.complete_task_trigger_cfg[task_id] = v
			end
		end
	end
	return self.complete_task_trigger_cfg
end

function NovicePopDialogWGData:GetCompleteTaskTriggerCfgByTaskId(task_id)
	return self:GetCompleteTaskTriggerCfg()[task_id]
end

-- 设置已触发的弹窗
function NovicePopDialogWGData:SetTriggeredList(id)
	self.triggered_list[id] = true
	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	PlayerPrefsUtil.SetInt(role_id .. "NovicePopDialogWGData.triggered_list" .. id, 1)
end

-- 判断弹窗是否已经触发
function NovicePopDialogWGData:IsTriggered(id)
	if self.triggered_list[id] == nil then
		local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
		local result = PlayerPrefsUtil.GetInt(role_id .. "NovicePopDialogWGData.triggered_list" .. id)
		self.triggered_list[id] = result == 1
	end
	return self.triggered_list[id]
end