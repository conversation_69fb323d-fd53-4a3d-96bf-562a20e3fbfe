TianShiGodownHillRewardShowView = TianShiGodownHillRewardShowView or BaseClass(SafeBaseView)
function TianShiGodownHillRewardShowView:__init(view_name)
	self.view_name = "TianShiGodownHillRewardShowView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/tianshi_godownhill_ui_prefab", "layout_tianshi_godownhill_gift_show")
	self:SetMaskBg(true, true)
end

function TianShiGodownHillRewardShowView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function TianShiGodownHillRewardShowView:ReleaseCallBack()
	if self.item_cell_list then
		self.item_cell_list:DeleteMe()
		self.item_cell_list = nil
	end
end

function TianShiGodownHillRewardShowView:InitParam()
	self.data_list = {}
end

function TianShiGodownHillRewardShowView:InitPanel()
	self.item_cell_list = AsyncListView.New(ItemCell, self.node_list.item_root)
    self.item_cell_list:SetStartZeroIndex(true)
end

function TianShiGodownHillRewardShowView:InitListener()
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function TianShiGodownHillRewardShowView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self.data_list = v.data_list
		end
	end
	self:RefreshView()
end

function TianShiGodownHillRewardShowView:RefreshView()
	self.item_cell_list:SetDataList(self.data_list)
end

function TianShiGodownHillRewardShowView:OnClickGetBtn()
	self:Close()
end