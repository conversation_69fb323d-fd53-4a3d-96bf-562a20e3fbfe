require("game/exp_guide/exp_guide_wg_data")
require("game/exp_guide/exp_guide_wg_view")

ExpGuideWGCtrl = ExpGuideWGCtrl or BaseClass(BaseWGCtrl)

function ExpGuideWGCtrl:__init()
	if ExpGuideWGCtrl.Instance then
		error("[ExpGuideWGCtrl]:Attempt to create singleton twice!")
	end
	ExpGuideWGCtrl.Instance = self

    self.data = ExpGuideWGData.New()
    self.view = ExpGuideWGView.New(GuideModuleName.ExpGuideWGView)

    self:RegisterAllEvents()
end

function ExpGuideWGCtrl:__delete()
    self:UnRegisterAllEvents()

    -- 销毁data
    self.data:DeleteMe()
	self.data = nil
    -- 销毁view
	self.view:DeleteMe()
	self.view = nil

    ExpGuideWGCtrl.Instance = nil
end

function ExpGuideWGCtrl:RegisterAllEvents()
    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function ExpGuideWGCtrl:UnRegisterAllEvents()
    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function ExpGuideWGCtrl:OnRoleAttrChange()
    local main_ui_view = MainuiWGCtrl.Instance:GetView()
    if not main_ui_view then
        return
    end

    main_ui_view:UpdateExpGuideBtn()
end

function ExpGuideWGCtrl:OpenView()
    self.view:Open()
end

function ExpGuideWGCtrl:CloseExpGuideView()
    if self.view:IsOpen() then
        self.view:Close()
    end
end