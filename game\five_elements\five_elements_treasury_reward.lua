FiveElementsTreasuryRewardView = FiveElementsTreasuryRewardView or BaseClass(SafeBaseView)

function FiveElementsTreasuryRewardView:__init(view_name)
	self.view_name = "FiveElementsTreasuryRewardView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_treasury_result")
	self:SetMaskBg(false, true)
end

function FiveElementsTreasuryRewardView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
	self:InitRewardList()
	XUI.AddClickEventListener(self.node_list["skip_anim_toggle"], BindTool.Bind(self.AniOnClickJump, self)) --跳过动画
end

function FiveElementsTreasuryRewardView:ReleaseCallBack()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
	if self.once_summon_item then
		self.once_summon_item:DeleteMe()
		self.once_summon_item = nil
	end
end

function FiveElementsTreasuryRewardView:CloseCallBack()
	self:StopTween()
end

function FiveElementsTreasuryRewardView:OpenIndexCallBack()
	self.node_list["jump_toggle"]:SetActive(FiveElementsWGData.Instance:GetJumpAni())  
end

function FiveElementsTreasuryRewardView:OnFlush(param_t)
	self:RefreshView()
	if self.is_shilian then
		self:PlayTenRewardTween(true)
	else
		self:PlayOneRewardTween(true)
	end
end

function FiveElementsTreasuryRewardView:AniOnClickJump()
    FiveElementsWGData.Instance:SetJumpAni()
	self.node_list["jump_toggle"]:SetActive(FiveElementsWGData.Instance:GetJumpAni())
	FiveElementsWGCtrl.Instance:FlushToggle()
end

function FiveElementsTreasuryRewardView:InitParam()
	self.is_play_tween = false
	self.reward_item_list = nil
	self.play_tween_index = 0
	self.is_shilian = false
end

function FiveElementsTreasuryRewardView:InitPanel()
	self.once_summon_item = TreasuryRewardItem.New()
	self.once_summon_item:DoLoad(self.node_list.once_item_root)
end

function FiveElementsTreasuryRewardView:InitListener()
	XUI.AddClickEventListener(self.node_list.sure_btn, BindTool.Bind1(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list["draw_btn"], BindTool.Bind1(self.OnClickDrawBtn, self))
end

function FiveElementsTreasuryRewardView:InitRewardList()
	local res_async_loader = AllocResAsyncLoader(self, "treasury_reward_item")
	res_async_loader:Load("uis/view/five_elements_ui_prefab", "treasury_reward_item", nil,
		function(new_obj)
			if IsNil(new_obj) then
				return
			end

			local item_root = self.node_list.reward_root.transform
			local layout_group = self.node_list.reward_root.grid_layout_group
			local cell_x = layout_group.cellSize.x
			local cell_y = layout_group.cellSize.y
			local spacing_x = layout_group.spacing.x
			local spacing_y = layout_group.spacing.y

			local item_list = {}
			for i=1,10 do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(item_root, false)
				item_list[i] = TreasuryRewardItem.New(obj)
				item_list[i]:SetIndex(i)
				item_list[i]:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
			end

			self.reward_item_list = item_list
			self:RefreshView()
		end)
end

function FiveElementsTreasuryRewardView:OnClickSureBtn()
	if self.is_play_tween then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElementsTreasury.IsWorkIng)
		return
	end
	self:Close()
end

function FiveElementsTreasuryRewardView:OnClickDrawBtn()
	--检测跳过动画
	if not  FiveElementsWGData.Instance:GetJumpAni() then
		--转盘动画未复位/抽奖动画进行中
		if self.is_play_tween then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElementsTreasury.AnimPlaying)
			return
		end
	end

	local index = FiveElementsWGData.Instance:CacheOrGetDrawIndex()
    FiveElementsWGCtrl.Instance:ClickUseDrawItem(index, function()
        local cfg = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
		if cfg[index] ~= nil then
			local jump_cfg = FiveElementsWGData.Instance:GetPoolJumpCfg()
    		local is_auto_item = FiveElementsWGData.Instance:GetAutoUseItem() --是否自动使用跳转道具
    		local param_id = is_auto_item and jump_cfg.item_id or 0
			FiveElementsWGData.Instance:SetAniIsplaying(true)
			FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_DRAW, cfg[index].mode, param_id)
			--self:Close()
		end
    end)
end

function FiveElementsTreasuryRewardView:RefreshView()
	local reward_list = FiveElementsWGData.Instance:GetTreasuryResultInfo().result_item_list or {}  --服务器下发的奖励列表
	if #reward_list > 1 then
		self:ShowTenReward(reward_list)
		self:PlayTenRewardTween()
	elseif #reward_list == 1 then
		self:ShowOneReward(reward_list[1])
		self:PlayOneRewardTween()
	end

	self.is_shilian = #reward_list > 1
	self.node_list.once_content:SetActive(#reward_list == 1)
	self.node_list.reward_root:SetActive(#reward_list > 1)

	----[[ 再抽一次
	local index = FiveElementsWGData.Instance:CacheOrGetDrawIndex()
	if index == 1 then
		self.node_list.draw_btn_lbl.text.text = string.format(Language.FiveElementsTreasury.RefineAgain, 1)
	elseif index == 2 then
		self.node_list.draw_btn_lbl.text.text = string.format(Language.FiveElementsTreasury.RefineAgain, 10)
	end
	--]]

	self:FlushCostInfoShow()
end


-- 单抽奖励展示
function FiveElementsTreasuryRewardView:ShowOneReward(reward_data)
	self.once_summon_item:SetData(reward_data)
end

function FiveElementsTreasuryRewardView:PlayOneRewardTween(is_play)
	local is_skip =  FiveElementsWGData.Instance:GetJumpAni()
	if is_skip then
		self.once_summon_item:RestItem()
		self.once_summon_item:PlayYoyoTween()
	elseif is_play then
		self.once_summon_item:ReadyPlayTween()
		self.once_summon_item:PlayFlyTween()
	end
end

---[[ 十连抽动画播放
function FiveElementsTreasuryRewardView:ShowTenReward(reward_list)
	if not IsEmptyTable(self.reward_item_list) then
		local item_list = self.reward_item_list
		for i = 1, #item_list do
			item_list[i]:SetData(reward_list[i])
		end
	end
end

function FiveElementsTreasuryRewardView:PlayTenRewardTween(is_play)
	local is_skip =  FiveElementsWGData.Instance:GetJumpAni()
	local item_list = self.reward_item_list
	if is_skip then
		self:StopTween()
	elseif is_play and not self.is_play_tween then
		for i = 1, #item_list do
			item_list[i]:ReadyPlayTween()
		end
		self.reward_item_list[1]:PlayFlyTween(true)
		self.play_tween_index = 1
		self.is_play_tween = true
	end
end

function FiveElementsTreasuryRewardView:NextGetRewardTween()
	if self.is_play_tween then
		local index = self.play_tween_index + 1
		self.play_tween_index = index
		if self.reward_item_list[index] then
			self.reward_item_list[index]:PlayFlyTween(true)
		else
			self.is_play_tween = false
		end
	end
end

function FiveElementsTreasuryRewardView:StopTween()
	if not IsEmptyTable(self.reward_item_list) then
		local item_list = self.reward_item_list
		for i = 1, #item_list do
			item_list[i]:RestItem()
			item_list[i]:PlayYoyoTween()
		end
	end
	self.is_play_tween = false
end

--刷新货币信息
function FiveElementsTreasuryRewardView:FlushCostInfoShow()
	local index = FiveElementsWGData.Instance:CacheOrGetDrawIndex()
	local treasury_info = FiveElementsWGData.Instance:GetTreasuryDrawConsumeCfg()
	if not treasury_info then
		return 
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(treasury_info[index].cost_item_id)
	local str = string.format("%d/%d", has_num, treasury_info[index].cost_item_num)
	local str_color = has_num >= treasury_info[index].cost_item_num and COLOR3B.GREEN or COLOR3B.PINK
    local bundle, asset = ResPath.GetItem(treasury_info[index].cost_item_id)

    self.node_list["const_img"].image:LoadSprite(bundle, asset)
    self.node_list["const_lbl"].text.text = ToColorStr(str, str_color)

    --折扣
    local show_discount = false
	self.node_list["discount_bg"]:SetActive(show_discount)
end
--]]

--------------------------------------------------------------------------------------

TreasuryRewardItem = TreasuryRewardItem or BaseClass(BaseRender)

function TreasuryRewardItem:__init()
	self.center_pos_x = 0
	self.center_pos_y = 0
	self.m_tween = nil
	self.m_yoyo_tween = nil
	self.is_best_reward = false
end

function TreasuryRewardItem:__delete()
	self:StopItemTween()
end

function TreasuryRewardItem:DoLoad(parent)
	self:LoadAsset("uis/view/five_elements_ui_prefab", "treasury_reward_item", parent.transform)
end

function TreasuryRewardItem:LoadCallBack()
	self.model_pos_x, self.model_pos_y = RectTransform.GetAnchoredPositionXY(self.node_list.reward_model_root.rect)
	XUI.AddClickEventListener(self.node_list.item_bg, BindTool.Bind1(self.OnClickSummonItem, self))
end

function TreasuryRewardItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		return
	end

	self.is_best_reward = item_cfg.color >= 4
	self.node_list.name_label.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

	local color = item_cfg.color or 1
    if self.node_list.item_bg then
        local bundle,asset = ResPath.GetCommon("a2_sc_btn_bg_" .. color)
        self.node_list.item_bg.image:LoadSprite(bundle, asset)
    end
    if self.node_list.item_effect then
        local asset_name = BaseCell_Ui_Circle_Effect[color]
        local bundle,asset = ResPath.GetWuPinKuangEffectUi(asset_name)
        self.node_list.item_effect:ChangeAsset(bundle, asset)
    end

	local bundle,asset = ResPath.GetItem(item_cfg.icon_id)
    self.node_list.item_icon.image:LoadSprite(bundle, asset)

	self:FlushItemEffect(item_cfg.color)
	self.node_list.reward_model_root:SetActive(true)
end

function TreasuryRewardItem:OnClickSummonItem()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end

    local item_id = data.item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg then
		local item_data = {}
        item_data.item_id = item_id
        item_data.color = item_cfg.color or 1
        item_data.is_bind = item_cfg.isbind or 1
        TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL, nil)
    end
end

function TreasuryRewardItem:FlushItemEffect(color)
	local eff_id = TIAN_SHEN_SHEN_SHI_MODEL_EFFECT[color]
	local bundle, asset = ResPath.GetEffectUi(eff_id)
	self.node_list.effect_root:ChangeAsset(bundle, asset)

	if self.is_best_reward then
		local bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_zhuanpanka_gj)
		self.node_list.best_effect_root:ChangeAsset(bundle, asset)
	end
end

-- 十连时播
function TreasuryRewardItem:PlayFlyTween(need_move)
	if self.m_tween then
		return
	end

	self.node_list.reward_model_root.transform:SetLocalScale(0.5, 0.5, 0.5)
	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = need_move and self.node_list.tween_root.rect:DOAnchorPos(u3dpool.vec3(0, 0, 0), 0.2)
	local tween_alpha = self.node_list.tween_root.canvas_group:DoAlpha(0, 1, 0.2)

	if self.is_best_reward then
		local tween_scale_big = self.node_list.tween_root.transform:DOScale(1.5, 0.5)
		tween_scale_big:SetEase(DG.Tweening.Ease.OutCubic)
		local tween_scale_min = self.node_list.tween_root.transform:DOScale(1, 0.2)
		local root_pos = self.node_list.reward_model_root.transform.position
		local tween_z_big = self.node_list.tween_root.transform:DOLocalMove(u3dpool.vec3(root_pos.x, root_pos.y, -50), 0.5)
		local tween_z_min = self.node_list.tween_root.transform:DOLocalMove(u3dpool.vec3(root_pos.x, root_pos.y, 0), 0.5)


		tween_sequence:Append(tween_scale_big)
		tween_sequence:Join(tween_alpha)
		tween_sequence:Join(tween_z_big)
		tween_sequence:InsertCallback(0.2, function ()
			self.node_list.best_effect_root:SetActive(true)
		end)
		tween_sequence:AppendInterval(1)
		tween_sequence:Append(tween_scale_min)
		tween_sequence:Join(tween_z_min)
		if need_move then
			tween_sequence:Append(tween_move)
		end
		tween_sequence:AppendCallback(function ()
			self.node_list.effect_root:SetActive(true)
		end)
	else
		local tween_scale_big = self.node_list.tween_root.transform:DOScale(0.7, 0.1)
		local tween_scale_min = self.node_list.tween_root.transform:DOScale(1, 0.1)

		tween_sequence:Append(tween_alpha)
		if need_move then
			tween_sequence:Join(tween_move)
		end
		tween_sequence:Append(tween_scale_big)
		tween_sequence:Append(tween_scale_min)
		tween_sequence:InsertCallback(0.2, function ()
			self.node_list.effect_root:SetActive(true)
		end)
	end

	tween_sequence:OnComplete(function ()
		self.m_tween:Kill()
		self.m_tween = nil
		self:PlayYoyoTween()
		FiveElementsWGCtrl.Instance:PlayTreasuryNextRewardTween()
	end)

	self.m_tween = tween_sequence
end

function TreasuryRewardItem:PlayYoyoTween()
	if self.m_yoyo_tween then
		return
	end
	self.m_yoyo_tween = self.node_list.reward_model_root.transform:DOAnchorPosY(self.model_pos_y + 10, 1)
	self.m_yoyo_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
end

-- 算下在布局组件中间的偏移点(先只考虑10个两行的情况)
function TreasuryRewardItem:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
	local index = self:GetIndex()
	local row = 5											-- 多少个一行
	local row_num = math.ceil(index / row)					-- 行数
	local center_index = row / 2 + row * (row_num - 1) 		-- 这一行的中间数
	local center_row_num = 1
	local center_x = (cell_x + spacing_x) * (center_index - index + 0.5)
	local center_y = -(cell_y + spacing_y) * (center_row_num - row_num + 0.5)
	self.center_pos_x = center_x
	self.center_pos_y = center_y
end

function TreasuryRewardItem:ReadyPlayTween()
	self:StopItemTween()

	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, self.center_pos_x, self.center_pos_y)
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_model_root.rect, self.model_pos_x, self.model_pos_y)

	self.node_list.tween_root.canvas_group.alpha = 0
	self.node_list.effect_root:SetActive(false)
	self.node_list.best_effect_root:SetActive(false)
	self.node_list.reward_model_root.transform:SetLocalScale(0, 0, 0)
end

function TreasuryRewardItem:RestItem()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 0, 0)
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_model_root.rect, self.model_pos_x, self.model_pos_y)
	self.node_list.tween_root.canvas_group.alpha = 1
	self.node_list.tween_root.transform:SetLocalScale(1, 1, 1)
	self.node_list.reward_model_root.transform:SetLocalScale(0.5, 0.5, 0.5)
	self.node_list.effect_root:SetActive(false)
	self.node_list.best_effect_root:SetActive(false)
	self:StopItemTween()
end

function TreasuryRewardItem:StopItemTween()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
	if self.m_yoyo_tween then
		self.m_yoyo_tween:Kill()
		self.m_yoyo_tween = nil
	end
end