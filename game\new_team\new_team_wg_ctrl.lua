require("game/new_team/new_team_view")
require("game/new_team/team_view")
require("game/new_team/new_team_wg_data")
require("game/new_team/new_team_change_goal_view")
require("game/new_team/new_team_recruit_view")
require("game/new_team/new_team_invite_view")
require("game/new_team/new_team_quick_view")
require("game/new_team/new_team_apply_view")
require("game/new_team/new_team_prepare_view")
require("game/new_team/team_baoming_enter")
require("game/new_team/team_prepare_view")
require("game/new_team/team_match_view")
require("game/new_team/team_talk_view")
require("game/new_team/team_add_friend")
require("game/new_team/team_hebing_view")
require("game/new_team/quick_invite_team")
require("game/new_team/quick_invite_match")
require("game/new_team/team_call_together")
require("game/new_team/team_clone_view")
require("game/new_team/team_pop_menu")
require("game/new_team/team_invite_alert")
require("game/new_team/team_clone_help_reward_view")

NewTeamWGCtrl = NewTeamWGCtrl or BaseClass(BaseWGCtrl)

function NewTeamWGCtrl:__init()
	if nil ~= NewTeamWGCtrl.Instance then
		ErrorLog("[NewTeamWGCtrl]:Attempt to create singleton twice!")
	end
	NewTeamWGCtrl.Instance = self

	-- self.view = NewTeamView.New() -- GuideModuleName.NewTeamView
	self.data = NewTeamWGData.New()
	self.change_goal_view = NewTeamChangeGoalView.New(GuideModuleName.NewTeamChangeGoalView)
	self.recruit_view = NewTeamRecruitView.New(GuideModuleName.NewTeamRecruitView)
	self.invite_view = NewTeamInviteView.New(GuideModuleName.NewTeamInviteView)
	self.quick_view = NewTeamQuickView.New(GuideModuleName.NewTeamQuickView)
	self.apply_view = NewTeamApplyView.New()
	self.hebing_view = TeamHeBingView.New()
	self.prepare_view = NewTeamPrepareView.New(GuideModuleName.NewTeamPrepareView)
	self.quick_invite_team = QuickInviteTeam.New(GuideModuleName.QuickInviteTeamView)
	self.quick_invite_match = QuickInviteMatch.New(GuideModuleName.QuickInviteMatch)

	self.baoming_enter_view = TeamBaoMingEnterView.New(GuideModuleName.NewTeamBaoMingEnterView)
	self.jf_prepare_view = TeamPrepareView.New(GuideModuleName.TeamPrepareView)
	self.team_match_veiw = TeamMatchView.New(GuideModuleName.TeamMatchView)
	self.team_talk_view = TeamTalkView.New()
	self.team_pop_menu = TeamPopMenu.New()

	self:RegisterAllProtocols()

	self.m_view = TeamView.New(GuideModuleName.NewTeamView)
	self.team_add_friend = TeamAddFriend.New()
	self.team_call_together=TeamCallTogether.New()

	self.request_callback_list = {}

	self.specify_invite_alert = TeamInviteAlert.New()

	self.clone_help_reward_view = TeamCloneHelpRewardView.New()

	self:BindGlobalEvent(LoginEventType.GAME_SERVER_DISCONNECTED, BindTool.Bind(self.OnDisConnectedGameServer, self))
end

function NewTeamWGCtrl:__delete()
	self.team_add_friend:DeleteMe()
	
	-- if nil ~= self.view then
	-- 	self.view:DeleteMe()
	-- 	self.view = nil
	-- end

	if self.clone_help_reward_view then
		self.clone_help_reward_view:DeleteMe()
		self.clone_help_reward_view = nil
	end

	if nil ~= self.m_view then
		self.m_view:DeleteMe()
		self.m_view = nil
	end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.change_goal_view then
		self.change_goal_view:DeleteMe()
		self.change_goal_view = nil
	end

	if nil ~= self.invite_view then
		self.invite_view:DeleteMe()
		self.invite_view = nil
	end

	if nil ~= self.quick_view then
		self.quick_view:DeleteMe()
		self.quick_view = nil
	end

	if nil ~= self.apply_view then
		self.apply_view:DeleteMe()
		self.apply_view = nil
	end

	if nil ~= self.hebing_view then
		self.hebing_view:DeleteMe()
		self.hebing_view = nil
	end

	if nil ~= self.prepare_view then
		self.prepare_view:DeleteMe()
		self.prepare_view = nil
	end

	if self.quick_invite_team then
		self.quick_invite_team:DeleteMe()
		self.quick_invite_team = nil
	end

	if self.quick_invite_match then
		self.quick_invite_match:DeleteMe()
		self.quick_invite_match = nil
	end

	if nil ~= self.baoming_enter_view then
		self.baoming_enter_view:DeleteMe()
		self.baoming_enter_view = nil
	end

	if nil ~= self.jf_prepare_view then
		self.jf_prepare_view:DeleteMe()
		self.jf_prepare_view = nil
	end

	if nil ~= self.team_match_veiw then
		self.team_match_veiw:DeleteMe()
		self.team_match_veiw = nil
	end

	if nil ~= self.team_talk_view then
		self.team_talk_view:DeleteMe()
		self.team_talk_view = nil
	end

	if nil ~= self.team_call_together then
		self.team_call_together:DeleteMe()
		self.team_call_together = nil
	end

	if self.specify_invite_alert then
		self.specify_invite_alert:DeleteMe()
		self.specify_invite_alert = nil
	end
	self.request_callback_list = {}

	NewTeamWGCtrl.Instance = nil
end

-- 协议注册
function NewTeamWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSTeamFbStartErq)
	self:RegisterProtocol(CSAutoHaveTeam)
	self:RegisterProtocol(CSChangeTeamLimit)
	self:RegisterProtocol(CSCreateTeam)
	self:RegisterProtocol(CSApplyForEnterTeamFbReq)
	self:RegisterProtocol(CSGetNearRoleList)
	self:RegisterProtocol(CSStartOrCancelTeamFB) --前往目标
	self:RegisterProtocol(CSTeamMergeReq)

	self:RegisterProtocol(SCTeamMatchInfo, "OnSCTeamMatchInfo")
	self:RegisterProtocol(SCEnterTeamFbNotice, "OnSCEnterTeamFbNotice")
	self:RegisterProtocol(SCNearRoleListRet, "OnSCNearRoleListRet")
	self:RegisterProtocol(SCRoomInfo, "OnSCRoomInfo")
	self:RegisterProtocol(SCOutOfRoom, "OnSCOutOfRoom")
    self:RegisterProtocol(SCSomebodyCannotEnterTeamFbNotice, "OnSCSomebodyCannotEnterTeamFbNotice")
	self:RegisterProtocol(SCTeamLeaderMarkChange, "OnSCTeamLeaderMarkChange")
	self:RegisterProtocol(SCTeamMergeReqRet, "OnSCTeamMergeReqRet")

	self:RegisterProtocol(CSQueryRoleTeamInfo)
	self:RegisterProtocol(SCQueryRoleTeamInfoRet, "OnSCQueryRoleTeamInfoRet")

	self:RegisterProtocol(CSNoLongerOperateReq)
	self:RegisterProtocol(CSTeamOneKeyMergeReq)

	self:RegisterProtocol(SCTeamInviteChannelChat, "OnSCTeamInviteChannelChat")
	self:RegisterProtocol(SCTeamInviteMatch, "OnSCTeamInviteMatch")

	self:RegisterProtocol(CSTeamTargetShouting)

	self:RegisterProtocol(CSTeamFenshenInfo, "OnCSTeamFenshenInfo")
end

function NewTeamWGCtrl:Flush( ... )
	self.m_view:Flush( ... )
end

-- function NewTeamWGCtrl:SendSCTeamMatchStateReq()
-- 	local send_protocol = ProtocolPool.Instance:GetProtocol(CSTeamMatchStateReq)
-- 	send_protocol:EncodeAndSend()
-- end

function NewTeamWGCtrl:SendAutoMatchTeam(operate_type, team_type, teamfb_mode,match_type,param, is_invite_uid)
	--护送拦截
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end
	
	--match_type == 2 匹配机器人
	if match_type ~= 2 and team_type == GoalTeamType.QingYuanFb and operate_type == 0 then
		local room_menber_list = SocietyWGData.Instance:GetTeamMemberList()
		local num = #room_menber_list
		if num ~= 2 then 
			TipWGCtrl.Instance:ShowSystemMsg(Language.Society.QingYuanPiPei)
			return
		end

		-- if room_menber_list[1].sex == room_menber_list[2].sex then 
		-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Society.QingYuanPiPei)
		-- 	return
		-- end
	end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSAutoHaveTeam)
	send_protocol.operate_type = operate_type
	send_protocol.team_type = team_type
	send_protocol.teamfb_mode = teamfb_mode
	send_protocol.match_type = match_type or 2
	send_protocol.param = param or 0   	--1 表示一匹配到机器人就直接进入
	send_protocol.is_invite_uid = is_invite_uid or 0
	send_protocol:EncodeAndSend()

	-- 新请求不再需要参数
	-- local send_protocol = ProtocolPool.Instance:GetProtocol(CSTeamFbStartErq)
	-- send_protocol:EncodeAndSend()
end

--改变队伍限制条件
function NewTeamWGCtrl:SendChangeTeamLimit(team_type, teamfb_mode, min_level, max_level)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeTeamLimit)
	send_protocol.team_type = team_type or 0
	send_protocol.teamfb_mode = teamfb_mode
	send_protocol.min_level = min_level
	send_protocol.max_level = max_level
	send_protocol:EncodeAndSend()
end

function NewTeamWGCtrl:SendNoLongerOperateReq(no_longer_type, role_id, is_clear, record_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSNoLongerOperateReq)
	send_protocol.no_longer_type = no_longer_type
	send_protocol.role_id = role_id
	send_protocol.is_clear = is_clear or 0
	send_protocol.record_type = record_type or 0
	send_protocol:EncodeAndSend()
end

function NewTeamWGCtrl:SendTeamOneKeyMergeReq(count, merge_team_list)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSTeamOneKeyMergeReq)
	send_protocol.count = count
	send_protocol.role_uid = merge_team_list or {}
	send_protocol:EncodeAndSend()
end

-- 创建目标队伍
function NewTeamWGCtrl:SendCreateTeam(team_type, teamfb_mode, min_level, max_level)
	--护送拦截
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCreateTeam)
	if BossWGData.IsBossScene(Scene.Instance:GetSceneType()) then
		send_protocol.must_check = 1
	else
		send_protocol.must_check = 0
	end
	-- send_protocol.assign_mode = 1
	send_protocol.member_can_invite = 1		--默认可邀请
	send_protocol.team_type = team_type
	send_protocol.teamfb_mode = teamfb_mode
	send_protocol.min_level = min_level
	send_protocol.max_level = max_level
	send_protocol:EncodeAndSend()
end

-- 回复是否进入组队副本
function NewTeamWGCtrl:SendReplyTeamFb(team_index, fb_type, is_accept, layer)       --队伍索引, 副本类型, 是否同意, 副本层数
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSApplyForEnterTeamFbReq)
	send_protocol.team_index = team_index
	send_protocol.fb_type = fb_type
	send_protocol.is_accept = is_accept
	send_protocol.layer = layer
	send_protocol:EncodeAndSend()
end

-- 查询玩家详细信息
function NewTeamWGCtrl:QueryRoleInfo( uid )
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSQueryRoleInfo)
	send_protocol.target_uid = uid
	send_protocol:EncodeAndSend()
end

-- 请求当前场景玩家列表
function NewTeamWGCtrl:SendGetNearRoleList()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGetNearRoleList)
	send_protocol:EncodeAndSend()
end

function NewTeamWGCtrl:OnSCTeamMatchStateReq(protocol)
	--self.data:SetIsMatching(protocol.is_matching)
	--if self.m_view:IsOpen() then
	--	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView)
	--	NewTeamWGCtrl.Instance.m_view:Flush({TabIndex.team_pingtai, TabIndex.team_near}, "MatchStateChange", { MatchStateChange = true})
	--end
	--MainuiWGCtrl.Instance:ChangeMatchState(self.data:GetMatchStateInfo())
end

function NewTeamWGCtrl:OnSCTeamMatchInfo(protocol)
	if protocol.is_matching == 1 then
		self.data:SetMatchingTeamTypeAndMode(protocol.team_type, protocol.teamfb_mode)
	end
	self.data:SetIsMatching(protocol.is_matching)
	self:Flush(TabIndex.team_my_team)
    self:Flush(TabIndex.team_pingtai, "MatchStateChange", { MatchStateChange = true})
--    self:Flush(TabIndex.team_near, "MatchStateChange", { MatchStateChange = true})
	GlobalEventSystem:Fire(TeamInfoQuery.TEAM_INFO_BACK)
	GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)

	MainuiWGCtrl.Instance:ChangeMatchState(protocol)
    --自己匹配状态改变的时候， 请求一下当前列表
	local team_type, team_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, team_mode)
	--SocietyWGCtrl.Instance:SendTeamListReq(team_type, team_mode)
	if NewTeamWGCtrl.Instance:CheckIsOpenPingTaiIndex() then
		local team_type, team_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
		SocietyWGCtrl.Instance:SendTeamListReq(team_type, team_mode)
	end
	if NewTeamWGCtrl.Instance:CheckIsOpenNearIndex() then
		SocietyWGCtrl.Instance:SendTeamListReq(-1, -1)
	end
	if protocol.is_matching == 1 then
		self:OpenTeamMatchView()
		--self:OpenBaoMingEnterView()

		----开始【副本名称&难度】匹配
		--SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.StartMatchTip, goal_info.team_type_name))
	else
		self:CloseTeamMatchView()
		self:CloseBaoMingEnterView()
        ----队长已取消【副本名称】匹配
        --SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.CloseMatchTip, goal_info.team_type_name))
	end
     GlobalEventSystem:Fire(OtherEventType.MatchChangeEvent)
end

function NewTeamWGCtrl:SendInviteUser( user_id ,team_type, is_notice)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSInviteUser)
	send_protocol.role_id = user_id
	send_protocol.type = team_type or 0
	send_protocol.is_notice = is_notice or 1
	send_protocol:EncodeAndSend()
end

function NewTeamWGCtrl:OnSCEnterTeamFbNotice(protocol)

end

function NewTeamWGCtrl:OnSCNearRoleListRet(protocol)
	for k,v in pairs(protocol.role_list) do
		AvatarManager.Instance:SetAvatarKey(v.orig_uid > 0 and v.orig_uid or v.user_id, v.avatar_key_big, v.avatar_key_small )
	end
	self.data:SetNearRoleList(protocol)
	self.invite_view:Flush()
	GlobalEventSystem:Fire(OtherEventType.NearRoleListRet)
end

function NewTeamWGCtrl:Open(param_t)
	if param_t then
		if 1 == SocietyWGData.Instance:GetIsInTeam() then
			local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(param_t.team_type, param_t.fb_mode)
			 --组队的最大等级改为等级排行的第一名
            local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
            local top_lv = goal_info.role_max_level or top_user_level
			local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
			if (now_team_type ~= param_t.team_type or now_fb_mode ~= param_t.fb_mode) and SocietyWGData.Instance:GetIsTeamLeader() == 1 then
				NewTeamWGData.Instance:SetTeamTypeAndMode(param_t.team_type, param_t.fb_mode)
				NewTeamWGCtrl.Instance:SendAutoMatchTeam(1, now_team_type, now_fb_mode)
				NewTeamWGCtrl.Instance:SendChangeTeamLimit(param_t.team_type, param_t.fb_mode, goal_info.role_min_level, top_lv)
				now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
				NewTeamWGCtrl.Instance:SendAutoMatchTeam(0, now_team_type, now_fb_mode)
			end
		else
			NewTeamWGData.Instance:SetNowGoalInfoByTypeAndMode(param_t.team_type, param_t.fb_mode)
		end
		--self.view:SetIsMatching(param_t.is_match)
        self.m_view:SetIsMatching(param_t.is_match)

	end
	--self.view:Open()
     self.m_view:Open(TabIndex.team_my_team)
end

function NewTeamWGCtrl:OpenNewView()
	self.m_view:Open()
	self:Flush()
end

function NewTeamWGCtrl:OnClickQingyuanAddTimes()
    local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
    local fb_info = MarryWGData.Instance:GetQyFbInfo()
    if not other_cfg or not fb_info then
        print_error("other_cfg is a nil value!!!")
        return
    end
    local bind_gold = RoleWGData.Instance.role_info.bind_gold
    local gold = RoleWGData.Instance.role_info.gold
    local cost_text = Language.Common.GoldText
    if bind_gold >= other_cfg.fb_buy_times_gold_cost then
		cost_text = Language.Common.BindGoldText
	end 
    if other_cfg.fb_buy_times_limit > fb_info.self_buy_jion_fb_times then
        if gold + bind_gold >= other_cfg.fb_buy_times_gold_cost then      
            local desc = ""
            local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
            if lover_id <= 0 then
                desc = string.format(Language.Marry.FbBuyCountTips1, cost_text, other_cfg.fb_buy_times_gold_cost)
            else
                desc = string.format(Language.Marry.FbBuyCountTips, cost_text, other_cfg.fb_buy_times_gold_cost)
            end
            local ok_func = function()
                MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB)
            end

            TipWGCtrl.Instance:OpenAlertTips(desc, ok_func)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoEnoughGold)
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.GouMaiTips2)
    end
end

function NewTeamWGCtrl:PingTaiClickFlush()
	self.m_view:PingTaiOnClickFlush()
end

function NewTeamWGCtrl:SwitchView(tab_index)
	self.m_view:ChangeToIndex(tab_index)
end

function NewTeamWGCtrl:OpenChangeGoalView(is_pingtai)
	self.change_goal_view:SetIsPingTai(is_pingtai)
	self.change_goal_view:Open()
end

function NewTeamWGCtrl:OpenInviteView()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:OpenInviteView()
    else
        self.invite_view:Open()
    end
end

function NewTeamWGCtrl:FlushInviteView()
	if self.invite_view:IsOpen() then
		self.invite_view:Flush()
	end
end

function NewTeamWGCtrl:CloseInviteView()
	if self.invite_view:IsOpen() then
		self.invite_view:Close()
	end
end

function NewTeamWGCtrl:CreateInviteRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
    self.invite_view:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
end

function NewTeamWGCtrl:ForceFlushInviteView()
	self.invite_view:ForceFlushInviteList()
end

function NewTeamWGCtrl:OpenQuickView()
	self.quick_view:Open()
end

function NewTeamWGCtrl:OpenRecruitView()
	self.recruit_view:Open()
end

function NewTeamWGCtrl:OpenApplyView()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        CrossTeamWGCtrl.Instance:OpenApplyView()
    else
        self.apply_view:Open()
    end
end

function NewTeamWGCtrl:GetTodayCheckActive()
	return self.apply_view:GetTodayCheckActive()
end

function NewTeamWGCtrl:OpenPrepareView()
	self.prepare_view:Open()
end
function NewTeamWGCtrl:GuildFirstEnterFb(fb_type,call_back)
	self.prepare_view:GuildFirstEnterFbView(fb_type,call_back)
end

function NewTeamWGCtrl:OpenCustomMenu(buff,id,pos)
	--self.view:OpenCustomMenu(buff,id,pos)
    self.m_view:OpenCustomMenu(buff,id,pos)
end

function NewTeamWGCtrl:OpenBaoMingEnterView()
	ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
	-- if not ViewManager.Instance:IsOpen(GuideModuleName.NewTeamBaoMingEnterView) then
	-- 	ViewManager.Instance:Open(GuideModuleName.NewTeamBaoMingEnterView, 0)
	-- end
end
function NewTeamWGCtrl:CloseBaoMingEnterView()
	if ViewManager.Instance:IsOpen(GuideModuleName.NewTeamBaoMingEnterView) then
		ViewManager.Instance:Close(GuideModuleName.NewTeamBaoMingEnterView)
	end
end

function NewTeamWGCtrl:OnSCTeamInviteChannelChat(protocol)
	self.data:OnSCTeamInviteChannelChat(protocol)	
end

function NewTeamWGCtrl:OpenQuickInviteTeam()
	if not self.quick_invite_team:IsOpen() then
		self.quick_invite_team:Open()
	else
		self.quick_invite_team:Flush()
	end
end

function NewTeamWGCtrl:OnSCTeamInviteMatch(protocol)
	local invite_match_info = protocol.invite_match_info
	local team_target = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(invite_match_info.team_type, invite_match_info.team_fb_mode)
	if not team_target then
		return
	end

	local is_open = false
	local view_name, tab_index = NewTeamWGData.Instance:GetJumpViewNameAndIndex(invite_match_info.team_type, invite_match_info.team_fb_mode)
	if view_name and tab_index then
		is_open = FunOpen.Instance:GetFunIsOpened(tab_index)
	end

	if not is_open then
		return
	end

	if not self.quick_invite_match:IsOpen() and (not ViewManager.Instance:IsOpen(GuideModuleName.TeamPrepareView))then
		self.data:SetTeamInviteMatch(protocol)
	else
		self.quick_invite_match:Flush()
	end
end

function NewTeamWGCtrl:OpenQuickInviteMatch()
	if not self.quick_invite_match:IsOpen() then
		self.quick_invite_match:Open()
	else
		self.quick_invite_match:Flush()
	end
end

-- 打开收到邀请(区分招募)
function NewTeamWGCtrl:OpenSpecificInviteTeam()
	local team_info = NewTeamWGData.Instance:GetSpecificInviteTeamTopTeamInfo()
	if team_info == nil or IsEmptyTable(team_info) then return end

	local data = {}
	local inviter_str = string.format(Language.NewTeam.SpecifyInviter, team_info.inviter_name)
	local target_str = string.format(Language.NewTeam.SpecifyInviteTeamTypeName, self:GetTeamTargetStr(team_info))

	data.ok_str = Language.NewTeam.AlertTipApply
	data.lable_str = inviter_str .. "\n" .. target_str
	data.ok_func = BindTool.Bind(self.OnAlertClickOK, self, team_info)
	data.cancel_func = BindTool.Bind(self.OnAlertClickCancel, self, team_info)
	data.countdown_func = BindTool.Bind(self.OnAlertCountDown, self, team_info)

	self.specify_invite_alert:SetDataAndOpen(data)
end

function NewTeamWGCtrl:OnAlertClickOK(info)
	self:SendInviteUserTransmitRet(info, 0)
end

function NewTeamWGCtrl:OnAlertClickCancel(info)
	self:SendInviteUserTransmitRet(info, 1)
end

function NewTeamWGCtrl:OnAlertCountDown(info)
	self:SendInviteUserTransmitRet(info, 1)
	self.specify_invite_alert:Close()
end

function NewTeamWGCtrl:SendInviteUserTransmitRet(invite_info, is_received)
	is_received = is_received or 1
	if not invite_info or not invite_info.inviter or invite_info.inviter == 0 then
		return
	end
	if is_received == 0 and Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(invite_info.inviter)
		if not is_my_zhandui then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.DifferentZhandui)
			return
		end
	end
	SocietyWGCtrl.Instance:SendInviteUserTransmitRet(invite_info.inviter, is_received, 0)
end

-- 获得队伍目标文本
function NewTeamWGCtrl:GetTeamTargetStr(team_info)
	local team_target_cfg = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_info.team_type, team_info.team_fb_mode) -- 队伍目标配置

	-- 是否显示地图名
	local map_name = Config_scenelist[Scene.Instance:GetSceneId()] and Config_scenelist[Scene.Instance:GetSceneId()].name or "" -- 获取当前所在地图名称
	local is_same_scene = false
    if team_info.team_type == TeamDataConst.GoalType.None then --是无目标 且场景id相同 且是boss场景
        is_same_scene = Scene.Instance:GetSceneId() == team_info.leader_scene_id and BossWGData.IsBossScene(Scene.Instance:GetSceneType())
    end
	local target_str = is_same_scene and map_name or Language.NewTeam.NoTeamTypeTarget --显示地图名或目标名
	local normal_target_name = (not IsEmptyTable(team_target_cfg) and team_target_cfg.team_type_name and team_target_cfg.team_type_name ~= "") and team_target_cfg.team_type_name or Language.NewTeam.NoTeamTypeTarget
    local team_type_name = is_same_scene and target_str or normal_target_name
	return team_type_name
end

function NewTeamWGCtrl:ClearQuickInviteTeamList()
	self.data:ClearQuickInviteTeamList()
	if self.quick_invite_team:IsOpen() then
		self.quick_invite_team:Close()
	end
end

function NewTeamWGCtrl:OpenPopMenu(menulabels, point, callback_func, callback_param)
	self.team_pop_menu:SetMenuData(menulabels)
	self.team_pop_menu:SetPosition(point)
	self.team_pop_menu:BindCallBack(callback_func, callback_param)
	self.team_pop_menu:Open()
end

-- --请求报名进入
-- function NewTeamWGCtrl:SendTeamFbSignUp(team_type, teamfb_mode)
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamFbSignUp)
-- 	protocol.team_type = team_type
-- 	protocol.teamfb_mode = teamfb_mode
-- 	protocol:EncodeAndSend()
-- end

function NewTeamWGCtrl:SetIsAutoPrepare(state)
    self.is_auto_prepare = state
end

--房间信息下发
function NewTeamWGCtrl:OnSCRoomInfo(protocol)
	self.data:SetRoomInfo(protocol)
	local need_open_prepare = not self.cur_not_fake_prepare
	---------------------------------------
	--策划说匹配到机器人的时候 需要做一个假的匹配过程 看起来真一点。。。。。。
	--一个人进入副本，匹配两个机器人不需要准备
	-- local robot_num = 0
	-- local is_prepare = 0
	-- for k,v in pairs(protocol.room_member_list) do
	-- 	if v.is_robert == 1 then
	-- 		robot_num = robot_num + 1
	-- 	elseif v.role_original_id == GameVoManager.Instance:GetMainRoleVo().role_id then
	-- 		is_prepare = v.fbroom_ready
	-- 	end
	-- end
	-- if SocietyWGData.Instance:GetIsTeamLeader() == 1 and is_prepare == 0 and not self.is_auto_prepare then
	-- 	SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
	-- 	self.is_auto_prepare = true
	-- 	return
	-- end

	-- if robot_num == 2 then
	-- 	if is_prepare == 0 then
	-- 		SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
	-- 	end
	-- 	return
	-- end

	-- if protocol.team_type == GoalTeamType.QingYuanFb and protocol.teamfb_mode == 0 then--情缘副本
	-- 	if MarryWGData.Instance:IsSingleEnterFB() then--单人进入
	-- 		SocietyWGCtrl.Instance:SendTeamReadyStateChange(1)
	-- 		return
	-- 	end
	-- end
	----------------------------------------
	----------------------------------------

	if protocol.teamfb_enter_timestamp > TimeWGCtrl.Instance:GetServerTime() and need_open_prepare then
		--打开等待准备
		if not ViewManager.Instance:IsOpen(GuideModuleName.TeamPrepareView) then
			local open_guide_data = FuBenPanelWGData.Instance:GetFuBenJiangHuGuideFlagUnClear()
			if open_guide_data ~= nil then
				local guide_data = Split(open_guide_data, "|")
				local id = guide_data[3] and tonumber(guide_data[3]) or 0
				if (id == 31 or id == 32) and tonumber(guide_data[2] or 0) + 15 * 60 > TimeWGCtrl.Instance:GetServerTime() then
					FuBenPanelWGData.Instance:ExistFuBenJiangHuGuideFlag()
					local guide_cfg = FunctionGuide.Instance:GetGuideCfgByTrigger(GuideTriggerType.FuBenPanelResult, tonumber(guide_data[1] or 0))
					if guide_cfg ~= nil then
						FunctionGuide.Instance:SetCurrentGuideCfg(guide_cfg)
					end
				end
			end
			GlobalEventSystem:Fire(OtherEventType.MatchSuccess)
			ViewManager.Instance:Open(GuideModuleName.TeamPrepareView)
		end
	end
	ViewManager.Instance:FlushView(GuideModuleName.TeamPrepareView)

    --如果在匹配副本，刷新房间信息
    local scene_type = Scene.Instance:GetSceneType()
    if NewTeamWGData.Instance:GetIsInRoomScene(scene_type) then
        MainuiWGCtrl.Instance:FlushRoom()
    end
end

--离开房间
function NewTeamWGCtrl:OnSCOutOfRoom(protocol)
	local old_room_index = NewTeamWGData.Instance:GetRoomIndex()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if protocol.room_index == old_room_index then
		--local who_str
		local is_me = role_id == protocol.role_original_id
		--自己退出房间
		if is_me then
			if ViewManager.Instance:IsOpen(GuideModuleName.TeamPrepareView) then
				ViewManager.Instance:Close(GuideModuleName.TeamPrepareView)
			end
			--who_str = Language.NewTeam.NoticeYour
			NewTeamWGData.Instance:ClearRoomInfo()
		else
			--who_str = protocol.role_name
		end
		--if Scene.Instance:GetSceneType() == SceneType.Common then
		--	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.CanelPrepareProcedure, who_str))
		--end
	end
end

--前往目标，is_cancel：是否取消，1 取消 0 进入
function NewTeamWGCtrl:StartOrCancelTeamFB(is_cancel)
	local protocol = ProtocolPool.Instance:GetProtocol(CSStartOrCancelTeamFB)
	protocol.is_cancel = is_cancel
	protocol:EncodeAndSend()
end

function NewTeamWGCtrl:OpenTeamMatchView()
	-- if not ViewManager.Instance:IsOpen(GuideModuleName.TeamMatchView) then
	-- 	ViewManager.Instance:Open(GuideModuleName.TeamMatchView)
	-- end
end

function NewTeamWGCtrl:CloseTeamMatchView()
	-- if ViewManager.Instance:IsOpen(GuideModuleName.TeamMatchView) then
	-- 	ViewManager.Instance:Close(GuideModuleName.TeamMatchView)
	-- end
end

--某人不能进入组队副本提示
-- 如果这个有问题的人是角色本身，那就提醒“【你】因为什么原因不能进"
-- 分两种情况，
-- 1.你当前是队长，弹窗，提示是否踢掉他。
-- 2.你当前不是队长，就弹提示”【XXX】因为什么原因不能进“
function NewTeamWGCtrl:OnSCSomebodyCannotEnterTeamFbNotice(protocol)
    local is_me = RoleWGData.Instance:InCrossGetOriginUid() == protocol.role_id
    local who_str = is_me and Language.NewTeam.NoticeYour or protocol.role_name
    local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
    if is_me then

    else

    end

	--所有队员都弹的提示
	if protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_ENTER_TIMES then    -- 进入次数不足
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.NoticeTips0, who_str))
		return
	elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_LEVEL then      -- 等级未达到
		local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(protocol.team_type, protocol.teamfb_mode)
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.NoticeTips2, who_str, goal_info.team_type_name))
		return
	end

	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(protocol.team_type, protocol.teamfb_mode)

	local str
	-- 自己是队长
	if is_leader then
		--“谁”是其他队员， 弹窗
		if not is_me then
			if not self.notice_kick_alert then
				self.notice_kick_alert = Alert.New()
			end
			if protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_ITEM then       -- 物品材料（门票）不足
				local item_config = ItemWGData.Instance:GetItemConfig(protocol.reason_param)
				if item_config then
					local item_name = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
					str = string.format(Language.NewTeam.NoticeTips3, who_str, item_name)
				end
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_GOLD then       -- 合并次数花费元宝不足
				str = string.format(Language.NewTeam.NoticeTips4, who_str)
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_OFFLINE then    -- 不在线
				str = string.format(Language.NewTeam.NoticeTips5, who_str)
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_STATUS then     -- 当前状态不能进入
				str = string.format(Language.NewTeam.NoticeTips6, who_str)
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_OTHER then      -- 其他
            elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_IN_FB then 		-- 在副本中
                local scene_cfg = ConfigManager.Instance:GetSceneConfig(tonumber(protocol.reason_param))
                local scene_str = Language.NewTeam.Fuben
                if scene_cfg and scene_cfg.name then
                    scene_str = scene_cfg.name
                end
				str = string.format(Language.NewTeam.NoticeTips7, who_str, scene_str)
			end

			self.notice_kick_alert:SetLableString(str)
			self.notice_kick_alert:SetCancelFunc(function()
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CancelBaoMingAlert)
			end)
			self.notice_kick_alert:SetOkFunc(function()
                SocietyWGCtrl.Instance:SendKickOutOfTeam(protocol.role_id)
                self.data:SetIsMatching(0)
				-- local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
				-- local is_match = NewTeamWGData.Instance:GetIsMatching()
				-- local operate = is_match and 1 or 0
				-- if not is_match then
				-- 	NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, team_type, fb_mode)
				-- end
			end)
			self.notice_kick_alert:Open()
		else
			--“谁”是自己弹提示
			if protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_ITEM then       -- 物品材料（门票）不足
				local item_config = ItemWGData.Instance:GetItemConfig(protocol.reason_param)
				if item_config then
					local item_name = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
					str = string.format(Language.NewTeam.NoticeTipsLeaderMe3, who_str, item_name, goal_info.team_type_name)
				end
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_GOLD then       -- 合并次数花费元宝不足
				--SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.NoticeTips4, who_str))
				str = string.format(Language.NewTeam.NoticeTipsLeaderMe4, who_str, goal_info.team_type_name)
			--elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_OFFLINE then    -- 不在线
			--	str = string.format(Language.NewTeam.NoticeTipsLeaderMe5, who_str)
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_STATUS then     -- 当前状态不能进入
				--SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.NoticeTips6, who_str))
				str = string.format(Language.NewTeam.NoticeTipsLeaderMe6, who_str, goal_info.team_type_name)
			elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_OTHER then      -- 其他
            elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_IN_FB then      -- 在副本中
                local scene_cfg = ConfigManager.Instance:GetSceneConfig(tonumber(protocol.reason_param))
                local scene_str = Language.NewTeam.Fuben
                if scene_cfg and scene_cfg.name then
                    scene_str = scene_cfg.name
                end
				str = string.format(Language.NewTeam.NoticeTipsLeaderMe7, who_str, scene_str, goal_info.team_type_name)
			end
			if str ~= nil and str ~= "" then
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
		end
	else
		if protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_ITEM then       -- 物品材料（门票）不足
			local item_config = ItemWGData.Instance:GetItemConfig(protocol.reason_param)
			if item_config then
				local item_name = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
				str = string.format(Language.NewTeam.NoticeTipsOtherTarget3, who_str, item_name, goal_info.team_type_name)
			end
		elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_GOLD then       -- 合并次数花费元宝不足
			str = string.format(Language.NewTeam.NoticeTipsOtherTarget4, who_str, goal_info.team_type_name)
		elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_OFFLINE then    -- 不在线
			str = string.format(Language.NewTeam.NoticeTipsOtherTarget5, who_str, goal_info.team_type_name)
		elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_STATUS then     -- 当前状态不能进入
			str = string.format(Language.NewTeam.NoticeTipsOtherTarget6, who_str, goal_info.team_type_name)
		elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_OTHER then      -- 其他
        elseif protocol.reason == TEAM_FB_CAN_NOT_ENTER_TYPE.TEAM_FB_CAN_NOT_ENTER_TYPE_IN_FB then      -- 在副本中
            local scene_cfg = ConfigManager.Instance:GetSceneConfig(tonumber(protocol.reason_param))
            local scene_str = Language.NewTeam.Fuben
            if scene_cfg and scene_cfg.name then
                scene_str = scene_cfg.name
            end
			str = string.format(Language.NewTeam.NoticeTipsOtherTarget7, who_str,scene_str, goal_info.team_type_name)
		end
		if str ~= nil and str ~= "" and not is_me then --自己会弹两条提示，去掉一条
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end
end

function NewTeamWGCtrl:CheckIsOpenPingTaiIndex()
	if self.m_view:IsOpen() and self.m_view:GetShowIndex() == TabIndex.team_pingtai then
		return true
	end
	return false
end
function NewTeamWGCtrl:CheckIsOpenMyTeamIndex()
	if self.m_view:IsOpen() and self.m_view:GetShowIndex() == TabIndex.team_my_team then
		return true
	end
	return false
end
function NewTeamWGCtrl:CheckIsOpenNearIndex()
	if self.m_view:IsOpen() and self.m_view:GetShowIndex() == TabIndex.team_near then
		return true
	end
	return false
end

function NewTeamWGCtrl:ChangeMustCheck(is_must_check)
	if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeMustCheck)
		send_protocol.must_check = is_must_check
		send_protocol:EncodeAndSend()
	end
end

--打開喊话界面
function NewTeamWGCtrl:OpenTalkView(data)
	self.team_talk_view:SetData(data)
	self.team_talk_view:Open()
end

function NewTeamWGCtrl:CloseTalkView()
	if self.team_talk_view:IsOpen() then
		self.team_talk_view:Close()
	end
end

--显示喊话界面
function NewTeamWGCtrl:ShowTalkView(is_cross_channel, shouting_content)
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level < COMMON_CONSTS.CREATE_TEAM_LIMIT then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.CREATE_TEAM_LIMIT))
		return
	end

	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if SocietyWGData.Instance:GetTeamMemberCount() >= (GoalQingYuanFbMaxCount[now_team_type] or GameEnum.MAX_TEAM_MEMBER_NUMS) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ManyTower.TeamIsMaxMember)
		return
	end

	if CountDownManager.Instance:HasCountDown("normal_team_world_talk") then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CDWorldTalk)
		return
	end

	self.recruit_view:Close()
	self:SendWordTalk(is_cross_channel, shouting_content)
end

--发送喊话
function NewTeamWGCtrl:SendWordTalk(is_cross_channel, shouting_content)
	local teamIndex = SocietyWGData.Instance:GetTeamIndex()
	if teamIndex then
		local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
		local role_min_level, role_max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		--改为服务端处理内容
		self:SendTeamTargetShouting(team_type, fb_mode, role_min_level, role_max_level, 0, is_cross_channel, shouting_content)

		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.WorldInvite1)
		GlobalEventSystem:Fire(TeamWorldTalk.NEW_TEAM_WORLD_TALK)
		self:CloseTalkView()

		if not (team_type == 0 and fb_mode == 1) then
			--如果当前无目标喊话，则不发送快速加入请求
			ChatWGCtrl.Instance:SendTeamInviteChannelChat()
		end
	end
end

-- is_special_index:特殊组队自定义喊话 is_special_index = 1
function NewTeamWGCtrl:SendTeamTargetShouting(team_type, fb_mode, role_min_level, role_max_level, is_special_index, is_cross_channel, shouting_content)
	-- print_error("组队喊话请求", team_type, fb_mode, role_min_level, role_max_level, is_special_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamTargetShouting)
	protocol.team_type = team_type or 0
	protocol.fb_mode = fb_mode or 0
	protocol.role_min_level = role_min_level or 0
	protocol.role_max_level = role_max_level or 0
	protocol.is_special_index = is_special_index or 0
	protocol.is_cross_channel = is_cross_channel and 1 or 0
	local content = shouting_content or ""
	protocol.shouting_notice_size = string.len(content)
	protocol.shouting_content = content
	protocol:EncodeAndSend()
end

--队长标记变化
function NewTeamWGCtrl:OnSCTeamLeaderMarkChange(protocol)
	local role = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if role then
		role:SetAttr("is_team_leader", protocol.is_team_leader)
	end
end

function NewTeamWGCtrl:ChangNotGoalWhenOutFromOtherFb()
	local goal_cfg = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
	NewTeamWGData.Instance:SetTeamTypeAndMode(goal_cfg.team_type, goal_cfg.fb_mode)
	 --组队的最大等级改为等级排行的第一名
	local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
	NewTeamWGData.Instance:SetTeamLimitLevel(goal_cfg.role_min_level, top_user_level)
	if 1 == SocietyWGData.Instance:GetIsInTeam() and SocietyWGData.Instance:GetIsTeamLeader() == 1 then
		NewTeamWGCtrl.Instance:SendChangeTeamLimit(goal_cfg.team_type, goal_cfg.fb_mode, goal_cfg.role_min_level, top_user_level)
	end
end

function NewTeamWGCtrl:CheckAddFriend(scene_type, member_list)
	-- local member_list = SocietyWGData.Instance:GetTeamMemberList()  --缓存副本结束时候的队伍信息，防止有人退队，信息更新
	if IsEmptyTable(member_list) then
		return
	end
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local count = 0
	local add_frient_list = {}
	local id = UserVo.IsCrossServer(role_vo.role_id) and role_vo.origin_uid or role_vo.role_id
	for k, v in pairs(member_list) do
		if not SocietyWGData.Instance:CheckIsFriend(v.orgin_role_id) and v.server_id == role_vo.server_id
			and v.orgin_role_id ~= role_vo.origin_uid then
	       table.insert(add_frient_list, v)
	       count = count + 1
	    end
	end
	if count > 0 then
		local config = FuBenWGData.GetFbSceneConfig(scene_type)
		local name = config and string.format(Language.Common.TeamAddFriend, config.name) or Language.Common.AddFriend
		self.team_add_friend:SetData(add_frient_list, name)
		self.team_add_friend:Open()
	end
end

function NewTeamWGCtrl:FLushPrePareTimes()
	if self.jf_prepare_view:IsOpen() and self.jf_prepare_view:IsLoadedIndex(0) then
		self.jf_prepare_view:Flush(0,"prepare_times")
	end
end

-- 请求合并
function NewTeamWGCtrl:SendTeamMergeReq(role_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamMergeReq)
	protocol.role_id = role_id
	protocol:EncodeAndSend()
end

--收到合并消息
function NewTeamWGCtrl:OnSCTeamMergeReqRet(protocol)
	self.data:AddHeBingInfo(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, self.data:GetHeBingCount(), function ()
		self:OpenHeBingView()
		return true
	end)
	GlobalEventSystem:Fire(OtherEventType.TeamHeBingInfo)
end

--打开合并界面
function NewTeamWGCtrl:OpenHeBingView()
	if not self.hebing_view:IsOpen() then
		self.hebing_view:Open()
	end
end
--关闭合并界面
function NewTeamWGCtrl:CloseHeBingView()
	if self.hebing_view:IsOpen() then
		self.hebing_view:Close()
	end
end

--合并界面移除一条消息
function NewTeamWGCtrl:RemoveHeBingItem(role_id)
	self.data:RemoveHeBingItem(role_id)
	if self.data:GetHeBingCount() <= 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
		if self.hebing_view:IsOpen() then
			self.hebing_view:Close()
		end
		return
	end
	if self.hebing_view:IsOpen() then
		self.hebing_view:Flush()
	end
end

function NewTeamWGCtrl:GetHeBingTodayCheckActive()
	return self.hebing_view:GetTodayCheckActive()
end

function NewTeamWGCtrl:CreateHeBingRoleHeadCell(role_id, role_name, prof,sex, is_online, node,plat_type, server_id, plat_name)
    self.hebing_view:CreateRoleHeadCell(role_id, role_name, prof,sex, is_online, node,plat_type, server_id, plat_name)
end

--查询玩家队伍信息
function NewTeamWGCtrl:QueryTeamInfo(role_id, callback)
	if role_id < 0 or nil == callback then
		return
	end

	for k, v in pairs(self.request_callback_list) do
		if v.callback == callback then
			return
		end
	end

	table.insert(self.request_callback_list, {["role_id"] = role_id, ["callback"] = callback,})

	local protocol = ProtocolPool.Instance:GetProtocol(CSQueryRoleTeamInfo)
	protocol.role_id = role_id
	protocol:EncodeAndSend()
end

function NewTeamWGCtrl:OnSCQueryRoleTeamInfoRet(protocol)
	self:TeamInfoReqCallBack(protocol)
end

function NewTeamWGCtrl:TeamInfoReqCallBack(protocol)
	local count = #self.request_callback_list
	if count > 0 then
		local info = nil
		for i = count, 1, -1 do
			info = self.request_callback_list[i]
			if info.role_id == protocol.role_id then
				info.callback(protocol)
				table.remove(self.request_callback_list, i)
			end
		end
	end
end


--处理申请
function NewTeamWGCtrl:RemoveTeamJoinReq(role_id)
	if nil == role_id then
		return
	end

	SocietyWGData.Instance:RemoveTeamJoinReq(role_id)
	if SocietyWGData.Instance:GetReqTeamListSize() <= 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView)
		MainuiWGCtrl.Instance:FlushView(0, "team_cell_flush")
		if self.apply_view:IsOpen() then
			self.apply_view:Close()
		end
		return
	end

	self.apply_view:Flush()
end

function NewTeamWGCtrl:OnDisConnectedGameServer(custom_disconnect_reason)
	self:CloseTeamMatchView()
end

--设置巅峰等级
function NewTeamWGCtrl.CheckDianFengLevel(level, icon_node, text_node)
	local isv, level_str = RoleWGData.Instance:GetDianFengLevel(level)
	if icon_node then
		icon_node:SetActive(isv)
	end

	if isv then
        text_node.text.text = level_str
    else
        text_node.text.text = string.format(Language.Common.LevelNormal, level_str)
    end
end


function NewTeamWGCtrl:F2SendTeamFuBenEnter(team_type,fb_mode,max_member, no_tips)
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end
	max_member = max_member or 5

	local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	if not info then
		return
	end

	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0

	-- if no_tips then
	self.cur_not_fake_prepare = false --no_tips
	-- end
	local quick_in_fb = 0 	-- no_tips and 1 or 0

	local go_to_team_func = function ()
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
        local top_lv = info.role_max_level or top_user_level
        if is_not_in_team then
            NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
        else
            NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)
        end
        NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
        SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表
        ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
	end

    local go_to_fuben_func = function ()
        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
        -- if not is_not_in_team then
            local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
            local top_lv = info.role_max_level or top_user_level
                -- NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
            NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, top_lv)
			if is_not_in_team then
				NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
			else
				NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)
			end
            -- NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)
        -- end

		NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode,nil,quick_in_fb)
	end

    local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
	if is_not_in_team then --不在队伍
		if no_tips then
			go_to_fuben_func()
        else
            -- if team_type == GoalTeamType.Exp_FuMoZhanChuan then --经验本
            --     local str = string.format(Language.NewTeam.ExpFbLackOFMembers, max_member)
            --     FuBenPanelWGCtrl.Instance:OpenExpAlertView(str, go_to_fuben_func, go_to_team_func)
            -- else
            -- end
			if not self.enter_fuben_tips then
				self.enter_fuben_tips = Alert.New()
			end
			local str = string.format(Language.NewTeam.LackOFMembers, max_member, ToColorStr(info.team_type_name, COLOR3B.DEFAULT_NUM))
			self.enter_fuben_tips:SetLableString(str)
			self.enter_fuben_tips:SetCancelFunc(go_to_team_func)
			self.enter_fuben_tips:SetOkFunc(go_to_fuben_func)
			self.enter_fuben_tips:SetCancelString(Language.NewTeam.GoToMakeTeam)
			self.enter_fuben_tips:SetOkString(Language.NewTeam.EnterDirectly)
			self.enter_fuben_tips:Open()
			--go_to_fuben_func()
		end
		self.is_auto_prepare = false
	else
		if not 1 == SocietyWGData.Instance:GetIsTeamLeader() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanDo)
			return
		end
		self.is_auto_prepare = false
		if is_match then
			NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode, MATCH_TYPE.MATCH_TYPE_ONLY_ROBOT,quick_in_fb)
		else
			--组队的最大等级改为等级排行的第一名
			
			local team_list = SocietyWGData.Instance:GetTeamMemberList()
			local mamber_num = #team_list
			if max_member > mamber_num then
				if no_tips then
					go_to_fuben_func()
                else
                    -- if team_type == GoalTeamType.Exp_FuMoZhanChuan then --经验本
                    --     local str = string.format(Language.NewTeam.ExpFbLackOFMembers, max_member)
                    --     FuBenPanelWGCtrl.Instance:OpenExpAlertView(str, go_to_fuben_func, go_to_team_func)
                    -- else
                    -- end
					if not self.enter_fuben_tips then
						self.enter_fuben_tips = Alert.New()
					end
					local str = string.format(Language.NewTeam.LackOFMembers, max_member, ToColorStr(info.team_type_name, COLOR3B.DEFAULT_NUM))
					self.enter_fuben_tips:SetLableString(str)
					self.enter_fuben_tips:SetCancelFunc(go_to_team_func)
					self.enter_fuben_tips:SetOkFunc(go_to_fuben_func)
					self.enter_fuben_tips:SetCancelString(Language.NewTeam.GoToMakeTeam)
					self.enter_fuben_tips:SetOkString(Language.NewTeam.EnterDirectly)
					self.enter_fuben_tips:Open()
					--go_to_fuben_func()
				end
            else
                go_to_fuben_func()
			end
		end
		
	end
end

function NewTeamWGCtrl:FlushTextInvite()
	if self.invite_view:IsOpen() and self.invite_view:IsLoaded() then
	    self.invite_view:FlushTextInvite()
    end
end

function NewTeamWGCtrl:GetTeamViewIsLoaded()
	return self.m_view:IsOpen() and self.m_view:IsLoaded()
end

function NewTeamWGCtrl:ExitTeam()
	local content = ""
    local main_role = Scene.Instance:GetMainRole()
    content = string.format(Language.NewTeam.QuitTeamStr, main_role.vo.name, main_role.vo.level)
    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1,nil,true,false)
	SocietyWGCtrl.Instance:SendExitTeam()
	NewTeamWGData.Instance:ClearTeamInfo()
end

function NewTeamWGCtrl:OpenTeamCallTogether(call_data)
	self.team_call_together:SetDataAndOpen(call_data)
end

------------------------------------分身--------------------------------
function NewTeamWGCtrl:OnCSTeamFenshenInfo(protocol)
	self.data:SetTeamFenshenInfo(protocol)

	if self.clone_help_reward_view:IsOpen() then
		self.clone_help_reward_view:Flush()
	end
end

function NewTeamWGCtrl:OpenCloneHelpRewardView()
	if not self.clone_help_reward_view:IsOpen() then
		self.clone_help_reward_view:Open()
	end
end