HundredEquipRatio = HundredEquipRatio or BaseClass(SafeBaseView)

function HundredEquipRatio:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg()

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "hundredequip_ratio")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function HundredEquipRatio:LoadIndexCallBack()
    local bundle, asset = ResPath.GetRawImagesJPG("a3_bbdz_dbj_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
    self.node_list.title_view_name.text.text = Language.HundredEquip.ViewName2

    XUI.AddClickEventListener(self.node_list.button_buy, BindTool.Bind1(self.OnClickBuy, self))
    XUI.AddClickEventListener(self.node_list.btn_go_map, BindTool.Bind1(self.OnClickGoMap, self))
    XUI.AddClickEventListener(self.node_list.image_cost_icon, BindTool.Bind1(self.OnClickCostItem, self))
    XUI.AddClickEventListener(self.node_list.btn_open_rank, BindTool.Bind1(self.OnClickOpenRankBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close_rank, BindTool.Bind1(self.OnClickCloseRankBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_my_rank_award, BindTool.Bind1(self.OnClickMyRankAwardBtn, self))

    -- 策划说只保留一个，不显示选择列表
    -- self.scroll_ratio_list = AsyncListView.New(HundredEquipRatioRender, self.node_list.scroll_ratio_list)
    -- self.scroll_ratio_list:SetSelectCallBack(BindTool.Bind1(self.OnClickRatioCalllBack, self))
    self.scroll_recharge_reward_list = AsyncListView.New(ItemCell, self.node_list.scroll_recharge_reward_list)
    self.scroll_recharge_reward_list:SetStartZeroIndex(true)
    self.scroll_map_rank_list = AsyncListView.New(HundredequipRankCell, self.node_list.scroll_map_rank_list)
    self.scroll_map_rank_list:SetStartZeroIndex(true)

    local bundle = "uis/view/hundred_equip_ui_prefab"
	local asset = "hundredequip_ratio_award_render"
    self.scroll_map_reward_list = AsyncBaseGrid.New()
    self.scroll_map_reward_list:CreateCells({list_view = self.node_list["scroll_map_reward_list"], itemRender = HundredEquipRatioAwardRender,
    assetBundle = bundle, assetName = asset, change_cells_num = 1, col = 4})

    self.select_cell_index = 1
end

function HundredEquipRatio:ReleaseCallBack()
    self.select_cell_index = nil

    if self.scroll_map_reward_list then
        self.scroll_map_reward_list:DeleteMe()
        self.scroll_map_reward_list = nil
    end

    -- if self.scroll_ratio_list then
    --     self.scroll_ratio_list:DeleteMe()
    --     self.scroll_ratio_list = nil
    -- end

    if self.scroll_recharge_reward_list then
        self.scroll_recharge_reward_list:DeleteMe()
        self.scroll_recharge_reward_list = nil
    end

    if self.scroll_map_rank_list then
        self.scroll_map_rank_list:DeleteMe()
        self.scroll_map_rank_list = nil
    end
end

function HundredEquipRatio:OnFlush()
    self.list_data = HundredEquipWGData.Instance:GetRechargeTaskList()
    -- self.scroll_ratio_list:SetDataList(self.list_data)
    self:SetFlushView()
end

function HundredEquipRatio:OnClickRatioCalllBack(cell, cell_index)
    local data = cell:GetData()
	if not data then return end

    self.select_cell_index = cell_index
    self:SetFlushView()
end

function HundredEquipRatio:SetFlushView()
    local slt_data = self.list_data[self.select_cell_index]
    if not slt_data then
        return
    end
    local bg_name = "a2_bbbz_bj12"
    local award_bg_name = "a2_bbbz_bj13"
    local txt_name = "a2_bbbz_ysj5"
    local cfg = slt_data.cfg
    local title_name = "a2_bbbz_ysj4"
    if self.select_cell_index > 1 then
        bg_name = "a2_bbbz_bj1"
        award_bg_name = "a2_bbbz_bj15"
        txt_name = "a2_bbbz_ysj6"
        title_name = "a2_bbbz_ysj7"
    end
    -- self.node_list.rawimage_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(bg_name))
    -- self.node_list.rawimage_award_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(award_bg_name))

    -- self.node_list.rawimage_ratio_title.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(title_name))
    local real_recharge = HundredEquipWGData.Instance:GetRealRechargeNum()
    local price = RoleWGData.GetPayMoneyStr(cfg.recharge_num)
    local cur_price = RoleWGData.GetPayMoneyStr(real_recharge)
    local condition_txt = string.format(Language.HundredEquip.RechargeTxt1, price, cur_price)
    self.node_list.text_receive_condition.text.text = condition_txt
    XUI.SetGraphicGrey(self.node_list.button_buy, slt_data.state == 1)
    local btn_name = ""
    if real_recharge < cfg.recharge_num then
        btn_name = Language.HundredEquip.RechargeTxt3
    else
        btn_name = Language.HundredEquip.RechargeTxt4
    end
    self.node_list.text_price.text.text = btn_name
    self.scroll_recharge_reward_list:SetDataList(cfg.reward)

    local enter_map_cfg = HundredEquipWGData.Instance:GetEnterMapCfgByType(cfg.map_type)
    if enter_map_cfg then
        self.scroll_map_reward_list:SetDataList(enter_map_cfg.drop_item_list)

        local item_tab = enter_map_cfg.reward
        local has_count = ItemWGData.Instance:GetItemNumInBagById(item_tab.item_id)
        self.node_list.text_cost_money.text.text = string.format("%s/%s", has_count, item_tab.num)

        local image_cost_icon = ItemWGData.Instance:GetItemIconByItemId(item_tab.item_id)
        self.node_list.image_cost_icon.image:LoadSprite(ResPath.GetItem(image_cost_icon))
    end

    self.node_list.image_buy_remind:SetActive(slt_data.state == 0 and real_recharge >= cfg.recharge_num)
    local fuben_red = HundredEquipWGData.Instance:RatioGoToMapRed(cfg.map_type)
    self.node_list.image_fuben_red:SetActive(fuben_red)
end

function HundredEquipRatio:FlushRank()
    local my_rank_info = HundredEquipWGData.Instance:GetMyRankInfo()
    self.node_list.text_my_rank.text.text = string.format(Language.HundredEquip.MyRank, my_rank_info.my_rank or 0)
    self.node_list.text_my_score.text.text = string.format(Language.HundredEquip.MyScore, my_rank_info.my_wave or 0)

    local rank_info_list = HundredEquipWGData.Instance:GetRankInfoList()
    self.scroll_map_rank_list:SetDataList(rank_info_list)
end

function HundredEquipRatio:OnClickBuy()
    local slt_data = self.list_data[self.select_cell_index]
    if not slt_data then
        return
    end

    local real_recharge = HundredEquipWGData.Instance:GetRealRechargeNum()
    if real_recharge < slt_data.cfg.recharge_num then
        ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
    elseif slt_data.state == 0 then
        HundredEquipWGCtrl.Instance:SendHundredEquipRequest(HUNDREDFOLD_DROP_OPERATE_TYPE.HUNDREDFOL_REAL_RECHARGE_REWARD, slt_data.cfg.seq)
    end
end

function HundredEquipRatio:OnClickGoMap()
    local slt_data = self.list_data[self.select_cell_index]
    if not slt_data then
        return
    end
    local enter_map_cfg = HundredEquipWGData.Instance:GetEnterMapCfgByType(slt_data.cfg.map_type)
    local item_tab = enter_map_cfg.reward
    local has_count = ItemWGData.Instance:GetItemNumInBagById(item_tab.item_id)
    if has_count < item_tab.num then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HundredEquip.RechargeTxt8)
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(item_tab.item_id)
    TipWGCtrl.Instance:OpenAlertTips(string.format(Language.HundredEquip.RechargeTxt9, ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])), function ()
        if self:IsOpen() then
            FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.HUNDREDFOLD_DROP_FB, enter_map_cfg.type)
        end
    end)
end

function HundredEquipRatio:OnClickCostItem()
    local slt_data = self.list_data[self.select_cell_index]
    if not slt_data then
        return
    end

    local enter_map_cfg = HundredEquipWGData.Instance:GetEnterMapCfgByType(slt_data.cfg.map_type)
    if enter_map_cfg then
        local item_tab = enter_map_cfg.reward
        TipWGCtrl.Instance:OpenItem({item_id = item_tab.item_id})
    end
end

function HundredEquipRatio:OnClickOpenRankBtn()
    self.node_list.btn_open_rank:SetActive(false)
    self.node_list.rank_node:SetActive(true)

    local my_rank_info = HundredEquipWGData.Instance:GetMyRankInfo()
    if IsEmptyTable(my_rank_info) then
        HundredEquipWGCtrl.Instance:SendHundredEquipRequest(HUNDREDFOLD_DROP_OPERATE_TYPE.CS_E_HUNDREDFOLD_DROP_CLIENT_OPERATE_TYPE_RANK_INFO)
        return
    end
    self:FlushRank()
end

function HundredEquipRatio:OnClickCloseRankBtn()
    self.node_list.btn_open_rank:SetActive(true)
    self.node_list.rank_node:SetActive(false)
end

-- 展示排名奖励预览
function HundredEquipRatio:OnClickMyRankAwardBtn()
    HundredEquipWGCtrl.Instance:OpenAwardPreView()
end


HundredEquipRatioRender = HundredEquipRatioRender or BaseClass(BaseRender)

function HundredEquipRatioRender:OnFlush()
    if not self.data then
        return
    end

    local real_recharge = HundredEquipWGData.Instance:GetRealRechargeNum()
    local bg_name = self.data.cfg.seq > 0 and "a2_bbbz_pcc2" or "a2_bbbz_pcc"
    self.node_list.image_bg.image:LoadSprite(ResPath.GetHundredEquipImg(bg_name))
    self.node_list.text_name.text.text = self.data.cfg.name
    local fuben_red = HundredEquipWGData.Instance:RatioGoToMapRed(self.data.cfg.map_type)
    local award_red = self.data.state == 0 and real_recharge >= self.data.cfg.recharge_num
    self.node_list.image_remind:SetActive(fuben_red or award_red)
end

function HundredEquipRatioRender:OnSelectChange(is_select)
    self.node_list.image_slt:SetActive(is_select)
end


HundredEquipRatioAwardRender = HundredEquipRatioAwardRender or BaseClass(BaseRender)

function HundredEquipRatioAwardRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_parent)
    end
end

function HundredEquipRatioAwardRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function HundredEquipRatioAwardRender:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData(self.data)
end


-----------------------------------------------------------------------------------
-- 积分排行榜cell
-------------------------------------------------------------------------------------------
HundredequipRankCell = HundredequipRankCell or BaseClass(BaseRender)

function HundredequipRankCell:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local is_show_rank_icon = (data.rank > 0 and data.rank < 3)
    if is_show_rank_icon then
        self.node_list.icon_rank:SetActive(true)
        self.node_list.icon_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..data.rank))
        self.node_list.text_rank.text.text = ""
    else
        self.node_list.icon_rank:SetActive(false)
        self.node_list.text_rank.text.text = tostring(data.rank)
    end

    self.node_list.text_name.text.text = data.name
    self.node_list.text_score.text.text = data.wave
end