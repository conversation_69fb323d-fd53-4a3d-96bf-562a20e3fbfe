﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class DG_Tweening_LoopTypeWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>gin<PERSON>num(typeof(DG.Tweening.LoopType));
		<PERSON><PERSON>("Restart", get_Restart, null);
		<PERSON><PERSON>("Yoyo", get_Yoyo, null);
		<PERSON><PERSON>("Incremental", get_Incremental, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<DG.Tweening.LoopType>.Check = CheckType;
		StackTraits<DG.Tweening.LoopType>.Push = Push;
	}

	static void Push(IntPtr L, DG.Tweening.LoopType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(DG.Tweening.LoopType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Restart(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.LoopType.Restart);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Yoyo(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.LoopType.Yoyo);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Incremental(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.LoopType.Incremental);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		DG.Tweening.LoopType o = (DG.Tweening.LoopType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

