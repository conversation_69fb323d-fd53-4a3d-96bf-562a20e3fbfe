MarryPaiQiView = MarryPaiQiView or BaseClass(SafeBaseView)

function MarryPaiQiView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_paiqi")
end

function MarryPaiQiView:__delete()

end

function MarryPaiQiView:ReleaseCallBack()
	if self.paiqi_item then
		for k,v in pairs(self.paiqi_item) do
			v:DeleteMe()
		end
		self.paiqi_item = nil 
	end

	if self.marry_paiqi_list then
		self.marry_paiqi_list:DeleteMe()
		self.marry_paiqi_list = nil
	end

	if self.paiqi_handler_alert then
		self.paiqi_handler_alert:DeleteMe()
		self.paiqi_handler_alert = nil
	end
	if MarryWGData.Instance then
		MarryWGData.Instance:SetMarryTimeSeq(nil)
	end
	self.is_auto_huadong = nil
	self.item_data = nil

	-- if self.my_head_cell then
	-- 	self.my_head_cell:DeleteMe()
	-- 	self.my_head_cell = nil
	-- end

	-- if self.love_head_cell then
	-- 	self.love_head_cell:DeleteMe()
	-- 	self.love_head_cell = nil
	-- end
end

function MarryPaiQiView:OpenCallBack()
	-- MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_SCHEDULE_REQ)    -- 获取婚宴排期列表信息
end

function MarryPaiQiView:LoadCallBack()
	self.is_auto_huadong = true
	self:CreatePaiQiList()
	self:CreatePaiQiCell()
	self.node_list["btn_qingtie"].button:AddClickListener(BindTool.Bind(self.OnClickQingTie, self))
	--self.node_list["window_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.paiqi_handler_alert = Alert.New()
end

function MarryPaiQiView:OnFlush()
	local paiqi_data = MarryWGData.Instance:GetWeddingScheduleInfo()
    if IsEmptyTable(paiqi_data) then
		return
	end
	if self.marry_paiqi_list then
		self.marry_paiqi_list:SetDataList(paiqi_data,0)
		if self.is_auto_huadong then
			local index = 1
			for k,v in pairs(paiqi_data) do
				if 1 == v.is_mine then
					index = k
					self.is_auto_huadong = false
					break
				end
			end
			self:JunpIndex(index,#paiqi_data)
		end
	end
	if self.item_data and self.item_data.is_mine == 1 then
		local seq = self.item_data.seq or MarryWGData.Instance:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
		local applincant_data_num = MarryWGData.Instance:GetWeddingApplicantRed(seq)
		self.node_list["paiqi_red"]:SetActive(applincant_data_num > 0)
	end
end
--跳转
function MarryPaiQiView:JunpIndex(index,all_num)
	if self.marry_paiqi_list and all_num > 0 then
		if index > 10 then 
			self.node_list["ph_paiqi_list"].scroller:ReloadData(index/all_num)
		end
		self.marry_paiqi_list:SelectIndex(index)
	end
end
--索要请帖
function MarryPaiQiView:OnClickQingTie()
	if nil == self.item_data then
		return
	end
	if 1 == self.item_data.is_mine then
		--邀请宾客
		 local sequence = MarryWGData.Instance:GetCurWeddingSequence()
		if sequence ~= MARRY_WEDDING_TYPE.NONE then
			local seq = MarryWGData.Instance:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
			if self.item_data then
				seq = self.item_data.seq
			end
			MarryWGCtrl.Instance:OpenInviteView(seq)
			self:Close()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YuYueTips3)
		end
	else
		local is_firends = SocietyWGData.Instance:CheckIsFriend(self.item_data.role_id)
		if not is_firends then
			is_firends = SocietyWGData.Instance:CheckIsFriend(self.item_data.lover_id)
		end
		if self.paiqi_handler_alert and not is_firends then
			self.paiqi_handler_alert:SetShowCheckBox(false)
			self.paiqi_handler_alert:SetOkFunc(function ()
				SocietyWGCtrl.Instance:AddFriend(self.item_data.role_id,1)
				SocietyWGCtrl.Instance:AddFriend(self.item_data.lover_id,1)
				-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.AleardySendReq)
			end)
			self.paiqi_handler_alert:SetLableString(Language.Marry.ApplayAddFriends)
			self.paiqi_handler_alert:Open()
			return
		end
		if 1 == self.item_data.is_full then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.PaiQiHint2)
			return
		end
		MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.HUNYAN_OPERA_TYPE_APPLY,self.item_data.seq)
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.QPaiQiHint)
	end
	
end
--初始化奖励格子
function MarryPaiQiView:CreatePaiQiCell()
	local reward_item_list = MarryWGData.Instance:GetPaiQiRewardItemData()
	if nil == self.paiqi_item then
		self.paiqi_item = {}
	end
	for k,v in pairs(reward_item_list) do
		if nil == self.paiqi_item[k] then
			self.paiqi_item[k] = ItemCell.New()
			self.paiqi_item[k]:SetInstanceParent(self.node_list["ph_cell"])
		end
		self.paiqi_item[k]:SetData(v)
	end
end

--初始化预约列表
function MarryPaiQiView:CreatePaiQiList()
	self.marry_paiqi_list = AsyncListView.New(MarryPaiQiListItemRender, self.node_list["ph_paiqi_list"])
   	self.marry_paiqi_list:SetSelectCallBack(BindTool.Bind1(self.SelectPaiQiListItemCallBack, self))  --索要请柬
end

function MarryPaiQiView:SelectPaiQiListItemCallBack(item)
	if not item or not item:GetData() then return end
	self.item_data = item:GetData()
	if 1 == self.item_data.is_mine then
		self.node_list["btn_qingtie_name"].text.text = Language.Marry.MarryBtn2  --邀请宾客
		local seq = self.item_data.seq or MarryWGData.Instance:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
		local applincant_data_num = MarryWGData.Instance:GetWeddingApplicantRed(seq)
		self.node_list["paiqi_red"]:SetActive(applincant_data_num > 0)
	else
		self.node_list["btn_qingtie_name"].text.text = Language.Marry.MarryBtn1  --索要请柬
		self.node_list["paiqi_red"]:SetActive(false)
	end
	local is_can_q = true
	if 1 == self.item_data.is_full or 1 == self.item_data.is_invited then
		is_can_q = false
	end
	XUI.SetButtonEnabled(self.node_list["btn_qingtie"], is_can_q)
	self:SetRoleHeadInfo(self.item_data)
end

--加载结婚双方信息
function MarryPaiQiView:SetRoleHeadInfo(marry_info)
	if not marry_info or IsEmptyTable(marry_info) then
		print_error("加载结婚双方信息失败!")
		return
	end
	
	--自己的头像数据
	-- if not self.my_head_cell then
	-- 	self.my_head_cell = BaseHeadCell.New(self.node_list["my_head"])
	-- end

	-- local my_data = {}
	-- my_data.role_id = marry_info.role_id
	-- my_data.prof = marry_info.role_prof
	-- my_data.sex = marry_info.role_sex

	-- if my_data ~= nil then
	-- 	--self.my_head_cell:SetImgBg(marry_info.role_fashion_photforame > 0)
	-- 	self.my_head_cell:SetData(my_data)
	-- end

	--对象的头像数据
	-- if not self.love_head_cell then
	-- 	self.love_head_cell = BaseHeadCell.New(self.node_list["love_head"])
    -- end

	-- local love_data = {}
	-- love_data.role_id = marry_info.lover_id
	-- love_data.prof = marry_info.lover_prof
	-- love_data.sex = marry_info.lover_sex

	-- if love_data ~= nil then
	-- 	--self.love_head_cell:SetImgBg(marry_info.lover_fashion_photforame > 0)
	-- 	self.love_head_cell:SetData(love_data)
	-- end

    self.node_list["lbl_name_1"].text.text = marry_info.role_gamename
    self.node_list["lbl_name_2"].text.text = marry_info.lover_gamename
end



---MarryPaiQiListItemRender
MarryPaiQiListItemRender = MarryPaiQiListItemRender or BaseClass(BaseRender)
function MarryPaiQiListItemRender:__init()	
	self.is_select = false
	self.interactable = nil
end

function MarryPaiQiListItemRender:__delete()
	
end

function MarryPaiQiListItemRender:LoadCallBack()
end

function MarryPaiQiListItemRender:OnFlush()
	if not self.data then 
		return
	end

	--男左女右
	local role_sex = self.data.role_sex
	if role_sex == 1 then
		self.node_list["lbl_name"].text.text = self.data.role_gamename
		self.node_list["lbl_lover_name"].text.text = self.data.lover_gamename
	else
		self.node_list["lbl_name"].text.text = self.data.lover_gamename
		self.node_list["lbl_lover_name"].text.text = self.data.role_gamename
	end
	local str = Language.Marry.PaiQiHint5
	local bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d3c")
	if 1 == self.data.is_mine then
		str = Language.Marry.PaiQiHint1
	elseif 1 == self.data.is_full then
		str = Language.Marry.PaiQiHint2
		bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d3")
	elseif 1 == self.data.is_ongoing then
		str = Language.Marry.PaiQiHint3
		bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d2b")
	elseif 1 == self.data.is_invited then
		str = Language.Marry.PaiQiHint4
	end
	self.node_list.img_bg.image:LoadSprite(bundel, asset)
	self.node_list["lbl_time_tips"].text.text = str
end

function MarryPaiQiListItemRender:OnSelectChange(is_on)
	if self.node_list["img_hl"] then
		self.node_list["img_hl"]:SetActive(is_on)
		--self.node_list.img_bg:SetActive(not is_on)
	end
	if self.node_list["lbl_time"] then
        local time = MarryWGData.Instance:GetShowYuYueTime(self.data.seq)
        local flag = MarryWGData.Instance:GetMarryTodayCanOrderTomorrow()
        local str = flag and Language.Marry.Tomorrow or ""
        if time == "" then
            str = ""
        end
		self.node_list["lbl_time"].text.text = str .. time
	end
end