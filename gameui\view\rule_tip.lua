RuleTip = RuleTip or BaseClass(SafeBaseView)

local TEXT_SPACING = 30
local MAX_SCROLLVIEW_HEIGHT = 300

function RuleTip:__init()
	self.view_layer = UiLayer.Pop
	-- self.is_maskbg_button_click = true
	self.text_height = 0
	self:SetMaskBg(true, true)
	if RuleTip.Instance then
		ErrorLog("[RuleTip] Attemp to create a singleton twice !")
	end
	RuleTip.Instance = self
	self:LoadConfig()
end

function RuleTip:__delete()
	RuleTip.Instance = nil
end

function RuleTip:LoadConfig()
	self.view_name = "RuleTip"
	self:AddViewResource(0, "uis/view/tips/ruletip_prefab", "rule_tip")
	self.is_modal = false
end

function RuleTip:ReleaseCallBack()
	self.title_name = nil
	self.content = nil

	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
	end
	self.cell_list = nil
	self.list_view = nil
end

function RuleTip:LoadCallBack()
	self.cell_list = {}
	self.bg_width = self.node_list["Container"].rect.sizeDelta.x
	self.high_limit = self.node_list["size"].rect.sizeDelta.y

	self:CreatListView()
end

function RuleTip:OpenCallBack()
	self.text_height = 0
end

function RuleTip:CloseCallBack()
	self.text_height = 0
end

-- 创建List
function RuleTip:CreateArrListView(cell, data_index)
	 local data_index = data_index + 1

    if nil == self.cell_list[cell] then
        self.cell_list[cell] = ExplainTextItem.New(cell.gameObject)
    end
    self.cell_list[cell]:ShieldIcon(self.shield_icon)
	self.cell_list[cell]:SetData(self.content_t[data_index])
end

function RuleTip:GetNumberOfCells()
    return  #self.content_t
end

function RuleTip:CellSizeDel(data_index)
	local str = self.content_t[data_index + 1] or ""
	local str_tab = Split(str, "<n>")
	str = table.concat(str_tab, "\n")

	self.node_list["TestText"].text.text = str
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

	local hight = math.ceil(self.node_list["TestText"].rect.rect.height)
	self.text_height = self.text_height + hight + TEXT_SPACING
	self:ChangeHeight()
	return hight or 0
end

function RuleTip:SetTitle(title_name)
	self.title_name = title_name
end

function RuleTip:SetContent(content ,title_name, evil, explain_info,shield_icon)
	if title_name then
		self.title_name = title_name
	end
	self.evil = evil
	self.explain_info = explain_info
	self.shield_icon = shield_icon

	self.content = content
	self.content_t = Split(self.content, "\n")
	self:Open()
end

--自适应
function RuleTip:ChangeHeight()
	local scroll_node = self.node_list["ScrollView"]
	if scroll_node == nil then
		return
	end

	local text_height = 0
	text_height = self.text_height > MAX_SCROLLVIEW_HEIGHT and MAX_SCROLLVIEW_HEIGHT or self.text_height
	scroll_node.rect.sizeDelta = Vector2(scroll_node.rect.sizeDelta.x, text_height)
end

function RuleTip:CreatListView()
	self.list_view = self.node_list["ScrollView"]
	local list_view_delegate = self.list_view.list_simple_delegate
    list_view_delegate.CellSizeDel = BindTool.Bind(self.CellSizeDel, self)
    list_view_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.CreateArrListView, self)
end

function RuleTip:OnFlush()
	self:FlushCellView()
	self.node_list["title_text"].text.text = self.title_name or ""
	self.node_list["title_text"]:SetActive(nil ~= self.title_name)
	--self.node_list["title"]:SetActive(nil ~= self.title_name)
	self.node_list["Explain"]:SetActive(nil ~= self.evil or nil ~= self.explain_info)
	self.node_list["ExplainText2"]:SetActive(nil ~= self.explain_info)
	if self.evil then
		self.node_list["ExplainText"].text.text = self.evil
	elseif self.explain_info then
		self.node_list["ExplainText"].text.text = ""
		self.node_list["ExplainText2"].text.text = self.explain_info.text
		self.node_list["ExplainText2Value"].text.text = self.explain_info.value
		self.node_list["ExplainText2Img"].image:LoadSprite(self.explain_info.asset_name ,self.explain_info.bundle_name, function ()
			self.node_list["ExplainText2Img"].image:SetNativeSize()
		end)
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.size.rect)
end

function RuleTip:FlushCellView()
	if self.list_view then
		self.list_view.scroller:ReloadData(0)
	end

	self:ChangeHeight()
end


-------------------------------------------------------------------------------
ExplainTextItem = ExplainTextItem or BaseClass(BaseRender)
function ExplainTextItem:__init()
	self.shield_icon = false
end

function ExplainTextItem:__delete()
end

function ExplainTextItem:ShieldIcon(enabled)
	self.shield_icon = enabled
	--这个点不要了，暂时保留
	-- if self.node_list and self.node_list.point then
	-- 	self.node_list.point:SetActive(not enabled)
	-- end
end

function ExplainTextItem:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local str = ""
	if type(data) == "string" then
		local str_tab = Split(data, "<n>")
		str = table.concat(str_tab, "\n")
	end
	self.node_list["rule_text"].text.text = str

	self:ShieldIcon(self.shield_icon)
end
