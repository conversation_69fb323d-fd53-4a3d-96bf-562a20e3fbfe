BaoXiangThankTips = BaoXiangThankTips or BaseClass(SafeBaseView)

function BaoXiangThankTips:__init()
	self:SetMaskBg(false)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangThankTips:LoadConfig()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_thank_tips")
end

function BaoXiangThankTips:__delete()

end

function BaoXiangThankTips:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BaoXiangThankTips:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind1(self.OnClickOK, self))
	XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind1(self.OnClickClose, self))
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
end

function BaoXiangThankTips:OnFlush()
	if IsEmptyTable(self.data) then return end
	local item = self.data
	local quality = item.quality
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	-- local color = treasure_cfg and treasure_cfg.common_color or 1
	local bx_name = treasure_cfg and treasure_cfg.name or ""
	local str = string.format(Language.BiZuoBaoXiang.ThankDesc,bx_name)
	self.node_list["rich_dialog"].text.text = str
	local rewards_item = treasure_cfg.rewards_item
	local rewards_data = {}
	for i=0,#rewards_item do
		table.insert(rewards_data,rewards_item[i])
	end
	self.reward_list:SetDataList(rewards_data)
end

function BaoXiangThankTips:SetData(data)
	self.data = data
end

function BaoXiangThankTips:OnClickOK()
	self:OnClickClose()
end

function BaoXiangThankTips:OnClickClose()
	if IsEmptyTable(self.data) then return end
	local uid = self.data.uid
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REWARD_ACK,uid)
	local item = GuildBaoXiangWGData.Instance:GetBaoXiangThankData()
	if not IsEmptyTable(item) then 
		 self:SetData(item)
		 self:Flush()
		 return
	end
	self:Close()
end