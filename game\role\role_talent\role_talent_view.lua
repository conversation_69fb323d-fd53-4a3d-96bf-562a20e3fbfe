
local TalentType = {
	GONGJI = 1,
	FANGYU = 2,
	TONGYONG = 3,
	JINGTONG = 4,
}

SkillView.init_talent = false

function SkillView:InitRoleTalentView()
	self.talent_scroll_list = {}
	self.talent_skill_layout_list = {}
	self.cur_select_talent_type = 1

	-- self.node_list["btn_role_talent_uplevel"].button:AddClickListener(BindTool.Bind(self.OnClickRoleTalentUplevel, self))

	self.node_list["btn_role_talent_again"].button:AddClickListener(BindTool.Bind(self.OnClickRoleTalentAgain, self))
	self.node_list["btn_jinjie_req"].button:AddClickListener(BindTool.Bind1(self.OnClickJinJieReq, self))
	for i = 1, 3 do
		if self.talent_scroll_list[i] == nil then
			self.talent_scroll_list[i] = TalentSkillItemGroup.New(self.node_list['ph_role_talent' .. i])
			self.talent_scroll_list[i]:SetSkillType(i)
			self.talent_scroll_list[i]:SetParent(self)
		end
		self.node_list["btn_role_talent_" .. i].button:AddClickListener(BindTool.Bind(self.OnSwitchRoleTalentType, self, i))
	end

	XUI.AddClickEventListener(self.node_list["lbl_role_talent_item"], BindTool.Bind(self.OnClickRoleTalentItem, self))

	local cfg = RoleWGData.Instance:GetRoleTalentSkillResetItem()
	local bundle, asset = ResPath.GetItem(cfg.talent_point_id)
	self.node_list.lbl_role_talent_item.image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list.lbl_role_talent_item.image:SetNativeSize()
	end)
	self:FlushRoleTalentView()
	self.talent_scroll_list[1]:SelectFirst()

	local bundle, asset = ResPath.GetRawImagesPNG(string.format("a3_jn_rw_icon_%d", self.cur_select_talent_type))
	if self.node_list.RawImage_tongyong then
	 	self.node_list["btn_role_talent_type_raw"].raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["btn_role_talent_type_raw"].raw_image:SetNativeSize()
		end)
	end
end
function SkillView:OnSwitchRoleTalentType(index)
	self.cur_select_talent_type = index
	for i = 1, 3 do
		self.node_list["btn_role_talent_" .. i]:SetActive(true)
		self.talent_scroll_list[i]:SetActive(index == i)
		self.talent_scroll_list[i]:CurSelectTalentSkillType(index)
		self.node_list["btn_role_talent_select_" .. i]:SetActive(index == i)
	end
	self.node_list["btn_role_talent_" .. self.cur_select_talent_type]:SetActive(false)
	self.talent_scroll_list[index]:SelectFirst()

	local bundle, asset = ResPath.GetRawImagesPNG(string.format("a3_jn_rw_icon_%d", self.cur_select_talent_type))
	if self.node_list.RawImage_tongyong then
	 	self.node_list["btn_role_talent_type_raw"].raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["btn_role_talent_type_raw"].raw_image:SetNativeSize()
		end)
	end
end

function SkillView:DeleteRoleTalentView()
	if self.talent_skill_layout_list then
		for k,v in pairs(self.talent_skill_layout_list)do
			v:DeleteMe()
		end
		self.talent_skill_layout_list = nil
	end

	if self.reset_alert ~= nil then
		self.reset_alert:DeleteMe()
		self.reset_alert = nil
	end

	self.reset_role_talent_flag = nil
	self.show_skill_layout = nil

	if self.talent_scroll_list then
		for k,v in pairs(self.talent_scroll_list) do
			v:DeleteMe()
		end
		self.talent_scroll_list = nil
	end

	self.buy_item_ing = nil
	self.isOpenJingTong = nil
end

-- 刷新左边按钮
function SkillView:FlushRoleTalentView()
	for i=1,3 do
		if self.talent_scroll_list[i] ~= nil then
			self.talent_scroll_list[i]:Flush()
		end
	end

	local count_1 = RoleWGData.Instance:GetActTalentCount(1)
	local count_2 = RoleWGData.Instance:GetActTalentCount(2)
	local count_3 = RoleWGData.Instance:GetActTalentCount(3)
	local count = count_1 + count_2 + count_3

	-- self.node_list.user_num_1.text.text = count_1
	-- self.node_list.user_num_2.text.text = count_2
	-- self.node_list.user_num_3.text.text = count_3
	-- self.node_list.totle_user_num.text.text = string.format(Language.RoleTalent.TotalUserdTalent, count, RoleWGData.Instance:GetRoleTalentPoint() + count)

	if RoleWGData.Instance:GetRoleTalentEffect() and not self.reset_role_talent_flag then
		--EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["telent_effect_root"], Vector3(0,0,0))
		--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_shengjichenggong)
		--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["telent_effect_root"].transform)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["telent_effect_root"]})
	end

	if self.reset_role_talent_flag then
		self.reset_role_talent_flag = false
	end
end

function SkillView:FlushRightPanel(data)
	if nil == data then return end
	self.cur_select_talent_data = data
	local talent_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, (data.level == 0 and data.max_level or data.level))
	self.node_list.talent_skill_icon.image:LoadSprite(ResPath.GetTalentSkill(data.icon))
	self.node_list.talent_skill_name.text.text = data.name
	self.node_list.now_skill_condition.text.text = talent_cfg.desc
	self.node_list.now_skill_title_text.text.text = data.level == 0 and "满级效果" or "当前效果"
	self.node_list.talent_skill_level.text.text = data.level .."/".. data.max_level

	if data.level < data.max_level then
		local level = data.level + 1
		local next_talent_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, level)
		self.node_list.next_skill_condition.text.text = next_talent_cfg.desc

		local is_condition, limit_desc = RoleWGData.Instance:GetTalentIsSatisfyPreconditions(data)
		local is_show_limit = not is_condition and data.level < data.max_level
		self.node_list.limit_desc:SetActive(is_show_limit)
		self.node_list.upgrade_desc:SetActive(not is_show_limit)
		self.node_list.max_level_img:SetActive(false)
		if is_show_limit then
			self.node_list.limit_desc_text.text.text = limit_desc
		end

		local talent_point = RoleWGData.Instance:GetRoleTalentPoint()
		local xiaohao = next_talent_cfg.xiaohao or 0
		local tex_color = xiaohao > talent_point and COLOR3B.RED or COLOR3B.GREEN
		self.node_list["lbl_role_talent_point"].text.text = ToColorStr(string.format("%s/%s", talent_point, xiaohao), tex_color)
		self.node_list.btn_upgrade_redpoint:SetActive(talent_point >= xiaohao)
	else
		self.node_list.limit_desc:SetActive(false)
		self.node_list.upgrade_desc:SetActive(false)
		self.node_list.max_level_img:SetActive(true)
		self.node_list.next_skill_condition.text.text = Language.RoleTalent.MaxLv
	end

	for i= 1, 3 do
		self.node_list["lbl_role_talent_" .. i].text.text = string.format(Language.RoleTalent.TalentSort[i], RoleWGData.Instance:GetActTalentCount(i))
		self.node_list["lbl_role_talent_select_" .. i].text.text = string.format(Language.RoleTalent.TalentSort[i], RoleWGData.Instance:GetActTalentCount(i))
	end

	local other = RoleWGData.Instance:GetRoleTalentSkillResetItem()
	self.node_list.talent_skill_tips.text.text = string.format(Language.RoleTalent.TalentSkillPointTips, other.open_talent_level, other.base_talent_point)
end

function SkillView:OnClickRoleTalentItem()
	local cfg = RoleWGData.Instance:GetRoleTalentSkillResetItem()
	local cost_itemid = cfg.talent_point_id
	local data = {}
	data.item_id = cost_itemid
	TipWGCtrl.Instance:OpenItem(data)
end

function SkillView:OnClickJinJieReq()
	if self.cur_select_talent_data and self.cur_select_talent_data.level < self.cur_select_talent_data.max_level then
		RoleWGCtrl.Instance:SendRoleTelentOperate(ROLE_TALENT_OPERATE_TYPE.ROLE_TALENT_OPERATE_TYPE_UPLEVEL
			, self.cur_select_talent_data.talent_id, self.cur_select_talent_data.level+1)
	elseif self.cur_select_talent_data.level >= self.cur_select_talent_data.max_level then
		TipWGCtrl.Instance:ShowSystemMsg(Language.RoleTalent.TipsMaxLv)
	end
end

-- -- 点击加点
-- function SkillView:OnClickRoleTalentUplevel()
-- 	if self.send_upgrade then return end
-- 	local select_item = self.show_skill_layout:GetSelectSkillItem()
-- 	local gongji_skill_list = RoleWGData.Instance:SetRoleTalentLevelList(1)
-- 	local data = nil ~= select_item and select_item:GetData() or gongji_skill_list[1]
-- 	if data == nil or (select_item and select_item.is_max_level) then return end

-- 	RoleWGCtrl.Instance:SendRoleTelentOperate(ROLE_TALENT_OPERATE_TYPE.ROLE_TALENT_OPERATE_TYPE_UPLEVEL, data.talent_id)
-- 	self.send_upgrade = true
-- end

function SkillView:GetHaveActiveTalent()
	local has_active_talent = false
	local count
	for i = 1, 4 do
		local count = RoleWGData.Instance:GetActTalentCount(i)
		if count >= 1 then
			has_active_talent = true
			break
		end
	end
	return has_active_talent
end

function SkillView:OnClickRoleTalentAgain()
	-- local count_1 = RoleWGData.Instance:GetActTalentCount(1)
	-- local count_2 = RoleWGData.Instance:GetActTalentCount(2)
	-- local count_3 = RoleWGData.Instance:GetActTalentCount(3)
	-- if (count_1 + count_2 + count_3) <= 0 then
	-- 	TipWGCtrl.Instance:ShowSystemMsg()
	-- end
	-- self.reset_alert = self.reset_alert or Alert.New()

	if self:GetHaveActiveTalent() then
		local other = RoleWGData.Instance:GetRoleTalentSkillResetItem()
		local function resetRoleSkill()
			self.reset_role_talent_flag = true
			RoleWGCtrl.Instance:SendRoleTelentOperate(ROLE_TALENT_OPERATE_TYPE.ROLE_TALENT_OPERATE_TYPE_RESET)
		end

		local function buyResetItem()
			ShopWGCtrl.Instance:SendShopBuy(other.reset_consume_item, 1, 0, 0, other.seq)
			if ShopWGData.GetItemGold(other.reset_consume_item) <= RoleWGData.Instance:GetRoleInfo().gold then
				self.buy_item_ing = other.reset_consume_item
			end
		end
		local item_cfg = ItemWGData.Instance:GetItemConfig(other.reset_consume_item)
		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(other.seq)
		if ItemWGData.Instance:GetItemNumInBagById(other.reset_consume_item) > 0 then
			local text = string.format(Language.RoleTalent.TalentSkillResetTop, item_cfg.name)
			-- self.reset_alert:SetLableString(text)
			-- self.reset_alert:SetOkFunc(resetRoleSkill)
		else
			-- local text = string.format(Language.RoleTalent.TalentSkillBuyTop, item_cfg.name, shop_cfg.price)
			-- self.reset_alert:SetLableString(text)
			-- self.reset_alert:SetOkFunc(buyResetItem)

			-- ShopTip.Instance:SetData(item_cfg,1,GameEnum.SHOP, nil , other.seq, nil, 1)
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_cfg.id})
			return
		end
		-- Language.RoleTalent.TalentSkillResetTop1
		-- self.reset_alert:Open()
		local item_num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
		local color = item_num >= 1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		local str = string.format("%s/1", ToColorStr(item_num,color))
		TipWGCtrl.Instance:OpenTipsOpenView({item_id = item_cfg.id},Language.RoleTalent.TalentSkillResetTop1,Language.Guild.CHONGZHI,resetRoleSkill,str)

	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleTalent.ErrorTip)
	end
end

---------------左侧列表--------------------
TalentListItemRender = TalentListItemRender or BaseClass(BaseRender)
function TalentListItemRender:__init()
end

function TalentListItemRender:__delete()
end

function TalentListItemRender:OnFlush()
	local other_cfg = RoleWGData.Instance:GetRoleTalentSkillResetItem()
	-- 沒開通
	if RoleWGData.Instance:GetRoleLevel() < other_cfg.open_proficient_talent_level or not TaskWGData.Instance:GetTaskIsCompleted(other_cfg.task_id) then
		if self.index == 4 then
			self.node_list.lock.image.enabled = true
			self.node_list.lbl_talent_item_name.text.enabled = false
			self.node_list.img_act_num_bg:SetActive(false)
			self.node_list.root_obj.button.interactable = false
			self.node_list.high.image.enabled = false
			return
		end
	end
	self.node_list.lock.image.enabled = false
	self.node_list.lbl_talent_item_name.text.enabled = true
	self.node_list.img_act_num_bg:SetActive(true)
	self.node_list.root_obj.button.interactable = true


	self.node_list["lbl_talent_item_name"].text.text = self.data .. "\n" .. Language.Role.TabSub2[3]
	local act_talent_count = 0
	local act_talent_count = RoleWGData.Instance:GetActTalentCount(self.index)
	self.node_list["lbl_act_talent_count"].text.text = act_talent_count   -- 总加点数
	local bool = self:IsSelectIndex()
	self.node_list.high.image.enabled = bool
end

-------------------第一页  攻击 or 防御 or 通用 or 精通 --------------------
TalentSkillItemGroup = TalentSkillItemGroup or BaseClass(BaseRender)
function TalentSkillItemGroup:__init()
	self.skill_list = {}
	self.item_obj_list = {}
	self.last_select_item = nil
	self.cur_select_talent_type = 1
end

function TalentSkillItemGroup:__delete()
	if self.skill_list then
		for k,v in pairs(self.skill_list) do
			v:DeleteMe()
		end
		self.skill_list = nil
	end
	self.skill_data_list = nil
	self.cur_type = nil
	self.flush_call_back = nil

	self.item_obj_list = {}

	self.parent = nil
	self.cur_select_talent_type = 1
end

function TalentSkillItemGroup:CurSelectTalentSkillType(type_index)
	self.cur_select_talent_type = type_index
end

function TalentSkillItemGroup:SetParent(parent)
	self.parent = parent
end

function TalentSkillItemGroup:SetSkillType(index)
	if index then
		self.cur_type = index
		self.skill_data_list = RoleWGData.Instance:SetRoleTalentLevelList(self.cur_type)
	end
	self:CreateChild()
end

function TalentSkillItemGroup:CreateChild()
	local content = self.node_list.content
	if self.skill_data_list then
		for i=1,#self.skill_data_list do
			self.item_obj_list[i] = ResMgr:Instantiate(self.node_list["talent_item_prefab"].gameObject)
			self.item_obj_list[i].transform:SetParent(content:FindObj("skill_" .. i).transform, false)
			self.item_obj_list[i]:SetActive(true)
			self.skill_list[i] = TalentSkillItem.New(self.item_obj_list[i])
			self.skill_list[i]:SetClickCallBack(BindTool.Bind(self.OnSelectSkillItem, self))
		end
	end
end

function TalentSkillItemGroup:SelectFirst()
	self:OnSelectSkillItem(self.skill_list[1])
	self.parent:FlushRightPanel(self.skill_list[1].data)
end

function TalentSkillItemGroup:OnFlush()
	if self.cur_type then
		self.skill_data_list = RoleWGData.Instance:SetRoleTalentLevelList(self.cur_type)
		for k,v in ipairs(self.skill_list) do
			v:SetData(self.skill_data_list[k])
			v:SetTalentIcon(1, self.skill_data_list[k])
		end

		if self.cur_select_talent_type == self.cur_type and self.select_skill_item and self.select_skill_item.data then
			self.parent:FlushRightPanel(self.select_skill_item.data)
		end

		-- if self.select_skill_item == nil then
		-- 	self:OnSelectSkillItem(self.skill_list[1])
		-- end
	end
	if self.flush_call_back then
		self.flush_call_back()
	end
end

function TalentSkillItemGroup:SetFlushCallBack(callback)
	self.flush_call_back = callback
end

-- 选择某个技能格子
function TalentSkillItemGroup:OnSelectSkillItem(item)
	if self.select_skill_item ~= item then
		-- RoleWGCtrl.Instance:OpenRoleTalentTips(item.data)
		self.parent:FlushRightPanel(item.data)

		if self.select_skill_item ~= nil then
			self.select_skill_item:CancleSelect()
		end
		item:Select()
		self.select_skill_item = item
	end
end

function TalentSkillItemGroup:GetSelectSkillItem()
	return self.select_skill_item
end

function TalentSkillItemGroup:SetClickSkilLItemCallback(callback)
	self.click_skill_item_callback = callback
end


-------------------单元格--------------------
TalentSkillItem = TalentSkillItem or BaseClass(BaseRender)
function TalentSkillItem:__init()
	self.is_max_level = false
end

function TalentSkillItem:__delete()
	self.node_list.high:SetActive(false)
	self.is_max_level = nil
	self.remember_talent_id = nil
	self.remember_talent_level = nil
end

function TalentSkillItem:ChangeHighLight(isOn)
	-- self.node_list.high.image.enabled = isOn
end

-- 角色天赋
function TalentSkillItem:OnFlush()
	local data = self:GetData()
	if data == nil then return end
	if self.node_list.img_skill_icon and self.node_list.gray_icon then
		self.node_list.img_skill_icon.image:LoadSprite(ResPath.GetTalentSkill(data.icon))
		self.node_list.gray_icon.image:LoadSprite(ResPath.GetTalentSkill(data.icon))
		self.node_list.gray_icon:SetActive(data.level == 0)
	end
	if self.node_list.lbl_skill_level then
		self.node_list.lbl_skill_level.text.text = data.level .."/".. data.max_level
		self.is_max_level = data.level == data.max_level
	end
end

-- 灵宠天赋
function TalentSkillItem:SetTalentIcon(index,data)
	if data then
		self.node_list['img_talent_skill_icon'].image:LoadSprite(ResPath.GetTalentSkill(data.icon))
		self.node_list['img_talent_skill_icon']:SetActive(true)
		local is_active = data.level ~= 0
		local is_condition = RoleWGData.Instance:GetTalentIsSatisfyPreconditionsTwo(data)
		local talent_point = RoleWGData.Instance:GetRoleTalentPoint()
		local talent_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, (data.level == 0 and 1 or data.level))
		local need_xiaohao = talent_cfg and talent_cfg.xiaohao or 0
		local need_talent = talent_cfg and talent_cfg.xiaohao or 0
		--不满足条件 或（等级小于1 and 点数不够）置灰
		local can_not_up_grade1 = ((not is_condition) or (data.level < 1 and talent_point < need_talent))
		local can_not_up_grade2 = ((not is_condition) or (talent_point < need_talent))
		local is_max = data.level == data.max_level
		self.node_list.lock:SetActive(can_not_up_grade1)
		self.node_list.lock_bg:SetActive(can_not_up_grade1)

	--	local color = talent_point >= need_talent and "#06ff00" or COLOR3B.WHITE
	--	color =  and COLOR3B.YELLOW or color
		if nil == self.remember_talent_id then
			self.remember_talent_id = data.talent_id
			self.remember_talent_level = data.level
		else
			if self.remember_talent_id == data.talent_id and self.remember_talent_level < data.level then
				-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_role_talent_levelup_01)
				-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["upgrade_eff"].transform)
			end
			self.remember_talent_id = data.talent_id
			self.remember_talent_level = data.level
		end
		self.node_list["canup_eff"]:SetActive(not can_not_up_grade2 and not is_max)
		self.node_list['num'].text.text = data.level .."/".. data.max_level -- ToColorStr(data.level .."/".. data.max_level, color)
		self.node_list["ph_talent_cell1"].button:AddClickListener(BindTool.Bind(self.OnClick,self))
	end
end

-- 改变点击回调
function TalentSkillItem:OnClick(handler)
	self.handler(self)
end

-- 改变点击回调
function TalentSkillItem:SetClickCallBack(handler)
	self.handler = handler
end

function TalentSkillItem:CancleSelect()
	self.node_list.high:SetActive(false)
end

function TalentSkillItem:Select()
	self.node_list.high:SetActive(true)
end
