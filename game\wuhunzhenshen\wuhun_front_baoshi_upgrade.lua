-------------------------------------------------------------------------------------------------------------------
--升级宝石提示
-------------------------------------------------------------------------------------------------------------------
WuHunBaoShiUpgradeView = WuHunBaoShiUpgradeView or BaseClass(SafeBaseView)
function WuHunBaoShiUpgradeView:__init()
	self:SetMaskBg(true)

	self:LoadConfig()
end

function WuHunBaoShiUpgradeView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 520)})
	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_baoshi_upgrade_tips")
end

function WuHunBaoShiUpgradeView:LoadCallBack()
	--self:SetSecondView(Vector2(620, 562))
	self.node_list.title_view_name.text.text = Language.WuHunZhenShen.WuhunFrontBaoshiTxt6
	self.left_cell = ItemCell.New(self.node_list.ph_cell_1)
	self.right_cell = ItemCell.New(self.node_list.ph_cell_2)

	self.stone_list_view = AsyncListView.New(WuHunBlockRender, self.node_list["have_stone_list"])
	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnUpgradeClick,self))

	WuHunWGCtrl.Instance:InitBaoShiUpGradeAlertTips(BindTool.Bind(self.AlertOkFnc, self))
end

function WuHunBaoShiUpgradeView:ReleaseCallBack()
	if self.left_cell then
		self.left_cell:DeleteMe()
		self.left_cell = nil
	end

	if self.right_cell then
		self.right_cell:DeleteMe()
		self.right_cell = nil
	end

	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end

	self.data = nil
	self.enough_price = nil
	self.still_need_price = nil
	self.need_stone_desc = nil
end

function WuHunBaoShiUpgradeView:AlertOkFnc()
	if self.enough_price then
		WuHunWGCtrl.Instance:SendWuHunFrontOperate(
			WUHUN_FRONT_OPERATE_TYPE.ENGRAVE_LEVEL,
			self.data.wuhun_id,
			self.data.hunzhen_index,
			self.data.front_gem_index,
			self.data.engrave_index - 1
		)

		self:Close()
	else
		self:Close()
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--刻印index 1到6
function WuHunBaoShiUpgradeView:SetData(data)
	self.data = data
end

--设置价格
function WuHunBaoShiUpgradeView:SetPrice(price)
	self.node_list.lbl_price.text.text = price
end

function WuHunBaoShiUpgradeView:SetLeftCellData(item_id)
	local name_str, attr_str = WuHunFrontWGData.Instance:GetBaoShiNatrue(item_id)
	self.node_list.lbl_name_1.text.text = name_str
	self.node_list.lbl_attr_1.text.text = attr_str
	self.left_cell:SetData({item_id = item_id})
end

function WuHunBaoShiUpgradeView:SetRightCellData(item_id)
	local name_str, attr_str = WuHunFrontWGData.Instance:GetBaoShiNatrue(item_id)
	self.node_list.lbl_name_2.text.text = name_str
	self.node_list.lbl_attr_2.text.text = attr_str
	self.right_cell:SetData({item_id = item_id})
end

function WuHunBaoShiUpgradeView:OnFlush()
	local old_item_id = self.data.old_item_id
	local old_item_cfg = WuHunFrontWGData.Instance:GetEngraveLevelCfg(old_item_id)
	local new_item_id = old_item_cfg and old_item_cfg.new_item_id or old_item_id
	self.still_need_price = WuHunFrontWGData.Instance:ComputBaoShiUpgradePrice(old_item_id)
	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	local bind_gold = 0--GameVoManager.Instance:GetMainRoleVo().bind_gold
	self.enough_price = role_gold + bind_gold >= self.still_need_price

	local need_stone_desc = WuHunFrontWGData.Instance:CalcUpgradeNeedStoneStr(old_item_id)
	local cost_baoshi = ""
	if need_stone_desc then
		local and_str = self.still_need_price > 0 and Language.Equip.And or ""
		cost_baoshi = and_str .. need_stone_desc
	end

	local tips_str = ""
	if self.still_need_price > 0 then
		tips_str = string.format(Language.WuHunZhenShen.WuhunFrontBaoshiTxt3, self.still_need_price, cost_baoshi)
	else
		tips_str = string.format(Language.WuHunZhenShen.WuhunFrontBaoshiTxt2, cost_baoshi)
	end
	WuHunWGCtrl.Instance:SetBaoShiUpGradeAlertTips(self.still_need_price > 0, tips_str)

	local hide_price = self.still_need_price > 0
	self:SetPrice(self.still_need_price) --设置升级价格
	self:SetLeftCellData(old_item_id)
	self:SetRightCellData(new_item_id)

	self.node_list.price_part:SetActive(hide_price) --设置价格图标显隐
	self.node_list.btn_text:SetActive(not hide_price)

	local stone_list = WuHunFrontWGData.Instance:GetAllTypeBaoShiByType(self.data.engrave_type)
	if self.stone_list_view then
		self.stone_list_view:SetDataList(stone_list)
		self.stone_list_view:CancelSelect()
	end

	self.node_list.stonetip:SetActive(IsEmptyTable(stone_list))

	self.node_list.image_cell_red:CustomSetActive(true)
	if not self.arrow_tweener then
		self.arrow_tweener = SetUIGreenImageCommonJump(self.node_list.image_cell_red)
	end
end

function WuHunBaoShiUpgradeView:OnUpgradeClick()
	WuHunWGCtrl.Instance:OpenBaoShiUpGradeAlertTips(self.still_need_price > 0)
end

-------------------------------------------------------------------------------------------------------------------
WuHunBlockRender = WuHunBlockRender or BaseClass(BaseRender)
function WuHunBlockRender:__init()
	self.show_item = ItemCell.New(self.node_list.item_node)
end

function WuHunBlockRender:__delete()
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function WuHunBlockRender:OnFlush()
	self.show_item:SetData(self.data)
end
