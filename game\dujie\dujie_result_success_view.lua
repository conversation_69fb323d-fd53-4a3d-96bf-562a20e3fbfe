DujieResultSuccessView = DujieResultSuccessView or BaseClass(SafeBaseView)

function DujieResultSuccessView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_success_view")
end

function DujieResultSuccessView:LoadCallBack()

    if not self.attr_list then
		self.attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.attr_list)
	end

    self.skill_icon = SkillShowSkillRender.New(self.node_list["skill_icon"])
    self.skill_icon:SetNeedChangeSkillBtnPos(false)
    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.OnClickBtnOk, self))
end

function DujieResultSuccessView:ReleaseCallBack()
    if self.attr_list then
        self.attr_list:DeleteMe()
        self.attr_list = nil
    end

    if self.skill_icon then
        self.skill_icon:DeleteMe()
        self.skill_icon = nil
    end
end

function DujieResultSuccessView:CloseCallBack()

    self:CleanResetTimer()
    self:CleanAnimTimer()

    ViewManager.Instance:Close(GuideModuleName.DujieView)
    ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)

end


function DujieResultSuccessView:OnFlush()
    -- 数据

    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    self:FlushStageShow(base_info.level,true)

end

function DujieResultSuccessView:FlushStageShow(level, is_anim)
    
    local cur_level_cfg = DujieWGData.Instance:GetLevelCfg(level)
    local next_level_cfg = DujieWGData.Instance:GetLevelCfg(level+1)
    local old_level_cfg = DujieWGData.Instance:GetLevelCfg(level - 1)
    
    -- 界面属性
    local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(old_level_cfg, cur_level_cfg, nil, nil, 1, 5)
    self.attr_list:SetDataList(attr_list)

	-- 阶段图片
    local stage_asset_1, jinjie_asset_1 = ResPath.GetRawImagesPNG("a3_dujie_stage_"..old_level_cfg.type)
    self.node_list.img_level.raw_image:LoadSprite(stage_asset_1, jinjie_asset_1, function ()
        self.node_list.img_level.raw_image:SetNativeSize()
    end)
    local stage_asset_2, jinjie_asset_2 = ResPath.GetRawImagesPNG("a3_dujie_stage_"..(cur_level_cfg.type))
    self.node_list.img_next_level.raw_image:LoadSprite(stage_asset_2, jinjie_asset_2, function ()
        self.node_list.img_next_level.raw_image:SetNativeSize()
    end)

    -- if old_level_cfg.type_seq == 0 then
    --     self.node_list.text_level_dujie.text.text = old_level_cfg.type
    -- else
    --     self.node_list.text_level_dujie.text.text = cur_level_cfg.type.."·"..NumberToChinaNumber(cur_level_cfg.type_seq)
    -- end

    -- if cur_level_cfg.type == next_level_cfg.type then
    --     self.node_list.text_next_level_dujie.text.text = cur_level_cfg.type.."·"..NumberToChinaNumber(cur_level_cfg.type_seq+1)
    -- else
    --     self.node_list.text_next_level_dujie.text.text = next_level_cfg.type.."·"..NumberToChinaNumber(next_level_cfg.type_seq+1)

    -- end
    
    
    
    local type_str = DujieWGData.Instance:GetTypeStr(cur_level_cfg.type)
    self.node_list.text_level.text.text = string.format(Language.Dujie.DujieSuccessStr ,type_str)

    -- 功能开启提示
    local desc = DujieWGData.Instance:GetNextOpenFuncDesc(level,true)

    self.node_list.text_open_func_desc.text.text = desc

    local index = DujieWGData.Instance:GetLevelIndex(level)
    local index_max = DujieWGData.Instance:GetTypeMax(cur_level_cfg.type, cur_level_cfg.type_seq) 
    local target_fillAmount = index/index_max
    if is_anim then
        self.node_list.img_node_end:SetActive(false)
        -- local old_index = DujieWGData.Instance:GetLevelIndex(level-1)
        -- local old_fillAmount = old_index/index_max
        local old_fillAmount = 0
        local fillAmount_diff = target_fillAmount - old_fillAmount

        self:CleanAnimTimer()
        self.anim_timer = CountDown.Instance:AddCountDown(0.5, 0.02,
            -- 回调方法
            function(elapse_time, total_time)
                local counter_fillAmount = old_fillAmount + fillAmount_diff * (elapse_time/total_time)
                self.node_list.img_bar.image.fillAmount = counter_fillAmount
                local pos = Mathf.Lerp(-215, 240, counter_fillAmount)
                -- self.node_list.text_level.rect.anchoredPosition = Vector2(pos,58)
            end,
            -- 倒计时完成回调方法
            function()
                self.node_list.img_bar.image.fillAmount = target_fillAmount
                local pos = Mathf.Lerp(-215, 240, target_fillAmount)
                -- self.node_list.text_level.rect.anchoredPosition = Vector2(pos,58)

                self.node_list.img_node_end:SetActive(index == index_max)
            end
        )
    else
        self.node_list.img_node_end:SetActive(index == index_max)
        self.node_list.img_bar.image.fillAmount = target_fillAmount
        local pos = Mathf.Lerp(-215, 240, target_fillAmount)
        -- self.node_list.text_level.rect.anchoredPosition = Vector2(pos,58)
    end

    local icon_type_list = Split(cur_level_cfg.open_icon_type,",")
    local have_anger_skill =false
    local have_body =false
    local have_skill = false
    for i, v in ipairs(icon_type_list) do
        if tonumber(v) == 21 then
            have_anger_skill = true
        elseif tonumber(v) == 31 then
            have_body = true
        else
            local dujie_skill_id = DujieWGData.Instance:GetOtherCfg("dujie_skill_"..v)
            self.skill_icon:SetData({skill_id = dujie_skill_id, skill_level = 1,skill_name = Language.Dujie.SkillName[v]})
            have_skill = true
        end
    end

    self.node_list.img_anger_skill:SetActive(have_anger_skill)
    self.node_list.img_body:SetActive(have_body)
    self.node_list.skill_icon:SetActive(have_skill)

    if have_anger_skill or have_body or have_skill then
        self.node_list.rect_new_get:SetActive(true)
        self.node_list.rect_empty:SetActive(false)
        self.node_list.attr_list.rect.sizeDelta = Vector2(653,212)
    else
        self.node_list.rect_new_get:SetActive(false)
        self.node_list.rect_empty:SetActive(true)
        self.node_list.attr_list.rect.sizeDelta = Vector2(653,224)
    end

end

function DujieResultSuccessView:OnClickBtnOk()
    self:Close()
end

function DujieResultSuccessView:CleanResetTimer()
    if self.reset_timer then
        GlobalTimerQuest:CancelQuest(self.reset_timer)
        self.reset_timer = nil
    end
end

function DujieResultSuccessView:CleanAnimTimer()
    if self.anim_timer and CountDown.Instance:HasCountDown(self.anim_timer) then
        CountDown.Instance:RemoveCountDown(self.anim_timer)
        self.anim_timer = nil
    end
end


