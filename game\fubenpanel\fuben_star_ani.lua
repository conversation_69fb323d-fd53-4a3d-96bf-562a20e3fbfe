FubenStarAniView = FubenStarAniView or BaseClass(SafeBaseView)

function FubenStarAniView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_star_ani")
	self.active_close = false
	self.fb_star = -1
	self.data = nil
	self.begin_time = 0
	self.over_time = 0
end

function FubenStarAniView:__delete()

end

function FubenStarAniView:ReleaseCallBack()
	self.star_move_pos_1 = nil
	self.star_move_pos_2 = nil
	self.star_move_pos_3 = nil
	self.star = -1
	self.wave = -1
	self.star_num_record = 0
	if self.star_effect then
		self.star_effect = nil
	end
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end

	if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end

	self.data = nil
end

function FubenStarAniView:SetData(data)
	self.data = data
end

-- 
function FubenStarAniView:ArrowClickChange(ison)
	if not self.node_list or not self.node_list.layout_fuben then
		return 
	end
	
	self.node_list.layout_fuben:SetActive(not ison)
end

function FubenStarAniView:LoadCallBack()
	self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ArrowClickChange, self))
	self.star_move_pos_1 = self.node_list.pos_1.transform.position.x
	self.star_move_pos_2 = self.node_list.pos_2.transform.position.x
	self.star_move_pos_3 = self.node_list.pos_3.transform.position.x
	self.node_list.layout_star.transform.position = self.node_list.pos_3.transform.position
	self:Flush()
	local scene_type = Scene.Instance:GetSceneType()
	self.node_list["per_boss_tip"]:SetActive(scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.HIGH_TEAM_EQUIP_FB)
end

function FubenStarAniView:OpenCallBack()
	self.need_ani = true
end

--第一段位移
function FubenStarAniView:FirstMoveAni()
	if not self:IsLoaded() then self.is_need_playani = true return end
	self.node_list.layout_star.transform:DOMoveX(self.star_move_pos_2, 2):SetEase(DG.Tweening.Ease.Linear)
end

--第一段位移
function FubenStarAniView:OnFlush()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		self:YuanGuXianDianOnFlush()
	elseif scene_type == SceneType.COPPER_FB then
		self:CopperOnFlush()
	else
		if nil == self.data then
			return
		end
		self:CommonOnFlush()
	end
end

function FubenStarAniView:GetCurStarNum()
	return self.star_num_record or 0
end

function FubenStarAniView:UpdateTime()
	local scene_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if self.need_ani and self.begin_time <= server_time then
		self:FirstMoveAni()
		self.need_ani = false
	end
	local pass_time = server_time - self.begin_time
	local star = self:GetStar()
	self.star_num_record = star
	FuBenPanelWGData.Instance:SetFubenStarNum(star)
	local scene_type = Scene.Instance:GetSceneType()
	for i=1, 3 do
		if scene_type == SceneType.HIGH_TEAM_EQUIP_FB or scene_type == SceneType.TIAN_SHEN_FB  then
			
			self.node_list["img_star_" .. i]:SetActive(star >= 0)
			if star >= 0 then

			end
			self.node_list["img_wu"]:SetActive(star < 0)
		else
			self.node_list["img_star_" .. i]:SetActive(star > 0)
			if star > 0 then

			end
			self.node_list["img_wu"]:SetActive(star == 0)
		end
	end
	self:UpdateStr(star, pass_time)
end

function FubenStarAniView:GetStar()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local pass_time = server_time - self.begin_time
	local star = 3
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
		star = scene_info.cur_star_num
	elseif scene_type == SceneType.COPPER_FB then
		local scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
		star = scene_info.cur_star_num
	elseif scene_type == SceneType.TIAN_SHEN_FB then
		if pass_time > self.data.time0 then
			star = -1
		elseif pass_time > self.data.time1 then
			star = 0
		elseif pass_time > self.data.time2 then
			star = 1
		elseif pass_time > self.data.time3 then
			star = 2
		end
	else
		if pass_time > self.data.time1 then
			star = 0
		elseif pass_time > self.data.time2 then
			star = 1
		elseif pass_time > self.data.time3 then
			star = 2
		end
	end
	return star
end


function FubenStarAniView:UpdateStr(star, pass_time)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		self:YuanGuXianDianUpdateStr(star, pass_time)
	elseif scene_type == SceneType.COPPER_FB then
		self:CopperUpdateStr(star, pass_time)
	elseif scene_type == SceneType.PERSON_BOSS then
		self:PersonalBossUpdateStr(star, pass_time)
	else
		self:CommonUpdateStr(star, pass_time)
	end

end

--普通刷新
function FubenStarAniView:CommonOnFlush()
	local scene_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end
	self.begin_time = scene_info.flush_timestamp
	self.over_time = scene_info.time_out_stamp

	if self.timer_quest == nil then
		self:UpdateTime()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateTime, self), 1)
	end
end

--装备本刷新
function FubenStarAniView:YuanGuXianDianOnFlush()
	local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
	if not scene_info then return end
	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end
	self.begin_time = scene_info.prepare_end_timestamp
	self.over_time = scene_info.finish_timestamp

	if self.timer_quest == nil then
		self:UpdateTime()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateTime, self), 1)
	end
end

--铜币本刷新
function FubenStarAniView:CopperOnFlush()
	local scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
	if not scene_info then return end
	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end
	self.begin_time = scene_info.prepare_end_timestamp
	self.over_time = scene_info.scene_timeout_timestamp

	if self.timer_quest == nil then
		self:UpdateTime()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateTime, self), 1)
	end
end

function FubenStarAniView:CommonUpdateStr(star, pass_time)
	local str = ""
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TIAN_SHEN_FB then
		if star >= 0 then
			pass_time = self.data["time" .. star] - pass_time
			str = string.format(self.data.str, TimeUtil.FormatSecond(pass_time, 2), star, self.data["per" .. star] .. "%")
		else
			pass_time = math.max(self.over_time - TimeWGCtrl.Instance:GetServerTime(), 0)
			str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond(pass_time, 2))
		end
	else
		if star > 0 then
			pass_time = self.data["time" .. star] - pass_time
			str = string.format(self.data.str, TimeUtil.FormatSecond(pass_time, 2), star, self.data["per" .. star] .. "%")
		else
			pass_time = math.max(self.over_time - TimeWGCtrl.Instance:GetServerTime(), 0)
			str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond(pass_time, 2))
		end
	end
	self.node_list["rich_star_tips"].text.text = str
end

function FubenStarAniView:YuanGuXianDianUpdateStr(star, pass_time)
	local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
    --local str = ""
	--local time
	--if star > 0 then
	--	time = scene_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()
	--	str = string.format(Language.FuBenPanel.CopperStarTask, TimeUtil.FormatSecond(time, 2), star - 1)
	--else
	--	time = math.max(self.over_time - TimeWGCtrl.Instance:GetServerTime(), 0)
	--	str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond(time, 2))
	--end
	--self.node_list["rich_star_tips"].text.text = str
	if star >= 0 then
		self.node_list["per_boss_tip"]:SetActive(true)
		pass_time = scene_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()
		self.node_list["per_boss_tip_1"].text.text = string.format(Language.FuBenPanel.CopperStarTask3, TimeUtil.FormatSecond(pass_time, 2))
		self.node_list["per_boss_tip_2"].text.text = string.format(Language.FuBenPanel.CopperStarTask4, star)
		local per = self.data["per" .. star]
		if per then
			self.node_list["per_boss_tip_3"].text.text = string.format(Language.FuBenPanel.CopperStarTask5, per)
		end
	else
		local str = ""
		local time
		local scene_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
		self.node_list["per_boss_tip"]:SetActive(false)
		time = math.max(scene_info.finish_timestamp - TimeWGCtrl.Instance:GetServerTime(), 0)
		str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond(time, 2))
		self.node_list["rich_star_tips"].text.text = str
	end
end

function FubenStarAniView:CopperUpdateStr(star, pass_time)
	local scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
    local str = ""
	local time
	if star > 0 then
		time = scene_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()
		str = string.format(Language.FuBenPanel.CopperStarTask, TimeUtil.FormatSecond(time, 2), star - 1)
	else
		time = math.max(self.over_time - TimeWGCtrl.Instance:GetServerTime(), 0)
		str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond(time, 2))
	end
	self.node_list["rich_star_tips"].text.text = str
end

function FubenStarAniView:PersonalBossUpdateStr(star, pass_time)
    local str = ""
	local time
	if star >= 0 then
		self.node_list["per_boss_tip"]:SetActive(true)
		pass_time = self.data["time" .. star] - pass_time
		self.node_list["per_boss_tip_1"].text.text = string.format(Language.FuBenPanel.CopperStarTask3, TimeUtil.FormatSecond(pass_time, 2))
		self.node_list["per_boss_tip_2"].text.text = string.format(Language.FuBenPanel.CopperStarTask4, star)
		local per = self.data["per" .. star]
		self.node_list["per_boss_tip_3"].text.text = string.format(Language.FuBenPanel.CopperStarTask5, per)
	else
		local scene_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
		self.node_list["per_boss_tip"]:SetActive(false)
		time = math.max(scene_info.time_out_stamp - TimeWGCtrl.Instance:GetServerTime(), 0)
		str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond(time, 2))
		self.node_list["rich_star_tips"].text.text = str
	end
end