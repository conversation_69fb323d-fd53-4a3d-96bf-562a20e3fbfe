RolePartShieldHandle = RolePartShieldHandle or BaseClass(ReuseableShieldHandle)

function RolePartShieldHandle:__init(role, shield_obj_type, part)
	self:Init(role, shield_obj_type, part)
end

function RolePartShieldHandle:__delete()
	self.role = nil
end

function RolePartShieldHandle:Init(role, shield_obj_type, part)
	self.role = role
	self.shield_obj_type = shield_obj_type
	self.part = part
end

function RolePartShieldHandle:Clear()
	self.role = nil
end

function RolePartShieldHandle:VisibleChanged(visible)
	if self.role then
		self.role:PartVisibleChanged(self.part, visible)
	end
end