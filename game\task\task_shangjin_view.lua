TaskShangJinView = TaskShangJinView or BaseClass(SafeBaseView)

local VIP_6_CARD_SEQ = 6
function TaskShangJinView:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/task_prefab", "layout_shangjin_task_panel")
end

function TaskShangJinView:LoadCallBack()
	if not self.shangjin_task_list then
		self.shangjin_task_list = AsyncListView.New(ShangJinTaskRender, self.node_list.shangjin_task_list)
	end

	if not self.task_event then
		self.task_event = GlobalEventSystem:Bind(OtherEventType.TASK_INFO_CHANGE, BindTool.Bind(self.OnDailyChange, self))    -- 任务进度改变
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TaskShangJinView, self.get_guide_ui_event)

	XUI.AddClickEventListener(self.node_list.task_stroy_btn, BindTool.Bind1(self.OnClickTaskStroy, self))
	XUI.AddClickEventListener(self.node_list.refresh_btn, BindTool.Bind1(self.OnClickRefreshTask, self))
	XUI.AddClickEventListener(self.node_list.btn_double, BindTool.Bind1(self.OnClickDouble, self))
end

function TaskShangJinView:ReleaseCallBack()

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ShanHaiJingView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
	
	if self.shangjin_task_list then
		self.shangjin_task_list:DeleteMe()
		self.shangjin_task_list = nil
	end

	if self.task_event then
        GlobalEventSystem:UnBind(self.task_event)
        self.task_event = nil
	end

	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if self.quick_finish_alert_window then
		self.quick_finish_alert_window:DeleteMe()
		self.quick_finish_alert_window = nil
	end

	if self.alert_refresh_window then
		self.alert_refresh_window:DeleteMe()
		self.alert_refresh_window = nil
	end
end

-- 关闭前调用
function TaskShangJinView:CloseCallBack()
	-- 加一个条件，如果打开了界面点了关闭存在已接取的悬赏则直接切换到悬赏寻路
	local task_id = TaskWGData.Instance:GetCurrBountyTask()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then return end

	-- body
	TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
	TaskGuide.Instance:CanAutoAllTask(true)
	TaskGuide.Instance:SideTOStopTask(false)
end

-- 任务变更刷新
function TaskShangJinView:OnDailyChange(task_id)
	self:Flush()
end

function TaskShangJinView:OnFlush()
	self:FlushShangjinTaskList()
	self:FlushShangjinTaskLimit()
end

-- 刷新当前的悬赏列表
function TaskShangJinView:FlushShangjinTaskList()
	local list = TaskWGData.Instance:GetBountyList()
	self.shangjin_task_list:SetDataList(list)
	local info = TaskWGData.Instance:GetBountyListInfo()

	-- 刷新价格
	local base_cfg = TaskWGData.Instance:GetBountyBaseCfg()
	if base_cfg and info then
		local bounty_free_fresh = base_cfg.bounty_free_fresh or 1
		local today_free_fresh_num = info.today_free_fresh_num or 1
		self.node_list.operate_const_icon:CustomSetActive(today_free_fresh_num <= 0)
		self.node_list.btn_text.text.text = Language.Task.ShangJinRefreshBtnTxt

		if today_free_fresh_num > 0 then
			local color = today_free_fresh_num > 0 and COLOR3B.L_GREEN or COLOR3B.L_RED
			local free_str = string.format("（%d/%d）", today_free_fresh_num, bounty_free_fresh)
			local color_free_str = ToColorStr(free_str, color)
			local free_new_str = string.format("%s%s", Language.Guild.FlushFree, color_free_str) 
			self.node_list.operate_const_text.text.text = ToColorStr(free_new_str, COLOR3B.C21)
		else
			local item_icon = ItemWGData.Instance:GetItemIconByItemId(base_cfg.bounty_fresh_item_id) --拥有的数量
			local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.bounty_fresh_item_id)
			local need_num = base_cfg.bounty_fresh_item_num or 1
			local color = item_num >= need_num and COLOR3B.L_GREEN or COLOR3B.L_RED
	
			local bundle, asset = ResPath.GetItem(item_icon)
			self.node_list.operate_const_icon.image:LoadSpriteAsync(bundle, asset)
			self.node_list.operate_const_text.text.text = string.format("%s/%s", ToColorStr(item_num, color), need_num) 
		end
	end
end

-- 刷新当前的上限信息
function TaskShangJinView:FlushShangjinTaskLimit()
	local base_cfg = TaskWGData.Instance:GetBountyBaseCfg()
	local info = TaskWGData.Instance:GetBountyListInfo()
	local bounty_accept_limit = base_cfg and base_cfg.bounty_accept_limit or 0
	local today_accept_bounty_num = info and info.today_accept_bounty_num or 0
	local last_accept_num = bounty_accept_limit - today_accept_bounty_num
	local color = last_accept_num > 0 and COLOR3B.L_GREEN or COLOR3B.L_RED
	local str = string.format("%s/%d", ToColorStr(last_accept_num, color), bounty_accept_limit) 
	self.node_list.task_limit_txt.text.text = string.format(Language.Task.ShangJinAcceptLimit, str)
	-- 设置手记红点
	local note_red = TaskWGData.Instance:CheckRiChangNoteRedPoint()
	self.node_list.task_stroy_remind:CustomSetActive(note_red)
end

-----------------------------------------------------------------------------
-- 手记打开
function TaskShangJinView:OnClickTaskStroy()
	TaskWGCtrl.Instance:OpenShangJingStoryView()
end

-- 刷新悬赏榜列表
function TaskShangJinView:OnClickRefreshTask()
	if TaskWGData.Instance:IsAcceptBounty() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Task.ShangJinErrorTiips)
		return
	end

	if TaskWGData.Instance:GetBountyAcceptLimit() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Task.ShangJinErrorTiips2)
		return
	end

	local base_cfg = TaskWGData.Instance:GetBountyBaseCfg()
	local info = TaskWGData.Instance:GetBountyListInfo()

	if base_cfg and info then
		local bounty_free_fresh = base_cfg.bounty_free_fresh or 1
		local today_free_fresh_num = info.today_free_fresh_num or 1
		if today_free_fresh_num > 0 then
			self:CheckShangJinRefresh()
		else
			local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.bounty_fresh_item_id) --拥有的数量
			local need_num = base_cfg.bounty_fresh_item_num or 1
			local bounty_fresh_gold = base_cfg.bounty_fresh_gold or 1
			local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(base_cfg.bounty_fresh_item_id)
			local item_name = item_cfg and item_cfg.name or ""
			local item_color = item_cfg and item_cfg.color or ""
			local name_str = ToColorStr(item_name, ITEM_COLOR[item_color])

			if has_num > need_num then
				self:CheckShangJinRefresh()
			else
				if not self.alert_window then
					self.alert_window = Alert.New(nil, nil, nil, nil, true)
					self.alert_window:SetCheckBoxDefaultSelect(false)
				end

				self.alert_window:SetLableString(string.format(Language.Task.ShangJinRefreshTiips, name_str, bounty_fresh_gold))
				self.alert_window:SetOkFunc(function ()
					if not RoleWGData.Instance:GetIsEnoughUseGold(bounty_fresh_gold) then
						VipWGCtrl.Instance:OpenTipNoGold()
					else
						self:CheckShangJinRefresh()
					end
				end)
				self.alert_window:Open()
			end
		end
	end
end

-- 刷新悬赏榜列表
function TaskShangJinView:CheckShangJinRefresh()
	if not self.alert_refresh_window then
		self.alert_refresh_window = Alert.New(nil, nil, nil, nil, true)
		self.alert_refresh_window:SetCheckBoxDefaultSelect(false)
	end

	local list = TaskWGData.Instance:GetBountyList()
	local is_has_ssr = false
	for i, v in ipairs(list) do
		local bounty_cfg = TaskWGData.Instance:GetBountyCfgById(v.bounty_id)
		if bounty_cfg and bounty_cfg.bounty_color >= 3 and v.state == BOUNTY_OPERATE_ENUM.BOUNTY_TASK_STATUS_NOT_ACCEPT then
			is_has_ssr = true
			break
		end
	end

	if is_has_ssr then
		self.alert_refresh_window:SetLableString(Language.Task.ShangJinRefreshTiips2)
		self.alert_refresh_window:SetOkFunc(function ()
			TaskWGCtrl.Instance:SendOperateTypeRefresh()
		end)
		self.alert_refresh_window:Open()
	else
		TaskWGCtrl.Instance:SendOperateTypeRefresh()
	end
end

-- vip多倍领取
function TaskShangJinView:OnClickDouble()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local is_has_power = VipPower.Instance:GetHasPower(VipPowerId.daily_task_double)

	if not is_has_power then
		ViewManager.Instance:Open(GuideModuleName.LayoutZeroBuyView)
		return
	else
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = VIP_6_CARD_SEQ})
	end
end

-- 引导升级
function TaskShangJinView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "guide_node" then
		return self.node_list.guide_node, BindTool.Bind(function ()
			TaskWGCtrl.Instance:SendOperateTypeAccept(ui_param)
		end, self)
	elseif ui_name == "guide_node2" then
		return self.node_list.guide_node2, BindTool.Bind(self.OnClickFindTask, self)
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-- 点击寻路
function TaskShangJinView:OnClickFindTask()
	if FunctionGuide.Instance:GetIsGuide() then
		FunctionGuide.Instance:StartNextStep()
	end

	local task_id = TaskWGData.Instance:GetCurrBountyTask()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then return end

	-- body
	TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
	TaskGuide.Instance:CanAutoAllTask(true)
	TaskGuide.Instance:SideTOStopTask(false)
	TaskWGCtrl.Instance:CloseShangJingView()
end

-- 获取一键花费的弹窗
function TaskShangJinView:GetQuickFinishAlertWindow()
	if not self.quick_finish_alert_window then
		self.quick_finish_alert_window = Alert.New(nil, nil, nil, nil, true)
		self.quick_finish_alert_window:SetCheckBoxDefaultSelect(false)
	end

	return self.quick_finish_alert_window
end

----------------------------------------------------------------------------
ShangJinTaskRender = ShangJinTaskRender or BaseClass(BaseRender)
function ShangJinTaskRender:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.accept_btn, BindTool.Bind(self.OnClickAccept, self))
	XUI.AddClickEventListener(self.node_list.quick_finish_btn, BindTool.Bind(self.OnClickQuickFinish, self))
	XUI.AddClickEventListener(self.node_list.find_task_btn, BindTool.Bind(self.OnClickFindTask, self))
end

-- 点击接取任务
function ShangJinTaskRender:OnClickAccept()
	if TaskWGData.Instance:IsAcceptBounty() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Task.ShangJinErrorTiips)
		return
	end

	if TaskWGData.Instance:GetBountyAcceptLimit() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Task.ShangJinErrorTiips2)
		return
	end

	TaskWGCtrl.Instance:SendOperateTypeAccept(self.index)
end

-- 点击一键完成
function ShangJinTaskRender:OnClickQuickFinish()
	local bounty_cfg = TaskWGData.Instance:GetBountyCfgById(self.data.bounty_id)
	if not bounty_cfg then
		return
	end
	-- 判断等级限制
	if not TaskWGData.Instance:OneKeyBtnIsOpen() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.OneKeyOpenDesc)
		return 
	end

	local alert_window = TaskWGCtrl.Instance:GetQuickFinishAlertWindow()
	if alert_window then
		local spend = TaskWGData.Instance:GetBountyQuickFinishSpend(self.data.bounty_id)
		alert_window:SetLableString(string.format(Language.Task.ShangJinQuickFinish, spend))
		alert_window:SetOkFunc(function ()
			if not RoleWGData.Instance:GetIsEnoughUseGold(spend) then
				VipWGCtrl.Instance:OpenTipNoGold()
			else
				TaskWGCtrl.Instance:SendOperateTypeOneKeyFinish(self.index)
			end
		end)
		alert_window:Open()
	end
end

-- 点击寻路
function ShangJinTaskRender:OnClickFindTask()
	if FunctionGuide.Instance:GetIsGuide() then
		FunctionGuide.Instance:StartNextStep()
	end

	local task_id = TaskWGData.Instance:GetCurrBountyTask()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then return end

	-- body
	TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
	TaskGuide.Instance:CanAutoAllTask(true)
	TaskGuide.Instance:SideTOStopTask(false)
	TaskWGCtrl.Instance:CloseShangJingView()
end

function ShangJinTaskRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	self.old_bounty_id = nil
end

function ShangJinTaskRender:OnFlush()
	if not self.data then return end

	local bounty_cfg = TaskWGData.Instance:GetBountyCfgById(self.data.bounty_id)

	if not bounty_cfg then
		return
	end

	--悬赏平直默认提高一个品质
	local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", bounty_cfg.bounty_color + 1))
	self.node_list.task_type.image:LoadSprite(bundle, asset, function()
		self.node_list.task_type.image:SetNativeSize()
	end)

	if BEAST_EFFECT_COLOR[bounty_cfg.bounty_color] then
		bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[bounty_cfg.bounty_color + 1])
		self.node_list.task_type:ChangeAsset(bundle, asset)
	end

	local str = string.format("a3_mrxs_color_di_%d", bounty_cfg.bounty_color)
	bundle, asset = ResPath.GetTaskImg(str)
	self.node_list.color_di.image:LoadSprite(bundle, asset, function()
		self.node_list.color_di.image:SetNativeSize()
	end)
	local color = bounty_cfg.bounty_color > 2 and COLOR3B.C5 or COLOR3B.C21
	self.node_list.title.text.text =ToColorStr(bounty_cfg.bounty_desc, color) 
	self.reward_list:SetDataList(bounty_cfg.reward_item)
	self.node_list.accept_btn:CustomSetActive(self.data.state == BOUNTY_OPERATE_ENUM.BOUNTY_TASK_STATUS_NOT_ACCEPT)
	self.node_list.quick_finish_btn:CustomSetActive(self.data.state == BOUNTY_OPERATE_ENUM.BOUNTY_TASK_STATUS_ACCEPT and TaskWGData.Instance:OneKeyBtnIsOpen())
	self.node_list.find_task_btn:CustomSetActive(self.data.state == BOUNTY_OPERATE_ENUM.BOUNTY_TASK_STATUS_ACCEPT)
	self.node_list.complete_status:CustomSetActive(self.data.state == BOUNTY_OPERATE_ENUM.BOUNTY_TASK_STATUS_FINISH)

	-- 刷新结局数
	local cur_num, all_num = TaskWGData.Instance:GetBountyFinalTaskNum(self.data.bounty_id)
	local progress = string.format("%s/%d", ToColorStr(cur_num, COLOR3B.L_GREEN), all_num)

	if cur_num == all_num then
		self.node_list.task_status_txt.text.text = ToColorStr(string.format(Language.Task.ShangJinFinalTaskNum, Language.Task.ShangJinFinalTaskNumFull, ""), COLOR3B.L_GREEN)
	else
		self.node_list.task_status_txt.text.text = string.format(Language.Task.ShangJinFinalTaskNum, "", progress)
	end

	str = cur_num > 0 and string.format("a3_mrxs_ren%d", bounty_cfg.bounty_icon) or "a3_mrxs_jy"
	bundle, asset = ResPath.GetTaskImg(str)
	self.node_list.task_lh.image:LoadSprite(bundle, asset, function()
		self.node_list.task_lh.image:SetNativeSize()
	end)

	if self.old_bounty_id ~= nil and self.old_bounty_id ~= self.data.bounty_id then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_mrxs_saoguang")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_root.transform)
	end

	self.old_bounty_id = self.data.bounty_id
end