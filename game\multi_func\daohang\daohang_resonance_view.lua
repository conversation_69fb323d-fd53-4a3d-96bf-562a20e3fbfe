DaoHangResonanceView = DaoHangResonanceView or BaseClass(SafeBaseView)

function DaoHangResonanceView:__init()
	self:SetMaskBg(true, true)
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, daohang_bundle_name, "daohang_resonance")
end

function DaoHangResonanceView:LoadCallBack()
	if not self.content_list then
		self.content_list = {}

		for i = 1, 3 do
			self.content_list[i] = DaoHangResonanceItemRender.New(self.node_list["content_item" .. i])
		end
	end
end

function DaoHangResonanceView:ReleaseCallBack()
	if self.content_list then
		for k, v in pairs(self.content_list) do
			v:DeleteMe()
		end

		self.content_list = nil
	end

	self.data = nil
end

function DaoHangResonanceView:SetData(data)
	self.data = data
end

function DaoHangResonanceView:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	for i = 1, 3 do
		local cell_data = self.data[i]
		local has_data = not IsEmptyTable(cell_data)

		if has_data then
			self.content_list[i]:SetData(cell_data)
		end

		self.node_list["content_item" .. i]:CustomSetActive(has_data)
	end
end

-----------------------------------------------DaoHangResonanceItemRender--------------------------------------------------
DaoHangResonanceItemRender = DaoHangResonanceItemRender or BaseClass(BaseRender)

function DaoHangResonanceItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.left_active_btn, BindTool.Bind(self.OnClickActiveBtn, self))
end

function DaoHangResonanceItemRender:__delete()
	if self.click_callback then
		self.click_callback = nil
	end
end

function DaoHangResonanceItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local data = self.data
	self.node_list.lbl_attr_title.text.text = data.top_title or ""
	self.node_list.now_level.text.text = data.cur_level_desc or ""
	self.node_list.no_attr_tip.text.text = data.no_cur_attr_desc or ""
	self.node_list.no_attr_tip:CustomSetActive(data.no_cur_attr)
	local has_cur_attr_data = not IsEmptyTable(data.cur_attr_data)
	self.node_list.layout_cur_attr_add:CustomSetActive(not data.no_cur_attr and has_cur_attr_data)

	if not data.no_cur_attr and has_cur_attr_data then
		for i = 1, 5 do
			local attr_data = (data.cur_attr_data or {})[i]
			local has_data = not IsEmptyTable(attr_data)
			
			if has_data then
				self.node_list["rich_cur_name" .. i].text.text = attr_data.attr_name or ""
				self.node_list["rich_cur_value" .. i].text.text = attr_data.value_str or ""
			end

			self.node_list["rich_cur_" .. i]:CustomSetActive(has_data)
		end
	end

	self.node_list.next_level.text.text = data.next_level_desc or ""
	self.node_list.max_attr_tip.text.text = data.no_next_attr_desc or ""
	self.node_list.max_attr_tip:CustomSetActive(data.no_next_attr)
	local has_next_attr_data = not IsEmptyTable(data.next_attr_data)
	self.node_list.layout_next_attr_add:CustomSetActive(not data.no_next_attr and has_next_attr_data)

	if not data.no_next_attr and has_next_attr_data then
		for i = 1, 5 do
			local attr_data = (data.next_attr_data or {})[i]
			local has_data = not IsEmptyTable(attr_data)

			if has_data then
				self.node_list["rich_next_name" .. i].text.text = attr_data.attr_name or ""
				self.node_list["rich_next_value" .. i].text.text = attr_data.value_str or ""
			end

			self.node_list["rich_next_" .. i]:CustomSetActive(has_data)
		end
	end

	self.click_callback = data.click_callback
	self.node_list.remind:CustomSetActive(data.remind)
	self.node_list.yimanji:CustomSetActive(data.is_max_level)
	self.node_list.left_active_btn:CustomSetActive(not data.is_max_level)
	XUI.SetButtonEnabled(self.node_list.left_active_btn, data.remind)
end

function DaoHangResonanceItemRender:OnClickActiveBtn()
	if IsEmptyTable(self.data) then
		return
	end

	if self.click_callback then
		self.click_callback(self)
	end
end