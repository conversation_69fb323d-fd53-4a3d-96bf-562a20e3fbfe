NewComposeView = NewComposeView or BaseClass(SafeBaseView)

function NewComposeView:InitLongHunCompose()
	self.jump_open = self.jump_open or 1
	if nil == self.hecheng_lh_target_item then
		self.hecheng_lh_target_item = ItemCell.New(self.node_list["LH_hecheng_target_item"])
	end
	if nil == self.lh_hecheng_cost_item3 then
		self.lh_hecheng_cost_item3 = ItemCell.New(self.node_list["LH_hecheng_item3"])
	end

	if nil == self.lh_cost_item_list then
		self.lh_cost_item_list = {}
		for i= 1,2 do
			self.lh_cost_item_list[i] = ItemCell.New(self.node_list["LH_hecheng_item"..i])
		end
	end
	self.node_list["LH_hecheng_btn"].button:AddClickListener(BindTool.Bind(self.LongHunClickCompose,self))
	self.node_list["LH_target_click"].button:AddClickListener(BindTool.Bind(self.LongHunClickTarget,self,3))
	self.node_list["LH_cost_click1"].button:AddClickListener(BindTool.Bind(self.LongHunClickTarget,self,1))
	self.node_list["LH_cost_click2"].button:AddClickListener(BindTool.Bind(self.LongHunClickTarget,self,2))
	self.node_list["LH_btn_remind"].button:AddClickListener(BindTool.Bind(self.LongHunClickRemind,self))
	self:CreateLHAccordion(self.jump_open)
	--LongHunWGData.Instance:SetIsOpenHeCheng()
	--RemindManager.Instance:Fire(RemindName.MingWen_HeCheng)
	if not self.mingwen_change_event then
		self.mingwen_change_event = GlobalEventSystem:Bind(OtherEventType.LONG_HUN_BAG_CHANGE,BindTool.Bind(self.FlushHeCheng,self))
	end

	--self:FlushHeCheng()
	--连线特效 暂时不用 以后更换
	--self:InitLongHunLineEffect()
end

----连线特效 暂时不用 以后更换
-- function NewComposeView:InitLongHunLineEffect()
-- 	if not self.longhun_line_obj then
-- 		self.longhun_line_obj = {}
-- 		local effect_root = self.node_list.LH_lineeffectpos
-- 		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_hechengmianban_3_F2)
-- 		local async_loader = AllocAsyncLoader(self, "compose_longhun_effect")
-- 		async_loader:SetParent(effect_root.transform)
-- 		async_loader:SetIsUseObjPool(true)
-- 		async_loader:Load(bundle_name, asset_name,
-- 		function (obj)
-- 			local line = obj.transform:Find("line_6")
-- 			self.longhun_line_obj[1] = line and line.gameObject

-- 			local line2 = obj.transform:Find("line_5")
-- 			self.longhun_line_obj[2] = line2 and line2.gameObject

-- 			local line3 = obj.transform:Find("line_2")
-- 			self.longhun_line_obj[3] = line3 and line3.gameObject

-- 			local line4 = obj.transform:Find("line_3")
-- 			self.longhun_line_obj[4] = line4 and line4.gameObject
-- 			self:FlushHeChengRight()
-- 		end)
-- 	end
-- end


function NewComposeView:JumpComposeBigType(jump_type)
	self.jump_open = jump_type
end

function NewComposeView:ShowLongHunCallBack()
	self:InitLongHunCompose()
end

function NewComposeView:DeleteLHHeCheng()

end

function NewComposeView:ReleaseLHHeCheng()
	if self.hecheng_lh_target_item then
		self.hecheng_lh_target_item:DeleteMe()
		self.hecheng_lh_target_item = nil
	end
	if self.mingwen_change_event then
		GlobalEventSystem:UnBind(self.mingwen_change_event)
		self.mingwen_change_event = nil
	end

	if self.lh_hecheng_cost_item3 then
		--for i= 1, 3 do
			self.lh_hecheng_cost_item3:DeleteMe()
		--end
		self.lh_hecheng_cost_item3 = nil
	end

	if self.lh_hecheng_cell_list then
		for k,v in pairs(self.lh_hecheng_cell_list) do
			for t,q in pairs(v) do
				q:DeleteMe()
			end
		end
		self.lh_hecheng_cell_list = nil
	end

	if self.lh_cost_item_list then
		for k,v in pairs(self.lh_cost_item_list) do
			v:DeleteMe()
		end
		self.lh_cost_item_list = nil
	end

	self.lh_hecheng_accordion_list = nil
	self.longhun_line_obj = nil
end

function NewComposeView:CreateLHAccordion(index)
	if self.lh_hecheng_accordion_list then return end
	local accordion_tab,father_num = LongHunWGData.Instance:GetComposeFatherList()
	self.lh_hecheng_accordion_list = {}

	for i=1,10 do
		self.node_list["LH_SelectBtn"..i]:SetActive(i <= father_num)
	end

	for i=1, father_num do
		self.lh_hecheng_accordion_list[i] = {}
		self.lh_hecheng_accordion_list[i].text_name = self.node_list["LH_text_btn_"..i]
		self.lh_hecheng_accordion_list[i].text_high_name = self.node_list["LH_text_btn_hl_"..i]
		self.lh_hecheng_accordion_list[i].select_btn = self.node_list["LH_SelectBtn"..i]
		self.lh_hecheng_accordion_list[i].select_btn.toggle:AddValueChangedListener(BindTool.Bind(self.OnLHClickExpandHandler,self,accordion_tab[i].compose_type))
		self.lh_hecheng_accordion_list[i].list = self.node_list["LH_List"..i]
		self.lh_hecheng_accordion_list[i].red_dot = self.node_list["LH_eq_remind"..i]
		self.lh_hecheng_accordion_list[i].list_item_cell = {}
		--local accor_data = nil
		--if nil ~= accordion_tab[i] and nil ~= accordion_tab[i] then
		self.lh_hecheng_accordion_list[i].text_name.text.text = accordion_tab[i].compose_name
		self.lh_hecheng_accordion_list[i].text_high_name.text.text = accordion_tab[i].compose_name
		-- 	accor_data = accordion_tab[i].child
		--end
		--self:LoadLHHeChengCell(i)
	end
	self.togglegroup = self.node_list["LH_hecheng_cell_toggle_group"]
	local open_jump_index = index and index or accordion_tab[1].compose_type
	self:OnLHClickExpandHandler(open_jump_index,true)
end

function NewComposeView:OnLHClickExpandHandler(index,is_on)
	if not is_on then return end
	self.cur_btn_list_num = index
	if nil == self.lh_hecheng_accordion_list then
	 	self:CreateLHAccordion(index)
	 	return
	end

	if nil == self.lh_hecheng_cell_list or nil == self.lh_hecheng_cell_list[index] then
		self:LoadLHHeChengCell(index)
	else
		self.lh_hecheng_accordion_list[index].list_item_cell[1]:GetComponent("Toggle").isOn = true
	end
end


function NewComposeView:LoadLHHeChengCell(index)
	local res_async_loader = AllocResAsyncLoader(self, "longhun_accordion_item" .. index)
	res_async_loader:Load("uis/view/compose_ui_prefab", "longhun_accordion_item", nil,
		function(new_obj)
			local item_vo = {}
			local accor_data = LongHunWGData.Instance:GetComPoseCellList(index)
			local get_id = -1
			local num = 1
			for k,v in pairs(accor_data) do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.lh_hecheng_accordion_list[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.togglegroup.toggle_group
				self.lh_hecheng_accordion_list[index].list_item_cell[num] = obj
				num = num + 1
				local item_render = LHHeChengListItemRender.New(obj)
				item_render:AddClickEventListener(BindTool.Bind(self.OnClickLHComPoseCell,self,accor_data[k]),true)
				item_render:SetData(accor_data[k]) --里层按钮赋值信息
				--item_render.parent_view = self
				item_vo[k] = item_render
				-- if get_id == -1 then
				-- 	get_id = k
				-- end
			end
			if nil == self.lh_hecheng_cell_list then
				self.lh_hecheng_cell_list = {}
			end
			self.lh_hecheng_cell_list[index] = item_vo
			if self.lh_hecheng_accordion_list[index].select_btn.accordion_element.isOn then
				self:OnLHClickExpandHandler(index,true)
			else
				self.lh_hecheng_accordion_list[index].select_btn.accordion_element.isOn = true
			end
		end)


end

function NewComposeView:OnClickLHComPoseCell(cell_data,is_On)
	local is_limite,condition = LongHunWGData.Instance:GetPosyIsLimite(cell_data[1].get_id)
	self.cur_hecheng_id = cell_data[1].get_id
	self.cur_hecheng_data = cell_data[1]
	if is_limite then
		local str = string.format(Language.MingWenView.DuiHuanConditionTips,condition)
		TipWGCtrl.Instance:ShowSystemMsg(str)
	else
		self:FlushHeChengRight()
	end
end

function NewComposeView:FlushHeCheng()
	self:FlushHeChengRight()
	self:FlushHeChengRed()
end

function NewComposeView:FlushHeChengRed()
	self.node_list["LH_remind_note"]:SetActive(LongHunWGData.Instance:GetComposeRemind() == 1)
	for k,v in pairs(self.lh_hecheng_accordion_list) do
		local accor_data = LongHunWGData.Instance:GetComPoseCellList(k)
		local is_red = 0
		v.red_dot:SetActive(false)
		for t,q in pairs(accor_data) do
			is_red = LongHunWGData.Instance:GetMingWenItemCanHeCheng(q[1].get_id)
			if is_red == 1 then
				v.red_dot:SetActive(true)
				break
			end
		end
	end
	if nil == self.lh_hecheng_cell_list then
		self.lh_hecheng_cell_list = {}
	end
	for k,v in pairs(self.lh_hecheng_cell_list) do
		for t,q in pairs(v) do
			q:FlushHeChengRed()
		end
	end
end

function NewComposeView:ClearHeChengRightPanel()
	self.node_list["LH_hecheng_target_name"].text.text = ""
	self.node_list["LH_hecheng_name1"].text.text = ""
	self.node_list["LH_hecheng_name2"].text.text = ""
	for i =1,3 do
		self.node_list["LH_hecheng_item"..i]:SetActive(false)
	end
	self.node_list["LH_hecheng_lock3"]:SetActive(true)
	self.node_list["LH_hecheng_target_item"]:SetActive(false)
end

function NewComposeView:FlushHeChengRight()
	self.max_cost_level = {}
	if nil == self.cur_hecheng_id then
		return
	end
	local is_limite,condition = LongHunWGData.Instance:GetPosyIsLimite(self.cur_hecheng_id)
	local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(self.cur_hecheng_id)
	if IsEmptyTable(base_cfg) then
		self:ClearHeChengRightPanel()
	else
		--self.node_list["LH_hecheng_target_item"]:SetActive(true)
		-- local cfg = ItemWGData.Instance:GetItemConfig(self.cur_hecheng_id)
		-- print_error(">>>>>",self.cur_hecheng_id,cfg)
		self.hecheng_lh_target_item:SetData({item_id = self.cur_hecheng_id})

 	-- 	self.node_list["LH_hecheng_target_item"].image:LoadSpriteAsync(bundle, asset,function ()
		-- 	self.node_list["LH_hecheng_target_item"].image:SetNativeSize()
		-- end)

		self.is_enough = true
		if self.cur_hecheng_data.stuff_id_vec ~= 0 and self.cur_hecheng_data.stuff_num_vec > 0 then
			self.node_list["LH_hecheng_item3"]:SetActive(true)
			local data = {}
			data.item_id = self.cur_hecheng_data.stuff_id_vec
			self.lh_hecheng_cost_item3:SetData(data)
			local item_cfg = ItemWGData.Instance:GetItemConfig(self.cur_hecheng_data.stuff_id_vec)
			local have_num = ItemWGData.Instance:GetItemNumInBagById(self.cur_hecheng_data.stuff_id_vec)
			self.is_enough = have_num >= self.cur_hecheng_data.stuff_num_vec
			local color = self.is_enough and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			local str = ToColorStr(have_num .."/".. self.cur_hecheng_data.stuff_num_vec, color)
			self.lh_hecheng_cost_item3:SetRightBottomColorText(str)
			self.lh_hecheng_cost_item3:SetRightBottomTextVisible(true)
			self.node_list["LH_item_name3"].text.text = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
			self.node_list["LH_hecheng_lock3"]:SetActive(false)
			--self.node_list["LH_item_name3bg"]:SetActive(true)
			if self.longhun_line_obj and self.longhun_line_obj[1] then
				self.longhun_line_obj[1]:SetActive(true)
			end
		else
			if self.longhun_line_obj and self.longhun_line_obj[1] then
				self.longhun_line_obj[1]:SetActive(false)
			end
			self.node_list["LH_hecheng_item3"]:SetActive(false)
			--self.node_list["LH_item_name3bg"]:SetActive(false)
			self.node_list["LH_hecheng_lock3"]:SetActive(true)
		end

		local posy_id_list = string.split(self.cur_hecheng_data.lifesoul_id_vec,"|")
		local posy_num_list = string.split(self.cur_hecheng_data.lifesoul_num_vec,"|")
		local need_double = false
		local lifesoul_id_vec = {}
		local lifesoul_num_vec = {}

		for k,v in pairs(posy_id_list) do
			lifesoul_id_vec[k] = tonumber(v)
		end

		for t,q in pairs(posy_num_list) do
			lifesoul_num_vec[t] = tonumber(q)
		end

		if lifesoul_id_vec[2] then
			need_double = true
		end

		local have_first_need = lifesoul_id_vec[1] and lifesoul_num_vec[1] > 0
		if self.longhun_line_obj and self.longhun_line_obj[3] then
			self.longhun_line_obj[3]:SetActive(have_first_need)
			self.longhun_line_obj[2]:SetActive(have_first_need)
			self.longhun_line_obj[4]:SetActive(need_double)
		end

		local equip_posy = {}
		for i=1,7 do
			local solt_data = LongHunWGData.Instance:GetEquipPosyDataBySlot(i)
			if not IsEmptyTable(solt_data) then
				if lifesoul_id_vec[1] == solt_data.lifesoul.item_id and lifesoul_id_vec[1] ~= -1 then
					equip_posy[1] = solt_data.lifesoul
					equip_posy[1].equip_index = i
				elseif lifesoul_id_vec[2] and lifesoul_id_vec[2] == solt_data.lifesoul.item_id and lifesoul_id_vec[2] ~= -1 then
					equip_posy[2] = solt_data.lifesoul
					equip_posy[2].equip_index = i
				end
			end
		end

		for i =1 ,2 do
			if (i == 1 or need_double) and lifesoul_id_vec[i] and lifesoul_id_vec[i] ~= -1 then
				self.node_list["LH_hecheng_item"..i]:SetActive(true)
				self.node_list["LH_hecheng_lock"..i]:SetActive(false)
				local num = LongHunWGData.Instance:GetMingWenBagItemNum(lifesoul_id_vec[i])
				if equip_posy[i] then
					num = num +1
				end

				--local base_cfg1 = LongHunWGData.Instance:GetMingWenBaseCfgByID(lifesoul_id_vec[i])
				--self.node_list["LH_hecheng_name"..i].text.text = ToColorStr(base_cfg1.name,ITEM_COLOR[base_cfg1.quality])

				local is_enough1 = num >= lifesoul_num_vec[i]
				if not is_enough1 then
					self.is_enough = false
				end

				local color1 = is_enough1 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
				local data1 = {}
				data1.item_id = lifesoul_id_vec[i]
				self.lh_cost_item_list[i]:SetData(data1)
				local str1 = ToColorStr(num .. "/" .. lifesoul_num_vec[i], color1)
				self.lh_cost_item_list[i]:SetRightBottomColorText(str1)
				self.lh_cost_item_list[i]:SetRightBottomTextVisible(true)

				--self.node_list["LH_hecheng_cost_num"..i].text.text = str1
				-- local cfg = ItemWGData.Instance:GetItemConfig(lifesoul_id_vec[i])
 			-- 	local bundle, asset = ResPath.GetItem(cfg.icon_id)

 			-- 	self.node_list["LH_hecheng_item"..i].image:LoadSpriteAsync(bundle, asset,function ()
				-- 	self.node_list["LH_hecheng_item"..i].image:SetNativeSize()
				-- end)

			else
				self.node_list["LH_hecheng_lock"..i]:SetActive(true)
				self.node_list["LH_hecheng_name"..i].text.text = ""
				self.node_list["LH_hecheng_item"..i]:SetActive(false)
				self.node_list["LH_hecheng_cost_num"..i].text.text = ""
			end

		end
		--self.node_list["LH_hecheng_probability"].text.text = self.is_enough and "100%" or "0%"  --成功率
		self:HeChengCacularAttr(self.is_enough,lifesoul_id_vec,lifesoul_num_vec,need_double,equip_posy)
	end
end

function NewComposeView:HeChengCacularAttr(is_enough,lifesoul_id_vec,lifesoul_num_vec,need_double,equip_posy)
	local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(self.cur_hecheng_id)
	local attr_type,attr_value,add_value = LongHunWGData.Instance:GetEquipPosyAttrBySlot({item_id = self.cur_hecheng_id,level = 1})
	local name_list,is_pre = LongHunWGData.Instance:GetAttributeNameListByType(attr_type,true)

	local get_level = 1
	local bag_item_list = {}
	self.max_cost_level = {}
	if is_enough then
		for i = 1, 2 do
			if i == 1 or need_double then
				local need_bag_item = lifesoul_num_vec[i]
				if equip_posy[i] then
					need_bag_item = need_bag_item -1
				end
				if need_bag_item > 0 then
					bag_item_list[i] = {}
				 	local available_item_list = LongHunWGData.Instance:GetMingWenBagItemByID(lifesoul_id_vec[i])
				 	local find_num = 0
				 	for k,v in pairs(available_item_list) do
				 		if not self.max_cost_level[i] then
				 			self.max_cost_level[i] = v.level
				 		elseif self.max_cost_level[i] < v.level then
				 			self.max_cost_level[i] = v.level
				 		end

				 		if find_num < need_bag_item then
				 			find_num = find_num + 1
				 			bag_item_list[i][find_num] = v
				 		else
				 			local smallest = -1
				 			for t,q in pairs(bag_item_list[i]) do
				 				if smallest == -1 then
				 					smallest = t
				 				else
				 					if bag_item_list[i][smallest].level > q.level then
				 						smallest = t
				 					end
				 				end
				 			end
				 			if bag_item_list[i][smallest].level < v.level then
				 				bag_item_list[i][smallest] = v
				 			end
				 		end
				 	end
				end
			end
		end

		local get_exp = 0
		local get_minyuin = 0
		for i= 1,2 do
			if equip_posy[i] then
				local add_exp,add_minyin = LongHunWGData.Instance:GetMingWenFenJieGet(equip_posy[i])
				get_exp = get_exp + add_exp
				get_minyuin = get_minyuin + add_minyin
				if not self.max_cost_level[i] or  equip_posy[i].level > self.max_cost_level[i] then
					self.max_cost_level[i] = equip_posy[i].level
				end

			end
			if bag_item_list[i] then
				for k,v in pairs(bag_item_list[i]) do
					local add_exp,add_minyin = LongHunWGData.Instance:GetMingWenFenJieGet(v)
					get_exp = get_exp + add_exp
					get_minyuin = get_minyuin + add_minyin
				end
			end
		end

		get_level = LongHunWGData.Instance:CacularComposeLevel(base_cfg.cost_type,get_exp,get_minyuin)
	end

	for i =1 ,2 do
		if lifesoul_id_vec[i] and lifesoul_id_vec[i] ~= -1 then
			local base_cfg1 = LongHunWGData.Instance:GetMingWenBaseCfgByID(lifesoul_id_vec[i])
			local high_level = self.max_cost_level[i] and self.max_cost_level[i] or 1
			self.node_list["LH_hecheng_name"..i].text.text = ToColorStr(base_cfg1.name.."Lv."..high_level,ITEM_COLOR[base_cfg1.quality])
		else
			self.node_list["LH_hecheng_name"..i].text.text = ""
		end
	end

	self.node_list["LH_hecheng_target_name"].text.text = ToColorStr(base_cfg.name.."Lv."..get_level,ITEM_COLOR[base_cfg.quality])

	self.copmpse_item_list = bag_item_list
	self.compose_equip_list = equip_posy
end

function NewComposeView:LongHunClickCompose()
	if self.is_enough then
		local index_list = {}
		local count = 0
		local normal_sloat_num = LongHunWGData.Instance:GetNormalSlotNum()
		for i=1,2 do
			if self.compose_equip_list[i] then
				count = count + 1
				index_list[count] = {}
				if self.compose_equip_list[i].equip_index > normal_sloat_num then
					index_list[count].type = PPSY_OPERA_TYPE.POSY_FROM_SPECIAL_SLOT
					index_list[count].index = self.compose_equip_list[i].equip_index - normal_sloat_num - 1
				else
					index_list[count].type = PPSY_OPERA_TYPE.POSY_FROM_NORMAL_SLOT
					index_list[count].index = self.compose_equip_list[i].equip_index - 1
				end
			end
		end

		if not IsEmptyTable(self.copmpse_item_list) then
			for i=1,2 do
				if self.copmpse_item_list[i] then
					for k,v in pairs(self.copmpse_item_list[i]) do
						count = count + 1
						index_list[count] = {}
						index_list[count].type = PPSY_OPERA_TYPE.POSY_FROM_BAG
						index_list[count].index = v.index
					end
				end
			end
		end
		LongHunWGCtrl.Instance:SendMingWenCompose(self.cur_hecheng_id,count, index_list)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.ComposeNotEnough)
	end
end

function NewComposeView:LongHunClickTarget(index)
	if (index == 1 or index == 2) and self.cur_hecheng_data then
		local level = self.max_cost_level[index] or 1
		local posy_id_list = string.split(self.cur_hecheng_data.lifesoul_id_vec,"|")
		if not posy_id_list[index] then return end
		local data = {}
		data.item_id = tonumber(posy_id_list[index])
		if data.item_id <=0 then return end
		data.level = level
		LongHunWGCtrl.Instance:OpenLongHunOpera(data)
	elseif index == 3 and self.cur_hecheng_id then
		LongHunWGCtrl.Instance:OpenLongHunOpera({item_id = self.cur_hecheng_id,level = 1,})
	end
end

function NewComposeView:LongHunClickRemind()
	LongHunWGData.Instance:ChangeComposeRemind()
	self:FlushHeChengRed()
end

function NewComposeView:ShowLongHunSuccess()
	if self.node_list["LH_effect_pos"] then
		local bundle_name, asset_name = ResPath.GetEffectUi("UI_HCCG_F2")
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["LH_effect_pos"].transform, 1)
	end
end
--------------------------------------------------------------------------

LHHeChengListItemRender = LHHeChengListItemRender or BaseClass(BaseRender)

function LHHeChengListItemRender:__init()
	self.node_list["lock_btn"].button:AddClickListener(BindTool.Bind(self.OnClickLock, self))
end

function LHHeChengListItemRender:ReleaseCallBack()

end

function LHHeChengListItemRender:OnFlush()


	if IsEmptyTable(self.data) then return end
	local is_limite,condition = LongHunWGData.Instance:GetPosyIsLimite(self.data[1].get_id)
	self.node_list["lock_text"]:SetActive(is_limite)
	self.node_list["lock_text"].text.text = string.format(Language.MingWenView.HeChengCondition,condition)
	self.node_list["lock_btn"]:SetActive(is_limite)
	self.node_list["text_name"].text.text = self.data[1].sonlabel_name or ""
	self.node_list["text_name_hl"].text.text = self.data[1].sonlabel_name or ""
	self:FlushHeChengRed()
end

function LHHeChengListItemRender:OnClickLock()
	local is_limite,condition = LongHunWGData.Instance:GetPosyIsLimite(self.data[1].get_id)
	local str = string.format(Language.MingWenView.DuiHuanConditionTips,condition)
	TipWGCtrl.Instance:ShowSystemMsg(str)
end

function LHHeChengListItemRender:FlushHeChengRed()
	if IsEmptyTable(self.data) then
		self.node_list["img_remind"]:SetActive(false)
	else
		local is_red = LongHunWGData.Instance:GetMingWenItemCanHeCheng(self.data[1].get_id)
		self.node_list["img_remind"]:SetActive(is_red == 1)
	end
end
