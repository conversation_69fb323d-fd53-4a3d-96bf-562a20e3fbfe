-------------------------------奇闻异录羁绊界面-------------------------------
StrangeCatalogJiBanView = StrangeCatalogJiBanView or BaseClass(SafeBaseView)

function StrangeCatalogJiBanView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/strange_catalog_ui_prefab", "layout_qwyl_jiban")
	self.cur_select_group_type = -1
end

function StrangeCatalogJiBanView:__delete()
end

function StrangeCatalogJiBanView:ReleaseCallBack()
	if self.group_type_tab then
		self.group_type_tab:DeleteMe()
		self.group_type_tab = nil
	end

	if self.qwyl_jiban_list then
		self.qwyl_jiban_list:DeleteMe()
		self.qwyl_jiban_list = nil
	end

	self.cur_select_group_type = -1
end

function StrangeCatalogJiBanView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.StrangeCatalog.JiBanName

	self.group_type_tab = AsyncListView.New(StrangeCatalogJiBanTog, self.node_list.qwyl_group_type_list)
	self.group_type_tab:SetStartZeroIndex(true)
	self.group_type_tab:SetSelectCallBack(BindTool.Bind1(self.TopBarChangeToIndex, self))
	self.group_type_tab:SetRefreshCallback(BindTool.Bind1(self.RefreshCallback, self))
	self:RefreshTypeTab()

	if not self.qwyl_jiban_list then
		self.qwyl_jiban_list = AsyncListView.New(StrangeCatalogJiBanItem, self.node_list.qwyl_jiban_list)
	end
end

function StrangeCatalogJiBanView:RefreshTypeTab()
	local cfg = StrangeCatalogWGData.Instance:GetJiBanAllGroup()
	local tab_group_name_list = {}
	for k,v in pairs(cfg) do
		tab_group_name_list[k] = {}
		tab_group_name_list[k].index = k
		tab_group_name_list[k].name = v[1].group_name
	end

	self.group_type_tab:SetDataList(tab_group_name_list)
end

function StrangeCatalogJiBanView:RefreshCallback(item_cell, cell_index)
	item_cell:RefreshRedmind()
end

function StrangeCatalogJiBanView:TopBarChangeToIndex(cell)
	local cell_index = cell:GetData().index

	if self.cur_select_group_type == cell_index then
		return
	end

	self.cur_select_group_type = cell_index

	self:FlushJiBanListInfo()
end

function StrangeCatalogJiBanView:OnFlush()
	if not self.group_type_tab then
		return
	end

	self.group_type_tab:RefreshActiveCellViews()
	self:RefreshTypeTab()
	self:FlushJiBanListInfo()
end

function StrangeCatalogJiBanView:FlushJiBanListInfo()
	if not self.cur_select_group_type then
		return
	end

	local jiban_data_list = StrangeCatalogWGData.Instance:GetJiBanShowList(self.cur_select_group_type)
	if not jiban_data_list then
		return
	end

	if self.qwyl_jiban_list ~= nil then
		self.qwyl_jiban_list:SetDataList(jiban_data_list)
	end
end

------------StrangeCatalogJiBanTog--------------
StrangeCatalogJiBanTog = StrangeCatalogJiBanTog or BaseClass(BaseRender)
function StrangeCatalogJiBanTog:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.Text.text.text = self.data.name
	self.node_list.Text_hl.text.text = self.data.name
end

function StrangeCatalogJiBanTog:OnClick()
	if self.click_callback then
		self.click_callback(self.data.index)
	end
	self:SetSelectIndex(self.data.index)
end

function StrangeCatalogJiBanTog:OnSelectChange(is_select)
	self.node_list.qwyl_jiban_toggle.toggle.isOn = is_select
end

function StrangeCatalogJiBanTog:RefreshRedmind()
	if self.data == nil then
		return
	end

	local remind = StrangeCatalogWGData.Instance:GetJibanGroupRemind(self.data.index)
	self.node_list.RedPoint:SetActive(remind)
end

----------------------------StrangeCatalogJiBanItem羁绊升级item
StrangeCatalogJiBanItem = StrangeCatalogJiBanItem or BaseClass(BaseRender)
function StrangeCatalogJiBanItem:LoadCallBack()
	if not self.jiban_item_list then
		self.jiban_item_list = AsyncListView.New(ItemCell, self.node_list.jiban_item_list)
	end

	if not self.attr_list then
		self.attr_list = {}
		for i = 1, 2 do
			self.attr_list[i] = CommonAddAttrRender.New(self.node_list.attr_list:FindObj("attr" .. i))
			self.attr_list[i]:SetAttrNameNeedSpace(true)
		end
	end

	if not self.spe_list then
		self.spe_list = SpeAttrRender.New(self.node_list.spe_list:FindObj("attr1"))
	end

	XUI.AddClickEventListener(self.node_list.uplevel_btn, BindTool.Bind(self.OnClickLevelUpBtn, self))
end

function StrangeCatalogJiBanItem:ReleaseCallBack()
	if self.jiban_item_list then
		self.jiban_item_list:DeleteMe()
		self.jiban_item_list = nil
	end

	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end

		self.attr_list = nil
	end

	if self.spe_list then
		self.spe_list:DeleteMe()
		self.spe_list = nil
	end
end

function StrangeCatalogJiBanItem:OnFlush()
	if not self.data then
		return
	end

	self.node_list.group_name.text.text = self.data.seq_name
	local level = StrangeCatalogWGData.Instance:GetQWJiBanServerInfoBySeq(self.data.seq)
	local is_act = level > 0
	local is_max = level > 6

	self.node_list.group_level.text.text = string.format(Language.StrangeCatalog.CurJiBanLevel, level)
	self.node_list.btn_text.text.text = is_act and Language.StrangeCatalog.LevelUp or Language.StrangeCatalog.Active
	local next_jiban_cfg = StrangeCatalogWGData.Instance:GetJiBanCfgBySeq(self.data.seq, level + 1)
	if not IsEmptyTable(next_jiban_cfg) then
		local qua_name = Language.ShiTianSuit.InfuseSoulQuality[next_jiban_cfg.need_zhuling_level]
		self.node_list.group_condition.text.text = string.format(Language.StrangeCatalog.LevelReached, qua_name)
	end

	self.node_list.max_flag:SetActive(is_max)
	self.node_list.uplevel_btn:SetActive(not is_max)
	self.node_list.group_condition:SetActive(not is_max)

	local item_list = StrangeCatalogWGData.Instance:GetJiBanItemListBySeq(self.data.seq)
	self.jiban_item_list:SetDataList(item_list)

	local can_up_level = false  --判断升级的条件
	if not IsEmptyTable(next_jiban_cfg) then
		for k, v in ipairs(item_list) do
			local cur_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(v.seq)
			local qiwu_xl_level = cur_info.zhuling_level
			if qiwu_xl_level >= next_jiban_cfg.need_zhuling_level then
				can_up_level = true
			else
				can_up_level = false
				break
			end
		end
	end
	self.node_list.remind:SetActive(not is_max and can_up_level)

	local attr_data, special_attr = StrangeCatalogWGData.Instance:GetJiBanAttrListBySeq(self.data.seq)
	for k, v in pairs(self.attr_list) do
		v:SetData(attr_data[k])
	end

	if not IsEmptyTable(special_attr) then
		self.spe_list:SetData(special_attr)
	end
end

function StrangeCatalogJiBanItem:OnClickLevelUpBtn()
	if not self.data then
		return
	end

	local item_list = StrangeCatalogWGData.Instance:GetJiBanItemListBySeq(self.data.seq)
	local level = StrangeCatalogWGData.Instance:GetQWJiBanServerInfoBySeq(self.data.seq)
	local next_jiban_cfg = StrangeCatalogWGData.Instance:GetJiBanCfgBySeq(self.data.seq, level + 1)
	local can_act = false
	for k, v in ipairs(item_list) do
		local cur_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(v.seq)
		local qiwu_xl_level = cur_info.zhuling_level
		if qiwu_xl_level >= next_jiban_cfg.need_zhuling_level then
			can_act = true
		else
			can_act = false
			break
		end
	end

	if can_act then
		StrangeCatalogWGCtrl.Instance:OnHandBookOperate(HANDBOOK_OPERA_TYPE.JIBAN, self.data.seq)
	else
		local qua_name = Language.ShiTianSuit.InfuseSoulQuality[next_jiban_cfg.need_zhuling_level]
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.StrangeCatalog.LevelReached, qua_name))
	end
end

SpeAttrRender = SpeAttrRender or BaseClass(BaseRender)
function SpeAttrRender:__delete()
	self:KillArrowTween()
end

function SpeAttrRender:KillArrowTween()
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function SpeAttrRender:LoadCallBack()
	self:KillArrowTween()
	local tween_time = 0.8
	local node = self.node_list.arrow
	if node then
		RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -18)
		self.arrow_tweener = node.rect:DOAnchorPosY(-12, tween_time)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function SpeAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	self.node_list.attr_name.text.text = self.data.attr_str
	self.node_list.attr_value.text.text = self.data.attr_value / 100 .. "%"
	self.node_list.add_value.text.text = self.data.add_value / 100 .. "%"

	if self.data.add_value > 0 then
		self.node_list.arrow:SetActive(true)
	end

end