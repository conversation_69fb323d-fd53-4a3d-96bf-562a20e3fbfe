-----------------------------------------------------
-- 通用结构体
-----------------------------------------------------
CommonStruct = CommonStruct or {}

-- 物品参数
function CommonStruct.ItemParamData()
	return {
		quality = 0,								-- 品质
		shen_level = 0,								-- 神铸等级
		fuling_level = 0,							-- 附灵等级
		has_lucky = 0,								-- 幸运属性
		fumo_id = 0,								-- 附魔id

		strengthen_level = 0,						-- 强化等级
		star_level = 0,								-- 升星等级
		xianpin_type_list = {},						-- 仙品属性
		baoshi_t = {},								-- 宝石 list
		refine_level = 0,							-- 精炼等级

		suit_index = 0,								-- 套装激活 id
		suit_open_num = 0,							-- 套装激活件数
		impression = 0,								-- 装备印记
		is_refine = 0,								-- 是否真炼
	}
end

-- 物品数据
function CommonStruct.ItemDataWrapper()
	return {
		item_id = 0,
		num = 0,
		is_bind = 0,
		has_param = 0,
		invalid_time = 0,
		gold_price = 0,
		index = 0,
		frombody = true, 						--param 使用自己的装备信息 false为解析服务端
		param = CommonStruct.ItemParamData()
	}
end

-- 神兽物品数据
function CommonStruct.SSItemDataWrapper()
	return {
		index = 0,
		item_id = 0,
		strength_level = 0,
		shuliandu = 0,
		attr_list = {},
	}
end

-- 铜币
function CommonStruct.CoinDataWrapper(num)
	num = num or 0
	local vo = CommonStruct.ItemDataWrapper()
	vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN
	vo.num = num
	return vo
end

-- 绑定元宝
function CommonStruct.BindGoldDataWrapper(num)
	num = num or 0
	local vo = CommonStruct.ItemDataWrapper()
	vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL
	vo.num = num
	return vo
end

-- 经验
function CommonStruct.ExpDataWrapper(num)
	num = num or 0
	local vo = CommonStruct.ItemDataWrapper()
	vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_EXP
	vo.num = num
	return vo
end
-- 女娲石
function CommonStruct.NvWaShiDataWrapper(num)
	num = num or 0
	local vo = CommonStruct.ItemDataWrapper()
	vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_NVWASHI
	vo.num = num
	return vo
end

-- 仙魂
function CommonStruct.XianHunDataWrapper(num)
	num = num or 0
	local vo = CommonStruct.ItemDataWrapper()
	vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_XIANHUN
	vo.num = num
	return vo
end

-- 真气
function CommonStruct.YuanLiDataWrapper(num)
	num = num or 0
	local vo = CommonStruct.ItemDataWrapper()
	vo.item_id = COMMON_CONSTS.VIRTUAL_ITEM_YUANLI
	vo.num = num
	return vo
end

-- 真气
function CommonStruct.UniqueUserID()
	return {plat_type = -1, server_id = -1, role_id = 0}
end