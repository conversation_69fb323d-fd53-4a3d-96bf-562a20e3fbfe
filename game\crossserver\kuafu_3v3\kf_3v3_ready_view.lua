KF3V3ReadyView = KF3V3ReadyView or BaseClass(SafeBaseView)
function KF3V3ReadyView:__init()
	self.view_layer = UiLayer.PopTop
	self.view_style = ViewStyle.Full
	self.active_close = false
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_3v3_new_ready")
end

function KF3V3ReadyView:ReleaseCallBack()
	if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end
	if self.match_info_change_event then
		GlobalEventSystem:UnBind(self.match_info_change_event)
		self.match_info_change_event = nil
	end
	if self.loading_view_close_event then
		GlobalEventSystem:UnBind(self.loading_view_close_event)
		self.loading_view_close_event = nil
	end
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
end

function KF3V3ReadyView:LoadCallBack()
    self.member_info_list = {}
	for i = 1, 6 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = KF3V3ReadyRender.New(self.node_list["info"..i])
			self.member_info_list[i]:SetIndex(i)
        end
    end
	self.match_info_change_event = GlobalEventSystem:Bind(OtherEventType.KF3V3MatchInfoChange, BindTool.Bind(self.OnMatchInfoChange, self))
	--self.node_list.blue_bg.rect.anchoredPosition = Vector2(0, 300)
	--self.node_list.red_bg.rect.anchoredPosition = Vector2(0,-300)

	--self.node_list["blue_bg"]:SetActive(false)
	--self.node_list["red_bg"]:SetActive(false)

	if Scene.Instance:IsSceneLoading() then
		if not self.loading_view_close_event then
			self.loading_view_close_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.SceneLoadComplete,self))
		end
	else
		GlobalTimerQuest:AddDelayTimer(function()
			self:Flush()
			self:DoAnim()
		end, 0.7)
	end
end

function KF3V3ReadyView:SceneLoadComplete()
	GlobalTimerQuest:AddDelayTimer(function()
		self:Flush()
		self:DoAnim()
	end, 0.7)
end

function KF3V3ReadyView:ShowIndexCallBack()
end

function KF3V3ReadyView:DoAnim()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	self.sequence = DG.Tweening.DOTween.Sequence()
	self.sequence:AppendCallback(function()
		--self.node_list["blue_bg"]:SetActive(true)
		--self.node_list["red_bg"]:SetActive(true)

    	--self.node_list["blue_bg"].rect:DOAnchorPosY(0, 0.2)
		--self.node_list["red_bg"].rect:DOAnchorPosY(0, 0.2)
    end)
	self.sequence:AppendInterval(0.2)
	self.sequence:AppendCallback(function()
		-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_kuafu_3v3_01)--UI_jinjiechenggong)
		-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_boom_pos.transform, 2, nil, nil)
    end)
	self.sequence:AppendInterval(1)
	self.sequence:AppendCallback(function()
		KF3V3WGCtrl.Instance:SendReady()
    end)
end

function KF3V3ReadyView:OnFlush()
	local info_list = KF3V3WGData.Instance:GetMatchInfo()
	if not info_list or #info_list < 0 then
		return
	end
	for i = 1, #info_list do
		self.member_info_list[i]:SetData(info_list[i])
	end
end

function KF3V3ReadyView:OnMatchInfoChange(notify_reason)
	if notify_reason == Cross3V3MatchingInfoNotifyReason.ClientReady then
		self:Flush()
	end
end


KF3V3ReadyRender = KF3V3ReadyRender or BaseClass(BaseRender)
function KF3V3ReadyRender:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function KF3V3ReadyRender:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function KF3V3ReadyRender:OnFlush()
    if not self.data then
        return
    end
	self.node_list.root:SetActive(self.data.uid ~= 0)
	local play_name = self.data.name
    local play_name_list = Split(self.data.name, "_")
    if not IsEmptyTable(play_name_list) then
        if play_name_list[2] then
            play_name = string.format(Language.KuafuPVP.ServerName_1, play_name_list[2], play_name_list[1])
        else
            local main_vo = RoleWGData.Instance:GetRoleVo()
			local server_str = "s" .. main_vo.merge_server_id
			play_name = string.format(Language.KuafuPVP.ServerName_1, server_str, play_name_list[1])
        end
    end
	self.node_list.role_name.text.text = play_name
	--self.node_list.zhandui_name.text.text = self.data.zhandui_name

    --[[
    if KF3V3WGData.Instance:GetMySide() == self.data.side then--策划需求两方的段位都要显示,先屏蔽此处
        self.node_list.score:SetActive(true)
        local grade_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.score)
        self.node_list.score.text.text = grade_cfg.name
        ChangeToQualityText(self.node_list.score.text, RankGradeEnum[grade_cfg.rank_id])
    else
        self.node_list.score:SetActive(false)
    end
    --]]

    -- self.node_list.score:SetActive(true)
    -- local grade_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.score)
	-- self.node_list["score"].image:LoadSprite(ResPath.GetNoPackPNG("a2_duanwei" .. grade_cfg.grade)) 		-- 段位icon

    if self.data.uid ~= 0 then
    	local data = {}
    	local uid = self.data.uid
    	--高位表示平台类型 平台类型为0表示是机器人
    	if self.data.uuid and self.data.uuid.temp_high == 0 then
    		uid = 0
    	end
	    data.role_id = uid
	    data.prof = self.data.prof
	    data.sex = self.data.sex
	    data.fashion_photoframe = self.data.photoframe
    	self.head_cell:SetData(data)

		local img_name = self.index <= 3 and "a3_jjc_txkl" or "a3_jjc_txkh"
		local bundle, asset = ResPath.GetF2Field1v1(img_name)
		self.head_cell:ChangeBg(bundle, asset, true)
    end

	-- 战力 
	self.node_list["cap"].text.text = self.data.capability or 0

	if self.node_list.career_upgrade_type then
		local can_show = self.data.career_upgrade_type and self.data.career_upgrade_type > 0
		self.node_list.career_upgrade_type:SetActive(can_show)
		if can_show then
			self.node_list.career_upgrade_type.image:LoadSprite(ResPath.GetCareerTypeImage(self.data.career_upgrade_type))
		end
	end
end

