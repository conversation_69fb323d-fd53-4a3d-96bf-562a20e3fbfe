﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using Nirvana;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
/// <summary>
/// The effect of this scene.
/// </summary>
[ExecuteInEditMode]
[RequireComponent(typeof(AudioSource))]
public sealed class SceneAudio : SceneObject
{
    private AudioSource adudio;

    /// <summary>
    /// Gets the Sound.
    /// </summary>
    public AudioSource Sound
    {
        get { return this.adudio; }
    }

    private void Awake()
    {
        this.adudio = this.transform.GetComponent<AudioSource>();
    }

    private void OnValidate()
    {
        this.name = "Sound";
    }
}

#endif
