CrossFlowerRankWGData = CrossFlowerRankWGData or BaseClass()

function CrossFlowerRankWGData:__init()
    if CrossFlowerRankWGData.Instance then
		error("[CrossFlowerRankWGData] Attempt to create singleton twice!")
		return
	end
    
	CrossFlowerRankWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("cross_charm_rank_auto")
	self.rank_reward_cfg = ListToMapList(cfg.rank_reward, "sex")

	self.cur_sex = 0
end

function CrossFlowerRankWGData:__delete()
    CrossFlowerRankWGData.Instance = nil
end

function CrossFlowerRankWGData:GetRankListCfg()
	return self.rank_reward_cfg
end

--获取性别配置
function CrossFlowerRankWGData:GetRankSexListCfg(sex)
	return self.rank_reward_cfg[sex] or {}
end

--没有上榜
function CrossFlowerRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, charm = 0}
end

function CrossFlowerRankWGData:ExpandRankData(rank_list, nNextIndex, nCurRank, index)
	local next_data = rank_list[nNextIndex]
	if next_data then
		local nNextRank = next_data.rank
		if nCurRank + 1 ~= nNextRank then -- 说明断了 要补一下
			for nSectionIndex = nCurRank + 1, nNextRank - 1 do
				local data = {}
				data.no_true_rank = true
				data.index = index
				data.rank_data = self:CreatNoRankItemData(nSectionIndex)
				table.insert(self.flowerRankdata.rank_list, data)
				index = index + 1
			end
		end
	end

	return index
end

function CrossFlowerRankWGData:SetCrossFlowerRankInfo(protocol)
	self.flowerRankdata = {}
	self.flowerRankdata.rank_list = {}

	local index = 1
	local cfg = self:GetRankSexListCfg(protocol.sex_type)
	self.cur_sex = protocol.sex_type
	self.my_charm = protocol.my_charm

	for i = 1, #protocol.rank_list do
		if i == 1 then
			if protocol.rank_list[i].rank ~= 1 then -- 没有第一名
				local item = {}
				item.rank_data = self:CreatNoRankItemData(1)
				item.index = index
				item.no_true_rank = true
				index = index + 1
				table.insert(self.flowerRankdata.rank_list, item)
				index = self:ExpandRankData(protocol.rank_list, 1, 1, index)
			end
		end

		local item = {}
		item.index = index
		item.rank_data = protocol.rank_list[i]
		local nCurRank = item.rank_data.rank
		table.insert(self.flowerRankdata.rank_list, item)
		index = index + 1

		index = self:ExpandRankData(protocol.rank_list, i + 1, nCurRank, index)
	end

	local rank_cfg = cfg[#cfg]
	local nMaxRank = rank_cfg.max_rank
	if index - 1 < nMaxRank then
		for nSectionIndex = index, nMaxRank do
			local data = {}
			data.no_true_rank = true
			data.index = nSectionIndex
			data.rank_data = self:CreatNoRankItemData(nSectionIndex)
			table.insert(self.flowerRankdata.rank_list, data)
		end
	end
end

--玩家当前魅力值
function CrossFlowerRankWGData:GetMyChramInfo()
	return self.my_charm or 0
end

--组合完成的排行数据
function CrossFlowerRankWGData:GetRankInfo()
	if self.flowerRankdata ~= nil and self.flowerRankdata.rank_list ~= nil then
		return self.flowerRankdata.rank_list	
	end
	return nil
end

--获取配置表排行区间数据
function CrossFlowerRankWGData:GetRankItemData(rank_data)
	local rank_cfg = CrossFlowerRankWGData.Instance:GetRankSexListCfg(self.cur_sex)
	if rank_cfg ~= nil and rank_data ~= nil then
		for _,v in ipairs(rank_cfg) do
			if rank_data.rank >= v.min_rank and rank_data.rank <= v.max_rank then
				return v
			end
		end
	end

	return nil
end

--获取自己上榜信息
function CrossFlowerRankWGData:GetMyRankInfo()
	local rank_list = self:GetRankInfo()
	if rank_list == nil then
		return nil
	end

	local my_uid = RoleWGData.Instance:GetUUid()
	for k, v in pairs(rank_list) do
		if v.rank_data and v.rank_data.uuid then
			if v.rank_data.uuid == my_uid then
				return v
			end
		end
	end

	return nil
end

--获取前三名信息
function CrossFlowerRankWGData:GetTopThreeRankInfo()
	local rank_cfg = self:GetRankInfo()
	if rank_cfg == nil then
		return
	end

	local data_list = {}
	for i = 1, 3 do
		table.insert(data_list, rank_cfg[i])
	end

	return data_list
end
