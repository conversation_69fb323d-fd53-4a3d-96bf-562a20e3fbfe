GuildBattleSceneLogic = GuildBattleSceneLogic or BaseClass(CommonActivityLogic)

function GuildBattleSceneLogic:__init()

end

function GuildBattleSceneLogic:__delete()

end

function GuildBattleSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonActivityLogic.Enter(self, old_scene_type, new_scene_type)
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.GUILD)
	-- if MainuiWGCtrl.Instance:GetMenuIsShow() then
	-- 	GuildBattleWGCtrl.Instance:Close()
	-- else
	-- 	GuildBattleWGCtrl.Instance:Open()
	-- end
	local act_statu = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.XIANMENGZHAN)
	if nil ~= act_statu then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(act_statu.next_time)
	end
end

function GuildBattleSceneLogic:Out()
	CommonActivityLogic.Out(self)

	-- GuildBattleWGCtrl.Instance:Close()
end


function GuildBattleSceneLogic:GetRoleNameBoardText(role_vo)
	local role_kill = role_vo.special_param or 0

	local t = {}
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	t.color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t.text = role_vo.name

	if role_kill >= 3 then
		t.text = t.text .. "<color=" .. COLOR3B.YELLOW .. ">" .. string.format(Language.Dungeon.Kills, role_kill) .. "</color>"
	end
	return t
end

-- 获取角色仙盟名
function GuildBattleSceneLogic:GetGuildNameBoardText(role_vo)
	local t = {}
	local index = 1
	local guild_name = role_vo.guild_name or ""
	local guild_post = role_vo.guild_post

	if "" == guild_name then return t end
	guild_name = "【" .. guild_name .. "】"
	local authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[guild_post]
	local post_name = authority and authority.post or ""
	local guild_id = RoleWGData.Instance.role_vo.guild_id

	t[index] = {}
	t[index].color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t[index].text = guild_name .. COMMON_CONSTS.POINT
	index = index + 1

	t[index] = {}
	t[index].color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t[index].text = post_name

	return t
end

-- 获取采集物特殊名字显示
function GuildBattleSceneLogic:GetGatherSpecialText(gather_vo)
	local t = {}
	-- local guild_name, guild_camp = GuildBattleData.Instance:GetGuildNameByGatherId(gather_vo.gather_id)
	-- if guild_name then
	-- 	t[1] = {}
	-- 	t[1].color = CAMP_COLOR3B[guild_camp]
	-- 	t[1].text = "【" .. guild_name .. "】"
	-- 	return t
	-- end
	return t
end

function GuildBattleSceneLogic:IsRoleEnemy(target_obj, main_role)
	if main_role:GetVo().guild_id == target_obj:GetVo().guild_id then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end

-- -- 是否是挂机打怪的敌人
-- function GuildBattleSceneLogic:IsGuiJiMonsterEnemy(target_obj)
-- 	if nil == target_obj or target_obj:GetType() ~= SceneObjType.Role
-- 		or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
-- 		return false
-- 	end
-- 	return true
-- end

-- -- 获取挂机打怪的敌人
-- function GuildBattleSceneLogic:GetGuiJiMonsterEnemy()
-- 	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
-- 	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
-- 	return Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
-- end

function GuildBattleSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
	-- GuildBattleWGCtrl.Instance:ShowAction(is_show)
end

-- 此场景优先保证单位数量
function GuildBattleSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function GuildBattleSceneLogic:IsEnemyVisiblePriortiy()
	return true
end