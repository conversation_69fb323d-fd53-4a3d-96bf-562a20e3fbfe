BossXiezhuRuleView = BossXiezhuRuleView or BaseClass(SafeBaseView)
local normal_viewport_high = 154
local normal_panel_high = 390
local ATTR_COUNT = 10
function BossXiezhuRuleView:__init()
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_xiezhu_reward")
    self:SetMaskBg(true)
end

function BossXiezhuRuleView:__delete()
end

function BossXiezhuRuleView:ReleaseCallBack()
    self.des_list = {}
    if nil ~= self.reward_grid then
        self.reward_grid:DeleteMe()
        self.reward_grid = nil
    end
end

function BossXiezhuRuleView:LoadCallBack()
    self.des_list = {}
    for i=1,ATTR_COUNT do
		self.des_list[i] = self.node_list.des_list:FindObj("text" .. i)
	end
    if not self.reward_grid then
        self.reward_grid = AsyncBaseGrid.New()
		local t = {}
		t.col = 4
		t.change_cells_num = 1
		t.itemRender = ItemCell
		t.list_view = self.node_list["ph_grid"]
		self.reward_grid:CreateCells(t)
        self.reward_grid:SetStartZeroIndex(false)
    end
end

function BossXiezhuRuleView:OnFlush()
    local reward_data = BossXiezhuWGData.Instance:GetXiezhuReward()
	self.reward_grid:SetDataList(reward_data)

    local str =  Language.BossXiezhu.XieZhuMainTuleTip
    local reward_tips_des = Split(str, "\n") 
    local index = #reward_tips_des
    for i = 1, ATTR_COUNT do
        self.des_list[i]:SetActive(i <= index)
        if i <= index then
            self.des_list[i].text.text = reward_tips_des[i]
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.des_list[i].rect)
        end
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.des_list.rect)
    local now_high = self.node_list.des_list.rect.sizeDelta.y
    local panel_high = normal_panel_high
    if now_high < normal_viewport_high then
        self.node_list.scroll.scroll_rect.enabled = false
        panel_high = normal_panel_high - (normal_viewport_high - now_high)
    else
        self.node_list.scroll.scroll_rect.enabled = true
        panel_high = normal_panel_high
    end
    self.node_list.panel.rect.sizeDelta = Vector2(376, panel_high)
end

function BossXiezhuRuleView:ShowOrHideXieZhuRule()
	local is_active = self.node_list["xiezhu_rule_panel"]:GetActive()
	if is_active then
		self.node_list["xiezhu_rule_panel"]:SetActive(false)
	else
		self.node_list["xiezhu_rule_panel"]:SetActive(true)
	end
end