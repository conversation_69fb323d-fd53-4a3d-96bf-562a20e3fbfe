MarketSellTips = MarketSellTips or BaseClass(SafeBaseView)

function MarketSellTips:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel_2", {vector2 = Vector2(0, -6), sizeDelta = Vector2(892, 594)})
	self:AddViewResource(0, "uis/view/market_ui_prefab", "market_sell_tips")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function MarketSellTips:__delete()
end

function MarketSellTips:OpenCallBack()
	if not MarketWGData.Instance:CheckIsCanMarket(self.data) then
		self:Close()
		return
	end

	MarketWGCtrl.Instance:SendReqAuctionTypeInfo()
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_id)
	MarketWGCtrl.Instance:SendCSRoleAuctionItem(cfg.big_id, cfg.small_id)
	self.password = 0

	if self.data.is_bangyu then
		local other_cfg = MarketWGData.Instance:GetOtherCfg()
		local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
		local min_shelves_gond_bind_num = other_cfg.min_shelves_gond_bind_num
		local can_conver_num = math.floor(bangyu_money / min_shelves_gond_bind_num)
		self.amount = can_conver_num > 0 and 1 or 0
		self.total_price = other_cfg.consume_gold * self.amount
		self.unit_price = other_cfg.consume_gold
	else
		self.amount = 1
		local star_level = self.data.param and self.data.param.star_level or 0
		self.total_price = self:GetSuggessedPrice() * self.amount
		self.unit_price = self:GetSuggessedPrice()
	end


	--self.total_price = Language.Market.TipsTotalPrice--MarketWGData.Instance:GetMinPrice()
end

function MarketSellTips:CloseCallBack()
	self.need_open_market = nil
end

-- 获得推荐价格
function MarketSellTips:GetSuggessedPrice()
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_id)
	local star_level = self.data.param and self.data.param.star_level or 0
	local suggessed_price = MarketWGData.Instance:ConvertAuctionPrice(cfg, star_level) 
	return suggessed_price
end

function MarketSellTips:LoadCallBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.Market.Tab2
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.other_same_goods_list = AsyncListView.New(MarketSellTipsSameGoods, self.node_list["other_same_goods_list"])

	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind1(self.OnClickPlus, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind1(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["password_btn"], BindTool.Bind1(self.OnClickSetPassword, self))
	XUI.AddClickEventListener(self.node_list["amount_btn"], BindTool.Bind1(self.OnClickSetAmount, self))
	XUI.AddClickEventListener(self.node_list["sell_btn"], BindTool.Bind1(self.OnClickSell, self))
	XUI.AddClickEventListener(self.node_list["total_price_btn"], BindTool.Bind1(self.OnClickSetTotalPrice, self))
	XUI.AddClickEventListener(self.node_list["no_jump_flag_btn"], BindTool.Bind1(self.OnClickJumpFlag, self))

	self.password = 0
	if self.data.is_bangyu then
		local other_cfg = MarketWGData.Instance:GetOtherCfg()
		local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
		local min_shelves_gond_bind_num = other_cfg.min_shelves_gond_bind_num
		local can_conver_num = math.floor(bangyu_money / min_shelves_gond_bind_num)
		self.amount = can_conver_num > 0 and 1 or 0
		self.total_price = other_cfg.consume_gold * self.amount
	else
		self.amount = 1
		self.total_price = self:GetSuggessedPrice() * self.amount
	end
	self:InitPanel()
end

function MarketSellTips:InitPanel()
	local password_quanxian = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.MARKET_PASSWORD)
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	local limit_level = 0
	for i = 0, max_level - 1 do
		if password_quanxian["param_" .. i] > 0 then
			limit_level = i
			break
		end
	end

	self.node_list.vip_tips.text.text = limit_level > 0 and string.format(Language.Market.PasswordVipLevel, limit_level) or ""
end

function MarketSellTips:ReleaseCallBack()
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end	

	if self.other_same_goods_list then
		self.other_same_goods_list:DeleteMe()
		self.other_same_goods_list = nil
	end

	self.need_open_market = nil
end

function MarketSellTips:SetData(data, need_open_market)
	self.data = data
	self.need_open_market = need_open_market
end

function MarketSellTips:OnFlush(param_list, index)
	self:FlushUnitPrice()
	self:FlushTotalPrice()
	self:FlushAmount()
	self:FlushAmountBtnStatus()
	--self:FlushUnitBtnStatus()
	self:FlushPassword()

	self.item_cell:SetData(self.data)
	if self.data.is_bangyu then
		self.item_cell:SetRightBottomTextVisible(false)
	else
		self.item_cell:SetRightBottomTextVisible(true)
	end

	local samp_goods_list_info = MarketWGData.Instance:GetGoodsInfoByItemid(self.data.item_id)

	self.other_same_goods_list:SetDataList(samp_goods_list_info)
	self.node_list["empty_tips"]:SetActive(IsEmptyTable(samp_goods_list_info))
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, false)

	-- 剩余可上架数目
	self.node_list["leftover_amount"]:SetActive(not self.data.is_bangyu)
	self.node_list["leftover_amount"].text.text = string.format(Language.Market.LeftoverStr, MarketWGData.Instance:CanSellAmount() - MarketWGData.Instance:GetMyGoodsListAmount())

	--特殊显示数量
	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	self.node_list["spe_show_num_str"]:SetActive(self.data.is_bangyu)
	self.node_list["spe_show_num_str"].text.text =  string.format(Language.Market.HaveBindGoldNum, other_cfg.min_shelves_gond_bind_num)   

	-- Vip税率说明
	self.node_list["vip_tax"].text.text = string.format(Language.Market.VipTaxDesc, VipWGData.Instance:GetRoleVipLevel(), MarketWGData.Instance:GetTax() * 100)

	-- 货币类型
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_id)
	local auction_price_type = cfg.auction_price_type
	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		self.node_list.total_price_img.image:LoadSprite(bundel ,asset, function ()
			self.node_list.total_price_img.image:SetNativeSize()
		end)

		self.node_list.unit_price_img.image:LoadSprite(bundel, asset, function ()
			self.node_list.unit_price_img.image:SetNativeSize()
		end)
	end

	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local prof_meet = item_cfg.limit_prof == prof or item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT
	local sex_meet = item_cfg.limit_sex == sex or item_cfg.limit_sex == GameEnum.SEX_NOLIMIT
	local is_sure_prof = prof_meet and sex_meet
	self.node_list["spe_tips_str"].text.text = is_sure_prof and "" or Language.Market.SpeTipStr[0]

	self.node_list["week_shelves_count_str"]:SetActive(self.data.is_bangyu)
	self.node_list["week_active_str"]:SetActive(self.data.is_bangyu)

	local week_active, week_shelves_count, jump_flag = MarketWGData.Instance:GetAuctionInfo()
	if self.data.is_bangyu then
		local other_cfg = MarketWGData.Instance:GetOtherCfg()
		local shelves_week_active = other_cfg.shelves_week_active
		local week_shelves_num = other_cfg.week_shelves_num
		local shelves_color = week_shelves_num > week_shelves_count and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		local shelves_str = ToColorStr(week_shelves_count .. "/" .. week_shelves_num, shelves_color)
		self.node_list["week_shelves_count_str"].text.text = string.format(Language.Market.WeekShelves[1], shelves_str)

		local active_color = week_active >= shelves_week_active and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		local active_str = ToColorStr(week_active .. "/" .. shelves_week_active, active_color)
		self.node_list["week_active_str"].text.text = string.format(Language.Market.WeekShelves[2], active_str)
	end

	self.node_list.no_jump_flag_btn:SetActive(self.need_open_market)
	self.node_list.no_jump_flag_img:SetActive(jump_flag == 1)
end

function MarketSellTips:FlushUnitPrice()
	local unit_price = 0
	if type(self.total_price) == "number" and math.floor(self.total_price/self.amount) > 0 then
		unit_price = math.ceil(self.total_price / self.amount) 
	elseif self.data.is_bangyu then 
		unit_price = 0
	else
		unit_price = 1
	end

	if self.node_list["unit_price"] then
		self.node_list["unit_price"].text.text = unit_price
	end
end

function MarketSellTips:FlushAmount()
	self.node_list["amount"].text.text = self.amount
end

function MarketSellTips:FlushTotalPrice()
	if self.node_list["total_price"] and self.total_price then
		self.node_list["total_price"].text.text = self.total_price--self.unit_price * self.amount
	end
end

function MarketSellTips:FlushPassword()
	--self.node_list["set_password_succ"]:SetActive(self.password >= 100000)
	if self.password >= 100000 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PasswordSetSuc)
	end
end

function MarketSellTips:FlushAmountBtnStatus()
	if self.data.is_bangyu then
		local other_cfg = MarketWGData.Instance:GetOtherCfg()
		local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
		local min_shelves_gond_bind_num = other_cfg.min_shelves_gond_bind_num
		local can_conver_num = math.floor(bangyu_money / min_shelves_gond_bind_num)
		XUI.SetGraphicGrey(self.node_list["btn_plus"], self.amount >= can_conver_num)
		XUI.SetGraphicGrey(self.node_list["btn_minus"], self.amount <= 1)
	else
		XUI.SetGraphicGrey(self.node_list["btn_plus"], self.amount >= self.data.num)
		XUI.SetGraphicGrey(self.node_list["btn_minus"], self.amount <= 1)
	end
end

function MarketSellTips:FlushUnitBtnStatus()
	XUI.SetGraphicGrey(self.node_list["btn_unitplus"], self.unit_price >= 999999)
	XUI.SetGraphicGrey(self.node_list["btn_unitminus"], self.unit_price <= MarketWGData.Instance:GetMinPrice())
end

function MarketSellTips:ShowIndexCallBack()

end

function MarketSellTips:OnClickPlus()
	if nil == self.data then
		return
	end

	if self.data.is_bangyu then
		local other_cfg = MarketWGData.Instance:GetOtherCfg()
		local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
		local min_shelves_gond_bind_num = other_cfg.min_shelves_gond_bind_num
		local can_conver_num = math.floor(bangyu_money / min_shelves_gond_bind_num)
		if can_conver_num > 0 then
			self:ChangeAmount(math.min(self.amount + 1, can_conver_num))
		end
	else
		self:ChangeAmount(math.min(self.amount + 1, self.data.num))
	end
end

function MarketSellTips:OnClickMinus()
	if nil == self.data then
		return
	end

	if self.data.is_bangyu then
		if self.amount > 0 then
			self:ChangeAmount(math.max(self.amount - 1, 1))
		end
	else
		self:ChangeAmount(math.max(self.amount - 1, 1))
	end
end

function MarketSellTips:ChangeAmount(new_amount)
	self.total_price = math.floor(self.total_price / self.amount * new_amount)
	self.amount = new_amount
	self:FlushAmount()
	self:FlushAmountBtnStatus()
	self:FlushTotalPrice()
	self:FlushUnitPrice()
end

--点击设置总价
function MarketSellTips:OnClickSetTotalPrice()
	if nil == self.data then
		return
	end

	local change_price_limit = GLOBAL_CONFIG.param_list.market_can_change_price == 1
	if change_price_limit then
		return
	end

	if self.data.is_bangyu then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Market.NoChangePrice)
		return
	end

	-- 货币类型
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_id)
	local auction_price_type = cfg.auction_price_type
	if auction_price_type == AUCTION_PRICE_TYPE.GOLD_BIND then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Market.NoChangePrice)
		return
	end

	local function callback(input_num)
		self.total_price = input_num
		self:FlushUnitPrice()
		self:FlushTotalPrice()
		--self:FlushUnitBtnStatus()
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.total_price)
	num_keypad:SetMaxValue(999999999)
	num_keypad:SetMinValue(MarketWGData.Instance:GetMinPrice())
	num_keypad:SetOkCallBack(callback)
	num_keypad:SetMinValueTips(string.format(Language.Market.SellMinValueTips, MarketWGData.Instance:GetMinPrice()))
end

-- 点击设置数量
function MarketSellTips:OnClickSetAmount()
	if nil == self.data then
		return
	end
	local function callback(input_num)
		self:ChangeAmount(input_num)
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.amount)
	if self.data.is_bangyu then
		local other_cfg = MarketWGData.Instance:GetOtherCfg()
		local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
		local min_shelves_gond_bind_num = other_cfg.min_shelves_gond_bind_num
		local can_conver_num = math.floor(bangyu_money / min_shelves_gond_bind_num)
		num_keypad:SetMaxValue(can_conver_num)
		num_keypad:SetMinValue(can_conver_num > 0 and 1 or 0)
	else
		num_keypad:SetMaxValue(self.data.num)
		num_keypad:SetMinValue(1)
	end

	num_keypad:SetOkCallBack(callback)
end

-- 点击设置密码按钮
function MarketSellTips:OnClickSetPassword()
	-- 判断VIP权限
	if not MarketWGData.Instance:CanSetPassword() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.VipLevelNo)
		return
	end


	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetPopString(Language.Market.ScaleMiMa)
	num_keypad:SetMaxValue(999999, true)
	num_keypad:SetOkCallBack(function(input_num)
		if math.floor(input_num / 100000) == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.ErrorMiMa)
			self.password = 0
			self:FlushPassword()
			return
		end
		self.password = input_num
		self:FlushPassword()
	end)
end

-- 点击出售
function MarketSellTips:OnClickSell()
	if self.amount <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.SellSuccTips2)
		self:Close()
		return
	end

	if type(self.total_price) == "number" then
		local auction_item_type = AUCTION_ITEM_TYPE.ITEM
		if self.data.is_bangyu then
			local week_active, week_shelves_count = MarketWGData.Instance:GetAuctionInfo()
			local other_cfg = MarketWGData.Instance:GetOtherCfg()
			local shelves_week_active = other_cfg.shelves_week_active
			local week_shelves_num = other_cfg.week_shelves_num
			if shelves_week_active > week_active then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.ErrorWeekShelves[2])
				return
			end

			if week_shelves_count >= week_shelves_num then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.ErrorWeekShelves[1])
				return
			end

			auction_item_type = AUCTION_ITEM_TYPE.BIND_GOLD
		end

		MarketWGCtrl.Instance:SendAddGoods(self.data.knapsack_type, self.data.index, self.amount, self.total_price, self.password, auction_item_type)
		if self.need_open_market then
			local week_active, week_shelves_count, jump_flag = MarketWGData.Instance:GetAuctionInfo()
			if jump_flag == 0 then
				ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_sell)
			end
		end
		self:Close()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.SellSuccTips1)
	end
end

function MarketSellTips:OnClickJumpFlag()
	local week_active, week_shelves_count, jump_flag = MarketWGData.Instance:GetAuctionInfo()
	local change_flag = jump_flag == 0 and 1 or 0

	MarketWGCtrl.Instance:SendReqJumpFlag(change_flag)
end
----------------------------------------------------
-- 其他同种商品item
----------------------------------------------------
MarketSellTipsSameGoods = MarketSellTipsSameGoods or BaseClass(BaseGridRender)
function MarketSellTipsSameGoods:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function MarketSellTipsSameGoods:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
	end
	self.item_cell = nil
end

function MarketSellTipsSameGoods:OnFlush()
	if self.data == nil then return end
	-- 物品名称
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_data.item_id, nil, false)
	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	-- 物品格子
	local item_data = {}
	item_data.item_id = self.data.item_data.item_id
	item_data.is_bind = self.data.item_data.is_bind
	local item_count = (other_cfg.gold_bind_id == self.data.item_data.item_id) and (self.data.item_data.num * other_cfg.min_shelves_gond_bind_num) or self.data.item_data.num
	item_data.num = item_count
	item_data.star_level = ((self.data.item_data or {}).param or {}).star_level or 0

	if ControlBeastsCultivateWGData.Instance:IsBeastAlchemyEquipItem(self.data.item_data.item_id) then
		local param = self.data.item_data.param
		local words_list = {}

		for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
			words_list[j] = {}
			words_list[j].words_seq = param.xianpin_type_list[j + 1] or -1			-- 词条 seq = words / 100 words_value = words % 100
		end

		local equip_data = {}
		equip_data.item_id = item_data.item_id
		equip_data.words_list = words_list
		item_data.equip_info = equip_data
		item_data.is_bag_equip = true
	end
	self.item_cell:SetData(item_data)

	-- 总价
	self.node_list["item_price"].text.text = self.data.total_price

	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_data.item_id)
	local auction_price_type = cfg.auction_price_type
	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		self.node_list.img_price.image:LoadSprite(bundel, asset, function ()
			self.node_list.img_price.image:SetNativeSize()
		end)
	end
end
