HolyDarkSkillPopEquipBag = HolyDarkSkillPopEquipBag or BaseClass(SafeBaseView)

function HolyDarkSkillPopEquipBag:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(902, 522)})
	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_holy_dark_skill_equip_bag")
end

function HolyDarkSkillPopEquipBag:__delete()
end

function HolyDarkSkillPopEquipBag:ReleaseCallBack()
	if self.equip_bag_grid then
		self.equip_bag_grid:DeleteMe()
		self.equip_bag_grid = nil
	end

	if self.color_list_view then
		self.color_list_view:DeleteMe()
		self.color_list_view = nil
	end

	self.cur_select_color_index = nil
	self.is_show_color_part = false

	-- if self.evolve_equip_sepcial_alert then
	-- 	self.evolve_equip_sepcial_alert:DeleteMe()
	-- 	self.evolve_equip_sepcial_alert = nil
	-- end
	 self.relic_seq = nil
end


function HolyDarkSkillPopEquipBag:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.HolyDarkWeapon.SkillPopEquipTitle

	self.color_list_view = AsyncListView.New(HolyPinZhiListRender, self.node_list["color_list"])
	self.color_list_view:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))
	if self.cur_select_color_index == nil then
		self.cur_select_color_index = 5 -- 默认紫色
	end

	self.color_list_view:SetDefaultSelectIndex(self.cur_select_color_index)
	self.color_list_view:SetDataList(Language.HolyDarkWeapon.NameList2)
	self.node_list["cur_color_text"].text.text = Language.HolyDarkWeapon.NameList2[self.cur_select_color_index]
	XUI.AddClickEventListener(self.node_list["btn_select_color"], BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list["close_color_list_part"], BindTool.Bind(self.OnClickSelectColor, self))

	self.equip_bag_grid = ShenShouGrid.New()
	self.equip_bag_grid:CreateCells({
	    col = 9,
	    cell_count = 45,
	    list_view = self.node_list["ph_equip_bag_grid"],
	    itemRender = HolyDarkSkillPopEquipCell,
	    change_cells_num = 2,
	})
	self.equip_bag_grid:SetStartZeroIndex(false)
	self.equip_bag_grid:SetIsMultiSelect(true)
	self.equip_bag_grid:SetSelectCallBack(BindTool.Bind1(self.SelectCellCallBack, self))

	--批量贡献
	self.node_list.btn_insert.button:AddClickListener(BindTool.Bind(self.OnClcikInsert,self))
end

-- 选择品质回调
function HolyDarkSkillPopEquipBag:SelectColorCallBack(cell)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	local index = cell:GetIndex()
	if index == self.cur_select_color_index then
		return
	end

	self.cur_select_color_index = index
	self.node_list["cur_color_text"].text.text = Language.HolyDarkWeapon.NameList2[self.cur_select_color_index]
	self:OnClickSelectColor()
	self:FlushBagList()
end

-- 展开品质筛选列表
function HolyDarkSkillPopEquipBag:OnClickSelectColor()
	self.is_show_color_part = not self.is_show_color_part
	self.node_list["color_select_panel"]:SetActive(self.is_show_color_part)
	self.node_list["arrow_down"]:SetActive(self.is_show_color_part)
	self.node_list["arrow_up"]:SetActive(not self.is_show_color_part)
end

function HolyDarkSkillPopEquipBag:SetDataAndOpen(relic_seq)
    self.relic_seq = relic_seq
    self:Open()
end

function HolyDarkSkillPopEquipBag:OnFlush()
	if not self.relic_seq then
        return
    end

    self:FlushBagList()
end

function HolyDarkSkillPopEquipBag:FlushBagList()
	local real_color = (#Language.HolyDarkWeapon.NameList2 - self.cur_select_color_index) + 1
	local data = HolyDarkWeaponWGData.Instance:GetUpSkillEquipBag(self.relic_seq)
    self.equip_bag_grid:SetDataList(data)
    self.equip_bag_grid:SetHolyDarkSkillPopSelect(real_color)
    self:FlushConsume()
end

function HolyDarkSkillPopEquipBag:SelectCellCallBack(cell, can_not_select)
	if can_not_select then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyDarkWeapon.EquipAddMax)--仓库容量不足，不可以捐献
		return
	end

	self:FlushConsume()
end

function HolyDarkSkillPopEquipBag:FlushConsume()
	if not self.relic_seq then
        return
    end

    local select_data = self.equip_bag_grid:GetAllSelectCell()
    local add_per = 0
    if not IsEmptyTable(select_data) then
    	for k, v in pairs(select_data) do
    		local equip_cfg = HolyDarkWeaponWGData.Instance:GetRelicEquipItemData(v.item_id)
    		if equip_cfg then
    			add_per = add_per + (equip_cfg.exp) * v.num
    		end
    	end
    end

    if add_per > 0 then
		self.equip_bag_grid:SetNoSelectState(#select_data >= HolyDarkWeaponWGData.DecompsEquip)
		self.node_list["btn_insert_txt"].text.text = Language.HolyDarkWeapon.EquipAddEquipBtn2
		XUI.SetButtonEnabled(self.node_list["btn_insert"], true)
	else
		self.equip_bag_grid:SetNoSelectState(false)
		self.node_list["btn_insert_txt"].text.text = Language.HolyDarkWeapon.EquipAddEquipBtn1
		XUI.SetButtonEnabled(self.node_list["btn_insert"], false)
	end
	
	local insert_preface_tip_text  = string.format(Language.HolyDarkWeapon.EquipAddExp, add_per)

    local skill_level, skill_exp = HolyDarkWeaponWGData.Instance:GetRelicSkillLevel(self.relic_seq)
    local cur_skill_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSkillLevelCfg(self.relic_seq, skill_level)
    local will_add_exp = skill_exp + add_per
	local cur_level_max_exp = cur_skill_level_cfg.need_exp
    local total_add_lev = 0
    if will_add_exp > cur_level_max_exp then
        while(true) do
            if will_add_exp - cur_level_max_exp > 0 then
                will_add_exp = will_add_exp - cur_level_max_exp
                total_add_lev = total_add_lev + 1
                local cfg = HolyDarkWeaponWGData.Instance:GetRelicSkillLevelCfg(self.relic_seq, skill_level + total_add_lev)
                if not cfg then
                    break
                end

                cur_level_max_exp = cfg.need_exp
            else
                break
            end
        end
    end

    if total_add_lev > 0 then
        insert_preface_tip_text = string.format(Language.HolyDarkWeapon.SkillLevelUpBag, insert_preface_tip_text, skill_level + total_add_lev)
    end

    self.node_list["insert_preface_tip"].text.text = insert_preface_tip_text
end

function HolyDarkSkillPopEquipBag:OnClcikInsert()
	if not self.relic_seq then
        return
    end

	local select_data = self.equip_bag_grid:GetAllSelectCell()
	local add_per = 0
	local count = 0

	if not IsEmptyTable(select_data) then
    	for k, v in pairs(select_data) do
    		local equip_cfg = HolyDarkWeaponWGData.Instance:GetRelicEquipItemData(v.item_id)
    		if equip_cfg then
	    		add_per = add_per + (equip_cfg.exp) * v.num
	    		count = count + v.num
	    	end
    	end
    end

	local ok_fun = function ()
		HolyDarkWeaponWGData.Instance:SetSkillSelectBagParam(select_data, add_per, count)
		local weapon_type = HolyDarkWeaponWGData.Instance:GetCurWeaponType()
		local module_name
		if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
			module_name = GuideModuleName.HolyWeapon
		else
			module_name = GuideModuleName.DarkWeapon
		end

		ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_equip_skill)

		self:Close()
	end

	ok_fun()
end


-------------
HolyDarkSkillPopEquipCell = HolyDarkSkillPopEquipCell or BaseClass(ItemCell)
function HolyDarkSkillPopEquipCell:__init()
	self.need_default_eff = true
	self:SetItemTipFrom(ItemTip.FROM_BAG_ON_GUILD_STORGE)
    self:SetIsShowTips(false)
    self:UseNewSelectEffect(true)
end

function HolyDarkSkillPopEquipCell:SetSelect(is_select, item_call_back)
	ItemCell.SetSelectEffect(self, is_select)	
end

----------------HolyPinZhiListRender-------------------
HolyPinZhiListRender = HolyPinZhiListRender or BaseClass(BaseRender)
function HolyPinZhiListRender:OnFlush()
	self.node_list.holy_dark_pinzhi_txt.text.text = self.data
	self.node_list.select_bg:SetActive(self.is_select)
end

function HolyPinZhiListRender:OnSelectChange(is_select)
	if self.node_list.select_bg then
		self.node_list.select_bg:SetActive(is_select)
	end
end
