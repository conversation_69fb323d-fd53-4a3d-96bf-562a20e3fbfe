SystemForceWGData = SystemForceWGData or BaseClass()

function SystemForceWGData:__init()
    if nil ~= SystemForceWGData.Instance then
        ErrorLog("[SystemForceWGData]:Attempt to create singleton twice!")
    end
    SystemForceWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("system_forceshow_cfg_auto")
    self.forceshow_seq_cfg = ListToMap(cfg.forceshow, "seq")
    self.chapter_cfg = ListToMap(cfg.chapter, "seq", "chapter")
    self.chapter_day_cfg = ListToMapList(cfg.chapter, "seq", "day")
    self.task_cfg = ListToMapList(cfg.task, "seq","chapter")
    self.icon_show_cfg = ListToMap(cfg.icon_show, "server_day")  -- 主界面图标及特效
    self.other_cfg = cfg.other[1]

    -- UI风格缓存
    self.ui_type_cache = {}
    for k, v in pairs(self.forceshow_seq_cfg) do
        self.ui_type_cache[k] = v.fun_ui_type
    end

    self.system_force_info_list = {}  -- 章节信息
    self.system_force_task_list = {}  -- 任务信息
    RemindManager.Instance:Register(RemindName.SystemForceLongShen, BindTool.Bind(self.GetSystemForceLongRemind, self))
end

function SystemForceWGData:__delete()
    SystemForceWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.SystemForceLongShen)

    self.system_force_info_list = nil
    self.system_force_task_list = nil
end

--设置预告信息
function SystemForceWGData:SetSystemForceInfo(protocol)
    -- 直接赋值 self.system_force_info_list[seq] = protocol 会使得后面的数据覆盖前面的，脏了前面的表
    local target_seq = protocol.seq

    local data = {
        seq = target_seq,
        rmb_buy_flag = protocol.rmb_buy_flag,
        finish_reward_flag = protocol.finish_reward_flag,
        chapter_reward_flag = protocol.chapter_reward_flag,
    }

    self.system_force_info_list[target_seq] = data
end

--设置任务信息
function SystemForceWGData:SetSystemForceTaskInfo(protocol)
    local seq = protocol.seq
    self.system_force_task_list[seq] = protocol.task_list
end

-- 单个任务改变
function SystemForceWGData:SetSingleTaskInfo(protocol)
    local seq = protocol.seq
    local task_id = protocol.task_id
    if self.system_force_task_list[seq] and self.system_force_task_list[seq][task_id] then
        self.system_force_task_list[seq][task_id] = protocol.single_task
    end
end

function SystemForceWGData:GetSystemForceInfoBySeq(seq)
   return self.system_force_info_list[seq]
end

function SystemForceWGData:GetSystemForceTaskBySeq(seq, task_id)
   return (self.system_force_task_list[seq] or {})[task_id]
end

function SystemForceWGData:GetForceshowAllCfg()
    return self.forceshow_seq_cfg
end

function SystemForceWGData:GetForceshowIsHaveFunNameCfg(fun_name)
    for k, v in pairs(self.forceshow_seq_cfg) do
        if v.fun_name == fun_name then
            return true
        end
    end

    return false
end

function SystemForceWGData:GetForceshowFunIsOpen(seq)
    -- local seq = -1
    -- for k, v in pairs(self.forceshow_seq_cfg) do
    --     if v.fun_name == fun_name then
    --         seq = k
    --         break
    --     end
    -- end

    local info = self:GetSystemForceInfoBySeq(seq)
    if IsEmptyTable(info) then
		return false
	end

    local cur_chapter = self:GetCurrentChapter(seq)
    local chapter_flag = info.chapter_reward_flag[cur_chapter] == 1
    return chapter_flag
end

--获取索引配置by seq
function SystemForceWGData:GetForceshowCfgBySeq(seq)
    return self.forceshow_seq_cfg[seq]
end

--获取索引章节配置by Seq
function SystemForceWGData:GetChapteCfgBySeq(seq)
    return self.chapter_cfg[seq]
end

--获取当前章节配置by Seq and chapter
function SystemForceWGData:GetCurSeqChapterCfg(seq, cur_chapter)
    return (self.chapter_cfg[seq] or {})[cur_chapter]
end

function SystemForceWGData:GetDayChapteCfgBySeq(seq)
    return self.chapter_day_cfg[seq]
end

--获取索引最大章节
function SystemForceWGData:GetMaxChapteBySeq(seq)
    local chapter_cfg = self:GetChapteCfgBySeq(seq) or {}
    return #chapter_cfg
end

--获取对应任务列表
function SystemForceWGData:GetTaskCfg(seq, cur_chapter)
    return (self.task_cfg[seq] or {})[cur_chapter]
end

--判断当前索引应该拿的章节
function SystemForceWGData:GetCurrentChapter(seq)
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local chapter_cfg = self:GetChapteCfgBySeq(seq)
    local info = self:GetSystemForceInfoBySeq(seq)

    if IsEmptyTable(info) or IsEmptyTable(chapter_cfg) then
        return 0
    end

    local cur_chapter = 0
    for i = 0, #chapter_cfg do
        local data = chapter_cfg[i]
        if info.chapter_reward_flag[data.chapter] == 0 and data.day <= server_day then
            cur_chapter = i
            break
        end
    end

    return cur_chapter
end

--获取排序任务列表数据
function SystemForceWGData:GetSortTaskData(seq, cur_chapter)
   local task_info = self:GetTaskCfg(seq, cur_chapter)
   local task_list = {}
   if not IsEmptyTable(task_info) then
        for k, v in pairs(task_info) do
            local list = __TableCopy(v)
            local info = self:GetSystemForceTaskBySeq(v.seq, v.task_id)
            list.progress_num = info and info.progress_num or 0
            list.progress_num = info and info.progress_num or 0
            list.status = info and info.status or 0
            if info and info.status == SYSTEM_FORCESHOW_TASK_STATUS.NONE then
                list.sort_index = 100
            elseif info and info.status == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH then
                list.sort_index = 10
            elseif info and info.status == SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH then
                list.sort_index = 1000
            end

            table.insert(task_list, list)
        end
   end

   --table.sort(task_list, SortTools.KeyLowerSorters("sort_index", "task_id"))
   return task_list
end

--章节按钮状态
function SystemForceWGData:GetCurrentChapterState(seq, cur_chapter)
    local info = self:GetSystemForceInfoBySeq(seq)
    if IsEmptyTable(info) then
        return false
    end

    local chapter_flag = info.chapter_reward_flag[cur_chapter] == 1

    -- 策划要求 隔天才能领取章节奖励啊
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local chapter_cfg = self:GetForceshowCfgBySeq(seq)

    -- if cur_day ~= chapter_cfg.close_day then
    --     return false
    -- end

    if chapter_flag then
        return false
    else
        local task_info = self:GetTaskCfg(seq, cur_chapter)
        if not IsEmptyTable(task_info) then
            local task_state = true
            for k, v in pairs(task_info) do
                local task_data = self:GetSystemForceTaskBySeq(v.seq, v.task_id)
                if task_data and task_data.status ~= SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH then
                    task_state = false
                    break
                end
            end

            return task_state
        else
            return false
        end
    end
end

function SystemForceWGData:GetCheckFunOpen(seq)
    if seq and seq >= 0 then
        local is_open = SystemForceWGData.Instance:GetForceshowFunIsOpen(seq)
        return is_open
    else
        local other_cfg = self:GetOtherCfg()
        if not IsEmptyTable(other_cfg) then
            local role_level = RoleWGData.Instance:GetRoleLevel()
            if role_level < other_cfg.open_level then
                return false
            end
        end

        local data_list = self:GetForceshowAllCfg()

        for k, v in pairs(data_list) do
            if self:GetCheckOpenBySeq(v.seq) then
                return true
            end
        end

        return false
    end
end

--功能开启判断  需要改成判断所有活动
function SystemForceWGData:GetCheckOpenBySeq(seq)
    local cfg = self:GetForceshowCfgBySeq(seq)
    local info = self:GetSystemForceInfoBySeq(seq)
    if not cfg or IsEmptyTable(info) then
        return false
    end

    local open_day = cfg.open_day
    local close_day = cfg.close_day
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local max_chapter = self:GetMaxChapteBySeq(seq) --最大章节
    local chapter_not_fatch_flag = info.chapter_reward_flag[max_chapter] ~= 1 --最大章节奖励状态

    if cur_day < open_day or cur_day > close_day or role_level < cfg.open_level then
        return false
    else
        return chapter_not_fatch_flag
    end
end

function SystemForceWGData:GetSystemForceLongRemind()
    local other_cfg = self:GetOtherCfg()
    if not IsEmptyTable(other_cfg) then
        local role_level = RoleWGData.Instance:GetRoleLevel()
        if role_level < other_cfg.open_level then
            return 0
        end
    end

    --local data_list = self:GetForceshowAllCfg()
    local data_list = self:GetShowActDataList()

    for k, v in pairs(data_list) do
        if self:SystemForceLongRemind(v.seq) == 1 then
            return 1
        end
    end

    return 0
end

--红点
function SystemForceWGData:SystemForceLongRemind(seq)
    local cfg = self:GetForceshowCfgBySeq(seq)
    local info = SystemForceWGData.Instance:GetSystemForceInfoBySeq(seq)
    if not cfg or IsEmptyTable(info) then
        return 0
    end

    -- local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    -- local open_day = cfg.open_day
    -- local close_day = cfg.close_day

    -- if cur_day < open_day or cur_day > close_day then
    --     return 0
    -- end 

    local cur_chapter = self:GetCurrentChapter(seq)
    local chapter_state = self:GetCurrentChapterState(seq, cur_chapter)
    --章节是否可以领取
    if chapter_state then
        return 1
    end

    -- --2023/6/1 策雪威划要求奖励只能当天领取 后端不愿意加(过时)状态类型 特殊处理
    -- if cur_day ~= open_day then
    --     return 0
    -- end

    --当前章节的任务是否可以领取
    local task_info = self:GetTaskCfg(seq, cur_chapter)
    if not IsEmptyTable(task_info) then
        for k, v in pairs(task_info) do
            info = self:GetSystemForceTaskBySeq(v.seq, v.task_id)
            if info.status == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH then
                return 1
            end
        end
    end

    return 0
end

function SystemForceWGData:GetCurIconShowCfg()
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local icon_show_cfg = self.icon_show_cfg[server_day] or {}

    if not IsEmptyTable(icon_show_cfg) then
        return icon_show_cfg
    end

    return self.icon_show_cfg[#self.icon_show_cfg]
end

-------------------------------------------------------------------
-- 根据发下来的数据算一遍红点 拿seq
function SystemForceWGData:GetDefaultShowIndex()
    local data_list = self:GetShowActDataList()
    local index = 1

    for k, v in pairs(data_list) do
        if self:SystemForceLongRemind(v.seq) == 1 then
            return index
        end

        index = index + 1
    end

    return 1
end

function SystemForceWGData:GetLeftBtnState(seq)
    if seq <= 0 then
        return false, false
    end

    local active = false

    local show_data_list = self:GetShowActDataList()
    for k, v in pairs(show_data_list) do
        if v.seq < seq then
            active = true

            if self:SystemForceLongRemind(v.seq) == 1 then
                return true, true
            end
        end
    end

    return active, false
end

function SystemForceWGData:GetRightBtnState(seq)
    local active = false

    local show_data_list = self:GetShowActDataList()
    for k, v in pairs(show_data_list) do
        if v.seq > seq then
            active = true

            if self:SystemForceLongRemind(v.seq) == 1 then
                return true, true
            end
        end
    end

    return active, false
end

function SystemForceWGData:GetUITypeBySeq(seq)
    return self.ui_type_cache[seq] or 0
end

function SystemForceWGData:GetShowActDataList()
    local cfg_list = self:GetForceshowAllCfg()
    local data_list = {}
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()

    for k, v in pairs(cfg_list) do
        local open_day = v.open_day
        local close_day = v.close_day
        local is_receive = self:GetForceshowFunIsOpen(v.seq)
        if cur_day >= open_day and cur_day <= close_day and not is_receive and role_level >= v.open_level then
            table.insert(data_list, v)
        end
    end

    return data_list
end

function SystemForceWGData:GetActIsOpenBySeq(seq)
    local cfg = self:GetForceshowCfgBySeq(seq)
    if cfg then
        local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        local role_level = RoleWGData.Instance:GetRoleLevel()
        local open_day = cfg.open_day
        local close_day = cfg.close_day
        if cur_day >= open_day and cur_day <= close_day and role_level >= cfg.open_level then
            return true
        end
    end

    return false
end

function SystemForceWGData:GetOtherCfg()
    return self.other_cfg
end