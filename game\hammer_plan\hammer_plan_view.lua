HammerPlanView = HammerPlanView or BaseClass(SafeBaseView)

function HammerPlanView:__init()
	self:SetMaskBg()

	self:AddViewResource(0, "uis/view/hammer_plan_ui_prefab", "hammer_plan_view")
end

function HammerPlanView:LoadCallBack()
	if not self.hammer_plan_list then
		self.hammer_plan_list = AsyncListView.New(ItemCell, self.node_list.hammer_plan_list)
		self.hammer_plan_list:SetStartZeroIndex(true)
	end

	if not self.display_model then
		self.display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.display_model:SetRenderTexUI3DModel(display_data)
		-- self.display_model:SetUI3DModel(self.node_list.display_root.transform,
		-- 	self.node_list.display_root.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.display_model)

		local res_id = HammerPlanWGData.Instance:GetHammerPlanShowModel()
		if res_id then
			local bundle, asset = ResPath.GetOtherUIModel(res_id)
			self.display_model:SetMainAsset(bundle, asset)
		end
	end

	self.node_list.hammer_desc.text.text = Language.HammerPlan.HammerDesc
	local virtual_gold_limit = HammerPlanWGData.Instance:GetHammerPlanBuffVirtualGold()
	self.node_list.info_volume_text.text.text = string.format(Language.HammerPlan.RechargeVolumeLimit, virtual_gold_limit)
	-- XUI.AddClickEventListener(self.node_list.btn_hammer, BindTool.Bind(self.OnClickHammerBtn, self))
	-- XUI.AddClickEventListener(self.node_list.get_weapon_btn, BindTool.Bind(self.OnClickGetWeaponBtn, self))

	XUI.AddClickEventListener(self.node_list.btn_daliy_reward, BindTool.Bind(self.OnClickDaliyRewardBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_hammer, BindTool.Bind(self.OnClickHammerPlanBtn, self))
end

function HammerPlanView:ShowIndexCallBack()
	if self.display_model then
		local hammer_plan_active = HammerPlanWGData.Instance:GetHammerPlanState()

		if hammer_plan_active then
			self.display_model:SetTrigger("rest")
		else
			self.display_model:SetTrigger("die")
			self:StartHammerPlanCountDown()
		end

		self.node_list.xuanyun_effect:SetActive(not hammer_plan_active)
	end

	HammerPlanWGCtrl.Instance:SetCheckRecharge(false)
	HammerPlanWGCtrl.Instance:SetCheckRecharge(false)
end

function HammerPlanView:ReleaseCallBack()
	if self.hammer_plan_list then
		self.hammer_plan_list:DeleteMe()
		self.hammer_plan_list = nil
	end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	self:RemoveWeekCardCountDown()
end

function HammerPlanView:OnFlush()
	local daily_reward_flag = HammerPlanWGData.Instance:IsGetDailyReward()
	self.node_list.daliy_reward_remind:SetActive(not daily_reward_flag)
	XUI.SetGraphicGrey(self.node_list.btn_daliy_reward, daily_reward_flag)

	local cfg = HammerPlanWGData.Instance:GetHammerPlanOpenDayCfg()
	local show_reward_data_list = (cfg or {}).rmb_buy_reward_item_show or {}
	self.hammer_plan_list:SetDataList(show_reward_data_list)

	-- 特权 永久特权
	local pivilege_state, forever_pivilege = HammerPlanWGData.Instance:AreTherePrivileges()
	self.node_list.info_bg:SetActive(pivilege_state)
	self.node_list.get_weapon_btn:SetActive(not forever_pivilege)
	self.node_list.sell_out_flag:SetActive(pivilege_state and not forever_pivilege)
	self.node_list.btn_hammer:SetActive(not forever_pivilege)

	local hammer_plan_active = HammerPlanWGData.Instance:GetHammerPlanState()
	self.node_list.power_cold_time:SetActive(not hammer_plan_active)
	XUI.SetGraphicGrey(self.node_list.btn_hammer, not hammer_plan_active or forever_pivilege)

	if pivilege_state and not forever_pivilege then
		self:StartHammerPlanCountDown()
	end

	local has_time, need_time = HammerPlanWGData.Instance:GetBuffBuyTime()
	if has_time and need_time then
		local color = has_time >= need_time and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.node_list.weapon_get_progress.text.text = string.format(Language.HammerPlan.PermanentPrivilegeTime,
			need_time, color, has_time, need_time)
	end

	if forever_pivilege then
		self.node_list.info_time.text.text = Language.HammerPlan.PermanentPrivilege
	end
end

function HammerPlanView:OnClickHammerBtn()
	local hammer_plan_active = HammerPlanWGData.Instance:GetHammerPlanState()
	if hammer_plan_active then
		self.display_model:SetTrigger("hit")
		RechargeWGCtrl.Instance:SendWeekCardOperate(WEEKCARD_OPERATE_TYPE.WEEKCARD_OPERATE_TYPE_FETCH_BUFF_REWARD)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HammerPlan.HasHammerPlan)
	end
end

function HammerPlanView:StartHammerPlanCountDown()
	local time = TimeWGCtrl.Instance:GetServerTime()
	local cd_time = TimeWGCtrl.Instance:NowDayTimeEnd(time) - time
	local time_str = TimeUtil.FormatSecondDHM6(cd_time)
	local pivilege_state, forever_pivilege = HammerPlanWGData.Instance:AreTherePrivileges()

	if not forever_pivilege then
		self.node_list.info_time.text.text = string.format(Language.HammerPlan.RemainningTime, time_str)
	end

	self.node_list.power_cold_time.text.text = string.format(Language.HammerPlan.PowerRecoveryTime, time_str)

	if cd_time > 0 then
		self:RemoveWeekCardCountDown()

		CountDownManager.Instance:AddCountDown("hammer_plan",
			BindTool.Bind(self.UpdateWeekCardCountDown, self),
			BindTool.Bind(self.OnWeekCardComPlete, self),
			nil, cd_time, 1)
	end
end

function HammerPlanView:UpdateWeekCardCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time

	if valid_time > 0 then
		local time_str = TimeUtil.FormatSecondDHM6(valid_time)
		local pivilege_state, forever_pivilege = HammerPlanWGData.Instance:AreTherePrivileges()

		if self.node_list.info_time and not forever_pivilege then
			self.node_list.info_time.text.text = string.format(Language.HammerPlan.RemainningTime, time_str)
		end

		if self.node_list.power_cold_time then
			self.node_list.power_cold_time.text.text = string.format(Language.HammerPlan.PowerRecoveryTime, time_str)
		end
	end
end

function HammerPlanView:OnWeekCardComPlete()
	if self.display_model then
		self.display_model:SetTrigger("rest")
		self.node_list.xuanyun_effect:SetActive(false)
	end
end

function HammerPlanView:RemoveWeekCardCountDown()
	if CountDownManager.Instance:HasCountDown("hammer_plan") then
		CountDownManager.Instance:RemoveCountDown("hammer_plan")
	end
end

function HammerPlanView:OnClickDaliyRewardBtn()
	local daily_reward_flag = HammerPlanWGData.Instance:IsGetDailyReward()
	if daily_reward_flag then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HammerPlan.GetDailyReward)
	else
		RechargeWGCtrl.Instance:SendWeekCardOperate(WEEKCARD_OPERATE_TYPE.WEEKCARD_OPERATE_TYPE_FETCH_BUFF_REWARD)
		HammerPlanWGCtrl.Instance:SetCheckDailyReward(true)
	end
end

function HammerPlanView:OnClickHammerPlanBtn()
	local hammer_plan_active = HammerPlanWGData.Instance:GetHammerPlanState()

	if not hammer_plan_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HammerPlan.HasHammerPlan)
		return
	end

	local day_cfg = HammerPlanWGData.Instance:GetHammerPlanOpenDayCfg()
	if not IsEmptyTable(day_cfg) then
		local has_time, need_time = HammerPlanWGData.Instance:GetBuffBuyTime()
		if has_time and need_time then
			if has_time >= need_time then
				return
			end

			local price = day_cfg.price
			local rmb_type = day_cfg.rmb_type
			local rmb_seq = day_cfg.rmb_seq
			RechargeWGCtrl.Instance:Recharge(price, rmb_type, rmb_seq)
		end
	end
end

function HammerPlanView:HammerPlanAnimation()
	self.display_model:SetTrigger("hit")
	ReDelayCall(self, function()
		if self.node_list.xuanyun_effect then
			self.node_list.xuanyun_effect:SetActive(true)
		end
	end, 3.6, "hammer_plan_xuanyun_effect")
end
