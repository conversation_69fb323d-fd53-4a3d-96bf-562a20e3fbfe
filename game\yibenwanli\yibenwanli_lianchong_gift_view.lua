function YiBenWanLiView:InitLianChongGift(index,loaded_times)
	if not self.cell_list then
		self:Create<PERSON>ell()
	end
	if not self.display_model then
		self.display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.display_model:SetRenderTexUI3DModel(display_data)
		-- self.display_model:SetUI3DModel(self.node_list.display_model.transform, self.node_list.display_model.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	if not self.reward_list then
		self.reward_list = {}
		for i=1,4 do
			self.reward_list[i] = ItemCell.New(self.node_list.reward_list)
		end
	end

	-- self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickLianChongGiftCloseBtn,self))
	self.node_list.charge_btn.button:AddClickListener(BindTool.Bind(self.OnClickChargeBtn,self))
end

local model_path = {
	[1] = ResPath.GetRoleModel,
	[2] = ResPath.GetPetModel,
	[3] = ResPath.GetMountModel,
	[4] = ResPath.GetWingModel,
	[5] = ResPath.GetFaBaoModel,
	[6] = ResPath.GetMonsterModel,
	[7] = ResPath.GetMountModel,
}

function YiBenWanLiView:DeleteLianChongGift()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v.parent_view = nil
			v:DeleteMe()
		end
		self.cell_list = nil
	end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
end

function YiBenWanLiView:CreateCell()
	self.cell_list = {}
	local data_list = YiBenWanLiWGData.Instance:GetLianChongGiftCellData()

	local res_async_loader = AllocResAsyncLoader(self, "lianchong_gift_cell")
	res_async_loader:Load("uis/view/yibenwanli_ui_prefab", "lianchong_gift_cell", nil, function(new_obj)
		for i = 1,4 do

			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			if self.node_list["cell_"..i] then
				obj_transform:SetParent(self.node_list["cell_"..i].transform, false)
			end
			local item_cell = LianChongGiftCell.New(obj)
			item_cell.parent_view = self
			item_cell:SetIndex(i)
			self.cell_list[i] = item_cell
			if not data_list then return end
			self.cell_list[i]:SetData(data_list[i])
		end
	end)

end


function YiBenWanLiView:FlushLianChongGift()
	local data_list = YiBenWanLiWGData.Instance:GetLianChongGiftCellData()
	if not data_list then return end

	for i,v in ipairs(self.cell_list) do
		if data_list[i] then
			v:SetData(data_list[i])
		end
	end
	local lianchong_gift_info = YiBenWanLiWGData.Instance:GetLianChongGiftInfo()
	local cur_accrued_day = lianchong_gift_info.day or 0
	self.node_list.total_day_count.text.text = string.format(Language.YiBneWanLi.AccruedDay,cur_accrued_day)
	self.node_list.desc.text.text = ""

	-- 0 不可领 1 可领取 2 已领取
	local data = YiBenWanLiWGData.Instance:GetLianChongGiftData()
	if not data then return end
	local cur_recharge = lianchong_gift_info.chongzhi_gold or 0
	local color = cur_recharge >= data.require_gold and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.desc.text.text = string.format(Language.YiBneWanLi.DescText,cur_recharge,data.require_gold )

	for k,v in pairs(self.reward_list) do
		if data.daily_item[k - 1] then
			v:SetActive(true)
			v:SetData(data.daily_item[k - 1])
		else
			v:SetActive(false)
		end
	end
	self.node_list.slider_value.slider.value = YiBenWanLiWGData.Instance:GetLianChongGiftSlideValue()
	local fetch_flag = lianchong_gift_info.day_state or 0
	self.node_list.charge_btn_text.text.text = Language.YiBneWanLi.LianChongBtnText[fetch_flag + 1]
	-- XUI.SetButtonEnabled(self.node_list.charge_btn,fetch_flag ~= 2)
	self.node_list.charge_btn:SetActive(fetch_flag ~= 2)
	self.node_list.fetch_flag:SetActive(fetch_flag == 2)
	self.node_list.redpoint:SetActive(fetch_flag == 1)

	local model_value = YiBenWanLiWGData.Instance:GetLianChongGiftModel()
	if not model_value then return end
	self.node_list.model_desc.text.text = model_value.model_des

	-- local banner_list = Split(model_value.banner_des,"|")

	-- for i=1,2 do
	-- 	self.node_list["banner_" .. i]:SetActive(false)
	-- 	if banner_list[i] then
	-- 		self.node_list["banner_" .. i]:SetActive(true)
	-- 		self.node_list["banner_" .. i].image:LoadSprite(ResPath.YiBenWanLiImagePath("banner_img_"..banner_list[i]))
	-- 		self.node_list["banner_" .. i].image:SetNativeSize()
	-- 	end
	-- end

	if self.display_model then
		local prof = RoleWGData.Instance:GetRoleProf()
		local bundle, asset
		if model_value.model_type == 1 then
			local res_id = ResPath.GetFashionModelId(prof, model_value.model_id)
			bundle, asset = model_path[model_value.model_type](res_id)
		else
			bundle, asset = model_path[model_value.model_type](model_value.model_id)
		end
		self.display_model:SetMainAsset(bundle, asset)
		local position_tab = string.split(model_value.model_pos, "|")
		local rotation_tab = string.split(model_value.model_rotate, "|")
		local vector_pos = Vector3(tonumber(position_tab[1]), tonumber(position_tab[2]), tonumber(position_tab[3]))
		local vector_rot = Vector3(tonumber(rotation_tab[1]), tonumber(rotation_tab[2]), tonumber(rotation_tab[3]))
		local vector_scale = Vector3(model_value.model_scale, model_value.model_scale, model_value.model_scale)
		self.display_model:ResetRotation()
		self.display_model:CustomDisplayPositionAndRotation(vector_pos, vector_rot, vector_scale)
	end

end

function YiBenWanLiView:OnClickLianChongGiftCloseBtn()
	self:Close()
end

function YiBenWanLiView:OnClickChargeBtn()
	local lianchong_gift_info = YiBenWanLiWGData.Instance:GetLianChongGiftInfo()
	local fetch_flag = lianchong_gift_info.day_state or 0
	if fetch_flag == 1 then
		YiBenWanLiWGCtrl.Instance:SendYiBenWanLiWGCtrl(YIBENWANLI_OPERA_TYPE.YIBENWANLI_OPERA_RECHARGE_FETCH)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, "recharge_cz")
	end
end

---------------------------------------------------------------LianChongGiftCell----------------------------------------------------------------------------------

LianChongGiftCell = LianChongGiftCell or BaseClass(BaseRender)

function LianChongGiftCell:__init()
	self.node_list.cell_icon.button:AddClickListener(BindTool.Bind(self.OnClickCellIcon,self))
end

function LianChongGiftCell:__delete()
	-- body
end

function LianChongGiftCell:OnFlush()
	if not self.data then return end
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.cfg.accrued_item[0].item_id)
	self.node_list.quality_bg.image:LoadSprite(ResPath.YiBenWanLiIconPath("lc_bg_"..cfg.color))
	self.node_list.cell_icon.image:LoadSprite(ResPath.GetItem(cfg.icon_id))
	self.node_list.remind:SetActive(self.data.state == 1)
	self.node_list.hl_bg:SetActive(self.data.state == 1)
	self.node_list.fetch_flag:SetActive(self.data.state == 2)
	self.node_list.day_count.text.text = string.format(Language.YiBneWanLi.Day,self.data.cfg.day)
end

function LianChongGiftCell:OnClickCellIcon()
	if not self.data then return end

	if self.data.state == 1 then
		YiBenWanLiWGCtrl.Instance:SendYiBenWanLiWGCtrl(YIBENWANLI_OPERA_TYPE.YIBENWANLI_OPERA_ACCRUED_FETCH)
		return
	end

	TipWGCtrl.Instance:OpenItem(self.data.cfg.accrued_item[0])
end