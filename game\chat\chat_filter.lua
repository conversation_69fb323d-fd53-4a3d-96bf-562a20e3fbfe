------------------------------------------------------
--聊天过滤，也称敏感词过滤
--<AUTHOR>
------------------------------------------------------
ChatFilter = ChatFilter or BaseClass()
function ChatFilter:__init()
	if ChatFilter.Instance ~= nil then
		ErrorLog("[ChatFilter] attempt to create singleton twice!")
		return
	end
	ChatFilter.Instance = self
	self.filter_list = self:CompilePatterns(config_chatfilter_list)
	self.usernamefilter_list = self:CompilePatterns(config_usernamefilter_list)
	self.filter_link_list = {}
	self.filter_emoji_list = {}

	self:CheckBackground()
end

function ChatFilter:__delete()
	ChatFilter.Instance = nil
	self.filter_list = nil
	self.usernamefilter_list = nil
end

-- 预编译正则表达式
function ChatFilter:CompilePatterns(filter_list)
	if not filter_list then return {} end
	local compiled_list = {}
	for _, pattern in ipairs(filter_list) do
		table.insert(compiled_list, {pattern = pattern, len = self:Utfstrlen(pattern)})
	end

	return compiled_list
end

-- 后台控制屏蔽词
function ChatFilter:CheckBackground()
	if IS_AUDIT_VERSION then
		return
	end

	local refreshData = {
        plat_id = CHANNEL_AGENT_ID,
    }

    PhpHandle.HandlePhpGetRequest(GLOBAL_CONFIG.api_urls.client.keyword, refreshData, refreshData,
		function(cbData)
			local keywords_list = cbData.data.keywords or {}

			for k,pattern in pairs(keywords_list) do
				table.insert(self.filter_list, {pattern = pattern, len = self:Utfstrlen(pattern)})
				table.insert(self.usernamefilter_list, {pattern = pattern, len = self:Utfstrlen(pattern)})
			end
		end, nil
	)
end

--过滤敏感词，将过敏感词用*号代替
function ChatFilter:Filter(content)
	content = self:LinkHandler(content, false)
	local match_list = self:GetMatchList(content, self.filter_list)
	return self:LinkHandler(self:ReplaceMatch(match_list, content), true)
end

--是否含有非法字符
function ChatFilter:IsIllegal(content, is_username)
	local match_list
	if is_username then
		match_list = self:GetMatchList(content, self.usernamefilter_list)
	else
		match_list = self:GetMatchList(content, self.filter_list)
	end

	-- print_error("----敏感字列表---", match_list)
	return #match_list > 0
end

function ChatFilter:GetMatchList(content, filter_list)
	if not filter_list then
		return {}
	end

	local match_list = {}
	for _, item in ipairs(filter_list) do
		local s, e = string.find(content, item.pattern, 1, true)
		if s and e then
			table.insert(match_list, { match = item.pattern, len = item.len})
		end
	end

	return match_list
end

function ChatFilter:ReplaceMatch(match_list, content)
	if not match_list or #match_list == 0 then
		return content
	end

	table.sort(match_list, function(a, b) return a.len > b.len end)

	for _,v in ipairs(match_list) do
		local r_str = string.rep("*", v.len)
		content = string.gsub(content, v.match, r_str)
	end

	return content
end

function ChatFilter:Utfstrlen(str)
	local left, cnt = #str, 0
	local arr = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
	while left > 0 do
		local tmp = string.byte(str, -left)
		for i = #arr, 1, -1 do
			if tmp >= arr[i] then
				left = left - i
				break
			end
		end
		cnt = cnt + 1
	end

	return cnt
end

-- 字符中链接过滤的处理
-- 先替换掉链接，避免被过滤掉
-- 最后过滤完后再替换回来
function ChatFilter:LinkHandler(content, is_replace)
	content = content or ""
	if is_replace then
		for i,v in ipairs(self.filter_link_list) do
			content = string.gsub(content, "#LK#", v, 1)
		end

		self.filter_link_list = {}
		for i,v in ipairs(self.filter_emoji_list) do
			content = string.gsub(content, "#EM#", v, 1)
		end

		self.filter_emoji_list = {}
	else
		for str in string.gmatch(content, "{.-}") do
			table.insert(self.filter_link_list, str)
			content = string.gsub(content, str, "#LK#", 1)
		end

		-- 2022/02/08 防拉人，将数字0-9添加到了屏蔽字库，表情需要特殊处理，表情扩容了对应匹配规则需要改动
		local emoji_rule = "%[[0-2][0-5][0-9]%]"
		for word in string.gmatch(content, emoji_rule) do
			table.insert(self.filter_emoji_list, word)
		end

		content = string.gsub(content, emoji_rule, "#EM#")
	end

	return content
end

function string.utf8len(input)
	local len  = string.len(input)
	local left = len
	local cnt  = 0
	local arr  = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
	while left ~= 0 do
		local tmp = string.byte(input, -left)
		local i   = #arr
		while arr[i] do
			if tmp >= arr[i] then
				left = left - i
				break
			end
			i = i - 1
		end
		cnt = cnt + 1
	end
	return cnt
end

function ChatFilter.IsEmoji(content)
    local len = string.utf8len(content)
    for i = 1, len do
        local str = UTFSub.SubStringUTF8(content, i, i)
        local byteLen = string.len(str)
        if byteLen > 3 then
            return true
        end


        if byteLen == 3 then
            if string.find(str, "[\226][\132-\173]") or string.find(str, "[\227][\128\138]") then
                return true
            end
        end


        if byteLen == 1 then
            local ox = string.byte(str)
            if (33 <= ox and 47 >= ox) or (58 <= ox and 64 >= ox) or (91 <= ox and 96 >= ox) or (123 <= ox and 126 >= ox) or (str == "　") then
                return true
            end
        end
    end
    return false
end