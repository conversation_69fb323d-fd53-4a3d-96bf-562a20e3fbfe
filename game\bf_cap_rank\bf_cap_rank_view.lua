BFCapRankView = BFCapRankView or BaseClass(SafeBaseView)

function BFCapRankView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/kf_cap_rank_ui_prefab", "layout_bf_cap_rank")
end

function BFCapRankView:LoadCallBack()
    self:LoadModel()

    if not self.rank_reward_list then
        self.rank_reward_list = AsyncListView.New(KFCapRankRewardListItem, self.node_list["rank_reward_list"])
    end

    if not self.rank_list then
        self.rank_list = AsyncListView.New(BFCapRankListItem, self.node_list["rank_list"])
    end

    self.node_list.rank_reward_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self, 1))
    self.node_list.rank_list_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self, 2))

    for i = 1, 3 do
        self.node_list["openpanel_btn" .. i].button:AddClickListener(BindTool.Bind(self.OnClickOperaBtn, self, i))
    end

    self.node_list.rank_reward_tog.toggle.isOn = true
    self:FlushOperaBtns()
end

function BFCapRankView:LoadModel()
    if self.model_display == nil then
		self.model_display = OperationActRender.New(self.node_list["model_root"])
	end

    local cfg_type = BFCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
    if cfg_type and cfg_type.model_show > 0 then
        local data = {}
        data.item_id = cfg_type.model_show
        data.render_type = 0
        data.position = Vector3(0, 0, 0)
	    data.rotation = Vector3(0, 0, 0)
        data.scale = Vector3(1, 1, 1)
        self.model_display:SetData(data)
    end
end

function BFCapRankView:ReleaseCallBack()
    if self.rank_reward_list then
        self.rank_reward_list:DeleteMe()
        self.rank_reward_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

    if CountDownManager.Instance:HasCountDown("bf_cap_rank_down") then
		CountDownManager.Instance:RemoveCountDown("bf_cap_rank_down")
	end
end

function BFCapRankView:OpenCallBack()
    BFCapRankWGCtrl.Instance:SendCapRankReq(CAPABILITY_RANK_OPERATE_TYPE.RANK_INFO)
end

function BFCapRankView:OnFlush(param_t)
    self:LoginTimeCountDown()
    local total_zhanli = BFCapRankWGData.Instance:GetMyRankValue()
	self.node_list["my_cap"].text.text = total_zhanli or 0
    local rank_data = BFCapRankWGData.Instance:GetMyRankInfo()
	if rank_data ~= nil then
		self.node_list["my_rank"].text.text = string.format(Language.SystemCapRank.RechargeRankTitle2, rank_data.index)
	else
		self.node_list["my_rank"].text.text = Language.SystemCapRank.RechargeNoRank
	end

    self:FlushRankRewardPanel()
    local cfg_type = BFCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg_type.model_show)
    if item_cfg then
        self.node_list.title_txt.text.text = item_cfg.name
    end
end

function BFCapRankView:OnTogValueChange(index, is_on)
    if is_on then
        if index == 1 then
            self:FlushRankRewardPanel()
        else
            self:FlushRankListPanel()
        end
    end
end

function BFCapRankView:FlushRankRewardPanel()
    local cfg_type = BFCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
    if cfg_type then
        local reward_list = BFCapRankWGData.Instance:GetActRewardCfg(cfg_type.type)
        if reward_list then
            self.rank_reward_list:SetDataList(reward_list)
        end
    end
end

function BFCapRankView:FlushRankListPanel()
    local cfg_type = BFCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
    if cfg_type then
        local data_list = BFCapRankWGData.Instance:GetRankInfo()
        if data_list then
            self.rank_list:SetDataList(data_list)
        end
    end
end

function BFCapRankView:FlushOperaBtns()
    local other_cfg = BFCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
    for i = 1, 3 do
        local bottom_name = (other_cfg or {})["bottom_name" .. i] or ""
        local open_panel = (other_cfg or {})["open_panel" .. i] or ""
        self.node_list["openpanel_btn" .. i]:CustomSetActive(bottom_name ~= "" and open_panel ~= "")
        self.node_list["openpanel_btn_des" .. i].text.text = bottom_name
    end
end

function BFCapRankView:OnClickOperaBtn(index)
    local other_cfg = BFCapRankWGData.Instance:GetActTypeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
    local open_panel = (other_cfg or {})["open_panel" .. index] or ""
    local act_type = (other_cfg or {})["act_type" .. index] or ""

    if open_panel ~= "" and act_type ~= "" then
        if ActivityWGData.Instance:GetActivityIsOpen(act_type) then
            FunOpen.Instance:OpenViewNameByCfg(open_panel)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.SystemCapRank.NoOpenActTips)
        end
    elseif open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(open_panel)
    end
end

------------------------------------活动时间倒计时
function BFCapRankView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_BF_CAP_RANK)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM6(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("bf_cap_rank_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function BFCapRankView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM6(valid_time)
	end
end

function BFCapRankView:OnComplete()
    self.node_list["act_time"].text.text = ""
	self:Close()
end

---------------------------------------KFCapRankRewardListItem----------------------------------
KFCapRankRewardListItem = KFCapRankRewardListItem or BaseClass(BaseRender)

function KFCapRankRewardListItem:LoadCallBack()
    if not self.rank_list then
        self.rank_list = AsyncListView.New(ItemCell, self.node_list.rank_list)
        self.rank_list:SetStartZeroIndex(true)
    end
end

function KFCapRankRewardListItem:__delete()
    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function KFCapRankRewardListItem:OnFlush()
    if not self.data then
		return
	end

	local desc = ""
	if self.data.min_rank == self.data.max_rank then
		desc = string.format(Language.KFImproveCapRank.RechargeRankTitle2, self.data.min_rank)
	else
		desc = string.format(Language.KFImproveCapRank.RechargeRankTitle3, self.data.min_rank, self.data.max_rank)
	end

    local other_cfg = BFCapRankWGData.Instance:GetActOtherCfg()
	local suit_data_list = WardrobeWGData.Instance:GetSuitSingtonData(other_cfg.suit_ids)
    local real_act_num = suit_data_list and suit_data_list.real_act_num or 0
    local suit_num_enough = real_act_num >= self.data.minimum_active_count
    local num_color = suit_num_enough and COLOR3B.D_GREEN or COLOR3B.PINK
    local str = string.format("(%d/%d)", real_act_num, self.data.minimum_active_count)
    local suit_progress = ToColorStr(str, num_color)

    self.node_list["rank_desc1"].text.text =string.format(Language.KFImproveCapRank.SystemCapLongShenRankTitle1, self.data.minimum_active_count, suit_progress)
    self.node_list["rank_desc"].text.text = string.format(Language.KFImproveCapRank.SystemCapLongShenRankTitle, desc, self.data.reach_value)
    self.rank_list:SetDataList(self.data.reward_item)
end

---------------------------------BFCapRankListItem-----------------------------
BFCapRankListItem = BFCapRankListItem or BaseClass(BaseRender)

function BFCapRankListItem:OnFlush()
    if not self.data then
		return
	end

    local is_top_3
    local player_rank
    if self.data.no_true_rank then  --未上榜
        self.node_list.cap.text.text = Language.KFImproveCapRank.BaoMi

        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
	else
		self.node_list.cap.text.text = self.data.rank_data.rank_value
        is_top_3 = self.data.index <= 3
        player_rank = self.data.index
	end

    local user_name = self.data.rank_data.name
	if self.data.no_true_rank then
		user_name = Language.KFImproveCapRank.XuWeiYiDai
	end

    self.node_list.name.text.text = user_name
    self.node_list.rank_icon:CustomSetActive(is_top_3)
 
    if not is_top_3 then
		self.node_list.rank_desc.text.text = player_rank
	else
		self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. player_rank))
		self.node_list.rank_desc.text.text = ""
	end

    local is_top_one = self.data.index == 1
    self.node_list.bg:CustomSetActive(is_top_one)

    if not is_top_one and is_top_3 then
        local bundle, asset = ResPath.GetCommonImages("a2_xt_pm" .. self.data.index)
        self.node_list.bg_oth.image:LoadSprite(bundle, asset)
    end

    self.node_list.bg_oth:CustomSetActive(not is_top_one and is_top_3)
    self.node_list.bg_zi:CustomSetActive(not is_top_3 and self.data.index % 2 ~= 0)
end

