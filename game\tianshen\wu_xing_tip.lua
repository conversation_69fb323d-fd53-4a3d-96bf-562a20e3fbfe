WuXingTip = WuXingTip or BaseClass(SafeBaseView)

function WuXingTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_wuxing_tip")
end

function WuXingTip:__delete()
end

function WuXingTip:ReleaseCallBack()

end

function WuXingTip:LoadCallBack()
	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind1(self.Close, self))
end

function WuXingTip:ShowIndexCallBack()

end

function WuXingTip:OnFlush()
	for i =1,3 do
		self.node_list["rule"..i].text.text = Language.TianShen.WuXingRule[i]
	end
end
