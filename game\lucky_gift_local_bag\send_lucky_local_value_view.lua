SendLuckyLocalValueView = SendLuckyLocalValueView or BaseClass(SafeBaseView)

function SendLuckyLocalValueView:__init()
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "send_lucky_value_view")
	self:SetMaskBg(true, true)
end

function SendLuckyLocalValueView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.gocharge_btn, BindTool.Bind(self.ClickGoChargeBtn,self))
end

function SendLuckyLocalValueView:OnFlush()
	local current_num, max_num = LuckyGiftBagLocalWgData.Instance:GetRechargeAddLuckyValye()
	self.node_list.lucky_value_slider.text.text = string.format(Language.LuckyGiftBag.RechargeAddLuckyValue, current_num, max_num)
	self.node_list.racharge_promot.text.text = string.format(Language.LuckyGiftBag.RechargeAddPromot)
end

function SendLuckyLocalValueView:ClickGoChargeBtn()
	FunOpen.Instance:DoOpenViewByName(GuideModuleName.Recharge, TabIndex.recharge_cz)
end