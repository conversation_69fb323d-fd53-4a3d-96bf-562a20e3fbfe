SwornView = SwornView or BaseClass(SafeBaseView)

function SwornView:__init()
	self:SetMaskBg()
	self.view_style = ViewStyle.Half

	self.default_index = TabIndex.sworn_suit
	self.view_name = GuideModuleName.SwornView
	local suit_tab_list = { TabIndex.sworn_suit, TabIndex.sworn_upstar, TabIndex.sworn_uplevel }
	local bundle_name = "uis/view/sworn_ui_prefab"
	self:AddViewResource(0, bundle_name, "sworn_bg_view")
	self:AddViewResource(TabIndex.sworn_start, bundle_name, "sworn_view")
	self:AddViewResource(TabIndex.sworn_apply, bundle_name, "sworn_apply_view")
	self:AddViewResource(TabIndex.sworn_imprint, bundle_name, "sworn_imprint_view")
	-- self:AddViewResource(suit_tab_list, bundle_name, "sworn_manual_view")
	self:AddViewResource(TabIndex.sworn_suit, bundle_name, "sworn_manual_suit")
	self:AddViewResource(TabIndex.sworn_upstar, bundle_name, "sworn_manual_upstar")
	self:AddViewResource(TabIndex.sworn_uplevel, bundle_name, "sworn_manual_uplevel")
	self:AddViewResource(TabIndex.sworn_task, bundle_name, "sworn_task_view")
	self:AddViewResource(TabIndex.sworn_taoyuan, bundle_name, "sworn_taoyuan_view")
	self:AddViewResource(suit_tab_list, bundle_name, "sworn_suit_tab")
	self:AddViewResource(0, bundle_name, "VerticalTabbar")
	self:AddViewResource(0, bundle_name, "HorizontalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")

	self.tab_sub = { nil, nil, nil, nil, nil, Language.Sworn.TabSub }
	self.remind_tab = {
		{ RemindName.Sworn_Start },
		{ RemindName.Sworn_Apply },
		{ RemindName.Sworn_Build_Task },
		{ RemindName.Sworn_Build_Taoyuan },
		{ RemindName.Sworn_Imprint },
		{ RemindName.Sworn_Suit,         RemindName.Sworn_Upstar, RemindName.Sworn_Uplevel },
	}
end

function SwornView:OpenCallBack()
	SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_TEAM_INFO)
end

function SwornView:CloseCallBack()
	self.old_show_index = nil
	self.select_suit_index = nil
	self.need_check_pu_again = nil
	self.my_jieyi_state = nil

	self:SwornImporintOneKeyStop()
	self:SwornUpStarOneKeyStop()
end

function SwornView:ReleaseCallBack()
	self.select_suit_index = nil
	self.last_show_index = nil
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:SwornStartViewReleaseCallBack()
	self:SwornApplyViewReleaseCallBack()
	self:SwornImporintViewReleaseCallBack()

	self:SwornSuitViewReleaseCallBack()
	self:SwornUplevelViewReleaseCallBack()
	self:SwornUpStarViewReleaseCallBack()

	self:SwornTaskViewReleaseCallBack()
	self:SwornTaoYuanViewReleaseCallBack()
end

function SwornView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Sworn.SwornTitle
	self.select_suit_index = nil
	self.last_show_index = -1
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetIndieVerBarRemind(math.floor(TabIndex.sworn_suit / 10), RemindName.Sworn_Equip)
		self.tabbar:Init(Language.Sworn.TabGrop, self.tab_sub, "uis/view/sworn_ui_prefab", "uis/view/sworn_ui_prefab",
			self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.SwornView, self.tabbar)
	end
end

function SwornView:LoadIndexCallBack(index)
	if index == TabIndex.sworn_start then
		self:InitSwornStartView()
	elseif index == TabIndex.sworn_apply then
		self:InitSwornApplyView()
	elseif index == TabIndex.sworn_imprint then
		self:InitSwornImporintView()
	elseif index == TabIndex.sworn_suit then
		self:InitSwornSuitTab()
		self:InitSwornSuitView()
	elseif index == TabIndex.sworn_upstar then
		self:InitSwornSuitTab()
		self:InitSwornUpstarView()
	elseif index == TabIndex.sworn_uplevel then
		self:InitSwornSuitTab()
		self:InitSwornUplevelView()
	elseif index == TabIndex.sworn_task then
		self:InitSwornTaskView()
	elseif index == TabIndex.sworn_taoyuan then
		self:InitSwornTaoYuanView()
	end
end

function SwornView:ShowIndexCallBack(index)
	self:ShowIndexChange(self.last_show_index)
	self.last_show_index = index

	if index == TabIndex.sworn_start then
		self:ShowSwornStartView()
	elseif index == TabIndex.sworn_apply then
		self:ShowSwornApplyView()
	elseif index == TabIndex.sworn_imprint then
		self:ShowSwornImporintView()
	elseif index == TabIndex.sworn_suit then
		self:ShowSwornSuitIndexCallBack()
	elseif index == TabIndex.sworn_task then
		self:ShowSwornTaskView()
	elseif index == TabIndex.sworn_taoyuan then
		self:ShowSwornTaoYuanView()
	end

	local ver_index = math.floor(index / 10)
	local is_new_jump = ver_index ~= self.old_ver_index
	self.old_ver_index = ver_index

	-- 重新点击金兰谱,需要重新选择套装
	if ver_index == math.floor(TabIndex.sworn_suit / 10) and is_new_jump then
		self.need_check_pu_again = true
	end
end

function SwornView:ShowIndexChange(index)
	if index == TabIndex.sworn_imprint then
		self:SwornImporintOneKeyStop()
		return
	end

	if index == TabIndex.sworn_upstar then
		self:SwornUpStarOneKeyStop()
		return
	end

	if index == TabIndex.sworn_uplevel then
		self:SwornUpLevelOneKeyStop()
		return
	end
end

function SwornView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.sworn_start then
				self:FlushSwornStartView()
			elseif index == TabIndex.sworn_apply then
				self:FlushSwornApplyView()
			elseif index == TabIndex.sworn_imprint then
				self:FlushSwornImporintView()
			elseif index == TabIndex.sworn_suit
				or index == TabIndex.sworn_upstar
				or index == TabIndex.sworn_uplevel then
				self:TrySelectSuit(v.to_ui_param)
			elseif index == TabIndex.sworn_task then
				self:FlushSwornTaskView()
			elseif index == TabIndex.sworn_taoyuan then
				self:FlushSwornTaoYuanView()
			end
		elseif k == "task_flush_time" then
			self:FlushSwornTaskTimeCount()
		end
	end
end

function SwornView:InitSwornSuitTab()
	for i = 0, 2 do
		self.node_list["equip_toggle_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickSuitToggleCallBack, self,
			i))
	end
end

function SwornView:ToSelectTabIndex(tab_index)
	if self.show_index == tab_index and self:IsLoadedIndex(tab_index) then
		self:ShowIndexCallBack(tab_index)
		self:__MergeFlushParam(tab_index, "all", { "all" }, false)
	end

	self:ChangeToIndex(tab_index)
end

function SwornView:ChangeSuitTabStatus(is_limit)
	if not self.tabbar then
		return
	end

	if is_limit == nil then
		local suit_index = self:GetSelectSuitIndex()
		is_limit = not SwornWGData.Instance:GetSuitHadEquip(suit_index)
	end

	local callback
	if is_limit then
		callback = function()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Sworn.TabErrorDesc2)
		end
	end

	self.tabbar:SetHorOtherBtn(TabIndex.sworn_uplevel, is_limit, callback)
	self.tabbar:SetHorOtherBtn(TabIndex.sworn_upstar, is_limit, callback)
end

function SwornView:OnClickSuitToggleCallBack(suit_index)
	if self.select_suit_index == suit_index then
		return
	end

	self:SwornUpStarOneKeyStop()
	self:SwornUpLevelOneKeyStop()

	self.select_suit_index = suit_index
	-- 刷新按钮高亮
	for i = 0, 2 do
		self.node_list["toogle_hight_" .. i]:SetActive(i == suit_index)
	end

	local tabber_index = TabIndex.sworn_suit
	local is_had_equip = SwornWGData.Instance:GetSuitHadEquip(suit_index)
	if not is_had_equip then
		self:ChangeSuitTabStatus(true)
		self:ToSelectTabIndex(tabber_index)
	else
		self:ChangeSuitTabStatus(false)
		-- 跳红点
		tabber_index = SwornWGData.Instance:GetSuitJumpIndex(suit_index)
		if tabber_index ~= self.show_index then
			self:ToSelectTabIndex(tabber_index)
		else
			self:FlushJinLanPuView()
		end
	end

	RemindManager.Instance:Fire(RemindName.Sworn_Suit)
	RemindManager.Instance:Fire(RemindName.Sworn_Upstar)
	RemindManager.Instance:Fire(RemindName.Sworn_Uplevel)
end

function SwornView:GetSelectSuitIndex()
	return self.select_suit_index
end

function SwornView:TrySelectSuit(to_ui_param)
	local jump_suit_index = self.select_suit_index
	if self.need_check_pu_again then
		self.need_check_pu_again = false
		if to_ui_param then
			jump_suit_index = tonumber(to_ui_param)
		else
			-- 跳第一个有红点的套装
			local toggle_remind_list = SwornWGData.Instance:GetAllSuitEquipRemindList()
			jump_suit_index = 0
			for suit = 0, #toggle_remind_list do
				if toggle_remind_list[suit] then
					jump_suit_index = suit
					break
				end
			end
		end
	end

	if self.old_show_index ~= self.show_index then
		self.need_jump_hole = true
		self.old_show_index = self.show_index
	end

	jump_suit_index = jump_suit_index or 0
	if jump_suit_index ~= self.select_suit_index then
		self.need_jump_hole = true
		self:OnClickSuitToggleCallBack(jump_suit_index)
	else
		self:FlushJinLanPuView()
	end
end

function SwornView:FlushJinLanPuView()
	if not self:IsLoadedIndex(self.show_index) then
		return
	end

	-- 刷新套装红点
	local toggle_remind_list = SwornWGData.Instance:GetAllSuitEquipRemindList()
	for suit = 0, 2 do
		self.node_list["toggle_red_" .. suit]:SetActive(toggle_remind_list[suit])
	end

	if self.show_index == TabIndex.sworn_suit then
		self:FlushSuitView()
	elseif self.show_index == TabIndex.sworn_upstar then
		self:FlushUpStarView(self.need_jump_hole)
	elseif self.show_index == TabIndex.sworn_uplevel then
		self:FlushUpLevelView(self.need_jump_hole)
	end

	self.need_jump_hole = false
end

--激活/升级/升星成功
function SwornView:UpSucessEffect(node, Effect_type)
	if node then
		TipWGCtrl.Instance:ShowEffect({
			effect_type = Effect_type,
			is_success = true,
			pos = Vector2(0, 0),
			parent_node = node
		})
	end
end
