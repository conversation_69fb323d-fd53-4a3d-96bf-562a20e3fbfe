-----------------------------------------
-- 技能学习itemRender
-----------------------------------------
SkillLearnItemRender = SkillLearnItemRender or BaseClass(BaseRender)
function SkillLearnItemRender:__init()
	self.handler = nil
	self.img_skill_icon_normal = self.node_list["img_skill_icon_normal"]
	self.skill_icon = self.node_list["ph_skill_icon"]
	self.skill_lock = self.node_list["ph_skill_lock"]
	
	if self.skill_icon.button then
		self.skill_icon.button:AddClickListener(BindTool.Bind(self.OnClick,self))
	end

	self.lbl_skill_unopen = self.node_list["lbl_skill_unopen"]
	self.lbl_skill_name = self.node_list["lbl_skill_name"]
	self.light_skill_name = self.node_list["light_skill_name"]
end

function SkillLearnItemRender:__delete()
    self.lbl_skill_unopen = nil
	self.lbl_skill_name = nil
	self.light_skill_name = nil
	self.img_skill_icon_normal = nil
	self.skill_icon = nil
	self.skill_lock = nil
	self.rember_skill_level = nil
	self.last_skill_id = nil
	self.last_skill_aweak_name = nil
end

function SkillLearnItemRender:OnFlush()
	self:CheckSeriesShow()
	if self.data == nil then
		return
	end

	self:OnChangeHighLight(self:IsSelectIndex())

	if self.data.skill_id then
		self.skill_lock:SetActive(false)
		if self.data.skill_id == 0 then					--未开放的技能
			self.skill_icon:SetActive(false)
			self.lbl_skill_name:SetActive(false)
			self.light_skill_name:SetActive(false)
			self.node_list["aweak_eff"]:SetActive(false)
			-- self.node_list["aweak_img"]:SetActive(false)
			if IS_AUDIT_VERSION then
				self.lbl_skill_unopen:SetActive(false)
				-- self.img_skill_icon_normal:SetActive(false)
			else
				self.lbl_skill_unopen:SetActive(true)
				-- self.img_skill_icon_normal:SetActive(true)
				self.lbl_skill_unopen:SetActive(Language.Role.ToExpect)
			end
		else
			self.skill_icon:SetActive(true)
			self.lbl_skill_name:SetActive(true)
			--self.light_skill_name:SetActive(true)
			-- self.img_skill_icon_normal:SetActive(true)
			--图标
			local bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id))
			self.skill_icon.image:LoadSprite(bundle, asset, function()
				self.skill_icon.image:SetNativeSize()
			end)

			local skill_info = SkillWGData.Instance:GetSkillInfoById(self.data.skill_id)
			if skill_info ~= nil then
				local skill_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.data.skill_id, skill_info.awake_level)
				XUI.SetGraphicGrey(self.skill_icon, false)
				self:SetSkillName(self.data.skill_name)
				if skill_break_cfg then
					self.node_list["aweak_eff"]:SetActive(true)
					-- self.node_list["aweak_img"]:SetActive(true)
					self:SetSkillName(self.data.skill_name)
				else
					self.node_list["aweak_eff"]:SetActive(false)
					-- self.node_list["aweak_img"]:SetActive(false)
				end
				self.node_list["txt_level"].text.text = skill_info.level
				self.node_list["light_txt_level"].text.text = skill_info.level
				--记录当前的技能等级播放特效
				if nil == self.rember_skill_level then
					self.rember_skill_level = skill_info.level
				elseif self.rember_skill_level ~= skill_info.level and self.rember_skill_level < skill_info.level then
					local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
					EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos"].transform, 0.5)
					self.rember_skill_level = skill_info.level
				end
			else
				XUI.SetGraphicGrey(self.skill_icon, true)
				self.node_list["txt_level"].text.text = 0
				self.node_list["light_txt_level"].text.text = 0
				self.node_list["aweak_eff"]:SetActive(false)
				-- self.node_list["aweak_img"]:SetActive(false)
				if self.index > 4 then
					self:SetSkillName(Language.Skill.OpenTip_4)
				else
					local info = SkillWGData.Instance:GetTaskSkillOpen(self.data.skill_id)
					self:SetSkillName(info and ToColorStr(info.unlock_desc, COLOR3B.RED))
				end
			end
		end
	else
		self.skill_lock:SetActive(true)
		self.skill_icon:SetActive(false)
		self.node_list["aweak_eff"]:SetActive(false)
		-- self.node_list["aweak_img"]:SetActive(false)
		self.node_list["txt_level"].text.text = 0
		self.node_list["light_txt_level"].text.text = 0
		self:SetSkillName(ToColorStr(Language.Skill.OpenTip_6, COLOR3B.RED))
	end
end

function SkillLearnItemRender:SetSkillName(text)
	self.lbl_skill_name.text.text = text
	self.light_skill_name.text.text = text
end

-- 设置高亮
function SkillLearnItemRender:OnChangeHighLight(is_select)
	--self.img_skill_icon_normal:CustomSetActive(not is_select)
	self.node_list.highlight:CustomSetActive(is_select)
end

function SkillLearnItemRender:CheckRemind(tab_index)
	if self.data == nil or next(self.data) == nil then
		self.node_list["icon_remind"]:SetActive(false)
		return
	end

    if self.data.skill_id == nil or self.data.skill_id == 0 then
        self.node_list["icon_remind"]:SetActive(false)
        return
    end

    local skill_info = SkillWGData.Instance:GetSkillInfoById(self.data.skill_id)
	local is_show_red = RoleWGData.Instance:GetSkillShowRed(self.data.skill_id, skill_info, tab_index)
    self.node_list["icon_remind"]:SetActive(is_show_red)
end

function SkillLearnItemRender:SetRedType(tab_index)
	self:CheckRemind(tab_index)
end

function SkillLearnItemRender:CheckSeriesShow()
	local _, skill_series = SkillWGData.Instance:GetCurUpGradeInfo()
	if skill_series == 0 or self.data.skill_id == nil then
		self.node_list["series_type_bg"]:SetActive(false)
	else
		self.node_list["series_type_bg"]:SetActive(true)
		self.node_list["txt_series_type"].text.text = Language.Skill.SeriesTxt[skill_series]
	end
end