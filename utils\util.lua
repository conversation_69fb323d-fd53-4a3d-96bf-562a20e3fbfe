require("utils/bit")

function IsNil(uobj)
	return uobj == nil or uobj:Equals(nil)
end

local split_cache = {}
local DB_INDEX_MARK_BIT = 16 --根据角色id获取服id的偏移值

function Split(split_string, splitter, need_cache)
	-- 以某个分隔符为标准，分割字符串
	-- @param split_string 需要分割的字符串
	-- @param splitter 分隔符
	-- @return 用分隔符分隔好的table

	local split_result = {}
	if not split_string then
		return split_result
	end

	split_string = tostring(split_string)
	if need_cache and split_cache[split_string] then
		return split_cache[split_string]
	end
	
	local search_pos_begin = 1
	while true do
		local find_pos_begin, find_pos_end = string.find(split_string, splitter, search_pos_begin)
		if not find_pos_begin then
			break
		end

		table.insert(split_result, string.sub(split_string, search_pos_begin, find_pos_begin - 1))
		search_pos_begin = find_pos_end + 1
	end

	if search_pos_begin <= #split_string then
		table.insert(split_result, string.sub(split_string, search_pos_begin))
	end

	if need_cache then
		split_cache[split_string] = split_result
	end

	return split_result
end

local develop_mode = require("editor/develop_mode")

function Join(join_table, joiner)
	-- 以某个连接符为标准，返回一个table所有字段连接结果
	-- @param join_table 连接table
	-- @param joiner 连接符
	-- @param return 用连接符连接后的字符串

	if #join_table == 0 then
		return ""
	end

	return table.concat(join_table, joiner)
end

-- 需要被重构的接口 勿使用
function TableCopy(t, n)
	if nil == t then return end
	if nil == n then
		n = 1
	end

	local new_t = {}
	for k, v in pairs(t) do
		if n > 0 and type(v) == "table" then
			local T = TableCopy(v, n - 1)
			new_t[k] = T
		else
			new_t[k] = v
		end
	end

	-- 监测DeepCopy
	if develop_mode:IsDeveloper() then
		develop_mode:OnLuaCall("table_copy")
	end

	return new_t
end

local cost_time = 0
local delay
local traceback
-- 已废弃方法 禁止使用
function __TableCopy(t, n)
	if nil == t then return end
	if nil == n then
		n = 1
	end
	-- local time = socket.gettime()
	local new_t = {}
	for k, v in pairs(t) do
		if n > 0 and type(v) == "table" then
			local T = TableCopy(v, n - 1)
			new_t[k] = T
		else
			new_t[k] = v
		end
	end

	-- 监测DeepCopy
	if develop_mode:IsDeveloper() then
		-- develop_mode:OnLuaCall("table_copy")
		-- traceback = debug.traceback()
		-- cost_time = cost_time + (socket.gettime() - time)
		-- delay = delay or GlobalTimerQuest:AddDelayTimer(function ()
		-- 	if cost_time > 0.005 then
		-- 	end
		-- 	cost_time = 0
		-- 	delay = nil
		-- end,1)
	end

	return new_t
end

function DeepCopy(object)
	-- @param object 需要深拷贝的对象
	-- @return 深拷贝完成的对象

	local lookup_table = {}
	local function _copy(pobj)
		if type(pobj) ~= "table" then
			return pobj
		elseif lookup_table[pobj] then
			return lookup_table[pobj]
		end

		local new_table = {}
		lookup_table[pobj] = new_table
		for index, value in pairs(pobj) do
			new_table[_copy(index)] = _copy(value)
		end

		return setmetatable(new_table, getmetatable(pobj))
	end

	-- 监测DeepCopy
	if develop_mode:IsDeveloper() then
		develop_mode:OnLuaCall("deep_copy")
	end

	return _copy(object)
end

function SwitchFuncs(t)
	t.Case = function (self, x)
		return self[x] or self.default
	end

	return t
end

function ToBoolean(s)
	-- 将字符串转换为boolean值

	local transform_map = {
		["true"] = true,
		["false"] = false,
	}

	return transform_map[s]
end

function TabToStr(tab, flag)
	local result = ""
	result = string.format("%s{", result)

	for k,v in pairs(tab) do
		if type(k) == "number" then
			if type(v) == "table" then
				result = string.format( "%s[%d]=%s,", result, k, TabToStr( v ) )
			elseif type(v) == "number" then
				result = string.format( "%s[%d]=%d,", result, k, v )
			elseif type(v) == "string" then
				result = string.format( "%s[%d]=%q,", result, k, v )
			elseif type(v) == "boolean" then
				result = string.format( "%s[%d]=%s,", result, k, tostring(v) )
			else
				if flag then
					result = string.format( "%s[%d]=%q,", result, k, type(v) )
				else
					error("the type of value is a function or userdata")
				end
			end
		else
			if type(v) == "table" then
				result = string.format( "%s%s=%s,", result, k, TabToStr( v, flag ) )
			elseif type(v) == "number" then
				result = string.format( "%s%s=%d,", result, k, v )
			elseif type(v) == "string" then
				result = string.format( "%s%s=%q,", result, k, v )
			elseif type(v) == "boolean" then
				result = string.format( "%s%s=%s,", result, k, tostring(v) )
			else
				if flag then
					result = string.format( "%s[%s]=%q,", result, k, type(v) )
				else
					error("the type of value is a function or userdata")
				end
			end
		end
	end
	result = string.format( "%s}", result )
	return result
end


local function tail(n, k)
	local u, r = '', 0
	for i = 1, k do
		n, r = math.floor(n/0x40), n%0x40
		u = string.char(r+0x80) .. u
	end
	return u, n
end

function To_Utf8(a)
  local n, u = tonumber(a), ''
  if n<0x80 then                        -- 1 byte
	return string.char(n)
  elseif n<0x800 then                   -- 2 byte
	u, n = tail(n, 1)
	return string.char(n+0xc0) .. u
  elseif n<0x10000 then                 -- 3 byte
	u, n = tail(n, 2)
	return string.char(n+0xe0) .. u
  elseif n<0x200000 then                -- 4 byte
	u, n = tail(n, 3)
	return string.char(n+0xf0) .. u
  elseif n<0x4000000 then               -- 5 byte
	u, n = tail(n, 4)
	return string.char(n+0xf8) .. u
  else                                  -- 6 byte
	u, n = tail(n, 5)
	return string.char(n+0xfc) .. u
  end
end

function IsEmptyTable(t)
	return t == nil or _G.next(t) == nil
end

function SortTableKey(tab, is_sub)
	local index_list = {}
	for k in pairs(tab) do
		index_list[#index_list + 1] = k
	end
	
	local sort_func = is_sub and function(a, b) return a > b end or function(a, b) return a < b end
	table.sort(index_list, sort_func)
	
	local temp_list = {}
	for i = 1, #index_list do
		temp_list[i] = tab[index_list[i]]
	end
	
	return temp_list
end

--大数非实数表示显示 返回string
function BigNumFormat(value)
	if value < 100000000 then
		return tostring(value)
	end

	local high = math.floor(value / 100000000)
	local low = value % 100000000
	return string.format("%d%08d", high, low)
end

ListToMapConfig = {}
ListToMapConfig.list_to_map_cache = {}
ListToMapConfig.list_to_map_list_cache = {}

function ListToMap(list, ...)
	if nil == list then
		ErrorLog("ListToMap list is nil")
		return nil
	end

	
	local key_list = {...}
	local max_depth = #key_list

	-- 缓存处理后的数据
	-- local key_str = ""
	-- for i,v in ipairs(key_list) do
	-- 	key_str = key_str .. "#"
	-- end
	-- ListToMapConfig.list_to_map_cache[list] = ListToMapConfig.list_to_map_cache[list] or {}
	-- if nil ~= ListToMapConfig.list_to_map_cache[list][key_str] then
	-- 	return ListToMapConfig.list_to_map_cache[list][key_str]
	-- end

	if max_depth <= 0 then
		ErrorLog("ListToMap max_depth is error")
		return nil
	end

	local map = {}
	local function parse_item(t, item, depth)
		local key = item[key_list[depth]]
		if nil == t[key] then
			t[key] = {}
		end

		if depth < max_depth then
			parse_item(t[key], item, depth + 1)
		else
			t[key] = item
		end
	end

	if list[0] ~= nil then
		parse_item(map, list[0], 1)
	end

	for i,v in ipairs(list) do
		parse_item(map, v, 1)
	end

	if G_IsDeveloper then
		table.insert(ListToMapConfig, map)
	end

	-- 监测lua性能
	if develop_mode:IsDeveloper() then
		develop_mode:OnLuaCall("list_to_map", list, key_list)
	end

	-- ListToMapConfig.list_to_map_cache[list][key_str] = map
	return map
end

-- 参数list  是无序的
function ListToMapByDisorder(list, ...)
	if nil == list then
		ErrorLog("ListToMap list is nil")
		return nil
	end

	local key_list = {...}
	local max_depth = #key_list

	if max_depth <= 0 then
		ErrorLog("ListToMap max_depth is error")
		return nil
	end

	local map = {}
	local function parse_item(t, item, depth)
		local key = item[key_list[depth]]
		if nil == t[key] then
			t[key] = {}
		end

		if depth < max_depth then
			parse_item(t[key], item, depth + 1)
		else
			t[key] = item
		end
	end

	for i,v in pairs(list) do
		parse_item(map, v, 1)
	end

	return map
end

-- 使用 ListToMapList(tt, "id") 后将 tt = {
-- 	{id = 2, name = "23", sex = 0},
-- 	{id = 2, name = "23", sex = 1},
-- 	{id = 3, name = "23", sex = 0},
-- 	{id = 3, name = "23", sex = 1},
-- }
-- 变成tt = {
-- 	[2] = {{id = 2, name = "23", sex = 0},
-- 		   {id = 2, name = "23", sex = 1},}

-- 	[3] = {{id = 3, name = "23", sex = 0},
--		   {id = 3, name = "23", sex = 1},}
-- }
function ListToMapList(list, ...)
	if nil == table then
		ErrorLog("ListToMapList list is nil")
		return nil
	end
	
	local key_list = {...}
	local max_depth = #key_list

	if max_depth <= 0 then
		ErrorLog("ListToMapList max_depth is error")
		return nil
	end

	-- 缓存处理后的数据
	-- local key_str = ""
	-- for i,v in ipairs(key_list) do
	-- 	key_str = key_str .. "#"
	-- end
	-- ListToMapConfig.list_to_map_list_cache[list] = ListToMapConfig.list_to_map_list_cache[list] or {}
	-- if nil ~= ListToMapConfig.list_to_map_list_cache[list][key_str] then
	-- 	return ListToMapConfig.list_to_map_list_cache[list][key_str]
	-- end
	local map = {}
	local function parse_item(t, item, depth)
		local key = item[key_list[depth]]
		if nil == t[key] then
			t[key] = {}
		end

		if depth < max_depth then
			parse_item(t[key], item, depth + 1)
		else
			table.insert(t[key], item)
		end
	end

	if list[0] ~= nil then
		parse_item(map, list[0], 1)
	end

	for i,v in ipairs(list) do
		parse_item(map, v, 1)
	end

	if G_IsDeveloper then
		table.insert(ListToMapConfig, map)
	end

	-- 监测lua性能
	if develop_mode:IsDeveloper() then
		develop_mode:OnLuaCall("list_to_map_list", list, key_list)
	end

	-- ListToMapConfig.list_to_map_list_cache[list][key_str] = map

	return map
end

function StringLen(inputstr)
	-- body
	if not inputstr or type(inputstr) ~= "string" or #inputstr <= 0 then
        return 0
    end
    local length = 0  -- 字符的个数
    local i = 1
    local list = {}
    while true do
        local curByte = string.byte(inputstr, i)
        local byteCount = 1
        if curByte > 239 then
            byteCount = 4  -- 4字节字符
        elseif curByte > 223 then
            byteCount = 3  -- 汉字
        elseif curByte > 128 then
            byteCount = 2  -- 双字节字符
        else
            byteCount = 1  -- 单字节字符
        end

        local char = string.sub(inputstr, i, i + byteCount - 1)
        -- print_error(char)  -- 打印单个字符
        list[#list + 1] = char
        i = i + byteCount
        length = length + 1
        if i > #inputstr then
            break
        end
    end

    return length, list
end

-- 检测，并返回符合长度的字符串
function CheckStringLen(inputstr, max_len)
	-- body
	if not inputstr or type(inputstr) ~= "string" or #inputstr <= 0 then
        return true
    end
    local total_len = 0  -- 字符长度
    local i = 1
    local list = {}
    while true do
        local curByte = string.byte(inputstr, i)
        local byteCount = 1
        if curByte > 239 then
            byteCount = 4  -- 4字节字符
        elseif curByte > 223 then
            byteCount = 3  -- 汉字
        elseif curByte > 128 then
            byteCount = 2  -- 双字节字符
        else
            byteCount = 1  -- 单字节字符(数字英文)
        end
        if 1 == byteCount then
        	total_len = total_len + byteCount + 0.5
        else
        	total_len = total_len + byteCount
        end

        local char = string.sub(inputstr, i, i + byteCount - 1)
        list[#list + 1] = char
        i = i + byteCount
        if total_len > max_len then
        	table.remove(list, #list)
        	return false, list
        end
		if i > #inputstr then
            break
        end
    end
    return true, list
end

-- 按空格长度获取一定长度内的文本
function StringLenBySpace(inputstr, need_space, have_emoji)
	need_space = need_space or 0
	local str = ""
	if not inputstr or type(inputstr) ~= "string" or #inputstr <= 0 or need_space <= 0 then
        return str
    end

	local list = {}
    local start_byte = 1
	local total_space = 0
    while true do
        local curByte = string.byte(inputstr, start_byte)
        local byteCount = 1
		local spaceCount = 1
		local is_normal_calc = true
		if have_emoji and curByte == 91 then  -- 匹配到'['
			local start_index, end_index = string.find(inputstr, "%[%d+%]", start_byte)
			if start_index and end_index and (end_index - start_index) == 4 then
				byteCount = end_index - start_index + 1
				spaceCount = 2
				is_normal_calc = false
			end
		end

		if is_normal_calc then
			if curByte > 239 then
				byteCount = 4  -- 4字节字符
				spaceCount = 2
			elseif curByte > 223 then
				byteCount = 3  -- 汉字
				spaceCount = 2
			elseif curByte > 128 then
				byteCount = 2  -- 双字节字符
				spaceCount = 2
			else
				byteCount = 1  -- 单字节字符
				spaceCount = 1
			end
		end

        local char = string.sub(inputstr, start_byte, start_byte + byteCount - 1)
        -- print_error(char)  -- 打印单个字符
        start_byte = start_byte + byteCount
		list[#list + 1] = char
		total_space = total_space + spaceCount
        if start_byte > #inputstr or total_space >= need_space then
            break
        end
    end

	str = table.concat(list)
    return str
end

--检测第一个字符是否为汉字
function CheckChineseCharacters(inputstr)
	if not inputstr or type(inputstr) ~= "string" or #inputstr <= 0 then
        return false
    end
    local i = 1
	local curByte = string.byte(inputstr, i)
	if curByte > 239 then
		return false
	elseif curByte > 223 then
		return true	--汉字
	elseif curByte > 128 then
		return false
	else
		return false
	end
end

--检测字符c串内有几个汉字
function CheckCharactersHaveCh(inputstr)
	if not inputstr or type(inputstr) ~= "string" or #inputstr <= 0 then
        return 0
    end
    local i = 0
    for v = 1, #inputstr do
		local curByte = string.byte(inputstr, v)
		if curByte > 223 and  curByte <= 239 then
			i = i + 1
		end
    end
    return i
end

--检测字符c串内有几个大写英文字母
function CheckCharactersHaveBigEV(inputstr)
	if not inputstr or type(inputstr) ~= "string" or #inputstr <= 0 then
        return 0
    end
    local i = 0
    for v = 1, #inputstr do
		local curByte = string.byte(inputstr, v)
		if curByte > 64 and  curByte <= 90  then
			i = i + 1
		end
    end
    return i
end

function GetTableLen( tab )
	local i = 0
	if type(tab) == "table" then
		for k,v in pairs(tab) do
			if v ~= nil then
				i = i + 1
			end
		end
		return i
	end
	return i
end

function NumberToChinaNumber(szNum)
    ---阿拉伯数字转中文大写
    local szChMoney = ""
    local iLen = 0
    local iNum = 0
    local iAddZero = 0
    local hzUnit = Language.ChinaNub.hzUnit
    local hzNum =	Language.ChinaNub.hzNum
    local tonum_szNum = tonumber(szNum)
    if nil == tonum_szNum then
        return tostring(szNum)
    end

    --直接返回零
    if 0 == tonum_szNum then
    	return Language.ChinaNub.hzNum[1]
    end

    iLen = string.len(szNum)
    if iLen > 10 or iLen == 0 or tonum_szNum < 0 then
        return tostring(szNum)
    end
	
    for i = 1, iLen  do
        iNum = string.sub(szNum,i,i)
        if iNum == 0 and i ~= iLen then
            iAddZero = iAddZero + 1
        else
            if iAddZero > 0 then
            szChMoney = szChMoney..hzNum[1]
        end
            szChMoney = szChMoney..hzNum[iNum + 1] --//转换为相应的数字
            iAddZero = 0
        end
        if (iAddZero < 4) and (0 == (iLen - i) % 4 or 0 ~= tonumber(iNum)) then
            szChMoney = szChMoney..hzUnit[iLen-i+1]
        end
    end

    local function removeZero(num)
        --去掉末尾多余的 零
        num = tostring(num)
        local szLen = string.len(num)
        local zero_num = 0
        for i = szLen, 1, -3 do
            szNum = string.sub(num,i-2,i)
            if szNum == hzNum[1] then
                zero_num = zero_num + 1
            else
                break
            end
        end
        num = string.sub(num, 1,szLen - zero_num * 3)
        szNum = string.sub(num, 1,6)
        --- 开头的 "一十" 转成 "十" , 贴近人的读法
        if szNum == hzNum[2]..hzUnit[2] then
            num = string.sub(num, 4, string.len(num))
        end
        return num
    end

    return removeZero(szChMoney)
end

function StrToTable(str)
    if str == nil or type(str) ~= "string" then
        return {}
    end

    if not loadstring("return " .. str) then
    	return {}
    end
	
    return loadstring("return " .. str)()
end

function TableToStr(t, depth)
	if t == nil then return "" end
    local retstr = "{"
    local is_ary = true
    local i = 1

    for key, value in pairs(t) do
        local end_s = ","
        local start_s = ""

        if depth == 1 and type(value) == 'table' then
            start_s = "\n		"
        end

        if key == i and is_ary then
            retstr = retstr .. start_s .. ToStringEx(value, depth) .. end_s
        else
            is_ary = false
            if type(key) == 'number' then
                retstr = retstr .. start_s .. string.format("[%s]=%s", ToStringEx(key, depth), ToStringEx(value, depth)) .. end_s
            elseif type(key) == 'string' then
                retstr = retstr .. start_s .. string.format("%s=%s", key, ToStringEx(value, depth)) .. end_s
            end
        end

        i = i + 1
    end

    retstr = retstr .. "}"
    return retstr
end

function ToStringEx(value, depth)
	if value == null then
		return 'null'
    elseif type(value)=='table' then
    	depth = depth + 1
       return TableToStr(value, depth)
    elseif type(value)=='string' then
    	value = string.gsub(value, "\n", "\\n")
    	value = string.gsub(value, "\r", "\\r")
        return '\"'..value..'\"'
    else
       return tostring(value)
    end
end

--把时-分-秒-转换为时间戳
--@time 的个格式为 h-m-s
function TimeToSecond(time)
	local tim_tab = Split(time,":")
	local tmp = os.date("*t",TimeWGCtrl.Instance:GetServerTime())
	local timer = os.time({day = tmp.day, month = tmp.month, year = tmp.year,
		hour = tim_tab[1] or 0, min = tim_tab[2] or 0, second = tim_tab[3] or 0,isdst = tmp.isdst})
	return timer
end

--把时-分-秒-转换为时间戳
--@time 的个格式为 h-m-s
function TimeToSecondByTab(hour, min, second)
	local tmp = os.date("*t",TimeWGCtrl.Instance:GetServerTime())
	local timer = os.time({day = tmp.day, month = tmp.month, year = tmp.year,
		hour = hour or 0, min = min or 0, second = second or 0, isdst = tmp.isdst})
	return timer
end

function ReverseList(list)
	-- body
	if not list or IsEmptyTable(list) then return list end
	local reverse_list = {}
	for i=#list,1,-1 do
		table.insert(reverse_list, list[i])
	end
	return reverse_list
end

function MathClamp(v, minValue, maxValue)
    if v < minValue then
        return minValue
    end
    if( v > maxValue) then
        return maxValue
    end
    return v
end

function CheckList(list, ...)
	if not list or type(list) ~= "table" then
		return
	end

	local data = nil
	for i = 1, select('#', ...) do
		local param = select(i, ...)
	    if param == nil then
	    	return
	    else
	    	if list[param] == nil then
				return
			end
	    end

	    list = list[param]
	end
	
	data = list
	return data
end

-- 文本去除空格
function DeleteStrSpace(str)
	str = str or ""
	str = string.gsub(str, " ", "")
	return str
end

--整型取位  服务端端要求 列如 151  百位1 十位5 个位1  分别表示不同的意义
--例如求 百位 IntgerAnalyze(151, 3)  return 1
function IntgerAnalyze(key, position)
	-- //这里只用得上前6位
	if key == nil or position == nil or key <= 0 or position <= 0 or position > 6 then
		return 0
	end
	local value = math.pow(10, position - 1)
	return math.floor(key/value)%10;
end


--------------------------------------------------------------------------
--归类排序
--t:表
--IsAheadCallBack：判断方法，判断当前值是否满足某条件，返回bool
--------------------------------------------------------------------------
function TableSortByCondition(t, IsAheadCallBack)
	if t == nil or next(t) == nil or IsAheadCallBack == nil then
		print_error("排序传值错误，表或回调为空")
		return t
	end
	local ahead_list = {}
	local behind_list = {}
	for i,v in ipairs(t) do
		if IsAheadCallBack(v) then
			table.insert(ahead_list, v)
		else
			table.insert(behind_list, i)
		end
	end
	for i,v in ipairs(behind_list) do
		table.insert(ahead_list,t[v])
	end
	return ahead_list
end

function ContainsValue(table, value, ...)
	for k, v in pairs(table) do
		if select('#', ...) > 0 then
			local is_same = true
			for i = 1, select('#', ...) do
				local key = select(i, ...)
				if v[key] ~= value[key] then
					is_same = false
				end
			end

			if is_same then
				return true, k
			end
		else
			if v == value then
				return true, k
			end
		end
	end
	return false, nil
end

function TableSplitByCondition(t, IsSelect)
	if t == nil or next(t) == nil or IsSelect == nil then
		return t
	end

	local ahead_list = {}
	for i,v in ipairs(t) do
		if IsSelect(v) then
			table.insert(ahead_list, v)
		end
	end

	return ahead_list
end

function IsSameArray(tab_1, tab_2)
	if tab_1 == nil or tab_2 == nil then
		return tab_1 == tab_2
	end

	if tab_1 ~= #tab_2 then
		return false
	end

	for i,v in ipairs(tab_1) do
		if tab_1[i] ~= tab_2[i] then
			return false
		end
	end

	return true
end

function IsSameTable(t1, t2)
	if type(t1) ~= type(t2) then return false end
	if type(t1) ~= 'table' then return t1 == t2 end

	-- 记录已比较的key防止循环引用
    local compared = {}

	-- 比较t1的所有键值对
	for k1, v1 in pairs(t1) do
		local v2 = t2[k1]
		if not IsSameTable(v1, v2) then
			return false
		end
		compared[k1] = true
	end

	-- 检查t2是否有额外键
	for k2, _ in pairs(t2) do
		if not compared[k2] then
			return false
		end
	end

	return true
end

--转换成在聊天面板输出的 避免EmojiTextUtil的解析
function TableToChatStr(t, depth)
    if t == nil then return "" end
    local retstr= "["

    local i = 1
    local is_ary = true
    for key,value in pairs(t) do
    	local end_s = ","
    	local start_s = ""
    	if depth == 1 and type(value)=='table' then
    		-- start_s = "\n		"
    		end_s = ","
    	end

        if key == i and is_ary then
            retstr = retstr .. start_s .. ToChatStringEx(value, depth) .. end_s
        else
        	is_ary = false
            if type(key)=='number' then
                retstr = retstr .. start_s .. '['..ToChatStringEx(key, depth).."]="..ToChatStringEx(value, depth) .. end_s

            elseif type(key) == 'string' then
            	retstr = retstr .. start_s .. key .. "=" .. ToChatStringEx(value, depth) .. end_s
           	end
        end

        i = i+1
    end

    retstr = retstr .. "]"

	return retstr
end

function ToChatStringEx(value, depth)
	if value == null then
		return 'null'
    elseif type(value)=='table' then
    	depth = depth + 1
       return TableToChatStr(value, depth)
    elseif type(value)=='string' then
    	value = string.gsub(value, "\n", "\\n")
    	value = string.gsub(value, "\r", "\\r")
        return '\"'..value..'\"'
    else
       return tostring(value)
    end
end

--获得排序后的list view(界面list view动画用)
function GetSortListView(list_tb)
	local sort_list = {}
	for i, v in pairs(list_tb) do
		local data = {}
		data.index = v:GetIndex()
		data.item = v
		sort_list[#sort_list + 1] = data
	end
	table.sort(sort_list, SortTools.KeyLowerSorter("index"))
	return sort_list
end

function ToLLStr(high_int, low_int)
	return table.concat({tostring(high_int), ":", tostring(low_int)})
end

function LLStrToInt(ll_str)
	local tab = Split(ll_str, ':')
	return tonumber(tab[1]), tonumber(tab[2])
end

function TwoUIntToLL(temp_high, temp_low)
	return LuaUInt64.LongLongToLuaNumber(temp_high, temp_low) or 0
end

function GetClientRoleIdByServerIdAndServerRoleId(server_id, server_role_id)
	return bit:_lshift(server_id, DB_INDEX_MARK_BIT) + bit:_lshift(server_role_id, 1)
end

-- 根据物品的品质排序,同品质的按照配置顺序排
function SortDataByItemColor(item_list)
	local data_list = {}
	if not item_list then
		return data_list
	end
	
	local item_cfg = nil
	local big_type = nil
	local is_show_zhengui = false
	local zhengui_effect_flag = 0
	local color_flag = 1
	local star_level = 0

	for k,v in pairs(item_list) do
		item_cfg,big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			is_show_zhengui = ItemWGData.Instance:CheckIsShowZhenGuiEffect(item_cfg.zhengui_effect or 1)
			zhengui_effect_flag = is_show_zhengui and 100000 or 0
			color_flag = item_cfg.color or 1

			if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
				star_level = v.param and v.param.star_level or 0
			else
				star_level = 0
			end

			data_list[zhengui_effect_flag + color_flag * 10000 + star_level * 1000 - k] = v
		end
	end

	return SortTableKey(data_list, true)
end

function GFind(cfg, param)
	local s,e = string.find(cfg, param, 1)
	local fun = function ()
		if not s or not e then
			return
		end
		local str = string.sub(cfg, s, e)
		s,e = string.find(cfg, param, s + 1)
		return str
	end

	return fun
end

function CombinServerRoleName(server_id, role_name)
	return string.format("[s%s]%s", server_id, role_name)
end

-- 获取解码base64、位移、异或后的数据(配合php用的)
function Base64Decode(data, key)
	local data_base64 = mime.unb64(data)
	if not data_base64 then
		return ""
	end
	
	local length = #data_base64
    local key_strlen = #key
    local xor_text = {}
    
    for i = 1, length do
        local bit_a = string.byte(data_base64, i)
        local bit_b = string.byte(key, (i - 1) % key_strlen + 1)
        xor_text[i] = string.char(bit:_xor(bit_a, bit_b))
    end

    return table.concat(xor_text)
end

function UrlDecode(s)
	s = string.gsub(s, '%%(%x%x)', function(h) return string.char(tonumber(h, 16)) end)
	return s
end

function UrlEncode(s)
	s = string.gsub(s, "([^%w%.%- ])", function(c) return string.format("%%%02X", string.byte(c)) end)
	return string.gsub(s, " ", "+")
end

local common_star_res = {
	[0] = "a3_ty_xx_zc0",
	[1] = "a3_ty_xx_zc",
	[2] = "a3_ty_xx_zc1",
	[10] = "a3_ty_xx_zc0",
	[11] = "a3_ty_xx_zc",
	[12] = "a3_ty_xx_zc1",
}

-- 对比common_star_res 灰色星星图片不同
local common_star_res2 = {
	[0] = "a3_ty_xx_zh",
	[1] = "a3_ty_xx_zc",
	[2] = "a3_ty_xx_zc1",
	[10] = "a3_ty_xx_zh",
	[11] = "a3_ty_xx_zc",
	[12] = "a3_ty_xx_zc1",
}

local customized_suit_star_res = {
	[0] = "a3_ty_xx_zh",
	[1] = "a3_ty_xx_zc",
	[2] = "a3_ty_xx_zc2",
	[10] = "a3_ty_xx_zh",
	[11] = "a3_ty_xx_zc",
	[12] = "a3_ty_xx_zc2",
}

local special_star_res = {
	[0] = "a3_ty_spxx_0_",
	[1] = "a3_ty_spxx_1_",
	[2] = "a3_ty_spxx_2_",
}

local special_star_res_2 = {
	[0] = "a3_ty_star_0",
	[1] = "a3_ty_star_1",
	[2] = "a3_ty_star_2",
}

local special_star_res_3 = {
	[0] = "a3_ty_xx_zc0",
	[1] = "a3_ty_xx_zc",
	[2] = "a3_ty_sp_xx_zc2",
	[3] = "a3_ty_sp_xx_zc3",
	[4] = "a3_ty_sp_xx_zc4",
	[5] = "a3_ty_sp_xx_zc5",
}

local special_star_res_4 = {
	[0] = "a3_ty_xx_zc0",
	[1] = "a3_ty_xx_zc",
	[2] = "a3_ty_xx_zc1",
	[3] = "a3_ty_xx_zc2",
	[4] = "a3_ty_xx_zc3",
	[5] = "a3_ty_xx_zc4",
	[6] = "a3_ty_xx_zc5",
	[7] = "a3_ty_xx_zc6",
	[8] = "a3_ty_xx_zc7",
	[9] = "a3_ty_xx_zc8",
	[10] = "a3_ty_xx_zc9",
	[11] = "a3_ty_xx_zc10",
	[12] = "a3_ty_xx_zc11",
	[13] = "a3_ty_xx_zc12",
	[14] = "a3_ty_xx_zc13",
	[15] = "a3_ty_xx_zc14",
	[16] = "a3_ty_xx_zc15",
	[17] = "a3_ty_xx_zc16",
	[18] = "a3_ty_xx_zc17",
	[19] = "a3_ty_xx_zc18",
	[20] = "a3_ty_xx_zc19",
}

--评分
local special_star_res_5 = {
    [0] = "a3_fb_ysz_4",
    [1] = "a3_fb_ysz_3",
    [2] = "a3_fb_ysz_2",
    [3] = "a3_fb_ysz_1",
}

local special_star_res_effect_5 = {
    [0] = "UI_pingfen_A",
    [1] = "UI_pingfen_S",
    [2] = "UI_pingfen_SS",
    [3] = "UI_pingfen_SSS",
}

-- 每5星一种资源
function GetStarImgResByStar(star_num, is_small_satr, no_empty,bg_type)
	star_num = star_num or 0
	bg_type = bg_type or 1
	local star_res_list = {}
	local value = 0
	local seq = is_small_satr and 0 or 10
	local per_value = math.floor(star_num / GameEnum.ITEM_MAX_STAR)
	per_value = math.max(no_empty and 1 or 0, per_value)
	local last_star_num = star_num - per_value * GameEnum.ITEM_MAX_STAR

	for i = 1, GameEnum.ITEM_MAX_STAR do
		value = i <= last_star_num and per_value + 1 or per_value
		if bg_type== 1 then
			star_res_list[i] = common_star_res[value + seq] or common_star_res[0]
		elseif bg_type == 2 then
			star_res_list[i] = common_star_res2[value + seq] or common_star_res2[0]
		end
	end

	return star_res_list
end

-- 每5星一种资源 永世套装专用 customized_suit_star_res
function GetSuitStarImgResByStar(star_num, is_small_satr, no_empty)
	star_num = star_num or 0
	local star_res_list = {}
	local value = 0
	local seq = is_small_satr and 0 or 10
	local per_value = math.floor(star_num / GameEnum.ITEM_MAX_STAR)
	per_value = math.max(no_empty and 1 or 0, per_value)
	local last_star_num = star_num - per_value * GameEnum.ITEM_MAX_STAR

	for i = 1, GameEnum.ITEM_MAX_STAR do
		value = i <= last_star_num and per_value + 1 or per_value
		star_res_list[i] = customized_suit_star_res[value + seq] or customized_suit_star_res[0]
	end

	return star_res_list
end



-- 特殊星星资源(武魂)
function GetSpecialStarImgResByStar(star_num, no_empty)
	star_num = star_num or 0
	local star_res_list = {}
	local value = 0
	local res_str = ""
	local per_value = math.floor(star_num / GameEnum.ITEM_MAX_STAR)
	per_value = math.max(no_empty and 1 or 0, per_value)
	local last_star_num = star_num - per_value * GameEnum.ITEM_MAX_STAR

	for i = 1, GameEnum.ITEM_MAX_STAR do
		value = i <= last_star_num and per_value + 1 or per_value
		res_str = special_star_res[value] or special_star_res[0]
		star_res_list[i] = res_str .. i
	end

	return star_res_list
end

-- 特殊星星资源(诸神)
function GetSpecialStarImgResByStar2(star_num)
	star_num = star_num or 0
	local star_res_list = {}
	local value = 0
	local per_value = math.floor(star_num / GameEnum.ITEM_MAX_STAR)
	local last_star_num = star_num - per_value * GameEnum.ITEM_MAX_STAR

	for i = 1, GameEnum.ITEM_MAX_STAR do
		value = i <= last_star_num and per_value + 1 or per_value
		star_res_list[i] = special_star_res_2[value] or special_star_res_2[0]
	end

	return star_res_list
end

-- 特殊星星资源(幻兽)(超过10星)
function GetSpecialStarImgResByStar3(star_num)
	star_num = star_num or 0
	local star_res_list = {}
	local value = 0
	local per_value = math.floor(star_num / GameEnum.ITEM_MAX_STAR)
	local last_star_num = star_num - per_value * GameEnum.ITEM_MAX_STAR

	for i = 1, GameEnum.ITEM_MAX_STAR do
		if last_star_num == 0 then
			value = per_value
		else
			value = i <= last_star_num and per_value + 1 or 0
		end
	
		star_res_list[i] = special_star_res_3[value] or special_star_res_3[0]
	end

	return star_res_list
end

-- 特殊星星资源(雷法)(超过10星)
function GetSpecialStarImgResByStar4(star_num)
	star_num = star_num or 0
	local star_res_list = {}
	local value = 0
	local per_value = math.floor(star_num / GameEnum.ITEM_MAX_STAR)
	local last_star_num = star_num - per_value * GameEnum.ITEM_MAX_STAR

	for i = 1, GameEnum.ITEM_MAX_STAR do
		if last_star_num == 0 then
			value = per_value
		else
			value = i <= last_star_num and per_value + 1 or 0
		end
	
		star_res_list[i] = special_star_res_4[value] or special_star_res_4[0]
	end

	return star_res_list
end

-- 特殊星星资源(评分)
function GetSpecialStarImgResByStar5(star_num)
	return special_star_res_5[star_num] or special_star_res_5[0]
end

function GetSpecialStarEffect5(star_num)
	return special_star_res_effect_5[star_num] or special_star_res_effect_5[0]
end

--将list从0开始的索引转成从1开始  key必须是数字
function ListIndexFromZeroToOne(list_tab)
	if not list_tab or type(list_tab) ~= "table" then
		return list_tab
	end

	local data = {}
	for k_1, v_1 in pairs(list_tab) do
		data[k_1 + 1] = v_1
	end

	return data
end

--将list的索引转成从0开始的列表.
function ListIndexStartFromZero(list_tab)
	if not list_tab or type(list_tab) ~= "table" then
		return list_tab
	end

	local data = {}
	local index = 0
	for k_1, v_1 in pairs(list_tab) do
		data[index] = v_1
		index = index + 1
	end

	return data
end

--设置通用绿标的跳动
function SetUIGreenImageCommonJump(node_image)
	local x, y = RectTransform.GetAnchoredPositionXY(node_image.rect)
	RectTransform.SetAnchoredPositionXY(node_image.rect, x, -15)
	local arrow_tweener = node_image.gameObject.transform:DOAnchorPosY(0, 0.5)
	arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	return arrow_tweener
end

local common_twenty_star_gray_res = {
	[1] = "a3_ty_xx_zh",
	[2] = "a3_ty_xx_zh1",
	[3] = "a3_ty_xx_zh2",
	[4] = "a3_ty_xx_zh3",
	[5] = "a3_ty_xx_zh4",
}

local common_twenty_star_light_res = {
	[1] = "a3_ty_xx_zc",
	[2] = "a3_ty_xx_zc1",
	[3] = "a3_ty_xx_zc2",
	[4] = "a3_ty_xx_zc3",
	[5] = "a3_ty_xx_zc4",
	[6] = "a3_ty_xx_zc5",
	[7] = "a3_ty_xx_zc6",
	[8] = "a3_ty_xx_zc7",
	[9] = "a3_ty_xx_zc8",
	[10] = "a3_ty_xx_zc9",
	[11] = "a3_ty_xx_zc10",
	[12] = "a3_ty_xx_zc11",
	[13] = "a3_ty_xx_zc12",
	[14] = "a3_ty_xx_zc13",
	[15] = "a3_ty_xx_zc14",
	[16] = "a3_ty_xx_zc15",
	[17] = "a3_ty_xx_zc16",
	[18] = "a3_ty_xx_zc17",
	[19] = "a3_ty_xx_zc18",
	[20] = "a3_ty_xx_zc19",
}

-- 五星一种资源 20种资源 20星得前5星补充灰色
function GetTwenTyStarImgResByStar(star_num)
	star_num = star_num or 0
	local one_star_range = 5
	local star_type_num = 20
	local star_res_list = {}

	if star_num <= 0 then
		for i = 1, one_star_range do
			star_res_list[i] = common_twenty_star_gray_res[1]
		end

		return star_res_list
	end

	local total_star = one_star_range * star_type_num

	if star_num % total_star == 0 then
		for i = 1, one_star_range do
			star_res_list[i] = common_twenty_star_light_res[star_type_num]
		end

		return star_res_list
	end

	local range_value = (star_num > total_star) and (star_num % total_star) or star_num
	local beishu = math.floor(range_value / one_star_range)
	local yushu  = range_value % one_star_range

	local light_star_index = (beishu > 0) and (yushu > 0 and (beishu + 1) or beishu) or 1
	local gray_star_index = math.min(math.floor(range_value / star_type_num) + 1, one_star_range)
	local start_star_index = light_star_index * one_star_range - one_star_range + 1
	local diff_value = range_value - start_star_index + 1

	local gray_rand_value = range_value % star_type_num
	local is_show_gray = (gray_rand_value > 0) and (gray_rand_value <= one_star_range)

	for i = 1, one_star_range do
		local is_light = diff_value >= i
		local img_res = is_light and common_twenty_star_light_res[light_star_index] or 
			(is_show_gray and common_twenty_star_gray_res[gray_star_index] or common_twenty_star_light_res[light_star_index - 1])
		star_res_list[i] = img_res
	end

	return star_res_list
end

-- 	去掉富文本中带有颜色的字符串
local color_pattern = "#[0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F]"
local color_prefix = "<color=>"
local color_suffix = "</color>"
function RemoveRichTextColorTags(str)
	local str = string.gsub(str, color_pattern, "")	-- 去掉颜色
	str = string.gsub(str, color_prefix, "")				-- 去掉颜色富文本前缀
	str = string.gsub(str, color_suffix, "")				-- 去掉颜色富文本后缀
	return str
end
