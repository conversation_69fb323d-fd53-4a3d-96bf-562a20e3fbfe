DragonTempleEquipBag = DragonTempleEquipBag or BaseClass(SafeBaseView)

function DragonTempleEquipBag:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(956, 560)})
	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_dragon_temple_equip_bag")
end

function DragonTempleEquipBag:__delete()
end

function DragonTempleEquipBag:ReleaseCallBack()
	if self.equip_bag_list then
		self.equip_bag_list:DeleteMe()
		self.equip_bag_list = nil
	end
end


function DragonTempleEquipBag:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.DragonTemple.EquipBagTitle

	if nil == self.equip_bag_list then
        self.equip_bag_list = AsyncBaseGrid.New()
        self.equip_bag_list:SetStartZeroIndex(true)
        self.equip_bag_list:CreateCells({
            col = 10,
            cell_count = 60,
            list_view = self.node_list["ph_equip_bag_grid"],
            change_cells_num = 2,
        })
    end
end

function DragonTempleEquipBag:OnFlush()
	local bag_info = DragonTempleWGData.Instance:GetBagInfo()
	self.equip_bag_list:SetDataList(bag_info)
end