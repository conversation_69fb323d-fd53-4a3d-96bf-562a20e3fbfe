function LoverPkMsgView:JbjlLoadIndexCallBack()
    if not self.rank_jb_list then
        self.rank_jb_list = AsyncListView.New(LoverPkJBJLItemRender, self.node_list.rank_jb_list)
    end

    if not self.my_jb_item_list then
        self.my_jb_item_list = AsyncListView.New(ItemCell, self.node_list.my_jb_item_list)
    end

    self.node_list.act_jb_time.text.text = Language.LoverPK.JBJLTipDesc
end

function LoverPkMsgView:JbjlShowIndexCallBack()
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_RANK)
end

function LoverPkMsgView:JbjlReleaseCallBack()
    if self.rank_jb_list then
        self.rank_jb_list:DeleteMe()
        self.rank_jb_list = nil
    end

    if self.my_jb_item_list then
        self.my_jb_item_list:DeleteMe()
        self.my_jb_item_list = nil
    end
end

function LoverPkMsgView:JbjlOnFlush()
    local rank_list, my_rank_data = LoverPkWGData.Instance:GetRegularSeasonRankDataListAndMyInfo()
    self.rank_jb_list:SetDataList(rank_list)
    self:JbjlFlushMyRabkInfo(my_rank_data)
end

function LoverPkMsgView:JbjlFlushMyRabkInfo(my_rank_data)
    local info_data = my_rank_data.info
    local has_data = not IsEmptyTable(info_data)
    self.node_list.not_my_jb_item:CustomSetActive(not has_data)
    self.node_list.my_jb_item:CustomSetActive(has_data)

    if has_data then
        self.my_jb_item_list:SetDataList(my_rank_data.cfg.reward_list)
        self.node_list.my_jb_rank.text.text = string.format(Language.LoverPK.JBJLRankValueStr, info_data.rank_id)
        local server_id = UserVo.GetServerId(info_data.uuid1.temp_low)
        self.node_list.my_jb_server.text.text = string.format(Language.Common.ServerIdFormat, server_id)
        self.node_list.my_jb_score.text.text = info_data.score
        self.my_jb_item_list:SetDataList(my_rank_data.cfg.reward_item)
    end
end

-------------------------------LoverPkJBJLItemRender--------------------------------------
LoverPkJBJLItemRender = LoverPkJBJLItemRender or BaseClass(BaseRender)

function LoverPkJBJLItemRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function LoverPkJBJLItemRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function LoverPkJBJLItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank = self.data.rank_id
	if self.node_list["bg"] then
		self.node_list.bg:SetActive(rank > 3 )
	end

    self.node_list["quality_bg"]:SetActive(rank <= 3)
	if rank <= 3 then
        self.node_list["quality_bg"].image:LoadSprite(ResPath.GetCommon("a2_xm_di_dj" .. rank))
    end

    self.reward_list:SetDataList(self.data.cfg.reward_item)

    self.node_list["rank"]:SetActive(self.index > 3)
    self.node_list["rank_img"]:SetActive(self.index <= 3)
    if rank <= 3 then
        self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a2_pm_"..rank))
        self.node_list["rank_img"].image:SetNativeSize()
        self.node_list.rank_text.text.text = rank
    else
        self.node_list["rank"].text.text = rank
        self.node_list.rank_text.text.text = ""
    end

    local info = self.data.info
    local has_role_info = not IsEmptyTable(info)
    if has_role_info then
        self.node_list.role_name1.text.text =  info.name1 or ""
        self.node_list.role_name2.text.text =  info.name2 or ""
        local server_id = UserVo.GetServerId((info.uuid1 or {}).temp_low or 0)
        server_id = server_id > 0 and server_id or RoleWGData.Instance:GetOriginServerId()
        self.node_list.server_name.text.text = string.format(Language.Common.ServerIdFormat, server_id)
		self.node_list.score.text.text = self.data.info.score or ""
    else
        self.node_list.role_name1.text.text =  Language.LoverPK.XiaHuaXian
        self.node_list.role_name2.text.text =  Language.LoverPK.XiaHuaXian
        self.node_list.server_name.text.text =  Language.LoverPK.XiaHuaXian
        self.node_list.score.text.text = Language.LoverPK.XiaHuaXian
    end
end