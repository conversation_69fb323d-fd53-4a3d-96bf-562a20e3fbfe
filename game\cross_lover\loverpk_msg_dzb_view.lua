--[[
				     0(第一名!)            (轮次4)
	       0				  1			   (轮次3)
	   0	   1	    2          3	   (轮次2)
	 0   1   2   3   4    5     6     7    (轮次1: 决定轮次2有哪些人)
	0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15  (轮次0:初始化)
--]]

local DZB_SHOW_TYPE = {
	DUIZHENBIAO = 1,
	JINGCAI = 2,
}

function LoverPkMsgView:DzbLoadIndexCallBack()
    if not self.msg_duizhanbiao_item_list then
        self.msg_duizhanbiao_item_list = {}
        
        for k,v in pairs(LoverPkWGData.COMPETIITION_SYSTEM_ORDER) do
            self.msg_duizhanbiao_item_list[k] = {}

            for i = 0, v - 1 do
                local render = LoverPKMsgDZBItemRender.New(self.node_list["role_team_" .. k .. "_" .. i])
                render:SetCombatPhase(k)
                render:SetIndex(i)
                render:SetClickRoleCallBack(BindTool.Bind(self.JingCaiClickRoleCallBack,self))
                render:Flush()
                self.msg_duizhanbiao_item_list[k][i] = render
            end
        end
    end

	self.cur_dzb_show_type = DZB_SHOW_TYPE.DUIZHENBIAO
	self.select_role_list = {}   -- 竞猜选中

	XUI.AddClickEventListener(self.node_list["jingcai_btn"],BindTool.Bind(self.ClickJingCaiBtn,self))
	XUI.AddClickEventListener(self.node_list["sendhua_btn"],BindTool.Bind(self.ClickSnedFlowerBtn,self))
	XUI.AddClickEventListener(self.node_list["jingcai_parent"],BindTool.Bind(self.ClickCloseJingCai,self))
	XUI.AddClickEventListener(self.node_list["touzhu_btn"],BindTool.Bind(self.ClickTouZhuBtn,self))
	XUI.AddClickEventListener(self.node_list["juesai_left"], BindTool.Bind(self.ClickJueSaiL,self))
	XUI.AddClickEventListener(self.node_list["juesai_right"], BindTool.Bind(self.ClickJueSaiR,self))
end

function LoverPkMsgView:DzbShowIndexCallBack()
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_KNOCKOUT_MATCH_INFO)
	LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.INFO_KNOCKOUT_GUESS_INFO)
	self.knockout_state_cache = LoverPkWGData.KNOCKOUT_STATE.WAIT_START
end

function LoverPkMsgView:DzbReleaseCallBack()
    if self.msg_duizhanbiao_item_list then
        for k, v in pairs(self.msg_duizhanbiao_item_list) do
            for i, u in pairs(v) do
                u:DeleteMe()
            end
        end

        self.msg_duizhanbiao_item_list = nil
    end

	if CountDownManager.Instance:HasCountDown("loverpk_dzb_jingcai_state_time") then
		CountDownManager.Instance:RemoveCountDown("loverpk_dzb_jingcai_state_time")
	end
end

function LoverPkMsgView:DzbOnFlush()
	local winner_info = LoverPkWGData.Instance:GetDZBWInnerInfo()
	local has_winner_data = not IsEmptyTable(winner_info)
	self.node_list.xuwei_img:CustomSetActive(not has_winner_data)
	self.node_list.guanjun_name:CustomSetActive(has_winner_data)
	self.node_list.guanjun_head_bg:CustomSetActive(has_winner_data)
	-- self.node_list.guanjun_head:CustomSetActive(has_winner_data)

	if has_winner_data then
		for i = 1, 2 do
			self.node_list["guanjun_name_" .. i].text.text = winner_info["name" .. i]
		end

		local is_robot = winner_info.is_robot1 and winner_info.is_robot1 == 1 or false
		if is_robot then
			local default_role_id = winner_info.default_uuid.temp_low or 0
			XUI.UpdateRoleHead(self.node_list.guanjun_head_default, self.node_list.guanjun_head, default_role_id,  winner_info.default_sex, winner_info.default_prof, false, false, false)
		else
			local role_id = ((winner_info or {}).uuid1 or {}).temp_low or 0
			BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
				XUI.UpdateRoleHead(self.node_list.guanjun_head_default, self.node_list.guanjun_head, role_id,  protocol.sex,  protocol.prof, false, false, false)
			end)
		end
	end

	self:FlushDuiZhenBiao()

	local match_type =LoverPkWGData.Instance:GetMatchType()
	local knockout_state = LoverPkWGData.Instance:GetKnockoutState()
	local knockout = LoverPkWGData.Instance:GetCurKnockoutRound()

	if match_type == CROSS_COUPLE_2V2_ATCH_TYPE.KNOCKOUT and (knockout_state == LoverPkWGData.KNOCKOUT_STATE.WAIT_START or knockout_state == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT)
	and knockout < 3 then
		local knockout_state_end_time = LoverPkWGData.Instance:GetKnockoutStateEndTime()

		if CountDownManager.Instance:HasCountDown("loverpk_dzb_jingcai_state_time") then
			CountDownManager.Instance:RemoveCountDown("loverpk_dzb_jingcai_state_time")
		end

		if knockout_state_end_time > 0 then
			self:JingCaiRefreshTime(0, knockout_state_end_time)
			CountDownManager.Instance:AddCountDown("loverpk_dzb_jingcai_state_time",
				function (elapse_time, total_time)
					self:JingCaiRefreshTime(elapse_time, total_time)
				end,
				function ()
					self:JingCaiTimeComplete()
				end,
			nil, knockout_state_end_time)
		else
			self:JingCaiTimeComplete()
		end
	else
		self:JingCaiTimeComplete()
	end
end

function LoverPkMsgView:JingCaiClickRoleCallBack(item)
	if nil == item then
        return
    end

	local round, idx = item.combat_phase, item.index
	if nil == round or nil == idx then
		return
	end

	if self.cur_dzb_show_type ~= DZB_SHOW_TYPE.JINGCAI then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()

	if scene_type ~= SceneType.CROSS_PK_LOVER_READY and scene_type ~= SceneType.CROSS_PK_LOVER then
		return
	end

	local match_type = LoverPkWGData.Instance:GetMatchType()
	local knockout = LoverPkWGData.Instance:GetCurKnockoutRound()

	if match_type == CROSS_COUPLE_2V2_ATCH_TYPE.MATCH then
		return
	end

	if knockout >= 3 then
		return
	end
	
	local now_knockout_state = LoverPkWGData.Instance:GetKnockoutState()
	if now_knockout_state ~= LoverPkWGData.KNOCKOUT_STATE.WAIT_START then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiCloseTips)
		return
	end

	local is_lunkong, is_jingcai = LoverPkWGData.Instance:TeamCanJingCai(round, idx)

	if is_lunkong then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.LunKongNotJingCai)
		return
	end

	if is_jingcai then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.TeamHasJIngCai)
		return
	end

	local oth_idx = (idx == 0 or idx % 2 == 0) and (idx + 1) or idx - 1
	local is_on = self.select_role_list[idx]
	self.select_role_list[idx] = not is_on
	self.select_role_list[oth_idx] = false

	if self.msg_duizhanbiao_item_list[round][idx] then
		self.msg_duizhanbiao_item_list[round][idx]:SetSelectImageState(not is_on)
	end

	if self.msg_duizhanbiao_item_list[round][oth_idx] then
		self.msg_duizhanbiao_item_list[round][oth_idx]:SetSelectImageState(false)
	end

	if round == 3 then
		if idx == 0 then
			self.node_list["left_h"]:SetActive(not is_on)
			self.node_list["right_h"]:SetActive(false)
		else
			self.node_list["left_h"]:SetActive(false)
			self.node_list["right_h"]:SetActive(not is_on)
		end
	end

	self:FlushJingCaiConsume()
end

function LoverPkMsgView:FlushDuiZhenBiao(cur_group)
	if cur_group then
		if self.msg_duizhanbiao_item_list[cur_group] then
			for i, v in pairs(self.msg_duizhanbiao_item_list[cur_group]) do
				v:Flush()
			end
		end
	else
		for k, list in pairs(self.msg_duizhanbiao_item_list) do
			for i, v in pairs(list) do
				v:Flush()
			end
		end
	end
end

function LoverPkMsgView:ClickJingCaiBtn()
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type ~= SceneType.CROSS_PK_LOVER_READY and scene_type ~= SceneType.CROSS_PK_LOVER then
		TipWGCtrl.Instance:OpenAlertTips(Language.LoverPK.CanNotJinCaiNeedInScene, function()
			CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
		end)

		return
	end

	local match_type = LoverPkWGData.Instance:GetMatchType()
	local knockout = LoverPkWGData.Instance:GetCurKnockoutRound()

	if match_type == CROSS_COUPLE_2V2_ATCH_TYPE.MATCH then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiOpenTip3)
		return
	end

	if knockout == 4 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.HasChampion)
		return
	end

	if knockout >= 3 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiCloseTips)
		return
	end
	
	local now_knockout_state = LoverPkWGData.Instance:GetKnockoutState()
	if now_knockout_state ~= LoverPkWGData.KNOCKOUT_STATE.WAIT_START then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiCloseTips)
		return
	end

	local can_jingcai = LoverPkWGData.Instance:IsCanJingcai()
	if not can_jingcai then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.LunKongNotJingCai)
		return
	end

	self.cur_dzb_show_type = DZB_SHOW_TYPE.JINGCAI
	self:FlushJingCai(true)
end

function LoverPkMsgView:ClickCloseJingCai()
	self.cur_dzb_show_type = DZB_SHOW_TYPE.DUIZHENBIAO
	self:FlushJingCai(false)
end

function LoverPkMsgView:ClickTouZhuBtn()
	local knockout = LoverPkWGData.Instance:GetCurKnockoutRound()
	local team_is_jingcai_over = LoverPkWGData.Instance:IsGroupAllJingCai(knockout)
	if team_is_jingcai_over then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiAllCount)
		return
	end

	if IsEmptyTable(self.select_role_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiNotSelect)
		return
	end

	local num = 0
	for k,v in pairs(self.select_role_list) do
		if v == true then
			num = num + 1
		end
	end

	if num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiNotSelect)
		return
	end

	local target_knockout = knockout + 1
	local guess_cfg = LoverPkWGData.Instance:GetKnockoutGuessCfg(target_knockout)
	local coin = guess_cfg.guess_need_gold * num
	local str = string.format(Language.LoverPK.JingCaiAlertDesc, coin)

	TipWGCtrl.Instance:OpenAlertTips(str, function ()
		for k,v in pairs(self.select_role_list) do
			if v == true then
				LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.GUESS_KNOCKOUT, target_knockout, k)
			end
		end

		self:ClickCloseJingCai()
	end)
end

function LoverPkMsgView:ClickSnedFlowerBtn()
	LoverPkWGCtrl.Instance:OpenLoverPKBlessingView()
end

function LoverPkMsgView:ClickJueSaiL()
	self:JingCaiClickRoleCallBack(self.msg_duizhanbiao_item_list[3][0])
end

function LoverPkMsgView:ClickJueSaiR()
	self:JingCaiClickRoleCallBack(self.msg_duizhanbiao_item_list[3][1])
end

-- 刷新竞猜时间
function LoverPkMsgView:JingCaiRefreshTime(elapse_time, total_time)
	if self.node_list.jingcai_time_text then
		local now_knockout_state = LoverPkWGData.Instance:GetKnockoutState()
		local target_knockout_round = LoverPkWGData.Instance:GetCurKnockoutRound()  -- 当前所在轮次
		local jingcai_time_str = ""

		if target_knockout_round < 0 then
			jingcai_time_str = Language.LoverPK.TaoTaiDZBStateStr[1] .. TimeUtil.FormatSecond(total_time - elapse_time, 2)
		else
			local knockout_ready_time = LoverPkWGData.Instance:GetKnockoutCfgByAttrName("knockout_ready_time") or 0   -- 准备时间  30s
			local fight_time = LoverPkWGData.Instance:GetKnockoutCfgByAttrName("fight_time") or 0                       --战斗时间 180

			if now_knockout_state == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT then
				jingcai_time_str = Language.LoverPK.TaoTaiDZBStateStr[0] .. TimeUtil.FormatSecond(total_time - elapse_time - knockout_ready_time, 2)
			else
				if target_knockout_round >= 3 then
					jingcai_time_str = Language.LoverPK.TaoTaiDZBStateStr[2]
				else
					jingcai_time_str = Language.LoverPK.TaoTaiDZBStateStr[1] .. TimeUtil.FormatSecond(total_time - elapse_time, 2)
				end
			end
		end

		if self.knockout_state_cache == LoverPkWGData.KNOCKOUT_STATE.WAIT_START and now_knockout_state == LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiCloseTips)
			self:ClickCloseJingCai()
		end
		
		self.knockout_state_cache = now_knockout_state
		self.node_list.jingcai_time_text.text.text = jingcai_time_str
	end
end

function LoverPkMsgView:JingCaiTimeComplete()
	self.node_list["jingcai_time_text"].text.text = ""

	if self.cur_dzb_show_type == DZB_SHOW_TYPE.JINGCAI then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.JingCaiCloseTips)
		self:ClickCloseJingCai()
	end
end

function LoverPkMsgView:FlushJingCai(is_show)
	self.node_list["jingcai_parent"]:SetActive(self.cur_dzb_show_type == DZB_SHOW_TYPE.JINGCAI)
	local knockout = LoverPkWGData.Instance:GetCurKnockoutRound()
	knockout = knockout + 1

	if is_show then
		self.node_list["juesai"]:SetActive(knockout == 3)
		if knockout == 3 then
			local data = LoverPkWGData.Instance:GetDzbMenberInfoByRoundAndIdx(3, 0)
			local data2 = LoverPkWGData.Instance:GetDzbMenberInfoByRoundAndIdx(3, 1)

			if IsEmptyTable(data) or IsEmptyTable(data2) then
				self:ClickCloseJingCai()
				return
			end

			for i = 1, 2 do
				self.node_list["left_name" .. i].text.text = data["name" .. i] or ""
				self.node_list["right_name" .. i].text.text = data2["name" .. i] or ""
			end

			local is_robot1 = data.is_robot1 == 1
			local is_robot2 = data2.is_robot1 == 1
			local left_role_id = ((data or {}).uuid1 or {}).temp_low or 0
			local right_role_id = ((data or {}).uuid1 or {}).temp_low or 0

			if is_robot1 then
				XUI.UpdateRoleHead(self.node_list.left_head_default, self.node_list.left_head, left_role_id,  data.default_sex, data.default_prof, false, false, false)
			else
				BrowseWGCtrl.Instance:BrowRoelInfo(left_role_id, function (protocol)
					XUI.UpdateRoleHead(self.node_list.left_head_default, self.node_list.left_head, left_role_id,  protocol.sex,  protocol.prof, false, false, false)
				end)
			end

			if is_robot2 then
				XUI.UpdateRoleHead(self.node_list.right_head_default, self.node_list.right_head, right_role_id,  data.default_sex, data.default_prof, false, false, false)
			else
				BrowseWGCtrl.Instance:BrowRoelInfo(right_role_id, function (protocol)
					XUI.UpdateRoleHead(self.node_list.right_head_default, self.node_list.right_head, right_role_id,  protocol.sex,  protocol.prof, false, false, false)
				end)
			end

			local left_flag = LoverPkWGData.Instance:CanShowJingCaiFlag(3, 0)
			local right_flag = LoverPkWGData.Instance:CanShowJingCaiFlag(3, 1)
			self.node_list["left_h"]:SetActive(left_flag)
			self.node_list["right_h"]:SetActive(right_flag)
		end

		if self.node_list["group_" .. knockout] and knockout < 3 then
			self.node_list["group_" .. knockout].transform:SetParent(self.node_list["jingcai_parent"].transform)
		end

		self.select_role_list = {}
		self:FlushJingCaiConsume()
	else
		for i = 0, 3 do
			self.node_list["group_" .. i].transform:SetParent(self.node_list["duizhen_group"].transform)
		end

		self:FlushDuiZhenBiao(knockout)
	end
end

function LoverPkMsgView:FlushJingCaiConsume()
	local num = 0
	if not IsEmptyTable(self.select_role_list) then
		for k,v in pairs(self.select_role_list) do
			if v == true then
				num = num + 1
			end
		end
	end

	local knockout = LoverPkWGData.Instance:GetCurKnockoutRound()
	knockout = knockout + 1

	local guess_cfg = LoverPkWGData.Instance:GetKnockoutGuessCfg(knockout)
	local guess_need_gold = guess_cfg and guess_cfg.guess_need_gold or 0
	-- local team_num = LoverPkWGData.COMPETIITION_SYSTEM_ORDER[knockout]
	-- local all_win_get = guess_cfg.guess_win_gold * team_num / 2
	-- self.node_list["all_win_num"].text.text = all_win_get
	self.node_list["consume1"]:SetActive(false)
	self.node_list["consume2"]:SetActive(num > 0)
	self.node_list["consume_num"].text.text = num * guess_need_gold
end

-------------------------------LoverPKMsgDZBItemRender--------------------------------
LoverPKMsgDZBItemRender = LoverPKMsgDZBItemRender or BaseClass(BaseRender)

function LoverPKMsgDZBItemRender:__init()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.ClickRoleBtn, self))
	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.ClickRewardBtn, self))
end

function LoverPKMsgDZBItemRender:__delete()
    self.click_role_callback = nil
end

function LoverPKMsgDZBItemRender:SetCombatPhase(combat_phase)
	self.combat_phase = combat_phase
end

function LoverPKMsgDZBItemRender:SetIndex(index)
	self.index = index
end

function LoverPKMsgDZBItemRender:SetClickRoleCallBack(call_back)
	self.click_role_callback = call_back
end

function LoverPKMsgDZBItemRender:OnFlush()
	if nil == self.combat_phase or nil == self.index then
		return
	end

	local data = LoverPkWGData.Instance:GetDzbMenberInfoByRoundAndIdx(self.combat_phase, self.index)
	local role_id = ((data or {}).uuid1 or {}).temp_low or 0
	local has_role_info = role_id > 0
	local name1 = data.name1 or ""
	local name2 = data.name2 or ""
	local is_robot = data and data.is_robot1 and data.is_robot1 == 1 or false

	if self.node_list["name"] then
		self.node_list["name"].text.text = has_role_info and name1 or Language.LoverPK.XuWeiYiDai
	end

	for i = 1, 2 do
		if self.node_list["name" .. i] then
			self.node_list["name" .. i].text.text = i == 1 and name1 or name2
		end
	end

	if self.node_list.xuwei then
		self.node_list.xuwei:CustomSetActive(not has_role_info)
	end

	if self.node_list.name_bg then
		self.node_list.name_bg:CustomSetActive(has_role_info)
	end

	if self.node_list.desc_server_bg then
		self.node_list.desc_server_bg:CustomSetActive(has_role_info)

		if has_role_info then
			if self.node_list.desc_server_id then
				local server_id = UserVo.GetServerId(role_id)
				server_id = server_id > 0 and server_id or RoleWGData.Instance:GetOriginServerId()
				self.node_list.desc_server_id.text.text = string.format(Language.Common.ServerIdFormat, server_id)
			end
		end
	end

	local result_info = LoverPkWGData.Instance:GetDZBMenberResultInfo(self.combat_phase, self.index)
	local is_result = result_info and result_info.is_result or false
	local is_winner = result_info and result_info.is_win or false
	local is_win = is_result and is_winner

	if self.node_list.bg_win then
		self.node_list.bg_win:CustomSetActive(is_result and is_winner)
	end

	if self.node_list.win then
		self.node_list.win:CustomSetActive(is_result and is_winner)
	end

	if self.node_list.lose then
		self.node_list.lose:CustomSetActive(is_result and not is_winner)
	end

	local guess_info = LoverPkWGData.Instance:GetDZBMenberGuessInfo(self.combat_phase, self.index)

	local can_get_reward = is_win and guess_info and guess_info.status == 1
	if self.node_list.reward_btn then
		self.node_list.reward_btn:CustomSetActive(can_get_reward)
	end

	local is_get_reward = is_win and guess_info and guess_info.status == 2
	if self.node_list.reward_open then
		self.node_list.reward_open:CustomSetActive(is_get_reward)
	end

	local can_show_jingcai_flag = LoverPkWGData.Instance:CanShowJingCaiFlag(self.combat_phase, self.index)

	if self.node_list.jingcai_img then
		self.node_list.jingcai_img:CustomSetActive(can_show_jingcai_flag)
	end

	self:SetSelectImageState(can_show_jingcai_flag)

	if self.node_list.head and self.node_list.head_default then
		if has_role_info then
			if is_robot then
				local default_role_id = data.default_uuid.temp_low or 0
				XUI.UpdateRoleHead(self.node_list.head_default, self.node_list.head, default_role_id,  data.default_sex, data.default_prof, false, false, false)
			else
				BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
					XUI.UpdateRoleHead(self.node_list.head_default, self.node_list.head, role_id,  protocol.sex,  protocol.prof, false, false, false)
				end)
			end
		end
	end

	if self.node_list.desc_get_flower_num then
		local flower_str = ""

		if not IsEmptyTable(data) and data.flowers_count then
			flower_str = "x".. data.flowers_count
		end

		self.node_list.desc_get_flower_num.text.text = flower_str
	end
end

function LoverPKMsgDZBItemRender:ClickRoleBtn()
    if self.click_role_callback then
		self.click_role_callback(self)
	end
end

function LoverPKMsgDZBItemRender:ClickRewardBtn()
	if nil == self.combat_phase or nil == self.index then
		return
	end

	LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.GUESS_KNOCKOUT_REWARD, self.combat_phase, self.index)
end

function LoverPKMsgDZBItemRender:SetSelectImageState(state)
	if self.node_list.select_img then
		self.node_list.select_img:CustomSetActive(state)
	end
end