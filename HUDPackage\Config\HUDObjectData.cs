using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HUDProgramme
{
    [Serializable]
    public struct HUDObject
    {
        public string name;

        public GameObject obj;
    }

    public class HUDObjectData : MonoBehaviour
    {
        public List<HUDObject> list = new List<HUDObject>();

        public void AddPrefab(GameObject obj)
        {
            if (obj == null)
                return;

            HUDObject temp = GetAnimPrefab(obj.name);

            if (temp.name == "" || temp.obj == null)
            {
                temp.name = obj.name;
                temp.obj = obj;
            }

            list.Add(temp);
        }

        public HUDObject GetAnimPrefab(string anim_name)
        {
            HUDObject temp_data = new HUDObject();

            if (list == null || list.Count <= 0)
                return temp_data;

            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].name == name)
                    return list[i];
            }

            return temp_data;
        }
    }
}