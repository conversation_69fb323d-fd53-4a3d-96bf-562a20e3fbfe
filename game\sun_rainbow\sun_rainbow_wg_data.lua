SunRainbowWgData = SunRainbowWgData or BaseClass()
SunRainbowWgData.BIG_REWARD_NUM = 4 -- 一轮奖励
SunRainbowWgData.SMALL_REWARD_NUM = 8 -- 一轮奖励

function SunRainbowWgData:__init()
    if SunRainbowWgData.Instance then
        ErrorLog("[SunRainbowWgData] Attemp to create a singleton twice !")
    end

    SunRainbowWgData.Instance = self
    self:InitConfig()

    self.grade = 0
    self.cur_draw_times = 0
    self.into_sp_reward_pool_num = 0
    self.fetch_flag = 0
    self.record_list = {}
	self.point = 0
	self.redemption_list = {}

	RemindManager.Instance:Register(RemindName.SunRainbow, BindTool.Bind(self.ShowRed, self))
end

function SunRainbowWgData:__delete()
    SunRainbowWgData.Instance = nil

	self.grade = nil
    self.cur_draw_times = nil
    self.into_sp_reward_pool_num = nil
    self.fetch_flag = nil
    self.record_list = nil
	self.point = nil
	self.redemption_list = nil

	RemindManager.Instance:UnRegister(RemindName.SunRainbow)
end

function SunRainbowWgData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_guanri_changhong_cfg_auto")
    self.reward_pool_cfg = ListToMapList(cfg.reward_pool, "reward_pool_id")         -- 奖池
    self.open_cfg = ListToMap(cfg.open_day, "grade")
    self.grade_cfg = ListToMap(cfg.config, "grade")
    self.reward_cfg = ListToMap(cfg.reward_pool, "reward_pool_id","reward_id")
    self.consume_cfg = ListToMapList(cfg.consume, "consume")
    self.rebate_cfg = ListToMap(cfg.rebate, "rebate", "index")
    self.probability_cfg = ListToMapList(cfg.item_random_desc, "grade")
    self.sun_shop_cfg = ListToMapList(cfg.redemption_points, "grade")
end

--全部数据
function SunRainbowWgData:SetAllInfo(protocol)
    self.grade = protocol.grade														-- 档位
	self.cur_draw_times = protocol.person_draw_count								-- 个人抽奖次数
    self.is_skip_comic = protocol.is_skip_comic										-- 跳过动画？
    self.into_sp_reward_pool_num = protocol.into_sp_reward_pool_num					-- 下一次进去大奖的次数
    self.fetch_flag = protocol.leiji_reward_fetch_flag								-- 累计奖励领取标记
    self.point = protocol.point														-- 积分
	self.redemption_list = protocol.redemption_list									-- 根据索引获取每个奖励已经兑换了的次数
end

function SunRainbowWgData:GetCurDrawTimes()
    return self.cur_draw_times
end

function SunRainbowWgData:GetCurPoint()
	return self.point
end

function SunRainbowWgData:GetExchangeNum(seq)
    return self.redemption_list[seq] or 1
end

function SunRainbowWgData:GetNextBigRewardTimes()
    return self.into_sp_reward_pool_num
end

function SunRainbowWgData:SetRecord(record_list)
    self.record_list = record_list
    self.new_record_time = self.record_list[1] and self.record_list[1].draw_time or 0
end




function SunRainbowWgData:GetFanliHasGet(index)
    return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index)) ~= 0
end

function SunRainbowWgData:ShowRed()
	if self:GetEnoughItem() then
        return 1
    end

    if self:GetFanliRed() then
        return 1
    end

    return 0
end

function SunRainbowWgData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

function SunRainbowWgData:GetFanliRed()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local draw_time = self:GetCurDrawTimes()
    for i, v in ipairs(self.rebate_cfg[rebate]) do
        if draw_time >= v.draw_num then
            if not self:GetFanliHasGet(v.index) then
                return true
            end
        else
            return false
        end
    end
end




function SunRainbowWgData:CacheOrGetDrawIndex(btn_index)
    if btn_index then
        self.draw_btn_index = btn_index
    end

    return self.draw_btn_index
end

function SunRainbowWgData:GetGradeCfg()
    return self.grade_cfg[self.grade] or self.grade_cfg[0]          -- 容错 没有配置 取默认配置
end

function SunRainbowWgData:GetRewardPool()
    local cfg = self:GetGradeCfg()
    return self.reward_pool_cfg[cfg.reward_pool_id] or {}
end

function SunRainbowWgData:GetRewardById(reward_pool_id, reward_id)
    return self.reward_cfg[reward_pool_id or 1][reward_id]
end

function SunRainbowWgData:GetProbabilityInfo()
    return self.probability_cfg[self.grade] or {}
end

function SunRainbowWgData:GetSunShopCfg()
    return self.sun_shop_cfg[self.grade] or {}
end

function SunRainbowWgData:GetConsumeCfg()
    local cfg = self:GetGradeCfg()
    return self.consume_cfg[cfg.consume] or {}
end

function SunRainbowWgData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = self:GetConsumeCfg()
        for i, v in pairs(cfg) do
            table.insert(self.item_data_change_list, v.draw_item.item_id)
        end
    end
    return self.item_data_change_list
end

function SunRainbowWgData:ShowNeedDisplay()
    local pool_cfg = self:GetRewardPool()
	local list_data = {}
    if not IsEmptyTable(pool_cfg) then
        for k,v in pairs(pool_cfg) do
            if v.need_reward_display == 1 then
                table.insert(list_data, v)
            end
        end
    end

	return list_data
end

function SunRainbowWgData:GetDifferentTypeCfg()
    local list_data = self:ShowNeedDisplay()
	local big_reward_list = {}
    local small_reward_list = {}
    if not IsEmptyTable(list_data) then
        for k,v in pairs(list_data) do
            if v.reward_show == 1 then
                if v.reward_type == 1 then
                    table.insert(big_reward_list, v)
                elseif v.reward_type == 2 then
                    table.insert(small_reward_list, v.reward_item)
                end
            end
        end
    end

	return big_reward_list, small_reward_list
end

-- 获取奖励对应的配置
function SunRainbowWgData:CalDrawRewardList(protocol)
    local data_list = {}
    local item_ids = {}
    if not protocol or not protocol.count or protocol.count <= 0 then
        return data_list, item_ids
    end

    local grade_cfg = self:GetGradeCfg()
    local reward_pool_id = grade_cfg.reward_pool_id
    for i,v in ipairs(protocol.reward_list) do
        local cfg = self:GetRewardById(reward_pool_id, v.reward_id)
        if cfg then
            local temp = cfg.reward_item
            temp.draw_corner_type = cfg.reward_big_show
            table.insert(data_list, temp)
        else
            print_error("错误数据 请检查贯日奖励配置 reward_pool_id,reward_id: ", reward_pool_id, v.reward_id)
        end
    end

	for i, v in pairs(protocol.baodi_item) do
		if v.item_id > 0 then
			table.insert(item_ids, v.item_id)
		end
	end

	return data_list, item_ids
end

function SunRainbowWgData:GetFanliRewardList()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local rebate_cfg = self.rebate_cfg[rebate]
    local draw_time = self:GetCurDrawTimes()
    local list = {}

    for i = 1, #rebate_cfg do
        local temp = {}
        -- if draw_time >= rebate_cfg[i].draw_num then
        --     if not self:GetFanliHasGet(rebate_cfg[i].index) then
        --         temp.index = rebate_cfg[i].index
        --     else
        --         temp.index = 1000 + rebate_cfg[i].index
        --     end
        -- else
        --     temp.index = 100 + rebate_cfg[i].index
        -- end
        temp.index = rebate_cfg[i].index

        temp.data = rebate_cfg[i]
        temp.has_get = self:GetFanliHasGet(rebate_cfg[i].index) and 1 or 0
        table.insert(list, temp)
    end

    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyUpperSorter("index"))
    end

    return list
end

function SunRainbowWgData:GetRecordInfo()
    local list = {}
    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            list[k] = v
        end
    end

    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyUpperSorter("draw_time"))
    end

    return list
end

