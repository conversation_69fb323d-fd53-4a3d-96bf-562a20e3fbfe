EmojiTextUtil = EmojiTextUtil or BaseClass()
EmojiTextUtil.parse_func_list = nil
local is_not_target = false
local is_bag_item = false

-- 清除emojitext
function EmojiTextUtil.ClearEmojiText(emoji_text)
	if not IsNil(emoji_text)  then
		emoji_text:Clear()
	end
end

-- not_target 按钮可穿透, font_size已没用但是去掉太麻烦就不管了   跑马灯的按钮需要被隐藏ignored_link
function EmojiTextUtil.ParseRichText(emoji_text, content, font_size, color, is_small, not_target, rich_content_type, has_bubble, ignored_link, is_light_bubble)
	if nil == emoji_text or nil == content then
		return
	end
	-- 先清空emoji_text
	EmojiTextUtil.TextStr = ""
	EmojiTextUtil.ButtonIndex = 0
	--技术设置的,大于一千就会有下划线
	EmojiTextUtil.ButtonIndexLine = 1000
	---------------------------------------------------------------------
	--[[修改了点击实现，去掉旧接口 调用新接口
		EmojiTextUtil.ClearEmojiText(emoji_text)
	]] 
	-- 应该可以使用GetOrAddComponent接口来清除
	--:GetOrAddComponent(typeof(TMPTextLinkOpener))
	local tmp_link_opener = emoji_text.gameObject:GetOrAddComponent(typeof(TMPTextLinkOpener))
	if tmp_link_opener then
		tmp_link_opener:Clear()
	end
	-------------------------------------------------------------------------
	is_not_target = not_target

	--类型(根据类型显示不同的颜色，大小，下划线)
	rich_content_type = rich_content_type or RICH_CONTENT_TYPE.NONE

	font_size = font_size or 20
	color = color or COLOR3B.WHITE
	--print_error("1", color, content)
	content = CommonDataManager.ParseGameName(content)
	local i, j = 0, 0
	local element_list = {}
	local last_pos = 1
	for loop_count = 1, 100 do
		i, j = string.find(content, "({.-})", j + 1)-- 匹配规则{face;20} {item;26000}
		if nil == i or nil == j then
			if last_pos <= #content then
				table.insert(element_list, {0, string.sub(content, last_pos, -1)})
			end
			break
		else
			if 1 ~= i and last_pos ~= i then
				table.insert(element_list, {0, string.sub(content, last_pos, i - 1)})
			end
			table.insert(element_list, {1, string.sub(content, i, j)})
			last_pos = j + 1
		end
	end

	local rule
	local link_name
	for i2, v2 in ipairs(element_list) do
		if 0 == v2[1] then
			EmojiTextUtil.AddText(emoji_text, v2[2], color)
		else
			rule = string.sub(v2[2], 2, -2)
			local rule_list = Split(rule, ";")
			if rule_list and rule_list[1] == "Link_name" then --后插的在原基础不好改动所以单独拎出来
				link_name = rule_list[2]
			else
				EmojiTextUtil.ParseMark(emoji_text, rule_list, font_size, color,
										rich_content_type, has_bubble, ignored_link, link_name, is_light_bubble)
			end
		end
	end

	if nil == emoji_text then
		print_error("emoji_text is nil!!!!!")
		return
	end

	emoji_text.text = EmojiTextUtil.TextStr
	EmojiTextUtil.TextStr = ""
end

--添加文字
function EmojiTextUtil.AddText(emoji_text, text, color)
	local str = text or ""
	if color then
		str = ToColorStr(text, color)
	end
	EmojiTextUtil.TextStr = string.format("%s%s", EmojiTextUtil.TextStr, str)
	return EmojiTextUtil.TextStr
end

--获取解析后的字符串(纯文本的情况下可调用)
function EmojiTextUtil.GetAnalysisText(content, color,rich_content_type)
	if nil == content then
		 return ""
	end

	EmojiTextUtil.TextStr = ""
	color = color or COLOR3B.WHITE
	local i, j = 0, 0
	local element_list = {}
	local last_pos = 1
	for loop_count = 1, 100 do
		i, j = string.find(content, "({.-})", j + 1)-- 匹配规则{face;20} {item;26000}
		if nil == i or nil == j then
			if last_pos <= #content then
				table.insert(element_list, {0, string.sub(content, last_pos, -1)})
			end
			break
		else
			if 1 ~= i and last_pos ~= i then
				table.insert(element_list, {0, string.sub(content, last_pos, i - 1)})
			end
			table.insert(element_list, {1, string.sub(content, i, j)})
			last_pos = j + 1
		end
	end

	local rule
	local link_name = nil
	for i2, v2 in ipairs(element_list) do
		if 0 == v2[1] then
			EmojiTextUtil.AddText(nil, v2[2], color)
		else
			rule = string.sub(v2[2], 2, -2)
			local rule_list = Split(rule, ";")
			if rule_list and rule_list[1] == "Link_name" then --后插的在原基础不好改动所以单独拎出来
				link_name = rule_list[2]
			else
				EmojiTextUtil.ParseMark(nil, rule_list, nil, color, rich_content_type, nil, nil, link_name)
			end
		end
	end
	return EmojiTextUtil.TextStr
end

function EmojiTextUtil.ParseMark(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, link_name, is_light_bubble)--ignored_link, text_attr, bigchatface_status, dabiaoqing_flag, dabiaoqing_size)
	local mark = params[1]
	if nil == mark then return end
	EmojiTextUtil.Init()
	local func = EmojiTextUtil.parse_func_list[mark]
	if nil ~= func then
		func(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, link_name, is_light_bubble)
	else
		print_error("unknown mark:" .. mark .. "!")
	end
end

----如果是可点击或图片的类型需要加到btn_mask_list 中
function EmojiTextUtil.Init()
	if nil == EmojiTextUtil.parse_func_list then
		EmojiTextUtil.parse_func_list = {
			["showpos"] = EmojiTextUtil.ParseShowPos,
			["gamename"] = EmojiTextUtil.ParseGameName,
			["face"] = EmojiTextUtil.ParseFace,
			["point"] = EmojiTextUtil.ParsePoint,
			["helppoint"] = EmojiTextUtil.ParseHelpPoint,
			["position"] = EmojiTextUtil.ParsePosition,
			["scene"] = EmojiTextUtil.ParseScene,
			["sceneid"] = EmojiTextUtil.ParseSceneId,
			["newline"] = EmojiTextUtil.ParseNewLine,
			["i"] = EmojiTextUtil.ParseItem,
			["eq"] = EmojiTextUtil.ParseItem,
			["tianshen_eq"] = EmojiTextUtil.ParseTianShenItem,
			["camp"] = EmojiTextUtil.ParseCamp,
			["r"] = EmojiTextUtil.ParseRole,
			["rand_name"] = EmojiTextUtil.ParseRandName,
			["wordcolor"] = EmojiTextUtil.ParseWordColor,
			["wordsColor"] = EmojiTextUtil.ParseWordColor,
			["channeltype"] = EmojiTextUtil.ParseChannelType,
			["channelmark"] = EmojiTextUtil.ParseChannelMark,
			["team"] = EmojiTextUtil.ParseTeam,
			["team_fenshen"] = EmojiTextUtil.ParseTeamFenShen,
			["teamfb"] = EmojiTextUtil.ParseTeamFb,
			["guildinfo"] = EmojiTextUtil.ParseGuildInfo,
			["guildinfo2"] = EmojiTextUtil.ParseGuildInfo2,
			["guildjoin"] = EmojiTextUtil.ParseGuildJoin,
			["money"] = EmojiTextUtil.ParseMoney,
			["chinese_num"] = EmojiTextUtil.ParseChineseNum,
			["zhuxie_task_item"] = EmojiTextUtil.ParseZhuXieTaskItem,
            ["shenyuan_boss_id"] = EmojiTextUtil.ParseShenYuanCaiji, --前往采集
			["fazhen_name"] = EmojiTextUtil.ParseFaZhenName,
			["defend_area"] = EmojiTextUtil.ParseDefendArea,
			["mount"] = EmojiTextUtil.ParseMount,
			["qibing"] = EmojiTextUtil.ParseQiBing,
			["to_decimal"] = EmojiTextUtil.ParseToDecimal,
			["monster"] = EmojiTextUtil.ParseMonster,
			["title"] = EmojiTextUtil.ParseTitle,
			["eq_shenzhu"] = EmojiTextUtil.ParseEquipShenZhu,
			["eq_quality"] = EmojiTextUtil.ParseEquipQuality,
			["csa_sub_type"] = EmojiTextUtil.ParseCsaSubType,
			["openLink"] = EmojiTextUtil.ParseOpenLink,
			["qingyuan_card"] = EmojiTextUtil.ParseQingYuanCard,
			["activityPos"] = EmojiTextUtil.ParseActivityPos,
			["activity"] = EmojiTextUtil.ParseActivity,
			["prof"] = EmojiTextUtil.ParseProf,
			["xianjie"] = EmojiTextUtil.ParseXianJie,
			["card_color"] = EmojiTextUtil.ParseCardColor,
			["gengu_title"] = EmojiTextUtil.ParseGengu,
			["jingmai_title"] = EmojiTextUtil.ParseJingmai,
			["mentality"] = EmojiTextUtil.ParseMentality,
			["feisheng_times"] = EmojiTextUtil.ParseFeiSheng,  -- 飞升名字
            ["shizhuang_name"] = EmojiTextUtil.ParseShiZhuangName,  -- 时装名字
            ["shizhuang_link"] = EmojiTextUtil.ParseShiZhuangLink,  -- 时装跳转

			["tianshen_name"] = EmojiTextUtil.ParseTianShenName,  -- 天神名字

			["fabao"] = EmojiTextUtil.ParseFabao,
			["longhui"] = EmojiTextUtil.ParseLonghui,
			["bubble"] = EmojiTextUtil.ParseBubble,
			-- ["cardzu"] = EmojiTextUtil.ParseCardzu,
			["cross_plant"] = EmojiTextUtil.ParseCrossPlant,
			["cross_team_fb"] = EmojiTextUtil.CrossTeamFb,
			["cross_air_war_monster"] = EmojiTextUtil.ParseCrossAirWarMonster,
			["cross_air_war_auction"] = EmojiTextUtil.ParseCrossAirWarAuction,
			["upgrade_type"] = EmojiTextUtil.UpgradeType,
			["ei"] = EmojiTextUtil.ParseEi,
			--["building"] = EmojiTextUtil.ParseBuilding,
			--["brick_color"] = EmojiTextUtil.ParseBrickColor,
			["hunshou_color"] = EmojiTextUtil.ParseHunshouColor,
			["title_title"] = EmojiTextUtil.ParseTianxiangeTitle,
			["color_outline"] = EmojiTextUtil.ParseWordColorAndOutline,
			["scene_id"] = EmojiTextUtil.ParseFbSceneId,
			["worldboss_lnk"] = EmojiTextUtil.ParseWorldBossInfo,
			["guild_post"] = EmojiTextUtil.GuildPost,
			["mount_special_img"] = EmojiTextUtil.MountName,
			["foot_id_img"] = EmojiTextUtil.FootName,
			["waist_id_img"] = EmojiTextUtil.WaistName,
			["lingchong_special_img"] = EmojiTextUtil.MountLingChongName,
			["shizhuang_type"] = EmojiTextUtil.ShizhuangType,
			["shizhuang_index"] = EmojiTextUtil.ShizhuangIndex,
			["skill_id"] = EmojiTextUtil.SkillId,
			["shenshou_compose_id"] = EmojiTextUtil.ShenShouComposeId,
			["eq_compose"] = EmojiTextUtil.ComposeStar,
			["mount_grade"] = EmojiTextUtil.ParseMountGrade,
			["lingchong_grade"] = EmojiTextUtil.ParseLingChongGrade,
			["marry_type"] = EmojiTextUtil.ParseMarryType,
			["imp_type"] = EmojiTextUtil.ParseXiaoGui,
			["time_length"] = EmojiTextUtil.ParseXiaoGuiTime,
			["equip_suit_part"] =  EmojiTextUtil.EquipSuitPart,
			["equip_suit"] =  EmojiTextUtil.EquipSuit,
			["role_level"] =  EmojiTextUtil.EquipRoleLevel,
			["rich_image"] = EmojiTextUtil.ParseRichImage,
			["opperside"] = EmojiTextUtil.ParseOpperSide,
			["shengpin_quality_type"] = EmojiTextUtil.ParseShengPinQualityType,
			["percent"] = EmojiTextUtil.ParsePercent,
			["discount_buy"] = EmojiTextUtil.ActDiscount,
			["mingwenge"] = EmojiTextUtil.MingWenGe,
			["noticeid"] = EmojiTextUtil.ParseNoticeId,
			["touzi"] = EmojiTextUtil.ParseTouzi,
			["npdd_sddody"] = EmojiTextUtil.ParseBossAssist,
			["bag_ronglian"] = EmojiTextUtil.ParseBagRongLian,
			["upgrade_skill_id"] = EmojiTextUtil.UpgradeSkillId,
			["keep_kill_title"] = EmojiTextUtil.PraseKillTitle,
			["jingjie_level"] = EmojiTextUtil.UpgradeJingJie,
			["mount_upgrade_skill_id"] = EmojiTextUtil.UpgradeMountSkill,
			["tujian"] = EmojiTextUtil.ParseTuJian,
			["tujian_group"] = EmojiTextUtil.ParseTuJianZuHe,
			["shizhuang_power"] = EmojiTextUtil.ShiZhuangPower,
			["rush_type"] = EmojiTextUtil.OpenServerCompetition, -- 开服比拼
			["time"] = EmojiTextUtil.GuildAnswerTime, -- 答题时间
			["ra_tianshen_xunbao_layer"] = EmojiTextUtil.GodXunBao,
			["wengua_reward_id"] = EmojiTextUtil.WenGua, --问卦

			["huakun_id"] = EmojiTextUtil.ParseKunName, -- 化鲲名字
			["guild_skill"] = EmojiTextUtil.GuildSkillName,--仙盟技能

			["shenshou_id"] = EmojiTextUtil.ParseShenShouName, -- 阵眼名字
			["shenqi_name"] = EmojiTextUtil.TianShenShenQiShenXing,--天神神器升星
			["guild_battle_zone"] = EmojiTextUtil.GuildZoneName,--仙盟战赛区
			["shenqi_waiguan_name"] = EmojiTextUtil.TianShenShenQiWaiguan,--天神神器外观
			["kanjia_name"] = EmojiTextUtil.ParseKanJiaGiftName,--天神神器外观
			["cross3v3_score"] = EmojiTextUtil.ParseCross3v3Score,--跨服3v3积分
			["upquality_grade"] = EmojiTextUtil.EquipUpqualitySucc,
            ["equip_part"] = EmojiTextUtil.EquipPart,
            ["chestshop"] = EmojiTextUtil.ParseTreasureHunt, --新寻宝
            ["join_zhandui"] = EmojiTextUtil.ParseJoinZhanDui, 		--加入战队
            ["zhanduiname"] = EmojiTextUtil.ParseZhanDuiName, 		--加入战队
            ["zero_buy_xiajia"] = EmojiTextUtil.ZeroBuyXiaJiaLink, 		--零元购下架
            ["shenbingawaken_boss"] = EmojiTextUtil.ParseBossJump, 		--神兵觉醒boss跳转
            ["who_see_you"] = EmojiTextUtil.ParseWhoSeeYouRole, 		--被其他玩家查看信息时，自己在线的情况下，系统弹出错误码：XXX正在偷偷打量你 客户端做假传闻
            ["go_to_see"] = EmojiTextUtil.GoToSeeRole,  ---被其他玩家查看信息时，我要查看
			["auction_item_id"] = EmojiTextUtil.ParseAuctionItemId,
			["wardrobe_seq"] = EmojiTextUtil.WardrobeSeq,
			["huanhua_fetter_seq"] =  EmojiTextUtil.HuanhuaFetterSeq,
			["wateringflower"] = EmojiTextUtil.ParseWateringFlower,
            ["daily_treasure_quality"] = EmojiTextUtil.ParseDailyTreasureQuality,
            ["dianfeng_level"] = EmojiTextUtil.ParseDianFengLv, --巅峰等级
			["duck_index"] = EmojiTextUtil.ParseDuckIndex,
			["server_group_seq"] = EmojiTextUtil.ParseServerGroupSeq,
			["cross_server_id"] = EmojiTextUtil.ParseCrossServerID,
			["gather_id"] = EmojiTextUtil.ParseGatherName,
			["client_line"] = EmojiTextUtil.ParseClientLine, 		-- 客户端下划线
			["gather_feng_ling"] = EmojiTextUtil.ParseGatherFengLing, --立即前往
			["gather_point"] = EmojiTextUtil.PraseGatherPos,
			["tiancai_chushen"] = EmojiTextUtil.ParseChuShenItem,		-- 天才厨神
            ["fa_tiancai_chushen"] = EmojiTextUtil.ParseFAChuShenItem,		--节日活动 天才厨神
            ["color_change"] = EmojiTextUtil.ParseColorChange,		--品质颜色亮底色 暗底色转换 
            ["qihun_fuwen"] = EmojiTextUtil.ParseQiHunFuWen, 	-- 器魂符文
			["xiulian_grade"] = EmojiTextUtil.ParseQiHunXL, 			-- 器魂修炼
			["qihun"] = EmojiTextUtil.ParseQiHun, 			-- 器魂修炼
			["boss_convene"] = EmojiTextUtil.ParseBossConvene, --立即前往
			["boss_convene_pos"] = EmojiTextUtil.PraseBossConvenePos,
			["boss_convene_name"] = EmojiTextUtil.PraseBossConveneName,
			["wealth_god_per"] = EmojiTextUtil.ParseWealthGodPer,-- 喜迎财神
			["number"] = EmojiTextUtil.ParseNumber,							-- 数字（解决屏蔽数字导致显示问题）
			["artifact_seq"] = EmojiTextUtil.ParseArtifactNotice,-- 仙魔神器升级
			["artifact_fetter_seq"] = EmojiTextUtil.ParseFetterNotice,-- 仙魔神器羁绊
			["cross_flag_battle_camp"] = EmojiTextUtil.ParseFGBCampNotice, -- 夺旗战场采集获得阵容宝箱提醒 {cross_flag_battle_camp;%d}
			["wuhun_tower_type"] = EmojiTextUtil.ParseWuHunTowerNotice, 	-- 武魂塔传闻 {wuhun_tower_type;%d}
			["area_server_id"] = EmojiTextUtil.CorssServerGroupName,
			["openDyeColor"] = EmojiTextUtil.ParseFashionDyeColor,
			["beast_id"] = EmojiTextUtil.ParseBeastName, -- 幻兽名字
			["cross_boss_strike_monster"] = EmojiTextUtil.ParseCrossBossStrikeMonster,
			["team_common_boss_fb_like_type"] = EmojiTextUtil.TeamCommonBossFbLike,
			["BeastEquipShare"] = EmojiTextUtil.ParseBeastEquipShare,
		}
		local name_cfg = ConfigManager.Instance:GetAutoConfig("randname_auto").random_name[1]
		if not name_cfg then return end
		EmojiTextUtil.first_list = {}
		EmojiTextUtil.last_list = {}

		local the_list_1 = {}
		local the_list_2 = {}
		the_list_1[GameEnum.FEMALE] = name_cfg.common_first
		the_list_2[GameEnum.FEMALE] = name_cfg.female_last
		the_list_1[GameEnum.MALE] = name_cfg.common_first
		the_list_2[GameEnum.MALE] = name_cfg.male_last

		EmojiTextUtil.first_list = the_list_1
		EmojiTextUtil.last_list = the_list_2
	end
end

--巅峰等级
function EmojiTextUtil.ParseDianFengLv(emoji_text, params, font_size, color, rich_content_type, has_bubble)
    local level = tonumber(params[2])
	local string_lv = RoleWGData.GetLevelString(level)
	EmojiTextUtil.AddText(emoji_text, string_lv, color)
end

--战队名字
function EmojiTextUtil.ParseZhanDuiName(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local color
	if has_bubble then
		color = COLOR3B.BLUE
	else
		color = COLOR3B.D_BLUE
	end

	EmojiTextUtil.AddText(emoji_text, params[2], color)
end

--神兵觉醒跳转vipboss
function EmojiTextUtil.ParseBossJump(emoji_text, params, font_size, color, rich_content_type, has_bubble)
    local cur_page = tonumber(params[3])
    local boss_index = tonumber(params[4])
    local boss_lv = tonumber(params[2])
	local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
            local layer, index1 = BossWGData.Instance:GetVipbossidByBossLv(boss_lv)
            BossWGData.Instance:SetBossTuJianIndex(TabIndex.boss_vip, layer, index1)
            ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
		 end, index)
	end
	local color = TianShenJuexingWGData.Instance:GetBossStrColor(cur_page, boss_index)
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, tostring(params[2]), font_size, color, callback, nil, true, rich_content_type) --, has_bubble
end

--加入战队按钮
function EmojiTextUtil.ParseJoinZhanDui(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			local temp_low = tonumber(params[2])
			local temp_high = tonumber(params[3])
			local zhandui_id = MsgAdapter.InitUUID()
			zhandui_id.temp_low = temp_low
			zhandui_id.temp_high = temp_high
			ZhanDuiWGCtrl.Instance:SendApplyToJoinZhanDui(zhandui_id)
		end, index)
	end
	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, Language.ZhanDui.AddZhanDuiLinkBtnName, font_size, nil, callback, nil, true, rich_content_type, has_bubble)
end

function EmojiTextUtil.ParseTreasureHunt( emoji_text, params, font_size, color, rich_content_type )
    local name = Language.TreasureHunt.TreasureNameByMode[tonumber(params[2]) + 1]
	EmojiTextUtil.AddText(emoji_text, name, color)
end

function EmojiTextUtil.WardrobeSeq(emoji_text, params, font_size, color, rich_content_type)
	local suit = tonumber(params[2])
	local theme_cfg = WardrobeWGData.Instance:GetThemeCfgBySuit(suit)
	if IsEmptyTable(theme_cfg) then
		return
	end

	local show_item_id = 0
	local part_cfg = WardrobeWGData.Instance:GetActivationPartList(suit)
	if not IsEmptyTable(part_cfg) then
		for k, v in pairs(part_cfg) do
			if v.icon > 0 then
				show_item_id = WardrobeWGData.Instance:GetPartActItemId(suit, v.part)
			end
		end
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
	local name_color = COLOR3B.DEFAULT
	if item_cfg then
		name_color = ITEM_COLOR_DARK[item_cfg.color] or name_color
	end

	EmojiTextUtil.AddText(emoji_text, ToColorStr(theme_cfg.name, name_color), color)
end

function EmojiTextUtil.HuanhuaFetterSeq(emoji_text, params, font_size, color, rich_content_type)
	local suit = tonumber(params[2])
	local theme_cfg = HuanHuaFetterWGData.Instance:GetThemeCfgBySuit(suit)
	if IsEmptyTable(theme_cfg) then
		return
	end

	local show_item_id = 0
	local part_cfg = HuanHuaFetterWGData.Instance:GetActivationPartList(suit)
	if not IsEmptyTable(part_cfg) then
		for k, v in pairs(part_cfg) do
			if v.icon > 0 then
				show_item_id = HuanHuaFetterWGData.Instance:GetPartActItemId(suit, v.part)
			end
		end
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
	local name_color = COLOR3B.DEFAULT
	if item_cfg then
		name_color = ITEM_COLOR_DARK[item_cfg.color] or name_color
	end

	local cfg = HuanHuaFetterWGData.Instance:GetToggleShowCfg(theme_cfg.label_type, theme_cfg.big_type, theme_cfg.small_type)
	EmojiTextUtil.AddText(emoji_text, ToColorStr(cfg.suit_name, name_color), color)
end

function EmojiTextUtil.CorssServerGroupName(emoji_text, params, font_size, color, rich_content_type)
	local plat_type = tonumber(params[2]) or 0
	local server_id = tonumber(params[3]) or 0

	local name = ""
	if not CrossServerWGData.Instance:GetIsSameCrossServerGroupAsRole(server_id, plat_type) then
		name = ToColorStr(Language.Common.OtherServerGroup, COLOR3B.WHITE)
	end

	EmojiTextUtil.AddText(emoji_text, name, color)
end

-- 解析换色分享
function EmojiTextUtil.ParseFashionDyeColor(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, spec_link_name)
	local link_name = Language.NewAppearanceDye.SchemeColorShareChat
	local link_type = tonumber(params[2]) or 0
	local uid = tonumber(params[3]) or 0

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			-- 直接发送查看其他玩家染色信息协议
			NewAppearanceDyeWGCtrl.Instance:OnReqDyeingOtherInfo(uid)
		end, index)
	end

	local size = font_size or (rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and 20 or 21)
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, size, nil, callback, true, true, rich_content_type, has_bubble)
end

function EmojiTextUtil.ParseAuctionItemId(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local item_id = tonumber(params[2])
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local link_name = Language.Market.IWantBuy
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_buy, "all", {open_param = item_id})
		end, index)
	end
	local size = font_size or (rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and 20 or 21)
	if link_name ~= "" then
		EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, size, nil, callback, true, true, rich_content_type, has_bubble)
	end
end

function EmojiTextUtil.EquipUpqualitySucc(emoji_text, params, font_size, color, rich_content_type)
	local quality = Language.Equip.ColorShengPin_D[math.ceil(params[2] / 10) + 1]
	EmojiTextUtil.AddText(emoji_text, quality)
end

function EmojiTextUtil.EquipPart(emoji_text, params, font_size, color, rich_content_type)
	local item_cfg = ItemWGData.Instance:GetItemConfig(tonumber(params[2]))
	if not item_cfg then return end

	local part = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	EmojiTextUtil.AddText(emoji_text, part)
end

function EmojiTextUtil.PraseKillTitle(emoji_text, params, font_size, color, rich_content_type )
    local title_info = KuafuYeZhanWangChengWGData.Instance:GetRoleTitle(params[2])
    local name = title_info and title_info.title_name or ""
	EmojiTextUtil.AddText(emoji_text, name, color)
end

function EmojiTextUtil.GuildAnswerTime(emoji_text, params, font_size, color, rich_content_type )
    local time = TimeUtil.FormatSecond(tonumber(params[2]), 2)
	time = ToColorStr(time, COLOR3B.D_GLOD)
	EmojiTextUtil.AddText(emoji_text, time, color)
end

function EmojiTextUtil.WenGua(emoji_text, params, font_size, color, rich_content_type )
    local item =  NewXunbaoWGData.Instance:GetGuaItemByRewardId(tonumber(params[2]))
	local cfg = ItemWGData.Instance:GetItemConfig(item.reward_item.item_id)
	local name = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
	EmojiTextUtil.AddText(emoji_text, name, color)
end

function EmojiTextUtil.GodXunBao(emoji_text, params, font_size, color, rich_content_type )
    local layer_info = GodGetRewardWGData.Instance:GetLayerCfgByLayer(tonumber(params[2]))
	EmojiTextUtil.AddText(emoji_text, layer_info.name, color)
end

function EmojiTextUtil.ParseBagRongLian(emoji_text, params, font_size, color, rich_content_type )
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, TabIndex.recharge_vip)
		end, index)
	end
	is_bag_item = false
	local str = params[2]
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, str, font_size, COLOR3B.RED, callback, nil, true, rich_content_type)
end

function EmojiTextUtil.UpgradeSkillId(emoji_text, params, font_size, color, rich_content_type )
	local skill_type = math.floor(params[2] / 10)
	local skill_id = params[2] % 10
	local skill_name = NewAppearanceWGData.Instance:GetAdvancedSkillName(skill_type, skill_id)
	skill_name = ToColorStr(skill_name,COLOR3B.GREEN)
	EmojiTextUtil.AddText(emoji_text, skill_name)
end

--坐骑技能升级
function EmojiTextUtil.UpgradeMountSkill(emoji_text, params)
	local skill_id = params[2] % 10
	local skill_name = NewAppearanceWGData.Instance:GetBaseQiChongSkillName(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, skill_id)
	skill_name = ToColorStr(skill_name, COLOR3B.D_GREEN)
	EmojiTextUtil.AddText(emoji_text, skill_name)
end

-- 图鉴解析
function EmojiTextUtil.ParseTuJian(emoji_text, params, font_size, color, rich_content_type )
	local show_name = ShanHaiJingWGData.Instance:GetTuJianNameBySeq(tonumber(params[2]))
	EmojiTextUtil.AddText(emoji_text, show_name)
end

-- 图鉴羁绊解析
function EmojiTextUtil.ParseTuJianZuHe(emoji_text, params, font_size, color, rich_content_type )
	local show_name, cfg = ShanHaiJingWGData.Instance:GetZuHeNameBySeq(tonumber(params[2]))
	if cfg == nil then
		return
	end
	
	EmojiTextUtil.AddText(emoji_text, show_name, ITEM_COLOR_DARK[cfg.group_color - 1])
end

function EmojiTextUtil.UpgradeJingJie(emoji_text, params, font_size, color, rich_content_type )
	local cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(tonumber(params[2]))
	local show_name = ToColorStr(cfg.name,  JINGJIE_COLOR3B[cfg.name_color])
	EmojiTextUtil.AddText(emoji_text, show_name, color)
end

function EmojiTextUtil.ParseBossAssist(emoji_text, params, font_size, color, rich_content_type )
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			BossWGCtrl.Instance:GoToHelpGuildAssist(params)
		end, index)
	end
	is_bag_item = false
	local str = Language.Chat.BossHelp
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, str, font_size, COLOR3B.BLUE, callback, nil, true, rich_content_type)
end

function EmojiTextUtil.ParseTouzi(emoji_text, params, font_size, color, rich_content_type )
	local dianshu = tonumber(params[2])
	if dianshu == nil then
		return
	end

	local color_param
	if rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
		color_param = "#01AB26"
	else
		color_param = "#7cffb7"
	end
	if 100 == dianshu then
		color_param = "#ff0000"
	elseif dianshu > 80 then
		color_param = "#a335ee"
	elseif dianshu > 50 then
		color_param = "#36c4ff"
	end
	EmojiTextUtil.AddText(emoji_text, dianshu, color_param)
end

function EmojiTextUtil.MingWenGe(emoji_text, params, font_size, color, rich_content_type )
	local str = params[2]
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			FuBenPanelWGCtrl.Instance:Open(TabIndex.fubenpanel_welkin)
		end, index)
	end
	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, str,font_size, color, callback, nil, true, false)
end

function EmojiTextUtil.ActDiscount(emoji_text, params, font_size, color, rich_content_type )
	local name = ActDiscountData.Instance:GetPhaseNameByPhase(tonumber(params[2]))
	local color = COLOR3B.YELLOW
	EmojiTextUtil.AddText(emoji_text, name, color)
	ActDiscountData.Instance:SetJumpIndex(tonumber(params[2]))
end

--升品装备品质
function EmojiTextUtil.ParseShengPinQualityType(emoji_text, params, font_size, color, rich_content_type)
	local quality = tonumber(params[2]) or 0
	local name = Language.Equip.ShengPin[quality] or ""
	local color = params[3] and "#"..params[3] or COLOR3B.WHITE
	EmojiTextUtil.AddText(emoji_text, name, color)
end

--升品装备品质
function EmojiTextUtil.ParsePercent(emoji_text, params, font_size, color, rich_content_type)
	local percent = (tonumber(params[2]) or 0)/100 .. "%"
	local color = params[3] and "#"..params[3] or COLOR3B.WHITE
	EmojiTextUtil.AddText(emoji_text, percent,color)
end

function EmojiTextUtil.EquipRoleLevel(emoji_text, params, font_size, color, rich_content_type)
	local level_max = tonumber(params[2])
	-- local x = 1
	local level_str = RoleWGData.GetLevelStringImg(level_max)
	if nil ~= level_str then
		EmojiTextUtil.AddText(emoji_text, level_str, color)
	end
end

local Equip_Suit_OrderList = {}


-- 已经不存在多套套装概念, 现在只有一个套装
function EmojiTextUtil.EquipSuit(emoji_text, params, font_size, color, rich_content_type)
	local color_list = {
		[0] = COLOR3B.D_PURPLE,
		COLOR3B.D_ORANGE,
		COLOR3B.D_RED,
	}

	local equip_body_index = tonumber(params[3]) or 0
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
	local suit_name = EquipmentWGData.Instance:GetEquipmenSuitTitleName(equip_body_seq, equip_type)
	EmojiTextUtil.AddText(emoji_text, suit_name, color_list[2])

	-- local suit_index = tonumber(params[2]) or -1
	-- local part = tonumber(params[3]) or 0
	-- local suit_order = tonumber(params[4]) or 0
	-- local text_color = color_list[suit_index] or COLOR3B.WHITE
	-- local suit_index_name = Language.Equip.TabSub3[suit_index + 1] or ""
	-- local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part)
	-- local suit_name = ""
	-- if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 	suit_name = Language.Equip.XianQiSuit
	-- else
	-- 	suit_name = EquipmentWGData.Instance:GetEquipmenSuitName(suit_order)
	-- end
	-- local suit_str = string.format("【%s】%s", suit_index_name, suit_name)
	-- EmojiTextUtil.AddText(emoji_text, suit_str, text_color)
end


function EmojiTextUtil.EquipSuitPart(emoji_text, params, font_size, color, rich_content_type)
	local color_list = {
		[0] = COLOR3B.D_PURPLE,
		COLOR3B.D_ORANGE,
		COLOR3B.D_RED,
	}

	local suit_index = tonumber(params[2]) or -1
	local item_id = tonumber(params[3]) or 0
	local text_color = color_list[suit_index] or COLOR3B.WHITE
	local suit_index_name = Language.Equip.TabSub3[suit_index + 1] or ""
	local item_name = ItemWGData.Instance:GetItemName(item_id)
	local suit_str = string.format("【%s·%s】", suit_index_name, item_name)
	EmojiTextUtil.AddText(emoji_text, suit_str, text_color)
end


function EmojiTextUtil.SkillId(emoji_text, params, font_size, color, rich_content_type)
	local skill_id = tonumber(params[2]) or 0
	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
	if nil ~= skill_cfg and nil ~= skill_cfg.skill_name then
		EmojiTextUtil.AddText(emoji_text, skill_cfg.skill_name, color)
	end
end

function EmojiTextUtil.ShizhuangType(emoji_text, params, font_size, color, rich_content_type)
	local part_type = tonumber(params[2]) or 0

	local image_name = Language.NewAppearance.AppearanceTypeName[part_type] or ""
	EmojiTextUtil.AddText(emoji_text, image_name, color)
end

function EmojiTextUtil.ShizhuangIndex(emoji_text, params, font_size, color, rich_content_type)
	local part_type = tonumber(params[2]) or 0
	local index = tonumber(params[3]) or 0

	local image_name = ""
	local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index)
	if image_cfg ~= nil then
		local item_id = NewAppearanceWGData.Instance:GetFashionItemId(part_type, index)
		local data = ItemWGData.Instance:GetItemConfig(item_id)
		if data then
			color = ITEM_TIP_D_COLOR[data.color]
		end
		image_name = image_cfg.name
	end

	EmojiTextUtil.AddText(emoji_text, image_name, color)
end

function EmojiTextUtil.ShiZhuangPower(emoji_text, params, font_size, color, rich_content_type)
	local power = params[2]
	color = COLOR3B.D_GREEN
	EmojiTextUtil.AddText(emoji_text, power, color)
end

-- 只进行开服冲榜文本替换
function EmojiTextUtil.OpenServerCompetition(emoji_text, params, font_size, color, rich_content_type)
	local rushtype = tonumber(params[2])
	if rushtype then
		local act_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rushtype)
		if act_cfg then
			EmojiTextUtil.AddText(emoji_text, act_cfg.show_title, color)
		end
	end
end

function EmojiTextUtil.GuildSkillName(emoji_text, params, font_size, color, rich_content_type)
	local skill_id = tonumber(params[2]) or 0
	local guild_skill_name = ""
	local cfg = GuildWGData.Instance:GetCurOneSkillCfg( skill_id )
	if cfg then
		guild_skill_name = cfg.skill_name
	end
	local color =  COLOR3B.PINK
	EmojiTextUtil.AddText(emoji_text, guild_skill_name, color)
end

function EmojiTextUtil.ParseKunName(emoji_text, params, font_size, color, rich_content_type)
	local kun_id = tonumber(params[2]) or 0
	local kun_name = ""
	local cfg = NewAppearanceWGData.Instance:GetKunActCfgById( kun_id )
	if cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.active_need_item_id)
		if item_cfg then
			color = ITEM_COLOR_DARK[item_cfg.color]
			kun_name = item_cfg.name
		end
	end

	EmojiTextUtil.AddText(emoji_text, kun_name, color)
end

function EmojiTextUtil.ParseShenShouName(emoji_text, params, font_size, color, rich_content_type)
	local shenshou_id = tonumber(params[2]) or 0
	local shenshou_name = ""
	local cfg = ShenShouWGData.Instance:GetShenShouCfg( shenshou_id )
	if cfg then
		shenshou_name = cfg.name -- '【' .. ToColorStr(cfg.name,ITEM_COLOR[cfg.series+1])   .. '】'
	end
	EmojiTextUtil.AddText(emoji_text, shenshou_name, ITEM_COLOR[cfg.series+1])
end

function EmojiTextUtil.ParseCross3v3Score(emoji_text, params, font_size, color, rich_content_type)
	local score = tonumber(params[2]) or 0
	local duanwei = ""
	local grade_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(score)
	if grade_cfg then
		duanwei = grade_cfg.name
	end
	EmojiTextUtil.AddText(emoji_text, duanwei, color)
end

function EmojiTextUtil.ParseKanJiaGiftName(emoji_text, params, font_size, color, rich_content_type)
	local gift_id = tonumber(params[2]) or 0
	local gift_name = ""
	local cfg = BossAssistWGData.Instance:GetKanJiaItemInfo(gift_id)
	if cfg then
		gift_name = cfg.libao_name
	end
	local color = ITEM_COLOR[cfg.color]
	EmojiTextUtil.AddText(emoji_text, gift_name, color)
end

function EmojiTextUtil.LingChongName(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local image_name, color = NewAppearanceWGData.Instance:GetQiChongSpecialImageName(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, index)
	if nil ~= image_name then
		EmojiTextUtil.AddText(emoji_text, image_name, color)
	end
end

function EmojiTextUtil.FootName(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(index)
	local color = ITEM_COLOR[7]
	if nil ~= cfg then
		EmojiTextUtil.AddText(emoji_text, cfg.name, color)
	end
end

function EmojiTextUtil.WaistName(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(index)
	color = ITEM_COLOR[7]
	if nil ~= cfg then
		EmojiTextUtil.AddText(emoji_text, cfg.name, color)
	end
end

function EmojiTextUtil.MountName(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local image_name, color = NewAppearanceWGData.Instance:GetQiChongSpecialImageName(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, index)
	if nil ~= image_name then
		EmojiTextUtil.AddText(emoji_text, image_name, color)
	end
end

function EmojiTextUtil.MountLingChongName(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local image_name, color = NewAppearanceWGData.Instance:GetQiChongSpecialImageName(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, index)
	if nil ~= image_name then
		EmojiTextUtil.AddText(emoji_text, image_name, color)
	end
end

function EmojiTextUtil.ParseWordColorAndOutline(emoji_text, params, font_size, color, rich_content_type)
	if #params < 4 then return end

	local color_str
	if 7 == string.len(params[2]) then
		color_str = string.sub(params[2], 2, -1)
	else
		color_str = params[2]
	end

	local outline_color
	if 7 <= string.len(params[3]) then
		outline_color = string.sub(params[3], 2, -1)
	else
		outline_color = params[3]
	end
	local text_attr = {}
	text_attr.outline_size = text_attr.outline_size or 1
	text_attr.outline_color = outline_color

	if 6 == string.len(color_str) then
		EmojiTextUtil.AddText(emoji_text, params[4])--, Str2C3b(color_str), text_attr)
	end
end

function EmojiTextUtil.TianShenShenQiShenXing(rich_text, params, font_size, color, rich_content_type)-- ignored_link, text_attr, bigchatface_status)
	local tianshen_index = tonumber(params[2]) or 0
	local cfg = TianShenWGData.Instance:GetShenQiByIndex(tianshen_index)
	local str = string.format(Language.TianShen.TianShenNameShowColor,cfg.name)
	EmojiTextUtil.AddText(rich_text, str, color)
end

function EmojiTextUtil.TianShenShenQiWaiguan(rich_text, params, font_size, color, rich_content_type)-- ignored_link, text_attr, bigchatface_status)
	local waiguan_index = tonumber(params[2]) or 0
	local cfg = TianShenWGData.Instance:GetWaiGuanCfg(waiguan_index)

	EmojiTextUtil.AddText(rich_text, cfg.name, color)
end

function EmojiTextUtil.GuildZoneName(rich_text, params, font_size, color, rich_content_type)
	local zone_index = tonumber(params[2]) or 0
	local str = Language.Guild.GuildZone[zone_index]
	EmojiTextUtil.AddText(rich_text, str, color)
end

function EmojiTextUtil.ParseFbSceneId(emoji_text, params, font_size, color, rich_content_type,has_bubble)
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(tonumber(params[2]))
    if scene_cfg == nil then return end
    color = COLOR3B.DEFAULT_NUM
    if rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW and has_bubble then
        color = COLOR3B.DEFAULT_NUM
    end
	EmojiTextUtil.AddText(emoji_text, scene_cfg.name, color)
end

function EmojiTextUtil.ParseWorldBossInfo(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, link_name)
	if #params < 3 then return end
	 local enum = {
            [BossWGData.BossOpenType.WORLD_BOSS] = TabIndex.boss_world,
            [BossWGData.BossOpenType.VIP_BOSS] = TabIndex.boss_vip,
            [BossWGData.BossOpenType.PERSON_BOSS] = TabIndex.boss_personal,
            [BossWGData.BossOpenType.DABAO_BOSS] = TabIndex.boss_dabao,
            [BossWGData.BossOpenType.KF_BOSS] = TabIndex.worserv_boss_mh,
            [BossWGData.BossOpenType.SG_BOSS] = TabIndex.worserv_boss_sgyj,
            --[BossWGData.BossOpenType.HMSY_BOSS] = TabIndex.worserv_boss_hmsy,
        }
	local boss_type = tonumber(params[2]) or 0
	local boss_layer = tonumber(params[3]) or 1
	local boss_id = tonumber(params[4]) or 0

	--local tujian_cfg = BossWGData.Instance:GetTuJianCfg(boss_id)
	local boss_data = BossWGData.Instance:GetBossInfoByBossId(boss_id)
	if not boss_data then
		print_log("找不到boss配置，boss_id=", boss_id)
		return
	end

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
            --暂时使用洛书图鉴的配置
            if boss_type ~= BossWGData.BossOpenType.KF_BOSS then
            	local off = boss_type == BossWGData.BossOpenType.VIP_BOSS and 1 or 0
                BossWGData.Instance:SetBossJumpInfo(enum[boss_data.boss_type], boss_data.layer + off, boss_data.boss_view_index)
                FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, enum[boss_data.boss_type])
            else
                BossWGData.Instance:SetBossTuJianIndex(enum[boss_data.boss_type], boss_data.layer, boss_data.index)
                FunOpen.Instance:OpenViewByName(GuideModuleName.WorldServer, enum[boss_data.boss_type])
            end

			--BossWGData.Instance:SetBossTuJianIndex(tujian_cfg.boss_type, tujian_cfg.tier, tujian_cfg.location)
		end, index)
	end
	is_bag_item = false

	local size = font_size or (rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and 20 or 22)
	link_name = link_name or Language.Boss.GoKing
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, size, nil, callback, true, true, rich_content_type)
end

function EmojiTextUtil.ParseShowPos(emoji_text, params, font_size, color, rich_content_type)
end

function EmojiTextUtil.UpgradeType(emoji_text, params, font_size, color, rich_content_type)
	local view_type = tonumber(params[2]) or 0
	local name = nil
	if -1 ~= view_type then
		name = Language.NewAppearance.AdvancedTypeName[view_type] or ""
	end
	if nil ~= name then
		EmojiTextUtil.AddText(emoji_text, name, color)
	end
end

function EmojiTextUtil.CrossTeamFb(emoji_text, params, font_size, color, rich_content_type)
	local layer = tonumber(params[2]) or 0
	-- local name = TeamfbData.Instance:GetNameLevelByLayer(layer)
	-- if nil ~= name then
	-- 	EmojiTextUtil.AddText(emoji_text, name, COLOR3B.GREEN, text_attr)
	-- end
end

-- 解析空战数据
function EmojiTextUtil.ParseCrossAirWarMonster(emoji_text, params, font_size, color, rich_content_type)
	local seq = tonumber(params[2]) or 0
	local monster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(seq)
	if monster_cfg then
		EmojiTextUtil.AddText(emoji_text, monster_cfg and monster_cfg.stage_name or "")
	end
end

-- 解析空战数据
function EmojiTextUtil.ParseCrossAirWarAuction(emoji_text, params, font_size, color, rich_content_type)
	local auction_grade = tonumber(params[2]) or 0
	local auction_round = tonumber(params[3]) or 0
	local auction_round_daxie = CommonDataManager.GetDaXie(auction_round)
	EmojiTextUtil.AddText(emoji_text, auction_round_daxie)
end

-- 解析神龙来袭
function EmojiTextUtil.ParseCrossBossStrikeMonster(emoji_text, params, font_size, color, rich_content_type)
	local group_index = tonumber(params[2]) or 0
	local color = tonumber(params[3]) or 0
	local boss_color_cfg = BOSSInvasionWGData.Instance:GetBossColorCfg(group_index, color)
	
	if boss_color_cfg then
		EmojiTextUtil.AddText(emoji_text, boss_color_cfg and boss_color_cfg. color_name or "")
	end
end

function EmojiTextUtil.TeamCommonBossFbLike(emoji_text, params, font_size, color, rich_content_type)
	local role_name = params[2] or ""
	local like_type = tonumber(params[3]) or 1
	local text_content = string.format(Language.FuBenTeamCommonBoss.LikeDesc2[like_type], role_name) 
	local color = COLOR3B.WHITE
	EmojiTextUtil.AddText(emoji_text, text_content)
end
function EmojiTextUtil.ParseGameName(emoji_text, params, font_size, color, rich_content_type)
	EmojiTextUtil.AddText(emoji_text, CommonDataManager.GetGameName(), color)
end

-- 解析幻兽内丹分享
function EmojiTextUtil.ParseBeastEquipShare(emoji_text, params, font_size, color, rich_content_type)
	local equip_str_list = string.split(params[2], ",")
	local equip_item_id = tonumber(equip_str_list[1]) or 1
	local equip_slot_lv = tonumber(equip_str_list[2]) or 1
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(equip_item_id)

	if nil == item_cfg then
		return
	end

	local equip_info = {}
	equip_info.item_id = equip_item_id
	equip_info.index = 0
	equip_info.words_list = {}

	if params[3] ~= nil and params[3] ~= "" then
		local start_index = 0
		local words_str_list = string.split(params[3], ",")
		for i, v in ipairs(words_str_list) do
			equip_info.words_list[start_index] = {}
			equip_info.words_list[start_index].words_seq = tonumber(v) or 1
			equip_info.words_list[start_index].can_replace_words = tonumber(v) or 1
			start_index = start_index + 1
		end
	end

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(index)
			local item_data = {
				item_id = equip_item_id, 
				equip_info = equip_info, 
				is_bag_equip = true,
			}

			TipWGCtrl.Instance:OpenItem(item_data, ItemTip.BEAST_ALCHEMY_EQUIP_BAG)
		end, index)
	end
	color = ITEM_COLOR_LIGHT[item_cfg.color]
	local name_str = "["..item_cfg.name.."]"
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, name_str, font_size, color, callback, nil, false, rich_content_type)
end

function EmojiTextUtil.ParseFace(emoji_text, params, font_size, color, rich_content_type)
	if nil == emoji_text then
		return
	end
	local face_id = tonumber(params[2]) or 0
	local img_path_num = 0
	if face_id >= 1 and face_id <= 40 then
		face_id = face_id + 99
		face_id = string.format("%03d", face_id)
		EmojiTextUtil.CreateBigFace(emoji_text, face_id)
	elseif face_id >= COMMON_CONSTS.BIGCHAT_TESHUFACE_ID_FIRST and face_id <= COMMON_CONSTS.BIGCHAT_TESHUFACE_ID_LAST then
		face_id = face_id - 100
		face_id = string.format("%03d", face_id)
		EmojiTextUtil.CreateBigFace(emoji_text, face_id)
	end
end

-- 添加动态表情
function EmojiTextUtil.CreateBigFace(emoji_text, index)
	local emoji_str = EmojiTextUtil.ToEmojiStr(index)
	EmojiTextUtil.AddText(emoji_text, emoji_str)
end


--params //2 scene_name //3 x //4 y  //5 scene_id //6 plat_name //7 current_server_id //8 plat_type //9是否是跨服频道
function EmojiTextUtil.ParsePoint(emoji_text, params, font_size, color, rich_content_type,has_bubble)
	if #params < 5 then return end

    local is_cross_channel = tonumber(params[9]) == 1 --是跨服频道

    local scene_id = tonumber(params[5])
    local scene_config = ConfigManager.Instance:GetSceneConfig(scene_id)
    local target_scene_type = nil
    if scene_config then
        target_scene_type = scene_config.scene_type
    end

    local point_text = params[2] .. "(" .. params[3] .. "," .. params[4] .. ")"
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			--护送拦截
			if YunbiaoWGData.Instance:GetIsHuShong() then
				TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
				return
            end

            local main_role = Scene.Instance:GetMainRole()
            if main_role ~= nil and not main_role:IsDeleted() then
                if main_role:IsInXunYou() then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
                    return
                end
            end
            if is_cross_channel then --跨服频道下，深渊和蛮荒神兽，寻宝boss，走通用
                if target_scene_type == SceneType.Shenyuan_boss or target_scene_type == SceneType.KF_BOSS
                or target_scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS or target_scene_type == SceneType.XianJie_Boss then
                    HandleClickPoint.CheckSceneIdOperate(scene_id, {params[2],scene_id,tonumber(params[3]),tonumber(params[4])})
                    return
                end
            end
         
			-- if params[8] then --只有是主城的坐标

				
            --     local plat_type = tonumber(params[8])
            --     local server_id = tonumber(params[7])
            --     local mainrole_cur_plat_type = RoleWGData.Instance:GetCurPlatType()
            --     local mainrole_cur_server_id = RoleWGData.Instance:GetCurServerId()
            --     if RoleWGData.Instance:GetPlatType() ~= plat_type or RoleWGData.Instance:GetMergeServerId() ~= server_id then --自己的服相对于坐标的服是跨服
            --         CountryMapWGCtrl.Instance:SendCrossEnterOtherGs(plat_type, server_id, CROSS_ENTER_OTHER_GS_KEY.DOOR, function()
			-- 			GuajiWGCtrl.Instance:MoveToPos(scene_id, tonumber(params[3]), tonumber(params[4]), 0)
			-- 		end)
            --     else
            --         HandleClickPoint.CheckSceneIdOperate(scene_id, {params[2],scene_id,tonumber(params[3]),tonumber(params[4])})
            --     end
            -- else --不是主城坐标，肯定是自己服的坐标     
            --     HandleClickPoint.CheckSceneIdOperate(scene_id, {params[2],scene_id,tonumber(params[3]),tonumber(params[4])})
            -- end
			HandleClickPoint.CheckSceneIdOperate(scene_id, {params[2],scene_id,tonumber(params[3]),tonumber(params[4])})

		end, index)
	end
	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, point_text,font_size, nil, callback, nil, true, rich_content_type, has_bubble)
end

function EmojiTextUtil.ParseHelpPoint(emoji_text, params, font_size, color, rich_content_type)
	if #params < 6 or tonumber(params[6]) == RoleWGData.Instance.role_vo.role_id then return end

	local point_text = params[2] .. "<color=#01AB26FF>(" .. params[3] .. "," .. params[4] .. ")</color>"
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			GuajiWGCtrl.Instance:FlyToScenePos(tonumber(params[5]) or 0, tonumber(params[3]) or 0,  tonumber(params[4]) or 0, false)
		end, index)
	end
	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, point_text,font_size, color, callback, nil, true, rich_content_type)
end

-- 同场景内传送
function EmojiTextUtil.ParsePosition(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local scene_id = tonumber(params[2])
	local x = tonumber(params[3])
	local y = tonumber(params[4])
	local point_text = "(" .. x .. "," .. y .. ")"
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y)
		end, index)
	end
	is_bag_item = false
	color = COLOR3B.D_GREEN
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, point_text,font_size, color, callback, nil, true, rich_content_type)
end

function EmojiTextUtil.ParseScene(emoji_text, params, font_size, color, rich_content_type)

end

function EmojiTextUtil.ParseSceneId(emoji_text, params, font_size, color, rich_content_type)
	if #params < 2 then return end
	local scene_id = tonumber(params[2])
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if nil == scene_cfg then
		return
    end
	EmojiTextUtil.AddText(emoji_text, scene_cfg.name,color)
end

function EmojiTextUtil.ParseNewLine(emoji_text, params, font_size, color, rich_content_type)
	EmojiTextUtil.AddText(emoji_text, "\n", color)
end

function EmojiTextUtil.ShenShouComposeId(emoji_text, params, font_size, color, rich_content_type)
	local item_id = tonumber(params[2]) or 0
	local star_count = tonumber(params[4]) or 0
	local item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(item_id)
	local attr_list = string.split(params[3],":")
	for i=1,#attr_list do
		attr_list[i] = {attr_type = tonumber(attr_list[i])}
		local random_cfg = ShenShouWGData.Instance:GetRandomAttrCfg(item_cfg.quality, attr_list[i].attr_type)
		attr_list[i].attr_value = random_cfg and random_cfg.attr_value or 0
	end

	if nil == item_cfg then
		return
	end
	if nil == params[3] then return end
	local spc_attr_list = Split(params[3], ":")
	color = ITEM_TIP_COLOR[item_cfg.quality+1] or COLOR3B.WHITE

	EmojiTextUtil.CreateUnderLineBtn(emoji_text,star_count .. Language.Common.Star .. "【"..item_cfg.name.."】", font_size, color, function (index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function ()
			ShenShouWGCtrl.Instance:OpenShenShouEquipTip({item_id = item_id,star_count = star_count
				,attr_list = attr_list}, ShenShouEquipTip.FROM_LINK) --
		end, index)
	end,nil, false)
end

--{eq_compose;%d;%d;%s}
--合成专用
function EmojiTextUtil.ComposeStar(emoji_text, params, font_size, color, rich_content_type )
	local item_id = tonumber(params[3]) or 0
	local star_level = tonumber(params[2]) or 0 --星级

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if nil == item_cfg then
		return
	end
	color = ITEM_COLOR_DARK[item_cfg.color] or COLOR3B.WHITE

	local btn_bg_path = "lucency_bg"	--策划要求把原来那个底色去掉，换成了一个透明的底

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			local item_data = CommonStruct.ItemDataWrapper()
			item_data.item_id = item_id
			if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and nil ~= params[4] then

				local item_param = Split(params[4], ":")
				item_data.frombody = false
				item_data.has_param = 1                                                    --装备有附加属性
				item_data.param.star_level = tonumber(item_param[4] or 0)                	--星级
				item_data.param.strengthen_level = tonumber(item_param[5] or 0)            --强化等级

				item_data.param.baoshi_t = {}                                            --宝石镶嵌
				for i = 1, GameEnum.MAX_STONE_COUNT do
					local vo = {}
					vo.item_id = tonumber(item_param[5 + i] or 0)
					item_data.param.baoshi_t[i - 1] = vo
				end

				item_data.param.stone_baptize_level = tonumber(item_param[12] or 0)          --宝石精炼等级
				item_data.param.suit_index = (tonumber(item_param[13]) or -1) + 1
				item_data.param.suit_open_num = tonumber(item_param[14] or 0)
				item_data.param.xianpin_type_list = {}                                       --仙品属性
				item_data.invalid_time = -1
				for i = 1, GameEnum.MAX_XIANPIN_COUNT do
					local xianpin_type = tonumber(item_param[14 + i] or 0)
					if xianpin_type > 0 then
						table.insert(item_data.param.xianpin_type_list, xianpin_type)
					end
				end


				local info_type = item_param[1]
				if info_type == 1 or info_type == 4 or info_type == 6 then   --1 装备洗炼 2 装备合成 3 装备寻宝 4 装备强化 6 帮派仓库
					TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROME_BROWSE_ROLE)              --可能为装备合成锻造传闻
				else
					TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_CHATEXT)
				end
			elseif nil == params[4] then
				TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_WORLD)--FROM_WORLD                     --可能为掉落寻宝传闻
			end
		end, index)
	end
	is_bag_item = true
	local name_str = star_level .. Language.Common.Star ..  "["..item_cfg.name.."]"
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, name_str, font_size, color, callback, nil, false, rich_content_type)
end

function EmojiTextUtil.ParseTianShenItem(emoji_text, params, font_size, color, rich_content_type)
	local item_id = tonumber(params[2]) or 0
	local bag_index = tonumber(params[3]) or 0

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if nil == item_cfg then
		return
	end

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(index)
			local item_data = TianShenWGData.Instance:GetBagInfoByIndex(bag_index)
			TipWGCtrl.Instance:OpenItem(item_data)
		end, index)
	end
	color = ITEM_COLOR_DARK[item_cfg.color]
	local name_str = "["..item_cfg.name.."]"
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, name_str, font_size, color, callback, nil, false, rich_content_type)
end

function EmojiTextUtil.ParseItem(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, link_name, is_light_bubble)
	local item_id = tonumber(params[2]) or 0
	local is_bind = tonumber(params[4]) or 0

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if nil == item_cfg then
		return
	end

	if rich_content_type == RICH_CONTENT_TYPE.MAIN_UI then
		color = ITEM_COLOR_DARK[item_cfg.color]
	elseif rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW and not has_bubble then
		color = ITEM_COLOR_DARK[item_cfg.color]
	elseif rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW and has_bubble then
        color = ITEM_COLOR[item_cfg.color]

		if is_light_bubble then
			color = ITEM_COLOR_LIGHT[item_cfg.color]
		end
    elseif rich_content_type == RICH_CONTENT_TYPE.ParseSystemTips then
		color = ITEM_COLOR_DARK[item_cfg.color]
	else
		color = ITEM_COLOR[item_cfg.color]
	end
	local btn_bg_path = "lucency_bg"	--策划要求把原来那个底色去掉，换成了一个透明的底

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(index)
			local item_data = CommonStruct.ItemDataWrapper()
			item_data.item_id = item_id
			item_data.is_bind = is_bind
			if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and nil ~= params[3] then
				local item_param = Split(params[3], ":")
				item_data.frombody = false
				item_data.has_param = 1                                                    --装备有附加属性
				item_data.param.star_level = tonumber(item_param[4] or 0)                   --星级
				item_data.param.strengthen_level = tonumber(item_param[5]or 0)            --强化等级
				item_data.param.baptize_list = {}                                          --洗炼

				item_data.param.baoshi_t = {}                                            --宝石镶嵌
				for i = 1, GameEnum.MAX_STONE_COUNT do
					local vo = {}
					vo.item_id = tonumber(item_param[5 + i] or 0)
					item_data.param.baoshi_t[i - 1] = vo
				end

				item_data.param.stone_baptize_level = tonumber(item_param[12] or 0)          --宝石精炼等级
				item_data.param.suit_index = (tonumber(item_param[13]) or -1) + 1
				item_data.param.suit_open_num = tonumber(item_param[14] or 0)
				item_data.param.xianpin_type_list = {}                                       --仙品属性
				item_data.invalid_time = -1
				for i = 1, GameEnum.MAX_XIANPIN_COUNT do
					local xianpin_type = tonumber(item_param[14 + i] or 0)
					if xianpin_type > 0 then
						table.insert(item_data.param.xianpin_type_list, xianpin_type)
					end
				end

				local info_type = tonumber(item_param[1])
				if info_type == EQUIPMENT_BOARDCAST_TYPE.EQUIPMENT_BOARDCAST_TYPE_BAPTIZE or
					info_type == EQUIPMENT_BOARDCAST_TYPE.EQUIPMENT_BOARDCAST_TYPE_STRENGTH then   --1 装备洗炼 2 装备合成 3 装备寻宝 4 装备强化 6 帮派仓库
					-- info_type == EQUIPMENT_BOARDCAST_TYPE.EQUIPMENT_BOARDCAST_TYPE_GUILD_PUTITEM

					TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROME_BROWSE_ROLE)              --可能为装备合成锻造传闻

				-- elseif info_type == EQUIPMENT_BOARDCAST_TYPE.EQUIPMENT_BOARDCAST_TYPE_MAX then 								--仙器真炼
				-- 	item_data.param.is_refine = 1
				-- 	TipWGCtrl.Instance:OpenItem(item_data)
				else
					TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_CHATEXT)  --改成这个是为了根据星级 显示仙品属性
				end

			elseif "" == params[3] then
				TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_WORLD)--FROM_WORLD                     --可能为掉落寻宝传闻
			else
				TipWGCtrl.Instance:OpenItem(item_data)
			end
		end, index)
	end

	is_bag_item = true
	local name_str = "[".. item_cfg.name .."]"
	if emoji_text == nil then
		EmojiTextUtil.AddText(emoji_text, name_str, color)
	else
		EmojiTextUtil.CreateUnderLineBtn(emoji_text, name_str, font_size, color, callback, nil, false, rich_content_type)
	end
end

function EmojiTextUtil.ParseNoticeId(emoji_text, params, font_size, color, rich_content_type)
	if nil == emoji_text then
		return
	end
	local notice_list = ConfigManager.Instance:GetAutoConfig("notice_new_auto").notice_list
	local cfg = notice_list and notice_list[tonumber(params[2])]
	if not cfg then return end
	local name_str =  params[3]
	local callback = function (index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			FunOpen.Instance:OpenViewNameByCfg(cfg.open_panel_name)
		end, index)
	end
	EmojiTextUtil.CreateUnderLineBtn(emoji_text, name_str, font_size, COLOR3B.GREEN, callback, nil, true, rich_content_type)
end

--  {rich_image;频道类型}    --解析聊天频道图片
function EmojiTextUtil.ParseRichImage(emoji_text, params, font_size, color, rich_content_type)

end

function EmojiTextUtil.ParseClientLine(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then
		return
	end

	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, params[2], font_size, params[3], nil, nil, true, rich_content_type)
end


function EmojiTextUtil.CreateUnderLineBtn(emoji_text, name, font_size, color, callback, btn_bg_path, is_show_line, rich_content_type)
	if nil == emoji_text then
		return
	end
	color = color or EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type)
	if is_not_target then
		name = EmojiTextUtil.ToUnderLineStr(name)
	else
		EmojiTextUtil.ButtonIndex = EmojiTextUtil.ButtonIndex + 1
		name = EmojiTextUtil.ToButtonStr(name)
	end

	local button_name = ToColorStr(name, color)
	EmojiTextUtil.AddText(emoji_text, button_name)
	if callback then
		callback(EmojiTextUtil.ButtonIndex)
	end
end

--添加下划线按钮(上面那个不是添加下划线的,这个才是)
function EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, name, font_size, color, callback, btn_bg_path, is_show_line, rich_content_type, has_qipao)
	if nil == emoji_text then
		return
	end
	color = color or EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, has_qipao)
	EmojiTextUtil.ButtonIndexLine = EmojiTextUtil.ButtonIndexLine + 1
	name = EmojiTextUtil.ToButtonStrIndex(name)
	local button_name = name
	if color ~= nil then
		button_name = ToColorStr(name, color)
	end
	EmojiTextUtil.AddText(emoji_text, button_name)

	if callback then
		callback(EmojiTextUtil.ButtonIndexLine)
	end
end


function EmojiTextUtil.GetLineSizeByContentType( rich_content_type )
	if rich_content_type == RICH_CONTENT_TYPE.MAIN_UI then
		return 16
	elseif rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
		return 17
	end
	return 17
end

function EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, has_qipao)
	if rich_content_type == RICH_CONTENT_TYPE.MAIN_UI then
		return COLOR3B.DEFAULT_NUM
	elseif rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
		if has_qipao then
			return F2ChatTextColor[10]
		end
		return F2ChatTextColor[7]
	elseif rich_content_type == RICH_CONTENT_TYPE.Mail then
		return COLOR3B.GREEN
	elseif rich_content_type == RICH_CONTENT_TYPE.BagRongLian then
		return nil
	elseif rich_content_type == RICH_CONTENT_TYPE.ParseRoleColor then
		return COLOR3B.PURPLE
	elseif rich_content_type == RICH_CONTENT_TYPE.ParseZhanDuiRoleColor then
		return COLOR3B.D_PINK	--战队记录角色名字颜色
	end

	return F2ChatTextColor[7]
end

function EmojiTextUtil.GetUnderLineColorByContentType( rich_content_type )
	if rich_content_type == RICH_CONTENT_TYPE.MAIN_UI then
		return COLOR3B.DEFAULT_NUM
	elseif rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
		return F2ChatTextColor[7]
	elseif rich_content_type == RICH_CONTENT_TYPE.Mail then
		return nil
	elseif rich_content_type == RICH_CONTENT_TYPE.BagRongLian then
		return nil
	end
	return F2ChatTextColor[7]
end

function EmojiTextUtil.ParseCamp(emoji_text, params, font_size, color, rich_content_type)
	if nil ~= params[2] then
		local camp = tonumber(params[2])
		EmojiTextUtil.AddText(emoji_text, Language.Common.CampName[camp], CAMP_COLOR3B[camp])
	end
end

function EmojiTextUtil.ParseRole(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(x, y)
            local role_id = tonumber(params[2])
            if role_id == RoleWGData.Instance:InCrossGetOriginUid() then
                return
            end

			BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
                ChatWGCtrl.Instance:CreateRoleHeadCell(param_protocol.role_id, params[3], param_protocol.prof, param_protocol.sex,
                param_protocol.is_online, Vector2(x,y), param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
			end)
		end, index)
	end

	if nil ~= params[3] then
		if params[3] == "NULL" then
			-- 随机名
			local rand_num = math.floor(math.random(1, 200))
			params[3] = CommonDataManager.GetRandomName(rand_num)
		end
		
		local str = params[3]
		local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
	    local name_list = Split(str, "_")
	    local name_str = ""
		if not IsEmptyTable(name_list) then
			if not is_cross_server_stage then 
				name_str = "【"..name_list[1].."】"
			else
				if name_list[2] then
					local has_s = string.find(name_list[2],"s")
					if has_s then
						name_str = string.format(Language.BiZuo.ServerName_2,name_list[2],name_list[1])
					else
						name_str = string.format(Language.BiZuo.ServerName_1,name_list[2],name_list[1])
					end
				else
					name_str = "【"..name_list[1].."】"
				end
			end
		end

		rich_content_type = rich_content_type or RICH_CONTENT_TYPE.ParseRoleColor
		if rich_content_type == RICH_CONTENT_TYPE.NONE or rich_content_type == RICH_CONTENT_TYPE.MAIN_UI or rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
			color = has_bubble and COLOR3B.L_GREEN or F2ChatTextColor[0]
		elseif rich_content_type == RICH_CONTENT_TYPE.ParseZhanDuiRoleColor then
			color = nil
		end

		EmojiTextUtil.CreateUnderLineBtn(emoji_text, name_str, font_size, color, callback, nil, false, rich_content_type)
	end
end

function EmojiTextUtil.ParseRandName(emoji_text, params, font_size, color, rich_content_type)
	local sex = math.random(0, 1)
	local count = 10
	local first_list = EmojiTextUtil.first_list[sex]
	local last_list = EmojiTextUtil.last_list[sex]
	local name, first_index, last_index, isill = "", 1, 1, false
	while count > 0 do
		count = count - 1
		first_index = math.random(1, #first_list)
		last_index = math.random(1, #last_list)
		name = first_list[first_index] .. last_list[last_index]
		isill = ChatFilter.Instance:IsIllegal(name, true)
		-- 存在敏感字
		if isill then
			table.remove(first_list, first_index)
			table.remove(last_list, last_index)
		else
			EmojiTextUtil.AddText(emoji_text, name, COLOR3B.BLUE)
			return
		end
	end
	EmojiTextUtil.AddText(emoji_text, "richman", COLOR3B.BLUE)
end

function EmojiTextUtil.ParseChannelType(emoji_text, params, font_size, color, rich_content_type)
	if nil ~= params[3] then
		EmojiTextUtil.AddText(emoji_text, params[3], COLOR3B.BLUE)
	end
end

function EmojiTextUtil.ParseWordColor(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then
		return
	end

	local color_str
	if 8 == string.len(params[2]) then
		color_str = string.sub(params[2], 2, -1)
	else
		color_str = params[2]
	end
	if not string.find(color_str, "#") then
		color_str = "#" .. color_str
	end
	if 7 == string.len(color_str) then
		EmojiTextUtil.AddText(emoji_text, params[3], color_str)
	end
end

function EmojiTextUtil.ParseChannelMark(emoji_text, params, font_size, color, rich_content_type)
	print_error("EmojiText不支持动态添加Image")
end

-- team
function EmojiTextUtil.ParseTeam(emoji_text, params, font_size, color, rich_content_type, has_qipao)
	if #params < 3 then return end
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			if params[4] then
				local min_level = tonumber(params[4])
				if min_level > 0 then
					local role_level = GameVoManager.Instance:GetMainRoleVo().level
					if min_level > role_level then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.LevelNotEnough)
						return
					end
				end
			end

			local is_open, tips = FunOpen.Instance:GetFunIsOpened("NewTeamView", true)
			if not is_open then
				if tips then
					SysMsgWGCtrl.Instance:ErrorRemind(tips)
				end
				return
			end

			if SocietyWGData.Instance:GetIsInTeam() == 1 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.YouHasTeam)
				return
			end

			local team_type = params[5] or ""
			local team_index = tonumber(params[3]) or 0
			--记录请求加入的类型
			--ScoietyData.Instance:SetReqTeamIndex(team_index, team_type)

			SocietyWGCtrl.Instance:SendReqJoinTeam(team_index)
		end, index)
	end

	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, params[2], font_size, nil, callback, true, true, rich_content_type, has_qipao)
end

-- team_fenshen
function EmojiTextUtil.ParseTeamFenShen(emoji_text, params, font_size, color, rich_content_type, has_qipao)
	if #params < 3 then return end
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
		    local is_fenshen_open = NewTeamWGData.Instance:GetFenshenFuncIsOpen()
			
			if not is_fenshen_open then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FenShenNotOpen)
				return
			end

			if params[4] then
				local min_level = tonumber(params[4])
				if min_level > 0 then
					local role_level = GameVoManager.Instance:GetMainRoleVo().level
					if min_level > role_level then
						SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.LevelNotEnough)
						return
					end
				end
			end

			local team_type = params[5] or ""
			local team_index = tonumber(params[3]) or 0

			SocietyWGCtrl.Instance:SendReqJoinTeam(team_index, 1)
		end, index)
	end

	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, params[2], font_size, nil, callback, true, true, rich_content_type, has_qipao)
end

-- teamfb
function EmojiTextUtil.ParseTeamFb(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then return end
	local team_type = tonumber(params[2])
	local fb_mode = tonumber(params[3])
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type , fb_mode)
	EmojiTextUtil.AddText(emoji_text, goal_info.team_type_name, COLOR3B.D_ORANGE)
end

-- guildinfo
function EmojiTextUtil.ParseGuildInfo(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then
		return
	end

	if params[3] == "" or params[3] == nil then
		return
	end

	local guild_name = "【" ..params[3].. "】"
	color = COLOR3B.D_BLUE
	if rich_content_type == RICH_CONTENT_TYPE.Mail then
		color = COLOR3B.BLUE
	end
	EmojiTextUtil.AddText(emoji_text, guild_name, color)
end

-- guildinfo2
function EmojiTextUtil.ParseGuildInfo2(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then return end
	if params[3] == "" or params[3] == nil then return end
	local guild_name = string.format("【%s】",params[3])
	color = COLOR3B.D_BLUE
	if rich_content_type == RICH_CONTENT_TYPE.Mail then
		color = COLOR3B.BLUE
	end
	EmojiTextUtil.AddText(emoji_text, guild_name, color)
end

function EmojiTextUtil.GuildPost(emoji_text, params, font_size, color, rich_content_type)
	if params[2] == "" or params[2] == nil then return end
	if tonumber(params[2]) == 0 then return end

	local guild_post_list = Language.Guild.GuildPost
	if guild_post_list and #guild_post_list >= tonumber(params[2]) then
		local guild_post = "【" .. guild_post_list[tonumber(params[2])] .. "】"
		EmojiTextUtil.AddText(emoji_text, guild_post, color)
	end
end

-- guildjoin
function EmojiTextUtil.ParseGuildJoin(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, link_name)
    local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
            local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.Guild)
            if is_open then
                GuildWGCtrl.Instance:SendJoinGuildReq(params[2])
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FunOpenTip)
            end
        end, index)
    end
    is_bag_item = false

	link_name = link_name or Language.Common.ShengQingJiaRu
    EmojiTextUtil.CreateUnderLineBtn(emoji_text, link_name, font_size, nil, callback, true, true, rich_content_type)
end

-- money
function EmojiTextUtil.ParseMoney(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then return end

	local money_type_str = ""

	local money_type = tonumber(params[3]) or 0
	if 0 == money_type then
		money_type_str = Language.Common.Bind .. Language.Common.Coin
	elseif 1 == money_type then
		money_type_str = Language.Common.Bind .. Language.Common.Gold
	elseif 2 == money_type then
		money_type_str = Language.Common.Coin
	elseif 3 == money_type then
		money_type_str = Language.Common.Gold
	elseif 4 == money_type then
		money_type_str = Language.Common.Ticket
	end
	color = params[4] ~= nil and '#' .. params[4] or color
	EmojiTextUtil.AddText(emoji_text, params[2] .. money_type_str, color)
end

-- chinese_num
function EmojiTextUtil.ParseChineseNum(emoji_text, params, font_size, color, rich_content_type)
	local num = tonumber(params[2]) or 0

	local daxie_num = CommonDataManager.GetDaXie(num)

	if nil ~= daxie_num then
		color = params[3]~= nil and '#' .. params[3] or COLOR3B.D_BLUE

		if rich_content_type == RICH_CONTENT_TYPE.Mail then
			color = COLOR3B.BLUE
		end
		EmojiTextUtil.AddText(emoji_text, daxie_num, color)
	end
end

-- zhuxie_task_item
function EmojiTextUtil.ParseZhuXieTaskItem(emoji_text, params, font_size, color, rich_content_type)

end

--前往采集深渊宝箱
function EmojiTextUtil.ParseShenYuanCaiji(emoji_text, params, font_size, color, rich_content_type)
    local boss_id = tonumber(params[2])
    local disappear_time = tonumber(params[3])
    local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			local boss_info = BossWGData.Instance:GetShenYuanBossDataInfoByid(boss_id)
            if boss_info then
                local refresh_time = boss_info.status == 0 and boss_info.next_refresh_time --下次刷新时间
                local cur_time = TimeWGCtrl.Instance:GetServerTime()
                local zero_time = TimeWGCtrl.Instance:NowDayTimeStart(cur_time)
                local delay_time = BossWGData.Instance:ShenYuanBossOtherCfg().gather_disappear_s or 0
                local disappear_time_stamp = disappear_time + delay_time + zero_time --理论消失时间戳
                if refresh_time then
                    disappear_time_stamp = math.min(refresh_time, disappear_time_stamp)
                end
                if cur_time > disappear_time_stamp or boss_info.status == 1 then --boss存活/过了时间
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.BoxIsDisappear)
                    return
                end

                local level = RoleWGData.Instance:GetRoleLevel()
                if level < boss_info.need_role_level then
                    SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.CannotGoToCaiji, RoleWGData.GetLevelString(boss_info.need_role_level)))
                    return
                end

                local param = {}
                param[2] = boss_info.scene_id
                local gather_pos = Split(boss_info.gather_pos, ",")
                param[3] = tonumber(gather_pos[1])
                param[4] = tonumber(gather_pos[2])
                BossWGCtrl.Instance:GoToHelpGuildAssist(param)
            end
		end, index)
	end

    color = COLOR3B.D_GREEN
    local link_name = Language.Boss.GoToCaiji
    EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, font_size, color, callback, true, true, rich_content_type)
end

-- fazhen_name
function EmojiTextUtil.ParseFaZhenName(emoji_text, params, font_size, color, rich_content_type)
	local fazhen_grade = tonumber(params[2]) or 0
	local fazhen_name = ""
	local fazhen_color = FAZHEN_COCOR3B[fazhen_grade] or COLOR3B.WHITE
	EmojiTextUtil.AddText(emoji_text, fazhen_name, fazhen_color)
end

-- defend_area
function EmojiTextUtil.ParseDefendArea(emoji_text, params, font_size, color, rich_content_type)

end

-- mount
function EmojiTextUtil.ParseMount(emoji_text, params, font_size, color, rich_content_type)
	local mount_grade = tonumber(params[2]) or 0
	if mount_grade > 1000 then
		local image_list = ConfigManager.Instance:GetAutoConfig("mount_auto").special_img[mount_grade - 1000]
		if nil ~= image_list and nil ~= image_list.item_id then
			local name = ItemWGData.Instance:GetItemName(image_list.item_id) or ""
			EmojiTextUtil.AddText(emoji_text, name, COLOR3B.D_ORANGE)
		end
	else
		local client_grade = math.max(mount_grade - 1, 1)
		local image_list = ConfigManager.Instance:GetAutoConfig("mount_auto").image_list[client_grade]
		if nil ~= image_list and nil ~= image_list.show_grade and nil ~= image_list.image_name then
			color = GRADE_D_COCOR3B[image_list.show_grade]
			EmojiTextUtil.AddText(emoji_text, image_list.image_name, color)
		end
	end
end

function EmojiTextUtil.ParseMountGrade(emoji_text, params, font_size, color, rich_content_type)
	local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, tonumber(params[2]))
	local grade = cfg.grade_num .. Language.Common.Jie
	EmojiTextUtil.AddText(emoji_text, grade, color)
end

function EmojiTextUtil.ParseLingChongGrade(emoji_text, params, font_size, color, rich_content_type)
	local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, tonumber(params[2]))
	local grade = cfg.grade_num .. Language.Common.Jie
	EmojiTextUtil.AddText(emoji_text, grade, color)
end

function EmojiTextUtil.ParseMarryType(emoji_text, params, font_size, color, rich_content_type)
	local marry_type = Language.Marry.MarryType[tonumber(params[2])]
	EmojiTextUtil.AddText(emoji_text, marry_type, color)
end

function EmojiTextUtil.ParseXiaoGui(emoji_text, params, font_size, color, rich_content_type)
	local imp_type = tonumber(params[2]) or 0
	local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfgType(imp_type)
	if xiaogui_cfg then
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(xiaogui_cfg.item_id)
		color = ItemWGData.Instance:GetItemColor(xiaogui_cfg.item_id)
		if item_cfg then
			EmojiTextUtil.AddText(emoji_text, item_cfg.name, color)
		end
	end
end

function EmojiTextUtil.ParseXiaoGuiTime(emoji_text, params, font_size, color, rich_content_type)
	local time_length = tonumber(params[2]) or 0
	local hour, min = 0, 0
	hour = math.floor(time_length / 3600)
	min = math.floor((time_length % 3600) / 60)
	local time_str = string.format(Language.Equip.TotalTimeDesc, hour, min)
	EmojiTextUtil.AddText(emoji_text, time_str, color)
end

-- bubble
function EmojiTextUtil.ParseBubble(emoji_text, params, font_size, color, rich_content_type)
	local seq = tonumber(params[2])
	local cfg = NewAppearanceWGData.Instance:GetBubbleCfg()
	local bubble_name = cfg[seq].name or ""
	EmojiTextUtil.AddText(emoji_text, bubble_name, COLOR3B.YELLOW)
end

-- qibing
function EmojiTextUtil.ParseQiBing(emoji_text, params, font_size, color, rich_content_type)
	local qibing_level = tonumber(params[2]) or 1
	local qibing_cfg = ConfigManager.Instance:GetAutoConfig("mount_auto").qibing[qibing_level]
	if nil ~= qibing_cfg and nil ~= qibing_cfg.name then
		EmojiTextUtil.AddText(emoji_text, qibing_cfg.name, GRADE_COCOR3B[qibing_level] or COLOR3B.WHITE)
	end
end

--to_decimal
function EmojiTextUtil.ParseToDecimal(emoji_text, params, font_size, color, rich_content_type)
	local rate = tonumber(params[2]) or 0
	EmojiTextUtil.AddText(emoji_text, rate / 100, COLOR3B.YELLOW)
end

--monster
function EmojiTextUtil.ParseMonster(emoji_text, params, font_size, color, rich_content_type)
	local monster_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[tonumber(params[2]) or 0]
	local monster_color
	if nil ~= params[3] and params[3] ~= "" then
		monster_color = "#"..params[3]
	else
		monster_color = "#DF140E"
	end

	local show_jieshu = false
	if nil ~= params[4] then
		show_jieshu = tonumber(params[4]) == 1
	end
	monster_color = rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and COLOR3B.D_RED or monster_color
	if nil ~= monster_config and nil ~= monster_config.name then
		local name = monster_config.name
		if show_jieshu then
			name = name .. "(" .. string.format(Language.Common.Order,monster_config.boss_jieshu) .. ")"
		end
		EmojiTextUtil.AddText(emoji_text, name, monster_color) --, Str2C3b(monster_color), text_attr)
	end
end

local Opperside_type = {
    XianMengZhengBa = 1,
    KuaFu3V3 = 2,
}
--opperside
function EmojiTextUtil.ParseOpperSide(emoji_text, params, font_size, color, rich_content_type)
	--print_error("ParseOpperSide > ",params)
    --param2 :  1 仙盟争霸 2 跨服3v3
    if nil == params[3] then
        return
    end
    if nil ~= params[2] then
        local name = ""
        if tonumber(params[3]) == Opperside_type.XianMengZhengBa then
            name = tonumber(params[2]) == 0 and Language.ZCRuneText.BlueSide or Language.ZCRuneText.RedSide
        elseif tonumber(params[3]) == Opperside_type.KuaFu3V3 then
            name = tonumber(params[2]) == 1 and Language.ZCRuneText.BlueSide or Language.ZCRuneText.RedSide
        end
        EmojiTextUtil.AddText(emoji_text, name, color)
    end
end

--title
function EmojiTextUtil.ParseTitle(emoji_text, params, font_size, color, rich_content_type)
	local title_config = TitleWGData.GetTitleConfig(tonumber(params[2]) or 0)
	if nil ~= title_config and nil ~= title_config.name then
		EmojiTextUtil.AddText(emoji_text, title_config.name, COLOR3B.YELLOW)
	end
end

--eq_shenzhu
function EmojiTextUtil.ParseEquipShenZhu(emoji_text, params, font_size, color, rich_content_type)
	local shenzhu = tonumber(params[2]) or 0
	local prefixion = shenzhu > 0 and EquipmentWGData.Instance:GetShengzhuPrefixion(shenzhu) .. "·" or ""
	EmojiTextUtil.AddText(emoji_text, prefixion, COLOR3B.YELLOW)
end

--eq_quality
function EmojiTextUtil.ParseEquipQuality(emoji_text, params, font_size, color, rich_content_type)
	local quality = tonumber(params[2]) or 0
	local color_name = Language.Common.ColorName[quality + 1] or ""
	color = EQUIP_COLOR[quality] or COLOR3B.WHITE
	EmojiTextUtil.AddText(emoji_text, color_name, color)
end

--csa_sub_type
function EmojiTextUtil.ParseCsaSubType(emoji_text, params, font_size, color, rich_content_type)
	local sub_type = tonumber(params[2]) or 0
	local name = ActCombineData.GetCombineServerName(sub_type)
	EmojiTextUtil.AddText(emoji_text, name, COLOR3B.YELLOW)
end

--openLink
function EmojiTextUtil.ParseOpenLink(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, spec_link_name)
	if ignored_link then return end
	local link_type = tonumber(params[2]) or 0
	local link_cfg = ChatWGData.Instance:GetOpenLinkCfg(link_type)
	local link_name = nil
	if spec_link_name then
		link_name = spec_link_name
	else
		link_name = nil ~= link_cfg and link_cfg.name or "unknown link type:" .. link_type
	end

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			EmojiTextUtil.DoByLinkType(link_type, params[3], params[4], params[5])
		end, index)
	end
	is_bag_item = false
	local size = font_size or (rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and 20 or 21)

	if link_cfg then
		EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, size, nil, callback, true, true, rich_content_type, has_bubble)
	end
end

--zero_buy_xiajia--零元购下架超链接
function EmojiTextUtil.ZeroBuyXiaJiaLink(emoji_text, params, font_size, color, rich_content_type)
	local link_name = ""
	local cur_step = tonumber(params[2])
	if cur_step and #Language.ZeroBuy.GiftNames >= cur_step then
		link_name = Language.ZeroBuy.GiftNames[cur_step] .. Language.ZeroBuy.ActivityName
	end

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			LayoutZeroBuyWGCtrl.Instance:OpenZeroBuyByIndex(cur_step)
		end, index)
	end
	local size = font_size or (rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and 20 or 21)
	if link_name ~= "" then
		EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, size, nil, callback, true, true, rich_content_type)
	end
end

--qingyuan_card
function EmojiTextUtil.ParseQingYuanCard(emoji_text, params, font_size, color, rich_content_type)
	local card_info = MarryWGData.Instance:GetCardInfoByIdAndLevel(tonumber(params[2]),tonumber(params[3]))
	if nil ~= card_info and nil ~= card_info.card_name then
		EmojiTextUtil.AddText(emoji_text, card_info.card_name, COLOR3B.YELLOW)
	end
end

--activityPos
function EmojiTextUtil.ParseActivityPos(emoji_text, params, font_size, color, rich_content_type)
	if #params < 5 then return end
	local point_text = ""
	local pos_type = tonumber(params[5]) or 0
	if pos_type ~= -1 then
		local desc = Language.Common.ActivityPosStr[pos_type] or ""
		point_text = desc.. "(" .. params[3] .. "," .. params[4] .. ")"
		if pos_type == 2 or pos_type == 4 then
			point_text = desc
		end

		local function callback(index)
			local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
			tmp_link_opener:AddClickListener(function()
				EmojiTextUtil.FlyToPos(tonumber(params[2]) or 0, tonumber(params[3]) or 0, tonumber(params[4]) or 0)
			end, index)
		end

		EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, point_text,font_size, nil, callback, nil, true, rich_content_type)
	end
end

-- 拆分服务器组
function EmojiTextUtil.ParseServerGroupSeq(emoji_text, params, font_size, color, rich_content_type)
	local server_group_seq =  tonumber(params[2]) or 1
	local server_id = CrossServerWGData.Instance:GetServerMainServerIDBySeq(server_group_seq)
	local text_content = string.format("[s%d]", server_id)
	local server_color = COLOR3B.YELLOW
	EmojiTextUtil.AddText(emoji_text, text_content, server_color)
end

-- 拆分服务器ID
function EmojiTextUtil.ParseCrossServerID(emoji_text, params, font_size, color, rich_content_type, has_bubble, ignored_link, link_name, is_small)
	local part_type = tonumber(params[2]) or 0
	local server_id = tonumber(params[3]) or 0
	local server_color = COLOR3B.YELLOW
	local name = string.format("[s%d]", server_id)
	EmojiTextUtil.AddText(emoji_text, name, color)
end

function EmojiTextUtil.ParseGatherName(emoji_text, params, font_size, color, rich_content_type)
	local gather_id =  tonumber(params[2]) or 1
	local gather_cfg = Scene.Instance:GetGatherCfgByGatherId(gather_id)
	if gather_cfg then
		EmojiTextUtil.AddText(emoji_text, gather_cfg.show_name, COLOR3B.YELLOW)
	end
end

--activity
function EmojiTextUtil.ParseActivity(emoji_text, params, font_size, color, rich_content_type)
	local act_type = tonumber(params[2])
	if nil == act_type then return end
	local act_name = nil
	if CompetitionWGData.IsBipinType(act_type) then
		act_name = Language.Competition.BipinName[act_type] or ""
	else
		act_name = ActivityWGData.Instance:GetActivityName(tonumber(params[2]))
	end

	if nil == act_name then
		local cfg = ServerActivityWGData.Instance:GetClientActCfgByOpenType(act_type)
		if cfg ~= nil then
			act_name = cfg.act_name
		end
	end
	if nil == act_name then
		act_name = ""
	end

	act_name = act_name .. Language.OpenServer.HuoDongText
	local color = color or "#A5492D"
	EmojiTextUtil.AddText(emoji_text, act_name, color)
end

--prof
function EmojiTextUtil.ParseProf(emoji_text, params, font_size, color, rich_content_type)
	local prof_name = Language.Common.ProfName[tonumber(params[3] or 0)][tonumber(params[2] or 0)] or ""
	EmojiTextUtil.AddText(emoji_text, prof_name, "#ff5501")
end

--xianjie
function EmojiTextUtil.ParseXianJie(emoji_text, params, font_size, color, rich_content_type)
	if #params < 3 then return end
	local cfg = XianjieData.GetXianJieCfgByLv(tonumber(params[3]))
	if nil == cfg then
		return
	end
	EmojiTextUtil.AddText(emoji_text, cfg.name,"#"..params[2])--, Str2C3b(params[2]), text_attr)
end

--card_color
function EmojiTextUtil.ParseCardColor(emoji_text, params, font_size, color, rich_content_type)
	local color = tonumber(params[2]) or 1
	local color_name = Language.Card.ColorName[color]
	if nil ~= color_name then
		EmojiTextUtil.AddText(emoji_text, color_name, ITEM_COLOR[color])
	end
end

-- gengu_title
function EmojiTextUtil.ParseGengu(emoji_text, params, font_size, color, rich_content_type)

end

-- jingmai_title
function EmojiTextUtil.ParseJingmai(emoji_text, params, font_size, color, rich_content_type)

end

-- mentality
function EmojiTextUtil.ParseMentality(emoji_text, params, font_size, color, rich_content_type)
	local gengu_type = (tonumber(params[2]) or 0) + 1
	local gengu_name = Language.Meridian.NameList[gengu_type]
	if nil ~= gengu_name then
		EmojiTextUtil.AddText(emoji_text, gengu_name, COLOR3B.YELLOW)
	end
end

-- 飞升名字解析
function EmojiTextUtil.ParseFeiSheng(emoji_text, params, font_size, color, rich_content_type)
	local fei_times = tonumber(params[2]) or 0
	local fei_name = Language.FeiShen.NameTable[fei_times]
	local name = fei_name or ""
	EmojiTextUtil.AddText(emoji_text, name, color)
end

-- 时装跳转
function EmojiTextUtil.ParseShiZhuangLink(emoji_text, params, font_size, color, rich_content_type)
	local part_type = tonumber(params[3]) or 0
    local index = tonumber(params[4]) or 0
    local link_cfg = ChatWGData.Instance:GetOpenLinkCfg(tonumber(params[2]))
	if not link_cfg then
		return
    end
	
    local link_name = nil ~= link_cfg and link_cfg.name or "unknown link type:" .. tonumber(params[2])

    local click_func =
    function()
        local param = {}
        param["item_id"] = NewAppearanceWGData.Instance:GetFashionItemId(part_type, index)
        FunOpen.Instance:OpenViewByName(link_cfg.view_name, link_cfg.tab_index, param)
    end
    local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
            click_func()
		end, index)
    end

	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, link_name, font_size, nil, callback, true, true, rich_content_type)
end

-- 时装名字解析
function EmojiTextUtil.ParseShiZhuangName(emoji_text, params, font_size, color, rich_content_type)
	local part_type = tonumber(params[2]) or 0
	local index = tonumber(params[3]) or 0
	local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index)
	if not cfg then
		return
	end

	local name = cfg.name or ""
	local item_id = NewAppearanceWGData.Instance:GetFashionItemId(part_type, index)
		local data = ItemWGData.Instance:GetItemConfig(item_id)
		if data then
			color = ITEM_COLOR[data.color]
		end


	EmojiTextUtil.AddText(emoji_text, name, color)
end

-- 天神名字解析
function EmojiTextUtil.ParseTianShenName(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local cfg = TianShenWGData.Instance:GetTianShenCfg(index)
	if not cfg then
		return
	end

	local name_color = TIANSHEN_COLOR3B[cfg.series]
	local name = string.format("<color=%s>%s</color>", name_color, cfg.bianshen_name) or ""
	EmojiTextUtil.AddText(emoji_text, name, color)
end

-- 法宝信息解析
function EmojiTextUtil.ParseFabao(emoji_text, params, font_size, color, rich_content_type)
	local id = tonumber(params[2]) or 0
	local name = 0 --没用代码已经删了，需要另外找办法
	EmojiTextUtil.AddText(emoji_text, name, COLOR3B.YELLOW)
end

-- 龙徽信息解析
function EmojiTextUtil.ParseLonghui(emoji_text, params, font_size, color, rich_content_type)
	local lv = tonumber(params[2]) or 0
	local name = GuildWGData.GetXianShuName(lv)
	EmojiTextUtil.AddText(emoji_text, name, COLOR3B.YELLOW)
end

-- 神兵图鉴
function EmojiTextUtil.ParseCrossPlant(emoji_text, params, font_size, color, rich_content_type)
	local cross_plant = tonumber(params[2]) or 0
	local name = ""
	local cfg = {}
	for k,v in pairs(cfg) do
		if cross_plant == v.type then
			name = v.name
			break
		end
	end
	EmojiTextUtil.AddText(emoji_text, name, COLOR3B.YELLOW)
end

-- 装备的部位，%d是对应下标
function EmojiTextUtil.ParseEi(emoji_text, params, font_size, color, rich_content_type)
	local index = tonumber(params[2]) or 0
	local name = Language.Equip.EquipName[index] or ""
	EmojiTextUtil.AddText(emoji_text, name, COLOR3B.YELLOW)
end

function EmojiTextUtil.ParseHunshouColor(emoji_text, params, font_size, color, rich_content_type)
	local color_number = tonumber(params[2]) or 0
	local color_str = Language.Pet.HunshouColor[color_number] or ""
	local color = ITEM_COLOR[color_number] or COLOR3B.WHITE

	EmojiTextUtil.AddText(emoji_text, color_str, color)
end

function EmojiTextUtil.ParseTianxiangeTitle(emoji_text, params, font_size, color, rich_content_type)
	local level = tonumber(params[2]) or 0
	local name_str = DailyWGData.Instance:GetCurTitleName(level)
	EmojiTextUtil.AddText(emoji_text, name_str, color)
end

function EmojiTextUtil.FlyToPos(scene_id, x, y)
    local main_role = Scene.Instance:GetMainRole()
    if main_role:IsInXunYou() then
        return
    end

    if scene_id == Scene.Instance:GetSceneId()  then --太近不给跳,直接移动
	 	if GuajiWGCtrl.CheckRange(x, y, 40) then
	 		GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y)
	 	else
	 		if VipPower.Instance:GetHasPower(VipPowerId.scene_fly) then
    			TaskWGCtrl.Instance:JumpFly(scene_id, x, y, true)
    		else
    			GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y)
    		end
	 	end
    else
    	if VipPower.Instance:GetHasPower(VipPowerId.scene_fly) then
    		TaskWGCtrl.Instance:JumpFly(scene_id, x, y, true)
    	else
			GuajiWGCtrl.Instance:FlyToScene(scene_id, nil, false)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y)
		end
    end
end


function EmojiTextUtil.GoToSeeRole(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(x, y)
            local role_id = tonumber(params[2])
            local plat_type = tonumber(params[3])
            local merge_server_id = tonumber(params[4])
            local other_role_vo = {
                merge_server_id = merge_server_id,
                origin_plat_type = plat_type
            }
            local is_cross = not RoleWGData.Instance:IsSameServer(other_role_vo)
            BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
                BrowseWGCtrl.Instance:OpenWithUid(role_id, nil, is_cross, plat_type, 1)
			end, plat_type, is_cross)
		end, index)
	end

	local str = Language.Browse.GoToSee
    rich_content_type = rich_content_type or RICH_CONTENT_TYPE.ParseRoleColor
    if rich_content_type == RICH_CONTENT_TYPE.NONE or rich_content_type == RICH_CONTENT_TYPE.MAIN_UI or rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
        color = has_bubble and COLOR3B.GLOD or F2ChatTextColor[0]
    elseif rich_content_type == RICH_CONTENT_TYPE.ParseZhanDuiRoleColor then
        color = nil
    end

    EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, str, font_size, color, callback, nil, false, rich_content_type)
end


function EmojiTextUtil.ParseWhoSeeYouRole(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(x, y)
            local role_id = tonumber(params[2])
            local plat_type = tonumber(params[4])
            local merge_server_id = tonumber(params[5])
            local other_role_vo = {
                merge_server_id = merge_server_id,
                origin_plat_type = plat_type
            }
            local is_cross = not RoleWGData.Instance:IsSameServer(other_role_vo)
			BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
                ChatWGCtrl.Instance:CreateRoleHeadCell(param_protocol.role_id, params[3], param_protocol.prof, param_protocol.sex,
                param_protocol.is_online, Vector2(x,y), param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name) --U3DObject(btn.gameObject,btn.transform,_G)
			end, plat_type, is_cross)
		end, index)
	end

	if nil ~= params[3] then
		if params[3] == "NULL" then
			-- 随机名
			local rand_num = math.floor(math.random(1, 200))
			params[3] = CommonDataManager.GetRandomName(rand_num)
		end
		local str = "["..params[3].."]"

		rich_content_type = rich_content_type or RICH_CONTENT_TYPE.ParseRoleColor
		if rich_content_type == RICH_CONTENT_TYPE.NONE or rich_content_type == RICH_CONTENT_TYPE.MAIN_UI or rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW then
			color = has_bubble and COLOR3B.GLOD or F2ChatTextColor[0]
		elseif rich_content_type == RICH_CONTENT_TYPE.ParseZhanDuiRoleColor then
			color = nil
		end

		EmojiTextUtil.CreateUnderLineBtn(emoji_text, str, font_size, color, callback, nil, false, rich_content_type)
	end
end

-- 种花浇水活动前往拜访
function EmojiTextUtil.ParseWateringFlower(emoji_text, params, font_size, color, rich_content_type)
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(index)
			WateringFlowersWGData.Instance:SetGardenFlag(1)
			WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_CONFIRM_INVITE, params[4])
			WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_OTER_INFO_REQ, params[4])
			WateringFlowersWGCtrl.Instance:CloseWateringRecordView()
		end, index)
	end

	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, params[3], font_size, params[2], callback, true, true, rich_content_type)
end


--每日宝箱解析
function EmojiTextUtil.ParseDailyTreasureQuality(emoji_text, params, font_size, color, rich_content_type)
	local quality = tonumber(params[2])
	local cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	local name = cfg and cfg.name or ""
	local common_color = cfg and cfg.common_color or 1
	local str = ""
	if rich_content_type == RICH_CONTENT_TYPE.Mail then
		str = ToColorStr(name,ITEM_COLOR[common_color])
	else
		str = ToColorStr(name,ITEM_COLOR_DARK[common_color])
	end

	EmojiTextUtil.AddText(emoji_text, str, color)
end

--鸭子解析
function EmojiTextUtil.ParseDuckIndex(emoji_text, params, font_size, color, rich_content_type)
	local duck_index = tonumber(params[2])
	local cfg = DuckRaceWGData.Instance:GetDuckTraitCfg(duck_index)
	local monster_id = DuckRaceWGData.Instance:GetDuckMonsterId(duck_index)
	local mosnter_cfg = BossWGData.Instance:GetMonsterInfo(monster_id)
	local name = mosnter_cfg and mosnter_cfg.name or ""
	local str = string.format(Language.DuckRace.DuckNameInDarkView[duck_index], name)
	EmojiTextUtil.AddText(emoji_text, str, color)
end


--天才厨神
function EmojiTextUtil.ParseChuShenItem(emoji_text, params, font_size, color, rich_content_type)
	local item_id = tonumber(params[2]) or 0
	local sex =  tonumber(params[3]) or GameEnum.MALE
	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(index)
			ChuShenWGData.Instance:ChatHelp(item_id, role_id)
		end, index)
	end
	local size = font_size or (rich_content_type == RICH_CONTENT_TYPE.MAIN_UI and 20 or 21)
	local color = COLOR3B.D_GREEN
	local str = sex == GameEnum.MALE and Language.Activity.GaoShuTa or Language.Activity.GaoShuTa_Nv
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, str, size, color, callback, true, true, rich_content_type)
end

--节日活动 天才厨神
function EmojiTextUtil.ParseFAChuShenItem(emoji_text, params, font_size, color, rich_content_type,has_bubble)
	local item_id = tonumber(params[2]) or 0
	local sex =  tonumber(params[3]) or GameEnum.MALE
	local role_id = tonumber(params[4]) or 0
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function(index)
			FestivalChuShenWGData.Instance:ChatHelp(item_id, role_id)
		end, index)
	end
	color = COLOR3B.D_GREEN
    if rich_content_type == RICH_CONTENT_TYPE.CHAT_WINDOW and has_bubble then
        color = COLOR3B.GREEN
    end
	local str = sex == GameEnum.MALE and Language.FestivalChuShen.GaoShuTa or Language.FestivalChuShen.GaoShuTa_Nv
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, str, font_size, color, callback, true, true, rich_content_type)
end

--品质颜色亮底色 暗底色转换   {color_change;%s;%s}
function EmojiTextUtil.ParseColorChange(emoji_text, params, font_size, color, rich_content_type,has_bubble)
	local color_num = tonumber(params[2]) or 1
	color = ITEM_COLOR_DARK[color_num]
	if has_bubble then
		color = ITEM_COLOR[color_num]
	end
	EmojiTextUtil.AddText(emoji_text, params[3], color)
end

function EmojiTextUtil.ParseQiHunFuWen(emoji_text, params, font_size, color, rich_content_type, has_bubble)
	local lingzhi_type = tonumber(params[2]) or 0
	local skill_index = tonumber(params[3]) or 0
	local skill_cfg = LingZhiSkillWGData.Instance:GetFuWenCfg(lingzhi_type, skill_index, 0)
	local str = ''
	if skill_cfg then
		str = ToColorStr(skill_cfg.skill_name, ITEM_COLOR_DARK[skill_cfg.skill_color])
	end

	EmojiTextUtil.AddText(emoji_text, str, color)
end

function EmojiTextUtil.ParseQiHun(emoji_text, params, font_size, color, rich_content_type )
	local lingzhi_type = tonumber(params[2]) or 0
	local grade = tonumber(params[3]) or 0
	local xl_cfg = LingZhiSkillWGData.Instance:GetXiuLianCfg(lingzhi_type, grade)
	local str = ''
	if xl_cfg then
		str = ToColorStr(xl_cfg.type_name, ITEM_COLOR_DARK[xl_cfg.color])
	end

	EmojiTextUtil.AddText(emoji_text, str, color)
end

function EmojiTextUtil.ParseQiHunXL(emoji_text, params, font_size, color, rich_content_type )
	local lingzhi_type = tonumber(params[2]) or 0
	local grade = tonumber(params[3]) or 0
	local xl_cfg = LingZhiSkillWGData.Instance:GetXiuLianCfg(lingzhi_type, grade)
	local str = ''
	if xl_cfg then
	str = ToColorStr(string.format(Language.LingZhi.Tip9, xl_cfg.grade_name, xl_cfg.grade_show), ITEM_COLOR_DARK[xl_cfg.color])
	end

	EmojiTextUtil.AddText(emoji_text, str, color)
end

--点击链接类型
function EmojiTextUtil.DoByLinkType(link_type, param1, param2, param3)
	local link_cfg = ChatWGData.Instance:GetOpenLinkCfg(link_type)
	if nil == link_cfg then
		Log("请先配置", link_type)
		return
    end

    if link_type == CHAT_LINK_TYPE.FAKE_CLIENT_LINK_TYPE then					-- 客户端自定义的假的
		ActivityWGCtrl.Instance:DoJoinActivity(tonumber(param1))
	elseif link_type == CHAT_LINK_TYPE.CHAT_LINK_TYPE_563 then					-- 衣橱跳转
		local jump_suit = WardrobeWGData.Instance:GetJumpIndexOnShowList(tonumber(param1))
		FunOpen.Instance:OpenViewByName(link_cfg.view_name, link_cfg.tab_index, {jump_suit = jump_suit})
	elseif link_type == CHAT_LINK_TYPE.GUILD_APPLY then							-- 仙盟申请
		if RoleWGData.Instance.role_vo.guild_id > 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.HasInGuild)
		else
			FunOpen.Instance:OpenViewByName(link_cfg.view_name, link_cfg.index)
		end

	elseif link_type == CHAT_LINK_TYPE.GUILD_FB_END then
		GuildWGCtrl.Instance.view:SetSpecialParam(2)
		FunOpen.Instance:OpenViewByName(link_cfg.view_name, link_cfg.tab_index)
	elseif link_type == CHAT_LINK_TYPE.CAN_JIA_HUN_YAN then						-- 婚宴副本
		WeddingWGCtrl.Instance:SendJoinHunyan(tonumber(param1) or 0)

	elseif link_type == CHAT_LINK_TYPE.WO_QIUHUN then							-- 我要求婚
        local user_id = tonumber(param1)
        if not RoleWGData.Instance:GetIsMainRole(user_id) then
            MarryWGCtrl.Instance:GotoYueLao(user_id)
        end
	elseif link_type == CHAT_LINK_TYPE.WO_CHONGZHI then							-- 我要充值
		local chongzhi_info = ServerActivityWGData.Instance:GetTotalChongZhiData()
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	elseif link_type == CHAT_LINK_TYPE.DAY_DANBI then
		ServerActivityWGCtrl.Instance:OpenByActId(ServerActClientId.RAND_DAY_DANBI_CHONGZHI)

	elseif link_type == CHAT_LINK_TYPE.WO_BAISHI then   -- 情缘副本 同意组队
		MasterWGCtrl.Instance:OpenSelectMasterView({user_id = tonumber(param1) or 0, gamename = param2, level = tonumber(param1)})

	elseif link_type == CHAT_LINK_TYPE.ACTDISCOUNT then
		local index = ActDiscountData.Instance:SetJumpIndex()
		ActDiscountData.Instance:SetPhaseIndex(index)
		ViewManager.Instance:Open(GuideModuleName.ActDiscount)

	elseif link_type == CHAT_LINK_TYPE.IWANT then  				--开服累充我也要
		local is_open = ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.SEVENDAY_RECHARGE)
		if is_open then
			ServerActivityWGCtrl.Instance:OpenOpenserverCompetitionByUniqueKey(link_cfg.key)
			return
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoKaiFuLeiChong)
			return
		end
	elseif link_type == CHAT_LINK_TYPE.RECHARGE_TQTZ_1 then
		RechargeWGCtrl.Instance:TouZiPlanTurn(0)
	elseif link_type == CHAT_LINK_TYPE.RECHARGE_TQTZ_2 then
		RechargeWGCtrl.Instance:TouZiPlanTurn(1)
	elseif link_type == CHAT_LINK_TYPE.RECHARGE_TQTZ_3 then
		RechargeWGCtrl.Instance:TouZiPlanTurn(2)
	elseif link_type == CHAT_LINK_TYPE.OPENSERVER_COMPETITION_RUSHTYPE then
		local r_type = tonumber(param1)
		if r_type then
			ServerActivityWGCtrl.Instance:OpenOpenserverCompetitionByUniqueKey(link_cfg.key, {rush_type = r_type})
		end
	elseif link_type == CHAT_LINK_TYPE.CHAT_LINK_TYPE_524 then--鸿蒙神域拦截
		local scene_index = tonumber(param1)
		local scene_id = tonumber(param2)
		local cfg = BossWGData.Instance:GetHMSYLayerBySceneIdAndIndex(scene_id)
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if open_day < cfg.opengame_day then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HMSY.NotOpenDay)
			return
		end
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level < cfg.open_level then
			local _, need_level = RoleWGData.Instance.GetLevelString(cfg.open_level)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel,need_level))
			return
		end
		BossWGCtrl.Instance:SendHMSYEnterReq(scene_id, scene_index)
	elseif link_type == CHAT_LINK_TYPE.GOTOVIP6 then
		ViewManager.Instance:Open(link_cfg.view_name, link_cfg.tab_index, nil, {5})
	elseif link_type == CHAT_LINK_TYPE.ZERO_BUY then--零元购上架
        LayoutZeroBuyWGCtrl.Instance:OpenZeroBuyByIndex(tonumber(param1) or 0)
    elseif link_type == CHAT_LINK_TYPE.FORTUNE_CAT_JUMP then
        local not_open, limit_lv = FortuneCatWGData.Instance:JudgeIsNotOpenByLv()
        if not_open then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FortuneCat.LvNotEnough, limit_lv))
            return
        end
        if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT) then
            FunOpen.Instance:OpenViewByName(link_cfg.view_name, link_cfg.tab_index)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivityIsEnd)
        end
    elseif link_type == CHAT_LINK_TYPE.TIANSHENJIANLIN then
    	TianshenRoadWGCtrl.Instance:GotoShiLian()
    elseif link_type == CHAT_LINK_TYPE.XINGTIANLAIXI then
    	QuanMinBeiZhanWGCtrl.Instance:GotoShiLian()
    elseif link_type == CHAT_LINK_TYPE.TaskChain then
    	local is_open, open_level = OperationTaskChainWGData.Instance:CheckIsOpen()
		if not is_open then
			if open_level ~= nil then
				local str = string.format(Language.Common.JoinEventActLevelLimit, open_level)
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end

			return
		end

		local scene_type = Scene.Instance:GetSceneType()
		if OperationTaskChainWGData.Instance:IsTaskChainScene() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.IsInScene)
			return
		else
			local scene_id = tonumber(param1)
			local npc_id = tonumber(param2)
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
			GuajiWGCtrl.Instance:MoveToNpc(npc_id, nil, scene_id)
		end
	--每日宝箱告知密码 点击事件处理
	elseif link_type == CHAT_LINK_TYPE.BAOXIANG_CODE then
		local quality = tonumber(param1)
		local role_id = tonumber(param2)

		local main_role_id = RoleWGData.Instance:GetMainRoleId()
		if role_id == main_role_id then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.NotTallMySelf)
			return
		end
		local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
		if IsEmptyTable(info) then return end
		local info_quality = info.quality
		local bx_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
		local common_color = bx_cfg and bx_cfg.common_color or 1
		local name = bx_cfg and bx_cfg.name or ""
		name = ToColorStr(name,ITEM_COLOR_DARK[common_color])
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local tall_my_code_time = EmojiTextUtil.GetTallMyCodeTime()
		if tall_my_code_time > server_time then
			local time = math.floor(tall_my_code_time - server_time)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BiZuoBaoXiang.TallCodeCDTime,time))
			return
		end
		local open_tag = info.open_tag
		local password_list = info.password_list
		local password_bit = info.password_bit
		local virtual_id = bx_cfg and bx_cfg.virtual_id or 91146
		local item_cfg = ItemWGData.Instance:GetItemConfig(virtual_id)
		local str = ""
		local rand_num = math.random(1,4)
		if open_tag == 1 then
			local code_str = ""
			for i,v in ipairs(password_list) do
				code_str = code_str .. Language.BiZuoBaoXiang.CodeStr[v]
			end
			local tall_all_str = Language.BiZuoBaoXiang.TallAllCode[rand_num]
			str = string.format(tall_all_str, code_str)
		else
			local tall_my_str = Language.BiZuoBaoXiang.TallMyCode[rand_num]
			local code_str = Language.BiZuoBaoXiang.CodeStr[password_list[password_bit]]
			str = string.format(tall_my_str, Language.BiZuoBaoXiang.NormalStr[password_bit], code_str)
		end
		EmojiTextUtil.AddTallMyCodeTime(server_time + 30)
		local curr_send_channel = tonumber(param3) or CHANNEL_TYPE.GUILD
		ChatWGCtrl.Instance:SendChannelChat(curr_send_channel, str, CHAT_CONTENT_TYPE.TEXT)
	elseif link_type == CHAT_LINK_TYPE.ST_XIULI_LINK then
		if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.SHITUXIULI) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OperationActivity.ActivityStr1)
			return
		end

		local teacher_vip_limit = ShiTuXiuLiWGData.Instance:GetOtherCfgList("teacher_vip_limit") or 0
		local my_vip_level = VipWGData.Instance:GetVipLevel()
		local jump_index = 2
		if my_vip_level ~= nil and my_vip_level >= teacher_vip_limit then
			jump_index = 1
		end

		ShiTuXiuLiWGCtrl.Instance:OpenShiTuView(jump_index)
	--新 Boss协助 点击
	elseif link_type == CHAT_LINK_TYPE.BOSS_XIEZHU_LINK then
		local uid = tonumber(param1)
		local role_id = RoleWGData.Instance:InCrossGetOriginUid()
		if role_id == uid then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.NorHelpSelf)
			return
		end
		local invoke_info = BossXiezhuWGData.Instance:GetInvokeInfoByRoleId(uid)
		if IsEmptyTable(invoke_info) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.CurXueZhuOver)
			return
		end

		local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
		-- if state == ASSIST_STATUS.ASSIST_STATUS_INVOKE then
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.IsZhaoJiState)
		-- 	return
		-- end

		local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
	    local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
	    local can_xiezhu = xiezhu_num < xiezhu_count
	    if can_xiezhu then
	    	BossXiezhuWGCtrl.Instance:SendXiezhuBossInfo(invoke_info)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.XiezhuNotTimer)
	    end
	elseif link_type == 941 or link_type == 942 or link_type == 943 then
		local open_territorial = tonumber(param1) or -1
		FunOpen.Instance:OpenViewByName(GuideModuleName.CountryMapMapView, nil, {open_territorial = open_territorial + 1})
	elseif link_type == CHAT_LINK_TYPE.QIHUN_BUY_LINK then
		local lingzhi_type = tonumber(param1) or 0
		local fun_name = LingZhiSkillWGData.FunName[lingzhi_type]
		local fun_is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
		if not fun_is_open then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FunOpenTip)
			return
		end
		local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
		if not server_info then
			return
		end
		local is_open = server_info.is_open == 1
		if not is_open then
			if lingzhi_type == LINGZHI_SKILL_TYPE.WING then
				ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_wing)
			elseif lingzhi_type == LINGZHI_SKILL_TYPE.FABAO then
				ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_fabao)
			elseif lingzhi_type == LINGZHI_SKILL_TYPE.JIANZHEN then
				ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_jianzhen)
			elseif lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
				ViewManager.Instance:Open(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_upgrade_shenbing)
			end
			LingZhiWGCtrl.Instance:OpenLingZhiZhiGouTip(server_info)
			return
		end
		if lingzhi_type == LINGZHI_SKILL_TYPE.WING then
			ViewManager.Instance:Open(GuideModuleName.WingLinZhiSkillView,TabIndex.lingzhi_ql_up)
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.FABAO then
			ViewManager.Instance:Open(GuideModuleName.FaBaoLinZhiSkillView,TabIndex.lingzhi_ql_up)
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.JIANZHEN then
			ViewManager.Instance:Open(GuideModuleName.JianZhenLinZhiSkillView,TabIndex.lingzhi_ql_up)
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
			ViewManager.Instance:Open(GuideModuleName.ShenBingLinZhiSkillView,TabIndex.lingzhi_ql_up)
		end
	elseif link_type == CHAT_LINK_TYPE.GUILD_HEBING_MAIL_LINK then
		local tab_index = TabIndex.guild_info
		if RoleWGData.Instance.role_vo.guild_id == 0 then
			tab_index = TabIndex.guild_guildlist
		end
		GuildWGCtrl.Instance:Open(tab_index)
	elseif link_type == CHAT_LINK_TYPE.MERGE_FIREWORK_JULING then
		if HeFuJuLingWGData.Instance:GetHeFuZhuLingIsOpen() then
			ViewManager.Instance:Open(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2127)
			HeFuJuLingWGData.Instance:SetGardenFlag(1)
			local uid = tonumber(param1)
			HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_CONFIRM_INVITE, uid)
			HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_OTER_INFO_REQ, uid)
			HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING_LOG_REQ, uid)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.RoleLevelNotOpenActicity)
		end
	elseif link_type == CHAT_LINK_TYPE.BILLION_DEZG then
		local uid = tonumber(param1)
		local item_seq = tonumber(param2)
		local grade = tonumber(param3)
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.HIGH_PRICE_RMB_BUY_HELP, uid, item_seq, grade)
		ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_dezg, "jump_tab", {jump_tab = item_seq})
	elseif link_type == CHAT_LINK_TYPE.BILLION_DRPT then
		local item_seq = 0
		local show_day = 0
		local split_list = string.split(param1, ",")			--uid,grade,seq,show_day

		if #split_list > 1 then
			item_seq = tonumber(split_list[3])
			show_day = tonumber(split_list[4])
		end

		local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local is_show = server_day == show_day
		if not is_show then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BillionSubsidy.DRPTNotOpenAct)
			return
		end

		ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_drpt, "jump_tab", {jump_tab = item_seq})
	elseif link_type == CHAT_LINK_TYPE.CROSSTREASURE_BEAST then
		ViewManager.Instance:Open(GuideModuleName.CrossTreasureView)	
	elseif link_type == CHAT_LINK_TYPE.XUNYUAN_LINK then
		ViewManager.Instance:Open(GuideModuleName.ProfessWallView, TabIndex.profess_wall_xunyuan, "jump_page", {page = tonumber(param1)})
	elseif link_type == CHAT_LINK_TYPE.DUJIE then
		DujieWGCtrl.Instance:GotoDujieArea()
	else
		local params = nil
		if link_cfg.key and link_cfg.key ~= "" then
			params = {link_cfg_key = link_cfg.key}
		end
		FunOpen.Instance:OpenViewByName(link_cfg.view_name, link_cfg.tab_index, params)
	end
end

function EmojiTextUtil.AddTallMyCodeTime(time)
	EmojiTextUtil.tall_my_code_time = time
end

function EmojiTextUtil.GetTallMyCodeTime()
	return EmojiTextUtil.tall_my_code_time or 0
end


local EmojiMask = "[%s]"
function EmojiTextUtil.ToEmojiStr(index)
	return string.format(EmojiMask, index)
end

local UnderLineMask = "<link=%d><u>%s</u></link>"
function EmojiTextUtil.ToUnderLineStr(content)
	return content
	-- return string.format(UnderLineMask, content)
end

local ButtonMask = "<link=%d>%s</link>"
function EmojiTextUtil.ToButtonStr(content)
	return string.format(ButtonMask, EmojiTextUtil.ButtonIndex, content)
end

function EmojiTextUtil.ToButtonStrIndex(content)
	return string.format(UnderLineMask, EmojiTextUtil.ButtonIndexLine, content)
end

function EmojiTextUtil.ToButtonStrIndexByFix(content)
	return string.format(UnderLineMask, 1001, content)
end

function EmojiTextUtil.ParseGatherFengLing(emoji_text, params, font_size, color, rich_content_type)-- ignored_link, text_attr, bigchatface_status)
	local point_text = params[2]
	local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
            BossWGData.Instance:ClearCurSelectBossID()
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:ClearGuaJiInfo()
            end
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None) --设置不自动挂机，防止寻路过程中回去打怪
			HandleClickPoint.CheckSceneIdOperate(tonumber(params[3]), {0,tonumber(params[3]),tonumber(params[4]),tonumber(params[5])})
		end, index)
	end
	is_bag_item = false
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, point_text,font_size, nil, callback, nil, true, rich_content_type,true)
end

function EmojiTextUtil.PraseGatherPos(emoji_text, params, font_size, color, rich_content_type)-- ignored_link, text_attr, bigchatface_status)
	local point_text = params[2]
	color = EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, true)
	EmojiTextUtil.AddText(emoji_text, params[2], color)
end

function EmojiTextUtil.ParseBossConvene(emoji_text, params, font_size, color, rich_content_type)-- ignored_link, text_attr, bigchatface_status)
	local point_text = params[2]
	local function callback(index)
        local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
        	BossWGData.Instance:TryGoToConveneBoss(params) 
        end,index)
	end
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, point_text,font_size, nil, callback, nil, true, rich_content_type,true)
end

function EmojiTextUtil.PraseBossConvenePos(emoji_text, params, font_size, color, rich_content_type)
	local scene_id = params[2]
	local scene_config = ConfigManager.Instance:GetSceneConfig(scene_id)
	if IsEmptyTable(scene_config) then
		print_error("没有配置scene_id=", scene_id)
		return
	end
	local scene_name = scene_config.name
	local str = scene_name--.."("..params[3]..","..params[4]..")"
	color = EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, true)
	EmojiTextUtil.AddText(emoji_text, str, color)
end

function EmojiTextUtil.PraseBossConveneName(emoji_text, params, font_size, color, rich_content_type)
	color = EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, true)
	EmojiTextUtil.AddText(emoji_text, params[2], color)
end

function EmojiTextUtil.ParseWealthGodPer(emoji_text, params, font_size, color, rich_content_type)
	color = EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, true)
	local per = params[2]
	EmojiTextUtil.AddText(emoji_text, per, color)
end

function EmojiTextUtil.ParseArtifactNotice(emoji_text, params, font_size, color, rich_content_type)
	color = EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, true)
	local per = tonumber(params[2]) or 0
	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(per)
	local name = artifact_cfg and artifact_cfg.name or ""
	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			ViewManager.Instance:Open(GuideModuleName.ArtifactView)
		end, index)
	end
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, name, font_size, nil, callback, nil, true, rich_content_type, true)
end

function EmojiTextUtil.ParseFetterNotice(emoji_text, params, font_size, color, rich_content_type)
	color = EmojiTextUtil.GetUnderTextColorByContentType(rich_content_type, true)
	local per = tonumber(params[2]) or 0
	local fetter_cfg = ArtifactWGData.Instance:GetFetterCfg(per)
	local name = fetter_cfg and fetter_cfg.skill_name or ""

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			ViewManager.Instance:Open(GuideModuleName.ArtifactView)
		end, index)
	end
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, name, font_size, nil, callback, nil, true, rich_content_type, true)
end

-- 解析数字
function EmojiTextUtil.ParseNumber(emoji_text, params, font_size, color)
	local number = tonumber(params[2]) or 0
	EmojiTextUtil.AddText(emoji_text, number, color)
end

function EmojiTextUtil.ParseFGBCampNotice(emoji_text, params, font_size, color, rich_content_type)
	local camp = tonumber(params[2])
	local camp_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(camp)
	local team_name = camp_cfg and camp_cfg.camp_name or ""
	EmojiTextUtil.AddText(emoji_text, team_name)
end

-- 武魂塔类型
function EmojiTextUtil.ParseWuHunTowerNotice(emoji_text, params, font_size, color, rich_content_type)
	local tower_type = tonumber(params[2])
	local tower_type_name = Language.WuHunZhenShen.WuhunTowerTypeGrop[tower_type]
	EmojiTextUtil.AddText(emoji_text, tower_type_name)
end

function EmojiTextUtil.ParseBeastName(emoji_text, params, font_size, color, rich_content_type)
	local beast_id = tonumber(params[2]) or 0
	local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
	local beast_name = cfg and cfg.beast_name or ""

	local function callback(index)
		local tmp_link_opener = emoji_text:GetComponent(typeof(TMPTextLinkOpener))
		tmp_link_opener:AddClickListener(function()
			TipWGCtrl.Instance:OpenItem({item_id = beast_id})
		end, index)
	end
	EmojiTextUtil.CreateUnderLineBtnNew(emoji_text, beast_name, font_size, COLOR3B.QUALITY_COLOUR, callback, nil, true, rich_content_type, true)
	--EmojiTextUtil.AddText(emoji_text, beast_name, COLOR3B.QUALITY_COLOUR)
end