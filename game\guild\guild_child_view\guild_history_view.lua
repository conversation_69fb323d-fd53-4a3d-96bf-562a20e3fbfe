-- 仙盟历史记录
function GuildView:InitHistoryView()
	self:CreateHistoryList()
end

function GuildView:DeleteHistoryView()
	if self.history_list then 
		self.history_list:DeleteMe()
		self.history_list = nil
	end
end

-- 创建历史记录列表
function GuildView:CreateHistoryList()
	if nil == self.history_list then
		self.history_list = AsyncListView.New(GuildHistoryItem, self.node_list.layout_lishijilu.ph_info_historylist)
	end
end

-- 刷新历史记录数据
function GuildView:FlushHistoryListDatasource()
	local event_list = GuildDataConst.GUILD_EVENT_LIST
	local event_show_list = GuildDataConst.SHOW_EVENT_LIST
	self.history_datasource = {}
	for i=1, event_list.count do
		local item = event_list.list[event_list.count - i + 1]
		if nil ~= event_show_list[item.event_type] then
			local datasource = {event_type = item.event_type, owner_post = item.event_owner_post, owner = item.event_owner, time = item.event_time, big_event = item.big_event, param0 = item.param0, param1 = item.param1, param2 = item.param2, param3 = item.param3, sparam0 = item.sparam0}
			table.insert(self.history_datasource, datasource)
		end	
	end

	if nil ~= self.history_list then
		table.sort(self.history_datasource, SortTools.KeyUpperSorter('time'))
		self.history_list:SetDataList(self.history_datasource)
	end
end

function GuildView:OnFlushHistoryView()
	self:FlushHistoryListDatasource()
	self.history_list:JumpToTop()
end