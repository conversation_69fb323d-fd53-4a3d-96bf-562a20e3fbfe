function ShiTianSuitStrengthenView:GemstoneLoadCallBack()
    XUI.AddClickEventListener(self.node_list.gem_resonance_btn, BindTool.Bind(self.OnClickResonanceBtn, self))
    XUI.AddClickEventListener(self.node_list.inlay_btn, BindTool.Bind(self.OnClickOneKeyInlayBtn, self))

    if not self.gemstone_list then
        self.gemstone_list = {}
        for i = 1, 6 do
            self.gemstone_list[i] = ShiTianStoneRender.New(self.node_list["gemstone_item_" .. i])
            self.gemstone_list[i]:SetIndex(i)
        end
    end
end

function ShiTianSuitStrengthenView:GemstoneReleaseCallBack()
    if self.gemstone_list then
        for k, v in pairs(self.gemstone_list) do
            v:DeleteMe()
        end

        self.gemstone_list = nil
    end
end

function ShiTianSuitStrengthenView:GemstoneOnFlush()
    if not self.cur_select_ep_data then
        return
    end

    local ep_bundle, ep_asset = ResPath.GetF2RawImagesPNG("a2_lhtz_epimg_" .. self.cur_select_ep_data.suit_seq .. "_" .. self.cur_select_ep_data.part)
    self.node_list.gemstone_target_ep.raw_image:LoadSprite(ep_bundle, ep_asset, function()
        self.node_list.gemstone_target_ep.raw_image:SetNativeSize()
    end)

    self:FlushStonePanel()
end

function ShiTianSuitStrengthenView:FlushStoneTotalLevelPanel()
    if not self.cur_select_ep_data then
        return
    end

    local total_level_remind = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(self.cur_select_ep_data.suit_seq, SHOW_TYPE.Gemstone)
    local total_is_max = ShiTianSuitStrengthenWGData.Instance:GetCurTotalCanActiveOrIsMax(self.cur_select_ep_data.suit_seq, SHOW_TYPE.Gemstone, true)
    self.node_list.gem_resonance_remind:SetActive(not total_is_max and total_level_remind)
end

function ShiTianSuitStrengthenView:FlushStonePanel(is_update)
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    if is_update then
        local is_compose = ShiTianSuitStrengthenWGData.Instance:GetIsCompose()
        local effect_type = is_compose > 0 and UIEffectName.s_shengji or UIEffectName.s_xiangqian
        TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
							is_success = true, pos = Vector2(0, 0), parent_node = self.node_list.gemstone_effect})
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))

        local jump_ep_index, is_remind_jump = ShiTianSuitStrengthenWGData.Instance:GetOneRemindEpIndex(suit_seq, part, SHOW_TYPE.Gemstone)
        if jump_ep_index ~= part and is_remind_jump then
            self.shitian_ep_list:JumpToIndex(jump_ep_index)
            return
        end
    end

    self:FlushStoneTotalLevelPanel()
    local stone_data_list = ShiTianSuitStrengthenWGData.Instance:GetStoneDataList(suit_seq, part)
    for k, v in pairs(self.gemstone_list) do
        local stone_data = {
            stone_id = stone_data_list[k - 1] or 0,
            suit_seq = suit_seq,
            part = part
        }

        v:SetData(stone_data)
    end
end

function ShiTianSuitStrengthenView:OnClickOneKeyInlayBtn()
    if not self.cur_select_ep_data then
        return
    end

    local suit_seq = self.cur_select_ep_data.suit_seq
    local part = self.cur_select_ep_data.part
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    if not is_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.NoActiveShiTianEp)
        return
    end

    local list = ShiTianSuitStrengthenWGData.Instance:GetBaoShiOneKeyInlay(suit_seq, part)
	if IsEmptyTable(list) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Equipment.InlayBSNoBetterTips)
		return
	end

    ShiTianSuitStrengthenWGCtrl.Instance:SendCSShiTianOneKeyInlayReq(suit_seq, part, #list, list)
end

-------------- 宝石孔位 --------------
ShiTianStoneRender = ShiTianStoneRender or BaseClass(BaseRender)

function ShiTianStoneRender:LoadCallBack()
    self.stone_type = -1
    XUI.AddClickEventListener(self.node_list.add_btn, BindTool.Bind(self.OnClickAddBtn, self))
end

function ShiTianStoneRender:OnFlush()
    if not self.data then
        return
    end

    self.stone_type = ShiTianSuitStrengthenWGData.Instance:GetEquipGemTypeBySlot(self.data.part).shitianstone_type or -1
    local stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(self.data.stone_id)
    local has_ep_stone = not IsEmptyTable(stone_cfg)
    self.node_list.gemstone:SetActive(has_ep_stone)
    self.node_list.empty_img:SetActive(not has_ep_stone)
    self.node_list.attr_bg:SetActive(has_ep_stone)

    local attr_des1, attr_des2, grade_str
    if has_ep_stone then
        local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(self.data.stone_id))
        self.node_list.gemstone.image:LoadSprite(bundle, asset, function()
            self.node_list.gemstone.image:SetNativeSize()
        end)

        local cur_attr_list = EquipWGData.GetSortAttrListByTypeCfg(stone_cfg, "attr_id", "attr_val", 1, 2)
        attr_des1 = ShiTianSuitStrengthenWGData.GetStoneDescForCfg(cur_attr_list[1].attr_str, cur_attr_list[1].attr_value)
        attr_des2 = ShiTianSuitStrengthenWGData.GetStoneDescForCfg(cur_attr_list[2].attr_str, cur_attr_list[2].attr_value)
        grade_str = CommonDataManager.GetDaXie(stone_cfg.level)
    end

    self.node_list.attr1.text.text = attr_des1 and attr_des1 or ""
    self.node_list.attr2.text.text = attr_des2 and attr_des2 or ""
    self.node_list.grade.text.text = grade_str and grade_str or ""

    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(self.data.suit_seq, self.data.part)
    local stone_list = ItemWGData.Instance:GetShiTianStoneItemList(self.data.suit_seq, self.stone_type)
    local has_better_stone = ShiTianSuitStrengthenWGData.Instance:GetCurHasBetterStone(self.data.suit_seq, self.stone_type, self.data.stone_id)
    local can_compose = ShiTianSuitStrengthenWGData.Instance:ComputBaoShiUpgradePrice(self.data.stone_id) <= 0
    self.node_list.better_flag:SetActive(has_ep_stone and is_act and (has_better_stone or can_compose))
    self.node_list.remind:SetActive(not has_ep_stone and #stone_list > 0 and is_act)
end

function ShiTianStoneRender:OnClickAddBtn()
    if self.stone_type == -1 then
        return
    end

    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(self.data.suit_seq, self.data.part)
    if not is_act then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.NoActiveShiTianEp)
        return
    end

    local stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(self.data.stone_id)
    local show_data = {suit_seq = self.data.suit_seq, part = self.data.part, slot = self.index - 1, stone_type = self.stone_type, ep_stone_id = self.data.stone_id}
    if stone_cfg then
        local is_max = ShiTianSuitStrengthenWGData.Instance:GetStoneIsMax(stone_cfg.shitianstone_type, self.data.stone_id)
        if is_max then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ShiTianSuit.MaxMsg[SHOW_TYPE.Gemstone])
            return
        end

        local has_better_stone = ShiTianSuitStrengthenWGData.Instance:GetCurHasBetterStone(self.data.suit_seq, self.stone_type, self.data.stone_id)
        if has_better_stone then
            ShiTianSuitStrengthenWGCtrl.Instance:OpenInlayStoneView(show_data)
            return
        end

        ShiTianSuitStrengthenWGCtrl.Instance:OpenStoneComposeView(show_data)
    else
        local stone_list = ItemWGData.Instance:GetShiTianStoneItemList(self.data.suit_seq, self.stone_type)
        if IsEmptyTable(stone_list) then
            local item_id = ShiTianSuitStrengthenWGData.Instance:GetDefaultStone(self.data.suit_seq, self.stone_type).stone_id or 0
            TipWGCtrl.Instance:OpenItem({item_id = item_id})
            return
        end

        ShiTianSuitStrengthenWGCtrl.Instance:OpenInlayStoneView(show_data)
    end
end