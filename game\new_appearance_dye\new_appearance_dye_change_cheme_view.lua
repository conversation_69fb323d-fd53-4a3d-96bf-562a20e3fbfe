-- 染色方案改名
NewAppearanceDyeChangeChemeView = NewAppearanceDyeChangeChemeView or BaseClass(SafeBaseView)
local TITLE_LIMIT = 4
local DESC_LIMIT = 20

function NewAppearanceDyeChangeChemeView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(554, 412)})
    self:AddViewResource(0, "uis/view/new_appearance_dye_prefab", "layout_fashion_dye_change_cheme")
    self:SetMaskBg(true)
end

function NewAppearanceDyeChangeChemeView:ReleaseCallBack()
    self.cur_scheme_data = nil
end

function NewAppearanceDyeChangeChemeView:SetCurSchemeData(cur_scheme_data)
    self.cur_scheme_data = cur_scheme_data
end

function NewAppearanceDyeChangeChemeView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewAppearanceDye.SchemeChangeTitle
    XUI.AddClickEventListener(self.node_list.btn_change_cheme, BindTool.Bind2(self.ChangeCheme, self))
    XUI.AddClickEventListener(self.node_list.btn_change_cancel, BindTool.Bind2(self.Close, self))
    self.node_list.cheme_title.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnChemeTitleEndEdit, self))
    self.node_list.cheme_desc.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnChemeDescEndEdit, self))
end

function NewAppearanceDyeChangeChemeView:OnFlush()
    if not self.cur_scheme_data then
        return
    end

    local cheme_index = self.cur_scheme_data.pro_cfg.project_id + 1
    local name_str = self.cur_scheme_data.pro_cfg and self.cur_scheme_data.pro_cfg.project_title or "" 
    local desc_str = self.cur_scheme_data.pro_cfg and self.cur_scheme_data.pro_cfg.project_desc or ""

    if self.cur_scheme_data.dyeing_info and self.cur_scheme_data.dyeing_info.name ~= "" then
        name_str = self.cur_scheme_data.dyeing_info.name
    end

    if self.cur_scheme_data.dyeing_info and self.cur_scheme_data.dyeing_info.info ~= "" then
        desc_str = self.cur_scheme_data.dyeing_info.info
    end
  
    if name_str == "" then
        name_str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(self.index))
    end

    if desc_str == "" then
        desc_str = string.format(Language.NewAppearanceDye.SchemeTips, NumberToChinaNumber(self.index))
    end

    self.node_list.cheme_title.input_field.text = name_str
    self.node_list.cheme_desc.input_field.text = desc_str
    local len = string.utf8len(name_str)
    self.node_list.cheme_title_limit.text.text = string.format("%d/%d", len, TITLE_LIMIT)
    len = string.utf8len(desc_str)
    self.node_list.cheme_desc_limit.text.text = string.format("%d/%d", len, DESC_LIMIT)
end

function NewAppearanceDyeChangeChemeView:OnChemeTitleEndEdit()
    local name_str = self.node_list.cheme_title.input_field.text
    local len = string.utf8len(name_str)
    self.node_list.cheme_title_limit.text.text = string.format("%d/%d", len, TITLE_LIMIT)
end

function NewAppearanceDyeChangeChemeView:OnChemeDescEndEdit()
    local name_str = self.node_list.cheme_desc.input_field.text
    local len = string.utf8len(name_str)
    self.node_list.cheme_desc_limit.text.text = string.format("%d/%d", len, DESC_LIMIT)
end
--------------------------------------------------------------------------------
-- 更改描述
function NewAppearanceDyeChangeChemeView:ChangeCheme()
    if not self.cur_scheme_data then
        return
    end

    local name_str = self.node_list.cheme_title.input_field.text
    local name_desc = self.node_list.cheme_desc.input_field.text
    NewAppearanceDyeWGCtrl.Instance:CSShizhuangDyeingChangeInfoOper(self.cur_scheme_data.seq, self.cur_scheme_data.pro_cfg.project_id, name_str, name_desc)
    self:Close()
end

