RebateFireWorksView = RebateFireWorksView or BaseClass(SafeBaseView)

function RebateFireWorksView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/rebate_gift_ui_prefab", "rebate_fire_works_view")
end

function RebateFireWorksView:LoadCallBack()
    for i = 1, 2 do
        self.node_list["fireworks_btn_draw_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, i))
        self.node_list["fireworks_btn_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self))
    end
    XUI.AddClickEventListener(self.node_list["fireworks_btn_range"], BindTool.Bind1(self.OnClickGaiLvShow, self))

    self.fireworks_item_data_change = BindTool.Bind(self.OnDrawItemChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.fireworks_item_data_change)

    if nil == self.fireworks_show_cell then
        self.fireworks_show_cell = {}
        for i = 1, 10 do
            self.fireworks_show_cell[i] = ItemCell.New(self.node_list["show_item_" .. i])
        end
    end

    -- if self.draw_item == nil then
    --     self.draw_item = ItemCell.New(self.node_list["fireworks_item_pos"])
    -- end
end

function RebateFireWorksView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("rebate_fireworks_down") then
		CountDownManager.Instance:RemoveCountDown("rebate_fireworks_down")
	end

    if self.fireworks_item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.fireworks_item_data_change)
        self.fireworks_item_data_change = nil
    end

    if self.fireworks_show_cell then
        for k, v in pairs(self.fireworks_show_cell) do
            v:DeleteMe()
        end
        self.fireworks_show_cell = nil
    end

    -- if self.draw_item then
    --     self.draw_item:DeleteMe()
    --     self.draw_item = nil
    -- end
end

function RebateFireWorksView:OpenCallBack()
    RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW, FIREWORKS_DRAW_OPERATE_TYPE.INFO)
end

function RebateFireWorksView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushshowView()
		end
	end

    self:LoginTimeCountDown()
end

function RebateFireWorksView:FlushshowView()
    self:FlushDrawBtnShow()

    local show_list = RebateGiftActivityWGData.Instance:GetFireWorksRewardShowCfg()
    --珍稀奖励展示
    if show_list then
        for i = 1, #self.fireworks_show_cell do
            if show_list[i] and show_list[i].item then
                self.fireworks_show_cell[i]:SetData(show_list[i].item)
            else
                self.fireworks_show_cell[i]:SetActive(false)
            end
        end
    end

    --抽奖道具
    -- local draw_item_cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawItem()
    -- if draw_item_cfg or draw_item_cfg.cost_item_id > 0 then
    --     self.draw_item:SetData({item_id = draw_item_cfg.cost_item_id})
    -- end

    local baodi_times = RebateGiftActivityWGData.Instance:GetFireWorksBaoDiDrawTimes()
    local is_show_baodi = RebateGiftActivityWGData.Instance:GetFireWorksDrawIsShowBaodiTimes()
    local is_baodi = baodi_times == -1
    if baodi_times > 0 then
        self.node_list["baodi_txt"].text.text = string.format(Language.RebateGiftAct.FireWorksDrawTimes, baodi_times)
    end

    self.node_list["baodi_txt"]:SetActive(not is_baodi and is_show_baodi)
end

--抽奖
function RebateFireWorksView:OnClickRecord(draw_type) --抽奖
    local cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
    --检查道具数量
    if num >= cfg.cost_item_num then
        RebateGiftActivityWGData.Instance:CacheOrGetDrawIndex(draw_type)
        --发送协议
        RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(
            ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW, 
            FIREWORKS_DRAW_OPERATE_TYPE.DRAW,
            cfg.mode
        )
    else
        RebateGiftActivityWGCtrl.Instance:ClickUseDrawItem(draw_type, function ()
            self:OnClickDrawBuy(draw_type)
        end)
    end
end

function RebateFireWorksView:OnClickGaiLvShow() --抽奖概率
    RebateGiftActivityWGCtrl.Instance:OpenGaiLvView()
end

function RebateFireWorksView:FlushDrawBtnShow(is_flush_num) --刷新抽奖次数
    local cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawConsumeCfg()
    if cfg == nil then
        return
    end

    local item_cfg
    local mode_cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawItem()
    local item_id = mode_cfg.cost_item_id
    local count
    for i = 1, 2 do
        if cfg[i] then
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then
                    --道具图标
                    self.node_list["fireworks_btn_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                end
                --抽几次
                self.node_list["fireworks_txt_buy_" .. i].text.text = string.format(Language.RebateGiftAct.DrawBtnDesc, cfg[i].times)
                -- --折扣
                --local is_zhekou = cfg[i].count ~= cfg[i].cost_item_num
                --self.node_list["btn_discount_" .. i]:SetActive(is_zhekou)
                -- if is_zhekou then
                --     self.node_list["txt_lh_discount_" .. i].text.text = cfg[i].cost_item_num .. "折"
                -- end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["fireworks_btn_red_" .. i]:SetActive(count >= cfg[i].cost_item_num)
            local color = count >= cfg[i].cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_PINK
            local left_str = ToColorStr(count, color) 
            self.node_list["fireworks_btn_num_" .. i].text.text = left_str .."/".. cfg[i].cost_item_num
            self.node_list["fireworks_btn_num_"..i].text.color = Str2C3b(color)

            --self.node_list["item_num"].text.text = string.format(Language.RebateGiftAct.FireWorksItemNum, left_str)
        end
    end
end

function RebateFireWorksView:ShowDrawItemTips()
    local cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawItem()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.cost_item_id})
end

--点击购买
function RebateFireWorksView:OnClickDrawBuy(draw_type)
    local cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawConsumeCfg()
    local item_cfg = RebateGiftActivityWGData.Instance:GetFireWorksDrawItem()
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.cost_item_id)
    local consume = item_cfg.cost_gold * (cur_cfg.cost_item_num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        RebateGiftActivityWGData.Instance:CacheOrGetDrawIndex(draw_type)
		--发送协议
        RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(
            ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW, 
            FIREWORKS_DRAW_OPERATE_TYPE.DRAW,
            cur_cfg.mode
        )
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--物品监听刷新按钮显示
function RebateFireWorksView:OnDrawItemChange(item_id)
    local check_list = RebateGiftActivityWGData.Instance:GetFireWorksItemDataChangeList()
    if check_list ~= nil then
        for i, v in pairs(check_list) do
            if v == item_id then
                self:FlushDrawBtnShow(true)
                return
            end
        end
    end
end

------------------------------------活动时间倒计时
function RebateFireWorksView:LoginTimeCountDown()
 --    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW)
	-- if activity_data ~= nil then
	-- 	local invalid_time = activity_data.end_time
 --        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
 --            self.node_list["fireworks_time_down"].text.text = TimeUtil.FormatSecondDHM(invalid_time - TimeWGCtrl.Instance:GetServerTime())
 --            CountDownManager.Instance:AddCountDown("rebate_fireworks_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
 --        end
	-- end
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("rebate_fireworks_down") then
            CountDownManager.Instance:RemoveCountDown("rebate_fireworks_down")
        end

        CountDownManager.Instance:AddCountDown("rebate_fireworks_down", 
            BindTool.Bind(self.UpdateCountDown, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function RebateFireWorksView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list["fireworks_time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function RebateFireWorksView:OnComplete()
    self.node_list["fireworks_time_down"].text.text = ""
end
--------------------------------------------------------