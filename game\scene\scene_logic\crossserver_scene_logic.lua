CrossServerSceneLogic = CrossServerSceneLogic or BaseClass(CommonActivityLogic)

function CrossServerSceneLogic:__init()
	
end

function CrossServerSceneLogic:__delete()

end

function CrossServerSceneLogic:Enter(old_scene_type, new_scene_type)	
	CommonActivityLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		ViewManager.Instance:CloseAll()
	end
end

function CrossServerSceneLogic:Out()
	CommonActivityLogic.Out(self)
end