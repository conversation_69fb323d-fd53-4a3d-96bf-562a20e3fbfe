TianShenJuexingWGData = TianShenJuexingWGData or BaseClass()
TianShenJuexingWGData.AWAKE_OPER_TYPE = {
    AWAKE_OPER_TYPE_FETCH_REWARD = 0,
    AWAKE_OPER_TYPE_SEND_INFO = 1,
    TIANSHEN_AWAKEN = 2, --激活天神
}

function TianShenJuexingWGData:__init()
    if TianShenJuexingWGData.Instance then
		Error<PERSON><PERSON>("[TianShenJuexingWGData] attempt to create singleton twice!")
		return
	end

    TianShenJuexingWGData.Instance = self
    local cfg = ConfigManager.Instance:GetAutoConfig("tianshenawake_cfg_auto")
    local other_cfg = cfg.other[1]
    self.awaken_task_cfg = cfg.task_cfg
    self.awaken_other_cfg = other_cfg
    self.right_reward_item = other_cfg.model_id
    self.show_skill_id = other_cfg.skill_id
    RemindManager.Instance:Register(RemindName.TianShenAwaken, BindTool.Bind(self.GetAwakenRemind, self))
end

function TianShenJuexingWGData:__delete()
    TianShenJuexingWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.TianShenAwaken)

    self.show_skill_id = nil
    self.tianshen_awake_tequan = nil
end

function TianShenJuexingWGData:GetOtherCfg()
    return self.awaken_other_cfg
end

function TianShenJuexingWGData:GetTaskListInfo()
    if IsEmptyTable(self.task_list) then
        self.task_list = {}
        for k, v in ipairs(self.awaken_task_cfg) do
            local data = {
                task_id = v.task_id,
                param1 = v.param1,
                param2 = v.param2,
                award_attr = v.award_attr,
                task_description = v.task_description,
                name = v.name,
                goto_module = v.goto_module,
                story_des = v.story_des,
                task_process = 0,
                is_complete = false,
                is_get = false,
                reward_item = v.reward_item
            }

            self.task_list[data.task_id] = data
        end
    end

    local info = self:GetAwakenInfo()
    for k, v in pairs(self.task_list) do
        if info and info.task_progress ~= nil then
            local task_progress_data = info.task_progress[v.task_id]
            if task_progress_data ~= nil then
                v.task_process = task_progress_data.task_process
                v.is_complete = task_progress_data.is_complete
                v.is_get = task_progress_data.is_get
            end
        end
    end

    local function task_sort(a, b)
        if a.is_get ~= b.is_get then
            return not a.is_get and b.is_get
        elseif a.is_complete ~= b.is_complete then
            return a.is_complete and not b.is_complete
        else
            return a.task_id < b.task_id
        end
    end

    table.sort(self.task_list, task_sort)
    return self.task_list
end

function TianShenJuexingWGData:SetAwakenInfo(protocol)
    self.awaken_info = protocol
end

function TianShenJuexingWGData:GetAwakenInfo()
    return self.awaken_info
end

function TianShenJuexingWGData:GetIsBuyGift()
    local data = self:GetAwakenInfo()
   return data and data.is_buy_gift or false
end

--所有任务是否完成
function TianShenJuexingWGData:GetTianShenAwakenIsCompleted()
    local data = self:GetAwakenInfo()
    return data and data.is_all_complete or false
end

-- 是否能激活功能
function TianShenJuexingWGData:GetIsCanActive()
    local data = self:GetAwakenInfo()
    if data then
        return (data.is_all_complete or data.is_buy_gift) and data.is_all_get
    end

    return false
end

function TianShenJuexingWGData:GetActiveBtnRemind()
   local data = self:GetAwakenInfo()
   if data and data.open_flag == 1 and (data.is_all_complete or data.is_buy_gift) and not data.is_active then
        return 1
    end

   return 0
end

function TianShenJuexingWGData:GetAwakenRemind()
    local data = self:GetAwakenInfo()
    if not data or data.open_flag == 0 then
        return 0
    end

    if (data.is_all_complete or data.is_buy_gift) and data.is_active then
        return 1
    end

    for k,v in pairs(data.task_progress) do
        if v.is_complete and not v.is_get then
            return 1
        end
    end

    return 0
end

-- 任务进度
-- 已完成数，总数
function TianShenJuexingWGData:GetTaskProgess()
    local data = self:GetAwakenInfo()
    local complete_num = 0
    if data then
        for k,v in pairs(data.task_progress) do
            if v.is_complete then
                complete_num = complete_num + 1
            end
        end
    end

    return complete_num, #self.awaken_task_cfg
end

function TianShenJuexingWGData:GetBossStrColor(cur_page,boss_index)
    local selected_data = self:GetTaskListInfo()[cur_page]
    if not IsEmptyTable(selected_data) then
        local flag = bit:d2b_two(selected_data.task_process)
        local color = flag[boss_index - 1] == 1 and COLOR3B.GREEN or COLOR3B.RED
        color = selected_data.is_complete and COLOR3B.GREEN or color
        return color
    end

    return COLOR3B.GREEN
end

--所有任务奖励是否领取
function TianShenJuexingWGData:GetTianShenAwakenIsFetched()
    local data = self:GetAwakenInfo()
    return data and data.is_all_get or false
end

function TianShenJuexingWGData:GetTianShenAwakenIsActive()
    local data = self:GetAwakenInfo()
    return data and data.is_active or false
end

-- 活动结束
function TianShenJuexingWGData:GetTianShenAwakenIsEnd()
    local data = self:GetAwakenInfo()
    local time = data and data.close_time or 0
    local flag = time < TimeWGCtrl.Instance:GetServerTime()
    return flag
end

function TianShenJuexingWGData:GetShenWuFuncIsOpen()
    return self:GetTianShenAwakenIsEnd() or self:GetTianShenAwakenIsActive()
end

--主界面按钮 为true 隐藏 
function TianShenJuexingWGData:GetMainTianShenAwakenBtnState()
    return (self:GetTianShenAwakenIsActive() and self:GetTianShenAwakenIsFetched()) or self:GetTianShenAwakenIsEnd()
end

function TianShenJuexingWGData:TianshenTequan()
    if not self.tianshen_awake_tequan then
        self.tianshen_awake_tequan = ConfigManager.Instance:GetAutoConfig("cangjinshangpu_cfg_auto").other[1].tianshen_awake_tequan
    end

    -- return "CangJinExchangeView#cangjin_exchange_diamond_tequan##sel_index=" .. self.tianshen_awake_tequan
    return "YanYuGePrivilegeView#yanyuge_privilege_yytq"
end