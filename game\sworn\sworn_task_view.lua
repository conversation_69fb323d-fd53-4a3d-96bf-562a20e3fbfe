function SwornView:InitSwornTaskView()
    for i = 0, 1 do
        self.node_list["task_toggle_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickSwornTaskToggle, self, i))
    end

    if not self.task_active_reward_list then
        self.task_active_reward_list = AsyncListView.New(SwornTaskActiveRewardCell, self.node_list["task_week_box_list"])
    end

    if not self.build_show_task_list then
        self.build_show_task_list = AsyncListView.New(SwornBuildTaskRender, self.node_list["task_show_list"])
    end

    XUI.AddClickEventListener(self.node_list.info_btn, BindTool.Bind(self.OpenSwornRuleTip, self))

    self.select_task_index = nil
    self:FlushSwornTaskTimeCount()
end

function SwornView:ShowSwornTaskView()

end

function SwornView:SwornTaskViewReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("sworn_build_task_time") then
        CountDownManager.Instance:RemoveCountDown("sworn_build_task_time")
    end

    if self.task_active_reward_list then
		self.task_active_reward_list:DeleteMe()
		self.task_active_reward_list = nil
	end

    if self.build_show_task_list then
        self.build_show_task_list:DeleteMe()
        self.build_show_task_list = nil
    end

    self.select_task_index = nil
end

function SwornView:FlushSwornTaskView()
    if not self.select_task_index then
        self:TaskDefaultSelectToggle()
    end

    self:FlushSwornTaskOtherInfo()
    self:FlushSwornTaskShowList()
end

function SwornView:FlushSwornTaskOtherInfo()
    local build_active_sum = SwornWGData.Instance:GetBuildActiveValue()
    self.node_list.task_week_value.text.text = string.format(Language.Sworn.BuildActiveValue, build_active_sum)

    local build_active_reward = SwornWGData.Instance:GetBuildShowActiveReward()
    if self.task_active_reward_list then
        self.task_active_reward_list:SetDataList(build_active_reward)
    end

    local slider_value = SwornWGData.Instance:GetBuildRewardProgress()
    self.node_list.task_active_slider.slider.value = slider_value

    for i = 0, 1 do
        local is_red = SwornWGData.Instance:GetBuildTaskTypeIsCanFetch(i)
        self.node_list["task_remind_" .. i]:SetActive(is_red)
    end
end

function SwornView:FlushSwornTaskShowList()
    if not self.select_task_index then
        return
    end
    local task_list = SwornWGData.Instance:GetBuildTaskListByType(self.select_task_index)
    self.build_show_task_list:SetDataList(task_list)
end

function SwornView:TaskDefaultSelectToggle()
    local default_index = SwornWGData.Instance:GetBuildTaskDefaultSelect()
    self.node_list["task_toggle_" .. default_index].toggle.isOn = true
    self:OnClickSwornTaskToggle(default_index)
end

function SwornView:OnClickSwornTaskToggle(type)
    if self.select_task_index == type then
        return
    end

    self.select_task_index = type
    self:FlushSwornTaskShowList()
end

function SwornView:FlushSwornTaskTimeCount()
    if not self:IsLoadedIndex(TabIndex.sworn_task) then
		return
	end

    local time = SwornWGData.Instance:GetBuildFlushEndTime()
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("sworn_build_task_time") then
            CountDownManager.Instance:RemoveCountDown("sworn_build_task_time")
        end

        CountDownManager.Instance:AddCountDown("sworn_build_task_time", 
            BindTool.Bind(self.SwornTaskUpdateTimeCallBack, self), 
            BindTool.Bind(self.SwornTaskTimeOnComplete, self), 
            nil, time, 1)
    else
        self:SwornTaskTimeOnComplete()
    end
end

function SwornView:SwornTaskUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["task_remain_time"].text.text = string.format(Language.Sworn.BuildEndTime, time_str) 
end

function SwornView:SwornTaskTimeOnComplete()
    self.node_list.task_remain_time.text.text = ""
end

function SwornView:OpenSwornRuleTip()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.Sworn.TaskRuleText, Language.Sworn.TaskRuleTitle)
	end
end


--------------------------------SwornTaskActiveRewardCell---------
SwornTaskActiveRewardCell = SwornTaskActiveRewardCell or BaseClass(BaseRender)

function SwornTaskActiveRewardCell:LoadCallBack()
    self.node_list.click_btn.button:AddClickListener(BindTool.Bind(self.OnClickBox, self))
end

function SwornTaskActiveRewardCell:__delete()

end

function SwornTaskActiveRewardCell:OnFlush()
	if not self.data then
		return
	end

    local cfg = self.data.cfg
    local bundle, asset = ResPath.GetCommonIcon("a2_zdzl_bx_" .. cfg.seq + 1)
	self.node_list["box_img"].image:LoadSprite(bundle, asset)

    self.node_list.need_num.text.text = string.format(Language.Sworn.BoxActivestr, cfg.need_value)
    self.node_list.red:SetActive(self.data.can_get_flag)
    self.node_list.can_get_bg:SetActive(self.data.can_get_flag)
    self.node_list.gouzi:SetActive(self.data.get_flag == 1)

    XUI.SetGraphicGrey(self.node_list["box_img"], self.data.get_flag ~= 1 and not self.data.can_get_flag)
end

function SwornTaskActiveRewardCell:OnClickBox()
	if not self.data then
		return
	end

    local cfg = self.data.cfg
    if self.data.get_flag == 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Common.YiLingQu)
    elseif not self.data.can_get_flag then
        TipWGCtrl.Instance:ShowRewardDisplay(cfg.reward_item)
    else
        SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.BUILD_ACTIVE_REWARD, cfg.seq)
    end
end

------------------SwornBuildTaskRender-----------
SwornBuildTaskRender = SwornBuildTaskRender or BaseClass(BaseRender)
function SwornBuildTaskRender:__init()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.item_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["jump_btn"], BindTool.Bind(self.OnClickBtn, self))
end

function SwornBuildTaskRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function SwornBuildTaskRender:OnFlush()
    if not self.data then
        return
    end

    local cfg = self.data.cfg
    self.node_list.bq_name.text.text = Language.Sworn.TaskLimitType[cfg.task_limit_type]
    local bq_name = cfg.task_limit_type == 0 and "a2_jy_yq_1" or "a2_jy_yq_2"
    local bundle, asset = ResPath.GetSwornImg(bq_name)
    self.node_list.bq_img.image:LoadSprite(bundle, asset, function()
        self.node_list.bq_img.image:SetNativeSize()
    end)

    local color = self.data.task_progress >= cfg.condition and COLOR3B.GREEN or COLOR3B.RED
    local num_str = ToColorStr(self.data.task_progress .. "/" .. cfg.condition, color)
    self.node_list.task_desc.text.text = string.format(Language.Sworn.TaskDesc, cfg.task_des, num_str)
    self.node_list.task_add_exp.text.text = string.format(Language.Sworn.AddExpValue, cfg.add_exp)
    self.node_list.task_add_active_value.text.text = string.format(Language.Sworn.AddActiveVlaue, cfg.add_active_value)

    local had_sworn = SwornWGData.Instance:HadSworn()
    local btn_text = had_sworn and Language.Sworn.BuildTaskBtnStr[self.data.task_status] or Language.Sworn.NoSworn
    local font_size =  had_sworn and 26 or 22
    self.node_list.btn_text.text.fontSize = font_size
    self.node_list.btn_text.text.text = btn_text

    self.node_list.remind:SetActive(self.data.task_status == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.jump_btn:SetActive(self.data.task_status ~= REWARD_STATE_TYPE.FINISH)
    self.node_list.is_finish:SetActive(self.data.task_status == REWARD_STATE_TYPE.FINISH)
    self.reward_list:SetDataList(cfg.reward_item)

    
    if self.data.task_status == REWARD_STATE_TYPE.UNDONE then
        self.node_list.jump_btn.image:LoadSprite(ResPath.GetCommonButton("a2_ty_anniu_3"))
    elseif self.data.task_status == REWARD_STATE_TYPE.CAN_FETCH then
        self.node_list.jump_btn.image:LoadSprite(ResPath.GetCommonButton("a2_ty_anniu_4"))
    end
end


function SwornBuildTaskRender:OnClickBtn()
    local my_jieyi_state = SwornWGData.Instance:HadSworn()
    if not self.data or not my_jieyi_state then
        return
    end

    if self.data.task_status == REWARD_STATE_TYPE.CAN_FETCH then
        SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.BUILD_TASK_REWARD, self.data.task_seq)
    else
        self:OnClickJumpView()
    end
end

function SwornBuildTaskRender:OnClickJumpView()
    if not self.data then
        return
    end

    local cfg = self.data.cfg

    if cfg.open_panel ~= "" then
        if cfg.act_type == "" then --非活动
            FunOpen.Instance:OpenViewNameByCfg(cfg.open_panel)
        else
            local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(cfg.act_type) --活动是否开启
            if is_act_open then
                FunOpen.Instance:OpenViewNameByCfg(cfg.open_panel)
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
            end
        end
    end
end