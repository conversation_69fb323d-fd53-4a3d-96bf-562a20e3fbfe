BFCapRankWGData = BFCapRankWGData or BaseClass()

function BFCapRankWGData:__init()
	if BFCapRankWGData.Instance then 
		ErrorLog("[BFCapRankWGData] Attemp to create a singleton twice !")
	end

	BFCapRankWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("capability_rank_auto")
    self.act_cfg = cfg.type
	self.act_reward_cfg = ListToMapList(cfg.reward, "grade")
    self.other_cfg = cfg.other[1]
end

function BFCapRankWGData:__delete()
	BFCapRankWGData.Instance = nil
end

function BFCapRankWGData:GetActOtherCfg()
    return self.other_cfg or {}
end

function BFCapRankWGData:GetActTypeCfg(act_type)
    return self.act_cfg[act_type] or {}
end

function BFCapRankWGData:GetActRewardCfg(type)
	return self.act_reward_cfg[type] or {}
end

function BFCapRankWGData:SetCurActRewardType(reward_type)
	self.cur_reward_type = reward_type
end

function BFCapRankWGData:GetCurActRewardType()
	return self.cur_reward_type or 0
end

function BFCapRankWGData:GetMyRankValue()
	return self.my_rank_value or nil
end

function BFCapRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end

function BFCapRankWGData:ExpandRankData(index)
	local rank_item = {}
	rank_item.no_true_rank = true
	rank_item.index = index
	rank_item.rank_data = self:CreatNoRankItemData(index)
	return rank_item 
end

function BFCapRankWGData:SetSwornRankSort(protocol)
	if protocol.self_rank_value then
		self.my_rank_value = protocol.self_rank_value
	end

	if protocol.total_capability then
		self.total_capability = protocol.total_capability
	end

	self.rank_list = {}
	local cur_reward_type = self:GetCurActRewardType()
	local cfg = self:GetActRewardCfg(cur_reward_type)
	local rank_cfg = cfg[#cfg]
	local max_rank = rank_cfg.max_rank
	for i = 1, max_rank do
		local rank_item = {}
		if protocol.rank_item_list[i] then
			rank_item.no_true_rank = false
			rank_item.index = i
			rank_item.rank_data = protocol.rank_item_list[i]
		else
			rank_item = self:ExpandRankData(i)
		end
		self.rank_list[i] = rank_item
	end
end

function BFCapRankWGData:GetRankInfo()
	return self.rank_list
end

function BFCapRankWGData:GetRankItemData(rank_zhanli)
	local cur_reward_type = self:GetCurActRewardType()
	local rank_cfg = self:GetActRewardCfg(cur_reward_type)
	local other_cfg = self:GetActOtherCfg()
	local suit_data_list = WardrobeWGData.Instance:GetSuitSingtonData(other_cfg.suit_ids)
	local real_act_num  = suit_data_list and suit_data_list.real_act_num or 0
	if not IsEmptyTable(rank_cfg) then
		for k ,v in ipairs(rank_cfg) do
			local suit_num_enough = real_act_num >= v.minimum_active_count
			if rank_zhanli >= v.reach_value and suit_num_enough then
				return k, v
			end
		end
	end

	return nil
end


function BFCapRankWGData:GetMyRankInfo()
	local rank_list = self:GetRankInfo()
	if rank_list == nil then
		return nil
	end
    
	local my_uid = RoleWGData.Instance:GetOriginUid()
	for k, v in pairs(rank_list) do
		if v.rank_data and v.rank_data.uid then
			if v.rank_data.uid == my_uid then
				return v
			end
		end
	end

	return nil
end

function BFCapRankWGData:GetMyCapRank()
	local all_cap = self.total_capability or 0
	local other_cfg = self:GetActOtherCfg()
	if all_cap >= other_cfg.minimum_capability then
		return true
	end

	return false
end

function BFCapRankWGData:GetMyTotalCap()
	return self.total_capability or 0 
end