ActivePerfertQingrenWGData = ActivePerfertQingrenWGData or BaseClass()

function ActivePerfertQingrenWGData:__init()
	if ActivePerfertQingrenWGData.Instance then
		error("[ActivePerfertQingrenWGData] Attempt to create singleton twice!")
		return
	end
	ActivePerfertQingrenWGData.Instance = self
	--RemindManager.Instance:Register(RemindName.ActivePlate,BindTool.Bind(self.GetActivePlateRemind,self))
	self.persom_profess_info_list_marry = {}
    self.all_profess_info_list_marry = {}
    self.love_city_info = {
        total_process = 0,
        process_reward_flag = 0,
        task_list = {}
    }
    self.profess_love_info = {}

	self.danmu_all_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").sweet_talk_fake_notice,"profess_type")

    RemindManager.Instance:Register(RemindName.KFCityLove, BindTool.Bind(self.GetCityLoveRemind, self))
end

function ActivePerfertQingrenWGData:__delete()
    --RemindManager.Instance:UnRegister(RemindName.ActivePlate)
    RemindManager.Instance:UnRegister(RemindName.KFCityLove)
	ActivePerfertQingrenWGData.Instance = nil
	self.has_set_profess_score = nil
	self.is_person_dan_mu = nil
	self.profess_to_score_lover   = nil
	self.beprofess_score_lover   = nil
	self.person_total_score = nil
	self.persom_profess_info_list_marry = nil
	self.all_profess_info_list_marry = nil
	self.danmu_all_cfg = nil
end

--获取全称热恋的任务列表跟任务进度奖励
function ActivePerfertQingrenWGData:GetQuanChengReLianTaskCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
	return cfg.loving_city_task, cfg.loving_city_process_reward
end

--获取全称热恋模型材料相关
function ActivePerfertQingrenWGData:GetQuanChengReLianCfgOther()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
	return cfg.other[1]
end

--爱的表白
function ActivePerfertQingrenWGData:GetBiaoBaiRankCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
	return cfg.profess_for_love_rank_reward
end

function ActivePerfertQingrenWGData:GetBiaoBaiScoreCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
	return cfg.profess_for_love_score_reward
end

function ActivePerfertQingrenWGData:GetBiaoBaiDataList()
    if not self.biaobai_data_list then
        self.biaobai_data_list = {}
        local cfg = self:GetBiaoBaiRankCfg()
        for k,v in pairs(cfg) do
            local data = {}
            data.is_rank = true
            data.reward_item = {}
            for k1,v1 in pairs(v.reward_item) do
                data.reward_item[#data.reward_item + 1] = v1
            end
            data.rank_max_client = v.rank_max_client
            data.rank_min_client = v.rank_min_client
            data.rank_val_min_client = v.rank_val_min_client
            data.sort = 1999 - k
            self.biaobai_data_list[#self.biaobai_data_list + 1] = data
        end
        local cfg1 = self:GetBiaoBaiScoreCfg()
        for k,v in pairs(cfg1) do
            local data = {}
            data.is_rank = false
            data.reward_item = {}
            for k1,v1 in pairs(v.reward_item) do
                data.reward_item[#data.reward_item + 1] = v1
            end
            data.index = v.id + 1       
            data.sort = 999 + data.index
            data.score_value = v.score_value
            self.biaobai_data_list[#self.biaobai_data_list + 1] = data
        end
        table.sort(self.biaobai_data_list, function(a, b)
            return a.sort > b.sort
        end)
    end
    return self.biaobai_data_list
end

function ActivePerfertQingrenWGData:GetCurLoverProgress(leiji_data, intimacy_value)
    local cur_progress = 0
	local cur_intimacy_value = tonumber(intimacy_value)
    if next(leiji_data) == nil or cur_intimacy_value == nil then
		return cur_progress
	end

    local slider_piecewise = {0.2, 0.4, 0.6, 0.8, 1}
    for k, v in pairs(slider_piecewise) do
        local seq = k - 1
        local length = #slider_piecewise
        local cur_need = leiji_data[seq] and leiji_data[seq].need_process or 0
        local next_need = leiji_data[seq + 1] and leiji_data[seq + 1].need_process or leiji_data[#leiji_data].need_process
        local cur_value = slider_piecewise[seq] and slider_piecewise[seq] or 0
        local next_value = slider_piecewise[seq + 1] and slider_piecewise[seq + 1] or slider_piecewise[length]

        if cur_intimacy_value > cur_need and cur_intimacy_value <= next_need then
            cur_progress = (cur_intimacy_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
        elseif cur_intimacy_value > leiji_data[#leiji_data].need_process then
			cur_progress = slider_piecewise[length]
			break
        end
    end

    return cur_progress
end

function ActivePerfertQingrenWGData:GetBiaoBaiEndTimeCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").profess_for_love_end
	return cfg[1]
end
--甜言蜜语
function ActivePerfertQingrenWGData:GetMiYuRankCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto")
	return cfg.sweet_talk_fake_notice
end

function ActivePerfertQingrenWGData:SetLoveCityInfo(protocol)
    self.love_city_info.total_process = protocol.total_process
    self.love_city_info.process_reward_flag = protocol.process_reward_flag
    self.love_city_info.task_list = protocol.task_list
end

function ActivePerfertQingrenWGData:GetServerLoveCityInfo()
    return self.love_city_info
end

function ActivePerfertQingrenWGData:GetLoveCityRewardListInfo()
    if not self.love_city_reward_info then
        self.love_city_reward_info = {}
        local _, reward_cfg = self:GetQuanChengReLianTaskCfg()
        local index = 1
        for k, v in pairs(reward_cfg) do
            local info = {}
            info.need_process = v.need_process
            info.seq = v.seq  
            info.reward_item = v.reward_item        
            info.dec = v.dec
            self.love_city_reward_info[index] = info
            index = index + 1
        end
    end
    local flag = self:GetServerLoveCityInfo().process_reward_flag or 0
    local total_progress = self:GetServerLoveCityInfo().total_process or 0
    local num_flag = bit:d2b(flag)
    for k, v in pairs(self.love_city_reward_info) do
        local is_get = num_flag[32 - v.seq]
        if is_get == 0 and total_progress >= v.need_process then
            v.reward_states = 0
        elseif is_get == 0 and  total_progress < v.need_process then
            v.reward_states = 1
        elseif is_get == 1 then
            v.reward_states = 2
        end
    end
    table.sort(self.love_city_reward_info, SortTools.KeyLowerSorter("reward_states","seq"))
    return self.love_city_reward_info
end

function ActivePerfertQingrenWGData:GetLoveCityListInfo()
    if not self.love_city_task_info then
        self.love_city_task_info = {}
        local love_city_tasklist_cfg, _ = self:GetQuanChengReLianTaskCfg()
        local index = 1
        for k, v in pairs(love_city_tasklist_cfg) do
            local info = {}
            info.reward_process = v.reward_process
            info.task_process = v.task_process  
            info.task_name = v.task_name
            info.task_desc = v.task_desc
            info.reward_show = v.reward_show
            info.open_panel = v.open_panel
            info.sort_index = 1
            info.task_id = v.task_id
            info.process = 0
            info.index = index
            self.love_city_task_info[index] = info
            index = index + 1
        end
    end
    local task_list = self.love_city_info.task_list
    for k, v in pairs(self.love_city_task_info) do
        if task_list[v.index] then
            if task_list[v.index].cur_state == 0 then
                v.sort_index = 1
            elseif task_list[v.index].cur_state == 1 then
                v.sort_index = 0
            else
                v.sort_index = 2
            end
            v.process = task_list[v.index].process
        end
    end
    table.sort(self.love_city_task_info, SortTools.KeyLowerSorters("sort_index","task_id"))
	return self.love_city_task_info
end

function ActivePerfertQingrenWGData:SetLoveBiaoBaiRank(protocol)
	self.biaobai_rank_list_info = protocol
	self:RankCaculator()
	for k,v in pairs(protocol.rank_list) do
		AvatarManager.Instance:SetAvatarKey(v.h_uid, v.avatar_key_big_1, v.avatar_key_small_1)
		AvatarManager.Instance:SetAvatarKey(v.w_uid, v.avatar_key_big_2, v.avatar_key_small_2)
    end
end

function ActivePerfertQingrenWGData:RankCaculator()
	if self.biaobai_rank_list_info and not IsEmptyTable(self.biaobai_rank_list_info.rank_list) then
		local class_cfg = self:GetBiaoBaiRankCfg()
		local class_tab = {}
		local class_max = {}
		local class_star = {}
        self.is_have_rank = false
        self.my_rank_score = 0
		for i =1,#class_cfg do -- in pairs(class_cfg) do
			class_tab[i] = 0
			class_max[i] = class_cfg[i].rank_max
			class_star[i] = class_cfg[i].rank_max_client
		end

		for k,v in pairs(self.biaobai_rank_list_info.rank_list) do
            v.dang_index = -1
            v.rank_index2 = k
            v.rank_index = k
			for t,q in pairs(class_cfg) do
				if v.rank_value >= q.rank_val_min_client then
					v.dang_index = q.seq + 1
					break
				end
			end
		end

		self.love_rank_tab = {}
		local uid = RoleWGData.Instance:InCrossGetOriginUid()
		for i=1,#self.biaobai_rank_list_info.rank_list do --,v in pairs(self.biaobai_rank_list_info.rank_list) do
            local k = i
            local v = self.biaobai_rank_list_info.rank_list[i]
			for t,q in pairs(class_cfg) do
				if v.dang_index and class_tab[v.dang_index] and class_max[v.dang_index] then
                    if class_tab[v.dang_index] < class_max[v.dang_index] then
                        if k==1 or (k > 1 and v.rank_value ~= self.biaobai_rank_list_info.rank_list[k-1].rank_value) then
                            v.rank_index2 = class_star[v.dang_index] + class_tab[v.dang_index]
                            v.rank_index = v.rank_index2
                            class_tab[v.dang_index] = class_tab[v.dang_index] + 1
                            self.love_rank_tab[v.rank_index2] = v
                            self.love_rank_tab[v.rank_index2].limit_value = q.rank_val_min_client
                            break
                        elseif(k >1 and v.rank_value == self.biaobai_rank_list_info.rank_list[k-1].rank_value)then
                            v.rank_index2 = class_star[v.dang_index] + class_tab[v.dang_index]
                            v.rank_index = self.biaobai_rank_list_info.rank_list[k-1].rank_index
                            class_tab[v.dang_index] = class_tab[v.dang_index] + 1
                            self.love_rank_tab[v.rank_index2] = v
                            self.love_rank_tab[v.rank_index2].limit_value = q.rank_val_min_client
                            break
                        end

                    elseif  class_tab[v.dang_index] >= class_max[v.dang_index] and (k > 1 and v.rank_value == self.biaobai_rank_list_info.rank_list[k-1].rank_value) then
					        v.rank_index2 = self.biaobai_rank_list_info.rank_list[k-1].rank_index2 + 1
                            v.rank_index = self.biaobai_rank_list_info.rank_list[k-1].rank_index
                            for i=1,#class_tab do
                                if class_tab[v.dang_index+1] <class_max[v.dang_index +1] then
					                class_tab[v.dang_index+1] = class_tab[v.dang_index+1] + 1
                                    break
                                end
                            end
					        self.love_rank_tab[v.rank_index2] = v
					        self.love_rank_tab[v.rank_index2].limit_value = q.rank_val_min_client
					        break                       
				    else
					   if  v.dang_index and v.dang_index ~= -1 then
					   	   v.dang_index = v.dang_index + 1
					   end
                    end
				end
			end

			if (v.h_uid == uid or v.w_uid == uid) and v.dang_index ~= -1 then
   				self.is_have_rank = v.rank_index
   				self.my_rank_score = v.rank_value
   			end
		end

	end
end

function ActivePerfertQingrenWGData:SetLoveBiaoBaiRank1()
	local cfg = self:GetBiaoBaiRankCfg()
	-- local info = self.biaobai_rank_list_info.rank_list
    local rank_t = {}
    local uid = RoleWGData.Instance:InCrossGetOriginUid()
    local is_have_rank = nil
    local rank_score = 0
    local table_all = {} 
    local cell_index = 1
    for k,v in pairs(cfg) do
    	for i = v.rank_max_client,v.rank_min_client do
    		table_all[cell_index] = {}
    		table_all[cell_index].limit_value = v.rank_val_min_client
    		table_all[cell_index].rank_index = cell_index
    		table_all[cell_index].dang_index = k
    		table_all[cell_index].rank_value = 0
    		cell_index = cell_index + 1
    	end
    end

    if self.love_rank_tab and not IsEmptyTable(self.love_rank_tab) then
   		for k,v in pairs(self.love_rank_tab) do
            if v.deng_index ~= -1 then
    		  table_all[v.rank_index2] = v
            end
   		end
   	end
    return table_all ,self.is_have_rank,self.rank_score
end

function ActivePerfertQingrenWGData:GetSelfLoveBiaoBaiRank1()
	local user_id = RoleWGData.Instance:InCrossGetOriginUid()
	local data = self:SetLoveBiaoBaiRank1()

	if not data or IsEmptyTable(data) then
		return nil, nil
	end

	for k,v in ipairs(data) do
		if user_id == v.w_uid then
			return v, k
		end
	end

	return nil, nil
end


function ActivePerfertQingrenWGData:SetLoveBiaoBaiRank2(min,max)
	if nil == self.biaobai_rank_list_info then return {} end
	local info = self.biaobai_rank_list_info.rank_list
	if IsEmptyTable(info) then return {} end
	local data = {}
	for k,v in pairs(info) do
		if v.rank_value >= min and v.rank_value <= max then
			table.insert(data,v)
		end
	end
	return data
end

function ActivePerfertQingrenWGData:GetLoveBiaoBaiRank()
	return self.biaobai_rank_list_info or {}
end

--甜言蜜语单人弹幕返回
function ActivePerfertQingrenWGData:SetPersonalInfo(protocol)
	self.persom_profess_info_list = protocol.items
	self.persom_profess_info_list_marry = protocol.items_1

	for i=1,3 do
		if not IsEmptyTable(self.persom_profess_info_list[i]) then
			table.sort(self.persom_profess_info_list[i],SortTools.KeyUpperSorter("profess_time"))
		end
	end
	if IsEmptyTable(self.persom_profess_info_list_marry) then return end
	table.sort(self.persom_profess_info_list_marry,SortTools.KeyUpperSorter("profess_time"))
end

function ActivePerfertQingrenWGData:GetPersonalInfo()
	return self.persom_profess_info_list or {} ,self.persom_profess_info_list_marry or {}
end

--甜言蜜语全部信息
function ActivePerfertQingrenWGData:SetAllProfessInfo(protocol)
	self.all_profess_info_list = protocol.items
	self.all_profess_info_list_marry = protocol.items_1
	for i=1,3 do
		if not IsEmptyTable(self.all_profess_info_list[i]) then
			table.sort(self.all_profess_info_list[i],SortTools.KeyUpperSorter("profess_time"))
		end
		local count = 20 - #self.all_profess_info_list[i]  --最低20条表白信息
		if count >= 0 then
			local cfg = self.danmu_all_cfg[i]
			for k=1,count do
				table.insert(self.all_profess_info_list[i],self.danmu_all_cfg[i][k])
			end
		end
	end
	if IsEmptyTable(self.all_profess_info_list_marry) then return end
	table.sort(self.all_profess_info_list_marry,SortTools.KeyUpperSorter("profess_time"))
end

function ActivePerfertQingrenWGData:GetAllProfessInfo()
	return self.all_profess_info_list or {},self.all_profess_info_list_marry or {}
end

--设置爱的表白积分
function ActivePerfertQingrenWGData:SetLoveBiaoBaiScore(protocol)
	self.profess_to_score  = protocol.profess_to_score
	self.beprofess_score   = protocol.beprofess_score
	self.profess_to_score_lover   = protocol.profess_to_score_lover
	self.beprofess_score_lover   = protocol.beprofess_score_lover
	self.person_total_score = protocol.total_score
	self.has_set_profess_score = true
end

function ActivePerfertQingrenWGData:GetLoveBiaoBaiScore()
	return self.profess_to_score or 0,self.beprofess_score or 0 ,self.has_set_profess_score ,self.person_total_score
end

--获取弹幕移动时间  间隔
function ActivePerfertQingrenWGData:GetDanMuParam(type_index)
	local cfg_1 = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").other[1].barrage_time
	local cfg_2 = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").other[1].barrage_space
	local barrage_time_table = Split(cfg_1,"|")
	local barrage_space_table = Split(cfg_2,"|")
	return barrage_time_table[type_index],barrage_space_table[type_index]
end

function ActivePerfertQingrenWGData:GetCityLoveRemind()
	if not OpenServerAssistWGData.Instance:GetOpenSererActIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY) or
    not ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY, true) then return 0 end

    local list_info = self:GetLoveCityListInfo()
    for k,v in pairs(list_info) do
        if v.sort_index == 0 then
			return 1
		end
    end
    local city_info_cfg = self:GetQuanChengReLianCfgOther()
    local _, loving_city_process_reward = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianTaskCfg()
	local num_flag = bit:d2b(self.love_city_info.process_reward_flag)
	if self.love_city_info.total_process >= city_info_cfg.loving_city_convert_image_progress_value and num_flag[32] == 0 then
        return 1
	end
	
	for i = 1, 5 do
		local is_get = num_flag[32 - i]
        if is_get == 0 and self.love_city_info.total_process >= loving_city_process_reward[i].need_process then
			return 1
		end
    end
	return 0
end

function ActivePerfertQingrenWGData:IsOpenActivityWanMeiQingRen()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY)
	local limit_level = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").other[1].perfect_lover_open_level
	local role_level =  RoleWGData.Instance:GetRoleLevel()
	if activity_info and activity_info.status ~= ACTIVITY_STATUS.CLOSE and limit_level and role_level >= limit_level then
		return true
	end
	return false
end

function ActivePerfertQingrenWGData:SetIsPersonDanMu(is_person)
	self.is_person_dan_mu = is_person
end

function ActivePerfertQingrenWGData:GetIsPersonDanMu()
	return self.is_person_dan_mu
end

function ActivePerfertQingrenWGData:SetProfessLoveRankInfo(protocol)
	self.profess_rank_list = protocol.profess_rank_list
end

function ActivePerfertQingrenWGData:GetProfessLoveRankInfo()
	return self.profess_rank_list or {}
end

function ActivePerfertQingrenWGData:SetProfessForLoveInfo(protocol)
    self.profess_love_info.first_rank_list = protocol.first_rank_list --排行第一的couple
    self.profess_love_info.total_score = protocol.total_score--排行第一的总积分
    self.profess_love_info.owner_total_score = protocol.owner_total_score --自身总积分
    self.profess_love_info.reward_index = protocol.reward_index--获得的奖励下标
    self.profess_love_info.score_reward_flag = protocol.score_reward_flag --达标奖励发放标志
    self.profess_love_info.owner_rank_index = protocol.owner_rank_index --自身排名
end

function ActivePerfertQingrenWGData:GetPersonTotalScoreByIndex(index)
    local score = IsEmptyTable(self.profess_love_info) and 0 or self.profess_love_info.owner_total_score
    return score
end

function ActivePerfertQingrenWGData:GetPersonRewardFlagByIndex(index)
    local flag = IsEmptyTable(self.profess_love_info) and 0 or self.profess_love_info.score_reward_flag
    local flag_tb = bit:d2b_two(flag)
    return flag_tb[index]
end

function ActivePerfertQingrenWGData:GetProfessForLoveInfo()
	return self.profess_love_info or {}
end

function ActivePerfertQingrenWGData:GetProfessForLoveOwenerInfo()
	return self.profess_love_info.owner_rank_index or -1, self.profess_love_info.owner_total_score or 0
end
