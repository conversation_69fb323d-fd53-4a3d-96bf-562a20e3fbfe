DayDayFanLiView = DayDayFanLiView or BaseClass(SafeBaseView)
function DayDayFanLiView:__init()
	self.view_style = ViewStyle.Full

	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/act_dayday_fanli_prefab", "layout_daydayfanli")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
end

function DayDayFanLiView:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end

	if self.lingqu_list_view then
		self.lingqu_list_view:DeleteMe()
		self.lingqu_list_view = nil
	end

	-- if self.item_cell then
	-- 	self.item_cell:DeleteMe()
	-- 	self.item_cell = nil
	-- end

	if self.skill_content then
		for k, v in pairs(self.skill_content) do
			v:DeleteMe()
		end
		self.skill_content = nil
	end
end

function DayDayFanLiView:LoadCallBack()
	local bundle_name, asset_name = "uis/uires/res/x1ui/act_dayday_fanli_atlas", "title" --标题
	self.node_list.img_title.image:LoadSprite(bundle_name, asset_name, function()
		self.node_list.img_title.image:SetNativeSize()
	end)
	XUI.AddClickEventListener(self.node_list.btn_opne_recharge, BindTool.Bind1(self.OnRechargeClick, self))

	--随机奖励列表
	if nil == self.reward_list_view then
		self.reward_list_view = AsyncListView.New(DayDayFanLiRewardView, self.node_list["reward_list"])
	end
	local reward_list = DayDayFanLiData.Instance:GetDayDayFanLiRewardList()
	self.reward_list_view:SetDataList(reward_list)

	if nil == self.lingqu_list_view then
		self.lingqu_list_view = AsyncListView.New(DayDayFanLiLingQuListRender, self.node_list["lingqu_list"])
	end
	
	self:FlushLeftModelContent()
end

--刷新数据
function DayDayFanLiView:OnFlush()
	local lignqu_list, dazzle_gold = DayDayFanLiData.Instance:GetLingQuList()
	local total_day = DayDayFanLiData.Instance:GetDayDayFanLiTotalDay()
	self.lingqu_list_view:SetDataList(lignqu_list, 3)
	self.node_list["have_chongzhi_time"].text.text = string.format(Language.DayDayFanLi.HaveChongZhiDay, total_day)
	self.node_list["total_get_yuanbao"].text.text = string.format(Language.DayDayFanLi.LeiJiFanLi, dazzle_gold)

	local model_cfg = DayDayFanLiData.Instance:GetModelShowCfg()
	self.node_list["get_time_item"].text.text = string.format(Language.DayDayFanLi.NeedChongZhiDay,
		model_cfg.day - total_day)
end

--刷新模型
function DayDayFanLiView:FlushLeftModelContent()
	local cfg = DayDayFanLiData.Instance:GetModelShowCfg()
	if nil == self.skill_content then
		self.skill_content = {}
	end
	-- if nil == self.item_cell then
	-- 	self.item_cell = ItemCell.New(self.node_list["item_pos"])
	-- end
	-- self.item_cell:SetData(cfg.reward_item[0])
	local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.reward_item[0].item_id)
	local str = string.gsub(item_cfg.name, Language.DayDayFanLi.JiHuoKa, "")
	self.node_list["reward_name"].text.text = str
	self.node_list["cap_value"].text.text = item_cfg.capability_show

	local skill_partent = self.node_list["skill_content"]
	if cfg.skill_1 == "" then
		skill_partent:SetActive(false)
		return
	end
	skill_partent:SetActive(true)

	for i = 1, 3 do
		if nil == self.skill_content[i] then
			self.skill_content[i] = DayDayFanLiSkill.New(skill_partent:FindObj("ph_advanced_skill_item_" .. i))
		end
		self.skill_content[i]:SetData({ skill_name = cfg["skill_" .. i], skill_describe = cfg["skill_describe_" .. i],
			skill_icon = cfg["skill_icon_" .. i] })
	end
end

--前往充值
function DayDayFanLiView:OnRechargeClick()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end

-------------------------------------------------------------------------
--领取列表
DayDayFanLiLingQuListRender = DayDayFanLiLingQuListRender or BaseClass(BaseRender)
function DayDayFanLiLingQuListRender:__init()
	XUI.AddClickEventListener(self.node_list["btn_lingqu"], BindTool.Bind(self.OnLingQuClick, self))
end

function DayDayFanLiLingQuListRender:__delete()
	if self.cell_list then
		for k, v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function DayDayFanLiLingQuListRender:OnFlush()
	if nil == self.data then
		return
	end
	local index = 1
	if nil == self.cell_list then
		self.cell_list = {}
	end
	for k, v in pairs(self.data.reward_item) do
		if nil == self.cell_list[index] then
			self.cell_list[index] = ItemCell.New(self.node_list["reward_list"])
		else
			self.cell_list[index]:SetVisible(true)
		end
		self.cell_list[index]:SetData(v)
		index = index + 1
	end
	if self.cell_list[index] then
		for i = index, #self.cell_list do
			self.cell_list[i]:SetVisible(false)
		end
	end
	if self.data.flag == 3 then --尚未领取
		self:SetBtnShow(true)
	elseif self.data.flag == 0 then
		self:SetBtnShow(false)
		self.node_list["Img_YLQ"]:SetActive(false)
		self.node_list["Img_NLQ"]:SetActive(true)
	else
		self:SetBtnShow(false)
		self.node_list["Img_YLQ"]:SetActive(true)
		self.node_list["Img_NLQ"]:SetActive(false)
	end
	self.node_list["chongzhidesc"].text.text = string.format(Language.DayDayFanLi.LeiJiChongZhi,
		self.data.need_chongzhi_day, self.data.need_gold)
end

function DayDayFanLiLingQuListRender:SetBtnShow(enable)
	if enable then
		self.node_list["Img_YLQ"]:SetActive(false)
		self.node_list["Img_NLQ"]:SetActive(false)
	end
	self.node_list["btn_lingqu"]:SetActive(enable)
	self.node_list["btn_receive_effect"]:SetActive(enable)
end

function DayDayFanLiLingQuListRender:OnLingQuClick()
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({
		rand_activity_type = ACTIVITY_TYPE.DayDayFanLi,
		opera_type = DAYDAYFANLI.RE_CHONGZHI_DAY_REWARD_OPERA_TYPE_RANK_REWARD,
		param_1 = self.data.index
	})
end

-------------------------------------------------------------------------
--奖励列表
DayDayFanLiRewardView = DayDayFanLiRewardView or BaseClass(BaseRender)
function DayDayFanLiRewardView:__init()

end

function DayDayFanLiRewardView:__delete()
	if self.cell_list then
		for k, v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function DayDayFanLiRewardView:OnFlush()
	if nil == self.data then
		return
	end
	if nil == self.cell_list then
		self.cell_list = {}
	end
	for i, v in ipairs(self.data) do
		if nil == self.cell_list[i] then
			self.cell_list[i] = ItemCell.New(self.view)
		end
		self.cell_list[i]:SetData(v)
	end
end

-------------------------------------------------------------------------
DayDayFanLiSkill = DayDayFanLiSkill or BaseClass() -- MountlingChoneImageSkillItem
function DayDayFanLiSkill:OnFlush()
	local data = self:GetData()
	if data == nil then
		return
	end
	local bundel, asset = ResPath.GetSkillIconById(data.skill_icon)
	self.node_list["img_skill_icon"].image:LoadSprite(bundel, asset, function()
		self.node_list["img_skill_icon"].rect.sizeDelta = Vector2(COMMON_CONSTS.ItemCellSize, COMMON_CONSTS.ItemCellSize)
	end)

	self.node_list["Img"].button.interactable = true
	self.node_list["skill_name"].text.text = data.skill_name
end

function DayDayFanLiSkill:OnSelectClick()
	local data = self:GetData()
	if data == nil then
		return
	end
	if next(data) == nil then
		return
	end
	local skill_describe = data.skill_describe
	local limit_text = ""

	local show_data = {
		icon = data.skill_icon,
		top_text = data.skill_name,
		body_text = skill_describe,
		limit_text = limit_text,
		x = 0,
		y = -100,
		set_pos2 = true,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end
