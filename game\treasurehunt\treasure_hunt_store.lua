--新寻宝积分商城
TreasureHuntStoreView = TreasureHuntStoreView or BaseClass(SafeBaseView)

function TreasureHuntStoreView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(60, -27), sizeDelta = Vector2(932, 522)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_store")
    self:SetMaskBg(true, true)
end

function TreasureHuntStoreView:ReleaseCallBack()
    if self.store_grid_list then
        self.store_grid_list:DeleteMe()
        self.store_grid_list = nil
    end
    self.shop_type = nil
end

function TreasureHuntStoreView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TreasureHunt.StoreTitle
    if not self.store_grid_list then
		self.store_grid_list = AsyncBaseGrid.New()
        --self.store_grid_list:SetIsShowTips(false)           
        local bundle = "uis/view/treasurehunt_ui_prefab"
		local asset = "store_item_render"        
        self.store_grid_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_store_grid"],
        assetBundle = bundle, assetName = asset, itemRender = StoreItemCell})
        self.store_grid_list:SetStartZeroIndex(false)
    end
end

function TreasureHuntStoreView:CloseCallBack()
    self.shop_type = nil
end

function TreasureHuntStoreView:OnClickGetOut()
    local shop_type = TreasureHuntWGCtrl.Instance:GetTreasureType()
    self.shop_type = self.shop_type or shop_type
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_FETCH_ALL, self.shop_type - 1)
end

function TreasureHuntStoreView:OnClickSort()
    
end

function TreasureHuntStoreView:OnFlush(param)
    local shop_type = TreasureHuntWGCtrl.Instance:GetTreasureType() or 1
    local is_need_reset_list = not self.shop_type
    for k, v in pairs(param) do
        if k == "all" then
            if v.open_param then
                self.shop_type = tonumber(v.open_param)
            end
        end
    end
    self.shop_type = self.shop_type or shop_type
    local info = TreasureHuntWGData.Instance:GetConvertShopCfgByType(self.shop_type - 1)
    if not IsEmptyTable(info) then
        table.sort(info, function(a, b)
            local item_cfg1 = ItemWGData.Instance:GetItemConfig(a.itemid)
            local item_cfg2 = ItemWGData.Instance:GetItemConfig(b.itemid)
            if not IsEmptyTable(item_cfg1) and not IsEmptyTable(item_cfg2) then          
                if item_cfg1.color == item_cfg2.color then
                    return a.price > b.price
                else
                    return item_cfg1.color > item_cfg2.color
                end
            end
            
        end)
    end
    local data_list = self.store_grid_list:GetDataList()
    if not IsEmptyTable(data_list) and not IsEmptyTable(self.store_grid_list:GetAllCell()) and not is_need_reset_list then
        for k, v in pairs(self.store_grid_list:GetAllCell()) do
            v:FlushPrice()
        end
    else
        self.store_grid_list:SetDataList(info)
    end

    local data = TreasureHuntWGData.Instance:GetBaseData()
    self.node_list.score_text.text.text = data.chestshop_score_list[self.shop_type] or 0
    if info and info[1] then
        local score_itemcfg = ItemWGData.Instance:GetItemConfig(info[1].show_item)
        local asset, bundle = ResPath.GetItem(score_itemcfg.icon_id) --icon_id
        self.node_list.icon.image:LoadSprite(asset, bundle,function()
            --self.node_list.icon.image:SetNativeSize()
        end)
    end
end

----------------------------------------------------------------------------------
StoreItemCell = StoreItemCell or BaseClass(BaseRender)
function StoreItemCell:__delete()
    if self.item_info then
        self.item_info:DeleteMe()
        self.item_info = nil
    end
end
function StoreItemCell:__init()
end

function StoreItemCell:LoadCallBack()
    self.node_list.btn_buy.button:AddClickListener(BindTool.Bind(self.OnClickBuy, self))
end

function StoreItemCell:OnFlush()
	if not self.data then
		return
    end
    if not self.node_list.btn_buy then
		return
    end
    if not self.item_info then
        self.item_info = ItemCell.New(self.node_list.cell_pos)
    end
   
    self:FlushPrice()
   
    self.item_info:SetData({item_id = self.data.itemid})
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.itemid)
    self.node_list.Text.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

    local score_itemcfg = ItemWGData.Instance:GetItemConfig(self.data.show_item)
    local asset, bundle = ResPath.GetItem(score_itemcfg.icon_id) --icon_id
    self.node_list.icon.image:LoadSprite(asset, bundle)
end

function StoreItemCell:FlushPrice()
    local data = TreasureHuntWGData.Instance:GetBaseData()
    local item_num = data.chestshop_score_list[self.data.price_type + 1] or 0
    local color = item_num >= self.data.price and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.price_text.text.text = ToColorStr(self.data.price, color)
end

function StoreItemCell:OnClickBuy()
    local data = TreasureHuntWGData.Instance:GetBaseData()
    local item_num = data.chestshop_score_list[self.data.price_type + 1] or 0
    if item_num >= self.data.price then
        TreasureHuntWGCtrl.Instance:DoShopOperation(TreasureHuntWGData.CHESTSHOP_CONVERT_REQ.CHESTSHOP_CONVERT_BUY, self.data.index , 1)
    else
        local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.show_item)
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.TreasureHunt.ScoreNotEnough, item_cfg.name))
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.show_item})
    end
end