-- 小鸭疾走轮次结束后的投票面板
DuckRaceResultsVoteView = DuckRaceResultsVoteView or BaseClass(SafeBaseView)

function DuckRaceResultsVoteView:__init()
	self.view_style = ViewStyle.Half
	-- self:SetMaskBg(true)
	self.active_close = false
	
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_race_results_vote")
end

function DuckRaceResultsVoteView:__delete()

end

function DuckRaceResultsVoteView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("duck_vote_auto_close_timer") then
		CountDownManager.Instance:RemoveCountDown("duck_vote_auto_close_timer")
	end
	if not IsEmptyTable(self.duck_vote_list) then
		for k, v in pairs(self.duck_vote_list) do
			v:DeleteMe()
		end
		self.duck_vote_list = {}
	end
end

function DuckRaceResultsVoteView:LoadCallBack()
	self.duck_vote_list = {}
	local list_obj = self.node_list["duck_vote_list"].transform
	local data_list = DuckRaceWGData.Instance:GetResultsDuckList()
	for i = 1, 3 do
		local str = "duck_vote_item" .. i
		local obj = U3DObject(list_obj:Find(str).gameObject, list_obj:Find(str), self)
		self.duck_vote_list[i] = DuckVoteItem.New(obj)
		self.duck_vote_list[i]:SetIndex(i)
		self.duck_vote_list[i]:SetData(data_list[i])
	end
	self:StartAutoClose()
end

function DuckRaceResultsVoteView:OnFlush()

end

function DuckRaceResultsVoteView:StartAutoClose()
	local other_cfg = DuckRaceWGData.Instance:GetOtherCfg()
	if CountDownManager.Instance:HasCountDown("duck_vote_auto_close_timer") then
		CountDownManager.Instance:RemoveCountDown("duck_vote_auto_close_timer")
	end

	CountDownManager.Instance:AddCountDown("duck_vote_auto_close_timer", BindTool.Bind1(self.UpdateRefreshTime, self),
		BindTool.Bind1(self.CompleteCallBack, self), nil, other_cfg.duck_vote_time, 1)
end

function DuckRaceResultsVoteView:UpdateRefreshTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list["close_text"].text.text = string.format(Language.DuckRace.AutoCloseDes, time)
end

function DuckRaceResultsVoteView:CompleteCallBack()
	self:Close()
end

------------------------------------------------------------
-- 每轮结算投票item
DuckVoteItem = DuckVoteItem or BaseClass(BaseGridRender)
function DuckVoteItem:__init()
	if not self.duck_model then
		self.duck_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["duck_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = true,
		}

		self.duck_model:SetRenderTexUI3DModel(display_data)

		-- self.duck_model:SetUI3DModel(self.node_list["duck_model"].transform,
		-- 	self.node_list["duck_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end
	XUI.AddClickEventListener(self.node_list["oper_btn"], BindTool.Bind(self.ClickOperBtn, self))
end

function DuckVoteItem:__delete()
	if self.duck_model then
		self.duck_model:DeleteMe()
		self.duck_model = nil
	end
end

function DuckVoteItem:OnFlush()
	if not self.data then return end
	local cfg = DuckRaceWGData.Instance:GetDuckTraitCfg(self.data.index)
	local rank = self.data.rank
	rank = rank == 0 and 1 or rank
	local talk_list = Split(cfg["rank_talk_" .. rank], "|")
	local num = math.random(1, #talk_list)
	self.node_list["dialog_text"].text.text = talk_list[num]
	local monster_id = DuckRaceWGData.Instance:GetDuckMonsterId(self.data.index)
	local mosnter_cfg = BossWGData.Instance:GetMonsterInfo(monster_id)
	local bundle, asset = ResPath.GetMonsterModel(mosnter_cfg.resid)
	self.duck_model:SetMainAsset(bundle, asset)
end

function DuckVoteItem:ClickOperBtn()
	if self.index == 1 then
		local monster_cfg = BossWGData.Instance:GetMonsterInfo(DuckRaceWGData.Instance:GetDuckMonsterId(self.data.index))
		local str = string.format(Language.DuckRace.AwardDes, monster_cfg.name)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	else
		DuckRaceWGCtrl.Instance:SendCSCrossRacingOpera(CROSS_RACING_OPERA.CROSS_RACING_OPERA_VOTE, self.data.index)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.DuckRace.PenaltyDes)
	end
	XUI.SetButtonEnabled(self.node_list["oper_btn"], false)
end
