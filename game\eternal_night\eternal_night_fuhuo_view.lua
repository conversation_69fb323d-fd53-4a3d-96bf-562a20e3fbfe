EternalNightFuHuoView = EternalNightFuHuoView or BaseClass(SafeBaseView)

EternalNightFuHuoView.FuHuoType = {
	Type1 = 1,
	Type2 = 2,
}

local FuHuoType = {
    None = 0,
	Common = 1,
	Here = 2,
}

EternalNightFuHuoView.COUNTDOWNTYPE = "fuhuo_daojishi"			-- 倒计时key
EternalNightFuHuoView.COUNTDOWNTYPE_GOLD = "fuhuo_gold_daojishi"-- 元宝购买倒计时key
EternalNightFuHuoView.COUNTDOWNTYPE_FREE = "fuhuo_free_daojishi"-- 免费复活倒计时key

function EternalNightFuHuoView:__init()
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_fuhuo")
	self.active_close = false
	self.killer_name = ""
	self.killer_level = ""
	self.type = nil
	self.param = nil
	self.fuhuo_type = FuHuoType.None
	self.gongneng_sort = {}
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false,true)
	--self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function EternalNightFuHuoView:__delete()
end

function EternalNightFuHuoView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_free_fuhuo"], BindTool.Bind1(self.OnFuhuoDianHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_gold_fuhuo"], BindTool.Bind1(self.OnGoldFuHuoHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_viptz_fuhuo"], BindTool.Bind1(self.OnGoldFuHuoHandler, self))

	XUI.AddClickEventListener(self.node_list["btn_free_fuhuo2"], BindTool.Bind1(self.OnFuhuoDianHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_gold_fuhuo2"], BindTool.Bind1(self.OnGoldFuHuoHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_viptz_fuhuo2"], BindTool.Bind1(self.OnGoldFuHuoHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_stone_fuhuo2"], BindTool.Bind1(self.OnGoldFuHuoHandler, self))

	self.node_list["btn_open_chat"].button:AddClickListener(function()
		ChatWGCtrl.Instance:OpenChatWindow()
	end)

	self.item_list = AsyncListView.New(ItemCell,self.node_list["item_list"])

	self.gongneng_sort = FuBenWGData.Instance:GetFuhuoBtn()
    self:CreateGongNengHead()

	if not self.drop_item then
		self.drop_item = ItemCell.New(self.node_list.drop_item)
	end
end

function EternalNightFuHuoView:ReleaseCallBack()
	self.fuhuo_common_callback = nil
	self.fuhuo_here_callback = nil
	self.fuhuo_type = FuHuoType.None

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

	if self.drop_item then
		self.drop_item:DeleteMe()
		self.drop_item = nil
	end

    CountDownManager.Instance:RemoveCountDown(EternalNightFuHuoView.COUNTDOWNTYPE)
end

function EternalNightFuHuoView:OpenCallBack()
	self.fuhuo_type = FuHuoType.None
end

function EternalNightFuHuoView:CloseCallBack()
	self.fuhuo_type = FuHuoType.None
	self.type = nil
	self.param = nil
end

function EternalNightFuHuoView:OnFlush(param_t)
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType()
	if cur_grade_type and (cur_grade_type == EternalNightWGData.GradeType.Collect 
		or cur_grade_type == EternalNightWGData.GradeType.Snatch) then
		
		self.node_list["fuhuo_type1"]:SetActive(true)
		self.node_list["fuhuo_type2"]:SetActive(false)
		local drop_equip_data = EternalNightWGData.Instance:GetDropEquipData()
		if not IsEmptyTable(drop_equip_data) and drop_equip_data.equip_id > 0 then
			local drop_equip_id = drop_equip_data.equip_id
			local name = drop_equip_data.name or ""
			local data = {}
			local item_cfg = {item_id = drop_equip_id, tips_from_view = ItemTip.FROM_ETERNAL_NIGHT}
			table.insert(data,item_cfg)
			self.item_list:SetDataList(data)
			self.node_list["has_equip"]:SetActive(true)
			self.node_list["not_equip"]:SetActive(false)
			self.node_list["has_equip_tips"]:SetActive(true)
			self.node_list["not_equip_tips"]:SetActive(false)
			self.node_list["has_equip_tips"].text.text = string.format(Language.EternalNight.HasEquipTips,name)
		else
			self.node_list["has_equip"]:SetActive(false)
			self.node_list["not_equip"]:SetActive(true)
			self.node_list["not_equip_tips"]:SetActive(true)
			self.node_list["has_equip_tips"]:SetActive(false)
		end
		self.node_list.drop_item.rect.anchoredPosition = Vector2(0, 0)
	elseif cur_grade_type and cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		self.node_list["fuhuo_type1"]:SetActive(false)
		self.node_list["fuhuo_type2"]:SetActive(true)
		local live_num = EternalNightWGData.Instance:GetLiveNum()
		self.node_list["shenyu_num"].text.text = string.format(Language.EternalNight.ShengYuStr1,live_num)
		local can_fuhuo = EternalNightWGData.Instance:GetEliminateCanFuHuo()
		self.node_list["fuhuo_btns"]:SetActive(can_fuhuo)
		self.node_list["not_life_text"]:SetActive(not can_fuhuo)
		self.node_list["has_life_num"]:SetActive(can_fuhuo)
		local relive_times = EternalNightWGData.Instance:GetEliminateReliveTimes()
		self.node_list["has_life_num"].text.text = string.format(Language.EternalNight.FuHuoShengYuNum,relive_times)
		self.node_list.drop_item.rect.anchoredPosition = Vector2(0, -50)
	end

	local show_drop_item = ((nil ~= self.type) and (nil ~= self.param) and (self.type == FUHUO_TYPE.TYPE_ETERNAL_NIGHT) and (self.param > 0))
	self.node_list.drop_root:CustomSetActive(show_drop_item)

	if show_drop_item then
		self.drop_item:SetData({item_id = self.param})
	end

	for k, v in pairs(param_t) do
		if k == "all" then
		elseif k == "daojishi" then
			self:DaoJiShi(v.time or 1)
		end
	end

   	self:FlushVipTzFreeStatus()
    self:SetReviveBtnPosition()
    self:SetFuhuoBtnsCentent()
    self:FlushFuhuoBtnStatus()

end

function EternalNightFuHuoView:FlushFuhuoBtnStatus()
    local fuhuo_stone_num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE)
    self.node_list.stone_container:SetActive(fuhuo_stone_num > 0)
    self.node_list.gold_container:SetActive(fuhuo_stone_num <= 0)
    self.node_list.text_stone.text.text = string.format(Language.Fuhuo.Stone, fuhuo_stone_num <= 999 and fuhuo_stone_num or 999)
    self.node_list.lbl_fuhuo_stone2.text.text = string.format(Language.Fuhuo.Stone, fuhuo_stone_num <= 999 and fuhuo_stone_num or 999)
end

-- 设置复活按钮位置
function EternalNightFuHuoView:SetReviveBtnPosition()
	if self.is_free_fuhuo then
		self.node_list["btn_gold_fuhuo2"]:SetActive(false)
		self.node_list["btn_stone_fuhuo2"]:SetActive(false)
		self.node_list["btn_free_fuhuo2"]:SetActive(true)
		self.node_list["btn_viptz_fuhuo2"]:SetActive(true)
	else
		self:IsStoneBtn()
		self.node_list["btn_free_fuhuo2"]:SetActive(true)
		self.node_list["btn_viptz_fuhuo2"]:SetActive(false)
	end
end

function EternalNightFuHuoView:IsStoneBtn() -- 根据是否有复活石显示复活石btn还是仙玉btn
	local num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE)
	self.node_list["btn_gold_fuhuo2"]:SetActive(num <= 0)
	self.node_list["btn_stone_fuhuo2"]:SetActive(num > 0)
end

-- 复活按钮上面的字内容
-- 复活价格说明
function EternalNightFuHuoView:SetFuhuoBtnsCentent()
	local coin_cost = FuhuoWGCtrl.Instance:GetFuhuoGold()
	self.node_list.btn_gold_fuhuo2_text1.text.text = coin_cost
	self.node_list.btn_gold_fuhuo2_text2.text.text = Language.Fuhuo.FuHuoXiaoHaoPrompt

	self.node_list.lbl_fuhuo_money2.text.text = coin_cost
    self.node_list.lbl_fuhuo_money.text.text = Language.Fuhuo.FuHuoXiaoHaoPrompt

    local item_config = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.RESURGENCE_STONE)
    if item_config then
        self.node_list["stone_icon"].image:LoadSprite(ResPath.GetItem(item_config.icon_id))
        self.node_list["text_stone_name"].text.text = item_config.name
        self.node_list["stone_icon2"].image:LoadSprite(ResPath.GetItem(item_config.icon_id))
        self.node_list["text_stone_name2"].text.text = item_config.name
    end
end

function EternalNightFuHuoView:FlushVipTzFreeStatus()
	local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	local free_count = RechargeWGData.Instance:GetFreeFuHuoCount()
	local is_free_fuhuo = is_active and free_count > 0
	if is_free_fuhuo then
		self.node_list.viptz_fuhuo_count.text.text = string.format(Language.Fuhuo.FreeCountDesc, free_count)
		self.node_list.free_fuhuo_count2.text.text = string.format(Language.Fuhuo.FreeCountDesc, free_count)
	end
	self.is_free_fuhuo = is_free_fuhuo
	self.node_list.btn_viptz_fuhuo:SetActive(is_free_fuhuo)
	self.node_list.btn_gold_fuhuo:SetActive(not is_free_fuhuo)
end

function EternalNightFuHuoView:SetFuhuoCallback(common_callback,here_callback)
	self.fuhuo_common_callback = common_callback
	self.fuhuo_here_callback = here_callback
end

function EternalNightFuHuoView:FuhuoCallback()
	if self.fuhuo_common_callback and self.fuhuo_type == FuHuoType.Common then
		self.fuhuo_common_callback()
	elseif self.fuhuo_here_callback and self.fuhuo_type == FuHuoType.Here then
		self.fuhuo_here_callback()
	end
	
	self:Close()
end

function EternalNightFuHuoView:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid, type, param)
	self.killer_name = killer_name or ''
	self.is_role = is_role or false
	self.killer_level = killer_level or 0
	self.type = type or nil
	self.param = param or nil
	self:Flush()
end

-- 倒计时
function EternalNightFuHuoView:DaoJiShi(time)
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType()
	if cur_grade_type and (cur_grade_type == EternalNightWGData.GradeType.Collect 
		or cur_grade_type == EternalNightWGData.GradeType.Snatch) then

		time = time or COMMON_CONSTS.REALIVE_TIME
		self.node_list.lbl_tips2.text.text = time
		self.node_list["fuhuo_time_slider1"].image.fillAmount = 1
	elseif cur_grade_type and cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		self.node_list["daojishi_text"].text.text = time
		self.node_list["fuhuo_time_slider2"].image.fillAmount = 1
	end

	CountDownManager.Instance:RemoveCountDown(EternalNightFuHuoView.COUNTDOWNTYPE)
	self.node_list.lbl_tips2.text.text = time

	CountDownManager.Instance:AddCountDown(EternalNightFuHuoView.COUNTDOWNTYPE,
		BindTool.Bind1(self.UpdateCountDownTime, self),
		BindTool.Bind(self.CompleteCountDownTime, self, true),
		nil, time, 0.5)
end

-- 倒计时每次循环执行的函数
function EternalNightFuHuoView:UpdateCountDownTime(elapse_time, total_time)
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType()
	local last_time = math.floor(total_time - elapse_time)
	if cur_grade_type and (cur_grade_type == EternalNightWGData.GradeType.Collect 
		or cur_grade_type == EternalNightWGData.GradeType.Snatch) then

		local str = last_time
		if self.node_list.lbl_tips2 then
			self.node_list.lbl_tips2.text.text = str
		else
			print_log(string.format(Language.Common.DebugStr, "['shijian'] is nil"))
		end

		if self.node_list["fuhuo_time_slider1"] then
			self.node_list["fuhuo_time_slider1"].image.fillAmount = last_time / total_time
		end
	elseif cur_grade_type and cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		if not self.node_list["daojishi_text"] then
			return
	    end

		self.node_list["daojishi_text"].text.text = last_time

		if self.node_list["fuhuo_time_slider2"] then
			self.node_list["fuhuo_time_slider2"].image.fillAmount = last_time / total_time
		end
	end
end

function EternalNightFuHuoView:CompleteCountDownTime(is_auto_fuhuo)
	local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType()
	if cur_grade_type and (cur_grade_type == EternalNightWGData.GradeType.Collect 
		or cur_grade_type == EternalNightWGData.GradeType.Snatch) then
		
		-- 防止倒计时结束没有自动免费复活
		if self.node_list.btn_free_fuhuo and self.node_list.btn_free_fuhuo:GetActive() then
			XUI.SetButtonEnabled(self.node_list.btn_free_fuhuo, true)
		end
		if is_auto_fuhuo then
			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
		end
	elseif cur_grade_type and cur_grade_type == EternalNightWGData.GradeType.Eliminate then
		local can_fuhuo = EternalNightWGData.Instance:GetEliminateCanFuHuo()
		if can_fuhuo then
			-- 防止倒计时结束没有自动免费复活
			if self.node_list.btn_free_fuhuo2 and self.node_list.btn_free_fuhuo2:GetActive() then
				XUI.SetButtonEnabled(self.node_list.btn_free_fuhuo2, true)
			end
			if is_auto_fuhuo then
				FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
			end
			return
		end
		self.node_list["daojishi_text"].text.text = ""
		self:Close()
	end
end

--点击元宝复活
function EternalNightFuHuoView:OnGoldFuHuoHandler()
	local scene_type = Scene.Instance:GetSceneType()
	if false == self.is_complete_gold_timer and (scene_type ~= SceneType.Kf_PVP) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fuhuo.TimeEndOperate)
		return
	end

    self.fuhuo_type = FuHuoType.Here
    
    if self.is_free_fuhuo then
    	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.TOZI_FREE)
    else
	    local fuhuo_stone = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE)
	    if fuhuo_stone > 0 then
	        FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_STUFF, 0, -1)
	    else
	        FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
	    end
    end

	local old_obj = ReviveWGData.Instance:GetBeHitData()
	if old_obj and old_obj:IsRole() and scene_type ~= SceneType.ZhuXie and scene_type ~= SceneType.KFZhuXieZhanChang then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, old_obj, SceneTargetSelectType.SELECT)
	end
	EternalNightWGData.Instance:ClearDropEquipData()
end

-- 点击复活点复活
function EternalNightFuHuoView:OnFuhuoDianHandler()
	if false == self.is_complete_free_timer then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fuhuo.TimeEndOperate)
		return
	end

	self.fuhuo_type = FuHuoType.Common
	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
	EternalNightWGData.Instance:ClearDropEquipData()
end

-- 创建功能头像
function EternalNightFuHuoView:CreateGongNengHead()
	if nil ~= self.gongneng_sort then
		for i = 1, 3 do
			self.node_list["btn_getway"..i]:SetActive(false)
		end
		local timecount = 0
		for i, v in ipairs(self.gongneng_sort) do
			if nil ~= v.img_name then
				timecount = timecount + 1
				self.node_list["btn_getway"..i]:SetActive(true)
				--self.node_list["getway_img"..i].image:LoadSprite(ResPath.GetCommonPanel(v.img_name))
				XUI.AddClickEventListener(self.node_list["btn_getway"..i], BindTool.Bind(self.ClickFunIconHandler, self, v))
			end
		end
	end
end

function EternalNightFuHuoView:ClickFunIconHandler(param_t)
	ViewManager.Instance:Open(param_t.view_name, param_t.tab_index)
end

function EternalNightFuHuoView:GetUseFuHuoType()
	return self.fuhuo_type
end