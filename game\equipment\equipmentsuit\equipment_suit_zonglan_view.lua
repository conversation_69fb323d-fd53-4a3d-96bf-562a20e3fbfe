EquipmentSuitZongLanView = EquipmentSuitZongLanView or BaseClass(SafeBaseView)

function EquipmentSuitZongLanView:__init()
    self:SetMaskBg(true,true)
    self:AddViewResource(0, "uis/view/equipment_suit_ui_prefab", "layout_suit_zonglan_view")
end

function EquipmentSuitZongLanView:SetDataAndOpen(data)
    if IsEmptyTable(data) then
        return
    end

    self.show_data = data

    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function EquipmentSuitZongLanView:ReleaseCallBack()
    if not IsEmptyTable(self.equip_suit_attr_list) then
        for k,v in pairs(self.equip_suit_attr_list) do
            v:DeleteMe()
        end
        self.equip_suit_attr_list = {}
    end

    if not IsEmptyTable(self.xianqi_suit_attr_list) then
        for k,v in pairs(self.xianqi_suit_attr_list) do
            v:DeleteMe()
        end
        self.xianqi_suit_attr_list = {}
    end
end

function EquipmentSuitZongLanView:LoadCallBack()
    self.equip_suit_attr_list = {}
    self.xianqi_suit_attr_list = {}
    for i=1,3 do
        local group_obj = self.node_list["equip_suit_attr_list"]
        local obj = ResMgr:Instantiate(self.node_list["attri_num_render"].gameObject)
        local obj_transform = obj.transform
        obj_transform:SetParent(group_obj.transform, false)
        self.equip_suit_attr_list[i] = SuitZongLanRender.New(obj)
        self.equip_suit_attr_list[i]:SetIndex(i)
    end
    for i=1,3 do
        local group_obj = self.node_list["xianqi_suit_attr_list"]
        local obj = ResMgr:Instantiate(self.node_list["attri_num_render"].gameObject)
        local obj_transform = obj.transform
        obj_transform:SetParent(group_obj.transform, false)
        self.xianqi_suit_attr_list[i] = SuitZongLanRender.New(obj)
        self.xianqi_suit_attr_list[i]:SetIndex(i)
    end

end

function EquipmentSuitZongLanView:OnFlush()
    if IsEmptyTable(self.show_data) then
        return
    end

    local nor_equiop_data, xianqi_equip_data, cap = EquipmentWGData.Instance:GetToTalEquipSuitZongLangData(self.show_data.equip_body_seq)
    local nor_has_active, xianqi_has_active = true, true

    for k, v in ipairs(self.equip_suit_attr_list) do
        local data = nor_equiop_data[k]

        if data and data.active then
            v:SetData(data)
            v:SetActive(true)
            nor_has_active = false
        else
            v:SetActive(false)
        end
    end

    for k,v in ipairs(self.xianqi_suit_attr_list) do
        local data = xianqi_equip_data[k]

        if data and data.active then
            v:SetData(data)
            v:SetActive(true)
            xianqi_has_active = false
        else
            v:SetActive(false)
        end
    end

    self.node_list["equip_not_attr"]:SetActive(nor_has_active)
    self.node_list["xianqi_not_attr"]:SetActive(xianqi_has_active)
    self.node_list["cap_value"].text.text = cap

    -- print_error(nor_equiop_data)
    -- print_error(xianqi_equip_data)
    -- print_error(cap)


    -- local equip_data = EquipmentWGData.Instance:GetEquipSuitZongLangData()
    -- local equip_active_num = #equip_data
    -- for i,v in ipairs(self.equip_suit_attr_list) do
    --     if i <= equip_active_num then
    --         v:SetActive(true)
    --         local data = equip_data[i]
    --         v:SetData(data)
    --     else
    --         v:SetActive(false)
    --     end
    -- end

    -- local xianqi_data = EquipmentWGData.Instance:GetXianQiSuitZongLangData()
    -- local xianqi_active_num = #xianqi_data
    -- for i,v in ipairs(self.xianqi_suit_attr_list) do
    --     if i <= xianqi_active_num then
    --         v:SetActive(true)
    --         local data = xianqi_data[i]
    --         v:SetData(data)
    --     else
    --         v:SetActive(false)
    --     end
    -- end
    -- self.node_list["equip_not_attr"]:SetActive(equip_active_num <= 0)
    -- self.node_list["xianqi_not_attr"]:SetActive(xianqi_active_num <= 0)
    -- local cap = EquipmentWGData.Instance:GetXianQiSuitZongLangAllCap()
    -- self.node_list["cap_value"].text.text = cap
end


------------------------------SuitZongLanRender----------------------------
SuitZongLanRender = SuitZongLanRender or BaseClass(BaseRender)
function SuitZongLanRender:__init()
    self.attr_list = {}
    local attr_num = 7
    local attr_list_obj = self.node_list.attr_list
    for i = 1, attr_num do
        self.attr_list[i] = {}
        local key = "attr_" .. i
        self.attr_list[i].attr_parent = attr_list_obj:FindObj(key)
        self.attr_list[i].attr_name = attr_list_obj:FindObj(key .. "/attr_name")
        self.attr_list[i].attr_value = attr_list_obj:FindObj(key .. "/attr_value")
    end
end

function SuitZongLanRender:__delete()
    self.attr_list = nil
end

function SuitZongLanRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    local suit_name = EquipmentWGData.Instance:GetEquipmenSuitTitleName(self.data.equip_body_seq, self.data.equip_type)
    local need_str = string.format(Language.Equip.SuitNumCompany3, self.data.cfg.same_order_num)

    -- if self.data.equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
    --     suit_name = Language.Equip.XianQiSuit
    -- else
    --     suit_name = EquipmentWGData.Instance:GetNormalEquipSuitShowNameCfg(self.data.equip_body_seq)
    -- end

    self.node_list["suit_name"].text.text = string.format("%s%s", suit_name, need_str)

    local cal_attr_data = function(attr_cfg)
        local attr_list = AttributeMgr.GetAttributteValueByClass(attr_cfg)
        local attr_data = {}
        local tem_index = 1

        for name, value in pairs(attr_list) do
            attr_data[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
            tem_index = tem_index + 1
        end
    
        if not IsEmptyTable(attr_data) then
            table.sort(attr_data, SortTools.KeyLowerSorter("sort_index"))
        end

        return attr_data
    end

    local attr_data_list = cal_attr_data(self.data.cfg)
    for k, v in ipairs(self.attr_list) do
        if attr_data_list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_data_list[k].attr_type)
            local name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_data_list[k].attr_type, true)
            local value = is_per and attr_data_list[k].value or attr_data_list[k].value / 100 .. "%"
            v.attr_name.text.text = name
            v.attr_value.text.text = "+" .. value
            v.attr_parent:SetActive(true)
        else
            v.attr_parent:SetActive(false)
        end
    end

    self.view:SetActive(true)

    -- local data = self:GetData()
    -- if IsEmptyTable(data) then
    --     self.view:SetActive(false)
    --     return
    -- else
    --     self.view:SetActive(true)
    -- end

    -- local need_str = string.format(Language.Equip.SuitNumCompany3, data.count)

    -- local order = data.order
    -- local suit_index = data.suit_index
    -- local suit_name = ""
    -- local suit_type = Language.Equip.SuitTypeText[suit_index + 1]
    -- local equip_type = data.equip_type
    -- local equip_part = GameEnum.EQUIP_INDEX_XIANLIAN
    -- local list = {}
    -- if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
    --     suit_name = Language.Equip.XianQiSuit
    --     list = EquipmentWGData.Instance:GetXianQiSuitAttrCfgBy(suit_index, data.count)
    -- else
    --     suit_name = EquipmentWGData.Instance:GetEquipmenSuitName(order)
    --     list = EquipmentWGData.Instance:GetSuitStoneAttrCfgBy(suit_index, order, data.count)
    -- end

    -- self.node_list["suit_name"].text.text = string.format("%s%s%s", suit_type, suit_name, need_str)

    -- for k, v in ipairs(self.attr_list) do
    --     if list[k] then
    --         local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
    --         local name = EquipmentWGData.Instance:GetAttrNameByAttrStr(list[k].attr_type, true)
    --         local value = is_per and list[k].value or list[k].value / 100 .. "%"
    --         v.attr_name.text.text = name
    --         v.attr_value.text.text = "+" .. value
    --         v.attr_parent:SetActive(true)
    --     else
    --         v.attr_parent:SetActive(false)
    --     end
    -- end
end