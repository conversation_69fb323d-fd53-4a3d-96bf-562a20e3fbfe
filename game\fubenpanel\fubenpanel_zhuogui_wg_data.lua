FuBenPanelWGData = FuBenPanelWGData or BaseClass()

function FuBenPanelWGData:InitZhuoGuiData()
    self.zhuogui_task_infos = {}
    self.zhuogui_event_info = {}
    
    RemindManager.Instance:Register(RemindName.ZhuoGuiFuBen, BindTool.Bind(self.GetZhuoGuiTaskRemind, self))
end


function FuBenPanelWGData:DeleteZhuoGuiData()
    RemindManager.Instance:UnRegister(RemindName.ZhuoGuiFuBen)
end

function FuBenPanelWGData:GetZhuoGuiOtherCfg()
    return ConfigManager.Instance:GetAutoConfig("ghost_fb_cfg_auto").other[1]
end

function FuBenPanelWGData:GetZhuoGuiEventCfg(event_id)
    local cfg = ConfigManager.Instance:GetAutoConfig("ghost_fb_cfg_auto")
    return cfg.event[event_id]
end

-- 捉鬼信息
function FuBenPanelWGData:SetGhostFbBaseInfo(protocol)
    self.zhuogui_refresh_event_times = protocol.refresh_event_times
    self.zhuogui_total_refresh_event_times = protocol.total_refresh_event_times
    self.zhuogui_nuqi = protocol.zhuogui_nuqi
end

function FuBenPanelWGData:GetZhuoGuiEventTimes()
    return self.zhuogui_refresh_event_times or 0
end

function FuBenPanelWGData:GetZhuoGuiEventTotalTimes()
    return self.zhuogui_total_refresh_event_times or 0
end

function FuBenPanelWGData:GetZhuoGuiNuQi()
    return self.zhuogui_nuqi or 0
end



-- 捉鬼任务信息
function FuBenPanelWGData:SetGhostFbTaskInfo(protocol)
	self.zhuogui_task_infos = protocol.task_infos
end

-- 捉鬼任务信息更新
function FuBenPanelWGData:SetGhostFbTaskUpdate(protocol)
	self.zhuogui_task_infos[protocol.task_id] = protocol.task_info
end

function FuBenPanelWGData:GetZhuoGuiTaskInfo(task_id)
    return self.zhuogui_task_infos[task_id]
end

function FuBenPanelWGData:GetZhuoGuiTaskList()
    return ConfigManager.Instance:GetAutoConfig("ghost_fb_cfg_auto").task
end

function FuBenPanelWGData:GetZhuoGuiTaskSortList()
    local list = {}
    local task_list = self:GetZhuoGuiTaskList()
    for k,v in ipairs(task_list) do
        local data = {}
        data.cfg = v
        local info = self:GetZhuoGuiTaskInfo(v.task_id)
        data.sort = (info and info.status ==3) and v.task_id + 10000 or v.task_id
        table.insert(list, data)
    end

    SortTools.SortAsc(list, "sort")
    return list
end

function FuBenPanelWGData:GetZhuoGuiTaskNoFinishNum()
    local num = 0
    local list = self:GetZhuoGuiTaskList()
    for k,v in ipairs(list) do
        local info = self:GetZhuoGuiTaskInfo(v.task_id)
        if info and info.status ~= 2 then
            num = num + 1
        end
    end

    return num
end

function FuBenPanelWGData:GetZhuoGuiTaskRemind()
    local list = self:GetZhuoGuiTaskList()
    for k,v in ipairs(list) do
        local info = self:GetZhuoGuiTaskInfo(v.task_id)
        if info and info.status == 1 then
            return 1
        end
    end

    return 0
end




-- 捉鬼事件信息
function FuBenPanelWGData:SetGhostFbEventInfo(protocol)
	self.zhuogui_event_info = protocol.event_info
end

function FuBenPanelWGData:GetGhostFbEventInfo()
    return self.zhuogui_event_info
end

-- 事件状态
function FuBenPanelWGData:GetZhuoGuiEventSatus()
    local other_cfg = self:GetZhuoGuiOtherCfg()
    local max_count = other_cfg.max_refresh_event_times
    local cur_count = FuBenPanelWGData.Instance:GetZhuoGuiEventTimes()
    if max_count - cur_count <= 0 then
        return GHOST_FB_EVENT_STATUS.FINISH
    end

    if self.zhuogui_event_info.event_id and self.zhuogui_event_info.event_id > 0 then
        return GHOST_FB_EVENT_STATUS.ACCEPT
    end

    return GHOST_FB_EVENT_STATUS.NONE
end



-- 捉鬼个人Boss
function FuBenPanelWGData:SetGhostFbPersonSceneInfo(protocol)
	self.zhuogui_boss_info = protocol.info
end

function FuBenPanelWGData:GetGhostFbPersonSceneInfo()
    return self.zhuogui_boss_info
end

function FuBenPanelWGData:GetZhuoGuiBossSceneCfg()
	local info = self:GetGhostFbPersonSceneInfo()
	if not info then
		return nil
	end

	local cfg = ConfigManager.Instance:GetAutoConfig("ghost_fb_cfg_auto").baotu
	for k,v in ipairs(cfg) do
		if v.seq == info.seq and v.monster_id == info.monster_id then
			return v
		end
	end

	return nil
end

function FuBenPanelWGData:GetZhuoGuiBossSceneRewardList()
	local info = self:GetGhostFbPersonSceneInfo()
	if not info then
		return {}
	end

    local seq = info.seq
    local total_num = self:GetZhuoGuiEventTotalTimes()
	local cfg = ConfigManager.Instance:GetAutoConfig("ghost_fb_cfg_auto").baotu_reward

	for k,v in ipairs(cfg) do
		if v.seq == seq and total_num >= v.min_times and total_num <= v.max_times then
			return v.reward_item
		end
	end

	return {}
end

function FuBenPanelWGData:GetZhuoGuiBossSceneCfgByItemId(item_id)
    if not self.zhuogui_boss_scene_map_itemid_cfg then
        local cfg = ConfigManager.Instance:GetAutoConfig("ghost_fb_cfg_auto").baotu
        self.zhuogui_boss_scene_map_itemid_cfg = ListToMap(cfg, "item_id")
    end

    return self.zhuogui_boss_scene_map_itemid_cfg[item_id]
end