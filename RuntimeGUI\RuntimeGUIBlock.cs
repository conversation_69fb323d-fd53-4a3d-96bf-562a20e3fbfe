﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeGUIBlock
{
	public GameObject gameObject;
	public RectTransform Block;
	private bool isCanActiveBlock = false;
	private bool activeState = false;

	public void Init()
	{
		gameObject = GameObject.Find("GameRoot/UILayer/RuntimeGUIBlock");
		if (gameObject == null)
		{
			return;
		}
		
		if (RuntimeGUIMgr.Instance.IsGUIOpening() || RuntimeGUIMgr.Instance.IsAndroidGM())
		{
			isCanActiveBlock = true;
		}
		else
		{
			isCanActiveBlock = false;
		}
		gameObject.SetActive(isCanActiveBlock);
        activeState = isCanActiveBlock;
        Block = gameObject.transform.Find("Block").GetComponent<RectTransform>();
	}

	public void ShowRect(Rect rect, float scale)
	{

		if (!isCanActiveBlock)
		{
			return;
		}

		if (Block == null)
		{
			return;
		}
		
		SetBlockActive(true);
		Block.sizeDelta = new Vector2(rect.width, rect.height);
		
		float posX = rect.position.x;  // - Screen.width / 2
		float posY = - rect.position.y; // Screen.height / 2 
		Block.anchoredPosition = new Vector2(posX * scale, posY * scale);
		Block.transform.localScale = new Vector3(scale, scale, scale);
	}

	public void HideRect()
	{	
		if (Block == null)
		{
			return;
		}
		SetBlockActive(false);
	}

	private void SetBlockActive(bool isActive)
	{
		if (activeState == isActive)
		{
			return;
		}	
		if (Block == null)
		{
			return;
		}
		Block.gameObject.SetActive(isActive);
		activeState = isActive;
	}
}