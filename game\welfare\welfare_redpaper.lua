WorldRedPaperView = WorldRedPaperView or BaseClass(SafeBaseView)

function WorldRedPaperView:__init()
	self.view_style = ViewStyle.Full
	self.view_name = GuideModuleName.WorldRedPaper
	self.is_align_right = true						-- 是否向右对齐
	self:SetMaskBg()

	self.default_index = TabIndex.world_paper

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel2")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel_adorn")
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_red_packet_word")
end

function WorldRedPaperView:LoadCallBack()
	--if self.red_pocket_scroll == nil then
		self:CreateRedPacketGrid()
	--end
	local remind_tab = {}
	self.node_list.layout_blank_tip:SetActive(false)
	self.qiangzhi_red_packet_shuaxin = true
	GuildWGData.Instance:SetGuildRedFlush(true)
	self.node_list.btn_red_tips.button:AddClickListener(BindTool.Bind(self.OnClickRedPocketTips, self))
	self.node_list.panel_change.button:AddClickListener(BindTool.Bind(self.OnClickChangePanlel, self))
	self:CreateGetRedPacketWayList()
	self.node_list.panel_change_text.text.text = Language.Channel[4]
	self.node_list.title_view_name.text.lineSpacing = 1
	self.node_list.title_view_name.text.fontSize = 24
end

function WorldRedPaperView:ShowIndexCallBack(index)
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
	self:Flush(index)
end

function WorldRedPaperView:ReleaseCallBack()
	if nil ~= self.red_pocket_scroll then
		self.red_pocket_scroll:DeleteMe()
		self.red_pocket_scroll  = nil
	end

	if nil ~= self.jilu_list then
		self.jilu_list:DeleteMe()
		self.jilu_list  = nil
	end

	if nil ~= self.get_hongbao_way_list then
		self.get_hongbao_way_list:DeleteMe()
		self.get_hongbao_way_list  = nil
	end
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
end

function WorldRedPaperView:CloseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
end

function WorldRedPaperView:ShowRedPacketCallBack()
	self.qiangzhi_red_packet_shuaxin = true
end


function WorldRedPaperView:CreateGetRedPacketWayList()

	if self.get_hongbao_way_list == nil then
		 self.get_hongbao_way_list = AsyncListView.New(WorldGetRedPocketWayItemRender, self.node_list["ph_getred_list"])
		 self.get_hongbao_way_list:SetSelectCallBack(BindTool.Bind(self.RedCellCallBack,self))
 	end

 	local data_lsit = WelfareWGData.Instance:GetGuildSystemRedpaperCfg(2)
 	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local need_remove_table = person_info.send_system_record
	local last_data_list = {}
	for k,v in pairs(data_lsit) do
		local is_need = true
		for m, n in pairs(need_remove_table) do
			if n.red_paper_type == 2 and n.systerm_paper_type == v.type and n.systerm_paper_level >= v.level then
				is_need = false
				break
			end
		end

		if v.type == 2 then
			is_need = false
			break
		end

		if is_need then
			v.is_shoudong = 0
			table.insert(last_data_list, v)
		end
	end
	--插入一条手动发红包数据（特殊处理数据）
	local data = {is_shoudong = 1}
	table.insert(last_data_list, data)
 	if last_data_list then
 		self.get_hongbao_way_list:SetDataList(last_data_list,0)
 	end
end

function WorldRedPaperView:RedCellCallBack(item,index,is_default)
	if is_default then
		return
	end
	--只有手动红包走，其他类型拦截
	if not item or nil == item:GetData() or item:GetData().is_shoudong ~= 1 then
		return
	end

	self:OnClickSendRedPocketTips()
end

function WorldRedPaperView:OnFlush(param_t, index)
	--print_error("FlushWorldPaper",param_t,index)
	-- for k, v in pairs(param_t) do
	-- 	if "all" == k then
	-- 		if index == TabIndex.world_paper then
	-- 			self:FlushWorldPaper()
	-- 		end
	-- 	end
	-- end
	self:FlushWorldPaper()
end
function WorldRedPaperView:FlushWorldPaper()
	--print_error("FlushWorldPaper")
	local is_show_point = GuildWGData.Instance:IsShowGuildZongLanRedBagRedPoint()
	self.node_list.red_paper_panel_tips:SetActive(is_show_point == 1)
	self:CreateGetRedPacketWayList()

	local red_paper_info1 = WelfareWGData.Instance:GetWorldRedpaperAllInfo()
	local red_paper_info = DeepCopy(red_paper_info1)
	local temp_list = {}
	for k,v in pairs(red_paper_info) do
		if v.owner_uid > 0 then
			table.insert(temp_list,v)
		end
	end

	local role_uid = RoleWGData.Instance:InCrossGetOriginUid()

	for k,v in pairs(temp_list) do
		v.sort_order = 3
		local data_base_cfg = {}
		if v.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(v.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperSeqCfg(v.paper_type,v.paper_level)
		end
		local have_get_count = #v.record_list

		for m,n in pairs(v.record_list) do
			if n.uid == role_uid then
				v.sort_order = 2
			end
		end

		if v.sort_order ~= 2 and data_base_cfg and have_get_count >= data_base_cfg.num then
			v.sort_order = 1
		end
	end
	table.sort( temp_list, SortTools.KeyUpperSorter("sort_order") )

	self.node_list["no_red_hint"]:SetActive(#temp_list <= 0)
	self.red_pocket_scroll:SetDataList(temp_list, 3)
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	if IsEmptyTable(person_info) then return end
	local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
	--local count = data_base_cfg_info.world_daily_fetch_count_limit - person_info.world_receive_count
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	if role_vip >= data_base_cfg_info.double_fetch_vip then
		self.node_list.lbl_tips_havecount.text.text = string.format(Language.Welfare.RedPaperHaveCount,person_info.day_distribute_ticket_count,data_base_cfg_info.vip_sliver_ticket_max)
		self.node_list.lbl_tips_havecount_1.text.text = string.format(Language.Welfare.RedPaperHaveCount_1,person_info.day_receive_gold_count,data_base_cfg_info.vip_gold_bind_max)
	else
		self.node_list.lbl_tips_havecount.text.text = string.format(Language.Welfare.RedPaperHaveCount,person_info.day_distribute_ticket_count,data_base_cfg_info.sliver_ticket_max)
		self.node_list.lbl_tips_havecount_1.text.text = string.format(Language.Welfare.RedPaperHaveCount_1,person_info.day_receive_gold_count,data_base_cfg_info.gold_bind_max)
	end
	--self.node_list.lbl_tips_havecount.text.text = string.format(Language.Welfare.RedPaperHaveCount,count)
end

function WorldRedPaperView:CreateRedPacketGrid()
	local bundle, asset = "uis/view/guild_ui_prefab", "ph_red_item"
	self.red_pocket_scroll = AsyncBaseGrid.New()
	self.red_pocket_scroll:CreateCells({col = 4, cell_count = 100, itemRender = WorldRedPocketGridItemRender,
		list_view = self.node_list["ph_red_scroll"], assetBundle = bundle, assetName = asset, change_cells_num = 1})
	self.red_pocket_scroll:SetStartZeroIndex(false)
end

function WorldRedPaperView:OnClickRedPocketTips()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.WorlddAfterTips)
		role_tip:SetContent(Language.Guild.GuildTips)
	else
		print_error("WorldRedPaperView:ClickbtnGuildCK","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end
--主动发送红包
function WorldRedPaperView:OnClickSendRedPocketTips()
	local cfg = WelfareWGData.Instance:GetWelfareCfg()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	if role_level < cfg.other_config[1].level_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Welfare.RedPaperLevelLimit,cfg.other_config[1].level_limit) )
		return
	end

	if role_vip_level < cfg.other_config[1].vip_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Welfare.RedPaperVipLimit,cfg.other_config[1].vip_limit))
		return
	end
--	print_error(person_info.day_distribute_count ,cfg.other_config[1].distribute_count_limit_per_day)
	if person_info.day_distribute_count >= cfg.other_config[1].distribute_count_limit_per_day then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedPaperSendOver)
		return
	end
	GuildWGCtrl.Instance:OpenGuildVIPRedView(2)
end
function WorldRedPaperView:OnClickChangePanlel()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotEnterNoGuild)
		return
	end
	ViewManager.Instance:Open(GuideModuleName.GuildView, TabIndex.guild_redpacket)
	self:Close()
end



-------------------------------------------------------------------------------
----------------------------------------------------------------------------
WorldRedPocketGridItemRender = WorldRedPocketGridItemRender or BaseClass(BaseRender)
function WorldRedPocketGridItemRender:__init()

end

function WorldRedPocketGridItemRender:__delete()
	if self.head then
		self.head:DeleteMe()
		self.head = nil
	end
end

function WorldRedPocketGridItemRender:LoadCallBack()
	self.head = BaseHeadCell.New(self.node_list.img_head)
	self.node_list.record_btn.button:AddClickListener(BindTool.Bind(self.OnClickRecord, self))
	self.node_list.img_red_send.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
end

function WorldRedPocketGridItemRender:OnFlush()
	if not self.data then return end
	-- --显示头像(使用新方案)
	self.head:SetData({role_id = self.data.owner_uid, prof = self.data.prof, sex = self.data.sex})
	self.node_list.lbl_role_name.text.text = self.data.owner_game_name
	local data_base_cfg = {}
	if self.data.paper_type == 0 then
		data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(self.data.paper_level)
		if data_base_cfg then
			self.node_list.rich_desc_limit.text.text = data_base_cfg.descript or self.data.str_des
		end
	else
		data_base_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperSeqCfg(self.data.paper_type,self.data.paper_level)
		if data_base_cfg then
			self.node_list.rich_desc_limit.text.text = data_base_cfg.descript
		end
	end

	local role_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local is_get_over = false
	local have_get_count = 0
	for k,v in pairs(self.data.record_list) do
		if v.uid > 0 then
			have_get_count = have_get_count + 1
		end
	end
	for k,v in pairs(self.data.record_list) do

		if v.uid == role_uid then
			is_get_over = true
			break
		end
	end

	local num = data_base_cfg and data_base_cfg.num or 0
	local is_num_over = have_get_count >= num
	if is_num_over then
		--判断自己是否领取过
		self.node_list.record_btn:SetActive(true)--查看他人手气
		self.node_list.img_red_send:SetActive(false)--开
		self.node_list.number_paper_group:SetActive(false)--总数量
	else
		self.node_list.record_btn:SetActive(is_get_over)
		self.node_list.img_red_send:SetActive(not is_get_over)
		self.node_list.number_paper_group:SetActive(not is_get_over)
	end
	self.node_list.yicuoguo:SetActive(not is_get_over and is_num_over)--已错过
	self.node_list.img_red_yilingqu_text:SetActive(is_get_over)--已领取

	local b,a
	local is_enough = false
	local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	if data_base_cfg and data_base_cfg.bind_gold > 0 then
		self.node_list.number_paper.text.text = data_base_cfg.bind_gold
		b,a = ResPath.GetF2CommonIcon("i_xiaohao_bangyu")

		if role_vip >= data_base_cfg_info.double_fetch_vip then
			is_enough = person_info.day_receive_gold_count >= data_base_cfg_info.vip_gold_bind_max
		else
			is_enough = person_info.day_receive_gold_count >= data_base_cfg_info.gold_bind_max
		end

	else
		self.node_list.number_paper.text.text = data_base_cfg and data_base_cfg.silver_ticket or 0
		b,a = ResPath.GetF2CommonIcon("i_xiaohao_yuanbao")
		if role_vip >= data_base_cfg_info.double_fetch_vip then
			is_enough = person_info.day_distribute_ticket_count >= data_base_cfg_info.vip_sliver_ticket_max
		else
			is_enough = person_info.day_distribute_ticket_count >= data_base_cfg_info.sliver_ticket_max
		end
	end
	XUI.SetButtonEnabled(self.node_list.img_red_send,not is_enough)

	self.node_list["ticket_image"].image:LoadSprite(b,a,function()
		XUI.ImageSetNativeSize(self.node_list["ticket_image"])
	end)
end

function WorldRedPocketGridItemRender:OnClickLingQu()
	-- local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	-- local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	-- if person_info.world_receive_count >= red_cfg.other_config[1].world_daily_fetch_count_limit then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedPaperGetOver)
	-- 	return
	-- end

	--local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	--print_error(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_GUILD,self.data.info_index)
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_WORLD,self.data.info_index,0,"")
	self:OnClickRecord()
end

function WorldRedPocketGridItemRender:OnClickRecord()
	GuildWGCtrl.Instance:OpenGuildRedPacketView(self.data,2)
end



-------------------------------------------------------------------------------
OPEN_VIEE_TYPE = {
	SHOU_CHONG = 1,
	UP_VIP = 2,
	TOUZIJIHUA = 3,
	LEICHONG = 4,
	GUILD_DATI = 5,
}
----------------------------------------------------------------------------
WorldGetRedPocketWayItemRender = WorldGetRedPocketWayItemRender or BaseClass(BaseRender)
function WorldGetRedPocketWayItemRender:__init()
end

function WorldGetRedPocketWayItemRender:__delete()

end

function WorldGetRedPocketWayItemRender:LoadCallBack()
	self.node_list.btn_open_viewname.button:AddClickListener(BindTool.Bind(self.OnClickOpenView, self))

end

function WorldGetRedPocketWayItemRender:OnFlush()
	if not self.data then
		return
	end

	if self.data.is_shoudong == 1 then
		self.node_list["normal_panel"]:SetActive(false)
		self.node_list["specila_panel"]:SetActive(true)
		self:OnFlushSpecialPanel()
	else
		self.node_list["normal_panel"]:SetActive(true)
		self.node_list["specila_panel"]:SetActive(false)
		self:OnFlushNormalPanel()
	end
end

function WorldGetRedPocketWayItemRender:OnFlushNormalPanel()
	self.node_list.rich_desc.text.text = self.data.goal_descript
	if self.data.bind_gold > 0 then
		--self.node_list.gold_image.image:LoadSprite(ResPath.GetF2CommonIcon("i_xiaohao_bangyu"))
		self.node_list.gold_num.text.text = self.data.bind_gold
		local b,a = ResPath.GetF2CommonIcon("i_xiaohao_bangyu")
		self.node_list.gold_image.image:LoadSprite(b,a,function()
			XUI.ImageSetNativeSize(self.node_list["gold_image"])
		end)
	else
		--self.node_list.gold_image.image:LoadSprite(ResPath.GetF2CommonIcon("i_xiaohao_yuanbao"))
		local b,a = ResPath.GetF2CommonIcon("i_xiaohao_yuanbao")
		self.node_list.gold_image.image:LoadSprite(b,a,function()
			XUI.ImageSetNativeSize(self.node_list["gold_image"])
		end)
		self.node_list.gold_num.text.text = self.data.silver_ticket
	end
	local limit_info = WelfareWGData.Instance:GetPersonRedpaperInfo().redpaper_list
	XUI.SetButtonEnabled(self.node_list.btn_open_viewname,self.data.sort_index == 0 or self.data.type == 2)
	-- for k,v in pairs(limit_info) do
	-- 	if v.red_paper_type == 1 and v.paper_type == self.data.type and v.paper_level == self.data.level then
	-- 		XUI.SetButtonEnabled(self.node_list.btn_open_viewname,true)
	-- 		break
	-- 	end
	-- end
end

function WorldGetRedPocketWayItemRender:OnFlushSpecialPanel()
	local shengyu_num, max_num = WelfareWGData.Instance:GetDailyRedBagNumInfo()
	-- self.node_list["rich_num_limint"].text.text = shengyu_num .. "/" .. max_num
	self.node_list["rich_num_limint"].text.text = string.format(Language.Guild.DayLimintRed, shengyu_num)
end

function WorldGetRedPocketWayItemRender:OnClickOpenView()
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM,self.data.info_index)
	local content = self.data.Chanel_descript .. "{openLink;119}"
	ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, content, CHAT_CONTENT_TYPE.TEXT, nil, nil, true)
end
