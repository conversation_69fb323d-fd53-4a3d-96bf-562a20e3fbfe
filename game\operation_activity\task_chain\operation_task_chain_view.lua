function OperationActivityView:LoadIndexCallBackTask<PERSON>hain()
	self.node_list.btn_go.button:AddClickListener(BindTool.Bind1(self.OnClickTaskChainGo, self))
	self.node_list.btn_slider.button:AddClickListener(BindTool.Bind1(self.OnClickPreview, self))
	self.node_list.task_chain_right_flag_icon.button:AddClickListener(BindTool.Bind1(self.OnClickGetItem, self))

	self:FlushTaskChainPictureAndTextContent()
end

function OperationActivityView:ReleaseTaskChainView()
	if CountDownManager.Instance:HasCountDown("task_chain_act_view_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_act_view_timer")
	end

    if self.task_chain_reward_list then
    	self.task_chain_reward_list:DeleteMe()
    	self.task_chain_reward_list = nil
    end

    if self.task_chain_task_list ~= nil then
    	for k,v in pairs(self.task_chain_task_list) do
    		if v then
    			v:DeleteMe()
    		end
    	end

    	self.task_chain_task_list = nil
    end

    if self.task_chain_box_list ~= nil then
    	for k,v in pairs(self.task_chain_box_list) do
    		if v then
    			v:DeleteMe()
    		end
    	end

    	self.task_chain_box_list = nil
	end
	
	self.task_chain_interface_cfg = nil
end

function OperationActivityView:FlushTaskChainPictureAndTextContent()
	self.task_chain_interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if not self.task_chain_interface_cfg then
		return 
	end

	self.node_list.fish_line_1.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.task_chain_interface_cfg.pic_2))
	self.node_list.fish_line_1.image:SetNativeSize()
	self.node_list.fish_line_2.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.task_chain_interface_cfg.pic_2))
	self.node_list.fish_line_2.image:SetNativeSize()
	self.node_list.fish_line_3.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.task_chain_interface_cfg.pic_2))
	self.node_list.fish_line_3.image:SetNativeSize()
	self.node_list.fish_line_4.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.task_chain_interface_cfg.pic_2))
	self.node_list.fish_line_4.image:SetNativeSize()
	self.node_list.task_chain_left_flag_icon.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.task_chain_interface_cfg.pic_11))
	self.node_list.task_chain_left_flag_icon.image:SetNativeSize()

	self.node_list.btn_go.image:LoadSprite(ResPath.GetCommonButtonToggle_atlas(self.task_chain_interface_cfg.pic_10))
	self.node_list.btn_go.image:SetNativeSize()

	-- self.node_list.btn_slider:LoadSprite(ResPath.GetOperationTaskChainF2(  ))
	self.node_list.slider_fill.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.task_chain_interface_cfg.pic_9))
	self.node_list.reward_list_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("task_chain_act_" .. self.task_chain_interface_cfg.pic_12))
	self.node_list.reward_list_bg.raw_image:SetNativeSize()

	self.node_list.task_chain_right_rawbg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(self.task_chain_interface_cfg.pic_1))
	self.node_list.task_chain_right_rawbg.raw_image:SetNativeSize()

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.task_chain_interface_cfg.item_id)
	if item_cfg ~= nil then
		self.node_list.text_get_item.text.text = Language.OpertionAcitvity.TaskChain.TaskChainOpenStr .. item_cfg.name

		local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
		self.node_list.task_chain_right_flag_icon.image:LoadSprite(bundle, asset)
		self.node_list.task_chain_right_flag_icon.image:SetNativeSize()
	end
end

function OperationActivityView:OnClickTaskChainGo()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN or OperationTaskChainWGData.Instance:GetIsTaskChainTaskScene() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.IsInScene)
		return
	end

	local is_open = OperationTaskChainWGData.Instance:GetTaskChainIsOpen()
	if not is_open then
		local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
		if day_index_cfg ~= nil then
			SysMsgWGCtrl.Instance:ErrorRemind(day_index_cfg.tips1)
		end
		return
	end

	local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
	
	if day_index_cfg ~= nil then
		local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
    	if task_chain_cfg ~= nil then
			self:Close()
			BossWGData.Instance:SetBossEnterFlag(false)
    		GuajiWGCtrl.Instance:ForceMoveToNpc(task_chain_cfg.npc_id, nil, task_chain_cfg.npc_scene)
    	end
	end	
end

function OperationActivityView:OnClickPreview()
	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainRewardView)
end

function OperationActivityView:OnClickGetItem()
	if self.task_chain_interface_cfg ~= nil then
		TipWGCtrl.Instance:OpenItem({item_id = self.task_chain_interface_cfg.item_id}, ItemTip.FROM_NORMAL, nil)
	end
end

function OperationActivityView:FlushTaskChainShowReward()
	if self.task_chain_reward_list == nil then
		self.task_chain_reward_list = AsyncListView.New(ItemCell, self.node_list.show_item_list)
	end

	local data_list = OperationTaskChainWGData.Instance:GetShowRewardList()
	self.task_chain_reward_list:SetDataList(data_list)
end

function OperationActivityView:FlushTaskChainTaskInfo()
	local is_need_load = false
	if self.task_chain_task_list == nil then
		is_need_load = true
	end

	local show_list = OperationTaskChainWGData.Instance:GetShowTaskList()
	if show_list == nil or #show_list == 0 then
		return
	end

	local show_num = #show_list
	if self.task_chain_task_list ~= nil and show_num > #self.task_chain_task_list then
		is_need_load = true
	end

	if is_need_load then
		local asset,bundle = ResPath.GetOperationTaskChainPrefabF2("render_task_chain_icon")
		local res_async_loader = AllocResAsyncLoader(self, "render_task_chain_icon")
		res_async_loader:Load(asset, bundle, nil,
			function(new_obj)
				if nil == new_obj then return end

				local cur_num = 1
				if self.task_chain_task_list ~= nil then
					cur_num = show_num - #self.task_chain_task_list
				else
					self.task_chain_task_list = {}
				end

				for i = cur_num, show_num do
					local obj = ResMgr:Instantiate(new_obj)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.node_list.root_task.transform, false)
					local item_render = TaskChainTaskInfoRender.New(obj)
					table.insert(self.task_chain_task_list, item_render)
				end

				for k,v in pairs(self.task_chain_task_list) do
					v:SetIndex(k)
					v:SetData(show_list[k])
				end
			end)
	else
		if self.task_chain_task_list == nil then
			return
		end

		for i = 1, show_num do
			if self.task_chain_task_list[i] ~= nil then
				self.task_chain_task_list[i]:SetActive(true)
				self.task_chain_task_list[i]:SetIndex(i)
				self.task_chain_task_list[i]:SetData(show_list[i])
			end
		end

		if show_num < #self.task_chain_task_list then
			for i = show_num, #self.task_chain_task_list do
				if self.task_chain_task_list[i] ~= nil then
					self.task_chain_task_list[i]:SetActive(false)
				end
			end
		end
	end
end

function OperationActivityView:FlushTaskChainBoxReward()
	local is_need_load = false

	local data_list, value = OperationTaskChainWGData.Instance:GetBoxDataInfo()
	if data_list == nil or #data_list == 0 then
		return
	end

	local show_num = #data_list
	local max_show = GameEnum.TASK_CHAIN_MAX_SHOW_BOX_NUM

	if self.task_chain_box_list == nil then
		is_need_load = true
	else
		if #self.task_chain_box_list < show_num and #self.task_chain_box_list < max_show then
			is_need_load = true
		end
	end

	if is_need_load then
		local asset,bundle = ResPath.GetOperationTaskChainPrefabF2("render_task_chain_box")
		local res_async_loader = AllocResAsyncLoader(self, "render_task_chain_box")
		res_async_loader:Load(asset, bundle, nil,
			function(new_obj)
				if nil == new_obj then return end
				local load_num = show_num > max_show and max_show or show_num
				if self.task_chain_box_list ~= nil then
					load_num = show_num - #self.task_chain_box_list
				else
					self.task_chain_box_list = {}
				end

				for i = 1, load_num do
					local obj = ResMgr:Instantiate(new_obj)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.node_list.root_reward.transform, false)
					local item_render = TaskChainBoxRender.New(obj)
					table.insert(self.task_chain_box_list, item_render)					
				end

				for k,v in pairs(self.task_chain_box_list) do
					v:SetData(data_list[k])
				end	
			end)
	else
		if self.task_chain_box_list == nil then
			return
		end

		for i = 1, show_num do
			if self.task_chain_box_list[i] ~= nil then
				self.task_chain_box_list[i]:SetActive(true)
				self.task_chain_box_list[i]:SetData(data_list[i])
			end
		end

		if show_num < #self.task_chain_box_list then
			for i = show_num, #self.task_chain_box_list do
				if self.task_chain_box_list[i] ~= nil then
					self.task_chain_box_list[i]:SetActive(false)
				end
			end
		end		
	end

	if self.node_list.progress_reward ~= nil then
		self.node_list.progress_reward.slider.value = value
	end
end

function OperationActivityView:OnFlushTaskChainView()
	self:FlushTaskChainPictureAndTextContent()
	
	if CountDownManager.Instance:HasCountDown("task_chain_act_view_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_act_view_timer")
	end

    local data = OperationTaskChainWGData.Instance:GetTaskChainInfo()
    if data == nil then
    	return
    end

    if data.mingwang and data.mingwang > 0 then
    	self.node_list.str_xian_xia.text.text = data.mingwang
    else
    	self.node_list.str_xian_xia.text.text = ""
    end

    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
    if act_info ~= nil then
    	local count_time = act_info.end_time - TimeWGCtrl.Instance:GetServerTime()
    	if count_time > 0 then
    		local show_time = OperationTaskChainWGData.Instance:GetViewShowTime()
    		if show_time ~= 0 then
    			self:SetActRemainTime(TabIndex.operation_act_task_chain, show_time)
    		end
    		-- CountDownManager.Instance:AddCountDown("task_chain_act_view_timer", BindTool.Bind1(self.FlushTaskChainActTime, self), nil, act_info.end_time, nil, 1)
    	end
    end

    local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
    if day_index_cfg ~= nil then
    	local open_str = Language.OpertionAcitvity.TaskChain.TaskChainOpenStr
    	local open_tab = Split(day_index_cfg.task_chain_start, "|")
    	if open_tab ~= nil and next(open_tab) ~= nil then
    		for i = 1, #open_tab do
    			open_str = open_str .. string.sub(open_tab[i], 1, 2) .. ":" ..string.sub(open_tab[i], 3, 4)
    			if i < #open_tab then
    				open_str = open_str .. ","
    			end
    		end
    	end

    	self.node_list.str_open_time.text.text = open_str

    	local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
    	if task_chain_cfg ~= nil then
    		self:SetOutsideRuleTips(task_chain_cfg.rule_1)
    		self:SetRuleInfo(task_chain_cfg.rule_2, task_chain_cfg.task_chain_name)

			self.node_list.str_btn_go.text.text = task_chain_cfg.btn_str
			self.node_list.right_title_1.text.text = task_chain_cfg.title_1
			self.node_list.right_title_2.text.text = task_chain_cfg.title_2
			
			self.node_list.task_chain_left_rawbg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(task_chain_cfg.pic_1))
			self.node_list.task_chain_left_rawbg.raw_image:SetNativeSize()
			self.node_list.task_chain_left_desc_rawimg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(task_chain_cfg.pic_2))
			self.node_list.task_chain_left_desc_rawimg.raw_image:SetNativeSize()

    		local scene_cfg = ConfigManager.Instance:GetSceneConfig(task_chain_cfg.npc_scene)
    		local npc_str = ""
    		if scene_cfg ~= nil then
    			if scene_cfg.npcs then
    				for k,v in pairs(scene_cfg.npcs) do
    					if v.id == task_chain_cfg.npc_id then
							npc_str = string.format(Language.OpertionAcitvity.TaskChain.TaskChainGoStrParam, scene_cfg.name, task_chain_cfg.npc_name, v.x, v.y, task_chain_cfg.btn_str)
							npc_str = Language.OpertionAcitvity.TaskChain.TaskChainGoStr .. ToColorStr(npc_str, COLOR3B.GREEN)
    						break
    					end
    				end
    			end
    		end

    		self.node_list.str_go_way.text.text = npc_str
    	end
    end

	-- local asset, bundle
	-- self.node_list.img_tip.image:LoadSprite(asset, bundle)
	-- self.node_list.img_tip.image:SetNativeSize()

    -- local sub_act_info = ServerActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
    -- local is_open = false
    -- if sub_act_info ~= nil and sub_act_info.status ~= ACTIVITY_STATUS.CLOSE then
    -- 	is_open = true
    -- end

	XUI.SetGraphicGrey(self.node_list.btn_go, not OperationTaskChainWGData.Instance:GetTaskChainIsOpen())

	self.node_list.img_red_enter:SetActive(OperationTaskChainWGData.Instance:GetIsCanEnter())
	self:FlushTaskChainShowReward()
	self:FlushTaskChainTaskInfo()
	self:FlushTaskChainBoxReward()
end

function OperationActivityView:FlushTaskChainActTime(elapse_time, total_time)
	if self.node_list.str_act_time ~= nil then
		local value = math.floor(total_time - elapse_time)
		local str = string.format(Language.OpenServer.ActRemainTimeNew, TimeUtil.FormatSecondDHM2(value))
		self.node_list.str_act_time.text.text = str
	end
end


-----------------------------------TaskChainTaskInfoRender------------------------------------------------
TaskChainTaskInfoRender = TaskChainTaskInfoRender or BaseClass(BaseRender)

function TaskChainTaskInfoRender:__init()
	self.interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()

	self:FlushPictureAndTextContent()
end

function TaskChainTaskInfoRender:__delete()
	self.interface_cfg = nil
end

function TaskChainTaskInfoRender:FlushPictureAndTextContent()
	if not self.interface_cfg then
		return 
	end

	--[[
	self.node_list.img_icon_bg.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.interface_cfg.pic_3))
	self.node_list.img_icon_bg.image:SetNativeSize()
	self.node_list.name_bg.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.interface_cfg.pic_4))
	self.node_list.name_bg.image:SetNativeSize()
	self.node_list.arrow_img.image:LoadSprite(ResPath.GetOperationTaskChainF2(self.interface_cfg.pic_5))
	self.node_list.arrow_img.image:SetNativeSize()
	--]]
end

function TaskChainTaskInfoRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	-- self.node_list.root_right:SetActive(not data.is_last)
	self.node_list.img_icon.image:LoadSprite(ResPath.GetOperationTaskChainF2(data.task_cfg.icon))
	self.node_list.img_icon.image:SetNativeSize()
	self.node_list.img_finish:SetActive(data.task_flag == 1)
	self.node_list.img_mask:SetActive(data.task_flag == 1)
	self.node_list.str_name.text.text = data.task_cfg.task_name
end

-----------------------------------TaskChainBoxRender------------------------------------------------

TaskChainBoxRender = TaskChainBoxRender or BaseClass(BaseRender)

function TaskChainBoxRender:__init()
	self.node_list.img_box.button:AddClickListener(BindTool.Bind1(self.ClickBox, self))
	self.interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
end

function TaskChainBoxRender:__delete()
	self.interface_cfg = nil
end

function TaskChainBoxRender:ClickBox()
	if not self.data then
		return 
	end

	local value = OperationTaskChainWGData.Instance:GetMingWangValue()
	if self.data.cfg.mingwang <= value and self.data.reward_flag == 0 then
		OperationTaskChainWGCtrl.Instance:SendTaskChainMWRewardFetch(self.data.index)
		if self.data.cfg ~= nil then
			local reward_data = {}
			for k,v in pairs(self.data.cfg.reward_item) do
				table.insert(reward_data, v)
			end
			
			TipWGCtrl.Instance:ShowGetReward(nil, reward_data, false, false, {again_btn = false})
		end
	else
		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainRewardView)
	end
end

function TaskChainBoxRender:OnFlush()
	if not self.data then return end
	local value = OperationTaskChainWGData.Instance:GetMingWangValue()

	if self.interface_cfg then
		local asset, bundle

		if self.data.cfg.mingwang > value then
			asset, bundle = ResPath.GetF2CommonImages(self.interface_cfg.pic_6)
		elseif self.data.cfg.mingwang <= value and self.data.reward_flag == 0 then
			asset, bundle = ResPath.GetF2CommonImages(self.interface_cfg.pic_7)
		elseif self.data.cfg.mingwang <= value and self.data.reward_flag ~= 0 then
			asset, bundle = ResPath.GetF2CommonImages(self.interface_cfg.pic_8)
		end
	
		self.node_list.img_box.image:LoadSprite(asset, bundle)
		self.node_list.img_box.image:SetNativeSize()
	end

	self.node_list.img_red:SetActive(self.data.cfg.mingwang <= value and self.data.reward_flag == 0)
	self.node_list.str_score.text.text = self.data.cfg.mingwang
end