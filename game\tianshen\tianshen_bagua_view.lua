local temp_path = {
	[1] = Vector3(-85, 207, 0),
	[2] = Vector3(-187, 103, 0),
	[3] = Vector3(-187, -65, 0),
	[4] = Vector3(-85, -169, 0),
	[5] = Vector3(85, -169, 0),
	[6] = Vector3(187, -65, 0),
	[7] = Vector3(187, 103, 0),
	[8] = Vector3(85, 207, 0),
}

function TianShenView:BaGuaInit()
	self.cur_select_bagua = 0
	self.color_select_bagua = 0
end

function TianShenView:BaGuaLoadCallBack()
	self.cur_show_bagua_panel = 1
	self.bagua_list_is_load = false
	if nil == self.bagua_list_view then
		self.bagua_list_view = {}
		for i = 1, 8 do
			self.bagua_list_view[i] = BaGuaListCell.New(self.node_list.Ts_bagua_list:FindObj("bagua_list_cell_" .. i))
			self.bagua_list_view[i]:SetIndex(i)
		end
	end

	if not self.suit_attr_render_list then
		self.suit_attr_render_list = {}
		for i = 1, 5 do
			local instance = self.node_list.attr_list_contant:FindObj("taozhuang_attr_item_" .. i)
			self.suit_attr_render_list[i] = BaGuaAttrItemRender.New(instance)
			self.suit_attr_render_list[i]:SetIndex(i)
		end
	end

	if nil == self.bagua_bag_list then
		self.bagua_bag_list = AsyncListView.New(BaGuaBagCell, self.node_list.bagua_bag_list)
	end

	self.node_list.btn_bagua_zhanshi.toggle:AddValueChangedListener(BindTool.Bind(self.ClickBaGuaShowPanel, self, 1))
	self.node_list.btn_bagua_bag.toggle:AddValueChangedListener(BindTool.Bind(self.ClickBaGuaShowPanel, self, 2))

	--[[self.node_list["btn_bagua_zhanshi"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaShowPanel, self, 1))
	self.node_list["btn_bagua_bag"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaShowPanel, self, 2))]]
	self.node_list["bagua_btn_shenqi_shop"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaShop, self))
	self.node_list["TS_bagua_equip_btn"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaQuickEquip, self))
	self.node_list["bagua_skill_lock"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaSkill, self))

	self.node_list["bagua_skill_btn"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaSkill, self))

	for i = 1, 8 do
		self.node_list["bagua_click"..i].button:AddClickListener(BindTool.Bind(self.ClickBaGuaEquip, self, i))
	end

	for i =1, 9 do
		self.node_list["bagua_color_btn"..i].button:AddClickListener(BindTool.Bind(self.ClickBaGuaSelectColor, self, i))
	end

	self.node_list["bagua_color_select_btn"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaSelectPanel, self, 1))
	self.node_list["bagua_close_select"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaSelectPanel, self, 2))
	self.node_list["btn_bagua_zsx"].button:AddClickListener(BindTool.Bind(self.ClickBaGuaSX, self))
	self.node_list["last_btn"].button:AddClickListener(BindTool.Bind(self.BaGuaCellClickCallBack, self, 0))
	self.node_list["next_btn"].button:AddClickListener(BindTool.Bind(self.BaGuaCellClickCallBack, self, 1))

	self.node_list["bagua_skill_tips"].text.text = Language.TianShen.BaGuaSkillUnlockTips
end

function TianShenView:BaGuaReleseCallBack()
	if self.wait_stop_delay then
		GlobalTimerQuest:CancelQuest(self.wait_stop_delay) 
		self.wait_stop_delay = nil
	end

	if self.shake_insert_one_anim then
		GlobalTimerQuest:CancelQuest(self.shake_insert_one_anim) 
		self.shake_insert_one_anim = nil
	end

    self:StopMoveTween()

	if self.bagua_list_view then
		for i = 1, 8 do
			self.bagua_list_view[i]:DeleteMe()
			self.bagua_list_view[i] = nil
		end
		self.bagua_list_view = nil
	end

	if self.suit_attr_render_list then
		for i, v in ipairs(self.suit_attr_render_list) do
			v:DeleteMe()
		end
		self.suit_attr_render_list = nil
	end

	if self.bagua_bag_list then
		self.bagua_bag_list:DeleteMe()
		self.bagua_bag_list = nil
	end	

	self.cur_select_bagua = 0
	self.color_select_bagua = 0
	self.old_show_bagua = nil
	TianShenBaGuaWGData.Instance:SetCurSelectBaGua(self.cur_select_bagua)
	self.bagua_list_is_load = false
	self.cur_show_bagua_panel = nil

	self:ClearOpenViewBaguaCellAnima()
end

function TianShenView:BaGuaShowIndexCallBack()
	self.cur_select_bagua = TianShenBaGuaWGData.Instance:GetOpenBaGuaSelect()
	if self.cur_select_bagua >= 6 then
		self.need_jump_to_bottom = true
	end
	self.color_select_bagua = self.cur_select_bagua

	TianShenBaGuaWGData.Instance:SetCurSelectBaGua(self.cur_select_bagua)
	TianShenBaGuaWGData.Instance:CheckBaGuaStronger()
	self:OpenViewBaguaCellAnima()
end

function TianShenView:ClearOpenViewBaguaCellAnima()
	if self.open_view_tween then
		for k,v in pairs(self.open_view_tween) do
			v:Kill()
			v = nil
		end
		self.open_view_tween = nil
	end
end

function TianShenView:OpenViewBaguaCellAnima()
	self:ClearOpenViewBaguaCellAnima()
	self.open_view_tween = {}

	for index = 8, 2, -1 do
		local bagua_obj = self.node_list["bagua_part" .. index]
		RectTransform.SetAnchoredPositionXY(bagua_obj.rect, temp_path[1].x, temp_path[1].y)

		if bagua_obj and bagua_obj.rect then
			local path = {}
			local time = 0.6
			for path_index = 1, index do
				table.insert(path, temp_path[path_index])
			end

			local tween = bagua_obj.transform:DOLocalPath(
				path,
				time,
				DG.Tweening.PathType.CatmullRom,
				DG.Tweening.PathMode.Ignore,
				10)

			self.open_view_tween[index] = tween
		end
	end
end

function TianShenView:MainPanelImageSwitch()
	local asset, bundle = ResPath.GetF2RawImagesPNG("a3_zs_ct_" .. self.cur_select_bagua)
	self.node_list.shenhun_img.raw_image:LoadSprite(asset, bundle, function()
		self.node_list.shenhun_img.raw_image:SetNativeSize()
	end)

	local bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
	local is_need_grey = false

	for i = 1, 8 do
		if bagua_equip_info[i].item_id == 0 then
			is_need_grey = true
			break
		end
	end

	XUI.SetGraphicGrey(self.node_list.shenhun_img, is_need_grey)

	local shenhun_slot_cfg = TianShenBaGuaWGData.Instance:GetTianShenBaguaSlot()
	self.node_list["last_btn"]:SetActive(self.cur_select_bagua ~= 0)
	self.node_list["next_btn"]:SetActive(self.cur_select_bagua ~= #shenhun_slot_cfg)
end

function TianShenView:ClickBaGuaSelectColor(color)
	self.color_select_bagua = color - 1
	self:ClickBaGuaSelectPanel(2)
	self:FlushBaGuaShowPanel(true)
end

function TianShenView:ClickBaGuaSX()
	local _, all_attr_list, all_is_pre = TianShenBaGuaWGData.Instance:GetBaGuaSlotZhanLi(self.cur_select_bagua)
	if IsEmptyTable(all_attr_list) then 
        return
    end

    local num = 0
    local attr_list_data = {}
    for k,v in pairs(all_attr_list) do
        num = num + 1
        attr_list_data[num] = {}
        attr_list_data[num].attr_str = k
        attr_list_data[num].attr_value = v
        --attr_list_data[num].is_pre = all_is_pre[k].is_pre or false
        attr_list_data[num].attr_type = tonumber(all_is_pre[k].attr_type) or 1000
        attr_list_data[num].sort_index = TianShenBaGuaWGData.Instance:GetBaGuaAttrSortIndex(attr_list_data[num].attr_type)
    end

    table.sort(attr_list_data, function (a,b)
        if a.sort_index < b.sort_index then
            return true
        end
        return false
    end)

	local tips_data = {
        title_text = Language.TianShen.TZAllAttrTitle,
        attr_data = attr_list_data
    }
	TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
	--TianShenWGCtrl.Instance:BaGuaOpenShuXing(self.cur_select_bagua)
end

function TianShenView:ClickBaGuaQuickEquip()
	local wear_type = 0
	local wear_list = {}
	local have_wear = false
	for i =1,8 do
		--if not compose_list[i] then
			wear_type,wear_list = TianShenBaGuaWGData.Instance:GetWearableEquip(self.cur_select_bagua,i-1)
			-- if wear_type == 1 and wear_list[1].star == 3 then
			-- 	if wear_list and wear_list[1] then
			-- 		TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_PUT,wear_list[1].index)
			-- 	end
			-- 	have_wear = true
			-- else
			local bagua_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
			local equip_info = bagua_info[i]
			local now_star = 0
			if equip_info.item_id > 0 then
				local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(equip_info.item_id)
				now_star = base_cfg.star
			end

				for q = 3,1 ,-1 do
					if wear_type == 1 and wear_list[1] and wear_list[1].star >= q then
						TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_PUT,wear_list[1].index)
						have_wear = true
						break
					else
						local base_cfg3 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_select_bagua,q,i-1)
						if base_cfg3.star > now_star then
							local can_compose, compose_way,compose_type = TianShenBaGuaWGData.Instance:CheckCanComposeItem(base_cfg3.itemid)
							if can_compose then
								local use_equip = 0
								for c1,c2 in pairs(compose_way) do
									if c2.use_equip then
										use_equip = 1
										break
									end
								end
								TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,base_cfg3.itemid,use_equip)
								have_wear = true
								break
							end
						end
					end
				end
			--end
		--end
	end
	if not have_wear then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.HereNoEquip)
		self:ClickBaGuaShop()
	end
end

function TianShenView:ClickBaGuaQuickCompose2()
	local compose_type = 0
	local compose_list = {}
	compose_type,compose_list = TianShenBaGuaWGData.Instance:GetComposeAbleTable(self.color_select_bagua)
	local had_list = {}
	if compose_type == 1 then
		for k,v in pairs(compose_list) do
			TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,k,v.equip_num)
			had_list[v.index] = true
		end
	end
	return compose_type == 1,had_list
end



function TianShenView:ClickBaGuaSkill()
	local skill_info = TianShenBaGuaWGData.Instance:GetTianShenSuitSkillInfo(self.cur_select_bagua)
	for t,q in pairs(skill_info) do
		if q.skill_type > 0 and q.skill_des and q.param_0 then
			local show_data = {
				icon = q.skill_icon,
				top_text = q.skill_name,
				body_text = string.format(q.skill_des,(tonumber(q.param_0)/100).."%"),
				limit_tip = "",
				x = 0,
				y = 0,
				capability = q.capability_inc,
			}
			NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
			break
		end
	end
end

function TianShenView:ClickBaGuaQuickCompose()
	local compose_type = 0
	local compose_list = {}
	compose_type,compose_list = TianShenBaGuaWGData.Instance:GetComposeAbleTable(self.color_select_bagua)
	if compose_type == 1 then
		for k,v in pairs(compose_list) do
			TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_UP_STAR,k,v.equip_num)
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.BaGuaComposeNotEnough)
	end
end


function TianShenView:ClickBaGuaSelectPanel(is_select)
	self.node_list["bagua_color_panel"]:SetActive(is_select == 1)
	self.node_list["select_close_img"]:SetActive(is_select ~= 1)
	self.node_list["select_open_img"]:SetActive(is_select == 1)
	if is_select == 1 then
		for i=1,9 do
			self.node_list["color_select_bg"..i]:SetActive(self.color_select_bagua == i-1)
		end
	end
end

function TianShenView:ClickBaGuaShop()
	TianShenWGCtrl.Instance:OpenBaGuaShopView()
end

function TianShenView:ClickBaGuaShowPanel(index)
	if self.cur_show_bagua_panel ~= index then
		self.cur_show_bagua_panel = index
		self:FlushBaGuaShowPanel(true)
	end
end

function TianShenView:FlushBaGuaJump(param_t)
	for k,v in pairs(param_t) do
		if k == "jump_bagua" then
			self.cur_select_bagua = v.index
			self.color_select_bagua = v.index
			if self.cur_select_bagua >= 6 then
				self.need_jump_to_bottom = true
			end
			TianShenBaGuaWGData.Instance:SetCurSelectBaGua(self.cur_select_bagua)
		end
	end
	self:BaGuaFlush()
end

function TianShenView:BaGuaFlush(value)
	self:BaGuaFlushLeft()
	self:BaGuaFlushRight(value)
	self:BaGuaFlushModel()
	self:MainPanelImageSwitch()
end
function TianShenView:CheckPlayeBaGuaEquipAnim(info)
	if info then
		if self.cur_select_bagua == info.part -1 then
			self:PlayeBaGuaEquipAnim(info.index)
		end 
	end
end

function TianShenView:PlayeBaGuaEquipAnim(i)
	if not self.bagua_move_tween then
		self.bagua_move_tween = {}
	end

	if self.bagua_move_tween[i] then
		self.bagua_move_tween[i]:Kill()
		self.bagua_move_tween[i] = nil
	end

	self.node_list["bagua_part"..i].transform.localScale = u3dpool.vec3(2, 2, 2)
	self.bagua_move_tween[i] = self.node_list["bagua_part"..i].transform:DOScale(Vector3(1, 1, 1), 0.3)

	self.bagua_move_tween[i]:OnComplete(function()
		self.bagua_move_tween[i]:Kill()
		self.bagua_move_tween[i] = nil
		self:PlayerBoomEffect(i)
	end)

	self.node_list["click_stop_mask"]:SetActive(true)
	if self.shake_insert_one_anim then
		GlobalTimerQuest:CancelQuest(self.shake_insert_one_anim) 
		self.shake_insert_one_anim = nil
	end
	self.shake_insert_one_anim = GlobalTimerQuest:AddDelayTimer(function ()
		self:StopShakeAndClickable(0.3)
		GlobalTimerQuest:CancelQuest(self.shake_insert_one_anim) 
		self.shake_insert_one_anim = nil

	end,0.3)
	
	self.bagua_move_tween[i]:SetEase(DG.Tweening.Ease.InQuart)
end

function TianShenView:StopShakeAndClickable(time)
	if self.wait_stop_delay then
		GlobalTimerQuest:CancelQuest(self.wait_stop_delay) 
		self.wait_stop_delay = nil
	end

	if time == 0 then
		self.node_list["click_stop_mask"]:SetActive(false)
	else
		self.wait_stop_delay = GlobalTimerQuest:AddDelayTimer(function ()
			self.node_list["click_stop_mask"]:SetActive(false)
			GlobalTimerQuest:CancelQuest(self.wait_stop_delay) 
			self.wait_stop_delay = nil
		end,time)
	end
end

function TianShenView:PlayAllBoom()
	for i=1,8 do
		self:PlayerBoomEffect(i)
	end
end

function TianShenView:PlayerBoomEffect(i)
	local bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
	if bagua_equip_info[i] and bagua_equip_info[i].item_id then
		local item_cfg = ItemWGData.Instance:GetItemConfig(bagua_equip_info[i].item_id)
		local eff_id = TIAN_SHEN_SHEN_SHI_MODEL_EFFECT[item_cfg.color]
		if eff_id then
			local eff_bundle, eff_asset = ResPath.GetEffectUi(eff_id)
	    	local eff_trf = self.node_list["bagua_part" .. i].transform
	    	EffectManager.Instance:PlayAtTransform(eff_bundle, eff_asset, eff_trf, 1, u3dpool.vec3(0, 0, 0))
		end
	end
end

function TianShenView:StopMoveTween()
	if self.bagua_move_tween then
		for k,v in pairs(self.bagua_move_tween) do
			if v then
				v:Kill()
				v = nil
			end
		end
		self.bagua_move_tween = nil
	end
end


function TianShenView:BaGuaFlushModel()
	local bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
	if nil == self.old_model_bagua or self.old_model_bagua ~= self.cur_select_bagua then
		self.old_model_bagua = self.cur_select_bagua
		self.old_bagua_info = {}
		
		for i=1,8 do
			self.old_bagua_info[i] = {}
			self.old_bagua_info[i].item_id = bagua_equip_info[i].item_id
		end

		self.node_list.bagua_skill_lock:SetActive(not TianShenBaGuaWGData.Instance:CheckWearMax(self.cur_select_bagua))
	else
		local old_num = 0
		local new_num = 0
		for i =1,8 do
			if bagua_equip_info[i].item_id > 0 then
				new_num = new_num + 1
			end
			if self.old_bagua_info[i] and self.old_bagua_info[i].item_id > 0 then
				old_num = old_num + 1
			end

			if self.old_bagua_info[i] and self.old_bagua_info[i].item_id ~= bagua_equip_info[i].item_id then
				self:PlayeBaGuaEquipAnim(i)
				self.old_bagua_info[i].item_id = bagua_equip_info[i].item_id
			end
		end
		self.old_model_bagua = self.cur_select_bagua

		if new_num > old_num and new_num == 8 then
			self:PlayerSuoLianBroken()
			self.need_jump_to_bottom = true
			self:BaGuaFlushLeft()
		else
			self.node_list.bagua_skill_lock:SetActive(not TianShenBaGuaWGData.Instance:CheckWearMax(self.cur_select_bagua))
		end
	end

	local base_cfg
	local can_wear
	local active_num = 0
	for i =1,8 do
		local have_equip = bagua_equip_info[i].item_id > 0
		if bagua_equip_info[i] and bagua_equip_info[i].item_id > 0 then
			base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(bagua_equip_info[i].item_id)
			for t=1,3 do
				self.node_list["star"..i..t]:SetActive(base_cfg.star >= t)--and base_cfg.star >=2)
			end

			active_num = active_num + 1
		else
			for t=1,3 do
				self.node_list["star"..i..t]:SetActive(false)
			end
		end

		can_wear = TianShenBaGuaWGData.Instance:GetWearableEquip(self.cur_select_bagua,i-1)
		if can_wear == 1 then
			self:ShowBaGuaModelRed(i, can_wear, have_equip)
		else
			if base_cfg and base_cfg.star then
				if base_cfg.star >= 3 then
					can_wear = 0
				else
					local base_cfg3 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_select_bagua,base_cfg.star+1,i-1)
					can_wear = TianShenBaGuaWGData.Instance:CheckCanComposeItem(base_cfg3.itemid)
					can_wear = can_wear and 1 or 0
				end
			else
				local base_cfg2 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_select_bagua,1,i-1)
				can_wear = TianShenBaGuaWGData.Instance:CheckCanComposeItem(base_cfg2.itemid)
				can_wear = can_wear and 1 or 0
			end
			self:ShowBaGuaModelRed(i, can_wear, have_equip)
		end
	end

	local m_bundle, m_asset = "", ""
	local model_id = TianShenBaGuaWGData.Instance:GetBaGuaModelID(self.cur_select_bagua)
	for i = 1, 8 do
		-- local load_call_back = function ()
		-- 	self:BaGuaModelLoadComplete(i)
		-- end
		local bundle, asset
		local item_cfg
		local have_equip = bagua_equip_info[i].item_id > 0
		if have_equip then
			item_cfg = ItemWGData.Instance:GetItemConfig(bagua_equip_info[i].item_id)
		else
			local part_base_info = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfoByindex(self.cur_select_bagua, 1, i - 1)
			item_cfg = ItemWGData.Instance:GetItemConfig(part_base_info.itemid)
		end
		if item_cfg then
			bundle, asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list["bagua_mode" .. i].image:LoadSprite(bundle, asset)
		end

		self.node_list["jd_" .. i]:SetActive(active_num >= i)
	end
end

function TianShenView:ShowBaGuaModelRed(index, can_wear, have_equip)
	self.node_list["bague_model_red"..index]:SetActive(can_wear == 1)
	self.node_list["bagua_add" .. index]:SetActive(not have_equip and can_wear == 1)
	self.node_list["bagua_mask" .. index]:SetActive(not have_equip and can_wear ~= 1)
	self.node_list["bagua_mode" .. index]:SetActive((not have_equip and can_wear ~= 1) or have_equip)
end

-- function TianShenView:BaGuaModelLoadComplete(index)
-- 	local bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
-- 	XUI.SetGraphicGrey(self.node_list["bagua_mode" .. index], not (bagua_equip_info[index].item_id > 0))
-- 	-- self.node_list["bagua_mode" .. index].image:SetNativeSize()
-- end

function TianShenView:PlayerSuoLianBroken()
	if self.shake_insert_one_anim then
		GlobalTimerQuest:CancelQuest(self.shake_insert_one_anim) 
		self.shake_insert_one_anim = nil
	end

	GlobalTimerQuest:AddDelayTimer(function ()
		self:DelaySuoLianBoom()
	end,0.5)
end

function TianShenView:DelaySuoLianBoom()
	self.node_list["click_stop_mask"]:SetActive(false)
end

function TianShenView:PlaySkillActiveAnim()
	local skill_cfg = {}
	local skill_info = TianShenBaGuaWGData.Instance:GetTianShenSuitSkillInfo(self.cur_select_bagua)
	if not IsEmptyTable(skill_info) then
		for t,q in pairs(skill_info) do
			if q.skill_type > 0 and q.skill_des and q.param_0 then
				skill_cfg.desc = string.format(q.skill_des,(tonumber(q.param_0)/100).."%")
				skill_cfg.icon = q.skill_icon
				skill_cfg.name = q.skill_name
				skill_cfg.need_move = true
				skill_cfg.target = self.node_list.bagua_img_skill_icon
				skill_cfg.cur_select_bagua = self.cur_select_bagua
		        local data = {name = skill_cfg.name, desc = skill_cfg.desc, res_fun = ResPath.GetSkillIconById, icon = skill_cfg.icon}
		        TipWGCtrl.Instance:ShowGetNewSkillView2(data)
				--TipWGCtrl.Instance:ShowBaGuaUnLockView(skill_cfg)
				GlobalTimerQuest:AddDelayTimer(function ()
					if self.node_list and self.node_list.bagua_skill_lock then
						self.node_list.bagua_skill_lock:SetActive(not TianShenBaGuaWGData.Instance:CheckWearMax(self.cur_select_bagua))
					end
				end,2)
				return
			end
		end
	end
	self.node_list.bagua_skill_lock:SetActive(not TianShenBaGuaWGData.Instance:CheckWearMax(self.cur_select_bagua))
end

function TianShenView:BaGuaFlushLeft()
	if self.bagua_list_view then
		local list_data = TianShenBaGuaWGData.Instance:GetTianShenBaguaOpenSlot()
		local info_list = {}
		for k,v in pairs(list_data) do
			info_list[k] = {}
			info_list[k].info = v
		end

		for i = 1, 8 do
			if not IsEmptyTable(info_list[i]) then
				self.bagua_list_view[i]:SetData(info_list[i])
			else
				self.bagua_list_view[i]:Flush()
			end
		end
	end
end

function TianShenView:BaGuaFlushRight(value)
	self:FlushBaGuaShowPanel(value)
end

function TianShenView:FLushBaGuaShopRed()
	if self.node_list["bagua_shop_red"] then
		self.node_list["bagua_shop_red"]:SetActive(TianShenBaGuaWGData.Instance:GetBaGuaShopRed() == 1)
	end
end

function TianShenView:FlushBaGuaShowPanel(flush_type)
	if not self:IsLoadedIndex(TianShenView.TabIndex.BaGua) then
		return
	end

	self.node_list["bagua_attr_panel"]:SetActive(1 == self.cur_show_bagua_panel)
	self.node_list["bagua_bag_panel"]:SetActive(2 == self.cur_show_bagua_panel)

	self.node_list["bagua_shop_red"]:SetActive(TianShenBaGuaWGData.Instance:GetBaGuaShopRed() == 1)

	if TianShenBaGuaWGData.Instance:InfoHaveChange() or flush_type then
		TianShenBaGuaWGData.Instance:SetInfoChange(false)
		local red_flag = 0
		local wear_list = {}
		for i =1,8 do
   			red_flag ,wear_list= TianShenBaGuaWGData.Instance:GetWearableEquip(self.cur_select_bagua,i-1)
   			local bagua_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
			local equip_info = bagua_info[i]
			local now_star = 0
			if equip_info.item_id > 0 then
				local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(equip_info.item_id)
				now_star = base_cfg.star
			end
			for q = 3,1 ,-1 do
				if red_flag == 1 and wear_list[1] and wear_list[1].star >= q then
					red_flag = 1
					break
				else
					local base_cfg3 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_select_bagua,q,i-1)
					if base_cfg3.star > now_star then
						local can_compose = TianShenBaGuaWGData.Instance:CheckCanComposeItem(base_cfg3.itemid)
						if can_compose then
							red_flag = 1
							break
						end
					end
				end
			end
   			if red_flag == 1 then
   				break
   			end
   		end

		self.node_list["bagua_equip_red"]:SetActive(red_flag ==1)
	end
	self.node_list["bagua_bag_red"]:SetActive(false)
	local all_red_info = TianShenBaGuaWGData.Instance:GetAllBaGuaRed()

	self.node_list["color_select_red"]:SetActive(false)

	for i=1,9 do
		self.node_list["color_select_red"..i]:SetActive(false)
	end
	if not IsEmptyTable(all_red_info) then
		for k,v in pairs(all_red_info) do
			if v.equip_red == 1 then
				self.node_list["color_select_red"..v.bagua_index+1]:SetActive(true)
				self.node_list["color_select_red9"]:SetActive(true)
				self.node_list["color_select_red"]:SetActive(true)
				self.node_list["bagua_bag_red"]:SetActive(true)
			end
		end
	end

	local zhanli = TianShenBaGuaWGData.Instance:GetBaGuaSlotZhanLi(self.cur_select_bagua)
	self.node_list["Ts_bagua_zhanli"].text.text = zhanli or 0
	local skill_info = TianShenBaGuaWGData.Instance:GetTianShenSuitSkillInfo(self.cur_select_bagua)
	local name_info = TianShenBaGuaWGData.Instance:GetBaGuaNameInfo(self.cur_select_bagua)
	self.node_list.bagua_name.image:LoadSpriteAsync(ResPath.GetCommon("a3_quality_text_" .. name_info.index + 1))
	self.node_list.tz_name.text.text = name_info.name
	if not IsEmptyTable(skill_info) then
		for t,q in pairs(skill_info) do
			if q.skill_type > 0 and q.skill_des and q.param_0 then
				local desc_str = TianShenBaGuaWGData.Instance:CheckWearMax(self.cur_select_bagua) and Language.TianShen.SkillBaGuaText2 or Language.TianShen.SkillBaGuaText1
				if not TianShenBaGuaWGData.Instance:CheckWearMax(self.cur_select_bagua) then
					desc_str = desc_str .. Language.TianShen.SkillOpenDesc
				end

				--self.node_list.skll_title.text.text = desc_str
				self.node_list.bagua_skll_desc.text.text = string.format(q.skill_des, (tonumber(q.param_0)/100).."%")

				local bundle,asset = ResPath.GetSkillIconById(q.skill_icon)
				self.node_list["bagua_img_skill_icon"].image:LoadSpriteAsync(bundle, asset,function()
					self.node_list["bagua_img_skill_icon"].image:SetNativeSize()
				end)
				--self.node_list.bagua_skll_name.text.text = q.skill_name
				break
			else
				self.node_list.bagua_skll_desc.text.text = ""
				--self.node_list.bagua_skll_name.text.text = ""
				--self.node_list.skll_title.text.text = ""
			end
		end
	else
		self.node_list.bagua_skll_desc.text.text = ""
	end
	if 1 == self.cur_show_bagua_panel then
		local info_count = #skill_info
		for i = 1, info_count do
			self.suit_attr_render_list[i]:SetData(skill_info[i])
		end
		for i = 1, 5 do
			self.suit_attr_render_list[i]:SetActive(i <= info_count)
		end
		--[[
		local skill_num = 0
		local attr_num = 0
		local str = ""
		local hight_num = 0
		for k,v in pairs(skill_info) do
			skill_num = skill_num + 1
			attr_num = 0
			local bagua_name = name_info.name
			if v.is_active then
				local count_str = v.count.."/"..v.count
				if v.count_star > 1 then
					self.node_list["taozhuang_effect"..skill_num].text.text = string.format(Language.TianShen.BaGuaSuitAcitve2, v.count_star, bagua_name, COLOR3B.DEFAULT, count_str)
				else
					self.node_list["taozhuang_effect"..skill_num].text.text = string.format(Language.TianShen.BaGuaSuitAcitve, bagua_name, COLOR3B.DEFAULT, count_str)
				end
			else
				local count_str = v.active_count.."/"..v.count
				if v.count_star > 1 then
					local count_str2 = ""
					if v.count_star == 2 then
						count_str2 = v.two_star_count.."/"..v.count
					elseif v.count_star == 3 then
						count_str2 = v.three_star_count.."/"..v.count
					end
					self.node_list["taozhuang_effect"..skill_num].text.text = string.format(Language.TianShen.BaGuaSuitAcitve2, v.count_star, bagua_name, COLOR3B.RED, count_str2)
				else
					self.node_list["taozhuang_effect"..skill_num].text.text = string.format(Language.TianShen.BaGuaSuitAcitve, bagua_name, COLOR3B.RED, count_str)
				end
			end
			str = ""
			if v.attr_type_list and tonumber(v.attr_value_list[1]) ~= 0 then
				for t,q in pairs(v.attr_type_list) do
					attr_num = attr_num + 1
					if v.is_pre[t] then
						str = Language.Common.TipsAttrNameList[q] .. ToColorStr(" +" .. (tonumber(v.attr_value_list[t])/100).."%", COLOR3B.DEFAULT_NUM)
					else
						str = Language.Common.TipsAttrNameList[q] .. ToColorStr(" +" .. tonumber(v.attr_value_list[t]), COLOR3B.DEFAULT_NUM)
					end

					if self.node_list["taozhuang_attr"..skill_num..attr_num] then
						self.node_list["taozhuang_attr"..skill_num..attr_num].text.text = str
					end
				end
			end
			for i =1,4 do
				self.node_list["taozhuang_attr"..skill_num..i]:SetActive(i <= attr_num)
			end
			hight_num = hight_num + (attr_num+1)*32
		end
		
		self.node_list.taozhuang_scroll.scroll_rect.verticalNormalizedPosition = 1

		if hight_num ~= 0 then
			self:ReSetContantPosition()
		end
		]]
	elseif 2 == self.cur_show_bagua_panel then
		self.node_list["bagua_color_select_text"].text.text = ToColorStr(Language.TianShen.BaGuaColorSelect[self.color_select_bagua],ITEM_COLOR[self.color_select_bagua+1])
		self.bagua_bag_list:SetDataList(TianShenBaGuaWGData.Instance:GetTianShenBaGuaBagList(self.color_select_bagua))
	end
end

function TianShenView:ReSetContantPosition(re_try)
	if nil == self.old_show_bagua or self.old_show_bagua ~= self.cur_select_bagua then
		local time = 0
		if nil == self.old_show_bagua or re_try then
			time = 0.1
		end
		self.old_show_bagua = self.cur_select_bagua
		GlobalTimerQuest:AddDelayTimer(function ()
			local hight1 = self.node_list["attr_list_contant"].rect.sizeDelta.y
			if hight1 == 0 and not re_try then
				self:ReSetContantPosition(true)
				return
			elseif hight1 == 0 then
				return
			end
		end,time)
	end
end

function TianShenView:BaGuaCellClickCallBack(is_next_index)
	local switch_index = 0
	if is_next_index > 0 then
		local shenhun_slot_cfg = TianShenBaGuaWGData.Instance:GetTianShenBaguaSlot()
		switch_index = math.min(self.cur_select_bagua + 1, #shenhun_slot_cfg)
	else
		switch_index = math.max(self.cur_select_bagua - 1, 0)
	end

	if TianShenBaGuaWGData.Instance:CheckBaGuaIsOpenNew(switch_index) then
		self.cur_select_bagua = switch_index
		self.color_select_bagua = switch_index
		TianShenBaGuaWGData.Instance:SetCurSelectBaGua(self.cur_select_bagua)
		self:BaGuaFlush(true)
		self:OpenViewBaguaCellAnima()
	else
		local name_cfg = TianShenBaGuaWGData.Instance:GetBaGuaNameInfo(switch_index)
		local role_level = GameVoManager.Instance:GetMainRoleVo().level
		if name_cfg and name_cfg.open_level and role_level >= name_cfg.open_level then
			TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.BaGuaPaiJiHuo)
		else
			TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.TianShen.BaGuaOpenNeed2, name_cfg.open_level))
		end
	end

end

function TianShenView:ClickBaGuaEquip(index)
	local bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(self.cur_select_bagua)
	if bagua_equip_info[index] and bagua_equip_info[index].item_id > 0 then
		local click_func = TianShenWGCtrl.Instance:GetBaGuaClickFunc(bagua_equip_info[index],ItemTip.FROM_TIANSHEN_BAGUA_EQUIP)
		TianShenBaGuaWGData.Instance:GetBaGuaImageByBaGua(self.cur_select_bagua)
		TipWGCtrl.Instance:OpenItem(bagua_equip_info[index],ItemTip.FROM_TIANSHEN_BAGUA_EQUIP,nil,nil,click_func)
	else
		local base_cfg2 = {}
		for i =1,3 do
			base_cfg2 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_select_bagua,i,index-1)
			if TianShenBaGuaWGData.Instance:ChackHaveBagua(base_cfg2.itemid) then
				TianShenWGCtrl.Instance:OpenBaGuaEquipTip(bagua_equip_info[index])
				return
			end
		end

		base_cfg2 = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(self.cur_select_bagua,1,index-1)
		local can_compsoe,compose_way,compose_type = TianShenBaGuaWGData.Instance:CheckCanComposeItem(base_cfg2.itemid)
		local use_equip = 0
		for c1,c2 in pairs(compose_way) do
			if c2.use_equip then
				use_equip = 1
				break
			end
		end
		
		if can_compsoe then
			local compose_data = {}
			compose_data.cost_item = base_cfg2.stuff_id
			compose_data.target_item_id = base_cfg2.itemid
			compose_data.compose_type = compose_type and 1 or 0
			compose_data.compose_way = compose_way
			compose_data.count_2 = use_equip
			TianShenWGCtrl.Instance:BaGuaOpenComPose(compose_data)
			return
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg2.itemid})
			--TianShenWGCtrl.Instance:OpenBaGuaEquipTip(bagua_equip_info[index])
		end
	end
end


function TianShenView:BaGuaItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)

end

-------------------------------------
-- BaGuaAttrItemRender 套装属性reder
-------------------------------------
BaGuaAttrItemRender = BaGuaAttrItemRender or BaseClass(BaseRender)

function BaGuaAttrItemRender:OnFlush()
	if not self.data then
		return
	end
	local suit_data = self.data
	self.node_list["taozhuang_active_tag"]:SetActive(suit_data.is_active)
	self.node_list["taozhuang_notactive_tag"]:SetActive(not suit_data.is_active)
	local bagua_name = suit_data.name
	if suit_data.is_active then
		local count_str = suit_data.count.."/"..suit_data.count
		self.node_list["taozhuang_effect"].text.text = string.format(Language.TianShen.BaGuaSuitAcitve3, COLOR3B.DEFAULT, count_str, suit_data.count_star)
	else
		local count_str = suit_data.active_count.."/"..suit_data.count
		if suit_data.count_star == 2 then
			count_str = suit_data.two_star_count.."/"..suit_data.count
		elseif suit_data.count_star == 3 then
			count_str = suit_data.three_star_count.."/"..suit_data.count
		end
		self.node_list["taozhuang_effect"].text.text = string.format(Language.TianShen.BaGuaSuitAcitve3, COLOR3B.RED, count_str, suit_data.count_star)
	end

	local str = ""
	local attr_num = 0
	if suit_data.attr_type_list and tonumber(suit_data.attr_value_list[1]) ~= 0 then
		for t, q in pairs(suit_data.attr_type_list) do
			attr_num = attr_num + 1
			self.node_list["taozhuang_attr_name_" .. attr_num].text.text = Language.Common.AttrNameList2[q]
			if suit_data.is_pre[t] then
				str = " +" .. tonumber(suit_data.attr_value_list[t])/100 .. "%"
			else
				str = " +" .. tonumber(suit_data.attr_value_list[t])
			end
			self.node_list["taozhuang_attr_value_" .. attr_num].text.text = str
		end
	end
	for i =1,4 do
		self.node_list["attr" .. i]:SetActive(i <= attr_num)
	end
end


----------------
BaGuaListCell = BaGuaListCell or BaseClass(BaseRender)

function BaGuaListCell:LoadCallBack()
end

function BaGuaListCell:__delete()
end

function BaGuaListCell:OnFlush()
	if not IsEmptyTable(self.data) and self.data.info then
		local index = TianShenBaGuaWGData.Instance:GetCurSelectBaGua()
		self.node_list.HL:SetActive(self.data.info.index == index)
	else
		self.node_list.HL:SetActive(false)
	end
end

----------------
BaGuaBagCell = BaGuaBagCell or BaseClass(BaseRender)

function BaGuaBagCell:__init()
	self.bag_cell_list = {}
	for i =1 ,5 do
		self.bag_cell_list[i] = ItemCell.New(self.node_list["cell_pos"..i])
		self.node_list["click_btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickBagCell,self,i))
	end
end

function BaGuaBagCell:__delete()
	if self.bag_cell_list then
		for k,v in pairs(self.bag_cell_list) do
			v:DeleteMe()
		end
		self.bag_cell_list = nil
	end
end

function BaGuaBagCell:OnFlush()
	local base_cfg = {}
	local red_info = {}
	for i = 1,5 do
		if self.data and not IsEmptyTable(self.data[i]) then
			base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.data[i].item_id)
			red_info = TianShenBaGuaWGData.Instance:GetBaGuaItemRedInfo(self.data[i].item_id)
			self.node_list["cell_pos"..i]:SetActive(true)
			self.bag_cell_list[i]:SetData(self.data[i])
			if base_cfg then
				self.bag_cell_list[i]:SetLeftTopImg(base_cfg.star or 0)
			else
				self.bag_cell_list[i]:SetLeftTopImg(0)
			end
			self.bag_cell_list[i]:SetLeftTopTextVisible(false)
			self.node_list["red"..i]:SetActive(red_info.equip_red == 1)
		else
			self.node_list["red"..i]:SetActive(false)
			self.node_list["cell_pos"..i]:SetActive(false)
		end
	end
end

function BaGuaBagCell:OnClickBagCell(index)
	if not IsEmptyTable(self.data[index]) then
		local click_func = TianShenWGCtrl.Instance:GetBaGuaClickFunc(self.data[index],ItemTip.FROM_TIANSHEN_BAGUA_BAG)
		TipWGCtrl.Instance:OpenItem(self.data[index],ItemTip.FROM_TIANSHEN_BAGUA_BAG,nil,nil,click_func)
	end
end

---------------
BaGuaAttrListCell = BaGuaAttrListCell or BaseClass(BaseRender)

function BaGuaAttrListCell:__init()

end

function BaGuaAttrListCell:__delete()

end

function BaGuaAttrListCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local str = ""
		for i =1,2 do
			if self.data[i] then
				str = ""
				if self.data[i].is_pre then
					str = str.. Language.Common.TipsAttrNameList2[self.data[i].name]
					self.node_list["attr_name"..i].text.text = str--..ToColorStr(tonumber(self.data[i].value)/100 .."%","#ffffffff")
					self.node_list["attr_text"..i].text.text = tonumber(self.data[i].value) / 100 .. "%"
				else
					str = str..Language.Common.TipsAttrNameList2[self.data[i].name]
					self.node_list["attr_name"..i].text.text = str--..ToColorStr(self.data[i].value, "#ffffffff")
					self.node_list["attr_text"..i].text.text = self.data[i].value
				end
			else
				self.node_list["attr_name"..i].text.text = ""
				self.node_list["attr_text"..i].text.text = ""
			end

			self.node_list["attr_"..i]:SetActive(not IsEmptyTable(self.data[i]))
		end
	end
end