SocietyView.MAIL_FUJIAN_NUM = 10								-- 最大附件数量
SocietyView.MAIL_CONTENT_LEN = 512							-- 邮件内容最大长度
SocietyView.MAIL_SEND_ITEM = 3								-- 最大发送物品数据

MAIL_VIRTUAL_ITEM_BATTLEFIELDHONOR = 0							-- 战场荣誉
MAIL_VIRTUAL_ITEM_YUANLI = 1									-- 元力
MAIL_VIRTUAL_ITEM_XINHUN = 2									-- 星魂
MAIL_VIRTUAL_ITEM_GUILDGONGXIAN = 3								-- 仙盟贡献
MAIL_VIRTUAL_ITEM_SHENGWANG = 4									-- 声望
MAIL_VIRTUAL_ITEM_BIND_GOLD = 5									-- 绑定元宝
MAIL_VIRTUAL_ITEM_GOLD = 6										-- 元宝
MAIL_VIRTUAL_ITEM_BIND_COIN = 7									-- 绑定铜币
MAIL_VIRTUAL_ITEM_CROSS_HONOR = 8								-- 跨服荣誉
MAIL_VIRTUAL_ITEM_COIN = 9										-- 铜币
MAIL_VIRTUAL_ITEM_GONGXUN = 10									-- 功勋
MAIL_VIRTUAL_ITEM_JINGHUA = 11									-- 精华
MAIL_VIRTUAL_ITEM_FISHING_SCORE = 12							-- 跨服钓鱼积分
MAIL_VIRTUAL_ITEM_ROLE_EXP = 13									-- 经验
MAIL_VIRTUAL_ITEM_SILVER_TICKET = 14							-- 银票
MAIL_VIRTUAL_ITEM_SHENGWANG_ASSIST = 15							-- 协助声望
MAIL_VIRTUAL_ITEM_SHENGWANG_NOLIMIT = 16						-- 无限制声望
MAIL_VIRTUAL_ITEM_CANGJINSHANGPU_SCORE = 18						-- 藏金商铺积分		

SocietyView.MailVirtualItem = {
	[MAIL_VIRTUAL_ITEM_BATTLEFIELDHONOR] = 90071,
	-- [MAIL_VIRTUAL_ITEM_YUANLI] =
	-- [MAIL_VIRTUAL_ITEM_XINHUN] =
	[MAIL_VIRTUAL_ITEM_GUILDGONGXIAN] =	90074,
	[MAIL_VIRTUAL_ITEM_SHENGWANG] =	90073,
	[MAIL_VIRTUAL_ITEM_BIND_GOLD] =	90348,
	[MAIL_VIRTUAL_ITEM_GOLD] = 65534,
	[MAIL_VIRTUAL_ITEM_BIND_COIN] = 65535,
	-- [MAIL_VIRTUAL_ITEM_CROSS_HONOR] =
	[MAIL_VIRTUAL_ITEM_COIN] = 65536,
	[MAIL_VIRTUAL_ITEM_GONGXUN] = 90088,
	[MAIL_VIRTUAL_ITEM_JINGHUA] = 90070,
	-- [MAIL_VIRTUAL_ITEM_FISHING_SCORE] =,
	[MAIL_VIRTUAL_ITEM_ROLE_EXP] = 90050,
	[MAIL_VIRTUAL_ITEM_SHENGWANG_ASSIST] = 90073,
	[MAIL_VIRTUAL_ITEM_SHENGWANG_NOLIMIT] = 90073,
	[MAIL_VIRTUAL_ITEM_CANGJINSHANGPU_SCORE] = 65551,
}	-- 附件虚拟物品对应的ID

local send_info_pos = {
	[1] = {x = -120, y = 258},
	[2] = {x = -120, y = 318},
	[3] = {x = -120, y = 121},
}

local show_gold_group_pos = {
	["title"] = {x = -2 , y = 278},
	["reward_list"] = {x = 0, y = 194}  
}

local hide_gold_group_pos = {
	["title"] = {x = -2 , y = 218},
	["reward_list"] = {x = 0, y = 132}
}

function SocietyView:InitMailView()
	SocietyWGData.Instance:SetMailMark(false)
	self.layout_mail_base = self.node_list["layout_mail_base"]
	self.list_mail_list = self.node_list["list_mainlist"]

	self.mail_get = self.node_list["layout_mail_get"]
	self.mail_info = self.node_list["layout_mail_info"]

	self.select_mail_index = -1
	self.send_name = ""

	self.is_get_mail_list = true
	self.select_mail_offset = nil
	self:CreateEmailList()
	self:ClearReceiveMail()
	self:RegisterMailEvents()
	self.select_mail_data_temp = nil
end

function SocietyView:DeleteMailView()
	self.select_mail_data_temp = nil
	for k, v in pairs(self.m_send_item_list) do
		v:DeleteMe()
	end

	if nil ~= self.mail_grid then
		self.mail_grid:DeleteMe()
		self.mail_grid = nil
	end

	if nil ~= self.gold_num_keyboard then
		self.gold_num_keyboard:DeleteMe()
		self.gold_num_keyboard = nil
	end

	if nil ~= self.item_num_keyboard then
		self.item_num_keyboard:DeleteMe()
		self.item_num_keyboard = nil
	end

	if nil ~= self.mail_list then
		self.mail_list:DeleteMe()
		self.mail_list = nil
	end

	if nil ~= self.mail_reward_list then
		self.mail_reward_list:DeleteMe()
		self.mail_reward_list = nil
	end

	self.list_mail_list = nil

	self.mail_get = nil
	self.mail_info = nil
	self.writemail_send = nil

	self.write_mail_view = nil

	self.select_mail_index = nil
	self.send_name = nil

	self.is_get_mail_list = nil
	self.select_mail_offset = nil
	self.input_sendName = nil

	self.layout_mail_base = nil
end


--请求邮件列表
function SocietyView:LoadMailList()
	if true == self.is_get_mail_list then
		SocietyWGData.Instance:SetHasNoReadMail(false, 0)

		local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailGetList)
		send_protocol:EncodeAndSend()
		self.is_get_mail_list = false
	end
end

function SocietyView:RegisterMailEvents()

	XUI.AddClickEventListener(self.node_list["btn_getall"], BindTool.Bind(self.OnGetAllFujian, self,false)) --获取附件全部
	XUI.AddClickEventListener(self.node_list["btn_delread"], BindTool.Bind(self.OnDelRead, self,false))     --删除已读 多个
	XUI.AddClickEventListener(self.node_list["btn_getfujian"], BindTool.Bind(self.OnGetAllFujian, self,true)) --单个获取附件
	-- XUI.AddClickEventListener(self.node_list["btn_getfujian"], BindTool.Bind1(self.OnGetFujian, self)) --单个获取附件 原来的
	XUI.AddClickEventListener(self.node_list["btn_del"], BindTool.Bind(self.OnDelRead, self,true))   --删除已读 单个
	--XUI.AddClickEventListener(self.node_list["layout_del"], BindTool.Bind1(self.OnDeleteMail, self))   --删除已读 单个  原来的
	--XUI.AddClickEventListener(self.node_list["CheckMark_All"], BindTool.Bind1(self.SelectAllDeleteMail, self))
end
function SocietyView:SelectAllDeleteMail()
	local vas = self.node_list.chckmark_all_icon:GetActive()
	self.node_list.chckmark_all_icon:SetActive(not vas)
	--local data = SocietyWGData.Instance:GetMailList()
	if vas then
		SocietyWGData.Instance:RemoveAllSelectMail()
	else
		local tab = __TableCopy(self.mail_list_new)
		SocietyWGData.Instance:AddSelectMailList(tab)
	end
	--local data = SocietyWGData.Instance:GetMailList()
	--self.mail_list:SetData(data,3)
	self.node_list.list_mainlist.scroller:RefreshAndReloadActiveCellViews(true)
	 -- print_error(2,self.mail_list_new)
	--self.node_list.list_mainlist.scroller:RefreshActiveCellViews()
end

function SocietyView:OnBackMainView()
	 --print_error("OnBackMainView~~~~~~~~~~~~~~~~~~~~~~")
	self.mail_get.gameObject:SetActive(true)
	self.mail_info.gameObject:SetActive(true)
	SocietyWGData.Instance:SetIsWriteMailBool(false)

end

function SocietyView:OnToSendView()
	-- print_error("OnToSendView~~~~~~~~~~~~~~~~~~~~~~")
	self.mail_get.gameObject:SetActive(false)
	self.mail_info.gameObject:SetActive(false)
	--print_error("1111111111")
	SocietyWGData.Instance:SetIsWriteMailBool(true)

end

--获取所有附件
function SocietyView:OnGetAllFujian(is_danren)
	 --print_error("OnGetAllFujian---获取附件--------------------")
	local mail_list = SocietyWGData.Instance:GetMailList()
	local temp_has_fu_jian = {}
	if is_danren then
		if -1 == self.select_mail_index then
			return
		end
		local mail = SocietyWGData.Instance:FindMail(self.select_mail_index)
		if nil == mail or 0 == mail.has_attachment then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoFuJian)
			return
		end
		table.insert(temp_has_fu_jian,mail.mail_index)
	else
		table.sort(mail_list, function(a, b)
    		    	return a.mail_status.recv_time > b.mail_status.recv_time
    	    	end)
		for k,v in pairs(mail_list) do
			if 1 == v.has_attachment then
				table.insert(temp_has_fu_jian,v.mail_index)
			end
		end
	end

	if #temp_has_fu_jian <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoFuJian)
		return
	end

	SocietyWGCtrl.Instance:CSMailOneKeyFetchAttachmentArr(temp_has_fu_jian)
	SocietyWGCtrl.Instance:OnRecvMainRoleInfo()
	AudioService.Instance:PlayRewardAudio()
end
function SocietyView:ClearFuJianWuPin()

	if nil == self.node_list or nil == self.node_list.reward_title then
		return
	end

	if self.mail_reward_list then
		self.mail_reward_list:SetDataList({})
	end

	self.node_list.reward_title:SetActive(false)
	self.node_list.Gold.tmp.text = 0
	self.node_list.Gold_blind.tmp.text = 0
	self.node_list.Coin.tmp.text = 0
	self.node_list.Silver.tmp.text = 0
	self.node_list.gold_group:SetActive(false)
end

function SocietyView:ClearReceiveMail()
	if nil == self.node_list["rich_mailsendnametitle"] then return end
	self.node_list["rich_mailcontent"].tmp.text = ""
	self.node_list["rich_mailsendnametitle"].tmp.text = ""
	self.node_list["label_receivetime"].tmp.text = ""
	self.node_list.rich_mailsendname.tmp.text = ""
	self.node_list.Gold.tmp.text = 0
	self.node_list.Gold_blind.tmp.text = 0
	self.node_list.Coin.tmp.text = 0
	self.node_list.Silver.tmp.text = 0
	self.node_list.gold_group:SetActive(false)
	self.node_list.reward_title:SetActive(false)

	if self.mail_reward_list then
		self.mail_reward_list:SetDataList({})
	end
end

--删除已读
function SocietyView:OnDelRead(is_danren)
	local all_read_mail = {}
	if is_danren then
		if -1 == self.select_mail_index then
			return
		end
		table.insert(all_read_mail,self.select_mail_index)
	else
		all_read_mail = SocietyWGData.Instance:GetAllReadMail()
	end
	if #all_read_mail <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoMail)
		return
	end

	if is_danren then
		TipWGCtrl.Instance:OpenAlertTips(Language.Society.DeleteTips2, function()
			SocietyWGCtrl.Instance:CSMailOneKeyDelete(all_read_mail)
		end)
	else
		TipWGCtrl.Instance:OpenAlertTips(Language.Society.DeleteTips, function()
			SocietyWGCtrl.Instance:CSMailOneKeyDelete(all_read_mail)
		end)
	end

end

--获取附件
function SocietyView:OnGetFujian()
	if -1 == self.select_mail_index then
		return
	end
	local mail = SocietyWGData.Instance:FindMail(self.select_mail_index)
	if nil == mail or 0 == mail.has_attachment then
		Log("msg:没有附件")
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoFuJian)
		return
	end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailFetchAttachment)
	send_protocol.mail_index = self.select_mail_index
	send_protocol.item_index = -1
	send_protocol.is_last = 1
	send_protocol:EncodeAndSend()
end

-- function SocietyView:OnSendMail()
-- 	local mailText = self.input_content.text

-- 	--判断是否选取发送人
-- 	if nil == gameName == nil or "" == gameName then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NotChooseUser)
-- 		return
-- 	end

-- 	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
-- 	if gameName == main_role_vo.name then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NotSendSelf)
-- 		return
-- 	end

-- 	-- 是否填写邮件内容
-- 	if nil == mailText or "" == mailText then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.MailNotEmpty)
-- 		return
-- 	end
-- 	if string.len(mailText) >= SocietyView.MAIL_CONTENT_LEN then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.MailToLong)
-- 		return
-- 	end


-- 	-- 等级限制
--   	if COMMON_CONSTS.SEND_MAIL_LEVEL > main_role_vo.level then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.LevelNotEnough, COMMON_CONSTS.SEND_MAIL_LEVEL))
--  		return
--   	end

-- 	--发送邮件
-- 	SocietyWGCtrl.Instance:GetUserInfoByName(3750, gameName, function (flag, user_info)
-- 		if 0 == flag or nil == user_info then
-- 			--没有填写人物提示
-- 			if nil ~= Language.Society["UserNotExist"] then
-- 				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society["UserNotExist"])
-- 			end
-- 			return
-- 		end

-- 		local item_knapindex_list = {}
-- 		local item_comsume_num = {}
-- 		local count = 0
-- 		for k,v in pairs(self.m_send_item_list) do
-- 			if v:GetData() then
-- 				table.insert(item_knapindex_list, v:GetData().from_index)
-- 				table.insert(item_comsume_num, v:GetData().num)
-- 				count = count + 1
-- 			end
-- 		end
-- 		for i = 1, SocietyView.MAIL_SEND_ITEM do
-- 			if nil == item_knapindex_list[i] then
-- 				item_knapindex_list[i] = 0
-- 			end
-- 			if nil == item_comsume_num[i] then
-- 				item_comsume_num[i] = 0
-- 			end
-- 		end
-- 		-- 发送邮件
-- 		local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailSend)
-- 		send_protocol.recver_uid = user_info.role_id
-- 		send_protocol.gold = 0
-- 		send_protocol.coin = 0
-- 		send_protocol.item_count = count
-- 		send_protocol.item_knapindex_list = item_knapindex_list
-- 		send_protocol.item_comsume_num = item_comsume_num
-- 		send_protocol.subject = "si ren"
-- 		send_protocol.contenttxt = mailText
-- 		send_protocol:EncodeAndSend()
-- 		self:SendMailSucces()
-- 		SocietyWGData.Instance:SetIsWriteMailBool(false)
-- 		-- print_error("send-------------".. user_info.role_id)
-- 		-- self:DelSendMail()
-- 	end)
-- end

--删除邮件
function SocietyView:OnDeleteMail()
	-- print_error("OnDeleteMail-----------------------------------")

	if self.select_mail_index >= 0 then
		local mail = SocietyWGData.Instance:FindMail(self.select_mail_index)
		--print_error("单个删除邮件"..self.select_mail_index)
		--删除邮件时候判断是否没有附件
		if nil == mail then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NoSelect)
			return
		end
		if 0 == mail.has_attachment then
			--SocietyWGCtrl.Instance:SetDelMailCount(1)
			local send_protocol = ProtocolPool.Instance:GetProtocol(CSMailDelete)
			send_protocol.mail_index = self.select_mail_index
			send_protocol:EncodeAndSend()
			self.node_list["rich_mailcontent"].tmp.text = ""
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.CanNotDelwithFujian)
		end
	end
end

--设置接收的邮件详细信息
function SocietyView:SetMailDetailMsg(detail_info)
	local mail = SocietyWGData.Instance:FindMail(detail_info.mail_index)
	if not mail or not self:IsLoadedIndex(SocietyView.Tab_M) then
		return
	end

	SocietyWGData.Instance:SetIsReadMail(detail_info.mail_index)

	local name = Language.Society.GuangFang
	local is_filter_content = false
	if 1 == mail.mail_status.kind then
		name = mail.mail_status.sender_name
		self.send_name = name or ""
		is_filter_content = true
	elseif 3 == mail.mail_status.kind then
		name = Language.Society.guild
		is_filter_content = true
	end

	if mail.subject and mail.subject ~= ""then
		name = mail.subject
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local end_time = mail.mail_status.recv_time + 7 * 24 * 3600
	local time_table = TimeUtil.Format2TableDHMS(TimeWGCtrl.Instance:GetServerTime() - mail.mail_status.recv_time)
	local time_day_num = 20 - time_table.day >= 0 and 20 - time_table.day or 0
	self.node_list.label_endtime.tmp.text = string.format(Language.Society.MailGuoQiTime,time_day_num)
	self.node_list.rich_mailsendname.tmp.text  = Language.Society.EmailName
	local str11 = EmojiTextUtil.GetAnalysisText(name, "#A84B05FF", RICH_CONTENT_TYPE.Mail)
	self.node_list["rich_mailsendnametitle"].tmp.text = str11
	local str_receivetime = string.format(Language.Society.ReceiveTime,os.date("%Y/%m/%d  %H:%M", mail.mail_status.recv_time))
	self.node_list.label_receivetime.tmp.text = str_receivetime
	local content = detail_info.content_param.contenttxt
	if is_filter_content then
		content = ChatFilter.Instance:Filter(content)
	end

	content = string.gsub(content, "\\n","\n")

	local all_have_fujian = SocietyWGData.Instance:GetAllHaveFujian()
	self.node_list.btn_getfujian:SetActive(mail.has_attachment == 1)
	self.node_list.btn_getall:SetActive(#all_have_fujian > 1)
	self.node_list.btn_del:SetActive(mail.has_attachment ~= 1)
	local tmp = self.node_list.rich_mailcontent.tmp
	EmojiTextUtil.ParseRichText(tmp, content, 20, "#4A3908FF", nil, false,RICH_CONTENT_TYPE.Mail)
	if nil == self.mail_reward_list then
		self.mail_reward_list = AsyncListView.New(MailRewardRender, self.node_list.list_reward_list)
	end

	local num = 0
	local temp_reward_list = {}
	-- 物品
	for i, v in ipairs(detail_info.content_param.item_list) do
		if 0 ~= v.item_id and num < SocietyView.MAIL_FUJIAN_NUM then
			num = num + 1
			local item_data = v
			local item_id = item_data.item_id
			local cfg = ItemWGData.Instance:GetItemConfig(item_id)
			if nil == cfg then
				print_error("缺少物品对应配置：：：",item_id, "邮件数据：", detail_info.content_param.subject)
			end
			item_data.has_attachment = mail.has_attachment
			table.insert(temp_reward_list, item_data)
		end
	end

	local coin = detail_info.content_param.coin
	local coin_bind = detail_info.content_param.coin_bind
	local gold = detail_info.content_param.gold
	local gold_bind = detail_info.content_param.gold_bind
	local coin_index = 0
	local coin_bind_index = 0
	local gold_index = 0
	local gold_bind_index = 0
	local temp_gold_num = 0
	local temp_bindgold_num = 0
	local temp_coin_num = 0
	local temp_silver_num = 0

	-- 虚拟物品
	for i, v in ipairs(detail_info.content_param.virtual_item_list.virtual_item_type) do
		if v > -1 and num < SocietyView.MAIL_FUJIAN_NUM and detail_info.content_param.virtual_item_list.virtual_item_num[i] > 0 then
			local item_num = detail_info.content_param.virtual_item_list.virtual_item_num[i]
			if v == 5 then
				temp_bindgold_num = temp_gold_num + item_num
			elseif v == 6 then
				temp_gold_num = temp_gold_num + item_num
			elseif v == 9 or v == 7 then
				temp_coin_num = temp_coin_num + item_num
			elseif v == 14 then
				temp_silver_num = temp_silver_num + item_num
			else
				num = num + 1
				local index = num
				local money_extra = 0
				local item_data = {["item_id"] = SocietyView.MailVirtualItem[v] or 0, ["num"] = item_num, ["is_bind"] = 0, has_attachment = mail.has_attachment}

				table.insert(temp_reward_list, item_data)
			end
		end
	end

	self.node_list.Gold.tmp.text = detail_info.content_param.gold + temp_gold_num
	self.node_list.Gold_blind.tmp.text = detail_info.content_param.gold_bind + temp_bindgold_num
	self.node_list.Coin.tmp.text = detail_info.content_param.coin + temp_coin_num
	self.node_list.Silver.tmp.text = temp_silver_num
	local is_open_gold_group = detail_info.content_param.gold + temp_gold_num > 0 or detail_info.content_param.gold_bind + temp_bindgold_num > 0 or
	detail_info.content_param.coin + temp_coin_num > 0 or temp_silver_num > 0
	self.node_list.gold_group:SetActive(is_open_gold_group)
	-- tilte和奖励位置
	self.mail_reward_list:SetDataList(temp_reward_list)
	self.node_list.reward_title:SetActive(#temp_reward_list > 0 or is_open_gold_group)
	local pos_list = is_open_gold_group and #temp_reward_list > 0 and show_gold_group_pos or hide_gold_group_pos
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_title.rect, pos_list["title"].x, pos_list["title"].y)
	RectTransform.SetAnchoredPositionXY(self.node_list.list_reward_list.rect, pos_list["reward_list"].x, pos_list["reward_list"].y)

	-- 发件人文本位置
	local index =  3
	if is_open_gold_group and #temp_reward_list > 0 then
		index = 2
	elseif #temp_reward_list > 0 or is_open_gold_group then
		index = 1
	end
	RectTransform.SetAnchoredPositionXY(self.node_list.send_info_pos.rect, send_info_pos[index].x, send_info_pos[index].y)
end

function SocietyView:CreateEmailList()
	self.mail_list = AsyncListView.New(SocietyMailRender, self.list_mail_list)
	self.mail_list:SetSelectCallBack(BindTool.Bind1(self.EmailSelectCallBack, self))

	--self:Flush(SocietyView.Tab_M, "mail_list")
end
function SocietyView:SelectMailListIndexByItem(index)
	if self.mail_list then
		self.mail_list:SelectIndex(index)
	end
end

function SocietyView:EmailSelectCallBack(select_item, index)
--	 print_error("EmailSelectCallBack---------------------------------",select_item.data,index,self.mail_list:GetSelectIndex())

	if nil == select_item or nil == select_item.data then
		return
	end
	--SocietyWGData.Instance:SetCurMailItem(select_item.data)
	self.select_mail_data_temp = select_item.data
	self.select_mail_index = select_item.data.mail_index
	local flag, mail = SocietyWGData.Instance:SetMailRead(self.select_mail_index)

	if 1 == flag and mail then
		--local item = self.mail_list:GetItemAt(index)
		if select_item then
			select_item:SetData(mail)
		end
	end

	------------------------XUI.SetButtonEnabled(self.node_list["btn_getfujian"], mail~= nil and 0 ~= mail.has_attachment)
	-- todo:可优化，把信息缓存下来，不用每次都查询
	SocietyWGCtrl.Instance:CSMailRead(self.select_mail_index)
end

function SocietyView:FlushMailView(vas)  -- -1批量删除 0全部邮件 2 提取附件
	if nil == self.list_mail_list then
		return
	end

	if vas and vas < 0 then
		self:ClearReceiveMail()
	end

	local data = SocietyWGData.Instance:GetMailList()
	self.node_list.btn_getfujian:SetActive(false)
	self.node_list.btn_del:SetActive(vas == 2)
	local all_read_mail = SocietyWGData.Instance:GetAllReadMail()

	self.node_list.label_endtime:SetActive(#data > 0)
	self.node_list.mail_num_value.text.text = string.format(Language.Society.MailNum, #data)


	--通知主界面未读邮件
	if nil ~= data and nil ~= self.mail_list then
		--if nil == vas then
			table.sort(data, function(data1, data2)
				if nil ~= data1.mail_status and nil ~= data2.mail_status then
					--if data1.mail_status.is_read == data2.mail_status.is_read then
					return data1.mail_status.recv_time > data2.mail_status.recv_time
					--else
					--	return data1.mail_status.is_read < data2.mail_status.is_read
					--end
				end

				return true
			end)
		--end
		if #data == 0 then
			self.node_list["no_mail"].gameObject:SetActive(true)
		else
			self.node_list["no_mail"].gameObject:SetActive(false)
		end

		if vas and vas > -1 then
			self.mail_list:SetDataList(data, 2)
			local index = self.mail_list:GetSelectIndex()
			self.mail_list:SelectIndex(index)
		else
			self.mail_list:SetDefaultSelectIndex(1)
			self.mail_list:SetDataList(data, 0)
		end
	end

	self.node_list["text_tips_mail"]:SetActive(data == nil or IsEmptyTable(data))
end
function SocietyView:SelectNowSocietyIndex()
	--self.mail_list:SetDefaultSelectIndex(1)
end

function SocietyView:IOpenWriteMail(name)
	if name then
		self.input_sendName.text = name
		self:OnToSendView()
	end
end

function SocietyView:SendMailSucces()
	-- print_error("SendMailSucces")
	self:OnBackMainView()
	self.input_sendName.text = ""
	self.input_content.text = ""


end

function SocietyView:ReSelect()
	if nil == self.mail_list then
		return
	end

	local index = self.mail_list:GetSelectIndex()
	if index <= 0 then
		index = 1
	end

	self.mail_list:SelectIndex(index)
	-- local curr_item = self.mail_list:GetSelectItem()
	-- local index = self.mail_list:GetItemIndex(curr_item)
	-- if index <= 0 then
	-- 	index = 1
	-- 	curr_item = self.mail_list:GetItemAt(1)
	-- end

	-- if nil ~= curr_item then
	-- 	self.mail_list:SetSelectItem(curr_item, index)
	-- end
end

function SocietyView:SetSocietyMailIsPauseRefresh(is_pause)
	if nil ~= self.mail_list then
		self.mail_list:SetIsPuaseRefresh(is_pause)
 	end
end

--邮件回复(只提供给本view使用，如果外部调用请从society_ctrl中的接口调用)
function SocietyView:OnWriteBack()
	self:IOpenWriteMail(self.send_name)
	SocietyWGData.Instance.is_write_mail = true
end


MailRewardRender = MailRewardRender or BaseClass(BaseRender)

function MailRewardRender:__init()

end

function MailRewardRender:LoadCallBack()
	self.base_cell = ItemCell.New(self.node_list["pos"])
end

function MailRewardRender:ReleaseCallBack()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function MailRewardRender:OnFlush()
	if nil == self.data then return end
	self.node_list.get_mark:SetActive(self.data.has_attachment ~= 1)
	self.base_cell:SetData(self.data)

	if self.data.has_attachment ~= 1 then
		self.base_cell:SetUpFlagIconVisible(false)
		self.base_cell:SetEffectRootEnable(false)
	end
end
