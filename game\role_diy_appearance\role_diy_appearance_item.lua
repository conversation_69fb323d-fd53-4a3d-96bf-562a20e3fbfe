RoleDiyPresetStyleCell = RoleDiyPresetStyleCell or BaseClass(BaseRender)

function RoleDiyPresetStyleCell:LoadCallBack()

end

function RoleDiyPresetStyleCell:__delete()
end

function RoleDiyPresetStyleCell:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetDiyAppearanceImg(self.data.icon)
    self.node_list.icon_img.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_img.image:SetNativeSize()
    end)
end

function RoleDiyPresetStyleCell:OnSelectChange(is_select)
    self.node_list.hl_img:SetActive(is_select)
end


------------------------RoleDiyEyeShadowStyleCell------------
RoleDiyEyeShadowStyleCell = RoleDiyEyeShadowStyleCell or BaseClass(BaseRender)

function RoleDiyEyeShadowStyleCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickBtn,self))
end

function RoleDiyEyeShadowStyleCell:__delete()
end

function RoleDiyEyeShadowStyleCell:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetDiyAppearanceImg(self.data.icon)
    self.node_list.icon_img.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_img.image:SetNativeSize()
    end)
    
    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    self.node_list.hl_img:SetActive(cur_show_diy_appearance_data and cur_show_diy_appearance_data.eye_shadow_color == self.data.eye_shadow_color)
end

function RoleDiyEyeShadowStyleCell:OnClickBtn()
    if not self.data then
        return
    end

    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    if cur_show_diy_appearance_data and cur_show_diy_appearance_data.eye_shadow_color == self.data.eye_shadow_color then
        return
    end

    RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.EYE_SHADOW_COLOR, self.data)
end

------------------------RoleDiyPupilTypeCell------------
RoleDiyPupilTypeCell = RoleDiyPupilTypeCell or BaseClass(BaseRender)

function RoleDiyPupilTypeCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickBtn,self))
end

function RoleDiyPupilTypeCell:__delete()
end

function RoleDiyPupilTypeCell:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetDiyAppearanceImg(self.data.icon)
    self.node_list.icon_img.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_img.image:SetNativeSize()
    end)
    
    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    self.node_list.hl_img:SetActive(cur_show_diy_appearance_data and cur_show_diy_appearance_data.pupil_type == self.data.pupil_type)

    if self.data.is_pay_type ~= 1 then
        self.node_list.lock_btn:SetActive(false)
    else
        local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(self.data.pay_type, self.data.pay_seq)
        if pay_cfg then
            self.node_list.need_num.text.text = pay_cfg.consume_num 
            local item_icon = ItemWGData.Instance:GetItemIconByItemId(pay_cfg.consume_id)
            local bundle, asset = ResPath.GetItem(item_icon)
			self.node_list["consume_icon"].image:LoadSpriteAsync(bundle, asset)
        end
       
        local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(self.data.pay_type, self.data.pay_seq)
        self.node_list.lock_btn:SetActive(active_flag ~= 1)

        local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
		local cost_num = pay_cfg.consume_num
        self.node_list.red_img:SetActive(item_num >= cost_num and active_flag ~= 1)
    end
end

function RoleDiyPupilTypeCell:OnClickBtn()
    if not self.data then
        return
    end

    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    if cur_show_diy_appearance_data and cur_show_diy_appearance_data.eye_shadow_color == self.data.eye_shadow_color then
        return
    end

    RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.PUPIL_TYPE, self.data)
end
------------------------RoleDiyColorCell------------
RoleDiyColorCell = RoleDiyColorCell or BaseClass(BaseRender)

function RoleDiyColorCell:LoadCallBack()

end

function RoleDiyColorCell:__delete()
end

function RoleDiyColorCell:OnFlush()
    if not self.data then
        return
    end

    self.node_list.reset_img:SetActive(self.data.reset_flag == 1)
    self.node_list.color_img:SetActive(self.data.reset_flag == 0)

    if self.data.reset_flag == 0 then
        local bundle, asset = ResPath.GetDiyAppearanceImg(self.data.icon)
        self.node_list.color_img.image:LoadSprite(bundle, asset, function()
            self.node_list.color_img.image:SetNativeSize()
        end)
        -- local part_color = self.data.part_color
        -- local color = UtilU3d.ConvertHexToColor(part_color)
        -- self.node_list.color_img.image.color = color
    end
end

function RoleDiyColorCell:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.hl_img:SetActive(is_select and self.data.reset_flag == 0)
end


------------------------RoleDiyMouthTypeCell------------
RoleDiyMouthTypeCell = RoleDiyMouthTypeCell or BaseClass(BaseRender)

function RoleDiyMouthTypeCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickBtn,self))
end

function RoleDiyMouthTypeCell:__delete()
end

function RoleDiyMouthTypeCell:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetDiyAppearanceImg(self.data.icon)
    self.node_list.icon_img.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_img.image:SetNativeSize()
    end)
    
    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    self.node_list.hl_img:SetActive(cur_show_diy_appearance_data and cur_show_diy_appearance_data.mouth_color == self.data.mouth_color)
end

function RoleDiyMouthTypeCell:OnClickBtn()
    if not self.data then
        return
    end

    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    if cur_show_diy_appearance_data and cur_show_diy_appearance_data.mouth_color == self.data.mouth_color then
        return
    end

    RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.MOUTH_COLOR, self.data)
end

------------------------RoleDiyFaceDecalCell------------
RoleDiyFaceDecalCell = RoleDiyFaceDecalCell or BaseClass(BaseRender)

function RoleDiyFaceDecalCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickBtn,self))
end

function RoleDiyFaceDecalCell:__delete()
end

function RoleDiyFaceDecalCell:OnFlush()
    if not self.data then
        return
    end

    local cfg = self.data.cfg
    local icon_name = self.data.sex == 1 and cfg["nan_icon"] or cfg["nv_icon"]
    local bundle, asset = ResPath.GetDiyAppearanceImg(icon_name)
    self.node_list.icon_img.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_img.image:SetNativeSize()
    end)
    
    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    self.node_list.hl_img:SetActive(cur_show_diy_appearance_data and cur_show_diy_appearance_data.face_decal_id == cfg.face_decal_id)
    if cfg.is_pay_type ~= 1 then
        self.node_list.lock_btn:SetActive(false)
    else
        local pay_cfg = RoleDiyAppearanceWGData.Instance:GetPayCfgByTypeSeq(cfg.pay_type, cfg.pay_seq)
        if pay_cfg then
            self.node_list.need_num.text.text = pay_cfg.consume_num 
            local item_icon = ItemWGData.Instance:GetItemIconByItemId(pay_cfg.consume_id)
            local bundle, asset = ResPath.GetItem(item_icon)
			self.node_list["consume_icon"].image:LoadSpriteAsync(bundle, asset)
        end
       
        local active_flag = DressingRoleDiyWGData.Instance:GetDiyAppearanceActiveInfo(cfg.pay_type, cfg.pay_seq)
        self.node_list.lock_btn:SetActive(active_flag ~= 1)

        local item_num = ItemWGData.Instance:GetItemNumInBagById(pay_cfg.consume_id)
		local cost_num = pay_cfg.consume_num
        self.node_list.red_img:SetActive(item_num >= cost_num and active_flag ~= 1)
    end
end

function RoleDiyFaceDecalCell:OnClickBtn()
    if not self.data then
        return
    end

    local cfg = self.data.cfg
    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    if cur_show_diy_appearance_data and cur_show_diy_appearance_data.face_decal_id == cfg.face_decal_id then
        return
    end

    RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.FACE_DECAL, cfg)
end

------------------------RoleDiyHairTypeCell------------
RoleDiyHairTypeCell = RoleDiyHairTypeCell or BaseClass(BaseRender)

function RoleDiyHairTypeCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClickBtn,self))
end

function RoleDiyHairTypeCell:__delete()
end

function RoleDiyHairTypeCell:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetDiyAppearanceImg(self.data.icon)
    self.node_list.icon_img.image:LoadSprite(bundle, asset, function()
        self.node_list.icon_img.image:SetNativeSize()
    end)
    
    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    self.node_list.hl_img:SetActive(cur_show_diy_appearance_data and cur_show_diy_appearance_data.hair_id == self.data.hair_id)
end

function RoleDiyHairTypeCell:OnClickBtn()
    if not self.data then
        return
    end

    local cur_show_diy_appearance_data = RoleDiyAppearanceWGData.Instance:GetCurShowDiyData()
    if cur_show_diy_appearance_data and cur_show_diy_appearance_data.hair_id == self.data.hair_id then
        return
    end

    RoleDiyAppearanceWGCtrl.Instance:AddDiyAppearaceData(RoleDiyAppearanceWGData.ADD_DIY_TYPE.HAIR_TYPE, self.data)
end