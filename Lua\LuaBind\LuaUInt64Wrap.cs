﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class LuaUInt64Wrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(LuaUInt64), typeof(System.Object));
		<PERSON><PERSON>RegFunction("Make", Make);
		<PERSON><PERSON>Function("RShift", RShift);
		<PERSON><PERSON>unction("LShift", LShift);
		<PERSON><PERSON>unction("DAnd", DAnd);
		<PERSON><PERSON>Function("FromString", FromString);
		L.RegFunction("And", And);
		<PERSON><PERSON>RegFunction("Or", Or);
		<PERSON><PERSON>RegFunction("Xor", Xor);
		<PERSON><PERSON>RegFunction("FromDouble", FromDouble);
		<PERSON><PERSON>Function("ToDouble", ToDouble);
		<PERSON><PERSON>Function("ToString", ToString);
		<PERSON><PERSON>RegFunction("UInt64ToBytes", UInt64ToBytes);
		<PERSON><PERSON>RegFunction("BytesToUInt64", BytesToUInt64);
		<PERSON><PERSON>unction("LongLongToLuaNumber", LongLongToLuaNumber);
		<PERSON><PERSON>unction("New", _CreateLuaUInt64);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateLuaUInt64(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				LuaUInt64 obj = new LuaUInt64();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: LuaUInt64.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Make(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 1);
			uint arg1 = (uint)LuaDLL.luaL_checknumber(L, 2);
			byte[] o = LuaUInt64.Make(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RShift(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			long arg0 = LuaDLL.tolua_checkint64(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			double o = LuaUInt64.RShift(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LShift(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			long arg0 = LuaDLL.tolua_checkint64(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			double o = LuaUInt64.LShift(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DAnd(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			long arg0 = LuaDLL.tolua_checkint64(L, 1);
			long arg1 = LuaDLL.tolua_checkint64(L, 2);
			double o = LuaUInt64.DAnd(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FromString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			byte[] o = LuaUInt64.FromString(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int And(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
			byte[] o = LuaUInt64.And(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Or(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
			byte[] o = LuaUInt64.Or(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Xor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
			byte[] o = LuaUInt64.Xor(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FromDouble(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			double arg0 = (double)LuaDLL.luaL_checknumber(L, 1);
			byte[] o = LuaUInt64.FromDouble(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToDouble(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			double o = LuaUInt64.ToDouble(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToString(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1 && TypeChecker.CheckTypes<byte[]>(L, 1))
			{
				byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
				string o = LuaUInt64.ToString(arg0);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 1 && TypeChecker.CheckTypes<LuaUInt64>(L, 1))
			{
				LuaUInt64 obj = (LuaUInt64)ToLua.ToObject(L, 1);
				string o = obj.ToString();
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: LuaUInt64.ToString");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UInt64ToBytes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ulong arg0 = LuaDLL.tolua_checkuint64(L, 1);
			byte[] o = LuaUInt64.UInt64ToBytes(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BytesToUInt64(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
			ulong o = LuaUInt64.BytesToUInt64(arg0);
			LuaDLL.tolua_pushuint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LongLongToLuaNumber(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 1);
			uint arg1 = (uint)LuaDLL.luaL_checknumber(L, 2);
			double o = LuaUInt64.LongLongToLuaNumber(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

