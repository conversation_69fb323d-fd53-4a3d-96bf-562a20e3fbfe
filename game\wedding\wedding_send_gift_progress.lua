WeddingSendGiftProgressView = WeddingSendGiftProgressView or BaseClass(SafeBaseView)

function WeddingSendGiftProgressView:__init()
	self.active_close = false
	
	self.view_layer = UiLayer.FloatText
	self:LoadConfig()
end

function WeddingSendGiftProgressView:__delete()
	
end

function WeddingSendGiftProgressView:ReleaseCallBack()
	self.slider = nil
	self.danmu_is_open = nil
end

function WeddingSendGiftProgressView:LoadConfig()
    self.view_name = "WeddingSendGiftProgressView"
    self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_send_gift_2")
end

function WeddingSendGiftProgressView:LoadCallBack()
	self.slider = self.node_list["prog9_hunyan"]:GetComponent(typeof(UnityEngine.UI.Slider))
	self.node_list["danmu_btn"].button:AddClickListener(BindTool.Bind(self.OnClinkDanMu, self))
	if not self.danmu_is_open then
		self:OnClinkDanMu()
	end
end

function WeddingSendGiftProgressView:ShowIndexCallBack()
	self:Flush()
end

function WeddingSendGiftProgressView:CloseCallBack()
	
end

function WeddingSendGiftProgressView:OnFlush()
	self:WeddingBtn()
end


function WeddingSendGiftProgressView:WeddingBtn()
	local marry_cfg = MarryWGData.Instance:GetCurWeddingInfo()
	local my_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local weeding_info = MarryWGData.Instance:GetWeddingRoleInfo()
	local seq = MarryWGData.Instance:GetInviteGuestsByWeddingSequence().wedding_yuyue_seq
	local applincant_data_num = MarryWGData.Instance:GetWeddingApplicantRed(seq)
	if next(weeding_info) then
		local redu_cfg = MarryWGData.Instance:GetHunYanCfgByReDu(weeding_info.wedding_liveness)

		if my_id == marry_cfg.role_id or my_id == marry_cfg.lover_role_id then
			-- XUI.SetButtonEnabled(self.node_list["btn_wedding"], weeding_info.is_baitang ~= 2)
			--self.node_list["btn_guests_msg"]:SetActive(true)
			--self.node_list["img_flag"]:SetActive(applincant_data_num > 0)
		else
			-- XUI.SetButtonEnabled(self.node_list["btn_wedding"], false)
			--self.node_list["btn_guests_msg"]:SetActive(false)
		end
		-- self.node_list["lbl_wedding"].text.text = (weeding_info.is_baitang ~= 2) and Language.Marry.WeddingBaiTangBtn[1] or Language.Marry.WeddingBaiTangBtn[2]

		local percent = 0
		if weeding_info.wedding_liveness <= redu_cfg.liveness_var then
			percent = (weeding_info.wedding_liveness / redu_cfg.liveness_var)	
		else
			percent = 1
		end

		if nil ~= self.slider then
			self.slider.value = percent
		end

		self.node_list["lbl_zhufuzhi"].text.text = weeding_info.wedding_liveness .. "/" .. redu_cfg.liveness_var
	end
end

--拜堂
-- function WeddingSendGiftView:OnBtnWeddingHandler()
-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Marry.BaiTangQingQiu)
-- 	WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_OPERA_TYPE_BAITANG_REQ)
-- end

function WeddingSendGiftProgressView:OnClinkDanMu()
	self.danmu_is_open = not self.danmu_is_open
	self.node_list.danmu_btn_on:SetActive(self.danmu_is_open)
	if self.danmu_is_open then
		ViewManager.Instance:Open(GuideModuleName.MarryDanMu)
	else
		ViewManager.Instance:Close(GuideModuleName.MarryDanMu)
	end
end