SiXiangExchangeView = SiXiangExchangeView or BaseClass(SafeBaseView)

function SiXiangExchangeView:__init(view_name)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(1098, 590)})
    self:AddViewResource(0, "uis/view/sixiang_call_prefab", "sixiang_exchange_view")
    self:SetMaskBg(true, true)
end

function SiXiangExchangeView:LoadCallBack()
    self:InitPanel()
end

function SiXiangExchangeView:ReleaseCallBack()
    if self.exchange_grid then
        self.exchange_grid:DeleteMe()
        self.exchange_grid = nil
    end
end

function SiXiangExchangeView:OpenCallBack()

end

function SiXiangExchangeView:OnFlush(param_t)
    self:RefreshView()
end

function SiXiangExchangeView:InitPanel()
    local bundle = "uis/view/sixiang_call_prefab"
    local asset = "sixiang_exchange_render"
    self.exchange_grid = AsyncBaseGrid.New()
    self.exchange_grid:SetStartZeroIndex(false)
    self.exchange_grid:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["exchange_list"],
                assetBundle = bundle, assetName = asset, itemRender = SiXiangExchangeItem})
end

function SiXiangExchangeView:RefreshView()
    --local show_index = SiXiangCallWGCtrl.Instance:GetCurShowIndex()
    local data_list = {}
    local title_str = ""
    -- if show_index == TabIndex.sixiang_call_sx then
    --     data_list = SiXiangCallWGData.Instance:GetExchangeDataList()
    --     title_str = Language.SiXiangCall.ShopTitle
    -- elseif show_index == TabIndex.sixiang_call_xqzl then--仙器真炼
    --     data_list = SiXiangCallWGData.Instance:GetXQZLExchangeDataList()
    --     title_str = Language.SiXiangCall.XQZLShopTitle
    -- end
    data_list = SiXiangCallWGData.Instance:GetXQZLExchangeDataList()
    title_str = Language.SiXiangCall.XQZLShopTitle

    self.node_list.title_view_name.text.text = title_str
    if not IsEmptyTable(data_list) then
        self.exchange_grid:SetDataList(data_list)
    end
end

-------------------------------------------------------------------------------

SiXiangExchangeItem = SiXiangExchangeItem or BaseClass(BaseRender)

function SiXiangExchangeItem:__init()
    
end

function SiXiangExchangeItem:__delete()
    self.item_cell:DeleteMe()
    self.item_cell = nil
    self.show_tab_index = nil
end

function SiXiangExchangeItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_root)
    XUI.AddClickEventListener(self.node_list.const_img, BindTool.Bind1(self.OnClickConstImg, self))
    XUI.AddClickEventListener(self.node_list.exchange_btn, BindTool.Bind1(self.OnClickExchangeBtn, self))
end

function SiXiangExchangeItem:OnFlush()
    self.show_tab_index = SiXiangCallWGCtrl.Instance:GetCurShowIndex()
    local data = self:GetData()

    self.item_cell:SetData(data.goods)

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.goods.item_id)
    if item_cfg then
        self.node_list.item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    end

    local const_item_id = 0
    local money_num = 0
    -- if self.show_tab_index == TabIndex.sixiang_call_sx then
    --     const_item_id = SiXiangCallWGData.Instance:GetSiXiangSummonOtherCfg("lotto_exchange")
    --     money_num = SiXiangCallWGData.Instance:GetSiXiangShopMoneyNum()
    -- elseif self.show_tab_index == TabIndex.sixiang_call_xqzl then--仙器真炼
    --     local item_tab = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("lotto_exchange")
    --     if item_tab and item_tab.item_id then
    --         const_item_id = item_tab.item_id
    --     end
    --     money_num = SiXiangCallWGData.Instance:GetXianQitShopMoneyChange()
    -- end

    local item_tab = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("lotto_exchange")
    if item_tab and item_tab.item_id then
        const_item_id = item_tab.item_id
    end
    money_num = SiXiangCallWGData.Instance:GetXianQitShopMoneyChange()

    local const_item_cfg = ItemWGData.Instance:GetItemConfig(const_item_id)
    if const_item_cfg then
        local b, a = ResPath.GetItem(const_item_cfg.icon_id)
        self.node_list["const_img"].image:LoadSprite(b, a)
    end

    local str_color = money_num >= data.exchange_num and COLOR3B.GREEN or COLOR3B.RED
    local str = string.format("%d/%d", money_num, data.exchange_num)
    self.node_list.const_lbl.text.text = ToColorStr(str, str_color)
end

function SiXiangExchangeItem:OnClickConstImg()
    local const_item_id = 0
    -- if self.show_tab_index == TabIndex.sixiang_call_sx then
    --     const_item_id = SiXiangCallWGData.Instance:GetSiXiangSummonOtherCfg("lotto_exchange")
    -- elseif self.show_tab_index == TabIndex.sixiang_call_xqzl then--仙器真炼
    --     local item_tab = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("lotto_exchange")
    --     if item_tab and item_tab.item_id then
    --         const_item_id = item_tab.item_id
    --     end
    -- end
    local item_tab = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("lotto_exchange")
    if item_tab and item_tab.item_id then
        const_item_id = item_tab.item_id
    end
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = const_item_id})
end

function SiXiangExchangeItem:OnClickExchangeBtn()
    local data = self:GetData()
    local money_num = 0
    -- if self.show_tab_index == TabIndex.sixiang_call_sx then
    --     money_num = SiXiangCallWGData.Instance:GetSiXiangShopMoneyNum()
    -- elseif self.show_tab_index == TabIndex.sixiang_call_xqzl then--仙器真炼
    --     money_num = SiXiangCallWGData.Instance:GetXianQitShopMoneyChange()
    -- end
    money_num = SiXiangCallWGData.Instance:GetXianQitShopMoneyChange()
    if data and money_num >= data.exchange_num then
        -- if self.show_tab_index == TabIndex.sixiang_call_sx then
        --     SiXiangCallWGCtrl.Instance:SendSiXiangRequest(OP_YUAN_SHEN_ZHAO_HUAN_TYPE.SHOP_CONVERT, data.seq)
        -- elseif self.show_tab_index == TabIndex.sixiang_call_xqzl then--仙器真炼
        --     SiXiangCallWGCtrl.Instance:SendMachineOp(MACHINE_OP_TYPE.SHOP_CONVERT, data.seq)
        -- end
        SiXiangCallWGCtrl.Instance:SendMachineOp(MACHINE_OP_TYPE.SHOP_CONVERT, data.seq)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
    end
end