LoverPkWGData = LoverPkWGData or BaseClass()

-- 淘汰赛时间线状态
LoverPkWGData.KNOCKOUT_STATE = {
    WAIT_START = 0, -- 等待下一轮开始
    IN_COMBAT = 1,  -- 战斗中
    IS_END = 2,     -- 战斗结束
}

-- 淘汰赛轮次
LoverPkWGData.COMPETIITION_SYSTEM_ORDER = {
	[0] = 16,
	[1] = 8,
	[2] = 4,
	[3] = 2,
}

-- 后端定的轮次
-- 0(第一名!)            (轮次4)
-- 0				  1			   (轮次3)
-- 0	   1	    2          3	   (轮次2)
-- 0   1   2   3   4    5     6     7    (轮次1: 决定轮次2有哪些人)
-- 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15  (轮次0:初始化)

function LoverPkWGData:__init()
    if LoverPkWGData.Instance then
        ErrorLog("[KuafuPVPWGData] attempt to create singleton twice!")
        return
    end
    LoverPkWGData.Instance =self

    local cfg = ConfigManager.Instance:GetAutoConfig("cross_couple_battle_config_auto")
    self.other_cfg = cfg.other[1]
    self.match_rank_reward_cfg = cfg.match_rank_reward
    self.match_count_cfg = cfg.match_count
    self.knockout_rank_reward_cfg = cfg.knockout_rank_reward
    self.gather_rewards_cfg = cfg.gather_rewards
    self.match_reward_cfg = cfg.match_reward
    self.knockout_cfg = cfg.knockout[1]
    self.knockout_guard_monsters_cfg = cfg.knockout_guard_monsters[1]
    self.send_flower_buff_cfg = cfg.send_flower_buff
    self.knockout_guess_cfg = cfg.knockout_guess

    self.fszz_uuid1 = {}
    self.fszz_uuid2 = {}
    self.regular_season_rank_info = {}  -- 常规赛 初赛 积分榜 倾城对决
    self.cross_couple_2v2_score = 0
    self.cross_couple_2v2_lover_score = 0
	self.cross_couple_2v2_match_count = 0
    self.cross_couple_2v2_total_match_count = 0
	self.reward_acquired_list_flag = {}

    self.lover_pk_qcdj_my_score = 0
    self.lover_pk_qcdj_lover_score = 0

    self.match_count_reward_remind = false
    self.lover_pk_qcdj_remind = false
    self.match_rank_reward_list = {}
    self.knockout_rank_info = {}  --封神之战排行榜
    self.knockout_rank_reward_list = {}

    self.total_send_flower_count = 0

    self.is_matchinging = false   -- 是否匹配中
    self.match_type = CROSS_COUPLE_2V2_ATCH_TYPE.MATCH   --匹配类型   匹配赛   淘汰赛
    self.match_team_type = CROSS_COUBLE_2V2_MATCH_TYPE.SINGLE_PERSON    --匹配赛队伍类型   单人 双人
    self.start_match_time = 0   -- 开始匹配的时间戳

    self.pk_state = -1
	self.next_state_time = -1
    self.side_list = {}
    self.match_side = -1

    self.dzb_menber_info_list = {}

    self.cal_dzb_menber_result_info = {}
    self.dzb_guess_info_list = {}


    self.can_join_knockout = false   -- 是否有资格参加淘汰赛
    self.konckout_is_lose = false   --淘汰赛是否已经被淘汰
    self.my_konckout_round_cache = {}  -- 淘汰赛每轮我所在位置的信息
    self.is_knockout_winner = false  -- 是否是冠军

    ----------淘汰赛----------
    self.knockout_round = -1   -- 当前轮次
    self.knockout_next_round_start_time = 0 -- 下一场战斗开始时间
    self.knockout_cur_round_start_time = 0  -- 本轮战斗开始时间

    self.send_flowers_count = 0

    RemindManager.Instance:Register(RemindName.LoverPkQCDJ, BindTool.Bind(self.GetLoverPkQCDJRemind, self))
    RemindManager.Instance:Register(RemindName.LoverPkFSJL, BindTool.Bind(self.GetLoverPkFSJLRemind, self))
    RemindManager.Instance:Register(RemindName.LoverPKDZB, BindTool.Bind(self.GetLoverPkDZBViewRemind, self))

    self:InitCache()
end

function LoverPkWGData:__delete()
    LoverPkWGData.Instance = nil
end

function LoverPkWGData:InitCache()
    local match_rank_reward_list = {}

    for k, v in pairs(self.match_rank_reward_cfg) do
        if v.min_rank <= v.max_rank then
            for i = v.min_rank, v.max_rank do
                match_rank_reward_list[i] = match_rank_reward_list[i] or {}
                match_rank_reward_list[i].rank_id = i
                match_rank_reward_list[i].cfg = v
            end
        end
    end

    self.match_rank_reward_list = match_rank_reward_list

    local knockout_rank_reward_list = {}
    for k, v in pairs(self.knockout_rank_reward_cfg) do
        if v.min_rank <= v.max_rank then
            for i = v.min_rank, v.max_rank do
                knockout_rank_reward_list[i] = knockout_rank_reward_list[i] or {}
                knockout_rank_reward_list[i].rank_id = i
                knockout_rank_reward_list[i].cfg = v
            end
        end
    end

    self.knockout_rank_reward_list = knockout_rank_reward_list
end

-------------------------------------------get_start--------------------------------------------
function LoverPkWGData:GetOtherCfg()
    return self.other_cfg
end

function LoverPkWGData:GetOtherCfgDataByAttrName(name)
    return self.other_cfg[name]
end

-- {rank_id = , info = ，cfg = }
function LoverPkWGData:GetRegularSeasonRankDataListAndMyInfo()
    local my_rank_info = {}
    local uuid = RoleWGData.Instance:GetUUid()

    for k, v in pairs(self.match_rank_reward_list) do
        local rank_id = v.rank_id
        local rank_info = self:GetRegularSeasonRankInfoByRankId(rank_id)
        v.info = rank_info

        if uuid == rank_info.uuid1 or uuid == rank_info.uuid2 then
            my_rank_info = v
        end
    end

    return self.match_rank_reward_list, my_rank_info
end

function LoverPkWGData:GetMatchCountRewardCfgBySeq(seq)
    return self.match_count_cfg[seq]
end

function LoverPkWGData:GetMatchCountRewardCfg()
    return self.match_count_cfg
end

function LoverPkWGData:GetKnockoutRankDataListAndMyInfo()
    local my_rank_info = {}
    local uuid = RoleWGData.Instance:GetUUid()

    for k, v in pairs(self.knockout_rank_reward_list) do
        local rank_id = v.rank_id
        local rank_info = self:GetKnockoutRankInfoByRankId(rank_id)
        v.info = rank_info

        if IsEmptyTable(my_rank_info) and (uuid == rank_info.uuid1 or uuid == rank_info.uuid2) then
            my_rank_info = v
        end
    end

    return self.knockout_rank_reward_list, my_rank_info
end

function LoverPkWGData:GetBountyDataInfo()
    local total_flower_count = self:GetTotalSendFlowerCount()
    local cur_grade = 0

    for k, v in pairs(self.gather_rewards_cfg) do
        if total_flower_count >= v.flowers_count_begin and total_flower_count <= v.flowers_count_end then
            cur_grade = v.grade
            break
        end

        cur_grade = v.grade
    end

    local cur_grade_cfg = self:GetGatherRewardsCfgByGrade(cur_grade)
    local next_grade_cfg = self:GetGatherRewardsCfgByGrade(cur_grade + 1)

    return total_flower_count, cur_grade_cfg, next_grade_cfg
end

function LoverPkWGData:GetGatherRewardsCfgByGrade(grade)
    return self.gather_rewards_cfg[grade] or {}
end

function LoverPkWGData:GetMatchRewardCfgByIsCouple(is_couple)
    return self.match_reward_cfg[is_couple == 1 and 1 or 0]
end

function LoverPkWGData:GetKnockoutGuardMonstersCfg()
    return self.knockout_guard_monsters_cfg
end

function LoverPkWGData:GetSendFlowerBuffCfg()
    return self.send_flower_buff_cfg
end

function LoverPkWGData:GetKnockoutGuessCfg(knockout)
    return self.knockout_guess_cfg[knockout]
end

function LoverPkWGData:GetFSZZRoleUUID()
    return self.fszz_uuid1, self.fszz_uuid2
end

function LoverPkWGData:GetRegularSeasonRankDataList()
    return self.regular_season_rank_info
end

function LoverPkWGData:GetRegularSeasonRankInfoByRankId(rank_id)
    return self.regular_season_rank_info[rank_id] or {}
end

function LoverPkWGData:GetKnockoutRankInfoByRankId(rank_id)
    return self.knockout_rank_info[rank_id] or {}
end

function LoverPkWGData:GetCrossCoupleMatchCount()
    return self.cross_couple_2v2_total_match_count
end

function LoverPkWGData:GetCrossCoupleMatchGetRewardFlag(seq)
    return self.reward_acquired_list_flag[seq] == 1
end

function LoverPkWGData:IsCanGetMatchCountRewardFlag(seq)
    local can_get, is_get = false, false

    local cfg = self:GetMatchCountRewardCfgBySeq(seq)
    if IsEmptyTable(cfg) then
        return is_get, can_get
    end

    local match_count = self:GetCrossCoupleMatchCount()
    is_get = self:GetCrossCoupleMatchGetRewardFlag(seq)
    can_get = not is_get and match_count >= cfg.match_count

    return can_get, is_get
end

function LoverPkWGData:GetLoverPKQCDJScore()
    return self.lover_pk_qcdj_my_score, self.lover_pk_qcdj_lover_score
end

function LoverPkWGData:GetTotalSendFlowerCount()
    return self.total_send_flower_count
end

-- 匹配状态
function LoverPkWGData:GetIsMatching()
    return self.is_matchinging
end

-- 赛制类型  
function LoverPkWGData:GetMatchType()
    return self.match_type
end

function LoverPkWGData:GetMatchingTeamType()
    return self.match_team_type
end

-- 已经匹配的时间
function LoverPkWGData:GetMatchTime()
    if not self:GetIsMatching() then
        return 0
    end

    return math.floor(TimeWGCtrl.Instance:GetServerTime() - self.start_match_time)
end

function LoverPkWGData:GetTotalCoupleScore()
    return self.cross_couple_2v2_score, self.cross_couple_2v2_lover_score
end

function LoverPkWGData:GetMatchCount()
    return self.cross_couple_2v2_match_count
end

function LoverPkWGData:GetMatchCountRewardRemind()
    return self.match_count_reward_remind
end

function LoverPkWGData:GetLoverPKFightMenberInfo()
    return self.side_list
end

function LoverPkWGData:GetMyMatchSide()
    return self.match_side
end

function LoverPkWGData:GetMatchPKStateAndNextStateTime()
    return self.pk_state, self.next_state_time
end

function LoverPkWGData:GetDzbMenberInfoByRoundAndIdx(round, idx)
    return (self.dzb_menber_info_list[round] or {})[idx] or {}
end

function LoverPkWGData:GetKnockoutCfgByAttrName(name)
    return self.knockout_cfg[name]
end

-- 对战输赢结果数据
function LoverPkWGData:GetDZBMenberResultInfo(round, idx)
    return (self.cal_dzb_menber_result_info[round] or {})[idx] or {}
end

-- 对战竞猜状态数据
function LoverPkWGData:GetDZBMenberGuessInfo(round, idx)
    return (self.dzb_guess_info_list[round] or {})[idx] or {}
end

-- 能否 竞猜
function LoverPkWGData:IsCanGuess(round, idx)
    -- 是否已经竞猜
    local cur_guess_data = self:GetDZBMenberGuessInfo(round, idx)
    if not IsEmptyTable(cur_guess_data) then
        return false
    end

    local other_idx = idx == 0 and 1 or (idx % 2 == 0 and (idx + 1) or (idx - 1))
    local team_other_guess_data = self:GetDZBMenberGuessInfo(round, other_idx)
    if not IsEmptyTable(team_other_guess_data) then
        return false
    end

    local cur_knockout_round = self:GetCurKnockoutRound()

    if round <= cur_knockout_round then
        return false
    end
    
    --时间内？
    local knockout_state_end_time = self:GetKnockoutStateEndTime()

    if knockout_state_end_time <= 0 then
        return false
    end
end

function LoverPkWGData:CanShowJingCaiFlag(round, idx)
    local result_info = self:GetDZBMenberResultInfo(round, idx)
	local is_result = result_info and result_info.is_result or false
	local is_winner = result_info and result_info.is_win or false

    if is_result and is_winner then
        return false
    end

    local other_index = idx == 0 and 1 or (idx % 2 == 0 and (idx + 1) or (idx - 1))
    local other_result_info = self:GetDZBMenberResultInfo(round, other_index)
	local oth_is_result = other_result_info and other_result_info.is_result or false
	local oth_is_winner = other_result_info and other_result_info.is_win or false

    if oth_is_result and oth_is_winner then
        return false
    end

    local knockout = self:GetCurKnockoutRound() + 1
	if round ~= knockout then
        return false
    end

    local guess_info = self:GetDZBMenberGuessInfo(round, idx)
    if guess_info and guess_info.status == 1 then
        return true
    end

    return false
end

-- 淘汰赛阶段   时间
function LoverPkWGData:GetKnockoutStateEndTime()
    local time = self.knockout_next_round_start_time - TimeWGCtrl.Instance:GetServerTime()
    return time > 0 and time or 0
end

function LoverPkWGData:GetKnockoutState()
    local knockout_state = LoverPkWGData.KNOCKOUT_STATE.WAIT_START
    local knockout_round = self:GetCurKnockoutRound()
    -- 后端特殊 - 1 是第一段前 最开始的起点
    if knockout_round < 0 then
        knockout_state = LoverPkWGData.KNOCKOUT_STATE.WAIT_START
    elseif knockout_round == 4 then
        knockout_state = LoverPkWGData.KNOCKOUT_STATE.IS_END
    else
        local knockout_ready_time = self:GetKnockoutCfgByAttrName("knockout_ready_time") or 0   -- 准备时间  30s
        local fight_time = self:GetKnockoutCfgByAttrName("fight_time") or 0                       --战斗时间 180
        local server_time = TimeWGCtrl.Instance:GetServerTime()                 --服务器时间
        local diff = server_time - self.knockout_cur_round_start_time                  -- 差值正数递增
        -- 战斗中
        if diff >= 0 and diff <= fight_time then
            knockout_state = LoverPkWGData.KNOCKOUT_STATE.IN_COMBAT
        elseif diff >= 0 and diff > fight_time and diff <= fight_time + knockout_ready_time then
            --等待下轮开始
            knockout_state = LoverPkWGData.KNOCKOUT_STATE.WAIT_START
        end
    end

    return knockout_state
end

function LoverPkWGData:GetCurKnockoutRound()
    return self.knockout_round
end

function LoverPkWGData:GetKnockoutRoundIsLose()
    return self.konckout_is_lose, self.konckout_player_rank_round
end

function LoverPkWGData:CanJoinKnockout()
    return self.can_join_knockout
end

function LoverPkWGData:GetKnockoutRoundIsBye(round)
    if IsEmptyTable(self.my_konckout_round_cache[round]) then
        return false
    end

    local data = self.my_konckout_round_cache[round]
    local round = data.round
    local idx = data.idx
    local other_idx = idx == 0 and 1 or (idx % 2 == 0 and (idx + 1) or (idx - 1)) 
    local next_info = self:GetDzbMenberInfoByRoundAndIdx(round, other_idx)

    if IsEmptyTable(next_info) then
        return true
    end

    return false
end

function LoverPkWGData:IsKnockoutWinner()
    return self.is_knockout_winner
end

function LoverPkWGData:GetDZBWInnerInfo()
    return self.dzb_menber_winner_info
end

-- 所有可以送花的队伍
function LoverPkWGData:GetCanSendFlowerMenberInfoList()
    local data_list = {}

    for i = 0, 15 do
        local data = self:GetDzbMenberInfoByRoundAndIdx(0, i)
        if not IsEmptyTable(data) then
            table.insert(data_list, data)
        end
    end

    return data_list
end

--是否存在队伍能竞猜
function LoverPkWGData:IsCanJingcai()
    local knockout = self:GetCurKnockoutRound()
    knockout = knockout >= 0 and knockout or 0
    local team_num = LoverPkWGData.COMPETIITION_SYSTEM_ORDER[knockout]

    if not team_num then
        return false
    end

    local can_jingcai = self:GroupCanJingCai(knockout, team_num)
    if not can_jingcai then
        return false
    end

    return true
end

function LoverPkWGData:GroupCanJingCai(knockout, num)
    for i = 0, num - 1 do
        local idx1 = i == 0 and 0 or i
        local oth_idx = i == 0 or i % 2 == 0 and (i + 1) or i - 1
        local info1 = self:GetDzbMenberInfoByRoundAndIdx(knockout, idx1)
		local info2 = self:GetDzbMenberInfoByRoundAndIdx(knockout, oth_idx)
        if not IsEmptyTable(info1) and not IsEmptyTable(info2) then
			return true
		end
    end

	return false
end

function LoverPkWGData:TeamCanJingCai(knockout, idx)
    local is_lunkong = false
    local is_jingcai = false
    knockout = knockout >= 0 and knockout or 0
    local idx1 = idx == 0 and 0 or idx
    local oth_idx = (idx == 0 or idx % 2 == 0) and (idx + 1) or idx - 1
    local info1 = self:GetDzbMenberInfoByRoundAndIdx(knockout, idx1)
    local info2 = self:GetDzbMenberInfoByRoundAndIdx(knockout, oth_idx)
    if IsEmptyTable(info1) or IsEmptyTable(info2) then
        is_lunkong = true
    end

    local jingcai_info = self:GetDZBMenberGuessInfo(knockout, idx1)
    local jingcai_info2 = self:GetDZBMenberGuessInfo(knockout, oth_idx)

    if not IsEmptyTable(jingcai_info) or not IsEmptyTable(jingcai_info2) then
        is_jingcai = true
    end

    return is_lunkong, is_jingcai
end

function LoverPkWGData:IsGroupAllJingCai(knockout)
    knockout = knockout + 1
    local num = LoverPkWGData.COMPETIITION_SYSTEM_ORDER[knockout]
    -- print_error("该组是否已经竞猜完全",knockout, num)
    for i = 0, num - 1 do
        local idx1 = i == 0 and 0 or i
        local oth_idx = (i == 0 or i % 2 == 0) and (i + 1) or i - 1
        local info1 = self:GetDZBMenberGuessInfo(knockout, idx1)
		local info2 = self:GetDZBMenberGuessInfo(knockout, oth_idx)
        -- print_error("该组是否已经竞猜完全",knockout, idx1,oth_idx,IsEmptyTable(info1), IsEmptyTable(info2))
        if IsEmptyTable(info1) and IsEmptyTable(info2) then
			return false
		end
    end

	return true
end

function LoverPkWGData:GetMySendFlowerCount()
    return self.send_flowers_count, self:GetOtherCfgDataByAttrName("flower_reward_count")
end
--------------------------------------------get_end---------------------------------------------

----------------------------------------------cal_start-----------------------------------------
function LoverPkWGData:CalLoverPkQCDJRemind()
    local remind = false
    local count_remind = false
    local mecha_count_reward_cfg = self:GetMatchCountRewardCfg()

    if not IsEmptyTable(mecha_count_reward_cfg) then
        for k, v in pairs(mecha_count_reward_cfg) do
            if self:IsCanGetMatchCountRewardFlag(v.seq) then
                remind = true
                count_remind = true
                break
            end
        end
    end

    self.lover_pk_qcdj_remind = remind
    self.match_count_reward_remind = count_remind
end
----------------------------------------------cal_end-------------------------------------------

-------------------------------------------remind_start-----------------------------------------
function LoverPkWGData:GetLoverPkQCDJRemind()
    local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    if not act_isopen then
        return 0
    end

    if self.lover_pk_qcdj_remind then
        return 1
    end

    return 0
end

function LoverPkWGData:GetLoverPkFSJLRemind()
    local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    if not act_isopen then
        return 0
    end

    if self:GetLoverPKDZBRemind() then
        return 1
    end

    return 0
end

function LoverPkWGData:GetLoverPkDZBViewRemind()
    local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    if not act_isopen then
        return 0
    end

    if self:GetLoverPKDZBRemind() then
        return 1
    end

    return 0
end

function LoverPkWGData:GetLoverPKDZBRemind()
    if IsEmptyTable(self.dzb_menber_info_list) then
        return false
    end
    
    for round, round_data in pairs(self.dzb_menber_info_list) do
        for idx, idx_data in pairs(round_data) do
            local result_info = LoverPkWGData.Instance:GetDZBMenberResultInfo(round, idx)

            if not IsEmptyTable(result_info) then
                local guess_info = LoverPkWGData.Instance:GetDZBMenberGuessInfo(round, idx)
                local is_win = result_info.is_result and result_info.is_win
                if is_win and not IsEmptyTable(guess_info) then
                    local can_get_reward = guess_info.status == 1

                    if can_get_reward then
                       return true
                    end
                end
            end
        end
    end

    return false
end

-------------------------------------------remind_end-------------------------------------------

-------------------------------------------protocol_start---------------------------------------
function LoverPkWGData:SetSCCross2V2WinnerInfo(protocol)
    self.fszz_uuid1 = protocol.uuid1
    self.fszz_uuid2 = protocol.uuid2
end

function LoverPkWGData:SetSCCrossCouple2v2Rank(protocol)
    self.regular_season_rank_info = protocol.rank_info
end

function LoverPkWGData:SetSCCrossCouple2V2BaseInfo(protocol)
    self.cross_couple_2v2_score = protocol.cross_couple_2v2_score 		 -- 个人总积分
    self.cross_couple_2v2_lover_score = protocol.cross_couple_2v2_lover_score   --伴侣积分
	self.cross_couple_2v2_match_count = protocol.cross_couple_2v2_match_count -- 个人单日总匹配次数  匹配赛战斗次数
    self.cross_couple_2v2_total_match_count = protocol.cross_couple_2v2_total_match_count --个人赛季总匹配场次
	self.reward_acquired_list_flag = bit:d2b_l2h(protocol.reward_acquired_list_flag, nil, true)   --比赛场次奖励领取标记
    self.send_flowers_count = protocol.send_flowers_count

    self:CalLoverPkQCDJRemind()
end

function LoverPkWGData:SetSCCross2V2CoupleInfo(protocol)
    local my_uuid = RoleWGData.Instance:GetUUid()
    local is_uuid1 = my_uuid == protocol.uuid1
    local score1, score2 = protocol.score1, protocol.score2

    self.lover_pk_qcdj_my_score = is_uuid1 and score1 or score2
    self.lover_pk_qcdj_lover_score = is_uuid1 and score2 or score1
end

function LoverPkWGData:SetSCCrossCouple2v2KnockOutRank(protocol)
    self.knockout_rank_info = protocol.rank_info
end

function LoverPkWGData:SetTotalFlowersCount(protocol)
    self.total_send_flower_count = protocol.total_send_flower_count
end

function LoverPkWGData:SetSCCross2V2MatchingInfo(protocol) --  1 2 
    self.start_match_time = TimeWGCtrl.Instance:GetServerTime()
    self.match_team_type = protocol.team_type
    local notify_reason = protocol.notify_reason

    if notify_reason == Cross2V2MatchingInfoNotifyReason.StartMatching then
        self.is_matchinging = true
    elseif notify_reason == Cross2V2MatchingInfoNotifyReason.MatchingSucc then
        self.is_matchinging = false
    end
end

function LoverPkWGData:SetSCCross2V2MatchingCancel(protocol)   --
    self.is_matchinging = false
end

function LoverPkWGData:SetSCCrossCouple2v2StandBySceneInfo(protocol)
    self.match_type = protocol.match_type
    -- print_error("当前是匹配赛还是淘汰赛", self.match_type, self.match_type == CROSS_COUPLE_2V2_ATCH_TYPE.MATCH and "匹配赛" or "淘汰赛")
end

-- 比赛状态 0 倒计时  1对战  2 结束时间戳
function LoverPkWGData:SetSCCrossCouple2V2PKSceneInfo(protocol)
    self.pk_state = protocol.pk_state
	self.next_state_time = protocol.next_state_time
    self.side_list = protocol.side_list

    local uuid = RoleWGData.Instance:GetUUid()
    for k, v in pairs(self.side_list) do
        for i, u in pairs(v) do
            if uuid == u.user_id then
                self.match_side = u.side
                -- print_error("设置我的队伍类型", u.side)
                break
            end
        end
    end
end

-- 对阵表信息  --淘汰赛对阵表信息    增量全体换
function LoverPkWGData:SetSCCross2V2KnockoutMatchInfo(protocol)
    local dzb_menber_info_list = {}
    local can_join_knockout = false
    local uuid = RoleWGData.Instance:GetUUid()
    local my_konckout_round_cache = {}

    for i = 1, protocol.count do
        local data = protocol.team[i]
        
        -- 未处理头像功能 添加默认头像数据
        data.default_uuid = uuid
        data.default_sex = 1 -- math.random(0, 1)
        data.default_prof = 1 -- math.random(GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_4)

        if not IsEmptyTable(data) then
            dzb_menber_info_list[data.round] = dzb_menber_info_list[data.round] or {}
            dzb_menber_info_list[data.round][data.idx] = data

            if uuid == data.uuid1 or  uuid == data.uuid2 then
                can_join_knockout = true
                my_konckout_round_cache[data.round] = data
            end
        end
    end

    self.can_join_knockout = can_join_knockout
    self.my_konckout_round_cache = my_konckout_round_cache
    self.dzb_menber_info_list = dzb_menber_info_list    -- 轮次  idx 缓存

    -- 计算冠军信息 后端多发一轮 最后一轮表示冠军信息
    self.dzb_menber_winner_info = (self.dzb_menber_info_list[4] or {})[0] or {}
    local is_knockout_winner = false
    if not IsEmptyTable(self.dzb_menber_winner_info) then
        if uuid == self.dzb_menber_winner_info.uuid1 or uuid == self.dzb_menber_winner_info.uuid2 then
            is_knockout_winner = true
        end
    end

    self.is_knockout_winner = is_knockout_winner

    -- 4 0 ->  (3 0  3 1)
    -- 3 0  (2 0  21)      3 1   (22 23)
    -- 2 0  (10  11)  21(12 13)  22(14  15)  23(16  17)
    -- 1 0 (00  01)  11 (02 03)  12(04  05)  13(06  07)  14(08  09)  15(10 011) 16(012 013) 17(014 015)

    -- 计算出胜利信息
    local cal_dzb_menber_result_info = {}
    for i = 1, 4 do  -- round 
        for j = 1, LoverPkWGData.COMPETIITION_SYSTEM_ORDER[i - 1] do   --idx
            local round = i - 1
            local idx = j - 1
    
            local info = (self.dzb_menber_info_list[round] or {})[idx] or {}
            local next_info = (self.dzb_menber_info_list[round + 1] or {})[math.floor(idx / 2)] or {}
               
            if IsEmptyTable(info) then
                cal_dzb_menber_result_info[round] = cal_dzb_menber_result_info[round] or {}
                cal_dzb_menber_result_info[round][idx] = {is_result = false, is_win = false}
            else
                if IsEmptyTable(next_info) then
                    cal_dzb_menber_result_info[round] = cal_dzb_menber_result_info[round] or {}
                    cal_dzb_menber_result_info[round][idx] = {is_result = false, is_win = false}
                else
                    local is_win = (info.uuid1 == next_info.uuid1 and info.uuid2 == next_info.uuid2) or (info.uuid1 == next_info.uuid2 and info.uuid2 == next_info.uuid1)
                    cal_dzb_menber_result_info[round] = cal_dzb_menber_result_info[round] or {}
                    cal_dzb_menber_result_info[round][idx] = {is_result = true, is_win = is_win}
                end
            end
       end
    end

    self.cal_dzb_menber_result_info = cal_dzb_menber_result_info
end

--[[
    时间线
    0轮进入最开始等待 +  0轮开始  + 0轮结束/1轮开始等待  + 1轮开始 + 1轮结束/2轮开始等待  + 2轮开始 + 2轮结束/3轮开始等待  + 3轮开始  + 3轮结束  
    knockout_round 当前赛段 --淘汰赛 0：16进8  1 8进4  2 4进2  3 2进1    4已经决出冠军   
]]
function LoverPkWGData:SetSCCross2v2KnockOutInfo(protocol)
    local knockout_round = protocol.knockout_round
    self.knockout_round = knockout_round     
    self.knockout_next_round_start_time = protocol.next_round_start_time
    self.knockout_cur_round_start_time = protocol.cur_round_start_time
end

-- 竞猜总数据 status-- 0->未竞猜, 1-> 已竞猜, 2-> 已领取奖励
function LoverPkWGData:SetSCCrossCouple2V2GuessListInfo(protocol)
    local dzb_guess_info_list = {}
    local guess_list = protocol.guess_list
    for k, v in pairs(guess_list) do
        dzb_guess_info_list[v.round] = dzb_guess_info_list[v.round] or {}
        dzb_guess_info_list[v.round][v.index] = v
    end

    self.dzb_guess_info_list = dzb_guess_info_list
    -- print_error("竞猜总数居", self.dzb_guess_info_list)
end

-- 竞猜单更
function LoverPkWGData:SetSCCrossCouple2V2GuessInfo(protocol)
    local data = protocol.single_guess_info
    self.dzb_guess_info_list[data.round] = self.dzb_guess_info_list[data.round] or {}
    self.dzb_guess_info_list[data.round][data.index] = data
end

function LoverPkWGData:SetSCCross2v2KnockOutPlayerRankInfo(protocol)
    -- print_error("淘汰赛个人排名信息  轮次 = ",protocol.round, "输赢 = ", protocol.is_lose, protocol.is_lose == 0 and "还没数" or "输了")
    self.konckout_player_rank_round = protocol.round
    self.konckout_is_lose = protocol.is_lose == 1
end

--------------------------------------------protocol_end----------------------------------------