PanaceaFurnaceView = PanaceaFurnaceView or BaseClass(SafeBaseView)

local rotate_time = 3   --旋转动画时间

function PanaceaFurnaceView:__init()
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/panacea_furnace_ui_prefab", "layout_panacea_furnace")
end

function PanaceaFurnaceView:OpenCallBack()
    PanaceaFurnaceWGCtrl.Instance:ReqPanaceaFurnaceInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.INFO)
end

function PanaceaFurnaceView:LoadCallBack()
    self.cur_show_index = 1

    if not self.score_reward_list then
        self.score_reward_list = AsyncListView.New(PanaceaFurnaceScoreRewardItem, self.node_list.score_reward_list)
    end

    if not self.preview_list then
        self.preview_list = PanaceaFurnaceScorePreViewList.New()
		self.preview_list:SetStartZeroIndex(false)
        self.preview_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.preview_list,
			assetBundle = "uis/view/panacea_furnace_ui_prefab", assetName = "lottery_item", itemRender = PanaceaFurnaceLotteryItem})
    end

    if not self.shop_list then
        self.shop_list = AsyncBaseGrid.New()
		self.shop_list:SetStartZeroIndex(false)
        self.shop_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.shop_list,
			assetBundle = "uis/view/panacea_furnace_ui_prefab", assetName = "panacea_furnace_shop_item", itemRender = PanaceaFurnaceShopItem})
    end

    for i = 1, 3 do
        XUI.AddClickEventListener(self.node_list["lottery_btn_" .. i], BindTool.Bind2(self.OnClickLotteryBtn, self, i))
        self.node_list["icon" .. i].button:AddClickListener(BindTool.Bind(self.ShowCostItemTips, self))
    end

	self.node_list["btn_ignore_ani"].button:AddClickListener(BindTool.Bind(self.AniOnClickJump, self))
    XUI.AddClickEventListener(self.node_list.lottery_toggle, BindTool.Bind(self.OnSelectToggle, self, 1))
    XUI.AddClickEventListener(self.node_list.shop_toggle, BindTool.Bind(self.OnSelectToggle, self, 2))
	XUI.AddClickEventListener(self.node_list["click_mask"], BindTool.Bind(self.OnClickMask, self)) --遮罩点击
	self.node_list["lucky_item_icon"].button:AddClickListener(BindTool.Bind(self.ShowLuckItemTips, self))
	XUI.AddClickEventListener(self.node_list.probability_show_btn, BindTool.Bind(self.OpenGaiLvView, self))

	self.node_list["click_mask"]:SetActive(false)
end

function PanaceaFurnaceView:ReleaseCallBack()
    self:CleanTimer()

    if self.score_reward_list then
        self.score_reward_list:DeleteMe()
        self.score_reward_list = nil
    end

    if self.preview_list then
        self.preview_list:DeleteMe()
        self.preview_list = nil
    end

    if self.shop_list then
        self.shop_list:DeleteMe()
        self.shop_list = nil
    end

    if self.tween then
		self.tween:Kill()
		self.tween = nil
    end
end

function PanaceaFurnaceView:ShowIndexCallBack()
    self:FlushTimeCount()
    self:FlushLotteryIcon()
end

function PanaceaFurnaceView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushTimesReward()
			self:FlushPanelDraw()
			self:FlushPanelConvert()
			self:FlushAniStatus()
        elseif k == "play_ani" then
            self:PlayAnimation()
        end
    end

    self:ChangeShowPanel()
end

function PanaceaFurnaceView:PlayAnimation()
	local is_jump_ani = PanaceaFurnaceWGData.Instance:GetJumpAni()
	if not is_jump_ani then
		self.node_list["click_mask"]:SetActive(true)

	   	self.tween = DG.Tweening.DOTween.Sequence()
        local tween_rotate = self.node_list.arrow.transform:DORotate(Vector3(0, 0, -360 * rotate_time),
        rotate_time, DG.Tweening.RotateMode.FastBeyond360)
        tween_rotate:SetEase(DG.Tweening.Ease.OutCubic)
        self.tween:Append(tween_rotate)
        self.tween:OnUpdate(function()
            local rotate_z = self.node_list.arrow.transform.localEulerAngles.z
            local tween_idx = self:GetCurPointIndex(rotate_z)
            self.preview_list:ShowHlLight(tween_idx)
        end)
        self.tween:OnComplete(function()
            self.preview_list:ShowHlLight(0)
        	self.node_list["click_mask"]:SetActive(false)
            self.tween:Kill()
            self.tween = nil
        end)
	end
end

function PanaceaFurnaceView:GetCurPointIndex(rotate_z)
    local rotate = 360 / 16
    return 16 - math.floor((rotate_z / rotate))
end

--刷新次数奖励
function PanaceaFurnaceView:FlushTimesReward()
    local remind = PanaceaFurnaceWGData.Instance:ShowPanaceaFurnaceRemind()
    self.node_list.lottery_toggle_remind:SetActive(remind > 0)
	local list = PanaceaFurnaceWGData.Instance:GetAllTimesRewardInfo()
	self.score_reward_list:SetDataList(list)
	local draw_times = PanaceaFurnaceWGData.Instance:GetDrawTimes()
	self.node_list.score_num.text.text = draw_times
end

--刷新抽奖界面
function PanaceaFurnaceView:FlushPanelDraw()
	local show_list = PanaceaFurnaceWGData.Instance:GetShowRewardPool()
    self.preview_list:SetDataList(show_list)

	local luck_value = PanaceaFurnaceWGData.Instance:GetLuckyValue()
	local grade = PanaceaFurnaceWGData.Instance:GetCurGrade()
	local baodi_cfg = PanaceaFurnaceWGData.Instance:GetBaoDiCfgByGrade(grade)
	if baodi_cfg then
        local progress = luck_value / baodi_cfg.need_lucky
		self.node_list.refine_progress.image.fillAmount = progress
        self.node_list.pro_txt.text.text = string.format(Language.PanaceaFurnace.Progress, math.floor(progress * 100))
	end

	self:FlushBtnInfoShow()
end

--刷新兑换界面
function PanaceaFurnaceView:FlushPanelConvert()
	local convert_list = PanaceaFurnaceWGData.Instance:GetShowConvertData()
	self.shop_list:SetDataList(convert_list)

	local grade = PanaceaFurnaceWGData.Instance:GetCurGrade()
	local baodi_cfg = PanaceaFurnaceWGData.Instance:GetBaoDiCfgByGrade(grade)
	if baodi_cfg then
		self.node_list.lucky_item_icon.image:LoadSprite(ResPath.GetItem(baodi_cfg.item.item_id))
		local num = ItemWGData.Instance:GetItemNumInBagById(baodi_cfg.item.item_id)
		self.node_list.lucky_item_num.text.text = num
	end
end

function PanaceaFurnaceView:OnItemDataChange()
    local remind = PanaceaFurnaceWGData.Instance:ShowPanaceaFurnaceRemind()
    self.node_list.lottery_toggle_remind:SetActive(remind > 0)
    self:FlushBtnInfoShow()
    self:FlushPanelConvert()
end

function PanaceaFurnaceView:FlushTimeCount()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_PANACEA_FURNACE)
    if time > 0 then
        CountDownManager.Instance:AddCountDown("panacea_furnace_act_timer",
            BindTool.Bind(self.UpdateCountDown, self),
            BindTool.Bind(self.UpdateCountDown, self, 0, 0),
            nil, time, 1)
    else
        self:CleanTimer()
        self:UpdateCountDown(0, 0)
    end
end

--刷新按钮信息
function PanaceaFurnaceView:FlushBtnInfoShow()
	local other_cfg = PanaceaFurnaceWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if IsEmptyTable(item_cfg) then
    	return
    end

    for i = 1, 3 do
        local mode_cfg = PanaceaFurnaceWGData.Instance:GetModeCfgByMode(i)
        if mode_cfg then
            local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
            local color = item_count >= mode_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
            local num_text = item_count .. "/" .. mode_cfg.cost_item_num
            self.node_list["money_num_" .. i].text.text = ToColorStr(num_text, color)
            self.node_list["lottery_btn_remind_" .. i]:SetActive(item_count >= mode_cfg.cost_item_num)
        end
    end
end

function PanaceaFurnaceView:FlushLotteryIcon()
    local other_cfg = PanaceaFurnaceWGData.Instance:GetOtherCfg()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
    if IsEmptyTable(item_cfg) then
    	return
    end

    for i = 1, 3 do
        self.node_list["icon" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.id))
    end
end

function PanaceaFurnaceView:OnClickMask()
    TipWGCtrl.Instance:ShowSystemMsg(Language.GloryCrystal.IsPlayingTrun)
end

function PanaceaFurnaceView:CleanTimer()
	if CountDownManager.Instance:HasCountDown("panacea_furnace_act_timer") then
		CountDownManager.Instance:RemoveCountDown("panacea_furnace_act_timer")
	end
end

function PanaceaFurnaceView:UpdateCountDown(elapse_time, total_time)
	local act_time = math.ceil(total_time - elapse_time)
	if self.node_list and self.node_list.act_timer then
        if act_time > 0 then
            self.node_list.act_timer.text.text = string.format(Language.PanaceaFurnace.ActTimer, TimeUtil.FormatSecondDHM2(act_time))
        else
            self.node_list.act_timer.text.text = Language.PanaceaFurnace.TimeOver
        end
	end
end

function PanaceaFurnaceView:ChangeShowPanel()
    self.node_list.lottery_panel:SetActive(self.cur_show_index == 1)
    self.node_list.shop_panel:SetActive(self.cur_show_index == 2)
end

function PanaceaFurnaceView:OnSelectToggle(index)
    if index == self.cur_show_index then
        return
    end

    self.cur_show_index = index
    self:ChangeShowPanel()
    self:FlushPanelDraw()
	self:FlushPanelConvert()
end

function PanaceaFurnaceView:OnClickLotteryBtn(mode_type)
    PanaceaFurnaceWGCtrl.Instance:ClickUse(mode_type)
end

function PanaceaFurnaceView:RewardListShow(protocol)
	local again_func = function ()
		self:OperateBtn(self.operate_mode_id)
	end

	local other_info = {}
	other_info.again_text = Language.TreasureHunt.BtnText[self.operate_mode_id + 1]

	local base_cfg = ControlBeastsContractWGData.Instance:GetBaseCfg()
	if base_cfg then
		other_info.stuff_id = base_cfg.cost_item_id
		local mode_cfg = ControlBeastsContractWGData.Instance:GetCurBeastDrawMode(self.operate_mode_id)
		other_info.times = mode_cfg and mode_cfg.cost_item_num or 0
		other_info.spend = base_cfg.cost_gold
	end

	TipWGCtrl.Instance:ShowGetCommonReward(protocol.item_list, again_func, other_info, false)
end

--跳过抽奖动画按钮
function PanaceaFurnaceView:AniOnClickJump()
	PanaceaFurnaceWGData.Instance:SetJumpAni()
	self:FlushAniStatus()
end

--跳过动画按钮状态
function PanaceaFurnaceView:FlushAniStatus()
	local is_jump_ani = PanaceaFurnaceWGData.Instance:GetJumpAni()
	self.node_list["ignore_img"]:SetActive(is_jump_ani)
end

function PanaceaFurnaceView:ShowCostItemTips()
    local other_cfg = PanaceaFurnaceWGData.Instance:GetOtherCfg()
    local item_id = other_cfg.cost_item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function PanaceaFurnaceView:ShowLuckItemTips()
   	local grade = PanaceaFurnaceWGData.Instance:GetCurGrade()
	local baodi_cfg = PanaceaFurnaceWGData.Instance:GetBaoDiCfgByGrade(grade)
	if baodi_cfg then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = baodi_cfg.item.item_id})
	end
end

function PanaceaFurnaceView:OpenGaiLvView()
    local info = PanaceaFurnaceWGData.Instance:GetGaiLvInfo()
    TipWGCtrl.Instance:OpenTipsRewardProView(info)
    -- TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

---------------预览抽奖物品列表---------------
PanaceaFurnaceScorePreViewList = PanaceaFurnaceScorePreViewList or BaseClass(AsyncBaseGrid)
function PanaceaFurnaceScorePreViewList:ShowHlLight(hl_index)
    local cell_list = self:GetAllCell()
    for k, v in pairs(cell_list) do
        v:ShowHlImg(hl_index)
	end
end

---------------预览抽奖物品---------------
PanaceaFurnaceLotteryItem = PanaceaFurnaceLotteryItem or BaseClass(BaseRender)
function PanaceaFurnaceLotteryItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
end

function PanaceaFurnaceLotteryItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function PanaceaFurnaceLotteryItem:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData(self.data.item)
    self.node_list.name_txt.text.text = ItemWGData.Instance:GetItemName(self.data.item.item_id)
end

function PanaceaFurnaceLotteryItem:ShowHlImg(hl_index)
    local is_show = false
    if hl_index then
        is_show = self.index == hl_index
    end

    self.node_list.hl_img:SetActive(is_show)
end

---------------累积积分奖励item---------------
PanaceaFurnaceScoreRewardItem = PanaceaFurnaceScoreRewardItem or BaseClass(BaseRender)
function PanaceaFurnaceScoreRewardItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
	self.node_list["click_get_btn"].button:AddClickListener(BindTool.Bind(self.ClickGetReward, self))
end

function PanaceaFurnaceScoreRewardItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function PanaceaFurnaceScoreRewardItem:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData(self.data.item)
    self.node_list.score.text.text = self.data.need_draw_times
    self.node_list.click_get_btn:SetActive(self.data.state == REWARD_STATE_TYPE.CAN_FETCH)
    self.item_cell:SetLingQuVisible(self.data.state == REWARD_STATE_TYPE.FINISH)
end

function PanaceaFurnaceScoreRewardItem:ClickGetReward()
	if not self.data then
        return
    end

    if self.data.state == REWARD_STATE_TYPE.CAN_FETCH then
    	PanaceaFurnaceWGCtrl.Instance:ReqPanaceaFurnaceInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.FETCH_TIMES_REWARD, self.data.seq)
    end
end

---------------兑换商品item---------------
PanaceaFurnaceShopItem = PanaceaFurnaceShopItem or BaseClass(BaseRender)
function PanaceaFurnaceShopItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.icon, BindTool.Bind(self.ShowStuffItemTips, self))
end

function PanaceaFurnaceShopItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function PanaceaFurnaceShopItem:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData(self.data.item)
    self.node_list.name.text.text = ItemWGData.Instance:GetItemName(self.data.item.item_id)
    self.node_list.cost_num.text.text = self.data.stuff_num_1

    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(self.data.stuff_id_1)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    local is_buy_done = self.data.cur_convert_times >= self.data.time_limit
    local color = is_buy_done and COLOR3B.RED or COLOR3B.DEFAULT_NUM
    self.node_list.limit_count.text.text = string.format(Language.PanaceaFurnace.LimitCount, color, self.data.cur_convert_times, self.data.time_limit)
    self.node_list.buy_flag:SetActive(is_buy_done)
    self.node_list.buy_btn:SetActive(not is_buy_done)
end

function PanaceaFurnaceShopItem:OnClickBuyBtn()
	if not self.data then
		return
	end

	if self.data.cur_convert_times >= self.data.time_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GloryCrystal.MaxNumConvert)
		return
	end

	local item_count = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id_1)
	if item_count < self.data.stuff_num_1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GloryCrystal.StuffNotEnough)
		return
	end

	PanaceaFurnaceWGCtrl.Instance:ReqPanaceaFurnaceInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.CONVERT, self.data.seq)
end

function PanaceaFurnaceShopItem:ShowStuffItemTips()
	if not self.data then
		return
	end

    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.stuff_id_1}) 
end