FiveElementsView = FiveElementsView or BaseClass(SafeBaseView)

function FiveElementsView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self.default_index = TabIndex.five_elements_overview
	self.view_name = GuideModuleName.FiveElementsView
	local bundle_name = "uis/view/five_elements_ui_prefab"
	--self:AddViewResource(0, bundle_name, "five_elements_bg")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(TabIndex.five_elements_talent, bundle_name, "laout_five_elements_talent")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.five_elements_overview, bundle_name, "five_elements_preview")
	self:AddViewResource(TabIndex.five_elements_knapsack, bundle_name, "five_elements_knapsack")
	self:AddViewResource(TabIndex.five_elements_cangming, bundle_name, "layout_five_elements_cangming")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
	self.tab_sub = {}
	self.remind_tab = {
		{ RemindName.FiveElement_Overview },
		{ RemindName.FiveElement_Knapsack },
		{ RemindName.FiveElement_Talent },
		{ RemindName.FiveElement_CangMing },
	}
end

function FiveElementsView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:OverviewReleaseCallBack()
	self:KnapsackViewReleaseCallBack()
	self:TalentViewReleaseCallBack()
	self:CangMingViewReleaseCallBack()
end

function FiveElementsView:OpenCallBack()
	-- 进入游戏时数据已下发
	--FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.INFO)

	if nil ~= self.tabbar then
		self.tabbar:AddAllListen()
	end
end

function FiveElementsView:CloseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:UnAllListen()
	end
end

function FiveElementsView:LoadIndexCallBack(index)
	if index == TabIndex.five_elements_overview then
		self:InitFiveElementOverview()
	elseif index == TabIndex.five_elements_knapsack then
		self:InitFiveElementKnapsackView()
	elseif index == TabIndex.five_elements_talent then
		self:InitFiveElementTalentView()
	elseif index == TabIndex.five_elements_cangming then
		self:InitFiveElementCangMingView()
	end
end

function FiveElementsView:ShowIndexCallBack(index)
	if index == TabIndex.five_elements_overview then
		self:ShowFiveElementOverview()
	elseif index == TabIndex.five_elements_knapsack then
		self:ShowFiveElementKnapsackView()
	elseif index == TabIndex.five_elements_talent then
		self:ShowFiveElementTalentView()
	elseif index == TabIndex.five_elements_cangming then
		self:ShowFiveElementCangMingView()
	end
end

function FiveElementsView:ChangeFiveElementsViewBg(name)
	local bundle, asset = ResPath.GetRawImagesJPG(name)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function FiveElementsView:LoadCallBack()
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:Init(Language.FiveElements.TabGrop, nil, nil, nil, self.remind_tab)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.FiveElementsView, self.tabbar)
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function FiveElementsView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.five_elements_overview then
				self:OnFlushFiveElementOverview()
			elseif index == TabIndex.five_elements_knapsack then
				self:OnFlushFiveElementKnapsackView(param_t, index)
			elseif index == TabIndex.five_elements_talent then
				self:OnFlushTalent(param_t, index)
			elseif index == TabIndex.five_elements_cangming then
				self:OnFlushCangMing(param_t, index)
			end
		elseif k == "KnapsackItemChangeFlush" then
			self:KnapsackItemChangeFlush()
		elseif k == "OverviewItemChangeFlush" then
			self:OverviewItemChangeFlush()
		elseif k == "Part_cell_Change" then
			if index == TabIndex.five_elements_overview then
				self:OnFlushFiveElementOverview()
			elseif index == TabIndex.five_elements_knapsack then
				self:OnFlushFiveElementKnapsackView(param_t, index)
			end
		elseif k == "BagChange" then
			self:OnFlushFiveElementKnapsackView(param_t, index)
		elseif k == "UpdateSkill" then
			self:UpdateSkill()
		elseif k == "flush_use_waist_light" then
			self:OnFlushTypeList()
			self:FlushCangMingInfoPanel()
		end
	end
end
