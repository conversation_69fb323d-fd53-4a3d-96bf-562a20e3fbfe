--[[
	时间函数库
	常用时间格式
	%a abbreviated weekday name (e.g., Wed)
	%A full weekday name (e.g., Wednesday)
	%b abbreviated month name (e.g., Sep)
	%B full month name (e.g., September)
	%c date and time (e.g., 09/16/98 23:48:10)
	%d day of the month (16) [01-31]
	%H hour, using a 24-hour clock (23) [00-23]
	%I hour, using a 12-hour clock (11) [01-12]
	%M minute (48) [00-59]
	%m month (09) [01-12]
	%p either "am" or "pm" (pm)
	%S second (10) [00-61]
	%w weekday (3) [0-6 = Sunday-Saturday]
	%x date (e.g., 09/16/98)
	%X time (e.g., 23:48:10)
	%Y full year (1998)
	%y two-digit year (98) [00-99]
	%% the character '%'
	用法：
	os.date("%x",os.time()) --> 07/29/2014
	os.date("%Y-%m-%d", os.time()) --> 2014-07-29
]]

TimeUtil = TimeUtil or {}
----[[时区修改
-- 服务器时区
GlobalServerTimeZone = 3600 * 8
local mLocalTimeZone = 3600 * 8
local mTimeZoneDiff = 0
local old_os_date = os.date
local old_os_time = os.time

-- 获取时区
function TimeUtil.GetLocalTimeZoneAndDiff()
	local now = old_os_time()
	mLocalTimeZone = os.difftime(now, old_os_time(old_os_date("!*t", now)))
	local is_dst = old_os_date("*t", now).isdst
	-- 夏令时
	if is_dst then
		mLocalTimeZone = mLocalTimeZone + 3600
	end

	mTimeZoneDiff = GlobalServerTimeZone - mLocalTimeZone
	return mLocalTimeZone, mTimeZoneDiff
end

TimeUtil.GetLocalTimeZoneAndDiff()

-- 重写os.date函数，忽略本地时区设置，按服务器时区格式化时间
-- @param format: 同os.date第一个参数
-- @param timestamp:服务器时间戳
os.date = function (format, timestamp)
			if timestamp then
				timestamp = timestamp + mTimeZoneDiff
			end

		    return old_os_date(format, timestamp)
		end

-- 重写os.time函数，忽略本地时区设置，返回服务器时区时间戳
-- @param timedata: 服务器时区timedate
os.time = function (timedate)
			local timestamp = old_os_time(timedate)
			if timestamp then
				return timestamp - mTimeZoneDiff
			end

			return timestamp
		end
--]]



function TimeUtil.Format2TableDHM(time)
	local time_tab = {day = 0, hour = 0, min = 0}
	if time > 0 then
		time_tab.day = math.floor(time / (60 * 60 * 24))
		time_tab.hour = math.floor((time / (60 * 60)) % 24)
		time_tab.min = math.floor((time / 60) % 60)
	end

	return time_tab
end

function TimeUtil.Format2TableDHM2(time)
	local time_tab = {day = 0, hour = 0, min = 0, sec = 0}
	if time > 0 then
		time_tab.day = math.floor(time / (60 * 60 * 24))
		time_tab.hour = math.floor((time / (60 * 60)) % 24)
		time_tab.min = math.floor((time / 60) % 60)
		time_tab.sec = math.floor(time % 60)
	end

	return time_tab
end

function TimeUtil.Format2TableDHM3(time)
	local time_tab = {day = 0, hour = 0, min = 0, sec = 0, milliseccond = 0}
	if time > 0 then
		time_tab.day = math.floor(time / (60 * 60 * 24))
		time_tab.hour = math.floor((time / (60 * 60)) % 24)
		time_tab.min = math.floor((time / 60) % 60)
		time_tab.sec = math.floor(time % 60)
		local _, milliseccond = math.modf(time)
		time_tab.milliseccond = milliseccond * 1000
	end

	return time_tab
end

function TimeUtil.Format2TableDHMS(time)
	local time_tab = {day = 0, hour = 0, min = 0, s = 0}
	if time > 0 then
		time_tab.day = math.floor(time / (60 * 60 * 24))
		time_tab.hour = math.floor((time / (60 * 60)) % 24)
		time_tab.min = math.floor((time / 60) % 60)
		time_tab.s = math.floor(time % 60)
	end

	return time_tab
end

function TimeUtil.FormatUnixTime2Date(unixTime)
    if unixTime and unixTime >= 0 then
        local tb = {}
        tb.year = tonumber(os.date("%Y", unixTime))
        tb.month =tonumber(os.date("%m", unixTime))
        tb.day = tonumber(os.date("%d", unixTime))
        tb.hour = tonumber(os.date("%H", unixTime))
        tb.minute = tonumber(os.date("%M", unixTime))
        tb.second = tonumber(os.date("%S", unixTime))
        return tb
    end
end

--1800 转成当天时间戳
function TimeUtil.FormatCfgTimestamp(cfg_time)
    local server_time = TimeWGCtrl.Instance:GetServerTime()
	if nil == cfg_time or not tonumber(cfg_time) then
		return 0
    end
    local time_num = tonumber(cfg_time)
    local tab = os.date("*t", server_time)
    tab.day = tab.day
    tab.hour = time_num / 100
    tab.min = time_num % 100
    tab.sec = 0
    return os.time(tab)
end

-- 2	分：秒
-- 4	秒
-- 其他	时：分：秒
function TimeUtil.FormatSecond(time, model)
	local s = ""
	if time > 0 then
		local hour = math.floor(time / 3600)
		local minute = math.floor((time / 60) % 60)
		local second = math.floor(time % 60)
		if 2 == model then
			s = string.format("%02d:%02d", minute, second)
		elseif 4 == model then
			s = string.format("%02d", second)
		else
			s = string.format("%02d:%02d:%02d", hour, minute, second)
		end
	else
		if 2 == model then
			s = string.format("%02d:%02d", 0, 0)
		elseif 3 == model then
			s = string.format("%02d:%02d:%02d", 0, 0, 0)
		end
	end

	return s
end

--天时分
--时分
function TimeUtil.FormatSecondDHM( time )
	local time_tab = TimeUtil.Format2TableDHM(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
	else
		return string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
	end
end

--天时分
--时分秒
--分秒
--秒
function TimeUtil.FormatSecondDHM2( time )
	local time_tab = TimeUtil.Format2TableDHM2(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
	elseif time_tab.hour > 0 then
		return string.format(Language.Common.TimeStr3, time_tab.hour, time_tab.min, time_tab.sec)
	elseif time_tab.min > 0 then
		return string.format(Language.Common.TimeStr5, time_tab.min, time_tab.sec)
	else
		return string.format(Language.Common.TimeStr8, time_tab.sec)
	end
end

--天
--时分秒
--分秒
--秒
function TimeUtil.FormatSecondDHM3( time )
	local time_tab = TimeUtil.Format2TableDHM2(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr7, time_tab.day)
	elseif time_tab.hour > 0 then
		return string.format(Language.Common.TimeStr3, time_tab.hour, time_tab.min, time_tab.sec)
	elseif time_tab.min > 0 then
		return string.format(Language.Common.TimeStr5, time_tab.min, time_tab.sec)
	else
		return string.format(Language.Common.TimeStr8, time_tab.sec)
	end
end

--日:时:分:秒
--时:分:秒
--分:秒
function TimeUtil.FormatSecondDHM4(time )
	if nil == time then
		return ""
	end
	local time_tab = TimeUtil.Format2TableDHM2(time)
	if nil == time_tab then
		return ""
	end
	
	local day = time_tab.day
	local hour = time_tab.hour
	local minute = time_tab.min
	local second = time_tab.sec
	if time_tab.day > 0 then
		return string.format("%02d:%02d:%02d:%02d", day, hour, minute, second)
	elseif time_tab.hour > 0 then
		return string.format("%02d:%02d:%02d", hour, minute, second)
	else
		return string.format("%02d:%02d", minute, second)
	end
end

--天
--时:分:秒
function TimeUtil.FormatSecondDHM5( time )
	if time > 0 then
		local time_tab = TimeUtil.Format2TableDHM2(time)
		if time_tab.day > 0 then
			return string.format(Language.Common.TimeStr7, time_tab.day)
		else
			return string.format("%02d:%02d:%02d", time_tab.hour, time_tab.min, time_tab.sec)
		end
	else
		return string.format("%02d:%02d:%02d", 0, 0, 0)
	end
end

--0天00:00:00
--时:分:秒
function TimeUtil.FormatSecondDHM6( time )
	if time > 0 then
		local time_tab = TimeUtil.Format2TableDHM2(time)
		if time_tab.day > 0 then
			return string.format(Language.Common.TimeStr9, time_tab.day,time_tab.hour, time_tab.min, time_tab.sec)
		else
			return string.format("%02d:%02d:%02d", time_tab.hour, time_tab.min, time_tab.sec)
		end
	else
		return string.format("%02d:%02d:%02d", 0, 0, 0)
	end
end

--天时分
--时分
--分
function TimeUtil.FormatSecondDHM7(time)
	local time_tab = TimeUtil.Format2TableDHM2(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
	elseif time_tab.hour > 0 then
		return string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
	else
		return string.format(Language.Common.TimeStr6, time_tab.min)
	end
end

--天时分秒
--时分秒
--分秒
--秒
function TimeUtil.FormatSecondDHM8( time )
	local time_tab = TimeUtil.Format2TableDHM2(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr2, time_tab.day, time_tab.hour, time_tab.min, time_tab.sec)
	elseif time_tab.hour > 0 then
		return string.format(Language.Common.TimeStr3, time_tab.hour, time_tab.min, time_tab.sec)
	elseif time_tab.min > 0 then
		return string.format(Language.Common.TimeStr5, time_tab.min, time_tab.sec)
	else
		return string.format(Language.Common.TimeStr8, time_tab.sec)
	end
end


-- 0天00:00:00
-- 00:00:00
-- 00:00
function TimeUtil.FormatSecondDHM9(time)
	if time and time > 0 then
		local time_tab = TimeUtil.Format2TableDHM2(time)
		if time_tab.day > 0 then
			return string.format(Language.Common.TimeStr9, time_tab.day, time_tab.hour, time_tab.min, time_tab.sec)
		elseif time_tab.hour > 0 then
			return string.format("%02d:%02d:%02d", time_tab.hour, time_tab.min, time_tab.sec)
		else
			return string.format("%02d:%02d", time_tab.min, time_tab.sec)
		end
	else
		return string.format("%02d:%02d", 0, 0)
	end
end

function TimeUtil.FormatSecondDHMSMS(time)
	if time and time > 0 then
		local time_tab = TimeUtil.Format2TableDHM3(time)
		if time_tab.day > 0 then
			return string.format(Language.Common.TimeStr9, time_tab.day, time_tab.hour, time_tab.min, time_tab.sec)
		elseif time_tab.hour > 0 then
			return string.format("%02d:%02d:%02d", time_tab.hour, time_tab.min, time_tab.sec)
		elseif time_tab.min > 0 then
			return string.format("%02d:%02d", time_tab.min, time_tab.sec)
		else
			return string.format("%02d:%02d:%02d", time_tab.min, time_tab.sec, time_tab.milliseccond / 10)
		end
	else
		return string.format("%02d:%02d", 0, 0)
	end
end

-- 天时分秒
function TimeUtil.FormatSecond2DHMS(time)
	local time_tab = TimeUtil.Format2TableDHMS(time)
	local str_list = Language.Common.TimeList

	return string.format("%02d%s%02d%s%02d%s%02d%s",
						time_tab.day, str_list.d,
						time_tab.hour, str_list.h,
						time_tab.min, str_list.min,
						time_tab.s, str_list.s)
end


-- 时：分：秒
function TimeUtil.FormatSecond2HMS(time)
	local s = TimeUtil.FormatSecond(time, 3)

	return s
end

-- 分：秒
function TimeUtil.FormatSecond2MS(time)
	local s = TimeUtil.FormatSecond(time, 2)

	return s
end

-- 时：分：秒
function TimeUtil.FormatTable2HMS(time_tab)
	if nil == time_tab then
		return ""
	end

	local hour = time_tab.hour
	local minute = time_tab.min
	local second = time_tab.sec or time_tab.s

	return string.format("%02d:%02d:%02d", hour, minute, second)
end

--天时
--时分
--分秒
--秒
function TimeUtil.FormatTimeLanguage2(time)
	if time <= 0 then
		return ""
	end

	local time_tab = TimeUtil.Format2TableDHMS(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr4, time_tab.day, time_tab.hour)
	elseif time_tab.hour > 0 then
		return string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
	elseif time_tab.min > 0 then
		return string.format(Language.Common.TimeStr5, time_tab.min, time_tab.s)
	else
		return string.format(Language.Common.TimeStr8, time_tab.s)
	end
end

--天
--时
--分
--秒
function TimeUtil.FormatTimeLanguage3(time)
	if time <= 0 then
		return ""
	end

	local time_tab = TimeUtil.Format2TableDHMS(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr7, time_tab.day)
	elseif time_tab.hour > 0 then
		return string.format(Language.Common.TimeStr13, time_tab.hour)
	elseif time_tab.min > 0 then
		return string.format(Language.Common.TimeStr6, time_tab.min)
	else
		return string.format(Language.Common.TimeStr8, time_tab.s)
	end
end

-- 0月0日00:00:00
function TimeUtil.FormatSecond2MYHM(time)
	if nil == time then
		return ""
	end

	local time_str = ""
	local str = os.date("*t", time)
	time_str = string.format(Language.OpenServer.TimeTip, str.month, str.day, str.hour, str.min)
	return time_str
end

-- 0月0日
function TimeUtil.FormatSecond2MY(time)
	if nil == time then
		return ""
	end

	local time_str = ""
	local str = os.date("*t", time)
	time_str = string.format(Language.OpenServer.TimeTip1, str.month, str.day)
	return time_str
end

-- 月/日/时/分
function TimeUtil.FormatSecond2MYHM1(time)
	if nil == time then
		return ""
	end

	local time_str = ""
	local str = os.date("*t", time)
	time_str = string.format(Language.OpenServer.TimeTip2, str.month, str.day, str.hour, str.min)
	return time_str
end

--获取当前是周几
function TimeUtil.FormatSecond3MYHM1(time)
	if nil == time then
		return 0
	end

	local week_day = 0
	local str = os.date("*t", time)
	week_day = str.wday
	if week_day == 1 then --1表示周日
		week_day = 7
	else
		week_day = week_day - 1
	end

	return week_day
end


--获取年月日
function TimeUtil.FormatSecond5MYHM1(time)
	if nil == time then
		return ""
	end

	local str = os.date("*t", time)
	return str.year, str.month, str.day
end

--年/月/日
function TimeUtil.FormatYMD(time)
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local year = time_tab.year
	local month = time_tab.month
	local day = time_tab.day

	return string.format("%02d/%02d/%02d", year, month, day)
end

--月日时分秒
function TimeUtil.FormatMDHMS(time )
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local month = time_tab.month
	local day = time_tab.day
	local hour = time_tab.hour
	local minute = time_tab.min
	local second = time_tab.sec
	return string.format("%02d/%02d\u{00A0}%02d:%02d:%02d", month, day, hour, minute, second)
end

--返回格式：x月y日 24:59:59
function TimeUtil.FormatMDHMS2(time)
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local month = time_tab.month
	local day = time_tab.day
	local hour = time_tab.hour
	local minute = time_tab.min
	local second = time_tab.sec
	return string.format((Language.Common.TimeStr12 .. " %02d:%02d:%02d"), month, day, hour, minute, second)
end

--时:分:秒
function TimeUtil.FormatHMS(time )
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local hour = time_tab.hour
	local minute = time_tab.min
	local second = time_tab.sec
	return string.format("%02d:%02d:%02d", hour, minute, second)
end

-- 00:00
function TimeUtil.MSTime(time)
	local minute = math.fmod(math.floor(time/60), 60)
	if minute < 10 then
		minute = "0" .. minute
	end
	local second = math.fmod(time, 60)
	if second < 10 then
		second = "0" .. second
	end
	return string.format("%s:%s", minute, second)
end

--年-月-日 时:分:秒
--年/月/日 时:分:秒
function TimeUtil.FormatYMDHMS(time, not_forever)
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local year = time_tab.year
	local month = time_tab.month
	local day = time_tab.day
	local hour = time_tab.hour
	local minute = time_tab.min
	local second = time_tab.sec
	if not_forever then
		return string.format("%02d-%02d-%02d %02d:%02d:%02d", year, month, day, hour, minute, second)
	else
		return string.format("%02d/%02d/%02d %02d:%02d:%02d", year, month, day, hour, minute, second)
	end
end

--年月日时
function TimeUtil.FormatYMDH(time)
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local str_list = Language.Common.TimeList
	return string.format("%d%s%d%s%d%s%d%s",
						time_tab.year, str_list.y,
						time_tab.month, str_list.m,
						time_tab.day, str_list.d,
						time_tab.hour, str_list.h)
end

--年/月/日 时:分
function TimeUtil.FormatYMDHM(time)
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local year = time_tab.year
	local month = time_tab.month
	local day = time_tab.day
	local hour = time_tab.hour
	local minute = time_tab.min

	return string.format("%02d/%02d/%02d %02d:%02d", year, month, day, hour, minute)
end

-- 时:分
function TimeUtil.FormatHM(time)
	local time_tab = os.date("*t", time)
	return string.format("%02d:%02d", time_tab.hour, time_tab.min)
end

-- 获取当天剩余时间
function TimeUtil.GetTodayRestTime(time)
	local str_time = os.date("*t", time)
	local hour = 23 - str_time.hour
	local min = 59 - str_time.min
	local sec = 60 - str_time.sec
	return hour * 3600 + min * 60 + sec
end

-- 获取当天已过时间
function TimeUtil.GetTodayPassTime(time)
	local str_time = os.date("*t", time)
	return str_time.hour * 3600 + str_time.min * 60 + str_time.sec
end

-- 时分秒 清0
function TimeUtil.GetEarlyTime(time)
	if not time then
		return
	end

	local time_tab = os.date("*t", time)
	time_tab.hour = 0
	time_tab.min = 0
	time_tab.sec = 0
	return os.time(time_tab)
end

-- 返回这周剩余时间，时间段（周一 -- 周日）
function TimeUtil.GetRestTimeInWeek()
	local time = TimeWGCtrl.Instance:GetServerTime()
	if nil == time then
		return 0
	end

	local today_rest_time = TimeUtil.GetTodayRestTime(time)
	local today_in_week = TimeUtil.FormatSecond3MYHM1(time)
	local rest_time = (7 - today_in_week) * 86400 + today_rest_time

	return rest_time
end

--天时分,秒 （单独发）
function TimeUtil.FormatSecondDHMAndS( time )
	local time_tab = TimeUtil.Format2TableDHM2(time)
	return string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min), string.format(Language.Common.TimeStr8, time_tab.sec)
end

-- 天时分
-- 时分秒
function TimeUtil.FormatTimeDHMS(time)
	local time_tab = TimeUtil.Format2TableDHM2(time)
	if time_tab.day > 0 then
		return string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
	else
		return string.format(Language.Common.TimeStr3, time_tab.hour, time_tab.min, time_tab.sec)
	end
end

--获取 (天/时/分)前 其中一个,优先级:天>时>分(最低返还1分)
function TimeUtil.GetBeforeDHMOnOne(time_tab)
	if time_tab <= 0 then
		return ""
	end

	local t = TimeUtil.Format2TableDHMS(time_tab)
	if t.day > 0 then
		return string.format(Language.Common.BeforeXXDay, t.day)
	elseif t.hour > 0 then
		return string.format(Language.Common.BeforeXXHour, t.hour)
	elseif t.min > 0 then
		return string.format(Language.Common.BeforeXXMinute, t.min)
	else
		return string.format(Language.Common.BeforeXXMinute, 1)
	end
end

--获取 (天/时/分/秒)
function TimeUtil.GetTimeOneUnit(time_tab)
	if time_tab <= 0 then
		return ""
	end

	local t = TimeUtil.Format2TableDHMS(time_tab)
	local show_time = 0
	if t.day > 0 then
		show_time = t.hour > 0 and t.day + 1 or t.day
		return string.format(Language.Common.TimeStr7, show_time)
	elseif t.hour > 0 then
		show_time = t.min > 0 and t.hour + 1 or t.hour
		return string.format(Language.Common.TimeStr13, show_time)
	elseif t.min > 0 then
		show_time = t.s > 0 and t.min + 1 or t.min
		return string.format(Language.Common.TimeStr6, show_time)
	else
		return string.format(Language.Common.TimeStr8, t.s)
	end
end


--时：分：秒(天也算上,将天转成小时加上)
function TimeUtil.FormatDToHAndMS(time)
	if nil == time then
		return ""
	end

	local time_tab = TimeUtil.Format2TableDHM2(time)
	local day = time_tab.day
	local hour = time_tab.hour + day * 24
	local minute = time_tab.min
	local second = time_tab.sec
	return string.format("%02d:%02d:%02d", hour, minute, second)
end

--月日（大写）
function TimeUtil.FormarDaXieTimeYMD(time)
	if nil == time then
		return ""
	end

	local time_str = ""
	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local month = time_tab.month
	local day = time_tab.day

	local month_str = CommonDataManager.GetDaXie(month)
	local day_str = CommonDataManager.GetDaXie(day)
	time_str = string.format(Language.Common.TimeStr11, month_str, day_str)
	return time_str
end

-- time_str = "1130|1450"
-- split_symbol 分割符、separator 时间拼接符
function TimeUtil.ParsingTimeStr(time_str, split_symbol, separator)
    time_str = time_str or ""
    split_symbol = split_symbol or "|"
    separator = separator or "、"
    local time_list = Split(time_str, split_symbol)
    local server_time = os.date("*t", TimeWGCtrl.Instance:GetServerTime())

    local time = 0
    local time_data = {}
    local time_str_list = {}
    for k,v in ipairs(time_list) do
        local data = {}
        time = tonumber(v) or 0
        server_time.hour = math.floor(time / 100)
        server_time.min = time % 100
		server_time.sec = 0
        data.definite_time = os.time(server_time)
        local time_desc = os.date("%H:%M", data.definite_time)
        data.time_desc = time_desc

        table.insert(time_data, data)
        table.insert(time_str_list, time_desc)
    end

    local time_desc = ""
    if not IsEmptyTable(time_str_list) then
    	time_desc = table.concat(time_str_list, separator)
    end

    return time_desc, time_data
end

-- 获取当月天数
function TimeUtil.GetCurMonthDaysAmount()
	local year, month = os.date("%Y", TimeWGCtrl.Instance:GetServerTime()), os.date("%m", TimeWGCtrl.Instance:GetServerTime()) + 1
	return os.date("%d", os.time({year = year, month = month, day = 0}))
end

--[月-日][时:分]
function TimeUtil.FormatMDHM(time)
	if nil == time then
		return ""
	end

	local time_tab = os.date("*t", time)
	if nil == time_tab then
		return ""
	end

	local month = time_tab.month
	local day = time_tab.day
	local hour = time_tab.hour
	local minute = time_tab.min
	return string.format("[%02d-%02d][%02d:%02d]", month, day, hour, minute)
end