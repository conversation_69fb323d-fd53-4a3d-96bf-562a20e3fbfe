local MaxWidth = 1000  --列表最大宽度
local CellWidth = 100 --格子宽度
local CellSpace = 6  --格子间距

local StarPos = Vector3(0, 0, 0)

FuBenCommonWinView = FuBenCommonWinView or BaseClass(SafeBaseView)

function FuBenCommonWinView:__init()
	self.view_style = ViewStyle.Half
    self.view_name = "FuBenCommonWinView"
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_min_panel")
	-- self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_a3_commmon_tiaozhanjiesuan_panel")
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_common_win")

	-- 星星
	self.playing_index = 0
	self.star_num = 0
	--------------
	self.view_layer = UiLayer.Pop

	self.scene_type = 0
	self.fb_end_time = 0
	self.fb_exp = 0
	self.fb_gold = 0
	self.skill = 0
	self.reward_type = nil
	self.enter_next_flag = 0

	self.active_close = false
	self.data_list = {}
	self.is_use_mask = false
end

function FuBenCommonWinView:__delete()

end

function FuBenCommonWinView:ReleaseCallBack()
	if self.fb_cells then
		for i, v in ipairs(self.fb_cells) do
			v:DeleteMe()
		end
		self.fb_cells = {}
	end

	if self.lbl_record_num then
		self.lbl_record_num:DeleteMe()
		self.lbl_record_num = nil
	end

	if CountDownManager.Instance:HasCountDown("common_fb_close_timer") then
		CountDownManager.Instance:RemoveCountDown("common_fb_close_timer")
	end

    if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end

	self.scene_type = 0
	self.fb_end_time = 0
	self.fb_exp = 0
	self.fb_gold = 0
	self.star_num = 0
	self.skill = 0
	self.reward_type = nil
	self.enter_next_flag = 0

	self.data_list = nil
	self.star_img_list = nil
	if self.special_cell_list then
		self.special_cell_list:DeleteMe()
		self.special_cell_list = nil
	end

    if self.delay_play_star_quest then
        GlobalTimerQuest:CancelQuest(self.delay_play_star_quest)
        self.delay_play_star_quest = nil
    end

	if self.play_star_event then
		GlobalEventSystem:UnBind(self.play_star_event)
		self.play_star_event = nil
	end

	if self.rune_tower_head_cell then
		self.rune_tower_head_cell:DeleteMe()
		self.rune_tower_head_cell = nil
	end

	self.is_play_star = nil
	self.close_call_back = nil
end

function FuBenCommonWinView:LoadCallBack()
	self.star_img_list = {}
	self.fb_cells = {}
	self.cell_parent_list = {}

	self.node_list.layout_skill:SetActive(self.skill > 0)
	self.special_cell_list = AsyncListView.New(CommonListItemRender, self.node_list["layout_cells_list"])
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.OnClinkCloseHandler, self))
	self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClinkCloseHandler, self))
	self.node_list["btn_again"].button:AddClickListener(BindTool.Bind(self.OnClinkAgainHandler, self))
	self.node_list["btn_next_level"].button:AddClickListener(BindTool.Bind(self.OnClinkNextLevelHandler, self))
	self.node_list["btn_exit"].button:AddClickListener(BindTool.Bind(self.OnClinkCloseHandler, self))
	self.node_list["rune_tower_add_friend"].button:AddClickListener(BindTool.Bind(self.OnClickRuneAddFriend, self))

	self.node_list.layout_exp_money_tips:SetActive(false)
	self.node_list.bottom_parent:SetActive(false)
	self.node_list.Bound:SetActive(false)
	self.root_node.gameObject:SetActive(false)
	self.node_list.victory:SetActive(true)
end

function FuBenCommonWinView:OpenCallBack()
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ShengLi, nil, true))
end

function FuBenCommonWinView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("common_fb_close_timer") then
		CountDownManager.Instance:RemoveCountDown("common_fb_close_timer")
	end

    self.is_play_star = nil
    if Scene.Instance:GetSceneType() ~= SceneType.Common then
        if self.enter_next_flag <= 0 and Scene.Instance:GetSceneType() == self.scene_type  then
            FuBenWGCtrl.Instance:SendLeaveFB()
        else
            self.enter_next_flag = self.enter_next_flag - 1
        end
	end
    self.data_list = {}
	self.close_call_back = nil
end

function FuBenCommonWinView:ShowIndexCallBack()
	-- UITween.ShowCommonTiaoZhanJieSuanPanelTween(self, true, self.node_list.tween_info_root, nil)
	self.node_list["layout_result_2"]:SetActive(false)
	self.node_list["layout_result_1"]:SetActive(true)
	self.node_list["layout_result_3"]:SetActive(false)
	self.node_list["layer_saodang"]:SetActive(false)
	self.node_list["layout_star"]:SetActive(true)
	-- self.node_list["double_reward"]:SetActive(false)
	self.node_list["img_gold"]:SetActive(true)
	self.node_list["lbl_tips2_value"]:SetActive(true)
	self.node_list["xinmo_desc"]:SetActive(false)
	self.node_list.zhuzhan_content:SetActive(false)
	self.node_list.manhuanggudian:SetActive(false)
	self.node_list.transfer_text:SetActive(false)
	self.node_list.info_active_root:SetActive(true)
	self.node_list.exp_score_image:SetActive(false)
	self.node_list["drop_probability"]:SetActive(false)
	-- self.node_list["fuben_probability"]:SetActive(false)
	-- self.node_list["marry_xinyoulingxi"]:SetActive(false)
	self.node_list["lbl_sytitle_tips"]:SetActive(false)
	-- self.node_list["diff_icon"]:SetActive(false)
	self.node_list["lbl_sytitle_tips"].text.text = ""
	self.node_list["lbl_title_tips"].text.text = ""
	self.node_list.lbl_pass_time.text.text = ""
	-- self.node_list.fuben_probability.text.text = ""
	self.node_list["lbl_tips1_value"].text.text = CommonDataManager.ConverExp(self.fb_exp or 0)
	self.node_list["lbl_tips2_value"].text.text = self.fb_gold
	self.node_list["lbl_tips2_value"].rect.anchoredPosition = Vector2(142,58)
	self.node_list["btn_again"]:SetActive(false)
	self.node_list.exp_fb_special:SetActive(false)
	self.node_list.layout_country_map:SetActive(false)
	self.node_list.rune_tower_info:SetActive(false)
	self.node_list.spe_leader_img:SetActive(false)
	self.node_list["Bound"].rect.anchoredPosition = Vector2(0, 0)
	self.node_list["Bound"].rect.localScale = Vector3(1, 1, 1)


	if self.scene_type == SceneType.QingYuanFB then --情缘副本
		local marry_fb_info = MarryWGData.Instance:GetQyFbSceneInfo()
		local desc = ""
        local is_couple = marry_fb_info.is_couple == 1 --是情缘
        local is_helper = marry_fb_info.is_helper == 1 --是情缘
        if is_couple then
            -- self.node_list["marry_xinyoulingxi"]:SetActive(false)
            if marry_fb_info.is_same == 1 then
                desc = Language.FuBen.QingYuanFbTips1
            else
                desc = Language.FuBen.QingYuanFbTips2
            end
        elseif is_helper then
            desc = ""
        else
			desc = Language.FuBen.QingYuanFbTips3
		end
        self.node_list["lbl_title_tips"].rect.sizeDelta = Vector2(500, 25)
		self.node_list["lbl_title_tips"].text.text = desc
		self.node_list["layout_star"]:SetActive(false)
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list.Bound:SetActive(not is_helper)
		self.node_list.manhuanggudian:SetActive(is_helper)
	elseif  self.scene_type == SceneType.TEAM_BOOTYBAY_FB then --天帝陵
		self.node_list["layout_star"]:SetActive(false)
		local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
		local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes()
    	local remain_fb_times = other_cfg.team_fb_enter_times_limit - enter_teamfb_times
		local color = remain_fb_times > 0 and "#79fa82" or "#ff0000"
        self.node_list.lbl_title_tips.text.text = string.format(Language.BootyBay.EnterWabaoFBTimes, color, remain_fb_times, other_cfg.team_fb_enter_times_limit)

    elseif self.scene_type == SceneType.BOOTYBAY_FB then --单人天帝陵
        self.node_list["layout_star"]:SetActive(false)
        self.node_list.lbl_title_tips.text.text = Language.BootyBay.SingleFuBenSuccess
        local is_remain = BootyBayWGData.Instance:GetIsShowAgainBtn()
		self.node_list["layout_result_3"]:SetActive(is_remain)
        self.node_list["layout_result_1"]:SetActive(not is_remain)

	elseif self.scene_type == SceneType.BaGuaMiZhen_FB then --通天塔
		local layer = FuBenWGData.Instance:GetBaGuaMiZhenFBStatus()
		layer = layer == 0 and 1 or layer
		-- self.node_list.diff_icon:SetActive(true)
		-- local bundle, asset = ResPath.GetCommonIcon("a2_zjm_fb_nd" .. layer)
		-- self.node_list.diff_icon.image:LoadSprite(bundle, asset, function()
		-- 	self.node_list.diff_icon.image:SetNativeSize()
		-- end)
		local bgmz_cur_data_list = BaGuaMiZhenWGData.Instance:GetBossInfoCfg()
		local cur_data = bgmz_cur_data_list[layer]
		if cur_data and cur_data.name_fb then
			self.node_list.diff_desc.text.text = cur_data.name_fb
		end
		--难度显示改成副本名字
		-- self.node_list.diff_desc.text.text = Language.FuBen.FuBenDiffDesc[layer]
		local boss_name = BaGuaMiZhenWGData.Instance:GetCurBossName(layer)
		local desc =  string.format(Language.FuBen.BaGuaMiZhenWinDec,boss_name)
		self.node_list["lbl_title_tips"].text.text = Language.FuBen.BaGuaMiZhenWinDec1
		self.node_list["layout_star"]:SetActive(false)
        self.node_list.bottom_parent:SetActive(true)
        self.node_list["layout_result_2"]:SetActive(false)

        local is_remain = FuBenPanelWGData.Instance:IsFubenBaGuaRemaindTimes()
		self.node_list["layout_result_3"]:SetActive(is_remain)
        self.node_list["layout_result_1"]:SetActive(not is_remain)

    elseif self.scene_type == SceneType.COPPER_FB then --聚宝阁
		self.node_list["layout_result_2"]:SetActive(false)
        local is_remain = FuBenPanelWGData.Instance:IsTongBiRemaindTimes()
		local not_saodang_open = self.is_enter_next ~= false
		local show_3 = is_remain and not_saodang_open
		self.node_list["layout_result_3"]:SetActive(show_3)
        self.node_list["layout_result_1"]:SetActive(not show_3)

	elseif self.scene_type == SceneType.PET_FB or self.scene_type == SceneType.FakePetFb then --苍穹变
		FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_OTHER_INFO)

	elseif self.scene_type == SceneType.Wujinjitan or self.scene_type == SceneType.LingHunGuangChang then --镇妖境
		local is_helper = WuJinJiTanWGData.Instance:GetIsZhuZhan()
		-- self.node_list.zhuzhan_content:SetActive(is_helper)
		self.node_list.left:SetActive(false)
		self.node_list.right:SetActive(false)
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list.layout_star:SetActive(not is_helper)
		self.node_list.Bound:SetActive(not is_helper)
		self.node_list.manhuanggudian:SetActive(is_helper)
		self.node_list.exp_fb_special:SetActive(true)
		-- self.node_list["Bound"].rect.anchoredPosition = Vector2(0, -65)
		self.node_list["Bound"].rect.localScale = Vector3(0.9,0.9,0.9)
        self.node_list.lbl_fb_itemtips:SetActive(false)


	elseif self.scene_type == SceneType.DEMONS_FB then
		self.node_list["right"]:SetActive(false)
		self.node_list["layout_star"]:SetActive(false)
		self.node_list["xinmo_desc"]:SetActive(true)
	elseif self.scene_type == SceneType.ManHuangGuDian_FB then
		local scene_info = ManHuangGuDianWGData.Instance:GetSceneInfo()
		local is_helper = scene_info.is_helper == 1
		self.node_list.Bound:SetActive(not is_helper)
		self.node_list.rongyu_text:SetActive(is_helper)
		self.node_list.manhuanggudian:SetActive(true)
		self.node_list["layout_star"]:SetActive(false)
		self.node_list.info_active_root:SetActive(false)
		FuBenWGCtrl.Instance:SendManHuangGuDianReq(MANHUANGGUDIAN_REQ_TYPE.RANK_INFO)
	elseif self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB then --幻灵境
		local is_helper = TeamEquipFbWGData.Instance:GetIsZhuZhan()
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list.layout_star:SetActive(not is_helper)
		self.node_list.Bound:SetActive(not is_helper)
        self.node_list.manhuanggudian:SetActive(is_helper)
        self.node_list["layout_result_2"]:SetActive(false)
        local is_remain = FuBenPanelWGData.Instance:IsHighTeamEquipHasTimes()
		self.node_list["layout_result_3"]:SetActive(is_remain)
        self.node_list["layout_result_1"]:SetActive(not is_remain)

    elseif self.scene_type == SceneType.PERSON_BOSS then
        self.node_list.info_active_root:SetActive(false)
        local single_boss_info = BossWGData.Instance:GetSingleBossInfo()
        local boss_id = single_boss_info.boss_id

        local boss_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
        if boss_cfg == nil then
            print_error("取不到怪物表的配置, boss_id =",boss_id)
			return
        end
        local boss_name = boss_cfg.name
        local desc = string.format(Language.FuBen.PersonBossWinDes,boss_name)
        self.node_list["lbl_title_tips"].text.text = desc
        self.node_list["lbl_title_tips"].rect.localPosition = Vector2(0, 132)
		self.node_list["layout_star"]:SetActive(false)
        self.node_list.bottom_parent:SetActive(true)
        self.node_list.lbl_fb_itemtips:SetActive(true)

        local is_remain = BossWGData.Instance:GetPersonNextLayerCanKill()
		self.node_list["layout_result_3"]:SetActive(is_remain)
		self.node_list["layout_result_2"]:SetActive(false)
		self.node_list["layout_result_1"]:SetActive(not is_remain)
	elseif self.scene_type == SceneType.Shenyuan_boss then -- 深渊Boss
		local _1, _2 ,boss_id = BossWGData.Instance:GetCurSelectBossID()
        local boss_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
        if boss_cfg == nil then
            print_error("取不到怪物表的配置, boss_id =",boss_id)
			return
        end
        local boss_name = boss_cfg.name
        local desc = ""
        if self.reward_type then
        	desc = string.format(Language.FuBen.FuBenKind[self.reward_type], boss_name)
        end
        self.node_list.BoundParent.transform.localPosition = Vector3(0, -60, 0)
        self.node_list["lbl_sytitle_tips"]:SetActive(true)
		self.node_list["lbl_sytitle_tips"].text.text = desc
		self.node_list["layout_star"]:SetActive(false)
        self.node_list.bottom_parent:SetActive(true)
        self.node_list.lbl_fb_itemtips:SetActive(true)
		self.node_list["layout_result_3"]:SetActive(false)
		self.node_list["layout_result_2"]:SetActive(false)
		self.node_list["layout_result_1"]:SetActive(true)
	elseif self.scene_type == SceneType.FengShenBang then
		self.node_list["layout_star"]:SetActive(false)
		local scene_id = FengShenBangWGData.Instance:GetFSBSceneId()
		local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataBySceneID(scene_id)
		local monster_cfg = scene_cfg and BossWGData.Instance:GetMonsterInfo(scene_cfg.boss_id)
		if monster_cfg then
			self.node_list["lbl_title_tips"].text.text = string.format(Language.FengShenBang.WinDesc, monster_cfg.name)
		end
	elseif self.scene_type == SceneType.SPECIAL_PERSONAL_BOSS then
		self.node_list["layout_star"]:SetActive(false)
	elseif self.scene_type == SceneType.PERSON_MABI_BOSS_DISPLAY then
		self.node_list["layout_star"]:SetActive(false)
		self.node_list.lbl_fb_itemtips:SetActive(false)
	elseif self.scene_type == SceneType.HUNDRED_EQUIP then
		local rank_info_list, my_rank, my_wave = HundredEquipWGData.Instance:GetFuBenRankInfoList()
		self.node_list["lbl_title_tips"].text.text = string.format(Language.HundredEquip.CurWaveNum, my_wave)
		self.node_list["lbl_sytitle_tips"]:SetActive(true)
		self.node_list["lbl_sytitle_tips"].text.text = string.format(Language.HundredEquip.MaxRankNum, my_rank)
		self.node_list["layout_star"]:SetActive(false)
	elseif self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_1 then
		local is_helper = FuBenTeamCommonBossWGData.Instance:SetGetIsHelpState() == 1
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list.layout_star:SetActive(not is_helper)
		self.node_list.Bound:SetActive(not is_helper)
        self.node_list.manhuanggudian:SetActive(is_helper)
        self.node_list["layout_result_2"]:SetActive(false)
		self.node_list["layout_result_3"]:SetActive(false)
        self.node_list["layout_result_1"]:SetActive(true)
	elseif self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_2 then
		local is_helper = FuBenTeamCommonBossWGData.Instance:SetGetIsHelpState() == 1
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list.layout_star:SetActive(not is_helper)
		self.node_list.Bound:SetActive(not is_helper)
        self.node_list.manhuanggudian:SetActive(is_helper)
        self.node_list["layout_result_2"]:SetActive(false)
		self.node_list["layout_result_3"]:SetActive(false)
        self.node_list["layout_result_1"]:SetActive(true)
	elseif self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_3 then
		local is_helper = FuBenTeamCommonBossWGData.Instance:SetGetIsHelpState() == 1
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list.layout_star:SetActive(not is_helper)
		self.node_list.Bound:SetActive(not is_helper)
        self.node_list.manhuanggudian:SetActive(is_helper)
        self.node_list["layout_result_2"]:SetActive(false)
		self.node_list["layout_result_3"]:SetActive(false)
        self.node_list["layout_result_1"]:SetActive(true)
	elseif self.scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		local is_helper = FuBenTeamCommonTowerWGData.Instance:SetGetIsHelpState() == 1
		self.node_list.info_active_root:SetActive(not is_helper)
		self.node_list["layout_star"]:SetActive(false)
		self.node_list.Bound:SetActive(not is_helper)
		self.node_list.manhuanggudian:SetActive(is_helper)
		self.node_list["layout_result_2"]:SetActive(false)
		self.node_list["layout_result_3"]:SetActive(false)
        self.node_list["layout_result_1"]:SetActive(true)
		self.node_list.rune_tower_info:SetActive(true)
	end
	
	if self.scene_type ~= SceneType.ManHuangGuDian_FB then
		self:Flush()
	end

	self:ShowDuoBei()
end

function FuBenCommonWinView:CreateCells(item_num,item_data_list)
	if self.scene_type == SceneType.QingYuanFB then
		self:CreateQingYuanCells(item_num,item_data_list)
		return
	end
	if item_num > #self.fb_cells then
		for i = #self.fb_cells + 1, item_num do
			self.fb_cells[i] = ItemCell.New(self.node_list.layout_fixed_cells)
		end
	elseif item_num < #self.fb_cells then
		for i = item_num + 1, #self.fb_cells do
			self.fb_cells[i]:SetActive(false)
		end
	end
	for k,v in ipairs(item_data_list)do
		if self.fb_cells[k] ~= nil and v ~= nil then
			self.fb_cells[k]:SetActive(true)
			self.fb_cells[k]:SetData(v)
			self.fb_cells[k]:SetUselessModalActive(false)
		end
	end
end

function FuBenCommonWinView:CreateQingYuanCells(item_num,item_data_list)
	local bundle_name = "uis/view/marry_ui_prefab"
	local asset_name = "ph_fb_item"
	if item_num > #self.fb_cells then
		for i = #self.fb_cells + 1, item_num do
			self.fb_cells[i] = MarryFBWinRewardItemRender.New()
			self.fb_cells[i]:LoadAsset(bundle_name, asset_name, self.node_list.layout_qingyuan_cells.transform)
		end
	elseif item_num < #self.fb_cells then
		for i = item_num + 1, #self.fb_cells do
			self.fb_cells[i]:SetActive(false)
		end
	end
	for k,v in ipairs(item_data_list)do
		if self.fb_cells[k] ~= nil and v ~= nil then
			self.fb_cells[k]:SetActive(true)
			self.fb_cells[k]:SetData(v)
		end
	end
end

function FuBenCommonWinView:OnFlush()
	if CountDownManager.Instance:HasCountDown("common_fb_close_timer") then
		CountDownManager.Instance:RemoveCountDown("common_fb_close_timer")
	end
	self.node_list.star_img.transform.anchoredPosition = StarPos

	if self.fb_end_time > 0 then
		self:UpdateCountDownTime(1, self.fb_end_time)
		CountDownManager.Instance:AddCountDown("common_fb_close_timer", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind1(self.CompleteCountDownTime, self), nil, self.fb_end_time, 1)
    else
        self.node_list.lbl_fb_endtime.text.text = ""
    end

	local item_data_list = {}
	if self.data_list ~= nil and not IsEmptyTable(self.data_list) then
		for k, v in pairs(self.data_list) do
			if v.item_id and v.item_id > 0 and v.num > 0 then
                item_data_list[#item_data_list + 1] = v
			end
		end
    end

	if self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_1 or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_2 
		or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_3 then
			--策划需求  某些奖励必须放前面展示  特殊处理
			local fuben_other_cfg  = FuBenTeamCommonBossWGData.Instance:GetFuBenOtherCfg()
			local spe_reward_id_list = {}
			if fuben_other_cfg.spe_task_reward_id_1 then
				spe_reward_id_list = {[fuben_other_cfg.spe_task_reward_id_1] = true}
			end

			local is_have_spe_item_num = 0
			item_data_list, is_have_spe_item_num = self:SortDataList(item_data_list, spe_reward_id_list)
			local team_leader_reward_list = FuBenTeamCommonBossWGData.Instance:SetGetLeaderReward()
			if not IsEmptyTable(team_leader_reward_list) then
				for i, v in ipairs(team_leader_reward_list) do
					table.insert(item_data_list, is_have_spe_item_num + 1, v)
				end
				self.node_list.spe_leader_img:SetActive(true)
			end
	else
		item_data_list = self:SortDataList(item_data_list)
	end

	local item_num = GetTableLen(item_data_list)
	if self.scene_type == SceneType.QingYuanFB then
		self:CreateQingYuanCells(item_num,item_data_list)
	else
		self.node_list["layout_cells_list"]:SetActive(true)
		self.node_list["layout_fixed_cells"]:SetActive(false)
		self.special_cell_list:SetDataList(item_data_list)
		local width = item_num > 9 and MaxWidth or CellWidth * item_num + (item_num - 1) * CellSpace
		self.node_list["layout_cells_list"].rect.sizeDelta = Vector2(width, CellWidth)
		self.node_list["layout_cells_list"].scroll_rect.enabled = item_num > 9
	end

	local is_saodang_mark = FuBenPanelWGData.Instance:GetSaoDangMark()
	if is_saodang_mark then
		FuBenPanelWGData.Instance:SetSaoDangMark(false)
		self:PlayStarAction(self.star_num)
	else
		if self.scene_type == SceneType.ZHUSHENTA_FB or self.scene_type == SceneType.YUGUXIANDIANYINDAO then
			self:PlayStarAction(self.star_num)
		elseif self.scene_type == SceneType.ZHUANZHI_FB then
			self.node_list.transfer_text:SetActive(true)
			self:PlayStarAction(3)
        else
			if ViewManager.Instance:IsOpen(GuideModuleName.SceneLoading) then
				if not self.play_star_event then
					self.play_star_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW,BindTool.Bind(self.SceneLoadComplete,self))
				end
			else
				self:PlayStarAction(self.star_num)
			end
		end
	end

	if self.skill > 0 then
		local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(self.skill)
		if skill_data then
			self.node_list["img_skill"].image:LoadSprite(ResPath.GetSkillIconById(skill_data.icon))
			self.node_list["lbl_skill_name"].text.text = skill_data.name
			self.node_list["skill_desc"].text.text = skill_data.desc
		end
	end

	local path,name = ResPath.GetCommonIcon("a3_huobi_honor")
	self.node_list["img_gold"].image:LoadSprite(path,name)
	self.node_list.lbl_tips_2:SetActive(false)

	if self.scene_type == SceneType.COPPER_FB then
		local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
		local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
		self.node_list["lbl_saodang_num"].text.text = item_num .. "/" .. other_cfg.sweep_consum_item_num
	elseif self.scene_type == SceneType.DefenseFb then
		self.node_list["lbl_tips_2"].text.text = Language.DefenseFb.GainXianHunShi
		local b,a = ResPath.GetJuHun("img_item2")
		self.node_list["img_gold"].image:LoadSprite(b,a,function()
			XUI.ImageSetNativeSize(self.node_list["img_gold"])
			self.node_list["img_gold"]:SetActive(true)
		end )
		local other_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
		local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.sweep_item_id)
		self.node_list["lbl_saodang_num"].text.text = item_num .. "/" .. other_cfg.sweep_item_num
		local drop_info = FuBenWGData.Instance:GetDropInfo(FUBEN_TYPE.FBCT_TAFANG)
		self.node_list["lbl_tips2_value"].text.text = drop_info.flexible_int1
		self.node_list["right"]:SetActive(drop_info.flexible_int1 > 0)
	elseif self.scene_type == SceneType.Wujinjitan or self.scene_type == SceneType.LingHunGuangChang then
		local role_info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
		self.node_list["lbl_tips_2"].text.text = Language.FuBenPanel.KillMester
		self.node_list.lbl_tips_2:SetActive(true)
		self.node_list["img_gold"]:SetActive(false)
		local monster_coin = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
		self.node_list["lbl_tips2_value"].text.text = monster_coin.kill_monster_count or 0
		--if is_zhuzhan then
			--local shengwang_value = NewTeamWGData.Instance:GetXieZhuRewardValue(GoalTeamType.Exp_DuJieXianZhou)
			--self.node_list.zhuzhan_value.text.text = string.format(Language.FuBenPanel.ZhuZhanGetShengWang, shengwang_value)
		--end
		local now_level = GameVoManager.Instance:GetMainRoleVo().level
		local old_level = role_info.first_enter_level or now_level
		old_level = old_level > now_level and now_level or old_level
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(old_level)
		local new_is_vis, new_role_level = RoleWGData.Instance:GetDianFengLevel(now_level)

		self.node_list.exp_fb_old_level.text.text = role_level
		self.node_list.exp_fb_dianfeng1:SetActive(is_vis)
		self.node_list.exp_fb_new_level.text.text = new_role_level
		self.node_list.level_change_group:SetActive(true)

		self.node_list.exp_fb_coin.text.text = role_info.fetch_coin

		local drop_time = 0.3  --降落时间
		local start_delay = 0.4 --开始落下延迟
		self.node_list.exp_fb_dianfeng2:SetActive(now_level > old_level and new_is_vis)

		self.node_list.exp_fb_new_level:SetActive(now_level > old_level)
		self.node_list.exp_fb_level_arrow:SetActive(now_level > old_level)

		local get_exp = self.fb_exp or 0
		local text_obj = self.node_list.exp_fb_exp:GetComponent(typeof(TMPro.TextMeshProUGUI))
		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverExpFBNum(num)
			if postfix_name == "" then
				text_obj.text = (string.format("%.0f", value)) .. postfix_name
			else
				text_obj.text = (value) .. postfix_name
			end
		end

        local exp_fun1 = function ()
            if not self.node_list.exp_fb_get_exp then
                return
            end
			local end_pos = Vector3(-104,14,0)
			local end_scale = Vector3(1,1,1)
        	self.node_list.exp_fb_get_exp.rect.anchoredPosition = Vector2(-153, 74)
			self.node_list.exp_fb_get_exp.rect.localScale = Vector3(3,3,1)
			self.node_list.exp_fb_get_exp.rect:DOAnchorPos(end_pos, drop_time):OnComplete(
                function()
                    if not text_obj then
                        return
                    end
        			UITween.DONumberTo(text_obj, 0, get_exp, 1.5, update_fun,nil)
        		end)
			self.node_list.exp_fb_get_exp.rect:DOScale(end_scale,drop_time)
		end

		GlobalTimerQuest:AddDelayTimer(function ()
			exp_fun1()
		end,start_delay)

	elseif self.scene_type == SceneType.ZHUSHENTA_FB then
		self.node_list["lbl_tips_2"].text.text = ""
		self.node_list["img_gold"]:SetActive(false)
		self.node_list["lbl_tips2_value"]:SetActive(false)


	elseif self.scene_type == SceneType.PET_FB or self.scene_type == SceneType.FakePetFb then --苍穹变
		self.node_list["layout_result_2"]:SetActive(true)
		self.node_list["layout_result_1"]:SetActive(false)
		self.node_list["right"]:SetActive(false)
		local pet_info = FuBenPanelWGData.Instance:GetPetFBReward()
		if not pet_info then
			return
		end

		local role_level = RoleWGData.Instance:GetRoleLevel()
		local had_count = FuBenPanelWGData.Instance:CheckPetCount()
		-- 0 无 	 1 挑战下层 	 2 扫荡 	 3 再来一次
		local btn_status = 0
		if 1 == pet_info.is_sweep_fb then
			btn_status = had_count and 2 or 0
		else
			local next_cfg = FuBenPanelWGData.Instance:GetPetFBCfgByLevel(pet_info.level + 1)
			if next_cfg == nil then
				if had_count then
					btn_status = pet_info.star_num >= 3 and 2 or 3
				end
			else
				if had_count then
					if role_level < next_cfg.role_level then
						btn_status = pet_info.star_num >= 3 and 2 or 3
					else
						btn_status = pet_info.star_num >= 3 and 1 or 3
					end
				end
			end

			local cfg = FuBenWGData.Instance:GetSaoDangCfg(FUBEN_TYPE.FBCT_PETBEN)
			if cfg and role_level < cfg.pre_show_level then
				btn_status = 0
			end
		end
		-- 可进入次数大于0才显示扫荡
		local can_enter_times = FuBenPanelWGData.Instance:GetPetCanEnterTimes()

		self.node_list["btn_again"]:SetActive(btn_status > 0 and can_enter_times > 0)
		self.node_list["btn_again_text"].text.text = Language.FuBenPanel.PetFBBtnDesc[btn_status] or ""

	elseif self.scene_type == SceneType.VIP_BOSS then
		self.node_list.info_active_root:SetActive(false)
		local time = BossWGData.Instance:GetFubenPassTime()
		if time ~= 0 then
			self.node_list.lbl_pass_time:SetActive(true)
			self.node_list.lbl_pass_time.text.text = string.format(Language.Boss.PassTime,time)
		end
		self.node_list.lbl_fb_itemtips:SetActive(true)
		self.node_list.drop_probability:SetActive(true)
		local cfg = BossWGData.Instance:GetPersonBossOtherCfg()
		if cfg ~= nil then
			self.node_list.drop_probability:SetActive(true)
			local probability = cfg["star"..self.star_num]
			self.node_list.drop_probability.text.text = string.format(Language.Boss.StarDropProb, probability)
		end
	elseif self.scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		local rune_tower_mvp_info = FuBenTeamCommonTowerWGData.Instance:SetGetMvpInfo()
		if IsEmptyTable(rune_tower_mvp_info) then
			return
		end 

		if not self.rune_tower_head_cell then
			self.rune_tower_head_cell = BaseHeadCell.New(self.node_list.rune_tower_head_cell)
		end

		----头像
		local data = {}
		if rune_tower_mvp_info and rune_tower_mvp_info.mvp_is_robot == 1 then
			data.role_id = 0
		else
			data.role_id = rune_tower_mvp_info.mvp_uuid.temp_low
		end

		data.prof = rune_tower_mvp_info.prof
		data.sex = rune_tower_mvp_info.sex
		data.fashion_photoframe = rune_tower_mvp_info.shizhuang_photoframe
		self.rune_tower_head_cell:SetImgBg(true)
		self.rune_tower_head_cell:SetData(data)

		self.node_list.rune_tower_desc.text.text = string.format(Language.FuBenPanel.FubenRuneTowerDesc, rune_tower_mvp_info.mvp_name)
		self.node_list.rune_tower_add_friend:SetActive(data.role_id ~= 0 and data.role_id ~= RoleWGData.Instance:GetUUid().temp_low 
														and not SocietyWGData.Instance:CheckIsFriend(data.role_id))
	end

	local item_num11 = tonumber(self.node_list["lbl_tips2_value"].text.text or 0)
	if not self.fb_exp or self.fb_exp <= 0 then
		self.node_list["left"]:SetActive(false)
	elseif self.scene_type ~= SceneType.Wujinjitan and self.scene_type ~= SceneType.LingHunGuangChang then
		self.node_list["left"]:SetActive(true)
	end
	if item_num11 <= 0 then
		self.node_list["right"]:SetActive(false)
	elseif self.scene_type ~= SceneType.Wujinjitan and self.scene_type ~= SceneType.LingHunGuangChang then
		self.node_list["right"]:SetActive(true)
	end

	--协助隐藏经验
	if self.scene_type == SceneType.QingYuanFB then
		local marry_fb_info = MarryWGData.Instance:GetQyFbSceneInfo()
		self.node_list["left"]:SetActive(not (1 == marry_fb_info.is_helper))
	end
	self:ManHuangGuDianOnFlush()
end

function FuBenCommonWinView:ManHuangGuDianOnFlush()
	local is_helper = false
	if self.scene_type == SceneType.Wujinjitan or self.scene_type == SceneType.LingHunGuangChang then
		is_helper = WuJinJiTanWGData.Instance:GetIsZhuZhan()
	elseif self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		is_helper = TeamEquipFbWGData.Instance:GetIsZhuZhan()
	elseif self.scene_type == SceneType.ManHuangGuDian_FB then
		local scene_info = ManHuangGuDianWGData.Instance:GetSceneInfo()
		is_helper = scene_info.is_helper == 1
		if not is_helper then
			--波数
			--local wave = scene_info.curr_wave_index
			--self.node_list.lbl_manhuang_cur_boshu.text.text = wave
			--通关时间
			local pass_time = scene_info.pass_time_s
			self.node_list.time_text:SetActive(true)
			self.node_list.time_text.text.text = string.format(Language.FuBenPanel.ManHuangPassTime, TimeUtil.MSTime(pass_time))
			--超越多少玩家
			local rank_info = ManHuangGuDianWGData.Instance:GetManHuangRankInfo()
			local self_rank = rank_info.self_rank
			local total_count = rank_info.rank_count
			local value = 100

			if total_count > 0 then
				value = (1 - (self_rank - 1) / total_count) * 100 --策划给的公式 --(total_count - self_rank)/total_count * 100
			end
			value = string.format("%.0f", value)
			self.node_list.rank_text:SetActive(true)
			self.node_list.rank_text.text.text = string.format(Language.FuBenPanel.ManHuangRankText, value)
		end
	elseif self.scene_type == SceneType.QingYuanFB then
		local marry_fb_info = MarryWGData.Instance:GetQyFbSceneInfo()
		is_helper = marry_fb_info.is_helper == 1
	elseif self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_1 or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_2
			or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_3 then
				is_helper = FuBenTeamCommonBossWGData.Instance:SetGetIsHelpState() == 1
	elseif self.scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		is_helper = FuBenTeamCommonTowerWGData.Instance:SetGetIsHelpState() == 1
    end

    if is_helper then
        if self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB or self.scene_type == SceneType.LingHunGuangChang
        or self.scene_type == SceneType.QingYuanFB or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_1 
		or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_2 or self.scene_type == SceneType.TEAM_COMMON_BOSS_FB_3	
		or self.scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then --幻林境 --经验本 --情缘副本
            local helper_times, total_xiezhu_times = NewTeamWGData.Instance:GetXieZhuTimesByTeamWinType(GoalTeamType.ManHuangGuDian)
            local remain_times = total_xiezhu_times - helper_times
            if remain_times >= 0 then
                local reward_value = NewTeamWGData.Instance:GetXieZhuRewardValue(GoalTeamType.ManHuangGuDian)
                self.node_list.rongyu_text.text.text = string.format(Language.FuBenPanel.ManHuangRongYuTip, reward_value, remain_times)
            else
                self.node_list.layout_cells_list:SetActive(false)
                self.node_list.rongyu_text:SetActive(true)
                self.node_list.rongyu_text.text.text = Language.FuBenPanel.ManHuangRongYuMaxTip
            end
        end
	end
end

function FuBenCommonWinView:SceneLoadComplete()
	self:PlayStarAction(self.star_num)
end

function FuBenCommonWinView:UpdateCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
        if self.node_list.lbl_fb_endtime then
            if self.scene_type == SceneType.COPPER_FB and FuBenPanelWGData.Instance:IsTongBiRemaindTimes() then --聚宝阁
                self.node_list.lbl_fb_endtime.text.text = string.format(Language.Common.AutoContinueFight, math.floor(total_time - elapse_time))
            elseif self.scene_type == SceneType.BOOTYBAY_FB and BootyBayWGData.Instance:GetIsShowAgainBtn() then --天帝陵
                self.node_list.lbl_fb_endtime.text.text = string.format(Language.Common.AutoContinueFight, math.floor(total_time - elapse_time))
            else
                if self.is_enter_next then
                    self.node_list.lbl_fb_endtime.text.text = string.format(Language.GuildBattleRanked.AutoEnterNextFb, math.floor(total_time - elapse_time))
                else
                    self.node_list.lbl_fb_endtime.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, math.floor(total_time - elapse_time))
                end
            end
		end
	end
end

function FuBenCommonWinView:CompleteCountDownTime()
    if self.scene_type == SceneType.COPPER_FB and FuBenPanelWGData.Instance:IsTongBiRemaindTimes() then --聚宝阁
        self:SendContinueFbNext()
        return
    end
    if self.scene_type == SceneType.BOOTYBAY_FB then
        local is_remain = BootyBayWGData.Instance:GetIsShowAgainBtn()
        if is_remain then
            BootyBayWGCtrl.Instance:SendAgainFunc()
            self.enter_next_flag = 1
        else
            FuBenWGCtrl.Instance:SendLeaveFB()
        end
        self:Close()
        return
    end

	if self.scene_type == SceneType.ZHUSHENTA_FB then
		self:Close()
		MainuiWGCtrl.Instance:SetShowTimeTextState( false )
		return
	end
	self:Close()
	self:OnClinkCloseHandler()
end

function FuBenCommonWinView:SetCommonWinData(scene_type, data_list, exp, gold, star_num, fb_end_time, skill_index ,is_enter_next,is_saodang, reward_type, close_call_back)
    self.scene_type = scene_type
	self.data_list = data_list
	if scene_type ~= SceneType.HIGH_TEAM_EQUIP_FB then
		--self.data_list = self:SortDataList(self.data_list)
	else
		for k,v in pairs(self.data_list) do
			v.param = {}
			v.param.star_level = v.star
		end
	end

	self.reward_type = reward_type
	self.fb_exp = exp
	self.fb_gold = gold
	self.star_num = star_num
	self.fb_end_time = fb_end_time
	self.skill = skill_index or 0
	self.is_enter_next = is_enter_next
	self.is_saodang = is_saodang
	self.close_call_back = close_call_back
	if not self:IsOpen() then
		self:Open()
		local member_list = SocietyWGData.Instance:GetTeamMemberList() --缓存副本结束时候的队伍信息，防止有人退队，信息更新
		if NewTeamWGData.Instance:GetIsInRoomScene(scene_type) and not is_enter_next then
			GlobalTimerQuest:AddDelayTimer(function ()
				NewTeamWGCtrl.Instance:CheckAddFriend(scene_type, member_list)
			end,1)
		end
	else
		self:Flush()
	end
end

--珍稀外观使用类型
local user_type_tb ={
    [13] = true, --骑宠
    [18] = true, --化形类
}

function FuBenCommonWinView:SortDataList(data_list, spe_reward_id_list)
	if IsEmptyTable(data_list) then
		return data_list, false
	end
	
	local is_have_spe_item_num = 0
	for k,v in pairs(data_list) do
		if v.item_id and v.item_id > 0 and v.num > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			local use_type = item_cfg and item_cfg.use_type
			if use_type and user_type_tb[use_type] then
				v.is_zhenxi = 1
			else
				v.is_zhenxi = 0
			end
			local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
			v.is_equip = 0
			if big_type and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
				v.is_equip = 1
				v.sort_star = v.param and v.param.star_level or 0
			else
				v.sort_star = 0
			end
			v.sort_color = item_cfg and item_cfg.color or 1
			v.order = item_cfg.order or 0

			if spe_reward_id_list and spe_reward_id_list[v.item_id] then
				v.sort_color = 100
				is_have_spe_item_num = is_have_spe_item_num + 1
			end
		else
			v.is_zhenxi = 0
			v.is_equip = 0
			v.sort_color = 0
			v.sort_star = 0
			v.order = 0
		end
	end

	table.sort(data_list, SortTools.KeyUpperSorters("sort_color", "is_zhenxi","is_equip","sort_star","order", "item_id"))
    return data_list, is_have_spe_item_num
end

--递归调用
function FuBenCommonWinView:PlayStarAction(star_num)
	if self.is_play_star then return end --防止服务器下发多次 is_finish , is_pass
	self.is_play_star = true

	local star_res = GetSpecialStarImgResByStar5(star_num)
	local bundle, asset = ResPath.GetCommonImages(star_res)
	XUI.SetNodeImage(self.node_list.star_img, bundle, asset)

	local star_effct_res = GetSpecialStarEffect5(star_num)
	local eff_bundle, eff_asset = ResPath.GetUIEffect(star_effct_res)
    self.node_list.star_img_effect:ChangeAsset(eff_bundle, eff_asset)

	self.node_list.star_img.rect:DOScale(Vector3(1,1,1), 0.5)
	self.node_list.star_img.rect:DORotate(Vector3(0, 0, -360 * 3), 0.3, DG.Tweening.RotateMode.FastBeyond360)

	self.root_node.gameObject:SetActive(true)

    self.node_list.Bound:SetActive(false)
	
    -- local qingyuan_pianyi = self.scene_type == SceneType.QingYuanFB and 40 or 0
	-- self.node_list.Bound.transform.localPosition = Vector3(0, qingyuan_pianyi, 0)

	-- local hun_ling_jing = self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB and 10 or 0
	-- self.node_list.Bound.transform.localPosition = Vector3(0, hun_ling_jing, 0)

	local bound_pos_y = 0
	if self.scene_type == SceneType.QingYuanFB then
		bound_pos_y = 40
	elseif self.scene_type == SceneType.LingHunGuangChang then
		bound_pos_y = -65
	elseif self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		bound_pos_y = 10
	end

	self.node_list.Bound.transform.localPosition = Vector3(0, bound_pos_y, 0)


	GlobalTimerQuest:AddDelayTimer(function()
		self.node_list.layout_exp_money_tips:SetActive(self.scene_type ~= SceneType.QingYuanFB and self.scene_type ~= SceneType.PERSON_BOSS)
		self.node_list.bottom_parent:SetActive(true)
		self.node_list.Bound:SetActive(true)
		if self.scene_type == SceneType.Wujinjitan or self.scene_type == SceneType.LingHunGuangChang then
			local info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
			if info and info.exp_percent > 0 then
				self.node_list.exp_score_image:SetActive(true)
				self.node_list.exp_score_image.transform.localScale = Vector3(2,2,2)
				self.node_list.exp_score_image.rect:DOScale(1,0.5)
			end
		end

		UITween.AlpahShowPanel(self.node_list.layout_exp_money_tips.gameObject, true, 1.5)
		UITween.AlpahShowPanel(self.node_list.bottom_parent.gameObject, true, 1.5)
		UITween.MoveShowPanel(self.node_list.Bound.gameObject, Vector3(1000,0), 0.5)
	end, 0.5)
end

function FuBenCommonWinView:OnClinkCloseHandler()
	local call_back = self.close_call_back
	self:Close()
	if self.scene_type == SceneType.ZHUSHENTA_FB then MainuiWGCtrl.Instance:SetShowTimeTextState( false )  return end
	if self.scene_type == SceneType.DEMONS_FB then
		if self.skill > 0 then
			local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(self.skill)
			if skill_data then
				self.icon_path = ResPath.GetSkillIconById(skill_data.icon)
			end
			self:StartFly()
		end
	end

	if call_back ~= nil then
		call_back()
	end
    if self.scene_type == SceneType.LingHunGuangChang then  --策划要求组队本结束打开对应面板
        local enter_times = FuBenPanelWGData.Instance:GetExpFubenTimes() --策划要求新手第一次进入时不打开对应面板
        local is_first = enter_times <= 1
        if not is_first then
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_exp)
        end
    elseif self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
        ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_equip_high)
    end

	self.close_call_back = nil
end

function FuBenCommonWinView:OnClickRuneAddFriend()
	local rune_tower_mvp_info = FuBenTeamCommonTowerWGData.Instance:SetGetMvpInfo()
	if IsEmptyTable(rune_tower_mvp_info) then
		return
	end 

	local role_id = 0
	if rune_tower_mvp_info and rune_tower_mvp_info.mvp_is_robot == 1 then
		role_id = 0
	else
		role_id = rune_tower_mvp_info.mvp_uuid.temp_low
	end

	if role_id ~= 0 and role_id ~= RoleWGData.Instance:GetUUid().temp_low and (not SocietyWGData.Instance:CheckIsFriend(role_id)) then
		SocietyWGCtrl.Instance:IAddFriend(role_id)
	end
end

function FuBenCommonWinView:StartFly()
	local is_visible = MainuiWGData.Instance:GetMianUiBottomBtnVisible()
	if is_visible == true then
		MainuiWGCtrl.Instance:ChangeBottomOnOffBtnState()
	end
	self:FlyEnd()
end

function FuBenCommonWinView:FlyEnd()
	MainuiWGCtrl.Instance:ChangeBottomOnOffBtnState()
end

function FuBenCommonWinView:OnClinkAgainHandler()
	if self.scene_type == SceneType.COPPER_FB then
		local copper_result_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
		if copper_result_info then
			local role_vip = VipWGData.Instance:GetRoleVipLevel()
			local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyCountCfg()
			local copper_info = FuBenPanelWGData.Instance:GetTongBiInfo()
			local time = vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times
			if not FuBenPanelWGCtrl.Instance:CheckCopperCount() and time > 0 then
				FuBenPanelWGCtrl.Instance:OpenCopperBuy(true)
			else
				FuBenPanelWGCtrl.Instance:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_SWEEP_FB, copper_result_info.cur_layer)
			end
        end
    elseif self.scene_type == SceneType.DefenseFb then
		local tf_cfg_other = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
		local tf_fb_buy_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
		local tf_fb_join_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES) or 0
		local left_times = tf_cfg_other.enter_free_times + tf_fb_buy_num - tf_fb_join_num

		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyTfCountCfg()
		local day_buy_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
		local time = vip_buy_cfg["param_" .. role_vip] - day_buy_times

		if left_times == 0 and time > 0 then
			FuBenPanelWGCtrl.Instance:OpenTaFangBuy()
		elseif left_times == 0 and time <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.DefenseFb.LeftSweepTimes)
		else
			local tafang_sweepmsg_View = FuBenPanelWGCtrl.Instance.tafang_saodang
			local sweep_boss_num = FuBenPanelWGData.Instance:GetTaFanIsNologer() and FuBenPanelWGData.Instance:GetTaFanSaoDanBossNum() or 0
			FuBenWGCtrl.Instance:SendSwipeFB(FUBEN_TYPE.FBCT_TAFANG, sweep_boss_num)
		end
	elseif self.scene_type == SceneType.PET_FB or self.scene_type == SceneType.FakePetFb then
		local pet_result_info = FuBenPanelWGData.Instance:GetPetFBReward()
		if not pet_result_info then
			return
		end

		local had_count = FuBenPanelWGData.Instance:CheckPetCount()
		-- 0 无 	 1 挑战下层 	 2 扫荡 	 3 再来一次
		local btn_status = 0
		if 1 == pet_result_info.is_sweep_fb then
			btn_status = had_count and 2 or 0
		else
			local next_cfg = FuBenPanelWGData.Instance:GetPetFBCfgByLevel(pet_result_info.level + 1)
			if next_cfg == nil then
				if had_count then
					btn_status = pet_result_info.star_num >= 3 and 2 or 3
				end
			else
				if had_count then
					local role_level = RoleWGData.Instance:GetRoleLevel()
					if role_level < next_cfg.role_level then
						btn_status = pet_result_info.star_num >= 3 and 2 or 3
					else
						btn_status = pet_result_info.star_num >= 3 and 1 or 3
					end
				end
			end
		end

		if btn_status == 0 then
			local role_vip = VipWGData.Instance:GetRoleVipLevel()
			local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyPetCfg()
			local pet_all_info = FuBenPanelWGData.Instance:GetPetAllInfo()
			local buy_times = vip_buy_cfg["param_" .. role_vip] - pet_all_info.buy_times
			if buy_times > 0 then
				FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_PETBEN)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotTimesEnter)
			end

		elseif btn_status == 1 then
			self.enter_next_flag = 1
			self:SendContinueFbNext(pet_result_info.level + 1)
			self:Close()
		elseif btn_status == 2 then
			FuBenWGCtrl.Instance:ShowSaoDangPanel(FUBEN_TYPE.FBCT_PETBEN, pet_result_info.level, nil, nil)
		elseif btn_status == 3 then
			self.enter_next_flag = 1
			self:Close()
			self:SendContinueFbNext(pet_result_info.level)
		end
	end
end


function FuBenCommonWinView:SendContinueFbNext(param)
    FuBenWGCtrl.Instance:SendFBReqNextLevel(param)
    self.enter_next_flag = 1
    self:Close()
    Scene.Instance:SimulationSceneLoad()
end

--点击进入下个难度
function FuBenCommonWinView:OnClinkNextLevelHandler()
	if self.scene_type == SceneType.BaGuaMiZhen_FB then --通天塔
		local is_remain = FuBenPanelWGData.Instance:IsFubenBaGuaRemaindTimes()
        if is_remain then
            self:SendContinueFbNext()
        end
    elseif self.scene_type == SceneType.BOOTYBAY_FB then --单人天帝陵
        local is_remain = BootyBayWGData.Instance:GetIsShowAgainBtn()
        if is_remain then
            BootyBayWGCtrl.Instance:SendAgainFunc()
            self.enter_next_flag = 1
            self:Close()
        end
    elseif self.scene_type == SceneType.COPPER_FB then --聚宝阁
        local is_remain = FuBenPanelWGData.Instance:IsTongBiRemaindTimes()
        if is_remain then
            self:SendContinueFbNext()
        end
    elseif self.scene_type == SceneType.PERSON_BOSS then --个人boss
        local is_remain = BossWGData.Instance:GetPersonNextLayerCanKill()
        if is_remain then
            local ok_func = function()
                self:SendContinueFbNext()
            end
            if Scene.Instance:GetSceneType() ==  SceneType.PERSON_BOSS then
                BossWGCtrl.Instance:OpenPersonCosumeView(ok_func)
            else
                local boss_id = BossWGData.Instance:GetEnterPersonBossId()
                if boss_id then
                    BossOfferWGCtrl.Instance:JumToByID(boss_id)
                else
                    ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_personal)
                end
                self:Close()
            end
        end
    elseif self.scene_type == SceneType.HIGH_TEAM_EQUIP_FB then --幻灵境
        local is_remain, is_can_enter_next = FuBenPanelWGData.Instance:IsHighTeamEquipHasTimes()
        if is_remain then
            if not is_can_enter_next then
                self:SendContinueFbNext()
            else
                local ok_func = function()
                    FuBenWGCtrl.Instance:SendFBReqNextLevel(1)
                    self.enter_next_flag = 1
                    self:Close()
                    Scene.Instance:SimulationSceneLoad()
                end
                local cancle_func = function()
                    self:SendContinueFbNext()
                end
                local time = CountDownManager.Instance:GetRemainTime("common_fb_close_timer")
                if not self.alert_tips then
                    self.alert_tips = Alert.New()
                end
                self.alert_tips:SetLableString(Language.FuBenPanel.HighTeamTips)
                self.alert_tips:SetOkFunc(ok_func)
                self.alert_tips:SetCancelFunc(cancle_func)
                self.alert_tips:SetCountDownFunc(time, nil, function()
                    self.alert_tips:Close()
                end)
                self.alert_tips:SetOkString(Language.FuBenPanel.NextLv)
                self.alert_tips:SetCancelString(Language.FuBenPanel.ContinueFight)
                self.alert_tips:Open()
            end
        end
    end
end

function FuBenCommonWinView:GetCloseBtn()
	return nil
end

function FuBenCommonWinView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.CloseBtn and nil ~= self.node_list.btn_close_window then
		return self.node_list.btn_close_window.node, BindTool.Bind(self.OnClinkCloseHandler, self)
	end
	return nil, nil
end

function FuBenCommonWinView:ShowDuoBei()
	local scene_type = Scene.Instance:GetSceneType()
	local task_type = TianshenRoadWGData.Instance:GetDuoBeiTaskType(scene_type)
	if task_type then
		if TianshenRoadWGData.Instance:GetDuoBeiTimes(task_type) > 0 then
			self.node_list.duobei_icon:SetActive(true)
		end
	end
end

-----------------------------------------------------------------
----CommonListItemRender
CommonListItemRender = CommonListItemRender or BaseClass(BaseRender)
function CommonListItemRender:__init()
	self.cells = ItemCell.New(self.node_list["ph_cells"])
end

function CommonListItemRender:__delete()
	if self.cells then
		self.cells:DeleteMe()
		self.cells = nil
	end
end

function CommonListItemRender:OnFlush()
	if not self.data then return end
	-- self.node_list.three_flag:SetActive(false)

	if self.data.item_id > 0 then
		self.cells:SetData(self.data)
		self.cells:SetUselessModalActive(false)
		--self.cells:SetLeftTopImg(self.data.star)
	end
	--三星额外
	-- if self.data.is_three_must_drop ~= nil and self.node_list.three_flag then
	-- 	self.node_list.three_flag:SetActive(self.data.is_three_must_drop and self.data.is_three_must_drop)
	-- 	if self.data.is_three_must_drop then
	-- 		self.node_list.three_flag_text.text.text = Language.FuBenPanel.ThreeEWai
	-- 	end
	-- end
end
