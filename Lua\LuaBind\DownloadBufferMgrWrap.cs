﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class DownloadBufferMgrWrap
{
	public static void Register(LuaState L)
	{
		L.<PERSON>ginStaticLibs("DownloadBufferMgr");
		<PERSON><PERSON>Function("<PERSON><PERSON>Buffer", CreateBuffer);
		<PERSON><PERSON>unction("OnGameStop", OnGameStop);
		<PERSON><PERSON>ction("DestoryAllBuffer", DestoryAllBuffer);
		<PERSON><PERSON>("DestoryBuffer", DestoryBuffer);
		<PERSON><PERSON>RegFunction("Update", Update);
		<PERSON><PERSON>ar("MinWriteSize", get_MinWriteSize, set_MinWriteSize);
		<PERSON><PERSON>("WriteCD", get_WriteCD, set_WriteCD);
		<PERSON><PERSON>("IsDebug", get_IsDebug, set_IsDebug);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateBuffer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			DownloadBufferMgr.CreateBuffer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			DownloadBufferMgr.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DestoryAllBuffer(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				DownloadBufferMgr.DestoryAllBuffer();
				return 0;
			}
			else if (count == 1)
			{
				bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
				DownloadBufferMgr.DestoryAllBuffer(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DownloadBufferMgr.DestoryAllBuffer");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DestoryBuffer(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				DownloadBufferMgr.DestoryBuffer(arg0);
				return 0;
			}
			else if (count == 2)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
				DownloadBufferMgr.DestoryBuffer(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: DownloadBufferMgr.DestoryBuffer");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			DownloadBufferMgr.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinWriteSize(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, DownloadBufferMgr.MinWriteSize);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WriteCD(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, DownloadBufferMgr.WriteCD);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsDebug(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, DownloadBufferMgr.IsDebug);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinWriteSize(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			DownloadBufferMgr.MinWriteSize = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_WriteCD(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			DownloadBufferMgr.WriteCD = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsDebug(IntPtr L)
	{
		try
		{
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			DownloadBufferMgr.IsDebug = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

