ShengyuBossSceneLogic = ShengyuBossSceneLogic or BaseClass(CommonFbLogic)
function ShengyuBossSceneLogic:__init()
	self.last_check_time = 0
end

function ShengyuBossSceneLogic:__delete()
end

function ShengyuBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:Open(GuideModuleName.Boss_ShengYu_FollowUi)
	-- BossWGCtrl.Instance.shenyu_rank_list:OnClickShengyuAscription()
	self.show_quit_time = false
	self.is_set_mainui_time = false
	-- local select_obj_list = Scene.Instance:GetRoleList()
	-- local obj_select = nil
	-- for _,v in pairs(select_obj_list) do
	-- 	obj_select = v
	-- 	break
	-- end
	-- if obj_select then
	-- 	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj_select, SceneTargetSelectType.SELECT)
	-- end
	local tired = BossWGData.Instance:SacredBossTireInfo()
	if nil ~= tired then
		BossWGCtrl.Instance:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_SHENYU_BOSS_TIRE, tired)
	end
	GuajiWGCtrl.Instance:ClearAllOperate()
end

function ShengyuBossSceneLogic:GetKickTime()
	if self.kick_time_s == nil then
		self.kick_time_s = BossWGData.Instance:GetSacredBossOtherAuto().kick_time_s or 30 		--副本退出时间
	end
	return self.kick_time_s
end

function ShengyuBossSceneLogic:Out()
	BossWGCtrl.Instance.shenyu_rank_list:OnClickShengyuAscriptionEnd()
	BossWGCtrl.Instance.shenyu_rank_list:Close()
	self.is_set_mainui_time = false
	self.show_quit_time = false
	CommonFbLogic.Out(self)
	ViewManager.Instance:Close(GuideModuleName.Boss_ShengYu_FollowUi)
	BossWGCtrl.Instance:OutSceneCallback()
	BossWGCtrl.Instance:OutShengyuSceneCallback()

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end
end

function ShengyuBossSceneLogic:OnObjCreate(obj)
	if obj and not SceneObj.select_obj and self:IsEnemy(obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
end

function ShengyuBossSceneLogic:OnLoadingComplete()
	-- print_error("OnLoadingComplete")
	 MainuiWGCtrl.Instance:ShowMainuiMenu(false)    
end

-- 是否是挂机打怪的敌人
function ShengyuBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function ShengyuBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)

	if now_time - self.last_check_time > 1 then
		self.last_check_time = now_time
		local tab = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUSACREDBOSS)
		local time = tab.next_time
		if tab == nil then
			return
		end
		local kick_time_s = self:GetKickTime()
		if time - TimeWGCtrl.Instance:GetServerTime() <= kick_time_s and not self.show_quit_time then
			FuBenPanelCountDown.Instance:SetTimerInfo(time)
			self.show_quit_time = true
		else
			if tab and not self.is_set_mainui_time then
				if time - TimeWGCtrl.Instance:GetServerTime() > 0 then
					self.is_set_mainui_time = true
					MainuiWGCtrl.Instance:SetFbIconEndCountDown(time)
				else
					return
				end
			end
		end
	end
end

function ShengyuBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
			self:SetGuaiJi(GUAI_JI_TYPE.ROLE)
		end
	end
	BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function ShengyuBossSceneLogic:GetGuajiCharacter()
	local target_obj
	target_obj = self:GetNormalRole()
	if target_obj ~= nil then
		return target_obj
	end
	if target_obj == nil then
		return self:GetMonster()
	end
end

function ShengyuBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			GuajiCache.target_obj = v
			return v
		end
	end
end

function ShengyuBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	return Scene.Instance:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
end

function ShengyuBossSceneLogic:IsMonsterEnemy(target_obj, main_role)
	local max_tired = BossWGData.Instance:GetSacredCfg().other[1].daily_boss_tire
	local tire_value = BossWGData.Instance:GetSacreBossTire()
	if tire_value == nil then
		return
	end
	if tire_value >= max_tired then
		if target_obj:IsBoss() then
			return false, Language.Boss.OutTire
		end
	end
	return true
end

-- function ShengyuBossSceneLogic:GetFbSceneMonsterBossCfg()
-- 	-- return BossWGData.Instance:GetSacredBossCfg()
-- end

function ShengyuBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	monsters_list = BossWGData.Instance:GetCurSceneAllMonster(nil,BossWGData.MonsterType.Monster)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function ShengyuBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

function ShengyuBossSceneLogic:GetFbSceneMonsterCfg()
	local list = BossWGData.Instance:GetCurSceneAllMonster(nil, BossWGData.MonsterType.Monster)
	return list,#list
end