KfUltimateReadySceneLogic = KfUltimateReadySceneLogic or BaseClass(CrossServerSceneLogic)

function KfUltimateReadySceneLogic:__init()
	-- self.change_special_param_func = BindTool.Bind(self.ChangeSpecialParam, self)
	-- self.change_special = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, self.change_special_param_func)
	-- self.change_rob_special = GlobalEventSystem:Bind(SceneEventType.OBJ_ENTER_SHADOW, self.change_special_param_func)
end

function KfUltimateReadySceneLogic:__delete()
	if self.change_special then
		GlobalEventSystem:UnBind(self.change_special)
		self.change_special = nil
	end

	if self.change_rob_special then
		GlobalEventSystem:UnBind(self.change_rob_special)
		self.change_rob_special = nil
	end

	if self.shield_pet_rule then
		self.shield_pet_rule:DeleteMe()
		self.shield_pet_rule = nil
	end
end

function KfUltimateReadySceneLogic:Update(now_time, elapse_time)
	CrossServerSceneLogic.Update(self, now_time, elapse_time)
end

-- 角色阵营发生变化
function KfUltimateReadySceneLogic:ChangeSpecialParam(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	if (not role) or (not role.vo) then
		return
	end

	local ultimate_data = {}
	ultimate_data.server_id = role.vo.origin_server_id
	local cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(role.vo.special_param)
	ultimate_data.camp_name = cfg and cfg.camp_name or ""
	ultimate_data.camp = role.vo.special_param

	local follow_ui = role:GetFollowUi()
	if follow_ui then
		follow_ui:SetUltimateState(ultimate_data)
	end
end

-- 进入准备场景
function KfUltimateReadySceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type == new_scene_type then
		return
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        MainuiWGCtrl.Instance:SetTaskContents(false)
        MainuiWGCtrl.Instance:SetOtherContents(true)
		-- MainuiWGCtrl.Instance:SetBtnTianxianPavilionStatus(false)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.UltimateBattlefield.ReadySceneTitle)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		MainuiWGCtrl.Instance:SetMianUITargetPos(nil, -40)
	end)


	local show_data = self:GetQuestionShowData()
	UltimateBattlefieldWGCtrl.Instance:OpenFollowView(show_data)
	self.shield_pet_rule = SimpleRule.New(ShieldObjType.Pet, ShieldRuleWeight.Middle, function (obj)
		return not obj:IsOnwerPet()
	end)
	self.shield_pet_rule:Register()
end

--获取答题倒计时
function KfUltimateReadySceneLogic:GetQuestionShowData()
	local question_info = UltimateBattlefieldWGData.Instance:GetQuestionInfo()

	if not question_info then
		return nil
	end

	local show_data = {}
	show_data.end_time = question_info.question_standby_time
	show_data.end_str = Language.UltimateBattlefield.WaitQuestionTime
	show_data.show_type = CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_QUESTION
	return show_data
end

-- 离开场景
function KfUltimateReadySceneLogic:Out(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Out(self)
	if old_scene_type == new_scene_type then
		return
	end

	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:SetBtnTianxianPavilionStatus(true)
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil, 0)
	UltimateBattlefieldWGCtrl.Instance:ClsoeFollowView()
end