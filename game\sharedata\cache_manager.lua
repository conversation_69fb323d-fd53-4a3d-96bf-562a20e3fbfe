-- 缓存管理
CacheManager = CacheManager or BaseClass()

-- 缓存类型
CacheType = {
	Private = "private_chat_cache",					-- 私聊缓存
	Message = "message_cache",						--	留言
}

function CacheManager:__init()
	if CacheManager.Instance ~= nil then
		Error<PERSON><PERSON>("[CacheManager] attempt to create singleton twice!")
		return
	end
	CacheManager.Instance = self
	self.cache_list = {}
	self.total_cache_list = {}
end

function CacheManager:__delete()
	CacheManager.Instance = nil
	self.cache_list = nil
end

function CacheManager:GetCache(cache_key)
	if self.cache_list[cache_key] then
		return self.cache_list[cache_key]
	end

	----------测试清空缓存-----------
	-- PlayerPrefsUtil.SetString(cache_key, "")
	----------------------
	
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name
	local last_server_list = LoginWGData.Instance:GetLastLoginServer() --最近登陆服务器

	if IsEmptyTable(last_server_list) then 
		-- 奇怪，这个号没进缓存
		self.cache_list[cache_key] = {}
		return self.cache_list[cache_key]
	end
	local server_id = tonumber(last_server_list[1]) or 0
	local cache_str = nil

	local total_cache = self.total_cache_list[cache_key]
	if not total_cache then
		cache_str = PlayerPrefsUtil.GetString(cache_key)
		if cache_str and  "" ~= cache_str then
			total_cache = StrToTable(cache_str)
		end
		total_cache = total_cache or {}
	end

	if not total_cache[account_name] then
		total_cache[account_name] = {}
	end

	if not total_cache[account_name][server_id] then
		total_cache[account_name][server_id] = {}
	end

	local index = nil
	for k,v in pairs(total_cache[account_name][server_id]) do
		if v.role_id == RoleWGData.Instance:GetOriginUid() then
			index = tonumber(k)
			break
		end
	end
	local len = #total_cache[account_name][server_id]
	if not index then
		if len < 2 then
			index = len + 1
		else
			index = 2
		end
	end 

	if not total_cache[account_name][server_id][index] then
		total_cache[account_name][server_id][index] = {}
	end

	self.cache_list[cache_key] = total_cache[account_name][server_id][index]
	self.total_cache_list[cache_key] = total_cache

	return self.cache_list[cache_key]
end

function CacheManager:SetCache(cache_key, list)
	-- print_error("SetCache:::", cache_key, list)
	-- if not list then return end
	if list then
		list.role_id = RoleWGData.Instance:GetOriginUid()
	end
	self.cache_list[cache_key] = list
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name
	local last_server_list = LoginWGData.Instance:GetLastLoginServer() --最近登陆服务器
	if IsEmptyTable(last_server_list) then 
		-- 奇怪，这个号没进缓存		
		return
	end
	local server_id = tonumber(last_server_list[1])
	local index = nil
	local total_cache = self.total_cache_list[cache_key]
	if nil == total_cache then
		return
	end
	for k,v in pairs(total_cache[account_name][server_id]) do
		if v.role_id == RoleWGData.Instance:GetOriginUid() then
			index = tonumber(k)
			break
		end
	end
	index = index or 1
	total_cache[account_name][server_id][index] = self.cache_list[cache_key]
	
	self.total_cache_list[cache_key] = total_cache
	local cache_str = TableToStr(total_cache, 1)	
	
	PlayerPrefsUtil.SetString(cache_key, cache_str)
end

--获取私聊缓存数据
function CacheManager:GetMsgCache(key,role_id)
	key = key or ""
	local cache_key = key .. role_id
	if self.cache_list[cache_key] then
		return self.cache_list[cache_key],cache_key
	end

	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name
	local last_server_list = LoginWGData.Instance:GetLastLoginServer() --最近登陆服务器

	if IsEmptyTable(last_server_list) then 
		-- 奇怪，这个号没进缓存
		self.cache_list[cache_key] = {}
		return self.cache_list[cache_key],cache_key
	end
	local server_id = tonumber(last_server_list[1]) or 0
	local cache_str = nil

	local total_cache = self.total_cache_list[cache_key]
	if not total_cache then
		cache_str = PlayerPrefsUtil.GetString(cache_key)
		if cache_str and  "" ~= cache_str then
			total_cache = StrToTable(cache_str)
		end
		total_cache = total_cache or {}
	end

	if not total_cache[account_name] then
		total_cache[account_name] = {}
	end

	if not total_cache[account_name][server_id] then
		total_cache[account_name][server_id] = {}
	end

	local index = nil
	for k,v in pairs(total_cache[account_name][server_id]) do
		if v.role_id == RoleWGData.Instance:GetOriginUid() then
			index = tonumber(k)
			break
		end
	end
	local len = #total_cache[account_name][server_id]
	if not index then
		if len < 2 then
			index = len + 1
		else
			index = 2
		end
	end 

	if not total_cache[account_name][server_id][index] then
		total_cache[account_name][server_id][index] = {}
	end

	self.cache_list[cache_key] = total_cache[account_name][server_id][index]
	self.total_cache_list[cache_key] = total_cache
	-- print_error(">>>>>>>>>>self.cache_list[cache_key]",cache_key,self.cache_list[cache_key])
	return self.cache_list[cache_key] , cache_key
end

--保存私聊缓存数据
function CacheManager:SetMsgCache(key,role_id,list)
	-- print_error("----<color=#79b9fa> 保存私聊缓存数据 </color>-----", key, role_id, list)
	key = key or ""
	local cache_key = key .. role_id
	if list then
		list.role_id = RoleWGData.Instance:GetOriginUid()
	end
	self.cache_list[cache_key] = list
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name
	local last_server_list = LoginWGData.Instance:GetLastLoginServer() --最近登陆服务器
	if IsEmptyTable(last_server_list) then 
		return
	end
	local server_id = tonumber(last_server_list[1]) or 0
	local index = nil
	local total_cache = self.total_cache_list[cache_key]
	if nil == total_cache then
		total_cache = {}
		total_cache[account_name] = {}
		total_cache[account_name][server_id] = {}	
		total_cache[account_name][server_id][1] = {}
		total_cache[account_name][server_id][1] = list
	end
	for k,v in pairs(total_cache[account_name][server_id]) do
		if v.role_id and v.role_id == RoleWGData.Instance:GetOriginUid() then
			index = tonumber(k)
			break
		end
	end
	index = index or 1
	total_cache[account_name][server_id][index] = self.cache_list[cache_key]


	self.total_cache_list[cache_key] = total_cache
	local cache_str = TableToStr(total_cache, 1)	
	-- print_error("SetString:::", cache_key, total_cache)
	-- print_error("SetString:::", cache_key, cache_str)
	PlayerPrefsUtil.SetString(cache_key, cache_str)
	return cache_key,cache_str
end

function CacheManager:ClearRoleMsgCache(key,role_id)
	key = key or ""
	local cache_key = key .. role_id
	PlayerPrefsUtil.DeleteKey(cache_key)
end

-- 清档时掉用这里
function CacheManager:ClearAllCache()
	-- body
	for k,v in pairs(CacheType) do
		self:GetCache(v)
		self:SetCache(v, nil)
	end
end