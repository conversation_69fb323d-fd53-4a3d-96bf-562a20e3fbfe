local boss_drop_timer_key = "boss_drop_timer"

function NewFestivalActivityView:LoadIndexCallBackBossDrop()
    if not self.diaoluo_item_cell then
        self.diaoluo_item_cell = ItemCell.New(self.node_list.diaoluo_cell_pos)
    end

    self:InitBossDropImageAndText()
    self:CreateBossDropCountDown()
end

function NewFestivalActivityView:ReleaseBossDrop()
    if CountDownManager.Instance:HasCountDown(boss_drop_timer_key) then
        CountDownManager.Instance:RemoveCountDown(boss_drop_timer_key)
    end

    if self.diaoluo_item_cell then
        self.diaoluo_item_cell:DeleteMe()
        self.diaoluo_item_cell = nil
    end
end

function NewFestivalActivityView:OnFlushBossDrop()
    local cur_drop_num = NewFestivalBossDropWGData.Instance:GetDropCount()
    local max_num = NewFestivalBossDropWGData.Instance:GetBossDropOtherCfg().drop_count
    self.node_list.diaoluo_drop_count.text.text = string.format(Language.BossDrop.ToDayDropCount, self.diaoluo_num_color, cur_drop_num, max_num)
end

function NewFestivalActivityView:InitBossDropImageAndText()
    local reward_cfg = NewFestivalBossDropWGData.Instance:GetBossDropOtherCfg()
    self.diaoluo_item_cell:SetData({item_id = reward_cfg.item})

    local bg_bundle, bg_asset = ResPath.GetNewFestivalRawImages("diaoluo_bg")
    self.node_list.diaoluo_bg.raw_image:LoadSprite(bg_bundle, bg_asset, function()
        self.node_list.diaoluo_bg.raw_image:SetNativeSize()
    end)

    local title_bundle, title_asset = ResPath.GetNewFestivalRawImages("diaoluo_title")
    self.node_list.diaoluo_title.raw_image:LoadSprite(title_bundle, title_asset, function ()
        self.node_list.diaoluo_title.raw_image:SetNativeSize()
    end)

    local slogan_bundle, slogan_asset = ResPath.GetNewFestivalRawImages("diaoluo_slogan")
    self.node_list.diaoluo_slogan.raw_image:LoadSprite(slogan_bundle, slogan_asset, function()
        self.node_list.diaoluo_slogan.raw_image:SetNativeSize()
    end)

    -- local count_bg_bundle, count_bg_asset = ResPath.GetNewFestivalRawImages("diaoluo_di")
    -- self.node_list.diaoluo_count_bg.raw_image:LoadSprite(count_bg_bundle, count_bg_asset, function ()
    --     self.node_list.diaoluo_count_bg.raw_image:SetNativeSize()
    -- end)

    local timer_bg_bundle, timer_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di2")
    self.node_list.diaoluo_timer_bg.image:LoadSprite(timer_bg_bundle, timer_bg_asset, function ()
        self.node_list.diaoluo_timer_bg.image:SetNativeSize()
    end)

    local client_show_cfg = NewFestivalActivityWGData.Instance:GetBossDropOtherCfg()
    self.node_list.diaoluo_rule_content.text.text = client_show_cfg.rule_content
    self.node_list.diaoluo_rule_scroll.scroll_rect.verticalNormalizedPosition = 1
    self.diaoluo_time_color = client_show_cfg.time_part_color
    self.diaoluo_num_color = client_show_cfg.num_color
    self.node_list.diaoluo_rule_content.text.color = Str2C3b(client_show_cfg.rule_color)
    self.node_list.diaoluo_time.text.color = Str2C3b(client_show_cfg.timer_color)
    self.node_list.diaoluo_drop_count.text.color = Str2C3b(client_show_cfg.drop_count_color)
end

function NewFestivalActivityView:CreateBossDropCountDown()
    if CountDownManager.Instance:HasCountDown(boss_drop_timer_key) then
        return
    end

    local time, total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_JRHD_JFDL)
    if time > 0 then
        self:BossDropUpdateCountDown(total_time - time, total_time)
        CountDownManager.Instance:AddCountDown(boss_drop_timer_key, BindTool.Bind1(self.BossDropUpdateCountDown, self), BindTool.Bind1(self.BossDropCompleteCallBack, self), nil, time, 1)
    else
        self:BossDropCompleteCallBack()
    end
end

function NewFestivalActivityView:BossDropUpdateCountDown(elapse_time, total_time)
    if self.node_list and self.node_list.diaoluo_time then
        self.node_list.diaoluo_time.text.text = string.format(Language.BossDrop.ActEndTime, self.diaoluo_time_color, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
    end
end

function NewFestivalActivityView:BossDropCompleteCallBack()
    if self.node_list and self.node_list.diaoluo_time then
        self.node_list.diaoluo_time.text.text = Language.Common.ActivityIsEnd
    end
end