MergeSPRankGoldRender = MergeSPRankGoldRender or BaseClass(BaseRender)

function MergeSPRankGoldRender:LoadCallBack()
    self.item_list = AsyncListView.New(MergeSPRankItem, self.node_list.item_list)

    self.is_load_complete = true

	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_list)
	if self.parent_scroll_rect then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end
function MergeSPRankGoldRender:__delete()
    if self.nested_scroll_rect then
		self.nested_scroll_rect:DeleteMe()
		self.nested_scroll_rect = nil
	end
    self.is_load_complete = nil
    self.parent_scroll_rect = nil

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end
function MergeSPRankGoldRender:OnFlush()
    if not self.data then
        return
    end

    --奖励格子列表
    local item_data = SortDataByItemColor(self.data.item_list)
    self.item_list:SetDataList(item_data)

    self.node_list.rank_value.text.text = string.format(
        Language.MergeSPRank.RankGoldReawrdDesc,
        self.data.rank_hight,
        self.data.rank_low,
        self.data.reach_value)
    -- if self.data.rank <= 3 then
    --     local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank)
    --     self.node_list["rank_img"].image:LoadSprite(bundle, asset, function ()
    --         self.node_list["rank_img"].image:SetNativeSize()
    --     end)
    -- else
    --     self.node_list["rank_text"].text.text = self.data.rank
    -- end
    --self.node_list["rank_img"]:SetActive(self.data.rank <= 3)
    --self.node_list["rank_text"]:SetActive(self.data.rank > 3)

    -- if self.data.is_empty_value then
    --     self.node_list.name.text.text = Language.MergeSPRank.XuWeiYiDai
    --     self.node_list.rank_value.text.text = string.format(Language.MergeSPRank.NeedConsumeGold, self.data.reach_value)
    --     return
    -- end

    -- if not MergeSpecialRankWGData.Instance:GetIsEnd() then
    --     self.node_list.name.text.text = Language.MergeSPRank.BaoMi
    -- else
    --     self.node_list.name.text.text = self.data.name
    -- end

    -- self.node_list.rank_value.text.text = self.data.rank_value
end

function MergeSPRankGoldRender:SetParentScrollRect(scroll_rect)
	self.parent_scroll_rect = scroll_rect

	if self.is_load_complete then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end