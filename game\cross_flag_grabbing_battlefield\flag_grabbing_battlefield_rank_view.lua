FlagGrabbingBattleFieldRankView = FlagGrabbingBattleFieldRankView or BaseClass(SafeBaseView)

function FlagGrabbingBattleFieldRankView:__init()
    self.view_layer = UiLayer.MainUIHigh
	self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_mainui_rank_view")
end

function FlagGrabbingBattleFieldRankView:LoadCallBack()
    if not self.fgb_rank_list then
		self.fgb_rank_list = AsyncListView.New(FGBRankListItemRender, self.node_list.fgb_rank_list)
        self.fgb_rank_list:SetStartZeroIndex(false)
	end

    if not self.my_rank_item then
        self.my_rank_item = FGBRankListItemRender.New(self.node_list.fgb_my_rank)
    end

    self.main_top_arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind(self.HideOrShowRankPanel,self))
	--self.node_list.title_view_name.text.text = Language.FlagGrabbingBattlefield.FGBRankPanelTitle
end

function FlagGrabbingBattleFieldRankView:ReleaseCallBack()
	if self.fgb_rank_list then
		self.fgb_rank_list:DeleteMe()
		self.fgb_rank_list = nil
	end

    if self.my_rank_item then
        self.my_rank_item:DeleteMe()
        self.my_rank_item = nil
    end

    if self.main_top_arrow_click_event then
        GlobalEventSystem:UnBind(self.main_top_arrow_click_event)
        self.main_top_arrow_click_event = nil
    end
end

function FlagGrabbingBattleFieldRankView:OnFlush()
    local rank_list, my_rank_data = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBScoreRankList()
    local has_rank_list = not IsEmptyTable(rank_list)
    local has_my_rank_data = not IsEmptyTable(my_rank_data)

    self.node_list.no_rank_list_flag:CustomSetActive(not has_rank_list)
    self.node_list.fgb_rank_list:CustomSetActive(has_rank_list)
    self.node_list.no_my_rank_flag:CustomSetActive(not has_my_rank_data)

    if has_rank_list then
        self.fgb_rank_list:SetDataList(rank_list)
    end

    if has_my_rank_data then
        self.my_rank_item:SetData(my_rank_data)
    end

    self.node_list.fgb_my_rank:CustomSetActive(has_my_rank_data)
end

function FlagGrabbingBattleFieldRankView:HideOrShowRankPanel(is_on)
    if self.is_show_on ~= is_on then
        self.is_show_on = is_on
        if is_on then
            self.node_list["both_move_right_node"].rect:DOAnchorPosX(50, 0.4)
            self.node_list["both_move_right_node"].canvas_group:DoAlpha(1, 0, 0.4)
            self.node_list["both_move_right_node"].canvas_group.blocksRaycasts = false
        else
            self.node_list["both_move_right_node"].rect:DOAnchorPosX(-0, 0.4)
            self.node_list["both_move_right_node"].canvas_group:DoAlpha(0, 1, 0.4)
            self.node_list["both_move_right_node"].canvas_group.blocksRaycasts = true
        end
    end
end

-----------------------------------FGBRankListItemRender----------------------------------
FGBRankListItemRender = FGBRankListItemRender or BaseClass(BaseRender)

function FGBRankListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_top_3 = self.data.rank <= 3
    self.node_list.rank_num_image:CustomSetActive(is_top_3)
	if is_top_3 then
        self.node_list.rank_num_image.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank))
	end

    self.node_list.role_name.text.text = self.data.name
    self.node_list.rank_text.text.text = self.data.rank
    self.node_list.role_score.text.text = self.data.score
    local team_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(self.data.camp)
    local color = team_cfg.seq == 0 and "#377ace" or "#bc3147"
    self.node_list.role_team_name.text.text = ToColorStr(team_cfg.camp_name, color)
end