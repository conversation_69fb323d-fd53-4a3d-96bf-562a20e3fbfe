BossPrivilegeUpgradeView = BossPrivilegeUpgradeView or BaseClass(SafeBaseView)

function BossPrivilegeUpgradeView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_privilege_upgrade")
end

function BossPrivilegeUpgradeView:LoadCallBack()
	if self.cur_attr_list == nil then
        self.cur_attr_list = {}
        local node_num = self.node_list["cur_attr_list"].transform.childCount
        for i = 1, node_num do
            self.cur_attr_list[i] = BossPrivilegeUpgradeAttrRender.New(self.node_list["cur_attr_list"]:FindObj("attr_" .. i))
			self.cur_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    if self.next_attr_list == nil then
        self.next_attr_list = {}
        local node_num = self.node_list["next_attr_list"].transform.childCount
        for i = 1, node_num do
            self.next_attr_list[i] = BossPrivilegeUpgradeAttrRender.New(self.node_list["next_attr_list"]:FindObj("attr_" .. i))
			self.next_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

	XUI.AddClickEventListener(self.node_list.active_btn, BindTool.Bind(self.OnClickLevelUp, self))
end

function BossPrivilegeUpgradeView:ReleaseCallBack()
	if self.cur_attr_list then
	    for k, v in pairs(self.cur_attr_list) do
	        v:DeleteMe()
	    end

	    self.cur_attr_list = nil
    end

    if self.next_attr_list then
	    for k, v in pairs(self.next_attr_list) do
	        v:DeleteMe()
	    end

	    self.next_attr_list = nil
    end
end

function BossPrivilegeUpgradeView:OnFlush()
	local cur_level ,max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
	local is_max_level = cur_level >= max_level
	local cur_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesListCfgByLevel(cur_level)
	for i = 1, 5 do
		local data = cur_times_list[i]
		local is_show = false
		if data then
			local active = data.cfg.is_lock == 0
			is_show = active
			if active then
				local str_color = data.times > 0 and IS_CAN_COLOR.ENOUGH or COLOR3B.D_RED
				local times_str = ToColorStr(data.times, str_color)
				self.node_list["cur_fb_name" .. i].text.text = data.cfg.scene_name
				self.node_list["cur_fb_times" .. i].text.text = times_str
			end
		end

		self.node_list["cur_fb_name" .. i]:CustomSetActive(is_show)
		self.node_list["cur_fb_times" .. i]:CustomSetActive(is_show)
	end


	local cur_attr_list = BossPrivilegeWGData.Instance:GetCurPrivilegeAttrList(cur_level)
	for k,v in pairs(self.cur_attr_list) do
		local attr = cur_attr_list[k]
		local attr_value = attr and attr.attr_value or 0
		local str_color = attr_value > 0 and IS_CAN_COLOR.ENOUGH or COLOR3B.D_RED
        v:SetAttrSpecialColor(str_color)
        v:SetData(attr)
    end

	self.node_list.next_level_times:CustomSetActive(not is_max_level)
	self.node_list.next_attr_list:CustomSetActive(not is_max_level)
	self.node_list.privilege_desc:CustomSetActive(not is_max_level)
	self.node_list.active_btn:CustomSetActive(not is_max_level)
	self.node_list.max_flag:CustomSetActive(is_max_level)
	local next_level = cur_level >= max_level and max_level or cur_level + 1
	local next_privilege_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesListCfgByLevel(next_level)
	for i = 1, 5 do
		local data = next_privilege_times_list[i]
		local is_show = false
		if data then
			local active = data.cfg.is_lock == 0
			is_show = active and not is_max_level
			if active then
				local str_color = data.times > 0 and IS_CAN_COLOR.ENOUGH or COLOR3B.D_RED
				local times_str = ToColorStr(data.times, str_color)
				self.node_list["next_fb_name" .. i].text.text = data.cfg.scene_name
				self.node_list["next_fb_times" .. i].text.text = times_str
			end
		end

		self.node_list["next_fb_name" .. i]:CustomSetActive(is_show)
		self.node_list["next_fb_times" .. i]:CustomSetActive(is_show)
	end

	local next_attr_list = BossPrivilegeWGData.Instance:GetCurPrivilegeAttrList(next_level)
	for k,v in pairs(self.next_attr_list) do
		local attr = next_attr_list[k]
		local attr_value = attr and attr.attr_value or 0
		local str_color = attr_value > 0 and IS_CAN_COLOR.ENOUGH or COLOR3B.D_RED
        v:SetAttrSpecialColor(str_color)
		v:SetData(attr)
	end

	local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
	local active_str = ""
	local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive()
	if is_active then
		if next_times_list.condition_type == 1 then --灵玉类型
			active_str = string.format(Language.BossPrivilege.BtnUpgradeTxt, next_times_list.condition_value)
		elseif next_times_list.condition_type == 2 then --直购类型
			local price = RoleWGData.GetPayMoneyStr(next_times_list.condition_value, next_times_list.rmb_type, next_times_list.rmb_seq)
			active_str = string.format(Language.BossPrivilege.BtnUpgradeTxt2, price)
		end
	else
		local times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(1)
		active_str = string.format(Language.BossPrivilege.BtnActiveTxt, times_list.condition_value)
	end
	self.node_list.btn_text.text.text = active_str
end

function BossPrivilegeUpgradeView:OnClickLevelUp()
	local is_active = BossPrivilegeWGData.Instance:GetBossPrivilegeIsActive() --是否激活过
	if not is_active then                                                  --激活
		local times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(1)

		local ok_func = function()
			local enough = RoleWGData.Instance:GetIsEnoughUseGold(times_list.condition_value)
			if enough then
				BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.LEVEL_UP) --升级特权请求(0为激活)
				--首次激活自动开启特权
				BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.SET_STATUS,
					BOSS_DROP_PRIVILEGE_OPEN_TYPE.OPEN)
			else
				VipWGCtrl.Instance:OpenTipNoGold()
			end
		end

		local str = string.format(Language.BossPrivilege.DiscountBuyTips, times_list.condition_value)
		local check_string = "boss_privilege"
		TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string, nil)
	else
		local cur_level, max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
		local next_level = cur_level >= max_level and max_level or cur_level + 1
		local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
		if not next_times_list or cur_level >= max_level then
			return
		end

		local text_dec = ""
		local is_enough_money
		if next_times_list.condition_type == 1 then --灵玉类型
			text_dec = string.format(Language.BossPrivilege.ArrayBuyTips1, next_times_list.condition_value)
			is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(next_times_list.condition_value)
		elseif next_times_list.condition_type == 2 then --直购类型
			local price = RoleWGData.GetPayMoneyStr(next_times_list.condition_value, next_times_list.rmb_type,
				next_times_list.rmb_seq)
			text_dec = string.format(Language.BossPrivilege.ArrayBuyTips2, price)
		end

		local ok_func = function()
			if next_times_list.condition_type == 1 then
				if is_enough_money then
					BossPrivilegeWGCtrl.Instance:SendBossPrivilegeReq(BOSS_DROP_PRIVILEGE_OPERATE_TYPE.LEVEL_UP)
				else
					VipWGCtrl.Instance:OpenTipNoGold()
				end
			elseif next_times_list.condition_type == 2 then
				RechargeWGCtrl.Instance:Recharge(next_times_list.condition_value, next_times_list.rmb_type,
					next_times_list.rmb_seq)
			end
		end

		TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
	end
end

-- function BossPrivilegeUpgradeView:FlushCurPrivilegePanel()
-- 	local cur_level ,max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
-- 	local next_level = cur_level >= max_level and max_level or cur_level + 1
-- 	local cur_level ,max_level = BossPrivilegeWGData.Instance:GetCurPrivilegeLevel()
-- 	local next_level = cur_level >= max_level and max_level or cur_level + 1
-- 	local cur_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(cur_level)
--     local next_times_list = BossPrivilegeWGData.Instance:GetPrivilegeTimesCfg(next_level)
-- 	local cur_attr_list = BossPrivilegeWGData.Instance:GetCurPrivilegeAttrList(cur_level)
-- 	local next_attr_list = BossPrivilegeWGData.Instance:GetCurPrivilegeAttrList(next_level)

--     for i = 1, 5 do
-- 		local is_hide = false
-- 		local func_name = BossPrivilegeWGData.Instance:GetBossFuncOpenNameBySeq(i - 1)
--         if func_name and func_name ~= "" then --只屏蔽掉要求的，不屏蔽没开启的。
--             is_hide = FunOpen.Instance:GetFunByName(func_name).trigger_param >= 9999
--         end

-- 		self.node_list["cur_fb_name" .. i]:SetActive(not is_hide)
-- 		self.node_list["next_fb_name" .. i]:SetActive(not is_hide)
-- 		self.node_list["cur_fb_times" .. i]:SetActive(not is_hide)
-- 		self.node_list["next_fb_times" .. i]:SetActive(not is_hide)

-- 		if not is_hide then
-- 			local times
--         	if i > #cur_times_list.type then
--         	    times = 0
--         	else
--         	    times = tonumber(cur_times_list.type[i].times)
--         	end
--         	local name = Language.BossPrivilege.BossFBName[i]
--         	local str_color = times > 0 and "#00730BFF" or "#970101FF"
--         	local times_str = ToColorStr(times, str_color)
--         	self.node_list["cur_fb_name" .. i].text.text = name
--         	self.node_list["next_fb_name" .. i].text.text = name
--         	self.node_list["cur_fb_times" .. i].text.text = times_str

-- 		end
--     end

-- 	for k,v in pairs(self.cur_attr_list) do
--         v:SetData(cur_attr_list[k])
--     end

-- 	self.node_list.cur_level.text.text = string.format(Language.BossPrivilege.UpgradeLevel, cur_level) 

-- 	if cur_level < max_level then --没有满级
-- 		for i = 1, 5 do
-- 			local times
-- 			if i > #next_times_list.type then
-- 				times = 0
-- 			else
-- 				times = tonumber(next_times_list.type[i].times)
-- 			end
-- 			local str_color = times > 0 and "#00730BFF" or "#970101FF"
-- 			local times_str = ToColorStr(times, str_color)
-- 			self.node_list["next_fb_times" .. i].text.text = times_str
-- 		end

-- 		self.node_list.next_level.text.text = string.format(Language.BossPrivilege.UpgradeLevel, next_level)

-- 		for k,v in pairs(self.next_attr_list) do
-- 			v:SetData(next_attr_list[k])
-- 		end

-- 		local btn_str = ""
-- 		if next_times_list.condition_type == 1 then --灵玉类型
--         	btn_str = string.format(Language.BossPrivilege.ArrayBuyStr, next_times_list.condition_value)
-- 		elseif next_times_list.condition_type == 2 then  --直购类型
-- 			local price = RoleWGData.GetPayMoneyStr(next_times_list.condition_value, next_times_list.rmb_type, next_times_list.rmb_seq)
-- 			btn_str = string.format(Language.BossPrivilege.ArrayBuyStr2, price)
--   		end
-- 	end
-- end

BossPrivilegeUpgradeAttrRender = BossPrivilegeUpgradeAttrRender or BaseClass(CommonAddAttrRender)
function BossPrivilegeUpgradeAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	local is_per = self.data.is_per or EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
	if not is_per and self.need_per then
		is_per = self.need_per
		self.need_per = false
	end

	local per_desc = is_per and "%" or ""
    local value_str = (is_per and self.data.attr_value / 100 or self.data.attr_value) or 0
	local attr_name = self.data.attr_name or EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
    self.node_list.attr_name.text.text = attr_name
    self.node_list.attr_value.text.text = value_str .. per_desc

    self.node_list.arrow:SetActive(true)
    self.node_list.add_value:SetActive(true)
    self.node_list.arrow.image.enabled = true
    if self.data.add_value and self.data.add_value > 0 then
        value_str = is_per and self.data.add_value / 100 or self.data.add_value
        self.node_list.add_value.text.text = value_str .. per_desc
    else
        if self.real_hide_next then
            self.node_list.arrow:SetActive(false)
            self.node_list.add_value:SetActive(false)
		else
            self.node_list.arrow.image.enabled = false
            self.node_list.add_value.text.text = ""
        end
    end

	if self.data.add_value and self.data.add_value > 0 and self.arrow_tweener_need then
		self.node_list.arrow:SetActive(false)
		self.node_list.add_value:SetActive(true)
	end

	local special_attr_color = SPECIAL_ATTR_COLOR[self.data.attr_str]
	if special_attr_color == nil then
		special_attr_color = self.special_color
	end

	if nil ~= special_attr_color then
		self.node_list.attr_value.text.text = ToColorStr(self.node_list.attr_value.text.text, special_attr_color)
	end

    self.view:SetActive(true)
end