﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

/// <summary>
/// 上下漂浮动画，全局频率一致
/// </summary>
public class FloatTween : MonoBehaviour {
	static float floatValue = 0;
	static int behaviourCount = 0;
	public static int speed = 1;
	public float distance = 10;
	Vector3 originalPos;
	// Use this for initialization
	void Start () {
		originalPos = transform.localPosition;
	}
	
	public static void SetSpeed(int newSpeed)
	{
		speed = newSpeed;
	}

	void OnEnable()
	{
		if(behaviourCount == 0)
		{
			if(speed == 0)
			{
				speed = 1;
			}
			DOTween.To((x) => floatValue = x, -1, 1, 1/speed).SetLoops(-1, LoopType.Yoyo).SetEase(Ease.Linear);
		}
		behaviourCount++;
	}

	void OnDisable()
	{
		behaviourCount--;
	}

	// Update is called once per frame
	void Update () {
		transform.localPosition = new Vector3(originalPos.x, originalPos.y + floatValue * distance, originalPos.z);
	}
}
