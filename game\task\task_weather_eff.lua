TaskWeatherEff = TaskWeatherEff or BaseClass(VisibleObj)

-- 任务暂未实现
-- 暂时屏蔽等级
-- 触发随机天气系统的等级
local TRIGGER_LEVEL = 0
local Effect_Name =
{
	[1] = {bundle_name = "effects2/prefab/environment/tongyong_prefab", assst_name = "tianqi_guangshu"},
	[2] = {bundle_name = "effects2/prefab/environment/tongyong_prefab", assst_name = "xiayu", voice = "thunder"},
	[3] = {bundle_name = "effects2/prefab/environment/tongyong_prefab", assst_name = "Xddt01_xeu"},
	[4] = {bundle_name = "effects2/prefab/environment/tongyong_prefab", assst_name = "dalei", voice = "thunder"},
}

function TaskWeatherEff:__init()
	self.shield_obj_type = ShieldObjType.Weather
	self:CreateShieldHandle()
	self.task_change = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE, BindTool.Bind(self.OnTaskChange, self))
	self.change_scene_handle = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self.scene_quit_handle = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind1(self.RemoveWeatherEff, self))
end

function TaskWeatherEff:__delete()
	if self.task_change then
		GlobalEventSystem:UnBind(self.task_change)
		self.task_change = nil
	end
	if self.change_scene_handle then
		GlobalEventSystem:UnBind(self.change_scene_handle)
		self.change_scene_handle = nil
	end
	if self.scene_quit_handle then
		GlobalEventSystem:UnBind(self.scene_quit_handle)
		self.scene_quit_handle = nil
	end
	if self.close_weather_change then
		GlobalEventSystem:UnBind(self.close_weather_change)
		self.close_weather_change = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

	self:RemoveWeatherEff()
end


function TaskWeatherEff:OnTaskChange(task_event_type, task_id)
	local is_show, name, asset, voice = self:CheckTask()
	if is_show then
		self:RemoveWeatherEff()
		self:ShowWeatherEff(is_show, name, asset, voice)
	else
		local level = GameVoManager.Instance:GetMainRoleVo().level
		if level < TRIGGER_LEVEL then
			self:ShowWeatherEff(false)
		end
	end
end

function TaskWeatherEff:OnSceneChangeComplete()
	self:CheckCanShowEff()
end

function TaskWeatherEff:CheckCanShowEff()
	local is_show, name, asset, voice = self:CheckTask()
	if is_show then
		self:ShowWeatherEff(is_show, name, asset, voice)
	else
		local level = GameVoManager.Instance:GetMainRoleVo().level
		local weather_cfg = self:GetWeather()

		if level >= TRIGGER_LEVEL and weather_cfg then
			local asset = Effect_Name[weather_cfg.effect_name]
			self:ShowWeatherEff(true, asset.assst_name, asset.bundle_name, asset.voice)
		else
			self:ShowWeatherEff(false)
		end
	end
end

function TaskWeatherEff:GetWeather(scene_id)
	-- body
	local fb_config = ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto")
	scene_id = scene_id or Scene.Instance:GetSceneId()
	if not fb_config.scene_weather then return nil end
	for k,v in pairs(fb_config.scene_weather) do
		if v.scene_id == scene_id then
			return v
		end
	end
	return nil
end

function TaskWeatherEff:CheckTask()
	local zhu_task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
	if zhu_task_list[1] then
		return TaskWGData.Instance:ShowWeatherEff(zhu_task_list[1])
	else
		return false
	end
end

local weather_eff_name = nil
local show_voice = nil
function TaskWeatherEff:ShowWeatherEff(is_show, name, asset, voice)
	if IsLowMemSystem then
		return
	end
	show_voice =voice
	if is_show and voice and voice ~= "" then
		if nil == self.time_quest then
			self.time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.ShowVioce, self, voice), 8)
		end
	elseif self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	if IsNil(MainCamera) or nil == MainCamera.transform  then
		print_warning("MainCamera not exist", asset, name)
		return
	end
	weather_eff_name = is_show and name or nil
	if weather_eff_name and (IsNil(self.weather_eff_obj) or weather_eff_name ~= name) then
		self.weather_eff = AllocAsyncLoader(self, "WeatherEff")
		self.weather_eff:SetParent(MainCamera.transform)
		self.weather_eff:Load(asset, name, function(obj)
			if nil == obj or IsNil(MainCamera) or nil == MainCamera.transform then
				print_warning("obj not exist", asset, name)
				return
			end
			if IsNil(MainCamera) or nil == MainCamera.transform  then
				print_warning("MainCamera not exist", asset, name)
				return
			end
			self:UpdateWeatherActive()
		end)
	elseif not weather_eff_name then
		self:RemoveWeatherEff()
	end
end

function TaskWeatherEff:ShowVioce()
	local flag = false --SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_WEATHWE)
	if not flag and show_voice and show_voice ~= "" then
		AudioManager.PlayAndForget("audios/sfxs/other", show_voice)
	end
end

function TaskWeatherEff:RemoveWeatherEff()
	if self.weather_eff then
		self.weather_eff:Destroy()
	end
	self.weather_eff = nil
	self.weather_eff_obj = nil
end

function TaskWeatherEff:SetGuideFbEff(index)
	self:RemoveWeatherEff()
	local asset = Effect_Name[index]
	self:ShowWeatherEff(true, asset.assst_name, asset.bundle_name, asset.voice)
end

function TaskWeatherEff:VisibleChanged(visible)
	self:UpdateWeatherActive()
end

function TaskWeatherEff:UpdateWeatherActive()
	if not IsNil(self.weather_eff_obj) then
		self.weather_eff_obj.gameObject:SetActive(self:GetVisiable())
	end
end