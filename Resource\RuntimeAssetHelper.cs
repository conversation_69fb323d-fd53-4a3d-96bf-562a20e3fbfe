﻿using UnityEngine.Networking;
using System.IO;
using System;
using System.Collections;
using Nirvana;
using UnityEngine;

public static class RuntimeAssetHelper
{
    public static void InsureDirectory(string path)
    {
        var dir = Path.GetDirectoryName(path);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
    }

    public static void WriteWebRequestData(string path, UnityWebRequest unityWebRequest)
    {
        InsureDirectory(path);
        File.WriteAllBytes(path, unityWebRequest.downloadHandler.data);
    }

    public static bool TryWriteWebRequestData(string path, UnityWebRequest unityWebRequest)
    {
        if (null == unityWebRequest)
            return false;

        byte[] sourceData = unityWebRequest.downloadHandler.data;
        if (sourceData.Length <= 0)
            return false;

        bool succ = true;
        try
        {
            InsureDirectory(path);
            var streamWriter = File.Create(path);
            streamWriter.Write(sourceData, 0, sourceData.Length);
            streamWriter.Dispose();
            streamWriter.Close();
        }
        catch (System.Exception)
        {
            succ = false;
        }

        return succ;
    }

    public static WriteHandle TryWriteWebRequestDataByStream(string path, UnityWebRequest unityWebRequest, Action<bool> cb, int count = 100 * 1024)
    {
        var co = Scheduler.RunCoroutine(WriteWebRequestDataByStream(path, unityWebRequest, cb, count));
        return new WriteHandle(co);
    }

    private static IEnumerator WriteWebRequestDataByStream(string path, UnityWebRequest unityWebRequest, Action<bool> cb, int count)
    {
        if (null == unityWebRequest)
        {
            cb(false);
            yield break;
        }

        byte[] sourceData = unityWebRequest.downloadHandler.data;
        if (sourceData.Length <= 0)
        {
            cb(false);
            yield break;
        }

        InsureDirectory(path);
        bool succ = true;
        var streamWriter = File.Create(path);
        int offset = 0;
        while (offset < sourceData.Length)
        {
            count = Math.Min(count, sourceData.Length - offset);
            try
            {
                streamWriter.Write(sourceData, offset, count);
            }
            catch (System.Exception)
            {
                succ = false;
                break;
            }
            offset += count;
            yield return 0;
        }

        streamWriter.Dispose();
        streamWriter.Close();
        cb(succ);
    }

    public static string IntToString(int value, string format)
    {
        return value.ToString(format);
    }

    // 删除文件夹（及文件夹下所有子文件夹和文件）
    public static void DeleteDirectory(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
            return;

        foreach (string d in Directory.GetFileSystemEntries(directoryPath))
        {
            if (File.Exists(d))
            {
                FileInfo fi = new FileInfo(d);
                if (fi.Attributes.ToString().IndexOf("ReadOnly") != -1)
                    fi.Attributes = FileAttributes.Normal;
                DeleteFile(d);
            }
            else
                DeleteDirectory(d);
        }
        try
        {
            Directory.Delete(directoryPath);
        }
        catch (Exception e)
        {
            Debug.LogError(e.Message);
        }
    }

    // 删除文件
    private static void DeleteFile(string filePath)
    {
        if (File.Exists(filePath))
        {
            try
            {
                File.Delete(filePath);
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }
        }
    }

    public struct WriteHandle
    {
        private Coroutine co;
        public WriteHandle(Coroutine co)
        {
            this.co = co;
        }

        public void Stop()
        {
            if (null != this.co)
            {
                Scheduler.StopShcCoroutine(co);
            }
        }
    }
}
