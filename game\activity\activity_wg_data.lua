
ActivityWGData = ActivityWGData or BaseClass(BaseWGCtrl)

--需要开房间活动的类型
ActivityWGData.DoubleSideFBType = {
	[ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG] = 1,
	[ACTIVITY_TYPE.YEZHANWANGCHENG] = 1,
	-- [ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT] = 1,
	-- [ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF] = 1,
}

function ActivityWGData:__init()
	if ActivityWGData.Instance then
		ErrorLog("[ActivityWGData] attempt to create singleton twice!")
		return
	end
	ActivityWGData.Instance =self

	self.activity_list = {}									-- 活动信息
	self.room_info_list = {}								-- 房间信息

	self.act_change_callback = {}

	self.next_monster_invade_time = 0
	self.limit_level_list = {}
	self:DailyInit()
	self:InitActivityCfg()
	self.hide_active_list = {}

	self.double_side_act_finish_list = {}
	self.main_act_open_day_list = {}

	self.act_tip_cfg_map = ConfigManager.Instance:GetAutoConfig("daily_activity_auto").activity_tip

	self.zhuxie_reward_show  =  ListToMapList(ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").boss_reward_show, "boss_id")
	self.zhuxie_reward  =  ListToMap(ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").kill_hurt_rank_reward, "rank")

	self.kf_zhuxie_reward_show  =  ListToMapList(ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").boss_reward_show, "boss_id")
	self.kf_zhuxie_reward =  ListToMap(ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").kill_hurt_rank_reward, "rank")

	self.daily_activity_daily_cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto").daily
	--动态运营活动表
	self.operation_activity_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").operation_activity_dec
	self.operation_activity_type_cfg = ListToMap(self.operation_activity_cfg, "activity_type")
end

function ActivityWGData:__delete()
	ActivityWGData.Instance = nil
	self.hide_active_list = {}
end

--开房间活动是否已参与结束
function ActivityWGData:SetSCDoubleSideFBIsFinish(info)
	if info then
		self.double_side_act_finish_list[info.activity_type] = info.is_activity_fb_finish
		if self:GetActDoubleSideFBIsFinish(info.activity_type) and ActivityWGData.DoubleSideFBType[info.activity_type] then
			if info.activity_type ~= ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG and info.activity_type ~= ACTIVITY_TYPE.YEZHANWANGCHENG then
				MainuiWGCtrl.Instance:ActivitySetButtonVisible(info.activity_type, false)
			end
			ActivityWGCtrl.Instance:RemoveActNotice(info.activity_type)
		end
	end
end

function ActivityWGData:GetActDoubleSideFBIsFinish(act_type)
	if self.double_side_act_finish_list[act_type] then
		local is_finish = self.double_side_act_finish_list[act_type] or 0
		return is_finish > 0
	end
	return false
end

function ActivityWGData:SetActivityStatus(activity_type, status, next_time, start_time, end_time, open_type)
	if IS_AUDIT_VERSION then
		return
	end
	if activity_type == ACTIVITY_TYPE.KF_BOSS_FIGHT then
		open_type = 1
	end
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, next_time, open_type)
	end
end

function ActivityWGData:ChangeActivityStatus(activity_type, key, value, need_push)
	if self.activity_list[activity_type] and self.activity_list[activity_type][key] then
		self.activity_list[activity_type][key] = value
		if need_push then
			local data_list = self.activity_list[activity_type]
			for k, v in pairs(self.act_change_callback) do
				v(activity_type, data_list.status, data_list.next_time, data_list.open_type)
			end
		end
	end
end

--设置8-11天奖励状态
function ActivityWGData:SetTianShenRoadActivityState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}

	local tianshen_road_state = ACTIVITY_STATUS.CLOSE
	for i = ACTIVITY_TYPE.GOD_DENGLU_YOULI, ACTIVITY_TYPE.GOD_DOUBLE_JIANGLI do
		if self.activity_list[i] ~= nil and self.activity_list[i].status == ACTIVITY_STATUS.OPEN then
			tianshen_road_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	-- 20003 客户端自己的活动入口
	if self.activity_list[ACTIVITY_TYPE.GOD_TIANSHENROAD] == nil then
		if status == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.GOD_TIANSHENROAD, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if tianshen_road_state == ACTIVITY_STATUS.CLOSE then
			self:SetActivityStatus(ACTIVITY_TYPE.GOD_TIANSHENROAD, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.GOD_TIANSHENROAD] = nil
		end
	end

	if activity_type == ACTIVITY_TYPE.GOD_DOUBLE_JIANGLI then
		MainuiWGCtrl.Instance:FlushDuoBei()
	elseif activity_type == ACTIVITY_TYPE.GOD_JIANGLIN then
		-- self:SetActivityStatus(4, status, next_time, start_time, end_time, open_type)
	elseif activity_type == ACTIVITY_TYPE.GOD_CHONGBANG then
		for k, v in pairs(self.act_change_callback) do
			v(activity_type, status, next_time, open_type)
		end
	end
end

--设置12-14天活动状态
function ActivityWGData:SetQuanMinBeiZhanActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}

	local beizhan_state = ACTIVITY_STATUS.CLOSE
	for i = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI, ACTIVITY_TYPE.RAND_ACT_BEIZHAN_BEIZHANHAOLI do
		if self.activity_list[i] ~= nil and self.activity_list[i].status == ACTIVITY_STATUS.OPEN then
			beizhan_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	-- 20005 客户端自己的活动入口
	if self.activity_list[ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN] == nil then
		if status == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if beizhan_state == ACTIVITY_STATUS.CLOSE then
			self:SetActivityStatus(ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN] = nil
		end
	end

	if activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DUOBEILAIXI then
		MainuiWGCtrl.Instance:FlushDuoBei()
	elseif activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI then
		-- self:SetActivityStatus(5, status, next_time, start_time, end_time, 1)
	elseif activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK then
		for k, v in pairs(self.act_change_callback) do
			v(activity_type, status, next_time, open_type)
		end
	end
	
	BZTurnTableWGData.Instance:ClearActCache(activity_type, status)
end

--活动状态(仙器解封)
function ActivityWGData:SetXianQiJieFengActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}

	local xianqi_act_state = ACTIVITY_STATUS.CLOSE
	for i = ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI, ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_BEIZHANHAOLI do
		if self.activity_list[i] ~= nil and self.activity_list[i].status == ACTIVITY_STATUS.OPEN then
			xianqi_act_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	-- 20022 客户端自己的活动入口
	if self.activity_list[ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG] == nil then
		if status == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if xianqi_act_state == ACTIVITY_STATUS.CLOSE then
			self:SetActivityStatus(ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.ACTIVITY_XIANQI_JIEFENG] = nil
		end
	end

	if activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI then
		MainuiWGCtrl.Instance:FlushDuoBei()
	elseif activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI then
		-- self:SetActivityStatus(5, status, next_time, start_time, end_time, 1)
	elseif activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LONGHUNRANK then
		for k, v in pairs(self.act_change_callback) do
			v(activity_type, status, next_time, open_type)
		end
	end
	
	-- XQJFTurnTableData.Instance:ClearActCache(activity_type, status)
end

function ActivityWGData:GetActivityStatus()
	return self.activity_list
end

function ActivityWGData:GetActivityStatuByType(activity_type)
	return self.activity_list[activity_type]
end

--获得某个活动是否开启
function ActivityWGData:GetActivityIsOpen(act_type)
	local activity_info = self:GetActivityStatuByType(act_type)
	if nil ~= activity_info and ACTIVITY_STATUS.OPEN == activity_info.status then
		return true
	end
	return false
end

--获得某个活动是否关闭
function ActivityWGData:GetActivityIsClose(act_type)
	local activity_info = self:GetActivityStatuByType(act_type)
	if nil == activity_info or ACTIVITY_STATUS.CLOSE == activity_info.status then
		return true
	end
	return false
end

function ActivityWGData:SetNextMonsterInvadeTime(time)
	self.next_monster_invade_time = time
end

function ActivityWGData:GetNextMonsterInvadeTime()
	return self.next_monster_invade_time
end

--根据类型获得活动配置
function ActivityWGData:GetActivityCfgByType(act_type)
	return self.act_cfg_map[act_type]
end

--获得某个活动是否当天开启
function ActivityWGData:GetActivityIsInToday(act_type)
	local act_cfg = self:GetActivityCfgByType(act_type)
	if act_cfg == nil then
		return false
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local week_day = TimeUtil.FormatSecond3MYHM1(server_time)

	local open_day_list = self.main_act_open_day_list[act_cfg.act_type]
	if not open_day_list then
		if act_cfg.limit_day and "" ~= act_cfg.limit_day then
			open_day_list = {}
			local day_list = Split(act_cfg.limit_day, ":")
			for k,v in pairs(day_list) do
				open_day_list[k] = tonumber(v)
			end
		else
			open_day_list = {}
		end

		self.main_act_open_day_list[act_cfg.act_type] = open_day_list
	end

	local is_open_day = false
	for k, v in pairs(open_day_list) do
		if v == week_day then
			is_open_day = true
			break
		end
	end
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or -1
	if open_day > 0 and open_day <= 3 then							--开服前三天
		if act_type == ACTIVITY_TYPE.XIANMENGZHAN then
			is_open_day = (2 == open_day)
		end
	end
	return is_open_day
end

-- 注册监听活动状态改变
function ActivityWGData:NotifyActChangeCallback(callback)
	for k,v in pairs(self.act_change_callback) do
		if v == callback then
			return
		end
	end
	self.act_change_callback[#self.act_change_callback + 1] = callback
end

-- 取消注册
function ActivityWGData:UnNotifyActChangeCallback(callback)
	for k,v in pairs(self.act_change_callback) do
		if v == callback then
			table.remove(self.act_change_callback, k)
		end
	end
end

function ActivityWGData:SetRoomStatusList(activity_type, room_user_max, room_status_list)
	self.room_info_list[activity_type] = {['activity_type'] = activity_type, ['room_user_max'] = room_user_max, ['room_status_list'] = room_status_list}
end

function ActivityWGData:GetRoomIndex(activity_type)
	local room_data = self.room_info_list[activity_type]
	local room_index = 0
	if room_data then
		local room_user_max = room_data.room_user_max
		local room_list = room_data.room_status_list
		local n = room_user_max
		for k,v in pairs(room_list) do
			if v.is_open == 1 and v.role_num < n then
				n = v.role_num
				room_index = v.index
			end
		end
	end
	return room_index
end

function ActivityWGData:GetRoomStatuList()
	return self.room_info_list
end

function ActivityWGData:GetRoomStatuesByActivityType(activity_type)
	return self.room_info_list[activity_type]
end

function ActivityWGData:GetDailyActivityDailyCfg(act_type)
	return self.daily_activity_daily_cfg[act_type]
end

function ActivityWGData:GetActivityName(act_type)
	local cfg = self:GetDailyActivityDailyCfg(act_type)
	return cfg and cfg.name or tostring(act_type)
end

function ActivityWGData.GetActivityStatusName(status)
	if status == ACTIVITY_STATUS.CLOSE then 				--活动关闭状态
		return "活动关闭状态"
	elseif status == ACTIVITY_STATUS.STANDY then			--活动准备状态
		return "活动准备状态"
	elseif status == ACTIVITY_STATUS.OPEN then				--活动进行中
		return "活动进行中"
	end
	return tostring(status)
end

-- 获取活动剩余时间,结束时间
function ActivityWGData:GetActivityResidueTime(act_type)
	local time = 0
	local next_time = 0
	local activity = self:GetActivityStatuByType(act_type)
	if activity then
		next_time = activity.next_time or TimeWGCtrl.Instance:GetServerTime()
		time = next_time - TimeWGCtrl.Instance:GetServerTime()
	end
	return time, next_time
end

--请求进入活动的房间
function ActivityWGData:OnEnterRoom(activity_type)
	local room_info_list = self:GetRoomStatuesByActivityType(activity_type)
	if nil ~= room_info_list and nil ~= room_info_list.room_status_list then
		-- 选择房间人数最少的进入
		local min_role_num = 9999
		local enter_room_index = 0
		local activity_room_list = room_info_list.room_status_list
		for _, room_status in pairs(activity_room_list) do
			if ACTIVITY_ROOM_STATUS.OPEN == room_status.is_open then
				if room_status.role_num < min_role_num then
					min_role_num = room_status.role_num
					enter_room_index = room_status.index
				end
			end
		end

		local activity_cfg = DailyWGData.Instance:GetActivityConfig(activity_type)

		if nil ~= activity_cfg then
			ActivityWGCtrl.Instance:SendActivityEnterReq(activity_type, enter_room_index)
		end
	elseif activity_type == ACTIVITY_TYPE.GONGCHENGZHAN then
		ActivityWGCtrl.Instance:SendActivityEnterReq(activity_type, 0)
	else
		ActivityWGCtrl.Instance:SendActivityRoomStatusReq(ACTIVITY_TYPE.ZHUXIE)
	end
end

-- 跨服活动
function ActivityWGData:SetCrossRandActivityStatus(activity_type, status, start_time, end_time)
	self.activity_list[activity_type] = {
	["type"] = activity_type,
	["status"] = status,
	["start_time"] = start_time,
	["end_time"] = end_time,
	}

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, end_time)
	end
end

--新加的 跨服活动信息
function ActivityWGData:SetCrossChannelActivityStatus(activity_type, status, start_time, end_time)
	self.activity_list[activity_type] = {
	["type"] = activity_type,
	["status"] = status,
	["start_time"] = start_time,
	["end_time"] = end_time,
	}

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, end_time)
	end
end

function ActivityWGData:InitActivityCfg()
	local act_list_cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto").daily
	self.limit_level_list = {}
	self.act_cfg_map = {}
	for k,v in pairs(act_list_cfg) do
		self.limit_level_list[v.act_type] = v.limit_level
		self.act_cfg_map[v.act_type] = v
	end
end

function ActivityWGData:GetActivityLimitLevelById(act_type)
	if self.limit_level_list[act_type] ~= nil then
		return self.limit_level_list[act_type]
	end
	return 0
end

function ActivityWGData:GetActivityIsOpenByLevel(act_type)
	if self.limit_level_list[act_type] ~= nil then
		return self.limit_level_list[act_type] <= RoleWGData.Instance.role_vo.level
	end
	return false
end

--获取活动开启在开服第几天
function ActivityWGData:GetActivityOpenInServerDay(act_type)
	local activity_info = self:GetActivityStatuByType(act_type)
	if activity_info and activity_info.start_time then
		local start_day = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(activity_info.start_time)
		return start_day
	end

	return 0
end

--获取当前活动开的第几天
function ActivityWGData:GetActivityCurOpenday(act_type)
	local day = 0
	local activity_info = self:GetActivityStatuByType(act_type)
	if activity_info ~= nil and activity_info.status == ACTIVITY_STATUS.OPEN and activity_info.start_time then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local start_time = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(activity_info.start_time)
		local cur_time = TimeWGCtrl.Instance:GetOpenServerDayByTimestamp(server_time)
		day = cur_time - start_time + 1
	end

	return day
end

--返利按钮中的按钮列表
function ActivityWGData:SetHideActivityInfo(act_type,status,act_cfg)
	if status == ACTIVITY_STATUS.CLOSE then
		if self.hide_active_list[act_type] then
			self.hide_active_list[act_type] = nil
		end
	else
		self.hide_active_list[act_type] = {act_cfg = act_cfg}
	end
	if self.hide_active_callback then
		self.hide_active_callback()
	end
end

function ActivityWGData:GetHideActivityInfo()
	return self.hide_active_list
end

--返利按钮事件回调
function ActivityWGData:SetHideCallBack(callback)
	self.hide_active_callback = callback
end


-- 根据活动类型获取拜谒配置数据
function ActivityWGData:GetBaiJieCfgByActivityType(activity_type)
	local worship_cfg = ConfigManager.Instance:GetAutoConfig("worship_auto").other
	for k,v in pairs(worship_cfg) do
		if v.activityde_type == activity_type then
			return v
		end
	end
end

-- 根据活动类型获取拜谒气泡内容
function ActivityWGData:GetBubbleContentListById(activity_type)
	local worship_bubble_cfg = ConfigManager.Instance:GetAutoConfig("worship_auto").bubble_content
	local list = {}
	if worship_bubble_cfg then
		for k,v in pairs(worship_bubble_cfg) do
			if v.activityde_type == activity_type then
				table.insert(list, v)
			end
		end
	end
	return list
end

-- 获取气泡内容 根据场景类型
function ActivityWGData:GetBubbleContentById(scene_id)
	local activity_type = WORSHIP_SCENE_TYPE[scene_id]
	local bubble_list = self:GetBubbleContentListById(activity_type)
	local content = {}
	if bubble_list and #bubble_list > 0 then
		local num = math.random(1, #bubble_list) or 1
		content = bubble_list[num]
	end
	return content
end

function ActivityWGData:GetIsInListByID(scene_id)
	local activity_type = WORSHIP_SCENE_TYPE[scene_id]
	local worship_cfg = self:GetBaiJieCfgByActivityType(activity_type)
	local is_click = false
	if worship_cfg then
		local num = math.random(1, 100)
		is_click = num <= worship_cfg.probability
	end
	return is_click
end

--全场景搜索怪物时间
function ActivityWGData:AllSceneLookMonsterTime(activity_type)
	if ActivityWGData.Instance:GetActivityIsOpen(activity_type) then
		return 3
	else
		return 10000000
	end
end

--诛邪战场先关信息处理
--获取一个未完成的人物id
function ActivityWGData:GetOneTaskID()
	local list = self:GetZhuXieTaskList()
	for i,v in ipairs(list) do
		local task_info = self:GetOneZhuXieTaskInfo(v.cfg.task_id) or {}
		local param_value = task_info.param_value or 0
		local max_value = task_info.max_value or 0
		local is_fetch_reward = task_info.is_fetch_reward or 0
		if is_fetch_reward == 0 and param_value >= max_value and v.cfg.task_type ~= ZhuXieFollow.Task_Type.Role then
			return v.cfg.task_id
		elseif is_fetch_reward == 0 and v.cfg.task_type ~= ZhuXieFollow.Task_Type.Role then
			return v.cfg.task_id
		end
	end
	--随便给的一个任务号
	-- return 2
end

--活动开启状态，包含部分开服活动
function ActivityWGData:GetSomeActivityIsOpen(act_type)
	local activity_info = self:GetActivityStatuByType(act_type)
	if nil ~= activity_info and ACTIVITY_STATUS.OPEN == activity_info.status then
		return true
	end
	if act_type == ACTIVITY_TYPE.RAND_COLLECT_ITEMS then--开服集字
		return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_COLLECT_ITEMS)
	end
	return false
end

--获取诛邪狂暴时刻最小血量百分比
function ActivityWGData:GetZhuXieMinHpPer()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").other[1]
	return other_cfg.HP or 0
end

--获取跨服诛邪狂暴时刻最小血量百分比
function ActivityWGData:GetKFZhuXieMinHpPer()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("cross_zhuxie_auto").other[1]
	return other_cfg.HP or 0
end

function ActivityWGData:GetBossId(is_kf)
	local config = is_kf and "cross_zhuxie_auto" or "zhuxieconfig_auto"
	local other_cfg = ConfigManager.Instance:GetAutoConfig(config).other[1]
	return other_cfg.boss_id or 0
end

--检查活动图标是否显示
function ActivityWGData:IsActShow(act_type)
	if act_type == ACTIVITY_TYPE.GUILD_FB then
		return not GuildWGData.Instance:IsFinishGuildFb()
	elseif act_type == ACTIVITY_TYPE.GUILD_CHUAN_GONG then
		local guild_answer_act = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
		if not IsEmptyTable(guild_answer_act) and (guild_answer_act.status == ACTIVITY_STATUS.STANDY or guild_answer_act.status == ACTIVITY_STATUS.OPEN) then
			return false
		else
			return not GuildAnswerWGData.Instance:HasGetPassExp()
		end
	elseif act_type == ACTIVITY_TYPE.HUSONG then
		return YunbiaoWGData.Instance:GetHusongRemainTimes() > 0
	--需要开房间活动的类型
	elseif ActivityWGData.DoubleSideFBType[act_type] == 1 then
		--永夜战场按钮显示做特殊处理
		if act_type ~= ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG and act_type ~= ACTIVITY_TYPE.YEZHANWANGCHENG then
			return not ActivityWGData.Instance:GetActDoubleSideFBIsFinish(act_type)
		end
	end
	return true
end


--检查活动是否能弹出提示
function ActivityWGData:IsCanShowActNotice(act_type)
	if act_type == ACTIVITY_TYPE.GUILD_FB then
		return not GuildWGData.Instance:IsFinishGuildFb()
	elseif act_type == ACTIVITY_TYPE.GUILD_CHUAN_GONG then
		local guild_answer_act = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_ANSWER)
		if not IsEmptyTable(guild_answer_act) and (guild_answer_act.status == ACTIVITY_STATUS.STANDY or guild_answer_act.status == ACTIVITY_STATUS.OPEN) then
			return false
		else
			return not GuildAnswerWGData.Instance:HasGetPassExp()
		end
	elseif act_type == ACTIVITY_TYPE.HUSONG then
		return YunbiaoWGData.Instance:GetHuSongCanGoonNum() > 0
	--需要开房间活动的类型
	elseif ActivityWGData.DoubleSideFBType[act_type] == 1 then
		return not ActivityWGData.Instance:GetActDoubleSideFBIsFinish(act_type)
	elseif act_type == ACTIVITY_TYPE.XIANMENGZHAN then
		local is_show1 = GuildWGData.Instance:CanJoinGuildWar()
		return is_show1
	elseif act_type == ACTIVITY_TYPE.KF_XIANMENGZHAN then
		local is_guild_war = GuildWGData.Instance:GetMyCanJoinGuildWar()
		if not is_guild_war then
			return false
		end

		local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
		if my_guild_opponent_guild and my_guild_opponent_guild.guild_id <= 0 then
			return false
		end
		local is_over = GuildWGData.Instance:GetMyGuildWarIsOver()
		if is_over then
			return false
		end
		return true
	elseif act_type == ACTIVITY_TYPE.GUILD_ANSWER then
		local question_info = GuildAnswerWGData.Instance:GetQuestionInfo()
		local question_state = question_info.question_state or 0
		local question_end_timestamp = question_info.question_end_timestamp or 0
		if question_state == 1 and question_end_timestamp == 0 then
			return false
		end
	end
	
	return true
end

-- 检查活动开启等级
function ActivityWGData:CheckActOpenByRoleLevel(act_type)
	local cfg = self:GetActivityCfgByType(act_type)
	if cfg then
		local level = RoleWGData.Instance:GetRoleLevel()
		return level >= cfg.limit_level
	end

	return false
end

function ActivityWGData:InitActTipMap()
end

function ActivityWGData:GetActivityTipCig(act_type)
	return self.act_tip_cfg_map[act_type]
end

---[[ 运营活动相关
--设置动态运营活动活动状态
function ActivityWGData:SetOperationActivityActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}
	--收集已经开启的运营活动
	OperationActivityWGData.Instance:SetAleardayOpenActivity(activity_type, status)

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, end_time)
	end

	self:CreakMainViewActivityBtn()
end

--判断该活动是否属于运营活动
function ActivityWGData:GetCanLookActivityInfo(activity_type)
	return self.operation_activity_type_cfg[activity_type]
end

function ActivityWGData:CreakMainViewActivityBtn()
	local can_activity_open = OperationActivityWGData.Instance:GetOpenActivityList()
	local operation_state = ACTIVITY_STATUS.CLOSE
	local operation_state_two = ACTIVITY_STATUS.CLOSE

	for k,v in pairs(self.operation_activity_cfg) do
		if can_activity_open[v.activity_type] then
			local cfg = self:GetCanLookActivityInfo(v.activity_type)
			if cfg and cfg.btn_type == OPERATION_ACTIVITY_BTN_TYPE.ONE then
				operation_state = ACTIVITY_STATUS.OPEN
			elseif cfg and cfg.btn_type == OPERATION_ACTIVITY_BTN_TYPE.TWO then
				operation_state_two = ACTIVITY_STATUS.OPEN
			end
		end

		if operation_state == ACTIVITY_STATUS.OPEN and operation_state_two == ACTIVITY_STATUS.OPEN then
			break
		end
	end

	-- 20018 客户端自己的活动入口
	if self.activity_list[ACTIVITY_TYPE.OPERATIONACTIVITY] == nil then
		if operation_state == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if operation_state == ACTIVITY_STATUS.CLOSE then
			self:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.OPERATIONACTIVITY] = nil
		end
	end

	-- 20010 客户端自己的活动入口2
	if self.activity_list[ACTIVITY_TYPE.OPERATIONACTIVITY_TWO] == nil then
		if operation_state_two == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY_TWO, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if operation_state_two == ACTIVITY_STATUS.CLOSE then
			self:SetActivityStatus(ACTIVITY_TYPE.OPERATIONACTIVITY_TWO, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.OPERATIONACTIVITY_TWO] = nil
		end
	end
end
--]]
---[[
function ActivityWGData:IsDuoBeiTimes(task_type)
	local tianshen_count = TianshenRoadWGData.Instance:GetDuoBeiTimes(task_type)
  	local beizhan_count = QuanMinBeiZhanWGData.Instance:GetDuoBeiTimes(task_type)
  	local xianqi_count = ActXianQiJieFengWGData.Instance:GetDuoBeiTimes(task_type)
  	local operation_count = OperationActDuoBeiWGData.Instance:GetDuoBeiTimes(task_type)
    local merge_count = MergeActDuoBeiWGData.Instance:GetDuoBeiTimes(task_type)
    local festival_count = FestivalActDuoBeiWGData.Instance:GetDuoBeiTimes(task_type)

    local times = (tianshen_count > 0 and tianshen_count) or (beizhan_count > 0 and beizhan_count) or (xianqi_count > 0 and xianqi_count)
     				or (operation_count > 0 and operation_count) or (merge_count > 0 and merge_count) or (festival_count > 0 and festival_count)
    return tianshen_count > 0 or beizhan_count > 0 or xianqi_count > 0 or operation_count > 0 or merge_count > 0 or festival_count > 0, times
end

function ActivityWGData:GetHasDuoBeiInCopy()
	return TianshenRoadWGData.Instance:GetHasDuoBeiInCopy() or QuanMinBeiZhanWGData.Instance:GetHasDuoBeiInCopy()
		or OperationActDuoBeiWGData.Instance:GetHasDuoBeiInCopy() or MergeActDuoBeiWGData.Instance:GetHasDuoBeiInCopy()
		or ActXianQiJieFengWGData.Instance:GetHasDuoBeiInCopy()
end

function ActivityWGData:GetHasDuoBeiInBoss()
	return OperationActDuoBeiWGData.Instance:GetHasDuoBeiInBoss() or QuanMinBeiZhanWGData.Instance:GetHasDuoBeiInBoss()
		or TianshenRoadWGData.Instance:GetHasDuoBeiInBoss() or MergeActDuoBeiWGData.Instance:GetHasDuoBeiInBoss()
		or ActXianQiJieFengWGData.Instance:GetHasDuoBeiInBoss()
end

function ActivityWGData:GetHasDuoBeiInWorldBoss()
	return TianshenRoadWGData.Instance:GetHasDuoBeiInWorldBoss() or QuanMinBeiZhanWGData.Instance:GetHasDuoBeiInWorldBoss()
		or OperationActDuoBeiWGData.Instance:GetHasDuoBeiInWorldBoss() or MergeActDuoBeiWGData.Instance:GetHasDuoBeiInWorldBoss()
		or ActXianQiJieFengWGData.Instance:GetHasDuoBeiInWorldBoss()
end

function ActivityWGData:GetItemDuoBeiTimes(task_type , item_id)
	local tianshen_count = TianshenRoadWGData.Instance:GetItemDuoBeiTimes(task_type, item_id)
	local beizhan_count = QuanMinBeiZhanWGData.Instance:GetItemDuoBeiTimes(task_type, item_id)
	local xianqi_count = ActXianQiJieFengWGData.Instance:GetItemDuoBeiTimes(task_type, item_id)
	local operation_count = OperationActDuoBeiWGData.Instance:GetItemDuoBeiTimes(task_type, item_id)
    local merge_count = MergeActDuoBeiWGData.Instance:GetItemDuoBeiTimes(task_type, item_id)
    local festival_count = FestivalActDuoBeiWGData.Instance:GetItemDuoBeiTimes(task_type, item_id)
	local times = (tianshen_count > 0 and tianshen_count) or (beizhan_count > 0 and beizhan_count) or (xianqi_count > 0 and xianqi_count)
					 or (operation_count > 0 and operation_count) or (merge_count > 0 and merge_count) or (festival_count > 0 and festival_count)
	return tianshen_count > 0 or beizhan_count > 0 or xianqi_count > 0 or operation_count > 0 or merge_count > 0 or festival_count > 0, times
end

function ActivityWGData:GetDuoBeiTimes(task_type)
	local duobei_count = TianshenRoadWGData.Instance:GetDuoBeiTimes(task_type)
	if duobei_count == 0 then
		duobei_count = QuanMinBeiZhanWGData.Instance:GetDuoBeiTimes(task_type)
	end
	if duobei_count == 0 then
		duobei_count = ActXianQiJieFengWGData.Instance:GetDuoBeiTimes(task_type)
	end
	if duobei_count == 0 then
		duobei_count = OperationActDuoBeiWGData.Instance:GetDuoBeiTimes(task_type)
    end
    if duobei_count == 0 then
		duobei_count = MergeActDuoBeiWGData.Instance:GetDuoBeiTimes(task_type)
	end
    if duobei_count == 0 then
		duobei_count = FestivalActDuoBeiWGData.Instance:GetDuoBeiTimes(task_type)
	end
	return duobei_count
end
--]]

--合服活动
function ActivityWGData:SetMergeActivityActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}
	--收集已经开启的合服活动
	MergeActivityWGData.Instance:SetAleardayOpenActivity(activity_type, status)

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, end_time)
	end

	self:CreakMainViewMergeActivityBtn()
end

function ActivityWGData:CreakMainViewMergeActivityBtn()
	local can_activity_open = MergeActivityWGData.Instance:GetOpenActivityList()
	local activity_state = ACTIVITY_STATUS.CLOSE
    local activity_cfg = MergeActivityWGData.Instance:GetMergeActivityAllCfg()
	for k,v in pairs(activity_cfg) do
		if can_activity_open[v.activity_type] then
			activity_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	if self.activity_list[ACTIVITY_TYPE.HEFU_ACTIVITY] == nil then
        if activity_state == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.HEFU_ACTIVITY, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if activity_state == ACTIVITY_STATUS.CLOSE then
            self:SetActivityStatus(ACTIVITY_TYPE.HEFU_ACTIVITY, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.HEFU_ACTIVITY] = nil
		end
	end
end

--节日活动
function ActivityWGData:SetFestivalActivityActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}
	--收集已经开启的合服活动
	FestivalActivityWGData.Instance:SetAleardayOpenActivity(activity_type, status)

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, end_time)
	end

	self:CreakMainViewFestivalActivityBtn()
end

function ActivityWGData:CreakMainViewFestivalActivityBtn()
	local can_activity_open = FestivalActivityWGData.Instance:GetOpenActivityList()
	local activity_state = ACTIVITY_STATUS.CLOSE
    local activity_cfg = FestivalActivityWGData.Instance:GetFestivalActivityAllCfg()
	for k,v in pairs(activity_cfg) do
		if can_activity_open[v.activity_type] then
			activity_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	if self.activity_list[ACTIVITY_TYPE.FESTIVA_ACTIVITY] == nil then
        if activity_state == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.FESTIVA_ACTIVITY, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if activity_state == ACTIVITY_STATUS.CLOSE then
            self:SetActivityStatus(ACTIVITY_TYPE.FESTIVA_ACTIVITY, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.FESTIVA_ACTIVITY] = nil
		end
	end
end

--返利活动
function ActivityWGData:SetRebateActivityActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}
	--收集已经开启的返利活动
	RebateActivityWGData.Instance:SetAleardayOpenActivity(activity_type, status)

	for k, v in pairs(self.act_change_callback) do
		v(activity_type, status, end_time)
	end

	self:CreateMainViewRebateActivityBtn()
end

function ActivityWGData:CreateMainViewRebateActivityBtn()
	local can_activity_open = RebateActivityWGData.Instance:GetOpenActivityList()
	local activity_state = ACTIVITY_STATUS.CLOSE
    local activity_cfg = RebateActivityWGData.Instance:GetActivityAllCfg()
	for k,v in pairs(activity_cfg) do
		if can_activity_open[v.activity_type] then
			activity_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	if self.activity_list[ACTIVITY_TYPE.REBATE_ACTIVITY] == nil then
        if activity_state == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.REBATE_ACTIVITY, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if activity_state == ACTIVITY_STATUS.CLOSE then
            self:SetActivityStatus(ACTIVITY_TYPE.REBATE_ACTIVITY, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.REBATE_ACTIVITY] = nil
		end
	end
end

function ActivityWGData:SetNewFestivalActivityActState(activity_type, status, next_time, start_time, end_time, open_type)
	self.activity_list[activity_type] = {
		["type"] = activity_type,
		["status"] = status,
		["next_time"] = next_time,
		["start_time"] = start_time,
		["end_time"] = end_time,
		["open_type"] = open_type,
	}

	local act_state = ACTIVITY_STATUS.CLOSE
	local cfg = NewFestivalActivityWGData.Instance:GetAllFestivalActivityCfg()
	if IsEmptyTable(cfg) then
		return
	end

	for k, v in pairs(cfg) do
		if self.activity_list[v.activity_type] ~= nil and self.activity_list[v.activity_type].status == ACTIVITY_STATUS.OPEN then
			act_state = ACTIVITY_STATUS.OPEN
			break
		end
	end

	-- 20084 新节日活动入口
	if self.activity_list[ACTIVITY_TYPE.NEW_FESTIVA_ACTIVITY] == nil then
		if act_state == ACTIVITY_STATUS.OPEN then
			self:SetActivityStatus(ACTIVITY_TYPE.NEW_FESTIVA_ACTIVITY, ACTIVITY_STATUS.OPEN, 0, 0, 0, 1)
		end
	else
		if act_state == ACTIVITY_STATUS.CLOSE then
			self:SetActivityStatus(ACTIVITY_TYPE.NEW_FESTIVA_ACTIVITY, ACTIVITY_STATUS.CLOSE, 0, 0, 0, 1)
			self.activity_list[ACTIVITY_TYPE.NEW_FESTIVA_ACTIVITY] = nil
		end
	end
end
