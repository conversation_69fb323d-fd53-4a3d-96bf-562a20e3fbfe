LongYunZhanLingView = LongYunZhanLingView or BaseClass(SafeBaseView)

function LongYunZhanLingView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/longyun_zhanling_ui_prefab", "layout_longyun_zhanling_view")
end

function LongYunZhanLingView:LoadCallBack()
	LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_ORDER_INFO)

	XUI.AddClickEventListener(self.node_list.btn_buy_zhanling_level, BindTool.Bind(self.OnClickBugZhanLingLevelBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_active_high_zhanling,
		BindTool.Bind(self.OnClickActiveHighZhanLingBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_m_get_all_reward, BindTool.Bind(self.OnClickGetAllReward, self))

	self.scorll_index = -1
	local max_level_cfg = LongYunZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()
	self.max_show_level = max_level_cfg and max_level_cfg.seq or 0

	if not self.gd_show_reward_item then
		self.gd_show_reward_item = LYZhanLingRewardRender.New(self.node_list["gd_reward_cell"])
		self.gd_show_reward_item:SetIsGuDingReward(true)
	end

	if not self.left_show_reward_list then
		self.left_show_reward_list = AsyncListView.New(ItemCell, self.node_list.left_show_reward_list)
		self.left_show_reward_list:SetStartZeroIndex(true)
	end

	if not self.m_reward_list then
		self.m_reward_list = AsyncListView.New(LYZhanLingRewardRender, self.node_list.m_reward_list)
		self.m_reward_list:SetStartZeroIndex(true)
		self.node_list.m_reward_list.scroller.scrollerEndScrolled = BindTool.Bind(self.OnScrollValueChanged, self)
	end

	self:SetZhanLingTime()

	self.node_list.tips_label.text.text = Language.LongYunZhanLing.ZhanLingTipLable
end

function LongYunZhanLingView:ReleaseCallBack()
	if self.left_show_reward_list then
		self.left_show_reward_list:DeleteMe()
		self.left_show_reward_list = nil
	end

	if self.m_reward_list then
		self.m_reward_list:DeleteMe()
		self.m_reward_list = nil
	end

	if self.gd_show_reward_item then
		self.gd_show_reward_item:DeleteMe()
		self.gd_show_reward_item = nil
	end

	if CountDownManager.Instance:HasCountDown("LongYunZhanLingView") then
		CountDownManager.Instance:RemoveCountDown("LongYunZhanLingView")
	end

	self.max_show_level = nil
	self.old_gd_show_level = nil
	self.scorll_index = nil
end

function LongYunZhanLingView:SetJumpRewardflag(bool)
	self.jump_reward_flag = bool
end

function LongYunZhanLingView:SetZhanLingTime()
	local time = LongYunZhanLingWGData.Instance:GetZhanLingResetTime()
	local cycle_day = LongYunZhanLingWGData.Instance:GetZhanLingCycleTime()

	if cycle_day > time then
		if CountDownManager.Instance:HasCountDown("LongYunZhanLingView") then
			CountDownManager.Instance:RemoveCountDown("LongYunZhanLingView")
		end

		self.node_list.reset_time_str.text.text = string.format(
			Language.LongYunZhanLing.ZhanLingReSetTimeDesc, TimeUtil.FormatSecondDHM2(cycle_day - time))
		CountDownManager.Instance:AddCountDown("LongYunZhanLingView", function(elapse_time, total_time)
			local valid_time = total_time - elapse_time
			if valid_time > 0 then
				if self.node_list.reset_time_str then
					self.node_list.reset_time_str.text.text = string.format(
						Language.LongYunZhanLing.ZhanLingReSetTimeDesc, TimeUtil.FormatSecondDHM2(valid_time))
				end
			end
		end, function()
			if self.node_list.reset_time_str then
				self.node_list.reset_time_str.text.text = ""
			end
		end, cycle_day - time + TimeWGCtrl.Instance:GetServerTime(), nil, 1)
	else
		self.node_list.reset_time_str.text.text = ""
	end
end

function LongYunZhanLingView:OnFlush()
	local reward_cfg = LongYunZhanLingWGData.Instance:GetCurGroupCfg()
	if IsEmptyTable(reward_cfg) then
		return
	end

	self.left_show_reward_list:SetDataList(reward_cfg.order_preview)

	local zhanling_level = LongYunZhanLingWGData.Instance:GetZhanLingLevel()
	local cur_zhanling_cfg = LongYunZhanLingWGData.Instance:GetZhanLingCfg(zhanling_level)
	local next_zhanling_cfg = LongYunZhanLingWGData.Instance:GetZhanLingCfg(zhanling_level + 1)
	local devote = LongYunZhanLingWGData.Instance:GetLongYunZhanLingDevote()
	local is_max = IsEmptyTable(next_zhanling_cfg)
	local start_zhanling_cfg = LongYunZhanLingWGData.Instance:GetZhanLingCfg(0)

	local min_zhanling_level = devote < start_zhanling_cfg.need_devote

	if min_zhanling_level then
		self.node_list.m_level_num.text.text = 0
	else
		self.node_list.m_level_num.text.text = zhanling_level + 1
	end

	if is_max then
		self.node_list.m_pregress_label.text.text = devote
		self.node_list.m_pregress_slider.slider.value = 1
	else
		local cur_exp_value = min_zhanling_level and devote or (devote - cur_zhanling_cfg.need_devote)
		cur_exp_value = cur_exp_value > 0 and cur_exp_value or 0
		local need_act_value = min_zhanling_level and cur_zhanling_cfg.need_devote or
			(next_zhanling_cfg.need_devote - cur_zhanling_cfg.need_devote)
		self.node_list.m_pregress_label.text.text = cur_exp_value .. "/" .. need_act_value
		self.node_list.m_pregress_slider.slider.value = cur_exp_value / need_act_value
	end

	self.node_list.tips_text.text.text = is_max and Language.LongYunZhanLing.ZhanLingTipText2 or
		Language.LongYunZhanLing.ZhanLingTipText

	local is_open_higer_zhanling = LongYunZhanLingWGData.Instance:GetHigerOrderRewardFlag()
	self.node_list.m_high_lock:CustomSetActive(not is_open_higer_zhanling)
	self.node_list.btn_active_high_zhanling:SetActive(not is_open_higer_zhanling)

	local reward_list = LongYunZhanLingWGData.Instance:GetCurZhanLingRewardCfgList()

	if self.jump_reward_flag then
		self.m_reward_list:SetDataList(reward_list)
		self.reward_list_jump_index = self:GetRewardAutoJumpIndex(zhanling_level)
		self:JumpToRealIndex()
		self.jump_reward_flag = nil
	else
		self.m_reward_list:SetDataList(reward_list)
	end

	self:FlushGuDingShowReward(self.scorll_index)

	local remind_get = LongYunZhanLingWGData.Instance:GetZhanLingRemind() == 1
	self.node_list.get_all_red_point:SetActive(remind_get)

	local can_get_all_reward = LongYunZhanLingWGData.Instance:CanGetZhanLingAllReward()
	self.node_list.btn_m_get_all_reward:SetActive(can_get_all_reward)

	local is_max = LongYunZhanLingWGData.Instance:GetZhanLingIsMaxLevel()
	self.node_list.btn_buy_zhanling_level:SetActive(not is_max)
end

function LongYunZhanLingView:GetRewardAutoJumpIndex(zhanling_level)
	local reward_list = LongYunZhanLingWGData.Instance:GetCurZhanLingRewardCfgList()
	local def_level = 0
	local cur_level = zhanling_level
	local max_level_cfg = LongYunZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()

	if IsEmptyTable(reward_list) then
		return def_level
	end

	for k, v in ipairs(reward_list) do
		local nor_can_get, higer_can_get = LongYunZhanLingWGData.Instance:IsCanGetZhanLingRewardBySeq(v.seq)
		if nor_can_get or higer_can_get then
			return v.seq
		end
	end

	if cur_level >= max_level_cfg.seq then
		def_level = max_level_cfg.seq
	else
		def_level = cur_level
	end

	return def_level
end

function LongYunZhanLingView:JumpToRealIndex()
	if self.m_reward_list then
		local percent = 0
		if self.m_reward_list.list_view and self.m_reward_list.list_view.scroller
			and self.reward_list_jump_index > 4 then
			local cell_height = 64
			local cell_num = self.m_reward_list:GetListViewNumbers()
			local total_length = cell_num * cell_height
			local view_show_height = self.node_list["m_reward_list"].rect.sizeDelta.y
			local scroll_size = total_length - view_show_height
			local jump_position = (self.reward_list_jump_index - 1) * cell_height
			percent = jump_position / scroll_size
		end

		self.m_reward_list:JumptToPrecent(percent)
	end
end

function LongYunZhanLingView:FlushGuDingShowReward(level)
	if not level or level < 0 then
		return
	end

	local max_level_cfg = LongYunZhanLingWGData.Instance:GetZhanLingMaxLevelCfg()
	if IsEmptyTable(max_level_cfg) then
		return
	end

	local show_level = -1
	local reward_list = LongYunZhanLingWGData.Instance:GetCurZhanLingRewardCfgList()

	if not IsEmptyTable(reward_list) then
		for k, v in ipairs(reward_list) do
			if ((v.fixed_show > 0) and (v.seq > level) and (show_level < 0 or (show_level > 0 and show_level > v.seq))) then
				show_level = v.seq
			end
		end
	end

	if show_level < 0 then
		show_level = max_level_cfg.seq
	end

	local reward = LongYunZhanLingWGData.Instance:GetZhanLingCfg(show_level)
	if self.old_gd_show_level ~= show_level then
		self.gd_show_reward_item:SetData(reward)
		self.old_gd_show_level = show_level
	end
end

function LongYunZhanLingView:OnScrollValueChanged(scroll_obj, start_idx, end_idx)
	local now_index = end_idx
	if self.scorll_index == now_index then
		return
	end

	self.scorll_index = now_index
	if self.reward_list_jump_index then
		if now_index >= self.reward_list_jump_index then
			self.old_gd_show_level = now_index
			self:FlushGuDingShowReward(now_index)
			self.reward_list_jump_index = nil
		end
	else
		now_index = now_index > self.max_show_level and self.max_show_level or now_index
		self:FlushGuDingShowReward(now_index)
	end
end

function LongYunZhanLingView:OnClickBugZhanLingLevelBtn()
	LongYunZhanLingWGCtrl.Instance:OpenBugZhanLingLevelView()
end

function LongYunZhanLingView:OnClickActiveHighZhanLingBtn()
	LongYunZhanLingWGCtrl.Instance:OpenUnLockZhanLingView()
end

function LongYunZhanLingView:OnClickGetAllReward()
	if LongYunZhanLingWGData.Instance:GetZhanLingRemind() == 0 then
		TipsSystemManager.Instance:ShowSystemTips(Language.LongYunZhanLing.NoGetReward)
		return
	end

	LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_FETCH_ORDER_REWARD, -1,
		3)
end

------------------------------------------LYZhanLingRewardRender-------------------------------------------
LYZhanLingRewardRender = LYZhanLingRewardRender or BaseClass(BaseRender)
function LYZhanLingRewardRender:__init()
	self.view:SetActive(true)
end

function LYZhanLingRewardRender:LoadCallBack()
	if self.node_list["btn_nor_get"] then
		self.node_list["btn_nor_get"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self, 0))
	end

	if self.node_list["btn_high_get"] then
		self.node_list["btn_high_get"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self, 1))
	end

	self.normal_item = LYZhanLingRewardItem.New(self.node_list.normal_item)

	self.high_item_list = {}
	for i = 1, 2 do
		self.high_item_list[i] = LYZhanLingRewardItem.New(self.node_list["high_item_" .. i])
		self.high_item_list[i]:SetIsHighItem(true)
		self.high_item_list[i]:SetIndex(i)
	end
end

function LYZhanLingRewardRender:__delete()
	if self.normal_item then
		self.normal_item:DeleteMe()
		self.normal_item = nil
	end

	if self.high_item_list then
		for k, v in pairs(self.high_item_list) do
			v:DeleteMe()
		end
		self.high_item_list = nil
	end
end

function LYZhanLingRewardRender:SetIsGuDingReward(bool)
	self.is_guding_reward = bool
end

function LYZhanLingRewardRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local level = self.data.seq
	local nor_can_get, high_can_get = LongYunZhanLingWGData.Instance:IsCanGetZhanLingRewardBySeq(level)
	self.node_list.title.text.text = level + 1
	if self.node_list["btn_nor_get"] then
		self.node_list["btn_nor_get"]:SetActive(nor_can_get)
	end

	if self.node_list["btn_high_get"] then
		self.node_list["btn_high_get"]:SetActive(high_can_get)
	end

	local cur_zhanling_level = LongYunZhanLingWGData.Instance:GetZhanLingLevel()
	self.node_list["progress_up"]:SetActive(cur_zhanling_level >= level)
	self.node_list["progress_down"]:SetActive(cur_zhanling_level > level)

	if self.node_list.normal_item_red_point then
		self.node_list.normal_item_red_point:SetActive(nor_can_get)
	end

	if self.node_list.high_red_point1 then
		self.node_list.high_red_point1:SetActive(high_can_get)
	end

	if self.node_list.high_red_point2 then
		self.node_list.high_red_point2:SetActive(high_can_get)
	end

	local nor_is_get, higer_is_get = LongYunZhanLingWGData.Instance:IsGetZhanLingRewardBySeq(self.data.seq)
	self.normal_item:SetData({ reward_data = self.data.item[0], is_get = nor_is_get })
	for k, v in ipairs(self.high_item_list) do
		v:SetData({ reward_data = self.data.added_item[k - 1], is_get = higer_is_get })
	end

	-- if self.node_list.normal_lock then
	-- 	self.node_list.normal_lock:SetActive(not (nor_is_get or nor_can_get))
	-- end

	-- if self.node_list.high_lock then
	-- 	local is_open_higer_zhanling = LongYunZhanLingWGData.Instance:GetHigerOrderRewardFlag()
	-- 	self.node_list.high_lock:SetActive(not is_open_higer_zhanling or (not (higer_is_get or high_can_get)))
	-- end

	if self.node_list.bg then
		self.node_list.bg:SetActive(self.index % 2 == 0)
	end
end

function LYZhanLingRewardRender:OnClickGetReward(opera_type)
	if IsEmptyTable(self.data) then
		return
	end

	LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_FETCH_ORDER_REWARD,
		self.data.seq, opera_type)
end

---------------------------------LYZhanLingRewardItem--------------------------------------
LYZhanLingRewardItem = LYZhanLingRewardItem or BaseClass(BaseRender)

function LYZhanLingRewardItem:LoadCallBack()
	self.reward_item = ItemCell.New(self.node_list.item_node)
end

function LYZhanLingRewardItem:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end

	self.is_high_item = nil
end

function LYZhanLingRewardItem:SetIsHighItem(bool)
	self.is_high_item = bool
end

function LYZhanLingRewardItem:OnFlush()
	if IsEmptyTable(self.data.reward_data) then
		self.view:SetActive(false)
	else
		self.view:SetActive(true)

		local is_open_higer_zhanling = LongYunZhanLingWGData.Instance:GetHigerOrderRewardFlag()
		self.reward_item:SetData(self.data.reward_data)
		-- self.node_list.item_lock:SetActive(self.is_high_item and not is_open_higer_zhanling)
		self.node_list.item_isget:SetActive(self.data.is_get)
	end
end
