require("game/activity/daily/activity_daily_protocol")
require("game/activity/activity_wg_data")
require("game/activity/daily/activity_daily_wg_data")
require("game/crossserver/crossserver_common/crossserver_wg_data")
require("game/activity/daily/zhuxie_follow")
require("game/activity/daily/zhuxie_reward_view")
require("game/activity/daily/zhuxie_task_finish_view")
require("game/activity/daily/kf_zhuxie_follow_view")
require("game/activity/daily/zhuxie_hurt_rank_view")
require("game/activity/activity_notice")
require("game/activity/daily/zhuxie_rank_reaward_view")
require("game/activity/pre_show_rule_view")
require("game/activity/common_right_up_countdown")
require("game/activity/activity_count_down_view")
require("game/activity/activity_jiesuan_view")
require("game/activity/activity_tips_view")
require("game/activity/activity_second_confirmation_view")

BACK_FROM_CROSS_SERVER_FLAG_MAP = {}
-- 活动
ActivityWGCtrl = ActivityWGCtrl or BaseClass(BaseWGCtrl)

-- 活动准备状态弹出提示
ActivityStandShowNotice =
{
	[ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA] = true, 							-- 跨服星图秘境
	[ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE] = true,				-- 跨服终极战场
}

function ActivityWGCtrl:__init()
	if ActivityWGCtrl.Instance then
		ErrorLog("[ActivityWGCtrl] attempt to create singleton twice!")
		return
	end
	ActivityWGCtrl.Instance = self

	self.data = ActivityWGData.New()

	self.zhuxie_task_view = ZhuXieFollow.New()
	self.zhuxie_reward_view = ZhuXieRewardView.New()
	self.zhuxie_task_finish_view = ZhuXieTaskFinishView.New()

	self.kf_zhuxie_follow_view = KFZhuXieFollowView.New()
	self.zhuxie_hurt_rank_view = ZhuXieHurtRankView.New()
	--self.act_notice_view = ActivityForenoticeView.New()
	self.zhuxie_rank_reward = ZhuXieRankRewardView.New()
	self.pre_show_rule_view = PreShowRuleView.New()
	self.activity_tips_view = ActivityTipsView.New()

    -- self.right_count_down_view = RightUpCountDownView.New()
    self.activity_count_down_view = ActivityCountDownView.New()
    self.activity_jiesuan_view = ActivityJiesuanView.New()

    self.activity_second_confirmation_view = ActivitySecondConfirmationView.New()

	self:RegisterAllProtocols()
	self:RegisterDailyActProtocols()
	self.ui_icon_btn_effect = nil

	self.cross_server_flag_invalid_time = 0
	self.is_open_server_open = false

	self.delay_day_pass_list = {}
	self.temp_act_tip_list = {}
end

function ActivityWGCtrl:__delete()
	if self.zhuxie_task_view ~= nil then
		self.zhuxie_task_view:DeleteMe()
	end
	self.zhuxie_task_view = nil

	if self.ph_reward_view ~= nil then
		self.ph_reward_view:DeleteMe()
	end
	self.ph_reward_view = nil

	if self.view ~= nil then
		self.view:DeleteMe()
	end
	self.view = nil

	if self.data ~= nil then
		self.data:DeleteMe()
	end
	if self.zhuxie_reward_view ~= nil then
		self.zhuxie_reward_view:DeleteMe()
		self.zhuxie_reward_view = nil
	end
    if self.zhuxie_reward_view ~= nil then
		self.zhuxie_reward_view:DeleteMe()
		self.zhuxie_reward_view = nil
	end

	self.data = nil


	self.ui_icon_btn_effect = nil
	ActivityWGCtrl.Instance = nil

	if self.zhuxie_task_finish_view then
		self.zhuxie_task_finish_view:DeleteMe()
		self.zhuxie_task_finish_view = nil
	end

	if self.kf_zhuxie_follow_view then
		self.kf_zhuxie_follow_view:DeleteMe()
		self.kf_zhuxie_follow_view = nil
	end

	if self.right_count_down_view then
		self.right_count_down_view:DeleteMe()
		self.right_count_down_view = nil
	end

	-- if self.act_notice_view then
	-- 	self.act_notice_view:DeleteMe()
	-- 	self.act_notice_view = nil
	-- end

	if self.zhuxie_rank_reward then
		self.zhuxie_rank_reward:DeleteMe()
		self.zhuxie_rank_reward = nil
	end

	if self.activity_tips_view then
		self.activity_tips_view:DeleteMe()
		self.activity_tips_view = nil
	end

	if self.activity_second_confirmation_view then
		self.activity_second_confirmation_view:DeleteMe()
		self.activity_second_confirmation_view = nil
	end

	self.cross_server_flag_invalid_time = 0
	self.delay_day_pass_list = {}
	Runner.Instance:RemoveRunObj(self)
end

function ActivityWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCActivityStatus, "OnActivityStatus")
	self:RegisterProtocol(SCActivityRoomStatusAck, "OnActivityRoomStatusAck")
	self:RegisterProtocol(SCCrossRandActivityStatus, "OnCrossRandActivityStatus")
	-- 下一次怪物入侵刷新时间
	self:RegisterProtocol(SCMonsterInvadeTimeNotice, "OnMonsterInvadeTimeNotice")

	--判断活动是否已结束 是在有效的时间 活动提前结束的标记  开房间活动是否已参与结束
	self:RegisterProtocol(SCDoubleSideFBIsFinish, "OnSCDoubleSideFBIsFinish")
	self:RegisterProtocol(CSDoubleSideFBIsFinishReq)	--开房间活动是否已参与结束请求

	-- 跨服活动状态
	self:RegisterProtocol(SCCrossChannelActivityStatus, "OnSCCrossChannelActivityStatus")
	-- 跨服活动 统一拉去数据，领取奖励等操作
	self:RegisterProtocol(CSCrossChannelActivityRequest)


	self:RegisterProtocol(CSActivityRoomStatusReq)
	self:RegisterProtocol(CSActivityEnterReq)

	Runner.Instance:AddRunObj(self, 8)

	self:BindGlobalEvent(OtherEventType.PASS_DAY2, function()
		self:DoDayPassList()
		RechargeWGCtrl.Instance:CSPeriodOperaReq(PERIOD_OP_TYPE.PERIOD_OP_TYPE_ALL_INFO, 0)
		ZhanLingWGCtrl.Instance:SendZhanLingRequest(ZhanLingOperType.ExchangeInfo)
		XianLingGuZhenWGCtrl.Instance:CheckActGrade()
	end)

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.OnEnterGameServerSucc, self))
	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))
end

function ActivityWGCtrl:Update(now_time, elapse_time)
	if self.cross_server_flag_invalid_time > 0 and now_time >= self.cross_server_flag_invalid_time then
		BACK_FROM_CROSS_SERVER_FLAG_MAP[BACK_FROM_CROSS_SERVER_FLAG.ACTIVITY_HALL_MIN_PANEL_FORBID_OPEN] = 0
		self.cross_server_flag_invalid_time = 0
	end
end

function ActivityWGCtrl:AddDayPass(protocol)
	table.insert(self.delay_day_pass_list, __TableCopy(protocol))
end

function ActivityWGCtrl:DoDayPassList()
	local len = #self.delay_day_pass_list
	local times = math.ceil(len / 5)
	GlobalTimerQuest:AddTimesTimer(function()
		for i = 1, 5 do
			local msg = table.remove(self.delay_day_pass_list, 1)
			if msg then
				self:OnActivityStatus(msg)
			else
				break
			end
		end
	end,0,times)
end

--开房间活动是否已参与结束
function ActivityWGCtrl:OnSCDoubleSideFBIsFinish(protocol)
	self.data:SetSCDoubleSideFBIsFinish(protocol)
	local act_type = protocol.activity_type
	if self.send_double_side_callback and self.send_double_side_callback[act_type] then
		local callback = self.send_double_side_callback[act_type]
		callback(protocol)
		self.send_double_side_callback[act_type] = nil
	end
end

--开房间活动是否已参与结束请求
function ActivityWGCtrl:SendCSDoubleSideFBIsFinishReq(act_type, send_callback)
	if self.send_double_side_callback == nil then
		self.send_double_side_callback = {}
	end

	if self.send_double_side_callback[act_type] == nil then
		self.send_double_side_callback[act_type] = send_callback
	end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSDoubleSideFBIsFinishReq)
	send_protocol.activity_type = act_type
	send_protocol:EncodeAndSend()
end

-- 活动信息
function ActivityWGCtrl:OnActivityStatus(protocol)
	if IS_AUDIT_VERSION then
		return
	end

	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_openserver_day == -1 then
		self:AddDayPass(protocol)
		return
	end

	local activity_type = protocol.activity_type

	-- if activity_type == xxx then
	-- 	local state_str = protocol.status == ACTIVITY_STATUS.OPEN and "开启" or (protocol.status == ACTIVITY_STATUS.STANDY and "准备中" or "关闭")
	-- 	print_error(string.format("活动：%s   %s", activity_type, state_str))
	-- end

	---[[ 8-11天活动
	if activity_type == ACTIVITY_TYPE.GOD_DENGLU_YOULI or
		activity_type == ACTIVITY_TYPE.GOD_CHAOZHI_SHOUCHONG or
		activity_type == ACTIVITY_TYPE.GOD_LEIJI_HAOLI or
		activity_type == ACTIVITY_TYPE.GOD_WANT_SHENQI or
		activity_type == ACTIVITY_TYPE.GOD_CHONGBANG or
		activity_type == ACTIVITY_TYPE.GOD_JIANGLIN or
		activity_type == ACTIVITY_TYPE.GOD_MOWANG_YOULI or
		activity_type == ACTIVITY_TYPE.GOD_DOUBLE_JIANGLI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_TIANSHEN_DALIAN or
		activity_type == ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA then

		if activity_type == ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA then
			if protocol.status == ACTIVITY_STATUS.OPEN then
				--限时秒杀活动开启时需要自己去请求协议
				ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_XIANSHI_MIAOSHA, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_INFO)
			elseif protocol.status == ACTIVITY_STATUS.CLOSE then
				TianShenRoadMiaoshaWGData.Instance:InitBuyTimesList()
			end
		end
		
	 	self.data:SetTianShenRoadActivityState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
	 	return
	end
	--]]

	---[[ 12-14天活动
	if activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DENGLUYOULI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DUOBEILAIXI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_ZHANLIBIPIN or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_BEIZHANHAOLI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK or
		activity_type == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DALIAN then

		self.data:SetQuanMinBeiZhanActState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		return
	end
	--]]

	---[[ (仙器解封)
	if activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_MEIRISHOUCHONG or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LEICHONGHAOLI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_WOYAOLONGHUN or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_ZHANLIBIPIN or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_BEIZHANHAOLI or
		activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LONGHUNRANK then
			--print_error("FFFF===== 仙器解封 活动信息", activity_type, protocol.status == ACTIVITY_STATUS.OPEN)
		self.data:SetXianQiJieFengActState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		return
	end
	--]]

	---[[ 动态运营活动
	if nil ~= self.data:GetCanLookActivityInfo(activity_type) then
		local status = protocol.status
		if status ~= ACTIVITY_STATUS.OPEN then
			--撤回状态我们客户端当作关闭处理
			if status == ACTIVITY_STATUS.PAUSE then
				status = ACTIVITY_STATUS.CLOSE
			end
		end

		if activity_type == ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA then
			if status == ACTIVITY_STATUS.OPEN then
				--限时秒杀活动开启时需要自己去请求协议
				ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_INFO)
			elseif status == ACTIVITY_STATUS.CLOSE then
				XianshiMiaoshaWGData.Instance:InitBuyTimesList()
			end
		elseif activity_type == ACTIVITY_TYPE.OPERA_ACT_FENGZHENG and status == ACTIVITY_STATUS.OPEN then
			FZGetRewardWGCtrl.Instance:SendAllLayerRequire()
		elseif activity_type == ACTIVITY_TYPE.OPERA_ACT_QUANFU_JUANXIAN and status == ACTIVITY_STATUS.OPEN then
			OperationJuanXianWGCtrl.Instance:SendActivityRewardOp(OPERATION_QUANFU_JUANXIAN_OP_TYPE.TYPE_INFO)
		elseif activity_type == ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS and status == ACTIVITY_STATUS.OPEN then
			WateringFlowersWGCtrl.Instance:CSOAWaterFlowerInfo(OA_WATER_FLOWER_OPERA_TYPE.OA_WATER_FLOWER_OPERA_TYPE_INFO_REQ)
		elseif activity_type == ACTIVITY_TYPE.OPERA_ACT_CHUSHEN and status ~= ACTIVITY_STATUS.OPEN then
			ChuShenWGData.Instance:ResertAllActInfo()
		elseif activity_type == ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN then
			if status == ACTIVITY_STATUS.OPEN then
				OperationTaskChainWGCtrl.Instance:SendGetTaskChainInfo()
			elseif status == ACTIVITY_STATUS.CLOSE then
				OperationTaskChainWGCtrl.Instance:CloseAcceptNpc()
			end
		end

		self.data:SetOperationActivityActState(activity_type, status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		return
	end
    --]]

    --合服活动
    if nil ~= MergeActivityWGData.Instance:GetMergeActivityCfg(activity_type) then
		if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT and protocol.status == ACTIVITY_STATUS.OPEN then
			LoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.INFO)
		elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE then
			if protocol.status == ACTIVITY_STATUS.OPEN then
				--限时秒杀活动开启时需要自己去请求协议
				ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_INFO)
			elseif protocol.status == ACTIVITY_STATUS.CLOSE then
				HeFuMiaoshaWGData.Instance:InitBuyTimesList()
			end
		elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE and protocol.status == ACTIVITY_STATUS.OPEN then
			MergeFirstRechargeWGCtrl.Instance:SendDayShouChongReq(MERGE_ACTIVITY_SHOUCHONG_TYPE.TYPE_INFO)
		elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE and protocol.status == ACTIVITY_STATUS.OPEN then
			MergeLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(MERGE_XIANSHILEICHONG_TYPE.TYPE_INFO)
		elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DUOBEI and protocol.status == ACTIVITY_STATUS.OPEN then
			MergeDuoBeiWGCtrl.Instance:SendDayDuoBeiReq()
		elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN then
			if protocol.status == ACTIVITY_STATUS.OPEN then
				HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INFO_REQ)
			elseif protocol.status == ACTIVITY_STATUS.CLOSE then
				-- 活动关闭重置数据
				HeFuJuLingWGData.Instance:CreateJuLingInfoList()
			end
		end      
		self.data:SetMergeActivityActState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		return
	end


    --节日活动
    if nil ~= FestivalActivityWGData.Instance:GetFestivalActivityCfg(activity_type) then
		if activity_type == ACTIVITY_TYPE.FESTIVAL_ACT_OA_LOGINGIFT and protocol.status == ACTIVITY_STATUS.OPEN then
			FestivalLoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.INFO)
		elseif activity_type == ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2 then
			if protocol.status == ACTIVITY_STATUS.OPEN then
				--限时秒杀活动开启时需要自己去请求协议
				ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_INFO)
			elseif protocol.status == ACTIVITY_STATUS.CLOSE then
				FestivalMiaoshaWGData.Instance:InitBuyTimesList()
			end
		-- elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE and protocol.status == ACTIVITY_STATUS.OPEN then
		-- 	FestivalFirstRechargeWGCtrl.Instance:SendDayShouChongReq(MERGE_ACTIVITY_SHOUCHONG_TYPE.TYPE_INFO)
		elseif activity_type == ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE and protocol.status == ACTIVITY_STATUS.OPEN then
			FestivalLeiChongRechargeWGCtrl.Instance:SendLeiChongRechargeReq(MERGE_XIANSHILEICHONG_TYPE.TYPE_INFO)
		elseif activity_type == ACTIVITY_TYPE.FESTIVAL_ACT_OA_DUOBEI_2 and protocol.status == ACTIVITY_STATUS.OPEN then
			FestivalDuoBeiWGCtrl.Instance:SendDayDuoBeiReq()
		-- elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN then
		-- 	if protocol.status == ACTIVITY_STATUS.OPEN then
		-- 		FestivalJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INFO_REQ)
		-- 	elseif protocol.status == ACTIVITY_STATUS.CLOSE then
		-- 		-- 活动关闭重置数据
		-- 		FestivalJuLingData.Instance:CreateJuLingInfoList()
		-- 	end
		end      
		self.data:SetFestivalActivityActState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		return
	end

    --新节日活动
	if nil ~= NewFestivalActivityWGData.Instance:GetFestivalActivityCfg(activity_type) then
		self.data:SetNewFestivalActivityActState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		return
	end

    --返利活动
    if RebateActivityWGData.Instance:GetRebateActivityCfg(activity_type) then
        self.data:SetRebateActivityActState(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
		if activity_type == ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI and protocol.status == ACTIVITY_STATUS.OPEN then
	 		RebateActivityWGCtrl.Instance:SendActZhiGouOpera(ACT_ZHICHONG_OPER_TYPE.ACT_ZHICHONG_OPER_TYPE_SEND_INFO)
	 	end
		return
    end

    if activity_type == ACTIVITY_TYPE.LIMITEDTIMEOFFER then--特惠直购
        if protocol.status == ACTIVITY_STATUS.OPEN then --请求信息
            ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.LIMITEDTIMEOFFER, TIME_LIMITED_OFFER_OPERA_TYPE.TIME_LIMITED_OFFER_INFO, 0, 0, 0)
        end
        LimitedTimeOfferWGCtrl.Instance:SetRealAcivityStatus(protocol.status, protocol)
    end

	self.data:SetActivityStatus(activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)


	-- 有些活动提示窗口只需要在活动准备状态中弹出，所以加个 ActivityStandShowNotice 类型判断
	if protocol.status == ACTIVITY_STATUS.OPEN then 
		-- if not ActivityStandShowNotice[activity_type] then  
			self:OpenActivityNoticeView(activity_type)
		-- end
	elseif protocol.status == ACTIVITY_STATUS.STANDY then
		-- if ActivityStandShowNotice[activity_type] then
			--self:OpenActivityNoticeView(activity_type) 
		-- end
	elseif protocol.status == ACTIVITY_STATUS.CLOSE then
		self:RemoveActNotice(activity_type)
		--self:RemoveActivityTips(activity_type)
	end

	self:FlushNothingInfo(activity_type, protocol)
	self:FlushKfActInfo(activity_type, protocol)
	self:FlushBizuoInfo(activity_type, protocol)
	self:FlushOtherActInfo(activity_type, protocol)
end

function ActivityWGCtrl:FlushBizuoInfo(activity_type, protocol)
	BiZuoWGData.Instance:SetActivityHallCfg()

	local content = nil
	local activity_cfg = DailyWGData.Instance:GetActivityConfig(activity_type)
	if activity_cfg ~= nil and activity_type ~= ACTIVITY_TYPE.RAND_WEEKEND_BOSS then --
		local level_limit = activity_cfg.level
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		if main_role_vo.level < level_limit then
			return
		end

		local name = activity_cfg.name

		if activity_cfg.act_type == 1026 then
			name = Language.Common.CloseBeta
		elseif activity_cfg.act_type == ACTIVITY_TYPE.KF_HONORHALLS then
			if KuafuHonorhallWGData.Instance:IsKuaFu() then
				name = name .. Language.Honorhalls.KfStr
			end
		end

		if ACTIVITY_STATUS.CLOSE == protocol.status then
			content = "{showpos;2}" .. name..Language.Activity.HuoDongYiGuanBi
			if CrossServerWGData.LAST_CROSS_TYPE == activity_type and activity_type ~= ACTIVITY_TYPE.KF_GUILDBATTLE and activity_type ~= ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK then
				if Scene.Instance:GetSceneType() ~= SceneType.KF_DUCK_RACE then -- 小鸭疾走不要退出来
					CrossServerWGCtrl.ConnectBack()
				end
			end
		elseif ACTIVITY_STATUS.STANDY == protocol.status then
			if activity_type == ACTIVITY_TYPE.HUNYAN then
				content = "{showpos;2}" .. name..Language.Activity.ActivityPrepareTen
			else
				--content = "{showpos;2}" .. name..Language.Activity.ActivityPrepare
				if activity_cfg.ready_time_min ~= nil then
					content = string.format("{showpos;2}" .. name..Language.Activity.ActivityPrepareFormat, activity_cfg.ready_time_min)
				else
					content = "{showpos;2}" .. name..Language.Activity.ActivityPrepare
				end
			end

			if not BiZuoWGData.Instance:IsShieldByActType(activity_type) and not MainuiWGData.Instance:CheckMainUIActIsLimit(activity_cfg) 
				and not BiZuoWGData.Instance:IsShieldChuanWenByActType(activity_type) then -- 活动大厅周历里面有的才开
				TipWGCtrl.Instance:ShowSystemScroll(content)
			end
        elseif ACTIVITY_STATUS.OPEN == protocol.status then
            --策划让在开启时加跳转链接
            local link_str = string.format("{openLink;%d;%d}", CHAT_LINK_TYPE.FAKE_CLIENT_LINK_TYPE, activity_type)
			content = "{showpos;2}" .. name ..Language.Activity.ActivityStart .. link_str
			if not BiZuoWGData.Instance:IsShieldByActType(activity_type) and not MainuiWGData.Instance:CheckMainUIActIsLimit(activity_cfg)
				and not BiZuoWGData.Instance:IsShieldChuanWenByActType(activity_type) then -- 活动大厅周历里面有的才开
                TipWGCtrl.Instance:ShowSystemScroll(content)
			end

			if activity_type == ACTIVITY_TYPE.ACTIVITY_PAOKU then
				self:SendActivityRoomStatusReq(ACTIVITY_TYPE.ACTIVITY_PAOKU)
			end
		end
	end

	if nil ~= content and (activity_type < ACTIVITY_TYPE.OPEN_SERVER
	or activity_type >= ACTIVITY_TYPE.Act_Roller) and activity_type ~= ACTIVITY_TYPE.QINGTIE 
	and activity_type ~= ACTIVITY_TYPE.KF_HotSpring then -- 跨服温泉不显示传闻
		-- 活动大厅周历里面有的才开
		if not BiZuoWGData.Instance:IsShieldByActType(protocol.activity_type) and not MainuiWGData.Instance:CheckMainUIActIsLimit(activity_cfg) 
			and not BiZuoWGData.Instance:IsShieldChuanWenByActType(activity_type) then
			ChatWGCtrl.Instance:AddRumorMsg(content)
		end
	end

	if activity_type == ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE and ActivityWGData.Instance:GetActivityIsOpen(activity_type) then
		RemindManager.Instance:Fire(RemindName.EveryDayOneLove)
	end

	RemindManager.Instance:Fire(RemindName.Calendar)
end

function ActivityWGCtrl:FlushOtherActInfo(activity_type, protocol)
	-- 帮派试炼结束，不显示"试"
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_FB) == false then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MI_JING, 0)
	end

	if activity_type == ACTIVITY_TYPE.ZHUXIE then
		RankWGCtrl.Instance:SendRankReq(RankKind.Person, PersonRankType.Level)
		ActivityWGCtrl.Instance:SendActivityRoomStatusReq(ACTIVITY_TYPE.ZHUXIE)
	end

	if SIGNUPED_AUTO_JOIN_ACT_TYPE[activity_type] ~= nil then
		local act_cfg = self.data:GetActivityCfgByType(activity_type)
		local role_level = GameVoManager.Instance:GetMainRoleVo().level
		if role_level >= act_cfg.limit_level and role_level < act_cfg.level_max then
			if protocol.status == ACTIVITY_STATUS.OPEN and activity_type ~= ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE then
				self:AutoJoinActByType(activity_type)
			else
				if protocol.status == ACTIVITY_STATUS.STANDY and activity_type == ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE then
					self:AutoJoinActByType(activity_type)
				end
			end
		end
	end

	if protocol.status == ACTIVITY_STATUS.OPEN then
		-- 登录豪礼活动开启 请求数据
		OpenServerAssistWGCtrl.Instance:RequestActivityInfo(activity_type)
	end

	--婚宴巡游
    if activity_type == ACTIVITY_TYPE.HUNYAN then
        MarryWGCtrl.Instance:ActivityChangeCallback(activity_type, protocol.status, protocol.next_status_switch_time, protocol.open_type, protocol.param_1)
		MarryWGCtrl.Instance:OpenXunYouOpenHintView()
	end

	-- 红包雨
	if activity_type == ACTIVITY_TYPE.CROSS_RED_PACKET_RAIN then
		RedPacketRainWGCtrl.Instance:UpdateSceneShow()
	end
end

function ActivityWGCtrl:FlushKfActInfo(activity_type, protocol)
	if activity_type == ACTIVITY_TYPE.KF_LIEKUN then
		GuildWGData.Instance:SetLieKunRemind(protocol.status == ACTIVITY_STATUS.OPEN and 1 or 0)
    elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT then --招财猫
        FortuneCatWGCtrl.Instance:ActivityChangeCallBack(protocol.status)
    elseif activity_type == ACTIVITY_TYPE.KF_HONORHALLS then
        KuafuHonorhallWGCtrl.Instance:ActivityChangeCallback(activity_type, protocol.status, protocol.next_status_switch_time, protocol.open_type)
	elseif activity_type == ACTIVITY_TYPE.KF_XIANMENGZHAN then
		if protocol.status == ACTIVITY_STATUS.OPEN or protocol.status == ACTIVITY_STATUS.STANDY then
			GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE)
		end
	elseif activity_type == ACTIVITY_TYPE.GUILD_ANSWER then
		if protocol.status == ACTIVITY_STATUS.OPEN then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.CHAT_GUILD, 0, function ()
				--聊天仙盟
				ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[CHANNEL_TYPE.GUILD])
				MainuiWGCtrl.Instance:FlushGuildIcon()
			end)
            MainuiWGCtrl.Instance:FlushGuildIcon(true)
			if ChatWGCtrl.Instance:GetIsOpenGuild() then
				ChatWGCtrl.Instance:RefreshChannel(CHANNEL_TYPE.GUILD, true)
				GuildAnswerWGCtrl.Instance:OpenRankView()
			end
		elseif protocol.status == ACTIVITY_STATUS.CLOSE then
			GuildAnswerWGCtrl.Instance:ResetOpenChatFlag()
            MainuiWGCtrl.Instance:FlushGuildIcon(false)
			if ChatWGCtrl.Instance:GetIsOpenGuild() then
				ChatWGCtrl.Instance:RefreshChannel(CHANNEL_TYPE.GUILD, true)
				GuildAnswerWGCtrl.Instance:CloseRankView()
			end
		end
		GuildAnswerWGCtrl.Instance:FlushTimerView()
		ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_act, "select_index", {sel_index = 3})
	elseif activity_type == ACTIVITY_TYPE.GUILD_CHUAN_GONG then
		GuildAnswerWGCtrl.Instance:FlushPassBtnState()
	elseif activity_type == ACTIVITY_TYPE.GOD_XUNBAO and protocol.status == ACTIVITY_STATUS.OPEN then
		GodGetRewardWGCtrl.Instance:SendAllLayerRequire()

	elseif activity_type == ACTIVITY_TYPE.OPEN_SERVER and protocol.status == ACTIVITY_STATUS.CLOSE then
		if not TianShuWGData.Instance:IsGetAllReward() then
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.OPEN)
		end
	--需要开房间活动的类型
	elseif ActivityWGData.DoubleSideFBType[activity_type] == 1 then
		if protocol.status == ACTIVITY_STATUS.OPEN then
			ActivityWGCtrl.Instance:SendCSDoubleSideFBIsFinishReq(activity_type)
		end
	end
end

function ActivityWGCtrl:FlushNothingInfo(activity_type, protocol)
	-- 在跨服退出时会设置该标记，回到原服后因为该协议会收到很多次，
	-- 标记失效放在update里处理。从收到该协议后2秒该标记失效, 以不会影响后续的弹出
	-- if protocol.activity_type == 7 or protocol.activity_type == 2219 or protocol.activity_type == 2220 or protocol.activity_type == 1025 then
		-- print_error("ActivityWGCtrl:OnActivityStatus-->>",protocol.activity_type, ActivityWGData.Instance:GetActivityName(protocol.activity_type), ActivityWGData.Instance.GetActivityStatusName(protocol.status),protocol)
	-- end
	local forbid_open_panel = BACK_FROM_CROSS_SERVER_FLAG_MAP[BACK_FROM_CROSS_SERVER_FLAG.ACTIVITY_HALL_MIN_PANEL_FORBID_OPEN]
	if 1 == forbid_open_panel then
		self.cross_server_flag_invalid_time = Status.NowTime + 2
	end

	local kfpvp_open_panel = BACK_FROM_CROSS_SERVER_FLAG_MAP[BACK_FROM_CROSS_SERVER_FLAG.KFPVP_OPEN]
	if 1 == kfpvp_open_panel then
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_PVP) then
			KuafuPVPWGCtrl.Instance:Open()
		end
		BACK_FROM_CROSS_SERVER_FLAG_MAP[BACK_FROM_CROSS_SERVER_FLAG.KFPVP_OPEN] = 0
	end

	if activity_type == ACTIVITY_TYPE.OPEN_SERVER and protocol.status == ACTIVITY_STATUS.OPEN then
		self.is_open_server_open = true
	elseif activity_type == ACTIVITY_TYPE.OPEN_SERVER and protocol.status == ACTIVITY_STATUS.CLOSE then
		self.is_open_server_open = false
	end
end

--报名过的活动自动进入该活动的活动场景
function ActivityWGCtrl:AutoJoinActByType(act_type)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Common then
		-- local signup_cfg = BiZuoWGData.Instance:GetSignupConfigByActType(act_type)
		-- local activity_hall_signup_flag = BiZuoWGData.Instance.activity_hall_signup_flag
		-- if not IsEmptyTable(activity_hall_signup_flag) then
		-- 	if not signup_cfg then return end
		-- 	if BiZuoWGData.Instance:GetIsSignUpFlagBySeq(signup_cfg.seq) and SIGNUPED_AUTO_JOIN_ACT_TYPE[act_type] 
		-- 		and not BiZuoWGData.Instance:GetHasSignUpFlagByEnter(signup_cfg.seq) then
		-- 		SIGNUPED_AUTO_JOIN_ACT_TYPE[act_type](act_type,scene_type)
		-- 		BiZuoWGData.Instance:SetHasSignUpFlagByEnter(signup_cfg.seq)
		-- 	end
		-- end
		--策划需求：活动去掉报名和报名状态  到了活动时间直接出弹框是否进入
		if SIGNUPED_AUTO_JOIN_ACT_TYPE[act_type] then
			SIGNUPED_AUTO_JOIN_ACT_TYPE[act_type](act_type,scene_type)
		end
	end
end

-- 快接弹框方式进入
function ActivityWGCtrl:AutoQuickJoinActByType(act_type)
	if SIGNUPED_AUTO_JOIN_ACT_TYPE[act_type] then
		local scene_type = Scene.Instance:GetSceneType()
		SIGNUPED_AUTO_JOIN_ACT_TYPE[act_type](act_type, scene_type)
	else
		MainuiWGCtrl.Instance:ClickEnterActivity(act_type)
	end
end

-- 活动房间信息
function ActivityWGCtrl:OnActivityRoomStatusAck(protocol)
	-- print_error(protocol.activity_type, protocol.room_user_max, protocol.room_status_list)
	self.data:SetRoomStatusList(protocol.activity_type, protocol.room_user_max, protocol.room_status_list)
end

-- 下一次怪物入侵刷新时间
function ActivityWGCtrl:OnMonsterInvadeTimeNotice(protocol)
	self.data:SetNextMonsterInvadeTime(protocol.next_monster_invade_time)
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id and 103 == scene_id then
		self:UpdataMonsterInvadeCountDown()
	end
end

function ActivityWGCtrl:UpdataMonsterInvadeCountDown()
	local act_statu = self.data:GetActivityStatuByType(ACTIVITY_TYPE.MONSTER_INVADE)
	if nil == act_statu then
		return
	end
	if act_statu.status == ACTIVITY_STATUS.OPEN then
		if self.monster_invade_delay then
			GlobalTimerQuest:CancelQuest(self.monster_invade_delay)
			self.monster_invade_delay = nil
		end
		local next_time = self.data:GetNextMonsterInvadeTime() or 0
		local count_time = math.max(next_time - TimeWGCtrl.Instance:GetServerTime(), 0)
		if act_statu.next_time - next_time < 60 or 0 == count_time then -- 离结束不到1分钟或已到刷新时间
			-- UiInstanceMgr.Instance:CloseSceneNormalCountDown()
			return
		end
		if count_time <= 20 then
			-- UiInstanceMgr.Instance:ShowSceneNormalCountDown(count_time)
		else
			self.monster_invade_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.UpdataMonsterInvadeCountDown,self), 1)
		end
	end
end

function ActivityWGCtrl:CloseMonsterInvadeCountDown()
	-- UiInstanceMgr.Instance:CloseSceneNormalCountDown()
	if self.monster_invade_delay then
		GlobalTimerQuest:CancelQuest(self.monster_invade_delay)
		self.monster_invade_delay = nil
	end
end

-- 发送请求获取房间信息
function ActivityWGCtrl:SendActivityRoomStatusReq(activity_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSActivityRoomStatusReq)
	protocol.activity_type = activity_type
	protocol:EncodeAndSend()
end

function ActivityWGCtrl:SendActivityEnterReq(activity_type, room_index)
	if not FuBenWGCtrl.CanEnterFuben() then
		return
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSActivityEnterReq)
	protocol.activity_type = activity_type
	protocol.room_index = self.data:GetRoomIndex(activity_type)
	protocol:EncodeAndSend()
end

function ActivityWGCtrl:OpenPopView(activity_type, boo)
	local callback = function ()
		self:openPopView(activity_type,boo)
	end

	if activity_type == ACTIVITY_TYPE.GUILD_FB then
		if RoleWGData.Instance.role_vo.guild_id == 0 then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, TabIndex.guild_guildlist)
			local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("guild")
			if is_open then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
			end
		else
			GuildWGCtrl.Instance:SendGuildFbEnterReq()
		end
		return
	elseif activity_type == ACTIVITY_TYPE.XIANMENGZHAN then
		local cfgs = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
		if RoleWGData.Instance.role_vo.guild_id == 0 then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, TabIndex.guild_guildlist)
			local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("guild")
			if is_open then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
			end
			return
		end
	elseif activity_type == ACTIVITY_TYPE.TIANSHENJIANLIN then
		TianshenRoadWGCtrl.Instance:GotoShiLian()
		return
	elseif activity_type == ACTIVITY_TYPE.XINGTIANLAIXI then
		QuanMinBeiZhanWGCtrl.Instance:GotoShiLian()
		return
	elseif activity_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD then
		ActXianQiJieFengWGCtrl.Instance:GotoShiLian()
		return
	elseif activity_type == ACTIVITY_TYPE.CLIENT_MOWUJINGLIN then
		MoWuJiangLinWGCtrl.Instance:GotoShiLian()
		return
	elseif activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS then
		GuildBossWGCtrl.SendGuildBossEnterReq()
		return
	elseif activity_type == ACTIVITY_TYPE.SCERET_ACTIVITY_SHOW then
		SecretAreaWGCtrl.Instance:SendCorssSecretAreaOperate(SECRET_AREA_TYPE.XingTuMiJing_EnterScene)
		return
	end
	MainuiWGCtrl.Instance:AddInitCallBack(activity_type,callback)
end

function ActivityWGCtrl:openPopView( activity_type, boo )
	-- 活动大厅周历里面有的才开
	if BiZuoWGData.Instance:IsShieldByActType(activity_type) then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if activity_type == ACTIVITY_TYPE.ZHUXIE then
		-- ActIvityHallWGCtrl.Instance:OpenMinPanel(activity_type)
		ActivityWGData.Instance:OnEnterRoom(ACTIVITY_TYPE.ZHUXIE)
		return
	elseif activity_type == ACTIVITY_TYPE.KF_ZHUXIE then
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_ZHUXIE)
		return
	elseif activity_type == ACTIVITY_TYPE.SHOW_TASK_CHAIN then
		OperationTaskChainWGCtrl.Instance:OperaGo()
		return
	end

	if activity_type == ACTIVITY_TYPE.KF_PVP then
		KuafuPVPWGCtrl.Instance:Open()
		return
	elseif activity_type == ACTIVITY_TYPE.JIUSHEDAO then	 --帮派禁地特殊处理
		MountWGCtrl.Instance:AllDownMount()
		ActIvityHallWGCtrl.Instance:OpenActivity(activity_type)
	elseif ACTIVITY_TYPE.HOTSPRING == activity_type
			or ACTIVITY_TYPE.KF_HOTSPRING == activity_type
			or ACTIVITY_TYPE.YEZHANWANGCHENG == activity_type
			or ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT == activity_type
			or ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF == activity_type
		then
			if SceneType.HotSpring ~= scene_type and SceneType.KF_HotSpring ~= scene_type then
				MountWGCtrl.Instance:AllDownMount()
				ActIvityHallWGCtrl.Instance:OpenActivity(activity_type)
			end
	else
		local act_cfg = self.data:GetActivityCfgByType(activity_type)
		-- --暂时测试加
		-- if activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT then
		-- 	EternalNightWGCtrl.Instance:SendCSEternalNightEnter(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT)
		-- elseif activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF then
		-- 	EternalNightWGCtrl.Instance:SendCSEternalNightEnter(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
		-- end

		if act_cfg then
			if act_cfg.open_panel_name == GuideModuleName.ActIvityHall and boo == true then
				-- if scene_type == SceneType.Common and not Story.Instance:GetIsStoring() then --只有普通场景打开 现在没有这个需求了
				if scene_type == SceneType.Common then
					ActIvityHallWGCtrl.Instance:OpenMinPanel(activity_type)
				end
			elseif act_cfg.open_panel_name == GuideModuleName.ActIvityHall then
				ActIvityHallWGCtrl.Instance:OpenActivity(activity_type)
			else
				FunOpen.Instance:OpenViewNameByCfg(act_cfg.open_panel_name)
				-- 检测是否需要直购窗口
				LimitTimeGiftWGCtrl.Instance:CheckOpenViewPopupGift(activity_type)
				DiscountPurchaseWGCtrl.Instance:CheckOpenViewPopupGift(activity_type)
			end
		end
	end
end

function ActivityWGCtrl:Open(tab_index, param_t)
	-- 屏蔽以前活动面板(主要是避免策划入口没改好导致会打开老的活动面板)
	-- self.view:Open(tab_index)
end

function ActivityWGCtrl:Close()
	self.view:Close()
end
function ActivityWGCtrl:OpenActDescView(data)

end

--诛邪任务面板
function ActivityWGCtrl:OpenZhuXieFollow()
	self.zhuxie_task_view:Open()
	-- MainuiWGCtrl.Instance:SetTaskActive(false)	--隐藏任务栏
	self:UpdataZhuXieFollow()
end
function ActivityWGCtrl:OpenZhuXieReward()
	self.zhuxie_reward_view:Open()
end

--诛邪任务面板
function ActivityWGCtrl:UpdataZhuXieFollow()
	self.zhuxie_task_view:FlushZhuXieFollowView()
end

function ActivityWGCtrl:CloseZhuXieFollow()
	self.zhuxie_task_view:Close()
	-- MainuiWGCtrl.Instance:SetTaskActive(true)	--显示任务栏
end

function ActivityWGCtrl:OpenPhRewardView()
	-- self.ph_reward_view:Open()
end

--记录当前诛邪任务id
function ActivityWGCtrl:RemberCurZhuXieID(task_id)
	self.zhuxie_task_id = task_id
end

function ActivityWGCtrl:GetCurZhuXieID()
	return self.zhuxie_task_id
end
--自动点击诛邪未完成的任务
function ActivityWGCtrl:OnclcikOneTaskItem(task_id)
	self.zhuxie_task_view:OnclcikOneTaskItem(task_id)
end

function ActivityWGCtrl:ZhuXieFollowViewFlush(...)
	self.zhuxie_task_view:Flush(...)
end

--跨服诛邪任务面板

function ActivityWGCtrl:OpenKFZhuXieFollowView()
	self.kf_zhuxie_follow_view:Open()
	-- MainuiWGCtrl.Instance:SetTaskActive(false)	--隐藏任务栏
end

function ActivityWGCtrl:KFZhuXieFollowViewFlush(...)
	self.kf_zhuxie_follow_view:Flush(...)
end

function ActivityWGCtrl:FlushKFZhuXieFollowView( ... )
	self.kf_zhuxie_follow_view:FlushKFZhuXieFollowView()
end

function ActivityWGCtrl:CloseKFZhuXieFollowView()
	self.kf_zhuxie_follow_view:Close()
	-- MainuiWGCtrl.Instance:SetTaskActive(true)	--显示任务栏
end

--自动进行一个任务
function ActivityWGCtrl:AutoProceedATask(task_id)
	self.kf_zhuxie_follow_view:OnClickTaskCell(task_id)
end

-- 跨服充值排行榜活动协议接收
function ActivityWGCtrl:OnCrossRandActivityStatus(protocol)
	-- print("ActivityWGCtrl:OnCrossRandActivityStatus-->>", ActivityWGData.Instance:GetActivityName(protocol.activity_type), ActivityWGData.Instance.GetActivityStatusName(protocol.status))
	self.data:SetCrossRandActivityStatus(protocol.activity_type, protocol.status, protocol.begin_time, protocol.end_time)

	if protocol.activity_type == ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG then

		-- 根据ACTIVITY_TYPE来获取活动状态
		local act_info = self.data:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG) or {}
		-- 判断活动是否开启
		if act_info.status == ACTIVITY_STATUS.OPEN then
			self.data:SetActivityStatus(ACTIVITY_TYPE.HUANLEZADANRANK, ACTIVITY_STATUS.OPEN)
			HuanlezadanWGCtrl.Instance:SendReq(RA_CS_CROSS_RA_SMASHED_EGG_INFO.CS_CROSS_RA_SMASHED_EGG_INFO)
			RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG)
		else
			self.data:SetActivityStatus(ACTIVITY_TYPE.HUANLEZADANRANK, ACTIVITY_STATUS.CLOSE)
		end
	end
end

--跨服  统一跨服随机活动拉数据，领取奖励等操作
function ActivityWGCtrl:SendCSCrossChannelActivityRequest(param_t)
	-- Log("跨服随机活动请求---->>>>", param_t.activity_type, param_t.opera_type, param_t.param_1, param_t.param_2, param_t.param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossChannelActivityRequest)
	protocol.activity_type = param_t.activity_type
	protocol.opera_type = param_t.opera_type
	protocol.param_1 = param_t.param_1 or 0
	protocol.param_2 = param_t.param_2 or 0
	protocol.param_3 = param_t.param_3 or 0
	protocol:EncodeAndSend()
end

--新加 跨服活动状态
function ActivityWGCtrl:OnSCCrossChannelActivityStatus(protocol)
	if nil ~= NewFestivalActivityWGData.Instance:GetFestivalActivityCfg(protocol.activity_type) then
		self.data:SetNewFestivalActivityActState(protocol.activity_type, protocol.status, 0, protocol.start_time, protocol.end_time, 0)
		return
	end

	self.data:SetCrossChannelActivityStatus(protocol.activity_type, protocol.status, protocol.start_time, protocol.end_time)
end


function ActivityWGCtrl:IsOpenServerOpen()
	return self.is_open_server_open
end

--设置8-11天活动状态
function ActivityWGCtrl:SetElevenDayActivityState(protocol)
	-- body
end

function ActivityWGCtrl:OpenZhuXieTaskFinishView(data)
	self.zhuxie_task_finish_view:SetData(data)
	if self.zhuxie_task_finish_view:IsOpen() then
		self.zhuxie_task_finish_view:Flush()
	else
		self.zhuxie_task_finish_view:Open()
	end

end

function ActivityWGCtrl:OpenZhuXieHurtRankView()
	if not self.zhuxie_hurt_rank_view:IsOpen() then
		self.zhuxie_hurt_rank_view:Open()
	else
		self.zhuxie_hurt_rank_view:Flush()
	end
end

function ActivityWGCtrl:OpenActivityNoticeView(act_type)
	-- print_error("act_type:",act_type)
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
	if act_cfg and MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) then
		return
	end
	if not self.data:IsCanShowActNotice(act_type) then
		return
	end

	-- 普通场景才弹活动提示
	local scene_type = Scene.Instance:GetSceneType()

    if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS then
        if scene_type ~= SceneType.Common and
            scene_type ~= SceneType.KF_BOSS and
            scene_type ~= SceneType.WorldBoss and
            scene_type ~= SceneType.DABAO_BOSS and
            scene_type ~= SceneType.VIP_BOSS and
            scene_type ~= SceneType.PERSON_BOSS and
            scene_type ~= SceneType.Shenyuan_boss and
            scene_type ~= SceneType.GUILD_ANSWER_FB then --仙盟神兽在领地场景也要弹
            self.temp_act_tip_list[act_type] = true
            return
        end
	elseif act_type == ACTIVITY_TYPE.TREASURE_HUNT_BOSS_ACTIVITY then -- 策划要求寻宝boss任何场景都可以弹，不做限制

	elseif act_type == ACTIVITY_TYPE.HUSONG then  -- 护送双倍次数不足时不弹
		local times = YunbiaoWGData.Instance:GetHusongRemainTimes()
		if times <= 0 then
			return
		end
    else
        if scene_type ~= SceneType.Common and
            scene_type ~= SceneType.KF_BOSS and
            scene_type ~= SceneType.WorldBoss and
            scene_type ~= SceneType.DABAO_BOSS and
            scene_type ~= SceneType.VIP_BOSS and
            scene_type ~= SceneType.PERSON_BOSS and
            scene_type ~= SceneType.Shenyuan_boss then

            self.temp_act_tip_list[act_type] = true
            return
        end
    end

	local cfg = self.data:GetActivityTipCig(act_type)
	if cfg then
		-- self.act_notice_view:SetData(cfg)
		-- if not self.act_notice_view:IsLoaded() then
		-- 	self.act_notice_view:Open()
		-- end

		self.activity_tips_view:SetActivityData(cfg)
		if not self.activity_tips_view:IsLoaded() then
			self.activity_tips_view:Open()
		end
	end
end

-- 模拟配置直接弹
function ActivityWGCtrl:OpenActNotice(cfg_data)
	-- if cfg_data then
	-- 	self.act_notice_view:SetData(cfg_data)
	-- 	self.act_notice_view:Open()
	-- end
end

function ActivityWGCtrl:RemoveActNotice(act_type)
	-- local cfg = self.data:GetActivityTipCig(act_type)
	-- if cfg then
	-- 	self.act_notice_view:RemoveAct(act_type)
	-- end
end

function ActivityWGCtrl:OpenActivityTips(cfg_data)
	if cfg_data then
		self.activity_tips_view:SetActivityData(cfg_data)
		self.activity_tips_view:Open()
	end
end

function ActivityWGCtrl:OpenSecondConfirmationView(content_txt,ok_func,close_func,ok_txt,count_down_time)
	self.activity_second_confirmation_view:SetData(content_txt,ok_func,close_func,ok_txt,count_down_time)
end

function ActivityWGCtrl:RemoveActivityTips(act_type)
	local cfg = self.data:GetActivityTipCig(act_type)
	if cfg then
		self.activity_tips_view:RemoveActivity(act_type)
	end
end

function ActivityWGCtrl:KFZhuXieAutoFindTarget(data)
	if self.kf_zhuxie_follow_view:IsOpen() then
		self.kf_zhuxie_follow_view:AutoFindTarget(data)
	end
end

function ActivityWGCtrl:ZhuXieAutoFindTarget(data)
	if self.zhuxie_task_view:IsOpen() then
		self.zhuxie_task_view:AutoFindTarget(data)
	end
end

--s是否跨服参数
function ActivityWGCtrl:OpenZhuXieRankView(is_kf)
	local boss_id = nil
	if is_kf then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ZHUXIE)
		print_error(activity_info)
		-- if nil ~= activity_info then
		-- 	local flag = activity_info.status ~= ACTIVITY_STATUS.OPEN
		-- 	if flag then
		-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhuXie.ActivityNoOpen)
		-- 		return
		-- 	end
		-- end

		local boss_info = ActivityWGData.Instance:GetKFZhuXieTaskInfo() or {}
		if not IsEmptyTable(boss_info) then
			boss_id = boss_info.boss_id
		end
	else
		-- local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ZHUXIE)
		-- if nil ~= activity_info then
		-- 	local flag = activity_info.status ~= ACTIVITY_STATUS.OPEN
		-- 	if flag then
		-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhuXie.ActivityNoOpen)
		-- 		return
		-- 	end
		-- end

		local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
		if not IsEmptyTable(boss_info) then
			boss_id = boss_info.boss_id
		end
	end
	self.zhuxie_rank_reward:SetBossId(is_kf, boss_id)
end

function ActivityWGCtrl:OpenPreShowRuleView()
	self.pre_show_rule_view:Open()
end

--status 1,准备阶段，2 活动阶段
function ActivityWGCtrl:OpenRightCountDown(time, status)
    self.right_count_down_view:SetEndTime(time, status)
	self.right_count_down_view:Open()
end

function ActivityWGCtrl:CloseRightCountDown()
    if self.right_count_down_view:IsOpen() then
        self.right_count_down_view:Close()
    end
end

function ActivityWGCtrl:OnEnterGameServerSucc()
	PlayerPrefsUtil.SetInt("KFZhuXieZhanChang", 0)
	PlayerPrefsUtil.SetInt("HotSpring", 0)

end

function ActivityWGCtrl:OnSceneChangeComplete(old_scene_type, new_scene_type)
	local scene_map = {
		[SceneType.KFZhuXieZhanChang] = "KFZhuXieZhanChang",
		[SceneType.KF_HotSpring] = "HotSpring",
	}
	local scene_type = Scene.Instance:GetSceneType()
	if scene_map[scene_type] then
		if PlayerPrefsUtil.GetInt(scene_map[scene_type]) == 0 then
			if scene_type ~= SceneType.KF_HotSpring 
				or scene_type == SceneType.KF_HotSpring and HotSpringWGData.Instance:GetIsRoleLevelLimit()
			then
				--ActivityWGCtrl.Instance:OpenPreShowRuleView()
				PlayerPrefsUtil.SetInt(scene_map[scene_type], 1)
			end
		end
	end

	if self.temp_act_tip_list and scene_type == SceneType.Common then
		for act_type,state in pairs(self.temp_act_tip_list) do
			if state then
				local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
				if act_cfg and act_cfg.scene_type and old_scene_type ~= act_cfg.scene_type then
					local activity_data = self.data:GetActivityStatuByType(act_type)
					if activity_data and activity_data.status ~= ACTIVITY_STATUS.CLOSE then
						self:OpenActivityNoticeView(act_type)
					end
				end
				self.temp_act_tip_list[act_type] = false
			end
		end
	end
end

function ActivityWGCtrl:DoJoinActivity(activity_type)
    if activity_type == nil then
        print_error("activity_type == nil" )
        return
    end
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type ~= SceneType.GuDaoJiZhan_FB then
            GuDaoFuBenWGCtrl.Instance:SendEnterGuDaoFB()
            self:OpenPopView(activity_type)
        end
        return
    end
    local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(activity_type)
    if act_cfg ~= nil then
        if act_cfg.open_panel_name and act_cfg.open_panel_name ~= "" then
            FunOpen.Instance:OpenViewNameByCfg(act_cfg.open_panel_name)
			-- 检测是否需要直购窗口
			LimitTimeGiftWGCtrl.Instance:CheckOpenViewPopupGift(activity_type)
			DiscountPurchaseWGCtrl.Instance:CheckOpenViewPopupGift(activity_type)
        else
        	if activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT or activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF then
        		EternalNightWGCtrl.Instance:SendCSEternalNightEnter(activity_type)
        	elseif activity_type == ACTIVITY_TYPE.KF_DUCK_RACE then -- 小鸭疾走
        		DuckRaceWGCtrl.Instance:SendEnterScene()
        	else
	            print_error("获取不到日常活动里面的配置open_panel_name, activity_type =", activity_type, "活动名：", act_cfg.name)
        	end
        end
    else
        print_error("获取不到配置, activity_type =", activity_type)
    end
end

---[[ 天神降临 魔王来袭 10s倒计时
function ActivityWGCtrl:OpenActCountDown(info_list)
	self.activity_count_down_view:Flush(0, "count_down_info", info_list)
	self.activity_count_down_view:Open()
end

function ActivityWGCtrl:CloseActCountDown(act_type)
	if act_type == self.activity_count_down_view:GetCurActType() then
		self.activity_count_down_view:Close()
	end
end

function ActivityWGCtrl:CheckNowCountDownActType(act_type)
	if act_type == self.activity_count_down_view:GetCurActType() then
		return true
	end
end
--]]

-- 天神降临 魔王来袭 结算界面
function ActivityWGCtrl:OpenActJiseSuanView(act_type)
	self.activity_jiesuan_view:SetActType(act_type)
	RareItemDropWGCtrl.Instance:AddEndShowRareItemCallBack(function ()
		self.activity_jiesuan_view:Open()
	end)
end