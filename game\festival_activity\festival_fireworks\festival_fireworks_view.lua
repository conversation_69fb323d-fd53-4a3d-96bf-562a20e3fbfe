

-- local Ani_Name = {
--     [0] = "action_move",
--     [1] = "begin_purple",
--     [2] = "begin_orange",
--     [3] = "begin_pink",
--     [4] = "end_purple",
--     [5] = "end_orange",
--     [6] = "end_pink",
-- }


function FestivalActivityView:DelFireworksView()
    if self.fw_big_cell_list then
        self.fw_big_cell_list:DeleteMe()
        self.fw_big_cell_list = nil
    end

    if self.fw_zhenxi_cell_list then
        self.fw_zhenxi_cell_list:DeleteMe()
        self.fw_zhenxi_cell_list = nil
    end

    if self.fw_fanli_list then
        self.fw_fanli_list:DeleteMe()
        self.fw_fanli_list = nil
    end

    -- if self.niudan_item_data_change then
    --     ItemWGData.Instance:UnNotifyDataChangeCallBack(self.niudan_item_data_change)
    --     self.niudan_item_data_change = nil
    -- end

    self.is_play_niudan_ani = false
end

-- function FestivalActivityView:InitFireworksView()
-- 	self.node_list["btn_fw_record"].button:AddClickListener(BindTool.Bind1(self.OnClickFwsRecord, self))
-- 	for i = 1, 3 do
-- 		self.node_list["btn_fw_buy_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickFwsDraw, self, i))
-- 	end
--     self:CreateFwsShowCell()
--     self.node_list["niudan_btn_ignore_ani"].button:AddClickListener(BindTool.Bind(self.OnClickNiuDanIgnore, self))
    
--     self.niudan_item_data_change = BindTool.Bind(self.OnFwsItemChange, self)
--     ItemWGData.Instance:NotifyDataChangeCallBack(self.niudan_item_data_change)
--     self:LoadFireworksImages()
-- end

function FestivalActivityView:LoadFireworksImages()
    -- local leichong_rawimage_bundle, leichong_rawimage_asset = ResPath.GetFestivalRawImages("xyndgg")
    -- self.node_list["title_img"].raw_image:LoadSprite(leichong_rawimage_bundle, leichong_rawimage_asset, function ()
    --     self.node_list["title_img"].raw_image:SetNativeSize()
    -- end)

    local cfg = FestivalFireworksWGData.Instance:GetGradeCfg()
    --self.node_list.txt_fw_times.text.color = Str2C3b(cfg.left_text_color)
    --self.node_list.niudan_top_txt.text.color = Str2C3b(cfg.left_text_color)
    -- for i = 1, 3 do
    --     self.node_list["fw_red_num_"..i].text.color = Str2C3b(cfg.down_text_color)
    -- end
    self.down_color = cfg.down_text_color
end

--跳过
function FestivalActivityView:OnClickNiuDanIgnore()
    local is_ignore = FestivalFireworksWGData.Instance:GetSkipAnim() == 1
    FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.ANIM_FLAG, is_ignore and 0 or 1)
end

--展示的格子
function FestivalActivityView:CreateFwsShowCell()
    self.fw_big_cell_list = AsyncListView.New(MergeFireworksShowRender,self.node_list["fw_big_cell_list"] )
    self.fw_zhenxi_cell_list = AsyncListView.New(MergeFireworksShowRender,self.node_list["fw_zhenxi_cell_list"])
    self.fw_fanli_list = AsyncListView.New(FestivalFireworksFanliRender, self.node_list["fw_leiji_list"])

    self.fw_egg_list = {}
    for i = 1, 8 do
        self.fw_egg_list[i] = FestivalFireworksEggRender.New(self.node_list["egg_" .. i])
    end
end


--更新日志和协议信息
function FestivalActivityView:ShowIndexFireworks()
    self:SetOutsideRuleTips(Language.MergeFireworks.OutSideTip)
    self:SetFireworksRuleTip()
    FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
    FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
    if not self.is_play_niudan_ani then
        --self:SetNiuDanjiSpine("idle")
    end
end

--参数刷新
function FestivalActivityView:OnFlushFireworks(param)
    for i, v in pairs(param) do
        if v == "all" then
            self:FlushFwsViewShow()
        elseif v == "record" then
            self:FlushFwsRecordRed()
        elseif v == "play_ani" then
            self:PlayNiuDanjiAni()
        elseif v == "reset_ani" then
            self.is_play_niudan_ani = false
            self:SetNiuDanjiEnd()
        end
    end
end

function FestivalActivityView:PlayNiuDanjiAni()
    local ignore_anim = FestivalFireworksWGData.Instance:GetSkipAnim() == 1
    local btn_index = FestivalFireworksWGData.Instance:CacheOrGetDrawIndex()
    if ignore_anim then
        return
    end

    self.is_play_niudan_ani = true
    self:SetNiuDanjiSpine(btn_index)
    self.play_niudan_ani = GlobalTimerQuest:AddDelayTimer(function()
        self.play_niudan_ani = nil
        self.is_play_niudan_ani = false
    end, 3) --滚动
end


-- 设置动画
function FestivalActivityView:SetNiuDanjiSpine(btn_index)
    if self.fw_egg_list == nil then
        return
    end

    if btn_index == 1 then
        local range_index = math.random(1, #self.fw_egg_list)
        self.fw_egg_list[range_index]:EggStartAni()
    elseif btn_index == 2 or btn_index == 3 then
        for i = 1, 8 do
            self.fw_egg_list[i]:EggStartAni()
        end
    end
end
-- 结束动画
function FestivalActivityView:SetNiuDanjiEnd()
    if self.fw_egg_list then
        for i = 1, 8 do
            self.fw_egg_list[i]:EggEndAni()
        end
    end
end

--刷新界面
function FestivalActivityView:FlushFwsViewShow()
    self:FlushIgnoreState()
    self:FlushFwsBtnShow()
    self:FlushFwsShowCell()
    --self:FlushFwsGuarantee()
    --扭蛋次数
    --local draw_times = FestivalFireworksWGData.Instance:GetCurDrawTimes()
    --self.node_list["txt_fw_times"].text.text = string.format(Language.MergeFireworks.TxtTimes, draw_times)
end

function FestivalActivityView:FlushIgnoreState()
    local is_ignore = FestivalFireworksWGData.Instance:GetSkipAnim() == 1
    self.node_list["niudan_ignore_img"]:SetActive(is_ignore)
end

--3个按钮刷新，传参数只刷新数量
function FestivalActivityView:FlushFwsBtnShow(is_flush_num)
    local cfg = FestivalFireworksWGData.Instance:GetConsumeCfg()
    local item_cfg
    local count, asset
    for i = 1, 3 do
        if cfg[i] then
            local item_id = cfg[i].yanhua_item.item_id
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then               
                    --道具图标
                    self.node_list["fw_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                    self.node_list["fw_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowNiuDanItemTips, self, item_id))
                     --特效
                    --asset = BaseCell_Ui_Circle_Effect[item_cfg.zhengui_effect]           
                end
                --按钮价格
                self.node_list["txt_fw_buy_" .. i].text.text = string.format(Language.MergeFireworks.TxtBuy, cfg[i].onekey_lotto_num)
                -- --折扣
                self.node_list["fw_discount_" .. i]:SetActive(cfg[i].discount_text ~= "")
                if cfg[i].discount_text ~= "" then
                    self.node_list["txt_fw_discount_" .. i].text.text = cfg[i].discount_text .. "折"
                end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["fw_red_" .. i]:SetActive(true)
            self.node_list["niudan_red_" .. i]:SetActive(count >= cfg[i].yanhua_item.num)
            --self.down_color = self.down_color or COLOR3B.DEFAULT
            local color = count >= cfg[i].yanhua_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
            local left_str = ToColorStr(count, color) 
            self.node_list["fw_red_num_" .. i].text.text = left_str .."/".. cfg[i].yanhua_item.num
            self.node_list["fw_red_num_"..i].text.color = Str2C3b(color)
        end
    end
end

function FestivalActivityView:ShowNiuDanItemTips(item_id)
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end

function FestivalActivityView:FlushFwsRecordRed()
	--大奖记录红点
    local read_time = RoleWGData.GetRolePlayerPrefsInt("Festival_Fireworks_record")
    local new_record_time = FestivalFireworksWGData.Instance:GetRecordTime()
    self.node_list["fw_record_red"]:SetActive(new_record_time > read_time)
end

--展示格子刷新
function FestivalActivityView:FlushFwsShowCell()
    local show_cell_list1, show_cell_list2, baodi_list = FestivalFireworksWGData.Instance:GetShowCellList()
    local zhenxi_list = FestivalFireworksWGData.Instance:GetZhenXiRewardList()
    local fanli_list = FestivalFireworksWGData.Instance:GetFanliList()
    self.fw_big_cell_list:SetDataList(show_cell_list1)
    self.fw_zhenxi_cell_list:SetDataList(zhenxi_list)
    self.fw_fanli_list:SetDataList(fanli_list)
    -- self.fw_mid_cell_list:SetDataList(show_cell_list2)
    -- self.fw_small_cell_list:SetDataList(baodi_list)
end

--保底次数显示
function FestivalActivityView:FlushFwsGuarantee()
    --local next_times = FestivalFireworksWGData.Instance:GetNextBaodiTimes()
    --self.node_list["fw_guarantee"]:SetActive(true)
    --self.node_list["txt_fw_last_times"].text.text = next_times
end

--ruletip
function FestivalActivityView:SetFireworksRuleTip()
    local grade_cfg = FestivalFireworksWGData.Instance:GetGradeCfg()
    if not IsEmptyTable(grade_cfg) then
        local desc = grade_cfg.tip_desc
        local title = grade_cfg.tip_title or Language.MergeFireworks.TipTitle
        local probility_str = FestivalFireworksWGData.Instance:GetItemsProbility()
        desc = desc .. probility_str
        self:SetRuleInfo(desc,title)
    end
end

--打开记录
function FestivalActivityView:OnClickFwsRecord()
	FestivalFireworksWGCtrl.Instance:OpenRecord()
end

--点击抽奖
function FestivalActivityView:OnClickFwsDraw(draw_type)
    if self.is_play_niudan_ani then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeFireworks.PlayAni)
        return
    end
	--check enough
	local cfg = FestivalFireworksWGData.Instance:GetConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.yanhua_item.item_id)
	if num >= cfg.yanhua_item.num then
		--足够就关闭界面放特效
			--是否跳过动画
        FestivalFireworksWGCtrl.Instance:GotoFire(function()
            FestivalFireworksWGData.Instance:CacheOrGetDrawIndex(draw_type)
            FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, cfg.onekey_lotto_num, 1)
        end)
	else
		--不足够买
        FestivalFireworksWGCtrl.Instance:ClickUse(draw_type, function()
            self:OnClickFwsBuy(draw_type)
        end)
	end
end

--点击购买
function FestivalActivityView:OnClickFwsBuy(draw_type)
    local cfg = FestivalFireworksWGData.Instance:GetConsumeCfg()
    local cur_cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.yanhua_item.item_id)
    local consume = cfg[1].consume_count * (cur_cfg.yanhua_item.num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        FestivalFireworksWGData.Instance:CacheOrGetDrawIndex(draw_type)
		FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, cfg[draw_type].onekey_lotto_num)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--物品监听刷新按钮显示
function FestivalActivityView:OnFwsItemChange(item_id)
    local check_list = FestivalFireworksWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == item_id then
            self:FlushFwsBtnShow(true)
            return
        end
    end
end

--------------------------------------------------------------------------------------------
FestivalFireworksFanliRender = FestivalFireworksFanliRender or BaseClass(BaseRender)
function FestivalFireworksFanliRender:__delete()
    
end

function FestivalFireworksFanliRender:LoadCallBack()
    self.node_list["btn_fanli"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))

    self.item_list_view = AsyncListView.New(ItemCell,self.node_list["fw_item_list"])
end

function FestivalFireworksFanliRender:ReleaseCallBack()
    if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
    end
end

function FestivalFireworksFanliRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data.data
    local draw_times = FestivalFireworksWGData.Instance:GetCurDrawTimes()
    self.node_list["txt_get"]:SetActive(self.data.has_get == 1)
    local color = draw_times >= data.lotto_num and COLOR3B.GREEN or COLOR3B.D_RED
    local str = draw_times .. "/" .. data.lotto_num
    local desc = ToColorStr(str, color)
    self.node_list["txt_times"].text.text = string.format(Language.MergeFireworks.TxtTimes, desc, data.lotto_num)

    local is_show = data.lotto_num <= draw_times and self.data.has_get ~= 1
    self.node_list["red"]:SetActive(is_show)
    XUI.SetButtonEnabled(self.node_list["btn_fanli"], is_show)

    local reward_data = {}
	if data.reward_item then
		for i=0,#data.reward_item do
			table.insert(reward_data,data.reward_item[i])
		end
	end
	reward_data = SortDataByItemColor(reward_data)
	self.item_list_view:SetDataList(reward_data)
end

function FestivalFireworksFanliRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = FestivalFireworksWGData.Instance:GetCurDrawTimes()
    local data = self.data.data
    if draw_times >= data.lotto_num and self.data.has_get == 0 then
        FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.FETCH, data.index)
    end
end

MergeFireworksShowRender = MergeFireworksShowRender or BaseClass(BaseRender) 

function MergeFireworksShowRender:LoadCallBack()
    self.show_item = ItemCell.New(self.node_list.pos)
end

function MergeFireworksShowRender:__delete()
    if self.show_item then
        self.show_item:DeleteMe()
    end
    self.show_item = nil
    self.item_id = 0
end

function MergeFireworksShowRender:SetData(data)
    self.data = data

    if not IsEmptyTable(self.data) and not IsEmptyTable(self.data.reward_item) then
        if self.item_id ~= self.data.reward_item.item_id then
            self.item_id = self.data.reward_item.item_id
            self.show_item:SetData(self.data.reward_item)
        end
    end
end

FestivalFireworksEggRender = FestivalFireworksEggRender or BaseClass(BaseRender)


function FestivalFireworksEggRender:EggStartAni()
    local hammer_tween1 = self.node_list["hammer"].canvas_group:DoAlpha(0, 1, 0.5)
    hammer_tween1:OnComplete(function ()
        local hammer_tween2 = self.node_list["hammer"].transform:DOLocalRotate(u3dpool.vec3(0, 0, 30), 0.5, DG.Tweening.RotateMode.Fast)
        hammer_tween2:SetEase(DG.Tweening.Ease.InOutBack)
        hammer_tween2:OnComplete(function ()
            -- self.node_list["egg_img_1"].canvas_group:DoAlpha(1, 0, 0.2)
            -- self.node_list["egg_img_2"].canvas_group:DoAlpha(0, 1, 0.2)
            self.node_list["egg_img_1"]:SetActive(false)
            self.node_list["egg_img_2"]:SetActive(true)
            self.node_list["hammer"].transform:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.5, DG.Tweening.RotateMode.Fast)
            self.node_list["hammer"].canvas_group:DoAlpha(1, 0, 0.5)
        end)
    end)
end

function FestivalFireworksEggRender:EggEndAni()
    self.node_list["hammer"].canvas_group.alpha = 0
    -- self.node_list["egg_img_1"].canvas_group.alpha = 1
    -- self.node_list["egg_img_2"].canvas_group.alpha = 0
    self.node_list["egg_img_1"]:SetActive(true)
    self.node_list["egg_img_2"]:SetActive(false)
end