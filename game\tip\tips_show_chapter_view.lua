TipsShowChapterView = TipsShowChapterView or BaseClass(SafeBaseView)

function TipsShowChapterView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Guide
	self:AddViewResource(0, "uis/view/chaptertip_prefab", "chapter_tip")
end


function TipsShowChapterView:__delete()

end

function TipsShowChapterView:ReleaseCallBack()
	if self.show_delay_timer then
		GlobalTimerQuest:CancelQuest(self.show_delay_timer)
		self.show_delay_timer = nil
	end	
	if self.show_chaper_title_timer then
		GlobalTimerQuest:CancelQuest(self.show_chaper_title_timer)
		self.show_chaper_title_timer = nil
	end
	if self.close_delay_timer then
		GlobalTimerQuest:CancelQuest(self.close_delay_timer)
		self.close_delay_timer = nil
	end
	self.task_info = nil 

	self:CancelTween()
end

function TipsShowChapterView:LoadCallBack()
end

function TipsShowChapterView:ShowIndexCallBack()
	self:Flush()
end

function TipsShowChapterView:SetTaskInfo(task_info)
	self.task_info = task_info 
	self:Open()
end

function TipsShowChapterView:OnFlush()
	if not self.task_info then return end
	self.node_list.chapter_num.tmp.text = string.format(Language.Dungeon.TheChapter,Language.Common.UpNum[self.task_info.show_chaper_id])
	-- self.node_list.chapter_num.image:LoadSprite('uis/uires/res/x1ui/chapter_tip_atlas',"num_" .. self.task_info.show_chaper_id) --数字多少章节
	-- self.node_list.chapter_name.image:LoadSprite('uis/uires/res/x1ui/chapter_tip_atlas',"name_" .. self.task_info.show_chaper_id) --章节名字
	if self.show_delay_timer then
		GlobalTimerQuest:CancelQuest(self.show_delay_timer)
		self.show_delay_timer = nil
	end	
	if self.show_chaper_title_timer then
		GlobalTimerQuest:CancelQuest(self.show_chaper_title_timer)
		self.show_chaper_title_timer = nil
	end
	if self.close_delay_timer then
		GlobalTimerQuest:CancelQuest(self.close_delay_timer)
		self.close_delay_timer = nil
	end	
	self.timer_index = 1
	self.timer_title_index = 1
	local emoji_text = self.node_list.chapter_desc.emoji_text
	local color = "#C2B38DFF"
	local chapter_dec
	local other_str_index = 0
 	local talk_len, list = StringLen(self.task_info.show_chaper_info)
	self.show_delay_timer = GlobalTimerQuest:AddTimesTimer(function ()
		if not list[self.timer_index] then
			return
		end
 		chapter_dec = table.concat(list, "", 1, self.timer_index)
 		chapter_dec = string.gsub(chapter_dec, "\n", "</line>\n<line>")
 		chapter_dec = string.format(Language.TipSChapTer.Line, chapter_dec)
		emoji_text.text = chapter_dec
		self.timer_index = self.timer_index + 1
	end,0.05,talk_len + 1) --0.05每次延时时间,  #self.task_info.show_chaper_info/3总延时时间 = 字长/3

	local chaper_title = Language.TipSChapTer.ChapTerName[self.task_info.show_chaper_id]
	self.node_list.chapter_name.tmp.text = chaper_title

	self:CancelTween()
	local sequence = DG.Tweening.DOTween.Sequence()
	self.node_list["chapter_num"].canvas_group.alpha = 0
	local tween_1 = self.node_list["chapter_num"].canvas_group:DoAlpha(0, 1, 1)
	sequence:Append(tween_1)

	self.node_list["chapter_name"].canvas_group.alpha = 0
	local tween_2 = self.node_list["chapter_name"].canvas_group:DoAlpha(0, 1, 1)
	sequence:Append(tween_2)

	self.close_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 2.5)

	self.sequence_tween = sequence
end

function TipsShowChapterView:CancelTween()
    if self.sequence_tween then
        self.sequence_tween:Kill()
        self.sequence_tween = nil
    end
end

