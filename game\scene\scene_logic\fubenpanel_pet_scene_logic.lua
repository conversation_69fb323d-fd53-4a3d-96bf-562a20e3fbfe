 FuBenPanelPetLogic = FuBenPanelPetLogic or BaseClass(CommonFbLogic)
local PET_STATE = {
	NONE = 0,
	STATE_DEFAULT_POINT = 1,
	STATE_TO_AUTO_FIGHT = 2,
}

function FuBenPanelPetLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.record_time = 0
	self.cur_state = PET_STATE.NONE
	self.pet_fb_cfg = nil
	self.pet_fb_level = nil
end

function FuBenPanelPetLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	self.pet_fb_cfg = nil
	self.pet_fb_level = nil
end

function FuBenPanelPetLogic:Enter(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
		ViewManager.Instance:CloseAll()
	end

	FuBenWGCtrl.Instance:CSFBPlayedCG()

	self.main_role = Scene.Instance:GetMainRole()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.FbName.XianLingShengDian)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		FuBenPanelWGCtrl.Instance:OpenPetTaskView()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_PETBEN)
	if FuBenWGData.Instance:DefFBState() and FuBenWGData.Instance:GetCurPetIndex() then
		FuBenWGData.Instance:DefFBState(false)
	end

	self.goddess_obj = nil

	self:GetGoddessObj()

	if not self.monster_enter_event1 then
		self.monster_enter_event1 = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER,BindTool.Bind(self.ProGetGoddessObj,self))
	end
	if not self.goddess_hurt_event then
		self.goddess_hurt_event = GlobalEventSystem:Bind(OtherEventType.GODDESS_HURT_FLOAT_TEXT, BindTool.Bind(self.OnGoddessHurt,self))
    end
    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function FuBenPanelPetLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end

function FuBenPanelPetLogic:GetGoddessObj()
	-- 这里拿到的level有问题，不及时，我进入level1的副本但是第一次拿到的数据level还是0
	local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
	self.pet_fb_level = pet_scence_info.level
	if pet_scence_info and not IsEmptyTable(pet_scence_info) then
		self.pet_fb_cfg = FuBenPanelWGData.Instance:GetPetItemByLayer(pet_scence_info.level)
		if self.pet_fb_cfg then
			self.monster_id = self.pet_fb_cfg.xiannv_id
		end
	end
	self:GetGoddessObjByMonsterId(self.monster_id)
end

function FuBenPanelPetLogic:ProGetGoddessObj(monster_vo)
	--刷新有问题，改下(策划说基本就一个level没及时刷新也没事)
	local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
	if self.pet_fb_level ~= pet_scence_info.level then
		self:GetGoddessObj()
	end

	if monster_vo.monster_id == self.monster_id then
		self:GetGoddessObjByMonsterId(self.monster_id)
	end
end

function FuBenPanelPetLogic:GetGoddessObjByMonsterId(monster_id)
	self.goddess_obj = Scene.Instance:GetMonstObjByMonstID(monster_id)
	if self.goddess_obj and self.goddess_obj.draw_obj then
		local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
		self.pet_fb_cfg = FuBenPanelWGData.Instance:GetPetItemByLayer(pet_scence_info.level)
		
		local rotation_params = nil
		local position_params = nil
		if self.pet_fb_cfg then
			rotation_params = Split(self.pet_fb_cfg.huanhua_rotation, "|")
			position_params = Split(self.pet_fb_cfg.huanhua_position, "|")
		end
		
		if rotation_params then
			local rot_x, rot_y, rot_z = tonumber(rotation_params[1]), tonumber(rotation_params[2]), tonumber(rotation_params[3])
			self.goddess_obj.draw_obj:SetRotation(Quaternion.Euler(rot_x or 0, rot_y or 0, rot_z or 0))
		end

		if position_params then
			local pos_x, pos_y, pos_z = tonumber(position_params[1]), tonumber(position_params[2]), tonumber(position_params[3])
			self.goddess_obj.draw_obj.root_transform.localPosition = Vector3(pos_x or 0, pos_y or 0, pos_z or 0)
		end
	end
end

function FuBenPanelPetLogic:OnGoddessHurt(blood)
	if self.goddess_obj == nil then
		return
	end

	local data = {}
	data.fighttype = FIGHT_TYPE.NORMAL
	data.blood = blood
	HUDManager.Instance:ShowHurtEnter(self.goddess_obj, data)
	
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
	for i, v in pairs(fall_item_list) do
		return
	end

	if GuajiCache.guaji_type == GuajiType.Auto then
		local pos_x, pos_y = FuBenPanelWGData.Instance:GetPetPeriPos()
		local gcl = GuajiWGCtrl.Instance
	    gcl:SetMoveToPosCallBack(function ()
	    	gcl:ClearAllOperate()
	    	gcl:ClearGuajiCache()
	        gcl:SetGuajiType(GuajiType.Auto)
	    end)

        gcl:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y + 4) --仙女前方两个格子
	end
end

function FuBenPanelPetLogic:Out(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		CommonFbLogic.Out(self)
		MainuiWGCtrl.Instance:SetTaskContents(true)
		-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(true)
		MainuiWGCtrl.Instance:SetOtherContents(false)
		FuBenPanelWGCtrl.Instance:ClosePetTaskView()
		FuBenPanelCountDown.Instance:CloseViewHandler()
	end

	FuBenPanelWGCtrl.Instance:CloseStarAniView()

	if self.monster_enter_event1 then
		GlobalEventSystem:UnBind(self.monster_enter_event1)
		self.monster_enter_event1 = nil
	end
	if self.goddess_hurt_event then
		GlobalEventSystem:UnBind(self.goddess_hurt_event)
		self.goddess_hurt_event = nil
	end
	-- GuajiWGCtrl.Instance:SetGuajiRange(nil)

	local is_false =  FuBenPanelWGData.Instance:GetPetFBReward()
	if is_false.is_pass ==  0 then
		GlobalTimerQuest:AddDelayTimer(function ()
			FuBenWGCtrl.Instance:OpenFuBenLoseView()
			-- MainuiWGCtrl.Instance:SetTaskActive(true)
			MainuiWGCtrl.Instance:SetFBNameState(false)
			MainuiWGCtrl.Instance:SetTeamBtnState(true)
		end, 1.5)

	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	if self.state_machine then
		self.state_machine:DeleteMe()
		self.state_machine = nil
	end
	if self.goddess_obj then
		self.goddess_obj = nil
	end
	if self.main_role then
		self.main_role = nil
	end
	FuBenPanelWGCtrl.Instance.is_pet_sceneani_move = false
    FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_pet)
    if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function FuBenPanelPetLogic:OpenFbSceneCd()

end

-- 获取挂机打怪的敌人
function FuBenPanelPetLogic:GetGuajiCharacter()
	return self:SelectObjHelper(Scene.Instance:GetMonsterList())
end

function FuBenPanelPetLogic:SelectObjHelper(obj_list)
	local target_obj = nil
	local target_distance = 10000
	local point_list = FuBenPanelWGData.Instance:GetGuaJiPoint()
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj, target_distance
	end

	local m_pox_x , m_pos_y = main_role:GetLogicPos()
	local g_pos_x, g_pos_y = m_pox_x , m_pos_y
	if self.goddess_obj then
		g_pos_x, g_pos_y = self.goddess_obj:GetLogicPos()
	end

	local my_is_in_polygon = GameMath.IsInPolygon(point_list, u3d.vec2(m_pox_x, m_pos_y))
	local skill_info = SkillWGData.Instance:GetFirstCommonSkillInfo()
	local skill_cfg = nil
	if skill_info ~= nil and skill_info.skill_id ~= 0 then
		skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_info.skill_id, 1)
	end

	local skill_dis_pow2 = 0
	if skill_cfg then
		skill_dis_pow2 = skill_cfg.distance * skill_cfg.distance
	end

	--找到距离goddess最近的
	for _, v in pairs(obj_list) do
		if v:IsCharacter() or v:GetModel():IsVisible() then
			local can_select = Scene.Instance:IsEnemy(v, main_role)
			local target_x, target_y = v:GetLogicPos()
			if can_select then
				can_select = GameMath.IsInPolygon(point_list, u3d.vec2(target_x, target_y))
				-- 策划需求，如果怪在范围外，人在挂机范围内，这个时候如果怪在普攻的范围内，要去打怪
				if not can_select and my_is_in_polygon and skill_cfg ~= nil then
					if GameMath.GetDistance(m_pox_x , m_pos_y, target_x, target_y, false) <= skill_dis_pow2 then
						can_select = true
					end
				end
			end

			if can_select then
				local distance = GameMath.GetDistance(g_pos_x, g_pos_y, target_x, target_y, false)
				if distance < target_distance then
					if v:IsInBlock() then
						if nil == target_obj then
							target_obj = v
						end
					else
						target_obj = v
						target_distance = distance
					end
				end
			end
		end
	end

	return target_obj, target_distance
end

function FuBenPanelPetLogic:SpecialSelectEnemy()
	if GuajiCache.guaji_type == GuajiType.Auto then
		return true
	end
	return false
end

-- 获取挂机打怪的位置(不去主动找怪)
function FuBenPanelPetLogic:GetGuiJiMonsterPos()
	return nil
end

function FuBenPanelPetLogic:IsMonsterEnemy(target_obj, main_role)
	if target_obj and self.goddess_obj then
		if target_obj.vo.monster_id == self.goddess_obj:GetMonsterId() then --.vo.monster_id
			return false
		end
	end

	return BaseSceneLogic.IsMonsterEnemy(self, target_obj, main_role)
end

-- 是否达到指定条件搜索全地图怪
function FuBenPanelPetLogic:ConditionScanMonster(vo)
	--宠物本需要判断挂机范围
	local point_list = FuBenPanelWGData.Instance:GetGuaJiPoint()
	return GameMath.IsInPolygon(point_list, u3d.vec2(vo.pos_x, vo.pos_y))
end

function FuBenPanelPetLogic:GetGuajiPos()
	 local is_auto = FuBenPanelWGCtrl.Instance:SetPetFBPrepareTime()
	 if is_auto then
	 	return nil, nil
	 end

	 return FuBenPanelWGData.Instance:GetPetGuaJiPos()
end

function FuBenPanelPetLogic:GetGuaJiDir()
	return FuBenPanelWGData.Instance:GetPetGuaJiDir()
end