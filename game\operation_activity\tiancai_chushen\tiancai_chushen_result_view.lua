ChuShenCookResultPanel = ChuShenCookResultPanel or BaseClass(SafeBaseView)

function ChuShenCookResultPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/operation_tiancai_chushen_prefab", "layout_tiancai_cook_result_view")
end

function ChuShenCookResultPanel:LoadIndexCallBack()
	self.grop_list_1 = {}
	self.grop_list_2 = {}
	self.grop_list_0 = {}
	for i=1,7 do
		local cell = ItemCell.New(self.node_list["goods_reward_"..i])
		self.grop_list_1[i] = cell

		local cell1 = ItemCell.New(self.node_list["goods_reward_1_"..i])
		self.grop_list_2[i] = cell1

		local cell_2 = ItemCell.New(self.node_list["goods_reward_2_"..i])
		self.grop_list_0[i] = cell_2
	end


	-- self.grop_list_1 = AsyncListView.New(ChuShenRewardRender, self.node_list.group_1)
	-- self.grop_list_2 = AsyncListView.New(ChuShenRewardRender, self.node_list.group_2)
	-- self.grop_list_0 = AsyncListView.New(ChuShenRewardRender, self.node_list.group_1_1)

	-- local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	-- local bundle_name, asset_name = ResPath.GetEffectUi(interface_cfg.pic_21)
	-- local b,a = ResPath.GetEffectUi(interface_cfg.pic_24)
	-- self.node_list.parti_pos_1:ChangeAsset(b, a)
	-- self.node_list.parti_pos_2:ChangeAsset(b, a)
	
 end 

 function ChuShenCookResultPanel:LoadSkeleAni()
 	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
 	if nil == interface_cfg then
 		return
 	end


	-- self.ske_ani_big = AllocAsyncLoader(self, "piaodai_big_1")
	-- self.ske_ani_big:SetIsUseObjPool(true)
	-- self.ske_ani_big:SetParent(self.node_list.big_bg_type_1.transform)
	-- self.ske_ani_big:Load("uis/view/operation_tiancai_chushen_prefab", interface_cfg.pic_41)

	-- self.ske_ani_big1 = AllocAsyncLoader(self, "piaodai_small_1")
	-- self.ske_ani_big1:SetIsUseObjPool(true)
	-- self.ske_ani_big1:SetParent(self.node_list.big_bg_type_1_1.transform)
	-- self.ske_ani_big1:Load("uis/view/operation_tiancai_chushen_prefab", interface_cfg.pic_42)

	-- self.ske_ani_big2 = AllocAsyncLoader(self, "flower_skeani_1")
	-- self.ske_ani_big2:SetIsUseObjPool(true)
	-- self.ske_ani_big2:SetParent(self.node_list.flower_group_1.transform) 
	-- self.ske_ani_big2:Load("uis/view/operation_tiancai_chushen_prefab", interface_cfg.pic_43)


	-- self.ske_ani_big3 = AllocAsyncLoader(self, "flower_skeani_1_1")
	-- self.ske_ani_big3:SetIsUseObjPool(true)
	-- self.ske_ani_big3:SetParent(self.node_list.flower_group_1_1.transform)
	-- self.ske_ani_big3:Load("uis/view/operation_tiancai_chushen_prefab", interface_cfg.pic_43)
 end

function ChuShenCookResultPanel:ReleaseCallBack()
	-- if self.grop_list_0 then
	-- 	self.grop_list_0:DeleteMe()
	-- 	self.grop_list_0 = nil
	-- end

	-- if self.grop_list_1 then
	-- 	self.grop_list_1:DeleteMe()
	-- 	self.grop_list_1 = nil
	-- end

	-- if self.grop_list_2 then
	-- 	self.grop_list_2:DeleteMe()
	-- 	self.grop_list_2 = nil
	-- end



	self.cook_type = nil
	self.menu_id = nil

	self.ske_ani_big0 = nil
	self.ske_ani_big1 = nil
	self.ske_ani_big2 = nil
	self.ske_ani_big3 = nil
end

 function ChuShenCookResultPanel:OnFlush()
 	if nil == self.cook_type then
 		return
 	end
 	local menu_name = 0
 	local oa_index = ChuShenWGData.Instance:GetChuShenActInfoData()
	local reward_cfg = ChuShenWGData.Instance:GetChuShenParamCfg()
	local reward_base_cfg, reward_cfg1 = ChuShenWGData.Instance:GetMenuListCfg()
	local data_list = {}
	local unlock_reward_list = {}
	local is_special_menu = false
	local special_id = 0
	local quality_color = 2
	for k,v in pairs(reward_base_cfg) do
		if v.is_special == 1 then
			special_id = v.item_id
			break
		end
	end

	if self.menu_id > 0 then
		for k,v in pairs(reward_cfg1) do
			if v.id == self.menu_id then
				data_list = v.reward_item
				menu_name = v.menu_name_image
				local temp_data = v.consume_item
				for i=0,2 do
					if temp_data[i] and temp_data[i].item_id == special_id then
						is_special_menu = true
						break
					end
				end

				local b, a =  ResPath.GetF2CommonImages("yuan_spe_color_"..v.menu_quality)
				quality_color = v.menu_quality
				self.node_list["item_cell_image_bg_1"].image:LoadSprite(b, a)
				self.node_list["item_cell_image_bg_1_1"].image:LoadSprite(b, a)
				local parti_b, parti_a = ChuShenWGData.Instance:GetPartiAsesst(v.menu_quality)
				self.node_list["item_cell_image_parti_1"]:ChangeAsset(parti_b, parti_a)
				self.node_list["item_cell_image_parti_1_1"]:ChangeAsset(parti_b, parti_a)
				self.node_list["item_cell_image_parti_1"]:SetActive(v.menu_quality > SHOW_EFFECT_LEVEL)
				self.node_list["item_cell_image_parti_1_1"]:SetActive(v.menu_quality > SHOW_EFFECT_LEVEL)

				local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.menu_id)
				local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
				self.node_list["item_cell_image_1"].image:LoadSprite(center_image_b, center_image_a, function ()
						XUI.ImageSetNativeSize(self.node_list["item_cell_image_1"])
					end)

				self.node_list["item_cell_image_1_1"].image:LoadSprite(center_image_b, center_image_a, function ()
						XUI.ImageSetNativeSize(self.node_list["item_cell_image_1_1"])
					end)
				unlock_reward_list = v.unlock_item
				break
			end
		end
		
	else
		for k,v in pairs(reward_cfg) do
			if oa_index == v.oa_index then
				data_list = self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_4 and v.fail_reward_item_2 or v.fail_reward_item
				unlock_reward_list = v.unlock_all_item
				break
			end
		end

		local other_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
		local default_item_id = self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_4 and other_cfg.mid_cuisine_icon or other_cfg.dark_cuisine_icon
		menu_name = self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_4 and other_cfg.menu_name_image or 0
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(default_item_id)
		local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
		self.node_list["item_cell_image_1"].image:LoadSprite(center_image_b, center_image_a, function ()
				XUI.ImageSetNativeSize(self.node_list["item_cell_image_1"])
			end)

		self.node_list["item_cell_image_1_1"].image:LoadSprite(center_image_b, center_image_a, function ()
				XUI.ImageSetNativeSize(self.node_list["item_cell_image_1_1"])
			end)
		local b, a =  ResPath.GetF2CommonImages("yuan_spe_color_"..item_cfg.color)
		quality_color = item_cfg.color
		self.node_list["item_cell_image_bg_1"].image:LoadSprite(b, a)
		self.node_list["item_cell_image_bg_1_1"].image:LoadSprite(b, a)
		local parti_b, parti_a = ChuShenWGData.Instance:GetPartiAsesst(item_cfg.color)
		self.node_list["item_cell_image_parti_1"]:ChangeAsset(parti_b, parti_a)
		self.node_list["item_cell_image_parti_1_1"]:ChangeAsset(parti_b, parti_a)
		self.node_list["item_cell_image_parti_1"]:SetActive(item_cfg.color > SHOW_EFFECT_LEVEL)
		self.node_list["item_cell_image_parti_1_1"]:SetActive(item_cfg.color > SHOW_EFFECT_LEVEL)
	end

	local bundle, asset = ResPath.GetChuShenImg("menu_image_"..menu_name)
	local unlock_b, unlock_a = ResPath.GetChuShenImg("unlock_success_image_"..quality_color)
	local success_b, success_a = ResPath.GetChuShenImg("cooking_success_image_"..quality_color)
 	self.node_list.desc_1.text.text = ""
	self.node_list.desc_1_1.text.text = ""

	self.node_list["desc_name_1"].image:LoadSprite(bundle, asset, function ()
		XUI.ImageSetNativeSize(self.node_list["desc_name_1"])
	end)
	self.node_list["desc_name_1_1"].image:LoadSprite(bundle, asset, function ()
		XUI.ImageSetNativeSize(self.node_list["desc_name_1_1"])
	end)

	self.node_list["desc_text_1"].image:LoadSprite(unlock_b, unlock_a, function ()
		XUI.ImageSetNativeSize(self.node_list["desc_text_1"])
	end)
	self.node_list["desc_text_1_1"].image:LoadSprite(success_b, success_a, function ()
		XUI.ImageSetNativeSize(self.node_list["desc_text_1_1"])
	end)

 	if self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_1 	-- 失败
 	or self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_2 
 	or self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_4 then  -- 成功但没解锁新菜谱

 	 	self.node_list.all_group:SetActive(false)
 	 	self.node_list.desc_1:SetActive(false)
 	 	self.node_list.desc_1_1:SetActive(true)
 	 	self.node_list.desc_2:SetActive(false)
 		self.node_list.desc_3:SetActive(false)
 		self.node_list.big_bg_type_1:SetActive(false)
 		self.node_list.big_bg_type_2:SetActive(false)
 		self.node_list.item_cell_image_bg_1:SetActive(false)
 		-- self.node_list.flower_group_1:SetActive(false)

 		-- self.node_list.flower_group_1_1:SetActive(true)
 		self.node_list.item_cell_image_bg_1_1:SetActive(true)
 		self.node_list.group_1_1:SetActive(true)
 		self.node_list.desc_2_1:SetActive(true)
 		self.node_list.big_bg_type_1_1:SetActive(is_special_menu)
 		self.node_list.big_bg_type_2_1:SetActive(not is_special_menu)

 		if self.grop_list_0 then
 			--self.grop_list_0:SetDataList(self.result_reward_list)
 			for k,v in pairs(self.grop_list_0) do
 				self.node_list["goods_reward_2_"..k]:SetActive(nil ~= self.result_reward_list[k - 1])
 				if self.result_reward_list[k - 1] then
 					v:SetData(self.result_reward_list[k - 1])
 				end 
 			end
 		end


 		self.node_list.desc_2_1.text.text = Language.Activity.ChuShenGetRewARD

 	elseif self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_3 then -- 成功并解锁新菜谱

 		self.node_list.all_group:SetActive(true)
 	 	self.node_list.desc_1:SetActive(true)
 	 	self.node_list.desc_1_1:SetActive(false)
 	 	self.node_list.desc_2:SetActive(true)
 		self.node_list.desc_3:SetActive(true)
 		self.node_list.big_bg_type_1:SetActive(is_special_menu)
 		self.node_list.big_bg_type_2:SetActive(not is_special_menu)
 		self.node_list.item_cell_image_bg_1:SetActive(true)
 		-- self.node_list.flower_group_1:SetActive(true)

 		-- self.node_list.flower_group_1_1:SetActive(false)
 		self.node_list.item_cell_image_bg_1_1:SetActive(false)
 		self.node_list.group_1_1:SetActive(false)
 		self.node_list.desc_2_1:SetActive(false)
 		self.node_list.big_bg_type_1_1:SetActive(false)
 		self.node_list.big_bg_type_2_1:SetActive(false)
 		if self.grop_list_1 then
 			--self.grop_list_1:SetDataList(self.unlock_reware_list)
 			for k,v in pairs(self.grop_list_1) do
 				self.node_list["goods_reward_"..k]:SetActive(nil ~= self.unlock_reware_list[k - 1])
 				if self.unlock_reware_list[k - 1] then
 					v:SetData(self.unlock_reware_list[k - 1])
 				end 
 			end
 		end
 		self.node_list.group_2:SetActive(true)
 		self.node_list.desc_3:SetActive(true)
 		 if self.grop_list_2 then
 		 	--self.grop_list_2:SetDataList(self.result_reward_list)
 			for k,v in pairs(self.grop_list_2) do
 				self.node_list["goods_reward_1_"..k]:SetActive(nil ~= self.result_reward_list[k - 1])
 				if self.result_reward_list[k - 1] then
 					v:SetData(self.result_reward_list[k - 1])
 				end 
 			end
 		end
 		self.node_list.desc_2.text.text = Language.Activity.ChuShenFirstUnlock
 		self.node_list.desc_3.text.text = Language.Activity.ChuShenGetRewARD
 	end

 	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
 	if nil == interface_cfg then
 		return
 	end

 	if is_special_menu then
 		if self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_3 then   --高级+解锁
 			self.node_list.bg_di_bg_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_big_bg_"..quality_color))
 			self.node_list.big_bg_type_small_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_menu_bg_"..quality_color))
 			self.node_list.bg_di_up_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_"..quality_color))
 			self.node_list.bg_top_line_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_line_"..quality_color))
 			self.node_list.bg_buttom_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_buttom_"..quality_color))
 		else
 			self.node_list.bg_di_bg_1_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_big_bg_"..quality_color))
 			self.node_list.big_bg_type_small_1_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_menu_bg_"..quality_color))	
 			self.node_list.bg_di_up_1_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_"..quality_color))
 			self.node_list.bg_top_line_1_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_line_"..quality_color))
 			self.node_list.bg_buttom_1_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_buttom_"..quality_color))																		--高级
 		end
 	else
 		if self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_3 then   --中级+解锁
 			self.node_list.bg_di_bg_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_big_bg_"..quality_color))
 			self.node_list.big_bg_type_small_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_menu_bg_"..quality_color))
 			self.node_list.bg_di_up_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_"..quality_color))
 			self.node_list.bg_top_line_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_line_"..quality_color))
 			self.node_list.bg_buttom_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_buttom_"..quality_color))
 		else           
 			self.node_list.bg_di_bg_2_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_big_bg_"..quality_color))
 			self.node_list.big_bg_type_small_1_2.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_menu_bg_"..quality_color))        
 			self.node_list.bg_di_up_2_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_"..quality_color))
 			self.node_list.bg_top_line_2_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_top_line_"..quality_color))
 			self.node_list.bg_buttom_2_1.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("result_bg_buttom_"..quality_color))     													--中级
 		end
 	end
 	self:LoadSkeleAni()
 end

 function ChuShenCookResultPanel:CloseCallBack()
 	if self.cook_type == TIANCAICHUSHEN_COOK_TYPE.TIANCAICHUSHEN_COOK_TYPE_3 then -- 成功并解锁新菜谱
 		local _, cfg = ChuShenWGData.Instance:GetMenuListCfg()
 		local all_num = #cfg or 1
		local has_num = ChuShenWGData.Instance:GetAllUnlockNum()
		if all_num == has_num then
			ChuShenWGCtrl.Instance:OpenRewardPanel()
		end
 	end
 end

function ChuShenCookResultPanel:SetDataOpen(protocol)
	if not protocol.cook_type or not protocol.id then
		return
	end

	self.cook_type = protocol.cook_type
	self.menu_id = protocol.id
	self.result_reward_list = protocol.reware_list
	self.unlock_reware_list = protocol.unlock_reware_list
	self:Open()
end

---------------------------------------------------------------------------
ChuShenRewardShowPanel = ChuShenRewardShowPanel or BaseClass(SafeBaseView)

function ChuShenRewardShowPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(4, 6), sizeDelta = Vector2(648, 362)})
	self:AddViewResource(0, "uis/view/operation_tiancai_chushen_prefab", "layout_tiancai_unlock_result_view")
end

function ChuShenRewardShowPanel:LoadIndexCallBack()
	self.node_list.title_view_name.text.text = Language.Activity.ChuShenRankReward
	self.grop_list_1 = {}
	for i=1,3 do
		local cell = ItemCell.New(self.node_list["goods_reward_"..i])
		self.grop_list_1[i] = cell
	end
	XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind(self.GetReward, self))
 end 

 function ChuShenRewardShowPanel:ReleaseCallBack()

 end

 function ChuShenRewardShowPanel:OnFlush()
 	local _, cfg = ChuShenWGData.Instance:ChunShenOpenLimit()
 	local data_list = cfg and cfg.unlock_all_item
 	if self.grop_list_1 then
		for k,v in pairs(self.grop_list_1) do
			self.node_list["goods_reward_"..k]:SetActive(nil ~= data_list[k - 1])
			if data_list[k - 1] then
				v:SetData(data_list[k - 1])
			end 
		end
	end

	local _, all_num = ChuShenWGData.Instance:GetMenuListCfg()
	local has_num = ChuShenWGData.Instance:GetAllUnlockNum()
	self.node_list.desc_2.text.text = string.format(Language.Activity.ChuShenProcess, has_num, #all_num)
	local _, unlock_all = ChuShenWGData.Instance:GetChuShenActInfoData()

	if unlock_all == 0 or unlock_all == 2 then
 		XUI.SetGraphicGrey(self.node_list["get_btn"], true)
 	else
 		XUI.SetGraphicGrey(self.node_list["get_btn"], false)
 	end

 end

 function ChuShenRewardShowPanel:GetReward()
 	local _, unlock_all = ChuShenWGData.Instance:GetChuShenActInfoData()
	if unlock_all == 0 then
	 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenNotAllUnlock)
	 	return
 	elseif unlock_all == 2 then
 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenGetReward1)
 		return
 	end
 	ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_6)
 	ChuShenWGData.Instance:ChuShenUnlockRemindMark()
 	self:Close()
 end