--二次确认界面，带倒计时
SecondConfirmationView = SecondConfirmationView or BaseClass(SafeBaseView)

function SecondConfirmationView:__init()
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/dialog_ui_prefab", "second_confirmation_view")
end

function SecondConfirmationView:__delete()
end

function SecondConfirmationView:CloseCallBack()
	self:ClearCountDown()
end

function SecondConfirmationView:ReleaseCallBack()
	self.content_txt = nil
	self.ok_func = nil
	self.close_func = nil
	self.ok_txt = nil
	self.count_down_time = nil
	self:ClearCountDown()
end

function SecondConfirmationView:ShowIndexCallBack()
	self:Flush()
end

function SecondConfirmationView:SetData(content_txt,ok_func,close_func,ok_txt,count_down_time)
	self.content_txt = content_txt
	self.ok_func = ok_func
	self.close_func = close_func
	self.ok_txt = ok_txt
	self.count_down_time = count_down_time
	self:Open()
end

function SecondConfirmationView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile
	-- self:SetSecondView(nil, self.node_list["size"])
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
   -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self.node_list.btn_cancel.button:AddClickListener(BindTool.Bind(self.OnClickCancel,self))
	self.node_list.btn_OK.button:AddClickListener(BindTool.Bind(self.OnClickOK,self))
	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.OnClickClose,self))
end

function SecondConfirmationView:ClearCountDown()
	if CountDown.Instance:HasCountDown(self.count_down) then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function SecondConfirmationView:OnFlush()
	self.node_list.rich_dialog.text.text = self.content_txt or ""
	self:ClearCountDown()
	if self.count_down_time and self.count_down_time > 0 then
		self.count_down = CountDown.Instance:AddCountDown(self.count_down_time,1,BindTool.Bind(self.UpdateTime,self),BindTool.Bind(self.CompleteTime,self))
	else
		self.node_list.ok_text.text.text = self.ok_txt or ""
	end
end

function SecondConfirmationView:UpdateTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if time > 0 then
		self.node_list.ok_text.text.text = string.format("%s(%s)",self.ok_txt or "",time)
	end
end

function SecondConfirmationView:CompleteTime()
	self:ClearCountDown()
	self:OnClickOK()
end

function SecondConfirmationView:OnClickCancel()
	if self.close_func then
		self.close_func()
	else
		self:Close()
	end
end

function SecondConfirmationView:OnClickOK()
	if self.ok_func then
		self.ok_func()
	end
	self:Close()
end

function SecondConfirmationView:OnClickClose()
	self:Close()
end