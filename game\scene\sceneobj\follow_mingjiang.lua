FollowMingJiang = FollowMingJiang or BaseClass(FollowObj)

function FollowMingJiang:__init(vo)
    self.obj_type = SceneObjType.FollowMingJiang
    self.followui_class = CharacterFollow
    self.draw_obj:SetObjType(self.obj_type)

    self.res_id = vo.soulboy_lt_id
    self:SetObjId(vo.obj_id)
    self.is_visible = true
    self.status = 0

    self.follow_offset = -2
    self.sqrt_stop_distance = 6 * 6
    self.sqrt_slow_down_distance = 6 * 6
    -- self:SetMaxForce(80)
    self.mass = 0.3
    self.min_move_distance = 6 * 6

    self.record_time = self.now_time or 0
    self.record_talk_time = 0
    self.is_talk = false
    self.can_bubble = false
end

function FollowMingJiang:__delete()
    if self.bobble_timer_quest then
        GlobalTimerQuest:CancelQuest(self.bobble_timer_quest)
        self.bobble_timer_quest = nil
    end

    if self.release_timer then
        GlobalTimerQuest:CancelQuest(self.release_timer)
        self.release_timer = nil
    end

    if self.parent_scene and self.vo and self.vo.owner_obj_id then
        local owner_obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
        if owner_obj then
            owner_obj:ReleaseFollowMingJiang()
        end
    end
    self:CancelPlayActionBackFun()
end

function FollowMingJiang:InitAppearance()
    if self.draw_obj:IsDeleted() then return end

    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(false)
    end
    self:ChangeModel(SceneObjPart.Main, ResPath.GetBianShenModel(self.res_id))
    -- 跟随天神，写死武器
    self:ChangeModel(SceneObjPart.Weapon, ResPath.GetTianShenShenQiPath(100010201))

    self.exist_time = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].exist_time
    self.interval = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].interval
    self.record_time = self.record_time + self.interval
    local prefab_data_cfg = ConfigManager.Instance:GetPrefabDataAutoConfig("Mingjiang", self.res_id)

    -- 把cameraShakes震屏配置置空，但不能影响原配置表
    local prefab_data = {}
    for k,v in pairs(prefab_data_cfg) do
        if k == "actorTriggers" then
            prefab_data[k] = {}
            for k2, v2 in pairs(v) do
                if k2 == "cameraShakes" then
                    prefab_data[k][k2] = {}
                else
                    prefab_data[k][k2] = v2
                end
            end
        else
            prefab_data[k] = v
        end
    end
    self:SetActorConfigPrefabData(prefab_data)
end

function FollowMingJiang:CreateFollowUi()
    Character.CreateFollowUi(self, 0)
end

function FollowMingJiang:ReloadUIName()
    if self.follow_ui ~= nil then
        self.follow_ui:SetName(ToColorStr((self.vo and self.vo.name) or "", COLOR3B.DEFAULT), self)
    end
end

-- 战斗屏蔽随机移动
function FollowMingJiang:ShieldWadnerForce(target)
    return target.IsFightState and target:IsFightState() or false
end

function FollowMingJiang:Update(now_time, elapse_time)
    FollowObj.Update(self, now_time, elapse_time)
    if not self.can_bubble or not self:GetVisiable() then
        return
    end
    if nil ~= self.follow_ui and self:OwnerIsMainRole() then
        if now_time > self.record_time and self.is_talk == false then
            self.is_talk = true
            self:UpdataBubble()
            self.follow_ui:ForceSetVisible(true)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
                self.follow_ui:CancelForceSetVisible()
                self.follow_ui:HideBubble()
                self.is_talk = false
                self.record_time = now_time + self.interval
            end,self.exist_time)
        end
    end
end

function FollowMingJiang:ChangeStatus(status)
    self.status = status
end

function FollowMingJiang:GetIsTalk()
    -- body
    return self.is_talk
end


function FollowMingJiang:IsCharacter()
    return false
end

function FollowMingJiang:GetOwerRoleId()
    return self.vo.owner_role_id
end

function FollowMingJiang:SetTrigger(key)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        if main_part then
            main_part:SetTrigger(key)
        end
    end
end

function FollowMingJiang:SetBool(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetBool(key, value)
        end
        if weapon_part then
            weapon_part:SetBool(key, value)
        end
    end
end

function FollowMingJiang:SetInteger(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetInteger(key, value)
        end
        if weapon_part then
            weapon_part:SetInteger(key, value)
        end
    end
end

function FollowMingJiang:DoAttack(...)
    if self:IsMove() then
        return
    end
    self:CancelPlayActionBackFun()
    Character.DoAttack(self, ...)
end

function FollowMingJiang:EnterStateAttack()
    local anim_name = nil
    local info_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
    if nil ~= info_cfg then
        if TianShenWGData.Instance:GetIsTianShenNormalSkill(self.attack_skill_id) then
            anim_name = "combo" .. info_cfg.action
        else
            anim_name = SkillWGData.GetActionString(self.attack_skill_id, info_cfg.action, self.attack_index)
        end
        self.action_time_record = self:GetActionTimeRecord(anim_name)
        if self.action_time_record and self.action_time_record.speed then
            local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
            main_part:SetFloat(anim_name.."_speed", self.action_time_record.speed)
        end
    end
    Character.EnterStateAttack(self, anim_name)
end

function FollowMingJiang:CancelPlayActionBackFun()
    if self.play_action_back_fun then
        GlobalTimerQuest:CancelQuest(self.play_action_back_fun)
    end
    self.play_action_back_fun = nil
end

function FollowMingJiang:AttackActionEndHandle()
    if self.action_time_record ~= nil and self.action_time_record.has_back then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        local part_obj = part:GetObj()
        if part_obj == nil or IsNil(part_obj.gameObject) then
            return
        end
        self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnPlayActionBackEnd, self), self.action_time_record.back_time)
        part:CrossFade(self.anim_name.."_back")
    else
        self:ChangeToCommonState()
    end
end

function FollowMingJiang:OnPlayActionBackEnd()
    self:ChangeToCommonState()
end

function FollowMingJiang:IsFollowMingJiang()
    -- return true
    return true
end


function FollowMingJiang:GetRandBubbletext()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    if #bubble_cfg > 0 then
        math.randomseed(os.time())
        local bubble_text_index = math.random(1, #bubble_cfg)
        return bubble_cfg[bubble_text_index].pet_talk
    else
        return ""
    end
end

function FollowMingJiang:GetFirstBubbleText()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    return bubble_cfg[1].pet_talk
end

function FollowMingJiang:UpdataBubble()
    if nil ~= self.follow_ui then
        local text = self:GetRandBubbletext()
        self.follow_ui:ChangeBubble(text)
    end
end

function FollowMingJiang:GetActionTimeRecord(anim_name)
    if TianShenBossActionConfig[self.res_id] then
        return TianShenBossActionConfig[self.res_id][anim_name]
    end
    return RoleActionConfig[0][anim_name]
end

function FollowMingJiang:OwnerIsMainRole()
    return self.vo.owner_is_mainrole or false
end

function FollowMingJiang:FolluiVisibByFly(visible)
    if not visible then
        if self.follow_ui then
            self.follow_ui:ForceSetVisible(false)
        end
    else
        if self.follow_ui then
            self.follow_ui:CancelForceSetVisible()
        end
    end
end