---------------------------------------------------------
--可通过里快速取路径
---------------------------------------------------------
GamePath = GamePath or {}
function GamePath.DefTexture()
	return "ui/common/item_default_icon.png"
end

function GamePath.GetCommon(name)
	return string.format("ui/common/%s.png",name)
end

function GamePath.GetRole(name)
	return string.format("ui/role/%s.png",name)
end

function GamePath.GetItem(icon_id)
	return string.format("ui/item/%d.png",icon_id)
end

function GamePath.GetMount(name)
	return string.format("ui/mount/%s.png", name)
end

function GamePath.GetKf1V1(name)
	return string.format("ui/kf_1v1/%s.png", name)
end
function GamePath.GetKf3V3(name)
	return string.format("ui/kf_3v3/%s.png", name)
end

function GamePath.GetSwordSoulPiece(soul_id, piece)
	return string.format("ui/swordsoul/soul_%s_%s.png", soul_id, piece)
end

function GamePath.GetStoneRes(stone_type, stone_level)
	return string.format("ui/stone/24%s%s.png", stone_type, stone_level)
end

function GamePath.GetWing(name)
	return string.format("ui/wing/%s.png", name)
end

function GamePath.GetRoleHead(name)
	return string.format("ui/head/%s.png", name)
end

function GamePath.GetCampIcon(name)
	return string.format("ui/mainui/camp_%s.png", name)
end

function GamePath.GetChooseBtn(name)
	return string.format("ui/mainui/%s.png", name)
end

function GamePath.GetMainui(name)
	return string.format("ui/mainui/%s.png", name)
end

function GamePath.GetField1v1(name)
	return string.format("ui/field1v1/%s.png", name)
end

function GamePath.GetSkillIconById(icon_id)
	return string.format("ui/skill/%s.png", icon_id)
end

function GamePath.GetSkillWordIcon(id)
	return string.format("ui/skill/skill_word_%d.png", id)
end

function GamePath.GetChatMark(name)
	return string.format("ui/chat/%s_mark.png", name)
end

function GamePath.GetFace(index)
	return string.format("ui/face/%02d.png", index)
end

function GamePath.GetChat(name)
	return string.format("ui/chat/%s.png", name)
end

function GamePath.GetNpcPic(npc_id)
	return string.format("ui/npc/%d.png", npc_id)
end

function GamePath.GetGuild(name)
	return string.format("ui/guild/%s.png", name)
end

function GamePath.GetGuildBattlePath(name)
	return string.format("ui/guildbattle/%s.png", name)
end

function GamePath.GetEquipment(name)
	return string.format("ui/equipment/%s.png", name)
end

function GamePath.GetLogin(name)
	return string.format("ui/login/%s.png", name)
end

function GamePath.GetRoleCreate(name)
	return string.format("ui/role_create/%s.png", name)
end

function GamePath.GetMap(name)
	return string.format("ui/map/%s.png", name)
end

function GamePath.GetSociety(name)
	return string.format("ui/society/%s.png", name)
end

function GamePath.GetManyTower(name)
	return string.format("ui/manytower/%s.png", name)
end

function GamePath.GetWelfare(name)
	return string.format("ui/welfare/%s.png", name)
end

function GamePath.GetDailyResPath(name)
	return string.format("ui/daily/%s.png", name)
end

function GamePath.GetGuide(name)
	return string.format("ui/guide/%s.png", name)
end

function GamePath.GetVipResPath(name)
	return string.format("ui/vip/%s.png", name)
end

function GamePath.GetActivityResPath(name)
	return string.format("ui/activity/%s.png", name)
end

function GamePath.GetMarryResPath(name)
	return string.format("ui/marry/%s.png", name)
end

function GamePath.GetFirstRecharge(name)
	return string.format("ui/firstrecharge/%s.png", name)
end

function GamePath.GetTheluckyrollerPath(name)
	return string.format("ui/theluckyroller/%s.png", name)
end

function GamePath.GetFightResPath(name)
	return string.format("ui/fight/%s.png", name)
end

function GamePath.GetRankResPath(name)
	return string.format("ui/rank/%s.png", name)
end

function GamePath.GetOpenServerActivity(name)
	return string.format("ui/openserver/%s.png", name)
end

function GamePath.GetRechargePath(name)
	return string.format("ui/recharge/%s.png", name)
end

function GamePath.GetBit32(name)
	return string.format("ui/bit32/%s.png", name)
end

function GamePath.GetBit8(name)
	return string.format("ui/bit8/%s.png", name)
end

function GamePath.GetWord(name)
	return string.format("ui/word/%s.png", name)
end

-- 场景相关
function GamePath.GetScene(name)
	return string.format("ui/scene/%s.png", name)
end

function GamePath.GetSceneMapPath(res_id, x, y)
	local config = Config_scenelist[res_id]
	config = nil ~= config and config or {}
	return string.format("scene/%d/front/pic%d_%d.jpg", config.res_id or 101, x, y)
end

--大图资源获取
function GamePath.GetBigPainting(name, is_jpg)
	local suffix = is_jpg and "jpg" or "png"
	return string.format("ui/painting/%s.%s",name, suffix)
end

function GamePath.GetPainting(name, is_jpg)
	local suffix = is_jpg and "jpg" or "png"
	return string.format("painting/%s.%s" , name, suffix)
end

function GamePath.GetTitle(name)
	return string.format("ui/title/%s.png",name)
end

function GamePath.GetLogoPath()
	local res_name = "big_logo_clxxz"
	if AgentAdapter.GetLogoResName then
		res_name = AgentAdapter:GetLogoResName()
	end
	return string.format("ui/painting/%s.png", res_name)
end

function GamePath.GetLoadingBgPath()
	local res_name = "loading_bg_clxxz";
	if AgentAdapter.GetLoadingbgName then
		res_name = AgentAdapter:GetLoadingbgName()
	end
	return string.format("ui/login/%s.jpg", res_name)
end

function GamePath.GetManyTowerSkillPath(id)
	return string.format("ui/manytower/skill_%d.png", id)
end


function GamePath.GetSuperGift(name)
	return string.format("ui/supergift/%s.png",name)
end

function GamePath.GetCarnival(name)
	return string.format("ui/carnival/%s.png",name)
end

function GamePath.GetCardZu(name)
	return string.format("ui/cardzu/%s.png", name)
end

function GamePath.GetSuperVip(name)
	return string.format("ui/supervip/%s.png", name)
end