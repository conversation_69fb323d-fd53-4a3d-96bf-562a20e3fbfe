﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ToLuaProfileReWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ToLuaProfile), typeof(System.Object));
		<PERSON><PERSON>RegFunction("AddProfileBeginSample", AddProfileBeginSample);
		<PERSON><PERSON>RegFunction("AddProfileEndSample", AddProfileEndSample);
		<PERSON><PERSON>unction("New", _CreateToLuaProfile);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateToLuaProfile(IntPtr L)
	{
		try
		{
#if UNITY_EDITOR || UNITY_STANDALONE
			UnityEngine.Profiling.Profiler.BeginSample("--_Create ToLuaProfile--");
#endif

			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				ToLuaProfile obj = new ToLuaProfile();
				ToLua.PushObject(L, obj);
#if UNITY_EDITOR || UNITY_STANDALONE
			UnityEngine.Profiling.Profiler.EndSample();
#endif

				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: ToLuaProfile.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddProfileBeginSample(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			ToLuaProfile.AddProfileBeginSample(arg0);

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddProfileEndSample(IntPtr L)
	{
		try
        {
            ToLua.CheckArgsCount(L, 0);
			ToLuaProfile.AddProfileEndSample();

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

