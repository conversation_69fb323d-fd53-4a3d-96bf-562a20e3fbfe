YanYuGePrivilegeView = YanYuGePrivilegeView or BaseClass(SafeBaseView)

function YanYuGePrivilegeView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    
    self.default_index = TabIndex.yanyuge_privilege_wltz
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(TabIndex.yanyuge_privilege_wltz, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_wltz_view")
    self:AddViewResource(TabIndex.yanyuge_privilege_yytq, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_yytq_view")
    self:AddViewResource(TabIndex.yanyuge_privilege_nztq, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_nztq_view")
    -- self:AddViewResource(TabIndex.yanyuge_privilege_zztq, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_zztq_view")
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_tz_common_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function YanYuGePrivilegeView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_nztq, BindTool.Bind(self.OnClickNZTQBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_wltz, BindTool.Bind(self.OnClickWLTZBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_yytq, BindTool.Bind(self.OnClickYYTQBtn, self))
    -- XUI.AddClickEventListener(self.node_list.btn_zztq, BindTool.Bind(self.OnClickZZTQBtn, self))
    XUI.AddClickEventListener(self.node_list.score_bg, BindTool.Bind(self.OnClickScoreBtn, self))

    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
    local item_cfg = ItemWGData.Instance:GetItemConfig(show_item)
    if item_cfg then
        self.node_list.score_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    local other_remind_list = {RemindName.YanYuGe_Privilege_WLTZ, RemindName.YanYuGe_Privilege_NZTQ, RemindName.YanYuGe_Privilege_YYTQ, RemindName.YanYuGe_Privilege_ZZTQ}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

    FunOpen.Instance:RegisterFunUi(FunName.YanYuGePrivilegeNZTQ, self.node_list["btn_nztq"])
    FunOpen.Instance:RegisterFunUi(FunName.YanYuGePrivilegeWLTZ, self.node_list["btn_wltz"])
    -- FunOpen.Instance:RegisterFunUi(FunName.YanYuGePrivilegeZZTQ, self.node_list["btn_zztq"])
    FunOpen.Instance:RegisterFunUi(FunName.YanYuGePrivilegeYYTQ, self.node_list["btn_yytq"])
end

function YanYuGePrivilegeView:LoadIndexCallBack(index)
    if index == TabIndex.yanyuge_privilege_wltz then
        self:WLTZLoadCallBack()
    elseif index == TabIndex.yanyuge_privilege_yytq then
        self:YYTQLoadCallBack()
    elseif index == TabIndex.yanyuge_privilege_nztq then
        self:NZTQLoadCallBack()
    -- elseif index == TabIndex.yanyuge_privilege_zztq then
    --     self:ZZTQLoadCallBack()
    end
end

function YanYuGePrivilegeView:ShowIndexCallBack(index)
    local bundle, asset 

    if index == TabIndex.yanyuge_privilege_wltz then
        self:WLTZShowIndexCallBack()
        bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj")
    elseif index == TabIndex.yanyuge_privilege_yytq then
        self:YYTQShowIndexCallBack()
        bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj2")
    elseif index == TabIndex.yanyuge_privilege_nztq then
        self:NZTQShowIndexCallBack()
        bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj2")
    -- elseif index == TabIndex.yanyuge_privilege_zztq then
    --     self:ZZTQShowIndexCallBack()
    --     bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj")
    end

    if self.node_list.RawImage_tongyong and nil~= bundle and nil ~= asset then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    self.node_list.btn_nztq_select:CustomSetActive(index == TabIndex.yanyuge_privilege_nztq)
    self.node_list.btn_wltz_select:CustomSetActive(index == TabIndex.yanyuge_privilege_wltz)
    self.node_list.btn_yytq_select:CustomSetActive(index == TabIndex.yanyuge_privilege_yytq)
    -- self.node_list.btn_zztq_select:CustomSetActive(index == TabIndex.yanyuge_privilege_zztq)
end

function YanYuGePrivilegeView:ReleaseCallBack()
    self:WLTZReleaseCallBack()
    self:YYTQReleaseCallBack()
    self:NZTQReleaseCallBack()
    -- self:ZZTQReleaseCallBack()

    if self.remind_callback then
        RemindManager.Instance:UnBind(self.remind_callback)
        self.remind_callback = nil
    end
end

function YanYuGePrivilegeView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.yanyuge_privilege_wltz then
                self:WLTZOnFlush(param_t)
            elseif index == TabIndex.yanyuge_privilege_yytq then
                self:YYTQOnFlush(param_t)
            elseif index == TabIndex.yanyuge_privilege_nztq then
                self:NZTQOnFlush(param_t)
            -- elseif index == TabIndex.yanyuge_privilege_zztq then
            --     self:ZZTQOnFlush(param_t)
            end
        end
    end

    local score = YanYuGeWGData.Instance:GetCurScore()
    self.node_list.cur_score.tmp.text = score
end

function YanYuGePrivilegeView:OnClickNZTQBtn()
    self:ChangeToIndex(TabIndex.yanyuge_privilege_nztq)
end

function YanYuGePrivilegeView:OnClickWLTZBtn()
    self:ChangeToIndex(TabIndex.yanyuge_privilege_wltz)
end

function YanYuGePrivilegeView:OnClickYYTQBtn()
    self:ChangeToIndex(TabIndex.yanyuge_privilege_yytq)
end

-- function YanYuGePrivilegeView:OnClickZZTQBtn()
--     self:ChangeToIndex(TabIndex.yanyuge_privilege_zztq)
-- end

function YanYuGePrivilegeView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.YanYuGe_Privilege_WLTZ then
        self.node_list.btn_wltz_remind:SetActive(num > 0)
    elseif remind_name == RemindName.YanYuGe_Privilege_NZTQ then
        self.node_list.btn_nztq_remind:SetActive(num > 0)
    elseif remind_name == RemindName.YanYuGe_Privilege_YYTQ then
        self.node_list.btn_yytq_remind:SetActive(num > 0)
    -- elseif remind_name == RemindName.YanYuGe_Privilege_ZZTQ then
    --     self.node_list.btn_zztq_remind:SetActive(num > 0)
    end
end

function YanYuGePrivilegeView:OnClickScoreBtn()
    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")

    if show_item and show_item > 0 then
        TipWGCtrl.Instance:OpenItem({ item_id = show_item })
    end
end

local TAB_INDEX_CFG = {
    [TabIndex.yanyuge_privilege_wltz] = {FunName = FunName.YanYuGePrivilegeWLTZ, RemindName = RemindName.YanYuGe_Privilege_WLTZ},
    [TabIndex.yanyuge_privilege_yytq] = {FunName = FunName.YanYuGePrivilegeYYTQ, RemindName = RemindName.YanYuGe_Privilege_YYTQ},
    [TabIndex.yanyuge_privilege_nztq] = {FunName = FunName.YanYuGePrivilegeNZTQ, RemindName = RemindName.YanYuGe_Privilege_NZTQ},
    -- [TabIndex.yanyuge_privilege_zztq] = {FunName = FunName.YanYuGePrivilegeZZTQ, RemindName = RemindName.YanYuGe_Privilege_ZZTQ}
}

function YanYuGePrivilegeView:CalcShowIndex()
    local default_index = 0

    for k, v in pairs(TAB_INDEX_CFG) do
        if FunOpen.Instance:GetFunIsOpened(v.FunName) then
            if default_index <= 0 then
                default_index = k
            end

            if RemindManager.Instance:GetRemind(v.RemindName) > 0 then
                return k
            end
        end
    end

	return default_index
end