GuildActivityDaTiRank = GuildActivityDaTiRank or BaseClass(SafeBaseView)

function GuildActivityDaTiRank:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_dati_rank")
	self:SetMaskBg()
end

function GuildActivityDaTiRank:ReleaseCallBack()
    if self.role_list then
        self.role_list:DeleteMe()
        self.role_list = nil
    end
end

function GuildActivityDaTiRank:OpenCallBack()
    GuildAnswerWGCtrl.Instance:SendCSGuildQuestionQueryRank()
end

function GuildActivityDaTiRank:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Guild.RankInfoTitle
    self:SetSecondView(nil, self.node_list["size"])
    self.role_list = AsyncListView.New(GuildDaTiRankItem, self.node_list["role_list"])
end

function GuildActivityDaTiRank:OnFlush()
    local guild_rank_info = GuildAnswerWGData.Instance:GetGuildViewAnswerRank()
    self.role_list:SetDataList(guild_rank_info)
end

--------------------------------------------------------------------------------------
GuildDaTiRankItem = GuildDaTiRankItem or BaseClass(BaseRender)

function GuildDaTiRankItem:__init()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function GuildDaTiRankItem:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function GuildDaTiRankItem:OnFlush()
    self.node_list["bg"]:SetActive(self.index % 2 == 1)
    self.node_list["rank_img"]:CustomSetActive(self.index <= 3)
    if self.index <= 3 then
        local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
        self.node_list["rank_img"].image:SetNativeSize()
        self.node_list["rank_num"].text.text = ""
    else
        self.node_list["rank_num"].text.text = self.data.rank
    end
    self.node_list["name"].text.text = self.data.guild_name
    self.node_list["hurt_text"].text.text = self.data.guild_score
    
    if self.data.reward then
        self.node_list["reward_list"]:SetActive(true)
        local data_list = {}
        table.insert(data_list,self.data.reward)
        self.reward_list:SetDataList(data_list)
    else
        self.node_list["reward_list"]:SetActive(false)
    end
end
