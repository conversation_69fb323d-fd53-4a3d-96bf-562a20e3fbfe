ShiTianSuitWGData = ShiTianSuitWGData or BaseClass()
function ShiTianSuitWGData:__init()
    if ShiTianSuitWGData.Instance ~= nil then
		print_error("[ShiTianSuitWGData] attempt to create singleton twice!")
		return
	end
	ShiTianSuitWGData.Instance = self

    local suit_cfg = ConfigManager.Instance:GetAutoConfig("shitian_suit_cfg_auto")
    self.shitian_equip_map = ListToMap(suit_cfg.suit, "suit_seq", "part")
    self.shitian_equip_map_hole = ListToMap(suit_cfg.suit, "icon_id")
    self.shitian_equip_star_cfg = ListToMap(suit_cfg.star_level, "suit_seq", "part", "star_level")
    self.shitian_equip_reward_cfg = ListToMap(suit_cfg.reward, "suit_seq", "seq")
    self.suit_description_cfg = ListToMap(suit_cfg.suit_description, "suit_seq")
    self.shitian_other_cfg = suit_cfg.other[1]

    self.part_item_list = {}
    self.equip_upstar_stuff_id_list = {}
    for k, v in pairs(suit_cfg.star_level) do
        self.equip_upstar_stuff_id_list[v.cost_item_id] = true
    end

    self.equip_unlock_stuff_id_list = {}
    for k, v in pairs(suit_cfg.suit) do
        self.equip_unlock_stuff_id_list[v.icon_id] = true
        self.equip_unlock_stuff_id_list[v.cost_item_id1] = true
        self.equip_unlock_stuff_id_list[v.cost_item_id2] = true
    end

    RemindManager.Instance:Register(RemindName.ShiTianSuitActive, BindTool.Bind(self.GetShiTianSuitRed, self))
end

function ShiTianSuitWGData:__delete()
    ShiTianSuitWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.ShiTianSuitActive)
end

-- 是否是弑天装备
function ShiTianSuitWGData:IsShiTianEquip(change_item_id)
	return self.shitian_equip_map_hole[change_item_id] ~= nil
end

function ShiTianSuitWGData:IsShiTianChangeItem(change_item_id)
    return self.equip_unlock_stuff_id_list[change_item_id] ~= nil
end

function ShiTianSuitWGData:IsUpStarStuffID(item_id)
	return self.equip_upstar_stuff_id_list[item_id] ~= nil
end

--所有套装信息
function ShiTianSuitWGData:SetSuitAllInfo(protocol)
	self.part_item_list = protocol.part_item_list
end

--单个套装更新
function ShiTianSuitWGData:UpdateSuitInfo(protocol)
    if self.part_item_list[protocol.suit_seq] then
	    self.part_item_list[protocol.suit_seq] = protocol.part_item
    end
end

--获取类型配置
function ShiTianSuitWGData:GetSuitTypeCfg()
    local show_list = {}
    local cfg = self.suit_description_cfg
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if not IsEmptyTable(cfg) then
         for i = 0,#cfg do
			if server_day >= cfg[i].open_day then
				table.insert(show_list, i, cfg[i])
			end
		end
	end

	return show_list
end

function ShiTianSuitWGData:GetSuitTypeCfgBySeq(suit_seq)
	return self.suit_description_cfg[suit_seq]
end

function ShiTianSuitWGData:GetSuitTypeCfgLength()
	return #self.suit_description_cfg
end

--根据孔位 获取部位信息
function ShiTianSuitWGData:GetShiTianHole(suit_seq, hole)
    return (self.shitian_equip_map[suit_seq] or {})[hole]
end

-- 根据套装获取孔位list
function ShiTianSuitWGData:GetShiTianHoleList(suit_seq)
	return self.shitian_equip_map[suit_seq]
end

-- 获取部位信息
function ShiTianSuitWGData:GetHoleInfo(suit_seq, hole)
	return ((self.part_item_list[suit_seq] or {})["hole_item_list"] or {})[hole]
end

-- 获取套装信息
function ShiTianSuitWGData:GetSuitPartInfo(suit_seq)
	return self.part_item_list[suit_seq]
end

--星级等级配置
function ShiTianSuitWGData:GetEquipStarCfg(suit_seq, hole, star_level)
	return ((self.shitian_equip_star_cfg[suit_seq] or {})[hole] or {})[star_level]
end

--部位星级
function ShiTianSuitWGData:GetHoleStarLevel(suit_seq, hole)
    local hole_info = self:GetHoleInfo(suit_seq, hole)
    return hole_info and hole_info.star_level or 0
end

-- 获取最大星级
function ShiTianSuitWGData:GetMaxStar(suit_seq, hole)
	local star_level_cfg = (self.shitian_equip_star_cfg[suit_seq] or {})[hole] or {}
	local data = star_level_cfg[#star_level_cfg]
	return data and data.star_level or 0
end

--部位索引
function ShiTianSuitWGData:GetShiTIanEquipHole(item_id)
	local data = self.shitian_equip_map_hole[item_id]  
	return data
end

--奖励配置
function ShiTianSuitWGData:GetSuitRewradCfg(suit_seq)
    return self.shitian_equip_reward_cfg[suit_seq]
end
-- 获取套装列表
function ShiTianSuitWGData:GetSuitHoleList(suit_seq)
	local hole_list = {}
	local cfg_hole_list = self:GetShiTianHoleList(suit_seq)
	for hole, cfg in pairs(cfg_hole_list) do
        local hole_info = self:GetHoleInfo(suit_seq, hole)
        if hole_info then
            local data = {
                suit = cfg.suit_seq,
                hole = cfg.part,
                item_id = cfg.icon_id,
                star_level = hole_info.star_level or 0,
                is_unlock = hole_info.is_unlock or 0,
            }
            hole_list[hole] = data
        end
	end

	return hole_list
end

--装备激活数量
function ShiTianSuitWGData:GetSuitActNum(suit_seq)
    local suit_list = self:GetSuitHoleList(suit_seq)
    local nor_num, sp_num = 0, 0
    if suit_list == nil then
        return nor_num, sp_num
    end

    for k, v in pairs(suit_list) do
        local hole_cfg = self:GetShiTianHole(v.suit, v.hole)
        if hole_cfg then
            if hole_cfg.is_normal_part < 1 and v.is_unlock > 0 then
                sp_num = sp_num + 1
            elseif v.is_unlock > 0 then
                nor_num = nor_num + 1
            end
        end
    end
    
    return nor_num, sp_num
end

-- 获取部位是否激活
function ShiTianSuitWGData:GetHoleIsAct(suit_seq, hole)
	local info = self:GetHoleInfo(suit_seq, hole)
	return info and info.is_unlock > 0
end

--特殊槽位 是否能解锁
function ShiTianSuitWGData:GetSpHoleIsLock(suit_seq, hole)
    local hole_cfg = self:GetShiTianHole(suit_seq, hole)
    if hole_cfg == nil then
        return false
    end
    
    local nor_act_num = self:GetSuitActNum(suit_seq)
    local task2_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id1)
    local task3_num = ItemWGData.Instance:GetItemNumInBagById(hole_cfg.cost_item_id2)
    if nor_act_num >= hole_cfg.need_normal_part_count and
    task2_num >= hole_cfg.cost_item_num1 and
    task3_num >= hole_cfg.cost_item_num2 then
        return true
    end

    return false
end

--装备属性
function ShiTianSuitWGData:GetShiTianEquipStarAttrList(item_id, star_level)
	local nor_attr_list = {}
	local xp_attr_list = {}
	local hole_cfg = self:GetShiTIanEquipHole(item_id)
	local cur_star_cfg = self:GetEquipStarCfg(hole_cfg.suit_seq, hole_cfg.part, star_level)
    local next_star_cfg = self:GetEquipStarCfg(hole_cfg.suit_seq, hole_cfg.part, star_level + 1)

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local lock_attr_id, lock_attr_value = 0, 0
	local max_attr_num = 7

	if cur_star_cfg == nil or hole_cfg == nil then
        return nor_attr_list, xp_attr_list
    end
    
    local is_act = self:GetHoleIsAct(hole_cfg.suit_seq, hole_cfg.part)

	for i = 1, max_attr_num do
        lock_attr_id = hole_cfg["attr_id" .. i]
        lock_attr_value = hole_cfg["attr_value" .. i]
        
        attr_id = cur_star_cfg["attr_id" .. i]
        attr_value = cur_star_cfg["attr_value" .. i]
        if attr_id and attr_value then
            local attr_str = i < 3 and em_data:GetAttrStrByAttrId(attr_id) or em_data:GetAttrStrByAttrId(lock_attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }   

            if i < 3 then --基础属性
                data.attr_value = is_act and data.attr_value + lock_attr_value or lock_attr_value
            else --仙品属性
                data.attr_value = lock_attr_value
            end

            if next_star_cfg ~= nil then
                data.add_value = is_act and next_star_cfg["attr_value" .. i] - data.attr_value + lock_attr_value  or 0
            else
                data.add_value = 0
            end
            

            if i < 3 then
                table.insert(nor_attr_list, data)
            else
                data.add_value = 0
                table.insert(xp_attr_list, data)
            end
        end
    end

	if not IsEmptyTable(nor_attr_list) then
        table.sort(nor_attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end
    
    if not IsEmptyTable(xp_attr_list) then
        table.sort(xp_attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return nor_attr_list, xp_attr_list
end

function ShiTianSuitWGData:GetSuitRewardList(suit_seq)
    local info = self:GetSuitPartInfo(suit_seq)
    local reward_seq = info and info.reward_seq or 0
    local cur_reawrd_cfg = self:GetSuitRewradCfg(suit_seq)
    if reward_seq < 0 then
        return cur_reawrd_cfg[0]
    end

    if reward_seq >= #cur_reawrd_cfg then
        return cur_reawrd_cfg[#cur_reawrd_cfg]
    end

    --显示下一索引的奖励
    return cur_reawrd_cfg[reward_seq + 1]
end

--模型展示列表
function ShiTianSuitWGData:GetRewardModelItemList(suit_seq)
    local info = self:GetSuitPartInfo(suit_seq)
    local reward_seq = info and info.reward_seq or 0
    local cur_reawrd_cfg = self:GetSuitRewradCfg(suit_seq)
    local model_item_list = {}

    if reward_seq >= #cur_reawrd_cfg then
        reward_seq = #cur_reawrd_cfg
    else
        reward_seq = reward_seq + 1
    end

    for i = 0, reward_seq do
        local item_cfg = cur_reawrd_cfg[i].reward_item[0]
        if item_cfg then
            model_item_list[item_cfg.item_id] = true
        end
    end

    return model_item_list
end

--所有外观展示
function ShiTianSuitWGData:GetRewardModelAllItemList(suit_seq)
    local info = self:GetSuitPartInfo(suit_seq)
    local cur_reawrd_cfg = self:GetSuitRewradCfg(suit_seq)
    local item_list = {}
    if not IsEmptyTable(cur_reawrd_cfg) then
        for i = 0, #cur_reawrd_cfg do
            local item_cfg = cur_reawrd_cfg[i].reward_item[0]
            if item_cfg then
                item_list[item_cfg.item_id] = true
            end
        end
    end

    return item_list
end

-----------------------红点---------------
function ShiTianSuitWGData:GetShiTianSuitRed()
    local show_info = self:GetSuitTypeCfg()
    for k, v in pairs(show_info) do
        if self:GetSuitRemind(v.suit_seq) then
            return 1
        end
    end

    return 0
end

--套装红点
function ShiTianSuitWGData:GetSuitRemind(suit_seq)
    local suit_list = self:GetShiTianHoleList(suit_seq)
    for hole = 0, #suit_list do
		local data = suit_list[hole]
		if data and self:GetHoleUpStarRemind(suit_seq, hole) then
			return true
		end
	end

    if self:GetRewardIsAct(suit_seq) then
        return true
    end

    return false
end


function ShiTianSuitWGData:GetHoleUpStarRemind(suit_seq, hole)
    local hole_info = self:GetHoleInfo(suit_seq, hole)
    local hole_cfg = self:GetShiTianHole(suit_seq, hole)
    if not hole_info or not hole_cfg then
        return false
    end

    if hole_cfg.is_normal_part < 1 and hole_info.is_unlock < 1 then
        local is_lock = self:GetSpHoleIsLock(suit_seq, hole)
        if not is_lock then
            return false
        end
    end

    local max_star = self:GetMaxStar(suit_seq, hole)
    local cur_star = hole_info.star_level
    if cur_star >= max_star then
        return false
    end

    local stuff_item_id, stuff_item_num
    if hole_info.is_unlock < 1 then
        stuff_item_id = hole_cfg.cost_item_id1
        stuff_item_num = hole_cfg.cost_item_num1
        local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_item_id)
        return has_num >= stuff_item_num
    else
        local star_cfg = self:GetEquipStarCfg(suit_seq, hole, cur_star)
        if star_cfg then
            stuff_item_id = star_cfg.cost_item_id
            stuff_item_num = star_cfg.cost_item_num
            local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_item_id)
            return has_num >= stuff_item_num
        end
    end

    return false
end

--奖励是否能激活
function ShiTianSuitWGData:GetRewardIsAct(suit_seq)
    local reward_list = self:GetSuitRewardList(suit_seq)
    local nor_act_num = self:GetSuitActNum(suit_seq)
    local info = self:GetSuitPartInfo(suit_seq)
    local reward_seq = info and info.reward_seq or 0
    local cur_reawrd_cfg = self:GetSuitRewradCfg(suit_seq)
    local is_get_all_reward = reward_seq >= #cur_reawrd_cfg
    if nor_act_num >= reward_list.need_normal_part_count and not is_get_all_reward then
        return true
    end

    return false
end