TeamBootyBayFBSceneLogic = TeamBootyBayFBSceneLogic or BaseClass(CommonFbLogic)

function TeamBootyBayFBSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function TeamBootyBayFBSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
    GlobalEventSystem:UnBind(self.role_pos_change)
    GlobalEventSystem:UnBind(self.obj_die_event)
end

function TeamBootyBayFBSceneLogic:Enter(old_scene_type, new_scene_type)
    self.is_first = true
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
    if not guaji_state then
      GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)  --挂机
    end

    local ctrl = MainuiWGCtrl.Instance
    ctrl:AddInitCallBack(nil, function()
        ctrl:PlayTopButtonTween(false)
        ctrl:SetMianUITopButtonStateTwo(true)
        ctrl.view:SetBtnLevel(true)
        GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
        ctrl:SetFBNameState(true, Language.BootyBay.TeamLevelName)
		ctrl:SetTeamBtnState(false)

        --ctrl:SetTaskActive(true)    --任务栏
        ctrl:SetTaskContents(false)
        ctrl:SetOtherContents(true)

    end)

	BootyBayWGCtrl.Instance:CloseBootybayFollowView()
	BootyBayWGCtrl.Instance:OpenTeamBootyBayFBView()
    ViewManager.Instance:Close(GuideModuleName.BootyBayFBReadyView)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BOOTYBAY_READY_VIEW, 0)
    MainuiWGCtrl.Instance:SetShrinkButtonIsOn(false)
    self:SetBlockPointOne()
    self:SetBlockPointTwo()
    self:SetTriggerRang()

    self.role_pos_change = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.PlayerPosChange, self))
    self.obj_die_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDead, self))
end

function TeamBootyBayFBSceneLogic:Out()
    CommonFbLogic.Out(self)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    BootyBayWGCtrl.Instance:CloseTeamBootyBayFBView()
    MainuiWGCtrl.Instance:PlayTopButtonTween(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    BootyBayWGData.Instance:SetWaBaoType(0)
    GlobalEventSystem:UnBind(self.role_pos_change)
    GlobalEventSystem:UnBind(self.obj_die_event)

    --切换成无目标
    NewTeamWGCtrl.Instance:ChangNotGoalWhenOutFromOtherFb()

    if self.block_point_list then
        for k,v in pairs(self.block_point_list) do
           AStarFindWay:RevertBlockInfo(v.x,v.y)
        end
    end
    self.block_point_lis = nil

    if self.block_point_list2 then
        for k,v in pairs(self.block_point_list2) do
           AStarFindWay:RevertBlockInfo(v.x,v.y)
        end
    end

    self.block_point_list2 = nil

    self.setting = nil

end

function TeamBootyBayFBSceneLogic:SetBlockPointOne()
    local cfg = BootyBayWGData.Instance:GetOtherConfig()
    --阻碍点
    local block_point_list = Split(cfg.block_point,"|")
    self.block_point_list = {}
    for k,v in pairs(block_point_list) do
       local list = {}
       local temp_list = Split(v,",")
       list.x = temp_list[1]
       list.y = temp_list[2]
       table.insert(self.block_point_list,list)
    end
end

function TeamBootyBayFBSceneLogic:SetBlockPointTwo()
    local cfg = BootyBayWGData.Instance:GetOtherConfig()
    --阻碍点2
    local block_point_list2 = Split(cfg.monster_block,"|")
    self.block_point_list2 = {}
    for k,v in pairs(block_point_list2) do
       local list = {}
       local temp_list = Split(v,",")
       list.x = temp_list[1]
       list.y = temp_list[2]
       table.insert(self.block_point_list2,list)
    end
end

function TeamBootyBayFBSceneLogic:SetTriggerRang()
    local cfg = BootyBayWGData.Instance:GetOtherConfig()

    --触发范围
    local trigger_range = Split(cfg.block_fanwei,"|")
    self.trigger_range_list = {}

    for k,v in pairs(trigger_range) do
       local list = {}
       local temp_list = Split(v,",")
       list.x = temp_list[1]
       list.y = temp_list[2]
       table.insert(self.trigger_range_list,list)
    end
end

function TeamBootyBayFBSceneLogic:CanGetMoveObj()
    return true
end


function TeamBootyBayFBSceneLogic:PlayerPosChange(x,y)
    if not self.block_point_list then
        self:SetBlockPointOne()
    end

    if not self.trigger_range_list then
        self:SetTriggerRang()
    end

    if not self.block_point_list or not self.trigger_range_list then return end

    --如果没设置过，并且进入范围内
    if not self.setting and GameMath.IsInPolygon(self.trigger_range_list, {x=x,y=y}) then
        self.setting = true
        for k,v in pairs(self.block_point_list) do
           AStarFindWay:SetBlockInfo(v.x,v.y)
        end
    end

end

function TeamBootyBayFBSceneLogic:CreateCustomBlockPoint()
    if not self.block_point_list2 then
        self:SetBlockPointTwo()
    end

    if not self.block_point_list2 then return end

    local kill_monster = BootyBayWGData.Instance:GetTeamCurMonsterCount()
    --策划说写死数量就好
    if kill_monster >= 32 then
        for k,v in pairs(self.block_point_list2) do
           AStarFindWay:RevertBlockInfo(v.x,v.y)
        end
    else
        for k,v in pairs(self.block_point_list2) do
           AStarFindWay:SetBlockInfo(v.x,v.y)
        end
    end
end

function TeamBootyBayFBSceneLogic:IsShowJumpPoint()
    --策划说写死数量就好
    local kill_monster = BootyBayWGData.Instance:GetTeamCurMonsterCount()
    return kill_monster >= 32
end

function TeamBootyBayFBSceneLogic:OnObjDead(obj_dead)
    -- if obj_dead:GetType() == SceneObjType.Monster then
    --   Scene.Instance:CheckClientObj()
    -- end
end
