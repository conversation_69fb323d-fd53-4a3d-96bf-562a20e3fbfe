CapabilityWelfareView = CapabilityWelfareView or BaseClass(SafeBaseView)

local COUNT_DOWN_KEY = "dragon_trial_count_down"

function CapabilityWelfareView:__init()
	self:SetMaskBg()
	self:SetMaskBgAlpha(230 / 255)
	self.view_name = GuideModuleName.CapabilityWelfare

	self:AddViewResource(0, "uis/view/capability_welfare_ui_prefab", "layout_capability_welfare")
end

function CapabilityWelfareView:__delete()

end

function CapabilityWelfareView:LoadCallBack()
	if not self.day_select_list then
		self.day_select_list = AsyncListView.New(WelfareDaySelectItemRender, self.node_list.day_select_item_list)
		self.day_select_list:SetSelectCallBack(BindTool.Bind(self.OnSelectDayCallBack, self))
		self.day_select_list.IsLimitSelectByIndex = function(list, cell_index)
			local all_task_info = CapabilityWelfareWGData.Instance:GetAllTaskInfo()
			local data = all_task_info[cell_index]
			if data.is_show_open_tip then
				local jump_view = CapabilityWelfareWGData.Instance:GetJumpView()
				FunOpen.Instance:OpenViewNameByCfg(jump_view)
				return true
			end
			if not data.is_open then
				local str = string.format(Language.CapabilityWelfare.DayClickTip,
					Language.CapabilityWelfare.DayStr[cell_index])
					SysMsgWGCtrl.Instance:ErrorRemind(str)
				return true
			end
			return false
		end
	end

	if not self.task_list_view then
		self.task_list_view = AsyncListView.New(CapabilityWelfareTaskItemRender, self.node_list.task_list_view)
	end

	if not self.fanal_task_item then
		self.fanal_task_item = CapabilityWelfareTaskItemRender.New(self.node_list.final_task_item)
	end

	--[[
	if not self.final_reward_list then
		self.final_reward_list = AsyncListView.New(WelfareRewardItemRender, self.node_list.final_reward_list)
		self.final_reward_list:SetStartZeroIndex(true)
	end
	]]

    XUI.AddClickEventListener(self.node_list.btn_get_final_reward, BindTool.Bind(self.OnClickFinalRewardBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_jump_view, BindTool.Bind(self.OnClickJumpBtn, self))
	XUI.AddClickEventListener(self.node_list.esoterica_btn, BindTool.Bind(self.OnClickEsotercaImg, self))

	local model_cfg = CapabilityWelfareWGData.Instance:GetModelCfg()
	-- 模型
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_root"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		self:FlushModel(model_cfg)
	end
end

function CapabilityWelfareView:ReleaseCallBack()
	if self.task_list_view then
		self.task_list_view:DeleteMe()
		self.task_list_view = nil
	end

	if self.day_select_list then
		self.day_select_list:DeleteMe()
		self.day_select_list = nil
	end

	if self.fanal_task_item then
		self.fanal_task_item:DeleteMe()
		self.fanal_task_item = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	--[[
	if self.final_reward_list then
		self.final_reward_list:DeleteMe()
		self.final_reward_list = nil
	end
	]]

	self.all_task_info = nil
	self.cur_select_index = nil
end

function CapabilityWelfareView:LoadIndexCallBack(index)
end

function CapabilityWelfareView:OnFlush(param_t, index)
	local all_task_info = CapabilityWelfareWGData.Instance:GetAllTaskInfo()
	if IsEmptyTable(all_task_info) then
		return
	end

	self.all_task_info = all_task_info

	local jump_index = self.cur_select_index or CapabilityWelfareWGData.Instance:GetJumpDay()
	if self.day_select_list then
		self.day_select_list:SetDataList(all_task_info)
		self.day_select_list:JumpToIndex(jump_index)
	end
	--[[
	local final_reward_item = CapabilityWelfareWGData.Instance:GetFinalReward()
	self.final_reward_list:SetDataList(final_reward_item)

	local can_get_final_reward, _ = CapabilityWelfareWGData.Instance:GetFetchFinalRewardState()
	self.node_list["btn_get_final_reward"]:CustomSetActive(can_get_final_reward)
	]]--
end

function CapabilityWelfareView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg) then
		return
	end

	self.node_list.model_root:CustomSetActive(model_cfg.res_type == 0)
	self.node_list.esoterica_root:CustomSetActive(model_cfg.res_type == 1)

	if model_cfg.res_type == 0 then
		self:FlushModelData(model_cfg)
	elseif model_cfg.res_type == 1 then
		self:FlushEsoterica(model_cfg)
	end
end

function CapabilityWelfareView:FlushEsoterica(model_cfg)
	local slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(model_cfg.seq)
    if not slot_cfg then
        return
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. slot_cfg.img_id)
    self.node_list.esoterica_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["esoterica_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_xf_wb" .. slot_cfg.skill_img)
    self.node_list.esoterica_skill_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["esoterica_skill_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetA2Effect(slot_cfg.ui_effect_asset)
    self.node_list.esoterica_effect:ChangeAsset(bundle, asset)
end

function CapabilityWelfareView:FlushModelData(model_cfg)
	local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	-- display_data.model_click_func = function ()
	-- 	TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	-- end
	if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if model_cfg.model_rot and model_cfg.model_rot ~= "" then
		local rot_list = string.split(model_cfg.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		--display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end
	display_data.model_rt_type = ModelRTSCaleType.L
	self.model_display:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.whole_display_pos and model_cfg.whole_display_pos ~= "" then
		local pos_list = string.split(model_cfg.whole_display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, pos_x, pos_y)
	end
	
	local rot_x, rot_y, rot_z = 0, 0, 0
	if model_cfg.whole_display_rot and model_cfg.whole_display_rot ~= "" then
		local rot_list = string.split(model_cfg.whole_display_rot, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
		self.node_list.model_root.transform.localRotation = Quaternion.Euler(rot_x, rot_y, rot_z)
	end

    if model_cfg.whole_display_scale and model_cfg.whole_display_scale ~= "" then
        local scale = tonumber(model_cfg.whole_display_scale)
        Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, scale, scale, scale)
    end
end

function CapabilityWelfareView:OnSelectDayCallBack(cell)
	self.cur_select_index = cell.index
	self:FlushTaskList()
end

function CapabilityWelfareView:FlushTaskList()
	local day_info = self.all_task_info[self.cur_select_index]
	if not day_info then
		return
	end

	self.task_list_view:SetDataList(day_info.task_list)
	self.fanal_task_item:SetData(day_info.final_task)
end

function CapabilityWelfareView:OnClickFinalRewardBtn()
	CapabilityWelfareWGCtrl.Instance:GetFinalRewardReq()
end

function CapabilityWelfareView:OnClickJumpBtn()
	local jump_view_new = CapabilityWelfareWGData.Instance:GetJumpViewNew()
	FunOpen.Instance:OpenViewNameByCfg(jump_view_new)
end

function CapabilityWelfareView:OnClickEsotercaImg()
	local model_cfg = CapabilityWelfareWGData.Instance:GetModelCfg()

	if not IsEmptyTable(model_cfg) then
		if model_cfg.res_type == 1 and model_cfg.item_id and "" ~= model_cfg.item_id then
			TipWGCtrl.Instance:OpenItem({item_id = model_cfg.item_id})
		end
	end
end
--------------------------------
-- 战力任务Render
--------------------------------
CapabilityWelfareTaskItemRender = CapabilityWelfareTaskItemRender or BaseClass(BaseRender)

function CapabilityWelfareTaskItemRender:LoadCallBack()
	if not self.show_reward_list then
		self.show_reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.show_reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list["btn_go_improve"], BindTool.Bind(self.OnClickGoImproveBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_get_reward"], BindTool.Bind(self.OnClickGetRewardBtn, self))
end

function CapabilityWelfareTaskItemRender:ReleaseCallBack()
	if self.show_reward_list then
		self.show_reward_list:DeleteMe()
		self.show_reward_list = nil
	end
end

function CapabilityWelfareTaskItemRender:OnFlush()
	if not self.data then
		return
	end

	local cap = CapabilityWelfareWGData.Instance:GetTotalCapability()
	local cap_str = CommonDataManager.ConverExpExtend(cap)
	local need_cap = self.data.cfg.need_capability
	local need_cap_str = CommonDataManager.ConverExpExtend(need_cap)
	local is_enough = cap >= need_cap

	local color
	if self.data.is_final_task then
		color = is_enough and COLOR3B.D_GREEN or "#FFE094"
	else
		color = is_enough and "#3C8652" or "#A93C3C"
	end
	self.node_list["txt_task_des"].text.text = ToColorStr(
		string.format(Language.CapabilityWelfare.TaskDes, cap_str, need_cap_str), color)

	self.show_reward_list:SetDataList(self.data.cfg.reward_item)

	local had_fetch = self.data.fetch_flag == 1
	self.node_list["btn_get_reward"]:CustomSetActive(self.data.can_fetch)
	self.node_list["btn_go_improve"]:CustomSetActive(not self.data.can_fetch and not had_fetch)
	self.node_list["had_get_flag"]:CustomSetActive(had_fetch)
end

function CapabilityWelfareTaskItemRender:OnClickGoImproveBtn()
	local btn_rect = self.node_list.btn_go_improve.rect
	local screen_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, btn_rect.position)
	MainuiWGCtrl.Instance:SetDataAndOpenStrongMenu({ s_pos = screen_pos, offset_x = -170 })
end

function CapabilityWelfareTaskItemRender:OnClickGetRewardBtn()
	CapabilityWelfareWGCtrl.Instance:GetRewardReq(self.data.seq)
end

--------------------------------
-- 天数ItemRender
--------------------------------
WelfareDaySelectItemRender = WelfareDaySelectItemRender or BaseClass(BaseRender)

function WelfareDaySelectItemRender:LoadCallBack()
end

function WelfareDaySelectItemRender:ReleaseCallBack()
end

function WelfareDaySelectItemRender:OnFlush()
	if not self.data then
		return
	end

	local str = Language.CapabilityWelfare.DayStr[self.index]
	self.node_list["txt_day"].text.text = str
	self.node_list["txt_day_hl"].text.text = str

	local is_red = CapabilityWelfareWGData.Instance:GetDayIsRed(self.data.open_day)
	self.node_list["red_point"]:CustomSetActive(is_red)
	self.node_list["lock_img"]:CustomSetActive(not self.data.is_open)
end

function WelfareDaySelectItemRender:OnSelectChange(is_select)
	self.node_list.hl_image:SetActive(is_select)
	self.node_list.normal_img:SetActive(not is_select)
end

--------------------------------
--
--------------------------------
WelfareRewardItemRender = WelfareRewardItemRender or BaseClass(BaseRender)

function WelfareRewardItemRender:LoadCallBack()
	if not self.show_item then
		self.show_item = ItemCell.New(self.node_list.item_cell_root)
	end
end

function WelfareRewardItemRender:ReleaseCallBack()
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function WelfareRewardItemRender:OnFlush()
	if not self.data then
		return
	end
	self.show_item:SetData(self.data)
	local can_fetch, fetch_flag = CapabilityWelfareWGData.Instance:GetFetchFinalRewardState()
	self.node_list.has_fetch_flag:CustomSetActive(fetch_flag == 1)
	self.node_list.red_point:CustomSetActive(can_fetch)
end
