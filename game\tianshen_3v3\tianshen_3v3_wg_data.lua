TianShen3v3WGData = TianShen3v3WGData or BaseClass()

TS3V3_SIDE = {
	BLUE = 0,
	RED = 1,
}

TS3V3_SIDE_MAX = 1

TS3V3_BUFF_TYPE = {
	HP = 0,  					-- 回血
	ADD_SCORE = 4, 				-- 增加积分
	SUB_SCORE = 5, 	 			-- 减少积分
}

function TianShen3v3WGData:__init()
	if TianShen3v3WGData.Instance ~= nil then
		ErrorLog("[TianShen3v3WGData] attempt to create singleton twice!")
		return
	end
	TianShen3v3WGData.Instance = self
	self:InitProtocolInfo()
	self:InitCfg()
	RemindManager.Instance:Register(RemindName.TianShen3v3JoinRewardRemind, BindTool.Bind(self.GetTianShen3v3JoinRewardRemind, self))
	RemindManager.Instance:Register(RemindName.TianShen3v3BtnRemind, BindTool.Bind(self.GetTianShen3v3BtnRemind, self))
end

function TianShen3v3WGData:__delete()
	TianShen3v3WGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.TianShen3v3JoinRewardRemind)
	RemindManager.Instance:UnRegister(RemindName.TianShen3v3BtnRemind)
end

------------------------------------ 协议数据存取 -----------------------------------
-- 初始化协议数据
function TianShen3v3WGData:InitProtocolInfo()
	self.matching = false 									-- 是否匹配中
	self.start_match_timestamp = 0 							-- 开始匹配时间戳

	self.my_side = 0 										-- 我的阵营

	-- 匹配成功的角色信息
	self.side_info_list = {}
	for k, side in pairs(TS3V3_SIDE) do
    	self.side_info_list[side] = {}
    end

    self.relive_timestamp = 0 								-- 复活时间
    self.killer_name = "" 									-- 杀手名称

	self.get_score_times = 0								-- 获得积分的场次
	self.join_times = 0 									-- 本次活动参与场次
	self.grade_score = 0									-- 主角赛季积分
	self.join_reward_flag = bit:d2b_two(0) 					-- 参与奖励标记
	self.win_times = 0										-- 胜场
	self.select_tianshen_index = 0							-- 选择的天神index

	self.scene_role_info_list = {}							-- 场景角色实时信息
	self.scene_role_info_dic = {} 							-- 场景角色实时信息,key是uuid_str

	self.map_role_info_list = {}							-- 小地图角色信息
	self.map_role_info_dic = {} 							-- 小地图角色信息，key是uuid_str

	self.buff_info_list = {} 								-- buff列表

	self.season_rank_info_list = {} 						-- 赛季排名信息列表
	self.my_season_rank = -1 								-- 我的赛季排名
end

-- 设置匹配信息
function TianShen3v3WGData:SetMatchStateInfo(protocol)
	self.matching = protocol.matching
	self.start_match_timestamp = protocol.start_match_timestamp
end

-- 是否匹配中
function TianShen3v3WGData:GetIsMatching()
	return self.matching
end

-- 获取已匹配时长
function TianShen3v3WGData:GetMatchDuration()
	if self.start_match_timestamp > 0 then
		return math.max(0, math.floor(TimeWGCtrl.Instance:GetServerTime() - self.start_match_timestamp)) 
	else
		return -1
	end
end

-- 设置匹配成功角色信息
function TianShen3v3WGData:SetMatchReadyInfo(protocol)
	self.my_side = protocol.my_side
	self.side_info_list = protocol.side_info_list
end

-- 获得我所在的阵营
function TianShen3v3WGData:GetMySide()
	return self.my_side
end

-- 获得敌对的阵营
function TianShen3v3WGData:GetOtherSide()
	for k, side in pairs(TS3V3_SIDE) do
		if side ~= self:GetMySide() then
			return side
		end
	end
end

-- 获得所给阵营的敌对阵营
function TianShen3v3WGData:GetEnemySide(side)
	for k, v in pairs(TS3V3_SIDE) do
		if v ~= side then
			return v
		end
	end
end

-- 得到阵营角色信息
function TianShen3v3WGData:GetSideInfoList()
	return self.side_info_list
end

-- 设置主角复活信息
function TianShen3v3WGData:SetMainRoleReliveInfo(protocol)
	self.relive_timestamp = protocol.relive_timestamp 							-- 复活时间戳
	self.killer_name = protocol.killer_name										-- 杀手名称
end

-- 获得主角复活时间
function TianShen3v3WGData:GetReliveTimestamp()
	return self.relive_timestamp
end

-- 获得杀手名称
function TianShen3v3WGData:GetKillerName()
	return self.killer_name
end

-- 设置主角信息
function TianShen3v3WGData:SetTianShen3v3MainRoleInfo(protocol)
	self.get_score_times = protocol.get_score_times								-- 已获得积分的场次
	self.join_times = protocol.join_times 										-- 本次活动参与场次
	self.grade_score = protocol.grade_score 									-- 赛季段位积分
	self.join_reward_flag = protocol.join_reward_flag 							-- 参与奖励标记
	self.win_times = protocol.win_times 										-- 胜场
	self.select_tianshen_index = protocol.tianshen_index						-- 选择的天神index
end

-- 获得剩余可获得积分的场次
function TianShen3v3WGData:GetSurplusCanGetScoreTimes()
	local can_get_score_times = self:GetOtherCfg().get_score_times
	return can_get_score_times - self.get_score_times
end

-- 设置是否需要打开积分场次用完的提醒面板
function TianShen3v3WGData:SetNeedOpenCanGetScoreTipsFlag(need_open)
	self.can_get_score_tips_need_open = need_open
end

-- 获得是否需要打开积分场次用完的提醒面板
function TianShen3v3WGData:GetNeedOpenCanGetScoreTipsFlag()
	return self.can_get_score_tips_need_open
end

-- 获得主角赛季积分
function TianShen3v3WGData:GetSeasonScore()
	return self.grade_score
end

-- 获得主角参与场次
function TianShen3v3WGData:GetJoinTimes()
	return self.join_times
end

-- 获取本赛季胜场场次
function TianShen3v3WGData:GetWinTimes()
	return self.win_times
end

-- 获取当前3v3出战中的天神index
function TianShen3v3WGData:GetSelectTianShenIndex()
	return self.select_tianshen_index
end

-- 判断对应seq的参与奖励是否已经领取
function TianShen3v3WGData:GetJoinRewardFetched(seq)
	return self.join_reward_flag[seq] == 1
end

-- 根据对应参与奖励seq是否可领取
function TianShen3v3WGData:GetJoinRewarCanFetch(join_reward_seq)
	local join_reward_cfg = self:GetJoinRewardCfgBySeq(join_reward_seq)
	return self:GetJoinTimes() >= join_reward_cfg.match_times and not self:GetJoinRewardFetched(join_reward_seq)
end

-- 获得第一个未领取奖励的参与奖励seq
function TianShen3v3WGData:GetFirstUnreceivedJoinRewardSeq()
	for seq = 0, #self.join_reward_flag do
		if not self:GetJoinRewardFetched(seq) then
			return seq
		end
	end
	return -1
end

-- 设置场景角色实时信息
function TianShen3v3WGData:SetSceneRoleInfoList(protocol)
	self.scene_role_info_list = protocol.scene_role_info_list
	self.scene_role_info_dic = protocol.scene_role_info_dic
end

-- 获得场景角色实时信息
function TianShen3v3WGData:GetSceneRoleInfoList()
	return self.scene_role_info_list
end

-- 获得场景角色实时信息
function TianShen3v3WGData:GetSceneRoleInfoDic()
	return self.scene_role_info_dic
end

-- 根据uuid_str获得对应场景角色信息
function TianShen3v3WGData:GetSceneRoleInfoByUuidStr(uuid_str)
	return self:GetSceneRoleInfoDic()[uuid_str]
end

-- 设置战斗场景实时信息
function TianShen3v3WGData:SetSceneInfo(protocol)
	self.scene_info = protocol
end

-- 获得战斗场景实时信息
function TianShen3v3WGData:GetSceneInfo()
	return self.scene_info
end

-- 获得占领点信息
function TianShen3v3WGData:GetOccupyPointInfoList()
	if not self:GetSceneInfo() then
		return nil
	end
	return self:GetSceneInfo().occupy_point_list
end

-- 获取对应占领点的占领方
function TianShen3v3WGData:GetOccupyPointSide(index)
	local occupy_point_info = self:GetOccupyPointInfoByIndex(index)
	if occupy_point_info then
		return occupy_point_info.occupy_side
	end
	return -1
end

-- 根据占领点下标获得点位信息
function TianShen3v3WGData:GetOccupyPointInfoByIndex(point_index)
	local point_info_list = self:GetOccupyPointInfoList()
	if point_info_list then
		return point_info_list[point_index]
	end
	return nil
end

-- 获得主角阵营的场景角色实时信息
function TianShen3v3WGData:GetMySideSceneRoleInfoList()
	local my_side = self:GetMySide()
	if self:GetSceneRoleInfoList() then
		return self:GetSceneRoleInfoList()[my_side]
	end
	return nil
end

-- 获得进阶所需的积分
function TianShen3v3WGData:GetAdvanceNeedScore()
	local _, next_grade_cfg = self:GetGradeCfgByScore(self:GetSeasonScore())
	if next_grade_cfg then
		return next_grade_cfg.score - self:GetSeasonScore(), next_grade_cfg.score
	end
	return 0, 0
end

-- 设置小地图角色信息
function TianShen3v3WGData:SetMapInfo(protocol)
	self.map_role_info_list = protocol.map_role_info_list
	self.map_role_info_dic = protocol.map_role_info_dic
end

-- 获取小地图角色信息
function TianShen3v3WGData:GetMapRoleInfoList()
	return self.map_role_info_list
end

-- 获取小地图角色信息
function TianShen3v3WGData:GetMapRoleInfoDic()
	return self.map_role_info_dic
end

-- 新增捡起来的buff数据
function TianShen3v3WGData:AddBuffInfo(protocol)
	local buff_cfg = self:GetBuffCfg(protocol.buff_type)
	if buff_cfg.show_in_buff_list == 1 then
		self.buff_info_list[protocol.buff_type] = {buff_type = protocol.buff_type, end_timestamp = protocol.end_timestamp}
	end
end

-- 获得buff列表
function TianShen3v3WGData:GetBuffInfoList()
	return self.buff_info_list
end

-- 获得有效的buff列表
function TianShen3v3WGData:GetVaildBuffInfoList()
	local result = {}
	for _, v in pairs(self:GetBuffInfoList()) do
		if v.end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
			table.insert(result, v)
		end
	end
	return result
end

-- 设置赛季排名信息
function TianShen3v3WGData:SetSeasonRankInfo(protocol)
	self.season_rank_info_list = protocol.season_rank_info_list
	self.my_season_rank = protocol.my_season_rank
end

-- 获得赛季排名信息
function TianShen3v3WGData:GetSeasonRankInfoList(protocol)
	return self.season_rank_info_list
end

function TianShen3v3WGData:GetMySeasonRank()
	return self.my_season_rank
end
----------------------------------- 协议数据存取End ---------------------------------

------------------------------------ 配置数据存取 -----------------------------------
-- 初始化配置表
function TianShen3v3WGData:InitCfg()
	self.rule_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").rule, "rule_perent_index") 			-- 规则配置
	self.grade_reward_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").season_reward, "grade") 			-- 赛季段位奖励配置
	self.join_reward_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").join_reward, "seq") 				-- 参与奖励
	self.chuanwen_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").kill_news, "type", "kill_count") 	-- 连杀传闻配置
	self.buff_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").buff, "buff_type") 						-- buff配置
end

-- 获取规则配置
function TianShen3v3WGData:GetRuleCfg()
	return self.rule_cfg
end

-- 根据index获得规则配置
function TianShen3v3WGData:GetRuleByIndexCfg(perent_index)
	return self:GetRuleCfg()[perent_index]
end

-- 获得赛季段位奖励
function TianShen3v3WGData:GetGradeRewardCfg()
	return self.grade_reward_cfg
end

-- 获得赛季排名奖励
function TianShen3v3WGData:GetRankRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").season_rank_reward
end

-- 获得参与奖励配置
function TianShen3v3WGData:GetJoinRewardCfg()
	return self.join_reward_cfg
end

-- 获得最大场次奖励配置
function TianShen3v3WGData:GetMaxJoinRewardCfg()
	if not self.max_join_reward_cfg then
		local max_times = 0
		for k,v in pairs(self:GetJoinRewardCfg()) do
			if max_times < v.match_times then
				self.max_join_reward_cfg = v
				max_times = v.match_times
			end
		end
	end
	return self.max_join_reward_cfg, self.max_join_reward_cfg.match_times
end

-- 根据参与奖励seq获得奖励配置
function TianShen3v3WGData:GetJoinRewardCfgBySeq(join_reward_seq)
	return self:GetJoinRewardCfg()[join_reward_seq]
end

-- 根据参与次数获得参与奖励seq
function TianShen3v3WGData:GetJoinRewardCfgSeqByTimes(times)
	local cfg_list = self:GetJoinRewardCfg()
	for seq = 0, #cfg_list do
		if cfg_list[seq].match_times > times then
			return math.max(0, seq - 1)
		end
	end
	return 0
end

-- 获得其他配置
function TianShen3v3WGData:GetOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").other[1]
end

-- 获得养成度配置
function TianShen3v3WGData:GetCompleteRateCfg()
	return ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").tianshen_cultivate[1]
end

-- 计算天神3v3单个天神战斗力
function TianShen3v3WGData:CalCapability(complete_rate)
	local factor_2 = self:GetCompleteRateCfg().factor_2
	local factor_3 = self:GetCompleteRateCfg().factor_3
	return math.floor(RoleWGData.Instance:GetMainRoleCap() * (factor_2 + factor_3 * complete_rate/100)) 
end

-- 获得天神战场战力
function TianShen3v3WGData:GetCapability(tianshen_index)
	local complete_rate = TianShenWGData.Instance:GetCompleteRate(tianshen_index)
	return self:CalCapability(complete_rate)
end

-- 获得段位配置
function TianShen3v3WGData:GetGradeCfg()
	return ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").grade
end

-- 根据积分获得段位配置
function TianShen3v3WGData:GetGradeCfgByScore(score)
	local grade_cfg_list = self:GetGradeCfg()
	local last_grade_cfg = nil
	for i,v in ipairs(grade_cfg_list) do
		if v.score > score then
			return last_grade_cfg, v
		end
		last_grade_cfg = v
	end
	return nil, nil
end

-- 获得点位配置
function TianShen3v3WGData:GetPointCfg()
	return ConfigManager.Instance:GetAutoConfig("tianshen3v3_auto").occupy_point
end

-- 获取占领点配置
function TianShen3v3WGData:GetPointCfgByIndex(index)
	return self:GetPointCfg()[index]
end

-- 是否在占领点
function TianShen3v3WGData:InPoint(logic_x, logic_y)
	for i,v in ipairs(self:GetPointCfg()) do
		if self:InSinglePoint(logic_x, logic_y, v.index) then
			return true, v
		end
	end
	return false, nil
end

function TianShen3v3WGData:InSinglePoint(logic_x, logic_y, point_index)
	local point_cfg = self:GetPointCfgByIndex(point_index)
	local pos_tab = Split(point_cfg.occupy_pos, ',', true)
	local x = tonumber(pos_tab[1])
	local y = tonumber(pos_tab[2])
	local distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = x, y = y})
	if distance < point_cfg.occupy_radius then
		return true, point_cfg
	end
	return false
end

-- 获取最近的未占领的占领点坐标
function TianShen3v3WGData:FindNearestUnoccupationPointPos()
	local min_distance_cfg = nil
	local min_distance = math.huge
	local min_distance_point_x = nil
	local min_distance_point_y = nil
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local logic_x, logic_y = main_role:GetLogicPos()
		for i,v in ipairs(self:GetPointCfg()) do
			local occupy_side = self:GetOccupyPointSide(v.index)
			if occupy_side ~= self:GetMySide() then
				local pos_tab = Split(v.occupy_pos, ',', true)
				local x = tonumber(pos_tab[1])
				local y = tonumber(pos_tab[2])
				local distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = x, y = y})
				if distance < min_distance then
					min_distance = distance
					min_distance_cfg = v
					min_distance_point_x = x
					min_distance_point_y = y
				end
			end
		end
		return min_distance_point_x, min_distance_point_y, min_distance_cfg, min_distance
	end
	return 0, 0, nil, 0
end

-- 获取最近的抢夺中的占领点坐标
function TianShen3v3WGData:FindNearestOccupyingPointPos()
	local min_distance_cfg = nil
	local min_distance = math.huge
	local min_distance_point_x = nil
	local min_distance_point_y = nil
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local logic_x, logic_y = main_role:GetLogicPos()
		for i,v in ipairs(self:GetPointCfg()) do
			local occupy_info = self:GetOccupyPointInfoByIndex(v.index)
			if occupy_info and occupy_info.occupying then
				local pos_tab = Split(v.occupy_pos, ',', true)
				local x = tonumber(pos_tab[1])
				local y = tonumber(pos_tab[2])
				local distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = x, y = y})
				if distance < min_distance then
					min_distance = distance
					min_distance_cfg = v
					min_distance_point_x = x
					min_distance_point_y = y
				end
			end
		end
		return min_distance_point_x, min_distance_point_y, min_distance_cfg, min_distance
	end
	return 0, 0, nil, 0
end

-- 获取最近的抢夺中的或未占领的占领点坐标
function TianShen3v3WGData:GetNearestOccupyingOrUnoccupationPointPos()
	local main_role = Scene.Instance:GetMainRole()
	local unoccupation_point_x, unoccupation_point_y, unoccupation_cfg = TianShen3v3WGData.Instance:FindNearestUnoccupationPointPos()
	local occupying_point_x, occupying_point_y, occupying_cfg = TianShen3v3WGData.Instance:FindNearestOccupyingPointPos()
	if main_role then
		local logic_x, logic_y = main_role:GetLogicPos()
		if unoccupation_cfg and occupying_cfg then
			local unoccupation_distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = unoccupation_point_x, y = unoccupation_point_y})
			local occupying_distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = occupying_point_x, y = occupying_point_y})
			if unoccupation_distance < occupying_distance then
				return unoccupation_point_x, unoccupation_point_y, unoccupation_cfg, unoccupation_distance
			else
				return occupying_point_x, occupying_point_y, occupying_cfg, occupying_distance
			end
		elseif unoccupation_cfg then
			local unoccupation_distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = unoccupation_point_x, y = unoccupation_point_y})
			return unoccupation_point_x, unoccupation_point_y, unoccupation_cfg, unoccupation_distance
		elseif occupying_cfg then
			local occupying_distance = u3d.v2Distance({x = logic_x, y = logic_y}, {x = occupying_point_x, y = occupying_point_y})
			return occupying_point_x, occupying_point_y, occupying_cfg, occupying_distance
		end
	end
	return 0, 0, nil, 0
end

-- 获取占领点坐标
function TianShen3v3WGData:GetPointPos(index)
	local cfg = self:GetPointCfgByIndex(index)
	local pos_tab = Split(cfg.occupy_pos, ',', true)
	local x = tonumber(pos_tab[1])
	local y = tonumber(pos_tab[2])
	return {x = x, y = y}, x, y
end


-- 获得赛季时间
function TianShen3v3WGData:GetSeasonTimeStr()
	local season_cfg_str = self:GetOtherCfg().season_end_day
	local tab = Split(season_cfg_str, "|")
	local day_1 = tonumber(tab[1])
	local day_2 = tonumber(tab[2])
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tab = os.date("*t", cur_time)
	if time_tab.day > day_2 then
		local start_str = string.format(Language.TianShen3v3.MonthDay, time_tab.month, day_2)
		local end_str = string.format(Language.TianShen3v3.MonthDay, time_tab.month, TimeUtil.GetCurMonthDaysAmount())
		return start_str .. " - " .. end_str
	else
		local start_str = string.format(Language.TianShen3v3.MonthDay, time_tab.month, day_1)
		local end_str = string.format(Language.TianShen3v3.MonthDay, time_tab.month, day_2 - 1)
		return start_str .. " - " .. end_str
	end
end

-- 获得连杀传闻配置
function TianShen3v3WGData:GetChuanWenCfg(type, kill_count)
	return CheckList(self.chuanwen_cfg, type, kill_count)
end

-- 阻挡区域
function TianShen3v3WGData:GetCircularBlock()
	local relive_pos = self:GetOtherCfg().relive_pos_1
	if self:GetOtherSide() == TS3V3_SIDE.RED then
		relive_pos = self:GetOtherCfg().relive_pos_2
	end

	local middle_pos = Split(relive_pos, ",")
	local middle_x = tonumber(middle_pos[1])
	local middle_y = tonumber(middle_pos[2])
	local radius = 22

	local math_cos = math.cos
	local math_sin = math.sin
	local const = 3.14 / 180 / 2
	local point_list = {}
	for i = 1, 360 * 2 do
		local x = middle_x + radius * math_cos(i * const)
		local y = middle_y + radius * math_sin(i * const)
		point_list[#point_list + 1] = {x = x, y = y}
	end

	local radius = 21
	for i = 1, 360 * 2 do
		local x = middle_x + radius * math_cos(i * const)
		local y = middle_y + radius * math_sin(i * const)
		point_list[#point_list + 1] = {x = x, y = y}
	end

	return point_list
end

function TianShen3v3WGData:GetBuffCfg(buff_type)
	return self.buff_cfg[buff_type]
end
----------------------------------- 配置数据存取End ---------------------------------

function TianShen3v3WGData:SetIsAutoFight(is_auto_fight) 
	self.is_auto_fight = is_auto_fight
end

function TianShen3v3WGData:GetIsAutoFight()
	return self.is_auto_fight
end

function TianShen3v3WGData:GetTianShen3v3JoinRewardRemind()
	local join_reward_cfg = self:GetJoinRewardCfg()
	for k,v in pairs(join_reward_cfg) do
		if self:GetJoinRewarCanFetch(v.seq) then
			return 1
		end
	end

	return 0
end

function TianShen3v3WGData:GetTianShen3v3BtnRemind()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.TIANSHEN_3V3) then
		return 1
	end
	return 0
end