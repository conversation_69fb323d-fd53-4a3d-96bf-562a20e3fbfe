﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using System;



public partial class YYDayNightCycleProfile
{

#if UNITY_EDITOR

    private DateTime lastTimeZoneCheck = DateTime.MinValue;

#endif

    private void UpdateTimeZone()
    {
        if (TimeZoneOffsetSeconds == -1111)
        {
            TimeZoneOffsetSeconds = (int)(Longitude * 24 / 360) * 3600;

#if UNITY_EDITOR

            //if ((DateTime.UtcNow - lastTimeZoneCheck).TotalSeconds > 10.0)
            //{
            //    lastTimeZoneCheck = DateTime.UtcNow;
            //    WebClientWithTimeout c = new WebClientWithTimeout();
            //    c.Timeout = 3000;
            //    TimeSpan unixTimeSpan = new DateTime(Year, Month, Day, 1, 1, 1, DateTimeKind.Utc) - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            //    string url = "http://api.timezonedb.com/v2/get-time-zone?by=position&lat=" + Latitude + "&lng=" + Longitude + "&time=" + (long)unixTimeSpan.TotalSeconds + "&key=1H9B390ZKKPX";
            //    try
            //    {
            //        c.DownloadStringCompleted += (o, e) =>
            //        {
            //            string xml = e.Result;
            //            System.Text.RegularExpressions.Match m = System.Text.RegularExpressions.Regex.Match(xml, @"\<gmtOffset\>(?<gmtOffset>.*?)\</gmtOffset\>");
            //            if (m.Success)
            //            {
            //                TimeZoneOffsetSeconds = int.Parse(m.Groups["gmtOffset"].Value);
            //                WeatherMakerScript.QueueOnMainThread(() =>
            //                {
            //                    SerializationHelper.SetDirty(this);
            //                });
            //            }
            //        };
            //        c.DownloadStringAsync(new System.Uri(url));
            //    }
            //    catch
            //    {
            //        // eat exceptions
            //    }
            //}

#endif

        }
    }

    private DateTime prevDt;

    //internal string TimeOfDayLabel = string.Empty;
   
    public DateTime DateTime
    {
        get
        {
            TimeSpan ts = TimeOfDayTimeSpan;
            return new DateTime(Year, Month, Day, ts.Hours, ts.Minutes, ts.Seconds, ts.Milliseconds, DateTimeKind.Local);
        }
        set
        {
            DateTime dt = value.ToLocalTime();
            Year = dt.Year;
            Month = dt.Month;
            Day = dt.Day;
            TimeOfDay = (float)dt.TimeOfDay.TotalSeconds;
        }
    }

    public TimeSpan TimeOfDayTimespan { get; private set; }
    public TimeSpan TimeOfDayTimeSpan { get { return TimeSpan.FromSeconds(TimeOfDay); } set { TimeOfDay = (float)value.TotalSeconds; } }
    public float NightMultiplier { get; private set; }
  

  
    private void UpdateTimeOfDay(bool updateTimeOfDay)
    {
        
        UpdateTimeZone();
        prevDt = DateTime;

#if UNITY_EDITOR

        TimeOfDayLabel = DateTime.Today.Add(TimeSpan.FromSeconds(TimeOfDay)).ToString("hh:mm tt");

#endif

        if (!updateTimeOfDay || !Application.isPlaying)
        {
            return;
        }

        if (NightMultiplier != 1.0f && Speed != 0.0f)
        {
            TimeOfDay += (Speed * accumulatedTime);
        }
        else if (NightMultiplier == 1.0f && NightSpeed != 0.0f)
        {
            TimeOfDay += (NightSpeed * accumulatedTime);
        }
        if (AdjustDateWhenDayEnds)
        {
            // handle wrapping of time of day
            if (TimeOfDay < 0.0f)
            {
                TimeOfDay += SecondsPerDay;
                DateTime dt = new DateTime(Year, Month, Day) - TimeSpan.FromDays(1.0) + TimeSpan.FromSeconds(TimeOfDay);
                Year = dt.Year;
                Month = dt.Month;
                Day = dt.Day;
            }
            else if (TimeOfDay >= SecondsPerDay)
            {
                TimeOfDay -= SecondsPerDay;
                DateTime dt = new DateTime(Year, Month, Day) + TimeSpan.FromDays(1.0) + TimeSpan.FromSeconds(TimeOfDay);
                Year = dt.Year;
                Month = dt.Month;
                Day = dt.Day;
            }
        }
        else if (TimeOfDay < 0.0f)
        {
            TimeOfDay += SecondsPerDay;
        }
        else if (TimeOfDay >= SecondsPerDay)
        {
            TimeOfDay -= SecondsPerDay;
        }
        TimeOfDayTimespan = TimeSpan.FromSeconds(TimeOfDay);

        // send events
        //if (prevDt.Year != Year)
        //{
        //    YYWeather.Instance.YearChanged.Invoke(this);
        //}
        //if (prevDt.Month != Month)
        //{
        //    YYWeather.Instance.MonthChanged.Invoke(this);
        //}
        //if (prevDt.Day != Day)
        //{
        //    YYWeather.Instance.DayChanged.Invoke(this);
        //}
        //if (prevDt.Hour != TimeOfDayTimespan.Hours)
        //{
        //    YYWeather.Instance.HourChanged.Invoke(this);
        //}
        //if (prevDt.Minute != TimeOfDayTimespan.Minutes)
        //{
        //    YYWeather.Instance.MinuteChanged.Invoke(this);
        //}
        //if (prevDt.Second != TimeOfDayTimespan.Seconds)
        //{
        //    YYWeather.Instance.SecondChanged.Invoke(this);
        //}
    }



    private class WebClientWithTimeout : System.Net.WebClient
    {
        protected override System.Net.WebRequest GetWebRequest(Uri uri)
        {
            System.Net.WebRequest w = base.GetWebRequest(uri);
            w.Timeout = Timeout;
            return w;
        }

        /// <summary>
        /// Milliseconds
        /// </summary>
        public int Timeout { get; set; }
    }


}
