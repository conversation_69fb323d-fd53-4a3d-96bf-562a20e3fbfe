﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public struct String_Int_Key
{
    public string strValue;
    public int intValue;
    public String_Int_Key(string strValue, int intValue)
    {
        this.strValue = strValue;
        this.intValue = intValue;
    }
}

public class String_Int_Comparer : IEqualityComparer<String_Int_Key>
{
    private static volatile String_Int_Comparer defaultComparer;
    public static String_Int_Comparer Default
    {
        get
        {
            if (defaultComparer == null)
            {
                defaultComparer = new String_Int_Comparer();
            }

            return defaultComparer;
        }
    }

    public bool Equals(String_Int_Key x, String_Int_Key y)
    {
        return x.strValue == y.strValue &&
            x.intValue == y.intValue;
    }

    public int GetHashCode(String_Int_Key obj)
    {
        int hashcode = obj.strValue.GetHashCode();
        hashcode ^= obj.intValue.GetHashCode();

        return hashcode;
    }
}
