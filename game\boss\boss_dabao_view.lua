local Max_Index = 10

function BossView:DeleteDabaoBossView()
	self.dabao_tip_load = nil
	self.need_flush_dabao_tip = nil

	if self.dabao_cell_list ~= nil then
        self.dabao_cell_list:DeleteMe()
        self.dabao_cell_list = nil
	end

	if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end

	if not IsEmptyTable(self.chips_item_list) then
        for k, v in pairs(self.chips_item_list) do
            v:DeleteMe()
        end
        self.chips_item_list = nil
    end

	self.is_load_complete = false
	self.parent_scroll_rect = nil
	self.dabao_cell_old_list = nil
end

function BossView:InitDabaoBossView()
	self.dabao_tip_load = true
	if self.need_flush_dabao_tip then
		self:OnFlushDabaoBossView()
	end
	self.node_list["btn_dabao_vip"]:SetActive(false)
	self:CreateDaBaoRareFallItem()
	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.cell_parent)
	self.is_load_complete = true

	self.chips_item_list = {}
    local list_node = self.node_list["chip_list"]
    for i = 0, Max_Index do
        self.chips_item_list[i] = BossDaBaoChipRender.New(list_node:FindObj("chip_" .. i))
        self.chips_item_list[i]:SetIndex(i)
    end

	XUI.AddClickEventListener(self.node_list["go_wear_btn"], BindTool.Bind(self.OnClickGoWearEquip, self))
	--self.node_list["btn_dabao_vip"]:SetActive(false).button:AddClickListener(BindTool.Bind(self.OpenTimesVipView, self, TabIndex.boss_dabao))
end

function BossView:SetParentScrollRect(scroll_rect)
    self.parent_scroll_rect = scroll_rect

    if self.is_load_complete then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

function BossView:CreateDaBaoRareFallItem()
	if not self.dabao_cell_list then
		self.dabao_cell_list = AsyncBaseGrid.New()
		local t = {}
		t.col = 3
		t.change_cells_num = 1
		t.itemRender = BossRewardCell
		t.list_view = self.node_list["dabao_cell_list"]
		self.dabao_cell_list:CreateCells(t)
		self.dabao_cell_list:SetStartZeroIndex(false)
	end
end

function BossView:OnFlushDabaoBossView()
	if not self.dabao_tip_load then
		self.need_flush_dabao_tip = true 
		return
	end
	local enter_time, max_times = BossWGData.Instance:GetDaBaoBossRemainEnterTimes()
	local color = max_times > enter_time and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED
	local num_str = ToColorStr(string.format("%d/%d", max_times - enter_time, max_times), color)
	if self.node_list["rich_dabao_tips"] then
        --self.node_list["btn_dabao_vip"]:SetActive(BossWGData.Instance:GetIsShowUpVip())
        self.node_list["rich_dabao_tips"].text.text = Language.Boss.DaBaoBossRichTip1 .. num_str
	end

	local cur_layer = self:GetCurLayer()
	local enter_role_num = BossWGData.Instance:GetAlRoleEnterInfo(cur_layer) or 0
	if self.node_list["enter_role_num"] then
		self.node_list["enter_role_num"].text.text = string.format(Language.Boss.EnterRoleNum, enter_role_num)
	end

	local show_order_num, show_quality_num = BossWGData.Instance:GetDaboBossShowIsEneter(cur_layer)
	local equip_show_list = CultivationWGData.Instance:GetCharmEquipShowCfg()
	if not equip_show_list then
		return
	end
	local show_list = equip_show_list[show_order_num]
	local list = {}

	for k, v in pairs(show_list) do
		local _, item_color = ItemWGData.Instance:GetItemColor(v.item_id)
		if v.solt == 0 and item_color >= show_quality_num then
			table.insert(list, v)
			break
		end
	end

	for k, v in pairs(show_list) do
		local _, item_color = ItemWGData.Instance:GetItemColor(v.item_id)
		if v.solt ~= 0 and item_color == show_quality_num then
			table.insert(list, v)
		end
	end

	for k,v in pairs(self.chips_item_list) do
        v:SetData(list)
    end
	self:refreshDabaoRareFall()
end

function BossView:SelectDabaoLayer(old_index)
	local cur_layer = self:GetCurLayer()
	local falg, need_level, min_order_num, min_color_num = BossWGData.Instance:GetDaboBossIsEneter(cur_layer)
	local next_min_order_num, next_min_color_num = BossWGData.Instance:GetDaboBossNextIsEneter(cur_layer)
	local enter_flag = CultivationWGData.Instance:GetDaboBossIsCanEneter(min_order_num, min_color_num)
	local next_enter_flag = CultivationWGData.Instance:GetDaboBossIsCanEneter(next_min_order_num, next_min_color_num)

	local str = ""
	local color = ""
	local color_text = ""
	local text_flag = 1
	if min_order_num == 0 or min_color_num == 0 or (enter_flag and next_min_order_num ~= 0) then
		--显示下级.
		color_text = Language.Common.ColorName4[next_min_color_num] or ""
		str = string.format(Language.Boss.DabaoBossEnterLimitText, next_min_order_num, color_text)
		color = next_enter_flag and COLOR3B.C8 or COLOR3B.C10
		text_flag = 2
	else
		color_text = Language.Common.ColorName4[min_color_num] or ""
		str = string.format(Language.Boss.DabaoBossEnterLimitText, min_order_num, color_text)
		color = enter_flag and COLOR3B.C8 or COLOR3B.C10
		text_flag = 1
	end
	self.node_list.enter_limit_text.text.text = Language.Boss.DabaoBossEnterLimitTextTitle[text_flag]
	self.node_list.dabao_enter_limit.text.text = ToColorStr(str, color)

	if not falg and cur_layer == 1 then
		self:refreshBossList()
		return
	else
        if not falg then
            need_level = RoleWGData.GetLevelString(need_level)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel,need_level))
			if old_index ~= self.cur_select_layer then
				local flag2 = BossWGData.Instance:GetDaboBossIsEneter(old_index)
				if not flag2 then
					old_index = BossWGData.Instance:GetDaBaoLevelLayerCfg()
				end
				self.layer_btn_list:SelectIndex(old_index)
			end
			return
		end
	end
end

function BossView:OpenTimesVipView(tab_index)
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	if role_vip == COMMON_CONSTS.MAX_VIP_LEVEL then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.SGVipTip6)
	else
		BossWGCtrl.Instance:OpenQuickVipBuyView(tab_index)
	end
end

function BossView:refreshDabaoRareFall()
    local boss_info = self.select_item_data
	if IsEmptyTable(boss_info) then
		return
	end

	local list = {}
	local data

	for i, v in pairs(boss_info.drop_item_list) do
		if boss_info.is_item_list then
			data = BossWGData.Instance:GetBossCellInfo(v, boss_info)
		else
			data = {
				item_id = tonumber(v),
				show_duobei = boss_info.show_duobei,
				task_type = boss_info.task_type,
				cell_scale = 0.9,
			}
		end
		table.insert(list, data)
	end

	if self.dabao_cell_old_list == nil then
		self.dabao_cell_list:SetDataList(list)
		self.dabao_cell_old_list = list
	else
		local flag = BossWGData.Instance:BossDataTableEquals(list, self.dabao_cell_old_list)
		if flag == false then
			self.dabao_cell_list:SetDataList(list)
			self.dabao_cell_old_list = list
		end
	end
end

function BossView:OnClickGoWearEquip()
    FunOpen.Instance:OpenViewByName(GuideModuleName.MultiFunctionView)
end




BossDaBaoChipRender = BossDaBaoChipRender or BaseClass(BaseRender)
function BossDaBaoChipRender:__init()
end

function BossDaBaoChipRender:LoadCallBack()
	self.can_get_flag = false
	self.get_flag = false
    self.node_list["page_self"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function BossDaBaoChipRender:__delete()
	self.can_get_flag = nil
	self.get_flag = nil
end

function BossDaBaoChipRender:OnFlush()
    if self.data == nil then
        return
    end

    local slot = self.index
    local bundle, asset
	local bg_bundle, bg_asset

	local solt_bag_data =  CultivationWGData.Instance:GetCharmOneKeyBagCache(slot)
	local have_flag = false
	local equip_cfg =  CultivationWGData.Instance:GetCharmEquipByItemId(self.data[slot + 1].item_id)
	local _, equip_color = ItemWGData.Instance:GetItemColor(self.data[slot + 1].item_id)
	if solt_bag_data then
		for k, v in pairs(solt_bag_data) do
			if v.order >= equip_cfg.order and v.color >= equip_color then
				have_flag = true
			end
		end
	else
		bundle, asset = ResPath.GetMultiFunctionImg("a2_charm_holy_seal_paper" .. slot)
		bg_bundle, bg_asset = ResPath.GetMultiFunctionImg("a2_charm_holy_seal_item_bg0")
	end
	
	local chip_data = CultivationWGData.Instance:GetCharmSoltDataBySolt(slot)

	if chip_data and chip_data.item_id ~= 0 then
		local chip_equip_cfg =  CultivationWGData.Instance:GetCharmEquipByItemId(chip_data.item_id)
		local _, chip_color = ItemWGData.Instance:GetItemColor(chip_data.item_id)
		self.get_flag = chip_equip_cfg.order >= equip_cfg.order and chip_color >= equip_color 
	end

	self.can_get_flag = self.get_flag or have_flag
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data[slot + 1].item_id)
	local _, item_color = ItemWGData.Instance:GetItemColor(self.data[slot + 1].item_id)
	if self.can_get_flag and item_cfg then
		bundle, asset = ResPath.GetItem(item_cfg.icon_id)
		bg_bundle, bg_asset = ResPath.GetMultiFunctionImg("a2_charm_holy_seal_item_bg" .. item_color)

		local effect_name = BaseCell_Ui_Circle_Effect[item_cfg.color]
		if effect_name then
			local effect_bundle, effect_assert = ResPath.GetWuPinKuangEffectUi(effect_name)
			--self.node_list["effect_root"]:ChangeAsset(effect_bundle, effect_assert)
			self.node_list["effect_root"]:SetActive(true)
		else
			self.node_list["effect_root"]:SetActive(false)
		end
	else
		self.node_list["effect_root"]:SetActive(false)
		bundle, asset = ResPath.GetMultiFunctionImg("a2_charm_holy_seal_paper" .. slot)
		bg_bundle, bg_asset = ResPath.GetMultiFunctionImg("a2_charm_holy_seal_item_bg0")
	end
	self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset)
    self.node_list.icon.image:LoadSprite(bundle, asset)
end

function BossDaBaoChipRender:OnClickCell()
    if self.data == nil then
        return
    end

	local slot = self.index
    TipWGCtrl.Instance:OpenItem({item_id = self.data[slot + 1].item_id})
end