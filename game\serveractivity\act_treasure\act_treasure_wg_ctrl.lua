require("game/serveractivity/act_treasure/act_treasure_wg_data")
require("game/serveractivity/act_treasure/act_treasure_view")

ActTreasureWGCtrl = ActTreasureWGCtrl or BaseClass(BaseWGCtrl)

function ActTreasureWGCtrl:__init()
	ActTreasureWGCtrl.Instance = self
	self.data = ActTreasureWGData.New()
	self.view = ActTreasureView.New(GuideModuleName.TreasureAct)
	self:RegisterAllProtocols()
end

function ActTreasureWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if nil ~= self.view then 
		self.view:DeleteMe()
		self.view = nil
	end
	ActTreasureWGCtrl.Instance = nil 

end

function ActTreasureWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRATreasureInfo, "OnRATreasureInfo")
end



function ActTreasureWGCtrl:Open()
	self.view:Open()
end

function ActTreasureWGCtrl:OnRATreasureInfo(protocol)
	self.data:UpdataInfoData(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end


function ActTreasureWGCtrl:SendAllInfoReq() 
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE) then
		return
	end
	local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_treasure_item_id)
	local param_t = {}
	if item_num <= 0 then
		ShopWGCtrl.Instance:SendShopBuy(other_cfg[1].national_treasure_gift, 1, 1, 0)
	end
	param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE,
			opera_type = RA_NATIONAL_TREASURE_TYPE.RA_NATIONAL_TREASURE_TYPE_USE_ITEM 
		}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ActTreasureWGCtrl:SendTreasureInfoReq() 
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE) then
		return
	end
	if self.data.times > 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Treasure_btnText.Tip)
		return true
	end
	local other_cfg = ActTreasureWGData.Instance:GetTreasureOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_treasure_item_id)
	local param_t = {}
	if item_num <= 0 then
		ShopWGCtrl.Instance:SendShopBuy(other_cfg[1].national_treasure_gift, 1, 1, 0)
	end
	param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE,
			opera_type = RA_NATIONAL_TREASURE_TYPE.RA_NATIONAL_TREASURE_TYPE_USE_ITEM 
		}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end