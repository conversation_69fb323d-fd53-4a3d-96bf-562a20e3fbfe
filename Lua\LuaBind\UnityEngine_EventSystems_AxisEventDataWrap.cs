﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_EventSystems_AxisEventDataWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.EventSystems.AxisEventData), typeof(UnityEngine.EventSystems.BaseEventData));
		<PERSON><PERSON>Function("New", _CreateUnityEngine_EventSystems_AxisEventData);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ar("moveVector", get_moveVector, set_moveVector);
		L.RegVar("moveDir", get_moveDir, set_moveDir);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_EventSystems_AxisEventData(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.EventSystems.EventSystem arg0 = (UnityEngine.EventSystems.EventSystem)ToLua.CheckObject<UnityEngine.EventSystems.EventSystem>(L, 1);
				UnityEngine.EventSystems.AxisEventData obj = new UnityEngine.EventSystems.AxisEventData(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.EventSystems.AxisEventData.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_moveVector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.EventSystems.AxisEventData obj = (UnityEngine.EventSystems.AxisEventData)o;
			UnityEngine.Vector2 ret = obj.moveVector;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index moveVector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_moveDir(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.EventSystems.AxisEventData obj = (UnityEngine.EventSystems.AxisEventData)o;
			UnityEngine.EventSystems.MoveDirection ret = obj.moveDir;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index moveDir on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_moveVector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.EventSystems.AxisEventData obj = (UnityEngine.EventSystems.AxisEventData)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.moveVector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index moveVector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_moveDir(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.EventSystems.AxisEventData obj = (UnityEngine.EventSystems.AxisEventData)o;
			UnityEngine.EventSystems.MoveDirection arg0 = (UnityEngine.EventSystems.MoveDirection)ToLua.CheckObject(L, 2, typeof(UnityEngine.EventSystems.MoveDirection));
			obj.moveDir = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index moveDir on a nil value");
		}
	}
}

