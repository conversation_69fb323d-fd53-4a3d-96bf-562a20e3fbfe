CustomizaedRumorsExchangeView = CustomizaedRumorsExchangeView or BaseClass(SafeBaseView)

function CustomizaedRumorsExchangeView:__init()
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/customized_rumors_ui_prefab", "layout_recharge_exchange_view")
end

function CustomizaedRumorsExchangeView:LoadCallBack()
    if not self.rumors_type_list then
        self.rumors_type_list = AsyncListView.New(CustomizaedRumorsExchangeTypeRender, self.node_list.rumors_type_list)
		self.rumors_type_list:SetSelectCallBack(BindTool.Bind(self.OnSelectRumorsType<PERSON>and<PERSON>, self))
		self.rumors_type_list:SetDefaultSelectIndex(nil)
    end

    if not self.reward_show_list then
        self.reward_show_list = AsyncListView.New(ItemCell, self.node_list.reward_show_list)
		self.reward_show_list:SetStartZeroIndex(true)
    end

    if not self.select_reward_list then
		self.select_reward_list = CustomizaedRumorsSelectRewardGrid.New()
		self.select_reward_list:CreateCells({col = 6, cell_count = 12, list_view = self.node_list["select_reward_list"],
		assetBundle = "uis/view/customized_rumors_ui_prefab", assetName = "select_small_cell",  itemRender = CRSelectRewardCellRender})
	end

    self.select_reward_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectCallBack, self))
	self.select_reward_list:SetStartZeroIndex(false)
	self.select_reward_list:SetIsMultiSelect(true)

	self.select_grade_data = {}
	self.cur_select_grade = -1

	XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClicGetReward, self))
    XUI.AddClickEventListener(self.node_list.btn_pack_send, BindTool.Bind(self.OnClicPackSend, self))
end

function CustomizaedRumorsExchangeView:ReleaseCallBack()
    if self.rumors_type_list then
        self.rumors_type_list:DeleteMe()
        self.rumors_type_list = nil
    end

    if self.reward_show_list then
        self.reward_show_list:DeleteMe()
        self.reward_show_list = nil
    end

    if self.select_reward_list then
        self.select_reward_list:DeleteMe()
        self.select_reward_list = nil
    end
end

function CustomizaedRumorsExchangeView:SetDefaultSelectGrade(grade)
	self.default_select_grade = grade
	self.first_enter_need_select = true
end

function CustomizaedRumorsExchangeView:OnFlush()
	local nav_data_list = CustomizedRumorsWGData.Instance:GetRumorBRNavDataList()
	self.rumors_type_list:SetDataList(nav_data_list)
	self.rumors_type_list:JumpToIndex(self:GetRumorTypeSelect(nav_data_list))
end

function CustomizaedRumorsExchangeView:GetRumorTypeSelect(nav_data_list)
	if self.first_enter_need_select then
		self.first_enter_need_select = false

		if self.default_select_grade then
			return self.default_select_grade
		end
	end

	if self.cur_select_grade > 0 then
		local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(self.cur_select_grade)

		if remind_info.remind then
			return self.cur_select_grade
		end
	end

	for k, v in pairs(nav_data_list) do
		local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(v.grade)

		if remind_info.remind then
			return v.grade
		end
	end

	return self.cur_select_grade > 0 and self.cur_select_grade or 1
end

function CustomizaedRumorsExchangeView:OnSelectRumorsTypeHandler(item)
	if nil == item or IsEmptyTable(item.data) then
        return
    end

	local data = item.data
	self.select_grade_data = data
	self.cur_select_grade = data.grade
    self.reward_show_list:SetDataList(data.reward_item)

	-- 需要保持Id 反读
	local data_list = {}
	local choose_item_data_list = data.choose_item
	for i = 0, #choose_item_data_list do
		local data_info = choose_item_data_list[i]
		local data = {}
		data.item_id = data_info.item_id
		data.num = data_info.num
		data.is_bind = data_info.is_bind
		data.select_index = i
		table.insert(data_list, data)
	end

	self.select_reward_list:SetDataList(data_list)

	local rmb_recharge = CustomizedRumorsWGData.Instance:GetRumorRealRecharge()
	local need_recgharge = data.need_rmb_num
	local can_get_reward = false

	local use_time = CustomizedRumorsWGData.Instance:GetGradeSendTime(data.grade)
	local can_use_time = data.free_count
	local time = 0

	if need_recgharge <= rmb_recharge then
		local is_get = CustomizedRumorsWGData.Instance:IsGetGradeReward(data.grade)
		if not is_get then
			can_get_reward = true
		end

		time = data.free_count - use_time
	else
		time = 0
	end

	XUI.SetButtonEnabled(self.node_list.btn_get_reward, can_get_reward)

	local color = time > 0 and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.desc_can_send_time.text.text = ToColorStr(time .. "/" .. can_use_time, color)
end	

function CustomizaedRumorsExchangeView:OnClicPackSend()
	if IsEmptyTable(self.select_grade_data) then
		return
	end

	--有没有领取奖励
	local is_get_reward = CustomizedRumorsWGData.Instance:IsGetGradeReward(self.cur_select_grade)
	if not is_get_reward then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.PleaseGetRewardFirst)
		return
	end

	local rmb_recharge = CustomizedRumorsWGData.Instance:GetRumorRealRecharge()
	local need_recgharge = self.select_grade_data.need_rmb_num

	if rmb_recharge < need_recgharge then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.BroadCastNotActive)
		return
	end

	local use_time = CustomizedRumorsWGData.Instance:GetGradeSendTime(self.select_grade_data.grade)
	local can_use_time = self.select_grade_data.free_count

	if use_time >= can_use_time then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.BroadCastTimeNotEnough)
		return
	end

	local select_list = self.select_reward_list:GetAllSelectCell()
	local other_cfg = CustomizedRumorsWGData.Instance:GetOtherCfg()

	if IsEmptyTable(select_list) or #select_list < other_cfg.need_select_count then
		local desc = string.format(Language.CustomizedRumors.BroadCastSendNeedSelectNum, other_cfg.need_select_count)
		SysMsgWGCtrl.Instance:ErrorRemind(desc)
		return
	end

	local select_index = 0
	local empty_tab = bit:d2b(0)
	for k, v in pairs(select_list) do
		select_index = v.select_index
		empty_tab[32 - select_index] = 1
	end

	local flag = bit:b2d(empty_tab)

	CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.SEND_REWARD, self.select_grade_data.grade, flag)
end

function CustomizaedRumorsExchangeView:OnClicGetReward()
	if IsEmptyTable(self.select_grade_data) then
		return
	end

	local rmb_recharge = CustomizedRumorsWGData.Instance:GetRumorRealRecharge()
	local need_recgharge = self.select_grade_data.need_rmb_num

	if need_recgharge <= rmb_recharge then
		local is_get = CustomizedRumorsWGData.Instance:IsGetGradeReward(self.select_grade_data.grade)
		if not is_get then
			CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.GET_REWARD, self.select_grade_data.grade)
		end
	end
end

function CustomizaedRumorsExchangeView:OnSelectCallBack()
end

CustomizaedRumorsExchangeTypeRender = CustomizaedRumorsExchangeTypeRender or BaseClass(BaseRender)
function CustomizaedRumorsExchangeTypeRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.desc_name.text.text = self.data.broadcast_name or ""
	self.node_list.desc_name_hl.text.text = self.data.broadcast_name or ""

	local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(self.data.grade)
	self.node_list.remind:CustomSetActive(remind_info.remind)
end

function CustomizaedRumorsExchangeTypeRender:OnSelectChange(is_select)
	self.node_list.bg:CustomSetActive(not is_select)
	self.node_list.select:CustomSetActive(is_select)
end
-----------------------------------CRSelectRewardCellRender------------------------------------
CRSelectRewardCellRender = CRSelectRewardCellRender or BaseClass(BaseRender)
function CRSelectRewardCellRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_pos"])
		self.item_cell:SetIsShowTips(false)
	end

	self.node_list["select_click"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
	self.event_listener = self.node_list["select_click"].event_trigger_listener
	if self.event_listener then
		self.event_listener:AddPointerDownListener(BindTool.Bind(self.OnClickPointDown, self))
		self.event_listener:AddPointerUpListener(BindTool.Bind(self.OnClickPointUp, self))
	end

	self.long_touch_flag = false
end

function CRSelectRewardCellRender:__delete()
	self:CancelClickLongTouchTimer()

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.long_touch_flag = nil
end

function CRSelectRewardCellRender:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data)
end

function CRSelectRewardCellRender:OnSelectChange(is_select)
	self.node_list["select_flag"]:SetActive(is_select)
end

function CRSelectRewardCellRender:OnClickCell()
	if not self.data then
		return
	end

	if self.long_touch_flag then 
		return
	end
	
	self:OnClick()
end

function CRSelectRewardCellRender:OnClickPointDown()
	if not self.data then
		return
	end

	self.long_touch_flag = false

	self.long_touch_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.long_touch_flag = true
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		self:CancelClickLongTouchTimer()
	end, 1)
end

function CRSelectRewardCellRender:OnClickPointUp()
	self:CancelClickLongTouchTimer()
end

function CRSelectRewardCellRender:CancelClickLongTouchTimer()
	if self.long_touch_delay_timer then
		GlobalTimerQuest:CancelQuest(self.long_touch_delay_timer)
		self.long_touch_delay_timer = nil
	end
end
-------------------------------CustomizaedRumorsSelectRewardGrid---------------------------------
CustomizaedRumorsSelectRewardGrid = CustomizaedRumorsSelectRewardGrid or BaseClass(AsyncBaseGrid)
function CustomizaedRumorsSelectRewardGrid:IsSelectMultiNumLimit(cell_index)
	local other_cfg = CustomizedRumorsWGData.Instance:GetOtherCfg()
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= other_cfg.need_select_count then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.limit_select_num)
            return true
        end
    end

    return false
end
