function PrivilegeCollectionView:ZZTQLoadIndecCallBack()
    if not self.zztq_privilege_list then
        self.zztq_privilege_list = AsyncListView.New(PRICOLZZZTQItemCellRender, self.node_list.zztq_privilege_list)
        self.zztq_privilege_list:SetSelect<PERSON>allBack(BindTool.Bind(self.OnSelectZZTQCellHandler, self))
    end

    if not self.zztq_pri_reward_list then
        self.zztq_pri_reward_list = AsyncListView.New(ZZTQPriItemCellRender, self.node_list.zztq_pri_reward_list)
        self.zztq_pri_reward_list:SetStartZeroIndex(false)
    end

    if not self.zztq_daily_reward_list then
        self.zztq_daily_reward_list = AsyncListView.New(ZZTQDailyRewardItemCellRender, self.node_list.zztq_daily_reward_list)
        self.zztq_daily_reward_list:SetStartZeroIndex(false)
    end

    -- if not self.zztq_model then
    --     self.zztq_model = OperationActRender.New(self.node_list.zztq_model)
    --     self.zztq_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    -- end

	-- if not self.zztq_target_item then
	-- 	self.zztq_target_item = ItemCell.New(self.node_list.zztq_target_pos)
	-- end

    XUI.AddClickEventListener(self.node_list["btn_zztq_buy"],BindTool.Bind(self.OnClickZZTQBuy, self))
	XUI.AddClickEventListener(self.node_list["btn_zztq_left"],BindTool.Bind(self.OnClickZZTQLeftBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_zztq_right"],BindTool.Bind(self.OnClickZZTQRightBtn, self))

    self.select_zztq_index = -1
    self.select_zztq_data = {}
end

function PrivilegeCollectionView:ZZTQShowIndecCallBack()
    self.select_zztq_index = -1
	self.zztq_model_cache = nil
	-- Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.PRI_COL_ZZTQ)
end

function PrivilegeCollectionView:ZZTQReleaseCallBack()
    if self.zztq_privilege_list then
        self.zztq_privilege_list:DeleteMe()
        self.zztq_privilege_list = nil
    end

    if self.zztq_pri_reward_list then
        self.zztq_pri_reward_list:DeleteMe()
        self.zztq_pri_reward_list = nil
    end

    if self.zztq_daily_reward_list then
        self.zztq_daily_reward_list:DeleteMe()
        self.zztq_daily_reward_list = nil
    end

    if self.zztq_role_model then
        self.zztq_role_model:DeleteMe()
        self.zztq_role_model = nil
    end

    if self.zztq_lingchong_model then
        self.zztq_lingchong_model:DeleteMe()
        self.zztq_lingchong_model = nil
    end

    if self.zztq_xianwa_model then
        self.zztq_xianwa_model:DeleteMe()
        self.zztq_xianwa_model = nil
    end

    if self.zztq_mount_model then
        self.zztq_mount_model:DeleteMe()
        self.zztq_mount_model = nil
    end

	if self.zztq_weapon_model then
		self.zztq_weapon_model:DeleteMe()
		self.zztq_weapon_model = nil
	end

	-- if self.zztq_target_item then
	-- 	self.zztq_target_item:DeleteMe()
	-- 	self.zztq_target_item = nil
	-- end

    -- if self.zztq_model then
    --     self.zztq_model:DeleteMe()
    --     self.zztq_model = nil
    -- end

	self.zztq_model_cache = nil
end

function PrivilegeCollectionView:ZZTQOnFlush()
    local data_list = PrivilegeCollectionWGData.Instance:GetZZTQDataListNoSort()
    self.zztq_privilege_list:SetDataList(data_list)
    self.zztq_privilege_list:JumpToIndex(self:GetZZTQDefaultSelect(data_list))

	local real_recharge_num = PrivilegeCollectionWGData.Instance:GetRealRechargeNum()
    self.node_list.desc_zztq_sflq.text.text = string.format(Language.PrivilegeCollection.RealRechargeNumStr, real_recharge_num)
end

function PrivilegeCollectionView:GetZZTQDefaultSelect(data_list)
    if not IsEmptyTable(self.select_zztq_data) then
        if PrivilegeCollectionWGData.Instance:GetZZTQPriRedPointBySeq(self.select_zztq_data.seq) then
            for k, v in pairs(data_list) do
                if v.seq == self.select_zztq_data.seq then
                    return k
                end
            end
        end
    end

    local default_select_index = 1
    for k, v in pairs(data_list) do
        if PrivilegeCollectionWGData.Instance:GetZZTQPriRedPointBySeq(v.seq) then
            return k
        end

        if not IsEmptyTable(self.select_zztq_data) and v.seq == self.select_zztq_data.seq then
            default_select_index = k
        end
    end

    return default_select_index
end

function PrivilegeCollectionView:ZZTQPlayLastAction()
	-- if self.zztq_role_model then
	-- 	self.zztq_role_model:PlayLastAction()
	-- end

	-- if self.zztq_lingchong_model then
	-- 	self.zztq_lingchong_model:PlayLastAction()
	-- end

	-- if self.zztq_xianwa_model then
	-- 	self.zztq_xianwa_model:PlayLastAction()
	-- end

	-- if self.zztq_mount_model then
	-- 	self.zztq_mount_model:PlayLastAction()
	-- end

	-- if self.zztq_weapon_model then
	-- 	self.zztq_weapon_model:PlayLastAction()
	-- end

	self:FlushZZTQModel(self.select_zztq_data)
end

function PrivilegeCollectionView:OnSelectZZTQCellHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    self.select_zztq_index = item.index
    self.select_zztq_data = item.data
    self:FlushZZTQInfo()

	local data_list = PrivilegeCollectionWGData.Instance:GetZZTQDataListNoSort()
	self.node_list["btn_zztq_left"]:CustomSetActive(item.index > 1)
	self.node_list["btn_zztq_right"]:CustomSetActive(item.index < #data_list)

	self.node_list.desc_czed.text.text = self.select_zztq_data.desc1
	self.node_list.desc_jhzq.text.text = self.select_zztq_data.desc2
	self.node_list.desc_tpbl.text.text = self.select_zztq_data.desc3
end

function PrivilegeCollectionView:OnClickZZTQLeftBtn()
	self.zztq_privilege_list:JumpToIndex(self.select_zztq_index - 1)
end

function PrivilegeCollectionView:OnClickZZTQRightBtn()
	self.zztq_privilege_list:JumpToIndex(self.select_zztq_index + 1)
end

function PrivilegeCollectionView:FlushZZTQInfo()
    if IsEmptyTable(self.select_zztq_data) then
        return
    end

    self:FlushZZTQModel(self.select_zztq_data)

	local bundle, asset = ResPath.GetRawImagesPNG(self.select_zztq_data.zztq_title)
	self.node_list.zztq_title.raw_image:LoadSprite(bundle, asset, function ()
		self.node_list.zztq_title.raw_image:SetNativeSize()
	end)

	local right_name_title, righe_name_asset = ResPath.GetRawImagesPNG(self.select_zztq_data.zztq_right_title)
	self.node_list.zztq_right_icon.raw_image:LoadSprite(right_name_title, righe_name_asset, function ()
		self.node_list.zztq_right_icon.raw_image:SetNativeSize()
	end)

    local reward_item_id = self.select_zztq_data.show_reward
    self.node_list.zztq_cap_value.text.text = ItemShowWGData.CalculateCapability(reward_item_id, false)

	-- self.zztq_target_item:SetData({item_id = reward_item_id})
    -- self.node_list.zztq_right_icon.image:LoadSprite(ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(reward_item_id)))

    -- self.node_list.zztq_right_title.text.text = self.select_zztq_data.name

    local is_get_privilege = PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(self.select_zztq_data.seq)
    local is_get_tequan_reward = PrivilegeCollectionWGData.Instance:IsGetTequanReward(self.select_zztq_data.seq)
    local is_has_day_reward = PrivilegeCollectionWGData.Instance:IsTequanHasDayReward(self.select_zztq_data.seq)
    local is_get_reward = PrivilegeCollectionWGData.Instance:IsGetTeQuanEveryDayReward(self.select_zztq_data.seq)

    self.node_list.zztq_flag_is_get:CustomSetActive(is_get_privilege and is_get_tequan_reward and (not is_has_day_reward or (is_has_day_reward and is_get_reward)))
    self.node_list.btn_zztq_buy:CustomSetActive(not is_get_privilege or not is_get_tequan_reward or (is_has_day_reward and not is_get_reward))
    self.node_list.btn_zztq_buy_remind:CustomSetActive(is_get_privilege and (not is_get_reward or (is_has_day_reward and not is_get_reward)))

    local desc_zztq_buy_str = ""
	local desc_zztq_lock_str = ""
	local is_lock = false
    if is_get_privilege then
        if not is_get_tequan_reward then
            desc_zztq_buy_str = Language.PrivilegeCollection.TeQuanCanLingQuStr
        elseif is_has_day_reward and not is_get_reward then
            -- 每日奖励
            desc_zztq_buy_str = Language.PrivilegeCollection.TeQuanDayRewardCanLingQuStr
        end
    else
        local last_seq = self.select_zztq_data.last_seq
        local buy_type = self.select_zztq_data.buy_type
        if last_seq >= 0 and not PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(last_seq) then 
            local tequan_cfg = PrivilegeCollectionWGData.Instance:GetTeQuanCfgBySeq(last_seq)
            desc_zztq_lock_str = string.format(Language.PrivilegeCollection.TeQuanUnLockLastTipStr, tequan_cfg.name)
			is_lock = true
        else
            if buy_type == 1 then
                desc_zztq_buy_str = RoleWGData.GetPayMoneyStr(self.select_zztq_data.price, self.select_zztq_data.rmb_type, self.select_zztq_data.rmb_seq)
            elseif buy_type == 2 then
                desc_zztq_buy_str = string.format(Language.PrivilegeCollection.ScoreStr, self.select_zztq_data.price)
            elseif buy_type == 3 then
                desc_zztq_buy_str = string.format(Language.PrivilegeCollection.ZZTQRealRechargeNumStr, RoleWGData.GetPayMoneyStr(self.select_zztq_data.price, self.select_zztq_data.rmb_type, self.select_zztq_data.rmb_seq))
            end
        end
    end

    self.node_list.desc_zztq_buy.text.text = desc_zztq_buy_str
    self.node_list.desc_zztq_lock_text.text.text = desc_zztq_lock_str

	self.node_list.desc_zztq_buy:SetActive(not is_lock)
	self.node_list.desc_zztq_lock_text:SetActive(is_lock)

	-- local reward_data_list = {}
	-- if self.select_zztq_data.reward_item then
	-- 	for k, v in pairs(self.select_zztq_data.reward_item) do
	-- 		if v.item_id ~= reward_item_id then
	-- 			table.insert(reward_data_list, v)
	-- 		end
	-- 	end
	-- end

	local privilege_reward_data_list = {}

	if not IsEmptyTable(self.select_zztq_data.reward_item) then
		for k, v in pairs(self.select_zztq_data.reward_item) do
			table.insert(privilege_reward_data_list, {item_data = v, is_get_privilege = is_get_privilege, big_reward = v.item_id == reward_item_id})
		end
	end

    self.zztq_pri_reward_list:SetDataList(privilege_reward_data_list)

	local daily_reward_list = {}

	if not IsEmptyTable(self.select_zztq_data.day_reward_item) then
		for k, v in pairs(self.select_zztq_data.day_reward_item) do
			table.insert(daily_reward_list, {item_data = v, is_get_daily_reward = is_has_day_reward and is_get_reward})
		end
	end

    self.zztq_daily_reward_list:SetDataList(daily_reward_list)
end

function PrivilegeCollectionView:FlushZZTQModel(model_data)
	if self.zztq_model_cache == model_data then
		return
	end

	self.zztq_model_cache = model_data

	if model_data.need_show_role == 1 then
		if not self.zztq_role_model then
			self.zztq_role_model = RoleModel.New()
			self.zztq_role_model:SetUISceneModel(self.node_list["zztq_role_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
			self:AddUiRoleModel(self.zztq_role_model, TabIndex.pri_col_zztq)
		end
	else
		if self.zztq_role_model then
			self.zztq_role_model:RemoveAllModel()
		end
	end

	if self.zztq_lingchong_model then
		self.zztq_lingchong_model:RemoveAllModel()
	end

	if self.zztq_mount_model then
		self.zztq_mount_model:RemoveAllModel()
	end

	if self.zztq_mount_model then
        self.zztq_mount_model:RemoveAllModel()
    end

	if self.zztq_weapon_model then
		self.zztq_weapon_model:RemoveAllModel()
	end

	local show_list = PrivilegeCollectionWGData.Instance:GetShowModelCfgListBySeq(model_data.model_show_seq)
	if IsEmptyTable(show_list) then
		return
	end

	-- self.node_list["zztq_lc_display"]:SetActive(false)
	-- self.node_list["zztq_xw_display"]:SetActive(false)
	-- self.node_list["zztq_mount_display"]:SetActive(false)

	if model_data.need_show_role == 1 then
		local body_res_id = AppearanceWGData.Instance:GetRoleResId()
		local mount_res_id = 0
		local have_foot_print = false
		local has_fashion_show = false
	
		local res_id, fashion_cfg
		for k, data in pairs(show_list) do
			if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
				fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
				if fashion_cfg then                                                          -- 时装
					local prof = GameVoManager.Instance:GetMainRoleVo().prof
					body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
					has_fashion_show = true
				end
			elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				have_foot_print = true
			end
		end
	
		local d_body_res, d_hair_res, d_face_res
		local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
		local vo = main_role and main_role:GetVo()
		if not has_fashion_show and vo and vo.appearance then
			if vo.appearance.fashion_body == 0 then
				d_body_res = vo.appearance.default_body_res_id
				d_hair_res = vo.appearance.default_hair_res_id
				d_face_res = vo.appearance.default_face_res_id
			end
		end
	
		local animation_name = have_foot_print and SceneObjAnimator.Move or SceneObjAnimator.Rest
		
		local extra_role_model_data = {
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
			animation_name = animation_name,
		}
		self.zztq_role_model:SetRoleResid(body_res_id, nil, extra_role_model_data)
	end

	for k, v in pairs(show_list) do
		self:ShowZZTQModelByData(model_data, v)
	end

    self:ChangeZZTQModelShowScale(model_data.theme_seq)
end

function PrivilegeCollectionView:ShowZZTQModelByData(model_data, data)
    if IsEmptyTable(data) then
		return
	end

    local res_id, fashion_cfg
	if model_data.need_show_role == 1 and data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.zztq_role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.zztq_role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.zztq_role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.zztq_role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.zztq_role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.zztq_role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.zztq_role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.zztq_role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.zztq_role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.zztq_role_model:SetFootTrailModel(res_id)
				self.zztq_role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				self.zztq_role_model:PlayRoleAction(SceneObjAnimator.Move)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetZZTQLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			self:SetZZTQXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)

		if fashion_cfg and model_data.need_show_role == 1 and model_data.need_mount == 1 then
			self.zztq_role_model:SetMountResid(fashion_cfg.appe_image_id)
			-- self.zztq_role_model:PlayStartAction(MOUNT_RIDING_TYPE[1])

			local mount_ridding_type = MOUNT_RIDING_TYPE[1]
			local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(fashion_cfg.appe_image_id)
			if action_cfg and action_cfg.action and MOUNT_RIDING_TYPE[action_cfg.action] then
				mount_ridding_type = MOUNT_RIDING_TYPE[action_cfg.action]
			end
			self.zztq_role_model:PlayStartAction(mount_ridding_type)

		else
			if fashion_cfg then
				self:SetZZTQMountModelData(fashion_cfg.appe_image_id)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		-- if fashion_cfg then
		-- 	self:SetZZTQMountModelData(fashion_cfg.active_id)
		-- end

		if fashion_cfg and model_data.need_show_role == 1 and model_data.need_mount == 1 then
			self.zztq_role_model:SetMountResid(fashion_cfg.appe_image_id)
			-- self.zztq_role_model:PlayStartAction(MOUNT_RIDING_TYPE[1])
			local mount_ridding_type = MOUNT_RIDING_TYPE[1]
			local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(fashion_cfg.appe_image_id)
			if action_cfg and action_cfg.action and MOUNT_RIDING_TYPE[action_cfg.action] then
				mount_ridding_type = MOUNT_RIDING_TYPE[action_cfg.action]
			end
			self.zztq_role_model:PlayStartAction(mount_ridding_type)
		else
			if fashion_cfg then
				self:SetZZTQMountModelData(fashion_cfg.appe_image_id)
			end
		end
	elseif data.type == 6 then
		-- 武器
		local weapon_res_id, _ = RoleWGData.GetFashionWeaponId(nil, nil, data.param1)
		local bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)

		if model_data.need_show_role == 1 then
			self.zztq_role_model:SetWeaponModel(bundle, asset)
		else
			if not self.zztq_weapon_model then
				self.zztq_weapon_model = RoleModel.New()
				self.zztq_weapon_model:SetUISceneModel(self.node_list["zztq_weapon_display"].event_trigger_listener,
				MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
				self:AddUiRoleModel(self.zztq_weapon_model, TabIndex.pri_col_zztq)
			end

			self.zztq_weapon_model:SetMainAsset(bundle, asset)
		end
	end
end

function PrivilegeCollectionView:SetZZTQLingChongModelData(type, res_id)
	self.node_list["zztq_lc_display"]:SetActive(true)
	if not self.zztq_lingchong_model then
		self.zztq_lingchong_model = RoleModel.New()
		self.zztq_lingchong_model:SetUISceneModel(self.node_list["zztq_lc_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.zztq_lingchong_model, TabIndex.pri_col_zztq)
	else
		if self.zztq_lingchong_model then
			self.zztq_lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)

	self.zztq_lingchong_model:SetMainAsset(bundle, asset, function()
		self.zztq_lingchong_model:PlaySoulAction()
	end)

	self.zztq_lingchong_model:FixToOrthographic(self.root_node_transform)
end

function PrivilegeCollectionView:SetZZTQXianWaModelData(res_id)
	self.node_list["zztq_xw_display"]:SetActive(true)
	if nil == self.zztq_xianwa_model then
		self.zztq_xianwa_model = RoleModel.New()

		self.zztq_xianwa_model:SetUISceneModel(self.node_list["zztq_xw_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.zztq_xianwa_model, TabIndex.pri_col_zztq)
	else
		if self.zztq_xianwa_model then
			self.zztq_xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.zztq_xianwa_model:SetMainAsset(bundle, asset, function()
		self.zztq_xianwa_model:PlaySoulAction()
	end)

	self.zztq_xianwa_model:FixToOrthographic(self.root_node_transform)
end

function PrivilegeCollectionView:SetZZTQMountModelData(res_id)
    self.node_list["zztq_mount_display"]:SetActive(true)
	if not self.zztq_mount_model then
		self.zztq_mount_model = RoleModel.New()

        self.zztq_mount_model:SetUISceneModel(self.node_list["zztq_mount_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.zztq_mount_model, TabIndex.pri_col_zztq)
	else
		if self.zztq_mount_model then
			self.zztq_mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.zztq_mount_model:SetMainAsset(bundle, asset, function()
		self.zztq_mount_model:PlayMountAction()
	end)

	self.zztq_mount_model:FixToOrthographic(self.root_node_transform)
end

function PrivilegeCollectionView:ChangeZZTQModelShowScale(theme_seq)
	local data = PrivilegeCollectionWGData.Instance:GetShowModelThemeCfgBySeq(theme_seq)
	if IsEmptyTable(data) then
		return
	end

	-- local pos_str = data.main_whole_display_pos
	-- if pos_str and pos_str ~= "" then
	-- 	local pos = Split(pos_str, "|")
	-- 	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zztq_role_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	-- 	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zztq_weapon_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	-- end

    local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()

	local pos_str = data["main_pos_" .. prof.."_"..sex] or data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		if self.zztq_role_model then
			self.zztq_role_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		if self.zztq_weapon_model then
			self.zztq_weapon_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end
	end

	local rotate_str = data["main_rot_" .. prof.."_"..sex] or data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.zztq_role_model then
			self.zztq_role_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end

		if self.zztq_weapon_model then
			self.zztq_weapon_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end
	end

	local scale = data["main_scale_" .. prof.."_"..sex] or data.main_scale
	if scale and scale ~= "" then
		if self.zztq_role_model then
			self.zztq_role_model:SetUSAdjustmentNodeLocalScale(scale)
		end

		if self.zztq_weapon_model then
			self.zztq_weapon_model:SetUSAdjustmentNodeLocalScale(scale)
		end
	end

	--灵宠
	if self.node_list["zztq_lc_display"]:GetActive() then
		pos_str = data.pet_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zztq_lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.zztq_lingchong_model then
				self.zztq_lingchong_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.zztq_lingchong_model then
				self.zztq_lingchong_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			if self.zztq_lingchong_model then
				self.zztq_lingchong_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end

	--仙娃
	if self.node_list["zztq_xw_display"]:GetActive() then
		pos_str = data.xw_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zztq_xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.zztq_xianwa_model then
				self.zztq_xianwa_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.zztq_xianwa_model then
				self.zztq_xianwa_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			if self.zztq_xianwa_model then
				self.zztq_xianwa_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end

	--坐骑
	if self.node_list["zztq_mount_display"]:GetActive() then
		pos_str = data.mount_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.zztq_mount_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.zztq_mount_model then
				self.zztq_mount_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.zztq_mount_model then
				self.zztq_mount_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			if self.zztq_mount_model then
				self.zztq_mount_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end
end

function PrivilegeCollectionView:OnClickZZTQBuy()
    if IsEmptyTable(self.select_zztq_data) then
        return
    end

    local last_seq = self.select_zztq_data.last_seq
    local seq = self.select_zztq_data.seq
    local price = self.select_zztq_data.price
    local rmb_type, rmb_seq = self.select_zztq_data.rmb_type , self.select_zztq_data.rmb_seq
    local buy_type = self.select_zztq_data.buy_type

    if last_seq >= 0 then
        if not PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(last_seq) then
            local tequan_cfg = PrivilegeCollectionWGData.Instance:GetTeQuanCfgBySeq(last_seq)
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.PrivilegeCollection.TeQuanUnLockLastTipStr, tequan_cfg.name))
            return
        end
    end

    local is_get_privilege = PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(seq)

    if not is_get_privilege then
        if buy_type == 1 then
			if PrivilegeCollectionWGData.Instance:IsShowZZTQOneKeyPackageTeQuan() then
				local one_key_index = self.select_zztq_data.one_key_recharge_index
				local one_key_data = {}
	
				if one_key_index and "" ~= one_key_index then
					one_key_data = PrivilegeCollectionWGData.Instance:GetOneKeyTequanCfgByIndex(one_key_index)
				end
	
				if not IsEmptyTable(one_key_data) then
					if one_key_data.last_seq < 0 or PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(one_key_data.last_seq) then
						local one_key_package_info = {
							rmb_type = one_key_data.rmb_type, 
							rmb_seq = one_key_data.rmb_seq, 
							price = one_key_data.price, 
							last_price = one_key_data.last_price,
							price_str = RoleWGData.GetPayMoneyStr(one_key_data.price, one_key_data.rmb_type, one_key_data.rmb_seq),
							last_price_str = string.format(Language.PrivilegeCollection.OriginalPrice1, RoleWGData.GetPayMoneyStr(one_key_data.last_price)),
							discount_str = one_key_data.discount,
							cfg = one_key_data,
	
							call_back = function()
								RechargeWGCtrl.Instance:Recharge(one_key_data.price, one_key_data.rmb_type, one_key_data.rmb_seq)
							end
						}
	
						RechargeWGCtrl.Instance:Recharge(price, rmb_type, rmb_seq, -1, -1, one_key_package_info)
					else
						RechargeWGCtrl.Instance:Recharge(price, rmb_type, rmb_seq)
					end
				else
					RechargeWGCtrl.Instance:Recharge(price, rmb_type, rmb_seq)
				end
			else
				RechargeWGCtrl.Instance:Recharge(price, rmb_type, rmb_seq)
			end
        elseif buy_type == 2 then
            local score = PrivilegeCollectionWGData.Instance:GetCurScore()
            if score >= price then
                PrivilegeCollectionWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_TEQUAN, seq)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.PrivilegeCollection.NoEnoughScore)
            end
        elseif buy_type == 3 then
            local real_recharge_num = PrivilegeCollectionWGData.Instance:GetRealRechargeNum()

            if real_recharge_num < price then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.PrivilegeCollection.RealRechargeNumNotEnoughStr, RoleWGData.GetPayMoneyStr(price, rmb_type, rmb_seq)))
                ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
            end
        end
    else
        local is_get_tequan_reward = PrivilegeCollectionWGData.Instance:IsGetTequanReward(seq)
        if not is_get_tequan_reward then
            PrivilegeCollectionWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_TEQUAN_REWARD, seq)
        else
            local is_has_day_reward = PrivilegeCollectionWGData.Instance:IsTequanHasDayReward(seq)
            local is_get_privilege_reward = PrivilegeCollectionWGData.Instance:IsGetTeQuanEveryDayReward(seq)
            if is_has_day_reward and not is_get_privilege_reward then
                PrivilegeCollectionWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, seq)
            end
        end
    end
end

--------------------------------PRICOLZZZTQItemCellRender---------------------------------
PRICOLZZZTQItemCellRender = PRICOLZZZTQItemCellRender or BaseClass(BaseRender)

function PRICOLZZZTQItemCellRender:__init()
	self.is_lock = false
end

function PRICOLZZZTQItemCellRender:__delete()
	self.is_lock = nil

	if self.scale_tween then
        self.scale_tween:Kill()
        self.scale_tween = nil
    end
end

function PRICOLZZZTQItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.name.text.text = self.data.name
	self.node_list.normal_name.text.text = self.data.name
    local is_get_privilege = PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(self.data.seq)
	local lock = not is_get_privilege and (self.data.last_seq >= 0) and (not PrivilegeCollectionWGData.Instance:IsTeQuanUnLockBySeq(self.data.last_seq))
    self.node_list.lock_bg:CustomSetActive(lock)
	self.is_lock = lock

    local is_has_day_reward = PrivilegeCollectionWGData.Instance:IsTequanHasDayReward(self.data.seq)
    local is_get_reward = PrivilegeCollectionWGData.Instance:IsGetTeQuanEveryDayReward(self.data.seq)
    self.node_list.remind:CustomSetActive(is_get_privilege and (not is_get_reward or (is_has_day_reward and not is_get_reward)))

	local bundle, asset = ResPath.GetPrivilegeCollectionImg(self.data.zztq_icon)
	self.node_list.icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local bg_bundle, bg_asset = ResPath.GetPrivilegeCollectionImg(self.data.zztq_icon_di)
	self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset, function ()
		self.node_list.bg.image:SetNativeSize()
	end)
end

function PRICOLZZZTQItemCellRender:OnSelectChange(is_select)
    self.node_list["bg"]:CustomSetActive(not is_select)
    self.node_list["bg_hl"]:CustomSetActive(is_select)
	
	-- local target_scale = is_select and 1 or 0.8
	-- self.node_list.root.rect:DOScale(Vector3(target_scale, target_scale, target_scale), 0.3)

	if self.scale_tween then
        self.scale_tween:Kill()
        self.scale_tween = nil
    end

	local scale_vec3 = is_select and Vector3(1, 1, 1) or Vector3(0.8, 0.8, 0.8)
    if is_select then
        self.scale_tween = self.node_list.root.rect:DOScale(scale_vec3, 0.3)
    else
        self.node_list.root.rect.localScale = scale_vec3
    end

	self.node_list.normal_name_bg:CustomSetActive(not self.is_lock and not is_select)
	self.node_list.select_name_bg:CustomSetActive(not self.is_lock and is_select)
end

ZZTQPriItemCellRender = ZZTQPriItemCellRender or BaseClass(BaseRender)

function ZZTQPriItemCellRender:LoadCallBack()
	if not self.item then
		self.item = ItemCell.New(self.node_list.item_pos)
	end
end

function ZZTQPriItemCellRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function ZZTQPriItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item:SetData(self.data.item_data)
	self.item:SetLingQuVisible(self.data.is_get_privilege)
	self.node_list.kuang:CustomSetActive(self.data.big_reward)
end

ZZTQDailyRewardItemCellRender = ZZTQDailyRewardItemCellRender or BaseClass(BaseRender)

function ZZTQDailyRewardItemCellRender:LoadCallBack()
		if not self.item then
		self.item = ItemCell.New(self.node_list.item_pos)
	end
end

function ZZTQDailyRewardItemCellRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function ZZTQDailyRewardItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item:SetData(self.data.item_data)
	self.item:SetLingQuVisible(self.data.is_get_daily_reward)
	self.node_list.kuang:CustomSetActive(false)
end