ChatWGData = ChatWGData or BaseClass()

MAX_CHANNEL_MSG_NUM	= 200							-- 频道消息最大数量
MAX_SYS_CHANNEL_MSG_NUM	= 25						-- 系统消息最大数量
MAX_CHUANWEN_CHANNEL_MSG_NUM	= 15			 	-- 传闻消息最大数量
MAX_PRESERVE_MSG_NUM = 200							-- 私聊消息最大数量(私聊+留言)
MAX_MSG_CACHE_NUM = 50 			 					-- 留言消息最大数量
MAX_TRANSMIT_MSG_NUM = 10							-- 喇叭消息最大数量

CHAT_EDIT_MAX = 40									-- 聊天输入最大字符限制

CHAT_POS_MAX = 1									-- 发送坐标最大数量
CHAT_ITEM_MAX = 6									-- 发送道具最大数量
CHAT_FACE_MAX = 5									-- 发送表情最大数量

CHAT_AITE_CACHE_MAX_NUM = 50 						-- 公共聊天@艾特消息最大缓存数量

SCENE_CHAT_BUBBLE_MAX_NUM = 5 						--场景聊天气泡框最大显示数量
SCENE_CHAT_BUBBLE_TIME = 3 							--场景聊天气泡框单个持续时长(S)

ChatWGData.IS_PB_AUDIO_CHAT = false

local CacheMsgMaxCount = 20				 			--聊天缓存队列最大长度
local CanAiteChannel = {
	[1] = CHANNEL_TYPE.WORLD,
	[2] = CHANNEL_TYPE.TEAM,
	[3] = CHANNEL_TYPE.GUILD,
	[4] = CHANNEL_TYPE.SCENE,
	-- [5] = CHANNEL_TYPE.ZHANDUI3V3,
	[5] = CHANNEL_TYPE.CROSS,
}

ChatWGData.RedPacketMaching = string.format("{openLink;%d}", CHAT_LINK_TYPE.RED_PACKET)
ChatWGData.BillionDEZGMaching = string.format("{openLink;%d", CHAT_LINK_TYPE.BILLION_DEZG)
ChatWGData.BillionDRPTMaching = string.format("{openLink;%d", CHAT_LINK_TYPE.BILLION_DRPT)
ChatWGData.BossXiezhuMaching = "npdd_sddody" --boss__assist

function ChatWGData:__init()
	if ChatWGData.Instance then
		ErrorLog("[ChatWGData]:Attempt to create singleton twice!")
	end
	ChatWGData.Instance = self

	self.face_tab = {}								--表情列表，每次添加表情的时候插入这个列表，发送之前进行校验
	self.item_tab = {}								--物品列表，每次添加物品的时候插入这个列表，发送之前进行校验
	self.point_tab = {}								--坐标列表，每次添加坐标的时候插入这个列表，发送之前进行校验
	self.openlink_tab = {}							--超链接(openLink)列表，每次添加坐标的时候插入这个列表，发送之前进行校验

	self.transmit_msg_list = {}						-- 喇叭消息列表

    self.msg_id_inc = 0
    self.old_time_stamp = 0
	self.channel_list = {}							-- 频道列表

	self.private_id_inc = 0							-- 私聊增长id
	self.private_obj_map = {}						-- 私聊对象map
	self.private_obj_list = {}						-- 私聊对象list
	self.private_unread_list = {}					-- 私聊未读列表

	self.team_unread_list = {}						-- 组队未读列表
	self.blacklist = {}

	self.chat_channel_size_list = {}				--记录不同频道item的高度
	self.ignore_chatfilter_word_list = {}
	self.cache_list = {}							-- 本地缓存数据
	self.cache_private = {}							-- 本地私聊缓存

	self.open_level_list = {}						-- 初始化等级
	self.open_vip_list = {}							-- 初始化vip限制
	self.chat_cd_list = {}							-- 初始化cd限制
	self.publick_chat_cd_list = {}					-- 初始化公告cd限制
	self.open_daily_liveness_list = {}				-- 初始化活跃度限制
	self.open_real_chongzhi_list = {}               -- 初始化真充限制
	self.is_limit_liveness = 0
	self.liveness_limit_level = 0

	self:Init()

	self.channel_unread_list = {}

	self.temp_world_list = {}						--世界缓存列表
	self.temp_system_list = {}						--系统缓存列表
	self.temp_guild_system_list = {}				--仙盟系统缓存列表

	local system_str_cfg = ConfigManager.Instance:GetAutoConfig("system_str_auto")
	self.client_link_cfg = system_str_cfg.client_link
	local other_config_auto = ConfigManager.Instance:GetAutoConfig("other_config_auto")
	self.chat_word_reward_cfg = ListToMap(other_config_auto.chat_key, "seq")
	local chat_auto_cfg = ConfigManager.Instance:GetAutoConfig("chat_auto")
	self.wordface_cfg = ListToMap(chat_auto_cfg.wordface, "id")
	self.normal_face_cfg = chat_auto_cfg.normal_face
	self.gif_face = chat_auto_cfg.gif_face
	self.gif_face_by_id = ListToMap(chat_auto_cfg.gif_face, "id")

	self.chat_other_cfg = chat_auto_cfg.other[1]

	local interaction_auto_cfg = ConfigManager.Instance:GetAutoConfig("interaction_auto")
	self.interaction_paiyipai = interaction_auto_cfg.paiyipai
	self.paiyipai_other_cfg = interaction_auto_cfg.other
	self.interaction_paiyipai_by_index = ListToMap(interaction_auto_cfg.paiyipai, "index")
	self.pyp_role_setting_info = {}			--拍一拍玩家自定义设置信息
	self.pyp_channel_cd_list = {}			--拍一拍各频道cd
	self.pyp_channel_guide_list = {}		--拍一拍各频道引导消息
	self.is_silent_chat = 0
	self.chat_word_reward_flag = {}			-- 聊天口令领取标志

	self.all_channel_aite_list = {}			--全频道艾特我消息列表
	self.all_aite_list = {}					--全部艾特我消息列表(不分频道类型,只分先后)最大50条
	self.aite_block_list = {}				--@艾特消息块(@玩家名)信息

	self.scene_chat_bubble_cache_list = {}	--场景聊天气泡框信息缓存
	self.scene_chat_bubble_show_num = 0

	self:RegisterRemind()
end

function ChatWGData:__delete()
	ChatWGData.Instance = nil

	self.ignore_chatfilter_word_list = {}
	self.channel_unread_list = {}
	self.last_input_text = nil
	self.pyp_role_setting_info = {}
	self.pyp_channel_cd_list = {}
	self.pyp_channel_guide_list = {}
	self.all_channel_aite_list = {}
	self.all_aite_list = {}
	self.aite_block_list = {}

	self.scene_chat_bubble_cache_list = {}
	self.scene_chat_bubble_show_num = nil

	self:UnRegisterRemind()
end

function ChatWGData:RegisterRemind()
	RemindManager.Instance:Register(RemindName.ChatChannelWorld, BindTool.Bind(self.IsShowChatAiteRemind, self, CHANNEL_TYPE.WORLD))
	RemindManager.Instance:Register(RemindName.ChatChannelTeam, BindTool.Bind(self.IsShowChatAiteRemind, self, CHANNEL_TYPE.TEAM))
	RemindManager.Instance:Register(RemindName.ChatChannelCross, BindTool.Bind(self.IsShowChatAiteRemind, self, CHANNEL_TYPE.CROSS))
	RemindManager.Instance:Register(RemindName.ChatChannelGuild, BindTool.Bind(self.IsShowChatAiteRemind, self, CHANNEL_TYPE.GUILD))
	-- RemindManager.Instance:Register(RemindName.ChatChannelZhanDui, BindTool.Bind(self.IsShowChatAiteRemind, self, CHANNEL_TYPE.ZHANDUI3V3))
	RemindManager.Instance:Register(RemindName.ChatChannelScene, BindTool.Bind(self.IsShowChatAiteRemind, self, CHANNEL_TYPE.SCENE))
end

function ChatWGData:UnRegisterRemind()
	RemindManager.Instance:UnRegister(RemindName.ChatChannelWorld)
	RemindManager.Instance:UnRegister(RemindName.ChatChannelTeam)
	RemindManager.Instance:UnRegister(RemindName.ChatChannelCross)
	RemindManager.Instance:UnRegister(RemindName.ChatChannelGuild)
	-- RemindManager.Instance:UnRegister(RemindName.ChatChannelZhanDui)
	RemindManager.Instance:UnRegister(RemindName.ChatChannelScene)
end

function ChatWGData:FireChatRemind()
	RemindManager.Instance:Fire(RemindName.ChatChannelWorld)
	RemindManager.Instance:Fire(RemindName.ChatChannelTeam)
	RemindManager.Instance:Fire(RemindName.ChatChannelCross)
	RemindManager.Instance:Fire(RemindName.ChatChannelGuild)
	-- RemindManager.Instance:Fire(RemindName.ChatChannelZhanDui)
	RemindManager.Instance:Fire(RemindName.ChatChannelScene)
	self:CheckMainChatPanelBubble()
	ChatWGCtrl.Instance:FlushChatViewCheckAiteTimer()
end

function ChatWGData:Init()
	for k, v in pairs(CHANNEL_TYPE) do
		if v ~= CHANNEL_TYPE.PRIVATE then
			self.channel_list[v] = ChatWGData.CreateChannel()
		end
		self.chat_channel_size_list[v] = {}
	end
end

function ChatWGData:GetMsgId()
	self.msg_id_inc = self.msg_id_inc + 1
	return self.msg_id_inc
end

function ChatWGData:GetAgentAdaptShieldCfg()
	self.shield_thesaurus_auto = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto").shield
	return self.shield_thesaurus_auto
end

function ChatWGData:GetIgnoreChatfilterList()
	if nil == next(self.ignore_chatfilter_word_list) then
		local cfg = self:GetAgentAdaptShieldCfg()
		for k, v in pairs(cfg) do
			table.insert(self.ignore_chatfilter_word_list, v.shield_item)
		end
	end
	return self.ignore_chatfilter_word_list
end


----------------------------------------------------
-- 频道begin
----------------------------------------------------
-- 创建频道
function ChatWGData.CreateChannel()
	return {
		--is_pingbi = false,							-- 是否屏蔽
		is_auto_voice = false,						-- 是否自动语音
		cd_end_time = 0,							-- CD结束时间
		unread_num = 0,								-- 未读数量
		msg_list = {},								-- 消息列表
	}
end

-- 创建消息
function ChatWGData.CreateMsgInfo()
	return {
		msg_id = 0,									-- 消息id
		from_uid = 0,								-- 发送者id
		username = "",								-- 发送者名字
		sex = 0,									-- 性别
		camp = 0,									-- 阵营
		prof = 0,									-- 职业
		authority_type = 0,							-- 权限类型，GM、新手指导员之类
		content_type = 0,							-- 内容类型
		tuhaojin_color = 0,							-- 发消息字体颜色(土豪金)
		bigchatface_status = 0,						-- 大表情
		dabiaoqing_flag = 0,						-- 特殊表情(需要单个激活的表情)
		level = 0,									-- 等级
		vip_level = 0,								-- vip等级
		channel_type = 0,							-- 频道类型
		send_time_str = "",							-- 发送时间
		content = "",								-- 消息内容
		city_name = "",								-- 城市
		country_name = "",							-- 国家名称（单字）
		chat_content_show_type = CHAT_CONTENT_SHOW_TYPE.CHAT,		--聊天内容展示样式类型，默认聊天框.
		fix_show_main = false,						-- 是否强制显示主界面上
		chat_cell_height = -1,						-- ChatWindow中ChatCell的高度
		shield_vip_flag = 0,						-- 隐藏VIP标识
	}
end

function ChatWGData:SetChannelItemHeight(channel_type, msg_id, height)
	local channel_list = self.chat_channel_size_list[channel_type]
	if channel_list then
		channel_list[msg_id] = height
	end
end

function ChatWGData:GetChannelItemHeight(channel_type, msg_id)
	local channel_list = self.chat_channel_size_list[channel_type]
	if channel_list then
		return channel_list[msg_id] or 0
	end
	return nil
end

-- 获取频道
function ChatWGData:GetChannel(channel_type)
	if not self.channel_list[channel_type] then
		self.channel_list[channel_type] = ChatWGData.CreateChannel()
	end
	return self.channel_list[channel_type]
end

function ChatWGData:RemoveAllChannel()
	for k, v in pairs(CHANNEL_TYPE) do
		if v ~= CHANNEL_TYPE.PRIVATE then
			self.channel_list[v] = ChatWGData.CreateChannel()
		end
		self.chat_channel_size_list[v] = {}
	end
end

function ChatWGData:RemoveMainUIChannel(channel_type)
    local channel_list = self:GetChannel(CHANNEL_TYPE.MAINUI)
    local msg_list = channel_list.msg_list
    if #msg_list < 0 then
        return
    end

    for i = #msg_list, 1, -1 do
        if msg_list[i].channel_type == channel_type then
            table.remove(msg_list, i)
        end
    end
end

-- 清空某个频道
function ChatWGData:RemoveGetChannel(channel_type)
	self.channel_list[channel_type] = ChatWGData.CreateChannel()
end

-- 获取CD结束时间
function ChatWGData:GetChannelCdEndTime(channel_type)
	if nil ~= self.channel_list[channel_type] then
		return self.channel_list[channel_type].cd_end_time,self.channel_list[channel_type].is_person_cd
	end
	return 0
end

-- 设置CD结束时间
function ChatWGData:SetChannelCdEndTime(channel_type)
	if nil ~= self.channel_list[channel_type] then
		local add_cd_time, is_person_cd = self:GetChatCdLimint(channel_type)
		self.channel_list[channel_type].cd_end_time = Status.NowTime + add_cd_time
		self.channel_list[channel_type].is_person_cd = is_person_cd
		return self.channel_list[channel_type].cd_end_time
	end
end

-- 获取CD是否结束时间
function ChatWGData:GetChannelCdIsEnd(channel_type)
	if nil ~= self.channel_list[channel_type] then
		return (self.channel_list[channel_type].cd_end_time - Status.NowTime) <= 0
	end
	return false
end


-- 添加频道消息
function ChatWGData:AddChannelMsg(msg_info)
	--附近(场景)类型消息拦截
	if not self:CheckSceneChannelCanAddMsg(msg_info) then
		return
	end

	msg_info.msg_id = self:GetMsgId()

	local channel_type = msg_info.channel_type
	if channel_type == CHANNEL_TYPE.SPEAKER then --or channel_type == CHANNEL_TYPE.CROSS
		channel_type = CHANNEL_TYPE.WORLD
	end

	--拍一拍指引累计
	self:SetPYPGuideInfo(msg_info.channel_type, msg_info.msg_reason)

	if self:IsPingBiChannel(channel_type) then
		return
	end

	--2019/10/16 喇叭消息能发到对应的频道
	--if msg_info.speaker_type == nil then
		local channel = self:GetChannel(channel_type)
		if nil ~= channel then
			self:SetBillionDRPTMsg(msg_info)
			self:InsertMsgToChannel(channel, msg_info)
		end
	--end

	--场景频道和公会不插入全部
	if channel_type ~= CHANNEL_TYPE.SCENE then --and channel_type ~= CHANNEL_TYPE.GUILD
		self:InsertMsgToChannel(self.channel_list[CHANNEL_TYPE.ALL], msg_info)
	end

	if msg_info.fix_show_main == true --fix_show_main:强制在主界面聊天上显示
		or (msg_info.msg_reason ~= CHAT_MSG_RESSON.TEAM_TIPS and channel_type ~= CHANNEL_TYPE.SYSTEM
			and channel_type ~= CHANNEL_TYPE.CHUAN_WEN and msg_info.msg_reason ~= CHAT_MSG_RESSON.GUILD_TIPS 
			and msg_info.msg_reason ~= CHAT_MSG_RESSON.HUDONG_PYP) then
		self:InsertMsgToChannel(self.channel_list[CHANNEL_TYPE.MAINUI], msg_info)
	end

	---[[
	--设置场景中聊天气泡框(仅附近频道发言显示,显示除拍一拍以外信息) --boss假消息不显示
	if msg_info.channel_type == CHANNEL_TYPE.SCENE and not msg_info.is_monster and msg_info.msg_reason ~= CHAT_MSG_RESSON.HUDONG_PYP then
		local check_role = ChatWGCtrl.Instance:CheckRoleInMyView(msg_info.from_uid)
		-- print_error("FFFF===== check_role ~= nil", check_role ~= nil)
		local content = ChatWGData.Instance:TryAnalysisAiteMsg(msg_info.content, true)
		if check_role ~= nil then
			self:AddSceneChatBubbleInfo(msg_info.from_uid, content)
		end
	end
	--]]
end

function ChatWGData:InsertMsgToAll(msg_info)
	local channel_type = msg_info.channel_type
	if self:IsPingBiChannel(channel_type) then
		return
	end
	self:InsertMsgToChannel(self.channel_list[CHANNEL_TYPE.ALL], msg_info)
end

-- 插入消息到频道
function ChatWGData:InsertMsgToChannel(channel, msg_info)
	table.insert(channel.msg_list, msg_info)
	channel.unread_num = math.min(channel.unread_num + 1, MAX_CHANNEL_MSG_NUM)
	if msg_info.channel_type == CHANNEL_TYPE.SYSTEM then
		if #channel.msg_list > MAX_SYS_CHANNEL_MSG_NUM then
			table.remove(channel.msg_list, 1)
		end
	elseif msg_info.channel_type == CHANNEL_TYPE.CHUAN_WEN then
		if #channel.msg_list > MAX_CHUANWEN_CHANNEL_MSG_NUM then
			table.remove(channel.msg_list, 1)
		end
	else
		if #channel.msg_list > MAX_CHANNEL_MSG_NUM then
			local remove_data = table.remove(channel.msg_list, 1)
			if remove_data then--移除信息时检测移除未读@消息
				self:CheckSendIsReadAiteMsg(remove_data)
			end
		end
    end

    if msg_info.channel_type == CHANNEL_TYPE.SYSTEM then
        local old_time_stamp = self:GetShowCacheTimeStamp()
        for k, v in pairs(channel.msg_list) do
            if not msg_info.is_show_time then
                msg_info.is_show_time = 0
                if old_time_stamp == 0 then
                    msg_info.is_show_time = 1
                    self:SaveShowCacheTimeStamp(msg_info.send_time_str)
                elseif old_time_stamp + 300 <= msg_info.send_time_str then --策划需求超过五分钟后显示时间
                    msg_info.is_show_time = 1
                    self:SaveShowCacheTimeStamp(msg_info.send_time_str)
                end
            end
        end
    end
	ChatWGCtrl.Instance:flushwindow()
end

--设置百亿补贴多人拼团聊天数据.
function ChatWGData:SetBillionDRPTMsg(msg_info)
	if msg_info.chat_content_show_type == CHAT_CONTENT_SHOW_TYPE.BILLION_DRPT then
		local item_seq, player_list, grade = self:GetBillionDRPTInfo(msg_info.content)
	
		local act_state, start_time, rest_time = BillionSubsidyWGData.Instance:GetDRPTItemIsOpenBySeq(item_seq)
		local time_tab = TimeUtil.Format2TableDHMS(start_time)
		local time_hour = time_tab.hour > 0 and time_tab.hour or 0
		local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeqAndGrade(item_seq, grade)
		local tab_name = drpt_cfg and drpt_cfg.tab_name
		-- local player_num = BillionSubsidyWGData.Instance:GetDRPTPlayerNumBySeq(item_seq)
		msg_info.content = string.format(Language.BillionSubsidy.DRPTChatText, time_hour, tab_name) .. msg_info.content

		for i = 1, DRPT_MAX_PLAYER_NUM do
			if player_list[i] then
				AvatarManager.Instance:SetAvatarKey(player_list[i].uid, player_list[i].avatar_key_big, player_list[i].avatar_key_small)
			end
		end
	end
end

--解析百亿补贴多人拼团参数.
function ChatWGData:GetBillionDRPTInfo(content)
	local grade = 0
	local item_seq = 0
	local show_day = 0
	local player_list = {}

	local temp_str = ""
	local i, j = string.find(content, "({.-})")
	temp_str = string.sub(content, i + 1, j - 1)

	local rule_list = Split(temp_str, ";")
	local param1 = rule_list[3]
	local param2 = rule_list[4]

	local split_list = string.split(param1, ",")			--uid,grade,seq,show_day
	if #split_list > 1 then
		grade = tonumber(split_list[2])
		item_seq = tonumber(split_list[3])
		show_day = tonumber(split_list[4])
	end

	local drpt_cfg = BillionSubsidyWGData.Instance:GetDRPTCfgBySeq(item_seq)

	local player_split_list = string.split(param2, "|")		--uid,sex,prof,avatar_key_big,avatar_key_small|uid,sex,prof,avatar_key_big,avatar_key_small
	for i = 1, DRPT_MAX_PLAYER_NUM do
		player_list[i] =	--默认空数据.
		{
			item_seq = item_seq,
			grade = grade,
			show_day = show_day,
			max_participate_num = drpt_cfg and drpt_cfg.max_participate_num or 0,
			uid = 0,
			sex = 0,
			prof = 0,
			avatar_key_big = 0,
			avatar_key_small = 0,
		}
		if player_split_list[i] then
			local split_str = string.split(player_split_list[i], ",")
			player_list[i].uid = tonumber(split_str[1])
			player_list[i].sex = tonumber(split_str[2])
			player_list[i].prof = tonumber(split_str[3])
			player_list[i].avatar_key_big = tonumber(split_str[4])
			player_list[i].avatar_key_small = tonumber(split_str[5])
		end
	end

	return item_seq, player_list, grade
end

--解析百亿补贴大额直购参数.
function ChatWGData:GetBillionDEZGInfo(content)
	local uid = 0
	local item_seq = 0
	local grade = 0

	local temp_str = ""
	local i, j = string.find(content, "({.-})")
	temp_str = string.sub(content, i + 1, j - 1)

	local rule_list = Split(temp_str, ";")
	local uid = rule_list[3]
	local item_seq = rule_list[4]
	local grade = rule_list[5]

	return uid, item_seq, grade
end

-- 用于记录上次显示时间的时间戳
function ChatWGData:GetShowCacheTimeStamp()
	return self.old_time_stamp or 0
end

function ChatWGData:SaveShowCacheTimeStamp(time)
	self.old_time_stamp = time
end

-- 是否屏蔽
function ChatWGData:IsPingBiChannel(channel_type)
	if nil ~= self.channel_list[channel_type] then
		return self.channel_list[channel_type].is_pingbi
	end

	return false
end
----------------------------------------------------
-- 频道end
----------------------------------------------------

----------------------------------------------------
-- 私聊begin
----------------------------------------------------
-- 创建私聊对象
function ChatWGData.CreatePrivateObj()
	return {
		role_id = 0,								-- 角色id
		from_cross_uuid = 0,								-- 角色id
		username = "",								-- 角色名字
		sex = 0,									-- 性别
		camp = 0,									-- 阵营
		prof = 0,									-- 职业
		authority_type = 0,							-- 权限类型，GM、新手指导员之类
		level = 0,									-- 等级
		vip_level = 0,								-- vip等级
		unread_num = 0,								-- 未读消息数量
		msg_list = {},								-- 消息列表
		rank = 0,
		shield_vip_flag = 0,						-- 隐藏VIP标识
	}
end

-- 添加私聊对象
function ChatWGData:AddPrivateObj(role_id, private_obj)
	-- print_error("----添加私聊对象----", private_obj.role_id)
	if role_id and nil == self.private_obj_map[role_id] then
		self.private_obj_map[role_id] = private_obj
		table.insert(self.private_obj_list, private_obj)
	end
end

-- 移除私聊对象
function ChatWGData:RemovePrivateObj(private_obj)
	-- print_error("----移除私聊对象----", private_obj.role_id)
	if nil ~= self.private_obj_map[private_obj.role_id] then
		self.private_obj_map[private_obj.role_id] = nil

		local index = self:GetPrivateIndex(private_obj.role_id)
		if index > 0 then
			table.remove(self.private_obj_list, index)
		end
	end
end

-- 根据索引移除私聊对象
function ChatWGData:RemovePrivateObjByIndex(index)
	local private_obj = self.private_obj_list[index]
	if nil ~= private_obj then
		table.remove(self.private_obj_list, index)
		self.private_obj_map[private_obj.role_id] = nil
	end
end

-- 获取私聊列表
function ChatWGData:GetPrivateObjList()
	return self.private_obj_list
end

-- 获取私聊对象数量
function ChatWGData:GetPrivateObjCount()
	return #self.private_obj_list
end

-- 根据索引获取私聊对象
function ChatWGData:GetPrivateObjByIndex(index)
	return self.private_obj_list[index]
end

-- 根据角色id获取私聊对象
function ChatWGData:GetPrivateObjByRoleId(role_id)
	return self.private_obj_map[role_id]
end

-- 获取私聊对象索引
function ChatWGData:GetPrivateIndex(role_id)
	for k, v in pairs(self.private_obj_list) do
		if role_id == v.role_id then
			return k
		end
	end

	return 0
end

-- 添加私聊消息
-- key 缓存标记
function ChatWGData:AddPrivateMsg(role_id, msg_info, key,is_read)	--是否读取缓存
	-- print_error("---添加私聊消息----", role_id, msg_info, key)
	self:AddCache(role_id, msg_info, key,is_read)
	self:AddPrivateCache(role_id, msg_info, key,is_read)

	--拍一拍指引累计
	self:SetPYPGuideInfo(msg_info.channel_type, msg_info.msg_reason)

	local private_obj = self.private_obj_map[role_id]
	if nil == private_obj then
		private_obj = ChatWGData.CreatePrivateObj()
		private_obj.role_id = ChatWGCtrl.Instance.main_role_id or msg_info.from_uid
		private_obj.from_cross_uuid = msg_info.from_uid
		private_obj.username = msg_info.username or msg_info.gamename or msg_info.role_name
		private_obj.sex = msg_info.sex
		private_obj.camp = msg_info.camp
		private_obj.prof = msg_info.prof
		private_obj.authority_type = msg_info.authority_type
		private_obj.level = msg_info.level
		private_obj.vip_level = msg_info.vip_level
		private_obj.city_name = msg_info.city_name
		self:AddPrivateObj(role_id, private_obj)
	end

	msg_info.msg_id = self:GetMsgId()
	table.insert(private_obj.msg_list, msg_info)
	private_obj.unread_num = private_obj.unread_num + 1
	if #private_obj.msg_list > MAX_PRESERVE_MSG_NUM then
		table.remove(private_obj.msg_list, 1)
	end

	self.private_window_bubble_type = msg_info.personalize_window_bubble_type
end

-- 未打开面板时，有留言时检测
function ChatWGData:CheckFirst(role_id)
	-- body
	self:ReadPrivateCache(role_id)
end

-- 缓存私聊，
function ChatWGData:AddPrivateCache(role_id, msg_info, key, is_read)
	if not role_id then
		return
	end

	key = key or CacheType.Private
	is_read = is_read or false

	local friend = SocietyWGData.Instance:FindFriend(role_id)
	local is_online = false
	if friend then
		is_online = ONLINE_TYPE.ONLINE_TYPE_ONLINE == friend.is_online or ONLINE_TYPE.ONLINE_TYPE_CROSS == friend.is_online
	else
		print_error("-----不是好友能发私聊------")
	end

	if key ~= CacheType.Private and (not is_online) then
		return
	end

	if not self.cache_private[role_id] then
		self.cache_private[role_id] = {}
	end

	if #self.cache_private[role_id] >= MAX_PRESERVE_MSG_NUM then
		table.remove(self.cache_private[role_id], 1)
	end

	table.insert(self.cache_private[role_id], msg_info)
	if not is_read then
		CacheManager.Instance:SetMsgCache(CacheType.Private, role_id, self.cache_private[role_id])
	end
end

function ChatWGData:ReadPrivateCache(role_id)
	if not self.cache_private or self.cache_private[role_id] then return end
	self.cache_private[role_id] = {}
	local data = CacheManager.Instance:GetMsgCache(CacheType.Private,role_id)
	if not data or IsEmptyTable(data) then return end

	for k,v in pairs(data) do
		if k ~= "role_id" then
			self:AddPrivateMsg(role_id, v, CacheType.Private, true)
		end
	end
end


function ChatWGData:GetCacheLenById(role_id)
	-- body
	if not self.cache_list[role_id] then
		self.cache_list[role_id] = {}
	end
	return #self.cache_list[role_id]
end

-- 玩家不在线时，留言缓存，上限50条
function ChatWGData:AddCache(role_id, msg_info, key,is_read)
	key = key or CacheType.Message
	is_read = is_read or false
	if key ~= CacheType.Message or role_id == nil then
		return
	end

	local friend = SocietyWGData.Instance:FindFriend(role_id)
	local is_online = false
	if friend then
		is_online = ONLINE_TYPE.ONLINE_TYPE_ONLINE == friend.is_online or ONLINE_TYPE.ONLINE_TYPE_CROSS == friend.is_online
	else
		print_error("-----不是好友能发私聊------")
	end

	if is_online then
		return
	end

	if not self.cache_list[role_id] then
		self.cache_list[role_id] = {}
	end

	if #self.cache_list[role_id] >= MAX_MSG_CACHE_NUM then
		return
	end

	table.insert(self.cache_list[role_id], msg_info)
	if not is_read then
		CacheManager.Instance:SetMsgCache(CacheType.Message, role_id, self.cache_list[role_id])
	end
end

-- 玩家上线时，需要删掉他的缓存
function ChatWGData:RemoveCache(role_id)
	if not IsEmptyTable(self.cache_list[role_id]) then
		self.cache_list[role_id] = {}
		CacheManager.Instance:SetMsgCache(CacheType.Message, role_id, self.cache_list[role_id])
	end
end

--好友删除时清理关于聊天的缓存
function ChatWGData:RemoveMsgCahce(role_id)
	-- print_error("--<color=#f97878> 好友删除时清理关于聊天的缓存 </color>--", role_id)
	CacheManager.Instance:ClearRoleMsgCache(CacheType.Message,role_id)
	CacheManager.Instance:ClearRoleMsgCache(CacheType.Private,role_id)
end

-- is_move_table 玩家上线时，留言移动到私聊列表
function ChatWGData:ReadMessage(role_id, is_move_table)
	if not is_move_table and self.cache_list[role_id] then return end
	self.cache_list[role_id] = {}
	local data = CacheManager.Instance:GetMsgCache(CacheType.Message,role_id)
	if not data or IsEmptyTable(data) then return end

	for k,v in pairs(data) do
		if k ~= "role_id" then
			if is_move_table then
				self:AddPrivateCache(role_id, v, CacheType.Private,true)
			else
				self:AddPrivateMsg(role_id, v, CacheType.Message,true)
			end
		end
	end
end

function ChatWGData:WriteCache(key, table)
	-- body
	local str = nil
	if table then
		str = TabToStr(table, true)
	end

	key = key .. GameVoManager.Instance:GetMainRoleVo().role_id
	PlayerPrefsUtil.SetString(key, str)
end

function ChatWGData:ReadCache(key)
	-- body
	key = key .. GameVoManager.Instance:GetMainRoleVo().role_id
	local str = PlayerPrefsUtil.GetString(key)
	if not str then return nil end
	local table = StrToTable(str)
	if not table then return nil end
	return table
end

function ChatWGData:GetPrivateBubbleType()
	return self.private_window_bubble_type
end

-- 私聊未读列表
function ChatWGData:GetPrivateUnreadList()
	return self.private_unread_list
end

-- 添加私聊未读消息
function ChatWGData:AddPrivateUnreadMsg(msg_info)
	table.insert(self.private_unread_list, msg_info)
end

-- 移除私聊未读消息
function ChatWGData:RemPrivateUnreadMsg(uid)
	-- local temp_tab = {}
	local is_flush = false
	for k,v in pairs(self.private_unread_list) do
		if v.from_uid == uid then
			-- table.insert(temp_tab, v)
			table.remove(self.private_unread_list,k)
			is_flush = true
		end
	end
	
	-- if self.last_read_private_uid ~= uid then
	-- self.private_unread_list = temp_tab
	if is_flush then
		SocietyWGData.Instance:SetListNeedRefreshActive(true)
		MainuiWGCtrl.Instance:SetFriendRemind()
		SocietyWGCtrl.Instance:Flush("friend_list")
		RemindManager.Instance:Fire(RemindName.SocietyFriends)
		RemindManager.Instance:Fire(RemindName.SocietyFriends2)
	-- end
		self.last_read_private_uid = uid
	end
end
--获取未读数量
function ChatWGData:GetPrivateUnreadMsgNum( uid )
	local count = 0
	for k,v in pairs(self.private_unread_list) do
		if v.from_uid == uid then
			count = count + 1
		end
	end
	return count
end




----------------------------------------------------
-- 私聊end
----------------------------------------------------

-- 组队未读信息列表
function ChatWGData:GetTeamUnreaList()
	return self.team_unread_list
end

-- 添加组队未读消息
function ChatWGData:AddTeamUnreadMsg(msg_info)
	table.insert(self.team_unread_list, msg_info)
end

-- 移除组队未读消息
function ChatWGData:RemTeamUnreadMsg()
	self.team_unread_list = {}
end

-- 添加喇叭消息
function ChatWGData:AddTransmitInfo(transmit_info)
	table.insert(self.transmit_msg_list, __TableCopy(transmit_info))
	if #self.transmit_msg_list > MAX_TRANSMIT_MSG_NUM then
		table.remove(self.transmit_msg_list, 1)
	end
end

-- 弹出第一个喇叭消息
function ChatWGData:PopTransmit()
	return table.remove(self.transmit_msg_list, 1)
end


function ChatWGData:SetLocation(privance, city_name)
	self.privance = privance
	self.city_name = city_name
end

function ChatWGData:Getlocation()
	return self.privance or "北京", self.city_name or "北京"
end

function ChatWGData:SetLocationIndex(province_index, city_index)
	self.province_index = province_index
	self.city_index = city_index
end
function ChatWGData:GetLocationIndex()
	return self.province_index,self.city_index
end
--向表情列表中插入表情
function ChatWGData:InsertFaceTab(face_id)
	if face_id >= 1 and face_id <= 40 or (face_id >= COMMON_CONSTS.BIGCHAT_TESHUFACE_ID_FIRST and face_id <= COMMON_CONSTS.BIGCHAT_TESHUFACE_ID_LAST) then
		table.insert(self.face_tab, "{face;".. face_id .."}")
	end
end

function ChatWGData:InsertItemTab(item_data, is_bag)
	local mark = ""
	local param_str = ""
	local config, item_type = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		mark = "eq"
		local param = item_data.param or {}
		local param_data_list = {}
		param_data_list[1] = "2"
		param_data_list[4] = param.star_level or 0
		param_data_list[5] = item_data.strengthen_level or 0
		if param.xianpin_type_list ~= nil then
			for k, v in ipairs(param.xianpin_type_list) do
				param_data_list[14 + k] = v
			end
		end

		for i = 1, 20 do
			local num = param_data_list[i] or 0
			param_str = param_str .. num .. ":"
		end
	else
		mark = "i"
	end
	table.insert(self.item_tab, "{" .. mark .. ";".. item_data.item_id .. ";" .. param_str ..";" .. item_data.is_bind .. "}")
end

function ChatWGData:InsertPointTab(is_cross_server)
	is_cross_server = is_cross_server or 0
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local main_role_vo = main_role:GetVo()
		local x, y = main_role:GetLogicPos()
		local plat_name = main_role_vo.cur_plat_name
		local server_id = main_role_vo.current_server_id
		local plat_type = main_role_vo.plat_type
		local scene_id = Scene.Instance:GetSceneId()
		local scene_name = Scene.Instance:GetSceneName()
		local point_tab = "{point;" .. scene_name .. ";" 
							.. x .. ";" .. y .. ";" 
							.. scene_id .. ";" 
							.. plat_name .. ";" 
							.. server_id .. ";" 
							.. plat_type .. ";" 
							.. is_cross_server .. "}"
		table.insert(self.point_tab, point_tab)
	end
end

function ChatWGData:InsertOpenLinkTab(link_type,params1,params2,params3)
	params1 = params1 or 0
	params2 = params2 or 0
	params3 = params3 or 0
	table.insert(self.openlink_tab, "{openLink;".. link_type .. ";" .. params1 .. ";" .. params2 .. ";" .. params3 .. "}")
end

function ChatWGData:CheckFaceAndItem(msg)
	local str = msg
	--格式化列表中的表情
	for i,v in ipairs(self.face_tab) do
		local params = self:GetSplitData(v)

		local i, j = 0, 0
		while true do
			local i2, j2 = string.find(str, "(%/[0-9][0-9][0-9])", j+1)
			if nil == i2 or nil == j2 then
				i, j = string.find(str, "(%/[0-9][0-9])", j+1)
			else
				i, j = i2, j2
			end
			if nil == i or nil == j then
				break
			elseif params[2] == string.sub(str, i+1, j) then
				local src = string.sub(str, i, j)

				str = string.gsub(str, src, v)
			end
		end
	end


	--大表情是000-050
	--小表情是100-150
	local i, j = 0, 0
	while true do
		--1-50
		--100-150
		--在str中从第j+1个位置开始查找 ij 起止
		local i, j = string.find(str, "(%/%d+)", j)

		if nil == i or nil == j then
			break
		else
			--字符串截取
			local num =  tonumber(string.sub(str, i + 1, j))
			-- if num >= 1 and num <= 99 then
			-- 	num = num + 99
			-- elseif num >= 100 and num <= 200 then
			-- 	num = num - 100
			-- end
			local src = string.sub(str, i, j)
			if num >= 0 and num <= 9 then
				str = string.gsub(str, src, "<size=52><sprite name=\"" .. string.format("%03d", num) .. "\"></size>",1)
			elseif num > 9 and num < 100 then
				str = string.gsub(str, src, "<size=52><sprite name=\"" .. string.format("%03d", num) .. "\"></size>",1)
			elseif num >= 100 and num <= 200 then
				str = string.gsub(str, src, "<size=52><sprite name=\"" .. num .. "\"></size>",1)
			else
				break
			end


		end
	end

	--格式化坐标列表中的数据
	for i,v in ipairs(self.point_tab) do
		local params = self:GetSplitData(v)
		local pos_scene = string.gsub(params[2], "%-", "%%%-")
		local match = pos_scene .. "%(" .. params[3] .. "," .. params[4] .. "," .. params[6] .. "," .. params[7] .. "%)"

		local i, j = 0, 0
		while true do
			i, j = string.find(str, match, j + 1)
			if nil == i or nil == j then
				break
			else
				local a, b = string.find(str, pos_scene, i)
				local src = string.sub(str, b + 1 , j - 1)
				str = string.gsub(str, pos_scene .. "%(" .. src .. "%)", v)
			end
		end
	end

	--格式化超链接(openLink)列表中的数据
	for i,v in ipairs(self.openlink_tab) do
		local params = self:GetSplitData(v)
		local i, j = 0, 0
		while true do
			i, j = string.find(str, "(%[.-%])", j + 1)
			if nil == i or nil == j then
				break
			elseif params[2] == string.sub(str, i + 1, j - 1) then
				local src = string.sub(str, i + 1, j - 1)
				src = string.gsub(src, "%)", "%%%)")
				src = string.gsub(src, "%(", "%%%(")
				str = string.gsub(str, "%[" .. src .. "%]", v)
			end
		end
	end

	--格式化物品列表中的物品
	for i,v in ipairs(self.item_tab) do
		local params = self:GetSplitData(v)
		local i, j = 0, 0
		while true do
			i, j = string.find(str, "(%[.-%])", j + 1)
			if nil == i or nil == j then
				break
			elseif ItemWGData.Instance:GetItemName(params[2] + 0) == string.sub(str, i + 1, j - 1) then
				local src = string.sub(str, i + 1, j - 1)
				src = string.gsub(src, "%)", "%%%)")
				src = string.gsub(src, "%(", "%%%(")
				str = string.gsub(str, "%[" .. src .. "%]", v)
			end
		end
	end
	-- local text_num = AdapterToLua:utf8FontCount(str)
	-- local nums = 0
	-- local text_str = ""
	-- local text = 1
	-- for i=1,text_num do
	-- 	local chars = tonumber(string.sub(str, i, i))
	-- 	if chars ~= nil then
	-- 		nums = nums + 1
	-- 	else
	-- 		nums = 0
	-- 	end
	-- 	text = string.sub(str, i, i)
	-- 	if nums > 4 and chars ~= nil then
	-- 		src = string.gsub(src, "i", "%%%)")
	-- 		text_str = text_str .. "*"
	-- 	else
	-- 		text_str = text_str .. text
	-- 	end

	-- end
	-- print_error(">>>>>>>>>1",str)
	return str
end

-- 格式化，过滤文本
function ChatWGData:FormattingMsg(msg, content_type)
	if content_type == CHAT_CONTENT_TYPE.AUDIO then
		return msg
	end
	msg = string.gsub(msg, "{", "(")
	msg = string.gsub(msg, "}", ")")
	-- 删除头尾空格
	msg = string.match(msg,"%s*(.-)%s*$")
	local str = self:CheckFaceAndItem(msg)
	return str
end

function ChatWGData:GetSplitData(value)
	local mark
	mark = string.gsub(value, "{", "")
	mark = string.gsub(mark, "}", "")

	return Split(mark, ";")
end

function ChatWGData:ClearInput()
	self.face_tab = {}
	self.item_tab = {}
	self.point_tab = {}
	self.openlink_tab = {}
end

-- 校验列表与输入框
function ChatWGData.ExamineListByEditText(msg, n)
	local lists =
	{
		ChatWGData.Instance.point_tab,
		ChatWGData.Instance.item_tab,
		ChatWGData.Instance.face_tab,
		ChatWGData.Instance.openlink_tab,
	}
	local list = lists[n]
	local str = msg
	local find_str = ""
	local i, j = 1, 1
	local appear_num = 0
	for k,v in pairs(list) do
		local find_arr = Split(v, ";")
		if #find_arr > 0 then
			if n == 1 then
				find_str = find_arr[2] .. "%(" .. find_arr[3] .. "," .. find_arr[4] .. "," .. find_arr[6] .. "," .. find_arr[7] .. "%)"
			elseif n == 2 then
				find_str = "%[" .. ItemWGData.Instance:GetItemName(find_arr[2] + 0) .. "%]"
			elseif n == 3 then
				find_str = "%/" .. find_arr[2]
			end
			find_str = string.gsub(find_str, "}", "")
			find_str = string.gsub(find_str, "{", "")
			find_str = string.gsub(find_str, "%-", "%%%-")
			if 2 == n then
				find_str = string.gsub(find_str, "%)", "%%%)")
				find_str = string.gsub(find_str, "%(", "%%%(")
			end

			i, j = string.find(str, find_str, j)
			if j == nil then
				table.remove(list, k)
			else
			local n = 0
			msg, n = string.gsub(msg, find_str, "")
			appear_num = appear_num + n
			end
		end
	end
	return appear_num
end

-- 检查文本内容
function ChatWGData.ExamineEditText(msg, n)
	local num = n > 0 and 1 or 0
	local boolean = true
	local max_arr = {CHAT_POS_MAX, CHAT_ITEM_MAX, CHAT_FACE_MAX}
	for i = 1, 3 do
		local appear_num = num + ChatWGData.ExamineListByEditText(msg, i)
		if appear_num > max_arr[i] then
			if n == 0 or n == i then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat["TipMax" .. i])
				boolean = false
			end
		end
	end
	return boolean
end

-- 聊天输入最大字符限制，超出直接截断
function ChatWGData.ExamineEditTextNum(edit, num, e_type)
	if e_type == "return" then
		local str = edit:getText()
		local text_num = AdapterToLua:utf8FontCount(str)
		if text_num > num then
			str = AdapterToLua:utf8TruncateByFontCount(str, num)
			edit:setText(str)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		end
	end
end

-- 名字输入最大字符限制，去除空格，超出直接截断
function ChatWGData.ExamineEditNameNum(edit, num, e_type)
	if e_type == "return" then
		local text = edit:getText()
		text = string.gsub(text, "%s", "")			-- 空白符
		text = string.gsub(text, "　", "")			-- 全角空格
		edit:setText(text)
		ChatWGData.ExamineEditTextNum(edit, num, e_type)
	end
end

-- 数字输入最大字符限制，去除空格，超出直接截断
function ChatWGData.ExamineEditNum(edit, num, e_type)
	if e_type == "return" then
		local text = edit:getText()
		text = string.gsub(text, "%s", "")			-- 空白符
		text = string.gsub(text, "　", "")			-- 全角空格
		local n = tonumber(text);
		if n then
			edit:setText(text)
			ChatWGData.ExamineEditTextNum(edit, num, e_type)
		else
			text = ""
			edit:setText(text)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToNum)
		end
	end
end

-- 检查频道规则
function ChatWGData.ExamineChannelRule(channel, msg_reason)
	--local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- print_error(main_role_vo.level,COMMON_CONSTS.CHAT_LEVEL_LIMIT)
	-- --if channel == CHANNEL_TYPE.WORLD then
	-- 	if main_role_vo.level < 20 then	--聊天等級是否足夠
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.CHAT_LEVEL_LIMIT))
	-- 		print_error("等級不夠，不可聊天")
	-- 		return false
	-- 	end
	-- --end
	if not msg_reason and not ChatWGData.Instance:CheckBottomMaskActive(channel) then
		--判断等级是否足够
		-- if channel == CHANNEL_TYPE.GUILD then
		-- 	if main_role_vo.level < COMMON_CONSTS.GUILD_CHAT_LEVEL_LIMIT then	--帮派聊天是判断是否有帮派
		-- 		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.GUILD_CHAT_LEVEL_LIMIT))
		-- 		return false
		-- 	end
		-- elseif main_role_vo.level < COMMON_CONSTS.CHAT_LEVEL_LIMIT then
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.CHAT_LEVEL_LIMIT))
		-- 	return false
		-- end
		return false
	end
	-- --帮派聊天是判断是否有帮派
	-- if channel == CHANNEL_TYPE.GUILD and RoleWGData.Instance.role_vo.guild_id <= 0 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
	-- 	return false
	-- end

	return true
end

-- 过滤消息，返回是否显示
function ChatWGData.FiltrationMsg(content)
	local is_show = true
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local find_pos = string.find(content, "{team;")
	if nil ~= find_pos then
		local team_element = string.sub(content, find_pos, string.len(content))
		team_element = string.gsub(team_element, "{", "")
		team_element = string.gsub(team_element, "}", "")
		local params = Split(team_element, ";")
		local team_index = tonumber(params[3])
		local team_lev = tonumber(params[4]) or 0

		if role_vo.level < team_lev then
			is_show = false
		end
	end
	return is_show
end

function ChatWGData:SetBlacklist(list)
	self.blacklist = list
end

function ChatWGData:GetBlacklist()
	return self.blacklist
end

function ChatWGData:InBlacklist(user_id, name)
	user_id = user_id or 0
	name = name or ""
	for k,v in pairs(self.blacklist) do
		if v.user_id == user_id or v.user_name == name then
			return true
		end
	end
	return false
end

function ChatWGData:GetWordFaceCfg()
	return self.wordface_cfg
end

function ChatWGData:GetNormalFaceCfg()
	return self.normal_face_cfg
end

function ChatWGData:GetGifFaceCfg()
	return self.gif_face
end

function ChatWGData:GetGifFaceByIndex(id)
	-- print_error("FFF===== ", self.gif_face_by_id[id])
	return self.gif_face_by_id and self.gif_face_by_id[id]
end

--聊天时 检测玩家输入的文本是否含VIP限制表情 true:可显示 false:不可显示
function ChatWGData:CheckCanShowStrGifFace(str)
	local chat_vip_limit = 6
	local my_vip_level = VipWGData.Instance:GetRoleVipLevel()
	if my_vip_level >= chat_vip_limit then
		return true
	end
	local temp_str = self:FormattingMsg(str)
	-- print_error("FFF==== temp_str", temp_str)
	return not self:CheckStringHasGifFace(temp_str)
end

function ChatWGData:CheckStringHasGifFace(str)
	if not str or str == "" then
		return false
	end
	local i, j = 0, 0
	local has_gif_face = false
	local num = 0
	local cicle_count = 0
	while true do
		--在str中从第j+1个位置开始查找 ij 起止
		i, j = string.find(str, "(%[[0-9][0-9][0-9]%])", j)
		-- print_error("FFF===== i, j", i, j)
		if nil == i or nil == j then
			break
		else
			--字符串截取
			num =  tonumber(string.sub(str, i + 1, j - 1))
			-- print_error("FFFFF==== string.sub(str, i + 1, j)", string.sub(str, i + 1, j - 1), num)
			if self:GetGifFaceByIndex(num) then
				has_gif_face = true
				break
			end
		end

		cicle_count = cicle_count + 1
		if cicle_count >= 20 then
			break
		end
	end
	-- print_error("cicle_count", cicle_count)
	return has_gif_face
end

function ChatWGData:SetIsChatBag(bo)
	self.is_chat_bag = bo
end

function ChatWGData:GetIsChatBag()
	return self.is_chat_bag
end

function ChatWGData:GetShowItemDataList()
	local show_data_list = {}

	--已经装备
	local equip_data_list = EquipWGData.Instance:GetDataList()
	if equip_data_list~=nil then
		for k,v in pairs(equip_data_list) do
			if v ~= nil and v.item_id > 0 then
				table.insert(show_data_list,v)
			end
		end
	end
	--法宝信息
	local fabao_info = EquipWGData.Instance:GetFabaoInfo()
	if fabao_info.fabao_id > 0 then
		local fabao_cfg = ItemWGData.Instance:GetItemConfig(fabao_info.fabao_id )
		local invalid_time = fabao_info.fabao_gain_time + fabao_cfg.time_length
		table.insert(show_data_list, {item_id = fabao_info.fabao_id, invalid_time = invalid_time, frombody = true})
	end

	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	local arr =imp_guard_info.item_wrapper
	for i=1,#arr do
		if arr[i].item_id > 0 then
		table.insert(show_data_list, {item_id = arr[i].item_id, invalid_time = arr[i].invalid_time, frombody = true})
		end
	end

	--背包数据
	local data_list = ItemWGData.Instance:GetBagItemDataList()--ChatWinData.Instance:GetFashionItem()
	for k,v in pairs(data_list) do
		if v ~= nil and v.item_id > 0 then
			table.insert(show_data_list,v)
		end
	end

	return show_data_list
end

--展示材料背包物品
function ChatWGData:GetShowStuffItemDataList()
	local show_data_list = {}
	--材料背包
	local data_list = ItemWGData.Instance:GetStuffStorgeItemData()
	for k,v in pairs(data_list) do
		if v ~= nil and v.item_id > 0 then
			table.insert(show_data_list, v)
		end
	end
	return show_data_list
end

-- 获取频道未读信息列表
function ChatWGData:GetChannelUnreaList(channel_type)
	return self.channel_unread_list[channel_type]
end

function ChatWGData:GetWordUnreaList()
	return self.channel_unread_list[CHANNEL_TYPE.WORLD]
end
function ChatWGData:GetTeamUnreaList()
	return self.channel_unread_list[CHANNEL_TYPE.TEAM]
end
function ChatWGData:GetGuildUnreaList()
	return self.channel_unread_list[CHANNEL_TYPE.GUILD]
end
function ChatWGData:GetCrossUnreaList()
	return self.channel_unread_list[CHANNEL_TYPE.CROSS]
end
function ChatWGData:GetZuduiUnreaList()
	return self.channel_unread_list[CHANNEL_TYPE.ZUDUI]
end

-- 添加频道未读消息
function ChatWGData:AddChannelUnreadMsg(msg_info, channel_type)
	if not self.channel_unread_list[channel_type] then
		self.channel_unread_list[channel_type] = {}
	end
	table.insert(self.channel_unread_list[channel_type], msg_info)
end

-- 移除频道未读消息
function ChatWGData:ClearChannelUnreadMsg(channel_type)
	if self.channel_unread_list and self.channel_unread_list[channel_type] then
		self.channel_unread_list[channel_type] = {}
	end
end

function ChatWGData:GetChannelTimeStamp()
	return self.time_stamp_list or {}
end
function ChatWGData:SetChannelTimeStamp(channel_type,time_stamp)
	if not self.time_stamp_list then
		self.time_stamp_list = {}
	end
	self.time_stamp_list[channel_type] = time_stamp
	-- ChatWGCtrl.Instance:SaveChannelInfo(self.time_stamp_list)
end

function ChatWGData:SetDefaultChannelInfo()
	self.time_stamp_list = {}
end

function ChatWGData:SetGuaJiAndCustomInfo(protocol)
	local guaji_info = protocol.guaji_info
	if guaji_info == '' then
		self:SetDefaultChannelInfo()
		return
	end
	local str = Split(guaji_info,"|")
	for k,v in pairs(str) do
		if v and v ~= "" then
			local info_str = Split(v,",")
			local channel_type = tonumber(info_str[1])
			local time_stamp = tonumber(info_str[2])
			self:SetChannelTimeStamp(channel_type,time_stamp)
		end
	end
end

function ChatWGData:GetIsShouldSendTimeStamp(channel_type)
	if not self.old_channel_msg_count_list[channel_type] then
		self.old_channel_msg_count_list[channel_type] = 0
	end
	if self.channel_list[channel_type] then
		local msg_list = self.channel_list[channel_type].msg_list
		return #msg_list > self.old_channel_msg_count_list[channel_type]
	end
end

function ChatWGData:SetPingBiList(list)
	self.pingbi_list = list
	for i=1,#self.pingbi_list do
		local channel = self:GetChannel(CustomEnumChatChannel[i - 1])
		channel.is_pingbi = self.pingbi_list[i] == 1 and true or false
	end
end

--添加传闻缓存数据
function ChatWGData:AddTempChuanWenList(msg_info)
	if #self.temp_system_list >= CacheMsgMaxCount then
		table.remove(self.temp_system_list, 1)
	end
	table.insert(self.temp_system_list, msg_info)
end

function ChatWGData:RemoveTempChuanWenList(key)
	table.remove(self.temp_system_list, key)
end

function ChatWGData:GetTempChuanWenList()
	return self.temp_system_list
end

--添加世界缓存数据
function ChatWGData:AddTempWorldList(msg_info)
	table.insert(self.temp_world_list, msg_info)
end

function ChatWGData:RemoveTempWorldList(key)
	table.remove(self.temp_world_list, key)
end

function ChatWGData:GetTempWorldList()
	return self.temp_world_list
end

--添加仙盟系统缓存数据
function ChatWGData:AddTempGuildSystemList(msg_info)
	table.insert(self.temp_guild_system_list, msg_info)
end

function ChatWGData:RemoveGuildSystemList(key)
	table.remove(self.temp_guild_system_list, key)
end

function ChatWGData:GetGuildSystemList()
	return self.temp_guild_system_list
end

function ChatWGData:ClearGuildSystemList()
	self.temp_guild_system_list = {}
end

--清理传闻缓存信息   有一些战场传闻太多，离开了场景还在发
function ChatWGData:ClearCacheChuangSystemList()
	self.temp_guild_system_list = {}
	self.temp_system_list = {}
end

--缓存上一次未发送的文字
function ChatWGData:CacheLastInputText(str)
	self.last_input_text = str
end

function ChatWGData:GetCacheInputText()
	return self.last_input_text or ""
end

-- 获取中英混合字符串
function ChatWGData:SubStringUTF8(str, startIndex, endIndex)
    if startIndex < 0 then
        startIndex = self:SubStringGetTotalIndex(str) + startIndex + 1
    end

    if endIndex ~= nil and endIndex < 0 then
        endIndex = self:SubStringGetTotalIndex(str) + endIndex + 1
    end

    if endIndex == nil then
        return string.sub(str, self:SubStringGetTrueIndex(str, startIndex))
    else
        return string.sub(str, self:SubStringGetTrueIndex(str, startIndex), self:SubStringGetTrueIndex(str, endIndex + 1) - 1)
    end
end

--获取中英混合UTF8字符串的真实字符数量
function ChatWGData:SubStringGetTotalIndex(str)
    local curIndex = 0
    local i = 1
    local lastCount = 1
    repeat
        lastCount = self:SubStringGetByteCount(str, i)
        i = i + lastCount
        curIndex = curIndex + 1
    until(lastCount == 0)
    return curIndex - 1
end

function ChatWGData:SubStringGetTrueIndex(str, index)
    local curIndex = 0
    local i = 1
    local lastCount = 1
    repeat
        lastCount = self:SubStringGetByteCount(str, i)
        i = i + lastCount
        curIndex = curIndex + 1
    until(curIndex >= index)
    return i - lastCount
end

--返回当前字符实际占用的字符数
function ChatWGData:SubStringGetByteCount(str, index)
    local curByte = string.byte(str, index)
    local byteCount = 1
    if curByte == nil then
        byteCount = 0
    elseif curByte > 0 and curByte <= 127 then
        byteCount = 1
    elseif curByte>= 192 and curByte<= 223 then
        byteCount = 2
    elseif curByte>= 224 and curByte<= 239 then
        byteCount = 3
    elseif curByte>= 240 and curByte<= 247 then
        byteCount = 4
    end
    return byteCount
end

function ChatWGData:GetOpenLinkCfg(link_type)
	return self.client_link_cfg and self.client_link_cfg[link_type]
end


-----------------------------------新聊天等级，聊天间隔限制start---------------------------------------

-- 设置聊天限制信息
function ChatWGData:SetChatAllInfo(protocol)
	self.open_level_list = protocol.open_level_list
	self.open_vip_list = protocol.open_vip_list
	self.chat_cd_list = protocol.chat_cd_list
	self.publick_chat_cd_list = protocol.publick_chat_cd_list

	self.is_limit_liveness = protocol.is_limit_liveness
	self.liveness_limit_level = protocol.liveness_limit_level
	self.open_real_chongzhi_list = protocol.open_real_chongzhi_list

	if self.is_limit_liveness == 1 then--运营动态设置活跃度聊天限制条件生效
		self.open_daily_liveness_list = protocol.open_daily_liveness_list
	end
end

-- 获取聊天限制等级
function ChatWGData:GetChatLevelLimint(chat_type)
	--策划需求：仙盟答题活动开启时，不满足发言添加 在答题中需要特殊处理
	if chat_type == CHANNEL_TYPE.GUILD then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.GUILD_ANSWER_FB then
			local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
			if act_open then
				return 1 ,self.open_vip_list[chat_type] or 0, 0
			end
		end
	end

	return self.open_level_list[chat_type] or 0, self.open_vip_list[chat_type] or 0 ,self.open_real_chongzhi_list[chat_type] or 0
end

-- 获取聊天限制等级,并且是否是单个cd仅限单个频道使用
function ChatWGData:GetChatCdLimint(chat_type)
	if self.chat_cd_list[chat_type] and self.chat_cd_list[chat_type] > 0 then
		return self.chat_cd_list[chat_type], true
	end

	if self.publick_chat_cd_list[chat_type] and self.publick_chat_cd_list[chat_type] > 0 then
		return self.publick_chat_cd_list[chat_type], false
	end
	if chat_type == CHANNEL_TYPE.ZUDUI then
		return 20, false
	else
		return 0, false
	end
end

function ChatWGData:CheckBottomMaskActive(curr_send_channel)
	--系统,组队,传闻  显示  请切换到其他频道聊天
	-- if self.curr_send_channel == CHANNEL_TYPE.SYSTEM or self.curr_send_channel == CHANNEL_TYPE.ZUDUI or self.curr_send_channel == CHANNEL_TYPE.CHUAN_WEN then
	-- 	return true
	-- end

	--优先判断跨服组
	if curr_send_channel == CHANNEL_TYPE.CROSS then
		if not CrossServerWGData.Instance:GetIsEnterCrossSeverStage() then
			return false
		end
	end

	--仙盟 请先加入一个仙盟
	if curr_send_channel == CHANNEL_TYPE.GUILD then
		if RoleWGData.Instance.role_vo.guild_id <= 0 then
			return false
		end
	end

	--队伍 请先加入一个队伍
	if curr_send_channel == CHANNEL_TYPE.TEAM then
		if 0 == SocietyWGData.Instance:GetIsInTeam() then
			return false
		end
	end

	--战队 请先加入一个战队
	if curr_send_channel == CHANNEL_TYPE.ZHANDUI3V3 then
		if not ZhanDuiWGData.Instance:GetIsInZhanDui() then
			return false
		end
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local real_chongzhi_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()
	local limint_level, limint_vip_level ,limint_real_chongzhi= self:GetChatLevelLimint(curr_send_channel)

	local is_open = true

	if role_level < limint_level then
		is_open = false
	end

	if limint_vip_level > 0 and ( vip_level < limint_vip_level) then
		return false
	end

	if real_chongzhi_rmb < limint_real_chongzhi then
		return false
	end

	if self:CheckChatDailyLivenessLimit(curr_send_channel) then--活跃度限制
		return false
	end

	return is_open
end

-----------------------------------新聊天等级，聊天间隔限制end---------------------------------------

-- 设置喇叭CD结束时间
function ChatWGData:SetLaBaCdEndTime()
	local cd_time, clear_cd = self:GetChatCdLimint(CHANNEL_TYPE.SPEAKER)
	self.laba_cd_end_time = Status.NowTime + cd_time
	self.is_clear_laba_cd = clear_cd
end

-- 获取喇叭CD结束时间,是否个人cd
function ChatWGData:GetLaBaCdEndTime()
	return self.laba_cd_end_time or Status.NowTime, self.is_clear_laba_cd
end

-- 获取喇叭是否开启
function ChatWGData:GetLaBaIsOpen()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local daily_liveness = BiZuoWGData.Instance:GetTotalExp()
	local real_chongzhi_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()
	local limint_level, limint_vip_level ,limint_real_chongzhi = self:GetChatLevelLimint(CHANNEL_TYPE.SPEAKER)
	--local vip_end = ChatWGData.Instance:GetVipIsEnd()

	--判断优先级: 1.VIP等级限制 2.开放等级限制 3.活跃度限制
	local is_open = true
	local str = ""

	if limint_vip_level > 0 and vip_level < limint_vip_level then
		is_open = false
		str = string.format(Language.Chat.CanPlayLaBa[2], limint_vip_level)
	elseif role_level < limint_level then
		is_open = false
		str = Language.Chat.CanPlayLaBa[1]
	elseif self:CheckChatDailyLivenessLimit(CHANNEL_TYPE.SPEAKER) then--活跃度限制
		is_open = false
		str = Language.Chat.DailyLivenessLimitChat
	elseif real_chongzhi_rmb < limint_real_chongzhi then
		is_open = false
		str = Language.Chat.CanPlayLaBa[3]
	end

	return is_open, str
end

function ChatWGData:GetChatDailyLivenessLimitInfo(channel_type)
	return self.is_limit_liveness, self.liveness_limit_level, self.open_daily_liveness_list[channel_type]
end

--is_chat_limit:是否活跃度限制发言
function ChatWGData:CheckChatDailyLivenessLimit(channel_type, show_tip)
	local is_limit, limit_level, limit_daily_liveness = self:GetChatDailyLivenessLimitInfo(channel_type)
	if is_limit ~= 1 then--限制条件不生效
		return false
	end

	limit_level = limit_level or 0--不满足等级限制,才判断活跃度
	limit_daily_liveness = limit_daily_liveness or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local daily_liveness = BiZuoWGData.Instance:GetTotalExp()
	local is_chat_limit = role_level < limit_level and limit_daily_liveness > 0 and daily_liveness < limit_daily_liveness

	if is_chat_limit and show_tip then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.DailyLivenessLimitChatFM, limit_level, limit_daily_liveness))
	end

	return is_chat_limit
end

--====================================拍一拍 start =========================

function ChatWGData:GetPaiYiPaiCfg()
	return self.interaction_paiyipai
end

function ChatWGData:GetPaiYiPaiOtherCfg()
	return self.paiyipai_other_cfg[1]
end

function ChatWGData:GetPaiYiPaiCfgByIndex(index)
	if not IsEmptyTable(self.interaction_paiyipai_by_index) and index then
		return self.interaction_paiyipai_by_index[index]
	end
	return nil
end

function ChatWGData:GetPaiYiPaiNameByIndex(index)
	local cfg = self:GetPaiYiPaiCfgByIndex(index)
	local str = Language.Chat.PYPDefName
	if cfg and cfg.touch_name then
		str = cfg.touch_name
	end
	return str
end

function ChatWGData:OnCachePYPRoleSettingInfo(protocol)
	local data = {}
	-- 0：失败 1：成功 2：查询 
	if protocol.ret_result ~= 2 then
		local show_str = protocol.ret_result == 1 and Language.Chat.PYPSettingSucc or  Language.Chat.PYPSettingUnSucc
		SysMsgWGCtrl.Instance:ErrorRemind(show_str)
	end
	
	data.action_index = protocol.action_index		-- 拍一拍类型索引 
	data.is_set_flag = protocol.is_set_flag		--  
	data.is_first = protocol.is_first
	data.msg_buff = protocol.msg_buff				-- 自定义内容
	self.pyp_role_setting_info = data
end

function ChatWGData:GetPYPRoleSettingInfo()
	return self.pyp_role_setting_info
end

--判断是否首次拍一拍,显示引导文本
function ChatWGData:GetIsFirstPYP()
	local is_first = false
	if self.pyp_role_setting_info then
		is_first = self.pyp_role_setting_info.is_first == 0
	end
	return is_first
end

--设置拍一拍各频道CD
function ChatWGData:SetPYPCDByChannelType(channel_type)
	if channel_type then
		self.pyp_channel_cd_list[channel_type] = Status.NowTime
	end
end

--获取拍一拍各频道CD
function ChatWGData:GetPYPCDByChannelType(channel_type)
	local is_can_pyp = true
	local cd_time = 0
	if channel_type and self.pyp_channel_cd_list[channel_type] then
		cd_time = Status.NowTime - self.pyp_channel_cd_list[channel_type]
		local other_cfg = self:GetPaiYiPaiOtherCfg()
		local cold_time = other_cfg and other_cfg.cd_time
		is_can_pyp = cd_time > (cold_time or 10)
		cd_time = cold_time - math.floor(cd_time)
	end
	-- print_error("FFF====== is_can_pyp, cd_time", is_can_pyp, cd_time)
	return is_can_pyp, cd_time
end

function ChatWGData:GetPYPIsFunOpened()
	local is_open = false
	local limit_level = 120
	local other_cfg = self:GetPaiYiPaiOtherCfg()
	if other_cfg then
		local role_level = Scene.Instance:GetMainRole().vo.level
		limit_level = other_cfg.open_level or limit_level
		is_open = role_level >= limit_level
	end

	return is_open, limit_level
end

-- 是否屏蔽拍一拍
function ChatWGData:GetIsShieldPYP()
	local other_cfg = self:GetPaiYiPaiOtherCfg()
	return other_cfg.is_shield == 1
end

--设置拍一拍是否屏蔽
function ChatWGData:SetPYPIsPingbi(is_pingbi)
	local save_flag = is_pingbi and 1 or 0
	local player_uid = RoleWGData.Instance:InCrossGetOriginUid()
	PlayerPrefsUtil.SetInt(player_uid .. "paiyipai_show_key", save_flag)
end

function ChatWGData:GetPYPIsPingbi()
	local player_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local save_flag = PlayerPrefsUtil.GetInt(player_uid .. "paiyipai_show_key")
	return save_flag == 1
end

--设置拍一拍各频道屏蔽引导信息
--last_timestamp:上一次记录时间戳------策划又说不要了,先屏蔽
--msg_count:累计信息数量
function ChatWGData:SetPYPGuideInfo(channel_type, msg_reason)
	if msg_reason ~= CHAT_MSG_RESSON.HUDONG_PYP then
		self:ResetSetPYPGuideInfo(channel_type)
		return
	end
	if channel_type and self.pyp_channel_guide_list then
		if not self.pyp_channel_guide_list[channel_type] then
			self.pyp_channel_guide_list[channel_type] = {}
		end
		-- if not self.pyp_channel_guide_list[channel_type].last_timestamp then
		-- 	self.pyp_channel_guide_list[channel_type].last_timestamp = Status.NowTime
		-- end
		if not self.pyp_channel_guide_list[channel_type].msg_count then
			self.pyp_channel_guide_list[channel_type].msg_count = 0
		end
		self.pyp_channel_guide_list[channel_type].msg_count = self.pyp_channel_guide_list[channel_type].msg_count + 1
	end
end

--获取拍一拍各频道是否显示屏蔽引导信息
--1分钟内,同频道拍一拍信息达到10条,进行屏蔽引导
function ChatWGData:CheckPYPPingBiGuide(channel_type)
	local is_guide_pingbi = false
	if self.pyp_channel_guide_list and self.pyp_channel_guide_list[channel_type] then
		-- local last_timestamp = self.pyp_channel_guide_list[channel_type].last_timestamp or 0
		local cur_msg_count = self.pyp_channel_guide_list[channel_type].msg_count or 0
		-- local interval_s = Status.NowTime - last_timestamp--间隔秒数
		local max_msg_count, max_interval_s = self:GetPYPGuideResetCfg()

		-- if interval_s > max_interval_s then--时间超时
		-- 	is_guide_pingbi = false
		-- 	self:ResetSetPYPGuideInfo(channel_type)
		-- else--规定时间内,判断信息数量是否达标
		-- 	if cur_msg_count >= max_msg_count then--进行引导
		-- 		is_guide_pingbi = true
		-- 		self:ResetSetPYPGuideInfo(channel_type)
		-- 	else
		-- 		is_guide_pingbi = false
		-- 	end
		-- end

		if cur_msg_count > max_msg_count then--进行引导
			is_guide_pingbi = true
			self:ResetSetPYPGuideInfo(channel_type)
		end
	end
	return is_guide_pingbi
end

function ChatWGData:ResetSetPYPGuideInfo(channel_type)
	if channel_type and self.pyp_channel_guide_list and self.pyp_channel_guide_list[channel_type] then
		-- self.pyp_channel_guide_list[channel_type].last_timestamp = nil
		self.pyp_channel_guide_list[channel_type].msg_count = 0
	end
end

--10条信息,60秒间隔
function ChatWGData:GetPYPGuideResetCfg()
	return 10, 60
end

function ChatWGData:AddPYPGuideMsgInfo(msg_info, pyp_guide_type)
	local temp_info = {}
	if not IsEmptyTable(msg_info) then
		for k, v in pairs(msg_info) do
			temp_info[k] = v
		end
		temp_info.pyp_guide_type = pyp_guide_type
	end
	return temp_info
end

function ChatWGData:GetPYPShowStr(pyp_msg_info)
	local pyp_show_str = ""
	--我拍别人
	local is_i_touch = RoleWGData.Instance:InCrossGetOriginUid() == pyp_msg_info.from_uid
	--别人拍我
	local is_touch_me = RoleWGData.Instance:InCrossGetOriginUid() == pyp_msg_info.target_role_id
	--拍一拍显示类型
	local pyp_name = ChatWGData.Instance:GetPaiYiPaiNameByIndex(pyp_msg_info.pyp_index or 0)
	--是否显示玩家自定义内容
	local is_player_custom = pyp_msg_info.is_set_flag ~= 0 and pyp_msg_info.msg_buff ~= ""
	--服务器名称
	local send_server_name, target_server_name = "", ""
	-- if IS_ON_CROSSSERVER and pyp_msg_info.channel_type == CHANNEL_TYPE.SCENE then
	-- 	send_server_name = string.format(Language.Chat.PYPServerName, pyp_msg_info.from_role_server_id)
	-- 	target_server_name = string.format(Language.Chat.PYPServerName, pyp_msg_info.target_role_server_id)
	-- end

	if is_player_custom then
		if is_i_touch then
			pyp_show_str = string.format(Language.Chat.CustomITouchOther, pyp_name, pyp_msg_info.target_role, target_server_name, pyp_msg_info.msg_buff)
		elseif is_touch_me then
			pyp_show_str = string.format(Language.Chat.CustomOtherTouchMe, pyp_msg_info.username, send_server_name, pyp_name, pyp_msg_info.msg_buff)
		else
			pyp_show_str = string.format(Language.Chat.CustomOtherTouchOther, pyp_msg_info.username, send_server_name, pyp_name, pyp_msg_info.target_role, target_server_name, pyp_msg_info.msg_buff)
		end
	else
		if is_i_touch then
			pyp_show_str = string.format(Language.Chat.ITouchOther, pyp_name, pyp_msg_info.target_role, target_server_name)
		elseif is_touch_me then
			pyp_show_str = string.format(Language.Chat.OtherTouchMe, pyp_msg_info.username, send_server_name, pyp_name)
		else
			pyp_show_str = string.format(Language.Chat.OtherTouchOther, pyp_msg_info.username, send_server_name, pyp_name, pyp_msg_info.target_role, target_server_name)
		end
	end

	local is_about_me = is_i_touch or is_touch_me
	local color = "#aaadb5"
	if is_about_me then
		color = pyp_msg_info.channel_type == CHANNEL_TYPE.PRIVATE and COLOR3B.GREEN or COLOR3B.D_GREEN
	end
	local color_str = "<color=%s>%s</color>"
	pyp_show_str = string.format(color_str, color, pyp_show_str)
	-- print_error("FFF===== pyp_show_str", pyp_show_str, color)
	return pyp_show_str
end
--====================================拍一拍 end =========================

--检测字符串是否包含空格符号
function ChatWGData:CheckStringHadBlank(text)
	local len = string.len(text)
	local qukong_text = string.gsub(text, "%s", "")
	local qukong_text_len = string.len(qukong_text)
	if qukong_text_len ~= len then
		return true
	end
	return false
end

function ChatWGData:OnSCChatUserInfo(protocol)
	self.is_silent_chat = protocol.is_silent_chat or 0
end

function ChatWGData:GetIsInSilent()
	return self.is_silent_chat == 1
end

--=====================================@ 艾特操作 start
function ChatWGData:OnSCChatCalledList(protocol)
	self.all_aite_list = {}
	self.all_channel_aite_list = {}
	-- print_error("FFF==== protocol.call_list", protocol.call_list)
	for i, v in ipairs(protocol.call_list) do
		self.all_aite_list[i] = v

		if not self.all_channel_aite_list[v.channel_type] then
			self.all_channel_aite_list[v.channel_type] = {}
		end
		table.insert(self.all_channel_aite_list[v.channel_type], v)
	end

	self:CheckClearNotExistAiteInfo()
	self:CheckAndClearAiteCache()
	self:FireChatRemind()
end

function ChatWGData:OnSCSingleChatCallInfo(protocol)
	local info = protocol.call_info
	if IsEmptyTable(info) then
		return
	end

	table.insert(self.all_aite_list, info)

	if not self.all_channel_aite_list[info.channel_type] then
		self.all_channel_aite_list[info.channel_type] = {}
	end
	table.insert(self.all_channel_aite_list[info.channel_type], info)

	self:CheckAndClearAiteCache()
	self:FireChatRemind()
end

--检测移除艾特信息超过最大缓存
function ChatWGData:CheckAndClearAiteCache()
	local count = #self.all_aite_list - CHAT_AITE_CACHE_MAX_NUM
	if count > 0 then
		for i = 1, count do
			local remove_info = table.remove(self.all_aite_list, 1)
			if remove_info then
				self:RemoveChannelAiteInfo(remove_info.channel_type, 1)
			end
		end
	end
end

function ChatWGData:RemoveAiteInfo(remove_idx)
	if remove_idx and self.all_aite_list then
		-- print_error("FFFF===== 移除 全部@信息 remove_idx", remove_idx)
		table.remove(self.all_aite_list, remove_idx)
	end
	self:FireChatRemind()
end

function ChatWGData:RemoveChannelAiteInfo(channel_type, remove_idx)
	if channel_type and remove_idx and self.all_channel_aite_list[channel_type] then
		-- print_error("FFFF===== 移除 频道@信息 channel_type, remove_idx", channel_type, remove_idx)
		table.remove(self.all_channel_aite_list[channel_type], remove_idx)
	end
	self:FireChatRemind()
end

function ChatWGData:GetAllChannelAiteInfo()
	return self.all_channel_aite_list
end

function ChatWGData:GetAiteInfoByChannel(channel_type)
	return self.all_channel_aite_list[channel_type]
end

function ChatWGData:ClearAiteInfoByChannel(channel_type)
	if channel_type and not IsEmptyTable(self.all_channel_aite_list) then
		self.all_channel_aite_list[channel_type] = {}
	end

	local temp_list = {}
	if not IsEmptyTable(self.all_aite_list) then
		for i, v in ipairs(self.all_aite_list) do
			if v.channel_type ~= channel_type then
				temp_list[i] = v
			end
		end
	end
	self.all_aite_list = temp_list
	self:FireChatRemind()
end

--设置 艾特玩家整体字符块信息
function ChatWGData:SetAiTeBlockList(aite_data, block_str)
	aite_data.block_str = block_str
	-- print_error("FFF==== block_str", block_str)
	table.insert(self.aite_block_list, aite_data)
end

function ChatWGData:SetNewAiTeBlockList(new_list)
	self.aite_block_list = new_list
end

--重置 艾特玩家整体字符块信息
function ChatWGData:ReSetAiTeBlockList()
	self.aite_block_list = {}
end

--检测单次聊天是否已经艾特同一玩家
function ChatWGData:CheckAiTeBlockIsExist(block_str)
	if not IsEmptyTable(self.aite_block_list) then
		for k, v in pairs(self.aite_block_list) do
			if v.block_str == block_str then
				return true
			end
		end
	end
	return false
end

--检测聊天内容是否包含艾特玩家信息
function ChatWGData:CheckChatMagHasAiTeInfo()
	return not IsEmptyTable(self.aite_block_list)
end

function ChatWGData:GetChatMagHasAiTeInfo()
	return self.aite_block_list
end

function ChatWGData:GetSendAiteInfo(msg_info, aite_msg_type)
	local send_aite_data = {
		oper_type = aite_msg_type,
		channel_type = msg_info.channel_type,
		timestamp = 0,
		server_id = msg_info.merge_server_id,
		plat_type = msg_info.origin_plat_type,
		role_uid = msg_info.from_uid,
	}
	return send_aite_data
end

--转换@信息标记
function ChatWGData:ConvertToAiTeMsg(msg_content)
	local temp_content = msg_content
	if self:CheckChatMagHasAiTeInfo() then
		-- 信息尾加上@标记:发送时的时间戳
		local time_stamp = math.floor(TimeWGCtrl.Instance:GetServerTime())
		local aite_color, aite_name
		-- 向服务器请求@玩家
		for i, v in ipairs(self.aite_block_list) do
			--设置玩家名称特殊颜色
			aite_name = string.gsub(v.block_str, " ", "")--先把空格符号去掉,否则内容仅为@玩家,匹配不上
			aite_color = string.format(AITE_STRING_CONST.Color, aite_name)
			temp_content = string.gsub(temp_content, aite_name, aite_color)

			v.timestamp = time_stamp
			ChatWGCtrl.Instance:ChatAiTeSomeone(v)
		end
		temp_content = string.format(AITE_STRING_CONST.Format, temp_content, time_stamp)
	end
	return temp_content
end

--解析文本中@标识信息
function ChatWGData:TryAnalysisAiteMsg(msg_content, is_main_ui)
	local temp_content_tb = Split(msg_content, AITE_STRING_CONST.Start)
	-- print_error("FFF=====temp_content_tb", temp_content_tb)
	local temp_content = temp_content_tb[1] or msg_content
	if is_main_ui then
		temp_content = string.gsub(temp_content, AITE_STRING_CONST.FindColor, AITE_STRING_CONST.MainUiColor)
	end
	return temp_content
end

--检测文本中@标识信息,并发送消息已读给服务器
function ChatWGData:CheckSendIsReadAiteMsg(msg_data)
	if IsEmptyTable(msg_data) then
		return
	end
	local read_data = self:GetSendAiteInfo(msg_data, AITE_MSG_TYPE.Read)
	local timestamp = self:GetAiteMsgTime(msg_data.content)
	if timestamp > 0 then--@消息存在
		--发送消息已读给服务器
		if not IsEmptyTable(read_data) then
			read_data.timestamp = timestamp
			self:TryToSendReadAiteMsg(read_data)
		end
	end
end

--截取文本中@信息的时间戳
function ChatWGData:GetAiteMsgTime(msg_content)
	local timestamp = -1
	local _, start_idx = string.find(msg_content, AITE_STRING_CONST.Start)
	local end_idx, _ = string.find(msg_content, AITE_STRING_CONST.End)
	if start_idx and end_idx then
		timestamp = tonumber(string.sub(msg_content, start_idx + 1, end_idx - 1))
	end
	return timestamp
end

function ChatWGData:TryToSendReadAiteMsg(read_data)
	local is_can_send = false
	--剔除艾特信息缓存
	-- print_error("FFF=== read_data", read_data)
	-- print_error("FFF=== self.all_aite_list", self.all_aite_list)
	for k, v in pairs(self.all_aite_list) do
		if v.timestamp == read_data.timestamp and v.channel_type == read_data.channel_type 
			and v.server_id == read_data.server_id and v.plat_type == read_data.plat_type
			and v.uid == read_data.role_uid then
			is_can_send = true
			self:RemoveAiteInfo(k)
			break
		end
	end

	local channel_aite_info = self.all_channel_aite_list[read_data.channel_type]
	-- print_error("FFF=== channel_aite_info", channel_aite_info)
	if channel_aite_info then
		for k, v in pairs(channel_aite_info) do
			if v.timestamp == read_data.timestamp and v.channel_type == read_data.channel_type 
				and v.server_id == read_data.server_id and v.plat_type == read_data.plat_type
				and v.uid == read_data.role_uid then
				is_can_send = true
				self:RemoveChannelAiteInfo(read_data.channel_type, k)
				break
			end
		end
	end
	if is_can_send then--本地缓存信息剔除成功,则告知服务端已读
		ChatWGCtrl.Instance:ChatAiTeSomeone(read_data)
	end
end

function ChatWGData:IsShowChatAiteRemind(channel_type)
	if channel_type and not IsEmptyTable(self.all_channel_aite_list[channel_type]) then
		return 1
	end
	return 0
end

function ChatWGData:CheckAllChatRemind()
	if not IsEmptyTable(self.all_aite_list) then
		return 1
	end
	return 0
end

function ChatWGData:CheckMainChatPanelBubble()
	local num_flag = self:CheckAllChatRemind()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.CHAT_AITE_ME, num_flag, function ()
		local open_type
		for i, v in ipairs(CanAiteChannel) do
			if 1 == self:IsShowChatAiteRemind(v) then
				open_type = v
				break
			end
		end
		if open_type then
			--点击打开聊天
			ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndex[open_type])
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NotAiTeMsg)
		end
	end)
end

--登陆时检测清除不存在的艾特信息
--(被艾特方有艾特未读,但在其下线后该消息已被刷出200条以外的情况)
function ChatWGData:CheckClearNotExistAiteInfo()
	if IsEmptyTable(self.all_channel_aite_list) then
		return--无艾特消息,或者消息未下发
	end
	
	local read_data, channel_list, first_msg
	for channel_type, aite_list in pairs(self.all_channel_aite_list) do
		channel_list = self:GetChannel(channel_type).msg_list
		first_msg = channel_list and channel_list[1]
		if first_msg and first_msg.msg_timestamp and first_msg.msg_timestamp > 0 then
			--跟据时间戳判定艾特消息是否存在
			for k, v in pairs(aite_list) do
				if v.timestamp < first_msg.msg_timestamp then
					read_data = {}
					read_data.oper_type = AITE_MSG_TYPE.Read
					read_data.timestamp = v.timestamp
					read_data.channel_type = v.channel_type
					read_data.server_id = v.server_id
					read_data.plat_type = v.plat_type
					read_data.role_uid = v.uid
					self:TryToSendReadAiteMsg(read_data)--告知服务器去除@消息
				end
			end
		end
	end
end
--=====================================@ 艾特操作 end

---[[附近(场景)频道 ============start=============
function ChatWGData:SetCacheSceneChannelKey(key)
	self.chat_scene_channel_key = key
end

function ChatWGData:GetCacheSceneChannelKey()
	return self.chat_scene_channel_key
end

function ChatWGData:GetCurSceneChannelKey()
	local cur_scene_id = Scene.Instance:GetSceneId() or 1-- 当前场景id
	local cur_scene_key = RoleWGData.Instance:GetAttr("scene_key") or 1-- 当前场景key
	local cur_plat_type = RoleWGData.Instance:GetCurPlatType() or 1-- 当前所在平台id
	local cur_server_id = RoleWGData.Instance:GetCurServerId() or 1-- 当前所在服id
	local scene_channel_key = (cur_scene_id * 100000) + (cur_scene_key * 10000) + (cur_plat_type * 1000) + cur_server_id
	return scene_channel_key
end

--检测是否能添加进当前 附近(场景)聊天记录
function ChatWGData:CheckSceneChannelCanAddMsg(msg_info)
	if IsEmptyTable(msg_info) then
		return false
	end

	if msg_info.channel_type ~= CHANNEL_TYPE.SCENE then--不是附近(场景)类型不拦截
		-- print_error("FFFFF==== 不是附近(场景)类型不拦截", msg_info.channel_type)
		return true
	end

	local cur_scene_id = Scene.Instance:GetSceneId()-- 当前场景id
	local cur_scene_key = RoleWGData.Instance:GetAttr("scene_key")-- 当前场景key
	local cur_plat_type = RoleWGData.Instance:GetCurPlatType()-- 当前所在平台id
	local cur_server_id = RoleWGData.Instance:GetCurServerId()-- 当前所在服id
	--[[
	local test_str = "场景id对比: %s_%s  场景key对比: %s_%s  平台类型对比: %s_%s  服务器对比: %s_%s"
	local test_str_1 = string.format(test_str, msg_info.scene_id, cur_scene_id, msg_info.scene_key, cur_scene_key, msg_info.cur_plat_type, cur_plat_type, msg_info.cur_server_id, cur_server_id)
	print_error(test_str_1)
	--]]
	if msg_info.scene_id ~= cur_scene_id or msg_info.scene_key ~= cur_scene_key
		or msg_info.cur_plat_type ~= cur_plat_type or msg_info.cur_server_id ~= cur_server_id then
		-- print_error("FFF===== 触发拦截 附近(场景)类型消息")
		return false
	end
	-- print_error("FFF===== 添加 附近(场景)类型消息")
	return true
end

function ChatWGData:AddSceneChatBubbleInfo(role_id, content)
	if not role_id or not content then
		return
	end
	-- print_error("FFF===== 添加一条缓存")
	local temp_t = {}
	temp_t.role_id = role_id
	temp_t.content = content
	table.insert(self.scene_chat_bubble_cache_list, temp_t)
	ChatWGCtrl.Instance:TryShowNextSceneRoleChatBubble()
end

function ChatWGData:GetSceneChatBubbleInfo()
	return self.scene_chat_bubble_cache_list or {}
end

function ChatWGData:DelSceneChatBubbleFirstInfo()
	local temp_t = self.scene_chat_bubble_cache_list
	if not IsEmptyTable(temp_t) then
		-- print_error("FFF===== 删除一条缓存")
		table.remove(temp_t, 1)
		self.scene_chat_bubble_cache_list = temp_t
	end
end

function ChatWGData:ChangeSceneChatBubbleShowNum(change_num)
	local temp_num = self:GetSceneChatBubbleShowNum()
	temp_num = temp_num + change_num
	self.scene_chat_bubble_show_num = temp_num
end

function ChatWGData:GetSceneChatBubbleShowNum()
	local temp_num = self.scene_chat_bubble_show_num or 0
	temp_num = temp_num < 0 and 0 or temp_num
	return temp_num
end

--切换场景,重置信息
function ChatWGData:ResetSceneChatBubbleInfo()
	-- print_error(self.scene_chat_bubble_show_num, self.scene_chat_bubble_cache_list)
	self.scene_chat_bubble_show_num = 0
	self.scene_chat_bubble_cache_list = {}
end

--]]附近(场景)频道 ==============end=============

--可在跨服频道下分享坐标的场景
function ChatWGData:CanSharePosInCrossChannel()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.Common then
        if Scene.Instance:GetSceneId() == 1003 then
            return true
        end
    --深渊，蛮荒神兽，寻宝boss
    elseif scene_type == SceneType.Shenyuan_boss or scene_type == SceneType.KF_BOSS or 
    scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS or scene_type == SceneType.XianJie_Boss then
        return true
    end
    return false
end

--是否开启跨服频道
function ChatWGData:IsShowCrossSharePosBtn()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local real_chongzhi_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()
	local limint_level, limint_vip_level, limint_real_chongzhi = ChatWGData.Instance:GetChatLevelLimint(CHANNEL_TYPE.CROSS)
	local is_open = false

	if role_level >= limint_level and (limint_vip_level > 0 and vip_level >= limint_vip_level )
		 and real_chongzhi_rmb >= limint_real_chongzhi then
		is_open = true
	end

    if is_open then
		return CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
    end

    return false
end

function ChatWGData:GetForbidChatChanel()
	return {CHANNEL_TYPE.WORLD,
			CHANNEL_TYPE.ZUDUI,
			CHANNEL_TYPE.TEAM,
			CHANNEL_TYPE.GUILD,
			CHANNEL_TYPE.SCENE,
			CHANNEL_TYPE.CROSS,
			CHANNEL_TYPE.MAINUI,}
end

function ChatWGData:IsPHPVipLimitChat(channel_type)
	local php_vip_limit = GLOBAL_CONFIG.param_list.all_chat_vip_level or 0
	local php_role_level_limit = GLOBAL_CONFIG.param_list.all_chat_role_level or 0
	local role_level = RoleWGData.Instance:GetAttr('level')
	local vip_level = VipWGData.Instance:GetRoleVipLevel()

	if channel_type and channel_type == CHANNEL_TYPE.GUILD then
		return (php_vip_limit > vip_level and GLOBAL_CONFIG.param_list.guild_chat_need_vip_limit == 1) or
			(php_role_level_limit > role_level and GLOBAL_CONFIG.param_list.guild_chat_need_level_limit == 1)
	end
	
	--人物等级限制
	if  role_level < php_role_level_limit then
		local str = string.format(Language.Chat.ChatLimitByRoleLevel, php_role_level_limit)
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return true
	end

	-- vip等级限制
	if vip_level < php_vip_limit then
		local str = string.format(Language.Chat.ChatLimitByVIP, php_vip_limit)
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return true
	end

	--累充限制
	local php_recharge_limit = GLOBAL_CONFIG.param_list.vip_market_recharge_limit_open or 0
	local history_recharge = RechargeWGData.Instance:GetRealChongZhiRmb()
	if history_recharge < php_recharge_limit then
		local str = string.format(Language.Chat.ChatHistoryRecharge, php_recharge_limit)
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return true
	end

	return false
end

function ChatWGData:GetChatwordRewardCfg(seq)
	return self.chat_word_reward_cfg[seq]
end

function ChatWGData:SetChatWordRewardInfo(protocol)
	self.chat_word_reward_flag = protocol.chat_word_reward_flag
end

function ChatWGData:GetChatWordRewardIsGet(seq)
	return self.chat_word_reward_flag[seq] == 1
end

--跨服聊天查看资料 开关
function ChatWGData:GetIsShowCrossChat()
	return self.chat_other_cfg.is_show_check_info == 0
end