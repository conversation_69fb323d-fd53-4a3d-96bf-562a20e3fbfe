PersonMaBiBossSceneView = PersonMaBiBossSceneView or BaseClass(SafeBaseView)

function PersonMaBiBossSceneView:__init()
	self.active_close = false
	self.view_layer = UiLayer.Normal
    self.view_cache_time = 0
    self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/person_mabiboss_ui_prefab", "person_bossmabi_scene_view")
end

-- function PierreDirectPurchaseView:OpenCallBack()
-- 	if PierreDirectPurchaseWGData.Instance:GetIsNoSendProtocal() then
-- 		local opera_type = PIERRE_DIRECT_PURCHASE_OP_TYPE.GET_ALL
-- 		PierreDirectPurchaseWGCtrl.Instance:SendRequest(opera_type)
-- 	end
-- end

function PersonMaBiBossSceneView:ReleaseCallBack()
	ResMgr:Destroy(self.node_list["person_bossmabi_scene_left_view"].gameObject)

	if self.list_fb_reward then
		self.list_fb_reward:DeleteMe()
		self.list_fb_reward = nil
	end
end

function PersonMaBiBossSceneView:LoadCallBack()
	local left_parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.node_list["person_bossmabi_scene_left_view"].transform:SetParent(left_parent.transform, false)
	self.node_list["person_bossmabi_scene_left_view"].rect.anchoredPosition = Vector2(-62, 0)

	-- MainuiWGCtrl.Instance:SetTaskButtonTrue()

	self.node_list.desc_title.text.text = Language.PersonMabiBoss.FBNane
	self.node_list.desc_fb_describe.text.text = Language.PersonMabiBoss.FBDescribe

	if not self.list_fb_reward then
		self.list_fb_reward = AsyncListView.New(ItemCell, self.node_list.list_fb_reward)
		self.list_fb_reward:SetStartZeroIndex(true)
		local data_list = PersonMaBiBossWGData.Instance:GetRewardDataList()
		self.list_fb_reward:SetDataList(data_list)
	end
end

function PersonMaBiBossSceneView:OnFlush(param_t)
	local fb_cfg = PersonMaBiBossWGData.Instance:GetPersonFBCfg()
	if not IsEmptyTable(fb_cfg) then
		local boss_id = fb_cfg.boss_id
		local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)

		if not IsEmptyTable(boss_cfg) then
			local is_passs = PersonMaBiBossWGData.Instance:GetFBIsPass()
			local color = is_passs and COLOR3B.GREEN or COLOR3B.RED
			local target_value = is_passs and 1 or 0
			self.node_list.desc_fb_target.text.text = string.format(Language.PersonMabiBoss.FBProgress, boss_cfg.name, color, target_value, 1)
		end
	end
end