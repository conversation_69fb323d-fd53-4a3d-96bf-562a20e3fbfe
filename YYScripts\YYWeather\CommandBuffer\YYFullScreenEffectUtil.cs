﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public class YYFullScreenEffectUtil
{
    internal static void AttachBilateralBlur(CommandBuffer commandBuffer, Material blurMaterial, RenderTargetIdentifier sourceIdentifier,
           RenderTargetIdentifier targetIdentifier, RenderTextureFormat format, WeatherDownsampleScale downsampleScale, Camera camera)
    {
        int scale = (int)downsampleScale;
        RenderTextureDescriptor desc = GetRenderTextureDescriptor(scale, 0, 1, format, 0, camera);
        commandBuffer.GetTemporaryRT(WMS._MainTex4, desc, FilterMode.Bilinear);

        int blurPass = 0;
        int blitPass = 2;

        // horizontal blur  // 0 
        commandBuffer.Blit(sourceIdentifier, WMS._MainTex4, blurMaterial, blurPass);

        // vertical blur  // 1
        commandBuffer.Blit(WMS._MainTex4, sourceIdentifier, blurMaterial, blurPass + 1);

        ////

        if (sourceIdentifier != targetIdentifier)
        {
            // upsample  // 2
            if(YYCameraDepth.Instance!=null)
               commandBuffer.Blit(sourceIdentifier, YYCameraDepth.Instance.colorRT, blurMaterial, blitPass);
            else
              commandBuffer.Blit(sourceIdentifier, targetIdentifier, blurMaterial, blitPass);

            //commandBuffer.Blit(sourceIdentifier, YYCameraDepth.Instance.colorRT, blurMaterial, blitPass);
            //commandBuffer.DrawQuad(sourceIdentifier, targetIdentifier, blurMaterial, blitPass);
        }

        // cleanup
        commandBuffer.ReleaseTemporaryRT(WMS._MainTex4);
    }

    public static RenderTextureDescriptor GetRenderTextureDescriptor(int scale, int mod, int scale2, RenderTextureFormat format, int depth = 0, Camera camera = null)
    {
        scale = Mathf.Clamp(scale, 1, 16);
        RenderTextureDescriptor desc;
        //if (UnityEngine.XR.XRDevice.isPresent && (camera == null || camera.stereoEnabled))
        //{
        //    desc = UnityEngine.XR.XRSettings.eyeTextureDesc;
        //}
        //else
        if (camera == null)
        {
            desc = new RenderTextureDescriptor(Screen.width, Screen.height, format, depth);
        }
        else
        {
            desc = new RenderTextureDescriptor(camera.pixelWidth, camera.pixelHeight, format, depth);
        }
        desc.depthBufferBits = depth;
        desc.width = desc.width / scale;
        desc.height = desc.height / scale;
        desc.autoGenerateMips = false;
        desc.useMipMap = false;
        if (mod > 0)
        {
            while (desc.width % mod != 0) { desc.width++; }
            while (desc.height % mod != 0) { desc.height++; }
        }
        desc.width /= scale2;
        desc.height /= scale2;
        return desc;
    }


    internal static void DestroyRenderTexture(ref RenderTexture tex)
    {
        if (tex != null)
        {
            try
            {
                if (tex == RenderTexture.active)
                {
                    RenderTexture.active = null;
                }
                tex.Release();
            }
            catch
            {
            }
            try
            {
                GameObject.DestroyImmediate(tex);
            }
            catch
            {
            }
            tex = null;
        }
    }

    internal static RenderTexture CreateRenderTexture(RenderTextureDescriptor desc, FilterMode filterMode = FilterMode.Bilinear, TextureWrapMode wrapMode = TextureWrapMode.Clamp)
    {
        RenderTexture tex = new RenderTexture(desc);
        tex.filterMode = filterMode;
        tex.wrapMode = wrapMode;
        tex.hideFlags = HideFlags.HideAndDontSave;
        tex.useMipMap = tex.autoGenerateMips = false;
        tex.Create();
        return tex;
    }
}
