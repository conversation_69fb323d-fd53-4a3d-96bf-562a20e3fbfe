
TianShen3v3PrepareSceneLogic = TianShen3v3PrepareSceneLogic or BaseClass(CrossServerSceneLogic)

function TianShen3v3PrepareSceneLogic:__init()
end

function TianShen3v3PrepareSceneLogic:__delete()
end

-- 进入场景
function TianShen3v3PrepareSceneLogic:Enter(old_scene_type, new_scene_type)--20 110
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)

	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetLimitGiftBtnTransActive(false)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance.view:SetAttackMode(ATTACK_MODE.PEACE)
        MainuiWGCtrl.Instance:SetOtherContents(true)
        MainuiWGCtrl.Instance:SetTaskContents(false)
		-- MainuiWGCtrl.Instance.view:FlushWorkTalkBtnActive(new_scene_type ~= SceneType.Kf_PvP_Prepare)
		ViewManager.Instance:Open(GuideModuleName.TianShen3v3PrepareSceneView)
	end)

	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.TIANSHEN_3V3)
	if activity_status then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_status.next_time)
	end

	-- 检查是否需要打开可获得积分场次用完的提醒
	if TianShen3v3WGData.Instance:GetNeedOpenCanGetScoreTipsFlag() then
		TipWGCtrl.Instance:OpenAlertTips(Language.TianShen3v3.ScoreTimesZeroTips)
		TianShen3v3WGData.Instance:SetNeedOpenCanGetScoreTipsFlag(false)
	end
end

function TianShen3v3PrepareSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self,old_scene_type, new_scene_type)
	MainuiWGCtrl.Instance:SetLimitGiftBtnTransActive(true)
	if old_scene_type ~= new_scene_type then
		-- ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
		ViewManager.Instance:CloseAll()
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
            MainuiWGCtrl.Instance:SetOtherContents(false)
            MainuiWGCtrl.Instance:SetTaskContents(true)
			MainuiWGCtrl.Instance:ResetTaskPanel()
			-- MainuiWGCtrl.Instance.view:SetTaskCallBack(nil)
		end)
		ViewManager.Instance:Close(GuideModuleName.TianShen3v3PrepareSceneView)
	end
end

function TianShen3v3PrepareSceneLogic:IsEnemy()
	return false
end

-- 角色是否需要进行优先级的计算
function TianShen3v3PrepareSceneLogic:IsRoleNeedCalculatePriortiy()
	return true
end

function TianShen3v3PrepareSceneLogic:GetColorName(role)
	if role and role:GetVo() then
		if role:GetVo().role_id == GameVoManager.Instance:GetMainRoleVo().role_id and role:GetVo().plat_type == RoleWGData.Instance:GetPlatType() then
			return role:GetVo().name, COLOR3B.ROLE_NAME_COLOR
		end
		return role:GetVo().name, COLOR3B.WHITE
	end
	return "", COLOR3B.WHITE
end

-- -- 角色显示的优先级
-- function TianShen3v3PrepareSceneLogic:GetRoleVisiblePriortiy(role)
-- 	local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
-- 	if role:GetVo().zhandui3v3_id == zhandui_info.zhandui_id then
-- 		return SceneAppearPriority.High
-- 	end

-- 	return SceneAppearPriority.Middle
-- end