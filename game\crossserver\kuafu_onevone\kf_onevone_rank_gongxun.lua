FieldPvPGongXunView = FieldPvPGongXunView or BaseClass(SafeBaseView)

function FieldPvPGongXunView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_season_reward")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function FieldPvPGongXunView:LoadCallBack()
	self.select_panel_index = 1
    self:SetSecondView(nil, self.node_list["size"])
	self.node_list["title_view_name"].text.text = Language.KuafuPVP.ViewName_SeasonReward
	self.duanwei_reward_list = AsyncListView.New(KF1V1DuanWeiRewardItemRender, self.node_list.duanwei_reward_list_1)
	self.rank_reward_list = AsyncListView.New(KF1v1RankRewardItemRender, self.node_list.rank_reward_list)
	
	self.node_list["top_btn_1"].button:AddClickListener(BindTool.Bind(self.ChangePanelByIndex, self, 1))
	self.node_list["top_btn_2"].button:AddClickListener(BindTool.Bind(self.ChangePanelByIndex, self, 2))
end

function FieldPvPGongXunView:ChangePanelByIndex(index)
	self.select_panel_index = index
	for i=1, 2 do
		self.node_list["image_high_"..i]:SetActive(index == i)
		self.node_list["nor_text"..i]:SetActive(index ~= i)
	end
	self.node_list.season_rank_reward_time.text.text = index == 1 and Language.Field1v1.SeasonEndDesc or Language.Field1v1.SeasonEndDesc_1
	if index == 1 then
		local kf_info = KuafuOnevoneWGData.Instance:Get1V1Info()
		local ranking_data = KuafuOnevoneWGData.Instance:GetdwRankReward(kf_info.cur_season)
		self.node_list.rank_reward_list:SetActive(true)
		self.node_list.duanwei_reward_list_1:SetActive(false)
		self.rank_reward_list:SetDataList(ranking_data)
	elseif index == 2 then
		self.node_list.duanwei_reward_list_1:SetActive(true)
		self.node_list.rank_reward_list:SetActive(false)
		local _,data = KuafuOnevoneWGData.Instance:GetGridNum()
		local data_list = {}
		for k,v in pairs(data) do
			if k % 5 == 1 then
				table.insert(data_list,v)
			end
		end
		table.sort(data_list, SortTools.KeyUpperSorter("seq"))
		self.duanwei_reward_list:SetDataList(data_list)
	end
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_table = os.date("*t", server_time)
	local month_day_count = os.date("%d",os.time({year=time_table.year,month=time_table.month+1,day=0}))
	local count_shengyu = month_day_count - time_table.day + 1
	self.node_list.join_count.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,time_table.month,month_day_count,count_shengyu)
end

function FieldPvPGongXunView:ShowIndexCallBack(index)
	
end

function FieldPvPGongXunView:ReleaseCallBack()
	if self.jifen_reward_list then
		self.jifen_reward_list:DeleteMe()
		self.jifen_reward_list = nil
	end
	self.has_load_callback = nil
	self.need_flush = nil
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
	if self.duanwei_reward_list then
		self.duanwei_reward_list:DeleteMe()
		self.duanwei_reward_list = nil
	end
	self.select_panel_index = nil
end

function FieldPvPGongXunView:OnFlush( param )
	local select_index = CheckList(param, "all", "choose")
	if self.select_panel_index > 0 then
		select_index = select_index or self.select_panel_index
		self:ChangePanelByIndex(select_index)
	end
end

function FieldPvPGongXunView:FlushRankGongXunPanel(index)
	if index == KFPVP_TYPE.ONE then
		local _,data = KuafuOnevoneWGData.Instance:GetGridNum()
		local new_data = TableCopy(data)
		table.sort(new_data, SortTools.KeyUpperSorter("seq"))

		if self.jifen_reward_list then
			self.jifen_reward_list:SetData(new_data,2)
			--self.jifen_reward_list:JumpToTop(true)
		end
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local time_table = os.date("*t", server_time)
		local month_day_count = os.date("%d",os.time({year=time_table.year,month=time_table.month+1,day=0}))
		local count_shengyu = month_day_count - time_table.day + 1
		self.node_list.season_end_time.text.text = string.format(Language.Kuafu1V1.RankingMatchDesc2,time_table.month,month_day_count,count_shengyu)

	elseif index == KFPVP_TYPE.MORE then
		local data = KuafuPVPWGData.Instance:GetJiFenRewardCfg()
		if self.jifen_reward_list then
			self.jifen_reward_list:SetData(data,2)
			--self.jifen_reward_list:JumpToTop(true)
		end
		self.node_list.lbl_total_jifen.text.text = KuafuPVPWGData.Instance:GetRewardGongxun()
	end

end

--赛季奖励
KF1v1RankRewardItemRender = KF1v1RankRewardItemRender or BaseClass(BaseRender)
function KF1v1RankRewardItemRender:__init()
	self.item_list = AsyncListView.New(KF1V1RankRewardItemCell, self.node_list.item_list)
end
function KF1v1RankRewardItemRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end
function KF1v1RankRewardItemRender:OnFlush()
	--奖励物品
	local data_list = {}
	for i = 0, 20 do
		if self.data.reward_item[i] then
			table.insert(data_list, self.data.reward_item[i])
		else
			break
		end
	end


	self.item_list:SetDataList(data_list, 0)
	--self.node_list.item_list.rect.sizeDelta = Vector2(cell_width[#item_data] or more_width, self.node_list.item_list.rect.sizeDelta.y)
	self.node_list.bg:SetActive(self.index % 2 == 1)
	local base_cfg = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
	self.node_list.rank_text.text.text = self.data.name

	--参与场次要求
	self.node_list.join_count.text.text = string.format(Language.KuafuPVP.JoinCount, base_cfg.atleast_join_times)
end

--段位奖励
KF1V1DuanWeiRewardItemRender = KF1V1DuanWeiRewardItemRender or BaseClass(BaseRender)
function KF1V1DuanWeiRewardItemRender:__init()
	self.item_list = AsyncListView.New(KF1V1RankRewardItemCell, self.node_list.item_list)
end
function KF1V1DuanWeiRewardItemRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end
function KF1V1DuanWeiRewardItemRender:OnFlush()
	--奖励物品
	local data_list = {}
	for i = 0, 20 do
		if self.data.reward_item[i] then
			table.insert(data_list, self.data.reward_item[i])
		else
			break
		end
	end
	self.node_list.duanwei_text.text.text = self.data.grade_name
	ChangeToQualityText(self.node_list.duanwei_text, RankGradeEnum[self.data.rank_id])
	self.item_list:SetDataList(data_list)
	self.node_list.bg:SetActive(self.index % 2 == 1)
	local b, a = ResPath.GetF2Field1v1Rank(self.data.rank_id)
	self.node_list.duanwei_img.image:LoadSprite(b, a, function()
	end)
	--参与场次要求
	self.node_list.join_count.text.text = string.format(Language.KuafuPVP.JoinCount_1, self.data.score)
end

KF1V1RankRewardItemCell = KF1V1RankRewardItemCell or BaseClass(BaseRender)
function KF1V1RankRewardItemCell:__init()
	self.cell = ItemCell.New(self.node_list.pos)
end
function KF1V1RankRewardItemCell:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end
function KF1V1RankRewardItemCell:OnFlush()
	self.cell:SetData(self.data)
end