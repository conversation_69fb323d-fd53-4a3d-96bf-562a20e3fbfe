function HolyDarkWeaponView:InitHolyDarkUpStarView()
	if not self.upstar_show_list then
		self.upstar_show_list = AsyncListView.New(HolyDarkUpStarRender, self.node_list["upstar_equip_list"])
		self.upstar_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectUpStarCell, self))
		self.upstar_show_list:SetDefaultSelectIndex(nil)
	end

	if not self.upstar_equip_item then
		self.upstar_equip_item = ItemCell.New(self.node_list["upstar_equip_cell"])
	    self.upstar_equip_item:SetIsShowTips(false)
		self.upstar_equip_item:SetClickCallBack(BindTool.Bind(self.OnClickUpStarEquipCell, self))
	end

	if not self.upstar_stuff_item then
		self.upstar_stuff_item = ItemCell.New(self.node_list["upstar_stuff_cell"])
	end

	XUI.AddClickEventListener(self.node_list["upstar_btn"], BindTool.Bind(self.ClickUpStarBtn, self))

	self.upstar_select_data = nil
end

function HolyDarkWeaponView:DeleteHolyDarkUpStarView()
	if self.upstar_show_list then
		self.upstar_show_list:DeleteMe()
		self.upstar_show_list = nil
	end

	if self.upstar_equip_item then
        self.upstar_equip_item:DeleteMe()
        self.upstar_equip_item = nil
    end

    if self.upstar_stuff_item then
        self.upstar_stuff_item:DeleteMe()
        self.upstar_stuff_item = nil
    end


	self.upstar_select_index = nil
	self.upstar_select_data = nil
end

function HolyDarkWeaponView:FlushHolyDarkUpStarView()
	local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
		local wear_all_data = HolyDarkWeaponWGData.Instance:GetHolyDarkEquipWearList(relic_seq)
		local data = {}
		for i = 0, 8 do
			if wear_all_data[i] and wear_all_data[i].item_id > 0 then
				table.insert(data, wear_all_data[i])
			end
		end

		self.upstar_show_list:SetDataList(data)
		local click_cell_index = self.upstar_select_index or 1
		if not self.upstar_select_index then
			for i, v in ipairs(data) do
				if HolyDarkWeaponWGData.Instance:GetRelicSlotCanUpStar(v) then
					click_cell_index = i
					break
				end
			end
		end

		self.upstar_show_list:JumpToIndex(click_cell_index)
    end
end

function HolyDarkWeaponView:OnSelectUpStarCell(cell)
	if nil == cell and nil == cell.data then
		return
	end

	self.upstar_select_data = cell.data
	self.upstar_select_index = cell.index
	self:FlushUpStarLeftPanel()
end

function HolyDarkWeaponView:FlushUpStarLeftPanel()
	if self.upstar_select_data == nil then
		return
	end 

	local data = self.upstar_select_data
	self.upstar_equip_item:SetData({item_id = data.item_id})
	self.upstar_equip_item:SetLeftTopImg(data.star_level or 0)
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local item_name = item_cfg and item_cfg.name or ""
	if data.star_level > 0 then
		local star_str = CommonDataManager.GetAncientNumber(data.star_level)
		item_name = string.format(Language.HolyDarkWeapon.EquipStarName, star_str, item_name)
	end

	if data.level > 0 then
		item_name = string.format(Language.HolyDarkWeapon.EquipLevelName, item_name, data.level)
	end

	self.node_list.upstar_equip_name.text.text = item_name
	local cur_star_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotStarCfg(data.relic_seq, data.slot_index, data.star_level)
	local next_star_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotStarCfg(data.relic_seq, data.slot_index, data.star_level + 1)
	local is_max = IsEmptyTable(next_star_cfg)
	self.node_list.upstar_stuff_cell:SetActive(not is_max)
	self.node_list.upstar_btn:SetActive(not is_max)
	self.node_list.upstar_level_max:SetActive(is_max)
	self.node_list.upstar_remind:SetActive(false)

	if not IsEmptyTable(cur_star_cfg) then
		self.upstar_stuff_item:SetData({item_id = cur_star_cfg.cost_item_id})
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.cost_item_id)
		local color = item_num >= cur_star_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.upstar_stuff_item:SetRightBottomTextVisible(true)
		self.upstar_stuff_item:SetRightBottomText(ToColorStr(item_num .. '/' .. cur_star_cfg.cost_item_num, color))
		self.node_list.upstar_remind:SetActive(not is_max and item_num >= cur_star_cfg.cost_item_num)
	end

	self:OnFlushUpStarAttrPanel()
end

function HolyDarkWeaponView:OnFlushUpStarAttrPanel()
	if self.upstar_select_data == nil then
		return
	end 

	local data = self.upstar_select_data
	local cur_star_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSlotStarAttrList(data.relic_seq, data.slot_index, data.star_level)
	local next_star_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSlotStarAttrList(data.relic_seq, data.slot_index, data.star_level + 1)
	if not IsEmptyTable(cur_star_attr_list) then
		for i = 1, 5 do
			if cur_star_attr_list[i] then
				self.node_list["upstar_cur_attr_" .. i]:SetActive(true)
				local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(cur_star_attr_list[i].attr_str, true)
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(cur_star_attr_list[i].attr_str)
				local per_value = is_per and cur_star_attr_list[i].attr_value / 100 or cur_star_attr_list[i].attr_value
				local per_str = is_per and "%" or ""
				self.node_list["upstar_cur_attr_" .. i].text.text = attr_name .. "   " .. per_value .. per_str
			else
				self.node_list["upstar_cur_attr_" .. i]:SetActive(false)
			end
		end
	else
		if not IsEmptyTable(next_star_attr_list) then
			for i = 1, 5 do
				if next_star_attr_list[i] then
					self.node_list["upstar_cur_attr_" .. i]:SetActive(true)
					local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(next_star_attr_list[i].attr_str, true)
					self.node_list["upstar_cur_attr_" .. i].text.text = attr_name .. "   " .. "0"
				else
					self.node_list["upstar_cur_attr_" .. i]:SetActive(false)
				end
			end
		else
			for i = 1, 5 do
				self.node_list["upstar_cur_attr_" .. i]:SetActive(false)
			end
		end
	end

	if not IsEmptyTable(next_star_attr_list) then
		for i = 1, 5 do
			if next_star_attr_list[i] then
				self.node_list["upstar_next_attr_" .. i]:SetActive(true)
				local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(next_star_attr_list[i].attr_str, true)
				local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(next_star_attr_list[i].attr_str)
				local per_value = is_per and next_star_attr_list[i].attr_value / 100 or next_star_attr_list[i].attr_value
				local per_str = is_per and "%" or ""
				self.node_list["upstar_next_attr_" .. i].text.text = attr_name .. "   " .. ToColorStr(per_value .. per_str, "#8ee08e")
			else
				self.node_list["upstar_next_attr_" .. i]:SetActive(false)
			end

			self.node_list.upstar_lnext_attr_max:SetActive(false)
		end
	else
		for i = 1, 5 do
			self.node_list["upstar_next_attr_" .. i]:SetActive(false)
		end

		self.node_list.upstar_lnext_attr_max:SetActive(true)
	end
end

function HolyDarkWeaponView:OnClickUpStarEquipCell()
	if self.upstar_select_data == nil then
		return
	end 

	TipWGCtrl.Instance:OpenItem(self.upstar_select_data, ItemTip.FROM_RELIC_EQUIP)
end

function HolyDarkWeaponView:ClickUpStarBtn()
	local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
    	local data = self.upstar_select_data
    	local cur_star_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotStarCfg(data.relic_seq, data.slot_index, data.star_level)
		local next_star_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotStarCfg(data.relic_seq, data.slot_index, data.star_level + 1)
		local is_max = IsEmptyTable(next_star_cfg)
		if is_max then
			TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.MaxStar)
            return
		end

		if not IsEmptyTable(cur_star_cfg) then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.cost_item_id)
			if item_num >= cur_star_cfg.cost_item_num then
				HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.SLOT_UPSTAR, data.relic_seq, data.slot_index)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ItemNotEnough)
				TipWGCtrl.Instance:OpenItem({item_id = cur_star_cfg.cost_item_id})
            	return
			end
		end
    end
end

function HolyDarkWeaponView:PlayUpStarEffect()
	if self.node_list["star_succ_pos"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0),
								parent_node = self.node_list["star_succ_pos"]})
	end
end

function HolyDarkWeaponView:DoHolyDarkUpStarViewAnim()
	local tween_info = UITween_CONSTS.HolyDark
	RectTransform.SetAnchoredPositionXY(self.node_list.upstar_equip_list.rect, 440, 0)
	local right_tween = self.node_list.upstar_equip_list.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
	RectTransform.SetSizeDeltaXY(self.node_list["upstar_attr_panel"].rect, 0, 328)
	self.node_list["upstar_attr_panel"].rect:DOSizeDelta(Vector2(820, 328), 1)
end

-----------------------------
HolyDarkUpStarRender = HolyDarkUpStarRender or BaseClass(BaseRender)

function HolyDarkUpStarRender:LoadCallBack()
	self.equip_item = ItemCell.New(self.node_list["equip_cell"])
    self.equip_item:SetIsShowTips(false)
	self.equip_item:SetClickCallBack(BindTool.Bind(self.OnItemClick, self))
end

function HolyDarkUpStarRender:__delete()
	if self.equip_item then
        self.equip_item:DeleteMe()
        self.equip_item = nil
    end
end

function HolyDarkUpStarRender:OnFlush()
	if not self.data then
		return
	end

	self.equip_item:SetData({item_id = self.data.item_id})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name_text.text.text = item_cfg and item_cfg.name or ""
	self.node_list.upstar_level.text.text = string.format(Language.HolyDarkWeapon.UpStarStr, self.data.star_level)
	local is_up = HolyDarkWeaponWGData.Instance:GetRelicSlotCanUpStar(self.data)
	self.node_list.remind:SetActive(is_up)
	self.equip_item:SetLeftTopImg(self.data.star_level or 0)
end

function HolyDarkUpStarRender:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.hl_img:SetActive(is_select)
end

function HolyDarkUpStarRender:OnItemClick()
	if not self.data then
		return
	end

	TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_RELIC_EQUIP)
end