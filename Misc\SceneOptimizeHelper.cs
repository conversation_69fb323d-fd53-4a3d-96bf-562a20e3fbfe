﻿using LuaInterface;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using System.IO;
using Game;
using Nirvana;
using System;

#if UNITY_EDITOR
using UnityEditor;
#endif

[Serializable]

public class SceneOptimizeHelper
{
    private bool isAllowHighPerformance;
    private bool isIgnoreEffectCulling;

    public void CheckScene(bool isAllowHighPerformance, bool isIgnoreEffectCulling)
    {
        this.isAllowHighPerformance = isAllowHighPerformance;
        this.isIgnoreEffectCulling = isIgnoreEffectCulling;

        this.CheckSceneModel();
        this.CheckSceneEffect();
        this.CheckComponents();
        this.CheckWater();
    }

    private void CheckSceneModel()
    {
        GameObject modelsObj = GameObject.Find("Main/Models");
        if (modelsObj == null)
        {
            GameObject gameObject = new GameObject("Models");
            gameObject.transform.parent = GameObject.Find("Main").transform;
        }

        GameObject rootObj = GameObject.Find("Main/Models/Terrain");
        if (rootObj == null)
        {
            GameObject gameObject = new GameObject("Terrain");
            gameObject.transform.parent = modelsObj.transform;
        }

        rootObj = GameObject.Find("Main/Models/Stone");
        if (rootObj == null)
        {
            GameObject gameObject = new GameObject("Stone");
            gameObject.transform.parent = modelsObj.transform;
        }

        rootObj = GameObject.Find("Main/Models/Building");
        if (rootObj == null)
        {
            GameObject gameObject = new GameObject("Building");
            gameObject.transform.parent = modelsObj.transform;
        }

        rootObj = GameObject.Find("Main/Models/Plant");
        if (rootObj == null)
        {
            GameObject gameObject = new GameObject("Plant");
            gameObject.transform.parent = modelsObj.transform;
        }

        rootObj = GameObject.Find("Main/Models/Others");
        if (rootObj == null)
        {
            GameObject gameObject = new GameObject("Others");
            gameObject.transform.parent = modelsObj.transform;
        }
    }

    private void CheckSceneEffect()
    {
        GameObject rootObj = GameObject.Find("Main");
        if (rootObj == null) return;

        Transform cgPos = rootObj.transform.Find("CGPos");

        var particles = rootObj.GetComponentsInChildren<ParticleSystem>();
        for (int i = 0; i < particles.Length; i++)
        {
            if (cgPos != null && particles[i].transform.IsChildOf(cgPos))
            {
                continue;
            }

            GameObjectAttach attach = particles[i].GetComponentInParent<GameObjectAttach>();
            if (null == attach)
            {
                Debug.LogErrorFormat("场景中的粒子特效请严格使用GameObjectAttach来加载，通知特效处理, {0},   {1}", particles[i].name, SceneManager.GetActiveScene().name);
                if (GameRoot.Instance) GameRoot.AddLuaWarning("场景中的特效请严格使用GameObjectAttach来加载，通知特效处理", "Normal");
            }
        }

        if (!this.isIgnoreEffectCulling)
        {
            GameObjectAttach[] attchs = rootObj.GetComponentsInChildren<GameObjectAttach>();
            for (int i = 0; i < attchs.Length; i++)
            {
                var cullObj = attchs[i].GetComponentInParent<CameraCullObj>();
                if (null == cullObj)
                {
                    Debug.LogErrorFormat("场景中的粒子特效没用使用CullObj，通知特效处理, {0},   {1}", attchs[i].gameObject.name, SceneManager.GetActiveScene().name);
                }
            }
        }
    }

    private void CheckWater()
    {
        if (!this.isAllowHighPerformance)
        {
            GameObject waterObj = GameObject.Find("Main/Waters");
            if (null == waterObj) return;
            var renderers = waterObj.GetComponentsInChildren<Renderer>();
            for (int i = 0; i < renderers.Length; i++)
            {
                if (renderers[i].sharedMaterial.shader.name == "Water/RefractWaveWater")
                {
                    Debug.LogErrorFormat("场景中的水请不要再使用RefractWaveWater, {0},   {1}", renderers[i].name, SceneManager.GetActiveScene().name);
                }
            }
        }
    }

    private void CheckComponents()
    {
        GameObject modelsObj = GameObject.Find("Main/Models");
        if (null != modelsObj)
        {
            Collider[] colliders = modelsObj.GetComponentsInChildren<Collider>();
            if (colliders != null && colliders.Length > 0)
            {
                foreach (Collider collider in colliders)
                {
                    if (collider != null && collider.enabled && !collider.GetType().Equals(typeof(TerrainCollider)) && collider.gameObject.GetComponent<SrpTerrainFeature>() == null)
                    {
                        bool isTerrainCollider = collider.GetType().Equals(typeof(TerrainCollider));
                        if (isTerrainCollider && !collider.enabled)
                        {
                            Debug.LogErrorFormat("Terrain的Terrain Collider组件未激活，请通知地编处理，节点：{0}", collider.gameObject.name);
                        }
                        else if (!isTerrainCollider && collider.enabled && collider.gameObject.GetComponent<SrpTerrainFeature>() == null)
                        {
                            Debug.LogErrorFormat("场景中存在非法组件，请通知地编 进行一键优化{0}，Main/Models节点下存在Collider组件{1}", SceneManager.GetActiveScene().name, collider.gameObject.name);
                        }
                    }
                }
            }
        }
    }

    public bool IsColliderChildNode(Transform current, Transform collider)
    {
        if (current == null || collider == null)
        {
            return false;
        }

        if (current.parent == null)
        {
            return false;
        }
        else
        {
            if (current.parent == collider)
            {
                return true;
            }
            return IsColliderChildNode(current.parent, collider);
        }
    }

#if UNITY_EDITOR

    [NoToLua]
    public void QuickOptimize()
    {
        this.FixGameObject();
        this.FixHerolight();
        this.FixTerrainMatKeyword();
        this.DelInvalidUseObj();
        this.OptimizeQualityActives();
        this.OptimizeCullObj();
        this.OptimizeGameObjectAttach();
        this.DelInvalidComponents();
        //this.OptimizeReflectionProbe();
    }

    [NoToLua]
    private void DelInvalidUseObj()
    {
        GameObject rootObj = null;
        string[] paths = new string[] { "Main/Models", "Main/Animations", "Main/Waters" };
        List<GameObject> list = new List<GameObject>();
        for (int i = 0; i < paths.Length; i++)
        {
            rootObj = GameObject.Find(paths[i]);
            if (null != rootObj)
            {
                int childCount = rootObj.transform.childCount;
                for (int j = 0; j < childCount; j++)
                {
                    Transform child = rootObj.transform.GetChild(j);
                    if (null != child && !child.gameObject.activeSelf)
                    {
                        list.Add(child.gameObject);
                    }
                }
            }
        }

        paths = new string[] { 
            "Main/Models/Stone", 
            "Main/Models/Plant", 
            "Main/Models/Building", 
            "Main/Models/Others", 
            "Main/Effects", 
            "Main/Waters", 
            "Main/Animations" 
        };
        for (int i = 0; i < paths.Length; i++)
        {
            rootObj = GameObject.Find(paths[i]);
            if (null != rootObj)
            {
                Transform[] transforms = rootObj.GetComponentsInChildren<Transform>();
                for (int m = 0; m < transforms.Length; m++)
                {
                    if (rootObj != transforms[m].gameObject
                        && transforms[m].transform.childCount == 0
                        && null == transforms[m].GetComponentInParent<Animator>()
                        && null == transforms[m].GetComponentInChildren<Renderer>()
                        && null == transforms[m].GetComponentInChildren<GameObjectAttach>())
                    {
                        list.Add(transforms[m].gameObject);
                        GameObject.DestroyImmediate(transforms[m].gameObject, true);
                    }
                }
            }
        }

        for (int i = 0; i < list.Count; i++)
        {
            GameObject.DestroyImmediate(list[i].gameObject, true);
        }
    }

    private void DelInvalidComponents()
    {
        string[] paths = new string[] { "Main/Models", "Main/Animations" };
        for (int m = 0; m < paths.Length; m++)
        {
            GameObject modelsObj = GameObject.Find(paths[m]);
            if (null != modelsObj)
            {
                var colliders = modelsObj.GetComponentsInChildren<MeshCollider>();
                for (int i = 0; i < colliders.Length; i++)
                {
                    GameObject.DestroyImmediate(colliders[i], true);
                }
            }
        }

        GameObject rootObj = GameObject.Find("Main");
        if (null != rootObj)
        {
            GameObjectAttach[] attachs = rootObj.GetComponentsInChildren<GameObjectAttach>(true);
            for (int i = 0; i < attachs.Length; i++)
            {
                if (attachs[i].IsGameobjectMissing())
                {
                    GameObject.DestroyImmediate(attachs[i], true);
                }
            }
        }
    }

    private void FixHerolight()
    {
        GameObject herolightObj = GameObject.Find("Main/Hero light");
        if (herolightObj == null)
        {
            Debug.LogError("严重错误，找不到Main/Hero light，请马上检查！！！");
            return;
        }
        Light herolight = herolightObj.GetComponent<Light>();
        if (herolight == null)
        {
            Debug.LogError("严重错误，Main/Hero light没有挂载Light组件，请马上检查！！！");
            return;
        }
        herolight.cullingMask &= ~(1 << GameLayers.UI
                                            | 1 << GameLayers.UI3D
                                            | 1 << GameLayers.UI3DEffect
                                            | 1 << GameLayers.UI3DTerrain
                                            | 1 << GameLayers.UICG
                                            | 1 << GameLayers.UIScene
                                            | 1 << GameLayers.UIEffect);
        herolight.type = LightType.Directional;
    }

    private void FixGameObject()
    {
        GameObject rootObj = GameObject.Find("Main");
        if (null == rootObj)
        {
            Debug.LogErrorFormat("不存在Main节点, {0}", SceneManager.GetActiveScene().name);
            return;
        }

        GameObject modelsObj = GameObject.Find("Main/Models");
        if (null == modelsObj)
        {
            Debug.LogErrorFormat("不存在Main/Models节点, {0}", SceneManager.GetActiveScene().name);
            return;
        }

        // 调整物体
        //GameObject normalObj = GameObject.Find("Main/Models/Normal");
        //List<Transform> objList = new List<Transform>();
        //if (null != normalObj)
        //{
        //    for (int i = 0; i < normalObj.transform.childCount; i++)
        //    {
        //        objList.Add(normalObj.transform.GetChild(i));
        //    }
        //}

        //GameObject batchingStaticObj = GameObject.Find("Main/Models/BatchingStatic");
        //if (null != batchingStaticObj)
        //{
        //    for (int i = 0; i < batchingStaticObj.transform.childCount; i++)
        //    {
        //        objList.Add(batchingStaticObj.transform.GetChild(i));
        //    }
        //}

        //foreach (var item in objList)
        //{
        //    item.SetParent(modelsObj.transform);
        //}

        //if (null != normalObj) GameObject.DestroyImmediate(normalObj);
        //if (null != batchingStaticObj) GameObject.DestroyImmediate(batchingStaticObj);

        // colliders
        //objList.Clear();

        List<Transform> objList = new List<Transform>();
        GameObject colliderObj = GameObject.Find("Collider");
        if (null != colliderObj)
        {
            for (int i = 0; i < colliderObj.transform.childCount; i++)
            {
                objList.Add(colliderObj.transform.GetChild(i));
            }
        }

        GameObject collidersObj = GameObject.Find("Colliders");
        if (null == collidersObj)
        {
            collidersObj = new GameObject("Colliders");
            collidersObj.transform.SetParent(rootObj.transform);
        }

        foreach (var item in objList)
        {
            item.SetParent(collidersObj.transform);
        }

        if (null != colliderObj) GameObject.DestroyImmediate(colliderObj);


        // 动画
        objList.Clear();
        GameObject animationObj = GameObject.Find("Main/Animation");
        if (null != animationObj)
        {
            for (int i = 0; i < animationObj.transform.childCount; i++)
            {
                objList.Add(animationObj.transform.GetChild(i));
            }
        }

        GameObject animationsObj = GameObject.Find("Main/Animations");
        if (null == animationsObj)
        {
            animationsObj = new GameObject("Animations");
            animationsObj.transform.SetParent(rootObj.transform);
        }

        foreach (var item in objList)
        {
            item.SetParent(animationsObj.transform);
        }

        // Animation里如果不是动画则移到model里，可以合批
        objList.Clear();
        for (int i = 0; i < animationsObj.transform.childCount; i++)
        {
            var animationChildObj = animationsObj.transform.GetChild(i);
            if (!animationChildObj.GetComponent<Animation>() && !animationChildObj.GetComponent<Animator>() &&
                animationChildObj.GetComponent<MeshFilter>())
            {
                objList.Add(animationsObj.transform.GetChild(i));
            }
        }

        foreach (var item in objList)
        {
            item.SetParent(modelsObj.transform);
        }

        if (null != animationObj) GameObject.DestroyImmediate(animationObj);

        // 水
        objList.Clear();
        GameObject waterObj = GameObject.Find("Main/Water");
        if (null != waterObj)
        {
            for (int i = 0; i < waterObj.transform.childCount; i++)
            {
                objList.Add(waterObj.transform.GetChild(i));
            }
        }

        GameObject watersObj = GameObject.Find("Main/Waters");
        if (null == watersObj)
        {
            watersObj = new GameObject("Waters");
            watersObj.transform.SetParent(rootObj.transform);
        }

        foreach (var item in objList)
        {
            item.SetParent(watersObj.transform);
        }

        if (null != waterObj) GameObject.DestroyImmediate(waterObj);

        // 特效
        objList.Clear();
        GameObject effectsObj = GameObject.Find("Main/effect");
        if (null != effectsObj)
        {
            for (int i = 0; i < effectsObj.transform.childCount; i++)
            {
                objList.Add(effectsObj.transform.GetChild(i));
            }
        }

        GameObject effectsObj2 = GameObject.Find("Main/Effect");
        if (null != effectsObj2)
        {
            for (int i = 0; i < effectsObj2.transform.childCount; i++)
            {
                objList.Add(effectsObj2.transform.GetChild(i));
            }
        }

        GameObject effectObj = GameObject.Find("Main/Effects");
        if (null == effectObj)
        {
            effectObj = new GameObject("Effects");
            effectObj.transform.SetParent(rootObj.transform);
        }

        foreach (var item in objList)
        {
            item.SetParent(effectObj.transform);
        }

        if (null != effectsObj) GameObject.DestroyImmediate(effectsObj);
        if (null != effectsObj2) GameObject.DestroyImmediate(effectsObj2);

        Transform[] transforms = rootObj.GetComponentsInChildren<Transform>();
        for (int i = 0; i < transforms.Length; i++)
        {
            GameObjectUtility.SetStaticEditorFlags(transforms[i].gameObject, ~StaticEditorFlags.BatchingStatic);
        }
    }

    private void FixTerrainMatKeyword()
    {
        GameObject modelNode = GameObject.Find("Main/Models");
        modelNode = modelNode == null ? GameObject.Find("Main/Model") : modelNode;
        if (modelNode == null)
        {
            Debug.LogError("严重错误！！！无找到Main/Models节点，请检查！！！");
            return;
        }

        Transform terrainNode = modelNode.transform.Find("Terrain");
        if (terrainNode == null)
        {
            Debug.LogError("严重错误！！！无找到Main/Models/Terrain节点，请检查！！！");
            return;
        }

        Terrain[] terrains = terrainNode.GetComponentsInChildren<Terrain>();
        if (terrains != null && terrains.Length > 0)
        {
            for (int i = 0; i < terrains.Length; i++)
            {
                Material material = terrains[i].materialTemplate;
                if (material != null)
                {
                    material.EnableKeyword("FOG_LINEAR");
                    material.EnableKeyword("INSTANCING_ON");
                    material.EnableKeyword("LIGHTMAP_ON");
                    material.EnableKeyword("_NORMALMAP");
                    material.EnableKeyword("_MASKMAP");
                    material.EnableKeyword("_ADDITIONAL_LIGHTS");
                    material.EnableKeyword("_ADDITIONAL_LIGHT_SHADOWS");
                    material.EnableKeyword("_TERRAIN_INSTANCED_PERPIXEL_NORMAL");
                    material.EnableKeyword("_MAIN_LIGHT_SHADOWS");
                }
            }
        }
    }

    private void OptimizeQualityActives()
    {
        GameObject effectObj = GameObject.Find("Main/Effects");
        if (null == effectObj)
        {
            Debug.LogErrorFormat("不存在Effect节点, {0}", SceneManager.GetActiveScene().name);
            return;
        }

        GameObject waterObj = GameObject.Find("Main/Waters");
        if (null == waterObj)
        {
            Debug.LogErrorFormat("不存在Effect节点, {0}", SceneManager.GetActiveScene().name);
            return;
        }

        OptimizeQualityActive(effectObj, 1);
        OptimizeQualityActive(waterObj, 4);
    }

    private void OptimizeQualityActive(GameObject effectObj, int level)
    {
        QualityControlActive qualityCtrl = effectObj.GetOrAddComponent<QualityControlActive>();
        ControlItem[] controls = qualityCtrl.GetControls();

        List<ControlItem> list = new List<ControlItem>();
        HashSet<UnityEngine.Object> hashSet = new HashSet<UnityEngine.Object>();
        if (null != controls)
        {
            foreach (var item in controls)
            {
                if (null == item.Target)
                {
                    continue;
                }

                for (int i = 0; i < effectObj.transform.childCount; i++)
                {
                    if (effectObj.transform.GetChild(i).gameObject == item.Target)
                    {
                        hashSet.Add(item.Target);
                        list.Add(item);
                    }
                }
            }
        }

        for (int i = 0; i < effectObj.transform.childCount; i++)
        {
            GameObject gameObject = effectObj.transform.GetChild(i).gameObject;
            if (!hashSet.Contains(gameObject))
            {
                hashSet.Add(gameObject);
                ControlItem item = new ControlItem();
                item.Target = gameObject;
                item.EnabledLevels = new bool[4];
                for (int m = 0; m < level; m++)
                {
                    item.EnabledLevels[m] = true;
                }

                list.Add(item);
            }
        }

        qualityCtrl.SetControls(list.ToArray());
    }

    private void OptimizeCullObj()
    {
        if (this.isIgnoreEffectCulling)
        { 
            return;
        }
        
        GameObject rootObj = GameObject.Find("Main");
        GameObjectAttach[] attchs = rootObj.GetComponentsInChildren<GameObjectAttach>();
        for (int i = 0; i < attchs.Length; i++)
        {
            attchs[i].GetOrAddComponent<CameraCullObj>();
        }
    }

    //设置cullingMask无效果，屏蔽
    //private void OptimizeReflectionProbe()
    //{
    //    GameObject reflectionProbeObj = GameObject.Find("Main/Reflection Probe");
    //    if (reflectionProbeObj)
    //    {
    //        ReflectionProbe reflectionProbe = reflectionProbeObj.GetComponent<ReflectionProbe>();
    //        if (reflectionProbe == null)
    //        {
    //            Debug.LogError("场景规范警告！没有挂载Reflection Probe组件，请马上检查");
    //        }
    //        reflectionProbe.cullingMask &= ~(1 << GameLayers.UI
    //                                        | 1 << GameLayers.UI3D
    //                                        | 1 << GameLayers.UI3DEffect
    //                                        | 1 << GameLayers.UI3DTerrain
    //                                        | 1 << GameLayers.UICG
    //                                        | 1 << GameLayers.UIScene
    //                                        | 1 << GameLayers.UIEffect);
    //    }
    //    else
    //    {
    //        Debug.LogError("场景规范警告！没有找到 Main/Reflection Probe，请马上检查");
    //    }
    //}

    private void OptimizeGameObjectAttach()
    {
        GameObject rootObj = GameObject.Find("Main");
        GameObjectAttach[] attchs = rootObj.GetComponentsInChildren<GameObjectAttach>();
        for (int i = 0; i < attchs.Length; i++)
        {
            attchs[i].RefreshAssetBundleName();
        }
    }

    private void OptimizeDisconnectPrefab()
    {
        GameObject rootObj = GameObject.Find("Main");
        GameObjectAttach[] attchs = rootObj.GetComponentsInChildren<GameObjectAttach>();
        for (int i = 0; i < attchs.Length; i++)
        {
            if (null == attchs[i])
            {
                continue;
            }

            var oldGameObj = attchs[i].gameObject;
            if (PrefabUtility.GetPrefabParent(oldGameObj))
            {
                GameObject newGameObj = new GameObject();
                newGameObj.name = oldGameObj.name;
                newGameObj.transform.parent = oldGameObj.transform.parent;
                newGameObj.transform.localPosition = oldGameObj.transform.localPosition;
                newGameObj.transform.localRotation = oldGameObj.transform.localRotation;
                newGameObj.transform.localScale = oldGameObj.transform.localScale;
                var components = oldGameObj.GetComponents<Component>();

                for (int m = 0; m < components.Length; m++)
                {
                    Type type = components[m].GetType();
                    UnityEditorInternal.ComponentUtility.CopyComponent(components[m]);

                    if (null == newGameObj.GetComponent(type))
                    {
                        UnityEditorInternal.ComponentUtility.PasteComponentAsNew(newGameObj);
                    }
                }

                GameObject.DestroyImmediate(oldGameObj);
            }
        }
    }

#endif
}
