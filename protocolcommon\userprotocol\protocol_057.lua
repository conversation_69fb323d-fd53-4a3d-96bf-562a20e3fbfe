-- 跨服修罗塔个人活动信息
SCCrossXiuluoTowerSelfActivityInfo = SCCrossXiuluoTowerSelfActivityInfo or BaseClass(BaseProtocolStruct)
function SCCrossXiuluoTowerSelfActivityInfo:__init()
	self.msg_type = 5701
end

function SCCrossXiuluoTowerSelfActivityInfo:Decode()
	self.cur_layer = MsgAdapter.ReadShort()
	self.immediate_realive_count = MsgAdapter.ReadShort()
	self.total_kill_count = MsgAdapter.ReadInt()
	self.kill_role_count = MsgAdapter.ReadInt()
	self.cur_layer_kill_count = MsgAdapter.ReadInt()
	self.reward_cross_honor = MsgAdapter.ReadInt()
	self.reward_exp = MsgAdapter.ReadLL()
	self.delay_kick_out_timestamp = MsgAdapter.ReadUInt()
	--self.cur_layer_role_num = MsgAdapter.ReadInt()						--当前层玩家的数量
end

--  跨服修罗塔排行榜信息
SCCrossXiuluoTowerRankInfo = SCCrossXiuluoTowerRankInfo or BaseClass(BaseProtocolStruct)

function SCCrossXiuluoTowerRankInfo:__init()
	self.msg_type = 5702
end

function SCCrossXiuluoTowerRankInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.rank = {}
	for i=1, count do
		local vo  = {}
		vo.uuid = MsgAdapter.ReadUUID()
		vo.user_name = MsgAdapter.ReadStrN(32)
		vo.finish_time = MsgAdapter.ReadUShort()
		vo.max_layer = MsgAdapter.ReadShort()
		MsgAdapter.ReadChar()
		vo.camp = MsgAdapter.ReadChar()
		vo.obj_id = MsgAdapter.ReadUShort()
		vo.avatar_key_big = MsgAdapter.ReadUInt()				-- 大头像
		vo.avatar_key_small = MsgAdapter.ReadUInt()				-- 小头像
		vo.photoframe =  MsgAdapter.ReadShort()					-- 头像框 
		vo.sex =  MsgAdapter.ReadShort()						-- 性别
		vo.prof = MsgAdapter.ReadInt()

		self.rank[i] = vo
	end
end

--  跨服修罗塔改变层提示
SCCrossXiuluoTowerChangeLayerNotice = SCCrossXiuluoTowerChangeLayerNotice or BaseClass(BaseProtocolStruct)

function SCCrossXiuluoTowerChangeLayerNotice:__init()
	self.msg_type = 5703
end

function SCCrossXiuluoTowerChangeLayerNotice:Decode()
	self.is_drop_layer = MsgAdapter.ReadInt()
end

-----------------------------------------修罗塔通关回调---------------------------------------------------------------
SCCrossXiuluoTowerUserResult = SCCrossXiuluoTowerUserResult or BaseClass(BaseProtocolStruct)

function SCCrossXiuluoTowerUserResult:__init()
	self.msg_type = 5704
end

function SCCrossXiuluoTowerUserResult:Decode()
	self.max_layer = MsgAdapter.ReadChar()
	self.rank_pos = MsgAdapter.ReadChar()
	self.kill_role_count = MsgAdapter.ReadShort()
	self.finish_time = MsgAdapter.ReadInt()
	self.total_exp = MsgAdapter.ReadLL()
end

-- 跨服修罗塔属性加成
SCCrossXiuluoTowerInfo = SCCrossXiuluoTowerInfo or BaseClass(BaseProtocolStruct)

function SCCrossXiuluoTowerInfo:__init()
	self.msg_type = 5705
	self.uuid = ""
end

function SCCrossXiuluoTowerInfo:Decode()
	self.buy_realive_count = MsgAdapter.ReadInt()
	self.add_gongji_per = MsgAdapter.ReadShort()
	self.add_hp_per = MsgAdapter.ReadShort()
	self.uuid = MsgAdapter.ReadInt() .. "_" .. MsgAdapter.ReadInt()
	self.is_pass_all_layer = MsgAdapter.ReadInt()
end

-- 跨服荣誉值改变
SCCrossHonorChange = SCCrossHonorChange	or BaseClass(BaseProtocolStruct)

function SCCrossHonorChange:__init()
	self.msg_type = 5706
end

function SCCrossHonorChange:Decode()
	self.honor = MsgAdapter.ReadLL()
	self.delta_honor = MsgAdapter.ReadLL()
end


-- 跨服1v1活动信息
SCCrossActivity1V1SelfInfo = SCCrossActivity1V1SelfInfo	or BaseClass(BaseProtocolStruct)

function SCCrossActivity1V1SelfInfo:__init()
	self.msg_type = 5707
end

function SCCrossActivity1V1SelfInfo:Decode()
	self.cross_score_1v1 = MsgAdapter.ReadInt()
	self.cross_day_join_1v1_count = MsgAdapter.ReadInt()

	self.cross_1v1_join_time_reward_flag = MsgAdapter.ReadShort()
	self.cross_1v1_fetch_score_reward_flag = MsgAdapter.ReadShort()

	self.today_buy_times = MsgAdapter.ReadShort()
	self.cur_season = MsgAdapter.ReadShort()

	self.cross_1v1_curr_activity_add_score = MsgAdapter.ReadInt()

	self.cross_lvl_total_join_times = MsgAdapter.ReadShort()
	self.cross_1v1_total_win_times = MsgAdapter.ReadShort()
	self.cross_1v1_gongxun = MsgAdapter.ReadInt()

	self.cross_1v1_dur_win_times = MsgAdapter.ReadShort()
	self.cross_1v1_use_ring = {}
	for i = 1, 2 do
		self.cross_1v1_use_ring[i] = MsgAdapter.ReadChar()
	end

	self.cross_1v1_have_ring ={}
	for i=1, GameEnum.CROSS_1V1_SEASON_MAX do
		local data = MsgAdapter.ReadChar()
		self.cross_1v1_have_ring[i] = data
	end
	self.today_reward_count = MsgAdapter.ReadInt() --今日一奖励次数
end

-- 跨服1V1战斗开始
SCCross1v1FightStart = SCCross1v1FightStart	or BaseClass(BaseProtocolStruct)

function SCCross1v1FightStart:__init()
	self.msg_type = 5708
end

function SCCross1v1FightStart:Decode()
	self.timestamp_type = MsgAdapter.ReadInt()
	self.fight_start_timestmap = MsgAdapter.ReadUInt()
end

-- 跨服3v3主角信息刷新
SCCrossMultiuserChallengeSelfInfoRefresh = SCCrossMultiuserChallengeSelfInfoRefresh	or BaseClass(BaseProtocolStruct)

function SCCrossMultiuserChallengeSelfInfoRefresh:__init()
	self.msg_type = 5709
end

function SCCrossMultiuserChallengeSelfInfoRefresh:Decode()
	self.self_side = MsgAdapter.ReadInt()
	self.kills = MsgAdapter.ReadInt()
	self.assist = MsgAdapter.ReadInt()
	self.dead = MsgAdapter.ReadInt()
end


-- 跨服3v3信息刷新
SCCrossMultiuserChallengeMatchInfoRefresh = SCCrossMultiuserChallengeMatchInfoRefresh or BaseClass(BaseProtocolStruct)

function SCCrossMultiuserChallengeMatchInfoRefresh:__init()
	self.msg_type = 5710
end

function SCCrossMultiuserChallengeMatchInfoRefresh:Decode()
	self.side_score_list = {}
	for i = 1, 2 do
		self.side_score_list[i] = MsgAdapter.ReadInt()
	end
	self.stronghold_list = {}
	-- for i = 1, GameEnum.CROSS_MULTIUSER_CHALLENGE_STRONGHOLD_NUM do
	-- 	local vo = {}
	-- 	vo.obj_id = MsgAdapter.ReadUShort()
	-- 	vo.owner_side = MsgAdapter.ReadShort()
	-- 	vo.cur_gather_side = MsgAdapter.ReadInt()
	-- 	vo.cur_gather_end_time = MsgAdapter.ReadUInt()
	-- 	self.stronghold_list[i] = vo
	-- end
		self.flag_info = {}
		self.flag_info.flag_id = MsgAdapter.ReadInt()
		self.flag_info.flag_side = MsgAdapter.ReadInt()
		self.flag_info.flag_curhp = MsgAdapter.ReadLL()
		self.flag_info.flag_maxhp = MsgAdapter.ReadLL()

end

-- 跨服3v3匹配状态
SCCrossMultiuserChallengeMatchState = SCCrossMultiuserChallengeMatchState or BaseClass(BaseProtocolStruct)

function SCCrossMultiuserChallengeMatchState:__init()
	self.msg_type = 5711
end

function SCCrossMultiuserChallengeMatchState:Decode()
		self.match_state = MsgAdapter.ReadShort()
		self.win_side = MsgAdapter.ReadShort()
		self.next_state_time = MsgAdapter.ReadUInt()
		self.user_info_list = {}
		for i = 1, GameEnum.CROSS_MULTIUSER_CHALLENGE_SIDE_MEMBER_COUNT * 2 do
			local vo ={}
			vo.plat_type = MsgAdapter.ReadShort()
			vo.obj_id = MsgAdapter.ReadUShort()
			vo.role_id = MsgAdapter.ReadInt()
			vo.name = MsgAdapter.ReadStrN(32)
			vo.prof = MsgAdapter.ReadShort()
			vo.sex = MsgAdapter.ReadShort()
			vo.kills = MsgAdapter.ReadShort()
			vo.assist = MsgAdapter.ReadShort()
			vo.dead = MsgAdapter.ReadShort()
			vo.occupy = MsgAdapter.ReadShort()
			vo.origin_score = MsgAdapter.ReadInt()
			vo.add_score = MsgAdapter.ReadInt()
			vo.add_honor = MsgAdapter.ReadInt()
			vo.add_gongxun = MsgAdapter.ReadInt()
			vo.is_mvp = MsgAdapter.ReadInt()
			vo.server_id = MsgAdapter.ReadInt()
			vo.capability = MsgAdapter.ReadLL()
			vo.index = i
			self.user_info_list[i] = vo
		end
end

-- 跨服3v3基本信息
SCCrossMultiuserChallengeBaseSelfSideInfo = SCCrossMultiuserChallengeBaseSelfSideInfo or BaseClass(BaseProtocolStruct)

function SCCrossMultiuserChallengeBaseSelfSideInfo:__init()
	self.msg_type = 5712
end

function SCCrossMultiuserChallengeBaseSelfSideInfo:Decode()
	local user_count = MsgAdapter.ReadInt()
	self.user_list = {}
	for i=1,user_count do
		local vo = {}
		vo.plat_type = MsgAdapter.ReadInt()
		vo.server_id = MsgAdapter.ReadInt()
		vo.uid = MsgAdapter.ReadInt()
		vo.user_name = MsgAdapter.ReadStrN(32)
		vo.sex = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		vo.camp = MsgAdapter.ReadChar()
		MsgAdapter.ReadChar()
		vo.head_icon_big = MsgAdapter.ReadUInt()
		vo.head_icon_small = MsgAdapter.ReadUInt()
		vo.level = MsgAdapter.ReadInt()
		vo.wing_appeid = MsgAdapter.ReadShort()
		MsgAdapter.ReadShort()
		vo.challenge_score = MsgAdapter.ReadInt()
		vo.win_percent = MsgAdapter.ReadInt()
		vo.mvp_count = MsgAdapter.ReadInt()
		vo.capability = MsgAdapter.ReadLL()
		vo.appearance = ProtocolStruct.ReadRoleAppearance()
		vo.prof = MsgAdapter.ReadInt()

		self.user_list[i] = vo
	end
end

-- 跨服3v3角色活动信息
SCCrossMultiuserChallengeSelfActicityInfo = SCCrossMultiuserChallengeSelfActicityInfo or BaseClass(BaseProtocolStruct)

function SCCrossMultiuserChallengeSelfActicityInfo:__init()
	self.msg_type = 5713
end

function SCCrossMultiuserChallengeSelfActicityInfo:Decode()
	self.info = {}
	self.info.challenge_mvp_count = MsgAdapter.ReadInt()
	self.info.challenge_score = MsgAdapter.ReadInt()
	self.info.challenge_total_match_count = MsgAdapter.ReadInt()
	self.info.challenge_win_match_count = MsgAdapter.ReadInt()
	self.info.win_percent = MsgAdapter.ReadShort()
	self.info.today_match_count = MsgAdapter.ReadShort()
	self.info.matching_state = MsgAdapter.ReadInt()
	self.info.join_reward_fetch_flag = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	self.info.cross_3v3_season_reward_use = {}
	for i=1, 2 do
		self.info.cross_3v3_season_reward_use[i] = MsgAdapter.ReadChar()
	end

	self.info.cross_3v3_season_reward = {}
	for i=1, GameEnum.CROSS_1V1_SEASON_MAX do
		self.info.cross_3v3_season_reward[i] = MsgAdapter.ReadChar()
	end

	self.info.gongxun_reward_fetch_flag = MsgAdapter.ReadInt()
	self.info.gongxun_value = MsgAdapter.ReadInt()

	self.info.season_count = MsgAdapter.ReadInt()
	self.info.total_buy_count = MsgAdapter.ReadInt()
	self.info.today_reward_count = MsgAdapter.ReadInt() --今日一奖励次数

end

-- 跨服3v3获取队友位置信息
SCMultiuserChallengeTeamMemberPosList = SCMultiuserChallengeTeamMemberPosList or BaseClass(BaseProtocolStruct)

function SCMultiuserChallengeTeamMemberPosList:__init()
	self.msg_type = 5714
end

function SCMultiuserChallengeTeamMemberPosList:Decode()
	local member_count = MsgAdapter.ReadInt()
	self.team_member_list = {}
	for i = 1, member_count do
		local member_info = {}
		member_info.role_id = MsgAdapter.ReadInt()
		member_info.obj_id = MsgAdapter.ReadUShort()
		member_info.reserved = MsgAdapter.ReadChar()
		member_info.is_leave_scene = MsgAdapter.ReadChar()
		member_info.pos_x = MsgAdapter.ReadShort()
		member_info.pos_y = MsgAdapter.ReadShort()
		member_info.dir = MsgAdapter.ReadFloat()
		member_info.distance = MsgAdapter.ReadFloat()
		member_info.move_speed = MsgAdapter.ReadLL()
		self.team_member_list[i] = member_info
	end
end

CSCrossMultiuserChallengeFetchGongxunReward  = CSCrossMultiuserChallengeFetchGongxunReward  or BaseClass(BaseProtocolStruct)
function CSCrossMultiuserChallengeFetchGongxunReward:__init()
	self.msg_type = 5737
end

function CSCrossMultiuserChallengeFetchGongxunReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteShort(self.seq)
    MsgAdapter.WriteShort(0)
end
--帮派试炼通关标记
SCGuildFBPassInfo = SCGuildFBPassInfo or BaseClass(BaseProtocolStruct)
function SCGuildFBPassInfo:__init()
	self.msg_type = 5735
end

function SCGuildFBPassInfo:Decode()
	self.is_pass = MsgAdapter.ReadInt()
end

--帮派试炼个人信息
SCGuildFBRoleInfo = SCGuildFBRoleInfo or BaseClass(BaseProtocolStruct)
function SCGuildFBRoleInfo:__init()
	self.msg_type = 5736
end

function SCGuildFBRoleInfo:Decode()
	self.rank = MsgAdapter.ReadInt()
	self.hurt_val = MsgAdapter.ReadLL()
end



-- 请求开始跨服
CSCrossStartReq = CSCrossStartReq or BaseClass(BaseProtocolStruct)
function CSCrossStartReq:__init()
	self.msg_type = 5750

	self.cross_activity_type = 0
	self.param = 0
end
function CSCrossStartReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.cross_activity_type)
	MsgAdapter.WriteShort(self.param)


end

-- 跨服修罗塔报名
CSCrossXiuluoTowerJoinReq = CSCrossXiuluoTowerJoinReq or BaseClass(BaseProtocolStruct)
function CSCrossXiuluoTowerJoinReq:__init()
	self.msg_type = 5751
end
function CSCrossXiuluoTowerJoinReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.req_type)
end

-- 跨服修罗塔购买buff
CSCrossXiuluoTowerBuyBuff = CSCrossXiuluoTowerBuyBuff or BaseClass(BaseProtocolStruct)
function CSCrossXiuluoTowerBuyBuff:__init()
	self.msg_type = 5752
	self.is_buy_realive_count = 0
	self.is_use_gold_bind = 0

end
function CSCrossXiuluoTowerBuyBuff:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.is_buy_realive_count)
	MsgAdapter.WriteShort(self.is_use_gold_bind)
end

-- 跨服1v1匹配请求
CSCrossMatch1V1Req = CSCrossMatch1V1Req or BaseClass(BaseProtocolStruct)
function CSCrossMatch1V1Req:__init()
	self.msg_type = 5753

end
function CSCrossMatch1V1Req:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 跨服1v1战斗准备
CSCross1v1FightReady = CSCross1v1FightReady or BaseClass(BaseProtocolStruct)
function CSCross1v1FightReady:__init()
	self.msg_type = 5754

end
function CSCross1v1FightReady:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 跨服1v1领取奖励
CSCross1v1FetchRewardReq = CSCross1v1FetchRewardReq or BaseClass(BaseProtocolStruct)
function CSCross1v1FetchRewardReq:__init()
	self.msg_type = 5755
end

function CSCross1v1FetchRewardReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.fetch_type)
	MsgAdapter.WriteShort(self.seq)
end

-- 跨服1v1购买次数
CSCross1v1BuyTimeReq = CSCross1v1BuyTimeReq or BaseClass(BaseProtocolStruct)
function CSCross1v1BuyTimeReq:__init()
	self.msg_type = 5762
end

function CSCross1v1BuyTimeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--穿戴戒指
CSCross1v1WearRingReq = CSCross1v1WearRingReq or BaseClass(BaseProtocolStruct)
function CSCross1v1WearRingReq:__init()
	self.msg_type = 5763
end

function CSCross1v1WearRingReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opr_type)
	MsgAdapter.WriteInt(self.ring_seq)
end


--穿戴令牌
CSCrossPVPWearCardReq = CSCrossPVPWearCardReq or BaseClass(BaseProtocolStruct)
function CSCrossPVPWearCardReq:__init()
	self.msg_type = 5765
end

function CSCrossPVPWearCardReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opr_type)
	MsgAdapter.WriteInt(self.ring_seq)
end



--跨服3v3请求匹配（队长发起）
CSCrossMultiuserChallengeMatchgingReq = CSCrossMultiuserChallengeMatchgingReq or BaseClass(BaseProtocolStruct)
function CSCrossMultiuserChallengeMatchgingReq:__init()
	self.msg_type = 5756
end

function CSCrossMultiuserChallengeMatchgingReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 跨服3v3请求同队基本信息
CSCrossMultiuserChallengeGetBaseSelfSideInfo = CSCrossMultiuserChallengeGetBaseSelfSideInfo or BaseClass(BaseProtocolStruct)
function CSCrossMultiuserChallengeGetBaseSelfSideInfo:__init()
	self.msg_type = 5757
end

function CSCrossMultiuserChallengeGetBaseSelfSideInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 跨服3v3获取每日奖励
CSCrossMultiuserChallengeFetchDaycountReward = CSCrossMultiuserChallengeFetchDaycountReward or BaseClass(BaseProtocolStruct)
function CSCrossMultiuserChallengeFetchDaycountReward:__init()
	self.msg_type = 5758
end

function CSCrossMultiuserChallengeFetchDaycountReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.seq)
end

-- 跨服3v3取消匹配
CSCrossMultiuerChallengeCancelMatching = CSCrossMultiuerChallengeCancelMatching or BaseClass(BaseProtocolStruct)
function CSCrossMultiuerChallengeCancelMatching:__init()
	self.msg_type = 5759
end

function CSCrossMultiuerChallengeCancelMatching:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 跨服3v3，请求队友位置信息
CSMultiuserChallengeReqSideMemberPos = CSMultiuserChallengeReqSideMemberPos or BaseClass(BaseProtocolStruct)
function CSMultiuserChallengeReqSideMemberPos:__init()
	self.msg_type = 5760
end

function CSMultiuserChallengeReqSideMemberPos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

SCCross1vNFightingResultToRole = SCCross1vNFightingResultToRole or BaseClass(BaseProtocolStruct)
function SCCross1vNFightingResultToRole:__init()
	self.msg_type = 5732
	self.result = 0
end

function SCCross1vNFightingResultToRole:Decode()
	self.result = MsgAdapter.ReadShort()
end

CSCross1vNInfo = CSCross1vNInfo or BaseClass(BaseProtocolStruct)
function CSCross1vNInfo:__init()
	self.msg_type = 5764
end

function CSCross1vNInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

SCCross1vNPersonInfo = SCCross1vNPersonInfo or BaseClass(BaseProtocolStruct)
function SCCross1vNPersonInfo:__init()
	self.msg_type = 5733
end

function SCCross1vNPersonInfo:Decode()
	self.cross_1vn_best_continue_win_times = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
end

--5723  获得BUFF广播
SCCrossXiuluoTowerBuffInfo = SCCrossXiuluoTowerBuffInfo or BaseClass(BaseProtocolStruct)
function SCCrossXiuluoTowerBuffInfo:__init()
	self.msg_type = 5723
	self.obj_id = 0
	self.buff_num = 0
	self.next_time = 0
end

function SCCrossXiuluoTowerBuffInfo:Decode()
	self.obj_id = MsgAdapter.ReadUShort()
	self.buff_num = MsgAdapter.ReadShort()
	self.next_time = MsgAdapter.ReadUInt()
end

--下发跨服排行榜列表
SCGetCrossPersonRankListAck = SCGetCrossPersonRankListAck or BaseClass(BaseProtocolStruct)
function SCGetCrossPersonRankListAck:__init()
	self.msg_type = 5740
	self.rank_list = {}
end

function SCGetCrossPersonRankListAck:Decode()
	self.rank_list = {}
	self.rank_type = MsgAdapter.ReadInt()
	self.self_rank = MsgAdapter.ReadInt()
	self.self_value = MsgAdapter.ReadLL()
	local count = MsgAdapter.ReadInt()
	local role, fashion_photoframe, image_cfg
	for i = 1, count do
		role = {}
		role.plat_type = MsgAdapter.ReadInt()
		role.user_id = MsgAdapter.ReadInt()
		role.user_name = MsgAdapter.ReadStrN(32)
		role.level = MsgAdapter.ReadShort()
		role.vip_level = MsgAdapter.ReadShort()
		MsgAdapter.ReadChar()
		role.sex = MsgAdapter.ReadChar()
		role.camp = MsgAdapter.ReadShort()
		role.jingjie = MsgAdapter.ReadInt()
		role.worship_count = MsgAdapter.ReadInt()
		role.exp = MsgAdapter.ReadLL()
		role.flexible_int = MsgAdapter.ReadInt()
		role.rank_value = MsgAdapter.ReadLL()
		role.flexible_ll = MsgAdapter.ReadLL()
		role.flexible_name = MsgAdapter.ReadStrN(32)
        role.server_id = MsgAdapter.ReadInt()
        role.fame = MsgAdapter.ReadInt()

		-- 头像框
		fashion_photoframe = MsgAdapter.ReadInt()
		image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, fashion_photoframe)
		role.fashion_photoframe = image_cfg and image_cfg.resouce or 0

		role.shield_vip_flag = MsgAdapter.ReadChar()	--隐藏VIP等级信息
		role.reserved1 = MsgAdapter.ReadChar()			--预留1
		role.reserved2 = MsgAdapter.ReadShort()			--预留2
		role.prof = MsgAdapter.ReadInt()


		self.rank_list[i] = role
	end
end

-- 获取跨服排行榜列表
CSCrossGetPersonRankList = CSCrossGetPersonRankList or BaseClass(BaseProtocolStruct)
function CSCrossGetPersonRankList:__init()
	self.msg_type = 5741
	self.rank_type = 0
end

function CSCrossGetPersonRankList:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.rank_type)
end


-------------牧场---------------------------------------------------------------


CSCross1v1MatchResultReq = CSCross1v1MatchResultReq or BaseClass(BaseProtocolStruct)
function CSCross1v1MatchResultReq:__init()
	self.msg_type = 5748
	self.req_type = 0
end

function CSCross1v1MatchResultReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.req_type)
end