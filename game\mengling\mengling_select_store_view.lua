MengLingInlayStroeView = MengLingInlayStroeView or BaseClass(SafeBaseView)

function MengLingInlayStroeView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -10), sizeDelta = Vector2(600, 604)})
	self:AddViewResource(0, "uis/view/function_collections_ui/mengling_prefab", "layout_mengling_baishi_select")
end

function MengLingInlayStroeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Charm.DaoHangKeYinStoreTitle

	if not self.stone_list_view then
		self.stone_list_view = AsyncListView.New(MengLingSelectBaoShiItemRender, self.node_list["ph_select_baoshi_list_view"])
		self.stone_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItem<PERSON><PERSON><PERSON>, self))
	end
end

function MengLingInlayStroeView:ReleaseCallBack()
	if self.stone_list_view then
		self.stone_list_view:DeleteMe()
		self.stone_list_view = nil
	end

	self.change_data_list = nil
	self.select_seq = nil
	self.select_slot = nil
	self.select_hole = nil
end

function MengLingInlayStroeView:SetData(seq, slot, hole, data_list)
	self.select_seq = seq
	self.select_slot = slot
	self.select_hole = hole
	self.change_data_list = data_list
end

function MengLingInlayStroeView:OnFlush()
	if self.stone_list_view and self.change_data_list then
		self.stone_list_view:SetDataList(self.change_data_list)
		self.stone_list_view:CancelSelect()
	end
end

function MengLingInlayStroeView:OnSelectItemHandler(item, cell_index, is_default, is_click)
	if not is_click or nil == item or nil == item.data then
		return
	end

	MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_EQUIP_STONE,	self.select_seq,
	self.select_slot,
	self.select_hole,
	item.data.index)
	self:Close()
end

------------------------------------------MengLingSelectBaoShiItemRender-----------------------------------------
MengLingSelectBaoShiItemRender = MengLingSelectBaoShiItemRender or BaseClass(BaseRender)

function MengLingSelectBaoShiItemRender:LoadCallBack()
	if not self.arrow_tweener then
		local tween_arrow = self.node_list["img_remind"]
		RectTransform.SetAnchoredPositionXY(tween_arrow.rect, -8, -5)
		self.arrow_tweener = tween_arrow.gameObject.transform:DOAnchorPosY(0, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end

	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["ph_item"])
	end
end

function MengLingSelectBaoShiItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function MengLingSelectBaoShiItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData(self.data)
	local store_cfg = MengLingWGData.Instance:GertMengLingStoreCfg(self.data.item_id)

	if not IsEmptyTable(store_cfg) then
		local attr_data = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(store_cfg, "attr_id", "attr_value")

		if not IsEmptyTable(attr_data) then
			for i = 1, 4 do
				local data = attr_data[i]
			    local has_data = not IsEmptyTable(data)

				if has_data then
					self.node_list["lbl_attr" .. i].text.text = data.attr_name .. "  " .. data.value_str
				end
				
				self.node_list["lbl_attr" .. i]:CustomSetActive(has_data)
			end
		end
	end
end