﻿using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public static class ScrollRectExtensions
{
    public static Tweener DoVerticalPosition(
this ScrollRect scroll, float from, float to, float duration, Action complete)
    {
        //from = Mathf.Clamp(from, 0, 1);
        //to = Mathf.Clamp(to, 0, 1);
        Tweener t = DOTween.To(() => from, x => from = x, to, duration);
        t.OnUpdate(() =>
        {
            if (null != scroll)
            {
                scroll.verticalNormalizedPosition = from;
            }
        });
        t.OnComplete(() =>
        {
            scroll.verticalNormalizedPosition = to;
            if (null != complete)
            {
                complete();
            }
        });
        return t;
    }
    public static Tweener DoHorizontalPosition(
this ScrollRect scroll, float from, float to, float duration, Action complete)
    {
        //from = Mathf.Clamp(from, 0, 1);
        //to = Mathf.Clamp(to, 0, 1);
        Tweener t = DOTween.To(() => from, x => from = x, to, duration);
        t.OnUpdate(() =>
        {
            if (null != scroll)
            {
                scroll.horizontalNormalizedPosition = from;
            }
        });
        t.OnComplete(() =>
        {
            scroll.horizontalNormalizedPosition = to;
            if (null != complete)
            {
                complete();
            }
        });
        return t;
    }

}
