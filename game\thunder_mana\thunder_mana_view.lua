ThunderManaView = ThunderManaView or BaseClass(SafeBaseView)

local SHOW_TYPE = 
{
    LEVEL = 1,            -- 等级
    GRADE = 2,            -- 阶级
    STAR = 3,             -- 星星
}

local UP_STAR_TYPE = {
	CONST = 0,				-- 消耗材料进阶
	SWALLOW = 1,			-- 吞噬进阶
}

local UP_LEVEL_DELT_TIME = 0.5

function ThunderManaView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
    self:SetMaskBg()

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/thunder_mana_ui_prefab", "layout_thunder_mana")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function ThunderManaView:__delete()
end

function ThunderManaView:OpenCallBack()
	self.cur_select_view_type = self.view_name == GuideModuleName.ShadyThunderView and ThunderManaWGData.ThunderType.ShadyType or 
							ThunderManaWGData.ThunderType.SunType
end

function ThunderManaView:CloseCallBack()
	self:StopUpLevelOperator()
end

function ThunderManaView:LoadCallBack()
	self:InitViewPanelInfo()

	if self.equip_slot_list == nil then
        self.equip_slot_list = {}
        for i = 0, ThunderManaWGData.EquipCount - 1 do
            self.equip_slot_list[i] = ThunderManaEquipSlotCell.New(self.node_list["equip_slot_" ..i])
            self.equip_slot_list[i]:SetIndex(i)
            self.equip_slot_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectEquipSlotCallBack, self))
        end
    end

	if not self.part_level_attr_list then
        self.part_level_attr_list = {}
        local parent_node = self.node_list["part_level_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.part_level_attr_list[i] = cell
        end
    end

	if not self.part_level_cost_cell then
		self.part_level_cost_cell = ItemCell.New(self.node_list["part_level_cost"])
	end

	if not self.part_grade_cost_cell then
		self.part_grade_cost_cell = ItemCell.New(self.node_list["part_grade_cost"])
	end
	
	if not self.cur_star_list then
		self.cur_star_list = {}
		for i = 1, 5 do
			self.cur_star_list[i] = self.node_list["cur_star_" .. i]
		end
	end

	if not self.next_star_list then
		self.next_star_list = {}
		for i = 1, 5 do
			self.next_star_list[i] = self.node_list["next_star_" .. i]
		end
	end

	if not self.part_star_attr_list then
        self.part_star_attr_list = {}
        local parent_node = self.node_list["part_star_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.part_star_attr_list[i] = cell
        end
    end

	-- 特殊一个
	if not self.star_compose_aim then
		self.star_compose_aim = ThunderManaStarUpItemRender.New(self.node_list.star_compose_aim)
		self.star_compose_aim:SetClickCallBack(BindTool.Bind1(self.OnSelectStarUpItem, self))
	end

	-- 升星材料
	if not self.star_cell_list then
		self.star_cell_list = {}

		for i = 1, 3 do
			self.star_cell_list[i] = {}
			local cell = ThunderManaStarUpItemRender.New(self.node_list[string.format("star_compose_cell_%d", i)])
			cell:SetClickCallBack(BindTool.Bind1(self.OnSelectStarUpItem, self))
			self.star_cell_list[i].cell = cell
			self.star_cell_list[i].root = self.node_list[string.format("star_compose_cell_root_%d", i)]
			self.star_cell_list[i].cell_txt = self.node_list[string.format("star_compose_cell_txt_%d", i)]
		end
	end

	-- 升星吞噬材料列表
	if not self.star_up_root_type_swallow_list then
		self.star_up_root_type_swallow_list = AsyncListView.New(ThunderManaStarUpSwallowItemRender, self.node_list.star_up_root_type_swallow_list)
		self.star_up_root_type_swallow_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectStarUpSwallowItem, self))
		self.star_up_root_type_swallow_list:SetUseRenderClick(true)
	end

	if not self.suit_list then
		self.suit_list = AsyncListView.New(ThunderManSuitCell, self.node_list.suit_list)
		self.suit_list:SetStartZeroIndex(true)
	end

	if not self.part_star_attr_level then
        self.part_star_attr_level = CommonAddAttrRender.New(self.node_list.part_star_attr_level)
    end

    if not self.part_star_attr_grade then
        self.part_star_attr_grade = CommonAddAttrRender.New(self.node_list.part_star_attr_grade)
    end

	for i = 1, 3 do
		self.node_list["part_type_btn_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickChangeShowRoot, self, i))
	end

	XUI.AddClickEventListener(self.node_list["part_level_bag_btn"], BindTool.Bind(self.OnClickPartLevelBag, self))
	XUI.AddClickEventListener(self.node_list["part_level_once_up_btn"], BindTool.Bind(self.OnClickAutoUpLevel, self))
	XUI.AddClickEventListener(self.node_list["part_grade_up_btn"], BindTool.Bind(self.OnClickUpGrade, self))
	XUI.AddClickEventListener(self.node_list["part_type_lock_btn_2"], BindTool.Bind(self.OnClickLock, self))
	XUI.AddClickEventListener(self.node_list["part_type_lock_btn_3"], BindTool.Bind(self.OnClickLock, self))
	XUI.AddClickEventListener(self.node_list["chang_view_btn"], BindTool.Bind(self.OnClickChangeView, self))
	XUI.AddClickEventListener(self.node_list["all_attr_btn"], BindTool.Bind(self.OnClickOpenAllAttrView, self))
	XUI.AddClickEventListener(self.node_list.btn_star_up, BindTool.Bind2(self.ClickComposeStartUp, self))
	XUI.AddClickEventListener(self.node_list.btn_quick_star_up, BindTool.Bind2(self.ClickComposeQuickStartUp, self))

    XUI.AddClickEventListener(self.node_list.compose_look_btn, BindTool.Bind2(self.ComposeEvolvePreview, self))
    XUI.AddClickEventListener(self.node_list.compose_batch_btn, BindTool.Bind2(self.OpenBatchQuickSpView, self))

	XUI.AddClickEventListener(self.node_list["supreme_btn"], BindTool.Bind(self.OnClickSupremeBtn, self))
    XUI.AddClickEventListener(self.node_list["halo_btn"], BindTool.Bind(self.ClickHaloBtn, self))

	-- 绑定红点
	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
	RemindManager.Instance:Bind(self.remind_change, RemindName.SupremeFields)
	RemindManager.Instance:Bind(self.remind_change, RemindName.NewAppearance_WaiGuan_Halo)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ShadyThunderView, self.get_guide_ui_event)
	
	self.is_auto_up_level = false
end

function ThunderManaView:InitViewPanelInfo()
    local bundle, asset = ResPath.GetRawImagesJPG("a3_lf_bj_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    local select_type = self.view_name == GuideModuleName.ShadyThunderView and 0 or 1
	self.node_list.title_view_name.text.text = Language.ThunderMana.ThunderTitleName[select_type]

	local bg_name_1 = select_type == 0 and "a3_lf_bj_4" or "a3_lf_bj_5"
	local bundle_1, asset_1 = ResPath.GetRawImagesPNG(bg_name_1)
	if self.node_list.thunder_type_bg then
		self.node_list["thunder_type_bg"].raw_image:LoadSprite(bundle_1, asset_1, function()
			self.node_list["thunder_type_bg"].raw_image:SetNativeSize()
		end)
	end

	self.node_list.shady_effect:SetActive(select_type == 0)
	self.node_list.sun_effect:SetActive(select_type == 1)
	self.node_list.chang_view_btn_text.tmp.text = select_type == 0 and Language.ThunderMana.BtnNameStr[5] or Language.ThunderMana.BtnNameStr[4]
end

function ThunderManaView:ReleaseCallBack()
	self:CancelAutoLevelTimer()

	if self.equip_slot_list then
        for k, v in pairs(self.equip_slot_list) do
            v:DeleteMe()
        end
        self.equip_slot_list = nil
    end

	if self.part_level_attr_list then
        for k,v in pairs(self.part_level_attr_list) do
            v:DeleteMe()
        end
        self.part_level_attr_list = nil
    end

	if self.part_level_cost_cell then
		self.part_level_cost_cell:DeleteMe()
		self.part_level_cost_cell = nil
	end

	if self.part_grade_cost_cell then
		self.part_grade_cost_cell:DeleteMe()
		self.part_grade_cost_cell = nil
	end
	
	if self.part_star_attr_list then
        for k,v in pairs(self.part_star_attr_list) do
            v:DeleteMe()
        end
        self.part_star_attr_list = nil
    end

	if self.star_compose_aim then
        self.star_compose_aim:DeleteMe()
        self.star_compose_aim = nil
    end

	if self.star_cell_list and #self.star_cell_list > 0 then
        for i, v in ipairs(self.star_cell_list) do
            if v and v.cell then
                v.cell:DeleteMe()
                v.cell = nil
            end
        end

        self.star_cell_list = nil
    end

	if self.suit_list then
		self.suit_list:DeleteMe()
		self.suit_list = nil
	end

	if self.part_star_attr_level then
        self.part_star_attr_level:DeleteMe()
        self.part_star_attr_level = nil
    end

    if self.part_star_attr_grade then
        self.part_star_attr_grade:DeleteMe()
        self.part_star_attr_grade = nil
    end

	if self.star_up_root_type_swallow_list then
		self.star_up_root_type_swallow_list:DeleteMe()
		self.star_up_root_type_swallow_list = nil
	end

	if self.remind_change  then
		RemindManager.Instance:UnBind(self.remind_change)
	end
	self.remind_change = nil

	self.cur_select_view_type = nil
	self.select_equip_data = nil
	self.cur_right_show_type = nil
	self.is_auto_up_level = false
	self.cur_star_list = nil
	self.next_star_list = nil
	self.star_up_list = nil				-- 当前的升星列表
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ShadyThunderView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function ThunderManaView:ShowIndexCallBack(index)

end

function ThunderManaView:OnFlush(param_t, index)
	if not self.cur_select_view_type then
		return
	end

  	self:OnFlushEquipSlot()
	self:OnFlushSuitInfo()
	self:FlushEquipRightPanel()

	local cap = ThunderManaWGData.Instance:GetThunderCapValue(self.cur_select_view_type)
	self.node_list.thunder_cap:SetActive(cap > 0)
	self.node_list.cap_value.tmp.text = cap

	local batch_list = ThunderManaWGData.Instance:GetQuickBagComposeData()
	self.node_list.compose_batch_red:CustomSetActive((not IsEmptyTable(batch_list)))
	self.node_list.compose_batch_btn:CustomSetActive((not IsEmptyTable(batch_list)))
end

function ThunderManaView:OnFlushEquipSlot()
	local equip_info = ThunderManaWGData.Instance:GetAllEquipPartCfgByType(self.cur_select_view_type)
	for k, v in pairs(self.equip_slot_list) do
        v:SetData(equip_info[k])
    end

	if not self.select_equip_data then
		local click_cell_index = 0
		for k, v in pairs(equip_info) do
			local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(v.seq, v.part)
            if part_info and part_info.item_id > 0 then
                click_cell_index = k
                break
            end
        end

		self.equip_slot_list[click_cell_index]:OnClick()
	end

	self:FlushEquipRightPanel()
end

function ThunderManaView:OnFlushSuitInfo()
	local select_type = self.view_name == GuideModuleName.ShadyThunderView and 0 or 1
	local suit_cfg = ThunderManaWGData.Instance:GetThunderSuitCfg(select_type)
	self.suit_list:SetDataList(suit_cfg)

	local reward_index = ThunderManaWGData.Instance:GetThunderGetSuitRewardIndex(select_type)
	if reward_index >= 0 then
		self.suit_list:JumpToIndex(reward_index, 3)
		local cur_suit_cfg = ThunderManaWGData.Instance:GetThunderSuitCfg(select_type, reward_index)
		local need_star = cur_suit_cfg.need_star or 0
		local need_num = cur_suit_cfg.need_num or 0
		local equip_count = ThunderManaWGData.Instance:GetThunderGetEquipNum(select_type, need_star)
		local color = equip_count >= need_num and COLOR3B.GREEN or COLOR3B.RED
		local equip_num_str = ToColorStr(equip_count .. "/" .. need_num, color)
		self.node_list.suit_condition_str.tmp.text = string.format(Language.ThunderMana.SuitStr[1], cur_suit_cfg.text, equip_num_str) 
	else
		self.node_list.suit_condition_str.tmp.text = Language.ThunderMana.SuitStr[0]
	end
end

function ThunderManaView:FlushEquipRightPanel()
    if not self.select_equip_data then
        return
    end

	local level_remind = ThunderManaWGData.Instance:GetEuqipSlotLevelRemind(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_level_remind:SetActive(level_remind)
	
	local grade_remind = ThunderManaWGData.Instance:GetEuqipSlotGradeRemind(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_grade_remind:SetActive(grade_remind)

	local star_remind = ThunderManaWGData.Instance:GetEuqipSlotStarRemind(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_star_remind:SetActive(star_remind)
	
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_type_lock_btn_2:SetActive(not part_info or part_info.item_id <= 0)
	self.node_list.part_type_lock_btn_3:SetActive(not part_info or part_info.item_id <= 0)

	self:FlushEuqipLevelPanel()
	self:FlushEquipGradePanel()
	self:FlushEquipStarPanel()
end

function ThunderManaView:FlushEuqipLevelPanel()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_level_no_equip:SetActive(not part_info or part_info.item_id <= 0)
	self.node_list.part_level_info:SetActive(part_info and part_info.item_id > 0)

	if not part_info or part_info.item_id <= 0 then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(part_info.item_id)
	self.node_list.part_level_name_str.tmp.text = string.format(Language.ThunderMana.LevelName, NumberToChinaNumber(part_info.star), item_cfg and item_cfg.name or "", part_info.level)

    local level_attr_list = ThunderManaWGData.Instance:GetEquipPartLevelAttrList(part_info.seq, part_info.part, part_info.level)
	for k,v in pairs(self.part_level_attr_list) do
        v:SetData(level_attr_list[k])
    end
	
	local cur_level_cfg = ThunderManaWGData.Instance:GetEquipPartLevelCfg(part_info.seq, part_info.part, part_info.level)
	local next_level_cfg = ThunderManaWGData.Instance:GetEquipPartLevelCfg(part_info.seq, part_info.part, part_info.level + 1)

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	local cur_level = part_info.level
	local is_max_level = not next_level_cfg
	local part_level_limit = equip_cfg and equip_cfg.part_level_limit or 0
	self.node_list.part_level_cost:SetActive(not (is_max_level or cur_level >= part_level_limit))
	self.node_list.part_level_cost_num:SetActive(not (is_max_level or cur_level >= part_level_limit))
	self.node_list.part_level_max_level:SetActive(is_max_level or cur_level >= part_level_limit)
	self.node_list.part_level_spe_tip:SetActive(not is_max_level and cur_level >= part_level_limit)
	self.node_list.part_level_once_up_remind:SetActive(false)
	if not (is_max_level or cur_level >= part_level_limit) and cur_level_cfg then
		self.part_level_cost_cell:SetData({item_id = cur_level_cfg.cost_item_id})
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		local color = item_num >= cur_level_cfg.cost_item_num and COLOR3B.GREEN or COLOR3B.RED
		self.node_list["part_level_cost_num"].tmp.text = ToColorStr(item_num .. "/" .. cur_level_cfg.cost_item_num, color)
		self.node_list.part_level_once_up_remind:SetActive(item_num >= cur_level_cfg.cost_item_num)
	end

	local bag_remind = ThunderManaWGData.Instance:GetEuqipSlotIsBetterWear(part_info.seq, part_info.part)
	self.node_list.part_level_bag_remind:SetActive(bag_remind)
end

function ThunderManaView:FlushEquipGradePanel()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_grade_no_equip:SetActive(not part_info or part_info.item_id <= 0)
	self.node_list.part_grade_info:SetActive(part_info and part_info.item_id > 0)

	if not part_info or part_info.item_id <= 0 then
		return
	end

	local cur_grade_cfg = ThunderManaWGData.Instance:GetEquipPartGradeCfg(part_info.seq, part_info.part, part_info.grade)
	local next_grade_cfg = ThunderManaWGData.Instance:GetEquipPartGradeCfg(part_info.seq, part_info.part, part_info.grade + 1)
	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)

	local cur_grade = part_info.grade
	local is_max_grade = not next_grade_cfg
	local part_grade_limit = equip_cfg and equip_cfg.part_grade_limit or 0

	self.node_list.part_grade_cur_num.tmp.text = string.format(Language.ThunderMana.GradeName, NumberToChinaNumber(part_info.grade))
	self.node_list.part_grade_next_num.tmp.text = string.format(Language.ThunderMana.GradeName, NumberToChinaNumber(part_info.grade + 1))
	self.node_list.part_grade_next_root:SetActive(not is_max_grade)
	self.node_list.part_grade_next_attr_group:SetActive(not is_max_grade)
	if is_max_grade then
		self.node_list.part_grade_cur_root.transform.localScale = Vector3(1, 1, 1)
		self.node_list.part_grade_cur_root.transform.localPosition = Vector3(0, 230, 0)
		self.node_list.part_grade_cur_attr_group.transform.localPosition = Vector3(0, 0, 0)
	else
		self.node_list.part_grade_cur_root.transform.localScale = Vector3(0.8, 0.8, 0.8)
		self.node_list.part_grade_cur_root.transform.localPosition = Vector3(-110, 230, 0)
		self.node_list.part_grade_cur_attr_group.transform.localPosition = Vector3(-75, 0, 0)
	end

	local cur_level_attr_per = cur_grade_cfg and cur_grade_cfg.attr_per or 0
	local cur_star_attr_per = cur_grade_cfg and cur_grade_cfg.equip_attr_per or 0
	local next_level_attr_per = next_grade_cfg and next_grade_cfg.attr_per or 0
	local next_star_attr_per = next_grade_cfg and next_grade_cfg.equip_attr_per or 0
	self.node_list.part_grade_cur_attr_name_1.tmp.text = Language.ThunderMana.AttrName[0]
	self.node_list.part_grade_cur_attr_name_2.tmp.text = Language.ThunderMana.AttrName[1]
	self.node_list.part_grade_cur_attr_num_1.tmp.text = (cur_level_attr_per / 100) .. "%"
	self.node_list.part_grade_cur_attr_num_2.tmp.text = (cur_star_attr_per / 100) .. "%"
	self.node_list.part_grade_next_attr_num_1.tmp.text = (next_level_attr_per / 100) .. "%"
	self.node_list.part_grade_next_attr_num_2.tmp.text = (next_star_attr_per / 100) .. "%"

	self.node_list.part_grade_cost:SetActive(not (is_max_grade or cur_grade >= part_grade_limit))
	self.node_list.part_grade_cost_num:SetActive(not (is_max_grade or cur_grade >= part_grade_limit))
	self.node_list.part_grade_max:SetActive(is_max_grade or cur_grade >= part_grade_limit)
	self.node_list.part_grade_spe_tip:SetActive(not is_max_grade and cur_grade >= part_grade_limit)
	self.node_list.part_grade_up_remind:SetActive(false)

	if not (is_max_grade or cur_grade >= part_grade_limit) and cur_grade_cfg then
		self.part_grade_cost_cell:SetData({item_id = cur_grade_cfg.cost_item_id})
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
		local color = item_num >= cur_grade_cfg.cost_item_num and COLOR3B.GREEN or COLOR3B.RED
		self.node_list["part_grade_cost_num"].tmp.text = ToColorStr(item_num .. "/" .. cur_grade_cfg.cost_item_num, color)
		self.node_list.part_grade_up_remind:SetActive(item_num >= cur_grade_cfg.cost_item_num)
	end
end

function ThunderManaView:FlushEquipStarPanel()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_star_no_equip:SetActive(not part_info or part_info.item_id <= 0)
	self.node_list.part_star_info:SetActive(part_info and part_info.item_id > 0)

	if not part_info or part_info.item_id <= 0 then
		return
	end

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return
	end

	local next_star_id = equip_cfg and equip_cfg.next_star_id
	local next_equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(next_star_id)
	local is_max_star = not next_equip_cfg
	local cur_star = part_info.star
	self.node_list.part_star_next_root:SetActive(not is_max_star)
	self.node_list.part_star_next_name_str:SetActive(not is_max_star)
	self.node_list.part_star_cur_root.transform.localPosition = Vector3(is_max_star and 0 or -105, 284, 0)
	self.node_list.part_star_cur_name_str.transform.localPosition = Vector3(is_max_star and 0 or -105, 242, 0)

	self.node_list.part_star_cur_name_str.tmp.text = string.format(Language.ThunderMana.StarName, NumberToChinaNumber(cur_star or 0))
	self.node_list.part_star_next_name_str.tmp.text = string.format(Language.ThunderMana.StarName, NumberToChinaNumber(next_equip_cfg and next_equip_cfg.star or 0))
	
	local cur_star_res_list = GetSpecialStarImgResByStar4(cur_star)
	local no_have_star = "a3_ty_xx_zc0"
	for k, v in pairs(self.cur_star_list) do
		v:CustomSetActive(cur_star_res_list[k] ~= no_have_star)
		v.image:LoadSprite(ResPath.GetCommonImages(cur_star_res_list[k]))
	end

	if not is_max_star then
		local next_star_res_list = GetSpecialStarImgResByStar4(next_equip_cfg.star or 0)
		local no_have_star = "a3_ty_xx_zc0"
		for k,v in pairs(self.next_star_list) do
			v:CustomSetActive(next_star_res_list[k] ~= no_have_star)
			v.image:LoadSprite(ResPath.GetCommonImages(next_star_res_list[k]))
		end
	end

	local star_attr_list = ThunderManaWGData.Instance:GetEquipPartStarAttrList(part_info.item_id)
    for k,v in pairs(self.part_star_attr_list) do
        v:SetData(star_attr_list[k])
    end

	-- 计算等级上限
    local now_level_limit = equip_cfg and equip_cfg.part_level_limit or 0
    local next_level_limit = next_equip_cfg and next_equip_cfg.part_level_limit or 0
	local level_attr_value = now_level_limit
	local level_add_value =  next_level_limit - now_level_limit
    local level_limit_data = {}
    level_limit_data.attr_str = 0
    level_limit_data.attr_value = level_attr_value
    level_limit_data.add_value = level_add_value
    self.part_star_attr_level:SetRealHideNext(level_attr_value == 0)
    self.part_star_attr_level:SetData(level_limit_data)
    self.part_star_attr_level:ResetName(Language.ThunderMana.AttrName[2])
	self.part_star_attr_level:ResetAttrVlaue(string.format(Language.ThunderMana.AttrName[4], level_attr_value))
	if level_add_value > 0 then
		self.part_star_attr_level:ResetAddAttrVlaue(string.format(Language.ThunderMana.AttrName[4], next_level_limit))
	end

    -- 计算阶级上限
	local now_grade_limit = equip_cfg and equip_cfg.part_grade_limit or 0
    local next_grade_limit = next_equip_cfg and next_equip_cfg.part_grade_limit or 0
	local grade_attr_value = now_grade_limit
	local grade_add_value =  next_grade_limit - now_grade_limit
    local grade_limit_data = {}
    grade_limit_data.attr_str = 0
    grade_limit_data.attr_value = grade_attr_value
    grade_limit_data.add_value = grade_add_value
    self.part_star_attr_grade:SetRealHideNext(grade_attr_value == 0)
    self.part_star_attr_grade:SetData(grade_limit_data)
    self.part_star_attr_grade:ResetName(Language.ThunderMana.AttrName[3])
	self.part_star_attr_grade:ResetAttrVlaue(string.format(Language.ThunderMana.AttrName[5], grade_attr_value))
	if grade_add_value > 0 then
		self.part_star_attr_grade:ResetAddAttrVlaue(string.format(Language.ThunderMana.AttrName[5], next_grade_limit))
	end
	

	self.node_list.part_star_max:SetActive(is_max_star)
	self.node_list.part_star_spend_panel:SetActive(not is_max_star)
	self.star_up_list = {}

	local up_star_type = equip_cfg and equip_cfg.up_star_type or 0
	self.node_list.star_up_root_type_const:CustomSetActive(up_star_type == UP_STAR_TYPE.CONST)
	self.node_list.star_up_root_type_swallow:CustomSetActive(up_star_type == UP_STAR_TYPE.SWALLOW)

	if up_star_type == UP_STAR_TYPE.CONST then
		self:FlushComposeSpendConstMessage()
	else
		self:FlushComposeSpendSwallowMessage()
	end
end

-- 刷新消耗物(正常)
function ThunderManaView:FlushComposeSpendConstMessage(is_quick_add)
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	self.node_list.part_star_no_equip:SetActive(not part_info or part_info.item_id <= 0)
	self.node_list.part_star_info:SetActive(part_info and part_info.item_id > 0)

	if not part_info or part_info.item_id <= 0 then
		return
	end

	local is_compose, compose_data = ThunderManaWGData.Instance:GetEuqipSlotStarRemindById(part_info.item_id)
	if is_quick_add then
		self.star_up_list = compose_data
	end

	self.node_list.btn_star_up_remind:CustomSetActive(is_compose)	
	self.node_list.btn_quick_star_up_remind:CustomSetActive(is_compose)	

    for i, v in ipairs(self.star_cell_list) do
        if v and v.root then
            v.root:CustomSetActive(false)
        end
    end

	-- 组装数据
	local empty = {}
	local first_index = 1
	local find_index = 1
	local now_sp_num = 0
	local not_get_tips = ""
	local equip_star_up_list = ThunderManaWGData.Instance:GetEquipComposeCost(part_info.item_id)

	for i, data in ipairs(equip_star_up_list) do
		if data.special_item ~= nil then
			self.node_list.star_compose_aim_root:CustomSetActive(true)
			local list = (self.star_up_list or empty).special_list or {}
			local aim_data = list[first_index] or nil
			self.star_compose_aim:ResetCache()
            self.star_compose_aim:SetIsSpecial(true)

			if aim_data ~= nil then
				aim_data.item_id = data.special_item
				self.star_compose_aim:SetData(aim_data)
			else
				self.star_compose_aim:SetData({cur_item_id = data.special_item})
			end
	
			now_sp_num = 0
			for i, v in ipairs(list) do
				now_sp_num = now_sp_num + v.cur_item_num
			end

			local color = now_sp_num >= data.num and COLOR3B.GREEN or COLOR3B.RED
			local progress_str = ToColorStr(string.format("%d/%d", now_sp_num, data.num), color)
			self.node_list.star_compose_aim_txt.text.text = progress_str
	
			if now_sp_num < data.num then
				local item_cfg = ItemWGData.Instance:GetItemConfig(data.special_item)
				if item_cfg then
					local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(data.special_item)
					local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
					local name_str = string.format("%s%s%s", sp_star_num, Language.Common.Star, item_cfg.name)

					local str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[item_cfg.color], data.num, name_str)
					not_get_tips = string.format("%s(%s)", str, progress_str) 
				end
			end
		elseif data.star ~= nil then
            local list = ((self.star_up_list or empty).star_list or empty)[data.star] or {}
            local aim_data = list[first_index] or nil
			local virtual_cfg = ThunderManaWGData.Instance:GetVirualItemByStar(part_info.item_id, data.star)
			local virtual_id = virtual_cfg and virtual_cfg.virtual_item_id or 0
            
            if self.star_cell_list[find_index] then
                self.star_cell_list[find_index].root:CustomSetActive(true)
                self.star_cell_list[find_index].cell:ResetCache()
                self.star_cell_list[find_index].cell:SetStar(data.star)

                if aim_data ~= nil then
                    self.star_cell_list[find_index].cell:SetData(aim_data)
                else
                    self.star_cell_list[find_index].cell:SetData({cur_item_id = virtual_id})
                end
            end

            now_sp_num = 0
			for i, v in ipairs(list) do
				now_sp_num = now_sp_num + v.cur_item_num
			end

            local color = now_sp_num >= data.num and COLOR3B.GREEN or COLOR3B.RED
            local progress_str = ToColorStr(string.format("%d/%d", now_sp_num, data.num), color)
            self.star_cell_list[find_index].cell_txt.text.text = progress_str

            if now_sp_num < data.num then
				local item_cfg = ItemWGData.Instance:GetItemConfig(virtual_id)
				if item_cfg then
					local str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[item_cfg.color], data.num, item_cfg.name)
					not_get_tips = string.format("%s(%s)", str, progress_str) 
				end
            end

            find_index = find_index + 1
		end
	end

	self.node_list.star_compose_aim_tips.text.text = not_get_tips
    self.node_list.btn_star_up:CustomSetActive(not_get_tips == "")
    self.node_list.btn_quick_star_up:CustomSetActive(not_get_tips ~= "")
end

-- 刷新消耗物(吞噬)
local ONCE_SWALLOW_NUM = 20
function ThunderManaView:FlushComposeSpendSwallowMessage(is_quick_add)
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info then
		return
	end

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return
	end

	-- 刷新吞噬
	if not self.star_up_list then
		self.star_up_list = {}
	end

	local is_compose, exp_data_list = ThunderManaWGData.Instance:GetEuqipSlotSwallowStarRemindById(part_info)
	if is_quick_add then
		self.star_up_list = exp_data_list
	end

	local virtual_cfg = ThunderManaWGData.Instance:GetVirualItemByStar(part_info.item_id, part_info.star)
	local virtual_id = virtual_cfg and virtual_cfg.show_item_id or 0
	local now_exp = part_info.exp
	local exp_limit = equip_cfg.up_star_cost1
	local now_add_exp = 0

	self.node_list.btn_star_up_remind:CustomSetActive(is_compose)	
	self.node_list.btn_quick_star_up_remind:CustomSetActive(is_compose)	
	self.node_list.btn_star_up:CustomSetActive(not IsEmptyTable(self.star_up_list))
    self.node_list.btn_quick_star_up:CustomSetActive(IsEmptyTable(self.star_up_list))
	if IsEmptyTable(self.star_up_list) then	-- 插入一个虚拟道具
		table.insert(self.star_up_list, {cur_item_id = virtual_id})
	end

	self.star_up_root_type_swallow_list:SetDataList(self.star_up_list)

	for i, v in ipairs(self.star_up_list) do
		if v and v.cur_item_id then
			local cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(v.cur_item_id)

			if cfg ~= nil then
				now_add_exp = now_add_exp + cfg.item_exp_value * v.cur_item_num
			end
		end
	end

	local add_exp_str = ""
	if now_add_exp > 0 then
		add_exp_str = ToColorStr(string.format("+%d", now_add_exp), COLOR3B.GREEN)
	end
	
	local str = ToColorStr(Language.ThunderMana.ThunderTitleName[equip_cfg.seq], COLOR3B.D_BLUE)
	self.node_list.star_up_swallow_progress.text.text = string.format("%s%s/%s", now_exp, add_exp_str, exp_limit)
	self.node_list.star_up_swallow_exp_slider.slider.value = (now_add_exp + now_exp) / exp_limit
	self.node_list.star_up_swallow_now_slider.slider.value = now_exp / exp_limit
	self.node_list.star_compose_aim_tips.text.text = string.format(Language.ThunderMana.ComposeSpTips3, str)
end

function ThunderManaView:OnSelectEquipSlotCallBack(cell)
    if nil == cell or nil == cell.data then
		return
	end

	if self.select_equip_data == cell.data then 
		return 
	end

	self.select_equip_data = cell.data

	for i, v in pairs(self.equip_slot_list) do
		v:OnSelectChange(cell.index == v.index)
	end

	local select_index = SHOW_TYPE.LEVEL
	if ThunderManaWGData.Instance:GetEuqipSlotLevelRemind(self.select_equip_data.seq, self.select_equip_data.part) then
		select_index = SHOW_TYPE.LEVEL
	elseif ThunderManaWGData.Instance:GetEuqipSlotGradeRemind(self.select_equip_data.seq, self.select_equip_data.part) then
		select_index = SHOW_TYPE.GRADE
	elseif ThunderManaWGData.Instance:GetEuqipSlotStarRemind(self.select_equip_data.seq, self.select_equip_data.part) then 
		select_index = SHOW_TYPE.STAR
	end

	if self.node_list["part_type_btn_" .. select_index].toggle.isOn then
		self:OnClickChangeShowRoot(select_index, true)
	else
		self.node_list["part_type_btn_" .. select_index].toggle.isOn = true
	end

	self:StopUpLevelOperator()
	self:FlushEquipRightPanel()
end

function ThunderManaView:OnClickChangeShowRoot(index, is_on)
	if nil == index or not is_on or self.cur_right_show_type == index then
		return
	end

	self.cur_right_show_type = index
	self:StopUpLevelOperator()

	self.node_list.compose_look_btn:SetActive(self.cur_right_show_type == SHOW_TYPE.STAR)
end

function ThunderManaView:OnClickPartLevelBag()
    if not self.select_equip_data then
        return
    end

	local cfg = ThunderManaWGData.Instance:GetEquipPartCfgByType(self.select_equip_data.seq, self.select_equip_data.part)
	ThunderManaWGCtrl.Instance:OpenEquipBagView(cfg and cfg.select_part_index or 0)
end

function ThunderManaView:OnClickAutoUpLevel()
    if not self.select_equip_data then
        return
    end

	if self.is_auto_up_level then
		self:StopUpLevelOperator()
		return
	end

	self:OnClickUpLevel()
end

function ThunderManaView:OnClickUpLevel()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info or part_info.item_id <= 0 then
		return
	end

	local cur_level_cfg = ThunderManaWGData.Instance:GetEquipPartLevelCfg(part_info.seq, part_info.part, part_info.level)
	local next_level_cfg = ThunderManaWGData.Instance:GetEquipPartLevelCfg(part_info.seq, part_info.part, part_info.level + 1)
	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	local cur_level = part_info.level
	local is_max_level = not next_level_cfg
	local part_level_limit = equip_cfg and equip_cfg.part_level_limit or 0

	if not (is_max_level or cur_level >= part_level_limit) and cur_level_cfg then
		local cost_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		if cost_num >= cur_level_cfg.cost_item_num then
			--ThunderManaWGCtrl.Instance:SendCSThunderRequest(THUNDER_OPERATE_TYPE.PART_UPLEVEL, part_info.seq, part_info.part)
			--传入part为-1  服务端给部位去自动升级操作
			ThunderManaWGCtrl.Instance:SendCSThunderRequest(THUNDER_OPERATE_TYPE.PART_UPLEVEL, part_info.seq, -1)
			self:SetUpLevelButtonEnabled(true)
		else
			self:StopUpLevelOperator()
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_level_cfg.cost_item_id})
		end
	else
		self:StopUpLevelOperator()
		if is_max_level then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[2])
		elseif cur_level >= part_level_limit then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[3])
		end
	end
end

function ThunderManaView:CancelAutoLevelTimer()
	if self.auto_up_level_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_up_level_timer_quest)
		self.auto_up_level_timer_quest = nil
	end
end

function ThunderManaView:AutoUpLevelUpOnce()
	if not self.select_equip_data then
        return
    end

	if self.is_auto_up_level then
		self.auto_up_level_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnClickUpLevel, self), UP_LEVEL_DELT_TIME)
	end
end

function ThunderManaView:StopUpLevelOperator()
	self:SetUpLevelButtonEnabled(false)
end

function ThunderManaView:SetUpLevelButtonEnabled(enabled)
	self.is_auto_up_level = enabled
	local btn_str = enabled and Language.ThunderMana.BtnNameStr[3] or Language.ThunderMana.BtnNameStr[2]
	if self.node_list["part_level_up_str"] then
		self.node_list["part_level_up_str"].tmp.text = btn_str
	end

	if not enabled then
		self:CancelAutoLevelTimer()
	end
end

function ThunderManaView:OnClickUpGrade()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info or part_info.item_id <= 0 then
		return
	end

	local cur_grade_cfg = ThunderManaWGData.Instance:GetEquipPartGradeCfg(part_info.seq, part_info.part, part_info.grade)
	local next_grade_cfg = ThunderManaWGData.Instance:GetEquipPartGradeCfg(part_info.seq, part_info.part, part_info.grade + 1)
	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)

	local cur_grade = part_info.grade
	local is_max_grade = not next_grade_cfg
	local part_grade_limit = equip_cfg and equip_cfg.part_grade_limit or 0

	if not (is_max_grade or cur_grade >= part_grade_limit) and cur_grade_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
		if item_num >= cur_grade_cfg.cost_item_num then
			ThunderManaWGCtrl.Instance:SendCSThunderRequest(THUNDER_OPERATE_TYPE.PART_UPGRADE, part_info.seq, part_info.part)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({cur_grade_cfg.cost_item_id})
		end
	else
		if is_max_grade then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[4])
		elseif cur_grade >= part_grade_limit then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[5])
		end
	end
end

function ThunderManaView:OnClickLock()
	TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[6])
end

function ThunderManaView:OnClickChangeView()
	if self.view_name == GuideModuleName.ShadyThunderView then
		ViewManager.Instance:Open(GuideModuleName.SunThunderView)
	else
		ViewManager.Instance:Open(GuideModuleName.ShadyThunderView)
	end

	self:Close()
end

function ThunderManaView:OnClickOpenAllAttrView()
	local select_view = self.view_name == GuideModuleName.ShadyThunderView and ThunderManaWGData.ThunderType.ShadyType or
					ThunderManaWGData.ThunderType.SunType

	ThunderManaWGCtrl.Instance:OpenAllAttrView(select_view)		
end

function ThunderManaView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["effect_pos"]})
end

-- 升星
function ThunderManaView:ClickComposeStartUp()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info or part_info.item_id <= 0 then
		return
	end

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return
	end

	local up_star_type = equip_cfg and equip_cfg.up_star_type or 0
	if up_star_type == UP_STAR_TYPE.SWALLOW then
		-- 调用吞噬升星
		self:CheckComposeSwallowStartUp()
		return
	end

	local equip_star_up_list = ThunderManaWGData.Instance:GetEquipComposeCost(part_info.item_id)
	if not equip_star_up_list then
		return
	end
    local empty = {}

	-- 检测是否满足条件
	local final_check_satisfy_fun = function()
		for i, data in ipairs(equip_star_up_list) do
			if data.special_item ~= nil then
				local now_select_num = 0
				local list = (self.star_up_list or empty).special_list or empty

				for i, v in ipairs(list) do
					now_select_num = now_select_num + v.cur_item_num
				end

				if now_select_num ~= data.num then
					return false
				end
			elseif data.star ~= nil then
				local list = ((self.star_up_list or empty).star_list or empty)[data.star] or empty
				local now_select_num = 0

				for i, v in ipairs(list) do
					now_select_num = now_select_num + v.cur_item_num
				end

				if now_select_num ~= data.num then
					return false
				end    
			end
		end

		return true
	end

	local stuff_table = {}

	if self.star_up_list then
		if self.star_up_list.special_list then
			for i, v in ipairs(self.star_up_list.special_list) do
				local stuff_data = {}
				stuff_data.bag_index = ThunderManaWGData.Instance:GetItemIndex(v.cur_item_id)
				stuff_data.item_num = v.cur_item_num
				table.insert(stuff_table, stuff_data)
			end
		end

		if self.star_up_list.star_list then
			for k, v in pairs(self.star_up_list.star_list) do
				for m, n in ipairs(v) do
					local stuff_data = {}
					stuff_data.bag_index = ThunderManaWGData.Instance:GetItemIndex(n.cur_item_id)
					stuff_data.item_num = n.cur_item_num
					table.insert(stuff_table, stuff_data)
				end
			end
		end
	end

	if final_check_satisfy_fun() then
		local compose_list = {}
		local compose_data = {}
		compose_data.seq = self.select_equip_data.seq
		compose_data.stuff_count = #stuff_table
		compose_data.param = self.select_equip_data.part
		compose_data.stuff_item_list = stuff_table

		table.insert(compose_list, compose_data)
		ThunderManaWGCtrl.Instance:SendCSThunderEquipUpStar(#compose_list, compose_list)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.NotEnoughStuff)
	end
end

-- 升星(一键添加)
function ThunderManaView:ClickComposeQuickStartUp()
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info or part_info.item_id <= 0 then
		return
	end

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return
	end

	local up_star_type = equip_cfg and equip_cfg.up_star_type or 0

	if up_star_type == UP_STAR_TYPE.CONST then
		self:FlushComposeSpendConstMessage(true)
	else
		self.star_up_list = nil
		self:FlushComposeSpendSwallowMessage(true)
	end
end

-- 升星材料点击
function ThunderManaView:OnSelectStarUpItem(star_up_cell)
	if not star_up_cell.data then return end
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info or part_info.item_id <= 0 then
		return
	end

    local show_data = {}
	show_data.aim_item_id = part_info.item_id
	show_data.cur_item_id = star_up_cell.data.cur_item_id
    show_data.special_list = self.star_up_list and self.star_up_list.special_list or nil
    show_data.star_list = self.star_up_list and self.star_up_list.star_list or nil
    show_data.is_special = star_up_cell:GetIsSpecial()
    show_data.star_num = star_up_cell:GetStarNum()

    ThunderManaWGCtrl.Instance:SetSelectManaBatchOkCallBack(BindTool.Bind1(self.OnBatchOkCallBack, self))
    ThunderManaWGCtrl.Instance:OpenManaBatchSelectView(show_data)
end

-- 升星消耗选择
function ThunderManaView:OnBatchOkCallBack(is_special, star_num, select_list)
    if not self.star_up_list then
        return
    end

    if is_special then
        self.star_up_list.special_list = select_list
    else
        if not self.star_up_list.star_list then
            self.star_up_list.star_list = {}
        end

		self.star_up_list.star_list[star_num] = select_list
    end

	self:FlushComposeSpendConstMessage()
end

-- 升星材料点击(吞噬)
function ThunderManaView:OnSelectStarUpSwallowItem(star_swallow_up_cell, cell_index, is_default, is_click)
	if is_default or (not is_click) then
		return
	end

	if not star_swallow_up_cell.data then return end
	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.select_equip_data.seq, self.select_equip_data.part)
	if not part_info or part_info.item_id <= 0 then
		return
	end

	local show_data = {}
	show_data.part_info = part_info
	show_data.now_list = self.star_up_list
    ThunderManaWGCtrl.Instance:SetSelectManaBatchSwallowOkCallBack(BindTool.Bind1(self.OnBatchSwallowOkCallBack, self))
    ThunderManaWGCtrl.Instance:OpenManaBatchSelectSwallowView(show_data)
end

-- 升星消耗选择
function ThunderManaView:OnBatchSwallowOkCallBack(select_list)
	self.star_up_list = select_list
	self:FlushComposeSpendSwallowMessage()
end

--升星点击(吞噬)
function ThunderManaView:CheckComposeSwallowStartUp()
	if IsEmptyTable(self.star_up_list) then
		return
	end

	local send_data = {}
	for i, v in ipairs(self.star_up_list) do
		local data = {}
		data.item_id = v.cur_item_id
		data.item_num = v.cur_item_num
		data.bag_index = ThunderManaWGData.Instance:GetItemIndex(v.cur_item_id)
		table.insert(send_data, data)
	end

	ThunderManaWGCtrl:SendCSThunderEquipDecompos(self.select_equip_data.seq, self.select_equip_data.part, send_data)
end

--预览
function ThunderManaView:ComposeEvolvePreview()
	if not self.select_equip_data then
        return
    end

	ThunderManaWGCtrl.Instance:OpenStarPreview(self.select_equip_data.seq, self.select_equip_data.part)
end

-- 批次升星
function ThunderManaView:OpenBatchQuickSpView()
	ThunderManaWGCtrl.Instance:OpenManaQuickSpView()
end

function ThunderManaView:OnClickSupremeBtn()
    ViewManager.Instance:Open(GuideModuleName.SupremeFieldsWGView)
end

function ThunderManaView:ClickHaloBtn()
    ViewManager.Instance:Open(GuideModuleName.NewAppearanceHaloWGView)
end

function ThunderManaView:RemindChangeCallBack(remind_name, num)
    if remind_name == RemindName.SupremeFields and self.node_list.supreme_red then
		self.node_list.supreme_red:SetActive(num > 0)
	elseif RemindName.NewAppearance_WaiGuan_Halo and self.node_list.halo_red then
		self.node_list.halo_red:CustomSetActive(num > 0)
	end
end

function ThunderManaView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end

---------ThunderManaEquipSlotCell--------------
ThunderManaEquipSlotCell = ThunderManaEquipSlotCell or BaseClass(BaseRender)
function ThunderManaEquipSlotCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["cell_click_btn"], BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list["equip_add_btn"], BindTool.Bind(self.OnClickAdd, self))

	if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star_" .. i]
		end
	end
end

function ThunderManaEquipSlotCell:__delete()
	self.item_click_callback = nil
	self.star_list = nil
end

function ThunderManaEquipSlotCell:SetCellClickCallBack(call_back)
    self.item_click_callback = call_back
end

function ThunderManaEquipSlotCell:OnFlush()
    if self.data == nil then
        return
    end

	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.data.seq, self.data.part)
	if not part_info then
		return
	end

	self.node_list.data_info_group:SetActive(part_info.item_id > 0)
	self.node_list.empty_data_group:SetActive(part_info.item_id <= 0)

	local is_remind = ThunderManaWGData.Instance:GetEuqipSlotRemindByPart(self.data.seq, self.data.part)
	self.node_list.remind:SetActive(is_remind)
	if part_info.item_id <= 0 then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(part_info.item_id)
	if item_cfg then
		self.node_list["equip_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id)) --道具图标
		if part_info.grade > 0 then
			local num_str = string.format(Language.ThunderMana.GradeName, NumberToChinaNumber(part_info.grade))
			self.node_list.equip_name.tmp.text = string.format("%s·%s", num_str, item_cfg.name or "")
		else
			self.node_list.equip_name.tmp.text = item_cfg.name or ""
		end
	end	

	self.node_list.equip_level.tmp.text = part_info.level or 0

	local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	local star_res_list = GetSpecialStarImgResByStar4(equip_item_star_cfg.star or 0)
	local no_have_star = "a3_ty_xx_zc0"
	for k,v in pairs(self.star_list) do
		v:CustomSetActive(star_res_list[k] ~= no_have_star)
		v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
	end
end

function ThunderManaEquipSlotCell:OnClick()
    if self.item_click_callback then
        self.item_click_callback(self)
    end
end

function ThunderManaEquipSlotCell:OnClickAdd()
	if self.data == nil then
        return
    end

	self:OnClick()
	ThunderManaWGCtrl.Instance:OpenEquipBagView(self.data.select_part_index)
end

function ThunderManaEquipSlotCell:OnSelectChange(is_select)
    self.node_list["hl_img"]:SetActive(is_select)
end




----------------------------------升星材料item-----------------------
ThunderManaStarUpItemRender = ThunderManaStarUpItemRender or BaseClass(BaseRender)
function ThunderManaStarUpItemRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetClickCallBack(BindTool.Bind1(self.OnClick, self))
        self.beast_item:SetIsShowTips(false)
	end

    if not self.item_aim_cell then
		self.item_aim_cell = ItemCell.New(self.node_list.item_aim_cell)
        self.item_aim_cell:SetClickCallBack(BindTool.Bind1(self.OnClick, self))
        self.item_aim_cell:SetIsShowTips(false)
	end
end

-- 点击回调
function ThunderManaStarUpItemRender:OnClick(isOn)
    BaseRender.OnClick(self, isOn)
end

function ThunderManaStarUpItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end

    if self.item_aim_cell then
		self.item_aim_cell:DeleteMe()
		self.item_aim_cell = nil
	end

    self.is_special = nil
    self.star_num = nil
end

-- 重置数据
function ThunderManaStarUpItemRender:ResetCache()
    self.item_aim_cell:SetIsShowTips(false)
    self.is_special = nil
    self.star_num = nil

    if self.node_list and self.node_list.add_btn then
        self.node_list.add_btn:CustomSetActive(true)
    end
end

-- 设置为特殊位置
function ThunderManaStarUpItemRender:SetIsSpecial(is_special)
    self.is_special = is_special
end

-- 设置为特殊位置
function ThunderManaStarUpItemRender:GetIsSpecial()
    return self.is_special
end

-- 设置星级
function ThunderManaStarUpItemRender:SetStar(star_num)
    self.star_num = star_num
end

-- 设置星级
function ThunderManaStarUpItemRender:GetStarNum()
    return self.star_num
end


function ThunderManaStarUpItemRender:OnFlush()
    if IsEmptyTable(self.data) then
       return 
    end

    self.node_list.item_pos:CustomSetActive(self.data.cur_item_num ~= nil)
    self.node_list.add_btn:CustomSetActive(self.data.cur_item_num == nil)
    self.node_list.item_aim_cell:CustomSetActive(self.data.cur_item_num == nil)

    if self.data.cur_item_num ~= nil then
        self.beast_item:SetData({item_id = self.data.cur_item_id})
		local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.cur_item_id)
		local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
		self.beast_item:SetThunderManaStarMessage(sp_star_num)
    else
        self.item_aim_cell:SetData({item_id = self.data.cur_item_id})
        self.item_aim_cell:SetThunderManaStarMessage(self.star_num)

		if self.star_num == nil then
			local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.cur_item_id)
			local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
			self.item_aim_cell:SetThunderManaStarMessage(sp_star_num)
		end
    end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

-------------------ThunderManSuitCell-----------------
ThunderManSuitCell = ThunderManSuitCell or BaseClass(BaseRender)

function ThunderManSuitCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.lq_btn, BindTool.Bind2(self.ClickGetReward, self))
end

function ThunderManSuitCell:__delete()
end

function ThunderManSuitCell:OnFlush()
    if not self.data then
        return
    end

	-- 1.道具 2.技能
	if self.data.type == 1 then
		local item_icon = ItemWGData.Instance:GetItemIconByItemId(self.data.reward_item[0].item_id)
		local bundle, asset = ResPath.GetItem(item_icon)
        self.node_list.icon.image:LoadSprite(bundle, asset, function()
            self.node_list.icon.image:SetNativeSize()
        end)
	elseif self.data.type == 2 then
		self.node_list.icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.cur_buff_icon))
	end

	self.node_list.jiantou_img:SetActive(self.data.index ~= 0)
	local flag_state = ThunderManaWGData.Instance:GetThunderSuitFlagStateByIndex(self.data.seq, self.data.index)

	self.node_list.remind_img:SetActive(flag_state == REWARD_STATE_TYPE.CAN_FETCH)
	self.node_list.mask:SetActive(flag_state == REWARD_STATE_TYPE.UNDONE)

	local reward_index = ThunderManaWGData.Instance:GetThunderGetSuitRewardIndex(self.data.seq)
	self.node_list.hm_img:SetActive(reward_index == self.data.index)
end

function ThunderManSuitCell:ClickGetReward()
    if not self.data then
        return
    end

	local flag_state = ThunderManaWGData.Instance:GetThunderSuitFlagStateByIndex(self.data.seq, self.data.index)
	if flag_state == REWARD_STATE_TYPE.CAN_FETCH then
		ThunderManaWGCtrl.Instance:SendCSThunderRequest(THUNDER_OPERATE_TYPE.ACTIVE_SUIT, self.data.seq, self.data.index)
	elseif flag_state == REWARD_STATE_TYPE.UNDONE then
		--需要依次领取
		local pre_flag_state = REWARD_STATE_TYPE.FINISH
		if self.data.index ~= 0 then
			pre_flag_state = ThunderManaWGData.Instance:GetThunderSuitFlagStateByIndex(self.data.seq, self.data.index - 1)
		end

		if pre_flag_state ~= REWARD_STATE_TYPE.FINISH then
			TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[7])
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.ThunderMana.ErrorTip[8])
		end
		self:ShowTips()
	else
		self:ShowTips()
	end
end

function ThunderManSuitCell:ShowTips()
    if not self.data then
        return
    end

	-- 1.道具 2.技能
	if self.data.type == 1 then
		TipWGCtrl.Instance:OpenItem({item_id = self.data.reward_item[0].item_id})
	else
		local cur_buff_desc_id = self.data.cur_buff_desc_id or 0
		local cfg = FightWGData.Instance:GetBuffDescCfgByType(cur_buff_desc_id)

		local show_data = {
			x = 0,
			y = 0,
			set_pos2 = true,
			icon = self.data.cur_buff_icon or 0,
			top_text =  cfg and cfg.buff_name or "",
			show_level = false,
			hide_level = true,
			body_text = cfg and cfg.desc or "",
		}
		NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
	end
end


----------------------------------升星材料item-----------------------
ThunderManaStarUpSwallowItemRender = ThunderManaStarUpSwallowItemRender or BaseClass(BaseRender)
function ThunderManaStarUpSwallowItemRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetClickCallBack(BindTool.Bind1(self.OnClick, self))
        self.beast_item:SetIsShowTips(false)
	end

    if not self.item_aim_cell then
		self.item_aim_cell = ItemCell.New(self.node_list.item_aim_cell)
        self.item_aim_cell:SetClickCallBack(BindTool.Bind1(self.OnClick, self))
        self.item_aim_cell:SetIsShowTips(false)
	end
end

-- 点击回调
function ThunderManaStarUpSwallowItemRender:OnClick(isOn)
    BaseRender.OnClick(self, isOn)
end

function ThunderManaStarUpSwallowItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end

    if self.item_aim_cell then
		self.item_aim_cell:DeleteMe()
		self.item_aim_cell = nil
	end
end


function ThunderManaStarUpSwallowItemRender:OnFlush()
    if IsEmptyTable(self.data) then
       return 
    end

    self.node_list.item_pos:CustomSetActive(self.data.cur_item_num ~= nil)
    self.node_list.add_btn:CustomSetActive(self.data.cur_item_num == nil)
    self.node_list.item_aim_cell:CustomSetActive(self.data.cur_item_num == nil)

    if self.data.cur_item_num ~= nil then
        self.beast_item:SetData({item_id = self.data.cur_item_id, num = self.data.cur_item_num})
		local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.cur_item_id)
		local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
		self.beast_item:SetThunderManaStarMessage(sp_star_num)
    else
        self.item_aim_cell:SetData({item_id = self.data.cur_item_id})
        self.item_aim_cell:SetThunderManaStarMessage(self.star_num)

		if self.star_num == nil then
			local equip_item_star_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.cur_item_id)
			local sp_star_num = equip_item_star_cfg and equip_item_star_cfg.star or 0
			self.item_aim_cell:SetThunderManaStarMessage(sp_star_num)
		end
    end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end