local COLOR1 = StrToColor(COLOR3B.C26)
local COLOR2 = StrToColor("#725F42")
local COLOR3 = StrToColor(COLOR3B.C7)
local COLOR4 = StrToColor("#FFE68C")
local COLOR5 = StrToColor(COLOR3B.C23)
local COLOR6 = StrToColor(COLOR3B.C4)
local COLOR7 = StrToColor("#D4FDFF")

function BiZuoView:DeleteBiZuoView()
	self.select_index = -1

	if self.bizuo_laoding_bar then
		self.bizuo_laoding_bar:DeleteMe()
		self.bizuo_laoding_bar = nil
	end

	if self.bizuo_target_list then
		self.bizuo_target_list:DeleteMe()
		self.bizuo_target_list = nil
	end

	if nil ~= self.bizuo_effect then
		self.bizuo_effect:DeleteMe()
		self.bizuo_effect = nil
	end

	-- if self.huoyue_item_box_tween_list then
	-- 	for k,v in pairs(self.huoyue_item_box_tween_list) do
	-- 		v:Kill()
	-- 	end
	-- 	self.huoyue_item_box_tween_list = {}
	-- end

	if self.huoyue_item_list then
		for k,v in pairs(self.huoyue_item_list) do
			v:DeleteMe()
		end
		self.huoyue_item_list = nil
	end
	self.bizuo_tips = nil
	self.show_res_id = nil
	self.effect_obj = nil
	self.fabao_model = nil
	self.is_set_bizuo_circle_tween = nil

	if self.bizuo_circle_tween_list then
		for k, v in pairs(self.bizuo_circle_tween_list) do
			v:Kill(true)
			v = nil
		end
		self.bizuo_circle_tween_list = nil
	end
	self:DelBiZuoModelYoYoTween()
end

function BiZuoView:InitBiZuoView()
	self.show_res_id = nil
	self.select_index = -1
	--self.huoyue_item_box_tween_list = {}
	-- 创建任务列表
	if nil == self.bizuo_target_list then
		self.bizuo_target_list = AsyncBaseGrid.New()
		self.bizuo_target_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_bizuo_item_list"],
		assetBundle = "uis/view/bizuo_ui_prefab", assetName = "ph_item_render",  itemRender = BizuoCell})
		self.bizuo_target_list:SetStartZeroIndex(false)
		--self.bizuo_target_list = AsyncListView.New(BizuoCell, self.node_list["ph_bizuo_item_list"])
	end

	self.node_list["all_reward_get"].button:AddClickListener(BindTool.Bind(self.AllRewardGet, self))
	self.node_list["help_btn"].button:AddClickListener(BindTool.Bind(self.OpenTipsBtn, self)) --问号提示

	for i = 0, 3 do
		self.node_list["btn_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickDailyBtn, self, i))
		self.node_list["normal_text_" .. i].text.text = Language.BiZuo.DailyBtnName[i + 1]
		self.node_list["select_text_" .. i].text.text = Language.BiZuo.DailyBtnName[i + 1]
	end

	--活跃首页进度条
	if not self.huoyue_item_list then
		self.huoyue_item_list = {}
		local reward_list, exp_list = BiZuoWGData.Instance:GetHuoYueRewardListAndValue()
		self.reward_list = reward_list
		self.exp_list = exp_list

		for i = 1, HUOYUEDU_REWARD do
			local base_cell = ItemCell.New(self.node_list["huoyue_item_icon"..i])
			base_cell:SetIsShowTips(false)
			base_cell:SetCellBgEnabled(false)
			base_cell:SetShowCualityBg(false)
			base_cell:SetClickCallBack(BindTool.Bind(self.OnClickItem, self, i))
			base_cell:SetData({item_id = reward_list[i].item_id})
			base_cell:SetEffectRootEnable(false)
			self.huoyue_item_list[i] = base_cell
			-- local bundle, asset = ResPath.GetItem(reward_list[i].item_id)
			-- self.node_list["huoyue_item_icon" .. i].image:LoadSprite(bundle, asset, function()
			-- 	self.node_list["huoyue_item_icon" .. i].image:SetNativeSize()
			-- end)
			--self.node_list["huoyue_item_btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickItem, self, i))
		end
	end

	self:OnClickDailyBtn(0)
	self:UpdataType()
end

function BiZuoView:OpenTipsBtn()
	RuleTip.Instance:SetContent(Language.BiZuo.BiZuoCommonTip_1, Language.BiZuo.HuoYueTipsTitle)
end

function BiZuoView:DelBiZuoModelYoYoTween()
	if self.bizuo_model_yoyo_tween then
		self.bizuo_model_yoyo_tween:Kill()
		self.bizuo_model_yoyo_tween = nil
	end

	if self.bz_up_red_yoyo_tween then
		self.bz_up_red_yoyo_tween:Kill()
		self.bz_up_red_yoyo_tween = nil
	end
end

function BiZuoView:ShowIndexBiZuo()
	self:PlayTweenAnim()
end

--弃用
function BiZuoView:OpenHuoYueTip()
	local str = ""
	local vip_cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.HUOYUEDU)
	if not vip_cfg then return end
	local vip_level = RoleWGData.Instance.role_vo.vip_level
	local total_exp = BiZuoWGData.Instance:GetTotalExp()

	local temp_vip_level = 0
	for i = 0, 12 do
		if vip_cfg["param_" .. vip_level] < vip_cfg["param_" .. i] then
			temp_vip_level = i
			break
		elseif vip_cfg["param_" .. vip_level] == vip_cfg["param_" .. 12] then
			temp_vip_level = vip_level
			break
		end
	end
	local extra_value = vip_cfg["param_" .. temp_vip_level] - vip_cfg["param_" .. vip_level]
	if extra_value > 0 then
		str = string.format(Language.BiZuo.HuoYueTips_1, total_exp, vip_cfg["param_"..vip_level], temp_vip_level, extra_value)
	else
		str = string.format(Language.BiZuo.HuoYueTips_2, total_exp, vip_cfg["param_"..vip_level] )
	end
	SysMsgWGCtrl.Instance:ErrorRemind(str)
end


function BiZuoView:FlushHuoYueAward()
	local award_index_max = 0
	local total_exp = BiZuoWGData.Instance:GetTotalExp()
	total_exp = tonumber(total_exp) or 0
	for i = 1, HUOYUEDU_REWARD do
		local exp_num = self.exp_list[i] and tonumber(self.exp_list[i]) or 0
		if tonumber(total_exp) >= exp_num then
			award_index_max = i
		end

		if BiZuoWGData.Instance:GetIaGetAwrdFlag(i-1) then
			self.node_list["huoyue_item_yilingqu"..i]:SetActive(true)
			--self.node_list["huoyue_item_normal"..i]:SetActive(false)
			self.node_list["huoyue_item_can_get"..i]:SetActive(false)
			self.node_list["huoyue_item_remind"..i]:SetActive(false)

			-- if self.huoyue_item_box_tween_list[i] then
			-- 	self:PauseTween(i)
			-- end
		else
			self.node_list["huoyue_item_yilingqu"..i]:SetActive(false)
			--self.node_list["huoyue_item_normal"..i]:SetActive(true)
			self.node_list["huoyue_item_can_get"..i]:SetActive(total_exp~=0 and self.exp_list[i] <= total_exp)
			self.node_list["huoyue_item_remind"..i]:SetActive(total_exp~=0 and self.exp_list[i] <= total_exp)
			-- if total_exp~=0 and self.exp_list[i] <= total_exp then
			-- 	if self.huoyue_item_box_tween_list[i] then
			-- 		self.huoyue_item_box_tween_list[i]:Restart()
			-- 	else
			-- 		self.huoyue_item_box_tween_list[i] = DG.Tweening.DOTween.Sequence()
			-- 		UITween.ShakeAnimi(self.node_list["huoyue_item_normal"..i].transform, self.huoyue_item_box_tween_list[i])
			-- 	end
			-- elseif self.huoyue_item_box_tween_list[i] then
			-- 	self:PauseTween(i)
			-- end
		end

		--self.node_list["normal_flag"..i]:SetActive(not (total_exp~=0 and self.exp_list[i] <= total_exp))
		--self.node_list["finish_flag"..i]:SetActive(total_exp~=0 and self.exp_list[i] <= total_exp)
	end

	local slider_piecewise = {0.12, 0.246, 0.371, 0.497, 0.623, 0.748, 0.874, 1}
	-- if award_index_max > 0 then
	-- 	for i = 1, award_index_max do
	-- 		self.node_list["line_hl" .. i]:SetActive(true)
	-- 	end
	-- else
	-- 	for i = 1, 8 do
	-- 		self.node_list["line_hl" .. i]:SetActive(false)
	-- 	end
	-- end

	if award_index_max < HUOYUEDU_REWARD then
		local exp_pre_value = self.exp_list[award_index_max] or 0
		local exp_next_value = self.exp_list[award_index_max + 1]
		local slider_value = slider_piecewise[award_index_max] or 0
		local slider_next_value = (slider_piecewise[award_index_max + 1] or 1) - slider_value
		local slider_add_value = (total_exp - exp_pre_value) / (exp_next_value - exp_pre_value) * slider_next_value
		self.node_list["ProgressSlider"].slider.value = slider_value + slider_add_value  --水平进度条
		--self.node_list["bz_img_cicle_slider"].image.fillAmount = slider_value + slider_add_value  --环形进度条
	else
		self.node_list["ProgressSlider"].slider.value = 1
		--self.node_list["bz_img_cicle_slider"].image.fillAmount = 1
	end

	self:FlushHuoYueRewardText()
end

-- function BiZuoView:PauseTween(index)
-- 	self.huoyue_item_box_tween_list[index]:Pause()
-- 	self.node_list["huoyue_item_normal"..index].transform.localRotation = Quaternion.identity
-- end

function BiZuoView:FlushHuoYueRewardText()
	local cur_huoyue_exp = BiZuoWGData.Instance:GetTotalExp() or 0
	local cur_xiuwei_value = BiZuoWGData.Instance:GetTotalXiuWei() or 0
	self.node_list["cur_exp_value"].text.text = cur_huoyue_exp  --当前活跃值
	self.node_list["cur_xiuwei_value"].text.text = cur_xiuwei_value  --当前修为值
	for i = 1, HUOYUEDU_REWARD do
		if self.node_list["lbl_huoyue_item_exp"..i] and self.exp_list[i] then
			if cur_huoyue_exp >= self.exp_list[i] then
				--self.node_list["lbl_huoyue_item_exp"..i].text.text = ToColorStr(self.exp_list[i] .. "/" .. self.exp_list[i], COLOR3B.GREEN)
				self.node_list["lbl_huoyue_item_exp"..i].text.text = self.exp_list[i]
			else
				--self.node_list["lbl_huoyue_item_exp"..i].text.text = ToColorStr(cur_huoyue_exp .. "/" .. self.exp_list[i], COLOR3B.RED)
				self.node_list["lbl_huoyue_item_exp"..i].text.text = self.exp_list[i]
			end
		end
	end
end

function BiZuoView:OnClickItem(index)
	if BiZuoWGData.Instance:CanShowTips(index) then
		TipWGCtrl.Instance:OpenItem(self.reward_list[index])
		--RewardShowViewWGCtrl.Instance:SetRewardShowData(self.reward_list[index])--物品浏览
		return
	end

	--local reward_list = {}
	for i = 1, HUOYUEDU_REWARD do
		if not BiZuoWGData.Instance:CanShowTips(i) then
			-- for k, v in pairs(self.reward_list[i]) do
			-- 	table.insert(reward_list, v)
			-- end
			BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_FETCH_DAILY_REWARD, 0, 0, 0, i - 1)
		end
	end

	--TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
end

function BiZuoView:OnClickDailyBtn(index)
	if self.select_index == index then return end
	self.select_index = index
	for i = 0, 3 do
		self.node_list["btn_" .. i]:FindObj("normal_content"):CustomSetActive(i ~= index)
		self.node_list["btn_" .. i]:FindObj("select_content"):CustomSetActive(i == index)
	end
	self:Flush()
	self:PlayTweenAnim()
end

function BiZuoView:PlayTweenAnim()
	UITween.CleanAlphaShow(GuideModuleName.BiZuo)
	UITween.CleanAllMoveToShowPanel(GuideModuleName.BiZuo)
	local node = self.node_list["ph_bizuo_item_list"]
	local obj_transform = UITween.CanvasGroup(node)
	obj_transform.alpha = 0

    ReDelayCall(self, function()
		obj_transform.alpha = 1
		UITween.AlphaShow(GuideModuleName.BiZuo, node, 0.1, 1, 0.4, DG.Tweening.Ease.Linear)
    end, 0.1, "BiZuoView")
	UITween.MoveToShowPanel(GuideModuleName.BiZuo, node, Vector2(126, 42), Vector2(126, -12), 0.35, DG.Tweening.Ease.Linear)
end

function BiZuoView:AllRewardGet()
	for i = 1, HUOYUEDU_REWARD do
		if not BiZuoWGData.Instance:CanShowTips(i) then
			BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_FETCH_DAILY_REWARD, 0, 0, 0, i - 1)
		end
	end
end

function BiZuoView:UpdataType()
	local bizuo_info = BiZuoWGData.Instance:GetAllInfo()
	if bizuo_info and bizuo_info.level then
		local image_id = BiZuoWGData.Instance:GetBiZuoTypeByLevel(bizuo_info.level)
		if image_id then
			self.type = image_id
		end
	end
end

function BiZuoView:UpdataImageId()
	local bizuo_info = BiZuoWGData.Instance:GetAllInfo()
	if bizuo_info and bizuo_info.level then
		local image_id = BiZuoWGData.Instance:GetBiZuoTypeByLevel(bizuo_info.level)
		if image_id then
			if self.image_id ~= image_id then
				self.image_id = image_id
				self.type = image_id
			end
		end
	end
end

function BiZuoView:FlushBiZuoView(param_list)
	BiZuoWGData.Instance:SetActivityHallCfg()
	BiZuoWGData.Instance:FlushAllInfo()
	self:UpdataImageId()

	--self.node_list["Img_active_remind"]:SetActive(BiZuoWGData.Instance:CanLevelUpRemind() ~= 0)
	local data_list = BiZuoWGData.Instance:GetDailyAllData(self.select_index)

	table.sort(data_list, SortTools.KeyUpperSorters("sort2", "sort"))

	if self.bizuo_target_list then
		self.bizuo_target_list:SetDataList(data_list, 0)
	end
	self:FlushHuoYueAward()
end

function BiZuoView:DaycountChange()
	self:FlushBiZuoView()
end


--------------------------------------------------------------------------------------------------------------------
BizuoCell = BizuoCell or BaseClass(BaseGridRender)

function BizuoCell:__init()

end

function BizuoCell:__delete()
end

function BizuoCell:LoadCallBack()
	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoBtn, self))
	self.node_list.btn_item.button:AddClickListener(BindTool.Bind(self.OnClickBgBtn,self))

	if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(false)
    end

	self.go_btn_block = self.node_list.go_btn:GetComponent("UIBlock")
end

function BizuoCell:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function BizuoCell:OnFlush()
	if IsEmptyTable(self.data) then return end

	self.node_list.end_text:SetActive(false)
	self.node_list.act_time_content:SetActive(false)
	--self.node_list.signup_btn:SetActive(false)
	self.node_list["red_point"]:SetActive(false)

	--if self.data.tips_icon ~= "" and self.data.tips_icon > 0 then
		--self.node_list.reward_type.image:LoadSprite(ResPath.GetDailyTypeIcon("a3_rc_biaoqian" .. self.data.tips_icon))
		--self.node_list.tips_icon_text.text.text = self.data.tips_icon_text
		--self.node_list.reward_type:SetActive(true)
	--else
		--self.node_list.reward_type:SetActive(false)
	--end

	self.node_list.normal_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a3_rc_btn_xxl" .. self.data.task_type))
	self.node_list.complete_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a3_rc_btn_xxa" .. self.data.task_type))
	self.node_list.xiuwei_bg.image:LoadSprite(ResPath.GetDailyTypeIcon("a3_rc_btn_xd" .. self.data.task_type))
	self.node_list.huoyuedu_bg.image:LoadSprite(ResPath.GetDailyTypeIcon("a3_rc_btn_xd" .. self.data.task_type))

	if self.data.task_type == 0 then
		self.node_list.act_name.text.color = COLOR1
		self.node_list.go_btn_text.text.color = StrToColor(COLOR3B.C2)
		self.node_list.time_num.text.color = COLOR2
		self.node_list.desc.text.color = COLOR3
		self.node_list.xiuwei_title.text.color = COLOR4
		self.node_list.xiuwei_text.text.color = COLOR3
		self.node_list.huoyuedu_title.text.color = COLOR4
		self.node_list.huoyuedu.text.color = COLOR3
	elseif self.data.task_type == 1 then
		self.node_list.act_name.text.color = COLOR5
		self.node_list.go_btn_text.text.color = StrToColor(COLOR3B.C2)
		self.node_list.time_num.text.color = COLOR5
		self.node_list.desc.text.color = COLOR6
		self.node_list.xiuwei_title.text.color = COLOR7
		self.node_list.xiuwei_text.text.color = COLOR6
		self.node_list.huoyuedu_title.text.color = COLOR7
		self.node_list.huoyuedu.text.color = COLOR6
	end

	local max_times = self.data.complete_max_times - self.data.buy_times
	local max_huoyuedu = self.data.exp_per_times * (max_times)
	self.node_list.huoyuedu.text.text = max_huoyuedu > 0 and self.data.exp_per_times or "0"
	-- if max_huoyuedu > 0 then
	-- 	self.node_list.huoyuedu_bg:CustomSetActive(true)
	-- 	self.node_list.huoyuedu.text.text = self.data.exp_per_times
	-- else
	-- 	self.node_list.huoyuedu_bg:SetActive(false)
	-- end

	if self.data.xiuwei_per_times then
		-- self.node_list.xiuwei_bg:CustomSetActive(true)
		-- self.node_list.xiuwei_text.text.text = self.data.xiuwei_per_times
		local xiuwei_value = 0
		if self.data.xiuwei_per_times > 0 then
			local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
			xiuwei_value = number + self.data.xiuwei_per_times
			xiuwei_value = math.floor(xiuwei_value + xiuwei_value * rate / 100)
		end

		self.node_list.xiuwei_text.text.text = xiuwei_value > 0 and xiuwei_value or "0"
	else
		-- self.node_list.xiuwei_bg:SetActive(false)
	end
	--self.node_list.xiuwei_text.text.text = self.data.xiuwei_per_times or Language.BiZuo.NoHuoYueDu

	if self.data.parent_type == 1 then
		self:FlushDaily()
	elseif self.data.parent_type == 2 then
		self:FlushActivity()
	end
end

function BizuoCell:FlushDaily()
	-- 刷新图标
	--self.node_list.act_icon.image:LoadSprite(ResPath.GetF2MainUIImage("act_" .. self.data.icon))
	local name = self.data.name
	if self.data.type == BiZuoType.Type_3 and WuJinJiTanWGData.Instance:IsLingHunGuangChang() then
		name = Language.FuBenPanel.LingHunGuangChangTitle
	end
	self.node_list.act_name.text.text = name

	local max_times = self.data.complete_max_times - self.data.buy_times
	--策划说 >= 9999显示"无限"二字
	self.node_list.time_num.text.text = self.data.complete_max_times >= 9999 and Language.BiZuo.WuXian or string.format(Language.BiZuo.CompleteTime2, max_times - self.data.complete_times, max_times)

	local is_kaiqi = self.data.is_kaiqi
	local is_finish = self.data.is_finish

	self.node_list["red_point"]:SetActive(self.data.red_flag)
	self.node_list.normal_bg:SetActive(not is_finish)
	self.node_list.complete_bg:SetActive(is_finish)
	--self.node_list.finish_flag:SetActive(is_finish)
	--self.node_list.go_btn:SetActive(is_kaiqi)
	self.node_list.time_num:SetActive(is_kaiqi)
	self.node_list.go_btn_text:SetActive(is_kaiqi)
	--self.go_btn_block.enabled = is_kaiqi
	self.go_btn_block.enabled = not is_finish

	if not is_kaiqi then
		self.node_list.open_level.text.text = string.format(Language.BiZuo.LevelOpen, self.data.open_level)
	end
	self.node_list.open_level:SetActive(self.data.level_limit_flag)

	local data = BiZuoWGData.Instance:GetBiZuoShowTipData(self.data.type)
	if data then
		local reward_list_data = SortDataByItemColor(data.reward_item)
		self.reward_list:SetDataList(reward_list_data)
	end
end

function BizuoCell:FlushActivity()
	local day, time = BiZuoWGData.Instance:GetActOpenDesc(self.data)
	self.node_list.act_day.text.text = day
	self.node_list.act_time.text.text = time
	--self.node_list.signup_time.text.text = string.format(Language.BiZuo.Signup_Time_Segment, self.data.signup_begin_time, self.data.signup_end_time)

	self.node_list.act_name.text.text = self.data.name
	-- 是否参与过该活动
	local is_participation = BiZuoWGData.Instance:GetIsParticipationBySeq(self.data.act_seq)
	local complete_times = is_participation and 1 or 0
	-- if not self.data.dailywork_cfg then
	-- 	print_error("日常--任务表取不到配置,活动类型:", self.data.act_type)
	-- 	return
	-- end

	local times_des = ""
	local max_times = self.data.complete_max_times - self.data.buy_times
	local HuSongActType = 3 -- 护送活动号
	if self.data.act_type == HuSongActType then
		complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) --护送完成次数
	end

	if self.data.complete_max_times >= 9999 then
		times_des = Language.BiZuo.WuXian
	else
		times_des = string.format(Language.BiZuo.CompleteTime2, max_times - complete_times, max_times)
	end
	self.node_list.time_num.text.text = times_des

	--self.node_list.go_btn:SetActive(false)
	self.node_list.time_num:SetActive(false)
	self.node_list.go_btn_text:SetActive(false)
	self.go_btn_block.enabled = true
	--self.node_list.signup_time:SetActive(false)
	self.node_list.open_level:SetActive(false)

	--self.node_list.finish_flag:SetActive(false)
	self.node_list.complete_bg:SetActive(false)
	self.node_list.normal_bg:SetActive(true)
	self.node_list.act_time_content:SetActive(self.data.act_time_limit)

	if self.data.level_limit_flag then											-- 未开放（等级达不到限制等级）
		self.node_list.open_level:SetActive(true)
		self.node_list.open_level.text.text = string.format(Language.BiZuo.LevelOpen, RoleWGData.GetLevelString(self.data.open_level))
	-- 护送任务 护送次数已用完
	elseif 1 == self.data.is_husong_count_finish then		--已完成
		self.node_list.complete_bg:SetActive(true)
		self.node_list.normal_bg:SetActive(false)
		self.go_btn_block.enabled = false
	elseif 1 == self.data.is_open and 0 == self.data.is_act_open_day then    -- 开启中
		self.node_list.time_num:SetActive(true)
		self.node_list.go_btn_text:SetActive(true)
	elseif self.data.is_end and not is_participation then   -- 已结束--未参与
		self.node_list.end_text:SetActive(true)
		self.go_btn_block.enabled = false
		--已结束图标
	elseif self.data.is_end and is_participation then		-- 已结束--参与
		--self.node_list.finish_flag:SetActive(true)
		self.node_list.complete_bg:SetActive(true)
		self.node_list.normal_bg:SetActive(false)
		self.go_btn_block.enabled = false
		-- 已完成图标
	end
	local reward_list_data = SortDataByItemColor(self.data.reward_item)
	self.reward_list:SetDataList(reward_list_data)
end

function BizuoCell:OnClickGoBtn()
	if not self.data then return end

	if self.data.parent_type == 1 then
		local flag = self.data.type == BIZUO_TYPE.RI_CHANG and TaskWGData.Instance:CheckRiChangRedPoint()
		local is_kaiqi = (self.data.not_complete ~= 0 and self.data.is_open == 1) or flag
		if not is_kaiqi then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BiZuo.CSPro_NotOpen))
			return
		end

		if self.data.is_open == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BiZuo.WeiKaiQi,self.data.open_level))
			return
		end

		local main_role = Scene.Instance:GetMainRole()
		if main_role and main_role:CantPlayerDoMove(true) then
			return
		end

		local view_name, tab_index, is_close_view_after_jump = BiZuoWGData.Instance:GetOpenViewByIndex(self.data.index)
		is_close_view_after_jump = is_close_view_after_jump == 1
		if self.data.type == BIZUO_TYPE.RI_CHANG then --日常任务
			local is_finish = TaskWGData.Instance:GetBountyAcceptLimit()
			if is_finish then
				ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
				if is_close_view_after_jump then
					BiZuoWGCtrl.Instance:Close()
				end
				return
			end
			TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
			TaskWGData.Instance:DoShangJinTask()
			if is_close_view_after_jump then
				BiZuoWGCtrl.Instance:Close()
				ViewManager.Instance:Close(GuideModuleName.XiuWeiView)
			end
			return
		elseif self.data.type == BIZUO_TYPE.HU_SONG then --护送美人
			if NewTeamWGData.Instance:GetIsMatching() then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
				return
			end
			if YunbiaoWGData.Instance:GetIsHuShong() then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
				return
			end
			TaskGuide.Instance:CanAutoAllTask(false)
			if is_close_view_after_jump then
				BiZuoWGCtrl.Instance:Close()
			end
			ActIvityHallWGCtrl.Instance:DoHuSong()
			return
		elseif self.data.type == BIZUO_TYPE.XIAN_MENG then -- 仙盟周任务
			if RoleWGData.Instance.role_vo.guild_id <= 0 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
				return
			end
			local list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_MENG)
			local task_id = list and list[1]
			if task_id and task_id ~= 0 then
				MainuiWGCtrl.Instance:DoTask(task_id,TaskWGData.Instance:GetTaskStatus(task_id), true)
				if is_close_view_after_jump then
					BiZuoWGCtrl.Instance:Close()
				end
			end
			return
		elseif self.data.type == BIZUO_TYPE.GUILD_BUILD_TASK then
			if RoleWGData.Instance.role_vo.guild_id <= 0 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
				return
			end
		elseif self.data.type == BIZUO_TYPE.YEWAI_GUAJI then

			local scene_type = Scene.Instance:GetSceneType()
			local scene_id = Scene.Instance:GetSceneId()
			local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
			if scene_type ~= SceneType.Common or scene_id == bootybay_scene_id then
				TipWGCtrl.Instance:ShowSystemMsg(Language.BiZuo.NotGiJiDesc)
				return
			end

			local offline_exp_info = BiZuoWGData.Instance:GetOffLineGuaJiInfo()
			if not offline_exp_info then
				return
			end

			MoveCache.SetEndType(MoveEndType.Auto)
			GuajiWGCtrl.Instance:MoveToPos(offline_exp_info.hang_monster_id_scene_id, offline_exp_info.hang_monster_id_pos_x,
				offline_exp_info.hang_monster_id_pos_y, offline_exp_info.pre_radius)
			if is_close_view_after_jump then
				BiZuoWGCtrl.Instance:Close()
			end
		elseif self.data.type == BIZUO_TYPE.SIT then
			BiZuoWGCtrl.Instance:Close()
			GuajiWGCtrl.Instance:TryGoToSit()
		 end

		if not view_name then return end
		if view_name == "activity_hall" then
			self:JoinFuBenHandler()
		else
			--BiZuoWGCtrl.Instance:SetViewTipsFlag(false)
			local is_open = FunOpen.Instance:OpenViewByName(view_name, tab_index)
			if is_open and is_close_view_after_jump then
				BiZuoWGCtrl.Instance:Close()
			end
		end
	elseif self.data.parent_type == 2 then
		if not next(self.data) then
			return
		end

		if 1 == self.data.is_open and 0 == self.data.is_act_open_day then
			if self.data.is_open == 1 then
				ViewManager.Instance:CloseAll()
				ActIvityHallWGCtrl.Instance:OpenActivity(self.data.act_type)
			else
				BiZuoWGCtrl.Instance.activity_desc_view:SetData(self.data)
				BiZuoWGCtrl.Instance.activity_desc_view:Open()
			end
		else
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BiZuo.CSPro_NotOpen))
		end
	end
end

function BizuoCell:OnClickBgBtn()
	if not self.data then return end
	local data
	if self.data.parent_type == 1 then
		data = BiZuoWGData.Instance:GetBiZuoShowTipData(self.data.type)
	elseif self.data.parent_type == 2 then
		data = self.data
	end

	BiZuoWGCtrl.Instance.activity_desc_view:SetData(data)
	BiZuoWGCtrl.Instance.activity_desc_view:Open()
end

function BizuoCell:GetItemClick(index)
	return BindTool.Bind(self.OnClickGoBtn, self)
end
