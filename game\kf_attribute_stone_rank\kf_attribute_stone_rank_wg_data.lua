KFAttributeStoneRankWGData = KFAttributeStoneRankWGData or BaseClass()

function KFAttributeStoneRankWGData:__init()
	if KFAttributeStoneRankWGData.Instance then
		error("[KFAttributeStoneRankWGData] Attempt to create singleton twice!")
		return
	end

    KFAttributeStoneRankWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.KFAttributeStoneRank, BindTool.Bind(self.GetKFAttributeStoneRankRemind, self))
end

function KFAttributeStoneRankWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.KFAttributeStoneRank)
    KFAttributeStoneRankWGData.Instance = nil
end

function KFAttributeStoneRankWGData:InitParam()
    self.self_rank = 0
    self.self_score = 0
    self.person_score_reward_flag = {}
	self.rank_list = {}
    self.last_rank_list = {}
    self.score_item_list = {}
end

function KFAttributeStoneRankWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("cross_attribute_stone_rank_auto")
    self.other_cfg = cfg.other[1]
    self.reward_cfg = ListToMapList(cfg.reward, "grade", "week_day")
    self.score_cfg = ListToMap(cfg.score, "week_day")
    self.person_score_reward_cfg = ListToMap(cfg.person_score_reward, "week_day", "seq")
    self.grade_cfg = cfg.grade
end

-------------------红点相关start-------------------
function KFAttributeStoneRankWGData:GetKFAttributeStoneRankRemind()
    local cfg = self:GetPersonScoreRewardCfg()
    if IsEmptyTable(cfg) then
        return 0
    end

    for k, v in pairs(cfg) do
        if self.self_score >= v.score and self:GetScoreRewardFlag(v.seq) == 0 then
            return 1
        end
    end

    return 0
end
-------------------红点相关start-------------------

-------------------协议相关start-------------------
function KFAttributeStoneRankWGData:SetKFAttributeStoneRankData(protocol)
    self.self_rank = protocol.self_rank                                 -- 自己的排名
	-- self.self_score = protocol.self_score                            -- 自己的积分,由于后端处理麻烦，个人积分只使用16312协议的
    self:SetRankList(protocol.rank_item_list)                           -- 排行榜信息

end

function KFAttributeStoneRankWGData:SetKFAttributeStoneLastRankData(protocol)
    self:SetLastRankList(protocol.last_rank_item_list)                  -- 上期排行榜信息
end

function KFAttributeStoneRankWGData:SetKFAttributeStoneRankScoreRewardeFlag(protocol)
	self.person_score_reward_flag = bit:d2b_l2h(protocol.person_score_reward_flag, nil, true)
	self.self_score = protocol.self_score
end

function KFAttributeStoneRankWGData:GetSelfRank()
    return self.self_rank
end

function KFAttributeStoneRankWGData:GetSelfScore()
    return self.self_score
end

function KFAttributeStoneRankWGData:GetScoreRewardFlag(seq)
    return self.person_score_reward_flag[seq] or 1
end
-------------------协议相关end-------------------

function KFAttributeStoneRankWGData:GetOtherCfg()
    return self.other_cfg
end

function KFAttributeStoneRankWGData:GetRewardCfg()
    local week_day = self:GetCurWeekDay()
    local grade = self:GetGrade()
    return (self.reward_cfg[grade] or {})[week_day] or {}
end

function KFAttributeStoneRankWGData:GetScoreCfg()
    local week_day = self:GetCurWeekDay()
    return self.score_cfg[week_day] or {}
end

function KFAttributeStoneRankWGData:GetPersonScoreRewardCfg()
    local week_day = self:GetCurWeekDay()
    return self.person_score_reward_cfg[week_day] or {}
end

function KFAttributeStoneRankWGData:GetGrade()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    for k, v in pairs(self.grade_cfg) do
        if open_day >= v.min_server_day and open_day <= v.max_server_day then
            return v.grade
        end
    end

    return 0
end

function KFAttributeStoneRankWGData:GetPersonScoreRewardCfgBySeq(seq)
    local cfg = self:GetPersonScoreRewardCfg()
    return cfg[seq] or {}
end

function KFAttributeStoneRankWGData:GetGotoBtnNum()
    local cfg = self:GetScoreCfg()
    local btn_num = 0
    if not IsEmptyTable(cfg) then
        for i = 1, 3 do
            if cfg["act_type" .. i] ~= "" and cfg["act_type" .. i] ~= 0 then
                btn_num = btn_num + 1
            end
        end
    end

    return btn_num
end

function KFAttributeStoneRankWGData:GetModelData()
    local cfg = self:GetScoreCfg()
    local model_data = {}
    if not IsEmptyTable(cfg) then
        -- 模型数据部分
        local model_show_type = cfg.model_show_type
        if model_show_type == "" or model_show_type == 0 then
            return model_data
        end

        local display_data = {}
        local model_show_itemid = cfg.model_show_itemid
        if model_show_itemid ~= 0 and model_show_itemid ~= "" then
            local split_list = Split(model_show_itemid, "|")
            if #split_list > 1 then
                local list = {}
                for k, v in pairs(split_list) do
                    list[tonumber(v)] = true
                end

                display_data.model_item_id_list = list
            else
                display_data.item_id = model_show_itemid
            end
        end

        display_data.render_type = model_show_type - 1
        display_data.bundle_name = cfg.model_bundle_name
        display_data.asset_name = cfg.model_asset_name
        display_data.should_ani = true
        display_data.hide_model_block = false
        display_data.need_wp_tween = true
        if model_show_type == 1 and cfg.model_bundle_name ~= "" and cfg.model_asset_name ~= "" then
            display_data.need_wp_tween = false
        end

        -- 调整transform部分
        local transform_info = {}
        local display_pos = cfg.display_pos
        local display_rotation = cfg.display_rotation
        local display_scale = cfg.display_scale

        local pos_x, pos_y = 0, 0
        if display_pos and display_pos ~= "" then
            local pos_list = Split(display_pos, "|")
            pos_x = tonumber(pos_list[1]) or pos_x
            pos_y = tonumber(pos_list[2]) or pos_y
        end

        transform_info.pos_x = pos_x
        transform_info.pos_y = pos_y

        local rot_x, rot_y, rot_z = 0, 0, 0
        if display_rotation and display_rotation ~= "" then
            local rot_list = Split(display_rotation, "|")
            rot_x = tonumber(rot_list[1]) or rot_x
            rot_y = tonumber(rot_list[2]) or rot_y
            rot_z = tonumber(rot_list[3]) or rot_z
        end

        transform_info.rot_x = rot_x
        transform_info.rot_y = rot_y
        transform_info.rot_z = rot_z

        local scale = display_scale
        transform_info.scale = (scale and scale ~= "" and scale > 0) and scale or 1

        model_data.display_data = display_data
        model_data.transform_info = transform_info
        model_data.model_name = cfg.model_name
    end

    return model_data
end

function KFAttributeStoneRankWGData:GetCurWeekDay()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
    local week_day = TimeUtil.FormatSecond3MYHM1(server_time)
	return week_day == 7 and 0 or week_day
end

function KFAttributeStoneRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end

function KFAttributeStoneRankWGData:ExpandRankData(index)
	local rank_item = {}
	rank_item.no_true_rank = true
	rank_item.index = index
	rank_item.rank_data = self:CreatNoRankItemData(index)
	return rank_item
end

function KFAttributeStoneRankWGData:SetRankList(protocol_rank_list)
	local cfg = self:GetRewardCfg()
	local rank_cfg = cfg[#cfg]
	local max_rank = rank_cfg.max_rank
	for i = 1, max_rank do
		local rank_item = {}
		if protocol_rank_list[i] then
			rank_item.no_true_rank = false
			rank_item.index = i
			rank_item.rank_data = protocol_rank_list[i]
		else
			rank_item = self:ExpandRankData(i)
		end

		self.rank_list[i] = rank_item
	end
end

function KFAttributeStoneRankWGData:SetLastRankList(protocol_rank_list)
	local cfg = self:GetRewardCfg()
	local rank_cfg = cfg[#cfg]
	local max_rank = rank_cfg.max_rank
	for i = 1, max_rank do
		local rank_item = {}
		if protocol_rank_list[i] then
			rank_item.no_true_rank = false
			rank_item.index = i
			rank_item.rank_data = protocol_rank_list[i]
		else
			rank_item = self:ExpandRankData(i)
		end

		self.last_rank_list[i] = rank_item
	end
end

function KFAttributeStoneRankWGData:GetRankList()
	return self.rank_list
end

function KFAttributeStoneRankWGData:GetLastRankList()
	return self.last_rank_list
end

function KFAttributeStoneRankWGData:GetShowScoreItemList()
    local week_day = self:GetCurWeekDay()
    if self.score_item_list[week_day] then
        return self.score_item_list[week_day]
    end

    local list = {}
    local cfg = self:GetScoreCfg()
    if not IsEmptyTable(cfg) then
        local item_list = Split(cfg.item_score, "|")
        for k, v in ipairs(item_list) do
            local item_info = Split(v, ",")
            table.insert(list, {item_id = tonumber(item_info[1]), score = tonumber(item_info[2])})
        end
    end

    if #list > 0 then
        self.score_item_list[week_day] = list
    end

    return list
end