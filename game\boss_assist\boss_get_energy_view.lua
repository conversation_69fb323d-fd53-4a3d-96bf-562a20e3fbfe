--boss体力领取面板
BossGetEnergyView = BossGetEnergyView or BaseClass(SafeBaseView)

local Img_Bg_Name1 = "tl_shijian_guoqi" --过期
local Img_Bg_Name2 = "tl_shijian_yilingqu"
local Img_State_Name1 = "tl_shijian_guoqi_2" --过期
local Img_State_Name2 = "tl_shijian_yilingqu_2"

local Move_Pos_Tb = {
    [0] = {
        [1] = Vector2(723, 37),
        [2] = Vector2(723, 0),
        [3] = Vector2(723, 37),
    },
    [1] = {
        [1] = Vector2(18, 37),
        [2] = Vector2(155, 0),
        [3] = Vector2(288, 37),
    },
}


function BossGetEnergyView:__init()
	self.active_close = false
	self.view_layer = UiLayer.Normal
    self.view_name = "BossGetEnergyView"
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "boss_get_energy_panel")
    self.view_cache_time = 0
    self.mask_alpha = 225/255
	self.open_tween = nil
	self.close_tween = nil
end

function BossGetEnergyView:ReleaseCallBack()
    if self.get_item then
        self.get_item:DeleteMe()
        self.get_item = nil
    end
end

function BossGetEnergyView:ShowIndexCallBack()
    UITween.CleanAllTween(self.view_name)

    local tween_info = UITween_CONSTS.BossGetEnergy
    UITween.AlphaShow(self.view_name, self.node_list.total_bg, tween_info.BgFromAlpha, tween_info.BgToAlpha, tween_info.BgAlphaTime, tween_info.BgAlphaShowType)
    
    UITween.FakeHideShow(self.node_list.right_render_1)
    UITween.FakeHideShow(self.node_list.right_render_2)
    UITween.FakeHideShow(self.node_list.icon)
    UITween.FakeHideShow(self.node_list.btn_get)
    --宣传文字动画
    UITween.MoveToShowPanel(self.view_name, self.node_list.top_img, tween_info.UpImgStartPos, tween_info.UpImgEndPos, tween_info.up_tween_time, tween_info.UpTweenShowType)

    for i = 1, 3 do
        self.node_list["middle_"..i].rect.anchoredPosition = Move_Pos_Tb[0][i]
    end
    for i = 1, 3 do
        ReDelayCall(self,
        function()
            UITween.MoveToShowPanel(self.view_name, self.node_list["middle_"..i], Move_Pos_Tb[0][i], Move_Pos_Tb[1][i], tween_info.MoveTime) 
        end, tween_info.OffsetTime * (i - 1), "BossGetEnergyView_middle_"..i)
    end
    ReDelayCall(self, function()
        UITween.AlphaShow(self.view_name, self.node_list.right_render_1, tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
        UITween.AlphaShow(self.view_name, self.node_list.right_render_2, tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
        UITween.AlphaShow(self.view_name, self.node_list.icon, tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
        UITween.AlphaShow(self.view_name, self.node_list.btn_get, tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
    end, tween_info.BgAlphaTime, "BossGetEnergyView")


end

function BossGetEnergyView:LoadCallBack()
    self.node_list.btn_close.button:AddClickListener(BindTool.Bind1(self.Close, self))
    self.node_list.btn_get.button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
end

function BossGetEnergyView:OnClickGet()
    local can_get, seq = BossWGData.Instance:JudgeCanGetBossXianli()
    if can_get then
        BossAssistWGCtrl.Instance:SendGetXianli(seq)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.HadPassTime)
    end
end

function BossGetEnergyView:OnFlush()
    local cfg = BossWGData.Instance:GetBossXianliCfg()
    -- self.node_list.energy_num.text.text = 1000
    for i = 1, 2 do
        local temp_cfg = cfg[i]
        if temp_cfg then
            local state = self:GetStateBySeq(temp_cfg.seq)
            local str = state ~= 2 and Img_Bg_Name1 or Img_Bg_Name2
            local str2 = state ~= 3 and Img_State_Name1 or Img_State_Name2
            self:SetImgAsset(str2, self.node_list["time_img_state"..i])
            self:SetImgAsset(str, self.node_list["right_render_"..i])
            if state == 2 or state == 4 then
                self.node_list["time_img_state"..i]:SetActive(false)
            else
                self.node_list["time_img_state"..i]:SetActive(true)
            end
            local color = state == 2 and "#FFDCB0" or COLOR3B.DEFAULT
            local color1 = state == 2 and "#FFDCB0" or COLOR3B.DEFAULT
            local time_str = self:GetTimerStr(temp_cfg.start_time, temp_cfg.end_time)
            local time_str1 = ToColorStr(time_str, color)
            local str = ToColorStr(Language.Boss.Time_Name_Str[i]..time_str1, color1)
            self.node_list["time_txt_"..i].text.text = str
            if state == 3 then
                self.node_list.get_text.text.text = Language.Boss.GetStateStr[1]
            end
        end
    end
    local can_get, seq = BossWGData.Instance:JudgeCanGetBossXianli()
    --self.node_list.icon:SetActive(can_get)
    self.node_list.canget_effect:SetActive(can_get)
    self.node_list.canget_remind:SetActive(can_get)
    XUI.SetGraphicGrey(self.node_list.btn_get,not can_get)

    if can_get then
        self.node_list.get_text.text.text = Language.Boss.GetStateStr[0]    
    end
  
    if not IsEmptyTable(cfg) and not IsEmptyTable(cfg[1]) then
        local item = cfg[1].reward_item
        if not IsEmptyTable(item) then
            if not self.get_item then
                self.get_item = ItemCell.New(self.node_list["icon_img"])
            end
            self.get_item:SetData(item)
            -- self.node_list.energy_num.text.text = string.format("X%s", item.num)
        end
    end
end

function BossGetEnergyView:SetImgAsset(name, node_list)
    local bundle, asset = ResPath.GetBossAssitImg(name)
    node_list.image:LoadSprite(bundle, asset)
end

function BossGetEnergyView:GetTimerStr(start_time, end_time)
    local hour1 = string.format("%02d",start_time / 100)
    local min1 = string.format("%02d",start_time % 100)
    local hour2 = string.format("%02d",end_time / 100)
    local min2 = string.format("%02d",end_time % 100) 
    return hour1 .. ":" .. min1 .. "-".. hour2 .. ":" .. min2
end

--已错过1  可领取2  已领取3  时间未到4
function BossGetEnergyView:GetStateBySeq(seq)
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local flag_tb = BossAssistWGData.Instance:GetXianliFlagTbIndex(seq)
    local cfg = BossWGData.Instance:GetBossXianliCfg()
    local temp = cfg[seq + 1]
    local start_time = TimeUtil.FormatCfgTimestamp(temp.start_time)
    local end_time = TimeUtil.FormatCfgTimestamp(temp.end_time)
    if flag_tb == 1 then
        return 3
    end
    if start_time <= server_time and server_time <= end_time then
        return 2
    elseif start_time > server_time then --只能在时间段内打开界面
        return 4
    elseif end_time < server_time then
        return 1
    end
end