
XianLingBigRewardTip = XianLingBigRewardTip or BaseClass(SafeBaseView)
function XianLingBigRewardTip:__init()
	self.view_style = ViewStyle.Half
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_big_reward")
end

function XianLingBigRewardTip:__delete()

end

function XianLingBigRewardTip:ReleaseCallBack()
	if self.model_display_list then
		self.model_display_list:DeleteMe()
		self.model_display_list = nil
	end
end

function XianLingBigRewardTip:OpenCallBack()

end

function XianLingBigRewardTip:LoadCallBack()
	self.node_list["confirm_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo, self))
	self.node_list["notice_btn"].button:AddClickListener(BindTool.Bind(self.OnClicNotNotice, self))
end

function XianLingBigRewardTip:OnClicNotNotice()
	local cur_state = XianLingGuZhenWGData.Instance:GetNolongerTipFlag()
	XianLingGuZhenWGData.Instance:SetNoLongerTipFlag(not cur_state)
	self:FlushNoLonger()
end

function XianLingBigRewardTip:OnClickGo()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN) then
		ViewManager.Instance:Open(GuideModuleName.XianLingGuZhen)
		self:Close()
	end
end

function XianLingBigRewardTip:SetData(data)
	self.data = data or {}
end


function XianLingBigRewardTip:OnFlush()
	if not IsEmptyTable(self.data) then
		if not self.model_display_list then
			self.model_display_list = OperationActRender.New(self.node_list["model_display"])
			self.model_display_list:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
		end
		local display_data = {}
		display_data.item_id = self.data.item_id
		display_data.render_type = 0
		display_data.hide_model_block = false
		display_data.model_rt_type = ModelRTSCaleType.L
		self.model_display_list:SetData(display_data)
		self:FlushNoLonger()
		local power_num = ItemShowWGData.CalculateCapability(self.data.item_id, true)
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)

		local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(self.data.item_id, item_cfg.sys_attr_cap_location)
		if sys_type == ITEMTIPS_SYSTEM.MOUNT or
			sys_type == ITEMTIPS_SYSTEM.LING_CHONG or
			sys_type == ITEMTIPS_SYSTEM.HUA_KUN or
			sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
			self.node_list["common_max_capability_style"]:SetActive(true)
			self.node_list["common_capability_style"]:SetActive(false)
			self.node_list["max_cap_value"].text.text = power_num
		else
			power_num = ItemShowWGData.CalculateCapability(self.data.item_id)
			if power_num == 0 and item_cfg and item_cfg.capability_show > 0 then
				power_num = item_cfg.capability_show
			end

			self.node_list["cap_value"].text.text = power_num
			self.node_list["common_capability_style"]:SetActive(true)
			self.node_list["common_max_capability_style"]:SetActive(false)
		end

		local item_name = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
		self.node_list.title.text.text = item_cfg.name
		self.node_list["desc"].text.text = string.format(Language.XianLingGuZhen.GetBigRewardDesc,self.data.name, item_name)
	end
end

function XianLingBigRewardTip:FlushNoLonger()
	local no_longer = XianLingGuZhenWGData.Instance:GetNolongerTipFlag()
	self.node_list["no_tip_flag"]:SetActive(no_longer)
end