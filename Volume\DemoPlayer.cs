#if UNITY_EDITOR

using System;
using DG.Tweening;
using ScreenEffects;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;

//美术案例播放用
[ExecuteAlways]
public class DemoPlayer : MonoBehaviour
{
    public VolumeController volumeController;
    public ScreenEffectFeature screenEffectFeature;
    public Camera camera;
    public ParticleSystem particle;
    public float fadeIn = 0.5f;
    public float fadeOut = 0.5f;
    private ScreenEffect m_Zone;
    private ScreenEffect m_WangQi;
    
    private bool m_PlayablePlaying = false;
    private bool m_Paused = false;
    private float m_PlayableDuration => fadeIn + fadeOut;
    private float m_PlayableTime;
    private float m_ParticalClipIn;
    private Action m_PlayableOnStart;
    private Action<float> m_PlayableOnUpdate;
    private Action m_PlayableOnPause;
    private Action m_PlayableOnContinue;
    private Action m_PlayableOnEnd;
    private Vector3 m_OriginalCameraPos;

    private void OnEnable()
    {
        if (volumeController == null)
            volumeController = this.GetComponentInChildren<VolumeController>();
        if (screenEffectFeature == null)
            screenEffectFeature = this.GetComponentInChildren<ScreenEffectFeature>();
        if (camera == null)
            camera = this.GetComponentInChildren<Camera>();
        if (screenEffectFeature)
        {
            m_Zone = screenEffectFeature["Zone"];
            m_WangQi = screenEffectFeature["WangQi"];
        }

        if (camera)
        {
            m_OriginalCameraPos = camera.transform.position;
        }
    }

    private void Update()
    {
        UpdatePlayable();
    }
    
    private void ShakeCamera()
    {
        CameraShake.Shake();
    }

#region Playable

    private void UpdatePlayable()
    {
        if (m_PlayablePlaying && !m_Paused)
        {
            float time = m_PlayableTime;
            time += Time.deltaTime;
            if (m_PlayableTime < fadeIn && time >= fadeIn)
            {
                time = fadeIn;
                Pause();
            }
            else if (time >= m_PlayableDuration)
            {
                Stop();
            }
            m_PlayableOnUpdate?.Invoke(time);
            m_PlayableTime = time;
        }
    }
    private void Play()
    {
        m_PlayablePlaying = true;
        m_Paused = false;
        m_PlayableTime = 0;
        m_PlayableOnStart?.Invoke();
    }
    
    private void Pause()
    {
        m_Paused = true;
        m_PlayableOnPause?.Invoke();
    }

    private void Continue()
    {
        m_Paused = false;
        m_PlayableOnContinue?.Invoke();
    }

    private void Stop()
    {
        m_PlayablePlaying = false;
        m_PlayableOnEnd?.Invoke();
    }

#endregion

    public void PlayZone()
    {
        m_PlayableOnStart = () =>
        {
            //开始时播放Zone屏幕特效、镜头拉远、径向模糊
            screenEffectFeature.DoEffect(m_Zone, 1, 0.5f);
            volumeController.DoRadialBlur(new Vector2(0.5f, 0.5f), 0.2f, 0.0f, 0.2f, 0.05f);
            camera.transform.DOMove(new Vector3(0, 10.6f, 24.44f), 0.2f);
            Invoke("ShakeCamera", 0.2f); 
            particle.gameObject.SetActive(true);
            m_ParticalClipIn = 0;
        };
        m_PlayableOnUpdate = (f) =>
        {
            //驱动粒子特效
            particle.Simulate(f + m_ParticalClipIn);
        };
        m_PlayableOnPause = () =>
        {

        };
        m_PlayableOnContinue = () =>
        {
            //继续时播放关闭Zone屏幕特效、相机归原位
            screenEffectFeature.DoEffect(m_Zone, 0, 0.5f);
            camera.transform.DOMove(m_OriginalCameraPos, 0.4f);
            
            //假如特效时长为5秒，淡入淡出时间各为0.5秒，那么淡入区间为[0 ~ 0.5], 淡出区间为[4.5 ~ 5]
            m_ParticalClipIn = particle.main.duration - fadeOut - m_PlayableTime; 
        };
        m_PlayableOnEnd += () =>
        {
            particle.gameObject.SetActive(false);
        };
        Play();
    }
    public void PlayWangQi()
    {
        m_PlayableOnStart = () =>
        {
            screenEffectFeature.DoEffect(m_WangQi, 1, fadeIn);
            m_WangQi.block.SetVector("_CenterPosition", this.transform.position);
        };
        m_PlayableOnContinue = () =>
        {
            screenEffectFeature.DoEffect(m_WangQi, 0, fadeOut);
        };
        Play();
    }
    
    [CustomEditor(typeof(DemoPlayer))]
    public class DemoPlayerEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            //base.OnInspectorGUI();
            var comp = target as DemoPlayer;

            if (comp.m_Zone != null)
            {
                EditorGUILayout.PropertyField(this.serializedObject.FindProperty("particle"), new GUIContent("领域特效"));
            }
            EditorGUILayout.PropertyField(this.serializedObject.FindProperty("fadeIn"), new GUIContent("淡入时间"));
            EditorGUILayout.PropertyField(this.serializedObject.FindProperty("fadeOut"), new GUIContent("淡出时间"));
            serializedObject.ApplyModifiedProperties();

            if (!Application.isPlaying)
            {
                return;
            }

            if (comp.m_Zone != null)
            {
                if (!comp.m_PlayablePlaying)
                {
                    if (GUILayout.Button("播放【领域展开】"))
                    {
                        comp.PlayZone();
                    }
                }
                else
                {
                    if (comp.m_Paused)
                    {
                        if (GUILayout.Button("关闭【领域展开】"))
                        {
                            comp.Continue();
                        }
                    }
                }
            }

            if (comp.m_WangQi != null)
            {
                if (!comp.m_PlayablePlaying)
                {
                    if (GUILayout.Button("播放【望气】"))
                    {
                        comp.PlayWangQi();
                    }
                }
                else if (comp.m_Paused)
                {
                    if (GUILayout.Button("关闭【望气】"))
                        comp.Continue();
                }
            }
        }
    }
}

#endif