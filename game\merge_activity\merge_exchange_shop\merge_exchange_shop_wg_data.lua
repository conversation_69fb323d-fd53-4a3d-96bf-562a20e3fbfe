MergeExchangeShopWGData = MergeExchangeShopWGData or BaseClass()
local this = MergeExchangeShopWGData
function this:__init()
	MergeExchangeShopWGData.Instance = self
	self.data = {}
	self.period = 1
	self:LoadConfig()
	RemindManager.Instance:Register(RemindName.Merge_ExchangeShop, BindTool.Bind(self.IsShowRemind, self))
	MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT,{[1] = MERGE_EVENT_TYPE.LEVEL},
			BindTool.Bind(self.GetActCanOpen, self),
			BindTool.Bind(self.IsShowRemind, self))

	self:RegisterRemindInBag(RemindName.Merge_ExchangeShop)

	-- self.item_change_callback = BindTool.Bind(self.ItemChangeCallBack,self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
end

function this:__delete()
	RemindManager.Instance:UnRegister(RemindName.Merge_ExchangeShop)

	-- ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
	-- self.item_change_callback = nil

	self.event_item_id = nil
end

function this:RegisterRemindInBag(remind_name)
    local map = {}

    local cfg = ConfigManager.Instance:GetAutoConfig("combineserve_activity_convert_shop_auto").item
    for k,v in pairs(cfg) do
    	map[v.reward_item.item_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

-- 获取数据对象
function this:GetData()
	return self.data
end

function this:LoadConfig()
	local config = ConfigManager.Instance:GetAutoConfig("combineserve_activity_convert_shop_auto").item
	self.item_cfg = ListToMap(config, "period", "sort")
	local show_config = ConfigManager.Instance:GetAutoConfig("combineserve_activity_convert_shop_auto").show
	self.show_cfg = show_config
	self:InitData()
end

-- 初始化数据
function this:InitData()

	self.exchange_shop_list = {}
	self.event_item_id = {}
	self.data = {}
	self.data.show_remind = 0
	local cur_cfg = self.show_cfg[self.period] or self.show_cfg[1]
	self.data.model_id = cur_cfg.show_model
	self.data.show_item_id = cur_cfg.show_item_id
	self.data.show_model = cur_cfg.show_model ~= "" and self.data.show_item_id == ""
	self.data.show_model_bundle = cur_cfg.show_model_bundle
	self.data.position = cur_cfg.position
	self.data.rotation = cur_cfg.rotation
	self.data.scale = cur_cfg.scale
	self.data.item_name = cur_cfg.item_name

	local list = CheckList(self.item_cfg, self.period)
	for i = 1, #list do
		local cfg = CheckList(self.item_cfg, self.period, i)
		local data = {}
		data.item = cfg.reward_item
		local item_cfg = ItemWGData.Instance:GetItemConfig(data.item.item_id)
		data.name = item_cfg.name
		data.max_exchange_times = cfg.limit_num
		data.can_exchange_times = cfg.limit_num
		data.cost = cfg.stuff_count
		data.stuff_id = cfg.stuff_id
		data.stuff_icon = cfg.stuff_icon
		data.seq = cfg.seq
		table.insert(self.exchange_shop_list, data)

		self.event_item_id[cfg.stuff_id] = true
	end

	self.shop_list_num = #list
end

function this:SaveData(protocol)
	if protocol.cur_act_period ~= self.period then
		self.period = protocol.cur_act_period
		self:InitData()
	end

	--触发一下开启。。
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT)
	if act_info then
		MergeActivityWGData.Instance:SetAleardayOpenActivity(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT, act_info.status)
	end

	for k, data in ipairs(self.exchange_shop_list) do
		data.can_exchange_times = data.max_exchange_times - protocol.record_list[data.seq]
	end

	self.exchange_shop_list = TableSortByCondition(self.exchange_shop_list, function (value)
		return value.can_exchange_times ~= 0
	end)
end

-- 处理数据
function this:DisposeData(cur_data)

end

-- 红点判断
function this:IsShowRemind()
	if not MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT) then
		return 0
	end
	for k, data in pairs(self.exchange_shop_list) do
		if data.can_exchange_times > 0 then
			local num = ItemWGData.Instance:GetItemNumInBagById(data.stuff_id)
			if num >= data.cost then
				return 1
			end
		end
	end
	return 0
end

-- WGCtrl层物品数据改变调用
function this:CheckItemNum()

end

function this:GetShopListNum()
	return self.shop_list_num
end

function this:GetExchangeShopList()
	return self.exchange_shop_list
end

function this:CheckPeriod()

end

function this:GetActCanOpen()
   --暂时手动开启
	-- local vo = GameVoManager.Instance:GetMainRoleVo()
	-- local cur_cfg = self.show_cfg[self.period] or self.show_cfg[1]
    -- if vo.level >= cur_cfg.open_level then
	-- 	return true
	-- end
    -- return false
    return true
end

function this:ItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.event_item_id and self.event_item_id[change_item_id] then
		if not MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_CONVERT) then
			return
		end

		RemindManager.Instance:Fire(RemindName.Merge_ExchangeShop)
	end
end