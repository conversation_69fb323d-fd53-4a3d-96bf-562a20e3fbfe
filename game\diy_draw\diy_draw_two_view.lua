DIYDrawTwoView = DIYDrawTwoView or BaseClass(SafeBaseView)
function DIYDrawTwoView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "diydraw_two_view")
end

function DIYDrawTwoView:LoadCallBack()
    for i = 1, 2 do
        self.node_list["draw_btn_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, i))
        self.node_list["const_img_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self))
    end
    XUI.AddClickEventListener(self.node_list["btn_gailv"], BindTool.Bind1(self.OnClickGaiLvShow, self))
    XUI.AddClickEventListener(self.node_list["btn_choose_reward"], BindTool.Bind1(self.OnClickChooseReward, self))
    XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind1(self.Close, self))

    if self.reward_list == nil then
        self.reward_list = {}
        for i = 1, 6 do
            self.reward_list[i] = DIYDrawTwoRewardRender.New(self.node_list["reward_pos_" .. i])
        end
    end

    if self.item_data_change_callback == nil then
        self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
    end
end

function DIYDrawTwoView:ReleaseCallBack()
    if self.reward_list then
        for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
	    self.reward_list = nil
	end

    if CountDownManager.Instance:HasCountDown("diydraw_two_down") then
		CountDownManager.Instance:RemoveCountDown("diydraw_two_down")
	end

    if self.item_data_change_callback then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
        self.item_data_change_callback = nil
    end
end

function DIYDrawTwoView:OpenCallBack()
    DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.INFO)
end

function DIYDrawTwoView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushshowView()
            self:LoginTimeCountDown()
        end
	end
end

function DIYDrawTwoView:FlushshowView()
    self:FlushDrawBtnShow()

    local show_reward_list = DIYDrawWGData.Instance:GetDIY2SpecialRewardShowList()
    if show_reward_list then
        for i = 1, #show_reward_list do
            self.reward_list[i]:SetData(show_reward_list[i])
        end
    end
end

function DIYDrawTwoView:FlushDrawBtnShow(is_flush_num)
    local cfg = DIYDrawWGData.Instance:GetDIY2DrawConsumeCfg()
    if cfg == nil then
        return
    end

    local item_cfg
    local mode_cfg = DIYDrawWGData.Instance:GetDrawTwoItem()
    local item_id = mode_cfg.cost_item_id
    local count
    for i = 1, 2 do
        if cfg[i] then
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then
                    --道具图标
                    self.node_list["const_img_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                end
                --抽几次
                self.node_list["txt_buy_" .. i].text.text = string.format(Language.FiveElementsTreasury.BtnDrawDesc, cfg[i].times)
            end
             --道具红点数量
             count = ItemWGData.Instance:GetItemNumInBagById(item_id)
             self.node_list["btn_red_" .. i]:SetActive(count >= cfg[i].cost_item_num)
             local color = count >= cfg[i].cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
             local left_str = ToColorStr(count, color) 
             self.node_list["const_lbl_" .. i].text.text = left_str .."/".. cfg[i].cost_item_num
             self.node_list["const_lbl_"..i].text.color = Str2C3b(color)
        end
    end
end


function DIYDrawTwoView:OnClickRecord(btn_index)
    local cfg = DIYDrawWGData.Instance:GetDIY2DrawConsumeCfg()
    cfg = cfg[btn_index]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
    if num >= cfg.cost_item_num then
        DIYDrawWGData.Instance:CacheOrGetDIY2DrawIndex(btn_index)
        --发送协议
        DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cfg.mode)
    else
        DIYDrawWGCtrl.Instance:ClickUseDrawItem(DIYDRAW_TYPE.DIY2, btn_index, function ()
            self:OnClickDrawBuy(btn_index)
        end)
    end
end

function DIYDrawTwoView:ShowDrawItemTips()
    local cfg = DIYDrawWGData.Instance:GetDrawTwoItem()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.cost_item_id})
end

function DIYDrawTwoView:OnClickGaiLvShow() --抽奖概率
    local info = DIYDrawWGData.Instance:GetDrawTwoProbabilityInfo()
    TipWGCtrl.Instance:OpenTipsRewardProView(info)
    -- DIYDrawWGCtrl.Instance:OpenDrawTwoGaiLvView()
end

function DIYDrawTwoView:OnClickChooseReward() --切换奖池
    DIYDrawWGCtrl.Instance:OpenDIY2DrawChooseView()
end

function DIYDrawTwoView:OnItemDataChange(change_item_id)
    if DIYDrawWGData.Instance:GetDIY2StuffCfg(change_item_id) then
        self:FlushDrawBtnShow(true)
	end
end

--点击购买
function DIYDrawTwoView:OnClickDrawBuy(draw_type)
    local cfg = DIYDrawWGData.Instance:GetDIY2DrawConsumeCfg()
    local item_cfg = DIYDrawWGData.Instance:GetDrawTwoItem()
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.cost_item_id)
    local consume = item_cfg.cost_gold * (cur_cfg.cost_item_num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        DIYDrawWGData.Instance:CacheOrGetDIY2DrawIndex(draw_type)
		--发送协议
        DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cur_cfg.mode)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end


------------------------------------活动时间倒计时
function DIYDrawTwoView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            local time_str = string.format(Language.CatExplore.ActivityTime, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))
            self.node_list["act_time"].text.text = time_str
            CountDownManager.Instance:AddCountDown("diydraw_two_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end

function DIYDrawTwoView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        local time_str = string.format(Language.CatExplore.ActivityTime, TimeUtil.FormatSecondDHM8(valid_time))
        self.node_list["act_time"].text.text = time_str
    end
end

function DIYDrawTwoView:OnComplete()
    self.node_list["act_time"].text.text = ""
    self:Close()
end
--------------------------------------------------------


DIYDrawTwoRewardRender = DIYDrawTwoRewardRender or BaseClass(BaseRender)
function DIYDrawTwoRewardRender:__init()
    XUI.AddClickEventListener(self.node_list["icon"], BindTool.Bind1(self.OnClickItemTipsShow, self))
end

function DIYDrawTwoRewardRender:OnFlush()
	if not self.data then
		return
	end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)
    
    if item_cfg ~= nil then
        local cell_bundle, cell_asset = ResPath.GetCommon("a3_ty_wpk_" .. item_cfg.color)
        self.node_list.quality_box.image:LoadSprite(cell_bundle, cell_asset, function()
        self.node_list.quality_box.image:SetNativeSize()
        end)

        self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        local eff_bundle, eff_asset = ResPath.GetWuPinKuangEffectUi(BaseCell_Ui_Effect[item_cfg.color])
		self.node_list["effect"]:SetActive(true)
		self.node_list.effect:ChangeAsset(eff_bundle, eff_asset)

    end
end

function DIYDrawTwoRewardRender:OnClickItemTipsShow()
    if not self.data then
		return
	end

    TipWGCtrl.Instance:OpenItem({item_id = self.data.item.item_id})
end