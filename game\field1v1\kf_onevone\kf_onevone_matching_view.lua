KFOneVOneMatchingView = KFOneVOneMatchingView or BaseClass(SafeBaseView)

local COUNT_TIME1 = 120
local COUNT_TIME2 = 60
local START_TIME = 3
local ZERO_POS = 384

function KFOneVOneMatchingView:__init()
	self:SetMaskBg(false, false)
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_onevone_matching")
end

function KFOneVOneMatchingView:ReleaseCallBack()
	self.start_fight = nil

	if CountDownManager.Instance:HasCountDown("Kf1v1match_start") then
		CountDownManager.Instance:RemoveCountDown("Kf1v1match_start")
	end

	if self.my_head_cell then
		self.my_head_cell:DeleteMe()
		self.my_head_cell = nil
	end
	if self.enemy_head_cell then
		self.enemy_head_cell:DeleteMe()
		self.enemy_head_cell = nil
	end
end

function KFOneVOneMatchingView:CloseCallBack()

end

function KFOneVOneMatchingView:LoadCallBack()

	self.my_head_cell = BaseHeadCell.New(self.node_list["my_head_icon"])
	self.enemy_head_cell = BaseHeadCell.New(self.node_list["enemy_head_icon"])

	local role_vo = RoleWGData.Instance.role_vo
	self.node_list.role_name_1.text.text = role_vo.name
	self.node_list.role_level_1.text.text = Language.Rank.RankValueName[2] .. role_vo.level
	self.node_list.role_power_1.text.text = role_vo.capability

	local enemy_info = KFOneVOneWGData.Instance:GetMyEnemyInfo()
	if not IsEmptyTable(enemy_info) then
		self:StartFight()
	end
end


--复位
function KFOneVOneMatchingView:ShowIndexCallBack()
	self.node_list.big_bg.image.raycastTarget = false
	self.node_list["match_content"].canvas_group.blocksRaycasts = true
	self.node_list["match_content"].canvas_group.alpha = 1
	self.node_list["match_content"].rect.anchoredPosition = Vector2(0,0)
	self:PKMatchingAni()
end

function KFOneVOneMatchingView:PKMatchingAni()

 	--self.node_list.rotate_image_1:SetActive(false)
	--self.node_list.left_red_image.rect.anchoredPosition = Vector2(18, 600)
	--self.node_list.right_blue_image.rect.anchoredPosition = Vector2(18,-600)
	--  local tween1 = self.node_list.left_red_image.rect:DOAnchorPosY(204, 0.3)
	--  local tween2 = self.node_list.right_blue_image.rect:DOAnchorPosY(-197, 0.3)
	--  tween1:SetEase(DG.Tweening.Ease.Linear)
	--  tween2:SetEase(DG.Tweening.Ease.Linear)
	--  tween1:OnComplete(function ()
	--  	self.node_list.rotate_image_1:SetActive(true)
	--  end)
end

function KFOneVOneMatchingView:OnFlush()
	self:FLushAllHead()
	-- self:FLushRoleNum()
end

--刷新头像
function KFOneVOneMatchingView:FLushAllHead()
	local role_vo = RoleWGData.Instance.role_vo

	-- 第三个位置代表自己\
	self.my_head_cell:SetActive(true)
	self.my_head_cell:SetData({role_id = RoleWGData.Instance:InCrossGetOriginUid(),is_show_main = true, prof = role_vo.prof, sex = role_vo.sex})

	local bundle, asset = ResPath.GetF2Field1v1("a3_jjc_txkh")
	self.my_head_cell:ChangeBg(bundle, asset, true)

	local enemy = KFOneVOneWGData.Instance:GetMyEnemyInfo()
	if not IsEmptyTable(enemy) then
		local name = ""
		if enemy.name and enemy.name ~= "" then
			local name_list = Split(enemy.name, "_")
			local has_s = string.find(name_list[2],"s")
            if has_s then
                name = string.format(Language.BiZuo.ServerName_2,name_list[2],name_list[1])
            else
                name = string.format(Language.BiZuo.ServerName_1,name_list[2],name_list[1])
            end
		end
		self.node_list.role_name_2.text.text = name
		self.node_list.role_level_2.text.text = Language.Rank.EmenyLevel .. enemy.level
		self.node_list.role_power_2.text.text = enemy.capability
		local uuid = enemy.uuid
		local key = uuid.temp_low --.. uuid.temp_high
		self:FlushRoleHeadIcon(key, enemy.sex,enemy.prof)
	end
end

--重置头像
function KFOneVOneMatchingView:ResetHeadIcon()
	self.enemy_head_cell:SetData({})
	self.enemy_head_cell:SetActive(false)
end

--刷新匹配人数，开始倒计时
function KFOneVOneMatchingView:FLushRoleNum()
	local oppo_info = KFOneVOneWGData.Instance:GetMyEnemyInfo()
	if IsEmptyTable(oppo_info) then
		self:Close()
	else
		-- self:StartCountDown()
	end

end

--刷新头像
function KFOneVOneMatchingView:FlushRoleHeadIcon(key, sex,prof, fashion_photoframe)
	self:ResetHeadIcon()

	if self.enemy_head_cell then
		self.enemy_head_cell:SetActive(true)
		self.enemy_head_cell:SetData({role_id = key,prof = prof, sex = sex})

		local bundle, asset = ResPath.GetF2Field1v1("a3_jjc_txkl")
		self.enemy_head_cell:ChangeBg(bundle, asset, true)
	end
end


--开始倒计时，不传时间为60s总时间，传了为total_time倒计时
function KFOneVOneMatchingView:StartCountDown()
	if CountDownManager.Instance:HasCountDown("Kf1v1match_start") then
		CountDownManager.Instance:RemoveCountDown("Kf1v1match_start")
	end
	CountDownManager.Instance:AddCountDown("Kf1v1match_start", BindTool.Bind(self.UpdateCountDown,self),
		BindTool.Bind(self.CompleteStartCountDown, self), nil, START_TIME , 1)

	local bundle, asset = ResPath.GetF2Field1v1(START_TIME)
	self.node_list.matching_time.image:LoadSprite(bundle, asset, function()
		self.node_list.matching_time.image:SetNativeSize()
	end)

	self:DoTweenScaleContent(self.node_list.matching_time)
end

--完成3秒倒计时
function KFOneVOneMatchingView:CompleteStartCountDown()
	self:Close()
end

--倒计时处理
function KFOneVOneMatchingView:UpdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	if time <= 0 then
		--self.node_list["matching_time"].text.text = ""
	else
		local bundle, asset = ResPath.GetF2Field1v1(time)
		self.node_list.matching_time.image:LoadSprite(bundle, asset, function()
			self.node_list.matching_time.image:SetNativeSize()
		end)
		--self.node_list["matching_time"].text.text = time
		self:DoTweenScaleContent(self.node_list.matching_time)
	end
end

function KFOneVOneMatchingView:DoTweenScaleContent(node)
	local scale = Vector3(1, 1, 1)
	if node ~= nil then
		node:SetActive(true)
		node.rect.localScale = Vector3(2, 2, 2)
		node.rect:DOScale(scale, 0.3)
	end
end


--匹配到人，然后3秒倒计时
function KFOneVOneMatchingView:StartFight()
	--local fight_timestamp_type = KFOneVOneWGData.Instance:GetFightTimestampType()
	-- if fight_timestamp_type == KUAFUONEVONE_STATUS.PREPARE then
	-- 	self:CompleteStartCountDown()
	-- 	return
	-- end
	self.start_fight = true
	-- self:StartCountDown()
	self.node_list["match_content"].canvas_group.blocksRaycasts = true
	local enemy = KFOneVOneWGData.Instance:GetMyEnemyInfo()
	if not IsEmptyTable(enemy) then
		self.node_list.role_name_2.text.text = enemy.name
		self.node_list.role_level_2.text.text = Language.Rank.EmenyLevel .. enemy.level
		self.node_list.role_power_2.text.text = Language.Rank.EmenyCap .. enemy.capability
		local uuid = enemy.uuid
		local key = uuid.temp_low --.. uuid.temp_high
		self:FlushRoleHeadIcon(key, enemy.sex,enemy.prof)
	end
	-- self.node_list.matching_pa.rect.anchoredPosition = Vector2(11, -72)
end
