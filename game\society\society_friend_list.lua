SocietyFriendListView = SocietyFriendListView or BaseClass(SafeBaseView)

function SocietyFriendListView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	--self.m_data_mgr = SocietyWGData.Instance

	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_friendlist")
	
	self.m_choose_user_info = nil
	self.default_select_index = 1 --默认选择第一个
end

function SocietyFriendListView:ReleaseCallBack()
	if nil ~= self.friend_list then
		self.friend_list:DeleteMe()
		self.friend_list = nil
	end

	self.list_erji_friendlist = nil
	--self.m_data_mgr = nil
end

function SocietyFriendListView:LoadCallBack()
	self.list_erji_friendlist = self.node_list["list_frienditemlist"]

	XUI.AddClickEventListener(self.node_list["btn_chooseOK"], BindTool.Bind1(self.OnClickOK, self))
	XUI.AddClickEventListener(self.node_list["btn_window_close"], BindTool.Bind1(self.OnClose, self))

	self:CreateFriendList()
end

function SocietyFriendListView:CloseCallBack()
	
end

function SocietyFriendListView:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()
end

function SocietyFriendListView:ShowIndexCallBack()
	self:Flush()
end


function SocietyFriendListView:OnClickOK()
	local callbackfucn = SocietyWGCtrl.Instance:GetFriendListCallBack()
	if nil == callbackfucn then
		return
	end
	-- print_error("i'm not ok")

	if nil == self.m_choose_user_info then
		if nil ~= Language.Society["NotChooseUser2"] then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society["NotChooseUser2"])
		end
		return
	end
	callbackfucn(self.m_choose_user_info)
	self.m_choose_user_info = nil
	self:Close()
end

function SocietyFriendListView:OnClose()
	self:Close()
end
function SocietyFriendListView:CreateFriendList()
	self.friend_list = AsyncListView.New(SocietyFriendListRender, self.list_erji_friendlist)
	self.friend_list:SetSelectCallBack(BindTool.Bind1(self.FriendListSelectCallBack, self))	
end

--好友列表行选中事件
function SocietyFriendListView:FriendListSelectCallBack(item_data)
	if nil ~= item_data and nil ~= item_data.data then
		self.m_choose_user_info = item_data.data
	end
end

--刷新好友面板
function SocietyFriendListView:OnFlush()
	local data = SocietyWGData.Instance:GetFriendList()
	if nil ~= data and nil ~= self.friend_list then
		self.friend_list:SetDataList(data, 0)
		self.friend_list:JumpToTop()
	end


end