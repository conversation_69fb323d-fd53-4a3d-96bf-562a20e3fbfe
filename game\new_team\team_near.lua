TeamView = TeamView or BaseClass(SafeBaseView)

local TeamWorldInvite = "near_team_world_invite" --喊话
local RefreshTeamList = "near_refresh_team_list" --刷新队伍列表按钮

function TeamView:NearLoadCallBack()
    self.node_list["near_layout_blank_tip2"]:SetActive(false)
    self.near_list = AsyncListView.New(PingTaiQuickListItem, self.node_list["near_ph_quick_list"])

	--这里按钮接口都一样，直接调用平台那边也是一样的
	XUI.AddClickEventListener(self.node_list["near_btn_auto_match"], BindTool.Bind1(self.OnClickNearAutoMatch, self)) --自动匹配 （只有队长显示）
	XUI.AddClickEventListener(self.node_list["near_btn_flush"], BindTool.Bind(self.NearOnClickFlush, self, true))
	XUI.AddClickEventListener(self.node_list["near_btn_create_team"], BindTool.Bind1(self.OnClickNearCrateTeam, self)) --未组队：创建队伍， 已组队：前往目标
	XUI.AddClickEventListener(self.node_list["near_btn_auto_req"], BindTool.Bind1(self.PingTaiAutoRequest, self)) --未组队：一键申请， 已组队：退出队伍
	XUI.AddClickEventListener(self.node_list["near_btn_apply"], BindTool.Bind1(self.PingTaiOnClickApplyList, self)) 	--申请列表
	XUI.AddClickEventListener(self.node_list["near_btn_speak"], BindTool.Bind1(self.NearOnClickWorldTalk, self)) 	--喊话（世界， 跨服）

	self:OnTeamChange()
end
function TeamView:NearReleaseCallBack()
    if self.near_list then
		self.near_list:DeleteMe()
		self.near_list = nil
	end
	self.near_has_change_btn_gray = nil
end
function TeamView:NearShowIndexCallBack()
	--self:Flush(TabIndex.team_near, "NearFlushList", {NearFlushList = true})
	SocietyWGCtrl.Instance:SendTeamListReq(-1 , -1)
end
function TeamView:OnFlushNear(key, value)
    if key == "NearFlushList" then
		local team_list = NewTeamWGData.Instance:GetNearTeamList()
		if #team_list == 0 then
			self.node_list["near_layout_blank_tip2"]:SetActive(true)
		else
			self.node_list["near_layout_blank_tip2"]:SetActive(false)
		end
		self.near_list:SetDataList(team_list, 3)
	end

	self:NearFlushMatchBtn()
	self:NearFlushOtherBtn()
end

function TeamView:NearFlushOtherBtn()
	local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
	local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if is_in_team then --如果有队伍
		self.node_list.near_btn_auto_req_text.text.text = Language.NewTeam.QuitTeamBtnText 		--退出队伍
		self.node_list.near_btn_create_team_text.text.text = Language.NewTeam.GoToGoalBtnText		--前往目标
		local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
		--if team_type == 0 then		
		self.node_list["near_btn_create_team"]:CustomSetActive(false)
		--else
		--	self.node_list["near_btn_create_team"]:CustomSetActive(true)	
		--end
		self.node_list["near_btn_auto_match"]:CustomSetActive(is_leader and team_type ~= 0 and team_type ~= -1)
		if is_leader then --如果是队长
			self.node_list["near_btn_apply"]:CustomSetActive(true)
		else
			self.node_list["near_btn_apply"]:CustomSetActive(false)
		end
		local is_show_talk = 1 == SocietyWGData.Instance:GetIsInTeam() and SocietyWGData.Instance:GetTeamMemberCount() < (GoalQingYuanFbMaxCount[team_type] or 3)
		--if SocietyWGData.Instance:GetCurTeamMemberNum() == 3 then --如果满人了,隐藏喊话按钮
		--	self.node_list["near_btn_speak"]:CustomSetActive(false)
		--else
		--	self.node_list["near_btn_speak"]:CustomSetActive(true)
		--end
		self.node_list["near_btn_speak"]:CustomSetActive(is_show_talk)
	else
		self.node_list.near_btn_auto_req_text.text.text = Language.NewTeam.AutoReqBtnText 		--一键申请
		self.node_list.near_btn_create_team_text.text.text = Language.NewTeam.CreateTeamBtnText 	--创建队伍
		self.node_list["near_btn_speak"]:CustomSetActive(false)
		self.node_list["near_btn_apply"]:CustomSetActive(false)
		self.node_list["near_btn_create_team"]:CustomSetActive(true)
		self.node_list["near_btn_auto_match"]:CustomSetActive(team_type ~= 0 and team_type ~= -1)
	end

	if NotShowBtnEnter[team_type] then
		self.node_list["near_btn_auto_match"]:SetActive(false)
	end
	
	self:NearRemindState()
end
function TeamView:NearRemindState()
	local apply_list = SocietyWGData.Instance:GetReqTeamList()
	if # apply_list > 0 then
		self.node_list["NearRemind"]:SetActive(true)
		else
		self.node_list["NearRemind"]:SetActive(false)
	end
end

function TeamView:NearFlushMatchBtn()
	if NewTeamWGData.Instance:GetIsMatching() then
		self.node_list["near_btn_auto_match_text"].text.text = (Language.NewTeam.IsMatching)
	else
		self.node_list["near_btn_auto_match_text"].text.text = (Language.NewTeam.AutoMatch)
	end
end

function TeamView:NearOnClickWorldTalk()
	NewTeamWGCtrl.Instance:ShowTalkView()
end

function TeamView:NearOnClickFlush(bo)
	local function callback()
		SocietyWGCtrl.Instance:SendTeamListReq(-1, -1)
	end
	if bo then --只有点击按钮的时候，才有操作频繁判断
		OperateFrequency.Operate(callback, RefreshTeamList, 5)
	else
		callback()
	end
end

function TeamView:OnClickNearCrateTeam()
	local team_state = 1 == SocietyWGData.Instance:GetIsInTeam()
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if team_state then
		--跳转目标玩法入口界面
		local view_name, tab_index = NewTeamWGData.Instance:GetJumpViewNameAndIndex(team_type, team_fb_mode)
		if view_name and tab_index then
			FunOpen.Instance:OpenViewByName(view_name, tab_index)
		end
	else
		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(team_type, team_fb_mode, min_level, max_level)
	end
end

--自动匹配
function TeamView:OnClickNearAutoMatch()
	local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local info = {}
     --如果选中的是全部队伍，自动创建无目标
    if team_type == -1 and fb_mode == -1 then
        info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
    else
		info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
    end

	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	--如果没在匹配中，才进行一些操作
	if not is_match then
		if team_type == GoalTeamType.QingYuanFb then
			MarryWGCtrl.Instance:SendQingyuanReqInfo()
		end
        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
        local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
		local remain_count = total_count - cur_enter_count
		--组队的最大等级改为等级排行的第一名
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
        local top_lv = info.role_max_level or top_user_level
        if is_not_in_team then
            NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
            SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表
            if remain_count == 0 then 
				SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
				self:OnClickAddTimes()
				return
			end
        end
		NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
		NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, top_lv)
		NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)


		if remain_count == 0 and is_leader then 
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
			self:OnClickAddTimes()
			return
		end
		NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
	else
		local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
		if not self.pt_cancel_match_alert then
			self.pt_cancel_match_alert = Alert.New()
		end
		self.pt_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
		self.pt_cancel_match_alert:SetOkFunc(function()
			NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
		end)
		self.pt_cancel_match_alert:Open()
	end
end