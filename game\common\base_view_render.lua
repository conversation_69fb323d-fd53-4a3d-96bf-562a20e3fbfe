local UILayer = GameObject.Find("GameRoot/UILayer").transform
local TypeUINameTable = typeof(UINameTable)
local TypeButton = typeof(UnityEngine.UI.Button)
local TypeImage = typeof(UnityEngine.UI.Image)
local TypeCanvas = typeof(UnityEngine.Canvas)

BaseViewRender = BaseViewRender or BaseClass()

function BaseViewRender:__init()
	self.view_name = "none_name"
	self.view_instance = nil

	self.root_node = nil
	self.root_canvas = nil
	self.root_node_transform = nil
	self.gameobj_root_transform = nil
	self.clear_z_obj = nil

	self.mask_bg = nil
	self.mask_bg_node = nil
	self.tabbar = nil

	self.render_gameobj_t = {}
	self.ui_role_model_t = {}
	self.is_rendering = false										-- 是否渲染
	self.is_need_mask_bg = false
	self.is_safe_area_adapter = false
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.HD
	self:ResetNodeList()
end

function BaseViewRender:__delete()

end

function BaseViewRender:Clear()
	if self.__gameobj_loaders then
		ReleaseGameobjLoaders(self)
	end

	if self.__res_loaders then
		ReleaseResLoaders(self)
	end

	if nil ~= self.culling_timequest then
		GlobalTimerQuest:CancelQuest(self.culling_timequest)
		self.culling_timequest = nil
	end

	if nil ~= self.load_error_timer then
		GlobalTimerQuest:CancelQuest(self.load_error_timer)
		self.load_error_timer = nil
	end

	self.view_instance = nil
	self.root_canvas = nil
	self.root_node_transform = nil
	self.gameobj_root_transform = nil
	self.mask_bg = nil
	self.mask_bg_node = nil
	self.tabbar = nil
	self.safe_adapter = nil

	self.render_gameobj_t = {}
	self.ui_role_model_t = {}
	self.is_rendering = false

	self.clear_z_obj = nil

	self:ClearLoadingErrorObj()
end

function BaseViewRender:DestroyAllNode()
	if self.root_node then
		ResMgr:Destroy(self.root_node)
		self.root_node = nil
	end
	self:ResetNodeList()
end

function BaseViewRender:ResetNodeList()
	-- 节点树列表，通过名字索引，指向self.node_list中的name
	self.node_list = setmetatable({}, {__index = function(table, key)
		local value = rawget(table, key) or rawget(self.node_cache, key)
		if value then
			return value
		end

		for _, tbl in pairs(self.node_list) do
			value = tbl[key]
			if value then
				self.node_cache[key] = value
				return value
			end
		end

		return nil
	end})

	self.node_cache = {}
end

function BaseViewRender:OpenCallBack()
	self.base_view_effect:TryExecuteTweenOpen()
end

function BaseViewRender:CloseCallBack()
	self.base_view_effect:CloseCallback()
end

function BaseViewRender:SetViewName(view_name)
	self.view_name = view_name
end

function BaseViewRender:SetIsSafeAreaAdapter(is_safe_area_adapter)
	self.is_safe_area_adapter = is_safe_area_adapter
end

function BaseViewRender:GetNodeList()
	return self.node_list
end

function BaseViewRender:GetRootNode()
	return self.root_node
end

function BaseViewRender:SetReferenceResolution(reference_resolution)
	self.reference_resolution = reference_resolution
end

function BaseViewRender:TryCreateRooNode()
	if nil ~= self.root_node then
		return self.root_node, self.root_canvas, self.gameobj_root_transform, self.node_list
	end

	local BaseViewParentTemplate = SafeBaseView.GetBaseViewParentTemplate(self.reference_resolution)
	self.root_node = ResMgr:Instantiate(BaseViewParentTemplate, true, UILayer)
	self.root_node.name = self.view_name
	self.root_canvas = self.root_node:GetComponent(TypeCanvas)
	self.root_node_transform = self.root_node.transform
	self.gameobj_root_transform = self.root_node.transform:Find("Root")
	local z_obj = self.root_node.transform:Find("UIClearZDepth")
	if not IsNil(z_obj) then
		self.clear_z_obj = z_obj:GetComponent(typeof(UnityEngine.UI.Image))
	end

	return self.root_node, self.root_canvas, self.gameobj_root_transform, self.node_list
end

function BaseViewRender:AddRenderGameObjs(index, add_gameobj_list)
	local gameobj_list = self.render_gameobj_t[index]
	if nil == gameobj_list then
		gameobj_list = {}
		self.render_gameobj_t[index] = gameobj_list
	end

	local len = #gameobj_list
	for _, v in ipairs(add_gameobj_list) do
		local is_exists = false
		for i=1, len do
			if v == gameobj_list[i] then
				is_exists = true
			end
		end

		if not is_exists then
			table.insert(gameobj_list, v)
			local name_table = v:GetComponent(TypeUINameTable)
			self.node_list[v.name] = U3DNodeList(name_table, self)
		end
	end
end

function BaseViewRender:RefreshGameObjActive(show_index)
	local active_gameobjs = {}
	for k, list in pairs(self.render_gameobj_t) do
		for _, gameobj in ipairs(list) do
			if k == 0 or show_index == k then
				active_gameobjs[gameobj] = true
				gameobj:SetActive(true)
			elseif nil == active_gameobjs[gameobj] then --同一个index下可能有不同的gameobj
				gameobj:SetActive(false)
			end
		end
	end
end

function BaseViewRender:RefreshTabbar(tabbar, show_index, is_no_callback)
	if nil ~= tabbar and show_index > 0 then
		tabbar:ChangeToIndex(show_index, is_no_callback)
	end
end

function BaseViewRender:RefreshUiRoleModelActive()
	-- body
end

function BaseViewRender:SetRootNodeActiveInCulling(value)
	if not value then
		self.root_node_orginal_pos = self.root_node_orginal_pos or self.gameobj_root_transform.localPosition
		self.gameobj_root_transform.localPosition = Vector3(-100000, -100000, 0)
		if nil == self.culling_timequest then
			self.culling_timequest = GlobalTimerQuest:AddRunQuest(function ()
				if not IsNil(self.gameobj_root_transform) then
					local pos = self.gameobj_root_transform.localPosition
					if pos.x ~= -100000 and pos.y ~= -100000 then
						self.gameobj_root_transform.localPosition = Vector3(-100000, -100000, 0)
					end
				end
			end, 0)
		end
	else
		self:ResumeRooNodePosition()
	end

	if not IsNil(self.clear_z_obj) then
		self.clear_z_obj.enabled = value
	end

	if nil ~= self.mask_bg_node then
		self.mask_bg_node:SetActive(value)
	end
end

function BaseViewRender:ResumeRooNodePosition()
	if nil ~= self.culling_timequest then
		GlobalTimerQuest:CancelQuest(self.culling_timequest)
		self.culling_timequest = nil
	end

	if nil ~= self.root_node_orginal_pos and nil ~= self.root_node and nil ~= self.gameobj_root_transform then
		self.gameobj_root_transform.localPosition = self.root_node_orginal_pos or Vector3(0, 0, 0)
		self.root_node_orginal_pos = nil
	end
end

function BaseViewRender:SetRootNodeActive(value)
	self.is_rendering = value

	if nil ~= self.root_node then
		if value then
			self:ResumeRooNodePosition()
			self:TrySafeAreaAdapter()

			if not IsNil(self.clear_z_obj) then
				self.clear_z_obj.enabled = value
			end
		end
		self.root_node:SetActive(value)
	end

	if nil ~= self.mask_bg_node then
		self.mask_bg_node:SetActive(value)
	end
end

function BaseViewRender:SetRendering(value)
	if self.is_rendering ~= value then
		self.is_rendering = value
		self:SetRootNodeActiveInCulling(value)
	end
end

function BaseViewRender:IsRendering()
	return self.is_rendering
end

function BaseViewRender:SetViewInStance(view)
	self.view_instance = view
end

function BaseViewRender:SetMaskBg(is_maskbg_button_click, is_maskbg_click, on_mask_bg_click_callback, on_mask_bg_click_close)
	self.is_need_mask_bg = true
	if nil ~= is_maskbg_button_click then
		self.is_maskbg_button_click = is_maskbg_button_click
	end

	if nil ~= is_maskbg_click then
		self.is_maskbg_click = is_maskbg_click
	end

	self.on_mask_bg_click_callback = on_mask_bg_click_callback
	self.on_mask_bg_click_close = on_mask_bg_click_close
end

function BaseViewRender:SetMaskBgAlpha(value)
	if self.mask_bg_node then
		local mask_bg_image = self.mask_bg_node:GetComponent(TypeImage)
		mask_bg_image.color = Color.New(0, 0, 0, value)
	end
end

function BaseViewRender:TryShowMaskBg(value)
	if not self.is_need_mask_bg then
		return
	end

	if nil == self.mask_bg_node then
		self.mask_bg_node = self.root_node.transform:Find("MaskBg").gameObject
		local image =  self.mask_bg_node:GetComponent(TypeImage)
		image.raycastTarget = self.is_maskbg_click
		self.mask_bg = U3DObject(self.mask_bg_node.gameObject, nil, self)

		if self.is_maskbg_click and self.is_maskbg_button_click then
			self:ClearLoadErrorTimer()
			local button = self.mask_bg_node:GetOrAddComponent(TypeButton)
			button.transition = UnityEngine.UI.Selectable.Transition.None
			button:AddClickListener(function ()
				if self.is_maskbg_click and self.is_maskbg_button_click then -- 可以中途设置，使其不能点击关闭
					AudioManager.PlayAndForget(ResPath.UiseRes("SFXToggle"))
					if self.on_mask_bg_click_callback ~= nil then
						self.on_mask_bg_click_callback()
					else
						self.on_mask_bg_click_close()
					end
				end
			end)
		end
	end

	if nil ~= self.mask_bg_node then
		self.mask_bg_node:SetActive(true)
		self:SetMaskBgAlpha(value)
	end
end

function BaseViewRender:ClearLoadingErrorObj()
	self.eff_view_loading = nil
	self.fix_colse_btn = nil
end

function BaseViewRender:TryAddLoadErrorTimer()
	self:ClearLoadErrorTimer()
	self:TryHideFixCloseBtn()
	-- 3秒后出现loading
	if not self.is_maskbg_click or not self.is_maskbg_button_click then
		self.load_error_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:TryShowLoadAction()
		end, 3)		
	end
end

function BaseViewRender:TryShowLoadAction()
	self:ClearLoadErrorTimer()
	-- 开始播loading动画
	if self.eff_view_loading == nil and self.gameobj_root_transform ~= nil and not IsNil(self.gameobj_root_transform) then
		local async_loader = AllocAsyncLoader(self, "eff_view_loading")
		async_loader:SetParent(self.gameobj_root_transform)
		async_loader:SetIsUseObjPool(true)
		async_loader:Load("uis/view/miscpre_load_prefab", "ViewLoading",
		function (obj)
			if obj == nil then
				return
			end

			if not self.is_rendering then
				if async_loader ~= nil then
					async_loader:Destroy()
				else
					ResPoolMgr:Release(obj)
				end

				return
			end

			if self.view_instance ~= nil and self.view_instance:IsLoaded() then
				if async_loader ~= nil then
					async_loader:Destroy()
				else
					ResPoolMgr:Release(obj)
				end

				return
			end

			self.eff_view_loading = obj
			self.load_error_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self:SetMaskClickForLoadError()
			end, 5)
		end)
	else
		if self.eff_view_loading ~= nil and not IsNil(self.eff_view_loading) then
			self.eff_view_loading:SetActive(true)

			self.load_error_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self:SetMaskClickForLoadError()
			end, 5)
		end
	end	
end

function BaseViewRender:TryHideLoadAction()
	if self.eff_view_loading ~= nil and not IsNil(self.eff_view_loading) then
		self.eff_view_loading:SetActive(false)
	end
end

function BaseViewRender:TryHideFixCloseBtn()
	if self.fix_colse_btn ~= nil and not IsNil(self.fix_colse_btn) then
		self.fix_colse_btn:SetActive(false)
	end
end

function BaseViewRender:ClearLoadErrorTimer()
	if nil ~= self.load_error_timer then
		GlobalTimerQuest:CancelQuest(self.load_error_timer)
		self.load_error_timer = nil
	end
end

function BaseViewRender:SetMaskClickForLoadError()
	self:ClearLoadErrorTimer()

	-- 关闭按钮，让玩家可以关掉
	if self.fix_colse_btn == nil and self.gameobj_root_transform ~= nil and not IsNil(self.gameobj_root_transform) then
		local async_loader = AllocAsyncLoader(self, "fix_colse_btn")
		async_loader:SetParent(self.gameobj_root_transform)
		async_loader:SetIsUseObjPool(true)
		async_loader:Load("uis/view/miscpre_load_prefab", "btn_view_close",
		function (obj)
			if obj == nil then
				return
			end

			if not self.is_rendering then
				if async_loader ~= nil then
					async_loader:Destroy()
				else
					ResPoolMgr:Release(obj)
				end

				return
			end

			if self.view_instance ~= nil and self.view_instance:IsLoaded() then
				if async_loader ~= nil then
					async_loader:Destroy()
				else
					ResPoolMgr:Release(obj)
				end

				return
			end

			self.fix_colse_btn = obj
			local button = self.fix_colse_btn:GetComponent(TypeButton)
			button.transition = UnityEngine.UI.Selectable.Transition.None
			button:AddClickListener(function ()
				if self.view_instance ~= nil then
					self.view_instance:Close()
				end
			end)
		end)
	else
		if self.fix_colse_btn ~= nil and not IsNil(self.fix_colse_btn) then
			self.fix_colse_btn:SetActive(true)
		end
	end
end

function BaseViewRender:TryHideMaskBg()
	if nil ~= self.mask_bg_node then
		self.mask_bg_node:SetActive(false)
	end
end

function BaseViewRender:GetMaskBg()
	return self.mask_bg
end

function BaseViewRender:TryHandleDefaultCloseBtn(node_list, click_callback)
	if nil ~= node_list.btn_close_window and not node_list.btn_close_window.is_listener then
		node_list.btn_close_window.button:AddClickListener(click_callback)
		node_list.btn_close_window.is_listener = true
	end
end

function BaseViewRender:TryHandleDefaultRuleTipsBtn(node_list, click_callback)
	if nil ~= node_list.btn_rule_tips and not node_list.btn_rule_tips.is_listener then
		node_list.btn_rule_tips.button:AddClickListener(click_callback)
		node_list.btn_rule_tips.is_listener = true
	end
end

function BaseViewRender:TryRefreshDefaultRuleTipsBtn(node_list, view_name, index)
	if nil == node_list.btn_rule_tips then
		return
	end

	local view_rule = ViewRuleWGData.Instance:GetViewRuleCfg(view_name, index)
	node_list.btn_rule_tips:CustomSetActive(view_rule ~= nil)
end

function BaseViewRender:AddUiRoleModel(role_model, tab_index)
	self.ui_role_model_t[role_model] = {role_model = role_model, show_index = tab_index or 0}
end

function BaseViewRender:SetUiRoleModelStageIsActive(is_active)
	for k,v in pairs(self.ui_role_model_t) do
		k:SetModleStageIsActive(is_active)
		-- v:ViewOpenCallBack()
	end
end

function BaseViewRender:CtrlRoleModelActiveByIndex(show_index, force_state)
	for k,v in pairs(self.ui_role_model_t) do
		if v.show_index then
			if type(v.show_index) == "number" then
				if force_state ~= nil then
					k:SetModleStageIsActive(show_index == v.show_index and force_state)
				else
					k:SetModleStageIsActive(show_index == v.show_index or v.show_index == 0)
				end
			elseif type(v.show_index) == "table" then
				local is_show = false
				for _, var_index in pairs(v.show_index) do
					if show_index == var_index then
						if force_state ~= nil then
							is_show = force_state
						else
							is_show = true
						end
						
						break
					end
				end

				k:SetModleStageIsActive(is_show)
			end
		end
	end
end

-- 检测是否存在包含展示页签需要隐藏的模型，一般为场景模型
function BaseViewRender:CheckRoleModelActiveInIndex()
	for k,v in pairs(self.ui_role_model_t) do
		if v.show_index then
			return true
		end
	end

	return false
end

function BaseViewRender:TrySafeAreaAdapter()
	if self.is_safe_area_adapter and not self.safe_adapter and nil ~= self.gameobj_root_transform then
		self.safe_adapter = SafeAreaAdpater.Bind(self.gameobj_root_transform.gameObject)
	end
end

-- 蛋疼的旧代码
function BaseViewRender:SetSecondView(vec2, size_node, anchored_pos)
	if self.node_list["layout_commmon_second_root"] == nil then
		return
	end

	if anchored_pos then
		self.node_list["layout_commmon_second_root"].rect.anchoredPosition = anchored_pos
	end

	if vec2 then
		self.node_list["layout_commmon_second_root"].rect.sizeDelta = vec2
		return
	end

	if size_node then
		local size_rect = size_node.rect
		self.node_list["layout_commmon_second_root"].rect.sizeDelta = size_rect.sizeDelta
		self.node_list["layout_commmon_second_root"].rect.anchoredPosition = size_rect.anchoredPosition
	end
end

-- 蛋疼的旧代码
function BaseViewRender:SetTextViewNameShow(view_name, str)
	if not self.node_list.title_view_name then
		return
	end

	if str then
		self.node_list.title_view_name.text.text = str
		return
	end

	if view_name and Language.ViewName [view_name] then
		self.node_list.title_view_name.text.text = Language.ViewName[view_name]
	end
end

-- 蛋疼的旧代码
function BaseViewRender:GetGuideUiCallBack(ui_name, close_callback, is_open)
	if ui_name == GuideUIName.CloseBtn and is_open then
		return self.node_list.btn_close_window, close_callback
	end
	return self.node_list[ui_name], nil
end

function BaseViewRender:LoadSprite(bundle_name, asset_name, callback, cbdata)
	LoadSprite(self, bundle_name, asset_name, callback, cbdata)
end

function BaseViewRender:LoadSpriteAsync(bundle_name, asset_name, callback, cbdata)
	LoadSpriteAsync(self, bundle_name, asset_name, callback, cbdata)
end

function BaseViewRender:LoadRawImage(arg0, arg1, arg2)
	LoadRawImage(self, arg0, arg1, arg2)
end
