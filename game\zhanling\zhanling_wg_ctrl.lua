require("game/zhanling/zhanling_wg_data")
require("game/zhanling/zhanling_items")
require("game/zhanling/zhanling_view")
require("game/zhanling/zhanling_act_view")
require("game/zhanling/zhanling_desc_tips")
require("game/zhanling/zhanling_buy_level_view")
require("game/zhanling/zhanling_main_reward_view")
require("game/zhanling/zhanling_task_view")
require("game/zhanling/zhanling_shop_view")
require("game/zhanling/zhanling_result_view")

-- 【战令】
ZhanLingWGCtrl = ZhanLingWGCtrl or BaseClass(BaseWGCtrl)

function ZhanLingWGCtrl:__init()
	if nil ~= ZhanLingWGCtrl.Instance then
		ErrorLog("[ZhanLingWGCtrl] attempt to create singleton twice!")
		return
	end
	ZhanLingWGCtrl.Instance = self

    self.data = ZhanLingWGData.New()
	self.view = ZhanLingView.New(GuideModuleName.ZhanLingView)
	self.desc_tips = ZhanLingDescTips.New()
	self.act_view = ZhanLingActView.New()
	self.buy_level_view = ZhanLingBuyLevelView.New()
	self.result_view = ZhanLingResultView.New()
    self:RegisterAllProtocols()

	self.day_pass_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPassEvent, self))
end

function ZhanLingWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.act_view then
		self.act_view:DeleteMe()
		self.act_view = nil
	end

	if nil ~= self.desc_tips then
		self.desc_tips:DeleteMe()
		self.desc_tips = nil
	end

	if nil ~= self.buy_level_view then
		self.buy_level_view:DeleteMe()
		self.buy_level_view = nil
	end

	if nil ~= self.day_pass_event then
		GlobalEventSystem:UnBind(self.day_pass_event)
		self.day_pass_event = nil
	end

	self:ClearBuyLevelAlert()
	self:ClearFlushTaskAlert()
    ZhanLingWGCtrl.Instance = nil
end

-- 注册协议
function ZhanLingWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSZhanLingOpera)
	self:RegisterProtocol(SCZhanLingInfo, "OnSCZhanLingInfo")
	self:RegisterProtocol(SCZhanLingTaskInfo, "OnSCZhanLingTaskInfo")
	self:RegisterProtocol(SCZhanLingLimitExchangeItemInfo, "OnSCZhanLingLimitExchangeItemInfo")
	self:RegisterProtocol(SCZhanLingLevelBuyInfo, "OnSCZhanLingLevelBuyInfo")
end

function ZhanLingWGCtrl:OnDayPassEvent()
	-- print_error("---天数改变----", TimeWGCtrl.Instance:GetCurOpenServerDay())
	if self.view:IsOpen() then
		self.view:FlushTaskToggleTime()
	end
end

function ZhanLingWGCtrl:OnSCZhanLingInfo(protocol)
	--print_error("---战令信息----")
	local old_reward_list = self.data:GetRewardShowRemindInfo()
	local overflow_nor_num, overflow_high_num = self.data:GetAfterMaxLevelRewardNum()

	self.data:SetZhanLingInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.zl_main_reward)
		self.view:Flush(TabIndex.zl_task_panel)
	end

	if self.act_view:IsOpen() then
		self.act_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.ZhanLing_Reward)

	self:CheckNeedPopResultView(old_reward_list, overflow_nor_num, overflow_high_num)
end

function ZhanLingWGCtrl:OnSCZhanLingTaskInfo(protocol)
	--print_error("---任务信息----")
	self.data:SetZhanLingTaskInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.zl_task_panel)
	end

	RemindManager.Instance:Fire(RemindName.ZhanLing_Task)
end

function ZhanLingWGCtrl:OnSCZhanLingLimitExchangeItemInfo(protocol)
	-- ("---限购物品信息----")
	if self.coin_num_add_remind then
		local coin_num = self.data:GetExchangeItemNum()
		local other_cfg = self.data:GetZhanLingOtherCfg()
		local exchange_item_id = other_cfg and other_cfg.reward_item_id or 0
		if protocol.zhanling_coin_num > coin_num then
			local item_name = ItemWGData.Instance:GetItemNameDarkColor(exchange_item_id)
			local str = string.format(Language.SysRemind.AddItem, item_name, protocol.zhanling_coin_num - coin_num)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end
	self.coin_num_add_remind = true

	self.data:SetLimitExchangeItemInfo(protocol)
	RemindManager.Instance:Fire(RemindName.ZhanLing_ExChange)

	if self.view:IsOpen() then
		self.view:Flush(TabIndex.zl_shop_panel)
	end
end

function ZhanLingWGCtrl:OnSCZhanLingLevelBuyInfo(protocol)
	-- print_error("---等级购买信息----")
	self.data:SetZhanLingLevelBuyInfo(protocol)
	self:FlushBuyLevelView()
end

function ZhanLingWGCtrl:SendZhanLingRequest(opera_type, param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSZhanLingOpera)
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol.param_2 = param2 or 0
 	protocol.param_3 = param3 or 0
 	protocol:EncodeAndSend()
end

function ZhanLingWGCtrl:ZhanLingTaskFinishReturn(result)
	if result == 0 then
		if self.view:IsOpen() then
			ViewManager.Instance:FlushView(GuideModuleName.ZhanLingView, nil, "flush_anim")
		end
	end
end

function ZhanLingWGCtrl:OpenActZhanlingView()
	self.act_view:Open()
end

function ZhanLingWGCtrl:OpenBuyLevelView()
	self.buy_level_view:Open()
end

function ZhanLingWGCtrl:FlushBuyLevelView()
	self.buy_level_view:Flush()
end

function ZhanLingWGCtrl:SetDescTipsData(data)
	self.desc_tips:SetData(data)
end

function ZhanLingWGCtrl:SetBuyLevelAlertData(level_id, cost_value)
	cost_value = cost_value or 0
	if nil == self.buy_level_alert then
		self.buy_level_alert = Alert.New(nil, nil, nil, nil, true)
		self.buy_level_alert:SetCheckBoxDefaultSelect(false)
	end

	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	self.buy_level_alert:SetLableString(string.format(Language.ZhanLing.BuyZhanLingLevelTips, cost_value))
	self.buy_level_alert:SetOkFunc(function()
		if role_gold < cost_value then
			VipWGCtrl.Instance:OpenTipNoGold()
		else
			self:SendZhanLingRequest(ZhanLingOperType.BuyExp, level_id)
		end
	end)

	self.buy_level_alert:Open()
end

function ZhanLingWGCtrl:ClearBuyLevelAlert()
	if self.buy_level_alert then
		self.buy_level_alert:DeleteMe()
		self.buy_level_alert = nil
	end
end

function ZhanLingWGCtrl:SetFlushTaskAlertData(task_id, task_class, cell_index)
	local other_cfg = ZhanLingWGData.Instance:GetZhanLingOtherCfg()
	local cost_value = other_cfg and other_cfg.flash_cost or 0

	if nil == self.flush_task_alert then
		self.flush_task_alert = Alert.New(nil, nil, nil, nil, true)
		self.flush_task_alert:SetCheckBoxDefaultSelect(false)
	end

	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	local bind_gold = GameVoManager.Instance:GetMainRoleVo().bind_gold
	local cost_text = Language.Common.GoldText
	if bind_gold >= cost_value then
		cost_text = Language.Common.BindGoldText
	end
	self.flush_task_alert:SetLableString(string.format(Language.ZhanLing.FlushTaskTips, cost_text, cost_value))
	self.flush_task_alert:SetOkFunc(function()
		if (role_gold + bind_gold)  < cost_value then
			VipWGCtrl.Instance:OpenTipNoGold()
		else
			ZhanLingWGData.Instance:SetRefreshTaskID(cell_index)
			self:SendZhanLingRequest(ZhanLingOperType.FlushTask, task_id, task_class)
		end
	end)

	self.flush_task_alert:Open()
end

function ZhanLingWGCtrl:ClearFlushTaskAlert()
	if self.flush_task_alert then
		self.flush_task_alert:DeleteMe()
		self.flush_task_alert = nil
	end
end

function ZhanLingWGCtrl:CheckNeedPopResultView(old_reward_list, old_overflow_nor_num, old_overflow_high_num)
	local new_reward_list = self.data:GetRewardShowRemindInfo()
	if IsEmptyTable(old_reward_list) or IsEmptyTable(new_reward_list) then
		return
	end

	local reward_list = {}
	local nor_list = {}
	local hig_list = {}

	for _,v in pairs(old_reward_list) do
		if v.nor_reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			nor_list[v.level] = true
		end
		if v.high_reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			hig_list[v.level] = true
		end
	end

	if not IsEmptyTable(nor_list) or not IsEmptyTable(hig_list) then
		for _,v in pairs(new_reward_list) do
			if nor_list[v.level] and v.nor_reward_state == REWARD_STATE_TYPE.FINISH then
				reward_list[#reward_list + 1] = v.nor_reward_list
			end
			if hig_list[v.level] and v.high_reward_state == REWARD_STATE_TYPE.FINISH then
				reward_list[#reward_list + 1] = v.high_reward_list
			end
		end
	end

	---[[ 90级后的奖励
	local zhanling_info = self.data:GetZhanLingInfo()
	local cur_level = zhanling_info and zhanling_info.level or 0
	local cur_max_level = ZhanLingWGData.Instance:GetCurRewardMaxLevel()

	if cur_level >= cur_max_level then
		local reward = ZhanLingWGData.Instance:GetSpecialRewardShowRemindInfo()
		local overflow_nor_num, overflow_high_num = ZhanLingWGData.Instance:GetAfterMaxLevelRewardNum()

		local nor_sub_num = old_overflow_nor_num - overflow_nor_num
		if nor_sub_num > 0 then
			for i=1,nor_sub_num do
				reward_list[#reward_list + 1] = reward.nor_reward_list
			end
		end

		local high_sub_num = old_overflow_high_num - overflow_high_num
		if high_sub_num > 0 then
			for i=1,high_sub_num do
				reward_list[#reward_list + 1] = reward.high_reward_list
			end
		end
	end
	--]]

	if IsEmptyTable(reward_list) then
		return
	end

	local show_list = {}
	local is_active = self.data:GetIsActHighZhanLing()
	if not is_active then
		for i,v in ipairs(new_reward_list) do
			if cur_level >= v.level then
				show_list[#show_list + 1] = v.high_reward_list
			else
				break
			end
		end
	end

	self.result_view:Open()
	self.result_view:Flush(0, "reward_info", {reward_list = reward_list, show_list = show_list})
end
