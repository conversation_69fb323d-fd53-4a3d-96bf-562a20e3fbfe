TianShenHuamoTipsView = TianShenHuamoTipsView or BaseClass(SafeBaseView)

function TianShenHuamoTipsView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 386)})
	self:AddViewResource(0, "uis/view/tianshen_huamo_prefab", "layout_tianshen_hua_mo_tips")
	self:SetMaskBg(true)
end

function TianShenHuamoTipsView:__delete()

end

function TianShenHuamoTipsView:LoadCallBack()
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClinkOkHandler, self))
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.OnClinkClose, self))

	self.stuff_cell = ItemCell.New(self.node_list.ph_stuff_cell)
end

function TianShenHuamoTipsView:ReleaseCallBack()
    self.has_enough_stuff = false

	if self.stuff_cell then
		self.stuff_cell:DeleteMe()
		self.stuff_cell = nil
	end
end


function TianShenHuamoTipsView:SetData(data)
	self.data = data
	self:Open()
end

function TianShenHuamoTipsView:OnFlush()
    if not self.data then
        return
    end
    
    local item_id = self.data.reset_item_id
    local need_num = self.data.reset_item_num
	local have_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    local stuff_name = ItemWGData.Instance:GetItemName(item_id)
	local str = string.format(Language.TianShenHuaMo.ResetSkillTips, need_num, stuff_name)
	self.has_enough_stuff = have_num >= need_num
    local color = self.has_enough_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    self.node_list["rich_des_1"].text.text = str
    local item_str = string.format("%s/%s",have_num, need_num)
    self.stuff_cell:SetData({item_id = item_id, num = have_num})

    if need_num then
        self.stuff_cell:SetRightBottomColorText(item_str, color)
        self.stuff_cell:SetRightBottomTextVisible(true)
    end
    self.node_list["rich_des_2"].text.text = ""
end

function TianShenHuamoTipsView:OnClinkOkHandler()
    if self.has_enough_stuff then
        TianShenHuamoWGCtrl:CSTianShenRuMoSkillReset(self.data.index, self.data.demon_level)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.reset_item_id, is_bind = 1})
        self:Close()
        return
    end
	self:Close()
end

function TianShenHuamoTipsView:OnClinkClose()
	self:Close()
end
