
--天神宝匣--宝匣

local BX_Box_Select_Type = {
	Part = 1,
	Color = 2,
}

local BX_BOX_BG_EFFECT =
{
	[1] = "UI_SGLH_baoshiBG_lv",
	[2] = "UI_SGLH_baoshiBG_lan",
	[3] = "UI_SGLH_baoshiBG_zi",
	[4] = "UI_SGLH_baoshiBG_cheng",
	[5] = "UI_SGLH_baoshiBG_hong",
	[6] = "UI_SGLH_baoshiBG_fen",
	[7] = "UI_SGLH_baoshiBG_jin",
}

local BX_BOX_BAOSHI_EFFECT =
{
	[1] = "UI_SGLH_baoshi_lv",
	[2] = "UI_SGLH_baoshi_lan",
	[3] = "UI_SGLH_baoshi_zi",
	[4] = "UI_SGLH_baoshi_cheng",
	[5] = "UI_SGLH_baoshi_hong",
	[6] = "UI_SGLH_baoshi_fen",
	[7] = "UI_SGLH_baoshi_jin",
}

local BX_BOX_COLOR_EFFECT =
{
	[1] = "UI_sglh_icon_lv",
	[2] = "UI_sglh_icon_lan",
	[3] = "UI_sglh_icon_zi",
	[4] = "UI_sglh_icon_cheng",
	[5] = "UI_sglh_icon_hong",
}

local BX_BOX_BAOSHI_UP_EFFECT = "UI_SGLH_baoshi_up"

local tween_lhbg_rotatepos = Vector3(0, 0, 360)
local tween_lhbg2_rotatepos = Vector3(0, 0, -360)

local Box_View_Scale_Time = 1 --视野拉伸时间
local Box_Model_Shake_Time = 1 --模型抖动时间
local Box_Effect_Time = Box_View_Scale_Time + Box_Model_Shake_Time --模型上的特效播放延迟时间
local Box_Model_Anim_End_Time = Box_View_Scale_Time + Box_Model_Shake_Time + 0.5 --所有动画结束时间

local SHOW_REWARD_NUM = 8  --展示奖励数量
local ANIM_TIME = 3  --动画时间


function TianShenBaoXiaView:TSBoxReleaseCallBack()

	if self.show_model then
		self.show_model:DeleteMe()
		self.show_model = nil
	end

	if self.shenshi_item_list then
		self.shenshi_item_list:DeleteMe()
		self.shenshi_item_list = nil
	end

	for k, v in pairs(self.select_part_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	for k, v in pairs(self.select_color_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	self.is_tsbox_anim_playing = nil

	if self.reward_cells then
		for k, v in pairs(self.reward_cells) do
			v:DeleteMe()
		end
	end
	self.reward_cells = {}

	self.is_show_bag = false
end

function TianShenBaoXiaView:TSBoxCloseCallBack()
	self.is_tsbox_anim_playing = false
	self:ClearTimer()
end

function TianShenBaoXiaView:ClearTimer()
	if self.anim_timer then
		GlobalTimerQuest.CancelQuest(self.anim_timer)
		self.anim_timer = nil
	end
end

function TianShenBaoXiaView:TSBoxLoadCallBack()
	self.shenshi_item_list = AsyncListView.New(TianShenBoxBagItemListRender, self.node_list["bx_bag_list"])

	self.select_part_list = {}
	self.select_color_list = {}
	self.cur_select_color_index = -1
	self.cur_select_part_index = -1
	self.is_tsbox_anim_playing = false
	self.cache_model_res_name = ""
	self.cache_slider_model_res_name = ""
	self.is_show_bag = false

	--抽奖按钮
	self.node_list["btn_open_1"].button:AddClickListener(BindTool.Bind(self.TSBoxOnClickSendToGetAward, self, BAOXIA_SEND_GET_AWARD_TYPE.One))
	self.node_list["btn_open_2"].button:AddClickListener(BindTool.Bind(self.TSBoxOnClickSendToGetAward, self, BAOXIA_SEND_GET_AWARD_TYPE.N_Max))
	self.node_list["btn_open_3"].button:AddClickListener(BindTool.Bind(self.TSBoxOnClickSendToUpLevel, self))

	self.node_list["bx_btn_tips"].button:AddClickListener(function()
		RuleTip.Instance:SetContent(Language.TianShenBaoXia.BaoXia_Box_Tips, Language.TianShenBaoXia.BaoXia_Box_Tips_Title)
	end)

	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local mat_item_id = other_cfg.consume_item or 39144
	for i = 1, 2 do
		self.node_list["layout_money_" .. i].button:AddClickListener(function()
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = mat_item_id})
		end)
	end

	XUI.AddClickEventListener(self.node_list["btn_open_shenshi"], BindTool.Bind1(self.OnClickGoToShenShiBtn, self))
	XUI.AddClickEventListener(self.node_list["bx_skip_anim_btn"], BindTool.Bind(self.OnClickTSBoxSkipAnimBtn, self))
	XUI.AddClickEventListener(self.node_list["baoxia_btn_range"], BindTool.Bind(self.OnClickGaiLvShow, self))
	self.node_list["bx_skip_gou_img"]:SetActive(TianShenWGData.Instance:GetTSBoxIsSkipAnim())

	self.reward_cells = {}
	for i = 1, SHOW_REWARD_NUM do
		self.reward_cells[i] = BaoXiaRewardCell.New(self.node_list["reward_pos" .. i])
		self.reward_cells[i]:SetIndex(i)
	end

	if not self.show_model then
		self.show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["box_display_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.show_model:SetRenderTexUI3DModel(display_data)
	end
end

function TianShenBaoXiaView:TSBoxShowIndexCallBack()
	self:FlushModel()
	self.show_model:PlayRoleShowAction()

	self.old_model_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
end

function TianShenBaoXiaView:FlushModel()
	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	local show_reward_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade)
	self.show_model:SetMainAsset(show_reward_cfg.model_bundle_name, show_reward_cfg.model_asset_name)

	local scale = show_reward_cfg.model_scale
	if scale and scale ~= "" then
		self.show_model:SetRTAdjustmentRootLocalScale(scale)
	end

	local pos_x , pos_y, pos_z = 0, 0, 0
	if show_reward_cfg.model_pos and show_reward_cfg.model_pos ~= "" then
		local pos_list = string.split(show_reward_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
		self.show_model:SetRTAdjustmentRootLocalPosition(pos_x, pos_y, pos_z)
	end

	local rot_x , rot_y, rot_z = 0, 0, 0
	if show_reward_cfg.model_rot and show_reward_cfg.model_rot ~= "" then
		local pos_list = string.split(show_reward_cfg.model_rot, "|")
		rot_x = tonumber(pos_list[1]) or rot_x
		rot_y = tonumber(pos_list[2]) or rot_y
		rot_z = tonumber(pos_list[3]) or rot_z
		self.show_model:SetRTAdjustmentRootLocalRotation(rot_x, rot_y, rot_z)
	end

	
end

function TianShenBaoXiaView:TSBoxOnFlush(param_t)
	if self.show_model then
		self.show_model:PlayRoleAction(SceneObjAnimator.Idle)
	end
	for i = 1, SHOW_REWARD_NUM do
		self.reward_cells[i]:PlayFloatTween()
	end
	self.node_list.box_anim_hide_root:CustomSetActive(true)
	--设置名字
	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local show_reward_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade)

	if self.old_model_cfg and self.old_model_cfg.grade ~= cur_cfg.grade then
		self:FlushModel()
	end

	local quality_text = ToColorStr(Language.TianShenBaoXia.BoxQualityText[cur_cfg.grade], ITEM_COLOR[cur_cfg.grade])
	self.node_list["lbl_box_quality_des"].text.text = string.format(Language.TianShenBaoXia.BoxQualityDes, quality_text)
	
	--[[
	local bundle, asset = ResPath.GetRawImagesJPG("a3_sglh_bj_" .. cur_cfg.grade)
	self.node_list["raw_image"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["raw_image"].raw_image:SetNativeSize()
    end)
	bundle, asset = ResPath.GetRawImagesPNG("a3_sglh_sb_" .. cur_cfg.grade)
	self.node_list["img_quality_stone"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["img_quality_stone"].raw_image:SetNativeSize()
    end)
	bundle, asset = ResPath.GetRawImagesPNG("a3_sglh_h_" .. cur_cfg.grade)
	self.node_list["img_quality_light"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["img_quality_light"].raw_image:SetNativeSize()
    end)

	local bg_ef_bundle, bg_ef_asset = ResPath.GetA2Effect(BX_BOX_BG_EFFECT[cur_cfg.grade])
    self.node_list.bg_effect:ChangeAsset(bg_ef_bundle, bg_ef_asset)

	local bs_ef_bundle, bs_ef_asset = ResPath.GetA2Effect(BX_BOX_BAOSHI_EFFECT[cur_cfg.grade])
    self.node_list.stone_effect:ChangeAsset(bs_ef_bundle, bs_ef_asset)
	]]

	--设置进度条
	local next_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade + 1)
	local is_max_level = not next_cfg
	local cur_time = TianShenWGData.Instance:GetGodHoodDrawTimes()
	local is_can_uplevel = TianShenWGData.Instance:GetGodHoodDrawIsCanUpLevel()
	local s_value_1 = cur_time > 0 and cur_time - (cur_cfg.min_draw_count - 1) or 0
	local s_value_2 = (cur_cfg.max_draw_count- 1) - (cur_cfg.min_draw_count == 0 and 0 or cur_cfg.min_draw_count -1)
	self.node_list["bx_slider"].slider.value = is_max_level and 1 or s_value_1 / s_value_2
	self.node_list["bx_text_slider"].text.text = is_max_level and "MAX" or string.format("%s/%s", s_value_1, s_value_2)

	-- 设置材料
	local my_mat_count = TianShenWGData.Instance:GetGodHoodDrawLeftMat()
	local is_one_mat_enough = my_mat_count >= other_cfg.draw_cost
	local show_str_1 = my_mat_count .. "/" .. (other_cfg.draw_cost * BAOXIA_SEND_GET_AWARD_TYPE.One)
	show_str_1 = ToColorStr(show_str_1, is_one_mat_enough and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED)
	self.node_list["text_cost_money_1"].text.text = show_str_1
	self.node_list["btn_red_point_1"]:SetActive(is_one_mat_enough)

	--设置开启N次按钮显示:次数满足一次性开启2个才显示多开按钮
	local is_n_mat_enough = my_mat_count >= (BAOXIA_SEND_GET_AWARD_TYPE.Two * other_cfg.draw_cost)
	self.node_list["btn_open_2"]:SetActive(is_n_mat_enough and not is_can_uplevel)
	self.node_list["btn_red_point_2"]:SetActive(is_one_mat_enough)
	self.node_list["btn_open_1"]:SetActive(not is_can_uplevel)
	self.node_list["btn_open_3"]:SetActive(is_can_uplevel)

	if is_n_mat_enough then
		local can_open_num = math.floor(my_mat_count / other_cfg.draw_cost)
		can_open_num = can_open_num > BAOXIA_SEND_GET_AWARD_TYPE.N_Max and BAOXIA_SEND_GET_AWARD_TYPE.N_Max or can_open_num
		if not is_max_level then
			--策划需求:宝匣可抽取次数若超过升级次数时，只会显示升级前的次数
			local need_up_level_time = cur_cfg.max_draw_count - cur_time - 1
			can_open_num = can_open_num > need_up_level_time and need_up_level_time or can_open_num
			self.node_list["btn_open_2"]:SetActive(can_open_num > 1 and not is_can_uplevel)--当两个按钮都是开1个的时候，只显示开1个的按钮
		end

		local show_str_2 = my_mat_count .. "/" .. (other_cfg.draw_cost * can_open_num)
		--
		show_str_2 = ToColorStr(show_str_2, is_n_mat_enough and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED)
		self.node_list["text_cost_money_2"].text.text = show_str_2
		self.node_list["text_btn_open_2"].text.text = string.format(Language.TianShenBaoXia.BaoXia_Open_Btn, can_open_num)
	end
	local cur_reward_list = {}
	for i = 0, #show_reward_cfg.reward_item do
		local data = show_reward_cfg.reward_item[i]
		data.show_box_level = show_reward_cfg.grade
		cur_reward_list[i + 1] = data
	end

	for i = 1, SHOW_REWARD_NUM do
		self.node_list["reward_pos" .. i]:SetActive(not IsEmptyTable(cur_reward_list[i]))
		if cur_reward_list[i] then
			self.reward_cells[i]:SetData(cur_reward_list[i])
		end
	end
	self.node_list["bx_skip_gou_img"]:SetActive(TianShenWGData.Instance:GetTSBoxIsSkipAnim())
	self:TSBoxSetBagListShow()
end

function TianShenBaoXiaView:SendReward(can_open_num)
	--请求类型  s_type
	TianShenWGCtrl.Instance:OnCSGodhoodDrawOperate(GODHOOD_DRAW_OPERATE_TYPE.GODHOOD_DRAW_OPERATE_TYPE_DRAW, can_open_num)
	if can_open_num > 10 then
		TianShenWGData.Instance:DelayFlushShenShiRed(1)
	end
	self.is_tsbox_anim_playing = false
end

function TianShenBaoXiaView:Draw(can_open_num)

	if TianShenWGData.Instance:GetTSBoxIsSkipAnim() then
		self:SendReward(can_open_num)
		self.node_list.box_anim_hide_root:CustomSetActive(true)
	else
		self.node_list.box_anim_hide_root:CustomSetActive(false)
		self.is_tsbox_anim_playing = true

		local bundle, asset = ResPath.GetEffectUi("UI_sglh_choujiang")
		EffectManager.Instance:PlaySingleAtTransform(bundle, asset, self.node_list["up_effect_root"].transform, ANIM_TIME, nil, nil, Vector3(1, 1, 1))
	
		self.anim_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:SendReward(can_open_num)
		end, ANIM_TIME)
	end
end

--点击抽奖
function TianShenBaoXiaView:TSBoxOnClickSendToGetAward(s_type)
	--动画播放中 弹提示
	if self.is_tsbox_anim_playing then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenBaoXia.BaoXia_Anim_Is_Playing)
		return
	end

	local last_state = TianShenWGData.Instance:GetTSBoxIsSkipAnim()

	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	local grade_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfgByGrade(cur_cfg.grade)
	local my_mat_count = TianShenWGData.Instance:GetGodHoodDrawLeftMat()
	local is_mat_enough = false
	local can_open_num = 1

	if s_type == BAOXIA_SEND_GET_AWARD_TYPE.One then--单次抽 一次
		is_mat_enough = my_mat_count >= other_cfg.draw_cost
	else--单次抽 N次,最高10
		can_open_num = math.floor(my_mat_count / other_cfg.draw_cost)
		can_open_num = can_open_num > BAOXIA_SEND_GET_AWARD_TYPE.N_Max and BAOXIA_SEND_GET_AWARD_TYPE.N_Max or can_open_num
		local next_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade + 1)
		if next_cfg then
			--策划需求:宝匣可抽取次数若超过升级次数时，只会显示升级前的次数
			local cur_time = TianShenWGData.Instance:GetGodHoodDrawTimes()
			local need_up_level_time = grade_cfg.max_draw_count - cur_time
			can_open_num = can_open_num > need_up_level_time and need_up_level_time or can_open_num
		end
		is_mat_enough = can_open_num > BAOXIA_SEND_GET_AWARD_TYPE.One
	end

	

	--判断材料是否足够
	if is_mat_enough then
		--播放动画
		self:Draw(can_open_num)

	else
		--SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenBaoXia.Mat_Not_Enough)
		local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
		local mat_item_id = other_cfg.consume_item or 39144
		
		local tips_data = {}
		tips_data.item_id =mat_item_id
		tips_data.price = other_cfg.draw_cost_gold
		tips_data.draw_count = other_cfg.draw_cost
		tips_data.has_checkbox = true
		tips_data.checkbox_str = "tianshen_baoxia_draw"
		tips_data.has_num = my_mat_count

		-- 价格为0或者灵玉不足还是弹道具tips
		if tips_data.price <= 0 and not is_mat_enough then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = tips_data.item_id})
		else
			TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind(self.Draw, self, can_open_num), nil)
		end
	end

	LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_TS_EQUIP)
end

--升品.
function TianShenBaoXiaView:TSBoxOnClickSendToUpLevel()
	if self.is_tsbox_anim_playing then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenBaoXia.BaoXia_Anim_Is_Playing)
		return
	end
	--播放动画
	TianShenWGCtrl.Instance:OnCSGodhoodDrawOperate(GODHOOD_DRAW_OPERATE_TYPE.GODHOOD_DRAW_OPERATE_TYPE_UP_GRADE)
	local bundle, asset = ResPath.GetEffectUi("UI_sglh_shengpin")
	EffectManager.Instance:PlaySingleAtTransform(bundle, asset, self.node_list["up_effect_root"].transform, 2, nil, nil, Vector3(1, 1, 1))

	--[[
	if TianShenWGData.Instance:GetTSBoxIsSkipAnim() then
		send_reward_fun()
		self.node_list.box_anim_hide_root:CustomSetActive(true)
	else
		self.node_list.box_anim_hide_root:CustomSetActive(false)
		self.is_tsbox_anim_playing = true
		self.show_model:PlayRoleAction(SceneObjAnimator.Move)
		for i = 1, SHOW_REWARD_NUM do
			self.reward_cells[i]:PlayMoveTween(self.node_list.box_display_model.rect.position)
		end

		local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
		local show_reward_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade)
		EffectManager.Instance:PlaySingleAtTransform(show_reward_cfg.effect_bunble, show_reward_cfg.effect_asset, self.node_list["up_effect_root"].transform, ANIM_TIME, nil, nil, Vector3(1, 1, 1))
		self.anim_timer = GlobalTimerQuest:AddDelayTimer(function ()
			send_reward_fun()
			self.show_model:PlayRoleAction(SceneObjAnimator.Idle)
		end, ANIM_TIME)
	end
	]]
end

--播放节点升品特效.
function TianShenBaoXiaView:TSBoxPlayStoneUpLevelEffect()
	local bundle_name, asset_name = ResPath.GetA2Effect(BX_BOX_BAOSHI_UP_EFFECT)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["stone_up_effect"].transform, 2)
end

--背包列表刷新
function TianShenBaoXiaView:TSBoxSetBagListShow()
	if not self.shenshi_item_list then return end
	local mat_data_list = {}
	if self.cur_select_part_index >= 0 or self.cur_select_color_index > 0 then
		if self.cur_select_part_index >= 0 and self.cur_select_color_index > 0 then
			mat_data_list = TianShenWGData.Instance:GetTianShenBoxBagInfo(self.cur_select_part_index, self.cur_select_color_index)
		elseif self.cur_select_part_index >= 0 then
			mat_data_list = TianShenWGData.Instance:GetTianShenBoxBagInfo(self.cur_select_part_index, nil)
		else
			mat_data_list = TianShenWGData.Instance:GetTianShenBoxBagInfo(nil, self.cur_select_color_index)
		end
	else
		mat_data_list = TianShenWGData.Instance:GetTianShenBoxBagInfo(nil, nil)
	end

	self.shenshi_item_list:SetDataList(mat_data_list)
end

function TianShenBaoXiaView:OnClickTSBoxSkipAnimBtn()
	local last_state = TianShenWGData.Instance:GetTSBoxIsSkipAnim()
	self.node_list["bx_skip_gou_img"]:SetActive(not last_state)
	TianShenWGData.Instance:SetTSBoxSkipAnimFalg(not last_state)
end

function TianShenBaoXiaView:OnClickGoToShenShiBtn()
	ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_shenshi)
end

--抽奖概率
function TianShenBaoXiaView:OnClickGaiLvShow()
    local info = TianShenWGData.Instance:GetTianShenBaoxiaProbabilityInfo()
	-- TipWGCtrl.Instance:OpenTipsRewardProView(info)
	TipWGCtrl.Instance:OpenTipsRewardProGroupView(info)

end


--------------------------------TianShenBoxBagItemListRender  start------------------------------------------------
TianShenBoxBagItemListRender = TianShenBoxBagItemListRender or BaseClass(BaseRender)
function TianShenBoxBagItemListRender:__init()
	self.mat_item_list = {}
	for i = 1, 3 do
		self.mat_item_list[i] = {}
		self.mat_item_list[i].item = ItemCell.New(self.node_list["cell_pos" .. i])
		self.mat_item_list[i].item:SetItemTipFrom(ItemTip.FROM_BAG)
		self.mat_item_list[i].red = self.node_list["red" .. i]
		self.mat_item_list[i].item_go = self.node_list["cell_pos" .. i]
	end
end

function TianShenBoxBagItemListRender:__delete()
	if not IsEmptyTable(self.mat_item_list) then
		for i = 1, #self.mat_item_list do
			if not IsEmptyTable(self.mat_item_list[i]) then
				self.mat_item_list[i].item:DeleteMe()
				self.mat_item_list[i].item_go = nil
				self.mat_item_list[i] = nil
			end
		end
	end
end

function TianShenBoxBagItemListRender:OnFlush()
	for i = 1, #self.mat_item_list do
		self.mat_item_list[i].item_go:SetActive(false)
		self.mat_item_list[i].red:SetActive(false)
	end
	if not self.data then
		return
	end
	for i = 1, #self.data do
		if self.data[i].item_id then
			self.mat_item_list[i].item:SetData({item_id = self.data[i].item_id, index = self.data[i].bag_index, is_bind = self.data[i].is_bind})
			self.mat_item_list[i].item:SetRightBottomText(self.data[i].num or 0)
			self.mat_item_list[i].item:SetRightBottomTextVisible(true)
			self.mat_item_list[i].red:SetActive(TianShenWGData.Instance:CheckShenShiItemUpFlag(self.data[i].item_id))
			self.mat_item_list[i].item_go:SetActive(true)
		end
	end
end
--------------------------------TianShenBoxBagItemListRender  end------------------------------------------------

--------------------------------BaoXiaBagSelectRender  start------------------------------------------------
BaoXiaBagSelectRender = BaoXiaBagSelectRender or BaseClass(BaseRender)

function BaoXiaBagSelectRender:__delete()
end

function BaoXiaBagSelectRender:LoadCallBack()
	self.node_list["btn"].button:AddClickListener(BindTool.Bind1(self.OnClick, self))
end

function BaoXiaBagSelectRender:SetSelectIndex(index, select_btn_type)
	self.index = index
	self.select_btn_type = select_btn_type
end

function BaoXiaBagSelectRender:SetClickCallBack(callback)
	self.parent_call_back = callback
end

function BaoXiaBagSelectRender:SetName(name)
	self.node_list["text_select_name"].text.text = name
end

function BaoXiaBagSelectRender:SetSelect(is_select)
	self.node_list["img_select_bg"]:SetActive(is_select)
end

function BaoXiaBagSelectRender:SetRemindShow(is_show)
	self.node_list["img_select_red"]:SetActive(is_show)
end

function BaoXiaBagSelectRender:OnClick()
	-- print_error("FFFFFFFF   ====== self.index", self.index, self.select_btn_type)
	if self.select_btn_type == BX_Box_Select_Type.Part then
		self.parent_call_back(BX_Box_Select_Type.Part, self.index)
	elseif self.select_btn_type == BX_Box_Select_Type.Color then
		self.parent_call_back(BX_Box_Select_Type.Color, self.index)
	end
end
--------------------------------BaoXiaBagSelectRender  end------------------------------------------------

--------------------------------BaoXiaRewardCell------------------------------------------------
local Tween_Time = {
	[1] = 1,
	[2] = 1.5,
	[3] = 2,
	[4] = 2.5,
}

BaoXiaRewardCell = BaoXiaRewardCell or BaseClass(BaseRender)

function BaoXiaRewardCell:LoadCallBack()
	--self.star_pos = self.node_list["reward_root"].rect.anchoredPosition
	self.node_list["click_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRewardCell,self))
	self:PlayFloatTween()
end

function BaoXiaRewardCell:ReleaseCallBack()
	RectTransform.SetAnchoredPositionXY(self.node_list.reward_root.rect, 0, 0)

	if self.tweener then
		self.tweener:Kill()
		self.tweener = nil
	end
end

--[[
function BaoXiaRewardCell:PlayMoveTween(target_pos)
	if self.tweener then
		self.tweener:Kill()
		self.tweener = nil
	end
	-- target_pos.z = 0
	local tween_root = self.node_list["reward_root"].rect
	self.tweener = tween_root:DOMove(target_pos,2.2)
	self.tweener:OnComplete(function ()
		self.node_list["reward_root"]:CustomSetActive(false)
	end)
	self.tweener:SetEase(DG.Tweening.Ease.OutExpo)
end
]]

function BaoXiaRewardCell:PlayFloatTween()
	if self.tweener then
		self.tweener:Kill()
		self.tweener = nil
	end
	if not self.tweener then
		self.node_list["reward_root"]:CustomSetActive(true)
		local tween_root = self.node_list["reward_root"].rect
		--tween_root.anchoredPosition = self.star_pos
		local random_time = math.random(900, 1200)
		self.tweener = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 8, random_time / 1000)
		self.tweener:SetEase(DG.Tweening.Ease.InOutSine)
		self.tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function BaoXiaRewardCell:OnFlush()
	if not self.data then
		return
	end

	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if cfg then
		local bundle, asset = ResPath.GetItem(cfg.icon_id)
		self.node_list.icon_img.image:LoadSpriteAsync(bundle, asset, function()
			self.node_list.icon_img.image:SetNativeSize()
		end)

		bundle, asset = ResPath.GetF2TianShenImage("a3_ss_y_" .. cfg.color)
		self.node_list.bg.image:LoadSpriteAsync(bundle, asset, function()
			self.node_list.bg.image:SetNativeSize()
		end)

		bundle, asset = ResPath.GetEffect(BX_BOX_COLOR_EFFECT[cfg.color])
		self.node_list["color_effect"]:ChangeAsset(bundle, asset)
	end

	-- local bundle, asset = ResPath.GetF2TianShenImage("a3_sglh_gq_" .. self.data.show_box_level)
	-- self.node_list.bg.image:LoadSpriteAsync(bundle, asset, function()
	-- 	self.node_list.bg.image:SetNativeSize()
	-- end)
end

function BaoXiaRewardCell:OnClickRewardCell()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id, num = 1})
end