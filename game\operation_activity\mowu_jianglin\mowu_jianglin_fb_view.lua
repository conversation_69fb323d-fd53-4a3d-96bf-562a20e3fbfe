MoWuJiangLinFBView = MoWuJiangLinFBView or BaseClass(SafeBaseView)

function MoWuJiangLinFBView:__init(view_name)
	self.view_name = "MoWuJiangLinFBView"
	self.view_layer = UiLayer.MainUILow
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/operation_activity_ui/mowu_jianglin_prefab", "layout_mowu_fb_view")
end

function MoWuJiangLinFBView:LoadCallBack()
	self:InitPanel()
	self:RefreshRewardList()
end

function MoWuJiangLinFBView:ReleaseCallBack()
	if self.reward_partake_list then
		self.reward_partake_list:DeleteMe()
		self.reward_partake_list = nil
	end

	if self.reward_mabay_list then
		self.reward_mabay_list:DeleteMe()
		self.reward_mabay_list = nil
	end

	if not Is<PERSON>il(self.obj) then
        ResMgr:Destroy(self.obj)
    end
    self.obj = nil

    CountDownManager.Instance:RemoveCountDown("mwjl_fb_count_down")
end

function MoWuJiangLinFBView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:RefreshView()
			self:RefreshRewardList()
		end
	end
end

function MoWuJiangLinFBView:InitPanel()
	self.reward_partake_list = AsyncListView.New(ItemCell, self.node_list.reward_partake_list)
	self.reward_mabay_list = AsyncListView.New(ItemCell, self.node_list.reward_mabay_list)
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.XINGTIANLAIXI, BindTool.Bind(self.InitCallBack, self))
	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD, BindTool.Bind(self.InitCallBack, self))
end

function MoWuJiangLinFBView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.obj = self.node_list["layout_view"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform, false)
end

function MoWuJiangLinFBView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function MoWuJiangLinFBView:RefreshRewardList()
	local reward_cfg = MoWuJiangLinWGData.Instance:GetJiangLinReward()
	if reward_cfg and reward_cfg.reward_show then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_cfg.reward_show)
		self.reward_partake_list:SetDataList(data_list)
	end

	local boss_drop_cfg = MoWuJiangLinWGData.Instance:GetJiangLinBossDropCfg()
	if boss_drop_cfg then
		local data_list = OperationActivityWGData.Instance:SortDataByItemColor(boss_drop_cfg.random_item)
		self.reward_mabay_list:SetDataList(data_list)
	end
end

function MoWuJiangLinFBView:RefreshView()
	local act_info = MoWuJiangLinWGData.Instance:GetMoWuJianLinInfo()
	if act_info.state == ACTIVITY_STATUS.OPEN then
		self:FlushFBCountDown(act_info.next_time or 0)
	else
		self:FlushFBCountDown(0)
	end
end

function MoWuJiangLinFBView:FlushFBCountDown(next_time)
	CountDownManager.Instance:RemoveCountDown("mwjl_fb_count_down")
	local ser_time = TimeWGCtrl.Instance:GetServerTime()
	local count_down_time = math.floor(next_time - ser_time)
	if count_down_time > 0 then
		self.node_list.next_round_time.text.text = TimeUtil.FormatSecondDHM4(count_down_time)
		CountDownManager.Instance:AddCountDown("mwjl_fb_count_down", BindTool.Bind(self.UpdateFBCountDown, self), nil, nil, count_down_time, 1)
	else
		self.node_list.next_round_time.text.text = ""
	end
end

function MoWuJiangLinFBView:UpdateFBCountDown(elapse_time, total_time)
	self.node_list.next_round_time.text.text = TimeUtil.FormatSecondDHM4(total_time - elapse_time)
end