
function EquipmentWGData:InitTranssexCfg()
    self.transsex_cfg = ConfigManager.Instance:GetAutoConfig("change_item_sex_auto")
    self.item_transsex_cfg_1 = ListToMap(self.transsex_cfg.item_cfg, "item_id_0")
	self.item_transsex_cfg_2 = ListToMap(self.transsex_cfg.item_cfg, "item_id_1")
    self.equip_transsex_cfg_1 = ListToMap(self.transsex_cfg.equipment_cfg, "equip_id_0")
	self.equip_transsex_cfg_2 = ListToMap(self.transsex_cfg.equipment_cfg, "equip_id_1")
end

-- 获得道具转性配置
function EquipmentWGData:GetTranssexData(item_id)
	local data = nil
	local cfg = self.equip_transsex_cfg_1[item_id] or self.equip_transsex_cfg_2[item_id]
	if cfg then
		data = {}
		data.cfg = cfg
		data.is_equip = true
	else
		cfg = self.item_transsex_cfg_1[item_id] or self.item_transsex_cfg_2[item_id]
		if cfg then
			data = {}
			data.cfg = cfg
			data.is_equip = false
		end
	end
	return data
end

-- 根据背包数据获取转性价格
function EquipmentWGData:GetTranssexCostPrice(stuff_data)
	local star_level = 0

	if stuff_data.param then
		star_level = stuff_data.param.star_level or 0
	end

	local cfg_data = EquipmentWGData.Instance:GetTranssexData(stuff_data.item_id)
	local price_list = Split(cfg_data.cfg.cost_price, '|')
	local price = price_list[star_level + 1]

	if price == nil then
		print_error("转性价格配置错误，请检查道具转性表的cost_price:", cfg_data.cfg.cost_price, "  star_level:", star_level)
		price = 1
	end

	return price
end

-- 检查道具是否可以转性
function EquipmentWGData:CanTranssex(bag_item_info)
	local data = self:GetTranssexData(bag_item_info.item_id)

	if data then
		if data.is_equip then
			if bag_item_info.param then
				local equip_star = bag_item_info.param.star_level

				if equip_star >= data.cfg.star_limit then
					return true
				end
			end

			return false
		else
			return true
		end
	end
	return false 
end

-- 根据材料物品id获得产品物品id
function EquipmentWGData:GetTranssexProductItemId(item_id)
	local data = self:GetTranssexData(item_id)
	if data then
		if data.is_equip then
			if data.cfg.equip_id_0 == item_id then
				return data.cfg.equip_id_1
			elseif data.cfg.equip_id_1 == item_id then
				return data.cfg.equip_id_0
			end
		else
			if data.cfg.item_id_0 == item_id then
				return data.cfg.item_id_1
			elseif data.cfg.item_id_1 == item_id then
				return data.cfg.item_id_0
			end
		end
	end
	
	return nil
end

function EquipmentWGData:SetTranssexIsFree(is_free)
	self.transsex_is_free = is_free
end

function EquipmentWGData:GetTranssexIsFree()
	return self.transsex_is_free
end