CultivationTaskView = CultivationTaskView or BaseClass(SafeBaseView)

function CultivationTaskView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(760, 400)})
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_task_view")
end

function CultivationTaskView:LoadCallBack()
	if not self.reward_list1 then
		self.reward_list1 = AsyncListView.New(ItemCell, self.node_list.reward_list1)
		self.reward_list1:SetStartZeroIndex(true)
	end

	if not self.reward_list2 then
		self.reward_list2 = AsyncListView.New(ItemCell, self.node_list.reward_list2)
		self.reward_list2:SetStartZeroIndex(true)
	end

	self.node_list.title_view_name.text.text = Language.Cultivation.CultivationTaskViewTitle

	XUI.AddClickEventListener(self.node_list.btn_get1, BindTool.Bind(self.OnClickGetBtn, self, 1))
	XUI.AddClickEventListener(self.node_list.btn_get2, BindTool.Bind(self.OnClickGetBtn, self, 2))
end

function CultivationTaskView:ReleaseCallBack()
	if self.reward_list1 then
		self.reward_list1:DeleteMe()
		self.reward_list1 = nil
	end

	if self.reward_list2 then
		self.reward_list2:DeleteMe()
		self.reward_list2 = nil
	end
end

function CultivationTaskView:OnFlush()
    for i = 1, 2 do
		local task_info = CultivationWGData.Instance:GetRoleXiuWeiInfoByTaskType(i - 1)

		if not IsEmptyTable(task_info) then
			local task_cfg = CultivationWGData.Instance:GetXiuWeiTaskCfgByTypeAndSeq(task_info.task_type, task_info.task_seq)
			local target_task_cfg = {}
			local reward_item = {}

			-- 2023/06/12后端傻逼操作 因为领取后直接跳入下一个了 后端不发领取状态 说让拿不到任务配置时表示任务已经全部领取完了
			if IsEmptyTable(task_cfg) then
				local task_cfg = CultivationWGData.Instance:GetXiuWeiTaskCfg(i - 1)
				local target_cfg = task_cfg[#task_cfg]

				target_task_cfg = target_cfg
				reward_item = target_cfg.reward_item
			else
				target_task_cfg = task_cfg
				reward_item = task_cfg.reward_item
			end

			if i == 1 then
				self.reward_list1:SetDataList(reward_item)
			else
				self.reward_list2:SetDataList(reward_item)
			end

			self.node_list["desc_task_title" .. i].text.text = target_task_cfg.task_title
			self.node_list["desc_target" .. i].text.text = target_task_cfg.task_name
			self.node_list["desc_content" .. i].text.text = target_task_cfg.task_decs
			XUI.SetButtonEnabled(self.node_list["btn_get" .. i], task_info.status == 1)
			self.node_list["get_remind" .. i]:CustomSetActive(task_info.status == 1)
		end
	end
end

function CultivationTaskView:OnClickGetBtn(index)
	local task_info = CultivationWGData.Instance:GetRoleXiuWeiInfoByTaskType(index - 1)

	if not IsEmptyTable(task_info) then
		if task_info.status == 1 then
			-- CultivationWGCtrl.Instance:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.FETCH_TASK_REWARDS, task_info.task_type, task_info.task_seq)
		end
	end
end