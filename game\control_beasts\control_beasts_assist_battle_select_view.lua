ControlBeastsBattleAssistSelectView = ControlBeastsBattleAssistSelectView or BaseClass(SafeBaseView)

function ControlBeastsBattleAssistSelectView:__init()
	self:SetMaskBg(true, true)
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(920,568)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_battle_select2")

	self.battle_data = nil
	self.model_res_id = nil
end

function ControlBeastsBattleAssistSelectView:__delete()

end

function ControlBeastsBattleAssistSelectView:SetSelectData(battle_data)
	self.battle_data = battle_data
end


function ControlBeastsBattleAssistSelectView:ReleaseCallBack()
	self.battle_data = nil

	if self.assist_beast then
		self.assist_beast:DeleteMe()
		self.assist_beast = nil
	end

	if self.show_model then
		self.show_model:DeleteMe()
		self.show_model = nil
	end

	if self.buy_remind_alert ~= nil then
		self.buy_remind_alert:DeleteMe()
		self.buy_remind_alert = nil
	end

	self.model_res_id = nil
end

function ControlBeastsBattleAssistSelectView:LoadCallBack()
	if not self.assist_beast then
		self.assist_beast = BeststsItemRender.New(self.node_list.aim_beast_cell)
		self.assist_beast:SetClickCallBack(BindTool.Bind1(self.OnChangeBeastBtn, self))
	end

	if self.show_model == nil then
		self.show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.show_model:SetRenderTexUI3DModel(display_data)

		-- self.show_model:SetUI3DModel(self.node_list["display"].transform, self.node_list["EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.show_model)
	end

	XUI.AddClickEventListener(self.node_list.change_beast_btn, BindTool.Bind2(self.OnChangeBeastBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_hole_upgrade, BindTool.Bind2(self.HoleUpgradeBtn, self))
end

function ControlBeastsBattleAssistSelectView:OnFlush(param_t)
	local hole_data = ControlBeastsWGData.Instance:GetHoleDataById(self.battle_data.hole_index)

	if not hole_data then
		self.node_list.beast_name.text.text = ""
		return
	end

	if hole_data then
		self.assist_beast:SetData(hole_data)
		self.node_list.left_show:CustomSetActive(hole_data.beasts_bag_id ~= -1)
		self.node_list.change_beast_btn:CustomSetActive(hole_data.beasts_bag_id ~= -1)

		if hole_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(hole_data.beasts_bag_id)
			if beast_data and beast_data.server_data and beast_data.server_data.beast_id > 0 then
				local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_data.server_data.beast_id, beast_data.server_data.use_skin)
	
				if self.model_res_id ~= res_id then
					self.model_res_id = res_id
					local bundle, asset = ResPath.GetBeastsModel(res_id)
	
					self.show_model:SetMainAsset(bundle, asset)
					self.show_model:PlayRoleAction(SceneObjAnimator.Rest)
				end

				local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_data.server_data.beast_id)
				if beast_cfg then
					self.node_list.beast_name.text.text = beast_cfg.beast_name
				end

				local cap, _ = ControlBeastsWGData.Instance:GetBeastsCapValue(beast_data.server_data, nil, nil, hole_data.hole_id, hole_data.hole_level)
				self.node_list.beasts_cap_value.text.text = cap
			end
		end

		self.node_list.change_beast_remind:CustomSetActive(hole_data.red)
		
		local cur_table = self:MontageHoleAttrStr(hole_data)
		if cur_table then
			self.node_list.cur_level_text.text.text = string.format(Language.ContralBeasts.HoleTips14, hole_data.hole_level)
			self.node_list.cur_attr_str_1.text.text = cur_table.attr_str_1
			self.node_list.cur_attr_value_1.text.text = cur_table.attr_value_1
			self.node_list.cur_attr_str_2.text.text = cur_table.attr_str_2
			self.node_list.cur_attr_value_2.text.text = cur_table.attr_value_2
		end

		local next_table = self:MontageHoleAttrStr(hole_data, true)
		if next_table then
			self.node_list.next_level_root:CustomSetActive(true)
			self.node_list.next_level_full:CustomSetActive(false)
			self.node_list.btn_hole_upgrade:CustomSetActive(true)
			self.node_list.next_level_text.text.text = string.format(Language.ContralBeasts.HoleTips15, hole_data.hole_level + 1)
			self.node_list.next_attr_str_1.text.text = next_table.attr_str_1
			self.node_list.next_attr_value_1.text.text = next_table.attr_value_1
			self.node_list.next_attr_str_2.text.text = next_table.attr_str_2
			self.node_list.next_attr_value_2.text.text = next_table.attr_value_2
		else
			self.node_list.next_level_root:CustomSetActive(false)
			self.node_list.next_level_full:CustomSetActive(true)
			self.node_list.btn_hole_upgrade:CustomSetActive(false)
			self.node_list.next_level_text.text.text = string.format(Language.ContralBeasts.HoleTips15, hole_data.hole_level)
		end

		local cfg_data = ControlBeastsWGData.Instance:GetHoleLevelCfgById(hole_data.hole_id, hole_data.hole_level)
		---展示升级
		if cfg_data then
			self.node_list.xianyu_icon:CustomSetActive(cfg_data.condition_type == 1)
			
			if cfg_data.condition_type == 2 then	--2-直购id
				local price = RoleWGData.GetPayMoneyStr(cfg_data.condition_value, cfg_data.rmb_type, cfg_data.rmb_seq)
				self.node_list.upgrade_text.text.text = price
			elseif cfg_data.condition_type == 1 then	--1-灵玉
				local cost_value = cfg_data and cfg_data.condition_value or 0
				self.node_list.upgrade_text.text.text = cost_value
			end
		end
	end
end

-- 拼接属性文本字符串
function ControlBeastsBattleAssistSelectView:MontageHoleAttrStr(hole_data, is_next)
	local real_level = is_next and hole_data.hole_level + 1 or hole_data.hole_level
	local cfg_data = ControlBeastsWGData.Instance:GetHoleLevelCfgById(hole_data.hole_id, real_level)

	if (not cfg_data) or (not hole_data.attr_types) then
		return nil
	end

	local per = 100
	local attr_table = {}

	if hole_data.attr_types == 0 then
		attr_table.attr_str_1 = Language.ContralBeasts.HoleTips13
		if cfg_data and cfg_data.hole_addition then
			per = math.floor(cfg_data.hole_addition / 100) 
		end
		attr_table.attr_value_1 = string.format("%s%%", per)
		attr_table.attr_str_2 = ""
		attr_table.attr_value_2 = ""
	else
		if cfg_data and cfg_data.hole_addition then
			per = math.floor(cfg_data.hole_addition / 100) 
		end

		for index, attr_str in ipairs(hole_data.attr_list) do
			local new_str = EquipmentWGData.Instance:GetAttrName(attr_str, false, false)
			local index_str = string.format("attr_str_%d", index) 
			local value_str = string.format("attr_value_%d", index) 
			attr_table[index_str] = new_str
			attr_table[value_str] = string.format("%s%%", per) 
		end
	end

	return attr_table
end

----------------------------------------------------------------------------------
-- 切换灵兽
function ControlBeastsBattleAssistSelectView:OnChangeBeastBtn()
	if not self.battle_data then
		return
	end

	local hole_data = ControlBeastsWGData.Instance:GetHoleDataById(self.battle_data.hole_index)
	
	if hole_data then
		local is_change = hole_data.beasts_bag_id ~= -1
		local battle_list = ControlBeastsWGData.Instance:GetBattleBeastsList(is_change, self.battle_data.hole_index, hole_data.beasts_bag_id)
		ControlBeastsWGCtrl.Instance:OpenBeastsBattleSelectView(battle_list)
	end
end
-- 阵位升级
function ControlBeastsBattleAssistSelectView:HoleUpgradeBtn()
	if not self.battle_data then
		return
	end

	local hole_data = ControlBeastsWGData.Instance:GetHoleDataById(self.battle_data.hole_index)
	if hole_data then
		local role_gold = GameVoManager.Instance:GetMainRoleVo().gold

		if nil == self.buy_remind_alert then
			self.buy_remind_alert = Alert.New(nil, nil, nil, nil, true)
			self.buy_remind_alert:SetShowCheckBox(true, "beasts_purchase")
			self.buy_remind_alert:SetCheckBoxDefaultSelect(false)
		end

		local cfg_data = ControlBeastsWGData.Instance:GetHoleLevelCfgById(hole_data.hole_id, hole_data.hole_level)
		if cfg_data then
			if cfg_data.condition_type == 2 then	--2-直购id
				local price = RoleWGData.GetPayMoneyStr(cfg_data.condition_value, cfg_data.rmb_type, cfg_data.rmb_seq)
				self.buy_remind_alert:SetLableString(string.format(Language.ContralBeasts.HoleTips11, price, self.battle_data.title_name))
				self.buy_remind_alert:SetOkFunc(function()
					RechargeWGCtrl.Instance:Recharge(cfg_data.condition_value, cfg_data.rmb_type, cfg_data.rmb_seq)
					-- self:Close()
				end)
		
				self.buy_remind_alert:Open()
			elseif cfg_data.condition_type == 1 then	--1-灵玉
				local cost_value = cfg_data and cfg_data.condition_value or 0
				self.buy_remind_alert:SetLableString(string.format(Language.ContralBeasts.HoleTips16, cost_value, self.battle_data.title_name))
				self.buy_remind_alert:SetOkFunc(function()
					if role_gold < cost_value then
						VipWGCtrl.Instance:OpenTipNoGold()
					else
						--发送协议
						ControlBeastsWGCtrl.Instance:SendOperateTypeSlotUpgrade(hole_data.hole_id)
						-- self:Close()
					end
				end)
		
				self.buy_remind_alert:Open()
			end
		end
	end
end