-- ；累计充值  活动号 2091
TotalChargeView = TotalChargeView or BaseClass(SafeBaseView)
function TotalChargeView:__init()
	self:AddViewResource(0, "uis/view/total_recharge_ui_prefab", "layout_total_recharge")
	self.open_tween = nil
	self.close_tween = nil
	self.select_index = 1
end

function TotalChargeView:__delete()
	
end

function TotalChargeView:ReleaseCallBack()
	if self.total_recharge_list_two then
		self.total_recharge_list_two:DeleteMe()
		self.total_recharge_list_two = nil
	end

	if CountDownManager.Instance:HasCountDown("total_recharge") then
		CountDownManager.Instance:RemoveCountDown("total_recharge")
	end
	self.has_load_callback = nil
end

function TotalChargeView:LoadCallBack()
	self.has_load_callback = true
	self:CreateScrollList()
	self:RefreshView()
end

function TotalChargeView:CreateScrollList()
	self.total_recharge_list_two = AsyncListView.New(TotalRechargeListItemRender, self.node_list.ph_total_recharge_list)
end

function TotalChargeView:OpenCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
end

function TotalChargeView:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function TotalChargeView:CloseCallBack()
	self.open_tween = nil
end

function TotalChargeView:RefreshView()
	if not self.has_load_callback then
		return
	end
	local data_list = TotalChargeWGData.Instance:GetRechargeDataInfo()
	if not data_list or not next(data_list) then return end
	self.total_recharge_list_two:SetDataList(data_list,3)

	if CountDownManager.Instance:HasCountDown("total_recharge") then
		CountDownManager.Instance:RemoveCountDown("total_recharge")
	end
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI)
	if nil == activity_info then
		self.node_list.lbl_leave_tips.text.text = 0
		return
	end

	local mul_time = activity_info.next_time - TimeWGCtrl.Instance:GetServerTime()
	if ACTIVITY_STATUS.OPEN == activity_info.status and mul_time > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("total_recharge", BindTool.Bind1(self.UpdateOpenCountDownTime, self), BindTool.Bind1(self.CompleteOpenCountDownTime, self), activity_info.next_time, nil, 1)
	else
		self:CompleteOpenCountDownTime()
	end
end

function TotalChargeView:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.lbl_leave_tips.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
	end
end

function TotalChargeView:CompleteOpenCountDownTime()
	self.node_list.lbl_leave_tips.text.text = 0
end

-------------------------------------------------------------------
------------------         itemRender           -------------------
-------------------------------------------------------------------
TotalRechargeListItemRender = TotalRechargeListItemRender or BaseClass(BaseRender)
function TotalRechargeListItemRender:__init()
	self:CreateChild()
end

function TotalRechargeListItemRender:__delete()
	for i,v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	self.item_list = {}
end

function TotalRechargeListItemRender:CreateChild()
	self.item_list = {}
	for i=1,5 do
		local item_cell = ItemCell.New(self.node_list['ph_cell_' .. i])
		self.item_list[i] = item_cell
	end
	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind1(self.OnClickLingQu, self))
end

function TotalRechargeListItemRender:OnFlush()
	if nil == self.data or not next(self.data) then return end
	local item_data = self.data.reward_item
	if self.data.reward_item.item_id ~= nil then
		item_data = ServerActivityWGData.Instance:GetShowRewardListByCfg({self.data.reward_item}, false)
	else
		item_data = ServerActivityWGData.Instance:GetShowRewardListByCfg(self.data.reward_item, false)
	end

	for i=1,5 do
		if nil == item_data[i] then
			self.item_list[i]:SetVisible(false)
		else
			self.item_list[i]:SetData(item_data[i])
			self.item_list[i]:SetVisible(true)
		end
	end

	if self.data.is_geted then
		self.node_list.btn_lingqu:SetActive(true)
		self.node_list.img_yes_reward:SetActive(false)
		self.node_list.img_no_reward:SetActive(false)
	else
		self.node_list.btn_lingqu:SetActive(false)
		self.node_list.img_yes_reward:SetActive(false)
		self.node_list.img_no_reward:SetActive(true)
	end

	if self.data.is_have_opp then
		self.node_list.btn_lingqu:SetActive(false)
		self.node_list.img_yes_reward:SetActive(true)
		self.node_list.img_no_reward:SetActive(false)
	end
	self.node_list.lbl_desc.text.text = self.data.instruction
end

function TotalRechargeListItemRender:OnClickLingQu()
	local param_t ={}
	param_t = {
		rand_activity_type = self.data.reward_type,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD,
		param_1 = self.data.seq
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end

--override
function TotalRechargeListItemRender:CreateSelectEffect()
	
end