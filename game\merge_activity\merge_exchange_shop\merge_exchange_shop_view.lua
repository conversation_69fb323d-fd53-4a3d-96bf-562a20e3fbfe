local this = MergeActivityView

function this:LoadIndexCallBackExchange()
	self:InitListExchange()

	self.data = MergeExchangeShopWGData.Instance:GetData()
	-- self.node_list.exchange_name.text.text = self.data.item_name
	--self.node_list.exchange_des.text.text = Language.MergeExchange.Exchange_Des

	self:NewExchangeShopModel()

	-- self.flush_exchange = BindTool.Bind(self.FlushExchange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.flush_exchange)
end

function this:ReleaseExchangeCallBack()
	if self.exchange_count_down then
		CountDown.Instance:RemoveCountDown(self.exchange_count_down)
		self.exchange_count_down = nil
	end

	if self.exchange_shop_act_render then
		self.exchange_shop_act_render:DeleteMe()
		self.exchange_shop_act_render = nil
	end

	self:DeleteListExchange()

	-- ItemWGData.Instance:UnNotifyDataChangeCallBack(self.flush_exchange)
	-- self.flush_exchange = nil
end

function this:FlushExchange()
	if self.exchange_list ~= nil then
		self.exchange_list.scroller:RefreshActiveCellViews()
	end

	if self.exchange_shop_act_render then
		self.exchange_shop_act_render:ActModelPlayLastAction()
	end
end

function this:OnSelectExchangeShop()
	self:SetRuleInfo(Language.MergeExchange.Rule_Desc, Language.MergeExchange.Btn_Rule_Title)
	self:SetOutsideRuleTips(Language.MergeExchange.Out_Side_Rule or "")
end

function this:DeleteListExchange()
	self.exchange_list = nil
	if self.exchange_cell_list then
		for k,v in pairs(self.exchange_cell_list) do
			v:DeleteMe()
		end
		self.exchange_cell_list = nil
	end
end

function this:InitListExchange()
	self.exchange_cell_list = {}
	self.exchange_list = self.node_list.exchange_list
	local list_delegate = self.exchange_list.list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCellExchange, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCellExchange, self)
end
 
function this:GetNumberOfCellExchange()
	return math.ceil(MergeExchangeShopWGData.Instance:GetShopListNum() / 3)
end

function this:RefreshCellExchange(cell, cell_index)
	cell_index = cell_index + 1
	local item_cell = self.exchange_cell_list[cell]
	local data_list = self:GetDataListExchange()
	if not item_cell then
		item_cell = CommonItemGroup.New(cell.gameObject)
		item_cell:Init(MergeExchangeShopItem, "exchange_item", 3)
		self.exchange_cell_list[cell] = item_cell
	end
	item_cell:SetIndex(cell_index)
	if data_list then
		item_cell:SetData(data_list)
		item_cell:SetClickCallBack(function (data)
			self:OnClickItemExchange(data)
		end)
	end
end

function this:GetDataListExchange()
	return MergeExchangeShopWGData.Instance:GetExchangeShopList()
end

function this:OnClickItemExchange(data)
	-- self:FlushExchange()
end

function this:CountDownExchangeTime(end_time)
	-- if self.exchange_count_down then
	-- 	CountDown.Instance:RemoveCountDown(self.exchange_count_down)
	-- 	self.exchange_count_down = nil
	-- end
	-- local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- if server_time > end_time then
	-- 	return
	-- end
	-- self.node_list.exchange_end_time.text.text = "活动倒计时：" .. 
	-- ToColorStr(TimeUtil.FormatSecondDHM6(end_time - server_time), "#F6231BFF")
	
	-- self.exchange_count_down = CountDown.Instance:AddCountDown(end_time - server_time, 1, function ()
	-- 	local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- 	if end_time - server_time > 0 then
	-- 		self.node_list.exchange_end_time.text.text = "活动倒计时：" .. 
	-- 		ToColorStr(TimeUtil.FormatSecondDHM6(end_time - server_time), "#F6231BFF") 
	-- 	end
	-- end)
end

function this:NewExchangeShopModel()
	local data = {}
	if self.data.show_model then
		data.render_type = 0
		data.bundle_name = self.data.show_model_bundle
		data.asset_name = self.data.model_id
	elseif self.data.show_item_id == "" then
		data.bundle_name = self.data.show_picture_bundle
		data.asset_name = self.data.show_picture
		data.render_type = 2
	else
		data.item_id = self.data.show_item_id
		data.render_type = 0
	end

	if self.data.position and self.data.position ~= "" then
		local pos_list = string.split(self.data.position, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if self.data.rotation and self.data.rotation ~= "" then
		local rot_list = string.split(self.data.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if self.data.scale and self.data.scale ~= "" then
		data.model_adjust_root_local_scale = self.data.scale
	end

	data.model_rt_type = ModelRTSCaleType.L

	self.exchange_shop_act_render = OperationActRender.New(self.node_list.exchange_model)
	self.exchange_shop_act_render:SetData(data) --Test Model
end