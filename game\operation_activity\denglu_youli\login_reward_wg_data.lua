LoginRewardWGData = LoginRewardWGData or BaseClass()

function LoginRewardWGData:__init()
	if LoginRewardWGData.Instance then 
		ErrorLog("[LoginRewardWGData] Attemp to create a singleton twice !")
	end

	LoginRewardWGData.Instance = self
    OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD, {[1] = OPERATION_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowLoginRedPoint, self))
    RemindManager.Instance:Register(RemindName.OperationLoginRward, BindTool.Bind(self.IsShowLoginRedPoint, self))

    self:LoadConfig()
end

function LoginRewardWGData:__delete()
	LoginRewardWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.OperationLoginRward)
end

function LoginRewardWGData:LoadConfig()
	self.login_auto = ConfigManager.Instance:GetAutoConfig("operation_activity_new_login_reward_auto")
	self.login_reward = self.login_auto.login_reward
    self.login_activity_param = self.login_auto.activity_param
    self.login_interface = self.login_auto.interface
	self.login_language = self.login_auto.language
end

function LoginRewardWGData:GetActCanOpen()
	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return false
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= param_cfg.open_role_level then
		return true
	end
	return false
end

--根据开服天数获取档次配置
function LoginRewardWGData:GetParamCfgByServerOpenDay()
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD)--TimeWGCtrl.Instance:GetCurOpenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
    for i,v in ipairs(self.login_activity_param) do
		if v.start_server_day <= open_day and open_day < v.end_server_day and week == v.week_id then 
			return v
		end
	end
end

--根据配置档次获取奖励配置
function LoginRewardWGData:GetOpenDays()
	local reward = {}
	local activity_param = self:GetParamCfgByServerOpenDay() 

	if not activity_param then 
		return reward
	end

	local grade = activity_param.grade
	for i,v in ipairs(self.login_reward) do
		if v.grade == grade then
			table.insert(reward, v)
		end
	end
	return reward
end

--根据档次来获取界面配置
function LoginRewardWGData:GetViewCfg()
	local activity_param = self:GetParamCfgByServerOpenDay()
	if not activity_param then 
		return 
	end

	local interface = activity_param.interface
	for i,v in ipairs(self.login_interface) do
		if v.interface == interface then 
			return v
		end
	end
end

function LoginRewardWGData:GetLanguageCfg()
	return self.login_language
end


------------------------------------  登录奖励 -------------------------------------------
-- 获取登录奖励配置
function LoginRewardWGData:GetLoginRewardCfgByDay(day)
	local reward = self:GetOpenDays()
	if IsEmptyTable(reward) then 
		return 
	end

	--奖励与对应天数相等时出现
	for k,v in ipairs(reward) do
		if day == v.day_index then
			return v
		end
	end
end

--获取活动提示
function LoginRewardWGData:GetActivityTip(index)
	local cfg = self:GetThemeCfgByTabIndex(index)
	if cfg ~= nil then
		return cfg.tab_name, cfg.rule_desc
	end
	return nil,nil
end

function LoginRewardWGData:SetLoginRewardInfo(protocol)
	if self.logindaydata == nil then
		self.logindaydata = {}
	end
	self.logindaydata.day_list = {}
	self.logindaydata.login_day_num = protocol.login_day_num
	self.logindaydata.len = 0

	local cfg = self:GetOpenDays()
	if IsEmptyTable(cfg) then 
		return
	end
	for k,v in pairs(cfg) do
		local data = {}
		data.index = v.day_index
		data.vip_level = v.vip_lv or 0
		data.common_gift_state = protocol.reward_state[v.day_index].common_gift_state 	--是否领取
		data.special_gift_state = protocol.reward_state[v.day_index].special_gift_state
		table.insert(self.logindaydata.day_list,data)
		self.logindaydata.len = self.logindaydata.len + 1
	end
	RemindManager.Instance:Fire(RemindName.OperationLoginRward)
end


function LoginRewardWGData:GetDyaItemSelect()
	return self.cur_day_item_select, self.cur_day_item_select_bg
end

function LoginRewardWGData:DeleteItemSelect()
	self.cur_day_item_select = nil
	self.cur_day_item_select_bg = nil
end

function LoginRewardWGData:GetLoginDayData()
	return self.logindaydata
end

function LoginRewardWGData:GetLoginDyaIndex()
	if self.logindaydata == nil or self.logindaydata.login_day_num == nil then
		return 0
	end
	return self.logindaydata.login_day_num
end

function LoginRewardWGData:GetActivityEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD)
	if activity_info ~= nil then
		return activity_info.end_time
	end
	return  0
end

function LoginRewardWGData:IsShowLoginRedPoint()
	if not OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD) then
		return 0
	end

	if self.logindaydata ~= nil and self.logindaydata.day_list ~= nil then
		local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
		for k,v in ipairs(self.logindaydata.day_list) do
			local cfg = self:GetLoginRewardCfgByDay(v.index)
			if not cfg then 
				return 0
			end
			if v.common_gift_state == LoginRewardState.KLQ then
				return 1
			end

			if v.special_gift_state == LoginRewardState.KLQ then
				return 1
			else
				if v.special_gift_state == LoginRewardState.BKL and v.index <= self.logindaydata.login_day_num then
					if cfg.vip_lv ~= "" and cfg.vip_lv > 0 and cur_vip_level >= cfg.vip_lv then
						return 1
					end
				end
			end
		end
	end
	return 0
end

--领取时获取当前可领取的天数，如果前面有则跳转index-1，如果后面有则跳转index+1，如果当前没有可领则index+1刷新一次，
--若当前的index为最后的index则不跳转
function LoginRewardWGData:GetCanRewardDay()
	local canday = {}
	if self.logindaydata ~= nil and self.logindaydata.day_list ~= nil then 
		for i,v in ipairs(self.logindaydata.day_list) do
			--返回所有的领取状态
			table.insert(canday,v.common_gift_state)
		end
		return canday
	end
	return canday 
end
