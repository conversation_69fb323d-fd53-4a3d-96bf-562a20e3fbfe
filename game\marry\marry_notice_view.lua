MarryNoticeView = MarryNoticeView or BaseClass(SafeBaseView)

function MarryNoticeView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true,true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_notice_view")
end

function MarryNoticeView:__delete()

end

function MarryNoticeView:ReleaseCallBack()
	self.is_close = nil
	if self.cell_item then
		for i=0,1 do
			self.cell_item[i]:DeleteMe()
		end
		self.cell_item = nil
	end

    if self.open_tween_sequence then
        self.open_tween_sequence:Kill()
        self.open_tween_sequence = nil
    end
	if CountDownManager.Instance:HasCountDown("close_notice_countdown") then
		CountDownManager.Instance:RemoveCountDown("close_notice_countdown")
	end

	if CountDownManager.Instance:HasCountDown("noticebaby_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("noticebaby_end_countdown")
	end
end

function MarryNoticeView:CloseCallBack()
	self.is_close = false
end

function MarryNoticeView:IsCanCloseView(is_close)
	self.is_close = is_close
end

function MarryNoticeView:InitPlayTween()
	--self.node_list["marry_baby_notice_parent"].canvas_group.alpha = 0
	self.node_list["common_mask"].canvas_group.alpha = 0
	--self.node_list["marry_notice_parent"].canvas_group.alpha = 0
	-- local left_panel = self.node_list["left_panel"]
	-- local right_panel = self.node_list["right_panel"]
	-- local middle_panel = self.node_list["middle_panel"]
	-- left_panel.transform.anchoredPosition = Vector2(0, 27)
	-- right_panel.transform.anchoredPosition = Vector2(0, 24)
	-- middle_panel.transform.localScale = Vector3(0, 1, 1)
    if self.open_tween_sequence then
        self.open_tween_sequence:Kill()
        self.open_tween_sequence = nil
    end

	local sequence = DG.Tweening.DOTween.Sequence()
	-- local tween_1 = left_panel.rect:DOAnchorPosX(-622, 0.6)
	-- local tween_2 = right_panel.rect:DOAnchorPosX(588, 0.6)
	-- local tween_3 = middle_panel.rect:DOScale(Vector3(1, 1, 1), 0.6)
	-- sequence:Append(tween_1)
	-- sequence:Join(tween_2)
	-- sequence:Join(tween_3)

	sequence:OnComplete(function ()
		self.node_list["marry_baby_notice_parent"].canvas_group:DoAlpha(0, 1, 1)
		self.node_list["common_mask"].canvas_group:DoAlpha(0, 1, 1)
		self.node_list["marry_notice_parent"].canvas_group:DoAlpha(0, 1, 1)
	end)
	sequence:SetEase(DG.Tweening.Ease.Linear)
	self.open_tween_sequence = sequence
end


function MarryNoticeView:LoadCallBack()
    self.node_list["btn_lq"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function MarryNoticeView:ShowIndexCallBack()
	self.node_list["time_desc"].text.text = ""
	self.node_list["view_parent"].transform.anchoredPosition = Vector2(0,0)
	self.node_list["view_parent"].transform.localScale = Vector3(1,1,1)
	if self.is_close then
		local end_time = TimeWGCtrl.Instance:GetServerTime() + 15
		self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
		CountDownManager.Instance:AddCountDown("close_notice_countdown", BindTool.Bind1(self.UpdataEndTime, self), BindTool.Bind1(self.EndTimeCallBack, self), end_time, nil, 1)
		self.is_close = false
    end
    --self:InitPlayTween()
end

function MarryNoticeView:UpdataEndTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	self.node_list["time_desc"].text.text = string.format(Language.Marry.NoticeTimeHint,format_time.s)
end

function MarryNoticeView:EndTimeCallBack()
	self.node_list["time_desc"].text.text = ""
	self:MoveToTarget()
end

function MarryNoticeView:OnFlush()
	self:FlushView()
end

function MarryNoticeView:FlushView()
	local data = MarryWGData.Instance:GetCurNoticeReward()
	if nil == self.cell_item then
		self.cell_item = {}
		for i=0,1 do
		 	self.cell_item[i] = ItemCell.New(self.node_list["ph_cell_"..i])
		end
	end
	for i=0,1 do
		if data[i] then
			self.node_list["ph_cell_"..i]:SetActive(true)
			self.cell_item[i]:SetData(data[i])
		else
			self.node_list["ph_cell_"..i]:SetActive(false)
		end
	end

	local cur_states = MarryWGData.Instance:GetCurNoticeStates()
	local fetch_reward_flag = MarryWGData.Instance:GetCurNoticeRewardFlag()
	local str = MarryWGData.Instance:GetCurNoticeDes()
	-- self.node_list["btn_lq_text"].text.text = Language.Marry.MarryBntLq1
	--self.node_list["title_desc"].text.text = Language.Marry.NoticeTitleDesc
	self.node_list["title_desc"].text.text = Language.Marry.NoticeTitleDesc1
	self.node_list["notice_desc1"].text.text = Language.Marry.NoticeDesc1
	self.node_list["notice_desc2"].text.text = Language.Marry.NoticeDesc2
	self.node_list["notice_desc3"].text.text = Language.Marry.NoticeDesc3

	-- if cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY then
	-- 	self:SetOtherVisable(false)
	-- 	if fetch_reward_flag == 1 then
	-- 		self.node_list["btn_lq_text"].text.text = Language.Marry.MarryBntLq2
	-- 		local notice_cfg = MarryWGData.Instance:GetAdvanceNoticeCfg()
	-- 		local vary_level = notice_cfg.marry_button_vary_level or 0
	-- 		local role_lv = RoleWGData.Instance.role_vo.level or 0
	-- 		if role_lv >= vary_level then
	-- 			self.node_list["btn_lq_text"].text.text = Language.Marry.MarryBntLq3
	-- 		end
	-- 	end
	-- elseif cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY then
	-- 	self:SetOtherVisable(true)
	-- 	if fetch_reward_flag == 1 then
	-- 		self.node_list["btn_lq_text"].text.text = Language.Marry.MarryBntLq4
	-- 	end
	-- end

	local role_level = RoleWGData.Instance:GetRoleLevel() or 0
	local advance_notice_cfg = MarryWGData.Instance:GetAdvanceNoticeCfg()
	local reward_limit = advance_notice_cfg.reward_limit or 0
	self.node_list["btn_lq_text"].text.text = reward_limit <= role_level and Language.Marry.MarryBntLq1 or Language.Marry.MarryBntLq5

	self.node_list["img_ylq"]:SetActive(fetch_reward_flag == 1)
	self.node_list["btn_lq"]:SetActive(fetch_reward_flag ~= 1)
	self.node_list["lingqu_red"]:SetActive(MarryWGData.Instance:GetMarryNoticeRemind() == 1)
	-- XUI.SetButtonEnabled(self.node_list["btn_lq"], fetch_reward_flag == 0)

end

-- function MarryNoticeView:SetOneDes(str_list)
-- 	if nil == self.node_list["baby_notice_desc_1"] or nil == str_list[1] then
-- 		return
-- 	end
-- 	local get_baby_timestamp, get_baby_flag = MarryWGData.Instance:GetBabyLingQuInfo()
-- 	if get_baby_timestamp <= 0 then
-- 		self.node_list["baby_notice_desc_1"].text.text = str_list[1]
-- 		return
-- 	elseif get_baby_timestamp - TimeWGCtrl.Instance:GetServerTime() > 0 and get_baby_flag == 0 then
-- 		self:UpdataEndNoticeTime(TimeWGCtrl.Instance:GetServerTime(), get_baby_timestamp)
-- 		if CountDownManager.Instance:HasCountDown("noticebaby_end_countdown") then
-- 			CountDownManager.Instance:RemoveCountDown("noticebaby_end_countdown")
-- 		end
-- 		CountDownManager.Instance:AddCountDown("noticebaby_end_countdown", BindTool.Bind1(self.UpdataEndNoticeTime, self), BindTool.Bind1(self.EndTimeNoticeCallBack, self), get_baby_timestamp, nil, 1)
-- 	elseif get_baby_timestamp > 0 and get_baby_flag == 0 then --时间到未领取
-- 		self.node_list["baby_notice_desc_1"].text.text = Language.Marry.NoticeBabyHint
-- 		return
-- 	elseif get_baby_timestamp > 0 and get_baby_flag == 1 then --时间到已领取
-- 		self.node_list["baby_notice_desc_1"].text.text = Language.Marry.NoticeBabyHint2
-- 	end
-- end

function MarryNoticeView:UpdataEndNoticeTime(elapse_time, total_time)
	local time = total_time - elapse_time
	local format_time = TimeUtil.Format2TableDHMS(time)
	if format_time.day > 0 then
		self.node_list["baby_notice_desc_1"].text.text = string.format(Language.Marry.GetBabyTimeHint2[1],format_time.day,format_time.hour,format_time.min)
	elseif format_time.hour > 0 then
		self.node_list["baby_notice_desc_1"].text.text = string.format(Language.Marry.GetBabyTimeHint2[2],format_time.hour,format_time.min)
	elseif format_time.min > 0 then
		self.node_list["baby_notice_desc_1"].text.text = string.format(Language.Marry.GetBabyTimeHint2[3],format_time.min)
	else
		self.node_list["baby_notice_desc_1"].text.text = string.format(Language.Marry.GetBabyTimeHint2[4],format_time.s)
	end
end

function MarryNoticeView:EndTimeNoticeCallBack()
	self.node_list["baby_notice_desc_1"].text.text = Language.Marry.NoticeBabyHint
end

function MarryNoticeView:SetOtherVisable(enable)
	self.node_list["marry_notice_parent"]:SetActive(not enable)
	self.node_list["marry_baby_notice_parent"]:SetActive(enable)
end

function MarryNoticeView:OnClickGetReward()
	-- local cur_states = MarryWGData.Instance:GetCurNoticeStates()
	--local fetch_reward_flag = MarryWGData.Instance:GetCurNoticeRewardFlag()
	-- if cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY then
	-- 	if fetch_reward_flag == 1 then
	-- 		local notice_cfg = MarryWGData.Instance:GetAdvanceNoticeCfg()
	-- 		local vary_level = notice_cfg.marry_button_vary_level or 0
	-- 		local role_lv = RoleWGData.Instance.role_vo.level or 0
	-- 		if role_lv >= vary_level then
	-- 			FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_jiehun")
	-- 		end
	-- 		self:Close()
	-- 		return
	-- 	end
	-- elseif cur_states == QINGYUAN_ADVANCE_NOTICE_STATUS.QINGYUAN_ADVANCE_NOTICE_STATUS_BABY then
	-- 	if fetch_reward_flag == 1 then
	-- 		FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_jiehun")
	-- 		self:Close()
	-- 		return
	-- 	end
	-- end
	-- if fetch_reward_flag == 1 then
	-- 	local notice_cfg = MarryWGData.Instance:GetAdvanceNoticeCfg()
	-- 	local vary_level = notice_cfg.marry_button_vary_level or 0
	-- 	local role_lv = RoleWGData.Instance.role_vo.level or 0
	-- 	if role_lv >= vary_level then
	-- 		FunOpen.Instance:OpenViewByName(GuideModuleName.Marry, "marry_jiehun")
	-- 	end
	-- 	self:Close()
	-- 	return
	-- end
	-- MarryWGCtrl.Instance:SendQingYuanNoticeFetchReward(cur_states)

	local can_get = MarryWGData.Instance:GetMarryNoticeRemind() > 0 
	if can_get then
		local cur_states = MarryWGData.Instance:GetCurNoticeStates()
		MarryWGCtrl.Instance:SendQingYuanNoticeFetchReward(cur_states)
	end

	--巡游中直接拦截掉
	if MarryWGData.Instance:GetOwnIsXunyou() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
		return
	end
	
	-- 护送中不可操作
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end
	
	if TaskWGCtrl.Instance:IsFly() then
		return
	end	
	
	GuajiWGCtrl.Instance:StopGuaji()
	TaskGuide.Instance:CanAutoAllTask(false)
	ViewManager.Instance:CloseAll()
	
	local npc_cfg = MarryWGData.Instance:GetYueLaoCfg()
	if npc_cfg then
		local npc_id = MarryWGData.Instance:GetYueLaoNpcId()
		local range = TaskWGData.Instance:GetNPCRange(npc_id)
		GuajiWGCtrl.Instance:MoveToPos(npc_cfg.scene_id, npc_cfg.x, npc_cfg.y, range)
		MoveCache.SetEndType(MoveEndType.ClickNpc)
		MoveCache.param1 = npc_id
	end
end


function MarryNoticeView:MoveToTarget()
	self:DoScaleAnimation()
	local timer = 1.5
	local button_name = MainuiWGData.Instance:GetMainUIButtonName(GuideModuleName.MarryNotice)
	local mian_view = MainuiWGCtrl.Instance:GetView()
	local target = mian_view:GetMainUICanFlyButtonObj2(button_name)
	self.time_quest = GlobalTimerQuest:AddDelayTimer(function()
        local item = self.node_list["view_parent"]
        if item then
            local path = {}
            self.target_pos = target:GetComponent(typeof(UnityEngine.RectTransform)).position
            table.insert(path, self.target_pos)
            local tweener = item.transform:DOPath(
                path,
                timer,
                DG.Tweening.PathType.Linear,
                DG.Tweening.PathMode.TopDown2D,
                1.5,
                nil)
            tweener:SetEase(DG.Tweening.Ease.Linear)
            tweener:SetLoops(0)
            local close_view = function()
                -- MarryWGCtrl.Instance:OpenSelectLoverView(1) -- 结婚
                self:Close()
            end
            tweener:OnComplete(close_view)
            item.loop_tweener = tweener
        end
	end, 0)
end

function MarryNoticeView:DoScaleAnimation()
	self.node_list["view_parent"].rect:DOScale(Vector3(0, 0, 0), 1.5)
end


