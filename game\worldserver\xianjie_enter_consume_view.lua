--仙界魔王进入消耗物品界面
XianJieEnterConsumeView = XianJieEnterConsumeView or BaseClass(SafeBaseView)

function XianJieEnterConsumeView:__init()
	self.is_modal = true
	self.is_any_click_close = true

	self:SetMaskBg(true)
    self.view_name = "EnterConsumView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(700, 400)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_xianjie_enter_boss")
end

function XianJieEnterConsumeView:ReleaseCallBack()
	if nil ~=  self.comsun_cell then
		self.comsun_cell:DeleteMe()
		self.comsun_cell = nil
	end

	self.tiky_item_id = nil
	self.enter_comsun = nil
	self.map_tip = nil
	self.consum_tip = nil
	self.ok_func = nil
end

function XianJieEnterConsumeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.AlertTitile


	self.node_list["btn_enter_dabao"].button:AddClickListener(BindTool.Bind1(self.OnBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_cancle, BindTool.Bind1(self.Close, self))
	if nil ==  self.comsun_cell then
		self.comsun_cell = ItemCell.New(self.node_list["ph_cell"])
		self.comsun_cell:SetNeedItemGetWay(true)
	end
end

function XianJieEnterConsumeView:ShowIndexCallBack()
	self:Flush()
end


function XianJieEnterConsumeView:OnFlush()
    if not self.cur_boss_id then
        return
    end
    local cfg = XianJieBossWGData.Instance:GetBossCfgById(self.cur_boss_id)

    if IsEmptyTable(cfg) then
        print_error("取不到仙界boss配置 boss_id =",self.cur_boss_id)
        return
    end
    --self.tiky_id_seq = cfg.shop_seq


    -- local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.tiky_id_seq)
    -- if IsEmptyTable(shop_cfg) then
    --     print_error("取不到商店配置 seq =",self.tiky_id_seq)
    --     return
    -- end
    local other_cfg = XianJieBossWGData.Instance:GetOtherCfg()
    self.enter_comsun = other_cfg.enter_cost_item_num
	self.tiky_item_id = other_cfg.enter_cost_item_id
	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(self.tiky_item_id)
	self.comsun_cell:SetData({item_id = self.tiky_item_id})
	local color = has_tiky_num >= self.enter_comsun and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.comsun_cell:SetRightBottomColorText(has_tiky_num.."/"..self.enter_comsun, color)
	self.comsun_cell:SetRightBottomTextVisible(true)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.tiky_item_id)

    if item_cfg == nil then
        print_error("取不到物品配置 item_id =",self.tiky_item_id)
        return 
    end

    --local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
    --local cost_text = Language.Common.GoldText
	local name = item_cfg.name
	local color_1 = ITEM_COLOR[item_cfg.color]
	color_1 = color_1 or COLOR3B.GREEN
	name = ToColorStr(name, color_1)
    --local need_num = self.enter_comsun - has_tiky_num

    -- if bind_gold_num >= need_num * shop_cfg.price then
    --     cost_text = Language.Common.BindGoldText
    -- end 

    local slot_limit, slot_page_limit = cfg.slot_limit, cfg.slot_page_limit
    local cur_slot, cur_page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    local is_high = false
    if cur_slot > slot_limit then
        is_high = true
    elseif cur_slot == slot_limit and cur_page > slot_page_limit then
        is_high = true
    end

    local high_str = is_high and Language.XianJieBoss.CurStageTooHigh or ""
    --您已集齐当前层全部碎片，可激活并前往更高的层，是否依然前往
    local can_active = XianJieBossWGData.Instance:GetCurPageCanActive()
    local active_str = can_active and Language.XianJieBoss.CurStageCanActive or ""
    local str = ""
    if can_active then
        str = active_str
    elseif is_high then
        str = high_str
    end

    self.node_list["lbl_consum_money"]:SetActive(is_high or can_active)--has_tiky_num < self.enter_comsun)
    self.node_list["tip2"].tmp.text = Language.XianJieBoss.EnterXianjieTip
	self.node_list["lbl_consum_money"].tmp.text = str
	self.node_list["text_dabao_comsun"].tmp.text = Language.XianJieBoss.EnterXianjieDes1
	self.node_list["consume_num"].tmp.text = string.format(Language.XianJieBoss.EnterXianjieDes2, name, self.enter_comsun)
end

function XianJieEnterConsumeView:SetEnterBossComsunData(boss_id, ok_func)
    self.cur_boss_id = boss_id
    self.ok_func = ok_func
    self:Open()
end

function XianJieEnterConsumeView:OnBtnClick()
    --请求进入
    if self.ok_func then
        self.ok_func()
    end
end
