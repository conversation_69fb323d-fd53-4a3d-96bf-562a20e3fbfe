OpenBossHunterRankView = OpenBossHunterRankView or BaseClass(SafeBaseView)

local boss_add_sorce_key = {
	"world_boss_score",
	"dabao_boss_score",
	"vip_boss_score",
	"person_boss_score",
	"secret_boss_score"
}

function OpenBossHunterRankView:__init(view_name)
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_boss_hunter_rank")
end

function OpenBossHunterRankView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.OpenServer.OpenBossHunterRankTitle

	self.score_rank_list = AsyncListView.New(OpenBossHunterRankItemRender, self.node_list.score_rank_list)
	self.guild_rank_list = AsyncListView.New(OpenBossHunterGuildRankItemRender, self.node_list.guild_rank_list)

	XUI.AddClickEventListener(self.node_list.guild_rank_btn, BindTool.Bind(self.OnClickSwitchBtn, self, 1))
	XUI.AddClickEventListener(self.node_list.self_guild_score_btn, BindTool.Bind(self.OnClickSwitchBtn, self, 2))
end

function OpenBossHunterRankView:ReleaseCallBack()
	if self.score_rank_list then
		self.score_rank_list:DeleteMe()
		self.score_rank_list = nil
	end

	if self.guild_rank_list then
		self.guild_rank_list:DeleteMe()
		self.guild_rank_list = nil
	end
end

function OpenBossHunterRankView:OpenCallBack()
	self.act_boss_hunter_select_index = 1

	ServerActivityWGCtrl.Instance:SendOpenGameBossHunterReq(OGA_BOSS_HUNTER_REQ.OGA_BOSS_HUNTER_REQ_QUERY_SELF_GUILD_RANK)
end

function OpenBossHunterRankView:OnFlush()
	local is_show_guild_rank = self.act_boss_hunter_select_index == 1
	self.node_list.guild_rank_btn.toggle.isOn = is_show_guild_rank
	self.node_list.self_guild_score_btn.toggle.isOn = not is_show_guild_rank

	self:FlushGuildRankPanel(is_show_guild_rank)
	self:FlushScoreRankPanel(not is_show_guild_rank)
end

function OpenBossHunterRankView:OnClickSwitchBtn(index, is_on)
	if is_on then
		self.act_boss_hunter_select_index = index
		local is_show_guild_rank = self.act_boss_hunter_select_index == 1

		self:FlushGuildRankPanel(is_show_guild_rank)
		self:FlushScoreRankPanel(not is_show_guild_rank)
	end
end

function OpenBossHunterRankView:FlushGuildRankPanel(is_show)
	self.node_list.guild_rank_list:SetActive(is_show)
	self.node_list.titles1:SetActive(is_show)

	if is_show then
		local data_list = ServerActivityWGData.Instance:GetBossHunterInfo()

    	if IsEmptyTable(data_list) then
    	    return
    	end

		local act_cfg_list = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
    	local boss_hunter_cfg_list = act_cfg_list and act_cfg_list.boss_hunter_reward or {}
    	local rank_list = data_list.rank_list or {}
    	local temp_list = {}
    	for i=1,#boss_hunter_cfg_list do
    	    temp_list[i] = rank_list[i] or {}
    	end

		self.guild_rank_list:SetDataList(temp_list)
    end
end

function OpenBossHunterRankView:FlushScoreRankPanel(is_show)
	self.node_list.score_rank_list:SetActive(is_show)
	self.node_list.titles2:SetActive(is_show)
	self.node_list.bottom_bg:SetActive(is_show)
	self.node_list.no_one:SetActive(is_show)

	if is_show then
		local data_list = ServerActivityWGData.Instance:GetBossHunterMyGuidRankInfo()
		if IsEmptyTable(data_list) then
			self.node_list.no_one:SetActive(true)
			return
		end

		self.node_list.no_one:SetActive(false)
		local rank_list = data_list.rank_list or {}
		local temp_list = {}

		for i=1,15 do
			temp_list[i] = rank_list[i] or {}
		end

		self.score_rank_list:SetDataList(temp_list or rank_list)

		if data_list.monster_id > 0 then
			local scence_name = Language.OpenServer.BossHunterName[data_list.boss_type] or ""
			local boss_cfg = BossWGData.Instance:GetBossInfoByBossId(data_list.monster_id)
			local boss_name = boss_cfg and boss_cfg.boss_name or ""
			local key = boss_add_sorce_key[data_list.boss_type] or ""
			local opengameact = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
			local act_cfg_list = opengameact and opengameact.boss_hunter
			local boss_score = act_cfg_list and act_cfg_list[1][key] or 0
			self.node_list.tips_desc.text.text = string.format(Language.OpenServer.KillBossTips, data_list.last_killer_name, scence_name, boss_name, boss_score)
		else
			self.node_list.tips_desc.text.text = ""
		end
	end
end


--------------------------------ActBossHunterItemRender-----------------------------
OpenBossHunterRankItemRender = OpenBossHunterRankItemRender or BaseClass(BaseRender)

function OpenBossHunterRankItemRender:OnFlush()
	local data = self:GetData()
	local index = self:GetIndex()
	self.node_list.lbl_rank:SetActive(index > 3)
	self.node_list.img_rank:SetActive(index <= 3)
	if index <= 3 then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. index))
		self.node_list.bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. index))
		self.node_list.bg:SetActive(true)
	else
		self.node_list.lbl_rank.text.text = index
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a3_zqxz_xxdi"))
	end

	if IsEmptyTable(data) then
		self.node_list.lbl_name.text.text = Language.OpenServer.XuWeiYiDai
		self.node_list.lbl_score.text.text = "0"
		return
	end

	self.node_list.lbl_name.text.text = data.name
	self.node_list.lbl_score.text.text = data.score or "0"
end

-------------------------------
OpenBossHunterGuildRankItemRender = OpenBossHunterGuildRankItemRender or BaseClass(BaseRender)

function OpenBossHunterGuildRankItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function OpenBossHunterGuildRankItemRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.ph_cell)
end

function OpenBossHunterGuildRankItemRender:OnFlush()
    local data = self:GetData()
    local index = self:GetIndex()

    self.node_list.lbl_rank.text.text = index
    if index <= 3 then
        self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. index))
        self.node_list.root_bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_".. index))
		self.node_list.root_bg:SetActive(true)
    else
		self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_bg1_3"))
    end
    self.node_list.img_rank:SetActive(index <= 3)
    self.node_list.lbl_rank:SetActive(index > 3)

    local rank_bind_gold = ServerActivityWGData.Instance:GetBossHunterJiangLi(index)
    if rank_bind_gold > 0 then
        self.item_cell:SetData({item_id = 65533, num = rank_bind_gold, is_bind = 1})
    end
    self.item_cell:SetActive(rank_bind_gold > 0)
    if IsEmptyTable(data) then
        self.node_list.lbl_guild_name.text.text = Language.OpenServer.XuWeiYiDai
        self.node_list.lbl_score.text.text = 0
        return
    end

    self.node_list.lbl_guild_name.text.text = data.guild_name or ""
    self.node_list.lbl_score.text.text = data.boss_score or 0
end