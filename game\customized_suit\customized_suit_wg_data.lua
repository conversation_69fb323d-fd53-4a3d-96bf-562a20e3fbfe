CustomizedSuitWGData = CustomizedSuitWGData or BaseClass()

function CustomizedSuitWGData:__init()
	if CustomizedSuitWGData.Instance ~= nil then
		Error<PERSON><PERSON>("[CustomizedSuitWGData] attempt to create singleton twice!")
		return
	end

	CustomizedSuitWGData.Instance = self
	self.theme_skill_list = {}
	self.theme_owner_attr_list = {}
	self.xuancai_list = {}
	self:InitCfg()
	self:InitThemeSkillList()
	self:InitThemeSkillChooseSkill()

	RemindManager.Instance:Register(RemindName.CustomizedSuitView, BindTool.Bind(self.GetTotalRemindAndSuit, self))
end

function CustomizedSuitWGData:__delete()
	CustomizedSuitWGData.Instance = nil
end

---这里只初始化一下自己的技能相关，套装信息直接从衣橱拿
function CustomizedSuitWGData:InitCfg()
	local wardrobe_cfg = ConfigManager.Instance:GetAutoConfig("wardrobe_cfg_auto")
	if wardrobe_cfg then
		self.suit_star_map_cfg = ListToMap(wardrobe_cfg.star, "seq", "need_num")
		self.suit_chooseattr_cfg = ListToMap(wardrobe_cfg.chooseattr, "seq", "part")
	end
end

-- 展示列表..这里映射到衣橱数据表的数据
function CustomizedSuitWGData:UpdateSuitShowList()
	self.item_show_list = {}

	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local all_list = WardrobeWGData.Instance:GetSuitAllList()
	if all_list then
		for suit, suit_data in pairs(all_list) do
			local is_show = WardrobeWGData.Instance:IsShowSuitList(suit)
			if server_day >= suit_data.open_day or is_show then
				if suit_data.theme_type == WARDROBE_THEME_TYPE.CUSTOMIZED_SUIT then
					self.item_show_list[#self.item_show_list + 1] = suit_data
				end
			end
		end
	end

	if not IsEmptyTable(self.item_show_list) then
		table.sort(self.item_show_list, SortTools.KeyLowerSorter("sort_index"))
	end
end

function CustomizedSuitWGData:GetTotalRemindAndSuit()
	local cfg = self:GetSuitShowList()
	if IsEmptyTable(cfg) then
		return 0, 1
	end

	if not FunOpen.Instance:GetFunIsOpened(FunName.CustomizedSuitView) and (not self:GetOneSuitIsActive()) then
		return 0, 1
	end

	local length = #cfg
	for i = 1, length do
		--判断是否可以激活属性
		if cfg[i].can_act then
			return 1, i
		end

		-- 判断是否可以定制技能啥的
		if self:GetOneThemeRed(cfg[i].suit, false) then
			return 1, i
		end
	end

	return 0, 1
end

function CustomizedSuitWGData:GetOneSuitIsActive()
	local cfg = self:GetSuitShowList()
	if IsEmptyTable(cfg) then
		return false
	end

	local length = #cfg
	for i = 1, length do
		local act, mum, max_num = self:IsCustomizedSkillAct(cfg[i].suit)
		if act then
			return true
		end
	end

	return false
end

function CustomizedSuitWGData:GetSuitShowList()
	-- if not self.item_show_list or #self.item_show_list <= 0 then
	-- 	self:UpdateSuitShowList()
	-- end
	self:UpdateSuitShowList()
	return self.item_show_list
end

function CustomizedSuitWGData:GetCustomizedSuitInfoBySuit(suit)
	local show_suit_info = {}
	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			show_suit_info = v
			break
		end
	end
	
	return show_suit_info
end

function CustomizedSuitWGData:GetCustomizedSuitMinLevBySuit(suit, aim_level)
	local min_level = nil

	if not self.item_show_list then
		return min_level
	end

	local info = {}
	local num = 0
	local max_num = 0
	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			info = v.part_list
			
			for k1, part_data in pairs(info) do
				max_num = max_num + 1
				local level = self:GetSuitPartStar(part_data)

				if aim_level and level >= aim_level then
					num = num + 1
				end

				if min_level == nil then
					min_level = level
				end

				if min_level and min_level > level then
					min_level = level
				end
			end
		end
	end

	return min_level, num, max_num
end


function CustomizedSuitWGData:GetSuitPartStar(part_data)
	local level = 1

	--坐骑类型
	local appe_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT
	--灵宠
	local lingchong_type = MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG

	if part_data.type == WARDROBE_PART_TYPE.HUA_KUN then
		appe_type = MOUNT_LINGCHONG_APPE_TYPE.KUN
	end

	if part_data.type == WARDROBE_PART_TYPE.FASHION then				-- 时装大类
		level = NewAppearanceWGData.Instance:GetFashionLevel(part_data.param1, part_data.param2) - 1
	elseif part_data.type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
		local fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(part_data.param1) 
		if fashion_cfg then
			level = NewAppearanceWGData.Instance:GetSpecialQiChongStarLevel(appe_type, fashion_cfg.image_id) - 1
		end
	elseif part_data.type == WARDROBE_PART_TYPE.HUA_KUN then			-- 化鲲
		local fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(part_data.param1)
		if fashion_cfg then
			level = NewAppearanceWGData.Instance:GetSpecialQiChongStarLevel(appe_type, fashion_cfg.id)
		end
	elseif part_data.type == WARDROBE_PART_TYPE.LING_CHONG then
		local lingchong_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(lingchong_type, part_data.param1)
		if lingchong_cfg == nil then
			lingchong_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(lingchong_type, part_data.param1)
		end
	
		if lingchong_cfg then
			level = (lingchong_cfg.star_level or lingchong_cfg.star) - 1
		end
	end
	-- 目前时装到100星了 套装没有这么高 需要做个套装最高星级的判断限制
	local max_level = self:GetSuitMaxLevel(part_data.suit_seq)
	return math.min(level,max_level)
end

-- 获取套装的最高星级
function CustomizedSuitWGData:GetSuitMaxLevel(suit_seq)
	local enemy = {}
	if self.suit_star_map_cfg and suit_seq then
		local suit_data = self.suit_star_map_cfg[suit_seq]
		if suit_data then
			return suit_data[#suit_data].star
		end
	end
	return 0
end

--获取当前是否可以升升星升级（返回是否可以升星和升级， 优先升级）
function CustomizedSuitWGData:GetCustomizedSuitStarStatus(suit, cur_star, cur_skill_level)
	local min_level = self:GetCustomizedSuitMinLevBySuit(suit)
	local star_cfg  = self:GetThemeStarCfgBySuit(suit, min_level)
	if not star_cfg then
		return false, false
	end

	return cur_star < star_cfg.star, cur_skill_level < star_cfg.skill_upgrade_level
end

--获取当前的下一级技能等级是否可以提升
function CustomizedSuitWGData:GetCustomizedSuitIsUpGrade(suit, cur_star, cur_skill_level)
	local star_cfg  = self:GetThemeStarCfgBySuit(suit, cur_star + 1)
	if not star_cfg then
		return false
	end

	return cur_skill_level < star_cfg.skill_upgrade_level
end

function CustomizedSuitWGData:GetCustomizedSuitSatrAttr(suit, cur_star)
	local cur_star_cfg = self:GetThemeStarCfgBySuit(suit, cur_star)
	local next_star_cfg = self:GetThemeStarCfgBySuit(suit, cur_star + 1)

	local get_attr_list = function (cfg)
		local return_table = nil
		local operate_table = {}

		if cfg then
			for i = 1, 4 do
				local key = cfg["attr_id" .. i]
				if key then
					operate_table[key] = {}
					operate_table[key].attr_str = key
					operate_table[key].attr_value = cfg["attr_value" .. i]
				end
			end
		end

		for _, data in pairs(operate_table) do
			if not return_table then
				return_table = {}
			end

			if data and data.attr_str then
				table.insert(return_table, data)
			end
		end

		if return_table ~= nil then
			table.sort(return_table, function (a, b)
				return a.attr_str < b.attr_str
			end)
		end

		return return_table
	end

	local cur_star_attr_data = get_attr_list(cur_star_cfg)
	local next_star_attr_data = get_attr_list(next_star_cfg)
	return cur_star_attr_data, next_star_attr_data
end

--- 获取所有需要展示的套装通过套装id
function CustomizedSuitWGData:IsCustomizedSkillAct(suit)
	self:GetSuitShowList()
	local info = {}
	local num = 0
	local len = 0
	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			info = v.part_list
			len = #info
			
			if info[0] ~= nil then
				len = len + 1
			end

			for k1,v1 in pairs(info) do
				if v1.state ~= REWARD_STATE_TYPE.UNDONE then
					num = num + 1
				end
			end

			return num >= len, num, len
		end
	end

	return false, num, len
end

--- 获取所有需要展示的套装部件解锁状态
function CustomizedSuitWGData:IsCustomizedSuitPartStatus(suit, part)
	if not self.item_show_list then
		return 0
	end

	local info = {}

	for i, v in ipairs(self.item_show_list) do
		if v.suit == suit then
			info = v.part_list
			for k1,v1 in pairs(info) do
				if v1.part == part then
					return v1.state
				end
			end
		end
	end

	return 0
end



---初始化定制技能列表
function CustomizedSuitWGData:InitThemeSkillList()
	local theme_map_cfg = WardrobeWGData.Instance:GetThemeCfg()

	if not theme_map_cfg then
		return
	end

	for suit, suit_data in pairs(theme_map_cfg) do
		if suit_data.theme_type == WARDROBE_THEME_TYPE.CUSTOMIZED_SUIT then
			local theme_skill_data = {}
			local xuancai_data = {}
			theme_skill_data.suit_name = suit_data.name
			theme_skill_data.is_unlock = false			--是否解锁技能
			theme_skill_data.skill_id = 0				--是否解锁技能
			theme_skill_data.skill_level = 1			--是否解锁技能
			theme_skill_data.star = 0					--是否解锁技能
			theme_skill_data.star_red = false			--升星红点	
			theme_skill_data.upgrade_red = false		--技能升级红点
			theme_skill_data.select_skill_red = false	--可选择技能红点
			theme_skill_data.part_list = nil			--定制技能

			local part_list = WardrobeWGData.Instance:GetActivationPartList(suit)
			if part_list then
				theme_skill_data.part_list = {}
				xuancai_data.part_list = {}
				for part, part_data in pairs(part_list) do
					if part and part_data then
						theme_skill_data.part_list[part] = {}
						xuancai_data.part_list[part] = 0
					end
				end
			end

			self.theme_skill_list[suit] = theme_skill_data
			self.xuancai_list[suit] = xuancai_data
		end
	end
end

---初始化定制属性列表
function CustomizedSuitWGData:InitThemeSkillChooseSkill()
	if not self.suit_chooseattr_cfg then
		return
	end

	for seq, part_data in pairs(self.suit_chooseattr_cfg) do
		if not self.theme_owner_attr_list[seq] then
			self.theme_owner_attr_list[seq] = {}
		end

		for part, part_data2 in pairs(part_data) do
			if not self.theme_owner_attr_list[seq][part] then
				self.theme_owner_attr_list[seq][part] = {}
			end

			self.theme_owner_attr_list[seq][part].choose_attr1 = self:SplitThemeChooseSkill(part_data2.choose_attr1)
			self.theme_owner_attr_list[seq][part].choose_attr2 = self:SplitThemeChooseSkill(part_data2.choose_attr2)
		end
	end
end

---拆解属性
function CustomizedSuitWGData:SplitThemeChooseSkill(str)
	local attr_all_data = {}

	local attr_lists = Split(str, "|")
	for _, attr_str in ipairs(attr_lists) do
		if attr_str then
			local attr_list = Split(attr_str, ":")
			local index = tonumber(attr_list[1])
			local attr_data = {}
			attr_data.attr_str = tonumber(attr_list[2])
			attr_data.attr_value = tonumber(attr_list[3])

			if index then
				attr_all_data[index] = attr_data
			end
		end
	end

	return attr_all_data
end


---获取定制技能
function CustomizedSuitWGData:GetThemeSkillBySuit(suit)
	local enemy = {}
	return (self.theme_skill_list or enemy)[suit] or nil
end

--- 获取当前套装的属性列表
function CustomizedSuitWGData:GetThemePartAttrList(suit, part, is_left)
	if (not self.theme_owner_attr_list) or (not self.theme_owner_attr_list[suit]) or (not self.theme_owner_attr_list[suit][part]) then
		return nil
	end

	if is_left then
		return self.theme_owner_attr_list[suit][part].choose_attr1
	end

	return self.theme_owner_attr_list[suit][part].choose_attr2
end

--获取当前的属性
function CustomizedSuitWGData:GetCurrPartAttrList(suit, part, part_data)
	local left_data = self:GetThemePartAttrList(suit, part, true)
	local right_data = self:GetThemePartAttrList(suit, part, false)
	local return_data = {}

	if part_data and part_data.attr1_id and left_data then
		for _, attr_index in ipairs(part_data.attr1_id) do
			table.insert(return_data, left_data[attr_index])
		end
	end

	if part_data and part_data.attr2_id and right_data then
		table.insert(return_data, right_data[part_data.attr2_id])
	end

	return return_data
end


---获取套装定制part定制属性
function CustomizedSuitWGData:GetThemeAttrBySuitPart(suit, part)
	local theme_skill_data = self:GetThemeSkillBySuit(suit)
	if not theme_skill_data then
		return
	end

	if theme_skill_data.part_list then
		return theme_skill_data.part_list[part]
	end

	return nil
end

---重置套装定制part定制属性
function CustomizedSuitWGData:ResetThemeAttrBySuitPart(suit, part)
	local theme_skill_data = self:GetThemeSkillBySuit(suit)
	if not theme_skill_data then
		return
	end

	if theme_skill_data.part_list and theme_skill_data.part_list[part] then
		theme_skill_data.part_list[part] = {}
	end
end

---获取套装技能大师数据
function CustomizedSuitWGData:GetThemeStarCfgBySuit(suit, min_level)
	local enemy = {}
	return ((self.suit_star_map_cfg or enemy)[suit] or enemy)[min_level] or nil
end

---获取一个衣橱技能信息
function CustomizedSuitWGData:GetWardrobeBySkillId(skill_id, skill_level)
	return SkillWGData.Instance:GetCustomizedById(skill_id, skill_level)
end

---定制技能是否解锁
function CustomizedSuitWGData:ThemeSkillIsUnLock(suit)
	local data = self:GetThemeSkillBySuit(suit)

	if not data then
		return false
	end

	return data.is_unlock
end

-- 是否可以选择技能红点
function CustomizedSuitWGData:GetOneThemeSelectSkillRed(suit)
	local theme_skill_data = self:GetThemeSkillBySuit(suit)

	if not theme_skill_data then
		return false
	end

	return theme_skill_data.select_skill_red, theme_skill_data
end

-- 是否可以选择红点
function CustomizedSuitWGData:GetOneThemeStarUpRed(suit)
	local theme_skill_data = self:GetThemeSkillBySuit(suit)

	if not theme_skill_data then
		return false
	end

	return theme_skill_data.star_red or theme_skill_data.upgrade_red
end

---获取单个定制技能红点状态
function CustomizedSuitWGData:GetOneThemeRed(suit, is_skill)
	local theme_skill_data = self:GetThemeSkillBySuit(suit)

	if not theme_skill_data then
		return false
	end

	local is_select_skill_red = self:GetOneThemeSelectSkillRed(suit)
	local is_red = self:GetOneThemeStarUpRed(suit) or is_select_skill_red

	if theme_skill_data.part_list and (not is_skill) then
		---套装组件红点
		for part, part_data in pairs(theme_skill_data.part_list) do
			if part_data and part_data.red then
				is_red = is_red or part_data.red
				break
			end
		end
	end
	
	return is_red, theme_skill_data
end

---刷新单个定制技能红点状态
function CustomizedSuitWGData:RefreshOneThemeRed(theme_skill_data, suit)
	if not theme_skill_data then
		return
	end

	theme_skill_data.is_unlock = self:IsCustomizedSkillAct(suit)				--添加套装全部解锁条件
	if theme_skill_data.is_unlock then
		theme_skill_data.select_skill_red = theme_skill_data.skill_id == 0
		local star_red, upgrade_red = self:GetCustomizedSuitStarStatus(suit, theme_skill_data.star, theme_skill_data.skill_level)
		local is_upgrade = self:GetCustomizedSuitIsUpGrade(suit, theme_skill_data.star, theme_skill_data.skill_level)
		theme_skill_data.star_red = star_red
		theme_skill_data.upgrade_red = upgrade_red
		theme_skill_data.is_upgrade = is_upgrade
	end

	---套装组件红点
	for part, part_data in pairs(theme_skill_data.part_list) do
		if part_data then	--self:IsCustomizedSkillAct(suit) and (
			part_data.red = self:IsCustomizedSuitPartStatus(suit, part) ~= REWARD_STATE_TYPE.UNDONE and (part_data.attr2_id == nil or part_data.attr2_id == 0)
		end
	end
end

---刷新选择技能列表
function CustomizedSuitWGData:GetSelectSkillListBySuit(data, suit)
	local skill_level = data and data.skill_level or 1	---服务器默认小于1
	local curr_skill_id = data and data.skill_id or 0
	local curr_select_index = 1

	if skill_level <= 0 then
		skill_level = 1
	end

	local skill_cfg = GuiXuDreamWGData.Instance:GetSkillBySeq(suit)
	local return_table = {}

	if (not IsEmptyTable(skill_cfg)) then
		local select_skill_list = Split(skill_cfg.skill_id, "|") 
		if not select_skill_list or #select_skill_list <= 0 then
			return return_table
		end

		for index, var_skill_id in ipairs(select_skill_list) do
			local skill_id = tonumber(var_skill_id)
			local temp_data = self:GetSelectSkillBySkillId(skill_id, skill_level, curr_skill_id)

			if not IsEmptyTable(temp_data) then
				temp_data.suit = suit
				table.insert(return_table, temp_data)
			end

			if curr_skill_id == skill_id then
				curr_select_index = index
			end
		end
	end

	return return_table, curr_select_index
end

---获取一个前端技能信息
function CustomizedSuitWGData:GetSelectSkillBySkillId(skill_id, skill_level, cur_select_skill_id)
	local skill_data = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	local wardrobe_skill_cfg = self:GetWardrobeBySkillId(skill_id, skill_level)
	local temp_data = {}

	if skill_data and wardrobe_skill_cfg then
		temp_data.skill_id = skill_id
		temp_data.skill_name = wardrobe_skill_cfg.skill_name
		temp_data.cap_value = wardrobe_skill_cfg.capability_inc
		temp_data.skill_desc = skill_data.description
		temp_data.skill_desc2 = wardrobe_skill_cfg.skill_desc
		temp_data.skill_icon = skill_data.icon_resource
		temp_data.skill_level = skill_data.skill_level
		temp_data.skill_type = 0
		temp_data.cur_select_skill_id = cur_select_skill_id

		return temp_data
	end

	return nil
end

---界面解锁套装刷新啥的
function CustomizedSuitWGData:RefreshOneThemeRedbySuit(suit)
	if not self.theme_skill_list[suit] then
		return
	end

	self:RefreshOneThemeRed(self.theme_skill_list[suit], suit)
end

---界面解锁套装刷新啥的
function CustomizedSuitWGData:RefreshAllThemeRedbySuit()
	for suit, theme_skill_data in pairs(self.theme_skill_list) do
		self:RefreshOneThemeRed(theme_skill_data, suit)
	end
end

---刷新全部定制技能（服务器）
function CustomizedSuitWGData:RefreshAllThemeSkill(wardrobe_info_list)
	for _, wardrobe_info in ipairs(wardrobe_info_list) do
		self:RefreshOneThemeSkill(wardrobe_info)
	end
	
	---这个地方一定要调这个方法，没有激活的服务器不会下发，客户端却需要刷新
	self:RefreshAllThemeRedbySuit()		---刷新一下红点
end

---刷新单个定制技能（服务器）
function CustomizedSuitWGData:RefreshOneThemeSkill(wardrobe_info)
	if (not wardrobe_info) or (not self.theme_skill_list[wardrobe_info.seq]) then
		return
	end

	self.theme_skill_list[wardrobe_info.seq].skill_id = wardrobe_info.skill_id
	self.theme_skill_list[wardrobe_info.seq].skill_level = wardrobe_info.skill_level
	self.theme_skill_list[wardrobe_info.seq].star = wardrobe_info.star

	---清空一下重新赋值
	for _, attr_info in ipairs(wardrobe_info.part_attr_list) do
		if self.theme_skill_list[wardrobe_info.seq].part_list[attr_info.part] then
			local part_data = {}
			part_data.part = attr_info.part
			part_data.attr1_id = attr_info.attr1_id
			part_data.attr2_id = attr_info.attr2_id
	
			self.theme_skill_list[wardrobe_info.seq].part_list[attr_info.part] = part_data
		end
	end

	self:RefreshOneThemeRed(self.theme_skill_list[wardrobe_info.seq], wardrobe_info.seq)
end


---获取一个套装的战力
function CustomizedSuitWGData:GetCustomizedAttrCap(custom_attr_list)
	if not custom_attr_list then
		return 0
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

    for index, attr_cell in ipairs(custom_attr_list) do
		if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
			add_tab(attr_str, attr_cell.attr_value)
		end
    end

	local cap = AttributeMgr.GetCapability(attribute)
	return cap, attribute
end


---刷新全部炫彩列表（服务器）
function CustomizedSuitWGData:RefreshAllXuanCai(xuancai_info_list)
	for _, xuancai_info in ipairs(xuancai_info_list) do
		self:RefreshOneXuanCai(xuancai_info)
	end
end

---刷新单个炫彩（服务器）
function CustomizedSuitWGData:RefreshOneXuanCai(xuancai_info)
	if not xuancai_info then
		return
	end

	if not self.xuancai_list then
		self.xuancai_list = {}
	end

	if not self.xuancai_list[xuancai_info.seq] then
		self.xuancai_list[xuancai_info.seq] = {}
	end

	self.xuancai_list[xuancai_info.seq].part_list = {}
	for _, xuancai_id_info in ipairs(xuancai_info.wardrobe_xuancai_ids) do
		self.xuancai_list[xuancai_info.seq].part_list[xuancai_id_info.part] = xuancai_id_info.xuancai_id
	end
end

---刷新单个炫彩（服务器）
function CustomizedSuitWGData:RefreshOneXuanCai2(xuancai_info)
	if not xuancai_info then
		return
	end

	if not self.xuancai_list then
		self.xuancai_list = {}
	end

	if not self.xuancai_list[xuancai_info.seq] then
		self.xuancai_list[xuancai_info.seq] = {}
	end

	if self.xuancai_list[xuancai_info.seq].part_list then
		self.xuancai_list[xuancai_info.seq].part_list[xuancai_info.part] = xuancai_info.appen_image_id
	end
end

-- 获取是否存在炫彩
function CustomizedSuitWGData:GetXuanCaiRecord(suit, part)
    local emtry = {}
    return (((self.xuancai_list or emtry)[suit] or emtry).part_list or emtry)[part] or nil
end

-- 获取炫彩的appimage_id, 没有返回默认
function CustomizedSuitWGData:GetXuanCaiAppimageId(suit, part, res_id)
	local record_id = self:GetXuanCaiRecord(suit, part)
	
	if record_id == nil or record_id == 0 then
		return res_id
	end

	return record_id
end

--获得排序后的list view
function CustomizedSuitWGData:GetSortListView(list_tb)
	local sort_list = {}
	for i, v in pairs(list_tb) do
		local data = {}
		data.index = v:GetIndex()
		data.item = v
		sort_list[#sort_list + 1] = data
	end
	table.sort(sort_list, SortTools.KeyLowerSorter("index"))
	return sort_list
end