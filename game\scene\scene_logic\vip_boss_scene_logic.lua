local BOSS_REBIRTH_REMIND_INTERVAL = 30  -- s
local START_FIND_BOSS_TIME = 8
local PICK_INTERVAL = 2

VipBossSceneLogic = VipBossSceneLogic or BaseClass(CommonFbLogic)

function VipBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.last_select_monster = nil
	self.select_monster_event = nil
	self.next_check_time = 0
end

function VipBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	self.has_get_team_target = nil
	self.last_select_monster = nil
	self.next_check_time = 0

	if self.select_monster_event then
		GlobalEventSystem:UnBind(self.select_monster_event)
		self.select_monster_event = nil
	end
end

function VipBossSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self.has_get_team_target = -1
	-- MainuiWGCtrl.Instance:SetTaskButtonTrue()
	MainuiWGCtrl.Instance:ResetLightBoss()
	BaseFbLogic.SetLeaveFbTip(true)
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE, true)
	BossWGCtrl.Instance:EnterSceneCallback()
	BossWGCtrl.Instance:Close()
	BossWGData.Instance:SetBossEnterFlag(true)

    FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
    self.select_monster_event = GlobalEventSystem:Bind(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjCallBack, self))
	self.main_role_move_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnMainRoleMove, self))
	self.refresh_boss_list = GlobalEventSystem:Bind(OtherEventType.BOSS_CHANGE, BindTool.Bind(self.CheckUseBossChange, self))

	self.enter_fight_event = GlobalEventSystem:Bind(ObjectEventType.ENTER_FIGHT, BindTool.Bind(self.OnEnterFight, self))
	self.exit_fight_event = GlobalEventSystem:Bind(ObjectEventType.EXIT_FIGHT, BindTool.Bind(self.OnExitFight, self))

    if EquipTargetWGCtrl.Instance:CheckNeedBossNoticeTip() then
    	EquipTargetWGCtrl.Instance:OpenBossNoticeTip()
    end

	-- 策划要求第一层要和平模式 后面的根据配置表来
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id == 1360 then
		MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
	else
		local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
		MainuiWGCtrl.Instance:SendSetAttackMode(fb_cfg.default_mode)
	end


	-- 仙君遗迹一二层  转职装备收集弹窗常驻

	local is_in = TransFerWGData.Instance:IsVIPBossScene()
	if is_in then
		TransFerWGCtrl.Instance:OpenEquipCollectTips()
		MainuiWGCtrl.Instance:SetBossTips(true)
	else
		MainuiWGCtrl.Instance:SetBossTips(false)
	end

	self.boss_rebirth_boss_cfg = {}
	self.last_remind_time = 0
end

function VipBossSceneLogic:OnSelectObjCallBack(obj)
	if obj ~= nil and not obj:IsDeleted() and obj:IsBoss() then
		self.last_select_monster = obj:GetMonsterId()
	end
end

function VipBossSceneLogic:FuHuoCallBack(use_type)
	local tarck_type, track_role_uuid = self:GetTrackRoleInfo()
	if tarck_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end

	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack(true)
	else
		self:HereFuHuoCallBack()
	end
end

function VipBossSceneLogic:CommonMoveCallBack(is_force)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	if god_war_data.level ~= -1 and god_war_data.open_status == 1 and (not is_force) then		-- 激活特权或者并开启了
		return
	end

	if self.last_select_monster ~= nil and self.last_select_monster ~= 0 then
		if self.last_select_monster == GameEnum.TeamInvite then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			return
		end

		local scene_id = Scene.Instance:GetSceneId()
		local data = BossWGData.Instance:GetVipBossCfgByBossIdAndSceneId(self.last_select_monster, scene_id)
		if data ~= nil and next(data) ~= nil then
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)

			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
			GuajiCache.monster_id = self.last_select_monster
			MoveCache.param1 = self.last_select_monster
			local range = BossWGData.Instance:GetMonsterRangeByid(self.last_select_monster)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, data.x_pos, data.y_pos, range)
		else
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end			
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function VipBossSceneLogic:HereFuHuoCallBack()
	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:CommonMoveCallBack()
	end

end

function VipBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
		end
	end
	BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
end

-- 是否是挂机打怪的敌人
function VipBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function VipBossSceneLogic:IsMonsterEnemy( target_obj, main_role )
    local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
    if wb_other_cfg.boss_home_day_default_times == -1 then --无限
        return true
    end
	if not BossWGData.Instance:GetVipEnoughTimes() then
		if target_obj:IsBoss() and not BossAssistWGData.Instance:IsAssist() then
			return false, Language.Boss.OutTire
		end
	end
	return true
end

--是否能改变模式
function VipBossSceneLogic:CanChangeAttackMode()
    if BossWGData.Instance:GetEnterVipCfgbyIndex(1).scene_id == Scene.Instance:GetSceneId() then
        return false
    end
    return true
end

-- 获取挂机打怪的位置
function VipBossSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = nil
    local target_y = nil
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end

function VipBossSceneLogic:Out(old_scene_type, new_scene_type)
	self.last_select_monster = nil
	self.has_get_team_target = -1
	CommonFbLogic.Out(self)
	BossWGCtrl.Instance:OutSceneCallback()
	BossWGCtrl.Instance:SendVipAllReq()
	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    MainuiWGCtrl.Instance:ResetTaskPanel()
	MainuiWGCtrl.Instance:SetBossTips(false)
    FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
    -- 装备收集弹窗关闭
	TransFerWGCtrl.Instance:CloseEquipCollectTips()

	local _,boss_info = BossWGData.Instance:GetIsBossVipScene(Scene.Instance:GetSceneId())
	local level = boss_info and boss_info.level or nil
	if level and level == 0 then
		MainuiWGCtrl.Instance:SetButtonModeClick(true)
	end
	if BossWGData.Instance:GetIsEnterInScene() then
		BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	end
	EquipTargetWGCtrl.Instance:CloseBossNoticeTip()

	if self.main_role_move_event then
		GlobalEventSystem:UnBind(self.main_role_move_event)
		self.main_role_move_event = nil
   end

   	if self.refresh_boss_list then
		GlobalEventSystem:UnBind(self.refresh_boss_list)
		self.refresh_boss_list = nil
	end

	if self.enter_fight_event then
		GlobalEventSystem:UnBind(self.enter_fight_event)
		self.enter_fight_event = nil
	end

	if self.exit_fight_event then
		GlobalEventSystem:UnBind(self.exit_fight_event)
		self.exit_fight_event = nil
	end

	
   self.boss_rebirth_boss_cfg = nil
   self.last_remind_time = nil
end

function VipBossSceneLogic:OpenFbSceneCd()

end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function VipBossSceneLogic:GetGuajiCharacter()
	local is_need_stop = false

	local target_obj = self:GetMonster()
	if target_obj ~= nil then
		is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function VipBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function VipBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE

	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end		

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function VipBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function VipBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

-- function VipBossSceneLogic:AtkTeamLeaderTarget()
-- 	local team_leader_info = SocietyWGData.Instance:GetTeamLeader() or {}
-- 	local leader = Scene.Instance:GetRoleByRoleId(team_leader_info.role_id)
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

-- 	if not leader then
-- 		return
-- 	end

-- 	self.has_get_team_target = leader:GetVo() and leader:GetVo().obj_id or -1

-- 	if not leader:IsAtkPlaying() then
-- 		return
-- 	end

-- 	local target_obj
-- 	if leader:IsAtkPlaying() then
-- 		target_obj = leader:GetAttackTarget()
-- 		if not target_obj then
-- 			return
-- 		end
-- 	else
-- 		self.has_get_team_target = -1
-- 		return
-- 	end

-- 	if self:IsEnemy(target_obj) then
-- 		self.has_get_team_target = -1
-- 		GuajiWGCtrl.Instance:StopGuaji()
-- 		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
-- 	end
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end

function VipBossSceneLogic:GetTeamTargetFlag()
	return self.has_get_team_target or -1
end

-- 此场景优先保证单位数量
function VipBossSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function VipBossSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function VipBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	-- self:CheckGuaJiPosMove()

	local floor_time = math.floor(now_time)
	if floor_time >= self.next_check_time then
		self.next_check_time = floor_time + START_FIND_BOSS_TIME
		self:StartOpenFindBoss()
	end
end

-- 主角移动事件
function VipBossSceneLogic:OnMainRoleMove()
	--self:CheckUseBossReflushCard()
end

-- boss改变时需要预留1-2秒检测掉落
function VipBossSceneLogic:CheckUseBossChange()
	self:CheckUseBossReflushCard()

	-- 延迟PICK_INTERVAL秒触发
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	if god_war_data and god_war_data.level ~= -1 and god_war_data.open_status == 1 then
		if god_war_data.total_count > 0 then
			self.next_check_time = self.next_check_time + PICK_INTERVAL
			GuajiCache.target_obj_id  = 0
			GuajiCache.monster_id = 0
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BossPrivilege.GodWarError2)
		end
	end

	if god_war_data and god_war_data.level == -1 and (not self.is_open_ui_view) then
		if BossGodWarWGData.Instance:CheckFirstAutoOpenBossGodwar() then
			local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_godwar")
			if is_open then
				ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_godwar)
				self.is_open_ui_view = true
			end
		end
	end
end

-- boss改变时需要预留1-2秒检测掉落
function VipBossSceneLogic:CheckUseBossReflushCard()
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local boss_list = self:GetFbSceneMonsterBossCfg()
	if IsEmptyTable(boss_list) then
		return
	end

	local pos_x, pos_y = main_role:GetLogicPos()
	local target_distance = 1000 * 1000
	local boss_rebirth_cfg = MainuiWGData.Instance:GetBossRebirthCfg()
	local rebirth_range = boss_rebirth_cfg.rebirth_range
	local rebirth_dis = rebirth_range * rebirth_range
	local boss_cfg

	for k, v in pairs(boss_list) do
		local dis = GameMath.GetDistance(v.x, v.y, pos_x, pos_y, false)
		if dis < rebirth_dis and dis < target_distance then
			boss_cfg = v
			target_distance = dis
		end
	end

	if boss_cfg then
		local boss_state = BossWGData.Instance:GetBossStatusByBossId(boss_cfg.boss_id)
		if boss_state == 0 then
			local server_time = TimeWGCtrl.Instance:GetServerTime()

			local need_remind = false
			if IsEmptyTable(self.boss_rebirth_boss_cfg) then
				need_remind = true
			else
				local cache_boss_cfg = self.boss_rebirth_boss_cfg
				local is_same_boss_cfg = cache_boss_cfg.boss_id == boss_cfg.boss_id and cache_boss_cfg.x == boss_cfg.x and cache_boss_cfg.y == boss_cfg.y
				if not is_same_boss_cfg or (is_same_boss_cfg and (self.last_remind_time + BOSS_REBIRTH_REMIND_INTERVAL) < server_time) then
					need_remind = true
				end
			end

			if need_remind then
				self.boss_rebirth_boss_cfg = boss_cfg
				local rebirth_item, has_num = MainuiWGData.Instance:GetBossRefreshItemIdStuff()
				if rebirth_item then
					if self:TodayIgnoreBossReflushRemind() then
						BossWGCtrl.Instance:DoOperaBossRefresh(rebirth_item)
					else
						if not self:TodayNoTips() and BossWGData.Instance:IsBossQuickRebirthNotCountDown() then
							BossWGCtrl.Instance:OpenBossQuickRebirthShow()
						end
					end
				end
			end
		else
			self.boss_rebirth_boss_cfg = {}
		end
	end
end

function VipBossSceneLogic:TodayIgnoreBossReflushRemind()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local rebirth_reflush_status = PlayerPrefsUtil.GetInt("boss_quick_rebirth_reflush" .. main_role_id)
	return rebirth_reflush_status == 1
end

function VipBossSceneLogic:TodayNoTips()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local has_today_flag = PlayerPrefsUtil.GetInt("boss_quick_rebirth_tips" .. main_role_id) == open_day
	return has_today_flag
end

--- 查找boss
--------------------------------------------------
-- 开始自动寻找BOSS
function VipBossSceneLogic:StartOpenFindBoss()
	-- 4秒检测一次，未开启寻找，自动寻路状态, 第一次进入8秒后开始
	if START_FIND_BOSS_TIME >= 5 then
		START_FIND_BOSS_TIME = 4
	end
	self:StartFindBoss()
end

function VipBossSceneLogic:StartFindBoss()
	local god_war_data = BossGodWarWGData.Instance:GetPrivilegeInfo()
	-- print_error("VipBossSceneLogic:StartFindBoss()", god_war_data)
	if not god_war_data then
		return
	end

	if god_war_data.level == -1 or god_war_data.open_status == 0 or god_war_data.total_count <= 0 then		-- 未激活特权或者未开启或者没有次数
		return
	end

	local state = MainuiWGCtrl.Instance:GetRoleAutoXunluState()
	local is_joysticking = MainuiWGCtrl.Instance:GetView():GetIsJoystick()
	if state == XunLuStatus.XunLu or is_joysticking then			-- 寻路状态和手动操作不能操作
		return
	end

	-- print_error("检测", GuajiCache)
	if GuajiCache.target_obj_id > 0 or GuajiCache.monster_id > 0 or GuajiWGCtrl.Instance:CheckCanPick() then	-- 在战斗中或者在拾取物体中
		return
	end
	
	-- 战神特权查找boss
	local scene_id = Scene.Instance:GetSceneId()
	local list = BossWGData.Instance:GetVipBossListByScene(scene_id)
	local mian_role = Scene.Instance:GetMainRole()
	local pos_x = 0
	local pos_y = 0

	if mian_role ~= nil and not mian_role:IsDeleted() then
		pos_x, pos_y = mian_role:GetLogicPos()
	end

	local dis = 0
	local final_boss_cfg = nil

	for k, boss_cfg in pairs(list) do
		local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(boss_cfg.boss_id)
		if boss_info then
			if (boss_info.next_refresh_time or 0) -  TimeWGCtrl.Instance:GetServerTime() <= 0 then
				-- boss已刷新，寻路过去
				local boss_pos_x = boss_cfg.x_pos
				local boss_pos_y = boss_cfg.y_pos
				local temp_dis = GameMath.GetDistance(boss_pos_x, boss_pos_y, pos_x, pos_y, false) 

				if dis == 0 then
					dis = temp_dis
					final_boss_cfg = boss_cfg
				else
					if temp_dis < dis then
						dis = temp_dis
						final_boss_cfg = boss_cfg
					end
				end
            end
		end
	end

	if final_boss_cfg ~= nil then
		local range = BossWGData.Instance:GetMonsterRangeByid(final_boss_cfg.boss_id)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
			-- 扣除次数
			local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(final_boss_cfg.boss_id)

			if boss_info and (boss_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime() <= 0 and god_war_data.open_status == 1 then
				-- print_error("到达目的地boss存活, 扣除次数")
				BossPrivilegeWGCtrl.Instance:SendBossGodwarPrivilegeReq(ZHANSHENPRIVILEGE_OPERA_TYPE.REDUCE_TIMES)
			end

			self:SetGuaJiInfoPos(final_boss_cfg.x_pos, final_boss_cfg.y_pos, range, final_boss_cfg.boss_id)
			GuajiWGCtrl.Instance:StopGuaji()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
		end)
	
		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = final_boss_cfg.boss_id
		MoveCache.param1 = final_boss_cfg.boss_id

		GuajiWGCtrl.Instance:MoveToPos(final_boss_cfg.scene_id,
				final_boss_cfg.x_pos,
				final_boss_cfg.y_pos,
				range)
	end
end

-- 提示
function VipBossSceneLogic:OnEnterFight()
	MainuiWGCtrl.Instance:SetBossTips(false)
end

function VipBossSceneLogic:OnExitFight()
	MainuiWGCtrl.Instance:SetBossTips(true)
end