--------------------------------
-- 璀璨奇市
--------------------------------
LimitTimeGiftPurchaseView = LimitTimeGiftPurchaseView or BaseClass(SafeBaseView)

function LimitTimeGiftPurchaseView:__init(view_name)
	self:SetMaskBg(false, true)
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Half
	self.default_index = 10
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, "uis/view/limit_time_gift_prefab", "layout_limit_time_gift_purchase_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function LimitTimeGiftPurchaseView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.LimitTimeGift.TitleName
	local bundle, asset = ResPath.GetRawImagesPNG("a3_czjs_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:InitPanel()

	if self.reward_grade_list == nil then
        self.reward_grade_list = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.reward_grade_list:FindObj(string.format("super_purchase_item_%d", i))
            if cell_obj then
                local cell = SuperPurchaseItemRender.New(cell_obj)
                cell:SetIndex(i)
                self.reward_grade_list[i] = cell
            end
        end
    end

	if not self.gift_purchase_list_view then
		self.gift_purchase_list_view = AsyncListView.New(GiftPurchaseListItemRender, self.node_list.left_gift_list_view) -- 天神选择列表
		self.gift_purchase_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectPurchaseGiftHandler, self))
	end

	-- if not self.model_display then
	-- 	self.model_display = OperationActRender.New(self.node_list.display_root)
	-- 	self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	-- end
end

function LimitTimeGiftPurchaseView:ReleaseCallBack()
	if self.close_alert then
		self.close_alert:DeleteMe()
		self.close_alert = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

    if self.reward_grade_list and #self.reward_grade_list > 0 then
		for _, reward_grade_cell in ipairs(self.reward_grade_list) do
			reward_grade_cell:DeleteMe()
			reward_grade_cell = nil
		end

		self.reward_grade_list = nil
	end

	if self.gift_purchase_list_view then
		self.gift_purchase_list_view:DeleteMe()
		self.gift_purchase_list_view = nil
	end

	-- if self.model_display then
	-- 	self.model_display:DeleteMe()
	-- 	self.model_display = nil
	-- end

	self.cur_selected_grade = nil
	self.is_jump_last = nil
end

-- 初始化界面
function LimitTimeGiftPurchaseView:InitPanel()
	self.close_alert = Alert.New()

	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = {
		show_gold = true,
		show_bind_gold = true,
		show_coin = true,
		show_silver_ticket = true,
	}
	self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
end

function LimitTimeGiftPurchaseView:OpenCallBack()
	
end

function LimitTimeGiftPurchaseView:SetJumpStatus()
	self.is_jump_last = true
end

function LimitTimeGiftPurchaseView:OnFlush()
	local show_data_list = LimitTimeGiftWGData.Instance:GetCurrShowDataList()
	if IsEmptyTable(show_data_list) then
		return
	end

	local jump_index = 1
	if self.cur_selected_grade then
		for i, v in ipairs(show_data_list) do
			if v.grade == self.cur_selected_grade then
				jump_index = i
				break
			end
		end
	end

	if self.is_jump_last then
		local index = #show_data_list
		local data = show_data_list[index]
		self.cur_selected_grade = data.grade
		self.is_jump_last = nil
	end

	if self.gift_purchase_list_view then
		self.gift_purchase_list_view:SetDataList(show_data_list)
		self.gift_purchase_list_view:JumpToIndex(jump_index)
	end
end

--[[ 刷新模型啥的
function LimitTimeGiftPurchaseView:FlushModel(chapter_data)
	if not chapter_data then
		return
	end

	local display_data = {}
	if chapter_data["model_show_itemid"] ~= 0 and chapter_data["model_show_itemid"] ~= "" then
		local split_list = string.split(chapter_data["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = chapter_data["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = chapter_data["model_bundle_name"]
	display_data.asset_name = chapter_data["model_asset_name"]
	local model_show_type = tonumber(chapter_data["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	self.model_display:SetData(display_data)
	local scale = chapter_data["display_scale"]
	Transform.SetLocalScaleXYZ(self.node_list["display_root"].transform, scale, scale, scale)

	local pos_x, pos_y = 0, 0
	if chapter_data.display_pos and chapter_data.display_pos ~= "" then
		local pos_list = string.split(chapter_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, pos_x, pos_y)

	if chapter_data.rotation and chapter_data.rotation ~= "" then
		local rotation_tab = string.split(chapter_data.rotation,"|")
		self.node_list.display_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end
]]

function LimitTimeGiftPurchaseView:OnSelectPurchaseGiftHandler(cell, cell_index)
	local data = cell:GetData()
	if IsEmptyTable(data) then
		return
	end

	if self.cur_selected_grade and self.cur_selected_grade ~= data.grade then
		self:ShowSelectAnim()
	end
	self.cur_selected_grade = data.grade

	-- 刷新倒计时
	-- self:FlushTimeCountDown(data.end_time)
	-- 刷新模型
	-- local cfg = LimitTimeGiftWGData.Instance:GetCurrPopupGiftCfg()
	-- self:FlushModel(cfg)
	-- 刷新标签名称
	self.node_list.purchase_type_txt.text.text = data.title_name
	-- 刷新购买数据
	local gift_data = data.gift_data
	local title_img_res = nil

	if gift_data and self.reward_grade_list then   -- 设置三主战位的数据
		for i, item_reward_cell in ipairs(self.reward_grade_list) do
			if item_reward_cell and gift_data[i] then
				item_reward_cell:SetData(gift_data[i])

				if nil == title_img_res and gift_data[i].title_img_res then
					title_img_res = gift_data[i].title_img_res
				end
			end
		end
	end
	
	if nil ~= title_img_res then
		local bundle, asset = ResPath.GetRawImagesPNG(title_img_res)
		if self.node_list.title_img then
			self.node_list.title_img.raw_image:LoadSprite(bundle, asset, function()
				self.node_list.title_img.raw_image:SetNativeSize()
			end)
		end
	end
end

function LimitTimeGiftPurchaseView:ShowSelectAnim()
	UITween.CleanAllTween(GuideModuleName.LimitTimeGiftPurchase)
	UITween.MoveAlphaShowPos(GuideModuleName.LimitTimeGiftPurchase, self.node_list["reward_grade_list"].gameObject, Vector2(360, -1), Vector2(167, -1), 0.8)
end

function LimitTimeGiftPurchaseView:OnClickCloseWindow()
	if self.close_alert and LimitTimeGiftWGData.Instance:HasCanBuyGift() then
		self.close_alert:SetShowCheckBox(true, "LimitTimeGiftPurchaseClose")
		self.close_alert:SetLableString(Language.LimitTimeGift.CloseStr)
		self.close_alert:SetOkFunc(BindTool.Bind(self.Close, self))
		self.close_alert:SetOkString(Language.LimitTimeGift.CloseBtnSure)
		self.close_alert:SetCancelString(Language.LimitTimeGift.CloseBtnCancel)
		self.close_alert:Open()
		return
	end
	self:Close()
end


------------------------------------------------------------------------------
-- SuperPurchaseItemRender
------------------------------------------------------------------------------
SuperPurchaseItemRender = SuperPurchaseItemRender or BaseClass(BaseRender)

function SuperPurchaseItemRender:__delete()
    if self.item_reward_list then
        for k, v in pairs(self.item_reward_list) do
            v:DeleteMe()
        end
        self.item_reward_list = nil
    end

	self.item_reward_list_root = nil
end

function SuperPurchaseItemRender:LoadCallBack()
    if self.item_reward_list == nil then
        self.item_reward_list = {}
		self.item_reward_list_root = {}

        for i = 1, 5 do
			local cell_obj = self.node_list.reward_list:FindObj(string.format("reward_pos_%d", i))
			if cell_obj then
				local cell = ItemCell.New(cell_obj)
				cell:SetIndex(i)
				self.item_reward_list[i] = cell
				self.item_reward_list_root[i] = cell_obj
			end
        end
    end

	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnBuyOnClick, self))
end

function SuperPurchaseItemRender:OnBuyOnClick()
	if not self.data then return end
	-- 发送协议
	RechargeWGCtrl.Instance:Recharge(self.data.RMB, self.data.rmb_type, self.data.rmb_seq)
end

function SuperPurchaseItemRender:OnFlush()
    if not self.data then return end
	
	self.node_list.title_name.text.text = self.data.title_name
	self.node_list.discount_txt.text.text = string.format("%s\n%s", self.data.discount, Language.MustBuy.Discount)
	self.node_list.buy_text.text.text = self.data.price

	self.node_list.is_buy:CustomSetActive(self.data.is_buy)
	self.node_list.btn_root:CustomSetActive(not self.data.is_buy)

    local item_list = self.data.item_list
    if item_list and self.item_reward_list then
        for i, item_reward_cell in ipairs(self.item_reward_list) do
            if item_reward_cell then
				if item_list[i] then
					self:SetRewardRootVisible(true, i)
					item_reward_cell:SetData(item_list[i])
				else
					self:SetRewardRootVisible(false, i)
				end
            end
        end
    end
end

function SuperPurchaseItemRender:SetRewardRootVisible(Visible, index)
	if self.item_reward_list_root and self.item_reward_list_root[index] then
		self.item_reward_list_root[index]:CustomSetActive(Visible)
	end
end

------------------------------------
-- 礼包选择Item
------------------------------------
GiftPurchaseListItemRender = GiftPurchaseListItemRender or BaseClass(BaseRender)

function GiftPurchaseListItemRender:OnFlush()
	if not self.data then return end
	
	self.node_list["Text"].text.text = self.data.title_name
	self.node_list["TextHL"].text.text = self.data.title_name

	self:FlushTimeCountDown(self.data.end_time)
end

function GiftPurchaseListItemRender:ReleaseCallBack()
	self:CleanTimer()
end

function GiftPurchaseListItemRender:OnSelectChange(is_select)
	self.node_list["HLImage"]:SetActive(is_select)
end

function GiftPurchaseListItemRender:CleanTimer()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
    end
end

function GiftPurchaseListItemRender:FlushTimeCountDown(end_time)
	self:CleanTimer()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local total_time = end_time - server_time
	if total_time <= 0 then
		return
	end

	local color = total_time <= self.data.remind_time and COLOR3B.RED or nil
	self:SetTimeText(TimeUtil.FormatSecondDHM6(total_time), color)
    self.timer = CountDown.Instance:AddCountDown(total_time, 1, BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self))
end

function GiftPurchaseListItemRender:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	local color = valid_time <= self.data.remind_time and COLOR3B.RED or nil
	if valid_time > 0 then
		self:SetTimeText(TimeUtil.FormatSecondDHM6(valid_time), color)
	end
end

function GiftPurchaseListItemRender:OnComplete()
    self:SetTimeText("")
	LimitTimeGiftWGCtrl.Instance:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_INFO)
end

function GiftPurchaseListItemRender:SetTimeText(str, color)
	if color ~= nil then
		str = ToColorStr(str, color)
	end

	self.node_list["time_down"].text.text = str
	self.node_list["time_down_hl"].text.text = str
end