WuHunTowerWGData = WuHunTowerWGData or BaseClass()

WUHUN_TOWER_TYPE = {
    NORMAL = 0,     -- 普通副本
    HELL = 1,       -- 地狱副本
}

function WuHunTowerWGData:__init()
	if WuHunTowerWGData.Instance ~= nil then
		error("[WuHunTowerWGData] attempt to create singleton twice!")
		return
	end

	WuHunTowerWGData.Instance = self
	self:InitConfig()
	self:InitParam()

	-- 红点注册
	RemindManager.Instance:Register(RemindName.WuHunTower, BindTool.Bind(self.GetWuHunTowerRed,self))
end

function WuHunTowerWGData:__delete()
	self.wuhun_tower_monster_model_cfg = nil
	WuHunTowerWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.WuHunTower)
end

-- 初始化配置
function WuHunTowerWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("fb_wuhun_tower_auto")

	if cfg then
		self.wuhun_tower_base_cfg = cfg.other[1]
		self.wuhun_tower_monster_cfg = ListToMap(cfg.monster, "type", "seq")
		self.wuhun_tower_rank_reward_cfg = ListToMapList(cfg.rank_reward, "type")
	end
end

-- 默认参数
function WuHunTowerWGData:InitParam()
	self.wuhun_tower_monster_model_cfg = {}
	
	for monster_type, monster_cfg in pairs(self.wuhun_tower_monster_cfg) do
		self.wuhun_tower_monster_model_cfg[monster_type] = {}
		for seq, cfg_data in pairs(monster_cfg) do
			local model_cfg = {}
			local str_list = Split(cfg_data.display_pos, "|")
			model_cfg.pos_x = tonumber(str_list[1]) or 0
			model_cfg.pos_y = tonumber(str_list[2]) or 0
			model_cfg.display_scale = cfg_data.display_scale
			self.wuhun_tower_monster_model_cfg[monster_type][seq] = model_cfg
		end
	end
end

-- 获取基础信息表
function WuHunTowerWGData:GetTowerBaseCfg()
	return self.wuhun_tower_base_cfg
end

-- 获取开启等级
function WuHunTowerWGData:GetTowerBaseCfg()
	local emtry = {}
	return (self.wuhun_tower_base_cfg or emtry)["open_level"] or nil
end

-- 获取基础表属性
function WuHunTowerWGData:GetTowerBaseCfgParam(param_str)
	local emtry = {}
	return (self.wuhun_tower_base_cfg or emtry)[param_str] or nil
end

-- 获取关卡怪物配置
function WuHunTowerWGData:GetlevelMonsterCfgByTypeSeq(tower_type, monster_seq)
	local emtry = {}
	return ((self.wuhun_tower_monster_cfg or emtry)[tower_type] or emtry)[monster_seq] or nil
end

-- 获取关卡怪物位置大小配置
function WuHunTowerWGData:GetlevelMonsterModelCfgByTypeSeq(tower_type, monster_seq)
	local emtry = {}
	return ((self.wuhun_tower_monster_model_cfg or emtry)[tower_type] or emtry)[monster_seq] or nil
end

-- 获取关卡怪物列表
function WuHunTowerWGData:GetlevelMonsterCfgByType(tower_type)
	local emtry = {}
	return (self.wuhun_tower_monster_cfg or emtry)[tower_type] or nil
end

-- 获取关卡列表（只拿取4个）(少于2个拿取2个)
-- 这个方法依据当前的进度读取 show_offset 个列表
-- 如果只剩下4个从倒数第四个开始读
-- 如果只剩下2个从倒数第二个开始读
-- 如果当前进度达到最大则从倒数第二个开始读，并且取最后一个展示并锁住不给操作
function WuHunTowerWGData:GetLevelCfgList(tower_type, tower_level, show_offset)
	local type_list = self:GetlevelMonsterCfgByType(tower_type)
	if (not type_list) or (#type_list <= 0) then
		return nil, 1
	end

	local offset = #type_list - tower_level
	local is_full = offset < 0

	local is_cur_un_lock = true
	if tower_type == WUHUN_TOWER_TYPE.HELL then
		local item_info = WuHunTowerWGData.Instance:GetCurrTowerItemInfo(WUHUN_TOWER_TYPE.NORMAL)
		local normal_level = item_info and item_info.pass_seq or 0
		is_cur_un_lock = (normal_level >= (tower_level * 10))  
	end
	
	local start_index = tower_level
	local curr_data = nil
	if offset <= show_offset or is_full then		-- 小于0时表示达到最大值
		start_index = #type_list - show_offset
		
		if offset <= 2 then
			start_index = #type_list - 1
		end

		-- 达到最大数量取最后一个
		if is_full then
			tower_level = tower_level - 1
		end
	end

	local return_table = {}
	local index = 0
	for i = start_index, #type_list do
		if type_list[i] then
			local stage_table = {}
			stage_table.cfg_data = type_list[i]
			stage_table.is_full = is_full

			if type_list[i].seq == tower_level then
				stage_table.is_cur = true and is_cur_un_lock
				curr_data = stage_table
			else
				stage_table.is_cur = false
			end
			table.insert(return_table, stage_table)
		end

		index = index + 1
		if index >= show_offset then
			break
		end
	end

	return return_table, curr_data
end

-- 获取当前关的奖励列表
function WuHunTowerWGData:GetCurrLevelRewardCfg(tower_type, tower_level)
	local type_list = self:GetlevelMonsterCfgByType(tower_type)
	if (not type_list) or (#type_list <= 0) then
		return nil
	end

	--- 找到最近的10
	local ten_data = nil
	local is_max = tower_level > #type_list
	local real_index = is_max and tower_level - 1 or tower_level
	local one_data = type_list[real_index]
	local start_index = real_index
	for i = start_index, #type_list do
		if type_list[i] and (i % 10 == 0) then
			ten_data = type_list[i]
			break
		end
	end

	return one_data, ten_data, is_max
end

--获取当前能达到的最高层数
function WuHunTowerWGData:GetCurCanQuickFinishLevel(tower_type, tower_level)
	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local temp_level = tower_level
	local level_limit = 0
	local need_need_cap = 0
	local is_max = false

	while(true) do
		if not role_cap then
			break
		end

		temp_level = temp_level + 1
		local cfg = self:GetlevelMonsterCfgByTypeSeq(tower_type, temp_level)
		if not cfg then
			is_max = true
			temp_level = temp_level - 1
			break
		end

		if cfg.need_cap > role_cap  then
			temp_level = temp_level - 1
			need_need_cap = cfg.need_cap
			break
		end

		if role_lv < cfg.need_level then
			temp_level = temp_level - 1
			level_limit = cfg.need_level
			break
		end

		if temp_level - tower_level >= 200 then
			break
		end
	end

	return temp_level, level_limit, need_need_cap, is_max
end

-- 获取排行奖励列表
function WuHunTowerWGData:GetRankRewardListCfg(tower_type)
	local emtry = {}
	return (self.wuhun_tower_rank_reward_cfg or emtry)[tower_type] or nil
end

-- 获取当前排行对应配置
function WuHunTowerWGData:GetRankRewardCfgByTypeRank(tower_type, rank)
	local rank_reward_list = self:GetRankRewardListCfg(tower_type)
	if not rank_reward_list then
		return nil
	end

	for _, reward_data in pairs(rank_reward_list) do
		if rank >= reward_data.min_rank and rank <= reward_data.max_rank then
			return reward_data
		end
	end

	return nil
end

-- 获取奖励列表(超过100取10层的奖励，没超过100取每层的奖励)
function WuHunTowerWGData:GetJumpLevelRewardList(tower_type, tower_seq, tower_start_seq)
	local type_list = self:GetlevelMonsterCfgByType(tower_type)
	if (not type_list) or (#type_list <= 0) then
		return {}
	end

	local offset = tower_seq - tower_start_seq
	local is_ten_offset = false

	if offset >= 100 then
		is_ten_offset = true
	end

	if offset < 0 then
		return {}
	end

	local reward_list = {}

	for i = tower_start_seq, tower_seq do
		if type_list[i] then
			if is_ten_offset then
				if i % 10 == 0 then
					self:ComposeRewardList(reward_list, type_list[i])
				end
			else
				self:ComposeRewardList(reward_list, type_list[i])
			end
		end
	end

	local return_reward_list = {}
	for _, reward_data in pairs(reward_list) do
		if reward_data and reward_data.item_id then
			table.insert(return_reward_list, reward_data)
		end
	end

	return return_reward_list
end

-- 组装奖励
function WuHunTowerWGData:ComposeRewardList(reward_list, cfg)
	if not reward_list then
		reward_list = {}
	end

	if cfg and cfg.pass_reward_item then
		for _, reward_data in pairs(cfg.pass_reward_item) do
			if reward_data and reward_data.item_id then
				if reward_list[reward_data.item_id] then
					reward_list[reward_data.item_id].num = reward_list[reward_data.item_id].num + reward_data.num 
				else
					reward_list[reward_data.item_id] = {}
					reward_list[reward_data.item_id].item_id = reward_data.item_id
					reward_list[reward_data.item_id].num = reward_data.num
					reward_list[reward_data.item_id].is_bind = reward_data.is_bind
				end
			end
		end
	end
end

---- 服务器相关数据-------------------
-- 设置服务器基本数据
function WuHunTowerWGData:SetTowerItemInfo(protocol)
	for index, item_data in ipairs(protocol.item_list) do
		local tower_type = index - 1
		self:UpdateTowerItemInfo(tower_type, item_data)
	end
end

-- 刷新基本数据
function WuHunTowerWGData:UpdateTowerItemInfo(tower_type, tower_item_data)
	if self.tower_info == nil then
		self.tower_info = {}
	end

	self.tower_info[tower_type] = tower_item_data
end

-- 获取当前副本信息
function WuHunTowerWGData:GetCurrTowerItemInfo(tower_type)
	local emtry = {}
	return (self.tower_info or emtry)[tower_type] or nil
end

-- 获取排行榜数据
function WuHunTowerWGData:SetTowerRankInfo(protocol)
	if self.rank_info == nil then
		self.rank_info = {}
	end

	if self.my_rank_info == nil then
		self.my_rank_info = {}
	end

	self.rank_info[protocol.tower_type] = protocol.rank_item_list
	local uid = RoleWGData.Instance:GetUUid().temp_low

	for _, rank_data in ipairs(protocol.rank_item_list) do
		if rank_data and rank_data.uid == uid then
			self.my_rank_info[protocol.tower_type] = rank_data
			break
		end
	end
end

-- 获取排行榜数据（数据列表）
function WuHunTowerWGData:GetTowerRankInfo(tower_type)
	local emtry = {}
	return (self.rank_info or emtry)[tower_type] or nil
end

-- 获取我的排行榜数据（单个数据）
function WuHunTowerWGData:GetTowerMyRankInfo(tower_type)
	local emtry = {}
	return (self.my_rank_info or emtry)[tower_type] or nil
end

-- 设置场景数据
function WuHunTowerWGData:SetTowerSceneInfo(protocol)
	if self.scene_info == nil then
		self.scene_info = {}
	end

	self.scene_info = protocol
end

-- 获取场景数据
function WuHunTowerWGData:GetTowerSceneInfo()
	return self.scene_info
end

---- 自身方法-------------------
-- 武魂塔红点
function WuHunTowerWGData:GetWuHunTowerRed()
	if self:GetTowerTypeRed(WUHUN_TOWER_TYPE.NORMAL) > 0 or self:GetTowerTypeRed(WUHUN_TOWER_TYPE.HELL) > 0 then
		return 1
	end

	return 0
end

-- 获取红点信息
function WuHunTowerWGData:GetTowerTypeRed(tower_type)
	local item_info = self:GetCurrTowerItemInfo(tower_type)
	local pass_seq = item_info and item_info.pass_seq or -1

	if pass_seq == -1 then
		return 0
	end

	pass_seq = pass_seq + 1
 	local cfg_data = self:GetlevelMonsterCfgByTypeSeq(tower_type, pass_seq)
	local is_cur_un_lock = true

	if not cfg_data then
		return 0
	end

	if tower_type == WUHUN_TOWER_TYPE.HELL then
		local item_info = WuHunTowerWGData.Instance:GetCurrTowerItemInfo(WUHUN_TOWER_TYPE.NORMAL)
		local normal_pass_seq = item_info and item_info.pass_seq or 0
		is_cur_un_lock = (normal_pass_seq >= (pass_seq * 10))  
	end

	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
    local role_lv = RoleWGData.Instance:GetRoleLevel()

	if role_lv >= cfg_data.need_level and role_cap >= cfg_data.need_cap and is_cur_un_lock then
		return 1
	end

	return 0
end

