-- /jy_gm gmtbs:101 100 0 0 0
function BillionSubsidyView:MRSYLoadCallBack()
    if not self.mrsy_shop_list then
        self.mrsy_shop_list = AsyncBaseGrid.New()
        self.mrsy_shop_list:CreateCells(
            {col = 4,
            change_cells_num = 1,
            complement_col_item = true,
            list_view = self.node_list.mrsy_shop_list,
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
            assetName = "try_ticket_shop_item",
            itemRender = MRSYShopItem}
        )
    end
end

function BillionSubsidyView:MRSYReleaseCallBack()
    if self.mrsy_shop_list then
        self.mrsy_shop_list:DeleteMe()
        self.mrsy_shop_list = nil
    end
end

function BillionSubsidyView:MRSYOnFlush()
    local try_use_num = BillionSubsidyWGData.Instance:GetTryUseNum()
    self.node_list.mrsy_try_ticket_num.text.text = string.format(Language.BillionSubsidy.TryTicketNum, try_use_num)
    local shop_cfg = BillionSubsidyWGData.Instance:GetMRSYShopItemGradeCfg()
    if not shop_cfg then
        return
    end

    self.mrsy_shop_list:SetDataList(shop_cfg)
    self.node_list.mrsy_shop_list_scroll_bar:CustomSetActive(#shop_cfg > 7)
end

------------------------------- MRSYShopItem 商品item
MRSYShopItem = MRSYShopItem or BaseClass(BaseRender)
function MRSYShopItem:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
    end

    XUI.AddClickEventListener(self.node_list.use_btn, BindTool.Bind(self.OnClickUseBtn, self))
end

function MRSYShopItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function MRSYShopItem:OnFlush()
    local is_hide = IsEmptyTable(self.data)
    self.node_list.root:CustomSetActive(not is_hide)
    if is_hide then
        return
    end

    local item_data = self.data.reward[0]
    self.item_cell:SetData(item_data)
    self.node_list.shop_name.text.text = ItemWGData.Instance:GetItemName(item_data.item_id)

    local fake_price_str = RoleWGData.GetPayMoneyStr(self.data.fake_price)
    self.node_list.fake_price.text.text = string.format(Language.BillionSubsidy.TryUseItemPrice, fake_price_str)

    local use_count = BillionSubsidyWGData.Instance:GetTryTicketShopDataBySeq(self.data.item_seq)
    local is_use = use_count > 0
    local btn_str_index = is_use and 2 or 1
    self.node_list.use_btn_txt.text.text = Language.BillionSubsidy.TryUseTicketBtnStr[btn_str_index]
    XUI.SetButtonEnabled(self.node_list.use_btn, not is_use)
end

function MRSYShopItem:OnClickUseBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local member_level_cfg = BillionSubsidyWGData.Instance:GetMemberLevelCfg()
    if not member_level_cfg then
        return
    end

    local try_use_count = BillionSubsidyWGData.Instance:GetTryUseCount()                -- 已使用试用券次数
    local try_use_num = BillionSubsidyWGData.Instance:GetTryUseNum()                    -- 试用券次数
    if (member_level_cfg.daily_use_try_ticket_limit <= 0) or (try_use_count >= member_level_cfg.daily_use_try_ticket_limit) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.TryUseTicketNotUseCount)
        return
    elseif (member_level_cfg.daily_use_try_ticket_limit - try_use_count < self.data.cost) or try_use_num < self.data.cost then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.TryUseTicketNumNotEnough)
        return
    end

	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.USE_TRY_TICKET, self.data.item_seq)
end