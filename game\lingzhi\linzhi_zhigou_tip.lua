LingZhiZhiGouTips = LingZhiZhiGouTips or BaseClass(SafeBaseView)

local LingZhiShowT = {
    EFFECT_NAME = {
        [0] = "UI_qihun_yuyi",
        [1] = "UI_qihun_fabao",
        [2] = "UI_qihun_jianling",
        [3] = "UI_qihun_shenbing",
    },
    TITLE_NAME = {
        [0] = "仙翼注灵",
        [1] = "灵宝注灵",
        [2] = "背饰注灵",
        [3] = "神武注灵",
    },
    TIP_UI = {
        [0] = "a1_xyzr_biaodian",
        [1] = "a1_lbzr_biaodian",
        [2] = "a1_bszl_biaodi", 
        [3] = "a1_swzr_biaodian",
    },
    TITLE_UI = {
        [0] = "a1_xyzr_xy",
        [1] = "a1_lbzr_lb",
        [2] = "a1_bszl_baoshi",
        [3] = "a1_swzr_sw",
    },
    BG_IMG = {
        [0] = "a1_xyzr_dadi",
        [1] = "a1_lbzr_dadi",
        [2] = "a1_bszl_dadi",
        [3] = "a1_swzr_dadi",
    }
}

function LingZhiZhiGouTips:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/lingzhi_prefab", "layout_skill_qh_zhigou_view")
end

function LingZhiZhiGouTips:ReleaseCallBack()
    -- 倒计时
    if CountDownManager.Instance:HasCountDown("LingZhiZhiGouTips") then
        CountDownManager.Instance:RemoveCountDown("LingZhiZhiGouTips")
    end

    if self.attr_list_view then
        self.attr_list_view:DeleteMe()
        self.attr_list_view = nil
    end
end

function LingZhiZhiGouTips:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.skill_cell_0, BindTool.Bind(self.OnClickBaseSkill, self))
    XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.OnClickBtn, self))
    
    for i=1,8 do
        XUI.AddClickEventListener(self.node_list["skill_icon_" .. i], BindTool.Bind(self.OnClickSkill, self, i))
    end

    if not self.attr_list_view then
        self.attr_list_view = AsyncListView.New(LingZhiZhiGouAttrItem, self.node_list["attr_list"])
    end
end

function LingZhiZhiGouTips:ShowIndexCallBack()
    -- self:ViewAnimation()
end

function LingZhiZhiGouTips:OnClickBtn()
    if not self.data then
        return
    end
    
    local data = self.data
    local lingzhi_type = data.qihun_type
    data = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
    -- 是否购买
    local is_buy = data.is_buy == 1

    if is_buy then
        LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_OPERA_TYPE_ACTIVE,
            lingzhi_type)
        return
    end
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local discount_end_timestamp = data.discount_end_timestamp
    local need_discount = discount_end_timestamp > server_time

    local price_cfg = LingZhiSkillWGData.Instance:GetPriceCfg(lingzhi_type)
    local money = need_discount and price_cfg.price or price_cfg.affter_price
    local recharge_type = price_cfg.buy_flag
    local name = Language.LingZhi.NameList2[lingzhi_type]

    RechargeWGCtrl.Instance:Recharge(money, recharge_type, lingzhi_type)
end

function LingZhiZhiGouTips:OnClickBaseSkill()
    if not self.data then
        return
    end

    local data = self.data
    local lingzhi_type = data.qihun_type
    LingZhiWGCtrl.Instance:OpenLingZhiSkillTip({
        lingzhi_type = lingzhi_type,
        view_type = LingZhiSkillTips.ViewType.BaseSkill,
    })
end

function LingZhiZhiGouTips:OnClickSkill(index)
   if not self.data then
        return
    end

    local data = self.data
    local lingzhi_type = data.qihun_type
    local skill_index = index - 1
    local max_level = LingZhiSkillWGData.Instance:GetFuWenLevelMax(lingzhi_type, skill_index)
    local max_cfg = LingZhiSkillWGData.Instance:GetFuWenCfg(lingzhi_type, skill_index, max_level)
    LingZhiWGCtrl.Instance:OpenLingZhiSkillTip({
        lingzhi_type = lingzhi_type,
        max_level = max_level,
        max_cfg = max_cfg,
        view_type = LingZhiSkillTips.ViewType.MaxLevel,
    }) 
end

function LingZhiZhiGouTips:SetData(data)
    self.data = data
    self:Open()
    self:Flush()
end

function LingZhiZhiGouTips:OnFlush()
    if not self.data then
        return
    end

    local lingzhi_type = self.data.qihun_type
    local xiulian_cfg = LingZhiSkillWGData.Instance:GetXiuLianCfg(lingzhi_type, 1)
    local xiulian_attr = AttributeMgr.GetAttributteByClass(xiulian_cfg)
    local data = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
    local other_cfg = LingZhiSkillWGData.Instance:GetOtherCfg()
    local base_skill_cfg = LingZhiSkillWGData.Instance:GetBaseSkillCfg(lingzhi_type)
    local skill_power = other_cfg.power or 0
    skill_power = skill_power + AttributeMgr.GetCapability(nil, base_skill_cfg) + AttributeMgr.GetCapability(xiulian_attr)
    self.node_list.xl_power_num.text.text = skill_power

    local discount_end_timestamp = data.discount_end_timestamp
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    -- 是否需要折扣
    local need_discount = discount_end_timestamp > server_time
    local price_cfg = LingZhiSkillWGData.Instance:GetPriceCfg(lingzhi_type)
    self.node_list.skill_name.text.text = base_skill_cfg.skill_name or ''
    self.node_list.skill_dec.text.text = Language.LingZhi.Title3 .. (base_skill_cfg.zg_dec or '')
    self.node_list.sj_base_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(base_skill_cfg.skill_icon))

    self.node_list.RawImage.raw_image:LoadSprite(ResPath.GetRawImagesPNG(LingZhiShowT.BG_IMG[lingzhi_type]))
    self.node_list.title_img.image:LoadSprite(ResPath.GetLingZhiImg(LingZhiShowT.TITLE_UI[lingzhi_type]))
    self.node_list.skill_cell_tip_img.image:LoadSprite(ResPath.GetLingZhiImg(LingZhiShowT.TIP_UI[lingzhi_type]))
    self.node_list.skill_list_tip_img.image:LoadSprite(ResPath.GetLingZhiImg(LingZhiShowT.TIP_UI[lingzhi_type]))
    self.node_list.attr_list_tip_img.image:LoadSprite(ResPath.GetLingZhiImg(LingZhiShowT.TIP_UI[lingzhi_type]))

    local name = "a1_lz_skill_icon_high_"
    for i=1,8 do
        local max_level = LingZhiSkillWGData.Instance:GetFuWenLevelMax(lingzhi_type, i-1)
        local fuwen_cfg = LingZhiSkillWGData.Instance:GetFuWenCfg(lingzhi_type, i-1, max_level)
        local skill_icon_id = fuwen_cfg and fuwen_cfg.skill_icon_id or 1
        self.node_list['skill_icon_' .. i].image:LoadSprite(ResPath.GetLingZhiImg(name .. skill_icon_id))
    end
    local is_buy = data.is_buy == 1
    -- 倒计时
    if CountDownManager.Instance:HasCountDown("LingZhiZhiGouTips") then
        CountDownManager.Instance:RemoveCountDown("LingZhiZhiGouTips")
    end

    if need_discount and not is_buy then
        local end_time = discount_end_timestamp
        CountDownManager.Instance:AddCountDown("LingZhiZhiGouTips", BindTool.Bind(self.UpdateTimer, self),
            BindTool.Bind(self.CompleteTime, self), end_time, nil, 0.3)
    else
        self.node_list.time_str.text.text = ''
    end
    
    self.node_list.btn_discount_line:SetActive(not is_buy)
    -- self.node_list.btn_discount_bg:SetActive(not is_buy)
    -- if need_discount then
    --     self.node_list.btn_discount_bg_text.text.text = price_cfg.discount_text
    -- else
    --     self.node_list.btn_discount_bg_text.text.text = price_cfg.affter_text
    -- end

    local price = price_cfg.init_price
    local discount_price = 0
    if need_discount then
        discount_price = price_cfg.price
    else
        discount_price = price_cfg.affter_price
    end
    
    self.node_list.btn:SetActive(data.is_open ~= 1)

    if data.is_open ~= 1 and data.is_buy == 1 then
        self.node_list["btn_discount_price"].text.text = Language.LingZhi.BtnName_3
    else
        -- self.node_list.btn_txt.text.text = string.format(Language.LingZhi.RMBStr,price)
        local price_cfg = LingZhiSkillWGData.Instance:GetPriceCfg(lingzhi_type)
        local recharge_type = price_cfg.buy_flag
        self.node_list["btn_discount_price"].text.text = RoleWGData.GetPayMoneyStr(discount_price, recharge_type, lingzhi_type)
        --self.node_list["btn_discount_price"].text.text = string.format(Language.LingZhi.RMBStr,discount_price)
    end

    -- self.node_list.skill_title_img.image:LoadSprite(ResPath.GetLingZhiImg("lz_zg_title_" .. lingzhi_type))
    -- self.node_list.skill_txt_img.image:LoadSprite(ResPath.GetLingZhiImg("lz_zg_text_" .. lingzhi_type))
    self.node_list["skill_title"].text.text = LingZhiShowT.TITLE_NAME[lingzhi_type]
    -- local bundle, asset = ResPath.GetF2RawImagesPNG("lingzhi_zhigou_bg_" .. lingzhi_type)
    -- self.node_list["skill_raw_img"].raw_image:LoadSprite(bundle, asset,function ()
    --     self.node_list["skill_raw_img"].raw_image:SetNativeSize()
    -- end)
    --self.node_list.skill_raw_img_eff:ChangeAsset(ResPath.GetUIEffect(LingZhiShowT.EFFECT_NAME[lingzhi_type]))

    local skill_desc = Language.LingZhi.ZhiGouSkill[lingzhi_type]
    -- for i=1,7 do
    --     self.node_list["skill_txt"..i].text.text = skill_desc and skill_desc[i] or ""
    -- end
    
    local sort_attribute = AttributeMgr.SortAttribute()
    -- local attr_str8 = ""
    -- local attr_str9 = ""
    local attr_list = {}
    local index = 1
    for k,v in pairs(sort_attribute) do
        if xiulian_attr[v] and xiulian_attr[v] > 0 then
            attr_list[index] = {}
            attr_list[index].attr_str = v
            attr_list[index].attr_value = xiulian_attr[v]
            index = index + 1
        end
    end

    self.attr_list_view:SetDataList(attr_list)

    -- self.node_list["skill_txt8"].text.text = attr_str8
    -- self.node_list["skill_txt9"].text.text = attr_str9
    self.node_list.btn_red_1:SetActive(data.is_buy == 1 and data.is_open ~= 1)
end

function LingZhiZhiGouTips:UpdateTimer(elapse_time, total_time)
    local str
    local time = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
    str = string.format(Language.LingZhi.Tip6, time)

    if self.node_list["time_str"] then
        self.node_list["time_str"].text.text = str
    end
end

function LingZhiZhiGouTips:CompleteTime()
    AddDelayCall(self, function()
        self:Flush()
    end,0.3)
end

function LingZhiZhiGouTips:ViewAnimation()
    UITween.CleanAllTween(GuideModuleName.LingZhiZhiGouTips)

    local lb_start_pos = u3dpool.vec3(-1130, -98, 0)
    local lb_end_pos = u3dpool.vec3(-256, -98, 0)
    UITween.MoveAlphaShowPos(GuideModuleName.LingZhiZhiGouTips,self.node_list["left_buttom"],lb_start_pos,lb_end_pos)

    local right_start_pos = u3dpool.vec3(1060, 7, 0)
    local right_end_pos = u3dpool.vec3(244, 7, 0)
    UITween.MoveAlphaShowPos(GuideModuleName.LingZhiZhiGouTips,self.node_list["right_panel"],right_start_pos,right_end_pos)
end


------------------------------ LingZhiZhiGouAttrItem ------------------------------

LingZhiZhiGouAttrItem = LingZhiZhiGouAttrItem or BaseClass(CommonAddAttrRender)

function LingZhiZhiGouAttrItem:__init()
    CommonAddAttrRender.__init(self)
end

function LingZhiZhiGouAttrItem:__delete()
    CommonAddAttrRender.__delete(self)
end

function LingZhiZhiGouAttrItem:LoadCallBack()
    self:KillArrowTween()
end

function LingZhiZhiGouAttrItem:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
    self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
    self.node_list.attr_value.text.text = value_str .. per_desc

    self.view:SetActive(true)
end