FuBenStarClock = FuBenStarClock or BaseClass(BaseRender)

function FuBenStarClock:__init()
end

function FuBenStarClock:LoadCallBack()
	self.old_star_num = -1
end

function FuBenStarClock:ReleaseCallBack()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest) 
		self.timer_quest = nil
	end

	if self.change_state_anim then
        self.change_state_anim:Kill()
        self.change_state_anim = nil
    end

	self.old_star_num = -1
end

function FuBenStarClock:SetData(data)
	self.data = data or {}
end

function FuBenStarClock:OnFlush()
    if IsEmptyTable(self.data) then
    	return
    end

    local scene_info = FuBenWGData.Instance:GetStarViewFbSceneInfo()
	if IsEmptyTable(scene_info) then
		return
	end
	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	if scene_info.all_monster_dead then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	self.begin_time = scene_info.flush_timestamp
	self.over_time = scene_info.time_out_stamp
	self.next_star_timestamp = scene_info.next_star_timestamp

	if self.timer_quest == nil then
		self:UpdateTime()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateTime, self), 1)
	end
end

function FuBenStarClock:UpdateTime()
	local scene_info = FuBenWGData.Instance:GetStarViewFbSceneInfo()
	if nil == scene_info then
		return
	end

	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	if scene_info.all_monster_dead then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()

	-- 刷怪到现在的时间(总秒数)
	local pass_time = server_time - self.begin_time
	local star = self:GetStar()

	FuBenPanelWGData.Instance:SetFubenStarNum(star)

	local is_show_anim = false
	if star ~= self.old_star_num then
		self.old_star_num = star
		is_show_anim = true
	end

	local star_res = GetSpecialStarImgResByStar5(star)
	local bundle, asset = ResPath.GetCommonImages(star_res)
    self.node_list.level_img.image:LoadSprite(bundle, asset, function()
        self.node_list.level_img.image:SetNativeSize()
    end)

	local eff_bundle, eff_asset = ResPath.GetStarLevelUIEffect(star)
    self.node_list.level_effct:ChangeAsset(eff_bundle, eff_asset)

	if is_show_anim then
		self:ChangeStarAnim()
	end

	local cur_star_total_time = self.data["time" .. star]
	if cur_star_total_time then
		--当前经过的星星时间
		local star_time = self:GetTime(star)
		--当前星星的经过时间
		local cur_pass_time = pass_time - star_time

		local time_value = 0
		if self.next_star_timestamp and self.next_star_timestamp > 0 and self.next_star_timestamp - server_time > 0 then
			time_value = self.next_star_timestamp - server_time
		else
			time_value = cur_star_total_time - cur_pass_time
		end

		time_value = time_value <= 0 and 0 or time_value
		
		if time_value > 0 then
			if star > 0 then
				self.node_list["per_boss_tip"].text.text = string.format(Language.FuBenPanel.CommonFBDropDesc2, time_value, Language.Common.StarLevelStr[star])
			else
				self.node_list["per_boss_tip"].text.text = string.format(Language.FuBenPanel.CommonFBDropDesc3, time_value)
			end
		else
			self.node_list["per_boss_tip"].text.text = ""
		end
	end

	self.node_list.pass_time.text.text = TimeUtil.FormatSecond(pass_time, 3) 

	self:UpdateStr(star)
end

function FuBenStarClock:GetTime(star)
	local total_time = 0
	for i = 0, 3 do
		if star < i then
			total_time = total_time + self.data["time" .. i]
		end
	end
	return total_time
end

function FuBenStarClock:UpdateStr(star)
	local per = self.data["per" .. star]
	if per then
		self.node_list.per_boss_tip_1.text.text = string.format(Language.Boss.CurDropPro,per)
	end
end

function FuBenStarClock:GetStar()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local pass_time = server_time - self.begin_time
	local star = 3
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
		star = scene_info.cur_star_num
	elseif scene_type == SceneType.COPPER_FB then
		local scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
		star = scene_info.cur_star_num
	elseif scene_type == SceneType.FakePetFb or scene_type == SceneType.PET_FB then
		local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
		if pet_scence_info and not IsEmptyTable(pet_scence_info) then
			star = pet_scence_info.cur_star_num
		end
	elseif scene_type == SceneType.TIAN_SHEN_FB then
		if pass_time < self.data.time3 then
			star = 3
		elseif pass_time < self.data.time2 + self.data.time3 then
			star = 2
		elseif pass_time < self.data.time1 + self.data.time2 + self.data.time3 then
			star = 1
		elseif pass_time < self.data.time0 + self.data.time1 + self.data.time2 + self.data.time3 then
			star = 0
		end
	else
		if pass_time < self.data.time3 then
			star = 3
		elseif pass_time < self.data.time2 + self.data.time3 then
			star = 2
		elseif pass_time < self.data.time1 + self.data.time2 + self.data.time3 then
			star = 1
		elseif self.data.time0 and pass_time < self.data.time0 + self.data.time1 + self.data.time2 + self.data.time3 then
			star = 0
		else
			star = 0
		end
	end
	return star
end

function FuBenStarClock:ChangeStarAnim()
	if self.change_state_anim then
		self.change_state_anim:Kill()
		self.change_state_anim = nil
	end
	
	self.node_list.level_parent_node.transform.localScale = u3dpool.vec3(3, 3, 3)
	self.change_state_anim = DG.Tweening.DOTween.Sequence()
	self.change_state_anim:Append(self.node_list.level_parent_node.rect:DOScale(Vector3(1, 1, 1), 1.2):SetEase(DG.Tweening.Ease.OutBack))
	self.node_list.tanchu_effect:SetActive(false)
	self.node_list.tanchu_effect:SetActive(true)
	self.change_state_anim:OnComplete(function ()
		self.node_list.tanchu_effect:SetActive(false)
	end)
end