CrossServerWGData = CrossServerWGData or BaseClass()


CrossServerWGData.LAST_CROSS_TYPE = 0

function CrossServerWGData:__init()
	if CrossServerWGData.Instance then
		ErrorLog("[CrossServerWGData] attempt to create singleton twice!")
		return
	end
	CrossServerWGData.Instance =self
	self.cs_info = {
		cross_activity_type = 0,
		login_server_ip = 0,
		login_server_port = 0,
		pname = "",
		login_time = 0,
		login_str = "",
		anti_wallow = 0,
		server = 0,
	}

	self.cross_server_group_list = {}
	self.valid_cross_server_group_count = 0
	self.role_cross_server_group_seq = 0
	self.role_main_server_data = nil
	self.role_cross_server_group_valid_server_count = 0
	self.role_enter_cross_server_stage = false
	self.enter_corss_server_group_count = 0
	self.cross_server_mean_server_level = 0
end

function CrossServerWGData:__delete()
	CrossServerWGData.Instance = nil
	self.cs_info = nil
end

function CrossServerWGData:SetCrossInfo(info)
	if not info then return end
	for k,v in pairs(info) do
		self.cs_info[k] = v
	end
end

function CrossServerWGData:GetCrossInfo()
	return self.cs_info
end


--=========================== 2024年 跨服新数据  =====================================
function CrossServerWGData:SetCrossServerInfo(protocol)
	self.cross_server_group_list = protocol.cross_server_group_list
	-- 有效跨服组数量
	self.valid_cross_server_group_count = protocol.valid_cross_server_group_count
	-- 玩家所在跨服组的索引
	self.role_cross_server_group_seq = protocol.role_cross_server_group_seq
	-- 玩家所在跨服组的主服
	self.role_main_server_data = protocol.role_main_server_data
	-- 玩家所在跨服组，有多少个有效服
	self.role_cross_server_group_valid_server_count = protocol.role_cross_server_group_valid_server_count
	-- 玩家所在服是否进入跨服阶段
	self.role_enter_cross_server_stage = protocol.role_enter_cross_server_stage
	-- 有多少个跨服组进入跨服
	self.enter_corss_server_group_count = protocol.enter_corss_server_group_count
	-- 跨服平均世界等级
	self.cross_server_mean_server_level = protocol.cross_server_mean_server_level
end

-- 有多少个有效跨服组
function CrossServerWGData:GetValidCrossServerGroupCount()
	return self.valid_cross_server_group_count
end

-- 本服是否进入跨服状态 bool
function CrossServerWGData:GetIsEnterCrossSeverStage()
	return self.role_enter_cross_server_stage
end

-- 玩家所在跨服组的主服索引 seq
function CrossServerWGData:RoleCrossServerGroupSeq()
	return self.role_cross_server_group_seq
end

-- 玩家所在跨服组的主服信息 {server_id, plat_type}
function CrossServerWGData:GetRoleMainServerData()
	return self.role_main_server_data
end

-- 服务器平均世界等级
function CrossServerWGData:GetServerMeanWorldLevel()
	if not self:GetIsEnterCrossSeverStage() then
		return RankWGData.Instance:GetWordLevel()
	end

	return self.cross_server_mean_server_level
end

-- 获取跨服信息 seq, data  如果获取频繁，转表缓存
function CrossServerWGData:GetCrossServerData(server_id, plat_type)
	for group_seq, group_data in pairs(self.cross_server_group_list) do
		for server_seq, server_data in pairs(group_data.server_list) do
			if server_id == server_data.server_id and plat_type == server_data.plat_type then
				return group_seq, group_data
			end
		end
	end

	return 0, nil
end

-- 获取是否与玩家同一个跨服组 bool
function CrossServerWGData:GetIsSameCrossServerGroupAsRole(server_id, plat_type)
	local group_seq = self:GetCrossServerData(server_id, plat_type)
	return group_seq == self:RoleCrossServerGroupSeq()
end

-- 获取主服信息 server_id, plat_type
function CrossServerWGData:GetServerMainServerData(server_id, plat_type)
	local group_seq, group_data = self:GetCrossServerData(server_id, plat_type)
	if group_data then
		local data = (group_data.server_list or {})[1] or {}
		return data.server_id, data.plat_type
	end

	return nil, nil
end

-- 获取服务器列表信息（根据seq）
function CrossServerWGData:GetServerMainServerDataBySeq(group_seq)
	local server_group_data = self.cross_server_group_list and self.cross_server_group_list[group_seq]

	if not server_group_data then
		return nil
	end

	return server_group_data.main_server_data
end

-- 获取服务器列表信息（根据seq）
function CrossServerWGData:GetServerMainServerIDBySeq(group_seq)
	local main_server_data = CrossServerWGData.Instance:GetServerMainServerDataBySeq(group_seq)
	
	if not main_server_data then
		return 0
	end

	return main_server_data.server_id or 0
end