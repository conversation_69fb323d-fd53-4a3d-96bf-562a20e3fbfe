SwornBuildTaskType = {
	Person = 0,   -- 个人任务
	Team = 1,     -- 结义任务
	All = 2,      -- 所有任务
}

function SwornWGData:InitBuildData()
	self.jieyi_build_level_cfg = ListToMap(self.jieyi_cfg.jieyi_build_level, "level")
	self.jieyi_build_task_cfg = ListToMap(self.jieyi_cfg.jieyi_build_task, "task_seq")
	self.jieyi_build_active_reward_cfg = ListToMap(self.jieyi_cfg.jieyi_build_active_reward, "seq")
	self.jieyi_build_skill_cfg = ListToMap(self.jieyi_cfg.jieyi_build_skill, "skill_id", "skill_level")
	self.jieyi_build_box_cfg = self.jieyi_cfg.jieyi_build_box

	self.build_level = 0
	self.build_exp = 0
	self.build_get_back_num = 0
	self.build_active_sum = 0
	self.build_get_active_reward_flag = {}
	self.build_task_list = {}

	self.build_show_active_reward = {}     -- 活跃奖励
	self.build_show_all_task_list = {}     -- 所有任务
end


function SwornWGData:SetBuildAllInfo(protocol)
	self.build_level = protocol.build_level
	self.build_exp = protocol.build_exp
	self.build_get_back_num = protocol.build_get_back_num
	self.build_active_sum = protocol.build_active_sum
	self.build_get_active_reward_flag = protocol.build_get_active_reward_flag
	self.build_task_list = protocol.build_task_list

	self:UpdatBuildShowActiveReward()
	self:UpdatBuildAllShowTaskList()
end

function SwornWGData:SetBuildActiveUpdate(protocol)
	self.build_active_sum = protocol.build_active_sum
	self.build_get_active_reward_flag = protocol.build_get_active_reward_flag
	self:UpdatBuildShowActiveReward()
end

function SwornWGData:SetBuildFanliRewardUpdate(protocol)
	self.build_get_back_num = protocol.build_get_back_num
end

function SwornWGData:SetBuildLevelUpdate(protocol)
	self.build_level = protocol.build_level
	self.build_exp = protocol.build_exp
end

function SwornWGData:SetBuildTaskUpdate(protocol)
	local data = protocol.change_data
	for i, v in pairs(self.build_task_list) do
		if v.task_seq == data.task_seq then
			self.build_task_list[v.task_seq] = data
			self:UpdatBuildSingleShowTaskList(v.task_seq)
			return
		end
	end
end

function SwornWGData:GetBuildLevel()
	return self.build_level
end

function SwornWGData:GetBuildExp()
	return self.build_exp
end

function SwornWGData:GetBuildBackNum()
	return self.build_get_back_num
end

function SwornWGData:GetBuildTaskInfoBySeq(task_seq)
	return self.build_task_list[task_seq]
end

function SwornWGData:GetBuildActiveRewardFlag(seq)
	return self.build_get_active_reward_flag[seq] or 0
end

function SwornWGData:GetBuildActiveValue()
	return self.build_active_sum
end

function SwornWGData:GetBuildFlushEndTime()
	local had_sworn = SwornWGData.Instance:HadSworn()
	if not had_sworn then
		return 0
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local week_day = TimeUtil.FormatSecond3MYHM1(server_time)
	local zero_time = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
	local add_time = (8 - week_day) * 24 * 60 * 60
	return zero_time + add_time - server_time
end

function SwornWGData:UpdatBuildShowActiveReward()
	local show_active_reward_list = {}
	for k, v in pairs(self.jieyi_build_active_reward_cfg) do
		local data = {}
		local seq = v.seq
		data.seq = seq
		data.cfg = v
		data.get_flag = self:GetBuildActiveRewardFlag(seq)
		data.can_get_flag = data.get_flag ~= 1 and data.cfg.need_value <= self.build_active_sum
		table.insert(show_active_reward_list, data)
	end

	self.build_show_active_reward = show_active_reward_list
end

function SwornWGData:GetBuildShowActiveReward()
	return self.build_show_active_reward
end

function SwornWGData:UpdatBuildAllShowTaskList()
	for k, v in pairs(self.jieyi_build_task_cfg) do
		local task_seq = v.task_seq
		local data = self:GetBuildOneTaskDataBySeq(task_seq)
		self.build_show_all_task_list[task_seq] = data
	end
end

function SwornWGData:UpdatBuildSingleShowTaskList(task_seq)
	local data = self:GetBuildOneTaskDataBySeq(task_seq)
	self.build_show_all_task_list[task_seq] = data
end

function SwornWGData:GetBuildOneTaskDataBySeq(task_seq)
	local data = {}
	local cfg = self.jieyi_build_task_cfg[task_seq]
	data.task_seq = task_seq
	data.cfg = cfg
	local info = self:GetBuildTaskInfoBySeq(task_seq)
	data.task_progress = info and info.task_progress or 0
	local task_status = info and info.task_status or 0
	if task_status == 1 then
		data.task_status = REWARD_STATE_TYPE.FINISH
		data.sort_index = 1000
	elseif task_status == 0 and data.task_progress >= cfg.condition then
		data.task_status = REWARD_STATE_TYPE.CAN_FETCH
		data.sort_index = 10
	else	
		data.task_status = REWARD_STATE_TYPE.UNDONE
		data.sort_index = 100
	end

	return data
end

function SwornWGData:GetBuildTaskListByType(task_type)
	local cur_task_type = task_type or SwornBuildTaskType.All
	local show_task_list = {}
	local fun = function (all_tak_list, need_type)
		for k, v in pairs(all_tak_list) do
			if v.cfg and v.cfg.task_type == need_type then
				table.insert(show_task_list, v)
			end
		end
	end

	if cur_task_type == SwornBuildTaskType.All then
		return self.build_show_all_task_list
	elseif cur_task_type == SwornBuildTaskType.Person then
		fun(self.build_show_all_task_list, SwornBuildTaskType.Person)
	elseif cur_task_type == SwornBuildTaskType.Team then
		fun(self.build_show_all_task_list, SwornBuildTaskType.Team)
	end

	if not IsEmptyTable(show_task_list) then
		table.sort(show_task_list, SortTools.KeyLowerSorters("sort_index", "task_seq"))
	end
	return show_task_list
end

function SwornWGData:GetBuildTaskDefaultSelect()
	local person_red = self:GetBuildTaskTypeIsCanFetch(SwornBuildTaskType.Person)
	if person_red then
		return SwornBuildTaskType.Person
	end

	local team_red = self:GetBuildTaskTypeIsCanFetch(SwornBuildTaskType.Team)
	if team_red then
		return SwornBuildTaskType.Team
	end

	return SwornBuildTaskType.Person
end

function SwornWGData:GetBuildTaskTypeIsCanFetch(task_type)
	if not task_type then
		return false
	end

	local task_list = self:GetBuildTaskListByType(task_type)
	for k, v in pairs(task_list) do
		if v.task_status == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end

	return false
end

function SwornWGData:GetBuildLevelCfgBylevel(level)
	return self.jieyi_build_level_cfg[level]
end

function SwornWGData:GetBuildSkillCfgById(skill_id, level)
	return (self.jieyi_build_skill_cfg[skill_id] or {})[level]
end

function SwornWGData:GetBuildShowSkillList()
	local show_skill_list = {}
	local cfg = self.jieyi_build_level_cfg[self.build_level]

	if not cfg then
		return show_skill_list
	end

	local level_skill_list_cfg = Split(cfg.skill_list, '|')
	if not IsEmptyTable(level_skill_list_cfg) then
		for k, v in pairs(level_skill_list_cfg) do
			local skill_info_cfg = Split(v, ',')
			if skill_info_cfg then
				local skill_id = tonumber(skill_info_cfg[1])
				local skill_level = tonumber(skill_info_cfg[2])
				local skill_cfg = self:GetBuildSkillCfgById(skill_id, skill_level)
				table.insert(show_skill_list, skill_cfg)
			end
		end
	end

	return show_skill_list
end

function SwornWGData:GetBuildShowBoxCfg()
	for k, v in pairs(self.jieyi_build_box_cfg) do
		if self.build_get_back_num >= v.min_get_back_num and v.max_get_back_num >= self.build_get_back_num then
			return v
		end
	end

	return nil
end

function SwornWGData:GetBuildRewardProgress()
	local cur_progress = 0
	local cfg = self.build_show_active_reward
	local build_active_sum = self.build_active_sum
	if IsEmptyTable(cfg) then
		return cur_progress
	end

	local progress_list = {0, 0.33, 0.66, 1}			--对应的进度条值
	for k, v in pairs(cfg) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].cfg.need_value or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].cfg.need_value or cfg[#cfg].cfg.need_value
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]

		if build_active_sum > cur_need and build_active_sum <= next_need then
			cur_progress = (build_active_sum - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif build_active_sum > cfg[#cfg].cfg.need_value then
			cur_progress = 1
			break
		end
	end

	return cur_progress
end

-----------------remind-------------
--悬赏红点
function SwornWGData:GetSwornBuildTaskRemind()
	if self.my_sworn_state ~= SwornWGData.SWORN_TYPE.HAS_SWORN then
		return 0
	end

	for k, v in pairs(self.build_show_active_reward) do
		if v.can_get_flag then
			return 1
		end
	end

	for k, v in pairs(self.build_show_all_task_list) do
		if v.task_status == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

	return 0
end

--桃园红点
function SwornWGData:GetSwornBuildTaoYuanRemind()
	if self.my_sworn_state ~= SwornWGData.SWORN_TYPE.HAS_SWORN then
		return 0
	end

	if self.build_get_back_num > 0 then
		return 1
	end

	return 0
end