GuildWGCtrl = GuildWGCtrl or BaseClass(BaseWGCtrl)

-- 注册仙盟系统所有协议
function GuildWGCtrl:RegisterAllProtocols()
	--014
	self:RegisterProtocol(SCRoleGuildInfoChange, "OnGuildInfoChange")

	-- 018
	self:RegisterProtocol(CSFinishAllGuildTask)

	--042
	self:RegisterProtocol(SCAddGuildExpSucc, "OnJuanXianResult")
	self:RegisterProtocol(SCGuildPartyInfo, "OnGuildPartyInfo")

	self:RegisterProtocol(CSCreateGuild)
	self:RegisterProtocol(CSDismissGuild)
	self:RegisterProtocol(CSApplyForJoinGuild)
	self:RegisterProtocol(CSAppointGuild)
	self:RegisterProtocol(CSApplyForJoinGuildAck)
	self:RegisterProtocol(CSQuitGuild)
	self:RegisterProtocol(CSKickoutGuild)
	self:RegisterProtocol(CSGuildChangeNotice)
	self:RegisterProtocol(CSGuildMailAll)
	self:RegisterProtocol(CSGetGuildInfo)
	self:RegisterProtocol(CSGuildDelate)
	self:RegisterProtocol(CSApplyforSetup)
	self:RegisterProtocol(CSGuildCallIn)
	self:RegisterProtocol(CSGuildCheckCanDelate)
	self:RegisterProtocol(CSGuildPartyOp)
	self:RegisterProtocol(CSInviteGuild)
	self:RegisterProtocol(CSInviteGuildAck)
	self:RegisterProtocol(CSGuildPartyStartReq)
	self:RegisterProtocol(CSGuildFbStartReq)
	self:RegisterProtocol(CSGuildFbEnterReq)
	self:RegisterProtocol(CSGuildBonfireStartReq)
	self:RegisterProtocol(CSGuildBonfireGotoReq)
	self:RegisterProtocol(CSGuildBonfireAddMucaiReq)
	self:RegisterProtocol(CSGetGuildFBGuardPos)
	self:RegisterProtocol(CSGuildLingdiReward)
	self:RegisterProtocol(CSGuildDayReward)
	self:RegisterProtocol(CSGuildCallUpOperate)
	self:RegisterProtocol(CSGuildFbGuWu)
	self:RegisterProtocol(CSGuildChangeFlag)  --4300
	-- 052
	self:RegisterProtocol(CSSendGuildSosReq)

	-- 062
	self:RegisterProtocol(CSGuildBackToStation)

	-- 098
	self:RegisterProtocol(SCFlagUpdate, "OnSCFlagUpdate") -- 9828
	self:RegisterProtocol(SCCreateGuild, "OnCreateGuild")
	self:RegisterProtocol(SCApplyForJoinGuild, "OnApplyForJoinGuild")
	self:RegisterProtocol(SCNotifyGuildSuper, "OnNotifyGuildSuper")
	self:RegisterProtocol(SCQuitGuild, "OnExitGuild")
	self:RegisterProtocol(SCKickoutGuild, "OnKickoutGuild")
	self:RegisterProtocol(SCAppointGuild, "OnAppointGuild")
	self:RegisterProtocol(SCChangeNotice, "OnChangeNotice")
	self:RegisterProtocol(SCGuildMailAll, "OnGuildMailAll")
	self:RegisterProtocol(SCGuildBaseInfo, "OnGuildInfo")--仙盟基本信息
	self:RegisterProtocol(SCAllGuildBaseInfo, "OnAllGuildInfoList")--仙盟所有列表信息
	self:RegisterProtocol(SCGuildGetApplyForList, "OnGuildApplyForList")
	self:RegisterProtocol(SCGuildEventList, "OnGuildEventList") --仙盟仓库列表
	self:RegisterProtocol(SCGuildMemberList, "OnGuildMemberList")--仙盟成员列表
	self:RegisterProtocol(SCApplyForJoinGuildAck, "OnApplyForJoinGuildAck")
	self:RegisterProtocol(SCGuildCheckCanDelateAck, "OnGuildCheckCanDelate")
	self:RegisterProtocol(SCGuildOperaSucc, "OnGuildOperaSucc")
	self:RegisterProtocol(SCInviteNotify, "OnInviteNotify")
	self:RegisterProtocol(SCGuildMemberSos, "OnSCGuildMemberSos")
	self:RegisterProtocol(SCGuildResetName, "OnGuildResetName")
	self:RegisterProtocol(SCExchangeInfo, "OnGuildExchangeInfo")
	self:RegisterProtocol(SCGuildBossInfo, "OnGuildBossInfo")
	self:RegisterProtocol(SCBossRewardInfo, "OnOldBossRewardInfo")
	self:RegisterProtocol(SCGuildRoleOtherInfo, "OnGuildRoleOtherInfo")

	--仙盟守护
	self:RegisterProtocol(SCGuildFBInfo, "OnGuildFBInfo")
	self:RegisterProtocol(SCGuildFbRankInfo, "OnSCGuildFbRankInfo")
	self:RegisterProtocol(SCGuildFbCrossRankInfo, "OnSCGuildFbCrossRankInfo")
    self:RegisterProtocol(SCGuildFbDetailInfo, "OnSCGuildFbDetailInfo")
	self:RegisterProtocol(SCGuildFBRoleInfo, "OnSCGuildFBRoleInfo")
	self:RegisterProtocol(SCGuildBonfireStatus, "OnGuildBonfireStatus")
	self:RegisterProtocol(SCGuildFBGuardPos, "OnGuildFBGuardPos")
	self:RegisterProtocol(SCGuildFbInFBRankInfo, "OnSCGuildFbInFBRankInfo")
	self:RegisterProtocol(SCGuildFbInFBCrossRankInfo, "OnSCGuildFbInFBCrossRankInfo")

	self:RegisterProtocol(SCGuildBossRoleInfo, "OnGuildBossRoleInfo")
	self:RegisterProtocol(SCGuildBossHurtRankInfo, "OnGuildBossHurtRankInfo")
	self:RegisterProtocol(SCGuildBossRewardInfo, "OnGuildBossRewardInfo")

	--仙盟仓库
	self:RegisterProtocol(SCGuildStorageInfo, "OnGuildStorgeInfo")
	self:RegisterProtocol(SCGuildStorageChange, "OnGuildStorgeChangeInfo")
	self:RegisterProtocol(CSGuildStorageOperate)  						--仓库操作
	self:RegisterProtocol(CSGuildStorageDiscardItem)						--仙盟仓库销毁物品
	self:RegisterProtocol(CSGuildStorageDonateItems)						--仙盟仓库批量捐献

	-- 仙盟运势协议
	self:RegisterProtocol(CSGuildZhuLuckyReq)
	self:RegisterProtocol(CSGetAllGuildMemberLuckyInfo)
	self:RegisterProtocol(CSInviteLuckyZhufu)							--邀请仙盟祝福请求

	self:RegisterProtocol(SCGuildLuckyInfo, "OnGuildLuckyInfo")		--仙盟运势下发协议
	self:RegisterProtocol(SCInviteLuckyZhufu, "OnInviteLuckyZhufu")	--邀请仙盟祝福通知
	self:RegisterProtocol(SCGuildLuckyChangeNotice, "OnGuildLuckyChangeNotice")	--仙盟祝福变更通知

	--仙盟凶兽
	self:RegisterProtocol(SCGuildActiveDegreeInfo, "OnGuildActiveDegreeInfo")	--仙盟凶兽活跃等信息
	self:RegisterProtocol(CSGuildCallBeastReq)  								--召唤凶兽

	self:RegisterProtocol(CSAddGuildExp)  								--捐献
	self:RegisterProtocol(CSGuildUpLevel)  								--升级
	self:RegisterProtocol(CSGuildGetBuff)  								--领取buff
	self:RegisterProtocol(CSGuildExchange)  							--兑换

	self:RegisterProtocol(CSGuildBossOperate)


	self:RegisterProtocol(CSGuildDevelopOperate)						--建设操作

	--仙盟召集
	self:RegisterProtocol(SCGuildCall, "OnGuildCall")
	self:RegisterProtocol(CSGuildCallOperate)

	--仙盟红包
	self:RegisterProtocol(SCGuildRedPocketListInfo, "OnGuildRedPocketListInfo")
	self:RegisterProtocol(SCGuildRedPocketDistributeInfo, "OnGuildRedPocketDistributeInfo")
	self:RegisterProtocol(CSGuildRedPocketOperate)
	self:RegisterProtocol(CSGuildVipRedPocketDistribute)

	--帮派攻城
	self:RegisterProtocol(SCGongchengzhanInfo, "OnGongchengzhanInfo")

	--仙盟战活动显示
	self:RegisterProtocol(SCGuildBattleMatchInfo, "OnGuildBattleMatchInfo")
	self:RegisterProtocol(SCGuildBattleEndRankInfo, "OnGuildBattleEndRankInfo")

	self:RegisterProtocol(SCGuildCombineReq, "OnGuildCombineReq") 			--收到仙盟合并请求
	self:RegisterProtocol(CSGuildCombineReq) 								--仙盟合并请求
	self:RegisterProtocol(CSGuildCombineAck) 								--回复仙盟合并请求

	self:RegisterProtocol(SCGuildFBPassInfo, "OnGuildFBPassInfo") 			--帮派试炼通关标记

	self:RegisterProtocol(CSJiuSheDaoFBEnterReq) 								--帮派boss副本进入请求
	self:RegisterProtocol(SCJiuSheDaoFBInfo, "OnSCJiuSheDaoFBInfo") 			--副本boss副本信息
	self:RegisterProtocol(CSJiuSheDaoFBHandler) 								--副本boss处理请求
	self:RegisterProtocol(SCJiuSheDaoFBJumpHandler, "OnSCJiuSheDaoFBJumpHandler") 			--副本boss副本信息
	self:RegisterProtocol(SCGuildTuanZhangBeenKilled, "OnSCGuildTuanZhangBeenKilled")

	self:RegisterProtocol(CSCrossLieKunFBReq)
	self:RegisterProtocol(SCCrossLieKunFBPlayerInfo, "OnSCCrossLieKunFBPlayerInfo")
	self:RegisterProtocol(SCCrossLieKunFBSceneInfo, "OnSCCrossLieKunFBSceneInfo")
	self:RegisterProtocol(SCCrossLieKunFBGuildMsgInfo, "OnSCCrossLieKunFBGuildMsgInfo")
	self:RegisterProtocol(SCCrossLieKunFBGatherInfo, "OnSCCrossLieKunFBGatherInfo")
	self:RegisterProtocol(SCCrossLieKunBossHurtInfo, "OnSCCrossLieKunBossHurtInfo")
	self:RegisterProtocol(SCCrossLieKunFBScoreInfo, "OnSCCrossLieKunFBScoreInfo")

	--仙盟建设任务018
	self:RegisterProtocol(CSGiveUpGuildBuildTask)
	self:RegisterProtocol(CSRefreshGuildBuildTask)
	self:RegisterProtocol(CSGuildBuildTaskStatesChange)
	self:RegisterProtocol(SCGuildBuildTaskInfo, "OnSCGuildBuildTaskInfo")
    --仙盟工资
    self:RegisterProtocol(CSGuildWageTaskFetchReward)
    self:RegisterProtocol(SCGuildWageTaskInfo, "OnSCGuildWageTaskInfo")

	self:BindGlobalEvent(SceneEventType.SCENE_CHANGE_COMPLETE, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.DayChange, self))

	--仙盟成员战力排行
	self:RegisterProtocol(CSGuildMemberCapabilityRank)											--4216
	self:RegisterProtocol(SCGuildMemberCapabilityRank, "OnSCGuildMemberCapabilityRank")			--4219

	--仙盟技能信息
	self:RegisterProtocol(CSGuildSkillUpgradeReq)											--4200
	self:RegisterProtocol(SCGuildSkillInfo, "OnSCGuildSkillInfo")			--4221

	--088
	self:RegisterProtocol(CSApplyForTuanZhangAgentReq)
	self:RegisterProtocol(SCTuanZhangAgentInfo,"OnSCTuanZhangAgentInfo")

	--仙盟争霸奖励分配
	self:RegisterProtocol(CSGuildBattleRewardApply)
	self:RegisterProtocol(SCGuildBattleRewardAlloc,"OnSCGuildBattleRewardAlloc")
	self:RegisterProtocol(SCGuildBattleRewardInfo,"OnSCGuildBattleRewardInfo")

	--跨服仙盟战
	self:RegisterProtocol(CSCrossXianmengzhanOpera)
	self:RegisterProtocol(SCCrossXianmengzhanMatchState,"OnSCCrossXianmengzhanMatchState")
	self:RegisterProtocol(SCCrossXianmengzhanGuildRank,"OnSCCrossXianmengzhanGuildRank")
	self:RegisterProtocol(SCCrossXianmengzhanKeepWinInfo,"OnSCCrossXianmengzhanKeepWinInfo")
	self:RegisterProtocol(SCCrossXianmengzhanOneMatchFinish,"OnSCCrossXianmengzhanOneMatchFinish")

	--每日宝箱
	self:RegisterProtocol(CSDailyTreasureOperate)
	self:RegisterProtocol(SCDailyTreasureInfo, "OnSCDailyTreasureInfo")
	self:RegisterProtocol(SCDailyTreasureAllyItemInfo, "OnSCDailyTreasureAllyItemInfo")
	self:RegisterProtocol(SCDailyTreasureAllyItemUpdate, "OnSCDailyTreasureAllyItemUpdate")
	self:RegisterProtocol(SCDailyTreasureRecordInfo, "OnSCDailyTreasureRecordInfo")
	self:RegisterProtocol(SCDailyTreasureRecordAdd, "OnSCDailyTreasureRecordAdd")
	self:RegisterProtocol(SCDailyTreasureAllyItemRemove, "OnSCDailyTreasureAllyItemRemove")
	self:RegisterProtocol(SCDailyTreasureBeReqHelpRefreshInfo, "OnSCDailyTreasureBeReqHelpRefreshInfo")
	self:RegisterProtocol(SCDailyTreasureBeReqHelpRefreshUpdate, "OnSCDailyTreasureBeReqHelpRefreshUpdate")
	self:RegisterProtocol(SCDailyTreasureReqHelpRefreshInfo, "OnSCDailyTreasureReqHelpRefreshInfo")
	self:RegisterProtocol(SCDailyTreasureReqHelpRefreshUpdate, "OnSCDailyTreasureReqHelpRefreshUpdate")
	self:RegisterProtocol(SCDailyTreasureReqHelpRefreshReward, "OnSCDailyTreasureReqHelpRefreshReward")

	self:RegisterProtocol(CSFakeGuildOperate)
	self:RegisterProtocol(SCFakeGuildListInfo, "OnFakeGuildListInfo")							-- 假仙盟信息

	-- 仙盟新功能
	self:RegisterProtocol(CSGuildOperate)
	self:RegisterProtocol(SCGuildSignInfo, "OnSCGuildSignInfo")									-- 仙盟签到
	self:RegisterProtocol(SCGuildRoleSignInfo, "OnSCGuildRoleSignInfo")							-- 仙盟签到 个人信息

	self:RegisterProtocol(SCGuildGiftBargainInfo, "OnSCGuildGiftBargainInfo")					-- 仙盟商店-砍价
	self:RegisterProtocol(SCGuildRoleShopInfo, "OnSCGuildRoleShopInfo")							-- 仙盟商店 个人信息
	self:RegisterProtocol(SCGuildMemberGiftStatusUpdate, "OnSCGuildMemberGiftStatusUpdate")		-- 仙盟商店 成员砍价礼包信息更新
	self:RegisterProtocol(SCGuildMemberGiftBargainInfo, "OnSCGuildMemberGiftBargainInfo")		-- 仙盟商店 成员砍价记录
	self:RegisterProtocol(SCGuildMemberGiftBargainUpdate, "OnSCGuildMemberGiftBargainUpdate")	-- 仙盟商店 成员砍价记录-单条
end

--==========================042==========================--
function GuildWGCtrl:DayChange()
	self:SendGuildRedPocketOperate(GUILD_RED_POCKET_OPERATE_TYPE.GUILD_RED_POCKET_OPERATE_INFO_LIST, 0, 0)
	GuildBaoXiangWGData.Instance:ClearInputCode()
	GuildBaoXiangWGData.Instance:ClearPasswordCode()
	self:GuildViewFlush(TabIndex.guild_baoxiang)
end
-- 仙盟捐献结果返回
function GuildWGCtrl:OnJuanXianResult(protocol)
	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.AddGongXianVal, protocol.add_gongxian, protocol.add_gongxian * 10))
end
-- 仙盟守护信息返回
function GuildWGCtrl:OnGuildPartyInfo(protocol)
	GuildDataConst.GUARD_INFO_VO.got_exp = protocol.got_exp
	GuildDataConst.GUARD_INFO_VO.got_xianhun = protocol.got_xianhun
	GuildDataConst.GUARD_INFO_VO.got_gongxian = protocol.got_gongxian
	GuildDataConst.GUARD_INFO_VO.gather_count = protocol.gather_count
	GuildDataConst.GUARD_INFO_VO.is_double_rewardt = protocol.is_double_rewardt
	GuildDataConst.GUARD_INFO_VO.is_clear_cdt = protocol.is_clear_cdt
	GuildDataConst.GUARD_INFO_VO.next_gather_timet = protocol.next_gather_timet
	GuildDataConst.GUARD_INFO_VO.reset_gather_times = protocol.reset_gather_times

	self:UpdataRemindNum()
end

-- 创建仙盟请求
function GuildWGCtrl:SendCreateGuildReq(guild_name, create_type, index, notice, flag_id, flag_color, flag_name)
	local guild_notice = ""

	if nil ~= notice and "" ~= notice then
		guild_notice = ChatFilter.Instance:Filter(notice)
	end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCreateGuild)
	send_protocol.guild_name = nil ~= guild_name and guild_name or ""
	send_protocol.create_guild_type = create_type
	send_protocol.knapsack_index = index
	send_protocol.guild_notice = guild_notice
	send_protocol.flag_id = flag_id
	send_protocol.flag_color = flag_color
	send_protocol.flag_name = flag_name
	send_protocol:EncodeAndSend()
end

-- 申请入盟请求
function GuildWGCtrl:SendJoinGuildReq(guild_id, is_auto_join)
	local cannot_join, cd_time = GuildWGData.Instance:GetJoinGuildCdTime()

	if cannot_join then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Guild.JoinGuildCdTimeTip, TimeUtil.FormatSecondDHM2(cd_time)))
		return
	end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSApplyForJoinGuild)
	send_protocol.guild_id = guild_id
	send_protocol.is_auto_join = is_auto_join or 0
	send_protocol:EncodeAndSend()
end

-- 任命请求
function GuildWGCtrl:SendGuildAppointReq(guild_id, beappoint_uid, post)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSAppointGuild)
	send_protocol.guild_id = guild_id
	send_protocol.beappoint_uid = beappoint_uid
	send_protocol.post = post
	send_protocol:EncodeAndSend()
end

-- 批量完成仙盟任务
function GuildWGCtrl.SendFinishAllGuildTask(commit_times)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFinishAllGuildTask)
	send_protocol.commit_times = commit_times or 0
	send_protocol:EncodeAndSend()
end

-- 审批申请加入仙盟请求
function GuildWGCtrl:SendGuildApplyforJoinReq(guild_id, result, count, list)
	if nil == list then
		return
	end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSApplyForJoinGuildAck)
	send_protocol.guild_id = guild_id
	send_protocol.result = result
	send_protocol.count = count
	send_protocol.list = list
	send_protocol:EncodeAndSend()
end

-- 退出仙盟请求
function GuildWGCtrl:SendExigGuildReq(guild_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSQuitGuild)
	send_protocol.guild_id = guild_id
	send_protocol:EncodeAndSend()
end

-- 踢出仙盟请求
function GuildWGCtrl:SendKickoutGuildReq(guild_id, bekicker_count, list)
	if nil == list then
		return
	end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSKickoutGuild)
	send_protocol.guild_id = guild_id
	send_protocol.bekicker_count = bekicker_count
	send_protocol.list = list
	send_protocol:EncodeAndSend()
end

-- 仙盟公告修改请求
function GuildWGCtrl:SendGuildChangeNoticeReq(guild_id, notice)
	local guild_notice = ""

	if nil ~= notice and "" ~= notice then
		guild_notice = ChatFilter.Instance:Filter(notice)
	end

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildChangeNotice)
	send_protocol.guild_id = guild_id
	send_protocol.notice = guild_notice
	send_protocol:EncodeAndSend()
end

-- 发送仙盟邮件请求
function GuildWGCtrl:SendGuildMailReq(guild_id, subject, contenttxt_len, contenttxt)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildMailAll)
	send_protocol.guild_id = guild_id
	send_protocol.subject = subject
	send_protocol.contenttxt_len = contenttxt_len
	send_protocol.contenttxt = contenttxt
	send_protocol:EncodeAndSend()
end

-- 获取仙盟信息请求
function GuildWGCtrl:SendGetGuildInfoReq(guild_info_type, guild_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGetGuildInfo)
	send_protocol.guild_info_type = guild_info_type
	send_protocol.guild_id = guild_id
	send_protocol:EncodeAndSend()
end

-- 仙盟成员弹劾请求
function GuildWGCtrl:SendGuildDelateReq(guild_id, knapsack_index)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildDelate)
	send_protocol.guild_id = guild_id
	send_protocol.knapsack_index = knapsack_index
	send_protocol:EncodeAndSend()
end

-- 仙盟设置请求
function GuildWGCtrl:SendSettingGuildReq(guild_id, applyfor_setup, need_capability, need_level)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSApplyforSetup)
	send_protocol.guild_id = guild_id
	send_protocol.applyfor_setup = applyfor_setup
	send_protocol.need_capability = need_capability
	send_protocol.need_level = need_level
	send_protocol:EncodeAndSend()
end

-- 捐献请求
-- times捐献铜钱次数
-- item_list捐献物品列表[{item_id:100, item_num:1}, ....]
function GuildWGCtrl:SendAddGuildExpReq(juanxian_type, num)
	-- print_error(juanxian_type,num)
	self:SendGuildDevelopOperate(GUILD_BUILD_OPERA_TYPE.GUILD_DEVELOP_CONTRIBUTE, juanxian_type, num)
end

--仙盟升级
--hall_type 参照 GUILD_HALL_TYPE
function GuildWGCtrl:SendGuldUpLevelReq(hall_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildUpLevel)
	send_protocol.hall_type = hall_type
	send_protocol:EncodeAndSend()
end

--领取buff
function GuildWGCtrl:SendGetBuffReq()
	self:SendGuildDevelopOperate(GUILD_BUILD_OPERA_TYPE.GUILD_DEVELOP_OPENGUWU)
end

--兑换
function GuildWGCtrl:SendGuildExchangeReq(id, num)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildExchange)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_id = main_role_vo.guild_id
	send_protocol.guild_id = guild_id
	send_protocol.id = id
	send_protocol.num = num
	send_protocol:EncodeAndSend()
end

-- 建设操作
function GuildWGCtrl:SendGuildDevelopOperate(operate_type, param1, param2, param3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildDevelopOperate)
	send_protocol.operate_type = operate_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol.param3 = param3 or 0
	send_protocol:EncodeAndSend()
end

-- 仙盟招募请求
function GuildWGCtrl:SendGuildCallInReq(guild_id, content)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildCallIn)
	send_protocol.guild_id = guild_id
	send_protocol.content = content
	send_protocol:EncodeAndSend()
end

-- 检查是否能够弹劾盟主
function GuildWGCtrl:SendCheckCanDelateReq(guild_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildCheckCanDelate)
	send_protocol.guild_id = guild_id
	send_protocol:EncodeAndSend()
end

-- 发送解散仙盟请求
function GuildWGCtrl:SendDismissGuildReq(guild_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSDismissGuild)
	send_protocol.guild_id = guild_id
	send_protocol:EncodeAndSend()
end

-- 获取仙盟守护信息请求
function GuildWGCtrl:SendGuildPartyOpReq(do_what)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildPartyOp)
	send_protocol.do_what = do_what
	send_protocol:EncodeAndSend()
end

--==========================062==========================--
-- 发送返回驻地请求
function GuildWGCtrl:SendGuildBackToStationReq(guild_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBackToStation)
	send_protocol.guild_id = guild_id
	send_protocol:EncodeAndSend()
end

--==========================098==========================--
-- 仙盟信息变更
function GuildWGCtrl:OnGuildInfoChange(protocol)--15305
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if obj == nil or not obj:IsRole() then
	 	return
	end
	local vo = obj:GetVo()
	local old_guild_id = vo.guild_id
	local old_guild_post = vo.guild_post
	local old_gongxian = vo.guild_gongxian
	obj:SetAttr("guild_id", protocol.guild_id)
	obj:SetAttr("guild_post", protocol.guild_post)
	obj:SetAttr("guild_name", protocol.guild_name)
	obj:SetAttr("guild_gongxian", protocol.guild_gongxian)
	obj:SetAttr("guild_total_gongxian", protocol.guild_total_gongxian)
	if SceneObjType.MainRole == obj:GetType() then
		local guildvo = GuildDataConst.GUILDVO
		-- print_error(guildvo)
		guildvo.guild_id = vo.guild_id
		guildvo.guild_name = vo.guild_name
		guildvo.guild_post = vo.guild_post
		guildvo.guild_gongxian = vo.guild_gongxian
		guildvo.guild_gongxian = vo.guild_gongxian
		guildvo.guild_total_gongxian = vo.guild_total_gongxian
		guildvo.guild_exp = protocol.guild_exp
		guildvo.guild_money = protocol.guild_money
		guildvo.guild_level = protocol.guild_level
		guildvo.guild_contributeTimes = protocol.guild_contributeTimes
        guildvo.tuanzhang_name = protocol.tuanzhang_name
        guildvo.tuanzhang_uid = protocol.tuanzhang_uid
		guildvo.tuanzhang_prof = protocol.tuanzhang_prof
		guildvo.tuanzhang_sex = protocol.tuanzhang_sex

		RoleWGData.Instance:CacheAttr("guild_level", guildvo.guild_level) -- 缓存仙盟等级
		if old_guild_id <= 0 and vo.guild_id > 0 then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.InviteGuildTip,vo.guild_name))
            --打开欢迎进入界面
            if protocol.tuanzhang_uid ~= RoleWGData.Instance:GetRoleInfo().role_id then --不是自己创建的盟
                --self:OpenGuildWelcomeView()  去掉欢迎界面
            	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_ALLY_ITEM_INFO)
            end
            self:EnterGuildEvent()
		end

		if vo.guild_id ~= old_guild_id then 						--仙盟Id有变化
			local is_quit = vo.guild_id == 0
			local tab_index = is_quit and TabIndex.guild_guildlist or TabIndex.guild_info

			-- 仙盟信息变化后 详情界面展示模型数据是旧的，
			if not is_quit and self.view and self.view:IsOpen() and (self.view:GetShowIndex() == TabIndex.guild_guildlist) then
				GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
			end

			self:Open(math.floor(tab_index),{is_quit = is_quit})
			self.view:SetGuildAuthority()
			if vo.guild_id ~= 0 and self.pop_create_view:IsOpen() then
				self.pop_create_view:Close()
			end
			if is_quit then
				ChatWGData.Instance:RemoveGetChannel(CHANNEL_TYPE.GUILD)
				self:QuitGuildEvent()
			end
		elseif vo.guild_post ~= old_guild_post then
			local post_authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[vo.guild_post] --仙盟权位列表
			if nil ~= post_authority then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.AppointTip, post_authority.post))
			end
			self:GetAllGuildData()
		else
			self:GetAllGuildData()
		end
		self:GuildViewFlush(TabIndex.guild_info)

		self.view:SetTabVisible()

		if GuildWGCtrl.Instance.guild_juanxian_view:IsOpen() then
			GuildWGCtrl.Instance.guild_juanxian_view:OnFlushView()
		end
		self:GuildExeChange(protocol.add_guild_exp)

		if vo.guild_id == 0 then
			MainuiWGCtrl.Instance:ShowRedBtn()
			GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_ALL,nil,0)
			GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_LOGNHUI,nil,0)
		end

		if not GuildWGData.Instance:IsMemberHasPowerToDealApply(vo.guild_post) then
			if GuildWGCtrl.Instance:IsApplyForViewOpen() then
				GuildWGCtrl.Instance:CloseApplyForView()
			end
			-- MainuiWGCtrl.Instance:UpdateChatIcon()
		end
	end

	self:UpdataRemindNum()
	GuildWGCtrl.Instance:SendGuildRedPocketOperate(GUILD_RED_POCKET_OPERATE_TYPE.GUILD_RED_POCKET_OPERATE_INFO_LIST, 0, 0)
	self:OpenMainUiGuildBtn(vo.guild_id)

	RemindManager.Instance:Fire(RemindName.Guild_Skill)--帮派技能
	GlobalEventSystem:Fire(OtherEventType.Guild_Change)
end

--退出仙盟事件
function GuildWGCtrl:QuitGuildEvent()
	-- GuildBaoXiangWGData.Instance:ClearBeReqHelpRefresData()
	self:BaoXiangReaInvateTip()
	RemindManager.Instance:Fire(RemindName.BiZuoBaoXiangRemind)
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GUILD_SYSTEM_INFO)
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GET_GUILD_REDPAPER_INF0)
	GuildWGData.Instance:SetEnterGuildFlushFlag(false)
end

--加入仙盟
function GuildWGCtrl:EnterGuildEvent()
	-- GuildBaoXiangWGData.Instance:ClearBeReqHelpRefresData()
	RemindManager.Instance:Fire(RemindName.BiZuoBaoXiangRemind)
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GUILD_SYSTEM_INFO)
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GET_GUILD_REDPAPER_INF0)
	GuildWGData.Instance:SetEnterGuildFlushFlag(true)
	--成功加入仙盟 去掉仙盟邀请标记 清除仙盟邀请数据
	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.GUILD_INVITE_HINT)
	GuildWGData.Instance:SetOnInviteNotify({})
end

function GuildWGCtrl:OnSCFlagUpdate(protocol)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if nil == role_vo then
		return
	end
	if protocol.guild_id == role_vo.guild_id then
		local guildvo = GuildDataConst.GUILDVO
		guildvo.flag_id = protocol.flag_id
		guildvo.flag_color = protocol.flag_color
		guildvo.flag_name = protocol.flag_name
		self:GuildViewFlush(TabIndex.guild_info)
	end
end

-- 发送仙盟邮件结果
function GuildWGCtrl:OnGuildMailAll(protocol)
	if 0 == protocol.ret then 						-- 0：成功 其它失败
		self.pop_mail_view:Close()
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.MailSendSuccess)
	end
end

-- 创建仙盟结果
function GuildWGCtrl:OnCreateGuild(protocol)
	if 0 == protocol.ret then 						-- 0：成功 其它失败
		local guildvo = GuildDataConst.GUILDVO
		guildvo.guild_id = protocol.guild_id
		guildvo.guild_name = protocol.guild_name
		GuildWGCtrl.Instance:Open(11)
		GuildWGCtrl.Instance:CloseCreateGuildView()
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.CreateGuildSuccess)
		-- RemindManager.Instance:Fire(RemindName.ZhuZaiShenDian)
		RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)
	end
end

-- 玩家申请加入仙盟结果
function GuildWGCtrl:OnApplyForJoinGuild(protocol)
	if 0 == protocol.ret then 						-- 0：成功 其它失败
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ApplyForJoinGuild)
		local info_list = self.data:GetGuildListData()
		for i=1, #info_list do
			local info = info_list[i]
			if info.guild_id == protocol.guild_id then
				info.has_applying = 1
				break
			end
		end

		if self.view:IsOpen() then
			self.view:Flush(TabIndex.guild_guildlist)
		end
  	end

end

-- 仙盟消息通知
function GuildWGCtrl:OnNotifyGuildSuper(protocol)
	local notify_type_list = GuildDataConst.GUILD_NOTIFY_TYPE
	local notify_type = protocol.notify_type
	if notify_type == notify_type_list.APPLYFOR then
		self:MainuiTipApplyfor(1)
		if nil ~= self.pop_apply_for_view then
			if true == self.pop_apply_for_view:IsOpen() then
				self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO, GuildDataConst.GUILDVO.guild_id)
			end
		end
	elseif notify_type == notify_type_list.UNION_APPLYFOR then
	elseif notify_type == notify_type_list.UNION_JOIN then
	elseif notify_type == notify_type_list.UNION_QUIT then
	elseif notify_type == notify_type_list.UNION_REJECT then
	elseif notify_type == notify_type_list.UNION_APPLYFOR_SUCC then
	elseif notify_type == notify_type_list.MEMBER_ADD or notify_type == notify_type_list.MEMBER_REMOVE then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		if 0 ~= role_vo.guild_id then
			self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
			self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, GuildDataConst.GUILDVO.guild_id)
		end
	elseif notify_type == notify_type_list.MEMBER_SOS then
	elseif notify_type == notify_type_list.MEMBER_HUNYAN then
	end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO, main_role_vo.guild_id)--仙盟申请列表
end

-- 玩家退出仙盟结果
function GuildWGCtrl:OnExitGuild(protocol)
	--退出仙盟成功
	if protocol.ret == 0 then
		MainuiWGCtrl.Instance:ShowRedBtn()
		TaskWGCtrl.Instance:UpdateTaskPanelShow()
	end
	self.view:SetTabVisible()
	self.view:OnTabChangeHandler(TabIndex.guild_guildlist)
	RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
end

-- 管理员踢玩家结果返回
function GuildWGCtrl:OnKickoutGuild(protocol)
	if 0 == protocol.ret then 						-- 0：成功 其它失败
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.KickoutGuild)
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		if 0 ~= role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
		end
	end
end

-- 管理员任命玩家结果返回
function GuildWGCtrl:OnAppointGuild(protocol)
	if 0 == protocol.ret then 						-- 0：成功 其它失败
		if nil ~= self.pop_appoint_view and true == self.pop_appoint_view:IsOpen() then
			self.pop_appoint_view:Close()
		end
		local post_authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[protocol.post]
		if nil ~= post_authority then
			if post_authority.post == Language.Guild.MengZhu then -- 盟主转让，成员菜单按钮需要重新生成
				self.view:DestroyMemberMenuView()
			end
		end
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.AppointSuccess)
	end
end

-- 修改仙盟公告结果
function GuildWGCtrl:OnChangeNotice(protocol)
	if 0 == protocol.ret then 						-- 0：成功 其它失败
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ModNoticeResult)
		self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, protocol.guild_id)
	end
end

-- 仙盟基本信息
function GuildWGCtrl:OnGuildInfo(protocol)--sss
	local guildvo = {}
	guildvo.guild_id = protocol.guild_id
	guildvo.guild_name = protocol.guild_name
	guildvo.guild_level = protocol.guild_level
	guildvo.guild_exp = protocol.guild_exp
	guildvo.guild_max_exp = protocol.guild_max_exp
	guildvo.cur_member_count = protocol.cur_member_count
	guildvo.max_member_count = protocol.max_member_count
	guildvo.tuanzhang_uid = protocol.tuanzhang_uid
	guildvo.tuanzhang_name = protocol.tuanzhang_name
	guildvo.tuanzhang_prof = protocol.tuanzhang_prof
	guildvo.tuanzhang_sex = protocol.tuanzhang_sex
	guildvo.create_time = protocol.create_time
	guildvo.camp = protocol.camp
	guildvo.vip_levemy_guild_lv_rankmy_guild_lv_rankmy_guild_lv_rankmy_guild_lv_rankl = protocol.vip_level
	guildvo.applyfor_setup = protocol.applyfor_setup
	guildvo.guild_notice = protocol.guild_notice
	guildvo.auto_kickout_setup = protocol.auto_kickout_setup
	guildvo.applyfor_need_capability = protocol.applyfor_need_capability
	guildvo.applyfor_need_level = protocol.applyfor_need_level
	guildvo.guild_callin_times = protocol.callin_times
	guildvo.my_lucky_color = protocol.my_lucky_color
	guildvo.guild_money = protocol.guild_money
	guildvo.flag_id = protocol.flag_id
	guildvo.flag_color = protocol.flag_color
	guildvo.flag_name = protocol.flag_name
	guildvo.guild_contributeTimes = protocol.guild_contributeTimes
	
	-- guildvo.guild_notice = ChatFilter.Instance:Filter(guildvo.guild_notice)
	self.data:SetGuildActiveDegree(protocol.active_degree)
	guildvo.hall_level_list = protocol.hall_level_list
	guildvo.stuff_list = protocol.stuff_list

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if nil == role_vo then
		return
	end
	
	RoleWGData.Instance:CacheAttr("guild_level", guildvo.guild_level) -- 缓存仙盟等级
	if guildvo.guild_id == role_vo.guild_id then
		for k, v in pairs(guildvo) do
			GuildDataConst.GUILDVO[k] = v
		end
		if GuildDataConst.GUILD_IOPEN.ActivityGuardView then
			self.view:SelectIndex(GuildDataConst.TABBAR_CONTENT.Guild_Activity)
		elseif GuildDataConst.GUILD_IOPEN.ActivityMonsterView then
			self.view:SelectIndex(GuildDataConst.TABBAR_CONTENT.Guild_Activity)
			GuildDataConst.GUILD_IOPEN.ActivityMonsterView = false
		else
			self:GuildViewFlush(TabIndex.guild_info)
		end
	end

	if GuildDataConst.GUILD_IOPEN.InfoView then
		self:OpenInfoGuildView(guildvo)
		GuildDataConst.GUILD_IOPEN.InfoView = false
	end
	if GuildDataConst.GUILD_IOPEN.FlushGuildListShow then
		self:FlushGuildListShow(guildvo)
		GuildDataConst.GUILD_IOPEN.FlushGuildListShow = false
	end
	self:UpdataRemindNum()
	RemindManager.Instance:Fire(RemindName.Guild_Skill)--帮派技能
end

-- 所有仙盟信息列表
function GuildWGCtrl:OnAllGuildInfoList(protocol)
	self.data:SetGuildListData(protocol.info_list)
	if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end

    if self.view:IsOpen() then
  		self.view:Flush(TabIndex.guild_guildlist)
  	end
end

-- 管理员收到的申请加入仙盟列表
function GuildWGCtrl:OnGuildApplyForList(protocol)
	local apply_list = GuildDataConst.GUILD_APPLYFOR_LIST
	apply_list.count = protocol.count
	apply_list.list = protocol.apply_list

	if nil ~= self.pop_apply_for_view then
		self:MainuiTipApplyfor(protocol.count > 0 and 1 or 0)
		if self.pop_apply_for_view:IsOpen() then
			self.pop_apply_for_view:Flush()
		end
	end
	self:GuildViewFlush(TabIndex.guild_info)
	RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
	-- RemindManager.Instance:Fire(RemindName.Guild_ZongLan)--仙盟综览
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_GaiKuang)--综览概况
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_RedBag)--综览概况
end

-- 仙盟事件列表
function GuildWGCtrl:OnGuildEventList(protocol)
	local event_list = GuildDataConst.GUILD_EVENT_LIST
	event_list.count = protocol.count
	event_list.list = protocol.event_list
	if self.view:IsOpen() then
		self.view:Flush(math.floor(TabIndex.guild_history))
	end
	self.view:FlushCangKuList()
end

-- 仙盟成员列表
function GuildWGCtrl:OnGuildMemberList(protocol)
	local member_list = GuildDataConst.GUILD_MEMBER_LIST
	member_list.count = protocol.count
	member_list.list = protocol.member_list

	if self.pop_guild_info_view:IsOpen() then
		self.pop_guild_info_view:GetGuildList() --刷新帮派弹出框的成员列表
	end

	if self.view:IsOpen() then
		self.view:Flush()
		self.view:OnFlushMember()
	end

	local is_has_scmember_info = self:GetIsHasScmemberInfo()
	if nil == is_has_scmember_info then
		is_has_scmember_info = true
		self:DeleteMemberDefault()
	end
end

-- 回复加入申请通知
function GuildWGCtrl:OnApplyForJoinGuildAck(protocol)
	if 1 == protocol.result then 						-- 1：拒绝 0：失败
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.RefuseJoinGuild, protocol.guild_name))
		-- RemindManager.Instance:Fire(RemindName.ZhuZaiShenDian)
		RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)
	end
end

-- 是否能够弹劾盟主结果返回
function GuildWGCtrl:OnGuildCheckCanDelate(protocol)
	if 1 == protocol.can_delate then 						-- 1：成功 其它失败
		self.view:OnConfirmTanHeMengZhuHandler()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.DontDelate)
	end
end

-- 仙盟操作结果返回
function GuildWGCtrl:OnGuildOperaSucc(protocol)
	local type_list = GuildDataConst.GUILD_OPERA_TYPE
	local guildvo = GuildDataConst.GUILDVO
	local info_type= GuildDataConst.GUILD_INFO_TYPE
	if protocol.opera_type == type_list.APPLY_SET then
		self.pop_setting_view:Close()
		self:SendGetGuildInfoReq(info_type.ALL_GUILD_BASE_INFO, guildvo.guild_id)
	elseif  protocol.opera_type == type_list.CALL_IN then
		self.pop_enlist_view:Close()
	end
end

--=================仙盟邀请=================--

-- 邀请好友加入仙盟
function GuildWGCtrl:SendInviteGuild(beinvite_uid)
	local function callback()
		local guild_id = GameVoManager.Instance:GetMainRoleVo().guild_id
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSInviteGuild)
		send_protocol.guild_id = guild_id
		send_protocol.beinvite_uid = beinvite_uid
		send_protocol:EncodeAndSend()
	end
	OperateFrequency.Operate(callback, "inviteguide")
end

-- 邀请加入仙盟通知
function GuildWGCtrl:OnInviteNotify(protocol)
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.Guild)
	if is_open then
		self.data:SetOnInviteNotify(protocol)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.GUILD_INVITE_HINT, 1,BindTool.Bind1(self.OpenGuildInviteHint, self))
	end
end

function GuildWGCtrl:InviteHandler(data, result)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSInviteGuildAck)
	send_protocol.guild_id = data.guild_id
	send_protocol.invite_uid = data.invite_uid
	send_protocol.result = result
	send_protocol:EncodeAndSend()
	-- self.data:SetOnInviteNotify({})
end


-- 帮派求救
function GuildWGCtrl:SendSendGuildSosReq(sos_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSendGuildSosReq)
	protocol.sos_type = sos_type
	protocol:EncodeAndSend()
end

-- 帮派求救
function GuildWGCtrl:OnSCGuildMemberSos(protocol)
	if protocol.member_uid == GameVoManager.Instance:GetMainRoleVo().role_id or
	Scene.Instance:GetSceneType() ~= SceneType.Common then
		return
	end

	local icon_list = MainuiWGCtrl.Instance:GetTipIconList(MAINUI_TIP_TYPE.YUAN)
	if icon_list ~= nil then
		for k,v in pairs(icon_list) do
			if v:GetData() and v:GetData().param and v:GetData().param.member_uid == protocol.member_uid and v:IsVisible() then --不重复添加
				return
			end
		end
	end

	local data = {}
	data.x = protocol.member_pos_x
	data.y = protocol.member_pos_y
	data.member_uid = protocol.member_uid
	data.scene_id = protocol.member_scene_id
	data.name = protocol.member_name
	data.camp = GameVoManager.Instance:GetMainRoleVo().camp
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.YUAN, 3, BindTool.Bind2(self.QiuJiuHandler, self, data), data)
end

function GuildWGCtrl:OnGuildFBPassInfo(protocol)
	self.data:SetGuildFbIsPass(protocol.is_pass)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ShiLian)
end

-- 仙盟守护场景信息下发
function GuildWGCtrl:OnGuildFBInfo(protocol)
	local guild_fb_data = {}
	guild_fb_data.notify_reason = protocol.notify_reason
	guild_fb_data.curr_wave =  protocol.curr_wave
	guild_fb_data.next_wave_time =  protocol.next_wave_time
	guild_fb_data.wave_enemy_count =  protocol.wave_enemy_count
	guild_fb_data.wave_enemy_max =  protocol.wave_enemy_max
	guild_fb_data.is_pass =  protocol.is_pass
	guild_fb_data.is_finish =  protocol.is_finish
	guild_fb_data.pass_time = protocol.pass_time
	guild_fb_data.hp = protocol.hp
	guild_fb_data.max_hp = protocol.max_hp
	guild_fb_data.exp_total = protocol.exp_total
	guild_fb_data.doorHp = protocol.doorHp
	guild_fb_data.doorMaxHp = protocol.doorMaxHp
	guild_fb_data.rankList = protocol.rankList

	guild_fb_data.total_gold_guwu_tims = protocol.total_gold_guwu_tims 	--场景内总的元宝鼓舞次数
	guild_fb_data.total_coin_guwu_tims = protocol.total_coin_guwu_tims 	--场景内总的铜币鼓舞次数

	guild_fb_data.gold_guwu_times = protocol.gold_guwu_times 			--已经元宝鼓舞次数
	guild_fb_data.coin_guwu_times = protocol.coin_guwu_times 			--已经铜币鼓舞次数

	guild_fb_data.guild_rank_pos = protocol.guild_rank_pos  			--仙盟排名
	guild_fb_data.active_reward_count = protocol.active_reward_count 	--活跃奖励份数
	guild_fb_data.item_wrapper_list = protocol.item_wrapper_list
	if SceneType.GuildMiJingFB ~= Scene.Instance:GetSceneType() then
		return
	end

	GuildWGData.Instance:SetGuildMiJingSceneInfo(guild_fb_data)	-- 设置数据
	FuBenWGCtrl.Instance:SetGuildMjViewData(guild_fb_data)         -- 刷新界面
	FuBenWGCtrl.Instance:UpdataTaskFollow()

	GuildWGCtrl.Instance:FlushGuWuView()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		local t = {}
		t.scene_type = SceneType.GuildMiJingFB
		t.coin_guwu_count = 0
		t.gold_guwu_count = 0
		GlobalEventSystem:Fire(OtherEventType.FB_GUWU_CHANGE, t)
	end)

	if 1 == guild_fb_data.is_finish then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(TimeWGCtrl.Instance:GetServerTime() + 15)
		FuBenPanelWGCtrl.Instance:SetCountDowmTimer(TimeWGCtrl.Instance:GetServerTime() + 15,function ()
			FuBenWGCtrl.Instance:SendLeaveFB()
		end)
	end
end

-- 仙盟守护仙盟排名信息(本服)
function GuildWGCtrl:OnSCGuildFbRankInfo(protocol)
	--print_error("仙盟排行信息", protocol)
	GuildWGData.Instance:SetGuildFbRankInfo(protocol)
end
-- 仙盟守护仙盟排名信息(跨服)
function GuildWGCtrl:OnSCGuildFbCrossRankInfo(protocol)
	--print_error("仙盟跨服排行信息", protocol)
	GuildWGData.Instance:SetGuildFbRankInfo(protocol)
end

-- 仙盟守护排名信息 和 个人排名列表
function GuildWGCtrl:OnSCGuildFbDetailInfo(protocol)
	--print_error("个人排名列表", protocol)
    GuildWGData.Instance:SetGuildFbPersonRankInfo(protocol)
end

-- 仙盟试炼个人信息下发
function GuildWGCtrl:OnSCGuildFBRoleInfo(protocol)
	GuildWGData.Instance:SetGuildMiJingRoleInfo(protocol)
end

-- 仙盟副本守卫位置下发
function GuildWGCtrl:OnGuildFBGuardPos(protocol)
	-- if SceneType.GuildMiJingFB ~= Scene.Instance:GetSceneType() then
	-- 	return
	-- end
	-- local scene_logic = Scene.Instance:GetSceneLogic()
	-- scene_logic:OnAutoMoveToGuardPos(protocol.pos_x, protocol.pos_y)
end

-- 仙盟守护副本中排名（本服）
function GuildWGCtrl:OnSCGuildFbInFBRankInfo(protocol)
	GuildWGData.Instance:SetGuildShouHuInFBRankInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GuildShowHuRankView)
end

-- 仙盟守护副本中排名（跨服）
function GuildWGCtrl:OnSCGuildFbInFBCrossRankInfo(protocol)
	GuildWGData.Instance:SetGuildShouHuInFBRankInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GuildShowHuRankView)
end


-- 仙盟boss我的人物信息下发
function GuildWGCtrl:OnGuildBossRoleInfo(protocol)
	 GuildBossWGData.Instance:SetMyGuildBossPersonRankInfo(protocol)
	 --下发刷新

	 ViewManager.Instance:FlushView(GuideModuleName.GuildBossTaskView)
end

-- 仙盟boss伤害排行下发
function GuildWGCtrl:OnGuildBossHurtRankInfo(protocol)
	GuildBossWGData.Instance:SetGuildBossPersonRankInfo(protocol)
	--下发刷新
	ViewManager.Instance:FlushView(GuideModuleName.GuildBossTaskView)
end

-- 仙盟boss击杀结算奖励
function GuildWGCtrl:OnGuildBossRewardInfo(protocol)
	GuildBossWGData.Instance:SetGuildBossRewardList(protocol) --设置奖励数据
	FuBenWGCtrl.Instance:ShowBossEndReward(protocol) --刷新title
end


-- 仙盟仓库信息
function GuildWGCtrl:OnGuildStorgeInfo(protocol)
	self.guild_cangku_data:SetOpengridCount(protocol.open_grid_count)
	self.guild_cangku_data:SetGuildStorgeList(protocol.item_list)
	local old_score = self.guild_cangku_data:GetStorgeScore()
	if old_score > 0 and protocol.guild_storage_score > old_score then
		local lerp_score = protocol.guild_storage_score - old_score
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.GetJiFen,lerp_score))
	end
	self.guild_cangku_data:SetStorgeScore(protocol.guild_storage_score)
	if self.view:IsLoadedIndex(TabIndex.guild_cangku) then
		self.view:Flush(math.floor(TabIndex.guild_cangku))
	end
	if self.cangku_convert_tips:IsOpen() then
		self.cangku_convert_tips:Flush()
	end
end

-- 仙盟仓库格子信息变化
function GuildWGCtrl:OnGuildStorgeChangeInfo(protocol)
	self.guild_cangku_data:SetGuildStorgeCell(protocol.item_data)
	if self.view:IsLoadedIndex(TabIndex.guild_cangku) then
		self.view:Flush(math.floor(TabIndex.guild_cangku))
	end
	self:AutoDestoryStorgeItem()
end

function GuildWGCtrl:OnGuildBonfireStatus(protocol)
	local open_times = protocol.open_times
	local finish_timestamp = protocol.finish_timestamp

	local time = finish_timestamp - TimeWGCtrl.Instance:GetServerTime()
	local openstatus = 0

	if finish_timestamp == 0 and open_times == 0 then
		openstatus = 0  							--活动还未开启
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GUILD_BONFIRE, ACTIVITY_STATUS.CLOSE, finish_timestamp)
	elseif time > 0 then
		openstatus = 1 								--活动正在开启
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GUILD_BONFIRE, ACTIVITY_STATUS.STANDY, finish_timestamp) -- 设置准备以显示倒计时
	elseif finish_timestamp == 0 and open_times == 1 then
		openstatus = 2 								--活动已经结束
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GUILD_BONFIRE, ACTIVITY_STATUS.CLOSE, finish_timestamp)
	end
	self.data:SetGuildBonfireStatus(openstatus, open_times)
	self.data:SetGuildBonfireFinsishTime(finish_timestamp)
end

GuildFbNotifyReason = {
	ENTER = 0,
	WAIT = 1,
	UPDATE = 2,
	FINISH = 3,
	KILL_MOSTER = 4,
	GUWU = 5,
	MAX = 6,
}

function GuildWGCtrl:OnGuildResetName(protocol)
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if protocol.guild_id == guild_id then
		RoleWGData.Instance:SetAttr("guild_name", protocol.new_name)
		GuildWGData.Instance:GuildSetAttr("guild_name", protocol.new_name)
		Scene.Instance:GetMainRole():ReloadUIGuildName()
		if self.view:IsOpen() and self.view:IsLoadedIndex(TabIndex.guild_info) then
			self.view:ChangeGuildName(protocol.new_name)
		end
	end
end

--兑换信息返回，在上线和改变时会主动下发
function GuildWGCtrl:OnGuildExchangeInfo(protocol)
	self.data:SetExchangeList(protocol.exchange_t)
	if self.view:IsOpen() then
		self.view:Flush(math.floor(TabIndex.guild_exchange))
	end
end

function GuildWGCtrl:OnJiuHuStart(finish_timestamp)
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.JIUHUI, ACTIVITY_STATUS.OPEN, finish_timestamp)

	self.is_jiuhui_glow = false
	self.is_jiuhui_view_flag = false

	local function act_end()
		self:StopJiuhuiAct()
	end

	local function act_update()
		if not self.is_jiuhui_glow then
			local icon_name = ACTIVITY_TYPE.JIUHUI
			local ui_icon = MainuiWGCtrl.Instance:GetUiIcon(icon_name)
			if ui_icon then
				MainuiWGCtrl.Instance:SetIconPEVisibleByName(true, icon_name) --粒子特效
				self.is_jiuhui_glow = true
			end
		end

		if not self.is_jiuhui_view_flag then
			if SceneType.GuildStation ~= Scene.Instance:GetSceneType() then
				local is_done = self:OpenJiuhuiView()
				if is_done then
					self.is_jiuhui_view_flag = true
				end
			else
				self.is_jiuhui_view_flag = true
			end
		end

		if RoleWGData.Instance.role_vo.guild_id  == 0 then   -- check is leave guild
			self:StopJiuhuiAct()
		end
	end

	CountDownManager.Instance:RemoveCountDown("guildparty_activity")
	CountDownManager.Instance:AddCountDown("guildparty_activity", act_update, act_end, finish_timestamp, nil, 1)
	MainuiWGCtrl.Instance:SetIconPEVisibleByName(true, ACTIVITY_TYPE.JIUHUI) --粒子特效
end

function GuildWGCtrl:StopJiuhuiAct()
	GuildWGData.Instance:SetGuildPartyStatus(1, 1)
	if self.view:IsOpen() then
		self.view:OnFlushActivityGuard()
	end
	if Scene.Instance:GetSceneLogic().UpdateGuardViewShowState then
		Scene.Instance:GetSceneLogic():UpdateGuardViewShowState()
	end
	MainuiWGCtrl.Instance:SetIconPEVisibleByName(false, ACTIVITY_TYPE.JIUHUI) --粒子特效
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.JIUHUI, ACTIVITY_STATUS.CLOSE, 0)
	CountDownManager.Instance:RemoveCountDown("guildparty_activity")
end

function GuildWGCtrl:QiuJiuHandler(data, tip_icon)
	local name = data.name
	local camp_corlr = CAMP_COLOR3B[data.camp]
	data.tip_icon = tip_icon
	local str = string.format(Language.Guild.QIUYUAN, camp_corlr, name)
	local alert_qiujiu = self:GetAlertQiuJiu()
	if alert_qiujiu == nil then
		alert_qiujiu = Alert.New()
	end
	alert_qiujiu:SetLableString(str)
	alert_qiujiu:SetOkFunc(BindTool.Bind2(self.QiuJiuAlertOk, self, data))
	alert_qiujiu:SetCancelFunc(BindTool.Bind2(self.QiuJiuAlertCancel, self, data))
	alert_qiujiu:Open()
end

function GuildWGCtrl:QiuJiuAlertOk(data)
	Scene.Instance:ClearAllOperate()
	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(data.tip_icon)
end

function GuildWGCtrl:QiuJiuAlertCancel(data)
	MainuiWGCtrl.Instance:RemoveTipIconByIconObj(data.tip_icon)
end

function GuildWGCtrl:OnSceneChangeComplete(old_scene_type,new_scene_type)
	if new_scene_type ~= SceneType.Common then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.YUAN,0)
	end

end

-- 仙盟酒会状态请求
function GuildWGCtrl:SendGuildPartyStartReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildPartyStartReq)
	send_protocol:EncodeAndSend()
end
-- 仙盟秘境开启请求
function GuildWGCtrl:SendGuildFbStartReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildFbStartReq)
	send_protocol:EncodeAndSend()
end
-- 仙盟秘境进入请求
function GuildWGCtrl:SendGuildFbEnterReq()
	if GuildWGData.Instance:GetIsCanJoinGuildShouHu() == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ExceedLimitLevel)
		return
	end
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildFbEnterReq)
	send_protocol:EncodeAndSend()
end
-- 仙盟篝火开启请求
function GuildWGCtrl:SendGuildBonfireStartReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBonfireStartReq)
	send_protocol:EncodeAndSend()
end
-- 仙盟篝火前往请求
function GuildWGCtrl:SendGuildBonfireGotoReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBonfireGotoReq)
	send_protocol:EncodeAndSend()
end
-- 仙盟篝火添加木材请求
function GuildWGCtrl:SendGuildBonfireAddMucaiReq(mucai_num)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBonfireAddMucaiReq)
	send_protocol.mucai_num = mucai_num
	send_protocol:EncodeAndSend()
end
-- 仙盟副本守卫位置请求
function GuildWGCtrl:SendGuildFBPosReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGetGuildFBGuardPos)
	send_protocol:EncodeAndSend()
end

-----------------------------------------------------------
-- 仙盟运势
-----------------------------------------------------------

-- 所有仙盟信息列表
function GuildWGCtrl:OnGuildLuckyInfo(protocol)
	GuildWGData.Instance:SetGuildLuckyInfo(protocol)
	self:GuildViewFlush()
	self:GuildViewFlush()
end

-- 仙盟祝福变更通知
function GuildWGCtrl:OnGuildLuckyChangeNotice(protocol)
	--GuildWGData.Instance:SetGuildLuckyInfo(protocol)

	local camp_color = CAMP_COLOR3B[RoleWGData.Instance.role_vo.camp or 0]
	local str = string.format(Language.Guild.ChangeNotice, C3b2Str(camp_color), protocol.bless_name, protocol.to_color)
	-- SystemHint.Instance:FloatingLabel(str)
end

-- XXX邀请自己祝福他
function GuildWGCtrl:OnInviteLuckyZhufu(protocol)
	GuildWGData.Instance:SetInviteLuckyZhufu(protocol)
	if protocol.req_invite_uid ~= RoleWGData.Instance:InCrossGetOriginUid() then
		if nil == self.yunshi_uid_list then
			self.yunshi_uid_list = {}
		end
		self.yunshi_uid_list[protocol.req_invite_uid] = {uid = protocol.req_invite_uid, name = protocol.req_invite_name}

		local zhufu_cfg = GuildWGData.Instance:GetLuckyZhufuCfg()		--配置表里面最大次数
		local daty_counter = GuildWGData.Instance:GetGuildZhuFuNum()	--今日已祝福次数

		--剩余祝福次数
		local zhufu_times = zhufu_cfg - daty_counter
		if zhufu_times > 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.YUNSHI, 3,
					BindTool.Bind2(self.ClickTipsHandler, self, self.yunshi_uid_list[protocol.req_invite_uid]))
		end
	end
end

function GuildWGCtrl:ClickTipsHandler(info, tip_icon)
	if info ~= nil and info.name ~= nil and info.uid ~= nil then
		local content = string.format(Language.Guild.InviteZhufu, info.name)
		-- 弹出二次确认提示框
		if self.alert_window == nil then
			self.alert_window = Alert.New()
		end
		self.alert_window:SetLableString(content)
		self.alert_window:SetOkFunc(BindTool.Bind1(function ()
			self:SendGuildZhuLuckyReq(info.uid)
			-- self.view:OnFlushActivityLuck()
			self:GuildViewFlush()
			self:GuildViewFlush()
		end, self))
		self.alert_window:Open()
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(tip_icon)
	end
end

function GuildWGCtrl:SendGuildZhuLuckyReq(be_zhufu_uid)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildZhuLuckyReq)
	send_protocol.be_zhufu_uid = be_zhufu_uid
	send_protocol:EncodeAndSend()
end

--请求仙盟所有成员运势信息
function GuildWGCtrl:SendGetAllGuildMemberLuckyInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGetAllGuildMemberLuckyInfo)
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:SendInviteLuckyZhufu(invite_uid)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSInviteLuckyZhufu)
	send_protocol.invite_uid = invite_uid		-- 0的话是一键所有人
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnGuildActiveDegreeInfo(protocol)
	local open_times = protocol.open_times
	local finish_timestamp = protocol.finish_timestamp

	local time = finish_timestamp - TimeWGCtrl.Instance:GetServerTime()
	local openstatus = 0

	if finish_timestamp == 0 and open_times == 0 then
		openstatus = 0  							--活动还未开启
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.XIONGSHOU, ACTIVITY_STATUS.CLOSE, finish_timestamp)
	elseif time > 0 then
		openstatus = 2 								--活动正在开启
		self:OnXiongShouStart(finish_timestamp)
	elseif finish_timestamp == 0 and open_times == 1 then
		openstatus = 1 								--活动已经结束
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.XIONGSHOU, ACTIVITY_STATUS.CLOSE, finish_timestamp)
	end
	-- Z主界面的提醒图标
	if 2 == openstatus and SceneType.GuildStation ~= Scene.Instance:GetSceneType() then
		local callback = function()
			FunOpen.Instance:OpenViewNameByCfg("guild#guild_activity#uin=guild_act_item,uip=7")
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIONGSHOU, 1, callback)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIONGSHOU, 0)
	end

	self.data:SetGuildActiveDegree(protocol.active_degree)
	self.data:SetXiongShouStatus(openstatus, open_times)
	self.data:SetXiongShouFinsishTime(finish_timestamp)

	if Scene.Instance:GetSceneLogic().UpdateGuardViewShowState then
		Scene.Instance:GetSceneLogic():UpdateGuardViewShowState()
	end
end

function GuildWGCtrl:OnXiongShouStart(finish_timestamp)
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.XIONGSHOU, ACTIVITY_STATUS.OPEN, finish_timestamp)
end

--召唤仙盟神兽
function GuildWGCtrl:SendGuildCallBeastReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildCallBeastReq)
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnGuildBossInfo(protocol)
	self.data:SetBossList(protocol.boss_list)
end

function GuildWGCtrl:OnOldBossRewardInfo(protocol)
	self.data:SetBossRewardList(protocol.record_list)
end

function GuildWGCtrl:OnGuildRoleOtherInfo(protocol)
	self.data:SetGuildRoleOtherInfo(protocol.guild_cd_time)
end

-- 打开面板请求boss信息，服务端上线不下发，当有改变时会主动下发
function GuildWGCtrl:SendReqBossInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossOperate)
	send_protocol.oper_type = 0
	send_protocol.boss_index = 0
	send_protocol:EncodeAndSend()
end

-- boss操作
function GuildWGCtrl:SendGotoKillBoss(index)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossOperate)
	send_protocol.oper_type = 1
	send_protocol.boss_index = index
	send_protocol:EncodeAndSend()
end

-- 领取boss奖励
function GuildWGCtrl:SendFetchBossReward(index)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBossOperate)
	send_protocol.oper_type = 2
	send_protocol.boss_index = index
	send_protocol:EncodeAndSend()
end

-- 仓库操作
function GuildWGCtrl:SendStorgeOperate(operate_type, param1, param2, param3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildStorageOperate)
	send_protocol.operate_type = operate_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol.param3 = param3 or 0
	send_protocol:EncodeAndSend()
end

-- 放进仓库
function GuildWGCtrl:SendStorgetPutItem(bag_index, num)
	self:SendStorgeOperate(GUILD_STORGE_OPERATE.GUILD_STORGE_OPERATE_PUTON_ITEM, bag_index, num)
end

-- 取出仓库
function GuildWGCtrl:SendStorgetOutItem(storge_index, num, item_id)
	self:SendStorgeOperate(GUILD_STORGE_OPERATE.GUILD_STORGE_OPERATE_TAKE_ITEM, storge_index, num, item_id)
end

-- 请求仓库信息
function GuildWGCtrl:SendSorgeReqInfo()
	self:SendStorgeOperate(GUILD_STORGE_OPERATE.GUILD_STORGE_OPERATE_REQ_INFO)
end

--销毁仓库物品
function GuildWGCtrl:SendStorgetDiscardItem(item_count, item_list)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildStorageDiscardItem)
	send_protocol.item_count = item_count or 0
	send_protocol.item_list = item_list or {}

	send_protocol:EncodeAndSend()
end

--批量捐献物品
function GuildWGCtrl:SendStorgetDonteItems(item_count, item_list)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildStorageDonateItems)
	send_protocol.item_count = item_count
	send_protocol.item_list = item_list
	send_protocol:EncodeAndSend()
end

-- 仙盟召集操作
function GuildWGCtrl:OnGuildCall(protocol)
	if self.guild_call_alert == nil then
		self.guild_call_alert = Alert.New()
	end
	self.guild_call_alert:SetLableString(Language.Guild.GuildCallFollow)
	self.guild_call_alert:SetOkFunc(BindTool.Bind1(function ()
		if protocol.target_sceneid >= 1300 and 1309 >= protocol.target_sceneid then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoFlyScene)
		else
			--print_error(protocol.target_sceneid, protocol.caller_x, protocol.caller_y)
			-- 直接飞到目的地
			TaskWGCtrl.Instance:JumpFly(protocol.target_sceneid, protocol.caller_x, protocol.caller_y, true)
			--TaskWGCtrl.Instance:SendFlyByShoe(protocol.target_sceneid, protocol.caller_x, protocol.caller_y)
			-- 走到目的地
			-- Scene.Instance:GetSceneLogic():FlyToPos(protocol.caller_x, protocol.caller_y, protocol.target_sceneid, SceneObjType.Common, false)
		end
	end, self))
	self.guild_call_alert:Open()
end

-- 仙盟召集请求
function GuildWGCtrl:SendGuildCallOperate(operate)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildCallOperate)
	protocol.operate = operate
	protocol:EncodeAndSend()
end

-- 帮派每日福利
function GuildWGCtrl:SendCSGuildDayReward()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildDayReward)
	protocol:EncodeAndSend()
end

-- 帮派领地福利
function GuildWGCtrl:SendCSGuildLingdiReward(type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildLingdiReward)
	protocol.seq = type
	protocol:EncodeAndSend()
end

-- 帮派招揽请求
function GuildWGCtrl:SendGuildCallUpOperate()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildCallUpOperate)
	protocol:EncodeAndSend()
end

-- 帮派鼓舞请求
function GuildWGCtrl:SendGuildShouHuOperate(opera_type, param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildFbGuWu)
	protocol.opera_type = opera_type
	protocol.param = param or 0
	protocol:EncodeAndSend()
end



--攻城战城主信息
function GuildWGCtrl:OnGongchengzhanInfo(protocol)
	self.data:OnGongChengZhanOwnerInfo(protocol)
	self:GuildViewFlush()
end

-------仙盟合并请求-----------------
function GuildWGCtrl:OpenMergeView()
	self:OpenGuildHeBingShowView()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.GUILDMERGE, 0)
end

function GuildWGCtrl:OnGuildCombineReq(protocol)
	--print_error("接收帮派合并的信息",protocol)
	self.data:SetGuildCombineReq(protocol)
	if self.guild_hebing_show:IsOpen() then
		self.guild_hebing_show:Flush()
	end
	GuildWGCtrl.Instance:OpenGuildHeBingShowView()
end

function GuildWGCtrl:SendGuildMergeReq(target_guild_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildCombineReq)
	send_protocol.target_guild_id = target_guild_id or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:SendGuildMergeAck(target_guild_id, is_agree)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildCombineAck)
	send_protocol.target_guild_id = target_guild_id or 0
	send_protocol.is_agree = is_agree or 0
	send_protocol.reserve_sh = 0
	send_protocol:EncodeAndSend()
end

----------------仙盟红包-------------------
function GuildWGCtrl:OnGuildRedPocketListInfo(protocol)
	self.data:SetRedPocketListInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_redpacket)
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_RedBag)
end

function GuildWGCtrl:OnGuildRedPocketDistributeInfo(protocol)
	self.data:SetRedPocketDistributeInfo(protocol)
	self.guild_redpacket_tips:Flush()
end

function GuildWGCtrl:SendGuildRedPocketOperate(operate_type, red_pocket_id, param1)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildRedPocketOperate)
	send_protocol.operate_type = operate_type or 0
	send_protocol.red_pocket_id = red_pocket_id or 0
	send_protocol.param1 = param1 or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:SendGuildVipRedPocketDistribute(red_pocket_sub_type, gold_num, can_fetch_times)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildVipRedPocketDistribute)
	send_protocol.red_pocket_sub_type = red_pocket_sub_type or 0
	send_protocol.gold_num = gold_num or 0
	send_protocol.can_fetch_times = can_fetch_times or 0
	send_protocol:EncodeAndSend()
end


-----------------仙盟战活动显示------------------------------
function GuildWGCtrl:OnGuildBattleMatchInfo(protocol)
	self.data:SetGuildBattleMatchInfo(protocol)
	if protocol.guild_fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_ACTIVITY_END
	or protocol.guild_fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_SECOND_FIGHT_END then
		ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRankInfoReq(GUILD_BATTLE_INFO_REQ_TYPE.GUILD_BATTLE_INFO_REQ_TYPE_RANK)
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local guild_post = role_vo.guild_post
		local is_mengzhu = guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
		if is_mengzhu then
			local type_oper = GUILD_BATTLE_REWARD_APPLY_OR_ALLOC.GUILD_BATTLE_REWARD_INFO_SEND
			GuildWGCtrl.Instance:SendCSGuildBattleRewardApply(type_oper)
		end
		GuildWGCtrl.Instance:SendCSGuildBattleRewardApply(GUILD_BATTLE_REWARD_APPLY_OR_ALLOC.GUILD_BATTLE_REWARD_INFO_IF_MEMBER)
	end
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_War)
	end
	if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end
end

function GuildWGCtrl:OnGuildBattleEndRankInfo(protocol)
	GuildBattleRankedWGCtrl.Instance:SetGuildBattleEndRankInfo(protocol)
	--胜利失败界面的弹窗
	GuildWGData.Instance:GetGuildEndData(protocol.rank_list,protocol.is_win,protocol.zone)
	-- GuildBattleRankedWGCtrl.Instance:OpenGuildBattleRankedEndView(protocol.rank_list,protocol.is_win)
	if GuildBattleRankedWGCtrl.Instance.view:IsOpen() then
		GuildBattleRankedWGCtrl.Instance.view:FlushRank()
	end
	if self.guild_battle_end_rank_view and protocol.is_win ~= -1 then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiWGCtrl.Instance:StopGuaji()
		-- GuildWGData.Instance:GetGuildEndData(protocol.rank_list,protocol.is_win,protocol.zone)
		self.guild_battle_end_rank_view:SetDataList(protocol.rank_list, protocol.is_win)
		self.guild_battle_end_rank_view:Open()

	end
end

------------------------------------------------------------------------
function GuildWGCtrl:SendJiuSheDaoFBEnter()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSJiuSheDaoFBEnterReq)
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCJiuSheDaoFBInfo(protocol)
	-- print_error(protocol)
	self.data:SetJiuSheDaoFBInfo(protocol)
	self.guild_jiushedaofb_follow_view:Flush()
	if protocol.notify_reason == 7 then
		-- local obj = Scene.Instance:GetRoleByObjId(protocol.dead_role_obj_id)
		-- if nil ~= obj then
		-- 	Scene.Instance:CreateFootPrint(obj, protocol.effect_id)
		-- end
	end
end

function GuildWGCtrl:SendJiuSheDaoFBHandler(request_reason, platform, gold_relive_pos_x, gold_relive_pos_y)
	-- print_error("SendJiuSheDaoFBHandler",request_reason)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSJiuSheDaoFBHandler)
	send_protocol.request_reason = request_reason or 0
	send_protocol.platform = platform or 0
	send_protocol.gold_relive_pos_x = gold_relive_pos_x or 0
	send_protocol.gold_relive_pos_y = gold_relive_pos_y or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCGuildTuanZhangBeenKilled(protocol)
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if fb_scene_cfg.be_killed_rumor ~= 1 then --or IS_ON_CROSSSERVER
		return
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()

	if role_vo.guild_id ~= 0 then
		local chat_ctrl = ChatWGCtrl.Instance
		local chat_data = ChatWGData.Instance
		if nil == chat_ctrl or nil == chat_data then
			return
		end
		local main_role = Scene.Instance.main_role
		if nil ~= main_role then
			local x, y = main_role:GetLogicPos()
			local scene_name = Scene.Instance:GetSceneName()
			local content = string.format(Language.Guild.TuanZhangBeenKilledText, Language.Guild.GuildPost[role_vo.guild_post] or "", role_vo.name, scene_name , protocol.killer_name, scene_name, x, y, Scene.Instance:GetSceneId(), main_role.vo.cur_plat_name, main_role.vo.current_server_id )
			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT, CHAT_MSG_RESSON.GUILD_TIPS,nil,true)
		end
	end
end

function GuildWGCtrl:OnSCJiuSheDaoFBJumpHandler(protocol)
	-- print_error("OnSCJiuSheDaoFBJumpHandler",protocol.obj_id)
	local scene_obj = Scene.Instance:GetObj(protocol.obj_id)
	if scene_obj ~= nil then
		if scene_obj:IsMainRole() == false then
			local role_x,role_y = scene_obj:GetLogicPos()
		    local distance = GameMath.GetDistance(role_x, role_y, protocol.pos_x, protocol.pos_y, true)
		    scene_obj:SetSpecialJumpSpeed(distance)
			scene_obj:DoSpecialJump(protocol.pos_x,protocol.pos_y)
		end
	end
end

------------------------------猎鲲------------------------------------------------------

--创建boss墓碑
function GuildWGCtrl:CreateLieKunFBBossStone(protocol)
	local boss_stone_list = Scene.Instance:GetBossStoneList()
	local dead_boss_list = {}
	for k,v in pairs(protocol.boss_next_flush_timestamp) do
		if v > 0 and k > 1 then   -- 鲲不需要墓碑
			local cfg = {}
			cfg.boss_id = protocol.boss_id[k].boss_id
			cfg.next_refresh_time = v
			table.insert(dead_boss_list,cfg)
		end
	end
	if #dead_boss_list <= 0 then return end
	local need_create_list = {}
	local need_create = true
	for k,v in pairs(dead_boss_list) do
		for k1,v1 in pairs(boss_stone_list) do
			if v1:GetVo().boss_id == v.boss_id then
				need_create = false
				break
			end
		end
		if need_create then
			table.insert(need_create_list,v)
		else
			need_create = true
		end
	end
	if #need_create_list <= 0 then return end
	local bossinfo_list = self.data:GetLieKunBossListCfgByZone()
	local monster_list = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
	local monster_cfg
	for i,v in ipairs(need_create_list) do
		for k1,v1 in pairs(bossinfo_list) do
			if v1.boss_id == v.boss_id then
				monster_cfg = monster_list[v.boss_id]
				local stone_vo = GameVoManager.Instance:CreateVo(BossStoneVo)   -- 创建一个VO对象  参数为vo类
				stone_vo.pos_x = v1.x_pos
				stone_vo.pos_y = v1.y_pos
				stone_vo.name = v1.boss_name or ""
				stone_vo.boss_level = monster_cfg.level
				stone_vo.live_time = v.next_refresh_time
				stone_vo.boss_id = v.boss_id
				stone_vo.obj_id = Scene.Instance:GetSceneClientId()
				Scene.Instance:CreateObj(stone_vo, SceneObjType.BossStoneObj)   -- 创建一个场景对象 参数为 vo对象 和场景对象的类型
			end
		end
	end
end

function GuildWGCtrl:OnSCCrossLieKunFBSceneInfo(protocol)
	-- print_error('OnSCCrossLieKunFBSceneInfo 猎鲲场景信息------------------- ',protocol)
	self.data:SetCrossLieKunFBSceneInfo(protocol)
	self.liekun_follow_view:FlushBossInfo()
	-- GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION)
	if Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN then
		Scene.Instance:GetSceneLogic():ChangeDoorState(protocol.guild_name[1])
		self:CreateLieKunFBBossStone(protocol)
		local target_obj = GuajiCache.target_obj
		if not target_obj or not target_obj.vo then return end
		local monster_id = target_obj.vo.monster_id
		local index
		if monster_id and monster_id ~= 0 then
			for k,v in pairs(protocol.boss_id) do
				if v.boss_id == monster_id then
					local guild_id = protocol.guild_id[k]
					if guild_id ~= 0 then
         	 			GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION,protocol.guild_name[k],Language.Guild.GuiShu)
						return
					end
				end
			end
		end
	end
end

function GuildWGCtrl:SetAscriptionText(target_obj)
	if Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN then
		local protocol = self.data:GetCrossLieKunFBSceneInfo()
		if not protocol then return end
		if not target_obj or not target_obj.vo then return end
		local monster_id = target_obj.vo.monster_id
		local index
		if monster_id and monster_id ~= 0 then
			for k,v in pairs(protocol.boss_id) do
				if v.boss_id == monster_id then
					local guild_id = protocol.guild_id[k]
					if guild_id ~= 0 then
         	 			GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION,protocol.guild_name[k],Language.Guild.GuiShu,true)
						return
					end
				end
			end
		end
	end
end

function GuildWGCtrl:SendCrossLieKunFBReq(liekun_type, param1, param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossLieKunFBReq)
	send_protocol.type = liekun_type or 0
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCCrossLieKunFBPlayerInfo(protocol)
	self.data:SetCrossLieKunFBPlayerInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_liekun)
end

function GuildWGCtrl:OnSCCrossLieKunFBGuildMsgInfo(protocol)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()

	if role_vo.guild_id ~= 0 then
		local chat_ctrl = ChatWGCtrl.Instance
		local chat_data = ChatWGData.Instance
		if nil == chat_ctrl or nil == chat_data then
			return
		end

		local content = ""
		local msg = ""
		local scene_name = Scene.Instance:GetSceneName()
		if protocol.zone == 0 then
			if protocol.boss_type == 0 then
				content = string.format(Language.Guild.LieKunBossBeenKilleedText1, scene_name, protocol.pos_x, protocol.pos_y, Scene.Instance:GetSceneId())
				msg = string.format(Language.Guild.Tip_LieKunBossBeenKilleedText1, scene_name, protocol.pos_x, protocol.pos_y)
			else
				content = string.format(Language.Guild.LieKunBossBeenKilleedText2, scene_name, protocol.pos_x, protocol.pos_y, Scene.Instance:GetSceneId())
				msg = string.format(Language.Guild.Tip_LieKunBossBeenKilleedText2, scene_name, protocol.pos_x, protocol.pos_y)
			end
		else
			if protocol.boss_type == 0 then
				content = string.format(Language.Guild.LieKunBossBeenKilleedText3, scene_name, protocol.pos_x, protocol.pos_y, Scene.Instance:GetSceneId())
				msg = string.format(Language.Guild.Tip_LieKunBossBeenKilleedText3, scene_name, protocol.pos_x, protocol.pos_y)
			else
				content = string.format(Language.Guild.LieKunBossBeenKilleedText2, scene_name, protocol.pos_x, protocol.pos_y, Scene.Instance:GetSceneId())
				msg = string.format(Language.Guild.Tip_LieKunBossBeenKilleedText2, scene_name, protocol.pos_x, protocol.pos_y)
			end
		end
		ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT)
		self.liekun_follow_view:ShowMsgView(msg)
	end
end

function GuildWGCtrl:OnSCCrossLieKunFBGatherInfo(protocol)
	-- print_error('OnSCCrossLieKunFBGatherInfo---',protocol)
	GuildWGData.Instance:SetLieKunGatherInfo(protocol.gather_info)

	local in_liekun = Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN
	if protocol.gather_succ == 1 and in_liekun then
		if Scene.Instance:GetSceneLogic():GetGathering() then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end
	if in_liekun then
		Scene.Instance:GetSceneLogic():SetGathering(false)
	end
end

function GuildWGCtrl:OnSCCrossLieKunBossHurtInfo(protocol)
	-- print_error('OnSCCrossLieKunBossHurtInfo------',protocol)
	GuildWGData.Instance:SetCrossLieKunHurtInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.LieKunSingleOutPutTips)
	ViewManager.Instance:FlushView(GuideModuleName.LieKunGuideOutPutTips)
end

function GuildWGCtrl:OnSCCrossLieKunFBScoreInfo(protocol)
	-- print_error('OnSCCrossLieKunFBScoreInfo------',protocol)
	GuildWGData.Instance:SetCrossLieKunScoreInfo(protocol)
	self.liekun_follow_view:FlushScoreInfo()
end
--仙盟建设任务相关
function GuildWGCtrl:SendCancelGuildBuildTask(task_id,extra_vale)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGiveUpGuildBuildTask)
	send_protocol.task_id = task_id or 0
	send_protocol.extra_vale = extra_vale or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:SendFlushGuildBuildTask()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSRefreshGuildBuildTask)
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCGuildBuildTaskInfo(protocol)
	self.data:SetGuildBuildTaskInfo(protocol)
	self:FlushGuildBuildTaskView("from_guild_protocol")
	-- self:GuildViewFlush(GuildView.TABINDEX.TASK)
	-- RemindManager.Instance:Fire(RemindName.Guild_Build_Task)--仙盟建设
	-- RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
	TaskWGCtrl.Instance:UpdateTaskPanelShow()
	-- if self.data:IsNeedOpenOutScentEvent() then
		self:CreatLeventSceneEvent(self.data:IsNeedOpenOutScentEvent())
	-- end
end

function GuildWGCtrl:SendGuildBuildTaskStatesChange(task_id,extra_vale)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildBuildTaskStatesChange)
	send_protocol.task_id = task_id or 0
	send_protocol.extra_vale = extra_vale or 0
	send_protocol:EncodeAndSend()
end

--仙盟工资任务相关
function GuildWGCtrl:SendGetGuildWage(task_type,extra_vale)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildWageTaskFetchReward)
	send_protocol.type = task_type or 0
	send_protocol.extra_vale = extra_vale or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCGuildWageTaskInfo(protocol)
	self.data:SetGuildWageTaskInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_wage)
	RemindManager.Instance:Fire(RemindName.Guild_Wage)
	RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
end


----------------------------------------仙盟成员战力排行---------------------------------------------------
function GuildWGCtrl:OnSCGuildMemberCapabilityRank(protocol)
	self.data:SetGuildMemberRankInfo(protocol)
	ServerActivityWGCtrl.Instance:FlushServerActTabView(TabIndex.act_xianmeng_fengbang, "guild_rank", {"guild_rank"})
	ViewManager.Instance:FlushView(GuideModuleName.ActTitleAddView, 0, "guild_rank", {"guild_rank"})
end

function GuildWGCtrl:SendCSGuildMemberCapabilityRank()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildMemberCapabilityRank)
	send_protocol:EncodeAndSend()
end

-------------------------------------仙盟技能信息----------------------------------------------------------
function GuildWGCtrl:SendCSGuildSkillUpgradeReq(skill_id)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildSkillUpgradeReq)
	send_protocol.skill_id = skill_id or 0
	send_protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCGuildSkillInfo(protocol)
	self.data:SetGuildSkillInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_skill)
	end
	RemindManager.Instance:Fire(RemindName.Guild_Skill)--帮派技能
end



function GuildWGCtrl:CSApplyForTuanZhangAgentReq(operate_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSApplyForTuanZhangAgentReq)
	send_protocol.opera_type = operate_type
	send_protocol:EncodeAndSend()
end
function GuildWGCtrl:OnSCTuanZhangAgentInfo(protocol)
	self.data:SetGuildAgentInfo(protocol)
	-- if self.view:IsOpen() then
	-- 	self.view:Flush()
	-- end
	self.apply_tuanzhang_agent:Flush()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 0 ~= role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
	end
end

-- 更改仙盟旗帜信息
function GuildWGCtrl:SendChangeFlagReq(guild_id, flag_id, flag_color, flag_name)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildChangeFlag)
	protocol.guild_id = guild_id
	protocol.flag_id = flag_id
	protocol.flag_color = flag_color
	protocol.flag_name = flag_name
	protocol:EncodeAndSend()
end


---------仙盟争霸奖励分配
function GuildWGCtrl:SendCSGuildBattleRewardApply(type_oper,role_id,item_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildBattleRewardApply)
	protocol.type_oper = type_oper
	protocol.role_id = role_id or 0
	protocol.item_id = item_id or 0
	protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCGuildBattleRewardAlloc(protocol)
	GuildBattleRankedWGData.Instance:SetSCGuildBattleRewardAlloc(protocol)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)--活动争霸
	self:GuildViewFlush(TabIndex.guild_War)
	if self.guild_war_fenpei_view:IsOpen() then
		self.guild_war_fenpei_view:Flush()
	end
	ZhuZaiShenDianWGCtrl.Instance:FlushRewardView()
end

function GuildWGCtrl:OnSCGuildBattleRewardInfo(protocol)
	GuildBattleRankedWGData.Instance:SetSCGuildBattleRewardInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)--活动争霸
	self:GuildViewFlush(TabIndex.guild_War)
	if self.guild_war_fenpei_view:IsOpen() then
		self.guild_war_fenpei_view:Flush()
	end
	ZhuZaiShenDianWGCtrl.Instance:FlushRewardView()
end

function GuildWGCtrl:OnSCCrossXianmengzhanMatchState(protocol)
	self.data:SetKFXianmengzhanMatchInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_War)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)--活动争霸
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type == SceneType.XianMengzhan then
		GuildBattleRankedWGData.Instance:SetGuildBossInfo(protocol)
		GuildBattleRankedWGCtrl.Instance:FlushGuildBattleRankedBossInfo()
		local end_time = protocol.m_curr_match_end_time
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if end_time > server_time then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(end_time,true)
		end
	end
end

function GuildWGCtrl:OnSCCrossXianmengzhanGuildRank(protocol)
	self.data:SetKFXianmengzhanGuildRank(protocol)
	self:GuildViewFlush(TabIndex.guild_War)
	ZhuZaiShenDianWGCtrl.Instance:FlushRewardView()
	
	if self.guild_war_zhanbao_view:IsOpen() then
		self.guild_war_zhanbao_view:Flush()
	end
end


function GuildWGCtrl:OnSCCrossXianmengzhanKeepWinInfo(protocol)
	self.data:SetKFXianmengzhanKeepWinInfo(protocol)
end

function GuildWGCtrl:OnSCCrossXianmengzhanOneMatchFinish(protocol)
	GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE)
end


-- opera_type = 1 请求比赛进度
-- opera_type = 100 请求进入比赛副本
function GuildWGCtrl:ReqCrossXianmengzhanOpera(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossXianmengzhanOpera)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end


----------------------每日宝箱-------------------

function GuildWGCtrl:SendCSDailyTreasureOperate(operate_type,param1,param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDailyTreasureOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function GuildWGCtrl:OnSCDailyTreasureInfo(protocol)
	local old_info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	local need_change_code = false
	if old_info and old_info.quality and protocol.quality > old_info.quality then
		need_change_code = true
	end
	local old_paassword = GuildBaoXiangWGData.Instance:GetPasswordCode()
	if not old_paassword or tonumber(old_paassword) ~= protocol.password then
		GuildBaoXiangWGData.Instance:SetInputCode(nil)
		GuildBaoXiangWGData.Instance:SetPasswordCode(protocol.password)
	end

	self.guild_baoxiang_data:SetSCDailyTreasureInfo(protocol)
	if need_change_code and self.baoxiang_code_view and self.baoxiang_code_view:IsOpen() and self.baoxiang_code_view:IsLoaded() then
		self.baoxiang_code_view:InitCodeList()
	end
	self:GuildViewFlush(TabIndex.guild_baoxiang)
	local need_paly_anim = GuildBaoXiangWGData.Instance:GetNeedPlayAnim()
	if need_paly_anim then
		ViewManager.Instance:Open(GuideModuleName.BaoXiangGetResultView)
		GuildBaoXiangWGData.Instance:SetNeedPlayAnim(false)
	end
	self:BaoXiangReaInvateTip()
	RemindManager.Instance:Fire(RemindName.BiZuoBaoXiangRemind)
end

function GuildWGCtrl:OnSCDailyTreasureAllyItemInfo(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureAllyItemInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_baoxiang)
	self:BaoXiangReaInvateTip()
end

function GuildWGCtrl:OnSCDailyTreasureAllyItemUpdate(protocol)
	local item = protocol.item
	local help_refresh_uid = GuildBaoXiangWGData.Instance:GetHelpRefreshUid(item.uid)
	if help_refresh_uid then
		local old_data = GuildBaoXiangWGData.Instance:GetAllItemInfoListByUid(item.uid)
		if old_data and old_data.quality ~= item.quality then
			local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(item.quality)
			local baoxiang_name = treasure_cfg and treasure_cfg.name or ""
			local color = treasure_cfg and treasure_cfg.common_color or 2
			baoxiang_name = ToColorStr(baoxiang_name,ITEM_COLOR[color])
			local str = string.format(Language.BiZuoBaoXiang.RefreshSucc,baoxiang_name)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.RefreshFail)
		end
		GuildBaoXiangWGData.Instance:SetHelpRefreshUid(item.uid,false)
		GuildBaoXiangWGData.Instance:SetClickHelpRefreshUid(item.uid,false)
	end

	self.guild_baoxiang_data:SetSCDailyTreasureAllyItemUpdate(protocol)
	self:GuildViewFlush(TabIndex.guild_baoxiang)
	if self.baoxiang_flush_view:IsLoaded() then
		self.baoxiang_flush_view:Flush()
	end
	self:BaoXiangReaInvateTip()
end

function GuildWGCtrl:OnSCDailyTreasureRecordInfo(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureRecordInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_baoxiang)
end

local TEST_NUM = 1
function GuildWGCtrl:TestRecordAdd()

	local protocol = {}
	protocol.item = {}
	protocol.item.uid = 1111111
	protocol.item.name = "aaaa" .. TEST_NUM
	protocol.item.new_quality = 4
	protocol.item.old_quality = 3
	protocol.item.is_refuse = 1
	protocol.item.record_time = 1
	self:OnSCDailyTreasureRecordAdd(protocol)
	TEST_NUM = TEST_NUM + 1
end

function GuildWGCtrl:OnSCDailyTreasureRecordAdd(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureRecordAdd(protocol)
	self:GuildViewFlush(TabIndex.guild_baoxiang)
	local item = protocol.item
	local new_quality = item.new_quality
	local old_quality = item.old_quality
	local max_quality = GuildBaoXiangWGData.Instance:GetMaxQuality()
	if new_quality ~= old_quality then
		local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(new_quality)
		local color = treasure_cfg and treasure_cfg.common_color or 1
		local bx_name = ToColorStr(treasure_cfg and treasure_cfg.name or "",ITEM_COLOR[color])
		local str = string.format(Language.BiZuoBaoXiang.RefreshChuanWen,item.name,bx_name)
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		if str ~= "" then
			local msg_info = ChatWGData.CreateMsgInfo()
			msg_info.from_uid = 0
			msg_info.username = ""
			msg_info.sex = 0
			msg_info.camp = 0
			msg_info.prof = 0
			msg_info.authority_type = 0
			msg_info.tuhaojin_color = 0
			msg_info.level = 0
			msg_info.vip_level = 0
			msg_info.msg_reason = CHAT_MSG_RESSON.NORMAL
			msg_info.channel_type = CHANNEL_TYPE.CHUAN_WEN
			msg_info.content = str
			msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
			msg_info.is_add_team = false
			msg_info.title_text = Language.ChannelColor2[10]
			ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
			GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
			-- if not self.view:IsOpen() or self.view:GetShowIndex() ~= TabIndex.guild_baoxiang then
			-- 	GuildBaoXiangWGData.Instance:SetBaoXiangRemind(0)
			-- end
			GuildBaoXiangWGData.Instance:AddBaoXiangRefreshData(item)
			if self.baoxiang_refresh_alert and not self.baoxiang_refresh_alert:IsOpen() then
				local data = GuildBaoXiangWGData.Instance:GetBaoXiangRefreshData()
				self.baoxiang_refresh_alert:SetData(data)
				self.baoxiang_refresh_alert:Open()
			end
		end
	end
end

function GuildWGCtrl:OnSCDailyTreasureAllyItemRemove(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureAllyItemRemove(protocol)
end


function GuildWGCtrl:OnSCDailyTreasureBeReqHelpRefreshInfo(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureBeReqHelpRefreshInfo(protocol)
	self:BaoXiangReaInvateTip()
end


function GuildWGCtrl:OnSCDailyTreasureBeReqHelpRefreshUpdate(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureBeReqHelpRefreshUpdate(protocol)
	local is_add = protocol.is_add
	self:BaoXiangReaInvateTip()
	if self.baoxiang_flush_view:IsLoaded() then
		self.baoxiang_flush_view:Flush()
	end
end

function GuildWGCtrl:OnSCDailyTreasureReqHelpRefreshInfo(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureReqHelpRefreshInfo(protocol)
	self:GuildViewFlush(TabIndex.guild_baoxiang)
end

function GuildWGCtrl:OnSCDailyTreasureReqHelpRefreshUpdate(protocol)
	self.guild_baoxiang_data:SetSCDailyTreasureReqHelpRefreshUpdate(protocol)
	if self.baoxiang_flush_request_view:IsLoaded() then
		self.baoxiang_flush_request_view:Flush()
	end
	self:GuildViewFlush(TabIndex.guild_baoxiang)
end

function GuildWGCtrl:TestHelpRefreshReward()
	local protocol = {}
	protocol.name = 111111
	protocol.uid = 111111
	protocol.quality = 3
	self:OnSCDailyTreasureReqHelpRefreshReward(protocol)

end

function GuildWGCtrl:OnSCDailyTreasureReqHelpRefreshReward(protocol)
	self.guild_baoxiang_data:AddBaoXiangThankData(protocol)
	if self.baoxiang_thank_tips and not self.baoxiang_thank_tips:IsOpen() then
		local data = GuildBaoXiangWGData.Instance:GetBaoXiangThankData()
		self.baoxiang_thank_tips:SetData(data)
		self.baoxiang_thank_tips:Open()
	end
end

function GuildWGCtrl:BaoXiangReaInvateTip()
	local check_fun = function ()
		local main_role = Scene.Instance:GetMainRole()
		if not main_role then
			return
		end
		local main_vo = main_role.vo
		local guild_id = main_vo.guild_id
		local be_req_list = GuildBaoXiangWGData.Instance:GetCanBeReqHelpRefreshUidList()
		local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()
		local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
		local refresh_num = info and info.help_refresh_times or 0
		local has_num = other_cfg.help_refresh_times - refresh_num
		if not IsEmptyTable(be_req_list) and has_num > 0 and guild_id > 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Guild_BaoXaing_Req, 1,function ()
				ViewManager.Instance:Open(GuideModuleName.BaoXiangFlushView)
				return true
			end)
		else
			MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.Guild_BaoXaing_Req)
		end
	end
	--多个下发协议的地方会调用 添加延时刷新 避免同一帧执行
	ReDelayCall(self,check_fun, 1, "BaoXiangReaInvateTipDelay")
end

----------------------每日宝箱END-------------------


function GuildWGCtrl:OnSendFakeGuildOprate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFakeGuildOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function GuildWGCtrl:OnFakeGuildListInfo(protocol)
	if GuildWGData.Instance:GetIsShieldFakeGuild() then
		return
	end

	self.data:SetFakeGuildInfo(protocol)

    if self.view:IsOpen() then
  		self.view:Flush(TabIndex.guild_guildlist)
  	end
end

----------------------仙盟新功能start----------------------

-- 请求操作
function GuildWGCtrl:SendCSGuildOperateRequest(opera_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end

-- 仙盟签到
function GuildWGCtrl:OnSCGuildSignInfo(protocol)
	self.data:SetGuildSignInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_sign)
	end
end

-- 仙盟签到-个人
function GuildWGCtrl:OnSCGuildRoleSignInfo(protocol)
	self.data:SetGuildRoleSignInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_sign)
	end
end

-- 仙盟砍价
function GuildWGCtrl:OnSCGuildGiftBargainInfo(protocol)
	self.data:SetGuildGiftBargainInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_shop)
	end
end

-- 仙盟商店
function GuildWGCtrl:OnSCGuildRoleShopInfo(protocol)
	self.data:SetGuildRoleShopInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_shop)
	end
end

-- 仙盟商店 成员砍价礼包信息更新
function GuildWGCtrl:OnSCGuildMemberGiftStatusUpdate(protocol)
	self.data:UpdateGuildMemberGiftStatus(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_shop)
	end
end

-- 仙盟商店 成员砍价记录
function GuildWGCtrl:OnSCGuildMemberGiftBargainInfo(protocol)
	self.data:SetGuildMemberGiftBargainInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_shop)
	end
end

-- 仙盟商店 成员砍价记录-单条
function GuildWGCtrl:OnSCGuildMemberGiftBargainUpdate(protocol)
	self.data:SetGuildMemberGiftBargainUpdate(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_shop)
	end
end
----------------------仙盟新功能end------------------------