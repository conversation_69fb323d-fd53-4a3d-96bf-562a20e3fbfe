---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/10/10 11:44
---
local MaxShowRenderLen = 3
ManHuangFirstPassView = ManHuangFirstPassView or BaseClass(SafeBaseView)
function ManHuangFirstPassView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_manhuang_firstpass")
end

function ManHuangFirstPassView:ReleaseCallBack()
    if nil ~= self.first_pass_list then
        self.first_pass_list:DeleteMe()
        self.first_pass_list = nil
    end
end

function ManHuangFirstPassView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.FuBenPanel.ManHuangFirstPassViewName
	--self:SetSecondView(nil, self.node_list["size"])
    self:SetSecondView(Vector2(632,517))
    self.first_pass_list = AsyncListView.New(ManHuangFirstPassRender, self.node_list["first_pass_list"])

    XUI.AddClickEventListener(self.node_list.btn_auto_fetch, BindTool.Bind(self.OnClickAutoFetch))
end

function ManHuangFirstPassView:ShowIndexCallBack()
    --self:Flush()
    FuBenWGCtrl.Instance:SendManHuangGuDianReq(MANHUANGGUDIAN_REQ_TYPE.BASE_INFO)
end

function ManHuangFirstPassView:OnFlush()
    local data_list = ManHuangGuDianWGData.Instance:GetRewardDataList()
    self.first_pass_list:SetDataList(data_list, 0)

    if self.node_list["first_pass_list"] and self.node_list["first_pass_list"].gameObject and self.node_list["first_pass_list"].gameObject.activeInHierarchy then
        local jump_index = ManHuangGuDianWGData.Instance:GetJumpIndex()
        self.node_list["first_pass_list"].scroller:JumpToDataIndexForce(jump_index - 1)
    end

    local base_info = ManHuangGuDianWGData.Instance:GetManHuangBaseInfo()
    if not base_info then return end
    --我的波数
    self.node_list.my_wave.text.text = string.format(Language.FuBenPanel.ManHuangMaxWaveStr, base_info.manhuang_max_wave)
    local is_show_red = ManHuangGuDianWGData.Instance:GetIsCanFetch()
    XUI.SetGraphicGrey(self.node_list.btn_auto_fetch, not is_show_red)
end

function ManHuangFirstPassView:OnClickAutoFetch()
    FuBenWGCtrl.Instance:SendManHuangGuDianOperate(MANHUANGGUDIAN_OPERA_TYPE.AUTO_FETCH)
end

-------------------------------------------------------------------------------
ManHuangFirstPassRender = ManHuangFirstPassRender or BaseClass(BaseRender)
function ManHuangFirstPassRender:__init()
    XUI.AddClickEventListener(self.node_list.btn_fetch, BindTool.Bind(self.OnClickFetch, self))
    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    self.node_list.item_list.scroll_rect.enabled = false
end

function ManHuangFirstPassRender:__delete()
    if nil ~= self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function ManHuangFirstPassRender:OnFlush()
    local base_info = ManHuangGuDianWGData.Instance:GetManHuangBaseInfo()
    if not base_info then
        return
    end

    --左侧领取标记、按钮
    local flag_list = base_info.manhuang_first_reward_flag_list
    --print_error("标记列表 ，", flag_list)
    local flag = flag_list[self.data.wave]
    local pass_flag = base_info.manhuang_max_wave >= self.data.wave
    if pass_flag then
        if flag == MANHUANGGUDIAN_FLAG.NOT_FETCH then
            --未领取
            self.node_list.img_fetch:SetActive(false)
            self.node_list.btn_fetch:SetActive(true)
        else
            --已领取
            self.node_list.img_fetch:SetActive(true)
            self.node_list.btn_fetch:SetActive(false)
            self.node_list.img_fetch.image:LoadSprite(ResPath.GetF2CommonImages("biaoqian_1"))
            self.node_list.fetch_text.text.text = Language.Common.YiLingQu
        end
    else
        --未达成
        self.node_list.img_fetch:SetActive(true)
        self.node_list.btn_fetch:SetActive(false)
        self.node_list.img_fetch.image:LoadSprite(ResPath.GetF2CommonImages("biaoqian_2"))
        self.node_list.fetch_text.text.text = Language.Common.WEIDACHENG
    end

    --波数
    self.node_list.wave.text.text = string.format(Language.FuBenPanel.ManHuangWaveStr, self.data.wave)
    --物品奖励
    self.item_list:SetDataList(self.data.item_list, 0)
end

function ManHuangFirstPassRender:OnClickFetch()
    local base_info = ManHuangGuDianWGData.Instance:GetManHuangBaseInfo()
    if not base_info then return end
    local flag_list = base_info.manhuang_first_reward_flag_list
    local flag = flag_list[self.data.wave]
    if flag == MANHUANGGUDIAN_FLAG.FETCH then
        SysMsgWGCtrl.Instance.ErrorRemind(Language.FuBenPanel.ManHuangFetchMsg)
    end
    --print_error("请求领取奖励>>>>> ",self.data.wave)
    FuBenWGCtrl.Instance:SendManHuangGuDianOperate(MANHUANGGUDIAN_OPERA_TYPE.FETCH_REWARD, self.data.wave)
end
