﻿using Game;
using LuaInterface;
using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Rendering;
using UnityEngine.UI;

public class UI3DModel : MonoBehaviour, IOverrideOrder, IClippable
{
    private static SrpSortMaterial srpSortMaterial;
    private Vector3 virtualLightDir;

    private Transform rootCanvasTransform;
    private Canvas groupCanvas;
    private Dictionary<Renderer, int> renderOrignalOrderDic = new Dictionary<Renderer, int>();
    private Dictionary<GameObjectAttach, int> gameObjectAttaches = new Dictionary<GameObjectAttach, int>();
    private Dictionary<GameObject, AnimatorCullingMode> animatorCullModeDic = new Dictionary<GameObject, AnimatorCullingMode>();

    private float lastCheckChildTime = 0;
    private bool isGray = false;
    private bool isSupportClip;
    private Vector4 curClipRect = Vector4.zero;

    private Canvas clearZDepthCanvas;

    private RectMask2D lastMask = null;

    #region 安全工具方法

    /// <summary>
    /// 安全获取组件，避免空引用
    /// </summary>
    private static T SafeGetComponent<T>(GameObject go) where T : Component
    {
        if (go == null) return null;
        try
        {
            return go.GetComponent<T>();
        }
        catch (System.Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// 安全获取子组件，避免空引用
    /// </summary>
    private static T SafeGetComponentInChildren<T>(GameObject go, bool includeInactive = false) where T : Component
    {
        if (go == null) return null;
        try
        {
            return go.GetComponentInChildren<T>(includeInactive);
        }
        catch (System.Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// 安全获取父组件，避免空引用
    /// </summary>
    private T SafeGetComponentInParent<T>(bool includeInactive = false) where T : Component
    {
        if (this == null) return null;
        try
        {
            return GetComponentInParent<T>(includeInactive);
        }
        catch (System.Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// 验证GameObject有效性
    /// </summary>
    private static bool IsValidGameObject(GameObject go)
    {
        return go != null && go.transform != null;
    }

    /// <summary>
    /// 验证组件有效性
    /// </summary>
    private static bool IsValidComponent<T>(T component) where T : Component
    {
        return component != null && component.gameObject != null;
    }

    /// <summary>
    /// 安全获取组件在父级，避免空引用
    /// </summary>
    private static T SafeGetComponentInParent<T>(GameObject go) where T : Component
    {
        if (go == null) return null;
        try
        {
            return go.GetComponentInParent<T>();
        }
        catch (System.Exception)
        {
            return null;
        }
    }

    #endregion

    public GameObject GetTarget()
    {
        if (null == this) return null;

        return this.gameObject;
    }

    protected void Awake()
    {
        if (virtualLightDir == Vector3.zero)
        {
            GameObject light = GameObject.Find("GameRoot/UI3DModelLight");
            if (IsValidGameObject(light) && light.transform != null)
            {
                virtualLightDir = light.transform.forward;
            }
        }

        ResetRootCamera();
    }

    private void OnEnable()
    {
        if (!IsValidGameObject(gameObject))
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: gameObject is invalid in OnEnable");
#endif
            return;
        }

        if (srpSortMaterial == null)
            srpSortMaterial = new SrpSortMaterial();
        
        if (srpSortMaterial != null)
        {
            srpSortMaterial.RefreshRendersInGameObject(gameObject);
        }

        this.Reset();
        this.TryAddClip();

#if UNITY_EDITOR
        ActiveMonitor.OnEnableObj(this.gameObject, "UI3DModel");
#endif
    }

    private void OnDisable()
    {
        this.Clear();
        this.TryRemoveClip();

#if UNITY_EDITOR
        if (IsValidGameObject(this.gameObject))
        {
            ActiveMonitor.OnDisableObj(this.gameObject, "UI3DModel");
        }
#endif
    }

    private void Reset()
    {
        this.Clear();
        animatorCullModeDic.Clear();
        this.ResetRootCanvas();
        this.ResetAllRenders();
        this.ResetAllAttachObject();
        this.SetReOrderRender();
    }

    private void Clear()
    {
        this.TryRemoveInvalidRenders();
        this.TryRemoveInvalidGameObjectAttach();

        var attachIter = gameObjectAttaches.GetEnumerator();
        while(attachIter.MoveNext())
        {
            var attach = attachIter.Current.Key;
            if (IsValidComponent(attach))
            {
                QualityControlActive qualityAct = SafeGetComponentInChildren<QualityControlActive>(attach.gameObject);
                if (qualityAct != null) qualityAct.ResetOverrideLevel();
            }
        }
        gameObjectAttaches.Clear();

        var materialIter = renderOrignalOrderDic.GetEnumerator();
        while (materialIter.MoveNext())
        {
            var kv = materialIter.Current;
            Renderer renderer = kv.Key;
            if (IsValidComponent(renderer))
            {
                this.RevertLayer(renderer);
                if (MaterialMgr.Instance != null)
                {
                    MaterialMgr.Instance.ResumeMaterialsKeywordsAndRenderQueue(renderer);
                }
            }
        }
        renderOrignalOrderDic.Clear();

        if (OverrideOrderGroupMgr.Instance != null)
        {
            OverrideOrderGroupMgr.Instance.RemoveFromGroup(this.groupCanvas, this);
        }
        this.groupCanvas = null;
        this.rootCanvasTransform = null;
        lastCheckChildTime = 0;
        this.isGray = false;
        this.curClipRect = Vector4.zero;
    }

    protected void Update()
    {
        if (Time.time - lastCheckChildTime < 0.1)
        {
            return;
        }

        if (gameObjectAttaches.Count > 0)
        {
            CheckAttacheObject();
        }

        this.TryRemoveInvalidRenders();
        this.TryRemoveInvalidGameObjectAttach();
        this.TryUpdateApplyLight();
    }

    public void SetIsGrey(bool isGray)
    {
        if (this.isGray == isGray) return;

        this.isGray = isGray;
        var iter = renderOrignalOrderDic.GetEnumerator();
        while (iter.MoveNext())
        {
            if (IsValidComponent(iter.Current.Key))
            {
                this.RefreshCloneMaterialsInRender(iter.Current.Key);
            }
        }
    }

    public void SetIsSupportClip(bool isSupportClip)
    {
        if (this.isSupportClip == isSupportClip) return;

        this.isSupportClip = isSupportClip;
        this.TryAddClip();
    }

    public void OnAddGameobject(GameObject targetObj)
    {
        if (!IsValidGameObject(targetObj)) 
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: targetObj is invalid in OnAddGameobject");
#endif
            return;
        }

        this.RefreshRendersInGameObject(targetObj);
        this.RefreshAttachObjectInGameObject(targetObj);
        this.SetReOrderRender();

        if (!animatorCullModeDic.ContainsKey(targetObj))
        {
            Animator animator = SafeGetComponentInChildren<Animator>(targetObj, true);
            if (animator != null)
            {
                animatorCullModeDic.Add(targetObj, animator.cullingMode);
            }
        }
        this.SetAnimatorCullingMode(targetObj, AnimatorCullingMode.AlwaysAnimate);
    }

    public void OnRemoveGameObject(GameObject targetObj)
    {
        if (!IsValidGameObject(targetObj)) 
        {
#if UNITY_EDITOR
            Debug.LogWarning("UI3DModel: targetObj is invalid in OnRemoveGameObject");
#endif
            return;
        }

        this.OnGameObjectRemoved(targetObj);
    }

    private void OnTransformParentChanged()
    {
        this.ResetRootCanvas();
        this.SetReOrderRender();
        this.TryAddClip();
    }

    private void SetUI3DLayer(Renderer renderer)
    {
        if (!IsValidComponent(renderer)) return;
        
        if (renderer.gameObject.layer == GameLayers.UI3DTerrain)         // UI3D Terrrain是UI上接收3D的影子，不处理
        {
            return;
        }

        if (renderer is ParticleSystemRenderer || renderer is TrailRenderer)
        {
            renderer.gameObject.layer = GameLayers.UI3DEffect; // 区分的目的是UI3DEffect不接收投影
        }
        else
        {
            renderer.gameObject.layer = GameLayers.UI3D;
        }
    }

    private void RevertLayer(Renderer renderer)
    {
        if (!IsValidComponent(renderer)) return;
        
        if (renderer.gameObject.layer == GameLayers.UI3DTerrain)    // UI3D Terrrain是UI上接收3D的影子，不处理
        {
            return;
        }

        renderer.gameObject.layer = GameLayers.Default;
    }

    private void SetReOrderRender()
    {
        if (OverrideOrderGroupMgr.Instance != null)
        {
            OverrideOrderGroupMgr.Instance.SetGroupCanvasDirty(this.groupCanvas);
        }
    }

    public void SetOverrideOrder(int order, int orderInterval, int maxOrder, out int incOrder)
    {
        incOrder = 0;
        
        if (renderOrignalOrderDic.Count <= 0)
        {
            return;
        }
        
        var list = ListPool<RendererItem>.Get();
        if (list == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get list from pool");
#endif
            return;
        }

        var iter = renderOrignalOrderDic.GetEnumerator();
        while (iter.MoveNext())
        {
            if (IsValidComponent(iter.Current.Key))
            {
                list.Add(new RendererItem(iter.Current.Key, iter.Current.Value));
            }
        }

        if (list.Count <= 0)
        {
            ListPool<RendererItem>.Release(list);
            return;
        }

        list.Sort(CompareRenderer);

        int curOrder = list[0].order;
        int childMax = maxOrder - 1;
        order = Mathf.Min(childMax, order);
        for (int i = 0; i < list.Count; ++i)
        {
            var item = list[i];
            if (!IsValidComponent(item.renderer))
                continue;

            if (curOrder != item.order)
            {
                curOrder = item.order;
                ++order;
                order = Mathf.Min(childMax, order);
                item.renderer.sortingOrder = order;
                ++incOrder;
            }
            else
            {
                item.renderer.sortingOrder = order;
            }
        }

        ListPool<RendererItem>.Release(list);
        this.RefreshClearZDepthOrder();
    }

    public void ResetAllRenders()
    {
        renderOrignalOrderDic.Clear();
        
        if (IsValidGameObject(this.gameObject))
        {
            this.RefreshRendersInGameObject(this.gameObject);
        }
    }

    private void RefreshRendersInGameObject(GameObject gameObject)
    {
        if (!IsValidGameObject(gameObject)) 
        {
#if UNITY_EDITOR
            Debug.LogWarning("UI3DModel: gameObject is invalid in RefreshRendersInGameObject");
#endif
            return;
        }

#if UNITY_EDITOR
        if (gameObject.GetComponentInChildren<NirvanaRenderer>(true))
        {
            Debug.LogErrorFormat("请删除NirvanaRender，Ui3DModel不支持！否则将导致未知Bug");
            return;
        }
#endif

        var renderers = ListPool<Renderer>.Get();
        if (renderers == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get renderers list from pool");
#endif
            return;
        }
        
        try
        {
            gameObject.GetComponentsInChildren<Renderer>(true, renderers);
            for (int i = 0; i < renderers.Count; i++)
            {
                Renderer renderer = renderers[i];
                if (!IsValidComponent(renderer)) continue;
                
                this.SetUI3DLayer(renderer);
                if (!renderOrignalOrderDic.ContainsKey(renderer))
                {
                    renderOrignalOrderDic.Add(renderer, renderer.sortingOrder);
                }

                renderer.lightProbeUsage = LightProbeUsage.Off;
                this.RefreshCloneMaterialsInRender(renderer);
            }
        }
        finally
        {
            ListPool<Renderer>.Release(renderers);
        }
    }

    private void RefreshCloneMaterialsInRender(Renderer renderer)
    {
        if (!IsValidComponent(renderer)) 
        {
#if UNITY_EDITOR
            Debug.LogWarning("UI3DModel: renderer is invalid in RefreshCloneMaterialsInRender");
#endif
            return;
        }

        if (MaterialMgr.Instance == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: MaterialMgr.Instance is null");
#endif
            return;
        }

        Material[] newMaterials = MaterialMgr.Instance.GetClonedMaterials(renderer);
        if (newMaterials == null)
        {
#if UNITY_EDITOR
            Debug.LogWarning($"UI3DModel: Failed to get cloned materials for renderer {renderer.name}");
#endif
            return;
        }
        
        for (int i = 0; i < newMaterials.Length; i++)
        {
            Material newMaterial = newMaterials[i];
            RefreshCloneMaterial(newMaterial);
        }
    }

    private void RefreshCloneMaterial(Material material)
    {
        if (material == null)
        {
            return;
        }

        //美术大佬的要求，UI界面上的模型材质球不修改VirtualLightDir
        //if (material.IsKeywordEnabled("ENABLE_VIRTUAL_LIGHT_DIR"))
        //{
        //    material.SetVector("_VirtualLightDir", virtualLightDir);
        //}

        if (isSupportClip)
        {
            if (curClipRect != Vector4.zero)
            {
                material.EnableKeyword("ENABLE_CLIP_RECT");
                material.SetVector("_ClipRect", curClipRect);
            }
            else
            {
                material.DisableKeyword("ENABLE_CLIP_RECT");
            }
        }

        if (isGray)
        {
            material.EnableKeyword("ENABLE_POST_EFFECT");
            material.SetFloat("_PostEffectType", 3);
        }
        else
        {
            material.DisableKeyword("ENABLE_POST_EFFECT");
        }
    }

    private void ResetAllAttachObject()
    {
        gameObjectAttaches.Clear();
        
        if (IsValidGameObject(this.gameObject))
        {
            RefreshAttachObjectInGameObject(this.gameObject);
        }
    }

    private void RefreshAttachObjectInGameObject(GameObject gameObject)
    {
        if (!IsValidGameObject(gameObject)) 
        {
#if UNITY_EDITOR
            Debug.LogWarning("UI3DModel: gameObject is invalid in RefreshAttachObjectInGameObject");
#endif
            return;
        }

        var attaches = ListPool<GameObjectAttach>.Get();
        if (attaches == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get attaches list from pool");
#endif
            return;
        }
        
        try
        {
            gameObject.GetComponentsInChildren<GameObjectAttach>(true, attaches);
            for (int i = 0; i < attaches.Count; i++)
            {
                var attach = attaches[i];
                if (IsValidComponent(attach) && !gameObjectAttaches.ContainsKey(attach))
                {
                    gameObjectAttaches.Add(attach, attach.transform.childCount);
                }
            }
        }
        finally
        {
            ListPool<GameObjectAttach>.Release(attaches);
        }
    }

    private void SetAnimatorCullingMode(GameObject gameObject, AnimatorCullingMode cullingMode)
    {
        if (!IsValidGameObject(gameObject)) return;

        Animator animator = SafeGetComponentInChildren<Animator>(gameObject, true);
        if (animator != null)
        {
            animator.cullingMode = cullingMode;
        }
    }

    private void ResetRootCanvas()
    {
        if (OverrideOrderGroupMgr.Instance != null)
        {
            OverrideOrderGroupMgr.Instance.RemoveFromGroup(this.groupCanvas, this);
        }
        this.groupCanvas = null;
        this.rootCanvasTransform = null;

        var canvasScalers = ListPool<CanvasScaler>.Get();
        if (canvasScalers == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get canvasScalers list from pool");
#endif
            return;
        }
        
        try
        {
            this.GetComponentsInParent(true, canvasScalers);
            if (canvasScalers.Count > 0)
            {
                var canvasScaler = canvasScalers[0];
                if (IsValidComponent(canvasScaler) && OverrideOrderGroupMgr.Instance != null)
                {
                    this.groupCanvas = OverrideOrderGroupMgr.Instance.AddToGroup(this);
                    this.rootCanvasTransform = canvasScaler.transform;
                }
            }
        }
        finally
        {
            ListPool<CanvasScaler>.Release(canvasScalers);
        }
    }

    private void RefreshClearZDepthOrder()
    {
        if (groupCanvas == null || groupCanvas.transform == null)
        {
            return;
        }

        Transform clearZDepth = groupCanvas.transform.Find("UIClearZDepth");
        if (clearZDepth != null && clearZDepth.gameObject != null)
        {
            clearZDepth.gameObject.SetActive(true);
            clearZDepthCanvas = SafeGetComponent<Canvas>(clearZDepth.gameObject);
            if (clearZDepthCanvas != null && OverrideOrderGroupMgr.Instance != null)
            {
                clearZDepthCanvas.overrideSorting = true;
                clearZDepthCanvas.sortingOrder = groupCanvas.sortingOrder + OverrideOrderGroupMgr.Instance.GroupCanvasOrderInterval - 1;
            }
        }
    }

    // 检测GameObjectAttach加载的特效是否完成
    private void CheckAttacheObject()
    {
        lastCheckChildTime = Time.time;
        List<GameObjectAttach> completeAttachList = null;
        
        if (gameObjectAttaches.Count == 0)
        {
            return;
        }
        
        var iter = gameObjectAttaches.GetEnumerator();
        while(iter.MoveNext())
        {
            var attach = iter.Current.Key;
            if (IsValidComponent(attach) && attach.transform.childCount > iter.Current.Value)
            {
                if (completeAttachList == null) completeAttachList = ListPool<GameObjectAttach>.Get();
                if (completeAttachList != null) completeAttachList.Add(attach);
            }
        }

        if (completeAttachList != null)
        {
            try
            {
                var delIter = completeAttachList.GetEnumerator();
                while(delIter.MoveNext())
                {
                    var delAttach = delIter.Current;
                    gameObjectAttaches.Remove(delAttach);

                    if (IsValidComponent(delAttach))
                    {
                        QualityControlActive qualityAct = SafeGetComponentInChildren<QualityControlActive>(delAttach.gameObject);
                        if (qualityAct != null) qualityAct.SetOverrideLevel(0);

                        this.RefreshRendersInGameObject(delAttach.gameObject);
                    }
                }
            }
            finally
            {
                ListPool<GameObjectAttach>.Release(completeAttachList);
            }

            this.SetReOrderRender();
        }
    }

    // 在逻辑层会因为各种原因直接移除了Ui3DModel下的子节点Render，导致维护的Render失效
    // 此处应移除无效的Render，避免不必要Bug
    private void TryRemoveInvalidRenders()
    {
        if (renderOrignalOrderDic.Count == 0)
        {
            return;
        }
        
        List<Renderer> delList = null;
        List<UI3DModel> ui3dModels = ListPool<UI3DModel>.Get();
        if (ui3dModels == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get ui3dModels list from pool");
#endif
            return;
        }
        
        try
        {
            var iter = renderOrignalOrderDic.GetEnumerator();

            while(iter.MoveNext())
            {
                Renderer renderer = iter.Current.Key;
                if (!IsValidComponent(renderer))
                {
                    if (delList == null) delList = ListPool<Renderer>.Get();
                    if (delList != null) delList.Add(renderer);
                    continue;
                }

                ui3dModels.Clear();
                renderer.GetComponentsInParent<UI3DModel>(true, ui3dModels); 
                if (ui3dModels.Count <= 0)    // 如果已经不是挂在UI3DModel，则恢复层
                {
                    if (!SafeGetComponentInParent<UI3DShadow>(renderer.gameObject))
                    {
                        this.RevertLayer(renderer);
                        if (MaterialMgr.Instance != null)
                        {
                            MaterialMgr.Instance.ResumeMaterialsKeywordsAndRenderQueue(renderer);
                        }
                    }
                }
                else if(ui3dModels[0] != this) // 如果所在的Ui3dModle已经不是该Ui3dModle则从该Ui3dModle中移除
                {
                    if (delList == null) delList = ListPool<Renderer>.Get();
                    if (delList != null) delList.Add(renderer);
                }
            }
        }
        finally
        {
            ListPool<UI3DModel>.Release(ui3dModels);
        }
        
        if (delList != null)
        {
            try
            {
                for (int i = 0; i < delList.Count; i++)
                {
                    var renderer = delList[i];
                    renderOrignalOrderDic.Remove(renderer);
                }
            }
            finally
            {
                ListPool<Renderer>.Release(delList);
            }
        }
    }

    // 在逻辑层会因为各种原因直接移除了Ui3DModel下的子节点Render，导致维护的Attach失效
    // 此处应移除无效的Attach，避免不必要Bug
    private void TryRemoveInvalidGameObjectAttach()
    {
        if (gameObjectAttaches.Count == 0)
        {
            return;
        }
        
        List<GameObjectAttach> list = null;
        var iter = gameObjectAttaches.GetEnumerator();
        while(iter.MoveNext())
        {
            var attach = iter.Current.Key;
            if (!IsValidComponent(attach) || SafeGetComponentInParent<UI3DModel>(attach.gameObject) != this)
            {
                if (list == null)
                {
                    list = ListPool<GameObjectAttach>.Get();
                }

                if (list != null) list.Add(attach);
            }
        }

        if (list != null)
        {
            try
            {
                for (int i = 0; i < list.Count; i++)
                {
                    gameObjectAttaches.Remove(list[i]);
                }
            }
            finally
            {
                ListPool<GameObjectAttach>.Release(list);
            }
        }
    }

    private void OnGameObjectRemoved(GameObject gameObject)
    {
        if (!IsValidGameObject(gameObject))
        {
#if UNITY_EDITOR
            Debug.LogWarning("UI3DModel: gameObject is invalid in OnGameObjectRemoved");
#endif
            return;
        }
        
        // 移除Renderer
        var renderers = ListPool<Renderer>.Get();
        if (renderers == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get renderers list from pool");
#endif
            return;
        }
        
        try
        {
            gameObject.GetComponentsInChildren<Renderer>(true, renderers);
            for (int i = 0; i < renderers.Count; i++)
            {
                Renderer renderer = renderers[i];
                if (IsValidComponent(renderer) && renderOrignalOrderDic.ContainsKey(renderer))
                {
                    renderOrignalOrderDic.Remove(renderer);
                    this.RevertLayer(renderer);
                    if (MaterialMgr.Instance != null)
                    {
                        MaterialMgr.Instance.ResumeMaterialsKeywordsAndRenderQueue(renderer);
                    }
                }
            }
        }
        finally
        {
            ListPool<Renderer>.Release(renderers);
        }

        // 移除GameObjectAttach
        var attaches = ListPool<GameObjectAttach>.Get();
        if (attaches == null)
        {
#if UNITY_EDITOR
            Debug.LogError("UI3DModel: Failed to get attaches list from pool");
#endif
            return;
        }
        
        try
        {
            gameObject.GetComponentsInChildren<GameObjectAttach>(true, attaches);
            for (int i = 0; i < attaches.Count; i++)
            {
                GameObjectAttach attach = attaches[i];
                gameObjectAttaches.Remove(attach);
            }
        }
        finally
        {
            ListPool<GameObjectAttach>.Release(attaches);
        }

        // 恢复裁剪模式
        AnimatorCullingMode mode = AnimatorCullingMode.CullUpdateTransforms;
        if (animatorCullModeDic.TryGetValue(gameObject, out mode))
        {
            animatorCullModeDic.Remove(gameObject);
        }
        
        this.SetAnimatorCullingMode(gameObject, mode);
    }

    private void TryUpdateApplyLight()
    {
        /*
#if UNITY_EDITOR
        if (MaterialMgr.Instance.IsArtEdit())
        {
            var newDir = GameObject.Find("GameRoot/UI3DModelLight").transform.forward;
            if (newDir.x != virtualLightDir.x || newDir.y != virtualLightDir.y || newDir.z != virtualLightDir.z)
            {
                virtualLightDir = newDir;
                foreach (var kv in renderOrignalOrderDic)
                {
                    RefreshCloneMaterialsInRender(kv.Key);
                }
            }
        }
#endif
        */
    }

    private void TryAddClip()
    {
        if (!isSupportClip) return;

        RectMask2D mask2d = SafeGetComponentInParent<RectMask2D>(false);
        if (lastMask != mask2d)
        {
            if (IsValidComponent(lastMask))
            {
                lastMask.RemoveClippable(this);
                lastMask = null;
            }

            if (IsValidComponent(mask2d))
            {
                mask2d.AddClippable(this);
                lastMask = mask2d;
            }
        }
    }

    private void TryRemoveClip()
    {
        if (isSupportClip && IsValidComponent(lastMask))
        {
            lastMask.RemoveClippable(this);
            lastMask = null;
        }
        this.curClipRect = Vector4.zero;
    }


    [NoToLua]
    public void RecalculateClipping()
    {
        // donothing
    }

    private RectTransform m_RectTransform;
    [NoToLua]
    public RectTransform rectTransform
    {
        get 
        { 
            if (m_RectTransform == null && this != null)
            {
                m_RectTransform = SafeGetComponent<RectTransform>(this.gameObject);
            }
            return m_RectTransform;
        }
    }

    [NoToLua]
    public void Cull(Rect clipRect, bool validRect)
    {
        // donothing
    }

    private Camera rootCamera;
    private void ResetRootCamera()
    {
        this.rootCamera = null;

        if (this == null) return;

        List<Canvas> canvas = new List<Canvas>();
        this.GetComponentsInParent(true, canvas);

        if (canvas != null && canvas.Count > 0)
        {
            Canvas canva = canvas[0];
            if (IsValidComponent(canva))
            {
                rootCamera = canva.worldCamera;
            }
        }
    }

    [NoToLua]
    public void SetClipRect(Rect clipRect, bool validRect)
    {
        if (this.rootCanvasTransform == null || !this.isSupportClip || rootCamera == null)
        {
            return;
        }

        Vector4 newClipRect = Vector4.zero;
        if (validRect)
        {
            try
            {
                Vector3 minWorldPos = this.rootCanvasTransform.TransformPoint(new Vector2(clipRect.x, clipRect.y));
                Vector3 maxWorldPos = this.rootCanvasTransform.TransformPoint(new Vector2(clipRect.x + clipRect.width, clipRect.y + clipRect.height));

                Vector3 minScreenPos = rootCamera.WorldToScreenPoint(minWorldPos);
                Vector3 maxScreenPos = rootCamera.WorldToScreenPoint(maxWorldPos);

                newClipRect = new Vector4(minScreenPos.x, minScreenPos.y, maxScreenPos.x, maxScreenPos.y);
            }
            catch (System.Exception ex)
            {
#if UNITY_EDITOR
                Debug.LogError($"UI3DModel: Error in SetClipRect coordinate transformation: {ex.Message}");
#endif
                return;
            }
        }

        if (newClipRect != this.curClipRect)
        {
            this.curClipRect = newClipRect;
            var iter = renderOrignalOrderDic.GetEnumerator();
            while (iter.MoveNext())
            {
                if (IsValidComponent(iter.Current.Key))
                {
                    this.RefreshCloneMaterialsInRender(iter.Current.Key);
                }
            }
        }
    }

    private struct RendererItem
    {
        public Renderer renderer;
        public int order;
        public RendererItem(Renderer renderer, int order)
        {
            this.renderer = renderer;
            // ParticleSystem强制显示在最上层
            if (IsValidComponent(renderer) && SafeGetComponent<ParticleSystem>(renderer.gameObject) != null)
            {
                order = order + 60000;
            }
            this.order = order;
        }
    }

    private int CompareRenderer(RendererItem x, RendererItem y)
    {
        if (x.order > y.order)
            return 1;
        else if (x.order == y.order)
            return 0;
        else
            return -1;

    }

    public void SetClipSoftness(Vector2 clipSoftness)
    {
        // throw new System.NotImplementedException();
    }
}