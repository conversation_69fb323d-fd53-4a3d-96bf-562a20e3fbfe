RoleBagView = RoleBagView or BaseClass(SafeBaseView)

function RoleBagView:__init()
	self.close_mode = CloseMode.CloseVisible
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	local bundle_name = "uis/view/rolebag_ui_prefab"
	local view_bundle = "uis/view/rolebag_ui/equip_target_prefab"
	local long_zhu_view_bundle = "uis/view/long_zhu_ui_prefab"
	self:AddViewResource({TabIndex.rolebag_storge, TabIndex.rolebag_Integration, TabIndex.rolebag_longzhu}, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource({ TabIndex.rolebag_bag_all, TabIndex.rolebag_stuff }, bundle_name, "layout_left")
	self:AddViewResource(TabIndex.rolebag_bag_all, bundle_name, "layout_bag")
	self:AddViewResource(TabIndex.rolebag_stuff, bundle_name, "layout_stuff_bag")
	self:AddViewResource(TabIndex.rolebag_storge, bundle_name, "layout_storge")
	-- self:AddViewResource(TabIndex.rolebag_Target, view_bundle, "layout_equiptarget")
	self:AddViewResource(TabIndex.rolebag_Integration, view_bundle, "layout_equipment_integration")
	self:AddViewResource(TabIndex.rolebag_longzhu, long_zhu_view_bundle, "layout_longzhu_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

	local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BAG}
	self:SetTabShowUIScene(TabIndex.rolebag_bag_all, data)
	self:SetTabShowUIScene(TabIndex.rolebag_stuff, data)

	self.remind_tab = {
		{ RemindName.BagBag,  RemindName.BagSSS, RemindName.Equipment_NewYinJi, RemindName.SuperDragonSeal, RemindName.DragonKingToken , RemindName.EquipBodyTuPo},
		{ RemindName.BagStuff },
		nil,
		{ RemindName.EquipmentIntegration },
		{ RemindName.LongZhu },
		-- { RemindName.EquipTarget },
	}
end

function RoleBagView:ReleaseCallBack()
	if FunctionGuide.Instance and self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Bag, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	if ItemWGData.Instance and self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	if ItemWGData.Instance and self.itemlist_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemlist_data_event)
		self.itemlist_data_event = nil
	end
	
	-- if FunctionGuide.Instance then
	-- 	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.Bag, self.get_guide_ui_event)
	-- end
	
	-- if ItemWGData.Instance ~= nil then
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemlist_data_event)
	-- 	self.item_data_event = nil
	-- end

	self:DeleteBagView()
	self:DeleteStorgeView()
	self:DeleteStuffBagView()

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.role_info_widget then
		self.role_info_widget:DeleteMe()
		self.role_info_widget = nil
	end

	self.bag_show_tab = nil

	-- self:ETReleaseCallBack()
	self:EIReleaseCallBack()
	self:LongzhuReleaseCallBack()

	if self.remind_callback then
		RemindManager.Instance:UnBind(self.remind_callback)
		self.remind_callback = nil
	end

	self.bg_bundle = nil
	self.bg_asset = nil
end

function RoleBagView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Bag.TabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Bag, self.tabbar)
	end

	if not self.get_guide_ui_event then
		self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
		FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.Bag, self.get_guide_ui_event)
	end

	if not self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end

	if self.itemlist_data_event then
		self.itemlist_data_event = BindTool.Bind1(self.ItemDataListChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.itemlist_data_event, true)
	end

	self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.Bag]

	if not self.remind_callback then
		self.remind_callback = BindTool.Bind(self.OnRemindChange, self)

		local remind_list = {
			RemindName.Equipment_NewYinJi,
			RemindName.DragonKingToken,
			RemindName.SuperDragonSeal,
		}

		for k, v in pairs(remind_list) do
			RemindManager.Instance:Bind(self.remind_callback, v)
		end
	end
end

function RoleBagView:OnRemindChange(remind_name, num)
	if remind_name == RemindName.Equipment_NewYinJi then
		if nil ~= self.node_list.btn_equipment_mark_remind then
			self.node_list.btn_equipment_mark_remind:SetActive(num > 0)
		end
	elseif remind_name == RemindName.DragonKingToken then
		if nil ~= self.node_list.btn_dragon_king_token_remind then
			self.node_list.btn_dragon_king_token_remind:SetActive(num > 0)
		end
	elseif remind_name == RemindName.SuperDragonSeal then
		if nil ~= self.node_list.btn_super_dragon_seal_remind then
			self.node_list.btn_super_dragon_seal_remind:SetActive(num > 0)
		end
	end
end

function RoleBagView:LoadIndexCallBack(index, loaded_times)
	if index == TabIndex.rolebag_bag_all then
		RoleBagWGCtrl.Instance:OnReqEquipMeltInfo() --请求熔炼信息
		self:InitBagView(index)
	elseif index == TabIndex.rolebag_storge then
		self:InitStorgeView()
	elseif index == TabIndex.rolebag_stuff then
		self:InitStuffBagView(index)
	-- elseif index == TabIndex.rolebag_Target then
	-- 	self:InitETView()
	elseif index == TabIndex.rolebag_Integration then
		self:InitEIView()
	elseif index == TabIndex.rolebag_longzhu then
		self:InitLongZhuView()
	end

	self:ChangeTongYongBgIndex(index)
end

function RoleBagView:OpenCallBack()
	BagWGCtrl.Instance.is_show = false
	self:FlushRoleInfoWidget()

	if self.role_info_widget then
		self.role_info_widget:ParentViewOpenCallBack()
	end

	--NewYinJiJiChengWGData.Instance:CheckOldYinJiAttr()
end

function RoleBagView:ShowIndexCallBack(index)
	self.bag_show_tab = index

	if index == TabIndex.rolebag_bag_all then
		if self.node_list.ph_bag_grid and self.node_list.ph_bag_grid.scroll_rect then
			self.node_list.ph_bag_grid.scroll_rect.verticalNormalizedPosition = 1
		end
		self:FlushModel()
		ItemWGData.Instance:GetStuffStorgeItemData()
	elseif index == TabIndex.rolebag_storge then
		if self.node_list["BageListView1"].scroll_rect then
			self.node_list["BageListView1"].scroll_rect.verticalNormalizedPosition = 1
		end
		if self.node_list["BageListView2"].scroll_rect then
			self.node_list["BageListView2"].scroll_rect.verticalNormalizedPosition = 1
		end
	-- elseif index == TabIndex.rolebag_bag_all then
	-- 	self:SelectBagCallback(index)
	elseif index == TabIndex.rolebag_stuff then
		if self.node_list.list_view and self.node_list.list_view.scroll_rect then
			self.node_list.list_view.scroll_rect.verticalNormalizedPosition = 1
		end
		self:FlushModel()
		ItemWGData.Instance:GetStuffStorgeItemData()
		self:SelectStuffBagCallback(index)
	elseif index == TabIndex.rolebag_longzhu then
		self:ShowCallBack()
	end

	--因为背包界面是不做卸载的所以moneybar打开的时候 就不会刷新了  所以每次打开的时候需要手动调用一次刷新
	if self.money_bar then
		self.money_bar:Flush()
	end

	self:ChangeTongYongBgIndex(index)
	self:Flush(index)
end

function RoleBagView:ChangeTongYongBgIndex(index)
	local bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.NOT_HAVE_ROLEMODEL)

	if index == TabIndex.rolebag_bag_all then
		bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.HAVE_ROLEMODEL)
	elseif index == TabIndex.rolebag_storge then
		bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.HAVE_ROLEMODEL)
	elseif index == TabIndex.rolebag_stuff then
		bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.HAVE_ROLEMODEL)
	elseif index == TabIndex.rolebag_longzhu then
		bundle, asset = ResPath.GetRawImagesJPG("a3_mgxj_bg1")
	end

	if self.node_list["RawImage_tongyong"] and self.bg_bundle ~= bundle and self.bg_asset ~= asset then
		self.bg_bundle = bundle
		self.bg_asset = asset
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(self.bg_bundle, self.bg_asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

-- function RoleBagView:CloseCallBack()
-- 	BagWGCtrl.Instance.is_show = true
-- end

local temp_list = {
	[TabIndex.rolebag_bag_all] = true,
	[TabIndex.rolebag_storge] = true,
	[TabIndex.rolebag_stuff] = true
}
function RoleBagView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	if temp_list[self.show_index] then
		local param_t = {
			[index] = { item_id = item_id, index = index, reason = reason, put_reason = put_reason, old_num = old_num, new_num =
			new_num } }
		self:Flush(self.show_index, "itemdata_change", param_t)
	end

	if RoleBagWGCtrl.Instance.auto_sell_view:IsOpen() then
		RoleBagWGCtrl.Instance.auto_sell_view:Flush()
	end
end

function RoleBagView:ItemDataListChangeCallback()
	if temp_list[self.show_index] then
		self:Flush(self.show_index, "itemdata_list_change")
	end
end

function RoleBagView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.rolebag_bag_all then
				self:FlushBagView(v)
			elseif index == TabIndex.rolebag_storge then
				self:FlushStorgeView()
				self:FlushRoleInfoWidget()
			elseif index == TabIndex.rolebag_stuff then
				self:FlushStuffBagView()
			-- elseif index == TabIndex.rolebag_Target then
			-- 	self:JumpFlushETView(v.to_ui_param)
			elseif index == TabIndex.rolebag_Integration then
				self.ei_flush_wait_flag = true
				self:EISelectToggle(v.force_big_type, v.force_suit_type, v.force_slot_index)
				self:FlushEICapability()
			elseif index == TabIndex.rolebag_longzhu then
				self:FlushLongZhuItemData()
				self:SetLongZhuTogSelect()
				self:FlushLongZhuSkillInfo()
			end
		elseif "itemdata_list_change" == k then
			if index == TabIndex.rolebag_bag_all then
				self:OnBagItemDataChange()
			elseif index == TabIndex.rolebag_storge then
				self:OnStorgeItemDataChange()
			elseif index == TabIndex.rolebag_stuff then
				self:OnStuffBagItemDataChange()
			end
		elseif "itemdata_change" == k then
			if index == TabIndex.rolebag_bag_all then
				self:OnBagItemDataChange(v)
			elseif index == TabIndex.rolebag_storge then
				self:OnStorgeItemDataChange()
			elseif index == TabIndex.rolebag_stuff then
				self:OnStuffBagItemDataChange()
			end
		elseif "itemcd_change" == k then
			for k1, v1 in pairs(v) do
				if index == (TabIndex.rolebag_bag_all) then
					self:OnItemCDChange(v1.item_id, v1.index, v1.cd_end_time, v1.client_colddown)
				end
			end
		elseif "guard_change" == k then
			self:FlushRoleInfoWidget()
		elseif "flush_rolebag_model" == k then
			if self.show_index == TabIndex.rolebag_bag_all or self.show_index == TabIndex.rolebag_stuff then
				self:FlushModel()
			end
		elseif "flush_model" == k then
			if self.show_index == TabIndex.rolebag_bag_all or self.show_index == TabIndex.rolebag_stuff then
				self:FlushModelAction()
			end
		elseif "select_long_zhu" == k then
			self:SetLongZhuTogSelect(v.item_id)
		elseif "long_zhu_info" == k then
			self:FlushLongZhuItemData()
			self:SetLongZhuTogSelect()
			self:FlushLongZhuSkillInfo()
		elseif "longxi" == k then
			self:FlushLongXiBtnState()
		elseif k == "protocol_change" then
			self.ei_flush_wait_flag = true
			self:FlushEIToggleAllData()
			self:EISelectToggle(nil, nil, nil, true)
			self:FlushEICapability()
		elseif k == "item_change" then
			self:FlushEIViewAllNoCap()
		elseif k == "lv_change" then
			self:FlushEIToggleAllData()
			self:FlushEICapability()
		end
	end
end

function RoleBagView:CreateRoleInfoWidget()
	self.role_info_widget = RoleInfoView.New(self.node_list)
end

function RoleBagView:FlushRoleInfoWidget()
	if self.role_info_widget ~= nil then
		self.role_info_widget:SetRoleData(RoleWGData.Instance.role_vo)
	end
end

function RoleBagView:TryOpenCell(cell)
end

--获得开启格子需要的提示
function RoleBagView:GetOpenBagCellTip(to_index, grid_name)

end

function RoleBagView:GetCellOpenNeedCount(index)
	if self.open_cell_cost == nil then --开格子配置直接写在这
		self.open_cell_cost = {
			{ min_extend_index = 0,  need_item_count = 1 },
			{ min_extend_index = 35, need_item_count = 1 },
			{ min_extend_index = 45, need_item_count = 2 },
			{ min_extend_index = 50, need_item_count = 3 },
			{ min_extend_index = 55, need_item_count = 4 },
			{ min_extend_index = 60, need_item_count = 6 },
			{ min_extend_index = 65, need_item_count = 8 },
			{ min_extend_index = 70, need_item_count = 10 },
			{ min_extend_index = 75, need_item_count = 15 },
		}
	end

	local len = #self.open_cell_cost
	for i = len, 1, -1 do
		if index >= self.open_cell_cost[i].min_extend_index then
			return self.open_cell_cost[i].need_item_count
		end
	end
	return 0
end

function RoleBagView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return NextGuideStepFlag
		end
	elseif ui_name == GuideUIName.BagOneKeySell then
		if self.node_list.btn_auto_sell then
			return self.node_list.btn_auto_sell, BindTool.Bind1(self.OpenAutoSell, self)
		end
	elseif ui_name == GuideUIName.BagAutoMelting then
		return self.node_list.btn_auto_melting, BindTool.Bind1(self.OnClickAutoMelting, self)
	elseif ui_name == GuideUIName.BagGoldExchange then
		return self.node_list["btn_bag_yinpiao"], BindTool.Bind(self.OnClickExchangeYinPiao, self)
	elseif ui_name == GuideUIName.CloseBtn then
		if self.node_list.btn_close_window then
			return self.node_list.btn_close_window, BindTool.Bind1(self.Close, self)
		end
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end
