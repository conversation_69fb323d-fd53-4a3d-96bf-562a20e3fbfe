ArtifactAffectionChangeView = ArtifactAffectionChangeView or BaseClass(SafeBaseView)

function ArtifactAffectionChangeView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)
    self.view_layer = UiLayer.PopTop
    
    self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_affection_change")
end

function ArtifactAffectionChangeView:ReleaseCallBack()
    self:CleanDelayTimer()
end

function ArtifactAffectionChangeView:SetDataAndOpen(show_data)
	self.show_data = show_data
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function ArtifactAffectionChangeView:OnFlush()
	if not self.show_data then
		return
	end
	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.show_data.seq)
	if not artifact_cfg then
		return
	end
	
	self.node_list["show_root"]:SetActive(false)
	self.node_list["txt_show_desc"].text.text = string.format(Language.Artifact.AffectionChange, artifact_cfg.name, self.show_data.value)
	self.node_list["show_root"]:SetActive(true)

	self:CleanDelayTimer()
	self.hide_root_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 1.5)
end

function ArtifactAffectionChangeView:CleanDelayTimer()
	if self.hide_root_timer then
		GlobalTimerQuest:CancelQuest(self.hide_root_timer)
		self.hide_root_timer = nil
	end
end



