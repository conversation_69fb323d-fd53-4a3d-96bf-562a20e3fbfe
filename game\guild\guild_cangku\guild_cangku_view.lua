-- 仙盟仓库
function GuildView:InitGuildckView()
	GuildWGCtrl.Instance:SendSorgeReqInfo()
	self.ck_pinzhi_data_list = Language.Guild.GuildPinZhiName
	
	self.ck_pinzhi_jie = 0
	self.ck_pinzhi_name = Language.Guild.GuildPinZhiName[1][1]

	self.ck_pinjie_data_list = Language.Guild.GuildPinJieName
	self.ck_pinjie_color = 0
	self.ck_pinjie_name = Language.Guild.GuildPinJieName[1][1]

	self.ck_star_data_list = Language.Guild.GuildStarName
	self.ck_equip_star = 0
	self.ck_equip_star_name = Language.Guild.GuildStarName[1][1]


	self.is_admin = true
	GuildCangKuWGData.Instance:SetIsAdmin(self.is_admin)
	self.cell_count = 0
	-----------------------
	--仓库
	------------------------
	self.node_list.text_ck_destroy.text.text = Language.Guild.DonateEquip
--------------------------------------------------------------------
	-- 仓库网格
	self.storge_grid = ShenShouGrid.New()
	local cell_count = GuildCangKuWGData.Instance:GetOpengridCount()
	cell_count = cell_count ~= 0 and cell_count or 200
	cell_count = cell_count + 1    --加多一个常驻的物品兑换
	local bundle = "uis/view/guild_ui_prefab"
	local asset = "ck_bag_cell"
	self.storge_grid:CreateCells({col = 9, cell_count = cell_count, list_view = self.node_list["ph_storge_grid"],
		assetBundle = bundle, assetName = asset, itemRender = CKBagCell})
	self.storge_grid:SetSelectCallBack(BindTool.Bind(self.CkBagSelectCallBack,self))
	self.storge_grid:SetStartZeroIndex(false)
	self.storge_grid:SetIsMultiSelect(false)
	GuildCangKuWGData.Instance:SetIsMultiSelect(false)

	self.cangku_list_view = AsyncListView.New(CKListItemRender, self.node_list["ph_cangku_list"])

	-- 品质
	self.pinzhi_list_view = AsyncListView.New(CKPinZhiListRender, self.node_list["ph_pinzhi_list"])
	self.pinzhi_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectPinZhiItemCallBack, self))
	self.pinzhi_list_view:SetDataList(self.ck_pinzhi_data_list,0)
	self.node_list.layout_pinzhi_list:SetActive(false)
	self.node_list.layout_pinjie_list:SetActive(false)
	self.node_list.layout_star_list:SetActive(false)
	self.node_list.jiantou_pinzhi_down:SetActive(not self.node_list.layout_pinzhi_list:GetActive())
	self.node_list.jiantou_pinzhi_up:SetActive(self.node_list.layout_pinzhi_list:GetActive())
	self.node_list.jiantou_pinjie_down:SetActive(not self.node_list.layout_pinjie_list:GetActive())
	self.node_list.jiantou_pinjie_up:SetActive(self.node_list.layout_pinjie_list:GetActive())
	self.node_list.jiantou_star_down:SetActive(not self.node_list.layout_star_list:GetActive())
	self.node_list.jiantou_star_up:SetActive(self.node_list.layout_star_list:GetActive())

	-- 品阶
	self.pinjie_list_view = AsyncListView.New(CKPinJieListRender, self.node_list["ph_pinjie_list"])
	self.pinjie_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectPinJieItemCallBack, self))
	self.pinjie_list_view:SetDataList(self.ck_pinjie_data_list,0)

	-- 星级
	self.star_list_view = AsyncListView.New(CKStarListRender, self.node_list["ph_star_list"])
	self.star_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectStarItemCallBack, self))
	self.star_list_view:SetDataList(self.ck_star_data_list,0)

	--self.node_list.btn_GuildCK.button:AddClickListener(BindTool.Bind(self.ClickbtnGuildCK, self))
	self.node_list.btn_batch_gongxian.button:AddClickListener(BindTool.Bind(self.OnClickBatchGongXian, self))
	self.node_list.btn_ck_destroy.button:AddClickListener(BindTool.Bind(self.OnClickCKDestroy, self))
    self.node_list.layout_checkbox_prof:SetActive(true)
    --self.node_list.layout_checkbox_star:SetActive(false)
    --self.node_list.auto_destory_btn:SetActive(false)
    self.node_list.img_hook:SetActive(false)
    --self.node_list.img_hook_star:SetActive(false)
    self.node_list.layout_checkbox_prof.button:AddClickListener(BindTool.Bind(self.OnClickCKCheckBox, self))
    --self.node_list.layout_checkbox_star.button:AddClickListener(BindTool.Bind(self.OnClickCKCheckStarBox, self))
	--self.node_list.img_all_hook:SetActive(false)
	self.node_list.layout_pinzhi.button:AddClickListener(BindTool.Bind(self.OnClickCKPinZhi, self))
	self.node_list.layout_pinjie.button:AddClickListener(BindTool.Bind(self.OnClickCKPinJie, self))
	self.node_list.layout_star.button:AddClickListener(BindTool.Bind(self.OnClickCKStar, self))
	--self.node_list.auto_destory_btn.button:AddClickListener(BindTool.Bind(self.OnClickAutoDestory, self))
	
	--点击其他区域关闭
	self.node_list.close_pinzhi.button:AddClickListener(BindTool.Bind(self.OnClickCKPinZhi, self))
	self.node_list.close_pinjie.button:AddClickListener(BindTool.Bind(self.OnClickCKPinJie, self))
	self.node_list.close_star.button:AddClickListener(BindTool.Bind(self.OnClickCKStar, self))

	self.node_list.lbl_pinzhi_name.text.text = self.ck_pinzhi_name
	self.node_list.lbl_pinjie_name.text.text = self.ck_pinjie_name
	self.node_list.lbl_star_name.text.text = self.ck_equip_star_name

	self:GuildBagLoadCallBack()
	self:RestSelectStorgGrid()
	self.node_list.text_ck_destroy.text.text = Language.Guild.DestroyEquip
end

function GuildView:DeleteGuildckView()
	if self.pinzhi_list_view then
		self.pinzhi_list_view:DeleteMe()
		self.pinzhi_list_view = nil
	end

	if self.pinjie_list_view then
		self.pinjie_list_view:DeleteMe()
		self.pinjie_list_view = nil
	end

	if self.star_list_view then
		self.star_list_view:DeleteMe()
		self.star_list_view = nil
	end
	
	if self.bag_radio then
		self.bag_radio:DeleteMe()
		self.bag_radio = nil
	end

	if self.storge_grid then
		self.storge_grid:DeleteMe()
		self.storge_grid = nil
	end
	
	if self.cangku_list_view then
		self.cangku_list_view:DeleteMe()
		self.cangku_list_view = nil
	end
end

function GuildView:OnFlushGuildckView(param_t, index)
	self:OnItemDataListChange()
	self:FlushAutoDestoryBtnStatus()
end

function GuildView:FlushAutoDestoryBtnStatus()
	--self.node_list["auto_destory_img"]:SetActive(GuildCangKuWGData.Instance:GetNeedAutoDestory())
end

function GuildView:CkBagSelectCallBack(cell)

end

function GuildView:ClickbtnGuildCK()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.GuildCkTitle)
		role_tip:SetContent(Language.Guild.GuildCkShuoMing)
	else
		print_error("GuildView:ClickbtnGuildCK","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end

function GuildView:OnClickCKPinZhi()
	self.node_list.layout_pinzhi_list:SetActive(not self.node_list.layout_pinzhi_list:GetActive())
	self.node_list.jiantou_pinzhi_down:SetActive(not self.node_list.layout_pinzhi_list:GetActive())
	self.node_list.jiantou_pinzhi_up:SetActive(self.node_list.layout_pinzhi_list:GetActive())
end

function GuildView:OnClickCKPinJie()
	self.node_list.layout_pinjie_list:SetActive(not self.node_list.layout_pinjie_list:GetActive())
	self.node_list.jiantou_pinjie_down:SetActive(not self.node_list.layout_pinjie_list:GetActive())
	self.node_list.jiantou_pinjie_up:SetActive(self.node_list.layout_pinjie_list:GetActive())
end

function GuildView:OnClickCKStar()
	self.node_list.layout_star_list:SetActive(not self.node_list.layout_star_list:GetActive())
	self.node_list.jiantou_star_down:SetActive(not self.node_list.layout_star_list:GetActive())
	self.node_list.jiantou_star_up:SetActive(self.node_list.layout_star_list:GetActive())
end

-- 勾选是否自动销毁装备
function GuildView:OnClickAutoDestory()
	GuildCangKuWGData.Instance:SetNeedAutoDestory(not GuildCangKuWGData.Instance:GetNeedAutoDestory())
	self:FlushAutoDestoryBtnStatus()
	if GuildCangKuWGData.Instance:GetNeedAutoDestory() then
		GuildWGCtrl.Instance:DestoryStorgeItem()
	end
end

function GuildView:SelectPinZhiItemCallBack(item, cell_index, is_default, is_click)
	if is_default then return end
	if not item or not item:GetData() then return end
	local data = item:GetData()
	self.ck_pinzhi_jie = data[2]
	self.ck_pinzhi_name = data[1]
	self.node_list.lbl_pinzhi_name.text.text = self.ck_pinzhi_name
	local is_active = self.node_list.layout_pinzhi_list:GetActive()
	self.node_list.layout_pinzhi_list:SetActive(false)
	self.node_list.jiantou_pinzhi_down:SetActive(not is_active)
    self.node_list.jiantou_pinzhi_up:SetActive(is_active)
	if is_active then
		self:FlushCKStorgeView()
		self:SelectAllCKStorgeCell()
	end
end

function GuildView:SelectPinJieItemCallBack(item, cell_index, is_default, is_click)
	if is_default then return end
	if not item or not item:GetData() then return end
	local data = item:GetData()
	self.ck_pinjie_color = data[2]
	self.ck_pinjie_name = data[1]
	self.node_list.lbl_pinjie_name.text.text = self.ck_pinjie_name
	local is_active = self.node_list.layout_pinjie_list:GetActive()
	self.node_list.layout_pinjie_list:SetActive(false)
	self.node_list.jiantou_pinjie_down:SetActive(not is_active)
    self.node_list.jiantou_pinjie_up:SetActive(is_active)
	if is_active then
		self:FlushCKStorgeView()
		self:SelectAllCKStorgeCell()
	end
end

function GuildView:SelectStarItemCallBack(item, cell_index, is_default, is_click)
	if is_default then return end
	if not item or not item:GetData() then return end
	local data = item:GetData()
	self.ck_equip_star = data[2]
	self.ck_equip_star_name = data[1]
	self.node_list.lbl_star_name.text.text = self.ck_equip_star_name
	local is_active = self.node_list.layout_star_list:GetActive()
	self.node_list.layout_star_list:SetActive(false)
	self.node_list.jiantou_star_down:SetActive(not is_active)
    self.node_list.jiantou_star_up:SetActive(is_active)
	if is_active then
		self:FlushCKStorgeView()
		self:SelectAllCKStorgeCell()
	end
end

function GuildView:OnClickCKDestroy()--销毁装备
	local destroy_list = self.storge_grid:GetAllSelectCell()
	if destroy_list and not next(destroy_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.DestroyTips)
	else
		local destroy_item_list = {}
		for k,v in pairs(destroy_list) do
			destroy_item_list[#destroy_item_list + 1] = {storge_index = v.index, item_id = v.item_id}
		end
		table.sort(destroy_item_list, SortTools.KeyUpperSorter("storge_index"))
		GuildWGCtrl.Instance:SendStorgetDiscardItem(#destroy_item_list, destroy_item_list)
	end
	self.storge_grid:CancleAllSelectCell()
	--self.node_list.img_hook_star:SetActive(false)
	--self.node_list.img_all_hook:SetActive(false)
end

function GuildView:RestSelectStorgGrid()
	self.star_list_view:JumpToIndex(1)
	self.pinzhi_list_view:JumpToIndex(1)
	self.pinjie_list_view:JumpToIndex(1)
	self.ck_pinzhi_jie = 0
	self.ck_pinzhi_name = Language.Guild.GuildPinZhiName[1][1]
	self.ck_pinjie_color = 0
	self.ck_pinjie_name = Language.Guild.GuildPinJieName[1][1]
	self.ck_equip_star = 0
	self.ck_equip_star_name = Language.Guild.GuildStarName[1][1]
	self.node_list.lbl_pinzhi_name.text.text = self.ck_pinzhi_name
	self.node_list.lbl_pinjie_name.text.text = self.ck_pinjie_name
	self.node_list.lbl_star_name.text.text = self.ck_equip_star_name
	self:FlushCKStorgeView()
end

function GuildView:OnClickBatchGongXian()--批量贡献
	self:RestSelectStorgGrid()
	self.is_admin = not self.is_admin
	GuildCangKuWGData.Instance:SetIsAdmin(self.is_admin)
	self.storge_grid:SetIsMultiSelect(not self.is_admin)
	GuildCangKuWGData.Instance:SetIsMultiSelect(not self.is_admin)
	--	帮主批量处理
	local data_list = self.storge_grid:GetDataList()
	local conver_list = GuildCangKuWGData.Instance:GetGuildConvertItem()
	-- self.node_list["auto_destory_btn"]:SetActive(self.is_admin == false) -- 自动销毁仙盟仓库里面橙色1星及以下的装备选项
	if self.is_admin == false then
		self.node_list.text_batch_gongxian.text.text = Language.Guild.ManagementEnd --退出管理
        self.node_list.layout_checkbox_prof:SetActive(false)
        --self.node_list.layout_checkbox_star:SetActive(false)
		local conver_list = GuildCangKuWGData.Instance:GetGuildConvertItem()
		for i,v in ipairs(conver_list) do
			table.remove(data_list,i)
		end
	else
		self.node_list.text_batch_gongxian.text.text = Language.Guild.BatchProcessing --批量处理	
		self.storge_grid:SetIsMultiSelect(false)
        GuildCangKuWGData.Instance:SetIsMultiSelect(false)
        --self.node_list.layout_checkbox_star:SetActive(false)
		self.node_list.layout_checkbox_prof:SetActive(true)
		for i,v in ipairs(conver_list) do
			table.insert(data_list,i,v.reward_item)
		end
	end	

	self.storge_grid:SetDataList(data_list)
	self.storge_grid:CancleAllSelectCell()
    self.node_list.img_hook:SetActive(false)
    --self.node_list.img_hook_star:SetActive(false)
end

function GuildView:OnClickCKCheckBox(layout)
	self.node_list.img_hook:SetActive(not self.node_list.img_hook:GetActive())
	self:FlushCKStorgeView()
end

-- function GuildView:OnClickCKCheckStarBox(layout)
--     --self.node_list.img_hook_star:SetActive(not self.node_list.img_hook_star:GetActive())
--     if self.node_list.img_hook_star:GetActive() then
--         self.storge_grid:SetGuildCangkuEquipMeltSelect(3)
--     else
--         self.storge_grid:CancleAllSelectCell()
--     end
--     self.storge_grid:RefreshActiveCellViews()
-- end

function GuildView:OnItemDataListChange()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_EVENT_LIST, guild_id)
	self:FlushCKStorgeView()
	--GuildWGCtrl.Instance:RefreshTempBagView()
	self:FlushGuildBagList()
end

function GuildView:FlushCKStorgeView()
	local guild_post = GameVoManager.Instance:GetMainRoleVo().guild_post--军团职位
	if guild_post == GUILD_POST.FU_TUANGZHANG or guild_post == GUILD_POST.TUANGZHANG then
        self.node_list.btn_batch_gongxian:SetActive(true)
		self.node_list.btn_ck_destroy:SetActive(true)
    else
		self.node_list.btn_batch_gongxian:SetActive(false)
		self.node_list.btn_ck_destroy:SetActive(false)
	end

	local is_prof = self.node_list.img_hook:GetActive() --勾选显示职位的toggle
	local eq_data_list = GuildCangKuWGData.Instance:GetGuildStorgeItemDataList(is_prof,self.ck_pinzhi_jie,self.ck_pinjie_color,self.ck_equip_star,self.is_admin)--获得仓库里的所有物品
    self.storge_grid:SetDataList(eq_data_list)
    self.storge_grid:CancleAllSelectCell()
    -- if self.node_list.img_hook_star:GetActive() then
    --     self.storge_grid:SetGuildCangkuEquipMeltSelect(3)
    -- else
    --     self.storge_grid:CancleAllSelectCell()
    -- end

	local bagnum = #eq_data_list
	local open_count = GuildCangKuWGData.Instance:GetOpengridCount()
	self.node_list.cangku_rongliang.text.text = string.format(Language.Guild.CKrongLiang, bagnum, open_count)
	self.node_list.label_cur_gongxian.text.text = GuildCangKuWGData.Instance:GetStorgeScore()
end

function GuildView:SelectAllCKStorgeCell()
	self.storge_grid:CancleAllSelectCell()
    local data_list = self.storge_grid:GetDataList()
    if self.is_admin == false and (self.ck_pinzhi_jie ~= 0 or self.ck_pinjie_color ~= 0) then
    	self.storge_grid:SetAllSelectCell(#data_list)
    end
end

function GuildView:FlushCangKuList()
	local event_list = GuildDataConst.GUILD_EVENT_LIST
	local event_show_list = GuildDataConst.SHOW_EVENT_LIST
	local cangku_data_list = {}
	local cangku_data_list2 = {}
	for i=1, event_list.count do
		local item = event_list.list[event_list.count - i + 1]
		if nil ~= event_show_list[item.event_type] and item.event_type == GuildDataConst.GUILD_EVENT_TYPE.STORAGE_OPERATE then
			table.insert(cangku_data_list, item)
		end
	end
	if nil ~= self.cangku_list_view then
		table.sort(cangku_data_list, SortTools.KeyUpperSorter('event_time'))
		if #cangku_data_list > 50 then --控制显示50条记录
			for k,v in pairs(cangku_data_list) do
				if k<=50 then
					table.insert(cangku_data_list2,v)
				end
			end
			self.cangku_list_view:SetDataList(cangku_data_list2)
			return
		end
		self.cangku_list_view:SetDataList(cangku_data_list)
	end
	--if self.node_list.img_jaunxian ~= nil then
	--	self.node_list.img_jaunxian:SetActive(#cangku_data_list <= 0)
	--end
end



----------------CKPinZhiListRender-------------------
CKPinZhiListRender = CKPinZhiListRender or BaseClass(BaseRender)

function CKPinZhiListRender:__init()
end

function CKPinZhiListRender:__delete()
end

function CKPinZhiListRender:OnFlush()
	if not self.data then return end
	self.node_list.lbl_pinzhi_name.text.text = self.data[1]
	self.node_list.selectpinzhi_bg:SetActive(self.is_select)
end

-- 选择状态改变
function CKPinZhiListRender:OnSelectChange(is_select)
	if self.node_list.selectpinzhi_bg then
		self.node_list.selectpinzhi_bg:SetActive(is_select)
	end
end


----------------CKPinJieListRender-------------------
CKPinJieListRender = CKPinJieListRender or BaseClass(BaseRender)

function CKPinJieListRender:__init()
end

function CKPinJieListRender:__delete()
end

function CKPinJieListRender:OnFlush()
	if not self.data then return end
	self.node_list.lbl_pinjie_name.text.text = self.data[1]
	self.node_list.selectpinjie_bg:SetActive(self.is_select)
end
function CKPinJieListRender:OnSelectChange(is_select)
	if self.node_list.selectpinjie_bg then
		self.node_list.selectpinjie_bg:SetActive(is_select)
	end
end

----------------CKStarListRender-------------------
CKStarListRender = CKStarListRender or BaseClass(BaseRender)

function CKStarListRender:__init()
end

function CKStarListRender:__delete()
end

function CKStarListRender:OnFlush()
	if not self.data then return end
	self.node_list.lbl_star_name.text.text = self.data[1]
	self.node_list.select_bg:SetActive(self.is_select)
end
function CKStarListRender:OnSelectChange(is_select)
	if self.node_list.select_bg then
		self.node_list.select_bg:SetActive(is_select)
	end
end

----------------CKListItemRender-------------------
CKListItemRender = CKListItemRender or BaseClass(BaseRender)

function CKListItemRender:__init()
	self.node_list.rich_cangku_tips.button:AddClickListener(BindTool.Bind(self.OnGuildJoinHandler, self))
end

function CKListItemRender:__delete()
	
end

function CKListItemRender:OnGuildJoinHandler()

	local data = {}
	data.item_id = self.data.param1
	data.param = self.data.item_data
	TipWGCtrl.Instance:OpenItem(data)
end


function CKListItemRender:OnFlush()
	if not self.data then return end
	local itemname = ItemWGData.Instance:GetItemName(self.data.param1)
	local color = ItemWGData.Instance:GetItemColor(self.data.param1)
	local key = "Type_" .. self.data.event_type .."_" .. self.data.param0
	local item_name = ToColorStr(itemname, color)
	local history_params = os.date("*t", self.data.event_time)
	local mount = string.format(Language.Common.XXMXXD, history_params.month, history_params.day)
	local day = string.format(Language.Common.XXHXXM, history_params.hour, history_params.min)

	local rich_cangku_tips_str = ""
	if self.data.param0 == 3 then
		rich_cangku_tips_str = mount.. " ".. day.. "\n" .. string.format(Language.Guild[key], item_name)
	else
		rich_cangku_tips_str = mount.. " ".. day.. "\n" .. string.format(Language.Guild[key], self.data.event_owner, item_name)
	end

	self.node_list.rich_cangku_tips.text.text = rich_cangku_tips_str
end

-----------------------CKBagCell-------------
CKBagCell = CKBagCell or BaseClass(BaseGridRender)

function CKBagCell:__init()
end

function CKBagCell:__delete()
end

function CKBagCell:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["cell"])
	-- self.item_cell:SetIsShowTips(false)
	self.item_cell:SetItemTipFrom(ItemTip.FROM_STORGE_ON_GUILD_STORGE)
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnClick, self))
end

function CKBagCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function CKBagCell:OnFlush()
	if not self.data then return end
	self.item_cell:SetData(self.data)
end

function CKBagCell:SetClickCallBack(call_back)
	self.click_callback = call_back
end

function CKBagCell:OnClick()
	if not next(self.data) then return end 
	local is_admin = GuildCangKuWGData.Instance:GetIsAdmin()
	if is_admin then
		local convert_cfg = GuildCangKuWGData.Instance:GetIsConvertItem(self.data.item_id)
		if convert_cfg then
			GuildWGCtrl.Instance:OpenCangKuConvertTips(convert_cfg.id)
		else
			TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_STORGE_ON_GUILD_STORGE)
		end	
	end
	if self.click_callback ~= nil then
		self.click_callback(self)
	end
end

function CKBagCell:SetSelect(is_select, item_call_back)
	local is_data = next(self.data) ~= nil
	if is_select == false then
		self.node_list["select_gou"]:SetActive(false)
		self.node_list["select"]:SetActive(false)
	end
	local is_multi_select = GuildCangKuWGData.Instance:GetIsMultiSelect()
	if is_multi_select then
		self.node_list["select_gou"]:SetActive(is_select and is_data)
	else
		self.node_list["select"]:SetActive(is_select and is_data)
	end
end