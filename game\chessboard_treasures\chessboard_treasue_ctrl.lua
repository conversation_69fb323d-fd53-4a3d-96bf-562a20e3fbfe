require("game/chessboard_treasures/chessboard_treasue_data")
require("game/chessboard_treasures/chessboard_treasue_view")
require("game/chessboard_treasures/chessboard_treasue_reward_view")

ChessBoardTreasueCtrl = ChessBoardTreasueCtrl or BaseClass(BaseWGCtrl)

function ChessBoardTreasueCtrl:__init()
	if ChessBoardTreasueCtrl.Instance then
		error("[ChessBoardTreasueCtrl]:Attempt to create singleton twice!")
	end
	ChessBoardTreasueCtrl.Instance = self

    self.data = ChessBoardTreasueData.New()

    self.view = ChessBoardTreasueView.New(GuideModuleName.ChessBoardTreasueView)
    self.reward_show_view = ChessBoardTreasueRewardView.New()
    self:RegisterAllProtocols()
end

function ChessBoardTreasueCtrl:__delete()
    ChessBoardTreasueCtrl.Instance = nil
    self.data:DeleteMe()
	self.data = nil
    
    self.view:DeleteMe()
	self.view = nil

    self.reward_show_view:DeleteMe()
    self.reward_show_view = nil
end

function ChessBoardTreasueCtrl:OpenChessBoardTreasueView()
    self.view:Open()
end

function ChessBoardTreasueCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOARechargeScoreOperate, "OnOARechargeScoreOperate")
end

function ChessBoardTreasueCtrl:OnOARechargeScoreOperate(protocol)
    self.data:SetAllCheckBoardInfo(protocol)
    self:FlushView()

    RemindManager.Instance:Fire(RemindName.RemindChessPieces)
end

function ChessBoardTreasueCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.ChessBoardTreasueView)
end

function ChessBoardTreasueCtrl:OpenChessBoardTreasueRewardView()
    if self.reward_show_view then
        if not self.reward_show_view:IsOpen() then
            self.reward_show_view:Open()
        end
    end
end