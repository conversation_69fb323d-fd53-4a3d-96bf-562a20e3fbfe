-- X-修仙缘梦.xls
local item_table={
[1]={item_id=30447,num=2,is_bind=1},
[2]={item_id=26200,num=5,is_bind=1},
[3]={item_id=90075,num=10,is_bind=1},
[4]={item_id=30447,num=3,is_bind=1},
[5]={item_id=26500,num=1,is_bind=1},
[6]={item_id=90075,num=15,is_bind=1},
[7]={item_id=30447,num=4,is_bind=1},
[8]={item_id=26515,num=1,is_bind=1},
[9]={item_id=90075,num=20,is_bind=1},
[10]={item_id=30447,num=6,is_bind=1},
[11]={item_id=90075,num=30,is_bind=1},
[12]={item_id=30447,num=10,is_bind=1},
[13]={item_id=90075,num=50,is_bind=1},
[14]={item_id=26203,num=5,is_bind=1},
[15]={item_id=22531,num=1,is_bind=1},
[16]={item_id=26349,num=5,is_bind=1},
[17]={item_id=26346,num=1,is_bind=1},
[18]={item_id=26409,num=1,is_bind=1},
[19]={item_id=26409,num=2,is_bind=1},
[20]={item_id=26409,num=3,is_bind=1},
[21]={item_id=26409,num=5,is_bind=1},
[22]={item_id=26347,num=1,is_bind=1},
[23]={item_id=26377,num=1,is_bind=1},
[24]={item_id=26191,num=1,is_bind=1},
[25]={item_id=26345,num=1,is_bind=1},
[26]={item_id=26350,num=1,is_bind=1},
[27]={item_id=22576,num=1,is_bind=1},
[28]={item_id=26415,num=2,is_bind=1},
[29]={item_id=26415,num=3,is_bind=1},
[30]={item_id=62000,num=10,is_bind=1},
[31]={item_id=62001,num=1,is_bind=1},
[32]={item_id=26378,num=1,is_bind=1},
[33]={item_id=26379,num=1,is_bind=1},
[34]={item_id=30447,num=5,is_bind=1},
[35]={item_id=90075,num=25,is_bind=1},
[36]={item_id=22117,num=1,is_bind=1},
[37]={item_id=27612,num=1,is_bind=1},
[38]={item_id=26914,num=5,is_bind=1},
[39]={item_id=22090,num=3,is_bind=1},
[40]={item_id=26344,num=5,is_bind=1},
[41]={item_id=26349,num=1,is_bind=1},
[42]={item_id=26165,num=1,is_bind=1},
[43]={item_id=28846,num=1,is_bind=1},
[44]={item_id=48071,num=2,is_bind=1},
[45]={item_id=22615,num=3,is_bind=1},
[46]={item_id=62000,num=50,is_bind=1},
[47]={item_id=29615,num=8,is_bind=1},
[48]={item_id=39105,num=100,is_bind=1},
[49]={item_id=30425,num=3,is_bind=1},
[50]={item_id=44073,num=1,is_bind=1},
[51]={item_id=36366,num=3,is_bind=1},
[52]={item_id=28851,num=1,is_bind=1},
[53]={item_id=44500,num=1,is_bind=1},
[54]={item_id=22073,num=1,is_bind=1},
[55]={item_id=26079,num=2,is_bind=1},
[56]={item_id=26393,num=3,is_bind=1},
[57]={item_id=30447,num=20,is_bind=1},
[58]={item_id=10200,num=1,is_bind=1},
[59]={item_id=29206,num=1,is_bind=1},
[60]={item_id=22532,num=1,is_bind=1},
[61]={item_id=30808,num=20,is_bind=1},
[62]={item_id=30808,num=5,is_bind=1},
[63]={item_id=30808,num=6,is_bind=1},
[64]={item_id=30447,num=30,is_bind=1},
[65]={item_id=26193,num=1,is_bind=1},
[66]={item_id=30808,num=8,is_bind=1},
[67]={item_id=26369,num=1,is_bind=1},
[68]={item_id=22733,num=20,is_bind=1},
[69]={item_id=57832,num=1,is_bind=1},
[70]={item_id=39118,num=1,is_bind=1},
[71]={item_id=27814,num=1,is_bind=1},
[72]={item_id=27908,num=1,is_bind=1},
[73]={item_id=48071,num=1,is_bind=1},
}

return {
task={
{open_day=1,tab_type=1,param_1=300,reward_item={[0]=item_table[1],[1]=item_table[2]},reward_show={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},reward_progress=10,},
{seq=1,open_day=1,reward_item={[0]=item_table[1],[1]=item_table[2]},reward_show={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},},
{seq=2,open_day=1,reward_item={[0]=item_table[4],[1]=item_table[5]},reward_show={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6]},},
{seq=3,open_day=1,reward_item={[0]=item_table[7],[1]=item_table[8]},reward_show={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9]},},
{seq=4,open_day=1,reward_item={[0]=item_table[10],[1]=item_table[5]},reward_show={[0]=item_table[10],[1]=item_table[5],[2]=item_table[11]},},
{seq=5,open_day=1,reward_item={[0]=item_table[12],[1]=item_table[8]},reward_show={[0]=item_table[12],[1]=item_table[8],[2]=item_table[13]},},
{seq=6,open_day=1,task_seq=7,task_type=85,reward_item={[0]=item_table[1],[1]=item_table[14]},reward_show={[0]=item_table[1],[1]=item_table[14],[2]=item_table[3]},reward_progress=10,task_name="摇光之路（一）",task_desc="上阵1只幻兽",open_panel="ControlBeastsView#beasts_battle",},
{seq=7,task_seq=8,task_type=83,param_1=3,param_2=50,task_name="摇光之路（二）",task_desc="上阵3只50级幻兽",},
{seq=8,task_seq=9,task_type=111,task_name="摇光之路（三）",task_desc="世界聊天频道发送信息1次",is_close_view=1,open_panel="ChatView",},
{seq=9,open_day=1,task_seq=10,task_type=112,reward_item={[0]=item_table[7],[1]=item_table[14]},reward_show={[0]=item_table[7],[1]=item_table[14],[2]=item_table[9]},task_name="摇光之路（六）",task_desc="成功添加1次好友",open_panel="society#society_friend",},
{seq=10,task_seq=11,task_type=37,reward_item={[0]=item_table[1],[1]=item_table[2]},reward_show={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},task_name="摇光之路（八）",task_desc="通关1次日月修行",open_panel="fubenpanel#fubenpanel_exp",},
{seq=11,task_seq=12,param_1=2,task_name="摇光之路（九）",task_desc="通关2次日月修行",},
{seq=12,open_day=1,task_seq=13,task_type=113,reward_item={[0]=item_table[4],[1]=item_table[2]},reward_show={[0]=item_table[4],[1]=item_table[2],[2]=item_table[6]},reward_progress=15,task_name="摇光之路（十）",task_desc="成功加入仙盟",is_close_view=1,open_panel="guild",},
{seq=13,open_day=1,task_seq=14,task_type=114,param_1=6,reward_item={[0]=item_table[7],[1]=item_table[2]},reward_show={[0]=item_table[7],[1]=item_table[2],[2]=item_table[9]},task_name="摇光之路（十一）",task_desc="仙修境界达到筑基三重",open_panel="XiuWeiView",},
{seq=14,open_day=1,task_type=39,param_1=5,task_name="摇光之路（十八）",task_desc="击杀5只仙遗洞天BOSS",open_panel="boss#boss_vip",},
{seq=15,task_seq=16,param_1=8,task_name="摇光之路（十九）",task_desc="击杀8只仙遗洞天BOSS",},
{seq=16,open_day=1,task_seq=17,param_1=10,reward_item={[0]=item_table[4],[1]=item_table[15]},reward_show={[0]=item_table[4],[1]=item_table[15],[2]=item_table[6]},task_name="摇光之路（二十）",task_desc="击杀10只仙遗洞天BOSS",},
{seq=17,open_day=1,tab_type=3,task_seq=18,task_type=91,param_1=15,reward_item={[0]=item_table[4],[1]=item_table[16]},reward_show={[0]=item_table[4],[1]=item_table[16],[2]=item_table[6]},reward_progress=15,task_name="摇光之路（二一）",task_desc="天梯争霸挑战15次",open_panel="act_jjc#arena_tianti",},
{seq=18,open_day=1,task_type=115,param_2=68,task_name="摇光之路（二二）",task_desc="挑战2次昆仑玉虚",open_panel="fubenpanel#fubenpanel_control_beasts",},
{seq=19,open_day=1,tab_type=3,task_seq=20,task_type=101,param_1=2,reward_item={[0]=item_table[10],[1]=item_table[17]},reward_show={[0]=item_table[10],[1]=item_table[17],[2]=item_table[11]},reward_progress=30,task_name="摇光之路（二三）",task_desc="挑战2次熔火之心",open_panel="fubenpanel#fubenpanel_copper",},
{seq=20,open_day=1,task_type=102,reward_item={[0]=item_table[12],[1]=item_table[18]},reward_show={[0]=item_table[12],[1]=item_table[18],[2]=item_table[13]},task_name="摇光之路（二四）",task_desc="挑战3次天峰夺宝",open_panel="fubenpanel#fubenpanel_equip_high",},
{seq=21,tab_type=1,param_1=300,reward_item={[0]=item_table[1],[1]=item_table[19]},reward_show={[0]=item_table[1],[1]=item_table[19],[2]=item_table[3]},reward_progress=10,},
{seq=22,task_seq=2,param_1=600,reward_item={[0]=item_table[1],[1]=item_table[20]},reward_show={[0]=item_table[1],[1]=item_table[20],[2]=item_table[3]},task_name="摇光之路（十三）",task_desc="累计在线10分钟",},
{seq=23,task_seq=3,param_1=1200,reward_item={[0]=item_table[4],[1]=item_table[21]},reward_show={[0]=item_table[4],[1]=item_table[21],[2]=item_table[6]},reward_progress=15,task_name="摇光之路（十四）",task_desc="累计在线20分钟",},
{seq=24,tab_type=1,task_seq=4,param_1=1800,reward_item={[0]=item_table[7],[1]=item_table[21]},reward_show={[0]=item_table[7],[1]=item_table[21],[2]=item_table[9]},task_name="摇光之路（十五）",task_desc="累计在线30分钟",},
{seq=25,task_seq=5,param_1=3600,reward_item={[0]=item_table[10],[1]=item_table[21]},reward_show={[0]=item_table[10],[1]=item_table[21],[2]=item_table[11]},reward_progress=30,task_name="摇光之路（十六）",task_desc="累计在线60分钟",},
{seq=26,task_seq=6,param_1=5400,reward_item={[0]=item_table[12],[1]=item_table[21]},reward_show={[0]=item_table[12],[1]=item_table[21],[2]=item_table[13]},reward_progress=50,task_name="摇光之路（十七）",task_desc="累计在线90分钟",},
{seq=27,task_seq=7,task_type=114,param_1=10,reward_item={[0]=item_table[1],[1]=item_table[22]},reward_show={[0]=item_table[1],[1]=item_table[22],[2]=item_table[3]},reward_progress=10,task_name="开阳之路（一）",task_desc="仙修境界达到元婴二重",open_panel="XiuWeiView",},
{seq=28,task_seq=8,task_type=93,param_1=100,reward_item={[0]=item_table[4],[1]=item_table[23]},reward_show={[0]=item_table[4],[1]=item_table[23],[2]=item_table[6]},reward_progress=15,task_name="开阳之路（二）",task_desc="阵符总强化等级达到100级",open_panel="MengLingView#mengling_strong",},
{seq=29,task_seq=9,task_type=94,param_1=5,param_2=3,reward_item={[0]=item_table[7],[1]=item_table[24]},reward_show={[0]=item_table[7],[1]=item_table[24],[2]=item_table[9]},task_name="开阳之路（四）",task_desc="穿戴5个紫色品质阵符",open_panel="MengLingView#mengling_bag",},
{seq=30,task_seq=10,task_type=92,param_1=11,reward_item={[0]=item_table[1],[1]=item_table[25]},reward_show={[0]=item_table[1],[1]=item_table[25],[2]=item_table[3]},reward_progress=10,task_name="开阳之路（五）",task_desc="穿戴11个阵符",open_panel="MengLingView#mengling_bag",},
{seq=31,task_seq=11,param_3=1,reward_item={[0]=item_table[4],[1]=item_table[26]},reward_show={[0]=item_table[4],[1]=item_table[26],[2]=item_table[6]},reward_progress=15,task_name="开阳之路（六）",task_desc="激活1套紫色品质的乾坤阵",},
{seq=32,task_seq=12,task_type=95,param_2=3,param_3=2,reward_item={[0]=item_table[7],[1]=item_table[5]},reward_show={[0]=item_table[7],[1]=item_table[5],[2]=item_table[9]},task_name="开阳之路（八）",task_desc="激活1套紫色品质的五行阵",open_panel="MengLingView#mengling_bag",},
{seq=33,task_seq=13,param_1=200,reward_item={[0]=item_table[1],[1]=item_table[16]},reward_show={[0]=item_table[1],[1]=item_table[16],[2]=item_table[3]},reward_progress=10,task_name="开阳之路（九）",task_desc="等级达到200级",},
{seq=34,task_seq=14,task_type=3,param_1=240,reward_item={[0]=item_table[4],[1]=item_table[15]},reward_show={[0]=item_table[4],[1]=item_table[15],[2]=item_table[6]},reward_progress=15,task_name="开阳之路（十）",task_desc="等级达到240级",},
{seq=35,task_seq=15,task_type=83,param_2=200,reward_item={[0]=item_table[7],[1]=item_table[22]},reward_show={[0]=item_table[7],[1]=item_table[22],[2]=item_table[9]},task_name="开阳之路（十一）",task_desc="上阵3只200级幻兽",},
{seq=36,task_seq=16,task_type=84,param_1=3,param_2=5,task_name="开阳之路（十四）",task_desc="上阵3只5星幻兽",open_panel="ControlBeastsView#beasts_battle",},
{seq=37,task_seq=17,task_type=84,param_2=6,reward_item={[0]=item_table[7],[1]=item_table[27]},reward_show={[0]=item_table[7],[1]=item_table[27],[2]=item_table[9]},task_name="开阳之路（十五）",task_desc="上阵1只6星幻兽",open_panel="ControlBeastsView#beasts_battle",},
{seq=38,tab_type=3,task_seq=18,task_type=98,reward_item={[0]=item_table[1],[1]=item_table[25]},reward_show={[0]=item_table[1],[1]=item_table[25],[2]=item_table[3]},reward_progress=10,task_name="开阳之路（二十）",task_desc="击杀1只灵妖奇脉BOSS",open_panel="boss#boss_personal",},
{seq=39,tab_type=3,task_seq=19,task_type=103,param_2=3119,reward_item={[0]=item_table[1],[1]=item_table[26]},reward_show={[0]=item_table[1],[1]=item_table[26],[2]=item_table[3]},reward_progress=10,task_name="开阳之路（二一）",task_desc="参加1次阵地战",open_panel="PositionalWarfareView",},
{seq=40,tab_type=3,task_seq=20,task_type=104,param_2=2,reward_item={[0]=item_table[4],[1]=item_table[28]},reward_show={[0]=item_table[4],[1]=item_table[28],[2]=item_table[6]},reward_progress=15,task_name="开阳之路（二二）",task_desc="阵地战中击杀1只精英BOSS",open_panel="PositionalWarfareView",},
{seq=41,task_seq=21,param_1=18,reward_item={[0]=item_table[4],[1]=item_table[29]},reward_show={[0]=item_table[4],[1]=item_table[29],[2]=item_table[6]},reward_progress=15,task_name="开阳之路（二三）",task_desc="击杀18只仙遗洞天BOSS",},
{seq=42,task_seq=22,param_1=24,reward_item={[0]=item_table[7],[1]=item_table[15]},reward_show={[0]=item_table[7],[1]=item_table[15],[2]=item_table[9]},task_name="开阳之路（二五）",task_desc="击杀24只仙遗洞天BOSS",},
{seq=43,tab_type=3,task_seq=23,task_type=39,param_1=30,reward_item={[0]=item_table[7],[1]=item_table[22]},reward_show={[0]=item_table[7],[1]=item_table[22],[2]=item_table[9]},task_name="开阳之路（二七）",task_desc="击杀30只仙遗洞天BOSS",open_panel="boss#boss_vip",},
{seq=44,open_day=3,reward_item={[0]=item_table[1],[1]=item_table[30]},reward_show={[0]=item_table[1],[1]=item_table[30],[2]=item_table[3]},},
{seq=45,open_day=3,reward_item={[0]=item_table[1],[1]=item_table[30]},reward_show={[0]=item_table[1],[1]=item_table[30],[2]=item_table[3]},},
{seq=46,open_day=3,reward_item={[0]=item_table[4],[1]=item_table[30]},reward_show={[0]=item_table[4],[1]=item_table[30],[2]=item_table[6]},},
{seq=47,open_day=3,reward_item={[0]=item_table[7],[1]=item_table[30]},reward_show={[0]=item_table[7],[1]=item_table[30],[2]=item_table[9]},},
{seq=48,open_day=3,reward_item={[0]=item_table[10],[1]=item_table[30]},reward_show={[0]=item_table[10],[1]=item_table[30],[2]=item_table[11]},},
{seq=49,open_day=3,reward_item={[0]=item_table[12],[1]=item_table[30]},reward_show={[0]=item_table[12],[1]=item_table[30],[2]=item_table[13]},},
{seq=50,open_day=3,task_type=105,task_name="玉衡之路（十一）",task_desc="流光秘宝寻宝1次",open_panel="TreasureHunt#treasurehunt_equip",},
{seq=51,task_seq=8,param_1=10,reward_item={[0]=item_table[1],[1]=item_table[22]},reward_show={[0]=item_table[1],[1]=item_table[22],[2]=item_table[3]},task_name="玉衡之路（十二）",task_desc="流光秘宝寻宝10次",},
{seq=52,task_seq=9,param_1=20,reward_item={[0]=item_table[4],[1]=item_table[23]},reward_show={[0]=item_table[4],[1]=item_table[23],[2]=item_table[6]},reward_progress=15,task_name="玉衡之路（十三）",task_desc="流光秘宝寻宝20次",},
{seq=53,task_seq=10,param_1=50,reward_item={[0]=item_table[7],[1]=item_table[27]},reward_show={[0]=item_table[7],[1]=item_table[27],[2]=item_table[9]},task_name="玉衡之路（十四）",task_desc="流光秘宝寻宝50次",},
{seq=54,open_day=3,task_seq=11,task_type=105,param_1=100,param_2=1,reward_item={[0]=item_table[7],[1]=item_table[25]},reward_show={[0]=item_table[7],[1]=item_table[25],[2]=item_table[9]},task_name="玉衡之路（十五）",task_desc="流光秘宝寻宝100次",open_panel="TreasureHunt#treasurehunt_equip",},
{seq=55,open_day=3,task_type=106,param_1=5,param_2=2,task_name="玉衡之路（十六）",task_desc="穿戴5件2星阴雷法器",open_panel="ThunderManaSelectView",},
{seq=56,task_seq=13,param_3=1,reward_item={[0]=item_table[1],[1]=item_table[30]},reward_show={[0]=item_table[1],[1]=item_table[30],[2]=item_table[3]},task_name="玉衡之路（十七）",task_desc="穿戴5件2星阳雷法器",},
{seq=57,open_day=3,task_seq=14,task_type=107,param_2=3,reward_item={[0]=item_table[1],[1]=item_table[16]},reward_show={[0]=item_table[1],[1]=item_table[16],[2]=item_table[3]},reward_progress=10,task_name="玉衡之路（二十）",task_desc="穿戴1件3阶阴雷法器",open_panel="ThunderManaSelectView",},
{seq=58,open_day=3,task_seq=15,task_type=110,param_1=30,task_name="玉衡之路（二一）",task_desc="阴系法器总淬炼等级达到30级",open_panel="ThunderManaSelectView",},
{seq=59,open_day=3,task_seq=16,task_type=110,param_1=30,param_2=1,reward_item={[0]=item_table[7],[1]=item_table[22]},reward_show={[0]=item_table[7],[1]=item_table[22],[2]=item_table[9]},task_name="玉衡之路（二二）",task_desc="阳系法器总淬炼等级达到30级",open_panel="ThunderManaSelectView",},
{seq=60,open_day=3,tab_type=3,task_seq=17,task_type=38,reward_item={[0]=item_table[1],[1]=item_table[30]},reward_show={[0]=item_table[1],[1]=item_table[30],[2]=item_table[3]},reward_progress=10,task_name="天旋之路（十一）",task_desc="伏魔战场击杀1只BOSS",open_panel="boss#boss_world",},
{seq=61,task_seq=18,param_1=3,reward_item={[0]=item_table[4],[1]=item_table[30]},reward_show={[0]=item_table[4],[1]=item_table[30],[2]=item_table[6]},reward_progress=15,task_name="天旋之路（十二）",task_desc="伏魔战场击杀3只BOSS",},
{seq=62,open_day=3,tab_type=3,task_seq=19,task_type=38,param_1=5,reward_item={[0]=item_table[7],[1]=item_table[31]},reward_show={[0]=item_table[7],[1]=item_table[31],[2]=item_table[9]},task_name="天旋之路（十三）",task_desc="伏魔战场击杀5只BOSS",open_panel="boss#boss_world",},
{seq=63,open_day=3,tab_type=3,task_seq=20,task_type=99,reward_item={[0]=item_table[10],[1]=item_table[31]},reward_show={[0]=item_table[10],[1]=item_table[31],[2]=item_table[11]},reward_progress=30,task_name="天旋之路（十四）",task_desc="谪仙之境领取1次奖励",open_panel="WorldServer#world_new_shenyuan_boss",},
{seq=64,task_seq=21,param_1=3,reward_item={[0]=item_table[12],[1]=item_table[31]},reward_show={[0]=item_table[12],[1]=item_table[31],[2]=item_table[13]},reward_progress=50,task_name="天旋之路（十五）",task_desc="谪仙之境领取3次奖励",},
{seq=65,open_day=4,reward_item={[0]=item_table[1],[1]=item_table[23]},reward_show={[0]=item_table[1],[1]=item_table[23],[2]=item_table[3]},},
{seq=66,open_day=4,reward_item={[0]=item_table[1],[1]=item_table[23]},reward_show={[0]=item_table[1],[1]=item_table[23],[2]=item_table[3]},},
{seq=67,open_day=4,reward_item={[0]=item_table[4],[1]=item_table[23]},reward_show={[0]=item_table[4],[1]=item_table[23],[2]=item_table[6]},},
{seq=68,open_day=4,tab_type=1,task_seq=4,param_1=1800,task_name="摇光之路（十五）",task_desc="累计在线30分钟",},
{seq=69,open_day=4,reward_item={[0]=item_table[10],[1]=item_table[23]},reward_show={[0]=item_table[10],[1]=item_table[23],[2]=item_table[11]},},
{seq=70,open_day=4,reward_item={[0]=item_table[12],[1]=item_table[23]},reward_show={[0]=item_table[12],[1]=item_table[23],[2]=item_table[13]},},
{seq=71,open_day=4,task_type=65,reward_item={[0]=item_table[1],[1]=item_table[32]},reward_show={[0]=item_table[1],[1]=item_table[32],[2]=item_table[3]},task_name="天枢之路（一）",task_desc="星光魂契寻宝1次",open_panel="TreasureHunt#treasurehunt_fuwen",},
{seq=72,task_seq=8,param_1=10,reward_item={[0]=item_table[4],[1]=item_table[33]},reward_show={[0]=item_table[4],[1]=item_table[33],[2]=item_table[6]},reward_progress=15,task_name="天枢之路（二）",task_desc="星光魂契寻宝10次",},
{seq=73,open_day=4,task_seq=9,task_type=65,param_1=20,task_name="天枢之路（三）",task_desc="星光魂契寻宝20次",open_panel="TreasureHunt#treasurehunt_fuwen",},
{seq=74,task_seq=10,param_1=30,reward_item={[0]=item_table[7],[1]=item_table[32]},reward_show={[0]=item_table[7],[1]=item_table[32],[2]=item_table[9]},task_name="天枢之路（四）",task_desc="星光魂契寻宝30次",},
{seq=75,open_day=4,task_seq=11,task_type=87,param_1=10,reward_item={[0]=item_table[1],[1]=item_table[33]},reward_show={[0]=item_table[1],[1]=item_table[33],[2]=item_table[3]},reward_progress=10,task_name="天权之路（五）",task_desc="镶嵌10张魂卡",open_panel="MingWenView#ming_wen_xiang_qian",},
{seq=76,open_day=4,task_seq=12,task_type=7,param_1=8,param_2=4,reward_item={[0]=item_table[4],[1]=item_table[23]},reward_show={[0]=item_table[4],[1]=item_table[23],[2]=item_table[6]},reward_progress=15,task_name="天权之路（六）",task_desc="镶嵌8张橙色品质魂卡",open_panel="MingWenView#ming_wen_xiang_qian",},
{seq=77,open_day=4,task_seq=13,task_type=88,param_1=5,param_2=20,reward_item={[0]=item_table[7],[1]=item_table[27]},reward_show={[0]=item_table[7],[1]=item_table[27],[2]=item_table[9]},task_name="天权之路（六）",task_desc="镶嵌5张20级魂卡",open_panel="MingWenView#ming_wen_xiang_qian",},
{seq=78,open_day=4,task_seq=14,task_type=89,param_1=8,param_2=5,param_3=30,reward_item={[0]=item_table[10],[1]=item_table[23]},reward_show={[0]=item_table[10],[1]=item_table[23],[2]=item_table[11]},reward_progress=30,task_name="天权之路（六）",task_desc="镶嵌8张30级红色品质魂卡",open_panel="MingWenView#ming_wen_xiang_qian",},
{seq=79,task_seq=15,task_type=99,param_1=5,task_name="天权之路（二六）",task_desc="谪仙之境领取5次奖励",open_panel="WorldServer#world_new_shenyuan_boss",},
{seq=80,task_seq=16,param_1=8,reward_item={[0]=item_table[34],[1]=item_table[33]},reward_show={[0]=item_table[34],[1]=item_table[33],[2]=item_table[35]},reward_progress=25,task_name="天权之路（二七）",task_desc="谪仙之境领取8次奖励",},
{seq=81,open_day=4,param_1=70,reward_item={[0]=item_table[10],[1]=item_table[23]},reward_show={[0]=item_table[10],[1]=item_table[23],[2]=item_table[11]},reward_progress=30,task_name="天权之路（二八）",task_desc="九重劫塔通过第70关",},
{seq=82,open_day=4,param_1=80,reward_item={[0]=item_table[7],[1]=item_table[32]},reward_show={[0]=item_table[7],[1]=item_table[32],[2]=item_table[9]},task_name="天权之路（二九）",task_desc="九重劫塔通过第80关",},
{seq=83,open_day=4,task_type=100,param_1=2,task_name="天权之路（三十）",task_desc="挑战2次暗翼之巢",open_panel="fubenpanel#fubenpanel_pet",},
{seq=84,open_day=5,reward_item={[0]=item_table[1],[1]=item_table[36]},reward_show={[0]=item_table[1],[1]=item_table[36],[2]=item_table[3]},},
{seq=85,open_day=5,},
{seq=86,open_day=5,},
{seq=87,open_day=5,reward_item={[0]=item_table[7],[1]=item_table[26]},reward_show={[0]=item_table[7],[1]=item_table[26],[2]=item_table[9]},},
{seq=88,open_day=5,reward_item={[0]=item_table[10],[1]=item_table[37]},reward_show={[0]=item_table[10],[1]=item_table[37],[2]=item_table[11]},},
{seq=89,open_day=5,reward_item={[0]=item_table[12],[1]=item_table[15]},reward_show={[0]=item_table[12],[1]=item_table[15],[2]=item_table[13]},},
{seq=90,task_seq=7,task_type=67,param_2=1,reward_item={[0]=item_table[1],[1]=item_table[15]},reward_show={[0]=item_table[1],[1]=item_table[15],[2]=item_table[3]},task_name="天枢之路（十一）",task_desc="激活1个蓝色魂环",},
{seq=91,task_seq=8,param_1=2,reward_item={[0]=item_table[4],[1]=item_table[22]},reward_show={[0]=item_table[4],[1]=item_table[22],[2]=item_table[6]},reward_progress=15,task_name="天枢之路（十二）",task_desc="激活2个蓝色魂环",},
{seq=92,task_seq=9,param_2=2,reward_item={[0]=item_table[34],[1]=item_table[23]},reward_show={[0]=item_table[34],[1]=item_table[23],[2]=item_table[35]},reward_progress=25,task_name="天枢之路（十三）",task_desc="激活1个紫色魂环",},
{seq=93,open_day=5,task_seq=10,task_type=68,reward_item={[0]=item_table[1],[1]=item_table[27]},reward_show={[0]=item_table[1],[1]=item_table[27],[2]=item_table[3]},reward_progress=10,task_name="天枢之路（十八）",task_desc="魂环魂骨强化总等级达到1级",open_panel="shenshou#shj_shouhunzhuzhan",},
{seq=94,task_seq=11,param_1=2,reward_item={[0]=item_table[1],[1]=item_table[25]},reward_show={[0]=item_table[1],[1]=item_table[25],[2]=item_table[3]},task_name="天枢之路（十九）",task_desc="魂环魂骨强化总等级达到2级",},
{seq=95,task_seq=12,param_1=3,reward_item={[0]=item_table[1],[1]=item_table[26]},reward_show={[0]=item_table[1],[1]=item_table[26],[2]=item_table[3]},task_name="天枢之路（二十）",task_desc="魂环魂骨强化总等级达到3级",},
{seq=96,task_seq=13,param_1=4,reward_item={[0]=item_table[1],[1]=item_table[38]},reward_show={[0]=item_table[1],[1]=item_table[38],[2]=item_table[3]},task_name="天枢之路（二一）",task_desc="魂环魂骨强化总等级达到4级",},
{seq=97,open_day=5,task_seq=14,task_type=68,param_1=5,reward_item={[0]=item_table[7],[1]=item_table[16]},reward_show={[0]=item_table[7],[1]=item_table[16],[2]=item_table[9]},task_name="天枢之路（二二）",task_desc="魂环魂骨强化总等级达到5级",open_panel="shenshou#shj_shouhunzhuzhan",},
{seq=98,task_seq=15,task_type=69,reward_item={[0]=item_table[1],[1]=item_table[15]},reward_show={[0]=item_table[1],[1]=item_table[15],[2]=item_table[3]},reward_progress=10,task_name="天枢之路（二三）",task_desc="击杀1只荒天炎窟BOSS",},
{seq=99,task_seq=16,param_1=2,reward_item={[0]=item_table[4],[1]=item_table[22]},reward_show={[0]=item_table[4],[1]=item_table[22],[2]=item_table[6]},reward_progress=15,task_name="天枢之路（二五）",task_desc="击杀2只荒天炎窟BOSS",},
{seq=100,open_day=5,tab_type=3,task_seq=17,task_type=69,param_1=3,task_name="天枢之路（二六）",task_desc="击杀3只荒天炎窟BOSS",open_panel="WorldServer#worserv_boss_mh",},
{seq=101,task_seq=18,param_1=5,reward_item={[0]=item_table[12],[1]=item_table[27]},reward_show={[0]=item_table[12],[1]=item_table[27],[2]=item_table[13]},reward_progress=50,task_name="天枢之路（二七）",task_desc="击杀5只荒天炎窟BOSS",},
{seq=102,task_seq=19,param_2=2,reward_item={[0]=item_table[7],[1]=item_table[25]},reward_show={[0]=item_table[7],[1]=item_table[25],[2]=item_table[9]},task_name="天枢之路（二八）",task_desc="采集天系水晶1次",},
{seq=103,task_seq=20,param_2=1,reward_item={[0]=item_table[7],[1]=item_table[26]},reward_show={[0]=item_table[7],[1]=item_table[26],[2]=item_table[9]},task_name="天枢之路（二九）",task_desc="采集地系水晶1次",},
{seq=104,open_day=5,tab_type=3,task_seq=21,task_type=70,reward_item={[0]=item_table[7],[1]=item_table[38]},reward_show={[0]=item_table[7],[1]=item_table[38],[2]=item_table[9]},task_name="天枢之路（三十）",task_desc="采集人系水晶1次",open_panel="WorldServer#worserv_boss_mh",},
{seq=105,open_day=6,},
{seq=106,open_day=6,reward_item={[0]=item_table[1],[1]=item_table[27]},reward_show={[0]=item_table[1],[1]=item_table[27],[2]=item_table[3]},},
{seq=107,open_day=6,reward_item={[0]=item_table[4],[1]=item_table[25]},reward_show={[0]=item_table[4],[1]=item_table[25],[2]=item_table[6]},},
{seq=108,open_day=6,},
{seq=109,open_day=6,},
{seq=110,open_day=6,},
{seq=111,open_day=6,task_seq=7,task_type=15,param_1=6,reward_item={[0]=item_table[1],[1]=item_table[17]},reward_show={[0]=item_table[1],[1]=item_table[17],[2]=item_table[3]},reward_progress=10,task_name="天权之路（五）",task_desc="万图谱激活6个图鉴",open_panel="ShanHaiJingView",},
{seq=112,open_day=6,task_seq=8,task_type=96,param_1=8,param_2=5,reward_item={[0]=item_table[1],[1]=item_table[39]},reward_show={[0]=item_table[1],[1]=item_table[39],[2]=item_table[3]},reward_progress=10,task_name="天权之路（六）",task_desc="万图谱存在8个满星图鉴",open_panel="ShanHaiJingView",},
{seq=113,open_day=6,task_seq=9,task_type=97,param_2=10,reward_item={[0]=item_table[7],[1]=item_table[40]},reward_show={[0]=item_table[7],[1]=item_table[40],[2]=item_table[9]},task_name="天权之路（六）",task_desc="万图谱存在10阶的图鉴",open_panel="ShanHaiJingView",},
{seq=114,task_seq=10,param_1=12,reward_item={[0]=item_table[34],[1]=item_table[41]},reward_show={[0]=item_table[34],[1]=item_table[41],[2]=item_table[35]},reward_progress=25,task_name="天权之路（六）",task_desc="万图谱激活12个图鉴",},
{seq=115,task_seq=11,param_1=40,reward_item={[0]=item_table[7],[1]=item_table[42]},reward_show={[0]=item_table[7],[1]=item_table[42],[2]=item_table[9]},task_name="玉衡之路（十一）",task_desc="神武达到40级",},
{seq=116,open_day=6,task_seq=12,task_type=19,param_1=50,reward_item={[0]=item_table[7],[1]=item_table[38]},reward_show={[0]=item_table[7],[1]=item_table[38],[2]=item_table[9]},task_name="玉衡之路（十二）",task_desc="神武达到50级",open_panel="NewAppearanceWGView#new_appearance_upgrade_shenbing",},
{seq=117,task_seq=13,param_1=60,reward_item={[0]=item_table[10],[1]=item_table[16]},reward_show={[0]=item_table[10],[1]=item_table[16],[2]=item_table[11]},task_name="玉衡之路（十三）",task_desc="神武达到60级",},
{seq=118,task_seq=14,param_1=70,reward_item={[0]=item_table[10],[1]=item_table[15]},reward_show={[0]=item_table[10],[1]=item_table[15],[2]=item_table[11]},reward_progress=30,task_name="玉衡之路（十四）",task_desc="神武达到70级",},
{seq=119,task_seq=15,param_1=8,reward_item={[0]=item_table[7],[1]=item_table[22]},reward_show={[0]=item_table[7],[1]=item_table[22],[2]=item_table[9]},task_name="天旋之路（十六）",task_desc="击杀8只荒天炎窟BOSS",},
{seq=120,open_day=6,task_seq=16,param_1=12,task_name="天旋之路（十七）",task_desc="击杀12只荒天炎窟BOSS",},
{seq=121,task_seq=17,param_1=100,reward_item={[0]=item_table[7],[1]=item_table[27]},reward_show={[0]=item_table[7],[1]=item_table[27],[2]=item_table[9]},task_name="天旋之路（十八）",task_desc="九重劫塔通过第100关",},
{seq=122,open_day=6,task_seq=18,param_1=110,reward_item={[0]=item_table[7],[1]=item_table[25]},reward_show={[0]=item_table[7],[1]=item_table[25],[2]=item_table[9]},task_name="天旋之路（十九）",task_desc="九重劫塔通过第110关",},
{seq=123,open_day=6,task_seq=19,param_1=120,reward_item={[0]=item_table[7],[1]=item_table[26]},reward_show={[0]=item_table[7],[1]=item_table[26],[2]=item_table[9]},task_desc="九重劫塔通过第120关",},
{seq=124,open_day=7,},
{seq=125,open_day=7,},
{seq=126,open_day=7,},
{seq=127,open_day=7,},
{seq=128,open_day=7,},
{seq=129,open_day=7,},
{seq=130,open_day=7,task_type=81,reward_item={[0]=item_table[1],[1]=item_table[25]},reward_show={[0]=item_table[1],[1]=item_table[25],[2]=item_table[3]},task_name="天旋之路（二一）",task_desc="获得1位仙女青睐",open_panel="ArtifactView",},
{seq=131,task_seq=8,task_type=81,param_1=2,reward_item={[0]=item_table[7],[1]=item_table[26]},reward_show={[0]=item_table[7],[1]=item_table[26],[2]=item_table[9]},task_name="天旋之路（二二）",task_desc="获得2位仙女青睐",},
{seq=132,task_seq=9,param_1=5,reward_item={[0]=item_table[1],[1]=item_table[38]},reward_show={[0]=item_table[1],[1]=item_table[38],[2]=item_table[3]},reward_progress=10,task_name="天旋之路（二三）",task_desc="双修总羁绊达到5",},
{seq=133,task_seq=10,param_1=10,reward_item={[0]=item_table[1],[1]=item_table[16]},reward_show={[0]=item_table[1],[1]=item_table[16],[2]=item_table[3]},task_name="天旋之路（二五）",task_desc="双修总羁绊达到10",},
{seq=134,open_day=7,task_seq=11,task_type=82,param_1=15,task_name="天旋之路（二六）",task_desc="双修总羁绊达到15",open_panel="ArtifactView",},
{seq=135,task_seq=12,param_1=20,reward_item={[0]=item_table[4],[1]=item_table[22]},reward_show={[0]=item_table[4],[1]=item_table[22],[2]=item_table[6]},task_name="天旋之路（二七）",task_desc="双修总羁绊达到20",},
{seq=136,open_day=7,task_seq=13,task_type=82,param_1=30,task_name="天旋之路（二八）",task_desc="双修总羁绊达到30",open_panel="ArtifactView",},
{seq=137,task_seq=14,param_1=40,reward_item={[0]=item_table[7],[1]=item_table[27]},reward_show={[0]=item_table[7],[1]=item_table[27],[2]=item_table[9]},task_name="天旋之路（二九）",task_desc="双修总羁绊达到40",},
{seq=138,task_seq=15,param_1=50,reward_item={[0]=item_table[7],[1]=item_table[25]},reward_show={[0]=item_table[7],[1]=item_table[25],[2]=item_table[9]},task_name="天旋之路（三十）",task_desc="双修总羁绊达到50",},
{seq=139,task_seq=16,param_1=60,reward_item={[0]=item_table[34],[1]=item_table[26]},reward_show={[0]=item_table[34],[1]=item_table[26],[2]=item_table[35]},reward_progress=25,task_name="天旋之路（三一）",task_desc="双修总羁绊达到60",},
{seq=140,task_seq=17,param_1=150,reward_item={[0]=item_table[7],[1]=item_table[38]},reward_show={[0]=item_table[7],[1]=item_table[38],[2]=item_table[9]},task_name="天旋之路（十六）",task_desc="九重劫塔通过第150关",},
{seq=141,task_seq=18,param_1=160,reward_item={[0]=item_table[7],[1]=item_table[16]},reward_show={[0]=item_table[7],[1]=item_table[16],[2]=item_table[9]},task_name="天旋之路（十七）",task_desc="九重劫塔通过第160关",},
{seq=142,task_seq=19,param_1=170,reward_item={[0]=item_table[7],[1]=item_table[15]},reward_show={[0]=item_table[7],[1]=item_table[15],[2]=item_table[9]},task_name="天旋之路（十八）",task_desc="九重劫塔通过第170关",},
{seq=143,task_seq=20,param_1=180,reward_item={[0]=item_table[7],[1]=item_table[22]},reward_show={[0]=item_table[7],[1]=item_table[22],[2]=item_table[9]},task_name="天旋之路（十九）",task_desc="九重劫塔通过第180关",},
{seq=144,open_day=7,task_seq=21,task_type=64,param_1=190,task_name="天旋之路（二十）",task_desc="九重劫塔通过第190关",open_panel="fubenpanel#fubenpanel_welkin",}
},

task_meta_table_map={
[45]=22,	-- depth:1
[66]=22,	-- depth:1
[85]=22,	-- depth:1
[106]=85,	-- depth:2
[125]=85,	-- depth:2
[88]=25,	-- depth:1
[121]=101,	-- depth:1
[128]=88,	-- depth:2
[48]=25,	-- depth:1
[34]=35,	-- depth:1
[27]=25,	-- depth:1
[26]=25,	-- depth:1
[109]=88,	-- depth:2
[145]=101,	-- depth:1
[23]=22,	-- depth:1
[4]=25,	-- depth:1
[24]=25,	-- depth:1
[90]=27,	-- depth:2
[89]=26,	-- depth:2
[107]=23,	-- depth:2
[9]=10,	-- depth:1
[86]=107,	-- depth:3
[75]=74,	-- depth:1
[11]=7,	-- depth:1
[72]=7,	-- depth:1
[71]=27,	-- depth:2
[70]=26,	-- depth:2
[108]=24,	-- depth:2
[6]=27,	-- depth:2
[110]=89,	-- depth:3
[139]=137,	-- depth:1
[138]=137,	-- depth:1
[2]=23,	-- depth:2
[132]=137,	-- depth:1
[131]=7,	-- depth:1
[130]=90,	-- depth:3
[129]=89,	-- depth:3
[3]=24,	-- depth:2
[127]=108,	-- depth:3
[126]=107,	-- depth:3
[5]=26,	-- depth:2
[116]=117,	-- depth:1
[111]=90,	-- depth:3
[68]=24,	-- depth:2
[67]=23,	-- depth:2
[87]=108,	-- depth:3
[50]=27,	-- depth:2
[36]=37,	-- depth:1
[43]=44,	-- depth:1
[46]=23,	-- depth:2
[47]=24,	-- depth:2
[49]=26,	-- depth:2
[119]=117,	-- depth:1
[115]=112,	-- depth:1
[42]=44,	-- depth:1
[118]=119,	-- depth:2
[120]=121,	-- depth:2
[123]=145,	-- depth:2
[122]=123,	-- depth:3
[104]=105,	-- depth:1
[124]=145,	-- depth:2
[133]=137,	-- depth:1
[134]=133,	-- depth:2
[135]=35,	-- depth:1
[136]=135,	-- depth:2
[32]=33,	-- depth:1
[140]=137,	-- depth:1
[141]=145,	-- depth:2
[142]=145,	-- depth:2
[143]=145,	-- depth:2
[103]=105,	-- depth:1
[73]=74,	-- depth:1
[83]=142,	-- depth:3
[84]=143,	-- depth:3
[59]=35,	-- depth:1
[54]=55,	-- depth:1
[144]=145,	-- depth:2
[80]=83,	-- depth:4
[91]=94,	-- depth:1
[93]=91,	-- depth:2
[12]=11,	-- depth:2
[95]=94,	-- depth:1
[96]=94,	-- depth:1
[97]=94,	-- depth:1
[51]=91,	-- depth:2
[99]=105,	-- depth:1
[62]=61,	-- depth:1
[19]=84,	-- depth:4
[82]=141,	-- depth:3
[53]=55,	-- depth:1
[102]=101,	-- depth:1
[65]=64,	-- depth:1
[52]=51,	-- depth:3
[15]=99,	-- depth:2
[92]=91,	-- depth:2
[17]=42,	-- depth:2
[8]=7,	-- depth:1
[100]=101,	-- depth:1
[81]=80,	-- depth:5
[21]=65,	-- depth:2
[16]=15,	-- depth:3
[56]=96,	-- depth:2
[57]=56,	-- depth:3
},
daily_cfg={
{limit_time="3|1|3",param1=2000,attr_type1="kill_monster_per_exp",ylq_reward_icon="a3_xxym_icon_jy",},
{open_day=2,init_price="1000|3240|1500",price="500|1620|750",gift_star="12|16|8",limit_time="1|2|1",rmb_type=3,show_item={[0]=item_table[24],[1]=item_table[43],[2]=item_table[44]},param1=40000,skill_icon=1008,skill_name="破魔印记",skill_desc="生命值提高40000点",button_name="寻仙入宗",task_max_num=23,ylq_reward_icon="a3_xxym_icon_sxts",},
{open_day=3,init_price="3000|900|3000",price="1500|450|1500",gift_star="15|4|15",rmb_type=4,show_item={[0]=item_table[31],[1]=item_table[45],[2]=item_table[46]},skill_icon=1009,skill_name="生生不息",skill_desc="每升1级提高200生命",button_name="磨炼仙志",ylq_reward_icon="a3_xxym_icon_sxts",},
{open_day=4,init_price="3600|1800|2400",price="1800|900|1200",gift_star="20|6|8",limit_time="3|3|3",rmb_type=5,show_item={[0]=item_table[47],[1]=item_table[48],[2]=item_table[49]},param2=300,attr_type1="pojia_jc_per",attr_type2="fangyu_jc_per",skill_icon=1010,skill_name="乘风破浪",skill_desc="破甲+2%，防御+3%",button_name="参悟仙境",task_max_num=19,},
{open_day=5,init_price="8000|900|2500",price="4000|450|1250",gift_star="30|4|10",rmb_type=6,show_item={[0]=item_table[50],[1]=item_table[51],[2]=item_table[52]},param1=1500,attr_type1="rare_exterior_rate_per",skill_icon=1011,skill_name="大操盘手",skill_desc="外观掉落概率+15%",button_name="踏入仙列",},
{open_day=6,init_price="5000|7000|1500",price="2500|3500|750",gift_star="20|25|6",limit_time="4|1|5",rmb_type=7,show_item={[0]=item_table[31],[1]=item_table[53],[2]=item_table[54]},param1=2500,attr_type1="rare_equip_rate_per",skill_icon=1012,skill_name="欧皇加身",skill_desc="极品装备掉率+25%",button_name="仙游荒古",task_max_num=19,},
{open_day=7,init_price="2400|7000|2700",price="1200|3500|1350",gift_star="15|25|10",limit_time="5|1|5",rmb_type=8,show_item={[0]=item_table[55],[1]=item_table[53],[2]=item_table[56]},param1=500,attr_type1="baoji_shanghai_jm_per",skill_icon=1013,skill_name="单挑之王",skill_desc="pvp暴击伤害减免+5%",button_name="飞升仙帝",}
},

daily_cfg_meta_table_map={
},
pro_reward={
{reward_item={[0]=item_table[12],[1]=item_table[36]},},
{progress=1,condition=270,reward_item={[0]=item_table[57],[1]=item_table[36]},title="修仙之路（二）",},
{progress=2,condition=380,reward_item={[0]=item_table[58],[1]=item_table[36]},title="修仙之路（三）",},
{progress=3,condition=480,title="修仙之路（四）",},
{progress=4,condition=580,title="修仙之路（五）",},
{progress=5,condition=680,title="修仙之路（六）",},
{progress=6,condition=790,reward_item={[0]=item_table[59],[1]=item_table[60]},title="修仙之路（七）",},
{progress=7,condition=890,title="修仙之路（八）",},
{progress=8,condition=990,reward_item={[0]=item_table[57],[1]=item_table[21]},title="修仙之路（九）",},
{progress=9,condition=1090,reward_item={[0]=item_table[24],[1]=item_table[21]},title="修仙之路（十）",},
{progress=10,condition=1200,reward_item={[0]=item_table[61],[1]=item_table[57]},title="修仙之路（十一）",},
{progress=11,condition=1300,reward_item={[0]=item_table[62],[1]=item_table[57]},title="修仙之路（十二）",},
{progress=12,condition=1400,pos="0|-113",title="修仙之路（十三）",},
{progress=13,condition=1540,reward_item={[0]=item_table[62],[1]=item_table[57]},title="修仙之路（十四）",},
{progress=14,condition=1710,reward_item={[0]=item_table[62],[1]=item_table[57]},title="修仙之路（十五）",},
{progress=15,condition=1880,reward_item={[0]=item_table[63],[1]=item_table[57]},title="修仙之路（十六）",},
{progress=16,condition=2050,reward_item={[0]=item_table[63],[1]=item_table[57]},title="修仙之路（十七）",},
{progress=17,condition=2220,reward_item={[0]=item_table[24],[1]=item_table[63]},title="修仙之路（十八）",},
{progress=18,condition=2390,reward_item={[0]=item_table[63],[1]=item_table[57]},title="修仙之路（十九）",},
{progress=19,condition=2560,reward_item={[0]=item_table[63],[1]=item_table[64]},title="修仙之路（二十）",},
{progress=20,condition=2730,reward_item={[0]=item_table[65],[1]=item_table[66]},title="修仙之路（廿一）",},
{progress=21,condition=2900,reward_item={[0]=item_table[66],[1]=item_table[64]},title="修仙之路（廿二）",},
{progress=22,condition=3070,reward_item={[0]=item_table[66],[1]=item_table[64]},title="修仙之路（廿三）",},
{progress=23,condition=3240,reward_item={[0]=item_table[66],[1]=item_table[64]},title="修仙之路（廿四）",},
{progress=24,condition=3420,reward_item={[0]=item_table[66],[1]=item_table[64]},title="修仙之路（廿五）",}
},

pro_reward_meta_table_map={
[13]=14,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
task_default_table={seq=0,open_day=2,tab_type=2,task_seq=1,task_type=80,param_1=1,param_2=0,param_3=0,reward_item={[0]=item_table[7],[1]=item_table[23]},reward_show={[0]=item_table[7],[1]=item_table[23],[2]=item_table[9]},reward_coin=0,reward_bind_gold=0,reward_silver=0,reward_exp=0,attr_type1="gongji",attr_value1=0,attr_type2="",attr_value2=0,reward_progress=20,task_name="摇光之路（十二）",task_desc="累计在线5分钟",is_close_view=0,open_panel="bizuo#bizuo_bizuo",open_limit_type=0,},

daily_cfg_default_table={open_day=1,init_price="1500|1200|2400",price="750|600|1200",glod_type="1|1|1",gift_star="6|4|10",limit_time="2|3|3",rmb_type=2,show_item={[0]=item_table[67],[1]=item_table[68],[2]=item_table[69]},param1=200,param2=0,is_preview=0,attr_type1="shengming_max",attr_type2="",skill_icon=1007,skill_name="收益翻翻",skill_desc="杀怪经验提升20%",skill_desc_icon="a3_jjc_hz2",top_names="在线|修行|挑战",button_name="凡人寻仙",task_max_num=21,ylq_reward_icon="a3_xxym_icon_wg",new_show_item="90075|90075|90075",},

pro_reward_default_table={progress=0,condition=170,reward_item={[0]=item_table[57],[1]=item_table[60]},pos="0|0",title="修仙之路（一）",},

other_default_table={open_level=120,open_day=7,consume_id=27804,kun_egg_checkid="27922|27923|27924|27925|27926",pet_id=10104,item_id=27799,chibang_id=37202,first_id=10200,first_render_type=1,ui_pos1="-287|1|0",ui_rotation1="0|1|0",ui_scale1=1,second_id=10400,second_render_type=1,ui_pos2="-287|1|0",ui_rotation2="0|1|0",ui_scale2=1,third_id=30721,third_render_type=1,ui_pos3="-287|-52|0",ui_rotation3="0|2|0",ui_scale3=1,index=0,pet_zhanli=10000,ui_rotation="0|160|0",ui_pos="0|-2|0",ui_scale="210|210|210",miji_seq=1,miji_fameng_id="29024|29027|29030|29033|29036|29039",progress_max=3720,hecheng_seq=66,dasdal_reward_item={[0]=item_table[70],[1]=item_table[71],[2]=item_table[72],[3]=item_table[73]},}

}

