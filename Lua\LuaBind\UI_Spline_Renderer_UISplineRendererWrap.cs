﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UI_Spline_Renderer_UISplineRendererWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UI_Spline_Renderer.UISplineRenderer), typeof(UnityEngine.UI.MaskableGraphic));
		<PERSON><PERSON>RegFunction("SetVerticesDirty", SetVerticesDirty);
		<PERSON><PERSON>RegFunction("SetMaterialDirty", SetMaterialDirty);
		<PERSON><PERSON>RegFunction("Raycast", Raycast);
		<PERSON><PERSON>RegFunction("CrossFadeAlpha", CrossFadeAlpha);
		<PERSON><PERSON>RegFunction("CrossFadeColor", CrossFadeColor);
		<PERSON><PERSON>unction("UpdateTrueShadowCustomHash", UpdateTrueShadowCustomHash);
		<PERSON><PERSON>RegFunction("GetColorAt", GetColorAt);
		<PERSON><PERSON>RegFunction("GetWidthAt", GetWidthAt);
		<PERSON><PERSON>RegFunction("SetWidthCurve", SetWidthCurve);
		<PERSON><PERSON>unction("ChangeWidthCurveKey", ChangeWidthCurveKey);
		<PERSON><PERSON>unction("SetColorGradient", SetColorGradient);
		L.RegFunction("ChangeColorGradientAlphaKey", ChangeColorGradientAlphaKey);
		L.RegFunction("ForceUpdate", ForceUpdate);
		L.RegFunction("ReorientKnots", ReorientKnots);
		L.RegFunction("Create", Create);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("splineContainer", get_splineContainer, set_splineContainer);
		L.RegVar("startImages", get_startImages, set_startImages);
		L.RegVar("endImages", get_endImages, set_endImages);
		L.RegVar("lineTexturePreset", get_lineTexturePreset, set_lineTexturePreset);
		L.RegVar("startImagePreset", get_startImagePreset, set_startImagePreset);
		L.RegVar("endImagePreset", get_endImagePreset, set_endImagePreset);
		L.RegVar("color", get_color, set_color);
		L.RegVar("recursiveColor", get_recursiveColor, set_recursiveColor);
		L.RegVar("resolution", get_resolution, set_resolution);
		L.RegVar("mainTexture", get_mainTexture, null);
		L.RegVar("texture", get_texture, set_texture);
		L.RegVar("startImageSprite", get_startImageSprite, set_startImageSprite);
		L.RegVar("endImageSprite", get_endImageSprite, set_endImageSprite);
		L.RegVar("vertexCount", get_vertexCount, null);
		L.RegVar("startImageSize", get_startImageSize, set_startImageSize);
		L.RegVar("startImageOffsetMode", get_startImageOffsetMode, set_startImageOffsetMode);
		L.RegVar("startImageOffset", get_startImageOffset, set_startImageOffset);
		L.RegVar("normalizedStartImageOffset", get_normalizedStartImageOffset, set_normalizedStartImageOffset);
		L.RegVar("endImageSize", get_endImageSize, set_endImageSize);
		L.RegVar("endImageOffsetMode", get_endImageOffsetMode, set_endImageOffsetMode);
		L.RegVar("endImageOffset", get_endImageOffset, set_endImageOffset);
		L.RegVar("normalizedEndImageOffset", get_normalizedEndImageOffset, set_normalizedEndImageOffset);
		L.RegVar("recursiveMaterial", get_recursiveMaterial, set_recursiveMaterial);
		L.RegVar("material", get_material, set_material);
		L.RegVar("keepZeroZ", get_keepZeroZ, set_keepZeroZ);
		L.RegVar("keepBillboard", get_keepBillboard, set_keepBillboard);
		L.RegVar("width", get_width, set_width);
		L.RegVar("uvMode", get_uvMode, set_uvMode);
		L.RegVar("uvMultiplier", get_uvMultiplier, set_uvMultiplier);
		L.RegVar("uvOffset", get_uvOffset, set_uvOffset);
		L.RegVar("clipRange", get_clipRange, set_clipRange);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetVerticesDirty(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			obj.SetVerticesDirty();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMaterialDirty(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			obj.SetMaterialDirty();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Raycast(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			UnityEngine.Camera arg1 = (UnityEngine.Camera)ToLua.CheckObject(L, 3, typeof(UnityEngine.Camera));
			bool o = obj.Raycast(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CrossFadeAlpha(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
			obj.CrossFadeAlpha(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CrossFadeColor(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 5)
			{
				UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
				UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				obj.CrossFadeColor(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
				UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				bool arg4 = LuaDLL.luaL_checkboolean(L, 6);
				obj.CrossFadeColor(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UI_Spline_Renderer.UISplineRenderer.CrossFadeColor");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateTrueShadowCustomHash(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			obj.UpdateTrueShadowCustomHash();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetColorAt(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Color o = obj.GetColorAt(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetWidthAt(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float o = obj.GetWidthAt(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetWidthCurve(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			UnityEngine.AnimationCurve arg0 = (UnityEngine.AnimationCurve)ToLua.CheckObject<UnityEngine.AnimationCurve>(L, 2);
			obj.SetWidthCurve(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeWidthCurveKey(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Keyframe arg1 = StackTraits<UnityEngine.Keyframe>.Check(L, 3);
			obj.ChangeWidthCurveKey(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetColorGradient(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			UnityEngine.Gradient arg0 = (UnityEngine.Gradient)ToLua.CheckObject<UnityEngine.Gradient>(L, 2);
			obj.SetColorGradient(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeColorGradientAlphaKey(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3 && TypeChecker.CheckTypes<UnityEngine.GradientAlphaKey>(L, 3))
			{
				UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.GradientAlphaKey arg1 = StackTraits<UnityEngine.GradientAlphaKey>.To(L, 3);
				obj.ChangeColorGradientAlphaKey(arg0, arg1);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes<UnityEngine.GradientColorKey>(L, 3))
			{
				UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.GradientColorKey arg1 = StackTraits<UnityEngine.GradientColorKey>.To(L, 3);
				obj.ChangeColorGradientAlphaKey(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UI_Spline_Renderer.UISplineRenderer.ChangeColorGradientAlphaKey");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceUpdate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			obj.ForceUpdate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReorientKnots(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)ToLua.CheckObject<UI_Spline_Renderer.UISplineRenderer>(L, 1);
			obj.ReorientKnots();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Create(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				System.Collections.Generic.IEnumerable<UnityEngine.Vector3> arg0 = (System.Collections.Generic.IEnumerable<UnityEngine.Vector3>)ToLua.CheckObject<System.Collections.Generic.IEnumerable<UnityEngine.Vector3>>(L, 1);
				UnityEngine.RectTransform arg1 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				UI_Spline_Renderer.UISplineRenderer o = UI_Spline_Renderer.UISplineRenderer.Create(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 3)
			{
				System.Collections.Generic.IEnumerable<UnityEngine.Vector3> arg0 = (System.Collections.Generic.IEnumerable<UnityEngine.Vector3>)ToLua.CheckObject<System.Collections.Generic.IEnumerable<UnityEngine.Vector3>>(L, 1);
				UnityEngine.RectTransform arg1 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UI_Spline_Renderer.UISplineRenderer o = UI_Spline_Renderer.UISplineRenderer.Create(arg0, arg1, arg2);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 4)
			{
				System.Collections.Generic.IEnumerable<UnityEngine.Vector3> arg0 = (System.Collections.Generic.IEnumerable<UnityEngine.Vector3>)ToLua.CheckObject<System.Collections.Generic.IEnumerable<UnityEngine.Vector3>>(L, 1);
				UnityEngine.RectTransform arg1 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UI_Spline_Renderer.LineTexturePreset arg3 = (UI_Spline_Renderer.LineTexturePreset)ToLua.CheckObject(L, 4, typeof(UI_Spline_Renderer.LineTexturePreset));
				UI_Spline_Renderer.UISplineRenderer o = UI_Spline_Renderer.UISplineRenderer.Create(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 5)
			{
				System.Collections.Generic.IEnumerable<UnityEngine.Vector3> arg0 = (System.Collections.Generic.IEnumerable<UnityEngine.Vector3>)ToLua.CheckObject<System.Collections.Generic.IEnumerable<UnityEngine.Vector3>>(L, 1);
				UnityEngine.RectTransform arg1 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UI_Spline_Renderer.LineTexturePreset arg3 = (UI_Spline_Renderer.LineTexturePreset)ToLua.CheckObject(L, 4, typeof(UI_Spline_Renderer.LineTexturePreset));
				UI_Spline_Renderer.StartEndImagePreset arg4 = (UI_Spline_Renderer.StartEndImagePreset)ToLua.CheckObject(L, 5, typeof(UI_Spline_Renderer.StartEndImagePreset));
				UI_Spline_Renderer.UISplineRenderer o = UI_Spline_Renderer.UISplineRenderer.Create(arg0, arg1, arg2, arg3, arg4);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UI_Spline_Renderer.LineTexturePreset, UI_Spline_Renderer.StartEndImagePreset, UI_Spline_Renderer.StartEndImagePreset>(L, 4))
			{
				System.Collections.Generic.IEnumerable<UnityEngine.Vector3> arg0 = (System.Collections.Generic.IEnumerable<UnityEngine.Vector3>)ToLua.CheckObject<System.Collections.Generic.IEnumerable<UnityEngine.Vector3>>(L, 1);
				UnityEngine.RectTransform arg1 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UI_Spline_Renderer.LineTexturePreset arg3 = (UI_Spline_Renderer.LineTexturePreset)ToLua.ToObject(L, 4);
				UI_Spline_Renderer.StartEndImagePreset arg4 = (UI_Spline_Renderer.StartEndImagePreset)ToLua.ToObject(L, 5);
				UI_Spline_Renderer.StartEndImagePreset arg5 = (UI_Spline_Renderer.StartEndImagePreset)ToLua.ToObject(L, 6);
				UI_Spline_Renderer.UISplineRenderer o = UI_Spline_Renderer.UISplineRenderer.Create(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 6 && TypeChecker.CheckTypes<UnityEngine.Texture, UnityEngine.Sprite, UnityEngine.Sprite>(L, 4))
			{
				System.Collections.Generic.IEnumerable<UnityEngine.Vector3> arg0 = (System.Collections.Generic.IEnumerable<UnityEngine.Vector3>)ToLua.CheckObject<System.Collections.Generic.IEnumerable<UnityEngine.Vector3>>(L, 1);
				UnityEngine.RectTransform arg1 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				bool arg2 = LuaDLL.luaL_checkboolean(L, 3);
				UnityEngine.Texture arg3 = (UnityEngine.Texture)ToLua.ToObject(L, 4);
				UnityEngine.Sprite arg4 = (UnityEngine.Sprite)ToLua.ToObject(L, 5);
				UnityEngine.Sprite arg5 = (UnityEngine.Sprite)ToLua.ToObject(L, 6);
				UI_Spline_Renderer.UISplineRenderer o = UI_Spline_Renderer.UISplineRenderer.Create(arg0, arg1, arg2, arg3, arg4, arg5);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UI_Spline_Renderer.UISplineRenderer.Create");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_splineContainer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Splines.SplineContainer ret = obj.splineContainer;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index splineContainer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startImages(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			System.Collections.Generic.List<UnityEngine.UI.Image> ret = obj.startImages;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImages on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endImages(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			System.Collections.Generic.List<UnityEngine.UI.Image> ret = obj.endImages;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImages on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lineTexturePreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.LineTexturePreset ret = obj.lineTexturePreset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineTexturePreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startImagePreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.StartEndImagePreset ret = obj.startImagePreset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImagePreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endImagePreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.StartEndImagePreset ret = obj.endImagePreset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImagePreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_color(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Color ret = obj.color;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index color on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_recursiveColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool ret = obj.recursiveColor;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index recursiveColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_resolution(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			int ret = obj.resolution;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index resolution on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mainTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Texture ret = obj.mainTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mainTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_texture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Texture ret = obj.texture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index texture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startImageSprite(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Sprite ret = obj.startImageSprite;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageSprite on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endImageSprite(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Sprite ret = obj.endImageSprite;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageSprite on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_vertexCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			int ret = obj.vertexCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index vertexCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startImageSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.startImageSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startImageOffsetMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.OffsetMode ret = obj.startImageOffsetMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageOffsetMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.startImageOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_normalizedStartImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.normalizedStartImageOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index normalizedStartImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endImageSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.endImageSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endImageOffsetMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.OffsetMode ret = obj.endImageOffsetMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageOffsetMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_endImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.endImageOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_normalizedEndImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.normalizedEndImageOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index normalizedEndImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_recursiveMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool ret = obj.recursiveMaterial;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index recursiveMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_material(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Material ret = obj.material;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index material on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_keepZeroZ(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool ret = obj.keepZeroZ;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index keepZeroZ on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_keepBillboard(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool ret = obj.keepBillboard;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index keepBillboard on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float ret = obj.width;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uvMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.UVMode ret = obj.uvMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uvMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Vector2 ret = obj.uvMultiplier;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_uvOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Vector2 ret = obj.uvOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_clipRange(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Vector2 ret = obj.clipRange;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clipRange on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_splineContainer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Splines.SplineContainer arg0 = (UnityEngine.Splines.SplineContainer)ToLua.CheckObject(L, 2, typeof(UnityEngine.Splines.SplineContainer));
			obj.splineContainer = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index splineContainer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startImages(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			System.Collections.Generic.List<UnityEngine.UI.Image> arg0 = (System.Collections.Generic.List<UnityEngine.UI.Image>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.UI.Image>));
			obj.startImages = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImages on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endImages(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			System.Collections.Generic.List<UnityEngine.UI.Image> arg0 = (System.Collections.Generic.List<UnityEngine.UI.Image>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.UI.Image>));
			obj.endImages = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImages on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lineTexturePreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.LineTexturePreset arg0 = (UI_Spline_Renderer.LineTexturePreset)ToLua.CheckObject(L, 2, typeof(UI_Spline_Renderer.LineTexturePreset));
			obj.lineTexturePreset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineTexturePreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startImagePreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.StartEndImagePreset arg0 = (UI_Spline_Renderer.StartEndImagePreset)ToLua.CheckObject(L, 2, typeof(UI_Spline_Renderer.StartEndImagePreset));
			obj.startImagePreset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImagePreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endImagePreset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.StartEndImagePreset arg0 = (UI_Spline_Renderer.StartEndImagePreset)ToLua.CheckObject(L, 2, typeof(UI_Spline_Renderer.StartEndImagePreset));
			obj.endImagePreset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImagePreset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_color(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.color = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index color on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_recursiveColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.recursiveColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index recursiveColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_resolution(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.resolution = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index resolution on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_texture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.texture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index texture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startImageSprite(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Sprite arg0 = (UnityEngine.Sprite)ToLua.CheckObject(L, 2, typeof(UnityEngine.Sprite));
			obj.startImageSprite = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageSprite on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endImageSprite(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Sprite arg0 = (UnityEngine.Sprite)ToLua.CheckObject(L, 2, typeof(UnityEngine.Sprite));
			obj.endImageSprite = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageSprite on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startImageSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.startImageSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startImageOffsetMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.OffsetMode arg0 = (UI_Spline_Renderer.OffsetMode)ToLua.CheckObject(L, 2, typeof(UI_Spline_Renderer.OffsetMode));
			obj.startImageOffsetMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageOffsetMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.startImageOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_normalizedStartImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.normalizedStartImageOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index normalizedStartImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endImageSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.endImageSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endImageOffsetMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.OffsetMode arg0 = (UI_Spline_Renderer.OffsetMode)ToLua.CheckObject(L, 2, typeof(UI_Spline_Renderer.OffsetMode));
			obj.endImageOffsetMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageOffsetMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_endImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.endImageOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index endImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_normalizedEndImageOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.normalizedEndImageOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index normalizedEndImageOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_recursiveMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.recursiveMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index recursiveMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_material(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.material = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index material on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_keepZeroZ(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.keepZeroZ = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index keepZeroZ on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_keepBillboard(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.keepBillboard = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index keepBillboard on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.width = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uvMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UI_Spline_Renderer.UVMode arg0 = (UI_Spline_Renderer.UVMode)ToLua.CheckObject(L, 2, typeof(UI_Spline_Renderer.UVMode));
			obj.uvMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uvMultiplier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.uvMultiplier = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvMultiplier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_uvOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.uvOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index uvOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_clipRange(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UI_Spline_Renderer.UISplineRenderer obj = (UI_Spline_Renderer.UISplineRenderer)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.clipRange = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clipRange on a nil value");
		}
	}
}

