VientianeTianyinData = VientianeTianyinData or BaseClass()
VientianeTianyinData.MAX_REWARD_COUNT = 6 -- 一轮奖励

function VientianeTianyinData:__init()
    if VientianeTianyinData.Instance then
		error("[VientianeTianyinData] Attempt to create singleton twice!")
		return
	end

    VientianeTianyinData.Instance = self
    self:InitConfig()
    self.reward_flag = {}
    self.every_day_flag = false
    self.show_tip = true

    RemindManager.Instance:Register(RemindName.RemindVientiane, BindTool.Bind(self.GetRemind, self))
end

function VientianeTianyinData:__delete()
    RemindManager.Instance:UnRegister(RemindName.RemindVientiane)
    VientianeTianyinData.Instance = nil
    self.reward_flag = nil
    self.every_day_flag = nil
end

function VientianeTianyinData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_wanxiang_tianyin_cfg_auto")
    self.cur_reward_cfg = ListToMapList(cfg.reward, "grade", "round")
    self.cur_model_cfg = ListToMap(cfg.display_model, "grade", "round")
    self.suit_act_map_cfg = ListToMap(cfg.activation, "grade", "round", "part")
    self.open_day_cfg = cfg.open_day
    self.other_cfg = cfg.other[1]
end

function VientianeTianyinData:SetAllVientianeInfo(protocol)
    self.score = protocol.score
    self.grade = protocol.grade
	self.round_num = protocol.round_num
	self.reward_flag = protocol.reward_flag
    self.every_day_flag = protocol.every_day_flag == 1
end

function VientianeTianyinData:GetCurScore()
    return self.score or 0
end

function VientianeTianyinData:GetShopIsBuyFlag()
	return self.every_day_flag
end

function VientianeTianyinData:GetAllRewardState()
	return self.reward_flag[self.round_num] or {}
end

function VientianeTianyinData:GetRemind()
    local is_buy_free = self:GetShopIsBuyFlag()
	if self:GetReceiveRemind() or not is_buy_free then
		return 1
	end

	return 0
end


function VientianeTianyinData:GetReceiveRemind()
    local remind = false
    local score = self:GetCurScore()
    local flag_num = self:GetAllRewardState()
    local cur_cfg = self:GetCurRoundRewardCfg()
    if not IsEmptyTable(cur_cfg) and not IsEmptyTable(flag_num) then
        for i = 1, #cur_cfg do
            if score >= cur_cfg[i].need_score then
                if flag_num[i - 1] == 0 then
                    remind = true
                end
            end
        end
    end

    return remind
end

function VientianeTianyinData:GetCurLocation()
    local score = self:GetCurScore()
    local cur_cfg = self:GetCurRoundRewardCfg()
    local location = -1
    if not IsEmptyTable(cur_cfg) then
        for i = 1, #cur_cfg do
            if score < cur_cfg[i].need_score then
                location = i
                break
            elseif score >= cur_cfg[#cur_cfg].need_score then
                location = #cur_cfg + 1
            end
        end
    end

    return location
end

function VientianeTianyinData:GetTipShowShopCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = {}
	for i, v in ipairs(self.open_day_cfg) do
		if server_day >= v.start_day and server_day <= v.end_day then
			cfg = v
		end
	end

	return cfg
end

function VientianeTianyinData:GetIsShowTip()
	local is_show = false
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    if self.show_tip then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN)
		if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
            if role_level >= self.other_cfg.open_level then
                is_show = true
            end
		end
	end

	return is_show
end

function VientianeTianyinData:SetIsShowTip(state)
	self.show_tip = state
end


function VientianeTianyinData:GetRewardStateBySeq(seq) -- 获取奖励状态
    if not IsEmptyTable(self.reward_flag) then
        local flag_num = self:GetAllRewardState()
        return flag_num and flag_num[seq] == 1
    end
end

function VientianeTianyinData:GetCurRoundRewardCfg()
    return (self.cur_reward_cfg[self.grade] or {})[self.round_num]
end

function VientianeTianyinData:GetCurRoundModelCfg()
    return (self.cur_model_cfg[self.grade] or {})[self.round_num]
end

function VientianeTianyinData:GetActivationPartList()
	return (self.suit_act_map_cfg[self.grade] or {})[self.round_num]
end

function VientianeTianyinData:GetOtherCfg()
	return self.other_cfg
end
