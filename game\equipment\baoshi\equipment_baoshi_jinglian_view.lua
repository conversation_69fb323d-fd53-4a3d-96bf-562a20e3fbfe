local ATTRLIST1_ONLY_ATTR = 2 -- 属性列表1独有的属性索引

function EquipmentView:InitBaoShiJingLianView()
	if not self.equip_bsjl_list then
		self.equip_bsjl_list = AsyncListView.New(EquipBaoShiJLItemRender, self.node_list["bsjl_list_view"])
		self.equip_bsjl_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectBSJLEquipItemHandler, self))
	end

	if not self.bsjl_slot_list then
		self.bsjl_slot_list = {}

		for i = 1, GameEnum.MAX_STONE_COUNT do
			self.bsjl_slot_list[i] = BSJLStoneItem.New(self.node_list["bsjl_part"]:FindObj("baoshi_" .. i))
			self.bsjl_slot_list[i]:SetIndex(i - 1)
		end
	end

	local node1 = self.node_list["bsjl_attr_list1"]
	local node2 = self.node_list["bsjl_attr_list2"]
	local cur_node
	self.bsjl_level_attr = BSJLAttrListItem.New(self.node_list["level_attr"])
	self.bsjl_level_attr:SetIsLevelAttr(true)

	self.bsjl_attr_list = {}

	for i = 1, GameEnum.EQUIP_YUPO_ATTR_NUM + 1 do
		cur_node = node1
		if(i == ATTRLIST1_ONLY_ATTR) then
			cur_node = node2
		end

		local cell = BSJLAttrListItem.New(cur_node:FindObj("Attr"..i))
		cell:SetIndex(i)
		self.bsjl_attr_list[i] = cell
	end

	if not self.bsjl_item_cell then
		self.bsjl_item_cell = ItemCell.New(self.node_list["BSJL_show_item"])
		self.bsjl_item_cell:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end

	if not self.bsjl_equip_body_list then
		self.bsjl_equip_body_list = AsyncListView.New(BSJLEquipBodyListCellRender, self.node_list.bsjl_equip_body_list)
		self.bsjl_equip_body_list:SetStartZeroIndex(false)
		self.bsjl_equip_body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBSJLEquipBodyHandler, self))
		self.bsjl_equip_body_list:SetEndScrolledCallBack(BindTool.Bind(self.BSJLEquipBodyListSetEndScrollCallBack, self))
	end

	self.bsjl_select_equip_idnex = -1
	self.bsjl_jump_equip_body_seq = -1
	self.bsjl_jump_equip_body_equip_data = {}
	self.bsjl_need_equip_body_tween = true

	XUI.AddClickEventListener(self.node_list["bsjl_btn"], BindTool.Bind(self.OnBtnBaoShiJLHandler, self))
	XUI.AddClickEventListener(self.node_list["bsjl_cost_item"], BindTool.Bind(self.OnClickBSJLCostItem, self))
	XUI.AddClickEventListener(self.node_list["btn_bsjl_equip_body"], BindTool.Bind(self.OnClickBSJLEquipBodyBtn, self))
end

function EquipmentView:BaoShiJingLianDeleteMe()
	if self.equip_bsjl_list then
		self.equip_bsjl_list:DeleteMe()
		self.equip_bsjl_list = nil
	end

	if self.bsjl_slot_list then
		for k,v in pairs(self.bsjl_slot_list) do
			v:DeleteMe()
		end
		self.bsjl_slot_list = nil
	end

	if self.bsjl_attr_list then
		for k,v in pairs(self.bsjl_attr_list) do
			v:DeleteMe()
		end
		self.bsjl_attr_list = nil
	end

	if self.bsjl_item_cell then
		self.bsjl_item_cell:DeleteMe()
		self.bsjl_item_cell = nil
	end

	if self.bsjl_level_attr then
		self.bsjl_level_attr:DeleteMe()
		self.bsjl_level_attr = nil
	end

	if self.bsjl_equip_body_list then
		self.bsjl_equip_body_list:DeleteMe()
		self.bsjl_equip_body_list = nil
	end

	self.bsjl_need_equip_body_tween = nil
end

function EquipmentView:ShowBaoShiJingLianView()
	self.bsjl_select_equip_body_seq = nil
end

function EquipmentView:FlushBaoShiJingLianView()
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZYPJLEquipBodyDataList()
	self.bsjl_equip_body_list:SetDataList(total_equip_body_data_list)
	self.bsjl_equip_body_list:JumpToIndex(self:GetBSJLSelectEquipBodySeq(total_equip_body_data_list))
end

function EquipmentView:BSJLChangeToTargetEquipBody(data)
	if IsEmptyTable(data) then
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZYPJLEquipBodyDataList()
	if IsEmptyTable(total_equip_body_data_list) then
		return
	end

	self.bsjl_jump_equip_body_seq = data.equip_body_seq
	self.bsjl_jump_equip_body_equip_data = data.selct_part_data

	for k, v in pairs(total_equip_body_data_list) do
		if v.seq == self.bsjl_jump_equip_body_seq then
			if self.bsjl_equip_body_list then
				self.bsjl_equip_body_list:JumpToIndex(k)
				self.bsjl_jump_equip_body_seq = -1
			end

			break
		end
	end
end

function EquipmentView:GetBSJLSelectEquipBodySeq(total_equip_body_data_list)
	if self.bsjl_select_equip_body_seq then
		return self.bsjl_select_equip_body_index
	end

	local default_seq = -1
	local default_index = -1
	if not IsEmptyTable(total_equip_body_data_list) then
		for i = #total_equip_body_data_list, 1, -1 do
			local data = total_equip_body_data_list[i]

			if EquipmentWGData.Instance:GetEquipBodyBSJLRemind(data.seq) > 0 then
				return i
			end

			if default_seq < 0 or data.seq > default_seq  then
				local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(data.seq)
				
				if is_unlock and is_wear_equip then
					default_seq = data.seq
					default_index = i
				end
			end
		end
	end

	return default_index
end

-- function EquipmentView:GetBSJLSelectEquipBodySeq(total_equip_body_data_list)
-- 	if self.bsjl_select_equip_body_seq then
-- 		if EquipmentWGData.Instance:GetEquipBodyBSJLRemind(self.bsjl_select_equip_body_seq) then
-- 			return self.bsjl_select_equip_body_index
-- 		end
-- 	end

-- 	if not IsEmptyTable(total_equip_body_data_list) then
-- 		for k, v in pairs(total_equip_body_data_list) do
-- 			if EquipmentWGData.Instance:GetEquipBodyBSJLRemind(v.seq) > 0 then
-- 				return k
-- 			end
-- 		end
-- 	end

-- 	return self.bsjl_select_equip_body_index or 1
-- end

function EquipmentView:OnSelectBSJLEquipBodyHandler(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	local bsjl_seq_change = self.bsjl_select_equip_body_seq ~= data.seq
	self.bsjl_select_equip_body_seq = data.seq
	self.bsjl_select_equip_body_index = item.index
	self.bsjl_select_equip_body_data = data
	
	self:FlushBaoShiJingLianEquipDataList(bsjl_seq_change)
end

function EquipmentView:FlushBaoShiJingLianEquipDataList(bsjl_seq_change)
	local bsjl_equip_list_data = EquipmentWGData.Instance:GetEquipBaoShiShowList(self.bsjl_select_equip_body_seq)
	self.equip_bsjl_list:SetDataList(bsjl_equip_list_data)

	if not IsEmptyTable(self.bsjl_jump_equip_body_equip_data) then
		local select_index = -1
		for k, v in pairs(bsjl_equip_list_data) do
			if v.index == self.bsjl_jump_equip_body_equip_data.index then
				select_index = k
				break
			end
		end

		if select_index >= 0 then
			self.equip_bsjl_list:JumpToIndex(select_index)
			return
		end
	end

	local equip_index = self:CalcBSJLDefaultIndex(bsjl_equip_list_data, bsjl_seq_change)
	self.equip_bsjl_list:JumpToIndex(equip_index or 1)
end

-- 跳最近红点
function EquipmentView:CalcBSJLDefaultIndex(equip_list, bsjl_seq_change)
	if not bsjl_seq_change and self.bsjl_select_equip_idnex > 0 then
		local equip_remind = EquipmentWGData.Instance:GetBSJLRemindByPart(self.bsjl_select_equip_part)

		if equip_remind then
			return self.bsjl_select_equip_idnex
		end
	end

	local default_index = -1
	for k, v in pairs(equip_list) do
		if default_index < 0 then
			default_index = k
		end

		local equip_remind = EquipmentWGData.Instance:GetBSJLRemindByPart(v.index)

		if equip_remind then
			return k
		end
	end

	if self.bsjl_select_equip_idnex >= 0 and not IsEmptyTable(equip_list[self.bsjl_select_equip_idnex]) then
		return self.bsjl_select_equip_idnex
	else
		return default_index
	end
end

function EquipmentView:OnSelectBSJLEquipItemHandler(item, cell_index, is_default, is_click)
	if nil == item or nil == item.data then
		return
	end

	if item.data.index ~= self.bsjl_select_equip_part then
		self.node_list["bsjl_result_effect"]:SetActive(false)

		if self.bsjl_slot_list then
			for k, v in pairs(self.bsjl_slot_list) do
				v:StopInalyTween()
			end
		end
	end

	self.bsjl_select_equip_data = item.data
	self.bsjl_select_equip_part = item.data.index
	self.bsjl_select_equip_idnex = item.index

	self:FlushBSJLInfoPanel()
end

function EquipmentView:FlushBSJLInfoPanel()
	-- 物品格子
	if self.bsjl_select_equip_data then
		self.bsjl_item_cell:SetData(self.bsjl_select_equip_data)
	end

	-- 宝石属性
	local stone_info = EquipmentWGData.Instance:GetStoneInfoListByIndex(self.bsjl_select_equip_part)

	for k, v in pairs(self.bsjl_slot_list) do
		v:SetCurEquipPart(self.bsjl_select_equip_part)
		v:SetData(stone_info[k - 1])
	end

	-- 属性信息
	local cur_part_all_attr = EquipmentWGData.Instance:GetAllBSAttrByPart(self.bsjl_select_equip_part, true)
	local cur_JL_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(self.bsjl_select_equip_part)
	local next_refine_cfg = EquipmentWGData.Instance:GetStoneRefineByLevel(cur_JL_level + 1)
	local cur_remind = EquipmentWGData.Instance:GetBSJLRemindByPart(self.bsjl_select_equip_part)
	local bs_info = EquipmentWGData.Instance:GetStoneInfoListByIndex(self.bsjl_select_equip_part)
	local max_refine_level = EquipBodyWGData.Instance:GetRefineLevelLimit(self.bsjl_select_equip_body_seq)
	local is_max_refine_level = max_refine_level <= cur_JL_level

	-- 精炼等级变化/ 镶嵌变化
	local level_change = false
	local attr_info_cahnge = false

	if nil ~= self.bsjl_select_equip_part_cache and nil ~= self.bsjl_select_equip_part_level_cache and nil ~= self.bsjl_select_equip_bs_info_cache then
		if self.bsjl_select_equip_part_cache == self.bsjl_select_equip_part then
			if (cur_JL_level - self.bsjl_select_equip_part_level_cache) == 1 then
				level_change = true
				attr_info_cahnge = true
			end

			for i = 0, GameEnum.MAX_STONE_COUNT - 1 do
				local new_item_id = (bs_info[i] or{}).item_id or 0
				local item_id_cache = (self.bsjl_select_equip_bs_info_cache[i] or{}).item_id or 0
				if new_item_id ~= item_id_cache then
					attr_info_cahnge = true
					break
				end
			end
		end
	end

	for i = 1, #self.bsjl_attr_list do
		self.bsjl_attr_list[i]:SetCurLevel(cur_JL_level)
		self.bsjl_attr_list[i]:SetData(cur_part_all_attr[i])
		self.bsjl_attr_list[i]:SetMaxLevel(max_refine_level)

		if level_change or attr_info_cahnge then
			self.bsjl_attr_list[i]:PlayAttrValueUpEffect()
		end
	end

	self.bsjl_level_attr:SetData({level = cur_JL_level, is_max = is_max_refine_level or next_refine_cfg == nil})

	if level_change then
		self.bsjl_level_attr:PlayAttrValueUpEffect()
	end

	self.bsjl_select_equip_part_cache = self.bsjl_select_equip_part
	self.bsjl_select_equip_part_level_cache = cur_JL_level
	self.bsjl_select_equip_bs_info_cache = bs_info

	local cost_itemid = EquipmentWGData.Instance:GetBSJLCostItemIdByPart(self.bsjl_select_equip_part)

	if not is_max_refine_level and next_refine_cfg then
		local success_pct = next_refine_cfg.success_pct / 100
		local pct_str = string.format(Language.Equipment.BSJLSucceedPerDesc, success_pct)
		local yunshi_addition_cfg = QifuYunShiWGData.Instance:GetHasYunShiAdditionIdCfg(QifuYunShiWGData.ADDITION_TYPE.BAOSHI_TYPE)
		-- local yunshi_addition_value = 0
		-- if yunshi_addition_cfg then
		-- 	yunshi_addition_value = yunshi_addition_cfg.addition_value / 100
		-- end

		-- local str = string.format(Language.Equipment.AdditionRateStr, yunshi_addition_value)
		-- self.node_list["bsjl_succ_rate"].text.text = pct_str .. str
		self.node_list["bsjl_succ_rate"].text.text = pct_str

		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_itemid)
		local need_num = next_refine_cfg.stuff_num
		local item_num_color = have_num >= need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		str = string.format("<color=%s>%s/%s</color>", item_num_color, have_num, need_num)

		local bundle, asset = ResPath.GetItem(cost_itemid)
		self.node_list.bsjl_cost_item.image:LoadSpriteAsync(bundle, asset, function ()
			self.node_list.bsjl_cost_item.image:SetNativeSize()
		end)

		self.node_list["bsjl_cost_num"].text.text = str
		self.node_list["bsjl_max"]:SetActive(false)
		self.node_list["bsjl_nomax"]:SetActive(true)
		self.node_list["bsjl_btn"]:SetActive(true)
		self.node_list["bsjl_cost"]:SetActive(true)
	else
		self.node_list["bsjl_max"]:SetActive(true)
		self.node_list["bsjl_nomax"]:SetActive(false)
		self.node_list["bsjl_btn"]:SetActive(false)
		self.node_list["bsjl_cost"]:SetActive(false)
	end

	self.node_list["bsjl_btn_remind"]:SetActive(cur_remind)
	self.node_list["bsjl_btn_text"].text.text = Language.Equipment.BSJLBtnDefultText
end

function EquipmentView:OnBtnBaoShiJLHandler()
	if self.bsjl_select_equip_data == nil then
		return
	end

	--判断是否有镶嵌宝石
	local is_inlay = EquipmentWGData.Instance:GetBSJLIsInlayStoneByPart(self.bsjl_select_equip_part)
	if not is_inlay then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Equipment.NoStoneInlayToRefine)
		return
	end

	--判断是否有材料精炼
	local cur_JL_level = EquipmentWGData.Instance:GetStoneRefineLevelByPart(self.bsjl_select_equip_part)
	local next_refine_cfg = EquipmentWGData.Instance:GetStoneRefineByLevel(cur_JL_level + 1)
	local cost_itemid = EquipmentWGData.Instance:GetBSJLCostItemIdByPart(self.bsjl_select_equip_part)

	if next_refine_cfg then
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_itemid)
		local need_num = next_refine_cfg.stuff_num
		if have_num < need_num then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cost_itemid})
			return
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Equipment.BSJLKeepLevelDesc)
		return
	end

	EquipmentWGCtrl.Instance:SendStoneOperate(STONE_OPERA_TYPE.STONE_REFINE_UPLEVEL, self.bsjl_select_equip_part)
end

function EquipmentView:OnClickBSJLCostItem()
	local cost_itemid = EquipmentWGData.Instance:GetBSJLCostItemIdByPart(self.bsjl_select_equip_part)
	TipWGCtrl.Instance:OpenItem({item_id = cost_itemid})
end

function EquipmentView:OpenBSJLShowEquipTips()
	if self.bsjl_select_equip_data then
		TipWGCtrl.Instance:OpenItem(self.bsjl_select_equip_data, ItemTip.FROM_EQUIPMENT)
	end
end

function EquipmentView:ShowBaoshiJLEffect(result)
	result = result or 1

	local effect_type = result == 1 and UIEffectName.s_jinglian or UIEffectName.f_jinglian
	self.node_list["bsjl_result_effect"]:SetActive(true)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
						is_success = result == 1, pos = Vector2(0, 0), parent_node = self.node_list["bsjl_result_effect"]})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

function EquipmentView:OnClickBSJLEquipBodyBtn()
	EquipmentWGCtrl.Instance:OpenBSJLOverviewView()
end

function EquipmentView:BSJLEquipBodyListSetEndScrollCallBack()
	if self.bsjl_need_equip_body_tween then
		self.bsjl_need_equip_body_tween = false

		local tween_info = UITween_CONSTS.EquipBody

		local cell_list = self.bsjl_equip_body_list:GetAllItems()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end
end

-------------------------------------EquipBaoShiJLItemRender---------------------------------
EquipBaoShiJLItemRender = EquipBaoShiJLItemRender or BaseClass(EquipBaoShiItemRender)
function EquipBaoShiJLItemRender:OnFlush()
	self.is_jinlian_item = true
	if not self.data then
		return
	end

	EquipBaoShiItemRender.OnFlush(self)
end

----------------------------------------BSJLAttrListItem---------------------------------------
BSJLAttrListItem = BSJLAttrListItem or BaseClass(CommonAddAttrRender)
function BSJLAttrListItem:__init()
	self.is_level_attr = false
	self.cur_level = 0
	self.max_level = 9999
end

function BSJLAttrListItem:SetIsLevelAttr(bool)
	self.is_level_attr = bool
end

function BSJLAttrListItem:SetCurLevel(level)
	self.cur_level = level or 0
end

function BSJLAttrListItem:SetMaxLevel(level)
	self.max_level = level or 0
end

function BSJLAttrListItem:OnFlush()
	if IsEmptyTable(self.data) and self.index <= GameEnum.EQUIP_YUPO_ATTR_NUM then
		self.view:SetActive(false)
		return
	end

	if self.is_level_attr then
		self.node_list["cur_value"].text.text = string.format(Language.Equip.StrengthLv2, self.data.level)
		self.node_list["next_value"].text.text = not self.data.is_max and string.format(Language.Equip.StrengthLv2, self.data.level + 1) or Language.Equip.StrengthMaxLv
		return
	end

	local cur_refine_cfg = EquipmentWGData.Instance:GetStoneRefineByLevel(self.cur_level)
	local next_refine_cfg = EquipmentWGData.Instance:GetStoneRefineByLevel(self.cur_level + 1)
	local cur_up_per = cur_refine_cfg and cur_refine_cfg.add_attribute_pct / 10000 or 0
	local next_up_per = next_refine_cfg and next_refine_cfg.add_attribute_pct / 10000 or 0
	local no_max = self.max_level > self.cur_level and next_refine_cfg ~= nil

	if self.index <= GameEnum.EQUIP_YUPO_ATTR_NUM then
		local value = self.data.value
		local name_str = EquipmentWGData.Instance:GetAttrNameByAttrId(self.data.attr_id, true)
		local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_id)
		value = is_per and value / 100 or value
		local per_desc = is_per and "%" or ""

		local cur_value = math.ceil(value * (1 + cur_up_per))
		local next_value =  math.ceil(value * (next_up_per - cur_up_per))
		self.node_list["attr_name"].text.text = name_str
		self.node_list["cur_value"].text.text = cur_value .. per_desc
		self.node_list["next_value"].text.text = next_value .. per_desc
		self.view:SetActive(not is_per and (cur_value > 0 or next_value > 0))
	else
		self.node_list["attr_name"].text.text = Language.Equipment.BSJLAttrTotalDesc
		self.node_list["cur_value"].text.text = cur_up_per * 100 .. "%"
		self.node_list["next_value"].text.text = next_up_per * 100 .. "%"
		self.view:SetActive(cur_up_per > 0 or next_up_per > 0)
	end
	self.node_list["arrow"]:SetActive(no_max)
	self.node_list["next_value"]:SetActive(no_max)
end

-------------------------------------------BSJLEquipBodyListCellRender-------------------------------------------
BSJLEquipBodyListCellRender = BSJLEquipBodyListCellRender or BaseClass(BaseRender)

function BSJLEquipBodyListCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickTipBtn, self))
end

function BSJLEquipBodyListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
	self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
		self.node_list.icon_hl.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name

	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not is_unlocak and not is_wear_equip)
	self.node_list.no_equip_tip:CustomSetActive(is_unlocak and not is_wear_equip)
	self.node_list.btn_tip:CustomSetActive(not can_duanzao)

	local remind = EquipmentWGData.Instance:GetEquipBodyBSJLRemind(self.data.seq) > 0
	self.node_list.remind:CustomSetActive(remind)
end

function BSJLEquipBodyListCellRender:OnSelectChange(is_select)
	local is_special = self.data.type == 1
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special)
end

function BSJLEquipBodyListCellRender:OnClickTipBtn()
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)

	if not can_duanzao then
		if not is_unlocak then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLockCanDuanZao)
		elseif not is_wear_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquipCanDuanZao)
		end
	end
end

function BSJLEquipBodyListCellRender:PlayItemTween()
	UITween.FakeHideShow(self.node_list.root)
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
	self.cell_delay_key = "BSJLEquipBodyItemCellRender" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.root then
			UITween.FakeToShow(self.node_list.root)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end