--3V3倒计时
KF3V3StartDown = KF3V3StartDown or BaseClass(SafeBaseView)
function KF3V3StartDown:__init()
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_3v3_startdown")
    self.active_close = false
    self.view_layer = UiLayer.Normal
end

function KF3V3StartDown:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("kf_3v3_startdown") then
		CountDownManager.Instance:RemoveCountDown("kf_3v3_startdown")
	end
end

function KF3V3StartDown:ShowIndexCallBack()
    self.node_list.start_fight:SetActive(false)
    self.node_list.time_text:SetActive(true)
    if CountDownManager.Instance:HasCountDown("kf_3v3_startdown") then
		CountDownManager.Instance:RemoveCountDown("kf_3v3_startdown")
	end
    local total_time = math.floor(self.time_stamp - TimeWGCtrl.Instance:GetServerTime())
    self.cur_total_count = total_time + 1
    self:UpdateTime(0, total_time)
    CountDownManager.Instance:AddCountDown("kf_3v3_startdown", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil,  total_time, 1)
end

function KF3V3StartDown:DoStartDown(time_stamp, finish_callback)
    self.time_stamp = time_stamp
    self.finish_callback = finish_callback
	self:Open()
end

function KF3V3StartDown:UpdateTime(elapse_time, total_time)
    local temp_seconds = math.floor(total_time - elapse_time)
    self.node_list.time_text.text.text = temp_seconds
    self:DoTweenScaleContentFightStart(self.node_list.time_text)
    if temp_seconds <= 1 and temp_seconds > 0 then

    end
end

function KF3V3StartDown:CompleteTime(elapse_time, total_time)
    self.node_list.start_fight:SetActive(true)
    self:DoTweenScaleContentFightStart(self.node_list.start_fight)
    self.node_list.time_text:SetActive(false)

    if self.finish_callback then
		self.finish_callback()
		self.finish_callback = nil
	end
    GlobalTimerQuest:AddDelayTimer(function()
        self:Close()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end, 1)
end

function KF3V3StartDown:CloseCallBack()
end

function KF3V3StartDown:DoTweenScaleContentFightStart(node)
    if not node then return end
	local scale = Vector3(1,1,1)
	node.rect.localScale = Vector3(3,3,1)
	node.rect:DOScale(scale,0.3)
	EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
end