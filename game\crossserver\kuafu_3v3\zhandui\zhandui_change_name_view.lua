ZhanDuiChangeNameView = ZhanDuiChangeNameView or BaseClass(SafeBaseView)
function ZhanDuiChangeNameView:__init()
    self:SetMaskBg()
    --self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_common_panel", {sizeDelta = Vector2(530, 300)})
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_change_name")
end

function ZhanDuiChangeNameView:ReleaseCallBack()
    if self.invite_list then
        self.invite_list:DeleteMe()
        self.invite_list = nil
    end
    --[[if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end]]
    self.last_edit_time = nil
end

function ZhanDuiChangeNameView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.ZhanDui.ViewNameChangeName
	--self:SetSecondView(nil, self.node_list["size"])
    XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.OnClickChange, self))
    self.node_list.change_name_input_field.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnEditValueChange, self))
    --self.cell = ItemCell.New(self.node_list.cell_pos)
end

function ZhanDuiChangeNameView:ShowIndexCallBack()
    self:Flush()
end

function ZhanDuiChangeNameView:OnFlush()
    --[[local item_list = KuafuPVPWGData.GetModifyNameItem()
    if item_list[0] ~= nil then
        self.cell:SetFlushCallBack(function()
            local has_num = ItemWGData.Instance:GetItemNumInBagById(item_list[0].item_id)
            local need_num = item_list[0].num
            local color = has_num >= need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
            self.cell:SetRightBottomColorText(ToColorStr(has_num .. "/" .. need_num, color))
            self.cell:SetRightBottomTextVisible(true)
        end)
        self.cell:SetData(item_list[0])
        self.node_list.hide:SetActive(true)
        self.node_list.comsume_text:SetActive(false)
        local item_config = ItemWGData.Instance:GetItemConfig(item_list[0].item_id)
        self.node_list.item_name.text.text = ToColorStr(item_config.name, ITEM_COLOR[item_config.color])
    else
        self.node_list.hide:SetActive(false)
        self.node_list.comsume_text:SetActive(true)
        local str = ""
        local money_type, money = KuafuPVPWGData.GetModifyNameComsume()
        if money_type == Shop_Money_Type.Type1 then
            str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType1)
        elseif money_type == Shop_Money_Type.Type2 then
            str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType2)
        elseif money_type == Shop_Money_Type.Type4 then
            str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType4)
        elseif money_type == Shop_Money_Type.Type5 then
            str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType5)
        end
        self.node_list.comsume_text.text.text = str
    end]]

    self.node_list.comsume_text:SetActive(true)
    local str = ""
    local money_type, money = KuafuPVPWGData.GetModifyNameComsume()
    if money_type == Shop_Money_Type.Type1 then
        str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType1)
    elseif money_type == Shop_Money_Type.Type2 then
        str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType2)
    elseif money_type == Shop_Money_Type.Type4 then
        str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType4)
    elseif money_type == Shop_Money_Type.Type5 then
        str = string.format(Language.Shop.MoneyComsume, money, Language.Shop.MoneyType5)
    end
    self.node_list.comsume_text.text.text = str
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo() --战队名字
    if zhan_dui_info ~= nil then
        self.node_list.cur_name.text.text = zhan_dui_info.name
    end
    
end

function ZhanDuiChangeNameView:OnEditValueChange(str)
    local len, table = CheckStringLen(str, COMMON_CONSTS.MAX_ZHAN_DUI_NAME_LEN)
    if not len then
        if table then
            local str = ""
            for i = 1, #table do
                str = str .. table[i]
            end
            self.node_list.change_name_input_field.input_field.text = str
        end
        if self.last_edit_time and self.last_edit_time > Status.NowTime then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.CreateMaxFontCount)
        self.last_edit_time = Status.NowTime + 0.5
    end
end

function ZhanDuiChangeNameView:OnClickChange()
    local str = self.node_list.change_name_input_field.input_field.text
    local len, table = CheckStringLen(str, COMMON_CONSTS.MAX_ZHAN_DUI_NAME_LEN)
    if not len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.ChangeNoticeMaxFontCount)
        return
    end
    if ChatFilter.Instance:IsIllegal(str, true) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end
    ZhanDuiWGCtrl.Instance:ModifyZhanDuiName(str)
end
