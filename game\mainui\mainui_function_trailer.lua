MainUIFunctiontrailer = MainUIFunctiontrailer or BaseClass(BaseRender)

local FunTrailerType =
{
	Icon = 0,
	Model = 1,
}

function MainUIFunctiontrailer:__init()
	self.node_list["BtnModle"].button:AddClickListener(BindTool.Bind(self.TrailerModelClick, self))
	self.node_list["FunctionTrailer"]:SetActive(false)
	-- self.old_asset = ""
	-- self.save_model_root = {}
	-- self.can_get_img_tween = nil
end

function MainUIFunctiontrailer:__delete()
	-- self.old_asset = ""
	-- if self.can_get_img_tween then
	-- 	self.can_get_img_tween:Kill()
	-- 	self.can_get_img_tween = nil
	-- end
	-- self:ClearMainRoleModel()
end

function MainUIFunctiontrailer:LoadCallBack()
	self:FlushView()
end

-- function MainUIFunctiontrailer:ShowTrailerIcon(trailer_cfg)
-- 	local bundle, asset = nil, nil
-- 	local icon_t = Split(trailer_cfg.icon_fp_view, "##")
-- 	local is_raw = trailer_cfg.fp_is_rawimage == 1
-- 	if icon_t[2] then
-- 		bundle, asset = icon_t[2], icon_t[1]
-- 	else
-- 		local path = is_raw and ResPath.GetF2RawImagesPNG or ResPath.GetF2FunTrailerImages
-- 		bundle, asset = path(trailer_cfg.icon_fp_view)
-- 	end

-- 	local ui_scale = self:GetModelChangeInfo(trailer_cfg)
-- 	ui_scale = ui_scale or Vector3(1,1,1)
-- 	if bundle and asset then
-- 		self.node_list['btn_icon']:SetActive(not is_raw)
-- 		self.node_list['big_btn_icon']:SetActive(is_raw)

-- 		local node = is_raw and self.node_list['big_btn_icon'] or self.node_list['btn_icon']
-- 		local node_image = is_raw and self.node_list['big_btn_icon'].raw_image or self.node_list['btn_icon'].image
-- 		node_image:LoadSprite(bundle, asset, function()
-- 			node_image:SetNativeSize()
-- 			node.transform.localScale = ui_scale
-- 		end)
-- 	end
-- 	-- self:ClearMainRoleModel()
-- end

function MainUIFunctiontrailer:IsNotMarryScene()
    return Scene.Instance:GetSceneType() ~= SceneType.HunYanFb
end

function MainUIFunctiontrailer:FlushView()
	if IS_AUDIT_VERSION then
		self.node_list["FunctionTrailer"]:SetActive(false)
		return
	end

	local trailer_cfg, is_can_lq = OpenFunWGData.Instance:GetFirstCanLingQu()

	if trailer_cfg then
		local is_show, is_open, is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(trailer_cfg.id)
		self.can_reward = (is_open and not is_get)
	elseif self.trailer_cfg then
		local _,is_open,is_get = OpenFunWGData.Instance:GetNoticeIsCanShow(self.trailer_cfg.id)
		self.can_reward = (is_open and not is_get)
	end

	if trailer_cfg and self:IsNotMarryScene() and not IS_AUDIT_VERSION then
		self.trailer_cfg = trailer_cfg
		self.node_list["FunctionTrailer"]:SetActive(true)

		local role_level = RoleWGData.Instance:GetAttr("level")
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local kf_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
		if role_level < trailer_cfg.open_level then
			self.node_list["desc_fun_open"].text.text = trailer_cfg.open_dec
		elseif kf_world_level < trailer_cfg.open_cross_world_level then
			self.node_list["desc_fun_open"].text.text = trailer_cfg.open_world_level
		elseif open_day < trailer_cfg.open_server_day then
			self.node_list["desc_fun_open"].text.text = trailer_cfg.open_day_dec
		else
			self.node_list["desc_fun_open"].text.text = ""
		end

		-- self.node_list["can_get_effect"]:SetActive(is_can_lq)
		-- self:PlayCanGetImgTween(is_can_lq)

		self.node_list["flag_can_get"]:SetActive(is_can_lq)
		self.node_list["flag_can_get_remind_effect"]:SetActive(is_can_lq)
		self.node_list["flag_normal"]:SetActive(not is_can_lq)

		if self.can_reward then
			self.node_list["desc_fun_open"].text.text = Language.LingYuanLiBao.LingQu
		end

		self.node_list["fun_name_text"].text.text = trailer_cfg.fun_name or ""

		--判断显示模型还是图标
		-- self:ShowTrailerIcon(trailer_cfg)	--策划目前要求只显示图标,不知道后续会不会调整暂时屏蔽显示模型
	else
		self.node_list["FunctionTrailer"]:SetActive(false)
	end
end

function MainUIFunctiontrailer:GetModelChangeInfo(trailer_cfg)
	local ui_rotation = nil
	local ui_pos = nil
	local ui_scale = nil

	if nil ~= trailer_cfg.pos and "" ~= trailer_cfg.pos then
		local position = Split(trailer_cfg.pos,"|")
		ui_pos = Vector2(tonumber(position[1]), tonumber(position[2]))
	end

	if nil ~= trailer_cfg.rota and "" ~= trailer_cfg.rota then
		local rotation = Split(trailer_cfg.rota,"|")
		ui_rotation = Quaternion.Euler(tonumber(rotation[1]), tonumber(rotation[2]), tonumber(rotation[3]))
	end

	if nil ~= trailer_cfg.scale and "" ~= trailer_cfg.scale then
		ui_scale = Vector3(trailer_cfg.scale, trailer_cfg.scale, trailer_cfg.scale)
	end

	return ui_scale, ui_pos, ui_rotation
end

-- function MainUIFunctiontrailer:ShowMarryModel( res_show, ui_pos, ui_rotation, ui_scale )
-- 	local prof = RoleWGData.Instance.role_vo.prof
-- 	local bundle_l, asset_l = ResPath.GetRoleModel(ResPath.GetFashionModelId(prof, res_show))

-- 	if self.old_asset ~= asset_l then
-- 		self.model_view:SetMainAsset(bundle_l, asset_l)
-- 		self.old_asset = asset_l
-- 	end
-- end


-- function MainUIFunctiontrailer:ClearMainRoleModel()
-- 	if self.model_view then
-- 		self.model_view:DeleteMe()
-- 		self.model_view = nil
-- 	end
-- end

--模型点击
function MainUIFunctiontrailer:TrailerModelClick()
	TipWGCtrl.Instance:OpenFunTrailerTip()
end
--图片点击
function MainUIFunctiontrailer:TrailerIconClick()
	if self.trailer_cfg and self.trailer_cfg.is_model == FunTrailerType.Icon then
		TipWGCtrl.Instance:OpenFunTrailerTip()
	end
end

--设置展示的角色信息
-- function MainUIFunctiontrailer:SetShowRoleInfo(vo)
-- 	local data = {}
-- 	data.appearance = {}
-- 	data.prof = vo.prof
-- 	data.sex = vo.sex
-- 	return data
-- end

-- function MainUIFunctiontrailer:PlayCanGetImgTween(is_can_lq)
-- 	self.node_list["ani_lq"]:SetActive(is_can_lq)
-- 	if is_can_lq then
-- 		if not self.can_get_img_tween then
-- 			local tween_root = self.node_list["ani_lq"].rect
-- 			self.can_get_img_tween = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 10, 0.8)
-- 			self.can_get_img_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
-- 		else
-- 			self.can_get_img_tween:Restart()
-- 		end
-- 	elseif self.can_get_img_tween then
-- 		self.can_get_img_tween:Pause()
-- 	end
-- end