local develop_mode = UnityEngine.PlayerPrefs.GetInt("a3_develop_mode")

local DevelopMode = {}
local check_list = {}

function DevelopMode:Init()
	check_list = {
		require("editor.check_deleteme"),
		require("editor.check_listen"),
		require("editor.check_reference"),
		require("editor.check_bug"),
		require("editor.check_resmanager"),
		require("editor.check_mem"),
		require("editor.check_function_use_time"),
		require("editor.check_function_use_mem"),
		require("editor.check_obsolete"),
		require("editor.check_protocol"),
		require("editor.check_lua_call"),
		require("editor.check_class_count"),
	}

	self:RefreshDelopMode()
end

function DevelopMode:UnLoadAllcheck()
	for _, v in ipairs(check_list) do
		_G.package.loaded[v] = nil
	end

	check_list = {}
end

function DevelopMode:IsDeveloper()
	return 1 == develop_mode
end

function DevelopMode:RefreshDelopMode()
	G_IsDeveloper = self:IsDeveloper()
end

function DevelopMode:Update(now_time, elapse_time)
	if not self:IsDeveloper() then
		if GLOBAL_DELETE_ME_CHECK_SWITCH then
			check_list[1]:Update(now_time, elapse_time)
			check_list[6]:Update(now_time, elapse_time)
		end
		return
	end

	for _, v in ipairs(check_list) do
		if nil ~= v.Update then
			v:Update(now_time, elapse_time)
		end
	end
end

function DevelopMode:OnOpenCallBack(view)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnOpenCallBack then
			v:OnOpenCallBack(view)
		end
	end
end

function DevelopMode:OnCloseView(view)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnCloseView then
			v:OnCloseView(view)
		end
	end
end

function DevelopMode:OnLoadCallBack(view)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnLoadCallBack then
			v:OnLoadCallBack(view)
		end
	end
end

function DevelopMode:OnReleaseView(view)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnReleaseView then
			v:OnReleaseView(view)
		end
	end
end

function DevelopMode:OnCreateClass(class_type)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnCreateClass then
			v:OnCreateClass(class_type)
		end
	end
end

function DevelopMode:OnCreateObj(obj, class_type)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnCreateObj then
			v:OnCreateObj(obj, class_type)
		end
	end
end

function DevelopMode:OnDeleteObj(obj)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnDeleteObj then
			v:OnDeleteObj(obj)
		end
	end
end

function DevelopMode:OnBindFun(obj, func, new_func)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnBindFun then
			v:OnBindFun(obj, func, new_func)
		end
	end
end

function DevelopMode:OnGameStop()
	for _, v in ipairs(check_list) do
		if nil ~= v.OnGameStop then
			v:OnGameStop()
		end
	end
end

function DevelopMode:OnSendMsg(msg_type)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnSendMsg then
			v:OnSendMsg(msg_type)
		end
	end
end

function DevelopMode:OnReceiveMsg(msg_type)
	for _, v in ipairs(check_list) do
		if nil ~= v.OnReceiveMsg then
			v:OnReceiveMsg(msg_type)
		end
	end
end

function DevelopMode:OutputLog()
	for _, v in ipairs(check_list) do
		if nil ~= v.OutputLog then
			v:OutputLog()
		end
	end
end

function DevelopMode:OnLuaCall(event, ...)
	for i=1,#check_list do
		if nil ~= check_list[i].OnLuaCall then
			check_list[i]:OnLuaCall(event, ...)
		end
	end
end

DevelopMode:Init()
return DevelopMode