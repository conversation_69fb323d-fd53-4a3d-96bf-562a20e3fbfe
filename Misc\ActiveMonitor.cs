﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

public class ActiveMonitor
{
    private struct LogItem
    {
        public bool isEnable;
        public string reason;
        public string trackback;
    }

    private static Dictionary<GameObject, List<LogItem>> monitorTargets = new Dictionary<GameObject, List<LogItem>>();

    public static void OnEnableObj(GameObject gameobj, string reason)
    {
        //#if UNITY_EDITOR
        //        Record(gameobj, true, reason);
        //#endif
    }

    public static void OnDisableObj(GameObject gameobj, string reason)
    {
        //#if UNITY_EDITOR
        //        Record(gameobj, false, reason);
        //#endif
    }

    private static void Record(GameObject gameobj, bool isEnable, string reason)
    {
        if (null == gameobj || null == GameRoot.Instance) return;

        List<LogItem> trackbacks = null;
        if (!monitorTargets.TryGetValue(gameobj, out trackbacks))
        {
            trackbacks = new List<LogItem>();
            monitorTargets.Add(gameobj, trackbacks);
        }

        LogItem item = new LogItem();
        item.isEnable = isEnable;
        item.reason = reason;
        string title = isEnable ? "Enable" : "Disable";
        item.trackback = string.Format("{0} {1}", title, GameRoot.Instance.GetDebugTrackback());
        trackbacks.Add(item);
    }

    public static void Statistics()
    {
        foreach (var kv in monitorTargets)
        {
            var list = kv.Value;
            if (null == kv.Key || list.Count < 2)
            {
                continue;
            }

            bool isError = false;
            var first = list[0];
            var last = list[list.Count - 1];

            if ((first.reason == "GameObjectAttach" && list.Count < 4)
                || (first.reason == "UI3DModel" && list.Count < 3)
                || (first.reason == "UIEffect" && list.Count < 3)
                || (first.reason == "UIOverrideOrder" && list.Count < 3))
            {
                continue;
            }

            // enable --> disable 形式
            if (first.isEnable && list.Count % 2 == 0 && !last.isEnable) isError = true;
            // disable --> enable --> disable 形式
            if (!first.isEnable && list.Count % 2 != 0 && !last.isEnable) isError = true;
            // enable->disable->enable形式
            if (first.isEnable && list.Count % 2 != 0 && last.isEnable) isError = true;

            if (isError)
            {
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < list.Count; i++)
                {
                    if (i >= 1) builder.Append("\n");
                    builder.Append(list[i].trackback);
                }

                string fullPath = UtilU3d.GetGameObjectFullPath(kv.Key);
                GameRoot.AddLuaWarning("【Lua性能问题】对象来回激活, 代码质量差导致严重性能问题，马上修改！！(查看控制台日志)");
                Debug.LogErrorFormat("[{0}]激活和隐藏之间来回切换，影响性能！！！代码质量差！！！马上处理 ！！！次数：{1} ，路径:{2} \n {3}", first.reason, kv.Value.Count, fullPath, builder.ToString());
            }
        }

        monitorTargets.Clear();
    }
}
