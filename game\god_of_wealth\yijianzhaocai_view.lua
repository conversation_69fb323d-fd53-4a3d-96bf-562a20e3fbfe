------------------------------------------------------------------------
--	招财进宝-一键招财
------------------------------------------------------------------------
Yi<PERSON><PERSON>ZhaoCaiView = YiJianZhaoCaiView or BaseClass(SafeBaseView)

function YiJianZhaoCaiView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/god_of_wealth_ui_prefab", "layout_yijian_zhaocai_view")
end

function Yi<PERSON><PERSON>ZhaoCaiView:LoadCallBack()
	if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(<PERSON><PERSON><PERSON><PERSON>, self.node_list.reward_item_list)
		self.reward_item_list:SetStartZeroIndex(true)
	end
	self.node_list.btn_close_windows.button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list.CancelBtn.button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list.OKBtn.button:AddClickListener(BindTool.Bind(self.YiJianZhaoCaiBtn, self))
end

function YiJianZhaoCaiView:ReleaseCallBack()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function YiJianZhaoCaiView:OnFlush()
	local rewaed_list_data = GodOfWealthWGData.Instance:GetAllBuyRewardList()
	if self.reward_item_list then
		self.reward_item_list:SetDataList(rewaed_list_data)
	end
	local desc = GodOfWealthWGData.Instance:GetAllBuyDesc()
	self.node_list.show_text.text.text = desc
end

-- 一键购买
function YiJianZhaoCaiView:YiJianZhaoCaiBtn()
	local current_rmb_bug_cfg = GodOfWealthWGData.Instance:GetCurrentRmbBuyCfg()
    local need_num, need_count = GodOfWealthWGData.Instance:GetAutoRenBuyNeed()
    if not IsEmptyTable(current_rmb_bug_cfg) and need_num > 0 then
        RechargeWGCtrl.Instance:Recharge(need_num, current_rmb_bug_cfg.rmb_type, 100 + need_count)
    end
	self:Close()
end

