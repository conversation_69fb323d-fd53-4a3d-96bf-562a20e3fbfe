ZCTipsSystemView = ZCTipsSystemView or BaseClass(SafeBaseView)

local STYLE = {
	STYLE_1 = 1,
	STYLE_2 = 2,
}

function ZCTipsSystemView:__init()
	self:AddViewResource(0, "uis/view/miscpre_load_prefab", "ZCSystemTipsView")
	self.view_layer = UiLayer.PopTop

	self.messge = nil
	self.close_timer = nil
	self.anim_speed = 1
	self.is_close = false
	self.convertion = -1
	self.index = 0
end

function ZCTipsSystemView:__delete()
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
	self.has_load_callback = false
end

function ZCTipsSystemView:LoadCallBack()
	self.system_tips = self.node_list["SystemTips"]
	self.rich_text2 = self.node_list["RichText2"]
	self.system_tips2 = self.node_list["SystemTips2"]
	self.rich_text3 = self.node_list["RichText3"]
	self.system_tips3 = self.node_list["SystemTips3"]

	self.anim = self.system_tips.animator
	-- self.anim:SetFloat("Speed", self.anim_speed)
	self.canvas_group = self.system_tips.canvas_group
	self.begin_pos = self.system_tips.transform.localPosition
end

function ZCTipsSystemView:SystemTipsVis()
	return self.canvas_group and self.canvas_group.alpha > 0
end

function ZCTipsSystemView:ReleaseCallBack()
	-- 清理变量和对象
	if self.system_tips and self.begin_pos then
		self.system_tips.transform.localPosition = self.begin_pos
	end
	self.system_tips = nil
	self.rich_text2 = nil
	self.system_tips2 = nil
	self.rich_text3 = nil
	self.system_tips3 = nil
	self.anim = nil
	self.canvas_group = nil
	self.begin_pos = nil
	self.tween = nil
end

function ZCTipsSystemView:Show(msg, speed, index)
	if self.begin_pos then
		self.system_tips.transform.localPosition = self.begin_pos
	end
	self.index = index or 0
	self.convertion = -1
	self.area_tips_con = 0
	speed = speed or 1
	self.anim_speed = speed
	self.convertion = nil
	-- if self.anim then
	-- 	self.anim:SetFloat("Speed", speed)
	-- end
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
	self.close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTips, self), 2)
	self.messge = msg
	self:Open()
	self.delay_callback = function()
		self.system_tips:SetActive(true)
		self.system_tips2:SetActive(false)
		self.system_tips3:SetActive(false)
--		self.node_list["RichText"]:SetActive(true)
		self.node_list["RichText2"]:SetActive(false)
		self.node_list["RichText3"]:SetActive(false)
	end
	if self.has_load_callback then
		self:Flush()
	end
end
function ZCTipsSystemView:Show2(convertion,msg, speed)
	self.convertion = convertion
	speed = speed or 1
	self.anim_speed = speed
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
	self.close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTips, self), 2)
	self.messge = msg
	self:Open()
	local function callback()
		--self.node_list["RichText"]:SetActive(false)
		self.node_list["SystemTips"]:SetActive(false)
		if self.convertion == 1 then
			self.node_list["RichText2"]:SetActive(true)
			self.node_list["SystemTips2"]:SetActive(true)
			self.node_list["RichText3"]:SetActive(false)
			self.node_list["SystemTips3"]:SetActive(false)
		else
			self.node_list["RichText2"]:SetActive(false)
			self.node_list["SystemTips2"]:SetActive(false)
			self.node_list["RichText3"]:SetActive(true)
			self.node_list["SystemTips3"]:SetActive(true)
		end
	end
	self.delay_callback = callback

	if self.has_load_callback then
		self:Flush()
	end
end

function ZCTipsSystemView:ShowIndexCallBack()
	if not self.has_load_callback then
		self:Flush()
	end
	self.has_load_callback = true
	self:DoMove()
end

function ZCTipsSystemView:ChangeSpeed(speed)
	self.anim_speed = speed
	self:DoMove()
end

function ZCTipsSystemView:AddIndex()
	self.index = self.index + 1
end

function ZCTipsSystemView:DoMove()
	if nil == self.begin_pos or nil == self.system_tips then return end
	if self.tween then
		self.tween:Kill()
	end
	self.system_tips.transform.localPosition = Vector3(self.begin_pos.x, self.begin_pos.y + 50 * self.index, self.begin_pos.z)
	self.tween = self.system_tips.transform:DOLocalMoveY(self.begin_pos.y + 50 * (self.index + 1), 1 / self.anim_speed)
	self.tween:SetEase(DG.Tweening.Ease.Linear)
end

function ZCTipsSystemView:CloseTips()
	self.is_close = true
	self:Close()
end

function ZCTipsSystemView:CloseCallBack()
	self.is_close = true
	if self.close_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.close_timer)
		self.close_timer = nil
	end
end

function ZCTipsSystemView:GetCloseFlag()
	return self.is_close
end

function ZCTipsSystemView:OnFlush(param_list)
	if self.delay_callback then
		self.delay_callback()
		self.delay_callback = nil
	end

	local scene_style = self:GetSceneStyle()
	for k,v in pairs(STYLE) do
		self.node_list["system_tips_bg_" .. v]:SetActive(v == scene_style)
	end

	--self.rich_text.text.text = self.messge
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.system_tips.rect)
	EmojiTextUtil.ParseRichText(self.node_list["sys_tip_text_" .. scene_style].emoji_text, self.messge)

	if self.convertion == 0 then
		EmojiTextUtil.ParseRichText(self.rich_text3.emoji_text, self.messge)
	elseif self.convertion == 1 then
		EmojiTextUtil.ParseRichText(self.rich_text2.emoji_text, self.messge)
	end
end

function ZCTipsSystemView:GetSceneStyle()
	if SceneType.TianShen3v3 == Scene.Instance:GetSceneType() then
		return STYLE.STYLE_2
	else
		return STYLE.STYLE_1
	end
end

function ZCTipsSystemView:GetAnimSpeed()
	return self.anim_speed
end