--require("game/crossserver/kuafu_pvp/kf_pvp_cfg")
require("game/crossserver/kuafu_pvp/kf_pvp_wg_data")
--require("game/crossserver/kuafu_pvp/kf_pvp_view")

require("game/crossserver/kuafu_pvp/kf_pvp_prepare_view")
require("game/crossserver/kuafu_pvp/kf_pvp_finish_view")
require("game/crossserver/kuafu_pvp/kf_pvp_info_view")
require("game/new_team/new_team_view")
require("game/crossserver/kuafu_pvp/kf_progressbar")


KuafuPVPWGCtrl = KuafuPVPWGCtrl or BaseClass(BaseWGCtrl)
function KuafuPVPWGCtrl:__init()
	if KuafuPVPWGCtrl.Instance then
		error("[KuafuPVPWGCtrl]:Attempt to create singleton twice!")
	end
	KuafuPVPWGCtrl.Instance = self

	self.data = KuafuPVPWGData.New()


	self.prepare_view = KfPVPPrepareView.New() 	--战斗前准备
	self.finish_view = KfPVPFisish.New()
	self.info_view = KfPVPInfo.New() 		--2018-10-06-18:12:11


	self:RegisterAllProtocals()

	self:BindGlobalEvent(ObjectEventType.OBJ_CREATE, BindTool.Bind1(self.OnObjCreate, self))
end

function KuafuPVPWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.prepare_view then
		self.prepare_view:DeleteMe()
		self.prepare_view = nil
	end

	self.finish_view:DeleteMe()
	self.finish_view = nil

	if self.info_view then
		self.info_view:DeleteMe()
		self.info_view = nil
	end

	if self.alert_window then
  		self.alert_window:DeleteMe()
   		self.alert_window = nil
    end



	KuafuPVPWGCtrl.Instance = nil
end

function KuafuPVPWGCtrl:RegisterAllProtocals()
	----旧协议
	--self:RegisterProtocol(SCCrossMultiuserChallengeBaseSelfSideInfo, "OnCrossMultiuserChallengeBaseSelfSideInfo")   --5712 		跨服3v3基本信息
	--self:RegisterProtocol(SCCrossMultiuserChallengeSelfInfoRefresh, "OnCrossMultiuserChallengeSelfInfoRefresh")		--5709 		跨服3v3主角信息刷新
	--self:RegisterProtocol(SCCrossMultiuserChallengeMatchInfoRefresh, "OnCrossMultiuserChallengeMatchInfoRefresh")	--5710 		跨服3v3信息刷新
	--self:RegisterProtocol(SCCrossMultiuserChallengeMatchState, "OnCrossMultiuserChallengeMatchState")				-- 5711 	跨服3v3匹配状态
	--self:RegisterProtocol(SCCrossMultiuserChallengeSelfActicityInfo, "OnCrossMultiuserChallengeSelfActicityInfo")	-- 5713 	跨服3v3角色活动信息
	--self:RegisterProtocol(SCCrossMultiuserChallengeMatchingState, "OnCrossMultiuserChallengeMatchingState")			-- 14150 	跨服3V3匹配状态
	--self:RegisterProtocol(SCMultiuserChallengeHasMatchNotice, "OnMultiuserChallengeHasMatchNotice")					-- 14152 	跨服3V3匹配通知
	--
 	self:RegisterProtocol(CSCrossMultiuserChallengeFetchGongxunReward)
	self:RegisterProtocol(CSCrossMultiuserChallengeMatchgingReq)
	self:RegisterProtocol(CSCrossMultiuserChallengeGetBaseSelfSideInfo)
	self:RegisterProtocol(CSCrossMultiuserChallengeFetchDaycountReward)
	self:RegisterProtocol(CSCrossMultiuerChallengeCancelMatching)
	self:RegisterProtocol(CSCheckMultiuserChallengeHasMatch)
	self:RegisterProtocol(CSCrossPVPWearCardReq)
	self:RegisterProtocol(CS3V3OtherOperateReq)
end


function KuafuPVPWGCtrl:RemindPvPGongXun()
	return self.data:RemindKFPvP()
end


function KuafuPVPWGCtrl:Open()
	if Field1v1WGCtrl.Instance.view then
		Field1v1WGCtrl.Instance.view:Open(KUAFU_TAB_TYPE.PVP)
	end
end

--2018-10-06-18:12:11
function KuafuPVPWGCtrl:OpenPrepareView()
	if self.prepare_view and not self.prepare_view:IsOpen() then
		self.prepare_view:Open()
	end
end

function KuafuPVPWGCtrl:ClosePrepareView()
	if self.prepare_view then
		self.prepare_view:Close()
	end
end

function KuafuPVPWGCtrl:OpenFisishView()
	if self.finish_view then
		self.finish_view:Open()
	end
end

function KuafuPVPWGCtrl:CloseFisishView()
	if self.finish_view then
		self.finish_view:Close()
	end
end

--任务
function KuafuPVPWGCtrl:OpenTimeInfo()
end

function KuafuPVPWGCtrl:OpenInfoView()
	if self.info_view then
		self.info_view:Open()
	end
end

function KuafuPVPWGCtrl:CloseInfoView()
	if self.info_view then
		self.info_view:Close()
	end
end

--------------------------------协议-----------------------------

-- 跨服3v3基本信息
function KuafuPVPWGCtrl:OnCrossMultiuserChallengeBaseSelfSideInfo(protocol)
	self.data:SetMatesInfo(protocol.user_list)
	for k,v in pairs(protocol.user_list) do
		AvatarManager.Instance:SetAvatarKey(v.uid, v.head_icon_big, v.head_icon_small)
	end
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVP)
	Field1v1WGCtrl.Instance:FlushPrepareScenePanel(ACTIVITY_TYPE.KF_PVP)
end

-- 跨服3v3主角信息刷新
function KuafuPVPWGCtrl:OnCrossMultiuserChallengeSelfInfoRefresh(protocol)
	self.data:SetRoleInfo(protocol)
	local role = Scene.Instance:GetMainRole()
	if nil ~= role then
		role:GetVo().special_param = protocol.self_side
		role:ReloadUIName()
	end
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVP)
	Field1v1WGCtrl.Instance:FlushPrepareScenePanel(ACTIVITY_TYPE.KF_PVP)
end

-- 跨服3v3信息刷新
function KuafuPVPWGCtrl:OnCrossMultiuserChallengeMatchInfoRefresh(protocol)
	self.data:SetStrongHoldInfo(protocol)
	-- local gathen_list = Scene.Instance:GetGatherList() or {}
	-- for _, v in pairs(gathen_list) do
		-- v:UpdateNameBoard()
		-- v:UpdateSpriteFrame()
	-- end
	if self.info_view then
		self.info_view:Flush()
		local scene_id = Scene.Instance:GetSceneType()
		if scene_id == SceneType.Kf_PVP then
			local monster_list = Scene.Instance:GetMonsterList()
			for k,v in pairs(monster_list) do
				v:ReloadUIName()
			end
		end
	end
end

function KuafuPVPWGCtrl:OnObjCreate(obj)
	if Scene.Instance:GetSceneType() == SceneType.Kf_PVP then
		if obj:GetType() == SceneObjType.GatherObj then
			-- obj:UpdateNameBoard()
			-- obj:UpdateSpriteFrame()
		end
	end
end

-- 跨服3v3匹配状态
function KuafuPVPWGCtrl:OnCrossMultiuserChallengeMatchState(protocol)
	self.data:SetPrepareInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if protocol.match_state == 0 and scene_type == SceneType.Kf_PVP then
		self:OpenPrepareView()
	else
		self:ClosePrepareView()
	end
	if protocol.match_state == 1 then
		self:OpenInfoView()
	end
	if protocol.match_state == 2 then
		self:OpenFisishView()
	end
end

-- 跨服3v3角色活动信息
function KuafuPVPWGCtrl:OnCrossMultiuserChallengeSelfActicityInfo(protocol)
	self.data:SetActivityInfo(protocol.info)
	KuafuPVPWGData.Instance:GetRewardIntegralCfg(protocol)
	-- self.data:GetRewardValCfg(protocol.gongxun_value)
	local state_info = self.data:GetMatchStateInfo()
	state_info.matching_state = protocol.info.matching_state
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVP)
	Field1v1WGCtrl.Instance:FlushPrepareScenePanel(ACTIVITY_TYPE.KF_PVP)
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVPREWARED)
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVPRANK)
	Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVPDUAN)
	if KuafuOnevoneWGCtrl.Instance.ring_view:IsOpen() then
		KuafuOnevoneWGCtrl.Instance.ring_view:Flush(RING_TAB_TYPE.KingLingPai)
	end
	self:SendCheckMultiuserChallengeHasMatch() --获取3v3战斗状态
	Field1v1WGCtrl.Instance:FlushGongXunView(KFPVP_TYPE.MORE)
	RemindManager.Instance:Fire(RemindName.ActPVPbArena)
	RemindManager.Instance:Fire(RemindName.ActPVPJiFenbArena)
end

-- 跨服3V3匹配状态
function KuafuPVPWGCtrl:OnCrossMultiuserChallengeMatchingState(protocol)
	self.data:SetMatchStateInfo(protocol)
	if protocol.matching_state == 2 then
		-- CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_PVP)
		GlobalEventSystem:Fire(KFONEVONE1v1Type.START_PVP)
		Field1v1WGCtrl.Instance:SetMatchBtnFalsePVP()
	else
		if protocol.matching_state == 3 then
			self.data:SetMatesInfo({})
			self:SendCrossMultiuserChallengeGetBaseSelfSideInfo()
			GlobalEventSystem:Fire(KFONEVONE1v1Type.KF_INFO_CHANGE)
			ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
		else
			Field1v1WGData.Instance:SetCurMatchFlag(KFPVP_TYPE.MORE)
			ViewManager.Instance:Open(GuideModuleName.KfOneVOneMatch)
			ViewManager.Instance:FlushView(GuideModuleName.KfOneVOneMatch)
			self.data:SetMatesInfo({})
			-- print_error(protocol.matching_state)
			for k,v in ipairs(protocol.user_list) do
				AvatarManager.Instance:SetAvatarKey(v.role_id, v.head_icon_big, v.head_icon_small)
				self.data:AddTeamMate(v)
			end
		end
		Field1v1WGCtrl.Instance.view:Flush(KUAFU_TAB_TYPE.PVP)
	end
	Field1v1WGCtrl.Instance:FlushPrepareScenePanel(ACTIVITY_TYPE.KF_PVP)
end

-- 跨服3V3匹配通知
function KuafuPVPWGCtrl:OnMultiuserChallengeHasMatchNotice(protocol)
	if protocol.has_match == 1 then


		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_PVP)
		-- if nil == self.alert_window then
		-- 	local function ok_fun()
		-- 		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_PVP)
		-- 	end
		-- 	local function cancel_func()
		-- 		CrossServerWGCtrl.SendCrossStartReq(0)
		-- 	end
		-- 	self.alert_window = Alert.New(Language.KuafuPVP.MatchingTips, ok_fun, cancel_func, nil, nil, nil, false)
		-- 	self.alert_window:SetOkString(Language.KuafuPVP.EnterTxt)
		-- end
		-- self.alert_window:Open()



		-- self.alert_window:NoCloseButton()
		-- self.alert_window:UseOne()
	else
		local state_info = self.data:GetMatchStateInfo()
		if state_info.matching_state == 0 or state_info.matching_state == 1 then
			self:SendCrossMultiuerChallengeCancelMatching()
		end
		if self.notice_callback then
			self.notice_callback()
			self.notice_callback = nil
		end
	end
end

function KuafuPVPWGCtrl:AddTeamMatchCallBack( callback )
	if nil == callback then
		return
	end
	self.notice_callback = callback
end

function KuafuPVPWGCtrl:OnCrossMultiuserChallengeFetchGongxunReward(seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossMultiuserChallengeFetchGongxunReward)
	send_protocol.seq = seq
	send_protocol:EncodeAndSend()
end
-- 跨服3v3请求匹配（队长发起）
function KuafuPVPWGCtrl:SendCrossMultiuserChallengeMatchgingReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossMultiuserChallengeMatchgingReq)
	send_protocol:EncodeAndSend()
end

-- 跨服3v3请求同队基本信息
function KuafuPVPWGCtrl:SendCrossMultiuserChallengeGetBaseSelfSideInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossMultiuserChallengeGetBaseSelfSideInfo)
	send_protocol:EncodeAndSend()
end

-- 跨服3v3获取每日奖励
function KuafuPVPWGCtrl:SendCrossMultiuserChallengeFetchDaycountReward(seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossMultiuserChallengeFetchDaycountReward)
	send_protocol.seq = seq
	send_protocol:EncodeAndSend()
end

-- 跨服3v3取消匹配
function KuafuPVPWGCtrl:SendCrossMultiuerChallengeCancelMatching()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossMultiuerChallengeCancelMatching)
	send_protocol:EncodeAndSend()
end

-- 请求跨服3v3是否有
function KuafuPVPWGCtrl:SendCheckMultiuserChallengeHasMatch()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCheckMultiuserChallengeHasMatch)
	send_protocol:EncodeAndSend()
end

function KuafuPVPWGCtrl:SendChallengeFetchGongxunReward()
	return
end

--穿戴令牌
function KuafuPVPWGCtrl:SendCrossPVPCard(opr_type, ring_seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossPVPWearCardReq)
	send_protocol.opr_type = opr_type or 0
	send_protocol.ring_seq = ring_seq or 0
	send_protocol:EncodeAndSend()
end

function KuafuPVPWGCtrl:SendCrossPVPInfo(opera_type, param)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CS3V3OtherOperateReq)
	send_protocol.opera_type = opera_type or 0
	send_protocol.param = param or 0
	send_protocol:EncodeAndSend()
end












