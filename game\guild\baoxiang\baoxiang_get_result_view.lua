BaoXiangGetResultView = BaoXiangGetResultView or BaseClass(SafeBaseView)

function BaoXiangGetResultView:__init()
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangGetResultView:LoadConfig()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_get_result_view")
end

function BaoXiangGetResultView:__delete()

end

function BaoXiangGetResultView:ReleaseCallBack()
	if not IsEmptyTable(self.anim_model_list) then
		for k,v in pairs(self.anim_model_list) do
			v:DeleteMe()
		end
		self.anim_model_list = {}
	end

	if self.baoxiang_model then
		self.baoxiang_model:DeleteMe()
		self.baoxiang_model = nil
	end

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	if CountDownManager.Instance:HasCountDown("get_result_auto_close") then
		CountDownManager.Instance:RemoveCountDown("get_result_auto_close")
	end

end

function BaoXiangGetResultView:LoadCallBack()

end

function BaoXiangGetResultView:ShowIndexCallBack()
	self:StartAutoClose()
end

function BaoXiangGetResultView:OnFlush()
	self.node_list["get_desc"].text.text = ""
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if not IsEmptyTable(info) then
		local quality = info.quality
		local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
		local baoxiang_name = treasure_cfg and treasure_cfg.name or ""
		local color = treasure_cfg and treasure_cfg.common_color or 2
		self.node_list["get_desc"].text.text = string.format(Language.BiZuoBaoXiang.GetBaoXiang,ToColorStr(baoxiang_name,ITEM_COLOR[color]))
	end
end

function BaoXiangGetResultView:IgnoireAnim()
	for i=1,4 do
		local anim_obj = self.node_list["anim_model"..i].gameObject
		anim_obj.transform.localScale = u3dpool.vec3(0.8,0.8,0.8)
	end
	self.node_list["baoxiang_model"].transform.localScale = u3dpool.vec3(0.8,0.8,0.8)
end

function BaoXiangGetResultView:PlayAnim()

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end
	self.anim_tween = DG.Tweening.DOTween.Sequence()

	local baoxiang_obj = self.node_list["baoxiang_model"].gameObject
	local baoxiang_obj_x,baoxiang_obj_y =  baoxiang_obj.transform.anchoredPosition.x, baoxiang_obj.transform.anchoredPosition.y
	local start_pos = self.node_list["anim_model2"].gameObject.transform.anchoredPosition

	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if not IsEmptyTable(info) then
		local quality = info.quality or 1
		start_pos = self.node_list["anim_model"..quality].gameObject.transform.anchoredPosition
	end

	baoxiang_obj.transform.anchoredPosition = u3dpool.vec3(start_pos.x, start_pos.y, 0)
	baoxiang_obj.transform.localScale = u3dpool.vec3(0,0,0)
	self.node_list["get_desc"].text.text = ""
	self.node_list["auto_close_text"].text.text = ""
	self.is_play_anim = true
	for i=1,4 do
		local anim_obj = self.node_list["anim_model"..i].gameObject
		local pos_x,pos_y =  anim_obj.transform.anchoredPosition.x, anim_obj.transform.anchoredPosition.y
		anim_obj.transform.anchoredPosition = u3dpool.vec3(75, -80, 0)
		anim_obj.transform.localScale = u3dpool.vec3(0,0,0)
		local model_tween1 = anim_obj.transform:DOScale(Vector3(0.8, 0.8, 0.8), 0.5)
		local model_tween2 = anim_obj.transform:DOAnchorPos(Vector3(pos_x, pos_y, 0), 0.5)
		self.anim_tween:Append(model_tween1)
		self.anim_tween:Join(model_tween2)
	end
	local baoxiang_tween1 = baoxiang_obj.transform:DOScale(Vector3(1.5, 1.5, 1.5), 0.5)
	local baoxiang_tween2 = baoxiang_obj.transform:DOScale(Vector3(0.8, 0.8, 0.8), 0.5)
	local baoxiang_tween3 = baoxiang_obj.transform:DOAnchorPos(Vector3(baoxiang_obj_x, baoxiang_obj_y, 0), 0.5)
	self.anim_tween:Append(baoxiang_tween1)
	self.anim_tween:Append(baoxiang_tween2)
	self.anim_tween:Join(baoxiang_tween3)
	self.anim_tween:OnComplete(function ()
		self.is_play_anim = false
		self:Flush()
		self:StartAutoClose()
	end)
end

function BaoXiangGetResultView:StartAutoClose()
	if CountDownManager.Instance:HasCountDown("get_result_auto_close") then
		CountDownManager.Instance:RemoveCountDown("get_result_auto_close")
	end
	self:AutoCloseRefreshTime(0,5)
	CountDownManager.Instance:AddCountDown("get_result_auto_close", BindTool.Bind1(self.AutoCloseRefreshTime, self), BindTool.Bind1(self.CompleteCallBack, self), nil, 5,1)

end

function BaoXiangGetResultView:AutoCloseRefreshTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list["auto_close_text"].text.text = string.format(Language.BiZuoBaoXiang.AutoCloseDesc,time)
end

function BaoXiangGetResultView:CompleteCallBack()
	self:Close()
end
