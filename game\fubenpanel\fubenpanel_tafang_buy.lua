TaFangBuyView = TaFangBuyView or BaseClass(SafeBaseView)

function TaFangBuyView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_tafang_msg")
end

function TaFangBuyView:__delete()

end

function TaFangBuyView:ReleaseCallBack()
	if self.tafang_vip_num1 then
		self.tafang_vip_num1:DeleteMe()
		self.tafang_vip_num1 = nil
	end

	if self.tafang_vip_num2 then
		self.tafang_vip_num2:DeleteMe()
		self.tafang_vip_num2 = nil
	end
end

function TaFangBuyView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(589,412)
	local other_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	local str = string.format(Language.FuBenPanel.CopperBuyNum, other_cfg.buy_times_gold)
	
	self.node_list["rich_buy_desc"].text.text = str

	

	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))
end

function TaFangBuyView:OpenBuyView()
	
end

function TaFangBuyView:ShowIndexCallBack()
	self:Flush()
end

function TaFangBuyView:CreateVipNums()
end

function TaFangBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyTfCountCfg()
	local day_buy_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 0
	local temp_str = vip_buy_cfg["param_" .. role_vip] - day_buy_times
	local des
	if temp_str > 0 then
    	des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - day_buy_times, vip_buy_cfg["param_" .. role_vip])
    else
    	des = string.format(Language.FuBenPanel.CopperBuyTipsNotnough, vip_buy_cfg["param_" .. role_vip] - day_buy_times, vip_buy_cfg["param_" .. role_vip])
    end
	--local des = string.format(Language.FuBenPanel.CopperBuyTips, vip_buy_cfg["param_" .. role_vip] - day_buy_times, vip_buy_cfg["param_" .. role_vip])
	self.node_list["rich_buy_des_1"].text.text = des
	local next_vip = FuBenPanelWGData.Instance:GetVipBuyTfCount(role_vip)
	if role_vip < next_vip and vip_buy_cfg["param_" .. next_vip] then
		if vip_buy_cfg["param_" .. next_vip] > 0 then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. next_vip])
		end
	else
		des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip])
	end
	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end
	self.node_list["rich_buy_des_2"].text.text = des
	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].text.text = "V"..role_vip
		
		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].text.text = "V"..next_vip
			self.node_list["layout_next_vip"]:SetActive(true)	
			self.node_list["img_arrow"]:SetActive(true)	
		else
			self.node_list["img_vip_nexlevel"].text.text = "V"..role_vip

			self.node_list["layout_next_vip"]:SetActive(true)	
			self.node_list["img_arrow"]:SetActive(true)	
		end
	end
end

function TaFangBuyView:OnClinkCancelHandler()
	self:Close()
end

function TaFangBuyView:OnClinkBuyCount()
	FuBenWGCtrl.Instance:SendBuyFBEnterTimes(FUBEN_TYPE.FBCT_TAFANG)

	local role_vip = VipWGData.Instance:GetRoleVipLevel() or 0
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyTfCountCfg()
	local day_buy_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES) or 1000

	if vip_buy_cfg == nil or vip_buy_cfg["param_" .. role_vip] - day_buy_times <= 1 then
		--self:Close()
	end
end