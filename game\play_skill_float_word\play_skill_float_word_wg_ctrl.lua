require("game/play_skill_float_word/play_skill_float_word_view")
require("game/play_skill_float_word/play_skill_float_word_wg_data")
require("game/play_skill_float_word/play_skill_name_word_view")

PlaySkillFloatWordWGCtrl = PlaySkillFloatWordWGCtrl or BaseClass(BaseWGCtrl)

function PlaySkillFloatWordWGCtrl:__init()
	if PlaySkillFloatWordWGCtrl.Instance then
		<PERSON><PERSON>r<PERSON><PERSON>("[PlaySkillFloatWordWGCtrl] attempt to create singleton twice!")
		return
	end

	PlaySkillFloatWordWGCtrl.Instance = self

    self.view = PlaySkillFloatWordView.New(GuideModuleName.PlaySkillFloatWordView)
    self.skill_show_word_view = PlaySkillFloatWordView.New()
    self.skill_name_word_view = PlaySkillNameWordView.New()
    self.data = PlaySkillFloatWordWGCData.New()

end

function PlaySkillFloatWordWGCtrl:__delete()
	PlaySkillFloatWordWGCtrl.Instance = nil
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    self.skill_show_word_view:DeleteMe()
    self.skill_show_word_view = nil

    self.skill_name_word_view:DeleteMe()
    self.skill_name_word_view = nil
end

function PlaySkillFloatWordWGCtrl:SetFloatWordSkill(skill_id)
    local cfg = self.data:GetFloatSkillCfg(skill_id)
    if cfg then      -- 过滤掉普攻
        local info = {}
        info.skill_id = cfg.skill_id
        info.float_time = cfg.float_time
        info.tween_mode = cfg.tween_mode
        self:OpenFloatWordView(info)
    -- else     -- 去掉技能文字展示
    --     self:SetWordSkillNmaeShow(skill_id)
    end
end

-- 展示技能释放名称
function PlaySkillFloatWordWGCtrl:SetWordSkillNmaeShow(skill_id)
    local normal_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
    if normal_cfg and normal_cfg.skill_id > 100 then
        local info = {}
        info.skill_id = normal_cfg.skill_id
        info.skill_name = normal_cfg.skill_name
        info.cd_s = normal_cfg.cd_s
        self.skill_name_word_view:SetDataAndOpen(info)
    end
end

function PlaySkillFloatWordWGCtrl:OpenFloatWordView(info)
    self.view:SetDataAndOpen(info)
end

function PlaySkillFloatWordWGCtrl:SetSkillShowWord(skill_id)
    local cfg = self.data:GetFloatSkillCfg(skill_id)
    if cfg then
        local info = {}
        info.skill_id = cfg.skill_id
        info.float_time = cfg.float_time
        info.tween_mode = cfg.tween_mode
        self.skill_show_word_view:SetDataAndOpen(info)
    end
end

function PlaySkillFloatWordWGCtrl:OpenFloatWordViewByType(float_word_type)
    local cfg = self.data:GetFloatWordCfgByType(float_word_type)

    if cfg then
        local info = {}
        info.bundle = cfg.bundle
        info.asset = cfg.asset
        info.float_time = cfg.float_time
        info.tween_mode = cfg.tween_mode
        info.float_word_type = float_word_type
        self.skill_show_word_view:SetDataAndOpen(info)
    end
end