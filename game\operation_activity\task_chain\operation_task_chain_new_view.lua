OperationTaskChainView = OperationTaskChainView or BaseClass(SafeBaseView)

function OperationTaskChainView:__init(view_name)
	self.view_style = ViewStyle.Full
	self.view_name = "OperationTaskChainView"
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain")
	self:SetMaskBg(true, true)
end

function OperationTaskChainView:LoadCallBack()
	self:InitParam()
	self:InitPanle()
	self:InitListener()
	self:InitTaskInfoList()
end

function OperationTaskChainView:ReleaseCallBack()
	if self.task_info_item_list then
		for k,v in pairs(self.task_info_item_list) do
			v:DeleteMe()
		end
		self.task_info_item_list = nil
	end
	if self.task_chain_reward_list then
		self.task_chain_reward_list:DeleteMe()
		self.task_chain_reward_list = nil
	end
	if self.special_reward_cell then
		self.special_reward_cell:DeleteMe()
		self.special_reward_cell = nil
	end
	if self.box_item_list then
		for k,v in pairs(self.box_item_list) do
			v:DeleteMe()
		end
		self.box_item_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("operation_task_chain_count_down")
end

function OperationTaskChainView:InitParam()
	self.rule_title = ""
	self.rule_content = ""
	self.day_index_cfg = nil
	self.box_item_list = {}
	self.save_task_chain_id = 0
end

function OperationTaskChainView:InitPanle()
	self.task_chain_reward_list = AsyncListView.New(ItemCell, self.node_list.xx_reward_list)
	self.special_reward_cell = ItemCell.New(self.node_list.xx_special_reward_root)
end

function OperationTaskChainView:InitListener()
	XUI.AddClickEventListener(self.node_list.xx_tip_btn, BindTool.Bind(self.OnClickTipBtn, self))
	XUI.AddClickEventListener(self.node_list.xx_goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
end

function OperationTaskChainView:InitTaskInfoList()
	local obj_root = self.node_list.xx_today_panel
	local temp_list = {}
	for i=1,3 do
		temp_list[i] = TaskChainInfoRender.New(obj_root:FindObj("shijian_item_" .. i))
	end
	self.task_info_item_list = temp_list
end

function OperationTaskChainView:OnClickTipBtn()
	RuleTip.Instance:SetTitle(self.rule_title)
	RuleTip.Instance:SetContent(self.rule_content)
end

function OperationTaskChainView:OnClickGotoBtn()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN or OperationTaskChainWGData.Instance:GetIsTaskChainTaskScene() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.IsInScene)
		return
	end

	local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
	if not day_index_cfg then
		return
	end

	local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
	if not task_chain_cfg then
		return
	end

	local is_open = OperationTaskChainWGData.Instance:GetTaskChainIsOpen()
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(task_chain_cfg.tips1)
		return
	end

	self:Close()
	BossWGData.Instance:SetBossEnterFlag(false)
	GuajiWGCtrl.Instance:ForceMoveToNpc(task_chain_cfg.npc_id, nil, task_chain_cfg.npc_scene)
end

function OperationTaskChainView:OnFlush(param_t)
	self.day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()

	self:FlushPictureAndTextContent()
	self:FlushSpecialReward()
	self:FlushTaskInfoList()
	self:FlushActCountDown()
	self:FlsuhRewardList()
	self:FlsuhRightPanel()
	self:FlushOpenTime()
	self:FlushBoxList()
end

function OperationTaskChainView:FlushPictureAndTextContent()
	if self.save_task_chain_id == self.day_index_cfg.task_chain_id then
		return
	end
	local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(self.day_index_cfg.task_chain_id)
	if task_chain_cfg then
		self.node_list.xx_biaoqian_lbl.text.text = task_chain_cfg.task_chain_name
		self.node_list.xx_root_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(task_chain_cfg.pic_1))
		local bundle,asset = ResPath.GetF2RawImagesPNG(task_chain_cfg.pic_2)
		self.node_list.xx_title_img.raw_image:LoadSprite(bundle, asset, function ()
			self.node_list.xx_title_img.raw_image:SetNativeSize()
		end)
	end
end

function OperationTaskChainView:FlsuhRewardList()
	local data_list = OperationTaskChainWGData.Instance:GetShowRewardList()
	self.task_chain_reward_list:SetDataList(data_list)
end

function OperationTaskChainView:FlushSpecialReward()
	local interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if interface_cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(interface_cfg.item_id)
		self.node_list.xx_special_reward_name.text.text = Language.OpertionAcitvity.TaskChain.TaskChainOpenStr .. item_cfg.name
		self.special_reward_cell:SetData({item_id = interface_cfg.item_id})
	end
end

function OperationTaskChainView:FlushTaskInfoList()
	local task_list = OperationTaskChainWGData.Instance:GetShowTaskList()
	local task_item_list = self.task_info_item_list
	if task_item_list and task_list then
		for i=1,#task_item_list do
			task_item_list[i]:SetData(task_list[i] or {})
		end
	end
end

function OperationTaskChainView:FlushOpenTime()
	local day_index_cfg = self.day_index_cfg
	if not day_index_cfg then
		return
	end

	local is_open = OperationTaskChainWGData.Instance:GetTaskChainIsOpen()
	self.node_list.xx_goto_btn_effect:SetActive(is_open)

	-- 今日开启时间
	local open_str = Language.OpertionAcitvity.TaskChain.TaskChainOpenStr
	local open_tab = Split(day_index_cfg.task_chain_start, "|")
	if not IsEmptyTable(open_tab) then
		for i = 1, #open_tab do
			open_str = open_str .. string.sub(open_tab[i], 1, 2) .. ":" ..string.sub(open_tab[i], 3, 4)
			if i < #open_tab then
				open_str = open_str .. ","
			end
		end
	end
	self.node_list.xx_open_time.text.text = open_str
end

function OperationTaskChainView:FlsuhRightPanel()
	local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(self.day_index_cfg and self.day_index_cfg.task_chain_id)
	if not task_chain_cfg then
		return
	end
	self.node_list.xx_open_title.text.text = task_chain_cfg.title_1
	self.node_list.xx_enter_title.text.text = task_chain_cfg.title_2
	-- 入口位置
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(task_chain_cfg.npc_scene)
	local npc_str = ""
	if scene_cfg and scene_cfg.npcs then
		for _,v in pairs(scene_cfg.npcs) do
			if v.id == task_chain_cfg.npc_id then
				npc_str = string.format(Language.OpertionAcitvity.TaskChain.TaskChainGoStrParam, scene_cfg.name, task_chain_cfg.npc_name, v.x, v.y)
				break
			end
		end
	end
	self.node_list.xx_enter_desc.text.text = npc_str
	self.node_list.xx_goto_btn_lbl.text.text = task_chain_cfg.btn_str
	-- tips内容
	self.rule_title = task_chain_cfg.task_chain_name
	self.rule_content = task_chain_cfg.rule_2
	self.node_list.xx_tip_label.text.text = task_chain_cfg.rule_1
end

function OperationTaskChainView:FlushBoxList()
	local data_list,value = OperationTaskChainWGData.Instance:GetBoxDataInfo()
	if not data_list then
		self.node_list.xx_box_panel:SetActive(false)
		return
	end

	self.node_list.xx_slider_img.image.fillAmount = value - 0.1

	local item_list = self.box_item_list or {}
	if #data_list > #item_list then
		local box_root = self.node_list.xx_box_content
		for i = #item_list + 1, #data_list do
			item_list[i] = TaskChainRewardBoxItem.New(box_root)
		end
		self.box_item_list = item_list
	end

	for i=1,#item_list do
		item_list[i]:SetData(data_list[i])
		item_list[i]:SetVisible(true)
	end

	---[[ 算出被挡住的可领取宝箱位置向左移
	local mw_value = OperationTaskChainWGData.Instance:GetMingWangValue()
	local box_index = 1
	for i=1,#data_list do
		if mw_value >= data_list[i].cfg.mingwang and data_list[i].reward_flag == 0 then
			box_index = i
			break
		end
	end
	-- 时间不够了先写死
	local panel_with = self.node_list.xx_box_panel.rect.sizeDelta.x
	local box_with = 100
	local content_left = 20
	local content_spacing = 6
	local show_lenght = content_left + (box_with * box_index) + content_spacing * (box_index - 1)
	if show_lenght >= panel_with then
		RectTransform.SetAnchoredPositionXY(self.node_list.xx_box_content.rect, panel_with - show_lenght, 0)
	end
	--]]

	self.node_list.xx_box_panel:SetActive(true)
end

function OperationTaskChainView:FlushActCountDown()
	CountDownManager.Instance:RemoveCountDown("operation_task_chain_count_down")
	local end_time = OperationActivityWGData.Instance:GetActivityInValidTime(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	local count_down_time = end_time - TimeWGCtrl.Instance:GetServerTime()
	if count_down_time > 0 then
		self.node_list.xx_count_down_lbl.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"operation_task_chain_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.FlushActCountDown, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.xx_count_down_lbl.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.xx_count_down_lbl.text.color = Str2C3b(COLOR3B.RED)
	end
end

function OperationTaskChainView:UpdateCountDown(elapse_time, total_time)
	self.node_list.xx_count_down_lbl.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end

-----------------------------------TaskChainInfoRender------------------------------------------------

TaskChainInfoRender = TaskChainInfoRender or BaseClass(BaseRender)

function TaskChainInfoRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end

	local is_finish = data.task_flag == 1
	self.node_list.shijian_lbl.text.text = data.task_cfg.task_name
	self.node_list.shijian_img.image:LoadSprite(ResPath.GetOperationTaskChainF2(data.task_cfg.icon))
	self.node_list.tick_img:SetActive(is_finish)
	XUI.SetGraphicGrey(self.node_list.shijian_bg, is_finish)

	self:SetVisible(true)
end

-----------------------------------TaskChainRewardBoxItem------------------------------------------------

TaskChainRewardBoxItem = TaskChainRewardBoxItem or BaseClass(BaseRender)

function TaskChainRewardBoxItem:__init(instance)
	if not instance then
		return
	end
	self:LoadAsset("uis/view/operation_task_chain_ui_prefab", "task_chain_box_item", instance.transform)
end

function TaskChainRewardBoxItem:__delete()
	if self.box_tween then
		self.box_tween:Kill()
		self.box_tween = nil
	end
end

function TaskChainRewardBoxItem:LoadCallBack()
	if self.node_list.click_img then
		XUI.AddClickEventListener(self.node_list.click_img, BindTool.Bind(self.OnClickBox, self))
	end
end

function TaskChainRewardBoxItem:OnFlush()
	local data = self:GetData()
	local interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if IsEmptyTable(data) or not interface_cfg then
		self:SetVisible(false)
		return
	end

	local mw_value = OperationTaskChainWGData.Instance:GetMingWangValue()
	local is_finish = mw_value >= data.cfg.mingwang
	local can_get = is_finish and data.reward_flag == 0
	local asset, bundle
	if is_finish then
		if data.reward_flag == 0 then
			asset, bundle = ResPath.GetF2CommonImages(interface_cfg.pic_7)
		else
			asset, bundle = ResPath.GetF2CommonImages(interface_cfg.pic_8)
		end
	else
		asset, bundle = ResPath.GetF2CommonImages(interface_cfg.pic_6)
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(interface_cfg.item_id)
	if item_cfg then
		self.node_list.const_img.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	end

	self.node_list.score_label.text.text = data.cfg.mingwang
	self.node_list.box_img.image:LoadSprite(asset, bundle)
	self.node_list.jiedian_img.image:LoadSprite(ResPath.GetOperationTaskChainF2(is_finish and "xx_xhuanzhong" or "xx_ling"))
	self.node_list.red_point:SetActive(can_get)
	self:PlayBoxTween(can_get)
	self:SetVisible(true)
end

function TaskChainRewardBoxItem:OnClickBox()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end

	local value = OperationTaskChainWGData.Instance:GetMingWangValue()
	if data.cfg.mingwang <= value and data.reward_flag == 0 then
		OperationTaskChainWGCtrl.Instance:SendTaskChainMWRewardFetch(data.index)
		if data.cfg ~= nil then
			local reward_data = SortTableKey(data.cfg.reward_item)
			TipWGCtrl.Instance:ShowGetReward(nil, reward_data, false, false, {again_btn = false})
		end
	else
		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainRewardView)
	end
end

function TaskChainRewardBoxItem:PlayBoxTween(is_play)
	if is_play then
		if not self.box_tween then
			self.box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.tween_root.transform, self.box_tween)
		end
	elseif self.box_tween then
		self.box_tween:Kill()
		self.box_tween = nil
	end
end