-- 排名奖励结算界面

BOSSInvasionResultView = BOSSInvasionResultView or BaseClass(SafeBaseView)

function BOSSInvasionResultView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_result_view")
end

function BOSSInvasionResultView:SetDataListAndOpen(rank_id, data_list)
	if IsEmptyTable(data_list) then
		return
	end

	self.rank_id = rank_id
	self.data_list = data_list

	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function BOSSInvasionResultView:LoadCallBack()
	self.node_list.victory:CustomSetActive(true)

	if not self.reward_data_list then
		self.reward_data_list = AsyncListView.New(ItemCell, self.node_list.reward_data_list)
		self.reward_data_list:SetStartZeroIndex(false)
	end

	CountDownManager.Instance:AddCountDown("boss_invasion_result_count_down", 
		function(elapse_time, total_time)
			if self.node_list.desc_close_time then
				self.node_list.desc_close_time.text.text = string.format(Language.BOSSInvasion.ResultAutoCloseTime, math.ceil(total_time - elapse_time))
			end
		end, 
		function()
			self:Close()
		end,
	nil, 8, 1)
end

function BOSSInvasionResultView:ReleaseCallBack()
	if self.reward_data_list then
		self.reward_data_list:DeleteMe()
		self.reward_data_list = nil
	end

	self.rank_id = nil
	self.data_list = nil

	if CountDownManager.Instance:HasCountDown("boss_invasion_result_count_down") then
		CountDownManager.Instance:RemoveCountDown("boss_invasion_result_count_down")
	end
end

function BOSSInvasionResultView:OnFlush()
	local rank_str = self.rank_id > 0 and string.format(Language.BOSSInvasion.RankRewardRankStr, self.rank_id) or Language.BOSSInvasion.RankRewardNoMyRank
	self.node_list.desc_rank_str.text.text = string.format(Language.BOSSInvasion.ResultRankStr, rank_str)

	-- local rank_cfg = BOSSInvasionWGData.Instance:GetBossRankRewardCfgByRankId(my_rank)
	-- if rank_cfg then
	-- 	self.reward_data_list:SetDataList(rank_cfg.reward_item)
	-- end

	self.reward_data_list:SetDataList(self.data_list)

	local winner_uuid, usid_tab, hurt_top_guild_id = BOSSInvasionWGData.Instance:GetWinnerInfo()
	local is_cross = BOSSInvasionWGData.Instance:IsCrossServer()
	local sign_str = ""
	local is_same_team = false

	if is_cross then
		local server_id = RoleWGData.Instance:GetOriginServerId()
		sign_str = Language.BOSSInvasion.CrossState[1]
		local my_plat_type = RoleWGData.Instance:GetPlatType()
		is_same_team = (server_id == usid_tab.temp_low and my_plat_type == usid_tab.temp_high)
	else
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		sign_str = Language.BOSSInvasion.CrossState[0]
		is_same_team = (guild_id > 0 and guild_id == hurt_top_guild_id)
	end

	local my_uuid = RoleWGData.Instance:GetUUid()

	if winner_uuid == my_uuid then
		is_same_team = true
		local role_name = RoleWGData.Instance:GetAttr("name")
		self.node_list.desc_winner_str.text.text = string.format(Language.BOSSInvasion.ResultWinnerStr, role_name, sign_str)
	else
		BrowseWGCtrl.Instance:BrowRoelInfo(winner_uuid.temp_low, function (protocol)
			-- self.node_list.desc_boss_reward_tip.text.text = string.format(Language.BOSSInvasion.BossEndRewardTip, protocol.role_name, sign_str, reward_str)
			self.node_list.desc_winner_str.text.text = string.format(Language.BOSSInvasion.ResultWinnerStr, protocol.role_name, sign_str)
		end, winner_uuid.temp_high, true)
	end

	local get_reward_str = is_same_team and Language.BOSSInvasion.BossEndCanGetReward or Language.BOSSInvasion.BossEndCanNotGetReward
	local reward_str = string.format(get_reward_str, sign_str)
	self.node_list.desc_reward_str.text.text = reward_str
end