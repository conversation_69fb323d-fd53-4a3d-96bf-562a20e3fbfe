require("game/capability_contrast/capability_contrast_wg_data")
require("game/capability_contrast/capability_contrast_view")

CapabilityContrastWGCtrl = CapabilityContrastWGCtrl or BaseClass(BaseWGCtrl)

function CapabilityContrastWGCtrl:__init()
	if CapabilityContrastWGCtrl.Instance then
		error("[CapabilityContrastWGCtrl]:Attempt to create singleton twice!")
	end
	CapabilityContrastWGCtrl.Instance = self

    self.data = CapabilityContrastWGData.New()
    self.view = CapabilityContrastView.New(GuideModuleName.CapabilityContrastView)
    self:RegisterAllProtocols()
end

function CapabilityContrastWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	CapabilityContrastWGCtrl.Instance = nil
end

function CapabilityContrastWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSCapabilityCmpReqUUID)
	self:RegisterProtocol(CSCapabilityCmpCoressReq)
	self:RegisterProtocol(SCCapabilityCmpAck, "OnSCCapabilityCmpAck")
end

function CapabilityContrastWGCtrl:OnSCCapabilityCmpAck(protocol)
	-- print_error("【----返回-----】：", protocol.look_role_info)
	self.data:SetCapabilityInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	else
		self.view:Open()
	end
end

function CapabilityContrastWGCtrl:ReqCapabilityCmpUUID(uuid)
    -- print_error("【-----战力对比请求 uuid----】：", uuid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCapabilityCmpReqUUID)
	protocol.uuid = uuid or MsgAdapter.InitUUID()
	protocol:EncodeAndSend()
end

function CapabilityContrastWGCtrl:ReqCapabilityCmpCoress(plat_type, uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCapabilityCmpCoressReq)
	protocol.plat_type = plat_type or -1
	protocol.uid = uid or 0
	-- print_error("【-----战力对比请求 plat_type, uid----】：", protocol.plat_type, protocol.uid)
	protocol:EncodeAndSend()
end
