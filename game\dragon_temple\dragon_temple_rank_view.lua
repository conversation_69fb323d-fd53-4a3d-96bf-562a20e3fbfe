function DragonTempleView:LoadIndexCallBackRank()
	self.rank_role_id_cache = {}
	self.rank_donate_Reward_item_list = {}
	XUI.AddClickEventListener(self.node_list.rank_btn, BindTool.Bind1(self.OnClickRankBtn, self))
	XUI.AddClickEventListener(self.node_list.rank_info_btn, BindTool.Bind1(self.OnClickRankInfo, self))
	self:InitModelLists()
	self:FlushRankDisPlayTitle()
	self.node_list.rank_rule_desc.text.text = Language.DragonTemple.RankRuleDesc
	self.node_list.my_contribute_desc.text.text = Language.DragonTemple.RankMyLongHunDesc
end

function DragonTempleView:ShowIndexCallBackRank()
	DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.RANK_INFO, DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN)
	DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.RANK_INFO, DRAGON_TEMPLE_OPERATE_RANK_TYPE.MONEYCONTRIBUTE)
	self:DokRankViewAnim()

	self:StartRankCountDown()
end

function DragonTempleView:ReleaseRank()
	if self.rank_role_model_list then
		for k, v in pairs(self.rank_role_model_list) do
			v:DeleteMe()
		end

		self.rank_role_model_list = nil
	end

	if not IsEmptyTable(self.rank_donate_Reward_item_list) then
		for k, v in pairs(self.rank_donate_Reward_item_list) do
			v:DeleteMe()
		end

		self.rank_donate_Reward_item_list = nil
	end

	if self.rank_donate_tips then
		self.rank_donate_tips:DeleteMe()
		self.rank_donate_tips = nil
	end

	self:RemoveRankCountDown()
end

function DragonTempleView:OnFlushRank()
	self:FlushRankBonusPool()
	self:FlushRankModel()
	self:FlushRankBomReward()
end

function DragonTempleView:FlushRankBonusPool()
	self.node_list.contribute_value.text.text = DragonTempleWGData.Instance:GetRankBonusPoolNum()
	local longhun = DragonTempleWGData.Instance:GetLongHunValue()
end

function DragonTempleView:InitModelLists()
	if not self.rank_role_model_list then
		self.rank_role_model_list = {}
		for i = 1, 3 do
			local node = self.node_list["role_display" .. i]
			self.rank_role_model_list[i] = RoleModel.New()
			local display_data = {
				parent_node = node,
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.S,
				can_drag = true,
			}
	
			self.rank_role_model_list[i]:SetRenderTexUI3DModel(display_data)
			-- self.rank_role_model_list[i]:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
			self:AddUiRoleModel(self.rank_role_model_list[i])
		end
	end
end

function DragonTempleView:FlushRankModel()
	local longhun_rank_datalist = DragonTempleWGData.Instance:GetRankDataListByRankType(DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN)
	for i = 1, 3 do
		local data = (longhun_rank_datalist or {})[i]
		local no_data = IsEmptyTable(data)
		local name = no_data and Language.DragonTemple.RankXuWeiYiDai or data.name 
		if not no_data then
			local flush_fun = function (protocol)
				if not self.node_list then
					return
				end

				if self.rank_role_model_list[i] then
					local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true, ignore_shouhuan = true, ignore_tail = true, ignore_waist = true}
					self.rank_role_model_list[i]:SetModelResInfo(protocol, ignore_table)
					self.rank_role_model_list[i]:PlayRoleShowAction()
					self.rank_role_model_list[i]:FixToOrthographic(self.root_node_transform)
				end
			end

			local key_str = data.uuid.temp_high .. data.uuid.temp_low

			if not self.rank_role_id_cache[i] or key_str ~= self.rank_role_id_cache[i] then
				BrowseWGCtrl.Instance:SendGlobalQueryRoleInfo(data.uuid.temp_high, data.uuid.temp_low, nil, flush_fun)
				self.rank_role_id_cache[i] = key_str
			else
				self.rank_role_model_list[i]:PlayLastAction()
			end
		else
			self.rank_role_id_cache[i] = nil
		end

		self.node_list["role_name" .. i].text.text = name
		self.node_list["role_display" .. i]:SetActive(not no_data)
		self.node_list["no_role_info" .. i]:SetActive(no_data)
	end
end

function DragonTempleView:FlushRankBomReward()
	for i = 1, 5 do
		if not self.rank_donate_Reward_item_list[i] then
			self.rank_donate_Reward_item_list[i] = ItemCell.New(self.node_list["rank_slider_item_pos" .. i])
			self.rank_donate_Reward_item_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickItem, self, i))
			self.rank_donate_Reward_item_list[i]:SetIsShowTips(false)
		end

		local data_cfg = DragonTempleWGData.Instance:GetDonateRewardCfgBySeq(i - 1)

		if not IsEmptyTable(data_cfg) then
			local has_get_flag = DragonTempleWGData.Instance:GetRankSliderItemGetFlag(i - 1)
			local can_get_flag = DragonTempleWGData.Instance:GetRankSliderItemCanGetFlag(i - 1)
			
			self.rank_donate_Reward_item_list[i]:SetData(data_cfg.reward_item[0])
			self.rank_donate_Reward_item_list[i]:SetLingQuVisible(has_get_flag)
			self.node_list["rank_slider_item_canget" .. i]:SetActive(not has_get_flag and can_get_flag)
			self.node_list["rank_slider_item_value" .. i].text.text = string.format(Language.DragonTemple.RankDonateTime, data_cfg.need_times)
		end
	end

	self.node_list.rank_total_value.text.text = DragonTempleWGData.Instance:GetDailyDonateTime()
	self.node_list.rank_slider.slider.value = DragonTempleWGData.Instance:GetRankSliderValue()
end

function DragonTempleView:OnClickItem(index)
	local data_cfg = DragonTempleWGData.Instance:GetDonateRewardCfgBySeq(index - 1)
	if IsEmptyTable(data_cfg) then
		return 
	end

	local has_get_flag = DragonTempleWGData.Instance:GetRankSliderItemGetFlag(index - 1)
	local can_get_flag = DragonTempleWGData.Instance:GetRankSliderItemCanGetFlag(index - 1)
	
	if not has_get_flag and can_get_flag then
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.FETCH_DONATE_TIMES_REWARD, data_cfg.seq)
	else
		TipWGCtrl.Instance:OpenItem(data_cfg.reward_item[0])
	end
end

function DragonTempleView:FlushRankDisPlayTitle()
	local longhun_rank_cfg = DragonTempleWGData.Instance:GetLongHunRankCfg()
	for i = 1, 3 do
		local data = longhun_rank_cfg[i]
		if not IsEmptyTable(data) then
			local b,a = ResPath.GetTitleModel(data.title_id)
			self.node_list["title_display" .. i]:ChangeAsset(b, a, false)
		end
	end
end

function DragonTempleView:OnClickRankBtn()
	local donate_cfg = DragonTempleWGData.Instance:GetDonateCfg()
	if IsEmptyTable(donate_cfg) then
		return
	end

	local can_donate = DragonTempleWGData.Instance:CanDonateGetLongHun()
	if not can_donate then
		TipWGCtrl.Instance:OpenConfirmAlertTips(Language.DragonTemple.MaxRankDonateTime)
		return
	end

	local gold_num = RoleWGData.Instance.role_info.gold
	local donate_cost_gold = donate_cfg.donate_cost_gold
	local donate_add_longhun = donate_cfg.donate_add_longhun

	if gold_num < donate_cost_gold then
		VipWGCtrl.Instance:OpenTipNoGold()
	else
		if not self.rank_donate_tips then
			self.rank_donate_tips = Alert.New()
			self.rank_donate_tips:SetLableString(string.format(Language.DragonTemple.RankDonateTips, donate_cost_gold, donate_add_longhun))
			self.rank_donate_tips:SetOkFunc(function ()
				DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.DONATE, donate_cfg.seq)
				
				-- 后端说跨服排行榜信息不能广播 但是前端需要在捐献后刷新奖池，在此补发请求
				DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.RANK_INFO, DRAGON_TEMPLE_OPERATE_RANK_TYPE.LONGHUN)
				DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.RANK_INFO, DRAGON_TEMPLE_OPERATE_RANK_TYPE.MONEYCONTRIBUTE)
			end)
			self.rank_donate_tips:SetShowCheckBox(true, "dragon_temple_rank")
			self.rank_donate_tips:SetCheckBoxDefaultSelect(false)
			self.rank_donate_tips:SetCheckBoxText(Language.DragonTemple.NoRemind)
		end

		self.rank_donate_tips:Open()
	end
end

function DragonTempleView:OnClickRankInfo()
	DragonTempleWGCtrl.Instance:OpenLongHunRankInfoView()
end

function DragonTempleView:PlayRankDonateSucEffect()
	self:PlayUseEffect(UIEffectName.s_juanxian)
	local bundle_name, asset_name = ResPath.GetEffectUi("UI_effect_longshendian6")

	local effect_node = self.node_list["rank_donate_suc_effect"]
	if effect_node then
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, effect_node.transform, 3)
	end
end

function DragonTempleView:DokRankViewAnim()
    local tween_info = UITween_CONSTS.DragonTemple
    RectTransform.SetAnchoredPositionXY(self.node_list.rank_right_root.rect, 600, 0)

    self.node_list.rank_right_root.rect:DOAnchorPos(Vector2(45, 0), tween_info.movetime)
end


function DragonTempleView:StartRankCountDown()
	local cd_time = DragonTempleWGData.Instance:GetDonateCutOffTime()
	if cd_time > 0 then
		local time_str = TimeUtil.FormatSecond(cd_time)
		self.node_list.my_contribute_value.text.text = string.format(Language.DragonTemple.RankMyLongHunValue, time_str)
		self:RemoveRankCountDown()

		CountDownManager.Instance:AddCountDown("dragon_temple_rank", 
            BindTool.Bind(self.UpdateRankCountDown, self), 
            BindTool.Bind(self.OnRankCountDownComPlete, self), 
            nil, cd_time, 1)
	else
		self.node_list.my_contribute_value.text.text = string.format(Language.DragonTemple.RankCountDownOver)
	end
end

function DragonTempleView:UpdateRankCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time

	if valid_time > 0 then
		if self.node_list.my_contribute_value then
			local time_str = TimeUtil.FormatSecond(valid_time)
			self.node_list.my_contribute_value.text.text = string.format(Language.DragonTemple.RankMyLongHunValue, time_str)
		end
	end
end

function DragonTempleView:OnRankCountDownComPlete()
	self.node_list.my_contribute_value.text.text = string.format(Language.DragonTemple.RankCountDownOver)
end

function DragonTempleView:RemoveRankCountDown()
    if CountDownManager.Instance:HasCountDown("dragon_temple_rank") then
        CountDownManager.Instance:RemoveCountDown("dragon_temple_rank")
    end
end