﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_Universal_UniversalRenderPipelineAssetWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset), typeof(UnityEngine.Rendering.RenderPipelineAsset));
		<PERSON><PERSON>Function("LoadBuiltinRendererData", LoadBuiltinRendererData);
		<PERSON><PERSON>ction("GetRenderer", GetRenderer);
		L.RegFunction("OnBeforeSerialize", OnBeforeSerialize);
		L.RegFunction("OnAfterDeserialize", OnAfterDeserialize);
		L.RegFunction("New", _CreateUnityEngine_Rendering_Universal_UniversalRenderPipelineAsset);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ons<PERSON>("k_MinLutSize", 16);
		<PERSON><PERSON>("k_MaxLutSize", 65);
		<PERSON><PERSON>("AdditionalLightsDefaultShadowResolutionTierLow", get_AdditionalLightsDefaultShadowResolutionTierLow, null);
		L.RegVar("AdditionalLightsDefaultShadowResolutionTierMedium", get_AdditionalLightsDefaultShadowResolutionTierMedium, null);
		L.RegVar("AdditionalLightsDefaultShadowResolutionTierHigh", get_AdditionalLightsDefaultShadowResolutionTierHigh, null);
		L.RegVar("scriptableRenderer", get_scriptableRenderer, null);
		L.RegVar("supportsCameraDepthTexture", get_supportsCameraDepthTexture, set_supportsCameraDepthTexture);
		L.RegVar("supportsCameraOpaqueTexture", get_supportsCameraOpaqueTexture, set_supportsCameraOpaqueTexture);
		L.RegVar("opaqueDownsampling", get_opaqueDownsampling, null);
		L.RegVar("supportsTerrainHoles", get_supportsTerrainHoles, null);
		L.RegVar("storeActionsOptimization", get_storeActionsOptimization, set_storeActionsOptimization);
		L.RegVar("supportsHDR", get_supportsHDR, set_supportsHDR);
		L.RegVar("msaaSampleCount", get_msaaSampleCount, set_msaaSampleCount);
		L.RegVar("renderScale", get_renderScale, set_renderScale);
		L.RegVar("upscalingFilter", get_upscalingFilter, set_upscalingFilter);
		L.RegVar("fsrOverrideSharpness", get_fsrOverrideSharpness, set_fsrOverrideSharpness);
		L.RegVar("fsrSharpness", get_fsrSharpness, set_fsrSharpness);
		L.RegVar("mainLightRenderingMode", get_mainLightRenderingMode, null);
		L.RegVar("supportsMainLightShadows", get_supportsMainLightShadows, null);
		L.RegVar("mainLightShadowmapResolution", get_mainLightShadowmapResolution, null);
		L.RegVar("additionalLightsRenderingMode", get_additionalLightsRenderingMode, null);
		L.RegVar("maxAdditionalLightsCount", get_maxAdditionalLightsCount, set_maxAdditionalLightsCount);
		L.RegVar("supportsAdditionalLightShadows", get_supportsAdditionalLightShadows, null);
		L.RegVar("additionalLightsShadowmapResolution", get_additionalLightsShadowmapResolution, null);
		L.RegVar("additionalLightsShadowResolutionTierLow", get_additionalLightsShadowResolutionTierLow, null);
		L.RegVar("additionalLightsShadowResolutionTierMedium", get_additionalLightsShadowResolutionTierMedium, null);
		L.RegVar("additionalLightsShadowResolutionTierHigh", get_additionalLightsShadowResolutionTierHigh, null);
		L.RegVar("reflectionProbeBlending", get_reflectionProbeBlending, null);
		L.RegVar("reflectionProbeBoxProjection", get_reflectionProbeBoxProjection, null);
		L.RegVar("shadowDistance", get_shadowDistance, set_shadowDistance);
		L.RegVar("shadowCascadeCount", get_shadowCascadeCount, set_shadowCascadeCount);
		L.RegVar("cascade2Split", get_cascade2Split, null);
		L.RegVar("cascade3Split", get_cascade3Split, null);
		L.RegVar("cascade4Split", get_cascade4Split, null);
		L.RegVar("cascadeBorder", get_cascadeBorder, set_cascadeBorder);
		L.RegVar("shadowDepthBias", get_shadowDepthBias, set_shadowDepthBias);
		L.RegVar("shadowNormalBias", get_shadowNormalBias, set_shadowNormalBias);
		L.RegVar("supportsSoftShadows", get_supportsSoftShadows, null);
		L.RegVar("supportsDynamicBatching", get_supportsDynamicBatching, set_supportsDynamicBatching);
		L.RegVar("supportsMixedLighting", get_supportsMixedLighting, null);
		L.RegVar("supportsLightLayers", get_supportsLightLayers, null);
		L.RegVar("shaderVariantLogLevel", get_shaderVariantLogLevel, set_shaderVariantLogLevel);
		L.RegVar("volumeFrameworkUpdateMode", get_volumeFrameworkUpdateMode, null);
		L.RegVar("useSRPBatcher", get_useSRPBatcher, set_useSRPBatcher);
		L.RegVar("colorGradingMode", get_colorGradingMode, set_colorGradingMode);
		L.RegVar("colorGradingLutSize", get_colorGradingLutSize, set_colorGradingLutSize);
		L.RegVar("useFastSRGBLinearConversion", get_useFastSRGBLinearConversion, null);
		L.RegVar("useAdaptivePerformance", get_useAdaptivePerformance, set_useAdaptivePerformance);
		L.RegVar("conservativeEnclosingSphere", get_conservativeEnclosingSphere, set_conservativeEnclosingSphere);
		L.RegVar("numIterationsEnclosingSphere", get_numIterationsEnclosingSphere, set_numIterationsEnclosingSphere);
		L.RegVar("defaultMaterial", get_defaultMaterial, null);
		L.RegVar("defaultParticleMaterial", get_defaultParticleMaterial, null);
		L.RegVar("defaultLineMaterial", get_defaultLineMaterial, null);
		L.RegVar("defaultTerrainMaterial", get_defaultTerrainMaterial, null);
		L.RegVar("defaultUIMaterial", get_defaultUIMaterial, null);
		L.RegVar("defaultUIOverdrawMaterial", get_defaultUIOverdrawMaterial, null);
		L.RegVar("defaultUIETC1SupportedMaterial", get_defaultUIETC1SupportedMaterial, null);
		L.RegVar("default2DMaterial", get_default2DMaterial, null);
		L.RegVar("default2DMaskMaterial", get_default2DMaskMaterial, null);
		L.RegVar("decalMaterial", get_decalMaterial, null);
		L.RegVar("defaultShader", get_defaultShader, null);
		L.RegVar("renderingLayerMaskNames", get_renderingLayerMaskNames, null);
		L.RegVar("prefixedRenderingLayerMaskNames", get_prefixedRenderingLayerMaskNames, null);
		L.RegVar("lightLayerMaskNames", get_lightLayerMaskNames, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Rendering_Universal_UniversalRenderPipelineAsset(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = new UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LoadBuiltinRendererData(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset>(L, 1);
				UnityEngine.Rendering.Universal.ScriptableRendererData o = obj.LoadBuiltinRendererData();
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2)
			{
				UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset>(L, 1);
				UnityEngine.Rendering.Universal.RendererType arg0 = (UnityEngine.Rendering.Universal.RendererType)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.RendererType));
				UnityEngine.Rendering.Universal.ScriptableRendererData o = obj.LoadBuiltinRendererData(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset.LoadBuiltinRendererData");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRenderer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Rendering.Universal.ScriptableRenderer o = obj.GetRenderer(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBeforeSerialize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset>(L, 1);
			obj.OnBeforeSerialize();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnAfterDeserialize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset>(L, 1);
			obj.OnAfterDeserialize();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AdditionalLightsDefaultShadowResolutionTierLow(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset.AdditionalLightsDefaultShadowResolutionTierLow);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AdditionalLightsDefaultShadowResolutionTierMedium(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset.AdditionalLightsDefaultShadowResolutionTierMedium);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AdditionalLightsDefaultShadowResolutionTierHigh(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset.AdditionalLightsDefaultShadowResolutionTierHigh);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scriptableRenderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.ScriptableRenderer ret = obj.scriptableRenderer;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scriptableRenderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsCameraDepthTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsCameraDepthTexture;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsCameraDepthTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsCameraOpaqueTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsCameraOpaqueTexture;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsCameraOpaqueTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_opaqueDownsampling(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.Downsampling ret = obj.opaqueDownsampling;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index opaqueDownsampling on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsTerrainHoles(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsTerrainHoles;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsTerrainHoles on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_storeActionsOptimization(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.StoreActionsOptimization ret = obj.storeActionsOptimization;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index storeActionsOptimization on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsHDR(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsHDR;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsHDR on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_msaaSampleCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.msaaSampleCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index msaaSampleCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.renderScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_upscalingFilter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.UpscalingFilterSelection ret = obj.upscalingFilter;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index upscalingFilter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fsrOverrideSharpness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.fsrOverrideSharpness;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fsrOverrideSharpness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fsrSharpness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.fsrSharpness;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fsrSharpness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mainLightRenderingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.LightRenderingMode ret = obj.mainLightRenderingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mainLightRenderingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsMainLightShadows(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsMainLightShadows;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsMainLightShadows on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mainLightShadowmapResolution(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.mainLightShadowmapResolution;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mainLightShadowmapResolution on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_additionalLightsRenderingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.LightRenderingMode ret = obj.additionalLightsRenderingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additionalLightsRenderingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxAdditionalLightsCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.maxAdditionalLightsCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxAdditionalLightsCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsAdditionalLightShadows(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsAdditionalLightShadows;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsAdditionalLightShadows on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_additionalLightsShadowmapResolution(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.additionalLightsShadowmapResolution;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additionalLightsShadowmapResolution on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_additionalLightsShadowResolutionTierLow(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.additionalLightsShadowResolutionTierLow;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additionalLightsShadowResolutionTierLow on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_additionalLightsShadowResolutionTierMedium(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.additionalLightsShadowResolutionTierMedium;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additionalLightsShadowResolutionTierMedium on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_additionalLightsShadowResolutionTierHigh(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.additionalLightsShadowResolutionTierHigh;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index additionalLightsShadowResolutionTierHigh on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reflectionProbeBlending(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.reflectionProbeBlending;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reflectionProbeBlending on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reflectionProbeBoxProjection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.reflectionProbeBoxProjection;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reflectionProbeBoxProjection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.shadowDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowCascadeCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.shadowCascadeCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowCascadeCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cascade2Split(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.cascade2Split;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cascade2Split on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cascade3Split(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Vector2 ret = obj.cascade3Split;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cascade3Split on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cascade4Split(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Vector3 ret = obj.cascade4Split;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cascade4Split on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cascadeBorder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.cascadeBorder;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cascadeBorder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowDepthBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.shadowDepthBias;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowDepthBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowNormalBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float ret = obj.shadowNormalBias;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowNormalBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsSoftShadows(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsSoftShadows;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsSoftShadows on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsDynamicBatching(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsDynamicBatching;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsDynamicBatching on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsMixedLighting(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsMixedLighting;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsMixedLighting on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_supportsLightLayers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.supportsLightLayers;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsLightLayers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shaderVariantLogLevel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.ShaderVariantLogLevel ret = obj.shaderVariantLogLevel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shaderVariantLogLevel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_volumeFrameworkUpdateMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.VolumeFrameworkUpdateMode ret = obj.volumeFrameworkUpdateMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index volumeFrameworkUpdateMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useSRPBatcher(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.useSRPBatcher;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useSRPBatcher on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_colorGradingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.ColorGradingMode ret = obj.colorGradingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_colorGradingLutSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.colorGradingLutSize;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradingLutSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useFastSRGBLinearConversion(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.useFastSRGBLinearConversion;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useFastSRGBLinearConversion on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_useAdaptivePerformance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.useAdaptivePerformance;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useAdaptivePerformance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_conservativeEnclosingSphere(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool ret = obj.conservativeEnclosingSphere;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index conservativeEnclosingSphere on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_numIterationsEnclosingSphere(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int ret = obj.numIterationsEnclosingSphere;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index numIterationsEnclosingSphere on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultParticleMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultParticleMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultParticleMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultLineMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultLineMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultLineMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTerrainMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultTerrainMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultTerrainMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUIMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultUIMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultUIMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUIOverdrawMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultUIOverdrawMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultUIOverdrawMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUIETC1SupportedMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultUIETC1SupportedMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultUIETC1SupportedMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_default2DMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.default2DMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index default2DMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_default2DMaskMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.default2DMaskMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index default2DMaskMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_decalMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Material ret = obj.decalMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index decalMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.defaultShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderingLayerMaskNames(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			string[] ret = obj.renderingLayerMaskNames;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderingLayerMaskNames on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_prefixedRenderingLayerMaskNames(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			string[] ret = obj.prefixedRenderingLayerMaskNames;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index prefixedRenderingLayerMaskNames on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lightLayerMaskNames(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			string[] ret = obj.lightLayerMaskNames;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lightLayerMaskNames on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_supportsCameraDepthTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.supportsCameraDepthTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsCameraDepthTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_supportsCameraOpaqueTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.supportsCameraOpaqueTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsCameraOpaqueTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_storeActionsOptimization(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.StoreActionsOptimization arg0 = (UnityEngine.Rendering.Universal.StoreActionsOptimization)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.StoreActionsOptimization));
			obj.storeActionsOptimization = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index storeActionsOptimization on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_supportsHDR(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.supportsHDR = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsHDR on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_msaaSampleCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.msaaSampleCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index msaaSampleCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_renderScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.renderScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_upscalingFilter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.UpscalingFilterSelection arg0 = (UnityEngine.Rendering.Universal.UpscalingFilterSelection)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.UpscalingFilterSelection));
			obj.upscalingFilter = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index upscalingFilter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fsrOverrideSharpness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.fsrOverrideSharpness = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fsrOverrideSharpness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fsrSharpness(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.fsrSharpness = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fsrSharpness on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_maxAdditionalLightsCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.maxAdditionalLightsCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index maxAdditionalLightsCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.shadowDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowCascadeCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.shadowCascadeCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowCascadeCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cascadeBorder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.cascadeBorder = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cascadeBorder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowDepthBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.shadowDepthBias = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowDepthBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowNormalBias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.shadowNormalBias = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowNormalBias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_supportsDynamicBatching(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.supportsDynamicBatching = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index supportsDynamicBatching on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shaderVariantLogLevel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.ShaderVariantLogLevel arg0 = (UnityEngine.Rendering.Universal.ShaderVariantLogLevel)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.ShaderVariantLogLevel));
			obj.shaderVariantLogLevel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shaderVariantLogLevel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useSRPBatcher(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.useSRPBatcher = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useSRPBatcher on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_colorGradingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			UnityEngine.Rendering.Universal.ColorGradingMode arg0 = (UnityEngine.Rendering.Universal.ColorGradingMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.ColorGradingMode));
			obj.colorGradingMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_colorGradingLutSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.colorGradingLutSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index colorGradingLutSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_useAdaptivePerformance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.useAdaptivePerformance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index useAdaptivePerformance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_conservativeEnclosingSphere(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.conservativeEnclosingSphere = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index conservativeEnclosingSphere on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_numIterationsEnclosingSphere(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset obj = (UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.numIterationsEnclosingSphere = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index numIterationsEnclosingSphere on a nil value");
		}
	}
}

