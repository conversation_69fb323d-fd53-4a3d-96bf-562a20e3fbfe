GodPurchaseGiftShowView = GodPurchaseGiftShowView or BaseClass(SafeBaseView)

function GodPurchaseGiftShowView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/tianshen_purchase_ui_prefab", "layout_gift_show")
	self:SetMaskBg(true, true)
end

function GodPurchaseGiftShowView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
end

function GodPurchaseGiftShowView:ReleaseCallBack()
	if self.item_cell_list then
		self.item_cell_list:DeleteMe()
		self.item_cell_list = nil
	end

	self.data_list = nil
end

function GodPurchaseGiftShowView:InitParam()
	self.data_list = {}
end

function GodPurchaseGiftShowView:InitPanel()
	self.item_cell_list = AsyncListView.New(ItemCell, self.node_list.item_root)
	self.item_cell_list:SetStartZeroIndex(true)
end

function GodPurchaseGiftShowView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self.data_list = v.data_list
		end
	end
	self:RefreshView()
end

function GodPurchaseGiftShowView:RefreshView()
	self.item_cell_list:SetDataList(self.data_list)
end