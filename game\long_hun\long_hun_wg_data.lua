LongHunWGData = LongHunWGData or BaseClass()

function LongHunWGData:__init()
	if LongHunWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[LongHunWGData] attempt to create singleton twice!")
		return
	end
	LongHunWGData.Instance = self
	RemindManager.Instance:Register(RemindName.Compose_LongHun,BindTool.Bind(self.IsShowLongHunHeChengRedPoint, self))
	RemindManager.Instance:Register(RemindName.LongHun_XiangQian, BindTool.Bind(self.IsShowLongHunXiangQianRedPoint, self))

	local all_cfg = ConfigManager.Instance:GetAutoConfig("lifesoul_auto")

	self.list_map_base_cfg = all_cfg.base
	self.other_cfg = all_cfg.other[1]
	self.posy_level_cfg = all_cfg.level
	self.posy_special_cfg = all_cfg.special_slot
	self.posy_normal_cfg = all_cfg.slot
	self.posy_compose_father_list = all_cfg.label
	self.posy_compose_cfg = ListToMapList(all_cfg.compose,"compose_type","get_id")
	self.posy_compose_cfg_id = ListToMap(all_cfg.compose,"get_id")
	self.posy_limit_cfg = all_cfg.limit
	self.posy_attr_cfg = all_cfg.attr
	self.show_way_cfg = all_cfg.show_way
	self.big_cell_num = 5
	self.grid_list = {}
	self.all_grid_num = 0
	self.all_slot_num = 7
	self.normal_slot_num = 6
	self.bag_max_num = 300
	self.all_item_num_list = {}
	self.same_longhun_type_tab = {}
	self.base_slot = {}
	self.special_slot = {}
	self.fenjie_index_select = {}
	self.fenjie_color_select = {
		[1] = true,
		[2] = true,
		[3] = false,
		[4] = false,
		[5] = false,
	}

	self.fenjie_select_num = 0
	self.fenjie_exp = 0
	self.fenjie_xinjing = 0
	self.fenjie_auto = 1
	self.never_remind_red = 0
	self.fun_open_event = BindTool.Bind(self.FunOpenReBindRed, self)
	FunOpen.Instance:NotifyFunOpen(self.fun_open_event)
end

function LongHunWGData:__delete()
	-- RemindManager.Instance:UnRegister(RemindName.LongHun_HeCheng)
	RemindManager.Instance:UnRegister(RemindName.LongHun_XiangQian)
	RemindManager.Instance:UnRegister(RemindName.Compose_LongHun)
	-- RemindManager.Instance:UnRegister(RemindName.LongHun_FenJie)
	-- RemindManager.Instance:UnRegister(RemindName.LongHun_DuiHuan)
	FunOpen.Instance:UnNotifyFunOpen(self.sundry_red_event)
	LongHunWGData.Instance = nil
end

--龙魂背包
function LongHunWGData:OnSCLifesoulBagInfo(protocol)
	self.all_grid_num = protocol.grid_count
	self.grid_list = protocol.grid_list
	local data = {}
	self.all_item_num_list = {}
	for k,v in pairs(self.grid_list) do
		if nil == self.all_item_num_list[v.item_id] then
			self.all_item_num_list[v.item_id] = {}
		end
		self.all_item_num_list[v.item_id][v.index] = v
	end
	
	self:InitComposeRemind()
	self:GetAllRedBagLongHunIndex()
	self:MakePosyListSort()
end

--龙魂背包改变
function LongHunWGData:OnSCLifesoulGridInfo(protocol)
	local change_list = protocol.grid_list
	local change_count = protocol.grid_count
	if nil == self.grid_list then
		--self.grid_list = change_list
		self:OnSCLifesoulBagInfo(protocol)
		return
	end

	for i=1, change_count do
		local is_new = true
		for k,v in pairs(self.grid_list) do
			if v.index == change_list[i].index then
				if change_list[i].type <= 0 then
					if nil == self.all_item_num_list[change_list[i].item_id] then
						self.all_item_num_list[change_list[i].item_id] = {}
					end
					self.all_item_num_list[v.item_id][v.index] = nil
					-- table.remove(self.grid_list,k)
					self.grid_list[k] = nil
					self.all_grid_num = self.all_grid_num -1
				else
					if nil == self.all_item_num_list[change_list[i].item_id] then
						self.all_item_num_list[change_list[i].item_id] = {}
					end
					--table.remove(self.all_item_num_list[v.item_id],v.index)
					self.all_item_num_list[v.item_id][v.index] = nil
					v.item_id = change_list[i].item_id
					v.level = change_list[i].level
					v.type = change_list[i].type
					v.quality = change_list[i].quality
					v.is_lock = change_list[i].is_lock
					if nil == self.all_item_num_list[v.item_id] then
						self.all_item_num_list[v.item_id] = {}
					end

					self.all_item_num_list[v.item_id][v.index] = v
				end
				is_new = false
				break
			end
		end

		if is_new then
			if nil == self.all_item_num_list[change_list[i].item_id] then
				self.all_item_num_list[change_list[i].item_id] = {}
			end
			self.all_item_num_list[change_list[i].item_id][change_list[i].index] = change_list[i]
			table.insert(self.grid_list,change_list[i])
			self.all_grid_num = self.all_grid_num + 1
		end
	end
	
	self:GetAllRedBagLongHunIndex()
	self:MakePosyListSort()
end

--龙魂基本信息
function LongHunWGData:OnSCLifesoulBaseInfo(protocol)
	if not self.jingyan then
		self.jingyan = protocol.jingyan
	else
		if protocol.jingyan > self.jingyan then
			local add_value = protocol.jingyan - self.jingyan
			local item_id1,item_id2 = self:GeiCostMoneyID()
			local cfg = ItemWGData.Instance:GetItemConfig(item_id1)
			if not IsEmptyTable(cfg) then
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.SysRemind.AddItem, ToColorStr(cfg.name, GET_TIP_ITEM_COLOR[cfg.color]), add_value))
			end
		end
		self.jingyan = protocol.jingyan
	end
	self.xinjing = protocol.xinjing
	self.base_slot = protocol.normal_slot
	self.special_slot = protocol.special_slot
	self:CacularEquipType()
	--GlobalEventSystem:Fire(OtherEventType.Ming_Wen_Change,"")
	self:GetAllRedBagLongHunIndex()
end

function LongHunWGData:GetLongHunMoney()
	return self.jingyan or 0, self.xinjing or 0
end

function LongHunWGData:MakePosyListSort()
	if self.all_grid_num >= 2 then
		local table1 = {}
		for k,v in pairs(self.grid_list) do
			v.show_batter = self:GetBagIndexHaveRed(v.index) and 1 or 0
			table.insert(table1,v)
		end
		table.sort(table1,SortTools.KeyUpperSorters("show_batter","quality","level","type","index"))
		self.grid_list = table1
	end
end

function LongHunWGData:GetAllPosyItem()
	return self.grid_list or {}
end

function LongHunWGData:GetPosyBagListData()
	local list_data = {}
	--local min_num = self.all_grid_num > 20 and self.all_grid_num or 20
	local show_num = 0
	for i=1, 8 do
		list_data[i] = {}
	end
	local num = 1
	for k,v in pairs(self.grid_list) do
		if v.type > 1 then
			show_num = show_num + 1
			local a = math.ceil(num / self.big_cell_num)
			local b = num % self.big_cell_num
			if nil == list_data[a] then
				list_data[a] = {}
			end
			if b == 0 then
				b = self.big_cell_num
			end
			list_data[a][b] = v
			num = num + 1
		end
	end
	return list_data,show_num
end

--计算装备槽不可穿戴属性类型（身上装备属性不能重复）特殊符文槽排除其他符文类型
function LongHunWGData:CacularEquipType()
	self.unwearalbe_table = {}

	for p = 1, self.all_slot_num do
		self.unwearalbe_table[p] = {}
	end

	for i =1 ,self.normal_slot_num do
		if self.base_slot[i].lifesoul and self.base_slot[i].lifesoul.type > 0 then
			local base_cfg = self.list_map_base_cfg[self.base_slot[i].lifesoul.item_id]
			local attr_type = string.split(base_cfg.attr_type,"|")
			for t = 1,self.normal_slot_num do
				if i ~= t then
					for k,v in pairs(attr_type) do
						self.unwearalbe_table[t][math.floor(tonumber(v)/100)] = true
					end
				end
			end
		end
	end
end

function LongHunWGData:GetEquipSlotIsOpen(slot_index)
	if slot_index > self.normal_slot_num then
		slot_index = slot_index - 1 - self.normal_slot_num
		for k,v in pairs(self.posy_special_cfg) do
			if slot_index == v.slot_index then
				return GameVoManager.Instance:GetMainRoleVo().level >= v.role_level,v.role_level
			end
		end
	else
		slot_index = slot_index - 1
		for k,v in pairs(self.posy_normal_cfg) do
			if slot_index == v.slot_index then
				return GameVoManager.Instance:GetMainRoleVo().level >= v.role_level,v.role_level
			end
		end
	end
end


function LongHunWGData:GetEquipPosyDataBySlot(slot_index)
	if slot_index <= self.normal_slot_num then
		return self.base_slot[slot_index] or {}
	elseif slot_index <= self.all_slot_num then
		return self.special_slot[slot_index - self.normal_slot_num] or {}
	end
	return {}
end


function LongHunWGData:GetMingWenBaseCfgByID(item_id)
	return self.list_map_base_cfg[item_id] or {}
end

function LongHunWGData:GetComposeFatherList()
	local father_type_list = {}
	local num = 0
	local level =  GameVoManager.Instance:GetMainRoleVo().level
	for k,v in pairs(self.posy_compose_father_list) do
		if level >= v.subitem_show_level then
			father_type_list[v.compose_type] = v
			num = num + 1
		end
	end
	return father_type_list, num
end

function LongHunWGData:GetComPoseCellList(compose_type)
	local all_cfg = self.posy_compose_cfg[compose_type] or {}
	local open_cfg = {}
	local level = GameVoManager.Instance:GetMainRoleVo().level
	if not IsEmptyTable(all_cfg) then
		for k,v in pairs(all_cfg) do
			if level >= v[1].level then
				table.insert(open_cfg,v)
			end
		end
	end
	return open_cfg
end

function LongHunWGData:GetPosyIsLimite(item_id)
	local base_cfg = self.list_map_base_cfg[item_id]
	if not base_cfg then
		return false , 0
	end

	local limit_cfg = self.posy_limit_cfg[base_cfg.type]
	if not limit_cfg then
		return false , 0
	end

	--local pass_level = self:GetPassLevel()
	--return limit_cfg.tianxiange_fb_pass_level > pass_level,limit_cfg.tianxiange_fb_pass_level
	return false ,0
end


function LongHunWGData:GetMingWenBagItemNum(item_id)
	if self.all_item_num_list and self.all_item_num_list[item_id] then
		local num = 0
		for k,v in pairs(self.all_item_num_list[item_id]) do
			num = num + 1
		end
		return num
	else
		return 0
	end
end

function LongHunWGData:GetEquipPosyAttrBySlot(data)
	local base_cfg = self.list_map_base_cfg[data.item_id]
	if not base_cfg or base_cfg.attr_type == 0 then
		return {},{},{}
	end

	local attr_type = string.split(base_cfg.attr_type,"|")
	local attr_value = {}
	local max_value = {}
	local add_value = {}
	if data.level and self.posy_attr_cfg[data.level] then
		for k,v in pairs(attr_type) do
			attr_value[k] = self.posy_attr_cfg[data.level]["attr"..v]
			max_value[k] = self.posy_attr_cfg[self:GetLongHunMaxLevel()]["attr"..v]
			if self.posy_attr_cfg[data.level + 1] then
				add_value[k] = self.posy_attr_cfg[data.level + 1]["attr"..v]
			end
			attr_type[k] = math.floor(tonumber(attr_type[k]) / 100)
		end
	else
		return {},{},{}
	end

	return attr_type,attr_value,add_value,max_value
end


-- 获得属性名字
function LongHunWGData:GetAttributeNameListByType(attr_type_list, show_type)
	local name_list = {}
	local is_pre = {}
	if not IsEmptyTable(attr_type_list) then
		for k,v in pairs(attr_type_list) do
			local attr_type = tonumber(v)
			name_list[k] = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_type, show_type)
			is_pre[k] = EquipmentWGData.Instance:GetAttrIsPerByAttrId(attr_type)
		end
	end
	return name_list,is_pre
end

function LongHunWGData:GetMingWenBagItemByID(item_id)
	return self.all_item_num_list[item_id] or {}
end

function LongHunWGData:GetMingWenLvCfgByLv(level)
	return self:GetDeComposeCfgByLV(level)
end


function LongHunWGData:GetMingWenFenJieGet(data)
	local e_base_cfg = self:GetMingWenBaseCfgByID(data.item_id)
	local cost_type =  e_base_cfg.cost_type
	local level_cfg = self:GetMingWenLvCfgByLv(data.level)
	local level_cfg2 = self:GetMingWenLvCfgByLv(1)
	local new_get_jingyan = 0
	if level_cfg["get_jingyan_"..cost_type] then
		new_get_jingyan = level_cfg["get_jingyan_"..cost_type] - level_cfg2["get_jingyan_"..cost_type]
	end
	local new_get_mingyin = 0
	if level_cfg["get_mingyin_"..cost_type] then
		new_get_mingyin = level_cfg["get_mingyin_"..cost_type] - level_cfg2["get_mingyin_"..cost_type]
	end

	return new_get_jingyan, new_get_mingyin
end

function LongHunWGData:CacularComposeLevel(cost_type,exp,minyin)
	local level = 1
	for k,v in pairs(self.posy_level_cfg) do
		if not v["cost_jingyan_"..cost_type] then-- or not v["cost_mingyin_"..cost_type] then
			return level
		end

		if v["cost_jingyan_"..cost_type] == 0 then
			return 1
		end

		if v["cost_jingyan_"..cost_type] <= exp and v["cost_jingyan_"..cost_type] ~= 0 then --and v["cost_mingyin_"..cost_type] <= minyin then
			exp = exp - v["cost_jingyan_"..cost_type]
			level = k + 1
		else
			return level
		end
	end
	return level
end

function LongHunWGData:GetNormalSlotNum()
	return self.normal_slot_num or 6
end


function LongHunWGData:GetMingWenItemCanHeCheng(item_id)
	if self.never_remind_red == 1 then
		return 0
	end

	if nil == item_id then
		return 0
	end

	local is_limite,condition = LongHunWGData.Instance:GetPosyIsLimite(item_id)

	if is_limite then
		return 0
	end

	local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(item_id)
	local compose_data = self.posy_compose_cfg_id[item_id]

	if IsEmptyTable(base_cfg) or IsEmptyTable(compose_data) then
		return 0
	else
		local final_is_enough = true
		if compose_data.stuff_id_vec ~= 0 then
			local have_num = ItemWGData.Instance:GetItemNumInBagById(compose_data.stuff_id_vec)
			final_is_enough = have_num >= compose_data.stuff_num_vec
		end
		if not final_is_enough then
			return 0
		end
		local posy_id_list = string.split(compose_data.lifesoul_id_vec,"|")
		local posy_num_list = string.split(compose_data.lifesoul_num_vec,"|")
		local need_double = false
		local lifesoul_id_vec = {}
		local lifesoul_num_vec = {}

		for k,v in pairs(posy_id_list) do
			lifesoul_id_vec[k] = tonumber(v)
		end

		for t,q in pairs(posy_num_list) do
			lifesoul_num_vec[t] = tonumber(q)
		end

		if lifesoul_id_vec[2] then
			need_double = true
		end

		local equip_posy = {}
		
		for i=1,7 do
			local solt_data = LongHunWGData.Instance:GetEquipPosyDataBySlot(i)
			if not IsEmptyTable(solt_data) then
				if lifesoul_id_vec[1] == solt_data.lifesoul.item_id and lifesoul_id_vec[1] ~= -1 then
					equip_posy[1] = true
				elseif lifesoul_id_vec[2] and lifesoul_id_vec[2] == solt_data.lifesoul.item_id and lifesoul_id_vec[2] ~= -1 then
					equip_posy[2] = true
				end
			end
		end

		for i =1 ,2 do
			if (i == 1 or need_double) and lifesoul_id_vec[i] and lifesoul_id_vec[i] ~= -1 then
				local num = LongHunWGData.Instance:GetMingWenBagItemNum(lifesoul_id_vec[i])
				if equip_posy[i] then
					num = num +1
				end
				local is_enough1 = num >= lifesoul_num_vec[i]
				if not is_enough1 then
					final_is_enough = false
				end
			end
		end

		local same_type2 = nil
		if self.same_longhun_type_tab ~= nil then
			same_type2 = self.same_longhun_type_tab[base_cfg.type]
		end

		if self.best_type_list and self.best_type_list[base_cfg.type] then
			if self.best_type_list[base_cfg.type].quality >= base_cfg.quality then
				return 0
			end

			if same_type2 then
				if self.best_type_list[base_cfg.type].quality >= base_cfg.quality then
					return 0
				end
			end
		end


		for i=1,7 do
			local solt_data = LongHunWGData.Instance:GetEquipPosyDataBySlot(i)
			if (not IsEmptyTable(solt_data) and solt_data.lifesoul.type == base_cfg.type) or (same_type2 and same_type2 == solt_data.lifesoul.type) then
				if solt_data.lifesoul.quality >= base_cfg.quality then
					return 0
				end
			end
		end

		if final_is_enough then
			return 1
		end
	end
end

function LongHunWGData:LongHunCanUpGrade(slot_index)
	--if slot_index > self.normal_slot_num then
	--	return 0,0,0
	--end
	local equip_data = self:GetEquipPosyDataBySlot(slot_index)
	if IsEmptyTable(equip_data) or equip_data.lifesoul.type <= 0 then
		return 0,0,0
	end
	local base_cfg = self.list_map_base_cfg[equip_data.lifesoul.item_id]
	local next_need = self.posy_level_cfg[equip_data.lifesoul.level]["cost_jingyan_"..base_cfg.cost_type]
	local can_upgrade = self.jingyan >= next_need and 1 or 0
	local is_max = 0
	if next_need == 0 then
		is_max = 1
		can_upgrade = 0
	end
	return can_upgrade,next_need,is_max
end

function LongHunWGData:IsShowMainViewHeChengRedPoint()

	return 0
end

function LongHunWGData:IsShowLongHunHeChengRedPoint() --合成红点
	if self.never_remind_red == 1 then return 0 end
	for k,v in pairs(self.posy_compose_cfg_id) do
		if self:GetMingWenItemCanHeCheng(k) == 1 then
			return 1
		end
	end
	return 0
end

function LongHunWGData:IsShowLongHunXiangQianRedPoint() --镶嵌红点
	if not FunOpen.Instance:GetFunIsOpened(FunName.LongHunView) then
		return 0
	end
	for i=1,7 do
		if self:GetSlotRedByIndex(i) == 1 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LongHun_XianQian, 1, function()
    			ViewManager.Instance:Open(GuideModuleName.LongHunView,TabIndex.long_hun_xiang_qian)
    		end)
			return 1
		end
	end

	if self:IsShowLongHunFenJieRedPoint() == 1 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LongHun_XianQian, 1, function()
    		ViewManager.Instance:Open(GuideModuleName.LongHunView,TabIndex.long_hun_xiang_qian)
    	end)
		return 1
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LongHun_XianQian, 0, function()
    	ViewManager.Instance:Open(GuideModuleName.LongHunView,TabIndex.long_hun_xiang_qian)
    end)
	return 0
end

function LongHunWGData:IsShowLongHunFenJieRedPoint() --分解红点
	if not self.grid_list or IsEmptyTable(self.grid_list) then
		return 0
	end
	--策划需求 龙魂有可分解的材料加个红点提示吧，根据玩家默认勾选的品质来筛选，满足条件就给红点提示
	local select_color = self:GetSelectingColor()
	for k,v in pairs(self.grid_list) do
		if v.type == 1 then
			return 1
		end
		if select_color[v.quality] then
			return 1
		end
	end
	return 0
end

function LongHunWGData:IsShowLongHunDuiHuanRedPoint() -- 兑换红点

	return 0
end

function LongHunWGData:GetAllFightPower()
	return 0
end

function LongHunWGData:MakeSelectTable()
	-- body
end

function LongHunWGData:InitFenJieSelectColor()
	for i = 1, 5 do
		if PlayerPrefsUtil.HasKey("longhun_fenjie_color"..i) then
			self.fenjie_color_select[i] = PlayerPrefsUtil.GetInt("longhun_fenjie_color"..i) == 1 
		else
			PlayerPrefsUtil.SetInt("longhun_fenjie_color"..i,self.fenjie_color_select[i] and 1 or 0)
		end
	end
	--self:FenJieSelectColorItem()

end

function LongHunWGData:FenJieSelectColorItem()
	self.fenjie_index_select = {}
	self.fenjie_select_num = 0
	self.fenjie_exp = 0
	self.fenjie_xinjing = 0
	local is_best = {}

	-- for i=1,7 do
	-- 	local equip_data = self:GetEquipPosyDataBySlot(i)
	-- 	is_best[equip_data.lifesoul.type] = {}
	-- 	is_best[equip_data.lifesoul.type].quality = equip_data.lifesoul.quality
	-- 	is_best[equip_data.lifesoul.type].level = equip_data.lifesoul.level
	-- 	is_best[equip_data.lifesoul.type].is_lock = 0
	-- end

	for k,v in pairs(self.grid_list) do
		if v and v.type > 0 then
			if v.type >= 1 then --and not is_best[v.type] then
			-- 	is_best[v.type] =  {}
			-- 	is_best[v.type].is_lock = 1
			-- elseif v.type > 1 and is_best[v.type].is_lock == 0 and
			-- 	(v.quality > is_best[v.type].quality or (v.quality == is_best[v.type].quality and v.level > is_best[v.type].level)) then
			-- 	is_best[v.type].is_lock = 1
			-- else
				self.fenjie_index_select[v.index] = {}
				self.fenjie_index_select[v.index].index = v.index
				self.fenjie_index_select[v.index].quality = v.quality
				local cost_type = self.list_map_base_cfg[v.item_id].cost_type or 0
				local attr_type = self.list_map_base_cfg[v.item_id].attr_type or 0
				local compose_data = self.posy_compose_cfg_id[v.item_id]
				self.fenjie_index_select[v.index].get_xinjing = 0
				if not IsEmptyTable(compose_data) then
					if compose_data.decompose_show == 1 then
						self.fenjie_index_select[v.index].get_xinjing = compose_data.get_stuff_num_vec
					end
				end
				self.fenjie_index_select[v.index].cost_type = cost_type
				self.fenjie_index_select[v.index].get_jingyan = self.posy_level_cfg[v.level]["get_jingyan_"..cost_type]

				local suipian_flag = self.list_map_base_cfg[v.item_id].multiple_attr or 0
				local is_suipian_flag = suipian_flag < 0 --策划需求:龙魂碎片不需要区分品质选中
				if is_suipian_flag or (self.fenjie_color_select[v.quality] and v.is_lock ~= 1) then--and not string.find(attr_type, "|") then
					self.fenjie_index_select[v.index].select = 1
					self.fenjie_select_num = self.fenjie_select_num + 1
					self.fenjie_exp = self.fenjie_exp + self.fenjie_index_select[v.index].get_jingyan
					self.fenjie_xinjing = self.fenjie_xinjing + self.fenjie_index_select[v.index].get_xinjing
				else
					self.fenjie_index_select[v.index].select = 0
				end
			end
		end
	end
end

function LongHunWGData:SelectFenJieItem(index)
	if self.fenjie_index_select[index] then
		if self.fenjie_index_select[index].select == 0 then
			self.fenjie_select_num = self.fenjie_select_num + 1
			self.fenjie_index_select[index].select = 1
			self.fenjie_exp = self.fenjie_exp + self.fenjie_index_select[index].get_jingyan
			self.fenjie_xinjing = self.fenjie_xinjing + self.fenjie_index_select[index].get_xinjing
		else
			self.fenjie_select_num = self.fenjie_select_num - 1
			self.fenjie_index_select[index].select = 0
			self.fenjie_exp = self.fenjie_exp - self.fenjie_index_select[index].get_jingyan
			self.fenjie_xinjing = self.fenjie_xinjing - self.fenjie_index_select[index].get_xinjing
		end
	end
end

function LongHunWGData:GetFenJieItemSelect(index)
	if self.fenjie_index_select[index] then
		return self.fenjie_index_select[index].select
	else
		return 0
	end
end

function LongHunWGData:ChangeColorSelect(color)
	self.fenjie_color_select[color] = not self.fenjie_color_select[color]

	PlayerPrefsUtil.SetInt("longhun_fenjie_color"..color,self.fenjie_color_select[color] and 1 or 0)
	self:FenJieSelectColorItem()
	RemindManager.Instance:Fire(RemindName.LongHun_XiangQian)
	ViewManager.Instance:FlushView(GuideModuleName.LongHunView,TabIndex.long_hun_xiang_qian,"fenjie_red")
end

function LongHunWGData:GetSelectingColor()
	return self.fenjie_color_select
end

function LongHunWGData:GetFenJieGetMoney()
	return self.fenjie_exp, self.fenjie_xinjing
end

function LongHunWGData:GetAllSelectIndex()
	if self.fenjie_select_num == 0 then
		return 0,{}
	end
	local index_tab = {}
	local index = 0
	for k,v in pairs(self.fenjie_index_select) do
		if v.select == 1 then
			index = index + 1
			index_tab[index] = v.index
		end
	end
	return self.fenjie_select_num , index_tab
end

function LongHunWGData:InitFenJieMark()
	if PlayerPrefsUtil.HasKey("longhun_fenjie_auto") then
		self.fenjie_auto = PlayerPrefsUtil.GetInt("longhun_fenjie_auto")
	else
		PlayerPrefsUtil.SetInt("longhun_fenjie_auto",self.fenjie_auto)
	end
	if self.fenjie_auto == 1 and self.all_grid_num > self.bag_max_num then
		self:InitFenJieSelectColor()
		local count,index_list = self:GetAllSelectIndex()
		if count > 0 then
			LongHunWGCtrl.Instance:SendLongHunDeCompose(count, index_list)
		end
	end
end

function LongHunWGData:SetFenJieMark()
	self.fenjie_auto = self.fenjie_auto == 1 and 0 or 1
	PlayerPrefsUtil.SetInt("longhun_fenjie_auto",self.fenjie_auto)
end

function LongHunWGData:GetFenJieMark()
	return self.fenjie_auto or 0
end

function LongHunWGData:GetItemComposeData(item_id)
	return self.posy_compose_cfg_id[item_id] or {}
end

function LongHunWGData:GetDeComposeCfgByLV(level)
	return self.posy_level_cfg[level] or {}
end

function LongHunWGData:GetOpenLineTable()
	local line_list = {}
	for k,v in pairs(self.posy_normal_cfg) do
		local line_group = string.split(v.line_open,"|")
		for t,q in pairs(line_group) do
			if GameVoManager.Instance:GetMainRoleVo().level >= v.role_level then
				line_list[tonumber(q)] = true
			else
				line_list[tonumber(q)] = false
			end
		end
	end
	return line_list
end

function LongHunWGData:GetShowWayCfg()
	return self.show_way_cfg
end

function LongHunWGData:GetAllEquipAttr()
	local attr_name_table = {}
	local attr_num_table = {}
	local attr_is_pre = {}
	for i = 1,self.all_slot_num do
		local equip_data = self:GetEquipPosyDataBySlot(i)
		local attr_type,attr_value,add_value = self:GetEquipPosyAttrBySlot(equip_data.lifesoul)
		local name_list,is_pre = self:GetAttributeNameListByType(attr_type,true)
		for k, v in pairs(attr_type) do
			if nil == attr_name_table[v] then
				attr_name_table[v] = name_list[k]
				attr_num_table[v] = attr_value[k]
				attr_is_pre[v] = is_pre[k]

			else
				attr_num_table[v] = tonumber(attr_num_table[v]) + tonumber(attr_value[k])
			end

		end
	end
	return attr_name_table,attr_num_table,attr_is_pre
end

-- -1无法替换。 1-7 slot_index
function LongHunWGData:JudjementReallyPutIndex(data,target_index)
	local finally_index = -1 --target_index
	local finally_index2 = -1
	local hight_light_list = {}
	local is_special = self:IsSpecialPosyType(data.type)
	if is_special then
		local equip_data = self:GetEquipPosyDataBySlot(7)
		hight_light_list[1] = 7

		if equip_data.lifesoul.type < 1 then  --空槽
			return 1,hight_light_list,1
		end

		if data.type == equip_data.lifesoul.type then
			if data.quality > equip_data.lifesoul.quality then  --同样的铭文品质更高
				return 1,hight_light_list,1
			else
				return 1,hight_light_list,0
			end
		end
		return 1,hight_light_list,0
	end
	local hight_light_num = 0
	for i = 1, 6 do
		local equip_data = self:GetEquipPosyDataBySlot(i)
		local is_open = self:GetEquipSlotIsOpen(i)
		local is_conflict = false
		if is_open then
			if equip_data.lifesoul.type > 0 and equip_data.lifesoul.type == data.type then
				hight_light_num = hight_light_num + 1
				hight_light_list[hight_light_num] = i
				if data.quality > equip_data.lifesoul.quality then
					return hight_light_num,hight_light_list, 1
				else
					return hight_light_num,hight_light_list,0
				end
			else
				local base_cfg = self.list_map_base_cfg[data.item_id]
				--local base_cfg2 = self.list_map_base_cfg[equip_data.lifesoul.item_id]
				local attr_type1 = string.split(base_cfg.attr_type,"|")
				--local attr_type2 = string.split(base_cfg2.attr_type,"|")
				is_conflict = false
				for t,q in pairs(attr_type1) do
					if self.unwearalbe_table[i] and self.unwearalbe_table[i][math.floor(tonumber(q)/100)] then
						is_conflict = true
						break
					end
				end
				if not is_conflict then
					hight_light_num = hight_light_num + 1
					hight_light_list[hight_light_num] = i

					if equip_data.lifesoul.type < 1 then --空槽
						local hight_light_list2 = {}
						hight_light_list2[1] = i
						return 1,hight_light_list2,1
					end
				end
			end
		end
	end
	return hight_light_num,hight_light_list,0
end


function LongHunWGData:IsSpecialPosyType(posy_type)
	if nil == self.special_type_table then
		self.special_type_table = {}
		for k,v in pairs(self.posy_special_cfg) do
			local all_type = string.split(v.put_type,"|")
			for t,q in pairs(all_type) do
				self.special_type_table[tonumber(q)] = true
			end
		end
	end

	if self.special_type_table[posy_type] then
		return true
	end

	return false
end

function LongHunWGData:GeiCostMoneyID()
	return self.other_cfg.cost_item1 or 0,self.other_cfg.cost_item2 or 0, self.other_cfg.cost_item3 or 0
end

function LongHunWGData:GetComposeRemind()
	return self.never_remind_red
end

function LongHunWGData:ChangeComposeRemind()
	self.never_remind_red = self.never_remind_red == 1 and 0 or 1
	PlayerPrefsUtil.SetInt("longhun_compose_red",self.never_remind_red)
	RemindManager.Instance:Fire(RemindName.Compose_LongHun)
end

function LongHunWGData:InitComposeRemind()
	if PlayerPrefsUtil.HasKey("longhun_compose_red") then
		self.never_remind_red = PlayerPrefsUtil.GetInt("longhun_compose_red")
	else
		PlayerPrefsUtil.SetInt("longhun_compose_red",self.never_remind_red)
	end
end

function LongHunWGData:IsLongHunCoseItem(item_id)
	for k,v in pairs(self.other_cfg) do
		if v == item_id then
			return true
		end
	end
	return false
end

function LongHunWGData:GetAllDeComposeData()
	self.fenjie_exp = 0
	self.fenjie_xinjing = 0
	local decompose_data = {}
	for k,v in pairs(self.grid_list) do
		if self.fenjie_index_select[v.index] then
			if self.fenjie_index_select[v.index].select == 1 then
				self.fenjie_exp = self.fenjie_exp + self.fenjie_index_select[v.index].get_jingyan
				self.fenjie_xinjing = self.fenjie_xinjing +  self.fenjie_index_select[v.index].get_xinjing
			end
		end
		v.sort_index = self:GetDeComposeSortIndex(v.item_id)
		table.insert(decompose_data,v)
	end
	table.sort(decompose_data,SortTools.KeyUpperSorters("sort_index"))
	return decompose_data
end

function LongHunWGData:GetDeComposeSortIndex(item_id)
	if not self.decompose_sort_tab then
		self.decompose_sort_tab = {}
		local sort_index = 0
		for k,v in pairs(self.list_map_base_cfg) do
			sort_index = 0
			sort_index = sort_index + (10 - v.quality) * 100
			if v.type == 1 then
				sort_index = sort_index + 1000
			end
			self.decompose_sort_tab[v.id] = sort_index
		end
	end
	return self.decompose_sort_tab[item_id]
end

function LongHunWGData:GetSlotRedByIndex(slot_index)
	if not self:GetEquipSlotIsOpen(slot_index) then
		return 0
	end
	local equip_data = self:GetEquipPosyDataBySlot(slot_index)
	--if equip_data.lifesoul.type <= 0 then
		if slot_index > self.normal_slot_num then
			if self:GetSpecialLonghunRed(slot_index) == 1 then
				return 1
			end
		else
			if self:GetBaseBagPosy(slot_index) == 1 then
				return 1
			end
		end
	--end

	if self:LongHunCanUpGrade(slot_index) == 1 then
		return 1
	end

	return 0
end

function LongHunWGData:GetSlotRedByIndex2(slot_index) --镶嵌界面排除佩戴
	if not self:GetEquipSlotIsOpen(slot_index) then
		return 0
	end
	if self:LongHunCanUpGrade(slot_index) == 1 then
		return 1
	end

	return 0
end


------普通槽位是否有东西可以装
function LongHunWGData:GetBaseBagPosy(slot_index)
	for k,v in pairs(self.grid_list) do
		local base_cfg = self.list_map_base_cfg[v.item_id]
		if base_cfg and base_cfg.type > 1  and not self:IsSpecialPosyType(base_cfg.type) then
			local attr_type = string.split(base_cfg.attr_type,"|")
			local is_conflict = false
			for t,q in pairs(attr_type) do
				if self.unwearalbe_table[slot_index] and self.unwearalbe_table[slot_index][math.floor(tonumber(q)/100)] then
					is_conflict = true
					break
				end
			end

			if not is_conflict then
				local equip_data = self:GetEquipPosyDataBySlot(slot_index)
				if base_cfg.type > 1 and equip_data.lifesoul.type <= 0 then
					return 1
				end
				if base_cfg.type == equip_data.lifesoul.type then
					if equip_data.lifesoul.quality < v.quality then
						return 1
					elseif equip_data.lifesoul.quality == v.quality and equip_data.lifesoul.level < v.level then
						return 1
					end
				end
			end
		end
	end
	return 0
end

function LongHunWGData:GetSpecialLonghunRed(slot_index)
	local equip_data = self:GetEquipPosyDataBySlot(slot_index)

	for k,v in pairs(self.grid_list) do
		local base_cfg = self.list_map_base_cfg[v.item_id]
		if self:IsSpecialPosyType(base_cfg.type) then
			if equip_data.lifesoul.type > 0 then
				if base_cfg.type == equip_data.lifesoul.type then
					if equip_data.lifesoul.quality < v.quality then
						return 1
					elseif equip_data.lifesoul.quality == v.quality and equip_data.lifesoul.level < v.level then
						return 1
					end
				end
			else
				return 1
			end
		end
	end
	return 0
end

-- function LongHunWGData:CheckThisLongHunHaveRed(data)
-- 	if self:IsSpecialPosyType(data.type) then
-- 		local equip_data = self:GetEquipPosyDataBySlot(7)
-- 		if data.type == equip_data.lifesoul.type and data.type.quality >
-- 	else
-- 		for i= 1,6 do
-- 			local equip_data = self:GetEquipPosyDataBySlot(i)
-- 			if equip_data.lifesoul.type >0  and equip_data.lifesoul.type == data.type then
-- 				if equip_data.lifesoul.quality < data.quality then
-- 					return 1
-- 				elseif equip_data.lifesoul.quality == data.quality and equip_data.lifesoul.level < data.level then
-- 					return 1
-- 				end
-- 				return 0
-- 			end
-- 		end
-- 		return 0
-- 	end
-- end

function LongHunWGData:SelectAllBestLongHun()
	self.best_type_list = {}
	self.best_type_list_map = {}
	for k,v in pairs(self.grid_list) do
		if v.type ~= 1 then
			if nil == self.best_type_list[v.type] then
				self.best_type_list[v.type] = {}
				self.best_type_list[v.type].level = v.level
				self.best_type_list[v.type].quality = v.quality
				self.best_type_list[v.type].index = v.index
				self.best_type_list[v.type].item_id = v.item_id
				self.best_type_list_map[v.type] = {}
				self.best_type_list_map[v.type][v.index] = true
			elseif self.best_type_list[v.type].quality > v.quality then

			else
				if self.best_type_list[v.type].quality < v.quality
					or (self.best_type_list[v.type].quality == v.quality and self.best_type_list[v.type].level < v.level) then
					self.best_type_list[v.type].level = v.level
					self.best_type_list[v.type].quality = v.quality
					self.best_type_list[v.type].index = v.index
					self.best_type_list[v.type].item_id = v.item_id
				end
				if self.best_type_list[v.type].quality < v.quality then
					self.best_type_list_map[v.type] = {}
					self.best_type_list_map[v.type][v.index] = true
				elseif self.best_type_list[v.type].quality == v.quality then
					self.best_type_list_map[v.type][v.index] = true
				end
			end
		end
	end
end

function LongHunWGData:GetAllRedBagLongHunIndex()
	self:SelectAllBestLongHun()
	self.all_red_bag_longhun = {}
	if IsEmptyTable(self.best_type_list) then
		return
	end
	--type,v
	for k,v in pairs(self.best_type_list) do
		self.all_red_bag_longhun[v.index] = false
		local need_red = false
		if self:IsSpecialPosyType(k) then
			if LongHunWGData.Instance:GetEquipSlotIsOpen(7) then
				local equip_data = self:GetEquipPosyDataBySlot(7)
				if k == equip_data.lifesoul.type then
					if v.quality > equip_data.lifesoul.quality or (v.quality == equip_data.lifesoul.quality and v.level > equip_data.lifesoul.level) then
						need_red = true
					else
						need_red = false
					end
				elseif self:GetIsSameType(k,equip_data.lifesoul.type) then
					if v.quality > equip_data.lifesoul.quality 
						or (v.quality == equip_data.lifesoul.quality and self:IsJingShen(v.item_id)) then
						need_red = true
					else
						need_red = false
					end
				elseif equip_data.lifesoul.type > 0 then
					need_red = false
				else
					need_red = true
				end
			else
				need_red = false
			end
			for n,m in pairs(self.best_type_list_map[k]) do
				self.all_red_bag_longhun[n] = need_red
			end
		else
			local need_red2 = false
			for i = 1, 6 do
				local equip_data = self:GetEquipPosyDataBySlot(i)
				if LongHunWGData.Instance:GetEquipSlotIsOpen(i) then
					if k == equip_data.lifesoul.type then
						if v.quality > equip_data.lifesoul.quality or (v.quality == equip_data.lifesoul.quality and v.level > equip_data.lifesoul.level) then
							need_red2 = true
						else
							need_red2 = false
						end
						break
					elseif self:GetIsSameType(k,equip_data.lifesoul.type) then
						if v.quality > equip_data.lifesoul.quality 
							or (v.quality == equip_data.lifesoul.quality and self:IsJingShen(v.item_id)) then
							need_red2 = true
						else
							need_red2 = false
						end
						break 
					elseif equip_data.lifesoul.type <= 1 then --空槽
						local base_cfg = self.list_map_base_cfg[v.item_id]
						local attr_type = string.split(base_cfg.attr_type,"|")
						local is_conflict = false
						for t,q in pairs(attr_type) do
							if self.unwearalbe_table[i] and self.unwearalbe_table[i][math.floor(tonumber(q)/100)] then
								is_conflict = true
								break
							end
						end
						need_red2 = not is_conflict
					end
				end
			end

			for n,m in pairs(self.best_type_list_map[k]) do
				self.all_red_bag_longhun[n] = need_red2
			end

		end
	end
end

function LongHunWGData:GetBagIndexHaveRed(index)
	if self.all_red_bag_longhun then
		return self.all_red_bag_longhun[index] or false
	end
	return false
end

function LongHunWGData:SetFenJieStatus(value)
	self.is_playing_fenjie_ani = value
end

function LongHunWGData:GetIsPlayingFenJieAni()
	return self.is_playing_fenjie_ani or false
end

function LongHunWGData:SelectLongHunAndPut(slot_index) --空槽选择一个最合适可装的
	if self.all_red_bag_longhun then
		if not self:GetEquipSlotIsOpen(slot_index) then
			return -1
		end

		if IsEmptyTable(self.best_type_list) then
			return
		end

		local perfect_type = -1
		local attr_tab,perfect_cap
		perfect_cap = 0
		for k,v in pairs(self.best_type_list) do
			local base_cfg = self.list_map_base_cfg[v.item_id]
			if self.all_red_bag_longhun[v.index] then
				if slot_index > self.normal_slot_num then
					if self:IsSpecialPosyType(k) then
						if perfect_type == -1 then
							perfect_type = k
							local data = {item_id = v.item_id,level = 1}
							attr_tab,perfect_cap = self:GetEquipLongHunAttrListByItemId(data)
							--is_mul_attr = false--base_cfg.multiple_attr > 0 --双属性优先
						else
							local data = {item_id = v.item_id,level = 1}
							local _,cap2 = self:GetEquipLongHunAttrListByItemId(data)
							if cap2 > perfect_cap then
								perfect_type = k
								perfect_cap = cap2
							end

							-- if (is_mul_attr and base_cfg.multiple_attr > 0) or (not is_mul_attr and base_cfg.multiple_attr <= 0) then
							-- 	if v.quality > self.best_type_list[perfect_type].quality then
							-- 		perfect_type = k
							-- 		is_mul_attr = false-- base_cfg.multiple_attr > 0
							-- 	elseif v.quality == self.best_type_list[perfect_type].quality then
							-- 		if v.level > self.best_type_list[perfect_type].level then
							-- 			perfect_type = k
							-- 			is_mul_attr = false-- base_cfg.multiple_attr > 0
							-- 		elseif v.level == self.best_type_list[perfect_type].level then
							-- 			if v.item_id < self.best_type_list[perfect_type].item_id then
							-- 				perfect_type = k
							-- 				is_mul_attr = false-- base_cfg.multiple_attr > 0
							-- 			end
							-- 		end
							-- 	end
							-- elseif base_cfg.multiple_attr > 0 then
							-- 	perfect_type = k
							-- 	is_mul_attr = false-- base_cfg.multiple_attr > 0
							-- end
						end
					end
				else
					if not self:IsSpecialPosyType(k) then
						local attr_type = string.split(base_cfg.attr_type,"|")
						local is_conflict = false
						for t,q in pairs(attr_type) do
							if self.unwearalbe_table[slot_index] and self.unwearalbe_table[slot_index][math.floor(tonumber(q)/100)] then
								is_conflict = true
								break
							end
						end

						if not is_conflict then
							if perfect_type == -1 then
								perfect_type = k
								local data = {item_id = v.item_id,level = 1}
								attr_tab,perfect_cap = self:GetEquipLongHunAttrListByItemId(data)
							else
								local data = {item_id = v.item_id,level = 1}
								local _,cap2 = self:GetEquipLongHunAttrListByItemId(data)
								if cap2 > perfect_cap then
									perfect_type = k
									perfect_cap = cap2
								end
							end
						end
						-- if not is_conflict then
						-- 	if perfect_type == -1 then
						-- 		perfect_type = k
						-- 		is_mul_attr = false-- base_cfg.multiple_attr > 0 --双属性优先
						-- 	else
						-- 		if (is_mul_attr and base_cfg.multiple_attr > 0) or (not is_mul_attr and base_cfg.multiple_attr <= 0) then
						-- 			if v.quality > self.best_type_list[perfect_type].quality then
						-- 				perfect_type = k
						-- 				is_mul_attr = false-- base_cfg.multiple_attr > 0
						-- 			elseif v.quality == self.best_type_list[perfect_type].quality then
						-- 				if v.level > self.best_type_list[perfect_type].level then
						-- 					perfect_type = k
						-- 					is_mul_attr = false-- base_cfg.multiple_attr > 0
						-- 				elseif v.level == self.best_type_list[perfect_type].level then
						-- 					if v.item_id < self.best_type_list[perfect_type].item_id then
						-- 						perfect_type = k
						-- 						is_mul_attr = false-- base_cfg.multiple_attr > 0
						-- 					end
						-- 				end
						-- 			end
						-- 		elseif base_cfg.multiple_attr > 0 then
						-- 			perfect_type = k
						-- 			is_mul_attr = false-- base_cfg.multiple_attr > 0
						-- 		end
						-- 	end
						-- end
					end
				end
			end
		end

		local perfect_index = -1
		if perfect_type ~= -1 then
			perfect_index = self.best_type_list[perfect_type].index
		end
		return perfect_index
	else
		return -1
	end
end

function LongHunWGData:GetLongHunMaxLevel()
	if not self.max_level then
		self.max_level = #self.posy_level_cfg
	end
	return self.max_level
end

function LongHunWGData:GetEquipLongHunAttrListByItemId(data)
	local attr_list = {}
	data = data or {}
	local attr_id_list, attr_value_list, add_value_list,max_value = self:GetEquipPosyAttrBySlot(data)
	if IsEmptyTable(attr_id_list) or IsEmptyTable(attr_id_list) or IsEmptyTable(attr_id_list) then
		return attr_list
	end
	local now_level = data.level or 1
	local max_level = self:GetLongHunMaxLevel()
	local add_level = max_level - now_level

	local attr_name_list = {}
	for k, v in ipairs(attr_id_list) do
		local attr_id = tonumber(v)
		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
		local attr_value = tonumber(attr_value_list[k]) or 0
		local max_level_value = tonumber(max_value[k]) or 0
		if not attr_list[attr_str] then
			attr_list[attr_str] = {}
			attr_list[attr_str].value = attr_value
			attr_list[attr_str].max_level_value = max_level_value
		else
			attr_list[attr_str].value = attr_list[attr_str].value + attr_value
			attr_list[attr_str].max_level_value = attr_list[attr_str].max_level_value + max_level_value
		end
		attr_name_list[attr_str] = attr_list[attr_str].value
	end
	attr_name_list = AttributeMgr.GetAttributteByClass(attr_name_list)
	local cap = AttributeMgr.GetCapability(attr_name_list)
	return attr_list,cap
end

function LongHunWGData:SetLongHunBagSelectIndex(index)
	self.xiangqian_bag_select_bag = index
end

function LongHunWGData:GetLongHunBagSelectIndex()
	return self.xiangqian_bag_select_bag or -1
end

function LongHunWGData:GetIsSameType(type1,type2)
	if not self.same_longhun_type_tab then
		self.same_longhun_type_tab = {}
		local type_cfg1 = {}
		local type_cfg2 = {}
		for k,v in pairs(self.posy_compose_cfg_id) do
			if v.decompose_show == 0 then --晋升
				type_cfg1 = self.list_map_base_cfg[v.get_id] or {}
				type_cfg2 = self.list_map_base_cfg[v.lifesoul_id_vec] or {}
				if type_cfg1 and type_cfg1.type and type_cfg2 and type_cfg2.type then
					self.same_longhun_type_tab[type_cfg1.type] = type_cfg2.type
					self.same_longhun_type_tab[type_cfg2.type] = type_cfg1.type
				end
			end
		end
	end

	if self.same_longhun_type_tab[type1] and self.same_longhun_type_tab[type1] == type2 then
		return true
	end
	return false
end

function LongHunWGData:IsJingShen(item_id)
	if self.posy_compose_cfg_id[item_id] and self.posy_compose_cfg_id[item_id].decompose_show == 0 then
		return true
	end
	return false
end

function LongHunWGData:FunOpenReBindRed(fun_name)
	if fun_name == "LongHunView" then
		self:GetAllRedBagLongHunIndex()
		RemindManager.Instance:Fire(RemindName.LongHun_XiangQian)
		RemindManager.Instance:Fire(RemindName.Compose_LongHun)
	end
end