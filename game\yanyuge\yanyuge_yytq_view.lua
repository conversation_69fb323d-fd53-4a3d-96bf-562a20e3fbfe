function YanYuGePrivilegeView:YYTQLoadCallBack()
    if not self.yytq_privilege_list then
        self.yytq_privilege_list = AsyncListView.New(YYTQPrivilegeItemCellRender, self.node_list.yytq_privilege_list)
        self.yytq_privilege_list:SetStartZeroIndex(false)
    end

    self.tip_data_cache = {}
    XUI.AddClickEventListener(self.node_list.btn_diaochan_cyc, BindTool.Bind(self.OnClickDianChanCYCBtn, self))
end

function YanYuGePrivilegeView:YYTQShowIndexCallBack()
    self.cd_time = 0
    self:UpdateDiaoChanTip()
end

function YanYuGePrivilegeView:YYTQReleaseCallBack()
    if self.yytq_privilege_list then
        self.yytq_privilege_list:DeleteMe()
        self.yytq_privilege_list = nil
    end
end

function YanYuGePrivilegeView:YYTQOnFlush(param_t)
    local tequan_list = YanYuGeWGData.Instance:GetActiveSortTeQuanCfg()
    self.yytq_privilege_list:SetDataList(tequan_list)

    local is_get_all_privilege = YanYuGeWGData.Instance:IsGetAllYYTQ()
    local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTeQuanReward()
    self.node_list.btn_diaochan_cyc_remind:CustomSetActive(is_get_all_privilege and not is_get_tequan_reward)
end

function YanYuGePrivilegeView:UpdateDiaoChanTip()
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if self.cd_time <= server_time then
        local tip_data = YanYuGeWGData.Instance:GetSingleYYTQToolTip(self.tip_data_cache)
        self.tip_data_cache = tip_data
    
        if not IsEmptyTable(tip_data) then
            self.node_list.desc_diaochan_tip.tmp.text = tip_data.str
        end
        self.cd_time = server_time + 3
    else
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.YYTQCYCCdTime, math.ceil(self.cd_time - server_time)))
    end
end

function YanYuGePrivilegeView:OnClickDianChanCYCBtn()
    local is_get_all_privilege = YanYuGeWGData.Instance:IsGetAllYYTQ()
    local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTeQuanReward()

    if is_get_all_privilege and not is_get_tequan_reward then
        YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_ACTIVE_TEQUAN_REWARD)
    end

    self:UpdateDiaoChanTip()
end

----------------------------------YYTQPrivilegeItemCellRender------------------------------------
YYTQPrivilegeItemCellRender = YYTQPrivilegeItemCellRender or BaseClass(BaseRender)

function YYTQPrivilegeItemCellRender:LoadCallBack()
    if not self.reward_list_list then
        self.reward_list_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
        self.reward_list_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["btn_buy"],BindTool.Bind(self.OnClickBuy, self))
end

function YYTQPrivilegeItemCellRender:__delete()
    if self.reward_list_list then
        self.reward_list_list:DeleteMe()
        self.reward_list_list = nil
    end
end

function YYTQPrivilegeItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    
    local bundle, asset = ResPath.GetYanYuGeImg(self.data.title)
    self.node_list["title"].image:LoadSprite(bundle, asset, function()
        self.node_list["title"].image:SetNativeSize()
    end)
    
    local bundle, asset = ResPath.GetYanYuGeImg(self.data.icon)
    self.node_list["img_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["img_icon"].image:SetNativeSize()
    end)

    self.reward_list_list:SetDataList(self.data.day_reward_item)
    self.node_list.desc_fanli_tip.tmp.text = self.data.rebate_lable

    local is_get_privilege = YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.seq)
    local is_get_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(self.data.seq)
    local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTequanReward(self.data.seq)
    local is_has_day_reward = YanYuGeWGData.Instance:IsTequanHasDayReward(self.data.seq)

    -- self.node_list.flag_is_get:CustomSetActive(is_get_privilege and is_get_reward)
    -- self.node_list.btn_buy:CustomSetActive(not is_get_privilege or (is_get_privilege and not is_get_reward))
    -- self.node_list.btn_buy_remind:CustomSetActive(is_get_privilege and not is_get_reward)
    self.node_list.flag_is_get:CustomSetActive(is_get_privilege and is_get_tequan_reward and (not is_has_day_reward or (is_has_day_reward and is_get_reward)))
    self.node_list.btn_buy:CustomSetActive(not is_get_privilege or not is_get_tequan_reward or (is_has_day_reward and not is_get_reward))
    self.node_list.btn_buy_remind:CustomSetActive(is_get_privilege and (not is_get_reward or (is_has_day_reward and not is_get_reward)))

    if is_get_privilege then
        if not is_get_reward then
            self.node_list.desc_buy.tmp.text = Language.YanYuGe.TeQuanCanLingQuStr
        end
        self.node_list.desc_buy_tip.tmp.text = ""
        -- self.node_list.btn_buy_remind:CustomSetActive(false)
    else
        self.node_list.desc_buy_tip.tmp.text = self.data.reward_str
        local desc_buy_str = ""

        if self.data.last_seq >= 0 and not YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.last_seq) then 
            local tequan_cfg = YanYuGeWGData.Instance:GetTeQuanCfgBySeq(self.data.last_seq)
            desc_buy_str = string.format(Language.YanYuGe.TeQuanUnLockLastTipStr, tequan_cfg.name)
        else
            if self.data.buy_type == 1 then
                desc_buy_str = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
            elseif self.data.buy_type == 2 then
                desc_buy_str = string.format(Language.YanYuGe.ScoreStr, self.data.price)
            elseif self.data.buy_type == 3 then
                desc_buy_str = string.format(Language.YanYuGe.RealRechargeNumStr, RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq))
            end
        end
    
        self.node_list.desc_buy.tmp.text = desc_buy_str
        -- self.node_list.btn_buy_remind:CustomSetActive(self.data.buy_type == 2 and score >= self.data.price)
        -- local score = YanYuGeWGData.Instance:GetCurScore()
    end
end

function YYTQPrivilegeItemCellRender:OnClickBuy()
    if self.data.last_seq >= 0 then
        if not YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.last_seq) then
            local tequan_cfg = YanYuGeWGData.Instance:GetTeQuanCfgBySeq(self.data.last_seq)
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.TeQuanUnLockLastTipStr, tequan_cfg.name))
            return
        end
    end

    local is_get_privilege = YanYuGeWGData.Instance:IsTeQuanUnLockBySeq(self.data.seq)
    local is_get_privilege_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(self.data.seq)

    if not is_get_privilege then
        if self.data.buy_type == 1 then
            RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
        elseif self.data.buy_type == 2 then
            local score = YanYuGeWGData.Instance:GetCurScore()
            if score >= self.data.price then
                YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_TEQUAN, self.data.seq)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
            end
        elseif self.data.buy_type == 3 then
            local real_recharge_num = YanYuGeWGData.Instance:GetRealRechargeNum()

            if real_recharge_num < self.data.price then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.RealRechargeNumNotEnoughStr, RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)))
                ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
            end
        end
    else
        local is_get_tequan_reward = YanYuGeWGData.Instance:IsGetTequanReward(self.data.seq)
        if not is_get_tequan_reward then
            YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_TEQUAN_REWARD, self.data.seq)
        else
            local is_has_day_reward = YanYuGeWGData.Instance:IsTequanHasDayReward(self.data.seq)
            local is_get_privilege_reward = YanYuGeWGData.Instance:IsGetTeQuanEveryDayReward(self.data.seq)
            if is_has_day_reward and not is_get_privilege_reward then
                YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, self.data.seq)
            end
        end
        -- if not is_get_privilege_reward then
        --     YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.FETCH_EVERY_DAY_REWARD, self.data.seq)
        -- end
    end

    -- if self.data.buy_type == 1 then
    --     RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    -- else
    --     local score = YanYuGeWGData.Instance:GetCurScore()
    --     if score >= self.data.price then
    --         YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.BUY_TEQUAN, self.data.seq)
    --     else
    --         SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
    --     end
    -- end
end