
function DuckRaceView:ResultsLoadCallBack()
	self.results_round_obj_list = {}
	self.node_list["results_prefab"]:SetActive(false)
end

function DuckRaceView:ResultsReleaseCallBack()
	if self.results_round_obj_list then
		for k,v in ipairs(self.results_round_obj_list) do
			v:DeleteMe()
		end
		self.results_round_obj_list = {}
	end
end


function DuckRaceView:ResultsOnFlush(param_list)
	for k,v in pairs(self.results_round_obj_list) do
		v:SetActive(false)
	end
	for round_index = 1, DuckRaceWGData.CROSS_RACING_ROUND_COUNT do
		if self.results_round_obj_list[round_index] == nil then
			local go = ResMgr:Instantiate(self.node_list["results_prefab"].gameObject)
			go:SetActive(true)
			go.transform:SetParent(self.node_list["results_" .. round_index].transform, false)
			self.results_round_obj_list[round_index] = ReusltsRoundItem.New(go)
		end
		local obj = self.results_round_obj_list[round_index]
		obj:SetActive(true)
		obj:SetIndex(round_index)
		obj:Flush()
	end
end


---------------------------------------------------------------------------------
-- 每轮结算信息
ReusltsRoundItem = ReusltsRoundItem or BaseClass(BaseGridRender)
function ReusltsRoundItem:__init()
	if not self.duck_model then
		self.duck_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["duck_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = true,
		}

		self.duck_model:SetRenderTexUI3DModel(display_data)
		-- self.duck_model:SetUI3DModel(self.node_list["duck_model"].transform, self.node_list["duck_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end
end

function ReusltsRoundItem:__delete()
	if self.duck_model then
		self.duck_model:DeleteMe()
		self.duck_model = nil
	end
end

function ReusltsRoundItem:OnFlush()
	local round = self.index
	local winner_duck_info = DuckRaceWGData.Instance:GetRoundDuckWinner(round) 								-- 胜利的鸭子信息
	self.node_list["has_winner_panel"]:SetActive(winner_duck_info ~= nil)
	self.node_list["no_winner_panel"]:SetActive(winner_duck_info == nil)
	self.node_list["winner_desc"].text.text = string.format(Language.DuckRace.WinnerDesc, Language.Common.UpNum[round])
	if winner_duck_info then
		local duck_cfg = DuckRaceWGData.Instance:GetDuckCfg(winner_duck_info.duck_id) 						-- 根据鸭子id获得鸭子配置
		local monster_cfg = BossWGData.Instance:GetMonsterInfo(DuckRaceWGData.Instance:GetDuckMonsterId(winner_duck_info.index))
		self.node_list["duck_index"].image:LoadSprite(ResPath.GetDuckRaceImg("a2_naem_bg_" .. winner_duck_info.index)) 					-- 鸭子序号
		self.node_list["duck_name"].text.text = string.format(Language.DuckRace.DuckName[winner_duck_info.index], monster_cfg.name)		-- 鸭子名称
		self.node_list["tili_value"].text.text = duck_cfg.tili 												-- 鸭子体力值
		self.node_list["xingfen_value"].text.text = duck_cfg.xingfen 										-- 鸭子兴奋值
		self.node_list["my_reward_gold"].text.text = DuckRaceWGData.Instance:GetRoundRewardCoin(round) 		-- 我获得的奖金
		self.node_list["my_bet_value"].text.text = DuckRaceWGData.Instance:GetMyBetCountByDuckIndex(winner_duck_info.index, round) -- 本人对当前鸭子的下注数目
		local bundle, asset = ResPath.GetMonsterModel(monster_cfg.resid)
		self.duck_model:SetMainAsset(bundle, asset)
		--self.duck_model:PlayMonsterAction(false)
	end

end
