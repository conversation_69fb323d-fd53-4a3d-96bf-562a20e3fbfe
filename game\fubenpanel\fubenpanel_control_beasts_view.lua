function FuBenPanelView:InitControlBeastsView()
	if not self.beasts_reward_list then
		self.beasts_reward_list = AsyncListView.New(FuBenBeastsRewardCell, self.node_list["beasts_reward_list"])
		self.beasts_reward_list:SetStartZeroIndex(true)
	end

	if not self.beasts_leader_reward_list then
		self.beasts_leader_reward_list = AsyncListView.New(FuBenBeastsRewardCell, self.node_list["beasts_leader_reward_list"])
		self.beasts_leader_reward_list:SetStartZeroIndex(true)
	end

	self.node_list["layout_beasts_combine_mark"].button:AddClickListener(BindTool.Bind1(self.OnClickControlBeastsCombine, self))
	self.node_list["btn_beasts_enter"].button:AddClickListener(BindTool.Bind1(self.OnClickControlBeastsBaoMingEnter,self))
end

function FuBenPanelView:DeleteControlBeastsView()
	if self.beasts_reward_list then
		self.beasts_reward_list:DeleteMe()
		self.beasts_reward_list = nil
	end

	if self.beasts_leader_reward_list then
		self.beasts_leader_reward_list:DeleteMe()
		self.beasts_leader_reward_list = nil
	end
end

function FuBenPanelView:FlushControlBeastsView()
    local cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenTypeModelCfg(GoalTeamType.FbControlBeastsType, 0)
	if not cfg then
		return 
	end

    self.node_list.desc_control_beasts_ads.text.text = cfg.title_desc
    self.node_list.beasts_fb_desc.text.text = cfg.fb_desc

    for i = 1, 5 do
		if Language.FuBenPanel.ControlBeastsFuBenShuoming[i] then
			self.node_list["beasts_fb_info_sign"..i]:SetActive(true)
			self.node_list["beasts_fb_info"..i].text.text = Language.FuBenPanel.ControlBeastsFuBenShuoming[i]
		else
			self.node_list["beasts_fb_info_sign"..i]:SetActive(false)
		end
	end

	self.beasts_reward_list:SetDataList(cfg.show_reward_item)
	self.beasts_leader_reward_list:SetDataList(cfg.leader_show_reward_item)
	self:FlushControlBeastsEnterCount()
end

function FuBenPanelView:FlushControlBeastsEnterCount()
    if self.node_list["beasts_enter_count"] then
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
		local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)
		local max_count = team_type_cfg.max_times or 0
		local remain_times = max_count - enter_times
		self.node_list["beasts_enter_count"].text.color = Str2C3b(remain_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.L_RED)
        self.node_list["beasts_enter_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, max_count)
    end

	
    local hook_type = FuBenWGData.Instance:GetCombineStatus(FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1)
    self.node_list.layout_beasts_combine_hook:SetActive(hook_type == 1)
end

function FuBenPanelView:OnClickControlBeastsCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1 + 1].level_limit

    if need_level >= role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips, level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)
	local max_count = team_type_cfg.max_times or 0
	local remain_times = max_count - enter_times

    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end

    local vas = self.node_list.layout_beasts_combine_hook:GetActive()

    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine, FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1)
    else
        local callback_func = function()
        end

        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.TEAM_COMMON_BOSS_FB1, callback_func)
    end
end

function FuBenPanelView:OnClickControlBeastsBaoMingEnter(is_not_tips)
	local team_type = GoalTeamType.FbControlBeastsType
	local fb_mode = 0

	local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
	local enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)
	local max_count = team_type_cfg.max_times or 0
	local remain_count = max_count - enter_times
    if remain_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
        return
    end

    NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type, fb_mode, 5, is_not_tips)
end
---------------------FuBenBeastsRewardCell--------------------------------
FuBenBeastsRewardCell = FuBenBeastsRewardCell or BaseClass(BaseRender)
function FuBenBeastsRewardCell:__init()
	if not self.base_cell then
		self.base_cell = ItemCell.New(self.node_list["pos"])
		self.base_cell:SetIsShowTips(true)
	end
end

function FuBenBeastsRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function FuBenBeastsRewardCell:OnFlush()
	if not self.data then
		return
	end

	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(false)
end