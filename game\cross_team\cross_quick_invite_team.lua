CrossQuickInviteTeam = CrossQuickInviteTeam or BaseClass(SafeBaseView)

local DownCountTime = 20
function CrossQuickInviteTeam:__init()
	self.view_name = "CrossQuickInviteTeam"
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "quick_invite_team")

	self.running_timer = nil
	self.cur_times = 0
	self.cur_invite_team_info = nil
end

function CrossQuickInviteTeam:__delete()
end

function CrossQuickInviteTeam:ReleaseCallBack()
	self:ClearTimer()
end

function CrossQuickInviteTeam:LoadCallBack()
	self.node_list["IgnoreBtn"].button:AddClickListener(BindTool.Bind(self.OnIgnoreHandler, self))
	self.node_list["JoinBtn"].button:AddClickListener(BindTool.Bind(self.On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))

	self:FlushView()
end

function CrossQuickInviteTeam:OnIgnoreHandler()
	self:SendInviteUserTransmitRet(0)
	self:ClearTimer()
	self:FlushView()
end

function CrossQuickInviteTeam:OnJoinHandler()
	self:SendInviteUserTransmitRet(1)
	self:ClearTimer()
	self:Close()
end

function CrossQuickInviteTeam:SendInviteUserTransmitRet(is_received)
	is_received = is_received or 0
	if self.cur_invite_team_info == nil 
		or self.cur_invite_team_info.inviter == nil then
		return
	end
	CrossTeamWGCtrl.Instance:SendInviteUserRet(self.cur_invite_team_info.inviter, is_received)
end

function CrossQuickInviteTeam:OpenCallBack()
	
end

function CrossQuickInviteTeam:OnFlush()
    self:FlushView()
end

function CrossQuickInviteTeam:FlushView()
	if self.running_timer then
		return
	end
    local team_info = CrossTeamWGData.Instance:GetQuickInviteTeamTopTeamInfo()
	if team_info == nil or IsEmptyTable(team_info) then
		self:ClearTimer()
		self:Close()
		return
	end
	self.cur_invite_team_info = team_info

    self.node_list["inviter_name"].text.text = string.format(Language.NewTeam.QuickInviter, team_info.inviter_name)
	
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.inviter_name.rect)
	local team_type_name = CrossTeamWGData.Instance:GetSceneName()
	self.node_list["target"].text.text = string.format(Language.NewTeam.QuickInviteTeamTypeName, team_type_name)
	self.node_list["IgnoreBtnText"].text.text = string.format(Language.NewTeam.IgnoreStr, DownCountTime)

	if not self.running_timer then
		self.running_timer = GlobalTimerQuest:AddTimesTimer(function()
			self.cur_times = self.cur_times + 1

			if not self:IsLoaded() or not self:IsOpen() then
				self:ClearTimer()
				return
			end
			self.node_list["IgnoreBtnText"].text.text = string.format(Language.NewTeam.IgnoreStr, tostring(DownCountTime - self.cur_times))
            if self.cur_times >= DownCountTime then
				self:SendInviteUserTransmitRet(0)
                self:ClearTimer()
				self:FlushView()
			end
		end, 1, DownCountTime)
	end
end

function CrossQuickInviteTeam:ClearTimer()
	self.cur_times = 0
	GlobalTimerQuest:CancelQuest(self.running_timer)
	self.running_timer = nil
end