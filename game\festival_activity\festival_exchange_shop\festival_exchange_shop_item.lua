-----------------------FestivalExchangeShopItem-----------------------------------
FestivalExchangeShopItem = FestivalExchangeShopItem or BaseClass(BaseRender)

function FestivalExchangeShopItem:__init()
	-- XUI.AddClickEventListener(self.node_list.click_cell, BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_exchange, BindTool.Bind(self.ClickExchange, self))
	XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind(self.ClickStuff, self))
	
	self.node_list["btn_exchange"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_btn_huang"))

	local item_bg_bundle, item_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_dlsd_di")
 	self.node_list["item_bg"].image:LoadSprite(item_bg_bundle, item_bg_asset, function ()
 		self.node_list["item_bg"].image:SetNativeSize()
  	end)

	local exchange_num_bg_bundle, exchange_num_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_dlsd_kdh")
	self.node_list["exchange_num_bg"].image:LoadSprite(exchange_num_bg_bundle, exchange_num_bg_asset, function ()
		self.node_list["exchange_num_bg"].image:SetNativeSize()
	end)

	self.item_cell = FestivalExchangeShopItemCell.New(self.node_list.item)
end

function FestivalExchangeShopItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FestivalExchangeShopItem:OnFlush()
	if self.data == nil then
		self.view:SetActive(false)
		return
	end
	self.view:SetActive(true)
	-- local exchange_data = ExchangeShopWGData.Instance:GetData()
	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id)
	local common_color = FestivalActivityWGData.Instance:GetCommonColor()
	self.node_list.name.text.text = ToColorStr(self.data.name,common_color)
	self.node_list.cost.text.text = GetRightColor(num .. "/" .. self.data.cost, num >= self.data.cost, COLOR3B.L_GREEN, COLOR3B.L_RED)
	-- self.node_list.exchange_num.text.text = ToColorStr("可兑换:",common_color) .. GetRightColor(self.data.can_exchange_times, self.data.can_exchange_times > 0,
	-- 	COLOR3B.GREEN, COLOR3B.RED)
	self.node_list.exchange_num.text.text ="可兑换:" .. GetRightColor(self.data.can_exchange_times, self.data.can_exchange_times > 0,
		COLOR3B.GREEN, COLOR3B.RED)
	self.node_list.remind:SetActive(self.data.can_exchange_times > 0 and num >= self.data.cost)
	local config = ItemWGData.Instance:GetItemConfig(self.data.stuff_id)

	if config then
		local bundle, asset = ResPath.GetItem(config.icon_id)
		self.node_list.item_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.item_icon.image:SetNativeSize()
		end)
	else
		print_error("[FestivalExchangeShopItem] item_config is invaild, item_id is:", self.data.stuff_id)
	end


	self.node_list.btn_exchange:SetActive(self.data.can_exchange_times ~= 0)
	self.node_list.word_sold_out:SetActive(self.data.can_exchange_times == 0)

	self.item_cell:SetData(self.data.item)
end

function FestivalExchangeShopItem:SetClickCallBack(event)
	self.event = event
end

function FestivalExchangeShopItem:OnClick()
	if self.event then
		self.event(self)
	end
end

function FestivalExchangeShopItem:ClickExchange()
	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.stuff_id)
	if num < self.data.cost then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.stuff_id})
		return
	end

	local num = self.data.item.num
	local empty = ItemWGData.Instance:GetEmptyNum()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item.item_id)

	if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		return
	end
	
	TipWGCtrl.Instance:ShowGetItem(self.data.item)
	FestivalExchangeShopWGCtrl.Instance:SendOperExchange(1, self.data.seq)
end

function FestivalExchangeShopItem:ClickStuff()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.stuff_id}, ItemTip.FROM_NORMAL, nil)
end

FestivalExchangeShopItemCell = FestivalExchangeShopItemCell or BaseClass(ItemCell)

function FestivalExchangeShopItemCell:OnFlush()
	ItemCell.OnFlush(self)
	if not self.data then return end
	--self:ShowItemActive()
end

