﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;



public class RuntimeInfo
{
    public long NetworkIn;

    public long NetworkOut;

    public Vector4 PssInfo;

    public float Battery;

    public float Temperature;

    public List<float> CpuTemp;

    public float GpuLoad;

    public float[] CpuLoads;

    public int[] CpuFreqs;

    public float[] LastCpuTicks;

    public float GpuTime;
}

public class YYHardwareTrackManager : MonoBehaviour
{
    protected RuntimeInfo _runtimeInfo;

    private void Awake()
    {
        InitWithConfig(null);
    }

    protected void InitWithConfig(Dictionary<string, string> config)
    {
        //IL_0008: Unknown result type (might be due to invalid IL or missing references)
        //IL_000d: Unknown result type (might be due to invalid IL or missing references)
        _runtimeInfo = new RuntimeInfo
        {
            PssInfo = Vector4.zero,
            Battery = -1f,
            NetworkOut = -1L,
            NetworkIn = -1L,
            Temperature = -1f,
            CpuTemp = new List<float>(),
            GpuTime = 0f
        };
    }
}
