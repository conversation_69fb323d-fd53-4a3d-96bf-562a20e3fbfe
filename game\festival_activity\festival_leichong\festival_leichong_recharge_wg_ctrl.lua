require("game/festival_activity/festival_leichong/festival_leichong_recharge_wg_data")

FestivalLeiChongRechargeWGCtrl = FestivalLeiChongRechargeWGCtrl or BaseClass(BaseWGCtrl)
function FestivalLeiChongRechargeWGCtrl:__init()
	if FestivalLeiChongRechargeWGCtrl.Instance then
		ErrorLog("[FestivalLeiChongRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalLeiChongRechargeWGCtrl.Instance = self
	self.data = FestivalLeiChongRechargeWGData.New()
	self:RegisterAllProtocols()
	--self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function FestivalLeiChongRechargeWGCtrl:__delete()
	FestivalLeiChongRechargeWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
	if self.LCdelay_timer then
		GlobalTimerQuest:CancelQuest(self.LCdelay_timer)
		self.LCdelay_timer = nil
	end
end

function FestivalLeiChongRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCFALimitRecharge, "OnSCFALimitRecharge")
	self:RegisterProtocol(CSFALimitRechargeOpera)
end

function FestivalLeiChongRechargeWGCtrl:OnSCFALimitRecharge(protocol)
	self.data:SetLeiChongRechargeData(protocol)
	FestivalActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2263)
	RemindManager.Instance:Fire(RemindName.FestivalTotalRecharge)
	
end

function FestivalLeiChongRechargeWGCtrl:SendLeiChongRechargeReq(type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFALimitRechargeOpera)
	protocol.type = type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function FestivalLeiChongRechargeWGCtrl:OnPassDay()
		--做个容错，跨天的时候，服务器的数据可能还没同步过来
	self.LCdelay_timer = GlobalTimerQuest:AddDelayTimer(function()
		FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2263)
		self.LCdelay_timer = nil
	end, 1)
end

