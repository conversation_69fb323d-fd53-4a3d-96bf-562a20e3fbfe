﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CircleLayoutGroupWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(CircleLayoutGroup), typeof(UnityEngine.UI.LayoutGroup));
		<PERSON><PERSON>("CalculateLayoutInputVertical", CalculateLayoutInputVertical);
		<PERSON><PERSON>("SetLayoutHorizontal", SetLayoutHorizontal);
		<PERSON><PERSON>ction("SetLayoutVertical", SetLayoutVertical);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.Reg<PERSON>ar("TypeMode", get_TypeMode, set_TypeMode);
		<PERSON><PERSON>("Radius", get_Radius, set_Radius);
		<PERSON><PERSON>("InitAngle", get_InitAngle, set_InitAngle);
		<PERSON><PERSON>("KeepAngle", get_KeepAngle, set_KeepAngle);
		<PERSON><PERSON>("KeepKeepAngleVal", get_KeepKeepAngleVal, set_KeepKeepAngleVal);
		<PERSON><PERSON>("sectorAngle", get_sectorAngle, set_sectorAngle);
		<PERSON>.RegVar("SectorClockwise", get_SectorClockwise, set_SectorClockwise);
		L.RegVar("LockSubAngle", get_LockSubAngle, set_LockSubAngle);
		L.RegVar("SubAngle", get_SubAngle, set_SubAngle);
		L.RegVar("NotRotationInAll", get_NotRotationInAll, set_NotRotationInAll);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalculateLayoutInputVertical(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)ToLua.CheckObject<CircleLayoutGroup>(L, 1);
			obj.CalculateLayoutInputVertical();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLayoutHorizontal(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)ToLua.CheckObject<CircleLayoutGroup>(L, 1);
			obj.SetLayoutHorizontal();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLayoutVertical(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)ToLua.CheckObject<CircleLayoutGroup>(L, 1);
			obj.SetLayoutVertical();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TypeMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			CircleLayoutGroup.LayoutMode ret = obj.TypeMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TypeMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float ret = obj.Radius;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InitAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float ret = obj.InitAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index InitAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_KeepAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool ret = obj.KeepAngle;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index KeepAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_KeepKeepAngleVal(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float ret = obj.KeepKeepAngleVal;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index KeepKeepAngleVal on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_sectorAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float ret = obj.sectorAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sectorAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SectorClockwise(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool ret = obj.SectorClockwise;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SectorClockwise on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LockSubAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool ret = obj.LockSubAngle;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LockSubAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SubAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float ret = obj.SubAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SubAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NotRotationInAll(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool ret = obj.NotRotationInAll;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NotRotationInAll on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TypeMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			CircleLayoutGroup.LayoutMode arg0 = (CircleLayoutGroup.LayoutMode)ToLua.CheckObject(L, 2, typeof(CircleLayoutGroup.LayoutMode));
			obj.TypeMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TypeMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Radius(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Radius = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Radius on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_InitAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.InitAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index InitAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_KeepAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.KeepAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index KeepAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_KeepKeepAngleVal(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.KeepKeepAngleVal = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index KeepKeepAngleVal on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_sectorAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.sectorAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sectorAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SectorClockwise(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SectorClockwise = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SectorClockwise on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_LockSubAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.LockSubAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LockSubAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SubAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SubAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SubAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_NotRotationInAll(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CircleLayoutGroup obj = (CircleLayoutGroup)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.NotRotationInAll = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NotRotationInAll on a nil value");
		}
	}
}

