ChatSetting = ChatSetting or BaseClass(SafeBaseView)

local Index2Btn_Name = {
	[CHANNEL_TYPE.TEAM] = "btn_maskteam",
	[CHANNEL_TYPE.WORLD] = "btn_maskworld",
	[CHANNEL_TYPE.GUILD] = "btn_maskguild",
	[CHANNEL_TYPE.SYSTEM] = "btn_masksystem",
	[CHANNEL_TYPE.CROSS] = "btn_maskkuafu",

	[CHANNEL_TYPE.TEAM + 100] = "btn_autoteam",
	[CHANNEL_TYPE.WORLD + 100] = "btn_autoworld",
	[CHANNEL_TYPE.GUILD + 100] = "btn_autoguild",
	[CHANNEL_TYPE.CROSS + 100] = "btn_autokuafu",
}

function ChatSetting:__init()
	-- self:SetModal(true)
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	-- self.texture_path_list[1] = "res/xui/chat.png"
	-- self.texture_path_list[2] = "res/xui/face.png"
	-- self.config_tab = {{"chat_ui_cfg", 1, {0}},}
	-- self:SetIaAnyClickClose(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(694, 484)})
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_setting")

	--城市相关
	self.location_cfg = Config.Location
	self.use_real_location = 0
	self.menu_select_type = 1
	self.province_index = 1
	self.city_index = 1
	self.is_need_sync_to_server = false
	self.select_memu_callback = BindTool.Bind1(self.SelectLocationIndexCallBack, self)
	self.notify_location_callback = BindTool.Bind1(self.OnNotifyLocationChangeCallBack, self)
end

function ChatSetting:__delete()
end

function ChatSetting:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.ChatSettingTitle
	self.node_list["img_hook_0"]:SetActive(false)
	self.node_list["img_hook_3"]:SetActive(false)
	self.node_list["img_hook_4"]:SetActive(false)
	self.node_list["img_hook_16"]:SetActive(false)
	self.node_list["img_hook_6"]:SetActive(false)

	self.node_list["img_hook_100"]:SetActive(false)
	self.node_list["img_hook_103"]:SetActive(false)
	self.node_list["img_hook_104"]:SetActive(false)
	self.node_list["img_hook_106"]:SetActive(false)

	-- local area_type = RoleWGData.GetAreaType()
	self.node_list["btn_city"]:SetActive(false)--area_type == AREA_TYPES.DEFAULT)

	self:RegisterSettingEvent()
	self.msg_tag = self.msg_tag or {}
	for _,v in pairs(CustomEnumChatChannel) do
		local bo = ChatWGData.Instance:GetChannel(v).is_pingbi
		self.node_list.layout_setting["img_hook_" .. v]:SetActive(bo)
	end
	-- for k,v in pairs(self.msg_tag) do
	-- 	self.node_list.layout_setting["img_hook_" .. k]:SetActive(self.msg_tag[k] )
	-- end

	self.auto_tag = self.auto_tag or {}
	for k,v in pairs(self.auto_tag) do
		self.node_list.layout_setting["img_hook_" .. k]:SetActive(self.auto_tag[k])
	end
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.CloseWindow, self))
	self.is_show_location = true

	--城市相关
	self:CalcLocationInfo()
	self.use_real_location = 0
	Location.Instance:NotifyCityNameChange(self.notify_location_callback)

	self.node_list["layout_btn_check_box"].button:AddClickListener(BindTool.Bind(self.ClickCheckBoxCallBack, self))

	XUI.AddClickEventListener(self.node_list.layout_btn_city_1, BindTool.Bind2(self.OnOpenCitySelectMenu, self, 1))
	XUI.AddClickEventListener(self.node_list.layout_btn_city_2, BindTool.Bind2(self.OnOpenCitySelectMenu, self, 2))

	if self.province_index and self.city_index then
		local cfg = self.location_cfg[self.province_index]
		if nil == cfg or nil == cfg.city[self.city_index] then
			return
		end
		self.node_list["lbl_province"].text.text  = cfg.province
		self.node_list["lbl_city"].text.text  = cfg.city[self.city_index]

		local is_real_location = GameVoManager.Instance:GetMainRoleVo().is_real_location
		self.node_list["img_hook_city"]:SetActive(is_real_location == 1)
		self.use_real_location = is_real_location
	end
end

function ChatSetting:ClickCheckBoxCallBack()
	--1->0
	self.use_real_location = 1 == self.use_real_location and 0 or 1

	--使用实际地址
	if 1 == self.use_real_location then
		Location.Instance:UseRealLoaction()
	end

	self.is_need_sync_to_server = true
	self.node_list["img_hook_city"]:SetActive(self.use_real_location == 1)

	self:Flush()
end

function ChatSetting:OnOpenCitySelectMenu(menu_select_type)
	if 1 == self.use_real_location then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.XiuGaiDiZhi)
		return
	end

	--print_error("选择城市类型" , menu_select_type)
	self.menu_select_type = menu_select_type

	local data_list = {}
	if 1 == self.menu_select_type then
		--self.node_list["layout_city_list"].rect.anchoredPosition = Vector2(20,293)
		for _, v in pairs(self.location_cfg) do
			table.insert(data_list, v.province)
		end
	else
		--self.node_list["layout_city_list"].rect.anchoredPosition = Vector2(20,-353)
		local province_cfg = self.location_cfg[self.province_index]
		if nil ~= province_cfg then
			data_list = province_cfg.city
		end
	end

	local default_index = 1 == self.menu_select_type and self.province_index or self.city_index
	local pos_flag = 1 == self.menu_select_type and 1 or 2
	ChatWGCtrl.Instance:OpenCitySceleMenu(data_list, default_index, self.select_memu_callback, pos_flag)
end

function ChatSetting:SelectLocationIndexCallBack(menu_index)
	local province_index = 1
	local city_index = 1

	if self.menu_select_type == 1 then
		province_index = menu_index
		city_index = 1
	else
		province_index = self.province_index
		city_index = menu_index
	end

	local cfg = self.location_cfg[province_index]
	if nil == cfg or nil == cfg.city[city_index] then
		return
	end

	self.node_list["lbl_province"].text.text  = cfg.province
	self.node_list["lbl_city"].text.text  = cfg.city[city_index]

	ChatWGData.Instance:SetLocation(cfg.province, cfg.city[city_index])
	ChatWGData.Instance:SetLocationIndex(province_index, city_index)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	main_role_vo.city_name = cfg.city[city_index]
	self.province_index = province_index
	self.city_index = city_index
	self.is_need_sync_to_server = true

end

function ChatSetting:OnNotifyLocationChangeCallBack()
	self:CalcLocationInfo()

	self:Flush()
end

function ChatSetting:CalcLocationInfo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.use_real_location = main_role_vo.is_real_location

	_, self.province_index, self.city_index = self:GetProvinceNameByCityName(main_role_vo.city_name)
	--print_error("聊天 ", main_role_vo.city_name, self.province_index, self.city_index)
end

function ChatSetting:CloseWindow()
	self:Close()
	
end

function ChatSetting:CloseCallBack()
	ChatWGCtrl.Instance:CloseLocation()
	if self.is_need_sync_to_server then
		if 1 == self.use_real_location then
			Location.Instance:UseRealLoaction()
		else
			local province_cfg = self.location_cfg[self.province_index]
			if nil ~= province_cfg and nil ~= province_cfg.city[self.city_index] then
				Location.Instance:UseSelectLocation(province_cfg.city[self.city_index], province_cfg.province)
			end
		end
	end
end

function ChatSetting:RegisterSettingEvent()
	self.node_list["btn_maskworld"].button:AddClickListener(BindTool.Bind2(self.MaskChannelMsg, self, CHANNEL_TYPE.WORLD))
	self.node_list["btn_maskteam"].button:AddClickListener(BindTool.Bind2(self.MaskChannelMsg, self, CHANNEL_TYPE.TEAM))
	self.node_list["btn_maskguild"].button:AddClickListener(BindTool.Bind2(self.MaskChannelMsg, self, CHANNEL_TYPE.GUILD))
	self.node_list["btn_masksystem"].button:AddClickListener(BindTool.Bind2(self.MaskChannelMsg, self, CHANNEL_TYPE.SYSTEM))
	self.node_list["btn_maskkuafu"].button:AddClickListener(BindTool.Bind2(self.MaskChannelMsg, self, CHANNEL_TYPE.CROSS))
	self.node_list["btn_showlocation"].button:AddClickListener(BindTool.Bind1(self.ShowLocation, self))


	self.node_list["btn_autoworld"].button:AddClickListener(BindTool.Bind2(self.AutoPlayVoice, self, CHANNEL_TYPE.WORLD))
	self.node_list["btn_autoteam"].button:AddClickListener(BindTool.Bind2(self.AutoPlayVoice, self, CHANNEL_TYPE.TEAM))
	self.node_list["btn_autoguild"].button:AddClickListener(BindTool.Bind2(self.AutoPlayVoice, self, CHANNEL_TYPE.GUILD))
	self.node_list["btn_autokuafu"].button:AddClickListener(BindTool.Bind2(self.AutoPlayVoice, self, CHANNEL_TYPE.CROSS))

	local not_shield_voice = GLOBAL_CONFIG.param_list.shield_chat_voice ~= 1
	self.node_list["btn_autoworld"]:SetActive(not_shield_voice)
	self.node_list["btn_autoteam"]:SetActive(not_shield_voice)
	self.node_list["btn_autoguild"]:SetActive(not_shield_voice)
	self.node_list["btn_autokuafu"]:SetActive(not_shield_voice)
end

function ChatSetting:ShowLocation()
	self.is_show_location = not self.is_show_location
	self.node_list["img_hook_7"]:SetActive(self.is_show_location)
end

function ChatSetting:OnFlush()
	for _,v in pairs(CustomEnumChatChannel) do
		local bo = ChatWGData.Instance:GetChannel(v).is_pingbi
		local btn_name = Index2Btn_Name[v]
		self.node_list.layout_setting["img_hook_" .. v]:SetActive(bo)
		self.node_list[btn_name]:FindObj("nomal"):SetActive(not bo)
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

	local is_show_real_location = (self.use_real_location == 1)


	local province_name = self:GetProvinceNameByCityName(main_role_vo.city_name)
	if "" ~= province_name then
		self.node_list["lbl_province"].text.text = (province_name)
		self.node_list["lbl_city"].text.text = (main_role_vo.city_name)
	end
end

function ChatSetting:MaskChannelMsg(channel_type, sender)
	local channel = ChatWGData.Instance:GetChannel(channel_type)
	if nil == channel then
		return
	end
	channel.is_pingbi = not channel.is_pingbi
	ChatWGCtrl.Instance:SendPingBiInfo(ChatChannelCustomEnum[channel_type], channel.is_pingbi and 1 or 0)
end

function ChatSetting:AutoPlayVoice(channel_type, sender)
	-- print_error("++++++++++++++",channel_type+100)
	local channel = ChatWGData.Instance:GetChannel(channel_type)
	if nil == channel then
		return
	end
	-- print_error("--------------",channel_type+100)
	channel.is_auto_voice = not channel.is_auto_voice
	self.auto_tag[channel_type + 100] = channel.is_auto_voice

	self.node_list["img_hook_" .. (channel_type+100)]:SetActive(self.auto_tag[channel_type + 100])

	local btn_name = Index2Btn_Name[channel_type + 100]
	self.node_list[btn_name]:FindObj("nomal"):SetActive(not self.auto_tag[channel_type + 100])
end

function ChatSetting:GetProvinceNameByCityName(check_city_name)
	for p_index, v in ipairs(self.location_cfg) do
		local city_list = v.city
		for c_index, city_name in ipairs(city_list) do
			if city_name == check_city_name then
				return v.province, p_index, c_index
			end
		end
	end

	return "", 1, 1
end


----------------------CitySelectMenu-------------------
CitySelectMenu = CitySelectMenu or BaseClass(SafeBaseView)

function CitySelectMenu:__init()
	--self.is_any_click_close = true
	self:SetMaskBg(true,true)
	-- self:SetModal(true)
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_city_list")
	self.data_list = {}
	self.default_select_index = 1
	self.select_callback = nil
	self.open_tween = nil
	self.close_tween = nil
end

function CitySelectMenu:ReleaseCallBack()
	if self.city_list then
		self.city_list:DeleteMe()
		self.city_list = nil
	end
	self.select_callback = nil
	self.data_list = nil
	self.ph_list_view = nil
end

function CitySelectMenu:LoadCallBack()
	self.ph_list_view = self.node_list["ph_city_list"]
	self.city_list = AsyncListView.New(CityItemRender, self.ph_list_view)
	self.city_list:SetSelectCallBack(BindTool.Bind1(self.SelectCityCallBack, self))
	self:Flush()
end

function CitySelectMenu:SetMenuData(data_list, default_select_index, select_callback, pos_flag)
	self.data_list = data_list
	self.default_select_index = default_select_index
	self.select_callback = select_callback
	self.pos_flag = pos_flag
	self:Flush()
end

function CitySelectMenu:ShowIndexCallBack(index)
	if self.pos_flag == 1 then --省
		self.node_list["layout_city_list"]["layout_city_list"].rect.anchoredPosition = Vector2(-104, -262)
	else
		self.node_list["layout_city_list"]["layout_city_list"].rect.anchoredPosition = Vector2(-104, -350)
	end
end

function CitySelectMenu:OnFlush()
	self.city_list:SetDataList(self.data_list)
	self.city_list:JumpToTop()
end

function CitySelectMenu:SelectCityCallBack(item_cell, cell_index, is_default, is_click)
	if nil ~= self.select_callback then
		self.select_callback(cell_index)
	end
	-- if item_cell ~= nil then
	-- 	item_cell:SetHigtLight(true)
	-- end
    ChatWGCtrl.Instance:RefreshChannel()
    if not is_default then
        self:Close()
    end
end

-------------------------ItemRender------------------------------
CityItemRender = CityItemRender or BaseClass(BaseRender)

function CityItemRender:__init()
	-- self:SetModal(true)

end

function CityItemRender:__delete()

end

-- function CityItemRender:SetHigtLight(bo)
-- 	self.node_list["HL"]:SetActive(bo)
-- end

function CityItemRender:CreateChild()
	BaseRender.CreateChild(self)
end

function CityItemRender:OnFlush()
	if self.data == nil then return end
	self.node_list["lbl_select_name"].text.text = self.data
end