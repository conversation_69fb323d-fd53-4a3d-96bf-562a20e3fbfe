FuBenTabbar = FuBenTabbar or BaseClass(BaseRender)
function FuBenTabbar:__init(parent, ver_content, hor_content)
	self.ver_obj_list = {}
	self.ver_cell_list = {}
	self.hor_cell_list = {}
	self.remind_val_v_t = {}
	self.remind_val_h_t = {}
	self.remind_tab = {}
	self.remind_to_index = {}
	self.tab_fun_list = {}
	self.ver_fun_list = {}

	self.is_init = false
	self.is_no_callback = false
	self.ver_res_async_loader = nil
	self.select_ver_index = 1
	self.select_hor_index = {}


	self.ver_content = ver_content
	self.hor_content = hor_content
	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
end

function FuBenTabbar:__delete()
	if next(self.ver_cell_list) ~= nil then
		for _,v in pairs(self.ver_cell_list) do
			v:DeleteMe()
		end
	end
	self.ver_cell_list = {}

	if self.cambered_list then
		self.cambered_list:DeleteMe()
		self.cambered_list = nil
	end
	self.hor_cell_list = {}

	-- 取消红点监听
    self:UnAllListen()
end

function FuBenTabbar:AddAllListen()
	for k, v in pairs(self.remind_tab) do
		for k2, v2 in pairs(v) do
			RemindManager.Instance:Bind(self.remind_change, v2)
		end
	end
end

function FuBenTabbar:UnAllListen()
	RemindManager.Instance:UnBind(self.remind_change)
end


function FuBenTabbar:SetSelectCallback(callback)
	self.tabbar_call_back = callback
end

function FuBenTabbar:RemindChangeCallBack(remind_name, num)
	local index = self.remind_to_index[remind_name]
	if nil ~= index and nil ~= num then
		local ver = math.floor(index / 1000)
		local hor = index % 1000
		local old_num = self.remind_val_h_t[index] or 0
		self.remind_val_v_t[ver] = self.remind_val_v_t[ver] or 0
		self.remind_val_v_t[ver] = self.remind_val_v_t[ver] + num - old_num
		self.remind_val_h_t[index] = num

		if self.ver_cell_list[ver] and self.remind_val_v_t[ver] then
			self.ver_cell_list[ver]:ShowRemind(self.remind_val_v_t[ver] > 0)
		end

		if self.hor_cell_list[hor] and self.remind_val_h_t[index] and ver == self.select_ver_index then
			self.hor_cell_list[hor]:ShowRemind(self.remind_val_h_t[index] > 0)
		end
    end
end

function FuBenTabbar:Init(VerTab, HorTab, remind_tab)
	self.is_init = true
	self.tab_fun_list = {}
	self.ver_fun_list = {}

	if type(remind_tab) == "table" then
		for k, v in pairs(remind_tab) do
			for k2, v2 in pairs(v) do
				self.remind_to_index[v2] = k * 1000 + k2
				local num = RemindManager.Instance:GetRemind(v2)
				self:RemindChangeCallBack(v2, num)
			end
		end

		self.remind_tab = remind_tab
		self:AddAllListen()
	end

	self.ver_tab = VerTab or {}
	self.hor_tab = HorTab or {}
	self:InitSelectHorIndex()
	self.length = {}
	for i, v in pairs(self.hor_tab) do
		if v then
			self.length[i] = type(v) == "table" and #v or 1
		end
	end


	if self:GetVerListViewNumbers() > 0 then
		self:LoadVerCell()
	end
end

function FuBenTabbar:GetVerListViewNumbers()
	return #self.ver_tab
end

function FuBenTabbar:GetHorListViewNumbers()
	return #self.hor_tab
end

function FuBenTabbar:InitSelectHorIndex()
	for k,v in pairs(self.ver_tab) do
		if not self.select_hor_index[k] then
			self.select_hor_index[k] = self.hor_tab[k] and 1 or 0
		end
	end
end

function FuBenTabbar:LoadVerCell()
	for _,v in pairs(self.ver_cell_list) do
		v:DeleteMe()
	end
	self.ver_cell_list = {}


	local ver_num = self:GetVerListViewNumbers()
	local node_num = self.ver_content.transform.childCount
	for i = 1, node_num do
		local index = #self.ver_cell_list + 1
		local cell = FuBenVerCell.New(self.ver_content:FindObj("ver" .. i))
		cell:SetIndex(i)
		cell:SetDuoBeiData()
		cell:SetClickCallback(BindTool.Bind(self.VerListEventCallback, self))

		if nil ~= self.ver_fun_list[i] then
			cell:SetIsAct(self.ver_fun_list[i])
		end

		if self.remind_val_v_t[index] then
			cell:ShowRemind(self.remind_val_v_t[index] > 0)
		end

		if self.select_hor_index[index] and self.select_ver_index == index and not cell:IsOn() then
			self.is_no_callback = true
			cell:SetToggleIsOn(true)
		end

		self.ver_cell_list[index] = cell
	end
end

function FuBenTabbar:VerListEventCallback(cell, isOn)
	if not isOn then
		return
	end

	self.select_ver_index = cell:GetIndex()
	self:CheckHorCellSelect()
	local hor_num = self:GetHorListViewNumbers()
	local hor_index = self.select_hor_index[self.select_ver_index]
	local callback_index = hor_index and (self.select_ver_index * 10 + hor_index) or self.select_ver_index * 10
	self:LoadHorCell()
	if self.tabbar_call_back then
		self.tabbar_call_back(callback_index)
	end
end

function FuBenTabbar:LoadHorCell()
	if IsEmptyTable(self.hor_cell_list) then
		-- 弧形列表数据
		local cambered_list_data = {
			item_render = FuBenHorCell,
			asset_bundle = "uis/view/fubenpanel_prefab",
			asset_name = "HorizontalTabbarCell",

			scroll_list = self.hor_content,
			center_x = 0, center_y = 0,
			radius_x = 240, radius_y = 240,
			angle_delta = Mathf.PI * 0.18,
			origin_rotation = Mathf.PI * 0.14,
			is_drag_horizontal = false,
			viewport_count = 5,

			click_item_cb = BindTool.Bind(self.HorListEventCallback, self),
			-- drag_to_next_cb = BindTool.Bind(self.OnDragToNextCallBack, self),
			-- drag_to_last_cb = BindTool.Bind(self.OnDragToLastCallBack, self),
			-- on_drag_end_cb = BindTool.Bind(self.OnDragEndCallBack, self),
		}

		self.cambered_list = CamberedList.New(cambered_list_data)
		self.cambered_list:CreateCellList(5)
		local btn_item_list = self.cambered_list:GetRenderList()

		for k, item_cell in ipairs(btn_item_list) do
			item_cell:SetToggleGroup(self.hor_content.toggle_group)
			self.hor_cell_list[k] = item_cell
		end
	end

	local data = self.hor_tab[self.select_ver_index] or {}
	local fun_list = self.tab_fun_list[self.select_ver_index]
	for k, item_cell in ipairs(self.hor_cell_list) do
		local remind_num = self.remind_val_h_t[k + self.select_ver_index * 1000] or 0
		item_cell:ShowRemind(remind_num > 0)

		if k == self.select_hor_index[self.select_ver_index] and not item_cell:IsOn() then
			self.is_no_callback = true
			item_cell:SetToggleIsOn(true)
		end

		item_cell:SetData({ver_index = self.select_ver_index, data = data[k]})
		item_cell:SetIsAct(fun_list and fun_list[k])
	end
end

function FuBenTabbar:HorListEventCallback(cell, isOn)
	if not isOn then
		return
	end

	local cell_index = cell:GetIndex()
	self.select_hor_index[self.select_ver_index] = cell_index
	if self.tabbar_call_back and not self.is_no_callback then
		self.tabbar_call_back(self.select_ver_index * 10 + cell_index)
	end

	self.is_no_callback = false
end

function FuBenTabbar:SetToggleVisible(k, visible)
	local ver = math.floor(k / 10)
	local hor = k % 10

	if nil == self.tab_fun_list[ver] then
		self.tab_fun_list[ver] = {}
	end

	self.tab_fun_list[ver][hor] = visible
	local v_vis = visible
	if self.length[ver] then
		for i = 1, self.length[ver] do
			if self.tab_fun_list[ver][i] ~= false then
				v_vis = true
				break
			end
		end
	end

	if self.ver_cell_list[ver] then
		self.ver_cell_list[ver]:SetIsAct(v_vis)
	end
	self.ver_fun_list[ver] = v_vis

	if self.hor_cell_list[hor] and ver == self.select_ver_index then
		self.hor_cell_list[hor]:SetIsAct(visible)
	end
end

function FuBenTabbar:CheckHorCellSelect()
	local hor_show_index = self.select_hor_index[self.select_ver_index]
	if hor_show_index and self.tab_fun_list[self.select_ver_index] and self.tab_fun_list[self.select_ver_index][hor_show_index] == false
	and self.length[self.select_ver_index] then
		for i = 1, self.length[self.select_ver_index] do
			if self.tab_fun_list[self.select_ver_index][i] ~= false then
				self.select_hor_index[self.select_ver_index] = i
				break
			end
		end
	end
end

function FuBenTabbar:ChangeToIndex(index, is_no_callback)
	local v_index = math.floor(index / 10)
	local h_index = index % 10
	if self.select_ver_index == v_index and self.select_hor_index[self.select_ver_index] == h_index then
		return
	end

	self.select_ver_index = v_index
	self.select_hor_index[self.select_ver_index] = h_index
	if not self.is_init then
		return
	end

	local ver_cell = self.ver_cell_list[self.select_ver_index]
	if ver_cell and not ver_cell:IsOn() then
		self.is_no_callback = true
		ver_cell:SetToggleIsOn(true)
	end

	local hor_cell = self.hor_cell_list[h_index]
	if hor_cell and not hor_cell:IsOn() then
		self.is_no_callback = true
		hor_cell:SetToggleIsOn(true)
	end
end


function FuBenTabbar:FlushTabbarFlag()
	for k, v in pairs(self.ver_cell_list) do
		v:SetDuoBeiData()
	end

	for k, v in pairs(self.hor_cell_list) do
		v:SetDuoBeiData()
		v:SetJingYanData()
	end
end

------------------------------------------------
------------------------------------------------
FuBenVerCell = FuBenVerCell or BaseClass(BaseRender)
function FuBenVerCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.cell_root, BindTool.Bind(self.OnClick, self))
end

function FuBenVerCell:__delete()

end

-- 设置点击回调
function FuBenVerCell:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function FuBenVerCell:OnClick(is_on)
	if self.click_callback then
		self.click_callback(self, is_on)
	end
end

function FuBenVerCell:SetIsAct(is_act)
	XUI.SetGraphicGrey(self.view, not is_act)
end

function FuBenVerCell:IsOn()
	return self.view.toggle.isOn
end

function FuBenVerCell:SetToggleIsOn(is_on)
	self.view.toggle.isOn = is_on
end

function FuBenVerCell:ShowRemind(is_show)
	self.node_list.RedPoint:SetActive(is_show)
end

function FuBenVerCell:SetDuoBeiData()
	local task_list = Language.FuBenPanel.TabSub3
	local task_type = task_list[self.index] or {}
	if self.node_list.duobei ~= nil then
		self.node_list.duobei:SetActive(false)
		local is_duobei = false
		local times = 0
		for k,v in pairs(task_type) do
			is_duobei, times = ActivityWGData.Instance:IsDuoBeiTimes(v)
			if is_duobei then
				break
			end
		end

		if is_duobei then
			self.node_list.duobei:SetActive(true)
			-- self.node_list.duobei_value.text.text = Language.FuBenPanel.DuoBei
		end
	end
end











FuBenHorCell = FuBenHorCell or BaseClass(BaseRender)
function FuBenHorCell:LoadCallBack()
	self.is_act = false
	XUI.AddClickEventListener(self.node_list.cell_root, BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_other, BindTool.Bind(self.OnClickOtherBtn, self))
end

function FuBenHorCell:__delete()

end

-- 设置点击回调
function FuBenHorCell:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function FuBenHorCell:OnClick()
	if self.click_callback then
		self.click_callback(self)
	end
end

function FuBenHorCell:SetToggleGroup(group)
	self.view.toggle.group = group
end

function FuBenHorCell:IsOn()
	return self.view.toggle.isOn
end

function FuBenHorCell:SetToggleIsOn(is_on)
	self.view.toggle.isOn = is_on
end

function FuBenHorCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local data = self.data.data
	if data == nil then
		self:SetNameIcon(nil, true)
		self:ShowExpFlag(false)
		self:ShowDuobleFlag(false)
		return
	end

	local ver_index = self.data.ver_index
	local icon_id = ver_index * 10 + self.index
	self:SetNameIcon(icon_id)
	self:SetDuoBeiData()
	self:SetJingYanData()
end

function FuBenHorCell:SetIsAct(is_act)
	self.is_act = is_act
	XUI.SetGraphicGrey(self.node_list.nor_img, not is_act)
	XUI.SetGraphicGrey(self.node_list.hl_img, not is_act)

	if self.is_act == nil then
		self:ShowOtherBtn(Language.Common.JingQingQiDai)
	elseif self.is_act then
		self:HideOtherBtn()
	else
		local limit_str = self:GetLimitStr()
		self:ShowOtherBtn(limit_str)
	end
end

function FuBenHorCell:SetNameIcon(res_id, is_lock)
	local bundle, asset = "", ""
	if is_lock then
		bundle, asset = ResPath.GetCommonImages("a1_suo_bb")
	else
		local res = "fb_icon_" .. res_id
		bundle, asset = ResPath.GetFuBenImage(res)
	end

	self.node_list.nor_icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.nor_icon.image:SetNativeSize()
	end)

	if not is_lock then
		asset = asset .. "_hl"
	end

	self.node_list.hl_icon.image:LoadSprite(bundle, asset, function ()
		self.node_list.hl_icon.image:SetNativeSize()
	end)
end

function FuBenHorCell:ShowRemind(is_show)
	self.node_list.RedPoint:SetActive(is_show)
end

function FuBenHorCell:ShowOtherBtn(limit_str)
	self.node_list.btn_other:SetActive(true)
	self.node_list.limit_text.text.text = limit_str
end

function FuBenHorCell:HideOtherBtn()
	self.node_list.btn_other:SetActive(false)
end

function FuBenHorCell:ShowExpFlag(is_show)
	self.node_list.jingyan:SetActive(is_show)
end

function FuBenHorCell:ShowDuobleFlag(is_show)
	self.node_list.duobei:SetActive(is_show)
end

function FuBenHorCell:OnClickOtherBtn()
	if IsEmptyTable(self.data) then
		return
	end

	local data = self.data.data
	if data == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.WeiKaiQi)
		return
	end

	local icon_id = self.data.ver_index * 10 + self.index
	local fun_name = FunOpen.Instance:GetFunNameByViewNameAndNumberIndex(GuideModuleName.FuBenPanel, icon_id)
	local fun_cfg = FunOpen.Instance:GetFunByName(fun_name)
	local str = FunOpen.Instance:GetFunUnOpenCommonTip(fun_cfg)
	SysMsgWGCtrl.Instance:ErrorRemind(str)
end

function FuBenHorCell:GetLimitStr()
	local ver_index = self.data.ver_index
	local icon_id = ver_index * 10 + self.index
	local fun_name = FunOpen.Instance:GetFunNameByViewNameAndNumberIndex(GuideModuleName.FuBenPanel, icon_id)
	local fun_cfg = FunOpen.Instance:GetFunByName(fun_name)
	if fun_cfg == nil then
		return Language.Common.FunOpenTip
	end

	if fun_cfg.trigger_type == GuideTriggerType.LevelUp then
		local limit_lv = tonumber(fun_cfg.trigger_param) or 0
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if limit_lv > RoleWGData.GetRoleMaxLevel() then
			return string.format(Language.Common.FunOpenTip)
		else
			if limit_lv > role_level then
				return string.format(Language.FuBenPanel.TabbarLimitStr1, RoleWGData.GetLevelString(limit_lv))
			else
				if fun_cfg.cross_gs_count and fun_cfg.cross_gs_count > 1 then
					return string.format(Language.Common.FunOpenCrossCount, fun_cfg.cross_gs_count)
				else
					return string.format(Language.FuBenPanel.TabbarLimitStr1, RoleWGData.GetLevelString(limit_lv))
				end
			end
		end
	elseif fun_cfg.trigger_type == GuideTriggerType.CommitTask or fun_cfg.trigger_type == GuideTriggerType.AcceptTask then
		if TaskWGData.Instance:GetTaskIsCompleted(fun_cfg.trigger_param) then
			if fun_cfg.cross_gs_count and fun_cfg.cross_gs_count > 1 then
				return string.format(Language.Common.FunOpenCrossCount, fun_cfg.cross_gs_count)
			else
				return string.format(Language.FuBenPanel.TabbarLimitStr2, RoleWGData.GetLevelString(tonumber(fun_cfg.task_level) or 0))
			end
		else
			return string.format(Language.FuBenPanel.TabbarLimitStr2, RoleWGData.GetLevelString(tonumber(fun_cfg.task_level) or 0))
		end
	end

	return Language.Common.FunOpenTip
end

function FuBenHorCell:SetDuoBeiData()
	if IsEmptyTable(self.data) then
		return
	end

	local ver_index = self.data.ver_index
	local task_list = Language.FuBenPanel.TabSub3
	local task_type = task_list[ver_index] and task_list[ver_index][self.index] or -1

	if self.node_list.duobei ~= nil then
		local is_duobei, times = ActivityWGData.Instance:IsDuoBeiTimes(task_type)
		if is_duobei then
			self.node_list.duobei:SetActive(true)
			-- self.node_list.duobei_value.text.text = string.format(Language.FuBenPanel.DuoBei2, times)
		else
			self.node_list.duobei:SetActive(false)
		end
	end
end

--修真路经验特殊处理
function FuBenHorCell:SetJingYanData()
	if IsEmptyTable(self.data) then
		return
	end

	local ver_index = self.data.ver_index
	local task_list = Language.FuBenPanel.TabSub3
	local task_type = task_list[ver_index] and task_list[ver_index][self.index] or -1

	self.node_list.jingyan:SetActive(false)
	if task_type == RATSDUOBEI_TASK.FANRENXIUXIAN then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local fuben_level = FuBenPanelWGData.Instance:GetFBExpShowLv()
		local is_duobei, times = ActivityWGData.Instance:IsDuoBeiTimes(task_type)
		self.node_list.jingyan:SetActive(role_level <= fuben_level and not is_duobei)
	end
end