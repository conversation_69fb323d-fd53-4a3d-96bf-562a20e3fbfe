HaoliWanzhangView = HaoliWanzhangView or BaseClass(SafeBaseView)

local SLDER_VALUE = {0.1, 0.3, 0.5, 0.67, 0.85, 1}

function HaoliWanzhangView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
	
    self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/haoli_wanzhang_ui_prefab", "layout_haoli_wanzhang")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function HaoliWanzhangView:OpenCallBack()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG, OA_HAOLI_WANZHANG_OPERATE_TYPE.OA_HAOLI_WANZHANG_OPERATE_TYPE_INFO)
end

function HaoliWanzhangView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.HaoliWanzhang.ViewName

	local other_cfg = HaoliWanzhangWGData.Instance:GetTipShowShopCfg()
	local bundle, asset = ResPath.GetRawImagesJPG(other_cfg.bg_res)
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

    if not self.all_reward_list then
        self.all_reward_list = {}

        for i = 1, HaoliWanzhangWGData.MAX_REWARD_COUNT do
            self.all_reward_list[i] = HaoliWanzhangCell.New(self.node_list["reward_suit_item" .. i])
            self.all_reward_list[i]:SetIndex(i)
        end
    end

    --加载模型时装
	if not self.role_model then
        self.role_model = RoleModel.New()

		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.role_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.role_model)
	end

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    XUI.AddClickEventListener(self.node_list["everyday_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_skill_show"], BindTool.Bind(self.OnBtnShowSkill, self))

	self:FlushEndTime()
end

function HaoliWanzhangView:FlushEndTime()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG)

    if CountDownManager.Instance:HasCountDown("haoli_wanzhang_end_time") then
        CountDownManager.Instance:RemoveCountDown("haoli_wanzhang_end_time")
    end

    if time > 0 then
        CountDownManager.Instance:AddCountDown("haoli_wanzhang_end_time",
            BindTool.Bind(self.UpdateCountDown, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function HaoliWanzhangView:UpdateCountDown(elapse_time, total_time)
	if self.node_list["activity_time"] then
		local time = math.ceil(total_time - elapse_time)
		local time_str = TimeUtil.FormatSecondDHM8(time)
		self.node_list["activity_time"].text.text = string.format(Language.HaoliWanzhang.VientianeActTime, time_str)
	end
end

function HaoliWanzhangView:OnComplete()
	if self.node_list.activity_time then
		self.node_list.activity_time.text.text = ""
	end

    self:Close()
end

function HaoliWanzhangView:ReleaseCallBack()
    if self.all_reward_list then
		for k, v in pairs(self.all_reward_list) do
            v:DeleteMe()
        end

        self.all_reward_list = nil
	end

    if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

    if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.xianwa_model then
		self.xianwa_model:DeleteMe()
		self.xianwa_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

	if CountDownManager.Instance:HasCountDown("haoli_wanzhang_end_time") then
        CountDownManager.Instance:RemoveCountDown("haoli_wanzhang_end_time")
    end
end

function HaoliWanzhangView:OnFlush()
	local other_cfg = HaoliWanzhangWGData.Instance:GetTipShowShopCfg()
	local bundle, asset = ResPath.GetF2RawImagesPNG(other_cfg.title_res)
	
	self.node_list.tianyin_title.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.tianyin_title.raw_image:SetNativeSize()
    end)

    local cur_integral = HaoliWanzhangWGData.Instance:GetCurScore()
    local cur_integral_show = cur_integral / RECHARGE_BILI
	self.node_list.cur_integral.text.text = string.format(Language.HaoliWanzhang.CurIntegral, cur_integral_show)

	local cur_round = HaoliWanzhangWGData.Instance:GetCurRoundNum() + 1
	local total_round = HaoliWanzhangWGData.Instance:GetTotalRoundNum() + 1
    self.node_list.text_round.text.text = string.format(Language.HaoliWanzhang.RoundNumStr, cur_round, total_round)

	local is_buy_free = HaoliWanzhangWGData.Instance:GetShopIsBuyFlag()
	self.node_list.gift_is_get:SetActive(is_buy_free)
    self.node_list.gift_remind:SetActive(not is_buy_free)

	local round_cfg = HaoliWanzhangWGData.Instance:GetCurRoundRewardCfg()
	if not IsEmptyTable(round_cfg) then
		local count = #self.all_reward_list
		for k, v in ipairs(self.all_reward_list) do
			v:SetData(round_cfg[k])
		end
	end

	self:FlushOperationActModel()
	self:FlushSuitModel()
end

function HaoliWanzhangView:FlushOperationActModel()
    local cfg = HaoliWanzhangWGData.Instance:GetCurShowWaistCfg()

    if not cfg then
		return
	end

	local display_data = {}
	display_data.should_ani = false				-- 后续需要上下动就加配置
	display_data.hide_model_block = false
	display_data.render_type = cfg.model_show_type - 1
	display_data.is_show_model = true
	display_data.need_wp_tween = true

	if display_data.render_type == OARenderType.FZ or display_data.render_type == OARenderType.CangMing then
		display_data.waist_type = cfg.waist_type
	end

	display_data.model_click_func = function ()
		if cfg.model_show_itemid == nil or type(cfg.model_show_itemid) == "string" or cfg.model_show_itemid <= 0 then
			return
		end

		TipWGCtrl.Instance:OpenItem({item_id = cfg.model_show_itemid})
	end
	self.model_display:SetData(display_data)

	local pos_x, pos_y, pos_z = 0, 52, 0
	if cfg.display_pos and cfg.display_pos ~= "" then
		local pos_list = string.split(cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end

	local rot_x, rot_y, rot_z = 0, 0, 0
	if cfg.display_rotation and cfg.display_rotation ~= "" then
		local rot_list = string.split(cfg.display_rotation, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
	end

	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.display_root.rect, pos_x, pos_y, pos_z)
	self.node_list.display_root.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)

	local scale = cfg.display_scale
	scale = (scale and scale ~= "" and scale > 0) and scale or 1
	Transform.SetLocalScaleXYZ(self.node_list.display_root.transform, scale, scale, scale)
end


function HaoliWanzhangView:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = HaoliWanzhangWGData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG, OA_HAOLI_WANZHANG_OPERATE_TYPE.OA_HAOLI_WANZHANG_OPERATE_TYPE_EVERYDAY_REWARD)
	end
end



-- function HaoliWanzhangView:FlushSkillBtn()
--     local cfg = HaoliWanzhangWGData.Instance:GetCurShowWaistCfg()
--     if not cfg then
-- 		return
-- 	end
-- 	local show_skill = false
-- 	local model_show_type = tonumber(cfg["model_show_type"]) or 1
-- 	local render_model = model_show_type - 1
-- 	if render_model == OARenderType.FZ then
-- 		local skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(cfg.waist_type, 1)[1] or 0
-- 		if skill_id ~= 0 then
-- 			show_skill = true
-- 			local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
-- 			local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
-- 			self.node_list.skill_icon.image:LoadSpriteAsync(bundle, asset, function ()
-- 				self.node_list.skill_icon.image:SetNativeSize()
-- 			end)
-- 		end
-- 	elseif render_model == OARenderType.CangMing then
-- 		local waist_cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(cfg.waist_type)
-- 		if waist_cfg then
-- 			local skill_level = 1
-- 			local skill_cfg = SkillWGData.Instance:GetHaloSkillById(waist_cfg.skill_id, skill_level)
-- 			if skill_cfg then
-- 				local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_cfg.skill_id, skill_level)
-- 				if clien_skill_cfg then
-- 					show_skill = true
-- 					local s_bundle, s_asset = ResPath.GetSkillIconById(clien_skill_cfg.icon_resource)
-- 					self.node_list.skill_icon.image:LoadSprite(s_bundle, s_asset, function ()
-- 						self.node_list.skill_icon.image:SetNativeSize()
-- 					end)
-- 				end
-- 			end
-- 		end
-- 	end

-- 	if not show_skill then
-- 		print_error("找不到领域或沧溟直购活动的技能")
-- 	end
-- 	self.node_list.btn_skill_show:CustomSetActive(show_skill)
-- end

function HaoliWanzhangView:FlushSuitModel()
    local data_list = HaoliWanzhangWGData.Instance:GetActivationPartList()
	if IsEmptyTable(data_list) then
		return
	end

	self.role_model:RemoveAllModel()

    self.node_list["lc_root"]:SetActive(false)
	self.node_list["xw_root"]:SetActive(false)
	self.node_list["mount_root"]:SetActive(false)

	local body_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	local animation_name = SceneObjAnimator.Walk
	local has_fashion_show = false
    local fashion_cfg

	for k, data in pairs(data_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then		-- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)

			if fashion_cfg then																		-- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then	-- 足迹
			animation_name = SceneObjAnimator.Move
		elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.SHENBING then
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)

			if fashion_cfg then
				weapon_res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(fashion_cfg.resouce)
			end
		end
	end

	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		weapon_res_id = weapon_res_id,
		animation_name = animation_name,
    }
	self.role_model:SetRoleResid(body_res_id, function ()
		self.role_model:PlayRoleAction(animation_name)
	end, extra_role_model_data)

	for k, v in pairs(data_list) do
		self:ShowModelByData(v)
	end

    self:ChangeModelShowScale()
end

function HaoliWanzhangView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end

	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then				-- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then			-- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then 		-- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then		-- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then	-- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then	-- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then		-- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then		-- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then	-- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			-- elseif data.param1 == SHIZHUANG_TYPE.SHENBING then	-- 武器
			-- 	res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
			-- 	self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then		-- 足迹
				self.role_model:SetFootTrailModel(res_id)
				-- self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				self.role_model:PlayRoleAction(SceneObjAnimator.Move)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then		-- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end

	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then			-- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			self:SetXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then   -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.active_id)
		end
	end
end

function HaoliWanzhangView:SetLingChongModelData(type, res_id)
	self.node_list["lc_root"]:SetActive(true)
	if nil == self.lingchong_model then
		self.lingchong_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["lc_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		self.lingchong_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.lingchong_model)
	else
		if self.lingchong_model then
			self.lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)
	self.lingchong_model:SetMainAsset(bundle, asset, function()
		self.lingchong_model:PlaySoulAction()
	end)

	self.lingchong_model:FixToOrthographic(self.root_node_transform)
end

function HaoliWanzhangView:SetXianWaModelData(res_id)
	self.node_list["xw_root"]:SetActive(true)
	if nil == self.xianwa_model then
		self.xianwa_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["xw_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		self.xianwa_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.xianwa_model)
	else
		if self.xianwa_model then
			self.xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.xianwa_model:SetMainAsset(bundle, asset, function()
		self.xianwa_model:PlaySoulAction()
	end)

	self.xianwa_model:FixToOrthographic(self.root_node_transform)
end

function HaoliWanzhangView:SetMountModelData(res_id)
	self.node_list["mount_root"]:SetActive(true)
	if nil == self.mount_model then
		self.mount_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["mount_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.mount_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.mount_model)
	else
		if self.mount_model then
			self.mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

--[[
function HaoliWanzhangView:Update(now_time, elapse_time)
	if not self.is_foot_view then
		return
	end

	if self.next_create_footprint_time == 0 then
        self:CreateFootPrint()
        self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
    end

    if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
        self.next_create_footprint_time = 0
    end

    if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
        self.next_create_footprint_time = 0
    end

    self:UpdateFootprintPos()
end

function HaoliWanzhangView:CreateFootPrint()
	if nil == self.foot_effect_id then
		return
	end

	if nil == self.footprint_eff_t then
		self.footprint_eff_t = {}
	end

    local pos = self.role_model.draw_obj:GetRoot().transform
    local bundle, asset = ResPath.GetFootUIEffect(self.foot_effect_id)
	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
		if obj then
			if nil ~= obj then
				if self.role_model then
					obj.transform.localPosition = Vector3.zero
					obj:SetActive(false)
					obj:SetActive(true)
					table.insert(self.footprint_eff_t, {obj = obj, role_model = self.role_model})
					self.role_model:OnAddGameobject(obj)
				else
					ResPoolMgr:Release(obj)
				end
			end
		end
   	end)

	if #self.footprint_eff_t > 2 then
		local obj = table.remove(self.footprint_eff_t, 1)
		obj.role_model:OnRemoveGameObject(obj.obj)
		if not IsNil(obj.obj) then
			obj.obj:SetActive(false)
		end
	end
end

function HaoliWanzhangView:UpdateFootprintPos()
	if nil == self.footprint_eff_t then
		return
	end

	for k,v in pairs(self.footprint_eff_t) do
		if not IsNil(v.obj) then
			local pos = v.obj.transform.localPosition
			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
		end
	end
end

function HaoliWanzhangView:ClearFootEff()
	if self.footprint_eff_t ~= nil then
		for k,v in pairs(self.footprint_eff_t) do
			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
				v.role_model:OnRemoveGameObject(v.obj)
				v.obj:SetActive(false)
			end
		end
	end

	self.footprint_eff_t = {}
end
]]

function HaoliWanzhangView:ChangeModelShowScale()
	local data = HaoliWanzhangWGData.Instance:GetCurRoundModelCfg()
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_pos
    if pos_str and pos_str ~= "" then
        local pos = Split(pos_str, "|")
        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    local rotate_str = data.main_rot
    if rotate_str and rotate_str ~= "" then
        local rot = Split(rotate_str, "|")
        if self.role_model then
			self.role_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
		end
    end

    local scale = data.main_scale
    if scale and scale ~= "" then
    	RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
    end

    --灵宠
    if self.node_list["lc_root"]:GetActive() then
    	pos_str = data.pet_pos
	    if pos_str and pos_str ~= "" then
	        local pos = Split(pos_str, "|")
	        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	    end

	    rotate_str = data.pet_rot
	    if rotate_str and rotate_str ~= "" then
	        local rot = Split(rotate_str, "|")
	        if self.lingchong_model then
				self.lingchong_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
	    end

	    scale = data.lc_scale
	    if scale and scale ~= "" then
	    	RectTransform.SetLocalScale(self.node_list.lc_display.rect, scale)
	    end
    end

    --仙娃
    if self.node_list["xw_root"]:GetActive() then
    	pos_str = data.xw_pos
	    if pos_str and pos_str ~= "" then
	        local pos = Split(pos_str, "|")
	        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	    end

	    rotate_str = data.xw_rot
	    if rotate_str and rotate_str ~= "" then
	        local rot = Split(rotate_str, "|")
	        if self.xianwa_model then
				self.xianwa_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
	    end

	    scale = data.xw_scale
	    if scale and scale ~= "" then
	    	RectTransform.SetLocalScale(self.node_list.xw_display.rect, scale)
	    end
    end

    --坐骑
    if self.node_list["mount_display"]:GetActive() then
    	pos_str = data.mount_pos
	    if pos_str and pos_str ~= "" then
	        local pos = Split(pos_str, "|")
	        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mount_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	    end

	    rotate_str = data.mount_rot
	    if rotate_str and rotate_str ~= "" then
	        local rot = Split(rotate_str, "|")
	        if self.mount_model then
				self.mount_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
	    end

	    scale = data.mount_scale
	    if scale and scale ~= "" then
	    	RectTransform.SetLocalScale(self.node_list.mount_display.rect, scale)
	    end
    end
end

function HaoliWanzhangView:OnBtnShowSkill()
	local cfg = HaoliWanzhangWGData.Instance:GetCurShowWaistCfg()
    if not cfg then
		return
	end

	local render_model =cfg.model_show_type - 1
	if render_model == OARenderType.FZ then
		local data = SupremeFieldsWGData.Instance:SkillShowCfgList(cfg.waist_type, 1)
		CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
	elseif render_model == OARenderType.CangMing then
		local waist_cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(cfg.waist_type)
		if not waist_cfg then
			return
		end
	
		local data = {
			level = 1,
			skill_id = waist_cfg.skill_id,
			skill_halo_id = waist_cfg.type,
		}
		CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
	end
end

-----------------------------------HaoliWanzhangCell-----------------------------------
HaoliWanzhangCell = HaoliWanzhangCell or BaseClass(BaseRender)

function HaoliWanzhangCell:LoadCallBack()
	if not self.reward_item then
		self.reward_item = ItemCell.New(self.node_list["tianyin_item"])
	end

	self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function HaoliWanzhangCell:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function HaoliWanzhangCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local item_data = self.data.reward_item and self.data.reward_item[0] or {}
	self.reward_item:SetFlushCallBack(function ()
		self.reward_item:SetCellBgEnabled(false)
		self.reward_item:SetShowCualityBg(false)
		self.reward_item:SetDefaultEff(false)
		self.reward_item:SetBindIconVisible(false)
	end)
	self.reward_item:SetShowCualityBg(false)
	self.reward_item:SetData(item_data)

	local is_get = HaoliWanzhangWGData.Instance:GetRewardStateBySeq(self.data.seq)
	self.node_list.tianyin_is_get:SetActive(is_get)
	local recharge_value = self.data.need_lingyu / RECHARGE_BILI
	self.node_list.tianyin_integral.text.text = string.format(Language.HaoliWanzhang.RechargeStr, recharge_value)
	
	local score = HaoliWanzhangWGData.Instance:GetCurScore()
	local need_lingyu = self.data.need_lingyu
	-- self.node_list.get_btn:SetActive(need_lingyu <= score)

	local remind = not is_get and need_lingyu <= score
	self.node_list.is_remind:SetActive(remind)
	self.node_list.get_btn:SetActive(remind)

	-- if not is_get and need_lingyu <= score then
	-- 	self.node_list.is_remind:SetActive(true)
	-- 	self.node_list.get_btn:SetActive(true)
	-- else
	-- 	self.node_list.is_remind:SetActive(false)
	-- 	self.node_list.get_btn:SetActive(false)
	-- end
end

function HaoliWanzhangCell:OnClickGetReward()
	if IsEmptyTable(self.data) then
		return
	end

    local is_get = HaoliWanzhangWGData.Instance:GetRewardStateBySeq(self.data.seq)
    if is_get then
        self.node_list.tianyin_is_get:SetActive(true)
        TipWGCtrl.Instance:ShowSystemMsg(Language.HaoliWanzhang.VientianeIsGetReward)
        return
    end

	local score = HaoliWanzhangWGData.Instance:GetCurScore()
    local need_lingyu = self.data.need_lingyu

    if score >= need_lingyu then
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG, OA_HAOLI_WANZHANG_OPERATE_TYPE.OA_HAOLI_WANZHANG_OPERATE_TYPE_REWARD, self.data.seq)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.HaoliWanzhang.VientianeScoreLack)
    end
end
