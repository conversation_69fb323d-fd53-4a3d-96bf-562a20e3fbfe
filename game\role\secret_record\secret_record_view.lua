SecretRecordView = SecretRecordView or BaseClass(SafeBaseView)

function SecretRecordView:__init()
	self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/secret_record_ui_prefab", "layout_secret_record")
    self.time_state = 1  -- 1-今日  0-昨日
end

function SecretRecordView:__delete()

end

function SecretRecordView:ReleaseCallBack()
	if self.add_zhanli_source_list then
		self.add_zhanli_source_list:DeleteMe()
		self.add_zhanli_source_list = nil
	end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    self.time_state = 1
end

function SecretRecordView:CloseCallBack()

end

function SecretRecordView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.switch_btn, BindTool.Bind(self.OnClickSwitchBtn, self))
    XUI.AddClickEventListener(self.node_list.bottle_img_btn, BindTool.Bind(self.OnClickBottleBtn, self))
    XUI.AddClickEventListener(self.node_list.upgrade_btn, BindTool.Bind(self.OnClickUpgradeBtn, self))

    if nil == self.add_zhanli_source_list then
		self.add_zhanli_source_list = AsyncBaseGrid.New()
		self.add_zhanli_source_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["add_zhanli_source_list"],
		assetBundle = "uis/view/secret_record_ui_prefab", assetName = "zhanli_source_item",  itemRender = SecretRecordItemRender})
		self.add_zhanli_source_list:SetStartZeroIndex(false)
	end

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(false)
    end
end

function SecretRecordView:ShowIndexCallBack()
    SecretRecordWGCtrl.Instance:SendSecretRecordReq(SECRET_RECORD_OPERATE_TYPE.CHANGE_DAY, self.time_state)
    self:PlayAnim()
end

function SecretRecordView:PlayAnim()
    self.node_list.content_bg:SetActive(false)
    UITween.MoveToShowPanel(GuideModuleName.SecretRecordView, self.node_list.title_bg, Vector2(-441, -660), Vector2(-441, 3), 0.5, DG.Tweening.Ease.Linear, 
    function ()
        UITween.MoveToShowPanel(GuideModuleName.SecretRecordView, self.node_list.content_bg, Vector2(-878, 1), Vector2(-5, 1), 0.5, DG.Tweening.Ease.Linear)
        self.node_list.content_bg:SetActive(true)
    end)
end

function SecretRecordView:OnFlush()
	local secret_record_data = SecretRecordWGData.Instance
    local total_capability = secret_record_data:GetTotalCapability()
    local capability_list = secret_record_data:GetCapabilityList()
    local item_list = secret_record_data:GetItemList()

	local bottle_info = secret_record_data:GetBottleInfo()
	local level_info = secret_record_data:GetLevelCfgByLevel(bottle_info.level or 0)

	if nil == next(level_info) or nil == next(bottle_info) then return end
	-- 刷新进度条
	local cap = math.min(bottle_info.exp, level_info.need_exp)
	local percent = (cap / level_info.need_exp)
	self.node_list["Slider"].slider.value = percent
	-- 刷新等级
	local max_level = secret_record_data:GetMaxLevel()
	local text = ""
	if bottle_info.level >= max_level then
		text = Language.SecretRecord.MaxLevelText
		self.node_list["btn_text"].text.text = text
		XUI.SetButtonEnabled(self.node_list["upgrade_btn"], false)
	else
		text = string.format(Language.SecretRecord.Exp, bottle_info.exp, level_info.need_exp)
	end

    self.node_list["add_zhanli_text"].text.text = CommonDataManager.ConverExpByThousand(total_capability)
    self.node_list["bottle_level_text"].text.text = string.format(Language.SecretRecord.Level, bottle_info.level)
	self.node_list["slider_text"].text.text = text
    self.node_list["cur_day_text"].text.text = self.time_state == 1 and Language.SecretRecord.Day1 or Language.SecretRecord.Day2
    self.node_list["title_des"].text.text = self.time_state == 1 and Language.SecretRecord.DayDes1 or Language.SecretRecord.DayDes2

    if self.add_zhanli_source_list then
		self.add_zhanli_source_list:SetDataList(capability_list)
	end

	self.reward_list:SetDataList(item_list)

	self.node_list.red_point:SetActive(secret_record_data:GetSecretRecordRemind() ~= 0)
end

function SecretRecordView:OnClickSwitchBtn()
    self.time_state = self.time_state == 1 and 0 or 1
    SecretRecordWGCtrl.Instance:SendSecretRecordReq(SECRET_RECORD_OPERATE_TYPE.CHANGE_DAY, self.time_state)
end

function SecretRecordView:OnClickBottleBtn()
    SecretRecordWGCtrl.Instance:OpenTipsView()
end

function SecretRecordView:OnClickUpgradeBtn()
    BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_UPLEVEL)
end

----------------------------------新增战力来源item-----------------------
SecretRecordItemRender = SecretRecordItemRender or BaseClass(BaseRender)
function SecretRecordItemRender:__init()

end

function SecretRecordItemRender:LoadCallBack()

end

function SecretRecordItemRender:ReleaseCallBack()

end

function SecretRecordItemRender:OnFlush()
    if not self.data then return end

    local total_capability = SecretRecordWGData.Instance:GetTotalCapability()
    local capability_percent = total_capability ~= 0 and (self.data.cap / total_capability) * 100 or 0
    capability_percent = capability_percent - capability_percent % 0.1 -- 保留一位小数

    if Language.SecretRecord.NameList[self.index] then
        self.node_list.name.text.text = self.data.name .. "："
    end
    self.node_list.zhanli_value.text.text = CommonDataManager.ConverExpByThousand(self.data.cap)
    self.node_list.zhanli_percent.text.text = "（" .. capability_percent .. "%）"
end