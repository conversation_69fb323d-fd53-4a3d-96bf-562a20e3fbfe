require("game/guide/function_guide")
require("game/guide/normal_guide_view")
require("game/guide/normal_area_guide_view")
require("game/guide/guide_disable_click_view")
require("game/guide/girl_guide_view")
require("game/guide/fun_gesture_view")
require("game/guide/task_guide")
require("game/guide/automatic_use_equipment_view")
require("game/guide/key_use_view")
require("game/guide/market_pop_use_view")
require("game/guide/funopen")
require("game/guide/fade_view")
require("game/guide/offline_guaji_use_view")
require("game/guide/master_envelope_view")
require("game/guide/guide_sprite_born")
require("game/guide/fun_mowang_view")

-- 引导
GuideWGCtrl = GuideWGCtrl or BaseClass(BaseWGCtrl)

function GuideWGCtrl:__init()
	if GuideWGCtrl.Instance ~= nil then
		ErrorLog("[GuideWGCtrl] attempt to create singleton twice!")
		return
	end
	GuideWGCtrl.Instance = self

	self.fun_open = FunOpen.New()

	self.task_guide = TaskGuide.New()
	self.function_guide = FunctionGuide.New()
	self.master_envelope = MasterEnvelopeView.New(GuideModuleName.MasterEnvelopeView)
	self.guide_sprite_born = GuideSpriteBorn.New(GuideModuleName.GuideSpriteBorn)
	self:RegisterAllProtocals()
end

function GuideWGCtrl:__delete()
	self.fun_open:DeleteMe()
	self.fun_open = nil

	self.function_guide:DeleteMe()
	self.function_guide = nil

	self.task_guide:DeleteMe()
	self.task_guide = nil

	self.master_envelope:DeleteMe()
	self.master_envelope = nil

	self.guide_sprite_born:DeleteMe()
	self.guide_sprite_born = nil

	self.guide_flag = nil
	GuideWGCtrl.Instance = nil
end

function GuideWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSCommonGuideOperate)
	self:RegisterProtocol(SCCommonGuideInfo, "OnSCCommonGuideInfo")
	self:RegisterProtocol(SCCommonGuideUpdate, "OnSCCommonGuideUpdate")

	self:RegisterProtocol(SCSystemOpenLimitInfo, "OnSCSystemOpenLimitInfo")
	self:RegisterProtocol(SCSystemOpenLimitUpdate, "OnSCSystemOpenLimitUpdate")
end

function GuideWGCtrl:OnAllWGCtrlInited()
	self.fun_open:OnAllWGCtrlInited()
end

---------------------------------------数据信息保存--------------------------------------------
COMMON_GUIDE_OPERATE_BIT = {
	HIT_HAMSTER = 0,					-- 打地鼠新手记录
}

-- 数据信息保存请求
function GuideWGCtrl:SendCSCommonGuideOperate(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCommonGuideOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 请求信息
function GuideWGCtrl:SendWhackMoleGameEnd()
	self:SendCSCommonGuideOperate(COMMON_GUIDE_OPERATE_TYPE.COMMON_GUIDE_OPERATE_TYPE_INFO)
end

-- 请求设置(比特位和位置 0-256)
function GuideWGCtrl:SendCommonGuideSet(bit, is_set)
	self:SendCSCommonGuideOperate(COMMON_GUIDE_OPERATE_TYPE.COMMON_GUIDE_OPERATE_TYPE_SET, bit, is_set)
end

-- 数据信息
function GuideWGCtrl:OnSCCommonGuideInfo(protocol)
	self.guide_flag = protocol.guide_flag
end

-- 数据信息更新
function GuideWGCtrl:OnSCCommonGuideUpdate(protocol)
	if (not self.guide_flag) or (not self.guide_flag[protocol.bit]) then
		return
	end

	self.guide_flag[protocol.bit] = protocol.is_set
end

-- 获取当前比特位的设置值
function GuideWGCtrl:GetCommonGuideSet(bit)
	local empty = {}
	return (self.guide_flag or empty)[bit] or 0
end

function GuideWGCtrl:OnSCSystemOpenLimitInfo(protocol)
	--print_error("OnSCSystemOpenLimitInfo==",protocol)
	self.fun_open:SetSystemOpenLimitInfo(protocol)
end

function GuideWGCtrl:OnSCSystemOpenLimitUpdate(protocol)
	--print_error("OnSCSystemOpenLimitUpdate==",protocol)

	self.fun_open:SetSystemOpenLimitUpdate(protocol)

end
---------------------------------------------------------------------------------------