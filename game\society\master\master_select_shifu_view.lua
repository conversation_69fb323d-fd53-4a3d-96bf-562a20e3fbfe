MasterSelectMasterView = MasterSelectMasterView or BaseClass(SafeBaseView)

function MasterSelectMasterView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
	self.master_info = nil
end

function MasterSelectMasterView:__delete()

end

function MasterSelectMasterView:ReleaseCallBack()
	self.accept_name_txt = nil
end

function MasterSelectMasterView:LoadConfig()
	self:AddViewResource(0, "uis/view/master_ui_prefab", "layout_baishi_select")
end

function MasterSelectMasterView:LoadCallBack()
	self.accept_name_txt = self.node_list["lbl_accept_name_txt"]
	self.node_list["lbl_tips"].text.text = Language.Master.MasterSelectMasterTips

	XUI.AddClickEventListener(self.node_list["btn_select_gift"], BindTool.Bind1(self.Select<PERSON>ift<PERSON><PERSON><PERSON>, self))
	XUI.AddClickEventListener(self.node_list["img_autoname_0"], BindTool.Bind1(self.OpenFriendList, self))
end

function MasterSelectMasterView:SetData(data)
	self.data = data
end

--打开好友列表
function MasterSelectMasterView:OpenFriendList()
	SocietyWGCtrl.Instance:OpenFriendListView(9494,BindTool.Bind1(self.SelectFriendCallBack, self))
end

function MasterSelectMasterView:SelectFriendCallBack(user_info)
	if user_info then
		self.master_info = user_info
	end
	local friend_name = user_info.username or user_info.gamename or user_info.role_name or ""
	self.accept_name_txt.text.text = friend_name
end

function MasterSelectMasterView:ShowIndexCallBack()
	self.master_info = nil
	self.accept_name_txt.text.text = Language.Master.MasterSelectMasterStr
	if self.data then
		self.accept_name_txt.text.text = self.data.gamename
	end
end

function MasterSelectMasterView:SelectGiftHandler()
	if self.master_info or self.data then
		local can_mry,str = MasterWGData.Instance:CheckCanBaishi(self.master_info or self.data, false)
		if can_mry then
			MasterWGCtrl.Instance:OpenBaishiView(self.master_info and self.master_info.user_id or self.data.user_id, str)
			self:Close()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Master.MasterNoMasterTips)
	end
end

function MasterSelectMasterView:CloseCallBack()
	self.data = nil
end