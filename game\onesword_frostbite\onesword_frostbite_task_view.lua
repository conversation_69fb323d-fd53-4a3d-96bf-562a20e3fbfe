OneSwordFrostbiteTaskView = OneSwordFrostbiteTaskView or BaseClass(SafeBaseView)

function OneSwordFrostbiteTaskView:__init()
	self:SetMaskBg(true)

    self:AddViewResource(0, "uis/view/onesword_frostbite_ui_prefab", "layout_onesword_frostbite_task_view")

    self.is_loaded = false
end

function OneSwordFrostbiteTaskView:ReleaseCallBack()
	if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	if self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.OneSwordFrostbiteTaskView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

    self.is_loaded = false
end

function OneSwordFrostbiteTaskView:LoadCallBack()
    self.task_list = AsyncListView.New(OneSwordFrostbiteTaskRender, self.node_list["task_list"])
    self.is_loaded = true
	--self:DoCellsAnim()

	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.OneSwordFrostbiteTaskView, self.get_guide_ui_event)
end

function OneSwordFrostbiteTaskView:OpenCallBack()
    if self.is_loaded then
        --self:DoCellsAnim()
    end
end

function OneSwordFrostbiteTaskView:OnFlush()
    local task_list = OneSwordFrostbiteWGData.Instance:GetTaskList()
	if task_list then
		self.task_list:SetDataList(task_list)
	end
end

function OneSwordFrostbiteTaskView:DoCellsAnim()
	local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender
	self.node_list["task_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["task_list"]:SetActive(true)
        local list =  self.task_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "pursuit_cell_tween")
end

function OneSwordFrostbiteTaskView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.OneSwordFrostbiteTaskList then
        if nil == self.task_list then
			return
		end

		local cell_index = ui_param
		if cell_index >= 0 then
			local item = self.task_list:GetItemAt(cell_index)
			if item then
				return item.node_list.btn_lingqu
			end
		end
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

----------------------------------------------------------------------------------------------------

OneSwordFrostbiteTaskRender = OneSwordFrostbiteTaskRender or BaseClass(BaseRender)

function OneSwordFrostbiteTaskRender:LoadCallBack()
	self.item_list = {}
	self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardHandler, self))
	self.node_list["btn_go"].button:AddClickListener(BindTool.Bind1(self.OnClickGoHandler, self))
	self.task_reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function OneSwordFrostbiteTaskRender:__delete()
	if self.task_reward_list then
		self.task_reward_list:DeleteMe()
		self.task_reward_list = nil
	end
end

function OneSwordFrostbiteTaskRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list["condition"].text.text = self:GetTaskDesc()
	self.node_list["btn_lingqu"]:SetActive(self.data.can_receive)
	self.node_list["btn_yilingqu"]:SetActive(self.data.is_received)
	self.node_list["btn_go"]:SetActive(not self.data.can_receive and not self.data.is_received and self.data.cfg.panel ~= "")

	self.task_reward_list:SetDataList(SortTableKey(self.data.cfg.reward_item))
end

function OneSwordFrostbiteTaskRender:OnClickRewardHandler()
	OneSwordFrostbiteWGCtrl.Instance:SendOneSwordFrostbiteReq(ONESWORD_FROSTBITE_TYPE.OA_SWORD_FROSTBITE_OPERATE_TYPE_FETCH_TASK_REWARD, self.data.seq)
end

function OneSwordFrostbiteTaskRender:OnClickGoHandler()
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end

function OneSwordFrostbiteTaskRender:GetTaskDesc()
	local str = self.data.cfg.task_desc
	local param = self.data.process
	local pos = string.find(str, "%%s")
	if pos ~= nil then
		local color = param >= self.data.cfg.param1 and "<color=#3c8652>%d</color>" or "<color=#A93C3C>%d</color>"
		param = math.min(param, self.data.cfg.param1)
		str = string.format(str, string.format(color, param))
	end
	return str
end

function OneSwordFrostbiteTaskRender:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.WorldTreasureView, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "pursuit_task_item_" .. wait_index)
end