
HotSpringSceneLogic = HotSpringSceneLogic or BaseClass(CommonFbLogic)

function HotSpringSceneLogic:__init()
end

function HotSpringSceneLogic:__delete()
	if self.is_cancel_shuangxiu_alert then
		self.is_cancel_shuangxiu_alert:DeleteMe()
		self.is_cancel_shuangxiu_alert = nil
	end

	if self.shaung_xiu_state_change then
		GlobalEventSystem:UnBind(self.shaung_xiu_state_change)
		self.shaung_xiu_state_change = nil
	end
	self.move_cache = nil
	self.do_gather = nil
	self.old_atk_model = nil
end

function HotSpringSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- self.old_atk_model = MainuiWGCtrl.Instance:GetAttckMode()
	local callback = function ()
		local mcl = MainuiWGCtrl.Instance
		local ctrl = HotSpringWGCtrl.Instance
        mcl:SetTaskContents(false)
        mcl:SetOtherContents(true)
		mcl:SetFBNameState(true, Language.HotSpring.HotSpring)
		mcl:SetTeamBtnState(false)

		-- mcl:SendSetAttackMode(ATTACK_MODE.PEACE)
		mcl:SetSkillShowState(false)
		mcl:SetButtonModeClick(false)
		

		if not IS_ON_CROSSSERVER then
			self:OpenActivitySceneCd(ACTIVITY_TYPE.HOTSPRING)
		else
			self:OpenActivitySceneCd(ACTIVITY_TYPE.KF_HOTSPRING)
		end

		
		ctrl:FbInfoOpen()
		ctrl:SkillViewOpen()
		ctrl:SetAllRoleAppearance()
		-- 所有下坐骑(普通和战斗坐骑)
		MountWGCtrl.Instance:AllDownMount()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)

		HotSpringWGCtrl.Instance:OpenGatherTimerView()
	end
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_vo.shuangxiu_partner_obj > 0 then
		HotSpringWGCtrl.Instance:SendHotspringAction(main_vo.shuangxiu_partner_obj, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_DISMISS_SHUANGXIU)
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:ShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU)
		main_role:SetShuangxiuPartner(-1)
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil, callback)
	self.shaung_xiu_state_change = GlobalEventSystem:Bind(HotSpring.SHUANGXIU_STATE,BindTool.Bind(self.CacheCancelMove, self))

	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))
	if new_scene_type == SceneType.KF_HotSpring then
		self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
		ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	end
end

function HotSpringSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end
	local is_cross = Scene.Instance:GetSceneType() == SceneType.KF_HotSpring
	local cfg = ConfigManager.Instance:GetAutoConfig(is_cross and "cross_hotspring_auto" or "hotspring_auto").other[1]
	local poss = Split(cfg.auto_pos,"|")
	if not IsEmptyTable(poss) then
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), tonumber(poss[1]) , tonumber(poss[2]))
	end
end

function HotSpringSceneLogic:Out()
	CommonFbLogic.Out(self)
    HotSpringWGCtrl.Instance:CloseRoleListView()
	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil

	local callback = function ()
		local mcl = MainuiWGCtrl.Instance
		local ctrl = HotSpringWGCtrl.Instance
		mcl:SetTaskContents(true)
		mcl:SetFBNameState(false)
		mcl:SetTeamBtnState(true)
		mcl:SetButtonModeClick(true)
		mcl:SetOtherContents(false)
		-- mcl:SendSetAttackMode(self.old_atk_model)
		ctrl:FbInfoClose()
		ctrl:SkillViewClose()
		HotSpringWGData.Instance:ClearShuangXiuAllList()
		local main_role = Scene.Instance:GetMainRole()
		if main_role.vo.is_in_hot_spring then
			main_role.vo.is_in_hot_spring = false
		end

		if main_role.vo.is_in_snow then
			main_role.vo.is_in_snow = false
		end
		local vo = GameVoManager.Instance:GetMainRoleVo()
		vo.special_appearance = 0

		main_role:ShuangXiuState(SHUANGXIU_TYPE.NO_SHUANGXIU)
		main_role:SetShuangxiuPartner(-1)

		main_role:SetAnmoState(ANMO_TYPE.NO_ANMO)
		main_role:SetAnmoPartnerId(-1)

		main_role.vo.shuangxiu_attack_state = -1
		main_role.vo.shuangxiu_action_type = -1
		main_role.vo.is_hotspring_actor = -1
		mcl:SetSkillShowState(true)
		HotSpringWGCtrl.Instance:CloseProgressView()
		HotSpringWGCtrl.Instance:CloseGatherTimerView()
	end
	if self.shaung_xiu_state_change then
		GlobalEventSystem:UnBind(self.shaung_xiu_state_change)
		self.shaung_xiu_state_change = nil
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
end

function HotSpringSceneLogic:IsCanCheckWaterArea()
	return true
end

function HotSpringSceneLogic:RoleCanShowHp(role)
	return false
end

function HotSpringSceneLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function HotSpringSceneLogic:OpenActivitySceneCd(act_type)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if nil ~= activity_info then
		local flag = activity_info.status == ACTIVITY_STATUS.STANDY
		if flag then
			FuBenPanelWGCtrl.Instance:SetCountDowmType( nil ,GameEnum.FU_BEN_ZHUNBEI )
		end
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time,flag, flag and GameEnum.FU_BEN_ZHUNBEI or GameEnum.FU_BEN_OUT_SCENE)
	end
end

function HotSpringSceneLogic:GetWaterWayOffset()
	return 1.8
end

function HotSpringSceneLogic:GetIsCanMove( ... )
	local role = GameVoManager.Instance:GetMainRoleVo()
	local cache = {...}
 	if role.is_in_snow then return end

	if role.anmo_state == ANMO_TYPE.IS_ANMO then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_3)
		return false
	end
	
	if role.shuangxiu_state == SHUANGXIU_TYPE.IS_SHUANGXIU then
		self:GetCancelAlert(function()
			HotSpringWGCtrl.Instance:SendHotspringAction(role.shuangxiu_partner_obj, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_DISMISS_SHUANGXIU)
	 		self.move_cache = cache
		end)
		return false
	end
	return true
end

function HotSpringSceneLogic:CacheCancelMove()
	if self.move_cache then
		Scene.Instance:GetMainRole():DoMoveOperate(
			self.move_cache[1],
			self.move_cache[2],
			self.move_cache[3],
			self.move_cache[4],
			self.move_cache[5])
		self.move_cache = nil
	end
	if self.do_gather and MoveCache.end_type == MoveEndType.Gather then
		GuajiWGCtrl.Instance:OnOperate()
		self.do_gather = nil
	end
end

function HotSpringSceneLogic:GetIsCanGather()
	local role = GameVoManager.Instance:GetMainRoleVo()
	if role.is_in_snow then
		return false
	end

	if role.anmo_state == ANMO_TYPE.IS_ANMO then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_4)
		return false
	end

	if role.shuangxiu_state == SHUANGXIU_TYPE.IS_SHUANGXIU then
		self:GetCancelAlert(function()
			HotSpringWGCtrl.Instance:SendHotspringAction(role.shuangxiu_partner_obj, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_DISMISS_SHUANGXIU)
			self.do_gather = true
		end)
		return false
	end

	local times = HotSpringWGData.Instance:GetGatherInfo()
	local other_cfg = HotSpringWGData.Instance:GetHotSpringOtherCfg()
	if times >= other_cfg.gather_role_count then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.GatherTimers)
		return false
	end
	return true
end

function HotSpringSceneLogic:GetCancelAlert(callback)
	if nil == self.is_cancel_shuangxiu_alert then
		self.is_cancel_shuangxiu_alert = Alert.New(nil, nil, nil, nil, false, nil, false)
		self.is_cancel_shuangxiu_alert:SetLableString(Language.HotSpring.CancelShuangXiuAlert)
	end
	self.is_cancel_shuangxiu_alert:SetOkFunc(callback)
	self.is_cancel_shuangxiu_alert:Open()
end

function HotSpringSceneLogic:ForceShieldPet()
	return true
end

function HotSpringSceneLogic:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	-- 跨服温泉期间如果小鸭疾走开启了，则进入小鸭疾走场景
	if activity_type == ACTIVITY_TYPE.KF_DUCK_RACE 
		and status == ACTIVITY_STATUS.OPEN then
		DuckRaceWGCtrl.Instance:SendEnterScene()
	end
end