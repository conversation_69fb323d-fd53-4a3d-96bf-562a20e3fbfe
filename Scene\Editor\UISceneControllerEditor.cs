using DG.DemiEditor;
using Sirenix.OdinInspector.Editor;
using System.Collections.Generic;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using SceneEnvironment;
using Unity.VisualScripting.FullSerializer;
using UnityEditor;
using UnityEngine;
using static UISceneController;

[CustomEditor(typeof(UISceneController))]
public class UISceneControllerEditor : OdinEditor
{
    private int selectedTabIndex = 0;
    private string newConfigName;
    private SceneConfiguration clipBoard;
    private bool showTransfromConfigs = true;
    private bool showActiveStateConfigs = true;
    private bool showMeshRendererMaterialConfigs = true;
    private bool showCameraConfig = true;
    private bool showEnvironmentConfig = true;
    void OnEnable()
    {
        UISceneController controller = (UISceneController)target;
        selectedTabIndex = controller.currentConfigIndex;
    }

    public override void OnInspectorGUI()
    {
        UISceneController controller = (UISceneController)target;
        if (controller.configurations != null && controller.configurations.Count > 0)
        {
            if (GUILayout.Button("当场景Mian节点下的一二级节点增删，点击此按钮，同步更新所有配置"))
            {
                controller.IncrementalUpdateAllConfiguration();
            }

            GUILayout.Space(10);
            int newSelectedTabIndex = EditorGUILayout.Popup(selectedTabIndex, GetConfigNames(controller));
            if (newSelectedTabIndex != selectedTabIndex)
            {
                selectedTabIndex = newSelectedTabIndex;
                controller.currentConfigIndex = selectedTabIndex;
                controller.SetConfiguration(selectedTabIndex);
            }

            // 显示选中的配置信息
            if (selectedTabIndex >= 0 && selectedTabIndex < controller.configurations.Count)
            {
                SceneConfiguration config = controller.configurations[selectedTabIndex];
                config.configName = EditorGUILayout.TextField("配置名", config.configName);
                GUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button("应用当前场景节点状态更新配置"))
                    {
                        controller.UpdateCurrentConfiguration(config);
                    }

                    if (GUILayout.Button("移除配置"))
                    {
                        controller.configurations.RemoveAt(selectedTabIndex);
                        selectedTabIndex = Mathf.Clamp(selectedTabIndex - 1, 0, controller.configurations.Count - 1);
                        return;
                    }
                }
                GUILayout.EndHorizontal();
                
                GUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button("复制配置"))
                    {
                        clipBoard = config;
                    }
                    if (GUILayout.Button("粘贴配置") && clipBoard != null)
                    {
                        controller.SetConfiguration(clipBoard);
                        controller.UpdateCurrentConfiguration(config);
                    }
                }
                GUILayout.EndHorizontal();
                
                GUILayout.Space(10);

                GUIStyle boxStyle = GUI.skin.box;
                showTransfromConfigs = EditorGUILayout.Foldout(showTransfromConfigs, "Transform 配置", true);
                if (showTransfromConfigs)
                {
                    GUILayout.BeginVertical(boxStyle);
                    foreach (var transConfig in config.transformConfigs)
                    {
                        EditorGUILayout.ObjectField(transConfig.obj, typeof(GameObject), true);
                        transConfig.position = EditorGUILayout.Vector3Field("Position", transConfig.position);
                        Vector3 newRotation = EditorGUILayout.Vector3Field("Rotation", transConfig.rotation.eulerAngles);
                        transConfig.rotation = Quaternion.Euler(newRotation);
                        transConfig.scale = EditorGUILayout.Vector3Field("Scale", transConfig.scale);

                        GUILayout.Space(5);
                    }
                    GUILayout.EndVertical();
                }

                GUILayout.Space(10);
                showActiveStateConfigs = EditorGUILayout.Foldout(showActiveStateConfigs, "激活状态 配置", true);
                if (showActiveStateConfigs)
                {
                    GUILayout.BeginVertical(boxStyle);
                    foreach (var activeConfig in config.activeStateConfigs)
                    {
                        GUILayout.BeginHorizontal();
                        activeConfig.isActive = EditorGUILayout.Toggle(activeConfig.isActive, GUILayout.Width(20));
                        EditorGUILayout.ObjectField(activeConfig.obj, typeof(GameObject), true);
                        GUILayout.EndHorizontal();
                        GUILayout.Space(5);
                    }
                    GUILayout.EndVertical();
                }

                GUILayout.Space(10);
                GUILayout.BeginHorizontal();
                showMeshRendererMaterialConfigs = EditorGUILayout.Foldout(showMeshRendererMaterialConfigs, "MeshRenderer 材质配置", true);
                if (showMeshRendererMaterialConfigs && GUILayout.Button("+", GUILayout.Width(20)))
                {
                    config.meshRendererMaterialConfigs.Add(new MeshRendererMaterialConfig
                    {
                        obj = null,
                        materials = new List<Material>(),
                    });
                }
                GUILayout.EndHorizontal();

                if(showMeshRendererMaterialConfigs)
                {
                    GUILayout.BeginVertical(boxStyle);
                    {
                        for (int i = 0; i < config.meshRendererMaterialConfigs.Count; i++)
                        {
                            var mrmConfig = config.meshRendererMaterialConfigs[i];
                            GUILayout.BeginHorizontal();
                            mrmConfig.obj = (GameObject)EditorGUILayout.ObjectField(mrmConfig.obj, typeof(GameObject), true);
                            if (GUILayout.Button("-", GUILayout.Width(20)))
                            {
                                config.meshRendererMaterialConfigs.RemoveAt(i);
                                i--;
                                continue;
                            }
                            GUILayout.EndHorizontal();

                            if (mrmConfig.obj != null)
                            {
                                if (mrmConfig.materials.Count == 0)
                                {
                                    MeshRenderer meshRenderer = mrmConfig.obj.GetComponent<MeshRenderer>();
                                    if (meshRenderer != null)
                                        mrmConfig.materials.AddRange(meshRenderer.sharedMaterials);
                                }

                                GUILayout.BeginHorizontal();
                                GUILayout.Label("Materials", EditorStyles.boldLabel);
                                if (GUILayout.Button("+", GUILayout.Width(20)))
                                {
                                    mrmConfig.materials.Add(null);
                                }
                                GUILayout.EndHorizontal();

                                for (int j = 0; j < mrmConfig.materials.Count; j++)
                                {
                                    GUILayout.BeginHorizontal();
                                    mrmConfig.materials[j] = (Material)EditorGUILayout.ObjectField(mrmConfig.materials[j], typeof(Material), false);
                                    if (GUILayout.Button("-", GUILayout.Width(20)))
                                    {
                                        mrmConfig.materials.RemoveAt(j);
                                        j--;
                                    }
                                    GUILayout.EndHorizontal();
                                }
                            }

                            GUILayout.Space(10);
                        }
                    }
                    GUILayout.EndVertical();
                }
                
                GUILayout.Space(10);
                
                showCameraConfig = EditorGUILayout.Foldout(showCameraConfig, "相机 配置", true);
                if (showCameraConfig)
                {
                    var camConfig = config.cameraConfig;
                    camConfig.fieldOfView = EditorGUILayout.Slider("field of view",camConfig.fieldOfView, 0.01f, 179.99f);
                    config.cameraConfig = camConfig;
                }
                
                GUILayout.Space(10);

                showEnvironmentConfig = EditorGUILayout.Foldout(showEnvironmentConfig, "渲染环境 配置", true);
                if (showEnvironmentConfig)
                {
                    GUILayout.BeginVertical(boxStyle);
                    config.environmentData = (EnvironmentData)EditorGUILayout.ObjectField("Environemnt", config.environmentData, typeof(EnvironmentData));
                    config.characterData = (CharacterData)EditorGUILayout.ObjectField("Character", config.characterData, typeof(CharacterData));
                    GUILayout.EndVertical();
                }
                
                GUILayout.Space(10);
            }
        }
        
        if (string.IsNullOrEmpty(newConfigName))
        {
            newConfigName = "新配置 " + (controller.configurations.Count + 1);
        }

        GUILayout.BeginHorizontal();
        newConfigName = EditorGUILayout.TextField("应用当前场景节点状态生成新配置", newConfigName);
        if (GUILayout.Button("添加新配置"))
        {
            if (string.IsNullOrEmpty(newConfigName))
            {
                newConfigName = "新配置 " + (controller.configurations.Count + 1);
            }

            controller.SaveCurrentConfiguration(newConfigName);
            selectedTabIndex = controller.configurations.Count - 1;
            controller.currentConfigIndex = selectedTabIndex;
            newConfigName = "新配置 " + (controller.configurations.Count + 1);
        }
        GUILayout.EndHorizontal();

        if (GUI.changed)
        {
            EditorUtility.SetDirty(controller);
        }
    }

    // 获取所有配置的名称列表
    private string[] GetConfigNames(UISceneController controller)
    {
        string[] names = new string[controller.configurations.Count];
        for (int i = 0; i < controller.configurations.Count; i++)
        {
            names[i] = $"{controller.configurations[i].configName}  id:{i}";
        }
        return names;
    }
}
