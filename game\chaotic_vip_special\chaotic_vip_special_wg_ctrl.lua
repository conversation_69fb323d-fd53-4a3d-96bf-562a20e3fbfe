require("game/chaotic_vip_special/chaotic_vip_special_view")
require("game/chaotic_vip_special/chaotic_vip_special_wg_data")

ChaoticVipSpecialWGCtrl = ChaoticVipSpecialWGCtrl or BaseClass(BaseWGCtrl)
function ChaoticVipSpecialWGCtrl:__init()
    if nil ~= ChaoticVipSpecialWGCtrl.Instance then
        ErrorLog("[ChaoticVipSpecialWGCtrl]:Attempt to create singleton twice!")
    end
    ChaoticVipSpecialWGCtrl.Instance = self

	self.view = ChaoticVipSpecialView.New(GuideModuleName.ChaoticVipSpecialView)
	self.data = ChaoticVipSpecialWGData.New()
	self:RegisterAllProtocals()
end

function ChaoticVipSpecialWGCtrl:__delete()
    ChaoticVipSpecialWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function ChaoticVipSpecialWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCChaoticGiftVipSpecialInfo, "OnSCChaoticGiftVipSpecialInfo")
end

function ChaoticVipSpecialWGCtrl:OnSCChaoticGiftVipSpecialInfo(protocol)
	--print_error("=======vip========",protocol)
	self.data:SetChaoticVipSpecialInfo(protocol)
	RemindManager.Instance:Fire(RemindName.ChaoticVipSpecial)
	ViewManager.Instance:FlushView(GuideModuleName.ChaoticVipSpecialView)
	MainuiWGCtrl.Instance:FlushView(0,"chaotic_vip_special")
end

function ChaoticVipSpecialWGCtrl:SendChaoticVipSpecialReq(opera_type, param_1, param_2, param_3)
	HongHuangGoodCoremonyWGCtrl.Instance:SendHongHuangGoodCoremonyReq(opera_type, param_1, param_2, param_3)
end