XiuXianShiLianView = XiuXianShiLianView or BaseClass(SafeBaseView)

-- 目前一章最多展示多少个任务
local MAX_TASK_NUM = 4
local GuaJi_Item_id = SPECIAL_GUAJICARD.IDTWO
local SHOW_STATE = {
    Normal = 1,
    Special = 2,
}

-- /jy_gm completeiuxiantask:2 任务id
function XiuXianShiLianView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Window
	self.is_safe_area_adapter = true
    self.view_name = "XiuXianShiLian"
    self:SetMaskBgAlpha(60 / 255)
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/xiuxianshilian_ui_prefab", "layout_feixianshu")
end

function XiuXianShiLianView:LoadCallBack()
    self.cur_show_state = 0
    self.is_playing_open_view_ani = false
    self.show_panel_original_pos_x, self.show_panel_original_pos_y = RectTransform.GetAnchoredPositionXY(self.node_list.show_panel.rect)

    if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model_pos)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(XunXianRewardItem, self.node_list.reward_item_list)
		self.reward_item_list:SetStartZeroIndex(true)
	end

    if not self.chapter_reward_item_list then
		self.chapter_reward_item_list = AsyncListView.New(ItemCell, self.node_list.chapter_reward_item_list)
		self.chapter_reward_item_list:SetStartZeroIndex(true)
	end

    if not self.task_list then
		self.task_list = {}
        local root = self.node_list.task_list
        for i = 1, MAX_TASK_NUM do
            local cell = XiuXianShiLianViewTaskRender.New(root:FindObj("task_item" .. i))
            cell:SetIndex(i)
            cell:SetClickCallBack(BindTool.Bind(self.OnSelectTaskItem, self, true))
            self.task_list[i] = cell
        end
	end

    XUI.AddClickEventListener(self.node_list.option_btn, BindTool.Bind(self.OnClickOptionBtn, self))
    XUI.AddClickEventListener(self.node_list.unlock_btn, BindTool.Bind(self.OnClickUnLockBtn, self))
    XUI.AddClickEventListener(self.node_list.show_tip_btn, BindTool.Bind(self.OnClickShowTipBtn, self))
    self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.XiuXianShiLian, self.get_guide_ui_event)
end

function XiuXianShiLianView:ReleaseCallBack()
    self:CleanOpenViewTween()
    self:CleanEnterSpecialStateTween()
    self:CleanUnLockTween()
    self:RemoveDelayCloseTimer()
    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.XiuXianShiLian, self.get_guide_ui_event)
    self.get_guide_ui_event = nil

    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
    end

    if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
    end

    if self.chapter_reward_item_list then
		self.chapter_reward_item_list:DeleteMe()
		self.chapter_reward_item_list = nil
    end

    if self.task_list then
        for k, v in pairs(self.task_list) do
            v:DeleteMe()
        end

        self.task_list = nil
    end
end

function XiuXianShiLianView:ShowIndexCallBack()
    self.cur_show_chapter = 0
    self.cur_select_task_index = 0
    self:ResetNodeTransform(true)
end

function XiuXianShiLianView:CloseCallBack()
    if self.tween_timer and CountDown.Instance:HasCountDown(self.tween_timer) then
        CountDown.Instance:RemoveCountDown(self.tween_timer)
        self.tween_timer = nil
    end
end

function XiuXianShiLianView:OnFlush(param_t)
    if self.cur_show_chapter == 0 then
        self.cur_show_chapter = XiuXianShiLianWGData.Instance:GetLastChapterIndex()
        self:CheckCurShowState(true)
        self:FlushChapterInfo()
        self:FlushTaskList()
        self:PlayOpenViewAnimation()
    else
        self:CheckCurShowState()
    end

    for k,v in pairs(param_t) do
        if k == "all" then
            self:FlushChapterInfo()
            self:FlushTaskList()
        elseif k == "protocol_update" then
            -- 判断是否进新章节，如果是则播放解封动画，并在完成后回调切换状态并再次刷新界面
            if self:CheckCurShowChapter() then
                self:PlayUnLockAnimation()
            else
                self:FlushChapterInfo()
                self:FlushTaskList()
            end
        elseif k == "auto" then
            self:TriggerCloseOperate()
        end
	end
end

-------------- 刷新相关
function XiuXianShiLianView:FlushChapterInfo()
    if self.is_playing_open_view_ani then
        return
    end

    local chapter_data =  XiuXianShiLianWGData.Instance:GetXiuXianShiLianInfoByCaptherId(self.cur_show_chapter)
    if not chapter_data then
        return
    end

    self.node_list.chapter_name.text.text = chapter_data.chapter_name
    self.node_list.chapter_progress.text.text = string.format(Language.XiuXianShiLian.ChapterProgress, chapter_data.cur_task_index, chapter_data.all_task_count)
    self:FlushModel()
end

function XiuXianShiLianView:FlushModel()
    local display_data = XiuXianShiLianWGData.Instance:GetModelShowInfo(self.cur_show_chapter)
    if not IsEmptyTable(display_data) then
        self.model_display:SetData(display_data)
        self.node_list.img_bg:CustomSetActive(display_data.render_type > 0)
    end
end

function XiuXianShiLianView:FlushTaskList()
    if self.is_playing_open_view_ani then
        return
    end

    local default_index
    local jump_index
    local task_data_list = XiuXianShiLianWGData.Instance:GetShowViewTaskListInfoById(self.cur_show_chapter)
    for k, v in ipairs(self.task_list) do
        local cell_data = task_data_list[k]
        v:SetData(cell_data)
        if cell_data and cell_data.is_complete == 0 and not default_index then
            default_index = k
        end

        if cell_data and cell_data.has_fetched_reward == 0 and cell_data.is_complete == 1 and (not jump_index or self.cur_select_task_index == k) then
            jump_index = k
        end
    end

    if self.cur_select_task_index == 0 then
        self:OnSelectTaskItem(false, self.task_list[(jump_index or default_index or 1)])
    elseif (jump_index and self.cur_select_task_index ~= jump_index) or (default_index and self.cur_select_task_index ~= default_index) then
        self:OnSelectTaskItem(false, self.task_list[(jump_index or default_index)])
    else
        self:FlushTaskPanel()
    end
end

function XiuXianShiLianView:FlushTaskPanel()
    if self.cur_show_state == SHOW_STATE.Normal then
        local cell_data = self:GetCurSelectTaskCellData()
        if cell_data then
            local task_cfg = XiuXianShiLianWGData.Instance:GetTaskCfgByTaskType(cell_data.task_type)
            if task_cfg then
                self.node_list.task_title.text.text = Language.XiuXianShiLian.TaskTitle
                self.node_list.task_des.text.text = task_cfg.task_des
                local color = cell_data.progress >= task_cfg.param1 and COLOR3B.C8 or COLOR3B.C10
                self.node_list.task_progress.text.text = string.format(Language.XiuXianShiLian.ViewTaskProgress, color, cell_data.progress, task_cfg.param1)
                if task_cfg.reward_type == 0 then
                    local reward_list = {}
                    for k, v in pairs(task_cfg.reward_item) do
                        reward_list[k] = {
                            item_id = v.item_id,
                            num = v.num,
                            is_bind = v.is_bind,
                            is_received = cell_data.has_fetched_reward == 1
                        }
                    end

                    self.reward_item_list:SetDataList(reward_list)
                elseif task_cfg.reward_type == 1 then
                    self.reward_item_list:SetDataList({[0] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = task_cfg.money_param, is_received = cell_data.has_fetched_reward == 1}})
                elseif task_cfg.reward_type == 2 then
                    self.reward_item_list:SetDataList({[0] = {item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD, num = task_cfg.money_param, is_received = cell_data.has_fetched_reward == 1}})
                end
            end

            self.node_list.option_btn:CustomSetActive(cell_data.has_fetched_reward == 0)
            self.node_list.option_btn_remind:CustomSetActive(cell_data.has_fetched_reward == 0 and cell_data.is_complete == 1)
            self.node_list.fetch_flag:CustomSetActive(cell_data.has_fetched_reward == 1)
            self.node_list.task_des_scroll.scroll_rect.verticalNormalizedPosition = 1
            local btn_str_index = (cell_data.has_fetched_reward == 0 and cell_data.is_complete == 1) and 2 or 1
            self.node_list.option_btn_txt.text.text = Language.XiuXianShiLian.OptionBtnStr[btn_str_index]
        end
    elseif self.cur_show_state == SHOW_STATE.Special and not self.is_playing_open_view_ani then
        local chapter_data =  XiuXianShiLianWGData.Instance:GetXiuXianShiLianInfoByCaptherId(self.cur_show_chapter)
        if chapter_data then
            self.node_list.chapter_task_title.text.text = chapter_data.chapter_name
            self.node_list.chapter_task_des.text.text = chapter_data.chapter_des or ""
            self.node_list.chapter_task_des_scroll.scroll_rect.verticalNormalizedPosition = 1
            self.chapter_reward_item_list:SetDataList(chapter_data.item_list)
        end
    end

    self.node_list.task_panel:CustomSetActive(self.cur_show_state == SHOW_STATE.Normal)
    self.node_list.chapter_task_panel:CustomSetActive(self.cur_show_state == SHOW_STATE.Special and not self.is_playing_open_view_ani)
end

-------------- 点击相关
function XiuXianShiLianView:OnClickOptionBtn()
    local cell_data = self:GetCurSelectTaskCellData()
    if not cell_data then
        return
    end

    if cell_data.is_complete == 1 then
        XiuXianShiLianWGCtrl.Instance:SendXiuXianOper(XiuXianShiLianWGData.XIUXIAN_OPER_TYPE.XIUXIAN_OPER_TYPE_FETCH_REWARD, cell_data.task_type)
    else
        local task_cfg = XiuXianShiLianWGData.Instance:GetTaskCfgByTaskType(cell_data.task_type)
        if not task_cfg then
            return
        end

        if task_cfg.condition_type == XiuXianShiLianWGData.CONDITION_TYPE.CAP then
            if FunctionGuide.Instance:TriggerXiuXianShiLianCapGoGuide() then
                return
            end
        end

        if task_cfg.go_type == 1 then
            local param_tab = Split(task_cfg.go_param, "#")--FunOpen:.Instance:GetOpenParam(cfg.go_param)
            if param_tab ~= nil and param_tab[1] == FunName.Guild then --仙盟相关的功能,没加入仙盟认为没开启
                if RoleWGData.Instance.role_vo.guild_id == 0 then
                    GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
                    self:Close()
                else
                    FunOpen.Instance:OpenViewNameByCfg(task_cfg.go_param)
                    self:Close()
                end
            else
                FunOpen.Instance:OpenViewNameByCfg(task_cfg.go_param)
                self:Close()
            end
        elseif task_cfg.go_type == 2 then --挂机小符人特殊处理
            local num = ItemWGData.Instance:GetItemNumInBagById(GuaJi_Item_id)
            if num > 0 then
                FunOpen.Instance:OpenViewNameByCfg("bag_view#rolebag_bag_all")
                self:Close()
            else
                local tab_index = 50 --绑玉商店特殊处理
                ShopWGCtrl.Instance:ShopJumpToItemByIDAndTabIndex(GuaJi_Item_id, tab_index)
                self:Close()
            end
        elseif task_cfg.go_type == 3 then --世界聊天特殊处理
            local txt = XiuXianShiLianWGData.Instance:GetRandomContent()
            ViewManager.Instance:Open(GuideModuleName.ChatView, 30, nil ,{open_param = txt} )
            self:Close()
        end
    end
end

function XiuXianShiLianView:OnClickUnLockBtn()
    local chapter_info = XiuXianShiLianWGData.Instance:GetXiuXianShiLianInfoByCaptherId(self.cur_show_chapter)
    if chapter_info then
        if not chapter_info.can_get_reward and chapter_info.is_fetched == 0 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.XiuXianShiLian.CannotGet)
        elseif chapter_info.is_fetched == 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.XiuXianShiLian.AllGet)
        else
            XiuXianShiLianWGCtrl.Instance:SendXiuXianOper(XiuXianShiLianWGData.XIUXIAN_OPER_TYPE.XIUXIAN_OPER_TYPE_CHAPTER_REWARD, self.cur_show_chapter)
        end
    end
end

function XiuXianShiLianView:OnClickShowTipBtn()
    local cfg = XiuXianShiLianWGData.Instance:GetModelShowCfg(self.cur_show_chapter)
    if not cfg then
        return
    end

    TipWGCtrl.Instance:OpenItem({item_id = cfg.unlock_item})
end

function XiuXianShiLianView:OnSelectTaskItem(is_click, cell)
    if not cell then
        return
    end

    local cell_index = cell:GetIndex()
    if cell_index == self.cur_select_task_index and is_click then
        return
    end

    self.cur_select_task_index = cell_index
    self:FlushTaskPanel()
    for k, v in pairs(self.task_list) do
        v:OnSelectChange(k == self.cur_select_task_index)
    end
end

-------------- 动画相关
function XiuXianShiLianView:PlayOpenViewAnimation()
    if self.cur_show_state == SHOW_STATE.Special then
        self:TryPlayEnterSpecialStateAnimation(true)
        for k, v in pairs(self.task_list) do
            v:SetCellAlpha(1)
        end
        return
    end

    local display_data = XiuXianShiLianWGData.Instance:GetModelShowInfo(self.cur_show_chapter)
    if IsEmptyTable(display_data) then
        return
    end

    if display_data.render_type == OARenderType.RoleModel then
        self:PlayCellAlphaAni()
    else
        self:CleanOpenViewTween()
        self.is_playing_open_view_ani = true
        self.open_view_sequence = DG.Tweening.DOTween.Sequence()
        local pos_x, pos_y = RectTransform.GetAnchoredPositionXY(self.node_list.model_pos.rect)
        RectTransform.SetAnchoredPositionXY(self.node_list.model_pos.rect, pos_x, pos_y - 90)
        local img_pos_x, img_pos_y = RectTransform.GetAnchoredPositionXY(self.node_list.img_bg.rect)
        RectTransform.SetAnchoredPositionXY(self.node_list.img_bg.rect, img_pos_x, img_pos_y - 90)
        local ani_time = 0.5
        local move_tween = self.node_list.model_pos.rect:DOAnchorPosY(pos_y, ani_time)
        local img_move_tween = self.node_list.img_bg.rect:DOAnchorPosY(img_pos_y, ani_time)
        self.open_view_sequence:Append(move_tween)
        self.open_view_sequence:Join(img_move_tween)
        self.node_list.model_pos.canvas_group.alpha = 0.6
        local alpah_tween = self.node_list.model_pos.canvas_group:DoAlpha(0.6, 1, ani_time)
        self.open_view_sequence:Join(alpah_tween)
        self.open_view_sequence:OnComplete(function ()
            self.is_playing_open_view_ani = false
            self:PlayCellAlphaAni()
            self:TryPlayEnterSpecialStateAnimation(true)
        end)
    end
end

function XiuXianShiLianView:PlayCellAlphaAni()
    if self.tween_timer and CountDown.Instance:HasCountDown(self.tween_timer) then
        CountDown.Instance:RemoveCountDown(self.tween_timer)
        self.tween_timer = nil
    end

    local play_ani_cell_index = 1
    local try_call_cell_ani_func = function ()
        if self.task_list[play_ani_cell_index] then
            self.task_list[play_ani_cell_index]:PlayShowAni()
            play_ani_cell_index = play_ani_cell_index + 1
        end
    end

    try_call_cell_ani_func()
    self.tween_timer = CountDown.Instance:AddCountDown(1.4, 0.2,
        function()
            try_call_cell_ani_func()
        end)
end

function XiuXianShiLianView:CleanOpenViewTween()
    if self.open_view_sequence then
        self.open_view_sequence:Kill()
        self.open_view_sequence = nil
    end
end

function XiuXianShiLianView:TryPlayEnterSpecialStateAnimation(is_skip)
    -- 如果正在播放打开界面的动画则等动画播完后的回调
    if self.is_playing_open_view_ani then
        return
    end

    self:CleanEnterSpecialStateTween()
    if self.cur_show_state == SHOW_STATE.Special then
        self.node_list.ani_black_mask:CustomSetActive(true)
        self.node_list.show_name_panel:CustomSetActive(true)
        self.node_list.show_name.text.text = string.format(Language.XiuXianShiLian.ChapterTitle, NumberToChinaNumber(self.cur_show_chapter))
        self:FlushTaskPanel()
        if is_skip then
            self.node_list.effect_panel:CustomSetActive(true)
            RectTransform.SetAnchoredPositionXY(self.node_list.show_panel.rect, 0, 0)
            RectTransform.SetLocalScale(self.node_list.show_panel.rect, 1.2)
        else
            self.enter_special_state_sequence = DG.Tweening.DOTween.Sequence()
            self.enter_special_state_sequence:Append(self.node_list.show_panel.rect:DOAnchorPos(u3dpool.vec3(0, 0, 0), 0.5))
            self.enter_special_state_sequence:Join(self.node_list.show_panel.transform:DOScale(1.2, 0.5))
            self.enter_special_state_sequence:AppendCallback(function()
                self.node_list.effect_panel:CustomSetActive(true)
            end)
        end
    end
end

function XiuXianShiLianView:CleanEnterSpecialStateTween()
    if self.enter_special_state_sequence then
        self.enter_special_state_sequence:Kill()
        self.enter_special_state_sequence = nil
    end
end

function XiuXianShiLianView:PlayUnLockAnimation()
    self:CleanUnLockTween()
    self.node_list.unlock_img:CustomSetActive(true)
    self.node_list.unlock_img.transform.localScale = u3dpool.vec3(2, 2, 2)
    self.unlock_sequence = DG.Tweening.DOTween.Sequence()
    self.unlock_sequence:Append(self.node_list.unlock_img.transform:DOScale(Vector3(1, 1, 1), 0.5))
    self.unlock_sequence:AppendInterval(1)
    self.unlock_sequence:AppendCallback(function()
        self:FlushChapterInfo()
	end)

    self.unlock_sequence:AppendInterval(0.2)
    self.unlock_sequence:SetEase(DG.Tweening.Ease.OutCubic)
    self.unlock_sequence:OnComplete(function()
        self:ResetNodeTransform()
        self:FlushTaskList()
    end)
end

function XiuXianShiLianView:CleanUnLockTween()
    if self.unlock_sequence then
        self.unlock_sequence:Kill()
        self.unlock_sequence = nil
    end
end

function XiuXianShiLianView:ResetNodeTransform(is_init)
    if is_init then
        for k, v in pairs(self.task_list) do
            v:SetCellAlpha(0)
        end
    end

    self.node_list.unlock_img:CustomSetActive(false)
    self.node_list.show_name_panel:CustomSetActive(false)
    self.node_list.effect_panel:CustomSetActive(false)
    self.node_list.ani_black_mask:CustomSetActive(false)
    RectTransform.SetAnchoredPositionXY(self.node_list.show_panel.rect, self.show_panel_original_pos_x, self.show_panel_original_pos_y)
    RectTransform.SetLocalScale(self.node_list.show_panel.rect, 1)
end

-------------- 其他
function XiuXianShiLianView:CheckCurShowChapter()
    if self.cur_show_chapter > 0 then
        local chapter_info = XiuXianShiLianWGData.Instance:GetXiuXianShiLianInfoByCaptherId(self.cur_show_chapter)
        if chapter_info and chapter_info.is_complete == 1 and chapter_info.is_fetched == 1 and chapter_info.reward_is_fetched == 1 then
            self.cur_show_chapter = XiuXianShiLianWGData.Instance:GetLastChapterIndex()
            return true
        end
    end

    return false
end

function XiuXianShiLianView:CheckCurShowState(is_open_view)
    local cur_state = SHOW_STATE.Normal
    if XiuXianShiLianWGData.Instance:GetXiuXianSkillCanActive(self.cur_show_chapter) then
        cur_state = SHOW_STATE.Special
    end

    if self.cur_show_state > 0 and self.cur_show_state == cur_state then
        return
    end

    self.cur_show_state = cur_state
    if cur_state == SHOW_STATE.Special and not is_open_view then
        self:TryPlayEnterSpecialStateAnimation()
    end
end

function XiuXianShiLianView:GetCurSelectTaskCellData()
    if self.task_list[self.cur_select_task_index] then
        return self.task_list[self.cur_select_task_index]:GetData()
    end
end

function XiuXianShiLianView:GetGuideUiCallBack(ui_name, ui_param)
    return self.node_list[ui_name], BindTool.Bind(self.OnClickUnLockBtn, self)
end

--移除回调
function XiuXianShiLianView:RemoveDelayCloseTimer()
    if self.trigger_close_timer then
        GlobalTimerQuest:CancelQuest(self.trigger_close_timer)
        self.trigger_close_timer = nil
    end

    if self.trigger_close_timer2 then
        GlobalTimerQuest:CancelQuest(self.trigger_close_timer2)
        self.trigger_close_timer2 = nil
    end
end

--延迟5秒一键升级关闭
function XiuXianShiLianView:TriggerCloseOperate()
	self:RemoveDelayCloseTimer()
	self.trigger_close_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:GetAllCanFinishTaskOperate()
	end, 3)

    self.trigger_close_timer2 = GlobalTimerQuest:AddDelayTimer(function ()
        local chapter_info = XiuXianShiLianWGData.Instance:GetXiuXianShiLianInfoByCaptherId(self.cur_show_chapter)
        if chapter_info and chapter_info.can_get_reward and chapter_info.reward_is_fetched == 0 then
            XiuXianShiLianWGCtrl.Instance:SendXiuXianOper(XiuXianShiLianWGData.XIUXIAN_OPER_TYPE.XIUXIAN_OPER_TYPE_CHAPTER_REWARD, self.cur_show_chapter)
            self:Close()
        end
	end, 5)
end

-- 一键领取当前可领取的东西
function XiuXianShiLianView:GetAllCanFinishTaskOperate()
    local data_list1 = XiuXianShiLianWGData.Instance:GetTaskListInfoById(self.cur_show_chapter)
    if data_list1 ~= nil then
        for i, data in ipairs(data_list1) do
            if data.is_complete == 1 and data.has_fetched_reward == 0 then
                XiuXianShiLianWGCtrl.Instance:SendXiuXianOper(XiuXianShiLianWGData.XIUXIAN_OPER_TYPE.XIUXIAN_OPER_TYPE_FETCH_REWARD, data.task_type)
            end
        end
    end
end

----------------------- XiuXianShiLianViewTaskRender
XiuXianShiLianViewTaskRender = XiuXianShiLianViewTaskRender or BaseClass(BaseRender)
function XiuXianShiLianViewTaskRender:LoadCallBack()
    _, self.original_pos_y = RectTransform.GetAnchoredPositionXY(self.view.rect)
    XUI.AddClickEventListener(self.node_list.button, BindTool.Bind(self.OnSelectSelf, self))
end

function XiuXianShiLianViewTaskRender:ReleaseCallBack()
    self:CleanTween()
end

function XiuXianShiLianViewTaskRender:OnFlush()
    local is_hide = IsEmptyTable(self.data)
    if is_hide then
        self:SetVisible(false)
        return
    end

    self.node_list.not_complete_flag:CustomSetActive(self.data.is_complete == 0)
    self.node_list.has_reward_flag:CustomSetActive(self.data.is_complete == 1)
    self.node_list.remind:CustomSetActive(self.data.has_fetched_reward == 0 and self.data.is_complete == 1)
    self:SetVisible(true)
end

function XiuXianShiLianViewTaskRender:OnSelectSelf()
    if self.click_callback then
        self.click_callback(self)
    end
end

function XiuXianShiLianViewTaskRender:OnSelectChange(is_select)
    if self.node_list.hl_img.gameObject.activeSelf ~= is_select then
        local move_pos_y = is_select and self.original_pos_y + 6 or self.original_pos_y
        self.view.rect:DOAnchorPosY(move_pos_y, 0.2)
        self.node_list.hl_img:SetActive(is_select)
    end
end

function XiuXianShiLianViewTaskRender:PlayShowAni()
    self:CleanTween()
    if self.view.canvas_group then
        self.alpha_tween = self.view.canvas_group:DoAlpha(0, 1, 0.2)
    end
end

function XiuXianShiLianViewTaskRender:SetCellAlpha(value)
    self.view.canvas_group.alpha = value
end

function XiuXianShiLianViewTaskRender:CleanTween()
    if self.alpha_tween then
        self.alpha_tween:Kill()
        self.alpha_tween = nil
    end
end

----------------------- XunXianRewardItem
XunXianRewardItem = XunXianRewardItem or BaseClass(BaseRender)
function XunXianRewardItem:LoadCallBack()
    if not self.show_cell then
        self.show_cell = ItemCell.New(self.node_list["item_cell_node"])
    end
end

function XunXianRewardItem:ReleaseCallBack()
    if not self.show_cell then
        self.show_cell:DeleteMe()
        self.show_cell = nil
    end
end

function XunXianRewardItem:OnFlush()
    if not self.data then
        return
    end

    self.show_cell:SetData(self.data)
end