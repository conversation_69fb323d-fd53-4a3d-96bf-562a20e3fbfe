BiZuoView = BiZuoView or BaseClass(SafeBaseView)

local ITEM_HEIGHT = 172 --一个修为itemlist的高度
local SPACING = 19      --修为itemlist之间的间距
local INTERVAL = 106    --间隔
local SUB_TYPE_CONTENT = 469 --小类型内容高度

function BiZuoView:InitCultivationView()
    self.ct_select_type = 1

    self.small_type_list = {}
    for i = 1, 10 do
        self.small_type_list[i] = BiZuoCultivationBuffListItemRender.New(self.node_list.small_type_content:FindObj("bizuo_cultivation_small_type_item" .. i))
    end

    if not self.type_list then
		self.type_list = AsyncListView.New(BiZuoCultivationBuffTypeToggleRender, self.node_list.type_list)
        self.type_list:SetSelectCallBack(BindTool.Bind(self.OnClickExpandHandler, self))
	end
end

function BiZuoView:ShowIndexCultivation()
    CultivationWGCtrl.Instance:RequestData()
	self:PlayCultivationTweenAnim()
	self:JumpIndexToSmallTypeList()
end

function BiZuoView:ReleaseCultivationrView()
    if self.type_list then
        self.type_list:DeleteMe()
        self.type_list = nil
    end

    if self.small_type_list then
		for k, v in pairs(self.small_type_list) do
            v:DeleteMe()
		end

		self.small_type_list = nil
	end
end

function BiZuoView:FlushCultivationView()
	self:FlushRender()
    self:FlushSmallTypeList()
end

function BiZuoView:FlushRender()
    local accordion_tab = CultivationWGData.Instance:GetBigAccordionTable()
	if self.type_list then
		self.type_list:SetDataList(accordion_tab)
	end

	local is_remind = CultivationWGData.Instance:GetBuffRemindByBigType(self.ct_select_type)
	if not is_remind then
		local remind_type = CultivationWGData.Instance:GetBuffRemindTypeIndex()
		if remind_type > 0 then
			self.type_list:JumpToIndex(remind_type, 0)
		end
	end
end

function BiZuoView:OnClickExpandHandler(cell)
	if not cell.data then
        return
    end

	local old_type = self.ct_select_type
    self.ct_select_type = cell.data[1].big_type
    self:FlushSmallTypeList()
	if old_type ~= self.ct_select_type then
		self:PlayCultivationTweenAnim()
		self:JumpIndexToSmallTypeList()
	end
end

function BiZuoView:PlayCultivationTweenAnim()
	UITween.CleanAlphaShow(GuideModuleName.BiZuo)
	UITween.CleanAllMoveToShowPanel(GuideModuleName.BiZuo)
	local node = self.node_list["small_type_list"]
	local obj_transform = UITween.CanvasGroup(node)
	obj_transform.alpha = 0

    ReDelayCall(self, function()
		obj_transform.alpha = 1
		UITween.AlphaShow(GuideModuleName.BiZuo, node, 0.1, 1, 0.4, DG.Tweening.Ease.Linear)
    end, 0.1, "BiZuoCultivationView")
	UITween.MoveToShowPanel(GuideModuleName.BiZuo, node, Vector2(125, 10), Vector2(125, -25), 0.3, DG.Tweening.Ease.Linear)
end

function BiZuoView:FlushSmallTypeList()
    local data_list = CultivationWGData.Instance:GetAccordionDataList(self.ct_select_type)
    --local child_data = accordion_tab[self.ct_select_type].child_data

    for i = 1, 10 do
        if i <= #data_list then
            self.node_list.small_type_content:FindObj("bizuo_cultivation_small_type_item" .. i):SetActive(true)
            self.small_type_list[i]:SetData(data_list[i])
        else
            self.node_list.small_type_content:FindObj("bizuo_cultivation_small_type_item" .. i):SetActive(false)
        end
    end
end

function BiZuoView:JumpIndexToSmallTypeList()
	local data_list = CultivationWGData.Instance:GetAccordionDataList(self.ct_select_type)
	local jump_index = CultivationWGData.Instance:GetAccordionDataListJumpIndex(data_list)
	if jump_index >= 0 then
		ReDelayCall(self, function()
			RectTransform.SetAnchoredPositionXY(self.node_list["small_type_content"].rect, 0, SUB_TYPE_CONTENT * jump_index)
		end, 0.05, "JumpIndexToSmallTypeList")
	end
end

----------------------------------------------------------------------------
-- 大类型
----------------------------------------------------------------------------
BiZuoCultivationBuffTypeToggleRender = BiZuoCultivationBuffTypeToggleRender or BaseClass(BaseRender)
function BiZuoCultivationBuffTypeToggleRender:__init()

end

function BiZuoCultivationBuffTypeToggleRender:__delete()
end

function BiZuoCultivationBuffTypeToggleRender:OnFlush()
	self.node_list.normal_text.text.text = self.data[1].type_name
	self.node_list.select_text.text.text = self.data[1].type_name

	for k, v in pairs(self.data) do
		if CultivationWGData.Instance:GetBuffRemindByType(v.type) then
			self.node_list.remind:CustomSetActive(true)
			return
		end
	end

	self.node_list.remind:CustomSetActive(false)
end

function BiZuoCultivationBuffTypeToggleRender:FlushData()
	for k, v in pairs(self.data) do
		if CultivationWGData.Instance:GetBuffRemindByType(v.type) then
			self.node_list.remind:CustomSetActive(true)
			return
		end
	end

	self.node_list.remind:CustomSetActive(false)
end

function BiZuoCultivationBuffTypeToggleRender:OnSelectChange(is_select)
	self.node_list.select_content:SetActive(is_select)
    self.node_list.normal_content:SetActive(not is_select)
end

----------------------------------------------------------------------------
-- 小类型
----------------------------------------------------------------------------
BiZuoCultivationBuffListItemRender = BiZuoCultivationBuffListItemRender or BaseClass(BaseRender)
function BiZuoCultivationBuffListItemRender:__init()
	self.parent_view = nil
	self.client_sort = nil
	self.show_state = false
end

function BiZuoCultivationBuffListItemRender:__delete()
	self.parent_view = nil
	self.client_sort = nil
	self.show_state = false

    if self.ph_xiuwei_item_list then
		self.ph_xiuwei_item_list:DeleteMe()
		self.ph_xiuwei_item_list = nil
	end
end

function BiZuoCultivationBuffListItemRender:SetParent(view)
	self.parent_view = view
end


function BiZuoCultivationBuffListItemRender:LoadCallBack()
    if nil == self.ph_xiuwei_item_list then
		self.ph_xiuwei_item_list = AsyncBaseGrid.New()
		self.ph_xiuwei_item_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_xiuwei_item_list"],
		assetBundle = "uis/view/bizuo_ui_prefab", assetName = "xiuwei_item_render",  itemRender = BiZuoCultivationBuffItemRender})
		self.ph_xiuwei_item_list:SetStartZeroIndex(false)
	end

	XUI.AddClickEventListener(self.node_list.show_more_btn, BindTool.Bind(self.OnClickShowMoreBtn, self))
	XUI.AddClickEventListener(self.node_list.show_less_btn, BindTool.Bind(self.OnClickShowLessBtn, self))
end

function BiZuoCultivationBuffListItemRender:OnClickShowMoreBtn()
	self.node_list.show_more:SetActive(false)
    self.node_list.show_less:SetActive(true)
	self.show_state = true
	local data_list = CultivationWGData.Instance:GetCultivationBuffTable(self.data.type_sort, self.data.client_sort)
	self:SetRenderHight(self.show_state, #data_list)
end

function BiZuoCultivationBuffListItemRender:OnClickShowLessBtn()
	self.node_list.show_more:SetActive(true)
    self.node_list.show_less:SetActive(false)
	self.show_state = false
	local data_list = CultivationWGData.Instance:GetCultivationBuffTable(self.data.type_sort, self.data.client_sort)
	self:SetRenderHight(self.show_state, #data_list)
end

function BiZuoCultivationBuffListItemRender:OnFlush()
	if self.data then
		self.node_list.type_text.text.text = self.data.client_type_str
        local bundle, asset =  ResPath.GetDailyTypeIcon("a3_rc_icon" .. self.data.icon_type)
        self.node_list.icon.image:LoadSprite(bundle, asset, function()
            self.node_list.icon.image:SetNativeSize()
        end)

		self.node_list.desc.text.text = Language.BiZuo.XiuWeiDesc
		self.node_list.desc:CustomSetActive(self.data.num_tips == 1)

        if self.data.type_sort == nil or self.data.client_sort == nil then
            return
        end

        local data_list = CultivationWGData.Instance:GetCultivationBuffTable(self.data.type_sort, self.data.client_sort)

		if self.client_sort ~= self.data.client_sort then
			self.client_sort = self.data.client_sort
			self.show_state = false
		end

		if #data_list <= 4 then
			self.node_list.bottom_panel:SetActive(false)
		else
			self.node_list.bottom_panel:SetActive(true)
			self.node_list.show_more:SetActive(not self.show_state)
			self.node_list.show_less:SetActive(self.show_state)
		end

		if self.SetRenderHight then
			self:SetRenderHight(self.show_state, #data_list)
		else
			ReDelayCall(self, function()
				self:SetRenderHight(self.show_state, #data_list)
			end, 1, "BiZuoCultivationBuffListItemRender")
		end
	end

	function BiZuoCultivationBuffListItemRender:SetRenderHight(type, count)
		local data_list = CultivationWGData.Instance:GetCultivationBuffTableSoft(self.data.type_sort, self.data.client_sort)
		if not type and count > 4 then
			count = 4
			local data = {}
			for i = 1, count do
				table.insert(data, data_list[i])
			end
			data_list = data
		end

		if self.ph_xiuwei_item_list then
            self.ph_xiuwei_item_list:SetDataList(data_list)
        end

		local num = math.ceil(count / 2)
        local height1 = num * ITEM_HEIGHT + (num - 1) * SPACING
        --local height2 = height1 + INTERVAL
        local rect_transform1 = self.node_list.ph_xiuwei_item_list.rect
        --local rect_transform2 = self.node_list.bizuo_cultivation_type_item.rect
        RectTransform.SetSizeDeltaXY(rect_transform1, rect_transform1.sizeDelta.x, height1)
        --RectTransform.SetSizeDeltaXY(rect_transform2, rect_transform2.sizeDelta.x, height2)
		ReDelayCall(self, function()
			self.node_list.bizuo_cultivation_type_item:SetActive(false)
        	self.node_list.bizuo_cultivation_type_item:SetActive(true)
		end, 0.01, "BiZuoCultivationBuffListItemRender")
	end
end

-------------------------------------------------------------------------------------------------
BiZuoCultivationBuffItemRender = BiZuoCultivationBuffItemRender or BaseClass(BaseRender)

function BiZuoCultivationBuffItemRender:LoadCallBack()
    --XUI.AddClickEventListener(self.node_list.btn_go,BindTool.Bind(self.OnClickBtnGo, self))
    XUI.AddClickEventListener(self.node_list.xiuwei_btn,BindTool.Bind(self.OnClickBtnXiuWei, self))
    --XUI.AddClickEventListener(self.node_list.btn_incomplete,BindTool.Bind(self.OnClickBtnInComplete, self))
	self.xiuwei_btn_block = self.node_list.xiuwei_btn:GetComponent("UIBlock")
end

function BiZuoCultivationBuffItemRender:__delete()

end

function BiZuoCultivationBuffItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	--显示百分比
	if data.value_per > 0 then
		self.node_list.xiuwei_add_value_per:CustomSetActive(true)
		self.node_list.text_add2.text.text = (data.value_per / 100) .. "%"
	else
		self.node_list.xiuwei_add_value_per:CustomSetActive(false)
	end

	if data.value_int > 0 then
		self.node_list.xiuwei_add_value:CustomSetActive(true)
		self.node_list.text_add1.text.text = data.value_int
	else
		self.node_list.xiuwei_add_value:CustomSetActive(false)
	end

	local name, desc = CultivationWGData.Instance:GetCorrectNameBySubType(data.type)
	local new_desc, is_rich, progress_text, percent = self:GetDesc(desc)
	self.node_list.text_target.text.text = new_desc
    self.node_list.progress_text.text.text = progress_text
    self.node_list.progress_slider.slider.value = percent

	if CultivationWGData.Instance:IsReceivedBuffByIndex(data.type, data.index) then
		self.xiuwei_btn_block.enabled = false
		self.node_list.receive_content:CustomSetActive(false)
		self.node_list.receive_effect:CustomSetActive(false)
        --self.node_list.btn_incomplete:CustomSetActive(false)
		--self.node_list.img_received:CustomSetActive(true)
        self.node_list.btn_go_text:CustomSetActive(false)
		self.node_list.normal_bg:CustomSetActive(false)
        self.node_list.complete_bg:CustomSetActive(true)
	else
		--self.node_list.img_received:CustomSetActive(false)
		self.xiuwei_btn_block.enabled = true
        self.node_list.btn_go_text:CustomSetActive(not is_rich)
        self.node_list.complete_bg:CustomSetActive(false)
		self.node_list.normal_bg:CustomSetActive(true)
        self.node_list.receive_content:CustomSetActive(is_rich)
		self.node_list.receive_effect:CustomSetActive(is_rich)
        --self.node_list.btn_incomplete:CustomSetActive(not is_rich)
	end
end

-- 领取
function BiZuoCultivationBuffItemRender:OnClickBtnXiuWei()
	if self.data then
		local name, desc = CultivationWGData.Instance:GetCorrectNameBySubType(self.data.type)
		local new_desc, is_rich = self:GetDesc(desc)
		if is_rich then
			CultivationWGCtrl.Instance:ReceiveCultivationExpAdd(self.data.type, self.data.index)
		else
			if not self.data.open_panel_name then
				return
			end

			local t = Split(self.data.open_panel_name, "#")
			if GuideModuleName.Equipment == t[1] then				-- 装备
				local data_list = EquipWGData.Instance:GetDataList() -- 无装备，不给打开装备面板
				if IsEmptyTable(data_list) then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NeedWearEquip)
					return
				end
			end

			FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel_name)
		end
	end
end

-- 未完成
function BiZuoCultivationBuffItemRender:OnClickBtnInComplete()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.InCompleteTip)
end

-- 前往
function BiZuoCultivationBuffItemRender:OnClickBtnGo()
    if not self.data.open_panel_name then
        return
    end

    local t = Split(self.data.open_panel_name, "#")
    if GuideModuleName.Equipment == t[1] then				-- 装备
		local data_list = EquipWGData.Instance:GetDataList() -- 无装备，不给打开装备面板
		if IsEmptyTable(data_list) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NeedWearEquip)
			return
		end
    end

    FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel_name)
end

-- 获取描述,条件是否满足判断 不同类型 获取不同数据
function BiZuoCultivationBuffItemRender:GetDesc(desc)
	local is_rich = false
	local new_desc = ""
	local progress_text = ""
    local percent
	if self.data then
		local type = self.data.type

		if type == CULTIVATION_EXP_ADD_TYPE.ROLE_LEVEL then
			local level = RoleWGData.Instance:GetRoleLevel()
			is_rich = level >=self.data.target
			progress_text = self:SetDescColor(level, self.data.target, is_rich)
            percent = level / self.data.target < 1 and level / self.data.target or 1
		elseif type >= CULTIVATION_EXP_ADD_TYPE.EQUIP and type <= CULTIVATION_EXP_ADD_TYPE.MONSTER then
			local capability = CultivationWGData.Instance:GetCapability(type)
			is_rich = capability >=self.data.target
			progress_text = self:SetDescColor(CommonDataManager.ConverNumber(capability), CommonDataManager.ConverNumber(self.data.target), is_rich )
            percent = capability / self.data.target < 1 and capability / self.data.target or 1
		elseif type == CULTIVATION_EXP_ADD_TYPE.APPEARANCE then
			local quality = self.data.target
			local need_count = self.data.target_param
			local active_count = NewAppearanceWGData.Instance:GetActivaCountByQuality(quality)
			is_rich = active_count >= need_count
			progress_text = self:SetDescColor(active_count, need_count, is_rich )
			new_desc = string.format(desc,Language.Cultivation.QualityName[quality], need_count)
            percent = active_count / need_count < 1 and active_count / need_count or 1
		elseif type == CULTIVATION_EXP_ADD_TYPE.VIP_LEVEL then
			local level = VipWGData.Instance:GetRoleVipLevel()
			is_rich = level >=self.data.target
			progress_text = self:SetDescColor(level, self.data.target, is_rich )
            percent = level / self.data.target < 1 and level / self.data.target or 1
		end

		if type ~= CULTIVATION_EXP_ADD_TYPE.APPEARANCE then
			if type >= CULTIVATION_EXP_ADD_TYPE.EQUIP and type <= CULTIVATION_EXP_ADD_TYPE.MONSTER then
				new_desc = string.format(desc, CommonDataManager.ConverNumber(self.data.target))
			else

				new_desc = string.format(desc, self.data.target)
			end
		end

		return new_desc, is_rich, progress_text, percent
	end
	return new_desc, is_rich, progress_text, percent
end

function BiZuoCultivationBuffItemRender:SetDescColor(cur_value, target_value, is_rich)
	local color_green = "#3C8652"
	-- if CultivationWGData.Instance:IsReceivedBuffByIndex(self.data.type, self.data.index) then
	-- 	color_green = "#79fa82"
	-- end
	return string.format("<color=%s>%s</color>/%s",is_rich and color_green or "#A93C3C" , cur_value,target_value)
end