require("game/serveractivity/everyday_onelove/everyday_onelove_view")
require("game/serveractivity/everyday_onelove/everyday_onelove_wg_data")

EveryDayOneLoveWGCtrl = EveryDayOneLoveWGCtrl or BaseClass(BaseWGCtrl)

function EveryDayOneLoveWGCtrl:__init()
	if EveryDayOneLoveWGCtrl.Instance then
		print_error("[EveryDayOneLoveWGCtrl] Attemp to create a singleton twice !")
	end
	EveryDayOneLoveWGCtrl.Instance = self

	self.view = EveryDayOneLoveView.New()
	self.data = EveryDayOneLoveWGData.New()
	self:RegisterAllProtocols()
end

function EveryDayOneLoveWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	EveryDayOneLoveWGCtrl.Instance = nil
end

function EveryDayOneLoveWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCLoveDailyInfo, "OnSCLoveDailyInfo")
end

function EveryDayOneLoveWGCtrl:OnSCLoveDailyInfo(protocol)
	self.data:SetInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
	-- RemindManager.Instance:Fire(RemindName.EveryDayOneLove)
end

function EveryDayOneLoveWGCtrl:Open()
	self.view:Open()
end
