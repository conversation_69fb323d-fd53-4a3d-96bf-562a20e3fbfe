function NewFestivalActivityView:ReleaseCallBackRaffle()
	if self.sd_reward_list then
		for k, v in pairs(self.sd_reward_list) do
			v:DeleteMe()
		end
		self.sd_reward_list = nil
	end

	if self.sd_rebate_list then
		self.sd_rebate_list:DeleteMe()
		self.sd_rebate_list = nil
	end

	if self.jrsd_show_model then
		self.jrsd_show_model:DeleteMe()
		self.jrsd_show_model = nil
	end

	if CountDownManager.Instance:HasCountDown("sd_end_time") then
		CountDownManager.Instance:RemoveCountDown("sd_end_time")
	end
end

function NewFestivalActivityView:ShowIndexCallBackRaffle()
	NewFestivalRaffleWGCtrl.Instance:SendReq(OA_NEW_FES_ACT_SD_OPERATE_TYPE.INFO)
end

function NewFestivalActivityView:LoadIndexCallBackRaffle()
	if not self.jrsd_show_model then
		self.jrsd_show_model = OperationActRender.New(self.node_list.jrsd_model_pos)
		self.jrsd_show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.sd_reward_list then
		self.sd_reward_list = {}
		for i = 1, NewFestivalRaffleWGData.REWARD_NUM do
			self.sd_reward_list[i] = SDRewardCell.New(self.node_list["sd_reward_pool"]:FindObj("sd_reward_pool_" .. i))
			self.sd_reward_list[i]:SetIndex(i)
		end
	end

	if not self.sd_rebate_list then
		self.sd_rebate_list = AsyncListView.New(SDRebateRender, self.node_list["integral_reward_list"])
	end

	for i = 1, 3 do
		self.node_list["sd_raffle_btn_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickDraw, self, i))
		self.node_list["sd_cost_icon_" .. i].button:AddClickListener(BindTool.Bind2(self.ShowItemTips, self, i))
	end

	self.node_list["sd_probability"].button:AddClickListener(BindTool.Bind(self.OnClickProbability, self))     -- 概率公示

	self:LoadRaffleUi()
	self:FlushSDEndTime()
end

--清除计时器
function NewFestivalActivityView:FlushSDEndTime()
	local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_JRHD_JRSD)
	if CountDownManager.Instance:HasCountDown("sd_end_time") then
		CountDownManager.Instance:RemoveCountDown("sd_end_time")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("sd_end_time",
			BindTool.Bind(self.UpSDdateCountDown, self),
			BindTool.Bind(self.OnSDComplete, self),
			nil, time, 1)
	else
		self:OnSDComplete()
	end
end

function NewFestivalActivityView:UpSDdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	local sd_info_cfg = NewFestivalActivityWGData.Instance:GetNewFesActSDCfg()
	local time_act_color = sd_info_cfg and sd_info_cfg.time_act_color or COLOR3B.D_GREEN
	self.node_list["sd_act_time"].text.text = string.format(Language.NewFestivalActivity.SDActTime, time_act_color, time_str)
end

function NewFestivalActivityView:OnSDComplete()
	self.node_list.sd_act_time.text.text = ""
	self:Close()
end

function NewFestivalActivityView:OnFlushRaffle()
	local sd_display_cfg = NewFestivalRaffleWGData.Instance:GetDisPlayCfg()
	if IsEmptyTable(sd_display_cfg) then
		return
	end

	for k, v in ipairs(self.sd_reward_list) do
		v:SetData(sd_display_cfg[k])
	end

	local rebate_list = NewFestivalRaffleWGData.Instance:GetPointRewardCfg()
	if IsEmptyTable(rebate_list) then
		return
	end

	self.sd_rebate_list:SetDataList(rebate_list)

	for i = 1, 3 do
		local draw_cfg = NewFestivalRaffleWGData.Instance:GetDrawTypeCfg()
		local cur_draw_cfg = draw_cfg[i]
		local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_cfg.draw_item.item_id)
		local num_color = num >= cur_draw_cfg.draw_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.node_list["sd_cost_num_" .. i].text.text = ToColorStr(num .."/".. cur_draw_cfg.draw_item.num, num_color)
		self.node_list["buy_red_".. i]:SetActive(num >= cur_draw_cfg.draw_item.num)
	end

	local leiji_point = NewFestivalRaffleWGData.Instance:GetLeiJiPoint()
	self.node_list.sd_integral_num.text.text = leiji_point

	local baodi_cfg = NewFestivalRaffleWGData.Instance:GetBaoDiCfg()
	if not baodi_cfg then
		return
	end

	local cur_point = NewFestivalRaffleWGData.Instance:GetCurPoint()
	self.node_list.sd_score.text.text = string.format(Language.NewFestivalActivity.CurPoint, cur_point, baodi_cfg.need_points)
end

function NewFestivalActivityView:LoadRaffleUi()
	self:FlushRaffleModel()

	local sd_bg_bundle, sd_bg_asset = ResPath.GetNewFestivalRawImages("sd_buttom_bg")
	self.node_list["sd_bg"].raw_image:LoadSprite(sd_bg_bundle, sd_bg_asset, function ()
		self.node_list["sd_bg"].raw_image:SetNativeSize()
	end)

	local sd_title_bundle, sd_title_asset = ResPath.GetNewFestivalRawImages("sd_title")
	self.node_list["sd_title"].raw_image:LoadSprite(sd_title_bundle, sd_title_asset, function ()
		self.node_list["sd_title"].raw_image:SetNativeSize()
	end)

	local sd_right_panel_bundle, sd_right_panel_asset = ResPath.GetNewFestivalRawImages("sd_reward_bg")
	self.node_list["sd_right_panel"].raw_image:LoadSprite(sd_right_panel_bundle, sd_right_panel_asset, function ()
		self.node_list["sd_right_panel"].raw_image:SetNativeSize()
	end)

	local sd_score_bg_bundle, sd_score_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_sd_score_bg")
 	self.node_list["sd_score_bg"].image:LoadSprite(sd_score_bg_bundle, sd_score_bg_asset, function()
		self.node_list["sd_score_bg"].image:SetNativeSize()
	end)

	local sd_icon_bg_bundle, sd_icon_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_sd_text_bg1")
	local sd_point_bg_bundle, sd_point_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_sd_text_bg2")
	local sd_raffle_btn_bundle1, sd_raffle_btn_asset1 = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn2")
	local sd_raffle_btn_bundle2, sd_raffle_btn_asset2 = ResPath.GetNewFestivalActImages("a3_jrhd_jrsc_btn1")
	local draw_type = NewFestivalRaffleWGData.Instance:GetDrawTypeCfg()
	
	for k = 1, 3 do
		self.node_list["sd_icon_bg" .. k].image:LoadSprite(sd_icon_bg_bundle, sd_icon_bg_asset, function()
			self.node_list["sd_icon_bg" .. k].image:SetNativeSize()
		end)

		self.node_list["sd_point_bg" .. k].image:LoadSprite(sd_point_bg_bundle, sd_point_bg_asset, function()
			self.node_list["sd_point_bg" .. k].image:SetNativeSize()
		end)

		if k == 1 then
			self.node_list["sd_raffle_btn_" .. k].image:LoadSprite(sd_raffle_btn_bundle1, sd_raffle_btn_asset1, function()
				self.node_list["sd_raffle_btn_" .. k].image:SetNativeSize()
			end)
		else
			self.node_list["sd_raffle_btn_" .. k].image:LoadSprite(sd_raffle_btn_bundle2, sd_raffle_btn_asset2, function()
				self.node_list["sd_raffle_btn_" .. k].image:SetNativeSize()
			end)
		end

		local item_cfg = ItemWGData.Instance:GetItemConfig(draw_type[k].draw_item.item_id)
		if IsEmptyTable(item_cfg) then
			return
		end
		local icon_bundle, icon_asset = ResPath.GetItem(item_cfg.icon_id)
		self.node_list["sd_cost_icon_" .. k].image:LoadSprite(icon_bundle, icon_asset)

		self.node_list["buy_num_text_" .. k].text.text = string.format(Language.NewFestivalActivity.RaffleNum, draw_type[k].draw_num)
		self.node_list["sd_point_" .. k].text.text = string.format(Language.NewFestivalActivity.SDAddPoint, draw_type[k].add_points)
	end
	
	local sd_info_cfg = NewFestivalActivityWGData.Instance:GetNewFesActSDCfg()
	self.node_list.sd_integral_num.text.color = Str2C3b(sd_info_cfg.integral_color)
	self.node_list.sd_integral_desc.text.color = Str2C3b(sd_info_cfg.integral_color)
	self.node_list.sd_act_time.text.color = Str2C3b(sd_info_cfg.act_color)
	self.node_list.sd_score.text.color = Str2C3b(sd_info_cfg.score_color)
end

function NewFestivalActivityView:FlushRaffleModel()
	local model_data = NewFestivalRaffleWGData.Instance:GetModelCfg()
	if not model_data then
		return
	end

	local sd_display_data = {}
	sd_display_data.should_ani = true
	if model_data.model_show_itemid ~= 0 and model_data.model_show_itemid ~= "" then
		local split_list = string.split(model_data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			sd_display_data.model_item_id_list = list
		else
			sd_display_data.item_id = model_data.model_show_itemid
		end
	end

	sd_display_data.bundle_name = model_data["model_bundle_name"]
	sd_display_data.asset_name = model_data["model_asset_name"]
	local model_show_type = tonumber(model_data["model_show_type"]) or 1
	sd_display_data.render_type = model_show_type - 1

	local pos_x, pos_y, pos_z = 0, 0, 0
	if model_data.display_pos and model_data.display_pos ~= "" then
		local pos_list = string.split(model_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[2]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.jrsd_model_pos.rect, pos_x, pos_y, pos_z)

	if model_data.model_pos and model_data.model_pos ~= "" then
		local pos_list = string.split(model_data.model_pos, "|")
		local posx = tonumber(pos_list[1]) or 0
		local posy = tonumber(pos_list[2]) or 0
		local posz = tonumber(pos_list[3]) or 0

		sd_display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
	end

	local rot_x, rot_y, rot_z = 0, 0, 0
	local display_rotation = model_data.display_rotation
	if display_rotation and display_rotation ~= "" then
		local rot_list = string.split(display_rotation, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
	end

	local scale = model_data["display_scale"] or 1
	sd_display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	sd_display_data.model_adjust_root_local_scale = scale
	sd_display_data.model_rt_type = ModelRTSCaleType.L

	self.jrsd_show_model:SetData(sd_display_data)
end

function NewFestivalActivityView:OnClickDraw(draw_type)
	local draw_cfg = NewFestivalRaffleWGData.Instance:GetDrawTypeCfg()
	local cur_draw_cfg = draw_cfg[draw_type]
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_cfg.draw_item.item_id)
	if num >= cur_draw_cfg.draw_item.num then
		NewFestivalRaffleWGData.Instance:CacheOrGetDrawIndex(draw_type)
		NewFestivalRaffleWGCtrl.Instance:SendReq(RA_GUANRICHANGHONG_OP_TYPE.DRAW, cur_draw_cfg.draw_button)
	else
		--不足够买
		NewFestivalRaffleWGCtrl.Instance:ClickUse(draw_type, function()
			self:OnClickBuy(draw_type)
		end)
	end
end

function NewFestivalActivityView:OnClickBuy(draw_type)
	local draw_cfg = NewFestivalRaffleWGData.Instance:GetDrawTypeCfg()
	local cur_draw_cfg = draw_cfg[draw_type]
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_cfg.draw_item.item_id)
	local consume = cur_draw_cfg.consume_lingyu_num * (cur_draw_cfg.draw_item.num - num)
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	if enough then
		NewFestivalRaffleWGData.Instance:CacheOrGetDrawIndex(draw_type)
		NewFestivalRaffleWGCtrl.Instance:SendReq(OA_NEW_FES_ACT_SD_OPERATE_TYPE.DRAW, cur_draw_cfg.draw_button)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function NewFestivalActivityView:ShowItemTips(draw_type)
	local cfg = NewFestivalRaffleWGData.Instance:GetDrawTypeCfg()
	local item_id = cfg[draw_type].draw_item.item_id
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function NewFestivalActivityView:OnClickProbability()
    NewFestivalRaffleWGCtrl.Instance:OpenSDProbabilityView()
end

--------------------SDRewardCell--------------------
SDRewardCell = SDRewardCell or BaseClass(BaseRender)
function SDRewardCell:__init()
	self.reward_item = ItemCell.New(self.node_list["cell_pos"])
end

function SDRewardCell:__delete()
	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function SDRewardCell:OnFlush()
	if not self.data then
		return
	end

	self.reward_item:SetData(self.data.reward_item[0])
end


----------------------SDRebateRender--------------------

SDRebateRender = SDRebateRender or BaseClass(BaseRender)
function SDRebateRender:LoadCallBack()
	self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
	if not self.show_item then
		self.show_item = ItemCell.New(self.node_list.item_pos)
	end
end

function SDRebateRender:ReleaseCallBack()
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function SDRebateRender:OnFlush()
	if not self.data then
		return
	end

	local data = self.data.data
	if not IsEmptyTable(data.reward_item) then
		self.show_item:SetData(data.reward_item[0])
	end

	self.node_list.need_point.text.text = data.need_points
	local leiji_point = NewFestivalRaffleWGData.Instance:GetLeiJiPoint()
	local is_show = data.need_points <= leiji_point and self.data.has_get ~= 1
	self.node_list["is_get"]:SetActive(self.data.has_get == 1)
	self.node_list["btn_lingqu"]:SetActive(is_show)
end

function SDRebateRender:OnClickGet()
	if not self.data then
		return
	end

	local leiji_point = NewFestivalRaffleWGData.Instance:GetLeiJiPoint()
	local data = self.data.data
	if leiji_point >= data.need_points and self.data.has_get == 0 then
		NewFestivalRaffleWGCtrl.Instance:SendReq(OA_NEW_FES_ACT_SD_OPERATE_TYPE.GET_REWARD, data.seq)
	end
end