GuildBattleTopView = GuildBattleTopView or BaseClass(SafeBaseView)

function GuildBattleTopView:__init()
	self.active_close = false
	self:LoadConfig()
	self.view_name = "GuildBattleTopView"
	self.score_l_old_value = 0
	self.score_r_old_value = 0
end

function GuildBattleTopView:LoadConfig()
	self:AddViewResource(0, "uis/view/guild_battle_new_ui_prefab", "layout_guild_battle_top")
end

function GuildBattleTopView:LoadCallBack()
	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	self:LoadGuideTimer()
end

function GuildBattleTopView:LoadGuideTimer()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_time = act_info and act_info.next_time or 0

	local is_standy = act_info and act_info.status == ACTIVITY_STATUS.STANDY
	local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
	local is_show = is_standy or xiusai_state

	local count_down_time = 0
	if act_info.status == ACTIVITY_STATUS.STANDY then
		count_down_time = next_time - server_time
	elseif GuildWGData.Instance:GetIsPrepareRestTime() then
		local start_hour,start_min,during_time,start_hour2,start_min2,all_second,round_time_1,round_time_2 = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
		local start_time = act_info.start_time
		local round_rest_timer = act_info.start_time + round_time_1 + during_time
		count_down_time = round_rest_timer - server_time
	end

	--self.node_list["guide_panel"]:SetActive(is_show)
	self.node_list["panel"]:SetActive(not is_show)
	if count_down_time > 0 and is_show then
		if CountDownManager.Instance:HasCountDown("xmz_act_top_standy_countdown") then
			CountDownManager.Instance:RemoveCountDown("xmz_act_top_standy_countdown")
		end
		self:UpdateStandyTimer(0,count_down_time)
		CountDownManager.Instance:AddCountDown("xmz_act_top_standy_countdown", BindTool.Bind(self.UpdateStandyTimer,self), BindTool.Bind(self.StandyTimerCallBack,self), nil, count_down_time, 1)
	else
		self:StandyTimerCallBack()
	end
end

function GuildBattleTopView:UpdateStandyTimer(now_time, elapse_time)
	
end

function GuildBattleTopView:StandyTimerCallBack()
	--self.node_list.guide_panel.rect:DOAnchorPosY(200, 0.3)
	self.node_list.panel.rect:DOAnchorPosY(0, 0.3)
	TryDelayCall(self, function ()
		-- if self.node_list["guide_panel"] then
		-- 	self.node_list["guide_panel"]:SetActive(false)
		-- end
		
		if self.node_list["panel"] then
			self.node_list["panel"]:SetActive(true)
		end
	end, 0.3, "guild_battle_top_delay")
end

function GuildBattleTopView:ReleaseCallBack()
	if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	if CountDownManager.Instance:HasCountDown("xmz_act_top_standy_countdown") then
		CountDownManager.Instance:RemoveCountDown("xmz_act_top_standy_countdown")
	end

	self.score_l_old_value = nil
	self.score_r_old_value = nil
end

function GuildBattleTopView:ShrinkButtonsValueChange(isOn)
	if isOn then
		self.node_list.panel.rect:DOAnchorPosY(100, 0.3)
		--self.node_list.guide_panel.rect:DOAnchorPosY(200, 0.3)
	else
		self.node_list.panel.rect:DOAnchorPosY(0, 0.3)
		--self.node_list.guide_panel.rect:DOAnchorPosY(-63, 0.3)
	end
end

function GuildBattleTopView:ShowIndexCallBack(index)
	self:Flush()
end

function GuildBattleTopView:OnFlush(param_list, index)
	local guilddata = GuildBattleRankedWGCtrl.Instance.data
	local guild_info_list = guilddata:GetGuildBattleSceneGlobalInfo()
	if not IsEmptyTable(guild_info_list) then
		self:LingShiValueTextChange(self.node_list["score_l"].text,guild_info_list[0].total_score,1)
		self:LingShiValueTextChange(self.node_list["score_r"].text,guild_info_list[1].total_score,2)
		-- self.node_list.score_l.text.text = (guild_info_list[0].total_score)
		-- self.node_list.score_r.text.text = (guild_info_list[1].total_score)
		local guild_name_l = guild_info_list[0].guild_name
		local guild_name_r = guild_info_list[1].guild_name
		self.node_list.name_l.text.text = guild_name_l
		self.node_list.name_r.text.text = guild_name_r
	end
end

function GuildBattleTopView:LingShiValueTextChange(text_obj,new_value,sign)
	--local old_value = text_obj.text
	local old_value = 0
	if sign == 1 then
		old_value = self.score_l_old_value or 0
	else
		old_value = self.score_r_old_value or 0
	end
	local complete_fun = function()
		if text_obj then
	        text_obj.text = Language.Guild.GuildWarContentionValue .. new_value
	    end
		if sign == 1 then
			self.score_l_old_value = new_value
		else
			self.score_r_old_value = new_value
		end
    end
    local update_fun = function(num)
    	if text_obj then
	        text_obj.text = Language.Guild.GuildWarContentionValue .. math.ceil(num)
	    end
    end
    UITween.DONumberTo(text_obj, old_value, new_value, 1.5, update_fun, complete_fun)
end


function GuildBattleTopView:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_XIANMENGZHAN then
		self:LoadGuideTimer()
	end
end
