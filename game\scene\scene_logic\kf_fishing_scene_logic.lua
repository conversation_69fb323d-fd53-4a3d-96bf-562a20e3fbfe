KfFishingSceneLogic = KfFishingSceneLogic or BaseClass(CommonFbLogic)

function KfFishingSceneLogic:__init()
	self.open_view = false
end

function KfFishingSceneLogic:__delete()

end

function KfFishingSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
	-- FishingWGCtrl.Instance:SendFishingOperaReq(FISHING_OPERA_REQ_TYPE.FISHING_OPERA_REQ_TYPE_RANK_INFO)

	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.kf_FISHING)
	if activity_status then
		FuBenWGCtrl.Instance:SetOutFbTime(activity_status.next_time)
	end
end

function KfFishingSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	
end

function KfFishingSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- FishingWGCtrl.Instance:CloseRankView()
end

-- function KfFishingSceneLogic:GetIsCanGather()
-- 	local steal_count = FishingData.Instance:GetFishingOtherCfg().steal_count
--     local steal_fish_count = FishingData.Instance:GetFishingUserInfo().steal_fish_count
--     if steal_fish_count >= steal_count then
--         SysMsgWGCtrl.Instance:ErrorRemind(Language.Fishing.NoHasMyStealCount)
--         return false
--     end
--     return true
-- end
