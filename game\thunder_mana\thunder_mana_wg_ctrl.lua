require("game/thunder_mana/thunder_mana_wg_data")
require("game/thunder_mana/thunder_mana_select_view")
require("game/thunder_mana/thunder_mana_view")
require("game/thunder_mana/thunder_mana_halo_view")
require("game/thunder_mana/thunder_mana_equip_bag_view")
require("game/thunder_mana/thunder_mana_all_attr_view")
require("game/thunder_mana/thunder_mana_batch_select")
require("game/thunder_mana/thunder_mana_star_preview")
require("game/thunder_mana/thunder_mana_quick_sp")
require("game/thunder_mana/thunder_mana_batch_select_swallow")

ThunderManaWGCtrl = ThunderManaWGCtrl or BaseClass(BaseWGCtrl)

function ThunderManaWGCtrl:__init()
	if ThunderManaWGCtrl.Instance then
		ErrorLog("[ThunderManaWGCtrl] attempt to create singleton twice!")
		return
	end

	ThunderManaWGCtrl.Instance = self
    self.data = ThunderManaWGData.New()
	self.select_view = ThunderManaSelectView.New(GuideModuleName.ThunderManaSelectView)
	self.shady_thunder_view = ThunderManaView.New(GuideModuleName.ShadyThunderView)
	self.sun_thunder_view = ThunderManaView.New(GuideModuleName.SunThunderView)
	self.thunder_halo_view = ThunderManaHaloView.New(GuideModuleName.NewAppearanceHaloWGView)
	self.equip_bag_view = ThunderManaEquipBagView.New()
	self.all_attr_view = ThunderManaAllAttrView.New()
	self.thunder_batch_select_view = ManaBatchBatchSelect.New()
	self.thunder_mana_batch_select_swallow = ManaBatchBatchSelectSwallow.New()
	self.star_preview = ThunderManaStarPreview.New()
	self.thunder_mana_quick_sp = ThunderManaQuickSp.New()

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self:RegisterAllProtocols()
end

function ThunderManaWGCtrl:__delete()
	ThunderManaWGCtrl.Instance = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

	if self.select_view then
        self.select_view:DeleteMe()
        self.select_view = nil
    end

	if self.shady_thunder_view then
        self.shady_thunder_view:DeleteMe()
        self.shady_thunder_view = nil
    end

	if self.sun_thunder_view then
        self.sun_thunder_view:DeleteMe()
        self.sun_thunder_view = nil
    end

	if self.equip_bag_view then
        self.equip_bag_view:DeleteMe()
        self.equip_bag_view = nil
    end

	if self.all_attr_view then
        self.all_attr_view:DeleteMe()
        self.all_attr_view = nil
    end

	if self.thunder_halo_view then
		self.thunder_halo_view:DeleteMe()
		self.thunder_halo_view = nil
	end

	if self.thunder_batch_select_view then
		self.thunder_batch_select_view:DeleteMe()
		self.thunder_batch_select_view = nil
	end

	if self.thunder_mana_batch_select_swallow then
		self.thunder_mana_batch_select_swallow:DeleteMe()
		self.thunder_mana_batch_select_swallow = nil
	end

	if self.star_preview then
		self.star_preview:DeleteMe()
		self.star_preview = nil
	end

	if self.thunder_mana_quick_sp then
		self.thunder_mana_quick_sp:DeleteMe()
		self.thunder_mana_quick_sp = nil
	end
	
	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
	end
end

function ThunderManaWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSThunderOperate)
	self:RegisterProtocol(CSThunderEquipUpStar)
	self:RegisterProtocol(CSThunderEquipDecompos)

	self:RegisterProtocol(SCThunderItemInfo, "OnSCThunderItemInfo")
	self:RegisterProtocol(SCThunderItemUpdate, "OnSCThunderItemUpdate")
	self:RegisterProtocol(SCThunderBagInfo, "OnSCThunderBagInfo")
	self:RegisterProtocol(SCThunderBagChangeInfo, "OnSCThunderBagChangeInfo")
end

function ThunderManaWGCtrl:SendCSThunderRequest(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSThunderOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function ThunderManaWGCtrl:SendCSThunderEquipUpStar(compose_count, compose_list)
	-- print_error("升星", compose_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSThunderEquipUpStar)
	protocol.compose_count = compose_count or 0
	protocol.compose_list = compose_list or {}
	protocol:EncodeAndSend()
end

function ThunderManaWGCtrl:SendCSThunderEquipDecompos(seq, part, compose_list)
	-- print_error("吞噬", seq, part, compose_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSThunderEquipDecompos)
	protocol.seq = seq or 0
	protocol.part = part or 0
	protocol.compose_count = #compose_list
	protocol.compose_list = compose_list
	protocol:EncodeAndSend()
end

function ThunderManaWGCtrl:OnSCThunderItemInfo(protocol)
	--print_error("=======全部信息======", protocol)
	self.data:SetAllThunderItemInfo(protocol)
	self:FlushManaQuickSpView()

	RemindManager.Instance:Fire(RemindName.ThunderManaShady)
	RemindManager.Instance:Fire(RemindName.ThunderManaSun)
end

function ThunderManaWGCtrl:OnSCThunderItemUpdate(protocol)
	--print_error("=======更新信息======", protocol)
	self.data:SetSingleThunderItemInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ShadyThunderView)
	ViewManager.Instance:FlushView(GuideModuleName.SunThunderView)
	self:FlushManaQuickSpView()

	RemindManager.Instance:Fire(RemindName.ThunderManaShady)
	RemindManager.Instance:Fire(RemindName.ThunderManaSun)
end

function ThunderManaWGCtrl:OnSCThunderBagInfo(protocol)
	--print_error("=======背包信息======", protocol)
	self.data:SetAllThunderBagInfo(protocol)

	if self.equip_bag_view:IsOpen() then
		self.equip_bag_view:Flush()
	end
	self:FlushManaQuickSpView()
	RemindManager.Instance:Fire(RemindName.ThunderManaShady)
	RemindManager.Instance:Fire(RemindName.ThunderManaSun)
end

function ThunderManaWGCtrl:OnSCThunderBagChangeInfo(protocol)
	--print_error("=======背包单个变化======", protocol)
	local new_data = protocol.change_info
	local index = new_data.index
	local old_data = self.data:GetThunderBagInfoByIndex(index)
	local old_num = 0
	if not IsEmptyTable(old_data) then
		old_num = old_data.num
	end

	if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > old_num then
		local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
		local num = new_data.num
		num = new_data.num - old_num
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]), num)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

	self.data:SetThunderBagChangeInfo(protocol)

	if self.equip_bag_view:IsOpen() then
		self.equip_bag_view:Flush()
	end

	self:FlushManaQuickSpView()
	ViewManager.Instance:FlushView(GuideModuleName.ShadyThunderView)
	ViewManager.Instance:FlushView(GuideModuleName.SunThunderView)
	RemindManager.Instance:Fire(RemindName.ThunderManaShady)
	RemindManager.Instance:Fire(RemindName.ThunderManaSun)
end

function ThunderManaWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:GetIsEquipPartLevelCostCfg(change_item_id) or self.data:GetIsEquipPartGradeCostCfg(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.ShadyThunderView)
			ViewManager.Instance:FlushView(GuideModuleName.SunThunderView)

			RemindManager.Instance:Fire(RemindName.ThunderManaShady)
			RemindManager.Instance:Fire(RemindName.ThunderManaSun)
		end
	end
end

function ThunderManaWGCtrl:OnUpLevelResult(result, seq, part, level)
	local cur_select_view = seq == ThunderManaWGData.ThunderType.ShadyType and self.shady_thunder_view or self.sun_thunder_view
	if not cur_select_view:IsOpen() then
		return
	end

	if 0 == result then
		cur_select_view:StopUpLevelOperator()
	elseif 1 == result then
		cur_select_view:AutoUpLevelUpOnce()
		cur_select_view:PlayUseEffect(UIEffectName.s_djts)
	end
end

function ThunderManaWGCtrl:OnUpGradeResult(result, seq, part, grade)
	local cur_select_view = seq == ThunderManaWGData.ThunderType.ShadyType and self.shady_thunder_view or self.sun_thunder_view
	if not cur_select_view:IsOpen() then
		return
	end

	if 1 == result then
		cur_select_view:PlayUseEffect(UIEffectName.s_jsts)
	end
end

function ThunderManaWGCtrl:OnSuitRewardResult(result, seq, index)
	if 1 == result then
		self:OpenGetNewView(seq, index)
	end
end

function ThunderManaWGCtrl:OpenEquipBagView(index)
	local data = {
		select_part_index = index or 0,
	}

	self.equip_bag_view:SetShowDataAndOpen(data)
end

function ThunderManaWGCtrl:OpenAllAttrView(view_type)
	local data = {
		view_type = view_type or ThunderManaWGData.ThunderType.ShadyType,
	}

	self.all_attr_view:SetShowDataAndOpen(data)
end

-- 新形象（光环）打开
function ThunderManaWGCtrl:OpenAppearanceHaloView()
	if not self.thunder_halo_view:IsOpen() then
		self.thunder_halo_view:Open()
	else
		self.thunder_halo_view:Flush()
	end
end

-- 新形象（光环）刷新
function ThunderManaWGCtrl:FlushAppearanceHaloView()
	if self.thunder_halo_view:IsOpen() then
		self.thunder_halo_view:Flush()
	end
end

-- 选择界面刷新
function ThunderManaWGCtrl:FlushAManaSelectView()
	if self.select_view:IsOpen() then
		self.select_view:Flush()
	end
end

function ThunderManaWGCtrl:OpenGetNewView(thunder_type, index)
	local protocol = {appe_image_id = thunder_type, index_param = index, appe_type = ROLE_APPE_TYPE.THUNDER_MANA}
	AppearanceWGCtrl.Instance:OnGetNewAppearance(protocol)
end

-- 升星材料选择
function ThunderManaWGCtrl:OpenManaBatchSelectView(battle_data)
	self.thunder_batch_select_view:SetData(battle_data)
	if not self.thunder_batch_select_view:IsOpen() then
		self.thunder_batch_select_view:Open()
	else
		self.thunder_batch_select_view:Flush()
	end
end

-- 升星材料选择回调
function ThunderManaWGCtrl:SetSelectManaBatchOkCallBack(fuction)
	self.thunder_batch_select_view:SetOkBack(fuction)
end

-- 升星材料选择(吞噬)
function ThunderManaWGCtrl:OpenManaBatchSelectSwallowView(battle_data)
	self.thunder_mana_batch_select_swallow:SetData(battle_data)
	if not self.thunder_mana_batch_select_swallow:IsOpen() then
		self.thunder_mana_batch_select_swallow:Open()
	else
		self.thunder_mana_batch_select_swallow:Flush()
	end
end

-- 升星材料选择回调(吞噬)
function ThunderManaWGCtrl:SetSelectManaBatchSwallowOkCallBack(fuction)
	self.thunder_mana_batch_select_swallow:SetOkBack(fuction)
end

function ThunderManaWGCtrl:OpenStarPreview(thunder_type, part)
	local data = {
		thunder_type = thunder_type,
		part = part,
	}

	self.star_preview:SetShowDataAndOpen(data)
end

function ThunderManaWGCtrl:OpenManaQuickSpView()
	if not self.thunder_mana_quick_sp:IsOpen() then
		self.thunder_mana_quick_sp:Open()
	else
		self.thunder_mana_quick_sp:Flush()
	end
end

function ThunderManaWGCtrl:FlushManaQuickSpView()
	if self.thunder_mana_quick_sp:IsOpen() then
		self.thunder_mana_quick_sp:Flush()
	end
end


-- 物品变化
function ThunderManaWGCtrl:OnThunderEquipItemDataChange(change_data, change_item_index, change_reason, old_num, new_num)
	local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ThunderManaSelectView)
	if not is_fun_open then
		return
	end

	local change_item_id = change_data.item_id
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg then
			-- 快速使用
			self:AddKeyUse(change_data)
		end
	end
end

-- 快速使用
function ThunderManaWGCtrl:AddKeyUse(data)
	if data == nil then
		return
	end

	FunctionGuide.Instance:OpenThunderEquipKeyUseView(data)
end