﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Nirvana
{
	public sealed class UI3DDisplayCamera10086 : MonoBehaviour
	{
		internal GameObject DisplayObject
		{
			get
			{
				return this.displayObject;
			}
			set
			{
				bool flag = this.displayObject != null && this.displayObject != value;
				if (flag)
				{
					this.ResetRenderer(this.displayObject.transform);
					this.RemoveFromCache(this.displayObject.transform);
				}
				this.displayObject = value;
			}
		}


        /*
		 * 递归重置指定 Transform 及其所有子对象的渲染器。
		 * 如果 Transform 有 Renderer 组件，则移除其 UI3DDisplayRecord10086 并将其从缓存中清除。
		 */
        private void ResetRenderer(Transform transform)
		{
			Renderer component = transform.GetComponent<Renderer>();
			bool flag = component != null;
			if (flag)
			{
				UI3DDisplayRecord10086 component2 = component.GetComponent<UI3DDisplayRecord10086>();
				bool flag2 = component2 != null;
				if (flag2)
				{
					component2.ManualDestroy();
					UnityEngine.Object.Destroy(component2);
				}
				UI3DDisplayCamera10086.RendererItem rendererItem;
				bool flag3 = this.cache.TryGetValue(transform, out rendererItem);
				if (flag3)
				{
					rendererItem.Record = null;
				}
			}

			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				this.ResetRenderer(child);
			}
		}

        /*
         * 递归地将指定 Transform 及其所有子对象从缓存中移除。
         */
        private void RemoveFromCache(Transform transform)
		{
			bool flag = this.cache.ContainsKey(transform);
			if (flag)
			{
				this.cache.Remove(transform);
			}

			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				this.RemoveFromCache(child);
			}
		}

        /*
		 * 如果 Transform 没有 UI3DDisplayRecord10086 组件，则添加一个并初始化它。
		 */
        private UI3DDisplayRecord10086 AddRecord(Transform transform, Renderer renderer)
		{
			UI3DDisplayRecord10086 ui3DDisplayRecord = transform.GetComponent<UI3DDisplayRecord10086>();
			bool flag = ui3DDisplayRecord == null;
			if (flag)
			{
				ui3DDisplayRecord = renderer.gameObject.AddComponent<UI3DDisplayRecord10086>();
				ui3DDisplayRecord.hideFlags = HideFlags.DontSave;
				ui3DDisplayRecord.Initialize(renderer, this);
			}
			return ui3DDisplayRecord;
		}

        /*
         * 更新指定 Transform 及其子对象的缓存状态。如果缓存中没有该对象，则添加它并创建相应的记录。
		*/
        private void UpdateCache(Transform transform)
		{
			UI3DDisplayCamera10086.RendererItem rendererItem;
			bool flag = !this.cache.TryGetValue(transform, out rendererItem);
			if (flag)
			{
				rendererItem = new UI3DDisplayCamera10086.RendererItem();
				Renderer component = transform.GetComponent<Renderer>();
				bool flag2 = component != null && component.enabled;
				if (flag2)
				{
					rendererItem.Renderer = component;
					rendererItem.Record = this.AddRecord(transform, component);
				}
				this.cache.Add(transform, rendererItem);
			}
			else
			{
				bool flag3 = rendererItem.Renderer != null && rendererItem.Record == null;
				if (flag3)
				{
					rendererItem.Record = this.AddRecord(transform, rendererItem.Renderer);
				}
			}

			rendererItem.IsExisted = true;
			for (int i = 0; i < transform.childCount; i++)
			{
				Transform child = transform.GetChild(i);
				bool flag4 = !child.gameObject.activeSelf;
				if (!flag4)
				{
					this.UpdateCache(child);
				}
			}
		}

		private void OnDisable()
		{
			bool flag = this.DisplayObject != null;
			if (flag)
			{
				this.ResetRenderer(this.DisplayObject.transform);
			}
		}

		private GameObject displayObject;

		private Dictionary<Transform, UI3DDisplayCamera10086.RendererItem> cache = new Dictionary<Transform, UI3DDisplayCamera10086.RendererItem>();

		private class RendererItem
		{
			public bool IsExisted { get; set; }

			public Renderer Renderer { get; set; }

			public UI3DDisplayRecord10086 Record { get; set; }
		}
	}
}
