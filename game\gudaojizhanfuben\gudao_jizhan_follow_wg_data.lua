GuDaoFuBenWGData = GuDaoFuBenWGData or BaseClass()
function GuDaoFuBenWGData:__init()
	if GuDaoFuBenWGData.Instance then
		print_error("[GuDaoFuBenWGData] Attempt to create singleton twice!")
		return
	end
	GuDaoFuBenWGData.Instance = self
	local all_cfg = ConfigManager.Instance:GetAutoConfig("islet_fierce_battle_auto")

	self.boss_cfg =  ListToMap(all_cfg.boss_cfg,"boss_index")
	self.rewared_cfg = all_cfg.rank_reward
	self.other_cfg = all_cfg.other[1]
end

function GuDaoFuBenWGData:__delete()
	GuDaoFuBenWGData.Instance = nil
end

function GuDaoFuBenWGData:OnSCIsletFierceBattleHurtRankInfo(protocol)
	self.next_boss_fresh_time = protocol.fresh_time
	self.now_boss_index = protocol.boss_index
	self.damage_rank_list = protocol.damage_rank_list
	self.gather_id_list = protocol.gather_id_list 
end

function GuDaoFuBenWGData:OnSCIsletFierceBattleRoleInfo(protocol)
	self.battle_info_list = protocol.battle_info_list
end

function GuDaoFuBenWGData:GetAllGatherIDList()
	return self.gather_id_list or {}
end

function GuDaoFuBenWGData:GetBattleHrutRankInfo()
	return self.next_boss_fresh_time or 0,self.now_boss_index or -3,self.damage_rank_list or {}
end

function GuDaoFuBenWGData:GetCurWaveBoss(index)
	return self.boss_cfg[index] or {}
end

function GuDaoFuBenWGData:GetMyRank()
	if not self.damage_rank_list then return {} ,-1 end
	local my_name = GameVoManager.Instance:GetMainRoleVo().name
	for k,v in pairs(self.damage_rank_list) do
		if v.role_name == my_name then
			return v,k
		end
	end
	return {},-1
end

function GuDaoFuBenWGData:GetAllResultData()
	return self.battle_info_list or {}
end

function GuDaoFuBenWGData:GetPaimingRewared(index)
	for k,v in pairs(self.rewared_cfg) do
		if v.rank_id >= index then
			return v
		end 
	end
end

function GuDaoFuBenWGData:OnSCIsletFierceBattleRoleGather(protocol)
	self.gather_num = protocol.gather_num
end

function GuDaoFuBenWGData:GetGatherNum()
	return self.gather_num or 0
end

function GuDaoFuBenWGData:ClearGatherNum()
	self.gather_num = 0
end

function GuDaoFuBenWGData:GetGuaJiPos()
	return self.other_cfg.boss_pos_x,self.other_cfg.boss_pos_y
end