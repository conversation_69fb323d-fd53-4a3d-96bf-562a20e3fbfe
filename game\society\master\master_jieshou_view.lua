MasterJieshouView = MasterJieshouView or BaseClass(SafeBaseView)

function MasterJieshouView:__init()
	self:LoadConfig()
	self.ring_item = nil
	self.data = nil
end

function MasterJieshouView:__delete()

end

function MasterJieshouView:ReleaseCallBack()
	if self.ring_item then
		self.ring_item:DeleteMe()
		self.ring_item = nil
	end
end

function MasterJieshouView:LoadConfig()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/master_ui_prefab", "layout_jieshou")
end

function MasterJieshouView:LoadCallBack()
	self.node_list.btn_close.button:AddClickListener(BindTool.Bind2(self.AcceptHandler, self, 0))
	self.node_list.btn_baishi_jieshou.button:AddClickListener(BindTool.Bind2(self.Accept<PERSON><PERSON><PERSON>, self, 1))
	self:CreateRingItem()
end

function MasterJieshouView:CreateRingItem()
	self.ring_item = ItemCell.New(self.node_list["ph_item"])
end

function MasterJieshouView:SetData(data)
	self.data = data
end

function MasterJieshouView:ShowIndexCallBack()
	if nil == self.data then return end
	local gift_name = self.node_list.label_giftname.text.text
	local ring_cfg =  MasterWGData.Instance:GetOneBaishiCfgByType(self.data.baishi_type) or {}
	local item_data = ring_cfg.reward_item
	if item_data then
		self.ring_item:SetData(item_data)
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
		if item_cfg then
			gift_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		end
	end
	local str_value = Language.Master.MasterBackTips
	str_value = string.format(str_value, self.data.req_name)
	self.node_list.rich_tips.text.text = str_value
end

function MasterJieshouView:AcceptHandler(accept)
	if nil == self.data then return end
	if accept == 0 then
		MasterWGCtrl.Instance:SendResoluteBaishi(self.data.req_uid)
	elseif accept == 1 then
		MasterWGCtrl.Instance:SendAgreeBaishi(self.data.req_uid)
	end
	self:Close()
end

function MasterJieshouView:CloseCallBack()
	self.data = nil
end