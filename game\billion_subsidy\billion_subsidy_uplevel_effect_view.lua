-----------------------------------
-- 百亿补贴
-----------------------------------
BillionSubsidyUplevelEffectView = BillionSubsidyUplevelEffectView or BaseClass(SafeBaseView)

local EFFECT_LEVEL = {
	[1] = Ui_Effect.UI_bybt_huangjin,
	[2] = Ui_Effect.UI_bybt_heijin,
	[3] = Ui_Effect.UI_bybt_liujin,
}

function BillionSubsidyUplevelEffectView:__init()
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Window
	self.is_safe_area_adapter = true
	self.view_cache_time = 1
	self:SetMaskBg(false, true)

	self:AddViewResource(0, "uis/view/billion_subsidy_ui_prefab", "layout_billion_subsidy_uplevel")
end

function BillionSubsidyUplevelEffectView:LoadCallBack()

end

function BillionSubsidyUplevelEffectView:ReleaseCallBack()

end

function BillionSubsidyUplevelEffectView:SetDataAndOpen(member_level)
	self.member_level = member_level
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function BillionSubsidyUplevelEffectView:CloseCallBack()
	self.member_level = nil
end

function BillionSubsidyUplevelEffectView:OnFlush(param_t, index)
	if not self.member_level then
		return
	end
	local bundle, asset = ResPath.GetEffect(EFFECT_LEVEL[self.member_level])
	EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["effect_root"].transform, 3.2)
	--ReDelayCall(self, BindTool.Bind(self.Close, self), 3.2, "bybt_effect_view_close")
	ReDelayCall(self, function ()
		self:Close()
		BillionSubsidyWGCtrl.Instance:SetIsFirstOpen(false)
		local fetch_first_open_view_reward = BillionSubsidyWGData.Instance:GetIsFetchFirstOpenViewReward()
		if fetch_first_open_view_reward == 0 then
			local callback = function ()
				BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_FIRST_OPEN_VIEW_REWARD)
			end

			BillionSubsidyWGCtrl.Instance:PlayReceiveDCEffect(callback)
		end
	end, 3.2, "bybt_effect_view_close")
	self.member_level = nil
end