require("game/scene/scene_logic/base_scene_logic")
require("game/scene/scene_logic/base_fb_logic")
require("game/scene/scene_logic/common_scene_logic")
require("game/scene/scene_logic/common_act_logic")
require("game/scene/scene_logic/common_fb_logic")
require("game/scene/scene_logic/crossserver_scene_logic")
require("game/scene/scene_logic/guild_guard_scene_logic")
require("game/scene/scene_logic/dungeon_tower_scene_logic")
require("game/scene/scene_logic/dungeon_quality_scene_logic")
require("game/scene/scene_logic/daily_exp_fb_scene_logic")
require("game/scene/scene_logic/daily_peteq_fb_scene_logic")
require("game/scene/scene_logic/daily_equip_fb_scene_logic")
require("game/scene/scene_logic/daily_fuli_fb_scene_logic")
require("game/scene/scene_logic/wujinjitan_fb_scene_logic")
require("game/scene/scene_logic/guild_battle_scene_logic")
require("game/scene/scene_logic/field1v1_scene_logic")
require("game/field1v1/field_head_panel")
require("game/scene/scene_logic/qingyuan_scene_logic")
require("game/scene/scene_logic/guild_boss_scene_logic")
require("game/scene/scene_logic/wedding_scene_logic")
require("game/scene/scene_logic/kf_honorhalls_scene_logic")
require("game/scene/scene_logic/kf_yezhanwangcheng_scene_logic")
require("game/scene/scene_logic/kf_onevone_scene_logic")
require("game/scene/scene_logic/kf_onevone_prepare_scene_logic")
require("game/scene/scene_logic/kf_pvp_scene_logic")
require("game/scene/scene_logic/kf_pvp_prepare_scene_logic")
require("game/scene/scene_logic/kf_onevsn_sport_scene_logic")
require("game/scene/scene_logic/shenmozhixi_scene_logic")
require("game/scene/scene_logic/guild_mijing_scene_logic")
require("game/scene/scene_logic/transferproftask_scene_logic")
require("game/scene/scene_logic/daily_story_fb_scene_logic")
require("game/scene/scene_logic/daily_coin_fb_scene_logic")
require("game/scene/scene_logic/climb_tower_fb_scene_logic")
require("game/scene/scene_logic/yaoshouplaza_scene_logic")
require("game/scene/scene_logic/suoyaota_scene_logic")
require("game/scene/scene_logic/shuijing_scene_logic")
require("game/scene/scene_logic/siege_prepare_scene_logic")
require("game/scene/scene_logic/siege_battle_scene_logic")
require("game/scene/scene_logic/team_yaoshoujitan_scene_logic")
require("game/scene/scene_logic/team_many_tower_scene_logic")
require("game/scene/scene_logic/shitu_fb_logic")
require("game/scene/scene_logic/team_migongxianfu_scene_logic")
require("game/scene/scene_logic/daily_daoju_fb_scene_logic")
require("game/scene/scene_logic/zhongkui_fb_scene_logic")
require("game/scene/scene_logic/welkin_scene_logic")
require("game/scene/scene_logic/exp_fb_scene_logic")
require("game/scene/scene_logic/dabao_fb_scene_logic")
require("game/scene/scene_logic/gaozhan_fb_logic")
require("game/scene/scene_logic/qunxianluandou_scene_logic")
require("game/scene/scene_logic/wushuang_scene_logic")
require("game/scene/scene_logic/hot_spring_scene_logic")
require("game/scene/scene_logic/teamclimbtower_scene_logic")
require("game/scene/scene_logic/kf_boss_scene_logic")
require("game/scene/scene_logic/paoku_scene_logic")
require("game/scene/scene_logic/feixian_boss_scene_logic")
require("game/scene/scene_logic/vip_boss_scene_logic")
require("game/scene/scene_logic/guild_battle_ranked_scene_logic")
require("game/scene/scene_logic/defense_fb_scene_logic")
require("game/scene/scene_logic/fubenpanel_copper_scene_logic")
require("game/scene/scene_logic/fubenpanel_pet_scene_logic")
require("game/scene/scene_logic/fuben_bounty_devil_logic")
require("game/scene/scene_logic/fuben_bounty_fairy_logic")
require("game/scene/scene_logic/fuben_bounty_gaint_logic")
require("game/scene/scene_logic/fuben_bounty_kunlun_logic")
require("game/scene/scene_logic/world_boss_scene_logic")
require("game/scene/scene_logic/dabao_boss_scene_logic")
require("game/scene/scene_logic/team_equip_fb_scene_logic")
require("game/scene/scene_logic/kf_fishing_scene_logic")
require("game/scene/scene_logic/bangpaitask_fb_scene_logic")
require("game/scene/scene_logic/guild_answer_scnec_logic")
require("game/scene/scene_logic/fuben_demons_logic")
require("game/scene/scene_logic/new_player_scene_logic")
require("game/scene/scene_logic/new_player_battle_1_scene_logic")
require("game/scene/scene_logic/transferfuben_scene_logic")
require("game/scene/scene_logic/personal_boss_scene_logic")
require("game/scene/scene_logic/yuangu_fuben_scene_logic")
require("game/scene/scene_logic/yuangu_fuben_guide_scene_logic")
require("game/scene/scene_logic/sg_boss_scene_logic")
require("game/scene/scene_logic/jiushedao_fb_scene_logic")
require("game/scene/scene_logic/infinite_hell_fb_scene_logic")
require("game/scene/scene_logic/shengyu_boss_scene_logic")
require("game/scene/scene_logic/zhushenta_scene_logic")
require("game/scene/scene_logic/kf_shenyun_scene_logic")
require("game/scene/scene_logic/guide_boss_scene_logic")
require("game/scene/scene_logic/liekun_scene_logic")
require("game/scene/scene_logic/mijing_boss_scene_logic")
require("game/scene/scene_logic/mijing_kf_boss_scene_logic")
require("game/scene/scene_logic/boss_fight_scene_logic")
require("game/scene/scene_logic/tianshen_scene_logic")
require("game/scene/scene_logic/bootybay_scene_logic")
require("game/scene/scene_logic/team_bootybay_scene_logic")
require("game/scene/scene_logic/fuben_baguamizhen_logic")
require("game/scene/scene_logic/manhuanggudian_scene_logic")
require("game/scene/scene_logic/fubenpanel_fake_pet_scene_logic")
require("game/scene/scene_logic/fake_ts_fb_scene_logic")
require("game/scene/scene_logic/hmsy_scene_logic")
require("game/scene/scene_logic/kf_zhuxie_scene_logic")
require("game/scene/scene_logic/shenyuan_boss_scene_logic")
require("game/scene/scene_logic/gudao_jizhan_scene_logic")
require("game/scene/scene_logic/fengshenbang_scene_logic")
require("game/scene/scene_logic/guild_invite_scene_logic")
require("game/scene/scene_logic/eternal_night_scene_logic")
require("game/scene/scene_logic/eternal_night_final_scene_logic")
require("game/scene/scene_logic/task_chain_husong_logic")
require("game/scene/scene_logic/task_chain_scene_logic")
require("game/scene/scene_logic/task_chain_xunluo_caiji_logic")
require("game/scene/scene_logic/task_chain_caiji_logic")
require("game/scene/scene_logic/task_chain_sixteen_cell_logic")
require("game/scene/scene_logic/task_chain_yun_song_avoid_logic")
require("game/scene/scene_logic/task_chain_guard_logic")
require("game/scene/scene_logic/task_chain_boss_logic")
require("game/scene/scene_logic/tianshen_jianglin_logic")
require("game/scene/scene_logic/world_treasure_jianglin_logic")
require("game/scene/scene_logic/xingtian_laixi_logic")
require("game/scene/scene_logic/mowu_jianglin_logic")
require("game/scene/scene_logic/duck_race_logic")
require("game/scene/scene_logic/act_xqjf_xingtian_laixi_logic")
require("game/scene/scene_logic/treasure_hunt_boss_scene_logic")
require("game/scene/scene_logic/tianshen_3v3_prepare_scene_logic")
require("game/scene/scene_logic/tianshen_3v3_scene_logic")
require("game/scene/scene_logic/guild_battle_ranked_new_scene_logic")
require("game/scene/scene_logic/worlds_no1_scene_logic")
require("game/scene/scene_logic/worlds_no1_prepare_scene_logic")
require("game/scene/scene_logic/xianjie_boss_scene_logic")
require("game/scene/scene_logic/Secret_Area_scene_logic")
require("game/scene/scene_logic/cross_longmai_scene_logic")
require("game/scene/scene_logic/cross_yanglongsi_scene_logic")
require("game/scene/scene_logic/special_personal_boss_logic")
require("game/scene/scene_logic/zhuogui_fuben_logic")
require("game/scene/scene_logic/zhuogui_boss_logic")
require("game/scene/scene_logic/holy_heavenly_domain_mobai_scene_logic")
require("game/scene/scene_logic/cross_flag_grabbing_battlefield_scene_logic")
require("game/scene/scene_logic/holy_heavenly_domain_scene_logic")
require("game/scene/scene_logic/person_mabiboss_display_scene_logic")
require("game/scene/scene_logic/loverpk_prapare_scene_logic")
require("game/scene/scene_logic/loverpk_scene_logic")
require("game/scene/scene_logic/kf_ultimate_ready_scene_logic")
require("game/scene/scene_logic/kf_ultimate_battle_scene_logic")
require("game/scene/scene_logic/cross_everyday_recharge_boss_logic")
require("game/scene/scene_logic/wuhun_tower_scene_logic")
require("game/scene/scene_logic/positional_warfare_scene_logic")
require("game/scene/scene_logic/exp_west_scene_logic")
require("game/scene/scene_logic/phantom_dreamland_scene_logic")
require("game/scene/scene_logic/arena_tianti_logic")
require("game/scene/scene_logic/land_war_fb_scene_logic")
require("game/scene/scene_logic/boss_invasion_scene_logic")
require("game/scene/scene_logic/cross_air_war_scene_logic")
require("game/scene/scene_logic/hundred_equip_scene_logic")
require("game/scene/scene_logic/dragon_trial_scene_logic")
require("game/scene/scene_logic/fuben_team_common_boss_logic")
require("game/scene/scene_logic/fuben_team_rune_tower_logic")



SceneLogic = SceneLogic or {}

--组队副本通用场景
local FuBenTeamCommon = {
	[SceneType.TEAM_COMMON_BOSS_FB_1] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_2] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_3] = true,
}

local New_Guide_Battle_Scene_Id_1 = 155 --新手战斗场景1
function SceneLogic.Create(scene_type)
	--print_error(string.format("scene_type = %s, scene_id = %s", scene_type, Scene.Instance:GetSceneId()))
	local scene_logic = nil
	-- 根据场景类型创建场景逻辑
	if SceneType.TowerDefend == scene_type then
		scene_logic = DailyDaojuFbSceneLogic.New()
	elseif SceneType.KF_DUCK_RACE == scene_type then
		scene_logic = DuckRaceLogic.New()
	elseif SceneType.LingyuFb == scene_type then
		scene_logic = DungeonQualitySceneLogic.New()
	elseif SceneType.DefenseFb == scene_type then
		scene_logic = DefenseFbSceneLogic.New()
	elseif SceneType.CoinFb == scene_type then
		scene_logic = DailyCoinFbSceneLogic.New()
	elseif SceneType.ExpFb == scene_type then
		scene_logic = DailyExpFbSceneLogic.New()
	elseif SceneType.GuildStation == scene_type then
		scene_logic = GuildGuardSceneLogic.New()
	elseif SceneType.XianMengzhan == scene_type then
		-- scene_logic = GuildBattleSceneLogic.New()
		-- scene_logic = GuildBattleRankedSceneLogic.New() --帮派争霸
		scene_logic = GuildBattleRankedNewSceneLogic.New() --帮派争霸
	elseif SceneType.Common == scene_type then
		scene_logic = CommonSceneLogic.New()
	elseif SceneType.Field1v1 == scene_type then
		scene_logic = Field1v1SceneLogic.New()
	elseif SceneType.QingYuanFB == scene_type then
		scene_logic = QingYuanSceneLogic.New()
	elseif SceneType.HunYanFb == scene_type then
		scene_logic = WeddingSceneLogic.New()
	elseif SceneType.ShenMoZhiXiFB == scene_type then
		scene_logic = ShenMoZhiXiSceneLogic.New()
	elseif SceneType.GuildMiJingFB == scene_type then
		scene_logic = GuildMiJingSceneLogic.New()
	elseif SceneType.TransferProfTask == scene_type then
		scene_logic = TransferProfTaskSceneLogic.New()
	elseif SceneType.TeamFB == scene_type then
		scene_logic = TeamYaoShouJiTanSceneLogic.New()
	-- elseif SceneType.ManyTowerFB == scene_type then
	-- 	scene_logic = TeamManyTowerSceneLogic.New()
	elseif SceneType.MiGongXianFu == scene_type then
		scene_logic = TeamMiGongXianFuSceneLogic.New()
	elseif SceneType.Kf_Honorhalls == scene_type then
		scene_logic = KfHonorhallsSceneLogic.New()
	elseif SceneType.YEZHANWANGCHENGFUBEN == scene_type then
		scene_logic = KfYeZhanWangChengSceneLogic.New()
	elseif SceneType.Kf_OneVOne == scene_type then
		scene_logic = KfOneVOneSceneLogic.New()
	elseif SceneType.Kf_PVP == scene_type then
		scene_logic = KfPVPSceneLogic.New()
	elseif SceneType.Kf_OneVsN == scene_type then
		scene_logic = KFOneVsNSportSceneLogic.New()
	elseif SceneType.GuildBoss == scene_type then
		scene_logic = GuildBossSceneLogic.New()
	elseif SceneType.StoryFB == scene_type then
		scene_logic = DailyStoryFbSceneLogic.New()
	elseif SceneType.YaoShouPlaza == scene_type then
		scene_logic = YaoShouPlazaSceneLogic.New()
	elseif SceneType.SuoYaoTa == scene_type then
		scene_logic = SuoYaoTaSceneLogic.New()
	elseif SceneType.ShuiJing == scene_type then
		scene_logic = ShuiJingSceneLogic.New()
	elseif SceneType.Fb_Welkin == scene_type then
		scene_logic = FbWelkinSceneLogic.New()
	elseif SceneType.ZhuXie == scene_type then
		scene_logic = SiegePrepareSceneLogic.New()
	elseif SceneType.GongChengZhan == scene_type then
		scene_logic = SiegeBattleSceneLogic.New()
	elseif SceneType.ZhongKui == scene_type then
		-- scene_logic = ZhongKuiFBSceneLogic.New()
	elseif SceneType.CampGaojiDuobao == scene_type then
		scene_logic = BaseFbLogic.New()
	elseif SceneType.WeaponFb == scene_type then
		scene_logic = DailyPeteqFbSceneLogic.New()
	elseif SceneType.EquipFb == scene_type then
		scene_logic = DailyEquipFbSceneLogic.New()
	elseif SceneType.WelfareFb == scene_type then
		scene_logic = DailyFuliFbSceneLogic.New()
	elseif SceneType.Wujinjitan == scene_type or SceneType.LingHunGuangChang == scene_type then
		scene_logic = WujinjitanFbSceneLogic.New()
	elseif SceneType.Fb_ExpFb == scene_type then
		scene_logic = ExpFbSceneLogic.New()
	elseif SceneType.ShiTuPata == scene_type then
		scene_logic = FbShituSceneLogic.New()
	elseif SceneType.DaBaoFb == scene_type then
		scene_logic = DaBaoFbSceneLogic.New()
	elseif SceneType.GaoZhanFb == scene_type then
		scene_logic = GaoZhanFbLogic.New()
	elseif SceneType.WUSHUANGFB == scene_type then
		scene_logic = WushuangFbLogic.New()
	elseif SceneType.QunXianLuanDou == scene_type then
		scene_logic = QunXianLuanDouSceneLogic.New()
	elseif SceneType.HotSpring == scene_type or SceneType.KF_HotSpring == scene_type then
		scene_logic = HotSpringSceneLogic.New()
	elseif SceneType.KF_BOSS == scene_type then
		scene_logic = KfBossSceneLogic.New()
	elseif SceneType.MultiChallenge == scene_type then
		scene_logic = TeamClimbTowerSceneLogic.New()
	elseif SceneType.PAOKU == scene_type then
		scene_logic = PaoKuSceneLogic.New()
	elseif SceneType.FeixianBoss == scene_type then
		scene_logic = FeixianBossSceneLogic.New()
	elseif SceneType.WorldBoss == scene_type then
		scene_logic = WorldBossSceneLogic.New()
	elseif SceneType.VIP_BOSS == scene_type then
		scene_logic = VipBossSceneLogic.New()
	elseif SceneType.DABAO_BOSS == scene_type then
		scene_logic = DabaoBossSceneLogic.New()
	elseif SceneType.MJ_BOSS == scene_type then
		scene_logic = MiJingBossSceneLogic.New()
	elseif SceneType.MJ_KF_BOSS == scene_type then
		scene_logic = MiJingKFBossSceneLogic.New()
	elseif SceneType.GUIDE_BOSS == scene_type then
		scene_logic = GuideBossSceneLogic.New()
	elseif SceneType.COPPER_FB == scene_type then
		scene_logic = FuBenPanelCopperSceneLogic.New()
	elseif SceneType.PET_FB == scene_type then
		scene_logic = FuBenPanelPetLogic.New()
	elseif SceneType.FakePetFb == scene_type then
		scene_logic = FuBenPanelFakePetLogic.New()
	elseif SceneType.DEVILCOME_FB == scene_type then
		scene_logic = FuBenBountyDevilLogic.New()
	elseif SceneType.GAINT_FB == scene_type then
		scene_logic = FuBenBountyGaintLogic.New()
	elseif SceneType.PROFAIRY_FB == scene_type then
		scene_logic = FuBenBountyFairyLogic.New()
	elseif SceneType.KUNLUNTRIAL_FB == scene_type then
		scene_logic = FuBenBountyKunLunLogic.New()
	elseif SceneType.TEAM_EQUIP_FB == scene_type then
		scene_logic = TeamEquipFbSceneLogic.New()
	elseif SceneType.SCENE_TYPE_TASKFB_ZHILIAO == scene_type then
		scene_logic = BangPaiTaskFbFbSceneLogic.New()
	elseif SceneType.Fishing == scene_type then
		scene_logic = KfFishingSceneLogic.New()
	elseif SceneType.GUILD_ANSWER_FB == scene_type then
		scene_logic = GuildAnswerSceneLogic.New()
	elseif SceneType.FBCT_NEWPLAYERFB == scene_type then
		if Scene.Instance:GetSceneId() == New_Guide_Battle_Scene_Id_1 then
			scene_logic = NewPlayerFbBattle1SceneLogic.New()
		else
			scene_logic = NewPlayerFbSceneLogic.New()
		end
	elseif SceneType.DEMONS_FB == scene_type then
		scene_logic = FuBenDemonsLogic.New()
	elseif SceneType.ZHUANZHI_FB == scene_type then
		scene_logic = TransferFubenSceneLogic.New()
	elseif SceneType.PERSON_BOSS == scene_type then
		scene_logic = PersonalBossSceneLogic.New()
	elseif SceneType.HIGH_TEAM_EQUIP_FB == scene_type then
		scene_logic = YuanGuXianDianFBSceneLogic.New()
	elseif SceneType.YUGUXIANDIANYINDAO == scene_type then
		scene_logic = YuanGuXianDianGuidSceneLogic.New()
	elseif SceneType.SG_BOSS == scene_type then
		scene_logic = SgBossSceneLogic.New()
	elseif SceneType.JIUSHEDAO_FB == scene_type then
		scene_logic = JiuSheDaoSceneLogic.New()
	elseif SceneType.INFINITEHELL_FB == scene_type then
		scene_logic = InfiniteHellSceneLogic.New()
	elseif SceneType.ZHUSHENTA_FB == scene_type then
		scene_logic = ZhuShenTaSceneLogic.New()
	elseif SceneType.KFSHENYUN_FB == scene_type then
		scene_logic = KfShenYunSceneLogic.New()
	elseif SceneType.SHENGYU_BOSS == scene_type then
		scene_logic = ShengyuBossSceneLogic.New()
	elseif SceneType.CROSS_LIEKUN == scene_type then
		scene_logic = LieKunSceneLogic.New()
	elseif SceneType.Kf_OneVOne_Prepare == scene_type then
		scene_logic = KfOneVOnePrapareSceneLogic.New()
	elseif SceneType.Kf_PvP_Prepare == scene_type then
		scene_logic = KfPVPPrepareSceneLogic.New()
	elseif SceneType.KF_BOSS_FIGHT == scene_type then
		scene_logic = BossFightSceneLogic.New()
	elseif SceneType.TIAN_SHEN_FB == scene_type then
		scene_logic = TianShenSceneLogic.New()
	elseif SceneType.BOOTYBAY_FB == scene_type then
		scene_logic = BootyBayFBSceneLogic.New()
	elseif SceneType.TEAM_BOOTYBAY_FB == scene_type then
		scene_logic = TeamBootyBayFBSceneLogic.New()
	elseif SceneType.BaGuaMiZhen_FB == scene_type then
		scene_logic = BaGuaMiZhenSceneLogic.New()
	elseif SceneType.ManHuangGuDian_FB == scene_type then
		scene_logic = ManHuangGuDianSceneLogic.New()
	elseif SceneType.HONG_MENG_SHEN_YU == scene_type then
		scene_logic = HMSYSceneLogic.New()
	elseif SceneType.KFZhuXieZhanChang == scene_type then
		scene_logic = KFZhuXieZhanChang.New()
	elseif SceneType.FakeTianShenFb == scene_type then
        scene_logic = FakeTianShenFbSceneLogic.New()
    elseif SceneType.Shenyuan_boss == scene_type then
		scene_logic = ShenyuanBossSceneLogic.New()
	elseif SceneType.GuDaoJiZhan_FB == scene_type then
		scene_logic = GuDaoFBSceneLogic.New()
	elseif SceneType.FengShenBang == scene_type then
		scene_logic = FengShenBangSceneLogic.New()
	elseif SceneType.Guild_Invite == scene_type then
		scene_logic = GuildInviteSceneLogic.New()
	elseif SceneType.ETERNAL_NIGHT == scene_type then
		scene_logic = EternalNightSceneLogic.New()
	elseif SceneType.ETERNAL_NIGHT_FINAL == scene_type then
		scene_logic = EternalNightFinalSceneLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN == scene_type then
		scene_logic = TaskChainSceneLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_HUSONG == scene_type then
		scene_logic = TaskChainHuSongLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI == scene_type then
		scene_logic = TaskChainXunLuoCaiJiLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_CAI_JI == scene_type then
		scene_logic = TaskChainCaiJiLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_AVOID == scene_type then
		scene_logic = TaskChainSixteenCellLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID == scene_type then
		scene_logic = TaskChainYunSongAvoidLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_GUARD == scene_type then
		scene_logic = TaskChainGuardLogic.New()
	elseif SceneType.CROSS_TASK_CHAIN_BOSS == scene_type then
		scene_logic = TaskChainBossLogic.New()
	elseif SceneType.TIANSHEN_JIANLIN == scene_type then
		scene_logic = TianShenJiangLinLogic.New()
	elseif SceneType.WORLD_TREASURE_JIANLIN == scene_type then
		scene_logic = WorldTreasureJiangLinLogic.New()
	elseif SceneType.XINGTIANLAIXI_FB == scene_type then
		scene_logic = XingTianLaiXiLogic.New()
	elseif SceneType.MOWU_JIANLIN == scene_type then
		scene_logic = MoWuJiangLinLogic.New()
	elseif SceneType.XQJF_XING_TIAN_LAI_XI == scene_type then
		scene_logic = ActXQJFXingTianLaiXiLogic.New()
	elseif SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS == scene_type then
		scene_logic = TreasureHuntBossSceneLogic.New()
	elseif SceneType.TianShen3v3Prepare == scene_type then
		scene_logic = TianShen3v3PrepareSceneLogic.New()
	elseif SceneType.TianShen3v3 == scene_type then
		scene_logic = TianShen3v3SceneLogic.New()
	elseif SceneType.WorldsNO1 == scene_type then 		-- 天下第一
		scene_logic = WorldsNO1SceneLogic.New()
	elseif SceneType.WorldsNO1Prepare == scene_type then
		scene_logic = WorldsNO1ScenePrepareLogic.New()
    elseif SceneType.XianJie_Boss == scene_type then
        scene_logic = XianJieBossSceneLogic.New()
    elseif SceneType.SCENE_TYPE_KF_COUNTRY_SECRET_AREA == scene_type then
    	scene_logic = SecretAreaSceneLogic.New()
	elseif SceneType.CrossLongMai == scene_type then
		scene_logic = CrossLongMaiSceneLogic.New()
	elseif SceneType.SCENE_TYPE_CROSS_YANGLONG == scene_type then
		scene_logic = CrossYangLongSiSceneLogic.New()
	elseif SceneType.SPECIAL_PERSONAL_BOSS == scene_type then
		scene_logic = SpecialPersonalBossLogic.New()
	elseif SceneType.GHOST_FB_GLOBAL == scene_type then
		scene_logic = ZhuoGuiFuBenLogic.New()
	elseif SceneType.GHOST_FB_PERSON == scene_type then
		scene_logic = ZhuoGuiBossLogic.New()
	elseif SceneType.PERSON_MABI_BOSS_DISPLAY == scene_type then
		scene_logic = PersonMaBiBossDisplaySceneLogic.New()
	elseif SceneType.CROSS_DIVINE_DOMAIN == scene_type then
		scene_logic = HolyHeavenlyDomainSceneLogic.New()		-- 圣天神域
	elseif SceneType.CROSS_DIVINE_DOMAIN_MOBAI == scene_type then
		scene_logic = HolyHeavenlyDomainMoBaiSceneLogic.New()	-- 圣天神域膜拜
	elseif SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD == scene_type then
		scene_logic = CrossFlagGrabbingBattleFieldSceneLogic.New()
	elseif SceneType.CROSS_PK_LOVER_READY == scene_type then
		scene_logic = LoverPKPrepareSceneLogic.New()
	elseif SceneType.CROSS_PK_LOVER == scene_type then
		scene_logic = LoverPKSceneLogic.New()
	elseif SceneType.CROSS_ULTIMATE_BATTLE_READY == scene_type then
		scene_logic = KfUltimateReadySceneLogic.New()
	elseif SceneType.CROSS_ULTIMATE_BATTLE == scene_type then
		scene_logic = KfUltimateBattleSceneLogic.New()
	elseif SceneType.CROSS_EVERYDAY_RECHARGE_BOSS == scene_type then
		scene_logic = CrossEverydayRechargeBossLogic.New()
	elseif SceneType.WUHUN_TOWER_FB == scene_type then
		scene_logic = WuHunTowerSceneLogic.New()
	elseif SceneType.CROSS_LAND_WAR == scene_type then
		scene_logic = PositionalWarfareSceneLogic.New()
	elseif SceneType.SCENE_TYPE_EXP_WEST_FB == scene_type then
		scene_logic = ExpWestSceneLogic.New()
	elseif SceneType.PHANTOM_DREAMLAND_FB == scene_type then
		scene_logic = PhantomDreamlandSceneLogic.New()	
	elseif SceneType.ARENA_TIANTI == scene_type then
		scene_logic = ArenaTianTiSceneLogic.New()
	elseif SceneType.SCENE_TYPE_LAND_WAR_FB == scene_type then
		scene_logic = LandWarFbSceneLogic.New()	
	elseif SceneType.BOSS_INVASION == scene_type then
		scene_logic = BOSSInvasionLogic.New()	
	elseif SceneType.CROSS_AIR_WAR == scene_type then
		scene_logic = CrossAirWarSceneLogic.New()	
	elseif SceneType.HUNDRED_EQUIP == scene_type then
		scene_logic = HundredEquipSceneLogic.New()
	elseif SceneType.SCENE_TYPE_DRAGON_TRIALT_FB == scene_type then
		scene_logic = DragonTrialSceneLogic.New()
	elseif FuBenTeamCommon[scene_type] then
		scene_logic = FuBenCommonBossLogic.New()
	elseif SceneType.TEAM_COMMON_TOWER_FB_1 == scene_type then
		scene_logic = FuBenTeamRuneTowerLogic.New()
	else
		scene_logic = BaseSceneLogic.New()
	end

	if scene_logic ~= nil then
		scene_logic:SetSceneType(scene_type)
	end
	return scene_logic
end