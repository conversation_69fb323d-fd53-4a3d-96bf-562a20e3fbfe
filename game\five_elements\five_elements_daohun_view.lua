FiveElementsDaohunView = FiveElementsDaohunView or BaseClass(SafeBaseView)
local AUTO_UPGRADE_TIME = 0.2
function FiveElementsDaohunView:__init()
	self:SetMaskBg(false, true)
	self.is_auto_upgrade = false
    self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_daohun")
end

function FiveElementsDaohunView:ReleaseCallBack()
	if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

	if self.left_list then
        self.left_list:DeleteMe()
        self.left_list = nil
	end

	if self.upgrade_need_item_cell then
        self.upgrade_need_item_cell:DeleteMe()
        self.upgrade_need_item_cell = nil
	end

	self:CleanAutoUpgradeCoolTime()
end

function FiveElementsDaohunView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.one_key_btn, BindTool.Bind(self.OnAutoUpgradeClick, self))
	XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.OnUpgradeClick, self))
	XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnClickRuleBtn, self))

	if not self.left_list then
		self.left_list = AsyncListView.New(DaohunLeftRender, self.node_list["left_list"])
		self.left_list:SetSelectCallBack(BindTool.Bind1(self.OnClickLeftIcon, self))
	end

	if not self.attr_list then
		self.attr_list = {}
		local node_num = self.node_list["attr_list"].transform.childCount
        for i = 1, node_num do
            self.attr_list[i] = CommonAddAttrRender.New(self.node_list["attr_list"]:FindObj("attr_" .. i))
			self.attr_list[i]:SetIndex(i)
			self.attr_list[i]:SetAttrNameNeedSpace(true)
        end

		local node_num2 = self.node_list["special_attr_list"].transform.childCount
        for i = (node_num + 1), (node_num + node_num2) do
            local attr_obj = self.node_list.special_attr_list:FindObj("attr_" .. i)
            if attr_obj then
                self.attr_list[i] = CommonAddAttrRender.New(attr_obj)
                self.attr_list[i]:SetIndex(i)
                self.attr_list[i]:SetAttrNameNeedSpace(true)
            end
        end
	end

	if self.upgrade_need_item_cell == nil then
		self.upgrade_need_item_cell = ItemCell.New(self.node_list.upgrade_need_item_cell)
		self.upgrade_need_item_cell:SetNeedItemGetWay(true)
	end
end

function FiveElementsDaohunView:OnClickLeftIcon()
	local attr_data = self:GetCurSelectItemAttrData()
	if IsEmptyTable(attr_data) then
		local data_type = (self.left_list:GetCurItemData() or {}).type or 0
		print_error("cannot get data in waist_soul_skill_improve,type:", data_type)
		return
	end
	
	if self.is_auto_upgrade then
		self:CleanAutoUpgradeCoolTime()
		self:StopAutoUpgrade()
	end

	self:FlushMidLayer(attr_data)
	self:FlushRightLayer(attr_data)
end

--点击规则按钮
function FiveElementsDaohunView:OnClickRuleBtn()
	local attr_data = self:GetCurSelectItemAttrData()
	local type = attr_data and attr_data.type or 0
	local cfg_list = FiveElementsWGData.Instance:GetDaohunFactorCfgList(type)
	local level, next_level, factor, next_factor = 0, 0, 0, 0
	if not IsEmptyTable(cfg_list) then
		level = FiveElementsWGData.Instance:GetWaistLightOpen(type) or 0
		factor = (cfg_list[level] or {}).factor or 0
		next_factor = (cfg_list[level + 1] or {}).factor or factor
		next_level = cfg_list[level + 1] and (level + 1) or level
	end
	local str = string.format(Language.FiveElements.DaohunTipsStr, level, 100 + (factor/1000), next_level, 100 + (next_factor/1000))
    RuleTip.Instance:SetContent(str, Language.FiveElements.DaohunTipsTitle)
end

--------------------------------自动升级--------------------------------
function FiveElementsDaohunView:OnAutoUpgradeClick()
	if not self:CheckCurSelectIsActive() then
		self:StopAutoUpgrade()
		return
	end

    self.is_auto_upgrade = not self.is_auto_upgrade

    if self.is_auto_upgrade then
        self:OnceUpgrade()
    else
        self:StopAutoUpgrade()
    end

	self:FlushAutoButtonState()
end

-- 检测是否自动升级
function FiveElementsDaohunView:CheckAutoUpgrade()
    if self.is_auto_upgrade then
        -- 增加一个冷却
        self:CleanAutoUpgradeCoolTime()
        self.auto_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
            if self.is_auto_upgrade then
                self:OnceUpgrade()
			else
				self:StopAutoUpgrade()
            end
        end, AUTO_UPGRADE_TIME)
    end
end

function FiveElementsDaohunView:CleanAutoUpgradeCoolTime()
    if self.auto_cool_timer then
        GlobalTimerQuest:CancelQuest(self.auto_cool_timer)
        self.auto_cool_timer = nil
    end
end

--执行一次升级
function FiveElementsDaohunView:OnceUpgrade(is_click)
	local data = self:GetCurSelectItemAttrData()
	local cfg = data and FiveElementsWGData.Instance:GetDaohunCurrentLvAttrCfg(data.type) or {}
	if IsEmptyTable(data) or IsEmptyTable(cfg) or not self:CheckCurSelectIsActive() then
		self:StopAutoUpgrade()
		return
	end
	local max_level = FiveElementsWGData.Instance:GetDaohunMaxLevel(data.type)
	local level = FiveElementsWGData.Instance:GetDaohunLevel(data.type)
	if level >= max_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.UpgradeTips4)
        return
	end

	if is_click and self.is_auto_upgrade then    -- 手动点击且在自动升级中则不操作
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.UpgradeTips1)
        return
    end

	if not RoleWGData.GetIsEnoughAllCoin(cfg.cost) then--铜钱不足
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.UpgradeTips2)
		self:StopAutoUpgrade()
        return
	end

	local count = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
	if count < cfg.item_num then--所需升级道具不足
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.UpgradeTips3)
		self:StopAutoUpgrade()
        return
	end

	FiveElementsWGCtrl.Instance:SendWaistLightRequest(WAIST_LIGHT_OPERATE_TYPE.DAOHUN_UPGRADE, cfg.type)
end

-- 关闭自动升级
function FiveElementsDaohunView:StopAutoUpgrade()
    self.is_auto_upgrade = false
    self:FlushAutoButtonState()
end

-- 刷新按钮状态
function FiveElementsDaohunView:FlushAutoButtonState()
    local str = self.is_auto_upgrade and Language.FiveElements.UpgradeText2 or Language.FiveElements.UpgradeText1
    self.node_list.ony_key_text.text.text = str
end
--------------------------------自动升级--------------------------------

----获得当前选中的道魂是否已经激活
function FiveElementsDaohunView:CheckCurSelectIsActive()
	local data = self:GetCurSelectItemAttrData()
	if IsEmptyTable(data) then
		return false
	end

	local is_active = FiveElementsWGData.Instance:GetDaohunIsActive(data.type)
	if not is_active then
		local name = FiveElementsWGData.Instance:GetWaistLightCfgByType(data.type).name
		local daohun_cfg = self.left_list.data_list and self.left_list:GetCurItemData() or {}
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FiveElements.DaohunNotActive, name, daohun_cfg.star))
	end

	return is_active
end

-- 刷新按钮状态
function FiveElementsDaohunView:OnUpgradeClick()
    self:OnceUpgrade(true)
end

function FiveElementsDaohunView:FlushRightLayer(data)
	if IsEmptyTable(data) then
		data = self:GetCurSelectItemAttrData()
	end 

	--刷新此道魂的属性
	local attr_list = FiveElementsWGData.Instance:GetDaohunAttrList(data.type)
	if not IsEmptyTable(attr_list) then
		for i = 1, #self.attr_list do
			local attr_info = attr_list[i]
			self.attr_list[i]:SetRealHideNext(false)
			if self.attr_list[i].node_list["expand_root"] then
				self.attr_list[i].node_list["expand_root"]:CustomSetActive(not attr_info.add_value_str)
			end

			self.attr_list[i]:SetData(attr_info)
			if attr_info.add_value_str then
				self.attr_list[i]:ResetAddVlaue(attr_info.add_value_str, "#66c7ff")
			end
		end
	end

	--刷新技能描述
	local daohun_level = FiveElementsWGData.Instance:GetDaohunLevel(data.type)
	local dis_count = FiveElementsWGData.Instance:GetSkillDisNum(data.type)
	self.node_list.skill_des.text.text = string.format(Language.FiveElements.DaohunSkill, daohun_level, dis_count)

	--满级隐藏强化按钮显示已满级
	local max_level = FiveElementsWGData.Instance:GetDaohunMaxLevel(data.type)
	self.node_list.upgrade_layer:CustomSetActive(daohun_level < max_level)
	self.node_list.is_max:CustomSetActive(daohun_level >= max_level)

	self:FlushConsumeItem()
	self:FlushMoney()
end

function FiveElementsDaohunView:FlushConsumeItem()
	local data = self:GetCurSelectItemAttrData()
	if IsEmptyTable(data) then
		return
	end

	--刷新升级所需道具
	local item_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
	local str = item_num .. "/" .. data.item_num
	local color = item_num >= data.item_num and COLOR3B.GREEN or COLOR3B.RED
	self.upgrade_need_item_cell:SetData({item_id = data.item_id})
	self.upgrade_need_item_cell:SetRightBottomColorText(ToColorStr(str, color))
	self.upgrade_need_item_cell:SetRightBottomTextVisible(true)
end

function FiveElementsDaohunView:FlushMoney()
	local data = self:GetCurSelectItemAttrData()
	if IsEmptyTable(data) then
		return
	end 

	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	local coin_num, coin_str = CommonDataManager.ConverMoneyBarNew(main_role_vo.coin)
	self.node_list.need_num_text.text.text = coin_num .. coin_str .. "/" .. (data.cost or 0)
end

function FiveElementsDaohunView:FlushMidLayer(data)
	local is_change_index = not IsEmptyTable(data)
	if IsEmptyTable(data) then
		data = self:GetCurSelectItemAttrData()
	end 

	local name = FiveElementsWGData.Instance:GetWaistLightCfgByType(data.type).name--沧溟套装名字
	--没有激活则在节面界面显示激活条件
	local is_active = FiveElementsWGData.Instance:GetDaohunIsActive(data.type)
	self.node_list.lock_root:CustomSetActive(not is_active)
	if not is_active then
		local daohun_cfg = self.left_list.data_list and self.left_list:GetCurItemData() or {}
		self.node_list.lock_text.text.text = string.format(Language.FiveElements.DaohunNotActive, name, daohun_cfg.star)
	end

	--设置战力
	local cap = FiveElementsWGData.Instance:GetDaohunCap(data.type)
	self.node_list.cangming_cap_value.text.text = cap

	--根据道魂等级设置亮多少颗珠
	local daohun_level = FiveElementsWGData.Instance:GetDaohunLevel(data.type) or 0
	local level_img_num = math.floor((daohun_level % 10) / 2)
	if daohun_level > 1 and level_img_num == 0 then
		level_img_num = 5
	end
	for i = 1, 5 do
		self.node_list["level_img_" .. i]:CustomSetActive(i <= level_img_num)
	end

	self.node_list.level_text.text.text = daohun_level

	
	--改变左边列表选择的道魂才刷的东西
	if is_change_index then
		--设置中间的图片
		local bundle, asset = ResPath.GetRawImagesPNG("a2_wx_dh_" .. data.type)
		self.node_list.mid_image.raw_image:LoadSpriteAsync(bundle, asset, function ()
		    self.node_list.mid_image.raw_image:SetNativeSize()
    	end)
	
		--切换特效
		local effect_name = FiveElementsWGData.Instance:GetEffectNameByType(data.type)
		local bundle, asset = ResPath.GetA2Effect(effect_name)
    	if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
	        self.node_list.effect_attach.game_obj_attach.BundleName = nil
    		self.node_list.effect_attach.game_obj_attach.AssetName = nil
    	end
    	self.node_list.effect_attach:SetActive(true)
    	self.node_list.effect_attach:ChangeAsset(bundle,asset)

		self.node_list.name_text.text.text = name
	end
end

function FiveElementsDaohunView:FlushLeftLayer()
	local daohun_cfg_list = FiveElementsWGData.Instance:GetDaohunCfg()
	if IsEmptyTable(daohun_cfg_list) then
		return
	end
	
	self.left_list:SetDataList(daohun_cfg_list)
end

function FiveElementsDaohunView:OnFlush(param_t)
    for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushLeftLayer()
        elseif "FlushItem" == k then
			self:FlushMidLayer()
			self:FlushRightLayer()
			self:CheckAutoUpgrade()
		elseif "FlushConsumeItem" == k then
			self:FlushConsumeItem()
		end
	end
end

--获得当前选中的道魂详细数据
function FiveElementsDaohunView:GetCurSelectItemAttrData()
    if IsEmptyTable(self.left_list.data_list) then
		return {}
	end
	local data_type = (self.left_list:GetCurItemData() or {}).type or 0
	return FiveElementsWGData.Instance:GetDaohunCurrentLvAttrCfg(data_type) or {}
end

----------------------------------------DaohunLeftRender----------------------------------------
DaohunLeftRender = DaohunLeftRender or BaseClass(BaseRender)

function DaohunLeftRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cangming_cfg = FiveElementsWGData.Instance:GetWaistLightCfgByType(self.data.type)
	self.node_list.name.text.text = cangming_cfg.name
	local bundle, asset = ResPath.GetFiveElementsImg("a1_wx_type_" .. self.data.type)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
	    self.node_list.icon.image:SetNativeSize()
    end)

	local is_active = FiveElementsWGData.Instance:GetDaohunIsActive(self.data.type)

	XUI.SetGraphicGrey(self.node_list.icon, not is_active)
end

function DaohunLeftRender:OnSelectChange(is_select)
	self.node_list.bg_hl:SetActive(is_select)
end