WuHunCultivateView = WuHunCultivateView or BaseClass(SafeBaseView)
local WU_OPERATE_BTN = {
	SHENG_JI = 1,
	LIANHUN = 2,
	STAR_UP = 3,
}

local WU_OPERATE_TEXT = {
	AUTO = 1,
	STOP = 2,
	BREACH = 3,
	LIANHUN = 4,
}


function WuHunCultivateView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.full_screen = true
	self.is_safe_area_adapter = true
	self.view_name = "WuHunCultivateView"
	local bundle_name = "uis/view/wuhunzhenshen_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
	self:AddViewResource(0, bundle_name, "layout_wuhun_cultivate")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

	self.is_shower_loaded = false
	self.wuhun_list_view = nil
	self.cur_select_wh_index = nil
    self.cur_select_wh_data = nil
	self.curr_wuhun_appeid = nil
	self.curr_wuhun_operate_type = nil
	self.wuhun_item = nil
	self.wuhun_old_level = nil
	self.wuhun_old_exp = nil
	self.wuhun_spend_items = nil
	self.wuhun_spend_items_root = nil
	self.wuhun_auto_upgrade = false
	self.wuhun_slider_tween = nil

	self.wuhun_skill_list = nil
	self.attr_item_list = nil
	self.is_can_starup = nil --是否可以升星

	-- 模型相关
	self.origin_display_pos = nil
	self.show_model = nil
end

function WuHunCultivateView:__delete()

end

function WuHunCultivateView:LoadCallBack()
	local bundle, assert = ResPath.GetF2RawImagesPNG("a3_wh_bj")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	local title = Language.WuHunZhenShen.TitleName
	self.node_list.title_view_name.text.text = title

	if not self.wuhun_list_view then
		self.wuhun_list_view = AsyncListView.New(TianShenWuHunRender, self.node_list["wuhun_list_view"])
		self.wuhun_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectTianShenWuHunCallBack, self))
	end

	if self.attr_item_list == nil then
        self.attr_item_list = {}
        for i = 1, 6 do
            local attr_obj = self.node_list["layout_wuhun_attr"]:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
				cell:SetRealHideNext(true)
                self.attr_item_list[i] = cell
            end
        end
    end

	if self.wuhun_spend_items == nil then
        self.wuhun_spend_items = {}
		self.wuhun_spend_items_root = {}
        for i = 1, 3 do
            self.wuhun_spend_items_root[i] = self.node_list["wuhun_spend_itemlist"]:FindObj(string.format("wuhun_spend_pos_%d", i))
            if self.wuhun_spend_items_root[i] then
                local item_cell = ItemCell.New(self.wuhun_spend_items_root[i])
				item_cell:SetHideRightDownBgLessNum(-1)
				self.wuhun_spend_items[i] = item_cell
            end
        end
    end

	----[[模型展示
	if self.show_model == nil then
		self.origin_display_pos = self.node_list["wuhun_block"].rect.anchoredPosition

		self.show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["wuhun_block"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.show_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.show_model)
	end

-- if nil == self.role_intro_model then
-- 	self.role_intro_model = RoleModel.New()
-- 	local display_data = {
-- 		parent_node = self.node_list["wuhun_role_block"],
-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
-- 		rt_scale_type = ModelRTSCaleType.PS_S,
-- 		can_drag = true,
-- 	}
-- 	self.role_intro_model:SetRenderTexUI3DModel(display_data)

-- 	self:AddUiRoleModel(self.role_intro_model)
-- end

	self.starup_attr_item_list = {}
	for i = 1, 6 do
		local attr_obj = self.node_list.wuhun_star_attr_parent:FindObj(string.format("attr_%d", i))
        if attr_obj then
            local cell = CommonAddAttrRender.New(attr_obj)
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            cell:SetRealHideNext(true)
            self.starup_attr_item_list[i] = cell
        end
	end

	self.wuhun_starup_itemcell = ItemCell.New(self.node_list.node_wuhun_starup_itemcell)

	self:InitPropertyInfo()

	XUI.AddClickEventListener(self.node_list.wuhun_btn_active, BindTool.Bind2(self.WuHunActive, self))
	XUI.AddClickEventListener(self.node_list.btn_wuhun_upgrade, BindTool.Bind2(self.WuHunUpgradeBtn, self))
	XUI.AddClickEventListener(self.node_list.wuhun_btn_skill_show, BindTool.Bind2(self.WuHunOnSkillShowClick, self))
	XUI.AddClickEventListener(self.node_list.wuhun_btn_shengji, BindTool.Bind2(self.WuHunOnShengJiClick, self))
	XUI.AddClickEventListener(self.node_list.wuhun_btn_starup, BindTool.Bind2(self.WuHunOnStarUpClick, self))
	XUI.AddClickEventListener(self.node_list.wuhun_btn_lianhun, BindTool.Bind2(self.WuHunOnLianHunClick, self))
	XUI.AddClickEventListener(self.node_list.wuhun_huanhua, BindTool.Bind2(self.WuHunOnHuanHuaOperate, self))
	XUI.AddClickEventListener(self.node_list.wuhun_yi_huanhua, BindTool.Bind2(self.WuHunOnYiHuanHuaOperate, self))
	XUI.AddClickEventListener(self.node_list.btn_wuhun_starup, BindTool.Bind2(self.WuHunBtnStarupClick, self))
	XUI.AddClickEventListener(self.node_list.wuhun_material_buy_btn, BindTool.Bind(self.OnClickBtnOpenWuHunMaterialBuyView, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.WuHunCultivateView, self.get_guide_ui_event)
end

function WuHunCultivateView:InitPropertyInfo()
	self.property_item_tab = {}
	for i = 1, 3 do
		self.property_item_tab[i] = ItemCell.New(self.node_list["node_PettetItem" .. i])
		self.property_item_tab[i]:SetTipClickCallBack(BindTool.Bind(self.OnClickPropertyDanItem, self))
	end
end

function WuHunCultivateView:ReleaseCallBack()
	self.is_shower_loaded = false
	self.cur_select_ts_index = nil
    self.cur_select_ts_data = nil
	self.curr_wuhun_appeid = nil
	self.curr_wuhun_operate_type = nil
	self.wuhun_old_level = nil
	self.wuhun_old_exp = nil
	self.wuhun_auto_upgrade = false
	self.origin_display_pos = nil

	if self.wuhun_list_view then
		self.wuhun_list_view:DeleteMe()
		self.wuhun_list_view = nil
	end

	if self.attr_item_list and #self.attr_item_list > 0 then
		for _, attr_cell in ipairs(self.attr_item_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end

		self.attr_item_list = nil
	end

	if self.wuhun_spend_items and #self.wuhun_spend_items > 0 then
		for _, spend_item in ipairs(self.wuhun_spend_items) do
			spend_item:DeleteMe()
			spend_item = nil
		end

		self.wuhun_spend_items = nil
		self.wuhun_spend_items_root = nil
	end

	if self.wuhun_item then
		self.wuhun_item:DeleteMe()
		self.wuhun_item = nil
	end

	if self.starup_attr_item_list then
		for _, attr_cell in ipairs(self.starup_attr_item_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end

		self.starup_attr_item_list = nil
	end

	if self.wuhun_starup_itemcell then
		self.wuhun_starup_itemcell:DeleteMe()
		self.wuhun_starup_itemcell = nil
	end

	self.curr_details_hunzhen_appid = nil
	if self.show_model then
		self.show_model:RemoveSoulFormation()
        self.show_model:DeleteMe()
        self.show_model = nil
    end

	-- if self.role_intro_model then
	-- 	self.role_intro_model:DeleteMe()
	-- 	self.role_intro_model = nil
	-- end

	for	i = 1, 3 do
		if self["arrow_tweener"..i] then
			self["arrow_tweener"..i]:Kill()
			self["arrow_tweener"..i] = nil
		end

		if self.property_item_tab and self.property_item_tab[i] then
			self.property_item_tab[i]:DeleteMe()
			self.property_item_tab[i] = nil
		end
	end

	self.property_item_tab = nil
	self.is_can_starup = nil
	self:WuHunClearGradeSliderTween()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.WuHunCultivateView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function WuHunCultivateView:OnFlush(param_t)
	if param_t then
		for k,v in pairs(param_t) do
			if k == "all" or k == "tower" then
				if v.item_id and v.item_id ~= 0 then
					self:InitWuHunJumpData(v.item_id)
				end

				self:WuHunFlush()
				break
			elseif k == "Tupo" then
				self:WuHunOperateFinalEffect(2)
				self:WuHunFlush()
				break
			elseif k == "HuanHua" then
				self:WuHunRefreshHuanHuaStatus()
				break
			end
		end
	else
		self:WuHunFlush()
	end
end

function WuHunCultivateView:InitWuHunJumpData(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg then
		local wuhun_list = WuHunWGData.Instance:GetAllWuhunlist()
		local wuhun_active_cfg = WuHunWGData.Instance:GetWuhunResByItemId(item_cfg.id)
		local prop_cfg = WuHunWGData.Instance:GetPropertyDanCfgByItemId(item_id)
		if wuhun_list and wuhun_active_cfg then
			for index, data in pairs(wuhun_list) do
				if data.wuhun_id == wuhun_active_cfg.wuhun_id or data.wuhun_id == prop_cfg.id then
					self.cur_select_wh_index = index
					self.cur_select_wh_data = data
				end
			end
		end
	end
end

function WuHunCultivateView:WuHunFlush(is_item_refresh)
	self:WuHunFlushListView()							-- 左侧列表
	self:WuHunFlushDisPlayView()						-- 模型
	self:WuHunFlushSpendView()							-- 右侧数据
	self:SetWuHunMaterialBtn()							-- 设置法相风暴按钮
end

function WuHunCultivateView:WuHunSpendFlush()
	self:WuHunFlushListView()
	self:WuHunRefreshRedStatus()
	self:WuHunRefreshItemSpendItem()
	self:WuHunFlushSpendView()			-- 右侧数据
end

--物品变化
function WuHunCultivateView:ItemChangeFlush()
	self:WuHunSpendFlush()
	self:SetWuHunPropertyInfo()
end

function WuHunCultivateView:WuHunFlushListView()
	local wuhun_list = WuHunWGData.Instance:GetAllWuhunlist()
	local is_get = false

	if wuhun_list then
		self.wuhun_list_view:SetDataList(wuhun_list)
		if self.cur_select_wh_index == nil then
			for index, data in pairs(wuhun_list) do
				if not data.lock and self.cur_select_wh_index == nil then
					self.cur_select_wh_index = index
					self.cur_select_wh_data = data
				end

				if data.show_red or data.active_red then
					self.cur_select_wh_index = index
					self.cur_select_wh_data = data
					is_get = true
				end

				if data.wuhun_id and data.wuhun_id == WuHunWGData.Instance.wuhun_huanhua_id and is_get == false then
					self.cur_select_wh_index = index
					self.cur_select_wh_data = data
				end
			end

			if self.cur_select_wh_index == nil then
				--未取到带红点的默认第一个
				self.cur_select_wh_index = 1
				self.cur_select_wh_data = wuhun_list[self.cur_select_wh_index]
			end
		end
	end

	if self.wuhun_list_view then
		self.wuhun_list_view:SetDefaultSelectIndex(self.cur_select_wh_index)
	end
end

-- 天神武魂列表选择返回
function WuHunCultivateView:OnSelectTianShenWuHunCallBack(item, cell_index, is_default, is_click)
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	local is_same_ts = self.cur_select_wh_index == item.index
	if is_same_ts then
		return
	end

	self.cur_select_wh_index = item.index
	self.cur_select_wh_data = data
	self.wuhun_old_level = nil
	self.wuhun_old_exp = nil
	self.wuhun_auto_upgrade = false
	self:WuHunFlush()
end

-- 刷新模型相关信息
function WuHunCultivateView:WuHunFlushDisPlayView()
	local temp_wuhun_appeid = self.curr_wuhun_appeid

	if self.cur_select_wh_data then
		self.node_list.wuhun_name.text.text = self.cur_select_wh_data.wh_name
		self.node_list.wuhun_upgrade_level.text.text = string.format(Language.WuHunZhenShen.WuhunUpgradeLevel, self.cur_select_wh_data.level)
		local quality_level = WuHunWGData.Instance:GetWuHunSkillLevel(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state)
		
		-- 最高质量为8
		if quality_level > 8 then
			quality_level = 8
		end

		local bundle, asset = ResPath.GetCommonImages("a3_quality_text_" .. quality_level)
		self.node_list.wuhun_pingzhi_image.image:LoadSprite(bundle, asset, function()
			self.node_list.wuhun_pingzhi_image.image:SetNativeSize()
		end)

		local active = WuHunWGData.Instance:GetWuHunActiveCfg(self.cur_select_wh_data.wuhun_id)
		self.curr_wuhun_appeid = active and active.appe_image_id or 10114
	end

	if temp_wuhun_appeid ~= self.curr_wuhun_appeid then
		self:InitWuHunShowerModel()
	end
end

function WuHunCultivateView:InitWuHunShowerModel()
	if self.show_model == nil then
		return
	end

	local bundle, asset = ResPath.GetWuHunModel(self.curr_wuhun_appeid)
    self.show_model:SetMainAsset(bundle, asset, function ()
		self.show_model:PlayRoleAction(SceneObjAnimator.Rest)
	end)

	local front_use_index = WuHunFrontWGData.Instance:GetWuHunFrontHuanHuaIndex(self.cur_select_wh_data.wuhun_id)
	local cfg = WuHunFrontWGData.Instance:GetSoulFormationAppimageCfg(self.cur_select_wh_data.wuhun_id, front_use_index)

    if cfg then
        if self.curr_details_hunzhen_appid ~= cfg.app_image_id then
            self.curr_details_hunzhen_appid = cfg.app_image_id
            self.show_model:SetSoulFormationResid(cfg.app_image_id, self.cur_select_wh_data.wuhun_id)
        end
    else
        self.curr_details_hunzhen_appid = nil
        self.show_model:RemoveSoulFormation()
    end

	-- local role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
	-- self.role_intro_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Rest)

	-- self.role_intro_model:FixToOrthographicOnUIScene()
end

-- 刷新消耗相关信息
function WuHunCultivateView:WuHunFlushSpendView()
	if self:WuHunCheckDataIsNil() then
		return
	end

	self:WuHunSelectCurOperateType()
	self:WuHunRefreshRootStatus()
	self:WuHunRefreshRedStatus()
	self:WuHunRefreshHuanHuaStatus()
	self:WuHunRefreshItemSpendItem()
	self:SetWuHunPropertyInfo()
	--刷新激活物体
	if self.cur_select_wh_data.lock then
		self:WuHunShowAttrAndPower(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.lock, self.cur_select_wh_data.promotion_state, self.cur_select_wh_data.order)
		self:WuHunRefreshSkill()
		self:WuHunRefreshLianHun()
		self:WuHunRefreshStarupPanel()
	else
		if self.wuhun_old_level == nil or self.wuhun_old_exp == nil then
			self:WuHunFinalRefresh(false)
		else
			self:WuHunExecuteProgressAni()
		end
	end
end

function WuHunCultivateView:WuHunSelectCurOperateType()
	if self.cur_select_wh_data.lock then	-- 未解锁展示升级
		self.curr_wuhun_operate_type = nil
	end

	if self.curr_wuhun_operate_type == nil then
		self.curr_wuhun_operate_type = WU_OPERATE_BTN.SHENG_JI
	end
end

-- 右侧界面状态刷新
function WuHunCultivateView:WuHunRefreshRootStatus()
	if self:WuHunCheckDataIsNil() then
		return
	end

	self.node_list.wuhun_upgrade:CustomSetActive(not self.cur_select_wh_data.lock)
	self.node_list.wuhun_active_panel:CustomSetActive(self.cur_select_wh_data.lock)
	self.node_list.wuhun_upgrade_root:CustomSetActive(self.curr_wuhun_operate_type == WU_OPERATE_BTN.SHENG_JI)
	self.node_list.wuhun_starup_panel:CustomSetActive(self.curr_wuhun_operate_type == WU_OPERATE_BTN.STAR_UP)
	self.node_list.wuhun_btn_shengji_Image_nor:CustomSetActive(self.curr_wuhun_operate_type ~= WU_OPERATE_BTN.SHENG_JI)
	self.node_list.wuhun_btn_shengji_Image_hl:CustomSetActive(self.curr_wuhun_operate_type == WU_OPERATE_BTN.SHENG_JI)
	self.node_list.wuhun_btn_starup_Image_nor:CustomSetActive(self.curr_wuhun_operate_type ~= WU_OPERATE_BTN.STAR_UP)
	self.node_list.wuhun_btn_starup_Image_hl:CustomSetActive(self.curr_wuhun_operate_type == WU_OPERATE_BTN.STAR_UP)
	self.node_list.wuhun_btn_shengji_text.text.text = (not self.cur_select_wh_data.lock) and Language.Skill.UpLevel or Language.Common.Activate
	self.node_list.wuhun_btn_shengji_text_hl.text.text = (not self.cur_select_wh_data.lock) and Language.Skill.UpLevel or Language.Common.Activate
end

-- 界面红点刷新
function WuHunCultivateView:WuHunRefreshRedStatus()
	if self:WuHunCheckDataIsNil() then
		return
	end

	self.node_list.wuhun_active_remind:CustomSetActive(self.cur_select_wh_data.active_red)
	self.node_list.wuhun_upgrade_remind:CustomSetActive(self.cur_select_wh_data.level_up or self.cur_select_wh_data.breach_up)
	self.node_list.shengji_toggle_remind:CustomSetActive(self.cur_select_wh_data.level_up or self.cur_select_wh_data.breach_up)
	self.node_list.lianhun_toggle_remind:CustomSetActive(self.cur_select_wh_data.purgatory_up)
	self.node_list.starup_toggle_remind:CustomSetActive(self.cur_select_wh_data.starup_red)
end

-- 幻化中状态刷新
function WuHunCultivateView:WuHunRefreshHuanHuaStatus()
	if self:WuHunCheckDataIsNil() then
		return
	end

	local curr_id = WuHunWGData.Instance.wuhun_huanhua_id
	self.node_list.wuhun_yi_huanhua:CustomSetActive((not self.cur_select_wh_data.lock) and curr_id == self.cur_select_wh_data.wuhun_id)
	self.node_list.wuhun_huanhua:CustomSetActive((not self.cur_select_wh_data.lock) and curr_id ~= self.cur_select_wh_data.wuhun_id)
end

-- 刷新格子数据
function WuHunCultivateView:WuHunRefreshItemSpendItem()
	if self:WuHunCheckDataIsNil() then
		return
	end

	--刷新激活物体
	if self.cur_select_wh_data.lock then
		local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.cur_select_wh_data.wuhun_id)
		if active_cfg then
			self.wuhun_item = self:WuHunShowItem(self.wuhun_item, self.node_list.wuhun_item, active_cfg.item, active_cfg.item_num, true)
		end
	else
		self:WuHunRefreshSpendItem(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state)
	end
end

-- 设置属性丹数据信息
function WuHunCultivateView:SetWuHunPropertyInfo()
	local wuhun_id = self.cur_select_wh_data.wuhun_id
	local all_data = WuHunWGData.Instance:GetWuHunShuXingDan(wuhun_id)

	for i = 1, 3 do
		local one_data = all_data[i - 1]
		local num = ItemWGData.Instance:GetItemNumInBagById(one_data.use_item_id)
		local use_num = self.cur_select_wh_data["shuxing_dan" .. i]

		local data = {}
		data.item_id = one_data.use_item_id
		data.wuhun_shuxingdan = {
			use_limit_num = one_data.use_limit_num,
			pellet_index = i - 1,
			use_num = use_num,
			num = num
		}
		self.property_item_tab[i]:SetData(data)

		self.node_list["text_propertyAdd_num" .. i].text.text = "+" .. num
		self.node_list["text_propertyNum" .. i].text.text = use_num or 0
		self.node_list["text_propertyAdd_num" .. i]:CustomSetActive(not self.cur_select_wh_data.lock and use_num < one_data.use_limit_num and num > 0)
	end
end

-- 武魂展示属性
function WuHunCultivateView:WuHunShowAttrAndPower(wuhun_id, level, lock, promotion_state, order)
	local attr_list = WuHunWGData.Instance:GetWuhunAttrlist(wuhun_id, level, lock, promotion_state)
	if not attr_list then
		return
	end

	if self.attr_item_list then
		for i, attr_cell in ipairs(self.attr_item_list) do
			if attr_cell then
				if attr_list[i] and attr_cell then
					if attr_cell.view then
						attr_cell.view:SetActive(true)
					end
					
					attr_cell:SetData(attr_list[i])
					if attr_list[i] and attr_list[i].attr_str == -100 then
						local wuhun_li_cfg = WuHunWGData.Instance:GetWuHunLiCfg(self.cur_select_wh_data.wh_type)
						attr_cell:ResetName(wuhun_li_cfg and wuhun_li_cfg.wh_name or Language.WuHunZhenShen.WuHunZhiLi, COLOR3B.D_GLOD)
					end
				else
					if attr_cell.view then
						attr_cell.view:SetActive(false)
					end
				end
			end
		end
	end

	attr_list = WuHunWGData.Instance:PackageLianhunAttr(attr_list, wuhun_id, order, nil, self.cur_select_wh_data.star)
	self.node_list.wuhun_cap_value.text.text = WuHunWGData.Instance:GetWuHunAttrCap(attr_list)
end

-- 属性技能
function WuHunCultivateView:WuHunRefreshSkill()
	if not self.cur_select_wh_data then
		return
	end

	---刷新技能
	local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.cur_select_wh_data.wuhun_id)

	if not active_cfg then
		self.node_list.wuhun_btn_skill_show:CustomSetActive(false)
		return
	end

	local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state)
	local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_client_cfg then
		self.node_list.wuhun_btn_skill_show:CustomSetActive(false)
		return
	end

	self.node_list.wuhun_btn_skill_show:CustomSetActive(true)
	self.node_list.wuhun_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(wuhun_skill_client_cfg.icon_resource))

	local wuhun_skill_skill_cfg = WuHunWGData.Instance:GetWuHunSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_skill_cfg then
		self.node_list.wuhun_btn_skill_show:CustomSetActive(false)
		return
	end

	self.node_list.wuhun_skill_name.text.text = wuhun_skill_skill_cfg.skill_name
	self.node_list.wuhun_skill_level.text.text = skill_level
end

-- 属性炼魂
function WuHunCultivateView:WuHunRefreshLianHun()
	if self:WuHunCheckDataIsNil() then
		return
	end

	if self.cur_select_wh_data.lock then
		return
	end

	WuHunWGCtrl.Instance:OpenWuHunLianHunView(self.cur_select_wh_data, false)
end

-- 刷新物体展示方式 is_show_condition 是否展示所需
function WuHunCultivateView:WuHunShowItem(item_cell, root, item_id, need_count, is_show_condition)
	if item_cell == nil and root ~= nil then
		item_cell = ItemCell.New(root)
	end

	local item_count = ItemWGData.Instance:GetItemNumInBagById(item_id)

	if item_cell then
		item_cell:SetData({item_id = item_id, num = item_count})
	end

	if is_show_condition then
		if need_count then
			local str = string.format("%s/%s", item_count, need_count)
			local color = item_count >= need_count and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
			item_cell:SetRightBottomTextVisible(true)
			item_cell:SetRightBottomColorText(str, color)
		end

		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)

		if item_cfg and item_cfg.is_display_role and item_cfg.is_display_role <= 0 then
			item_cell:SetIsShowTips(false)

			item_cell:SetClickCallBack(function ()
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
			end)
		else
			item_cell:SetClickCallBack(nil)
			item_cell:SetIsShowTips(true)
		end
	end

	return item_cell
end

function WuHunCultivateView:WuHunRefreshSpendItem(wuhun_id, breach_lv, promotion_state)
	local spend_list = WuHunWGData.Instance:GetWuHunSpendList()
	local show_have_num = false

	if promotion_state == 1 then
		spend_list = WuHunWGData.Instance:GetWuHunBreachList(wuhun_id, breach_lv)
		show_have_num = true
	end

	if self.wuhun_spend_items and spend_list and self.wuhun_spend_items_root then
		for i, wuhun_spend_cell in ipairs(self.wuhun_spend_items) do
			if self.wuhun_spend_items_root[i] then
				self.wuhun_spend_items_root[i]:CustomSetActive(spend_list[i] ~= nil)
			end

			if wuhun_spend_cell and spend_list[i] then
				wuhun_spend_cell = self:WuHunShowItem(wuhun_spend_cell, nil, spend_list[i].item_id, spend_list[i].item_num, show_have_num)
			end
		end
	end
end

function WuHunCultivateView:WuHunFinalRefresh(is_need_anim)
	if not self.cur_select_wh_data then
		return
	end

	self:WuHunShowAttrAndPower(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.lock, self.cur_select_wh_data.promotion_state, self.cur_select_wh_data.order)
	self:WuHunRefreshProgress(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.exp, self.cur_select_wh_data.level, is_need_anim)
	self.wuhun_old_level = self.cur_select_wh_data.level
	self.wuhun_old_exp = self.cur_select_wh_data.exp
	---刷新消耗物
	self.node_list.wuhun_progress:SetActive(self.cur_select_wh_data.promotion_state ~= 1)
	self:WuHunFinalRefreshOperateText()
	self:WuHunRefreshLianHun()
	self:WuHunRefreshSkill()
	self:WuHunRefreshStarupPanel()
end

function WuHunCultivateView:WuHunRefreshProgress(wuhun_id, exp, level, is_anim)
	if not self.cur_select_wh_data then
		return
	end

	---刷新进度条
	local uplevel_exp_val = exp
	local level_cfg = WuHunWGData.Instance:GetWuHunLevelCfg(wuhun_id, level)
	local need_exp_val = level_cfg and level_cfg.next_exp or 0
	self.node_list["wuhun_text_progress"].text.text = uplevel_exp_val .. "/" .. need_exp_val
	if not is_anim then
		self.node_list["wuhun_progress"].slider.value = uplevel_exp_val / need_exp_val
	end
	local level_cfg = WuHunWGData.Instance:GetWuHunLevelCfg(wuhun_id, level + 1)

	local is_max = level_cfg == nil and self.cur_select_wh_data.promotion_state ~= 1
	self.node_list["btn_wuhun_upgrade"]:SetActive(level_cfg ~= nil or self.cur_select_wh_data.promotion_state == 1)
	self.node_list["wuhun_img_level_max"]:SetActive(is_max)
	self.node_list["wuhun_spend_itemlist"]:SetActive(not is_max)

	if is_max then
		self.node_list["wuhun_progress"].slider.value = 1
		self.node_list["wuhun_text_progress"].text.text = Language.WuHunZhenShen.WuhunUpgradeMax
	end
end

function WuHunCultivateView:WuHunExecuteProgressAni()
	if not self.cur_select_wh_data then
		return
	end

	local lerp_level_value = self.cur_select_wh_data.level - self.wuhun_old_level
	local level_cfg = WuHunWGData.Instance:GetWuHunLevelCfg(self.cur_select_wh_data.wuhun_id, self.wuhun_old_level)

	if level_cfg and level_cfg.next_exp then
		local aim_value = self.cur_select_wh_data.exp / level_cfg.next_exp

		if lerp_level_value > 0 then
			self:WuHunRefreshProgress(self.cur_select_wh_data.wuhun_id, level_cfg.next_exp, self.wuhun_old_level, true)
			self:WuHunProgressAni(aim_value, true, nil, function ()
				self.wuhun_old_level = self.wuhun_old_level + 1
				self.wuhun_old_exp = 0
				self:WuHunShowAttrAndPower(self.cur_select_wh_data.wuhun_id, self.wuhun_old_level, self.cur_select_wh_data.lock, self.cur_select_wh_data.promotion_state, self.cur_select_wh_data.order)
				self:WuHunExecuteProgressAni()
				self:WuHunOperateFinalEffect(1)
			end)
			return
		end

		self:WuHunRefreshProgress(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.exp, self.cur_select_wh_data.level, true)
		self:WuHunProgressAni(aim_value, false, nil, function ()
			self:WuHunCheckCanAutoUpGrade()	-- 检测是否可以继续
			self:WuHunFinalRefresh(true)
		end)
	end
end

function WuHunCultivateView:WuHunProgressAni(aim_value, is_full, update_func, complete_func)
	self:WuHunClearGradeSliderTween()
	local slider = self.node_list["wuhun_progress"].slider

	if is_full then
		aim_value = 1
	end

	if aim_value == 0 then
		if complete_func then
			complete_func()
		end

		return
	end

	local time = tonumber(string.format("%.2f", aim_value * 0.8))
	self.wuhun_slider_tween = slider:DOValue(aim_value, time)
	self.wuhun_slider_tween:OnComplete(function ()
		if is_full then
			slider.value = 0
		end

		if complete_func then
			complete_func()
		end
	end)
end

function WuHunCultivateView:WuHunFinalRefreshOperateText()
	if not self.cur_select_wh_data then
		return
	end

	if self.cur_select_wh_data.promotion_state == 1 then
		self:WuHunRefreshOperateText(WU_OPERATE_TEXT.BREACH)
	else
		self:WuHunRefreshBtnState()
	end
end

function WuHunCultivateView:WuHunRefreshOperateText(operate_type)
	if operate_type == WU_OPERATE_TEXT.AUTO then
		self.node_list["wuhun_btn_upgrade_tex"].text.text = Language.NewAppearance.UpGradeBtnDesc[5]
	elseif operate_type == WU_OPERATE_TEXT.STOP then
		self.node_list["wuhun_btn_upgrade_tex"].text.text = Language.NewAppearance.UpGradeBtnDesc[3]
	elseif operate_type == WU_OPERATE_TEXT.BREACH then
		self.node_list["wuhun_btn_upgrade_tex"].text.text = Language.FightSoul.BreakTitle1
	elseif operate_type == WU_OPERATE_TEXT.LIANHUN then
		self.node_list["wuhun_btn_upgrade_tex"].text.text = Language.WuHunZhenShen.WuHunLianHun
	end
end

function WuHunCultivateView:WuHunRefreshBtnState()
	local operate_type = self.wuhun_auto_upgrade and WU_OPERATE_TEXT.STOP or WU_OPERATE_TEXT.AUTO
	self:WuHunRefreshOperateText(operate_type)
end

-- 升级成功或突破成功特效
function WuHunCultivateView:WuHunOperateFinalEffect(operate_type)
	local effect_type = operate_type == 1 and UIEffectName.s_shengji or UIEffectName.s_tupo
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
						is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["wuhun_operate_effect_root"]})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

function WuHunCultivateView:WuHunFinalRefreshOperateText()
	if not self.cur_select_wh_data then
		return
	end

	if self.cur_select_wh_data.promotion_state == 1 then
		self:WuHunRefreshOperateText(WU_OPERATE_TEXT.BREACH)
	else
		self:WuHunRefreshBtnState()
	end
end


-- 检测物品是否足够
function WuHunCultivateView:WuHunCheckItemEnougth(wuhun_id, breach_lv, promotion_state)
	---刷新消耗物
	local spend_list = WuHunWGData.Instance:GetWuHunSpendList()
	local item_id = nil

	if promotion_state == 1 then
		spend_list = WuHunWGData.Instance:GetWuHunBreachList(wuhun_id, breach_lv)
	end

	for i, speed_items in ipairs(spend_list) do
		if speed_items then
			item_id = speed_items.item_id
			local item_count = ItemWGData.Instance:GetItemNumInBagById(speed_items.item_id)
			local need_count = speed_items.item_num or 1
			if item_count >= need_count then
				return true, item_id
			end
		end
	end

	return false, item_id
end

--- 检测是否当前选择了数据
function WuHunCultivateView:WuHunCheckDataIsNil()
	if self.cur_select_wh_data == nil then
		return true
	end

	return false
end

function WuHunCultivateView:WuHunClearGradeSliderTween()
    if self.wuhun_slider_tween then
        self.wuhun_slider_tween:Kill()
        self.wuhun_slider_tween = nil
    end
end

-- 检测自动化
function WuHunCultivateView:WuHunCheckCanAutoUpGrade()
	if self.wuhun_auto_upgrade then	---自动升级
		if self.cur_select_wh_data.promotion_state == 1 then
			self.wuhun_auto_upgrade = false
			return
		end

		if not self:WuHunCheckItemEnougth(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state) then
			self.wuhun_auto_upgrade = false
			return
		end

		self:WuHunCheckCanUpGrade()
	end
end


----------------------------------------------武魂升级界面 开始------------------------------------------------
-- 武魂激活
function WuHunCultivateView:WuHunActive()
	if not self.cur_select_wh_data then
		return
	end

	--刷新激活物体
	if self.cur_select_wh_data.lock then
		local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.cur_select_wh_data.wuhun_id)

		if active_cfg then
			local item_count = ItemWGData.Instance:GetItemNumInBagById(active_cfg.item)
			local need_count = active_cfg.item_num

			if item_count >= need_count then
				WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_GET_WUHUN, self.cur_select_wh_data.wuhun_id, 0)
			else
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = active_cfg.item})
			end
		end
	end
end

-- 武魂升级
function WuHunCultivateView:WuHunCheckCanUpGrade()
	if not self.cur_select_wh_data then
		return
	end

	if self:WuHunCheckItemEnougth(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state) then
		WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_UPGRADE, self.cur_select_wh_data.wuhun_id, 0)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunErrorTips)
	end
end

-- 武魂升级按钮(需要突破时为突破按钮)
function WuHunCultivateView:WuHunUpgradeBtn()
	if not self.cur_select_wh_data then
		return
	end

	if self.cur_select_wh_data.promotion_state == 1 then
		local is_enougth, item_id = self:WuHunCheckItemEnougth(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state)

		if is_enougth then
			WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_BREAK, self.cur_select_wh_data.wuhun_id, 0)
		else
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
		end
	else
		if self:WuHunCheckItemEnougth(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state) then
			self.wuhun_auto_upgrade = not self.wuhun_auto_upgrade
			self:WuHunRefreshBtnState()

			if self.wuhun_auto_upgrade then
				self:WuHunCheckCanUpGrade()
			end
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunErrorTips)
		end
	end
end

-- 切换升级类型
function WuHunCultivateView:WuHunOnShengJiClick()
	if self.curr_wuhun_operate_type ~= WU_OPERATE_BTN.SHENG_JI then
		self.curr_wuhun_operate_type = WU_OPERATE_BTN.SHENG_JI
		self:WuHunRefreshRootStatus()
	end
end

-- 切换升星类型
function WuHunCultivateView:WuHunOnStarUpClick()
	if not self.cur_select_wh_data then
		return
	end

	if self.cur_select_wh_data.lock then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunErrorTips4)
		return
	end

	if self.curr_wuhun_operate_type ~= WU_OPERATE_BTN.STAR_UP then
		self.curr_wuhun_operate_type = WU_OPERATE_BTN.STAR_UP
		self:WuHunRefreshRootStatus()
	end
end


----------------------------------------------武魂升星界面 开始------------------------------------------------
function WuHunCultivateView:WuHunRefreshStarupPanel()
	local slt_data = self.cur_select_wh_data
	local star = slt_data.star
	local star_cfg = WuHunWGData.Instance:GetWuhunStarLevelCfg(slt_data.wuhun_id, star)
	local next_star_cfg = WuHunWGData.Instance:GetWuhunStarLevelCfg(slt_data.wuhun_id, star + 1)
	local is_max_star = next_star_cfg == nil
	self.node_list.node_wuhun_starup_state:CustomSetActive(not is_max_star)
	local red_flag = WuHunWGData.Instance:GetWuhunStarUpRedShow(slt_data.wuhun_id)
	self.node_list.image_wuhun_btn_starup_red:CustomSetActive(red_flag)

	local attr_list = WuHunWGData.Instance:GetWuhunStarUpAttrlist(slt_data.wuhun_id, star)
	for k_i, k_v in ipairs(self.starup_attr_item_list) do
		if attr_list[k_i] then
			if k_v.view then
				k_v.view:SetActive(true)
			end

			k_v:SetData(attr_list[k_i])
			if attr_list[k_i] and attr_list[k_i].attr_str == -100 then
				local wuhun_li_cfg = WuHunWGData.Instance:GetWuHunLiCfg(slt_data.wh_type)
				k_v:ResetName(wuhun_li_cfg and wuhun_li_cfg.wh_name or Language.WuHunZhenShen.WuHunZhiLi, COLOR3B.D_GLOD)
			end
		else
			if k_v.view then
				k_v.view:SetActive(false)
			end
		end
	end

	local star_res_list = GetSpecialStarImgResByStar(star)
	local bundle, asset
	for i = 1, 5 do
		bundle, asset = ResPath.GetCommonImages(star_res_list[i])
		self.node_list["image_wuhun_starup_" .. i].image:LoadSprite(bundle, asset, function()
			self.node_list["image_wuhun_starup_" .. i].image:SetNativeSize()
		end)
	end
	self.node_list.node_wuhun_max_starup:CustomSetActive(is_max_star)

	self.is_can_starup = nil
	if not is_max_star then
		local num = ItemWGData.Instance:GetItemNumInBagById(next_star_cfg.item)
        local is_have = num >= next_star_cfg.item_num
        local color_v = is_have and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        local bom_txt = num .. "/" .. next_star_cfg.item_num
		self.is_can_starup = is_have

		self.wuhun_starup_itemcell:SetData({item_id = star_cfg.item})
		self.wuhun_starup_itemcell:SetRightBottomColorText(bom_txt, color_v)
		self.wuhun_starup_itemcell:SetRightBottomTextVisible(true)
	end
end

function WuHunCultivateView:WuHunBtnStarupClick()
	if not self.is_can_starup then
		return
	end

	WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_STARUP, self.cur_select_wh_data.wuhun_id)
end

function WuHunCultivateView:SetWuHunMaterialBtn()
	local is_show = WuHunWGData.Instance:IsWuHunActive(self.cur_select_wh_data)
	local is_can_buy = WuHunWGData.Instance:CheckWuhunMaterialIsCanBuy(self.cur_select_wh_data.wuhun_id)
	self.node_list.wuhun_material_buy_btn:SetActive(is_show and is_can_buy)
end

----------------------------------------------武魂升星界面 结束------------------------------------------------

function WuHunCultivateView:WuHunOnSkillShowClick()
	if not self.cur_select_wh_data then
		return
	end

	---刷新技能
	local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(self.cur_select_wh_data.wuhun_id)

	if not active_cfg then
		return
	end

	local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(self.cur_select_wh_data.wuhun_id, self.cur_select_wh_data.level, self.cur_select_wh_data.promotion_state)
	local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_client_cfg then
		return
	end

	local wuhun_skill_skill_cfg = WuHunWGData.Instance:GetWuHunSkillCfg(active_cfg.skill_id, skill_level)

	if not wuhun_skill_skill_cfg then
		return
	end

	local show_data = {
		icon = wuhun_skill_client_cfg.icon_resource,
		top_text = wuhun_skill_skill_cfg.skill_name,
		body_text = wuhun_skill_client_cfg.description,
		skill_level = skill_level,
		x = -49,
		y = -260,
		set_pos = true,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function WuHunCultivateView:WuHunOnLianHunClick()
	if not self.cur_select_wh_data then
		return
	end

	if self.cur_select_wh_data.lock then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunErrorTips3)
		return
	end

	if self:WuHunCheckDataIsNil() then
		return
	end

	WuHunWGCtrl.Instance:OpenWuHunLianHunView(self.cur_select_wh_data, true)
end

-- 点击幻化按钮
function WuHunCultivateView:WuHunOnHuanHuaOperate()
	if not self.cur_select_wh_data then
		return
	end

	WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_EQUIP_HUANHUA, self.cur_select_wh_data.wuhun_id, 0)
end

-- 点击已幻化按钮
function WuHunCultivateView:WuHunOnYiHuanHuaOperate()
	if not self.cur_select_wh_data then
		return
	end

	WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_EQUIP_HUANHUA, 0, 0)
end

-- 打开武魂材料直购界面.
function WuHunCultivateView:OnClickBtnOpenWuHunMaterialBuyView()
	ViewManager.Instance:Open(GuideModuleName.WuHunMaterialBuyView, nil, nil, self.cur_select_wh_data)
end

function WuHunCultivateView:OnClickPropertyDanItem(data)
	local wuhun_data = self.cur_select_wh_data
	if wuhun_data.lock then --未解锁
		local wuhun_li_cfg = WuHunWGData.Instance:GetWuHunLiCfg(wuhun_data.wh_type)
		if wuhun_li_cfg then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.WuHunZhenShen.WuhunShuXingDanTip1, wuhun_li_cfg.wh_name))
		end
	elseif data.wuhun_shuxingdan.use_num >= data.wuhun_shuxingdan.use_limit_num then --达到使用上限
		SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuhunShuXingDanTip2)
	elseif data.wuhun_shuxingdan.num <= 0 then --数量不足
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = data.item_id})
	else --直接使用
		WuHunWGCtrl.Instance:SendRoleWuHunOperate(ROLE_OPERA_TYPE.OPER_TYPE_PROPERTY_PELLET, wuhun_data.wuhun_id, data.wuhun_shuxingdan.pellet_index)
	end

	return true
end

function WuHunCultivateView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end

-------------------------TianShenWuHunRender-------------------------
TianShenWuHunRender = TianShenWuHunRender or BaseClass(BaseRender)

function TianShenWuHunRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.icon)
	self.item_cell:SetCellBgEnabled(false)
	self.item_cell:SetUseButton(false)
	self.item_cell:SetEffectRootEnable(false)
end

function TianShenWuHunRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TianShenWuHunRender:OnFlush()
	self.node_list.name.text.text = self.data.wh_name

	local red = false
	local lv_str = ""
	if self.data.lock then
		red = self.data.active_red
		lv_str = Language.WuHunZhenShen.WuhunNoActive
	else
		red = self.data.show_red
		lv_str = string.format(Language.WuHunZhenShen.WuhunUpgradeLevel2, self.data.level)
	end
	self.node_list["red"]:SetActive(red)

	self.node_list.lv.text.text = lv_str

	local star_cfg = WuHunWGData.Instance:GetWuhunStarLevelCfg(self.data.wuhun_icon, self.data.star)
	self.item_cell:SetData({ item_id = star_cfg.item })
	self.item_cell:SetIconGrey(self.data.lock)
	self.item_cell:SetGraphicGreyCualityBg(self.data.lock)
end

function TianShenWuHunRender:OnSelectChange(is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end