WelkinData = WelkinData or BaseClass()

function WelkinData:__init()
	if WelkinData.Instance then
		error("[WelkinData] Attempt to create singleton twice!")
		return
	end
	WelkinData.Instance = self
end

function WelkinData:__delete()
	WelkinData.Instance = nil
end
------天仙阁---------

function WelkinData:CloseCallBack()
	if self.role_display then
		self.role_display:GetRootNode():stopAllActions()
		self.is_play_anim = false
	end
end

-- 设置当前通过层数
function WelkinData:SetPassLevel(level)
	self.welkin_pass_level = level
end

-- 获取当前通过层数
function WelkinData:GetPassLevel()
	if nil == self.welkin_pass_level or self.welkin_pass_level < 0 
		or self.welkin_pass_level > #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg then
		return -1
	end
	return self.welkin_pass_level
end

-- 设置下一层通过层数
function WelkinData:SetPassFetchLevel(fetch_level)
	self.welkin_pass_fetch_level = fetch_level
end

-- 获取下一层通过层数
function WelkinData:GetPassFetchLevel()
	return self.welkin_pass_fetch_level
end

-- 获取配置上最大等级
function WelkinData:GetCfgMaxLevel()
	return #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg	
end

-- 是否达到顶层
function WelkinData:IsReachMaxLevel()
	return self.welkin_pass_level == #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg
end

-- 根据等级获取试炼称号
function WelkinData:GetCurTitleName(level)
	local cur_level = level or self.welkin_pass_level
	if nil == ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[cur_level] then return "" end
	return ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[cur_level].title_name
end

-- 根据等级获取当前站台信息
function WelkinData:GetTerraceListCfgByLevel(level)
	local terrace_list = {}
	if self:IsReachMaxLevel() then		-- 满级特殊处理
		local first_terrace_level = math.floor(level / 4) * 4 + 1 - 4
		for i = 0, 3 do
			table.insert(terrace_list, ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[first_terrace_level + i])
		end
		return terrace_list
	end

	local first_terrace_level = math.floor(level / 4) * 4 + 1
	for i = 0, 3 do
		table.insert(terrace_list, ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[first_terrace_level + i])
	end
	return terrace_list
end

-- 根据等级获取当前站台奖励
function WelkinData:GetTerraceRewardByLevel(level)
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").day_reward) do
		if v.level == level then
			return v
		end
	end
	return nil
end

function WelkinData:GetTerraceRewardRemind()
	local reward_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_TIANXIANGEFB_DAY_REWARD)
	return self.welkin_pass_level > 0 and reward_num == 0 and 1 or 0
end

-- 根据等级获取配置信息
function WelkinData:GetCfgByLevel(level)
	return ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").level_cfg[level]
end

-- 根据当前等级获取天宫试炼增加的总战力
function WelkinData:GetTotalZhanliByLevel(level)
	local cfg = self:GetCfgByLevel(level)
	if nil == cfg then return 0 end
	local attribute = AttributeMgr.GetAttributteByClass(cfg)
	local total_zhanli = AttributeMgr.GetCapability(attribute)
	return total_zhanli
end

-- 根据当前等级获取下一级增加的战力
function WelkinData:GetNextZhanliByLevel(level)
	local next_level = level + 1
	if next_level > self:GetCfgMaxLevel() then return 0 end		--达到最大值

	local cur_cfg = self:GetCfgByLevel(level)
	local cur_attribute = AttributeMgr.GetAttributteByClass(cur_cfg)
	local next_cfg = self:GetCfgByLevel(next_level)
	local next_attribute = AttributeMgr.GetAttributteByClass(next_cfg)

	local m_attribute = AttributeMgr.LerpAttributeAttr(cur_attribute, next_attribute)
	local add_zhanli = AttributeMgr.GetCapability(m_attribute)
	return add_zhanli
end