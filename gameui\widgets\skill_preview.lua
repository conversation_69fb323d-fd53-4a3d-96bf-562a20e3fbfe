SkillPreview = SkillPreview or BaseClass()

-- 时间轴操作类型
local TIMELINE_OPERATE = {
	CREATE_CHARACTER = 0,				-- 生成人物
	HIDE_CHARACTER = 1,					-- 隐藏人物
	ADD_BUFF = 2,						-- 增加buff
	REMOVE_BUFF = 3, 					-- 移除buff
	PALY_ANIM = 4, 						-- 播放动画
	MOVE = 5,  							-- 移动角色
	BEHURT = 6, 						-- 被打
	ATTACK = 7, 						-- 攻击
	RESET_POS = 8, 						-- 重置位置与旋转
	INSTANTIATE_PREFAB = 9, 			-- 实例化预制体
	CHANGE_CAMERA_DISTANCE = 10, 		-- 修改摄像机距离
	FLOAT_TEXT = 11, 					-- 飘字
	ATTACK_BY_INDEX = 12, 				-- 根据角色技能下标播放技能
	ANIMATOR_EVENT = 13,				-- 模拟触发动画回调(播放技能编辑上挂的特效)\
	CHANGE_SPECIAL_APPEARANCE = 14, 	-- 变身(特殊形象id)
	USE_SKILL = 15,						-- 使用技能
}

local CREATE_CHARACTER_TYPE = {
	ROLE = 1, 							-- 角色
	MONSTER = 2, 						-- 怪物
	TIANSHEN = 3,                       -- 天神
	JiJia = 4,                       	-- 机甲
	Jingjie = 5,                       	-- 怒气变身
}

-- 时间轴激活前提条件类型
local TIMELINE_PRECONDITION_TYPE = {
	WUHUN = 1 							-- 武魂镶嵌有某个效果的词条
}

local PREVIEW_SKILL_TYPE = {
	NORMAL_SKILL = 1, 					-- 普攻
	SKILL = 2,
}

local center_pos = Vector3(30, 18, -18)
local center_rot = Vector3(30, -60, 0)

function SkillPreview:__init(root_obj, render_target_raw, is_center, bundle, asset, camera_pos)
	self.root_obj = root_obj												--	目标位置
	self.render_target_raw = render_target_raw								--	目标RawImage
	self.render_width = self.render_target_raw.rect.rect.width				--  RT 宽度
	self.render_height = self.render_target_raw.rect.rect.height			--  RT 高度
	self.previewCb = nil													--  创建完成回调
	self.scene_obj = nil													--  场景物体
	self.main_role_vo_copy = nil 											-- 	主角属性
	self.is_playing = false													--	是否在播放
	self.cur_skill_id = nil 												-- 	当前正在播放的技能
	self.cur_timeline_list = nil 											-- 	当前技能对应的时间轴列表
	self.play_start_time = nil												--	开始播放预览的时间
	self.character_dic = {}													-- 	角色列表
	self.character_pool = {}												--  角色池
	self.character_move_tweener = {}										-- 	角色移动动画
	self.instantiate_loader_list = {}										--  加载器列表
	self.last_activeInHierarchy = false										--  是否在可视视图内
	self.is_need_hide = true												-- 	是否需要隐藏
	self.save_appearance_list = {}											--	保存的外观信息列表
	self.is_loaded = false
	self.play_end_callback = nil
	if self.render_target_raw then
		self.render_texture = UnityEngine.RenderTexture.GetTemporary(self.render_width, self.render_height, 24, UnityEngine.Experimental.Rendering.GraphicsFormat.B10G11R11_UFloatPack32)
		self.render_target_raw.raw_image.texture = self.render_texture
	end

	Runner.Instance:AddRunObj(self, nil, true)
	self:CreateSkillPreviewScene(bundle, asset, is_center)
end

-- 创建渲染对象
function SkillPreview:CreateSkillPreviewScene(bundle, asset, is_center)
	local scene_loader = AllocAsyncLoader(self, "skill_preview_scene")
	scene_loader:SetIsUseObjPool(false)
	scene_loader:SetParent(self.root_obj.transform)
	local bundle_name = bundle or "uis/view/skill_preview_scene_prefab"
	local asset_name = asset or "skill_preview_scene"

	scene_loader:Load(bundle_name, asset_name, function(obj)
		if IsNil(obj) then return end

		--  丢走到视野范围外
		self.scene_obj = obj
		-- local pos = self.scene_obj.transform.localPosition
		-- pos.x = 5000 + 10 * 3000
		-- self.scene_obj.transform.localPosition = pos

		local name_table = obj:GetComponent(typeof(UINameTable))
		self.node_list = U3DNodeList(name_table, self)

		-- 设置摄像机位置
		if is_center then
			Transform.SetLocalPositionXYZ(self.node_list.rt_camera.transform, center_pos.x, center_pos.y, center_pos.z)
			self.node_list.rt_camera.transform.rotation = Quaternion.Euler(center_rot.x, center_rot.y, center_rot.z)
		end

		-- 设置RT
		if self.render_target_raw then
	   		self.node_list.rt_camera.camera.targetTexture = self.render_texture
	   	end

		self.is_loaded = true
		if self.render_target_raw then
			self.render_target_raw.raw_image.enabled = true
		end

		-- 回调
		if self.previewCb then
			self.previewCb()
			self.previewCb = nil
		end

		-- self:TrySetCamera()
	end)
end

function SkillPreview:__delete()
	self.root_obj = nil
	self.render_target_raw = nil
	self.render_width = nil
	self.render_height = nil
	self.scene_obj = nil
	self.main_role_vo_copy = nil
	self.last_activeInHierarchy = false
	self.save_appearance_list = nil
	self.play_end_callback = nil
	if self.root_obj ~= nil and not IsNil(self.root_obj.gameObject) then
		self.root_obj:SetActive(false)
	end

	if self.character_dic then
		for k,v in pairs(self.character_dic) do
			v.obj:DeleteMe()
		end
	
		self.character_dic = nil
	end

	if self.render_texture then
		UnityEngine.RenderTexture.ReleaseTemporary(self.render_texture)
		self.render_texture = nil
	end

	self:OnPlayEnd()
	self:ClearAllLoader()
	self:ClearCharacterPool()
	self:KillAllMoveTweener()
	self:KillCameraTweener()
	self:CleanWHSimulateGeneralAttackCDTimer()
	self:CleanWHModelRemoveCDTimer()
	self:ClearDelaySimulateGeneralAttackOpera()

	Runner.Instance:RemoveRunObj(self)
end

-- 清除所有的加载器
function SkillPreview:ClearAllLoader()
	for i,v in ipairs(self.instantiate_loader_list) do
		v:Destroy()
	end
	self.instantiate_loader_list = {}
end

-- 清除所有的对象池中的对象
function SkillPreview:ClearCharacterPool()
	for _,v in pairs(self.character_pool) do
		if not v.obj:IsDeleted() then
			v.obj:DeleteMe()
		end
	end
	self.character_pool = {}
end

-- 清除所有的移动的动画
function SkillPreview:KillAllMoveTweener()
	for character_index, v in pairs(self.character_move_tweener) do
		self:KillMoveTweener(character_index)
	end
	self.character_move_tweener = {}
end

-- 清除摄像机上的动画
function SkillPreview:KillCameraTweener()
	if self.camera_tweener then
		self.camera_tweener:Kill()
		self.camera_tweener = nil
	end
end

-- 当前技能预览播放完毕
function SkillPreview:OnPlayEnd()
	self.is_playing = false
	self.cur_timeline_list = nil
	self.play_start_time = nil

	if self.play_end_callback then
		self.play_end_callback()
	end
end

-- 开始播放一个技能预览
function SkillPreview:OnPlayStart(need_move_pool)
	self.is_playing = true
	self.cur_timeline_list = nil

	if need_move_pool then
		self:MoveAllCharacterToPool()
	end

	self:KillAllMoveTweener()
	self:ClearAllLoader()
	self:KillCameraTweener()

	self.play_start_time = Status.NowTime
end

-- 移动所有人物到人物池
function SkillPreview:MoveAllCharacterToPool()
	if self.character_dic then
		for index, _ in pairs(self.character_dic) do
			self:MoveCharacterToPool(index, self.is_need_hide)					
		end
	end
end

-- 移除一个人物到人物池
function SkillPreview:MoveCharacterToPool(index, need_hide)
	local character_data = self.character_dic[index]

	if not character_data then
		print_error("没有找到需要隐藏的人物请检查，人物下标：", index)
		return false
	end

	self.character_pool[character_data.id] = character_data 			-- key值为时间轴id
	character_data.obj:GetRoot().gameObject:SetActive(not need_hide)
	local ain_obj = character_data.obj
	local is_clear_effect = ain_obj:IsMonster()

	if is_clear_effect then
		ain_obj:ClearActorEffect()
	end

	self.character_dic[index] = nil
	return true
end

-- 获取当前的时间轴列表
function SkillPreview:GetCurTimelineList()
	if not self.cur_skill_id then
		print_error("技能预览请传入对应的技能id")
		return nil
	end

	if self.cur_timeline_list then
		return self.cur_timeline_list
	end

	local timeline_cfg_list = SkillPreviewData.Instance:GetSkillTimeLineDataBySkillId(self.cur_skill_id)
	if not timeline_cfg_list then
		print_error("当前技能没有找到技能预览配置,请检查,skill_id:", self.cur_skill_id)
		return nil
	end

	self.cur_timeline_list = TableCopy(timeline_cfg_list) 
	return self.cur_timeline_list
end

-- Update 执行
function SkillPreview:Update(now_time, elapse_time)
	if (not self.root_obj) or IsNil(self.root_obj.gameObject) or IsNil(self.scene_obj) or (not self.is_playing) then
		return
	end

	local timeline_list = self:GetCurTimelineList()
	if timeline_list == nil then
		print_error("所给技能没有拿到预览配置， skill_id:", self.cur_skill_id)
		self:OnPlayEnd()
		return
	end
	
	-- 一帧最多执行十个操作
	for i = 0, 9 do
		local timeline_data = timeline_list[#timeline_list]
		if timeline_data then
			if Status.NowTime - self.play_start_time > timeline_data.operator_time then
				if self:Operate(timeline_data) then
					timeline_list[#timeline_list] = nil
				else
					-- 出了问题，结束播放
					self:OnPlayEnd()
					break
				end
			else
				break
			end
		else
			-- 当前技能播放完毕，结束播放
			self:OnPlayEnd()
			break
		end
	end
end

-- 执行操作
function SkillPreview:Operate(timeline_data)
	if IsNil(self.scene_obj) then
		return false
	end

	local timeline_cfg = timeline_data.timeline_cfg
	local index = timeline_cfg.operate_param_1			
	if timeline_cfg.operate == TIMELINE_OPERATE.CREATE_CHARACTER then
		return self:OperateCreateCharacter(timeline_data)
	elseif timeline_cfg.operate == TIMELINE_OPERATE.HIDE_CHARACTER then
		if not self:MoveCharacterToPool(index, self.is_need_hide) then
			return false
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.ADD_BUFF 
 		or timeline_cfg.operate == TIMELINE_OPERATE.REMOVE_BUFF then
		local character_data = self.character_dic[index]
		if character_data then
			local buff_type = timeline_cfg.operate_param_2 
			local product_id = nil
			if timeline_cfg.operate_param_3 and timeline_cfg.operate_param_3 ~= "" then
				product_id = timeline_cfg.operate_param_3
			end

			local time = nil
			if timeline_cfg.operate_param_4 and timeline_cfg.operate_param_4 ~= "" then
				time = timeline_cfg.operate_param_4
			end
			if timeline_cfg.operate == TIMELINE_OPERATE.ADD_BUFF then
				character_data.obj:AddBuff(buff_type, product_id, time, BUFF_CHANGE_BY.SKILL_PREVIEW) --这里传true也要播放buff变化的特效
			elseif timeline_cfg.operate == TIMELINE_OPERATE.REMOVE_BUFF then
				character_data.obj:RemoveBuff(buff_type, product_id, BUFF_CHANGE_BY.SKILL_PREVIEW)
			end
		else
			print_error('没有找到对应人物, 人物下标：', index, timeline_data)
			return false
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.PALY_ANIM then
		local character_data = self.character_dic[index]
		local target_obj_index = timeline_cfg.operate_param_3 or 0
		local target_data = self.character_dic[target_obj_index]
		if target_data and target_data.obj then
			character_data.obj:SetEnemyPos(target_data.obj:GetRoot().gameObject)
		end

		if character_data then
			character_data.obj:CrossAction(SceneObjPart.Main, timeline_cfg.operate_param_2)
		else
			print_error('没有找到对应人物, 人物下标：', index, timeline_data)
			return false
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.BEHURT then
		local character_data = self.character_dic[index]
		if character_data then
			if character_data.obj:IsHurt() then
				character_data.obj:DoStand()
			end
			character_data.obj:DoHurt()
		else
			print_error('没有找到触发被打的人物, 人物下标：', index, timeline_data)
			return false
		end
		-- 人物攻击
	elseif timeline_cfg.operate == TIMELINE_OPERATE.ATTACK then
		local skill_id = timeline_cfg.operate_param_2
		local character_data = self.character_dic[index]
		local target_obj_index = timeline_cfg.operate_param_3 or 0
		local target_data = self.character_dic[target_obj_index]
		if character_data and character_data.obj then
			if target_data and target_data.obj then
				character_data.obj:SetEnemyPos(target_data.obj:GetRoot().gameObject)
				character_data.obj:DoAttackForNoTarget(skill_id)
			else
				character_data.obj:DoAttackForNoTarget(skill_id)
			end

			self:CheckIsSpecialSkillShow(skill_id, character_data)
		else
			print_error('没有发动攻击的人物, 人物下标：', index, timeline_data)
			return false
		end
		-- 人物移动
	elseif timeline_cfg.operate == TIMELINE_OPERATE.MOVE then
		local local_pos_str = Split(timeline_cfg.operate_param_2, ',') 					-- 本地坐标
		local local_pos_x = tonumber(local_pos_str[1] or 0)
		local local_pos_z = tonumber(local_pos_str[2] or 0)
		local local_rota_y = timeline_cfg.operate_param_3 or 0 							-- 本地旋转
		local time = timeline_cfg.operate_param_4 										-- 移动时长
		local character_data = self.character_dic[index]
		if character_data then
			local character_obj = character_data.obj
			-- 移动
			local trans = character_obj:GetRoot().gameObject.transform
			self:KillMoveTweener(index)
			self.character_move_tweener[index] = trans:DOLocalMove(Vector3(local_pos_x, 0, local_pos_z), time)

			-- 旋转
			local euler = trans.localEulerAngles
			euler.y = local_rota_y
			trans.localEulerAngles = euler
			character_data.original_euler = euler
			local character_data = self.character_dic[index]
		else
			print_error('没有找到需要移动的人物, 人物下标：', index, timeline_data)
			return false
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.RESET_POS then
	   if not self:ResetPos(index) then
		   return false
	   end
	   	-- 生成物体
	elseif timeline_cfg.operate == TIMELINE_OPERATE.INSTANTIATE_PREFAB then
		local loader_key = string.format("%s-%s-%s", "TIMELINE_OPERATE.INSTANTIATE_PREFAB", timeline_cfg.id, timeline_data.operator_time)
		local prefab_loader = AllocAsyncLoader(self, loader_key)
		table.insert(self.instantiate_loader_list, prefab_loader)
		prefab_loader:SetIsUseObjPool(false)
		local character_data = self.character_dic[index]
		if character_data and character_data.obj and not IsNil(character_data.obj:GetRoot().gameObject) then
			prefab_loader:SetParent(character_data.obj:GetRoot().gameObject.transform)
		else
			prefab_loader:SetParent(self.node_list["gameobject_list"].transform)
		end

		local alive_time = 5
		if timeline_cfg.operate_param_6 ~= "" then
			alive_time = math.min(timeline_cfg.operate_param_6, 10)
		end
		prefab_loader:SetObjAliveTime(alive_time)
		prefab_loader:Load(timeline_cfg.operate_param_2, timeline_cfg.operate_param_3, function(obj)
			if IsNil(obj) then
				return
			end
			local obj_transform = obj.transform
			-- 位置
			local character_data = self.character_dic[index]
			if character_data then
				if character_data.obj and character_data.obj:GetRoot() and not IsNil(character_data.obj:GetRoot().gameObject) then
					obj_transform.position = character_data.obj:GetRoot().gameObject.transform.position
				else
					obj_transform.localPosition = character_data.original_pos
				end
			else
				print_error("没有找到物体生成位置对应的人物：", timeline_cfg.operate_param_3, timeline_data)
				return false
			end

			-- 旋转
			if timeline_cfg.operate_param_4 ~= "" then
				local euler = obj_transform.localEulerAngles
				euler.y = timeline_cfg.operate_param_4
				obj_transform.localEulerAngles = euler
			end

			-- 缩放
			if timeline_cfg.operate_param_5 ~= "" then
				local scale = obj_transform.localScale
				scale.x = timeline_cfg.operate_param_5
				scale.y = timeline_cfg.operate_param_5
				scale.z = timeline_cfg.operate_param_5
				obj_transform.localScale = scale
			end
	 	end, nil, function(obj) 
	 	end)
	elseif timeline_cfg.operate == TIMELINE_OPERATE.CHANGE_CAMERA_DISTANCE then
		local fov_value = timeline_cfg.operate_param_1
		self:KillCameraTweener()
		local duration = 0.8
		self.camera_tweener = DG.Tweening.DOTween.Sequence()
		self.camera_tweener:Join(self.node_list.rt_camera.camera:DOFieldOfView(fov_value, duration):SetEase(DG.Tweening.Ease.OutCubic))
	-- 根据人物性别和职业播放普攻
	elseif timeline_cfg.operate == TIMELINE_OPERATE.ATTACK_BY_INDEX then 
		local index = timeline_cfg.operate_param_1										-- 人物下标
		local character_data = self.character_dic[index]
		if character_data and character_data.obj:IsRole() then
			local skill_type = timeline_cfg.operate_param_2
			local skill_index = timeline_cfg.operate_param_3
			local skill_id = nil
			if skill_type == PREVIEW_SKILL_TYPE.NORMAL_SKILL then
				local normal_skill_list = SkillWGData.Instance:GetNormalSkillIdList(character_data.obj.vo.sex, character_data.obj.vo.prof)
				skill_id = normal_skill_list[skill_index]
			elseif skill_type == PREVIEW_SKILL_TYPE.SKILL then
				local skill_list = SkillWGData.Instance:GetSkillIdList(character_data.obj.vo.sex, character_data.obj.vo.prof)
				skill_id = skill_list[skill_index]
			end	
			if skill_id then
				character_data.obj:DoAttackForNoTarget(skill_id)
			else
				print_error("没有找到普攻技能id，index：", skill_index, timeline_data)
				return false
			end
		else
			print_error('没有找到需要普攻的角色(不支持怪物), 人物下标：', index, timeline_data)
			return false
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.ANIMATOR_EVENT then
		local index = timeline_cfg.operate_param_1
		local character_data = self.character_dic[index]
		if character_data and character_data.obj:IsCharacter() then
			local target_index = timeline_cfg.operate_param_3
			local target_obj = self.character_dic[target_index].obj
			character_data.obj:SimulationAnimatorEvent(timeline_cfg.operate_param_2, target_obj)
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.CHANGE_SPECIAL_APPEARANCE then
		local index = timeline_cfg.operate_param_1
		local character_data = self.character_dic[index]
		if character_data and character_data.obj:IsCharacter() then
			character_data.obj.vo.special_appearance = timeline_cfg.operate_param_2
			character_data.obj.vo.appearance_param = timeline_cfg.operate_param_3
			character_data.obj:UpdateAppearance(true)
		end
	elseif timeline_cfg.operate == TIMELINE_OPERATE.USE_SKILL then
		local index = timeline_cfg.operate_param_1
		local character_data = self.character_dic[index]
		if character_data and character_data.obj:IsCharacter() then
			local sex = character_data.obj.vo.sex
			local prof = character_data.obj.vo.prof
			local skill_id_list = SkillWGData.Instance:GetSkillIdList(sex, prof)
			local skill_index = timeline_cfg.operate_param_2 or 1
			local skil_id = skill_id_list[skill_index]
			character_data.obj:SetEnemyPos(character_data.obj:GetRoot().gameObject)
			character_data.obj:DoAttackForNoTarget(skil_id)
		end
	end

	return true
end

-- 创建一个角色
function SkillPreview:OperateCreateCharacter(timeline_data)
	local timeline_cfg = timeline_data.timeline_cfg
	local index = timeline_cfg.operate_param_1	

	-- if self.character_dic[index] ~= nil then
	-- 	print_error("重复生成了同一个下标的人物，index:", index, timeline_data)
	-- 	return false
	-- end

	local char_type = timeline_cfg.operate_param_2 									-- 人物类型
	local local_pos_str = Split(timeline_cfg.operate_param_3, ',') 					-- 本地坐标
	local local_pos_x = tonumber(local_pos_str[1] or 0)
	local local_pos_y = tonumber(local_pos_str[2] or 0)
	local local_pos_z = tonumber(local_pos_str[3] or 0)
	local local_rota_y = timeline_cfg.operate_param_4 or 0 							-- 本地旋转
	local local_scale = timeline_cfg.operate_param_5 or 0 							-- 本地缩放
	local character_data = nil
	local character_obj = nil
	local pool_id = timeline_cfg.id

	-- 先从人物池里取
	if self.character_dic[index] == nil then
		self.character_dic[index] = self.character_pool[pool_id]
	end

	if not self.character_dic[index] then
		character_data = {}
		character_data.id = pool_id
		character_data.index = index
		self.character_dic[index] = character_data
		
		if char_type == CREATE_CHARACTER_TYPE.ROLE then
			local role_vo = nil
			if timeline_cfg.operate_param_6 == "<main_role>" then
				local main_role_vo = SkillPreviewData.Instance:GetMainRoleVoCfgById(timeline_cfg.operate_param_7)
				role_vo = self:GetMainRoleVoCopy(main_role_vo, index)
			else
				local role_vo_cfg = nil
				if timeline_cfg.operate_param_6 and timeline_cfg.operate_param_6 ~= "" and type(timeline_cfg.operate_param_6) == "number" then
					role_vo_cfg = self:GetRoleVoCfg(timeline_cfg.operate_param_6)
				end
				role_vo = RoleVo.New()
				role_vo.name = ""
				role_vo.level = 1
				role_vo.sex = role_vo_cfg and role_vo_cfg.sex or 1
				role_vo.prof = role_vo_cfg and role_vo_cfg.prof or 1
				role_vo.special_appearance = role_vo_cfg and role_vo_cfg.special_appearance or 0
				role_vo.appearance_param = role_vo_cfg and role_vo_cfg.appearance_param or 0
				role_vo.max_hp = 10
				role_vo.hp = 10
				role_vo.appearance = ProtocolStruct.RoleAppearance()
			end

			character_obj = SkillPreviewRole.New(role_vo)
			character_obj:SetWuHunCreateCallBack(BindTool.Bind(self.WuHunCreateCallBack, self))
		elseif char_type == CREATE_CHARACTER_TYPE.MONSTER then
			local monster_id = tonumber(timeline_cfg.operate_param_6)
			local monster_vo = RoleVo.New()
			monster_vo.monster_id = monster_id
			monster_vo.name = ""
			monster_vo.level = 1
			monster_vo.sex = 1
			monster_vo.prof = 1
			monster_vo.max_hp = 10
			monster_vo.hp = 10
			character_obj = SkillPreviewRole.New(monster_vo)
		elseif char_type == CREATE_CHARACTER_TYPE.LINGCHONG then
			local lingchong_appeid = tonumber(timeline_cfg.operate_param_6)
			local pet_vo = PetObjVo.New()
			pet_vo.lingchong_appeid = lingchong_appeid
			pet_vo.name = ""
			pet_vo.level = 1
			pet_vo.prof = 1
			pet_vo.max_hp = 10
			pet_vo.hp = 10
			character_obj = SkillPreviewLingChong.New(pet_vo)
		elseif char_type == CREATE_CHARACTER_TYPE.TIANSHEN then
			local role_vo = self:GetMainRoleVoCopy(nil, index)
			role_vo.special_appearance = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM
			role_vo.appearance_param = timeline_cfg.operate_param_6
			character_obj = SkillPreviewRole.New(role_vo)
		elseif char_type == CREATE_CHARACTER_TYPE.Jingjie then
			local role_vo = self:GetMainRoleVoCopy(nil, index)
			role_vo.special_appearance = SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN
			role_vo.appearance_param = timeline_cfg.operate_param_6
			character_obj = SkillPreviewRole.New(role_vo)
		end

		character_data.obj = character_obj
		character_obj:InitAppearance()
		character_obj:UpdateAppearance()
		character_obj:InitInfo()
		local draw_obj = character_obj:GetDrawObj()
		local draw_obj_trans = draw_obj:GetTransfrom()
		draw_obj_trans:SetParent(self.node_list["character_list"].transform, false)
	else
		character_data = self.character_dic[index]
		character_obj = character_data.obj
		if character_data.obj:GetRoot().gameObject.activeSelf == false then
			character_data.obj:GetRoot().gameObject:SetActive(true)
		end

		character_obj.vo.special_appearance = 0
		character_obj.vo.appearance_param = 0
		if char_type == CREATE_CHARACTER_TYPE.TIANSHEN then
			character_obj.vo.special_appearance = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM
			character_obj.vo.appearance_param = timeline_cfg.operate_param_6
		elseif char_type == CREATE_CHARACTER_TYPE.Jingjie then
			character_obj.vo.special_appearance = SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN
			character_obj.vo.appearance_param = timeline_cfg.operate_param_6
		end

		character_obj:UpdateAppearance()
		character_obj:RemoveAllBuff()
		character_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)
	end

	if character_obj then
		local obj_transform = character_obj:GetRoot().gameObject.transform
		-- 坐标
		local pos = obj_transform.localPosition
		pos.x = local_pos_x
		pos.y = local_pos_y
		pos.z = local_pos_z
		obj_transform.localPosition = pos
		character_data.original_pos = pos

		-- 旋转
		local euler = obj_transform.localEulerAngles
		euler.y = local_rota_y
		obj_transform.localEulerAngles = euler
		character_data.original_euler = euler

		local wuhun_obj = character_obj:GetWuHun()
		if wuhun_obj then
			local wuhun_obj_trans = wuhun_obj:GetRoot().gameObject.transform
			wuhun_obj_trans.localEulerAngles = euler
		end

		-- 缩放
		local scale = obj_transform.localScale
		scale.x = local_scale
		scale.y = local_scale
		scale.z = local_scale
		obj_transform.localScale = scale
		character_data.original_scale = scale

		-- 天神特效修正特效大小
		if char_type == CREATE_CHARACTER_TYPE.TIANSHEN then
			character_obj:CorrectEffectTriggerCustomScale(local_scale)
		end
	else
		print_error("生成人物失败，请检查配置：", index, timeline_data)
		return false
	end

	return true
end

-- 武魂obj创建回调
function SkillPreview:WuHunCreateCallBack(pre_role)
	local wuhun_obj = pre_role:GetWuHun()
	if wuhun_obj then
        local draw_obj = wuhun_obj:GetDrawObj()
        local draw_obj_trans = draw_obj:GetTransfrom()
        draw_obj_trans:SetParent(self.node_list["character_list"].gameObject.transform)
        draw_obj_trans.localPosition = u3dpool.vec3(0, 0, 0)
		local character_trans = pre_role:GetRoot().gameObject.transform
        draw_obj_trans.localEulerAngles = character_trans.localEulerAngles
        draw_obj:SetScale(1, 1, 1)
    end
end

-- local COPY_KEY = {"sex", "prof", "wing_appeid", "yaobing_appeid", "shenwu_appeid", "tianshenshenqi_appeid", "appearance"} -- "daohun_appeid"
function SkillPreview:GetMainRoleVoCopy(main_role_cfg_vo, index)
	if self.main_role_vo_copy == nil then
		self.main_role_vo_copy = {}
	end

	if self.main_role_vo_copy[index] ~= nil then
		return self.main_role_vo_copy[index]
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local shower_vo = RoleVo.New()
	shower_vo.name = ""
	shower_vo.level = 1
	shower_vo.sex = role_vo.sex or 1
	shower_vo.prof = role_vo.prof or 1
	shower_vo.max_hp = 10
	shower_vo.hp = 10
	shower_vo.shenwu_appeid = 0
	shower_vo.appearance = ProtocolStruct.RoleAppearance()
	shower_vo.appearance.fashion_body = main_role_cfg_vo and main_role_cfg_vo.fashion_body or role_vo.appearance.fashion_body 
	shower_vo.appearance.fashion_wuqi = main_role_cfg_vo and main_role_cfg_vo.fashion_wuqi or role_vo.appearance.fashion_wuqi 
	shower_vo.appearance.fashion_guanghuan = main_role_cfg_vo and main_role_cfg_vo.fashion_guanghuan or role_vo.appearance.fashion_guanghuan 
	shower_vo.jianzhen_appeid = main_role_cfg_vo and main_role_cfg_vo.jianzhen_appeid or role_vo.jianzhen_appeid
	shower_vo.wing_appeid = main_role_cfg_vo and main_role_cfg_vo.wing_appeid or role_vo.fashion_body 

	shower_vo.appearance.fazhen_id = main_role_cfg_vo and main_role_cfg_vo.fazhen_id or role_vo.appearance.fazhen_id 
	shower_vo.appearance.skill_halo_id = main_role_cfg_vo and main_role_cfg_vo.skill_halo_id or role_vo.appearance.skill_halo_id 
	shower_vo.appearance_param = 0
	shower_vo.special_appearance = 0

	shower_vo.mount_appeid = main_role_cfg_vo and main_role_cfg_vo.mount_appeid or 0
	shower_vo.fight_mount_skill_level = 0
	shower_vo.wuhun_id = main_role_cfg_vo and main_role_cfg_vo.wuhun_id or 0
	shower_vo.wuhun_lv = main_role_cfg_vo and main_role_cfg_vo.wuhun_lv or 0
	shower_vo.beast_ids = main_role_cfg_vo and main_role_cfg_vo.beast_ids or 0

	self.main_role_vo_copy[index] = shower_vo
	return self.main_role_vo_copy[index]
end

function SkillPreview:ResetPos(character_index)
	local character_data = self.character_dic[character_index]
	if character_data then
		self:KillMoveTweener(character_index)
		local trans = character_data.obj:GetRoot().gameObject.transform
		trans.localScale = character_data.original_scale
		trans.localEulerAngles = character_data.original_euler
		trans.localPosition = character_data.original_pos
		return true
	else
		print_error('没有找到重置位置的人物, 人物下标：', character_index)
		return false
	end
end

function SkillPreview:PlaySkill(skill_id, is_move_pool)
	if not self.root_obj or IsNil(self.root_obj.gameObject) then
		return
	end

	local need_move_pool = is_move_pool and self.cur_skill_id ~= skill_id
	self.cur_skill_id = skill_id
	self:OnPlayStart(is_move_pool)
end

-- 移动到人物池的obj是否需要隐藏(龙珠技能循环播放用到)
function SkillPreview:SetNeedHide(_bool)
	self.is_need_hide = _bool
end

function SkillPreview:IsPlaying()
	return self.is_playing
end


function SkillPreview:SetPreviewLoadCb( cb )
	if not self.previewCb then
		self.previewCb = cb
	end
end

function SkillPreview:SetPreviewPlayEndCb( cb )
	if not self.play_end_callback then
		self.play_end_callback = cb
	end
end

function SkillPreview:GetPreviewIsLoaded()
	return self.is_loaded
end

-- 全屏的技能界面 需要重新设置主摄像机
function SkillPreview:TrySetCamera()
	self.urpcamera_component = self.node_list.rt_camera:GetComponent(typeof(URPCamera)) or nil
	if not IsNil(self.urpcamera_component) then
		Scene.Instance:CtrlFollowCameraEnabled(false)

		-- local snap_shot_bg = GameObject.Find("GameRoot/UILayer/SnapShotBackground")
		-- if not IsNil(snap_shot_bg) then
		-- 	snap_shot_bg:SetActive(false)
		-- end

		self.urpcamera_component:ChangeCameraToBase()
		-- NonsupportScreenShot = true
		-- NonsupportChangeCamera = true
	end
end

-- 恢复主摄像机
function SkillPreview:TryResetCamera()
	-- NonsupportScreenShot = false
	-- NonsupportChangeCamera = false
	-- local snap_shot_bg = GameObject.Find("GameRoot/UILayer/SnapShotBackground")
	-- if not IsNil(snap_shot_bg) then
	-- 	snap_shot_bg:SetActive(true)
	-- end
end







--------------------------------------------------------特殊技能展示区域-----------------------------
-- 展示特殊技能
function SkillPreview:CheckIsSpecialSkillShow(skill_id, character_data)
	if skill_id == nil or character_data == nil or character_data.obj == nil then
		return
	end

	-- 是武魂技能要模拟四段普攻
	local is_wuhun_skill = SkillWGData.Instance:GetIsWuHunSkill(skill_id)
	if is_wuhun_skill then
		self:CleanWHSimulateGeneralAttackCDTimer()
		self:CleanWHModelRemoveCDTimer()
		character_data.obj:BuildWuHunInstance(skill_id)

        local wuhun_obj = character_data.obj:GetWuHun()
        if wuhun_obj then
            wuhun_obj:DoAttackForNoTarget(skill_id, 0)
        end

		-- 播放四段普攻
		self.wh_simulate_general_attack_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:TryDoGeneralAttack(character_data)
		end, 3)
	end
end

-- 普攻展示
function SkillPreview:TryDoGeneralAttack(character_data)
	if character_data == nil or character_data.obj == nil then
		return
	end

	self.general_attack_list = CommonSkillShowData.GetRoleNormalSkillList()
	self.general_attack_index = 1

	self:SimulateGeneralAttackOpera(character_data)
end

-- 模拟四段普攻
function SkillPreview:SimulateGeneralAttackOpera(character_data)
	if character_data == nil or character_data.obj == nil then
		return
	end

	self:ClearDelaySimulateGeneralAttackOpera()
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local data = self.general_attack_list[self.general_attack_index] or {}

	local skill_id = data.skill_id
	if not skill_id then
		self:SimulateGeneralAttackOperaComplete(character_data)
		return
	end

	self.general_attack_index = self.general_attack_index + 1
	self:SimulateSingleGeneralAttackOpera(character_data, skill_id, self.general_attack_index)
	local do_next_action_time = self:GetRoleSkillTime(character_data, skill_id, self.general_attack_index)

	if do_next_action_time > 0 then
		self.general_attack_timer = GlobalTimerQuest:AddTimesTimer(function ()
			self:SimulateGeneralAttackOpera(character_data)
		end, do_next_action_time, 1)
	end
end

-- 获取技能展示时间
function SkillPreview:GetRoleSkillTime(character_data, skill_id, attack_index)
	if character_data == nil or character_data.obj == nil then
		return 0
	end

	local anim_name = SkillWGData.GetSkillActionStr(SceneObjType.SkillShower, skill_id, attack_index)
	local action_config = character_data.obj:GetActionTimeRecord(anim_name)
	if action_config then
		local before_skill_cfg = SkillWGData.Instance:GetBeforeSkillCfg(skill_id)
		if before_skill_cfg and before_skill_cfg.skill_type == FRONT_SKILL_TYPE.CHARGE then
			-- 加个1s播冲刺 + 停留
			return action_config.time + 1
		end

		-- 加0.5秒缓冲时间
		return action_config.time + 0.5
	end

	return 0
end

-- 单个普攻
function SkillPreview:SimulateSingleGeneralAttackOpera(character_data, skill_id, attack_index)
	if character_data == nil or character_data.obj == nil then
		return
	end

	character_data.obj:SetAttackIndex(attack_index)
	character_data.obj:DoAttackForNoTarget(skill_id, 0)

	local wuhun_obj = character_data.obj:GetWuHun()
	if wuhun_obj then
		wuhun_obj:DoAttackForNoTarget(skill_id, 0)
	end
end

-- 模拟四段普攻 完成
function SkillPreview:SimulateGeneralAttackOperaComplete(character_data)
	if character_data == nil or character_data.obj == nil then
		return
	end

    self.wh_model_remove_timer = GlobalTimerQuest:AddDelayTimer(function ()
		local wuhun_obj = character_data.obj:GetWuHun()
        if wuhun_obj then
            wuhun_obj:RemoveWuHunInstance()
        end
	end, 1)
end

-- 延迟播放普攻
function SkillPreview:CleanWHSimulateGeneralAttackCDTimer()
    if self.wh_simulate_general_attack_timer then
        GlobalTimerQuest:CancelQuest(self.wh_simulate_general_attack_timer)
        self.wh_simulate_general_attack_timer = nil
    end
end

-- 延迟移除武魂
function SkillPreview:CleanWHModelRemoveCDTimer()
    if self.wh_model_remove_timer then
        GlobalTimerQuest:CancelQuest(self.wh_model_remove_timer)
        self.wh_model_remove_timer = nil
    end
end

function SkillPreview:ClearDelaySimulateGeneralAttackOpera()
    if self.general_attack_timer then
        GlobalTimerQuest:CancelQuest(self.general_attack_timer)
        self.general_attack_timer = nil
    end
end