TipOpenFunctionFlyView = TipOpenFunctionFlyView or BaseClass(SafeBaseView)

--主界面右上角图标大小与间距
local TopRightBtnWidth = 72
local TopRightBtnHeight = 64

--主界面右下角图标大小与间距
local BottomRightBtnWidth = 94
local BottomRightBtnHeight = 94

local SpecialBtn = {
	BtnWorldBossView = true,
	BtnWorldServerView = true,
}

local STATIC_TIME = 2							--静止时间
local MOVE_TIME = 1.5							--移动时间

function TipOpenFunctionFlyView:__init()
	self:SetMaskBg(false, true, false, BindTool.Bind(self.BlockClick, self))
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "OpenFunctionFlyTips")
	self.view_layer = UiLayer.Pop
	self.is_fly_ani = false						--是否在飞行中
	self.play_audio = true
	self.open_tween = nil
	self.close_tween = nil
	self.can_do_fade = false
	self.view_cache_time = 0
end

function TipOpenFunctionFlyView:ReleaseCallBack()
	-- 清理变量和对象
	self.target_icon = nil
	self.target_rect = nil
	self.target_obj = nil
	self.move_node = nil
	self.new_obj = nil

	self:StopCountDown()
	self:StopTween()
	self:UnListenEvent()
	self:CancelTween()
end

function TipOpenFunctionFlyView:UnListenEvent()
	if self.player_button_event then
		GlobalEventSystem:UnBind(self.player_button_event)
		self.player_button_event = nil
	end

	if self.top_right_button_event then
		GlobalEventSystem:UnBind(self.top_right_button_event)
		self.top_right_button_event = nil
	end
end

function TipOpenFunctionFlyView:ShowIndexCallBack()
	if self.mask_bg then
		self.mask_bg:SetActive(true)
		self.hide_mask = false
	end

	local function solar_eclipse_end()
		--显示背景
		UITween.DoViewDuangOpenTween(self.node_list.Frame, self.node_list.Icon)
		if not self:HasTargetObj() then
			self:Close()
			return
		end
	
		if self.new_obj then
			ResMgr:Destroy(self.new_obj.gameObject)
			self.new_obj = nil
		end
	
		self.new_obj = U3DObject(ResMgr:Instantiate(self.target_obj.gameObject), nil, self)
		self.new_obj:SetActive(true)
		self.new_obj.transform.parent = self.node_list.Icon.transform
		self.new_obj.rect.anchorMin = u3dpool.vec2(0.5, 0.5)
		self.new_obj.rect.anchorMax = u3dpool.vec2(0.5, 0.5)
		self.new_obj.transform.localPosition = u3dpool.vec3(0, 0, 0)
		self.new_obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
		self.new_obj.transform.localScale = u3dpool.vec3(1, 1, 1)
	
		local canvas_group = self.new_obj:GetComponent(typeof(UnityEngine.CanvasGroup))
		if canvas_group then
			canvas_group.alpha = 1
		end
	
		self.is_click = false
		self.is_auto_task = TaskGuide.Instance.can_auto_all_task
		TaskGuide.Instance:CanAutoAllTask(false)			--停止接受任务
	
		self.btn_name, self.btn_pos = MainuiWGData.Instance:GetMainUIButtonNameAndPos(self.cfg)
		self.btn_pos = self.btn_pos or FunOpenIconFlyPos.Top
		if self.btn_pos == FunOpenIconFlyPos.Top then
			MainuiWGCtrl.Instance:SetMenuIconIsOn(false)
			MainuiWGCtrl.Instance:SetShrinkButtonIsOn(true)
		else
			MainuiWGCtrl.Instance:SetMenuIconIsOn(true)
		end
		
		if self.target_rect then
			self.btn_width = self.target_rect.sizeDelta.x
			self.btn_height = self.target_rect.sizeDelta.y
		else
			if self.btn_pos == FunOpenIconFlyPos.Top then
				self.btn_width = TopRightBtnWidth
				self.btn_height = TopRightBtnHeight
			else
				self.btn_width = BottomRightBtnWidth
				self.btn_height = BottomRightBtnHeight
			end
		end
	
		if self.cfg and self.cfg.zjm_fp_btn_hide == 1 then
			self.target_obj:SetActive(true)
		end
		
		self:InitIcon()
		self:SetTxt()
		self.is_special_btn = SpecialBtn[self.btn_name]
	
		local target_name = self.target_obj and self.target_obj.gameObject.name
		if self.btn_pos == FunOpenIconFlyPos.Top and MainuiWGCtrl.Instance.view:GetShrinkButtonIsOn() == false
		and not self.is_special_btn then
			MainuiWGCtrl.Instance.view:PlayBtnGroupAniCallBack(function(name)
				if target_name == name then
					self:ChangeTargetAlpha(0)
					self:ChangeTargetSize()
					MainuiWGCtrl.Instance.view:PlayBtnGroupAniCallBack()
				end
			end)
			MainuiWGCtrl.Instance.view:SetShrinkButtonIsOn(true)
		else
			self:ChangeTargetSize()
		end
	
		self:ChangeTargetAlpha(0)
		self:StartCountDown()
	end

	solar_eclipse_end()
	-- 策划说不要这个动画了有问题请直接问：谢子鹏
	-- self:DoSolarEclipseTween(solar_eclipse_end)
end

function TipOpenFunctionFlyView:DoSolarEclipseTween(callback)
	for i = 1, 8 do
		self.node_list["circle" .. i].transform.localScale = Vector3(0, 0, 0)
	end
	self.node_list.effect_root:CustomSetActive(false)
	self.node_list.Frame:CustomSetActive(false)
	self.node_list.Icon:CustomSetActive(false)
	self.node_list.circle.canvas_group.alpha = 1

	self:CancelTween()
	local show_tweener = DG.Tweening.DOTween.Sequence()
	for i = 1, 4 do
		show_tweener:Append(self.node_list["circle" .. i].rect:DOScale(Vector3(1, 1, 1), 0.1))
	end

	show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	show_tweener:OnComplete(function()
		self.node_list.effect_root:CustomSetActive(true)
		self.node_list.Frame:CustomSetActive(true)
		self.node_list.Icon:CustomSetActive(true)
		for i = 5, 8 do
			self.node_list["circle" .. i].rect:DOScale(Vector3(1, 1, 1), 0.1):SetDelay((i - 5) * 0.1)
		end

		callback()
	end)

	self.show_tweener = show_tweener
end

function TipOpenFunctionFlyView:CancelTween()
    if self.show_tweener then
        self.show_tweener:Kill()
        self.show_tweener = nil
    end
end

function TipOpenFunctionFlyView:CloseCallBack()
	self:ChangeTargetAlpha(1)
	self:RestTargetSize()

	self.target_icon = nil
	self.target_rect = nil
	self.target_obj = nil
	self.is_special_btn = false
	self:StopCountDown()
	self:StopTween()
	self:UnListenEvent()

	if self.is_auto_task then
		if Scene.Instance:GetSceneType() == SceneType.Common then
			GuajiWGCtrl.Instance:StopGuaji()
		end
		
		TaskGuide.Instance:CanAutoAllTask(true)		--继续自动做任务
	end

	local is_wait_guide = FunctionGuide.Instance:GetIsWaitGuide()
	if not is_wait_guide then
		GlobalEventSystem:Fire(MainUIEventType.PORTRAIT_TOGGLE_CHANGE, false)
		GlobalEventSystem:Fire(MainUIEventType.SHOW_OR_HIDE_SHRINK_BUTTON, false)
		-- TaskGuide.Instance:CanAutoAllTask(false)
	end
	TipWGCtrl.Instance:FlyEndEvent(self.cfg)

	self.cfg = nil
	GlobalTimerQuest:AddDelayTimer(function ()
		TipWGCtrl.Instance:ShowOpenFunFlyView()
	end, 0)
end

function TipOpenFunctionFlyView:SetData(cfg)
	self.cfg = cfg
end

function TipOpenFunctionFlyView:SetTargetObj(target_obj, cfg)
	self.target_obj = target_obj
	if not self:HasTargetObj() then
		return
	end
	
	self.target_rect = self.target_obj.rect or self.target_obj:GetComponent(typeof(UnityEngine.RectTransform))
	local icon_obj = self.target_obj.transform:Find("Icon")
	if not IsNil(icon_obj) then
		self.target_icon = icon_obj.gameObject
	else
		print_error("【---功能开启目标按钮有问题----】", cfg)
	end
end


function TipOpenFunctionFlyView:SetTxt()
	if self.cfg == nil then
		return
	end

	self.node_list["desc_root"]:CustomSetActive(self.cfg.show_name ~= nil and self.cfg.show_name ~= "")
	self.node_list["Text"].text.text = self.cfg.desc_txt or ""
	self.node_list["Icon_Text"].text.text = self.cfg.show_name
end

--初始化飞行图标
function TipOpenFunctionFlyView:InitIcon()
	--初始化坐标
	-- self.node_list["Icon"].rect.anchoredPosition = Vector2(0, 0)

	--初始化图片
	-- local icon = self.cfg.icon
	self.move_node = self.new_obj
	if self.cfg.open_type == FunOpenType.FlyTabOpen and self.cfg.show_name ~= "" then
		local image_obj = self.new_obj.transform:Find("Icon")
		if image_obj and self.cfg.icon ~= "" then
			image_obj = U3DObject(image_obj.gameObject, nil, self)
			local final_icon = self.cfg.icon

			if self.cfg.zjm_open_btn_name ~= nil and self.cfg.zjm_open_btn_name ~= "" then
				final_icon = self.cfg.zjm_open_btn_name
			end

			local bundle, asset = ResPath.GetMainUIIcon(final_icon)
			image_obj.image:LoadSpriteAsync(bundle, asset, function ()
				image_obj.image:SetNativeSize()
			end)
			local now_color = image_obj.image.color
			local color_value = Color.New(now_color.r, now_color.g, now_color.b, 1)
			image_obj.image.color = color_value

			image_obj:SetLocalPosition(0, -30, 0)

			if self.cfg.fly_pos and self.cfg.fly_pos ~= "" then
				local pos_list = string.split(self.cfg.fly_pos, "|")
				local pos_x = tonumber(pos_list[1]) or 0
				local pos_y = tonumber(pos_list[2]) or 0
				local pos_z = tonumber(pos_list[3]) or 0
	
				image_obj:SetLocalPosition(pos_x, pos_y, pos_z)
			end

			-- a2主界面boss按钮现在启用了spine隐藏了图标
			if self.cfg.zjm_zp_btn_name == "BtnWorldBossView" or self.cfg.zjm_zp_btn_name == "BtnWorldServerView" or self.cfg.zjm_zp_btn_name == "BtnFuBen" then
				image_obj:CustomSetActive(true)
				image_obj:SetLocalPosition()
			end
		end

		local fun_name_obj = self.new_obj.transform:Find("fun_name")
		if fun_name_obj then
			fun_name_obj.gameObject:SetActive(false)
		end

		local bg_lock_obj = self.new_obj.transform:Find("bg_lock")
		if bg_lock_obj then
			bg_lock_obj.gameObject:SetActive(false)
		end

		local lock_name_obj = self.new_obj.transform:Find("lock_name")
		if lock_name_obj then
			lock_name_obj.gameObject:SetActive(false)
		end
		--[[
		local name_obj = image_obj and image_obj.transform:Find("name")
		if name_obj then
			name_obj = U3DObject(name_obj.gameObject, nil, self)
			local bundle, asset = ResPath.GetMainUIIcon(self.cfg.name_icon)
			name_obj.image:LoadSpriteAsync(bundle, asset, function ()
				name_obj.image:SetNativeSize()
			end)
		end
		]]
	end

	--	副屏功能开启 飞行按钮隐藏背景
	if self.btn_pos ~= FunOpenIconFlyPos.Top then
		local bg_obj = self.new_obj.transform:Find("bg")
		local lock_obj = self.new_obj.transform:Find("lock")
		if bg_obj then
			bg_obj.gameObject:SetActive(false)
		end

		if lock_obj then
			lock_obj.gameObject:SetActive(false)
		end
	end

	local red_point = self.new_obj.transform:Find("RedPoint")
	if red_point then
		red_point.gameObject:SetActive(false)
	end

	local text = self.new_obj.transform:Find("Text")
	if text then
		text.gameObject:SetActive(false)
	end

	--百炼召唤的时间显示隐藏
	local sc_call_time = self.new_obj.transform:Find("sc_call_time")
	if sc_call_time then
		sc_call_time.gameObject:SetActive(false)
	end
end

--改变目标的大小
function TipOpenFunctionFlyView:ChangeTargetSize()
	if not self:HasTargetObj() or self.is_special_btn or self.cfg.open_type == FunOpenType.FlyTabOpen then
		return
	end

	if self.btn_pos == FunOpenIconFlyPos.Top then
		--这里是改变主界面上方功能图标的大小(这里的x代表间距)
		self.target_obj.rect.sizeDelta = Vector2(self.btn_width, self.btn_height)
	end
end

--还原目标的大小
function TipOpenFunctionFlyView:RestTargetSize()
	if not self:HasTargetObj() or self.is_special_btn or self.cfg.open_type == FunOpenType.FlyTabOpen then
		return
	end

	if self.btn_pos == FunOpenIconFlyPos.Top then
		self.target_obj.rect.sizeDelta = u3dpool.vec2(self.btn_width, self.btn_height)
	end
end

--改变目标的透明度
function TipOpenFunctionFlyView:ChangeTargetAlpha(alpha)
	if not self:HasTargetObj() or self.is_special_btn or self.cfg.open_type == FunOpenType.FlyTabOpen then
		return
	end

	local canvas_group
	if self.btn_pos == FunOpenIconFlyPos.Top then
		canvas_group = self.target_obj:GetComponent(typeof(UnityEngine.CanvasGroup))
	else
		-- 副屏只改变图标的显隐
		if self.target_icon then
			self.target_icon:SetActive(alpha == 1)
		end
	end

	if canvas_group ~= nil then
		canvas_group.alpha = alpha
	end
end

function TipOpenFunctionFlyView:HasTargetObj()
	return self.target_obj ~= nil and not IsNil(self.target_obj.gameObject)
end

function TipOpenFunctionFlyView:GetTargetObj()
	return self.target_obj
end

function TipOpenFunctionFlyView:StopTween()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
end

--开始移动到目标
function TipOpenFunctionFlyView:MoveToTarget()
	if not self:HasTargetObj() or nil == self.target_rect then
		self:Close()
		return
	end
	-- --获取指引按钮的屏幕坐标
	-- local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, target_rect_postion)

	-- --转换屏幕坐标为本地坐标
	-- local rect = self.move_node.rect
	-- local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))

	--计算偏移
	local offset_x, offset_y = 0, 0
	if self.btn_pos == FunOpenIconFlyPos.Top then
		offset_x = 0
	elseif self.cfg.open_type ~= FunOpenType.FlyTabOpen then
		offset_x = 0
	end

	--加上偏移值
	local position = self.target_rect.position
	position.x = position.x + offset_x

	--获取目标原始大小
	local target_normal_width = self.btn_width
	local target_normal_height = self.btn_height

	local move_tween = self.move_node.rect:DOMove(position, MOVE_TIME)
	move_tween:SetEase(DG.Tweening.Ease.OutCubic)
	local size_tween = self.target_rect:DOSizeDelta(Vector2(target_normal_width , target_normal_height), 0.7)
	size_tween:SetEase(DG.Tweening.Ease.Linear)
	local size_tween_1 = self.move_node.rect:DOScale(Vector3(0.85, 0.85, 0.85), 0.2)
	size_tween_1:SetEase(DG.Tweening.Ease.Linear)
	local size_tween_2 = self.move_node.rect:DOScale(Vector3(1, 1, 1), 0.2)
	size_tween_2:SetEase(DG.Tweening.Ease.Linear)

	--生成一个dotween队列
	self:StopTween()
	self.sequence = DG.Tweening.DOTween.Sequence()
	self.sequence:Append(move_tween)
	self.sequence:Insert(0.5, size_tween)
	self.sequence:Append(size_tween_1)
	self.sequence:Append(size_tween_2)
	self.sequence:SetUpdate(true)
	self.sequence:OnComplete(function()
		self.is_fly_ani = false
		MainuiWGCtrl.Instance:SetMenuIconIsOn(false)
		self:ChangeTargetAlpha(1)
		self:OpenViewOnFlyEnd()
		self:Close()
	end)
end

function TipOpenFunctionFlyView:StopCountDown()
	if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

--开始倒计时，结束后自动开始移动图标
function TipOpenFunctionFlyView:StartCountDown()
	self:StopCountDown()

	local function time_func(elapse_time, total_time)
		if elapse_time >= total_time then
			self:BlockClick()
			self:StopCountDown()
			return
		end
	end

	self.static_count_down = CountDown.Instance:AddCountDown(STATIC_TIME, 1, time_func)
end

function TipOpenFunctionFlyView:DoMoveToTargetTween()
	UITween.DoViewDuangCloseTween(self.node_list.circle, nil, function()
	end)

	self.node_list["desc_root"]:CustomSetActive(false)
	UITween.DoViewDuangCloseTween(self.node_list.Frame, nil, function()
		self:StartMove()
	end)
end

--开始移动图标
function TipOpenFunctionFlyView:StartMove()
	self.is_fly_ani = true
	self:MoveToTarget()
end

--强制开始移动（只有主界面两个收缩动画都完成了才处理）
function TipOpenFunctionFlyView:BlockClick()
	if self.is_click then
		return
	end

	if self.mask_bg then
		self.mask_bg:SetActive(false)
		self.hide_mask = true
	end

	local menu_button_ani_state = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
	local shrink_button_ani_state = MainuiWGCtrl.Instance.view:GetShrinkButtonIsOn()
	if menu_button_ani_state and self.btn_pos == FunOpenIconFlyPos.Bottom or
		shrink_button_ani_state and self.btn_pos == FunOpenIconFlyPos.Top then
		self:StopCountDown()
		self:DoMoveToTargetTween()
	else
		self:Close()
	end

	self.is_click = true
end

-- 开启飞行结束打开界面
function TipOpenFunctionFlyView:OpenViewOnFlyEnd()
	if not self.cfg then
		return
	end

	if self.cfg.name == OPEN_FUN_NAME.yuanshen then
		FunOpen.Instance:OpenViewNameByCfg("DujieView#0#op=0", GameEnum.YUAN_SHEN_ACTIVATE_ITEM)
	end
end