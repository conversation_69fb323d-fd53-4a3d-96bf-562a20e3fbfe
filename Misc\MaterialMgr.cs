﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MaterialMgr : Nirvana.Singleton<MaterialMgr>
{
    struct SyncMaterialItem
    {
        public Material material;
        public string[] excludeKeywords;
        public string[] excludeProperties;
    }

    private bool isNeedSyncMaterial = false;
    private bool isLimitMaterialClone = false;
    private bool isArtEdit = false;
    private Dictionary<Material, HashSet<SyncMaterialItem>> syncMaterialsDic = new Dictionary<Material, HashSet<SyncMaterialItem>>();
    private Dictionary<Renderer, Material[]> sharedMaterialsDic = new Dictionary<Renderer, Material[]>();
    private Dictionary<Renderer, Material> sharedMaterialDic = new Dictionary<Renderer, Material>();

    public void OnGameStartup()
    {
#if UNITY_EDITOR
        if (UnityEngine.PlayerPrefs.HasKey("a3_develop_mode"))
        {
            //isNeedSyncMaterial = UnityEngine.PlayerPrefs.GetInt("a3_develop_mode") == 2;
            isLimitMaterialClone = UnityEngine.PlayerPrefs.GetInt("a3_develop_mode") == 2;
            isArtEdit = UnityEngine.PlayerPrefs.GetInt("a3_develop_mode") == 2;
        }
#endif
    }

    public bool IsArtEdit()
    {
        return isArtEdit;
    }

    public bool IsNeedSyncMaterial()
    {
        return isNeedSyncMaterial;
    }

    public void OnGameStop()
    {
        syncMaterialsDic.Clear();
        sharedMaterialsDic.Clear();
        sharedMaterialDic.Clear();
    }

    public void Update()
    {
        this.UpdateDelInvalidRender();

#if UNITY_EDITOR
        if (isNeedSyncMaterial)
        {
            this.UpdateSyncMaterials();
        }
#endif
    }

    private void UpdateDelInvalidRender()
    {
        var materialsIter = sharedMaterialsDic.GetEnumerator();
        while (materialsIter.MoveNext())
        {
            if (null == materialsIter.Current.Key)
            {
                sharedMaterialsDic.Remove(materialsIter.Current.Key);
                break;
            }
        }

        var materialIter = sharedMaterialDic.GetEnumerator();
        while (materialIter.MoveNext())
        {
            if (null == materialIter.Current.Key)
            {
                sharedMaterialDic.Remove(materialIter.Current.Key);
                break;
            }
        }
    }

    private void UpdateSyncMaterials()
    {
        foreach (var kv in syncMaterialsDic)
        {
            Material oldMat = kv.Key;
            if (null == oldMat)
            {
                continue;
            }

            foreach (var matItem in kv.Value)
            {
                this.SyncMaterialProperties(oldMat, matItem.material, matItem.excludeProperties, matItem.excludeKeywords);
            }
        }
    }

    private void SyncMaterialProperties(Material oldMat, Material newMat, string[] excludeProperties, string[] excludeKeywords)
    {
        int oldRnderQueue = newMat.renderQueue;

        // 过滤出不拷贝的属性
        Dictionary<string, Vector4> keepVector4ProprtyDic = new Dictionary<string, Vector4>();
        foreach (var propertyName in excludeProperties)
        {
            if (newMat.HasProperty(propertyName))
            {
                var value = newMat.GetVector(propertyName);
                if (null != value)
                {
                    keepVector4ProprtyDic.Add(propertyName, value);
                }
            }
        }

        // 过滤出需要拷贝的keyword
        HashSet<string> keepKeywordSet = new HashSet<string>(oldMat.shaderKeywords);
        for (int i = 0; i < excludeKeywords.Length; i++)
        {
            if (newMat.IsKeywordEnabled(excludeKeywords[i]))
            {
                keepKeywordSet.Add(excludeKeywords[i]);
            }
            else
            {
                keepKeywordSet.Remove(excludeKeywords[i]);
            }
        }

        // 先同步所有属性（包括keyword)
        newMat.CopyPropertiesFromMaterial(oldMat);

        // 恢复过滤的属性
        foreach (var kv in keepVector4ProprtyDic)
        {
            newMat.SetVector(kv.Key, kv.Value);
        }
        newMat.renderQueue = oldRnderQueue;

        // 恢复过滤的keyword
        newMat.shaderKeywords = new string[] { };
        foreach (var item in keepKeywordSet)
        {
            newMat.EnableKeyword(item);
        }
    }

    public void RegisterSyncMaterials(Renderer renderer, string[] excludeKeywords, string[] excludeProperties)
    {
        if (!isNeedSyncMaterial || null == renderer || !sharedMaterialsDic.ContainsKey(renderer))
        {
            return;
        }

        Material[] oldMats = sharedMaterialsDic[renderer];
        Material[] newMats = renderer.materials;

        if (oldMats.Length != newMats.Length)
        {
            Debug.LogError("[MaterialMgr] RegisterSyncMaterials error");
            return;
        }

        for (int i = 0; i < oldMats.Length; i++)
        {
            Material oldMat = oldMats[i];
            Material newMat = newMats[i];
            if (null == oldMat || null == newMat || oldMat == newMat)
            {
                continue;
            }

            HashSet<SyncMaterialItem> set;
            if (!syncMaterialsDic.TryGetValue(oldMats[i], out set))
            {
                set = new HashSet<SyncMaterialItem>();
                syncMaterialsDic.Add(oldMats[i], set);
            }

            bool isExists = false;
            foreach (var item in set)
            {
                if (item.material == newMat)
                {
                    isExists = true;
                    break;
                }
            }

            if (!isExists)
            {
                SyncMaterialItem item = new SyncMaterialItem();
                item.material = newMat;
                item.excludeKeywords = excludeKeywords;
                item.excludeProperties = excludeProperties;
                set.Add(item);
            }
        }
    }

    public void UnRegisterSyncMaterials(Renderer renderer)
    {
        if (!isNeedSyncMaterial || null == renderer || !sharedMaterialsDic.ContainsKey(renderer))
        {
            return;
        }

        Material[] oldMats = sharedMaterialsDic[renderer];
        Material[] newMats = renderer.materials;

        if (oldMats.Length != newMats.Length)
        {
            Debug.LogError("[MaterialMgr] UnRegisterSyncMaterials error");
            return;
        }

        for (int i = 0; i < oldMats.Length; i++)
        {
            if (oldMats[i] &&syncMaterialsDic.ContainsKey(oldMats[i]))
            {
                HashSet<SyncMaterialItem> newMaterialSet = syncMaterialsDic[oldMats[i]];
                foreach (var item in newMaterialSet)
                {
                    if (newMats[i] && item.material == newMats[i])
                    {
                        newMaterialSet.Remove(item);
                        break;
                    }
                }
            }
        }
    }

    // 获得克隆材质球，克隆前缓存起原始材质球。unity的materials会确保只有一次克隆
    public Material[] GetClonedMaterials(Renderer renderer)
    {
        if (null == renderer) return new Material[] { };

        // 美术模式下限制克隆材质球，方便编辑
        if (isLimitMaterialClone)
        {
            return renderer.sharedMaterials;
        }

        if (!sharedMaterialsDic.ContainsKey(renderer))
        {
            // 要确保shaderMaterials完全是原始的需考虑在此之前已经通过sharedMaterial克隆过
            Material[] sharedMaterials = renderer.sharedMaterials;
            if (sharedMaterialDic.ContainsKey(renderer) && sharedMaterials.Length > 0)
            {
                sharedMaterials[0] = sharedMaterialDic[renderer];
            }

            sharedMaterialsDic.Add(renderer, sharedMaterials);
        }

        return renderer.materials;
    }

    // 获得共享材质球，如果之前没有克隆过，则直接通过sharedMaterials拿
    public Material[] GetSharedMaterials(Renderer renderer)
    {
        if (null == renderer) return new Material[] { };

        if (sharedMaterialsDic.ContainsKey(renderer))
        {
            return sharedMaterialsDic[renderer];
        }

        return renderer.sharedMaterials;
    }

    // 恢复到共享材质球，只有之前有克隆才进行恢复
    public void ResumeSharedMaterials(Renderer renderer)
    {
        if (null == renderer) return;
        if (sharedMaterialsDic.ContainsKey(renderer))
        {
            this.InternalSetSharedMaterials(renderer, sharedMaterialsDic[renderer]);
        }

       if (sharedMaterialDic.ContainsKey(renderer))
        {
            this.InternalSetSharedMaterial(renderer, sharedMaterialDic[renderer]);
        }
    }

    private Material[] tempMaterials = new Material[1];
    public void SetSharedMaterial(Renderer renderer, Material[] material)
    {
        if (null == material)
            return;
        tempMaterials = material;
        this.InternalSetSharedMaterials(renderer, tempMaterials);
        this.InternalSetSharedMaterial(renderer, material[0]);
    }

    public void SetSharedMaterials(Renderer renderer, Material[] sharedMaterials)
    {
        if (sharedMaterials.Length <= 0)
            return;

        this.InternalSetSharedMaterials(renderer, sharedMaterials);
        this.InternalSetSharedMaterial(renderer, sharedMaterials[0]);
    }

    // 恢复材质球的keyword，而不用删除克隆出来的材质球
    public void ResumeMaterialsKeywordsAndRenderQueue(Renderer renderer)
    {
        if (null == renderer) return;

        if (sharedMaterialsDic.ContainsKey(renderer))
        {
            var sharedMaterials = sharedMaterialsDic[renderer];
            for (int i = 0; i < renderer.materials.Length; i++)
            {
                if (i < sharedMaterials.Length && null != renderer.materials[i] && sharedMaterials[i])
                {
#if UNITY_EDITOR || UNITY_STANDALONE
                    var name1 = renderer.materials[i].name;
                    var name2 = sharedMaterials[i].name;
                    if (name1 != string.Format("{0} (Instance)", name2))
                    {
                        Debug.LogErrorFormat("[MaterialMgr] Big Bug!!!, ResumeMaterialsKeywordsAndRenderQueue error, {0} {1}", name1, name2);
                    }
#endif
                    renderer.materials[i].renderQueue = sharedMaterials[i].renderQueue;
                    renderer.materials[i].shaderKeywords = sharedMaterials[i].shaderKeywords;
                }
            }
        }
    }

    // 获得克隆材质球，克隆前缓存起原始材质球。unity的materials会确保只有一次克隆
    public Material GetClonedMaterial(Renderer renderer)
    {
        if (null == renderer) return null;

        // 美术模式下限制克隆材质球，方便编辑
        if (isLimitMaterialClone)
        {
            return renderer.sharedMaterial;
        }

        if (!sharedMaterialDic.ContainsKey(renderer))
        {
            // 要确保sharedMateria完全是原始的需考虑在此之前已经通过shaderMaterials克隆过
            Material sharedMaterial = renderer.sharedMaterial;
            if (sharedMaterialsDic.ContainsKey(renderer) && sharedMaterialsDic[renderer].Length > 0)
            {
                sharedMaterial = sharedMaterialsDic[renderer][0];
            }

            sharedMaterialDic.Add(renderer, sharedMaterial);
        }

        return renderer.material;
    }

    // 获得共享材质球，如果之前没有克隆过，则直接通过sharedMaterials拿
    public Material GetSharedMaterial(Renderer renderer)
    {
        if (null == renderer) return null;

        if (sharedMaterialDic.ContainsKey(renderer))
        {
            return sharedMaterialDic[renderer];
        }

        return renderer.sharedMaterial;
    }

    // 设置共享材质球，设置之前需清理之前克隆出来的
    private void InternalSetSharedMaterials(Renderer renderer, Material[] sharedMaterials)
    {
        if (null == renderer) return;

        if (sharedMaterialsDic.ContainsKey(renderer))
        {
            sharedMaterialsDic.Remove(renderer);

            var cloneMaterials = renderer.materials;
            for (int i = 0; i < cloneMaterials.Length; i++)
            {
                GameObject.Destroy(cloneMaterials[i]);
            }
        }

        renderer.sharedMaterials = sharedMaterials;
    }

    // 设置共享材质球，设置之前需清理之前克隆出来的
    private void InternalSetSharedMaterial(Renderer renderer, Material materials)
    {
        if (null == renderer) return;

        if (sharedMaterialDic.ContainsKey(renderer))
        {
            sharedMaterialDic.Remove(renderer);
            GameObject.Destroy(renderer.material);
        }

        renderer.sharedMaterial = materials;
    }
}
