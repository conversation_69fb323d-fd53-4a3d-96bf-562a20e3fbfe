--弃用
ArtifactAwakeView = ArtifactAwakeView or BaseClass(SafeBaseView)

function ArtifactAwakeView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self.is_safe_area_adapter = true

	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_bg_panel")
	self:AddViewResource(0, "uis/view/artifact_ui_prefab", "layout_artifact_awake")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function ArtifactAwakeView:__delete()

end

function ArtifactAwakeView:SetDataAndOpen(artifact_seq)
	self.artifact_seq = artifact_seq
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function ArtifactAwakeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Artifact.AwakeViewName

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_display"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if self.awake_attr_list == nil then
		self.awake_attr_list = {}
		local node_num = self.node_list["awake_attr_list"].transform.childCount
		for i = 1, node_num do
			self.awake_attr_list[i] = CommonAddAttrRender.New(self.node_list["awake_attr_list"]:FindObj("attr_" .. i))
			self.awake_attr_list[i]:SetAttrNameNeedSpace(true)
		end
	end

	XUI.AddClickEventListener(self.node_list["btn_awake"], BindTool.Bind(self.OnBtnAwake, self))
end

function ArtifactAwakeView:ReleaseCallBack()
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.awake_attr_list then
		for k, v in pairs(self.awake_attr_list) do
			v:DeleteMe()
		end
		self.awake_attr_list = nil
	end
end

function ArtifactAwakeView:CloseCallBack()
	self.artifact_seq = nil
	self.model_id = nil
	self.old_act_state = nil
	self.need_show_effect = nil
end

function ArtifactAwakeView:ShowIndexCallBack()

end

function ArtifactAwakeView:OnFlush(param_t, index)
	if not self.artifact_seq then
		return
	end

	for k, v in pairs(param_t) do
		if k == "all" then
			self.need_show_effect = false
			self:FlushShowModel()
			self:FlushMidPart()
			self:FlushRightPart()
		elseif k == "up_awake_level" then
			self.need_show_effect = true
			self:FlushMidPart()
			self:FlushRightPart()
		end
	end
end

function ArtifactAwakeView:FlushShowModel()
	if not self.artifact_seq then
		return
	end

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.artifact_seq)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.artifact_seq)
	local is_act = cur_artifact_data.level > 0
	if not self.model_id or self.model_id ~= artifact_cfg.model_id then
		self.node_list["model_display"]:SetActive(is_act)
		self.node_list["image_outline"]:SetActive(not is_act)
		if is_act then
			local bundle, asset = ResPath.GetShuangXiuShowUI(artifact_cfg.model_id)
			local display_data = {}
			display_data.bundle_name = bundle
			display_data.asset_name = asset
			display_data.render_type = OARenderType.Prefab
			self.model_display:SetData(display_data)
		else
			local bundle, asset = ResPath.GetRawImagesPNG(artifact_cfg.outline_img)
			self.node_list["image_outline"].raw_image:LoadSprite(bundle, asset, function()
				self.node_list["image_outline"].raw_image:SetNativeSize()
			end)
			local pos_x, pos_y = 0, 0
			if artifact_cfg.outline_pos and artifact_cfg.outline_pos ~= "" then
				local pos_list = string.split(artifact_cfg.outline_pos, "|")
				pos_x = tonumber(pos_list[1]) or pos_x
				pos_y = tonumber(pos_list[2]) or pos_y
			end
			RectTransform.SetAnchoredPositionXY(self.node_list["image_outline"].rect, pos_x, pos_y)
		end
		
		-- 背景图
		local bundle, asset = ResPath.GetRawImagesJPG(artifact_cfg.bg)
		if self.node_list.RawImage_bg then
			self.node_list["RawImage_bg"].raw_image:LoadSprite(bundle, asset, function()
				self.node_list["RawImage_bg"].raw_image:SetNativeSize()
			end)
		end

		self.model_id = artifact_cfg.model_id
	end
end

function ArtifactAwakeView:FlushMidPart()
	if not self.artifact_seq then
		return
	end

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.artifact_seq)
	local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.artifact_seq, cur_artifact_data.awake_level)
	local next_awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.artifact_seq, cur_artifact_data.awake_level + 1)
	if awake_cfg then
		self.node_list["txt_artifact_name"].text.text = next_awake_cfg and next_awake_cfg.awake_name or awake_cfg.awake_name
	end
	
	local star_res_list = GetTwenTyStarImgResByStar(cur_artifact_data.star_level)
	for i = 1, GameEnum.ITEM_MAX_STAR do
		self.node_list["star_" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
	end
end

function ArtifactAwakeView:FlushRightPart()
	if not self.artifact_seq then
		return
	end
	self:FlushAtrrInfo()

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.artifact_seq)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.artifact_seq)
	local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.artifact_seq, cur_artifact_data.awake_level)
	local next_awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.artifact_seq, cur_artifact_data.awake_level + 1)
	local is_max_awake = next_awake_cfg == nil

	if artifact_cfg and cur_artifact_data and awake_cfg then
		self.node_list["btn_awake"]:SetActive(not is_max_awake)
		if not is_max_awake then
			local str = cur_artifact_data.star_level .. "/" .. next_awake_cfg.need_star
			local awake_tips_str = string.format(Language.Artifact.AwakeTips, awake_cfg.awake_name, str)
			local can_awake = cur_artifact_data.star_level >= next_awake_cfg.need_star
			self.node_list["txt_awake_desc"].text.text = ToColorStr(awake_tips_str, can_awake and COLOR3B.GREEN or COLOR3B.RED)
			self.node_list["img_awake_remind"]:SetActive(can_awake)
		else
			self.node_list["txt_awake_desc"].text.text = Language.Artifact.MaxAwake
		end
	end
end

function ArtifactAwakeView:FlushAtrrInfo()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.artifact_seq)
	local attr_list = ArtifactWGData.Instance:GetArtifactAllAttrList(
		self.artifact_seq, 
		cur_artifact_data.level, 
		cur_artifact_data.star_level,
		cur_artifact_data.awake_level, 3)

	for k, v in pairs(self.awake_attr_list) do
		if self.need_show_effect then
			local data = v:GetData()
			if data and data.add_value and data.add_value > 0 then
				v:PlayAttrValueUpEffect()
			end
		end
		v:SetData(attr_list[k])
	end
	self.need_show_effect = false
end

function ArtifactAwakeView:OnBtnAwake()
	if not self.artifact_seq then
		return
	end

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.artifact_seq)
	if cur_artifact_data then
		local awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.artifact_seq, cur_artifact_data.awake_level)
		local next_awake_cfg = ArtifactWGData.Instance:GetArtifactAwakeCfg(self.artifact_seq, cur_artifact_data.awake_level + 1)
		local can_awake = next_awake_cfg ~= nil and cur_artifact_data.star_level >= next_awake_cfg.need_star
		if can_awake then
			ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.UP_AWAKE_LEVEL, self.artifact_seq)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Artifact.CanNotAwake)
		end
	end
end

-- 使用特效
function ArtifactAwakeView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({
		effect_type = effect_name,
		is_success = true,
		pos = Vector2(0, 0),
		parent_node = self.node_list["effect"]
	})
end
