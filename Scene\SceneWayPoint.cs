﻿using Nirvana;
using UnityEngine;
using System;

#if UNITY_EDITOR

[ExecuteInEditMode]
public sealed class SceneWayPoint : SceneObject
{
    [SerializeField]
    private int id;
    [SerializeField]
    private SceneWayPointData[] targetPoints;

    public int ID
    {
        get { return id; }
    }

    public SceneWayPointData[] TargetPoints
    {
        get { return targetPoints; }
    }

    private void Awake()
    {
        this.LoadPreview();
    }

    private void OnValidate()
    {
        if (Application.isPlaying)
        {
            return;
        }

        this.name = "SceneWayPoint" + this.id.ToString();
    }

    private void LoadPreview()
    {
        var preview = GameObject.CreatePrimitive(PrimitiveType.Cube);
        preview.transform.localPosition = new Vector3(0, 0.5f, 0);
        preview.hideFlags = HideFlags.HideAndDontSave;
        var previewObj = this.GetOrAddComponent<PreviewObject>();
        previewObj.SetPreview(preview);
    }

    private void OnDrawGizmos()
    {
        if (null == targetPoints)
            return;

        foreach (var point in targetPoints)
        {
            if (null == point.sceneWayPoint)
                continue;

            if (point.doubleDirection)
                Gizmos.color = Color.green;
            else
                Gizmos.color = Color.yellow;

            Gizmos.DrawLine(transform.position + Vector3.up * 2f, point.sceneWayPoint.transform.position + Vector3.up * 2f);
        }
    }
}

[Serializable]
public struct SceneWayPointData
{
    public SceneWayPoint sceneWayPoint;
    [Tooltip("是否是双向连接")]
    public bool doubleDirection;
}

#endif
