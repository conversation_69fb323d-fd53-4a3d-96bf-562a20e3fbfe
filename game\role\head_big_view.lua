--头像查看大图
HeadBigView = HeadBigView or BaseClass(SafeBaseView)

function HeadBigView:__init()
	self:SetMaskBg(true, true)
    self.open_tween = nil
    self.view_layer = UiLayer.Pop
    self.mask_alpha = 210/255
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_head_big_view")
end

function HeadBigView:__delete()

end

function HeadBigView:ReleaseCallBack()
	
end

function HeadBigView:LoadCallBack()
	
end

function HeadBigView:SetDataAndOpen(data)
    self.data = data
    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function HeadBigView:ShowIndexCallBack()
    
end

function HeadBigView:OnFlush()
    if self.data  then
        if self.data.path then
            self.node_list.big_img:SetActive(false)
            self.node_list.big_raw_img.raw_image:LoadURLSprite(self.data.path,
            function()
                self.node_list.big_raw_img.raw_image:SetNativeSize()
                self.node_list.big_raw_img:SetActive(true)
            end)
        else
            local index = AvatarManager.Instance:GetAvatarKey(self.data.role_id, true)
            --local bundle, asset = RoleWGData.Instance:GetHeadIconResByIdnex(index, self.data.sex, self.data.prof, true)
            if index <= GameEnum.CUSTOM_HEAD_ICON then --系统头像,策划说不用点击放大
                -- self.node_list.big_raw_img:SetActive(false)
                -- self.node_list.big_img.image:LoadSprite(bundle, asset,
                -- function()
                --     self.node_list.big_img.image:SetNativeSize()
                --     self.node_list.big_img:SetActive(true)
                -- end)
            else --自定义头像
                local call_back = function (path)
                    self.node_list.big_img:SetActive(false)
                    self.node_list.big_raw_img.raw_image:LoadURLSprite(path,
                    function()
                        self.node_list.big_raw_img.raw_image:SetNativeSize()
                        self.node_list.big_raw_img:SetActive(true)
                    end)
                end
                AvatarManager.Instance:GetAvatar(self.data.role_id, true, call_back)
            end
        end
    end
end

function HeadBigView:SetPath(Path)
    self.data = {}
    self.data.path = Path
    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function HeadBigView:CloseCallBack()
    self.data = nil
end