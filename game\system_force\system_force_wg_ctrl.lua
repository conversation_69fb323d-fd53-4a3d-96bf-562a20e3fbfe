require("game/system_force/system_force_wg_data")
require("game/system_force/system_force_longshen_view")

SystemForceWGCtrl = SystemForceWGCtrl or BaseClass(BaseWGCtrl)

function SystemForceWGCtrl:__init()
    if nil ~= SystemForceWGCtrl.Instance then
        ErrorLog("[SystemForceWGCtrl]:Attempt to create singleton twice!")
    end
    SystemForceWGCtrl.Instance = self

	self.data = SystemForceWGData.New()
	self.view = SystemForceLongShenView.New(GuideModuleName.SystemForceLongShenView)

	self:RegisterProtocol(CSystemForceShowOperate)
	self:RegisterProtocol(SCSystemForceShowInfo, "OnSCSystemForceShowInfo")
	self:RegisterProtocol(SCSystemForceShowTaskInfo, "OnSCSystemForceShowTaskInfo")
	self:RegisterProtocol(SCSystemForceShowTaskUpdate, "OnSCSystemForceShowTaskUpdate")

	self.enter_open_flag = false
	self.prepare_open_seq = -1

	-- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	self.open_view_fun = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.CheckViewOpen, self))
end

function SystemForceWGCtrl:__delete()
    SystemForceWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.open_view_fun then
		GlobalEventSystem:UnBind(self.open_view_fun)
		self.open_view_fun = nil
	end
end

--请求操作
function SystemForceWGCtrl:SendCSSystemForceRequest(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSystemForceShowOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function SystemForceWGCtrl:OnSCSystemForceShowInfo(protocol)
	-- print_error("=======信息======", protocol.seq,protocol)
	local old_is_open = SystemForceWGData.Instance:GetForceshowFunIsOpen(protocol.seq)
	self.data:SetSystemForceInfo(protocol)
	local is_view_open = self.view:IsOpen()
	if self.view and is_view_open then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.SystemForceLongShen)
	FunOpen.Instance:OnSystemForceChange(protocol.seq)
	local is_open = SystemForceWGData.Instance:GetForceshowFunIsOpen(protocol.seq)

	if old_is_open ~= is_open and self.view.open_view and self.view and is_view_open then
		self.view:Close()
		self.prepare_open_seq = protocol.seq
		-- local cfg = SystemForceWGData.Instance:GetForceshowCfgBySeq(protocol.seq)
		-- FunOpen.Instance:OpenViewNameByCfg(cfg.open_view)
	end
	MainuiWGCtrl.Instance:FlushView(0, "system_force_icon")
end

function SystemForceWGCtrl:IsPrepareOpenSystemForceSeqView()
	return self.prepare_open_seq >= 0
end

function SystemForceWGCtrl:OpenSystemForceSeqView()
	if self.prepare_open_seq >= 0 then
		local cfg = SystemForceWGData.Instance:GetForceshowCfgBySeq(self.prepare_open_seq)
		if cfg then
			FunOpen.Instance:OpenViewNameByCfg(cfg.open_view)
		end
		self.prepare_open_seq = -1
	end
end

function SystemForceWGCtrl:OnSCSystemForceShowTaskInfo(protocol)
	-- print_error("======任务信息======", protocol.seq, protocol)
	self.data:SetSystemForceTaskInfo(protocol)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.SystemForceLongShen)
end

function SystemForceWGCtrl:OnSCSystemForceShowTaskUpdate(protocol)
	-- print_error("=======任务单个信息变化信息======", protocol.seq,protocol)
	self.data:SetSingleTaskInfo(protocol)

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.SystemForceLongShen)
end

-- 天数改变 补发一下请求 跨天奖励可领取，当天任务会过期
function SystemForceWGCtrl:OnDayChange()
	SystemForceWGCtrl.Instance:SendCSSystemForceRequest(SYSTEM_FORCESHOW_OPERATE_TYPE.ALL_INFO)
end

function SystemForceWGCtrl:CheckViewOpen()
	if not self.enter_open_flag then
		self.enter_open_flag = true

		if self.data:GetCheckFunOpen() then
			if self.view then
				--self.view:Open()
			end
		end

		if self.open_view_fun then
			GlobalEventSystem:UnBind(self.open_view_fun)
			self.open_view_fun = nil
		end
	end
end