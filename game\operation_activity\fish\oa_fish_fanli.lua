OAFishFanLiView = OAFishFanLiView or BaseClass(SafeBaseView)

function OAFishFanLiView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/operation_fish_prefab", "layout_oa_fish_fanli")
	self:SetMaskBg(true, true)
end

function OAFishFanLiView:ReleaseCallBack()
    if self.fanli_list then
        self.fanli_list:DeleteMe()
        self.fanli_list = nil
    end
end

function OAFishFanLiView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.XiuZhenRoad.ViewName
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(3.85,1.61)
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(825.3,647.48)
    self:SetSecondView()

    self.fanli_list = AsyncListView.New(OAFishFanLiRender, self.node_list["fanli_list"])
    local draw_num = OAFishWGData.Instance:GetCurDrawNum()
    self.node_list["leiji"].text.text = string.format(Language.OAFish.LeijiAll, draw_num)
end

function OAFishFanLiView:OnFlush()
    local data = OAFishWGData.Instance:GetSortFanliList()
    self.fanli_list:SetDataList(data, 3)
end
-------------------------------------------------------------------------------------
OAFishFanLiRender = OAFishFanLiRender or BaseClass(BaseRender)
function OAFishFanLiRender:__delete()
    if self.cell_list then
        for i, v in pairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = nil
    end
end

function OAFishFanLiRender:LoadCallBack()
    self.node_list["btn"].button:AddClickListener(BindTool.Bind(self.OnClickBtn, self))
end

function OAFishFanLiRender:OnFlush()
    if not self.data then
        return
    end

    if not self.cell_list then
        self.cell_list = {}
    end

    self.node_list["desc"].text.text = string.format(Language.OAFish.LeijiRender, self.data.lotto_num)
    for i = 1, 4 do
        if self.data.reward_item[i-1] then
            if not self.cell_list[i] then
                self.cell_list[i] = ItemCell.New(self.node_list["cell" .. i])
            else
                self.cell_list[i]:SetActive(true)
            end
            self.cell_list[i]:SetData(self.data.reward_item[i-1])
        else
            if self.cell_list[i] then
                self.cell_list[i]:SetActive(false)
            end
        end
    end

    local has_receive = OAFishWGData.Instance:GetFanliHasReceive(self.data.index)

    local draw_num = OAFishWGData.Instance:GetCurDrawNum()
    self.node_list["red"]:SetActive(not has_receive and draw_num >= self.data.lotto_num)
    self.node_list["btn"]:SetActive(not has_receive and draw_num >= self.data.lotto_num)

    self.node_list.image_wdc:SetActive(draw_num < self.data.lotto_num)
    self.node_list.image_ylq:SetActive(has_receive)
end

function OAFishFanLiRender:OnClickBtn()
    if not self.data.index then
        return
    end

    OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.LEIJI_REWARD, self.data.index)
end