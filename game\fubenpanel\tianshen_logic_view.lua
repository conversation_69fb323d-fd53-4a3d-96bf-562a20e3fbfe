TianShenLogicView = TianShenLogicView or BaseClass(SafeBaseView)

function TianShenLogicView:__init()
    self.view_cache_time = 0
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_tianshen_scene_info")
    self.active_close = false

    self.change_callback = BindTool.Bind(self.TianShenChangeEvent, self)
end

function TianShenLogicView:__delete()

end

function TianShenLogicView:LoadCallBack()
    RoleWGData.Instance:NotifyAttrChange(self.change_callback, {"special_appearance"})
    self.tianshen_reward_list = AsyncListView.New(TianShenFbRewardItemRender, self.node_list["tianshen_reward_list"])
    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        self:InitCallBack()
    end)
end

function TianShenLogicView:ShowIndexCallBack()
    self:Flush()
end

function TianShenLogicView:InitCallBack()
    MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list.tianshen_buff,0)

    local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
    self.obj = self.node_list["layout_tianshen_info_root"].gameObject
    self.obj.transform:SetParent(parent.gameObject.transform)
    self.obj.transform.localPosition = Vector3.zero
    self.obj.transform.localScale= Vector3.one
    if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil
end

function TianShenLogicView:ReleaseCallBack()
    if self.tianshen_reward_list then
        self.tianshen_reward_list:DeleteMe()
        self.tianshen_reward_list = nil
    end

    if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
    self.is_out_fb = nil
    self.data = nil

    RoleWGData.Instance:UnNotifyAttrChange(self.change_callback)

    if self.timer_quest then
        GlobalTimerQuest:CancelQuest(self.timer_quest)
        self.timer_quest = nil
    end

    if self.star_view then
        self.star_view:DeleteMe()
        self.star_view = nil
    end

end

function TianShenLogicView:OpenCallBack()
    if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function TianShenLogicView:CloseCallBack()
    self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
    self.node_list.tianshen_buff:SetActive(false)
    self.node_list.tianshen_buff.transform:SetParent(self.root_node_transform,false)
end

function TianShenLogicView:OnFlush()
    local cfg = FuBenPanelWGData.Instance:GetTianShenSceneLogicCfg() or FuBenPanelWGData.Instance:GetTianShenFirstLayerSceneLogicCfg()
    local boss_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[cfg.boss_id]
    local scene_info = FuBenWGData.Instance:GetFbSceneLogicInfo()
    if boss_config then
        self.node_list["condition"].text.text = string.format(Language.FuBenPanel.KillTianShenBoss, boss_config.name, scene_info.kill_boss_num or 0)
    else
        print_error("获取不到配置 >>>>>  ", cfg.boss_id)
    end
    if RobertManager.Instance:IsPlaying() then
        local reward_items = {}
        for k,v in pairs(ConfigManager.Instance:GetAutoConfig("newer_cfg_auto").wts_reward[1].reward_item) do
           table.insert(reward_items, v)
        end
        self.tianshen_reward_list:SetDataList(reward_items, 3)
    else
        local drop_data_list = FuBenPanelWGData.Instance:GetTianShenDropList(cfg.layer)
        self.tianshen_reward_list:SetDataList(drop_data_list, 3)
    end
    local inc_hurt_while_bianshen = cfg.inc_hurt_while_bianshen / 100
    local dec_hurt_while_bianshen = cfg.dec_hurt_while_bianshen / 100
    self.node_list.buff_des.text.text = string.format(Language.FuBenPanel.TianShenBuffDes,inc_hurt_while_bianshen,dec_hurt_while_bianshen)
    self:TianShenChangeEvent("special_appearance")
    self:OnFlushStar()
	self.node_list.fb_desc_tips.text.text = Language.FuBenPanel.FubenPerBossTips4
end

function TianShenLogicView:TianShenChangeEvent(key)
    if key == "special_appearance" then
        local main_role = Scene.Instance:GetMainRole()
        if not main_role then return end
        local is_flag = main_role:IsTianShenAppearance()
        if is_flag then
            self.node_list.trail_effect:SetActive(true)
            local start_pos = Vector2(-2,-418)
            local x, y = self.node_list.trail_effect.transform.anchoredPosition.x, self.node_list.trail_effect.transform.anchoredPosition.y
            local end_pos = Vector2(x, y)
            GlobalTimerQuest:AddDelayTimer(function() UITween.MoveToShowPanel(GuideModuleName.TianShenLogicView,self.node_list.trail_effect, start_pos,end_pos,1.5,nil,function ()

            end) end, 0.1)
        else
            self.node_list.trail_effect:SetActive(false)
        end
    end

end

function TianShenLogicView:OnFlushStar()
    if nil == self.star_view then
        self.star_view =  FuBenStarClock.New(self.node_list.star_view)
    end
    local cfg = FuBenPanelWGData.Instance:GetTianShenSceneLogicCfg()
    local other_cfg = FuBenPanelWGData.Instance:GetTianShenOtherCfg()
    local data = {}
    if not IsEmptyTable(cfg) and not IsEmptyTable(other_cfg) then
        data = {time3 = cfg.star_time_3, time2 = cfg.star_time_2, time1 = cfg.star_time_1, time0 = cfg.star_time_0,per0 = other_cfg.star0, per1 = other_cfg.star1, per2 = other_cfg.star2, per3 = other_cfg.star3, str = Language.Boss.StarAniStr,}
    elseif SceneType.FakeTianShenFb == Scene.Instance:GetSceneType() and not IsEmptyTable(other_cfg) then
        cfg = FuBenPanelWGData.Instance:GetTianShenFirstLayerSceneLogicCfg()
        data = {time3 = cfg.star_time_3, time2 = cfg.star_time_2, time1 = cfg.star_time_1, per1 = other_cfg.star1, per2 = other_cfg.star2, per3 = other_cfg.star3, str = Language.Boss.StarAniStr,}
    end
    self.star_view:SetData(data)
    self.star_view:Flush()
    
end