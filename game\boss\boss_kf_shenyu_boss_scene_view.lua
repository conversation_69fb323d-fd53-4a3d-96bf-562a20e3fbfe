--废弃的
ShengYuBossSceneUI = ShengYuBossSceneUI or BaseClass(SafeBaseView)

function ShengYuBossSceneUI:__init()
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_syboss_panel")
    self.active_close = false
end

function ShengYuBossSceneUI:ReleaseCallBack()
    if self.boss_list_view then
        self.boss_list_view:DeleteMe()
        self.boss_list_view = nil
    end
end

function ShengYuBossSceneUI:CloseCallBack()
    local mainui_ctrl_instnace = MainuiWGCtrl.Instance
    mainui_ctrl_instnace:AddInitCallBack(nil, function ()
        local parent = mainui_ctrl_instnace:GetTaskOtherContent()
        self.node_list.layout_syboss_panel_root.transform:SetParent(self.root_node_transform,false)
        -- mainui_ctrl_instnace:ChangeTaskBtnName(Language.Task.task_text3)
        local mainui_view = MainuiWGCtrl.Instance:GetView()
        -- mainui_view:SetTaskCallBack(nil)
        -- mainui_view:SetTeamCallBack(nil)
        -- mainui_view:SetTaskButtonTrue()
        mainui_view:SetOtherContents(false)
    end)
    if self.shengyu_boss_global_event then
        GlobalEventSystem:UnBind(self.shengyu_boss_global_event)
        self.shengyu_boss_global_event = nil
    end
end

function ShengYuBossSceneUI:LoadCallBack()
    local mainui_ctrl_instnace = MainuiWGCtrl.Instance
    mainui_ctrl_instnace:AddInitCallBack(nil, function ()
        local parent = mainui_ctrl_instnace:GetTaskOtherContent()
        self.node_list.layout_syboss_panel_root.transform:SetParent(parent.transform,false)
        -- mainui_ctrl_instnace:ChangeTaskBtnName(Language.Task.task_text8)
        local mainui_view = MainuiWGCtrl.Instance:GetView()
        -- mainui_view:SetTaskCallBack(BindTool.Bind2(self.OnBossUIShow,self,true))
        -- mainui_view:SetTeamCallBack(BindTool.Bind2(self.OnBossUIShow,self,false))
        -- mainui_view:SetTaskButtonTrue()
        mainui_view:SetOtherContents(true)
    end)
end

function ShengYuBossSceneUI:CreateBossList()

end

function ShengYuBossSceneUI:ShowIndexCallBack()
    if not self.boss_list_view then
        self.boss_list_view = AsyncListView.New(ShengYuBossFollowRender,self.node_list.BossList)
        self.shengyu_boss_global_event = GlobalEventSystem:Bind(KF_BOSS_TYPE.SHENGYU_CLICK,BindTool.Bind(self.SetCurIndex, self))
    end
    self:Flush()
end

function ShengYuBossSceneUI:OnFlush()
    if self.boss_list_view then
        local data_list = BossWGData.Instance:GetSacredBossInfoList()
        self.boss_list_view:SetDataList(data_list, 3)
    end
end

-- function ShengYuBossSceneUI:OnBossUIShow( state )
--     self.node_list.layout_syboss_panel_root:SetActive(state)
-- end

function ShengYuBossSceneUI:SetCurIndex(index)
    self.boss_list_view:SelectIndex(index)
end

--刷新时间
function ShengYuBossSceneUI:OnClickShengyuAscriptionStart()
end

--即将离开
function ShengYuBossSceneUI:OnClickShengyuAscriptionEnd()
end

-----------------------------------------------------------------
ShengYuBossFollowRender = ShengYuBossFollowRender or BaseClass(BaseRender)
function ShengYuBossFollowRender:__init(instance)
    XUI.AddClickEventListener(self.node_list.BtnSelf, BindTool.Bind(self.OnClickBossRender, self))
end

function ShengYuBossFollowRender:ReleaseCallBack()
end

function ShengYuBossFollowRender:OnClickBossRender()
    local boss_data = self.data
    if nil ~= boss_data then
        if boss_data.boss_id > 0 then
            local sence_id = Scene.Instance:GetSceneId()
            local role = Scene.Instance:GetMainRole()
            local role_x,role_y = role:GetLogicPos()
            BossWGData.Instance:SetCurSelectBossID(0 , 0, boss_data.boss_id)
            if  role_x == boss_data.x_pos and role_y == boss_data.y_pos then
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            else
                GuajiWGCtrl:StopGuaji()
                AtkCache.target_obj = nil
                MoveCache.SetEndType(MoveEndType.FightByMonsterId)
                MoveCache.param1 = boss_data.boss_id
                GuajiCache.monster_id = boss_data.boss_id
                local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
                GuajiWGCtrl.Instance:MoveToPos(sence_id, boss_data.x_pos, boss_data.y_pos, range)
            end
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.ShengYu_Left_OnClick2) --需要手动寻找小怪
        end
    end
    GlobalEventSystem:Fire(KF_BOSS_TYPE.SHENGYU_CLICK, self.index)
end

function ShengYuBossFollowRender:OnFlush()
    if not self.data or IsEmptyTable(self.data) then
        return
    end

    local str = ""
    if self.data.boss_id > 0 then
        local monster_data = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
        if monster_data then
            str = string.format("%s Lv.%s", monster_data.name, monster_data.level)
            self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(monster_data.boss_jieshu))
        end
    else
        str = string.format(Language.Boss.ShengYu_Left_Monst1,self.data.monster_count)
    end
    self.node_list.TextDesc.text.text = str
    
    local tmp_str = ""
    if self.data.is_exist then
        if self.data.is_exist == 1 then
            tmp_str = Language.Boss.ShengYu_Left_OnClick1   --点击前往
        else
            tmp_str = Language.Boss.ShengYu_Left_Monst2     --Boss已击杀
        end
    end
    self.node_list.TimeDesc.text.text = tmp_str
end

function ShengYuBossFollowRender:OnSelectChange(is_select)
    if self.data.boss_id > 0 then
        if self.node_list["SelectLigth"] then
            self.node_list["SelectLigth"]:SetActive(is_select)
        end
    end
end