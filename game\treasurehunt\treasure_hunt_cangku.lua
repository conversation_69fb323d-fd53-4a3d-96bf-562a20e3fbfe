--新寻宝仓库
TreasureHuntStorageView = TreasureHuntStorageView or BaseClass(SafeBaseView)

function TreasureHuntStorageView:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "TreasureHuntStorageView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, -26), sizeDelta = Vector2(814, 524)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_storage")
    self:SetMaskBg(true, true)
    self.is_sorting = false
end

function TreasureHuntStorageView:ReleaseCallBack()
    if self.storage_grid then
        self.storage_grid:DeleteMe()
        self.storage_grid = nil
    end

    if self.delay_ckbag_tip ~= nil then
        GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
        self.delay_ckbag_tip = nil
    end
    self.is_sorting = false
    CountDownManager.Instance:RemoveCountDown("sort_treasure_storage")
end

function TreasureHuntStorageView:LoadCallBack()
    self.open_num = 207
    self.node_list.title_view_name.text.text = Language.TreasureHunt.StorageTitle
    if not self.storage_grid then
		self.storage_grid = AsyncBaseGrid.New()
        self.storage_grid:SetIsShowTips(false)        
        self.storage_grid:SetStartZeroIndex(false)              
		self.storage_grid:CreateCells({col = 9, cell_count = self.open_num, list_view = self.node_list["ph_bag_grid"],itemRender = TreasureHuntBagCell})
		self.storage_grid:SetSelectCallBack(BindTool.Bind1(self.SelectBagCellCallBack, self))
    end
    self.node_list["btn_onekey_getout"].button:AddClickListener(BindTool.Bind(self.OnClickGetOut, self))
    self.node_list["btn_sort"].button:AddClickListener(BindTool.Bind(self.OnClickSort, self))
end

function TreasureHuntStorageView:SelectBagCellCallBack(cell)
    if nil == cell then
		return
	end
	if nil == cell.data or not next(cell.data) then return end

	if self.delay_ckbag_tip then
		TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_FETCH_ITEM, cell:GetIndex() -1)
		GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
		self.delay_ckbag_tip = nil
		return
    end

	self.delay_ckbag_tip = GlobalTimerQuest:AddDelayTimer(function ()
        -- 在双击的0.2秒内 一键取出  会导致data数据为空 加个容错处理
        if cell.data and cell.data.item_id and cell.data.item_id > 0 then
            TipWGCtrl.Instance:OpenItem(cell.data, ItemTip.FROM_BAOXIANG,{fromIndex = cell:GetIndex()},nil)
        end
		GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
		self.delay_ckbag_tip = nil
	end,0.2)
end

function TreasureHuntStorageView:OnClickSort()
    if self.is_sorting then
        return
    end
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_REQ_SORT_BAG)
    self.relive_time = TimeWGCtrl.Instance:GetServerTime() + 5
    CountDownManager.Instance:AddCountDown("sort_treasure_storage",
            BindTool.Bind1(self.UpdateCountDownTime, self),
            BindTool.Bind(self.CompleteCountDownTime, self, true),
            self.relive_time, nil, 0.5)
end

function TreasureHuntStorageView:UpdateCountDownTime(elapse_time, total_time)
    self.is_sorting = true
    local last_time = math.ceil(total_time - elapse_time)
    XUI.SetButtonEnabled(self.node_list["btn_sort"], false)
    self.node_list["sort_text"].text.text = string.format(Language.Common.TimeStr8, last_time) 
end

function TreasureHuntStorageView:CompleteCountDownTime()
    self.is_sorting = false
    CountDownManager.Instance:RemoveCountDown("sort_treasure_storage")
    self.node_list["sort_text"].text.text = Language.TreasureHunt.Sort
    XUI.SetButtonEnabled(self.node_list["btn_sort"], true)
end

function TreasureHuntStorageView:OnClickGetOut()
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_FETCH_ALL)
end

function TreasureHuntStorageView:OnFlush(param)
    local info = TreasureHuntWGData.Instance:GetStorageInfo()
    local storage_grid_list = info.item_list or {}
    self.storage_grid:SetDataList(storage_grid_list)
    self.storage_grid:ReloadData(0)
end

----------------------------------------------------------------------------------
TreasureHuntBagCell = TreasureHuntBagCell or BaseClass(ItemCell)
function TreasureHuntBagCell:__delete()
    
end

function TreasureHuntBagCell:LoadCallBack()
    
end

function TreasureHuntBagCell:SetData(data)
    ItemCell.SetData(self,data)
end

