************************ = ************************ or BaseClass(BaseRender)
function ************************:__init(instance)
    self.ver_tab = {}
    self.ver_obj_list = {}
    self.ver_cell_list = {}

    self.hor_tab = {}
    self.hor_length = {}
    self.hor_obj_list = {}
    self.hor_cell_list = {}

    self.cur_select_slot = -1
    self.cur_select_ver_index = 0

    self.ver_cell_load_complete = false
    self.hor_cell_load_complete = false

    local vertical_tabbar = instance.VerticalTabbar
	if vertical_tabbar then
		self.ver_list = vertical_tabbar.VerticalTabbar
		self.ver_list_content = vertical_tabbar.VerticalTabbarContent
	end

    local horizontal_tabbar = instance.HorizontalTabbar
	if horizontal_tabbar then
        self.bg = horizontal_tabbar.bg
		self.hor_list = horizontal_tabbar.HorizontalTabbar
		self.hor_list_content = horizontal_tabbar.HorizontalTabbarContent
	end
end

function ************************:__delete()
    self:CleanVerAsyncLoader()
    self:CleanVerObjList()
    self:CleanVerCellList()

    self:CleanHorAsyncLoader()
    self:CleanHorObjList()
    self:CleanHorCellList()
end

function ************************:InitList(ver_tab, hor_tab)
    self.ver_tab = ver_tab or {}


    if self.hor_list then
        self.hor_tab = hor_tab or {}
        self.hor_length = {}
        for i , v in pairs(self.hor_tab) do
            self.hor_length[i] = (type(v) == "table") and #v or 0
        end
    end

    self:LoadVerCell()
    self:LoadHorCell()
end

function ************************:GetHorListViewNumbers(ver_index)
	return self.hor_length[ver_index] or 0
end

-- 设置标签选择回调
function ************************:SetSelectCallback(callback)
	self.tabbar_call_back = callback
end

-- 设置标签创建完成回调
function ************************:SetTabbarCompleteCallback(callback)
	self.tabbar_complete_call_back = callback
end

-- 执行标签创建完成回调
function ************************:OprateTabbarCompleteCallback()
    if not self.ver_cell_load_complete or not self.hor_cell_load_complete then
        return
    end

	if self.tabbar_complete_call_back then
        self.tabbar_complete_call_back()
    end
end

-- 设置神体选择回调
function ************************:SetGBodySelectCallback(callback)
	self.select_gbody_call_back = callback
end

function ************************:CleanVerAsyncLoader()
    if self.ver_res_async_loader then
		self.ver_res_async_loader:Destroy()
        self.ver_res_async_loader = nil
	end
end

function ************************:CleanVerObjList()
	for _, v in ipairs(self.ver_obj_list) do
		ResMgr:Destroy(v)
	end

	self.ver_obj_list = {}
end

function ************************:CleanVerCellList()
	for _, v in pairs(self.ver_cell_list) do
		v:DeleteMe()
	end

	self.ver_cell_list = {}
end

function ************************:LoadVerCell()
    -- self:CleanHorAsyncLoader()
    -- self:CleanHorObjList()
    -- self:CleanHorCellList()

    local flem_data = FairyLandEquipmentWGData.Instance
	local ver_num = flem_data:GetGodBodyMaxNum()
    local ver_data = flem_data:GetGodBodyList()
    local max_show_num = flem_data:GetGodBodyMaxShowNum1()

	self.ver_res_async_loader = AllocResAsyncLoader(self, "flem_tabbar_ver_load_key")
	self.ver_res_async_loader:Load("uis/view/fairy_land_equipment_ui_prefab", "VerticalTabbarCell", nil, function(new_obj)
		for i = 0, ver_num do
			local obj = ResMgr:Instantiate(new_obj)
			if nil == obj then
                return
            end

			obj.transform:SetParent(self.ver_list_content.transform, false)
			self.ver_obj_list[i] = obj
			local ver_item_cell = FLEMVerRender.New(obj)

			ver_item_cell:SetIndex(i)
			ver_item_cell:SetData(ver_data[i])
            ver_item_cell:SetActive(i <= max_show_num)
            ver_item_cell:SetClickCallBack(BindTool.Bind(self.OnClickVerCallBack, self))

			self.ver_cell_list[i] = ver_item_cell
		end

        self.ver_cell_load_complete = true
        if self.cahce_refresh_ver_tabbar then
            self:RefreshVerCellData()
        end

        self:OprateTabbarCompleteCallback()
        self:JumpToTabbar()
	end)
end

-- 刷新神体标签
function ************************:TryRefreshVerCellData()
    self.cahce_refresh_ver_tabbar = true
    if self.ver_cell_load_complete then
        self:RefreshVerCellData()
    end
end

-- 刷新神体标签
function ************************:RefreshVerCellData()
    -- print_error("---刷新神体标签------")
    self.cahce_refresh_ver_tabbar = nil
    local flem_data = FairyLandEquipmentWGData.Instance
    local ver_data = flem_data:GetGodBodyList()
    local max_show_num = flem_data:GetGodBodyMaxShowNum1()

    for k,v in pairs(self.ver_cell_list) do
        v:SetData(ver_data[k])
        v:SetActive(k <= max_show_num)
    end
end

-- 点击神体标签回调
function ************************:OnClickVerCallBack(ver_cell, tab_index)
    local data = ver_cell and ver_cell:GetData()
    if data == nil then
        return
    end

    local slot = data:GetSlotIndex()
    --local is_act = data:GetIsAct()
    -- if not is_act and tab_index == 10 then
    --     local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
    --     TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.ClickGodBodyLimitDesc2)
    --     self.cache_hor_bar_index = nil
    --     return
    -- end

    -- print_error("--点击神体标签回调---", slot, tab_index)
    self.cur_select_slot = slot
    self.cur_select_ver_index = tab_index
    
    -- 更新显示功能标签
    if self.hor_list_content then
        self:TryRefreshHorCellData()
        -- self.hor_list_content:CustomSetActive(tab_index == 10)
        -- self.bg:CustomSetActive(tab_index == 10)
        -- if tab_index == 10 then
        --     self:TryRefreshHorCellData()
        -- else
        --     for k,v in pairs(self.hor_cell_list) do
        --         v.view.toggle.isOn = false
        --     end
        -- end
    end

    -- 神体标签高亮
    for k,v in pairs(self.ver_cell_list) do
        v:RefreshHL(slot)
    end

    -- 选择神体标签回调
    if self.select_gbody_call_back then
        self.select_gbody_call_back(slot)
    end

    -- 选中功能标签
    local hor_index
    local hor_length = self:GetHorListViewNumbers(tab_index / 10)
    if hor_length > 0 then
        if self.cache_hor_bar_index then
            hor_index = self.cache_hor_bar_index % 10
            self.cache_hor_bar_index = nil
        else
            local remind_jump = FairyLandEquipmentWGData.Instance:GetSelectBodyJumpRemind(slot)
            hor_index = remind_jump % 10
        end
    else
        self.cache_hor_bar_index = nil
    end

    if hor_index then
        local hor_cell = self.hor_cell_list[hor_index]
        if hor_cell then
            if not hor_cell.view.toggle.isOn then
                hor_cell.view.toggle.isOn = true
                -- ReDelayCall(self, function()
                --     hor_cell.view.toggle.isOn = true
                -- end, 0.1, "************************")
            else
                self:OnClickHorCallBack(hor_cell, true)
            end
        end
    else
        if self.tabbar_call_back then
			self.tabbar_call_back(tab_index)
		end
    end
end

function ************************:CleanHorAsyncLoader()
    if self.hor_res_async_loader then
		self.hor_res_async_loader:Destroy()
        self.hor_res_async_loader = nil
	end
end

function ************************:CleanHorObjList()
	for _, v in ipairs(self.hor_obj_list) do
		ResMgr:Destroy(v)
	end

	self.hor_obj_list = {}
end

function ************************:CleanHorCellList()
	for _, v in pairs(self.hor_cell_list) do
		v:DeleteMe()
	end

	self.hor_cell_list = {}
end

function ************************:LoadHorCell()
    -- self:CleanVerAsyncLoader()
    -- self:CleanVerObjList()
    -- self:CleanVerCellList()

    local hor_num = 0
    for i = 1, #self.ver_tab do
        local num = self:GetHorListViewNumbers(i)
        if num > hor_num then
            hor_num = num
        end
    end

    self.hor_res_async_loader = AllocResAsyncLoader(self, "flem_tabbar_hor_load_key")
	self.hor_res_async_loader:Load("uis/view/fairy_land_equipment_ui_prefab", "HorizontalTabbarCell", nil, function(new_obj)
		for i = 1, hor_num do
			local obj = ResMgr:Instantiate(new_obj)
			if nil == obj then
                return
            end

			obj.transform:SetParent(self.hor_list_content.transform, false)
			self.hor_obj_list[i] = obj
			local hor_item_cell = FLEMHorRender.New(obj)
			hor_item_cell:SetIndex(i)
            hor_item_cell:SetActive(false)
            hor_item_cell:SetToggleGroup(self.hor_list.toggle_group)
            hor_item_cell:AddClickEventListener(BindTool.Bind(self.OnClickHorCallBack, self), true)
			self.hor_cell_list[i] = hor_item_cell
		end

        self.hor_cell_load_complete = true
        if self.cahce_refresh_hor_tabbar then
            self:RefreshHorCellData()
        end
        self:OprateTabbarCompleteCallback()
        self:JumpToTabbar()
	end)
end

-- 点击功能标签回调
function ************************:OnClickHorCallBack(cell, isOn)
    -- print_error("--点击功能标签回调---", cell and cell:GetIndex(), isOn, self.cur_select_ver_index)
	if cell and isOn then
		local hor_index = cell:GetIndex()
        local ver_index = self.cur_select_ver_index or 10
		if self.tabbar_call_back then
			self.tabbar_call_back(ver_index + hor_index)
		end
	end
end

-- 刷新神体标签
function ************************:TryRefreshHorCellData()
    self.cahce_refresh_hor_tabbar = true
    if self.hor_cell_load_complete then
        self:RefreshHorCellData()
    end
end

-- 刷新功能标签
function ************************:RefreshHorCellData()
    -- print_error("---刷新功能标签------")
    local flem_data = FairyLandEquipmentWGData.Instance
    local slot = self.cur_select_slot
    local hor_show = flem_data:GetViewIndexShowList(slot)
    local ver_index = math.floor(self.cur_select_ver_index / 10)
    local hor_data = self.hor_tab[ver_index] or {}
    local hor_index = 0
    for k,v in pairs(self.hor_cell_list) do
        hor_index = self.cur_select_ver_index + k
        local show_data = hor_show[hor_index] or {}
        v:SetData({slot = slot, name = hor_data[k], tab_index = hor_index})
        v:SetActive(show_data.is_show)
    end
end

-- 刷新全部标签
function ************************:RefreshAllCellData(refresh_ver, refresh_hor)
    if refresh_ver then
        self:TryRefreshVerCellData()
    end

    if refresh_hor then
        self:TryRefreshHorCellData()
    end
end

-- 标签选择
function ************************:TryJumpToTabbar(slot, hor_bar_index)
    self.cache_slot = slot
    self.cache_hor_bar_index = hor_bar_index
    self:JumpToTabbar()
end

function ************************:JumpToTabbar()
    -- print_error("---标签选择----", self.cache_slot, self.cache_hor_bar_index)
    if self.cache_slot == nil then
        return
    end

    if not self.ver_cell_load_complete or not self.hor_cell_load_complete then
        return
    end

    local ver_index = self.cache_hor_bar_index or 10
    ver_index = math.floor(ver_index / 10) * 10
    local cell = self.ver_cell_list[self.cache_slot]
    self.cache_slot = nil
    if cell then
        cell:OnClick(ver_index)
    end
end





-- 神体标签
FLEMVerRender = FLEMVerRender or BaseClass(BaseRender)
function FLEMVerRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_select"], BindTool.Bind(self.OnClick, self, 10))
    --XUI.AddClickEventListener(self.node_list["btn_gbody"], BindTool.Bind(self.OnClick, self, 10))
end

function FLEMVerRender:SetClickCallBack(callback)
    self.click_callback = callback
end

function FLEMVerRender:OnClick(tab_index)
	if nil ~= self.click_callback then
		self.click_callback(self, tab_index)
	end
end

function FLEMVerRender:RefreshHL(slot)
    if self.data == nil then
        return
    end

    local cur_slot = self.data:GetSlotIndex()
    local is_cur_slot = slot == cur_slot
    local is_select_book = is_cur_slot
    local is_select_body = is_cur_slot
    self.node_list["select_img"]:CustomSetActive(is_cur_slot)
    -- self.node_list["gbook_hl_content"]:CustomSetActive(is_select_book)
    -- self.node_list["gbody_nor_content"]:CustomSetActive(not is_select_body)
    -- self.node_list["gbody_hl_content"]:CustomSetActive(is_select_body)
end

function FLEMVerRender:OnFlush()
    if self.data == nil then
    	return
    end

    local flem_data = FairyLandEquipmentWGData.Instance
    local slot = self.data:GetSlotIndex()
    local is_act = self.data:GetIsAct()
    local page_gather_num = self.data:GetPageGatherNum()
    local need_num = flem_data:GetActBodyNeedNum()
    local slot_cfg = flem_data:GetGodBodyCfg(slot)
    -- 名字
    if slot_cfg then
        self.node_list["gbook_nor_text"].text.text = slot_cfg.book_name
        self.node_list["gbook_hl_text"].text.text = slot_cfg.book_name

        local body_level = self.data:GetLevel()
        -- local body_name = slot_cfg.body_name

        -- self.node_list["gbody_nor_text"].text.text = body_name
        -- self.node_list["gbody_hl_text"].text.text = body_name

        local body_desc
        if is_act then
            local level_cfg = flem_data:GetGBUpLevelCfg(slot, body_level)
            body_desc = level_cfg and level_cfg.name or ""

        else
            body_desc = Language.FairyLandEquipment.ActGodBodyRemindDesc--string.format(Language.FairyLandEquipment.ActGodBodyRemindDesc, slot_cfg.short_name)
            body_desc = ToColorStr(body_desc, COLOR3B.D_RED)
        end

        self.node_list["gbody_nor_limit_desc"].text.text = body_desc
        self.node_list["gbody_hl_limit_desc"].text.text = body_desc
        
        local bundle, asset = ResPath.GetFairyLandEquipImages("a3_lticon_" .. slot)
        self.node_list.lt_icon.image:LoadSpriteAsync(bundle, asset, function ()
            self.node_list.lt_icon.image:SetNativeSize()
        end)

        bundle, asset = ResPath.GetFairyLandEquipImages(slot_cfg.label_img)
        self.node_list.label_img.image:LoadSpriteAsync(bundle, asset, function ()
            self.node_list.label_img.image:SetNativeSize()
        end)
    end

    -- 锁
    self.node_list["gbody_lock"]:SetActive(not is_act)

    -- 天书收集状态
    for i = 0, need_num - 1 do
        self.node_list["page_act_" .. i]:SetActive(i < page_gather_num)
    end

    -- 红点
    if is_act then
        self.node_list["gbook_remind"]:SetActive(false)
         local remind = flem_data:GetGodBodySlotRemind(slot)
        if not remind and flem_data:GetSlotHolyEquipRemind(slot) then
            remind = true
        end

        if not remind and flem_data:GetForgeRemind(slot) then
            remind = true
        end

        self.node_list["gbody_remind"]:SetActive(remind)
    else
        local book_remind = flem_data:GeSlotGodBookRemind(slot)
        self.node_list["gbook_remind"]:SetActive(book_remind)
        self.node_list["gbody_remind"]:SetActive(false)
    end
end







-- 功能标签
FLEMHorRender = FLEMHorRender or BaseClass(BaseRender)
function FLEMHorRender:OnFlush()
    if self.data == nil then
    	return
    end

    local slot = self.data.slot
    local name = self.data.name
    self.node_list["Text"].text.text = name
    self.node_list["TextHL"].text.text = name
    
    local flem_data = FairyLandEquipmentWGData.Instance
    local remind = false
    local tab_index = self.data.tab_index
    if tab_index == TabIndex.fairy_land_eq_god_body then
        remind = flem_data:GetGodBodySlotRemind(slot)
    elseif tab_index == TabIndex.fairy_land_eq_holy_equip then
        remind = flem_data:GetSlotHolyEquipRemind(slot)
    elseif tab_index == TabIndex.fl_eq_forge_strengthen then
        remind = flem_data:GetStrengthenRedPointBySlot(slot)
    elseif tab_index == TabIndex.fl_eq_forge_evolve then
        remind = flem_data:GetEvolveSlotRemind(slot) == 1
    elseif tab_index == TabIndex.fl_eq_forge_upquality then
        remind = flem_data:GetUpqualitySlotRemind(slot) == 1
    end

    self.node_list["RedPoint"]:SetActive(remind)
end