require("game/festival_activity/festival_mowang_youli/festival_mowang_wg_data")

FestivalMoWangYouliWGCtrl = FestivalMoWangYouliWGCtrl or BaseClass(BaseWGCtrl)

function FestivalMoWangYouliWGCtrl:__init()
	if FestivalMoWangYouliWGCtrl.Instance then
		ErrorLog("[FestivalMoWangYouliWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalMoWangYouliWGCtrl.Instance = self

	self.data = FestivalMoWangYouliWGData.New()

	--self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
    self:BindGlobalEvent(OtherEventType.BossInfoChange, BindTool.Bind(self.OnBossInfoChange, self))
    --FestivalActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/festival_activity_mowangyouli_auto", BindTool.Bind(self.OnHotUpdate, self))
    self:RegisterAllProtocols()
end

function FestivalMoWangYouliWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    FestivalMoWangYouliWGCtrl.Instance = nil
end

function FestivalMoWangYouliWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(FestivalMowangYouLiDropLimitInfo,'OnFestivalMowangYouLiDropLimitInfo')
end

function FestivalMoWangYouliWGCtrl:OnPassDay()
    FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2267)
end

function FestivalMoWangYouliWGCtrl:OnBossInfoChange(boss_type)
    local old_enter_times, old_max_times = self.data:GetBossTimesInfo(boss_type)
    local enter_times, max_times = BossWGData.Instance:GetBossTimesInfo(boss_type)
    if old_enter_times ~= enter_times or old_max_times ~= max_times then
        self.data:SetBossTimesInfo(boss_type, enter_times, max_times)
        FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2267, "boss_times")
    end
end

function FestivalMoWangYouliWGCtrl:OnHotUpdate()
	self.data:LoadConfig()
    FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2267, "mowang_youli_hot_update")
end

function FestivalMoWangYouliWGCtrl:OnFestivalMowangYouLiDropLimitInfo(protocol)
	self.data:SetItemInfoData(protocol)
    FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2267,"mowang_youli_times")
end

function FestivalMoWangYouliWGCtrl:ReqCOAMoWangYouLiDropLimitItemInfo()
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.FESTIVAL_ACT_OA_MOWANGYOULI_2
 	protocol.opera_type = 0
 	protocol.param_1 = 0
 	protocol.param_2 = 0
 	protocol.param_3 = 0
 	protocol:EncodeAndSend()
end