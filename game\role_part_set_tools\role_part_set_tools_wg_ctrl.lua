require("game/role_part_set_tools/role_part_set_tools_view")

--------------------------------------------------------------
--角色相关，如属性，装备等
--------------------------------------------------------------
RolePartSetToolsWGCtrl = RolePartSetToolsWGCtrl or BaseClass(BaseWGCtrl)
function RolePartSetToolsWGCtrl:__init()
    if RolePartSetToolsWGCtrl.Instance then
        ErrorLog("[RolePartSetToolsWGCtrl] Attemp to create a singleton twice !")
    end

    RolePartSetToolsWGCtrl.Instance = self

    self.view = RolePartSetToolsView.New()
end

function RolePartSetToolsWGCtrl:__delete()
    RolePartSetToolsWGCtrl.Instance = nil
    self.view:DeleteMe()
end

function RolePartSetToolsWGCtrl:OpenRolePartToolsView()
	self.view:Open()
end