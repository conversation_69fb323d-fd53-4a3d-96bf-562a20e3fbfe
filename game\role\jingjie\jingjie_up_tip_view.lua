JingJieUpTipView = JingJieUpTipView or BaseClass(SafeBaseView)

function JingJieUpTipView:__init()
	self:SetMaskBgAlpha(0.9)
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/role_ui/jingjie_ui_prefab", "layout_jingjie_up")
end

function JingJieUpTipView:LoadCallBack()
	if not self.attr_list then
		self.attr_list = AsyncListView.New(JingJieUpTipAttrItemRender, self.node_list.attr_list)
	end
end

function JingJieUpTipView:ReleaseCallBack()
	if self.attr_list then
		self.attr_list:DeleteMe()
		self.attr_list = nil
	end
end

function JingJieUpTipView:OnFlush()
	local cur_level = JingJieWGData.Instance:GetJingJieLevel()
	local last_level = cur_level - 1
	last_level = last_level > 0 and last_level or 0
	local cur_level_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(cur_level)
	local last_level_cfg = JingJieWGData.Instance:GetJingJieCfgBylevel(last_level)
	self.node_list.jingjie_name.text.text = cur_level_cfg.name or ""
	self.node_list.desc_last_level.text.text = last_level_cfg.name or ""
	self.node_list.desc_cur_level.text.text = cur_level_cfg.name or ""

	local attr_list = JingJieWGData.Instance:GetJingJieAttrAddListCfgBylevel(cur_level)
	self.attr_list:SetDataList(attr_list)

	local bundle, asset = ResPath.GetRoleUIImage("a3_jingjie_" .. cur_level_cfg.jingjie_level + 3) --资源比表的数据多3个
	self.node_list.jingjie_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.jingjie_icon.image:SetNativeSize()
	end)
end

JingJieUpTipAttrItemRender = JingJieUpTipAttrItemRender or BaseClass(BaseRender)

function JingJieUpTipAttrItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list["attr_name"].text.text = AttributeMgr.DisposeAttrName(
	Language.Common.AttrName[AttributeMgr.GetAttributteKey(self.data.attr_name)], true, true)
	self.node_list["attr_value"].text.text = AttributeMgr.PerAttrValue(self.data.attr_name, self.data.last_value)
	self.node_list["add_value"].text.text = AttributeMgr.PerAttrValue(self.data.attr_name, self.data.attr_value)
end
