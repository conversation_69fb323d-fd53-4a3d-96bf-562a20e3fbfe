require("game/rechargereward/rechargereward_wg_data")
require("game/rechargereward/daily_total_recharge_view")
-- require("game/rechargereward/rechargereward_daily_view")
-- require("game/rechargereward/rechargereward_again_view")
-- require("game/rechargereward/rechargereward_third_view")
-- require("game/rechargereward/rechargereward_total_view")

RechargeRewardWGCtrl = RechargeRewardWGCtrl or BaseClass(BaseWGCtrl)
function RechargeRewardWGCtrl:__init()
	if RechargeRewardWGCtrl.Instance then
		ErrorLog("[RechargeRewardWGCtrl] Attemp to create a singleton twice !")
	end
	RechargeRewardWGCtrl.Instance = self

	self.data = RechargeRewardWGData.New()
	-- self.view = RechargeRewardView.New()
	self.daily_recharge_view = DailyTotalRechargeView.New(GuideModuleName.DailyRecharge)
	-- self.again_recharge_view = RechargeRewardAgainView.New()
	-- self.third_recharge_view = RechargeRewardThirdView.New()
	-- self.total_recharge_view = TotalRechargeRewardView.New()

	-- self.wangcheng_task_open_id = 680
	-- self.task_data_change_callback = BindTool.Bind(self.OnTaskDataListChange, self)
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function RechargeRewardWGCtrl:__delete()

	-- if self.view then
	-- 	self.view:DeleteMe()
	-- 	self.view = nil
	-- end
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if self.daily_recharge_view ~= nil then
		self.daily_recharge_view:DeleteMe()
		self.daily_recharge_view = nil
	end
	-- if self.again_recharge_view ~= nil then
	-- 	self.again_recharge_view:DeleteMe()
	-- 	self.again_recharge_view = nil
	-- end
	-- if self.third_recharge_view ~= nil then
	-- 	self.third_recharge_view:DeleteMe()
	-- 	self.third_recharge_view = nil
	-- end
	-- if self.total_recharge_view ~= nil then
	-- 	self.total_recharge_view:DeleteMe()
	-- 	self.total_recharge_view = nil
	-- end
	RechargeRewardWGCtrl.Instance = nil

end

-- function RechargeRewardWGCtrl:MainuiOpenCreate()
-- 	-- TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_change_callback, false)  --任务列表还没有下发先注册 后面删
-- end

function RechargeRewardWGCtrl:Open(param_t)
	if IS_ON_CROSSSERVER then return end
	if param_t and param_t.open_param then
		-- self.view:SetSelectIndex(tonumber(param_t.open_param))
	end
	-- local icon = MainuiWGCtrl.Instance:GetUiIcon(MainuiIconName.FirstCharge)
	-- if icon == nil then return end
	-- icon:RemoveIconEffect()
	-- self.view:Open()
end

function RechargeRewardWGCtrl:FLush()
	-- self.view:Flush()
	self.daily_recharge_view:Flush()
	-- self.again_recharge_view:Flush()
	-- self.third_recharge_view:Flush()
	-- self.total_recharge_view:Flush()
end

-- 刷新首充
function RechargeRewardWGCtrl:RefreshFirstRechargeView()
	-- self.view:Flush()
end

-- 刷新每日首充
function RechargeRewardWGCtrl:RefreshDailyRechargeView()
	self.daily_recharge_view:Flush()
end

-- 打开每日首沖界面
function RechargeRewardWGCtrl:OpenDailyRechargeView()
	-- local icon = MainuiWGCtrl.Instance:GetUiIcon(MainuiIconName.DailyRecharge)
	-- icon:RemoveIconEffect()
	self.daily_recharge_view:Open()
end

-- 打开再充界面
function RechargeRewardWGCtrl:OpenAgainRechargeView()
	-- self.again_recharge_view:Open()
end

-- 打开三充界面
function RechargeRewardWGCtrl:OpenThirdRechargeView()
	-- self.third_recharge_view:Open()
end

-- 打开累充界面
function RechargeRewardWGCtrl:OpenTotalRechargeView()
	-- self.total_recharge_view:Open()
end

-- function RechargeRewardWGCtrl:OnTaskDataListChange(task_id, task_reason)
-- 	if TaskWGData.Instance:GetTaskIsCompleted(self.wangcheng_task_open_id) then
-- 		TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_change_callback)
-- 	elseif task_id == self.wangcheng_task_open_id and task_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
-- 		TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_change_callback)
-- 		local chongzhi_data = ServerActivityWGData.Instance:GetTotalChongZhiData()
-- 		if chongzhi_data and chongzhi_data.history_recharge < 880 then
-- 			self:Open()
-- 		end
-- 	end
-- end