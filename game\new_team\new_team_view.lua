NewTeamView = NewTeamView or BaseClass(SafeBaseView)

-- local CustomPos = {
-- 	Vector2(-342.34,0),
-- 	Vector2(0,0),
-- 	Vector2(342.34,0)
-- }

local CustomPos = {
	Vector2(401,-107),
	Vector2(721,-107),
	Vector2(1042,-107)
}


function NewTeamView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg()
	
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_team")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_panel3_2")
	self.is_matching = false
	self.first_open = true
	self.delay_flush = false
	self.has_load_callback = false
	self.img_change_leader = false
end

function NewTeamView:__delete()

end

function NewTeamView:ReleaseCallBack()
	if self.team_type_change_event then
		GlobalEventSystem:UnBind(self.team_type_change_event)
		self.team_type_change_event = nil
	end

	if self.nt_team_list then
		for k,v in pairs(self.nt_team_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.nt_team_list = {}
	end

	if self.team_goal_list then
		for k,v in pairs(self.team_goal_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.team_goal_list = nil
	end

	if self.alert_window1 then
		self.alert_window1:DeleteMe()
		self.alert_window1 = nil
	end

	if self.alert_window3 then
		self.alert_window3:DeleteMe()
		self.alert_window3 = nil
	end

	if self.team_cell_flush then
		GlobalEventSystem:UnBind(self.team_cell_flush)
		self.team_cell_flush = nil
	end

	if self.world_talk_timecount then
		GlobalEventSystem:UnBind(self.world_talk_timecount)
		self.world_talk_timecount = nil
	end

	if self.word_talk_cd_over then
		GlobalEventSystem:UnBind(self.word_talk_cd_over)
		self.word_talk_cd_over = nil
	end

	if self.btn_leader_list then
		self.btn_leader_list:DeleteMe()
		self.btn_leader_list = nil
	end
	self.has_specail_load = nil
	self.need_speail_load = nil
	self.select_toggle_1 = nil
	self.first_open = nil
	self.delay_flush = false
	self.has_load_callback = false
	self.img_change_leader = false
	self.team_info_id = nil
	self.has_change_btn_gray = nil
end

function NewTeamView:CloseCallBack()
	self.is_matching = false
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	PlayerPrefsUtil.SetInt("team_type", team_type)
	PlayerPrefsUtil.SetInt("fb_mode", fb_mode)
	NewTeamWGData.Instance:ClearTeamInfo()
end

function NewTeamView:OpenCallBack()
	if self.is_matching and not NewTeamWGData.Instance:GetIsMatching() then
		self:OnClickAutoMatch()
	end
	self.member_list = {}
end

function NewTeamView:LoadCallBack()
	self.nt_team_list = {}
	for i=1,GameEnum.MAX_TEAM_MEMBER_NUMS do
		ResMgr:LoadGameobjSync("uis/view/new_team_ui_prefab", "ph_team_render",
			function (obj)
				obj.transform:SetParent(self.node_list["ph_team_list"].transform,false)
				obj = U3DObject(obj)
				self.nt_team_list[i] = TeamListItem.New(obj)
				self.nt_team_list[i]:SetIndex(i)
				if i == GameEnum.MAX_TEAM_MEMBER_NUMS then
					self:TeamCellFlush()
				end
			end)
		XUI.AddClickEventListener(self.node_list["btn_operate" .. i],BindTool.Bind2(self.OnClickKick,self,i))
	end

	self.team_goal_list = {}
	local goal_list, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial()
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local is_special = true

	local res_async_loader = AllocResAsyncLoader(self, "btn_team_common")
	res_async_loader:Load("uis/view/new_team_ui_prefab", "btn_team_common", nil, function(new_obj)
		for i,v in ipairs(goal_list) do
			local obj = ResMgr:Instantiate(new_obj)
			obj.transform:SetParent(self.node_list["viewport"].transform, false)
			self.team_goal_list[i] = GoalListItemRender.New(obj)
			self.team_goal_list[i]:SetData(v)
			self.team_goal_list[i].view.button:AddClickListener(BindTool.Bind(self.OnClickTeamGoal, self, self.team_goal_list[i]))
			if SocietyWGData.Instance:GetIsInTeam() == 1 then
				if NewTeamWGData.Instance:GetIsMatching() then
					local tmp_taraget = {}
					-- local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
					tmp_taraget.team_type = now_team_type
					tmp_taraget.fb_mode = now_fb_mode
					self.team_goal_list[i]:OnSelectChange(tmp_taraget)
				end
			elseif v.team_type == now_team_type and v.fb_mode == now_fb_mode then
				is_special = false
				self:OnClickTeamGoal(self.team_goal_list[i])
			end
		end
	end)

	if not IsEmptyTable(specail_list) then
		self.normal_goal_count = #goal_list
		local callback = function(btn)
			local res_async_loader = AllocResAsyncLoader(self, "fb_item")
			res_async_loader:Load("uis/view/new_team_ui_prefab", "fb_item", nil, function(new_obj)
				for i,v in ipairs(specail_list) do
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(btn, false)
					self.team_goal_list[self.normal_goal_count + i] = GoalListItemRender.New(obj)
					self.team_goal_list[self.normal_goal_count + i]:SetData(v)
					self.team_goal_list[self.normal_goal_count + i].view.button:AddClickListener(BindTool.Bind(self.OnClickTeamGoal, self, self.team_goal_list[self.normal_goal_count + i]))
					if i == #specail_list then
						self.has_specail_load = true
					end
					if self.need_speail_load then
						self:InitBtnSelectShow(true)
					end
				end
			end)
		end

		local res_async_loader = AllocResAsyncLoader(self, "Content")
		res_async_loader:Load("uis/view/new_team_ui_prefab", "Content", nil, function(new_obj)
			local obj = ResMgr:Instantiate(new_obj)
			obj.transform:SetParent(self.node_list["viewport"].transform, false)
			local btn = obj.transform:Find("List1").transform
			callback(btn)
			self.select_toggle_1 = obj.transform:Find("SelectBtn1"):GetComponent(typeof(UnityEngine.UI.Toggle))
			self.select_toggle_1:AddValueChangedListener(BindTool.Bind(self.InitBtnSelectShow, self))
			-- if is_special then
			for i,v in ipairs(specail_list) do
				if v.team_type == now_team_type and v.fb_mode == now_fb_mode then
					self.select_toggle_1.isOn = true
					break
				end
			end
		end)
	end

	self.alert_window1 = Alert.New()
	self.alert_window1:SetLableString(Language.NewTeam.EnterTip)
	self.alert_window1:SetOkFunc(BindTool.Bind1(self.OnClickAlertWindowOK, self))
	self.alert_window3 = Alert.New()

	XUI.AddClickEventListener(self.node_list.btn_create_team, BindTool.Bind1(self.OnClickCrateTeam, self))
	XUI.AddClickEventListener(self.node_list.btn_change_goal, BindTool.Bind1(self.OnClickChangeGoal, self))
	XUI.AddClickEventListener(self.node_list.level_limit_bg, BindTool.Bind1(self.OnClickChangeGoal, self))

	XUI.AddClickEventListener(self.node_list.btn_invite2, BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list.btn_invite3, BindTool.Bind1(self.OnClickInvite, self))

	XUI.AddClickEventListener(self.node_list.btn_match, BindTool.Bind1(self.OnClickAutoMatch, self))
	XUI.AddClickEventListener(self.node_list.btn_quick_team, BindTool.Bind1(self.OnClickQuick, self))
	XUI.AddClickEventListener(self.node_list.btn_apply, BindTool.Bind1(self.OnClickApplyList, self))

	for i=1,4 do
		self.node_list["btn_choose_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickChoose,self,i))
	end
	XUI.AddClickEventListener(self.node_list["btn_block"], BindTool.Bind1(self.OnClickBlock, self))

	XUI.AddClickEventListener(self.node_list.btn_word_talk, BindTool.Bind1(self.OnClickWorldTalk, self))

	self.team_cell_flush = GlobalEventSystem:Bind(TeamInfoQuery.TEAM_INFO_BACK,BindTool.Bind(self.TeamCellFlush,self))
	self.world_talk_timecount = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK,BindTool.Bind(self.UpdateTeamWordTalkBtn,self))
	self.word_talk_cd_over = GlobalEventSystem:Bind(TeamWorldTalk.COMPLETE_CALL_BACK,BindTool.Bind(self.ComleteTeamWoldTalkCD,self))
	self.member_list = {}

	self.has_load_callback = true

	if self.first_open then
		self.first_open = false
	end
	if self.delay_flush then
		self.delay_flush = false
		self:TeamCellFlush()
	end
	self.team_type_change_event = GlobalEventSystem:Bind(TeamEventType.TEAM_TYPE_CHANGE, BindTool.Bind(self.FlsuhLeftSelect, self))
end

function NewTeamView:InitBtnSelectShow(is_on)
	if is_on then
		if self.has_specail_load then
			local _, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial()
			local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
			local state = false
			for i,v in ipairs(specail_list) do
				if v.team_type == now_team_type and v.fb_mode == now_fb_mode then
					self:OnClickTeamGoal(self.team_goal_list[self.normal_goal_count + i])
					state = true
				end
			end
			if not state then
				self:OnClickTeamGoal(self.team_goal_list[self.normal_goal_count + 1])
			end
		else
			self.need_speail_load = true
		end
	end
end

function NewTeamView:OnClickInvite()
	NewTeamWGCtrl.Instance:OpenInviteView()
end

function NewTeamView:OnClickWorldTalk()
	if CountDownManager.Instance:HasCountDown("team_word_talk") then
		return
	end

	if GameEnum.MAX_TEAM_MEMBER_NUMS == #SocietyWGData.Instance:GetTeamMemberList() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ManyTower.TeamIsMaxMember)
		return
	end
	GlobalEventSystem:Fire(TeamWorldTalk.MAIN_WORLD_TALK)
	XUI.SetGraphicGrey(self.node_list.btn_word_talk,true)
    self.node_list.btn_word_talk_txt.text.text = string.format(Language.NewTeam.WorldTalk6, COMMON_CONSTS.TEAM_WORLD_TALK)
end
function NewTeamView:RemindState()

	local apply_list = SocietyWGData.Instance:GetReqTeamList()
	if # apply_list > 0 then
		self.node_list["Remind"]:SetActive(true)
		else
		self.node_list["Remind"]:SetActive(false)
	end

end
function NewTeamView:UpdateTeamWordTalkBtn(time)
	if self.node_list.btn_word_talk_txt then
		self.node_list.btn_word_talk_txt.text.text = string.format(Language.NewTeam.WorldTalk6, time)
	end
	if not self.has_change_btn_gray then
		self.has_change_btn_gray = true
		XUI.SetGraphicGrey(self.node_list["btn_word_talk"], true)
	end
end

function NewTeamView:ComleteTeamWoldTalkCD()
	if CountDownManager.Instance:HasCountDown("team_world_talk") then
        CountDownManager.Instance:RemoveCountDown("team_world_talk")
    end
    if self.node_list.btn_word_talk_txt then
   		self.node_list.btn_word_talk_txt.text.text = ""
   	end
   	self.has_change_btn_gray = nil
    XUI.SetGraphicGrey(self.node_list.btn_word_talk,false)
end


function NewTeamView:OnClickApplyList()
	NewTeamWGCtrl.Instance:OpenApplyView()
end

function NewTeamView:OnClickQuick()
	if 0 == SocietyWGData.Instance:GetIsInTeam() then
		NewTeamWGCtrl.Instance:OpenQuickView()
	else
	-- if self.data.role_id == RoleWGData.Instance.role_vo.role_id and 1 == SocietyWGData.Instance:GetIsInTeam() then

		local content = ""
	    local main_role = Scene.Instance:GetMainRole()
	    local str = "%s(%d级)玩家退出了队伍"
	    -- content = main_role.vo.name.."" .. "退出了队伍"
	    content = string.format(str,main_role.vo.name,main_role.vo.level)
	    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1)

		SocietyWGCtrl.Instance:SendExitTeam()

		NewTeamWGData.Instance:ClearTeamInfo()
		-- NewTeamWGData.Instance:SetTeamTypeAndMode(0, 0)
	-- else
	-- 	-- SocietyWGCtrl.Instance:SendKickOutOfTeam(self.data.role_id)
	end
end

function NewTeamView:OnClickKick( index )
	-- self.nt_team_list[index]:OnClickOperate()
end

function NewTeamView:OnClickAutoMatch()
	local goal_info = NewTeamWGData.Instance:GetNowGoalInfo()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local operate = NewTeamWGData.Instance:GetIsMatching() and 1 or 0
	NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, team_type, fb_mode)
end

function NewTeamView:OnClickCrateTeam()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	if 1 == SocietyWGData.Instance:GetIsTeamLeader() then
		if 1 == #SocietyWGData.Instance:GetTeamMemberList() then
			local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
			local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
			if now_goal_info.fb_type == FUBEN_TYPE.FBCT_WUJINJITAN then
				local is_first = FuBenWGData.Instance:GetEXPFbIsFirstEnter()
				if is_first == 1 then
					FuBenWGCtrl.Instance:SendEnterFB(now_goal_info.fb_type, 1, fb_mode)    -- 副本类型, 是否组队, 层数
					return
				end

			end
			self.alert_window1:Open()
		else
			self:OnClickAlertWindowOK()
		end
	else
		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
	end
	local content = ""
    local main_role = Scene.Instance:GetMainRole()
    --content = main_role.vo.name .. "创建了队伍"

    local str = "%s(%d级)玩家创建了队伍"
	    -- content = main_role.vo.name.."" .. "退出了队伍"
	content = string.format(str,main_role.vo.name,main_role.vo.level)
    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1)
end

function NewTeamView:OnClickAlertWindowOK()
	local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local now_goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	local flag_data = FuBenWGData.Instance:SetCombineMark()
	local is_combine_flag = bit:d2b(flag_data)
	if now_goal_info.fb_type == FUBEN_TYPE.FBCT_WUJINJITAN or now_goal_info.fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then
		local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local pass_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.pass_item_id)
		local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang() and FB_COMBINE_TYPE.LINGHUN_GUANGCHANG or FB_COMBINE_TYPE.WUJINJITAN
		local yet_enter_time = WuJinJiTanWGData.GetFBEnterTimes()
		local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
		local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
		local enter_dj_times = other_cfg.everyday_times + yet_buy_time + user_ticket_time - yet_enter_time
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local cost_text = Language.Common.GoldText
		if  is_combine_flag[32 - is_linghunguangchang_fb] == 1 then
			FuBenWGCtrl.Instance:SendFBUseCombine(1,now_goal_info.fb_type)
			if pass_num < enter_dj_times  then
				local need_buy_count = enter_dj_times - pass_num
				if need_buy_count <= 1 then
					if bind_gold_num >= other_cfg.buy_pass_item_gold then
						cost_text = Language.Common.BindGoldText
					end 
					self.alert_window3:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
				else
					if bind_gold_num >= other_cfg.buy_pass_item_gold * need_buy_count then
						cost_text = Language.Common.BindGoldText
					end 
					self.alert_window3:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient1, cost_text, other_cfg.buy_pass_item_gold,need_buy_count))
				end
				self.alert_window3:SetOkFunc(function ()
					if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold * need_buy_count) then
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, need_buy_count, 0, 1)
					else
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, need_buy_count, 0, 0)
					end
					self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
				end)
				self.alert_window3:Open()
				return
			else
				self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
			end
		else
			if 0 == pass_num then
				local flag_data = FuBenWGData.Instance:SetCombineMark()
				local is_combine_flag = bit:d2b(flag_data)
				if bind_gold_num >= other_cfg.buy_pass_item_gold then
					cost_text = Language.Common.BindGoldText
				end 
				self.alert_window3:SetLableString(string.format(Language.FuBenPanel.TicketsInsufficient, cost_text, other_cfg.buy_pass_item_gold))
				self.alert_window3:SetOkFunc(function ()
					if RoleWGData.Instance:GetIsEnoughBindGold(other_cfg.buy_pass_item_gold) then
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 1)
					else
						ShopWGCtrl.Instance:SendShopBuy(other_cfg.pass_item_id, 1, 0, 0)
					end
				end)
				self.alert_window3:Open()
				return
			else
				self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
			end
		end
		return
	elseif now_goal_info.fb_type == FUBEN_TYPE.HIGH_TEAM_EQUIP then
		if is_combine_flag[32 - FB_COMBINE_TYPE.YUANGU] == 1 then
			FuBenWGCtrl.Instance:SendFBUseCombine(1,now_goal_info.fb_type)
		end
		self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
	elseif now_goal_info.fb_type == FUBEN_TYPE.ZHUSHENTA_FB then
		if is_combine_flag[32 - FB_COMBINE_TYPE.KILL_GOD_TOWER] == 1 then
			FuBenWGCtrl.Instance:SendFBUseCombine(1,now_goal_info.fb_type)
		end
		self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
	else
		local is_can_entr = FuBenPanelWGCtrl.Instance:IsCheckCanEnterFb()
	    if not is_can_entr then
	    	SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CanNotEnterFb)
	    	return
	    end
	    self:EnterFubenLiXian(now_goal_info.fb_type, 1, fb_mode)
	end
	--FuBenWGCtrl.Instance:SendEnterFB(now_goal_info.fb_type, 1, fb_mode)    -- 副本类型, 是否组队, 层数


end
function NewTeamView:EnterFubenLiXian(fuben_type,is_team,layer)

	local menmber_info = SocietyWGData.Instance:GetTeamMemberList()
	local is_have_lixian = false
	for k,v in pairs(menmber_info) do
		if v.is_online ~= 1 then
			is_have_lixian = true
			break
		end
	end
	if is_have_lixian then
		self.alert_window3:SetLableString(Language.FuBenPanel.LiXianMember)
		self.alert_window3:SetOkFunc(function ()
			for k,v in pairs(menmber_info) do
				if v.is_online ~= 1 then
					SocietyWGCtrl.Instance:SendKickOutOfTeam(v.orgin_role_id)
				end
			end
			self:SetIsEnterFuBen()
			FuBenWGCtrl.Instance:SendEnterFB(fuben_type,is_team,layer)   -- 副本类型, 是否组队, 无意义
			FuBenPanelWGData.Instance:SetEnterType(is_team)
		end)
		self.alert_window3:Open()
	else
		self:SetIsEnterFuBen()
		FuBenWGCtrl.Instance:SendEnterFB(fuben_type,is_team,layer)   -- 副本类型, 是否组队, 无意义
		FuBenPanelWGData.Instance:SetEnterType(is_team)
	end

end
function NewTeamView:SetIsEnterFuBen()
	if FuBenPanelWGCtrl.Instance.view:IsOpen() then
		FuBenPanelWGCtrl.Instance.view.is_enter_fuben = true
	end
end


function NewTeamView:OnClickChangeGoal()
	if NewTeamWGData.Instance:GetIsMatching() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.AlertTipOK)
		return
	end
	if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		return
	end
	NewTeamWGCtrl.Instance:OpenChangeGoalView()
end

function NewTeamView:ShowIndexCallBack()
	NewTeamWGData.Instance:ClearTeamInfo()
	self:FlsuhLeftSelect()
	self:Flush()
end

function NewTeamView:FlsuhLeftSelect()
	if not self.has_load_callback then
		return
	end
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		return
	end
	local goal_list, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial()
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if now_team_type > 0 then
		for i,v in ipairs(self.team_goal_list) do
			if v.data and v.data.team_type == now_team_type and v.data.fb_mode == now_fb_mode then
				self:OnClickTeamGoal(v)
				break
			end
		end
		if self.select_toggle_1 and now_team_type == 4 then
			self.select_toggle_1.isOn = true
		end
	elseif self.team_goal_list[1] then
		self:OnClickTeamGoal(self.team_goal_list[1])
	end
end

function NewTeamView:SetIsMatching(flag)
	self.is_matching = flag
end

function NewTeamView:TeamCellFlush()
	if self.nt_team_list[3] == nil then
		self.delay_flush = true
		return
	end

	if self.has_load_callback == false then
		self.delay_flush = true
		return
	end

	local change_index , member_list = NewTeamWGData.Instance:GetTeamMemberList()
	self.member_list = member_list
	local member_state,btn_invite_state,state
	local is_leader = (1 == SocietyWGData.Instance:GetIsTeamLeader())

	for k,v in ipairs(change_index) do
		if v then
			self.nt_team_list[k]:SetData(member_list[v])
			member_state = member_list[v] and member_list[v].name or member_list[v]  --人名,"",nil
			btn_invite_state = member_state == "" or not member_state
			state = not btn_invite_state and k ~= 1 and member_list[v].role_id ~= RoleWGData.Instance:InCrossGetOriginUid() and is_leader

			self.node_list["btn_invite" .. k]:SetActive(btn_invite_state)
			self.node_list["shadow" .. k]:SetActive(not (member_list[v] and (member_list[v].role_id ~= 0)))
			self.nt_team_list[k]:SetCommonActive(member_list[v] ~= nil)
		end
	end
end

function NewTeamView:OnFlush()
	local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
	if not goal_info then
		return
	end
	local member_list = __TableCopy(SocietyWGData.Instance:GetTeamMemberList())
	self.node_list["lbl_fb_name"].text.text = goal_info.team_type_name
	self.node_list["btn_apply"]:SetActive(1 == SocietyWGData.Instance:GetIsInTeam() and 1 == SocietyWGData.Instance:GetIsTeamLeader())
	self.node_list["btn_word_talk"]:SetActive(1 == SocietyWGData.Instance:GetIsInTeam())


	local exp_add = NewTeamWGData.Instance:GetExpAdd() * 10
	self.node_list["exp_text"].text.text = exp_add .. "%"
	self:OnFlushBtn()
	for i=1,GameEnum.MAX_TEAM_MEMBER_NUMS do
		if not member_list[i] or member_list[i].is_online == 0 then
			self.node_list["img_people" .. i].image:LoadSprite(ResPath.GetNewTeamImg("a2_zd_renfaguang"))
		else
			self.node_list["img_people" .. i].image:LoadSprite(ResPath.GetNewTeamImg("a2_zd_renhuis"))
		end
	end
	self:FlushLevelLimit()
	self:FlushAllHighLight(goal_info)
	self:RemindState()
end

function NewTeamView:OnFlushBtn()
	local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	local team_state = 0 == SocietyWGData.Instance:GetIsInTeam()

	if NewTeamWGData.Instance:GetIsMatching() then
		self.node_list["text_match"].text.text = (Language.NewTeam.CancelMatch)
	else
		self.node_list["text_match"].text.text = (Language.NewTeam.AutoMatch)
	end
	self.node_list["text_quick_team"].text.text = team_state and Language.NewTeam.QuickTeam or Language.NewTeam.LeaveTeam
	self.node_list["btn_match"]:SetActive(1 == SocietyWGData.Instance:GetIsTeamLeader() or team_state)
	self.node_list["btn_create_team"]:SetActive(1 == SocietyWGData.Instance:GetIsTeamLeader() and 0 ~= goal_info.fb_type or team_state)

	if 1 == SocietyWGData.Instance:GetIsTeamLeader() then
		self.node_list["text_create_team"].text.text = (Language.FuBen.Enterfb)
	else
		self.node_list["text_create_team"].text.text = (Language.Task.CreateTeam)
	end
	if CountDownManager.Instance:HasCountDown("team_word_talk") then
		XUI.SetGraphicGrey(self.node_list.btn_word_talk,true)
	end
end

function NewTeamView:OpenCustomMenu(buff, id ,pos)
	self.custom_menu_state = not self.custom_menu_state
	self.team_info_id = self.custom_menu_state and id or nil
	self.node_list.choose_team_view:SetActive(self.custom_menu_state)
	for k,v in pairs(buff) do
		self.node_list["btn_choose_" .. k]:SetActive(v)
	end
	if self.custom_menu_state then
		self.node_list.layout_choose_team.rect.anchoredPosition = pos
	end
end

function NewTeamView:OnClickChoose(i)
	if self.team_info_id == nil or i == nil then return end
	if i == 1 then
		BrowseWGCtrl.Instance:OpenWithUid(self.team_info_id)
	elseif i == 2 then
		SocietyWGCtrl.Instance:IAddFriend(self.team_info_id)
	elseif i == 3 then
		SocietyWGCtrl.Instance:SendChangeTeamLeader(self.team_info_id)
	elseif i == 4 then
		SocietyWGCtrl.Instance:SendKickOutOfTeam(self.team_info_id)
	end
	self:OnClickBlock()
end

function NewTeamView:OnClickBlock()
	self.custom_menu_state = false
	self.team_info_id = nil
	self.node_list.choose_team_view:SetActive(false)
end

function NewTeamView:OnClickTeamGoal(cell)
	if cell == nil then
		return
	end
	if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		return
	end
	local goal_cfg = cell.data
	--组队的最大等级改为等级排行的第一名
    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
    local top_lv = goal_cfg.role_max_level or top_user_level
	NewTeamWGData.Instance:SetTeamTypeAndMode(goal_cfg.team_type, goal_cfg.fb_mode)
	NewTeamWGData.Instance:SetTeamLimitLevel(goal_cfg.role_min_level, top_lv)
	NewTeamWGData.Instance:GetSetCurSelectTarget(goal_cfg)
	if 1 == SocietyWGData.Instance:GetIsInTeam() then
		NewTeamWGCtrl.Instance:SendChangeTeamLimit(goal_cfg.team_type, goal_cfg.fb_mode, goal_cfg.role_min_level, top_lv)
	end
	self:FlushAllHighLight(cell.data)
	self:FlushLevelLimit()
end

function NewTeamView:FlushLevelLimit()
	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(min_level)
	local str = is_vis and role_level or "Lv." .. role_level
	self.node_list["lbl_role_level"].text.text = str
	self.node_list.feixian_image:SetActive(is_vis)

	is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(max_level)
	str = is_vis and role_level or "Lv." .. role_level
	self.node_list["lbl_role_level0"].text.text = str
	self.node_list.feixian_image0:SetActive(is_vis)
end

function NewTeamView:FlushAllHighLight(data)
	for k,v in pairs(self.team_goal_list) do
		v:OnSelectChange(data)
	end
	if data.fb_type ~= 44 then
		if self.select_toggle_1 then
			self.select_toggle_1.isOn = false
		end
	end
end

NumberBar_NewTeam = NumberBar_NewTeam or BaseClass()

-- 数字转list
function NumberBar_NewTeam:NumberToList(number)
	local num_list = {}
	local is_minus = false

	if number < 0 then
		number = -number
		is_minus = true
	end

	number = math.floor(number)
	table.insert(num_list, 1, "%")
	for i = 1, 20 do
		table.insert(num_list, 1, number % 10)
		number = math.floor(number / 10)
		if number <= 0 then
			break
		end
	end

	if is_minus then
		if self.has_minus then
			table.insert(num_list, 1, "minus")
		end
	else
		if self.has_plus then
			table.insert(num_list, 1, "plus")
		end
	end

	return num_list
end

-- BtnTeamListRender = BtnTeamListRender or BaseClass(BaseRender)

-- function BtnTeamListRender:__init()

-- end

-- function BtnTeamListRender:LoadCallBack()

-- end

-- function BtnTeamListRender:OnFlush()
-- 	if self.data == nil then return end
-- 	self.node_list.text_shaixuan_name.text.text = self.data.name
-- end

-- function BtnTeamListRender:OnSelectChange(is_select)
-- 	self.node_list.Image:SetActive(is_select)
-- end


GoalListItemRender = GoalListItemRender or BaseClass(BaseRender)

function GoalListItemRender:__init()
end

function GoalListItemRender:__delete()
    self.btn_index = nil
end

function GoalListItemRender:ReleaseCallBack()
	-- if self.node_list.arrow then
	-- 	self.node_list.arrow:SetActive(true)
	-- end
	-- if self.node_list.arrow_hl then
	-- 	self.node_list.arrow_hl:SetActive(true)
	-- end
end

function GoalListItemRender:OnFlush()
	local str = self.data.team_type_name

	local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(self.data.team_type)
	local remain_count = total_count - cur_enter_count
	local color = remain_count > 0 and COLOR3B.GREEN or COLOR3B.PINK
	local count = string.format(Language.NewTeam.RemainRewardTimes2, color, remain_count, total_count)
	if total_count == 0 then
		count = ""
	end

	if self.node_list["Text"] then
		self.node_list["Text"].text.text = str
	end

	if self.node_list["TextHL"] then
		self.node_list["TextHL"].text.text = str
	end

	if(self.data.team_type == 4) then
		str = Language.NewTeam.SpecialTeamName
	end

	if self.node_list["text_name"] then
		self.node_list["text_name"].text.text = str .. count
	end

	if self.node_list["text_name_hl"] then
		self.node_list["text_name_hl"].text.text = str .. count
	end
end

function GoalListItemRender:OnSelectChange(is_select)
	--local state = self.data.team_type == data.team_type and self.data.fb_mode == data.fb_mode
	--print_error(" ", self.data.team_type_name, self.data.team_type, self.data.fb_mode, data.team_type, data.fb_mode)
	if self.node_list["hight_light"] then
		self.node_list["hight_light"]:SetActive(is_select)
	end

	if self.node_list["normal_bg"] then
		self.node_list["normal_bg"]:SetActive(not is_select)
	end
end

function GoalListItemRender:SetBtnIndex(index)
	self.btn_index = index
end

function GoalListItemRender:GetBtnIndex()
    return self.btn_index
end

function GoalListItemRender:SetIsShowArrow(is_show)
	-- if self.node_list.arrow then
	-- 	self.node_list.arrow:SetActive(is_show)
	-- end
	-- if self.node_list.arrow_hl then
	-- 	self.node_list.arrow_hl:SetActive(is_show)
	-- end
end

-- function GoalListItemRender:ChangeHL()
-- 	self:OnSelectChange(false)
-- end