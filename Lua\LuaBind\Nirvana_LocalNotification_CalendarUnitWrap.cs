﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_LocalNotification_CalendarUnitWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(Nirvana.LocalNotification.CalendarUnit));
		<PERSON><PERSON>("Era", get_Era, null);
		<PERSON><PERSON>("Year", get_Year, null);
		<PERSON><PERSON>("Month", get_Month, null);
		<PERSON><PERSON>("Day", get_Day, null);
		<PERSON><PERSON>("Hour", get_Hour, null);
		<PERSON><PERSON>("Minute", get_Minute, null);
		<PERSON><PERSON>("Second", get_Second, null);
		<PERSON><PERSON>("Week", get_Week, null);
		<PERSON><PERSON>("Quarter", get_Quarter, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<Nirvana.LocalNotification.CalendarUnit>.Check = CheckType;
		StackTraits<Nirvana.LocalNotification.CalendarUnit>.Push = Push;
	}

	static void Push(IntPtr L, Nirvana.LocalNotification.CalendarUnit arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(Nirvana.LocalNotification.CalendarUnit), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Era(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Era);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Year(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Year);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Month(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Month);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Day(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Day);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Hour(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Hour);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Minute(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Minute);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Second(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Second);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Week(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Week);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Quarter(IntPtr L)
	{
		ToLua.Push(L, Nirvana.LocalNotification.CalendarUnit.Quarter);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		Nirvana.LocalNotification.CalendarUnit o = (Nirvana.LocalNotification.CalendarUnit)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

