﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public class YYFullScreenEffectFog : YYFullScreenEffect
{
    protected override WeatherCommandBuffer CreateCommandBuffer(Camera camera, YYTemporalReprojectionState reprojState, WeatherDownsampleScale downsampleScale, Action<WeatherCommandBuffer> preSetupCommandBuffer)
    {
        if (YYWeather.Instance == null)
        {
            Debug.Log("Cannot create command buffer, WeatherScript.Instance is null");
            return null;
        }

        WeatherCommandBuffer weatherCommandBuffer = GetOrCreateWeatherMakerCommandBuffer(camera);

        if (weatherCommandBuffer != null && weatherCommandBuffer.CommandBuffer != null)
        {
            camera.RemoveCommandBuffer(RenderQueue, weatherCommandBuffer.CommandBuffer);
            weatherCommandBuffer.CommandBuffer.Clear();
        }

        RenderTextureFormat defaultFormat = (camera.allowHDR ? RenderTextureFormat.DefaultHDR : RenderTextureFormat.Default);
        CommandBuffer commandBuffer = weatherCommandBuffer.CommandBuffer;
        int postSourceId = -1;
        RenderTargetIdentifier postSource = postSourceId;
        WeatherCameraType cameraType = weatherCommandBuffer.CameraType;


        int scale = (int)downsampleScale;

        commandBuffer.SetGlobalFloat(WMS._WeatherDownsampleScale, scale);


        /////绘制天上的 物体
        if (preSetupCommandBuffer != null)
        {
            preSetupCommandBuffer.Invoke(weatherCommandBuffer);
        }
        ///////
        //Enabled = false;
        if (Enabled)
        {

            int renderTargetRenderedImageId = WMS._MainTex3;
            int renderTargetDepthTextureId = WMS._WeatherMakerTemporaryDepthTexture;

            RenderTargetIdentifier renderTargetRenderedImage = new RenderTargetIdentifier(renderTargetRenderedImageId);
            RenderTargetIdentifier renderTargetDepthTexture = new RenderTargetIdentifier(renderTargetDepthTextureId);

            BlurShaderType blur = BlurShaderType;

            if (cameraType != WeatherCameraType.Normal)
            {
                if (BlurMaterial.passCount > 3)
                {
                    blur = BlurShaderType.Bilateral;
                }
                else
                {
                    blur = BlurShaderType.GaussianBlur17;
                }
            }

            int mod = (reprojState == null ? 0 : reprojState.ReprojectionSize);
            commandBuffer.GetTemporaryRT(renderTargetRenderedImageId, YYFullScreenEffectUtil.GetRenderTextureDescriptor(scale, mod, 1, defaultFormat, 0, camera), FilterMode.Bilinear);
            commandBuffer.GetTemporaryRT(renderTargetDepthTextureId, YYFullScreenEffectUtil.GetRenderTextureDescriptor(scale, mod, 1, RenderTextureFormat.RFloat, 0, camera), FilterMode.Point);


            // set blend mode for blitting to render texture

            commandBuffer.SetGlobalFloat(WMS._SrcBlendMode, (float)BlendMode.One);
            commandBuffer.SetGlobalFloat(WMS._DstBlendMode, (float)BlendMode.Zero);


            // render to final destination
            // pass 0 深度图上  雾效
            commandBuffer.Blit(renderTargetDepthTextureId, renderTargetRenderedImage, clonedMaterial, 0);



            if (cameraType == WeatherCameraType.Normal)// && DownsampleScalePostProcess != WeatherMakerDownsampleScale.Disabled)
            {
                // pass 2 和 3  径向模糊
                AttachPostProcessing(commandBuffer, clonedMaterial, 0, 0, defaultFormat, renderTargetRenderedImage, reprojState, camera, ref postSourceId, ref postSource);
                //AttachPostProcessing(commandBuffer, material, w, h, defaultFormat, renderTargetRenderedImageId, reprojState, ref postSourceId, ref postSource);
            }

            //双边模糊
            AttachBlurBlit(commandBuffer, renderTargetRenderedImage, renderTargetDepthTexture, CameraTargetIdentifier(), clonedMaterial, blur, defaultFormat, camera);


            // cleanup
            commandBuffer.ReleaseTemporaryRT(renderTargetRenderedImageId);
            commandBuffer.ReleaseTemporaryRT(renderTargetDepthTextureId);

            commandBuffer.SetGlobalTexture(WMS._MainTex2, new RenderTargetIdentifier(BuiltinRenderTextureType.None));

            //commandBuffer.SetGlobalTexture(WMS._MainTex2,);

        }

        ////////


        weatherCommandBuffer.Material = clonedMaterial;
        weatherCommandBuffer.ReprojectionState = reprojState;
        weatherCommandBuffer.RenderQueue = RenderQueue;

        ///开始
        camera.AddCommandBuffer(RenderQueue, commandBuffer);

        // 维护一个
        if (YYCommandBufferManager.Instance != null)
        {
            YYCommandBufferManager.Instance.AddCommandBuffer(weatherCommandBuffer);
        }


        return weatherCommandBuffer;

        //return base.CreateCommandBuffer(camera, reprojState, downsampleScale, preSetupCommandBuffer);
    }

    internal void AttachBlurBlit(CommandBuffer commandBuffer, RenderTargetIdentifier renderedImageId, RenderTargetIdentifier depthTextureId,
         RenderTargetIdentifier finalTargetId, Material material, BlurShaderType blur, RenderTextureFormat defaultFormat, Camera camera)
    {
        if (material != null && material.passCount > 1)
        {
            //  pass 1   还是fog  得到一个深度图
            commandBuffer.Blit(renderedImageId, depthTextureId, material, 1);
        }

        // set blend mode for blitting to camera target
        commandBuffer.SetGlobalFloat(WMS._SrcBlendMode, (float)SourceBlendMode);
        commandBuffer.SetGlobalFloat(WMS._DstBlendMode, (float)DestBlendMode);

        // 真正的双边模糊
        YYFullScreenEffectUtil.AttachBilateralBlur(commandBuffer, BlurMaterial, renderedImageId, finalTargetId, defaultFormat, DownsampleScale, camera);


    }
}
