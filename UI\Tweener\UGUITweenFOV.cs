﻿using UnityEngine;

[AddComponentMenu("UGUI/Tween/UGUI Tween FOV")]
[RequireComponent(typeof(Camera))]
public class UGUITweenFOV : UGUITweener
{
	public float from = 45f;
	public float to = 45f;

	Camera mCam;

	#if UNITY_4_3 || UNITY_4_5 || UNITY_4_6 || UNITY_4_7
	public Camera cachedCamera { get { if (mCam == null) mCam = camera; return mCam; } }
	#else
	public Camera cachedCamera { get { if (mCam == null) mCam = GetComponent<Camera>(); return mCam; } }
	#endif

	[System.Obsolete("Use 'value' instead")]
	public float fov { get { return this.value; } set { this.value = value; } }

	public float value { get { return cachedCamera.fieldOfView; } set { cachedCamera.fieldOfView = value; } }

	protected override void OnUpdate (float factor, bool isFinished) { value = from * (1f - factor) + to * factor; }

	static public UGUITweenFOV Begin (GameObject go, float duration, float to)
	{
		UGUITweenFOV comp = UGUITweener.Begin<UGUITweenFOV>(go, duration);
		comp.from = comp.value;
		comp.to = to;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = from; }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = to; }

}
