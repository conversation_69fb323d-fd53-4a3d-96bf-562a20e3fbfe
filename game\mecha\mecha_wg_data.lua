MechaWGData = MechaWGData or BaseClass(BaseWGCtrl)

-- 机甲部位导航栏显示配置 策划让写死！！！，提出配置
MechaWGData.MECHA_PART_SHOW_CFG = {
    [0] = {icon = "a2_jj_icon_qg",   icon_hl = "a2_jj_icon_qg1",   part_id = MECHA_PART_TYPE.BODY},
    [1] = {icon = "a2_jj_icon_shou", icon_hl = "a2_jj_icon_shou1", part_id = MECHA_PART_TYPE.LEFT_HAND},
    [2] = {icon = "a2_jj_icon_shou", icon_hl = "a2_jj_icon_shou1", part_id = MECHA_PART_TYPE.RIGHT_HAND},
    [3] = {icon = "a2_jj_icon_jiao", icon_hl = "a2_jj_icon_jiao1", part_id = MECHA_PART_TYPE.LEFT_FOOT},
    [4] = {icon = "a2_jj_icon_jiao", icon_hl = "a2_jj_icon_jiao1", part_id = MECHA_PART_TYPE.RIGHT_FOOT},
    [5] = {icon = "a2_jj_icon_yy",   icon_hl = "a2_jj_icon_yy1",   part_id = MECHA_PART_TYPE.LEFT_WING},
    [6] = {icon = "a2_jj_icon_yy",   icon_hl = "a2_jj_icon_yy1",   part_id = MECHA_PART_TYPE.RIGHT_WING},
}

MechaWGData.MECHA_WEAPON_SHOW_CFG = {
    [0] = {icon = "a2_jj_icon_jian",  icon_hl = "a2_jj_icon_jian1"}, --剑
    [1] = {icon = "a2_jj_icon_qiang", icon_hl = "a2_jj_icon_qiang1"}, -- 枪
}

function MechaWGData:__init()
	if MechaWGData.Instance ~= nil then
		print_error("[MechaWGData] attempt to create singleton twice!")
		return
	end
	MechaWGData.Instance = self

    self:InitConfig()

    RemindManager.Instance:Register(RemindName.MechaFighterPlane, BindTool.Bind(self.GetMechaFighterPlaneRemind, self))
    RemindManager.Instance:Register(RemindName.MechaWing, BindTool.Bind(self.GetMechaWingRemind, self))
    RemindManager.Instance:Register(RemindName.MechaWeapon, BindTool.Bind(self.GetMechaWeaponRemind, self))
    RemindManager.Instance:Register(RemindName.MechaToFight, BindTool.Bind(self.GetMechaToFightRemind, self))
    RemindManager.Instance:Register(RemindName.MechaEquip, BindTool.Bind(self.GetMechaEquipRemind, self))
end

function MechaWGData:InitConfig()
    local mecha_cfg = ConfigManager.Instance:GetAutoConfig("mechan_cfg_auto")
    self.other = mecha_cfg[1]
    self.base_cfg = mecha_cfg.base
    self.part_cfg = ListToMap(mecha_cfg.part, "mechan_seq", "part", "sort")
    self.weapon_part_cfg = ListToMap(mecha_cfg.part, "mechan_seq", "part", "weapon_type", "sort")
    self.part_seq_cfg = ListToMap(mecha_cfg.part, "seq")
    self.part_star_cfg = ListToMap(mecha_cfg.part_star, "seq", "star")
    self.help_fight_open_cfg = ListToMap(mecha_cfg.help_fight_open, "seq")
    self.help_fight_attr_cfg = ListToMapList(mecha_cfg.help_fight_attr, "mechan_seq")
    self.mecha_part_item_cfg = ListToMap(mecha_cfg.part, "active_item_id")   -- 机甲模型展示需要
    self.weapon_cfg = mecha_cfg.weapon
    self.wing_cfg = mecha_cfg.wing
    self.skill_show_cfg = mecha_cfg.skill_show
    
    self.puton_list = {}
	self.part_star = {}
	self.help_fight_battle = {}
	self.help_fight_open = {}
	self.choose_wing_skill_id = -1
	self.bianshen_seq = -1
	self.bianshen_end_time = 0
    self.bianshen_start_time = 0
    self.mecha_active_state = {}

    self.mfp_cost_item_cache = {}
    self.mfp_attr_info_data_cache = {}
    self.mfp_cap_cache = {}

    self.mw_cost_item_cache = {}
    self.mw_attr_info_data_cache = {}
    self.mw_cap_cache = {}

    self.mwp_cost_item_cache = {}
    self.mwp_attr_info_data_cache = {}
    self.mwp_cap_cache = {}

    self.mfp_remind = false
    self.mfp_remind_tab = {}

    self.mw_remind = false
    self.mw_remind_tab = {}

    self.mwp_remind = false
    self.mwp_remind_tab = {}

    self.mtf_remind = false
    self.mtf_main_fight_remind = false
    self.mtf_help_fight_remind_tab = {}
    self.mtf_cost_item_cache = {}

    self.mecha_active_part_data_cache = {}
    self.mecha_active_item_cache = {}

    self.marm_remind = false
    self.marm_remind_tab = {}

    self.mecha_cd_reduce = 0

    self:CalMechaCache()
end

function MechaWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.MechaFighterPlane)
    RemindManager.Instance:UnRegister(RemindName.MechaWing)
    RemindManager.Instance:UnRegister(RemindName.MechaWeapon)
    RemindManager.Instance:UnRegister(RemindName.MechaToFight)
    RemindManager.Instance:UnRegister(RemindName.MechaEquip)
    MechaWGData.Instance = nil
end

function MechaWGData:CalMechaCache()
    local wing_to_skill_cache = {}
    local wing_skill_active_cache = {}
    for k, v in pairs(self.wing_cfg) do
        local part_list = string.split(v.need_wing_part_seq, ",")
        local skill_cache = {}

        if not IsEmptyTable(part_list) then
            for i, u in pairs(part_list) do
                local seq = tonumber(u)
                wing_to_skill_cache[seq] = v.skill_id

                local part_cfg = self:GetPartCfgBySeq(seq)
                if not IsEmptyTable(part_cfg) then
                    skill_cache[part_cfg.part] = seq
                end
            end
        end

        wing_skill_active_cache[v.skill_id] = skill_cache
    end
    self.wing_to_skill_cache = wing_to_skill_cache
    self.wing_skill_active_cache = wing_skill_active_cache

    local weapon_skill_list_cache = {}
    for k, v in pairs(self.weapon_cfg) do
        local data = {}
        local normal_skill = {}
        local skill = {}

        local normal_skill_list = string.split(v.normal_skill, "|")
        for i, u in pairs(normal_skill_list) do
            table.insert(normal_skill, tonumber(u))
        end

        local skill_list = string.split(v.skill, "|")
        for i, u in pairs(skill_list) do
            table.insert(skill, tonumber(u))
        end

        data.normal_skill = normal_skill
        data.skill = skill
        weapon_skill_list_cache[v.part_seq] = data
    end

    self.weapon_skill_list_cache = weapon_skill_list_cache

    local mecha_base_bart_cache = {}
    local mecha_active_item_cache = {}
    for k, v in pairs(self.base_cfg) do
        local base_part = {}
        local part_data_list = string.split(v.base_part, ",")
        for i, u in pairs(part_data_list) do
            table.insert(base_part, tonumber(u))
        end

        mecha_base_bart_cache[v.seq] = base_part
        mecha_active_item_cache[v.active_item] = v
    end

    self.mecha_active_item_cache = mecha_active_item_cache
    self.mecha_base_bart_cache = mecha_base_bart_cache
end

--------------------------------get_start-------------------------------
-- 所有机甲
function MechaWGData:GetMechaShowDataList()
    return self.base_cfg
end

-- 单个机甲配置
function MechaWGData:GetMechaCfgBySeq(seq)
    return self.base_cfg[seq]
end

-- 部位列表
function MechaWGData:GetMechaPartShowDataList(mechan_seq)
   return self.part_cfg[mechan_seq]
end

-- 部位组件列表
function MechaWGData:GetMechaPartCellShowDataList(mechan_seq, part)
    return (self.part_cfg[mechan_seq] or {})[part]
end

-- 组件cfg
function MechaWGData:GetMechaPartCellCfg(mechan_seq, part, sort)
    return ((self.part_cfg[mechan_seq] or {})[part] or {})[sort]
end

-- 武器
function MechaWGData:GetMechaWeaponPartShowDataList(mechan_seq, part)
    return (self.weapon_part_cfg[mechan_seq] or {})[part]
end

function MechaWGData:GetMechaWeaponPartCellShowDataList(mechan_seq, part, weapon_type)
    local data_list = {}
    local target_data_list = ((self.weapon_part_cfg[mechan_seq] or {})[part] or {})[weapon_type]

    if not IsEmptyTable(target_data_list) then
        for k, v in pairs(target_data_list) do
            table.insert(data_list, v)
        end
    end

    return data_list
end

-- 部件星级
function MechaWGData:GetPartStarBySeq(seq)
    return self.part_star[seq] or -1
end

function MechaWGData:GetPartCfgBySeq(seq)
    return self.part_seq_cfg[seq]
end

-- 机甲的所有部位的星级 > 0 表示机甲激活了 
function MechaWGData:IsMechaActive(mecha_seq)
    return self.mecha_active_state[mecha_seq] or false
end

function MechaWGData:HasMechaActive()
    for k, v in pairs(self:GetMechaShowDataList()) do
        if self:IsMechaActive(v.seq) then
            return true
        end
    end

    return false
end

-- 获取机甲所有属性 本身激活 + 部位激活
function MechaWGData:GetMechaActiveShowAllAttrDataList(mecha_seq)
    local attr_data_list = {}
    local attr_index = 0
    local attr_id_to_index = {}
    
    local function add_attr_info(attr_data)
        local attr_id, attr_value = 0, 0
        local data_len = 8
    
        for i = 1, data_len do
            attr_id = attr_data["attr_id" .. i]
            attr_value = attr_data["attr_value" .. i]
            if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                local index
                if attr_id_to_index[attr_id] then
                    index = attr_id_to_index[attr_id]
                else
                    index = attr_index + 1
                    attr_index = attr_index + 1
                    attr_id_to_index[attr_id] = index
                end

                attr_data_list["attr_id" .. index] = attr_id
                attr_data_list["attr_value" .. index] = (attr_data_list["attr_value" .. index] or 0) + attr_value
            end
        end
    end

    if self:IsMechaActive(mecha_seq) then
        -- 激活属性
        add_attr_info(self:GetMechaCfgBySeq(mecha_seq))
        --部位激活属性
        for k, v in pairs(self:GetMechaPartShowDataList(mecha_seq)) do
            for x, y in pairs(v) do
                local star = self:GetPartStarBySeq(y.seq)
 
                if star >= 0 then
                    local star_cfg = self:GetPartUpStarCfg(y.seq, star)
                    if not IsEmptyTable(star_cfg) then
                        add_attr_info(star_cfg)
                    end
                end
            end
        end
    end

    if not IsEmptyTable(attr_data_list) then
        return EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_data_list, "attr_id", "attr_value", "attr_name", "attr_value", 1, attr_index, true)
    end

    return attr_data_list
end

-- 策划要求拿基础部位的属性
function MechaWGData:GetMechaActiveShowAttrDataList(mecha_seq, out_name, out_value_name)
    local attr_data_list = {}

    local attr_index = 0
    local attr_id_to_index = {}
    
    local function add_attr_info(attr_data)
        local attr_id, attr_value = 0, 0
        local data_len = 8
    
        for i = 1, data_len do
            attr_id = attr_data["attr_id" .. i]
            attr_value = attr_data["attr_value" .. i]
            if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                local index
                if attr_id_to_index[attr_id] then
                    index = attr_id_to_index[attr_id]
                else
                    index = attr_index + 1
                    attr_index = attr_index + 1
                    attr_id_to_index[attr_id] = index
                end

                attr_data_list["attr_id" .. index] = attr_id
                attr_data_list["attr_value" .. index] = (attr_data_list["attr_value" .. index] or 0) + attr_value
            end
        end
    end

    local cfg = self:GetMechaCfgBySeq(mecha_seq)
    if not IsEmptyTable(cfg) then
        add_attr_info(cfg)
        
        local base_part = self:GetMechaBasePartListByMechaSeq(mecha_seq)
        if not IsEmptyTable(base_part) then
            for k, v in pairs(base_part) do
                local star_cfg = self:GetPartUpStarCfg(v, 0)

                if not IsEmptyTable(star_cfg) then
                    add_attr_info(star_cfg)
                end
            end
        end
    end

    if not IsEmptyTable(attr_data_list) then
        local out_name = out_name or "attr_name"
        local out_value_name = out_value_name or "value_str"
        return EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_data_list, "attr_id", "attr_value", out_name, out_value_name, 1, attr_index, false)
    end
    
    return attr_data_list, 0
end

-- 机甲是否出战武器
function MechaWGData:IsMechaActiveWeapon(mecha_seq)
    local weapon_part = self:GetMechaPartWearPart(mecha_seq, MECHA_PART_TYPE.WEAPON)
    return weapon_part >= 0, weapon_part
end

function MechaWGData:GetMechaPartWearPart(mecha_seq, part)
    return (self.puton_list[mecha_seq] or {})[part] or -1
end

function MechaWGData:GetMechaPartWearPartList(mecha_seq)
    return self.puton_list[mecha_seq]
end

function MechaWGData:GetWeaponShowSkillCfg(part_seq)
    return self.weapon_cfg[part_seq]
end

function MechaWGData:GetPartUpStarCfg(seq, star)
    return (self.part_star_cfg[seq] or {})[star]
end

-- 唯一seq
function MechaWGData:IsPartIsMaxStar(seq)
    local cur_star = self:GetPartStarBySeq(seq)
    local next_star = cur_star + 1
    return IsEmptyTable(self:GetPartUpStarCfg(seq, next_star))
end

-- 唯一seq
function MechaWGData:GetPartUpLevelAttrDataList(seq)
    local is_max_level = self:IsPartIsMaxStar(seq)
    local attr_data_list = {}
    local cur_star = self:GetPartStarBySeq(seq)

    if is_max_level then
        local attr_data = self:GetPartUpStarCfg(seq, cur_star)
        if not IsEmptyTable(attr_data) then
            attr_data_list = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_data, "attr_id", "attr_value", "attr_name", "attr_value", 1, 8)
        end
    else
        local cur_is_zero_value = cur_star < 0
        local next_star = cur_star + 1
        cur_star = cur_star >= 0 and cur_star or 0
        local cur_attr_info = self:GetPartUpStarCfg(seq, cur_star)
        local next_attr_info = self:GetPartUpStarCfg(seq, next_star)
        -- 存在下一级加成
        attr_data_list = EquipmentWGData.Instance:OutSortAttrListHaveNextByTypeCfg(cur_attr_info, next_attr_info, cur_is_zero_value, nil, nil, nil, nil, nil, nil, nil, 1, 8)
    end

    return is_max_level, attr_data_list
end

function MechaWGData:IsMFPCostItem(change_item_id)
   return nil ~= self.mfp_cost_item_cache[change_item_id]
end

function MechaWGData:IsMWCostItem(change_item_id)
    return nil ~= self.mw_cost_item_cache[change_item_id]
end

function MechaWGData:IsMWPCostItem(change_item_id)
    return nil ~= self.mwp_cost_item_cache[change_item_id]
end

function MechaWGData:IsMTFCostItem(change_item_id)
    return nil ~= self.mtf_cost_item_cache[change_item_id]
end

function MechaWGData:NeedCheckFightState()
    for k, v in pairs(self:GetHelpFightBattleInfo()) do
        local is_open = self:GetHelpFightIsOpen(k)

        if not is_open then
            return true
        end
    end

    return false
end

function MechaWGData:GetMFPShowCap(seq)
    return self.mfp_cap_cache[seq] or 0
end

function MechaWGData:GetMWShowCap(seq)
    return self.mw_cap_cache[seq] or 0
end

function MechaWGData:GetMWPShowCap(seq)
    return self.mwp_cap_cache[seq] or 0
end

function MechaWGData:GetSKillIdBySeq(seq)
    return self.wing_to_skill_cache[seq] or -1
end

function MechaWGData:GetSkillActiveNeedPartList(skill_id)
    return self.wing_skill_active_cache[skill_id]
end

function MechaWGData:GetWeaponToSkillAllInfo(weapon_part_seq)
    return self.weapon_skill_list_cache[weapon_part_seq]
end

function MechaWGData:GetWeaponToNormalSkillInfo(weapon_part_seq)
    return (self.weapon_skill_list_cache[weapon_part_seq] or {}).normal_skill or {}
end

function MechaWGData:GetWeaponToSkillInfo(weapon_part_seq)
    return (self.weapon_skill_list_cache[weapon_part_seq] or {}).skill or {}
end

function MechaWGData:GetFightBattleDataList()
    local data_list = {}

    local main_data = {
        is_main_battle = true,
        seq = self:GetBianShenSeq(),
        is_open = true,
        open_cfg = {}
    }

    table.insert(data_list, main_data)

    for k, v in pairs(self:GetHelpFightOpenCfg()) do
        local data = {
            is_main_battle = false,
            seq = self:GetHelpFightBattleSeq(v.seq),
            is_open = self:GetHelpFightIsOpen(v.seq),
            open_cfg = v
        }

        table.insert(data_list, data)
    end

    return data_list
end

function MechaWGData:GetBianShenSeq()
    return self.bianshen_seq
end

function MechaWGData:GetCurBianShenMechaCfg()
    local bianshen_seq = self:GetBianShenSeq()

    if bianshen_seq >= 0 then
       return self:GetMechaCfgBySeq(bianshen_seq)
    end
end

function MechaWGData:IsBianShenMecha(mecha_seq)
    return self:GetBianShenSeq() == mecha_seq
end

function MechaWGData:GetBianShenEndTime()
    return self.bianshen_end_time
end

function MechaWGData:GetBianShenStartTime()
    return self.bianshen_start_time
end

function MechaWGData:GetHelpFightBattleSeq(seq)
    return self.help_fight_battle[seq]
end

function MechaWGData:GetHelpFightBattleInfo()
    return self.help_fight_battle
end

function MechaWGData:IsHelpFightMecha(mecha_seq)
    for k, v in pairs(self:GetHelpFightBattleInfo()) do
        if v == mecha_seq then
            return true
        end
    end

    return false
end

function MechaWGData:GetHelpFightOpenCfg()
    return self.help_fight_open_cfg
end

function MechaWGData:GetHelpFightOpenCfgBySeq(seq)
    return self.help_fight_open_cfg[seq]
end

function MechaWGData:GetHelpFightIsOpen(seq)
    return self.help_fight_open[seq] == 1
end

function MechaWGData:GetMechaCapBySeq(mechan_seq)
    return self:GetMFPShowCap(mechan_seq) + self:GetMWShowCap(mechan_seq) + self:GetMWPShowCap(mechan_seq)
end

function MechaWGData:GetHelpFightAttrDesc(mechan_seq)
    local cap = self:GetMFPShowCap(mechan_seq) + self:GetMWShowCap(mechan_seq) + self:GetMWPShowCap(mechan_seq)
    local attr_data_info = self.help_fight_attr_cfg[mechan_seq]

    for k, v in pairs(attr_data_info) do
        if cap >= v.min_cap and cap <= v.max_cap then
            return v
        end
    end
end

function MechaWGData:GetArmamentPartInfo(mechan_seq)
    local part_info = {}
    local part_res_info = {}

    -- 除去武器部位的
    for k, v in pairs(self:GetMechaPartShowDataList(mechan_seq)) do
        if k < MECHA_PART_TYPE.WEAPON then
            part_info[k] = -1
            part_res_info[k] = 0
            local wear_part = self:GetMechaPartWearPart(mechan_seq, k)
    
            if wear_part >= 0 then
                part_info[k] = wear_part
                local part_cfg = self:GetPartCfgBySeq(wear_part)
                part_res_info[k] = part_cfg.res_id or 0
            else
    
                for x, y in pairs(v) do
                    local star =  self:GetPartStarBySeq(y.seq)
     
                    if star >= 0 then
                        part_info[k] = y.seq
                        part_res_info[k] = y.res_id
                        break
                    end
                end
            end
        end
    end

    -- 武器部位
    part_info[MECHA_PART_TYPE.WEAPON] = -1
    part_res_info[MECHA_PART_TYPE.WEAPON] = 0

    local wear_part = self:GetMechaPartWearPart(mechan_seq, MECHA_PART_TYPE.WEAPON)
    if wear_part >= 0 then
        part_info[MECHA_PART_TYPE.WEAPON] = wear_part
        local part_cfg = self:GetPartCfgBySeq(wear_part)
        part_res_info[MECHA_PART_TYPE.WEAPON] = part_cfg.res_id or 0
    else
        for k, v in pairs(self:GetMechaWeaponPartShowDataList(mechan_seq, MECHA_PART_TYPE.WEAPON)) do
            for i, u in pairs(v) do
                local star =  self:GetPartStarBySeq(u.seq)
                if star >= 0 then
                    part_info[MECHA_PART_TYPE.WEAPON] = u.seq
                    part_res_info[MECHA_PART_TYPE.WEAPON] = u.res_id
                    break
                end
            end
        end
    end

    return part_info, part_res_info
end

function MechaWGData:GetActivePartDataList(Mechan_seq, part_id)
    return (self.mecha_active_part_data_cache[Mechan_seq] or {})[part_id] or {}
end

function MechaWGData:IsMechaCanToFighter(mecha_seq, is_main_fight)
    -- 当前出战状态是否存在当前机甲
    if is_main_fight then
        if self:GetBianShenSeq() == mecha_seq then
            return false
        end
    else
        --助战检测
        for k, v in pairs(self:GetHelpFightBattleInfo()) do
            if mecha_seq == v then
                return false
            end
        end
    end
    
    if self:IsMechaActive(mecha_seq) then
        if self:IsMechaActiveWeapon(mecha_seq) then
            return true
        end
    end

    return false
end

function MechaWGData:GetMachaToFightDataList(fight_pos_data)
    local data_list = {}

    if IsEmptyTable(fight_pos_data) then
        return data_list
    end

    local is_main_fight = fight_pos_data.is_main_battle
    for k, v in pairs(self:GetMechaShowDataList()) do
        if self:IsMechaCanToFighter(v.seq, is_main_fight) then
            table.insert(data_list, v)
        end
    end

    return data_list
end

function MechaWGData:IsMechaIsInFighterNow(mecha_seq)
    if self:GetBianShenSeq() == mecha_seq then
        return true
    end

    for k, v in pairs(self:GetHelpFightBattleInfo()) do
        if mecha_seq == v then
            return true
        end
    end

    return false
end

function MechaWGData:GetToFightCanPrepareMecha()
    local mecha_seq = -1

    for k, v in pairs(self:GetMechaShowDataList()) do
        if self:IsMechaActive(v.seq) then
            if not self:IsMechaIsInFighterNow(v.seq) then
                mecha_seq = v.seq
                break
            end
        end
    end

    return mecha_seq
end

function MechaWGData:GetShowSkillDataList()
    return self.skill_show_cfg
end

function MechaWGData:GetShowSkillCfgBySeq(seq)
    return self.skill_show_cfg[seq]
end

function MechaWGData:IsWingSkillIsActive(mecha_seq, skill_id)
    local skill_need_part = self:GetSkillActiveNeedPartList(skill_id)
    
    if IsEmptyTable(skill_need_part) then
        return false
    end

    local active = true
    for k, v in pairs(skill_need_part) do
        if self:GetMechaPartWearPart(mecha_seq, k) ~= v then
            active = false
        end
    end

    return active
end

function MechaWGData:GetMechaBasePartListByMechaSeq(seq)
    return self.mecha_base_bart_cache[seq]
end

function MechaWGData:IsMechaSysItem(item_id)
    return self:IsMechaPartItem(item_id) or self:IsMechaActivePartItem(item_id)
end

function MechaWGData:IsMechaPartItem(item_id)
    return nil ~= self.mecha_part_item_cfg[item_id]
end

function MechaWGData:GetMechaPartCfgByItemId(item_id)
    return self.mecha_part_item_cfg[item_id]
end

function MechaWGData:IsMechaActivePartItem(item_id)
    return nil ~= self.mecha_active_item_cache[item_id]
end

function MechaWGData:GetMechaActiveItemCfgByItemId(item_id)
    return self.mecha_active_item_cache[item_id]
end

function MechaWGData:GetMechaItemActiveState(item_id)
    if self:IsMechaActivePartItem(item_id) then
        local mecha_cfg = self:GetMechaActiveItemCfgByItemId(item_id)
        local seq = mecha_cfg and mecha_cfg.seq or -1

        if seq >= 0 then
            return self:IsMechaActive(seq)
        end
    elseif self:IsMechaPartItem(item_id) then
        local mecha_part_cfg = self:GetMechaPartCfgByItemId(item_id)
        local seq = mecha_part_cfg and mecha_part_cfg.seq or -1

        if seq >= 0 then
           return self:GetPartStarBySeq(seq) >= 0
        end
    end

    return false
end

function MechaWGData:IsMechaWearComplete(mecha_seq)
    local is_wear_complete = true

    local wear_list = self:GetMechaPartWearPartList(mecha_seq)
    if not IsEmptyTable(wear_list) then
        for k, v in pairs(wear_list) do
            if k ~= MECHA_PART_TYPE.LEFT_WING and k ~= MECHA_PART_TYPE.RIGHT_WING then
                if v < 0 then
                    return false
                end
            end
        end
    else
        is_wear_complete = false
    end

    return is_wear_complete
end

function MechaWGData:GetMechaCfgByItemId(item_id)
    if self:IsMechaActivePartItem(item_id) then
        local mecha_cfg = self:GetMechaActiveItemCfgByItemId(item_id)
        if not IsEmptyTable(mecha_cfg) then
            return self:GetMechaActiveShowAttrDataList(mecha_cfg.seq)
        end
    elseif self:IsMechaPartItem(item_id) then
        local part_cfg = self:GetMechaPartCfgByItemId(item_id)

        if not IsEmptyTable(part_cfg) then
            local star = 1
            local attr_cfg = self:GetPartUpStarCfg(part_cfg.seq, star)
            return EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_cfg, "attr_id", "attr_value", "attr_name", "value_str", 1, 8)
        end
    end

    return {}, 0
end
--------------------------------get_end-------------------------------

--------------------------------remind_start-------------------------------

function MechaWGData:GetMechaFighterPlaneRemind()
    if self.mfp_remind then
        return 1
    end

    return 0
end

function MechaWGData:GetMechaWingRemind()
    if self.mw_remind then
        return 1
    end

    return 0
end

function MechaWGData:GetMechaWeaponRemind()
    if self.mwp_remind then
        return 1
    end

    return 0
end

-- 策划特殊要求 没有激活的机甲不给显示红点
function MechaWGData:GetMechaToFightRemind()
    local has_active_mecha = false
    for k, v in pairs(self:GetMechaShowDataList()) do
        if self:IsMechaActive(v.seq) then
            has_active_mecha = true
            break
        end
    end

    if not has_active_mecha then
        return 0
    end

    if self.mtf_remind then
        return 1
    end

    return 0
end

function MechaWGData:GetMechaEquipRemind()
    return 0
end

-- MFP机甲红点
function MechaWGData:GetMFPMechaRemind(mecha_seq)
    return (self.mfp_remind_tab[mecha_seq] or {}).remind or false
end

--  MFP部位
function MechaWGData:GetMFPMechaPartRemind(mecha_seq, part)
    return (((self.mfp_remind_tab[mecha_seq] or {}).part_remind_tab or {})[part]or {}).remind or false
end

--MFP部位cell红点
function MechaWGData:GetMFPMechaPartCellRemind(mecha_seq, part, sort)
    return ((((self.mfp_remind_tab[mecha_seq] or {}).part_remind_tab or {})[part]or {}).part_cell_remind_tab or {})[sort] or false
end

-- MW机甲红点
function MechaWGData:GetMWMechaRemind(mecha_seq)
    return (self.mw_remind_tab[mecha_seq] or {}).remind or false
end

--  MW部位
function MechaWGData:GetMWMechaPartRemind(mecha_seq, part)
    return (((self.mw_remind_tab[mecha_seq] or {}).part_remind_tab or {})[part]or {}).remind or false
end

--MW部位cell红点
function MechaWGData:GetMWMechaPartCellRemind(mecha_seq, part, sort)
    return (((((self.mw_remind_tab[mecha_seq] or {}).part_remind_tab or {})[part]or {}).part_cell_remind_tab or {})[sort] or {}).remind or false
end

function MechaWGData:GetMWMechaPartCellRemindTab(mecha_seq, part, sort)
    return ((((self.mw_remind_tab[mecha_seq] or {}).part_remind_tab or {})[part]or {}).part_cell_remind_tab or {})[sort]
end

-- MWP机甲红点
function MechaWGData:GetMWPMechaRemind(mecha_seq)
    return (self.mwp_remind_tab[mecha_seq] or {}).remind or false
end

--  MWP部位
function MechaWGData:GetMWPMechaWeaponTypeRemind(mecha_seq, weapon_type)
    return (((self.mwp_remind_tab[mecha_seq] or {}).weapon_type_remind_tab or {})[weapon_type]or {}).remind or false
end

--MWP部位cell红点
function MechaWGData:GetMWPechaPartCellRemind(mecha_seq, weapon_type, sort)
    return (((((self.mwp_remind_tab[mecha_seq] or {}).weapon_type_remind_tab or {})[weapon_type]or {}).weapon_type_cell_remind_tab or {})[sort] or {}).remind or false
end

--
function MechaWGData:GetMWPMechaPartCellRemindTab(mecha_seq, weapon_type, sort)
    return ((((self.mwp_remind_tab[mecha_seq] or {}).weapon_type_remind_tab or {})[weapon_type]or {}).weapon_type_cell_remind_tab or {})[sort]
end

function MechaWGData:GetMTFMainFightRemind()
    return self.mtf_main_fight_remind
end

function MechaWGData:GetMTFHelpFightRemind(seq)
    return self.mtf_help_fight_remind_tab[seq] or false
end

function MechaWGData:GetMarmRemind()
    return self.marm_remind
end

function MechaWGData:GetMarmRemindByMechaSeq(mecha_seq)
    return self.marm_remind_tab[mecha_seq] or false
end
-----------------------------------remind_end-------------------------------

--------------------------------protocol_start------------------------------
function MechaWGData:SetMechanAllInfo(protocol)
    self.puton_list = protocol.puton_list                       -- 机甲的穿戴部位    16只机甲的穿戴的部件seq   list 8  8个部位对应的部件seq
	self.part_star = protocol.part_star                         -- 机甲的所有部件的星级  唯一seq对应
	self.help_fight_battle = protocol.help_fight_battle         -- 上阵助战的机甲seq       机甲唯一seq
	self.help_fight_open = protocol.help_fight_open             -- 助战位是否开启 （标记位1开启 0未开启）
	self.choose_wing_skill_id = protocol.choose_wing_skill_id   -- 已经选择的翅膀技能id   暂时无用
	self.bianshen_seq = protocol.bianshen_seq                   -- 变身机甲的id(主战位)   机甲唯一seq
	self.bianshen_end_time = protocol.bianshen_end_time         -- 变身机甲的结束时间戳 
    self.bianshen_start_time = protocol.bianshen_start_time

    self:CalMechaActiveState()
    self:CalFighterPlaneRemind()
    self:CalWingRemind()
    self:CalWeaponRemind()
    self:CalToFightRemind()
    self:CalMechaArmamentRemind()
    self:CalMechaBianShenCDReduce()
end

function MechaWGData:SetMechanPutonInfo(protocol)
    local mechan_seq = protocol.mechan_seq
    self.puton_list[mechan_seq] = protocol.part_puton
    self:CalToFightRemind()
    self:CalMechaArmamentRemind()
end

function MechaWGData:SetMechanPartStarInfo(protocol)
    local part_seq = protocol.part_seq
    self.part_star[part_seq] = protocol.star
    self:CalMechaActiveState(part_seq)
    self:CalFighterPlaneRemind()
    self:CalWingRemind()
    self:CalWeaponRemind()
    self:CalMechaArmamentRemind()
end

function MechaWGData:SetMechanMainFightInfo(protocol)
    self.bianshen_seq = protocol.bianshen_seq
    self:CalToFightRemind()
end

function MechaWGData:SetMechanHelpFightOpenInfo(protocol)
    self.help_fight_open = protocol.help_fight_open
    self:CalToFightRemind()
end

function MechaWGData:SetMechanHelpFightBattleInfo(protocol)
    local seq = protocol.seq
    local mechan_seq = protocol.mechan_seq
    self.help_fight_battle[seq] = mechan_seq
    self:CalToFightRemind()
    self:CalMechaBianShenCDReduce()
end

function MechaWGData:SetMechanChooseWingSkillInfo(protocol)
    self.choose_wing_skill_id = protocol.choose_wing_skill_id
end

function MechaWGData:SetMechanBianshenInfo(protocol)
    self.bianshen_seq = protocol.bianshen_seq
    self.bianshen_end_time = protocol.bianshen_end_time
    self.bianshen_start_time = protocol.bianshen_start_time
end
--------------------------------protocol_end--------------------------------

----------------------------------cal_start----------------------------------
-- 机甲激活状态缓存
function MechaWGData:CalMechaActiveState(seq)
    local function GetMechaActive(mecha_seq)
        local mecha_base_cfg = self:GetMechaCfgBySeq(mecha_seq)
        
        if not IsEmptyTable(mecha_base_cfg) then
            local base_part = self:GetMechaBasePartListByMechaSeq(mecha_seq)
            if not IsEmptyTable(base_part) then
                for k, v in pairs(base_part) do
                    local star = self:GetPartStarBySeq(v)

                    if star < 0 then
                        return false
                    end
                end

                return true
            end
        end

        return false
    end

    if nil == seq then
        local mecha_active_state = {}

        for k, v in pairs(self:GetMechaShowDataList()) do
            local mecha_seq = v.seq
            mecha_active_state[mecha_seq] = GetMechaActive(mecha_seq)
        end

        self.mecha_active_state = mecha_active_state
    else
        local part_cfg = self:GetPartCfgBySeq(seq)
        if not IsEmptyTable(part_cfg) then
            local mecha_seq = part_cfg.mechan_seq
            local mecha_active = GetMechaActive(mecha_seq)
            self.mecha_active_state[mecha_seq] = mecha_active
        end
    end
end

function MechaWGData:AddAttrInfo(mechan_seq, target_tab, attr_data, data_len)
    local attr_id, attr_value = 0, 0
    local data_len = data_len or 8

    target_tab[mechan_seq] = target_tab[mechan_seq] or {}

    for i = 1, data_len do
        attr_id = attr_data["attr_id" .. i]
        attr_value = attr_data["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
            target_tab[mechan_seq][attr_str] = target_tab[mechan_seq][attr_str] or 0
            target_tab[mechan_seq][attr_str] = target_tab[mechan_seq][attr_str] + attr_value
        end
    end
end

-- 战机界面红点
function MechaWGData:CalFighterPlaneRemind()
    local mfp_remind = false
    local mfp_cap_cache = {}
    local mfp_remind_tab = {}
    local mfp_cost_item_cache = {}
    local mfp_attr_info_data_cache = {}

    for k, v in pairs(self:GetMechaShowDataList()) do
        mfp_cap_cache[v.seq] = 0
        local part_remind_tab = {}
        local mecha_remind = false
        local is_mecha_active = self:IsMechaActive(k)
        self.mecha_active_part_data_cache[k] = self.mecha_active_part_data_cache[k] or {}

        if is_mecha_active then
            self:AddAttrInfo(v.seq, mfp_attr_info_data_cache, v)

            for i, u in pairs(self:GetMechaPartShowDataList(k)) do
                if i <= MECHA_PART_TYPE.RIGHT_FOOT then
                    local part_remind = false
                    local part_cell_remind_tab = {}
                    local mecha_active_part_data_cache = {}

                    for x, y in pairs(u) do
                        local remind = false
                        local is_max_star = self:IsPartIsMaxStar(y.seq)
                        local star =  self:GetPartStarBySeq(y.seq)

                        if star >= 0 then
                            table.insert(mecha_active_part_data_cache, y)
                            local sort_star_attr_info = self:GetPartUpStarCfg(y.seq, star)
                            self:AddAttrInfo(y.mechan_seq, mfp_attr_info_data_cache, sort_star_attr_info)
                        end

                        if not is_max_star then
                            star = star >= 0 and star or 0
                            local part_cell_cfg = self:GetPartUpStarCfg(y.seq, star)
    
                            if not IsEmptyTable(part_cell_cfg) then
                                local cost_item_id = part_cell_cfg.cost_item_id
                                local cost_item_num = part_cell_cfg.cost_item_num
                                local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
                                mfp_cost_item_cache[cost_item_id] = mfp_cost_item_cache
    
                                if has_num >= cost_item_num then
                                    remind = true
                                end
                            end
                        end
    
                        part_cell_remind_tab[y.sort] = remind

                        if remind then
                            part_remind = true
                            mecha_remind = true
                        end
                    end

                    part_remind_tab[i] = {}
                    part_remind_tab[i].remind = part_remind
                    part_remind_tab[i].part_cell_remind_tab = part_cell_remind_tab

                    self.mecha_active_part_data_cache[k][i] = mecha_active_part_data_cache
                end
            end
        else
            local active_cost = v.active_item
            local has_num = ItemWGData.Instance:GetItemNumInBagById(active_cost)
            mfp_cost_item_cache[active_cost] = active_cost

            if has_num > 0 then
                mecha_remind = true
            end
        end

        mfp_remind_tab[k] = {}
        mfp_remind_tab[k].remind = mecha_remind
        mfp_remind_tab[k].part_remind_tab = part_remind_tab

        if mecha_remind then
            mfp_remind = true
        end
    end

    self.mfp_remind = mfp_remind
    self.mfp_remind_tab = mfp_remind_tab
    self.mfp_cost_item_cache = mfp_cost_item_cache

    if not IsEmptyTable(mfp_attr_info_data_cache) then
        for k, v in pairs(mfp_attr_info_data_cache) do
            local base_attribute = AttributePool.AllocAttribute()
            base_attribute = base_attribute + v
            mfp_cap_cache[k] = AttributeMgr.GetCapability(base_attribute)
        end
    end

    self.mfp_attr_info_data_cache = mfp_attr_info_data_cache
    self.mfp_cap_cache = mfp_cap_cache
end

function MechaWGData:CalWingRemind()
    local mw_remind = false
    local mw_cap_cache = {}
    local mw_remind_tab = {}
    local mw_cost_item_cache = {}
    local mw_attr_info_data_cache = {}

    for k, v in pairs(self:GetMechaShowDataList()) do
        mw_cap_cache[v.seq] = 0
        local mecha_remind = false
        local part_remind_tab = {}

        self.mecha_active_part_data_cache[k] = self.mecha_active_part_data_cache[k] or {}

        for i, u in pairs(self:GetMechaPartShowDataList(k)) do
            if i > MECHA_PART_TYPE.RIGHT_FOOT and i <= MECHA_PART_TYPE.RIGHT_WING then
                local part_cell_remind_tab = {}
                local part_remind = false
                local mecha_active_part_data_cache = {}

                for x, y in pairs(u) do
                    local remind = false
                    local active_remind = false
                    local up_level_remind = false

                    local star = self:GetPartStarBySeq(y.seq)
                    local is_active = star >= 0
                    local part_cell_cfg = self:GetPartUpStarCfg(y.seq, star >= 0 and star or 0)

                    if not IsEmptyTable(part_cell_cfg) then
                        local cost_item_id = part_cell_cfg.cost_item_id
                        local cost_item_num = part_cell_cfg.cost_item_num
                        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

                        if is_active then
                            table.insert(mecha_active_part_data_cache, y)
                            local sort_star_attr_info = self:GetPartUpStarCfg(y.seq, star)
                            self:AddAttrInfo(y.mechan_seq, mw_attr_info_data_cache, sort_star_attr_info)

                            local is_max_star = self:IsPartIsMaxStar(y.seq)
                            if not is_max_star then
                                mw_cost_item_cache[cost_item_id] = cost_item_id
                                if has_num >= cost_item_num then
                                    up_level_remind = true
                                    remind = true
                                end
                            end
                        else
                            mw_cost_item_cache[cost_item_id] = cost_item_id
                            if has_num >= cost_item_num then
                                active_remind = true
                                remind = true
                            end
                        end
                    end

                    if remind then
                        part_remind = true
                        mecha_remind = true
                    end

                    part_cell_remind_tab[y.sort] = {}
                    part_cell_remind_tab[y.sort].remind = remind
                    part_cell_remind_tab[y.sort].active_remind = active_remind
                    part_cell_remind_tab[y.sort].up_level_remind = up_level_remind
                end
            
                part_remind_tab[i] = {}
                part_remind_tab[i].remind = part_remind
                part_remind_tab[i].part_cell_remind_tab = part_cell_remind_tab
                self.mecha_active_part_data_cache[k][i] = mecha_active_part_data_cache
            end
        end

        mw_remind_tab[k] = {}
        mw_remind_tab[k].remind = mecha_remind
        mw_remind_tab[k].part_remind_tab = part_remind_tab

        if mecha_remind then
            mw_remind = true
        end
    end

    self.mw_remind = mw_remind
    self.mw_remind_tab = mw_remind_tab
    self.mw_cost_item_cache = mw_cost_item_cache

    if not IsEmptyTable(mw_attr_info_data_cache) then
        for k, v in pairs(mw_attr_info_data_cache) do
            local base_attribute = AttributePool.AllocAttribute()
            base_attribute = base_attribute + v
            mw_cap_cache[k] = AttributeMgr.GetCapability(base_attribute)
        end
    end

    self.mw_attr_info_data_cache = mw_attr_info_data_cache
    self.mw_cap_cache = mw_cap_cache
end

function MechaWGData:CalWeaponRemind()
    local mwp_remind = false
    local mwp_cap_cache = {}
    local mwp_remind_tab = {}
    local mwp_cost_item_cache = {}
    local mwp_attr_info_data_cache = {}

    for k, v in pairs(self:GetMechaShowDataList()) do
        local mecha_remind = false
        local weapon_type_remind_tab = {}
        mwp_cap_cache[v.seq] = 0
        self.mecha_active_part_data_cache[k] = self.mecha_active_part_data_cache[k] or {}

        local part_data_list = self:GetMechaWeaponPartShowDataList(v.seq, MECHA_PART_TYPE.WEAPON)
        if not IsEmptyTable(part_data_list) then
            local mecha_active_part_data_cache = {}

            for i, u in pairs(part_data_list) do
                local weapon_type_cell_remind_tab = {}
                local weapon_type_cell_remind = false

                for x, y in pairs(u) do
                    local remind = false
                    local active_remind = false
                    local up_level_remind = false
                    
                    local star = self:GetPartStarBySeq(y.seq)
                    local is_active = star >= 0
                    local part_cell_cfg = self:GetPartUpStarCfg(y.seq, star >= 0 and star or 0)
                    if not IsEmptyTable(part_cell_cfg) then
                        local cost_item_id = part_cell_cfg.cost_item_id
                        local cost_item_num = part_cell_cfg.cost_item_num
                        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

                        if is_active then
                            table.insert(mecha_active_part_data_cache, y)
                            local sort_star_attr_info = self:GetPartUpStarCfg(y.seq, star)
                            self:AddAttrInfo(y.mechan_seq, mwp_attr_info_data_cache, sort_star_attr_info)

                            local is_max_star = self:IsPartIsMaxStar(y.seq)
                            if not is_max_star then
                                mwp_cost_item_cache[cost_item_id] = cost_item_id
                                if has_num >= cost_item_num then
                                    up_level_remind = true
                                    remind = true
                                end
                            end
                        else
                            mwp_cost_item_cache[cost_item_id] = cost_item_id
                            if has_num >= cost_item_num then
                                active_remind = true
                                remind = true
                            end
                        end
                    end

                    if remind then
                        weapon_type_cell_remind = true
                        mecha_remind = true
                    end

                    weapon_type_cell_remind_tab[y.sort] = {}
                    weapon_type_cell_remind_tab[y.sort].remind = remind
                    weapon_type_cell_remind_tab[y.sort].active_remind = active_remind
                    weapon_type_cell_remind_tab[y.sort].up_level_remind = up_level_remind
                end

                weapon_type_remind_tab[i] = {}
                weapon_type_remind_tab[i].remind = weapon_type_cell_remind
                weapon_type_remind_tab[i].weapon_type_cell_remind_tab = weapon_type_cell_remind_tab

                self.mecha_active_part_data_cache[k][MECHA_PART_TYPE.WEAPON] = mecha_active_part_data_cache
            end
        end

        mwp_remind_tab[k] = {}
        mwp_remind_tab[k].remind = mecha_remind
        mwp_remind_tab[k].weapon_type_remind_tab = weapon_type_remind_tab

        if mecha_remind then
            mwp_remind = true
        end
    end

    self.mwp_remind = mwp_remind
    self.mwp_remind_tab = mwp_remind_tab
    self.mwp_cost_item_cache = mwp_cost_item_cache

    if not IsEmptyTable(mwp_attr_info_data_cache) then
        for k, v in pairs(mwp_attr_info_data_cache) do
            local base_attribute = AttributePool.AllocAttribute()
            base_attribute = base_attribute + v
            mwp_cap_cache[k] = AttributeMgr.GetCapability(base_attribute)
        end
    end

    self.mwp_attr_info_data_cache = mwp_attr_info_data_cache
    self.mwp_cap_cache = mwp_cap_cache
end

function MechaWGData:CalToFightRemind()
    local mtf_remind = false
    local mtf_main_fight_remind = false
    local mtf_help_fight_remind_tab = {}
    local mtf_cost_item_cache = {}

    local function IsMechaCanToFighter(mecha_seq)
        if self:GetBianShenSeq() == mecha_seq then
            return false
        end

        for k, v in pairs(self:GetHelpFightBattleInfo()) do
            if mecha_seq == v then
                return false
            end
        end

        if self:IsMechaActive(mecha_seq) then
            if self:IsMechaActiveWeapon(mecha_seq) then
                return true
            end
        end
        
        return false
    end

    local bianshen_seq = self:GetBianShenSeq()

    if bianshen_seq < 0 then
        for k, v in pairs(self:GetMechaShowDataList()) do
            if IsMechaCanToFighter(v.seq) then
                mtf_main_fight_remind = true
                mtf_remind = true
                break
            end
        end
    end

    for k, v in pairs(self:GetHelpFightBattleInfo()) do
        local remind = false
        local seq = k
        local is_open = self:GetHelpFightIsOpen(seq)
        if is_open then
            for k, v in pairs(self:GetMechaShowDataList()) do
                if IsMechaCanToFighter(v.seq) then
                    remind = true
                    break
                end
            end
        else
            local open_cfg = self:GetHelpFightOpenCfgBySeq(seq)
            local role_level = RoleWGData.Instance:GetAttr('level')
            local level_enough = role_level >= open_cfg.role_level
            local cost_enough = false
            if open_cfg.consume_id > 0 and open_cfg.consume_num > 0 then
                mtf_cost_item_cache[open_cfg.consume_id] = open_cfg.consume_id
                local has_num = ItemWGData.Instance:GetItemNumInBagById(open_cfg.consume_id)
                cost_enough = has_num >= open_cfg.consume_num
            else
                cost_enough = true
            end

            if level_enough and cost_enough then
                remind = true
            end
        end

        if remind then
            mtf_remind = true
        end

        mtf_help_fight_remind_tab[k] = remind
    end

    self.mtf_remind = mtf_remind
    self.mtf_main_fight_remind = mtf_main_fight_remind
    self.mtf_help_fight_remind_tab = mtf_help_fight_remind_tab
    self.mtf_cost_item_cache = mtf_cost_item_cache
end

function MechaWGData:CalAttrInfo(data, data_len)
	local attr_list = {}
	local attr_id, attr_value = 0, 0
	local data_len = data_len or 8

	for i = 1, data_len do
		attr_id = data["attr_id" .. i]
		attr_value = data["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			attr_list[attr_str] = attr_value
		end
	end

	return attr_list
end

function MechaWGData:CalMechaArmamentRemind()
    local remind = false
    local marm_remind_tab = {}

    for k, v in pairs(self:GetMechaShowDataList()) do
        local mecha_remind = false

        local is_wear_complete = self:IsMechaWearComplete(k)
        if is_wear_complete then
            local wear_left_seq = self:GetMechaPartWearPart(k, MECHA_PART_TYPE.LEFT_WING)
            if wear_left_seq < 0 then
                local data_list = self:GetActivePartDataList(k, MECHA_PART_TYPE.LEFT_WING)

                if not IsEmptyTable(data_list) then
                    mecha_remind = true
                end
            end

            local wear_right_seq = self:GetMechaPartWearPart(k, MECHA_PART_TYPE.RIGHT_WING)
            if wear_right_seq < 0 then
                local data_list = self:GetActivePartDataList(k, MECHA_PART_TYPE.RIGHT_WING)

                if not IsEmptyTable(data_list) then
                    mecha_remind = true
                end
            end
        else
            local has_all_part = true
            for i, u in pairs(self:GetMechaPartShowDataList(k)) do
                if i ~= MECHA_PART_TYPE.LEFT_WING and i ~= MECHA_PART_TYPE.RIGHT_WING then
                    local data_list = self:GetActivePartDataList(k, i)
                    if IsEmptyTable(data_list) then
                        has_all_part = false
                        break
                    end
                end
            end

            if has_all_part then
                mecha_remind = true
            end
        end

        if mecha_remind then
            remind = true
        end

        marm_remind_tab[k] = mecha_remind
    end

    self.marm_remind = remind
    self.marm_remind_tab = marm_remind_tab
end

-- 特殊功能开启
function MechaWGData:GetMechaActIsOpen()
    local mecha_list = self:GetMechaShowDataList()

    if not IsEmptyTable(mecha_list) then
        for k, v in pairs(mecha_list) do
            if self:IsMechaActive(v.seq) then
                return true
            end

            local item_num = ItemWGData.Instance:GetItemNumInBagById(v.active_item)
            
            if item_num > 0 then
                return true
            end
        end
    end

	return false
end

function MechaWGData:CalMechaBianShenCDReduce()
    local cd = 0

    for k, v in pairs(self:GetHelpFightOpenCfg()) do
        local mecha_seq = self:GetHelpFightBattleSeq(v.seq) or -1

        if mecha_seq >= 0 then
            local mecha_data = self:GetHelpFightAttrDesc(mecha_seq)
            if not IsEmptyTable(mecha_data) then
                if mecha_data and mecha_data.bianshen_cd and mecha_data.bianshen_cd > 0 then
                    cd = cd + mecha_data.bianshen_cd
                end
            end
        end
    end
    
    self.mecha_cd_reduce = cd
end

function MechaWGData:GetHelpFightCDReduce()
    return self.mecha_cd_reduce
end
------------------------------------cal_end----------------------------------











--================================  主界面技能  ======================================
function MechaWGData:GetMainUISkillOrder(appearance_param_extend)
    local order_list = {}
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if not appearance_param_extend and main_role_vo.special_appearance ~= SPECIAL_APPEARANCE_TYPE.GUNDAM then
        return order_list, {1,1,1,1,1,1,1,1,1,1}
    end

    local param_extend = appearance_param_extend or main_role_vo.appearance_param_extend
    local weapon_seq = param_extend[MECHA_PART_TYPE.WEAPON]
    local wing_l_seq = param_extend[MECHA_PART_TYPE.LEFT_WING]
    local wing_r_seq = param_extend[MECHA_PART_TYPE.RIGHT_WING]
    local nor_skill_list = self:GetWeaponToNormalSkillInfo(weapon_seq)
    order_list[0] = nor_skill_list[1] or 0

    local weapon_skill_list = self:GetWeaponToSkillInfo(weapon_seq)
    for k,v in ipairs(weapon_skill_list) do
        table.insert(order_list, v)
    end

    local wing_skill_l = self:GetSKillIdBySeq(wing_l_seq)
    local wing_skill_r = self:GetSKillIdBySeq(wing_r_seq)
    if wing_skill_l and wing_skill_l ~= -1 and wing_skill_l == wing_skill_r then
        table.insert(order_list, wing_skill_l)
    end

    return order_list, {1,1,1,1,1,1,1,1,1,1}
end

function MechaWGData:GetMainUISkillList()
    local skill_list = {}
    local order_list = self:GetMainUISkillOrder()
    for i = 0, #order_list do
        local data = SkillWGData.Instance:GetJiJiaSkillConfig(order_list[i], 1)
        table.insert(skill_list, data)
    end

    return skill_list
end

function MechaWGData:GetSkillByAttackSeq(attack_index)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo.special_appearance ~= SPECIAL_APPEARANCE_TYPE.GUNDAM then
        return nil
    end

    local param_extend = main_role_vo.appearance_param_extend
    local weapon_seq = param_extend[MECHA_PART_TYPE.WEAPON]
    local nor_skill_list = self:GetWeaponToNormalSkillInfo(weapon_seq)
    return nor_skill_list[attack_index]
end