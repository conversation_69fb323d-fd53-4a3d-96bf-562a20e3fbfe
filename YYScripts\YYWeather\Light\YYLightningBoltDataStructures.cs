﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


/// <summary>
/// Camera modes
/// </summary>
public enum CameraMode
{
    /// <summary>
    /// Auto detect
    /// </summary>
    Auto,

    /// <summary>
    /// Force perspective camera lightning
    /// </summary>
    Perspective,

    /// <summary>
    /// Force orthographic XY lightning
    /// </summary>
    OrthographicXY,

    /// <summary>
    /// Force orthographic XZ lightning
    /// </summary>
    OrthographicXZ,

    /// <summary>
    /// Unknown camera mode (do not use)
    /// </summary>
    Unknown
}

public class YYLightningBoltDataStructures
{

	// Use this for initialization
	void Start () {
		
	}
	
	// Update is called once per frame
	void Update () {
		
	}
}
