FieldHeadPanel = FieldHeadPanel or BaseClass(SafeBaseView)

function FieldHeadPanel:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_field_head_panel")
	self.view_layer = UiLayer.MainUILow
	self.can_penetrate = true
	self.user_list = {}
	self.order = 1
	self.out_time = 0
	self.prepare_time = 0

	self.out_time_key = "field_head_out_time"
	self.prepare_time_key = "field_head_prepare_time"
	self.role_hp_list = {}
	for i = 1, 2 do
		self.role_hp_list[i] = {}
		self.role_hp_list[i].main_cur_hp = 0
		self.role_hp_list[i].main_max_hp = 0
	end

	self.is_set_data = false
	self.is_startimage_setfals = false
	self.send_over_pro = nil
end

function FieldHeadPanel:__delete()
	self.role_hp_list = nil
end

function FieldHeadPanel:ReleaseCallBack()
	Runner.Instance:RemoveRunObj(self)
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)

	self.is_startimage_setfals = nil
	self.send_over_pro = nil
	self.old_prepare_time = nil
end

function FieldHeadPanel:LoadCallBack()
	self.old_prepare_time = -1
	self:HideOverBtn()
	XUI.AddClickEventListener(self.node_list.over_btn, BindTool.Bind1(self.FinishThisFight, self))
	Runner.Instance:AddRunObj(self, 8)
	self.node_list.ph_timenum2:SetActive(false)
	self.node_list.layout_field_start:SetActive(false)--屏蔽垃圾倒计时.
end

function FieldHeadPanel:FinishThisFight()
	RobertManager.Instance:StopFight()
end

function FieldHeadPanel:HideOverBtn()
	if self.node_list.over_btn then
		self.node_list.over_btn:SetActive(false)
	end
end

function FieldHeadPanel:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
end

function FieldHeadPanel:OpenCallBack()
end

function FieldHeadPanel:ShowIndexCallBack()
end

function FieldHeadPanel:SetData(user_list)
	self.user_list = user_list
	if self.user_list then
		if nil == self.user_list[1] or nil == self.user_list[2] then
			return
		end

		if not self.is_set_data then
			self.is_set_data = true
			for k,v in pairs(self.user_list) do
				self.role_hp_list[k].main_cur_hp = v.hp
				self.role_hp_list[k].main_max_hp = v.max_hp
			end
		end

		for k,v in ipairs(self.user_list) do
			if v.hp == 0 then
				self.is_set_data = false
				break
			end
		end
	end

	self:Flush()
end

function FieldHeadPanel:FlushView()
	local main_role = GameVoManager.Instance:GetMainRoleVo()
	local list_role = Scene.Instance:GetRoleList()

	if self.user_list then
		for k, v in pairs(self.user_list) do
			if not v.level then
				return
			end

			local cap = v.capability
			local is_vis, limt_level = RoleWGData.Instance:GetDianFengLevel(v.level)
			limt_level = is_vis and limt_level or "Lv." .. limt_level
			if k == 1 then
				self.node_list.feixianlevel_imag:SetActive(is_vis)
				self.node_list["label_lv_1"]:SetActive(not is_vis)
				self.node_list["label_lv_feixian_1"].text.text = limt_level
				self.node_list["label_lv_1"].text.text = limt_level
			else
				self.node_list["label_lv_feixian_2"]:SetActive(is_vis)
				self.node_list["label_lv_2"]:SetActive(not is_vis)
				self.node_list["label_lv_feixian_2"].text.text = limt_level
				self.node_list["label_lv_2"].text.text = limt_level
			end

			self.node_list["label_name_" .. k].text.text = v.name
			self.node_list["ph_cap_" .. k].text.text = cap

			local role_id = v.role_id
			if IS_ON_CROSSSERVER and nil ~= v.origin_uid and v.origin_uid > 0 then
				role_id = v.origin_uid
				local is_self = role_id == RoleWGData.Instance:InCrossGetOriginUid()
				local server_id = is_self and RoleWGData.Instance:GetOriginServerId() or v.oppo_sever_id
				if is_self then
					local name = v.name .. "_s" .. server_id
					self.node_list["label_name_" .. k].text.text = name
				end
			end

			XUI.UpdateRoleHead(self.node_list["head_icon_" .. k],
				self.node_list["custom_head_icon_" .. k],
				role_id, v.sex, v.prof, false, true, true)
		end
	end
end

function FieldHeadPanel:OnFlush(param_list, index)
	for k, v in pairs(param_list) do
		if k == "out_time" then
			self:SetOutTime(v.time)
		elseif k == "prepare_time" then
			self:SetPrepareTime(v.time)
		elseif k == "star_img" then
			self:SetIsShowStartImg(v.is_show)
		end
	end

	self:FlushView()
end

function FieldHeadPanel:Update(now_time, elapse_time)
	if self.user_list then
		for k,v in pairs(self.user_list) do
			local role = Scene.Instance:GetObjectByObjId(v.obj_id)
			if role ~= nil then
			 	if role.vo.hp ~= self.role_hp_list[k].main_cur_hp or role.vo.max_hp ~= self.role_hp_list[k].main_max_hp then
			 		self.role_hp_list[k].main_cur_hp = role.vo.hp
			 		self.role_hp_list[k].main_max_hp = role.vo.max_hp
			 		self:RoleSliderChange(k, role.vo.hp, role.vo.max_hp)
			 	end
			end
		end
	end
end

function FieldHeadPanel:RoleSliderChange(index, cur_hp, max_hp)
	if self.node_list["role_slider_" .. index] then
		self.node_list["role_slider_" .. index].slider.value = cur_hp / max_hp
	end
end

-- 结束时间
function FieldHeadPanel:SetOutTime(time)
	self.out_time = time
	self.listen_out_flag = true
	local timer_func = BindTool.Bind1(self.OutTimerFunc, self)
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	if self.out_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown(self.out_time_key, timer_func, nil, self.out_time, nil, 1)
	end
end

function FieldHeadPanel:StopViewTimeCD()
	CountDownManager.Instance:RemoveCountDown(self.out_time_key)
	CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
	self.is_startimage_setfals = false
end

function FieldHeadPanel:OutTimerFunc()
	local time = math.floor(self.out_time - TimeWGCtrl.Instance:GetServerTime())
	if  time >= 0 and self.node_list.ph_timenum1 then
		self.node_list.ph_timenum1.text.text = time
	end
end

-- 准备时间
function FieldHeadPanel:SetPrepareTime(time)
	if not self:IsOpen() then
		return
	end

	if time and time > 0 then
		self.prepare_time = time
		self:SetPrepareTextActive(true)
		local timer_func = BindTool.Bind1(self.PrepareTimerFunc, self)

		if CountDownManager.Instance:HasCountDown(self.prepare_time_key) then
			CountDownManager.Instance:RemoveCountDown(self.prepare_time_key)
		end

		local total_time = self.prepare_time - TimeWGCtrl.Instance:GetServerTime()
		if total_time > 0 then
			CountDownManager.Instance:AddCountDown(self.prepare_time_key, timer_func,
			BindTool.Bind1(self.PrepareTimeCompleteCallback, self), nil, total_time, 0.5)
		end

		self.node_list.ph_timenum1.text.text = 60
	end
end

function FieldHeadPanel:PrepareTimeCompleteCallback()
	if not self.send_over_pro then
		self.send_over_pro = true
	end
	self.old_prepare_time = -1
end

function FieldHeadPanel:SetPrepareTextActive(state)
	if self.node_list.ph_timenum2 then
		self.node_list.ph_timenum2:SetActive(state)
	end
end

function FieldHeadPanel:PrepareTimerFunc(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if time > 0 then
		if self.old_prepare_time ~= time then
			self.old_prepare_time = time
			self.node_list.ph_timenum2.text.text = time
			self:DoTweenScaleContent(self.node_list.ph_timenum2_1)
		end
	else
		self.node_list.ph_timenum2_1:SetActive(false)
		if not self.is_startimage_setfals then
			-- self:DoTweenScaleContent(self.node_list.start_image)
			if self.node_list and self.node_list.start_image then
				self.node_list.start_image:SetActive(true)
			end
		end

		if not self.send_over_pro then
			self.send_over_pro = true
			self.is_startimage_setfals = true
			GlobalTimerQuest:AddDelayTimer(function ()
				if self.node_list and self.node_list.start_image ~= nil then
					self.node_list.start_image:SetActive(false)
				end
			end, 1)
		end
	end
end

function FieldHeadPanel:DoTweenScaleContent(node)
	local scale = Vector3(1, 1, 1)
	if node ~= nil then
		node:SetActive(true)
		node.rect.localScale = Vector3(2, 2, 2)
		node.rect:DOScale(scale, 0.3)
	end
end

function FieldHeadPanel:SetIsShowStartImg(is_show)
	if not self:IsLoaded() then
		return
	end

	if is_show then
		self.node_list.ph_timenum2:SetActive(false)
		self.node_list.ph_timenum2_1:SetActive(false)
		self.node_list.ph_timenum1:SetActive(true)
		self.node_list.mask_bg_one:SetActive(false)
	end
end
