------------------------------------------------------------
--人物简介View
------------------------------------------------------------
local CELL_COUNT = 2            -- 属性一行两个
function RoleView:InitIntroView()
	self.cell_list = {}
	self.list_view = self.node_list["list_view_arr"]
	self.spe_cell_list = {}
	self.spe_list_view = self.node_list["list_view_arr_spe"]

	local list_view_delegate = self.list_view.list_simple_delegate
    list_view_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.CreateArrListView, self)
	local spe_list_view_delegate = self.spe_list_view.list_simple_delegate
    spe_list_view_delegate.NumberOfCellsDel = BindTool.Bind(self.GetSpeNumberOfCells, self)
    spe_list_view_delegate.CellRefreshDel = BindTool.Bind(self.CreateSpeArrListView, self)

	XUI.AddClickEventListener(self.node_list["img_vip"], BindTool.Bind(self.OnClickVipCallBack, self))

	self.node_list["default_img_head"].button:AddClickListener(BindTool.Bind(self.OnChangeHeadHandler, self))
	self.node_list["btn_change_name"].button:AddClickListener(BindTool.Bind(self.OpenRenameView, self))
	self.node_list["btn_milu"].button:AddClickListener(BindTool.Bind(self.OnClickMiLu, self))
	self.node_list["btn_chenghao"].button:AddClickListener(BindTool.Bind(self.OnClickTitle, self))
	self.node_list["base_attr_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBaseAttrTip, self))
	self.node_list["spe_attr_btn"].button:AddClickListener(BindTool.Bind(self.OnClickSpeAttrTip, self))
	self.node_list["exp_addition_btn"].button:AddClickListener(BindTool.Bind(self.OnClickAddition, self))

	self.change_head_icon = GlobalEventSystem:Bind(OtherEventType.CHANGE_HEAD_ICON, BindTool.Bind(self.ChangeHeadIcon, self))
	self.change_model = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_APPERANCE_CHANGE, BindTool.Bind(self.FlushModel, self))

   	--FunOpen.Instance:RegisterFunUi(GuideModuleName.ChangeRolesView, self.node_list["btn_change_roles"])
	-- FunOpen.Instance:RegisterFunUi(GuideModuleName.SkyCurtain, self.node_list["btn_change_backgrounds"])	---检测功能开启
	-- 红点回调绑定
	self.jingjie_remind_callback = BindTool.Bind(self.JingJieRemindCallback, self)
	RemindManager.Instance:Bind(self.jingjie_remind_callback, RemindName.JingJie)					-- 境界
	RemindManager.Instance:Bind(self.jingjie_remind_callback, RemindName.SecretRecord)				-- 秘录
	RemindManager.Instance:Bind(self.jingjie_remind_callback, RemindName.Role_Head)
	RemindManager.Instance:Bind(self.jingjie_remind_callback, RemindName.JingMai)
	RemindManager.Instance:Bind(self.jingjie_remind_callback, RemindName.CustomAction)

	self.node_list.attr_toggle_1.toggle.isOn = true
	self:ChangeHeadIcon()
   self.world_level_change_event = GlobalEventSystem:Bind(OtherEventType.WORLD_LEVEL_CHANGE, BindTool.Bind(self.FlushWorldLevel, self))
end

function RoleView:ViewAnimation()
	-- local tween_info = UITween_CONSTS.RoleSys
	-- UITween.CleanAllTween(GuideModuleName.RoleView)
	-- UITween.FakeHideShow(self.node_list.intro_tween_1)
	-- UITween.FakeHideShow(self.node_list.intro_tween_2)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.intro_info_bg.rect, 300, 0)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.intro_btn_list.rect, -70, 61)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.intro_cap.rect, -189, -70)

	-- -- ReDelayCall(self, function()
	-- 	UITween.AlphaShow(GuideModuleName.RoleView,self.node_list.intro_tween_1, 0, tween_info.ToAlpha, tween_info.MoveTime, tween_info.AlphaShowType)
	-- 	self.node_list.intro_info_bg.rect:DOAnchorPos(Vector2(-142, 0), tween_info.MoveTime)
	-- 	self.node_list.intro_btn_list.rect:DOAnchorPos(Vector2(31, 61), tween_info.MoveTime)
	-- 	self.node_list.intro_cap.rect:DOAnchorPos(Vector2(-189, 6), tween_info.MoveTime)

	-- -- end, tween_info.MoveDelay, "intro_tween_1")

	-- ReDelayCall(self, function()
	-- 	UITween.AlphaShow(GuideModuleName.RoleView,self.node_list.intro_tween_2, 0, tween_info.ToAlpha, tween_info.AlphaTime)
	-- end, tween_info.AlphaDelay, "intro_tween_2")
end

function RoleView:FlushModel(appe_type)
	if not self:IsLoaded() then
		return
	end

	if appe_type ~= nil and not RoleWGData.IsNeedFlushModelAppeType(appe_type) then
		return
	end

	if nil == self.role_intro_model then
		local node = self.node_list.model_drag_event
		self.role_intro_model = RoleModel.New()
		self.role_intro_model:SetUISceneModel(node.event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		-- self.role_intro_model:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_intro_model, TabIndex.role_intro)
	end

	if self.role_intro_model and self.show_index == TabIndex.role_intro then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true}
		self.role_intro_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Rest)

		if role_vo.fabao_appeid ~= nil and role_vo.fabao_appeid > 0 then
			self.role_intro_model:SetBaoJuResid(role_vo.fabao_appeid)
		end

		self.role_intro_model:FixToOrthographicOnUIScene()
	end
end

function RoleView:OnClickBaseAttrTip()
	RuleTip.Instance:SetContent(Language.Role.BaseAttrTips, Language.Tip.CommonAttr)
end

function RoleView:OnClickSpeAttrTip()
	RuleTip.Instance:SetContent(Language.Role.SpeAttrTips, Language.Tip.SpecialAttr)
end

function RoleView:OnClickMiLu()
	ViewManager.Instance:Open(GuideModuleName.SecretRecordView)
end

function RoleView:OnClickTitle()
	RoleWGCtrl.Instance:OpenRoleTitleView()
end

function RoleView:DeleteIntroView()
	-- if FunOpen.Instance then
	-- 	FunOpen.Instance:UnRegsiterFunUi(GuideModuleName.ChangeRolesView)
	-- end

	if self.role_intro_model then
		self.role_intro_model:DeleteMe()
		self.role_intro_model = nil
	end

	if self.role_info_attri then
		self.role_info_attri:DeleteMe()
		self.role_info_attri = nil
	end

	if nil ~= self.role_attr_item_list then
		for k,v in pairs(self.role_attr_item_list) do
			v:DeleteMe()
		end
		self.role_attr_item_list = nil
	end

	if nil ~= self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end

	if nil ~= self.spe_cell_list then
		for k,v in pairs(self.spe_cell_list) do
			v:DeleteMe()
		end
		self.spe_cell_list = nil
	end

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	RemindManager.Instance:UnBind(self.jingjie_remind_callback)

	if self.change_head_icon then
		GlobalEventSystem:UnBind(self.change_head_icon)
		self.change_head_icon = nil
	end

	if self.world_level_change_event then
		GlobalEventSystem:UnBind(self.world_level_change_event)
		self.world_level_change_event = nil
	end

	if self.change_model then
		GlobalEventSystem:UnBind(self.change_model)
		self.change_model = nil
	end
	if nil ~= self.node_list.default_img_head then
		AvatarManager.Instance:CancelUpdateAvatar(self.node_list.default_img_head)
	end
	self.btn_head_effect = nil
	self.lbl_zhandoiuli = nil
	self.eff = nil
	self.head_gold_icon_remind = nil
	self.list_view = nil
	self.spe_list_view = nil
end

function RoleView:FluchRoleIntroViewAll()
	self:FlushRoleIntroView()

	--self.node_list["btn_change_backgrounds"]:SetActive(FunOpen.Instance:GetFunIsOpened(FunName.SkyCurtain) or BackgroundWGData:GetOneBackGroundIsActive())

	if self.list_view and not IsNil(self.list_view.scroller.ScrollRect) then
		self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
	end
	if self.spe_list_view and not IsNil(self.spe_list_view.scroller.ScrollRect) then
		self.spe_list_view.scroller:RefreshAndReloadActiveCellViews(true)
	end

	self:FlushModel()
	self:FlushRoleProfAndMarryInfo()
end

-- 创建属性List
function RoleView:CreateArrListView(cell,data_index)
    local attr_cell = self.cell_list[cell]
    if nil == attr_cell then
        attr_cell = RoleArrList.New(cell.gameObject)
        self.cell_list[cell] = attr_cell
    end

	attr_cell:SetIndex(data_index + 1)
    local attribute = RoleWGData.Instance:GetNewRoleAttr(1)
	for i = 1, CELL_COUNT do
		local index = data_index * CELL_COUNT + i
		attr_cell:SetArrData(i, attribute[index], true)
	end
end

function RoleView:GetSpeNumberOfCells()
    return math.ceil(RoleWGData.Instance:GetNewRoleAttrAmount(2) / CELL_COUNT)
end

-- 创建特殊属性List
function RoleView:CreateSpeArrListView(cell,data_index)
    local attr_cell = self.spe_cell_list[cell]

    if nil == attr_cell then
        attr_cell = RoleArrList.New(cell.gameObject)
        self.spe_cell_list[cell] = attr_cell
    end

	attr_cell:SetIndex(data_index + 1)
    local attribute = RoleWGData.Instance:GetNewRoleAttr(2)
	for i = 1, CELL_COUNT do
		local index = data_index * CELL_COUNT + i
		attr_cell:SetArrData(i, attribute[index])
	end
end

function RoleView:GetNumberOfCells()
    return math.ceil(RoleWGData.Instance:GetNewRoleAttrAmount(1) / CELL_COUNT)
end

function RoleView:ChangeHeadIcon()
	if not self:IsLoaded() then
		return
	end

	self:InitRoleHeadCell()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true

	self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_head_cell:SetData(data)
end

function RoleView:InitRoleHeadCell()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end
end

function RoleView:ChangeRoleAttrEvent(attr_name, value)
end

-- 角色基础信息
function RoleView:FlushRoleProfAndMarryInfo()
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if mainrolevo then
		local prof = mainrolevo.prof or 0
		local sex = mainrolevo.sex
		local prof_name = Language.Common.ProfName[sex][mainrolevo.prof]        						--职业
		local lover_name = mainrolevo.lover_name      												--伴侣名字
		if lover_name == "" then
			 lover_name = Language.Guild.SHWeiCanYu
		end
		local guild_Name = mainrolevo.guild_name   													-- 仙盟
		if guild_Name == "" then
			 guild_Name = Language.Guild.SHWeiCanYu
		end
		local server_id = mainrolevo.origin_server_id or 0   										--服务器id
		local server_name = LoginWGData.Instance:GetShowServerNameById(server_id) or Language.Guild.SHWeiCanYu
		self.node_list["value1"].text.text = prof_name
		self.node_list["value2"].text.text = guild_Name
		self.node_list["value3"].text.text = lover_name
		self.node_list["value4"].text.text = server_name
	end
end

function RoleView:FlushRoleZhandouli(role_vo)
	if self.node_list["zhan_dou_li"] then
		role_vo = role_vo or RoleWGData.Instance:GetRoleVo()
		self.node_list["zhan_dou_li"].text.text = role_vo.capability
	end
end

-- 角色信息
function RoleView:FlushRoleIntroView(attr_name, value)
	if not self:IsLoadedIndex(TabIndex.role_intro) then
		return
	end

	self.node_list.head_remind:SetActive(RemindManager.Instance:GetRemind(RemindName.Role_Head) > 0)

	local flag_red_num = RemindManager.Instance:GetRemind(RemindName.Role_Branch)
	self.node_list.chenghao_remind:SetActive(flag_red_num > 0)

	--local flag_red = BackgroundWGData.Instance:GetRemind()
	--self.node_list.background_remind:SetActive(flag_red == 1)

	local role_vo = RoleWGData.Instance:GetRoleVo()
	local is_vis,level = RoleWGData.Instance:GetDianFengLevel(role_vo.level)
	self.node_list["role_name"].text.text = role_vo.name
	self.node_list["dianfeng_img"]:SetActive(is_vis)
	self.node_list["level_txt"].text.text = level .. Language.Common.Ji

	self:FlushRoleZhandouli(role_vo)

	self.node_list.vip_num.text.text = string.format(Language.Role.VIPText, role_vo.vip_level)
	local cur_exp = RoleWGData.Instance.GetRoleExpCfgByLv(role_vo.level).exp
	if cur_exp ~= nil then
		self.node_list['expSlider'].slider.value = role_vo.exp/cur_exp
		self.node_list["num_exp"].text.text = ToColorStr(CommonDataManager.ConverExpByThousand(role_vo.exp), COLOR3B.C8)  .. " / " .. CommonDataManager.ConverExpByThousand(cur_exp)
	end
	
	if attr_name == "vip_level" then
		self.node_list["VipLevelText"].text.text = value
	end

	local value = SkillWGData.Instance:GetExpEfficiencyInfo()
	value = value and value.world_extra_percent or 0
	value = math.floor(value * 100 + 0.5)
	self.node_list.world_exp_add.text.text = string.format(Language.Role.WorldExpAdd, value .. "%")
	self:FlushWorldLevel()
end

function RoleView:FlushWorldLevel()
	if self:IsLoadedIndex(TabIndex.role_intro) then
		local get_world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
		local is_dianfeng,world_level = RoleWGData.Instance:GetDianFengLevel(get_world_level)
		if is_dianfeng then
			self.node_list.world_level_text.text.text = string.format(Language.Role.DianFeng,world_level)
		else
			self.node_list.world_level_text.text.text = world_level .. Language.Common.Ji
		end
	end
end

--更新属性值变化。
--注意为简化，属性名字与资源名字一一对应
function RoleView:FlushRoleAttrView(attr_name, value)
	if nil == self.role_attr_item_list then return end

	if "min_gong_ji" == attr_name then
		attr_name = "gong_ji"
	end

	for k,v in pairs(self.attr_t) do
		if attr_name == v then
			self.role_attr_item_list[k]:SetData(v)
		end
	end
end

function RoleView:UpdateExpExtraPer()
	-- if self.node_list.exp_txt then
	-- 	local per = RoleWGData.Instance:GetExpExtraPer()
	-- 	if per > 0 then
	-- 	else
	-- 	end
	-- end
end

function RoleView:SetCapability(num)

end

function RoleView:OnChangeHeadHandler()
	local is_open, tip = FunOpen.Instance:GetFunIsOpened(FunName.ChangeHeadView, true)
    if not is_open then
        local fun_cfg = FunOpen.Instance:GetFunByName(FunName.ChangeHeadView)
        if fun_cfg and fun_cfg.trigger_param then
            local lv_str = RoleWGData.GetLevelString(fun_cfg.trigger_param)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Role.HeadViewOpenLimit, lv_str))
		end
		return
	end

	RoleWGCtrl.Instance:OpenChangeHead()
end

function RoleView:OpenTeJie()
	local fun_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list.tejie
	local level = RoleWGData.Instance.role_vo.level
	if level >= fun_list.task_level then
		-- TeJieWGCtrl.Instance:Open()
		RoleWGCtrl.Instance:Close()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Dungeon.TipWuapon1)
	end
end

function RoleView:OpenRenameView()
	RoleWGCtrl.Instance:Open(nil, {sub_view_name = SubViewName.ReName})
end

function RoleView:OnClickVipCallBack()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_vip)
end

function RoleView:OnClickRoleEquipAttr(index)
	RoleWGCtrl.Instance:SetEquipTextData(index)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

function RoleView:OnClickAddition()
	ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end

function RoleView:JingJieRemindCallback(remind_name, num)
	if remind_name == RemindName.Role_Head and self.node_list.head_remind then
		self.node_list.head_remind:SetActive(num > 0)
	elseif remind_name == RemindName.SecretRecord and self.node_list.milu_remind then
		local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.SecretRecordView)
		self.node_list.milu_remind:SetActive(is_open and num > 0)
	-- elseif remind_name == RemindName.CustomAction and self.node_list.custom_action_remind then
	-- 	self.node_list.custom_action_remind:SetActive(num > 0)
	end
end

-- RecenAchievementItemRender
----------------------------------------------------------------------------
RoleArrList = RoleArrList or BaseClass(BaseRender)
function RoleArrList:SetIndex(index)
	self.index = index
	self.node_list.bg.image.enabled = false-- index % 2 == 0
end

function RoleArrList:SetArrData(i, data, is_normal)
	local attr_node = self.node_list["attr".. i]
	local value_node = self.node_list["value".. i]
	if not attr_node or not value_node then
		return
	end

	if data and data.name and data.cur_value then
		if is_normal then
			local num = tonumber(data.cur_value)
			local value, postfix_name = CommonDataManager.ConverPlayerPanelAttr(num)
			if postfix_name == "" then
				value_node.text.text = ToColorStr((string.format("%.0f", value)) .. postfix_name, COLOR3B.WHITE)
			else
				value_node.text.text = ToColorStr(value .. postfix_name, COLOR3B.WHITE)
			end
		else
			value_node.text.text = ToColorStr(data.cur_value, COLOR3B.WHITE)
		end

		attr_node.text.text = data.name
	else
		attr_node.text.text = ""
		value_node.text.text = ""
	end
end
