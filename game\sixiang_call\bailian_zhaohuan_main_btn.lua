BaiLianZhaoHuanMainBtn = BaiLianZhaoHuanMainBtn or BaseClass(BaseRender)

local bailian_zhaohuan_mainbtn_key = "bailian_zhaohuan_mainbtn_key"
function BaiLianZhaoHuanMainBtn:LoadCallBack()
end

function BaiLianZhaoHuanMainBtn:__delete()
    self:BLZHRemoveCountDown()
end

function BaiLianZhaoHuanMainBtn:OnFlush()
    --倒计时
    self:BLZHRemoveCountDown()
    local time
    if SiXiangTeDianWGData.Instance:GetIsOpenTeDian() then
        self.node_list["tedian_open"]:SetActive(true)
        time = SiXiangTeDianWGData.Instance:GetTeDianEndTimeStamp()
    else
        self.node_list["tedian_open"]:SetActive(false)
    end
    -- print_error(time, "四象特典开启状态:", SiXiangTeDianWGData.Instance:GetIsOpenTeDian())
    if time then
        local tedian_act_time = time - TimeWGCtrl.Instance:GetServerTime()
        if tedian_act_time > 0 then
            self:UpdateTime(0, tedian_act_time)
            CountDownManager.Instance:AddCountDown(bailian_zhaohuan_mainbtn_key, BindTool.Bind(self.UpdateTime, self),
                                                    BindTool.Bind(self.CompleteTime, self), nil, tedian_act_time, 1)
        end
    end
end

function BaiLianZhaoHuanMainBtn:UpdateTime(elapse_time, total_time)
    self:SetActTimeDes(TimeUtil.FormatSecondDHM5(total_time - elapse_time))
end

function BaiLianZhaoHuanMainBtn:CompleteTime()
    self:SetActTimeDes()
end

function BaiLianZhaoHuanMainBtn:SetActTimeDes(txt)
    if self.node_list and self.node_list["sc_call_time"] then
        self.node_list["sc_call_time"].text.text = txt or ""
    end
end

function BaiLianZhaoHuanMainBtn:BLZHRemoveCountDown()
    if CountDownManager.Instance:HasCountDown(bailian_zhaohuan_mainbtn_key) then
        CountDownManager.Instance:RemoveCountDown(bailian_zhaohuan_mainbtn_key)
    end
    self:SetActTimeDes()
end
