----------------------------------------------------
-- 狂嗨庆典
----------------------------------------------------
ActCrazyHighView = ActCrazyHighView or BaseClass(ActBaseViewTwo)

PATH_TYPE = {
	BIZUO = 1,
	ACT = 2,
}

function ActCrazyHighView:__init(act_id)
	-- self.parent = parent
	-- local ui_config = ConfigManager.Instance:GetUiConfig("act_subview_ui_cfg")
	-- local child_config = nil
	-- for k, v in pairs(ui_config) do
	-- 	if v.n == "layout_crazy_high" then
	-- 		child_config = v
	-- 		break
	-- 	end
	-- end
	-- self.act_id = act_id
	-- self:LoadView(parent, child_config)

	self.ui_config = {"uis/view/act_subview_ui_prefab","CrayHaiView"}
	self.config_tab = {
		{"layout_crazy_high", {0}},
	}
	self.open_tween = nil
	self.close_tween = nil
	self.act_id = act_id or ServerActClientId.RAND_ACTIVITY_TYPE_CRAZY_HIGH_CELEBRATION
end

function ActCrazyHighView:__delete()
	if self.task_list ~= nil then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	if self.reward_list ~= nil then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function ActCrazyHighView:ReleaseCallBack( )
	if self.task_list ~= nil then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	if self.reward_list ~= nil then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	if CountDownManager.Instance:HasCountDown("activity_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("activity_end_countdown")
	end
	self.has_load_callback = nil
end

function ActCrazyHighView:LoadCallBack()
	self.task_list = AsyncListView.New(CrazyHighTaskItemRender,self.node_list["ph_crazy_high_list"])
	self.reward_list = AsyncListView.New(CrazyHighItemRender, self.node_list["ph_crazy_high_list2"])
	self.has_load_callback = true
	self:RefreshView()
end

function ActCrazyHighView:RefreshView(param_list)
	if not self.has_load_callback then
		return
	end
	self:RefreshTopDesc()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CRAZY_HIGH_CELEBRATION)
	local task_data_list, reward_data_list = ServerActivityWGData.Instance:GetCrazyHighCfg()
	for i,v in ipairs(task_data_list) do
		local yet_count = info and info.cur_value_t.crazy_high_count_record_list[v.seq] or 0
		if yet_count >= v.allow_count then
			v.complete = 1
		else
			v.complete = 0
		end
	end

	table.sort(task_data_list, SortTools.KeyLowerSorters("complete", "seq"))
	self.task_list:SetDataList(task_data_list)

	for i,v in ipairs(reward_data_list) do
		if 1 == info.can_reward_flag[32 - (v.index - 1)] then
			v.is_lingqu = 1
		else
			v.is_lingqu = 0
		end
	end

	table.sort(reward_data_list, SortTools.KeyLowerSorters("is_lingqu", "exchange_high_count"))
	self.reward_list:SetDataList(reward_data_list)

	self.node_list.lbl_has_point.text.text = (string.format(Language.Activity.HasHighPoint, info.cur_value_t.high_count))

	if CountDownManager.Instance:HasCountDown("activity_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("activity_end_countdown")
	end
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
	if nil == open_act_cfg then
		return
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
	if nil == act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return
	end
	local time_left = act_info.next_time - TimeWGCtrl.Instance:GetServerTime()
	local act_open = open_act_cfg.open_type
	-- local act_status = ServerActivityWGData.Instance:GetVersionActivityNowStatus(act_open)
	-- time_left = act_status.next_status_time - TimeWGCtrl.Instance:GetServerTime()
	self:UpdateCountDownTime(0, time_left)
	CountDownManager.Instance:AddCountDown("activity_end_countdown", BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind1(self.CompleteCountDownTime, self), nil, time_left, 1)
end

function ActCrazyHighView:UpdateCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	local tip_str = ""--self.act_status.status == ACTIVITY_STATUS.CLOSE and Language.Activity.ActClosing or Language.Activity.ActPreparing
	if self.node_list["time"] then
		self.node_list["time"].text.text = (tip_str..TimeUtil.FormatSecond2DHMS(last_time))
	end
end

function ActCrazyHighView:CompleteCountDownTime(is_auto_fuhuo)
	-- self:RefreshView()
	self.node_list["layout_title_bg"]:SetActive(false)
end

--override
function ActCrazyHighView:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
		end
		self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = open_act_cfg.top_desc
	end
end

-------------------------------------------------------------------
------------------         itemRender           -------------------
-------------------------------------------------------------------
CrazyHighItemRender = CrazyHighItemRender or BaseClass(BaseRender)
function CrazyHighItemRender:__init()
	self.item_list = {}
	for i=1,4 do
		local item_cell = ItemCell.New(self.node_list["ph_cell_" .. i])
		self.item_list[i] = item_cell
	end

	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind1(self.OnClickLingQu, self))
end

function CrazyHighItemRender:__delete()
	for i,v in ipairs(self.item_list) do
		if v then
			v:DeleteMe()
			v = nil
		end
	end

	self.item_list = {}
end

function CrazyHighItemRender:LoadCallBack()

end

function CrazyHighItemRender:OnFlush()
	if nil == self.data then return end
	self.node_list.btn_lingqu:SetActive(true)
	self.node_list["wei_da_cheng"]:SetActive(false)
	self.node_list["yi_lingqu"]:SetActive(false)
	local item_data = self.data.exchange_item
	for i=1,4 do
		if item_data[i - 1] then
			self.item_list[i]:SetData(item_data[i - 1])
			self.item_list[i]:SetActive(true)
		else
			self.item_list[i]:SetActive(false)
		end
	end
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CRAZY_HIGH_CELEBRATION)

	if self.data.exchange_high_count > info.cur_value_t.high_count then
		-- self.node_list.img_no_reward:SetActive(true)
		-- self.node_list.img_yes_reward:SetActive(false)

		self.node_list.btn_lingqu:SetActive(false)

		self.node_list["wei_da_cheng"]:SetActive(true)
		--self.node_list["btn_lingqu_text"].text.text = "未达到"
	elseif self.data.exchange_high_count <= info.cur_value_t.high_count then
		-- self.node_list.img_no_reward:SetActive(false)
		-- self.node_list.img_yes_reward:SetActive(false)

		self.node_list.btn_lingqu:SetActive(true)
		self.node_list["wei_da_cheng"]:SetActive(false)

		--self.node_list["btn_lingqu_text"].text.text = "领  取"
	end

	if 1 == info.can_reward_flag[32 - (self.data.index - 1)] then
		-- self.node_list.img_no_reward:SetActive(false)
		-- self.node_list.img_yes_reward:SetActive(true)

		-- self.node_list.btn_lingqu:SetActive(false)
		self.node_list["btn_lingqu_text"].text.text = "已领取"
		self.node_list["yi_lingqu"]:SetActive(true)
		self.node_list.btn_lingqu:SetActive(false)
	end
	self.node_list.lbl_desc.text.text = (string.format(Language.Activity.HighPointDesc, self.data.exchange_high_count))
end

--override
function CrazyHighItemRender:CreateSelectEffect()

end

function CrazyHighItemRender:OnClickLingQu()
	local param_t ={}
	param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_CELEBRATION,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD,
		param_1 = self.data.index - 1
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end

-------------------
CrazyHighTaskItemRender = CrazyHighTaskItemRender or BaseClass(BaseRender)
function CrazyHighTaskItemRender:__init()
	XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind1(self.OnClickGo, self))
end

function CrazyHighTaskItemRender:__delete()

end

function CrazyHighTaskItemRender:LoadCallBack()

end

function CrazyHighTaskItemRender:OnFlush()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CRAZY_HIGH_CELEBRATION)
	local yet_count = info.cur_value_t.crazy_high_count_record_list[self.data.seq]
	local str = string.format(Language.Activity.HighPointDesc2, self.data.description, self.data.reward_high_count)--, yet_count, self.data.allow_count
	self.node_list["lbl_desc"].text.text = str
	self.node_list["count"].text.text = yet_count .. "/" .. self.data.allow_count
	XUI.SetButtonEnabled(self.node_list.btn_go, yet_count < self.data.allow_count)
	self.node_list.btn_text.text.text = yet_count < self.data.allow_count and "前  往" or "已完成"
	local bundle,asset = "", ""
	if self.data.path_type == PATH_TYPE.BIZUO then
		bundle, asset = ResPath.GetBiZuo(self.data.icon_id)
	elseif self.data.path_type == PATH_TYPE.ACT then
		bundle, asset = ResPath.GetActIvityHall(self.data.icon_id)
	end
 	self.node_list["icon"].image:LoadSprite(bundle,asset,function()
 		XUI.ImageSetNativeSize(self.node_list["icon"])
 	end)

end

--override
function CrazyHighTaskItemRender:CreateSelectEffect()

end

function CrazyHighTaskItemRender:OnClickGo()
	if 20 == self.data.seq then
		ServerActivityWGCtrl.Instance:ChangeToView(ACTIVITY_TYPE.ACT_GOBAL_XUNBAO,function()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongHaiWeiKaiShi)
		end)
	elseif 23 == self.data.seq then
		-- print_error("全民鉴宝")
		local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_NATIONAL_TREASURE)
		if not is_open then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NotOpenAct)
		else
			ViewManager.Instance:Open(GuideModuleName.TreasureAct)
		end
	elseif 12 == self.data.seq then
		local guild_name = RoleWGData.Instance.role_vo.guild_name
		if guild_name and guild_name ~= "" then
			FunOpen.Instance:OpenViewNameByCfg(self.data.open_param)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.NotEnterGuildAnswer)
		end
	else
		FunOpen.Instance:OpenViewNameByCfg(self.data.open_param)
	end
end


function CrazyHighTaskItemRender:OnClickCallback()
	ServerActivityWGCtrl.Instance:CloseActBanbenServerView()
end
