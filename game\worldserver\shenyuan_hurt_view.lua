ShenyuanBossHurtView = ShenyuanBossHurtView or BaseClass(SafeBaseView)

function ShenyuanBossHurtView:__init()
	self.default_index = 1
	self.active_close = false
	-- self.view_layer = UiLayer.MainUILow
	self.view_name = "<PERSON><PERSON>BossHurtView"
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_shenyuan_hurt_info")
	self.view_cache_time = 0
	self.open_tween = nil
	self.close_tween = nil
    self.next_call_time = 0
    self.is_safe_area_adapter = true
end

function ShenyuanBossHurtView:__delete()
end

-- function ShenyuanBossHurtView:HideOrShowBossTip(is_on)
--     is_on = MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn()
--     if self.is_show_on ~= is_on then
--         self.is_show_on = is_on
--         if is_on then
--             self.node_list["tip_image"].rect:DOAnchorPosX(400, 0.4)
--             self.node_list["tip_image"].canvas_group:DoAlpha(1, 0, 0.4)
--         else
--             self.node_list["tip_image"].rect:DOAnchorPosX(0, 0.4)
--             self.node_list["tip_image"].canvas_group:DoAlpha(0, 1, 0.4)
--         end
--     end
-- end

function ShenyuanBossHurtView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
    end
    if self.country_rank_list then
		self.country_rank_list:DeleteMe()
		self.country_rank_list = nil
    end
    self.next_call_time = 0

    if self.next_call_timer then
		GlobalTimerQuest:CancelQuest(self.next_call_timer)
		self.next_call_timer = nil
    end
    
    -- if self.main_top_arrow_click_event then
    --     GlobalEventSystem:UnBind(self.main_top_arrow_click_event)
    --     self.main_top_arrow_click_event = nil
    -- end
    -- self.is_show_on = false
end

function ShenyuanBossHurtView:ShowIndexCallBack()
    local init_callback = function ()
        self:Init()
    end
    local mainuictrl = MainuiWGCtrl.Instance
    mainuictrl:AddInitCallBack(nil,init_callback)
    self:Flush()
end

function ShenyuanBossHurtView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_qiangduo, BindTool.Bind1(self.OnClickQiangduo, self))
    XUI.AddClickEventListener(self.node_list.boss_count_add, BindTool.Bind1(self.OnClickAddShenyuan, self))
    -- self.main_top_arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK,BindTool.Bind(self.HideOrShowBossTip,self))
    self:InitList()
    self:CreateBossRankList()
    self:OnClickQiangduo() --进入场景主动去选择怪物
    self.node_list.tip_txt.tmp.text = Language.Boss.ShenYuanTipDes
end

function ShenyuanBossHurtView:OnClickAddShenyuan()
    BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.vat_shenyuanboss_buy_times)
end

function ShenyuanBossHurtView:InitList()
    self.rank_list = StrengthenAsyncListView.New(ShenyuanHurtRender, self.node_list.TaskList1)
end

function ShenyuanBossHurtView:CreateBossRankList()
    self.country_rank_list = AsyncListView.New(ShenYuanCountryHurtItem, self.node_list.country_hurt_list)
end

function ShenyuanBossHurtView:Init()
	local mainui_ctrl = MainuiWGCtrl.Instance

	local parent = mainui_ctrl:GetTaskOtherContent()
	self.node_list.task_root_view.transform:SetParent(parent.transform)
	self.node_list.task_root_view.transform.anchoredPosition = Vector3(0, 0, 0)
	self.node_list.task_root_view.transform.localScale = Vector3(1, 1, 1)

	-- mainui_ctrl:SetTaskPanel(false,151,-136.1, true)
	-- mainui_ctrl.view:SetTaskCallBack(function (ison)
	-- 	if self.node_list.task_root_view then
	-- 		self.node_list.task_root_view:SetActive(ison)
	-- 	end

    -- end)
    mainui_ctrl:SetTaskContents(false)
    mainui_ctrl:SetOtherContents(true)
    -- self:HideOrShowBossTip()
end

function ShenyuanBossHurtView:CloseCallBack()
    if self.node_list.task_root_view then
        self.node_list.task_root_view.transform:SetParent(self.root_node_transform, false)
    end
end

function ShenyuanBossHurtView:OnClickQiangduo()
    local scene_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x,role_y = role:GetLogicPos()
    local boss_data = BossWGData.Instance:GetShenYuanBossInfoBySceneId(scene_id)
    if IsEmptyTable(boss_data) then
        return
    end
    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic and scene_logic.GetPickedObj then
        local pick_obj = scene_logic:GetPickedObj()
        if pick_obj then
            scene_logic:DoGatherPickObj()
            return
        end
    end

    local pos = Split(boss_data.boss_pos, ",") 
    if role_x == tonumber(pos[1]) and role_y == tonumber(pos[2]) then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:SetGuaJiInfoPos(tonumber(pos[1]), tonumber(pos[2]), nil, boss_data.boss_id)
        end
    else
    	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
        local obj = Scene.Instance:GetMonstObjByMonstID(boss_data.boss_id)
        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
            GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic ~= nil then
                scene_logic:SetGuaJiInfoPos(tonumber(pos[1]), tonumber(pos[2]), nil, boss_data.boss_id)
            end
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end)
        MoveCache.SetEndType(MoveEndType.FightByMonsterId)
        GuajiCache.monster_id = boss_data.boss_id
        MoveCache.param1 = boss_data.boss_id
        local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
        if obj ~= nil and not obj:IsDeleted() then
            GuajiWGCtrl.Instance:MoveToObj(obj, range)
        else
            GuajiWGCtrl.Instance:MoveToPos(scene_id, tonumber(pos[1]), tonumber(pos[2]), range)
        end
    end
end

function ShenyuanBossHurtView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if "all" == k then
            self:FlushLeftHurt()
            self:FlushRightHurt()
        elseif "person_damage" == k then
            self:FlushLeftHurt()
        elseif "country_damage" == k then
            self:FlushRightHurt()
        end
    end
end

function ShenyuanBossHurtView:FlushRightHurt()
    local country_boss_list = BossWGData.Instance:GetShenYuanCountryHurtInfo()
    if self.country_rank_list ~= nil then
        self.country_rank_list:SetDataList(country_boss_list)
    else
        self:CreateBossRankList()
        self.country_rank_list:SetDataList(country_boss_list)
    end
    self.node_list.country_empty:SetActive(IsEmptyTable(country_boss_list))
end

function ShenyuanBossHurtView:FlushLeftHurt()
    self.node_list.des_text_info.text.text = Language.Boss.ShenYuanBossLeftDes
    local other_cfg = BossWGData.Instance:ShenYuanBossOtherCfg()
    local join_time, max_join_time = BossWGData.Instance:GetShenYuanTimesInfo()
    local color = join_time >= max_join_time and COLOR3B.D_RED or COLOR3B.WHITE
    local cur_time = max_join_time - join_time >= 0 and max_join_time - join_time or 0
    local str = ToColorStr(string.format("%d/%d",cur_time, max_join_time), color)
    self.node_list.boss_count_per.slider.value = cur_time / max_join_time
    self.node_list.boss_count_text.text.text = str

	local all_hurt_info = BossWGData.Instance:GetShenYuanHurtInfo()
    self.node_list.tips:SetActive(IsEmptyTable(all_hurt_info))

    if self.rank_list ~= nil then
        self.rank_list:SetDataList(all_hurt_info, 3)
    else
        self:InitList()
        self.rank_list:SetDataList(all_hurt_info, 3)
    end

	local main_role_hurt = BossWGData.Instance:GetShenYuanSingleHurtInfo().hurt
	self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(main_role_hurt)

    self.node_list.per_bg.slider.value = main_role_hurt / BossWGData.Instance:GetShenYuanlHurtInfoMaxValue()
    local index = BossWGData.Instance:GetShenYuanSingleHurtInfo().rank_count
    if main_role_hurt == 0 then
        -- self.node_list["my_rank_icon"]:SetActive(false)
        self.node_list.my_rank_txt.text.text = ""
    else
        -- if index < 4 then
        --     self.node_list["my_rank_icon"]:SetActive(true)
        --     self.node_list["my_rank_icon"].image:LoadSprite(ResPath.GetCommonImages("icon_paiming" .. index))
        --     self.node_list.my_rank_txt.text.text = ""
        -- else
        --     self.node_list["my_rank_icon"]:SetActive(false)
            self.node_list.my_rank_txt.text.text = index
        -- end
    end
end
--------------------------------------------------------------------------------
ShenyuanHurtRender = ShenyuanHurtRender or BaseClass(BaseRender)

function ShenyuanHurtRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.attk, BindTool.Bind(self.OnClickAttk, self))
    -- XUI.AddClickEventListener(self.node_list.flag_img, BindTool.Bind(self.OnClickFlag, self))
    XUI.AddClickEventListener(self.node_list.btn_bg, BindTool.Bind(self.OnClickAttackBG, self))
end

function ShenyuanHurtRender:OnClickAttk()
    if nil == self.data then return end
    if self.data.is_online == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotOnLine)
        return
    end
    local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
    if obj then
        local is_enemy, str = Scene.Instance:IsEnemy(obj)
        if is_enemy then
            local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
            GuajiWGCtrl.Instance:CancelSelect()
            GuajiWGCtrl.Instance:StopGuaji()
            GuajiWGCtrl.Instance:ClearTemporary()
            MoveCache.SetEndType(MoveEndType.AttackTarget)
            GuajiCache.target_obj = obj
            GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
            if guaji_state then
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            end
        else
            SysMsgWGCtrl.Instance:ErrorRemind(str)
        end
    else --没找到目标，不在视野范围内
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotInHorizon)
    end
end

function ShenyuanHurtRender:OnClickAttackBG()
    local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
    local role_id = self.data.uuid.temp_low
    if role_id == main_role_id then
        return
    end
    local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
    local is_enemy = false
    if obj then
        is_enemy = Scene.Instance:IsEnemy(obj)
    end

    if is_enemy then
        self:OnClickAttk()
        return
    end
    local my_server_id = RoleWGData.Instance:GetOriginServerId()
    local my_plat_type = RoleWGData.Instance:GetPlatType()
    local is_cross = my_server_id ~= self.data.server_id or my_plat_type ~= self.data.plat_type

    local member_count = SocietyWGData.Instance:GetTeamMemberCount()
    local str_tip, oprate
    local items = {
        Language.Menu.ShowInfo,
        Language.Menu.GiveFlower,
        --Language.Menu.Profess, 
    }
    if not is_cross then
        if SocietyWGData.Instance:GetIsMyFriend(role_id) then
            table.insert(items, Language.Menu.PrivateChat)
        else
            table.insert(items, Language.Menu.AddFriend)
        end
    end
    if not is_cross then
        if self.data.team_index <= 0 and member_count >= 0 then
            table.insert(items, Language.Menu.InviteTeam)
        elseif self.data.team_index > 0 and member_count <= 0 then
            table.insert(items, Language.Menu.ApplyTeam)
        elseif self.data.team_index > 0 and member_count > 0 then
            table.insert(items, Language.Menu.MergeTeam)
        end
    end

    if items == nil then
        return
    end
    local team_index = self.data.team_index
    BrowseWGCtrl.Instance:ShowOtherRoleInfo(role_id, self:GetPos(self.node_list["btn_bg"]), is_cross, self.data.plat_type, items, 
    BindTool.Bind(self.MenuClickCallBack, self, is_cross, team_index, items))
end

function ShenyuanHurtRender:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
    if nil == main_view or not main_view:IsOpen() then
        return
    end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
    local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x + 320
    y = local_position_tbl.y
    return Vector2(x, y)
end

function ShenyuanHurtRender:MenuClickCallBack(is_cross, team_index, items, index, sender, param, item_data)
    local menu_text = items[index]
    if menu_text == Language.Menu.ShowInfo then
        local role_id = param.role_id
        BrowseWGCtrl.Instance:OpenWithUid(role_id,nil,nil,param.plat_type,1)
    elseif  menu_text == Language.Menu.GiveFlower then
        if not is_cross then
            FlowerWGCtrl.Instance:OpenSendFlowerView(param.role_id, param.role_name, param.sex, param.prof)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
        end
    -- elseif menu_text == Language.Menu.Profess then
    --     if not is_cross then
    --         local is_friend = SocietyWGData.Instance:CheckIsFriend(param.role_id)
    --         if is_friend then
    --             ProfessWallWGData.Instance:SetDefaultInfo(param.role_id,nil)
    --             --ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
    --         else
    --             local data = {}
    --             data.role_id = param.role_id
    --             data.role_name = param.role_name
    --             data.sex = param.sex
    --             data.prof = param.prof
    --             SocietyWGCtrl.Instance:OpenAddTipsPanel(data)
    --         end
    --     else
    --         TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
    --     end
    elseif menu_text == Language.Menu.PrivateChat or menu_text == Language.Menu.AddFriend then
        if not is_cross then
            local role_id = param.role_id
            if SocietyWGData.Instance:GetIsMyFriend(role_id) then
                SocietyWGCtrl.Instance:Flush("find_role_id",{role_id})
            else
                SocietyWGCtrl.Instance:IAddFriend(param.role_id)
            end
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
        end
    elseif menu_text == Language.Menu.InviteTeam or menu_text == Language.Menu.ApplyTeam or menu_text == Language.Menu.MergeTeam then
        if item_data ~= nil and item_data[1] ~= nil and team_index ~= nil then
            local role_id = param.role_id
            if menu_text == Language.Menu.InviteTeam then
                if 0 == SocietyWGData.Instance:GetIsInTeam() then
                    local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
                    NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
                end
                NewTeamWGCtrl.Instance:SendInviteUser(role_id, 0, 1)
            elseif menu_text == Language.Menu.ApplyTeam and team_index > 0 then
                SocietyWGCtrl.Instance:SendReqJoinTeam(team_index)
            else
                NewTeamWGCtrl.Instance:SendTeamMergeReq(role_id)
            end    
        end   
    end
end


function ShenyuanHurtRender:OnClickFlag()
    if self.index <= 3 then
        -- BossWGCtrl.Instance:ShowShenYuanHurtFlag(self.index)
        BossWGCtrl.Instance:SetShenYuanFlagIndex(self.index)
        BossWGCtrl.Instance:OpenShenYuanHurtFlag()
    end
end

function ShenyuanHurtRender:OnFlush()
    if not self.data then
        return
    end
    
    local name = self.data.role_name
    self.node_list.name.text.text = name
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.name.rect)
	local damage = CommonDataManager.ConverNumber(self.data.hurt)
	self.node_list.damage.text.text = damage
    self.node_list.per_bg.slider.value = self.data.hurt / BossWGData.Instance:GetShenYuanlHurtInfoMaxValue()
    
    self.node_list.num:SetActive(true)
	-- if self.index < 4 then
	-- 	self.node_list["icon"]:SetActive(true)
	-- 	self.node_list["icon"].image:LoadSprite(ResPath.GetCommonImages("icon_paiming" .. self.index))
	-- 	self.node_list.num.text.text = ""
	-- else
	-- 	self.node_list["icon"]:SetActive(false)
		self.node_list.num.text.text = self.index
	-- end
    
    self:FlushBtnState()
    if self.index <= 2 then
        local bundle, asset = ResPath.GetMainUIIcon("a3_zjm_damage_rank" .. self.index)
        self.node_list.flag_img.image:LoadSprite(bundle, asset, function ()
            self.node_list.flag_img.image:SetNativeSize()
        end)
        self.node_list.flag_img:SetActive(true)
    else
        self.node_list.flag_img:SetActive(false)
    end
end

function ShenyuanHurtRender:FlushBtnState()
    local is_show = true
    local cur_obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
    local main_role = Scene.Instance:GetMainRole()
    local attack_mode = main_role:GetVo().attack_mode
    if cur_obj then
        local lover_id = main_role:GetVo().lover_uid or 0
        local is_lover = lover_id ~= 0 and lover_id == cur_obj:GetVo().role_id
        if is_lover and (attack_mode == ATTACK_MODE.GUILD or attack_mode == ATTACK_MODE.PEACE) then
           is_show = false
        end
        local is_team = SocietyWGData.Instance:IsTeamMember(cur_obj:GetOriginId())
        if is_team then
            is_show = false
         end
    end
	self.node_list["attk"]:SetActive(is_show and not self.data.is_mine)
end

function ShenyuanHurtRender:OnSelectChange(is_select)
	self.node_list["high"]:SetActive(is_select)
end

function ShenyuanHurtRender:ReleaseCallBack()

end



ShenyuanBossFlagTipView = ShenyuanBossFlagTipView or BaseClass(SafeBaseView)

function ShenyuanBossFlagTipView:__init()
    self.view_name = "ShenyuanBossFlagTipView"
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_shenyuan_flag_tip")

    self.view_layer = UiLayer.Pop
    self.is_any_click_close = true
    self.callback_func = nil                        -- 点击回调
    self.callback_param = nil                       -- 点击回调参数
    self.close_callback_func = nil                  -- 关闭回调
end

function ShenyuanBossFlagTipView:__delete()
end

function ShenyuanBossFlagTipView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.close_tip_btn, BindTool.Bind1(self.Close, self))
end

function ShenyuanBossFlagTipView:OnFlush()
    local index = BossWGCtrl.Instance:GetShenYuanFlagIndex()
    self.node_list.flag_tip_text.text.text = Language.WorldServer.PercentWinDesColor[index]
    self.node_list.flag_tip.rect.anchoredPosition = Vector2(420, 200 - ((index - 1) * 10))
end


------------------------------------------------ShenYuanCountryHurtItem--------------------------------------------------

ShenYuanCountryHurtItem = ShenYuanCountryHurtItem or BaseClass(BaseRender)
function ShenYuanCountryHurtItem:OnFlush()
    if not self.data then return end
    self.view:SetActive(true)

    self.node_list["rank_num"].text.text = self.index >= 4 and self.index or ""
    local plat_type = self.data.plat_type
	local server_id = self.data.server_id
    self.node_list["country_name"].text.text = server_id..Language.Login.Fu
    self.node_list["hurt_num"].tmp.text = CommonDataManager.ConverExpExtend(self.data.country_hurt)

    local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

    if self.index < 4 then
        bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["rank_icon"]:SetActive(true)
		self.node_list["rank_icon"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
	else
		self.node_list["rank_icon"]:SetActive(false)
    end

    self.node_list["fill"].image:LoadSprite(bg_bundle, bg_asset)

	-- self.node_list["rank_num"].tmp.text = self.index
	-- local plat_type = self.data.plat_type
	-- local server_id = self.data.server_id
	-- self.node_list["country_name"].tmp.text = server_id..Language.Login.Fu
    -- self.node_list.slider.slider.value = self.data.country_hurt / BossWGData.Instance:GetShenYuanlCountryHurtInfoMaxValue()
	-- self.node_list["hurt_num"].tmp.text = CommonDataManager.ConverExpExtend(self.data.country_hurt)
end
