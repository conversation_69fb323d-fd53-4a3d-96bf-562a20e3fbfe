-------------------------------------------
--角色背景（奇境）数据
--------------------------------------------
BackgroundWGData = BackgroundWGData or BaseClass()

function BackgroundWGData:__init()
	if BackgroundWGData.Instance then
		ErrorLog("[BackgroundWGData] Attemp to create a singleton twice !")
	end
	BackgroundWGData.Instance = self
	self:InitParam()
    self:InitConfig()
    self:RegisterBackgroundRemindInBag()
	self:InitToggleList()
	-- 注册红点涉及的对应物品(仅限人物背包，其他功能的特殊背包除外)，当物品改变时会对相应红点进行Fire()
	RemindManager.Instance:Register(RemindName.Role_Background,BindTool.Bind(self.GetRemind,self))
end

function BackgroundWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.Role_Background)
	BackgroundWGData.Instance = nil
	---配置表
	self.auto_cfg = nil 			---总配置表
	self.back_cfg = nil				---背景配置表
	self.back_item_map_cfg = nil	---背景物品配置表
	self.back_lv_map_cfg = nil		---背景等级配置表
	self.back_lv_stuff_cfg = nil	---背景等级材料配置表
	---本地数据
	self.background_list = nil		---所有的背景信息(本地)
	self.curr_use_back_id = 0		---当前选择的背景id(服务器)
end

-- 默认参数
function BackgroundWGData:InitParam()
	---配置表
	self.auto_cfg = nil 			---总配置表
	self.back_cfg = nil				---背景配置表
	self.back_item_map_cfg = nil	---背景物品配置表
	self.back_lv_map_cfg = nil		---背景等级配置表
	self.back_lv_stuff_cfg = nil	---背景等级材料配置表
	---本地数据
	self.background_list = nil		---所有的背景信息(本地)
	self.curr_use_back_id = 0		---当前选择的背景id(服务器)
end

-- 初始化配置
function BackgroundWGData:InitConfig()
	self.auto_cfg = ConfigManager.Instance:GetAutoConfig("back_cfg_auto")	---总配置表
	self.back_cfg = self.auto_cfg.back
	self.back_item_map_cfg = ListToMap(self.auto_cfg.back, "item_id")
	self.back_lv_map_cfg = ListToMap(self.auto_cfg.back_level, "background_id", "background_level")
	self.back_lv_stuff_cfg = ListToMap(self.auto_cfg.back_level, "stuff_id")
end

---注册背包物品变化红点信息
function BackgroundWGData:RegisterBackgroundRemindInBag()
	if (not self.back_cfg) or (not self.back_lv_stuff_cfg) then
		print_error(" BackgroundWGData RegisterBackgroundRemindInBag error")
		return
	end

	local item_id_list = {}
    for item_id, _ in pairs(self.back_item_map_cfg) do
        table.insert(item_id_list, item_id)
    end

    for item_id, _ in pairs(self.back_lv_stuff_cfg) do
    	table.insert(item_id_list, item_id)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.Role_Background, item_id_list, nil)
end

function BackgroundWGData:CheckIsBackgroundItemId(item_id)
	if (not self.back_cfg) or (not self.back_lv_stuff_cfg) then
		print_error(" BackgroundWGData RegisterBackgroundRemindInBag error")
		return false
	end

    for item_id, _ in pairs(self.back_item_map_cfg) do
        if item_id == item_id then
			return true
		end
    end

    for item_id, _ in pairs(self.back_lv_stuff_cfg) do
        if item_id == item_id then
			return true
		end
    end

	return false
end

-- 初始化列表
function BackgroundWGData:InitToggleList()
	if not self.back_cfg then
		print_error("BackgroundWGData InitToggleList error")
		return
	end

	self.background_list = {}
	for _, cfg_data in ipairs(self.back_cfg) do
		if cfg_data then
			local sub_data = self:InitSubToggleData(cfg_data)
			self.background_list[sub_data.background_id] = sub_data
		end
	end
end

---设置小类型数据
function BackgroundWGData:InitSubToggleData(data)
	local sub_data = {}
	sub_data.background_id = data.background_id
	sub_data.name = data.name
	sub_data.desc = data.desc
	sub_data.item_id = data.item_id
	sub_data.is_remind = false
	sub_data.lock = true
	sub_data.put_on_flag = false
	sub_data.level = 0
	return sub_data
end

---设置服务器数据
function BackgroundWGData:InitServerDta(server_data)
	self.curr_use_back_id = server_data.use_back_id
	for _, data in ipairs(server_data.back_item_list) do
		self:RefreshDataByID(data.back_id, data, true)
	end

	self:RefreshSubRemind()
end

---检测是否存在这个背景
function BackgroundWGData:CheckHaveBackground(list, background_id)
	for _, data in ipairs(list) do
		if data and data.background_id == background_id then
			return true
		end
	end
	return false
end

---排序一下
function BackgroundWGData:SortForBackId(list)
	if list then
		table.sort(list, function (a, b)
			return a.background_id < b.background_id
		end)
	end
end

---设置新背景激活
function BackgroundWGData:ActiveNewBackInfo(server_data)
	local data = {}
	data.back_id = server_data.active_new_back_id
	data.level = 0
	self:RefreshDataByID(data.back_id, data, false)
end

---设置背景使用
function BackgroundWGData:RefreshUseBack(server_data)
	local curr_back_id = self.curr_use_back_id
	self.curr_use_back_id = server_data.use_back_id
	---卸下当前
	local data = {}
	data.put_on_flag = false
	self:RefreshDataByID(curr_back_id, data, false)
	---穿戴服务器给到的
	data.put_on_flag = true
	self:RefreshDataByID(self.curr_use_back_id, data, false)
end

---刷新背景升星
function BackgroundWGData:RefreshUpLevel(background_id, up_level)
	local data = {}
	data.back_id = background_id
	data.level = up_level
	self:RefreshDataByID(data.back_id, data, true)
end

---更新一个背景属性
function BackgroundWGData:RefreshDataByID(background_id, data, is_refresh_red)
	if not self.background_list then
		return nil
	end
	if not self.background_list[background_id] then
		return nil
	end
	local sub_data = self.background_list[background_id]
	if sub_data then
		sub_data.level = data.level and data.level or sub_data.level
		sub_data.lock = false
		sub_data.put_on_flag = background_id == self.curr_use_back_id
		---刷新一下红点
		if is_refresh_red then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(sub_data.item_id)
			local next_level_data = BackgroundWGData.Instance:GetBackgroundLevelData(sub_data.background_id, sub_data.level, true)
			if not next_level_data then   ---满星
				sub_data.is_remind = false
			else
				sub_data.is_remind = (item_num >= next_level_data.stuff_num)
			end
		end
	end
	return sub_data
end

---更新一下所有一级的红点
function BackgroundWGData:RefreshSubRemind()
	if not self.background_list then
		return
	end
	for _, sub_data in ipairs(self.background_list) do
		if sub_data then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(sub_data.item_id)
			local next_level_data = BackgroundWGData.Instance:GetBackgroundLevelData(sub_data.background_id, sub_data.level, true)
			if not next_level_data then   ---满星
				sub_data.is_remind = false
			else
				sub_data.is_remind = (item_num >= next_level_data.stuff_num)
			end
		end
	end
end

-- 红点方法
function BackgroundWGData:GetRemind()
	-- 开启判断
	if (not FunOpen.Instance:GetFunIsOpened(FunName.RoleBgView)) and (not BackgroundWGData:GetOneBackGroundIsActive()) then
		return 0
	end
	if not self.background_list then
		return 0
	end
	self:RefreshSubRemind()
	for _, sub_data in ipairs(self.background_list) do
		if sub_data and sub_data.is_remind then
			return 1
		end
	end
	return 0
end


-- 红点方法
function BackgroundWGData:GetOneBackGroundIsActive()
	-- 开启判断
	if not self.background_list then
		return false
	end

	for _, sub_data in ipairs(self.background_list) do
		if sub_data and sub_data.item_id then
			local level_data = BackgroundWGData.Instance:GetBackgroundLevelData(sub_data.background_id, sub_data.level, false)
			local item_num = ItemWGData.Instance:GetItemNumInBagById(sub_data.item_id)
			if level_data then
				if not sub_data.lock then
					return true
				elseif item_num >= level_data.stuff_num then
					return true
				end
			end
		end
	end

	return false
end

---获取一个背景的战力
function BackgroundWGData:GetBackgroundAttrCap(background_id, level)
	local cap = 0
	if not self.back_lv_map_cfg then
		return cap
	end
	local background_data = self.back_lv_map_cfg[background_id][level]
	if not background_data then
		return cap
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end
	for i = 1, 6 do
		local attr_id = background_data["attr_id" .. i] or 0
		local attr_value = background_data["attr_value" .. i] or 0
		if attr_id > 0 and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			add_tab(attr_str, attr_value)
		end
	end
	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

---获取所有已激活背景的属性
function BackgroundWGData:ChechkHaveBackgroundAttr()
	if not self.background_list then
		return false
	end

	for _, background_data in ipairs(self.background_list) do
		if not background_data.lock then
			return true
		end
	end

	return false
end


---获取所有已激活背景的属性
function BackgroundWGData:GetHaveBackgroundAttr()
	if not self:ChechkHaveBackgroundAttr() then
		return nil
	end

	local all_attr_list = {}
	for _, attr in ipairs(self.background_list) do
		if not attr.lock then
			local attr_data, curr_table = self:GetBackgroundAttr(attr.background_id, attr.level)

			if curr_table and (not IsEmptyTable(curr_table)) then
				for _, attr_data in pairs(curr_table) do
					if attr_data and attr_data.attr_str then
						if not all_attr_list[attr_data.attr_str] then
							all_attr_list[attr_data.attr_str] = attr_data
						else
							all_attr_list[attr_data.attr_str].attr_value = all_attr_list[attr_data.attr_str].attr_value + attr_data.attr_value
						end
					end
				end
			end
		end
	end

	local return_attr_data = {}
	for _, attr_data in pairs(all_attr_list) do
		if attr_data and attr_data.attr_str then
			table.insert(return_attr_data, attr_data)
		end
	end

	if not IsEmptyTable(return_attr_data) then
		table.sort(return_attr_data, function (a, b)
			return a.attr_str < b.attr_str
		end)
	end
	return return_attr_data
end

---获取一个背景的属性配置根据物品id
function BackgroundWGData:GetBackgroundCfgByItemId(item_id)
	if not self.back_item_map_cfg then
		return nil
	end
	if not self.back_item_map_cfg[item_id] then
		return nil
	end
	if not self.back_lv_map_cfg then
		return nil
	end

	local data = self.back_item_map_cfg[item_id]
	if self.back_lv_map_cfg[data.background_id] and self.back_lv_map_cfg[data.background_id][0] then
		return self.back_lv_map_cfg[data.background_id][0]
	end
	return nil
end

---获取一个背景的所有属性
function BackgroundWGData:GetBackgroundAttr(background_id, cur_level)
	local background_data = self.back_lv_map_cfg[background_id]
	if not background_data then
		return nil
	end

	local next_lv = cur_level + 1
	local cur_cfg = background_data[cur_level]
	local next_cfg = background_data[next_lv]
	local cur_table = {}

	for i = 1, 6 do
		local key = cur_cfg["attr_id" .. i]
		local value = cur_cfg["attr_value" .. i]

		if key and key ~= 0 then
			cur_table[key] = {}
			cur_table[key].attr_str = key
			cur_table[key].attr_value = cur_cfg["attr_value" .. i]

			if next_cfg and next_cfg[key] ~= 0 then
				cur_table[key].add_value = next_cfg["attr_value" .. i] - cur_table[key].attr_value
			end
		end
	end

	local return_data = {}
	for _, attr_data in pairs(cur_table) do
		if attr_data and attr_data.attr_str and (attr_data.attr_value ~= 0 or attr_data.add_value ~= 0) then
			table.insert(return_data, attr_data)
		end
	end
	
	table.sort(return_data, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_data, cur_table
end

---获取一个背景当前或下一级的数据
function BackgroundWGData:GetBackgroundLevelData(background_id, cur_level, is_next)
	if not self.back_lv_map_cfg then
		return nil
	end
	local background_data = self.back_lv_map_cfg[background_id]
	if not background_data then
		return nil
	end
	local next_level = cur_level
	if is_next then
		next_level = cur_level + 1
	end
	if background_data[next_level] then
		return background_data[next_level]
	end
	return nil
end

---根据背景id获取一个背景配置
function BackgroundWGData:GetBigDataByID(background_id)
	return self.back_cfg[background_id]
end

---获取背景数据根据id
function BackgroundWGData:GetBackDataByID(background_id)
	local EmptyTable = {}
	return (self.background_list or EmptyTable)[background_id] or nil
end

---当前背景是否激活
function BackgroundWGData:IsActivation(background_id)
	local background_data = self:GetBackDataByID(background_id)
	if background_data then
		return (not background_data.lock)
	end
	return false
end

-- 获取背景列表
function BackgroundWGData:GetBackgroundList()
	return self.background_list
end