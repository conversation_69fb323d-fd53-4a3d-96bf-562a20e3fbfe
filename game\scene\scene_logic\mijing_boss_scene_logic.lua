MiJingBossSceneLogic = MiJingBossSceneLogic or BaseClass(CommonFbLogic)

function MiJingBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function MiJingBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function MiJingBossSceneLogic:Enter( old_scene_type, new_scene_type )
	-- if IS_ON_CROSSSERVER then
		BossWGCtrl.Instance:SendMiJingBossReq(BossView.ReqType.ALLINFO)
	-- end
	-- GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	BossWGCtrl.Instance:ResetLightSelectBoss()

	-- BossWGCtrl.Instance:EnterSceneCallback()
	BossWGCtrl.Instance:Close()
	-- BossWGCtrl.Instance:OpenSceneLogicView()
	BossWGCtrl.Instance:RefershBossStone()
	BossWGCtrl.Instance:OpenMJBossList()
	self:InitGoToPos()
	-- local main_role = Scene.Instance:GetMainRole()
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.GUILD)
	BossWGCtrl.Instance:SetRoleTiredIcon(DAY_COUNT.DAYCOUNT_ID_MJ_BOSS_TIRE)
	-- local select_obj_list = Scene.Instance:GetRoleList()
	-- local obj_select = nil
	-- for _,v in pairs(select_obj_list) do
	-- 	obj_select = v
	-- 	break
	-- end
	-- if obj_select then
	-- 	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj_select, SceneTargetSelectType.SELECT)
	-- end
end

function MiJingBossSceneLogic:InitGoToPos()
	local sence_id = Scene.Instance:GetSceneId()
	local type1, layer, boss_id = BossWGData.Instance:GetCurSelectBossID()
	if boss_id == GameEnum.BossAssist then
		BossWGCtrl.Instance:MoveToTargetPos()
	else
		local list_data = BossWGData.Instance:GetCrossMJBossByLayer(layer)
		if list_data == nil then return end
		local data = {}
		for k,v in pairs(list_data) do
			if v.boss_id == boss_id then
				data = v
				break
			end
		end
		if IsEmptyTable(data) then return end
	    GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
	        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	    end)

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = boss_id
		MoveCache.param1 = boss_id
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, data.x_pos, data.y_pos, range)
	end
end

function MiJingBossSceneLogic:OnObjCreate(obj)
	if obj and not SceneObj.select_obj and self:IsEnemy(obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
end

function MiJingBossSceneLogic:Out()
	CommonFbLogic.Out(self)
	BossWGCtrl.Instance:SendMiJingBossReq(BossView.ReqType.ALLINFO)
	BossWGCtrl.Instance:CloseMJBossList()

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end
end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function MiJingBossSceneLogic:GetGuajiCharacter()
	local target_obj
	target_obj = self:GetNormalRole()
	if target_obj ~= nil then
		return target_obj
	end
	if target_obj == nil then
		return self:GetMonster()
	end
end

function MiJingBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			GuajiCache.target_obj = v
			return v
		end
	end
end

function MiJingBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function MiJingBossSceneLogic:IsMonsterEnemy( target_obj, main_role )
	local info = BossWGData.Instance:GetSecretBossInfoByBossId(target_obj:GetVo().monster_id)
	local physical = BossWGData.Instance:GetNowOwnPower()
	if physical < info.kill_consume_physical then
		return false, Language.Boss.NoEnoughPower
	end
	return true
end

function MiJingBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function MiJingBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

-- 此场景优先保证单位数量
function MiJingBossSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function MiJingBossSceneLogic:IsEnemyVisiblePriortiy()
	return true
end
