
--------------------------------------------------
--310任务做完后退出场景引导魔王
--------------------------------------------------
FunMoWangView = FunMoWangView or BaseClass(SafeBaseView)
local PROGRESS_TIME = 4
local AUTO_CUE_TIME = 9 --自动点击
function FunMoWangView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/guide_ui_prefab", "layout_guide_mowang")
	self.view_layer = UiLayer.Guide
end

function FunMoWangView:__delete()
end

function FunMoWangView:ReleaseCallBack()
    self.is_star = nil
    if self.progress_tween then
        self.progress_tween:Kill()
        self.progress_tween = nil
    end
    --暂时留着
    -- if self.tween_progress_rotate then
    --     self.tween_progress_rotate:Kill()
    --     self.tween_progress_rotate = nil
    -- end

    if  self.btn_tween then
		self.btn_tween:Kill()
		self.btn_tween = nil
    end

    if  self.bg_tween then
		self.bg_tween:Kill()
		self.bg_tween = nil
    end

    if  self.tween then
		self.tween:Kill()
		self.tween = nil
    end

    if CountDownManager.Instance:HasCountDown("close_autocue_countdown") then
		CountDownManager.Instance:RemoveCountDown("close_autocue_countdown")
	end
end

function FunMoWangView:LoadCallBack()
    self.node_list["bg"]:SetActive(true)
    self.node_list["bg_open"]:SetActive(false)

    ViewManager.Instance:Close(GuideModuleName.TaskDialog)
    FunctionGuide.Instance:SetOpenFunGuide(false)
    self.node_list["btn_no_lingqu"]:SetActive(true)
    self.node_list["ke_ling"]:SetActive(false)
	-- local btn = self.node_list["btn_keling"]:GetComponent(typeof(EventTriggerListener))
	-- btn:AddPointerDownListener(BindTool.Bind(self.OnClickStar, self))
    -- btn:AddPointerUpListener(BindTool.Bind(self.OnClickEnd, self))
    self.node_list["btn_no_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickNoLingQu, self))
    local end_time = TimeWGCtrl.Instance:GetServerTime() + AUTO_CUE_TIME
    -- self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
    CountDownManager.Instance:AddCountDown("close_autocue_countdown", nil, BindTool.Bind1(self.EndTimeCallBack, self), end_time, nil, 1)
end

function FunMoWangView:ShowIndexCallBack()
     FunctionGuide.Instance:SetOpenFunGuide(false)
end

function FunMoWangView:SetData(task_id,reason)
    self.task_id = task_id
    self.reason = reason
end

function FunMoWangView:OnClickStar()
    if self.is_star then
        return
    end
    self.is_star = true
    self.node_list["fuzhou_effect"].image.enabled = false

    local call_back = function()
        self.node_list["fuzhou_effect"]:SetActive(false)
         if  self.btn_tween then
            self.btn_tween:Kill()
            self.btn_tween = nil
        end
        self:PlayNextAni()
    end

    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_fuzhirongjie)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["fuzhou_effect"].transform, 4, nil, nil, nil, nil, call_back)
    
    --另一种处理方式(进度条) 暂时保留
    -- self.progress_tween = self.node_list["keling_progress"].slider:DOValue(1, PROGRESS_TIME):OnComplete(function ( )
    --     self:PlayNextAni()
    -- end)
    -- self.progress_tween:SetEase(DG.Tweening.Ease.Linear)
    -- self.node_list["hight_light"].transform.rotation = Vector3(0, 0, 0)
    -- self.tween_progress_rotate = self.node_list["hight_light"].transform:DORotate(
    --         Vector3(0, 0, 360),
    --         PROGRESS_TIME,
    --         DG.Tweening.RotateMode.FastBeyond360)
    -- self.tween_progress_rotate:SetEase(DG.Tweening.Ease.Linear)

end

function FunMoWangView:EndTimeCallBack()
    self:OnClickNoLingQu()
end

function FunMoWangView:PlayNextAni()
    if not self.is_star then
        return
    end

    local bg_tween = function (trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 90),0.3):SetEase(DG.Tweening.Ease.OutQuint))
		return sequence
    end
    
    if self.bg_tween then
        self.bg_tween:Kill()
        self.bg_tween = nil
    end


    self.bg_tween = bg_tween(self.node_list.bg.transform):SetLoops(1)
    self.bg_tween:OnComplete(function ()
        self.node_list["bg"]:SetActive(false)
        self.node_list["bg_open"]:SetActive(true)

        self.tween = self.node_list["bg_open"].transform:DOSizeDelta(Vector2(216, 300), 0.5)
		self.tween:SetEase(DG.Tweening.Ease.OutQuint)
		self.tween:OnComplete(function ()
			self:OnClickFinish()
		end)
    end)
end

function FunMoWangView:OnClickFinish()
    if not self.is_finish then
        self.is_finish = true
    end
     FunctionGuide.Instance:SetOpenFunGuide(true)
    FunctionGuide.Instance:SetMoWangGuideFlag(true)
    local task_id = self.task_id or 0
    local reason = self.reason or 0
    FunOpen.Instance:OnOneTaskDataChange(task_id, reason)
    self:Close()
end

function FunMoWangView:OnClickNoLingQu()
    self.node_list['btn_no_lingqu']:SetActive(false)
    self.node_list['ke_ling']:SetActive(true)
    self:OnClickStar()
    --播放动画

    if self.btn_tween then
		self.btn_tween:Kill()
		self.btn_tween = nil
    end

    local btn_tween = function (trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 15),0.05):SetEase(DG.Tweening.Ease.OutQuint))
        sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -15),0.1):SetEase(DG.Tweening.Ease.OutQuint))
        sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 15),0.1):SetEase(DG.Tweening.Ease.OutQuint))
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -15),0.1):SetEase(DG.Tweening.Ease.OutQuint))
		sequence:Append(trans:DOLocalRotate(Vector3(0,0,0),0.05):SetEase(DG.Tweening.Ease.OutQuint))
		sequence:AppendInterval(0.5)
		return sequence
	end
    self.btn_tween = btn_tween(self.node_list.bg.transform):SetLoops(1000)

end

function FunMoWangView:EndTimeCallBack()
    self:OnClickNoLingQu()
end
--防止界面被其他东西顶掉造成影响
function FunMoWangView:CloseCallBack()
    if self.is_finish then
        return
    end
    FunctionGuide.Instance:SetOpenFunGuide(true)
    FunctionGuide.Instance:SetMoWangGuideFlag(true)
    local task_id = self.task_id or 0
    local reason = self.reason or 0
    FunOpen.Instance:OnOneTaskDataChange(task_id, reason)
    self.is_star = false
end



