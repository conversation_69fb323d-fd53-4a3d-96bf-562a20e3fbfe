﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ShrinkButtonWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ShrinkButton), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("ClearTween", ClearTween);
		<PERSON><PERSON>unction("CheckRedPoint", CheckRedPoint);
		<PERSON><PERSON>unction("GetRedPoint", GetRedPoint);
		L<PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearTween(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ShrinkButton obj = (ShrinkButton)ToLua.CheckObject<ShrinkButton>(L, 1);
			obj.ClearTween();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CheckRedPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ShrinkButton obj = (ShrinkButton)ToLua.CheckObject<ShrinkButton>(L, 1);
			obj.CheckRedPoint();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetRedPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ShrinkButton obj = (ShrinkButton)ToLua.CheckObject<ShrinkButton>(L, 1);
			int o = obj.GetRedPoint();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

