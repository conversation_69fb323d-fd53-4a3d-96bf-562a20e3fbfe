TeamManyTowerSceneLogic = TeamManyTowerSceneLogic or BaseClass(CommonFbLogic)

function TeamManyTowerSceneLogic:__init()

end

function TeamManyTowerSceneLogic:__delete()
	
end

function TeamManyTowerSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	-- XuiBaseView.CloseAllView()

	-- ManyTowerWGCtrl.Instance:FbInfoOpen()
	-- ManyTowerWGCtrl.Instance:FbSkillOpen()


	MainuiWGCtrl.Instance:OnIconbarVisible({}, true,  true)
	self.release_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.release_timer = nil
		MainuiWGCtrl.Instance:OnSkillBarViewOpen()
		MainuiWGCtrl.Instance:OnIconbarVisible({""}, false,  true)
	end, 0.3)
end

function TeamManyTowerSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function TeamManyTowerSceneLogic:Out()
	CommonFbLogic.Out(self)

	FuBenWGData.Instance:SetTeamTowerAutoRefresh(true)
	-- ManyTowerWGCtrl.Instance:FbSkillClose()
	-- ManyTowerWGCtrl.Instance:FbInfoClose()


	MainuiWGCtrl.Instance:OnIconbarVisible({""}, true,  true)
	MainuiWGCtrl.Instance:OnSkillBarViewClose()

	-- self:RemoveAllSceneSkill()
end

function TeamManyTowerSceneLogic:RemoveAllSceneSkill()

end