CrossFlowerRankView = CrossFlowerRankView or BaseClass(SafeBaseView)

function CrossFlowerRankView:__init()
    self:SetMaskBg(false, true)
    self.view_style = ViewStyle.Full

    self:AddViewResource(0, "uis/view/cross_flower_rank_ui_prefab", "layout_cross_flower_rank")
    self.role_vo = GameVoManager.Instance:GetMainRoleVo()
end

function CrossFlowerRankView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_window_close, BindTool.Bind1(self.Close, self))
    XUI.AddClickEventListener(self.node_list.rule_tips, BindTool.Bind1(self.OpenRuleTipsView, self))
    XUI.AddClickEventListener(self.node_list.btn_send_flower, BindTool.Bind1(self.OpenFlowerView, self))

    self.node_list["man_rank_toggle"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self,
        CROSS_FLOWER_RANK_SEX_TYPE.MAN))
    self.node_list["woman_rank_toggle"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self,
        CROSS_FLOWER_RANK_SEX_TYPE.WOMAN))

    --self.my_reward_list = AsyncListView.New(ItemCell, self.node_list["my_reward_list"])
    self.flower_rank_list = AsyncListView.New(CrossFlowerRankItemRender, self.node_list["flower_rank_list"])
    self.flower_rank_list:SetCreateCellCallBack(BindTool.Bind(self.OnCreateCell, self))
    self:LoginTimeCountDown()

    self.rank_member_list = {}
    for i = 1, 3 do
        self.rank_member_list[i] = FlowerRankMemberItemRender.New(self.node_list["cross_flower_member" .. i])
    end
end

function CrossFlowerRankView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("cross_flower_rank_down") then
        CountDownManager.Instance:RemoveCountDown("cross_flower_rank_down")
    end

    if self.flower_rank_list then
        self.flower_rank_list:DeleteMe()
        self.flower_rank_list = nil
    end

    -- if self.my_reward_list then
    --     self.my_reward_list:DeleteMe()
    --     self.my_reward_list = nil
    -- end

    if self.rank_member_list then
        for i, v in ipairs(self.rank_member_list) do
            v:DeleteMe()
        end
        self.rank_member_list = nil
    end
end

function CrossFlowerRankView:OpenCallBack()
    CrossFlowerRankWGCtrl.Instance:SendCrossFlowerReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_WEEK_ADD_CHARM,
        self.role_vo.sex)
end

--tips弹窗
function CrossFlowerRankView:OpenRuleTipsView()
    RuleTip.Instance:SetContent(Language.CrossFlowerRank.RuleTipsContent, Language.CrossFlowerRank.RuleTipsTitle)
end

--前往送花
function CrossFlowerRankView:OpenFlowerView()
    ViewManager.Instance:Open(GuideModuleName.Flower)
end

function CrossFlowerRankView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "all" then
            self:FlushRankToggle()
        elseif k == "rank_list" then
            self:FlushRankInfo()
        end
    end
end

--选择回调
function CrossFlowerRankView:OnClickSwitch(index)
    self.node_list["my_rank_root"]:SetActive(index == self.role_vo.sex)
    CrossFlowerRankWGCtrl.Instance:SendCrossFlowerReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_WEEK_ADD_CHARM, index)
end

function CrossFlowerRankView:FlushRankToggle()
    self.node_list["man_rank_toggle"].toggle.isOn = (self.role_vo.sex == 1)
    self.node_list["woman_rank_toggle"].toggle.isOn = (self.role_vo.sex ~= 1)
end

function CrossFlowerRankView:FlushRankInfo()
    local data_list = CrossFlowerRankWGData.Instance:GetRankInfo()
    if data_list ~= nil then
        self.flower_rank_list:SetDataList(data_list)
    end

    local my_rank_data = CrossFlowerRankWGData.Instance:GetMyRankInfo()
    --self.node_list["my_reward_list"]:SetActive(my_rank_data ~= nil)
    if my_rank_data ~= nil then
        self.node_list["flower_my_rank"].text.text = string.format(Language.CrossFlowerRank.MyRankDesc,
            my_rank_data.rank_data.rank)
        -- local data = CrossFlowerRankWGData.Instance:GetRankItemData(my_rank_data.rank_data)
        -- if data then
        --     self.my_reward_list:SetStartZeroIndex(true)
        --     self.my_reward_list:SetDataList(data.reward_item)
        -- end
    else
        self.node_list["flower_my_rank"].text.text = Language.CrossFlowerRank.NoMyRankDesc
    end

    local my_charm = CrossFlowerRankWGData.Instance:GetMyChramInfo()
    self.node_list["my_charm"].text.text = string.format(Language.CrossFlowerRank.MyCurCharm, my_charm)

    local rank_member_list = CrossFlowerRankWGData.Instance:GetTopThreeRankInfo()
    for i = 1, 3 do
        if rank_member_list[i] then
            self.rank_member_list[i]:SetData(rank_member_list[i])
        else
            self.rank_member_list[i]:SetData({})
        end
    end
end

function CrossFlowerRankView:OnCreateCell(cell)
    if cell ~= nil and self.node_list.flower_rank_list ~= nil then
        cell:SetParentScrollRect(self.node_list.flower_rank_list.scroll_rect)
    end
end

--活动时间倒计时
function CrossFlowerRankView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE
    .CROSS_CHANNEL_ACTIVITY_TYPE_CHARM_RANK)
    if activity_data ~= nil then
        local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list["time_count_down_txt"].text.text = TimeUtil.FormatSecondDHM2(invalid_time -
            TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("cross_flower_rank_down", BindTool.Bind1(self.UpdateCountDown, self),
                BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function CrossFlowerRankView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        self.node_list["time_count_down_txt"].text.text = TimeUtil.FormatSecondDHM2(valid_time)
    end
end

function CrossFlowerRankView:OnComplete()
    self.node_list["time_count_down_txt"].text.text = ""
    self.Close()
end

CrossFlowerRankItemRender = CrossFlowerRankItemRender or BaseClass(BaseRender)
function CrossFlowerRankItemRender:__init()
    self.item_list = {}
    self.is_load_complete = true
    self.nested_scroll_rect = SNestedScrollRect.New(self.node_list["item_rect"])
    if self.parent_scroll_rect then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

function CrossFlowerRankItemRender:__delete()
    self.parent_scroll_rect = nil
    if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end

    for k, v in pairs(self.item_list) do
        v:DeleteMe()
    end

    self.item_list = {}
    self.is_load_complete = false
end

function CrossFlowerRankItemRender:OnFlush()
    if not self.data then
        return
    end

    local charm_str = ""
    if self.data.no_true_rank then --未上榜
        local cfg_data = CrossFlowerRankWGData.Instance:GetRankItemData(self.data.rank_data)
        if cfg_data then
            charm_str = string.format(Language.CrossFlowerRank.CharmTxt, cfg_data.reach_value)
        end

        self.node_list.name.text.text = ""
    else
        charm_str = string.format(Language.CrossFlowerRank.CharmTxt, self.data.rank_data.charm)
    end

    local user_name = self.data.rank_data.name
    if self.data.no_true_rank then
        user_name = Language.CrossFlowerRank.XuWeiYiDai
    end

    local rank = self.data.rank_data.rank
    local is_top_3 = rank <= 3
    self.node_list.img_rank_img.image.enabled = is_top_3

    if not is_top_3 then
        self.node_list.rank.text.text = rank
        self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a1_ty_pmd4"))
        self.node_list.name.text.text = user_name
        self.node_list.charm.text.text = charm_str
    else
        self.node_list.img_rank_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank))
        self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_" .. rank))
        self.node_list.rank.text.text = ""
        self.node_list.name.text.text = ToColorStr(user_name, COLOR3B.WHITE)
        self.node_list.charm.text.text = ToColorStr(charm_str, COLOR3B.WHITE)
    end

    local cfg_data = CrossFlowerRankWGData.Instance:GetRankItemData(self.data.rank_data)
    if cfg_data then
        local data_list = {}
        for i = 0, #cfg_data.reward_item do
            table.insert(data_list, cfg_data.reward_item[i])
        end

        local item_list = self.item_list
        if #data_list > #item_list then
            local cell_parent = self.node_list["item_root"]
            for i = 1, #data_list do
                item_list[i] = item_list[i] or ItemCell.New(cell_parent)
            end
            self.item_list = item_list
        end

        for i = 1, #item_list do
            if data_list[i] then
                item_list[i]:SetData(data_list[i])
                item_list[i]:SetActive(true)
            else
                item_list[i]:SetActive(false)
            end
        end
    end
end

function CrossFlowerRankItemRender:SetParentScrollRect(scroll_rect)
    self.parent_scroll_rect = scroll_rect
    if self.is_load_complete then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

--排行榜前三名Item
FlowerRankMemberItemRender = FlowerRankMemberItemRender or BaseClass(BaseRender)
function FlowerRankMemberItemRender:__init()

end

function FlowerRankMemberItemRender:__delete()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end
end

function FlowerRankMemberItemRender:OnFlush()
    if self.data == nil then
        return
    end

    if self.data.no_true_rank then
        self.node_list["name"].text.text = Language.CrossFlowerRank.XuWeiYiDai
        self.node_list["wu_menber"]:SetActive(true)
        self.node_list["display"]:SetActive(false)
    else
        self.node_list["wu_menber"]:SetActive(false)
        self.node_list["display"]:SetActive(true)
        local flush_fun = function(protocol)
            if not self.node_list then
                return
            end
            if nil == self.model_display then
                self.model_display = RoleModel.New()
                local display_data = {
                    parent_node = self.node_list["display"],
                    camera_type = MODEL_CAMERA_TYPE.BASE,
                    -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
                    rt_scale_type = ModelRTSCaleType.M,
                    can_drag = true,
                }
        
                self.model_display:SetRenderTexUI3DModel(display_data)
                -- self.model_display:SetUI3DModel(self.node_list.display.transform,
                --     self.node_list.display.event_trigger_listener,
                --     1, false, MODEL_CAMERA_TYPE.BASE)
            end

            if self.model_display then
                local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true,
                    ignore_shouhuan = true, ignore_tail = true, ignore_waist = true }
                self.model_display:SetModelResInfo(protocol, ignore_table)
            end

            self.node_list["name"].text.text = self.data.rank_data.name
        end

        BrowseWGCtrl.Instance:BrowRoelInfo(self.data.rank_data.uuid.temp_low, flush_fun)
    end
end
