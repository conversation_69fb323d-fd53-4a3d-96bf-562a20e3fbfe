﻿using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

[ExecuteInEditMode]

public class VirticalText : BaseMeshEffect
{
    //[Tooltip("字和字之间的距离")]
    public float spacing = 1;
    public enum PosType
    {
        leftToRight = 0,
        righTotLeft,
    }
    public PosType orientation;

    private float lineSpacing = 1;
    private float textSpacing = 1;
    private float xOffset = 0;
    private float yOffset = 0;

    public override void ModifyMesh(VertexHelper helper)
    {
        if (!IsActive())
            return;

        List<UIVertex> verts = new List<UIVertex>();
        helper.GetUIVertexStream(verts);

        Text text = GetComponent<Text>();

        TextGenerator tg = text.cachedTextGenerator;


        lineSpacing = text.fontSize * text.lineSpacing;
        textSpacing = text.fontSize * spacing;

        xOffset = text.rectTransform.sizeDelta.x / 2 - text.fontSize / 2;
        yOffset = text.rectTransform.sizeDelta.y / 2 - text.fontSize / 2;

        List<UILineInfo> lines = new List<UILineInfo>();
        tg.GetLines(lines);

        int lineNum = (int)(text.rectTransform.sizeDelta.y / textSpacing);

        var txt = text.text;
        int line = 0;
        int charNum = 0;
        int count = 0;
        int stake = 0;
        int charXPos = 0;
        int charYPos = 0;
        for (int i = 0; i < tg.characterCountVisible; i++)
        {
            stake = charNum / lineNum;
            if (txt[i] == '\n')
            {
                bool lastIsNull = false;
                bool autoNull = true;
                if (i - 1 >= 0)
                {
                    lastIsNull = txt[i - 1] == '\n';
                }

                if (charNum > 0)
                {
                    autoNull = charNum % lineNum != 0;
                }

                if (autoNull || lastIsNull)
                {
                    line++;
                    charNum = 0;
                    count = stake + count;
                }
            }

            charXPos = charNum / lineNum + line + count;
            charYPos = charNum % lineNum;
            float x = charXPos * lineSpacing - xOffset;
            float y = -charYPos * textSpacing + yOffset;

            if (orientation == PosType.righTotLeft)
            {
                x = -charXPos * lineSpacing + xOffset;
            }

            ModifyText(helper, i, y, x);
            if (txt[i] != '\n')
            {
                charNum++;
            }
        }
    }

    void ModifyText(VertexHelper helper, int i, float charYPos, float charXPos)
    {
        UIVertex lb = new UIVertex();
        helper.PopulateUIVertex(ref lb, i * 4);

        UIVertex lt = new UIVertex();
        helper.PopulateUIVertex(ref lt, i * 4 + 1);

        UIVertex rt = new UIVertex();
        helper.PopulateUIVertex(ref rt, i * 4 + 2);

        UIVertex rb = new UIVertex();
        helper.PopulateUIVertex(ref rb, i * 4 + 3);

        Vector3 center = Vector3.Lerp(lb.position, rt.position, 0.5f);
        Matrix4x4 move = Matrix4x4.TRS(-center, Quaternion.identity, Vector3.one);

        Vector3 pos = new Vector3(charXPos, charYPos, 0);
        Matrix4x4 place = Matrix4x4.TRS(pos, Quaternion.identity, Vector3.one);
        Matrix4x4 transform = place * move;

        lb.position = transform.MultiplyPoint(lb.position);
        lt.position = transform.MultiplyPoint(lt.position);
        rt.position = transform.MultiplyPoint(rt.position);
        rb.position = transform.MultiplyPoint(rb.position);

        helper.SetUIVertex(lb, i * 4);
        helper.SetUIVertex(lt, i * 4 + 1);
        helper.SetUIVertex(rt, i * 4 + 2);
        helper.SetUIVertex(rb, i * 4 + 3);
    }
}