ShenShouEquipBagView = ShenShouEquipBagView or BaseClass(SafeBaseView)
local DELAY_TIME = 0.7
local MAX_QUALITY = 8
local MAX_STAR = 6

function ShenShouEquipBagView:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, -2), sizeDelta = Vector2(1139, 652)})
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_sseq_show")
	self.shenshou_cells = {}
	self.effect_list = {}
	self.quality = -1
	self.star = 0
	self.load_cell_complete_count = 0
	self.max_series = 0
	self.equip_list ={}
	self.equip_list_data ={}
	self.ss_cell_list = {}
	self.first_time_load = true
	self.shou_id = 0
	self.series = 0
	self.default_index = 0
	self.default_index2 = 0
	self.select_quality = -1
	self.select_star = -1
	-- 当前选中权重
	self.select_solt = -1
end

function ShenShouEquipBagView:__delete()

end

function ShenShouEquipBagView:ReleaseCallBack()
	if self.shenshou_cells then
		for k, v in pairs(self.shenshou_cells) do
			v:DeleteMe()
		end
	end
	self.shenshou_cells = {}
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end

	if self.ss_cell_list then
		for k,v in pairs(self.ss_cell_list) do
			for m,n in pairs(v) do
				n:DeleteMe()
			end
		end
	end
	self.ss_cell_list = {}

	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end

	if self.bag_filter then
		GlobalEventSystem:UnBind(self.bag_filter)
		self.bag_filter = nil
	end

	-- if self.model then
	-- 	self.model:DeleteMe()
	-- 	self.model = nil
	-- end

	self.pag_list_view = nil
	self.cache_model = nil
	self.equip_list = {}
	self.equip_list_data = {}

	self.sseq_bag = nil
	self.select_solt = -1

	self.ui_clear_zdepth = nil
	self.load_cell_complete_count = 0
	self.role_level = 0

	self.select_quality = -1
	self.select_star = -1

	self.select_equip_cell_index = -1
	if self.boss_display_model then
		self.boss_display_model:DeleteMe()
		self.boss_display_model = nil
	end
	self.cache_resid = nil
end

function ShenShouEquipBagView:CloseCallBack()
	ShenShouWGCtrl.Instance:CloseChooseBtnView()
	self.quality = -1
	self.star = 0
	self.shou_id = 0
	self.series = 0
	self.max_series = 0
	self.select_equip_cell_index = -1
	self.select_solt = -1
end

function ShenShouEquipBagView:OpenCallBack()
	if(self.load_cell_complete_count == self.max_series) then
		if self.ss_accordion_list[self.series].btn.toggle.isOn then
			self:FlushShenShouList()
		else
			self.ss_accordion_list[self.series].btn.toggle.isOn = true
		end
	end
end

function ShenShouEquipBagView:LoadCallBack()
	self.role_level = ShenShouWGCtrl.Instance.view.role_level
	self.default_index = RoleWGData.GetRolePlayerPrefsInt("shenshou_default_index")
	if self.default_index < 1 then
		self.default_index = #Language.ShenShou.ChooseBtnText + 1
		RoleWGData.SetRolePlayerPrefsInt("shenshou_default_index", self.default_index)
	end

	self.default_index2 = RoleWGData.GetRolePlayerPrefsInt("shenshou_default_index2")
	if self.default_index2 < 1 then
		self.default_index2 = #Language.ShenShou.ChooseBtnText2 + 1
		RoleWGData.SetRolePlayerPrefsInt("shenshou_default_index2", self.default_index2)
	end

	XUI.AddClickEventListener(self.node_list.close_quality, BindTool.Bind(self.OnClickCloseQuality, self))
	XUI.AddClickEventListener(self.node_list.close_star, BindTool.Bind(self.OnClickCloseStar, self))
	XUI.AddClickEventListener(self.node_list.btn_get_equip, BindTool.Bind1(self.OnClickGainEquip, self))
	XUI.AddClickEventListener(self.node_list.btn_compose, BindTool.Bind(self.OnClickCompose, self))
	XUI.AddClickEventListener(self.node_list.one_key_equip_btn, BindTool.Bind(self.OnOneKeyEquip, self))

	for i = 0, MAX_QUALITY do
        XUI.AddClickEventListener(self.node_list.quality_list:FindObj("quality_btn_" .. i), BindTool.Bind(self.OnSelectQuality, self, i)) --开始是橙色
    end

	for i = 0, MAX_STAR do
        XUI.AddClickEventListener(self.node_list.star_list:FindObj("star_btn_" .. i), BindTool.Bind(self.OnSelectStar, self, i)) --开始是橙色
    end

	self.node_list.title_view_name.text.text = Language.ShenShou.EquipTitle
	self.node_list.cur_quality_text.text.text = Language.ShenShou.ChooseBtnText[self.select_quality + 2]
	self.node_list.cur_star_text.text.text = Language.ShenShou.ChooseBtnText2[self.select_star + 2]

	self.pop_alert = Alert.New()
	self.pop_alert:SetLableString(Language.ShenShou.NoRightEquip)
	self.pop_alert:SetOkString(Language.ShenShou.GainEquip)

	self:CreateShenShouBag()----self.first_time_load
	self:CreateShenShouCells()
	self:CreateShenShouList()
	self.bag_filter = GlobalEventSystem:Bind(
		ShenShouEventType.BAG_FLUSH,
		BindTool.Bind(self.FlushShenShouBag, self)
	)

	self:SetUIClearZDepthActive()
	if nil == self.boss_display_model then
		self.boss_display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ss_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.boss_display_model:SetRenderTexUI3DModel(display_data)
		-- self.boss_display_model:SetUI3DModel(self.node_list["ss_model"].transform,
		-- 	self.node_list["ss_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	self.node_list.desc.text.text = Language.ShenShou.BagMaterialDesc
end

function ShenShouEquipBagView:CreateShenShouList()
	self.ss_accordion_list = {}
	for i = 1, Language.ShenShou.SS_SERIES do
		if i <= self.max_series then
			self.ss_accordion_list[i] = {}
			self.node_list["Content"]:FindObj("SelectBtn" .. i):SetActive(true)
			self.ss_accordion_list[i].btn = self.node_list["Content"]:FindObj("SelectBtn" .. i)
			self.ss_accordion_list[i].list = self.node_list["Content"]:FindObj("List" .. i)
			self.ss_accordion_list[i].btn.accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler, self, i))

			local name = ShenShouWGData.Instance:GetSeriesName(i)
			if name then
				self.node_list["text_btn" .. i].text.text = name
			end

			self:InstantSSItem(i)
		else
			self.node_list["Content"]:FindObj("SelectBtn" .. i):SetActive(false)
		end
	end
end

function ShenShouEquipBagView:InstantSSItem(index)
	local ss_list = ShenShouWGData.Instance:GetShenShouDataBySeries(index, self.role_level)
	if not self.ss_cell_list[index] then
		local res_async_loader = AllocResAsyncLoader(self, "ph_eq_item" .. index)

	 	res_async_loader:Load("uis/view/shenshou_ui_prefab", "ph_eq_item", nil,
	 		function(new_obj)
			    local item_vo = {}
			    for k,v in pairs(ss_list) do
			        local obj = ResMgr:Instantiate(new_obj)
			        local obj_transform = obj.transform
			        obj_transform:SetParent(self.ss_accordion_list[index].list.transform, false)
			        local item_render = ShenShouItemRender.New(obj)
					item_render.is_bag = true
			        item_render.parent_view = self
			        item_render:SetIndex(k)
			        item_render.view.button:AddClickListener(BindTool.Bind(self.SelectShenShouCellCallBack, self, item_render))
			        item_render:SetData(v)
			        item_vo[k] = item_render
			        if k == #ss_list then
						self.ss_cell_list[index] = item_vo
			      		self.load_cell_complete_count = self.load_cell_complete_count + 1
			   		end
			    end

			    if self.load_cell_complete_count == self.max_series then
					-- 第一次打开界面进行跳转
					if self.ss_accordion_list[self.series].btn.toggle.isOn then
						self:FlushShenShouList()
					else
						self.ss_accordion_list[self.series].btn.toggle.isOn = true
					end
				end
			end)
	else
		self:ShowFlushCell(index, ss_list)
	end
end

function ShenShouEquipBagView:ShowFlushCell(index, ss_list)
	if self.load_cell_complete_count < self.max_series then
		return
	end

	for k, v in pairs(self.ss_cell_list[index]) do
		if nil == ss_list[k] then
			break
		end
		v:SetData(ss_list[k])
	end
end

function ShenShouEquipBagView:CreateShenShouBag()
	self.sseq_bag = ShenShouGrid.New()
	self.sseq_bag:CreateCells({ col = 4, cell_count = 200, itemRender = ShenShouEquipCell,
		list_view = self.node_list["ph_bag_grid"] })
end

function ShenShouEquipBagView:CreateShenShouCells()
	local alpha_color = Color.New(1, 1, 1, 0.7)
	self.shenshou_cells = {}
	for i = 1, GameEnum.SHENSHOU_MAX_EQUIP_COUNT do
		self.shenshou_cells[i] = ShenShouEquipCell.New(self.node_list["ph_cell_" .. i])
		-- self.shenshou_cells[i]:SetPartName(Language.ShenShou.PartName[i])
		self.shenshou_cells[i]:SetItemTipFrom(ShenShouEquipTip.FROM_SHENSHOU)
		-- self.shenshou_cells[i]:SetItemIcon(ResPath.GetEquipIcon(i,EquipIcon.ShenShou))
		self.shenshou_cells[i]:SetItemIconAlpha(alpha_color)
		self.shenshou_cells[i]:AddClickEventListener(BindTool.Bind(self.OnClickShenShouCell, self, i), true)
		XUI.AddClickEventListener(self.node_list['btn_cell_' .. i], BindTool.Bind(self.FlushListByCurSolt, self, i))
	end
end

function ShenShouEquipBagView:SetUIClearZDepthActive()
	if IsNil(self.ui_clear_zdepth) then
		self.ui_clear_zdepth = self.root_node.transform:Find("UIClearZDepth").gameObject
		local ui_clear_zdepth_canvas = self.ui_clear_zdepth:GetComponent(typeof(UnityEngine.Canvas))
		self.ui_clear_zdepth.gameObject:SetActive(true)
		ui_clear_zdepth_canvas.overrideSorting = false
	end
end

function ShenShouEquipBagView:OnSelectQuality(quality)
	self.select_quality = quality - 1
	self.node_list.cur_quality_text.text.text = Language.ShenShou.ChooseBtnText[quality + 1]
	self.node_list.btn_select_quality.toggle.isOn = false
    self:FlushShenShouBag()
end

function ShenShouEquipBagView:OnSelectStar(star)
	self.select_star = star - 1
    self.node_list.cur_star_text.text.text = Language.ShenShou.ChooseBtnText2[star + 1]
	self.node_list.btn_select_star.toggle.isOn = false
    self:FlushShenShouBag()
end

function ShenShouEquipBagView:OnClickCloseQuality()
	self.node_list.btn_select_quality.toggle.isOn = false
end

function ShenShouEquipBagView:OnClickCloseStar()
	self.node_list.btn_select_star.toggle.isOn = false
end

--点击大按钮后刷新小按钮并且跳转到对应的小按钮位置
function ShenShouEquipBagView:FlushShenShouList(is_form_loaded)
	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end

	local ss_list = ShenShouWGData.Instance:GetShenShouConfigBySeries(self.series, self.role_level)
	self:InstantSSItem(self.series)
	local default_select_cell_index = 1
	if self.jump_shenshou_id then
		for k,v in pairs(ss_list) do
			if v.shou_id == self.jump_shenshou_id then
				default_select_cell_index = k
				break
			end
		end
		self.jump_shenshou_id = nil
	elseif is_form_loaded then
		for k1,v1 in pairs(ss_list) do
			if ShenShouWGData.Instance:GetEquipBagRemind(v1.shou_id) then
				default_select_cell_index = k1
				break
			end
		end
	end

	self:SelectShenShouCellCallBack(self.ss_cell_list[self.series][default_select_cell_index])
	--当跳转的索引超过6时会超出屏幕显示位置需要移动位置
	if(default_select_cell_index > 6) then
		self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
			self:CalcScroll(self.ss_cell_list[self.series][default_select_cell_index])
		end, DELAY_TIME)
	end
end

function ShenShouEquipBagView:IsCellRemind()
	local flag = false
	local btn_flag = false
	local is_visible = ShenShouWGData.Instance:IsShowShenShouRenderRed(self.shou_id)
	for k,v in pairs(self.shenshou_cells) do
		local cell_data = ShenShouWGData.Instance:GetOneSlotData(self.shou_id, k - 1)
		flag = ShenShouWGData.Instance:GetHasBetterShenShouEquip(cell_data, self.shou_id, k)
		btn_flag = btn_flag or (is_visible and flag)
	end

	return btn_flag
end

function ShenShouEquipBagView:CalcScroll(cell)
	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera,
		cell.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
	self.node_list.Content.rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	local per
	local content_size_y = self.node_list.Content.rect.sizeDelta.y
	local togglesv_size_y = self.node_list.ToggleSV.rect.sizeDelta.y
	if content_size_y + local_position_tbl.y < togglesv_size_y then
		per = 0
	else
		local rect = content_size_y - self.node_list.ToggleSV.rect.sizeDelta.y
		per = (content_size_y + local_position_tbl.y - togglesv_size_y + 26) / rect
	end

	per = math.min(per, 1)
	self.node_list.ToggleSV.scroll_rect.verticalNormalizedPosition = per
end

-- 刷新
function ShenShouEquipBagView:FlushListByCurSolt(solt_index)
	solt_index = solt_index and solt_index - 1
	self.select_solt = solt_index
	local need_cfg = ShenShouWGData.Instance:GetQualityRequirementCfg(self.shou_id, self.select_solt)
	if need_cfg then
		local need_item_id = ShenShouWGData.Instance:GetShenShouNeedShowCfg(need_cfg.slot, need_cfg.slot_need_quality,
			need_cfg.slot_need_star)
		if need_item_id ~= -1 then
			local data = {}
			data.strength_level = 0
			data.item_id = need_item_id
			data.star_count = need_cfg.slot_need_star > 0 and need_cfg.slot_need_star or 1
			ShenShouWGCtrl.Instance:OpenShenShouEquipTip(data, ShenShouEquipTip.FROM_EQUIMENT_HECHENG)
		end
	end

	self:FlushShenShouBag()
end

-- 显示激活特效
function ShenShouEquipBagView:ShowActiveEffect()
	-- if nil ~= self.node_list["effect"] then
	-- 	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jinjietexiao_two)
	-- 	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect"].transform)
	-- end
end

function ShenShouEquipBagView:OnClickExpandHandler(idx, is_on)
	self.series = idx

	if not is_on then
		return
	end

	if IsEmptyTable(((self.ss_cell_list or {})[idx] or {})[1] or {}) then
		return
	end

	if(self.jump_shenshou_id) then
		self:FlushShenShouList()
	else
		self:FlushShenShouList(true)
	end
end

function ShenShouEquipBagView:FlushShenShouCellHighLight()
	for k, v in ipairs(self.ss_cell_list[self.series]) do
		v:OnSelectChange(v.data.shou_id == self.shou_id)
	end
end

-- 选择神兽回调
function ShenShouEquipBagView:SelectShenShouCellCallBack(cell)
	if nil == cell or nil == cell.data then return end
	self.shou_id = cell.data.shou_id
	self.series = cell.data.series
	self:FlushShenShouBag()
	self:FlushEquipList()
	self:FlushCellRemind()
	self:FlushModel()
	self:FlushShenShouCellHighLight()
	--self:HandleEquipShenShouOneKey(self.select_shou_id)
	--self:PlayModelEffect(self.select_shou_id) --特效模型加载
end

function ShenShouEquipBagView:OnClickShenShouCell(cell_index)
	cell_index = cell_index and (cell_index - 1) or nil
	if cell_index == nil then
		return
	end

	self.select_equip_cell_index = cell_index
	self:FilterShenShouBag(cell_index)
end

function ShenShouEquipBagView:FilterShenShouBag(i)
	if not self.sseq_bag then
		return
	end

	local quality_requirement = ShenShouWGData.Instance:GetQualityRequirementCfg(self.shou_id, i) ----对应格子的品质
	local bag_cfg = __TableCopy(ShenShouWGData.Instance:FilterShenShouEq(self.select_quality, self.select_star))
	for k, v in pairs(bag_cfg) do
		v.grid_name = GRID_TYPE_SHENSHOU_BAG
	end

	local list_1 = {}
	local list_2 = {}
	for k, v in pairs(bag_cfg) do
		if v.item_id ~= 0 then
			if v.is_equip == 1 and quality_requirement
				and v.slot_index == quality_requirement.slot
				and v.quality >= quality_requirement.slot_need_quality
				and v.star_count >= quality_requirement.slot_need_star
			then
				local index = #list_1 + 1
				list_1[index] = v
			else
				local index = #list_2 + 1
				list_2[#list_2 + 1] = v
			end
		end
	end

	if #list_1 == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.NoRightEquip)
		return
	end

	self.quality = -1
	self.star = 0
	self:FlushShenShouBag()

	for a = 1, #list_2 do
		table.insert(list_1, list_2[a])
	end

	local flag
	for k1, v in ipairs(list_1) do
		flag = ShenShouWGData.Instance:GetIsBetterShenShouEquip(v, self.shou_id)	----比较是否有较好的升级
		v.is_better = flag and 1 or 0
		v.is_cur_select = v.slot_index == self.select_solt and 1 or 0
	end

	list_1[0] = table.remove(list_1, 1)
	self.sseq_bag:SetDataList(list_1)

	--选中效果暂定高亮，以后记得换特效
	--self.sseq_bag:SetIsMultiSelect(true)
	--self.sseq_bag:SetMultiSelectEffect(#list_1)
	--self.sseq_bag:SetIsMultiSelect(false)
end

function ShenShouEquipBagView:OnClickGainEquip()
	self:Close()
	FunOpen.Instance:OpenViewByName(GuideModuleName.WorldServer, "worserv_boss_mh")
end

function ShenShouEquipBagView:OnClickCompose()
	self:Close()
	FunOpen.Instance:OpenViewNameByCfg("other_compose#other_compose_eq_hecheng_shenshou")
end

function ShenShouEquipBagView:OnOneKeyEquip()
	ShenShouWGData.Instance:HandleEquipShenShouOneKey(self.shou_id)
end

function ShenShouEquipBagView:OpenShenShouBag(shou_id, series)
	if shou_id < 0 then return end

	self.shou_id = shou_id
	self.series = series
	self.jump_shenshou_id = shou_id
	self.max_series = ShenShouWGCtrl.Instance.view.max_series
	self:Open()
end

function ShenShouEquipBagView:SetShenShouImage()
	local actor_num = ShenShouWGData.Instance:GetActorNum(self.shou_id)
	if self.cache_model == actor_num then
		return
	end
	self.cache_model = actor_num
end

function ShenShouEquipBagView:ShowIndexCallBack()
	--这里获取RoleWGData.GetRolePlayerPrefsInt index
	self:Flush()
	self:SetShenShouImage()
end

function ShenShouEquipBagView:FlushShenShouActive()
	local shenshou_list = ShenShouWGData.Instance:GetShenshouList(self.shou_id)
	self.big_effect_flag = true
	if shenshou_list then
		for k, v in pairs(shenshou_list.equip_list) do
			if v.item_id == 0 then
				self.big_effect_flag = false
				break
			end
		end
	else
		self.big_effect_flag = false
	end
	--self.node_list.shenshoujihuo_effect:SetActive(self.big_effect_flag)
end

function ShenShouEquipBagView:OnFlush()
	self:InstantSSItem(self.series)
	self:FlushShenShouBag()
	self:FlushSbBigBtnRed()
	--self:FlushShenShouActive()
	self:FlushEquipList()
	self:FlushCellRemind()
	self:FlushModel()
end

--刷新装备列表
function ShenShouEquipBagView:FlushEquipList()
	local shenshou_list = ShenShouWGData.Instance:GetShenshouList(self.shou_id)
	local cell_cfg = ShenShouWGData.Instance:GetQualityRequirement(self.shou_id)
	if shenshou_list then
		for k, v in pairs(shenshou_list.equip_list) do
			self.shenshou_cells[k]:SetData(v)
			self.node_list['btn_cell_' .. k]:SetActive(v.item_id == 0)
			if v.item_id == 0 then
				self.shenshou_cells[k]:SetItemIcon(ResPath.GetF2SHJImgPath("equip_" .. k - 1))
				if cell_cfg[k] then
					self.node_list["ph_cell_" .. k].image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" ..
					cell_cfg[k].slot_need_quality + 1))
					self.shenshou_cells[k]:SetLeftTopImg(cell_cfg[k].slot_need_star)
				end
			end
		end
	else
		--空的时候没有装备
		for k, v in pairs(self.shenshou_cells) do
			if self.shenshou_cells[k] then
				self.shenshou_cells[k]:ClearAllParts()
				self.shenshou_cells[k]:SetData(nil)
				--背景板
				self.shenshou_cells[k]:SetItemIcon(ResPath.GetF2SHJImgPath("equip_" .. k - 1))
				local limit_quality = ShenShouWGData.Instance:GetQualityRequirementCfg(self.shou_id, k - 1)
				if limit_quality then
					self.shenshou_cells[k]:SetLeftTopImg(limit_quality.slot_need_star)
				end
			end

			--颜色
			if cell_cfg and cell_cfg[k] then
				self.node_list["ph_cell_" .. k].image:LoadSprite(ResPath.GetCommonImages("a3_ty_wpk_" ..
				cell_cfg[k].slot_need_quality + 1))
			end

			self.node_list['btn_cell_' .. k]:SetActive(true)
		end
	end

	local model_info = ShenShouWGData.Instance:GetShenShouCfg(self.shou_id)
	if model_info then
		self.node_list.shenshou_name.text.text = model_info.name
	end
end

function ShenShouEquipBagView:FlushSbBigBtnRed()
	for k,v in ipairs(self.ss_accordion_list) do
		if v.btn:FindObj("img_read") then
			v.btn:FindObj("img_read"):SetActive(ShenShouWGData.Instance:GetShenShouBagRemindBySeries(k))
		end
	end
end

--刷新模型
function ShenShouEquipBagView:FlushModel()
	local model_info = ShenShouWGData.Instance:GetShenShouCfg(self.shou_id)
	if nil ~= model_info and self.boss_display_model then
		if self.cache_resid == model_info.icon_id and self.boss_display_model then
			if self.boss_display_model:GetDrawObj() ~= nil then
				return
			end
		end

		self.boss_display_model:SetMainAsset(ResPath.GetDivineSoldierModel(model_info.icon_id))

		self.cache_resid = model_info.icon_id
		self.boss_display_model:PlayMonsterAction(false)
	end
end

--刷新格子红点
function ShenShouEquipBagView:FlushCellRemind()
	local flag = false
	local is_visible = ShenShouWGData.Instance:IsShowShenShouRenderRed(self.shou_id)
	for k, v in pairs(self.shenshou_cells) do
		local cell_data = ShenShouWGData.Instance:GetOneSlotData(self.shou_id, k - 1)
		flag = ShenShouWGData.Instance:GetHasBetterShenShouEquip(cell_data, self.shou_id, k)
		self.shenshou_cells[k]:SetCanOperateIconVisible(is_visible and flag)
	end
end

function ShenShouEquipBagView:FlushShenShouBag(index1, index2)
	local CHOOSE_1 = #Language.ShenShou.ChooseBtnText
	local CHOOSE_2 = #Language.ShenShou.ChooseBtnText2
	if index1 then
		self.default_index = index1 + 1
	end
	if self.default_index == CHOOSE_1 + 1 then
		self.quality = -1
	else
		self.quality = CHOOSE_1 - self.default_index
	end
	if index2 then
		self.default_index2 = index2 + 1
	end
	if self.default_index2 == CHOOSE_2 + 1 then
		self.star = 0
	else
		self.star = CHOOSE_2 - self.default_index2 + 1
	end

	self.shenshou_bag_filter_list = __TableCopy(ShenShouWGData.Instance:FilterShenShouEq(self.select_quality, self.select_star))

	local flag
	for i,v in ipairs(self.shenshou_bag_filter_list) do
		flag = ShenShouWGData.Instance:GetIsBetterShenShouEquip(v, self.shou_id)	----比较是否有较好的升级
		v.is_better = flag and 1 or 0
		v.is_cur_select = v.slot_index == self.select_solt and 1 or 0
	end

	SortTools.SortDesc(self.shenshou_bag_filter_list, "is_equip", "is_cur_select", "is_better", "quality", "star_count",
		"slot_index")

	self.shenshou_bag_filter_list[0] = table.remove(self.shenshou_bag_filter_list, 1)
	for k, v in pairs(self.shenshou_bag_filter_list) do
		v.grid_name = GRID_TYPE_SHENSHOU_BAG
	end

	if not self.sseq_bag and not self.shenshou_bag_filter_list then
		return
	end

	self.sseq_bag:SetDataList(self.shenshou_bag_filter_list, 2)
	self.sseq_bag:CancleAllSelectCell()
end

--------------------------------------------------------btn1------------------------------------

ChooseBtnView = ChooseBtnView or BaseClass(SafeBaseView)
function ChooseBtnView:__init()
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_choosebtn")

	self:SetMaskBg(true)
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.default_index = 0 --无操作打开默认为第一个
end

function ChooseBtnView:LoadCallBack()
	self.default_index = RoleWGData.GetRolePlayerPrefsInt("shenshou_default_index")
	for i = 0, 6 do
		self.node_list["text" .. i].text.text = string.format(Language.ShenShou.ChooseBtnText[i])
		XUI.AddClickEventListener(self.node_list["btn_" .. i], BindTool.Bind(self.OnClickTabindex, self, i))
	end
end

function ChooseBtnView:ShowIndexCallBack()
	--local default_index = ShenShouWGCtrl.Instance:GetSelectChooseBtnIndex()
	for i = 0, 6 do
		self.node_list["btn_select_" .. i]:SetActive(self.default_index == i + 1)
	end
end

function ChooseBtnView:OnClickTabindex(i)
	-- ShenShouWGCtrl.Instance.view.quality = i - 1
	-- ShenShouWGCtrl.Instance.view:FlushShenShouBag()
	self.default_index = i + 1 --给default_index传值
	GlobalEventSystem:Fire(ShenShouEventType.BAG_FLUSH, i, nil)

	--ShenShouWGCtrl.Instance:GetSelectChooseBtnIndex(i)
	for index = 0, 6 do
		self.node_list["btn_select_" .. index]:SetActive(index == i)
	end
	self:Close()
end

function ChooseBtnView:ReleaseCallBack()
	if self.image_node then
		self.image_node = nil
	end
	-- if ShenShouWGCtrl.Instance then
	-- 	ShenShouWGCtrl.Instance:SetSelectChooseBtnIndex(#Language.ShenShou.ChooseBtnText)
	-- end
end

function ChooseBtnView:CloseCallBack()
	if self.image_node then
		self.image_node.transform.localScale = Vector3(1, 1, 1)
	end
	RoleWGData.SetRolePlayerPrefsInt("shenshou_default_index", self.default_index)
end

function ChooseBtnView:SetOpenImage(node_image)
	self.image_node = node_image
end

----------------------------------------------------btn2-----------------------------------------
ChooseBtn2View = ChooseBtn2View or BaseClass(SafeBaseView)
function ChooseBtn2View:__init()
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_choosebtn_2")
	self:SetMaskBg(true)
	self.view_layer = UiLayer.PopWhite
	self.default_index2 = 0
end

function ChooseBtn2View:LoadCallBack()
	self.default_index2 = RoleWGData.GetRolePlayerPrefsInt("shenshou_default_index2")
	for i = 0, 3 do
		self.node_list["text" .. i].text.text = string.format(Language.ShenShou.ChooseBtnText2[i])
		XUI.AddClickEventListener(self.node_list["btn2_" .. i], BindTool.Bind(self.OnClickTab2index, self, i))
	end
end

function ChooseBtn2View:ShowIndexCallBack()
	for i = 0, 3 do
		self.node_list["btn_select_" .. i]:SetActive(self.default_index2 - 1 == i)
	end
end

function ChooseBtn2View:OnClickTab2index(i)
	-- ShenShouWGCtrl.Instance.shenshou_equip_bag_view.star = i
	-- ShenShouWGCtrl.Instance.shenshou_equip_bag_view:FlushShenShouBag()
	self.default_index2 = i + 1
	RoleWGData.SetRolePlayerPrefsInt("shenshou_default_index2", self.default_index2)
	GlobalEventSystem:Fire(ShenShouEventType.BAG_FLUSH, nil, i)
	for index = 0, 3 do
		self.node_list["btn_select_" .. index]:SetActive(index == i)
	end

	self:Close()
end

function ChooseBtn2View:SetOpenImage(node_image)
	self.image_node = node_image
end

function ChooseBtn2View:CloseCallBack()
	if self.image_node then
		self.image_node.transform.localScale = Vector3(1, 1, 1)
	end
end

function ChooseBtn2View:ReleaseCallBack()
	if self.image_node then
		self.image_node = nil
	end
	-- if ShenShouWGCtrl.Instance then
	-- 	ShenShouWGCtrl.Instance:SetSelectChooseBtnIndexTwo(#Language.ShenShou.ChooseBtnText2)
	-- end
end

----------------------------------------------EquipCell----------
EquipCell = EquipCell or BaseClass(BaseGridRender)

function EquipCell:__init()
end

function EquipCell:LoadCallBack()
	self.item_cell_list = {}
	for i = 1, 4 do
		local item_cell = ShenShouEquipCell.New(self.node_list["itemcell_" .. i])
		--item_cell.button:AddClickEventListener(BindTool.Bind(self.SelectShenShouBagCellCallBack, self, i))
		--:SetData(nil)
		-- item_cell:SetQualityIconVisible(true)
		table.insert(self.item_cell_list, item_cell)
	end
end

function EquipCell:__delete()
	for k, v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = {}
end

function EquipCell:OnFlush()
	for i = 1, 4 do
		-- if self.data and self.data[i] then
		self.item_cell_list[i]:SetData(self.data[i])
		-- end
	end
end

function EquipCell:ClearAllDatas()
	for i = 1, 4 do
		self.item_cell_list[i]:ClearAllParts()
	end
end