
EffectManager = EffectManager or BaseClass()

function EffectManager:__init()
	if EffectManager.Instance then
		print_error("EffectManager to create singleton twice")
	end
	EffectManager.Instance = self
	self.last_obj = nil
	self.timer_list = {}
end

function EffectManager:__delete()
	EffectManager.Instance = nil
end

function EffectManager:PlayAtTransform(bundle, asset, parent_transform, duration, position, rotation, scale, call_back, finish_callback)
	duration = duration or 1
	ResPoolMgr:__GetDynamicObjAsync(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		if parent_transform == nil or IsNil(parent_transform) then
			ResPoolMgr:Release(obj)
			return
		end

		local transform = obj.transform
		if position ~= nil then
			transform.localPosition = position
		end

		if rotation ~= nil then
			transform.rotation = rotation
		end

		if scale ~= nil then
			transform.localScale = scale
		end

		local control = obj:GetComponent(typeof(EffectControl))
		if control ~= nil then
			print_error("加载有EffectControl组件的请使用PlayControlEffect接口", bundle, asset)
		end

		transform:SetParent(parent_transform, false)

		GlobalTimerQuest:AddDelayTimer(function()
			ResPoolMgr:Release(obj)
			if nil ~= finish_callback then
				finish_callback()
			end
		end, duration)


		if nil ~= call_back then
			call_back(obj)
		end
	end, parent_transform, ResLoadPriority.high)
end

--连续播放时立即删除上个特效
function EffectManager:PlaySingleAtTransform(bundle, asset, parent_transform, duration, position, rotation, scale, call_back)
	duration = duration or 1
	local key = string.format("single_effect %s %s", bundle, asset)
	-- 移除旧计时器
	if self.timer_list[key] then
		GlobalTimerQuest:CancelQuest(self.timer_list[key])
		self.timer_list[key] = nil
	end

	local async_loader = AllocAsyncLoader(self, key)
	async_loader:SetIsUseObjPool(true)
	async_loader:SetParent(parent_transform)
	async_loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		local control = obj:GetComponent(typeof(EffectControl))
		if control ~= nil then
			print_error("加载有EffectControl组件的请使用PlayControlEffect接口", bundle, asset)
		end

		if position ~= nil then
			obj.transform.localPosition = position
		end

		if rotation ~= nil then
			obj.transform.rotation = rotation
		end

		if scale ~= nil then
			obj.transform.localScale = scale
		end


		local timer = GlobalTimerQuest:AddDelayTimer(function()
			async_loader:Destroy()
		end, duration)

		-- 记录新计时器
		self.timer_list[key] = timer

		if nil ~= call_back then
			call_back(obj)
		end
	end)
end

-- 播放普通特效，没有EffectControl的
function EffectManager:PlayEffect(target, bundle, asset, parent_transform, position, scale, call_back, duration)
	target.control_effect_counter = target.control_effect_counter or 0
	target.control_effect_counter = target.control_effect_counter + 1
	target.control_effect_counter = target.control_effect_counter % 15 -- 防止创建loader过多

	local async_loader = AllocAsyncLoader(target, "control_effect_loader_" .. target.control_effect_counter)
	local live_time = duration or 5
	live_time = live_time <= 5 and live_time or 5
	async_loader:SetObjAliveTime(live_time) --防止永久存在
	async_loader:SetIsUseObjPool(true)
	async_loader:SetLoadPriority(ResLoadPriority.mid)
	async_loader:SetParent(parent_transform or G_EffectLayer)
	if nil ~= scale then
		async_loader:SetLocalScale(Vector3(scale, scale, scale))
	end
	async_loader:SetLocalPosition(position)

	async_loader:Load(bundle, asset, function(obj)
		if not IsNil(obj) then
			local control = obj:GetComponent(typeof(EffectControl))
			if control ~= nil then
				print_error("加载有EffectControl组件的请使用PlayControlEffect接口", bundle, asset)
			end
		end

		if nil ~= call_back then
			call_back(obj)
		end
	end)
end

-- 播放带有EffectControl的特效
function EffectManager:PlayControlEffect(target, bundle, asset, position, deliverer_position, parent_transform, scale, call_back, finish_callback)
	target.control_effect_counter = target.control_effect_counter or 0
	target.control_effect_counter = target.control_effect_counter + 1
	target.control_effect_counter = target.control_effect_counter % 15 -- 防止创建loader过多

	local async_loader = AllocAsyncLoader(target, "control_effect_loader_" .. target.control_effect_counter)
	async_loader:SetObjAliveTime(15) --防止永久存在
	async_loader:SetIsUseObjPool(true)
	async_loader:SetParent(parent_transform or G_EffectLayer)

	async_loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		obj:SetActive(true)
		-- 使用第五个参数transform的话就可以把特效放在指定的transform上播放，否则只能放在坐标位置

		if position then
			obj.transform.position = position
		end

		if scale then
			obj.transform:SetLocalScale(scale, scale, scale)
		end

		if deliverer_position then
			local direction = deliverer_position - position
			-- local direction = math.abs(position - deliverer_position)
            direction.y = 0;
            obj.transform:SetPositionAndRotation(
                    position, Quaternion.LookRotation(direction));
		end

		local control = obj:GetOrAddComponent(typeof(EffectControl))
		if control == nil then
			async_loader:Destroy()
			print_error("手动增加EffectControl组件，或者改用PlayEffect接口", bundle, asset)
			return
		end

		control:Reset()
		control:WaitFinsh(function()
			async_loader:Destroy()
			if nil ~= finish_callback then
				finish_callback()
			end
		end)

		control:Play()
		if nil ~= call_back then
			call_back(obj)
		end
	end)
end

function EffectManager:PlayCommonSuccessEffect(root, pos, scale, audio_name, no_audio, success_type,duration)
	if IsNil(root.gameObject) then return end

	local bundle_name, asset_name = ResPath.GetEffectUi(success_type or "UI_jinjichenggong")
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, root.transform, duration, pos, nil, scale or Vector3(0.8,0.8,0.8))
	if not no_audio then
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(audio_name or "Advanced"))
	end
end