------------------------------------------------------------
--物品tip
------------------------------------------------------------
SPDisplayItemTip = SPDisplayItemTip or BaseClass(DisplayItemTip)

function SPDisplayItemTip:__init()
	if SPDisplayItemTip.Instance then
		ErrorLog("[SPDisplayItemTip] Attemp to create a singleton twice !")
	end
	SPDisplayItemTip.Instance = self

	self.view_name = "SPDisplayItemTip"
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:InitLoadUIConfig()
end

function SPDisplayItemTip:InitLoadUIConfig()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_background_common_panel")
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_itemtip", {vector2 = Vector2(160, 0)})
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function SPDisplayItemTip:__delete()
	SPDisplayItemTip.Instance = nil
end

function SPDisplayItemTip:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_txxy_bj3")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self:InitTipsPanel()
end

function SPDisplayItemTip:InitTipsPanel()
	self.base_tips = BaseTip.New(self.node_list["base_tip_root"])
	self.item_cell = self.base_tips:GetItemCell()
	self.item_cell:SetIsShowTips(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function SPDisplayItemTip:ShowIndexCallBack()
	self:FlushView()

	self.base_tips:SetLeftShowModelDiVisible(false)
	self.base_tips.node_list.top_color_bg:SetActive(false)
	self.base_tips.node_list.center_root.image.enabled = false
	self.base_tips.node_list.scroll_bar.image.enabled = false
	self.base_tips.node_list.scroll_handle.image.enabled = false
	self.base_tips.node_list.sp_display_bg:SetActive(true)
end

function SPDisplayItemTip:SetData(item_id)
	self.data = {item_id = item_id}
	self:Open()
end