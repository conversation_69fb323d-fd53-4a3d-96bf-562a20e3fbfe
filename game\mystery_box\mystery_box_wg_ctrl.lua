require("game/mystery_box/mystery_box_wg_data")
require("game/mystery_box/mystery_box_view")
require("game/mystery_box/mystery_point_shop_view")
require("game/mystery_box/mystery_box_gailv_view")

MysteryBoxWGCtrl = MysteryBoxWGCtrl or BaseClass(BaseWGCtrl)

function MysteryBoxWGCtrl:__init()
	if MysteryBoxWGCtrl.Instance then
		ErrorLog("[MysteryBoxWGCtrl] attempt to create singleton twice!")
		return
	end

	MysteryBoxWGCtrl.Instance = self
    
	self.data = MysteryBoxWGData.New()
    self.view = MysteryBoxView.New(GuideModuleName.MysteryBoxView)
    
    self.point_shop = MysteryPointShopView.New()
    self.gailv_view = MysteryBoxGailvView.New()

    self.alert = nil
    self:RegisterAllProtocols()

    self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
end

function MysteryBoxWGCtrl:__delete()
	MysteryBoxWGCtrl.Instance = nil

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
    
    if self.point_shop then
        self.point_shop:DeleteMe()
        self.point_shop = nil
    end

    if self.gailv_view then
        self.gailv_view:DeleteMe()
        self.gailv_view = nil
    end

    if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end
end

function MysteryBoxWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCBlindBoxCardAllInfo, "OnSCBlindBoxCardAllInfo")
	self:RegisterProtocol(SCBlindBoxCardDrawInfo, "OnDrawInfo")
	self:RegisterProtocol(SCBlindBoxCardCountRewardInfo, "OnDrawTaskFlagInfo")
	self:RegisterProtocol(SCBlindBoxCardHandbookTaskInfo, "OnHandbookTaskInfo")
	self:RegisterProtocol(SCBlindBoxCardConvertInfo, "OnCovertInfo")
	self:RegisterProtocol(SCBlindBoxCardRecordInfo, "OnRecordInfo")
end

function MysteryBoxWGCtrl:OnSCBlindBoxCardAllInfo(protocol)
    -- print_error("OnSCBlindBoxCardAllInfo", protocol)
    self.data:SetMysterBoxAllData(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.point_shop:IsOpen() then
        self.point_shop:Flush()
    end

    RemindManager.Instance:Fire(RemindName.MysteryBox)
end

function MysteryBoxWGCtrl:OnDrawInfo(protocol)
    -- print_error("OnDrawInfo", protocol)
    self.data:SetDrawInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "FlushDrawInfo")
    end

    if self.point_shop:IsOpen() then
        self.point_shop:Flush()
    end

    self:ShowGetReward()

    RemindManager.Instance:Fire(RemindName.MysteryBox)
end

--弹出恭喜获得界面
function MysteryBoxWGCtrl:ShowGetReward()
    local package_id = self.data:GetCurPackageId()
    local cfg = self.data:GetDrawPackageCfgBySeq(package_id)
    if IsEmptyTable(cfg) then
        return
    end

    local bao_di_list = self.data:GetDrawBaodiList()
    local id_list = self.data:GetDrawResultList()

    --组装other_info
    local other_info = {}
    other_info.times = #bao_di_list + #id_list
    other_info.stuff_id = cfg.consume_item_id
    other_info.spend = cfg.consume_price

    local best_data = nil
    if #bao_di_list == 1 then
        best_data = {item_id = bao_di_list[1], best_des = ""}
    elseif #bao_di_list > 1 then
        best_data = {item_ids = bao_di_list}
    end
    other_info.best_data = best_data

    local index = MysteryBoxWGData.DRAW_NUM_TO_INDEX[other_info.times]
    other_info.again_text = Language.TreasureHunt.BtnText[index]

    local again_func = function()
        self:ClickDraw(package_id, other_info.times)
    end
    TipWGCtrl.Instance:ShowGetCommonReward(id_list, again_func, other_info, true)


    if #bao_di_list > 0 then
        MysteryBoxWGCtrl.Instance:SendReq(MYSTERY_BOX_OPERATE_TYPE.LOG)
    end
end

function MysteryBoxWGCtrl:OnDrawTaskFlagInfo(protocol)
    -- print_error("OnDrawTaskFlagInfo", protocol)
    self.data:SetDrawTaskFlagList(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "FlushFoldView")
    end
end

function MysteryBoxWGCtrl:OnHandbookTaskInfo(protocol)
    --print_error("OnHandbookTaskInfo", protocol)
    self.data:SetHandbookTaskFlagList(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "FlushFoldView")
    end
end

function MysteryBoxWGCtrl:OnCovertInfo(protocol)
    -- print_error("OnCovertInfo", protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "FlushShopRemind")
    end

    self.data:SetCovertInfo(protocol)
    if self.point_shop:IsOpen() then
        self.point_shop:Flush()
    end


    RemindManager.Instance:Fire(RemindName.MysteryBox)
end

function MysteryBoxWGCtrl:OnRecordInfo(protocol)
    -- print_error("OnRecordInfo", protocol)
    self.data:SetLogList(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "FlushLog")
    end
end

function MysteryBoxWGCtrl:SendReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_BLIND_BOX_CARD
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
    --print_error("SendReq", protocol)
end

--点击抽奖
function MysteryBoxWGCtrl:ClickDraw(seq, draw_num)
    --数量检测
   local cfg = self.data:GetDrawPackageCfgBySeq(seq)
   if IsEmptyTable(cfg) then
        return
   end
   local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.consume_item_id)
   local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item_id)

   --抽奖道具不足弹窗
   if item_num < draw_num then
        local buy_num = draw_num - item_num
        local consume = cfg.consume_price * (draw_num - item_num)

        local func = function() 
            --检查仙玉
            local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
            if enough then
                local opera_type = MYSTERY_BOX_OPERATE_TYPE.DRAW
                self:SendReq(opera_type, seq, draw_num)
            else
                VipWGCtrl.Instance:OpenTipNoGold()
            end
        end


        if not self.alert then
            self.alert = Alert.New()
        end
        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "MysterBoxNoGold")
        self.alert:SetCheckBoxDefaultSelect(false)
        local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local str = string.format(Language.MysteryBox.NoGoldStr, name, consume, buy_num, cfg.seq_name, draw_num)

        self.alert:SetLableString(str)
        self.alert:SetOkFunc(func)
        self.alert:Open()
   else
       --抽奖道具足够弹确认抽奖弹框
       self:OpenDrawAlert(seq, draw_num, item_cfg.name, cfg.seq_name)
   end
end

function MysteryBoxWGCtrl:OpenDrawAlert(seq, draw_num, item_name, package_name)
    if not self.alert then
        self.alert = Alert.New()
    end
    self.alert:ClearCheckHook()
    self.alert:SetShowCheckBox(true, "MysterBoxDraw")
    self.alert:SetCheckBoxDefaultSelect(false)
    local str = string.format(Language.MysteryBox.DrawStr, draw_num, item_name, package_name,draw_num)
    self.alert:SetLableString(str)
    self.alert:SetOkFunc(function ()
        local opera_type = MYSTERY_BOX_OPERATE_TYPE.DRAW
        self:SendReq(opera_type, seq, draw_num)
    end)
    self.alert:Open()
end

function MysteryBoxWGCtrl:OpenPointShop()
    self.point_shop:Open()
end

function MysteryBoxWGCtrl:OpenGailvView()
    local data_list = self.data:GetGailvShowData()
    self.gailv_view:SetDataAndOpen(data_list)
end

function MysteryBoxWGCtrl:OnItemDataChange(change_item_id)
    if self.data:IsMysteryBoxConsumeId(change_item_id) and self.view:IsOpen() then
        self.view:Flush(0, "FlushConsumeItem")
    end
end