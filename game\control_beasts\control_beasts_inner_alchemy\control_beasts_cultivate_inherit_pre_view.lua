ControlBeastsInheritPreView = ControlBeastsInheritPreView or BaseClass(SafeBaseView)

function ControlBeastsInheritPreView:__init()
	self:SetMaskBg(true, true)
	local bundle_name = "uis/view/control_beasts_alchemy_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, -8), sizeDelta = Vector2(814, 578)})
    self:AddViewResource(0, bundle_name, "layout_beasts_alchemy_inherit_pre")
end

function ControlBeastsInheritPreView:ReleaseCallBack()
	self.pre_left_data = nil
	self.pre_right_data = nil

	if self.alchemy_inherit_left then
        self.alchemy_inherit_left:DeleteMe()
        self.alchemy_inherit_left = nil
    end

    if self.alchemy_inherit_right then
        self.alchemy_inherit_right:DeleteMe()
        self.alchemy_inherit_right = nil
    end
end

function ControlBeastsInheritPreView:SetInheritPreData(pre_left_data, pre_right_data)
	self.pre_left_data = pre_left_data
	self.pre_right_data = pre_right_data
end

function ControlBeastsInheritPreView:LoadCallBack()
	if not self.alchemy_inherit_left then
        self.alchemy_inherit_left = AlchemyInheritPreRender.New(self.node_list.alchemy_inherit_left)
    end

    if not self.alchemy_inherit_right then
        self.alchemy_inherit_right = AlchemyInheritPreRender.New(self.node_list.alchemy_inherit_right)
    end
	self.node_list.title_view_name.text.text = Language.ContralBeastsAlchemy.AlchemyInheritTitle

	XUI.AddClickEventListener(self.node_list.alchemy_inherit_btn, BindTool.Bind2(self.OnClickAlchemyInheritBtn, self))
end

function ControlBeastsInheritPreView:OnFlush(param_t)
	if (not self.pre_left_data) or (not self.pre_right_data) then
		return
	end

	self.alchemy_inherit_left:SetData(self.pre_left_data)
	self.alchemy_inherit_right:SetInherStuffData(self.pre_right_data)
	self.alchemy_inherit_right:SetData(self.pre_left_data)
end

-- 丹药点击传承
function ControlBeastsInheritPreView:OnClickAlchemyInheritBtn()
    local left_data = self.pre_left_data
    local right_data = self.pre_right_data
    
    if left_data == nil or right_data == nil then
        return
    end

    local left_grid_index = left_data.hole_id ~= nil and left_data.hole_id or left_data.equip_info.index
    ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateInheritance(
        left_data.hole_id or -1, 
        left_data.slot_id or -1, 
        left_grid_index or -1,
        right_data.equip_info.index
    )
	self:Close()
end

----------------------------------------AlchemyInheritPreRender---------------------------------
AlchemyInheritPreRender = AlchemyInheritPreRender or BaseClass(BaseRender)
function AlchemyInheritPreRender:LoadCallBack()
    if not self.inherit_item then
        self.inherit_item = ItemCell.New(self.node_list.inherit_item)
        self.inherit_item:SetIsShowTips(false)
    end

    -- 附加词条
    if self.alchemy_additional_list == nil then
        self.alchemy_additional_list = {}
        for i = 1, 4 do
            local attr_obj = self.node_list.alchemy_additional_list:FindObj(string.format("additional_0%d", i))
            if attr_obj then
                local cell = AlchemyAdditionalRender.New(attr_obj)
                cell:SetIndex(i)
                self.alchemy_additional_list[i] = cell
            end
        end
    end
end

function AlchemyInheritPreRender:ReleaseCallBack()
    if self.succinct_item then
        self.succinct_item:DeleteMe()
        self.succinct_item = nil
    end

    if self.alchemy_additional_list and #self.alchemy_additional_list > 0 then
		for _, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
			alchemy_additional_cell:DeleteMe()
			alchemy_additional_cell = nil
		end

		self.alchemy_additional_list = nil
	end
end

-- 设置材料数据
function AlchemyInheritPreRender:SetInherStuffData(stuff_data)
	local equip_info = stuff_data and stuff_data.equip_info or {}
    local words_list = equip_info and equip_info.words_list or {}
	self.inherit_words_list = words_list
end

function AlchemyInheritPreRender:OnFlush()
    if not self.data then
        return 
    end

    local is_empty_data = IsEmptyTable(self.data)

    if is_empty_data then
        return
    end

    -- 不是材料设置数据
    local is_has_data = false
    local is_has_additional = false
    local equip_info = self.data and self.data.equip_info or {}
    local words_list = equip_info and equip_info.words_list or {}
	local final_word_list = self.inherit_words_list or words_list
    local words_start_index = COMMON_CONSTS.NUMBER_ZERO
	local score = 0

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        local word_data = final_word_list[words_start_index]
        local final_words_seq = word_data.words_seq
		local random_value = final_words_seq % 100
		local real_seq = math.floor(final_words_seq / 100)
        alchemy_additional_cell:SetVisible(word_data ~= nil and final_words_seq ~= -1)

        if word_data ~= nil and final_words_seq ~= -1 then
            is_has_additional = true
            alchemy_additional_cell:ChangeNowlockStatus(false)
            alchemy_additional_cell:SetData(word_data)
        end

		local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)
		if cfg then
			-- 词条评分
			score = score + ((cfg.entry_score or 0) * (random_value / 100))
		end

        words_start_index = words_start_index + 1
    end

	if self.node_list.succinct_score_arrow then
		local compare_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(self.data.equip_info)
		local str = score > compare_score and "a3_ty_up_1" or "a3_ty_down_1"
		self.node_list.succinct_score_arrow:CustomSetActive(score ~= compare_score)
		self.node_list.succinct_score_arrow.image:LoadSprite(ResPath.GetCommon(str))
	end

	self.node_list.succinct_name_score.text.text = score
    self:FlushEquipDataMessage()
end

-- 刷新物品数据
function AlchemyInheritPreRender:FlushEquipDataMessage()
    if not self.data then
        return 
    end

    local data = self.data
    local equip_item_id = (data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    if equip_item_id ~= 0 then
        local item_data = {
            item_id = equip_item_id, 
            equip_info = data.equip_info,               -- 穿戴内丹装备数据
            is_bag_equip = false,                       -- 穿戴内丹背包点击
            is_wear = data.is_wear,                     -- 是否是已穿戴
            fight_slot = self.data.hole_id,             -- 出战位置
            equip_slot = self.data.slot_id,             -- 出战位置孔位
            equip_slot_lv = self.data.slot_lv,          -- 出战位置孔位等级
            is_wear_equip = self.data.is_wear_equip,    -- 是否展示装备字样
        }

        self.inherit_item:SetData(item_data)  
        local item_cfg = ItemWGData.Instance:GetItemConfig(equip_item_id)
        self.node_list.inherit_name.text.text = ToColorStr(item_cfg and item_cfg.name or "", ITEM_COLOR[item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO])
    end
end
