{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8003_attack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8003/boss8003_attack_prefab", "AssetName": "boss8003_attack", "AssetGUID": "5b4d36f6b0e02894680eefdce094b8d4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8003_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8003/boss8003_skill_prefab", "AssetName": "boss8003_skill", "AssetGUID": "81cf56dbbf6216540b587954aeff8100", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.4, "triggerFreeDelay": 0.0, "effectGoName": "eff_hit_zi", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_hit_zi_prefab", "AssetName": "eff_hit_zi", "AssetGUID": "d698f12751fd79a4a9609c4e601cfb66", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1_hit", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 1.4, "triggerFreeDelay": 0.0, "effectGoName": "eff_hit_zi", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_hit_zi_prefab", "AssetName": "eff_hit_zi", "AssetGUID": "d698f12751fd79a4a9609c4e601cfb66", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2_hit", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_attack1", "AssetGUID": "b7f8ea64a2d800f4783f89d2670b9c42", "IsEmpty": false}, "soundAudioGoName": "role4_attack1", "soundBtnName": "attack1", "soundIsMainRole": false}], "cameraShakes": [], "radialBlurs": []}}