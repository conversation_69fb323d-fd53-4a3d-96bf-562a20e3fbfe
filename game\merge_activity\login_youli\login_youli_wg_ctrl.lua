require("game/merge_activity/login_youli/login_youli_wg_data")
require("game/merge_activity/login_youli/login_youli_item")
--运营活动-登录有礼
LoginYouLiWGCtrl = LoginYouLiWGCtrl or BaseClass(BaseWGCtrl)

function LoginYouLiWGCtrl:__init()
	if LoginYouLiWGCtrl.Instance then
		ErrorLog("[LoginYouLiWGCtrl] Attemp to create a singleton twice !")
	end
	LoginYouLiWGCtrl.Instance = self

	self:RegisterAllProtocols()
    self.data = LoginYouLiWGData.New()
end

function LoginYouLiWGCtrl:__delete()
	LoginYouLiWGCtrl.Instance = nil

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end
end

function LoginYouLiWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSCServerActLoginGift)
    self:RegisterProtocol(SCCServerActLoginGiftInfo,'OnSCCServerActLoginGiftInfo')
end

--登录奖励信息
function LoginYouLiWGCtrl:OnSCCServerActLoginGiftInfo(protocol)
	self.data:SetLoginRewardInfo(protocol)
	MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT)
    MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2248, "RefreshLogin")
	RemindManager.Instance:Fire(RemindName.MergeLoginRward)
end

function LoginYouLiWGCtrl:SendActivityRewardOp(opera_type,param1)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSCServerActLoginGift)
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol:EncodeAndSend()
end