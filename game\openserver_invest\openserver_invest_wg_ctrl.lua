require("game/openserver_invest/openserver_invest_wg_data")
require("game/openserver_invest/openserver_invest_view")
require("game/openserver_invest/openserver_invest_reward_show_view")

OpenServerInvestWGCtrl = OpenServerInvestWGCtrl or BaseClass(BaseWGCtrl)

function OpenServerInvestWGCtrl:__init()
	if OpenServerInvestWGCtrl.Instance ~= nil then
		error("[OpenServerInvestWGCtrl] attempt to create singleton twice!")
		return
	end

	OpenServerInvestWGCtrl.Instance = self
	self.data = OpenServerInvestWGData.New()
	self.view = OpenServerInvestView.New(GuideModuleName.OpenServerInvestView)
	self.reward_show_view = OpenServerInvestRewardShowView.New()

	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.OnDayChange, self))
	if not self.level_change then
		self.level_change = BindTool.Bind(self.RoleDataChangeCallBack, self)
		RoleWGData.Instance:NotifyAttrChange(self.level_change, {"level"})
	end

	self:RegisterAllProtocols()
end

function OpenServerInvestWGCtrl:__delete()
	OpenServerInvestWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.reward_show_view then
		self.reward_show_view:DeleteMe()
		self.reward_show_view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.level_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.level_change)
		self.level_change = nil
	end

	if self.capability_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.capability_change)
		self.capability_change = nil
	end
end

-- 注册协议
function OpenServerInvestWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSOGAOperate)
    self:RegisterProtocol(SCOGAInvestInfo, "OnAllInfo")
end

--生成开服投资主界面按钮
function OpenServerInvestWGCtrl:CreatMainUiInvestBtn(mainbtn_type, is_creat)
	if is_creat == 0 then
		MainuiWGCtrl.Instance:InvateTip(mainbtn_type, 0)
	else
		MainuiWGCtrl.Instance:InvateTip(mainbtn_type, 1, function()
			ViewManager.Instance:Open(GuideModuleName.OpenServerInvestView)
			return true
		end)
	end
end

function OpenServerInvestWGCtrl:OpenRewardShowView(type)
	self.reward_show_view:SetType(type)
	if self.reward_show_view:IsOpen() then
		self.reward_show_view:Flush()
	else
		self.reward_show_view:Open()
	end
end

function OpenServerInvestWGCtrl:SendOpenServerInvestReq(operate_type, param1, param2, param3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSOGAOperate)
	send_protocol.act_index = 12	-- 固定12
	send_protocol.operate_type = operate_type or 0
	send_protocol.param1 = param1 or self.view.cur_type or 0
    send_protocol.param2 = param2 or 0
	send_protocol.param3 = param3 or 0
	send_protocol:EncodeAndSend()
end

function OpenServerInvestWGCtrl:OnAllInfo(protocol)
    if self.view:IsOpen() then
		local cur_type = self.view.cur_type
		local reward_flag, rmb_reward_flag = OpenServerInvestWGData.Instance:GetRewardFlagByType(cur_type)
		if protocol.reward_flag[cur_type] ~= reward_flag or
		   protocol.rmb_reward_flag[cur_type] ~= rmb_reward_flag then
			self.view:SetJumpRewardflag(true)
		end
		self.data:SetAllInfo(protocol)
		self.view:Flush()
	else
		self.data:SetAllInfo(protocol)
	end

	if self.reward_show_view:IsOpen() then
		self.reward_show_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.OpenServerInvestView)
end

--天数改变
function OpenServerInvestWGCtrl:OnDayChange()
	self:CheckActIsOpen()
end

function OpenServerInvestWGCtrl:RoleDataChangeCallBack(key, value, old_value)
	if key == "level" then
		self:CheckActIsOpen()
	end
end

function OpenServerInvestWGCtrl:CapabilityChangeCallBack(key, value, old_value)
	if key == "capability" then
		if self.view:IsOpen() then
			self.view:Flush()
		end
		RemindManager.Instance:Fire(RemindName.OpenServerInvestView)
	end
end

function OpenServerInvestWGCtrl:CheckActIsOpen()
	local is_open = OpenServerInvestWGData.Instance:AllActIsOpen()
	local status = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER_INVEST, status)

	if is_open then
		self:SendOpenServerInvestReq(OPENSERVER_INVEST_OPERATE_TYPE.OGA_INVEST_OPERATE_TYPE_INFO)
		if not self.capability_change then
			self.capability_change = BindTool.Bind(self.CapabilityChangeCallBack, self)
			RoleWGData.Instance:NotifyAttrChange(self.capability_change, {"capability"})
		end
	else
		if self.capability_change then
			RoleWGData.Instance:UnNotifyAttrChange(self.capability_change)
			self.capability_change = nil
		end

		if self.view:IsOpen() then
			self.view:Close()
		end
	end
end