TaskCallInfoObj = TaskCallInfoObj or BaseClass(FollowObj)

function TaskCallInfoObj:__init(vo)
	self.obj_type = SceneObjType.TaskCallInfoObj
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.TaskCallInfoObj
	self.shield_effect_type = ShieldObjType.TaskCallInfoEffect


    self.task_callinfo_id = vo.task_callinfo_id
	self.model_res_id = nil
	self.unit_distance = 2 * 50		-- 单位距离速度
	self.mass = 0.3
	self.sqrt_slow_down_distance = 4 * 4
	self:SetMaxSpeed(26)
	self.max_move_offset = 5

	-- 移动至目标所用（战斗移动）
	self.move_skill_id = nil
	self.target_x = nil
	self.target_y = nil
	self.target_obj_id = nil

	-- 是否随机漫步
	self.is_wander = false

	self.is_weapon_anim = false
	self:InitInfo()
	self:InitAppearance()
end

function TaskCallInfoObj:__delete()
	local owner_name = ""

	self.owner_obj = nil
	self.bundle_name = ""
	self.asset_name = ""
	self.model_res_id = nil

	self:ResetFightData()
	self.delete_traceback = owner_name .. "  " .. debug.traceback()
end

-- 重置战斗移动参数
function TaskCallInfoObj:ResetFightData()
	-- 移动至目标所用（战斗移动）
	self.move_skill_id = nil
	self.target_x = nil
	self.target_y = nil
	self.target_obj_id = nil
end

function TaskCallInfoObj:InitInfo()
	FollowObj.InitInfo(self)
	self:GetFollowUi()
	self:ReloadUIName()
end

function TaskCallInfoObj:IsWeaponOwnAnim()
    return self.is_weapon_anim
end

function TaskCallInfoObj:InitAppearance()
	local cfg = TaskWGData.Instance:GetTaskCallInfoCfg()
	local res_id = cfg and cfg.model_id or 0
	local weapon_res_id = cfg and cfg.weapon_id or -1

	self.is_weapon_anim = weapon_res_id > 0
	if res_id and res_id > 0 then
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", res_id))
	end

	local sync_anim_type = SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN
	self:ChangeObjWaitSyncAnimType(sync_anim_type)

	if res_id > 0 then
		local bundle, asset = ResPath.GetBianShenModel(res_id)
		self:ChangeModel(SceneObjPart.Main, bundle, asset)
	end

	if res_id > 0 and weapon_res_id > 0 then
		self:ChangeModel(SceneObjPart.Weapon, ResPath.GetTianShenShenQiPath(weapon_res_id))
	else
		self:RemoveModel(SceneObjPart.Weapon)
	end
end

function TaskCallInfoObj:CreateFollowUi()
    self.follow_ui = TaskCallInfoObjFollow.New(self.vo)
    self.follow_ui:OnEnterScene(self.is_enter_scene)
    self.follow_ui:Create(SceneObjType.TaskCallInfoObj)
end

function TaskCallInfoObj:ReloadUIName()
	if self.follow_ui ~= nil and self.owner_obj ~= nil then
		local role_name = self.owner_obj:GetName()
		local cfg = TaskWGData.Instance:GetTaskCallInfoCfg()
		local name_str = cfg and cfg.name or ""
		self.follow_ui:SetName(name_str, self)
	end
end

function TaskCallInfoObj:TryFlushAppearance()
	self:InitAppearance()
	self:ReloadUIName()
end

function TaskCallInfoObj:OnEnterScene()
	FollowObj.OnEnterScene(self)
	self:CreateShadow()
end

function TaskCallInfoObj:EnterStateAttack(anim_name)
	local anim_name = SceneObjAnimator.Atk1
	local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
	if nil ~= skill_cfg then
		anim_name = "attack" .. skill_cfg.action
	end

	FollowObj.EnterStateAttack(self, anim_name)
end

function TaskCallInfoObj:OnAttackPlayEnd()
	if not self.owner_obj then
		return
	end

	if self.owner_obj:IsDeleted() or self.owner_obj:IsDead() then
	else
		self:ChangeToCommonState()
	end
end

function TaskCallInfoObj:GetActionTimeRecord(anim_name)
	local cfg = TaskWGData.Instance:GetTaskCallInfoCfg()
	local res_id = cfg and cfg.model_id or 0
	if res_id > 0 then
		local atr = TianShenBossActionConfig[res_id]
		if atr then
			return atr[anim_name]
		end
	end

	return nil
end

-- 战斗屏蔽随机移动
function TaskCallInfoObj:ShieldWadnerForce(target)
	return self:IsFightState() or not self:GetVisiable()
end

function TaskCallInfoObj:IsTaskCallInfo()
	return true
end

function TaskCallInfoObj:IsOnwerPartner()
	return self.owner_objid == GameVoManager.Instance:GetMainRoleVo().obj_id
end

function TaskCallInfoObj:SetOwnerObj(owner_obj)
	self.owner_obj = owner_obj
end

function TaskCallInfoObj:PartEffectVisibleChanged(part, visible)
	if self:IsDeleted() then
		return
	end
	
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function TaskCallInfoObj:UpdateQualityLevel()
	if self:IsDeleted() then
		return
	end

	local base_offset = -1
	local owner_obj = self:GetOwnerObj()
	if owner_obj and owner_obj.IsMainRole and owner_obj:IsMainRole() then
		base_offset = 1
	end

	local model_offset = base_offset
	self:SetQualityLevelOffset(model_offset)

	local effect_offset = base_offset

	self:SetEffectQualityLevelOffset(effect_offset)

	-- 模型材质球品质偏移值。
    -- 偏移值越小则材质球品质越高

    if self.draw_obj == nil then
    	return
    end
end

function TaskCallInfoObj:CreateShadow()
	FollowObj.CreateShadow(self)
	
	if self.shadow then
		self.shadow:AddShieldRule(ShieldRuleWeight.Max, function()
			return not self:GetVisiable()
		end)
	end
end

function TaskCallInfoObj:TryUsePartnerSkill(skill_id, target_x, target_y, target_obj_id)
	if self:IsDeleted() then
		return
	end

	if not target_x or not target_y or not target_obj_id then
		return
	end

	local target_obj = GuajiCache.target_obj
	if not target_obj or target_obj:IsDeleted() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local skill_cfg = SkillWGData.Instance:GetResidentSkillById(skill_id, 1)
	if not skill_cfg then
		return
	end

	--local target_x, target_y = target_obj:GetLogicPos()
	local m_pos_x, m_pos_y = self:GetLogicPos()
	local target_dis = GameMath.GetDistance(m_pos_x, m_pos_y, target_x, target_y)
	local skill_dis = skill_cfg.distance or 1

	if target_dis <= skill_dis * skill_dis then
		SceneObj.SetDirectionByXY(self, target_x, target_y)
		self:StopMove()
		self:DoAttack(skill_id, target_x, target_y, target_obj_id)
	else
		self:StopMove()
		-- 移动至目标所用（战斗移动）
		self.move_skill_id = skill_id
		self.target_x = target_x
		self.target_y = target_y
		self.target_obj_id = target_obj_id

		self.speed = ((target_dis - skill_dis) / self.sqrt_max_distance) * self.unit_distance
		Character.DoMove(self, target_x, target_y, OBJ_MOVE_REASON.TASK_CALLINFO_FIGHT)
	end

	self:EnterFight()
end

-- 检测战斗移动
function TaskCallInfoObj:Update(now_time, elapse_time)
	FollowObj.Update(self, now_time, elapse_time)

	if self:IsFightState() and self.target_obj_id ~= nil and self.move_skill_id then
		local skill_cfg = SkillWGData.Instance:GetResidentSkillById(self.move_skill_id, 1)
		if not skill_cfg then
			return
		end

		local m_pos_x, m_pos_y = self:GetLogicPos()
		local target_dis = GameMath.GetDistance(m_pos_x, m_pos_y, self.target_x, self.target_y)
		local skill_dis = skill_cfg.distance or 1

		if target_dis <= skill_dis * skill_dis then
			SceneObj.SetDirectionByXY(self, self.target_x, self.target_y)
			self:StopMove()
			self:DoAttack(self.move_skill_id, self.target_x, self.target_y, self.target_obj_id)
			self:ResetFightData()
		end
	end
end

function TaskCallInfoObj:GetCurFollowTargetRealPos()
	local target = self.owner_obj

	if target then
		local real_pos = target.real_pos
	
		local deliverer_draw_obj = target:GetDrawObj()
		if not deliverer_draw_obj then
			return real_pos
		end
	
		local role_transform = deliverer_draw_obj:GetTransfrom()
	
		if not role_transform then
			return real_pos
		end
	
		self.radius = 2
		local pos = nil
		local center_back = -role_transform.forward * self.radius
		pos = role_transform.position + center_back
	
		if not pos then
			return real_pos
		end
	
		local task_callinfo_real_pos = u3d.vec2(0, 0)
		task_callinfo_real_pos.x = pos.x
		task_callinfo_real_pos.y = pos.z
	
		return task_callinfo_real_pos
	end
end