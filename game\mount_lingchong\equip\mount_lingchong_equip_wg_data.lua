MountLingChongEquipWGData = MountLingChongEquipWGData or BaseClass()

MountLingChongEquipWGData.MaxStar_Lv = 5

function MountLingChongEquipWGData:__init()
	if MountLingChongEquipWGData.Instance then
		Error<PERSON><PERSON>("[MountLingChongEquipWGData] attempt to create singleton twice!")
		return
	end
    MountLingChongEquipWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("mount_pet_equip_auto")
    local equip_cfg = ListToMapList(cfg.equip_cfg, "type")
    self.mount_equip_part_cfg = ListToMapList(equip_cfg[MOUNT_PET_EQUIP_TYPE.MOUNT], "part")
    self.lingchong_equip_part_cfg = ListToMapList(equip_cfg[MOUNT_PET_EQUIP_TYPE.PET], "part")
    self.huakun_equip_part_cfg = ListToMapList(equip_cfg[MOUNT_PET_EQUIP_TYPE.HUAKUN], "part")

    self.equip_id_cfg = ListToMap(cfg.equip_cfg, "equip_id")  ---以Itemid 为key

    self.mount_lingchong_other_cfg = cfg.other[1]

    local strengthen_cfg = ListToMapList(cfg.strengthen_cfg, "type")
    self.mount_strengthen_cfg = ListToMapList(strengthen_cfg[MOUNT_PET_EQUIP_TYPE.MOUNT], "part")
    self.lingchong_strengthen_cfg = ListToMapList(strengthen_cfg[MOUNT_PET_EQUIP_TYPE.PET], "part")
    self.huakun_strengthen_cfg = ListToMapList(strengthen_cfg[MOUNT_PET_EQUIP_TYPE.HUAKUN], "part")

    local up_star = ListToMapList(cfg.up_star, "type")
    self.mount_up_star_cfg = ListToMapList(up_star[MOUNT_PET_EQUIP_TYPE.MOUNT], "part")
    self.lingchong_up_star_cfg = ListToMapList(up_star[MOUNT_PET_EQUIP_TYPE.PET], "part")
    self.huakun_up_star_cfg = ListToMapList(up_star[MOUNT_PET_EQUIP_TYPE.HUAKUN], "part")

    self.compose_cfg = ListToMap(cfg.compose_cfg, "old_equip_id")

    self.suit_cfg = ListToMapList(cfg.suit_cfg, "type")

    self.strength_limit_cfg = ListToMapList(cfg.strengthen_limit, "type")

    self.get_way_cfg = ListToMapList(cfg.get_type, "type")

    self.show_model_cfg = ListToMap(cfg.show_model, "type")

    self.ningpin_cfg = ListToMap(cfg.ningpin_cfg, "type", "solt", "level")

    self.equip_wear_info = {}
    self.equip_resolve_info = {}

    RemindManager.Instance:Register(RemindName.MountEquip_Bag, BindTool.Bind(self.EquipBagRemind, self, MOUNT_PET_EQUIP_TYPE.MOUNT))
    RemindManager.Instance:Register(RemindName.MountEquip_Strength, BindTool.Bind(self.EquipStrengthRemind, self, MOUNT_PET_EQUIP_TYPE.MOUNT))
    RemindManager.Instance:Register(RemindName.MountEquip_Shengpin, BindTool.Bind(self.EquipShengpinRemind, self, MOUNT_PET_EQUIP_TYPE.MOUNT))
    RemindManager.Instance:Register(RemindName.MountEquip_Star, BindTool.Bind(self.EquipStarRemind, self, MOUNT_PET_EQUIP_TYPE.MOUNT))
    RemindManager.Instance:Register(RemindName.MountEquip_Suit, BindTool.Bind(self.EquipSuitRemind, self, MOUNT_PET_EQUIP_TYPE.MOUNT))
    RemindManager.Instance:Register(RemindName.MountEquip_Resolve, BindTool.Bind(self.EquipNingPinRemind, self, MOUNT_PET_EQUIP_TYPE.MOUNT))

    RemindManager.Instance:Register(RemindName.LingChongEquip_Bag, BindTool.Bind(self.EquipBagRemind, self, MOUNT_PET_EQUIP_TYPE.PET))
    RemindManager.Instance:Register(RemindName.LingChongEquip_Strength, BindTool.Bind(self.EquipStrengthRemind, self, MOUNT_PET_EQUIP_TYPE.PET))
    RemindManager.Instance:Register(RemindName.LingChongEquip_Shengpin, BindTool.Bind(self.EquipShengpinRemind, self, MOUNT_PET_EQUIP_TYPE.PET))
    RemindManager.Instance:Register(RemindName.LingChongEquip_Star, BindTool.Bind(self.EquipStarRemind, self, MOUNT_PET_EQUIP_TYPE.PET))
    RemindManager.Instance:Register(RemindName.LingChongEquip_Suit, BindTool.Bind(self.EquipSuitRemind, self, MOUNT_PET_EQUIP_TYPE.PET))
    RemindManager.Instance:Register(RemindName.LingChongEquip_Resolve, BindTool.Bind(self.EquipNingPinRemind, self, MOUNT_PET_EQUIP_TYPE.PET))
    --化鲲装备
    RemindManager.Instance:Register(RemindName.HuaKunEquip_Bag, BindTool.Bind(self.EquipBagRemind, self, MOUNT_PET_EQUIP_TYPE.HUAKUN))
    RemindManager.Instance:Register(RemindName.HuaKunEquip_Strength, BindTool.Bind(self.EquipStrengthRemind, self, MOUNT_PET_EQUIP_TYPE.HUAKUN))
    RemindManager.Instance:Register(RemindName.HuaKunEquip_Shengpin, BindTool.Bind(self.EquipShengpinRemind, self, MOUNT_PET_EQUIP_TYPE.HUAKUN))
    RemindManager.Instance:Register(RemindName.HuaKunEquip_Star, BindTool.Bind(self.EquipStarRemind, self, MOUNT_PET_EQUIP_TYPE.HUAKUN))
    RemindManager.Instance:Register(RemindName.HuaKunEquip_Suit, BindTool.Bind(self.EquipSuitRemind, self, MOUNT_PET_EQUIP_TYPE.HUAKUN))
    RemindManager.Instance:Register(RemindName.HuaKunEquip_Resolve, BindTool.Bind(self.EquipNingPinRemind, self, MOUNT_PET_EQUIP_TYPE.HUAKUN))
    
    self:RegisterStarRemindInBag(RemindName.MountEquip_Star, MOUNT_PET_EQUIP_TYPE.MOUNT)
    self:RegisterStarRemindInBag(RemindName.LingChongEquip_Star, MOUNT_PET_EQUIP_TYPE.PET)
    self:RegisterStarRemindInBag(RemindName.HuaKunEquip_Star, MOUNT_PET_EQUIP_TYPE.HUAKUN)
    self:RegisterStrengthRemindInBag(RemindName.MountEquip_Strength, MOUNT_PET_EQUIP_TYPE.MOUNT)
    self:RegisterStrengthRemindInBag(RemindName.LingChongEquip_Strength, MOUNT_PET_EQUIP_TYPE.PET)
    self:RegisterStrengthRemindInBag(RemindName.HuaKunEquip_Strength, MOUNT_PET_EQUIP_TYPE.HUAKUN)
end

function MountLingChongEquipWGData:__delete()
    MountLingChongEquipWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.MountEquipTotal)
    RemindManager.Instance:UnRegister(RemindName.MountEquip_Bag)
    RemindManager.Instance:UnRegister(RemindName.MountEquip_Strength)
    RemindManager.Instance:UnRegister(RemindName.MountEquip_Shengpin)
    RemindManager.Instance:UnRegister(RemindName.MountEquip_Star)
    RemindManager.Instance:UnRegister(RemindName.MountEquip_Suit)
    RemindManager.Instance:UnRegister(RemindName.MountEquip_Resolve)

    RemindManager.Instance:UnRegister(RemindName.LingChongEquipTotal)
    RemindManager.Instance:UnRegister(RemindName.LingChongEquip_Bag)
    RemindManager.Instance:UnRegister(RemindName.LingChongEquip_Strength)
    RemindManager.Instance:UnRegister(RemindName.LingChongEquip_Shengpin)
    RemindManager.Instance:UnRegister(RemindName.LingChongEquip_Star)
    RemindManager.Instance:UnRegister(RemindName.LingChongEquip_Suit)
    RemindManager.Instance:UnRegister(RemindName.LingChongEquip_Resolve)

    RemindManager.Instance:UnRegister(RemindName.HuaKunEquipTotal)
    RemindManager.Instance:UnRegister(RemindName.HuaKunEquip_Bag)
    RemindManager.Instance:UnRegister(RemindName.HuaKunEquip_Strength)
    RemindManager.Instance:UnRegister(RemindName.HuaKunEquip_Shengpin)
    RemindManager.Instance:UnRegister(RemindName.HuaKunEquip_Star)
    RemindManager.Instance:UnRegister(RemindName.HuaKunEquip_Suit)
    RemindManager.Instance:UnRegister(RemindName.HuaKunEquip_Resolve)


    self.suit_type_cfg = nil
end


function MountLingChongEquipWGData:GetIsFun(fun_name)
    return fun_name == "LingChongEquipView" or fun_name == "MountEquipView"
end

function MountLingChongEquipWGData:GetOtherCfg()
    return self.mount_lingchong_other_cfg
end

function MountLingChongEquipWGData:FlushMainInvateTip(show_type)
    if self:EquipBagRemind(show_type) == 1 then
        self:ShowMainuiIcon(show_type, true, 10)
        return
    end
    if self:EquipStrengthRemind(show_type) == 1 then
        self:ShowMainuiIcon(show_type, true, 20)
        return
    end
    if self:EquipShengpinRemind(show_type) == 1 then
        self:ShowMainuiIcon(show_type, true, 30)
        return
    end
    if self:EquipStarRemind(show_type) == 1 then
        self:ShowMainuiIcon(show_type, true, 40)
        return
    end
    if self:EquipSuitRemind(show_type) == 1 then
        self:ShowMainuiIcon(show_type, true, 50)
        return
    end
    self:ShowMainuiIcon(show_type, false)
	return 0
end

--功能提示
function MountLingChongEquipWGData:ShowMainuiIcon(show_type, flag, tabindex)
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local view_name, mainui_tip_type
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
        view_name = GuideModuleName.MountEquipView
        mainui_tip_type = MAINUI_TIP_TYPE.Mount_Equip
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        view_name = GuideModuleName.HuaKunEquipView
        mainui_tip_type = MAINUI_TIP_TYPE.HuaKun_Equip
    else
        view_name = GuideModuleName.LingChongEquipView
        mainui_tip_type = MAINUI_TIP_TYPE.Pet_Equip
    end

    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
        if role_lv < self.mount_lingchong_other_cfg.mount_opne_level then
            MainuiWGCtrl.Instance:InvateTip(mainui_tip_type,0)
            return
        end
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then--化鲲装备
        if self.mount_lingchong_other_cfg.kun_open_level and role_lv < self.mount_lingchong_other_cfg.kun_open_level then
            MainuiWGCtrl.Instance:InvateTip(mainui_tip_type,0)
            return
        end
    else 
        if role_lv < self.mount_lingchong_other_cfg.pet_open_level then
            MainuiWGCtrl.Instance:InvateTip(mainui_tip_type,0)
            return
        end
    end

    local is_open = FunOpen.Instance:GetFunIsOpened(view_name)
    if flag and view_name and is_open then
        MainuiWGCtrl.Instance:InvateTip(mainui_tip_type, 1, function ()
            FunOpen.Instance:OpenViewByName(view_name, tabindex)
            return true
        end)
    else
        MainuiWGCtrl.Instance:InvateTip(mainui_tip_type,0)
    end
end

function MountLingChongEquipWGData:RegisterStrengthRemindInBag(remind_name, show_type)
    local map = {}
    local cfg = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		cfg = self.mount_strengthen_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		cfg = self.lingchong_strengthen_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        cfg = self.huakun_strengthen_cfg
    end
    for k, v in pairs(cfg) do
        if not IsEmptyTable(v) then
            for k1, v1 in pairs(v) do
                map[v1.stuff_id] = true
            end
        end
    end
    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function MountLingChongEquipWGData:RegisterStarRemindInBag(remind_name, show_type)
    local map = {}

    local cfg = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		cfg = self.mount_up_star_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		cfg = self.lingchong_up_star_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        cfg = self.huakun_up_star_cfg
    end
    for k, v in pairs(cfg) do
        for k1, v1 in pairs(v) do
            map[v1.consume_stuff] = true
        end
    end
    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function MountLingChongEquipWGData:EquipBagRemind(show_type)
    local bag_list = self:GetBagItemDataList(show_type)
    for k, v in pairs(bag_list) do
        local is_up = self:IsBetterThanCurWearing(show_type, v.item_id)
        if is_up then
            self:ShowMainuiIcon(show_type, true, 10)
            return 1
        end
    end
	return 0
end

function MountLingChongEquipWGData:EquipStrengthRemind(show_type)
    for i = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, i)
        if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
            if self:GetEquipStrengthPartRemind(show_type, i) == 1 then
                self:ShowMainuiIcon(show_type, true, 20)
                return 1
            end
        end
    end
	return 0
end

function MountLingChongEquipWGData:GetEquipStrengthPartRemind(show_type, part)
    local info = self:GetEquipInfoByPart(show_type, part)
    if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
        local now_level = info.strengthen_level
        local is_max_level = self:IsStrengthMax(show_type, part, now_level)
        if not is_max_level then
            local is_limit = self:GetIsStrengthLimitLv(info.item_id, now_level)
            if not is_limit then
                local equip_cfg = self:GetStrengthCfgByLevel(show_type, part, now_level + 1)      
                local num = ItemWGData.Instance:GetItemNumInBagById(equip_cfg.stuff_id)
                local role_coin = RoleWGData.Instance.role_info.coin
                if num >= equip_cfg.num and role_coin >= equip_cfg.consume_coin then
                    return 1
                end
            end
        end
    end
    return 0
end

function MountLingChongEquipWGData:EquipShengpinRemind(show_type)
    local is_equip = false
    for i = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, i)
        if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
            is_equip = true
        end 
    end
    if is_equip == false then
        return 0
    end
    for i = 0, 5 do
        if self:GetPartWearCanShengPin(show_type, i) == 1 then
            self:ShowMainuiIcon(show_type, true, 30)
            return 1
        end
    end

    local data_list = self:GetShengpinMutiList(show_type)
    if #data_list > 0 then
        self:ShowMainuiIcon(show_type, true, 30)
        return 1
    end
	return 0
end

function MountLingChongEquipWGData:EquipStarRemind(show_type)
    for i = 0, 5 do
        if self:GetEquipStarPartRemind(show_type, i) == 1 then
            self:ShowMainuiIcon(show_type, true, 40)
            return 1
        end
    end
	return 0
end

function MountLingChongEquipWGData:GetEquipStarPartRemind(show_type, part)
    local i = part
    local info = self:GetEquipInfoByPart(show_type, i)
    if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
        local now_level = info.star_level
        local is_max_level = now_level >= MountLingChongEquipWGData.MaxStar_Lv
        if not is_max_level then
            local cur_cfg = self:GetStarCfgByLevel(show_type, i, now_level + 1)
            local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.consume_stuff)
            if num >= cur_cfg.num then
                return 1
            end
        end
    end
    return 0
end

function MountLingChongEquipWGData:GetEquipSuitDefaultIdx(show_type)
    local suit_list = self:GetSuitList(show_type)
    for k, v in pairs(suit_list) do
        local is_can = self:GetSuitCanActive(show_type, v.quality)
        if is_can then
            return k
        end
    end
	return 1
end

function MountLingChongEquipWGData:EquipSuitRemind(show_type)
    local suit_list = self:GetSuitList(show_type)
    for k, v in pairs(suit_list) do
        local is_can = self:GetSuitCanActive(show_type, v.quality)
        if is_can then
            self:ShowMainuiIcon(show_type, true, 50)
            return 1
        end
    end
	return 0
end

--获取当前界面展示模型id，name
function MountLingChongEquipWGData:GetCurShowModelId(show_type)
    local name, image_grade_num, display_res_id
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT or show_type == MOUNT_PET_EQUIP_TYPE.PET then--坐骑 灵宠
        local body_show_type = show_type == MOUNT_PET_EQUIP_TYPE.MOUNT and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
        local data = NewAppearanceWGData.Instance:GetQiChongInfo(body_show_type) -- protocol
        local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, data.star_level) --升星
        if upstar_cfg then
            image_grade_num = math.max(1, upstar_cfg.grade_num) --1或者阶数
            name = NewAppearanceWGData.Instance:GetBaseQiChongName(body_show_type, image_grade_num)
            display_res_id = upstar_cfg.appe_image_id
        end
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then--化鲲
        local def_show_kun_id = NewAppearanceWGData.Instance:GetKunUsedId()
        local huakun_info = NewAppearanceWGData.Instance:GetKunInfo(def_show_kun_id)
        local huakun_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(def_show_kun_id)
        if not IsEmptyTable(huakun_info) and not IsEmptyTable(huakun_cfg) then
            display_res_id = huakun_cfg.active_id
            name = huakun_cfg.name
            image_grade_num = huakun_info.level
        end
    end

    return display_res_id, name, image_grade_num
end

function MountLingChongEquipWGData:GetCanShowEquipBtn(qc_type)
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return role_lv >= self.mount_lingchong_other_cfg.mount_opne_level
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return role_lv >= self.mount_lingchong_other_cfg.pet_open_level
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self.mount_lingchong_other_cfg.kun_open_level and role_lv >= self.mount_lingchong_other_cfg.kun_open_level
    end
    return false
end

function MountLingChongEquipWGData:GetWayCfg(show_type)
    local get_way_list = {}
    local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way
    local cfg = self.get_way_cfg[show_type] or {}
    if not IsEmptyTable(cfg) then
        for k, v in pairs(cfg) do
            if v.get_type then
                local way_cfg = get_way_cfg[v.get_type]
                 if TipWGData.Instance:GetGetWayOpenFlag(way_cfg) then
                    local temp = {}
                    temp.icon = v.icon
                    temp.discription = way_cfg.discription
                    temp.open_panel = way_cfg.open_panel
                    get_way_list[#get_way_list + 1] = temp
                 end
            end
        end
    end
    return get_way_list
end

function MountLingChongEquipWGData:GetModelCfgByType(show_type)
    return self.show_model_cfg[show_type] or {}
end

function MountLingChongEquipWGData:SaveEquipBagInfo(protocol)
    if protocol.type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		self.mount_bag = protocol.grid_items
	elseif protocol.type == MOUNT_PET_EQUIP_TYPE.PET then
		self.pet_bag = protocol.grid_items
    elseif protocol.type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        self.huakun_equip_bag = protocol.grid_items
    end
end

--获取背包物品
function MountLingChongEquipWGData:GetItemIdInBagByGridIndex(show_type, indx)
    local bag = self:GetBagItemDataList(show_type)
    for k, v in pairs(bag) do
        if indx == v.grid_index then
            return v.item_id
        end
    end
end

--获取背包中数量
function MountLingChongEquipWGData:GetItemNumInBagById(show_type, item_id)
    local bag = self:GetBagItemDataList(show_type)
    local count = 0
    for k, v in pairs(bag) do
        if v.item_id == item_id then
            count = count + 1
        end
    end
    return count
end

function MountLingChongEquipWGData:GetBagItemDataList(show_type)
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		return self.mount_bag or {}
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		return self.pet_bag or {}
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        return self.huakun_equip_bag or {}
    end
	return {}
end

function MountLingChongEquipWGData:GetResolveBagItemDataList(show_type)
    local bag_list = self:GetBagItemDataList(show_type)
    local resolve_bag_list = {}
    if not IsEmptyTable(bag_list) then
        for k, v in pairs(bag_list) do
            if v.item_id ~= 0 then
                table.insert(resolve_bag_list, v)
            end
        end
    end

    local resolve_bag_sort_list = OperationActivityWGData.Instance:SortDataByItemColor(resolve_bag_list)
    return resolve_bag_sort_list
end

function MountLingChongEquipWGData:GetIsMountLingChongEquip(item_id)
    if self.equip_id_cfg[item_id] then
        return true
    end
	return false
end

function MountLingChongEquipWGData:GetMountLingChongEquipData(item_id)
    if self.equip_id_cfg[item_id] then
        return self.equip_id_cfg[item_id], self.equip_id_cfg[item_id].type, self.equip_id_cfg[item_id].part
    end
	return nil, nil, nil
end

function MountLingChongEquipWGData:GetMountEquipNingPinExp(item_id)
    if self.equip_id_cfg[item_id] then
        return self.equip_id_cfg[item_id].ningpin_exp
    end
    return nil
end

--获取点击加号显示的item_id tips
function MountLingChongEquipWGData:GetEquipTipsItemList(show_type, part)
    if not self.show_item_list then
        self.show_item_list = {}
    end
    if not self.show_item_list[show_type] then
        self.show_item_list[show_type] = {}
    end
    if self.show_item_list[show_type][part] then
        return self.show_item_list[show_type][part]
    end
    local item_list = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		item_list = self.mount_equip_part_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		item_list = self.lingchong_equip_part_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        item_list = self.huakun_equip_part_cfg
    end
    if not IsEmptyTable(item_list[part]) then
        for k, v in pairs(item_list[part]) do
            if v.quality == 5 then --策划说弹出tips要红色品质的
                self.show_item_list[show_type][part] = v.equip_id
                return self.show_item_list[show_type][part]
            end
        end
    end
	return nil
end

--获取套装列表
function MountLingChongEquipWGData:GetSuitList(show_type)
    if not self.suit_type_cfg then
        self.suit_type_cfg = {}
    end
    local index = 0
    local suit_cfg = self.suit_cfg[show_type]
    if not self.suit_type_cfg[show_type] and not IsEmptyTable(suit_cfg) then
        local cur_suit_cfg = {}
        for k, v in pairs(suit_cfg) do
            local quality = v.quality
            if not cur_suit_cfg[quality] then
                index = index + 1
                cur_suit_cfg[quality] = {}
                cur_suit_cfg[quality].index = index
            end
            local info = {}
            info.num = v.num
            info.quality = v.quality
            info.suit_name = v.suit_name
            info.add_percent = v.add_percent
            for i = 0, 4 do
                if v["attr_type_"..i] then
                    local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(v["attr_type_"..i])
                    info["attr_type_"..i] = v["attr_type_"..i]
                    info["attr_type_str_"..i] = attr_name
                    info["attr_name_"..i] = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_name, true, false)
                    info["attr_value_"..i] = v["attr_value_"..i]
                end
            end
            table.insert(cur_suit_cfg[quality], info)
        end

        self.suit_type_cfg[show_type] = {}
        for k, v in pairs(cur_suit_cfg) do
            local temp = {}
            temp.suit_grade = v
            temp.quality = v[1].quality
            temp.suit_name = v[1].suit_name
            temp.show_type = show_type
            table.insert(self.suit_type_cfg[show_type], temp)
        end
    end

    if not IsEmptyTable(self.suit_type_cfg[show_type]) then
        self:InitSuitFlag(show_type, #self.suit_type_cfg[show_type])
        local suit_list = self.suit_active_list[show_type]
        for k, v in pairs(self.suit_type_cfg[show_type]) do
            local active_flag = suit_list[k].active_flag or 0
            v.active_flag_tb = bit:d2b_two(active_flag)
            local satisty_flag = suit_list[k].satisty_flag or 0
            v.satisty_flag_tb = bit:d2b_two(satisty_flag)
        end
    end

    return self.suit_type_cfg[show_type] or {}
end

--界面上展示的
function MountLingChongEquipWGData:GetSuitViewList(show_type)
    local list = self:GetSuitList(show_type)
    local best_quality = self:GetEquipInfoBestQuality(show_type)
    local active_quality = 1
    for k, v in pairs(list) do
        local is_active, cur_num, active_count = self:GetSuitCanActive(show_type, v.quality)
        if active_count > 0 then
            if active_quality < v.quality then
                active_quality = v.quality
            end
        end
    end
    
    local total_quality = math.max(best_quality, active_quality)
    local data_list = {}
    for k, v in pairs(list) do
        if v.quality <= total_quality + 1 then
            data_list[#data_list+1] = v
        end
    end
    return data_list
end

--初始化数据，防止服务器数据没下发
function MountLingChongEquipWGData:InitSuitFlag(show_type, length)
    if not self.suit_active_list then
        self.suit_active_list = {}
    end
    if not self.suit_active_list[show_type] then
        self.suit_active_list[show_type] = {}
        for i = 1, length do
            local temp = {}
            temp.active_flag = 0 -- 激活标记  2 4 6 位
            temp.satisty_flag = 0 --部位装备满足标记(0-5)
            self.suit_active_list[show_type][i] = temp
        end
    end
end

--获取套装信息
function MountLingChongEquipWGData:GetSuitInfoByItemId(item_id)
    local this_cfg, show_type, part = self:GetMountLingChongEquipData(item_id)
    if not this_cfg then
        return nil
    end
    local quality = this_cfg.quality
    local is_active, cur_num, active_count = self:GetSuitCanActive(show_type, quality)
    local list = self:GetSuitList(show_type)
    local suit_cfg = {}

    for k, v in pairs(list) do
        if quality == v.quality then
            suit_cfg = v
        end
    end
    local active_flag_tb = suit_cfg.active_flag_tb
    local total_count = suit_cfg.suit_grade[#suit_cfg.suit_grade].num
    local data_info = {}
    local str = string.format(Language.MountPetEquip.TipsSuitNameStr, suit_cfg.suit_name, active_count, total_count)
    data_info.suit_title = str--ToColorStr(str, ITEM_COLOR[quality])
    local suit_attr_list = {}
    for k, v in pairs(suit_cfg.suit_grade) do
        if type(v)== "table" then   
            local color1 = active_flag_tb[v.num] == 1 and COLOR3B.D_GREEN or COLOR3B.GRAY
            local str1 = ToColorStr(string.format(Language.MountPetEquip.SuitNum, v.num), color1)
            local info = {}
            info.suit_name = str1
            info.attr_list = {}        
            for i = 0, 3 do
                local str = v["attr_name_"..i]
                if str then
                    local attr ={}
                    attr.attr_name = ToColorStr(str, color1)
                    attr.attr_value = ToColorStr(v["attr_value_"..i], color1)
                    info.attr_list[#info.attr_list+1] = attr
                end
            end

            local add_percent = v.add_percent
            if add_percent and add_percent ~= 0 then
                local attr ={}
                attr.attr_name = ToColorStr(Language.MountPetEquip.UpgradeAddPercent, color1)
                attr.attr_value = ToColorStr(add_percent / 100 .. "%", color1)
                info.attr_list[#info.attr_list+1] = attr
            end

            info.is_open = active_flag_tb[v.num] == 1
            suit_attr_list[#suit_attr_list+1] = info
        end
    end

    data_info.suit_attr_list = suit_attr_list
    return data_info
end

--判断某个套装是否可以激活,
function MountLingChongEquipWGData:GetSuitCanActive(show_type, quality)
    local suit_list = self:GetSuitList(show_type)
    local cur_suit_cfg, active_flag_tb, satisty_flag_tb
    for k, v in pairs(suit_list) do
        if v.quality == quality then
            active_flag_tb = v.active_flag_tb
            satisty_flag_tb = v.satisty_flag_tb
            cur_suit_cfg = v.suit_grade
            break
        end
    end
    local active_count = 0 --已经激活的数量
    if not IsEmptyTable(satisty_flag_tb) then
        for i = 0, 5 do --6个部位
            if satisty_flag_tb[i] == 1 then
                active_count = active_count + 1
            end
        end
    end
    local grade_active_index = 0 --当前已经激活的套装索引
    local grade_active_count = 0
    if not IsEmptyTable(cur_suit_cfg) and not IsEmptyTable(active_flag_tb) then
        for i = #cur_suit_cfg, 1, -1 do
            local num = cur_suit_cfg[i].num
            if active_flag_tb[num] == 1 then
                grade_active_index = i
                grade_active_count = num
                break
            end
        end
    end
    if active_count ~= 0 then
        if active_count >= grade_active_count then
            if cur_suit_cfg[grade_active_index + 1] then
                return cur_suit_cfg[grade_active_index + 1].num <= active_count, cur_suit_cfg[grade_active_index + 1].num, active_count
            else--表示全部激活了
                return false, 0, cur_suit_cfg[#cur_suit_cfg].num
            end
        end
    else
        return false, 0, 0
    end
    return false, 0, 0
end

--获取某一个套装的items
function MountLingChongEquipWGData:GetSuitItemList(show_type, quality)
    local item_list = self.equip_id_cfg
    local data_list = {}
    if not IsEmptyTable(item_list) then
        for k, v in pairs(item_list) do
            if v.type == show_type and v.quality == quality then
                data_list[#data_list+1] = v
            end
        end
    end
    table.sort(data_list, SortTools.KeyLowerSorter("part"))
    return data_list
end

function MountLingChongEquipWGData:SaveEquipSuitInfo(protocol)
    if not self.suit_active_list then
        self.suit_active_list = {}
    end
    self.suit_active_list[protocol.type] = protocol.suit_active_list
end

function MountLingChongEquipWGData:SaveEquipInfo(protocol)
    local data = {}
    data.type = protocol.type
    data.part = protocol.part
    data.is_bind = protocol.is_bind
    data.item_id = protocol.item_id
    data.star_level = protocol.star_level
    data.strengthen_level = protocol.strengthen_level
    if not self.equip_wear_info[protocol.type] then
        self.equip_wear_info[protocol.type] = {}
    end
    self.equip_wear_info[protocol.type][protocol.part] = data
end

function MountLingChongEquipWGData:SaveEquipResolveInfo(protocol)
    local data = {}
    data.type = protocol.type or 0
    data.resolve_equip_part_index = protocol.resolve_equip_part_index or 0
    data.resolve_level = protocol.resolve_level or 0
    data.resolve_exp = protocol.resolve_exp or 0
 
    if not self.equip_resolve_info[protocol.type] then
        self.equip_resolve_info[protocol.type] = {}
    end
    self.equip_resolve_info[protocol.type][protocol.resolve_equip_part_index] = data
end 

function MountLingChongEquipWGData:GetEquipResolveEquipByPart(show_type, part)
    return self.equip_resolve_info[show_type] and self.equip_resolve_info[show_type][part]
end

function MountLingChongEquipWGData:SaveEquipListInfo(protocol)
    self.equip_wear_info[protocol.type] = protocol.equip_list
end

--获取已经穿戴的
function MountLingChongEquipWGData:GetEquipInfoByPart(show_type, part)
    return self.equip_wear_info[show_type] and self.equip_wear_info[show_type][part]
end

--获取第一件已经穿戴的
function MountLingChongEquipWGData:GetEquipInfoFirstPart(show_type)
    for k = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, k)
        if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
            return k
        end
    end
end

--获取穿戴的品质最高的
function MountLingChongEquipWGData:GetEquipInfoBestQuality(show_type)
    local quality = 1
    for k = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, k)
        if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
            local this_cfg, _, part = self:GetMountLingChongEquipData(info.item_id)
            if this_cfg and quality <= this_cfg.quality then
                quality = this_cfg.quality
            end
        end
    end
    return quality
end

--凝品进入条件（所有穿戴装备品质达到幻彩）
function MountLingChongEquipWGData:GetEnterNingPingByQuality(show_type)
    local max_quality = 8
    for k = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, k)
        if not IsEmptyTable(info) and info.item_id then
            if info.item_id == 0 then
                return false
            elseif info.item_id ~= 0 then
                local this_cfg, _, part = self:GetMountLingChongEquipData(info.item_id)
                if this_cfg and this_cfg.quality < max_quality then
                    return false
                end
            end
        end
    end
    return true
end

function MountLingChongEquipWGData:IsBetterThanCurWearing(show_type, item_id)
    local this_cfg, _, part = self:GetMountLingChongEquipData(item_id)
    local this_quality = this_cfg and this_cfg.quality or 0
    local is_up = false
    local cur_quality = 0
    if part then
        local cur_wear = self:GetEquipInfoByPart(show_type, part)
        local cur_item_id = cur_wear and cur_wear.item_id or nil
        if not cur_item_id or cur_item_id == 0 then
            is_up = true
        else
            local cfg, _, _ = self:GetMountLingChongEquipData(cur_item_id)
            if cfg then
                cur_quality = cfg.quality
            end
            is_up = this_quality > cur_quality
        end
    end
    return is_up
end

--判断当前等级是否满级
function MountLingChongEquipWGData:IsStrengthMax(show_type, part, strengthen_level)
    if not part then
        return false
    end
    local cfg = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		cfg = self.mount_strengthen_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		cfg = self.lingchong_strengthen_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        cfg = self.huakun_strengthen_cfg
    end
    local info_list = cfg[part] or {}
    return strengthen_level >= #info_list
end

--判断当前凝品等级是否满级
function MountLingChongEquipWGData:IsNingPinMax(show_type, part, level)
    if not part then
        return false
    end
    local cfg = self.ningpin_cfg[show_type][part]

    local info_list = cfg or {}
    return level >= #info_list
end

--根据强化等级拿强化配置
function MountLingChongEquipWGData:GetStrengthCfgByLevel(show_type, part, strengthen_level)
    local cfg = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		cfg = self.mount_strengthen_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		cfg = self.lingchong_strengthen_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        cfg = self.huakun_strengthen_cfg
    end
    local info_list = cfg[part]

    if strengthen_level == 0 then --0级特殊处理
        for k, v in pairs(info_list) do
            if v.level == 1 then
                local data = {}
                data.attr_type_0 = v.attr_type_0
                data.attr_value_0 = 0
                data.attr_type_1 = v.attr_type_1
                data.attr_value_1 = 0
                data.attr_type_2 = v.attr_type_2
                data.attr_value_2 = 0
                data.attr_type_3 = v.attr_type_3
                data.attr_value_3 = 0
                data.level = 0
                return data
            end
        end
    end

    for k, v in pairs(info_list) do
        if strengthen_level == v.level then
            return v
        end
    end
    return {}
end

function MountLingChongEquipWGData:MountPetEquipAttr(show_type, percent)
    local capa = 0
    local now_attri_list
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT or show_type == MOUNT_PET_EQUIP_TYPE.PET then
        local body_show_type = show_type == MOUNT_PET_EQUIP_TYPE.MOUNT and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
        local data = NewAppearanceWGData.Instance:GetQiChongInfo(body_show_type)
        local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, data.star_level)
        now_attri_list = AttributeMgr.GetAttributteByClass(__TableCopy(upstar_cfg))
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备TODO
    end

    if now_attri_list then
        local attr = AttributeMgr.MulAttribute(now_attri_list, percent)
        capa = AttributeMgr.GetCapability(attr)
    end
    
    return capa
end


--强化等级限制
function MountLingChongEquipWGData:GetStrengthLimitLv(show_type, part, quality)
    local cfg = self.strength_limit_cfg[show_type]
    if cfg then
        for k, v in pairs(cfg) do
            if v.quality == quality and v.part == part then
                return v.strengthen_level_limit
            end
        end
    end
    return 0
end

function MountLingChongEquipWGData:GetIsStrengthLimitLv(item_id, now_level)
    local this_cfg, show_type, part = self:GetMountLingChongEquipData(item_id)
    if not this_cfg then
        return true -- 没找到，限制升级
    end

    local limit_lv = self:GetStrengthLimitLv(show_type, part, this_cfg.quality) or 0
    if now_level then
        return now_level >= limit_lv
    end
    return false
end

function MountLingChongEquipWGData:GetNextStrengthQuality(item_id, now_level)
    local this_cfg, show_type, part = self:GetMountLingChongEquipData(item_id)
    local cfg = self.strength_limit_cfg[show_type]
    if cfg then
        for k, v in pairs(cfg) do
            if part == v.part and v.strengthen_level_limit > now_level  then
                return v.quality
            end
        end
    end
    return 0
end

--根据星级拿星级属性,  全装备属性加成：(所有坐骑装备基础属性+所有坐骑装备强化属性）*坐骑全装备百分)
function MountLingChongEquipWGData:GetStarAttrByLevel(show_type, part, star_lv)
    local attr = {}
    local cfg = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		cfg = self.mount_up_star_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		cfg = self.lingchong_up_star_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        cfg = self.huakun_up_star_cfg
    end
    local equip_add_percent = 0
    local info_list = cfg[part]
    for k, v in pairs(info_list) do
        if star_lv >= v.star_level then
            local info = {}
            if v.attr_value_1 ~= 0 then
                info.attr_type = v.attr_type_1
                info.attr_value = v.attr_value_1
                attr[#attr + 1] = info
            end
            if star_lv == v.star_level then
                equip_add_percent = v.equip_add_percent
            end
        end
    end
    return attr, equip_add_percent
end

--根据星级拿对应配置
function MountLingChongEquipWGData:GetStarCfgByLevel(show_type, part, star_lv)
    local cfg = {}
    if show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
		cfg = self.mount_up_star_cfg
	elseif show_type == MOUNT_PET_EQUIP_TYPE.PET then
		cfg = self.lingchong_up_star_cfg
    elseif show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
        --化鲲装备
        cfg = self.huakun_up_star_cfg
    end
    local info_list = cfg[part]
    for k, v in pairs(info_list) do
        if star_lv == v.star_level then
            return v
        end
    end
end

--计算已经穿戴了的战力 + 套装战力
function MountLingChongEquipWGData:GetEquipCapability(show_type)
    local capa = 0
    for i = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, i)
        if not IsEmptyTable(info) then
            local temp = self:GetCapabilityByPartData(info)
            capa = capa + temp
        end
    end
    local suit_cap = self:GetSuitCapability(show_type)
    capa = capa + suit_cap
    return capa
end

--计算已经套装战力
function MountLingChongEquipWGData:GetSuitCapability(show_type)
    local capa = 0
    local attr = {}
    local suit_list = self:GetSuitList(show_type)
    for k, v in pairs(suit_list) do
        local suit_cfg = v.suit_grade
        local active_flag_tb = v.active_flag_tb
        if suit_cfg then
            for k1, v1 in pairs(suit_cfg) do
                if type(v1)== "table" and active_flag_tb[v1.num] == 1 then
                    for i = 0, 4 do
                        if v1["attr_type_"..i] then
                            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(v1["attr_type_"..i]) 
                            local attr_value = v1["attr_value_"..i]
                            if attr_str and attr_value and attr_value ~= 0 then
                                if not attr[attr_str] then
                                    attr[attr_str] = attr_value
                                else
                                    attr[attr_str] = attr[attr_str] + attr_value 
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    local attribute = AttributeMgr.GetAttributteByClass(attr)
    capa = AttributeMgr.GetCapability(attribute)
    return capa
end

--计算某个部位战力
function MountLingChongEquipWGData:GetCapabilityByPartData(data)
    if IsEmptyTable(data) then
        return 0 
    end
    local base_cfg, equip_type = self:GetMountLingChongEquipData(data.item_id)
    if not base_cfg then
        return 0
    end
    local attr_cfg = {}
    local enhance_percent = 1 + (base_cfg.precentage / 10000)
      --星级属性
    local attr
    local equip_add_percent = 0 
    if data.star_level and data.star_level > 0 then
        attr, equip_add_percent = self:GetStarAttrByLevel(equip_type, base_cfg.part, data.star_level)
    end
    equip_add_percent = 1 + equip_add_percent/10000
    if not IsEmptyTable(attr) then
        for k, v in pairs(attr) do
            if v.attr_type then
                local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_type)
                local attr_value = v["attr_value"] and tonumber(v["attr_value"] ) or 0
                if not attr_cfg[attr_name] then
                    attr_cfg[attr_name] = tonumber(attr_value) 
                else
                    attr_cfg[attr_name] = attr_cfg[attr_name] + tonumber(attr_value)
                end
            end
        end
    end

    --基础属性
    for k = 0, 4 do --最多五条
        local attr_type = base_cfg["attr_type_"..k]
        if attr_type then
            local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
            local attr_value = base_cfg["attr_value_"..k] and tonumber(base_cfg["attr_value_"..k] ) or 0
            if not attr_cfg[attr_name] then
                attr_cfg[attr_name] = tonumber(attr_value) * enhance_percent * equip_add_percent
            else
                attr_cfg[attr_name] = attr_cfg[attr_name] + tonumber(attr_value) * enhance_percent * equip_add_percent
            end
        end
    end
    --强化属性
    local strength_cfg
    if data.strengthen_level and data.strengthen_level > 0 then
        strength_cfg = self:GetStrengthCfgByLevel(equip_type, base_cfg.part, data.strengthen_level)
    end
    for k = 0, 4 do --最多五条
        if not IsEmptyTable(strength_cfg) then
            if strength_cfg["attr_type_"..k] then
            local attr_type = strength_cfg["attr_type_"..k]
            if attr_type then
                local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
                local attr_value = strength_cfg["attr_value_"..k] and tonumber(strength_cfg["attr_value_"..k] ) or 0
                if not attr_cfg[attr_name] then
                    attr_cfg[attr_name] = tonumber(attr_value) * enhance_percent * equip_add_percent
                else
                    attr_cfg[attr_name] = attr_cfg[attr_name] + tonumber(attr_value) * enhance_percent * equip_add_percent
                end
                end
            end
        end
    end

    local attribute = AttributeMgr.GetAttributteByClass(attr_cfg)
    local capability = AttributeMgr.GetCapability(attribute)
    return capability
end

--计算某个部位（总评分/装备评分）
function MountLingChongEquipWGData:GetScoreByData(data)
    if IsEmptyTable(data) then
       return 0 
    end
    local base_score, total_score = 0, 0
    local base_cfg, equip_type = self:GetMountLingChongEquipData(data.item_id)
	if not base_cfg then
		return 0, 0
    end
    --星级属性
    local attr
    local equip_add_percent = 0 
    if data.star_level and data.star_level > 0 then
        attr,equip_add_percent = self:GetStarAttrByLevel(equip_type, base_cfg.part, data.star_level)
    end
    equip_add_percent = 1 + equip_add_percent/10000
    if not IsEmptyTable(attr) then
        for k, v in pairs(attr) do
            if v.attr_type then
                local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_type)
                data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_name, false, true)
                local attr_value = v.attr_value or 0
                local score = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_name) * attr_value
                total_score = total_score + score
            end
        end
    end
     --基础属性
    for k = 0, 4 do --最多五条
        local attr_type = base_cfg["attr_type_"..k]
        if attr_type then
            local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
            local attr_value = base_cfg["attr_value_"..k] and tonumber(base_cfg["attr_value_"..k] ) or 0
            local score = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_name) * attr_value * equip_add_percent
            base_score = base_score + score
            total_score = total_score + score
        end
    end
    --强化属性
    local strength_cfg
    if data.strengthen_level and data.strengthen_level > 0 then
        strength_cfg = self:GetStrengthCfgByLevel(equip_type, base_cfg.part, data.strengthen_level)
    end
    for k = 0, 4 do --最多五条
        if not IsEmptyTable(strength_cfg) then
            if strength_cfg["attr_type_"..k] then
                local attr_type = strength_cfg["attr_type_"..k]
                if attr_type then
                    local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
                    local attr_value = strength_cfg["attr_value_"..k] and tonumber(strength_cfg["attr_value_"..k] ) or 0
                    local score = TipWGData.Instance:GetCommonPingFenCfgByIndex(attr_name) * attr_value * equip_add_percent
                    total_score = total_score + score
                end
            end
        end
    end
    

    local enhance_percent = 1 + base_cfg.precentage / 10000
    total_score = total_score * enhance_percent
	return total_score, base_score
end
--
function MountLingChongEquipWGData:ConvertAttrType(data)
    local list = {}
    for k = 0, 4 do --最多五条
        if not IsEmptyTable(data) then
            if data["attr_type_"..k] then
                local attr_type = data["attr_type_"..k]
                if attr_type then
                    local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_type)
                    local attr_value = data["attr_value_"..k] and tonumber(data["attr_value_"..k] ) or 0
                    if attr_value ~= 0 then
                        list[attr_name] = attr_value
                    end
                end
            end
        end
    end
    return list
end


------------升品相关--------------
--通过当前item获取配置
function MountLingChongEquipWGData:GetComposeCfgByItemId(item_id)
    return self.compose_cfg[item_id]
end

--存储已经选择了的列表--在批量选择界面
function MountLingChongEquipWGData:SaveShengpinSelectList(list)
    self.consume_grid_index_list = list
end

function MountLingChongEquipWGData:GetShengpinSelectList()
    if not self.consume_grid_index_list then
        self.consume_grid_index_list = {}
    end
    for i = 1, 8 do
        self.consume_grid_index_list[i] = self.consume_grid_index_list[i] or -1
    end
    return self.consume_grid_index_list
end

function MountLingChongEquipWGData:GetShengpinSelectListCount()
    if not self.consume_grid_index_list then
        self.consume_grid_index_list = {}
    end
    local count = 0
    local first_grid_idx
    for k, v in pairs(self.consume_grid_index_list) do
        if v > - 1 then
            count = count + 1   
        end
    end
    for k, v in pairs(self.consume_grid_index_list) do
        if v > - 1 then
            first_grid_idx = v
            break
        end
    end

    return count, first_grid_idx
end

--多重选择可升品的列表
function MountLingChongEquipWGData:GetShengpinMutiList(show_type)
    local bag_list = self:GetBagItemDataList(show_type)
    local fake_bag_list = {}
    local bag_grid_index = {}
    for k, v in pairs(bag_list) do
        if v.item_id and v.item_id ~= 0 then
            local temp = {}
            temp.item_id = v.item_id
            temp.is_bind = v.is_bind
            temp.show_type = show_type
            temp.grid_index = v.grid_index
            local equip_cfg, show_type, part = self:GetMountLingChongEquipData(v.item_id)
            temp.quality = equip_cfg and equip_cfg.quality or 0
            temp.part = part
            temp.sort_part = -part
            local info = self:GetEquipInfoByPart(show_type, part) --穿戴信息
            if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
                local this_cfg, _, part = self:GetMountLingChongEquipData(info.item_id)
                temp.sort_quality = this_cfg and -this_cfg.quality or 0
            else
                temp.sort_quality = 0
            end

            temp.sort_part = -part
            fake_bag_list[#fake_bag_list+1] = temp
            bag_grid_index[v.grid_index] = true
        end
    end
    table.sort(fake_bag_list, SortTools.KeyUpperSorters("quality", "sort_quality", "sort_part")) --策划说优先合成穿戴品质低的

    local shengpin_muti_list = {}
    for i = 0, 5 do
        local info = self:GetEquipInfoByPart(show_type, i)
        if not IsEmptyTable(info) then
            local item_list = self:GetCanShengpinDataList(info.item_id, bag_grid_index, true, fake_bag_list)
            if not IsEmptyTable(item_list) then
                local data = {}
                data.stuff_item_list = item_list
                data.use_wear_part = true
                data.cur_item_id = info.item_id
                shengpin_muti_list[#shengpin_muti_list+1] = data
            end
        end
    end
    
    for k, v in pairs(fake_bag_list) do
        if bag_grid_index[v.grid_index] then
            local item_list = self:GetCanShengpinDataList(v.item_id, bag_grid_index, false, fake_bag_list)
            if not IsEmptyTable(item_list) then
                local data = {}
                data.stuff_item_list = item_list
                data.use_wear_part = false
                data.cur_item_id = v.item_id
                shengpin_muti_list[#shengpin_muti_list+1] = data
            end
        end
    end
    return shengpin_muti_list
end

---is_use_wear 是否穿戴身上的
function MountLingChongEquipWGData:GetCanShengpinDataList(item_id, bag_grid_index, is_use_wear, fake_bag_list)
    local target_cfg = self:GetComposeCfgByItemId(item_id)
    local equip_cfg, show_type = self:GetMountLingChongEquipData(item_id)
    if IsEmptyTable(target_cfg) then
        return {}
    end
    local compose_str = target_cfg.consume_equip_2
    local str_list = Split(compose_str, "|")
    local item_list = {}
    local bag_list = fake_bag_list
    local has_must = 0

    local need_must_num = target_cfg.consume_num_1
    local must_item_limit_count = 0
    if is_use_wear then
        must_item_limit_count = need_must_num --使用身上穿的时候，需要相同的一个物品；
    else
        must_item_limit_count = need_must_num + 1  --使用背包，需要两个
    end
    local need_stuff_num = target_cfg.consume_num_2
    local cur_stuff_num = 0
    if not IsEmptyTable(bag_list) then
        for k, v in pairs(bag_list) do
            for k1, v1 in pairs(str_list) do
                if v.item_id == item_id and has_must < must_item_limit_count and bag_grid_index[v.grid_index] then --必定需要的
                    local data = {}
                    data.item_id = v.item_id
                    data.cur_grid_index = v.grid_index
                    if has_must == 0 then --必定需要的也就是和穿戴一样的
                        data.is_first_grid = v.grid_index
                    end
                    item_list[#item_list+1] = data
                    has_must = has_must + 1
                    bag_grid_index[v.grid_index] = false
                elseif v.item_id == tonumber(v1) and bag_grid_index[v.grid_index] and cur_stuff_num < need_stuff_num then
                    local data = {}
                    data.item_id = v.item_id
                    data.cur_grid_index = v.grid_index
                    item_list[#item_list+1] = data
                    cur_stuff_num = cur_stuff_num + 1
                    bag_grid_index[v.grid_index] = false 
                end
            end
        end
    end
   
    local need_num = target_cfg.consume_num_2 + must_item_limit_count --合成需要背包里物品的总数量
    if need_num <= #item_list and has_must == must_item_limit_count and cur_stuff_num == need_stuff_num then
        local data_list = {}
        for i = 1, need_num do
            data_list[i] = item_list[i]
        end
        return data_list
    else
        for k, v in pairs(item_list) do
            bag_grid_index[v.cur_grid_index] = true
        end
        return {}
    end
end


function MountLingChongEquipWGData:SaveMutiSelect(index, is_on)
    if not self.select_muti_list then
        self.select_muti_list = {}
    end
    self.select_muti_list[index] = is_on
end

function MountLingChongEquipWGData:ResetMutiSelect()
    self.select_muti_list = {}
end

function MountLingChongEquipWGData:GetMutiSelect()
    if not self.select_muti_list then
        self.select_muti_list = {}
    end
    return self.select_muti_list
end

function MountLingChongEquipWGData:GetMutiSelectIndex(index)
    if not self.select_muti_list then
        self.select_muti_list = {}
    end
    return self.select_muti_list[index]
end

--获取身上穿的某个部位是否能升品
function MountLingChongEquipWGData:GetPartWearCanShengPin(show_type, part)
    local info = self:GetEquipInfoByPart(show_type, part)
    if not IsEmptyTable(info) and info.item_id and info.item_id ~= 0 then
        local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(info.item_id)
        local equip_cfg, show_type = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(info.item_id)
        if IsEmptyTable(target_cfg) then
            return 0
        end
        local compose_str = target_cfg.consume_equip_2
        local str_list = Split(compose_str, "|")
        local item_list = {}
        local bag_list = MountLingChongEquipWGData.Instance:GetBagItemDataList(show_type)
        local need_must = true
        local cur_grid_index
        if target_cfg.consume_num_1 == 0 then
            need_must = false
            cur_grid_index = -1
        end
        if not IsEmptyTable(bag_list) then
            for k, v in pairs(bag_list) do
                for k1, v1 in pairs(str_list) do
                    if v.item_id == info.item_id and need_must and cur_grid_index == nil then --去掉一个必定需要的
                        cur_grid_index = v.grid_index
                    elseif v.item_id == tonumber(v1) then
                        -- 因材料都相同，可能出现即使该材料在材料一被记录了，也会加入item_list里算做材料二，故加此判断
                        if cur_grid_index == nil or cur_grid_index ~= v.grid_index then
                            local data = {}
                            data.item_id = v.item_id
                            data.cur_grid_index = v.grid_index
                            item_list[#item_list+1] = data
                        end
                    end
                end
            end
        end
        local count = #item_list
        if target_cfg.consume_num_2 <= count and cur_grid_index ~= nil then
            return 1
        end
    end
    return 0
end
------------升品end--------------

--获取可强化，升星，升品的
function MountLingChongEquipWGData:GetEquipCellShowRemind(show_index, show_type)
    local remind_list = {}
    if show_index == MountLingChongEquipViewIndex[show_type].Bag then
        for i = 0, 5 do
            remind_list[i] = false
        end
    elseif show_index == MountLingChongEquipViewIndex[show_type].Strength then
        for i = 0, 5 do
            local is_remind = MountLingChongEquipWGData.Instance:GetEquipStrengthPartRemind(show_type, i) == 1
            remind_list[i] = is_remind
        end
    elseif show_index == MountLingChongEquipViewIndex[show_type].ShengPin then
        for i = 0, 5 do
            local is_remind = MountLingChongEquipWGData.Instance:GetPartWearCanShengPin(show_type, i) == 1
            remind_list[i] = is_remind
        end
    elseif show_index == MountLingChongEquipViewIndex[show_type].UpStar then
        for i = 0, 5 do
            local is_remind = MountLingChongEquipWGData.Instance:GetEquipStarPartRemind(show_type, i) == 1
            remind_list[i] = is_remind
        end
    end
    return remind_list
end

function MountLingChongEquipWGData:GetNingPinCfg(show_type, solt, level)
    return ((self.ningpin_cfg[show_type] or {})[solt] or {})[level]
end

function MountLingChongEquipWGData:GetEquipNingPinAttr(show_type, solt, level)
    local cur_resolve_level_cfg = self:GetNingPinCfg(show_type, solt, level)
    local next_resolve_level_cfg = self:GetNingPinCfg(show_type, solt, level + 1)
    return cur_resolve_level_cfg, next_resolve_level_cfg
end

function MountLingChongEquipWGData:GetEquipNingLevelExp(show_type, solt, level)
    local cfg = self:GetNingPinCfg(show_type, solt, level)
    return cfg and cfg.exp or 0
end

--存储已经选择了的列表--凝品界面
function MountLingChongEquipWGData:SaveNingpinSelectList(list)
    self.resolve_grid_index_list = list
end

function MountLingChongEquipWGData:GetNingpinSelectList()
    if not self.resolve_grid_index_list then
        self.resolve_grid_index_list = {}
    end
    for i = 1, 50 do
        self.resolve_grid_index_list[i] = self.resolve_grid_index_list[i] or -1
    end
    return self.resolve_grid_index_list
end

function MountLingChongEquipWGData:GetEquipNingPinPartRemind(show_type, part, level)
    local is_max_level = self:IsNingPinMax(show_type, part, level)
    if is_max_level then
        return 0
    end
    local bag_list = self:GetBagItemDataList(show_type)
    local all_equip_exp = 0
    if not IsEmptyTable(bag_list) then
        for k, v in pairs(bag_list) do
          if v.item_id and v.item_id ~= 0 then
             local equip_exp = self:GetMountEquipNingPinExp(v.item_id)
             all_equip_exp = all_equip_exp + equip_exp
          end
        end
        local cur_level_exp = self:GetEquipNingLevelExp(show_type, part, level)
        local cur_equip_info = self:GetEquipResolveEquipByPart(show_type, part)
        if cur_equip_info == nil then
            return 0
        end
        local level_need_exp = cur_level_exp - cur_equip_info.resolve_exp 
        if all_equip_exp > level_need_exp then
            return 1
        end
    end
    return 0
end

function MountLingChongEquipWGData:GetEquipCanNingPin(show_type, part, level)
    local is_can = self:GetEquipNingPinPartRemind(show_type, part, level)
    return is_can == 1
end

function MountLingChongEquipWGData:EquipNingPinRemind(show_type)
    for i = 0, 5 do
        local ningpin_enter_flag = self:GetEnterNingPingByQuality(show_type)
        local cur_equip_info = self:GetEquipResolveEquipByPart(show_type, i)
        if cur_equip_info == nil then
            return 0
        end
        if self:GetEquipCanNingPin(show_type, i, cur_equip_info.resolve_level) and ningpin_enter_flag then
            return 1
        end
    end
	return 0
end

--凝品战力
function MountLingChongEquipWGData:GetNingPinCapability(type, part, level)
    local cur_level_cfg = self:GetEquipNingPinAttr(type, part, level)
    local next_level_cfg = self:GetEquipNingPinAttr(type, part, level + 1)
    local all_attr = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_type", "attr_value", 1, 5)
    
    local capability = 0
	local attr_list = AttributePool.AllocAttribute()

	for k, v in pairs(all_attr) do
		attr_list[v.attr_str] = attr_list[v.attr_str] + v.attr_value
	end

	capability = AttributeMgr.GetCapability(attr_list)

	return capability
end

--凝品总战力
function MountLingChongEquipWGData:GetNingPinAllCapability(type)
    local all_cap = 0
    for i = 0, 5 do
        local ningping_equip_level =self:GetEquipResolveEquipByPart(type, i)
        local ningpin_cap = self:GetNingPinCapability(type, i, ningping_equip_level.resolve_level)
        all_cap = all_cap + ningpin_cap
    end
    return all_cap
end
