LoverPkBlessingView = LoverPkBlessingView or BaseClass(SafeBaseView)

function LoverPkBlessingView:__init()
    self.view_name = GuideModuleName.LoverPKBlessingView
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_flower_blessing")
end

function LoverPkBlessingView:OpenCallBack()
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_KNOCKOUT_MATCH_INFO)
end

function LoverPkBlessingView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["next_btn"], BindTool.Bind(self.OnClickHeadShow, self, -1))
    XUI.AddClickEventListener(self.node_list["last_btn"], BindTool.Bind(self.OnClickHeadShow, self, 1))

    self.on_point_down = BindTool.Bind1(self.OnPointDown, self)
    self.on_point_up = BindTool.Bind1(self.OnPointUp, self)
    
    self.event_listener = self.node_list["seed_flower"].event_trigger_listener

    if self.event_listener ~= nil then
        self.event_listener:AddPointerDownListener(self.on_point_down)
        self.event_listener:AddPointerUpListener(self.on_point_up)
    end

    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_cell)
    end

    if not self.flower_blessing_attr_list then
        self.flower_blessing_attr_list = AsyncListView.New(LoverPKFBAListItemRender, self.node_list.flower_blessing_attr_list)
        self.flower_blessing_attr_list:SetStartZeroIndex(false)
    end

    self.show_head_value = 1
    self.select_menber_cache = {}
end

function LoverPkBlessingView:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.flower_blessing_attr_list then
        self.flower_blessing_attr_list:DeleteMe()
        self.flower_blessing_attr_list = nil
    end

    self.show_head_value = nil
    self.event_listener = nil
    self.on_point_down = nil
    self.on_point_up = nil
    self:RemoveCountDown()
end

function LoverPkBlessingView:OnFlush()
    local send_flower_data_list = LoverPkWGData.Instance:GetCanSendFlowerMenberInfoList()
    local has_send_lover_data = not IsEmptyTable(send_flower_data_list)
    self.node_list.item_cell:CustomSetActive(has_send_lover_data)
    self.node_list.lover_info:CustomSetActive(has_send_lover_data)
    self.node_list.no_can_send_lover:CustomSetActive(not has_send_lover_data)
    self.node_list.has_flowers:CustomSetActive(has_send_lover_data)
    XUI.SetButtonEnabled(self.node_list.seed_flower, has_send_lover_data)

    local send_flower_count, get_reward_count_limit = LoverPkWGData.Instance:GetMySendFlowerCount()
    local color = send_flower_count >= get_reward_count_limit and COLOR3B.D_RED or COLOR3B.D_GREEN
    local count = get_reward_count_limit - send_flower_count
    count = count > 0 and count or 0
    self.node_list.desc_send_flower_tip.text.text = string.format(Language.LoverPK.SendFlowerTip, color, count, get_reward_count_limit)

    if has_send_lover_data then
        local cost_item_id = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("flower_item_id")
        local cost_item_num = 1
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    
        self.item_cell:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. cost_item_num, (item_num >= cost_item_num) and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.item_cell:SetRightBottomColorText(right_text)
            self.item_cell:SetRightBottomTextVisible(true)
        end)

        self.item_cell:SetData({item_id = cost_item_id})

        self:FlushHeadShow()
    else
        local attr_data = {}
        local flower_blessing_attr_data = LoverPkWGData.Instance:GetSendFlowerBuffCfg()

        for k, v in pairs(flower_blessing_attr_data) do
            table.insert(attr_data, {cfg = v, flower_count = 0})
        end

        self.flower_blessing_attr_list:SetDataList(attr_data)
        self.node_list.flower_num.text.text = 0
        self.select_menber_cache = {}
    end
end

function LoverPkBlessingView:OnPointDown(event_data)
    self:RemoveCountDown()
    self:SeedFlower()
    self.quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.SeedFlower, self), 0.5)
end

function LoverPkBlessingView:OnPointUp(event_data)
    self:RemoveCountDown()
end

function LoverPkBlessingView:SeedFlower()
    if IsEmptyTable(self.select_menber_cache) then
        self:RemoveCountDown()
        return
    end

    local cost_item_id = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("flower_item_id")
    local hsa_flower = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    
    if hsa_flower <= 0 then
        self:RemoveCountDown()
        TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
        return
    end

    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.SEND_FLOWERS, self.select_menber_cache.round, self.select_menber_cache.idx)
end

function LoverPkBlessingView:RemoveCountDown()
    if self.quest then
        GlobalTimerQuest:CancelQuest(self.quest)
        self.quest = nil
    end
end

function LoverPkBlessingView:OnClickHeadShow(value)
    if not self.show_head_value then
        return
    end

    local send_flower_data_list = LoverPkWGData.Instance:GetCanSendFlowerMenberInfoList()
    local max_value = #send_flower_data_list
    local head_value = self.show_head_value + value
    head_value = head_value > max_value and (max_value - max_value) or head_value
    head_value = head_value < 0 and (head_value + max_value) or head_value

    self.show_head_value = head_value
    self:FlushHeadShow()
end

function LoverPkBlessingView:FlushHeadShow()
    local send_flower_data_list = LoverPkWGData.Instance:GetCanSendFlowerMenberInfoList()
    local target_data = send_flower_data_list[self.show_head_value]

    if not IsEmptyTable(target_data) then
        local attr_data = {}
        local flower_blessing_attr_data = LoverPkWGData.Instance:GetSendFlowerBuffCfg()
        
        for k, v in pairs(flower_blessing_attr_data) do
            table.insert(attr_data, {cfg = v, flower_count = target_data.flowers_count})
        end
        
        self.flower_blessing_attr_list:SetDataList(attr_data)
        local role_id = ((target_data or {}).uuid1 or {}).temp_low or -1
        local server_id = UserVo.GetServerId(role_id)
		server_id = server_id > 0 and server_id or RoleWGData.Instance:GetOriginServerId()
        self.node_list.server_id.text.text = string.format(Language.Common.ServerIdFormat, server_id)
        self.node_list.name_1.text.text = target_data.name1
        self.node_list.name_2.text.text = target_data.name2
        self.node_list.flower_num.text.text = target_data.flowers_count

        local is_robot = target_data.is_robot1 == 1 or false
		if is_robot then
			local default_role_id = target_data.default_uuid.temp_low or 0
			XUI.UpdateRoleHead(self.node_list.head_default, self.node_list.head, default_role_id,  target_data.default_sex, target_data.default_prof, false, false, false)
		else
			BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
				XUI.UpdateRoleHead(self.node_list.head_default, self.node_list.head, role_id,  protocol.sex,  protocol.prof, false, false, false)
			end)
		end

        self.select_menber_cache = target_data
    end
end

---------------------------------------LoverPKFBAListItemRender------------------------------------------------
LoverPKFBAListItemRender = LoverPKFBAListItemRender or BaseClass(BaseRender)

function LoverPKFBAListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local flower_count = self.data.flower_count or 0
    local flowers_count_begin = (self.data.cfg or {}).flowers_count_begin or 0
    local flowers_count_end = (self.data.cfg or {}).flowers_count_end or 0
    local buff_title = (self.data.cfg or {}).buff_title or ""
    local buff_txt = (self.data.cfg or {}).buff_txt or ""
    local active = (flower_count > 0) and (flower_count > flowers_count_begin)
    local color = active and "#2e7350" or "#4f3d2a"

    self.node_list.flower_num.text.text = ToColorStr(flowers_count_begin .. "-" .. flowers_count_end, color)
    self.node_list.desc.text.text = ToColorStr(buff_title, color)
    self.node_list.attr.text.text = ToColorStr(buff_txt, color)
end