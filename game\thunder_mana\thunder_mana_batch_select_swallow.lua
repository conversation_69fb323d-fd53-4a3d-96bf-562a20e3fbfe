-- 灵宠选择同星选择界面
ManaBatchBatchSelectSwallow = ManaBatchBatchSelectSwallow or BaseClass(SafeBaseView)
local ONCE_SWALLOW_NUM = 20

function ManaBatchBatchSelectSwallow:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {vector2 = Vector2(0, -2), sizeDelta = Vector2(706, 484)})
	self:AddViewResource(0, "uis/view/thunder_mana_ui_prefab", "layout_thunder_mana_batch_select")
    self.view_name = "ManaBatchBatchSelectSwallow"
    self:SetMaskBg(true)
end

function ManaBatchBatchSelectSwallow:ReleaseCallBack()
	if nil ~= self.batch_item_grid then
		self.batch_item_grid:DeleteMe()
    end
    self.batch_item_grid = nil
    self.show_data = nil
    self.ok_callback = nil
    self.need_num = nil
    self.cur_select_num = nil
end

function ManaBatchBatchSelectSwallow:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ThunderMana.ThunderTitleName[4]
    
    self.batch_item_grid = BeastBatchGrid.New(self)
    self.batch_item_grid:SetStartZeroIndex(false)
    self.batch_item_grid:SetIsShowTips(false)
    self.batch_item_grid:SetNoSelectState(false)
    self.batch_item_grid:SetIsMultiSelect(true)                       --  ,change_cells_num = 2
    self.batch_item_grid:CreateCells({col = 8 ,change_cells_num = 2, cell_count = 32,
    list_view = self.node_list["ph_item_grid"], itemRender = ManaBatchBatchSwallowSelectRender})
    self.batch_item_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))

    self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure, self))
    self.node_list["btn_one_key"].button:AddClickListener(BindTool.Bind(self.OnClickOneKey, self))
end

function ManaBatchBatchSelectSwallow:SetOkBack(callback)
	self.ok_callback = callback
end

function ManaBatchBatchSelectSwallow:SelectShenShouBagCellCallBack(cell)
	self:FlushAddNum()
end

function ManaBatchBatchSelectSwallow:SetData(show_data)
    self.show_data = show_data

    if self:IsLoaded() then
        self:Flush()
    end
end

function ManaBatchBatchSelectSwallow:OnFlush()
    self:FlushDataList()
    self:FlushAddNum()
end

-- 刷新列表
function ManaBatchBatchSelectSwallow:FlushDataList()
    if not self.show_data then
        return
    end

    -- 获取当前已经选取的材料列表
    local star_select_list = {}
    local grid_list = {}
    local part_info = self.show_data.part_info
	local all_bag_info = ThunderManaWGData.Instance:GetAllThunderBagInfo()

	for i, v in pairs(all_bag_info) do
		if not part_info then
			if v.item_id > 0 then
				table.insert(grid_list, v)
			end
		else
			if part_info.seq == v.seq and v.item_id > 0 and v.star <= part_info.star then
				table.insert(grid_list, v)
			end
		end
	end

    if self.show_data.now_list then
        for i, v in ipairs(self.show_data.now_list) do
            if v and v.cur_item_id then
                star_select_list[v.cur_item_id] = true
            end
        end
    end

    for index, grid_data in ipairs(grid_list) do
        if star_select_list[grid_data.item_id] then
            self.batch_item_grid.select_tab[1][index] = true --选中已先择的
        end
    end

    if #grid_list < 1 then
        self.batch_item_grid:CancleAllSelectCell()
    end

    self.batch_item_grid:SetDataList(grid_list)
    self.node_list["ph_item_grid"].scroll_rect.verticalNormalizedPosition = 1
end

-- 刷新数量
function ManaBatchBatchSelectSwallow:FlushAddNum()
    local part_info = self.show_data.part_info
	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if not equip_cfg then
		return
	end

	local exp_limit = equip_cfg.up_star_cost1
    local now_exp = part_info.exp
    local now_add_exp = 0
    local selected_cells = self.batch_item_grid:GetAllSelectCell()

    for i, v in ipairs(selected_cells) do
		if v and v.item_id then
			local cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(v.item_id)

			if cfg ~= nil then
				now_add_exp = now_add_exp + cfg.item_exp_value * v.num
			end
		end
	end

	local add_exp_str = ""
	if now_add_exp > 0 then
		add_exp_str = ToColorStr(string.format("+%d", now_add_exp), COLOR3B.GREEN)
	end

	self.node_list.tip_text.text.text = string.format(Language.ThunderMana.ComposeSpTips4, string.format("%s%s/%s", now_exp, add_exp_str, exp_limit)) 
end

-- 获取数量
function ManaBatchBatchSelectSwallow:GetNeedNum()
    return ONCE_SWALLOW_NUM
end

-- 一键选取
function ManaBatchBatchSelectSwallow:OnClickOneKey()
    local item_list = self.batch_item_grid:GetDataList()
    local cur_count = #item_list
    
    if cur_count <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.UpstarNotEnough)
        return
    end

    if cur_count > ONCE_SWALLOW_NUM then
        --数量不够
        self.batch_item_grid:SetMultiSelectEffect(ONCE_SWALLOW_NUM)
    else
        self.batch_item_grid:SetMultiSelectEffect(cur_count)
    end
end

-- 关闭回调
function ManaBatchBatchSelectSwallow:CloseCallBack()
    if self.batch_item_grid then
        self.batch_item_grid:CancleAllSelectCell()
    end
end

-- 选择完成
function ManaBatchBatchSelectSwallow:OnClickSure()
    local data_list = self.batch_item_grid:GetAllSelectCell()
    local assemble_table = {}
    for i, v in ipairs(data_list) do
        local data = {}
		data.cur_item_id = v.item_id
		data.cur_item_num = v.num
        table.insert(assemble_table, data)
    end

    if self.ok_callback then
        self.ok_callback(assemble_table)
    end

    self:Close()
end

-------------------------------------------------------------------------------------------------

ManaBatchBatchSwallowSelectRender = ManaBatchBatchSwallowSelectRender or BaseClass(ItemCell)
function ManaBatchBatchSwallowSelectRender:__init()
	self:UseNewSelectEffect(true)
end

function ManaBatchBatchSwallowSelectRender:OnClick()
    self:UseNewSelectEffect(true)
    if self.data then
        ItemCell.OnClick(self)
    end
end

function ManaBatchBatchSwallowSelectRender:OnFlush()
    if not self.data then
		return
    end

    ItemCell.OnFlush(self)
end

function ManaBatchBatchSwallowSelectRender:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return 
    end
    self:UseNewSelectEffect(true)
	ItemCell.SetSelectEffect(self, is_select)	
end