
XianQiJieFengLaiXiDownTime = XianQiJieFengLaiXiDownTime or BaseClass(SafeBaseView)

function XianQiJieFengLaiXiDownTime:__init()
	self.view_layer = UiLayer.MainUI
	self.view_name = "XianQiJieFengLaiXiDownTime"
	local assetbundle = "uis/view/act_xianqi_jiefeng_ui_prefab"
	self:AddViewResource(0, assetbundle, "layout_xtlx_countdown")
end

function XianQiJieFengLaiXiDownTime:ReleaseCallBack()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end

	CountDownManager.Instance:RemoveCountDown("laixi_count_down")
end

function XianQiJieFengLaiXiDownTime:OnFlush()
	self:TimeCountDown()
end

function XianQiJieFengLaiXiDownTime:TimeCountDown()
	CountDownManager.Instance:RemoveCountDown("laixi_count_down")

	local jianglin_data = ActXianQiJieFengWGData.Instance:GetJingLinData()
	if jianglin_data and jianglin_data.next_change_time then
		local count_down_time = math.floor(jianglin_data.next_change_time - TimeWGCtrl.Instance:GetServerTime())
		if count_down_time > 0 then
			if jianglin_data.cur_status == XingTianLaiXiStatus.REFRESH_STATUS_REFRESHING then
				self.node_list.tip.text.text = Language.XianQiJieFengAct.LaiXiStr1
			else
				self.node_list.tip.text.text = Language.XianQiJieFengAct.LaiXiStr2
			end
			self.node_list.downtime.text.text = TimeUtil.FormatSecondDHM4(count_down_time)
			CountDownManager.Instance:AddCountDown("laixi_count_down", BindTool.Bind1(self.UpdateDBCountDown, self), nil, nil, count_down_time, 1)
		else
			self:Close()
		end
	end
end

function XianQiJieFengLaiXiDownTime:UpdateDBCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.downtime.text.text = TimeUtil.FormatSecondDHM4(valid_time)
	end
end