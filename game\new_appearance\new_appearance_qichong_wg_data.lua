--===========================================================
--==================  骑宠 鲲 ================================
local empty_table = {}
function NewAppearanceWGData:InitQiChongParam()
    self.mount_info = {}
    self.qichong_info = {}
    self.kun_info = {}
    self.kun_used_id = -1
    self.kun_upgrade_exp = 0
    self.qichong_special_show_list = {}
    self.need_check_qichong_quick_tab = {}

    self.mount_fix_fail_count_cache = {}
    self.mount_per_fail_count_cache = {}
    self.mount_up_star_consume_cache = {}
    self.mount_attr_add_per_cache = {}

    self.lingchong_fix_fail_count_cache = {}
    self.lingchong_per_fail_count_cache = {}
    self.lingchong_up_star_consume_cache = {}
    self.lingchong_attr_add_per_cache = {}

    self.kun_fix_fail_count_cache = {}
    self.kun_per_fail_count_cache = {}
    self.kun_up_star_consume_cache = {}
end

function NewAppearanceWGData:InitQiChongCfg()
    self.mount_action_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("mount_action_auto").mount_action, "mount_id")
    local mount_cfg = ConfigManager.Instance:GetAutoConfig("mount_auto")
    -- 坐骑 - 基础
    self.mount_base_cfg = ListToMap(mount_cfg.image_scale_pos, "grade_num")
    self.mount_base_map_appeid_cfg = ListToMap(mount_cfg.image_scale_pos, "appe_image_id")
    self.mount_sxd_cfg = ListToMap(mount_cfg.shuxingdan, "slot_idx")
    self.mount_sxd_map_itemid_cfg = ListToMap(mount_cfg.shuxingdan, "shuxingdan_id")
    -- self.mount_sxd_limit_cfg = ListToMap(mount_cfg.shuxingdan_use_limit, "slot_idx")
    self.mount_base_upstar_cfg = ListToMap(mount_cfg.upstar_level, "star_level")
    self.mount_base_upstar_map_appeid_cfg = ListToMap(mount_cfg.upstar_level, "appe_image_id", "star_level")
    self.mount_base_skill_cfg = ListToMap(mount_cfg.skill, "skill_id", "skill_level")
    self.mount_base_skill_map_star_cfg = ListToMap(mount_cfg.skill, "active_star_level", "skill_level")
    -- 坐骑 - 珍稀
    self.mount_act_map_imageid_cfg = ListToMap(mount_cfg.mount_image_active, "image_id")
    self.mount_act_map_itemid_cfg = ListToMap(mount_cfg.mount_image_active, "active_item_id")
    self.mount_act_map_appeid_cfg = ListToMap(mount_cfg.mount_image_active, "appe_image_id")
    self.mount_special_image_skill_cfg = ListToMap(mount_cfg.mount_image_skill, "image_id", "skill_id")
    self.mount_special_upstar_cfg = ListToMap(mount_cfg.mount_image_upstar, "image_id")
    self.mount_special_upgrade_cfg = ListToMap(mount_cfg.mount_image_upgrade, "image_id", "grade")
    self.mount_special_upgrade_item_cfg = ListToMap(mount_cfg.image_upgrade_item, "item_id")
    -- 坐骑 - 特权加成
    self.mount_right_cfg = mount_cfg.privilege


    -- 坐骑升星属性衰减及消耗
    self.mount_fix_fail_count_cfg = ListToMapList(mount_cfg.fix_fail_count, "fix_fail_index")
    self.mount_per_fail_count_cfg = ListToMapList(mount_cfg.per_fail_count, "per_fail_index")
    self.mount_up_star_consume_cfg = ListToMapList(mount_cfg.up_star_consume, "up_star_index")
    self.mount_attr_add_per_cfg = ListToMapList(mount_cfg.attr_add_per, "attr_add_index")

    local lingchong_cfg = ConfigManager.Instance:GetAutoConfig("lingchong_auto")
    -- 灵宠 - 基础
    self.lingchong_base_cfg = ListToMap(lingchong_cfg.image_scale_pos, "grade_num")
    self.lingchong_base_map_appeid_cfg = ListToMap(lingchong_cfg.image_scale_pos, "appe_image_id")
    self.lingchong_sxd_cfg = ListToMap(lingchong_cfg.shuxingdan, "slot_idx")
    self.lingchong_sxd_map_itemid_cfg = ListToMap(lingchong_cfg.shuxingdan, "shuxingdan_id")
    -- self.lingchong_sxd_limit_cfg = ListToMap(lingchong_cfg.shuxingdan_use_limit, "slot_idx")
    self.lingchong_base_upstar_cfg = ListToMap(lingchong_cfg.upstar_level, "star_level")
    self.lingchong_base_upstar_map_appeid_cfg = ListToMap(lingchong_cfg.upstar_level, "appe_image_id", "star_level")
    self.lingchong_base_skill_cfg = ListToMap(lingchong_cfg.skill, "skill_id", "skill_level")
    self.lingchong_base_skill_map_star_cfg = ListToMap(lingchong_cfg.skill, "active_star_level", "skill_level")
    -- 灵宠 - 珍稀
    self.lingchong_act_map_imageid_cfg = ListToMap(lingchong_cfg.special_image_active, "image_id")
    self.lingchong_act_map_itemid_cfg = ListToMap(lingchong_cfg.special_image_active, "active_item_id")
    self.lingchong_act_map_appeid_cfg = ListToMap(lingchong_cfg.special_image_active, "appe_image_id")
    self.lingchong_special_image_skill_cfg = ListToMap(lingchong_cfg.special_image_skill, "image_id", "skill_id")
    self.lingchong_special_upstar_cfg = ListToMap(lingchong_cfg.special_image_up_star, "image_id")
    self.lingchong_special_upgrade_cfg = ListToMap(lingchong_cfg.special_image_upgrade, "image_id", "grade")
    self.lingchong_special_upgrade_item_cfg = ListToMap(lingchong_cfg.image_upgrade_item, "item_id")
    -- 灵宠 - 特权加成
    self.lingchong_right_cfg = lingchong_cfg.privilege

    -- 灵宠 - 气泡
    self.lingchong_bubble_list = ListToMapList(ConfigManager.Instance:GetAutoConfig("bubble_list_auto").pet_talk, "scene_type")

    -- 灵宠升星属性衰减及消耗
    self.lingchong_fix_fail_count_cfg = ListToMapList(lingchong_cfg.fix_fail_count, "fix_fail_index")
    self.lingchong_per_fail_count_cfg = ListToMapList(lingchong_cfg.per_fail_count, "per_fail_index")
    self.lingchong_up_star_consume_cfg = ListToMapList(lingchong_cfg.up_star_consume, "up_star_index")
    self.lingchong_attr_add_per_cfg = ListToMapList(lingchong_cfg.attr_add_per, "attr_add_index")

    -- 鲲
    local kun_cfg = ConfigManager.Instance:GetAutoConfig("huakun_auto")
    self.kun_act_map_id_cfg = ListToMap(kun_cfg.kun_set, "id")
    self.kun_act_map_itemid_cfg = ListToMap(kun_cfg.kun_set, "active_need_item_id")
    self.kun_act_map_appeid_cfg = ListToMap(kun_cfg.kun_set, "active_id")
    self.kun_resolve_map_itemid_cfg = ListToMap(kun_cfg.material, "item_id")
    self.kun_upstar_cfg = ListToMap(kun_cfg.upgrade_star, "id")
    self.kun_upgrade_cfg = ListToMap(kun_cfg.upgrade_lv, "id", "level")
    self.kun_skill_cfg = ListToMap(kun_cfg.kun_skill, "kun_id", "skill_id")
    self.kun_attr_add_per_cfg = ListToMapList(kun_cfg.attr_add_per, "attr_add_index")

    -- 鲲升星属性衰减及消耗
    self.kun_fix_fail_count_cfg = ListToMapList(kun_cfg.fix_fail_count, "fix_fail_index")
    self.kun_per_fail_count_cfg = ListToMapList(kun_cfg.per_fail_count, "per_fail_index")
    self.kun_up_star_consume_cfg = ListToMapList(kun_cfg.up_star_consume, "up_star_index")

    local rm = RemindManager.Instance
    
	rm:Register(RemindName.NewAppearance_Upgrade_Mount, BindTool.Bind(self.GetBaseQiChongAllRemind, self, TabIndex.new_appearance_upgrade_mount))
    rm:Register(RemindName.NewAppearance_Upgrade_LingChong, BindTool.Bind(self.GetBaseQiChongAllRemind, self, TabIndex.new_appearance_upgrade_lingchong))
    rm:Register(RemindName.NewAppearance_Mount_Upstar, BindTool.Bind(self.GetSpecialQiChongUpStarRemind, self, TabIndex.new_appearance_mount_upstar))
    rm:Register(RemindName.NewAppearance_Mount_Upgrade, BindTool.Bind(self.GetSpecialQiChongUpGradeRemind, self, TabIndex.new_appearance_mount_upgrade))
    rm:Register(RemindName.NewAppearance_LingChong_Upstar, BindTool.Bind(self.GetSpecialQiChongUpStarRemind, self, TabIndex.new_appearance_lingchong_upstar))
    rm:Register(RemindName.NewAppearance_LingChong_Upgrade, BindTool.Bind(self.GetSpecialQiChongUpGradeRemind, self, TabIndex.new_appearance_lingchong_upgrade))
    rm:Register(RemindName.NewAppearance_Kun_Upstar, BindTool.Bind(self.GetKunUpStarRemind, self, TabIndex.new_appearance_kun_upstar))
    rm:Register(RemindName.NewAppearance_Kun_Upgrade, BindTool.Bind(self.GetKunUpGradeRemind, self, TabIndex.new_appearance_kun_upgrade))

    self:GetBaseMountStuffRemindList()
    self:GetBaseLingChongStuffRemindList()
    self:GetSpecialMountStuffRemindList()
    self:GetSpecialLingChongStuffRemindList()
    self:GetKunStuffRemindList()
    self:InitUpStarCache()   
end

function NewAppearanceWGData:DeleteQiChongData()
    local rm = RemindManager.Instance
    rm:UnRegister(RemindName.NewAppearance_Upgrade_LingChong)
	rm:UnRegister(RemindName.NewAppearance_Upgrade_Mount)
    rm:UnRegister(RemindName.NewAppearance_Mount_Upstar)
    rm:UnRegister(RemindName.NewAppearance_Mount_Upgrade)
    rm:UnRegister(RemindName.NewAppearance_LingChong_Upstar)
    rm:UnRegister(RemindName.NewAppearance_LingChong_Upgrade)

    self.multi_mount_action_cache = nil
end

function NewAppearanceWGData:InitUpStarCache()
    self.mount_fix_fail_count_cache = self:CalAttrCfgPack(self.mount_fix_fail_count_cfg, "attr_per")
    self.mount_per_fail_count_cache = self:CalAttrCfgPack(self.mount_per_fail_count_cfg, "attr_per")
    self.mount_up_star_consume_cache = self:CalAttrCfgPack(self.mount_up_star_consume_cfg, "consume_num")
    self.mount_attr_add_per_cache = self:CalAttrCfgPack(self.mount_attr_add_per_cfg, "attr_add_per")

    self.lingchong_fix_fail_count_cache = self:CalAttrCfgPack(self.lingchong_fix_fail_count_cfg, "attr_per")
    self.lingchong_per_fail_count_cache = self:CalAttrCfgPack(self.lingchong_per_fail_count_cfg, "attr_per")
    self.lingchong_up_star_consume_cache = self:CalAttrCfgPack(self.lingchong_up_star_consume_cfg, "consume_num")
    self.lingchong_attr_add_per_cache = self:CalAttrCfgPack(self.lingchong_attr_add_per_cfg, "attr_add_per")

    self.kun_fix_fail_count_cache = self:CalAttrCfgPack(self.kun_fix_fail_count_cfg, "attr_per")
    self.kun_per_fail_count_cache = self:CalAttrCfgPack(self.kun_per_fail_count_cfg, "attr_per")
    self.kun_up_star_consume_cache = self:CalAttrCfgPack(self.kun_up_star_consume_cfg, "consume_num")
    self.kun_attr_add_per_cache = self:CalAttrCfgPack(self.kun_attr_add_per_cfg, "attr_add_per")
end

-- 坐骑动作
function NewAppearanceWGData:GetMountActionCfg(mount_id)
	return self.mount_action_cfg[mount_id]
end

function NewAppearanceWGData:GetMountCameraDis(mount_id)
    local cfg = self.mount_action_cfg[mount_id]
    return cfg and cfg.camera_dis or 0
end

-- 坐骑基础升星配置
function NewAppearanceWGData:GetMountBaseUpStarCfg(star_level)
    return self.mount_base_upstar_cfg[star_level]
end

function NewAppearanceWGData:GetMountBaseUpStarCfgByAppeId(appe_id, star_num)
    return (self.mount_base_upstar_map_appeid_cfg[appe_id] or empty_table)[star_num]
end

-- 坐骑技能配置
function NewAppearanceWGData:GetMountBaseSkillCfg(skill_id, skill_level)
    return (self.mount_base_skill_cfg[skill_id] or empty_table)[skill_level]
end

function NewAppearanceWGData:GetMountBaseSkillCfgByStarLevel(star_level, skill_level)
    return (self.mount_base_skill_map_star_cfg[star_level] or empty_table)[skill_level]
end

-- 坐骑属性丹
function NewAppearanceWGData:GetMountSXDCfg(slot)
    return self.mount_sxd_cfg[slot]
end

function NewAppearanceWGData:GetMountSXDCfgByItemId(item_id)
    return self.mount_sxd_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetIsMountSXD(item_id)
    return self.mount_sxd_map_itemid_cfg[item_id] ~= nil
end

-- function NewAppearanceWGData:GetMountSXDLimitCfg(item_id)
--     return self.mount_sxd_limit_cfg[item_id]
-- end

-- 珍稀坐骑激活配置
function NewAppearanceWGData:GetMountActCfgByImageId(image_id)
    return self.mount_act_map_imageid_cfg[image_id]
end

function NewAppearanceWGData:GetMountActCfgByItemId(item_id)
    return self.mount_act_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetMountActCfgByAppeId(appe_id)
    return self.mount_act_map_appeid_cfg[appe_id]
end

-- 珍稀坐骑升星
function NewAppearanceWGData:GetSpecialMountUpStarCfg(image_id)
    return self.mount_special_upstar_cfg[image_id]
    -- return (self.mount_special_upstar_cfg[image_id] or empty_table)[star_level]
end

function NewAppearanceWGData:GetSpecialMountUpStarAttrCfg(image_id, star_level)
    local up_level_cfg = self:GetSpecialMountUpStarCfg(image_id)
    local attr_data = {}

    if IsEmptyTable(up_level_cfg) then
        return attr_data
    end

    if star_level > up_level_cfg.max_up_level then
        return attr_data
    end

    local t_attr_id
    for i = 1, star_level do
        local fix_fail_value = self:GetSpecialMountUpLevelFixFailCfg(up_level_cfg.fix_fail_index, i)
        local per_fail_value = self:GetSpecialMountUpLevelPerFailCfg(up_level_cfg.per_fail_index, i)

        for i = 1, 6 do
            t_attr_id = up_level_cfg["attr_id" .. i]
            if t_attr_id > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(t_attr_id)
                local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str)
                local base_value = up_level_cfg["attr_value" .. i]
                local target_value = 0

                if is_per then
                    target_value = math.floor(base_value * per_fail_value / 10000)
                else
                    target_value = math.floor(base_value * fix_fail_value / 10000)
                end

                if nil == attr_data[attr_str] then
                    attr_data[attr_str] = 0
                end

                attr_data[attr_str] = attr_data[attr_str] + target_value
            end
        end 
    end

    return attr_data
end

function NewAppearanceWGData:GetSpecialMountUpLevelFixFailCfg(index, level)
    return (self.mount_fix_fail_count_cache[index] or {})[level]
end

function NewAppearanceWGData:GetSpecialMountUpLevelPerFailCfg(index, level)
    return (self.mount_per_fail_count_cache[index] or {})[level]
end

-- 珍稀坐骑技能
function NewAppearanceWGData:GetSpecialMountSkillCfg(image_id, skill_id)
    return (self.mount_special_image_skill_cfg[image_id] or empty_table)[skill_id]
end

function NewAppearanceWGData:GetSpecialMountSkillList(image_id)
    return self.mount_special_image_skill_cfg[image_id] or {}
end

-- 珍稀坐骑升阶
function NewAppearanceWGData:GetSpecialMountUpGradeCfg(image_id, grade)
    return (self.mount_special_upgrade_cfg[image_id] or empty_table)[grade]
end

-- 珍稀坐骑最大阶数
function NewAppearanceWGData:GetSpeicalMountMaxGrade()
    local cfg = self.mount_special_upgrade_cfg[0]
    return cfg and cfg[#cfg].grade or 0
end

-- 是否飞行坐骑
function NewAppearanceWGData:GetIsFlyMount(appe_id)
    local cfg = self:GetMountBaseUpStarCfgByAppeId(appe_id, 1)
    if not cfg then
        cfg = self:GetMountActCfgByAppeId(appe_id)
    end

    return cfg and cfg.is_fly_mount == 1
end

-- 是否坐骑（含鲲）在场景只播Idle动画
function NewAppearanceWGData:GetIsOnlyPlayIdelAniInScene(appe_id)
    local cfg = self:GetMountBaseUpStarCfgByAppeId(appe_id, 1)
    -- 特殊坐骑
    if not cfg then
        cfg = self:GetMountActCfgByAppeId(appe_id)
    end

    -- 鲲
    if not cfg then
        cfg = self:GetKunActCfgByAppeId(appe_id)
    end

    return cfg and cfg.no_run_only_idle == 1
end

-- 坐骑音效(鲲无音效)
function NewAppearanceWGData:GetMountAudio(appe_image_id)
    local cfg = self.mount_base_map_appeid_cfg[appe_image_id]
    if not cfg then
        cfg = self:GetMountActCfgByAppeId(appe_image_id)
    end

	if cfg and cfg.audio_name ~= "" then
        return cfg.audio_name
    end

    return nil
end

-- 基础灵宠升星配置
function NewAppearanceWGData:GetLingChongBaseUpStarCfg(star_level)
    return self.lingchong_base_upstar_cfg[star_level]
end

function NewAppearanceWGData:GetLingChongBaseUpStarCfgByAppeId(appe_id, star_num)
    return (self.lingchong_base_upstar_map_appeid_cfg[appe_id] or empty_table)[star_num]
end

-- 基础灵宠技能配置
function NewAppearanceWGData:GetLingChongBaseSkillCfg(skill_id, skill_level)
    return (self.lingchong_base_skill_cfg[skill_id] or empty_table)[skill_level]
end

function NewAppearanceWGData:GetLingChongBaseSkillCfgByStarLevel(star_level, skill_level)
    return (self.lingchong_base_skill_map_star_cfg[star_level] or empty_table)[skill_level]
end

-- 灵宠属性丹
function NewAppearanceWGData:GetLingChongSXDCfg(slot)
    return self.lingchong_sxd_cfg[slot]
end

function NewAppearanceWGData:GetLingChongSXDCfgByItemId(item_id)
    return self.lingchong_sxd_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetIsLingChongSXD(item_id)
    return self.lingchong_sxd_map_itemid_cfg[item_id] ~= nil
end

-- 珍稀灵宠激活配置
function NewAppearanceWGData:GetLingChongActCfgByImageId(image_id)
    return self.lingchong_act_map_imageid_cfg[image_id]
end

function NewAppearanceWGData:GetLingChongActCfgByItemId(item_id)
    return self.lingchong_act_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetLingChongActCfgByAppeId(appe_id)
    return self.lingchong_act_map_appeid_cfg[appe_id]
end

-- 珍稀灵宠技能
function NewAppearanceWGData:GetSpecialLingChongSkillCfg(image_id, skill_id)
    return (self.lingchong_special_image_skill_cfg[image_id] or empty_table)[skill_id]
end

function NewAppearanceWGData:GetSpecialLingChongSkillList(image_id)
    return self.lingchong_special_image_skill_cfg[image_id] or {}
end

-- 珍稀灵宠升星
function NewAppearanceWGData:GetSpecialLingChongUpStarCfg(image_id) -- , star_level
    return self.lingchong_special_upstar_cfg[image_id]
    -- return (self.lingchong_special_upstar_cfg[image_id] or empty_table)[star_level]
end

-- 灵宠每级得属性
function NewAppearanceWGData:GetSpecialLingChongUpStarAttrCfg(id, star_level)
    local up_level_cfg = self:GetSpecialLingChongUpStarCfg(id)
    local attr_data = {}

    if not up_level_cfg or star_level > up_level_cfg.max_up_level then
        return attr_data
    end

    local t_attr_id
    for i = 1, star_level do
        local fix_fail_value = self:GetSpecialLingChongUpLevelFixFailCfg(up_level_cfg.fix_fail_index, i)
        local per_fail_value = self:GetSpecialLingChongUpLevelPerFailCfg(up_level_cfg.per_fail_index, i)
        for i = 1, 6 do
            t_attr_id = up_level_cfg["attr_id" .. i]
            if t_attr_id > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(t_attr_id)
                local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str)
                local base_value = up_level_cfg["attr_value" .. i]
                local target_value = 0

                if is_per then
                    target_value = math.floor(base_value * per_fail_value / 10000)
                else
                    target_value = math.floor(base_value * fix_fail_value / 10000)
                end

                if nil == attr_data[attr_str] then
                    attr_data[attr_str] = 0
                end

                attr_data[attr_str] = attr_data[attr_str] + target_value
            end
        end 
    end

    return attr_data
end

function NewAppearanceWGData:GetSpecialLingChongUpLevelFixFailCfg(index, level)
    return (self.lingchong_fix_fail_count_cache[index] or {})[level]
end

function NewAppearanceWGData:GetSpecialLingChongUpLevelPerFailCfg(index, level)
    return (self.lingchong_per_fail_count_cache[index] or {})[level]
end

-- 珍稀灵宠升阶
function NewAppearanceWGData:GetSpecialLingChongUpGradeCfg(image_id, grade)
    return (self.lingchong_special_upgrade_cfg[image_id] or empty_table)[grade]
end

-- 珍稀灵宠最大阶数
function NewAppearanceWGData:GetSpeicalLingChongMaxGrade()
    local cfg = self.lingchong_special_upgrade_cfg[0]
    return cfg and cfg[#cfg].grade or 0
end

-- 灵宠气泡
function NewAppearanceWGData:GetLingChongBubbleList(scene_type)
	return self.lingchong_bubble_list[scene_type] or self.lingchong_bubble_list[0]
end

-- 鲲激活配置
function NewAppearanceWGData:GetKunActCfgById(image_id)
    return self.kun_act_map_id_cfg[image_id]
end

function NewAppearanceWGData:GetKunActCfgByItemId(item_id)
    local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_id)
    if not IsEmptyTable(compose_cfg) then
        item_id = compose_cfg.product_id
    end

    return self.kun_act_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetKunActCfgByAppeId(appe_id)
    return self.kun_act_map_appeid_cfg[appe_id]
end


-- 鲲-材料
function NewAppearanceWGData:GetKunStuffCfgByItemId(item_id)
    return self.kun_resolve_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetKunMaterial()
	return ConfigManager.Instance:GetAutoConfig("huakun_auto").material
end

-- 鲲 升星
function NewAppearanceWGData:GetKunUpStarCfg(id) -- , star_level
    return self.kun_upstar_cfg[id]
end

-- 鲲每级得属性
function NewAppearanceWGData:GetKunUpStarAttrCfg(id, star_level)
    local up_level_cfg = self:GetKunUpStarCfg(id)
    local attr_data = {}

    if star_level > up_level_cfg.max_up_level then
        return attr_data
    end

    local t_attr_id
    for i = 1, star_level do
        local fix_fail_value = self:GetKunUpLevelFixFailCfg(up_level_cfg.fix_fail_index, i)
        local per_fail_value = self:GetKunUpLevelPerFailCfg(up_level_cfg.per_fail_index, i)

        for i = 1, 6 do
            t_attr_id = up_level_cfg["attr_id" .. i]
            if t_attr_id > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(t_attr_id)
                local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_str)
                local base_value = up_level_cfg["attr_value" .. i]
                local target_value = 0

                if is_per then
                    target_value = math.floor(base_value * per_fail_value / 10000)
                else
                    target_value = math.floor(base_value * fix_fail_value / 10000)
                end

                if nil == attr_data[attr_str] then
                    attr_data[attr_str] = 0
                end

                attr_data[attr_str] = attr_data[attr_str] + target_value
            end
        end 
    end

    return attr_data
end

function NewAppearanceWGData:GetKunUpLevelFixFailCfg(index, level)
    return (self.kun_fix_fail_count_cache[index] or {})[level]
end

function NewAppearanceWGData:GetKunUpLevelPerFailCfg(index, level)
    return (self.kun_per_fail_count_cache[index] or {})[level]
end

function NewAppearanceWGData:GetKunMaxStar(id)
    return (self.kun_upstar_cfg[id] or {}).max_up_level or 0
end

-- 鲲 升阶
function NewAppearanceWGData:GetKunUpGradeCfg(id, grade)
    return (self.kun_upgrade_cfg[id] or empty_table)[grade]
end

function NewAppearanceWGData:GetKunMaxGrade()
    local cfg = self.kun_upgrade_cfg[1]
    return cfg and cfg[#cfg].level or 0
end

-- 鲲 技能
function NewAppearanceWGData:GetKunSkillCfg(id, skill_id)
    return (self.kun_skill_cfg[id] or empty_table)[skill_id]
end

function NewAppearanceWGData:GetKunSkillList(id)
    return self.kun_skill_cfg[id] or {}
end

-- 坐骑信息
function NewAppearanceWGData:SetMountInfo(protocol)
    self.had_mount_protocol_init = true

    self.mount_info = {}
    self.mount_info.mount_flag = protocol.mount_flag
	self.mount_info.star_level = protocol.star_level
    self.mount_info.grade = protocol.grade
	self.mount_info.upstar_bless_val = protocol.upstar_bless_val
	self.mount_info.used_imageid = protocol.used_imageid
	self.mount_info.active_skill_flag = protocol.active_skill_flag
	self.mount_info.shuxingdan_list = protocol.shuxingdan_list
    self.mount_info.special_image_list = protocol.special_image_list
    self.mount_info.skill_level_list = protocol.skill_level_list
    self.mount_info.right_flag = protocol.right_flag

    local list = self.need_check_qichong_quick_tab[MOUNT_LINGCHONG_APPE_TYPE.MOUNT] or {}
    for item_id, index in pairs(list) do
        FunctionGuide.Instance:OnItemDataChange(item_id, index, GameEnum.DATALIST_CHANGE_REASON_CHECK, PUT_REASON_TYPE.PUT_REASON_ChECK_TIP)
    end
    self.need_check_qichong_quick_tab[MOUNT_LINGCHONG_APPE_TYPE.MOUNT] = {}
end

-- 乘骑状态
function NewAppearanceWGData:GetMountRideState()
    return self.mount_info.mount_flag
end

-- 灵宠信息
function NewAppearanceWGData:SetLingChongInfo(protocol)
    self.had_lingchong_protocol_init = true

    self.qichong_info = {}
    self.qichong_info.star_level = protocol.star_level
    self.qichong_info.grade = protocol.grade
	self.qichong_info.upstar_bless_val = protocol.upstar_bless_val
	self.qichong_info.used_imageid = protocol.used_imageid
	self.qichong_info.active_skill_flag = protocol.active_skill_flag
	self.qichong_info.shuxingdan_list = protocol.shuxingdan_list
    self.qichong_info.special_image_list = protocol.special_image_list
    self.qichong_info.skill_level_list = protocol.skill_level_list
    self.qichong_info.right_flag = protocol.right_flag

    local list = self.need_check_qichong_quick_tab[MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG] or {}
    for item_id, index in pairs(list) do
        FunctionGuide.Instance:OnItemDataChange(item_id, index, GameEnum.DATALIST_CHANGE_REASON_CHECK, PUT_REASON_TYPE.PUT_REASON_ChECK_TIP)
    end
    self.need_check_qichong_quick_tab[MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG] = {}
end

-- 鲲信息
function NewAppearanceWGData:SetKunAllInfo(protocol)
    self.had_kun_protocol_init = true
    self.kun_info = protocol.kun_info
    self.kun_used_id = protocol.kun_used_id

    local list = self.need_check_qichong_quick_tab[MOUNT_LINGCHONG_APPE_TYPE.KUN] or {}
    for item_id, index in pairs(list) do
        FunctionGuide.Instance:OnItemDataChange(item_id, index, GameEnum.DATALIST_CHANGE_REASON_CHECK, PUT_REASON_TYPE.PUT_REASON_ChECK_TIP)
    end
    self.need_check_qichong_quick_tab[MOUNT_LINGCHONG_APPE_TYPE.KUN] = {}
end

-- 鲲单个信息
function NewAppearanceWGData:SetKunChange(protocol)
    local info = protocol.info
    self.kun_info[info.id] = info
    if info.is_used and self.kun_used_id ~= info.id then
        self.kun_used_id = info.id
    elseif not info.is_used and self.kun_used_id == info.id then
        self.kun_used_id = -1
    end
end

-- 鲲 经验
function NewAppearanceWGData:SetKunUpGradeExp(exp)
    self.kun_upgrade_exp = exp
end

function NewAppearanceWGData:GetKunUpGradeExp()
    return self.kun_upgrade_exp
end

function NewAppearanceWGData:GetKunInfo(id)
    return self.kun_info[id]
end

function NewAppearanceWGData:GetKunInfoByItemId(item_id)
    local cfg = self:GetKunActCfgByItemId(item_id)
    if cfg then
        return self:GetKunInfo(cfg.id)
    end

    return nil
end

-- 鲲 已使用id
function NewAppearanceWGData:GetKunUsedId()
    return self.kun_used_id
end

-- 骑宠信息
function NewAppearanceWGData:GetQiChongInfo(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		return self.mount_info
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self.qichong_info
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self.kun_info
    end
    
    return nil
end

-- 骑宠所有配置
function NewAppearanceWGData:GetQiChongAllCfg(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return ConfigManager.Instance:GetAutoConfig("mount_auto")
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return ConfigManager.Instance:GetAutoConfig("lingchong_auto")
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return ConfigManager.Instance:GetAutoConfig("huakun_auto")
    end
end

-- 骑宠信息 - 星级
function NewAppearanceWGData:GetBaseQiChongStarLevel(qc_type)
    local info = self:GetQiChongInfo(qc_type)
    return info and info.star_level or 0
end

-- 骑宠信息 - 阶数
function NewAppearanceWGData:GetBaseQiChongGrade(qc_type)
    local info = self:GetQiChongInfo(qc_type)
    return info and info.grade or 0
end

-- 骑宠信息 - 属性丹使用个数
function NewAppearanceWGData:GetQiChongSXDUsedNum(qc_type, slot)
    local info = self:GetQiChongInfo(qc_type)
    return ((info or empty_table)["shuxingdan_list"] or empty_table)[slot] or 0
end

-- 骑宠信息 - 技能等级
function NewAppearanceWGData:GetBaseQiChongSkillLevel(qc_type, skill_id)
    local info = self:GetQiChongInfo(qc_type)
    return ((info or {})["skill_level_list"] or {})[skill_id] or 0
end


-- 基础骑宠升星物品列表
function NewAppearanceWGData:GetQiChongBaseUpStarStuff(qc_type)
    local all_cfg = self:GetQiChongAllCfg(qc_type)
    return all_cfg and all_cfg.upstar_item
end

-- 骑宠基础配置
function NewAppearanceWGData:GetQiChongBaseCfgByGrade(qc_type, grade)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		return self.mount_base_cfg[grade]
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self.lingchong_base_cfg[grade]
    end
end

-- 基础骑宠升星配置
function NewAppearanceWGData:GetQiChongBaseUpStarCfg(qc_type, star_level)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		return self:GetMountBaseUpStarCfg(star_level)
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetLingChongBaseUpStarCfg(star_level)
    end
end

function NewAppearanceWGData:GetQiChongBaseCfgByAppeId(qc_type, appe_image_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		return self.mount_base_map_appeid_cfg[appe_image_id]
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self.lingchong_base_map_appeid_cfg[appe_image_id]
    end
end

-- 基础骑宠最大阶数
function NewAppearanceWGData:GetQiChongBaseMaxGrade(qc_type)
    local cfg
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		cfg = self.mount_base_cfg
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        cfg = self.lingchong_base_cfg
    end

    return cfg and cfg[#cfg].grade_num or 0
end

-- 基础骑宠名
function NewAppearanceWGData:GetBaseQiChongName(qc_type, grade)
    local cfg = self:GetQiChongBaseCfgByGrade(qc_type, grade)
    return cfg and cfg.image_name or ""
end

-- 基础骑宠技能配置
function NewAppearanceWGData:GetBaseQiChongSkillCfg(qc_type, skill_id, skill_level)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		return self:GetMountBaseSkillCfg(skill_id, skill_level)
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetLingChongBaseSkillCfg(skill_id, skill_level)
    end

    return nil
end

-- 基础骑宠技能名
function NewAppearanceWGData:GetBaseQiChongSkillName(qc_type, skill_id)
    local cfg = self:GetBaseQiChongSkillCfg(qc_type, skill_id, 1)
    return cfg and cfg.skill_name or ""
end

-- 基础骑宠 - 属性丹配置
function NewAppearanceWGData:GetQiChongSXDCfgByItemId(qc_type, item_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetMountSXDCfgByItemId(item_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetLingChongSXDCfgByItemId(item_id)
    end
end

-- 获取骑宠名字
function NewAppearanceWGData:GetQiChongNameByAppeId(qc_type, appe_image_id)
	local name_color = COLOR3B.D_PURPLE
    local cfg = self:GetQiChongBaseCfgByAppeId(qc_type, appe_image_id)
    if cfg then
        return cfg.image_name, name_color
    end

    cfg = self:GetSpecialQiChongActCfgByAppeId(qc_type, appe_image_id)
    if cfg then
        return cfg.image_name, name_color
    end

	return "", name_color
end

-- 骑宠属性丹列表
function NewAppearanceWGData:GetQiChongSXDlist(qc_type)
    local all_cfg = self:GetQiChongAllCfg(qc_type)
    local sxd_cfg = all_cfg and all_cfg.shuxingdan
    local list = {}
    if sxd_cfg == nil then
        return list
    end

    local info = self:GetQiChongInfo(qc_type)
    if info == nil then
        return list
    end

    local upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, info.star_level)
    if upstar_cfg == nil then
        return list
    end

    for k,v in ipairs(sxd_cfg) do
        local data = {cfg = v}
        data.is_open = v.open_grade <= upstar_cfg.grade_num
        data.used_num = info.shuxingdan_list[v.slot_idx] or 0
        data.had_num = ItemWGData.Instance:GetItemNumInBagById(v.shuxingdan_id)
        data.is_remind = data.had_num > 0 and data.is_open
        data.qc_type = qc_type
        table.insert(list, data)
    end

    return list
end

-- 骑宠基础技能列表
function NewAppearanceWGData:GetBaseQiChongSkillList(qc_type)
    local cfg
    local skill_list = {}
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		cfg = self.mount_base_skill_cfg
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        cfg = self.lingchong_base_skill_cfg
    end

    local info = self:GetQiChongInfo(qc_type)
    if not cfg or not info then
        return skill_list
    end

    local star_level = info.star_level or -1
    local skill_level, cfg_level = 0, 0
    for skill_id = 0, #cfg do
        skill_level = self:GetBaseQiChongSkillLevel(qc_type, skill_id)
        cfg_level = skill_level > 0 and skill_level or 1
        local cfg_data = cfg[skill_id][cfg_level]

        if cfg_data then
            local is_remind = false
            local next_skill_cfg = self:GetBaseQiChongSkillCfg(qc_type, skill_id, skill_level + 1)
            if next_skill_cfg then
                local uplevel_item_id = next_skill_cfg.uplevel_item_id or next_skill_cfg.item_id or 0
                local uplevel_item_num = next_skill_cfg.uplevel_item_num or next_skill_cfg.num or 9999
                local had_num = ItemWGData.Instance:GetItemNumInBagById(uplevel_item_id)
                is_remind = had_num >= uplevel_item_num
            end

            local data = {cfg = cfg_data}
            data.skill_id = cfg_data.skill_id or 0
            data.skill_level = cfg_level
            data.is_act = star_level >= (cfg_data.active_star_level or 0)
            data.qc_type = qc_type
            data.is_remind = is_remind and data.is_act
            table.insert(skill_list, data)
        end
    end

    return skill_list
end

-- 骑宠特权配置
function NewAppearanceWGData:GetQiChongRightCfg(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self.mount_right_cfg[0]
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self.lingchong_right_cfg[0]
    end
end

-- 骑宠特权是否激活
function NewAppearanceWGData:GetQiChongRightIsAct(qc_type)
    local info = self:GetQiChongInfo(qc_type)
    return info and info.right_flag and info.right_flag[0] == 1
end





-- 珍稀骑宠信息
function NewAppearanceWGData:GetSpecialQiChongInfo(qc_type, image_id)
    local info = self:GetQiChongInfo(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return (info or empty_table)[image_id]
    else
        return ((info or empty_table)["special_image_list"] or empty_table)[image_id]
    end
end

-- 珍稀骑宠星级
function NewAppearanceWGData:GetSpecialQiChongStarLevel(qc_type, image_id)
    local info = self:GetQiChongInfo(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return ((info or empty_table)[image_id] or empty_table)["star"] or 0
    else
        return (((info or empty_table)["special_image_list"] or empty_table)[image_id] or empty_table)["star_level"] or 0
    end
end

-- 珍稀骑宠阶数
function NewAppearanceWGData:GetSpecialQiChongGrade(qc_type, image_id)
    local info = self:GetQiChongInfo(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return ((info or empty_table)[image_id] or empty_table)["level"] or -1
    else
        return (((info or empty_table)["special_image_list"] or empty_table)[image_id] or empty_table)["grade"] or 0
    end
end

function NewAppearanceWGData:GetSpecialQiChongGradeExp(qc_type, image_id)
    local info = self:GetQiChongInfo(qc_type)
    return (((info or empty_table)["special_image_list"] or empty_table)[image_id] or empty_table)["grade_exp_val"] or 0
end

-- 珍稀骑宠技能是否激活
function NewAppearanceWGData:GetSpecialQiChongSkillIsAct(qc_type, image_id, skill_id)
    local info = self:GetQiChongInfo(qc_type)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        local grade = ((info or empty_table)[image_id] or empty_table)["level"] or -1
        local skill_cfg = self:GetKunSkillCfg(image_id, skill_id)
        if skill_cfg then
            return grade >= skill_cfg.active_grade
        end
    else
        return ((((info or empty_table)["special_image_list"] or empty_table)[image_id] or empty_table)["skill_active_flag"] or empty_table)[skill_id] == 1
    end

    return false
end

-- 骑宠激活配置 by 形象id
function NewAppearanceWGData:GetSpecialQiChongActCfgByImageId(qc_type, image_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetMountActCfgByImageId(image_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetLingChongActCfgByImageId(image_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunActCfgById(image_id)
    end

    return nil
end

-- 骑宠激活配置 by 资源id
function NewAppearanceWGData:GetSpecialQiChongActCfgByAppeId(qc_type, appe_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetMountActCfgByAppeId(appe_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetLingChongActCfgByAppeId(appe_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunActCfgByAppeId(appe_id)
    end

    return nil
end

-- 骑宠激活配置 by 物品id
function NewAppearanceWGData:GetSpecialQiChongActCfgByItemId(qc_type, item_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetMountActCfgByItemId(item_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetLingChongActCfgByItemId(item_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunActCfgByItemId(item_id)
    end

    return nil
end

-- 珍稀骑宠 品质名
function NewAppearanceWGData:GetQiChongSpecialImageName(qc_type, image_id)
	local name_color = COLOR3B.D_PURPLE
	local cfg = self:GetSpecialQiChongActCfgByImageId(qc_type, image_id)
	if nil == cfg then
		return "", name_color
	end

    local active_item_id = cfg.active_item_id or cfg.active_need_item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(active_item_id)
	return cfg.image_name, ITEM_TIP_COLOR[item_cfg.color]
end

-- 珍稀骑宠升星配置
function NewAppearanceWGData:GetSpecialQiChongUpStarCfg(qc_type, image_id) --, star_level
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetSpecialMountUpStarCfg(image_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetSpecialLingChongUpStarCfg(image_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunUpStarCfg(image_id)
    end

    return nil
end

-- 珍稀骑宠升星等级属性
function NewAppearanceWGData:GetSpecialQiChongUpStarAttrCfg(qc_type, image_id, star_level)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetSpecialMountUpStarAttrCfg(image_id, star_level)
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetSpecialLingChongUpStarAttrCfg(image_id, star_level)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunUpStarAttrCfg(image_id, star_level)
    end

    return {}
end

-- 升星消耗数量
function NewAppearanceWGData:GetSpecialQiChongUpStarCostNum(qc_type, image_id, star_level)
    local cfg = self:GetSpecialQiChongUpStarCfg(qc_type, image_id)
    local up_star_index = cfg.up_star_index
    local tb_temp, cost_num = {}, 0
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        tb_temp = self.mount_up_star_consume_cache[up_star_index] or {}
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        tb_temp = self.lingchong_up_star_consume_cache[up_star_index] or {}
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        tb_temp = self.kun_up_star_consume_cache[up_star_index] or {}
    end
    cost_num = tb_temp[star_level] or 0
    return cost_num
end

-- 珍稀骑宠最大星级
function NewAppearanceWGData:GetSpecialQiChongMaxStarLevel(qc_type, image_id)
    local cfg = self:GetSpecialQiChongUpStarCfg(qc_type, image_id)
    return cfg and cfg.max_up_level or 0
end

-- 珍稀骑宠各个等级得属性加成
function NewAppearanceWGData:GetSpecialQiChongAttrAddPer(qc_type, image_id, level)
    local cfg = self:GetSpecialQiChongUpStarCfg(qc_type, image_id)
    if IsEmptyTable(cfg) then return 0 end
    local attr_add_index = cfg.attr_add_index
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		return (self.mount_attr_add_per_cache[attr_add_index] or {})[level]
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return (self.lingchong_attr_add_per_cache[attr_add_index] or {})[level]
	elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return (self.kun_attr_add_per_cache[attr_add_index] or {})[level]
    end
    return 0
end

-- 珍稀骑宠升阶物品列表
function NewAppearanceWGData:GetSpecialQiChongUpGradeStuff(qc_type)
    local all_cfg = self:GetQiChongAllCfg(qc_type)
    return all_cfg and all_cfg.image_upgrade_item
end

-- 珍稀骑宠 升阶道具经验
function NewAppearanceWGData:GetSpecialQiChongGradeItemExp(qc_type, item_id)
    local cfg
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        cfg = self.mount_special_upgrade_item_cfg[item_id]
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        cfg = self.lingchong_special_upgrade_item_cfg[item_id]
    end

    return cfg and cfg.add_exp or 0
end

-- 珍稀骑宠 升阶配置
function NewAppearanceWGData:GetSpecialQiChongUpGradeCfg(qc_type, image_id, grade)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetSpecialMountUpGradeCfg(image_id, grade)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetSpecialLingChongUpGradeCfg(image_id, grade)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunUpGradeCfg(image_id, grade)
    end

    return nil
end

function NewAppearanceWGData:GetSpecialQiChongUpGradeCfgByAppeId(qc_type, appe_id, grade)
    local cfg, image_id
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        cfg = self:GetMountActCfgByAppeId(appe_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        cfg = self:GetLingChongActCfgByAppeId(appe_id)
    end

    image_id = cfg and cfg.image_id
    return self:GetSpecialQiChongUpGradeCfg(qc_type, image_id, grade)
end

-- 珍稀骑宠 是否激活
function NewAppearanceWGData:GetSpecialQiChongIsAct(qc_type, image_id)
    local grade = 0
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        local info = self:GetKunInfo(image_id)
        return info and info.is_act
    else
        grade = self:GetSpecialQiChongGrade(qc_type, image_id)
        return grade > 0
    end

    return false
end

-- 珍稀骑宠 技能列表
function NewAppearanceWGData:GetSpecialQiChongSkillList(qc_type, image_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        return self:GetSpecialMountSkillList(image_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        return self:GetSpecialLingChongSkillList(image_id)
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        return self:GetKunSkillList(image_id)
    end
end

-- 珍稀骑宠类型、形象id by 物品 id
function NewAppearanceWGData:GetQiChongTypeByItemId(item_id)
    local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_id)
    if not IsEmptyTable(compose_cfg) then
        item_id = compose_cfg.product_id
    end

    local cfg = self.mount_act_map_itemid_cfg[item_id]
    if cfg then
        return MOUNT_LINGCHONG_APPE_TYPE.MOUNT, cfg.image_id, cfg
    end

    cfg = self.lingchong_act_map_itemid_cfg[item_id]
    if cfg then
        return MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, cfg.image_id, cfg
    end

    cfg = self.kun_act_map_itemid_cfg[item_id]
    if cfg then
        return MOUNT_LINGCHONG_APPE_TYPE.KUN, cfg.id, cfg
    end

    return -1, -1, nil
end

-- 珍稀骑宠 是否激活 by 物品 id
function NewAppearanceWGData:GetQiChongIsActByItemId(item_id)
    local qc_type, image_id = self:GetQiChongTypeByItemId(item_id)
    return self:GetSpecialQiChongIsAct(qc_type, image_id)
end

-- 珍稀骑宠 资源id 激活属性配置
function NewAppearanceWGData:GetQiChongResIdAndActAttrCfg(item_id)
    local compose_cfg = ComposeWGData.Instance:GetComposeCfgByStuffId1(item_id)
	if not IsEmptyTable(compose_cfg) then
		item_id = compose_cfg.product_id
	end
    
    local qc_type, image_id = self:GetQiChongTypeByItemId(item_id)
    local act_cfg = self:GetSpecialQiChongActCfgByItemId(qc_type, item_id)

    local res_id = 0
    local act_attr_cfg
    if act_cfg then
        if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
            res_id = act_cfg.active_id
            act_attr_cfg = self:GetKunUpGradeCfg(image_id, 0)
        else
            res_id = act_cfg.appe_image_id
            act_attr_cfg = act_cfg
        end

        return res_id, act_attr_cfg
    end
end

-- 骑宠显示限制
function NewAppearanceWGData:GetQiChongItemIsCanShow(qc_type, data)
	if not data then
		return false
	end

    local active_item_id = data.active_item_id or data.active_need_item_id
	local num = ItemWGData.Instance:GetItemNumInBagById(active_item_id)
	if num > 0 then
		return true
	end

    local image_id = data.image_id or data.id
	local is_act = self:GetSpecialQiChongIsAct(qc_type, image_id)
	if is_act then
		return true
	end

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance:GetRoleLevel()
    if data.is_default_show == 1 then
        if data.condition_type == 2 then
			return open_day >= data.skynumber_show and role_level >= data.level_show
		else
			return open_day >= data.skynumber_show or role_level >= data.level_show
		end
    end

    return false
end

-- 珍稀骑宠显示列表 - 清除
function NewAppearanceWGData:ClearQiChongSpecialShowList(qc_type)
    if qc_type then
        self.qichong_special_show_list[qc_type] = nil
    else
        self.qichong_special_show_list = {}
    end
end

-- 珍稀骑宠显示列表
function NewAppearanceWGData:GetQiChongSpecialShowList(qc_type)
    local list = self.qichong_special_show_list[qc_type]
    if list then
        return list
    end

    local all_cfg = self:GetQiChongAllCfg(qc_type)
    if all_cfg == nil then
        return {}
    end

    local cfg
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        cfg = all_cfg.mount_image_active
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
        cfg = all_cfg.special_image_active
    elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        cfg = all_cfg.kun_set
    end

    if cfg == nil then
        return {}
    end

    local show_list = {}
    for k,v in ipairs(cfg) do
        local active_item_id = v.active_item_id or v.active_need_item_id
        local item_cfg = ItemWGData.Instance:GetItemConfig(active_item_id)
        local is_show = self:GetQiChongItemIsCanShow(qc_type, v)
        if is_show then
            local data = {
                image_id = v.image_id or v.id,
                appe_image_id = v.appe_image_id or 0,
                active_item_id = active_item_id,
                qc_type = qc_type,
                color = item_cfg and item_cfg.color or 0,
                func_type = 0,
            }
            table.insert(show_list, data)
        end
    end

    self.qichong_special_show_list[qc_type] = show_list
    return show_list
end

-- 珍稀骑宠 view列表
function NewAppearanceWGData:GetQiChongSortSpecialShowList(qc_type, func_type)
    local show_list = self:GetQiChongSpecialShowList(qc_type)
    local is_act, act_weight, color_weight
    for k,v in pairs(show_list) do
        is_act = self:GetSpecialQiChongIsAct(qc_type, v.image_id)
        v.func_type = func_type
        v.is_remind = self:GetSpecialQiChongSingleRmindByType(func_type, qc_type, v.image_id)
        act_weight = (v.is_remind or is_act) and 0 or 1000
        local item_cfg = ItemWGData.Instance:GetItemConfig(v.active_item_id)
        color_weight = item_cfg and item_cfg.color * 100 or 0
        v.sort = act_weight + color_weight + v.image_id
    end

    SortTools.SortAsc(show_list, "sort")
    return show_list
end

function NewAppearanceWGData:GetQCSortSpecialShowList(qc_type, func_type)
    local show_list = self:GetQiChongSpecialShowList(qc_type)

    for k,v in pairs(show_list) do
        v.func_type = func_type
        v.is_remind = self:GetSpecialQiChongSingleRmindByType(func_type, qc_type, v.image_id)
    end

    local data_list = {}
    for k,v in pairs(show_list) do
        local color = v.color

        if not data_list[color] then
            data_list[color] = {}
        end

        table.insert(data_list[color], v)
    end

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if not IsEmptyTable(v) then
               for i, u in pairs(v) do
                local is_act = self:GetSpecialQiChongIsAct(qc_type, u.image_id)
                local act_weight = 1000000

                if not is_act and u.is_remind then
                    act_weight = 0
                elseif is_act or u.is_remind then
                    act_weight = 1000
                end

                local item_cfg = ItemWGData.Instance:GetItemConfig(u.active_item_id)
                local color_weight = item_cfg and item_cfg.color * 10000 or 0
                u.sort = act_weight + color_weight + u.image_id    
               end 
            end

            SortTools.SortAsc(v, "sort")
        end
    end

    return data_list
end

-- 鲲 - 分解列表
function NewAppearanceWGData:GetKunResloveBagList()
	local huakun_bag_data = {}
	local bag_stuff_list = ItemWGData.Instance:GetStuffStorgeItemData()
	for k,v in pairs(bag_stuff_list) do
		if self:GetKunStuffCfgByItemId(v.item_id) then
			table.insert(huakun_bag_data, v)
		end
	end

	return huakun_bag_data
end

--====================  数据 end ===========================
--===========================================================

--===========================================================
--====================  属性 战力   ==========================
-- 百分比属性
function NewAppearanceWGData:GetSpecialQiChongPerAddAttr(qc_type, image_id, level)
    local attr_data = {}
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        local cfg = self:GetKunUpStarCfg(image_id)
        local attr_add_index = cfg.attr_add_index
        if not attr_add_index then
            return attr_data
        end
        local add_per_value = 0
        local next_add_per_value = 0
        for i = 1, level + 1 do
            if i <= level then
                add_per_value = add_per_value + self:GetSpecialQiChongAttrAddPer(qc_type, image_id, i)
            end
            next_add_per_value = next_add_per_value + (self:GetSpecialQiChongAttrAddPer(qc_type, image_id, i) or 0)
        end
    
        local attr_title_str = string.format(Language.NewAppearance.AttrAddPer, Language.NewAppearance.MountPerAttrGroup[qc_type])
        attr_data.attr_name = ToColorStr(attr_title_str, COLOR3B.GLOD)
        attr_data.is_per = true
        attr_data.cur_value = add_per_value 
        attr_data.attr_next_value = next_add_per_value
        if next_add_per_value > add_per_value then
            attr_data.add_value = next_add_per_value - add_per_value
        end
    else
        local cfg = self:GetSpecialQiChongUpStarCfg(qc_type, image_id)
        local attr_add_index = cfg.attr_add_index
        if not attr_add_index then
            return attr_data
        end
        local add_per_value = 0
        local next_add_per_value = 0
        for i = 1, level + 1 do
            if i <= level then
                add_per_value = add_per_value + self:GetSpecialQiChongAttrAddPer(qc_type, image_id, i)
            end
            next_add_per_value = next_add_per_value + (self:GetSpecialQiChongAttrAddPer(qc_type, image_id, i) or 0)
        end
    
        local attr_title_str = string.format(Language.NewAppearance.AttrAddPer, Language.NewAppearance.MountPerAttrGroup[qc_type])
        attr_data.attr_name = ToColorStr(attr_title_str, COLOR3B.GLOD)
        attr_data.is_per = true
        attr_data.cur_value = add_per_value 
        attr_data.attr_next_value = next_add_per_value
        if next_add_per_value > add_per_value then
            attr_data.add_value = next_add_per_value - add_per_value
        end
    end
    return attr_data
end


-- 基础骑宠 属性列表 / 战力
function NewAppearanceWGData:GetBaseQiChongAttrListAndCap(qc_type, star_level)
    local attr_list = {}
    local capability = 0
    local cur_upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, star_level)
    local next_upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, star_level + 1)
    local no_max = next_upstar_cfg ~= nil
    if cur_upstar_cfg == nil then
        return attr_list, capability
    end

    local get_attr_list_func = function (cur_list, next_list)
        local list = {}
        local cur_no_data = IsEmptyTable(cur_list)
        local temp_list = cur_no_data and next_list or cur_list
        for k, v in pairs(temp_list) do
            local data = {}
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k, true)
            local cur_value = cur_no_data and 0 or v
            local next_value = next_list[k] or 0
            local is_per = EquipmentWGData.Instance:GetAttrIsPer(k)
            local per_desc = is_per and "%" or ""
            if next_value > 0 then
                next_value = next_value - cur_value
                next_value = is_per and string.format("%.2f", next_value / 100) or math.floor(next_value)
                data.add_value = tonumber(next_value) .. per_desc
            end

            cur_value = is_per and string.format("%.2f", cur_value / 100) or math.floor(cur_value)
            data.cur_value = tonumber(cur_value) .. per_desc

            data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
            table.insert(list, data)
        end

        table.sort(list, SortTools.KeyLowerSorter("attr_sort"))
        return list
    end

    local cur_list, next_list = {}, {}
    local get_add_attr_func = function (list, map_list, cfg, beishu)
        if not list or not map_list or not cfg then
            return
        end

        beishu = beishu or 1
        for k,v in pairs(map_list) do
            local value = cfg[k]
            
            if value > 0 then
                local is_per = EquipmentWGData.Instance:GetAttrIsPer(v)
                if not is_per then
                    value = value * beishu
                end
                list[v] = list[v] and list[v] + value or value
            end
        end
    end

    local sxd_list = self:GetQiChongSXDlist(qc_type)
    local add_per = 1
    if sxd_list then
        for k,v in ipairs(sxd_list) do
            if v.is_open and v.used_num > 0 and v.cfg and v.cfg.attr_per > 0 then
                add_per = add_per + (v.cfg.attr_per / 10000 * v.used_num)
            end
        end
    end

    local sxd_extend_list = self:GetQiChongSXDExtendlist(qc_type)
    if sxd_extend_list then
        for k,v in ipairs(sxd_extend_list) do
            if v.is_open and v.used_num > 0 and v.cfg and v.cfg.attr_per > 0 then
                add_per = add_per + (v.cfg.attr_per / 10000 * v.used_num)
            end
        end
    end

    --藏金百分比
    local tequan_info = YanYuGeWGData.Instance:GetShowTeQuanList()
    if not IsEmptyTable(tequan_info) then
        for k, v in ipairs(tequan_info) do
            if v.act_flag == 1 then
                add_per = add_per + (v.cfg.appe_base_add_per / 10000)
            end
        end
    end

    -- 骚策划技能也要加百分比技能百分比
    local skill_list = self:GetBaseQiChongSkillList(qc_type)
    for k,v in pairs(skill_list) do
        if v.is_act then
            local attr_add = v.cfg.attr_per
            if attr_add and attr_add > 0 then
                add_per = add_per + (attr_add / 10000)
            end
        end
    end

    -- 骚策划还说天道石也要加百分比
    -- 天道石影响属性百分比,不影响百分比
    local charm_type = self:TransformQiChongTypeToCharmType(qc_type)
    local add_rate = CultivationWGData.Instance:GetAddRateByType(charm_type)
    add_per = add_per + (add_rate / 10000)

    --王者特权万分比
    local king_data = RechargeWGData.Instance:GetCurKingVipAddition()
    local is_active = RechargeWGCtrl.Instance:GetKingVipIsActive()
	if not IsEmptyTable(king_data) and is_active then
        local king_vip_index = 0
        if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
            king_vip_index = 5
        elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
            king_vip_index = 4
        end

        local attr_add = king_data["add_" .. king_vip_index]
		add_per = add_per + (attr_add / 10000)
	end

    --VIP周卡万分比.
    local remain_time = RechargeWGData.Instance:GetWeekCardRemainTime()
    if remain_time > 0 then
        local addition_idx = 0
        if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
            addition_idx = VIP_WEEK_CARD_GRADE_ADDITION_TYPE.WEEK_CARD_GRADE_ADDITION_TYPE_MOUNT
        elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
            addition_idx = VIP_WEEK_CARD_GRADE_ADDITION_TYPE.WEEK_CARD_GRADE_ADDITION_TYPE_LINGCHONG
        end

        local attr_add = RechargeWGData.Instance:GetWeekCardAdditionByIndex(addition_idx)
		add_per = add_per + attr_add
    end

    -- 特权加成
    local right_is_act = self:GetQiChongRightIsAct(qc_type)
    local right_cfg = self:GetQiChongRightCfg(qc_type)
    if right_is_act and right_cfg then
        add_per = add_per + (right_cfg.attr_per / 10000)
    end

    local upstar_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_upstar_cfg)
    get_add_attr_func(cur_list, upstar_cfg_map_attr_list, cur_upstar_cfg, add_per)
    if no_max then
        get_add_attr_func(next_list, upstar_cfg_map_attr_list, next_upstar_cfg, add_per)
    end

    -- 技能战力
    local skill_list = self:GetBaseQiChongSkillList(qc_type)
    for k,v in pairs(skill_list) do
        if v.is_act then
            capability = capability + self:GetSingleSkillCap(v.cfg)
        end
    end

    attr_list = get_attr_list_func(cur_list, next_list)

    -- 特权加成属性显示
    if right_cfg then
        local right_add_per_temp = {}
        right_add_per_temp.attr_name = ToColorStr(Language.Advanced.RightAttrStr, COLOR3B.GLOD)
        right_add_per_temp.cur_value = right_cfg.attr_per / 100 .. "%"
        right_add_per_temp.is_per = false
        right_add_per_temp.is_lock = not right_is_act
        right_add_per_temp.show_effect = true
        right_add_per_temp.hide_arrow = true
        if not right_is_act then
            right_add_per_temp.add_value = Language.Advanced.GoToAct
        end

        local is_has_cost = false
        local has_num = ItemWGData.Instance:GetItemNumInBagById(right_cfg.cost_item_id)
        is_has_cost = has_num >= right_cfg.cost_item_num
        right_add_per_temp.is_remind = not right_is_act and is_has_cost
        if not right_is_act and right_cfg then
            right_add_per_temp.click_func = function ()
                if right_cfg then
                    local btn_callback_event = {}
                    if not right_is_act and is_has_cost then
                        btn_callback_event[1] = {btn_text = Language.Tip.ButtonLabel[2], callback = function()
                            if right_cfg then
                                local index = ItemWGData.Instance:GetItemIndex(right_cfg.cost_item_id)
                                BagWGCtrl.Instance:SendUseItem(index, right_cfg.cost_item_num, 0, 0)
                            end
                        end
                        }
                    end

                    TipWGCtrl.Instance:OpenItem({item_id = right_cfg.cost_item_id}, nil, nil, nil, btn_callback_event, true)
                end
            end
        end
        table.insert(attr_list, right_add_per_temp)
    end

    -- 属性丹基础属性不影响进阶属性显示
    if sxd_list then
        local sxd_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(sxd_list[1].cfg)
        for k,v in ipairs(sxd_list) do
            if v.is_open and v.used_num > 0 then
                get_add_attr_func(cur_list, sxd_cfg_map_attr_list, v.cfg, v.used_num)
            end
        end
    end

    --藏金商铺属性定制
    local cangjin_attr = YanYuGeWGData.Instance:GetAllChooseAttrList()
    if not IsEmptyTable(cangjin_attr) then
        local cangjin_attr_attr_list = AttributeMgr.GetUsefulAttributteByClass(cangjin_attr)
        get_add_attr_func(cur_list, cangjin_attr_attr_list, cangjin_attr, add_per)
    end

    local attribute = AttributePool.AllocAttribute()
    for k,v in pairs(cur_list) do
        if attribute[k] then
            attribute[k] = attribute[k] + v
        end
    end

    capability = capability + AttributeMgr.GetCapability(attribute)
    return attr_list, capability
end


-- 珍稀骑宠 属性列表 / 战力
function NewAppearanceWGData:GetSpecialQiChongAttrListAndCap(qc_type, func_type, image_id, defult_lv)
    local attr_list = {}
    local capability = 0
    local info = self:GetSpecialQiChongInfo(qc_type, image_id)
    if info == nil then
        return attr_list, capability
    end

    local act_cfg = self:GetSpecialQiChongActCfgByImageId(qc_type, image_id)
    if act_cfg == nil then
        return attr_list, capability
    end

    local grade = info.grade
    local show_lv = defult_lv or 0
    grade = grade > 0 and grade or show_lv
    local cfg_need_grade = grade > 1 and grade or 1
    local cur_grade_cfg = self:GetSpecialQiChongUpGradeCfg(qc_type, image_id, cfg_need_grade)
    local get_attr_list_func = function (cur_list, next_list)  
        local list = {}
        local cur_no_data = IsEmptyTable(cur_list)
        local temp_list = cur_no_data and next_list or cur_list
        for k, v in pairs(temp_list) do
            local data = {}
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k, true)
            local cur_value = cur_no_data and 0 or v
            local next_value = next_list[k] or 0
            local is_per = EquipmentWGData.Instance:GetAttrIsPer(k)
            local per_desc = is_per and "%" or ""
            next_value = next_value - cur_value
            if next_value > 0 then
                -- next_value = is_per and next_value / 100 or next_value
                -- data.add_value = next_value .. per_desc

                next_value = next_value
                data.add_value = next_value
            end
            -- cur_value = is_per and cur_value / 100 or cur_value
            -- data.cur_value = cur_value .. per_desc
            data.cur_value = cur_value
            data.attr_value = cur_value

            data.attr_str = k
            data.is_per = is_per
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
            table.insert(list, data)
        end

        table.sort(list, SortTools.KeyLowerSorter("attr_sort"))
        return list
    end

    local cur_list, next_list = {}, {}
    local get_add_attr_func = function (list, map_list, cfg, default_value)
        if not list or not map_list or not cfg then
            return
        end

        -- if not IsEmptyTable(list) then
        --     local list_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(list)

        --     for k, v in pairs(list_map_attr_list) do
        --         local value = cfg[k]
        --         if default_value then
        --             list[v] = list[v] and list[v] + default_value or default_value
        --         elseif value and value > 0 then
        --             list[v] = list[v] and list[v] + value or value
        --         end
        --     end
        -- end

        -- local cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cfg)

        -- if not IsEmptyTable(cfg_map_attr_list) then
        --     for k, v in pairs(cfg_map_attr_list) do
        --         if cfg[k] and cfg[k] > 0 then
        --             if not list[v] then
        --                 if default_value then
        --                     list[v] = default_value
        --                 else
        --                     list[v] = cfg[k]
        --                 end
        --             end
        --         end
        --     end
        -- end

        for k,v in pairs(map_list) do
            local value = cfg[k]
            if default_value then
                list[v] = list[v] and list[v] + default_value or default_value
            elseif value and value > 0 then
                list[v] = list[v] and list[v] + value or value
            end
        end
    end

    local map_attr_cfg = self:GetSpecialQiChongUpStarAttrCfg(qc_type, image_id, 1)
    local upstar_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(map_attr_cfg)
    -- 策划改了升级属性 补充
    local act_ttr_info = self:GetSpecialQiChongUpStarAttrCfg(qc_type, image_id, 1)
    for k, v in pairs(act_ttr_info) do
        if nil == upstar_cfg_map_attr_list[k] then
            upstar_cfg_map_attr_list[k] = k
        end
    end

    local upgrade_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_grade_cfg)
    local hualing_cfg = self:GetHuaLingCfg(qc_type, image_id)
    local hualing_level = 0
    if not IsEmptyTable(hualing_cfg) then
        hualing_level = self:GetHuaLingLevel(qc_type, hualing_cfg.seq)
    end

    local cur_hualing_cfg = self:GetHuaLingAttrCfg(qc_type, image_id, hualing_level)
    local hualing_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_hualing_cfg)
    local next_hualing_cfg = self:GetHuaLingAttrCfg(qc_type, image_id, hualing_level + 1)

    -- 未激活
    if grade <= 0 then
        if func_type == QICHONG_FUNC_TYPE.UPSTAR then
            get_add_attr_func(next_list, upstar_cfg_map_attr_list, map_attr_cfg)
        elseif func_type == QICHONG_FUNC_TYPE.UPLEVEL then
            if cur_grade_cfg == nil then
                get_add_attr_func(next_list, upstar_cfg_map_attr_list, map_attr_cfg, 0)
            else
                local next_grade_cfg = self:GetSpecialQiChongUpGradeCfg(qc_type, image_id, cfg_need_grade + 1)
                get_add_attr_func(next_list, upgrade_cfg_map_attr_list, next_grade_cfg)
            end
        elseif func_type == QICHONG_FUNC_TYPE.HUALING then
            get_add_attr_func(next_list, hualing_cfg_map_attr_list, next_hualing_cfg)
        end
        attr_list = get_attr_list_func(nil, next_list)
        return attr_list, capability
    end

    -- 激活后
    local star_level = info.star_level
    local is_act = true

    if star_level <= 0 then -- 默认拿1星属性
        star_level = 1
        is_act = false
    end

    local cur_star_cfg = self:GetSpecialQiChongUpStarAttrCfg(qc_type, image_id, star_level)

    get_add_attr_func(cur_list, upstar_cfg_map_attr_list, cur_star_cfg)
    -- get_add_attr_func(cur_list, upstar_cfg_map_attr_list, act_cfg)
    get_add_attr_func(cur_list, upgrade_cfg_map_attr_list, cur_grade_cfg)
    get_add_attr_func(cur_list, hualing_cfg_map_attr_list, cur_hualing_cfg)

    local info = self:GetQiChongInfo(qc_type) or {}
    local active_map_list = {}
    if not IsEmptyTable(info.special_image_list) then
        for key, value in pairs(info.special_image_list) do
            if value.star_level > 0 then
                active_map_list[key] = value.star_level
            end
        end
    end

    local total_attr_add_per = 0
    if not IsEmptyTable(active_map_list) then
        local total_add_per = 0
        for key, value in pairs(active_map_list) do
            for i = 1, star_level do
                total_add_per = self:GetSpecialQiChongAttrAddPer(qc_type, key, i) or 0
                if total_add_per == 0 then break end
                if total_add_per > 0 then
                    total_attr_add_per = total_attr_add_per + total_add_per
                end
            end
        end
    end

    if func_type == QICHONG_FUNC_TYPE.UPSTAR then
        local next_star_cfg = self:GetSpecialQiChongUpStarAttrCfg(qc_type, image_id, star_level + 1)
        get_add_attr_func(next_list, upgrade_cfg_map_attr_list, cur_grade_cfg)
        get_add_attr_func(next_list, hualing_cfg_map_attr_list, cur_hualing_cfg)
        get_add_attr_func(next_list, upstar_cfg_map_attr_list, next_star_cfg)
    elseif func_type == QICHONG_FUNC_TYPE.UPLEVEL then
        local next_grade_cfg = self:GetSpecialQiChongUpGradeCfg(qc_type, image_id, cfg_need_grade + 1)
        get_add_attr_func(next_list, upstar_cfg_map_attr_list, cur_star_cfg)
        get_add_attr_func(next_list, upgrade_cfg_map_attr_list, next_grade_cfg)
        get_add_attr_func(next_list, hualing_cfg_map_attr_list, cur_hualing_cfg)
    elseif func_type == QICHONG_FUNC_TYPE.HUALING then
        if not IsEmptyTable(next_hualing_cfg) then
            get_add_attr_func(next_list, upstar_cfg_map_attr_list, cur_star_cfg)
            get_add_attr_func(next_list, upgrade_cfg_map_attr_list, cur_grade_cfg)
            get_add_attr_func(next_list, hualing_cfg_map_attr_list, next_hualing_cfg)
        end
    end

    local attribute = AttributePool.AllocAttribute()
    for k,v in pairs(cur_list) do
        if attribute[k] then
            attribute[k] = attribute[k] + v
        end
    end

    -- 进阶属性加成
    if total_attr_add_per > 0 then
        local attr_add_per = total_attr_add_per / 10000
        local base_info = self:GetQiChongInfo(qc_type)
        if base_info then
            local base_star_level = base_info.star_level
            local cur_base_upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, base_star_level)
            if cur_base_upstar_cfg then
                local base_upstar_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_base_upstar_cfg)
                for k,v in pairs(base_upstar_cfg_map_attr_list) do
                    local value = cur_base_upstar_cfg[k]
                    if value > 0 and attribute[v] then
                        value = value * attr_add_per
                        attribute[v] = attribute[v] + value
                    end
                end
            end
        end
    end

    -- 技能战力
    local skill_list =  self:GetSpecialQiChongSkillList(qc_type, image_id)
    for k,v in pairs(skill_list) do
        local is_act = self:GetSpecialQiChongSkillIsAct(qc_type, image_id, v.skill_id)
        if is_act then
            capability = capability + self:GetSingleSkillCap(v)
        end
    end

    attr_list = get_attr_list_func(cur_list, next_list)
    capability = capability + AttributeMgr.GetCapability(attribute)

    -- 增加属性展示
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT and (not is_act) then
        local t_attr_add_perf = NewAppearanceWGData.Instance:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.MOUNT, image_id, star_level)
        local attr_add_per = t_attr_add_perf or 0 --cfg and cfg.attr_add_perf

        if attr_add_per and attr_add_per > 0 then
            local temp = {}
            local name_str = Language.Tip.MountLingChongAttrName[MOUNT_LINGCHONG_APPE_TYPE.MOUNT]
            temp.attr_name = ToColorStr(name_str, COLOR3B.GLOD)
            temp.cur_value = attr_add_per
            temp.attr_value = attr_add_per
            temp.is_per = true
            table.insert(attr_list, temp)
        end
    end

    return attr_list, capability
end

-- 技能战力
function NewAppearanceWGData:GetSingleSkillCap(cfg)
	local cap = 0
	if IsEmptyTable(cfg) then
		return cap
	end

	local attr_list = {}
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end

		if attr_list[attr_str] then
			attr_list[attr_str] = attr_list[attr_str] + value
		else
			attr_list[attr_str] = value
		end
	end

    local attribute = AttributePool.AllocAttribute()
	for i = 1, 10 do
		local attr_value = cfg["param" .. i] or 0
		local attr_str = cfg["attr_type" .. i] or ""
		if attr_value > 0 and attr_str ~= "" then
			attr_str = AttributeMgr.GetAttributteKey(attr_str)
            if attribute[attr_str] then
			    add_tab(attr_str, attr_value)
            end
		end
	end

	-- 兼容多种傻逼配置
    local cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cfg)
    for k,v in pairs(cfg_map_attr_list) do
        local value = cfg[k]
        if value ~= "" and value > 0 then
            add_tab(v, value)
        end
    end

    for k,v in pairs(attr_list) do
        if attribute[k] then
            attribute[k] = attribute[k] + v
        end
    end
    
	local skill_inc = {
		attack_power = cfg.attack_power,
		defence_power = cfg.defence_power,
		capability_inc = cfg.capability_inc,
	}

	cap = AttributeMgr.GetCapability(attribute, skill_inc)

	return cap
end

-- 鲲 属性列表 / 战力
function NewAppearanceWGData:GetKunAttrListAndCap(func_type, image_id, defult_lv)
    local attr_list = {}
    local capability = 0
    local info = self:GetKunInfo(image_id)
    if info == nil then
        return attr_list, capability
    end

    local grade = info.level
    local defult_lv = defult_lv or 0
    local cfg_need_grade = grade > 0 and grade or defult_lv
    local cur_grade_cfg = self:GetKunUpGradeCfg(image_id, cfg_need_grade)
    local next_grade_cfg = self:GetKunUpGradeCfg(image_id, cfg_need_grade + 1)
    if cur_grade_cfg == nil then
        return attr_list, capability
    end

    -- 将cfg属性加到list中
    local get_add_attr_func = function (list, map_list, cfg)
        if not list or not map_list or IsEmptyTable(cfg) then
            return
        end

        for k,v in pairs(map_list) do
            local value = cfg[k]
            
            if value and value > 0 then
                list[v] = list[v] and list[v] + value or value
            end
        end
    end

    local get_sub_attr_func = function (list, map_list, cfg1, cfg2)
        if not list or not map_list or not cfg1 or not cfg2 then
            return
        end

        local value1, value2, offset_value = 0, 0, 0
        for k,v in pairs(map_list) do
            value1 = cfg1[k] or 0
            value2 = cfg2[k] or 0
            offset_value = value2 - value1
            if offset_value > 0 then
                list[v] = list[v] and list[v] + offset_value or offset_value
            end
        end
    end

    local get_attr_list_func = function (cur_list, next_list)
        local list = {}
        local cur_no_data = IsEmptyTable(cur_list)
        local temp_list = cur_no_data and next_list or cur_list
        for k, v in pairs(temp_list) do
            local data = {}
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k, true)
            data.attr_str = k
            local cur_value = cur_no_data and 0 or v
            local next_value = next_list[k] or 0
            local is_per = EquipmentWGData.Instance:GetAttrIsPer(k)
            local per_desc = is_per and "%" or ""
            next_value = next_value - cur_value
            if next_value > 0 then
                next_value = math.floor(next_value)
                data.add_value = next_value 
            end
            data.is_per = is_per
            data.cur_value =  math.floor(cur_value) 
            data.attr_value =  math.floor(cur_value) 
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
            table.insert(list, data)
        end
        table.sort(list, SortTools.KeyLowerSorter("attr_sort"))
        return list
    end

    local cur_list, next_list = {}, {}
    local star_level = info.star

    local cur_star_cfg = self:GetKunUpStarAttrCfg(image_id, star_level)

    local upstar_cfg_map_attr_cfg = star_level > 0 and cur_star_cfg or self:GetKunUpStarAttrCfg(image_id, 1)
    local upstar_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(upstar_cfg_map_attr_cfg)
    local upgrade_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_grade_cfg)

    local hualing_cfg = self:GetHuaLingCfg(MOUNT_LINGCHONG_APPE_TYPE.KUN, image_id)
    local hualing_level = 0
    if not IsEmptyTable(hualing_cfg) then
        hualing_level = self:GetHuaLingLevel(MOUNT_LINGCHONG_APPE_TYPE.KUN, hualing_cfg.seq)
    end
    local cur_hualing_cfg = self:GetHuaLingAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.KUN, image_id, hualing_level)
    local hualing_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_hualing_cfg)
    local next_hualing_cfg = self:GetHuaLingAttrCfg(MOUNT_LINGCHONG_APPE_TYPE.KUN, image_id, hualing_level + 1)

    local next_star_cfg = self:GetKunUpStarAttrCfg(image_id, star_level + 1)


    if not info.is_act then
        if func_type == QICHONG_FUNC_TYPE.UPSTAR then
            get_add_attr_func(next_list, upstar_cfg_map_attr_list, next_star_cfg)
        elseif func_type == QICHONG_FUNC_TYPE.UPLEVEL then
            get_sub_attr_func(next_list, upgrade_cfg_map_attr_list, cur_grade_cfg, next_grade_cfg)
        elseif func_type == QICHONG_FUNC_TYPE.HUALING then
            get_add_attr_func(next_list, hualing_cfg_map_attr_list, next_hualing_cfg)
        end

        attr_list = get_attr_list_func(cur_list, next_list)
        return attr_list, capability
    end

    -- 激活后
    get_add_attr_func(cur_list, upstar_cfg_map_attr_list, cur_star_cfg)
    get_add_attr_func(cur_list, upgrade_cfg_map_attr_list, cur_grade_cfg)
    get_add_attr_func(cur_list, hualing_cfg_map_attr_list, cur_hualing_cfg)

    if func_type == QICHONG_FUNC_TYPE.UPSTAR then
        get_add_attr_func(next_list, upstar_cfg_map_attr_list, next_star_cfg)
        get_add_attr_func(next_list, upgrade_cfg_map_attr_list, cur_grade_cfg)
        get_add_attr_func(next_list, hualing_cfg_map_attr_list, cur_hualing_cfg)
    elseif func_type == QICHONG_FUNC_TYPE.UPLEVEL then
        get_add_attr_func(next_list, upstar_cfg_map_attr_list, cur_star_cfg)
        get_add_attr_func(next_list, upgrade_cfg_map_attr_list, next_grade_cfg)
        get_add_attr_func(next_list, hualing_cfg_map_attr_list, cur_hualing_cfg)
    elseif func_type == QICHONG_FUNC_TYPE.HUALING then
        get_add_attr_func(next_list, upgrade_cfg_map_attr_list, cur_grade_cfg)
        get_add_attr_func(next_list, upstar_cfg_map_attr_list, cur_star_cfg)
        
        if not IsEmptyTable(next_hualing_cfg) then
            get_add_attr_func(next_list, hualing_cfg_map_attr_list, next_hualing_cfg)
        end
    end

    local attribute = AttributePool.AllocAttribute()
    for k,v in pairs(cur_list) do
        if attribute[k] then
            attribute[k] = attribute[k] + v
        end
    end

     -- 技能战力
    local skill_list = self:GetKunSkillList(image_id)
    for k,v in pairs(skill_list) do
        if grade >= v.active_grade then
            capability = capability + self:GetKunSkillAttrCap(v)
        end
    end

    local info = self:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.KUN) or {}
    local active_map_list = {}
    if not IsEmptyTable(info) then
        for key, value in pairs(info) do
            if value.is_act then
                active_map_list[key] = value.star
            end
        end
    end

    local total_attr_add_per = 0
    if not IsEmptyTable(active_map_list) then
        local total_add_per = 0
        for key, value in pairs(active_map_list) do
            for i = 1, star_level do
                total_add_per = self:GetSpecialQiChongAttrAddPer(MOUNT_LINGCHONG_APPE_TYPE.KUN, key, i) or 0
                if total_add_per == 0 then break end
                if total_add_per > 0 then
                    total_attr_add_per = total_attr_add_per + total_add_per
                end
            end
        end
    end
 
    -- 进阶属性加成
    if total_attr_add_per > 0 then
        local attr_add_per = total_attr_add_per / 10000
        local base_info = self:GetQiChongInfo(MOUNT_LINGCHONG_APPE_TYPE.KUN)
        if base_info then
            local cur_base_upstar_cfg = self:GetKunUpStarAttrCfg(image_id, star_level)
            if cur_base_upstar_cfg then
                local base_upstar_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_base_upstar_cfg)
                for k,v in pairs(base_upstar_cfg_map_attr_list) do
                    local value = cur_base_upstar_cfg[k]
                    if value > 0 and attribute[v] then
                        value = value * attr_add_per
                        attribute[v] = attribute[v] + value
                    end
                end
            end
        end
    end

    attr_list = get_attr_list_func(cur_list, next_list)
    capability = capability + AttributeMgr.GetCapability(attribute)
    return attr_list, capability
end

-- 鲲 单个技能战力
function NewAppearanceWGData:GetKunSkillAttrCap(cfg)
	local cap = 0
	if IsEmptyTable(cfg) then
		return cap
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end

		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

	for i = 1, 4 do
		local attr_id = cfg["attr_type" .. i] or 0
		local attr_value = cfg["attr_val" .. i] or 0
		if attr_id > 0 and attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			add_tab(attr_str, attr_value)
		end
	end

	-- 属性万分比 影响所有鲲的升级属性(除百分比属性)
	if cfg.attr_per and cfg.attr_per > 0 then
        local map_upgrade_cfg = self:GetKunUpGradeCfg(1, 0)
        local upgrade_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(map_upgrade_cfg)
		local attr_per = cfg.attr_per / 10000
 		local kun_list = self:GetQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.KUN)
        for k, v in pairs(kun_list) do
            local info = self:GetKunInfo(v.image_id)
            if info then
                local kun_cfg = self:GetKunUpGradeCfg(v.image_id, info.level)
                if info.is_act and kun_cfg then
                    for map_k, map_v in pairs(upgrade_cfg_map_attr_list) do
                        local value = kun_cfg[map_k]
                        if value > 0 then
                            local is_per = EquipmentWGData.Instance:GetAttrIsPer(map_v)
                            if not is_per then
                                add_tab(map_v, value * attr_per)
                            end
                        end
                    end
                end
            end
        end
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end
--====================  属性 战力   ==========================
--===========================================================



--===========================================================
--====================  红点   ==============================
-- 基础骑宠 升星红点
function NewAppearanceWGData:GetBaseQiChongUpStarRemind(qc_type)
    local info = self:GetQiChongInfo(qc_type)
    if info == nil or info.star_level == nil then
        return false
    end

    local next_upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, info.star_level + 1)
    if not next_upstar_cfg then
        return false
    end

    local cur_upstar_cfg = self:GetQiChongBaseUpStarCfg(qc_type, info.star_level)
    if not cur_upstar_cfg then
        return false
    end

    local upstar_bless_val = info.upstar_bless_val
    local need_bless = cur_upstar_cfg.need_bless - upstar_bless_val
    local stuff_list = self:GetQiChongBaseUpStarStuff(qc_type)
    local add_bless_sum = 0
    for k, v in pairs(stuff_list) do
        local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
        add_bless_sum = add_bless_sum + (item_num * v.add_bless)
        if add_bless_sum >= need_bless then
            return true
        end
    end

    return false
end

-- 基础骑宠进阶总红点
function NewAppearanceWGData:GetBaseQiChongAllRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    -- 判断功能开放
    -- local fun_name = tab_data.fun_name or FunName.NewAppearanceWGView
    -- if not FunOpen.Instance:GetFunIsOpened(fun_name) then
    --     return 0
    -- end

    local tip_type = tab_data.strengthen_type
    local qc_type = tab_data.qc_type
    local is_remind = self:GetBaseQiChongUpStarRemind(qc_type)
    if not is_remind then
        local sxd_list = self:GetQiChongSXDlist(qc_type)
        for k,v in ipairs(sxd_list) do
            if v.is_remind then
                is_remind = true
                break
            end
        end
    end

    if not is_remind then
        local skill_list = self:GetBaseQiChongSkillList(qc_type)
        for k,v in ipairs(skill_list) do
            if v.is_remind then
                is_remind = true
                break
            end
        end
    end

    if not is_remind then
        if AppearanceActIndexList[tab_index] then
            local rush_type = AppearanceActIndexList[tab_index]
            local is_act_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(rush_type)
            local show_red_point = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(rush_type)
            if show_red_point and is_act_open then
                is_remind = true
            end
        end
    end

    if not is_remind then
        if self:GetQiChongSXDRemind(qc_type) then
            is_remind = true
        end
    end

    if not is_remind and qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
        local already_choose_attr = YanYuGeWGData.Instance:IsAlreadyChooseAttr()
        local is_can_choose_attr =  YanYuGeWGData.Instance:GetIsActChooseAttrTeQuan()
        if is_can_choose_attr and (not already_choose_attr) then
            is_remind = true
        end
    end

    if not is_remind then
        if self:GetQiChongRightRemind(qc_type) then
            is_remind = true
        end
    end
    
    if is_remind then
        MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
            return true
        end)
        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    return 0
end

-- 特权红点
function NewAppearanceWGData:GetQiChongRightRemind(qc_type)
    local right_is_act = self:GetQiChongRightIsAct(qc_type)
    if right_is_act then
        return false
    end

    local right_cfg = self:GetQiChongRightCfg(qc_type)
    if right_cfg then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(right_cfg.cost_item_id)
        return has_num >= right_cfg.cost_item_num
    end

    return false
end


-- 珍稀骑宠 单个升星红点
function NewAppearanceWGData:GetSpecialQiChongSingleUpStarRemind(qc_type, image_id)
    local grade = self:GetSpecialQiChongGrade(qc_type, image_id)
    local act_cfg = self:GetSpecialQiChongActCfgByImageId(qc_type, image_id)
    if not act_cfg then
        return false
    end

    local need_item_id, need_num = 0, 1
    if grade <= 0 or act_cfg.timed_type == 2 then
        need_item_id = act_cfg.active_item_id
    else
        local star_level = self:GetSpecialQiChongStarLevel(qc_type, image_id)
        local max_level = self:GetSpecialQiChongMaxStarLevel(qc_type, image_id)

        if star_level >= max_level then
            return false
        end

        local up_star_cfg = self:GetSpecialQiChongUpStarCfg(qc_type, image_id)
        need_item_id = up_star_cfg.need_item

        if star_level > 0 then
            need_num = self:GetSpecialQiChongUpStarCostNum(qc_type, image_id, star_level)
        end
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
    return num >= need_num
end

-- 珍稀骑宠 升星红点
function NewAppearanceWGData:GetSpecialQiChongUpStarRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    local tip_type = tab_data.strengthen_type
    local qc_type = tab_data.qc_type
    local show_list = self:GetQiChongSpecialShowList(qc_type)
    for k,v in ipairs(show_list) do
        if self:GetSpecialQiChongSingleUpStarRemind(qc_type, v.image_id) then
            MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
                FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
                return true
            end)

            return 1
        end
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    return 0
end

-- 珍稀骑宠 单个升级（升阶）红点
function NewAppearanceWGData:GetSpecialQiChongSingleUpLevelRemind(qc_type, image_id)
    local grade = self:GetSpecialQiChongGrade(qc_type, image_id)
    if grade <= 0 then
        return false
    end

    local next_grade_cfg = self:GetSpecialQiChongUpGradeCfg(qc_type, image_id, grade + 1)
    if not next_grade_cfg then
        return false
    end

    local cur_grade_cfg = self:GetSpecialQiChongUpGradeCfg(qc_type, image_id, grade)
    if cur_grade_cfg then
        local had_exp = self:GetSpecialQiChongGradeExp(qc_type, image_id)
        local need_exp = cur_grade_cfg.upgrade_need_exp - had_exp
        local stuff_list = self:GetSpecialQiChongUpGradeStuff(qc_type)
        local add_bless_sum = 0
        for k, v in pairs(stuff_list) do
            local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
            add_bless_sum = add_bless_sum + (item_num * v.add_exp)
            if add_bless_sum >= need_exp then
                return true
            end
        end
    end
    
    return false
end

-- 珍稀骑宠 升阶红点
function NewAppearanceWGData:GetSpecialQiChongUpGradeRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    local tip_type = tab_data.strengthen_type
    local qc_type = tab_data.qc_type
    local show_list = self:GetQiChongSpecialShowList(qc_type)
    for k,v in ipairs(show_list) do
        if self:GetSpecialQiChongSingleUpLevelRemind(qc_type, v.image_id) then
            MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
                FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
                return true
            end)
            return 1
        end
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    return 0
end

-- 鲲 单个升星红点
function NewAppearanceWGData:GetKunSingleUpStarRemind(image_id)
    local info = self:GetKunInfo(image_id)
    if not info then
        return false
    end

    local need_item_id, need_num = 0, 1
    if not info.is_act then
        local cfg = self:GetKunActCfgById(image_id)
        if not cfg then
            return false
        end

        need_item_id = cfg.active_need_item_id
    else
        local star_level = info.star
        local kun_max_star = self:GetKunMaxStar(image_id)

        if star_level >= kun_max_star then
            return false
        end

        local cfg = self:GetKunUpStarCfg(image_id)
        need_item_id = cfg.item_id

        if star_level > 0 then
            need_num = self:GetSpecialQiChongUpStarCostNum(MOUNT_LINGCHONG_APPE_TYPE.KUN, cfg.id, star_level)
        end
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
    return num >= need_num
end

-- 珍稀骑宠 单个 升星/进阶 红点
function NewAppearanceWGData:GetSpecialQiChongSingleRmindByType(func_type, qc_type, image_id)
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        if func_type == QICHONG_FUNC_TYPE.UPSTAR then
            return self:GetKunSingleUpStarRemind(image_id)
        elseif func_type == QICHONG_FUNC_TYPE.UPLEVEL then
            return self:GetKunSingleUpLevelRemind(image_id)
        elseif func_type == QICHONG_FUNC_TYPE.HUALING then
            return self:GetSingleHuaLingRemind(MOUNT_LINGCHONG_APPE_TYPE.KUN, image_id)
        end
    else
        if func_type == QICHONG_FUNC_TYPE.UPSTAR then
            return self:GetSpecialQiChongSingleUpStarRemind(qc_type, image_id)
        elseif func_type == QICHONG_FUNC_TYPE.UPLEVEL then
            return self:GetSpecialQiChongSingleUpLevelRemind(qc_type, image_id)
        elseif func_type == QICHONG_FUNC_TYPE.HUALING then
            return self:GetSingleHuaLingRemind(qc_type, image_id)
        end
    end

    return false
end

-- 鲲 升星红点
function NewAppearanceWGData:GetKunUpStarRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    local is_remind = false
    local tip_type = tab_data.strengthen_type
    local show_list = self:GetQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.KUN)
    for k,v in ipairs(show_list) do
        if self:GetKunSingleUpStarRemind(v.image_id) then
            is_remind = true
            break
        end
    end

    if not is_remind then
        if AppearanceActIndexList[tab_index] then
            local rush_type = AppearanceActIndexList[tab_index]
            local is_act_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(rush_type)
            local show_red_point = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(rush_type)
            if show_red_point and is_act_open then
                is_remind = true
            end
        end
    end

    if is_remind then
        MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
            return true
        end)
        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    return 0
end

-- 鲲 单个升级（升阶）红点
function NewAppearanceWGData:GetKunSingleUpLevelRemind(image_id)
    local info = self:GetKunInfo(image_id)
    if not info then
        return false
    end

    if not info.is_act then
        return false
    end

    local level = info.level
    local cfg = self:GetKunUpGradeCfg(image_id, level)
    local next_cfg = self:GetKunUpGradeCfg(image_id, level + 1)
    if not cfg or not next_cfg then
        return false
    end

    local had_num = self:GetKunUpGradeExp()
    
    return had_num >= cfg.need_num
end

-- 鲲 升阶红点
function NewAppearanceWGData:GetKunUpGradeRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    local tip_type = tab_data.strengthen_type
    local show_list = self:GetQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.KUN)
    for k,v in ipairs(show_list) do
        if self:GetKunSingleUpLevelRemind(v.image_id) then
            MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
                FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
                return true
            end)

            return 1
        end
    end

    if self:GetKunResolveRemind() then
        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    return 0
end


-- 鲲 - 分解红点
function NewAppearanceWGData:GetKunResolveRemind()
	local bag_stuff_list = ItemWGData.Instance:GetStuffStorgeItemData()
	for k,v in pairs(bag_stuff_list) do
		if self:GetKunStuffCfgByItemId(v.item_id) then
			return true
		end
	end

    return false
end
--====================  红点 end ============================
--===========================================================


--===========================================================
--====================  提醒优化 ============================
-- 珍稀坐骑材料
function NewAppearanceWGData:GetBaseMountStuffRemindList()
    self.base_mount_stuff_remind_list = {}
    -- 升星消耗
    local all_cfg = self:GetQiChongAllCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
    for k,v in pairs(all_cfg.upstar_item) do
        local item_id = v.item_id
        if not self.base_mount_stuff_remind_list[item_id] then
            self.base_mount_stuff_remind_list[item_id] = {}
        end
        
        self.base_mount_stuff_remind_list[item_id][RemindName.NewAppearance_Upgrade_Mount] = true
    end

    -- 属性丹
    for k,v in pairs(self.mount_sxd_map_itemid_cfg) do
        if not self.base_mount_stuff_remind_list[k] then
            self.base_mount_stuff_remind_list[k] = {}
        end
        
        self.base_mount_stuff_remind_list[k][RemindName.NewAppearance_Upgrade_Mount] = true
    end

    -- 技能升级
    for k,v in pairs(all_cfg.skill) do
        local item_id = v.uplevel_item_id
        if not self.base_mount_stuff_remind_list[item_id] then
            self.base_mount_stuff_remind_list[item_id] = {}
        end
        
        self.base_mount_stuff_remind_list[item_id][RemindName.NewAppearance_Upgrade_Mount] = true
    end

    -- 属性丹扩展
    for k,v in pairs(self:GetPetStoreCfg()) do
        if v.is_show then
            if not self.base_mount_stuff_remind_list[v.shuxingdan_id] then
                self.base_mount_stuff_remind_list[v.shuxingdan_id] = {}
            end

            self.base_mount_stuff_remind_list[v.shuxingdan_id][RemindName.NewAppearance_Upgrade_Mount] = true
        end
    end

    -- 特权
    local right_cfg = self:GetQiChongRightCfg(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
    if right_cfg then
        if not self.base_mount_stuff_remind_list[right_cfg.cost_item_id] then
            self.base_mount_stuff_remind_list[right_cfg.cost_item_id] = {}
        end
        
        self.base_mount_stuff_remind_list[right_cfg.cost_item_id][RemindName.NewAppearance_Upgrade_Mount] = true
    end
end

function NewAppearanceWGData:GetBaseLingChongStuffRemindList()
    self.base_lingchong_stuff_remind_list = {}
    -- 升星消耗
    local all_cfg = self:GetQiChongAllCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
    for k,v in pairs(all_cfg.upstar_item) do
        local item_id = v.item_id
        if not self.base_lingchong_stuff_remind_list[item_id] then
            self.base_lingchong_stuff_remind_list[item_id] = {}
        end

        self.base_lingchong_stuff_remind_list[item_id][RemindName.NewAppearance_Upgrade_LingChong] = true
    end

    -- 属性丹
    for k,v in pairs(self.lingchong_sxd_map_itemid_cfg) do
        if not self.base_lingchong_stuff_remind_list[k] then
            self.base_lingchong_stuff_remind_list[k] = {}
        end

        self.base_lingchong_stuff_remind_list[k][RemindName.NewAppearance_Upgrade_LingChong] = true
    end

    -- 技能升级
    for k,v in pairs(all_cfg.skill) do
        local item_id = v.item_id
        if not self.base_lingchong_stuff_remind_list[item_id] then
            self.base_lingchong_stuff_remind_list[item_id] = {}
        end

        self.base_lingchong_stuff_remind_list[item_id][RemindName.NewAppearance_Upgrade_LingChong] = true
    end

    -- 属性丹扩展
    for k,v in pairs(self:GetMountStoreCfg()) do
        if v.is_show then
            if not self.base_lingchong_stuff_remind_list[v.shuxingdan_id] then
                self.base_lingchong_stuff_remind_list[v.shuxingdan_id] = {}
            end

            self.base_lingchong_stuff_remind_list[v.shuxingdan_id][RemindName.NewAppearance_Upgrade_LingChong] = true 
        end
    end

    -- 特权
    local right_cfg = self:GetQiChongRightCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
    if right_cfg then
        if not self.base_lingchong_stuff_remind_list[right_cfg.cost_item_id] then
            self.base_lingchong_stuff_remind_list[right_cfg.cost_item_id] = {}
        end
        
        self.base_lingchong_stuff_remind_list[right_cfg.cost_item_id][RemindName.NewAppearance_Upgrade_LingChong] = true
    end
end

-- 珍稀坐骑材料
function NewAppearanceWGData:GetSpecialMountStuffRemindList()
    self.special_mount_stuff_remind_list = {}
    self.mount_show_level_list = {}
    -- 珍稀激活
    for k,v in pairs(self.mount_act_map_itemid_cfg) do
        if not self.special_mount_stuff_remind_list[k] then
            self.special_mount_stuff_remind_list[k] = {}
        end
        
        self.special_mount_stuff_remind_list[k][RemindName.NewAppearance_Mount_Upstar] = true
        self.mount_show_level_list[v.level_show] = true
    end

    -- 珍稀进阶
    for k,v in pairs(self.mount_special_upgrade_item_cfg) do
        if not self.special_mount_stuff_remind_list[k] then
            self.special_mount_stuff_remind_list[k] = {}
        end
        
        self.special_mount_stuff_remind_list[k][RemindName.NewAppearance_Mount_Upgrade] = true
    end
end

-- 珍稀灵宠材料
function NewAppearanceWGData:GetSpecialLingChongStuffRemindList()
    self.special_lingchong_stuff_remind_list = {}
    self.lingchong_show_level_list = {}
    -- 珍稀激活
    for k,v in pairs(self.lingchong_act_map_itemid_cfg) do
        if not self.special_lingchong_stuff_remind_list[k] then
            self.special_lingchong_stuff_remind_list[k] = {}
        end
        
        self.special_lingchong_stuff_remind_list[k][RemindName.NewAppearance_LingChong_Upstar] = true
        self.lingchong_show_level_list[v.level_show] = true
    end

    -- 珍稀进阶
    for k,v in pairs(self.lingchong_special_upgrade_item_cfg) do
        if not self.special_lingchong_stuff_remind_list[k] then
            self.special_lingchong_stuff_remind_list[k] = {}
        end
        
        self.special_lingchong_stuff_remind_list[k][RemindName.NewAppearance_LingChong_Upgrade] = true
    end
end

-- 鲲材料
function NewAppearanceWGData:GetKunStuffRemindList()
    self.kun_stuff_remind_list = {}
    self.kun_show_level_list = {}

    -- 激活
    for k,v in pairs(self.kun_act_map_itemid_cfg) do
        if not self.kun_stuff_remind_list[k] then
            self.kun_stuff_remind_list[k] = {}
        end
        
        self.kun_stuff_remind_list[k][RemindName.NewAppearance_Kun_Upstar] = true
        self.kun_show_level_list[v.level_show] = true
    end
    
    -- 分解材料
    for k,v in pairs(self.kun_resolve_map_itemid_cfg) do
        if not self.kun_stuff_remind_list[k] then
            self.kun_stuff_remind_list[k] = {}
        end
        
        self.kun_stuff_remind_list[k][RemindName.NewAppearance_Kun_Upgrade] = true
    end
end

-- 是否骑宠材料
function NewAppearanceWGData:CheckQiChongStuffRemind(item_id)
    local flush_view = false
    local fire_remind_list = function (remind_list)
        if remind_list then
            for k,v in pairs(remind_list) do
                RemindManager.Instance:Fire(k)
            end
    
            flush_view = true
        end
    end

    -- 基础坐骑
    fire_remind_list(self.base_mount_stuff_remind_list[item_id])
    -- 基础灵宠
    fire_remind_list(self.base_lingchong_stuff_remind_list[item_id])
    -- 珍稀坐骑
    fire_remind_list(self.special_mount_stuff_remind_list[item_id])
    -- 珍稀灵宠
    fire_remind_list(self.special_lingchong_stuff_remind_list[item_id])
    -- 鲲
    fire_remind_list(self.kun_stuff_remind_list[item_id])

    return flush_view
end

-- 等级改变 - 珍稀坐骑
function NewAppearanceWGData:CheckMountLevelIsFireRemind(old_level, new_level)
    old_level = tonumber(old_level) or 0
    new_level = tonumber(new_level) or 0
    for k,v in pairs(self.mount_show_level_list) do
        if k >= old_level and k <= new_level then
            return true
        end
    end

    return false
end

-- 等级改变 - 珍稀骑宠
function NewAppearanceWGData:CheckLingChongLevelIsFireRemind(old_level, new_level)
    old_level = old_level or 0
    new_level = new_level or 0
    for k,v in pairs(self.lingchong_show_level_list) do
        if k >= old_level and k <= new_level then
            return true
        end
    end

    return false
end

-- 等级改变 - 鲲
function NewAppearanceWGData:CheckKunLevelIsFireRemind(old_level, new_level)
    old_level = old_level or 0
    new_level = new_level or 0
    for k,v in pairs(self.kun_show_level_list) do
        if k >= old_level and k <= new_level then
            return true
        end
    end

    return false
end

-- 珍稀骑宠 物品快速使用
function NewAppearanceWGData:CheckQiChongCanQuickUes(item_id, index)
    local qc_type = self:GetQiChongTypeByItemId(item_id)
    if qc_type < 0 then
        return false
    end

    local cfg = self:GetSpecialQiChongActCfgByItemId(qc_type, item_id)
    if cfg == nil then
        return false
    end

    if cfg.timed_type == 2 then
        return true
    end

    local image_id = cfg.image_id or cfg.id
	local star_level = self:GetSpecialQiChongStarLevel(qc_type, image_id)
    local max_level = self:GetSpecialQiChongMaxStarLevel(qc_type, image_id)

    if star_level < max_level then
        if not self.need_check_qichong_quick_tab[qc_type] then
            self.need_check_qichong_quick_tab[qc_type] = {}
        end

        if qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
            if not self.had_mount_protocol_init then
                self.need_check_qichong_quick_tab[qc_type][item_id] = index
                return false
            end
        elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
            if not self.had_lingchong_protocol_init then
                self.need_check_qichong_quick_tab[qc_type][item_id] = index
                return false
            end
        elseif qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
            if not self.had_kun_protocol_init then
                self.need_check_qichong_quick_tab[qc_type][item_id] = index
                return false
            end
        end

        local up_star_cfg = self:GetSpecialQiChongUpStarCfg(qc_type, image_id)
        local act_item_id = up_star_cfg.need_item or up_star_cfg.item_id
		local need_num = self:GetSpecialQiChongUpStarCostNum(qc_type, image_id, star_level)
        
        local my_item_num = ItemWGData.Instance:GetItemNumInBagById(act_item_id)
        return my_item_num >= need_num
    end
	
	return false
end
--====================  提醒优化 end =========================
--===========================================================




-- 珍稀骑宠 物品tips技能
function NewAppearanceWGData:GetQiChongSkillData(data, qc_type)
    local data_list = {}
    if IsEmptyTable(data) then
        return data_list
    end
    
    local act_cfg = self:GetSpecialQiChongActCfgByItemId(qc_type, data.item_id)
	if not act_cfg then
        return data_list
    end

    local image_id = act_cfg.image_id
    local skill_list = self:GetSpecialQiChongSkillList(qc_type, image_id)
    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not item_cfg then
        return data_list
    end

    data_list.data_list_1 = {}
    data_list.data_list_2 = {}
    data_list.data_list_3 = {}
    local title_img_list = {"a3_sl_bq_zs", "a3_sl_bq_bd"}
    if skill_list[0] then
        data_list.tab_title_name_2 = Language.Common.ZhuanShu
        data_list.data_list_2[1] = {}
        local bundle,asset = ResPath.GetSkillIconById(skill_list[0].skill_icon)
        data_list.data_list_2[1].bundle = bundle
        data_list.data_list_2[1].asset = asset
        data_list.data_list_2[1].color = item_cfg.color
        data_list.data_list_2[1].click_func = BindTool.Bind(self.OnQiChongSkillSelectClick, self, skill_list[0], qc_type)
    end
    data_list.data_list_2.title_img = title_img_list[1]

    if #skill_list > 1 then
        data_list.tab_title_name_3 = Language.Common.BeiDong
        for i = 1, #skill_list do
            if not IsEmptyTable(skill_list[i]) then
                data_list.data_list_3[i] = {}
                local bundle,asset = ResPath.GetSkillIconById(skill_list[i].skill_icon)
                data_list.data_list_3[i].bundle = bundle
                data_list.data_list_3[i].asset = asset
                data_list.data_list_3[i].color = item_cfg.color
                data_list.data_list_3[i].click_func = BindTool.Bind(self.OnQiChongSkillSelectClick, self, skill_list[i], qc_type)
            end
        end
        data_list.data_list_3.title_img = title_img_list[2]
    end

    return data_list
end

-- 鲲 物品tips技能
function NewAppearanceWGData:GetKunTipsSkillData(data)
    local data_list = {}
    if IsEmptyTable(data) then
        return data_list
    end

    local kun_cfg = self:GetKunActCfgByItemId(data.item_id)
    if IsEmptyTable(kun_cfg) then
    	return data_list
    end

    local skill_list = self:GetKunSkillList(kun_cfg.id)
    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not item_cfg then
        return data_list
    end

    data_list.data_list_1 = {}
    data_list.data_list_2 = {}
    data_list.data_list_3 = {}
    if not IsEmptyTable(skill_list) then
    	local bundle, asset
        data_list.tab_title_name_2 = Language.Common.BeiDong
        for i, skill_cfg in ipairs(skill_list) do
            data_list.data_list_2[i] = {}
            bundle, asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
            data_list.data_list_2[i].bundle = bundle
            data_list.data_list_2[i].asset = asset
            data_list.data_list_2[i].color = item_cfg.color
            data_list.data_list_2[i].click_func = BindTool.Bind(self.OnQiChongSkillSelectClick, self, skill_cfg, MOUNT_LINGCHONG_APPE_TYPE.KUN)
        end
        data_list.data_list_2.title_img = "a3_sl_bq_jj"
    end

    return data_list
end

-- 物品tips技能点击回调
function NewAppearanceWGData:OnQiChongSkillSelectClick(cfg, qc_type)
    if IsEmptyTable(cfg) then
        return
    end

    local skill_describe = cfg.skill_describe
    local limit_text = ""

    local image_id = cfg.image_id or cfg.kun_id
    local is_act = self:GetSpecialQiChongSkillIsAct(qc_type, image_id, cfg.skill_id)
    if not is_act then
        limit_text = string.format(Language.NewAppearance.SkillGradeActTips, cfg.active_grade)
    end

    local capability = 0
    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        capability = self:GetKunSkillAttrCap(cfg)
    else
        capability = self:GetSingleSkillCap(cfg)
    end

    local show_data = {
        icon = cfg.skill_icon,
        top_text = cfg.skill_name,
        body_text = skill_describe,
        limit_text = limit_text,
        x = 0,
        y = 0,
        set_pos2 = true,
        hide_next = true,
        is_lock = not is_act,
        capability = capability,
    }

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function NewAppearanceWGData:GetMultiMountJointAction(mount_id)
    local data = (self.multi_mount_action_cache or {})[mount_id]
    
    if IsEmptyTable(data) then
        self.multi_mount_action_cache = self.multi_mount_action_cache or {}
        self.multi_mount_action_cache[mount_id] = self.multi_mount_action_cache[mount_id] or {}

        local cfg = self:GetMountActionCfg(mount_id)
        if not IsEmptyTable(cfg) then
            local joint_action = cfg.joint_action
            if "" ~= joint_action and 0 ~= joint_action then
                local team_data_cfg_list = string.split(joint_action, "#")
                local team_data_list = {}
                
                if not IsEmptyTable(team_data_cfg_list) then
                    for k, v in pairs(team_data_cfg_list) do
                        local data_list = {}
                        local pos_cfg_data_list = string.split(v, "|")

                        if not IsEmptyTable(pos_cfg_data_list) then
                           for i, u in pairs(pos_cfg_data_list) do
                                local action_data_list = string.split(u, ",")
                                table.insert(data_list, {pos = tonumber(action_data_list[1]), action = tonumber(action_data_list[2])})
                           end
                        end

                        table.insert(team_data_list, data_list)
                    end
                end

                data = team_data_list
                self.multi_mount_action_cache[mount_id] = team_data_list
            end
        end
    end
    
    return data
end