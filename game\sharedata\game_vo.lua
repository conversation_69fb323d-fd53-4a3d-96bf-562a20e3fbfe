BaseVo = BaseVo or BaseClass()
function BaseVo:__init()
	self.obj_id = COMMON_CONSTS.INVALID_OBJID		-- 场景中的id
	self.name = ""									-- 名字
	self.pos_x = 0									-- X坐标
	self.pos_y = 0									-- Y坐标
end

function BaseVo:__delete()
end

-- 哮天犬
XiaoTianQuanVo = XiaoTianQuanVo or BaseClass(BaseVo)
function XiaoTianQuanVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = 0
	self.move_speed = 0
	self.xiaotianquan_id = -1
	self.hp = 100
end

-- 装饰物
DecorationVo = DecorationVo or BaseClass(BaseVo)
function DecorationVo:__init()
	self.scene_index = 0
	self.decoration_id = 0
end

-- 传送点
DoorVo = DoorVo or BaseClass(BaseVo)
function DoorVo:__init()
	self.door_id = 0
	self.name = "door"
	self.type = 0
	self.to_scene_id = 0
end

-- 跳跃点
JumpPointVo = JumpPointVo or BaseClass(BaseVo)
function JumpPointVo:__init()
	self.id = 0
	self.range = 3
	self.target_id = 0
	self.jump_type = 0
	self.air_craft_id = 0
	self.is_show = 0
	self.jump_speed = 2
	self.jump_act = 0
	self.jump_tong_bu = 0
	self.offset = nil
end
-- 特效
EffectObjVo = EffectObjVo or BaseClass(BaseVo)
function EffectObjVo:__init()
	self.main_deliverer_obj_id = -1					-- 主释放者对象id（用于闪电链）
	self.deliverer_obj_id = -1						-- 释放者对象id
	self.effect_id = 0
	self.target_pos_x = 0
	self.target_pos_y = 0
	self.target_obj_id = -1
end

--结婚巡游
MarryObjVo = MarryObjVo or BaseClass(BaseVo)
function MarryObjVo:__init()
	self.obj_id = 0
	self.marry_seq = -1 								--第几序列
	self.pos_x = 0
	self.pos_y = 0
	self.dir = 0
	self.distance = 0 									-- 方向距离
	self.move_speed = 0									-- 移动速度
end


--AIboss答题光圈
AnswerCircleVo = AnswerCircleVo or BaseClass(BaseVo)
function AnswerCircleVo:__init()
	self.obj_id = COMMON_CONSTS.INVALID_OBJID		-- 场景中的id
	self.name = ""									-- 名字
	self.pos_x = 0									-- X坐标
    self.pos_y = 0									-- Y坐标
    self.res_name = ""								--资源
    self.is_shield = false                          --是否被屏蔽
    self.effect_name = ""                           --播放的特效
end

-- 触碰物
TriggerObjVo = TriggerObjVo or BaseClass(BaseVo)
function TriggerObjVo:__init()
	self.obj_id = 0
	self.action_type = 0
	self.pos_x = 0
	self.pos_y = 0
	self.param0 = 0
	self.param1 = 0
	self.affiliation = 0
	self.trigger_name = ""
end

-- 掉落物
FallItemVo = FallItemVo or BaseClass(BaseVo)
function FallItemVo:__init()
	self.item_id = 0
	self.owner_role_id = 0
	self.team_index = 0
	self.coin = 0
	self.monster_id = 0
	self.item_num = 0
	self.drop_time = 0
	self.create_time = 0
	self.lose_owner_time = 0
	self.hide_type = 0			 -- 客户端掉落消失类型：1、飘到角色，2、飘到背包
	self.is_client_fall = false -- 是否客户端模拟掉落
	self.client_name = "" 		-- 客户端模拟名字
	self.star_num = 0
end

-- 采集物
GatherVo = GatherVo or BaseClass(BaseVo)
function GatherVo:__init()
	self.gather_id = 0
	self.param = 0
end

-- 塔防副本防御塔
DefenseVo = DefenseVo or BaseClass(TriggerObjVo)
function DefenseVo:__init()
	self.pos_index = 0
end

-- Boss墓碑
BossStoneVo = BossStoneVo or BaseClass(BaseVo)
function BossStoneVo:__init()
	self.pos_x = 0
	self.pos_y = 0
	self.boss_id = 0
	self.boss_level = 0
	self.live_time = 0
end

-- 服务端场景特效
ServerEffectVo = ServerEffectVo or BaseClass(BaseVo)
function ServerEffectVo:__init()
	self.product_method = 0
	self.product_id = 0
	self.birth_time = 0
	self.disappear_time = 0
	self.param1 = 0
	self.param2 = 0
	self.src_pos_x = 0
	self.src_pos_y = 0

	self.deliverer_obj_id = -1						-- 释放者对象id
	self.summoner_obj_id = -1 						-- 目标对象id
	self.res = ""
	self.dir = 0 			-- 弧度
end

-- 世界事件
EventVo = EventVo or BaseClass(BaseVo)
function EventVo:__init()
	self.world_event_id = 0
	self.hp = 0
	self.max_hp = 0
	self.move_speed = 0
	self.dir = 0
	self.distance = 0
	-- self.buff_mark_low = 0							-- 战斗特殊效果低32位
	-- self.buff_mark_high = 0							-- 战斗特殊效果高32位
	self.buff_flag = {}

	self.param_list = {}							-- key1 场景 id key2 npc索引
end

-- 怪
MonsterVo = MonsterVo or BaseClass(BaseVo)
function MonsterVo:__init()
	self.monster_id = 0
	self.monster_type = 0
	self.hp = 0
	self.max_hp = 0
	self.move_speed = 0
	self.dir = 0
	self.distance = 0
	-- self.buff_mark_low = 0							-- 战斗特殊效果低32位
	-- self.buff_mark_high = 0							-- 战斗特殊效果高32位
	self.buff_flag = {}

	self.hudun_hp = 0
	self.hudun_max_hp = 0
	self.is_has_owner = COMMON_CONSTS.INVALID_OBJID
	self.gamer_name = ""
	self.lose_owner_time = 0
	self.wabao_owner_uuid = 0
	self.special_param = 0
	self.monster_key = 0
end

-- 神石
ShenShiVo = ShenShiVo or BaseClass(BaseVo)
function ShenShiVo:__init()
	self.has_owner = 0
	self.hp = 0
	self.max_hp = 0
	self.pos_x = 0
	self.pos_y = 0
	self.obj_id = 0
	self.owner_obj_id = 0
	self.owner_uid = 0
end

-- NPC
NpcVo = NpcVo or BaseClass(BaseVo)
function NpcVo:__init()
	self.scene_index = 0
	self.npc_id = 0
end

-- FollowNPC
FollowNpcVo = FollowNpcVo or BaseClass(BaseVo)
function FollowNpcVo:__init()
	self.move_speed = 0
	self.owner_role_id = 0
	self.owner_obj_id = 0
	self.hp = 0
	self.npc_id = 0
end

BeautyObjVo = BeautyObjVo or BaseClass(BaseVo)
function BeautyObjVo:__init()
	self.move_speed = 0
	self.owner_role_id = 0
	self.owner_obj_id = 0
	self.hp = 0
	self.jingling_guanghuan_img_id = 0
	self.husong_taskid = 0
	self.husong_color = 0

end

-- 镖车
TruckObjVo = TruckObjVo or BaseClass(BaseVo)
function TruckObjVo:__init()
	self.truck_color = 0
	self.owner_role_id = 0
	self.owner_obj_id = -1
end

-- -- 宠物
-- PetObjVo = PetObjVo or BaseClass(BaseVo)
-- function PetObjVo:__init()
-- 	self.owner_role_id = 0
-- 	self.owner_obj_id = -1
-- 	self.pos_x = 0
-- 	self.pos_y = 0
-- 	self.pet_name = ""
-- 	self.pet_id = -1
-- end

-- 宠物
PetObjVo = PetObjVo or BaseClass(BaseVo)
function PetObjVo:__init()
	self.pet_id = 0
	self.lingchong_appeid = 0
	self.pet_type = 0
	self.obj_name = ""
	self.image_id = 0
	self.dir = 0
	self.hp = 0
	self.max_hp = 0
	self.move_speed = 0
	self.pos_x = 0
	self.pos_y = 0
	self.distance = 0
	self.grow = 0
	self.owner_objid = 0
	self.owner_obj_name = ""
	self.pet_level = 0
	self.pet_zizhi = 0
	self.role_vo = {}
end

-- 神将
ShenJiangObjVo = ShenJiangObjVo or BaseClass(BaseVo)
function ShenJiangObjVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.shenjiang_name = ""
	self.shenjiang_id = -1
end

-- 灵童/宝宝
SoulBoyObjVo = SoulBoyObjVo or BaseClass(BaseVo)
function SoulBoyObjVo:__init()
	self.use_baby_id = -1
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.name = ""
	self.soulboy_lt_id = -1
	self.soulboy_lg_id = -1
	self.soulboy_ls_id = -1
	self.lingtong_wing_id = -1
	self.soulboy_image = -1
	self.move_speed = 0
	self.hp = 100
end

-- 跟随天神
FollowMingjiangVo = FollowMingjiangVo or BaseClass(BaseVo)
function FollowMingjiangVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.name = ""
	self.move_speed = 0
	self.hp = 100
	self.soulboy_lt_id = -1
end

-- 精灵
JingLingObjVo = JingLingObjVo or BaseClass(BaseVo)
function JingLingObjVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.jingling_name = ""
	self.jingling_id = -1
end

-- 宝宝
BabyObjVo = BabyObjVo or BaseClass(BaseVo)
function BabyObjVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.use_baby_id = -1
end


GoddessObjVo = GoddessObjVo or BaseClass(BaseVo)
function GoddessObjVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.name = ""
	self.wing_res_id = 0
	self.shen_gong_res_id = 0
	self.goddess_res_id = -1
end

-- 温泉皮艇
BoatObjVo = BoatObjVo or BaseClass(BaseVo)
function BoatObjVo:__init()
	self.pos_x = 0
	self.pos_y = 0
	self.name = ""
	self.boy_obj_id = 0
	self.girl_obj_id = 0
	self.action_type = 0
end

-- 多人坐骑
MultiMountObjVo = MultiMountObjVo or BaseClass(BaseVo)
function MultiMountObjVo:__init()
	self.mount_id = 0
	self.mount_res_id = 0
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.partner_role_id = 0
	self.partner_obj_id = -1
	self.dir = 0
	self.move_speed = 0
end


-- 城主雕像
CityOwnerStatueVo = CityOwnerStatueVo or BaseClass(BaseVo)
function CityOwnerStatueVo:__init()
	self.obj_id = COMMON_CONSTS.INVALID_OBJID
	self.pos_x = 0
	self.pos_y = 0
	self.is_cz = true
end

-- 守护小鬼
GuardObjVo = GuardObjVo or BaseClass(BaseVo)
function GuardObjVo:__init()
	self.owner_role_id = 0
	self.owner_obj_id = -1
	self.pos_x = 0
	self.pos_y = 0
	self.guard_id = 0
	self.move_speed = 0
	self.hp = 100
end

-- 武魂
WuHunObjVo = WuHunObjVo or BaseClass(BaseVo)
function WuHunObjVo:__init()
	self.wuhun_id = 0
	self.wuhun_lv = 0							-- 武魂真身等级
	self.wuhun_appeid = 0
	self.wuhun_type = 0
	self.obj_name = ""
	self.image_id = 0
	self.dir = 0
	self.hp = 0
	self.max_hp = 0
	self.pos_x = 0
	self.pos_y = 0
	self.owner_objid = 0
	self.owner_obj_name = ""
	self.wuhun_level = 0
	self.role_vo = {}
end

-- 灵兽
BeastObjVo = BeastObjVo or BaseClass(BaseVo)
function BeastObjVo:__init()
	self.beast_id = 0
	self.beast_type = 0
	self.obj_name = ""
	self.image_id = 0
	self.dir = 0
	self.hp = 0
	self.max_hp = 0
	self.move_speed = 0
	self.pos_x = 0
	self.pos_y = 0
	self.distance = 0
	self.grow = 0
	self.owner_objid = 0
	self.owner_obj_name = ""
	self.beast_level = 0
	self.beast_zizhi = 0
	self.battle_index = 0
	self.beast_skin = -1
	self.role_vo = {}
end

-- 任务召唤物体
TaskCallInfoObjVo = TaskCallInfoObjVo or BaseClass(BaseVo)
function TaskCallInfoObjVo:__init()
	self.task_callinfo_id = 0
	self.obj_name = ""
	self.image_id = 0
	self.dir = 0
	self.hp = 0
	self.max_hp = 0
	self.move_speed = 0
	self.pos_x = 0
	self.pos_y = 0
	self.distance = 0
	self.grow = 0
	self.owner_objid = 0
	self.owner_obj_name = ""
	self.role_vo = {}
end

-- 双生武魂
ShuangShengTianShenObjVo = ShuangShengTianShenObjVo or BaseClass(BaseVo)
function ShuangShengTianShenObjVo:__init()
	self.shaungsheng_tianshen_aura_id = 0
	self.shaungsheng_tianshen_appid = 0
	self.shaungsheng_tianshen_type = 0
	self.obj_name = ""
	self.image_id = 0
	self.dir = 0
	self.hp = 0
	self.max_hp = 0
	self.pos_x = 0
	self.pos_y = 0
	self.owner_objid = 0
	self.owner_obj_name = ""
	self.wuhun_level = 0
	self.role_vo = {}
end

-- 角色
RoleVo = RoleVo or BaseClass(BaseVo)
function RoleVo:__init()
	self.role_id = 0								-- 角色ID
	self.origin_uid = 0                             -- 原服uid  非跨服状态下发的是 0
	self.origin_server_id = 0                       -- 原服serverid
	self.plat_name = ""								-- 平台名字
	self.uuid = MsgAdapter.InitUUID()               -- uuid
	self.dir = 0									-- 方向
	self.move_mode_param = 0						-- 移动模式参数
	self.role_status = 0							-- 角色状态
	self.hp = 0										-- 当前hp
	self.max_hp = 0									-- 最大hp
	self.mp = 0										-- 最大法力
	self.max_mp = 0									-- 最大法力值

	self.bianshen_hp = 0                            -- 当前变身hp
	self.bianshen_max_hp = 0                        -- 当前变身最大hp

	self.level = 0									-- 等级
	self.name = ""									-- 名字
	self.camp = 0									-- 阵营
	self.prof = 0									-- 职业
	self.sex = 0									-- 性别
	self.vip_level = 0								-- vip等级
	self.rest_partner_obj_id = 0					-- 双修伙伴id
	self.move_speed = 0								-- 移动速度
	self.distance = 0								-- 方向距离
	self.attack_mode = 0							-- 攻击模式
	self.name_color = 0								-- 名字颜色
	self.move_mode = 0								-- 移动模式
	self.authority_type = 0							-- 身份类型
	self.husong_color = 0							-- 护送任务颜色
	self.capability_rank = 0						-- 战力排名
	self.husong_taskid = 0							-- 护送任务ID
	self.guild_post = 0								-- 仙盟职位
	self.mount_appeid = 0							-- 坐骑外观
	self.flyup_use_image = 0						-- 坐骑使用的筋斗云资源
	self.jianzhen_appe = 0							-- 剑阵
	self.qilinbi = 0								-- 麒麟臂id

	self.wing_appeid = 0							-- 羽翼id
	self.fabao_appeid = 0							-- 法宝id
	self.soulboy_lt_id = 0						    -- 灵宠id
	self.shenwu_appeid = 0							-- 神武id
	self.soulboy_lg_id = 0						    -- 灵弓id
	self.soulboy_ls_id = 0							-- 灵骑id
	self.lingtong_wing_id = 0						-- 灵翼id
	self.jianzhen_appeid = 0						-- 剑阵id
	self.tianshenshenqi_appeid = 0					-- 神器外观id
	self.fight_mount_skill_level = 0				-- 战斗坐骑技能等级

	self.appearance = {							    -- 角色外观数据
		shizhuang_tail = 0,
		fashion_photoframe = 0,
		shizhuang_bracelet = 0,
		fashion_wuqi = 0,
		qilinbi = 0,
		fashion_foot = 0,
		wuqi_id = 0,
		shizhuang_wuqi = 0,
		shizhuang_body = 0,
		wing_appeid = 0,
		fashion_guanghuan = 0,
		wing_use_grade = 0,
		shizhuang_photoframe = 0,
		fashion_bubble = 0,
		jianzhen_appeid = 0,
		jianzhen = 0,
		xianjian_id = 0,
		shizhuang_guanghuan = 0,
		mask = 0,
		clothes_id = 0,
		fashion_body = 0,
		divineweapon_puton_seq = 0,
		fabao_appeid = 0,
		wing_jinhua_grade = 0,
		shizhuang_mask = 0,
		tail = 0,
		body_color = 0,
		waist = 0,
		shizhuang_bubble = 0,
		shizhuang_foot = 0,
		shouhuan = 0,
		wing_special_imageid = 0,
		shenwu_appeid = 0,
		linggong_appeid = 0,
		shizhuang_belt = 0,
		footprint_effect_id = 0,
		default_face_res_id = 0,
		default_hair_res_id = 0,
		default_body_res_id = 0,
		fazhen_id = -1,					-- 法阵 from 0,
		skill_halo_id = 0,
	}

	self.role_diy_appearance = {                    -- 
		hair_color = {},
		eye_size = 0,
		eye_position = 0,
		eye_shadow_color = {},
		left_pupil_type = 0,
		left_pupil_size = 0,
		left_pupil_color = {},
		right_pupil_type = 0,
		right_pupil_size = 0,
		right_pupil_color = {},
		mouth_size = 0,
		mouth_position = 0,
		mouth_color = {},
		face_decal_id = 0,
		preset_seq = 1,
	}

	self.xiaotianquan_id = -1 						-- 哮天犬
	self.task_callinfo_id = 0                       -- 任务召唤物
	self.pet_id = -1								-- 宠物id
	self.pet_obj_id = -1 							-- 宠物obj_id
	self.pet_special_img = 0						-- 宠物形象
	self.pet_level = 0								-- 宠物等级
	self.pet_zizhi_level = 0						-- 宠物资质等级
	self.use_halo_img = 0							-- 宠物光环id
	self.pet_name = ""								-- 宠物名字
	self.use_fly_img = 0							-- 宠物飞升
	self.use_fazhen_grade = 0						-- 宠物法阵

	self.shenjiang_id = -1							-- 神将id
	self.shenjiang_special_img = 0					-- 神将特殊形象id
	self.shenjiang_level = 0						-- 神将等级
	self.shenjiang_grade = 0						-- 神将阶数
	self.shenjiang_name = ""						-- 神将名字
	self.use_halo_id = 0							-- 神将光环id
	self.use_shenjiang_fly_id = 0					-- 神将飞升

	self.jingling_id = -1							-- 精灵id
	self.jingling_level = 0							-- 精灵等级id
	self.use_jingling_imageid = 0					-- 使用精灵形象
	self.jingling_name = ""							-- 精灵名字
	self.use_jingling_special_img = 0				-- 精灵幻化
	self.use_jingling_halo_img = 0					-- 精灵光环


	self.chengjiu_title_level = 0					-- 成就称号等级
	self.xianjie_level = 0							-- 仙阶

	self.guild_id = 0								-- 军团ID
	self.guild_name = ""							-- 军团名字
	self.used_title_list = {0,0,0}					-- 头顶称号数据
	self.use_pet_titleid = 0						-- 宠物称号
	self.use_shenjiang_titleid = 0					-- 神将称号
	-- self.buff_mark_low = 0							-- 战斗特殊效果低32位
	-- self.buff_mark_high = 0							-- 战斗特殊效果高32位
	self.buff_flag = {}
	self.special_param = 0							-- 特殊场景逻辑参数1
	self.special_param2 = 0							-- 特殊场景逻辑参数2
	self.height = 0									-- 跳跃高度
	self.special_appearance = 0						-- 特殊外观
	self.appearance_param = 0						-- 特殊外观参数
	self.appearance_param_extend = {}				-- 特殊外观参数列表

	self.shenbing_flag = 0							-- 神兵外观
	self.lover_name = ""							-- 结婚信息
	self.lover_uid = 0								-- 伴侣uid
	self.jilian_type = 0							-- 祭炼类型
	self.jinghua_husong_status = 0					-- 精华护送状态
	self.use_pet_halo_img = 0						-- 宠物光环
	self.used_pet_jie = 0							-- 宠物阶数
	self.use_shenjiang_halo_img = 0					-- 神将光环
	self.used_shenjiang_jie = 0						-- 神将阶数

	self.xinghun = 0								-- 星魂
	self.longhun = 0								-- 龙魂
	self.history_longhun = 0						-- 历史龙魂
	self.use_fabao_id = 0							-- 使用的法宝
	self.use_fabao_special_img_id = 0				-- 使用法宝幻化
	self.pet_exp = 0								-- 宠物经验
	self.shitu_exp = 0								-- 师徒经验
	self.day_longhun = 0						    -- 每日龙魂/贡献

	self.guild_gongxian = 0							-- 贡献
	self.guild_total_gongxian = 0					-- 总贡献
	self.all_charm = 0								-- 魅力
	self.shengwang = 0 								-- 声望
	self.day_reward_shengwang = 0 					-- 每日声望
	self.day_assist_shengwang = 0					-- 每日协助声望
	self.is_shadow = 0								-- 是角色影子
	self.tianxiange_level = 0						-- 天仙阁等级
	self.day_revival_times = 0						-- 每日复活次数
	self.zhan_ling = 0								-- 战灵（货币）
	self.zhan_hun = 0								-- 战魂（货币）
	self.shadow_type = 0
	self.shadow_param = 0
	self.halo_type  = 0

	self.wuxing_gongji = 0
	self.wuxing_fangyu = 0

	self.multi_mount_res_id = 0						-- 双人坐骑id
	self.multi_mount_is_owner = 0 					-- 是否当前双人坐骑的主人
	self.multi_mount_other_uid = 0 					-- 一起骑乘的玩家role_id
	self.multi_mount_huanhua_res_id = 0 			-- 双人坐骑幻化res_id
	self.is_in_hot_spring = false 					-- 是否在温泉区


	self.is_in_snow = false 						-- 温泉是否雪人状态
	self.is_in_hot_water = false 					-- 温泉是否被泼热水状态
	self.shuangxiu_state = 0 						-- 双修状态 (1 是双修，0 不是双修)
	self.shuangxiu_identity = 0 					-- 双修身份(0 是被发起者，1 是发起者, -1 表示都没进入双修状态的普通身份)
	self.shuangxiu_partner_obj = -1					-- 双修伙伴id

	self.anmo_state = 0								-- 按摩状态
	self.anmo_partner_obj = -1 						-- 按摩伙伴id
	self.is_anmo_sender = false 					-- 是否按摩发起人

	self.shuangxiu_attack_state = -1				-- 温泉攻击状态(-1 是普通状态，0 是被攻击方，1 是攻击方)
	self.shuangxiu_action_type = -1					-- 温泉动作(-1 是普通状态，3 是扔雪球，4 是泼热水)
	self.shuangxiu_attack_target_id = -1			-- 温泉攻击目标id
	self.is_hotspring_actor = -1					-- 温泉攻击方(-1 是普通状态，0 是扔雪球者，1 是泼热水者)

	self.jump_aspeed = 0
	self.jump_speed = 0
	self.jump_move_speed = 0
	self.tianming_appid = -1						-- 天命幻化
	self.role_title_appeid = 0						-- 江湖闯关层数

	self.love_stamp_img_id = 0						-- 印记
	self.own_drop_boss_count = 0					-- boss掉落归属个数

	self.use_baby_id = -1 							-- 宝宝使用形象
	self.guard_id = -1 							-- 守护小鬼外观
	self.shield_vip_flag = 0 					-- 隐藏贵族标识
	self.baozhu_act_flag = 0 					-- 宝珠激活标识
	self.task_appearn_param_1 = 0 				-- 任务高级坐骑
	self.career_upgrade_type = 0 				-- 职业进阶类型
	self.information_type = 0 					-- 情报类型
	self.information_num = 0					-- 情报数量
	self.plat_type = 0							-- 平台类型
	self.merge_plat_type = 0					-- 合服后的平台类型				
    self.merge_server_id = 0					-- 和服后的服务器ID
    self.area_index = -1                        -- 战区index(大跨服组的index) 0/1
    self.xianjie_boss_equip = {}                --当前仙界装备信息

	self.god_or_demon_type = 0                  -- 一念神魔类型
	self.god_or_demon_level = 0                 -- 一念神魔等级
	self.god_or_demon_grade = 0                 -- 一念神魔阶级

	self.wuhun_id = 0							-- 武魂真身
	self.wuhun_lv = 0							-- 武魂真身等级
	self.beast_id = 0							-- 幻兽
	self.wuhun_hunzhen_id = -1					-- 武魂魂阵
end 

-- 主角
MainRoleVo = MainRoleVo or BaseClass(RoleVo)
function MainRoleVo:__init()
	self.server_id = 0								-- 服ID
	self.current_server_id = 0                      -- 当前服id（在圣天城其他主城使用）
	self.cur_plat_name = ""						    -- 当前平台名
	self.scene_id = 0								-- 场景ID
	self.scene_key = 0								-- 场景Key
	self.last_scene_id = 0							-- 最后场景ID
	self.is_main_role = true

	self.energy = 0									-- 体力

	self.exp = 0									-- 经验
	self.vip_extra_role_exp = 0						-- 经验池经验
	self.max_exp = 0								-- 最大经验
	self.capability = 0								-- 战斗力
	self.buff_mark = 0								-- buff效果标记
	self.evil = 0									-- 罪恶值
	self.xianhun = 0								-- 仙魂
	self.yuanli = 0									-- 元力
	self.nv_wa_shi = 0								-- 女娲石
	self.lingjing = 0								-- 灵晶
	self.chengjiu = 0								-- 成就
	self.hunli = 0									-- 魂力
	self.gold = 0
	self.bind_gold = 0
	self.coin = 0
	self.replace_coin = 0							-- 代币
	self.cash_point = 0                             -- 现金点
	self.recharge_volume = 0                        -- 充值卷
	self.chivalrous = 0                             -- 侠义值

	self.is_team_leader = 0							-- 是否队长
	self.nuqi = 0									-- 怒气
	self.honour = 0									-- 荣誉
	self.cross_honor = 0							-- 跨服荣誉

	self.avatar_key_big = 0							-- 大头像
	self.avatar_key_small = 0						-- 小头像

	self.last_marry_time = 0 						-- 上一次结婚时间
	self.gongxun = 0								-- 功勋
	self.create_time = 0							-- 创建时间

	self.last_get_city_name_time = 0				-- 上次获得城市名字的时间
	self.city_name = ""								-- 城市名字
	self.is_real_location = 0

	self.create_timestamp = 0 						-- 创建角色时间


	self.shengming_max = 0			-- 基础生命
	self.gongji = 0					-- 基础攻击
	self.fangyu = 0					-- 基础防御
	self.pojia = 0					-- 基础破甲
	self.baoji_shanghai = 0			-- 基础暴击
	self.kangbao_shanghai = 0		-- 基础抗暴
	self.yuansu_sh = 0				-- 基础五行伤害
	self.yuansu_hj = 0				-- 基础五行护甲

	self.shengming_qq = 0			--生命窃取
	self.fangtan = 0				--反弹伤害
	self.shanghai_zs = 0			--真实伤害
	self.fangyu_zs = 0              --真实防御
	self.shengming_hf = 0  			--生命恢复
	self.shanghai_jn = 0      		--技能伤害
	self.shanbi_per = 0             --闪避几率
	self.mingzhong_per = 0		    --命中几率
	self.baoji_per = 0              --暴击率
	self.kangbao_per = 0			--抗暴率
	self.lianji_per = 0             --连击率
	self.lianjikang_per = 0         --连击抵抗
	self.jichuan_per = 0			--击穿率
	self.jichuankang_per = 0		--击穿抵抗
	self.gedang_per = 0             --格挡率
	self.podang_per = 0             --破档率
	self.baoji_shanghai_per = 0     --暴击伤害
	self.baoji_shanghai_jm_per = 0  --暴击伤害减免
	self.lianji_shanghai_per = 0	--连击伤害比例
	self.lianji_shanghai_jm_per = 0 --连击伤害减免
	self.gedang_ms_per = 0          --格挡免伤
	self.gedang_ms_my_per = 0       --格挡免伤免疫
	self.shanghai_jc_per = 0        --伤害加成
	self.shanghai_jm_per = 0        --伤害减免
	self.yuansu_jk_per = 0          --元素减抗
	self.yuansu_kx_per = 0          --元素抗性
	self.jineng_shanghai_zj_per = 0 --技能伤害增加
	self.jineng_shanghai_jm_per = 0 --技能伤害减免
	self.shanghai_quan_jc_per = 0   --全属性伤害加成
	self.shanghai_quan_jm_per = 0   --全属性伤害减免
	self.zengshang_boss_per = 0     --首领增伤
	self.jianshang_boss_per = 0     --首领减伤
	self.zengshang_guaiwu_per = 0	--怪物增伤
	self.jianshang_guaiwu_per = 0	--怪物减伤
	self.zengshang_per = 0		    --玩家增伤
	self.jianshang_per = 0		    --玩家减伤
	self.zengshang_bs_per = 0		--变身增伤
	self.jianshang_bs_per = 0		--变身减伤
	self.mingzhong_yc_per = 0		--异常状态命中
	self.dikang_yc_per = 0		    --异常状态抵抗
	self.zengshang_yc_per = 0		--异常状态增伤
	self.zhiliaoxiaoguo_per = 0	    --治疗效果
	self.zengshang_gx_per = 0		--高血增伤
	self.zengshang_xr_per = 0		--虚弱增伤
	self.shengming_hf_per = 0		--生命回复比例

	self.shengming_jc_per = 0		--基础生命加成
	self.gongji_jc_per = 0			--基础攻击加成
	self.fangyu_jc_per = 0			--防御加成
	self.pojia_jc_per = 0			--破甲加成
	self.yuansu_sh_jc_per = 0		--元素伤害加成
	self.yuansu_hj_jc_per = 0		--元素护甲加成
	self.baoji_jc_per = 0			--暴击固定值加成
	self.kangbao_jc_per = 0			--抗暴固定值加成
	self.shengming_qq_jc_per = 0	--生命窃取加成
	self.fangtan_jc_per = 0			--反弹伤害加成
	self.shanghai_zs_jc_per = 0		--真实伤害加成
	self.fangyu_zs_jc_per = 0		--真实防御加成

	self.base_max_hp = 0							-- 基础最大血量 不包含Buff附加
	self.base_max_mp = 0

	self.base_gongji = 0							-- 基础攻击
	self.base_fa_gongji = 0							-- 基础攻击(法术)
	self.base_fangyu = 0							-- 基础防御
	self.base_fa_fangyu = 0							-- 基础防御(法术)
	self.base_mingzhong = 0							-- 基础命中
	self.base_shanbi = 0							-- 基础闪避
	self.base_baoji = 0								-- 基础暴击
	self.base_jianren = 0							-- 基础坚韧
	self.base_move_speed = 0						-- 基础移动速度

	self.base_fujia_shanghai = 0					-- 附加伤害 改名无视一击
	self.base_dikang_shanghai = 0					-- 抵抗伤害
	self.base_per_jingzhun = 0						-- 精准万分比（无用）
	self.base_per_mingzhong = 0						-- 命中万分比
	self.base_per_shanbi = 0						-- 闪避万分比
	self.base_per_baoji = 0							-- 暴击万分比
	self.base_per_kangbao = 0						-- 抗暴万分比（无用）
	self.base_per_pofang = 0						-- 破防万分比
	self.base_per_mianshang = 0						-- 免伤万分比

	self.per_baoji_zengshang = 0						--暴击增伤
	self.per_baoji_jianshang = 0						--暴击减伤
	self.per_skill_zengshang = 0						--技能增伤
	self.per_skill_jianshang = 0						--技能减伤

	--self.base_per_baoji_jiacheng = 0				-- 暴击加成万分比

	self.gong_ji = 0								-- 攻击
	self.fa_gong_ji = 0								-- 法术攻击
	self.fang_yu = 0								-- 防御
	self.fa_fang_yu = 0								-- 法术防御
	self.ming_zhong = 0								-- 命中
	self.shan_bi = 0								-- 闪避
	self.bao_ji = 0									-- 暴击
	self.jian_ren = 0								-- 坚韧
	self.move_speed = 0								-- 移动速度
	self.po_jia = 0                                 -- 破甲
	self.fujia_shanghai = 0							-- 附加伤害 改名无视一击
	self.dikang_shanghai = 0						-- 抵抗伤害
	self.per_jingzhun = 0							-- 精准万分比（无用）
	self.per_pofang = 0								-- 破防万分比
	self.per_mianshang = 0							-- 免伤万分比

	self.huixinyiji_per = 0 						-- 会心一击几率
	self.huixinyiji_kang_per = 0 					-- 会心一击抵抗率
	self.gedang_chuantou_per = 0 					-- 格挡穿透
	self.gedang_jianshang_per = 0 					-- 格挡减伤

	self.per_pvp_reduce_hurt = 0					-- PVP免伤万分比
	self.per_pvp_add_hurt = 0						-- PVP增伤万分比
	self.per_pve_reduce_hurt = 0					-- PVE免伤万分比
	self.per_pve_add_hurt = 0						-- PVE增伤万分比
	self.per_shanghai_add = 0					    -- 伤害加成万分比

	self.huo_shanghai_zj_per = 0					--离火伤害增加
	self.boss_zhenshang = 0							--BOSS真伤
	self.boss_palsy_per = 0							--BOSS麻痹
	self.boss_seckill_per = 0						--BOSS秒杀
	self.lei_shanghai_zj_per = 0					--雷罚伤害增加
	self.lei_shanghai_jm_per = 0					--雷罚伤害减免
end

MapMoveVo = MapMoveVo or BaseClass(BaseVo)
function MapMoveVo:__init()
	self.obj_id = 0
	self.obj_type = 0
	self.type_special_id = 0
	self.dir = 0
	self.distance = 0
	self.pos_x = 0
	self.pos_y = 0
	self.move_speed = 0
	self.monster_key = 0
end
