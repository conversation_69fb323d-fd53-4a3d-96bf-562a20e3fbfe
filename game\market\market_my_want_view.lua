function MarketView:MyWantLoadCallBack()
	self.my_want_big_type_list = AsyncListView.New(MarketBigTypeItem, self.node_list["my_want_type_list"]) 			-- 求购大类型列表
	self.my_want_big_type_list:SetSelectCallBack(BindTool.Bind1(self.OnClickMyWantBigType, self))
	self.my_want_big_type_list:SetDataList(MarketWGData.Instance:GetBigTypeCfg())
	self.my_want_big_id = 1 																						-- 当前选中的大类型
	self.my_want_sub_type = -1 																						-- 当前选中的子类型

	self.can_want_list = AsyncListView.New(MarketCanWantItem, self.node_list["my_want_item_list"]) 					-- 可求购列表
	self.can_want_list:SetSelectCallBack(BindTool.Bind(self.OnClickCanWantIten, self))

	self.my_want_sub_type_item_list = {} 																			-- 用于存储子类型gameobject

	-- 品阶筛选下拉框
	self.node_list["my_want_order_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnMyWantOrderDropdownChange, self))
	self.order_dropdown_select = 0
	self.node_list["my_want_order_dropdown"].dropdown.value = self.order_dropdown_select

	-- 品质筛选下拉框
	self.node_list["my_want_color_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnMyWantColorDropdownChange, self))
	self.color_dropdown_select = 0
	self.node_list["my_want_color_dropdown"].dropdown.value = self.color_dropdown_select

	-- 星级下拉框
	self.node_list["my_want_star_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnMyWantStarDropdownChange, self))
	self.star_dropdown_select = 1
	self.node_list["my_want_star_dropdown"].dropdown.value = self.star_dropdown_select - 1

	-- 搜索按钮
	XUI.AddClickEventListener(self.node_list["my_want_search_btn"], BindTool.Bind1(self.OnClickMyWantSearch, self))
	XUI.AddClickEventListener(self.node_list["btn_publish"], BindTool.Bind1(self.OnClickPublish, self))
	XUI.AddClickEventListener(self.node_list["btn_reset"], BindTool.Bind1(self.OnClickReset, self))
	XUI.AddClickEventListener(self.node_list["my_want_total_price_btn"], BindTool.Bind1(self.OnClickSetTotalPrice, self))
	XUI.AddClickEventListener(self.node_list["my_want_amount_btn"], BindTool.Bind1(self.OnClickSetMyWantAmount, self))

	self.my_want_total_price = 0
	self.my_want_amount = 1

	self.my_want_star_list = {}

	self:FlushPublicPanel()
end

function MarketView:MyWantReleaseCallBack()
	if self.my_want_big_type_list ~= nil then
		self.my_want_big_type_list:DeleteMe()
		self.my_want_big_type_list = nil
	end

	if self.my_want_sub_type_item_list then
		for i, v in ipairs(self.my_want_sub_type_item_list) do
			v:DeleteMe()
			self.my_want_sub_type_item_list = {}
		end
	end

	if self.can_want_list then
		self.can_want_list:DeleteMe()
		self.can_want_list = nil
	end
end

function MarketView:MyWantShowIndexCallBack()

end

function MarketView:MyWantOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushMyWantAllPart()
		elseif k == "Part" then

		end
	end
end

function MarketView:FlushMyWantAllPart()
	self:FlushMyWantChildPanelActive()
	self:FlushMyWantSubTypeGrid()
	self:FlushCanWantList()
end

-- 子面板显隐
function MarketView:FlushMyWantChildPanelActive()
	self.node_list["my_want_subtype_panel"]:SetActive(self.my_want_sub_type == -1)
	self.node_list["my_want_public_panel"]:SetActive(self.my_want_sub_type ~= -1)
end

-- 刷新子类型grid
function MarketView:FlushMyWantSubTypeGrid()
	if self.sub_type_item_prefab == nil then
		self:LoadSubTypeItemPrefab(BindTool.Bind(self.FlushMyWantSubTypeGrid, self))
		return
	end

	for i, v in ipairs(self.my_want_sub_type_item_list) do
		v:SetActive(false)	
	end
	local sub_type_cfg = MarketWGData.Instance:GetSubTypeCfg(self.my_want_big_id)
	local index = 0
	for sub_type, cfg in pairs(sub_type_cfg) do
		index = index + 1
		if not self.my_want_sub_type_item_list[index] then
			local go = ResMgr:Instantiate(self.sub_type_item_prefab)
			go:SetActive(true)
			go.transform:SetParent(self.node_list["my_want_subtype_grid"].transform, false)
			self.my_want_sub_type_item_list[index] = MarketMyWantSubTypeItem.New(go)
			self.my_want_sub_type_item_list[index]:SetClickCallBack(BindTool.Bind(self.OnClickMyWantSubType, self))
		end
		self.my_want_sub_type_item_list[index]:SetData(cfg)
		self.my_want_sub_type_item_list[index]:SetActive(true)
	end
end

-- 刷新发布面板
function MarketView:FlushPublicPanel()
	self.node_list["my_want_total_price"].text.text = self.my_want_total_price
	self.node_list["my_want_custody"].text.text = self.my_want_total_price
	self.node_list["my_want_amount"].text.text = self.my_want_amount

	self:FlushMyWantStarDrowDown()
end

-- 点击设置总价
function MarketView:OnClickSetTotalPrice()
	local function callback(input_num)
		self.my_want_total_price = input_num
		self:FlushPublicPanel()
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(MarketWGData.Instance:GetMinPrice())
	num_keypad:SetMaxValue(9999999)
	num_keypad:SetMinValue(MarketWGData.Instance:GetMinPrice())
	num_keypad:SetOkCallBack(callback)
end

-- 点击设置数量
function MarketView:OnClickSetMyWantAmount()
	if self.select_can_want_auction_cfg == nil then
		return
	end

	local max = 999
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.select_can_want_auction_cfg.item_id)
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		max = 1
	end

	local function callback(input_num)
		self:ChangeMyWantAmount(input_num)
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.my_want_amount)
	num_keypad:SetMaxValue(max)
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(callback)
end

function MarketView:ChangeMyWantAmount(new_amount)
	self.my_want_total_price = math.floor(self.my_want_total_price / self.my_want_amount * new_amount)
	self.my_want_amount = new_amount
	self:FlushPublicPanel()
end

-- 根据大类型和子类型刷新求购列表
function MarketView:FlushCanWantList()
	if self.my_want_sub_type < 0 then
		return
	end
	local can_want_cfg = self:CalCanWantList(MarketWGData.Instance:GetAuctionCfgByType(self.my_want_big_id, self.my_want_sub_type))
	self.can_want_list:SetDataList(can_want_cfg)
	self.can_want_list:SelectIndex(1)
	self.can_want_list:JumpToTop()
	self.node_list["my_want_empty_tips"]:SetActive(IsEmptyTable(can_want_cfg))
end

-- 筛选求购信息
function MarketView:CalCanWantList(want_list)
	local result = {}
	if not want_list then
		return result
	end
	for i,v in ipairs(want_list) do
		if self:MyWantCanShow(v) then
			v.srot_key = (v.item_cfg.order or 0) * 1000000 + v.item_cfg.color * 10000 + v.item_id
			table.insert(result, v)
		end
	end

	table.sort(result, SortTools.KeyLowerSorter("srot_key"))
	return result
end

function MarketView:MyWantCanShow(want_info)
	local item_cfg = ItemWGData.Instance:GetItemConfig(want_info.item_id)
	-- 筛选阶数
	if self.my_want_order_dropdown_select ~= 0 then
		if item_cfg.order ~= self.my_want_order_list[self.my_want_order_dropdown_select] then
			return false
		end
	end

	-- 筛选品质
	if self.my_want_color_dropdown_select ~= 0 then
		if item_cfg.color ~= self.my_want_color_list[self.my_want_color_dropdown_select] then
			return false
		end
	end

	-- 筛选搜索文字内容
	if self.my_want_search_name and self.my_want_search_name ~= "" then
		if not string.find(item_cfg.name, self.my_want_search_name) then
			return false
		end
	end

	return true
end

-- 刷新阶数筛选下拉框
function MarketView:FlushMyWantOrderDrowDown()
	if self.my_want_sub_type < 0 then
		self.node_list["my_want_order_dropdown"]:SetActive(false)
		return
	end
	if IsEmptyTable(self.my_want_order_list) then
		self.order_dropdown_select = 0
		self.node_list["my_want_order_dropdown"]:SetActive(false)
		return
	end
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.Market.NameList1[0])
	for i, order in ipairs(self.my_want_order_list) do
		list_string:Add(Language.Market.NameList1[order])
	end
	self.node_list["my_want_order_dropdown"].dropdown:ClearOptions()
	self.node_list["my_want_order_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["my_want_order_dropdown"].dropdown.value = self.my_want_order_dropdown_select
	self.node_list["my_want_order_dropdown"]:SetActive(true)
end

-- 刷新品质筛选下拉框
function MarketView:FlushMyWantColorDrowDown()
	if self.my_want_sub_type < 0 then
		self.node_list["my_want_color_dropdown"]:SetActive(false)
		return
	end
	if IsEmptyTable(self.my_want_color_list) then
		self.color_dropdown_select = 0
		self.node_list["my_want_color_dropdown"]:SetActive(false)
		return
	end
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.Market.NameList2[0])
	for i, color in ipairs(self.my_want_color_list) do
		list_string:Add(Language.Market.NameList2[color])
	end
	self.node_list["my_want_color_dropdown"].dropdown:ClearOptions()
	self.node_list["my_want_color_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["my_want_color_dropdown"].dropdown.value = self.color_dropdown_select
	self.node_list["my_want_color_dropdown"]:SetActive(true)
end


-- 刷新星级筛选下拉框
function MarketView:FlushMyWantStarDrowDown()
	if not self.select_can_want_auction_cfg then
		self.node_list["my_want_equip_star_group"]:SetActive(false)
		return
	end

	if IsEmptyTable(self.my_want_star_list) then
		self.node_list["my_want_equip_star_group"]:SetActive(false)
		return
	end

	local list_string = System.Collections.Generic.List_string.New()
	for i, star in ipairs(self.my_want_star_list) do
		list_string:Add(Language.Market.SelectStar[star])
	end
	self.node_list["my_want_star_dropdown"].dropdown:ClearOptions()
	self.node_list["my_want_star_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["my_want_star_dropdown"].dropdown.value = self.star_dropdown_select - 1
	self.node_list["my_want_equip_star_group"]:SetActive(true)
end


-- 点击大类型
function MarketView:OnClickMyWantBigType(big_type_item, select_index)
	self.my_want_big_id = big_type_item.data.big_id
	-- 清空子类型
	self.my_want_sub_type = -1

	-- 清空筛选下拉框选择
	self.my_want_order_list = {}
	self.my_want_order_dropdown_select = 0
	self.my_want_color_list = {}
	self.my_want_color_dropdown_select = 0

	self.my_want_search_name = ""

	self.select_can_want_auction_cfg = nil

	self:FlushMyWantAllPart()
end

-- 点击子类型
function MarketView:OnClickMyWantSubType(sub_type_cfg)
	self.my_want_sub_type = sub_type_cfg.small_id
	MarketWGCtrl.Instance:SendCSRoleAuctionItem(self.my_want_big_id, self.my_want_sub_type)
	self.my_want_order_list = MarketWGData.Instance:GetOrderList(self.my_want_big_id, self.my_want_sub_type)
	self.my_want_color_list = MarketWGData.Instance:GetColorList(self.my_want_big_id, self.my_want_sub_type)
	self:FlushMyWantChildPanelActive()
	self:FlushMyWantOrderDrowDown()
	self:FlushMyWantColorDrowDown()
	self:FlushCanWantList()
end

-- 阶数筛选下拉框更变
function MarketView:OnMyWantOrderDropdownChange(index)
	self.my_want_order_dropdown_select = index
	self:FlushCanWantList()
end

-- 品质筛选下拉框更变
function MarketView:OnMyWantColorDropdownChange(index)
	self.my_want_color_dropdown_select = index
	self:FlushCanWantList()
end

-- 品质筛选下拉框更变
function MarketView:OnMyWantStarDropdownChange(index)
	self.star_dropdown_select = index + 1
	self.my_want_total_price = self:MyWantGetSuggessedPrice()
	self:FlushPublicPanel()
end

-- 点击搜索
function MarketView:OnClickMyWantSearch()
	self.my_want_search_name = self.node_list["my_want_search_input"].input_field.text
	self:FlushCanWantList()
end

-- 点击可求购列表item
function MarketView:OnClickCanWantIten(market_can_want_item)
	if self.select_can_want_auction_cfg == market_can_want_item.data then
		return
	end
	self.select_can_want_auction_cfg = market_can_want_item.data
	self.my_want_star_list = MarketWGData.Instance:GetStarList(self.select_can_want_auction_cfg.item_id)
	self.star_dropdown_select = 1
	self.my_want_total_price = self:MyWantGetSuggessedPrice()
	self.my_want_amount = 1
	self:FlushPublicPanel()
end

-- 获得推荐价格
function MarketView:MyWantGetSuggessedPrice()
	local star_level = self.my_want_star_list[self.star_dropdown_select]
	return MarketWGData.Instance:ConvertAuctionPrice(self.select_can_want_auction_cfg, star_level)
end

-- 点击发布
function MarketView:OnClickPublish()
	if self.select_can_want_auction_cfg then
		TipWGCtrl.Instance:OpenAlertTips(Language.Market.ConfirmWant, function()
			if not self.select_can_want_auction_cfg then
				return
			end
			local star_level = self.my_want_star_list[self.star_dropdown_select]
			MarketWGCtrl.Instance:SendAddWant(self.select_can_want_auction_cfg.item_id, star_level or 0, self.my_want_amount, self.my_want_total_price)
			self:OnClickReset()
		end)
	end
end

-- 点击重置
function MarketView:OnClickReset()
	self.my_want_total_price = self:MyWantGetSuggessedPrice()
	self.my_want_amount = 1
	self.star_dropdown_select = 1
	self:FlushPublicPanel()
end

---------------------市场求购面板求购清单列表item------------------
MarketCanWantItem = MarketCanWantItem or BaseClass(BaseRender)
function MarketCanWantItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function MarketCanWantItem:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function MarketCanWantItem:OnFlush()
	if self.data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		self.item_cell:SetData({item_id = self.data.item_id})
		-- 物品名称
		self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)

		-- 阶数
		self.node_list["order"].text.text = Language.Market.NameList1[item_cfg.order] --string.format(Language.Common.Level, item_cfg.limit_level)
	end
end

function MarketCanWantItem:ShowHighLight(is_select)
	self.node_list["high_light"]:SetActive(is_select)
end

-- 选择状态改变
function MarketCanWantItem:OnSelectChange(is_select)
	self:ShowHighLight(is_select)
end