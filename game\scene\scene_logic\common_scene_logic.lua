
CommonSceneLogic = CommonSceneLogic or BaseClass(BaseSceneLogic)

function CommonSceneLogic:__init()
	self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE,BindTool.Bind(self.ObjCreate,self))
end

function CommonSceneLogic:__delete()
	if nil ~= self.story then
		self.story:DeleteMe()
		self.story = nil
	end

	if self.obj_create_event then
		GlobalEventSystem:UnBind(self.obj_create_event)
		self.obj_create_event = nil
	end

end

function CommonSceneLogic:Enter(old_scene_type, new_scene_type)
	BaseSceneLogic.Enter(self, old_scene_type, new_scene_type)
	ActIvityHallWGCtrl.Instance:CheckDoHuSong()
	GuajiWGCtrl.Instance:OnCommonSceneEnter()
	local scene_id = Scene.Instance:GetSceneId()
	self.story = XinShouStorys.New(scene_id)
	if self.story:GetStoryNum() > 0 then
		RobertManager.Instance:Start()
	end
	if Scene.Instance:GetOldSceneId() ~= scene_id then
		self:ExcuteCallBack()
	else
	    self.main_role_create_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_CREATE,BindTool.Bind(self.ExcuteCallBack,self))
	end


	local mainui_ctrl = MainuiWGCtrl.Instance
	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	mainui_ctrl:AddInitCallBack(nil,function ()
		scene_id = Scene.Instance:GetSceneId()
		if bootybay_scene_id == scene_id then
			BootyBayWGCtrl.Instance:OpenBootybayFollowView()
			mainui_ctrl:SetTaskContents(false)
			mainui_ctrl:SetOtherContents(true)
			mainui_ctrl:SetMianUITopButtonStateTwo(true)
			mainui_ctrl:PlayTopButtonTween(false)
			-- mainui_ctrl:SetTaskButtonTrue()
			--mainui_ctrl:SetTaskPanel( false )
			mainui_ctrl:SetTeamBtnState(true)
			mainui_ctrl:SetFBNameState(false)

			if BootyBayWGData.Instance.wabao_type == WABAO_TYPE.WABAO_TYPE_TASK then
				BootyBayWGCtrl.Instance:SendTaskWaBao()
				GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.AutoWaBao)
			elseif BootyBayWGData.Instance.wabao_type == WABAO_TYPE.WABAO_TYPE_BAOTU then
				local item_data = BootyBayWGData.Instance:GetUseBaoTuInfo()
				BootyBayWGCtrl.Instance:SendBaoTuWaBao(item_data)
				GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.AutoWaBao)
			end
			return
		end

		BootyBayWGCtrl.Instance:CloseBootybayFollowView()
		BootyBayWGData.Instance:ClearUseBaoTuInfo()
		mainui_ctrl:SetMianUITopButtonStateTwo(true)
		-- mainui_ctrl:SetTaskContents(true)
		if not BossWGData.IsBossScene(Scene.Instance:GetSceneType()) then
			--mainui_ctrl:SetTaskPanel( true )
			mainui_ctrl:SetTaskContents(true)
			mainui_ctrl:SetOtherContents(false)
		end
		mainui_ctrl:SetTeamBtnState(true)
		mainui_ctrl:SetFBNameState(false)

		if MainuiWGData.Instance:GetTopShrinkIsClick() then
			mainui_ctrl:PlayTopButtonTween(false)
		end

		if IS_ON_CROSSSERVER then
			MainuiWGCtrl.Instance:ShowMainuiMenu(false)
		else
			MainuiWGCtrl.Instance:ShowMainuiMenu(true)
		end
    end)
end

function CommonSceneLogic:ObjCreate(obj)

end

function CommonSceneLogic:CharacterCanSelect(obj)
	return true
end

function CommonSceneLogic:ExcuteCallBack()

end

--退出
function CommonSceneLogic:Out(old_scene_type, new_scene_type)
	BaseSceneLogic.Out(self, old_scene_type, new_scene_type)

	ActivityWGCtrl.Instance:CloseMonsterInvadeCountDown()
	-- UiInstanceMgr.Instance:StopGuaJiTipsAction()
	if nil ~= self.kafu_delay then
		GlobalTimerQuest:CancelQuest(self.kafu_delay)
		self.kafu_delay = nil
	end

	if nil ~= self.main_role_create_event then
		GlobalEventSystem:UnBind(self.main_role_create_event)
		self.main_role_create_event = nil
	end
	RobertManager.Instance:Stop()
	TipWGCtrl.Instance:CloseEneterCommonSceneView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)

	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	local scene_id = Scene.Instance:GetSceneId()
	if bootybay_scene_id == scene_id then
		--切换成无目标
    	NewTeamWGCtrl.Instance:ChangNotGoalWhenOutFromOtherFb()
    	BootyBayWGCtrl.Instance:CloseBootybayFollowView()
	end
	-- MainuiWGCtrl.Instance:RemoveAllFbIcon()
	-- FuBenWGCtrl.Instance:CloseTaskFollow()

end

-- 获取传送门是否需要走自己的单独逻辑
function CommonSceneLogic:GetDoorNeedAloneLogic()
	local fun
	return fun
end

function CommonSceneLogic:IsMonsterEnemy(target_obj, main_role)
	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	local scene_id = Scene.Instance:GetSceneId()
	if bootybay_scene_id == scene_id and main_role ~= nil then
		local vo = main_role:GetVo()
		local target_vo = target_obj:GetVo()
		if vo ~= nil and vo.uuid ~= nil and target_vo ~= nil and target_vo.wabao_owner_uuid ~= nil then
			return vo.uuid["temp_low"] == target_vo.wabao_owner_uuid.temp_low and vo.uuid["temp_high"] == target_vo.wabao_owner_uuid.temp_high
		else
			return main_role:GetVo().uuid
		end
	end

	return true
end

-- 角色是否是敌人
function CommonSceneLogic:IsRoleEnemy(target_obj, main_role)
	main_role = main_role or Scene.Instance:GetMainRole()
	if nil == target_obj or nil == main_role or not target_obj:IsCharacter() then
		return false
	end

	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	local scene_id = Scene.Instance:GetSceneId()
	if bootybay_scene_id == scene_id then
		local flag = BootyBayWGCtrl.Instance:GetAutoWaBaoFlag()
		if flag then
			return false
		end

		return BaseSceneLogic.IsRoleEnemy(self,target_obj, main_role)
	end

	return BaseSceneLogic.IsRoleEnemy(self,target_obj, main_role)
end

function CommonSceneLogic:CanChangeAttackMode()
	return true
end

function CommonSceneLogic:GetNpcTaskNeedAloneLogic(cur_npc_id)
	local task_tab = {}

	local kf_3v3_other_cfg = KF3V3WGData.Instance:GetOtherCfg()
	--print_error("GetNpcTaskNeedAloneLogic>>>>>>>>", cur_npc_id, kf_3v3_other_cfg.npc_3v3)
	if cur_npc_id == kf_3v3_other_cfg.npc_3v3 then
		local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
		local talk_text = ""
		--活动开启判断（是否隐藏按钮）
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
		if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
			task_tab.hide_btn = 1
			talk_text = kf_3v3_other_cfg.npc_dialog_close
		else
			if is_in_zhandui then
				--显示在按钮上的倒计时
				task_tab.btn_time = 5
				task_tab.btn_text_format = Language.KuafuPVP.EnterPrepareTimeText
			end

			task_tab.hide_btn = 0
			talk_text = kf_3v3_other_cfg.npc_dialog_open
		end

		if is_in_zhandui then
			task_tab.btn_name = Language.KuafuPVP.BtnName_GoToWar
		else
			task_tab.hide_btn = 0
			talk_text = kf_3v3_other_cfg.npc_dialog_join
			task_tab.btn_name = Language.KuafuPVP.ViewName_JoinZhanDui
		end

		task_tab.npc_id = kf_3v3_other_cfg.npc_3v3
		local fun = function ()
			if is_in_zhandui then
				KF3V3WGCtrl.Instance:EnterPrepareScene()
			else
				ZhanDuiWGCtrl.Instance:OpenJoinView()
			end
		end
		task_tab.callback = fun
		--功能未开启
		local is_open_fun = FunOpen.Instance:GetFunIsOpened(FunName.KFPVP)
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if not is_open_fun or open_day < KF3V3WGData.Instance:GetOpenDay() then
			task_tab.hide_btn = 1
			talk_text = kf_3v3_other_cfg.npc_dialog_lock
			task_tab.callback = nil
		end
		task_tab.talk_text = talk_text
		--是否隐藏按钮下面倒计时
		task_tab.not_time = true
		--是否仅仅点击按钮才响应回调
		task_tab.only_click_btn_call_back = true
		return task_tab
	end

	local scene_id = Scene.Instance:GetSceneId()
	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
    if bootybay_scene_id == scene_id then
		local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
		if not is_accepted then
			local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
			task_tab.btn_name = Language.Task.accept_task
			task_tab.npc_id = other_cfg.npcid
			local fun = function ()
				BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_ACCEPT_TASK)
				GlobalTimerQuest:AddDelayTimer(function ()
					BootyBayWGCtrl.Instance:SendTaskWaBao()
				end, 1)
			end
			task_tab.callback = fun
			return task_tab
		else
			return nil
		end
	end

	return nil
end

-- 角色是否需要进行优先级的计算
function CommonSceneLogic:IsRoleNeedCalculatePriortiy()
	return true
end

-- 角色显示的优先级
function CommonSceneLogic:GetRoleVisiblePriortiy(role)
	local priortiy = BaseSceneLogic.GetRoleVisiblePriortiy(self, role)
	return priortiy
end

function CommonSceneLogic:GetFbSceneMonsterBossCfg()
	return nil
end
