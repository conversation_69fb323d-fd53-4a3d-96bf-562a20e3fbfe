require("game/tianshen_shuangsheng/tianshen_shuangsheng_wg_data")
require("game/tianshen_shuangsheng/tianshen_shuangsheng_view")
require("game/tianshen_shuangsheng/tianshen_shuangsheng_preview")

TianShenShuangShengWGCtrl = TianShenShuangShengWGCtrl or BaseClass(BaseWGCtrl)
function TianShenShuangShengWGCtrl:__init()
	if TianShenShuangShengWGCtrl.Instance then
		print_error("[TianShenShuangShengWGCtrl]:Attempt to create singleton twice!")
	end

	TianShenShuangShengWGCtrl.Instance = self
    self.data = TianShenShuangShengWGData.New()
    self.view = TianShenShuangShengView.New(GuideModuleName.TianShenShuangShengView)
	self.tianshen_shuangsheng_preview = TianShenShuangShengPreview.New()
	self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function TianShenShuangShengWGCtrl:__delete()
    self:UnRegisterAllEvents()

    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

	if self.tianshen_shuangsheng_preview then
		self.tianshen_shuangsheng_preview:DeleteMe()
		self.tianshen_shuangsheng_preview = nil
	end

    TianShenShuangShengWGCtrl.Instance = nil
end

function TianShenShuangShengWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTianshenAvatarInfo, "OnSCTianshenAvatarInfo")
	self:RegisterProtocol(SCTianshenAvatarAllInfo, "OnSCTianshenAvatarAllInfo")
end

function TianShenShuangShengWGCtrl:RegisterAllEvents()
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function TianShenShuangShengWGCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

--物品变化(这里需要更新红点)
function TianShenShuangShengWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if TianShenShuangShengWGData.Instance:CheckisActiveOrUpgradeItemId(change_item_id) then	---技能书物品变化刷新学习技能书红点
			TianShenShuangShengWGData.Instance:FlushAllTianShenRemind()
			self:FlushView()
			RemindManager.Instance:Fire(RemindName.TianShenShuangSheng)
		end
	end
end

-- 激活双生神灵
function TianShenShuangShengWGCtrl:SendOperateActiveAvatar(param_1)
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type24, param_1)
end

-- 升级双生神灵
function TianShenShuangShengWGCtrl:SendOperateUpgradeAvatar(param_1)
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type25, param_1)
end

-- 切换幻化
function TianShenShuangShengWGCtrl:SendOperateChangeAvatar(param_1, param_2, param_3)
	-- print_error("切换幻化", param_1, param_2, param_3)
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type26, param_1, param_2, param_3)
end

---返回天神双生的所有信息
function TianShenShuangShengWGCtrl:OnSCTianshenAvatarAllInfo(protocol)
	-- print_error("返回天神双生的所有信息", protocol)
	if protocol.avatar_list then
		self.data:FlushAllTianShenAvatar(protocol.avatar_list)
	end
	self:FlushView()
	RemindManager.Instance:Fire(RemindName.TianShenShuangSheng)
end

---返回天神双生单个信息
function TianShenShuangShengWGCtrl:OnSCTianshenAvatarInfo(protocol)
	-- print_error("返回天神双生单个信息", protocol)
	self.data:FlushOneTianShenAvatar(protocol.index, protocol.avatar_item)
	self:FlushView()
	RemindManager.Instance:Fire(RemindName.TianShenShuangSheng)
	--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_Info, "flush_display")
	GlobalEventSystem:Fire(SkillEventType.FLUSH_SKILL_LIST)		-- 刷新主角面的技能图标
end

-- 刷新界面
function TianShenShuangShengWGCtrl:FlushView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 打开界面
function TianShenShuangShengWGCtrl:OpenView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
    self.view:Open()
end

-- 打开展示详情
function TianShenShuangShengWGCtrl:OpenPreView(preview_data)
	self.tianshen_shuangsheng_preview:SetData(preview_data)
end
