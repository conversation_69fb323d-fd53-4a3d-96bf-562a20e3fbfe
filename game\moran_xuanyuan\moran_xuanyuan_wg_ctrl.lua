require("game/moran_xuanyuan/moran_xuanyuan_wg_data")
require("game/moran_xuanyuan/moran_xuanyuan_view")

MoRanXuanYuanWGCtrl = MoRanXuanYuanWGCtrl or BaseClass(BaseWGCtrl)
function MoRanXuanYuanWGCtrl:__init()
	if MoRanXuanYuanWGCtrl.Instance then
		ErrorLog("[MoRanXuanYuanWGCtrl] Attemp to create a singleton twice !")
	end
	MoRanXuanYuanWGCtrl.Instance = self
    self.view = MoRanXuanYuanView.New(GuideModuleName.MoRanXuanYuanView)
	self.data = MoRanXuanYuanWGData.New()


    self:RegisterAllProtocols()
    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
end

function MoRanXuanYuanWGCtrl:__delete()
    MoRanXuanYuanWGCtrl.Instance = nil

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function MoRanXuanYuanWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCRAMoRanXuanYuanInfo,'OnSCRAMoRanXuanYuanInfo')
    self:RegisterProtocol(SCRAMoRanXuanYuanRecordListInfo,'OnSCRAMoRanXuanYuanRecordListInfo')
    self:RegisterProtocol(SCRAMoRanXuanYuanDrawRewardInfo,'OnSCRAMoRanXuanYuanDrawRewardInfo')
    self:RegisterProtocol(SCRAMoRanXuanYuanBaoDiRewardDrawInfo,'OnSCRAMoRanXuanYuanBaoDiRewardDrawInfo')
end

function MoRanXuanYuanWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE  then
        local check_list = self.data:GetItemDataChangeList()
        for i, v in pairs(check_list) do
            if v == change_item_id then
                self.view:Flush()
                RemindManager.Instance:Fire(RemindName.MoRanXuanYuan)
            end
        end
    end
end

--请求
function MoRanXuanYuanWGCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_MoRan_XuanYuan,
		opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function MoRanXuanYuanWGCtrl:OnSCRAMoRanXuanYuanInfo(protocol)
    self.data:SetInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.MoRanXuanYuan)
end

function MoRanXuanYuanWGCtrl:OnSCRAMoRanXuanYuanRecordListInfo(protocol)
   -- print_error("====Record=====",protocol)
    self.data:SetRecord(protocol.record_list)
    if self.view:IsOpen() then
        self.view:Flush()
    end
end

function MoRanXuanYuanWGCtrl:OnSCRAMoRanXuanYuanDrawRewardInfo(protocol)
   -- print_error("====Draw=====",protocol)
    local btn_index = self.data:CacheOrGetDrawIndex()
    if not btn_index then
        return
    end

    local consume_cfg = self.data:GetConsumeCfg()
    local btn_cfg = consume_cfg[btn_index]
    if not btn_cfg then
        return
    end

    local consume_num = btn_cfg.yanhua_item.num

    local str = string.format(Language.MoRanXuanYuanView.BtnStr, btn_cfg.onekey_lotto_num)
    local ok_func = function ()
        if self.view:IsOpen() then
            self.view:Flush()
        end

        self:ClickUse(btn_index, function()
            self:SendReq(RA_MORANXUANYUAN_OP_TYPE.BUY, btn_cfg.onekey_lotto_num, 1)
        end)
    end

    local data_list = self.data:CalDrawRewardList(protocol)
    local other_info = {}
    other_info.again_text = str
    other_info.stuff_id = btn_cfg.yanhua_item.item_id
    other_info.times = consume_num
    other_info.spend = consume_cfg[1].consume_count
    TipWGCtrl.Instance:ShowGetCommonReward(data_list, ok_func, other_info, false)
end

function MoRanXuanYuanWGCtrl:OnSCRAMoRanXuanYuanBaoDiRewardDrawInfo(protocol)
    --print_error("====BaoDi=====",protocol)
    self.data:SaveDrawInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.MoRanXuanYuan)
end


function MoRanXuanYuanWGCtrl:ClickUse(index, func)
    --数量检测
   local cfg = self.data:GetConsumeCfg()
   local num = ItemWGData.Instance:GetItemNumInBagById(cfg[index].yanhua_item.item_id)

   --不足弹窗
   if num < cfg[index].yanhua_item.num then
       if not self.alert then
           self.alert = Alert.New()
       end
       self.alert:ClearCheckHook()
       self.alert:SetShowCheckBox(true, "moran_xuanyuan_fresh")
       self.alert:SetCheckBoxDefaultSelect(false)
       local item_cfg = ItemWGData.Instance:GetItemConfig(cfg[index].yanhua_item.item_id)
       local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
       local cost = cfg[1].consume_count * (cfg[index].yanhua_item.num - num)
       local str = string.format(Language.MoRanXuanYuanView.CostStr, name, cost)

       self.alert:SetLableString(str)
       self.alert:SetOkFunc(func)
       self.alert:Open()
   else
       --使用
       func()
   end
end