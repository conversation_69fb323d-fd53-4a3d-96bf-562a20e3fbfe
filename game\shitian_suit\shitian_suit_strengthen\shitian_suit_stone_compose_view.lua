ShiTianSuitStoneComposeView = ShiTianSuitStoneComposeView or BaseClass(SafeBaseView)

function ShiTianSuitStoneComposeView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/shitian_suit_ui_prefab", "layout_stone_compose")
end

function ShiTianSuitStoneComposeView:LoadCallBack()
    self.still_need_price = 999999999
    XUI.AddClickEventListener(self.node_list.up_btn, BindTool.Bind(self.OnClickUpBtn, self))

    if not self.stuff_item_list then
        self.stuff_item_list = AsyncListView.New(ItemCell, self.node_list.stuff_list)
    end

    self.cur_stone_compose_item = ShiTianStoneComposeRender.New(self.node_list.stone1)
    self.next_stone_compose_item = ShiTianStoneComposeRender.New(self.node_list.stone2)

	ShiTianSuitStrengthenWGCtrl.Instance:InitBaoShiUpGradeAlertTips(BindTool.Bind(self.AlertOkFnc, self))
end

function ShiTianSuitStoneComposeView:ReleaseCallBack()
    if self.stuff_item_list then
        self.stuff_item_list:DeleteMe()
        self.stuff_item_list = nil
    end

    if self.cur_stone_compose_item then
        self.cur_stone_compose_item:DeleteMe()
        self.cur_stone_compose_item = nil
    end

    if self.next_stone_compose_item then
        self.next_stone_compose_item:DeleteMe()
        self.next_stone_compose_item = nil
    end

	self.enough_price = nil
    self.still_need_price = nil
end

function ShiTianSuitStoneComposeView:OnFlush()
    if not self.show_data then
        return
    end

    local stsuit_strengthen_data = ShiTianSuitStrengthenWGData.Instance
    local stone_level_cfg = stsuit_strengthen_data:GetStoneComposeCfg(self.show_data.ep_stone_id)
    if not stone_level_cfg then
        return
    end

    local cur_stone_id = stone_level_cfg.old_shitianstone_item_id
    local next_stone_id = stone_level_cfg.new_shitianstone_item_id

    self.still_need_price = stsuit_strengthen_data:ComputBaoShiUpgradePrice(cur_stone_id)
	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	local bind_gold = 0--GameVoManager.Instance:GetMainRoleVo().bind_gold
	self.enough_price = role_gold + bind_gold >= self.still_need_price
    local need_cost_gold = self.still_need_price > 0

	local need_stone_desc = stsuit_strengthen_data:CalcUpgradeNeedStoneStr(cur_stone_id)
	local cost_baoshi = ""
	if need_stone_desc then
		local and_str = need_cost_gold and Language.Equip.And or ""
		cost_baoshi = and_str .. need_stone_desc
	end

	local tips_str = ""
	if need_cost_gold then
		tips_str = string.format(Language.Equip.TipsContent1, self.still_need_price, cost_baoshi)
	else
		tips_str = string.format(Language.Equip.TipsContent0, cost_baoshi)
	end

	ShiTianSuitStrengthenWGCtrl.Instance:SetBaoShiUpGradeAlertTips(need_cost_gold, tips_str)

    local stone_list = ItemWGData.Instance:GetShiTianStoneItemList(self.show_data.suit_seq, self.show_data.stone_type)
	table.sort(stone_list, SortTools.KeyLowerSorter("item_id"))
    self.stuff_item_list:SetDataList(stone_list)
    self.node_list.empty_tips:SetActive(IsEmptyTable(stone_list))

    self.cur_stone_compose_item:SetData(cur_stone_id)
    self.next_stone_compose_item:SetData(next_stone_id)

    self.node_list.normal_txt:SetActive(not need_cost_gold)
    self.node_list.cost:SetActive(need_cost_gold)
    self.node_list.cost_txt.text.text = self.still_need_price
end

function ShiTianSuitStoneComposeView:SetShowData(data)
    self.show_data = data
end

function ShiTianSuitStoneComposeView:OnClickUpBtn()
    ShiTianSuitStrengthenWGCtrl.Instance:OpenBaoShiUpGradeAlertTips(self.still_need_price > 0)
end

function ShiTianSuitStoneComposeView:AlertOkFnc()
    if not self.show_data then
        return
    end

	if self.enough_price then
        local use_gold = self.still_need_price > 0 and 1 or 0
        ShiTianSuitStrengthenWGCtrl.Instance:SendCSShiTianStrengthenReq(SHITIANSTONE_OPERA_TYPE.STONE_LEVEL_UP, self.show_data.suit_seq, self.show_data.part, self.show_data.slot, use_gold)
		self:Close()
	else
		self:Close()
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end


-------------- 宝石合成render --------------
ShiTianStoneComposeRender = ShiTianStoneComposeRender or BaseClass(BaseRender)

function ShiTianStoneComposeRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
end

function ShiTianStoneComposeRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function ShiTianStoneComposeRender:OnFlush()
    if not self.data then
        return
    end

    local stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(self.data)
    if not stone_cfg then
        return
    end

    self.item_cell:SetData({item_id = stone_cfg.item_id})
    local attr_list = EquipWGData.GetSortAttrListByTypeCfg(stone_cfg, "attr_id", "attr_val", 1, 2)
    for i = 1, #attr_list do
        local attr = attr_list[i]
        self.node_list["attr" .. i]:SetActive(not IsEmptyTable(attr))
        if attr then
            self.node_list["attr" .. i].text.text = ShiTianSuitStrengthenWGData.GetStoneDescForCfg(attr.attr_str, attr.attr_value)
        end
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data)
    if item_cfg then
        local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        self.node_list.stone_name.text.text = name_str
    end
end