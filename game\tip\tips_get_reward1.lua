TipsGetRewardView = TipsGetRewardView or BaseClass(SafeBaseView)

local ani_ten_flag_t = {}
local ani_ten_count = 1

local ANI_SPEED = 0.15
local LeftPadding = 30
local DelayIndex = 0
local OneCellWidth = 115
local DelaySpeed = 0.13
local TweenSpeed = 0.5

local Sort_Type = {
	[GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
	[GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
	[GameEnum.ITEM_BIGTYPE_GIF] = 8,
	[GameEnum.ITEM_BIGTYPE_OTHER] = 7,
	[GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}

function TipsGetRewardView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true)
	self.view_name = "TipsGetRewardView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_gift_result")
	self.datachange_callback = BindTool.Bind1(self.OnItemDataChange, self)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function TipsGetRewardView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_item_id == self.item_id then
		self:RefreshItemNum()
	end
end

function TipsGetRewardView:ReleaseCallBack()
	if nil ~= self.rect_list then
		self.rect_list:DeleteMe()
		self.rect_list = nil
	end

	if self.single_list then
		for i,v in ipairs(self.single_list) do
			v:DeleteMe()
		end
		self.single_list = nil
	end

	if self.yinji_list then
		for i,v in ipairs(self.yinji_list) do
			v:DeleteMe()
		end
		self.yinji_list = nil
	end

	self:CleanDelayTime()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TipsGetRewardView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function TipsGetRewardView:CleanDelayTime()
	if self.ten_time_quest then
		GlobalTimerQuest:CancelQuest(self.ten_time_quest)
		self.ten_time_quest = nil
	end

	if self.delay_time_1 then
		GlobalTimerQuest:CancelQuest(self.delay_time_1)
		self.delay_time_1 = nil
	end

	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end

	self:CleanDelayTime2()
end

function TipsGetRewardView:CleanDelayTime2()
	if self.delay_time_2 then
		GlobalTimerQuest:CancelQuest(self.delay_time_2)
		self.delay_time_2 = nil
	end
end

function TipsGetRewardView:CleanDelayAnim()
	if self.single_list then
		for k,v in pairs(self.single_list) do
		    v:CleanTween()
			v:SetAlpha(false)
		end
	end

	if self.rect_list then
		local list = self.rect_list:GetSortCellList()
		if not IsEmptyTable(list) then
			for k,v in ipairs(list) do
			    v:CleanDelayAnim()
				v:SetAlpha(false)
			end
		end
		ani_ten_flag_t = {}
	end
end

function TipsGetRewardView:CloseCallBack()
	self.no_need_sort = nil
	self:CleanDelayTime()
	self:CleanDelayAnim()
end

function TipsGetRewardView:LoadCallBack()
	self:InitXunBaoZhanshi()
	self:RegisterEvent()
	self:RefreshItemNum()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TipsGetRewardView, self.get_guide_ui_event)
end

-- 注册按钮事件
function TipsGetRewardView:RegisterEvent()
	self.node_list.btn_chouqu_again:SetActive(false)
	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind1(self.OnClickClose, self))
	XUI.AddClickEventListener(self.node_list.btn_chouqu_again, BindTool.Bind1(self.OnQuchuAgain, self))
end

-- 再来一次
function TipsGetRewardView:OnQuchuAgain()
	if self.again_func then
		self.again_func()
		return
	end
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
	if item_num > 0 then
		local bag_index = ItemWGData.Instance:GetItemIndex(self.item_id)
		BagWGCtrl.Instance:SendUseItem(bag_index)
	else
		self:Close()
	end
end

function TipsGetRewardView:OnClickClose()
	if self.sure_func then
		self.sure_func()
	end
	
	self:Close()
end

--初始化格子
function TipsGetRewardView:InitXunBaoZhanshi()
	self.rect_list = AsyncListView.New(GiftRewardCell, self.node_list.ph_zhanshii_cell)

	if not self.single_list then
		self.single_list = {}
		for i=1,10 do
			self.single_list[i] = GiftRewardSingleCell.New(self.node_list['single_cell_' .. i])
		end
	end

	if not self.yinji_list then
		self.yinji_list = {}
		for i=1,10 do
			self.yinji_list[i] = GiftRewardSingleCell.New(self.node_list['yinji_cell_' .. i])
		end
	end
end

-- other_info = {again_btn = true}
function TipsGetRewardView:SetData(item_id, id_list, again_func, other_info, no_need_sort, is_yinji, sure_func)
	self.item_id = item_id
	self.id_list = id_list
	self.again_func = again_func
	self.other_info = other_info
	self.no_need_sort = no_need_sort
	self.is_yinji = is_yinji
	self.sure_func = sure_func
end

function TipsGetRewardView:SortItem(item_list)
	if self.no_need_sort then
		return item_list
	end

	local item_cfg,item_type
	for i,v in ipairs(item_list) do
		item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.color = item_cfg and item_cfg.color or 0
		v.item_type = Sort_Type[item_type] or 1
		v.is_bind = item_cfg and item_cfg.isbind or 0
	end
	
	SortTools.SortDesc(item_list,"color", "item_type")
	return item_list
end

--刷新数据
function TipsGetRewardView:OnFlush(param_t)
	self:RefreshOtherSetting()
	self:RefreshView()
end

function TipsGetRewardView:RefreshView()
	local gift_info
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.item_id)
	if self.id_list and #self.id_list > 0 then
		gift_info = self.id_list
	else 							--固定礼包
		gift_info = ItemWGData.Instance:GetItemListInGift(self.item_id)
		if item_cfg and item_cfg.equip_star > 0 then
			gift_info = ItemWGData.Instance:GetResetGiftConfig(self.item_id)
		end

		if gift_info == nil then
			return
		end
		--print_error("固定礼包", gift_info)
	end
	gift_info = self:SortItem(gift_info)

	self:CleanDelayTime()

	self.node_list.list_mask:SetActive(false)
	self.node_list.ph_zhanshii_cell:SetActive(#gift_info > 10 and not self.is_yinji)
	self.node_list.single_list:SetActive(#gift_info <= 10)
	self.node_list.yinji_list:SetActive(#gift_info == 10 and self.is_yinji)
	if #gift_info > 10 then
		DelayIndex = (self.node_list.list_mask.rect.rect.width - LeftPadding) / OneCellWidth + 1
		ani_ten_flag_t = {}
		
		self.rect_list:SetDataList(gift_info,0)
		self.rect_list:JumpToTop()
		self.node_list.list_mask:SetActive(true)

		self.delay_time_1 = GlobalTimerQuest:AddDelayTimer(function ()

			local time = 0.25 * (#gift_info - 10)
			self.m_tween = self.node_list.ph_zhanshii_cell.scroll_rect:DoHorizontalPosition(0, 1, time, nil)
			self.m_tween:SetEase(DG.Tweening.Ease.Linear)
			self.m_tween:OnComplete(function ()
				self.m_tween = nil
				self.node_list.list_mask:SetActive(false)
			end)
		end, DelayIndex * DelaySpeed)

		return
	end

	if self.is_yinji and #gift_info == 10 then
		self.node_list.single_list:SetActive(false)
		for i,v in ipairs(self.yinji_list) do
			v:SetVisible(gift_info[i] ~= nil)
			v:SetAlpha(false)
			v:SetData(gift_info[i])
		end

		self.tween_index = 1
		self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
			if self.tween_index > #self.yinji_list then
				return 
			end

			self.yinji_list[self.tween_index]:DoAnim()
			self.yinji_list[self.tween_index]:SetAlpha(true)
			self.tween_index = self.tween_index + 1

		end, ANI_SPEED, #gift_info)
	else
		self.node_list.single_list:SetActive(true)
		for i,v in ipairs(self.single_list) do
			v:SetVisible(gift_info[i] ~= nil)
			v:SetAlpha(false)
			v:SetData(gift_info[i])
		end

		self.tween_index = 1
		self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
			if self.tween_index > #self.single_list then
				return 
			end

			self.single_list[self.tween_index]:DoAnim()
			self.single_list[self.tween_index]:SetAlpha(true)
			self.tween_index = self.tween_index + 1

		end, ANI_SPEED, #gift_info)
	end
end

function TipsGetRewardView:RefreshOtherSetting()
	self.node_list["again_btn_text"].text.text = Language.TreasureHunt.BtnText[1]
	local other_info = self.other_info
	local image = "a3_ty_btn_4"
	local color = COLOR3B.BLACK
	if other_info then
		image = other_info.again_btn and image or "a3_ty_btn_5"
		color = other_info.again_btn and color or COLOR3B.BLACK
		self.node_list.btn_chouqu_again:SetActive(other_info.again_btn)
		if other_info.again_text then
			self.node_list["again_btn_text"].text.text = other_info.again_text
		end
	else
		color = COLOR3B.BLACK
		image = "a3_ty_btn_5"
		self.node_list.btn_chouqu_again:SetActive(false)
		self.node_list["again_btn_text"].text.text = ""
	end

	self.node_list.btn_sure.image:LoadSprite(ResPath.GetCommonButton(image))
	self.node_list.btn_text.text.color = Str2C3b(color)

	if self.sure_func then
		self.node_list.btn_sure:SetActive(true)
	else
		self.node_list.btn_sure:SetActive(false)
	end
end

function TipsGetRewardView:RefreshItemNum()
	if not self.item_id then
		return
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.item_id)
	if item_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
		self.node_list.bottom_text.text.text = ToColorStr(item_cfg.name .. "x" .. item_num, ITEM_COLOR[item_cfg.color])
	end
end

function TipsGetRewardView:GetGuideUiCallBack(ui_name, ui_param)
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

----------------------------------------------------------------------------------------------
GiftRewardSingleCell = GiftRewardSingleCell or BaseClass(BaseRender)

function GiftRewardSingleCell:LoadCallBack()
	self.base_cell = ItemCell.New(self.node_list["cell"])
	self.base_cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
end

function GiftRewardSingleCell:ReleaseCallBack()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end

	self:CleanTween()
end

function GiftRewardSingleCell:SetAlpha(value)
	self.view.canvas_group.alpha = value and 1 or 0
end

function GiftRewardSingleCell:CleanTween()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
end

local scale1 = Vector3(1.4, 1.4, 1) --大小1
local scale2 = Vector3(1, 1, 1) --大小1
local effect_name = {

}
function GiftRewardSingleCell:DoAnim()
	self:CleanTween()
	self.view.transform.localScale = scale1

    local scale_tween_2 = self.view.rect:DOScale(scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InOutBack)

    self.sequence = DG.Tweening.DOTween.Sequence()

    self.sequence:Append(scale_tween_2)
    self.sequence:AppendCallback(function ()
		local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    	local eff_level = cfg.color

		ani_ten_flag_t[#ani_ten_flag_t + 1] = true

		if not effect_name[eff_level] then
			self.node_list.effect_attach:SetActive(false)
    		return
    	end

		local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])
        if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
	        self.node_list.effect_attach.game_obj_attach.BundleName = nil
       		self.node_list.effect_attach.game_obj_attach.AssetName = nil
		end
		self.node_list.effect_attach:SetActive(true)
		self.node_list.effect_attach:ChangeAsset(bundle,asset)

    end)
end

function GiftRewardSingleCell:FlushEffect()
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local eff_level = cfg.color

	if not effect_name[eff_level] then
		self.node_list.effect_attach:SetActive(false)
    	return
	end

	local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])
    if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
	    self.node_list.effect_attach.game_obj_attach.BundleName = nil
       	self.node_list.effect_attach.game_obj_attach.AssetName = nil
	end

	self.node_list.effect_attach:SetActive(true)
	self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function GiftRewardSingleCell:OnFlush()
	local data = self.data
	if data and ShenShouWGData.Instance:GetIsShenShouEquip(data.item_id) then
		local cfg = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)
		data.num = self.data.num
		data.item_id = self.data.item_id
		data.star_count = cfg and cfg.star or 0 		--神兽装备的初始星数
	end

	if self.data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_cfg then
			self.node_list.cell_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		else
			self.node_list.cell_name.text.text = ""
		end
	end

	self.node_list.extra_flag:SetActive(self.data and self.data.is_extra)
	self.base_cell:SetData(self.data)

end

---------------------- GiftRewardCell --------------------------------
GiftRewardCell = GiftRewardCell or BaseClass(GiftRewardSingleCell)

function GiftRewardCell:ReleaseCallBack()
	self:CleanDelayTime()
end

function GiftRewardCell:CleanDelayTime()
	if self.delay_tween then
		GlobalTimerQuest:CancelQuest(self.delay_tween)
		self.delay_tween = nil
	end
end

function GiftRewardCell:CleanDelayAnim()
	self:CleanTween()
	self:CleanDelayTime()
end

function GiftRewardCell:OnFlush()
	self.base_cell:SetData(self.data)

	self:CleanDelayAnim()

	if not ani_ten_flag_t[self.index] then
		self:SetAlpha(false)
		if self.index <= DelayIndex then
			self.delay_tween = GlobalTimerQuest:AddDelayTimer(function ()
				self:SetAlpha(true)
				self:DoAnim()
			end,(self.index-1)*0.15)
		else
			self.delay_tween = GlobalTimerQuest:AddDelayTimer(function ()
				self:SetAlpha(true)
				self:DoAnim()
			end,0.5)
		end
	else
		self:SetAlpha(true)
		self:FlushEffect()
	end

	if self.data and self.node_list.cell_name then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_cfg then
			self.node_list.cell_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		else
			self.node_list.cell_name.text.text = ""
		end
	end
end
