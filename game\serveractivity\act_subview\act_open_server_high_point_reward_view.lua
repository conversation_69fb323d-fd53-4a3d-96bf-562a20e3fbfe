ServerHighPointRewardView = ServerHighPointRewardView or BaseClass(SafeBaseView)
function ServerHighPointRewardView:__init()
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "city_info_score_panel")
	self:SetMaskBg(true)
	self.view_name = "ServerHighPointRewardView"
end

function ServerHighPointRewardView:LoadCallBack()
	self.reward_list = AsyncListView.New(ServerHighPointRwardRender, self.node_list["ph_reward_show"])
end

function ServerHighPointRewardView:ReleaseCallBack()
	if nil ~= self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function ServerHighPointRewardView:OnFlush()
	local reward_data_list = OpenServerAssistWGData.Instance:GetHighPointRewardList(true)
	self.reward_list:SetDataList(reward_data_list)
	local cur_hp = OpenServerAssistWGData.Instance:GetHighPointCount()
	self.node_list["total_score"].text.text = string.format(Language.Activity.AllScoreText, cur_hp)
end

-------------------------------------------奖励进度.------------------------------------------------
ServerHighPointRwardRender = ServerHighPointRwardRender or BaseClass(BaseRender)
function ServerHighPointRwardRender:ReleaseCallBack()
	if self.rewarditem_cells then
		for k, v in pairs(self.rewarditem_cells) do
			v:DeleteMe()
		end
		self.rewarditem_cells = nil
	end
end

function ServerHighPointRwardRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	if nil == self.rewarditem_cells then
		self.rewarditem_cells = {}
		for i = 1, 6 do
			self.rewarditem_cells[i] = ItemCell.New(self.node_list["cell_list"])
		end
	end
end

function ServerHighPointRwardRender:OnFlush()
	if not self.data then return end
	local cell_count = 0
	for i = 1, 6 do
		if self.data.reward_list[i - 1] then
			self.rewarditem_cells[i]:SetData(self.data.reward_list[i - 1])
			self.rewarditem_cells[i]:SetVisible(true)
			cell_count = cell_count + 1
		else
			self.rewarditem_cells[i]:SetVisible(false)
		end
	end

	self.node_list["lbl_lscs"].text.text = string.format(Language.Activity.CurScoreText, self.data.need_hp)
	self.node_list["btn_lingqu"]:SetActive(self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)
	self.node_list["image_wdc"]:SetActive(self.data.get_state == REWARD_STATE_TYPE.UNDONE)
	self.node_list["image_ylq"]:SetActive(self.data.get_state == REWARD_STATE_TYPE.FINISH)
	self.node_list.scroll_rect.scroll_rect.enabled = cell_count > 2
end

function ServerHighPointRwardRender:OnClickLingQu()
	if IsEmptyTable(self.data) then
		return
	end

	if self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH then
		OpenServerAssistWGCtrl.Instance:SendRandActivityRequest(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT,
			LOGIN_LUXURY_GIFT_OP.FETCH_REWARD, self.data.reward_grade)
		TipWGCtrl.Instance:ShowGetReward(nil, self.data.reward_list)
	end
end
