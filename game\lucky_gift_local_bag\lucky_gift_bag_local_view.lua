LuckyGiftBagLocalView = LuckyGiftBagLocalView or BaseClass(SafeBaseView)

local grade_reward_item_width = 352

function LuckyGiftBagLocalView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_gift_bag_local_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function LuckyGiftBagLocalView:ReleaseCallBack()
	self:CleanRefreshTime()

	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end

	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

	if self.lucky_grade_reward_list then
		self.lucky_grade_reward_list:DeleteMe()
		self.lucky_grade_reward_list = nil
	end

	if self.final_reward_item then
		self.final_reward_item:DeleteMe()
		self.final_reward_item = nil
	end

	if self.lucky_gift_bag_list then
		for k, v in pairs(self.lucky_gift_bag_list) do
            v:DeleteMe()
		end

		self.lucky_gift_bag_list = nil
	end

    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.delay_play_draw_idle3 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle3)
		self.delay_play_draw_idle3 = nil
	end

	if self.delay_play_draw_idle4 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle4)
		self.delay_play_draw_idle4 = nil
	end

	if self.operate_show_tween1 then
		self.operate_show_tween1:Kill()
		self.operate_show_tween1 = nil
	end

	if self.operate_show_tween2 then
		self.operate_show_tween2:Kill()
		self.operate_show_tween2 = nil
	end

	if self.operate_show_tween3 then
		self.operate_show_tween3:Kill()
		self.operate_show_tween3 = nil
	end

	if self.effect_end_callback then
		self.effect_end_callback = nil
	end
end

function LuckyGiftBagLocalView:CloseCallBack()
	if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.delay_play_draw_idle3 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle3)
		self.delay_play_draw_idle3 = nil
	end

    if self.delay_play_draw_idle4 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle4)
		self.delay_play_draw_idle4 = nil
	end

	if self.operate_show_tween then
		self.operate_show_tween:Kill()
		self.operate_show_tween = nil
	end

	if self.effect_end_callback then
		self.effect_end_callback = nil
	end

	if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
end

function LuckyGiftBagLocalView:OpenCallBack()
	LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_INFO)
	LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_BASE_INFO)
end

function LuckyGiftBagLocalView:ShowIndexCallBack()
	self:InitTweenRoot()
	self:DoViewTween()
end

function LuckyGiftBagLocalView:InitTweenRoot()
    RectTransform.SetAnchoredPositionXY(self.node_list.right_tween_root.rect, 1000, 0)
    RectTransform.SetAnchoredPositionXY(self.node_list.reward_tween_root.rect, 0, -300)
	self.node_list.btn_canvas_group.canvas_group.alpha = 0
	self.node_list.capability.canvas_group.alpha = 0
end

function LuckyGiftBagLocalView:DoViewTween()
	local tween_info = UITween_CONSTS.LuckyGiftBag

	local delay_func = function()
		self.node_list.btn_canvas_group.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
		self.node_list.capability.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
	end

	self.node_list.reward_tween_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
    local right_tween =  self.node_list.right_tween_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
	right_tween:OnComplete(delay_func)
end

function LuckyGiftBagLocalView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.LuckyGiftBag.Title

	if nil == self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if nil == self.lucky_grade_reward_list then
		self.lucky_grade_reward_list = AsyncListView.New(LuckyLocalGradeRewardRender, self.node_list.reward_item_list)
		self.node_list.reward_item_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnScrollRectValueChange, self))
	end

	if nil == self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.reward_item_pos)
	end

	self.lucky_gift_bag_list = {}
    for i = 1, 4 do
        self.lucky_gift_bag_list[i] = LuckyGiftBagLocalItemRender.New(self.node_list.lucky_gift_bag_list:FindObj("lucky_gift_bag_item" .. i))
    end

	-- 创建货币栏
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
		show_gold = true, show_bind_gold = false,
		show_coin = false, show_volume = false,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	RectTransform.SetAnchoredPositionXY(self.node_list["money_tabar_pos"].rect, -430, -33)

	self:FlushActiveRefreshTime()
	XUI.AddClickEventListener(self.node_list.btn_record, BindTool.Bind(self.OnRecordBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnClickTipsBtn, self))
	XUI.AddClickEventListener(self.node_list.daily_reward_btn, BindTool.Bind(self.OnDailyRewardBtnClick, self))
	-- 2021.12.22 策划觉得这个功能影响大佬留存，暂时屏蔽入口，功能保留
 	-- XUI.AddClickEventListener(self.node_list.btn_luckyvlaue_rank, BindTool.Bind(self.OnLuckyValueRankBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_send_lucky_value, BindTool.Bind(self.OnSendLuckyValueBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_through_train, BindTool.Bind(self.OnThroughTrainBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_super_double, BindTool.Bind(self.OnSuperDoubleBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_hatch_fight_mount, BindTool.Bind(self.OnHatchFightMount, self))

	 local super_double_state = LuckyGiftBagLocalWgData.Instance:GetOtherCfg().is_show_lucky_double == 1
	 --self.node_list.btn_super_double:SetActive(super_double_state)

	 self.model_display_id = nil

	 self.node_list["skip_spine_check"].button:AddClickListener(BindTool.Bind(self.OnClickSkipSpine, self))
end

function LuckyGiftBagLocalView:OnFlush(param_t)
	local model_data = LuckyGiftBagLocalWgData.Instance:GetCurrentRateShow()
	if not IsEmptyTable(model_data) and model_data.model_show_itemid then
		local capability = ItemShowWGData.CalculateCapability(model_data.model_show_itemid, true)
		self.node_list.cap_value.text.text = capability
		if self.reward_item then
			self.reward_item:SetData({item_id = model_data.model_show_itemid, num = 1, is_bind = 1})
		end
	end

	local get_rate_count, rate_count =  LuckyGiftBagLocalWgData.Instance:GetRewardProgress()
	if get_rate_count and rate_count then
		self.node_list.reward_item_flag:SetActive(rate_count <= get_rate_count)
		self.node_list.reward_progress_text.text.text = string.format(Language.LuckyGiftBag.LuckyGiftBagLocalRewardProgress, rate_count - get_rate_count, rate_count)
	end

	local reward_pool_data = LuckyGiftBagLocalWgData.Instance:GetRewardPoolInfo()
	if not IsEmptyTable(reward_pool_data) then
		for i = 1, 4 do
			if i <= #reward_pool_data then
				self.node_list.lucky_gift_bag_list:FindObj("lucky_gift_bag_item" .. i):SetActive(true)
				self.lucky_gift_bag_list[i]:SetData(reward_pool_data[i])
				self.lucky_gift_bag_list[i]:SetIndex(i)
			else
				self.node_list.lucky_gift_bag_list:FindObj("lucky_gift_bag_item" .. i):SetActive(false)
			end
		end
	end

	self:FlushModel()

	local lucky_value = LuckyGiftBagLocalWgData.Instance:GetLickyValue()
	self.node_list.lucky_value.text.text = lucky_value --string.format(Language.LuckyGiftBag.MyLuckyValue, ToColorStr(lucky_value, COLOR3B.GREEN))
	self.node_list.daily_reward_remind:SetActive(LuckyGiftBagLocalWgData.Instance:GetDailyRewardRemind() ~= 0)
	--self.node_list.btn_hatch_fight_mount:SetActive(model_data.show_fight_icon ~= 0)

	local data_list, start_reward, final_reward_data, slider_value = LuckyGiftBagLocalWgData.Instance:GetLuckyGradeRewardInfo()
	self.lucky_grade_reward_list:SetDataList(data_list)
	self:FlushFinalLuckyGradeReward(final_reward_data)
	self.node_list.slider.slider.value = slider_value

	self.node_list.ads_desc.text.text = Language.LuckyGiftBag.desc1
end

function LuckyGiftBagLocalView:FlushActiveRefreshTime()
	local close_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.CROSS_ACTIVITY_LUCKYGIFTBAGLOCAL)
	--local sever_time = TimeWGCtrl.Instance:GetServerTime()
	--local refresh_time = TimeWGCtrl.Instance:NowDayTimeEnd(sever_time)- sever_time
	local interval = 1
	self:CleanRefreshTime()
	self.refresh_time = CountDown.Instance:AddCountDown(close_time, interval,
	function(elapse_time, total_time)
		close_time = total_time - elapse_time
		if self.node_list.lucky_value_reset_time then
			self.node_list.lucky_value_reset_time.text.text = Language.LuckyGiftBag.LuckyValueRefreshTime .. ToColorStr(TimeUtil.FormatSecondDHM2(close_time), COLOR3B.D_GREEN)
		end
	end,
	function()
	end
)
end

function LuckyGiftBagLocalView:CleanRefreshTime()
    if self.refresh_time and CountDown.Instance:HasCountDown(self.refresh_time) then
        CountDown.Instance:RemoveCountDown(self.refresh_time)
        self.refresh_time = nil
    end
end

function LuckyGiftBagLocalView:FlushModel()
	local model_data = LuckyGiftBagLocalWgData.Instance:GetCurrentRateShow()
	if not IsEmptyTable(model_data) and self.model_display_id ~= model_data.model_show_itemid then
		self.model_display_id = model_data.model_show_itemid

		local display_data = {}
		display_data.should_ani = true
		display_data.bundle_name = model_data.model_bundle_name
		display_data.asset_name = model_data.model_asset_name
		display_data.render_type = model_data.model_show_type - 1
		display_data.item_id = model_data.model_show_itemid
		display_data.model_click_func = function()
            TipWGCtrl.Instance:OpenItem({ item_id = model_data.model_show_itemid })
        end
		display_data.model_rt_type = ModelRTSCaleType.L
		self.model_display:SetData(display_data)

		--self.node_list.img_skill:SetActive(model_data.show_icon == 1)
	end
end

-- 刷新最终奖励
function LuckyGiftBagLocalView:FlushFinalLuckyGradeReward(data_info)
	if IsEmptyTable(data_info) then
		return
	end

	-- 配置表中未标识那个是大奖，策划说拿最后一个档次
	data_info.is_final_reward = true
	if not self.final_reward_item then
		self.final_reward_item = LuckyLocalGradeRewardRender.New(self.node_list.final_reward_item_pos)
		-- 会出现没加载完赋值没刷新情况
		self.final_reward_item:SetData(data_info)
	else
		self.final_reward_item:SetData(data_info)
	end
end

function LuckyGiftBagLocalView:OnScrollRectValueChange()
	local rect = self.node_list.reward_item_list.scroll_rect.content
	self.node_list.slider.rect.sizeDelta = Vector2(rect.rect.size.x + grade_reward_item_width / 2, 10)
	local pos_x =  RectTransform.GetAnchoredPositionXY(self.node_list.reward_item_list.scroll_rect.content)
	RectTransform.SetAnchoredPositionXY(self.node_list.slider.rect, pos_x)
end

function LuckyGiftBagLocalView:OnRecordBtnClick()
    local data_list = LuckyGiftBagLocalWgData.Instance:GetRateRecordList()
	local tips_data_list = {}
    if not IsEmptyTable(data_list) then
		for i, v in ipairs(data_list) do
			local temp_data = {}
			temp_data.item_data = {}
			temp_data.item_data.item_id = v.item_id
			temp_data.item_data.num = v.num
			temp_data.item_data.is_bind = v.is_bind
			temp_data.consume_time = v.timestamp
			temp_data.role_name = v.name
			table.insert(tips_data_list, temp_data)
		end
    end

	TipWGCtrl.Instance:OpenTipsRewardRecordView(tips_data_list)
	-- LuckyGiftBagLoaclWgCtrl.Instance:OpenLuckyGiftBagRecordView()
end

function LuckyGiftBagLocalView:OnDailyRewardBtnClick()
	if LuckyGiftBagLocalWgData.Instance:GetDailyRewardRemind() == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LuckyGiftBag.DailyRewardHasGet)
	else
		LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_RECEIVE_DAILY_REWARD)
	end
end

function LuckyGiftBagLocalView:OnSendLuckyValueBtnClick()
	LuckyGiftBagLoaclWgCtrl.Instance:OpenSendLuckyValueView()
end

function LuckyGiftBagLocalView:OnThroughTrainBtnClick()
	LuckyGiftBagLoaclWgCtrl.Instance:OpenThroughTrainView()
end

function LuckyGiftBagLocalView:OnClickTipsBtn()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.LuckyGiftBag.RuleTitle)
	role_tip:SetContent(Language.LuckyGiftBag.RuleDesc)
end

function LuckyGiftBagLocalView:OnLuckyValueRankBtnClick()
	-- 策划说打开时请求下就好，没必要实时显示
	LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_RANK_INFO)
	LuckyGiftBagLoaclWgCtrl.Instance:OpenRankView()
end

function LuckyGiftBagLocalView:OnSuperDoubleBtnClick()
	LuckyGiftBagLoaclWgCtrl.Instance:OpenSuperDoubleView()
end

function LuckyGiftBagLocalView:OnHatchFightMount()
	ViewManager.Instance:Open(GuideModuleName.NewFightMountView)
end

function LuckyGiftBagLocalView:PlayDrawSpineEffect(prchase_mode)
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

	if prchase_mode == 1 then
		self.node_list["single_effect_root"]:SetActive(true)
	else
		self.node_list["duo_effect_root"]:SetActive(true)
	end

	self.node_list["ui_blocker"]:SetActive(true)


	math.randomseed(os.time())
	local rand_direction1 = math.random(1, 100)
	rand_direction1 = rand_direction1 > 50 and 1 or -1
	self.delay_play_draw_idle = GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list["bg"] then
			self:ShakeNodeAnimiBurst(self.node_list["bg"], self.operate_show_tween1, rand_direction1, 30)
		end
	
		if self.node_list and self.node_list["model"] then
			self:ShakeNodeAnimiBurst(self.node_list["model"], self.operate_show_tween1, rand_direction1, 30)
		end
	
		if self.node_list and self.node_list["UIOverride"] then
			self:ShakeNodeAnimiBurst(self.node_list["UIOverride"], self.operate_show_tween1, rand_direction1, 30)
		end
	end, 0.2)

	self.delay_play_draw_idle2 = GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list["bg"] then
			self:ShakeNodeAnimiBurst(self.node_list["bg"], self.operate_show_tween2, rand_direction1, 50)
		end
	
		if self.node_list and self.node_list["model"] then
			self:ShakeNodeAnimiBurst(self.node_list["model"], self.operate_show_tween2, rand_direction1 * -1, 50)
		end
	
		if self.node_list and self.node_list["UIOverride"] then
			self:ShakeNodeAnimiBurst(self.node_list["UIOverride"], self.operate_show_tween2, rand_direction1 * -1, 50)
		end
	end, 0.6)


	self.delay_play_draw_idle3 = GlobalTimerQuest:AddDelayTimer(function()
		-- local rand_direction2 = rand_direction1 * -1
		-- if self.node_list and self.node_list["bg"] then
		-- 	self:ShakeNodeAnimiBurst(self.node_list["bg"], self.operate_show_tween3, rand_direction2, 70)
		-- end

		-- if self.node_list and self.node_list["model"] then
		-- 	self:ShakeNodeAnimiBurst(self.node_list["model"], self.operate_show_tween3, rand_direction2, 70)
		-- end

		-- if self.node_list and self.node_list["UIOverride"] then
		-- 	self:ShakeNodeAnimiBurst(self.node_list["UIOverride"], self.operate_show_tween3, rand_direction2, 70)
		-- end

		self.delay_play_draw_idle4 = GlobalTimerQuest:AddDelayTimer(function()
			if self.effect_end_callback then
				self.effect_end_callback()
				self.effect_end_callback = nil
			end
			self:HideDrawSpineEffect()
		end, 0.5)
	end, 1.5)
end

-- 震动
function LuckyGiftBagLocalView:ShakeNodeAnimiBurst(trans, sequence, direction, strength)
	direction = direction or 1
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	strength = strength or 30
	local pos = trans.transform.anchoredPosition
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(strength * direction, strength * direction, 0), 0.01)) 	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-strength * direction, strength * direction, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-strength * direction, -strength * direction, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(strength * direction, -strength * direction, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-strength * direction, strength * direction, 0), 0.01))	--左5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(strength * direction, strength * direction, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-strength * direction, -strength * direction, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(strength * direction, -strength * direction, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-strength * direction, -strength * direction, 0), 0.01))	--下5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(strength * direction, strength * direction, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(-strength * direction, strength * direction, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOAnchorPos(pos + u3dpool.vec3(strength * direction, -strength * direction, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOAnchorPos(pos, 0.02))	--恢复 0
    sequence:SetEase(DG.Tweening.Ease.Linear)
end

function LuckyGiftBagLocalView:HideDrawSpineEffect()
	self.node_list["single_effect_root"]:SetActive(false)
	self.node_list["duo_effect_root"]:SetActive(false)
	self.node_list["ui_blocker"]:SetActive(false)

	if self.operate_show_tween1 then
		self.operate_show_tween1:Kill()
		self.operate_show_tween1 = nil
	end

	if self.operate_show_tween2 then
		self.operate_show_tween2:Kill()
		self.operate_show_tween2 = nil
	end

	if self.operate_show_tween3 then
		self.operate_show_tween3:Kill()
		self.operate_show_tween3 = nil
	end

    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.delay_play_draw_idle3 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle3)
		self.delay_play_draw_idle3 = nil
	end

	if self.delay_play_draw_idle4 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle4)
		self.delay_play_draw_idle4 = nil
	end

end

function LuckyGiftBagLocalView:SetEffectEndCallBack(call_back)
	self.effect_end_callback = call_back
end

function LuckyGiftBagLocalView:OnClickSkipSpine()
    local is_skip = LuckyGiftBagLocalWgData.Instance:GetSkipSpineStatus()
    is_skip = not is_skip
    LuckyGiftBagLocalWgData.Instance:SetSkipSpineStatus(is_skip)
    self:FlushSkipSpine()
end

function LuckyGiftBagLocalView:FlushSkipSpine()
	local is_skip = LuckyGiftBagLocalWgData.Instance:GetSkipSpineStatus()
	self.node_list.skip_spine_yes:SetActive(is_skip)
end
--------------------------------------------幸运值购买挡位信息-----------------------------------------------
LuckyGiftBagLocalItemRender = LuckyGiftBagLocalItemRender or BaseClass(BaseRender)
function LuckyGiftBagLocalItemRender:__init()
end

function LuckyGiftBagLocalItemRender:__delete()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function LuckyGiftBagLocalItemRender:LoadCallBack()
	if nil == self.reward_item_list then
		self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
	end

	self.node_list.reward_explain.text.text = Language.LuckyGiftBag.LuckyGiftBagItemExplain
	XUI.AddClickEventListener(self.node_list.bug_reward_btn, BindTool.Bind(self.OnBugRewardBtnClick, self))
end

function LuckyGiftBagLocalItemRender:OnFlush()
	if not IsEmptyTable(self.data) then
		self.node_list.reward_name.text.text = self.data[1].gift_name
		local item_data = LuckyGiftBagLocalWgData.Instance:GetRewardPoolItemDataList(self.data[1].grade, self.data[1].seq)
		self.reward_item_list:SetDataList(item_data)
		local info_data = LuckyGiftBagLocalWgData.Instance:GetGradeCfgInfo(self.data[1].grade, self.data[1].seq)
		--默认拿第一挡位购买信息
		local prchase_mode = GIFT_PRCHASE_OPERATE_TYPE.PRCHASE_MODE_ONE
		--local add_lucky_value = info_data[prchase_mode][1].add_lucky * LuckyGiftBagLocalWgData.Instance:GetCurrentLuckyDoubleAddPer()
		--self.node_list.add_lucky.text.text = string.format(Language.LuckyGiftBag.LuckyValueAdd, add_lucky_value)
		self.node_list.bug_reward_btn_text.text.text = info_data[prchase_mode][1].need_cost
	end
end

function LuckyGiftBagLocalItemRender:OnBugRewardBtnClick()
	local grade, seq, gift_name = self.data[1].grade, self.data[1].seq, self.data[1].gift_name
	LuckyGiftBagLoaclWgCtrl.Instance:OpenComfirmView(grade, seq, gift_name)
end

-----------------------------------------------进度条奖励信息------------------------------------------------------
LuckyLocalGradeRewardRender = LuckyLocalGradeRewardRender or BaseClass(BaseRender)
function LuckyLocalGradeRewardRender:__init()
end

function LuckyLocalGradeRewardRender:__delete()
end

function LuckyLocalGradeRewardRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell_pos)
		self.item_cell:SetIsShowTips(false)
	end
	self.item_cell:AddClickEventListener(BindTool.Bind1(self.OnClick, self))
end

function LuckyLocalGradeRewardRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil 
	end
end

function LuckyLocalGradeRewardRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	if self.node_list.bg then
		local mask = self.node_list.bg:GetComponent("Mask")
		mask.enabled = self.index == 4 and true or false
	end

	local reward_info = self.data.reward_info
	self.node_list.value.text.text = reward_info.need_lucky
	self.item_cell:SetShowCualityBg(false)
	self.item_cell:SetData({item_id = reward_info.item.item_id})
	self.item_cell:SetCellBgEnabled(false)
	self.item_cell:SetEffectRootEnable(false)

	local state = self:GetGradeRewardState()
	self.node_list.lingqu_flag:SetActive(state == REWARD_STATE_TYPE.FINISH)
	--self.node_list.kuang:SetActive(self.data.is_final_reward)
	self.node_list.can_get_flag:SetActive(state == REWARD_STATE_TYPE.CAN_FETCH)
	self.node_list.remind:SetActive(state == REWARD_STATE_TYPE.CAN_FETCH)
end

function LuckyLocalGradeRewardRender:GetGradeRewardState()
	local lucky_value = LuckyGiftBagLocalWgData.Instance:GetLickyValue()
	local need_lucky_value = self.data.reward_info.need_lucky

	if self.data.reward_flag == 0 and lucky_value >= need_lucky_value then
		return REWARD_STATE_TYPE.CAN_FETCH
	elseif self.data.reward_flag == 1 then
		return REWARD_STATE_TYPE.FINISH
	else
		return REWARD_STATE_TYPE.UNDONE
	end
end

function LuckyLocalGradeRewardRender:OnClick(cell)
	if not IsEmptyTable(self.data) then
		local lucky_value = LuckyGiftBagLocalWgData.Instance:GetLickyValue()
		local reward_info = self.data.reward_info
		local need_lucky_value = reward_info.need_lucky
		if self:GetGradeRewardState() == REWARD_STATE_TYPE.CAN_FETCH then
			LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_LUCKY_GRADE_REWARD, reward_info.seq)
		else
			TipWGCtrl.Instance:OpenItem({item_id = reward_info.item.item_id}, ItemTip.FROM_NORMAL)
		end
	end
end