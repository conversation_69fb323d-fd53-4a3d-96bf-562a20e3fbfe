-- K-跨服藏宝.xls
local item_table={
[1]={item_id=57833,num=1,is_bind=1},
[2]={item_id=48504,num=10,is_bind=1},
[3]={item_id=26369,num=2,is_bind=1},
[4]={item_id=26368,num=6,is_bind=1},
[5]={item_id=26367,num=6,is_bind=1},
[6]={item_id=57834,num=1,is_bind=1},
[7]={item_id=48504,num=20,is_bind=1},
[8]={item_id=26369,num=3,is_bind=1},
[9]={item_id=26368,num=9,is_bind=1},
[10]={item_id=26367,num=9,is_bind=1},
[11]={item_id=48504,num=2,is_bind=1},
[12]={item_id=22576,num=2,is_bind=1},
[13]={item_id=26345,num=2,is_bind=1},
[14]={item_id=26344,num=4,is_bind=1},
[15]={item_id=22006,num=2,is_bind=1},
[16]={item_id=57835,num=1,is_bind=1},
[17]={item_id=48504,num=30,is_bind=1},
[18]={item_id=26369,num=5,is_bind=1},
[19]={item_id=26368,num=15,is_bind=1},
[20]={item_id=26367,num=15,is_bind=1},
[21]={item_id=22576,num=3,is_bind=1},
[22]={item_id=26345,num=3,is_bind=1},
[23]={item_id=26344,num=6,is_bind=1},
[24]={item_id=22006,num=3,is_bind=1},
[25]={item_id=26563,num=1,is_bind=1},
[26]={item_id=48504,num=40,is_bind=1},
[27]={item_id=26369,num=10,is_bind=1},
[28]={item_id=26368,num=30,is_bind=1},
[29]={item_id=26367,num=30,is_bind=1},
[30]={item_id=22576,num=5,is_bind=1},
[31]={item_id=26345,num=5,is_bind=1},
[32]={item_id=26344,num=10,is_bind=1},
[33]={item_id=22006,num=5,is_bind=1},
[34]={item_id=26344,num=11,is_bind=1},
[35]={item_id=26368,num=3,is_bind=1},
[36]={item_id=26344,num=12,is_bind=1},
[37]={item_id=26368,num=4,is_bind=1},
[38]={item_id=26344,num=13,is_bind=1},
[39]={item_id=26368,num=5,is_bind=1},
[40]={item_id=26344,num=14,is_bind=1},
[41]={item_id=26344,num=15,is_bind=1},
[42]={item_id=26368,num=7,is_bind=1},
[43]={item_id=26344,num=16,is_bind=1},
[44]={item_id=26368,num=8,is_bind=1},
[45]={item_id=26344,num=17,is_bind=1},
[46]={item_id=26344,num=18,is_bind=1},
[47]={item_id=26368,num=10,is_bind=1},
[48]={item_id=30423,num=4,is_bind=1},
[49]={item_id=30423,num=10,is_bind=1},
[50]={item_id=30423,num=11,is_bind=1},
[51]={item_id=30423,num=12,is_bind=1},
[52]={item_id=30423,num=5,is_bind=1},
[53]={item_id=30423,num=6,is_bind=1},
[54]={item_id=30423,num=7,is_bind=1},
[55]={item_id=30423,num=8,is_bind=1},
[56]={item_id=30423,num=9,is_bind=1},
[57]={item_id=22090,num=1,is_bind=1},
[58]={item_id=26368,num=2,is_bind=1},
[59]={item_id=30423,num=3,is_bind=1},
[60]={item_id=57832,num=1,is_bind=1},
[61]={item_id=26369,num=1,is_bind=1},
[62]={item_id=26367,num=3,is_bind=1},
[63]={item_id=22576,num=1,is_bind=1},
[64]={item_id=26345,num=1,is_bind=1},
[65]={item_id=26344,num=2,is_bind=1},
[66]={item_id=22006,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
treasure={
{},
{seq=1,item_id=46569,type=2,gather_times=1,mature_times=0,}
},

treasure_meta_table_map={
},
level={
{color=2,},
{level=2,min_gather_times=2,max_gather_times=3,gather_id=3302,gather_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},color=3,name="极品灵珠",},
{level=3,min_gather_times=3,max_gather_times=4,gather_id=3303,gather_reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10]},loot_reward_item={[0]=item_table[11],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},color=4,name="圣品灵珠",},
{level=4,min_gather_times=4,max_gather_times=5,gather_id=3304,gather_reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},loot_reward_item={[0]=item_table[11],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},name="仙品灵珠",},
{level=5,min_gather_times=5,max_gather_times=6,gather_id=3305,gather_reward_item={[0]=item_table[25],[1]=item_table[26],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29]},loot_reward_item={[0]=item_table[11],[1]=item_table[30],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},color=6,name="神品灵珠",},
{seq=1,max_gather_times=1,gather_id=3306,gather_reward_item={},loot_reward_item={},param1=193,name="炸弹",}
},

level_meta_table_map={
},
beast_grade={
{},
{grade=2,min_open_day=11,max_open_day=50,normal_pool_id=21,special_pool_id=22,},
{grade=3,min_open_day=51,max_open_day=9999999,normal_pool_id=31,special_pool_id=32,}
},

beast_grade_meta_table_map={
},
beast_pool={
{},
{seq=1,beast_id=30451,gather_id=3202,succ_reward_item={[0]=item_table[34]},fail_reward_item={[0]=item_table[35]},},
{seq=2,beast_id=30452,gather_id=3203,succ_reward_item={[0]=item_table[36]},fail_reward_item={[0]=item_table[37]},},
{seq=3,beast_id=30453,gather_id=3204,succ_reward_item={[0]=item_table[38]},fail_reward_item={[0]=item_table[39]},},
{seq=4,beast_id=30456,gather_id=3205,succ_reward_item={[0]=item_table[40]},fail_reward_item={[0]=item_table[4]},},
{seq=5,beast_id=30459,gather_id=3206,succ_reward_item={[0]=item_table[41]},fail_reward_item={[0]=item_table[42]},},
{seq=6,beast_id=30462,gather_id=3207,succ_reward_item={[0]=item_table[43]},fail_reward_item={[0]=item_table[44]},},
{seq=7,beast_id=30465,gather_id=3208,succ_reward_item={[0]=item_table[45]},fail_reward_item={[0]=item_table[9]},},
{seq=8,beast_id=30468,gather_id=3209,succ_reward_item={[0]=item_table[46]},fail_reward_item={[0]=item_table[47]},},
{id=12,beast_id=30501,gather_id=3210,succ_reward_item={[0]=item_table[48]},fail_reward_item={[0]=item_table[32]},},
{id=12,},
{id=12,},
{id=12,},
{id=12,},
{id=12,},
{id=12,},
{id=12,},
{id=12,},
{id=21,},
{id=21,},
{id=21,},
{id=21,},
{id=21,},
{id=21,},
{id=21,},
{id=21,},
{id=21,},
{id=22,},
{id=22,},
{id=22,},
{id=22,},
{id=22,},
{id=22,},
{id=22,seq=6,beast_id=30594,gather_id=3216,succ_reward_item={[0]=item_table[49]},fail_reward_item={[0]=item_table[43]},},
{id=22,seq=7,beast_id=30637,gather_id=3217,succ_reward_item={[0]=item_table[50]},fail_reward_item={[0]=item_table[45]},},
{id=22,seq=8,beast_id=30653,gather_id=3218,succ_reward_item={[0]=item_table[51]},fail_reward_item={[0]=item_table[46]},},
{id=31,},
{id=31,},
{id=31,},
{id=31,},
{id=31,},
{id=31,},
{id=31,},
{id=31,},
{id=31,},
{id=32,},
{id=32,seq=1,beast_id=30515,gather_id=3211,succ_reward_item={[0]=item_table[52]},fail_reward_item={[0]=item_table[34]},},
{id=32,seq=2,beast_id=30536,gather_id=3212,succ_reward_item={[0]=item_table[53]},fail_reward_item={[0]=item_table[36]},},
{id=32,seq=3,beast_id=30556,gather_id=3213,succ_reward_item={[0]=item_table[54]},fail_reward_item={[0]=item_table[38]},},
{id=32,seq=4,beast_id=30567,gather_id=3214,succ_reward_item={[0]=item_table[55]},fail_reward_item={[0]=item_table[40]},},
{id=32,seq=5,beast_id=30578,gather_id=3215,succ_reward_item={[0]=item_table[56]},fail_reward_item={[0]=item_table[41]},},
{id=32,},
{id=32,},
{id=32,}
},

beast_pool_meta_table_map={
[28]=10,	-- depth:1
[46]=28,	-- depth:2
[40]=4,	-- depth:1
[38]=2,	-- depth:1
[41]=5,	-- depth:1
[39]=3,	-- depth:1
[42]=6,	-- depth:1
[44]=8,	-- depth:1
[45]=9,	-- depth:1
[52]=34,	-- depth:1
[43]=7,	-- depth:1
[33]=51,	-- depth:1
[27]=45,	-- depth:2
[31]=49,	-- depth:1
[11]=47,	-- depth:1
[12]=48,	-- depth:1
[13]=31,	-- depth:2
[14]=50,	-- depth:1
[15]=33,	-- depth:2
[16]=52,	-- depth:2
[17]=35,	-- depth:1
[18]=36,	-- depth:1
[32]=14,	-- depth:2
[20]=38,	-- depth:2
[22]=40,	-- depth:2
[23]=41,	-- depth:2
[24]=42,	-- depth:2
[25]=43,	-- depth:2
[26]=44,	-- depth:2
[53]=17,	-- depth:2
[29]=11,	-- depth:2
[30]=12,	-- depth:2
[21]=39,	-- depth:2
[54]=18,	-- depth:2
},
other_default_table={open_level=130,open_day=1,scene_id=1003,gather_times=10,beast_gather_times=10,max_treasure=3,treasure_rare_reward_item={[0]=item_table[57],[1]=item_table[58],[2]=item_table[59],[3]=item_table[32]},beast_rare_reward_item={[0]=item_table[57],[1]=item_table[58],[2]=item_table[59],[3]=item_table[32]},min_range=4,},

treasure_default_table={seq=0,item_id=46570,type=1,duration_time=300,gather_times=5,mature_times=120,},

level_default_table={seq=0,level=1,min_gather_times=1,max_gather_times=2,gather_id=3301,gather_time=3,gather_reward_item={[0]=item_table[60],[1]=item_table[2],[2]=item_table[61],[3]=item_table[35],[4]=item_table[62]},loot_reward_item={[0]=item_table[11],[1]=item_table[63],[2]=item_table[64],[3]=item_table[65],[4]=item_table[66]},drop_id=0,param1=0,color=5,name="凡品灵珠",},

beast_grade_default_table={grade=1,min_open_day=1,max_open_day=10,refresh_num=10,normal_pool_id=11,special_num=2,special_pool_id=12,},

beast_pool_default_table={id=11,seq=0,beast_id=30450,gather_id=3201,gather_time=3,duration_time=3600,succ_reward_item={[0]=item_table[32]},fail_reward_item={[0]=item_table[58]},}

}

