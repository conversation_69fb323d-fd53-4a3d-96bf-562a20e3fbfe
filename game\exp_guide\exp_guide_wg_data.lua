ExpGuideWGData = ExpGuideWGData or BaseClass()

function ExpGuideWGData:__init()
    if ExpGuideWGData.Instance then
		error("[ExpGuideWGData] Attempt to create singleton twice!")
		return
	end
    ExpGuideWGData.Instance = self
    
    self:InitParam()
    self:InitConfig()
end

function ExpGuideWGData:__delete()
    -- body

    ExpGuideWGData.Instance = nil
end

function ExpGuideWGData:InitParam()
    self.fun_list = {}
    self.exp_datas = {}
end

function ExpGuideWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("task_level_guide_auto")
    self.fun_list = cfg.exp_guide
    SortTools.SortAsc(self.fun_list, "sort")

    self.exp_datas = ListToMap(cfg.get_exp, "lev")
end

function ExpGuideWGData:GetFunList()
    return self.fun_list
end

function ExpGuideWGData:GetShowFunList()
    local all_fun_list = self:GetFunList()
    local role_lev = GameVoManager.Instance:GetMainRoleVo().level
    local show_fun_list = {}
    for _, cfg in ipairs(all_fun_list) do
        local use_num = self:GetNum(cfg)
        local all_num = self:GetAllNum(cfg)
        all_num = all_num == 0 and cfg.count or all_num
        if role_lev >= cfg.limit_lev and (all_num <= 1 or (all_num - use_num) > 0) then
            table.insert(show_fun_list, cfg)
        end
    end

    return show_fun_list
end

-- 已消耗次数
function ExpGuideWGData:GetNum(cfg)
    if cfg.count > 1 then
        if cfg.id == 144 then       -- 日月修行
            return WuJinJiTanWGData.GetFBEnterTimes()
        elseif cfg.id == 2021 then  -- 主城护送
            return DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT)
        elseif cfg.id == 2078 then  -- 天梯争霸
            return self:GetTianTiJoinTimes()
        elseif cfg.id == 188 then   -- 悬赏任务
            return self:GetBountyAcceptNum()
        elseif cfg.id == 197 then   -- 祈福
            return QiFuWGData.Instance:GetGoldBuyyuanliTimes()
        elseif cfg.id == 145 then   -- 天峰夺宝
            return TeamEquipFbWGData.Instance:GetHTEFbEnteredTimes()
        end
    end
    return 1
end

-- 总次数
function ExpGuideWGData:GetAllNum(cfg)
    local all_count = 0
    if cfg.id == 144 then       -- 日月修行
        local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
        local yet_buy_time = WuJinJiTanWGData.GetFBBuyTimes()
        local user_ticket_time = WuJinJiTanWGData.GetFBAddTimes()
        all_count = other_cfg.everyday_times + yet_buy_time + user_ticket_time
    elseif cfg.id == 2078 then  -- 天梯争霸
        local sum_num = ArenaTianTiWGData.Instance:GetSumTiaoZhanNum()
        all_count = sum_num
    elseif cfg.id == 197 then   -- 祈福
        local temp_vip = VipWGData.Instance:GetRoleVipLevel()
        all_count = VipPower.Instance:GetParam(VipPowerId.pray_exp_times, temp_vip)
    end

    return all_count
end

-- 悬赏次数
function ExpGuideWGData:GetBountyAcceptNum()
	local info = TaskWGData.Instance:GetBountyListInfo()
	local today_accept_bounty_num = info and info.today_accept_bounty_num or 0
    
    return today_accept_bounty_num
end

-- 天梯争霸次数
function ExpGuideWGData:GetTianTiJoinTimes()
    local remain_num =  ArenaTianTiWGData.Instance:GetResidueTiaoZhanNum()
    local sum_num = ArenaTianTiWGData.Instance:GetSumTiaoZhanNum()
    --local tianti_info = ArenaTianTiWGData.Instance:GetUserinfo()
    return  sum_num - remain_num
end

function ExpGuideWGData:GetExp(id)
    if id == 2010 then  -- 九重劫塔
        local cur_level = FuBenPanelWGData.Instance:GetPassLevel()
        local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(cur_level + 1)
        return cfg and cfg.roleexp or 0
    elseif id == 225 then  -- 战力福利
        local max_seq = CapabilityWelfareWGData.Instance:GetMaxGotSeq()
        local task_info = CapabilityWelfareWGData.Instance:GetTaskBySeq(max_seq+1)
        return task_info.add_exp or 0
    end

    local role_lev = GameVoManager.Instance:GetMainRoleVo().level
    local cfg = self.exp_datas[role_lev] or {}
    local exp = 0
    if id == 144 then       -- 日月修行
        exp = cfg.exp_3 or exp
    elseif id == 16 then    -- 挑战BOSS
        exp = cfg.exp_5 or exp
    elseif id == 2021 then  -- 主城护送
        exp = cfg.exp_6 or exp
    elseif id == 2078 then  -- 天梯争霸
        exp = cfg.exp_7 or exp
    elseif id == 188 then   -- 悬赏任务
        exp = cfg.exp_1 or exp
    elseif id == 197 then   -- 祈福
        exp = cfg.exp_2 or exp
    elseif id == 145 then   -- 天峰夺宝
        exp = cfg.exp_4 or exp
    end

    return exp
end

function ExpGuideWGData:GetWay(id)
    local cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way
    if cfg and cfg[id] then
        return cfg[id]
    end
    return nil
end