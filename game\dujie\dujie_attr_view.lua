DujieAttrView = DujieAttrView or BaseClass(SafeBaseView)
function DujieAttrView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_attr_view")
end

function DujieAttrView:LoadCallBack()
	if not self.attr_list then
		self.attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.attr_list)
	end
end

function DujieAttrView:ReleaseCallBack()
	if self.attr_list then
		self.attr_list:DeleteMe()
		self.attr_list = nil
	end
end

function DujieAttrView:OnFlush()
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()

    if base_info.level == 0 then
        local has_data = false
        self.node_list.no_attr_tip:CustomSetActive(not has_data)
        self.node_list.attr_list:CustomSetActive(has_data)
    else
        local data_list = DujieWGData.Instance:GetDujieAttrList(base_info.level)
        local has_data = not IsEmptyTable(data_list)
        self.node_list.no_attr_tip:CustomSetActive(not has_data)
        self.node_list.attr_list:CustomSetActive(has_data)
        if has_data then
            self.attr_list:SetDataList(data_list)
        end
    end
end

