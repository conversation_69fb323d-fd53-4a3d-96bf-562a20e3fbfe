
XianLingBigRewardNotice = XianLingBigRewardNotice or BaseClass(SafeBaseView)
function XianLingBigRewardNotice:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_reward_tip")
end

function XianLingBigRewardNotice:__delete()

end

function XianLingBigRewardNotice:ReleaseCallBack()
	if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	if nil ~= self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end

	if self.close_delay then
		GlobalTimerQuest:CancelQuest(self.close_delay)
		self.close_delay = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.data = nil
end

function XianLingBigRewardNotice:CloseCallBack()
	local function main_cb()
		local right_parent = MainuiWGCtrl.Instance:GetSkillRightOtherContent()
		right_parent.rect:DOAnchorPosX(0, 0.3)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,main_cb)
end


function XianLingBigRewardNotice:OpenCallBack()
	local function main_cb()
		local right_parent = MainuiWGCtrl.Instance:GetSkillRightOtherContent()
		right_parent.rect:DOAnchorPosX(300, 0.3)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,main_cb)
end


function XianLingBigRewardNotice:LoadCallBack()
	self.node_list["not_tip_toggle"].button:AddClickListener(BindTool.Bind(self.OnClicNotNotice, self))
	--self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo, self))

	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
    self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))

	if self.item_cell == nil then
        self.item_cell = ItemCell.New(self.node_list.item_cell)
    end
end

function XianLingBigRewardNotice:OnClickGo()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN) then
		ViewManager.Instance:Open(GuideModuleName.XianLingGuZhen)
		self:Close()
	end
end


function XianLingBigRewardNotice:OnClicNotNotice()
	local cur_state = XianLingGuZhenWGData.Instance:GetNolongerTipFlag()
	XianLingGuZhenWGData.Instance:SetNoLongerTipFlag(not cur_state)
	self:FlushNoLonger()
end


function XianLingBigRewardNotice:SetData(data)
	self.data = data or {}
end


function XianLingBigRewardNotice:OnFlush()
	if not self.close_delay then
		self.close_delay = GlobalTimerQuest:AddDelayTimer(function ()
			if self.close_delay then
				GlobalTimerQuest:CancelQuest(self.close_delay)
				self.close_delay = nil
			end

			self:Close()
 		end, 10)
	end

	if self.data and not IsEmptyTable(self.data) then
		if self.data.item_id ~= 0 then
			self.node_list["name_txt"].text.text = self.data.name
			local data = {item_id = self.data.item_id}
    		self.item_cell:SetData(data)
			local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
			local item_name = ItemWGData.Instance:GetItemName(self.data.item_id) or ""
			self.node_list.item_name.text.text = ToColorStr(item_name, color)

			local special_cfg = XianLingGuZhenWGData.Instance:GetSpecialCfgByItemId(self.data.item_id)
			local reward_name = ""
			if special_cfg and not IsEmptyTable(special_cfg) then
				local cfg = ItemWGData.Instance:GetItemConfig(special_cfg.reward_item[0].item_id)
				reward_name = ToColorStr(cfg.name,ITEM_COLOR[cfg.color])--cfg.name
			end

			if self.data.name ~= "" then
				self.node_list["name_txt"].text.text = string.format(Language.XianLingGuZhen.RewardNoticeDesc2, self.data.name, reward_name)
			else
				self.node_list["name_txt"].text.text = Language.XianLingGuZhen.RewardNoticeDesc1
			end
		end
	end

	self:FlushNoLonger()
end

function XianLingBigRewardNotice:FlushNoLonger()
	local no_longer = XianLingGuZhenWGData.Instance:GetNolongerTipFlag()
	self.node_list["no_tip_flag"]:SetActive(no_longer)
end

function XianLingBigRewardNotice:ShrinkButtonsValueChange(isOn)
    local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
	if isOn or menu_ison then
		self.node_list.root.rect:DOAnchorPosX(300, 0.3)
	else
		self.node_list.root.rect:DOAnchorPosX(-180, 0.3)
	end
end

function XianLingBigRewardNotice:MainMenuIconChangeEvent(isOn)
	if isOn then
		self.node_list.root.rect:DOAnchorPosX(300, 0.3)
	else
		self.node_list.root.rect:DOAnchorPosX(-180, 0.3)
	end
end