WeiWoDunZunView = WeiWoDunZunView or BaseClass(SafeBaseView)

function WeiWoDunZunView:__init()
    self.view_style = ViewStyle.Full
    self.view_layer = UiLayer.Normal
    self.is_safe_area_adapter = true

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/weiwoduzun_ui_prefab", "layout_weiwo_duzun")
end

function WeiWoDunZunView:__delete()
end

function WeiWoDunZunView:ReleaseCallBack()
    if self.task_type_list then
        self.task_type_list:DeleteMe()
        self.task_type_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.reward_cell then
        self.reward_cell:DeleteMe()
        self.reward_cell = nil
    end

    if self.fecth_role_model then
        self.fecth_role_model:DeleteMe()
        self.fecth_role_model = nil
    end

    self.select_task_data = nil
end

function WeiWoDunZunView:OpenCallBack()
    WeiWoDunZunWGCtrl.Instance:ReqWeiWoDunZunInfo(WEIWODUZUN_OPERTE_TYPE.INFO)
end

function WeiWoDunZunView:LoadCallBack()
    if not self.task_type_list then
        self.task_type_list = AsyncListView.New(WeiWoDunZunTaskRender, self.node_list["task_type_list"])
        self.task_type_list:SetSelectCallBack(BindTool.Bind(self.OnTaskTypeHandler, self))
    end

    if self.model_display == nil then
        self.model_display = OperationActRender.New(self.node_list["model_display"])
    end

    if self.fecth_role_model == nil then
        local node = self.node_list["fecth_role_model"]
        self.fecth_role_model = RoleModel.New()
        local display_data = {
            parent_node = node,
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.fecth_role_model:SetRenderTexUI3DModel(display_data)
        -- self.fecth_role_model:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    if self.reward_cell == nil then
        self.reward_cell = ItemCell.New(self.node_list["reward_pos"])
    end

    XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnClickRule, self))
    XUI.AddClickEventListener(self.node_list.open_suit_btn, BindTool.Bind(self.OpenSuitView, self))
    XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.GetTaskReward, self))

    self.select_task_data = nil
end

function WeiWoDunZunView:ShowIndexCallBack()

end

function WeiWoDunZunView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if k == "all" then
            local show_task_info = WeiWoDunZunWGData.Instance:GetTaskTypeCfg()
            if IsEmptyTable(show_task_info) then
                return
            end

            if self.task_type_list then
                self.task_type_list:SetDataList(show_task_info)
            end

            if not self.select_task_data then
                local jump_index = WeiWoDunZunWGData.Instance:GetSelectTypeIndex()
                self.task_type_list:JumpToIndex(jump_index)
            end

            self:FlushInfo()
            self:FlushModel()
        elseif k == "flush_task" then
            if self.task_type_list then
                self.task_type_list:RefreshActiveCellViews()
            end

            self:FlushInfo()
        end
    end
end

function WeiWoDunZunView:OnTaskTypeHandler(item)
    if nil == item or nil == item.data then
        return
    end

    local cell_data = item.data
    if self.select_task_data and self.select_task_data == cell_data then
        return
    end

    self.select_task_data = cell_data
    self:FlushInfo()
    self:FlushModel()

    WeiWoDunZunWGCtrl.Instance:ReqWeiWoDunZunInfo(WEIWODUZUN_OPERTE_TYPE.UPDATE, self.select_task_data.task_id)
end

function WeiWoDunZunView:FlushInfo()
    if self.select_task_data == nil then
        return
    end

    local item_data = self.select_task_data.item_list[0]
    self.reward_cell:SetData(item_data)
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_data and item_data.item_id)
    self.node_list.reward_name.text.text = item_cfg and item_cfg.name
    self.node_list.task_desc.text.text = self.select_task_data.des

    local cur_task_info = WeiWoDunZunWGData.Instance:GetTaskInfoById(self.select_task_data.task_id)
    if cur_task_info == nil then
        return
    end

    local is_fecth_role_id = cur_task_info.is_fecth_role_id
    local no_fecth_role = is_fecth_role_id == "" or is_fecth_role_id == 0
    self.node_list.no_data_panel:SetActive(no_fecth_role)
    self.node_list.is_get:SetActive(cur_task_info.reward_flag == REWARD_STATE_TYPE.FINISH)
    self.node_list.get_btn:SetActive(cur_task_info.reward_flag ~= REWARD_STATE_TYPE.FINISH)


    local open_day = 0
    local close_day = 0

    if self.select_task_data.show_time and self.select_task_data.show_time ~= "" then
        local show_time_list = string.split(self.select_task_data.show_time, "|")
        open_day = tonumber(show_time_list[1]) or 0
        close_day = tonumber(show_time_list[2]) or 0
    end

    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local show_desc = cur_day >= open_day and cur_day <= close_day
    self.node_list.show_time_bg:SetActive(show_desc)
    self.node_list.show_time_des.text.text = self.select_task_data.show_time_des
    self.fecth_role_model:ClearModel()
    XUI.SetButtonEnabled(self.node_list["get_btn"], no_fecth_role)
    if no_fecth_role then
        self.node_list.name.text.text = Language.WeiWoDunZunView.no_role_info

        local reward_flag = cur_task_info.reward_flag

        local get_str = ""
        if reward_flag == REWARD_STATE_TYPE.CAN_FETCH then
            get_str = Language.WeiWoDunZunView.GetStr[1]
        else
            get_str = Language.WeiWoDunZunView.GetStr[2]
            if self.select_task_data.act_type ~= "" then
                local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(self.select_task_data.act_type) --活动是否开启
                if not is_act_open then
                    get_str = Language.WeiWoDunZunView.GetStr[0]
                    XUI.SetButtonEnabled(self.node_list["get_btn"], false)
                end
            end
        end

        self.node_list.get_text.text.text = get_str
    else
        self.node_list.get_text.text.text = Language.WeiWoDunZunView.GetStr[0]
        self.node_list.name.text.text = cur_task_info.fecth_role_name
        BrowseWGCtrl.Instance:BrowRoelInfo(is_fecth_role_id, function(protocol)
            local role_vo = protocol
            local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true }

            if self.fecth_role_model then
                self.fecth_role_model:SetModelResInfo(role_vo, ignore_table, function()
                    self.fecth_role_model:PlayRoleShowAction()
                end)

                self.fecth_role_model:FixToOrthographic(self.root_node_transform)
            end
        end)
    end
end

function WeiWoDunZunView:FlushModel()
    if self.select_task_data == nil then
        return
    end

    local display_data = {}
    display_data.should_ani = true
    if self.select_task_data.model_show_itemid ~= 0 and self.select_task_data.model_show_itemid ~= "" then
        local split_list = string.split(self.select_task_data.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = self.select_task_data.model_show_itemid
        end
    end

    display_data.bundle_name = self.select_task_data["model_bundle_name"]
    display_data.asset_name = self.select_task_data["model_asset_name"]
    local model_show_type = tonumber(self.select_task_data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

    self.model_display:SetData(display_data)
    local scale = self.select_task_data["display_scale"]
    Transform.SetLocalScaleXYZ(self.node_list["model_display"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
    if self.select_task_data.display_pos and self.select_task_data.display_pos ~= "" then
        local pos_list = string.split(self.select_task_data.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.model_display.rect, pos_x, pos_y)

    if self.select_task_data.rotation and self.select_task_data.rotation ~= "" then
        local rotation_tab = string.split(self.select_task_data.rotation, "|")
        self.node_list["model_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
            rotation_tab[3])
    end
end

function WeiWoDunZunView:OnClickRule()
    RuleTip.Instance:SetContent(Language.WeiWoDunZunView.RuleTips, Language.WeiWoDunZunView.RuleTipsTitle)
end

function WeiWoDunZunView:OpenSuitView()
    ViewManager.Instance:Open(GuideModuleName.GuiXuDreamView)
end

function WeiWoDunZunView:GetTaskReward()
    if self.select_task_data == nil then
        return
    end

    local cur_task_info = WeiWoDunZunWGData.Instance:GetTaskInfoById(self.select_task_data.task_id)
    if cur_task_info == nil then
        return
    end

    if cur_task_info.reward_flag == REWARD_STATE_TYPE.CAN_FETCH then
        WeiWoDunZunWGCtrl.Instance:ReqWeiWoDunZunInfo(WEIWODUZUN_OPERTE_TYPE.FEACH_REWARD, self.select_task_data.task_id)
    else
        if self.select_task_data.open_panel ~= "" then
            if self.select_task_data.act_type == "" then --非活动
                FunOpen.Instance:OpenViewNameByCfg(self.select_task_data.open_panel)
            else
                local is_act_open = ActivityWGData.Instance:GetActivityIsOpen(self.select_task_data.act_type) --活动是否开启
                if is_act_open then
                    FunOpen.Instance:OpenViewNameByCfg(self.select_task_data.open_panel)
                else
                    TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ActivateNoOpen)
                end
            end
        end
    end
end

-----------------------------WeiWoDunZunTaskRender
local RUSH_TYPE = {
    [WEIWODUZUN_TASK_TYPE.WING] = 5, --羽翼榜
    [WEIWODUZUN_TASK_TYPE.ATTR] = 10, -- 战力榜
}
WeiWoDunZunTaskRender = WeiWoDunZunTaskRender or BaseClass(BaseRender)
function WeiWoDunZunTaskRender:__init()
    self.refresh_event = nil
end

function WeiWoDunZunTaskRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
end

function WeiWoDunZunTaskRender:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = ResPath.GetWeiWoDuZunImg("a2_wwdz_task_" .. self.data.task_id)
    self.node_list.bg.image:LoadSprite(bundle, asset, function()
        self.node_list.bg.image:SetNativeSize()
    end)

    local task_info = WeiWoDunZunWGData.Instance:GetTaskInfoById(self.data.task_id)
    local is_red = task_info and task_info.reward_flag == REWARD_STATE_TYPE.CAN_FETCH
    self.node_list.remind:SetActive(is_red)
    self.node_list.fecth_role_name.text.text = task_info and task_info.fecth_role_name or ""
    self:ShowTimeText()
    self:FlushTimeCount()

    local open_day = 0
    local close_day = 0

    if self.data.show_time and self.data.show_time ~= "" then
        local show_time_list = string.split(self.data.show_time, "|")
        open_day = tonumber(show_time_list[1]) or 0
        close_day = tonumber(show_time_list[2]) or 0
    end

    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    self.node_list.act_show:SetActive(cur_day >= open_day and cur_day <= close_day)
end

function WeiWoDunZunTaskRender:OnSelectChange(is_select)
    if not self.data then
        return
    end

    self.node_list.select_hl:SetActive(is_select)
end

function WeiWoDunZunTaskRender:FlushTimeCount()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

    self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.ShowTimeText, self), 0.5)
end

function WeiWoDunZunTaskRender:ShowTimeText()
    local time = self:GetTimeCount()
    if time > 0 then
        local time_str = TimeUtil.FormatSecondDHM9(time)
        self.node_list["time"].text.text = string.format(Language.WeiWoDunZunView.TimeStr, time_str)
        self.node_list.str_bg:SetActive(true)
    else
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end

        self.node_list.time.text.text = ""
        local task_info = WeiWoDunZunWGData.Instance:GetTaskInfoById(self.data.task_id)
        self.node_list.str_bg:SetActive(task_info and task_info.fecth_role_name ~= "")
    end
end

function WeiWoDunZunTaskRender:GetTimeCount()
    local time = 0
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if self.data.task_id == WEIWODUZUN_TASK_TYPE.ATTR or self.data.task_id == WEIWODUZUN_TASK_TYPE.WING then
        local rush_type = RUSH_TYPE[self.data.task_id]
        local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
        local close_day = opengame_cfg.close_day_index
        local format_time = os.date("*t", now_time)
        local end_time = os.time({
            year = format_time.year,
            month = format_time.month,
            day = format_time.day + close_day - open_day + 1,
            hour = 0,
            min = 0,
            sec = 0
        })

        if open_day >= opengame_cfg.open_day_index then
            local count_down_time = end_time - 3600 -- 23:00结算
            time = count_down_time - now_time
        end
    elseif self.data.task_id == WEIWODUZUN_TASK_TYPE.CHARM_MALE or self.data.task_id == WEIWODUZUN_TASK_TYPE.CHARM_FEMALE then
        local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
        if act_info and act_info.status == ACTIVITY_STATUS.OPEN then
            time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
        end
    end

    return time
end
