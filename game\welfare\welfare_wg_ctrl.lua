require("game/welfare/carnival_sevenday")
-- require("game/welfare/huoyue_view")
require("game/welfare/welfare_wg_data")
require("game/welfare/welfare_view")
require("game/welfare/upgrade_view")
require("game/welfare/vipgift_view")
-- require("game/welfare/dailyfind_view")
require("game/welfare/welfare_gift_view")
require("game/welfare/welfare_items")
require("game/welfare/dailyfind_render")
require("game/welfare/welfare_friend")
require("game/welfare/qiandao_view")
require("game/welfare/welfare_zaixian_view")
require("game/welfare/update_affiche_view")
-- require("game/welfare/user_batch_view")
require("game/welfare/welfarefriend_view")
-- require("game/welfare/welfare_qifu")
require("game/welfare/welfare_redpaper")
require("game/welfare/welfare_online_reward_view")
require("game/welfare/phone_bind_view")
require("game/welfare/accumulative_login_view")
-- require("game/welfare/welfare_handset_verify_view")
-- require("game/welfare/welfare_handset_verify_sdk_view")
-- require("game/welfare/welfare_zhaohui_reward_view")

-- 福利
WelfareWGCtrl = WelfareWGCtrl or BaseClass(BaseWGCtrl)

function WelfareWGCtrl:__init()
	if WelfareWGCtrl.Instance then
		ErrorLog("[WelfareWGCtrl]:Attempt to create singleton twice!")
	end
	WelfareWGCtrl.Instance = self

	self.data = WelfareWGData.New()
	self.view = WelfareView.New(GuideModuleName.Welfare)
	self.paper_view = WorldRedPaperView.New(GuideModuleName.WorldRedPaper)
	-- self.user_batch_view = WelfareBatchView.New()
	self.welfarefriend_list = WelfareFriend.New()
	-- self.welfare_reward_view = WelfareRewardView.New()
	self.need_auto_open = true
	self:RegisterAllProtocals()
	self:BindGlobalEvent(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.SceneLoadComplete, self))
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

	self:BindGlobalEvent(AuditEvent.IS_BIND_PHONE, BindTool.Bind(self.OnIsBindPhoneCallBack, self))
	self:BindGlobalEvent(AuditEvent.IS_BIND_PHONE_SUCCESS, BindTool.Bind(self.OnIsBindPhoneSuccessCallBack, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function WelfareWGCtrl:SceneLoadComplete()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then return end
	self:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_SELF_INFO)
	self:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GUILD_SYSTEM_INFO)
	self:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GET_GUILD_REDPAPER_INF0)
	self:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GET_WORLD_REDPAPER_INF0)

end
function WelfareWGCtrl:__delete()
	self.paper_view:DeleteMe()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	if nil ~= self.pop_alart then
		self.pop_alart:DeleteMe()
		self.pop_alart = nil
	end

	if nil ~= self.welfarefriend_list then
		self.welfarefriend_list:DeleteMe()
		self.welfarefriend_list = nil
	end

	if CountDownManager.Instance:HasCountDown("online_gift_end_time") then
		CountDownManager.Instance:RemoveCountDown("online_gift_end_time")
	end

	WelfareWGCtrl.Instance = nil
end

function WelfareWGCtrl:Open(index, param_t)
	local canday = self.data:GetCanRewardDay()
	if index and index == WELFARE_TYPE.KUANGHUAN then
		ViewManager.Instance:Open(GuideModuleName.SevenDay)
		if canday == SEVEN_DAY_LOGIN_MAX_REWARD_DAY + 1 then
			-- 领取完了
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.OtherErrors)
			-- return
		end
	end
	-- 打开面板直接发送协议-好友邀请请求全部信息
	-- self:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REQ_INFO)
	self.view:Open(index)
end

function WelfareWGCtrl:Flush(index, key, value)
	self.view:Flush(index, key, value)
end

function WelfareWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCWelfareInfo, "OnWelfareInfo") 							-- 福利信息

	self:RegisterProtocol(SCWelfareFindBackList, "OnSCWelfareFindBackList")			-- 日常任务找回列表
	self:RegisterProtocol(SCSevenDayLoginRewardInfo, "OnSevenDayLoginRewardInfo") 	-- 七天狂欢信息
	-- self:RegisterProtocol(SCDailyFindItemChange, "OnDailyFindItemChange") 			-- 日常找回单项变更

	--self:RegisterProtocol(SCOnlineRewardInfo, "OnOnlineRewardInfo") 				-- 在线奖励信息

	-- self:RegisterProtocol(SCFriendinviteNotice, "OnFriendinviteNotice")				-- 好友邀请通知
	self:RegisterProtocol(SyncFriendInviteInfo, "OnSyncFriendInviteInfo")			-- 好友邀请全部信息

	self:RegisterProtocol(SCSetChongJiGiftCount,"OnUpdateLevel")				-- 冲级福利
	self:RegisterProtocol(CSGetChongJiGiftCount)								-- 冲级福利请求
	self:RegisterProtocol(CSQiFuReq) 					                            -- 祈福

	self:RegisterProtocol(SCWelfareExchangeRewardFlag, "OnSCWelfareExchangeRewardFlag")	-- 福利兑换返回
	self:RegisterProtocol(CSWelfareGetExchangeRewardFlag)									-- 福利兑换
	self:RegisterProtocol(CSWelfareUplevelReward)									-- 升级福利
	self:RegisterProtocol(CSFetchSevenDayLoginReward) 								-- 领取七日狂欢
	self:RegisterProtocol(CSWelfareSignInReward) 									-- 请求领取签到
	self:RegisterProtocol(CSWelfareSignInFindBack) 									-- 请求签到找回
	self:RegisterProtocol(CSWelfareLeiJiReward)										-- 请求领取累计签到奖励
	self:RegisterProtocol(CSGetOfflineExp) 											-- 获取离线经验
	self:RegisterProtocol(CSGetDailyFindWelfare) 									-- 获取日常找回
	self:RegisterProtocol(CSWelfareActivityFind) 									-- 请求活动找回
	self:RegisterProtocol(CSFetchOnlineReward) 										-- 领取在线奖励
	self:RegisterProtocol(CSOnlineRewardInfo) 										-- 在线奖励信息请求

	-- self:RegisterProtocol(CSFriendinviteOperate)									-- 请求好友

	self:RegisterProtocol(CSUpdateNoticeFetchReward)								-- 更新公告领取奖励请求

	self:RegisterProtocol(SCGuildRedpaperAllInfo, "OnSCGuildRedpaperAllInfo")	    -- 仙盟红包信息
	self:RegisterProtocol(SCGuildRedpaperInfoChange, "OnSCGuildRedpaperInfoChange")	-- 仙盟红包信息改变
	self:RegisterProtocol(SCWorldRedpaperAllInfo, "OnSCWorldRedpaperAllInfo")	    -- 世界红包信息
	self:RegisterProtocol(SCWorldRedpaperInfoChange, "OnSCWorldRedpaperInfoChange")	-- 世界红包信息改变
	self:RegisterProtocol(SCUserRedpaperSelfInfo, "OnSCUserRedpaperSelfInfo")	    -- 个人系统红包信息
	self:RegisterProtocol(SCRedpaperDistributeRet, "RedpaperDistributeRet")	        -- 发放红包后返回 展示领取记录
	self:RegisterProtocol(CSRedpaperOperaReq)                                       -- 红包操作请求
	self:RegisterProtocol(SCGuildSystemRedpaperInfo, "OnSCGuildSystemRedpaperInfo") -- 系统红包信息

	--在线奖励
	self:RegisterProtocol(SCOnlineGiftInfo, "OnSCOnlineGiftInfo")
	self:RegisterProtocol(CSOnlineGiftOperReq)
	-- F2福利等级和Vip礼包
	self:RegisterProtocol(SCLevelAndVIPLevelInfo, "OnSCLevelAndVIPLevelInfo")
	self:RegisterProtocol(CSLevelAndVIPLevelOper)


	self:RegisterProtocol(SCVersionUpdateNotify, "OnVersionUpdateNotify") -- 后台公告信息变化

	self:RegisterProtocol(CSLeiJiLoginOperate,"OnCSLeiJiLoginOperate")          --周签到
	self:RegisterProtocol(SCLeiJiLoginInfo,"OnSCLeiJiLoginInfo")
end

function WelfareWGCtrl:OnRoleAttrValueChange(key, value , old_value)
	if key == "level" then
		-- -- Remind.Instance:DoRemind(RemindId.welfare_levelup)
	end
end

-- function WelfareWGCtrl:MainuiOpenCreate()
-- 	WelfareWGCtrl.Instance:CSGetUpLevel()
-- end

function WelfareWGCtrl:UpWelfareBtn()
	local day_reward = WelfareWGData.Instance:GetOpenDayReward()
end

--在线奖励信息
function WelfareWGCtrl:OnOnlineRewardInfo(protocol)
	self.data:SetFetchOnlineRewardFlag(protocol.fetch_online_reward_flag)
	self.data:SetTodayOnlineTime(protocol.today_online_time)
	self.data:SetOnlineGiftMax(protocol.online_reward_record_list)
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.ZAIXIAN)
	end
	-- -- Remind.Instance:DoRemind(RemindId.welfare_zaixian)
end

--领取在线奖励
function WelfareWGCtrl:SendFetchOnlineReward(reward_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFetchOnlineReward)
	send_protocol.reward_type = reward_type
	send_protocol:EncodeAndSend()
end

--在线奖励信息请求
function WelfareWGCtrl:SendOnlineRewardInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSOnlineRewardInfo)
	send_protocol:EncodeAndSend()
end

function WelfareWGCtrl:OnUpdateLevel(protocol)
	self.data:SetUpLevelWelfare(protocol)
end

function WelfareWGCtrl:OnWelfareInfo(protocol)
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_ZHUSHENTA_FB_INFO_REQ)

	self.data:GetOpenGameTime(protocol.opengame_days)

	-----------------------------------------------------升级福利
	local uplevel_reward_mark = bit:d2b(protocol.uplevel_reward_mark)
	local vo = self.data:GetUpGradeVo()
	vo.uplevel_reward_mark = uplevel_reward_mark
	self.data:UpdataUpGrade()

	local vip_reward_mark = bit:d2b(protocol.vip_reward_mark)
	local vipvo = self.data:GetVipGiftVo()
	vipvo.vipgift_reward_mark = vip_reward_mark
	self.data:UpdataVipGift()
	if not IS_ON_CROSSSERVER then
		RemindManager.Instance:Fire(RemindName.WelfareChongJi)--冲级
		RemindManager.Instance:Fire(RemindName.WelfareVipGift)--专属
		RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
	end

	RemindManager.Instance:Fire(RemindName.BiZuo) --日常主按钮

	--------------------------------------------------------------每月签到
	local qiandaovo = {}
	qiandaovo.cur_month = tonumber(os.date("%m", TimeWGCtrl.Instance:GetServerTime())) or 1
	-- qiandaovo.to_day = tonumber(os.date("%d", TimeWGCtrl.Instance:GetServerTime())) or 1
	-- qiandaovo.cur_day = math.min(tonumber(os.date("%d", TimeWGCtrl.Instance:GetServerTime())),31)
	local open_day = protocol.opengame_days
	local cur_qiandao_day = open_day%28
	qiandaovo.to_day = cur_qiandao_day == 0 and 28 or cur_qiandao_day
	qiandaovo.cur_day = cur_qiandao_day == 0 and 28 or cur_qiandao_day
	qiandaovo.sign_in_days = protocol.sign_in_days
	qiandaovo.sign_in_reward_mark = bit:d2b(protocol.sign_in_reward_mark)
	qiandaovo.day_chongzhi_sign_in_reward_mark = bit:d2b(protocol.day_chongzhi_sign_in_reward_mark)
	qiandaovo.day_sign_in_runtime_display_flag = bit:d2b(protocol.day_sign_in_runtime_display_flag)
	-- print_error("客户端显示用：",qiandaovo.day_sign_in_runtime_display_flag)
	local offset_day = qiandaovo.cur_day - protocol.sign_in_days
	if offset_day <= 0 then
		offset_day = 0
	end
	qiandaovo.offset_day = offset_day
	qiandaovo.leiji_reward_flag = bit:d2b(protocol.leiji_reward_flag)
	qiandaovo.fetch_leiji_reward_flag = bit:d2b(protocol.fetch_leiji_reward_flag)
	self.data:SetQianDaoInfo(qiandaovo)
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.QIANDAO)
	end

	self:CheckIsOpenQianDao()

	ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView, nil, "flush_cell")
end

function WelfareWGCtrl:CheckIsOpenQianDao()
	if not self.view:IsOpen() and not self.open_qiandao then
		self.open_qiandao = true
		local other_cfg_data = self.data:GetWelfareOtherCfg()[1]
		local qiandao_cfg = self.data:GetQianDaoItemCfg()
		local is_can_qiandao = false
		if qiandao_cfg then
			for k,v in pairs(qiandao_cfg) do
				if v.flag == QIANDDAO_STATUS.QIANDAO then
					is_can_qiandao = true
				end
			end
		end

		if other_cfg_data.retroactive_open == 1 and is_can_qiandao then
			local callback = function()
				ViewManager.Instance:Open(GuideModuleName.Welfare, TabIndex.welfare_qiandao)
			end

			if MainuiWGCtrl.Instance:IsLoadMainUiView() then
				callback()
			else
				MainuiWGCtrl.Instance:AddInitCallBack(nil, callback)
			end
		end
	end
end

function WelfareWGCtrl:DaycountChange()
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.QIANDAO)
	end
end

function WelfareWGCtrl:OnSCWelfareFindBackList(protocol)
	--日常任务找回
	self.data:ResetDailyFindItems()
	local dailyfindvo

	local temp_list = {}

	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for i = 1, protocol.daily_findback_count do
		local find_type = protocol.daily_findback_list[i].dailyfind_type

		local dailyfind_cfg = self.data:GetNewDailyFindCfg(NEW_DAILYFIND_TYPE.TASK_FIND, find_type)

		if dailyfind_cfg and dailyfind_cfg.is_open > 0 then
			if not temp_list[find_type] then
				temp_list[find_type] = {}
				temp_list[find_type].dailyfindvo = WelfareWGData.CreateDailyFindVo()
				temp_list[find_type].dailyfindvo.plusvo = dailyfind_cfg
				temp_list[find_type].dailyfindvo.times = protocol.daily_findback_list[i].can_find_times
				temp_list[find_type].dailyfindvo.small_type = protocol.daily_findback_list[i].dailyfind_type
				temp_list[find_type].dailyfindvo.big_type = NEW_DAILYFIND_TYPE.TASK_FIND
				temp_list[find_type].dailyfindvo.vip_times = protocol.daily_findback_list[i].vip_find_times

				temp_list[find_type].dailyfindvo.opengame_day = protocol.daily_findback_list[i].opengame_day or 0
				temp_list[find_type].dailyfindvo.role_level = protocol.daily_findback_list[i].role_level or 0

			else
				local times = temp_list[find_type].dailyfindvo.times + protocol.daily_findback_list[i].can_find_times
				local vip_times = temp_list[find_type].dailyfindvo.vip_times + protocol.daily_findback_list[i].vip_find_times
				local opengame_day = protocol.daily_findback_list[i].opengame_day
				local role_level = protocol.daily_findback_list[i].role_level

				temp_list[find_type].dailyfindvo.plusvo = dailyfind_cfg
				temp_list[find_type].dailyfindvo.times = times
				temp_list[find_type].dailyfindvo.vip_times = vip_times
				--取天数小的天数跟等级拿奖励
				temp_list[find_type].dailyfindvo.opengame_day = temp_list[find_type].dailyfindvo.opengame_day > opengame_day and opengame_day or temp_list[find_type].dailyfindvo.opengame_day
				temp_list[find_type].dailyfindvo.role_level = temp_list[find_type].dailyfindvo.opengame_day > opengame_day and role_level or temp_list[find_type].dailyfindvo.role_level
			end
		end
	end

	for k,v in pairs(temp_list) do
		self.data:AddDailyFindItem(v.dailyfindvo)
	end

	--日常活动
	local activity_join_flag = bit:d2b(protocol.activity_join_flag)						-- 活动参与标记
	local auto_activity_flag = bit:d2b(protocol.auto_activity_flag)						-- 活动委托标记

	local activity_find_flag_display_1 = bit:d2b(protocol.activity_find_flag_display_list[1])		-- 活动找回标记（显示用）第一天
	local activity_find_flag_display_2 = bit:d2b(protocol.activity_find_flag_display_list[2])		-- 活动找回标记（显示用）第二天

	local level_list = protocol.activity_find_role_level								-- 活动找回当天等级
	local activity_find_flag = {}
	activity_find_flag[1] = bit:d2b(protocol.activity_find_flag_list[1])						-- 活动找回标记第一天
	activity_find_flag[2] = bit:d2b(protocol.activity_find_flag_list[2])						-- 活动找回标记第二天

	local act_config = self.data:GetNewDailyFindCfgByDailyFind(NEW_DAILYFIND_TYPE.ACTIVITY_FIND)
	for k, v in pairs(act_config) do

		if (activity_find_flag_display_1[32 - v.find_type] == 1 or activity_find_flag_display_2[32 - v.find_type] == 1 ) and
			activity_join_flag[32 - v.find_type] == 0 and
			auto_activity_flag[32 - v.find_type] == 0 then
			local times = 0

			for i=1,2 do
				if activity_find_flag[i][32 - v.find_type] == 1 then
					times = times + 1
				end
			end

			dailyfindvo = WelfareWGData.CreateDailyFindVo()
			dailyfindvo.plusvo = v
			dailyfindvo.small_type = v.find_type
			dailyfindvo.big_type = NEW_DAILYFIND_TYPE.ACTIVITY_FIND
			dailyfindvo.times = times
			dailyfindvo.vip_times = 0
			local limit_level = ActivityWGData.Instance:GetActivityLimitLevelById(v.activity_type)

			for i=1,2 do
				if level_list[i] > 0 and level_list[i] >= limit_level then
					local day = i == 1 and 2 or 1
					dailyfindvo.opengame_day = cur_open_day - day
					dailyfindvo.role_level = level_list[i]
					break
				end
			end

			if v.is_open > 0 and dailyfindvo.role_level > 0 then
				self.data:AddDailyFindItem(dailyfindvo)
			end
		end
	end
	if BiZuoWGCtrl.Instance.view:IsOpen() then
		BiZuoWGCtrl.Instance.view:Flush(BiZuoView.TabIndex.DAILYFIND)
	end

	RemindManager.Instance:Fire(RemindName.BiZuo) --日常主按钮
	RemindManager.Instance:Fire(RemindName.ZhaoHui)--找回
end

--七日狂欢
function WelfareWGCtrl:OnSevenDayLoginRewardInfo(protocol)
	local sevendayvo = self.data:GetSevenDayVo()
	sevendayvo.notify_reason = protocol.notify_reason
	sevendayvo.account_total_login_daycount = protocol.account_total_login_daycount
	sevendayvo.seven_day_login_fetch_reward_mark = bit:d2b(protocol.seven_day_login_fetch_reward_mark)
	local fetch_flag = bit:b2d(sevendayvo.seven_day_login_fetch_reward_mark)
	if fetch_flag == 254 then
		self.data.is_seven_day_end = true
	else
		self.data.is_seven_day_end = false
	end
	self:UpdataCarnivalMaiuiIcon()

	self.data:UpdateSevenDayStatus()
	self:FlushKuangHuanView()
	self.data:SetLoginDay(protocol.account_total_login_daycount)
	self:UpWelfareBtn()
	MainuiWGCtrl.Instance:FlushView(0,"seven_day_icon")
end

function WelfareWGCtrl:FlushKuangHuanView()
	if ViewManager.Instance:IsOpen(GuideModuleName.SevenDay) then
		SevenDayWGCtrl.Instance:FlushSevenDayView()
	end
end

function WelfareWGCtrl:UpdataCarnivalMaiuiIcon()
	if not self.data:CheckIconVisible() then
		FunOpen.Instance:ForceCloseFunByName(FunName.Carnival)
	else
		FunOpen.Instance:ForceOpenFunByName(FunName.Carnival)
	end
end

--请求升级福利
function WelfareWGCtrl:CSGetUpLevel()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetChongJiGiftCount)
	protocol:EncodeAndSend()
end



-- function WelfareWGCtrl:OnDailyFindItemChange(protocol)
-- 	if protocol.result == 1 then
-- 		self.data:RemoveDailyFindItem(DAILYFIND_TYPE.TASK, protocol.dailyfind_type)
-- 	end
-- 	self.view:Flush(WELFARE_TYPE.DAILYFIND)
-- end


--发送领取十五天奖励协议
function WelfareWGCtrl:SendFetchSevenDayLoginReward(fetch_day)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFetchSevenDayLoginReward)
	protocol.fetch_day = fetch_day
	protocol:EncodeAndSend()
end

function WelfareWGCtrl:SendWelfareUplevelReward(seq,type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWelfareUplevelReward)
	protocol.seq = seq
	protocol.type = type
	protocol.qifu_times = 0
	protocol:EncodeAndSend()
end

--0只用免费 1 时优先用免费，不足再用元宝 ,最后个参数是次数
function WelfareWGCtrl:SendQiFuReq(type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSQiFuReq)
	protocol.type = type
	protocol:EncodeAndSend()
end

function WelfareWGCtrl:SendWelfareSignInReward(day, type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWelfareSignInReward)
	protocol.day = day
	protocol.type = type
	protocol:EncodeAndSend()
end

function WelfareWGCtrl:SendWelfareSignInFindBack(day)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWelfareSignInFindBack)
	protocol.day = day
	protocol.reserve_sh = 0
	protocol:EncodeAndSend()
end

function WelfareWGCtrl:SendGetOfflineExp(type, get_offline_hours)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetOfflineExp)
	protocol.type = type
	protocol.get_offline_hours = get_offline_hours
	protocol:EncodeAndSend()
end

function WelfareWGCtrl:SendGetDailyFindWelfare(dailyfind_type, get_type,count,is_vip)--2753
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetDailyFindWelfare)
	protocol.dailyfind_type = dailyfind_type
	protocol.get_type = get_type or 0
	protocol.count = count or 0
	protocol.is_vip = is_vip or 0
	protocol:EncodeAndSend()
end

function WelfareWGCtrl:SendWelfareActivityFind(find_type, is_free, find_times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWelfareActivityFind)--2758
	protocol.find_type = find_type
	protocol.is_free = is_free
	protocol.find_times = find_times
	protocol:EncodeAndSend()
end

-- 领取累计签到奖励
function WelfareWGCtrl:SendQianDaoOperate(index)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSWelfareLeiJiReward)
	send_protocol.index = index or 0
	send_protocol:EncodeAndSend()
end

------------------------------------------------------------------------------------------------
--更新公告信息
function WelfareWGCtrl:OnUpdateNoticeInfo(protocol)
	self.data:SetUpdateNoticeInfo(protocol)

	if not self.data:CanFetchReward() then
		FunOpen.Instance:ForceCloseFunByName(FunName.UpdateAffiche)
	end
	-- 第一个版本不显示公告
	if protocol.server_version == 0 then
		FunOpen.Instance:ForceCloseFunByName(FunName.UpdateAffichezonghe)
	end
end

function WelfareWGCtrl:OnFlushWelfareGongGao()
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.GONGGAO)
	end
end

-- 更新公告可领取奖励时进入游戏直接弹窗
function WelfareWGCtrl:UpdateAfficheOpen()
	if not IS_ON_CROSSSERVER and self.need_auto_open and FunOpen.Instance:GetFunIsOpened(FunName.UpdateAffiche) then
		self.need_auto_open = false
		self.view:Open(WELFARE_TYPE.GONGGAO)
	end
end

-- 发送领取奖励请求
function WelfareWGCtrl:SendUpdateNoticeFetchReward()
	local protocol = ProtocolPool.Instance:GetProtocol(CSUpdateNoticeFetchReward)
	protocol:EncodeAndSend()
end

-----------------------------------合f1公告start--------------------
-- 获取
function WelfareWGCtrl:GetNotifyInfo()
	if IS_AUDIT_VERSION then
		return
	end
	--返回
	local verify_callback = function(url, arg, data, size)
		local ret_t = cjson.decode(data)
		if ret_t and ret_t.ret == 0 and ret_t.data and type(ret_t.data) == "table" and WelfareWGData.Instance then
			WelfareWGData.Instance:SetNotifyInfo(ret_t.data)
			self.view:Flush(WELFARE_TYPE.GONGGAO)
			RemindManager.Instance:Fire(RemindName.WelfareGongGao)--公告
			RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
		end
	end

	local spid = CHANNEL_AGENT_ID
	local user_id = "_"
	local server_id = "_"
	local role_id = "_"
	if GameVoManager ~= nil and GameVoManager.Instance ~= nil then
		local user_vo = GameVoManager.Instance:GetUserVo()
		if user_vo ~= nil then
			if user_vo.account_user_id ~= nil and user_vo.account_user_id ~= "" then
				user_id = user_vo.account_user_id
			end

			if user_vo.plat_server_id ~= nil and user_vo.plat_server_id ~= "" then
				server_id = user_vo.plat_server_id
			end
		end

		local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
		if main_role_vo ~= nil then
			if main_role_vo.role_id ~= nil and main_role_vo.role_id ~= "" then
				role_id = main_role_vo.origin_uid > 0 and main_role_vo.origin_uid or main_role_vo.role_id
			end
		end
	end
	local now_server_time = os.time()
	local key = "bd1e3dbcf76412dec9b8a80e05e24757"
	local sign = MD52.GetMD5(spid .. user_id .. server_id .. role_id .. now_server_time .. key)

	local real_url = string.format("%s/api/c2s/update_notice.php?spid=%s&user_id=%s&server_id=%s&role_id=%s&time=%s&sign=%s",
									GlobalUrl, spid, user_id, server_id, role_id, tostring(now_server_time), sign)
	HttpClient:Request(real_url, verify_callback)
end
-- 获取奖励
function WelfareWGCtrl:GetNotifyReward()
	if IS_AUDIT_VERSION then
		return
	end
	--返回
	local verify_callback = function(url, arg, data, size)
		local ret_t = cjson.decode(data)
		if ret_t and ret_t.ret == 0 and WelfareWGData.Instance then
			WelfareWGData.Instance:NotifyRewardGetSuc()
			self.view:Flush(WELFARE_TYPE.GONGGAO)
			RemindManager.Instance:Fire(RemindName.WelfareGongGao)--公告
			RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
		end
	end

	local spid = CHANNEL_AGENT_ID
	local user_id = "_"
	local server_id = "_"
	local role_id = "_"
	local info = WelfareWGData.Instance:GetNotifyInfo()
	local version = info.version
	if GameVoManager ~= nil and GameVoManager.Instance ~= nil then
		local user_vo = GameVoManager.Instance:GetUserVo()
		if user_vo ~= nil then
			if user_vo.account_user_id ~= nil and user_vo.account_user_id ~= "" then
				user_id = user_vo.account_user_id
			end

			if user_vo.plat_server_id ~= nil and user_vo.plat_server_id ~= "" then
				server_id = user_vo.plat_server_id
			end
		end

		local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
		if main_role_vo ~= nil then
			if main_role_vo.role_id ~= nil and main_role_vo.role_id ~= "" then
				role_id = main_role_vo.origin_uid > 0 and main_role_vo.origin_uid or main_role_vo.role_id
			end
		end
	end
	local now_server_time = os.time()
	local key = "bd1e3dbcf76412dec9b8a80e05e24757"
	local sign = MD52.GetMD5(spid .. user_id .. server_id .. role_id .. version .. now_server_time .. key)
	local real_url = string.format("%s/api/c2s/receive_awards.php?spid=%s&user_id=%s&server_id=%s&role_id=%s&version=%s&time=%s&sign=%s",
									GlobalUrl, spid, user_id, server_id, role_id, version, tostring(now_server_time), sign)
	HttpClient:Request(real_url, verify_callback)
end

function WelfareWGCtrl:OnVersionUpdateNotify(protocol)
	WelfareWGData.Instance:SetNotifyVersion(protocol.version)
	self.view:Flush(WELFARE_TYPE.GONGGAO)
	RemindManager.Instance:Fire(RemindName.WelfareGongGao)--公告
	RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
end

-----------------------------------合f1公告end---------------------------------

-----------------------------------------------------------------------------------------------

function WelfareWGCtrl:OpenWelfareSetdata(index)
	self.welfarefriend_list:SetData(index)
end




--好友邀请功能------
-- 好友邀请通知
function WelfareWGCtrl:OnFriendinviteNotice(protocol)
	-- print_error("protocol:::",protocol)
	self.data:SetFriendinviteNotice(protocol)
	if protocol.notice_type == FRIENDINVITE_NOTICE.REQUEST_INVITE then
		local role_info = {role_id = protocol.operate_role_id, role_name = protocol.operate_role_name}
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIENDYAOQIN, 1, BindTool.Bind2(self.AlertFriendinviteNotice, self, role_info))
	end
	-- Remind.Instance:DoRemind(RemindId.welfare_friend)
end

function WelfareWGCtrl:AlertFriendinviteNotice(role_info)
	if nil == self.pop_alart then
		self.pop_alart = Alert.New(nil, nil, nil, nil, false)
	end
	self.pop_alart:SetLableString(string.format(Language.Welfare.FriendYaoqingTip, role_info.role_name))
	self.pop_alart:SetOkFunc(BindTool.Bind1(function ()
		self:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_AGREE_INVITE,role_info.role_id)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIENDYAOQIN, 0)
	end, self))
	self.pop_alart:SetCancelFunc(BindTool.Bind1(function ()
		self:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REFUSE_INVITE,role_info.role_id)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIENDYAOQIN, 0)
	end, self))

	self.pop_alart:Open()
end

-- -- 好友邀请全部信息
function WelfareWGCtrl:OnSyncFriendInviteInfo(protocol)
	-- print_error("protocol:::",protocol)
	self.data:SetSyncFriendInviteInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.FRIEND)
	end
	WelfareWGData.Instance:UpdataReward()
	-- Remind.Instance:DoRemind(RemindId.welfare_friend)
end

function WelfareWGCtrl:SendFriendinviteOperate(operate, param1, param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSFriendinviteOperate)
	send_protocol.operate = operate or 0
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end


function WelfareWGCtrl:IsShowSevenServerRedPoint()
	local day_list = self.data:GetSevenDayCfg()
	if day_list then
		for k,v in pairs(day_list) do
			if v.status == COMPLETE_STATUS.WEILINGQU then
				return true
			end
		end
	end
	return false
end
--补签
function WelfareWGCtrl:OnclcikQianDaoItem(day, cell)
	self.view:OnclcikQianDaoItem(day, cell)
end

function WelfareWGCtrl:WelfareClose()
	if self.view:IsOpen() then
		self.view:Close()
	end
end

 -- 仙盟红包信息
function WelfareWGCtrl:OnSCGuildRedpaperAllInfo(protocol)
	self.data:SetGuildRedpaperAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_redpacket)
	GuildWGCtrl.Instance:GuildRedPacketTipsFlush()
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_RedBag)
	MainuiWGCtrl.Instance:ShowRedBtn()
end
-- 仙盟红包信息改变
function WelfareWGCtrl:OnSCGuildRedpaperInfoChange(protocol)
	self.data:GuildRedpaperAllInfoChange(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_redpacket)
	GuildWGCtrl.Instance:GuildRedPacketTipsFlush()
	MainuiWGCtrl.Instance:ShowRedBtn()
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_RedBag)
end
 -- 世界红包信息
function WelfareWGCtrl:OnSCWorldRedpaperAllInfo(protocol)
	self.data:SetWorldRedpaperAllInfo(protocol)
	--self.view:Flush(TabIndex.world_paper)
	if self.paper_view:IsOpen() then
		self.paper_view:Flush()
	end
	GuildWGCtrl.Instance:GuildRedPacketTipsFlush()
--	RemindManager.Instance:Fire(RemindName.WelfarePaper)
	MainuiWGCtrl.Instance:ShowRedBtn()
	RemindManager.Instance:Fire(RemindName.XianLingGuZhen_QMHB)
	XianLingGuZhenWGCtrl.Instance:FlushQMFLView()
end
-- 世界红包信息改变
function WelfareWGCtrl:OnSCWorldRedpaperInfoChange(protocol)
	self.data:WorldRedpaperAllInfoChange(protocol)
	--self.view:Flush(TabIndex.world_paper)
	if self.paper_view:IsOpen() then
		self.paper_view:Flush()
	end
	GuildWGCtrl.Instance:GuildRedPacketTipsFlush()
--	RemindManager.Instance:Fire(RemindName.WelfarePaper)
	MainuiWGCtrl.Instance:ShowRedBtn()
	RemindManager.Instance:Fire(RemindName.XianLingGuZhen_QMHB)
	XianLingGuZhenWGCtrl.Instance:FlushQMFLView()
end
-- 个人系统红包信息
function WelfareWGCtrl:OnSCUserRedpaperSelfInfo(protocol)
	self.data:SetPersonRedpaperInfo(protocol)
	--self.view:Flush(TabIndex.world_paper)
	if self.paper_view:IsOpen() then
		self.paper_view:Flush()
	end
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_redpacket)
	GuildWGCtrl.Instance:GuildRedPacketTipsFlush()
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_RedBag)
	--RemindManager.Instance:Fire(RemindName.WelfarePaper)
	MainuiWGCtrl.Instance:ShowRedBtn()
	RemindManager.Instance:Fire(RemindName.XianLingGuZhen_QMHB)
	XianLingGuZhenWGCtrl.Instance:FlushQMFLView()
end

-- 系统红包信息
function WelfareWGCtrl:OnSCGuildSystemRedpaperInfo(protocol)
	self.data:SetSystemRedpaperInfo(protocol)
	if self.paper_view:IsOpen() then
		self.paper_view:Flush()
	end
	ViewManager.Instance:FlushView(GuideModuleName.GuildView, TabIndex.guild_redpacket)
	GuildWGCtrl.Instance:GuildRedPacketTipsFlush()
	RemindManager.Instance:Fire(RemindName.Guild_ZongLan_RedBag)
	MainuiWGCtrl.Instance:ShowRedBtn()
end
-- REDPAPER_OPERA_TYPE_SELF_INFO = 0,      	 --个人红包信息
--  REDPAPER_OPERA_TYPE_GET_TOTAL = 1,    		 -- 领取累计奖励
--  REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM = 2, 	 -- 系统红包 0 世界  1 是仙盟-_-
--  REDPAPER_OPERA_TYPE_DISTRIBUTE = 3,      	 -- 手动红包
--  REDPAPER_OPERA_TYPE_RECEIVE_GUILD = 4,    	 -- 领取仙盟红包 param1:索引
--  REDPAPER_OPERA_TYPE_RECEIVE_WORLD = 5,    	 -- 领取世界红包 param1:索引
-- 红包操作请求
function WelfareWGCtrl:OnCSRedpaperOperaReq(opera_type,param1,param2,param3)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSRedpaperOperaReq)
	send_protocol.opera_type = opera_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol.param3 = param3 or ""
	send_protocol:EncodeAndSend()
end
function WelfareWGCtrl:RedpaperDistributeRet(protocol)
	WelfareWGData.Instance:OpenRecordPanelByProto(protocol)
end

-- 在线奖励
function WelfareWGCtrl:SendOnlineGiftOperReq(opera_type,param1,param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSOnlineGiftOperReq)
	send_protocol.opera_type = opera_type or 0
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end

-- 兑换
function WelfareWGCtrl:SendGetExchangeRewardReq(session_key,param1,param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSWelfareGetExchangeRewardFlag)
	send_protocol.session_key =  nil ~= session_key and session_key or ""
	send_protocol:EncodeAndSend()
end

-- 福利兑换返回
function WelfareWGCtrl:OnSCWelfareExchangeRewardFlag(protocol)
	WelfareWGData.Instance:SetExchangeRewardFlag(protocol)
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.LIBAO)
	end
end

function WelfareWGCtrl:OnSCOnlineGiftInfo(protocol)
	WelfareWGData.Instance:SetOnlineGiftInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(WELFARE_TYPE.ONLINEREWARD)
	end
	--RemindManager.Instance:Fire(RemindName.WelfareOnlineGift)
	self:SetOnLineEndTime()
end
--周签到
function WelfareWGCtrl:OnCSLeiJiLoginOperate(operate_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLeiJiLoginOperate)
	protocol.operate_type = operate_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function WelfareWGCtrl:OnSCLeiJiLoginInfo(protocol)
	--print_error("累计登录", protocol)
	self.data:SetAllInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.AccumulativeLogin)
	RemindManager.Instance:Fire(RemindName.Welfare)
end

function WelfareWGCtrl:OnPassDay()
	self:OnCSLeiJiLoginOperate(LEIJI_LOGIN_OPERATE_TYPE.INFO)
end

---[[F2福利等级和Vip礼包
function WelfareWGCtrl:OnSCLevelAndVIPLevelInfo(protocol)
	WelfareWGData.Instance:SetUpLevelAndVIPGiftInfo(protocol)
	self.view:Flush(WELFARE_TYPE.SHENGJI)
    self.view:Flush(WELFARE_TYPE.VIPGIFT)
    MainuiWGCtrl.Instance:FlushView(0, "level_gift")
	RemindManager.Instance:Fire(RemindName.WelfareLevelGift)
	RemindManager.Instance:Fire(RemindName.WelfareVipGift)
end

function WelfareWGCtrl:RequestLevelAndVIPGift(reward_index, reward_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSLevelAndVIPLevelOper)
	send_protocol.reward_index = reward_index or 0
	send_protocol.reward_type = reward_type or 0
	send_protocol:EncodeAndSend()
end
--]]

function WelfareWGCtrl:SetOnLineEndTime()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local open_level = WelfareWGData.Instance:GetOneOpenLevel()
	local online_time = WelfareWGData.Instance:GetOnlineTime()
	if role_level < open_level then
		return
	end
	local need_time = self.data:GetOneEndTime()
	if need_time == 0 then
		return
	end

	local less_time = need_time - online_time
	if less_time <= 0 then
		return
	end
	local end_time = TimeWGCtrl.Instance:GetServerTime() + less_time
	if CountDownManager.Instance:HasCountDown("online_gift_end_time") then
		CountDownManager.Instance:RemoveCountDown("online_gift_end_time")
	end
	CountDownManager.Instance:AddCountDown("online_gift_end_time", nil, BindTool.Bind1(self.CompleteEndCallBack, self), end_time, nil, 1)
end

function WelfareWGCtrl:CompleteEndCallBack()
	self:SendOnlineGiftOperReq(ONLINE_GIFT_OPERA_TYPE.ONLINE_GIFT_OPERA_TYPE_ASK_INFO)
end


function WelfareWGCtrl:MainuiOpenCreate()
	if WelfareWGData.Instance:GetCanRewardDay() > SEVEN_DAY_LOGIN_MAX_REWARD_DAY then
		local seven_day_is_over = 1
		RoleWGData.SetRolePlayerPrefsInt("seven_day_over", seven_day_is_over)
        MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnSeventDayView", false)
    else
    	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.SevenDay)
    	MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnSeventDayView", is_open)
    end
end

----------------------------------------------------手机绑定start------------------------
-- function WelfareWGCtrl:IsPhoneBindOpen()
-- 	return self.data:IsPhoneBindOpen()
-- end

--绑定了 并且 奖励没领取 表示可领取
-- function WelfareWGCtrl:IsPhoneBindCanGetReward()
-- 	return not self.data:IsPhoneBindGotReward() and self:IsPhoneBinded()
-- end


-- function WelfareWGCtrl:SetIsPhoneBinded(b)
-- 	self.data:SetIsPhoneBinded(b)
-- 	if self.view then
-- 		self.view:RefreshPhoneBind()
-- 	end
-- 	RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
-- end

-- function WelfareWGCtrl:IsPhoneBinded()
-- 	return self.data:IsPhoneBinded()
-- end

-- function WelfareWGCtrl:RequestPhoneBindInfo()
-- 	if IS_AUDIT_VERSION then
-- 		return
-- 	end
-- 	--请求SDK 是否已经绑定手机
-- 	if AgentAdapter and AgentAdapter.Instance and not self:IsPhoneBinded() then
-- 		AgentAdapter.Instance:RequestPhoneBindStatus()
-- 	end

-- 	--请求该活动是否开启
-- 	local spid = CHANNEL_AGENT_ID
-- 	local cur_time = math.floor(TimeWGCtrl.Instance:GetServerTime())

-- 	local url_get_is_open = "%s?spid=%s&time=%s&sign=%s"
-- 	local str_open_md5 = MD52.GetMD5(string.format("%s%s%s", spid, cur_time, "bd1e3dbcf76412dec9b8a80e05e24757"))

-- 	local is_open_bindphone_url = GLOBAL_CONFIG.param_list.is_open_bindphone_url
-- 	if not is_open_bindphone_url or is_open_bindphone_url == '' then
-- 		return 
-- 	end

-- 	url_get_is_open = string.format(url_get_is_open, is_open_bindphone_url, spid, cur_time, str_open_md5)

-- 	HttpClient:Request(url_get_is_open, function (url, is_succ, data)
-- 		local json_data = cjson.decode(data)
-- 		if not json_data then
-- 			return
-- 		end
-- 		if not json_data.data or not self.data then
-- 			return
-- 		end
-- 		self.data:SetPhoneBindOpen(json_data.data.status == 1) --0关闭 1开启
-- 		self.view:updatePhoneBindTab()
-- 		RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
-- 	end)

-- 	--查询玩家奖励状态
-- 	local account_user_id = GameVoManager.Instance:GetUserVo().account_user_id

-- 	local url_check_award = "%s?spid=%s&user_id=%s&time=%s&sign=%s"
-- 	local str_check_md5 = MD52.GetMD5(string.format("%s%s%s%s", spid, account_user_id, cur_time, "bd1e3dbcf76412dec9b8a80e05e24757"))

-- 	local check_award_bindphone_url = GLOBAL_CONFIG.param_list.check_award_bindphone_url
-- 	if not check_award_bindphone_url or check_award_bindphone_url == '' then
-- 		return 
-- 	end

-- 	url_check_award = string.format(url_check_award, check_award_bindphone_url, spid, account_user_id, cur_time, str_check_md5)

-- 	HttpClient:Request(url_check_award, function (url, is_succ, data)
-- 		local json_data = cjson.decode(data)
-- 		if not json_data then
-- 			return
-- 		end
-- 		if not json_data.data or not self.data then
-- 			return
-- 		end
-- 		self.data:SetPhoneBindGotReward(json_data.data.status == 1) --0 表示已领取， 1表示未领取
-- 		RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
-- 			self.view:RefreshPhoneBind()
-- 	end)
-- end

-- function WelfareWGCtrl:RequestPhoneBindAward()
-- 	if IS_AUDIT_VERSION then
-- 		return
-- 	end
-- 	local spid = CHANNEL_AGENT_ID
-- 	local cur_time = math.floor(TimeWGCtrl.Instance:GetServerTime())
-- 	local user_vo = GameVoManager.Instance:GetUserVo()
-- 	local role_vo = GameVoManager.Instance:GetMainRoleVo()
-- 	local user_id = user_vo.account_user_id
-- 	local role_id = role_vo.role_id
-- 	local server_id = role_vo.server_id
-- 	local str_md5 = MD52.GetMD5(string.format("%s%s%s%s%s%s", spid, user_id, server_id, role_id, cur_time, "bd1e3dbcf76412dec9b8a80e05e24757"))

-- 	local url_get_reward = "%s?spid=%s&user_id=%s&server_id=%s&role_id=%s&time=%s&sign=%s"
-- 	local save_award_bindphone_url = GLOBAL_CONFIG.param_list.save_award_bindphone_url
-- 	if not save_award_bindphone_url or save_award_bindphone_url == '' then
-- 		return 
-- 	end

-- 	url_get_reward = string.format(url_get_reward, save_award_bindphone_url, spid, user_id, server_id, role_id, cur_time, str_md5)
-- 	HttpClient:Request(url_get_reward, function (url, is_succ, data)
-- 		local json_data = cjson.decode(data)
-- 		if not json_data then
-- 			return
-- 		end

-- 		if json_data.ret == 0 then  --0成功领取 其他 失败
-- 			self.data:SetPhoneBindGotReward(true)
-- 			self.view:RefreshPhoneBind()
-- 			RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
-- 		else
-- 			print_error(json_data.msg)
-- 		end
-- 	end)
-- end

-- -------------------------------------------手机绑定end-----------------------------------------




-------------------------------------------手机绑定New-----------------------------------------
-- php请求绑定手机验证
function WelfareWGCtrl:SendBindPhone(request_type, phone_name, code)
	-- 审核 编辑器 PC端 不请求
	if IS_AUDIT_VERSION or IS_LOCLA_WINDOWS_DEBUG_EXE or UnityEngine.Debug.isDebugBuild then
		return
	end

	local bind_phone_url = GLOBAL_CONFIG.param_list.bind_phone_url
	if bind_phone_url == nil or bind_phone_url == "" or request_type == nil then
		return
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()

	local user_id2 = user_vo.account_user_id or ""																-- 用户ID(带渠道ID的)
	local user_id = AgentAdapter ~= nil and tostring(AgentAdapter.Instance:GetUserInfo().UserID) or ""		-- 用户ID(不带渠道ID)
	user_id = user_id ~= "" and user_id or user_id2
	local server_id = main_role_vo.server_id or 0															-- 服ID
	local role_id = main_role_vo.role_id or 0																-- 角色ID
	local verify_data = AgentAdapter.Instance:GetLoginVerifyData()
	phone_name = phone_name or ""
	code = code or ""
	local time = os.time()

	local key = "bd1e3dbcf76412dec9b8a80e05e24757"
	local sign = MD52.GetMD5(request_type .. user_id .. server_id .. role_id .. verify_data .. phone_name .. code .. time .. key)
	local str = "%s?type=%s&user_id=%s&server_id=%s&role_id=%s&data=%s&phoneN=%s&code=%s&time=%s&sign=%s"
	local url = string.format(str, bind_phone_url, request_type, user_id, server_id, role_id, verify_data, phone_name, code, tostring(time), sign)

	if nil ~= self.http_bind_phone_call_back  then
		HttpClient:CancelRequest(self.http_bind_phone_call_back)
		self.http_bind_phone_call_back = nil
	end
	self.http_bind_phone_call_back = BindTool.Bind(self.OnBindPhoneCallBack, self)
	HttpClient:Request(url, self.http_bind_phone_call_back)
end

function WelfareWGCtrl:OnBindPhoneCallBack(url, is_succ, data)
	if not is_succ then
		print_error("[WelfareWGCtrl:OnBindPhoneCallBack]failed: false")
		return
	end
	local data_info = cjson.decode(data)
	if data_info == nil then
		print_error("[WelfareWGCtrl:OnBindPhoneCallBack]json format failed")
		return
	end
	if data_info.state ~= nil and data_info.state ~= 0 then
		-- print_error("[WelfareWGCtrl:OnBindPhoneCallBack]failed with code: ", data_info.state, data_info.msg)
		if data_info.msg and data_info.msg ~= "" then
			SysMsgWGCtrl.Instance:ErrorRemind(data_info.msg)
		end
		return
	end
	if data_info.msg and data_info.msg ~= "" then
		SysMsgWGCtrl.Instance:ErrorRemind(data_info.msg)
	end

	if data_info.data == nil then
		return
	end

	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	if data_info.data.type == PHONE_REQUEST_TYPE.IS_BIND then			-- 请求是否绑定返回结果
		-- is_bind是否绑定手机号 0没绑定 1绑定
		if data_info.data.is_bind then
			local is_bind_phone = tonumber(data_info.data.is_bind)
			if is_bind_phone == 1 then
				self.data:SetIsOpenHandsetVerify(false)
			end
			local is_bind_phone_reward = self.data:GetIsBindPhoneReward()
			if not is_bind_phone_reward and is_bind_phone == 1 then
				self:SendPhoneRewardRequest(data_info.data.phone or "")
			end
			self.data:SetBindPhoneState(is_bind_phone)
			GlobalEventSystem:Fire(AuditEvent.PHP_BIND_PHONE, data_info.data)
			RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
		end
	elseif data_info.data.type == PHONE_REQUEST_TYPE.GET_CODE then		-- 请求获取验证码返回结果
		if self.welfare_view then
			self.welfare_view:Flush(WELFARE_TYPE.PHONEBIND,"flush_button_verif_state")
		end
	elseif data_info.data.type == PHONE_REQUEST_TYPE.BIND then			-- 请求开始绑定手机号码返回结果
		self.data:SetBindPhoneState(1)
		if self.welfare_view then
			self.welfare_view:Flush(WELFARE_TYPE.PHONEBIND)
		end
		-- 这里要走后台领取礼包
		local bind_phone_number = self.data:GetBindPhoneNumber()
		if bind_phone_number and bind_phone_number ~= "" then
			self:SendPhoneRewardRequest(bind_phone_number)
		end
	end
end


-- 发送手机奖励请求
function WelfareWGCtrl:SendPhoneRewardRequest(phone_name)
	-- 审核 编辑器 PC端 不请求
	if IS_AUDIT_VERSION or IS_LOCLA_WINDOWS_DEBUG_EXE or UnityEngine.Debug.isDebugBuild then
		return
	end

	 local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local spid = CHANNEL_AGENT_ID
	local user_id2 = user_vo.account_user_id or ""																-- 用户ID(带渠道ID的)
	local user_id = AgentAdapter ~= nil and tostring(AgentAdapter.Instance:GetUserInfo().UserID) or ""		-- 用户ID(不带渠道ID)
	user_id = user_id ~= "" and user_id or user_id2
	local server_id = main_role_vo.server_id or 0															-- 服ID
	local role_id = main_role_vo.role_id or 0																-- 角色ID
	phone_name = phone_name or ""
	local time = os.time()

	local key = "33cc62b07ae98fffddd923b178aa0a14"
	local sign = MD52.GetMD5(spid .. user_id .. server_id .. role_id .. time .. key)

	local url = string.format("%s/api/c2s/send_phone_reward.php?spid=%s&user_id=%s&server_id=%s&role_id=%s&phone=%s&time=%s&sign=%s", 
		GlobalUrl, spid, user_id, server_id, role_id, phone_name, tostring(time), sign)
	if nil ~= self.http_phone_reward_call_back  then
		HttpClient:CancelRequest(self.http_phone_reward_call_back)
		self.http_phone_reward_call_back = nil
	end

	-- print_error("领取奖励SendPhoneRewardRequest>>>>>>>>", url)
	self.http_phone_reward_call_back = BindTool.Bind(self.OnPhoneRewardCallback, self)
	HttpClient:Request(url, self.http_phone_reward_call_back)
end

function WelfareWGCtrl:OnPhoneRewardCallback(url, is_succ, data)
	-- print_error("OnPhoneRewardCallback>>>>>>>>", url)
	if not is_succ then
		print_error("[WelfareWGCtrl:OnPhoneRewardCallback]failed: ")
		return
	end

	local info_data = cjson.decode(data)
	if info_data == nil then
		print_error("[WelfareWGCtrl:OnPhoneRewardCallback]json format failed")
		return
	end

	-- ret 状态码 0/成功 其他失败
	if info_data.ret ~= nil and info_data.ret ~= 0 then
		-- print_error("[WelfareWGCtrl:OnPhoneRewardCallback]failed with code: ", info_data.ret, info_data.msg)
		return
	end
	-- 来到这的话表示成功发奖励到玩家邮件上了

end

-- 是否绑定手机
function WelfareWGCtrl:OnIsBindPhoneCallBack(info)
	-- y_status是否绑定 true绑定 false非绑定
	if info and info.y_status == true then
		self.data:SetIsOpenHandsetVerifySdk(false)
		local is_bind_phone_reward = self.data:GetIsBindPhoneReward()
		if not is_bind_phone_reward then
			self:SendPhoneRewardRequest()
		end
		self.data:SetBindPhoneState(1)
	else
		self.data:SetBindPhoneState(0)
	end
	self.welfare_view:Flush(WELFARE_TYPE.PHONEBIND)
	RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
end

-- 是否绑定手机成功
function WelfareWGCtrl:OnIsBindPhoneSuccessCallBack(info)
	-- y_status是否绑定成功 true成功 false失败
	if info and info.y_status == true then
		self.data:SetBindPhoneState(1)
		self:SendPhoneRewardRequest()
	else
		self.data:SetBindPhoneState(0)
	end
	self.welfare_view:Flush(WELFARE_TYPE.PHONEBIND)
end

function WelfareWGCtrl:IsPhoneBindOpen()
	return self.data:IsPhoneBindOpen()
end