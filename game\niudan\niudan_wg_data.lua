NiuDanWGData = NiuDanWGData or BaseClass()

NiuDanWGData.REWARD_TYPE = {
	BIG = 1,
	NORMAL = 2,
	LOW = 3
}

function NiuDanWGData:__init()
	if NiuDanWGData.Instance then
		ErrorLog("[NiuDanWGData] Attemp to create a singleton twice !")
	end
	NiuDanWGData.Instance = self

	self.fws_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_yanhua_shengdian_auto")
	self.param_cfg = ListToMap(self.fws_cfg.config_param, "grade")
    self.grade_cfg = ListToMap(self.fws_cfg.grade, "grade")
    self.consume_cfg = ListToMapList(self.fws_cfg.consume, "consume")
    self.reward_cfg = ListToMap(self.fws_cfg.reward, "reward", "reward_id")
    self.rebate_cfg = ListToMap(self.fws_cfg.rebate, "rebate", "index")
    self.yanhua_cfg = ListToMap(self.fws_cfg.yanhua_show, "grade")
    self.role_sp_guarantee_cfg = ListToMapList(self.fws_cfg.role_sp_guarantee, "reward")
    self.item_random_desc = ListToMapList(self.fws_cfg.item_random_desc, "reward")
    self.other_cfg = self.fws_cfg.other[1]

    RemindManager.Instance:Register(RemindName.NiuDan, BindTool.Bind(self.ShowRed, self))
    -- FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2, {[1] = MERGE_EVENT_TYPE.LEVEL},
    -- BindTool.Bind(self.GetActIsOpen, self), BindTool.Bind(self.ShowRed, self))
    self:NiuDanRemindInBag(RemindName.NiuDan)
end

function NiuDanWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.NiuDan)
	NiuDanWGData.Instance = nil
end

function NiuDanWGData:ShowRed()
    if not self:GetActIsOpen() then
        return 0
    end

    --背包有道具
    if self:GetEnoughItem() then
        return 1
    end

    --返利
    if self:GetFanliRed() then
        return 1
    end

    return 0
end


--是否显示保底次数
function NiuDanWGData:GetIsShowBaodiTimes()
	return self.other_cfg.itemlist_display == 1
end

--有道具就有红点
function NiuDanWGData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--有能领取的返利有红点
function NiuDanWGData:GetFanliRed()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local draw_time = self:GetCurDrawTimes()
    for i, v in ipairs(self.rebate_cfg[rebate]) do
        if draw_time >= v.lotto_num then
            if not self:GetFanliHasGet(v.index) then
                return true
            end
        else
            return false
        end
    end
end

--获取活动是否能够开启
function NiuDanWGData:GetActIsOpen()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2)
    if not is_open then
        return false
    end

    local cur_cfg = self:GetCurCfg()
    if not cur_cfg then
        return false
    end

    return RoleWGData.Instance:GetRoleLevel() >= cur_cfg.open_level
end

function NiuDanWGData:GetCurCfg()
    return self.param_cfg[self.grade or 1]
end

function NiuDanWGData:GetGradeCfg()
    return self.grade_cfg[self.grade or 1]
end

function NiuDanWGData:GetConsumeCfg()
    local cfg = self:GetGradeCfg()
    return self.consume_cfg[cfg.consume]
end

function NiuDanWGData:GetYanhuaCfg()
    return self.yanhua_cfg[self.grade or 1]
end

function NiuDanWGData:GetRewardById(reward, reward_id)
    return self.reward_cfg[reward or 1][reward_id]
end

function NiuDanWGData:GetRebateByIndex(index, rebate)
    if rebate == nil then
        local cfg = self:GetGradeCfg()
        rebate = cfg and cfg.rebate or 1
    end
    return self.rebate_cfg[rebate][index]
end

function NiuDanWGData:CheckIsFireMap(x, y)
    if not self.yanhua_pos then
        self:GetYanhuaPos()
    end
    --检测是否在指定区域
    if math.floor((self.yanhua_pos.x - x) * (self.yanhua_pos.x - x)) + math.floor((self.yanhua_pos.y - y) * (self.yanhua_pos.y - y)) <= self.yanhua_range * self.yanhua_range then
        return true
    end
    return false
end

function NiuDanWGData:UpdateRecordCount()
    --更新记录时间
    RoleWGData.SetRolePlayerPrefsInt("Festival_Fireworks_record", TimeWGCtrl.Instance:GetServerTime())
end

function NiuDanWGData:GetRecordInfo()
    local list = {}
    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            list[k] = v
        end
    end
    
    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyUpperSorter("draw_time"))
    end
    --获取日志
    return list
end

--抽奖道具list
function NiuDanWGData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = NiuDanWGData.Instance:GetConsumeCfg()
        for i, v in pairs(cfg) do
            table.insert(self.item_data_change_list, v.yanhua_item.item_id)
        end
    end
    return self.item_data_change_list
end

--清除缓存
function NiuDanWGData:ClearCache()
    self.item_data_change_list = nil
    self.time_list = nil
end

--当前抽奖次数
function NiuDanWGData:GetCurDrawTimes()
    return self.cur_draw_times or 0
end

function NiuDanWGData:NiuDanRemindInBag(remind_name)
    local check_list = self:GetItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end

function NiuDanWGData:GetNextBaodiTimes()
    local cfg = self:GetGradeCfg()
    local reward = cfg.reward_unbind
    local reward_cfg = self.reward_cfg[reward or 1]
    local times_list_tb = {}
    for k, v in pairs(reward_cfg) do
        if v.player_guarantee ~= 0 then
            if not times_list_tb[v.player_guarantee] then
                times_list_tb[v.player_guarantee] = true
            end
        end
    end
    local draw_times = self:GetCurDrawTimes()
    local min = 9999
    if not IsEmptyTable(times_list_tb) then
        for k, v in pairs(times_list_tb) do
            local temp = draw_times % k
            temp = temp == 0 and k or k - temp
            if temp < min then
                min = temp
            end
        end
    end
    return min
end

function NiuDanWGData:GetBaoDiDrawTimes()
    local cfg = self:GetGradeCfg()
    local reward = cfg.reward_unbind
    local reward_cfg = self.reward_cfg[reward or 1]
    local baodi_times_list = {}
    for k,v in pairs(reward_cfg) do
        if v.reward_type == 1 then
            table.insert(baodi_times_list, v.player_guarantee)
        end
    end

    if not IsEmptyTable(baodi_times_list) then
        local draw_times = self:GetCurDrawTimes()
        local cfg_times = baodi_times_list[1]

        if draw_times < cfg_times then
            return cfg_times - draw_times
        end
    end

    return -1
end

--返利是否领取
function NiuDanWGData:GetFanliHasGet(index)
    return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index - 1)) ~= 0
end

--获取返利显示的格子
function NiuDanWGData:GetFanliList(len)
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1

    local list = {}
    local max = #self.rebate_cfg[rebate]
    local len = len or 5
    local t = {}
    for i = 0, len-1 do
        t = {
            data = self.rebate_cfg[rebate][max - i],
            has_get = self:GetFanliHasGet(max - i) and 1 or 0
        }
        table.insert(list, 1, t)
    end

    local flag
    for i = max - len, 1, -1 do
        flag = self:GetFanliHasGet(i)
        if not flag then
            t = {
                data = self.rebate_cfg[rebate][i],
                has_get = 0
            }
            table.insert(list, 1, t)
            table.remove(list, len + 1)
        end
    end

    return list
end

function NiuDanWGData:SortDataList(data_list)
	local list = {}
    if data_list and not IsEmptyTable(data_list) then    
        for k,v in pairs(data_list) do
            local temp = {}
            temp.reward_item = v.reward_item
			if temp.reward_item and temp.reward_item.item_id and temp.reward_item.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(temp.reward_item.item_id)       
                temp.color = item_cfg and item_cfg.color or 1
			else
                temp.color = 0
            end
            list[#list+1] = temp
		end
		table.sort(list, SortTools.KeyUpperSorters("color"))
    end
    return list
end

--已经抽到的奖励
function NiuDanWGData:SaveDrawInfo(protocol)
    self.had_draw_list = protocol.reward_info
end

--绑定奖池和非绑奖池的展示奖励
function NiuDanWGData:GetShowCellList()
    local list1 = {}
    local list2 = {}
    local baodi_list = {}
    local grade_cfg = self:GetGradeCfg()
    local index = 0
    local cfg = self.reward_cfg[grade_cfg.reward_bind]
    for i, v in ipairs(cfg) do
        if v.reward_show == 1 then
            table.insert(list1, v)
        elseif v.reward_show == 2 then
            table.insert(list2, v)
        elseif v.reward_show == 3 then
            table.insert(baodi_list, v)
        end
    end
    list2 = self:SortDataList(list2)
    baodi_list = self:SortDataList(baodi_list)
    local temp_list = {}
    local temp_count_list = {}
    for k, v in pairs(list1) do
        local temp = {}
        temp.reward = v
        temp.count = v.guarantee_reward_limit
        if not IsEmptyTable(self.had_draw_list) then
            for k1, v1 in pairs(self.had_draw_list) do
                if v.reward_id == v1.reward_id then
                    temp.count = v.guarantee_reward_limit - v1.get_count 
                end
            end
        end
        table.insert(temp_count_list, temp) 
    end
    for k2, v2 in pairs(temp_count_list) do
        if v2.count > 0 then
            table.insert(temp_list, v2.reward)
        end
    end
    temp_list = self:SortDataList(temp_list)

    return temp_list, list2, baodi_list
end

--珍稀奖励
function NiuDanWGData:GetZhenXiRewardList()
    local list1, list2, list3 = self:GetShowCellList()
    
    local big_list = {}
    for k, v in pairs(list2) do
        table.insert(big_list, v)
    end

    for k, v in pairs(list3) do
        table.insert(big_list, v)
    end
    
    big_list = self:SortDataList(big_list)
    return big_list
end

--模型大奖
function NiuDanWGData:GetBigReward()
    --大奖配置，other or 遍历
    local grade_cfg = self:GetGradeCfg()
    local reward = Split(grade_cfg.model_item, "|")
    reward[1] = tonumber(reward[1])
    reward[2] = tonumber(reward[2] or OARenderType.RoleModel)
    return reward
end

--是否跳过动画对应的延时
function NiuDanWGData:GetDelayTime()
    --是否跳过动画
    if self.is_skip_comic == 1 then
        return 0
    else
        local cache_index = self:CacheOrGetDrawIndex()
        return UITween_CONSTS.MergeFws.EffectDelay[cache_index] or 0
    end
end

--日志协议
function NiuDanWGData:SetRecord(record_list)
    self.record_list = record_list
    self.new_record_time = self.record_list[1] and self.record_list[1].draw_time or 0
end

--获取最新日志的时间
function NiuDanWGData:GetRecordTime()
    return self.new_record_time or 1
end

--协议信息
function NiuDanWGData:SetInfo(protocol)
    if self.grade ~= protocol.grade then
        self:ClearCache()
    end
    self.grade = protocol.grade
    self.cur_draw_times = protocol.person_draw_count
    self.fetch_flag = protocol.leiji_reward_fetch_flag
    self.is_skip_comic = protocol.is_skip_comic			--跳过动画？
	self.sp_guarantee_x = protocol.sp_guarantee_x		--特殊保底次数？ 弃用
	self.sp_guarantee_n = protocol.sp_guarantee_n		--特殊保底轮数？
	self.sp_enter_num = protocol.sp_enter_num			--进入保底库次数？

    self.gather_small_count = protocol.gather_small_count
    self.gather_big_count = protocol.gather_big_count
end

--是否跳过动画
function NiuDanWGData:GetSkipAnim()
    return self.is_skip_comic
end
--获取保底显示
function NiuDanWGData:GetSpGuarantee()
    return self.sp_guarantee_x or 0, self.sp_guarantee_n or 0, self.sp_enter_num or 0
end

-- 获取保底次数
function NiuDanWGData:GetGuaranteeListCount(reward)
	if not reward then
		return 0
	end

	local num = self.role_sp_guarantee_cfg[reward] and #self.role_sp_guarantee_cfg[reward] or 0
	return num
end

--烟花寻路位置，如果有改变可以在clearcache清除缓存
function NiuDanWGData:GetYanhuaPos()
    local cfg = self:GetYanhuaCfg()
    local pos = Split(cfg.yanhua_show_pos, ",")
    self.yanhua_pos = Vector2(tonumber(pos[1]), tonumber(pos[2]))
    self.yanhua_range = cfg.yanhua_show_range
end

--获取烟花汇演的时间配置
function NiuDanWGData:GetYanHuaTimeCfg()
    if not self.time_list then
        local list = {}
        local cfg = self:GetYanhuaCfg()
        cfg = Split(cfg.yanhua_show_time, "|")
        for i, v in ipairs(cfg) do
            table.insert(list, v)
        end
        self.time_list = list
    end
    return self.time_list
end

--获取烟花汇演scene_id
function NiuDanWGData:GetYanhuaSceneId()
    return self:GetYanhuaCfg().scene_id
end

--获取采集次数
function NiuDanWGData:GetGatherTimes()
    return self.gather_small_count, self.gather_big_count
end

--os.date的time_t和XX:XX比较时间差
function NiuDanWGData:GetDifTime(now_time, str_time)
    str_time = Split(str_time, ":")
    for i, v in pairs(str_time) do
        v = tonumber(v)
    end

    --在汇演时间（str_time）到汇演时间+duration期间弹窗，否则倒计时
    local hour = str_time[1] - now_time.hour
    local min = str_time[2] - now_time.min
    local sec = 0 - now_time.sec
    return hour * 3600 + min * 60 + sec
end

--是否汇演地图
function NiuDanWGData:CheckPerformArea()
    if Scene.Instance:GetSceneId() ~= self:GetYanhuaCfg().scene_id then
        return false
    end

    --汇演拿不到入侵服的活动信息，因此直接当作安全区处理
    --local time_list = self:GetYanHuaTimeCfg()
    --local time_t = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
    --for i, v in pairs(time_list) do
    --    if self:GetDifTime(time_t, v) < yanhua_cfg.yanhua_show_duration then
    --        return true
    --    end
    --end
    return true
end

--汇演期间不会被拉去打坐，在upate里执行，看看能否优化
function NiuDanWGData:IsPerformTimeForUpdate()
    if not self:GetActIsOpen() then
        self.time_t = nil
        return false
    end

    local yanhua_cfg = self:GetYanhuaCfg()
    if Scene.Instance:GetSceneId() ~= yanhua_cfg.scene_id then
        self.time_t = nil
        return false
    end

    if not FestivalFireworksWGCtrl.Instance:GetIsFireMap() then
        self.time_t = nil
        return false
    end

    local time_list = self:GetYanHuaTimeCfg()
    if not self.time_t then
        self.time_t = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
    end
    for i, v in pairs(time_list) do
        if self:GetDifTime(self.time_t, v) < yanhua_cfg.yanhua_show_duration then
            return true
        end
    end
    return false
end

--获取奖励对应的配置
function NiuDanWGData:CalDrawRewardList(protocol)
    local data_list = {}
	if not protocol or not protocol.count or protocol.count <= 0 then
		return data_list
	end

    local zhenxi_item_list = {}
	for i,v in ipairs(protocol.reward_list) do
		local cfg = self:GetRewardById(v.reward_pool_id, v.reward_id)
        if cfg then
            local temp = {}
            temp.is_zhenxi = v.is_zhenxi
            temp.reward = cfg.reward
            temp.reward_item = cfg.reward_item
            temp.reward_type = cfg.reward_type
            temp.rewrad_rare_show = cfg.rewrad_rare_show
            if temp.is_zhenxi and protocol.count == 50 then
                table.insert(zhenxi_item_list, temp)
            else
                table.insert(data_list, temp)
            end
		else
			print_error("错误数据 请检查奖励配置 reward_pool_id,reward_id: ", v.reward_pool_id, v.reward_id)
		end
    end
    local temp_data_list
    if not IsEmptyTable(zhenxi_item_list) then
        local zhenxi_index
        temp_data_list = {}
        zhenxi_index = math.random(22, 28)
        for k, v in ipairs(data_list) do
            if k < zhenxi_index then
                temp_data_list[k] = v
            else
                local zhenxi_list_count = #zhenxi_item_list
                if k == zhenxi_index then
                    for key, temp  in ipairs(zhenxi_item_list) do
                        temp_data_list[zhenxi_index + key - 1 ] = temp
                    end
                    temp_data_list[k + zhenxi_list_count] = v
                else
                    temp_data_list[k + zhenxi_list_count] = v
                end
            end
        end
    end

	return temp_data_list or data_list
end

--获取抽奖的选项
function NiuDanWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end


function NiuDanWGData:GetItemsProbility()
    local str, temp_str = "", ""
    local item_cfg
    local item_list = self:GetDataList()
    if not IsEmptyTable(item_list) then
        for k, v in pairs(item_list) do
            item_cfg = ItemWGData.Instance:GetItemConfig(v.reward_item.item_id)
            if item_cfg then
                temp_str = ToColorStr(item_cfg.name..": "..v.rewrad_rare_show * 100 .."%", ITEM_COLOR[item_cfg.color])
                str = str.. "\n" ..temp_str
            end
        end
    end
    return str
end

function NiuDanWGData:GetDataList(grade_cfg)
	local data_list = {}
	grade_cfg = grade_cfg or self:GetGradeCfg()
	if not grade_cfg then
		return data_list
	end

	local cfg = self.reward_cfg[grade_cfg.reward_bind] or {}
	for i, v in pairs(cfg) do
		if v.reward_show > 0 then
			table.insert(data_list, v)
		end
    end
    table.sort(data_list, function(a, b)
        if a.rewrad_rare_show ~= b.rewrad_rare_show then
            return a.rewrad_rare_show < b.rewrad_rare_show
        end
        local item_cfg1, item_cfg2
        if a and a.reward_item and a.reward_item.item_id then
            item_cfg1 = ItemWGData.Instance:GetItemConfig(a.reward_item.item_id)
        end
        if b and b.reward_item and b.reward_item.item_id then
            item_cfg2 = ItemWGData.Instance:GetItemConfig(b.reward_item.item_id)
        end
        local color1 = item_cfg1 and item_cfg1.color or 0
        local color2 = item_cfg2 and item_cfg2.color or 0
        return color1 > color2
    end)
	return data_list
end

function NiuDanWGData:GetGaiLvInfo()
	return self.item_random_desc[self.grade or 1] or {}
end