LevelRechargeWGData = LevelRechargeWGData or BaseClass()

function LevelRechargeWGData:__init()
	if LevelRechargeWGData.Instance ~= nil then
		ErrorLog("[LevelRechargeWGData] attempt to create singleton twice!")
		return
	end
	LevelRechargeWGData.Instance = self

	self.role_level_buy_flag = {}
	self.level_recharge_cfg = ConfigManager.Instance:GetAutoConfig("roleexp_auto").role_level_buy
	self.cur_price_cfg = ListToMap(self.level_recharge_cfg, "seq")
	self.show_tip = true
end

function LevelRechargeWGData:__delete()
	LevelRechargeWGData.Instance = nil
end

function LevelRechargeWGData:GetLevelRechargeCfg()
	return self.level_recharge_cfg or {}
end

function LevelRechargeWGData:GetCurPriceCfg(seq)
	return self.cur_price_cfg[seq] or {}
end

function LevelRechargeWGData:GetIsShowTip(index)
	if index == nil then
		index = 1
	end

	if self.show_tip then
		local level_limit = (self.level_recharge_cfg[index] or {}).rmb_buy_level
		local role_level = RoleWGData.Instance.role_vo.level
		local online_time = TimeWGCtrl.Instance:GetOnlineTimes()
		return level_limit and level_limit > role_level and online_time < self:GetLevelRechargeShowTime()
	end

	return false
end

function LevelRechargeWGData:SetIsShowTip(state)
	self.show_tip = state
end

function LevelRechargeWGData:GetLevelRechargeShowTime()
	return 30 * 60
end

function LevelRechargeWGData:SetIsBuy(seq)
	self.role_level_buy_flag[seq] = true
end

function LevelRechargeWGData:GetIsBuy(seq)
	return self.role_level_buy_flag[seq] or false
end

