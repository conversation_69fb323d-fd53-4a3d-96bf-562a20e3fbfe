
--天神宝匣
TianShenBaoXiaView = TianShenBaoXiaView or BaseClass(SafeBaseView)
function TianShenBaoXiaView:__init()
	self:SetMaskBg(false)
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	local bundle_name = "uis/view/tianshen_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, bundle_name, "layout_ts_box_baoxia_view")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

	self.view_name = "TianShenBaoXiaView"

	self.tab_sub = {}
	self.remind_tab = {
			{RemindName.TianShenBaoXia},
		}

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
end

function TianShenBaoXiaView:__delete()
	self.item_data_change_callback = nil
end

function TianShenBaoXiaView:ReleaseCallBack()
	-- if nil ~= self.tabbar then
	-- 	self.tabbar:DeleteMe()
	-- 	self.tabbar = nil
	-- end

	self:TSBoxReleaseCallBack()
end

function TianShenBaoXiaView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShen.TianShenBaoXiaViewTitle
	-- self.tabbar = Tabbar.New(self.node_list)
	-- self.tabbar:SetVerTabbarIconStr("ts_view")
	-- self.tabbar:SetVerTabbarCellName("TianShen_VerticalTabbarCell")
	-- self.tabbar:Init(Language.TianShenBaoXia.TabGrop, nil, "uis/view/tianshen_prefab", nil, self.remind_tab)
	-- self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	-- FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.TianShenBaoXia, self.tabbar)
end

function TianShenBaoXiaView:LoadIndexCallBack(index)
	self:TSBoxLoadCallBack()
end

function TianShenBaoXiaView:OpenCallBack()
	--请求宝匣信息
	TianShenWGCtrl.Instance:OnCSGodhoodDrawOperate(GODHOOD_DRAW_OPERATE_TYPE.GODHOOD_DRAW_OPERATE_TYPE_INFO)

    -- if nil ~= self.tabbar then
    --     self.tabbar:AddAllListen()
    -- end

    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function TianShenBaoXiaView:CloseCallBack()
    -- if nil ~= self.tabbar then
    --     self.tabbar:UnAllListen()
    -- end
    self:TSBoxCloseCallBack()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function TianShenBaoXiaView:ShowIndexCallBack(index)
	self.show_index = index
	self:TSBoxShowIndexCallBack()
end

function TianShenBaoXiaView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		self:TSBoxOnFlush()
	end
end

--物品变化
function TianShenBaoXiaView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if not self:IsLoaded() then
        return
    end
    self:TSBoxOnFlush()
end
