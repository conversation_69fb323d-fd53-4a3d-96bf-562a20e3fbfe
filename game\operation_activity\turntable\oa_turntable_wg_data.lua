local OATurnTablePageCellInfo = {
	[1] = {name = "祖炎龙", icon = "a2_zxtfgx_kp1"},
	[2] = {name = "云海冠", icon = "a2_zxtfgx_kp2"},
	[3] = {name = "九重峰", icon = "a2_zxtfgx_kp3"},
	[4] = {name = "断罪城", icon = "a2_zxtfgx_kp4"},
	[5] = {name = "神宵宫", icon = "a2_zxtfgx_kp5"},
	[6] = {name = "新耀令", icon = "a2_zxtfgx_kp6"},
	[7] = {name = "天书城", icon = "a2_zxtfgx_kp7"},
	[8] = {name = "瀚海罩", icon = "a2_zxtfgx_kp8"},
	[9] = {name = "天狼月", icon = "a2_zxtfgx_kp9"},
	[10] = {name = "神龙木", icon = "a2_zxtfgx_kp10"},
	[11] = {name = "通天塔", icon = "a2_zxtfgx_kp11"},
	[12] = {name = "苍穹弓", icon = "a2_zxtfgx_kp12"},
	[13] = {name = "祖龙王", icon = "a2_zxtfgx_kp1"},
	[14] = {name = "恶魔谷", icon = "a2_zxtfgx_kp4"},
	[15] = {name = "凌霄城", icon = "a2_zxtfgx_kp7"},
	[16] = {name = "云兮巅", icon = "a2_zxtfgx_kp3"},
}

OATurnTableWGData = OATurnTableWGData or BaseClass()

OATurnTableWGData.DIF_ANGLE = 20
OATurnTableWGData.MAX_RANK = 3
OATurnTableWGData.MAX_LAYER = 3

function OATurnTableWGData:__init()
	if OATurnTableWGData.Instance then
		ErrorLog("[OATurnTableWGData] Attemp to create a singleton twice !")
	end
	self.angle_list = {}
	self.result = {}
	self.layer_info = {}
	self.reward_list = {}
	self.record = {}
	self.near_record_time = 0
	OATurnTableWGData.Instance = self

	self:LoadCfg()

	self.act_change = GlobalEventSystem:Bind(OPERATION_ACTIVITY.ACT_STATE_CHANGE, BindTool.Bind(self.ClearActCache, self))
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE, {[1] = OPERATION_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetTurnTableIsOpen, self),BindTool.Bind(self.IsShowTurnTableRed, self))

	RemindManager.Instance:Register(RemindName.OATurnTable, BindTool.Bind(self.IsShowTurnTableRed, self))

end

function OATurnTableWGData:LoadCfg()
	self.act_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_rabbitgirl_treasure_auto")
	self.gear_cfg = ListToMap(self.act_cfg.layer, "layer")
	self.week_cfg = ListToMap(self.act_cfg.week, "layer", "round_count")
    self.reward_cfg = ListToMap(self.act_cfg.reward, "reward_id")
    self.reward_pool_cfg = ListToMapList(self.act_cfg.reward, "reward_pool_id")
    
	self.cost_cfg = ListToMapList(self.act_cfg.cost, "layer")
	self.record_cfg = ListToMap(self.act_cfg.record, "prop_id")
	
	self:RegisterRewardRemindInBag(RemindName.OATurnTable)
end

function OATurnTableWGData:__delete()
	if self.act_change then
		GlobalEventSystem:UnBind(self.act_change)
		self.act_change = nil
	end

	OATurnTableWGData.Instance = nil
end

function OATurnTableWGData:RegisterRewardRemindInBag(remind_name)
    local map = {}

	local len = #self.gear_cfg
	for i = 1, len do
		local draw_info = self:GetTurnTableCurDrawInfoByLayer(i) 
		if draw_info then
			map[draw_info.draw_consume_item_id] = true
		end
	end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function OATurnTableWGData:GetTurnTableIsOpen()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE) then
		return false
	end
    if IsEmptyTable(self:GetCurActLayerList()) then
        return false
    end
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = self.act_cfg.other[1].open_role_level
	return role_level >= need_level
end

function OATurnTableWGData:ClearActCache(act_type, status)
	if act_type ~= ACTIVITY_TYPE.OPERA_ACT_TURNTABLE then
		return
	end

	self.layer_list = nil

	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	PlayerPrefsUtil.SetInt("oa_turntable" .. "_ONCE" .. role_id, 0)

	if status then
		local layer_list = self:GetCurActLayerList()
		for i, v in pairs(layer_list) do
			OATurnTableWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.LAYER_INFO, v)
		end
	end
end

function OATurnTableWGData:GetCurActLayerList()
	if IsEmptyTable(self.layer_list)  then
		self.layer_list = {}
        local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
        local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
        local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
        for i, v in pairs(self.act_cfg.config_param) do
			if open_day >= v.start_server_day and open_day < v.end_server_day and week == v.week_index then
				local t = Split(v.layer, "|")
				for i, v in ipairs(t) do
					table.insert(self.layer_list, tonumber(v))
				end
				break
			end
		end
	end
	return self.layer_list
end

function OATurnTableWGData:GetLayerCfgByLayerNum(layer_num)
	return self.gear_cfg[layer_num] or {}
end

function OATurnTableWGData:GetWeekCfgByLayerNum(layer_num, round_count)
	return self.week_cfg[layer_num] and self.week_cfg[layer_num][round_count] or {}
end

function OATurnTableWGData:GetTurnTableRewardListByLayer(layer_num)
	--获取对应层数的奖励列表
	if not self.reward_list[layer_num] and self.layer_info[layer_num] then
		local reward_list = self.layer_info[layer_num].draw_reward_id_list or {}
		for i = #reward_list,1,-1 do
			if reward_list[i] == 0 then
				table.remove(reward_list, i)
			else
				break
			end
		end
		self.reward_list[layer_num] = reward_list
	end
	return self.reward_list[layer_num] or {}
end

function OATurnTableWGData:GetTurnTableRewardIsShowByLayerAndIndex(layer_num, index)
	--根据层数和id判断这个奖励有没有抽取
	local flag = self.layer_info[layer_num] and self.layer_info[layer_num].draw_result_flag or 0
    return bit:_and(flag, bit:_lshift(1, index-1)) == 0
end

function OATurnTableWGData:GetTurnTableRewardInfoById(id)
	--根据id取奖励信息
	return self.reward_cfg[id]
end

function OATurnTableWGData:GetTurnTableRoundTimeByLayer(layer_num)
	--根据层数取得当前的剩余时间和当前round
	--周期刷新问题从服务器拿
	local layer_info = self.layer_info[layer_num] or {}
	if layer_info.reward_round_id and layer_info.last_refresh_time then
		local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
		local round_time = layer_cfg.round_continue_time_h or 0
		local round_time_list = string.split(round_time, ",")
		round_time = tonumber(round_time_list[layer_info.reward_round_id + 1]) or 0

		local next_time = layer_info.last_refresh_time + round_time * 3600
		local act_end_time = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
		next_time = next_time < act_end_time.end_time and next_time or act_end_time.end_time
		next_time = next_time - TimeWGCtrl.Instance:GetServerTime()
		return next_time, layer_info.reward_round_id
	end
	return 0, -1
end

function OATurnTableWGData:GetTurnTablePoolTimeByLayer(cur_layer)
	local cur_round_count = self:GetCurRoundByLayer(cur_layer)
	local cur_pool_count = self:GetCurPoolByLayer(cur_layer)
    local week_cfg = self:GetWeekCfgByLayerNum(cur_layer, cur_round_count)
    local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local open_limit_day = OperationActivityWGData.Instance:GetWeekLoopOpenCfgByActid(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
    local ser_time = TimeWGCtrl.Instance:GetServerTime()
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
    local act_star_time = act_info and act_info.start_time or 0
    local act_end_time = act_info and act_info.end_time or 0

    if open_limit_day > 0 and open_limit_day + 1 == cur_open_day then
    	--开服天数限制特殊操作,否则下轮倒计时为负数
    	act_star_time = TimeWGCtrl.Instance:NowDayTimeStart(TimeWGCtrl.Instance:GetServerTime())
    end

    local next_pool_time = 0
    if week_cfg then
    	local pool_time = week_cfg.continue_time or 0
    	local pool_time_list = Split(pool_time, ",")
    	local next_time = 0
    	for i=1,#pool_time_list do
    		next_time = next_time + pool_time_list[i] * 3600
    		if i > cur_pool_count then
    			break
    		end
    	end
    	next_pool_time = act_star_time + next_time - ser_time
    end

    if next_pool_time > act_end_time - ser_time then--最后一轮的时间加个容错
    	next_pool_time = act_end_time - ser_time
    end
    return next_pool_time
end

function OATurnTableWGData:GetTurnTableIsDrawEmptyByLayer(layer_num)
	--根据层数判断当前层有没有抽空
	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	local num = self:GetLayerRewardNumByLayer(layer_num)
	return #reward_list <= num
end

function OATurnTableWGData:GetLayerRewardNumByLayer(layer_num)
	local flag = self.layer_info[layer_num] and self.layer_info[layer_num].draw_result_flag or 0
    return bit:d2b1n(flag)
end

function OATurnTableWGData:GetTurnTableAngleByLayerAndRank(layer, rank)
	--根据层数和圈数取当前圈的角度
	layer = layer or 1
	return self.angle_list[layer * OATurnTableWGData.MAX_LAYER + rank]
end

function OATurnTableWGData:CacheTurnTableAngleByLayerAndRank(layer, rank, angle)
	self.angle_list[layer * OATurnTableWGData.MAX_LAYER + rank] = angle
end

function OATurnTableWGData:GetTurnTableRecordByItemId(prop_id)
	return self.record_cfg and self.record_cfg[prop_id]
end


function OATurnTableWGData:GetTurnTableCurDrawInfoByLayer(layer_num)
	--根据层数从协议拿当前的抽奖信息
	if not self.cost_cfg[layer_num] then
		return nil
	end

	local t = {}
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	t.conmsum_xianyu = layer_cfg.conmsum_xianyu
	t.draw_consume_item_id = layer_cfg.draw_consume_item_id
	local draw_num = self:GetLayerRewardNumByLayer(layer_num)
	draw_num = draw_num + 1
	for i, v in pairs(self.cost_cfg[layer_num]) do
		if draw_num >= v.lower_limit_of_extraction and draw_num <= v.extract_upper_limit then
			t.draw_consume_item_count = v.draw_consume_item_count
			break
		end
	end
	return t
end

function OATurnTableWGData:GetAllDrawInfo(layer_num)
	local cfg = self.cost_cfg[layer_num]
	if not cfg then
		return nil
	end

	local t = {}
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	t.draw_consume_item_id = layer_cfg.draw_consume_item_id
	t.conmsum_xianyu = layer_cfg.conmsum_xianyu
	local draw_num = self:GetLayerRewardNumByLayer(layer_num)
	draw_num = draw_num + 1
	t.draw_consume_item_count = 0
	local times
	for i, v in pairs(cfg) do
		if draw_num < v.lower_limit_of_extraction then
			times = v.extract_upper_limit - v.lower_limit_of_extraction + 1
			t.draw_consume_item_count = t.draw_consume_item_count + v.draw_consume_item_count
		elseif draw_num >= v.lower_limit_of_extraction and draw_num <= v.extract_upper_limit then
			times = v.extract_upper_limit - draw_num + 1
			t.draw_consume_item_count = t.draw_consume_item_count + times * v.draw_consume_item_count
		end
	end
	return t
end

function OATurnTableWGData:GetRemainDrawTimesByLayer(layer_num)
	--根据层数判断当前还有多少个奖励
	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	local num = self:GetLayerRewardNumByLayer(layer_num)
	return #reward_list - num
end

function OATurnTableWGData:CheckAngleHasReward(layer_num, angle, rank)
	--获取当前的奖励列表
	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	for i, v in pairs(reward_list) do
		if self:GetTurnTableRewardIsShowByLayerAndIndex(layer_num, i) then
			local cfg = self:GetTurnTableRewardInfoById(v)
			--筛选rank，筛选是否领取
			if cfg and cfg.rank == rank then
				local rot_angle = Vector2.Angle(self.turntable_win_vector2, Vector2(cfg.x, cfg.y))
				local direct =  self.turntable_win_vector2.x * cfg.y - self.turntable_win_vector2.y * cfg.x
				if direct > 0 then--顺时针角度
					rot_angle = 360 - rot_angle
				end

				local dif_angle = math.abs(rot_angle - angle)%360
				local tolerance = rot_angle - angle > 0 and -1 or 1
				--遍历是否相似角度
				if dif_angle <= OATurnTableWGData.DIF_ANGLE or (360 - dif_angle) <= OATurnTableWGData.DIF_ANGLE then
					local rand_angle = GameMath.Rand(30,45)
					tolerance = tolerance * (rand_angle - dif_angle)
					return tolerance
				end
			end
		end
	end
	return 0
end

function OATurnTableWGData:GetCircleRotNum()
	--其他表的3个圈的圈数和持续时间
	local other_cfg = self.act_cfg.other[1]
	local cricle_num = other_cfg.cricle_num or {3,4,5}
	local rot_time = other_cfg.rot_time or 2
	return cricle_num, rot_time
end

function OATurnTableWGData:CheckCanResetByLayer(layer_num)
	--大奖已被抽取
	local big_reward_exist = self:GetBigRewardExistByLayer(layer_num)
	if big_reward_exist then
		return false
	end
	-- --剩余奖励数量

	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	if layer_cfg.is_auto_refresh == 0 then
		return false
	end

	local remains = self:GetRemainDrawTimesByLayer(layer_num)
	if remains > (layer_cfg.remaining_incentive_limit or 0) then
		return false
	end
	--本周期存在后续奖池
	return self:CheckHasNextPool(layer_num)
end


function OATurnTableWGData:CheckHasNextRound(layer_num)
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	local cur_round_count = self:GetCurRoundByLayer(layer_num) + 1
	if nil == layer_cfg or nil == layer_cfg.round_count then
		return false
	end

	local round_list = Split(layer_cfg.round_count, ",")
	local max_round_count = #round_list
	if cur_round_count >= max_round_count then
		return false
	end

	local layer_info = self.layer_info[layer_num] or {}
	if layer_info.reward_round_id and layer_info.last_refresh_time then
		local round_time = layer_cfg.round_continue_time_h or 0
		local round_time_list = string.split(round_time, ",")
		round_time = tonumber(round_time_list[layer_info.reward_round_id + 1]) or 0

		local next_time = layer_info.last_refresh_time + round_time * 3600
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TURNTABLE)
		return next_time < act_info.end_time
	end
	return false
end

function OATurnTableWGData:CheckHasNextPool(layer_num)
	local layer_cfg = self:GetLayerCfgByLayerNum(layer_num)
	local cur_round_count = self:GetCurRoundByLayer(layer_num)
	if nil == layer_cfg or nil == layer_cfg.round_count then
		return false
	end

	local round_list = Split(layer_cfg.round_count, ",")
	local max_round_count = #round_list

	local cur_pool_count = self:GetCurPoolByLayer(layer_num) + 1
	local reward_pool_id = self:GetWeekCfgByLayerNum(layer_num, cur_round_count).reward_pool_id
	if not reward_pool_id then
		return false
	end

	local pool_list = Split(reward_pool_id, ",")
	local max_pool_count = #pool_list

	return cur_pool_count < max_pool_count
end

function OATurnTableWGData:GetBigRewardExistByLayer(layer_num)
    local list = self:GetTurnTableRewardListByLayer(layer_num)
    local cfg
    for i, v in pairs(list) do
        cfg = self:GetTurnTableRewardInfoById(v)
		if cfg and cfg.is_best == 1 then
			local is_show = self:GetTurnTableRewardIsShowByLayerAndIndex(layer_num, i)
			if is_show then
				return is_show
			end
		end
    end
    return false
end

function OATurnTableWGData:GetNoticeDelayTime()
	local time
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	if PlayerPrefsUtil.GetInt("oa_turntable" .. "_ANIM" .. role_id) == 1 then
		time = 0
	else
		local _
		_, time = self:GetCircleRotNum()
	end
	time = time + 1--滑落时间
	return time
end

function OATurnTableWGData:IsShowTurnTableRed()
	local act_is_open = self:GetTurnTableIsOpen()
	if not act_is_open then
		return 0
	end

	--刷新下一周期了，给一次红点，这个要服务器下发
	local layer_list = OATurnTableWGData.Instance:GetCurActLayerList()
	for i,v in ipairs(layer_list) do
		if self:IsShowRedByLayer(v) then
			return 1
		end
	end	

	return 0
end

function OATurnTableWGData:IsShowRedByLayer(layer)
	--可重置了有个红点
	if self:CheckCanResetByLayer(layer) then
		return true
	end

	--检测道具够了有个红点（当前挡位道具抽完了？？）
	if self:GetTurnTableIsDrawEmptyByLayer(layer) then
		return false
	end

	local cur_draw_info = self:GetTurnTableCurDrawInfoByLayer(layer)
	if cur_draw_info.draw_consume_item_count == nil then
		return false
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	if num >= cur_draw_info.draw_consume_item_count then
		return true
	end
	--2020/04/25 22:16 策划需求去掉钱够显示红点
	-- if RoleWGData.Instance:GetRoleVo().gold >= cur_draw_info.conmsum_xianyu * cur_draw_info.draw_consume_item_count then
	-- 	return true
	-- end
	return false
end

function OATurnTableWGData:GetTurnTableCurDrawResultByLayer(layer_num)
	if self.result.layer ~= layer_num then
		return {}
	end

	local reward_list = self:GetTurnTableRewardListByLayer(layer_num)
	local reward_id = reward_list[self.result.slot + 1]

	return self:GetTurnTableRewardInfoById(reward_id)
end

function OATurnTableWGData:CalNewRecordNum()
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	if self.near_record_time ~= 0 then
		local count = 0
		for i, v in pairs(self.record) do
			if v.timestamp > self.near_record_time and role_id ~= v.uid then
				count = count + 1
			end
		end
		self:SetNewRecordCount(count)
	end
end

function OATurnTableWGData:SetNewRecordCount(count)
	self.new_record_count = count
end

function OATurnTableWGData:GetNewRecordCount()
	return self.new_record_count or 0
end

function OATurnTableWGData:ClearRecordCount()
	self.near_record_time = TimeWGCtrl.Instance:GetServerTime()
	self.new_record_count = 0
end

function OATurnTableWGData:GetRecordInfo()
	return self.record
end

function OATurnTableWGData:GetCurPoolByLayer(layer_num)
	return self.layer_info[layer_num] and self.layer_info[layer_num].reward_pool_id or 0
end

function OATurnTableWGData:GetCurRoundByLayer(layer_num)
	return self.layer_info[layer_num] and self.layer_info[layer_num].reward_round_id or 0
end

function OATurnTableWGData:GetLayerInfo(layer_num)
	return self.layer_info[layer_num] or {}
end

function OATurnTableWGData:SetLayerInfo(protocol)
	self.layer_info[protocol.layer] = protocol.layer_info
	self.reward_list[protocol.layer] = nil
	for i = 1, OATurnTableWGData.MAX_RANK do
		if self.angle_list[protocol.layer * OATurnTableWGData.MAX_LAYER + i] == nil then
			self.angle_list[protocol.layer * OATurnTableWGData.MAX_LAYER + i] = protocol.layer_info.dirs[i]
		end
	end
end

function OATurnTableWGData:SetRecordInfo(protocol)
	--筛选数目
	self.record = protocol.record_list
	self:CalNewRecordNum()
end

function OATurnTableWGData:SetResultInfo(protocol)
	self.result.layer = protocol.layer
    self.result.slot = protocol.hit_slot
end

-- function OATurnTableWGData:SetWinVector2(v2)
-- 	self.turntable_win_vector2 = v2
-- end

function OATurnTableWGData:ChangeTurnTableLayerBtnState(status)
	self.turning_anim = status
end

function OATurnTableWGData:GetIsTurningTable()
	return self.turning_anim or false
end

function OATurnTableWGData:GetItemsProbility(cur_layer)
    local reward_pool_index = self:GetCurPoolByLayer(cur_layer)
    local cur_round_count = self:GetCurRoundByLayer(cur_layer)
    local week_cfg = self:GetWeekCfgByLayerNum(cur_layer,cur_round_count)
    local reward_pool_id = 0
    if not IsEmptyTable(week_cfg) then
    	local reward_pool_id_list = Split(week_cfg.reward_pool_id,",")
    	reward_pool_id = tonumber(reward_pool_id_list[reward_pool_index + 1])
    end
    local str, temp_str = "", ""
    local item_cfg
    local item_list = self:GetDataList(reward_pool_id)
    if not IsEmptyTable(item_list) then
        for k, v in pairs(item_list) do
            item_cfg = ItemWGData.Instance:GetItemConfig(v.reward_item.item_id)
            if item_cfg then
                temp_str = ToColorStr(item_cfg.name..": "..v.rewrad_rare_show * 100 .."%", ITEM_COLOR[item_cfg.color])
                str = str.. "\n" ..temp_str
            end
        end
    end
    return str
end

function OATurnTableWGData:GetDataList(reward_pool_id)
    local data_list = {}
    local cfg = self.reward_pool_cfg[reward_pool_id] or {}
   
	for i, v in pairs(cfg) do
		table.insert(data_list, v)
    end
    table.sort(data_list, function(a, b)
        return a.rewrad_rare_show < b.rewrad_rare_show
    end)
	return data_list
end

-- 2021/12/1 已跟策划沟通让策划添加OATurnTablePageCellInfo数据配置，策划说先不加入配置，他自己先修改此脚本的OATurnTablePageCellInfo数据
function OATurnTableWGData:GetOATurnTablePageCellInfo(page_index)
	if IsEmptyTable(OATurnTablePageCellInfo[page_index]) then
		print_error("配置表配置奖励档位配置跟 \"OATurnTablePageCellInfo\" 档位数据匹配不上，请检查配置表配置和 \"OATurnTablePageCellInfo\"配置")
		return {}
	end
	return OATurnTablePageCellInfo[page_index]
end