function GameAssistantView:LoadAssistantFashion()
	if not self.assistant_fashion_model then
        self.assistant_fashion_model = OperationActRender.New(self.node_list["fashion_model"])
        self.assistant_fashion_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	-- 活跃获得
	if not self.fashion_item_list then
		self.fashion_item_list = AsyncListView.New(AssistantFashionItemRender, self.node_list.fashion_item_list)
		self.fashion_item_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectFashionItemCB, self))
	end

	if not self.assistant_fashion_model_alpha then
		self.assistant_fashion_model_alpha = self.node_list.assistant_fashion_model_show_root:GetOrAddComponent(typeof(UGUITweenAlpha))
	end

	if not self.assistant_fashion_show_alpha then
		self.assistant_fashion_show_alpha = self.node_list.assistant_fashion_show_root:GetOrAddComponent(typeof(UGUITweenAlpha))
	end
end

function GameAssistantView:ReleaseAssistantFashion()
    if self.assistant_fashion_model then
        self.assistant_fashion_model:<PERSON>ete<PERSON><PERSON>()
        self.assistant_fashion_model = nil
    end

	if self.fashion_item_list then
		self.fashion_item_list:DeleteMe()
		self.fashion_item_list = nil
	end

	self.fashion_select_index = nil
	self.fashion_select_data = nil
	self.assistant_fashion_model_alpha = nil
	self.assistant_fashion_show_alpha = nil
end

function GameAssistantView:CloseAssistantFashion()

end

function GameAssistantView:ShowAssistantFashion()

end

-- 点击跳转获取路径
function GameAssistantView:OnSelectFashionItemCB(get_way_item, cell_index, is_default, is_click)
	if (not get_way_item) or (not get_way_item.data) or is_default or (not is_click) then
		return
	end

	if cell_index == self.fashion_select_index then
		return
	end

	self.fashion_select_index = cell_index
	self.fashion_select_data = get_way_item.data
	self:FlushFashionModel()
	self:FlushFashionPower()
end

-- 刷新界面
function GameAssistantView:FlushAssistantFashion(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushFashionMessage()
			self:FlushFashionModel()
			self:FlushFashionPower()
		end
	end
end

-- 刷新数据
function GameAssistantView:FlushFashionMessage()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local list = nil

    if self.show_index == TabIndex.assistant_hot_buy then
        list = GameAssistantWGData.Instance:GetZhiGouCfgListByDay(cur_day)
    else
        list = GameAssistantWGData.Instance:GetWaiGuanCfgListByDay(cur_day)
    end

	self.select_index = 1
	self.fashion_select_data = list[self.select_index] or nil
	self.fashion_item_list:SetDataList(list)
	self.fashion_item_list:JumpToIndex(self.select_index, 2)
	self.assistant_fashion_show_alpha:ResetToBeginning()
end

-- 刷新模型
function GameAssistantView:FlushFashionModel()
    if not self.fashion_select_data then return end

    local display_data = {}
	display_data.should_ani = true
	if self.fashion_select_data.model_show_itemid ~= 0 and self.fashion_select_data.model_show_itemid ~= "" then
		local split_list = string.split(self.fashion_select_data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = self.fashion_select_data.model_show_itemid
		end
	end
	
	display_data.bundle_name = self.fashion_select_data["model_bundle_name"]
    display_data.asset_name = self.fashion_select_data["model_asset_name"]
    local model_show_type = tonumber(self.fashion_select_data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	if display_data.render_type == OARenderType.FZ or display_data.render_type == OARenderType.CangMing then
		display_data.waist_type = self.fashion_select_data.waist_type
	end

	self.assistant_fashion_model:SetData(display_data)
	local scale = 1
	if self.fashion_select_data.display_scale and self.fashion_select_data.display_scale ~= "" then
		scale = self.fashion_select_data["display_scale"]
	end
	Transform.SetLocalScaleXYZ(self.node_list["fashion_model"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
	if self.fashion_select_data.display_pos and self.fashion_select_data.display_pos ~= "" then
		local pos_list = string.split(self.fashion_select_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPositionXY(self.node_list["fashion_model"].rect, pos_x, pos_y)

	if self.fashion_select_data.display_rotation and self.fashion_select_data.display_rotation ~= "" then
		local display_rotation = string.split(self.fashion_select_data.display_rotation,"|")
		self.node_list["fashion_model"].transform.rotation = Quaternion.Euler(display_rotation[1], display_rotation[2], display_rotation[3])
	end

	self.assistant_fashion_model_alpha:ResetToBeginning()
end

-- 刷新战力
function GameAssistantView:FlushFashionPower()
    if not self.fashion_select_data then return end

	if self.fashion_select_data.model_show_itemid then
		local cap = ItemShowWGData.Instance:OnlyGetCapability(self.fashion_select_data.model_show_itemid)
		self.node_list.fashion_capability:CustomSetActive(cap ~= nil and cap > 0)
		self.node_list.fashion_cap_value.text.text = cap
	end

	local fashion_poster_1 = self.fashion_select_data.show_desc_1 or ""
	local fashion_poster_2 = self.fashion_select_data.show_desc_2 or ""
	self.node_list.fashion_poster.text.text = fashion_poster_1
	self.node_list.fashion_poster2.text.text = fashion_poster_2
end

---------------------------------------------------------------------------------------
AssistantFashionItemRender = AssistantFashionItemRender or BaseClass(BaseRender)
function AssistantFashionItemRender:LoadCallBack()
	if not self.fashion_item then
		self.fashion_item = ItemCell.New(self.node_list.item_root)
	end

    XUI.AddClickEventListener(self.node_list.btn_go_to, BindTool.Bind(self.OnClickGoToPath, self))
end

function AssistantFashionItemRender:__delete()
	if self.fashion_item then
		self.fashion_item:DeleteMe()
		self.fashion_item = nil
	end
end

function AssistantFashionItemRender:OnClickGoToPath()
    if not self.data then
		return
	end

    FunOpen.Instance:OpenViewNameByCfg(self.data.get_way_path)
end


function AssistantFashionItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.fashion_item_type_1:CustomSetActive(self.data.sign_type == 1)
	self.node_list.fashion_item_type_2:CustomSetActive(self.data.sign_type == 2)
	self.node_list.fashion_item_type_3:CustomSetActive(self.data.sign_type == 3)
    self.fashion_item:SetData({item_id = self.data.model_show_itemid})
	self.node_list.item_name.text.text = self.data.get_way_title
    self.node_list.desc.text.text = self.data.get_way_desc
end

function AssistantFashionItemRender:OnSelectChange(is_select)
	self.node_list.select:CustomSetActive(is_select)
end