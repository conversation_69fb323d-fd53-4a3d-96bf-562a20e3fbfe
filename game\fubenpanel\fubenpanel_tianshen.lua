--天神副本
FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

function FuBenPanelView:InitTianShen()
    self.cur_select_index = 1
    self.tianshen_list = AsyncListView.New(TianShenFbItemRender, self.node_list["tianshen_list"])
    self.tianshen_list:SetSelectCallBack(BindTool.Bind(self.OnClickTianShenRender, self))
    self.tianshen_list:SetDefaultSelectIndex(1)
    self.tianshen_reward_list = AsyncListView.New(TianShenFbRewardItemRender, self.node_list["tianshen_reward_list"])

    self:InitTianShenDescContent()

    XUI.AddClickEventListener(self.node_list["btn_tianshen_saodang"], BindTool.Bind(self.OnClickTianShenSaoDang, self))     --扫荡
    XUI.AddClickEventListener(self.node_list["btn_tianshen_enter"], BindTool.Bind(self.OnClickEnterTianshenFb, self))       --进入
    XUI.AddClickEventListener(self.node_list["btn_tianshen_add_count"], BindTool.Bind(self.OnClickTianShenAddCount, self))  --添加次数
    XUI.AddClickEventListener(self.node_list["btn_tianshen_rule"], BindTool.Bind(self.OnClickTianShenRule, self))  --添加次数
end

function FuBenPanelView:InitTianShenDescContent()
    self.node_list.tianshen_fb_desc.text.text = Language.FuBenPanel.FengShenTaiFBDesc

    for i = 1, 9 do
		if Language.FuBenPanel.FengShenTaiShuoMing[i] then
			self.node_list["ts_fb_info_desc"..i]:SetActive(true)
			self.node_list["ts_fb_info_desc"..i].text.text = Language.FuBenPanel.FengShenTaiShuoMing[i]
		else
			self.node_list["ts_fb_info_desc"..i]:SetActive(false)
		end
	end
end

function FuBenPanelView:DeleteTianShen()
    if self.tianshen_list then
        self.tianshen_list:DeleteMe()
        self.tianshen_list = nil
    end
    if self.tianshen_reward_list then
        self.tianshen_reward_list:DeleteMe()
        self.tianshen_reward_list = nil
    end

    if self.enter_tianshen_alert then
        self.enter_tianshen_alert:DeleteMe()
        self.enter_tianshen_alert = nil
    end

    self.sh_boss_model = nil
    self.old_select_index = nil
    self.cur_select_index = 1
end

--function FuBenPanelView:ShouIndexTianShenn(index)
--
--end


function FuBenPanelView:_SetList()
    if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_tianshen) then
        return
    end
    if self.tianshen_list then
        local data_list = FuBenPanelWGData.Instance:GetTianShenDataList()
        self.tianshen_list:SetDataList(data_list, 3)
    end
    self:_SetSaoDangBtn()
end

function FuBenPanelView:_SetSaoDangBtn()
    if self.node_list["btn_tianshen_saodang_text"] then
        --扫荡按钮
        local main_vo = GameVoManager.Instance:GetMainRoleVo()
        local limit_level = FuBenPanelWGData.Instance:GetTianShenSaoDangLevelLimit(FUBEN_TYPE.FBCT_TIANSHEN_FB)
        --self.node_list["btn_tianshen_saodang"]
        if limit_level <= main_vo.level then
            local star_num = FuBenPanelWGData.Instance:GetStarCountByLayer(self.cur_select_index)
            if star_num < 3 then
                XUI.SetButtonEnabled(self.node_list["btn_tianshen_saodang"], false)
                self.node_list["btn_tianshen_saodang_text"].text.text = Language.FuBenPanel.ThreeStarCanSaoDang
            else
                XUI.SetButtonEnabled(self.node_list["btn_tianshen_saodang"], true)
                self.node_list["btn_tianshen_saodang_text"].text.text = Language.FuBenPanel.TianShenSaoDangText_Can
            end
        end
       
        -- 扫荡按钮显隐逻辑
        -- local show_level = FuBenPanelWGData.Instance:GetTianShenSaoDangShowLevelLimit()
        local cfg = FuBenWGData.Instance:GetSaoDangCfg(FUBEN_TYPE.FBCT_TIANSHEN_FB)
        if cfg then
            self.node_list.btn_tianshen_saodang:SetActive(main_vo.level >= cfg.pre_show_level)
        else
            self.node_list.btn_tianshen_saodang:SetActive(false)
        end
    end
end

function FuBenPanelView:_InternalFlushTSList()
    self:_SetList()
    local max_layer = FuBenPanelWGData.Instance:GetTianShenMaxLayerCount()
    local cur_layer = FuBenPanelWGData.Instance:GetTianShenCurLayer()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local index = 1
    local cfg = FuBenPanelWGData.Instance:GetTianShenFbCfg(cur_layer)
    if cfg then
        if cfg.need_level <= role_level then
            index = cfg.layer
        else
            index = cur_layer - 1
        end
    else
        index = cur_layer
    end

    local data_list = FuBenPanelWGData.Instance:GetTianShenDataList()
    for i = 1, #data_list do
        if data_list[i].layer == index then
            index = i
        end
    end

    local jump_layer = MathClamp(index, 1, max_layer)
    self.tianshen_list:JumpToIndex(jump_layer)
end

function FuBenPanelView:OnClickTianShenRender(cell, index)
    local data = cell.data

    if IsEmptyTable(data) then
        return
    end

    if self.old_select_index == data.layer then
        return
    end
    self.cur_select_index = data.layer
    self:_FlushTS()
    self.old_select_index = data.layer
end

--点击扫荡
function FuBenPanelView:OnClickTianShenSaoDang()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local cfg = FuBenWGData.Instance:GetSaoDangCfg(FUBEN_TYPE.FBCT_TIANSHEN_FB)
	local limit_level = cfg.level_limit

    local star_num = FuBenPanelWGData.Instance:GetStarCountByLayer(self.cur_select_index)
    local can_enter_times = FuBenPanelWGData.Instance:GetTianShenCanEnterTimes()
    local item_id = FuBenPanelWGData.Instance:GetTianShenItemId()
    local item_config = ItemWGData.Instance:GetItemConfig(item_id)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    if role_level >= limit_level then
		if star_num <= 2 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CannotSweep)
			return
		end

		if can_enter_times > 0 then
            FuBenWGCtrl.Instance:ShowSaoDangPanel(FUBEN_TYPE.FBCT_TIANSHEN_FB, self.cur_select_index, nil, nil)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBenPanel.FunNotOpen, limit_level))
	end
end

--点击进入天神副本
function FuBenPanelView:OnClickEnterTianshenFb(is_guide)
	-- if is_guide then
	-- 	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_FAKE_TIANSHEN_FB)
	-- 	return
	-- end
    if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end
    local main_vo = GameVoManager.Instance:GetMainRoleVo()
    local need_level = FuBenPanelWGData.Instance:GetTianShenFbNeedLevel(self.cur_select_index)
    if need_level > main_vo.level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.TianShenEnterTip1)
        return
    end

    local cur_layer = FuBenPanelWGData.Instance:GetTianShenCurLayer()
    if cur_layer then
        if self.cur_select_index > cur_layer then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.TianShenPassTip)
            return
        end
    end

    local can_enter_times = FuBenPanelWGData.Instance:GetTianShenCanEnterTimes()
    local item_id = FuBenPanelWGData.Instance:GetTianShenItemId()
    local item_config = ItemWGData.Instance:GetItemConfig(item_id)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    if can_enter_times <= 0 then
        if item_num > 0 then
            --消耗材料进入
            if not self.enter_tianshen_alert then
                self.enter_tianshen_alert = Alert.New(nil,nil,nil,nil,true)
                self.enter_tianshen_alert:SetShowCheckBox(true)
            end

            self.enter_tianshen_alert:SetLableString(string.format(Language.FuBenPanel.UseItemDesc, item_config.name))
            self.enter_tianshen_alert:SetOkFunc(function ()
                local bag_index = ItemWGData.Instance:GetItemIndex(item_id)
                BagWGCtrl.Instance:SendUseItem(bag_index)
                FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_TIANSHEN_FB, self.cur_select_index)
            end)
            self.enter_tianshen_alert:Open()
        else
            FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_TIANSHEN_FB)
        end
    else
        FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_TIANSHEN_FB, self.cur_select_index)
    end
end

--点击增加次数
function FuBenPanelView:OnClickTianShenAddCount()
    FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_TIANSHEN_FB)
end

function FuBenPanelView:OnClickTianShenRule()
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.FuBenPanel.RuleTitle[24])
    role_tip:SetContent(Language.FuBen.TianShen_Des)
end

function FuBenPanelView:OnFlushTianShen(param_t)
    self:_InternalFlushTSList()
    self:OnFlushTianShenAddEffect()
end

function FuBenPanelView:OnFlushTianShenAddEffect()
    if not self:IsOpen() or not self:IsLoadedIndex(TabIndex.fubenpanel_tianshen) then
        return
    end
    if self.node_list.tianshen_add_effect then
        self.node_list.tianshen_add_effect:SetActive(FuBenPanelWGData.Instance:CheckTianShenEffect())
    end
end

function FuBenPanelView:OnFlushBuyTimes(param_t)
    self:_InternalFlushTSOther()
end
function FuBenPanelView:OnFlushEnterTimes(param_t)
    self:_InternalFlushTSOther()
end

function FuBenPanelView:_FlushTS()
    self:_InternalFlushTSRewardCell()
    self:_InternalFlushTSBossDisplay()
    self:_InternalFlushTSOther()
end

--刷新天神副本奖励
function FuBenPanelView:_InternalFlushTSRewardCell()
    local drop_data_list = FuBenPanelWGData.Instance:GetTianShenDropList(self.cur_select_index)
    self.tianshen_reward_list:SetDataList(drop_data_list, 3)
end

--刷新天神BOSS模型展示
function FuBenPanelView:_InternalFlushTSBossDisplay()
    if not self.sh_boss_model then
        self.sh_boss_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["RoleDisplay"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.sh_boss_model:SetRenderTexUI3DModel(display_data)
        -- self.sh_boss_model:SetUI3DModel(self.node_list["RoleDisplay"].transform, self.node_list["ModelEvent"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.sh_boss_model)
    end

    local cfg = FuBenPanelWGData.Instance:GetTianShenFbCfg(self.cur_select_index)
    if IsEmptyTable(cfg) then
        return
    end

    -- self.sh_boss_model:SetTianShenModel(cfg.appe_image_id, cfg.index, false, false, SceneObjAnimator.Rest)
    self.sh_boss_model:SetTianShenModel(cfg.appe_image_id, cfg.index, false, nil, SceneObjAnimator.Rest)
end

--刷新其他
function FuBenPanelView:_InternalFlushTSOther()
    --每天免费次数
    local day_free_times = FuBenPanelWGData.Instance:GetTianShenDayFreeTimes()
    local day_enter_times = FuBenPanelWGData.Instance:GetTianShenEnterTimes()
    local day_buy_times = FuBenPanelWGData.Instance:GetTianShenBuyTimes()
    local day_comsume_item_times = FuBenPanelWGData.Instance:GetTianShenComsumeItemTimes()
    --local vip_max_buy_times = VipPower.Instance:GetParam(VipPowerId.reward_tianshen_fb_buy_times)
    local current = (day_free_times + day_buy_times + day_comsume_item_times - day_enter_times)
    local color = current > 0 and COLOR3B.GREEN or COLOR3B.RED
    self.node_list["lbl_tianshen_count"].text.text = ToColorStr(current.. "/" .. (day_free_times + day_buy_times + day_comsume_item_times),color)

    self:_SetSaoDangBtn()
    self:OnFlushTianShenAddEffect()
    local ver_layer_name, hor_fb_name = FuBenPanelWGData.Instance:GetTianShenFbVerName(self.cur_select_index)
    self.node_list.layer_name.text.text = ver_layer_name
    self.node_list.tianshen_fb_model_name.text.text = ver_layer_name

    --星星
    local cur_layer = FuBenPanelWGData.Instance:GetTianShenCurLayer()
    if cur_layer then
        if self.cur_select_index < cur_layer then
            --显示三星
            for i = 1, 3 do
                self.node_list["tianshen_star"..i]:CustomSetActive(true)
            end
        elseif self.cur_select_index == cur_layer then
            --显示当前星星
            local star_count = FuBenPanelWGData.Instance:GetTianShenStarCount()
            for i = 1, 3 do
                self.node_list["tianshen_star"..i]:CustomSetActive(i <= star_count)
            end
        elseif self.cur_select_index > cur_layer then
            --显示0星
            for i = 1, 3 do
                self.node_list["tianshen_star"..i]:CustomSetActive(false)
            end
        end
    end
	
	--首次通关TIPS
    local star_num = FuBenPanelWGData.Instance:GetStarCountByLayer(self.cur_select_index)
    self.node_list["first_pass_text"]:CustomSetActive(star_num < 0)
end


----------------------TianShenFbItemRender------------------------
TianShenFbItemRender = TianShenFbItemRender or BaseClass(BaseRender)
function TianShenFbItemRender:__init()

end
function TianShenFbItemRender:__delete()

end
function TianShenFbItemRender:OnFlush()
    --名字
    self.node_list["name"].text.text = self.data.fb_name
    self.node_list["name_hl"].text.text = self.data.fb_name

    --星级
    local main_vo = GameVoManager.Instance:GetMainRoleVo()
    --等级条件不到直接显示多少级开启Tips
    if self.data.need_level > main_vo.level then
        local cur_layer = FuBenPanelWGData.Instance:GetTianShenCurLayer()
        if self.data.layer > cur_layer - 1 then
            self.node_list["star_container"]:CustomSetActive(false)
            self.node_list["desc"]:CustomSetActive(true)
            self.node_list["lock"]:CustomSetActive(true)
            --XUI.SetGraphicGrey(self.node_list.chahua, true)
            self.node_list["desc"].text.text = string.format(Language.FuBenPanel.LevelAstrict, self.data.need_level)
        else
             --通过了直接显示所有星星
            self.node_list["desc"]:CustomSetActive(false)
            self.node_list["lock"]:CustomSetActive(false)
           -- XUI.SetGraphicGrey(self.node_list.chahua, false)
            self.node_list["star_container"]:CustomSetActive(true)
            for i = 1, 3 do
                self.node_list["star"..i]:CustomSetActive(true)
            end
        end
    else
        --local pass_layer = FuBenPanelWGData.Instance:GetTianShenPassLayer()
        local cur_layer = FuBenPanelWGData.Instance:GetTianShenCurLayer()
        if self.data.layer > cur_layer - 1 then
            if self.data.layer == cur_layer then
                self.node_list["desc"]:CustomSetActive(false)
                self.node_list["lock"]:CustomSetActive(false)
               -- XUI.SetGraphicGrey(self.node_list.chahua, false)
                self.node_list["star_container"]:CustomSetActive(true)
                --显示对应星数
                local star_count = FuBenPanelWGData.Instance:GetTianShenStarCount()
                for i = 1, 3 do
                    self.node_list["star"..i]:CustomSetActive(i <= star_count)
                end
            else
                self.node_list["desc"].text.text = Language.FuBenPanel.TianShenPassTip
                self.node_list["desc"]:CustomSetActive(true)
                self.node_list["lock"]:CustomSetActive(true)
                --XUI.SetGraphicGrey(self.node_list.chahua, true)
                self.node_list["star_container"]:CustomSetActive(false)
            end
        else
             --通过了直接显示所有星星
            self.node_list["desc"]:CustomSetActive(false)
            self.node_list["lock"]:CustomSetActive(false)
           -- XUI.SetGraphicGrey(self.node_list.chahua, false)
            self.node_list["star_container"]:CustomSetActive(true)
            for i = 1, 3 do
                self.node_list["star"..i]:CustomSetActive(true)
            end
        end
    end

    -- local real_index = self.index % 5
    -- real_index = real_index == 0 and 5 or real_index
    -- self.node_list["chahua"].image:LoadSprite(ResPath.GetFuBenPanel("ts_img" .. real_index))

end

function TianShenFbItemRender:OnSelectChange(isSelect)
    self.node_list["HLImage"]:SetActive(isSelect)
    self.node_list["Image"]:SetActive(not isSelect)
    -- if isSelect then
    --     self.node_list.root.transform.localScale = Vector3(1,1,1)
    --     --if self.node_list.root.transform.localScale.x ~= 1.05 then
    --     --    self.node_list.root.transform:DOScale(Vector3(1.05,1.05,1.05), 0.2)
    --     --end
    -- else
    --     self.node_list.root.transform.localScale = Vector3.one
    --     --if self.node_list.root.transform.localScale.x ~= 1 then
    --     --    self.node_list.root.transform:DOScale(Vector3.one, 0.2)
    --     --end
    -- end
end




----------------------TianShenFbRewardItemRender------------------------
TianShenFbRewardItemRender = TianShenFbRewardItemRender or BaseClass(BaseRender)
function TianShenFbRewardItemRender:__init()
    self.base_cell = ItemCell.New(self.node_list["pos"])
    self.base_cell:SetIsShowTips(true)
end
function TianShenFbRewardItemRender:__delete()
    if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
end
function TianShenFbRewardItemRender:OnFlush()
    -- self.data.num = 0 --2019/10/15策划说屏蔽数量显示
    self.base_cell:SetData({item_id = self.data.item_id})
    local new_flag = FuBenPanelWGData.Instance:GetIsNew(self.data.item_id)
    self.node_list.three_flag:SetActive(self.data.is_three_must_drop == true)
    -- self.node_list.new_flag:SetActive(new_flag)
end