ChargeRepayment2WGData = ChargeRepayment2WGData or BaseClass()

function ChargeRepayment2WGData:__init()
	if ChargeRepayment2WGData.Instance then
		Error<PERSON><PERSON>("[ChargeRepayment2WGData] attempt to create singleton twice!")
		return
	end
	ChargeRepayment2WGData.Instance = self
	
	self.reward_active_flag = 0 			-- 当前激活的充值奖励
	self.reward_fetch_flag = 0 				-- 当前已获取的充值奖励
	self.history_charge_during_act = 0 		-- 活动期间的累积充值额
end

function ChargeRepayment2WGData:__delete()
	ChargeRepayment2WGData.Instance = nil
end

function ChargeRepayment2WGData:GetPaymentData()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = rand_config.charge_repayment2
	local charge_repayment2_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2)
	return charge_repayment2_cfg
end

function ChargeRepayment2WGData:SetPayMent2Info(protocol)
	self.reward_active_flag = protocol.reward_active_flag
	self.reward_fetch_flag = protocol.reward_fetch_flag
	self.history_charge_during_act = protocol.history_charge_during_act
end


function ChargeRepayment2WGData:GetRewardHasActiveFlag()
	return self.reward_active_flag
end

function ChargeRepayment2WGData:GetRewardHasFetchFlag()
	return self.reward_fetch_flag
end

function ChargeRepayment2WGData:GetTotalRecharge()
	return self.history_charge_during_act
end

function ChargeRepayment2WGData:GetChargeReward2Num()
	local num = 0
	local has_fetch_reward_flag = self:GetRewardHasFetchFlag()
	local oga_fetch_flag = bit:d2b(has_fetch_reward_flag)
	
	local reward_active_flag = self:GetRewardHasActiveFlag()
	local oga_active_flag = bit:d2b(reward_active_flag)

	for i=0, 5 do
		local is_auto_fetch = oga_fetch_flag[32 - i] == 1
		local is_auto_active = oga_active_flag[32 - i] == 1
		if is_auto_active and (not is_auto_fetch) then 
			num = num + 1
		end
	end
	return num
end