function LoverPkView:LoadFSZZ<PERSON>allBack()
    self.left_model = RoleModel.New()
    local left_display_data = {
        parent_node = self.node_list["left_model"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }

    self.left_model:SetRenderTexUI3DModel(left_display_data)
    -- self.left_model:SetUI3DModel(self.node_list.left_model.transform, self.node_list.left_model.event_trigger_listener,
    --     1, false, MODEL_CAMERA_TYPE.BASE)

    self.right_model = RoleModel.New()
    local right_display_data = {
        parent_node = self.node_list["right_model"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }

    self.right_model:SetR<PERSON>TexUI3DModel(right_display_data)
    -- self.right_model:SetUI3DModel(self.node_list.right_model.transform, self.node_list.right_model.event_trigger_listener,
    --     1, false, MODEL_CAMERA_TYPE.BASE)

    if not self.reward_list then
        self.reward_list = StrengthenAsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["btn_zhanji"], BindTool.Bind(self.ClickZhanJiBtn,self))
    XUI.AddClickEventListener(self.node_list["duizhen_btn"], BindTool.Bind(self.ClickDuiZhen,self))
    XUI.AddClickEventListener(self.node_list["hq_reward_btn"], BindTool.Bind(self.ClickHQRewardBtn,self))
    XUI.AddClickEventListener(self.node_list["btn_fszz_matching"], BindTool.Bind(self.ClickFSZZMatchIngBtn,self))
    XUI.AddClickEventListener(self.node_list["fszz_reward_item"], BindTool.Bind(self.ClickFSZZRewardItem,self))

    self.node_list.fszz_time_tips.text.text = Language.LoverPK.ActiveOpenTimeDesc
    self.node_list.rule_dec.text.text = Language.LoverPK.RuleDec

    local unlock_record_title = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("fszz_show_title")
	self.node_list["fszz_reward_item"]:ChangeAsset(ResPath.GetTitleModel(unlock_record_title))

    local reward_data_list = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("fszz_reward_list")
    self.reward_list:SetDataList(reward_data_list)

    self.fszz_role_uuid_cache1 = {}
    self.fszz_role_uuid_cache2 = {}

    self.node_list.no_model_left:CustomSetActive(true)
    self.node_list.no_model_right:CustomSetActive(true)
    self.node_list.left_name:CustomSetActive(false)
    self.node_list.right_name:CustomSetActive(false)
end

function LoverPkView:ReleaseFSZZCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.left_model then
        self.left_model:DeleteMe()
        self.left_model = nil
    end

    if self.right_model then
        self.right_model:DeleteMe()
        self.right_model = nil
    end

    self.fszz_role_uuid_cache1 = nil
    self.fszz_role_uuid_cache2 = nil
end

function LoverPkView:FlushFSZZCallBack()
    self:FlushModelInfo()

    local duizhen_remind = LoverPkWGData.Instance:GetLoverPKDZBRemind()
    self.node_list.duizhen_btn_remind:CustomSetActive(duizhen_remind)
end

function LoverPkView:ClickHQRewardBtn()
    LoverPkWGCtrl.Instance:OpenLoverPKBountyView()
end

function LoverPkView:ClickZhanJiBtn()
    LoverPkWGCtrl.Instance:OpenLoverPKMsgView(TabIndex.lover_pk_msg_fsjl)
end

function LoverPkView:ClickFSZZMatchIngBtn()
    local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    if not act_isopen then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.ActNotOpenCannotEnter)
        return
    end

    -- local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
    -- if lover_id <= 0 then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.NoLoverCannotEnter)
    --     return
    -- end

    local cur_scene_type = Scene.Instance:GetSceneType()
    if cur_scene_type == SceneType.CROSS_PK_LOVER_READY then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.RoleIsInThePreparScene)
        return
    end
 
    CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    self:Close()
end

function LoverPkView:ClickDuiZhen()
    LoverPkWGCtrl.Instance:OpenLoverPKMsgView(TabIndex.lover_pk_msg_dzb)
end

function LoverPkView:ClickFSZZRewardItem()
    local item_data = CommonStruct.ItemDataWrapper()
	local unlock_record_title = LoverPkWGData.Instance:GetOtherCfgDataByAttrName("fszz_show_title")
	local title_cfg = TitleWGData.Instance:GetConfig(unlock_record_title)
	item_data.item_id = title_cfg.item_id
	TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL)
end

function LoverPkView:FlushModelInfo()
    local user_id = RoleWGData.Instance:InCrossGetOriginUid()
    local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true}

    local lock_sex = -1
    local model_render = nil

    local set_model_data = function (role_vo)
        local sex = role_vo.sex
        local role_name = role_vo.role_name
        local is_dianfeng, show_level = RoleWGData.Instance:GetDianFengLevel(role_vo.level)
        local role_level_str = show_level .. Language.Tip.Ji

        if lock_sex >= 0 then
            if lock_sex == GameEnum.FEMALE then
                model_render = self.right_model
                self.node_list.no_model_right:CustomSetActive(false)
                self.node_list.right_name:CustomSetActive(true)
                self.node_list.right_name_text.text.text = role_name
                self.node_list.right_dianfeng_img:CustomSetActive(is_dianfeng)
                self.node_list.right_level.text.text = role_level_str
            else
                model_render = self.left_model
                self.node_list.no_model_left:CustomSetActive(false)
                self.node_list.left_name:CustomSetActive(true)
                self.node_list.left_name_text.text.text = role_name
                self.node_list.left_dianfeng_img:CustomSetActive(is_dianfeng)
                self.node_list.left_level.text.text = role_level_str
            end
        else
            if sex == GameEnum.FEMALE then
                lock_sex = GameEnum.MALE
                model_render = self.right_model
                self.node_list.no_model_right:CustomSetActive(false)
                self.node_list.right_name:CustomSetActive(true)
                self.node_list.right_name_text.text.text = role_name
                self.node_list.right_dianfeng_img:CustomSetActive(is_dianfeng)
                self.node_list.right_level.text.text = role_level_str
            else
                lock_sex = GameEnum.FEMALE
                model_render = self.left_model
                self.node_list.no_model_left:CustomSetActive(false)
                self.node_list.left_name:CustomSetActive(true)
                self.node_list.left_name_text.text.text = role_name
                self.node_list.left_dianfeng_img:CustomSetActive(is_dianfeng)
                self.node_list.left_level.text.text = role_level_str
            end
        end

        if model_render then
            model_render:SetModelResInfo(role_vo, ignore_table, function()
            end)
            model_render:FixToOrthographic(self.root_node_transform)
        end
    end
    
    local set_model_info = function (uuid)
        local role_id = uuid.temp_low or -1
        local plat_type = uuid.temp_high or -1
        local has_role_data = role_id > 0

        if has_role_data then
            if user_id == role_id then
                local role_vo = RoleWGData.Instance:GetRoleVo()
                set_model_data(role_vo)
            else
                BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
                    set_model_data(protocol)
                end, plat_type, true)
            end
        end
    end

    local role_uuid1, role_uuid2 = LoverPkWGData.Instance:GetFSZZRoleUUID()
    if self.fszz_role_uuid_cache1 ~= role_uuid1 then
        set_model_info(role_uuid1)
        self.fszz_role_uuid_cache1 = role_uuid1
    end

    if self.fszz_role_uuid_cache2 ~= role_uuid2 then
        set_model_info(role_uuid2)
        self.fszz_role_uuid_cache2 = role_uuid2
    end
end
