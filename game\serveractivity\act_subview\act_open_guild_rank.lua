---------------
--仙盟封榜
---------------
function ServerActivityTabView:XMFBReleaseCallBack()
	self.xmfb_select_index = nil
	self.xmfb_title_id = 0
    if self.xmfb_rank_list then
    	self.xmfb_rank_list:DeleteMe()
    	self.xmfb_rank_list = nil
    end
    if self.xmfb_title_list then
		self.xmfb_title_list:DeleteMe()
    	self.xmfb_title_list = nil
    end
    CountDownManager.Instance:RemoveCountDown("act_open_guild_rank_count_down")
end

function ServerActivityTabView:XMFBLoadCallBack()
	self.xmfb_title_id = 0
	self.xmfb_select_index = 1

	self.xmfb_guild_name_list = {}
	for i=1, 4 do
		self.xmfb_guild_name_list[i] = self.node_list["xmfb_guild_name_"..i]
		self.node_list["xmfb_toggle_"..i].toggle:AddValueChangedListener(BindTool.Bind(self.XMFBOnClickToggle, self, i))
	end

	self.node_list.xmfb_tips_btn.button:AddClickListener(BindTool.Bind(self.XMFBOnClickTipsBtn, self))
	self.node_list.xmfb_peidai_btn.button:AddClickListener(BindTool.Bind(self.XMFBOnClickPeiDai, self))

	self.xmfb_rank_list = AsyncListView.New(ServerActivityRankListRender, self.node_list.xmfb_rank_list)

	self.xmfb_title_list = AsyncListView.New(TitleListRender, self.node_list.xmfb_title_list)
	local act_cfg = ServerActivityWGData.Instance:GetKaiZongLiPaiNewCfg()
	if act_cfg then
		self.xmfb_title_list:SetDataList(act_cfg)
	end

	self:XMFBFlushCountDownTime()
	self:XMFBRefreshView()
end

function ServerActivityTabView:XMFBOnClickToggle(index, is_on)
	if is_on and self.xmfb_select_index ~= index then
		self.xmfb_select_index = index
		self:XMFBRefreshView()
	end
end

function ServerActivityTabView:XMFBOnClickPeiDai()
	local title_id = self.xmfb_title_id
	if title_id > 0 then
		if TitleWGData.Instance:IsUsedTitle(title_id) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.YiPeiDaiTitle)
			return
		end
		TitleWGCtrl.Instance:SendUseTitleReq({title_id})
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.WeiPeiDaiTips)
	end
end

function ServerActivityTabView:XMFBOnClickTipsBtn()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.OpenServer.ZuiqiangXianmeng)
	rule_tip:SetContent(Language.OpenServer.XianMengFengBangTips)
end

function ServerActivityTabView:XMFBShowCallBack()
	GuildWGCtrl.Instance:SendCSGuildMemberCapabilityRank()
end

function ServerActivityTabView:XMFBOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "guild_rank" then
			self:XMFBRefreshView()
		end
	end
end

function ServerActivityTabView:XMFBRefreshView()
	local rank_list = GuildWGData.Instance:GetGuildMemberRankInfo()
	for i=1,4 do
		if rank_list[i] and rank_list[i].guild_id ~= 0 then
			self.xmfb_guild_name_list[i].text.text = rank_list[i].guild_name
		else
			self.xmfb_guild_name_list[i].text.text = Language.KuafuGuildBattle.KfNoOccupy
		end
	end

	local title_list = TitleWGData.Instance:GetTitleIdList()
	for _,title_id in ipairs(title_list) do
		local is_act_title, actid = ServerActivityWGData.Instance:IsActGetTitle(title_id)
		if is_act_title and actid == ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI then
			self.xmfb_title_id = title_id
			break
		end
	end
	
	local is_used_title = false
	if self.xmfb_title_id > 0 then
		is_used_title = TitleWGData.Instance:IsUsedTitle(self.xmfb_title_id)
	end
	self.node_list.xmfb_peidai_btn_label.text.text = is_used_title and Language.OpenServer.YiPeiDaiTitle or Language.OpenServer.QuickPeiDai

	local rank_list_data = GuildWGData.Instance:GetGuildMemberRankInfoByIndex(self.xmfb_select_index)

	if not IsEmptyTable(rank_list_data) then
		self.xmfb_rank_list:SetDataList(rank_list_data)
		self.xmfb_rank_list:JumpToTop()
	end
end

function ServerActivityTabView:XMFBFlushCountDownTime()
	CountDownManager.Instance:RemoveCountDown("act_open_guild_rank_count_down")
	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI)
	if count_down_time > 0 then
		self.node_list.xmfb_act_count_down.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"act_open_guild_rank_count_down",
			BindTool.Bind(self.XMFBUpdateCountDown, self),
			BindTool.Bind(self.XMFBFlushCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.xmfb_act_count_down.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.xmfb_act_count_down.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:XMFBUpdateCountDown(elapse_time, total_time)
	self.node_list.xmfb_act_count_down.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
end


-------------------------------------------ServerActivityRankListRender---------------------------------------------------------------

ServerActivityRankListRender = ServerActivityRankListRender or BaseClass(BaseRender)

function ServerActivityRankListRender:LoadCallBack()
	self.node_list.click_btn.button:AddClickListener(BindTool.Bind(self.OnClickItem, self))
end

function ServerActivityRankListRender:OnFlush()
	local data = self:GetData()
	local index = self:GetIndex()
	if index <= 3 then
		self.node_list.rank_num_img.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_" .. index))
	end
	self.node_list.rank_num_img:SetActive(index <= 3)
	self.node_list.rank_num.text.text = index
	if IsEmptyTable(data) or data.user_id <= 0 then
		self.node_list.prower.text.text = "0"
		self.node_list.name.text.text = Language.OpenServer.XuWeiYiDai
		self.node_list.post_label.text.text = ""
		return
	end
	self.node_list.prower.text.text = data.capability
	self.node_list.name.text.text = data.user_name
	self.node_list.post_label.text.text = Language.Guild.GuildPost[data.guild_post]
end

function ServerActivityRankListRender:OnClickItem()
	BrowseWGCtrl.Instance:ShowOtherRoleInfo(self.data.user_id)
end

-------------------------------------------TitleListRender---------------------------------------------------------------

TitleListRender = TitleListRender or BaseClass(BaseRender)

function TitleListRender:OnFlush()
	local data = self:GetData()
	local b,a = ResPath.GetTitleModel(data.title_id)
	self.node_list.title_img:ChangeAsset(b, a, false)

	self.node_list.desc_label_1.text.text = string.format(Language.Common.BestDrops, data.drop_rate / 100000)

	self.node_list.desc_label_3:SetActive(data.max_zhanli ~= data.min_zhanli)
	self.node_list.rank_1:SetActive(data.max_zhanli == data.min_zhanli)

	if data.min_zhanli < 9999 then
		self.node_list.desc_label_3.text.text = string.format(Language.OpenServer.MengNeiZhanLi2, data.max_zhanli, data.min_zhanli)
	else
		self.node_list.desc_label_3.text.text = Language.Guide.RemainMenber
	end
end