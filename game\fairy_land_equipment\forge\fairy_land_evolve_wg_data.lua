local this = FairyLandEquipmentWGData

function this:InitEvolveData()
	RemindManager.Instance:Register(RemindName.FLEF_Evolve, BindTool.Bind(self.IsShowEvolveRedPoint, self))

	local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
	self.gb_equip_upgrade_cfg = ListToMap(main_cfg.gb_equip_upgrade, "slot", "part", "grade")	--圣装进阶
	self.gb_equip_upgrade_attr_cfg = ListToMap(main_cfg.gb_equip_upgrade, "slot", "part", "attr_index")	--圣装进阶
	self.gb_equip_upgrade_list_cfg = ListToMapList(main_cfg.gb_equip_upgrade, "slot", "part")	--圣装进阶
	self.gb_equip_attr_group_cfg = ListToMap(main_cfg.gb_equip_attr_group, "slot", "part")	--进阶属性组
	self.gb_equip_attr_list_cfg = ListToMap(main_cfg.gb_equip_attr_list, "attr_index")		--圣装进阶属性
	self.gb_equip_all_star_cfg = ListToMapList(main_cfg.gb_equip_all_star, "slot")		--圣装进阶属性
	self.gb_equip_add_grade_per_cfg = ListToMap(main_cfg.gb_equip_add_grade_per, "slot", "color", "grade")		--圣装进阶增加概率

	self.sort_evolve_equip_bag = {}
	self.sort_evolve_equip_bag_addper = {}
	self.sort_evolve_spe_equip_bag_addper = {}
	self.evolve_add_per_data_list = {}

	self:RegisterEvolveRemindInBag(RemindName.FLEF_Evolve)

end

function this:DeleteEvolveData()
	RemindManager.Instance:UnRegister(RemindName.FLEF_Evolve)
end

function this:RegisterEvolveRemindInBag(remind_name)
	self.evolve_remind_item_id_map = {}
    local cfg = self:GetGBEquipUpgradeCfg(0, 0, 1)
	if cfg and cfg.stuff_id > 0 then
		self.evolve_remind_item_id_map[cfg.stuff_id] = true
	end

    local item_id_list = {}
    for k,v in pairs(self.evolve_remind_item_id_map) do
        table.insert(item_id_list, k)
    end
    if not IsEmptyTable(item_id_list) then
	    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
	end
end

function this:GetIsEvolveRemindItem(id)
	if self.evolve_remind_item_id_map and self.evolve_remind_item_id_map[id] then
		return true
	end
	return false
end

function this:GetEvolveMasterRemind(slot)
	local cur_total_level = FairyLandEquipmentWGData.Instance:GetEvolveLevelAllLevel(slot)
	local next_attr_cfg = FairyLandEquipmentWGData.Instance:GetEvolveLevelTotalCfg(slot, true)
	local is_max_level = not next_attr_cfg
	local next_level = next_attr_cfg and next_attr_cfg.level or 0
	local can_active = not is_max_level and cur_total_level >= next_level--是否可激活
	return not is_max_level and can_active
end

function this:IsShowEvolveRedPoint()
	local show_list = self:GetGodBodyList()
    if IsEmptyTable(show_list) then
    	return 0
    end

    for k, v in pairs(show_list) do
        local slot = v:GetSlotIndex()
        local is_act = v:GetIsAct()
        if not is_act then 
        	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_EVOLVE, 0)
        	return 0 
        end

        local remind = self:GetEvolveSlotRemind(slot)
		if remind == 1 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_EVOLVE, 1, function ()
				ViewManager.Instance:Open(GuideModuleName.FairyLandEquipmentView,
					TabIndex.fl_eq_forge_evolve, nil, {to_ui_name = slot})
				return true
			end)
			return 1
		end
    end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FLEF_EVOLVE, 0)
	return 0
end

function this:GetEvolveSlotRemind(slot)
	for i = GOD_BODY_ENUM.MAX_HOLY_EQUIP_PART -1, 0, -1 do
		local remind = self:GetEquipNeedAddPer(slot, i)
		if remind == 1 then
			return 1
		end
	end

	local master_remind = self:GetEvolveMasterRemind(slot)
	if master_remind then
		return 1
	end

	return 0
end

function this:GetEvolveJumpPart(slot)
	local list = self:GetHolyEquipWearList(slot)
	if list == nil then
		return 0
	end

	local first_had_data_index
	for i = 0, #list do
		local data = list[i]
		if not first_had_data_index and data.item_id > 0 then
			first_had_data_index = i
		end

		if self:GetEquipNeedAddPer(data.slot, data.part) == 1 then
			return i
		end
	end

	return first_had_data_index or 0
end

function this:GetEquipNeedAddPer(slot, part)
	local cur_equip = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)


	if IsEmptyTable(cur_equip) or not cur_equip.is_wear then return 0 end
	local limit_color = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
	if cur_equip.color < limit_color then return 0 end

	local bag_list_info = FairyLandEquipmentWGData.Instance:GetHolyEquipBagList()
	local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(slot, part)
	local max_grade = FairyLandEquipmentWGData.Instance:GetEvolveMaxGrade()
	if grade >= max_grade then return 0 end
	local upgrade_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpgradeCfg(slot, part, grade + 1)
	local stuff_id = upgrade_cfg and upgrade_cfg.stuff_id or 0
	local init_per = upgrade_cfg and (upgrade_cfg.init_per / 100) or 0
	local add_per = 0
	local need_stuff_num = upgrade_cfg and upgrade_cfg.stuff_num or 0
	local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)

	local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)

	if is_spe_part then
		local color_per = FairyLandEquipmentWGData.Instance:GetSortEvolveSpeEquipBagAddper(slot, grade + 1)
		add_per = add_per + color_per
		if add_per + init_per >= 100 and has_stuff_num >= need_stuff_num then
			return 1
		end
	else
		local color_per = FairyLandEquipmentWGData.Instance:GetSortEvolveEquipBagAddper(slot, grade + 1)
		add_per = add_per + color_per
		if add_per + init_per >= 100 and has_stuff_num >= need_stuff_num then
			return 1
		end
	end

	return 0
end

function this:SortEvolveEquipBagList()
	self.sort_evolve_equip_bag = {}
	self.sort_evolve_equip_bag_addper = {}
	self.sort_evolve_spe_equip_bag_addper = {}
	local bag_list_info = FairyLandEquipmentWGData.Instance:GetHolyEquipBagList()
	if IsEmptyTable(bag_list_info) then
		return
	end

	for k, v in pairs(bag_list_info) do
		if v.item_id > 0 then
			-- local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(v.part)
			local equip_part_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(v.slot,v.part)
			local can_add = equip_part_info and equip_part_info.color >= v.color
			-- if not is_spe_part and can_add then
			if can_add then
				self.sort_evolve_equip_bag[v.index] = v
			end
		end
	end
	self:CaleEvolveEquipBagAddper()
end

function this:CaleEvolveEquipBagAddper()
	self.sort_evolve_equip_bag_addper = {}
	self.sort_evolve_spe_equip_bag_addper = {}
	if not IsEmptyTable(self.sort_evolve_equip_bag) then
		for k,equip in pairs(self.sort_evolve_equip_bag) do
			local equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(equip.item_id)
			local show_list = self:GetGodBodyList()
			local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(equip.part)
			for i,v in pairs(show_list) do
				local slot = v:GetSlotIndex()
				local is_act = v:GetIsAct()
				if is_act then
					local color = equip.color
					local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(equip.slot, equip.part)
					self:CaleEvolveEquipGradeAddper(slot, color,true, is_spe_part)
				end
			end	
		end
		
	end
end

function this:CaleEvolveEquipGradeAddper(slot, color, is_add, is_spe_part)
	local max_grade = FairyLandEquipmentWGData.Instance:GetEvolveMaxGrade()
	for temp_grade=1,max_grade do
		local per_cfg = self:GetEvolveEquipAddGradePerCfg(slot, color,temp_grade)
		local add_per_key = "add_per_list"
		local add_specail_per_key = "add_specail_per_list"

		if is_spe_part then
			add_per_key = "add_per_list_2"
			add_specail_per_key = "add_specail_per_list_2"
		end

		if per_cfg and per_cfg[add_per_key] then
			local add_per_list = per_cfg[add_per_key]
			local slot_add_per = add_per_list[slot + 1] or 0
			self.sort_evolve_equip_bag_addper[slot] = self.sort_evolve_equip_bag_addper[slot] or {}
			if self.sort_evolve_equip_bag_addper[slot][temp_grade] then
				local temp_per = self.sort_evolve_equip_bag_addper[slot][temp_grade]
				if is_add then
					self.sort_evolve_equip_bag_addper[slot][temp_grade] = temp_per + (slot_add_per / 100)
				else
					self.sort_evolve_equip_bag_addper[slot][temp_grade] = temp_per - (slot_add_per / 100)
					local cur_per = self.sort_evolve_equip_bag_addper[slot][temp_grade]
					self.sort_evolve_equip_bag_addper[slot][temp_grade] = cur_per < 0 and 0 or cur_per
				end
			else
				if is_add then
					self.sort_evolve_equip_bag_addper[slot][temp_grade] = (slot_add_per / 100) or 0
				else
					self.sort_evolve_equip_bag_addper[slot][temp_grade] = 0
				end
			end
		end
		if per_cfg and per_cfg[add_specail_per_key] then
			local add_specail_per_list = per_cfg[add_specail_per_key]
			local slot_add_specail_per = add_specail_per_list[slot + 1] or 0
			self.sort_evolve_spe_equip_bag_addper[slot] = self.sort_evolve_spe_equip_bag_addper[slot] or {}
			if self.sort_evolve_spe_equip_bag_addper[slot][temp_grade] then
				local temp_per = self.sort_evolve_spe_equip_bag_addper[slot][temp_grade]
				if is_add then
					self.sort_evolve_spe_equip_bag_addper[slot][temp_grade] = temp_per + (slot_add_specail_per / 100)
				else
					self.sort_evolve_spe_equip_bag_addper[slot][temp_grade] = temp_per + (slot_add_specail_per / 100)
					local cur_per = self.sort_evolve_spe_equip_bag_addper[slot][temp_grade]
					self.sort_evolve_spe_equip_bag_addper[slot][temp_grade] = cur_per < 0 and 0 or cur_per
				end
			else
				if is_add then
					self.sort_evolve_spe_equip_bag_addper[slot][temp_grade] = (slot_add_specail_per / 100) or 0
				else
					self.sort_evolve_spe_equip_bag_addper[slot][temp_grade] = 0
				end
			end
		end
	end
end

--需要重新缓存进化背包数据
function this:SetEvolveNeedReCaleEquip()
	self.need_re_cale_bag = true
end

--进化装备背包中单个格子的改变
function this:SortEvolveSingleEquipBag(change_reason, change_info, old_info)
	if self.need_re_cale_bag then
		self:SortEvolveEquipBagList()
		self.need_re_cale_bag = false
	else
		if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
			self:AddEvolveSingleEquipBag(change_info)        		--增
		elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
			self:ChangeEvolveSingleEquipBag(change_info, old_info)	--改
		elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
			self:RemoveEvolveSingleEquipBag(old_info)				--删
		end
	end
end

function this:AddEvolveSingleEquipBag(change_info)
	if IsEmptyTable(change_info) then return end
	local eq_slot = change_info.slot
	local part = change_info.part
	local color = change_info.color
	local index = change_info.index
	-- local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)
	local equip_part_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(eq_slot, part)
	local can_add = equip_part_info and equip_part_info.color >= color

	-- if not is_spe_part and can_add then
	if can_add then
		self.sort_evolve_equip_bag[index] = change_info
		local equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(change_info.item_id)
		local show_list = self:GetGodBodyList()
		local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)
		for k,v in pairs(show_list) do
			local slot = v:GetSlotIndex()
			local is_act = v:GetIsAct()
			if is_act then
				local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(eq_slot, part)
				self:CaleEvolveEquipGradeAddper(slot, color,true, is_spe_part)
			end
		end
	end
end

function this:RemoveEvolveSingleEquipBag(old_info)
	if IsEmptyTable(old_info) then return end
	if self.sort_evolve_equip_bag[old_info.index] then
		local eq_slot = old_info.slot
		local part = old_info.part
		local color = old_info.color
		local index = old_info.index
		self.sort_evolve_equip_bag[index] = nil

		-- local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)
		-- if not is_spe_part then
			local equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(old_info.item_id)
			local show_list = self:GetGodBodyList()
			local is_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)
			for k,v in pairs(show_list) do
				local slot = v:GetSlotIndex()
				local is_act = v:GetIsAct()
				if is_act then
					local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(eq_slot, part)
					self:CaleEvolveEquipGradeAddper(slot, color, false, is_spe_part)
				end
			end
		-- end
	end
end

function this:ChangeEvolveSingleEquipBag(change_info, old_info)
	self:RemoveEvolveSingleEquipBag(old_info)
	self:AddEvolveSingleEquipBag(change_info)
end

function this:GetSortEvolveEquipBag()
	return self.sort_evolve_equip_bag
end

function this:GetSortEvolveEquipBagData(slot, color)
	return self.sort_evolve_equip_bag[slot] and self.sort_evolve_equip_bag[slot][color]
end

function this:GetSortEvolveEquipBagList(slot)
	return self.sort_evolve_equip_bag[slot]
end

function this:GetSortEvolveEquipBagAddper(slot, grade)
	-- return self.sort_evolve_equip_bag_addper[slot] and self.sort_evolve_equip_bag_addper[slot][color] or 0
	return self.sort_evolve_equip_bag_addper[slot] and self.sort_evolve_equip_bag_addper[slot][grade] or 0
end

function this:GetSortEvolveSpeEquipBagAddper(slot, grade)
	-- return self.sort_evolve_spe_equip_bag_addper[slot] and self.sort_evolve_spe_equip_bag_addper[slot][color] or 0
	return self.sort_evolve_spe_equip_bag_addper[slot] and self.sort_evolve_spe_equip_bag_addper[slot][grade] or 0
end

function this:GetGBEquipUpgradeCfg(slot, part, grade)
	return self.gb_equip_upgrade_cfg and self.gb_equip_upgrade_cfg[slot] and self.gb_equip_upgrade_cfg[slot][part]
			and self.gb_equip_upgrade_cfg[slot][part][grade]
end

function this:GetGBEquipUpgradeListCfg(slot, part)
	return (self.gb_equip_upgrade_list_cfg[slot] or {})[part] or {}
end

function this:GetGBEquipUpgradeAttrCfg(slot, part, attr_index)
	return self.gb_equip_upgrade_attr_cfg and self.gb_equip_upgrade_attr_cfg[slot] and self.gb_equip_upgrade_attr_cfg[slot][part]
			and self.gb_equip_upgrade_attr_cfg[slot][part][attr_index]
end

function this:GetGBEquipAttrGroupCfg(slot, part)
	return self.gb_equip_attr_group_cfg and self.gb_equip_attr_group_cfg[slot] and self.gb_equip_attr_group_cfg[slot][part]
end

function this:GetGBEquipAttrGroupAttrList(slot, part)
	local cfg = self:GetGBEquipAttrGroupCfg(slot, part)
	local data_list = {}
	if not IsEmptyTable(cfg) then
		local attr_list = cfg.attr_list
		local attr_split = Split(attr_list, "|")
		-- return attr_split
		for k,v in pairs(attr_split) do
			local data = {}
			data.attr_index = tonumber(v)
			local cfg2 = self:GetGBEquipUpgradeAttrCfg(slot, part, data.attr_index)
			local attr_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipAttrListCfg(data.attr_index)
			local is_act = FairyLandEquipmentWGData.Instance:GetPartRandAttrIsAct(slot, part,data.attr_index)
			data.is_act = is_act and 1 or 0
			data.is_star = attr_cfg and attr_cfg.is_star or 0
			data.grade = cfg2 and cfg2.grade or 0
			data.attr_cfg = attr_cfg
			table.insert(data_list,data)
		end
		SortTools.KeyLowerSorters(data_list, "grade")
	end
	return data_list
end

function this:GetCurEvolveSlotEquipList(slot)
	local equip_info_list = FairyLandEquipmentWGData.Instance:GetHolyEquipWearList(slot)
	local list_data = {}
	if IsEmptyTable(equip_info_list) then return list_data end
	local equip_list = equip_info_list
	local max_grade = FairyLandEquipmentWGData.Instance:GetEvolveMaxGrade()
	local old_max_grade_num = self.evolve_max_grade_num or 0
	local old_max_equip_num = self.evolve_max_equip_num or 0
	self.evolve_max_grade_num = 0
	local limit_color = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
	for i = 0, XIANJIE_EQUIP_TYPE.XIANYIN do
		local equip_info = equip_list[i]
		if equip_info then
			if equip_info.item_id > 0 then
				local data = {}
				data.info = equip_info
				local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(equip_info.slot, equip_info.part)
				data.is_max = grade >= max_grade and 1 or 0
				data.part = i
				data.can_evolve = equip_info.color >= limit_color and 0 or 1
				if grade >= max_grade then
					self.evolve_max_grade_num = (self.evolve_max_grade_num or 0) + 1
				end
				table.insert(list_data,data)
			end
		end
	end
	self.evolve_max_equip_num = #list_data
	local need_flush = old_max_grade_num ~= self.evolve_max_grade_num or old_max_equip_num ~= self.evolve_max_equip_num
	SortTools.SortAsc(list_data, "can_evolve", "is_max", "part")
	return list_data , need_flush
end

function this:GetEvolveMaxGrade()
	return self.gb_equip_upgrade_cfg[0] and self.gb_equip_upgrade_cfg[0][0] and #self.gb_equip_upgrade_cfg[0][0] or 10
end

function this:GetGBEquipAttrListCfg(attr_index)
	return self.gb_equip_attr_list_cfg[attr_index]
end

function this:GetEvolveEquipBagList(slot, part)
	local bag_data = {}

    local slot_bag_list = self:GetSortEvolveEquipBag()
    if not IsEmptyTable(slot_bag_list) then
		for k,v in pairs(slot_bag_list) do
			table.insert(bag_data,v)
		end
	end
	SortTools.SortAsc(bag_data, "color", "slot", "part")
	return bag_data
end

function this:EvolveEquipBagIsHasItem(slot, part)
	local sort_data_list = FairyLandEquipmentWGData.Instance:GetSortEvolveEquipBag()
	if not IsEmptyTable(sort_data_list) then
		return true
	end
	return false
end

--获取进化装备颜色限制
function this:GetEvolveUpColorLimit()
	local main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
	local other_cfg = main_cfg.other[1]
	return other_cfg.equip_up_grade_color_limit or 1
end

--选择背包的数据参数 bag_list 选中的背包格子 add_per 选中格子的总和概率
function this:SetEvolveSelectBagParam(bag_list,add_per, count)
	self.evolve_bag_list = bag_list
	self.evolve_add_per = add_per
	self.evolve_select_count = count
end

function this:GetEvolveSelectBagParam()
	return self.evolve_bag_list , self.evolve_add_per or 0 , self.evolve_select_count or 0
end

function this:ClearEvolveSelectBagParam()
	self.evolve_bag_list = nil
	self.evolve_add_per = 0
	self.evolve_select_count = 0
end

function this:EvolvePopEquipIsSelect(index)
	if not IsEmptyTable(self.evolve_bag_list) then
		for k,v in pairs(self.evolve_bag_list) do
			if v == index then
				return true
			end
		end
	end
	return false
end

function this:GetPartRandAttrIsAct(slot, part,attr_index)
	local rand_attr = FairyLandEquipmentWGData.Instance:GetPartRandAttr(slot, part)
	for k,v in pairs(rand_attr) do
		if v == attr_index then
			return true
		end
	end
	return false
end

function this:GetPartRandAttrAndScore(slot, part)
	local wear_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	local limit_color = self:GetEvolveUpColorLimit()
	local rand_attr_list = FairyLandEquipmentWGData.Instance:GetPartRandAttr(slot, part)
	local equip_score = 0
	local attr_list = {}
	if not IsEmptyTable(rand_attr_list) and wear_info and wear_info.color >= limit_color then
		for k,v in pairs(rand_attr_list) do
			local attr_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipAttrListCfg(v)
			if attr_cfg then
				local temp = {}
				temp.is_star = attr_cfg.is_star
				if attr_cfg.gb_attr_add_per and attr_cfg.gb_attr_add_per > 0 then
					local attr_name = Language.FairyLandEquipment.EvolveGodBodyAddPer
					temp.label = attr_name .. "   " .. ToColorStr((attr_cfg.gb_attr_add_per / 100) .. "%", TIPS_COLOR.ATTR_VALUE)
					local gb_attr_add_per_score = FairyLandEquipmentWGData.Instance:GetOtherCfgByKey("gb_attr_add_per_score") or 0
					equip_score = equip_score + gb_attr_add_per_score
				else
					local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_cfg.attr_type, true)
					local type_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cfg.attr_type)
					local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(type_str)
					local attr_value = attr_cfg.attr_value
					if is_per then
						attr_value = (attr_cfg.attr_value / 100) .. "%"
					end
					temp.label = attr_name .. "   " .. ToColorStr(attr_value, TIPS_COLOR.ATTR_VALUE)
					
					equip_score = equip_score + TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_cfg.attr_type, attr_cfg.attr_value)
				end

				table.insert(attr_list, temp)
			end
		end
		SortTools.SortDesc(attr_list, "is_star")
	end
	return attr_list, equip_score
end

--获取全身装备配置
function this:GetEvolveLevelTotalCfg(slot, is_next)
	local curr_cfg = nil
	local next_cfg = nil
	local cur_slot_cfg_list = self.gb_equip_all_star_cfg[slot]
	local cur_total_act_level = self:GetEvolveLevelActiveTotalLv(slot)
	if not IsEmptyTable(cur_slot_cfg_list) then
		for k, v in pairs(cur_slot_cfg_list) do
			if v.level <= cur_total_act_level then
				curr_cfg = v
				next_cfg = cur_slot_cfg_list[k + 1]
			end
		end

		if is_next then
			if nil == curr_cfg then
				return cur_slot_cfg_list[1]
			else
				return next_cfg
			end
		end
	end
	return curr_cfg
end

--获取全身装备配置
function this:GetEvolveLevelTotalBaseCfg(slot)
	return self.gb_equip_all_star_cfg[slot] and self.gb_equip_all_star_cfg[slot][1]
end

function this:GetEvolveEquipAddGradePerCfg(slot, color, grade)
	local per_cfg = self.gb_equip_add_grade_per_cfg[slot] and self.gb_equip_add_grade_per_cfg[slot][color] and self.gb_equip_add_grade_per_cfg[slot][color][grade]
	
	if not self.evolve_add_per_data_list[slot] then
		self.evolve_add_per_data_list[slot] = {}
	end
	if not self.evolve_add_per_data_list[slot][color] then
		self.evolve_add_per_data_list[slot][color] = {}
	end
	if not self.evolve_add_per_data_list[slot][color][grade] then
		self.evolve_add_per_data_list[slot][color][grade] = {}
		if per_cfg then
			local add_per_list = Split(per_cfg.add_per, "|")
			local add_specail_per_list = Split(per_cfg.add_specail_per, "|")
			local add_per_list_2 = Split(per_cfg.add_per_2, "|")
			local add_specail_per_list_2 = Split(per_cfg.add_specail_per_2, "|")
			self.evolve_add_per_data_list[slot][color][grade].add_per_list = add_per_list
			self.evolve_add_per_data_list[slot][color][grade].add_specail_per_list = add_specail_per_list
			self.evolve_add_per_data_list[slot][color][grade].add_per_list_2 = add_per_list_2
			self.evolve_add_per_data_list[slot][color][grade].add_specail_per_list_2 = add_specail_per_list_2
		end
	end
	
	return self.evolve_add_per_data_list[slot][color][grade]
end

--根据 转数 获取装备总星级(当前所在的激活总星级)
function this:GetEvolveLevelActiveTotalLv(slot)
	return FairyLandEquipmentWGData.Instance:GetTotalStarLevel(slot) or 0
end

--根据 转数 获取当前穿戴装备星级总和
function this:GetEvolveLevelAllLevel(slot)
	local all_level = 0
	local all_rand_attr_list = FairyLandEquipmentWGData.Instance:GetSlotRandAttrList(slot)
	for part, list in pairs(all_rand_attr_list) do
		for k,v in pairs(list) do
			local attr_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipAttrListCfg(v)
			if attr_cfg and attr_cfg.is_star == 1 then
				all_level = all_level + 1
			end
		end
	end
	return all_level
end

function this:GetBrowseEquipRandAttrAndScore(equip_data)
	local rand_attr_list = equip_data.rand_attr
	local equip_score = 0
	local attr_list = {}
	if not IsEmptyTable(rand_attr_list) then
		for k,v in pairs(rand_attr_list) do
			local attr_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipAttrListCfg(v)
			if attr_cfg then
				local temp = {}
				temp.is_star = attr_cfg.is_star
				if attr_cfg.gb_attr_add_per and attr_cfg.gb_attr_add_per > 0 then
					local attr_name = Language.FairyLandEquipment.EvolveGodBodyAddPer
					temp.label = attr_name .. "+" .. (attr_cfg.gb_attr_add_per / 100) .. "%"
					local gb_attr_add_per_score = FairyLandEquipmentWGData.Instance:GetOtherCfgByKey("gb_attr_add_per_score") or 0
					equip_score = equip_score + gb_attr_add_per_score
				else
					local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_cfg.attr_type, true)
					local type_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cfg.attr_type)
					local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(type_str)
					local attr_value = attr_cfg.attr_value
					if is_per then
						attr_value = (attr_cfg.attr_value / 100) .. "%"
					end
					temp.label = attr_name .. "+" .. attr_value
					equip_score = equip_score + TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_cfg.attr_type, attr_cfg.attr_value)
				end
				if temp.is_star == 1 then
					temp.icon = "tips_xingxing1"
				end
				table.insert(attr_list,temp)
			end
		end
		SortTools.SortDesc(attr_list, "is_star")
	end
	return attr_list,equip_score
end

function this:GetEvolveHasGBAttrAddPer(slot)
	local slot_rand_list = FairyLandEquipmentWGData.Instance:GetSlotRandAttrList(slot)
	local add_per = 0
	for part, list in pairs(slot_rand_list) do
		for k,v in pairs(list) do
			local attr_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipAttrListCfg(v)
			if attr_cfg and attr_cfg.gb_attr_add_per and attr_cfg.gb_attr_add_per > 0 then
				add_per = add_per + attr_cfg.gb_attr_add_per
			end
		end
	end
	return add_per
end