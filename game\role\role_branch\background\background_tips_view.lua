------------------------------------------------------------
--背景属性View
------------------------------------------------------------
local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 11
BackgroundTipsView = BackgroundTipsView or BaseClass(SafeBaseView)

function BackgroundTipsView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "BackgroundTipsView"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function BackgroundTipsView:LoadCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}

	for i=1,ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end
	
	self:Flush()
end

function BackgroundTipsView:ReleaseCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
end

function BackgroundTipsView:OnFlush()
	self.node_list.attr_title_name.text.text = Language.Role.BackgroundAttrName

	local data_list = BackgroundWGData.Instance:GetHaveBackgroundAttr()
	local length = #data_list

	local index = 1
	if not IsEmptyTable(data_list) then
		for i = 1, ATTR_COUNT do
			local attr_data = data_list[i]
			if attr_data and self.attr_list[i] then
				---设置属性
				self.attr_name_list[i].text.text = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_data.attr_str, true, true)
				---设置属性
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_data.attr_str)
				local per_desc = is_per and attr_data.attr_value / 100 .. "%" or attr_data.attr_value 
				---基础属性
				if attr_data.attr_value then
					self.attr_value_list[i].text.text = "+" .. per_desc
				end
		   end
			if i <= length then
			   index = index + 1
			end
		end
	end

	if index < MAX_DISPLAY_NUM then
        self.node_list.attr_scroll.scroll_rect.enabled = false
    else
        self.node_list.attr_scroll.scroll_rect.enabled = true
    end

	if not IsEmptyTable(data_list) then
		for i = 1, ATTR_COUNT do
			self.attr_list[i]:SetActive(i <= length)
		end
	end
end
