--灵核抽奖
-------------------------------------------------------------------------
TianShenLingHeWGData.DRAW_TYPE = {
    LUCK_DRAW = 0,  --抽奖
    BUY = 1,        --商店购买
}

function TianShenLingHeWGData:InItLingHeDrawData()
    local draw_cfg = ConfigManager.Instance:GetAutoConfig("linghechoujiang_auto") --灵核抽奖
    self.runes_shop_mode_cfg = draw_cfg.runes_shop_mode --寻宝次数
    self.item_random_desc_cfg = draw_cfg.show_id  --大奖展示
  	self.linghe_probability_cfg = draw_cfg.item_random_desc --寻宝概率
  	self.runes_shop_item_cfg = draw_cfg.runes_shop_item -- 寻宝物品
    self.item_random_desc_cfg = draw_cfg.show_id  --大奖展示
    self.activity_sale_map_cfg = ListToMap(draw_cfg.activity_sale, "type")   --商店特惠物品
    
    --背包道具红点
    self:RegisterLingHeDrawRemindInBag(RemindName.TianShenLingHeDraw)
end

--主界面红点
function TianShenLingHeWGData:GetLingHeDrawRed()
    --背包有道具
    if self:GetEnoughItem() then
        return 1
    end

    return 0
end

--有道具就有红点
function TianShenLingHeWGData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

--抽奖次数
function TianShenLingHeWGData:GetDrawConsumeCfg()
    return self.runes_shop_mode_cfg
end

--大奖展示
function TianShenLingHeWGData:GetBigShowCfg()
    return self.item_random_desc_cfg
end

--抽奖道具list
function TianShenLingHeWGData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = self:GetDrawConsumeCfg()
        for i, v in pairs(cfg) do
            table.insert(self.item_data_change_list, v.stuff_id)
        end
    end
    return self.item_data_change_list
end

--商店物品
function TianShenLingHeWGData:GetDrawShopListCfg()
    local show_list = {}
    local can_add, buy_times, weight = false, 0, 0
    for k,v in ipairs(self.activity_sale_map_cfg) do
        buy_times = self:GetLingHeShopItemBuyTimes(v.type)
        can_add = false
        if v.subactivity_id > 0 then
            local before_buy_times = self:GetLingHeShopItemBuyTimes(v.subactivity_id)
            local before_cfg = self.activity_sale_map_cfg[v.subactivity_id]
            if before_cfg and before_buy_times >= before_cfg.limit_buy_times then
                can_add = true
            end
        else
            can_add = true
        end

        if can_add then
            weight = buy_times >= v.limit_buy_times and 100 or 0
            local data = {
                type = v.type,
                item_list = v.item[0],
                sale_name = v.sale_name,
                price_type = v.price_type,
                rmb_type = v.rmb_type,
                rmb_price = v.rmb_price,
                special_sale_price = v.special_sale_price,
                limit_buy_times = v.limit_buy_times,
                buy_num = buy_times,
                sort = v.type + weight
            }
            table.insert(show_list, data)
        end
    end

    SortTools.SortAsc(show_list, "sort")

    return show_list
end

-- enable_anim为false表示跳过动画
function TianShenLingHeWGData:SetAnimToggleData(enable_anim)
	self.anim_toggle_enable = enable_anim
end

-- 返回false表示跳过动画
function TianShenLingHeWGData:GetAnimToggleData(linghe_type)
	return self.anim_toggle_enable
end

--灵核抽奖 恭喜获得奖励列表
function TianShenLingHeWGData:SetLingHeDrawResultInfo(protocol)
    self.linghe_result = protocol.reward_list
end

function TianShenLingHeWGData:GetLingHeDrawResultInfo()
    return self.linghe_result
end

--灵核抽奖 商店售卖记录
function TianShenLingHeWGData:SetLingHeDrawShopInfo(protocol)
    self.linghe_shop_sale_records = protocol.linghe_shop_sale_records
end

function TianShenLingHeWGData:GetLingHeDrawShopInfo()
    return self.linghe_shop_sale_records
end

function TianShenLingHeWGData:GetLingHeShopItemBuyTimes(type)
    return self.linghe_shop_sale_records[type] or 0
end

--灵核抽奖 商店开启时间
function TianShenLingHeWGData:SetLingHeShopOpenTime(protocol)
    self.open_time = protocol.open_time
    self.end_time = protocol.end_time
    self.state = protocol.state
end

function TianShenLingHeWGData:GetLingHeShopOpenTime()
    return self.open_time, self.end_time, self.state
end

--商店开启状态
function TianShenLingHeWGData:GetLingHeShopOpenState()
    return self.state or 0
end

--背包道具改变红点
function TianShenLingHeWGData:RegisterLingHeDrawRemindInBag(remind_name)
    local check_list = TianShenLingHeWGData.Instance:GetItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end


--获取抽奖的选项
function TianShenLingHeWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

function TianShenLingHeWGData:GetProbabilityInfo()
	return self.linghe_probability_cfg
end

function TianShenLingHeWGData:ShowLingHeShopItem()
	local list_data = {}
	for k,v in pairs(self.runes_shop_item_cfg) do
		if v.is_weight_orange == 1 then
			table.insert(list_data, v)
		end
	end

	return list_data
end