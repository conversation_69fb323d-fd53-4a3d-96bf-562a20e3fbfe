--界面动画时间常量
-- 加备注方便策划修改

UITween_CONSTS = {

--================================== 【系统界面】 ================================
    --副本
    FuBen = {
        DelayAlphaShowTimeQuick = 0.3,   --延迟开始淡入时间(快速)
        DelayAlphaShowTime = 0.6, 		--延迟开始淡入时间
        FromAlpha = 0, 			--淡入初始alpha值
        ToAlpha = 1, 				--淡入结束alpha值
        AlphaTweenTime = 0.6, 				--淡入时长
        MoveTweenTime = 0.6,
        MoveTweenType = DG.Tweening.Ease.OutCubic,
        AlphaTweenType = DG.Tweening.Ease.OutCubic,    -- 主渐显变化曲线类型
        StartPosition = Vector2(500, -21),
        EndPosition = Vector2(-211, -24),                --移动结束位置
        FromScale = Vector3(0, 0, 0),
        ToScale  = Vector3(1, 1, 1),
        ScaleTweenTime = 0.8,
        -- 溶解
        DissolveFrameCount = 30, 		--一共溶解多少帧（这个值越大，时间越长，没有具体的时间参考。）
        DissolveStartPercent = 0.6,  	--开始溶解的百分比（按照目前的特效，从0.7开始最合适，因为前面0.3特效没有任何变化，导致看起来有延迟）

        TipsTweenTime = 0.6,            --说明窗移动时间

        -- 组队装备本翻牌
        TeamEquipCellTween = {
                            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
                            FromAlpha = 0.3,                        --渐变初始值
                            StartRotatetion = Vector3(0, 45, 0),    --初始旋转
                            AlphaTweenTime = 0.4,                   --渐变执行时间
                            RotateTweenTime = 0.4,                  --旋转执行时间
                            },

        -- 八卦本飞入
        BuGuaCellTween = {
                        NextDoDelay = 0.2,                          --下一目标执行 间隔时间
                        TextAlphaDoDelayTime = 0.2,                 --战力文字显示延迟时间
                        TextAlphaTweenTime = 0.4,                   --战力文字渐显时间

                        FromAlpha = 0.3,                            --渐变初始值
                        StartPosition = Vector2(-1000, 0),          --移动初始位置
                        EndPosition = Vector2(0, 0),                --移动结束位置
                        AlphaTweenTime = 0.4,                       --渐变执行时间
                        MoveTweenTime = 0.4,                        --移动执行时间
                        AlphaTweenType = DG.Tweening.Ease.Linear,   --渐变曲线
                        MoveTweenType = DG.Tweening.Ease.OutCubic,  --移动曲线
                        },
    },

    -- 角色系统
    RoleSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 0.8,         --右侧面板移入时间
        AlphaDelay = 0.8,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6, 		--右侧面板上的信息淡入时间
        FromAlpha = 0.3,		--右侧面板初始Alpha
        ToAlpha = 1, 			--右侧面板目标Alpha
        TaiZiStartScale = 0.4,  --台子开始尺寸
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 衣橱系统
    WardrobeSys = {
        leftTweenInfo = {FromAlpha = 0, ToAlpha = 1, StartPosition = Vector2(-133, 0),
                            EndPosition = Vector2(133, 0),  AlphaTweenTime = 1.2, MoveTweenTime = 0.6,
                            AlphaTweenType = DG.Tweening.Ease.Linear,
                        },

       rightTweenInfo = {FromAlpha = 0, ToAlpha = 1, StartPosition = Vector2(320, 0),
                           EndPosition = Vector2(0, 0),  AlphaTweenTime = 1.2, MoveTweenTime = 0.6,
                           AlphaTweenType = DG.Tweening.Ease.Linear,
       				   },

        middleMoveDelay = 0.2,
        middleMoveTime = 1,
        middleTweenType = DG.Tweening.Ease.Linear
    },

    -- 头像选择界面
    ChangeHead = {
        BgMoveTime = 0.4,         --背景移动时间
        ListMoveTime = 0.4,     --列表移动时间
        AlphaTime = 0.2, 		--右侧面板上的信息淡入时间
        DelayAlphaShowTime = 0.4,   
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 经验加成 格子
    ExpAditionCellTween = {
        DelayDoTime = 0.2,                      --列表延时翻牌时间
        NextDoDelay = 0.2,                      --下一目标执行 间隔时间
        FromAlpha = 0.3,                        --渐变初始值
        StartRotatetion = Vector3(60, 0, 0),    --初始旋转
        AlphaTweenTime = 0.4,                   --渐变执行时间
        RotateTweenTime = 0.4,                  --旋转执行时间
    },

     -- 目标装备系统
    EquipTargetSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 0.8,         --右侧面板移入时间
        AlphaDelay = 0.8,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        TaiZiStartScale = 0.4,  --台子开始尺寸
        ToTargetProgress = 0.5, --收集进度达到目标值时间
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellTween = {
                            NextDoDelay = 0.1,      --下一目标执行 间隔时间
                            AlphaTweenTime = 0.4,   --渐显时间
                            MoveTweenTime = 0.4,    --移动时间
                        }
    },

    -- 称号系统
    TitleSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 0.5,         --右侧面板移入时间
        AlphaDelay = 0.5,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6, 		--右侧面板上的信息淡入时间
        FromAlpha = 0.3,		--右侧面板初始Alpha
        ToAlpha = 1, 			--右侧面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    --元神寻宝
	XunBaoYuanShen = {
		-- OnceNameBgSpreadTime = 1,	--单抽奖励界面的名字伸展时间
		Interval = 0.3,				--每个奖励飘出的间隔
		-- BigInterval = 1.9,			--大奖奖励飘出的间隔
		RewardMoveTime = 0.5,		--每个奖励移动的时间
		ScaleTime = 1.5,			--比列放大缩小时间
		Scale = 1.2,				--比列
		EffectTime = 2.6,			--特效时间
		BigEffectTime = 4.9,		--大奖特效时间
		DoorEffBerforeTime = 0.7,	--门特效的时间 = eff - before(上面两个特效时间减去这个时间)
		BigShakeStartTime = 3.6,	--龙斗抖动开始的时间
		-- SpineTime = 2.4,			--龙骨时间
		BigSpineTime = 5.63,		--大奖龙骨时间
		DanMuInterval = 1.5,		--弹幕间隔  = other.danmu_time / danmuinterval
		DanMuIntervalMax = 50,		--弹幕间隔上限 = danmuinterval + DanMuIntervalMax / 100
		-- NDrawNameSpreadTime = 1,	--n抽的名字展开时间
		-- NDrawNameDelay = 0.5,			--n抽的移动开始到名字展开的延时
		-- OnceAnimStartVec3 = Vector3(0,0,0),		--单抽的动画初始大小
		-- OnceModelScaleTime = 0.5,	--单抽模型比例改变时间
		-- OnceScaleToName = 0.5,		--单抽模型比例开始到名字开始的时间
		-- NAnimStartVec3 = Vector3(0,0,0),		--单抽模型比例开始到名字开始的时间
		Shake = {
			shake_frame_num = 8,	--抖动帧数
			shake_pixel = 30,		--偏移像素
			long_shake_pixel = 8,	--长时间抖动的偏移像素
		},
		-- NorScaleTime = 0.1,			--回弹时间
		-- BigScale = 1.3,				--变大比例
		-- BigScaleTime = 0.3,			--变大时间
		-- SpineSwitch = 0.1,			--龙骨动画切换关闭普通动画的延时
		-- NScale = 2.2,				--n抽放大比例
		-- NScaleTime = 0.3,			--n抽比例时间
		-- BigStopTime = 1.2,			--大奖暂停时间
		-- NModelYMax = 23.4,			--n抽动画y上限
		-- NModelYMin = 13.4,			--n抽动画y下限
		-- NModelTime = 1,				--n抽单向动画移动时间
		-- NModelDelay = 0.15,			--n抽每组动画间隔
		-- NModelRot = 40,				--n抽大奖初始角度
		ShakeDelay = 2.4,			--抖屏延时
		-- TextTime = 0.2,				--元神文字的间隔时间
		-- TextScaleTime = 0.05,		--元神文字的比例动画时间
		-- TextScale = Vector3(4, 4, 4),			--元神文字的放大比例
		-- SoulSeqTime = 4,			--元神和下一个动画的间隔
	},

    -- 每日充值系统
    EveryDayRechargeSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 0.5,         --右侧面板移入时间
        AlphaDelay = 0.5,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.3,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alphame
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector3(1018, 0, 0),    --初始位置
            AlphaTweenTime = 0.4,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        }    
    },
    -- 每日充值 格子
    EveryDayRechargeTween = {
        DelayDoTime = 0.2,                      --列表延时翻牌时间
        NextDoDelay = 0.2,                      --下一目标执行 间隔时间
        FromAlpha = 0.3,                        --渐变初始值
        StartRotatetion = Vector3(60, 0, 0),    --初始旋转
        AlphaTweenTime = 0.4,                   --渐变执行时间
        RotateTweenTime = 0.4,                  --旋转执行时间
    },

      -- 排行榜系统
    RankSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 0.5,         --右侧面板移入时间
        AlphaDelay = 0.5,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

     -- 排行榜 格子
    RankCellTween = {
        DelayDoTime = 0.2,                      --列表延时翻牌时间
        NextDoDelay = 0.2,                      --下一目标执行 间隔时间
        FromAlpha = 0.3,                        --渐变初始值
        StartRotatetion = Vector3(60, 0, 0),    --初始旋转
        AlphaTweenTime = 0.4,                   --渐变执行时间
        RotateTweenTime = 0.4,                  --旋转执行时间
    },

	-- 成就系统
	AchievementSys = {
		AlphaTime = 0.1,                         --淡入时间
		AlphaShowType = DG.Tweening.Ease.Linear, -- 主渐显变化曲线类型

		-- 成就列表
		ListCellTween = {
			NextDoDelay = 0.1,                                  --下一目标执行 间隔时间
			AlphaTweenTime = 0.4,                               --渐显时间
			ScaleTweenTime = 0.4,                               --缩放时间
			FromScale = Vector3(0.1, 0.1, 0.1),                 --初始大小
			ToScale = Vector3(1, 1, 1),                         --结束大小
			FromAlpha = 0.3,                                    --初始Alpha
			ToAlpha = 1,                                        --目标Alpha
		}
	},

     -- 祈福
    QiFuSys = {
        MoveTime = 0.3,         --掉落时间
        AlphaDelay = 0.6,       --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,        --淡入时间
        NextDoDelay = 0.3,      --下一目标执行 间隔时间
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        VerTween = {
                            NextDoDelay = 0.2,      --下一目标执行 间隔时间
                            AlphaTweenTime = 0.4,   --渐显时间
                            MoveTweenTime = 0.4,    --移动时间
        },
        WindChineBackTime = 0.3,
    },

    -- Boss系统
    BossSys = {
        LeftMoveTime = 1,       --左侧列表移入时间
        RightMoveTime = 0.8,    --右侧面板移入时间
        AlphaDelay = 0.8,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.5, 		--右侧面板上的信息淡入时间
        FromAlpha = 0.3,		--右侧面板初始Alpha
        ToAlpha = 1, 			--右侧面板目标Alpha

        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },
    -- 世界服系统，跨服boss
    WorldServerSys = {
        LeftMoveTime = 1,       --左侧列表移入时间
        RightMoveTime = 0.8,    --右侧面板移入时间
        AlphaDelay = 0.8,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.5, 		--右侧面板上的信息淡入时间
        FromAlpha = 0.3,		--右侧面板初始Alpha
        ToAlpha = 1, 			--右侧面板目标Alpha

        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 坐骑 灵宠系统
    MountPetSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 0.8,         --右侧面板移入时间
        AlphaDelay = 0.8,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        TaiZiStartScale = 0.4,  --台子开始尺寸
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 坐骑 灵宠幻化系统
    MountPetHuanHuaSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 1,           --右侧面板移入时间
        AlphaDelay = 1,         --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 1,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        TaiZiStartScale = 0.4,  --台子开始尺寸
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },
    -- 骑宠装备系统
    MountPetEquipSys = {
        Common = {
            MoveDelay = 0,          --右侧面板延迟开始移入时间
            MoveTime = 1,           --右侧面板移入时间
            AlphaDelay = 1,         --右侧面板延迟开始Aplha淡入的时间
            AlphaTime = 1,        --右侧面板上的信息淡入时间
            FromAlpha = 0.3,        --右侧面板初始Alpha
            ToAlpha = 1,            --右侧面板目标Alpha
            TaiZiStartScale = 0.4,  --台子开始尺寸
            AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        },
        --套装
        Suit = {
            MoveDelay = 0,          --右侧面板延迟开始移入时间
            MoveTime = 1,           --右侧面板移入时间
            AlphaDelay = 1,         --右侧面板延迟开始Aplha淡入的时间
            AlphaTime = 1,        --右侧面板上的信息淡入时间
            FromAlpha = 0.3,        --右侧面板初始Alpha
            ToAlpha = 1,            --右侧面板目标Alpha
            TaiZiStartScale = 0.4,  --台子开始尺寸
            AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        },
    },

    -- 坐骑 灵宠幻化系统列表格子
    MountPetHuanHuaCellTween = {
        DelayDoTime = 0.2,                      --列表延时翻牌时间
        NextDoDelay = 0.2,                      --下一目标执行 间隔时间
        FromAlpha = 0.3,                        --渐变初始值
        StartRotatetion = Vector3(60, 0, 0),    --初始旋转
        AlphaTweenTime = 0.4,                   --渐变执行时间
        RotateTweenTime = 0.4,                  --旋转执行时间
    },

    -- 化鲲系统
    HuaKunSys = {
        MoveDelay = 0,          --右侧面板延迟开始移入时间
        MoveTime = 1,           --右侧面板移入时间
        AlphaDelay = 1,         --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 1,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        TaiZiStartScale = 0.4,  --台子开始尺寸
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 化鲲系统列表格子
    HuaKunCellTween = {
        DelayDoTime = 0.2,                      --列表延时翻牌时间
        NextDoDelay = 0.2,                      --下一目标执行 间隔时间
        FromAlpha = 0.3,                        --渐变初始值
        StartRotatetion = Vector3(60, 0, 0),    --初始旋转
        AlphaTweenTime = 0.4,                   --渐变执行时间
        RotateTweenTime = 0.4,                  --旋转执行时间
    },

    -- 主界面等级礼包出现动画
    MainViewLevelGiftTween = {
        FromAlpha = 0.1,                            --渐变初始值
        StartPosition = Vector2(114, -282),         --移动初始位置
        EndPosition = Vector2(-114, -282),          --移动结束位置
        AlphaTweenTime = 0.6,                       --渐变执行时间
        MoveTweenTime = 0.6,                        --移动执行时间
        AlphaTweenType = DG.Tweening.Ease.Linear,   --渐变曲线
        MoveTweenType = DG.Tweening.Ease.OutCubic,  --移动曲线
    },

    -- 四象系统
    SiXiangSys = {
        MoveTime = 0.8,         --右侧面板移入时间
        AlphaTime = 0.6, 		--右侧面板上的信息淡入时间
        FromAlpha = 0.3,		--右侧面板初始Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        SlotTween = {
            NextDoDelay = 0.2,      --下一目标执行 间隔时间
            AlphaTweenTime = 0.6,   --渐显时间
            MoveTweenTime = 0.6,    --移动时间
            StartPosition = Vector2(-160, 0),
            EndPosition = Vector2(-19, 0),
        },
    },


--================================== 【活动界面】 =================================

    -- 招财猫
    FortuneCatSys = {
        UpImgStartPos = Vector2(-5, 478),                       --宣传文字开始位置
        UpImgEndPos = Vector2(-5, 278),                         --宣传文字结束位置
        UpTweenTime = 0.8,                                      --宣传文字动画时间
        UpTweenShowType = DG.Tweening.Ease.OutBounce,           --缓动类型

        NumNextDoDelay = 0.2,
        TimeInterval = 0.5, 			                        --转盘图标和数字显示间隔
        AlphaShowType = DG.Tweening.Ease.Linear,                -- 主渐显变化曲线类型
        DelayAlphaShowTime = 0.4, 		                        --延迟开始淡入时间
        RecordItemTween = {
            NextDoDelay = 0.2,
            TextAlphaDoDelayTime = 0.2,
            TextAlphaTweenTime = 0.4,

            FromAlpha = 0.3,
            StartPosition = Vector2(0, 1000),
            EndPosition = Vector2(0, 0),
            AlphaTweenTime = 0.4,
            MoveTweenTime = 0.4,
            AlphaTweenType = DG.Tweening.Ease.Linear,
            MoveTweenType = DG.Tweening.Ease.OutCubic,
        },
    },
     -- 超值必买
    MustBuySys = {
        DelayAlphaShowTime = 0.4,
        DelayMoveTime = 0.4,
        MoveTweenType = DG.Tweening.Ease.Linear,
        NumNextDoDelay = 0.2,
        TimeInterval = 0.5,
        Item_Tween = {
            NextDoDelay = 0.2,
            TextAlphaDoDelayTime = 0.2,
            TextAlphaTweenTime = 0.4,
            FromAlpha = 0.3,
            StartPosition = Vector2(830, 0),
            EndPosition = Vector2(0, 0),
            AlphaTweenTime = 0.4,
            MoveTweenTime = 0.4,
            AlphaTweenType = DG.Tweening.Ease.Linear,
            MoveTweenType = DG.Tweening.Ease.OutCubic,

            AlphaTime = 0.2, --左边cell底部文字显示时间
        },
    },
    -- 神兵觉醒
    WeaponAwakenSys = {
        MoveTime = 0.8,         --时间
        MoveTweenType = DG.Tweening.Ease.OutCubic,
        BtnAlphaTime = 1.2, 	    --按钮的淡入时间
        BtnFromAlpha = 0,		--按钮初始Alpha
        BtnAlphaShowType = DG.Tweening.Ease.InCubic,    -- 按钮变化曲线类型

        AlphaTime = 0.6, 		--右侧面板上的信息淡入时间
        FromAlpha = 0.3,		--右侧面板初始Alpha
        ToAlpha = 1, 			--右侧面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        RightStartPos = Vector2(-445, 0),
        RightEndPos = Vector2(290, 0),
        DownStartPos = Vector2(-217, 28),
        DownEndPos = Vector2(369, 28),

        TitleMove = {
            FromScale = Vector3(1.2, 1.2, 1.2),    --标题开始大小
            ToScale = Vector3(1, 1, 1),           --标题结束大小
            ScaleTweenTime = 0.6,                  --标题动画时间
            ScaleTweenType = DG.Tweening.Ease.InOutBack,
        }

    },
    -- 周一狂欢
    ZhouYiCarnival = {
        MoveTime = 0.6,         --移动时间
        AlphaTime = 0.6, 		--渐变时间
    },    

    -- 仙玉转盘
    XianyuTrunTable = {
        MoveTime = 0.6,         --移动时间
        AlphaTime = 0.6,        --渐变时间
    },

     -- 运营活动捕鱼
     ActivityOperaFish = {
        FromScale = Vector3(0.2, 0.2, 0.2),
        ToScale = Vector3(1, 1, 1),
        StartPosition = Vector2(0, 0),
        EndPosition = Vector2(286, 154),
        ScaleTweenTime = 2,
        MoveTweenTime = 2,
        ScaleTweenType = DG.Tweening.Ease.OutCubic,
        MoveTweenType = DG.Tweening.Ease.OutCubic,
    },

    --限时抢购
    LimitTimeBuy = {
        DelayAlphaShowTimeQuick = 0.3,   --延迟开始淡入时间(快速)
        FromAlpha = 0.2,
        AlphaTime = 0.6,
        AlphaTweenType = DG.Tweening.Ease.InCubic,
        TitleStartScale = Vector3(1.2, 1.2, 1.2),    --开始大小
        TitleScaleTweenTime = 0.6,                   --动画时间
        TitleScaleTweenType = DG.Tweening.Ease.InOutBack,
        TitleScale = {
            FromScale = Vector3(1.2, 1.2, 1.2),    --左标题开始大小
            ToScale =  Vector3(1, 1, 1),           --左标题结束大小
            ScaleTweenTime = 1,                  --左标题动画时间
            ScaleTweenType = DG.Tweening.Ease.InOutBack,
        },
        BgMove = {
            BgStartPos = Vector2(-992, 0),
            BgEndPos = Vector2(0, 0),
            MoveTime = 0.5,
            MoveTweenType = DG.Tweening.Ease.InOutBack,
        },
        ListCellTween = {
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartRotatetion = Vector3(0, 45, 0),    --初始旋转
            AlphaTweenTime = 0.6,                   --渐变执行时间
            RotateTweenTime = 0.3,                  --旋转执行时间


            FromScale = Vector3(1, 1, 1),    --标签开始大小
            ToScale =  Vector3(0.7, 0.7, 0.7),           --标签结束大小
            ScaleTweenTime = 0.6,                  --标签动画时间
            ScaleTweenType = DG.Tweening.Ease.InOutBack,



            },
    },
    -- 修仙试炼
    XiuxianSys = {
        DelayAlphaShowTime = 0.4, 		--延迟开始淡入时间
        DelayMoveTime = 0.4,            --延迟播放左侧和右侧往中间移动的时间
        MoveTweenType = DG.Tweening.Ease.Linear,
        NumNextDoDelay = 0.2,           --左侧章节动画间隔
        TimeInterval = 0.5,             --显隐显示技能的时间
        Item_Tween = {
            NextDoDelay = 0.2,
            TextAlphaDoDelayTime = 0.2,
            TextAlphaTweenTime = 0.4,
            FromAlpha = 0.3,
            StartPosition = Vector2(500, 0),
            EndPosition = Vector2(0, 0),
            AlphaTweenTime = 0.4,
            MoveTweenTime = 0.4,
            AlphaTweenType = DG.Tweening.Ease.Linear,
            MoveTweenType = DG.Tweening.Ease.OutCubic,

            AlphaTime = 0.2, --左边cell底部文字显示时间
        },
    },

    -- 首充
    FirstCharge = {
        MoveTime = 0.8,         --右侧面板移入时间
        AlphaDelay = 0.8,       --右侧面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,        --右侧面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 充值
    Recharge = {
        DelayTime = 0.1,
        Next_time = 1,
        ListCellTween = {
            NextDoDelay = 0.1,
            FromAlpha = 0,                        --渐变初始值
            StartRotatetion = Vector3(0, 60, 0),    --初始旋转
            AlphaTweenTime = 0.5,                   --渐变执行时间
            RotateTweenTime = 0.3,                  --旋转执行时间
        }
    },
     -- boss领取仙力
     BossGetEnergy = {
        BgAlphaTime = 1,       --背景面板上的信息淡入时间
        BgFromAlpha = 0.3,        --背景面板初始Alpha
        BgToAlpha = 1,            --背景面板目标Alpha
        BgAlphaShowType = DG.Tweening.Ease.Linear,    -- 背景主渐显变化曲线类型

        UpImgStartPos = Vector2(121, 429),                       --宣传文字开始位置
        UpImgEndPos = Vector2(121, 226),                         --宣传文字结束位置
        UpTweenTime = 0.8,                                      --宣传文字动画时间
        UpTweenShowType = DG.Tweening.Ease.OutBounce,           --缓动类型

        MoveTime = 0.3,
        OffsetTime = 0.3,       --间隔
        AlphaTime = 1,       --面板上的信息淡入时间
        FromAlpha = 0.3,        --右侧面板初始Alpha
        ToAlpha = 1,            --右侧面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

	-- 天神夺宝
    GodGetReward = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.8,                 --面板移入时间
        AlphaDelay = 0.8,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        BubbleScale_1 = 1.3,            --气泡开始尺寸(前三个)
        BubbleScale_2 = 0.4,            --气泡开始尺寸(第四个往后)
        BubbleEndScale = 1,             --气泡结束尺寸
        BubbleEndScaleTime = 0.8,       --气泡缩放时间
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        BirdRotateOffset = 6,                   --小鸟倾斜偏移
        BirdRotateOffsetTime = 2,               --小鸟倾斜偏移时间(循环)
        BirdFlyOutPos = Vector3(800, 50, 0),    --小鸟飞出点
        BirdFlyBackTime = 1,                    --小鸟飞回时间
    },

    -- 零元购
    ZeroBuy = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.6,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
    },

    -- 开服活动
    ServerActivityTab = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector3(1018, 0, 0),    --初始位置
            AlphaTweenTime = 0.4,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        }
    },

    -- 开服冲榜
    KfActivityView = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector2(0, -86),       --初始位置
            EndPosition = Vector2(0, -86),         --移动结束位置
            AlphaTweenTime = 0.4,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        }
    },

    -- 永世套装
    CustomizedSuitView = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector2(-400, 0),       --初始位置
            EndPosition = Vector2(0, 0),         --移动结束位置
            AlphaTweenTime = 0.3,                   --渐变执行时间
            MoveTweenTime = 0.3,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        }
    },

    -- 花神祭典
    RebateExtinctGiftView = {
        DelayDoTime = 0.2,                          --延时时间

        ListCellRender = {
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0,                          --渐变初始值
            ToAlpha = 1,                            --面板目标Alpha
            AlphaTweenTime = 0.8,                   --渐变执行时间
        },

        ListBigRender = {
            FromScale = Vector3(0.8, 0.8, 0.8),     --初始大小
            ToScale = Vector3(1, 1, 1),             --目标大小
            ScaleTweenTime = 0.6,                   --缩放执行时间
            FromAlpha = 0.1,                        --渐变初始值
            ToAlpha = 1,                            --面板目标Alpha
            AlphaTweenTime = 0.6,                   --渐变执行时间
        }
    },

    -- 花舞仙境
    PremiumGiftView = {
        DelayDoTime = 0.2,                          --延时时间

        ListCellRender = {
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0,                          --渐变初始值
            ToAlpha = 1,                            --面板目标Alpha
            AlphaTweenTime = 0.8,                   --渐变执行时间
        }
    },

    -- 天神试炼
    TianshenRoadView = {
        DelayDoTime = 0.2,              --延时时间
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            NextDoDelay2 = 0.1,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector3(1018, 0, 0),    --初始位置
            AlphaTweenTime = 0.4,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        },

        FirstRechargeListCellRender = {
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0,                          --渐变初始值
            ToAlpha = 1,                            --面板目标Alpha
            AlphaTweenTime = 0.8,                   --渐变执行时间
        }
    },

    -- 天财地宝
    WorldTreasureView = {
        DelayDoTime = 0.2,              --延时时间
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.1,                      --下一目标执行 间隔时间
            NextDoDelay2 = 0.1,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector3(1018, 0, 0),    --初始位置
            AlphaTweenTime = 0.2,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.2,                  --旋转执行时间
        },

        FirstRechargeListCellRender = {
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0,                          --渐变初始值
            ToAlpha = 1,                            --面板目标Alpha
            AlphaTweenTime = 0.8,                   --渐变执行时间
        },

        FlowingItemRender = {
            DelayDoTime = 0.2,                      --列表延时时间
            NextDoDelay = 0.1,                      --下一目标执行 间隔时间
            FromAlpha = 0,                          --渐变初始值
            ToAlpha = 1,                            --面板目标Alpha
            AlphaTweenTime = 0.2,                   --渐变执行时间
            AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        }
    },

    -- 双修-同游
    ArtifactTravel = {
        ListCellRender = {
            DelayDoTime = 0.1,                      --列表延时翻牌时间
            NextDoDelay = 0.1,                      --下一目标执行 间隔时间
            FromAlpha = 0,                        --渐变初始值
            StartPosition = Vector3(0, -50, 0),    --初始位置
            AlphaTweenTime = 0.3,                   --渐变执行时间
            AlphaTweenType = DG.Tweening.Ease.OutQuad,    -- 渐变曲线
            MoveTweenTime = 0.3,                    --移动时间
            MoveTweenType = DG.Tweening.Ease.OutQuad    -- 移动曲线
        },

        RewardListCellRender = {
            DelayDoTime = 0.1,                      --列表延时翻牌时间
            NextDoDelay = 0.1,                      --下一目标执行 间隔时间
            FromAlpha = 0,                        --渐变初始值
            StartPosition = Vector3(150, 0, 0),    --初始位置
            AlphaTweenTime = 0.3,                   --渐变执行时间
            AlphaTweenType = DG.Tweening.Ease.OutQuad,    -- 渐变曲线
            MoveTweenTime = 0.3,                    --移动时间
            MoveTweenType = DG.Tweening.Ease.OutQuad    -- 移动曲线
        },
    },

    --合服烟花/扭蛋机
    MergeFws = {
        EffectDelay = {				--三档特效延时
            [1] = 3,
            [2] = 3,
            [3] = 3,
        }
    },

    -- 群雄逐鹿
    QunXiongZhuLuView = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        Toggle_DelayDoTime = 0.3,                   --顶部按钮砸下来的时间间隔
        Pai_DelayDoTime = 0.2,                      --按钮上的牌子砸下来的时间间隔(仙盟封榜)
        Bg_Open_DoTime = 0.3,                       --大背景展开动画时间

        SDRewardRender = {--仙盟争霸列表
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector3(600, 0, 0),    --初始位置
            AlphaTweenTime = 0.4,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        }
    },

    -- 返利活动
    RebateActivityView = {
        MoveDelay = 0,                  --面板延迟开始移入时间
        MoveTime = 0.5,                 --面板移入时间
        AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
        AlphaTime = 0.8,                --面板上的信息淡入时间
        FromAlpha = 0.3,                --面板初始Alpha
        ToAlpha = 1,                    --面板目标Alpha
        AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型

        ListCellRender = {
            DelayDoTime = 0.2,                      --列表延时翻牌时间
            NextDoDelay = 0.2,                      --下一目标执行 间隔时间
            FromAlpha = 0.3,                        --渐变初始值
            StartPosition = Vector3(1018, 0, 0),    --初始位置
            AlphaTweenTime = 0.4,                   --渐变执行时间
            MoveTweenTime = 0.4,                    --移动时间

            StartRotatetion = Vector3(60, 0, 0),    --初始旋转
            RotateTweenTime = 0.4,                  --旋转执行时间
        }
    },

    --合服的鸿蒙悟道
	QiFuHmwd = {
		YeQianRotation = -100, 	--页签偏转角度
		WaitTime = 0.3, 		--等待底板的淡入完成时间
		ExpQiuMoveTime = 0.6,	--自己的淡入和经验球的移动时间
		FromAlpha = 0.3,		--面板初始Alpha
		ToAlpha = 1, 			--面板目标Alpha
	},

        -- 神机系统
    HiddenWeaponView = {
        Build = {
            MoveTime = 0.7,                --一个动画移动完成时长
            FromAlpha = 0,                 --面板初始Alpha
            ToAlpha = 1,                   --面板目标Alpha
            AlphaTime = 0.7,               --淡入时长   
            RightStart = Vector2(100,0),
            RightEnd = Vector2(0,0),
        },

        UPGRADE = {
            MoveTime = 0.7,                --一个动画移动完成时长
            FromAlpha = 0,                 --面板初始Alpha
            ToAlpha = 1,                   --面板目标Alpha
            AlphaTime = 0.7,               --淡入时长   
            RightStart = Vector2(100,0),
            RightEnd = Vector2(0,0),

            StartScale = 0.8,
            EndScale = 1,
            ScaleTime = 0.5,                   --一个动画移动完成时长
        },

        DETAIL = {
            MoveTime = 0.7,                --一个动画移动完成时长
            FromAlpha = 0,                 --面板初始Alpha
            ToAlpha = 1,                   --面板目标Alpha
            AlphaTime = 0.7,               --淡入时长   
            RightStart = Vector2(100,0),
            RightEnd = Vector2(0,0),

            StartScale = 0.8,
            EndScale = 1,
            ScaleTime = 0.5,                   --一个动画移动完成时长
        },

        TABLE_BAR = {
            circle_from_rotate_0 = Vector3(0, 0, 0),    --初始旋转
            circle_end_rotate_0 =  Vector3(0, 0, 180),
            circle_rotate_time = 0.8,

            circle_from_rotate_1 = Vector3(0, 0, 180),    --初始旋转
            circle_end_rotate_1 =  Vector3(0, 0, 0),

            cilun_zhizhen_from_rotate_0 = Quaternion.Euler(0, 0, -45),    --暗器指针选中
            cilun_zhizhen_end_rotate_0 =  Vector3(0, 0, 45),
            cilun_zhizhen_rotate_time_0 = 0.8,

            cilun_zhizhen_from_rotate_1 = Quaternion.Euler(0, 0, 45),    --灵甲指针选中
            cilun_zhizhen_end_rotate_1 =  Vector3(0, 0, -45),
            cilun_zhizhen_rotate_time_1 = 0.8,

            cell_from_rotate = Quaternion.Euler(0, 60, 0),    --初始旋转
            cell_end_rotate =  Quaternion.Euler(0, 10, 0),
            cell_rotate_time = 0.7,

            FromAlpha = 0,                 --面板初始Alpha
            ToAlpha = 1,                   --面板目标Alpha
            AlphaTime = 0.7,               --淡入时长   
            NextTime = 0.2, 

        },

    },

    --神机预告相关
    ShenJiNotice = {
        --特卖按钮缩放动效参数
        SpecialSaleCommon = {
            BtnBuy_StartScale = 1.2,            --按钮来回缩放 起始大小
            BtnBuy_EndScale = 1,            --按钮来回缩放 结束大小
            BtnBuy_ScaleTime = 0.8,         --按钮来回缩放 缩放一次动画时长
        },

        --现世动效参数
        XianShi = {
            Bg_StartScale = 0.9,        --底板初始大小
            Bg_EndScale = 1,            --底板结束大小
            Bg_ScaleTime = 0.3,         --底板动画时长

            Title_StartScale = 1.5,         --标题初始大小
            Title_EndScale = 1,             --标题结束大小
            Title_ScaleTime = 0.2,          --标题动画时长
            Title_DelayTime = 0.1,          --标题延迟

            Cloud_MoveTime = 0.5,           --云移动时长
            Cloud_DelayTime = 0.1,          --云延迟

            XuanChuanYu_StartAlpha = 0,             --宣传语初始alpha
            XuanChuanYu_EndAlpha = 1,               --宣传语alpha
            XuanChuanYu_AlphaTime = 0.3,            --宣传语alpha时长
            XuanChuanYu_AlphaDelayTime = 0.1,       --宣传语alpha延迟

            XuanChuanYu_StartScale = 1.5,           --特装、震撼初始大小
            XuanChuanYu_EndScale = 1,               --特装、震撼标题结束大小
            XuanChuanYu_ScaleTime = 0.2,            --特装、震撼动画时长
            XuanChuanYu_ZhenHanDelayTime = 0.2,     ---特装、震撼动画延迟

            Model_MoveTime = 0.5,           --模型移动时长

            ModelSelf_FloatDistance = 30,       --模型自身浮动距离
            ModelSelf_FloatTime = 3,            --模型自身浮动一次时长
            ModelSelf_RotateLeftAngle = 10, --模型自身旋转至最左侧的角度
            ModelSelf_RotateRightAngle = -15,   --模型自身旋转至最右侧的角度
            ModelSelf_RotateTime = 3,           --模型自身旋转一次上面的ModelSelf_RotateLeftAngle、 ModelSelf_RotateRightAngle角度的时长

            OtherInfo_StartAlpha = 0,           --其他信息初始alpha
            OtherInfo_EndAlpha = 1,             --其他信息alpha
            OtherInfo_AlphaTime = 0.8,          --其他信息alpha时长
            OtherInfo_DelayTime = 0.3,          --其他信息延迟

        },

        --一折十连
        Sale1 = {
            Title_StartScale = 1.5,         --标题初始大小
            Title_EndScale = 1,             --标题结束大小
            Title_ScaleTime = 0.2,          --标题动画时长
            Title_DelayTime = 0.1,          --标题延迟

            Item_MoveTime = 0.8,            --十连道具图标淡入时长
            Item_DelayTime = 0.1,           --十连道具图标延迟
            Item_FloatDistance = 15,        --十连道具图标上下浮动距离
            Item_FloatTime = 1.5,           --十连道具图标上下浮动时长

            OtherItem_MoveDistance = 50,    --其他道具移动距离
            OtherItem_MoveTime = 0.8,       --其他道具图标移入时长
            OtherItem_DelayTime = 0.1,      --其他道具总延迟
            OtherItem_JianGe = 0.1,         --间隔移入时间

            OtherInfo_StartAlpha = 0,           --其他信息初始alpha
            OtherInfo_EndAlpha = 1,             --其他信息结束alpha
            OtherInfo_AlphaTime = 0.8,          --其他信息alpha时长
            OtherInfo_DelayTime = 0.2,          --其他信息延迟
        },

        --特惠
        TeHui = {
            MoveDelay = 0,                  --面板延迟开始移入时间
            MoveTime = 0.5,                 --面板移入时间
            AlphaDelay = 0.5,               --面板延迟开始Aplha淡入的时间
            AlphaTime = 1,                --面板上的信息淡入时间
            FromAlpha = 0.3,                --面板初始Alpha
            ToAlpha = 1,                    --面板目标Alpha
            NextDoDelay = 0.2,              --下一目标执行 间隔时间
            AlphaShowType = DG.Tweening.Ease.Linear,    -- 主渐显变化曲线类型
        },

        --绝世灵甲
        Sale2 = {
            Title_StartScale = 1.5,         --标题初始大小
            Title_EndScale = 1,             --标题结束大小
            Title_ScaleTime = 0.4,          --标题动画时长
            Title_DelayTime = 0.2,          --标题延迟

            Bg_StartScaleX = 0.7,               --卷轴初始大小
            Bg_EndScaleX = 1,                   --卷轴结束大小
            Bg_ScaleXTime = 0.4,                --卷轴动画时长
            JuanZhou_StartAlpha = 0,            --卷轴初始alpha
            JuanZhou_EndAlpha = 1,              --卷轴结束alpha
            JuanZhou_AlphaTweenTime = 0.4,      --卷轴alpha时长
            JuanZhou_AlphaDelayTime = 0,        --卷轴延迟alpha时长

            Model_LeftMoveDistance = 50,        --模型移动距离
            Model_MiddleMoveDistance = 20,      --模型移动距离
            Model_RightMoveDistance = 50,       --模型移动距离
            Model_MoveTime = 1,                 --模型移动时长
            Model_DelayTime = 0.1,              --模型延迟开始移动

            ModelSelf_FloatDistance1 = 30,       --模型自身浮动距离(左)
            ModelSelf_FloatTime1 = 2,            --模型自身浮动一次时长(左)
            ModelSelf_FloatDistance2 = 20,       --模型自身浮动距离(中)
            ModelSelf_FloatTime2 = 3,            --模型自身浮动一次时长(中)
            ModelSelf_FloatDistance3 = 30,       --模型自身浮动距离(右)
            ModelSelf_FloatTime3 = 2,            --模型自身浮动一次时长(右)

            OtherInfo_StartAlpha = 0,           --其他信息初始alpha
            OtherInfo_EndAlpha = 1,             --其他信息结束alpha
            OtherInfo_AlphaTime = 0.6,          --其他信息alpha时长
            OtherInfo_DelayTime = 0.3,          --其他信息延迟
        }
    },

    -- 幸运大礼包
    LuckyGiftBag = {
        canvas_group_show = 0.5,  --隐-->显示
        MoveTime = 0.8,           --右侧面板移入时间
    },

    -- 五行
    FiveElements = {
        canvas_group_show = 0.3,  -- 隐-->显示
        movetime          = 0.8,  -- 右侧面板移入时间
        scale_time        = 0.8,  -- 变大时间
        nextdodelay       = 0.1,  -- 导航栏展示间隔
        mid_show          = 0.8,  -- 背包中间显示时间
        item_movetime     = 0.3,  -- 右侧面板移入时间
        circle            = 4,    -- 圆转一圈时间 
    },

    -- 龙神殿
    DragonTemple = {
        canvas_group_show = 0.3,  -- 隐-->显示
        movetime          = 0.8,  -- 右侧面板移入时间
        scale_time        = 0.8,  -- 变大时间
        nextdodelay       = 0.1,  -- 导航栏展示间隔
        mid_show          = 0.8,  -- 背包中间显示时间
        item_movetime     = 0.3,  -- 右侧面板移入时间
        circle            = 4,    -- 圆转一圈时间 
        desc_time         = 1,
    },

    -- 至尊领域
    ZhiZunLingYu = {
        canvas_group_show = 0.7,  -- 隐-->显示
        movetime          = 0.8,  -- 右侧面板移入时间 
    },

    -- 圣器
    HolyDark = {
        canvas_group_show = 0.3,  -- 隐-->显示
        scale_time        = 0.8,  -- 变大时间
        movetime          = 0.8,  -- 右侧面板移入时间
    },

    MultiFunction = {
        canvas_group_show           = 0.3,  -- 隐-->显示
        movetime                    = 0.8,  -- 右侧面板移入时间
        longzhu_circle6_rotate      = 4,    -- 灵珠太极
        longzhu_circle4_rotate      = 5,    
        longzhu_circle5_scale       = 3,
        longzhu_circle1_scale       = 3,
        holy_seal_circle1           = 8,
        holy_seal_circle3           = 8,

        kaiguang_circle1_rotate     = 4,    -- 开光转盘
    },

    --弑天套装
    ShiTianSuit = {
        canvas_group_show           = 0.5,  -- 隐-->显示
        movetime                    = 0.8,  -- 右侧面板移入时间
    },

    -- 修真路
    XiuZhen_Road = {
        DelayDoTime = 0.2,      -- 列表延时翻牌时间
        AlphaDelay = 0.6,       -- 面板延迟开始Aplha淡入的时间
        AlphaTime = 0.1,        -- 淡入时间
        MoveTime = 0.8,         --右侧面板移入时间
    },

    -- 委托任务
    Assignment_Road = {
        DelayDoTime = 0.2,      -- 列表延时翻牌时间
        AlphaDelay = 0.6,       -- 面板延迟开始Aplha淡入的时间
        AlphaTime = 0.1,        -- 淡入时间
        MoveTime = 0.8,         --右侧面板移入时间
    },

	--须弥神域.
	GuiXuDreamAttr = {
		AlphaTweenTime = 0.4,     --渐显时间
		FromAlpha = 0.3,          --初始Alpha
		ToAlpha = 1,              --目标Alpha
		MoveTweenTime = 0.4,      --移动时间
		StartPosition = Vector3(0, 0), --初始位置
		EndPosition = Vector2(1000, 0), --移动结束位置
	},

    -- 充值
    EquipBody = {
        DelayTime = 0.1,
        Next_time = 1,
        ListCellTween = {
            NextDoDelay = 0.1,
            FromAlpha = 0,                        --渐变初始值
            StartRotatetion = Vector3(0, 60, 0),    --初始旋转
            AlphaTweenTime = 0.5,                   --渐变执行时间
            RotateTweenTime = 0.3,                  --旋转执行时间
        }
    },

    -- 主界面按钮
    MainUIBtn = {
        RankInfoRootShowTime = 8;                 -- 主界面按钮排名信息展示时间
    },
    
-- end
}
