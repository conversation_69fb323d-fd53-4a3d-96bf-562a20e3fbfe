--结婚的通用提示面板
MarryAlert = MarryAlert or BaseClass(SafeBaseView)

local NORMALLABLERECTWIDTH = 548 --lable矩形框初始宽度
function MarryAlert:__init(str, ok_func, cancel_func, close_func, has_checkbox, is_show_action, is_maskbg_button_click, is_btn_dislocation)
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true, nil, BindTool.Bind1(self.OnCloseHandler, self))
    self.view_name = "MarryAlert"
    self.ok_func = ok_func
    self.cancel_func = cancel_func
    self.close_func = close_func
    self.content_str = nil ~= str and str or ""
    self.close_type = -1 --0 确定关闭， 1 取消关闭， 2 其他地方面板关闭
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_jiehun_second_panel", {vector2 = Vector2(2, 42)})
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_confirm_dialog")
    self.is_maskbg_button_click = nil == is_maskbg_button_click and false or is_maskbg_button_click
    if has_checkbox == nil then
		has_checkbox = false
	end

    self.has_checkbox = has_checkbox
    self.is_kuozhan = false                          -- 默认为不显示扩展花费的提示
    self.is_nolonger_tips = true					-- 是否勾选不再提示
    self.is_show_bg = true                           --是否显示背景框
	self.checkbox_tip_text = Language.Common.DontTip
	self.ok_btn_text = Language.Common.BtnOK
	self.cancel_btn_text = Language.Common.BtnCancel
	self.data = nil
	self.record_not_tip = false
	self.auto_do_func = true
	self.check_box_default_select = true

	self.rich_auto_close = nil
	self.auto_close_time = -1
	self.is_use_one = false
	self.is_no_closeBtn = false
	if self.is_maskbg_button_click == false then
		self.is_no_closeBtn = true
	end
	self.panel_type = nil

	self.label_rect_width = -1
	--提示文字是否需要左对齐
	self.is_left = true
	--提示文字对齐样式 如：UnityEngine.TextAnchor.MiddleLeft
	self.alignment_type = nil
	self.is_btn_dislocation = false
end

function MarryAlert:__delete()

end
function MarryAlert:SetMarkName(str)
	self.panel_type = str
end


function MarryAlert:OpenCallBack()
	if self.auto_close_time > 0 then
		CountDownManager.Instance:AddCountDown("common_alert_timer",BindTool.Bind1(self.UpdataAlertTime, self),BindTool.Bind1(self.CompleteAlertTime, self),nil,self.auto_close_time, 1)
		-- self:UpdataAlertTime(0, self.auto_close_time - 1)
	else
		-- self.node_list.lbl_auto_close.text.text = ""
	end
	self:AutoSetLabelRectWidth()
	self:AutoSetAlignment()
end

function MarryAlert:UpdataAlertTime(elapse_time, total_time)
	local remain_time = total_time - elapse_time
	-- self.node_list.lbl_auto_close.text.text = string.format(Language.Task.AutoClose, remain_time)
end

function MarryAlert:CompleteAlertTime()
	-- self.node_list.lbl_auto_close.text.text = ""
	self:Close()
end

function MarryAlert:ReleaseCallBack()
	self.rich_auto_close = nil
	self.rich_dialog = nil
	self.dialog_line_spacing = nil
	self.image_bg = nil
	if CountDownManager.Instance:HasCountDown("common_alert_timer") then
		CountDownManager.Instance:RemoveCountDown("common_alert_timer")
	end
	if self.check_box_default_select then
		self.is_nolonger_tips = true
		self.is_kuozhan = false
	end

	self.is_should_load_call = nil
	self.load_complete = nil
	self.alignment_type = nil
	self.is_btn_dislocation = nil
end

--外部调用：关闭取消按钮
function MarryAlert:SetUseOneSign( enable )
	self.is_use_one = enable
end

function MarryAlert:ShowIndexCallBack()
	self.node_list["btn_cancel"]:SetActive(true)
	self.node_list["btn_close_window"]:SetActive(true)

	if self.is_use_one then
		self:UseOne()
	end

	self:NoCloseButton()
end

function MarryAlert:LoadCallBack()
    self.node_list["text_view_name"].text.text = Language.Common.AlertTitile
    
    self.rich_dialog = self.node_list["rich_dialog"]
	self.image_bg = self.node_list["img9_autoname_2"]
    self:SetLableString(self.content_str, self.is_kuozhan)
    self:SetOkCancelBtnImage(self.ok_btn_image_name,self.cancel_btn_image_name)
	if self.pos then
		self:SetRichDialogTxtPos(self.pos)
	end
	self:SetTextLineSpace(self.dialog_line_spacing)

    if self.is_kuozhan then
    	NORMALLABLERECTWIDTH = 500
	end
    self.node_list["remind_text"]:SetActive(self.is_kuozhan)
	self.node_list["btn_OK"]:FindObj("Text").text.text = self.ok_btn_text
	self.node_list["btn_cancel"]:FindObj("Text").text.text = self.cancel_btn_text
	self.node_list["layout_nolonger_tips"]:SetActive(self.has_checkbox)
	self.node_list["label_no_longer"].text.text = self.checkbox_tip_text

	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind1(self.OnClickCancel, self))
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind1(self.OnCloseHandler, self))
	self.node_list["img_nohint_hook"]:SetActive(self.check_box_default_select)
	self.node_list["btn_nohint_checkbox"].button:AddClickListener(BindTool.Bind1(self.OnClickCheckBox, self))
    self.node_list["img_bg"].image.enabled = self.is_show_bg
	self.load_complete = true
	if self.is_should_load_call then
		self.is_should_load_call = nil
		self:AutoSetAlignment()
		self:SetAlignment(self.alignment_type)
		self:AutoSetLabelRectWidth()
	end


	if self.is_btn_dislocation then
		if self.node_list["btn_OK"] then
			self.node_list["btn_OK"].transform:SetSiblingIndex(0)
		end
	else
		if self.node_list["btn_OK"] then
			self.node_list["btn_OK"].transform:SetSiblingIndex(1)
		end
	end

end

function MarryAlert:AutoSetAlignment()
	if not self.load_complete then
		self.is_should_load_call = true
		return
	end
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["rich_dialog"].rect)
	local hight = math.ceil(self.node_list["rich_dialog"].rect.rect.height)
	 if self.alignment_type then
		self.rich_dialog.text.alignment = self.alignment_type
	else
		self.rich_dialog.text.alignment = UnityEngine.TextAnchor.MiddleCenter
	end
end

function MarryAlert:SetAlignment(alignment_type)
	-- body
	self.alignment_type = alignment_type
	if alignment_type and self.rich_dialog then
		self.rich_dialog.text.alignment = alignment_type
	end
end
function MarryAlert:SetRichDialogTxtPos(pos)
	if pos then
		self.pos = pos
		if self:IsLoadedIndex(0) then
			self.rich_dialog.transform.localPosition = pos
			self.pos = nil
		end
	end
end


function MarryAlert:OnClickOK()
	self.close_type = 0
	local can_close = true
    local vas = self.node_list["img_nohint_hook"]:GetActive()
	if vas then

		if self.today_str then
        local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
        PlayerPrefsUtil.SetInt(self.today_str .. main_role_id, open_day)
    end
	end
	if nil ~= self.ok_func then
		self.record_not_tip = self.is_nolonger_tips
		can_close = self.ok_func(self.is_nolonger_tips, self.data)
		if nil == can_close then can_close = true end
	end
	if can_close then
		self:Close()
	end
end

function MarryAlert:OnClickCancel()
	self.close_type = 1
	local can_close = true
	if nil ~= self.cancel_func then
		can_close = self.cancel_func()
		if nil == can_close then can_close = true end
	end

	if can_close then
		self:Close()
	end
end

function MarryAlert:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("common_alert_timer") then
		CountDownManager.Instance:RemoveCountDown("common_alert_timer")
	end
	if self.label_rect_width > 0 then
		self.label_rect_width = -1
	end

	if self.close_type == 2 then
		if nil ~= self.close_func then
			self.close_func()
		end
	end

    self.close_type = -1
    self.is_show_bg = true
end

function MarryAlert:OnClickCheckBox()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	self.node_list["img_nohint_hook"]:SetActive(not is_visible)
	self.is_nolonger_tips = not is_visible
end

function MarryAlert:SetCheckBoxDefaultSelect(visible)
	self.check_box_default_select = visible
	self.is_nolonger_tips = visible
	if self.node_list["img_nohint_hook"] then
		self.node_list["img_nohint_hook"]:SetActive(visible)
	end
end


-- 设置是否显示背景
function MarryAlert:SetImgBGSetActive(is_active)
    self.is_show_bg = is_active
    if self.node_list["img_bg"] then
        self.node_list["img_bg"].image.enabled = self.is_show_bg
    end
end

-- 设置自动关闭时间
function MarryAlert:SetAutoCloseTime(time)
	self.auto_close_time = time or self.auto_close_time
end

-- 设置内容
function MarryAlert:SetLableString(str, is_kuozhan)
	if nil ~= str and "" ~= str then
		self.content_str = str
		if self.rich_dialog then
			EmojiTextUtil.ParseRichText(self.rich_dialog.emoji_text, self.content_str, 20, COLOR3B.BLACK)
			self:AutoSetAlignment()
		end
	end
	if is_kuozhan then
		self.is_kuozhan = is_kuozhan
	end
end

-- 设置确定按钮文字
function MarryAlert:SetOkString(str)
	if nil ~= str and "" ~= str then
		self.ok_btn_text = str

		if nil ~= self.node_list["btn_OK"] then
			self.node_list["btn_OK"]:FindObj("Text").text.text = self.ok_btn_text
		end
	end
end

-- 设置取消按钮文字
function MarryAlert:SetCancelString(str)
	if nil ~= str and "" ~= str then
		self.cancel_btn_text = str

		if nil ~= self.node_list["btn_cancel"] then
			self.node_list["btn_cancel"]:FindObj("Text").text.text = self.cancel_btn_text
		end
	end
end

-- 设置复选框的显示文本
function MarryAlert:SetCheckBoxText(str)
	if nil ~= str and "" ~= str then
		self.checkbox_tip_text = str

		if nil ~= self.node_list["label_no_longer"] then
			self.node_list["label_no_longer"].text.text = self.checkbox_tip_text
		end
	end
end

-- 设置确定回调
function MarryAlert:SetOkFunc(ok_func)
	self.ok_func = ok_func
end

-- 设置取消回调
function MarryAlert:SetCancelFunc(cancel_func)
	self.cancel_func = cancel_func
end

-- 设置关闭回调
function MarryAlert:SetCloseFunc(close_func)
	self.close_func = close_func
end

function MarryAlert:SetData(value)
	self.data = value
end

-- 是否显示复选框, is_show是否显示，today_str今日不再提示关键字
function MarryAlert:SetShowCheckBox(is_show, today_str)
	self.today_str = today_str
	if self.has_checkbox ~= is_show then
		self.has_checkbox = is_show
		if nil ~= self.node_list["layout_nolonger_tips"] then
			self.node_list["layout_nolonger_tips"]:SetActive(is_show)
		end
	end
end

function MarryAlert:GetIsNolongerTips()
	return self.is_nolonger_tips
end

function MarryAlert:Open()
    local has_today_flag = false
	if self.today_str then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
        has_today_flag = PlayerPrefsUtil.GetInt(self.today_str .. main_role_id) == open_day
	end
	if (self.record_not_tip and self.has_checkbox ) or has_today_flag then
		if self.auto_do_func then
			self.ok_func(self.is_nolonger_tips, self.data)
		end
	else
		SafeBaseView.Open(self)
	end
end

function MarryAlert:SetAutoDoFunc(value)
	self.auto_do_func = value
end

function MarryAlert:Close()
	if self.close_type == -1 then
		self.close_type = 2
	end

	SafeBaseView.Close(self)
end

--设置让取消按钮消失
function MarryAlert:UseOne()
	self.is_use_one = true
	if nil ~= self.node_list and nil ~= self.node_list["btn_cancel"] and nil ~= self.node_list["btn_OK"] then
		self.node_list["btn_cancel"]:SetActive(false)
	end
end

function MarryAlert:SetOkCancelBtnImage(image_str_ok, image_str_cancel)
    if image_str_ok ~= nil and image_str_cancel ~= nil then
        self.ok_btn_image_name = image_str_ok
        self.cancel_btn_image_name = image_str_cancel
        if nil ~= self.node_list and nil ~= self.node_list["btn_cancel"] and nil ~= self.node_list["btn_OK"] then
            self.node_list["btn_OK"].image:LoadSprite(ResPath.GetF2CommonButtonToggle(self.ok_btn_image_name))
            self.node_list["btn_cancel"].image:LoadSprite(ResPath.GetF2CommonButtonToggle(self.cancel_btn_image_name))
        end
    end
end

--设置按钮转换
function MarryAlert:SetBtnDislocation(is_btn_dislocation)
	self.is_btn_dislocation = is_btn_dislocation
end

function MarryAlert:NoCloseButton(value)
	if nil ~= value then
		self.is_no_closeBtn = value
	end

	if nil ~= self.node_list and nil ~= self.node_list["btn_close_window"] then
		self.node_list["btn_close_window"]:SetActive(not value)
	end
end

function MarryAlert:GetIsShowTips()
	return not self.record_not_tip
end

function MarryAlert:ClearCheckHook()
	self.record_not_tip = false
end

function MarryAlert:OnCloseHandler()
	if self.close_before_func then
		self.close_before_func()
	end

	self:Close()
end

-- 点击遮罩关闭前 执行方法
function MarryAlert:SetCloseBeforeFunc(func)
	self.close_before_func = func
end

function MarryAlert:SetTextLineSpace(num)
	self.dialog_line_spacing = num
	if self.rich_dialog and nil ~= self.dialog_line_spacing then
		self.rich_dialog.text.lineSpacing = self.dialog_line_spacing
	end
end
function MarryAlert:GetClickCheckBoxIsShow()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	return is_visible or false
end

function MarryAlert:SetLableRectWidth(width)
	self.label_rect_width = width
end

function MarryAlert:AutoSetLabelRectWidth()
	if not self.load_complete then
		self.is_should_load_call = true
		return
	end
	local height = self.rich_dialog.rect.rect.height
	if self.label_rect_width > 0 then
		self.rich_dialog.rect.sizeDelta = Vector2(self.label_rect_width, height)
	else
		self.rich_dialog.rect.sizeDelta = Vector2(NORMALLABLERECTWIDTH, height)
	end
end


function MarryAlert:SetTextIsLeft(states)
	if states ~= nil then
		self.is_left = states
	end
end
--获取复选框的状态
function MarryAlert:SetCheckBoxState()
	if self.node_list and self.node_list["img_nohint_hook"] then
		return self.node_list["img_nohint_hook"]:GetActive()
	end
	return false
end

--获取是否已经标记本次不在提示
function MarryAlert:GetIsNeedOpenViewFlag()
	local has_today_flag = false
	if self.today_str then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		has_today_flag = PlayerPrefsUtil.GetInt(self.today_str .. main_role_id) == open_day
	end

	if (self.record_not_tip and self.has_checkbox )
		or has_today_flag then
		if self.auto_do_func then
			return true
		end
	else
		return false
	end
end
