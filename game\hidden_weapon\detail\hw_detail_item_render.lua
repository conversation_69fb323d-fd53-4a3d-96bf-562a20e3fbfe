-- 暗器详情-背包列表
HWDetailItemRender = HWDetailItemRender or BaseClass(BaseRender)

function HWDetailItemRender:__init()

end

function HWDetailItemRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list["box_res"])
end

function HWDetailItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
    end
    self.item_cell = nil
end

function HWDetailItemRender:Equip()
    if not self.data or not self.data.equip then
        return
    end
    if not self.data.index or self.data.index < 0 then
        return
    end
    local ok_func = function(...)
        HiddenWeaponRequest:ReqEquip(self.data.index)
    end
    local equiped = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(self.data.equip.big_type)
    -- 当前无装备中，直接请求
    if equiped == nil or equiped.equip == nil then
        ok_func()
        return
    end
    -- 当前装备珍稀，尝试用非珍稀替换
    local data_special_flag = self.data.equip.special_flag or 0
    local equip_special_flag = equiped.equip.special_flag or 0
    if equip_special_flag > data_special_flag then
        local alert_content =
            string.format(
            Language.ShenJiEquip.SPECIAL_EQUIP_CHANGE,
            self:GetTextColor(equiped),
            equiped.equip.name,
            Language.ShenJiEquip.COMMON_WEAPONTYPE_NAME[self.data.equip.big_type] or ""
        )
        local alert = TipWGCtrl.Instance:OpenAlertTips(alert_content, ok_func,nil,nil,nil,nil,nil,nil,nil,Language.ShenJiEquip.KMAlterTitle)
        -- alert:SetTitle(Language.ShenJiEquip.KMAlterTitle)
        return
    end
    -- 当前的装备原始品质更高
    local origin_base_color = self.data.equip.origin_data.base_color or 0
    local origin_equip_color = equiped.equip.origin_data.base_color or 0

    if origin_equip_color > origin_base_color then
        local alert_content =
            string.format(Language.ShenJiEquip.COLOR_EQUIP_CHANGE, self:GetTextColor(equiped), equiped.equip.name)
        local alert = TipWGCtrl.Instance:OpenAlertTips(alert_content, ok_func,nil,nil,nil,nil,nil,nil,nil,Language.ShenJiEquip.KMAlterTitle)
        -- alert:SetTitle(Language.ShenJiEquip.KMAlterTitle)
        return
    end
    ok_func()
end

function HWDetailItemRender:GetTextColor(data)
    if data == nil or data.equip == nil or data.equip.base_color == nil then
        return COLOR3B.WHITE
    end

    return ITEM_COLOR[data.equip.base_color] or COLOR3B.WHITE
end

function HWDetailItemRender:OnFlush()
    if self.data == nil or self.data.equip == nil then
        self.item_cell:SetData({})
        self.node_list.bg_res_bar_selected:SetActive(false)
        self.node_list["upflag_icon"]:SetActive(false)
        self.node_list["group_equiped"]:SetActive(false)
        self.node_list["group_equip"]:SetActive(false)
        self.item_cell:SetCellBgEnabled(true)
        self.node_list.img_touch:SetActive(false)
        return
    end

    local data = self.data
    self.node_list.img_touch:SetActive(true)
    if self.node_list["remind_better"] then
        self.node_list["remind_better"]:SetActive(data.is_better == true)
    end

    if data.index == HW_ANQI_EQUIP_INDEX or data.index == HW_RUANJIA_EQUIP_INDEX then
        self.node_list["group_equip"]:SetActive(false)
        self.node_list["group_equiped"]:SetActive(true)
    else
        self.node_list["group_equip"]:SetActive(true)
        self.node_list["group_equiped"]:SetActive(false)
    end

    self.item_cell:SetData(self.data)
    self.item_cell:SetButtonComp(false)
    self.item_cell:SetCellBgEnabled(false)
    if data.is_better == true then
        self.node_list["upflag_icon"]:SetActive(true)
    else
        self.node_list["upflag_icon"]:SetActive(false)
    end
end

function HWDetailItemRender:OnSelectChange(is_select)
    -- 高亮
    if nil == is_select then
        return
    end
    self.node_list.bg_res_bar_selected:SetActive((not IsEmptyTable(self.data)) and is_select)
end
