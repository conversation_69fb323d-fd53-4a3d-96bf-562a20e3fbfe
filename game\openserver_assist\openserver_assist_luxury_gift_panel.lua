function OpenServerAssistView:InitLuxuryGift()
    self.lg_reward_list = AsyncListView.New(LuxuryGiftRwardRender, self.node_list["lg_reward_list"])
    self:InitLuxuryGiftActInfo()
end

function OpenServerAssistView:ReleaseLuxuryGift()
    if self.lg_reward_list then
        self.lg_reward_list:DeleteMe()
        self.lg_reward_list = nil
    end
end

function OpenServerAssistView:InitLuxuryGiftActInfo()
    local time_desc = OpenServerAssistWGData.Instance:GetRandActTimeDesc(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT)
    self.node_list.lg_act_time.text.text = time_desc

    local other = OpenServerAssistWGData.Instance:GetLuxuryGiftOtherCfg()
    if other and other.palay_desc then
        self.node_list.lg_act_desc.text.text = other.palay_desc
    end
end

function OpenServerAssistView:FlushLuxuryGift()
    local reward_list = OpenServerAssistWGData.Instance:GetLuxuryGiftRewardList()
    self.lg_reward_list:SetDataList(reward_list)
end


------------------------  LuxuryGiftRwardRender ----------------------------------
LuxuryGiftRwardRender = LuxuryGiftRwardRender or BaseClass(BaseRender)
function LuxuryGiftRwardRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_to_get"], BindTool.Bind(self.OnClickGetReward, self))
end

function LuxuryGiftRwardRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    end
end

function LuxuryGiftRwardRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function LuxuryGiftRwardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.time.text.text = self.data.show_date
    XUI.SetButtonEnabled(self.node_list.btn_to_get, self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)
    self.node_list.is_get:SetActive(self.data.get_state == REWARD_STATE_TYPE.FINISH)
    self.node_list.is_miss:SetActive(self.data.get_state == REWARD_STATE_TYPE.MISSED)
    self.node_list.btn_to_get:SetActive(self.data.get_state ~= REWARD_STATE_TYPE.FINISH and self.data.get_state ~= REWARD_STATE_TYPE.MISSED)
    self.node_list.btn_to_get_remind:SetActive(self.data.get_state == REWARD_STATE_TYPE.CAN_FETCH)
    self.reward_list:SetDataList(self.data.reward_list)
end

function LuxuryGiftRwardRender:OnClickGetReward()
    if IsEmptyTable(self.data) then
        return
    end

    OpenServerAssistWGCtrl.Instance:SendRandActivityRequest(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT,
                                    LOGIN_LUXURY_GIFT_OP.FETCH_REWARD, self.data.act_day)
end
