FiveElementsResolveView = FiveElementsResolveView or BaseClass(SafeBaseView)

function FiveElementsResolveView:__init()
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1102, 590)})
	self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_resolve")
end

function FiveElementsResolveView:LoadCallBack()
    if not self.rs_bag_grid then
        self.rs_bag_grid = FiveElementsResloveGrid.New()
        self.rs_bag_grid:SetStartZeroIndex(false)
        self.rs_bag_grid:SetIsMultiSelect(true)
        self.rs_bag_grid:SelectMaxSelect(50)
        self.rs_bag_grid:CreateCells({
            col = 5,
            cell_count = 16,
            list_view = self.node_list["rs_bag_grid"],
            itemRender = FiveElementsResloveBagItem,
            assetBundle = "uis/view/five_elements_ui_prefab",
            assetName = "five_elements_reslove_bag_cell",
            change_cells_num = 1,
        })
        
        self.rs_bag_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectCB, self))
    end

    if not self.rs_get_list_view then
        self.rs_get_list_view = AsyncListView.New(FiveElementsResloveGetItem, self.node_list["rs_get_list_view"])
    end

    self.rs_color_select_list = {}
    XUI.AddClickEventListener(self.node_list["rs_quality_toggle_2"], BindTool.Bind(self.OnRSSelectColor, self, {1, 2}))
    XUI.AddClickEventListener(self.node_list["rs_quality_toggle_3"], BindTool.Bind(self.OnRSSelectColor, self, 3))
    XUI.AddClickEventListener(self.node_list["rs_quality_toggle_4"], BindTool.Bind(self.OnRSSelectColor, self, 4))
    XUI.AddClickEventListener(self.node_list["rs_btn_resolve"], BindTool.Bind(self.OnClickReslove, self))

    self.is_rs_first_load = true
    self.node_list["rs_quality_toggle_2"].toggle.isOn = true
    self.node_list.title_view_name.text.text = Language.FiveElements.Resole_Title
end

function FiveElementsResolveView:ReleaseCallBack()
    self:CleanResloveTimer()

    self.rs_color_select_list = {}
    self.is_rs_first_load = nil
    self.rs_send_oprate_flag = nil

    if self.rs_bag_grid then
        self.rs_bag_grid:DeleteMe()
        self.rs_bag_grid = nil
    end

    if self.rs_get_list_view then
        self.rs_get_list_view:DeleteMe()
        self.rs_get_list_view = nil
    end
end

function FiveElementsResolveView:OnBagSelectCB(cell)
    self:FlushGetItemView()
end

function FiveElementsResolveView:OnFlush()
    local reslove_list = FiveElementsWGData.Instance:GetFiveElementsBagListInfo()
    self.node_list["rs_no_item"]:SetActive(#reslove_list == 0)
    self.rs_bag_grid:SetDataList(reslove_list)
    self.rs_bag_grid:SetColorSelcet(self.rs_color_select_list)
    self:FlushGetItemView()
end

function FiveElementsResolveView:OnRSSelectColor(color, is_on)
    if type(color) == "number" then
        self.rs_color_select_list[color] = is_on
    elseif type(color) == "table" then
        for k,v in pairs(color) do
            self.rs_color_select_list[v] = is_on
        end
    end

    if self.is_rs_first_load then
        self.is_rs_first_load = nil
        return
    end

    if self.rs_bag_grid then
        self.rs_bag_grid:SetColorSelcet(self.rs_color_select_list)
    end

    self:FlushGetItemView()
end

function FiveElementsResolveView:FlushGetItemView()
    self.rs_select_list = self.rs_bag_grid:GetAllSelectCell()
    local get_item_list = {}
    local tslh_data = TianShenLingHeWGData.Instance
    local get_id, get_num

    for k,v in pairs(self.rs_select_list) do
        local cfg = FiveElementsWGData.Instance:GetStoreInfoByItemId(v.item_id)
        get_id = cfg and cfg.decompos_item[0].item_id or 0
        get_num = cfg and cfg.decompos_item[0].num or 0

        if get_id > 0 and get_num > 0 then
            get_item_list[get_id] = get_item_list[get_id] or 0
            get_item_list[get_id] = get_item_list[get_id] + get_num
        end
    end

    local show_list = {}
    for k,v in pairs(get_item_list) do
        table.insert(show_list, {item_id = k, get_num = v})
    end

    if not IsEmptyTable(show_list) then
        SortTools.SortAsc(show_list, "item_id")
    end

    self.node_list["rs_no_get_tips"]:SetActive(#show_list == 0)
    self.rs_get_list_view:SetDataList(show_list)
end

function FiveElementsResolveView:CleanResloveTimer()
    if self.reslove_cd_timer then
        CountDown.Instance:RemoveCountDown(self.reslove_cd_timer)
        self.reslove_cd_timer = nil
    end
end

function FiveElementsResolveView:OnClickReslove()
    if IsEmptyTable(self.rs_select_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.StoreNotEnough)
        return
    end

    if self.reslove_cd_timer or self.rs_send_oprate_flag then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.ResloveDoing)
        return
    end

    self.rs_send_oprate_flag = true
    self.node_list["rs_resolve_effect"]:SetActive(true)
    self.reslove_cd_timer = CountDown.Instance:AddCountDown(1.8, 0.2,

    function (elapse_time, total_time)
         if elapse_time > 1.4 and self.rs_send_oprate_flag then
            FiveElementsWGCtrl.Instance:SendSFiveElementsDecompsStoneRequest(#self.rs_select_list, self.rs_select_list)
            self.rs_send_oprate_flag = nil
        end
    end,

    function()
        if self.node_list["rs_resolve_effect"] then
            self.node_list["rs_resolve_effect"]:SetActive(false)
        end

        self:CleanResloveTimer()
    end)
end

--------------------------------FiveElementsResloveGrid-------------------------
FiveElementsResloveGrid = FiveElementsResloveGrid or BaseClass(AsyncBaseGrid)
function FiveElementsResloveGrid:__init()
    self.cur_select_rs_num = 0
end

function FiveElementsResloveGrid:SelectMaxSelect(select_num)
    self.max_select_num = select_num
end

function FiveElementsResloveGrid:SelectCellHandler(cell)
    local cell_index = cell:GetIndex()

    if not self.select_tab[1][cell_index] then
        if self.cur_select_rs_num >= self.max_select_num then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.FiveElements.ResloveLimit)
            return
        end
    end

	self.cur_index = cell_index
	if self.is_multi_select then
        local is_select = false

		if not self.select_tab[1][cell_index] then
            is_select = true
			self.select_tab[1][cell_index] = true
            self.cur_select_rs_num = self.cur_select_rs_num + 1
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil
            self.cur_select_rs_num = self.cur_select_rs_num - 1
		else
            is_select = true
			self.select_tab[1][cell_index] = true
            self.cur_select_rs_num = self.cur_select_rs_num + 1
		end

        cell:SetSelect(is_select, true)
	else
		if not self.select_tab[1][cell_index] then
            self.select_tab[1] = {}
            self.select_tab[1][cell_index] = true
            self:__DoRefreshSelectState()
        end
	end

    if nil ~= self.select_callback then
		self.select_callback(cell)
	end
end

function FiveElementsResloveGrid:SetColorSelcet(select_color_list)
    select_color_list = select_color_list or {}
    if IsEmptyTable(select_color_list) then
        return
    end

    self.select_tab[1] = {}
    self.cur_select_rs_num = 0
    local data = self.cell_data_list

    for i = 1, self.has_data_max_index do
        if data[i] and select_color_list[data[i].color] then
            if self.cur_select_rs_num < self.max_select_num then
                self.cur_select_rs_num = self.cur_select_rs_num + 1
                self.select_tab[1][i] = true
            else
                break
            end
        end
    end

    self:__DoRefreshSelectState()
end

------------------------------FiveElementsResloveBagItem---------------------------
FiveElementsResloveBagItem = FiveElementsResloveBagItem or BaseClass(BaseRender)
function FiveElementsResloveBagItem:__init()

end

function FiveElementsResloveBagItem:LoadCallBack()
    self.lh_item = BaseLingHeCell.New(nil, self.node_list["linghe_node"])
    self.lh_item:SetUseButton(false)
end

function FiveElementsResloveBagItem:__delete()
    if self.lh_item then
        self.lh_item:DeleteMe()
        self.lh_item = nil
    end
end

function FiveElementsResloveBagItem:OnFlush()
    if self.data == nil then
        return
    end

    self.lh_item:SetData(self.data)
    self.node_list["name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
end

function FiveElementsResloveBagItem:OnSelectChange(is_select)
    self.node_list["normal_bg"]:SetActive(not is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end

---------------------------FiveElementsResloveGetItem------------------------------
FiveElementsResloveGetItem = FiveElementsResloveGetItem or BaseClass(BaseRender)
function FiveElementsResloveGetItem:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["can_get_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
    self.node_list["can_get_num"].text.text = self.data.get_num
end