UltimateBattlefieldWGData = UltimateBattlefieldWGData or BaseClass()

function UltimateBattlefieldWGData:__init()
	if UltimateBattlefieldWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[UltimateBattlefieldWGData] attempt to create singleton twice!")
		return
	end
	UltimateBattlefieldWGData.Instance = self
	RemindManager.Instance:Register(RemindName.CrossUltimate, BindTool.Bind(self.GetCrossUltimateRemind, self))

	self:InitCfg()
	self:InitDataParam()
end

function UltimateBattlefieldWGData:__delete()
	UltimateBattlefieldWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.CrossUltimate)
end

-- 初始化配配置
function UltimateBattlefieldWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_1vn_auto")
	if cfg then
		self.base_cfg = cfg.other and cfg.other[1] or nil
		self.stage_cfg = cfg.stage
		self.camp_cfg = cfg.camp
		self.question_cfg = cfg.question
		self.talent_cfg = cfg.talent
		self.talent_refresh_cfg = cfg.talent_refresh
		self.falling_cfg = cfg.falling
		self.score_reward_cfg = cfg.score_reward
		self.score_rank_reward_cfg = cfg.score_rank_reward
		self.convert_cfg = cfg.convert
		self.barrage_cfg = cfg.barrage
		self.skill_cfg = cfg.skill
	end
end

-- 初始化参数
function UltimateBattlefieldWGData:InitDataParam()
	self.find_pos = nil
end

-- 终极战场红点
function UltimateBattlefieldWGData:GetCrossUltimateRemind()
	local result = self:GetWorshipFlagRed()
	return result
end

---------------------------------------获取相关配置信息---------------------------
-- 获取基础信息
function UltimateBattlefieldWGData:GetBaseCfg()
	return self.base_cfg 
end

-- 获取阶段配置（根据阶段seq）
function UltimateBattlefieldWGData:GetStageCfgBySeq(seq)
	local emtry = {}
	return (self.stage_cfg or emtry)[seq] or nil
end

-- 获取阶段配置（根据阶段seq）
function UltimateBattlefieldWGData:GetAllStageCfg()
	if not self.stage_cfg then
		return nil
	end

	local table_return = {}
	for _, stage_data in pairs(self.stage_cfg) do
		local temp_data = {}
		temp_data.is_stage = true
		temp_data.stage_data = stage_data
		table.insert(table_return, temp_data)
	end

	table.sort(table_return, function (a, b)
		if a.stage_data == nil or b.stage_data == nil then
			return true
		end
		
		return a.stage_data.seq < b.stage_data.seq
	end)

	return table_return
end

-- 获取阵营配置（根据seq）
function UltimateBattlefieldWGData:GetCampCfgBySeq(seq)
	local emtry = {}
	return (self.camp_cfg or emtry)[seq] or nil
end

-- 获取题目配置（根据seq）
function UltimateBattlefieldWGData:GetQuestionCfgBySeq(seq)
	local emtry = {}
	return (self.question_cfg or emtry)[seq] or nil
end

-- 获取天赋配置（根据seq）
function UltimateBattlefieldWGData:GetTalentCfgBySeq(seq)
	local emtry = {}
	return (self.talent_cfg or emtry)[seq] or nil
end

-- 获取天赋列表（根据天赋类型）
function UltimateBattlefieldWGData:GetTalentListByType(talent_type)
	if (not self.talent_cfg) or (not talent_type) then
		return nil
	end

	local talent_table = {}
	for _, talent_data in pairs(self.talent_cfg) do
		if talent_data.talent_type == talent_type then
			table.insert(talent_table, talent_data)
		end
	end

	table.sort(talent_table, SortTools.KeyLowerSorter("seq"))

	return talent_table
end


-- 获取天赋刷新配置（根据答题次数）
function UltimateBattlefieldWGData:GetTalentRefreshCfgBySeq(question_times)
	local emtry = {}
	return (self.talent_refresh_cfg or emtry)[question_times] or nil
end

-- 获取凋落物配置（根据seq）
function UltimateBattlefieldWGData:GetFallingCfgBySeq(seq)
	local emtry = {}
	return (self.falling_cfg or emtry)[seq] or nil
end

-- 获取积分奖励配置（根据seq）
function UltimateBattlefieldWGData:GetScoreRewardCfgBySeq(seq)
	local emtry = {}
	return (self.score_reward_cfg or emtry)[seq] or nil
end

-- 获取积分排行奖励配置（根据排行）
function UltimateBattlefieldWGData:GetFallingCfgBySeq(role_rank)
	if not self.score_rank_reward_cfg then
		return nil
	end

	for _, rank_reward_data in pairs(self.score_rank_reward_cfg) do
		if role_rank >= rank_reward_data.min_rank and role_rank <= rank_reward_data.max_rank then
			return rank_reward_data
		end
	end
	
	return nil
end

-- 获取所有的积分排行奖励
function UltimateBattlefieldWGData:GetAllScoreRankReward()
	return self.score_rank_reward_cfg
end

-- 获取所有的积分奖励
function UltimateBattlefieldWGData:GetAllScoreReward()
	local table_return = {}
	for _, score_reward_data in pairs(self.score_reward_cfg) do
		table.insert(table_return, score_reward_data)
	end

	table.sort(table_return, SortTools.KeyUpperSorter("need_score"))
	return table_return
end

-- 获取所有的积分奖励(参考积分)
function UltimateBattlefieldWGData:GetAllScoreRewardByPlayerScore(player_score)
	local table_return = {}
	for _, score_reward_data in pairs(self.score_reward_cfg) do
		local temp_data = {}
		temp_data.cfg_data = score_reward_data
		temp_data.compare_score = score_reward_data.need_score - player_score
		temp_data.get_ed = false

		if temp_data.compare_score <= 0 then
			-- 超过了目标值移到最下面去
			temp_data.compare_score = temp_data.compare_score + 10000
			temp_data.get_ed = true
		end

		table.insert(table_return, temp_data)
	end

	table.sort(table_return, SortTools.KeyLowerSorter("compare_score"))
	return table_return
end

-- 获取积分奖励配置（根据seq）
function UltimateBattlefieldWGData:GetConvertCfgBySeq(seq)
	local emtry = {}
	return (self.convert_cfg or emtry)[seq] or nil
end

--获取所有的积分奖励配置
function UltimateBattlefieldWGData:GetShopShowInfo()
	return self.convert_cfg
end

-- 获取挂机点
function UltimateBattlefieldWGData:GetFindPostion()
	if self.find_pos then
		return self.find_pos
	end

	if not self.base_cfg then
		return nil
	end

	self.find_pos = {}
	local pos_str_list = Split(self.base_cfg.find_postion, ",")
	local center_x = tonumber(pos_str_list[1]) or 0
	local center_y = tonumber(pos_str_list[2]) or 0
	local real_x = math.random(center_x - 8, center_x + 8)
	local real_y = math.random(center_y - 8, center_y + 8)
	self.find_pos.x = real_x
	self.find_pos.y = real_y

	return self.find_pos
end

--获取某个弹幕配置根据Seq
function UltimateBattlefieldWGData:GetBarrageCfgBySeq(seq)
	local emtry = {}
	return (self.barrage_cfg or emtry)[seq] or nil
end

--随机获取一份弹幕配置
function UltimateBattlefieldWGData:GetBarrageRandomCfg()
	local seq = math.random(0, #self.barrage_cfg)
	local cfg = self:GetBarrageCfgBySeq(seq)
	return cfg
end

--弹幕
function UltimateBattlefieldWGData:GetBarrageMessage()
	local cfg = self:GetBarrageRandomCfg()
	return {desc_content = cfg and cfg.barrage_content or "",
	bg_bundle = nil,
	bg_asset = nil,
	is_shield_bg = true,
	need_random_color = true,
	move_speed = math.random(200, 500),
	color_data_list = ITEM_COLOR_DARK}
end

-- 获取技能列表
function UltimateBattlefieldWGData:GetTalentSkillCfg()
	return self.skill_cfg
end

-- 获取技能根据id
function UltimateBattlefieldWGData:GetTalentSkillCfgBySkillId(skill_id)
	return (self.skill_cfg or {})[skill_id] or nil
end

-------------------------------服务器相关更新数据------------------------------
-- 房间信息 场景外请求
function UltimateBattlefieldWGData:SetRoomInfo(protocol)
	if not self.room_info then
		self.room_info = {}
	end

	self.room_info.room_id = protocol.room_id
	self.room_info.end_time = protocol.end_time
end

-- 获取房间信息
function UltimateBattlefieldWGData:GetRoomInfo()
	return self.room_info
end

-- 排行榜信息
function UltimateBattlefieldWGData:SetScoreRankInfo(protocol)
	if not self.rank_info then
		self.rank_info = {}
	end

	self.rank_info.stage = protocol.stage
	self.rank_info.stage_win_flag = protocol.stage_win_flag
	self.rank_info.rank_item_list = protocol.rank_item_list

	local uuid = RoleWGData.Instance:GetUUid()
	-- 设置自己的排名和数据和对应阵营积分
	for _, rank_data in ipairs(self.rank_info.rank_item_list) do
		if rank_data.uuid == uuid then
			self.my_rank_info = rank_data
			break
		end
	end
end

-- 排行榜信息
function UltimateBattlefieldWGData:CheckIsSelf(uuid)
	local uuid_self = RoleWGData.Instance:GetUUid()
	return uuid == uuid_self
end

-- 排行榜某个玩家信息更新
function UltimateBattlefieldWGData:GuessFlagUpdate(protocol)
	if (not self.rank_info) or (not self.rank_info.rank_item_list) then
		return
	end

	for _, rank_data in ipairs(self.rank_info.rank_item_list) do
		if rank_data.uuid == protocol.uuid then
			rank_data.history_camp_flag = protocol.history_camp_flag
			rank_data.stage_guess_flag = protocol.stage_guess_flag
			rank_data.stage_guess_camp_flag = protocol.stage_guess_camp_flag
		end
	end
end

-- 获取积分排行榜
function UltimateBattlefieldWGData:GetScoreRankInfo()
	return self.rank_info
end

-- 获取自身的排行信息
function UltimateBattlefieldWGData:GetMyRankInfo()
	return self.my_rank_info
end

-- 获取当前的积分排行
function UltimateBattlefieldWGData:GetCurrRankInfoList()
	local rank_list = {}
	if not self.rank_info then
		return nil, nil
	end

	for _, rank_data in ipairs(self.rank_info.rank_item_list) do
		table.insert(rank_list, rank_data)
	end
	
	table.sort(rank_list, SortTools.KeyLowerSorter("rank"))

	return rank_list,self.my_rank_info
end

-- 玩家玩法相关信息 主动请求 有变更会推送(信息汇总)
function UltimateBattlefieldWGData:SetOtherBaseInfo(protocol)
	self.score = protocol.score
	self.question_flag = protocol.question_flag
	self.correct_answer_times = protocol.correct_answer_times
	self.talent_seq = protocol.talent_seq
	self.random_talent_seq = protocol.random_talent_seq
	self.refresh_talent_times = protocol.refresh_talent_times
	self.random_talent_flag = protocol.random_talent_flag
	self.history_camp_flag = protocol.history_camp_flag
	self.stage_guess_flag = protocol.stage_guess_flag
	self.stage_guess_camp_flag = protocol.stage_guess_camp_flag
end

-- 获取玩家当前积分
function UltimateBattlefieldWGData:GetPlayerScore()
	return self.score or 0
end

-- 获取玩家答题标志
function UltimateBattlefieldWGData:GetQuestionFlag()
	return self.question_flag or nil
end

-- 获取玩家答题正确次数
function UltimateBattlefieldWGData:GetPlayerAnswerTimes()
	return self.correct_answer_times or 0
end

-- 获取玩家已选择天赋索引
function UltimateBattlefieldWGData:GetPlayerTalentSeq()
	return self.talent_seq or -1
end

-- 获取玩家已选择天赋索引(默认)
function UltimateBattlefieldWGData:GetPlayerRandomTalentSeq()
	return self.random_talent_seq or 0
end

-- 获取玩家天赋已刷新次数
function UltimateBattlefieldWGData:GetPlayerTalentRefreshTimes()
	return self.refresh_talent_times or 0
end

-- 获取玩家当前天赋随机库
function UltimateBattlefieldWGData:GetPlayerRandomTalent()
	return self.random_talent_flag or nil
end

-- 获取当前的天赋随机列表
function UltimateBattlefieldWGData:GetPlayerRandomTalentList()
	if not self.random_talent_flag then
		return
	end

	local list = {}

	for index, value in pairs(self.random_talent_flag) do
		if value == 1 then
			-- 表配置是从零开始的
			table.insert(list, index)
		end
	end

	return list
end

-- 获取玩家历史的阵营选择
function UltimateBattlefieldWGData:GetHistoryCampFlag()
	return self.history_camp_flag or nil
end

-- 获取玩家当前的竞猜标记
function UltimateBattlefieldWGData:GetPlayerGuessFlag()
	return self.stage_guess_flag or nil
end

-- 获取玩家当前的竞猜阵营
function UltimateBattlefieldWGData:GetPlayerGuessCamp()
	return self.stage_guess_camp_flag or nil
end

-- 设置答题信息
function UltimateBattlefieldWGData:SetQuestionInfo(protocol)
	if not self.question_info then
		self.question_info = {}
	end

	self.question_info.question_num = protocol.question_num			--当前第几题 1开始
	self.question_info.question_seq = protocol.question_seq
	self.question_info.question_time = protocol.question_time
	self.question_info.question_standby_time = protocol.question_standby_time
end

-- 获取答题信息
function UltimateBattlefieldWGData:GetQuestionInfo()
	return self.question_info
end

-- 设置答题正确数量
function UltimateBattlefieldWGData:SetQuestionRightMessage(right_number, ratio_number)
	self.right_number = right_number
end

-- 获取答题正确数量
function UltimateBattlefieldWGData:GetQuestionRightNumber()
	return self.right_number or 0
end

-- 设置战场信息
function UltimateBattlefieldWGData:SetSceneInfo(protocol)
	if not self.battle_scene_info then
		self.battle_scene_info = {}
	end

	self.battle_scene_info.stage = protocol.stage			--当前第几题 1开始
	self.battle_scene_info.stage_win_flag = protocol.stage_win_flag
	self.battle_scene_info.next_stage_time = protocol.next_stage_time
	self.battle_scene_info.stage_guess_end_time = protocol.stage_guess_end_time
end

-- 获取战场信息
function UltimateBattlefieldWGData:GetSceneInfo()
	return self.battle_scene_info
end

-- 设置天赋信息
function UltimateBattlefieldWGData:SetTalentData(protocol)
	if not self.talent_info then
		self.talent_info = {}
	end

	self.talent_info.score = protocol.score			--当前第几题 1开始
	self.talent_info.score_reward_flag = protocol.score_reward_flag
	self.talent_info.buy_talent_time = protocol.buy_talent_time			--当前第几题 1开始
	self.talent_info.buy_talent_seq = protocol.buy_talent_seq
	self.talent_info.buy_random_talent_seq = protocol.buy_random_talent_seq
	self.talent_info.random_talent_flag = protocol.random_talent_flag
	self.talent_info.talent_item_list = protocol.talent_item_list
end

-- 获取天赋信息
function UltimateBattlefieldWGData:GetTalentData()
	return self.talent_info
end

-- 设置相关信息 主动请求 有变更会推送
function UltimateBattlefieldWGData:SetBaseInfo(protocol)
	if not self.base_data then
		self.base_data = {}
	end

	self.base_data.convert_times_list = protocol.convert_times_list
	self.base_data.worship_flag = protocol.worship_flag
	self.base_data.worship_times = protocol.worship_times

	self.base_data.barrage_reward_flag = protocol.barrage_reward_flag
	self.base_data.last_send_barrage_time = protocol.last_send_barrage_time
	self.base_data.send_barrage_times = protocol.send_barrage_times
end

-- 获取基础的相关信息
function UltimateBattlefieldWGData:GetBaseInfo()
	return self.base_data
end

-- 获取膜拜的相关红点信息(囊括了左，右，当前，和全体，只存在一种情况，看情况传参)
function UltimateBattlefieldWGData:GetWorshipFlagRed(index, is_left, is_curr)
	local act = ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE or nil
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)

	if act_status then
		if act_status.status == ACTIVITY_STATUS.OPEN or act_status.status == ACTIVITY_STATUS.STANDY then
			return 0
		end
	end

	local base_cfg = self:GetBaseCfg()
	local max_worship_times = base_cfg and base_cfg.worship_times or 0
	local base_info = self:GetBaseInfo()

	if (not base_info) or (not base_info.worship_flag) then
		return 0
	end

	local worship_flag = base_info.worship_flag
	local curr_worship_times = base_info and base_info.worship_times
	if curr_worship_times >= max_worship_times then
		return 0
	end

	local worship_infos = self:GetWorshipInfoList()
	if (not worship_infos) or (#worship_infos < 1) then
		return 0
	end

	local self_uid = RoleWGData.Instance:GetUUid().temp_low

	if is_curr and index then
		if worship_infos[index] and worship_infos[index].uid == self_uid then
			if worship_infos[index].worship_reward_times ~= 0 then
				return 1
			else
				return 0
			end
		else
			if worship_flag[index - 1] == 0 then
				return 1
			else
				return 0
			end
		end
	end

	local start_index = 1
	local end_index = #worship_infos

	if is_left ~= nil and index ~= nil then
		if is_left then
			end_index = index - 1
		else
			start_index = index + 1
		end

		if end_index > #worship_infos then
			return 0
		elseif start_index < 1 then
			return 0
		end
	end

	for i = start_index, end_index do
		if worship_infos[i] and worship_infos[i].uid == self_uid then
			if worship_infos[i].worship_reward_times ~= 0 then
				return 1
			end
		else
			if worship_flag[i - 1] == 0 then
				return 1
			end
		end
	end

	return 0
end	

-- 设置膜拜列表
function UltimateBattlefieldWGData:SetWorshipInfoList(protocol)
	if not protocol.worship_item_list then
		return
	end

	for index, worship_item in ipairs(protocol.worship_item_list) do
		self:UpdateWorshipInfo(index, worship_item, false)
	end
end

-- 更新膜拜对象
function UltimateBattlefieldWGData:UpdateWorshipInfo(index, protocol_data, is_protocol)
	if not self.worship_infos then
		self.worship_infos = {}
	end

	-- 是单独刷新需要增加一个下标
	local real_index = is_protocol and index + 1 or index
	self.worship_infos[real_index] = protocol_data
end

-- 获取膜拜列表
function UltimateBattlefieldWGData:GetWorshipInfoList()
	return self.worship_infos
end

-- 设置弹窗数据
function UltimateBattlefieldWGData:SetBarrageChatOpenData(protocol)
	if not self.barrage_info then
		self.barrage_info = {}
	end

	self.barrage_info.uid = protocol.uid
	self.barrage_info.name = protocol.name
	self.barrage_info.time = protocol.time
	self.barrage_info.worship_index = protocol.worship_index
end

-- 获取弹窗数据
function UltimateBattlefieldWGData:GetBarrageChatOpenData()
	return self.barrage_info
end

-- 检测是否可以获得弹幕奖励
function UltimateBattlefieldWGData:CheckCanGetReward()
	local base_data = self:GetBaseInfo()
	local base_cfg = self:GetBaseCfg()
	if (not base_data) or (not base_cfg) then
		return false
	end

	local limit_times = base_cfg.send_barrage_reward_times or 3
	local finish_times = base_data.barrage_reward_flag or 3
	local is_can_get_reward = finish_times < limit_times
	return is_can_get_reward
end

----------------------------------------------------------------------------------
-- 场景中检测当前活动是否开启
function UltimateBattlefieldWGData:CheckActIsBattleByScene()
	local scene_type = Scene.Instance:GetSceneType()
	local is_not_ready = self:CheckActIsBattle()
	return is_not_ready and scene_type == SceneType.CROSS_ULTIMATE_BATTLE
end

-- 检测是否是战斗时间
function UltimateBattlefieldWGData:CheckActIsBattle()
	local act = ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE or nil

	if act == nil then
		return false
	end

	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)
	local is_battle = act_status and act_status.status == ACTIVITY_STATUS.OPEN or false

	local is_guess = self:CheckActIsGuessTime()
	is_battle = is_battle and (not is_guess)

	return is_battle 
end

-- 检测是否处于竞猜时间
function UltimateBattlefieldWGData:CheckActIsGuessTime()
	-- 增加是否处于竞猜阶段
	local scene_info = self:GetSceneInfo()
	if not scene_info then
		return false
	end

	local guess_time = scene_info.stage_guess_end_time - TimeWGCtrl.Instance:GetServerTime()
	return guess_time > 0
end

-- 检测活动是否开启
function UltimateBattlefieldWGData:CheckActIsOpen()
	local act = ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE or nil
	local can_enter = false

	if act == nil then
		return can_enter
	end

	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)
	local room_info = self:GetRoomInfo()

	if (act_status == nil) or (room_info == nil) then
		return can_enter
	end

	if act_status.status == ACTIVITY_STATUS.STANDY then
		can_enter = true
	elseif act_status.status == ACTIVITY_STATUS.OPEN then
		local end_time = room_info.end_time or 0
		if end_time == 0 then
			can_enter = room_info.room_id ~= 0
		else
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			can_enter = room_info.room_id and (server_time < end_time)
		end
	end

	return can_enter 
end

