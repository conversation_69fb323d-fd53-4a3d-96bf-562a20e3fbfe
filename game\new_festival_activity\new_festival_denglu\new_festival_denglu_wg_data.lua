NewFestivalDengLuWGData = NewFestivalDengLuWGData or BaseClass()
NewFestivalDengLuWGData.SHOW_REWARD_POOL_COUNT = 7 -- 界面展示

function NewFestivalDengLuWGData:__init()
	if NewFestivalDengLuWGData.Instance then
		ErrorLog("[NewFestivalDengLuWGData] Attemp to create a singleton twice !")
	end

	NewFestivalDengLuWGData.Instance = self
	self.can_draw_num = 0
	self.cur_grade = -1
	self.reward_list = {}
	self:InitCfg()

	RemindManager.Instance:Register(RemindName.NewFestivalDengLu, BindTool.Bind(self.GetNewFestivalDLRemind, self))
end

function NewFestivalDengLuWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_denglu_config_auto")
	self.grade_cfg = cfg.open_day
	self.reward_cfg = ListToMap(cfg.reward_pool, "grade", "seq")
end

function NewFestivalDengLuWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.NewFestivalDengLu)
    NewFestivalDengLuWGData.Instance = nil
end

function NewFestivalDengLuWGData:GetCurRewardCfg(grade)
	return self.reward_cfg[grade]
end

function NewFestivalDengLuWGData:GetCurGrade()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k, v in pairs(self.grade_cfg) do
		if open_day >= v.start_day and open_day <= v.end_day then
			return v.grade
		end
	end

	return 0
end

function NewFestivalDengLuWGData:GetDisplaycfg()
	local cur_cfg = self:GetCurRewardCfg(self.cur_grade)
	local display_cfg = {}
	if not cur_cfg then
		return display_cfg
	end

	for k,v in pairs(cur_cfg) do
		if v.is_display == 1 then
			table.insert(display_cfg, v)
		end
	end

	return display_cfg
end

function NewFestivalDengLuWGData:InsertRewardList(item)
	table.insert(self.reward_list, item)
	return self.reward_list
end

function NewFestivalDengLuWGData:ClearRewardList()
	self.reward_list = {}
	return self.reward_list
end



function NewFestivalDengLuWGData:SetDengLuInfo(protocol)
	self.cur_grade = protocol.cur_grade
	self.can_draw_num = protocol.can_draw_num
	self.draw_num_list = protocol.draw_num_list
end

-- 当前可抽取次数
function NewFestivalDengLuWGData:GetCanDrawNum()
	return self.can_draw_num
end

function NewFestivalDengLuWGData:IsExhaust(seq)
	local cur_cfg = self:GetCurRewardCfg(self.cur_grade)
	if not cur_cfg then
		return false
	end

	local num_limit = cur_cfg[seq].num_limit
	for k, v in pairs(self.draw_num_list) do
		if seq + 1 == k then
			return num_limit == v
		end
	end

	return false
end

function NewFestivalDengLuWGData:GetDrawNumList()
	return self.draw_num_list
end

function NewFestivalDengLuWGData:GetNewFestivalDLRemind()
	if self:IsEnoughNum() then
		return 1
	end

	return 0
end

function NewFestivalDengLuWGData:IsEnoughNum()
	local draw_num = self:GetCanDrawNum()
	return draw_num >= 1
end

