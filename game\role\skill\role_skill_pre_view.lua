RoleSkillPreView = RoleSkillPreView or BaseClass(SafeBaseView)
local VideoPlayer = typeof(UnityEngine.Video.VideoPlayer)

function RoleSkillPreView:__init()
	self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(180/255)
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_pre_skill_show")
end

-- 设置初始的下标
function RoleSkillPreView:SetNowShowIndex(item_index)
    self.select_skill_index = item_index
end

function RoleSkillPreView:LoadCallBack()
    if not self.pre_skill_list then
        self.pre_skill_list = {}

        for i = 1, 4 do
            local obj_item = PreShowSkillItemRender.New(self.node_list[string.format("pre_skill_%d", i)])
            obj_item:SetIndex(i)
            obj_item:SetClickCallBack(BindTool.Bind(self.SelectSkillItemHandler,self))
            self.pre_skill_list[i] = obj_item
        end
    end

    self.video_player = self.node_list.skill_pre_rawimage:GetComponent(VideoPlayer) 
    self.node_list.video_progress.slider.onValueChanged:AddListener(BindTool.Bind(self.OnVideoProgerssChange, self))

    XUI.AddClickEventListener(self.node_list.btn_video_play, BindTool.Bind(self.OnClickSkillVideoPlay, self))
    XUI.AddClickEventListener(self.node_list.btn_video_pause, BindTool.Bind(self.OnClickSkillVideoPause, self))
    Runner.Instance:AddRunObj(self, 14)
end

-- 选中技能回调刷新界面
function RoleSkillPreView:SelectSkillItemHandler(item)
	if item == nil or nil == item.data then
		return
	end

	if self.select_skill_index == item.index then
		return
	end

    self.select_skill_index = item.index
	self.skill_data = item.data
    self:FlushCurrSkillVideo()
end

function RoleSkillPreView:ReleaseCallBack()
    if self.pre_skill_list and #self.pre_skill_list > 0 then
        for i, v in ipairs(self.pre_skill_list) do
            v:DeleteMe()
            v = nil
        end

        self.pre_skill_list = nil
    end

    self.select_skill_index = nil
    self.skill_data = nil
    self.is_playing = nil
    self.video_clips = nil
    Runner.Instance:RemoveRunObj(self)
end

-- 更新
function RoleSkillPreView:Update(now_time, elapse_time)
    if IsNil(self.video_player) or (not self.video_player.isPlaying) then
        return
    end

    self:FlushCurrPlayVideoProgress()
end

-- 刷新
function RoleSkillPreView:OnFlush(param_t)
    if self.select_skill_index == nil then
        self.select_skill_index = 1
    end

	local data = SkillWGData.Instance:GetSkillListByType(1)   -- 主动技能列表
	for i,v in ipairs(self.pre_skill_list) do
        v:SetVisible(data[i] ~= nil)

		if data[i] then
			v:SetData(data[i])
		end

        v:OnChangeHighLight(self.select_skill_index == i)
        if self.select_skill_index == i then
            self.skill_data = data[i]
        end
	end

    self:FlushCurrSkillVideo()
end

-- 刷新视频
function RoleSkillPreView:FlushCurrSkillVideo()
    if (self.select_skill_index == nil) or (self.skill_data == nil) then
        return
    end

    -- 准备更换视频
    if IsNil(self.video_player) then
        return
    end

    local play_video = function()
        if not IsNil(self.video_player) then
            -- 这里切换clip
            self.video_player.time = 0
            self.video_player:Play()
            self.is_playing = true
            self:FlushCurrPlayStatus()
        end
    end

    if self.video_clips == nil then
        self.video_clips = {}
    end

    if self.video_clips[self.skill_data.skill_id] then
        self.video_player.clip = self.video_clips[self.skill_data.skill_id]
        play_video()
    else
        local sex, prof = RoleWGData.Instance:GetRoleSexProf()
        local role_id = RoleWGData.Instance.GetJobModelId(sex, prof)
        local bundle, asset = ResPath.GetRoleSkillVideoPath(role_id, self.skill_data.skill_id)

        ResPoolMgr:GetVideoClip(bundle, asset, function (asset)
            if self.skill_data then
                if self.video_clips ~= nil then
                    self.video_clips[self.skill_data.skill_id] = asset
                end

                if not IsNil(self.video_player) then
                    self.video_player.clip = asset
                    play_video()
                end
            end
        end)
    end

    for i,v in ipairs(self.pre_skill_list) do
        v:OnChangeHighLight(self.select_skill_index == i)
	end
end

-- 更新进度
function RoleSkillPreView:FlushCurrPlayVideoProgress()
    if IsNil(self.video_player) then
        return
    end
    local curr_time = self.video_player.time
    local total_time = self.video_player.clip.length
    local progress = curr_time / total_time
    self.node_list.video_progress.slider.value = progress
end

-- 当前切换进度(播放中不能切换进度)
function RoleSkillPreView:OnVideoProgerssChange(value)
    if IsNil(self.video_player) or self.video_player.isPlaying then
        return
    end

    local total_time = self.video_player.clip.length
    self.video_player.time = total_time * value
end

-- 刷新播放按钮状态
function RoleSkillPreView:FlushCurrPlayStatus()
    self.node_list.btn_video_play:CustomSetActive(not self.is_playing)
    self.node_list.btn_video_pause:CustomSetActive(self.is_playing)
end
-----------------------------------------
-- 播放视频
function RoleSkillPreView:OnClickSkillVideoPlay()
    if IsNil(self.video_player) then
        return
    end

    self.video_player:Play()
    self.is_playing = true
    self:FlushCurrPlayStatus()
end

-- 暂停视频
function RoleSkillPreView:OnClickSkillVideoPause()
    if IsNil(self.video_player) then
        return
    end

    self.video_player:Pause()
    self.is_playing = false
    self:FlushCurrPlayStatus()
end
-----------------------------------------
-- 技能学习itemRender
-----------------------------------------
PreShowSkillItemRender = PreShowSkillItemRender or BaseClass(BaseRender)
function PreShowSkillItemRender:OnFlush()
	if self.data == nil then
		return
	end

    --图标
    local bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id))
    self.node_list.ph_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.ph_skill_icon.image:SetNativeSize()
    end)
	self.node_list.lbl_skill_name.text.text = self.data.skill_name
end

-- 设置高亮
function PreShowSkillItemRender:OnChangeHighLight(is_select)
	self.node_list.highlight:CustomSetActive(is_select)
    self.node_list.normal:CustomSetActive(is_select)
end