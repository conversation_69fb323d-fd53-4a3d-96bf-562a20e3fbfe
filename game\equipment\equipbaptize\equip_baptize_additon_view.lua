BaptizeAdditonView = BaptizeAdditonView or BaseClass(SafeBaseView)

function BaptizeAdditonView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/equipment_ui_prefab", "layout_equip_baptize_addition")
end

function BaptizeAdditonView:ReleaseCallBack()
    if self.act_list then
        for k, v in pairs(self.act_list) do
            v:DeleteMe()
        end
        self.act_list = nil
    end

    self.cur_equip_data = nil
end

function BaptizeAdditonView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Equip.BaptizeAdditonTitle
    --self:SetSecondView(nil, self.node_list["content"])

    self.act_list = {}
    for i = 1, 3 do
        self.act_list[i] = BaptizeAddAttrRender.New(self.node_list.act_list:FindObj("list_" .. i))
        self.act_list[i]:SetIndex(i)
    end
end

function BaptizeAdditonView:ShowIndexCallBack(index)
    self:Flush()
end

function BaptizeAdditonView:SetDataAndOpen(data)
    self.cur_equip_data = data
    self:Open()
end

function BaptizeAdditonView:OnFlush()
    self.node_list["equip_icon"]:SetActive(false)
    if IsEmptyTable(self.cur_equip_data) then
        return
    end
    self.node_list.equip_desc.text.text = Language.Equip.BaptizeAddActDesc
    local target_equip_part_index = self.cur_equip_data.index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    self.node_list.equip_name_text.text.text = Language.Equip.EquipName[target_equip_part_index]
    local bundle, asset = ResPath.GetEquipmentIcon("a2_zb_tb_" .. target_equip_part_index)
    if bundle and asset then
        self.node_list["equip_icon"].image:LoadSprite(bundle, asset, function ()
            self.node_list["equip_icon"]:SetActive(true)
            self.node_list["equip_icon"].image:SetNativeSize()
        end)
    end

	local attr_list = EquipmentWGData.Instance:GetEquipBaptizeAttrAddList(self.cur_equip_data.index)

    for k, v in pairs(self.act_list) do
        v:SetData(attr_list[k])
    end
end

--------------------------------------------------------------------------------
BaptizeAddAttrRender = BaptizeAddAttrRender or BaseClass(BaseRender)

function BaptizeAddAttrRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
    else
        self.view:SetActive(true)

		local color = self.data.is_act and COLOR3B.GREEN or "#AAC6DB"
		--local color_str = Language.Common.ColorName[self.data.grade] or Language.Common.ColorName[0]
		--local need_desc = string.format(Language.Equipment.BaptizeActDesc2, GameEnum.EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM, color_str)
        local need_desc = string.format(Language.Equipment.BaptizeActDesc3, Language.Equipment.BaptizeGradeName[self.data.highest_grade])

		local attr_str = ""
        --[[
		for i = 1, 2 do
			local type = self.data["attr_type" .. i]
			local value = self.data["attr_val" .. i]
			if value and value > 0 then
				local name = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
				name = DeleteStrSpace(name)
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
				value = is_per and (value * 0.01 .. "%") or value
				local attr_desc = name .. " +" .. value
				local huan_hang = 1 < i and "\n" or ""
				attr_str = attr_str .. huan_hang .. attr_desc
			end
		end
        ]]
        local type = self.data.attr_type1
        local value = self.data.attr_val1
        if value and value > 0 then
            local name = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
            name = DeleteStrSpace(name)
            local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
            value = is_per and (value * 0.01 .. "%") or value
            attr_str = name .. " +" .. value
        end
		self.node_list.need_desc.text.text = ToColorStr(need_desc, color)
		self.node_list.act_attr_desc.text.text = ToColorStr(attr_str, color)
    end
end
