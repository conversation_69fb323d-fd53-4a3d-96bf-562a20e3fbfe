EquipmentIntegrationUpgradeView = EquipmentIntegrationUpgradeView or BaseClass(SafeBaseView)
function EquipmentIntegrationUpgradeView:__init()
	self:SetMaskBg(true)
    self.suit_data = nil
	self:LoadConfig()
end

function EquipmentIntegrationUpgradeView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
	self:AddViewResource(0, "uis/view/rolebag_ui/equip_target_prefab", "layout_ei_upgrade_tips")
end

function EquipmentIntegrationUpgradeView:LoadCallBack()
    self.is_auto_level = false

	self.node_list.title_view_name.text.text = Language.EquipTarget.Title1

    if self.ei_cur_level_attr_list == nil then
        self.ei_cur_level_attr_list = {}
        local node_num = self.node_list["ei_cur_level_attr_list"].transform.childCount
        for i = 1, node_num do
            self.ei_cur_level_attr_list[i] = CommonAttrRender.New(self.node_list["ei_cur_level_attr_list"]:FindObj("attr_" .. i))
            self.ei_cur_level_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    if self.ei_next_level_attr_list == nil then
        self.ei_next_level_attr_list = {}
        local node_num = self.node_list["ei_next_level_attr_list"].transform.childCount
        for i = 1, node_num do
            self.ei_next_level_attr_list[i] = CommonAttrRender.New(self.node_list["ei_next_level_attr_list"]:FindObj("attr_" .. i))
            self.ei_next_level_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    XUI.AddClickEventListener(self.node_list["ei_auto_level_btn"], BindTool.Bind(BindTool.Bind(self.AutoUpLevel, self)))

    self.ei_suit_level_stuff_item = ItemCell.New(self.node_list["ei_suit_level_stuff_item"])
end

function EquipmentIntegrationUpgradeView:ReleaseCallBack()
    if self.ei_cur_level_attr_list then
        for k, v in pairs(self.ei_cur_level_attr_list) do
            v:DeleteMe()
        end
        self.ei_cur_level_attr_list = nil
    end

    if self.ei_next_level_attr_list then
        for k, v in pairs(self.ei_next_level_attr_list) do
            v:DeleteMe()
        end
        self.ei_next_level_attr_list = nil
    end

    if self.ei_suit_level_stuff_item then
        self.ei_suit_level_stuff_item:DeleteMe()
        self.ei_suit_level_stuff_item = nil
    end

    self:CancelAutoLevelTimer()
end

function EquipmentIntegrationUpgradeView:ShowIndexCallBack()
end

function EquipmentIntegrationUpgradeView:SetData(suit_data)
	self.suit_data = suit_data
end

function EquipmentIntegrationUpgradeView:OnFlush()
    if IsEmptyTable(self.suit_data) then return end

    local suit_index = self.suit_data.type
    local attr_list = EquipTargetWGData.Instance:GetEISuitActAttrList(suit_index)
    local suit_level = EquipTargetWGData.Instance:GetSuitLevel(suit_index)

    local cur_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level + 1)

    local attr_cur_level_list, attr_next_level_list  = EquipTargetWGData.Instance:GetEISuitCurAndNextLevelAttrList(attr_list, suit_index, suit_level)
    for k,v in pairs(self.ei_cur_level_attr_list) do
        v:SetData(attr_cur_level_list[k])
    end

    for k,v in pairs(self.ei_next_level_attr_list) do
        v:SetData(attr_next_level_list[k])
    end

    local is_remind = false
    if next_level_cfg then
        local item_data = {item_id = cur_level_cfg.cost_item_id}
        self.ei_suit_level_stuff_item:SetShowCualityBg(false)
        self.ei_suit_level_stuff_item:SetCellBgEnabled(false)
        self.ei_suit_level_stuff_item:SetData(item_data)
        self.ei_suit_level_stuff_item:SetEffectRootEnable(false)

        local num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        is_remind = num >= cur_level_cfg.cost_item_num
        local str_color = is_remind and COLOR3B.DEFAULT_NUM or COLOR3B.RED
        self.node_list["ei_level_need_num"].text.text = string.format("%s/%s", ToColorStr(num, str_color), cur_level_cfg.cost_item_num)
        RectTransform.SetAnchoredPositionXY(self.node_list.bg_1.rect, -177, 85)
    else
        RectTransform.SetAnchoredPositionXY(self.node_list.bg_1.rect, 0, 85)
    end

    self.node_list.max_flag:SetActive(IsEmptyTable(next_level_cfg))
    self.node_list.arrow_right:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.bg_2:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.level_cost:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.ei_level_need_num:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.ei_auto_level_btn:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.remind:SetActive(is_remind)

    self.node_list.lbl_name_1.text.text = string.format(Language.EquipTarget.LevelStr1, suit_level, self.suit_data.name)
    self.node_list.lbl_name_2.text.text = string.format(Language.EquipTarget.LevelStr1, suit_level + 1, self.suit_data.name)

    local bundle, asset = ResPath.GetRoleBagImg("a3_xjgy_tx" .. self.suit_data.bg)
	if self.node_list.icon_1 then
		self.node_list["icon_1"].image:LoadSprite(bundle, asset, function()
			self.node_list["icon_1"].image:SetNativeSize()
		end)
	end

    if self.node_list.icon_1 then
		self.node_list["icon_2"].image:LoadSprite(bundle, asset, function()
			self.node_list["icon_2"].image:SetNativeSize()
		end)
	end
end

function EquipmentIntegrationUpgradeView:AutoUpLevel()
	if self:IsAutoUpLevel() then --正在自动升级则取消强化
		self:StopLevelOperator()
		return
	end

	self:OnBtnUpLevel(1, true)
end

function EquipmentIntegrationUpgradeView:IsAutoUpLevel()
	return self.is_auto_level
end

function EquipmentIntegrationUpgradeView:StopLevelOperator()
	self:SetLevelButtonEnabled(false)
end

-- 使用特效
function EquipmentIntegrationUpgradeView:PlayEiUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
	parent_node = self.node_list["ei_effect_pos"]})
end

-- 设置强化按钮是否可用
function EquipmentIntegrationUpgradeView:SetLevelButtonEnabled(enabled)
	self.is_auto_level = enabled
	self:SetAutoLevelBtnNameStr(not enabled and Language.EquipTarget.AutoUpLevel or Language.EquipTarget.UpLevelBtnStop)
	if false == enabled then
		self:CancelAutoLevelTimer()
	end
end

-- 自动升级操作
function EquipmentIntegrationUpgradeView:AutoUpLevelUpOnce()
	self:CancelAutoLevelTimer()

    if self.suit_data == nil then
        return
    end

	if self:IsAutoUpLevel() then
		self.auto_level_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnBtnUpLevel, self, 1, true), 0.5)
	end
end

--取消自动升级倒计时
function EquipmentIntegrationUpgradeView:CancelAutoLevelTimer( )
	if nil ~= self.auto_level_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_level_timer_quest)
		self.auto_level_timer_quest = nil
	end
end

function EquipmentIntegrationUpgradeView:SetAutoLevelBtnNameStr(strength_btn_str)
	if self.node_list["ei_auto_level_text"] then
		self.node_list["ei_auto_level_text"].text.text = strength_btn_str
	end
end

function EquipmentIntegrationUpgradeView:OnBtnUpLevel(one_key, is_auto)
    if self.suit_data == nil then
        return
    end

    local suit_index = self.suit_data.type
    local suit_level = EquipTargetWGData.Instance:GetSuitLevel(suit_index)
    local cur_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level + 1)
	if next_level_cfg and cur_level_cfg then
		local cost_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		if cost_num >= cur_level_cfg.cost_item_num then
			EquipTargetWGCtrl.Instance:SendEIOperateReq(EQUIP_COLLECT_OPERATE_TYPE.SUIT_UPLEVEL, suit_index)
			if is_auto then
				self:SetLevelButtonEnabled(true)
			end
		else
			self:StopLevelOperator()
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_level_cfg.cost_item_id})
		end
	else
		self:StopLevelOperator()
	end
end