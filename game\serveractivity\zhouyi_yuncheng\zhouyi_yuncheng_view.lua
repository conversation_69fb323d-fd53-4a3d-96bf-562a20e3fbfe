
<PERSON>YiYunChengView = ZhouYiYunChengView or BaseClass(SafeBaseView)
function ZhouYiYunChengView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/zhouyi_yuncheng_prefab", "layout_zhouyi_yuncheng")
	self.cur_result_index = 1
end

function ZhouYiYunChengView:__delete()

end

function ZhouYiYunChengView:ReleaseCallBack()
	if self.show_item then
		for k,v in pairs(self.show_item) do
			v:DeleteMe()
		end
		self.show_item = nil
	end

	if self.world_record_list then
		self.world_record_list:DeleteMe()
		self.world_record_list = nil
	end

	-- if self.personal_record_list then
	-- 	self.personal_record_list:DeleteMe()
	-- 	self.personal_record_list = nil
	-- end

	if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
	self:CancleHLQuest()


	self.is_turning = false
end

function ZhouYiYunChengView:OpenCallBack()
	--
end

function ZhouYiYunChengView:LoadCallBack()
	self.show_item = {}
	for i= 1, 8 do
		self.show_item[i] = ItemCell.New(self.node_list["item_pos"..i])
	end
	if nil == self.world_record_list then
		self.world_record_list = AsyncListView.New(YunChengListCell, self.node_list.record_list1)
	end

	-- if nil == self.personal_record_list then
	-- 	self.personal_record_list = AsyncListView.New(YunChengListCell, self.node_list.record_list2)
	-- end

	if nil == self.task_list then
		self.task_list = AsyncListView.New(YunChengTaskCell, self.node_list.task_list)
	end

	self.node_list["jump_ani"].button:AddClickListener(BindTool.Bind(self.OnClickJump, self))
	self.node_list["change"].button:AddClickListener(BindTool.Bind(self.OnClickChangeRecord, self))
	self.node_list["roll_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRoll, self))
	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind(self.OnClickClose, self))

	for i = 1 ,2 do
		self.node_list["toggle_btn"..i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickRecordChange,self,i))
	end

end

function ZhouYiYunChengView:ShowIndexCallBack()
	self:ViewAnimation()
end

function ZhouYiYunChengView:ViewAnimation()
	local tween_info = UITween_CONSTS.ZhouYiCarnival
	local tween_root_1 = self.node_list.content
	UITween.CleanAllTween(GuideModuleName.ZhouYiYunCheng)
	UITween.FakeHideShow(tween_root_1)
	RectTransform.SetAnchoredPositionXY(self.node_list.muban_l.rect, -900, -40)
	RectTransform.SetAnchoredPositionXY(self.node_list.muban_r.rect, 1000, -40)

	self.node_list.muban_l.rect:DOAnchorPos(Vector2(-406, -40), tween_info.MoveTime)
	self.node_list.muban_r.rect:DOAnchorPos(Vector2(473, -40), tween_info.MoveTime)

	ReDelayCall(self, function()
		UITween.AlphaShow(GuideModuleName.ZhouYiYunCheng,tween_root_1, 0, 1, tween_info.AlphaTime)
	end, tween_info.MoveTime, "zhou_yi_carnival_1")
end

function ZhouYiYunChengView:OnClickJump()
	ZhouYiYunChengWGData.Instance:SetJumpAni()
	self:FlushAniStatus()
end

function ZhouYiYunChengView:FlushAniStatus()
	self.is_jump_ani = ZhouYiYunChengWGData.Instance:GetJumpAni()
	self.node_list.jump_toggle:SetActive(self.is_jump_ani)
end

function ZhouYiYunChengView:OnClickChangeRecord()
	if self.is_turning then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.IsWorkIng)
		return
	end
	ZhouYiYunChengWGCtrl.Instance:OpenRewardSelectView()
end

function ZhouYiYunChengView:OnClickRoll()
	local had_times = ZhouYiYunChengWGData.Instance:GetLeftRollTimes()
	if had_times <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.LackOfTimes)
		return
	end

	if not ZhouYiYunChengWGData.Instance:IsAvalibaleTime() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.TimeNotAvalibale)
		return
	end

	if self.is_turning then return end
	self.is_turning = true
	ZhouYiYunChengWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_DRAW)
end

function ZhouYiYunChengView:OnClickClose()
	self:Close()
end

function ZhouYiYunChengView:PlayRollAnimal()
	local item_cfg,index = ZhouYiYunChengWGData.Instance:GetRollResult()
	if index <= 0 then
		self.is_turning = false
		return
	end
	local time = 5

	if self.is_jump_ani then
		time = 0
	end

	self.tween = self.node_list.roll_father.transform:DORotate(Vector3(0, 0, -360 * time - 45 * (index - 1)),time,
		DG.Tweening.RotateMode.FastBeyond360)

	self.tween:SetEase(DG.Tweening.Ease.OutQuart)
	self.tween:OnComplete(function()
		self.is_turning = false
		self:ShowGetItemView(item_cfg)
		self:CancleHLQuest()
		self:ShowRollHightLight()
	end)

	self:CancleHLQuest()

	if self.is_jump_ani then
		return
	end

	self.show_hl_quest = GlobalTimerQuest:AddRunQuest(function ()
		self:ShowRollHightLight()
	end,0.02)
	self:ShowRollHightLight()
end


function ZhouYiYunChengView:CancleHLQuest()
	if self.show_hl_quest then
		GlobalTimerQuest:CancelQuest(self.show_hl_quest)
		self.show_hl_quest = nil
	end
end


function ZhouYiYunChengView:ShowRollHightLight()
	local father_rotation = self.node_list.roll_father.transform.localEulerAngles.z
	local index = (8 - math.floor((father_rotation + 22.5) / 45)) % 8 + 1
	for i =1,8 do
		self.node_list["highlight"..i]:SetActive(i == index)
	end
end

function ZhouYiYunChengView:OnFlush()
	if ZhouYiYunChengWGData.Instance:CheckNeedReqInfo() then return end
	self.node_list["rule_text"].text.text = Language.ZhouYiYunCheng.RuleDesc
	local have_times,need_times = ZhouYiYunChengWGData.Instance:GetLeftRollTimes()
	local boun_times = ZhouYiYunChengWGData.Instance:GetBonusTimes()
	need_times = boun_times - need_times
	local str = ToColorStr(need_times,"#1dff52ff")
	self.node_list["times"].text.text = have_times
	self.node_list["desc"].text.text = string.format(Language.ZhouYiYunCheng.Need_times,str)
	self:FlushAniStatus()
	local all_rewared_list = ZhouYiYunChengWGData.Instance:GetAllSelectItemList()

	if not IsEmptyTable(all_rewared_list) then
		for i = 1, 8 do
			if all_rewared_list[i] then
				local exchange_data = {}
				exchange_data.item_id = tonumber(all_rewared_list[i].item_id)
				exchange_data.num = tonumber(all_rewared_list[i].num)
				exchange_data.is_bind = tonumber(all_rewared_list[i].is_bind)
				self.show_item[i]:SetData(exchange_data)
			end
		end
	end
	self:FlushWorld()
	self:FlushPersonal()
	self:FlushXianLingTask()
	if ZhouYiYunChengWGData.Instance:GetLeftRollTimes() > 0 and ZhouYiYunChengWGData.Instance:IsAvalibaleTime() then
		self.node_list["effectpos"]:SetActive(true)
	else
		self.node_list["effectpos"]:SetActive(false)
	end
end

function ZhouYiYunChengView:OnClickRecordChange(index,is_on)
	if is_on then
		if index == 1 and self.cur_result_index ~= index then
			self.cur_result_index = index
			self:FlushWorld()
		elseif index == 2 and self.cur_result_index ~= index then
			self.cur_result_index = index
			self:FlushPersonal()
		end
	end
end

function ZhouYiYunChengView:FlushWorld()
	if self.cur_result_index ~= 1 then return end
	local world_data = ZhouYiYunChengWGData.Instance:GetWorldRecord()
	self:SetInfoBg(world_data)
	if nil == self.world_record_list then return end
	self.world_record_list:SetDataList(world_data)
end

function ZhouYiYunChengView:FlushPersonal()
	if self.cur_result_index ~= 2 then return end
	local personal_data = ZhouYiYunChengWGData.Instance:GetPersonalRecord()
	self:SetInfoBg(personal_data)
	if nil == self.world_record_list then return end
	self.world_record_list:SetDataList(personal_data)
end

function ZhouYiYunChengView:SetInfoBg(data)
	local is_have_info = not IsEmptyTable(data)
	self.node_list.not_info:SetActive(not is_have_info)
end

function ZhouYiYunChengView:FlushXianLingTask()
	local all_task_data = ZhouYiYunChengWGData.Instance:GetAllTaskDetail()
	self.task_list:SetDataList(all_task_data)
end

function ZhouYiYunChengView:ShowGetItemView(cfg)
	-- print_error("ShowGetItemView",item_cfg)
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(tonumber(cfg.item_id))
	-- local str = string.format(Language.Bag.GetItemTxt, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), tomunber(item_cfg.num))
	-- TipWGCtrl.Instance:ShowSystemMsg(str)
end


YunChengListCell = YunChengListCell or BaseClass(BaseRender)

function YunChengListCell:__init()
end

function YunChengListCell:__delete()
end

function YunChengListCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		local rewared_level = ZhouYiYunChengWGData.Instance:GetRewaredLevel(self.data.item_id)
		local rewared_name = Language.ZhouYiYunCheng.ReWaredLevel[rewared_level]
		if self.data.draw_role then

			if rewared_name then
				self.node_list["contant"].text.text = self.data.draw_role..Language.ZhouYiYunCheng.Congratulations1..rewared_name..ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			else
				self.node_list["contant"].text.text = self.data.draw_role..Language.ZhouYiYunCheng.Congratulations1..ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			end
		else
			local time_tab = os.date("*t", self.data.draw_timestamp)
			self.node_list["contant"].text.text = Language.ZhouYiYunCheng.Congratulations2..rewared_name..ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		end

	else
		self.node_list["contant"].text.text = ""
	end
end


YunChengTaskCell = YunChengTaskCell or BaseClass(BaseRender)

function YunChengTaskCell:__init()
	self.node_list["gobutton"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
end

function YunChengTaskCell:__delete()
end

function YunChengTaskCell:OnFlush()
	if not IsEmptyTable(self.data) then
		if self.data.task_type == 1 then
			local have_param = BiZuoWGData.Instance:GetTotalExp()
			local need_param = self.data.complete_param
			local color = have_param >= need_param and "#20950DFF" or COLOR3B.RED
			local str = " ("..ToColorStr(have_param,color).."/"..need_param..")"
			self.node_list["task_desc"].text.text = self.data.des..str
		else
			self.node_list["task_desc"].text.text = self.data.des
		end

		self.node_list["have_get"]:SetActive(false)
		self.node_list.gobutton:SetActive(true)
		self.node_list["red"]:SetActive(false)
		if not self.data.finish_flag and not self.data.get_flag then
			self.node_list["btn_text"].text.text = Language.ZhouYiYunCheng.BtnText
		elseif not self.data.get_flag then
			self.node_list["btn_text"].text.text = Language.ZhouYiYunCheng.BtnText2
			self.node_list["red"]:SetActive(true)
		elseif self.data.get_flag then
			self.node_list["btn_text"].text.text = Language.ZhouYiYunCheng.BtnText3
			self.node_list["have_get"]:SetActive(true)
			self.node_list.gobutton:SetActive(false)
		end
	else

	end
end

function YunChengTaskCell:OnClickGo()
	if self.data.finish_flag and not self.data.get_flag then
		ZhouYiYunChengWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_FETCH_DRAW_TIMES,self.data.task_type,0,0,0)
		return
	elseif not self.data.finish_flag then
		if self.data.task_type == 1 then
			ViewManager.Instance:Open(GuideModuleName.BiZuo,TabIndex.bizuo_bizuo)
		elseif self.data.task_type == 2 then
			ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_cz)
		elseif self.data.task_type == 3 then
		end
	end
end
