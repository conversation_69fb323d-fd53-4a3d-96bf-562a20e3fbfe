MarryObj = MarryObj or BaseClass(Character)
local point_t = {
	"couple_01",
	"couple_02",
}
local count = 0
local MARRY_ROLE_ID = 0
local MARRY_LOVER_ID = 0

function MarryObj:__init(marry_vo)
	self.marry_time = 0
	self.obj_type = SceneObjType.MarryObj
	self.draw_obj:SetObjType(self.obj_type)
	self:SetObjId(marry_vo.obj_id)
	self.vo = marry_vo
	self.is_first = false
	self.need_to_change_commonstate = true
	self.effect = {}
	self.effect_delay = {}
	self.is_init_name = false
end

function MarryObj:__delete()
	if ActivityWGData.Instance then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
		if MarryWGData.Instance and activity_info and activity_info.status ~= ACTIVITY_STATUS.XUNYOU then
			GlobalTimerQuest:AddDelayTimer(function()
				local vo = Scene.Instance:GetObjByUId(MARRY_ROLE_ID)
				local lover_vo = Scene.Instance:GetObjByUId(MARRY_LOVER_ID)
				if vo and vo.SetMarryFlag then
					vo:SetMarryFlag(0)
					if vo:IsMainRole() and not IsNil(MainCameraFollow) then
						MainCameraFollow.Target = vo:GetRoot().transform
					end
				end

				if lover_vo and lover_vo.SetMarryFlag then
					lover_vo:SetMarryFlag(0)
					if lover_vo:IsMainRole() and not IsNil(MainCameraFollow) then
						MainCameraFollow.Target = lover_vo:GetRoot().transform
					end
				end
				MARRY_ROLE_ID = 0
				MARRY_LOVER_ID = 0
			end, 0)
		end
    end
    if self.couple_loader then
        self.couple_loader:Destroy()
    end
	if self.vo.marry_seq == CruiseType.HuaJiao and not IsEmptyTable(self.couple) then
		for _,v in pairs(self.couple) do
			v = nil
		end
	end

	if self.follow_ui then
		self.follow_ui:DeleteMe()
		self.follow_ui = nil
	end

	if not IsEmptyTable(self.effect_delay) then
		for k,v in pairs(self.effect_delay) do
			GlobalTimerQuest:CancelQuest(v)
			self.effect_delay[k] = nil
		end
		self.effect_delay = nil
	end

	if not IsEmptyTable(self.effect) then
		for k,v in pairs(self.effect) do
			v:Destroy()
			v:DeleteMe()
			self.effect[k] = nil
		end
		self.effect = nil
	end

	self.is_first = false
	self.is_init_name = false
end

function MarryObj:InitAppearance()
	local cfg = MarryWGData.Instance:GetQingyuanCfg().hunyan_xunyou_duiwu
	self.res_id = cfg[self.vo.marry_seq + 1].show_model or 0
    self.name = self.res_id
	self:SetLogicPos(self.vo.pos_x, self.vo.pos_y)

	if nil == self.res_id or self.res_id == 0 then
		return
	end

	local hunyan_info = MarryWGData.Instance:GetCurWeddingInfo()
	MARRY_ROLE_ID = hunyan_info.role_id
	MARRY_LOVER_ID = hunyan_info.lover_role_id
	self.couple = {}
	local bundle_name, asset_name = ResPath.GetNpcModel(self.res_id)
	self:ChangeModel(SceneObjPart.Main, bundle_name, asset_name,
		function (obj)
			if nil == obj then
				return
			end

            if self.vo.marry_seq == CruiseType.HuaJiao then --挂两个人
                for i = 1, 2 do
                local sex, prof,res_id
                if i > 1 then
                    sex = GameEnum.MALE
                    if hunyan_info.role_sex == GameEnum.FEMALE then	
                        prof = hunyan_info.lover_role_prof
                    else
                        prof = hunyan_info.role_prof
                    end
                    res_id = 120103901
                else
                    sex = GameEnum.FEMALE
                    if hunyan_info.role_sex == GameEnum.FEMALE then	
                        prof = hunyan_info.role_prof
                    else
                        prof = hunyan_info.lover_role_prof
                    end
                    res_id = 320103901
                end
                local bundle_name, asset_name = ResPath.GetRoleXunyouModel(res_id)
                local obj_guadian = obj.transform:FindByName(point_t[i])
                self.couple_loader = AllocAsyncLoader(self, "marry_couple_" .. asset_name .. "_" .. i)
                self.couple_loader:SetIsUseObjPool(true)
                self.couple_loader:SetParent(obj_guadian)
                self.couple_loader:Load(bundle_name, asset_name, function (couple_obj)
                    if IsNil(couple_obj) then
                        self.couple_loader:Destroy()
                        return
                    end
                    --print_error("i",i,res_id,sex)
                    if not self:IsDeleted() then
                        local children = couple_obj.gameObject:GetComponentsInChildren(typeof(UnityEngine.Animator))
                        for i = 0, children.Length - 1 do
                            children[i]:CrossFadeInFixedTime(SceneObjAnimator.Mount_Jiehun, 0.2)
                        end
                    end
                    self.couple[i] = couple_obj end)
				end
			end

			if self.vo.distance > 0.1 then
				self:DoMove(math.floor(self.vo.pos_x + math.cos(self.vo.dir) * self.vo.distance), math.floor(self.vo.pos_y + math.sin(self.vo.dir) * self.vo.distance))
			end

			if self.vo.marry_seq == CruiseType.HuaTong then
				local part = self.draw_obj:GetPart(SceneObjPart.Main)
				local main_part_obj = part:GetObj()
				if main_part_obj then
					local children = main_part_obj.gameObject:GetComponentsInChildren(typeof(UnityEngine.Animator))
					for i = 0, children.Length - 1 do
						children[i]:ListenEvent("Sahua", BindTool.Bind(self.SahuaEffect, self, i + 1))
					end
				end
			end

			self:InitNameInfo()
		end)
end

function MarryObj:InitNameInfo()
	if self:IsDeleted() or self.is_init_name then
		return
	end

	local hunyan_info = MarryWGData.Instance:GetCurWeddingInfo()
	if hunyan_info.role_prof and hunyan_info.lover_role_prof then
		if self.vo.marry_seq == CruiseType.HuaJiao then
			if hunyan_info.role_sex == GameEnum.FEMALE then	
                self.vo.name = hunyan_info.role_name 
                self.vo.lover_name = hunyan_info.lover_role_name
			else
				self.vo.name = hunyan_info.lover_role_name
                self.vo.lover_name = hunyan_info.role_name
			end
        end
        -- print_error("InitNameInfo",self.vo.marry_seq,self.name,self.vo.name,self.vo.lover_name)
		if self.vo.name ~= "" then
			self:CreateFollowUi()
            self.follow_ui:SetHpVisiable(false)
		end
	end
	if self.follow_ui ~= nil and self.vo.name ~= "" then
		local draw_obj = self.draw_obj:GetRoot().transform
        self.follow_ui:SetName(self.vo.name or "", self)
        self.follow_ui:SetXunYouLoverName(self.vo.lover_name or "", self)
		self.follow_ui:SetFollowTarget(self.draw_obj:GetAttachPoint(AttachPoint.UI) or draw_obj, self.draw_obj:GetName())
		if self.vo.marry_seq == CruiseType.HuaJiao then
			self.follow_ui:SetLocalUI(0, 20, 0)
		end

		self.follow_ui:ForceSetVisible(true)
		self.is_init_name = true
	end
end

function MarryObj:CreateFollowUi()
    self.follow_ui = XunYouFollow.New(self.vo)
    self.follow_ui:Create(SceneObjType.MarryObj)
	self.follow_ui:SetOwnerObj(self)
	self.follow_ui:OnEnterScene(self.is_enter_scene)
    self:UpdateFollowUIRule()
    self.follow_ui:ForceSetVisible(false)
end

function MarryObj:SahuaEffect(i)
	if not self.draw_obj then
		return
	end

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if nil == main_part or nil == main_part:GetObj() then
		return
	end

	local main_obj = main_part:GetObj()
	local position = main_obj.transform.position
	local bundle, asset = ResPath.GetEnvironmentEffect("sahua2")
	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(position.x, position.y, position.z))
end

function MarryObj:RemoveEffect(i)
	if self.effect[i] ~= nil then
		self.effect[i]:Destroy()
		self.effect[i]:DeleteMe()
		self.effect[i] = nil
	end
end

function MarryObj:RemoveEffectDelay(i)
	if self.effect_delay[i] ~= nil then
		GlobalTimerQuest:CancelQuest(self.effect_delay[i])
		self.effect_delay[i] = nil
	end
end

function MarryObj:IsMarryObj()
	return true
end

function MarryObj:OnEnterScene()
    SceneObj.OnEnterScene(self)
end

function MarryObj:DoMove(pos_x, pos_y)
	Character.DoMove(self, pos_x, pos_y)
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	local main_part_obj = part:GetObj()
	if main_part_obj then
		local children = main_part_obj.gameObject:GetComponentsInChildren(typeof(UnityEngine.Animator))
		for i = 0, children.Length - 1 do
			children[i]:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Run)
		end
	end
end

function MarryObj:Update(now_time, elapse_time)
	Character.Update(self, now_time, elapse_time)
	if self.marry_time <= 0 then
		self.marry_time = Status.NowTime + 1
	end
	if self.marry_time > 0 and now_time >= self.marry_time then
		if not self:IsMove() and not self.is_first then
			self.is_first = true
			if self.vo.distance > 0.1 then
				self:DoMove(math.floor(self.vo.pos_x + math.cos(self.vo.dir) * self.vo.distance), math.floor(self.vo.pos_y + math.sin(self.vo.dir) * self.vo.distance))
			end
		end

		self.marry_time = 0
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
		if activity_info and activity_info.status and activity_info.status ~= ACTIVITY_STATUS.XUNYOU then
			Scene.Instance:DeleteObj(self.vo.obj_id, 0)
		end
	end
end