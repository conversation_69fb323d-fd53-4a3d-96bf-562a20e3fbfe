TianShen3v3SceneLogic = TianShen3v3SceneLogic or BaseClass(CrossServerSceneLogic)

function TianShen3v3SceneLogic:__init()
	self.enter_time = 0
end

function TianShen3v3SceneLogic:__delete()
	self.enter_time = nil
end

-- 进入场景
function TianShen3v3SceneLogic:Enter(old_scene_type, new_scene_type)--20 110
	self.enter_time = TimeWGCtrl.Instance:GetServerTime()
	ViewManager.Instance:CloseAll()
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3SceneView)
    MainuiWGCtrl.Instance:SetOtherContents(true)
    MainuiWGCtrl.Instance:SetTaskContents(false)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
	MainuiWGCtrl.Instance:SetLimitGiftBtnTransActive(false)

	self.main_role_enter_event = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnMainRoleEnter, self))
	self.guaji_btn_click_event = GlobalEventSystem:Bind(OtherEventType.HAND_CLICK_GUAJI_BTN, BindTool.Bind(self.OnClickGuajiBtn, self))
	self.move_by_click_event = GlobalEventSystem:Bind(OtherEventType.MOVE_BY_CLICK, BindTool.Bind(self.OnMoveByClick, self))
	self.relive_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_REALIVE, BindTool.Bind1(self.OnMainRoleRelive, self))
	self.move_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.OnRoleMove, self))
	--下移对方玩家头像血条
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, -100)
	
	-- 设置摄像机角度
	self:TryChangeCameraAngle()

	-- 刷新障碍区
	TianShen3v3WGCtrl.Instance:FlushSceneBlock()

	TianShen3v3WGData.Instance:SetIsAutoFight(true)

	self.last_occupy_side_list = {}
	self:FlushPointGather()
end

local last_run_time = 0
function TianShen3v3SceneLogic:Update(now_time, elapse_time)
	if now_time - last_run_time > 1 then
		self:Think()
		last_run_time = now_time
	end
end

function TianShen3v3SceneLogic:Out(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Out(self)
	self.enter_time = 0
	ViewManager.Instance:Close(GuideModuleName.TianShen3v3SceneView)
	ViewManager.Instance:Close(GuideModuleName.TianShen3v3EndCountdownView)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetLimitGiftBtnTransActive(true)
	if old_scene_type ~= new_scene_type then
		-- KF3V3WGCtrl.Instance:CancelChangeGhost()
		-- KF3V3WGCtrl.Instance:CloseLogicView()
		-- MainuiWGCtrl.Instance:SetTaskRootNodeActive(true)
		-- MainuiWGCtrl.Instance:SetTransparentMaskActive(false)
		MainuiWGCtrl.Instance:UpdateMainRoleHp()
	end

    if self.main_role_enter_event then
        GlobalEventSystem:UnBind(self.main_role_enter_event)
        self.main_role_enter_event = nil
    end

    if self.move_event then
        GlobalEventSystem:UnBind(self.move_event)
        self.move_event = nil
    end

    if self.guaji_btn_click_event then
        GlobalEventSystem:UnBind(self.guaji_btn_click_event)
        self.guaji_btn_click_event = nil
    end

   	if self.move_by_click_event then
   		GlobalEventSystem:UnBind(self.move_by_click_event)
   		self.move_by_click_event = nil
  	end

   	if self.relive_event then
   		GlobalEventSystem:UnBind(self.relive_event)
   		self.relive_event = nil
  	end

    --复原对方玩家头像血条
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)
end

-- 刷新占领点采集物
function TianShen3v3SceneLogic:FlushPointGather()
	local point_cfg = TianShen3v3WGData.Instance:GetPointCfg()
	for index, v in ipairs(point_cfg) do
		local gather_obj = Scene.Instance:GetGatherByGatherId(v.gather_id)
		local point_info = TianShen3v3WGData.Instance:GetOccupyPointInfoByIndex(v.index)
		if gather_obj and point_info then
			local occupy_side = point_info.occupy_side
			local draw_obj = gather_obj:GetDrawObj()
			if draw_obj then
				local part = draw_obj:GetPart(SceneObjPart.Main)
				if part then
					part:SetInteger("side", occupy_side)
					if part:GetObj() then
						local model = part:GetObj().transform:Find("model")
						if not IsNil(model) then
							local animator = model.gameObject:GetComponent(typeof(UnityEngine.Animator))
							if not IsNil(animator) then
								local last_occupy_side = self.last_occupy_side_list[index]
								if last_occupy_side and last_occupy_side >= 0 and last_occupy_side ~= occupy_side then
									animator:SetTrigger("tri_shankai")
								end
								animator:SetBool("is_shankai", occupy_side ~= -1)
								self.last_occupy_side_list[index] = occupy_side
							end

							for i = -1, TS3V3_SIDE_MAX do
								local obj = model:Find("side_" .. i .. "_renderer")
								if not IsNil(obj) then
									obj.gameObject:SetActive(occupy_side == i)
								end
							end
						end
					end
				end
			end
		end
	end
end

function TianShen3v3SceneLogic:Think()
	if not self:GetSceneLogicMoveState() then
		return
	end
	if TianShen3v3WGData.Instance:GetIsAutoFight() then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local in_point, point_cfg = TianShen3v3WGData.Instance:InPoint(x, y)
		local in_unoccupation_point = in_point and TianShen3v3WGData.Instance:GetOccupyPointSide(point_cfg.index) ~= TianShen3v3WGData.Instance:GetMySide()
		-- 判断是否在未占领的点上
		if in_unoccupation_point then
			local has_enemy, enemy = self:NearbyHasEnemyRole()
			if has_enemy then
				local enemy_x, enemy_y = enemy:GetLogicPos()
				if TianShen3v3WGData.Instance:InSinglePoint(enemy_x, enemy_y, point_cfg.index) then
					self:ToAtkRole(enemy)
				end
			end
		else
			local has_enemy, enemy = self:NearbyHasEnemyRole()
			if has_enemy then
				self:ToAtkRole(enemy)
			else
				self:MoveToNearestPoint()
			end
		end
	end
end

local fight_distance = 30
-- 附近是否有敌人
function TianShen3v3SceneLogic:NearbyHasEnemyRole()
	local role_list = Scene.Instance:GetRoleList()
	local main_role = Scene.Instance:GetMainRole()
	for _, v in pairs(role_list) do
		if self:IsEnemy(v, main_role) and self:CalDistance(main_role, v) < fight_distance then
			return true, v
		end
	end
	return false
end

-- 前往攻击玩家
function TianShen3v3SceneLogic:ToAtkRole(obj)
	local cur_obj = GuajiCache.target_obj
	if obj ~= cur_obj then
		GuajiWGCtrl.Instance:ClearTemporary()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		GuajiWGCtrl.Instance:DoAttackTarget(obj)
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
	end
end

-- 前往最近的占领点
function TianShen3v3SceneLogic:MoveToNearestPoint()
	local point_x, point_y, cfg = TianShen3v3WGData.Instance:GetNearestOccupyingOrUnoccupationPointPos()
	if cfg then
		GuajiWGCtrl.Instance:ClearAllOperate()
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:MoveToPos(TianShen3v3WGData.Instance:GetOtherCfg().pk_scene_id, point_x, point_y, 2)
	end
end

-- 计算两个场景物体距离
function TianShen3v3SceneLogic:CalDistance(obj_1, obj_2)
	local x, y = obj_1:GetLogicPos()
	local target_x, target_y = obj_2:GetLogicPos()
	local distance = u3d.v2Distance({x = x, y = y}, {x = target_x, y = target_y})
	return distance
end

-- function TianShen3v3SceneLogic:GetRoleNameBoardText(role_vo)
-- 	local col_t = COLOR3B.WHITE
-- 	if Scene.Instance:GetMainRole() then
-- 		local main_role_vo = Scene.Instance:GetMainRole():GetVo()
-- 		local main_role_side = main_role_vo.cross3v3_side
-- 		local role_side = role_vo.is_shadow and role_vo.special_param or role_vo.cross3v3_side
-- 		if role_vo.obj_id == main_role_vo.obj_id then
-- 			col_t = COLOR3B.WHITE
-- 		elseif main_role_side == role_side then
-- 			col_t = COLOR3B.YELLOW
-- 		else
-- 			col_t = COLOR3B.RED
-- 		end
-- 	end
-- 	local t = {}
-- 	t.color = col_t
-- 	t.text = role_vo.name
-- 	return t
-- end

-- -- 获取采集物特殊名字显示
-- function TianShen3v3SceneLogic:GetGatherSpecialText(gather_vo)
-- 	local t = {}
-- 	local gather_name, gather_color = KuafuPVPWGData.Instance:GetGatherNameByObjid(gather_vo.obj_id)
-- 	if gather_name then
-- 		t[1] = {}
-- 		t[1].color = gather_color
-- 		t[1].text = "【" .. gather_name .. "】"
-- 		return t
-- 	end
-- 	return t
-- end

-- 获取采集物特殊形象
function TianShen3v3SceneLogic:GetGatherSpecialRes(gather_vo)
	return KuafuPVPWGData.Instance:GetGatherResByObjid(gather_vo.obj_id)
end

-- 角色是否是敌人
function TianShen3v3SceneLogic:IsRoleEnemy(target_obj, main_role)
	local main_role_side = TianShen3v3WGData.Instance:GetMySide()
	if target_obj == nil or main_role_side == nil then
		return false, Language.Fight.Side
	end
	local target_vo = target_obj:GetVo()
	local target_side = target_vo.is_shadow == 1 and target_vo.special_param or target_vo.cross3v3_side 		-- 天神3v3和跨服3v3用同一个
	if main_role_side == target_side then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end

function TianShen3v3SceneLogic:SpecialSelectEnemy()
	return false
end

-- function TianShen3v3SceneLogic:GetGuajiCharacter()
-- 	local target_obj = self:GetRole()
-- 	if target_obj ~= nil then
-- 		GuajiCache.target_obj = target_obj
-- 		return target_obj, COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE, false
-- 	end
-- end

-- function TianShen3v3SceneLogic:GetRole( )
-- 	local main_role = Scene.Instance:GetMainRole()
-- 	local role_list = Scene.Instance:GetRoleList()
-- 	local limit_distance = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
-- 	local target_obj = nil
-- 	local main_x,main_y = main_role:GetLogicPos()
-- 	for k,v in pairs(role_list) do
-- 		if not v:IsMainRole() and self:IsRoleEnemy(v, main_role) and v:IsDead() == false then
-- 			local x,y = v:GetLogicPos()
-- 			local distance = GameMath.GetDistance(main_x, main_y, x, y, false)
-- 			if distance < limit_distance then
-- 				target_obj = v
-- 				limit_distance = distance
-- 			end
-- 		end
-- 	end
-- 	if target_obj ~= nil then
-- 		return target_obj
-- 	end
-- end

function TianShen3v3SceneLogic:IsHideBOSSNameState()
	return false
end

function TianShen3v3SceneLogic:CanGetMoveObj()
	return true
end

function TianShen3v3SceneLogic:OnRoleEnter(obj_id)
	-- local main_role = Scene.Instance:GetMainRole()
	-- if main_role and main_role:IsGhost() then
	-- 	role:SetIsPerformer(true)
	-- end

	-- if GuajiCache.target_obj == nil and self:IsRoleEnemy(role, main_role) then
	-- 	GuajiCache.target_obj = role
	-- 	GuajiWGCtrl.Instance:MoveToObj(role, 4)
	-- end
end

function TianShen3v3SceneLogic:OnMainRoleEnter(scene_id)
	self:TryChangeCameraAngle()
end

function TianShen3v3SceneLogic:OnClickGuajiBtn(is_auto)
	TianShen3v3WGData.Instance:SetIsAutoFight(is_auto)
end

function TianShen3v3SceneLogic:OnMoveByClick()
	TianShen3v3WGData.Instance:SetIsAutoFight(false)
end

function TianShen3v3SceneLogic:OnMainRoleRelive()
	TianShen3v3WGData.Instance:SetIsAutoFight(true)
end

function TianShen3v3SceneLogic:OnRoleMove()
	MainuiWGCtrl.Instance:FlushXunLuStates()
end

function TianShen3v3SceneLogic:TryChangeCameraAngle()	
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local other_cfg = TianShen3v3WGData.Instance:GetOtherCfg()
		local relive_pos_1 = Split(other_cfg.relive_pos_1, ",")
		local relive_pos_2 = Split(other_cfg.relive_pos_2, ",")
		local logic_x, logic_y = main_role:GetLogicPos()
		local angle_x, angle_y
		if (math.abs(logic_x - tonumber(relive_pos_1[1])) <= 10 and math.abs(logic_y - tonumber(relive_pos_1[2])) <= 10) then
			angle_x = 41
			angle_y = 90
			main_role:SetDirectionByXY(0, 0)
		elseif (math.abs(logic_x - tonumber(relive_pos_2[1])) <= 10 and math.abs(logic_y - tonumber(relive_pos_2[2])) <= 10) then
			angle_x = 41
			angle_y = -90
			main_role:SetDirectionByXY(0, -180)
		end

		if angle_x ~= nil and angle_y ~= nil then
			local scene_id = Scene.Instance:GetSceneId()
			local param_t = {scene_id = scene_id}
			Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, angle_x, angle_y, 17, param_t)
		end
	end
end

function TianShen3v3SceneLogic:GetGuajiSelectObjDistance()
	return 2000
end

function TianShen3v3SceneLogic:GetSceneLogicMoveState()
    return not (
    	ViewManager.Instance:IsOpen(GuideModuleName.TianShen3v3CountdownView) 
    	or ViewManager.Instance:IsOpen(GuideModuleName.TianShen3v3ReadyView) 
    	or ViewManager.Instance:IsOpen(GuideModuleName.TianShen3v3EndView)
    	or (TimeWGCtrl.Instance:GetServerTime() - self.enter_time < 5)					-- 前五秒不要动
    	) 
end

function TianShen3v3SceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function TianShen3v3SceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function TianShen3v3SceneLogic:GetFallItemItemId(vo)
	if vo.item_id == 0 and vo.is_buff_falling then
		local buff_cfg = TianShen3v3WGData.Instance:GetBuffCfg(vo.buff_appearance)
		if buff_cfg then
			return buff_cfg.item_id
		end
	end
	return nil
end

-- function TianShen3v3SceneLogic:IsEnemy(target_obj, main_role, ignore_table)
-- 	main_role = main_role or Scene.Instance:GetMainRole()
-- 	return self:IsRoleEnemy(target_obj, main_role)
-- end

-- function TianShen3v3SceneLogic:GetGuajiPos()
-- 	local pos_x, pos_y = self:GetGuiJiEnemyPos()
-- 	return pos_x, pos_y
-- end

-- function TianShen3v3SceneLogic:GetGuiJiEnemyPos()
-- 	local main_role_vo = Scene.Instance:GetMainRole():GetVo()
-- 	local main_role_side = main_role_vo.cross3v3_side
-- 	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
-- 	if not IsEmptyTable(obj_move_info_list) then
-- 		local scene_info = KF3V3WGData.Instance:GetSceneInfo()
-- 		local enemy_role_list = not IsEmptyTable(scene_info) and scene_info[main_role_side == 0 and 2 or 1] or {}
-- 		if not IsEmptyTable(enemy_role_list) then
-- 			for k,v in pairs(obj_move_info_list) do
-- 				for k2,v2 in pairs(enemy_role_list) do
-- 					if v.vo.role_id == v2.uid then
-- 						return v.vo.pos_x, v.vo.pos_y
-- 					end
-- 				end
-- 			end
-- 		end
-- 	end
-- 	return 227, 97
-- end