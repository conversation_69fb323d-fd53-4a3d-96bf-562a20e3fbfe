function QuanMinBeiZhanView:InitRoleCap()
	local theme_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeCfg(TabIndex.quanmin_beizhan_cap)
	self.node_list.cap_role_xuanchuantu.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(theme_cfg.xuanchuantu))

	self.is_rolecap_init = true
	self:FluahRoleCap()
	self:CapProgress()

end

function QuanMinBeiZhanView:CapProgress()
	local zhanli = RoleWGData.Instance:GetMainRoleCap()
	local list_cfg = QuanMinBeiZhanWGData.Instance:GetRoleCapList()
	local cur_cfg_data
	for i,v in ipairs(list_cfg) do
		if v.state == ActivityRewardState.BKL then
			cur_cfg_data = v
			break
		end
	end

	if cur_cfg_data then
		self.node_list.cap_desc.text.text = string.format(Language.QuanMinBeiZhan.CapStr9, cur_cfg_data.cfg.zhanli_need - zhanli)
		self.node_list.ProgressBG.slider.value = zhanli / cur_cfg_data.cfg.zhanli_need
		self.node_list.progress_num.text.text = string.format(Language.QuanMinBeiZhan.capstr19, zhanli, cur_cfg_data.cfg.zhanli_need)
	else
		self.node_list.progress_num.text.text = Language.QuanMinBeiZhan.CapStr15
		self.node_list.cap_desc.text.text = Language.QuanMinBeiZhan.CapStr16
		self.node_list.ProgressBG.slider.value = 1
	end
end

function QuanMinBeiZhanView:ReleaseRoleCap()
	if self.role_cap_reward_list then
		self.role_cap_reward_list:DeleteMe()
		self.role_cap_reward_list = nil
	end
	self.is_rolecap_init = nil
end

function QuanMinBeiZhanView:FluahRoleCap()
	if self.is_rolecap_init == nil then
		return
	end

	local list = QuanMinBeiZhanWGData.Instance:GetRoleCapList()
	if list ~= nil then
		if not self.role_cap_reward_list then
			self.role_cap_reward_list = AsyncListView.New(BZRoleCapItemRender, self.node_list.cap_role_reward_list)
		end
		self.role_cap_reward_list:SetDataList(list)
	end
end

-----------------------------------------------
BZRoleCapItemRender = BZRoleCapItemRender or BaseClass(BaseRender)
function BZRoleCapItemRender:__init()
	self.max_item_count = 4
end

function BZRoleCapItemRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnLingQuBtnClick, self))
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function BZRoleCapItemRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BZRoleCapItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.btn_wdc:SetActive(self.data.state == ActivityRewardState.BKL)
	self.node_list.btn_lingqu:SetActive(self.data.state == ActivityRewardState.KLQ)
	self.node_list.yilingqu:SetActive(self.data.state == ActivityRewardState.YLQ)

	local zhanli = RoleWGData.Instance:GetMainRoleCap()
	if zhanli >= self.data.cfg.zhanli_need then
		self.node_list.name.text.text = string.format(Language.QuanMinBeiZhan.CapStr1, self.data.cfg.zhanli_need)
	else
		self.node_list.name.text.text = string.format(Language.QuanMinBeiZhan.CapStr12, self.data.cfg.zhanli_need)
	end

	local list = {}
	for k,v in pairs(self.data.cfg.reward_item) do
		table.insert(list, v)
	end
	self.reward_list:SetDataList(list)
end

function BZRoleCapItemRender:OnLingQuBtnClick()
	if self.data.state == ActivityRewardState.KLQ then
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_ZHANLIBIPIN, ZHANLIBIPIN_OP_TYPE.TYPE_ROLE_REWARD, self.data.cfg.reward_id)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.QuanMinBeiZhan.CapStr6)
	end
end
