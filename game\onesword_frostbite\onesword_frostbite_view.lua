OneSwordFrostbiteView = OneSwordFrostbiteView or BaseClass(SafeBaseView)

function OneSwordFrostbiteView:__init()
    self:SetMaskBg(false, true)
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.is_safe_area_adapter = true
    --self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/onesword_frostbite_ui_prefab", "layout_onesword_frostbite_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

    local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.ONESWORD_FROSTBITE}
	self:SetTabShowUIScene(0, data)
end

function OneSwordFrostbiteView:__delete()

end

function OneSwordFrostbiteView:ReleaseCallBack()
    self.video_player = nil
    
	if self.grid_list then
		for k, v in pairs(self.grid_list) do
			v:DeleteMe()
		end
		self.grid_list = nil
	end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.big_reward_cell then
		self.big_reward_cell:DeleteMe()
		self.big_reward_cell = nil
	end

    if OneSwordFrostbiteWGData.Instance then
        OneSwordFrostbiteWGData.Instance:ResetModelIndex()
    end
end

function OneSwordFrostbiteView:CloseCallBack()

end

function OneSwordFrostbiteView:OnClickItemIcon()
	local draw_cfg = OneSwordFrostbiteWGData.Instance:GetCurRoundDrawCostNumCfg()
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = draw_cfg.draw_item_id})
end

function OneSwordFrostbiteView:OnClickModelShowItemBtn()
    local model_cfg = OneSwordFrostbiteWGData.Instance:GetModelCfg()
	if IsEmptyTable(model_cfg) then
		return
	end
	TipWGCtrl.Instance:OpenItem({item_id = model_cfg.model_show_itemid})
end

function OneSwordFrostbiteView:LoadCallBack()
    self.play_anim = false
    self.node_list.title_view_name.text.text = Language.OneSwordFrostbite.ViewName
    self:InitMoneyBar()
    XUI.AddClickEventListener(self.node_list.reward_show_btn, BindTool.Bind(self.OnClickRewardShowBtn, self))
    XUI.AddClickEventListener(self.node_list.open_task_btn, BindTool.Bind(self.OnClickOpenTaskBtn, self))
    XUI.AddClickEventListener(self.node_list.draw_btn, BindTool.Bind(self.OnClickDrawBtn, self, 0))
    XUI.AddClickEventListener(self.node_list.model_show_item_btn, BindTool.Bind(self.OnClickModelShowItemBtn, self))
    XUI.AddClickEventListener(self.node_list.item_icon, BindTool.Bind(self.OnClickItemIcon, self))

    -- local bundle, asset = ResPath.GetRawImagesPNG("a3_yjhs_bg")
    -- if self.node_list.RawImage_tongyong then
	-- 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end

    if not self.grid_list then
		self.grid_list = {}
		local node_num1 = self.node_list["grid_root1"].transform.childCount
        local node_num2 = self.node_list["grid_root2"].transform.childCount
		for i = 0, node_num1 - 1 do
			self.grid_list[i] = OneSwordFrostbiteRewardRender.New(self.node_list["grid_root1"]:FindObj("box_item" .. i))
			self.grid_list[i]:SetIndex(i)
            self.grid_list[i]:SetClickCallBack(BindTool.Bind1(self.OnClickCellBtn, self))
		end

        for i = node_num1, node_num1 + node_num2 - 1 do
			self.grid_list[i] = OneSwordFrostbiteRewardRender.New(self.node_list["grid_root2"]:FindObj("box_item" .. i))
			self.grid_list[i]:SetIndex(i)
            self.grid_list[i]:SetClickCallBack(BindTool.Bind1(self.OnClickCellBtn, self))
		end
	end

    self.big_reward_cell = ItemCell.New(self.node_list["big_reward_cell"])

    -- if not self.model_display then
    --     self.model_display = OperationActRender.New(self.node_list["model_display"])
    --     self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	-- end

    if not self.model_display then
        self.model_display = RoleModel.New()
        self.model_display:SetUISceneModel(self.node_list["model_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
        self:AddUiRoleModel(self.model_display, 0)
    end

    	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.OneSwordFrostbiteView, self.get_guide_ui_event)

    RectTransform.SetAnchoredPositionXY(self.node_list["money_tabar_pos"].rect, -410, -33)
    self.video_player = self.node_list.video:GetComponent(typeof(UnityEngine.Video.VideoPlayer))
end

function OneSwordFrostbiteView:InitMoneyBar()
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local draw_cfg = OneSwordFrostbiteWGData.Instance:GetDrawNumCfgBySeq(0)
        local dart_num = OneSwordFrostbiteWGData.Instance:GetDartNum()
        local show_params = {
			show_cangjin_score = true,
            show_gold = true,

			--自定义货币.
			show_custom_1 = draw_cfg and draw_cfg.draw_item_id ~= nil,
			custom_item_id_1 = draw_cfg and draw_cfg.draw_item_id or 0,
            custom_num_1 = dart_num,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end
end

function OneSwordFrostbiteView:ShowIndexCallBack()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.OneSwordFrostbiteView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE)
end

function OneSwordFrostbiteView:OnClickCellBtn(cell)
	if not cell or not cell.data then
		return
	end

    if cell.data >= 0 then
        return
    end

    self:OnClickDrawBtn(0, cell)
    --OneSwordFrostbiteWGCtrl.Instance:SendOneSwordFrostbiteReq(ONESWORD_FROSTBITE_TYPE.OA_SWORD_FROSTBITE_OPERATE_TYPE_DRAW, 0, cell.index)
end

function OneSwordFrostbiteView:OnFlush()
    local data_list = OneSwordFrostbiteWGData.Instance:GetBoxListData()
	for i, v in pairs(self.grid_list) do
		v:SetData(data_list[i])
	end

    local round = OneSwordFrostbiteWGData.Instance:GetFinalRewardRound()
    local model_cfg = OneSwordFrostbiteWGData.Instance:GetModelCfg()
    local item_name = ItemWGData.Instance:GetItemName(model_cfg.model_show_itemid)
    self.node_list.round_desc:SetActive(round >= 0)
    if round > 0 then
        self.node_list.round_desc.text.text = string.format(Language.OneSwordFrostbite.RoundDesc1, item_name, round)
    elseif round == 0 then
        self.node_list.round_desc.text.text = string.format(Language.OneSwordFrostbite.RoundDesc2, item_name)
    end

    local big_reward_cfg = OneSwordFrostbiteWGData.Instance:GetBigRewardCfg()
    if big_reward_cfg then
        --self.big_reward_cell:SetShowCualityBg(false)
        --self.big_reward_cell:SetCellBgEnabled(false)
        self.big_reward_cell:SetIsUseRoundQualityBg(true)
        self.big_reward_cell:SetCellBgEnabled(false)
        self.big_reward_cell:SetData(big_reward_cfg.reward_item[0])
        self.big_reward_cell:SetItemIconLocalScale(Vector3(0.7, 0.7, 0.7))
        self.big_reward_cell:SetBindIconVisible(false)
        self.big_reward_cell:SetEffectRootEnable(false)
    end

    local draw_cfg = OneSwordFrostbiteWGData.Instance:GetCurRoundDrawCostNumCfg()
    if not draw_cfg then
        return
    end
    local dart_num = OneSwordFrostbiteWGData.Instance:GetDartNum()

    self.node_list.cost_num.text.text = draw_cfg and draw_cfg.consume_num
    local bundle, asset = ResPath.GetItem(draw_cfg.draw_item_id)
    self.node_list["item_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list.item_icon.image:SetNativeSize()
    end)
    local is_can_draw = OneSwordFrostbiteWGData.Instance:GetIsCanDraw()
    local is_can_receive = OneSwordFrostbiteWGData.Instance:GetIsHaveTaskCanReceive()
    self.node_list.draw_btn_remind:SetActive(is_can_draw)
    self.node_list.open_task_btn_remind:SetActive(is_can_receive)
    self:FlushModel()

    local show_params = {
        show_cangjin_score = true,
        show_gold = true,

        --自定义货币.
        show_custom_1 = draw_cfg and draw_cfg.draw_item_id ~= nil,
        custom_item_id_1 = draw_cfg and draw_cfg.draw_item_id or 0,
        custom_num_1 = dart_num,
    }
    self.money_bar:SetMoneyShowInfo(0, 0, show_params)
    self.money_bar:Flush()
end

function OneSwordFrostbiteView:FlushModel()
	local model_cfg = OneSwordFrostbiteWGData.Instance:GetModelCfg()
	if IsEmptyTable(model_cfg) then
		return
	end

    -- 模型
    local model_bundle, model_asset = model_cfg.model_bundle_name, model_cfg.model_asset_name
    if self.model_display then
        self.model_display:SetMainAsset(model_bundle, model_asset)
    end

    if model_cfg.model_pos and model_cfg.model_pos ~= "" then
        local pos = Split(model_cfg.model_pos, "|")
        self.model_display:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    local rotate_str = model_cfg.model_rot
    if rotate_str and rotate_str ~= "" then
        local rot = Split(rotate_str, "|")
        self.model_display:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
    end

    local scale = model_cfg.model_scale
    if scale and scale ~= "" then
        self.model_display:SetUSAdjustmentNodeLocalScale(scale)
    end
end

function OneSwordFrostbiteView:PlayAnim()
    self.play_anim = true
    RectTransform.SetSizeDeltaXY(self.node_list.grid_root1.rect, 0, 410)
    RectTransform.SetSizeDeltaXY(self.node_list.grid_root2.rect, 0, 410)
    self.node_list.grid_root1.rect:DOSizeDelta(Vector2(550, 410), 1)
    ReDelayCall(self, function()
		self.node_list.grid_root2.rect:DOSizeDelta(Vector2(550, 410), 1):OnComplete(function ()
			self.play_anim = false
		end)
    end, 1, "OneSwordFrostbiteView")
end

-- 播放视频
function OneSwordFrostbiteView:PlayVideo()
    if IsNil(self.video_player) then
        return
    end

    --self.video_player.time = 0
    self.node_list.video_content:SetActive(true)
    self.video_player:Play()
    ReDelayCall(self, function()
        self.video_player:Stop()
		self.node_list.video_content:SetActive(false)
	end, 1.5, "OneSwordFrostbiteViewVideo")
end

function OneSwordFrostbiteView:OnClickRewardShowBtn()
    OneSwordFrostbiteWGCtrl.Instance:OpenRewardYulanView()
end

function OneSwordFrostbiteView:OnClickOpenTaskBtn()
    OneSwordFrostbiteWGCtrl.Instance:OpenTaskView()
end

function OneSwordFrostbiteView:OnClickDrawBtn(btn_index, cell)
    if self.play_anim then
        TipWGCtrl.Instance:ShowSystemMsg(Language.OneSwordFrostbite.DrawDesc1)
        return
    end

    local draw_cfg = OneSwordFrostbiteWGData.Instance:GetCurRoundDrawCostNumCfg()
    local other_cfg = OneSwordFrostbiteWGData.Instance:GetOtherCfg()
    local dart_num = OneSwordFrostbiteWGData.Instance:GetDartNum()
    if IsEmptyTable(draw_cfg) then
        return
    end

    local is_all_draw = OneSwordFrostbiteWGData.Instance:GetIsAllDrawFinish()
    if is_all_draw then
        TipWGCtrl.Instance:ShowSystemMsg(Language.OneSwordFrostbite.DrawDesc)
        return
    end

    -- 抽奖
    local tips_data = {}
    tips_data.item_id = draw_cfg.draw_item_id
    tips_data.price = other_cfg.draw_item_price
    tips_data.draw_count = draw_cfg.consume_num
    tips_data.cost_type = other_cfg.money_type
    tips_data.has_num = dart_num
    tips_data.has_checkbox = true
    tips_data.checkbox_str = string.format("onesword_frostbite%s", btn_index)
    TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind2(self.SendOneSwordFrostbiteReq, self, cell), nil)
    --LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_FIND_WARD)
end

function OneSwordFrostbiteView:SendOneSwordFrostbiteReq(cell)
    local box_seq = -1
    if cell then
        if cell.data and cell.data < 0 then
            box_seq = cell.index
        end
    else
        box_seq = OneSwordFrostbiteWGData.Instance:GetDrawBoxSeq()
    end

    if box_seq < 0 then
        return
    end

    OneSwordFrostbiteWGCtrl.Instance:SendOneSwordFrostbiteReq(ONESWORD_FROSTBITE_TYPE.OA_SWORD_FROSTBITE_OPERATE_TYPE_DRAW, 0, box_seq)
end

function OneSwordFrostbiteView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.OneSwordFrostbiteBoxList then
        if nil == self.grid_list then
			return
		end

		local cell_index = ui_param
		if cell_index >= 0 then
			local item = self.grid_list[cell_index]
			if item then
				return item:GetView()
			end
		end
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

---------------------------------------------------------
OneSwordFrostbiteRewardRender = OneSwordFrostbiteRewardRender or BaseClass(BaseRender)
function OneSwordFrostbiteRewardRender:__init()

end

function OneSwordFrostbiteRewardRender:LoadCallBack()
    self.btn_block = self.view:GetComponent("UIBlock")
    self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function OneSwordFrostbiteRewardRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function OneSwordFrostbiteRewardRender:OnFlush()
    if not self.data then return end

    if self.data >= 0 then
        local reward_data = OneSwordFrostbiteWGData.Instance:GetRewardPoolCfgBySeq(self.data)
        self.item_cell:SetData(reward_data.reward_item[0])
    end

    self.node_list.close_content:SetActive(self.data < 0)
    self.node_list.open_content:SetActive(self.data >= 0)
    self.btn_block.enabled = self.data < 0
end