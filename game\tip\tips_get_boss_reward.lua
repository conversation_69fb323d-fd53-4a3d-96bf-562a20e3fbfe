TipsGetCommonBossRewardView = TipsGetCommonBossRewardView or BaseClass(SafeBaseView)

local col_num = 6 							 		--每行多少个
local ANI_SPEED = 0.1
local reword_count = 0 				--该次寻宝奖励物品个数
local ani_flag_t = {}
local ani_count = 1
local scroll_verticalNormalizedPosition = 1
local row_num = 1								--显示3行
local lerp = 0.015--1 / (50 - (col_num * row_num)*0)* 0.5		--每次减少多少
local CLOSE_SHOW_TIME = 10


local Sort_Type = {
	[GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
	[GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
	[GameEnum.ITEM_BIGTYPE_GIF] = 8,
	[GameEnum.ITEM_BIGTYPE_OTHER] = 7,
	[GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}


function TipsGetCommonBossRewardView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    -- self.full_screen = true
    self.view_name = "TipsGetCommonBossRewardView"
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_common_boss_reward_result")

	self.close_show_time = CLOSE_SHOW_TIME
end

function TipsGetCommonBossRewardView:ReleaseCallBack()
	self:CloseTimer()
	if nil ~= self.zhanshi_grid then
		self.zhanshi_grid:DeleteMe()
		self.zhanshi_grid = nil
	end
	if nil ~= self.zhanshi_ten_grid then
		self.zhanshi_ten_grid:DeleteMe()
		self.zhanshi_ten_grid = nil
	end

	if self.extra_reward_list then
		self.extra_reward_list:DeleteMe()
		self.extra_reward_list = nil
	end

	if self.get_way_list then
		self.get_way_list:DeleteMe()
		self.get_way_list = nil
	end

	if self.xianqi_cell_list then
		for k, v in pairs(self.xianqi_cell_list) do
			v:DeleteMe()
			v = nil
		end
		self.xianqi_cell_list = nil
	end

    if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end

	self:CancelTimeQuest()
	self:CancelMoveScrollTimeQuest()
	self.is_skip_anim =	true
	self:ReleaseData()
end

function TipsGetCommonBossRewardView:ReleaseData()
	self.id_list = nil
	self.again_func = nil
	self.other_info = nil
	self.no_need_sort = nil
	self.sure_func = nil
end

function TipsGetCommonBossRewardView:LoadCallBack()
	self.is_skip_anim = true

	self:InitXunBaoZhanshi()
	self:RegisterEvent()
end

function TipsGetCommonBossRewardView:CloseTimer()

	if CountDownManager.Instance:HasCountDown("get_boss_reward") then
		CountDownManager.Instance:RemoveCountDown("get_boss_reward")
	end
end

function TipsGetCommonBossRewardView:ShowIndexCallBack(index)
	self.close_show_time = CLOSE_SHOW_TIME
	self:CloseTimer()

	self.node_list.btn_sure_text.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainBtnCountDownStr,(self.other_info and self.other_info.sure_text) or Language.Common.Confirm, self.close_show_time)
	self.timer = CountDownManager.Instance:AddCountDown("get_boss_reward", BindTool.Bind1(self.FlushAutoClick, self), BindTool.Bind(self.Close, self), nil, self.close_show_time, 1)
end

--初始化格子
function TipsGetCommonBossRewardView:InitXunBaoZhanshi()
	self.zhanshi_grid = CommonBossRewardGrid.New()
    self.zhanshi_grid:CreateCells({col = col_num, change_cells_num = 1 , list_view = self.node_list["ph_zhanshii_cell"],
    itemRender = CommonBossRewardCell})
    self.zhanshi_grid:SetStartZeroIndex(false)

	self.zhanshi_ten_grid = CommonBossRewardGrid.New()
    self.zhanshi_ten_grid:CreateCells({col = 5, change_cells_num = 1 , list_view = self.node_list["ph_zhanshii_ten_cell"],
    itemRender = TenCommonBossRewardCell})
    self.zhanshi_ten_grid:SetStartZeroIndex(false)

	self.extra_reward_list = AsyncListView.New(HundredEquipDTRewardCell, self.node_list.extra_reward_list)
	self.get_way_list = AsyncListView.New(CommonBossRewardGetWayCell, self.node_list.get_way_list)

	if not self.xianqi_cell_list then
		self.xianqi_cell_list = {}
		for i = 1, 2 do
			self.xianqi_cell_list[i] = XianQiUseCellRender.New(self.node_list["xiaoqi_cell_" .. i])
			self.xianqi_cell_list[i]:SetIndex(i)
		end
	end
end

function TipsGetCommonBossRewardView:CloseCallBack()
	GlobalEventSystem:Fire(OtherEventType.Common_Reward_Close3)
end

-- 注册按钮事件
function TipsGetCommonBossRewardView:RegisterEvent()
	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind1(self.OnSure, self))						--确定关闭界面
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnQuchuAgain, self))				--再来一次
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClinkSkipAnim, self))
	XUI.AddClickEventListener(self.node_list.btn_equip_drop_times_reward, BindTool.Bind(self.ShowDropTimesReward, self))
	XUI.AddClickEventListener(self.node_list.btn_award_tip, BindTool.Bind(self.OnClickAwardTip, self))

	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function TipsGetCommonBossRewardView:OnClinkSkipAnim(is_on)
	self.is_skip_anim = not is_on
end


function TipsGetCommonBossRewardView:SetData(id_list, again_func, other_info, no_need_sort, sure_func)
	self.id_list = id_list
	self.again_func = again_func
	self.other_info = other_info
	self.no_need_sort = no_need_sort
	self.sure_func = sure_func
end

--刷新数据
function TipsGetCommonBossRewardView:OnFlush()
	self.node_list.skip_anim_toggle.toggle.isOn = not self.is_skip_anim
	self.node_list.btn_one_more:CustomSetActive(self.again_func ~= nil)

	if self.other_info then
		self.node_list.btn_sure_text.text.text = self.other_info.sure_text or Language.Common.Confirm
		self.node_list.btn_again_txt.text.text = self.other_info.again_text or Language.TreasureHunt.BtnText[0]
		self.node_list.spend_root:CustomSetActive(self.other_info.stuff_id ~= nil and self.other_info.times ~= nil and self.other_info.spend ~= nil)

		if self.other_info.stuff_id then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(self.other_info.stuff_id) --拥有的材料数量
			self.node_list.xianyu_icon:CustomSetActive(has_num < self.other_info.times and self.other_info.spend > 0)
			self.node_list.zbitem_key:CustomSetActive(has_num > 0 or self.other_info.spend <= 0)

			self.node_list.zbitem_key_num.text.text = has_num
			self.node_list.img_zbitem.image:LoadSprite(ResPath.GetItem(self.other_info.stuff_id))
			self.node_list.one_more_cosume.text.text = (self.other_info.times - has_num) * self.other_info.spend
		end
	end

	--local show_count = #self.id_list
	--self:ChangeState(show_count <= col_num)
	self:ResetItemData()
	self:FlushTitleInfo()
	self:FlushXianqiCellList()
end

function TipsGetCommonBossRewardView:FlushAutoClick(elapse_time, total_time)
	if self.node_list.btn_sure_text then
		self.close_show_time = self.close_show_time - 1
		self.node_list.btn_sure_text.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainBtnCountDownStr,(self.other_info and self.other_info.sure_text) or Language.Common.Confirm, self.close_show_time)
	end
end

function TipsGetCommonBossRewardView:ChangeState(is_not_ten)
	self.node_list["zhanshii_cell_root"]:SetActive(is_not_ten)
	self.node_list["zhanshii_ten_cell_root"]:SetActive(not is_not_ten)
end

function TipsGetCommonBossRewardView:FlushTitleInfo()
	local scene_type = Scene.Instance:GetSceneType()
	local is_max = HundredEquipWGData.Instance:GetRmbBuyIsMaxLevel()
	self.node_list.xianli_not_enough_content:CustomSetActive(false)
	self.node_list.xianli_enough_content:CustomSetActive(true)

	local is_hundred = scene_type == SceneType.VIP_BOSS or scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.WorldBoss or scene_type == SceneType.HIGH_TEAM_EQUIP_FB
	self.node_list.btn_equip_drop_times_reward:SetActive(is_hundred)
	self.node_list.next_equip_drop_times_content:SetActive(is_hundred)
	self.node_list.btn_one_more:SetActive(not is_hundred and self.again_func ~= nil)

	local is_enough = BossAssistWGData.Instance:GetIsXianliReduce()
	if is_hundred then
		-- 普通升级倍率
		local level = HundredEquipWGData.Instance:GetLevelValue()
		local droptimes_data = HundredEquipWGData.Instance:GetDropTimesListCfg(level)
		local nor_rate = (droptimes_data[scene_type] or 0) / 100		-- 倍数

		-- 直购 倍率特权
		local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
		local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level) or {}
		local add_rate = ((level_cfg.drop_time_add_show or 0) / 100) * nor_rate	-- 额外倍数

		--守护特权.
		local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()

		local cur_rate = nor_rate + add_rate + (shtq_add_value / 100)
		self.node_list.hundred_equip_rate.text.text = cur_rate == 0 and 1 or cur_rate

		BossAssistWGData.Instance:SetIsXianliReduce(false)

		if not is_enough and scene_type == SceneType.VIP_BOSS then
			self.node_list.xianli_not_enough_content:CustomSetActive(true)
			self.node_list.xianli_enough_content:CustomSetActive(false)
			local get_way_list = BossWGData.Instance:GetXianqiGetWayCfg()
			self.get_way_list:SetDataList(get_way_list)
		end
		local data_list = self:GetExtraRewardData()
		self.extra_reward_list:SetDataList(data_list)
	end

	local y_value = 192
	local y_pos = 50
	if is_hundred then
		if not is_enough and scene_type == SceneType.VIP_BOSS then
			y_value = 82
			y_pos = 100
		end
	else
		y_value = 316
		y_pos = -6
	end

	RectTransform.SetSizeDeltaXY(self.node_list.zhanshii_cell_root.rect, 568, y_value)
	RectTransform.SetAnchoredPositionXY(self.node_list.zhanshii_cell_root.rect, 184, y_pos)

	local is_active = true
	if scene_type== SceneType.KF_BOSS then
		is_active = false
	end
	-- if is_max then
	-- 	is_active = false
	-- end
	if (is_hundred and not is_enough and scene_type == SceneType.VIP_BOSS) then
		is_active = false
	end
	local shtq_is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.pri_col_shtq)
	if not shtq_is_open then
		is_active = false
	end
	self.node_list.btn_award_tip:SetActive(is_active)
	-- self.node_list.btn_award_tip:SetActive(false)
	self.node_list.congrats_win:CustomSetActive(not is_hundred)
	self.node_list.hundred_eq_root:CustomSetActive(is_hundred)
end

function TipsGetCommonBossRewardView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	local xianli_potion, xianli_potion1 = BossWGData.Instance:GetBossXianliAddItem()
    if item_id == xianli_potion or item_id == xianli_potion1 then
        self:FlushXianqiCellList()
    end
end

function TipsGetCommonBossRewardView:FlushXianqiCellList()
	local xianli_potion, xianli_potion1 = BossWGData.Instance:GetBossXianliAddItem()

	local data_list = {}
	table.insert(data_list, {consume_item = xianli_potion})
	table.insert(data_list, {consume_item = xianli_potion1})

	for i = 1, 2 do
		if data_list[i] then
			self.xianqi_cell_list[i]:SetData(data_list[i])
		end
	end
end

function TipsGetCommonBossRewardView:SortItem(item_list)
	if self.no_need_sort then
		return item_list
	end

	local item_cfg,item_type
	for i,v in ipairs(item_list) do
		item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.color = item_cfg and item_cfg.color or 0
		v.item_type = Sort_Type[item_type] or 1
		v.is_bind = item_cfg and item_cfg.isbind or 0
	end
	
	SortTools.SortDesc(item_list, "color", "item_type")
	return item_list
end

function TipsGetCommonBossRewardView:CancelTimeQuest()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function TipsGetCommonBossRewardView:CancelMoveScrollTimeQuest()
	if self.move_scroll_quest then
		GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
		self.move_scroll_quest = nil
	end
end

function TipsGetCommonBossRewardView:GetExtraRewardData()
	local level = HundredEquipWGData.Instance:GetLevelValue()
	local is_max = HundredEquipWGData.Instance:GetLevelIsMaxLevel()
	level = is_max and level or level + 1
	local reward_data = HundredEquipWGData.Instance:GetAddedDropTimesListShowCfg2(level)
	local extra_reward_min_quailty = HundredEquipWGData.Instance:GetOtherCfgByName("extra_reward_min_quailty")
	local extra_reward_min_star = HundredEquipWGData.Instance:GetOtherCfgByName("extra_reward_min_star")
	local data_list = {}

	for k, v in pairs(reward_data) do
		table.insert(data_list, v)
	end

	for k, v in pairs(self.id_list) do
		local star = v.param and v.param.star_level or 0
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if ((v.color == extra_reward_min_quailty and star >= extra_reward_min_star) or v.color > extra_reward_min_quailty)
			and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			local data = {}
			data.item_id = v.item_id
			data.num = 1
			data.is_bind = v.is_bind
			data.param = {}
			data.param.star_level = star
			table.insert(data_list, data)
			table.insert(data_list, data)
		end
	end

	return data_list
end

function TipsGetCommonBossRewardView:ResetItemData()
	if not self.id_list then
		return
	end

	local list_info = self.id_list
	list_info = self:SortItem(list_info)

    reword_count = #list_info
	--local grid_target = #self.id_list <= col_num and self.zhanshi_grid or self.zhanshi_ten_grid
	local grid_target = self.zhanshi_grid
	if grid_target ~= nil then
		grid_target:SetDataList(list_info)
		self:CancelTimeQuest()
		ani_flag_t = {}
		ani_count = 1
		-- ANI_SPEED = 0.1 * ((col_num * row_num) / #self.id_list)

		if self.is_skip_anim then
			self.time_quest = GlobalTimerQuest:AddTimesTimer(function()
				self:DoCellAnimTen(true)
				end, ANI_SPEED, reword_count )
		else
			for i = 1, reword_count do
				self:DoCellAnimTen(false)
			end
		end

		if #self.id_list > col_num then
			self:CancelMoveScrollTimeQuest()
			-- grid_target:JumptToPrecent(1)
			lerp = 1 / (#self.id_list - (col_num * row_num)) * 0.5		--每次减少多少

			if reword_count > col_num * 2 and self.is_skip_anim then
				self.move_scroll_quest = GlobalTimerQuest:AddTimesTimer(function()
					-- self:MoveScroll()
					if scroll_verticalNormalizedPosition <= 0 then
						scroll_verticalNormalizedPosition = 0
						self:CancelMoveScrollTimeQuest()
					end
				end, 0.03, 999999)
			end
		end
	end
end

function TipsGetCommonBossRewardView:DoCellAnimTen(do_tween)
	if not self.id_list then
		return
	end
	--local grid_target = #self.id_list <= col_num and self.zhanshi_grid or self.zhanshi_ten_grid
	local grid_target = self.zhanshi_grid
    local cell = grid_target:GetCell(ani_count)
    ani_flag_t[ani_count] = true
	ani_count = ani_count + 1

	if cell ~= nil and cell:GetData() ~= nil then
		if do_tween then
			cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
			cell.view.transform:DOScale(Vector3(1, 1, 1), 0.2)
		else
			cell.view.transform.localScale = Vector3(1, 1, 1)
		end
		--特效
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0), nil, nil)
		cell:SetActive(true)
    end
end

function TipsGetCommonBossRewardView:MoveScroll()
	-- if ani_count > (col_num * row_num) then --and self.move_tween_complete then --
	-- 	scroll_verticalNormalizedPosition = scroll_verticalNormalizedPosition - lerp
	-- else
	-- 	scroll_verticalNormalizedPosition = 1
	-- end

	if self.node_list.ph_zhanshii_cell then
		self.node_list.ph_zhanshii_cell.scroll_rect.verticalNormalizedPosition = 0--scroll_verticalNormalizedPosition
	end
end

function TipsGetCommonBossRewardView:OnSure()
	if self.sure_func then
		self.sure_func()
	end
	
	self:Close()
end

function TipsGetCommonBossRewardView:OnQuchuAgain()
	if self.again_func then
		self.again_func()
	end
end

function TipsGetCommonBossRewardView:ShowDropTimesReward()
    HundredEquipWGCtrl.Instance:OpenDropTimesRewardView()
	self:Close()
end

function TipsGetCommonBossRewardView:OnClickAwardTip()
    -- HundredEquipWGCtrl.Instance:OpenAwardTip()
	ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_shtq)
	self:Close()
end

----------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------
CommonBossRewardGrid = CommonBossRewardGrid or BaseClass(AsyncBaseGrid)

-- 获得指定的格子
function CommonBossRewardGrid:GetCell(index)
    for k, v in pairs(self.cell_list) do
        local row = math.floor((index - 1) / self.columns)
        if row == v:GetRows() and v:GetActive()  then
            for k1, v1 in pairs(v:GetAllCell()) do
                if v1:GetIndex() == index then
                    return v1
                end
            end
        end
    end
	return nil
end

CommonBossRewardCell = CommonBossRewardCell or BaseClass(ItemCell)

function CommonBossRewardCell:OnFlush()
    self:SetActive(ani_flag_t[self.index] == true)
    self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)

	for k,v in pairs(BaseCell_Ui_Circle_Effect) do
		self:SetEffectEnable(false, v)
	end
	ItemCell.OnFlush(self)
end

function CommonBossRewardCell:SetActive(value)
	ItemCell.SetVisible(self, value and (self.index == nil or ani_flag_t[self.index]))
end


TenCommonBossRewardCell = TenCommonBossRewardCell or BaseClass(ItemCell)
function TenCommonBossRewardCell:OnFlush()
	self:SetActive(ani_flag_t[self.index] == true)
    self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)

	for k,v in pairs(BaseCell_Ui_Circle_Effect) do
		self:SetEffectEnable(false, v)
	end
	ItemCell.OnFlush(self)
end

function TenCommonBossRewardCell:SetActive(value)
	ItemCell.SetActive(self, value and (self.index == nil or ani_flag_t[self.index]))
end

CommonBossRewardGetWayCell = CommonBossRewardGetWayCell or BaseClass(BaseRender)

function CommonBossRewardGetWayCell:__delete()

end

function CommonBossRewardGetWayCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_go_way, BindTool.Bind(self.OnClickGoWay, self))
end

function CommonBossRewardGetWayCell:OnFlush()
	if IsEmptyTable(self.data) then
        return
    end

	local bundle, asset = ResPath.GetRoleBagImg(self.data.icon)
	self.node_list["icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["icon"].image:SetNativeSize()
	end)
end

function CommonBossRewardGetWayCell:OnClickGoWay()
	if IsEmptyTable(self.data) then
        return
    end

    if self.data.open_param and self.data.open_param ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_param)
    end
end

----------------------XianQiUseCellRender----------------------
XianQiUseCellRender = XianQiUseCellRender or BaseClass(BaseRender)
function XianQiUseCellRender:LoadCallBack()
	self.cell = ItemCell.New(self.node_list["cell"])

    self.node_list["use_btn"].button:AddClickListener(BindTool.Bind(self.OnClickUse, self))
end

function XianQiUseCellRender:ReleaseCallBack()
	if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end
end

function XianQiUseCellRender:OnFlush()
    local item = self.data
    local num = ItemWGData.Instance:GetItemNumInBagById(item.consume_item)
    self.cell:SetData({item_id = item.consume_item})
    local color = num > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    self.cell:SetRightBottomColorText(ToColorStr(num .. "/" .. 1, color))
    self.cell:SetRightBottomTextVisible(true)

    local cfg = ItemWGData.Instance:GetItemConfig(item.consume_item)
    self.node_list["name"].text.text = ToColorStr(cfg.name ,ITEM_COLOR[cfg.color])
    local desc = Language.Boss.XianLiItemDesc[item.consume_item]
    self.node_list["desc"].text.text = desc
end

function XianQiUseCellRender:OnClickUse()
    local num = ItemWGData.Instance:GetItemNumInBagById(self.data.consume_item)
    if num > 0 then
        local item_index = ItemWGData.Instance:GetItemIndex(self.data.consume_item)
        if item_index == -1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.NotItemTop)
            return
        end
        BagWGCtrl.Instance:SendUseItem(item_index, 1)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.consume_item})
    end
end