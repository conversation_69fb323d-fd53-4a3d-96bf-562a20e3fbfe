BiZuoView = BiZuoView or BaseClass(SafeBaseView)

BiZuoView.TabIndex = {
	BB = TabIndex.bizuo_bizuo,				-- 每日必做
	BAH = TabIndex.bizuo_act_hall,			-- 活动大厅
	BTT = TabIndex.bizuo_time_table,			-- 活动时间表
	GET_STRONGER = TabIndex.get_stronger,		-- 我要变强
	DAILYFIND = TabIndex.welfare_dailyfind,			-- 每日找回
	CULTIVATION = TabIndex.bizuo_cultivation,			-- 修为
	--CSPRO = TabIndex.bizuo_cross_server_pro,	-- 跨服进度
}

HUOYUEDU_REWARD = 8

function BiZuoView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(false)
	self:LoadConfig()

	self.reward_list = {}							-- 升级奖励列表
	self.btn_levelup_effect = nil
	self.type = 0
	self.is_safe_area_adapter = true
	self.image_id = 0
	self.is_show_tips = false
	self.default_index = BiZuoView.TabIndex.BB
end

function BiZuoView:LoadConfig()
	local bundle_name = "uis/view/bizuo_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	--self:AddViewResource(0, bundle_name, "layout_bizuo_panel")
	self:AddViewResource(0, bundle_name, "VerticalTabbar")
	self:AddViewResource(0, bundle_name, "layout_bizuo_xianxiu")
	self:AddViewResource(0, bundle_name, "layout_bizuo_cultivation_add_view")
	self:AddViewResource(BiZuoView.TabIndex.BB, bundle_name, "layout_bizuo")
	--self:AddViewResource(BiZuoView.TabIndex.CSPRO, bundle_name, "layout_corss_server_pro")
	self:AddViewResource(BiZuoView.TabIndex.BAH, bundle_name, "layout_activity_hall")
	self:AddViewResource(BiZuoView.TabIndex.BTT, bundle_name, "layout_week_calendar")
	self:AddViewResource(BiZuoView.TabIndex.GET_STRONGER, bundle_name, "layout_get_stronger")
	self:AddViewResource(BiZuoView.TabIndex.DAILYFIND, bundle_name, "layout_dailyfind")
	self:AddViewResource(BiZuoView.TabIndex.CULTIVATION, bundle_name, "layout_bizuo_cultivation_view")
	--self:AddViewResource(0, bundle_name, "layout_off_line")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function BiZuoView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:DeleteBiZuoView()
	self:DeleteWeekCalendarView()
	self:DeleteActivityHallView()
	self:DescoryDailyFind()
	self:DestroyCSProPanel()
	self:ReleaseGetStrongerView()
	self:ReleaseCultivationrView()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.BiZuo, self.get_guide_ui_event)
	self.get_guide_ui_event = nil

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
end

function BiZuoView:LoadCallBack()
	local ver_path = "uis/view/bizuo_ui_prefab"
	local remind_tab = {
		{RemindName.Daily},
		{},
		{RemindName.BiZuo_Cultivation},
		{},
		{RemindName.ZhaoHui},
		{},
	}
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:SetVerTabbarIconStr("bizuo_new_tab")
	self.tabbar:Init(Language.BiZuo.TabGrop3, nil, ver_path, nil, remind_tab)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))

	self:InitMoneyBar()

	self.node_list.title_view_name.text.text = Language.BiZuo.TextViewName

	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.BiZuo, self.tabbar)
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BiZuo, self.get_guide_ui_event)

	self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

	if self.node_list["bz_common_tanhao"] then
		XUI.AddClickEventListener(self.node_list["bz_common_tanhao"], BindTool.Bind1(self.OnClickCommonTipBtn, self))
	end

	if self.node_list["bz_common_wenhao"] then
		XUI.AddClickEventListener(self.node_list["bz_common_wenhao"], BindTool.Bind1(self.OnClickCommonTipBtn, self))
	end

	-- local lilian_is_open = FunOpen.Instance:GetFunIsOpened(FunName.TianShenLiLianView)
	-- local desc_index = lilian_is_open and 2 or 1
	--self.node_list["Txt"].text.text = Language.OfflineRest.GuaJiTimeDesc[desc_index]

	local bundle, asset = ResPath.GetRawImagesJPG("a3_rc_bg")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function BiZuoView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_gold = true,
			show_bind_gold = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function BiZuoView:LoadIndexCallBack(index)
	-- if index == BiZuoView.TabIndex.BB or index == BiZuoView.TabIndex.BAH or index == BiZuoView.TabIndex.BTT 
	-- 	or index == BiZuoView.TabIndex.CSPRO then
	-- 	XUI.AddClickEventListener(self.node_list.btn_add_time, BindTool.Bind(self.OnClickAddTime, self))
	-- end

	if index == BiZuoView.TabIndex.BB then
		self:InitBiZuoView()
	elseif index == BiZuoView.TabIndex.BAH then
		self:InitActivityHallView()
		-- self:InitBiZuoView()
	elseif index == BiZuoView.TabIndex.BTT then
		self:InitWeekCalendarView()
	-- elseif index == BiZuoView.TabIndex.CSPRO then
	-- 	self:InitCSProPanel()
	elseif index == BiZuoView.TabIndex.GET_STRONGER then
		self:InitGetStrongerView()
	elseif index == BiZuoView.TabIndex.DAILYFIND then
		self:InitDailyFindView()
	elseif index == BiZuoView.TabIndex.CULTIVATION then
		self:InitCultivationView()
	end
end

function BiZuoView:ShowIndexCallBack(index)
	self.common_tip_view_title = nil
	self.common_tip_view_desc = nil
	self.common_tip_nor_desc = nil
	self.common_tip_btn_falg = 0--代表默认显示叹号
	self.node_list.money_tabar_pos:CustomSetActive(true)
	--self.node_list.bottom_bg:CustomSetActive(false)
	if index == BiZuoView.TabIndex.BB then --活跃度
		self:ShowIndexBiZuo()
		self.common_tip_btn_falg = nil
		self.node_list.money_tabar_pos:SetActive(false)
		self.node_list.cultivation_add_content:SetActive(true)
		--self.node_list.bottom_bg:SetActive(true)
		--self.common_tip_view_title = Language.BiZuo.HuoYueTipsTitle --标题
		--self.common_tip_view_desc = Language.BiZuo.BiZuoCommonTip_1 --内容
		--self.common_tip_nor_desc = Language.BiZuo.BiZuoCommonTip_1

	elseif index == BiZuoView.TabIndex.BAH then
		self:InitActivityHallView()
		self.common_tip_btn_falg = nil--不显示Tip
	elseif index == BiZuoView.TabIndex.BTT then
		self:InitWeekCalendarView()
		self.common_tip_btn_falg = nil--不显示Tip
		self.node_list.money_tabar_pos:SetActive(false)
		self.node_list.cultivation_add_content:SetActive(true)
		--self.common_tip_nor_desc = Language.BiZuo.BiZuoCommonTip_2 
	elseif index == BiZuoView.TabIndex.GET_STRONGER then
		self.common_tip_btn_falg = nil--不显示Tip
	elseif index == BiZuoView.TabIndex.DAILYFIND then
		self.common_tip_view_title = Language.BiZuo.BiZuoZahaohuiTips
		self.common_tip_view_desc = Language.BiZuo.BiZuoZahaohuiTips2
		self.common_tip_nor_desc = Language.BiZuo.DailyFind
		self.common_tip_btn_falg = 1
		BiZuoWGData.Instance:SetZhaoHuiOpenFlag()
		self.node_list.money_tabar_pos:SetActive(true)
		self.node_list.cultivation_add_content:SetActive(false)
	elseif index == BiZuoView.TabIndex.CULTIVATION then
		self.node_list.money_tabar_pos:SetActive(false)
		self:ShowIndexCultivation()
		self.node_list.money_tabar_pos:SetActive(false)
		self.node_list.cultivation_add_content:SetActive(true)
	end

	if self.node_list["bz_off_line_root"] then --挂机时间
		self.node_list["bz_off_line_root"]:SetActive(index == BiZuoView.TabIndex.BB or index == BiZuoView.TabIndex.BAH)
		-- local off_line_x = (index == BiZuoView.TabIndex.BB or index == BiZuoView.TabIndex.BTT) and 300 or 0
		-- self.node_list["bz_off_line_root"].transform.localPosition = Vector3(off_line_x, -305, 0)
	end
	-- if self.node_list["layout_bizuo_root"] then --活跃点
	-- 	self.node_list["layout_bizuo_root"]:SetActive(index == BiZuoView.TabIndex.BB or index == BiZuoView.TabIndex.BAH)
	-- end

	self:FlushCommonTipInfo() --问号 叹号 显示切换
end

function BiZuoView:FlushCommonTipInfo()
	if self.node_list["bz_common_tanhao"] then
		self.node_list["bz_common_tanhao"]:SetActive(self.common_tip_btn_falg == 0)
	end
	if self.node_list["bz_common_wenhao"] then
		self.node_list["bz_common_wenhao"]:SetActive(self.common_tip_btn_falg == 1)
	end
	if self.node_list["bz_common_desc"] then
		self.node_list["bz_common_desc"]:SetActive(self.common_tip_nor_desc ~= nil)
		self.node_list["bz_common_desc"].text.text = self.common_tip_nor_desc or ""
	end
end

function BiZuoView:OnClickCommonTipBtn()
	--print_error(self.common_tip_view_title, self.common_tip_view_desc)
	if self.common_tip_view_title and self.common_tip_view_desc then
		RuleTip.Instance:SetContent(self.common_tip_view_desc, self.common_tip_view_title)
	end
end

function BiZuoView:OpenCallBack()
	CultivationWGCtrl.Instance:RequestData()
end

function BiZuoView:CloseCallBack(is_all)
	BiZuoWGCtrl.Instance:SetViewTipsFlag(false)
end

function BiZuoView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == BiZuoView.TabIndex.BB then
				self:FlushBiZuoView()
			elseif index == BiZuoView.TabIndex.BAH then
				self:InitActivityHallView()
				--self:FlushBiZuoView()
			elseif index == BiZuoView.TabIndex.BTT then
				self:InitWeekCalendarView()
			-- elseif index == BiZuoView.TabIndex.CSPRO then
			-- 	self:FlushCSProPanel()
			elseif index == BiZuoView.TabIndex.GET_STRONGER then
				self:FlushGetStrongerView()
			elseif index == BiZuoView.TabIndex.DAILYFIND then
				self:OnFlushDailyFind()
			elseif index == BiZuoView.TabIndex.CULTIVATION then
				self:FlushCultivationView()
			end
		elseif "uplevel" == k then
			self:FlushBiZuoView(param_t)
		end
	end
	self:FlushOfflineTime()
	self:FlushJinJie()
	self:FlushTotalAdd()
end

function BiZuoView:RoleLevelChange(attr_name, value)
	if attr_name == "level" then
		BiZuoWGData.Instance:SetActivityHallCfg()
		self:Flush(BiZuoView.TabIndex.BAH)
		self:Flush()
		--RemindManager.Instance:Fire(RemindName.ActivityHall)
	end
end

function BiZuoView:FlushTotalAdd()
	local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
	self.node_list.text_number.text.text = number
	self.node_list.text_rate.text.text = string.format(Language.Cultivation.ExpAddTotalStr3, rate)
end

function BiZuoView:FlushJinJie()
	-- 境界图标
	local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
	local jinjir_asset, jinjie_asset = ResPath.GetDailyTypeIcon(cur_stage_cfg.stage_title_icon)
	self.node_list.jinjie_icon.image:LoadSprite(jinjir_asset, jinjie_asset, function ()
		self.node_list.jinjie_icon.image:SetNativeSize()
	end)

	-- 前中后期显示
	local stage = cur_stage_cfg.stage % 3
	local jinjir_asset_1, jinjie_asset_1 = ResPath.GetDailyTypeIcon("a3_jj_tx_tb" .. stage)
	self.node_list.jinjie_stage.image:LoadSprite(jinjir_asset_1, jinjie_asset_1, function ()
		self.node_list.jinjie_stage.image:SetNativeSize()
	end)

	local cur_xiuwei_level_cfg = CultivationWGData.Instance:GetCurXiuWeiLevelCfg()
	local need_exp = cur_xiuwei_level_cfg.need_exp
    local cur_exp = CultivationWGData.Instance:GetXiuWeiExp()
	self.node_list.cur_exp.text.text = cur_exp
	self.node_list.need_exp.text.text = need_exp
	self.node_list.xianxiu_slider.slider.value = cur_exp / need_exp
	self.node_list.xianxiu_desc2.text.text = Language.BiZuo.XiuWeiDesc2
end

function BiZuoView:FlushOfflineTime()
	if self.node_list.lbl_off_line_time then
		local remain_offline_rest_time = OfflineRestWGData.Instance.remain_offline_rest_time
		local hour = math.floor(remain_offline_rest_time / 3600)
		local time_str = OfflineRestWGData.Instance:GetTimeStr(remain_offline_rest_time)
		if hour >= 2 then
			time_str = ToColorStr(time_str, COLOR3B.DEFAULT_NUM)
		else
			time_str = ToColorStr(time_str, COLOR3B.RED)
		end
		self.node_list.lbl_off_line_time.text.text = time_str
	end
end

function BiZuoView:OnClickAddTime()
	if OfflineRestWGData.Instance:GetRemainOfflineRestTime() >= OfflineRestWGData.OfflineTotalTime then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OfflineRest.OfflineTimeFull)
		return
	end

	local id_list = OfflineRestWGData.Instance:GetOffLineCardItemId()
	local num = 0
	local item_id = 0

	if id_list and #id_list > 0 then
		for i = #id_list,1,-1 do
			num = ItemWGData.Instance:GetItemNumInBagById(id_list[i])
			if num > 0 then
				item_id = id_list[i]
				break
			end
		end
	end

	if num == 1 then
		if OfflineRestWGData.Instance:IsOverstep(item_id, 1) then
			OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(item_id, 1)
			return
		end

		local index = ItemWGData.Instance:GetItemIndex(item_id)
		BagWGCtrl.Instance:SendUseItem(index, 1, 0, 0)
		return
	elseif num > 1 then
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(item_id)
		return
	end
	-- GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, other.item_id, 1, other.seq)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = SPECIAL_GUAJICARD.IDTWO})
end

function BiZuoView:OnClickAdditionBtn()
	ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end

function BiZuoView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return NextGuideStepFlag
		end
	elseif ui_name == GuideUIName.BiZuoDaliyTask then
		if nil == self.bizuo_target_list then
			return
		end

		local data_list = self.bizuo_target_list:GetDataList()
		local cell_index = -1
		if data_list then
			for k,v in ipairs(data_list) do
				if v.type == ui_param then
					cell_index = k
					break
				end
			end
		end

		if cell_index >= 0 then
			local item = self.bizuo_target_list:GetCell(cell_index)
			if item then
				return item:GetView()
			end
		end
	elseif ui_name == GuideUIName.BiZuoDaliyVerTab then
		if not self.tabbar then
			return
		end

		local item = self.tabbar:GetVerCell(ui_param)
		if item then
			return item:GetView()
		end
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end