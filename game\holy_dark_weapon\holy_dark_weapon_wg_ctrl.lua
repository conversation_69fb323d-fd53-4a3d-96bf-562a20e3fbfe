require("game/holy_dark_weapon/holy_dark_weapon_view")
require("game/holy_dark_weapon/holy_dark_weapon_wg_data")
require("game/holy_dark_weapon/holy_dark_weapon_tabbar")
require("game/holy_dark_weapon/holy_dark_weapon_seal_view")
require("game/holy_dark_weapon/holy_dark_seal_buy_view")
require("game/holy_dark_weapon/holy_dark_equip_bag_view")
require("game/holy_dark_weapon/holy_dark_equip_attr_view")
require("game/holy_dark_weapon/holy_dark_equip_skill_view")
require("game/holy_dark_weapon/holy_dark_skill_pop_equip_bag")
require("game/holy_dark_weapon/holy_dark_qianghua_view")
require("game/holy_dark_weapon/holy_dark_upstar_view")
require("game/holy_dark_weapon/holy_dark_jinjie_view")
require("game/holy_dark_weapon/holy_dark_month_buy_view")
require("game/holy_dark_weapon/holy_dark_jinjie_attr_view")
require("game/holy_dark_weapon/holy_dark_weapon_enter_view")
require("game/holy_dark_weapon/holy_dark_seal_once_shop_view")


HolyDarkWeaponWGCtrl = HolyDarkWeaponWGCtrl or BaseClass(BaseWGCtrl)
function HolyDarkWeaponWGCtrl:__init()
	if HolyDarkWeaponWGCtrl.Instance then
		ErrorLog("[HolyDarkWeaponWGCtrl]:Attempt to create singleton twice!")
	end
	HolyDarkWeaponWGCtrl.Instance = self

	self.enter_view = HolyDarkWeaponEnterView.New(GuideModuleName.HolyDarkWeaponEnter)
	self.holy_weapon = HolyDarkWeaponView.New(GuideModuleName.HolyWeapon)
	self.dark_weapon = HolyDarkWeaponView.New(GuideModuleName.DarkWeapon)
	self.data = HolyDarkWeaponWGData.New()
	self.seal_buy_view = HolyDarkSealBuyView.New()
	self.equip_attr_view = HolyDarkEquipAttrView.New()
	self.skill_pop_equip_bag = HolyDarkSkillPopEquipBag.New()
	self.month_buy_view = HolyDarkMonthBuyView.New()
	self.jinjie_attr_view = HolyDarkJinJieAttrView.New()
	self.seal_once_shop_view = HolyDarkSealOnceShopView.New()

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function HolyDarkWeaponWGCtrl:__delete()
    HolyDarkWeaponWGCtrl.Instance = nil

    if self.holy_weapon then
		self.holy_weapon:DeleteMe()
		self.holy_weapon = nil
	end

	if self.dark_weapon then
		self.dark_weapon:DeleteMe()
		self.dark_weapon = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.seal_buy_view then
		self.seal_buy_view:DeleteMe()
		self.seal_buy_view = nil
	end

	if self.equip_attr_view then
		self.equip_attr_view:DeleteMe()
		self.equip_attr_view = nil
	end

	if self.jinjie_attr_view then
		self.jinjie_attr_view:DeleteMe()
		self.jinjie_attr_view = nil
	end

	if self.skill_pop_equip_bag then
		self.skill_pop_equip_bag:DeleteMe()
		self.skill_pop_equip_bag = nil
	end

	if self.month_buy_view then
		self.month_buy_view:DeleteMe()
		self.month_buy_view = nil
	end

	if self.seal_once_shop_view then
		self.seal_once_shop_view:DeleteMe()
		self.seal_once_shop_view = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function HolyDarkWeaponWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSRelicOperate)
	self:RegisterProtocol(CSRelicDecompsEquip)

	self:RegisterProtocol(SCRelicItemInfo, "OnSCRelicItemInfo")
	self:RegisterProtocol(SCRelicItemUpdate, "OnSCRelicItemUpdate")
	self:RegisterProtocol(SCRelicPowerItemInfo, "OnSCRelicPowerItemInfo")
	self:RegisterProtocol(SCRelicPowerItemUpdate, "OnSCRelicPowerItemUpdate")
	self:RegisterProtocol(SCRelicLightBagInfo, "OnSCRelicLightBagInfo")
	self:RegisterProtocol(SCRelicLightBagChangeInfo, "OnSCRelicLightBagChangeInfo")
	self:RegisterProtocol(SCRelicDarkBagInfo, "OnSCRelicDarkBagInfo")
	self:RegisterProtocol(SCRelicDarkBagChangeInfo, "OnSCRelicDarkBagChangeInfo")
end

function HolyDarkWeaponWGCtrl:RegisterAllEvents()
	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	 -- 角色属性改变
    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

-- 物品变化
function HolyDarkWeaponWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local module_name
	local weapon_type = self.data:GetCurWeaponType()
	if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
		module_name = GuideModuleName.HolyWeapon
	else
		module_name = GuideModuleName.DarkWeapon
	end

	local fire_red = false
	if self.data:CheckIsSealStuff(change_item_id, change_reason) then
		ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_seal)
		fire_red = true
	elseif self.data:CheckIsSlotLevelStuff(change_item_id, change_reason) then
		ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_qinaghua)
		fire_red = true
	elseif self.data:CheckIsSlotStarStuff(change_item_id, change_reason) then
		ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_upstar)
		fire_red = true
	end

	if fire_red then
		RemindManager.Instance:Fire(RemindName.HolyWeapon)
	    RemindManager.Instance:Fire(RemindName.DarkWeapon)
	end

end

-- 角色属性改变
function HolyDarkWeaponWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
		local show_info = self.data:GetShowWeaponInfo()
		for k,v in pairs(show_info) do
            if (old_value < v.show_level and value >= v.show_level) or (old_value < v.open_level and value >= v.open_level) then
            	local weapon_type = self.data:GetCurWeaponType()
				local module_name
				if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
					module_name = GuideModuleName.HolyWeapon
				else
					module_name = GuideModuleName.DarkWeapon
				end

               	ViewManager.Instance:FlushView(module_name)
               	RemindManager.Instance:Fire(RemindName.HolyWeapon)
	    		RemindManager.Instance:Fire(RemindName.DarkWeapon)
                return
            end
        end
	end
end

function HolyDarkWeaponWGCtrl:OnPassDay()
	local module_name
	local weapon_type = self.data:GetCurWeaponType()
	if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
		module_name = GuideModuleName.HolyWeapon
	else
		module_name = GuideModuleName.DarkWeapon
	end

   	ViewManager.Instance:FlushView(module_name)
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
	RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

-- 通用请求请求操作
function HolyDarkWeaponWGCtrl:SendCSHolyDarkWeaponRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRelicOperate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function HolyDarkWeaponWGCtrl:OnSCRelicItemInfo(protocol)
	--print_error("=======圣器全部信息======", protocol)
	self.data:SetRelicItemAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
    RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

function HolyDarkWeaponWGCtrl:OnSCRelicItemUpdate(protocol)
	--print_error("-----------单个圣器变化---",protocol)
	self.data:RelicItemUpdateInfo(protocol)

	local weapon_type = self.data:GetCurWeaponType()
	local module_name
	if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
		module_name = GuideModuleName.HolyWeapon
	else
		module_name = GuideModuleName.DarkWeapon
	end

	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_seal)
	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_equip_bag)
	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_equip_skill)
	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_qinaghua)
	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_upstar)
	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_jinjie)

	self:FlushHolyDarkSealBuyView()
	if self.equip_attr_view:IsOpen() then
		self.equip_attr_view:Flush()
	end

	if self.seal_once_shop_view:IsOpen() then
		self.seal_once_shop_view:Flush()
	end

	self.data:ClearSkillSelectBagParam()
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
    RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

function HolyDarkWeaponWGCtrl:OnSCRelicPowerItemInfo(protocol)
	--print_error("----能量信息",protocol)
	self.data:SetRelicPowerItemInfo(protocol)
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
    RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

function HolyDarkWeaponWGCtrl:OnSCRelicPowerItemUpdate(protocol)
	--print_error("----单个能量信息改变",protocol)
	self.data:RelicPowerItemUpdateInfo(protocol)
	local weapon_type = self.data:GetCurWeaponType()
	local module_name
	if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
		module_name = GuideModuleName.HolyWeapon
	else
		module_name = GuideModuleName.DarkWeapon
	end

	ViewManager.Instance:FlushView(module_name, TabIndex.holy_dark_jinjie)

	self:FlushHolyDarkMonthBuyView()
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
    RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

function HolyDarkWeaponWGCtrl:OnSCRelicLightBagInfo(protocol)
	--print_error("【-----圣器背包总信息----】：", protocol)
	self.data:SetRelicLightBagInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.HolyWeapon, TabIndex.holy_dark_equip_bag)
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
    
end

function HolyDarkWeaponWGCtrl:OnSCRelicLightBagChangeInfo(protocol)
	--print_error("【-----圣器背包单个变化----】：", protocol)
	local new_data = protocol.change_info
	local index = new_data.index
	local old_data = self.data:GetRelicLightBagInfoByIndex(index)
	local old_num = 0
	if not IsEmptyTable(old_data) then
		old_num = old_data.num
	end

	if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > old_num then
		local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
		local num = new_data.num
		num = new_data.num - old_num
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]), num)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

	self.data:SetRelicLightBagChangeInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.HolyWeapon, TabIndex.holy_dark_equip_bag)
	RemindManager.Instance:Fire(RemindName.HolyWeapon)
end

function HolyDarkWeaponWGCtrl:OnSCRelicDarkBagInfo(protocol)
	--print_error("【-----暗器背包总信息----】：", protocol)
	self.data:SetRelicDarkBagInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DarkWeapon, TabIndex.holy_dark_equip_bag)
	RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

function HolyDarkWeaponWGCtrl:OnSCRelicDarkBagChangeInfo(protocol)
	--print_error("【-----暗器背包单个变化----】：", protocol)
	local new_data = protocol.change_info
	local index = new_data.index
	local old_data = self.data:GetRelicDarkBagInfoByIndex(index)
	local old_num = 0
	if not IsEmptyTable(old_data) then
		old_num = old_data.num
	end

	if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > old_num then
		local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
		local num = new_data.num
		num = new_data.num - old_num
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]), num)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
	end
	
	self.data:SetRelicDarkBagChangeInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DarkWeapon, TabIndex.holy_dark_equip_bag)
	RemindManager.Instance:Fire(RemindName.DarkWeapon)
end

function HolyDarkWeaponWGCtrl:SendCSRelicDecompsEquip(seq, count, bag_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRelicDecompsEquip)
	protocol.seq = seq
	protocol.count = count or 0
	protocol.bag_list = bag_list or {}
	protocol:EncodeAndSend()
end

function HolyDarkWeaponWGCtrl:OnRelicOperateResult(result, operate_type)
	--print_error("---操作返回----", result, operate_type)
	if result == 1 then
		local weapon_type = self.data:GetCurWeaponType()
		local view
		if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
			view = self.holy_weapon 
		else
			view = self.dark_weapon
		end

		if operate_type == RELIC_OPERATE_TYPE.SLOT_UPGRADE then
			view:PlayJinJieEffect()
		elseif operate_type == RELIC_OPERATE_TYPE.SLOT_UPSTAR then
			view:PlayUpStarEffect()
		elseif operate_type == RELIC_OPERATE_TYPE.SLOT_UPLEVEL then
			view:PlayQiangHuaEffect()
		elseif operate_type == RELIC_OPERATE_TYPE.RELIC_ACTIVE then
			view:PlayRelicJiHuoEffect()
			local relic_seq = self:GetHolyDarkViewSelectWeaponSeq()
			AppearanceWGCtrl.Instance:OnGetNewAppearance({appe_type = ROLE_APPE_TYPE.RELIC_TYPE, appe_image_id = relic_seq})
		end
	end
end

function HolyDarkWeaponWGCtrl:OnRelicDecomsEquipResult(result, operate_type)
	--print_error("---操作返回----", result)
	if result == 1 then
		local weapon_type = self.data:GetCurWeaponType()
		local view
		if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
			view = self.holy_weapon 
		else
			view = self.dark_weapon
		end

		view:PlaySkillUpEffect()
	end
end

function HolyDarkWeaponWGCtrl:GetHolyDarkViewSelectWeaponSeq()
	local weapon_type = self.data:GetCurWeaponType()
	if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
		return self.holy_weapon:GetCurSelectWeaponSeq()
	else
		return self.dark_weapon:GetCurSelectWeaponSeq()
	end
end

function HolyDarkWeaponWGCtrl:OpenHolyDarkSealBuyView(seal_data)
	self.seal_buy_view:SetDataAndOpen(seal_data)
end

function HolyDarkWeaponWGCtrl:OpenHolyDarkSealOnceShopView(seal_data)
	self.seal_once_shop_view:SetDataAndOpen(seal_data)
end

function HolyDarkWeaponWGCtrl:FlushHolyDarkSealBuyView()
	if self.seal_buy_view:IsOpen() then
		self.seal_buy_view:Flush()
	end
end

--套装激活界面
function HolyDarkWeaponWGCtrl:OpenSuitAttrView(relic_seq)
	self.equip_attr_view:SetDataAndOpen(relic_seq)
end

--技能升級消耗背包界面
function HolyDarkWeaponWGCtrl:OpenSkillPopEquipBagView(relic_seq)
	self.skill_pop_equip_bag:SetDataAndOpen(relic_seq)
end

--购买能量月卡
function HolyDarkWeaponWGCtrl:OpenHolyDarkMonthBuyView(seal_data)
	self.month_buy_view:SetDataAndOpen(seal_data)
end

function HolyDarkWeaponWGCtrl:FlushHolyDarkMonthBuyView()
	if self.month_buy_view:IsOpen() then
		self.month_buy_view:Flush()
	end
end
		
--进阶属性界面
function HolyDarkWeaponWGCtrl:OpenJinJieAttrView(item_data)
	self.jinjie_attr_view:SetDataAndOpen(item_data)
end
