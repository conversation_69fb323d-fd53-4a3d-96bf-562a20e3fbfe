SpecialPersonalBossSceneView = SpecialPersonalBossSceneView or BaseClass(SafeBaseView)

function SpecialPersonalBossSceneView:__init()
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_special_personal_boss")
	self.view_cache_time = 0
    self.is_safe_area_adapter = true
end

function SpecialPersonalBossSceneView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

	if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function SpecialPersonalBossSceneView:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    self.reward_list:SetStartZeroIndex(true)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
end

function SpecialPersonalBossSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["panel_root"] then
		self.obj = self.node_list["panel_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform, false)
		-- self.obj.transform.localPosition = Vector3(0, 0, 0)
		-- self.obj.transform.localScale = Vector3.one
	end

	if self.is_out_fb then
        self.obj:SetActive(false)
    else
        self.obj:SetActive(true)
    end

    self.is_out_fb = nil
end

function SpecialPersonalBossSceneView:OpenCallBack()
    self.is_out_fb = nil
end

function SpecialPersonalBossSceneView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function SpecialPersonalBossSceneView:OnFlush()
    local info = BossWGData.Instance:GetPersonSpecialBossSceneInfo()
    if not info then
        return
    end

    local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(info.monster_id)
    if boss_cfg then
        self.node_list["boss_name"].text.text = boss_cfg.name
    end

    local cfg = BossWGData.Instance:GetPersonSpecialBossSceneCfg()
    if cfg then
        local role_zhanli = RoleWGData.Instance:GetAttr("capability") or 0
        local zhanli_color = role_zhanli >= cfg.tuijian_zl and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
        self.node_list["tuijian_zhanli"].text.text = string.format(Language.FuBen.TuiJianZhanLi, ToColorStr(cfg.tuijian_zl, zhanli_color))
        self.reward_list:SetDataList(cfg.show_reward_item)
    end
end
