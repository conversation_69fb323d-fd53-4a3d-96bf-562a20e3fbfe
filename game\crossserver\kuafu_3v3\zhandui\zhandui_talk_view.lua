local ZhanDuiWorldInvite = "zhandui_world_talk" --喊话

ZhanDuiTalkView = ZhanDuiTalkView or BaseClass(SafeBaseView)
function ZhanDuiTalkView:__init()
    self:SetMaskBg()

    --self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_common_panel", {sizeDelta = Vector2(530, 266)})
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_talk")
end
function ZhanDuiTalkView:ReleaseCallBack()
    self.last_edit_time = nil
    self.data = nil
end
function ZhanDuiTalkView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.NewTeam.ViewNameTalk
    XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.OnClickChange, self))
    self.node_list.talk_input_field.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnEditValueChange, self))
end

function ZhanDuiTalkView:ShowIndexCallBack()
    self:SetDefaultTextValue()
end

--team_type, fb_mode, limit_min_level, limit_max_level
function ZhanDuiTalkView:SetData(data)
    self.data = data
end

function ZhanDuiTalkView:SetDefaultTextValue()
    local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
    local default_str = string.format(Language.ZhanDui.DefaultTalkText, zhandui_info.name)
    self.node_list.talk_input_field.input_field.text = default_str
    self.default_str = default_str
end

function ZhanDuiTalkView:OnEditValueChange(str)
    local len, table = CheckStringLen(str, COMMON_CONSTS.ZHAN_DUI_MAX_NOTICE_LEN)
    if not len then
        if table then
            local str = ""
            for i = 1, #table do
                str = str .. table[i]
            end
            self.node_list.talk_input_field.input_field.text = str
        end
        if self.last_edit_time and self.last_edit_time > Status.NowTime then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.WorldTalkMaxNum)
        self.last_edit_time = Status.NowTime + 0.5
    end
end


function ZhanDuiTalkView:OnClickChange()
    local str = string.gsub(self.node_list.talk_input_field.input_field.text, "^[ \t\n\r]+", "")	--过滤空字符串
    local len, table = CheckStringLen(str, COMMON_CONSTS.ZHAN_DUI_MAX_NOTICE_LEN)
    if not len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.WorldTalkMaxNum)
        return
    end

	local function callback()
		if str == "" then
			TipsSystemManager.Instance:ShowSystemTips(Language.Common.TeamTalkCanNotSendEmpty)
			return
		end
		--GlobalEventSystem:Fire(TeamWorldTalk.MAIN_WORLD_TALK)
        ZhanDuiWGCtrl.Instance:SendWordTalk(str)
	end
	OperateFrequency.Operate(callback, ZhanDuiWorldInvite, 10)
end
