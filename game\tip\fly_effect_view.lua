------------------------------------------------------------
--飞行粒子特效
------------------------------------------------------------
FlyEffectView = FlyEffectView or BaseClass(SafeBaseView)

local interval = 0.001					-- 生成特效间隔
local EffectCount = 8					-- 特效个数
local Section = 20						-- 区间大小
local EffScale = 0.8					-- 特效飞到目的地时大小

function FlyEffectView:__init()
	self.ui_config = {nil, "FlyEffectView"}
	self.view_layer = UiLayer.Pop
	self.open_tween = nil
	self.close_tween = nil

	self.effect_loader_list = {}

	self.tween_list = {}

	self.start_position = Vector3(0, 0, 0)
	self.end_position = Vector3(0, 0, 0)
	self.ease = DG.Tweening.Ease.Linear				--默认匀速运动
	self.duration = 1								--运行时间

	self.need_repeat = false						--重复回调
	self.effect_count = 0

	self.end_count = 0
	self.section = nil

	self.is_camber = false
	self.camber_power = 200
	self.bezier_timer_list = {}
end

function FlyEffectView:ReleaseCallBack()
	self.start_obj = nil
	self.end_obj = nil
	self.end_target_pos = nil
	self.my_uicamera = nil
	self.my_rect = nil

	self.effect_count = 0
	self.section = nil

	if self.add_time_quest then
		GlobalTimerQuest:CancelQuest(self.add_time_quest)
		self.add_time_quest = nil
	end

	if self.fly_time_quest then
		GlobalTimerQuest:CancelQuest(self.fly_time_quest)
		self.fly_time_quest = nil
	end

	if self.bezier_timer_list then
		for i,v in ipairs(self.bezier_timer_list) do
			GlobalTimerQuest:CancelQuest(self.bezier_timer_list[i])
		end
		self.bezier_timer_list = {}
	end

	self.effect_loader_list = {}

	for _, v in ipairs(self.tween_list) do
		v:Kill()
	end
	self.tween_list = {}

	if not self.need_repeat and self.complete_callback then
		self.complete_callback()
		self.complete_callback = nil
	end

	if self.check_remove_coundown ~= nil then
		GlobalTimerQuest:CancelQuest(self.check_remove_coundown)
	end

	self.check_remove_coundown = nil
	self.ease = nil

	self.parent_transform = nil

	MainuiWGCtrl.Instance:DoCleanParticleTimerFunc()
end

function FlyEffectView:SetAsset(bundle, asset)
	self.bundle = bundle
	self.asset = asset
end

function FlyEffectView:SetStartObj(obj)
	self.start_obj = obj
	if nil ~= self.start_obj then
		local obj_rect = self.start_obj:GetComponent(typeof(UnityEngine.RectTransform))
		self:SetStartObjPos(obj_rect.position)
	end
end

function FlyEffectView:SetStartObjPos(obj_pos)
	if nil ~= obj_pos then
		self.start_obj_pos = obj_pos
	end
end

function FlyEffectView:SetStartTargetPos(pos)
	if nil ~= pos then
		self.start_target_pos = pos
	end
end

function FlyEffectView:SetEndObj(obj)
	self.end_obj = obj
	if self.end_obj ~= nil and not IsNil(self.end_obj.gameObject) then
		local obj_rect = self.end_obj:GetComponent(typeof(UnityEngine.RectTransform))
		self:SetEndObjPos(obj_rect.position)
	end
end

function FlyEffectView:SetEndObjPos(obj_pos)
	if nil ~= obj_pos then
		self.end_obj_pos = obj_pos
	end
end

function FlyEffectView:SetEndTargetPos(pos)
	if nil ~= pos then
		self.end_target_pos = pos
	end
end

function FlyEffectView:SetEffectCount(effect_count)
	self.effect_count = effect_count
end

function FlyEffectView:SetSection(section)
	-- body
	self.section = section
end

function FlyEffectView:SetEndOffest(pos)
	-- body
	self.end_offest_ver2 = pos
end

function FlyEffectView:SetIsBag(is_bag)
	self.is_bag = is_bag
end

function FlyEffectView:SetStartPosition()
	self:CalcStartPos()
end

function FlyEffectView:SetEndPosition()
	self:CalcEndPos()
end

--设置速率曲线
function FlyEffectView:SetEase(ease)
	self.ease = ease
end

--花费时间
function FlyEffectView:SetDuration(duration)
	self.duration = duration
end

function FlyEffectView:SetCompleteCallBack(callback)
	self.complete_callback = callback
end

function FlyEffectView:SetNeedRepeat(need_repeat)
	self.need_repeat = need_repeat
end

function FlyEffectView:OnMoveEnd()
	self.end_count = self.end_count + 1
	if self.effect_loader_list[self.end_count] then
		self.effect_loader_list[self.end_count]:Destroy()
		self.effect_loader_list[self.end_count] = nil
	end
	if self.need_repeat and self.complete_callback then
		self.complete_callback()
	end
	if self.end_count >= self.effect_count then
		self.end_count = 0
		self:Close()
	end
end

function FlyEffectView:StartDoTween()
	local fly_count = 0
	if self.fly_time_quest then
		GlobalTimerQuest:CancelQuest(self.fly_time_quest)
		self.fly_time_quest = nil
	end

	local interval_time = interval
	if type(self.creat_max_time) == "number" then
		interval_time = math.random(1, self.creat_max_time) * 0.001
	end
	
	self.fly_time_quest = GlobalTimerQuest:AddRunQuest(function()
		if fly_count >= self.effect_count then
			GlobalTimerQuest:CancelQuest(self.fly_time_quest)
			self.fly_time_quest = nil
			return
		end
		fly_count = fly_count + 1

		local obj
		if self.effect_loader_list[fly_count] ~= nil then
			obj = self.effect_loader_list[fly_count]:GetGameObj()
		end


		if obj then
			-- 如果不是曲线飞行
			if not self.is_camber then
				local tween
				if self.is_bag then
					tween = DG.Tweening.DOTween.Sequence()
					tween:Append(obj.transform:DOLocalMove(self.end_position, self.duration))
					tween:Join(obj.transform:DOScale(Vector3(EffScale, EffScale, EffScale), self.duration))
				else
					tween = obj.transform:DOLocalMove(self.end_position, self.duration)
				end
				tween:SetEase(self.ease)
				tween:OnComplete(BindTool.Bind(self.OnMoveEnd, self))
				table.insert(self.tween_list, tween)
			-- 如果是曲线飞行
			else
				self:DOBezierMove(obj, self.end_position, BindTool.Bind(self.OnMoveEnd, self))
			end
		end
	end, interval_time)
end

function FlyEffectView:CreatEffect()
	local count = 0
	local random_min_x = self.start_position.x - self.section
	local random_max_x = self.start_position.x + self.section

	local random_min_y= self.start_position.y - self.section
	local random_max_y = self.start_position.y + self.section

	if self.add_time_quest then
		GlobalTimerQuest:CancelQuest(self.add_time_quest)
		self.add_time_quest = nil
	end

	self.add_time_quest = GlobalTimerQuest:AddRunQuest(function()
		if count >= self.effect_count then
			GlobalTimerQuest:CancelQuest(self.add_time_quest)
			self.add_time_quest = nil
			return
		end
		count = count + 1
		local loader = AllocAsyncLoader(self, "CreatFlyEffect" .. count)
		if not self.parent_transform then
			loader:SetParent(self.root_node.transform)
		else
			loader:SetParent(self.parent_transform)
		end
		loader:SetIsUseObjPool(true)
		loader:Load(self.bundle, self.asset, function(obj)
		-- GameObjectPool.Instance:SpawnAsset(self.bundle, self.asset, function(obj)
			if nil == self.root_node or nil == obj then
				if not self.need_repeat and self.complete_callback then
					self.complete_callback()
					self.complete_callback = nil
				end
				return
			end

			local x = math.random(random_min_x, random_max_x)
			local y = math.random(random_min_y, random_max_y)
			obj.transform.localPosition = Vector3(x, y, 0)
			table.insert(self.effect_loader_list, loader)
			if #self.effect_loader_list >= self.effect_count then
				self:StartDoTween()
			end
		end)
	end, interval)
end

function FlyEffectView:OpenCallBack()
	if self.check_remove_coundown ~= nil then
		GlobalTimerQuest:CancelQuest(self.check_remove_coundown)
		self.check_remove_coundown = nil
	end

	-- 最长时间5秒
	self.check_remove_coundown = GlobalTimerQuest:AddDelayTimer(function ()
		if self:IsOpen() then
			self:Close()
		end
	end, 5)

	self.end_count = 0

	self.my_uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
	self.my_rect = self.root_node:GetComponent(typeof(UnityEngine.RectTransform))

	self:SetStartPosition()
	self:SetEndPosition()
	self.effect_count = self.effect_count or EffectCount
	self.section = self.section or Section
	self:CreatEffect()
end

function FlyEffectView:CloseCallBack()
	self.start_obj = nil
	self.end_obj = nil
	self.start_obj_pos = nil
	self.end_obj_pos = nil
	self.start_target_pos = nil
	self.end_target_pos = nil
	self.my_uicamera = nil
	self.my_rect = nil
	self.effect_count = 0
	self.is_bag = nil
	self.camber_power = 200

	if self.add_time_quest then
		GlobalTimerQuest:CancelQuest(self.add_time_quest)
		self.add_time_quest = nil
	end

	if self.fly_time_quest then
		GlobalTimerQuest:CancelQuest(self.fly_time_quest)
		self.fly_time_quest = nil
	end

	for _, v in ipairs(self.effect_loader_list) do
		--ResPoolMgr:Release(v)
		v:Destroy()
		-- GameObjectPool.Instance:Free(v)
	end
	self.effect_loader_list = {}

	for _, v in ipairs(self.tween_list) do
		v:Pause()
	end
	self.tween_list = {}

	if not self.need_repeat and self.complete_callback then
		self.complete_callback()
		self.complete_callback = nil
	end

	if self.check_remove_coundown ~= nil then
		GlobalTimerQuest:CancelQuest(self.check_remove_coundown)
		self.check_remove_coundown = nil
	end

	TipWGCtrl.Instance:PopFlyEffectToPool(self, self.asset)
end

function FlyEffectView:SetIsCamber(is_camber)
	self.is_camber = is_camber
end

function FlyEffectView:SetCamberPower(camber_power)
	self.camber_power = camber_power or self.camber_power
end

-- 曲线飞行
function FlyEffectView:DOBezierMove(obj, end_pos, end_callback)
	local start_pos = obj.transform.localPosition
	if start_pos == end_pos or self.duration == 0 then
		return
	end

	local delta_x = self.start_position.x - end_pos.x
	local delta_y = self.start_position.y - end_pos.y
	local k = delta_y / delta_x
	local k_mark = (k > 0) and -1 or 1
	local b = self.start_position.y - k * self.start_position.x
	-- 判断特效起始点在飞行路线的哪一侧
	local func = function(x, y)
		return ((y > k * x + b) and -1 or 1) * -k_mark
	end

	end_pos.z = start_pos.z

	local dir = end_pos - start_pos
	local perpendicular = Vector3(0, 0, 0) 
	perpendicular.x = math.sqrt(dir.y^2 / (dir.y^2 + dir.x^2))
	perpendicular.y = k_mark * math.sqrt(1 - perpendicular.x^2)

	local mid_pos = Mathf.Lerp(start_pos, end_pos, 0.4) + perpendicular * self.camber_power * func(start_pos.x, start_pos.y)				-- 计算控制点
	mid_pos.z = start_pos.z

	local time = 0
	local index = #self.bezier_timer_list + 1
	local bezier_time_quest = GlobalTimerQuest:AddRunQuest(function()
		time = time + UnityEngine.Time.deltaTime

		if time >= self.duration or IsNil(obj.transform) then
			GlobalTimerQuest:CancelQuest(self.bezier_timer_list[index])
			if end_callback then
				end_callback()
			end
			return
		end
		obj.transform.localPosition = GameMath.CalculateSquareBezierPoint(time / self.duration, start_pos, mid_pos, end_pos)
	end, 0)
	table.insert(self.bezier_timer_list, bezier_time_quest)
end

function FlyEffectView:CalcStartPos()
	local t_pos = nil ~= self.start_obj_pos and self.start_obj_pos or self.start_target_pos
	local screen_pos_1 = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.my_uicamera, t_pos)
	local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.my_rect, screen_pos_1, self.my_uicamera, Vector2(0, 0))
	self.start_position = Vector3(local_pos_tbl.x, local_pos_tbl.y, 0)
end

function FlyEffectView:CalcEndPos()
	local t_pos = nil ~= self.end_obj_pos and self.end_obj_pos or self.end_target_pos
	local screen_pos_1 = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.my_uicamera, t_pos)
	local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.my_rect, screen_pos_1, self.my_uicamera, Vector2(0, 0))
	self.end_position = Vector3(local_pos_tbl.x, local_pos_tbl.y, 0)
end

function FlyEffectView:SetShowParentPanel(parent_transform)
	self.parent_transform = parent_transform
end

function FlyEffectView:SetCreatMaxTime(max_value)
	self.creat_max_time = max_value
end
