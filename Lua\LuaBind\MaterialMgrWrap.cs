﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class MaterialMgrWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(MaterialMgr), typeof(Nirvana.Singleton<MaterialMgr>));
		<PERSON><PERSON>Function("OnGameStartup", OnGameStartup);
		<PERSON><PERSON>un<PERSON>("IsArtEdit", IsArtEdit);
		<PERSON><PERSON>unction("IsNeedSyncMaterial", IsNeedSyncMaterial);
		<PERSON><PERSON>RegFunction("OnGameStop", OnGameStop);
		<PERSON><PERSON>RegFunction("Update", Update);
		<PERSON><PERSON>RegFunction("RegisterSyncMaterials", RegisterSyncMaterials);
		<PERSON><PERSON>RegFunction("UnRegisterSyncMaterials", UnRegisterSyncMaterials);
		L.RegFunction("GetClonedMaterials", GetClonedMaterials);
		<PERSON><PERSON>RegFunction("GetSharedMaterials", GetSharedMaterials);
		<PERSON><PERSON>Function("ResumeSharedMaterials", ResumeSharedMaterials);
		<PERSON><PERSON>("SetSharedMaterial", SetSharedMaterial);
		<PERSON><PERSON>("SetSharedMaterials", SetSharedMaterials);
		L.RegFunction("ResumeMaterialsKeywordsAndRenderQueue", ResumeMaterialsKeywordsAndRenderQueue);
		L.RegFunction("GetClonedMaterial", GetClonedMaterial);
		L.RegFunction("GetSharedMaterial", GetSharedMaterial);
		L.RegFunction("New", _CreateMaterialMgr);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateMaterialMgr(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				MaterialMgr obj = new MaterialMgr();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: MaterialMgr.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStartup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			obj.OnGameStartup();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsArtEdit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			bool o = obj.IsArtEdit();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsNeedSyncMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			bool o = obj.IsNeedSyncMaterial();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			obj.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Update(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			obj.Update();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RegisterSyncMaterials(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			string[] arg1 = ToLua.CheckStringArray(L, 3);
			string[] arg2 = ToLua.CheckStringArray(L, 4);
			obj.RegisterSyncMaterials(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnRegisterSyncMaterials(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			obj.UnRegisterSyncMaterials(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetClonedMaterials(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material[] o = obj.GetClonedMaterials(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSharedMaterials(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material[] o = obj.GetSharedMaterials(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResumeSharedMaterials(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			obj.ResumeSharedMaterials(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSharedMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material[] arg1 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 3);
			obj.SetSharedMaterial(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSharedMaterials(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material[] arg1 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 3);
			obj.SetSharedMaterials(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResumeMaterialsKeywordsAndRenderQueue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			obj.ResumeMaterialsKeywordsAndRenderQueue(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetClonedMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material o = obj.GetClonedMaterial(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSharedMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			MaterialMgr obj = (MaterialMgr)ToLua.CheckObject<MaterialMgr>(L, 1);
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			UnityEngine.Material o = obj.GetSharedMaterial(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

