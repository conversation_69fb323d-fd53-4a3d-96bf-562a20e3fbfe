-- ReviveView = ReviveView or BaseClass(SafeBaseView)

-- local ViewNameList = {
-- 	GuideModuleName.Forge, GuideModuleName.Advance, GuideModuleName.SpiritView, GuideModuleName.Goddess, GuideModuleName.BaoJu
-- }

-- function ReviveView:__init()
-- 	self:AddViewResource(0, "uis/view/commonwidgets_prefab", "BaseThreePanel")
-- 	self:AddViewResource(0, "uis/view/reviveview_prefab", "ReviveView")
-- 	self.is_modal = true
-- 	self.is_any_click_close = false
-- 	self.play_audio = true
-- end

-- function ReviveView:__delete()
-- 	if self.cell_list ~= nil then
-- 		for k, v in pairs(self.cell_list) do
-- 			v:DeleteMe()
-- 		end
-- 		self.cell_list = nil
-- 	end

-- 	if self.event ~= nil then
-- 		GlobalEventSystem:UnBind(self.event)
-- 		self.event = nil
-- 	end

-- 	self.show_free = nil
-- 	PlayerPrefsUtil.DeleteKey("fuhuo")
-- end

-- function ReviveView:CloseCallBack()
-- 	if self.event ~= nil then
-- 		GlobalEventSystem:UnBind(self.event)
-- 		self.event = nil
-- 	end

-- 	if self.count_down ~= nil then
-- 		CountDown.Instance:RemoveCountDown(self.count_down)
-- 		self.count_down = nil
-- 	end
-- 	ReviveWGData.Instance:SetKillerName("")
-- end

-- function ReviveView:OpenCallBack()
-- 	self.event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_REALIVE, BindTool.Bind1(self.OnMainRoleRevive, self))

-- 	if self.count_down ~= nil then
-- 		CountDown.Instance:RemoveCountDown(self.count_down)
-- 		self.count_down = nil
-- 	end
-- 	self:Flush()
-- 	ReviveWGData.Instance:SetLastReviveType(-1)
-- end

-- function ReviveView:LoadCallBack()
-- 	self.node_list["Bg"].rect.sizeDelta = Vector2(803, 490)

-- 	self.node_list["BtnClose"]:SetActive(false)
-- 	self.node_list["Txt"].text.text = Language.Fuhuo.ReviveName
-- 	self.node_list["ImgLocalRevive"].button:AddClickListener(BindTool.Bind(self.OnClickLocal, self))
-- 	self.node_list["ButResurgence"].button:AddClickListener(BindTool.Bind(self.OnClickLocal, self))
-- 	self.node_list["ButGratis"].button:AddClickListener(BindTool.Bind(self.OnClickFree, self))
-- 	self.node_list["ImgFreeRevive"].button:AddClickListener(BindTool.Bind(self.OnClickFree, self))
-- 	self.node_list["ButLocalRevive"].button:AddClickListener(BindTool.Bind(self.OnClickBtn3, self))
-- 	self.node_list["ImgLeftArrows"].button:AddClickListener(BindTool.Bind(self.ClickPre, self))
-- 	self.node_list["ImgRightArrows"].button:AddClickListener(BindTool.Bind(self.ClickNext, self))
-- 	self.node_list["ImgGuildReviveIcon"].button:AddClickListener(BindTool.Bind(self.OnClickGuildRevive, self))

-- 	self.node_list["ImgLocalRevive"]:SetActive(false)
-- 	self.node_list["ImgGuildReviveIcon"]:SetActive(true)
-- 	--self.node_list["TxtRevive"]:SetActive(false)
-- 	local list_delegate = self.node_list["ListView"].list_simple_delegate
-- 	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetMountNumberOfCells, self)
-- 	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshMountCell, self)
-- 	self.cell_list = {}
-- 	self.show_free = nil
-- end

-- function ReviveView:ReleaseCallBack()
-- 	if self.cell_list ~= nil then
-- 		for k, v in pairs(self.cell_list) do
-- 			v:DeleteMe()
-- 		end
-- 		self.cell_list = nil
-- 	end

-- 	if self.event ~= nil then
-- 		GlobalEventSystem:UnBind(self.event)
-- 		self.event = nil
-- 	end

-- 	if self.count_down ~= nil then
-- 		CountDown.Instance:RemoveCountDown(self.count_down)
-- 		self.count_down = nil
-- 	end

-- 	PlayerPrefsUtil.DeleteKey("fuhuo")
-- end

-- function ReviveView:GetMountNumberOfCells()
-- 	local gongneng_sort = ReviveWGData.Instance:GetGongNeng()
-- 	return #gongneng_sort
-- end

-- function ReviveView:RefreshMountCell(cell, cell_index)
-- 	local gongneng_image = ReviveWGData.Instance:GetGongNeng()
-- 	local gongneng_cell = self.cell_list[cell]
-- 	if gongneng_cell == nil then
-- 		gongneng_cell = GongNengCell.New(cell.gameObject)
-- 		self.cell_list[cell] = gongneng_cell
-- 	end
-- 	local data = {}
-- 	data.image_name = gongneng_image[cell_index+1].img_name
-- 	data.view_name = gongneng_image[cell_index+1].view_name
-- 	data.index = cell_index
-- 	gongneng_cell:SetData(data)
-- end

-- function ReviveView:ClickPre()
-- 	local position = self.node_list["ListView"].scroller.ScrollPosition
-- 	local index = self.node_list["ListView"].scroller:GetCellViewIndexAtPosition(position)
-- 	index = index - 1
-- 	self:JumpToIndex(index)
-- end

-- function ReviveView:ClickNext()
-- 	local position = self.node_list["ListView"].scroller.ScrollPosition
-- 	local index = self.node_list["ListView"].scroller:GetCellViewIndexAtPosition(position)
-- 	index = index + 1
-- 	self:JumpToIndex(index)
-- end

-- -- 点击公会复活
-- function ReviveView:OnClickGuildRevive()
-- 	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_HERE_ICON)
-- 	ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_HERE_ICON)
-- end

-- function ReviveView:JumpToIndex(index)
-- 	local max_count = self:GetMountNumberOfCells()
-- 	index = index >= max_count and max_count - 1 or index
-- 	if index < 0 then
-- 		index = 0
-- 	end
-- 	local width = self.node_list["ListView"].transform:GetComponent(typeof(UnityEngine.RectTransform)).sizeDelta.x
-- 	local space = self.node_list["ListView"].scroller.spacing
-- 	-- 当前页面可以显示的数量
-- 	local count = math.floor((width + space) / (100 + space))
-- 	if max_count <= count or index + count > max_count then
-- 		return
-- 	end

-- 	local jump_index = index
-- 	local scrollerOffset = 0
-- 	local cellOffset = 0
-- 	local useSpacing = false
-- 	local scrollerTweenType = self.node_list["ListView"].scroller.snapTweenType
-- 	local scrollerTweenTime = 0.1
-- 	local scroll_complete = nil
-- 	self.node_list["ListView"].scroller:JumpToDataIndexForce(
-- 		jump_index, scrollerOffset, cellOffset, useSpacing, scrollerTweenType, scrollerTweenTime, scroll_complete)
-- end

-- -- 原地满血复活
-- function ReviveView:OnClickLocal()
-- 	local vo = GameVoManager.Instance:GetMainRoleVo()
-- 	if self.remind_times ~= 0 then
-- 		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_HERE_ICON)
-- 		ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_HERE_ICON)
-- 	elseif not ReviveView.CanUseItem() then
-- 		local func = function ()
-- 			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_HERE_ICON)
-- 			ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_HERE_ICON)
-- 		end
-- 		local str = string.format(Language.Fuhuo.BuyFuHuo4, ReviveView.ReviveCost())
-- 		TipWGCtrl.Instance:ShowCommonAutoView("fuhuo", str, func)
-- 	end
-- end

-- function ReviveView:OnMainRoleRevive()
-- 	self:Close()
-- end
-- -- 免费复活
-- function ReviveView:OnClickFree()
-- 	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME)
-- 	ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME)
-- 	self:Close()
-- end

-- function ReviveView:OnClickBtn3()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.ClashTerritory then
-- 		local ct_info = ClashTerritoryData.Instance:GetTerritoryWarData()
-- 		local revive_cost = ClashTerritoryData.Instance:GetReviveCost()
-- 		if ct_info.current_credit >= revive_cost then
-- 			ClashTerritoryWGCtrl.Instance:SendTerritoryWarReliveFightBuy(ClashTerritoryData.ReviveType, ClashTerritoryData.ReviveGoods)
-- 			self:Close()
-- 		else
-- 			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotEnoughScore)
-- 		end
-- 	end
-- end

-- function ReviveView:OnFlush(param_t)
-- 	local diff_time = ReviveView.ReviveTime()

-- 	self.node_list["TxtKiller"].text.text = ReviveWGData.Instance:GetKillerName()
-- 	self.node_list["TxtNum"].text.text = ReviveView.ReviveCost()
-- 	self.node_list["TxtNum2"].text.text = ReviveView.ReviveCost()
-- 	self.node_list["ImgIconGold"].image:LoadSprite(ResPath.GetGoldIcon(5))
-- 	self.node_list["ImgIconGold2"].image:LoadSprite(ResPath.GetGoldIcon(5))

-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	-- self.node_list["ImgFreeRevive"]:SetActive(ReviveView.CanUseFreeRevive())
-- 	self.node_list["FreeRevive"]:SetActive(ReviveView.CanUseFreeRevive())
-- 	self.node_list["TxtNum"]:SetActive(not ReviveView.CanUseItem())
-- 	self.node_list["TxtNum2"]:SetActive(not ReviveView.CanUseItem())
-- 	UI:SetButtonEnabled(self.node_list["ButGratis"], not ReviveView.FreeReviveEnble())
-- 	self.node_list["Panel"]:SetActive(true)
-- 	self.node_list["TxtTips"]:SetActive(true)

-- 	self.node_list["PanelWorldBoss"]:SetActive(false)
-- 	self.node_list["XiuLuoTowerNode"]:SetActive(false)
-- 	self:UpdateShowBtn3()
-- 	local scene_id = Scene.Instance:GetSceneId()
-- 	local free_txt = Language.Fuhuo.FreeReviveTxt[1]
-- 	if BossWGData.IsBossScene(scene_id) then
-- 		if BossWGData.IsFamilyBossScene(scene_id) then
-- 			free_txt = Language.Fuhuo.FreeReviveTxt[2]
-- 		else
-- 			local main_role = Scene.Instance:GetMainRole()
-- 			if main_role and main_role.vo.top_dps_flag and main_role.vo.top_dps_flag > 0 then
-- 				free_txt = Language.Fuhuo.FreeReviveTxt[3]
-- 			end
-- 		end
-- 	end
-- 	self.node_list["TxtText3"].text.text = free_txt
-- 	if ReviveView.CanUseItem() then
-- 		if Scene.Instance:GetSceneType() == SceneType.Kf_XiuLuoTower and KuaFuXiuLuoTowerData.Instance:GetReviveTxt() then
-- 			local xlt_fh = KuaFuXiuLuoTowerData.Instance:GetReviveTxt()
-- 			self.node_list["XiuLuoTowerNode"]:SetActive(true)
-- 			self.node_list["TxtText3"]:SetActive(false)
-- 			local is_show_drop_des = KuaFuXiuLuoTowerData.Instance:GetCurLayerDes()
-- 			if nil ~= is_show_drop_des then
-- 				self.node_list["XiuLuoTxt2"]:SetActive(is_show_drop_des)
-- 				self.node_list["XiuLuoTxt1"]:SetActive(not is_show_drop_des)
-- 			end
-- 			self.node_list["TxtText"].text.text = xlt_fh
-- 		else
-- 			local item_count = ItemWGData.Instance:GetItemNumInBagById(ReviveDataItemId.ItemId)
-- 			local name = ItemWGData.Instance:GetItemName(ReviveDataItemId.ItemId)
-- 			self.node_list["TxtText"].text.text = name .. ":" .. item_count .. "/1"
-- 		end
-- 	else
-- 		self.node_list["TxtText"].text.text = ""
-- 	end
-- 	if BossWGData.IsMikuBossScene(Scene.Instance:GetSceneId()) and BossWGData.Instance:GetWroldBossWeary() >= 5 then
-- 		self.node_list["Panel"]:SetActive(false)
-- 		self.node_list["TxtTips"]:SetActive(false)
-- 		self.node_list["PanelWorldBoss"]:SetActive(true)

-- 		if (TimeWGCtrl.Instance:GetServerTime() - BossWGData.Instance:GetWroldBossWearyLastDie()) >= 62 then
-- 			FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME, 1) -- 第二个参数代表超时自动复活
-- 			ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME)
-- 			self:Close()
-- 			return
-- 		else
-- 			diff_time = BossWGData.Instance:GetWroldBossWearyLastDie() + 61 - TimeWGCtrl.Instance:GetServerTime()
-- 		end

-- 		if self.count_down == nil then
-- 			--self.node_list["TxtTime"].text.text = string.format("%s秒", 60) --60
-- 			self.node_list["TxtTime"].text.text = 60
-- 			self.count_down = CountDown.Instance:AddCountDown(
-- 				62, 1, function(elapse_time, total_time)
-- 					if elapse_time >= total_time then
-- 						return
-- 					end
-- 					local left_time = math.ceil(60 - elapse_time)
-- 					if left_time <= 0 then
-- 						FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME, 1) -- 第二个参数代表超时自动复活
-- 						ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME)
-- 						return
-- 					end
-- 					--self.node_list["TxtTime"].text.text = string.format("%s秒", left_time)
-- 					self.node_list["TxtTime"].text.text = left_time
-- 				end)
-- 		end
-- 		return
-- 	end

-- 	if self.count_down == nil then
-- 		self.count_down = CountDown.Instance:AddCountDown(
-- 			diff_time, 1, function(elapse_time, total_time)
-- 				local left_time = math.ceil(diff_time - elapse_time)
-- 				if left_time <= 0 then
-- 					FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME, 1) -- 第二个参数代表超时自动复活
-- 					ReviveWGData.Instance:SetLastReviveType(REALIVE_TYPE.REALIVE_TYPE_BACK_HOME)
-- 					CountDown.Instance:RemoveCountDown(self.count_down)
-- 					self.count_down = nil
-- 					self:Close()
-- 					return
-- 				end
-- 				local left_sec = math.floor(left_time)
-- 				self.node_list["TxtTips"].text.text =  string.format(Language.Fuhuo.Time, left_sec )

-- 			end)
-- 	end
-- 	self.node_list["TxtTips"].text.text = string.format(Language.Fuhuo.Time, diff_time )--diff_time
-- 	local used_times = ReviveWGData.Instance.UsedTime
-- 	local today_free_revive_num = ReviveWGData.Instance.today_free_revive_num
-- 	if used_times == today_free_revive_num then
-- 		self.show_free = false
-- 		--self.node_list["TxtNum"]:SetActive(true)
-- 		self.node_list["TxtReviveTime"]:SetActive(false)
-- 		self.remind_times = 0
-- 	else
-- 		self.show_free = true
-- 		self.node_list["TxtNum"]:SetActive(false)
-- 		self.node_list["TxtReviveTime"]:SetActive(true)
-- 		self.remind_times = today_free_revive_num - used_times
-- 		self.node_list["TxtReviveTime"].text.text = string.format(Language.Fuhuo.FreeTime, self.remind_times)
-- 	end

-- 	if scene_type == SceneType.CrossShuijing or scene_type == SceneType.ShuiJing then
-- 		local shuijing_free_revive_num = CrossCrystalData.Instance:GetActivityShuiJingFree()
-- 		local flag = shuijing_free_revive_num >= today_free_revive_num
-- 		local time = flag and today_free_revive_num or shuijing_free_revive_num
-- 		if used_times == time then
-- 			self.show_free = false
-- 			self.node_list["TxtReviveTime"]:SetActive(false)
-- 			self.remind_times = 0
-- 		else
-- 			self.show_free = true
-- 			self.node_list["TxtNum"]:SetActive(false)
-- 			self.node_list["TxtReviveTime"]:SetActive(true)
-- 			self.remind_times = time - used_times
-- 			self.node_list["TxtReviveTime"].text.text = string.format(Language.Fuhuo.FreeTime, self.remind_times)
-- 		end
-- 	end

-- 	if scene_type == SceneType.Kf_XiuLuoTower then
-- 		if not KuaFuXiuLuoTowerData.Instance:GetReviveTxt() then
-- 			self.node_list["TxtNum"]:SetActive(true)
-- 			self.show_free = false
-- 			--self.ndoe_list["TxtReviveTime"]:SetActive(false)
-- 		end
-- 		--self.node_list["TxtReviveTime"]:SetActive(true)
-- 		self.node_list["TxtReviveTime"]:SetActive(false and self.show_free)
-- 	else
-- 		self.node_list["TxtReviveTime"]:SetActive(true and self.show_free)
-- 	end
-- end

-- -- 是否能够使用公会复活
-- function ReviveView:CheckGuildRevive()
-- 	self.node_list["ImgLocalRevive"]:SetActive(false)
-- 	self.node_list["TxtRevive"]:SetActive(true)
-- 	self.node_list["ImgGuildReviveIcon"]:SetActive(true)

-- 	if GameVoManager.Instance:GetMainRoleVo().guild_id > 0 then
-- 		local rest_guild_daily_relive_times = GuildWGData.Instance:GetRestGuildTotalReviveCount() or 0
-- 		local rest_personal_revive_times = GuildWGData.Instance:GetRestPersonalGuildReviveCount() or 0
-- 		if rest_guild_daily_relive_times > 0 and rest_personal_revive_times > 0 then
-- 			self.node_list["TxtNum"]:SetActive(false)
-- 			self.node_list["TxtNum2"]:SetActivce(false)
-- 			self.node_list["TxtText2"].text.text = string.format(Language.Fuhuo.GuildRevive, rest_personal_revive_times)
-- 				self.node_list["ImgLocalRevive"]:SetActive(false)
-- 				--self.node_list["TxtRevive"]:SetActive(true)
-- 				self.node_list["ImgGuildReviveIcon"]:SetActive(true)
-- 				self.node_list["TxtNumer"].text.text = rest_guild_daily_relive_times
-- 		end
-- 	end
-- end

-- function ReviveView.CanUseItem()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.ShuiJing then
-- 		return false
-- 	elseif scene_type == SceneType.Kf_XiuLuoTower and KuaFuXiuLuoTowerData.Instance:GetReviveTxt() then
-- 		return true
-- 	end
-- 	local item_count = ItemWGData.Instance:GetItemNumInBagById(ReviveDataItemId.ItemId)
-- 	if item_count < 1 then
-- 		return false
-- 	end
-- 	return true
-- end

-- function ReviveView.ReviveTime()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.ShuiJing then
-- 		return ConfigManager.Instance:GetAutoConfig("activityshuijing_auto").other[1].relive_time
-- 	elseif IsFightSceneType[scene_type] or scene_type == SceneType.Kf_XiuLuoTower then
-- 		return 5
-- 	end
-- 	return ReviveDataTime.RevivieTime
-- end

-- function ReviveView.ReviveCost()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.Kf_XiuLuoTower then
-- 		return ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1].cross_relive_gold
-- 	end
-- 	local shop_cfg = ShopWGData.Instance:GetShopItemCfg(ReviveDataItemId.ItemId) or {}
-- 	return shop_cfg.gold or 20
-- end

-- function ReviveView.FreeReviveEnble()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.ShuiJing then
-- 		return false
-- 	end
-- 	return true
-- end

-- function ReviveView.CanUseFreeRevive()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.ShuiJing or scene_type == SceneType.CrossShuijing then
-- 		return false
-- 	end
-- 	return true
-- end

-- function ReviveView:UpdateShowBtn3()
-- 	local scene_type = Scene.Instance:GetSceneType()
-- 	if scene_type == SceneType.ClashTerritory then
-- 		self.node_list["ButLocalRevive"]:SetActive(true)
-- 		local ct_info = ClashTerritoryData.Instance:GetTerritoryWarData()
-- 		local revive_cost = ClashTerritoryData.Instance:GetReviveCost()
-- 		local color = revive_cost > ct_info.current_credit and "ff0000" or "00ff00"
-- 		self.node_list["TxtText2"].text.text = string.format(Language>ClashTerritory.ScoreCost, color, revive_cost)
-- 	else
-- 		self.node_list["ButLocalRevive"]:SetActive(false)
-- 	end
-- end

-- GongNengCell = GongNengCell or BaseClass(BaseRender)
-- function GongNengCell:__init()
-- 	self.node_list["Image"].button:AddClickListener(BindTool.Bind(self.ClickOpen, self))
-- end

-- function GongNengCell:SetData(data)
-- 	if data == nil then
-- 		return
-- 	end
-- 	self.data = data
-- 	local bundle, asset = ResPath.GetSystemIcon(self.data.image_name)
-- 	self.node_list["IconImage"].image:LoadSprite(bundle, asset .. ".png")

-- 	local word_bundle, word_asset = ResPath.GetSystemIcon(self.data.image_name .. "Name")
-- 	self.node_list["WordImage"].image:LoadSprite(word_bundle, word_asset)
-- end

-- function GongNengCell:ClickOpen()
-- 	if self.data then
-- 		ViewManager.Instance:Open(self.data.view_name)
-- 	end
-- end