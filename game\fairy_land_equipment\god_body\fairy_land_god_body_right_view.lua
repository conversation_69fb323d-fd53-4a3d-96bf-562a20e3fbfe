FairyLandGodBodyRightView = FairyLandGodBodyRightView or BaseClass(SafeBaseView)
function FairyLandGodBodyRightView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/fairy_land_equipment_ui_prefab", "layout_god_body_right_view")
end

function FairyLandGodBodyRightView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_uplevel_buy, BindTool.Bind(self.OnClickUplevelBuy, self))
	XUI.AddClickEventListener(self.node_list.btn_upgrade_buy, BindTool.Bind(self.OnClickUpgradeBuy, self))
	XUI.AddClickEventListener(self.node_list.uplevel_skill_icon, BindTool.Bind(self.OnClickUplevelSkill, self))
	XUI.AddClickEventListener(self.node_list.upgrade_skill_icon, BindTool.Bind(self.OnClickUpgradeSkill, self))
end

function FairyLandGodBodyRightView:ReleaseCallBack()
	self.right_type = nil
	self.slot = nil
end

function FairyLandGodBodyRightView:SetDataAndOpen(right_type, slot)
	self.right_type = right_type
    self.slot = slot
    self:Open()
end

function FairyLandGodBodyRightView:ShowIndexCallBack()
	self:OnToggleChanged(self.right_type)
end

function FairyLandGodBodyRightView:OnToggleChanged(right_type)
	self.node_list.uplevel_part:SetActive(GOD_BODY_ENUM.UPLEVEL_RIGHT == right_type)
	self.node_list.upgrade_part:SetActive(GOD_BODY_ENUM.UPGRADE_RIGHT == right_type)
end

function FairyLandGodBodyRightView:OnFlush()
    if self.slot == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local gb_data = fle_data:GetGodBodyData(self.slot)
    if gb_data == nil then
        return
    end

    local fle_str = Language.FairyLandEquipment
    local buy_seq = gb_data:GetRightBuySeq()
	-- 修炼特权
	local is_buy_uplevel = gb_data:GetIsBuyUplevelRight()
	local buy_uplevel_cfg = fle_data:GetGBUplevelRightCfg(buy_seq)
	if buy_uplevel_cfg then
		-- self.node_list.uplevel_desc1.text.text = buy_uplevel_cfg.up_time_per * 0.01 .. "%"
		local cap_value = fle_data:GetGodBodyUplevelRightCapability(self.slot)
		self.node_list.uplevel_cap_desc.text.text = cap_value
		self.node_list.btn_uplevel_buy_text.text.text = is_buy_uplevel and fle_str.RightBtnDesc[2]
												or 
												RoleWGData.GetPayMoneyStr(buy_uplevel_cfg.buy_price, buy_uplevel_cfg.buy_type, buy_uplevel_cfg.buy_seq)
		XUI.SetButtonEnabled(self.node_list.btn_uplevel_buy, not is_buy_uplevel)
		self.node_list["uplevel_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(buy_uplevel_cfg.skill_id))
		self.node_list["uplevel_skill_name"].text.text = buy_uplevel_cfg.name
	end

	-- 渡劫特权
    local is_buy_upgrade = gb_data:GetIsBuyUpgradeRight()
    local buy_upgrade_cfg = fle_data:GetGBUpgradeRightCfg(buy_seq)
    if buy_upgrade_cfg then
		-- self.node_list.upgrade_desc1.text.text = buy_upgrade_cfg.dujie_add_per * 0.01 .. "%"
		-- self.node_list.upgrade_desc2.text.text = buy_upgrade_cfg.return_stuff_per * 0.01 .. "%"
		local cap_value = fle_data:GetGodBodyUpgradeRightCapability(self.slot)
		self.node_list.upgrade_cap_desc.text.text = cap_value
		self.node_list.btn_upgrade_buy_text.text.text = is_buy_upgrade and fle_str.RightBtnDesc[2]
												or 
												RoleWGData.GetPayMoneyStr(buy_upgrade_cfg.buy_price, buy_upgrade_cfg.buy_type, buy_upgrade_cfg.buy_seq)
		XUI.SetButtonEnabled(self.node_list.btn_upgrade_buy, not is_buy_upgrade)
		self.node_list["upgrade_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(buy_upgrade_cfg.skill_id))
		self.node_list["upgrade_skill_name"].text.text = buy_upgrade_cfg.name
    end
end

-- 修炼特权购买
function FairyLandGodBodyRightView:OnClickUplevelBuy()
    if self.slot == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local gb_data = fle_data:GetGodBodyData(self.slot)
    if gb_data == nil then
        return
    end

    local buy_seq = gb_data:GetRightBuySeq()
    local is_buy = gb_data:GetIsBuyUplevelRight()
    local buy_cfg = fle_data:GetGBUplevelRightCfg(buy_seq)
    if buy_cfg == nil or is_buy then
        return
    end

	RechargeWGCtrl.Instance:Recharge(buy_cfg.buy_price, buy_cfg.buy_type, buy_cfg.buy_seq)
end

-- 渡劫特权购买
function FairyLandGodBodyRightView:OnClickUpgradeBuy()
    if self.slot == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local gb_data = fle_data:GetGodBodyData(self.slot)
    if gb_data == nil then
        return
    end

    local buy_seq = gb_data:GetRightBuySeq()
    local is_buy = gb_data:GetIsBuyUpgradeRight()
    local buy_cfg = fle_data:GetGBUpgradeRightCfg(buy_seq)
    if buy_cfg == nil or is_buy then
        return
    end

	RechargeWGCtrl.Instance:Recharge(buy_cfg.buy_price, buy_cfg.buy_type, buy_cfg.buy_seq)
end

-- 修炼技能
function FairyLandGodBodyRightView:OnClickUplevelSkill()
    if self.slot == nil then
        return
    end

	local fle_data = FairyLandEquipmentWGData.Instance
    local gb_data = fle_data:GetGodBodyData(self.slot)
    if gb_data == nil then
        return
    end

    local buy_seq = gb_data:GetRightBuySeq()
    local is_buy = gb_data:GetIsBuyUplevelRight()
    local buy_cfg = fle_data:GetGBUplevelRightCfg(buy_seq)
    if buy_cfg == nil  then
        return
    end

    local limit_text = is_buy and ToColorStr(Language.MultiMount.HasActive, COLOR3B.D_GREEN) or ToColorStr(Language.MultiMount.HasNotActive, COLOR3B.D_RED)
    local show_data = {
		icon = buy_cfg.skill_id,
		top_text = buy_cfg.name,					-- 技能名
		top_text_color = COLOR3B.D_GREEN,
		body_text = buy_cfg.skill_desc,							-- 当前等级技能描述
		capability = fle_data:GetGodBodyUplevelRightCapability(self.slot),
		x = 0,
		y = 0,
		set_pos2 = true,
		limit_text = limit_text,					-- （底部）限制描述
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

-- 渡劫技能
function FairyLandGodBodyRightView:OnClickUpgradeSkill()
    if self.slot == nil then
        return
    end

   	local fle_data = FairyLandEquipmentWGData.Instance
    local gb_data = fle_data:GetGodBodyData(self.slot)
    if gb_data == nil then
        return
    end

    local buy_seq = gb_data:GetRightBuySeq()
    local is_buy = gb_data:GetIsBuyUpgradeRight()
    local buy_cfg = fle_data:GetGBUpgradeRightCfg(buy_seq)
    if buy_cfg == nil then
        return
    end

    local limit_text = is_buy and ToColorStr(Language.MultiMount.HasActive, COLOR3B.D_GREEN) or ToColorStr(Language.MultiMount.HasNotActive, COLOR3B.D_RED)
    local show_data = {
		icon = buy_cfg.skill_id,
		top_text = buy_cfg.name,					-- 技能名
		top_text_color = COLOR3B.D_GREEN,
		body_text = buy_cfg.skill_desc,							-- 当前等级技能描述
		capability = fle_data:GetGodBodyUpgradeRightCapability(self.slot),
		x = 0,
		y = 0,
		set_pos2 = true,
		limit_text = limit_text,					-- （底部）限制描述
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)

end