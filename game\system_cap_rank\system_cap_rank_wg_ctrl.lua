require("game/system_cap_rank/system_cap_rank_wg_data")
require("game/system_cap_rank/system_cap_rank_sworn")
require("game/system_cap_rank/system_cap_rank_shenji")
require("game/system_cap_rank/system_cap_rank_linghe")
require("game/system_cap_rank/system_cap_rank_shengqi")
require("game/system_cap_rank/system_cap_rank_anqi")
require("game/system_cap_rank/system_cap_rank_zhuhun")
require("game/system_cap_rank/system_cap_rank_longshen")
require("game/system_cap_rank/system_cap_rank_yuling")
require("game/system_cap_rank/system_cap_rank_wuhun")
require("game/system_cap_rank/system_cap_rank_yushou")
require("game/system_cap_rank/system_cap_rank_item")
require("game/system_cap_rank/system_cap_rank_tips")

SystemCapRankWGCtrl = SystemCapRankWGCtrl or BaseClass(BaseWGCtrl)
function SystemCapRankWGCtrl:__init()
	if SystemCapRankWGCtrl.Instance then
		ErrorLog("[SystemCapRankWGCtrl] Attemp to create a singleton twice !")
	end
	SystemCapRankWGCtrl.Instance = self

    self.data = SystemCapRankWGData.New()
	self.rank_tips_view = SystemCapRankTipsView.New()
    self.sworn_rank_view = SystemCapRankSwornView.New(GuideModuleName.SystemCapRankSwornView)
    self.shenji_rank_view = SystemCapRankShenJiView.New(GuideModuleName.SystemCapRankShenJiView)
    self.linghe_rank_view = SystemCapRankLingHeView.New(GuideModuleName.SystemCapRankLingHeView)
    self.shengqi_rank_view = SystemCapRankShengQiView.New(GuideModuleName.SystemCapRankShengQiView)
    self.anqi_rank_view = SystemCapRankAnQiView.New(GuideModuleName.SystemCapRankAnQiView)
	self.zhuhun_rank_view = SystemCapRankZhuHunView.New(GuideModuleName.SystemCapRankZhuHunView)
	self.longshen_rank_view = SystemCapRankLongShenView.New(GuideModuleName.SystemCapRankLongShenView)
	self.yuling_rank_view = SystemCapRankYuLingView.New(GuideModuleName.SystemCapRankYuLingView)
	self.wuhun_rank_view = SystemCapRankWuHunView.New(GuideModuleName.SystemCapRankWuHunView)
	self.yushou_rank_view = SystemCapRankYuShouView.New(GuideModuleName.SystemCapRankYuShouView)
end

function SystemCapRankWGCtrl:__delete()
	SystemCapRankWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.sworn_rank_view then
		self.sworn_rank_view:DeleteMe()
		self.sworn_rank_view = nil
	end

	if self.shenji_rank_view then
		self.shenji_rank_view:DeleteMe()
		self.shenji_rank_view = nil
	end

	if self.linghe_rank_view then
		self.linghe_rank_view:DeleteMe()
		self.linghe_rank_view = nil
	end

	if self.shengqi_rank_view then
		self.shengqi_rank_view:DeleteMe()
		self.shengqi_rank_view = nil
	end

	if self.anqi_rank_view then
		self.anqi_rank_view:DeleteMe()
		self.anqi_rank_view = nil
	end

	if self.rank_tips_view then
		self.rank_tips_view:DeleteMe()
		self.rank_tips_view = nil
	end

	if self.zhuhun_rank_view then
		self.zhuhun_rank_view:DeleteMe()
		self.zhuhun_rank_view = nil
	end

	if self.longshen_rank_view then
		self.longshen_rank_view:DeleteMe()
		self.longshen_rank_view = nil
	end

	if self.yuling_rank_view then
		self.yuling_rank_view:DeleteMe()
		self.yuling_rank_view = nil
	end

	if self.wuhun_rank_view then
		self.wuhun_rank_view:DeleteMe()
		self.wuhun_rank_view = nil
	end

	if self.yushou_rank_view then
		self.yushou_rank_view:DeleteMe()
		self.yushou_rank_view = nil
	end
end


function SystemCapRankWGCtrl:OpenRankTipsView(rank_type)
	RankWGCtrl.Instance:SendGetPersonRankListReq(rank_type, nil)
	self.rank_tips_view:Open()
end