-- 锁屏
UnlockView = UnlockView or BaseClass(SafeBaseView)

local UnityApplication = UnityEngine.Application
local UnityRuntimePlatform = UnityEngine.RuntimePlatform

function UnlockView:__init()
	self.view_layer = UiLayer.Standby
	self:AddViewResource(0, "uis/view/setting_ui_prefab", "UnlockView")
end

function UnlockView:LoadCallBack()
	self.node_list["Slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderChange, self))
	local event_listener = self.node_list["Slider"].event_trigger_listener
	event_listener:AddPointerDownListener(BindTool.Bind(self.TouchDownEvent, self))
	event_listener:AddPointerUpListener(BindTool.Bind(self.TouchUpEvent, self))
	self.slider_value = 0
	self.node_list["Slider"].slider.value = 0
end

function UnlockView:ReleaseCallBack()
end

function UnlockView:OpenCallBack()
	self.slider_value = 0
	SettingWGCtrl.Instance:RemoveTimer()

	-- local main_fight_state = MainuiWGCtrl.Instance:GetFightToggleState()
	-- SettingWGData.Instance:SetFightToggleState(not main_fight_state)
	-- if not main_fight_state then
	-- 	GlobalEventSystem:Fire(MainUIEventType.CHNAGE_FIGHT_STATE_BTN, true)
	-- end

	-- 打开锁屏的时候手机帧频降到15
	local platform = UnityApplication.platform
	if platform == UnityRuntimePlatform.IPhonePlayer or platform == UnityRuntimePlatform.Android then
		QualityManager.Instance:SetCustomTargetFrame(15)
	end
end

function UnlockView:CloseCallBack()
	self.slider_value = 0
	if self.node_list["Slider"] then
		self.node_list["Slider"].slider.value = 0
	end

	if SettingWGData.Instance:GetNeedLuckView() then
		SettingWGCtrl.Instance:AddTimer()
	end

	-- 取消锁屏的时候手机帧频恢复到30
	local platform = UnityApplication.platform
	if platform == UnityRuntimePlatform.IPhonePlayer or platform == UnityRuntimePlatform.Android then
		QualityManager.Instance:SetCustomTargetFrame(-1)
	end
end

function UnlockView:TouchUpEvent()
	if self.node_list["Slider"] then
		self.node_list["Slider"].slider:DOValue(0, 0.1, false)
	end
end

function UnlockView:TouchDownEvent()
end

function UnlockView:OnSliderChange(value)
	self.slider_value = value
	if self.node_list["ShowImage"] then
		self.node_list["ShowImage"].text.color = Color.New(1, 1, 1, 1 - value)
	end

	if value == 1 then
		local screen_bright = SettingWGData.Instance:GetScreenBright()
		if screen_bright > 0 then
			DeviceTool.SetScreenBrightness(screen_bright)
		end
		-- local fight_toggle_state = SettingWGData.Instance:GetFightToggleState()
		-- if fight_toggle_state then
		-- 	GlobalEventSystem:Fire(MainUIEventType.CHNAGE_FIGHT_STATE_BTN, false)
		-- end
		self:Close()
	end
end

function UnlockView:OnFlush()
end
