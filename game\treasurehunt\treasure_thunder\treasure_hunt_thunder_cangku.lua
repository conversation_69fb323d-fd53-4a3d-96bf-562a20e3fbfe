TreasureHuntThunderStorageView = TreasureHuntThunderStorageView or BaseClass(SafeBaseView)

function TreasureHuntThunderStorageView:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "TreasureHuntThunderStorageView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, -26), sizeDelta = Vector2(814, 524)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_thunder_storage")
    self:SetMaskBg(true, true)
    self.is_sorting = false
end

function TreasureHuntThunderStorageView:ReleaseCallBack()
    if self.storage_grid then
        self.storage_grid:DeleteMe()
        self.storage_grid = nil
    end

    if self.delay_ckbag_tip ~= nil then
        GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
        self.delay_ckbag_tip = nil
    end

    self.is_sorting = false
    if CountDownManager.Instance:HasCountDown("sort_treasure_thunder_storage") then
        CountDownManager.Instance:RemoveCountDown("sort_treasure_thunder_storage")
    end
end

function TreasureHuntThunderStorageView:LoadCallBack()
    self.open_num = 207
    self.node_list.title_view_name.text.text = Language.TreasureHunt.StorageTitle
    if not self.storage_grid then
		self.storage_grid = AsyncBaseGrid.New()
        self.storage_grid:SetIsShowTips(false)        
        self.storage_grid:SetStartZeroIndex(false)              
		self.storage_grid:CreateCells({col = 9, cell_count = self.open_num, list_view = self.node_list["ph_bag_grid"],itemRender = TreasureHuntThunderBagCell})
		self.storage_grid:SetSelectCallBack(BindTool.Bind1(self.SelectBagCellCallBack, self))
    end

    self.node_list["btn_onekey_getout"].button:AddClickListener(BindTool.Bind(self.OnClickGetOut, self))
    self.node_list["btn_sort"].button:AddClickListener(BindTool.Bind(self.OnClickSort, self))
end

function TreasureHuntThunderStorageView:SelectBagCellCallBack(cell)
    if nil == cell then
		return
	end
	if nil == cell.data or not next(cell.data) then 
        return 
    end

	if self.delay_ckbag_tip then
        TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.GET_ONE_STORGE, cell:GetIndex() -1)
		GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
		self.delay_ckbag_tip = nil
		return
    end

	self.delay_ckbag_tip = GlobalTimerQuest:AddDelayTimer(function ()
        -- 在双击的0.2秒内 一键取出  会导致data数据为空 加个容错处理
        if cell.data and cell.data.item_id and cell.data.item_id > 0 then
            TipWGCtrl.Instance:OpenItem(cell.data, ItemTip.FROM_HUNT_THUNDER_STORGE,{fromIndex = cell:GetIndex()},nil)
        end
		GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
		self.delay_ckbag_tip = nil
	end,0.2)
end

function TreasureHuntThunderStorageView:OnClickSort()
    if self.is_sorting then
        return
    end

    TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.SORT_STORGE)
    self.relive_time = TimeWGCtrl.Instance:GetServerTime() + 5
    if CountDownManager.Instance:HasCountDown("sort_treasure_thunder_storage") then
        CountDownManager.Instance:RemoveCountDown("sort_treasure_thunder_storage")
    end

    CountDownManager.Instance:AddCountDown("sort_treasure_thunder_storage",
            BindTool.Bind1(self.UpdateCountDownTime, self),
            BindTool.Bind(self.CompleteCountDownTime, self, true),
            self.relive_time, nil, 0.5)
end

function TreasureHuntThunderStorageView:UpdateCountDownTime(elapse_time, total_time)
    self.is_sorting = true
    local last_time = math.ceil(total_time - elapse_time)
    XUI.SetButtonEnabled(self.node_list["btn_sort"], false)
    self.node_list["sort_text"].text.text = string.format(Language.Common.TimeStr8, last_time) 
end

function TreasureHuntThunderStorageView:CompleteCountDownTime()
    self.is_sorting = false
    if CountDownManager.Instance:HasCountDown("sort_treasure_thunder_storage") then
        CountDownManager.Instance:RemoveCountDown("sort_treasure_thunder_storage")
    end

    self.node_list["sort_text"].text.text = Language.TreasureHunt.Sort
    XUI.SetButtonEnabled(self.node_list["btn_sort"], true)
end

function TreasureHuntThunderStorageView:OnClickGetOut()
    TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.GET_ALL_STORGE)
end

function TreasureHuntThunderStorageView:OnFlush(param)
    local storage_grid_list = TreasureHuntThunderWGData.Instance:GetStorageItemList()
    self.storage_grid:SetDataList(storage_grid_list)
end

----------------------------------------------------------------------------------
TreasureHuntThunderBagCell = TreasureHuntThunderBagCell or BaseClass(ItemCell)
function TreasureHuntThunderBagCell:__delete()
    
end

function TreasureHuntThunderBagCell:LoadCallBack()
    
end

function TreasureHuntThunderBagCell:SetData(data)
    ItemCell.SetData(self,data)
end
