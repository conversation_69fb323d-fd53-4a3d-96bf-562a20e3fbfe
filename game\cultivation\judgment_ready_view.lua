JudgmentReadyView = JudgmentReadyView or BaseClass(SafeBaseView)

function JudgmentReadyView:__init()
	self:SetMaskBg(true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -22), sizeDelta = Vector2(600, 470)})
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_judgment_ready_view")

    
end

function JudgmentReadyView:ReleaseCallBack()

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function JudgmentReadyView:LoadCallBack()
    if not self.item_list then
        self.item_list = AsyncListView.New(JudgmentReadyItemRender, self.node_list.item_list)
    end
end

function JudgmentReadyView:OnFlush()
    local item_list = CultivationWGData.Instance:GetXiuweiToHpData()
    -- 无限列表
	if self.item_list then
		self.item_list:SetDataList(item_list)
    end

    
    local cur_exp = CultivationWGData.Instance:GetXiuWeiExp()
	self.node_list.text_have.text.text = string.format(Language.Cultivation.ExpHave,cur_exp)
end

------------------------


JudgmentReadyItemRender = JudgmentReadyItemRender or BaseClass(BaseRender)

function JudgmentReadyItemRender:LoadCallBack()

    self.item_cell = ItemCell.New(self.node_list["icon"])
    XUI.AddClickEventListener(self.node_list.btn_select,BindTool.Bind(self.OnClickBtnSelect, self))
end

function JudgmentReadyItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function JudgmentReadyItemRender:OnFlush()
	local data = self:GetData()
	if not data then
        self.view:CustomSetActive(false)
		return
	end 
	

	self.node_list.text_xiuwei.text.text = string.format(Language.Cultivation.JudgmentReadyStr1,data.xiuwei)
	self.node_list.text_hp.text.text = string.format(Language.Cultivation.JudgmentReadyStr2,data.hp)
    self.node_list.img_icon.image:LoadSprite(ResPath.GetItem(data.item_id))
end


function JudgmentReadyItemRender:OnClickBtnSelect()
    local cur_xiuwei = CultivationWGData.Instance:GetXiuWeiExp()
    if cur_xiuwei < self.data.xiuwei then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.JudgmentReadyTips)
        return
    end
    TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Cultivation.JudgmentTips,self.data.xiuwei), function()
		CultivationWGCtrl.Instance:ExchangeHp(self.data)
	end)
end