CommonEffectView = CommonEffectView or BaseClass(SafeBaseView)
function CommonEffectView:__init()
    self:AddViewResource(0, "uis/view/common_effect_prefab", "layout_common_effect")
    self.view_layer = UiLayer.PopTop
    self.play_queue = {}
    self.view_name = "CommonEffectView"
    self.open_tween = nil
	self.close_tween = nil
    self.view_cache_time = 0
end

function CommonEffectView:ReleaseCallBack()
    if self.close_timer then
        GlobalTimerQuest:CancelQuest(self.close_timer)
        self.close_timer = nil
    end
    if self.close_self_tween then
        self.close_self_tween:Kill()
        self.close_self_tween = nil
    end
    if self.scale_tweener then
        self.scale_tweener:Kill()
        self.scale_tweener = nil
    end
    if Runner.Instance:IsExistRunObj(self) then
        Runner.Instance:RemoveRunObj(self)
    end
    self.play_queue = {}
    self.old_asset_name = nil
    self.last_show_effect_time = nil
    self.canvas_group = nil
    self.old_parent_node = nil
end

function CommonEffectView:LoadCallBack()
    Runner.Instance:AddRunObj(self)
    self.canvas_group = self.node_list.root_obj.canvas_group
end

function CommonEffectView:CloseCallBack()
    if self.node_list["root_obj"] and self.node_list["layout_common_effect_root"] and not IsNil(self.node_list["root_obj"].transform) then
        self.node_list["root_obj"].transform:SetParent(self.node_list["layout_common_effect_root"].transform, false)
    end
    self.old_parent_node = nil
end

function CommonEffectView:SetData(data)
    if not data then
        print_error("播放特效的时候，没有设置 data")
        return
    end

    if not data.effect_type and not data.is_eff then
        print_error("播放特效的时候，没有设置 effect_type")
        return
    end

    if data.is_success == nil or type(data.is_success) ~= "boolean" then
        return
    end

    local effect_data = {}
    effect_data.effect_type = data.effect_type
    effect_data.parent_node = data.parent_node
    effect_data.is_success = data.is_success
    effect_data.pos = data.pos
    effect_data.is_eff = data.is_eff or false
    effect_data.eff_bundle = data.eff_bundle
    effect_data.eff_asset = data.eff_asset
    table.insert(self.play_queue, effect_data)
    if self.close_timer then
        GlobalTimerQuest:CancelQuest(self.close_timer)
        self.close_timer = nil
    end
    if self.close_self_tween then
        self.close_self_tween:Kill()
        self.close_self_tween = nil
    end
    if self.scale_tweener then
        self.scale_tweener:Kill()
        self.scale_tweener = nil
    end
    self:Open()
end

function CommonEffectView:Update(now_time, elapse_time)
    if self.last_show_effect_time and self.last_show_effect_time > now_time then
		return
	end
    if #self.play_queue <= 0 then
        return
    end
    self.last_show_effect_time = now_time + 0.3  --一秒弹一次
    local data = table.remove(self.play_queue, 1)
    -- self:ShowEffect(data)
    self:Flush(nil, "all", {data = data})
end

function CommonEffectView:OnFlush(param_list)
    for k,v in pairs(param_list) do
        if k == "all" then
            if v ~= nil and v.data ~= nil then
                self:ShowEffect(v.data)
            end
        end
    end
end

function CommonEffectView:ShowEffect(data)
    if not self.node_list["root_obj"] then
        return
    end
    if IsNil(self.node_list["root_obj"].gameObject) then
        return
    end

    if not data.is_eff then
        --特效
        local bundle, asset = ResPath.GetUIEffect(data.is_success and Ui_Effect.UI_common_cg_A3 or Ui_Effect.UI_common_sb_A3)
        if self.old_asset_name ~= asset then
            self.node_list.effect_common:ChangeAsset(bundle, asset)
            self.node_list.effect_common:SetActive(true)
        else
            self.node_list.effect_common:SetActive(false)
            self.node_list.effect_common:SetActive(true)
        end
        self.old_asset_name = asset
        self.node_list.root_effect:SetActive(false)
    else
        self.node_list.root_effect:SetActive(true)
    end
   

    --父节点
    if data.parent_node and data.parent_node.transform and self.old_parent_node ~= data.parent_node then
        self.node_list["root_obj"].transform:SetParent(data.parent_node.transform)
        self.old_parent_node = data.parent_node
    end
    self.node_list["tween_node"].transform.localScale = Vector3.one

    --位置
    local pos = data.pos
    if not pos then
        pos = Vector2.zero
    end
    self.node_list["root_obj"].rect.anchoredPosition = pos

    if not data.is_eff then
        self.node_list["effect_img"]:SetActive(true)
        --特效图片
        local b, a = ResPath.GetRawImagesPNG(data.effect_type)
        self.node_list["effect_img"].raw_image:LoadSprite(b, a, function()
            self.node_list.effect_img.raw_image:SetNativeSize()
        end)
    else
        EffectManager.Instance:PlayAtTransform(data.eff_bundle, data.eff_asset, self.node_list["root_effect"].transform)
    end

    self.canvas_group.alpha = 1

    if self.scale_tweener then
        self.scale_tweener:Kill()
        self.scale_tweener = nil
    end
    if self.close_timer then
        GlobalTimerQuest:CancelQuest(self.close_timer)
        self.close_timer = nil
    end
    if self.close_self_tween then
        self.close_self_tween:Kill()
        self.close_self_tween = nil
    end
    --胜利和失败两种表现
    if data.is_success then
        self:SuccessTween()
    else
        self:FailureTween()
    end
end

function CommonEffectView:ForceHideEffect()
    if not self:IsOpen() or not self:IsLoadedIndex(0) then return end
    if self.scale_tweener then
        self.scale_tweener:Kill()
        self.scale_tweener = nil
    end
    if self.close_timer then
        GlobalTimerQuest:CancelQuest(self.close_timer)
        self.close_timer = nil
    end
    if self.close_self_tween then
        self.close_self_tween:Kill()
        self.close_self_tween = nil
    end
    self.play_queue = {}
    self:Close()
end

function CommonEffectView:SuccessTween()
    self.node_list["tween_node"].transform.localScale = Vector3(2, 2, 2)
    self.scale_tweener = self.node_list["tween_node"].transform:DOScale(1, 0.2)
    if #self.play_queue <= 0 then
        self.scale_tweener:OnComplete(function()
            self.close_timer = GlobalTimerQuest:AddDelayTimer(function()
                self.close_self_tween = self.canvas_group:DoAlpha(1, 0, 0.5):OnComplete(function()
                    self:Close()
                end)
            end, 1)
        end)
    end
end

function CommonEffectView:FailureTween()
    if #self.play_queue <= 0 then
        self.close_timer = GlobalTimerQuest:AddDelayTimer(function()
            self.close_self_tween = self.canvas_group:DoAlpha(1, 0, 0.5):OnComplete(function()
                self:Close()
            end)
        end, 1)
    end
end
