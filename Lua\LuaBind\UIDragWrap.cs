﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UIDragWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UIDrag), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("SetDragData", SetDragData);
		<PERSON><PERSON>Function("GetDragData", GetDragData);
		<PERSON><PERSON>unction("SetIsCanDrag", SetIsCanDrag);
		<PERSON><PERSON>unction("SetIsCanDrop", SetIsCanDrop);
		<PERSON><PERSON>Function("ListenBeginDragCallback", ListenBeginDragCallback);
		<PERSON><PERSON>Function("UnListenBeginDragCallback", UnListenBeginDragCallback);
		<PERSON><PERSON>unction("ListenEndDragCallback", ListenEndDragCallback);
		<PERSON><PERSON>RegFunction("UnListenEndDragCallback", UnListenEndDragCallback);
		<PERSON><PERSON>unction("ListenDropCallback", ListenDropCallback);
		<PERSON><PERSON>un<PERSON>("UnListenDropCallback", UnListenDropCallback);
		<PERSON><PERSON>unction("OnBeginDrag", OnBeginDrag);
		L.RegFunction("OnDrag", OnDrag);
		L.RegFunction("OnEndDrag", OnEndDrag);
		L.RegFunction("OnDrop", OnDrop);
		L.RegFunction("Resume", Resume);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegFunction("DropCallBackAction", UIDrag_DropCallBackAction);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDragData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			object arg0 = ToLua.ToVarObject(L, 2);
			obj.SetDragData(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDragData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			object o = obj.GetDragData();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsCanDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsCanDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsCanDrop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsCanDrop(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenBeginDragCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.ListenBeginDragCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnListenBeginDragCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.UnListenBeginDragCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenEndDragCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.ListenEndDragCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnListenEndDragCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.UnListenEndDragCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenDropCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			UIDrag.DropCallBackAction arg0 = (UIDrag.DropCallBackAction)ToLua.CheckDelegate<UIDrag.DropCallBackAction>(L, 2);
			obj.ListenDropCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnListenDropCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			UIDrag.DropCallBackAction arg0 = (UIDrag.DropCallBackAction)ToLua.CheckDelegate<UIDrag.DropCallBackAction>(L, 2);
			obj.UnListenDropCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBeginDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnBeginDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnEndDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnEndDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrop(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Resume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UIDrag obj = (UIDrag)ToLua.CheckObject<UIDrag>(L, 1);
			obj.Resume();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UIDrag_DropCallBackAction(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<UIDrag.DropCallBackAction>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<UIDrag.DropCallBackAction>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

