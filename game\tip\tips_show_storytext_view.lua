TipsShowStoryTextView = TipsShowStoryTextView or BaseClass(SafeBaseView)

function TipsShowStoryTextView:__init()
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/taskstorytexttip_prefab", "task_story")
end

function TipsShowStoryTextView:__delete()
end

function TipsShowStoryTextView:ReleaseCallBack()
	if self.show_delay_timer then
		GlobalTimerQuest:CancelQuest(self.show_delay_timer)
		self.show_delay_timer = nil
	end
	self.task_info = nil 
end

function TipsShowStoryTextView:LoadCallBack()
end

function TipsShowStoryTextView:ShowIndexCallBack()
	self:Flush()
end

function TipsShowStoryTextView:SetTaskInfo(task_info)
	self.task_info = task_info 
	self:Open()
end

function TipsShowStoryTextView:OnFlush()
	if not self.task_info then return end
	self.node_list.strory_img.image:LoadSprite('uis/view/tips/taskstorytexttip/images_atlas',"task_story_img_" .. self.task_info,function ()
		self.node_list.strory_img.image:SetNativeSize()
	end)
	if self.show_delay_timer then
		GlobalTimerQuest:CancelQuest(self.show_delay_timer)
		self.show_delay_timer = nil
	end
	self.node_list.Frame.canvas_group.alpha = 1
	self.show_delay_timer = GlobalTimerQuest:AddTimesTimer(function ()
		local tweener = self.node_list.Frame.canvas_group:DoAlpha(1, 0,1.5)
		tweener:OnComplete(function ()
			self:Close()
		end)
	end, 2, 1)
end

