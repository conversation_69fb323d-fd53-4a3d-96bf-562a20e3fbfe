HolyDarkEquipAttrView = HolyDarkEquipAttrView or BaseClass(SafeBaseView)

function HolyDarkEquipAttrView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "holy_dark_euqip_attr")
end

function HolyDarkEquipAttrView:LoadCallBack()
    self.cur_attr_list = {}
	self.next_attr_list = {}
    for i = 1, 5 do
		self.cur_attr_list[i] = HolyDarkEquipAddAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = HolyDarkEquipAddAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end

    self.node_list["left_active_btn"].button:AddClickListener(BindTool.Bind(self.OnClickActive, self))
end

function HolyDarkEquipAttrView:ReleaseCallBack()
    if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	    self.cur_attr_list = nil
	end

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	    self.next_attr_list = nil
	end

    self.relic_seq = nil

end

function HolyDarkEquipAttrView:SetDataAndOpen(relic_seq)
    self.relic_seq = relic_seq
    self:Open()
end

function HolyDarkEquipAttrView:OnClickActive()
    if not self.relic_seq then
        return
    end

    HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.ACTIVE_SUIT, self.relic_seq)
end

function HolyDarkEquipAttrView:OnFlush()
    if not self.relic_seq then
        return
    end

    local suit_level = HolyDarkWeaponWGData.Instance:GetRelicSuitLevel(self.relic_seq)
    local cur_level_suit_cfg = HolyDarkWeaponWGData.Instance:GetRelicSuitCfg(self.relic_seq, suit_level)
    local next_level_suit_cfg = HolyDarkWeaponWGData.Instance:GetRelicSuitCfg(self.relic_seq, suit_level + 1)
    local cur_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSuitAttrList(self.relic_seq, suit_level)
    local next_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSuitAttrList(self.relic_seq, suit_level + 1)

    self.node_list["no_attr_tip"]:SetActive(cur_level_suit_cfg == nil)
    self.node_list["max_attr_tip"]:SetActive(next_level_suit_cfg == nil)
    
    local is_active = HolyDarkWeaponWGData.Instance:GetRelicSuitRemind(self.relic_seq)
    XUI.SetButtonEnabled(self.node_list["left_active_btn"], is_active)
    self.node_list["remind"]:SetActive(is_active)
    
    if cur_level_suit_cfg ~= nil then
        local cur_str = string.format(Language.HolyDarkWeapon.EquipSuitActDesc, Language.Common.ColorName[cur_level_suit_cfg.color])
        local enough_cur_num = HolyDarkWeaponWGData.Instance:GetRelicSuitEnoughColorNum(self.relic_seq, cur_level_suit_cfg.color)
        local color = enough_cur_num >= cur_level_suit_cfg.need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        local cur_act_str = string.format(Language.HolyDarkWeapon.EquipSuitCurAct, enough_cur_num, cur_level_suit_cfg.need_num)
        self.node_list["now_level"].text.text = cur_str .. ToColorStr(cur_act_str, color)
    else
        self.node_list["now_level"].text.text = ""
    end

    if next_level_suit_cfg ~= nil then
        local next_str = string.format(Language.HolyDarkWeapon.EquipSuitActDesc, Language.Common.ColorName[next_level_suit_cfg.color])
        local enough_next_num = HolyDarkWeaponWGData.Instance:GetRelicSuitEnoughColorNum(self.relic_seq, next_level_suit_cfg.color)
        local next_color = enough_next_num >= next_level_suit_cfg.need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        local next_act_str = string.format(Language.HolyDarkWeapon.EquipSuitCurAct, enough_next_num, next_level_suit_cfg.need_num)
        self.node_list["next_level"].text.text = next_str .. ToColorStr(next_act_str, next_color)
    else
        self.node_list["next_level"].text.text = ""
    end

    for i = 1, 5 do
        self.cur_attr_list[i]:SetData(cur_attr_list[i])
		self.next_attr_list[i]:SetData(next_attr_list[i])
    end
end

HolyDarkEquipAddAttrRender = HolyDarkEquipAddAttrRender or BaseClass(BaseRender)
function HolyDarkEquipAddAttrRender:OnFlush()
	if not IsEmptyTable(self.data) then
        local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
        local per_desc = is_per and "%" or ""
        local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
        self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
        self.node_list.attr_value.text.text = string.format("%s%s", value_str, per_desc)
        self.view:SetActive(true)
	else
		self.view:SetActive(false)
	end
end