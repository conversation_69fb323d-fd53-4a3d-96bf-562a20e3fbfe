LingHeDrawMainBtn = LingHeDrawMainBtn or BaseClass(BaseRender)

local linghe_draw_mainbtn_key = "linghe_draw_mainbtn_key"

function LingHeDrawMainBtn:LoadCallBack()
    
end

function LingHeDrawMainBtn:__delete()
    self:BLLHRemoveCountDown()
end

function LingHeDrawMainBtn:OnFlush()
    --倒计时
    self:BLLHRemoveCountDown()
    local time = 0

    if TianShenLingHeWGData.Instance:GetLingHeShopOpenState() ~= 0 then --开启中
        self.node_list["fanli_open"]:SetActive(true)
        local open_time, end_time = TianShenLingHeWGData.Instance:GetLingHeShopOpenTime()
        time = end_time
    else
        self.node_list["fanli_open"]:SetActive(false)
    end

    if time > 0 then
        local tedian_act_time = time - TimeWGCtrl.Instance:GetServerTime()
        if tedian_act_time > 0 then
            self:UpdateTime(0, tedian_act_time)
            CountDownManager.Instance:AddCountDown(linghe_draw_mainbtn_key, BindTool.Bind(self.UpdateTime, self),
                                                    BindTool.Bind(self.CompleteTime, self), nil, tedian_act_time, 1)
        end
    end
end

function LingHeDrawMainBtn:UpdateTime(elapse_time, total_time)
    self:SetActTimeDes(TimeUtil.FormatSecondDHM5(total_time - elapse_time))
end

function LingHeDrawMainBtn:CompleteTime()
    self:SetActTimeDes()
end


function LingHeDrawMainBtn:SetActTimeDes(txt)
    if self.node_list and self.node_list["lh_call_time"] then
        self.node_list["lh_call_time"].text.text = txt or ""
    end
end

function LingHeDrawMainBtn:BLLHRemoveCountDown()
    if CountDownManager.Instance:HasCountDown(linghe_draw_mainbtn_key) then
        CountDownManager.Instance:RemoveCountDown(linghe_draw_mainbtn_key)
    end
    self:SetActTimeDes()
end
