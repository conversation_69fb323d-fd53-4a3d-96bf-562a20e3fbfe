EquipStrengthItemRender = EquipStrengthItemRender or BaseClass(BaseRender)
function EquipStrengthItemRender:__init()
	self.item_cell = ItemCell.New(self.node_list.item_node)
	self.item_cell:SetIsShowTips(false)

	self.node_list.select_img:SetActive(true)
	self:OnSelectChange(false)
	XUI.AddClickEventListener(self.node_list.block_click, BindTool.Bind(self.OnClick, self))
end

function EquipStrengthItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipStrengthItemRender:OnSelectChange(is_select)
	self.node_list.select_img.image.enabled = is_select
end

function EquipStrengthItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.item_cell:ClearData()
		self.item_cell:SetItemIcon(ResPath.GetEquipIcon(self.index))
        -- local scale = self.index == 5 and Vector3.one * 0.7 or Vector3.one
		-- self.item_cell:SetItemIconLocalScale(scale)

		self.node_list.remind:SetActive(false)
		self.node_list.level_text.text.text = ""
		return
	end

	self.item_cell:SetData(self.data)

	local level = self.data.strengthen_level or 0
	if level > 0 then
		self.node_list.level_text.text.text = "+" .. level
	else
		self.node_list.level_text.text.text = Language.Equip.NoStrengthen
	end

	local equip_body_index = self.data.index
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local can_strength = EquipmentWGData.Instance:IsCanStrengthEquip(equip_body_seq, equip_body_index)
	self.node_list.remind:SetActive(can_strength)
end


-- -- 锻造强化的itemrender
-- EquipStrengthItemRender = EquipStrengthItemRender or BaseClass(EquipBaseRender)
-- function EquipStrengthItemRender:OtherFlush()
-- 	local level = self.data.strengthen_level or 0
-- 	if level > 0 then
-- 		self.node_list.level_text.text.text = "+" .. level
-- 	else
-- 		self.node_list.level_text.text.text = Language.Equip.NoStrengthen
-- 	end

-- 	self.node_list.remind:SetActive(self.data.can_strength)
-- end

-- function EquipStrengthItemRender:NoDataFlush()
-- 	self.node_list.remind:SetActive(false)
-- 	self.node_list.level_text.text.text = ""
	
-- end


-------------------------------------------------------------------------
-- 宝石镶嵌的itemrender--------------------------------------------------
-------------------------------------------------------------------------
EquipBaoShiItemRender = EquipBaoShiItemRender or BaseClass(BaseRender)
function EquipBaoShiItemRender:__init()
	self.old_item_id = 0
	self:CreateChild()
end

function EquipBaoShiItemRender:__delete()
	self.old_item_id = 0
	self.old_item_level = -1
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.lbl_name = nil
	self.upflag_icon = nil
	self.is_jinlian_item = nil
end

function EquipBaoShiItemRender:CreateChild()
	self.item_cell = ItemCell.New(self.node_list["ph_item"])
	self.item_cell:SetIsShowTips(false)

	if self:IsSelect() then
		self:OnSelectChange(true)
	end
end

function EquipBaoShiItemRender:OnFlush()
	if nil == self.data or nil == self.data.param then
		return
	end
	self:SetUpDownIconVisible(false)

	if self.item_cell and self.data.item_id ~= self.old_item_id then
		self.item_cell:SetData(self.data)
	end
	self.old_item_id = self.data.item_id
	local equipmentdata_instance = EquipmentWGData.Instance

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		if self.is_jinlian_item then
			local refine_level = equipmentdata_instance:GetStoneRefineLevelByPart(self.data.index)
			local level_str = refine_level > 0 and " +" .. refine_level or ""
			item_name = item_name .. level_str
		end
		self.node_list["lbl_name"].text.text = item_name
		self.node_list["lbl_name_hl"].text.text = item_name
	end

	local stone_info = equipmentdata_instance:GetStoneInfoListByIndex(self.data.index)
	if nil == stone_info then
		return
	end

	local stone_remind_list = {}
	local show_stone_list = {}
	for i= 0, GameEnum.MAX_STONE_COUNT - 1 do
		local is_open = equipmentdata_instance:BaoShiSlotIsOpen(self.data.index, i)
		if is_open then
			table.insert(show_stone_list, stone_info[i])
		end

		if not self.is_jinlian_item then
			local is_have_batter = equipmentdata_instance:GetStoneSlotHaveBatterState(self.data.index, i)
			local is_can_upgrade = equipmentdata_instance:GetStoneSlotCanUpgradeState(self.data.index, i)
			stone_remind_list[i] = is_have_batter or is_can_upgrade
		end
	end

	if self.is_jinlian_item then
		local remind_refine = EquipmentWGData.Instance:GetBSJLRemindByPart(self.data.index)
		self:SetUpDownIconVisible(remind_refine)
	else
		for i = 0, GameEnum.MAX_STONE_COUNT - 1 do
			if stone_remind_list[i] then
				self:SetUpDownIconVisible(true)
				break
			end
		end
	end

	if not IsEmptyTable(show_stone_list) then
		table.sort(show_stone_list, SortTools.KeyUpperSorter("item_id"))
	end

	for i = 0, GameEnum.MAX_STONE_COUNT - 1 do
		if show_stone_list[i + 1] then
			local data = show_stone_list[i + 1]
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			local bundle, asset = "", ""
			if item_cfg then
				local stone_cfg = equipmentdata_instance:GetBaoShiCfgByItemId(data.item_id)
				local stone_type = stone_cfg.stone_type
				bundle, asset = ResPath.GetEquipmentIcon("a2_bsi_" .. (stone_type or 1))
				self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
					self.node_list["icon_" .. i].image:SetNativeSize()
				end)
			end

			self.node_list["icon_" .. i]:SetActive(item_cfg ~= nil)
			self.node_list["img_suo_" .. i]:SetActive(false)
		else
			self.node_list["icon_" .. i]:SetActive(false)
			self.node_list["img_suo_" .. i]:SetActive(true)
		end
	end
end

--设置上下浮动图标
function EquipBaoShiItemRender:SetUpDownIconVisible(is_visible)
	self.node_list.img_remind:SetActive(is_visible)
end

function EquipBaoShiItemRender:OnSelectChange(is_select)
	self.node_list["normal_bg"]:SetActive(not is_select)
	self.node_list["select_bg"]:SetActive(is_select)
end

-- ============= SPStarRender ==========================
-- 爆开特效
local EXPLODE_EFFECT = {
	[1] = "UI_shengxingjihuo_lv",
	[2] = "UI_shengxingjihuo_lan",
	[3] = "UI_shengxingjihuo_zi",
	[4] = "UI_shengxingjihuo_cheng",
	[5] = "UI_shengxingjihuo_hong",
	[6] = "UI_shengxingjihuo_fen",
}

-- 常驻特效
local RESIDENT_EFFECT = {
	[1] = "UI_shengpin_lv",
	[2] = "UI_shengpin_lan",
	[3] = "UI_shengpin_zi",
	[4] = "UI_shengpin_cheng",
	[5] = "UI_shengpin_hong",
	[6] = "UI_shengpin_fen",
}

-- 光点聚集特效
local RALLY_EFFECT = {
	[1] = "UI_shenpin_gdhj_lv",
	[2] = "UI_shenpin_gdhj_lan",
	[3] = "UI_shenpin_gdhj_zi",
	[4] = "UI_shenpin_gdhj_cheng",
	[5] = "UI_shenpin_gdhj_hong",
	[6] = "UI_shenpin_gdhj_fen",
}

-- 拖尾特效
local TAIL_EFFECT = {
	[1] = "UI_shenpin_tw_lv",
	[2] = "UI_shenpin_tw_lan",
	[3] = "UI_shenpin_tw_zi",
	[4] = "UI_shenpin_tw_cheng",
	[5] = "UI_shenpin_tw_hong",
	[6] = "UI_shenpin_tw_fen",
}

SPStarRender = SPStarRender or BaseClass(BaseRender)
function SPStarRender:__init()
	self.old_quality_id = -1
	self.cache_effect_state = false		-- 解决快速点击计时器将特效显示出来
	self.game_obj_attach = self.node_list["star_effect"].gameObject:GetComponent(typeof(Game.GameObjectAttach))
end

function SPStarRender:__delete()
	self:StopMoveTween()
	self:CleanTweenCD()
	self.old_quality_id = nil
	self.game_obj_attach = nil
	self.cache_effect_state = nil
	self.doing_shengpin_tween = nil
end

function SPStarRender:OnFlush()
	if not self.data then
		return
	end

	self:StopMoveTween()

	local cfg = self.data.cfg
	local star_count = self.data.star_count or 0
	if IsEmptyTable(self.data.cfg) then
		self.node_list.star_icon:SetActive(false)
	else
		self:SetStarIcon(cfg.star_color)
		self.node_list.star_icon:SetActive(star_count >= self.index)
	end
end

function SPStarRender:CleanTweenCD()
	if CountDownManager.Instance:HasCountDown("SPStarRender" .. self.index) then
        CountDownManager.Instance:RemoveCountDown("SPStarRender" .. self.index)
    end
end

function SPStarRender:SetStarIcon(id)
	self:ResetEffectPos()
	self:CleanTweenCD()
	if self.doing_shengpin_tween then
		return
	end

	local star_count = self.data.star_count or 0
	local do_tween = self.data.do_tween
	if do_tween and star_count == self.index then
		-- 炸开
		self.cache_effect_state = true
		local bundle_1_name, asset_1_name = ResPath.GetEffectUi(EXPLODE_EFFECT[id])
		self:ShowStarEffectByAsset(bundle_1_name, asset_1_name, id)

		-- 常驻
		CountDownManager.Instance:AddCountDown("SPStarRender" .. self.index, nil,
			function()
				if not self.doing_shengpin_tween and self.node_list.star_effect then
					local bundle_2_name, asset_2_name = ResPath.GetEffectUi(RESIDENT_EFFECT[id])
					self:ShowStarEffectByAsset(bundle_2_name, asset_2_name, id)
				end
			end,
		nil, 0.4, 0.1)
	else
		-- 减少特效的显示隐藏
		local cur_show_state = self.node_list.star_effect:GetActive()
		local change_state = star_count >= self.index

		if cur_show_state ~= change_state then
			if not cur_show_state then
				local bundle_name, asset_name = ResPath.GetEffectUi(RESIDENT_EFFECT[id])
				self.game_obj_attach.BundleName = bundle_name
				self.game_obj_attach.AssetName = asset_name
				self.old_quality_id = id
			end
			self.cache_effect_state = change_state
			self.node_list.star_effect:SetActive(self.cache_effect_state)

		elseif cur_show_state and cur_show_state == change_state and self.old_quality_id ~= id then
			local bundle_name, asset_name = ResPath.GetEffectUi(RESIDENT_EFFECT[id])
			self.cache_effect_state = true
			self:ShowStarEffectByAsset(bundle_name, asset_name, id)
		end
	end

	local bundle = "uis/view/equipfumo_ui/images_atlas"
	local asset = "a2_spstar_icon_" .. id
	if self.node_list.star_icon then
		self.node_list.star_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.star_icon.image:SetNativeSize()
		end)
	end
end

function SPStarRender:ClearDoTweenState()
	self.doing_shengpin_tween = false
end

function SPStarRender:DoUpQualityTween1()
	self.doing_shengpin_tween = true
	self:ResetEffectPos()
	self.node_list.star_icon:SetActive(false)
	local id = self.old_quality_id
	local bundle_name, asset_name = ResPath.GetEffectUi(RALLY_EFFECT[id])
	self.cache_effect_state = true
	self:ShowStarEffectByAsset(bundle_name, asset_name, id)
end

function SPStarRender:DoUpQualityTween2(end_pos, move_time)
	if self.move_tween then
		return
	end

	end_pos = end_pos or Vector3(-17, -24, 0)
	move_time = move_time or 1
	local real_end_pos = TipWGCtrl.Instance:TurnBaseCellPos(end_pos, self.node_list.star_effect)

	local id = self.old_quality_id
	local bundle_name, asset_name = ResPath.GetEffectUi(TAIL_EFFECT[id])
	self:ShowStarEffectByAsset(bundle_name, asset_name, id)
	self.move_tween = self.node_list.star_effect.transform:DOLocalMove(real_end_pos, move_time)
	self.move_tween:OnComplete(function()
		if self.node_list.star_effect then
			self.node_list.star_effect:SetActive(false)
		end
	end)

	self.move_tween:SetEase(DG.Tweening.Ease.InCirc)
end

function SPStarRender:StopMoveTween()
	if self.move_tween then
		self.move_tween:Kill()
		self.move_tween = nil
	end
end

function SPStarRender:ResetEffectPos()
	self.node_list.star_effect.rect.anchoredPosition = Vector2(0, 0)
end

function SPStarRender:ShowStarEffectByAsset(bundle_name, asset_name, id)
	self.node_list.star_effect:SetActive(false)
	self.game_obj_attach.BundleName = bundle_name
	self.game_obj_attach.AssetName = asset_name
	self.old_quality_id = id
	self.node_list.star_effect:SetActive(self.cache_effect_state)
end

--=================  宝石槽位 render ===========================================
BSXQStoneItem = BSXQStoneItem or BaseClass(BaseRender)
function BSXQStoneItem:__init()
	self.is_init = false
	self.is_inlay = true

	if self.node_list["inlay_effect"] then
		self.node_list["inlay_effect"]:SetActive(false)
	end

	self.attr_part_list = {}
	for i = 1, GameEnum.EQUIP_YUPO_ATTR_NUM do
		self.attr_part_list[i] = self.node_list["attr_part"]:FindObj("attr_" .. i)
	end

	XUI.AddClickEventListener(self.node_list["btn_baoshi_add"], BindTool.Bind(self.OnBtnBaoShiAddHandler, self))
	XUI.AddClickEventListener(self.node_list["img_baoshi_suo"], BindTool.Bind(self.OnBtnBaoShiLockHandler, self))
	self.cur_equip_part = 0
	self.is_invaild = false
	self:CreatStoneItem()
end

function BSXQStoneItem:__delete()
	if self.stone_item ~= nil then
		self.stone_item:DeleteMe()
		self.stone_item = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end

	self.is_invaild = false
	self.is_jinlian_item = nil
	self.attr_part_list = nil
end

function BSXQStoneItem:CreatStoneItem()
	self.stone_item = ItemCell.New(self.node_list["baoshi_item"])
	self.stone_item:SetShowCualityBg(false)
	self.stone_item:SetCellBgEnabled(false)
	self.stone_item:NeedDefaultEff(false)
	self.stone_item:SetIsShowTips(false)
	self.stone_item:SetClickCallBack(BindTool.Bind2(self.OnBtnBaoShiAddHandler, self))
	self.node_list["img_baoshi_suo"]:SetActive(false)
	self.node_list["suo_bg"]:SetActive(false)
	self.node_list["baoshi_add_bg"]:SetActive(false)
	self:SetBaoShiRemind(false)
	self:SetBaoShiEffect(false)
end

--宝石口点击
function BSXQStoneItem:OnBtnBaoShiAddHandler()
	if self.is_invaild or not self.data then
		return
	end

	-- 已经镶嵌最高等级宝石
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(self.cur_equip_part)
	local jade_level_limit = EquipBodyWGData.Instance:GetEquipStoneJadeLevelLimit(equip_body_seq)

	local data_stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(self.data.item_id)
	if data_stone_cfg then
		if data_stone_cfg.level >= jade_level_limit then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Equip.ErrorTips7)
			return
		end
	end

	-- 有更好的宝石
	local select_list = EquipmentWGData.Instance:GetBaoShiListByStone(self.cur_equip_part, self.index)

	if IsEmptyTable(select_list) then
		if self.data.is_inlay then
			-- 打开升级界面
			local next_stone = EquipmentWGData.Instance:GetBaoShiLevelUpCfgByOldId(self.data.item_id)
			if next_stone then
				EquipmentWGCtrl.Instance:BaoShiUpGradeOpen(self.cur_equip_part, self.index)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Equip.ErrorTips6)
			end
		else
			-- 商店购买
			local menu_data = EquipmentWGData.Instance:GetBaoShiMenu(self.cur_equip_part)
			if not IsEmptyTable(menu_data) then
				local num = ShopWGData.Instance:GetMaxBuyNum(menu_data.shop_seq)
				local item_cfg = ItemWGData.Instance:GetItemConfig(menu_data.default_stone)
				ShopTip.Instance:SetData(item_cfg, 1, GameEnum.SHOP, nil, menu_data.shop_seq, nil, num)
			else
				print_error("----宝石BUG-----")
			end
		end
	else
		-- 打开宝石列表
		EquipmentWGCtrl.Instance:SelectBaoShiOpen(select_list, self.cur_equip_part, self.index)
	end
end

function BSXQStoneItem:OnBtnBaoShiLockHandler()
	if self.is_invaild then
		return
	end

	if IsEmptyTable(self.data) then
		return
	end

	-- local is_open = self.data.is_open == 1
	-- local limit_baoshi_cfg = EquipmentWGData.Instance:GetLimitBaoShiOpenCfg(self.cur_equip_part, self.index)
	-- if limit_baoshi_cfg and not is_open then
	-- 	if limit_baoshi_cfg.vip_level_limit > 0 then
	-- 		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, "recharge_vip")
	-- 	end
	-- end
end

function BSXQStoneItem:SetCurEquipPart(part)
	self.cur_equip_part = part or 0
end

function BSXQStoneItem:SetIsInVaild(is_invaild)
	self.is_invaild = is_invaild
	if is_invaild and self.node_list.act_desc then
	self.node_list.act_desc.text.text = Language.Equip.NoWearEquip
	end
end

function BSXQStoneItem:SetBaoShiRemind(enable)
	self.node_list["img_baoshi_remind"]:SetActive(enable)
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end

	if enable then
		RectTransform.SetAnchoredPositionXY(self.node_list["img_baoshi_remind"].rect, 58, -10)
		self.arrow_tweener = self.node_list["img_baoshi_remind"].gameObject.transform:DOAnchorPosY(0, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function BSXQStoneItem:SetBaoShiEffect(enable)
	self.node_list["effect"]:SetActive(enable)
end

function BSXQStoneItem:StopInalyTween()
	self.can_do_inlay_tween = false
	self.node_list["inlay_effect"]:SetActive(false)
end

function BSXQStoneItem:DoInalyTween()
	if not self.node_list.inlay_effect then
		return
	end

	self.can_do_inlay_tween = true
	self.node_list["inlay_effect"]:SetActive(false)
	self.node_list["inlay_effect"]:SetActive(true)

	GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
		if self.node_list.inlay_effect and self.can_do_inlay_tween then
			self.node_list["inlay_effect"]:SetActive(false)
		end
	end, self), 2)
end

function BSXQStoneItem:NoDataHideShow()
	-- self.node_list["img_baoshi_suo"]:SetActive(true)
	self.node_list["suo_bg"]:SetActive(true)
	self.node_list["ph_baoshi_item_bg"]:SetActive(false)
	-- self.node_list["baoshi_item"]:SetActive(false)
	self.node_list["attr_part"]:SetActive(false)
	self.node_list["limit_part"]:SetActive(false)
	self.node_list["baoshi_add_bg"]:SetActive(false)
	if self.node_list["quality_bg"] then
		local bundle, asset = ResPath.GetCommonImages("a2_ty_yuandi_txk")
		self.node_list["quality_bg"].image:LoadSprite(bundle, asset, function()
			self.node_list["quality_bg"].image:SetNativeSize()
		end)
	end

	self:SetBaoShiRemind(false)
	self:SetBaoShiEffect(false)
end

function BSXQStoneItem:OnFlush()
	if IsEmptyTable(self.data) then
		self:NoDataHideShow()
		return
	end

	local eqm_data = EquipmentWGData.Instance
	local bg_idx = 0
	local is_open = self.data.is_open == 1
	local is_inlay = self.data.is_inlay

	if self.is_init and is_inlay == true and self.is_inlay == false then
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.StrenthenFail, nil, true))
	end

	self.is_init = true
	self.is_inlay = is_inlay

	self.node_list["attr_part"]:SetActive(is_open and is_inlay)
	-- self.node_list["limit_part"]:SetActive(not is_inlay)
	self.node_list["limit_part"]:SetActive(is_open)
	self.node_list["ph_baoshi_item_bg"]:SetActive(is_open and is_inlay)
	-- self.node_list["baoshi_item"]:SetActive(is_open and is_inlay)
	-- self.node_list["img_baoshi_suo"]:SetActive(not is_open)
	self.node_list["suo_bg"]:SetActive(not is_open)
	self.node_list["baoshi_add_bg"]:SetActive(is_open and not is_inlay)-- and not self.is_jinlian_item)

	local is_have_batter = eqm_data:GetStoneSlotHaveBatterState(self.cur_equip_part, self.index)
	local is_can_upgrade = eqm_data:GetStoneSlotCanUpgradeState(self.cur_equip_part, self.index)
	-- self:SetBaoShiEffect(is_have_batter)
	-- self:SetBaoShiRemind(not is_have_batter and is_can_upgrade)
	self:SetBaoShiEffect(is_have_batter or is_can_upgrade)
	self:SetBaoShiRemind(is_have_batter or is_can_upgrade)

	if is_open and is_inlay then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		bg_idx = item_cfg.color
		-- local show_color = ITEM_COLOR[item_cfg.color]
		self.stone_item:SetData({item_id = self.data.item_id})
		local stone_cfg = eqm_data:GetBaoShiCfgByItemId(self.data.item_id)
		if stone_cfg then
			
			for i = 1, GameEnum.EQUIP_YUPO_ATTR_NUM do
				local type = stone_cfg["attr_type" .. i]
				local value = stone_cfg["attr_val" .. i]
				if value and value > 0 then
					local name = eqm_data:GetAttrNameByAttrId(type)
					local is_per = eqm_data:GetAttrIsPer(type)
					value = is_per and (value * 0.01 .. "%") or value
					self.node_list["attr_name" .. i].text.text = name--ToColorStr(name, show_color)
					self.node_list["attr_value" .. i].text.text = value--ToColorStr(value, show_color)
					self.attr_part_list[i]:SetActive(true)
				else
					self.attr_part_list[i]:SetActive(false)
				end
			end
		end

	elseif is_open and not is_inlay then
		self.node_list["act_desc"].text.text = ToColorStr(Language.Equipment.KeXiangQian, COLOR3B.GREEN)
	else
		local limit_cfg = eqm_data:GetLimitBaoShiOpenCfg(self.cur_equip_part, self.index)
		if limit_cfg then
			local baoshi_open_text = string.format(Language.Equip.LimitBaoShiOpen, limit_cfg.order_limit)
			-- if limit_cfg.vip_level_limit > 0 then
			-- 	baoshi_open_text = string.format(Language.Equip.LimitBaoShiVipOpen, limit_cfg.vip_level_limit)
			-- end
			self.node_list["act_desc"].text.text = baoshi_open_text
		else
			self.node_list["act_desc"].text.text = ""
		end
	end

--[[ 	local quality_bg = bg_idx > 0 and "_" .. bg_idx or ""
	local bundle, asset = ResPath.GetCommonImages("a1_item_round_bg" .. quality_bg)
	self.node_list["quality_bg"].image:LoadSprite(bundle, asset, function()
		self.node_list["quality_bg"].image:SetNativeSize()
	end)
 ]]
	if self.is_invaild then
		self.node_list["act_desc"].text.text = Language.Equip.NoWearEquip
	end
end


--------------------------------------------------------------------------------
BSJLStoneItem = BSJLStoneItem or BaseClass(BSXQStoneItem)
function BSJLStoneItem:OnFlush()
	self.is_jinlian_item = true
	if not self.data then
		return
	end

	BSXQStoneItem.OnFlush(self)
end
