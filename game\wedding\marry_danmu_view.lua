MarryDanMuView = MarryDanMuView or BaseClass(SafeBaseView)
local TEXT_MAX_NUM = 20 --最大限制字数20个汉字，按照字节计算
local DANMU_NUM = 4 --最大行数   有七行   策划要求改成四行显示
function MarryDanMuView:__init()
	self.view_layer = UiLayer.MainUIHigh
    -- self:SetMaskBg()
    self.view_name = "MarryDanMuView"
    self.active_close = false
    self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_danmu_view")
end

function MarryDanMuView:__delete()

end

function MarryDanMuView:ReleaseCallBack()
	if self.parent then
		for i=1,DANMU_NUM do
			if self.parent[i] then
				self.parent[i] = nil
			end
		end
	end
	if self.DanmuRender then
		for i=1,DANMU_NUM do
			local data = self.DanmuRender[i]
			if data then
				local num = #self.DanmuRender[i]
				if num > 0 then
					for i,v in ipairs(self.DanmuRender[i]) do
						v:DeleteMe()
					end
				end
				self.DanmuRender[i] = nil
			end
			
		end
	end

end

function MarryDanMuView:CloseCallBack()
	if self.parent then
		for i=1,DANMU_NUM do
			if self.parent[i] then
				self.parent[i] = nil
			end
		end
	end
	self.parent = nil
	if self.DanmuRender then
		for i=1,DANMU_NUM do
			local num = #self.DanmuRender[i]
			if num > 0 then
				for i,v in ipairs(self.DanmuRender[i]) do
					v:DeleteMe()
				end
			end
			self.DanmuRender[i] = nil
		end
	end
	self.DanmuRender = nil
end

function MarryDanMuView:LoadCallBack()

end

function MarryDanMuView:ShowIndexCallBack()
	self:Flush()
end

function MarryDanMuView:OnFlush()
	if nil == self.parent then
		self.parent = {}
		for i=1,DANMU_NUM do
			self.parent[i] = self.node_list["tex_parent_"..i]
		end
	end
	if nil == self.DanmuRender then
		self.DanmuRender = {}
		for i=1,DANMU_NUM do
			self.DanmuRender[i] = {}
		end
	end
	self:CreatDanMuRender()
end

function MarryDanMuView:OnClickSend()

end

function MarryDanMuView:CreatDanMuRender()
	local bundle_name = "uis/view/wedding_ui_prefab"
	local asset_name = "ph_danmu_item"
	local data = WeddingWGData.Instance:GetMarryDanMuInfo()
	if #data == 0 then
		return
	end
	for i=1,DANMU_NUM do
		local remder_num = #self.DanmuRender[i]
		if remder_num > 0 and #data > 0 then
			if self.DanmuRender[i][remder_num] then
				local is_can_creat = self.DanmuRender[i][remder_num]:GetRenderMoveInfo()
				if is_can_creat then
					local cell = MarryDanMuItemRender.New()
					cell:LoadAsset(bundle_name, asset_name, self.parent[i].transform)
					table.insert(self.DanmuRender[i],cell)
					-- local callback = self:RenderMove(i,self.DanmuRender[i][remder_num + 1])
					cell:SetData(data[1],i)
					table.remove(data,1)
					WeddingWGData.Instance:DelMarryDanMuInfo(false)
				end
			end
		elseif #data > 0 then
			local cell = MarryDanMuItemRender.New()
			cell:LoadAsset(bundle_name, asset_name, self.parent[i].transform)
			table.insert(self.DanmuRender[i],cell)
			-- local callback = self:RenderMove(i,self.DanmuRender[i][remder_num + 1])
			cell:SetData(data[1],i)
			table.remove(data,1)
			WeddingWGData.Instance:DelMarryDanMuInfo(false)
			
		end
	end
end

-- function MarryDanMuView:RenderMove(i,cell)
-- 	if cell then
-- 		function callback(del)
-- 			self:Flush()
-- 			if self.DanmuRender[i][1] then
-- 				self.DanmuRender[i][1]:DeleteMe()
-- 				self.DanmuRender[i][1] = nil
-- 			end
-- 		end
-- 		local dele_obj = BindTool.Bind(callback, self)
-- 		return dele_obj
-- 	end
-- end

function MarryDanMuView:DelDanMuRender(i)
	local remder_num = #self.DanmuRender[i]
	if remder_num > 0 then
		if self.DanmuRender[i][1] then
			self.DanmuRender[i][1]:DeleteMe()
			self.DanmuRender[i][1] = nil
		end
		table.remove(self.DanmuRender[i],1)
	end
end

MarryDanMuItemRender = MarryDanMuItemRender or BaseClass(BaseRender)
function MarryDanMuItemRender:__init()
end

function MarryDanMuItemRender:LoadCallBack()

end
--其实没机会被调用
function MarryDanMuItemRender:__delete()
	self.callback = nil
	self.is_creat = nil
	if self.delay_timer then
		GlobalTimerQuest:CancelQuest(self.delay_timer)
		self.delay_timer = nil
	end
end

function MarryDanMuItemRender:DeleteMeInfo()
	self.callback = nil
	self.is_creat = nil
	if self.delay_timer then
		GlobalTimerQuest:CancelQuest(self.delay_timer)
		self.delay_timer = nil
	end
end

function MarryDanMuItemRender:SetData(data,index)
	if not data then return end
	self.data = data
	-- self.callback = callback
	self.del_index = index
	self.is_creat = false
	self:Flush()
end

function MarryDanMuItemRender:OnFlush()
	if self.data == nil then return end
	local str = ""
	local item_name = ""
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)

	if item_cfg and self.data.item_id == MARRY_OTHER_TYPE.ITEM_ID_1 then
		item_name = item_cfg.name --ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		str = string.format(Language.Marry.ZhenQingZhuFu, self.data.sender_name, item_name, item_cfg.param1)
	elseif item_cfg and self.data.item_id == MARRY_OTHER_TYPE.ITEM_ID_2 then
		item_name = item_cfg.name --ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		str = string.format(Language.Marry.XingFuZhuFu, self.data.sender_name, item_name, item_cfg.param1, self.data.barrage)
	end
	self.node_list["des_text"].text.text = str
	-- local n,m = self:GetCurTextNum()
	-- local text_leng = n*20 + m*12 + 60 + 10 --汉字按照20字母算作12，60两边的边距，30两条弹幕之间留的间距
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["des_text"].rect)
	local text_length = self.node_list.des_text.rect.sizeDelta.x + 20 --间距
	-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["ph_danmu_item"].rect)
	-- print_error(self.node_list.ph_danmu_item.rect.anchoredPosition)
	local timer = text_length / 200
	self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		self.is_creat = true
		ViewManager.Instance:FlushView(GuideModuleName.MarryDanMu)
	end, timer)
	self:MoveToPos()
end

function MarryDanMuItemRender:GetRenderMoveInfo()
	-- local end_posx = self.node_list["ph_danmu_item"].rect.sizeDelta.x + 10
	-- local posx = self.node_list["ph_danmu_item"].rect.anchoredPosition.x
	-- if posx > end_posx then
	-- 	return true
	-- end
	return self.is_creat
end


function MarryDanMuItemRender:MoveToPos(callback)

	local tween = self.node_list["ph_danmu_item"].rect:DOAnchorPosX(-2000, 10)
	tween:SetEase(DG.Tweening.Ease.Linear)
	tween:OnComplete(function ()
		self:DeleteMeInfo()
		WeddingWGCtrl.Instance:DelDanMuRender(self.del_index)
	end)
	-- self.node_list["ph_danmu_item"].transform:DOAnchorPosX(-2000, 10):OnComplete(function()
	-- 	self.callback()
	-- end)
end

function MarryDanMuItemRender:GetCurTextNum(str)
	local desc_text = str or ""
	local c = ""
	local b = ""
	local i = 1
	local n = 0
	local m = 0
	while true do
        c = string.sub(desc_text,i,i)
        b = string.byte(c)
        if b > 128 then
                i = i + 3
                n = n + 1
        else
                i = i + 1
                m = m + 1
        end

        if i > #desc_text then
                break
        end
  	end
  	return n,m
end

