ZhanDuiChangeNoticeView = ZhanDuiChangeNoticeView or BaseClass(SafeBaseView)

function ZhanDuiChangeNoticeView:__init()
    self:SetMaskBg()
    --self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_common_panel", {sizeDelta = Vector2(530, 266)})
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_change_notice")
end

function ZhanDuiChangeNoticeView:ReleaseCallBack()
    self.last_edit_time = nil
end

function ZhanDuiChangeNoticeView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.ZhanDui.ViewNameChangeNotice
	--self:SetSecondView(nil, self.node_list["size"])
    XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.OnClickChange, self))
    self.node_list.change_notice_input_field.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnEditValueChange, self))
end

function ZhanDuiChangeNoticeView:OnEditValueChange(str)
    local len, table = CheckStringLen(str, COMMON_CONSTS.ZHAN_DUI_MAX_NOTICE_LEN)
    if not len then
        if table then
            local str = ""
            for i = 1, #table do
                str = str .. table[i]
            end
            self.node_list.change_notice_input_field.input_field.text = str
        end
        if self.last_edit_time and self.last_edit_time > Status.NowTime then
            return
        end
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.ChangeNoticeMaxFontCount)
        self.last_edit_time = Status.NowTime + 0.5
    end
end

function ZhanDuiChangeNoticeView:OnClickChange()
    local str = self.node_list.change_notice_input_field.input_field.text
    local len, table = CheckStringLen(str, COMMON_CONSTS.ZHAN_DUI_MAX_NOTICE_LEN)
    if not len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhanDui.ChangeNoticeMaxFontCount)
        return
    end
    if ChatFilter.Instance:IsIllegal(str, false) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end
    --TODO send msg
    --print_error("发送修改战队公告", str)
    ZhanDuiWGCtrl.Instance:ModifyZhanDuiNotice(str)
end