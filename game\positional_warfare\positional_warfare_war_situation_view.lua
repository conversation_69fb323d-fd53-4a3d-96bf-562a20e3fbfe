-- 战况界面
PositionalWarfareWarSituationView = PositionalWarfareWarSituationView or BaseClass(SafeBaseView)

function PositionalWarfareWarSituationView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_war_situation_view")
end

function PositionalWarfareWarSituationView:OpenCallBack()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.SITUATION_INFO)
end

function PositionalWarfareWarSituationView:LoadCallBack()
    if not self.war_situation_list then
        self.war_situation_list = AsyncListView.New(PWWarSituationCellRender, self.node_list.war_situation_list) 
    end

    self.node_list.title_view_name.text.text = Language.PositionalWarfare.WarSituationViewName
end

function PositionalWarfareWarSituationView:ReleaseCallBack()
    if self.war_situation_list then
        self.war_situation_list:DeleteMe()
        self.war_situation_list = nil
    end
end

function PositionalWarfareWarSituationView:OnFlush()
    local data_list = PositionalWarfareWGData.Instance:GetWarSituationDataList()
    -- data_list = {[1] = {land_seq = 1, monster_seq = 1, player_num = 30}}
    local has_data = not IsEmptyTable(data_list)
    self.node_list.war_situation_list:CustomSetActive(has_data)
    self.node_list.no_data:CustomSetActive(not has_data)

    if has_data then
        self.war_situation_list:SetDataList(data_list)
    end
end

--------------------------------------PWWarSituationCellRender----------------------------------------
PWWarSituationCellRender = PWWarSituationCellRender or BaseClass(BaseRender)

function PWWarSituationCellRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGoBtn, self))
end

function PWWarSituationCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local land_info = PositionalWarfareWGData.Instance:GetLandCfg(self.data.land_seq)
    if IsEmptyTable(land_info) then
        return
    end

    self.node_list.city_name.text.text = land_info.land_name

    local monster_cfg = PositionalWarfareWGData.Instance:GetCurMonsterCfg(self.data.land_seq, self.data.monster_seq)
    if IsEmptyTable(monster_cfg) then
        return
    end

	local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(monster_cfg.monster_id)

    if IsEmptyTable(boss_cfg) then
        return
    end

    self.node_list.boss_name.text.text = string.format(Language.PositionalWarfare.WarSituationDesc, boss_cfg.name, self.data.player_num) 
end

function PWWarSituationCellRender:OnClickGoBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local land_info = PositionalWarfareWGData.Instance:GetLandCfg(self.data.land_seq)
    if IsEmptyTable(land_info) then
        return
    end

    PositionalWarfareWGData.Instance:SetEnterSceneSelectDataCache(self.data.land_seq, self.data.monster_seq)
    CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LAND_WAR, self.data.land_seq)
end