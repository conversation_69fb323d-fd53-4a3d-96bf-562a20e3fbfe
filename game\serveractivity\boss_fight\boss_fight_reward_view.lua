--Boss乱斗
ActBossFightRewardView = ActBossFightRewardView or BaseClass(SafeBaseView)

function ActBossFightRewardView:__init()
	-- self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/boss_fight_ui_prefab", "layout_boss_fight_reward")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function ActBossFightRewardView:__delete()

end

function ActBossFightRewardView:ReleaseCallBack()
	if self.boss_list then
		self.boss_list:DeleteMe()
		self.boss_list = nil
	end
end

function ActBossFightRewardView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = Language.ActBossFight.RewardTitle
	self.node_list["act_desc"].text.text = Language.ActBossFight.RewardDesc
	self.boss_list = AsyncListView.New(ActBossFightRewardRender,self.node_list["ph_ranking_reward_list"])
	self.boss_list:SetDataList(ServerActivityWGData.Instance:GetBossFightReward())
end

function ActBossFightRewardView:OpenCallBack()

end

-- ActBossFightRewardRender -----------------------------------
ActBossFightRewardRender = ActBossFightRewardRender or BaseClass(BaseRender)
function ActBossFightRewardRender:__init()

end

function ActBossFightRewardRender:__delete()
	if self.cell_list then
		self.cell_list:DeleteMe()
		self.cell_list = nil
	end
end

function ActBossFightRewardRender:LoadCallBack()
	self.cell_list = AsyncListView.New(ItemCell,self.node_list["list"])
end

function ActBossFightRewardRender:OnFlush()
	if not self.data then
		return
	end

	if self.data.min_rank == self.data.max_rank then
		self.node_list["Text"].text.text = string.format(Language.ActBossFight.Rank1,self.data.min_rank + 1)
	else
		self.node_list["Text"].text.text = string.format(Language.ActBossFight.Rank2,self.data.min_rank + 1,self.data.max_rank + 1)
	end
	local list = {}
	for i=0,#self.data.reward_item do
		table.insert(list, self.data.reward_item[i])
	end

	self.cell_list:SetDataList(list)
end
