CrossAirWarProgView = CrossAirWarProgView or BaseClass(SafeBaseView)
local MAX_PROGRESS_VALUE = 8

function CrossAirWarProgView:__init()
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_prog")
   	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.HALF)
end

function CrossAirWarProgView:__delete()
end

function CrossAirWarProgView:LoadCallBack()
	if not self.prog_item_list then
		self.prog_item_list = {}
		local node_num = 6
		for i = 1, node_num do
			self.prog_item_list[i] = AirWarProgRender.New(self.node_list.prog_item_root:FindObj("prog_item_" .. i))
			self.prog_item_list[i]:SetIndex(i)
		end
	end

	XUI.AddClickEventListener(self.node_list.go_to_pos_btn, BindTool.Bind(self.OnClickFindBossPos, self))
end

function CrossAirWarProgView:ReleaseCallBack()
    if self.prog_item_list then
        for k, v in pairs(self.prog_item_list) do
            v:DeleteMe()
        end
        self.prog_item_list = nil
    end
end

function CrossAirWarProgView:CloseCallBack()
end

function CrossAirWarProgView:OnFlush(param)
	self:UpdateGodDemonProgPart()
end

function CrossAirWarProgView:UpdateGodDemonProgPart()
	local cur_monster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	local monster_info = CrossAirWarWGData.Instance:GetBossMonsterList()
	self.curr_progress = monster_info and monster_info[1]
	self.cur_progress_index = 1

	for i, value in ipairs(self.prog_item_list) do
		value:SetVisible(monster_info[i] ~= nil)

		if monster_info[i] ~= nil then
			value:SetCurSeq(cur_monster_seq)
			value:SetData(monster_info[i])

			if cur_monster_seq >= monster_info[i].seq then
				self.curr_progress = monster_info[i]
				self.cur_progress_index = i
			end
		end
	end

	if self.curr_progress ~= nil then
		local str = self.curr_progress.target_desc
		-- 是否处于最后阶段
		if self.cur_progress_index == #self.prog_item_list then
			local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
			if status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_GATHER then
				self.cur_progress_index = 7
				str = Language.CrossAirWar.ProgTitle1
			elseif status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_AUCTION then
				self.cur_progress_index = MAX_PROGRESS_VALUE
				str = string.format(Language.CrossAirWar.AuctionTitle, "")
			end

			if self.cur_progress_index > #self.prog_item_list then
				local render = self.prog_item_list[#self.prog_item_list]
				render:SetKilledStatus(true)
			end
		end
		
		self.node_list.progress_tips.text.text = string.format(Language.CrossAirWar.PhaseStr6, str)
		self.node_list.prog_val.slider.value = self.cur_progress_index / MAX_PROGRESS_VALUE
	end

	self.node_list.go_to_pos_btn:CustomSetActive(self.cur_progress_index ~= MAX_PROGRESS_VALUE)
	local all_score = CrossAirWarWGData.Instance:GetPlayerWarScore()
	self.node_list.score_tips.text.text = string.format(Language.CrossAirWar.PhaseStr4, ToColorStr(all_score, COLOR3B.GREEN))
end

------------------------------------------点击事件------------------------------------
--点击前往
function CrossAirWarProgView:OnClickFindBossPos()
	if not self.curr_progress then
		return
	end

	local moster_pos = CrossAirWarWGData.Instance:GetMonsterPosBySeq(self.curr_progress.seq)
	if not moster_pos then
		return
	end
	
	local pos = moster_pos[1]
	if pos == nil then
		return
	end

	if self.cur_progress_index > #self.prog_item_list then
		pos = CrossAirWarWGData.Instance:GetFindBoxPos()
	end
	
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
	
	local scene_id = Scene.Instance:GetSceneId()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pos.x, pos.y, COMMON_CONSTS.GUAJI_MAX_RANGE)	
	self:Close()
end

-------------------------- AirWarProgRender --------------------------
AirWarProgRender = AirWarProgRender or BaseClass(BaseRender)
function AirWarProgRender:ReleaseCallBack()
	self.cur_monster_seq = nil
end

function AirWarProgRender:SetCurSeq(var_cur_monster_seq)
	self.cur_monster_seq = var_cur_monster_seq
end

function AirWarProgRender:OnFlush()
	if not self.data then return end

	local cur_seq = self.cur_monster_seq or 0
	local phase_seq = self.data.seq
	local is_phase_passed = cur_seq > phase_seq
	self.node_list.progress_select:CustomSetActive(cur_seq == phase_seq)
	self:SetKilledStatus(is_phase_passed)
	self.node_list.boss_type_txt.text.text = self.data.target_desc

	local bundle, asset = ResPath.GetBossUI(self.data.monster_head) 
	if bundle and asset then
		self.node_list.head_icon.image:LoadSprite(bundle, asset)
	end
end

function AirWarProgRender:SetKilledStatus(kill_state)
	self.node_list.kill_sign:SetActive(kill_state)
end