TipsGetCommonRewardView = TipsGetCommonRewardView or BaseClass(SafeBaseView)

local col_num = 10 							 		--每行多少个
local ANI_SPEED = 0.1
local reword_count = 0 				--该次寻宝奖励物品个数
local ani_flag_t = {}
local ani_count = 1
local scroll_verticalNormalizedPosition = 1
local row_num = 1								--显示3行
local lerp = 0.015--1 / (50 - (col_num * row_num)*0)* 0.5		--每次减少多少


local Sort_Type = {
	[GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
	[GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
	[GameEnum.ITEM_BIGTYPE_GIF] = 8,
	[GameEnum.ITEM_BIGTYPE_OTHER] = 7,
	[GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}


function TipsGetCommonRewardView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self.view_name = "TipsGetCommonRewardView"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_common_reward_result")
end

function TipsGetCommonRewardView:ReleaseCallBack()
	if nil ~= self.zhanshi_grid then
		self.zhanshi_grid:DeleteMe()
		self.zhanshi_grid = nil
	end
	if nil ~= self.zhanshi_ten_grid then
		self.zhanshi_ten_grid:DeleteMe()
		self.zhanshi_ten_grid = nil
	end

	if self.best_item_list then
		self.best_item_list:DeleteMe()
		self.best_item_list = nil
	end

	self:CancelTimeQuest()
	self:CancelMoveScrollTimeQuest()
	self.is_skip_anim =	false
	self:ReleaseData()
end

function TipsGetCommonRewardView:ReleaseData()
	self.id_list = nil
	self.again_func = nil
	self.other_info = nil
	self.no_need_sort = nil
	self.sure_func = nil
end

function TipsGetCommonRewardView:LoadCallBack()
	self:InitXunBaoZhanshi()
	self:RegisterEvent()
end

--初始化格子
function TipsGetCommonRewardView:InitXunBaoZhanshi()
	self.zhanshi_grid = CommonRewardGrid.New()
    self.zhanshi_grid:CreateCells({col = col_num, change_cells_num = 1 , list_view = self.node_list["ph_zhanshii_cell"],
    itemRender = CommonRewardCell})
    self.zhanshi_grid:SetStartZeroIndex(false)

	self.zhanshi_ten_grid = CommonRewardGrid.New()
    self.zhanshi_ten_grid:CreateCells({col = 5, change_cells_num = 1 , list_view = self.node_list["ph_zhanshii_ten_cell"],
    itemRender = TenCommonRewardCell})
    self.zhanshi_ten_grid:SetStartZeroIndex(false)

	if not self.best_item_list then
        self.best_item_list = AsyncListView.New(CommonRewardShenPinCell, self.node_list.best_item_list)
    end
end

function TipsGetCommonRewardView:CloseCallBack()
	GlobalEventSystem:Fire(OtherEventType.Common_Reward_Close3)
end

-- 注册按钮事件
function TipsGetCommonRewardView:RegisterEvent()
	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind1(self.OnSure, self))						--确定关闭界面
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnQuchuAgain, self))				--再来一次
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClinkSkipAnim, self))
end

function TipsGetCommonRewardView:OnClinkSkipAnim(is_on)
	self.is_skip_anim = is_on

	if self.other_info and nil ~= self.other_info.set_skip_anim_func then
		self.other_info.set_skip_anim_func(self.is_skip_anim)
	end
end


function TipsGetCommonRewardView:SetData(id_list, again_func, other_info, no_need_sort, sure_func)
	self.id_list = id_list
	self.again_func = again_func
	self.other_info = other_info
	self.no_need_sort = no_need_sort
	self.sure_func = sure_func

	self.is_skip_anim = false
	if self.other_info and nil ~= self.other_info.get_skip_anim_func then
		self.is_skip_anim = self.other_info.get_skip_anim_func()
	end
end

--刷新数据
function TipsGetCommonRewardView:OnFlush()

	local top_title_png = (self.other_info and self.other_info.top_title_png) and self.other_info.top_title_png or "a3_gxhd_djhd_gxhd"
	local bundle, asset = ResPath.GetRawImagesPNG(top_title_png)
    self.node_list["top_title_bg"].raw_image:LoadSprite(bundle, asset, function()
    	self.node_list["top_title_bg"].raw_image:SetNativeSize()
    end)

	self.node_list.skip_anim_toggle.toggle.isOn = self.is_skip_anim
	self.node_list.btn_one_more:CustomSetActive(self.again_func ~= nil)

	if self.other_info then
		self.node_list.btn_sure_text.text.text = self.other_info.sure_text or Language.Common.Confirm
		self.node_list.btn_again_txt.text.text = self.other_info.again_text or Language.TreasureHunt.BtnText[0]
		self.node_list.spend_root:CustomSetActive(self.other_info.stuff_id ~= nil and self.other_info.times ~= nil and self.other_info.spend ~= nil)
		self.node_list.show_reward_desc:CustomSetActive(self.other_info.show_reward_desc ~= nil)
		self.node_list.text_reward_desc.text.text = self.other_info.show_reward_desc or ""
		
		if self.other_info.stuff_id then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(self.other_info.stuff_id) --拥有的材料数量
			self.node_list.xianyu_icon:CustomSetActive(self.other_info.show_spend ~= false and has_num < self.other_info.times)
			self.node_list.zbitem_key:CustomSetActive(has_num > 0)

			self.node_list.zbitem_key_num.text.text = has_num
			self.node_list.one_more_cosume.text.text = (self.other_info.times - has_num) * self.other_info.spend

			local cost_id
			local cost_type = self.other_info.cost_type or COST_TYPE.LINGYU
			if cost_type == COST_TYPE.LINGYU then
				cost_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
			elseif cost_type == COST_TYPE.YANYUGE_SCORE then
				cost_id = COMMON_CONSTS.VIRTUAL_ITEM_CANG_JIN_SCORE
			end
			local cost_item_bundle, cost_item_asset = ResPath.GetItem(cost_id)
			self.node_list.xianyu_icon.image:LoadSprite(cost_item_bundle, cost_item_asset)
			self.node_list.img_zbitem.image:LoadSprite(ResPath.GetItem(self.other_info.stuff_id))
		end
	end

	local show_count = #self.id_list
	self:ChangeState(show_count)
	self:ResetItemData()
	self:ShowBestReward()
end

function TipsGetCommonRewardView:ChangeState(show_count)
	local is_ten = show_count <= 10
	local is_show_list = show_count > 0
	self.node_list["zhanshii_cell_root"]:SetActive(not is_ten and is_show_list)
	self.node_list["zhanshii_ten_cell_root"]:SetActive(is_ten and is_show_list)
	
	self.node_list["show_reward_root"].vertical_layout_group.padding.top = is_show_list and 0 or 90
end

function TipsGetCommonRewardView:SortItem(item_list)
	if self.no_need_sort then
		return item_list
	end

	local item_cfg,item_type
	for i,v in ipairs(item_list) do
		item_cfg, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		v.color = item_cfg and item_cfg.color or 0
		v.item_type = Sort_Type[item_type] or 1
		v.is_bind = item_cfg and item_cfg.isbind or 0
	end
	
	SortTools.SortDesc(item_list, "color", "item_type")
	return item_list
end

function TipsGetCommonRewardView:CancelTimeQuest()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function TipsGetCommonRewardView:CancelMoveScrollTimeQuest()
	if self.move_scroll_quest then
		GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
		self.move_scroll_quest = nil
	end
end

function TipsGetCommonRewardView:ResetItemData()
	if not self.id_list then
		return
	end

	local list_info = self.id_list
	list_info = self:SortItem(list_info)

    reword_count = #list_info
	local grid_target = #self.id_list <= 10 and self.zhanshi_ten_grid or self.zhanshi_grid
	if grid_target ~= nil then
		grid_target:SetDataList(list_info)
		self:CancelTimeQuest()
		ani_flag_t = {}
		ani_count = 1
		-- ANI_SPEED = 0.1 * ((col_num * row_num) / #self.id_list)

		if not self.is_skip_anim then
			self.time_quest = GlobalTimerQuest:AddTimesTimer(function()
				self:DoCellAnimTen(true)
				end, ANI_SPEED, reword_count )
		else
			for i = 1, reword_count do
				self:DoCellAnimTen(false)
			end
		end

		local is_not_auti_move = self.other_info and self.other_info.is_not_auti_move
		if not is_not_auti_move and #self.id_list > 10 then
			self:CancelMoveScrollTimeQuest()
			grid_target:JumptToPrecent(1)
			lerp = 1 / (#self.id_list - (col_num * row_num)) * 0.5		--每次减少多少

			if reword_count > 30 and not self.is_skip_anim then
				self.move_scroll_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:MoveScroll()
					if scroll_verticalNormalizedPosition <= 0 then
						scroll_verticalNormalizedPosition = 0
						self:CancelMoveScrollTimeQuest()
					end
				end, 0.03, 999999)
			end
		end
	end
end

function TipsGetCommonRewardView:DoCellAnimTen(do_tween)
	if not self.id_list then
		return
	end
	local grid_target = #self.id_list <= 10 and self.zhanshi_ten_grid or self.zhanshi_grid
    local cell = grid_target:GetCell(ani_count)
    ani_flag_t[ani_count] = true
	ani_count = ani_count + 1

	if cell ~= nil and cell:GetData() ~= nil then
		if do_tween then
			cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
			cell.view.transform:DOScale(Vector3(1, 1, 1), 0.2)
		else
			cell.view.transform.localScale = Vector3(1, 1, 1)
		end
		--特效
		-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0), nil, nil)
		cell:SetActive(true)
    end
end

function TipsGetCommonRewardView:MoveScroll()
	if ani_count > (col_num * row_num) then --and self.move_tween_complete then --
		scroll_verticalNormalizedPosition = scroll_verticalNormalizedPosition - lerp
	else
		scroll_verticalNormalizedPosition = 1
	end

	if self.node_list.ph_zhanshii_cell ~= nil and not IsNil(self.node_list.ph_zhanshii_cell.gameObject) and self.node_list.ph_zhanshii_cell:GetActive() then
		self.node_list.ph_zhanshii_cell.scroll_rect.verticalNormalizedPosition = scroll_verticalNormalizedPosition
	end
end


function TipsGetCommonRewardView:ShowBestReward()
	self.node_list["best_container_root"]:SetActive(false)
	if not self.other_info then
		return
	end

	if not self.other_info.best_data then
		return
	end

	self.node_list["best_container_root"]:SetActive(true)
	local best_data = self.other_info.best_data

	local best_list = {}
	if best_data.item_id then
		table.insert(best_list, {item_id = best_data.item_id, draw_corner_type = DRAW_ITEM_CORNER_QUALITY_TYPE.SHEN_PIN})
	end

	if best_data.item_ids then
		for _, temp_item_id in ipairs(best_data.item_ids) do
			table.insert(best_list, {item_id = temp_item_id, draw_corner_type = DRAW_ITEM_CORNER_QUALITY_TYPE.SHEN_PIN})
		end
	end

	if best_data.item_list then
		for _, temp_item in ipairs(best_data.item_list) do
			table.insert(best_list, {item_id = temp_item.item_id, num = temp_item.num, draw_corner_type = DRAW_ITEM_CORNER_QUALITY_TYPE.SHEN_PIN})
		end
	end

	self.best_item_list:SetDataList(best_list)
	self.node_list.best_des:CustomSetActive(#best_list <= 1)

	if best_data.best_des then
		self.node_list.best_des.text.text = best_data.best_des
	end

	if best_data.best_text then
		self.node_list.best_text.text.text = best_data.best_text
	end
end


function TipsGetCommonRewardView:OnSure()
	if self.sure_func then
		self.sure_func()
	end
	
	self:Close()
end

function TipsGetCommonRewardView:OnQuchuAgain()
	if self.again_func then
		self.again_func()
	end
end

----------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------
CommonRewardGrid = CommonRewardGrid or BaseClass(AsyncBaseGrid)

-- 获得指定的格子
function CommonRewardGrid:GetCell(index)
    for k, v in pairs(self.cell_list) do
        local row = math.floor((index - 1) / self.columns)
        if row == v:GetRows() and v:GetActive()  then
            for k1, v1 in pairs(v:GetAllCell()) do
                if v1:GetIndex() == index then
                    return v1
                end
            end
        end
    end
	return nil
end

CommonRewardShenPinCell = CommonRewardShenPinCell or BaseClass(ItemCell)

function CommonRewardShenPinCell:OnFlush()
	ItemCell.OnFlush(self)
	ItemCell.SetItemDrawCornerBg(self)
end

CommonRewardCell = CommonRewardCell or BaseClass(ItemCell)

function CommonRewardCell:OnFlush()
    self:SetActive(ani_flag_t[self.index] == true)
    self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)

	for k,v in pairs(BaseCell_Ui_Circle_Effect) do
		self:SetEffectEnable(false, v)
	end
	ItemCell.OnFlush(self)
	ItemCell.SetItemDrawCornerBg(self)
end

function CommonRewardCell:SetActive(value)
	ItemCell.SetVisible(self, value and (self.index == nil or ani_flag_t[self.index]))
end


TenCommonRewardCell = TenCommonRewardCell or BaseClass(ItemCell)
function TenCommonRewardCell:OnFlush()
	self:SetActive(ani_flag_t[self.index] == true)
    self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)

	for k,v in pairs(BaseCell_Ui_Circle_Effect) do
		self:SetEffectEnable(false, v)
	end
	ItemCell.OnFlush(self)
	ItemCell.SetItemDrawCornerBg(self)
end

function TenCommonRewardCell:SetActive(value)
	ItemCell.SetActive(self, value and (self.index == nil or ani_flag_t[self.index]))
end