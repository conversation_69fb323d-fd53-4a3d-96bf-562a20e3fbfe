-- H-合服活动-仙盟争霸.xls
local item_table={
[1]={item_id=48080,num=1,is_bind=1},
[2]={item_id=26191,num=2,is_bind=1},
[3]={item_id=26410,num=8,is_bind=1},
[4]={item_id=48071,num=5,is_bind=1},
[5]={item_id=47106,num=10,is_bind=1},
[6]={item_id=26410,num=6,is_bind=1},
[7]={item_id=48071,num=4,is_bind=1},
[8]={item_id=47106,num=8,is_bind=1},
[9]={item_id=26191,num=1,is_bind=1},
[10]={item_id=47106,num=6,is_bind=1},
[11]={item_id=48081,num=1,is_bind=1},
[12]={item_id=26191,num=4,is_bind=1},
[13]={item_id=26411,num=10,is_bind=1},
[14]={item_id=48071,num=6,is_bind=1},
[15]={item_id=47106,num=15,is_bind=1},
[16]={item_id=26191,num=3,is_bind=1},
[17]={item_id=26411,num=8,is_bind=1},
[18]={item_id=26411,num=6,is_bind=1},
[19]={item_id=48082,num=1,is_bind=1},
[20]={item_id=26191,num=5,is_bind=1},
[21]={item_id=26411,num=15,is_bind=1},
[22]={item_id=48071,num=8,is_bind=1},
[23]={item_id=47106,num=20,is_bind=1},
[24]={item_id=26411,num=12,is_bind=1},
[25]={item_id=47106,num=12,is_bind=1},
[26]={item_id=48083,num=1,is_bind=1},
[27]={item_id=26191,num=6,is_bind=1},
[28]={item_id=26411,num=20,is_bind=1},
[29]={item_id=48071,num=10,is_bind=1},
[30]={item_id=26410,num=10,is_bind=1},
}

return {
config_param={
{},
{start_server_day=41,end_server_day=60,grade=1,},
{start_server_day=61,end_server_day=80,grade=2,},
{start_server_day=81,end_server_day=999,grade=3,}
},

config_param_meta_table_map={
},
reward={
{},
{rank_min=2,rank_max=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{rank_min=3,rank_max=3,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8]},},
{rank_min=4,rank_max=4,reward_item={[0]=item_table[1],[1]=item_table[9],[2]=item_table[6],[3]=item_table[7],[4]=item_table[10]},},
{grade=1,reward_item={[0]=item_table[11],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},},
{grade=1,reward_item={[0]=item_table[11],[1]=item_table[16],[2]=item_table[17],[3]=item_table[14],[4]=item_table[15]},},
{grade=1,reward_item={[0]=item_table[11],[1]=item_table[16],[2]=item_table[18],[3]=item_table[4],[4]=item_table[5]},},
{grade=1,reward_item={[0]=item_table[11],[1]=item_table[2],[2]=item_table[18],[3]=item_table[4],[4]=item_table[8]},},
{grade=2,reward_item={[0]=item_table[19],[1]=item_table[20],[2]=item_table[21],[3]=item_table[22],[4]=item_table[23]},},
{grade=2,reward_item={[0]=item_table[19],[1]=item_table[12],[2]=item_table[24],[3]=item_table[22],[4]=item_table[23]},},
{grade=2,reward_item={[0]=item_table[19],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},},
{grade=2,reward_item={[0]=item_table[19],[1]=item_table[16],[2]=item_table[13],[3]=item_table[14],[4]=item_table[25]},},
{grade=3,reward_item={[0]=item_table[26],[1]=item_table[27],[2]=item_table[28],[3]=item_table[29],[4]=item_table[23]},},
{grade=3,reward_item={[0]=item_table[26],[1]=item_table[20],[2]=item_table[21],[3]=item_table[29],[4]=item_table[23]},},
{grade=3,reward_item={[0]=item_table[26],[1]=item_table[20],[2]=item_table[24],[3]=item_table[22],[4]=item_table[15]},},
{grade=3,reward_item={[0]=item_table[26],[1]=item_table[12],[2]=item_table[24],[3]=item_table[22],[4]=item_table[25]},}
},

reward_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
[14]=2,	-- depth:1
[15]=3,	-- depth:1
[16]=4,	-- depth:1
},
config_param_default_table={start_server_day=1,end_server_day=40,grade=0,num_limit=4,tips_outside="仙境名次越高，奖励越好",tips_name="夺仙争霸",tips_inside="1.参与活动开启后的<color=#99ffbb>首届夺仙争霸</color>，赢丰厚奖励\n2.争霸结束后，将按<color=#99ffbb>仙境排名</color>进行发奖，奖励以<color=#99ffbb>邮件形式</color>发放\n3.只有<color=#99ffbb>参与过活动期间第一次争霸</color>的仙境成员才有奖励哦",},

reward_default_table={grade=0,rank_min=1,rank_max=1,reward_item={[0]=item_table[1],[1]=item_table[16],[2]=item_table[30],[3]=item_table[4],[4]=item_table[5]},}

}

