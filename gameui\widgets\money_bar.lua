MoneyBar = MoneyBar or BaseClass(BaseRender)

local MONNEY_TYPE = {
	[GameEnum.MONEY_BAR.BIND_GOLD] = "bind_gold",
	[GameEnum.MONEY_BAR.GOLD] = "gold",
	[GameEnum.MONEY_BAR.COIN] = "coin",
	[GameEnum.MONEY_BAR.SILVER_TICKET] = "silver_ticket",
	[GameEnum.MONEY_BAR.SHENGWANG] = "shengwang",
    [GameEnum.MONEY_BAR.GONG_YU] = "gongyu",
	[GameEnum.MONEY_BAR.CHIVALROUS] = "chivalrous",
	[GameEnum.MONEY_BAR.CASH_POINT] = "cash_point",

	[GameEnum.MONEY_BAR.MINGWEN_EXP] = "mingwen_exp",
	[GameEnum.MONEY_BAR.MINGWEN_MINYIN] = "mingwen_minyin",
	[GameEnum.MONEY_BAR.MINGWEN_XINJING] = "mingwen_xinjing",
	[GameEnum.MONEY_BAR.VOLUME] = "volume",
	[GameEnum.MONEY_BAR.CANG_JIN_SCORE] = "cangjin_score",
	
}

local MONEY_BAR_VARIABLE_NAME = {
	[GameEnum.MONEY_BAR.BIND_GOLD] = "BindGold",
	[GameEnum.MONEY_BAR.GOLD] = "Gold",
	[GameEnum.MONEY_BAR.COIN] = "Coin",
	[GameEnum.MONEY_BAR.SILVER_TICKET] = "sliver_ticket",
    [GameEnum.MONEY_BAR.SHENGWANG] = "shengwang",
    [GameEnum.MONEY_BAR.GONG_YU] = "gongyu",
	[GameEnum.MONEY_BAR.CHIVALROUS] = "chivalrous",
	[GameEnum.MONEY_BAR.CASH_POINT] = "cash_point",

	[GameEnum.MONEY_BAR.MINGWEN_EXP] = "mingwen_exp",
	[GameEnum.MONEY_BAR.MINGWEN_MINYIN] = "mingwen_minyin",
	[GameEnum.MONEY_BAR.MINGWEN_XINJING] = "mingwen_xinjing",
	[GameEnum.MONEY_BAR.VOLUME] = "volume",
	[GameEnum.MONEY_BAR.CANG_JIN_SCORE] = "cangjin_score",
}

function MoneyBar:__init(instance)
end

function MoneyBar:__delete()
	self.money_list = {}
	self:UnAllListen()

	if self.money_bar_change_event then
        GlobalTimerQuest:CancelQuest(self.money_bar_change_event)
		self.money_bar_change_event = nil
	end

	if self.delay_flush_timer then
		GlobalTimerQuest:CancelQuest(self.delay_flush_timer)
		self.delay_flush_timer = nil
	end

	if self.play_effect_recharge_timer then
		GlobalTimerQuest:CancelQuest(self.play_effect_recharge_timer)
		self.play_effect_recharge_timer = nil
	end

	self.other_name = ""
	self.is_gold_ani = nil

    self.gold_callback = nil
    self.ming_wen_callback = nil
    self.bagua_callback = nil
	self.beast_callback = nil

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	self.role_data_change_callback = nil
	self.recharge_volume_num = nil

	self.timer_effect = nil
	self.play_effect_num = nil
    self.icon_click_callback = nil
	self:CleanEffectTimer()
end

function MoneyBar:OpenCallBack()
	-- self.node_list.effect_lingyu:CustomSetActive(false)
end

function MoneyBar:LoadCallBack()
	local x, y = 0, 0
	if nil ~= self.is_show then
		x = self.is_show["pos_x"]
		y = self.is_show["pos_y"]
	end
	self.view.transform.anchoredPosition = Vector3(x, y, 0)

	self.money_list = {}
	for i = 1, GameEnum.MONEY_BAR.MAX_TYPE do
		local data = {}
		data.need_change = true
        data.value = self.node_list[MONEY_BAR_VARIABLE_NAME[i]]
		data.text = U3DObject(data.value.transform:Find("GameObject/Text").gameObject)
		self.money_list[MONNEY_TYPE[i]] = data
	end

	self.node_list["BtnBindGold"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD))
	self.node_list["BtnGold"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_GOLD))
	self.node_list["Btnsliverticket"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO))
	if self.node_list["BtnCoin"] then
		self.node_list["BtnCoin"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN))
	end

	if self.node_list["Btngongyu"] then
		self.node_list["Btngongyu"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.ITEM_GONGYU))
	end

	if self.node_list["Btnvolume"] then
		self.node_list["Btnvolume"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.ITEM_RECHARGE_VOLUME))
	end

	if self.node_list["BtnMingWenExp"] then
		self.node_list["BtnMingWenExp"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_MINGWEN))
		self.node_list["BtnMingWenMinYin"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_MINYIN))
		self.node_list["BtnMingWenXinJing"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_XINJING))
	end

	if self.node_list["BtnShengWang"] then
		self.node_list["BtnShengWang"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_HORNOR))
	end

	if self.node_list["BtnChivalrous"] then
		self.node_list["BtnChivalrous"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_CHIVALROUS))
	end

	if self.node_list["BtnCashPoint"] then
		self.node_list["BtnCashPoint"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_CASH_POINT))
	end
	
    if self.node_list["Btnbaguabi"] then
		local item_id = TianShenBaGuaWGData.Instance:GetBaGuaBiCfg()
        self.node_list["Btnbaguabi"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, item_id))
    end

	if self.node_list["btn_beast_exp"] then
        self.node_list["btn_beast_exp"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_BEAST_EXP))
	end

	if self.node_list["Btncangjin_score"] then
        self.node_list["Btncangjin_score"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.VIRTUAL_ITEM_CANG_JIN_SCORE))
	end

	if self.node_list["collect_card_chip_btn"] then
        self.node_list["collect_card_chip_btn"].button:AddClickListener(BindTool.Bind(self.OnClickAddGold, self, COMMON_CONSTS.ITEM_COLLECT_CARD_CHIP))
	end

	--自定义货币.
	for i = 1, 3 do
		if self.node_list["custom_btn_" .. i] then
			XUI.AddClickEventListener(self.node_list["custom_btn_" .. i], BindTool.Bind(self.OnClickCustomItemTip, self, i))
		end
	end

	self.role_data_change_callback = BindTool.Bind1(self.RoleDataChangeCallback, self)
	local attr_t = {"recharge_volume"}
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, attr_t)

    self.gold_callback = BindTool.Bind(self.MoneyChangeEvent, self)
    self.ming_wen_callback = BindTool.Bind(self.MingWenChangeEvent, self)
    self.bagua_callback = BindTool.Bind(self.BaGuaMoneyChangeEvent, self)
	self.beast_callback = BindTool.Bind(self.BeastExpNumChangeEvent, self)
	self.cangjin_callback = BindTool.Bind(self.CangJinScoreChangeEvent, self)

	self.item_data_event = BindTool.Bind(self.BagDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	self.mingwen_exp_num = 0
    self.mingwen_xinjing_num = 0
    self.mingwen_minyin_num = 0
	self:AddAllListen()
	self:Flush()
end

function MoneyBar:PlaylingyuEffect()
	-- self.timer_effect = GlobalTimerQuest:AddTimesTimer(function ()
	-- 			if self:IsOpen() then
	-- 				self.node_list.effect_lingyu:CustomSetActive(false)
	-- 				self.play_effect_num = self.play_effect_num - 1
	-- 				self:CleanEffectTimer()

	-- 				if self.play_effect_num > 0 then --这里继续播
	-- 					self:PlaylingyuEffect()
	-- 				end
	-- 			end
	-- 		end,
	-- 		2.5,
	-- 		1
	-- 	)

	-- self.node_list.effect_lingyu:CustomSetActive(true)
end

--背包物品发生改变监听
function MoneyBar:BagDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	--自定义货币.
	for i = 1, 3 do
		if self.show_custom and self.show_custom[i] and self.show_custom[i].is_show then
			if change_item_id == self.show_custom[i].item_id then
				if self.is_gold_ani then
					self:FlushCustom()
				else
					self:Flush()
				end
				break
			end
		end
	end
end

-- 	--不是使用真充卡
-- 	if (not new_num or not old_num or new_num >= old_num) or item_cfg.use_type ~= Item_Use_Type.XianYu then
-- 		return
-- 	end

-- 	if self.timer_effect then
-- 		self.play_effect_num = self.play_effect_num + 1
-- 	else
-- 		self.play_effect_num = 1
-- 	end

-- 	if not self.timer_effect then
-- 		self:PlaylingyuEffect()
-- 	end
-- end

--得到灵玉播放特效
function MoneyBar:LingyuCountAdd()
	if self.timer_effect then
 		self.play_effect_num = self.play_effect_num + 1
 	else
 		self.play_effect_num = 1
 	end

 	if not self.timer_effect then
 		self:PlaylingyuEffect()
 	end
end

-- 人物属性发生变化
function MoneyBar:RoleDataChangeCallback(attr_name, value, old_value)
	if attr_name == "recharge_volume" then
		self:BeastVolumeChangeEvent()
	end
end

function MoneyBar:CleanEffectTimer()
	if self.timer_effect then
        GlobalTimerQuest:CancelQuest(self.timer_effect)
        self.timer_effect = nil
    end
end

function MoneyBar:AddAllListen()
	if not self.money_change_event and self.gold_callback then
		self.money_change_event = GlobalEventSystem:Bind(OtherEventType.Gold_Change_Event, self.gold_callback)
	end

	if not self.mingwen_change_event and self.ming_wen_callback then
		self.mingwen_change_event = GlobalEventSystem:Bind(OtherEventType.Ming_Wen_Change, self.ming_wen_callback)
    end

    if not self.bagua_change_vevet and self.bagua_callback then
        self.bagua_change_vevet = GlobalEventSystem:Bind(OtherEventType.Ba_Gua_Change, self.bagua_callback)
    end

	if not self.beast_change_vevet and self.beast_callback then
        self.beast_change_vevet = GlobalEventSystem:Bind(OtherEventType.Beast_EXP_Change, self.beast_callback)
	end
	
	if not self.cangjin_score_event and self.cangjin_callback then
		self.cangjin_score_event = GlobalEventSystem:Bind(OtherEventType.CANG_JIN_SCORE_CHANGE, self.cangjin_callback)
	end
end

function MoneyBar:UnAllListen()
	GlobalEventSystem:UnBind(self.money_change_event)
    self.money_change_event = nil

    GlobalEventSystem:UnBind(self.mingwen_change_event)
    self.mingwen_change_event = nil

    GlobalEventSystem:UnBind(self.gongyu_change_vevet)
    self.gongyu_change_vevet = nil

    GlobalEventSystem:UnBind(self.bagua_change_vevet)
    self.bagua_change_vevet = nil

	GlobalEventSystem:UnBind(self.beast_change_vevet)
	self.beast_change_vevet = nil
	
	GlobalEventSystem:UnBind(self.cangjin_score_event)
	self.cangjin_score_event = nil
end

function MoneyBar:SetIconClickCallBack(call_back)
    self.icon_click_callback = call_back
end

function MoneyBar:OnClickAddGold(item_id)
    if self.icon_click_callback ~= nil then
        self.icon_click_callback()
    end

	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function MoneyBar:OnClickCustomItemTip(index)
	if self.show_custom and self.show_custom[index] and self.show_custom[index].is_show then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.show_custom[index].item_id})
	end
end

--[[
	show_params = {
		show_gold = true, show_bind_gold = true, show_coin = true,
		show_silver_ticket = true, show_mingwen_exp = true, show_mingwen_minyin = true,
		show_mingwen_xinjing = true, show_shengwang_xinjing = true, show_gongyu = true,
		show_bagua = true, show_chivalrous = true, show_cash_point = true

		--自定义货币.
		show_custom_1 = true,
		custom_item_id_1 = 0,
		custom_num_1 = 0,
	}
]]
function MoneyBar:SetMoneyShowInfo(x, y, show_params)
	if nil ~= self.view then
		self.view.transform.anchoredPosition = Vector3(x, y, 0)
	end

	self.is_show = {}
	self.is_show["pos_x"] = x
	self.is_show["pos_y"] = y
	self.is_show["gold"] = (show_params.show_gold == nil or show_params.show_gold) or false
	self.is_show["bind_gold"] = show_params.show_bind_gold or false
	self.is_show["coin"] = show_params.show_coin or false
	self.is_show["shengwang"] = show_params.show_shengwang_xinjing or false
	self.is_show["chivalrous"] = show_params.show_chivalrous or false
	self.is_show["cash_point"] = show_params.show_cash_point or false

    self.show_gongyu = show_params.show_gongyu or false
	--self.is_show["silver_ticket"] = show_params.show_silver_ticket or false
	self.show_mingwen_exp = show_params.show_mingwen_exp or false
	self.show_mingwen_minyin = show_params.show_mingwen_minyin or false
	self.show_mingwen_xinjing = show_params.show_mingwen_xinjing or false
    self.show_baguabi = show_params.show_bagua or false
	self.show_beast = show_params.show_beast or false
	self.show_volume = show_params.show_volume or false
	self.show_cangjin_score = show_params.show_cangjin_score or false

	self.show_custom = {}
	--自定义货币.
	for i = 1, 3 do
		self.show_custom[i] = {}
		self.show_custom[i].is_show = show_params["show_custom_" .. i] or false
		self.show_custom[i].item_id = show_params["custom_item_id_" .. i] or 0
		self.show_custom[i].custom_num = show_params["custom_num_" .. i]
	end
end

-- 招财猫界面 货币动画
function MoneyBar:FortuneCatDoAni(protocol)
    local main_role_vo = RoleWGData.Instance:GetRoleInfo()
    if main_role_vo["gold"] > protocol.gold then
        if self.money_bar_change_event then
            GlobalTimerQuest:CancelQuest(self.money_bar_change_event)
        end

        self.is_gold_ani = true
        local gold = main_role_vo["gold"]--CommonDataManager.ConverNumValue(main_role_vo["gold"])
        local gold_1 = protocol.gold--CommonDataManager.ConverNumValue(protocol.gold)
        local change_value, change_str = CommonDataManager.ConverMoneyBarNew(protocol.gold)
        local text_obj = self.node_list.xianyu_text:GetComponent(typeof(TMPro.TextMeshProUGUI))
        local complete_fun = function()
            text_obj.text = (change_value) .. change_str
            RoleWGData.Instance:SetBeforeLastMoney(main_role_vo.gold)
        end

        local update_fun = function(num)
            local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
            if postfix_name == "" then
                text_obj.text = (string.format("%.0f", value)) .. postfix_name
            else
                text_obj.text = (value) .. postfix_name
            end
        end

        UITween.DONumberTo(text_obj, gold, gold_1, 1.5, update_fun, complete_fun)
        self.node_list.Gold.transform:SetLocalScale(1,1,1)
        self.node_list.Gold.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
    else
        self.is_gold_ani = true
        local old_value =  main_role_vo["gold"]
        local end_func = function()
            local gold = old_value
            local gold_1 = protocol.gold
            local change_value, change_str = CommonDataManager.ConverMoneyBarNew(protocol.gold)
            local text_obj = self.node_list.xianyu_text:GetComponent(typeof(TMPro.TextMeshProUGUI))
            local complete_fun = function()
                text_obj.text = (change_value) .. change_str
                self.is_gold_ani = false
                RoleWGData.Instance:SetBeforeLastMoney(main_role_vo.gold)
            end

            local update_fun = function(num)
                local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
                if postfix_name == "" then
                    text_obj.text = (string.format("%.0f", value)) .. postfix_name
                else
                    text_obj.text = (value) .. postfix_name
                end
            end

            UITween.DONumberTo(text_obj, gold, gold_1, 1.5, update_fun, complete_fun)
            self.node_list.Gold.transform:SetLocalScale(1,1,1)
            self.node_list.Gold.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
        end

        if FortuneCatWGCtrl.Instance:GetIsDotween() then
            self.is_gold_ani = true
			 --招财猫界面动画5秒+仙玉飞的动画1.5秒
			self:DelayOperate(end_func, 6.5)
        else
			--self:DelayOperate(delay_fun, delay_time)
            self:DelayOperate(end_func, 2)
        end
    end
end

function MoneyBar:MoneyChangeEvent(protocol)
    if self:IsNil() then
		return
	end
	
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
    local is_add_gold = (protocol.gold or 0) > (main_role_vo["gold"] or 0)
    local is_add_bind_gold =  (protocol.bind_gold or 0) > (main_role_vo["bind_gold"] or 0)
	if protocol.reason_type == COST_REASON_TYPE.COST_REASON_INVALID then
		if is_add_gold or is_add_bind_gold then
			GlobalTimerQuest:AddDelayTimer(function ()
				self:Flush()
			end, 1.3)
			return
		end
	end

    if protocol.reason_type == COST_REASON_TYPE.GOLD_ADD_FORTUNE_CAT and protocol.gold then --招财猫
        self:FortuneCatDoAni(protocol)
		return
	end

	if protocol.gold and main_role_vo["gold"] < protocol.gold then
		if protocol.reason_type == COST_REASON_TYPE.REBATE_DAY_ACTIVITY then
			return
		end

		local is_recharge = RechargeWGCtrl.Instance:GetPanelShowIndex()
		if not is_recharge then
			self:Flush()
			return
		end

		GlobalEventSystem:Fire(OtherEventType.QiFu_Gold_Change_Event, GameEnum.MONEY_BAR.GOLD)
	elseif protocol.silver_ticket and main_role_vo["silver_ticket"] < protocol.silver_ticket
	and protocol.reason and protocol.reason == COST_REASON_TYPE.YUANBAO_QIFU_CHANG then
		local is_qifu = QiFuWGCtrl.Instance:GetPanelShowIndex()
		if not is_qifu then
			self:Flush()
			return
		end

		GlobalEventSystem:Fire(OtherEventType.QiFu_Gold_Change_Event)
	end

	self:Flush()
--[[
	if protocol.gold and main_role_vo["gold"] < protocol.gold then
		if self.is_gold_ani then
			return
		end
		
		local is_recharge = RechargeWGCtrl.Instance:GetPanelShowIndex()
		if not is_recharge
		and (protocol.reason_type ~= COST_REASON_TYPE.GOLD_ADD_MERGE_ZCMIAIMIAO or
			protocol.reason_type ~= COST_REASON_TYPE.REBATE_DAY_ACTIVITY) then
			self:Flush()
			return
		end

		if protocol.reason_type == COST_REASON_TYPE.GOLD_ADD_MERGE_ZCMIAIMIAO or
		protocol.reason_type == COST_REASON_TYPE.REBATE_DAY_ACTIVITY then
			return
		end


		self.node_list.Text_1.text.text = ""
		self.is_gold_ani = true
		self:DelayOperate(function()
			self.is_gold_ani = false
			self:Flush()
		end, 1.5)

		GlobalEventSystem:Fire(OtherEventType.QiFu_Gold_Change_Event, GameEnum.MONEY_BAR.GOLD)
	elseif protocol.silver_ticket and main_role_vo["silver_ticket"] < protocol.silver_ticket
		and protocol.reason and protocol.reason == COST_REASON_TYPE.YUANBAO_QIFU_CHANG then

		local is_qifu = QiFuWGCtrl.Instance:GetPanelShowIndex()
		if not is_qifu then
			self:Flush()
			return
		end

		self.is_gold_ani = true
		self:DelayOperate(function()
			self.is_gold_ani = false
			self:Flush()
		end, 2)

		GlobalEventSystem:Fire(OtherEventType.QiFu_Gold_Change_Event)

	elseif protocol.reason and protocol.reason == COST_REASON_TYPE.YUANBAO_REDPAPER_CHANG
		or protocol.reason == COST_REASON_TYPE.YUANBAO_GUILDSALARY_CHANG
		or protocol.reason == COST_REASON_TYPE.YUANBAO_HUMO_CHANG
		or protocol.reason == COST_REASON_TYPE.YUANBAOKA_CHANGE then

		--防止背包这种不释放的界面也漂元宝特效
		if self.view and self.view.gameObject and self.view.gameObject.activeInHierarchy == true then
			local is_redpack = protocol.reason == COST_REASON_TYPE.YUANBAO_REDPAPER_CHANG
			local effect_count = is_redpack and 15 or 20
			GuildWGData.Instance:SetRedPacketAnimShow(is_redpack)
			self:PlayEffectByRecharge(GameEnum.NEW_MONEY_BAR.SILVER_TICKET, self.node_list.moneybar_effect_pos.transform, "MoneyBar", effect_count)
		end

	elseif protocol.reason == COST_REASON_TYPE.SILVER_ADD_FETCH_TOTAL_REDPAPER then
		local end_obj = self:GetSlicketNode(GameEnum.NEW_MONEY_BAR.SILVER_TICKET)
		GuildWGData.Instance:SetRedPacketAnimEndObj(end_obj)
		self:DelayOperate(function()
			self:Flush()
		end, 1.1)

	elseif protocol.bind_gold and main_role_vo["bind_gold"] < protocol.bind_gold then
		if protocol.bind_gold - main_role_vo["bind_gold"] >= 10 and protocol.reason_type ~= COST_REASON_TYPE.BIND_GOLD_ADD_FIRST_BOSS_PERSON_KILL then
			self:PlayEffectByRecharge(GameEnum.NEW_MONEY_BAR.BIND_XIANYU, self.node_list.moneybar_effect_pos.transform, "MoneyBar", 20)
			if self.is_gold_ani then
				return
			end

			self:DelayOperate(function()
				self.is_gold_ani = false
				self:Flush()
			end, 1.5)
		else
			self:Flush()
		end

	elseif protocol.coin and main_role_vo["coin"] < protocol.coin then
		local is_qifu = QiFuWGCtrl.Instance:GetPanelShowIndex()
		if not is_qifu then
			self:Flush()
			return
		end

		self.is_gold_ani = true
		local old_money_value = main_role_vo["coin"]--CommonDataManager.ConverNumValue(main_role_vo["silver_ticket"])
		local new_money_value = protocol.coin--CommonDataManager.ConverNumValue(protocol.silver_ticket)
		local change_value, change_str = CommonDataManager.ConverMoneyBarNew(new_money_value)
		local delay_fun = function()
			local text_obj = self.node_list.tongbi_text:GetComponent(typeof(TMPro.TextMeshProUGUI))
			local complete_fun = function()
				text_obj.text = (change_value) .. change_str
				self.is_gold_ani = false
				RoleWGData.Instance:SetBeforeLastMoney(main_role_vo.gold, main_role_vo.bind_gold, main_role_vo.silver_ticket, main_role_vo.coin, main_role_vo.shengwang, true)
				self:Flush()
			end

			local update_fun = function(num)
				local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
				if postfix_name == "" then
					text_obj.text = (string.format("%.0f", value)) .. postfix_name
				else
					text_obj.text = (value) .. postfix_name
				end
			end

			UITween.DONumberTo(text_obj, old_money_value, new_money_value, 1.5, update_fun, complete_fun)
			local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
			EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.tongqian_icon.transform,1)
			self.node_list.Coin.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)--DOShakeScale(1.5)
		end

		self:DelayOperate(delay_fun, 1.5)
		if ViewManager.Instance:IsOpen(GuideModuleName.QIFU) and (protocol.coin - main_role_vo["coin"]) > COIN_NUM.COIN_NUMS then
			self:PlayEffectByRecharge(GameEnum.NEW_MONEY_BAR.COIN, self.node_list.moneybar_effect_pos.transform, "MoneyBar", effect_count)
		end
	else
		self:Flush()
	end
]]
end

function MoneyBar:DelayOperate(delay_fun, delay_time)
	if self.money_bar_change_event then
		GlobalTimerQuest:CancelQuest(self.money_bar_change_event)
	end

	self.money_bar_change_event = GlobalTimerQuest:AddDelayTimer(delay_fun, delay_time)
end

function MoneyBar:OnFlush()
	if self.is_gold_ani then
		return
	end

	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	for k,v in pairs(self.money_list) do
		if main_role_vo[k] and v.need_change then
			local old_money_value = RoleWGData.Instance:GetOldMoney(k)
			local new_money_value = main_role_vo[k] or 0
			local change_value, change_str = CommonDataManager.ConverMoneyBarNew(new_money_value)
			local text_text = v.text.text
			--local show_num = CommonDataManager.ConverMoneyBarNew(old_money_value)
			--local show_num_1, str = CommonDataManager.ConverMoneyBarNew(new_money_value)
			if old_money_value ~= new_money_value then
				self.node_list[MONNEY_TYPE_BAR_TEXT_GROUP[k]].transform.localScale = u3dpool.vec3(1, 1, 1)
				self.node_list[MONNEY_TYPE_BAR_TEXT_GROUP[k]].transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)

				-- local bundle_name, asset_name = ResPath.GetUIEffect("UI_yuanbao_get")
				-- EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list[MONNEY_TYPE_BAR_TEXT_1[k]].transform, 1, nil, nil, nil, nil)

				local complete_fun = function()
					text_text.text = (change_value) .. change_str
					self.node_list[MONNEY_TYPE_BAR_TEXT_GROUP[k]].transform.localScale = u3dpool.vec3(1, 1, 1)
					if self.delay_flush_timer then
						GlobalTimerQuest:CancelQuest(self.delay_flush_timer)
						self.delay_flush_timer = nil
					end

					self.delay_flush_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.FlushAllMoney, self), 1)
				end

				local update_fun = function(num)
					local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
					if postfix_name == "" then
						text_text.text = (string.format("%.0f", value)) .. postfix_name
					else
						text_text.text = (value) .. postfix_name
					end
				end

				if self.delay_flush_timer then
					GlobalTimerQuest:CancelQuest(self.delay_flush_timer)
					self.delay_flush_timer = nil
				end

				if text_text == nil or old_money_value == nil or new_money_value == nil then
					print_error("-----MoneyBar 报Nil-----", k, text_text == nil, old_money_value == nil, new_money_value == nil)
				else
					UITween.DONumberTo(text_text, old_money_value, new_money_value, 1.5, update_fun, complete_fun)
				end
			else
				v.text.text.text = (change_value) .. change_str
			end
		end

		v.value.gameObject:SetActive(self.is_show[k])
	end

	if self.node_list["MingWenExp"] then
		self.mingwen_exp_num, self.mingwen_xinjing_num, self.mingwen_minyin_num = MingWenWGData.Instance:GetMingWenExpAndXinJing()
		self.node_list["MingWenExp"]:SetActive(self.show_mingwen_exp)
		self.node_list["mingwen_exp_text"].text.text = self.mingwen_exp_num

		self.node_list["MingWenMinYin"]:SetActive(self.show_mingwen_minyin)
		self.node_list["mingwen_minyin_text"].text.text = self.mingwen_minyin_num

		self.node_list["MingWenXinJing"]:SetActive(self.show_mingwen_xinjing)
		self.node_list["mingwen_xinjing_text"].text.text = self.mingwen_xinjing_num
    end

    if self.node_list["baguabi"] and TianShenBaGuaWGData.Instance ~= nil then
        self.node_list.baguabi.transform.localScale = u3dpool.vec3(1, 1, 1)
        self.bagua_num = TianShenBaGuaWGData.Instance:GetBaGuaCoinNum()
        local value, postfix_name = CommonDataManager.ConverMoneyBarNew(self.bagua_num)
        if postfix_name == "" then
            self.node_list["baguabi_text"].text.text = (string.format("%.0f", value)) .. postfix_name
        else
            self.node_list["baguabi_text"].text.text = (value) .. postfix_name
        end
        self.node_list["baguabi"]:SetActive(self.show_baguabi)
    end

	if self.node_list["beast_exp"] then
		self.beast_exp_num = ControlBeastsWGData.Instance:GetBeastBaseInfoExp()
		self.node_list["beast_exp"]:SetActive(self.show_beast)
        local value, postfix_name = CommonDataManager.ConverMoneyBarNew(self.beast_exp_num)
        if postfix_name == "" then
            self.node_list["beast_exp_text"].text.text = (string.format("%.0f", value)) .. postfix_name
        else
            self.node_list["beast_exp_text"].text.text = (value) .. postfix_name
        end
	end

	self:FlushCustom()

	if self.node_list["volume"] and self.recharge_volume_num == nil then
		self.recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume")
		self.node_list["volume"]:SetActive(self.show_volume)
        local value, postfix_name = CommonDataManager.ConverMoneyBarNew(self.recharge_volume_num)
        if postfix_name == "" then
            self.node_list["volume_txt"].text.text = (string.format("%.0f", value)) .. postfix_name
        else
            self.node_list["volume_txt"].text.text = (value) .. postfix_name
        end
	end

	if self.node_list["cangjin_score"] and self.cangjin_score_num == nil then
		self.cangjin_score_num = YanYuGeWGData.Instance:GetCurScore()
		self.node_list["cangjin_score"]:SetActive(self.show_cangjin_score)
        local value, postfix_name = CommonDataManager.ConverMoneyBarNew(self.cangjin_score_num)
        if postfix_name == "" then
            self.node_list["cangjin_score_txt"].text.text = (string.format("%.0f", value)) .. postfix_name
        else
            self.node_list["cangjin_score_txt"].text.text = (value) .. postfix_name
        end
	end

	RoleWGData.Instance:SetBeforeLastMoney(main_role_vo.gold, main_role_vo.bind_gold, main_role_vo.silver_ticket, main_role_vo.coin, main_role_vo.shengwang, main_role_vo.chivalrous, main_role_vo.cash_point, true)
end

function MoneyBar:FlushCustom()
	--自定义货币.
	for i = 1, 3 do
		if self.show_custom and self.show_custom[i] and self.node_list["custom_" .. i] then
			self.node_list["custom_" .. i]:SetActive(self.show_custom[i].is_show)
			if self.show_custom[i].is_show then
				local has_num = 0
				if self.show_custom[i].custom_num then
					has_num = self.show_custom[i].custom_num
				else
					has_num = ItemWGData.Instance:GetItemNumInBagById(self.show_custom[i].item_id) --拥有的材料数量
				end
				
				local value, postfix_name = CommonDataManager.ConverMoneyBarNew(has_num)
				if postfix_name == "" then
					self.node_list["custom_text_" .. i].text.text = (string.format("%.0f", value)) .. postfix_name
				else
					self.node_list["custom_text_" .. i].text.text = (value) .. postfix_name
				end

				local bundel, asset = ItemWGData.Instance:GetTipsItemIcon(self.show_custom[i].item_id)
				self.node_list["custom_icon_" .. i].image:LoadSprite(bundel, asset)
			end
		end
	end
end

-- 动作结束一秒后同步，避免数据不一致
function MoneyBar:FlushAllMoney()
	self.delay_flush_timer = nil
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	if nil == main_role_vo then
		return
	end

	local change_value, change_str
	for k,v in pairs(self.money_list) do
		if main_role_vo[k] then
			change_value, change_str = CommonDataManager.ConverMoneyBarNew(main_role_vo[k])
			v.text.text.text = change_value .. change_str
		end
	end

	RoleWGData.Instance:SetBeforeLastMoney(main_role_vo.gold, main_role_vo.bind_gold, main_role_vo.silver_ticket, main_role_vo.coin, main_role_vo.shengwang, main_role_vo.chivalrous, main_role_vo.cash_point, true)
end

function MoneyBar:GetSlicketNode(pos_type)
	if self.node_list and self.node_list.yuanbao_icon and (nil == pos_type or pos_type == GameEnum.NEW_MONEY_BAR.SILVER_TICKET) then
		return self.node_list.yuanbao_icon
	end

	if self.node_list and self.node_list.bangyu_icon and (nil == pos_type or pos_type == GameEnum.NEW_MONEY_BAR.BIND_XIANYU) then
		return self.node_list.bangyu_icon
	end

	if self.node_list and self.node_list.tongqian_icon and (nil == pos_type or pos_type == GameEnum.NEW_MONEY_BAR.COIN) then
		return self.node_list.tongqian_icon
	end

	if pos_type == GameEnum.NEW_MONEY_BAR.XIANYU and self.node_list and self.node_list.xianyu_icon then
		return self.node_list.xianyu_icon
	end
end

function MoneyBar:PlayEffectByRecharge(money_type, rect, view_name, effect_count)
	rect = rect or self.node_list.moneybar_effect_pos.transform
	FightWGCtrl.Instance:DoMoneyEffect(money_type, rect)
	if self.play_effect_recharge_timer then
		GlobalTimerQuest:CancelQuest(self.play_effect_recharge_timer)
		self.play_effect_recharge_timer = nil
	end

	self.play_effect_recharge_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:PlayEffectByRechargeSecond(money_type, rect, view_name, effect_count)
	end, 0.5)
end

function MoneyBar:PlayEffectByRechargeSecond(money_type, rect, view_name, effect_count)
	if self.play_effect_recharge_timer then
		GlobalTimerQuest:CancelQuest(self.play_effect_recharge_timer)
		self.play_effect_recharge_timer = nil
	end

	if not self:GetActive() then
		return
	end

	TipWGCtrl.Instance:DestroyFlyEffectByViewName(view_name)
	local eff = MONEY_BAR_EFFECT[money_type]
	if nil == eff and nil == eff.FLY then
		return
	end

	effect_count = effect_count or 18
	local end_obj = self:GetSlicketNode(money_type)
	local bundle, asset = ResPath.GetEffectUi(eff.FLY)
	if end_obj and bundle and asset then
		TipWGCtrl.Instance:ShowFlyEffectManager(view_name, bundle, asset, rect, end_obj,
				DG.Tweening.Ease.OutCubic, 0.5, nil, nil, effect_count, 200, nil, false, true)

		self.play_effect_recharge_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:Flush()
			if self.play_effect_recharge_timer then
				GlobalTimerQuest:CancelQuest(self.play_effect_recharge_timer)
				self.play_effect_recharge_timer = nil
			end
		end, 0.5)
	end
end

function MoneyBar:MingWenChangeEvent()
	if not self.node_list["MingWenExp"] then
		return
	end

	local new_mingwen_exp_num, new_mingwen_xinjing_num, new_mingwen_minyin_num = MingWenWGData.Instance:GetMingWenExpAndXinJing()
	if new_mingwen_exp_num ~= self.mingwen_exp_num then
		local complete_fun = function()
			self.mingwen_exp_num = new_mingwen_exp_num
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["mingwen_exp_text"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["mingwen_exp_text"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["mingwen_exp_text"].text, self.mingwen_exp_num, new_mingwen_exp_num, 1.5, update_fun, complete_fun)
	end

	if new_mingwen_xinjing_num ~= self.mingwen_xinjing_num then
		local complete_fun = function()
			self.mingwen_xinjing_num = new_mingwen_xinjing_num
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["mingwen_xinjing_text"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["mingwen_xinjing_text"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["mingwen_xinjing_text"].text, self.mingwen_xinjing_num, new_mingwen_xinjing_num, 1.5, update_fun, complete_fun)
	end

	if new_mingwen_minyin_num ~= self.mingwen_minyin_num then
		local complete_fun = function()
			self.mingwen_minyin_num = new_mingwen_minyin_num
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["mingwen_minyin_text"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["mingwen_minyin_text"].text.text = (value) .. postfix_name
			end
		end

		UITween.DONumberTo(self.node_list["mingwen_minyin_text"].text, self.mingwen_minyin_num, new_mingwen_minyin_num, 1.5, update_fun, complete_fun)
	end
end

function MoneyBar:BaGuaMoneyChangeEvent()
    if not self.node_list["baguabi"] then
		return
	end

	local bagua_money = TianShenBaGuaWGData.Instance:GetBaGuaCoinNum()
	if bagua_money ~= self.bagua_num then
		local complete_fun = function()
			self.bagua_num = bagua_money
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["baguabi_text"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["baguabi_text"].text.text = (value) .. postfix_name
			end
		end

		self.node_list.baguabi.transform.localScale = u3dpool.vec3(1, 1, 1)
		UITween.DONumberTo(self.node_list["baguabi_text"].text, self.bagua_num, bagua_money, 1.5, update_fun, complete_fun)
		self.node_list.baguabi.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
	end
end

function MoneyBar:BeastExpNumChangeEvent()
    if not self.node_list["beast_exp"] or IsNil(self.node_list["beast_exp"].gameObject) then
		return
	end

	local beast_exp_num = ControlBeastsWGData.Instance:GetBeastBaseInfoExp()
	if beast_exp_num ~= self.beast_exp_num then
		local complete_fun = function()
			self.beast_exp_num = beast_exp_num
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["beast_exp_text"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["beast_exp_text"].text.text = (value) .. postfix_name
			end
		end

		self.node_list.beast_exp.transform:DOKill()
		self.node_list.beast_exp.transform.localScale = u3dpool.vec3(1, 1, 1)
		UITween.DONumberTo(self.node_list["beast_exp_text"].text, self.beast_exp_num, beast_exp_num, 1.5, update_fun, complete_fun)
		self.node_list.beast_exp.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
	end
end

function MoneyBar:ShowBeastExpEffect()
	if self.node_list.beast_exp_effect then
        local bundle_name, asset_name = ResPath.GetA2Effect("UI_yushou_jingyanshanshuo_lhl")
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.beast_exp_effect.transform,
                            4, Vector3(0, 0, 0), Quaternion.Euler(0, 0, 0))
    end
end

-- 代金卷变动
function MoneyBar:BeastVolumeChangeEvent()
    if not self.node_list["volume"] or IsNil(self.node_list["volume"].gameObject) then
		return
	end

	local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume")
	if recharge_volume_num ~= self.recharge_volume_num then
		local complete_fun = function()
			self.recharge_volume_num = recharge_volume_num
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["volume_txt"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["volume_txt"].text.text = (value) .. postfix_name
			end
		end

		self.node_list.volume.transform:DOKill()
		self.node_list.volume.transform.localScale = u3dpool.vec3(1, 1, 1)
		UITween.DONumberTo(self.node_list["volume_txt"].text, self.recharge_volume_num, recharge_volume_num, 1.5, update_fun, complete_fun)
		self.node_list.volume.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
	end
end

-- 藏金商铺积分变动
function MoneyBar:CangJinScoreChangeEvent()
    if not self.node_list["cangjin_score"] or IsNil(self.node_list["cangjin_score"].gameObject) then
		return
	end

	local cangjin_score_num = YanYuGeWGData.Instance:GetCurScore()
	if cangjin_score_num ~= self.cangjin_score_num then
		local complete_fun = function()
			self.cangjin_score_num = cangjin_score_num
		end

		local update_fun = function(num)
			local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
			if postfix_name == "" then
				self.node_list["cangjin_score_txt"].text.text = (string.format("%.0f", value)) .. postfix_name
			else
				self.node_list["cangjin_score_txt"].text.text = (value) .. postfix_name
			end
		end

		self.node_list.cangjin_score.transform:DOKill()
		self.node_list.cangjin_score.transform.localScale = u3dpool.vec3(1, 1, 1)
		UITween.DONumberTo(self.node_list["cangjin_score_txt"].text, self.cangjin_score_num, cangjin_score_num, 1.5, update_fun, complete_fun)
		self.node_list.cangjin_score.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
	end
end

-- 开服狂欢集卡万能碎片变动
function MoneyBar:CollectCardChipShow(collect_card_chip_count)
	self.node_list.collect_card_chip:CustomSetActive(true)
	self.node_list.collect_card_chip_text.text.text = collect_card_chip_count
end