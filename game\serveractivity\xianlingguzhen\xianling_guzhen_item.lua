

--------------------------------------------------------------------------------------------------------
XianLingRewardItemCell = XianLingRewardItemCell or BaseClass(BaseRender)

function XianLingRewardItemCell:__init()
	self.reward_item_cell = ItemCell.New(self.node_list["icon_bg"])
	self.is_set_idle_anim = false
end

function XianLingRewardItemCell:__delete()
	self.is_set_idle_anim = nil

	if self.reward_item_cell then
		self.reward_item_cell:DeleteMe()
		self.reward_item_cell = nil
	end
end

function XianLingRewardItemCell:SetIndex(index)
	self.cur_cell_index = index
end

function XianLingRewardItemCell:OnFlush()
	if self.data and self.data.reward_item and self.data.reward_item[0] then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item[0].item_id)
		if IsEmptyTable(item_cfg) then
			return
		end

		local color_res_index = 5
		if check_item_bg_res_list[item_cfg.color] then
			color_res_index = item_cfg.color
		end
		self.node_list["icon_bg"].image:LoadSprite(ResPath.GetXianLingGuZhenImg("lxmt_item_bg_" .. color_res_index))
		self.node_list["icon_bg"].image:SetNativeSize()

		self.reward_item_cell:SetShowCualityBg(false)
		self.reward_item_cell:SetCellBgEnabled(false)
		self.reward_item_cell:NeedDefaultEff(false)
		self.reward_item_cell:SetIsUseRoundQualityBg(true)
		self.reward_item_cell:SetData(self.data.reward_item[0])

		self:SetItemEffectShow(color_res_index + 5)
	end
end

function XianLingRewardItemCell:SetSelectHL(is_show)
	if self.node_list["hl_bg"] then
		local res_name = is_show and "lx_gai_xuanzhong" or "lx_gai_andi"
		self.node_list["hl_bg"].image:LoadSprite(ResPath.GetXianLingGuZhenImg(res_name))
		self.node_list["hl_bg"].image:SetNativeSize()
	end
end

function XianLingRewardItemCell:SetIconBgActive(is_show)
	if self.node_list["icon_bg"] then
		self.node_list["icon_bg"].image.enabled = is_show
	end
end

function XianLingRewardItemCell:SetItemEffectShow(color)
	if self.node_list["item_eff_pos"] then
		local eff_bundle, eff_asset = ResPath.GetWuPinKuangEffectUi(BaseCell_Ui_Circle_Effect[color])
		self.node_list["item_eff_pos"]:ChangeAsset(eff_bundle, eff_asset)
		self.node_list["item_eff_pos"]:SetActive(true)
	end
end

function XianLingRewardItemCell:HideEffectRoot()
	if self.node_list["item_eff_pos"] then
		self.node_list["item_eff_pos"]:SetActive(false)
	end
end

--------------------------------------------------------------------------------------------------------
XianLingListCell = XianLingListCell or BaseClass(BaseRender)

function XianLingListCell:__init()
	self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGet, self))
	self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo, self))
	self.show_cell = ItemCell.New(self.node_list["item_cell"])
end

function XianLingListCell:__delete()
	if self.show_cell then
		self.show_cell:DeleteMe()
		self.show_cell = nil
	end
end

function XianLingListCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local cur_task_info = XianLingGuZhenWGData.Instance:GetXianLingTaskStatus(self.data.task_id)
		if not cur_task_info or IsEmptyTable(cur_task_info) then return end
		local start_index, end_index = string.find(self.data.task_dec, "%%s")
		if start_index and end_index then
			--local color = self.data.param1 > cur_task_info.task_progress and COLOR3B.RED or COLOR3B.L_GREEN
			local cacular_num = cur_task_info.task_progress > self.data.param1 and self.data.param1 or cur_task_info.task_progress   
			--self.node_list["desc_text"].text.text = string.format(self.data.task_dec,ToColorStr(cacular_num,color))
			self.node_list["desc_text"].text.text = string.format(self.data.task_dec, cacular_num)
		else
			self.node_list["desc_text"].text.text = self.data.task_dec
		end

		self.task_status = cur_task_info.reward_flag
		self.node_list["redpoint"]:SetActive(false)
		if self.task_status == 0 then
			self.node_list["go_btn"]:SetActive(true)
			self.node_list["get_btn"]:SetActive(false)
			--self.node_list["get_btn_text"].text.text = Language.XianLingGuZhen.BtnText

			self.node_list["is_over_green"]:SetActive(false)
		elseif self.task_status == 1 then
			self.node_list["get_btn"]:SetActive(true)
			self.node_list["go_btn"]:SetActive(false)
			--self.node_list["get_btn_text"].text.text = Language.XianLingGuZhen.BtnText4

			self.node_list["is_over_green"]:SetActive(false)
			self.node_list["redpoint"]:SetActive(true)
		else
			self.node_list["get_btn"]:SetActive(false)
			self.node_list["go_btn"]:SetActive(false)
			self.node_list["is_over_green"]:SetActive(true)
		end

		self.show_cell:SetData(self.data.reward_item[0])
	else
		self.task_status = 0
	end
end

function XianLingListCell:OnClickGet()
	if not IsEmptyTable(self.data) then
		if self.task_status == 1 then
			XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_GET_TASK_REWARD,self.data.task_id)
		end
	end
end

function XianLingListCell:OnClickGo()
	if not IsEmptyTable(self.data) then
		if self.task_status == 0 and self.data.open_view ~= "" then
			local t = Split(self.data.open_view,"#")
			if t[1] == "PierreDirectPurchaseView" then
				if PierreDirectPurchaseWGData.Instance:CheckActIsOpen() then
					if t[2] then
						local gift_is_open,reason = PierreDirectPurchaseWGData.Instance:GetBugTypeIsOpen(tonumber(t[2]))
						if gift_is_open then
							ViewManager.Instance:Open(t[1],nil,"jump_index",{[1] = tonumber(t[2])})
						else
							TipWGCtrl.Instance:ShowSystemMsg(reason)
						end
					end
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.XianLingGuZhen.CanNotJump[1])
				end
			else
				FunOpen.Instance:OpenViewNameByCfg(self.data.open_view)
			end
		end
	end
end

BigRewardListCell = BigRewardListCell or BaseClass(BaseRender)

function BigRewardListCell:__init()
	self.item_cell = ItemCell.New(self.node_list["cell"])
end

function BigRewardListCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

end

function BigRewardListCell:OnFlush()
	if not IsEmptyTable(self.data) then
		if self.data.reward_item and self.data.reward_item[0] then
			self.item_cell:SetData(self.data.reward_item[0])
		end
	end
end


function BigRewardListCell:OnClickAdd()
	if self.data and self.data.seq then
		if XianLingGuZhenWGData.Instance:GetBigRewardHadBeenAdd(self.data.seq) then
		else
			XianLingGuZhenWGData.Instance:SetBigRewardBeenAdd(self.data.seq)
			self:Flush()
			XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_ADD_REWARD_COUNT,self.data.seq)
		end
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianLingGuZhen.HaveApply)
	end
end


XianLingRecordCell = XianLingRecordCell or BaseClass(BaseRender)

function XianLingRecordCell:__init()

end

function XianLingRecordCell:__delete()

end

function XianLingRecordCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local item_data = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		local match_num_str = (self.data.draw_number and self.data.draw_number > 0) and string.format(Language.XianLingGuZhen.LastRewardNumDesc, self.data.draw_number) or ''
		self.node_list["name_text"].text.text = string.format(Language.XianLingGuZhen.LastRewardDesc, self.data.name, match_num_str, item_data.name)
	else
		self.node_list["name_text"].text.text = ""
	end
end



XianLingItemCell = XianLingItemCell or BaseClass(BaseRender)

function XianLingItemCell:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.item_cell:SetIsUseRoundQualityBg(true)
	self.item_cell:SetCellBgEnabled(false)
end

function XianLingItemCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function XianLingItemCell:OnFlush()
	if not IsEmptyTable(self.data) then
		if self.data.reward_item and self.data.reward_item[0] then
			self.item_cell:SetData(self.data.reward_item[0])
			self.item_cell:SetBindIconVisible(false)
		end
	end
end

