------------------------------------------------------------
--背景属性View
------------------------------------------------------------
local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 11
PhantomDreamlandFBCardTipsView = PhantomDreamlandFBCardTipsView or BaseClass(SafeBaseView)

function PhantomDreamlandFBCardTipsView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.view_name = "PhantomDreamlandFBCardTipsView"
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_experience_fb_card_tip")
end

function PhantomDreamlandFBCardTipsView:LoadCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}

	for i = 1, ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end

	self:Flush()
end

function PhantomDreamlandFBCardTipsView:ReleaseCallBack()
	self.attr_list = nil
	self.attr_name_list = nil
	self.attr_value_list = nil
end

function PhantomDreamlandFBCardTipsView:OnFlush()
	self.node_list.attr_title_name.text.text = Language.Role.ExpWestAttrName
	local card_list = PhantomDreamlandWGData.Instance:GetAllCardInfo()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()

	if (not scene_info) or (not card_list) then
		return
	end

	local level = scene_info.level or 0

	local lv_cfg = PhantomDreamlandWGData.Instance:GetLevelCfg()
	if not lv_cfg then
		return
	end

	local card_pool = lv_cfg.card_pool or 0
	local list = {}
	for i, card_info in ipairs(card_list) do
		local cfg = PhantomDreamlandWGData.Instance:GetCardCfgByIndexSeq(card_pool, card_info.choose_seq)
		if cfg and cfg.type == 1 then
			table.insert(list, cfg)
		end
	end

	local length = #list
	local index = 1
	if not IsEmptyTable(list) then
		for i = 1, ATTR_COUNT do
			local attr_data = list[i]
			if attr_data and self.attr_list[i] then
				---设置属性
				self.attr_name_list[i].text.text = attr_data.name
				self.attr_value_list[i].text.text = attr_data.desc
			end

			if i <= length then
				index = index + 1
			end
		end
	end

	if index < MAX_DISPLAY_NUM then
		self.node_list.attr_scroll.scroll_rect.enabled = false
	else
		self.node_list.attr_scroll.scroll_rect.enabled = true
	end

	if not IsEmptyTable(card_list) then
		for i = 1, ATTR_COUNT do
			self.attr_list[i]:SetActive(i <= length)
		end
	end

	self.node_list.img_no_record:CustomSetActive(length <= 0)
end
