require("game/task/task_wg_data")
require("game/task/npc_dialog")
require("game/task/task_follow")
require("game/task/task_gain_exp_tip_view")
require("game/task/task_gain_capability_tip_view")
require("game/task/task_view")
require("game/task/task_consume_view")
require("game/task/task_weather_eff")
require("game/task/task_daily_reward_tip")
require("game/task/task_guild_reward_tip")
require("game/task/task_daily_batch_tips")
require("game/task/task_dice")
require("game/task/task_shangjin_view")
require("game/task/task_shangjin_stroy_view")


Auto_Go_Time = 5
-- 1 个人游历 2 双人游历 3 接受邀请 4 拒绝游历邀请
YouLiType = {
    Type1 = 1,
    Type2 = 2,
    Type3 = 3,
    Type4 = 4,
}
--------------------------------------------------------------
--任务相关
--------------------------------------------------------------
TaskWGCtrl = TaskWGCtrl or BaseClass(BaseWGCtrl)
function TaskWGCtrl:__init()
    if TaskWGCtrl.Instance then
        ErrorLog("[TaskWGCtrl] Attemp to create a singleton twice !")
    end
    TaskWGCtrl.Instance = self

    self.task_data = TaskWGData.New()
    self.npc_dialog = NpcDialog.New(GuideModuleName.TaskDialog)
    self.task_gain_exp_tip_view = TaskGainExpTipView.New()
    self.task_gain_capability_tip_view = TaskGainCapabilityTipView.New()
    self.task_follow = TaskFollow.New()
    self.task_consume_view = TaskConsumeView.New(GuideModuleName.TaskConsumeView)
    self.task_view = TaskView.New(GuideModuleName.Task)
    self.task_weather_eff = TaskWeatherEff.New()
    self.task_daily_reward_tips = TaskDailyRewardTip.New(GuideModuleName.TaskDailyRewardTip)
    self.task_guild_reward_tips = TaskGuildRewardTip.New(GuideModuleName.TaskGuildRewardTip)
    self.task_daily_batch_tips = TaskDailyBatchTips.New(GuideModuleName.TaskDailyBatchTips)
    self.task_dice = TaskDice.New(GuideModuleName.TaskDice)
    self.task_shangjin_view = TaskShangJinView.New(GuideModuleName.TaskShangJinView)
    self.task_shangjin_stroy_view = TaskShangJinStoryView.New()

    self.operate_follow_task_time = 0 --上次操作跟踪任务的时间

    self:RegisterAllProtocols()

    self.task_data_change = BindTool.Bind1(self.TaskDataChange, self)
    self.task_data_list_change = BindTool.Bind(self.OnTaskDataList, self)
    TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_list_change, true)
    TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_change, false)

    self:BindGlobalEvent(SceneEventType.SCENE_CHANGE_COMPLETE, self.task_data_change)
    self:BindGlobalEvent(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind1(self.CloseLoadingView, self))
    self:BindGlobalEvent(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnObjEnterRole, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_POS_CHANGE, BindTool.Bind(self.MainRoleAreaCheck, self))
    self:BindGlobalEvent(OtherEventType.GUIDE_STEP_CHANGE, BindTool.Bind(self.GuideStepChange, self))

    self.jumpend_callback = BindTool.Bind(self.JumpEndCallBack, self)
    self.task_updateing = false-- 任务发送/返回标记

    self.youli_info = nil
end

function TaskWGCtrl:__delete()
    TaskWGCtrl.Instance = nil

    TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_list_change)
    TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_change)

    self.task_follow:DeleteMe()
    self.task_follow = nil

    self.task_data:DeleteMe()
    self.task_data = nil

    self.npc_dialog:DeleteMe()
    self.npc_dialog = nil

    self.task_gain_exp_tip_view:DeleteMe()
    self.task_gain_exp_tip_view = nil

    if self.task_gain_capability_tip_view then
        self.task_gain_capability_tip_view:DeleteMe()
        self.task_gain_capability_tip_view = nil
    end

    self.task_view:DeleteMe()
    self.task_view = nil

    self.task_shangjin_view:DeleteMe()
    self.task_shangjin_view = nil

    self.task_consume_view:DeleteMe()
    self.task_consume_view = nil

    self.task_weather_eff:DeleteMe()
    self.task_weather_eff = nil

    self.task_daily_reward_tips:DeleteMe()
    self.task_daily_reward_tips = nil

    self.task_guild_reward_tips:DeleteMe()
    self.task_guild_reward_tips = nil

    self.task_daily_batch_tips:DeleteMe()
    self.task_daily_batch_tips = nil

    self.task_dice:DeleteMe()
    self.task_dice = nil

    self.task_shangjin_stroy_view:DeleteMe()
    self.task_shangjin_stroy_view = nil

    if self.alert_commit then
        self.alert_commit:DeleteMe()
        self.alert_commit = nil
    end

    self.cur_zhixing_task_cfg = nil
    self.youli_info = nil

    if self.cancle_guide_delay  then
        GlobalTimerQuest:CancelQuest(self.cancle_guide_delay)
        self.cancle_guide_delay  = nil
    end

    self:CancelUpdateAllNpcStatus()
end

function TaskWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCTaskListAck, "OnTaskListAck")
    self:RegisterProtocol(SCTaskInfo, "OnTaskInfo")
    self:RegisterProtocol(SCTaskRecorderList, "OnTaskRecorderList")
    self:RegisterProtocol(SCTaskRecorderInfo, "OnTaskRecorderInfo")
    self:RegisterProtocol(SCAccpetableTaskList, "OnAccpetableTaskList")
    self:RegisterProtocol(SCGuildTaskInfo, "BackSCGuildTaskInfo")
    self:RegisterProtocol(SCBountyTaskInfo, "BackSCBountyTaskInfo")
    self:RegisterProtocol(SCYouLiRet, "BackSCYouLiRet")
    self:RegisterProtocol(SCYouLiASS, "BackYouLiASS")
    self:RegisterProtocol(SCKillMonsterDropNotice, "BackSCKillMonsterDropNotice")
    self:RegisterProtocol(SCCanFlyByShoeAck, "OnSCCanFlyByShoeAck")
    self:RegisterProtocol(SCTaskCallInfo, "OnSCTaskCallInfo")
    self:RegisterProtocol(SCBountyListInfo, "OnSCBountyListInfo")
    

    self:RegisterProtocol(CSTaskListReq)
    self:RegisterProtocol(CSTaskGiveup)
    self:RegisterProtocol(CSFlyByShoe)
    self:RegisterProtocol(CSTaskAccept)
    self:RegisterProtocol(CSTaskCommit)
    self:RegisterProtocol(CSTaskTalkToNpc)
    self:RegisterProtocol(CSBountyCommitTask)
    self:RegisterProtocol(CSTaskItemCommit)
    self:RegisterProtocol(CSBountyListOperate)
    self:RegisterProtocol(CSTaskWalkToPos)
end

local old_dice_task
function TaskWGCtrl:OpenTaskDice(task_cfg)
    if self.task_dice:IsOpen() then
        return
    end

    if old_dice_task and task_cfg and old_dice_task == task_cfg.task_id then
        TaskGuide.Instance:CanAutoAllTask(true)
        TaskWGCtrl.Instance:SendTaskCommit(task_cfg.task_id)
    else
        old_dice_task = task_cfg.task_id
        self.task_dice:ShowView(task_cfg)
    end
end

function TaskWGCtrl:ClearOldDiceTask()
    old_dice_task = nil
end

function TaskWGCtrl:PlayEffect()
     if ViewManager.Instance:IsOpen(GuideModuleName.Task) then
        self.task_view:PlayGetEffect()
    end
end

function TaskWGCtrl:PlayEffect1()
    --  if ViewManager.Instance:IsOpen(GuideModuleName.TaskShangJinView) then
    --     self.task_shangjin_view:PlayGetEffect()
    -- end
end

function TaskWGCtrl:BackSCGuildTaskInfo(protocol)
    -- print_error("BackSCGuildTaskInfo:::", protocol)
    local old_guild_info = self.task_data:GetGuildInfo()
    self.task_data:BackGuildInfo(protocol)
    -- self:ShowRewardTip(old_guild_info, protocol.task_guild_info)
    self:CheckQuickAllTask(old_guild_info, protocol.task_guild_info)
    GlobalEventSystem:Fire(OtherEventType.TASK_GUILD_INFO_CHANGE)
    GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE)
end

function TaskWGCtrl:CheckQuickAllTask(old_guild_info, task_guild_info)
    if IsEmptyTable(old_guild_info) then return end
    if 0 == task_guild_info.complete_task_count then return end
    if task_guild_info.complete_task_count < TaskWGData.Instance:GetTaskGuildWeekMaxCount() then return end
    if task_guild_info.complete_task_count - old_guild_info.complete_task_count > 1 then
        self.task_data:QuickAllTaskCompleted(GameEnum.TASK_TYPE_MENG)
    end
end

function TaskWGCtrl:ShowRewardTip(old_guild_info, task_guild_info)
    if IsEmptyTable(old_guild_info) then return end
    local complete_task_count = old_guild_info.complete_task_count
    if 0 == task_guild_info.complete_task_count then return end
    local num = math.floor(task_guild_info.complete_task_count / 10) - math.floor(complete_task_count / 10)
    if 0 >= num then return end
    self.task_data:ShowTaskGuildNum(num)

    if Scene.Instance:GetSceneType() == SceneType.Common then
        TaskWGData.Instance:ShowTaskTypeTips(false)
        ViewManager.Instance:Open(GuideModuleName.TaskGuildRewardTip, nil, nil, {num})
    else
        TaskWGData.Instance:ShowTaskTypeTips(GameEnum.TASK_TYPE_MENG)
    end
end

function TaskWGCtrl:BackSCBountyTaskInfo(protocol)
    -- print_error("BackSCBountyTaskInfo:::", protocol)
    self.task_data:BackSCBountyInfo(protocol)
    self:TaskUpdateing(false)
end

--请求已接任务列表放回
function TaskWGCtrl:OnTaskListAck(protocol)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("OnTaskListAck", protocol)
    end

    -- print_error("任务列表",protocol)
    if not RoleWGCtrl.Instance:GetIsHasRoleData() then
        RoleWGCtrl.Instance:RegisterDelayProtocol(BindTool.Bind2(self.OnTaskListAck, self, protocol))
        return
    end
    TaskWGData.Instance:SetTaskAcceptedInfoList(protocol.task_accepted_list)
    TaskGuide.Instance:InitTask(true)
    self:SetHideNpcIdList(0, true)
    self.task_weather_eff:CheckCanShowEff()
end

function TaskWGCtrl:SetHideNpcIdList(task_id, is_init, reason)
    -- body
    Scene.Instance:SetHideGatherList()
end

function TaskWGCtrl:BackYouLiASS(protocol)
    -- print_error("BackYouLiASS:::", protocol)
    local function ok_func()
        self:SendYouLi(YouLiType.Type3, protocol.role_id)
    end

    local function cancel_fun()
        self:SendYouLi(YouLiType.Type4, protocol.role_id)
    end

    local str = string.format(Language.Task.youli_des01, protocol.name)
    TipWGCtrl.Instance:OpenAlertTips(str, ok_func, cancel_fun, nil, cancel_fun, 10)
end

function TaskWGCtrl:BackSCYouLiRet(protocol)
    -- print_error("BackSCYouLiRet:::", protocol)
    self.youli_info = protocol
    -- if self.youli_info.status == 2 then
    self.wait_play_cg = true
        -- GlobalTimerQuest:AddDelayTimer(function ()
        --     self.wait_play_cg = false
        -- end,10)
    -- end
    self:PlayYouliCg()
end

function TaskWGCtrl:CancleWait()
    self.wait_play_cg = false
end

function TaskWGCtrl:OnObjEnterRole(obj_id)
    -- body
    GlobalTimerQuest:AddDelayTimer(function()
        self:PlayYouliCg()
     end, 1)
end

function TaskWGCtrl:PlayYouliCg()
    -- body
    if not self.youli_info then return end
    if Scene.Instance:IsSceneLoading() then return end
    if Scene.Instance:IsChangeSceneIng() then return end
    -- if not self.wait_play_cg then return end

    local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
    local other_role_id = mainrole_vo.role_id ~= self.youli_info.inviter and self.youli_info.inviter or self.youli_info.binviter
    if 1 ~= self.youli_info.status and not Scene.Instance:GetRoleByRoleId(other_role_id) then return end

    local youli_other = TaskWGData.Instance.youli_other_cfg
    local pos = nil
    if mainrole_vo.role_id == self.youli_info.inviter then
        pos = youli_other.pos1
    else
        pos = youli_other.pos2
    end
    pos = string.split(pos, ",")
    local list = string.split(youli_other.cg, "|")
    if not list[1] or not list[2] then return false end
    local binviter = self.youli_info.binviter
    self.youli_info = nil

    CgManager.Instance:Play(BaseCg.New(list[1], list[2], {role_id = binviter, obj_type = SceneObjType.Role}),
        function()
            local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
            Scene.SendSyncJump(Scene.Instance:GetSceneId(), pos[1], pos[2], scene_key)
            Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
            if TaskGuide.Instance:NoviceCheckTask() and Scene.Instance:GetSceneType() == SceneType.Common then
                TaskGuide.Instance:CanAutoAllTask(true)
            end
        end)
    return true
end

function TaskWGCtrl:BackSCKillMonsterDropNotice(protocol)
    -- print_error("BackSCKillMonsterDropNotice:::", protocol)
    -- TaskWGData.Instance:KillMonsterTaskId(protocol.task_id)
    local task_cfg = TaskWGData.Instance:GetTaskConfig(protocol.task_id)
    local item_id = task_cfg and task_cfg.c_param4 or 0
    TaskWGData.Instance:SetKillMonsterTaskId(item_id, protocol.task_id)
    Scene.Instance:SimulationFall(100, 0, item_id, FallItemHideType.ToBag)
end

--怪物击杀掉落任务回复
function TaskWGCtrl:SendMonsterDropChange(task_id)
    -- print_error("SendMonsterDropChange:::", task_id)
    local cmd = ProtocolPool.Instance:GetProtocol(CSKillMonsterDropChange)
    cmd.task_id = task_id
    cmd:EncodeAndSend()
end

function TaskWGCtrl:CheckKillMonsterTask(item_id)
    -- body
    -- local task_id = TaskWGData.Instance:KillMonsterTaskId()
    local task_id = TaskWGData.Instance:GetKillMonsterTaskId(item_id)
    if task_id == nil then
        return
    end

    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
    if not task_cfg or task_cfg.c_param4 ~= item_id then return end

    self:SendMonsterDropChange(task_id)
end

--单条已接任务信息
function TaskWGCtrl:OnTaskInfo(protocol)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("OnTaskInfo", protocol)
    end

    if NpcDialog and NpcDialog.LAST_COMMIT_TASK == protocol.task_id and protocol.reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
        NpcDialog.LAST_COMMIT_TASK = 0
    end

    ----reason 0.信息改变 1.接取 2.移除
    -- print_error("OnTaskInfo:::单条已接任务信息",protocol)
    TaskWGData.Instance:ChangeTaskAccepted(protocol)
    self:UpdateToEffect(protocol)
    self:TaskUpdateing(false)
    Scene.Instance:CheckTaskCamera(protocol)

    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local task_cfg = TaskWGData.Instance:GetTaskConfig(protocol.task_id)
    if task_cfg == nil then
        -- print_error("TaskWGCtrl:OnTaskInfo .. not task cfg:" .. protocol.task_id)
        return
    end
    self:SetFirstTaskByUpdate(protocol)

    GlobalEventSystem:Fire(OtherEventType.TASK_INFO_CHANGE, task_cfg.task_id)
    local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
    if  task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
        -- if task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
            -- if task_cfg.accept_npc ~= "" then
                -- 屏蔽接受任务特效
                -- FightText.Instance:TaskStateChangeText("UI_effects_jsrw")
            -- end
        -- end
        if protocol.reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
            if task_cfg.show_chaper_id > 0 then
                TipWGCtrl.Instance:ShowChapterTipView(task_cfg)
            end
        end
        self:SetHideNpcIdList(protocol.task_id, false, protocol.reason)
    end
    if task_cfg.task_type == GameEnum.TASK_TYPE_MENG and task_status == GameEnum.TASK_STATUS_COMMIT then
        local other_cfg = ConfigManager.Instance:GetAutoConfig("tasklist_auto").other[1]
        local auto_task_viplevel = other_cfg.auto_task_viplevel or 0
        TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_MENG)
    end

    -- print_error("OnTaskInfo::: ", task_cfg.condition, task_status, GuajiCache.guaji_type)
    if (task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_11 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_14 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_23)
         and task_status == GameEnum.TASK_STATUS_COMMIT
         -- and TaskGuide.Instance:CanAutoAllTask()     -- 就算不在自动做任务也让玩家交任务
    then
	    if GuajiCache.guaji_type ~= GuajiType.None then
            if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
                -- 判断下一个主线任务是否卡等级，如果没有卡等级才停止挂机,继续做任务
                if not TaskWGData.Instance:GetNextZhuTaskIsBlockByLevel(task_cfg) then
                    GuajiWGCtrl.Instance:StopGuaji()
                    TaskGuide.Instance:CanAutoAllTask(true)
                else
                    self:OperateOnCommitStatus(task_cfg, nil)
                end
            elseif task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHI and task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHUAN then
                local scene_cfg = TaskWGData.Instance:GetSceneConfig(protocol.task_id)
                if not scene_cfg or Scene.Instance:GetSceneType() == scene_cfg.scene_type then -- 避免在某些场景接到新任务，但任务在其他场景无法自动做任务，导致停止挂机
                    GuajiWGCtrl.Instance:StopGuaji()
                    TaskGuide.Instance:CanAutoAllTask(true)
                end
            end
	    end
	end
    local reason,task_id = protocol.reason,protocol.task_id
    self:CheckTaskStoryTextShow(reason,task_id)

    if reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
        local other_cfg = TaskWGData.Instance:GetOtherInfo()
        if other_cfg and other_cfg.side_taskID and other_cfg.side_taskID == task_id then
            TaskGuide.Instance:SideTOStopTask(true)
        end

        if task_cfg.accept_npc.id then
            AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ReceiveMission))
        end

        if task_cfg.accept_bubble and task_cfg.accept_bubble ~= "" then
            TipsSystemManager.Instance:ShowSystemTips(task_cfg.accept_bubble)
        end

        if 1 == protocol.is_complete and task_cfg.complete_bubble and task_cfg.complete_bubble ~= "" then
            TipsSystemManager.Instance:ShowSystemTips(task_cfg.complete_bubble)
        end
        self:UpdateAllNpcBubbleStatus(protocol.task_id, TaskStatusType.Type1)
    elseif reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
        if task_cfg.complete_bubble and 1 == protocol.is_complete and task_cfg.complete_bubble ~= "" then
            TipsSystemManager.Instance:ShowSystemTips(task_cfg.complete_bubble)
        end

        if 1 == protocol.is_complete then
            self:UpdateAllNpcBubbleStatus(protocol.task_id, TaskStatusType.Type2)
        end
    elseif reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
        --策划说除了主线任务，其他任务完成都要加特效 2019/12/6
        if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
            local view = ViewManager.Instance:GetView(GuideModuleName.guild_task)
            if view and view:IsOpen() then
                local root_node = view:GetRootNode()
                if not IsNil(root_node) then
                    -- 屏蔽完成任务特效
                    -- FightText.Instance:TaskStateChangeText("UI_effects_wcrw_1", root_node.transform)
                end
            end
        elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then
            local has_effect =  VipWGData.Instance:IsVip()
            if role_vip >= COMMON_CONSTS.VIP_CHANGE and has_effect then    
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_vip_double, is_success = true,pos = Vector2(0, 0)})
            else
                TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_renwu, is_success = true, pos = Vector2(0, 187)})
            end
        else
            -- 屏蔽完成任务特效
            -- FightText.Instance:TaskStateChangeText("UI_effects_wcrw_1")
        end

        if task_cfg.commit_npc.id then
            AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.SystemLevelUp))
        end

        if GameEnum.TASK_TYPE_MENG == task_cfg.task_type then
            -- local task_guild_info = self.task_data:GetGuildInfo()
            -- if 9 == task_guild_info.complete_task_count % 10 then
            --     if Scene.Instance:GetSceneType() == SceneType.Common then
            --         self.task_data:ShowTaskTypeTips(false)
            --         ViewManager.Instance:Open(GuideModuleName.TaskGuildRewardTip, nil, nil, {1})
            --     else
            --         self.task_data:ShowTaskTypeTips(task_cfg.task_type)
            --     end
            -- end
        elseif GameEnum.TASK_TYPE_RI == task_cfg.task_type then
            -- local commint_times = DailyWGData.Instance:GetTaskCommintTimes()
            -- if 0 ~= commint_times and 0 == commint_times % 10 then
            -- -- if DailyWGData.Instance:GetTaskCommintTimes() >= TUMO_COMMIT_TIMES then
            --     if Scene.Instance:GetSceneType() == SceneType.Common then
            --         self.task_data:ShowTaskTypeTips(false)
            --         ViewManager.Instance:Open(GuideModuleName.TaskDailyRewardTip)
            --     else
            --         self.task_data:ShowTaskTypeTips(task_cfg.task_type)
            --     end
            -- end
        end

        if task_cfg.Submit_bubble and task_cfg.Submit_bubble ~= "" then
            TipsSystemManager.Instance:ShowSystemTips(task_cfg.Submit_bubble)
        end

        self:UpdateAllNpcBubbleStatus(protocol.task_id, TaskStatusType.Type3)
    end

    if protocol.is_silent == 1 then--服务端定，此种情况不飘特效，如用在换天时
        return
    end

    -- 护送
    local task_id = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").other[1].task_id
    if protocol.reason == 1 and protocol.task_id == task_id then
        YunbiaoWGCtrl.Instance:OnHusongAccept()
    end

    -- if task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
    --     TransFerWGCtrl.Instance:ChangeTaskState()
    -- end

    -- local effect_id = 0

    -- if protocol.reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
    -- if RoleWGData.Instance.role_vo.level > 1 then
    -- effect_id = 3021
    -- end
    -- elseif protocol.reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
    -- effect_id = 3022
    -- end
    -- if effect_id > 0 then
    -- local screen_w = RenderUnit:GetWidth() + IPHONE_X_SIZE * 2
    -- local screen_h = RenderUnit:GetHeight()

    -- local anim_path, anim_name = ResPath.GetEffectAnimPath(effect_id)

    -- local sprite = nil
    -- local function callback_func()
    -- sprite:removeFromParent()
    -- end

    -- sprite = RenderUnit.CreateAnimSprite(anim_path, anim_name, 0.1, 1, false, callback_func)
    -- if nil ~= sprite then
    -- sprite:setPosition(screen_w / 2, screen_h - 200)
    -- HandleRenderUnit:AddUi(sprite, COMMON_CONSTS.ZORDER_SYSTEM_EFFECT, COMMON_CONSTS.ZORDER_SYSTEM_EFFECT)
    -- end
    -- end
end

function TaskWGCtrl:ShowTaskTypeTips()
    -- body
    if Scene.Instance:GetSceneType() ~= SceneType.Common then return end
    local show_task_type = self.task_data:ShowTaskTypeTips()
    if not show_task_type then return end
    self.task_data:ShowTaskTypeTips(false)
    if GameEnum.TASK_TYPE_MENG == show_task_type then
        ViewManager.Instance:Open(GuideModuleName.TaskGuildRewardTip, nil, nil, {self.task_data:ShowTaskGuildNum()})
    elseif GameEnum.TASK_TYPE_RI == show_task_type then
        ViewManager.Instance:Open(GuideModuleName.TaskDailyRewardTip)
    end
end

-- 设置置顶任务
function TaskWGCtrl:SetFirstTaskByUpdate(protocol)
    -- body
    local task_cfg = TaskWGData.Instance:GetTaskConfig(protocol.task_id)
    if not task_cfg then return end

    local curr_task_is_other_first = TaskWGData.Instance:CurrTaskIsOtherFirst()
    if curr_task_is_other_first then return end

    local curr_task_id = TaskWGData.Instance:CurrTaskId()
    local curr_task_cfg = TaskWGData.Instance:GetTaskConfig(curr_task_id)
    if curr_task_cfg and 1 == curr_task_cfg.is_first then
        -- if protocol.reason ~= GameEnum.DATALIST_CHANGE_REASON_ADD then
        if protocol.reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
            TaskWGData.Instance:CurrTaskId(task_cfg.task_id)
        end
    elseif 1 == task_cfg.is_first and protocol.reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
        TaskWGData.Instance:CurrTaskId(task_cfg.task_id)
    else
        if protocol.reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE then
            TaskWGData.Instance:CurrTaskId(task_cfg.task_id)
        elseif protocol.reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
            if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU or
                task_cfg.task_type == GameEnum.TASK_TYPE_RI or
                task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
                TaskWGData.Instance:CurrTaskId(task_cfg.task_id)
            end
        end
    end
end

function TaskWGCtrl:SetFirstTaskByAcc(protocol)
    -- body
    local id_list = protocol.task_can_accept_id_list
    if IsEmptyTable(id_list) then return end

    local flag, task_cfg = false, nil
    for k,v in pairs(id_list) do
        task_cfg = TaskWGData.Instance:GetTaskConfig(k)
        flag = TaskWGData.Instance:IsInOtherFirst(k)
        if flag then
            TaskWGData.Instance:CurrTaskId(k)
            break
        elseif task_cfg and 1 == task_cfg.is_first then
            TaskWGData.Instance:CurrTaskId(k)
        elseif task_cfg and task_cfg.task_type == TaskGuide.Instance:CurrTaskType() then
            -- 自动做的任务一直置顶
            TaskWGData.Instance:CurrTaskId(k)
        end
    end
end

-- 采集任务，特效
function TaskWGCtrl:UpdateToEffect(protocol)
    -- body
    if 40 ~= protocol.task_id or 1 ~= protocol.is_complete or protocol.reason ~= GameEnum.DATALIST_CHANGE_REASON_UPDATE then return end
    local task_cfg = TaskWGData.Instance:GetTaskConfig(protocol.task_id)
    if not task_cfg or GameEnum.TASK_COMPLETE_CONDITION_2 ~= task_cfg.condition then return end
    -- a2的特效 会报错 提示没资源 不确定后续需不需要替换
    -- local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("effect_yanjiang")
    -- EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name)
end

--已完成任务列表返回
function TaskWGCtrl:OnTaskRecorderList(protocol)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("OnTaskRecorderList", protocol)
    end

    -- print_error("OnTaskRecorderList:::", protocol)
    if not RoleWGCtrl.Instance:GetIsHasRoleData() then
        RoleWGCtrl.Instance:RegisterDelayProtocol(BindTool.Bind2(self.OnTaskRecorderList, self, protocol))
        return
    end
    TaskWGData.Instance:SetTaskCompletedIdList(protocol.task_completed_id_list)
    --刷新一下主界面图标（功能开启相关）
    GlobalEventSystem:Fire(MainUIEventType.INIT_ICON_LIST)
    self:SetHideNpcIdList(0, true)
    --仙盟工资红点刷新，登陆的时候需要判断功能是否开启，有时判断是此列表还没下发
    local guild_id = GameVoManager.Instance:GetMainRoleVo().guild_id
    if guild_id > 0 then
        RemindManager.Instance:Fire(RemindName.Guild_Wage)
        RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
    end

    GlobalEventSystem:Fire(OtherEventType.TASK_COMPLETED_ID_LIST_INIT)

    Scene.Instance:CheckTaskCameraByLogic()
end

--任务记录列表数据改变
function TaskWGCtrl:OnTaskRecorderInfo(protocol)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("OnTaskRecorderInfo", protocol)
    end

    TaskWGData.Instance:ChangeTaskCompleted(protocol.completed_task_id)
    MainuiWGCtrl.Instance:ChangeFunctionTrailer()
end

--返回可接受列表
function TaskWGCtrl:OnAccpetableTaskList(protocol)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("OnAccpetableTaskList", protocol)
    end

    -- print_error("返回可接受列表OnAccpetableTaskListprotocol:::", protocol)
    if not RoleWGCtrl.Instance:GetIsHasRoleData() then
        RoleWGCtrl.Instance:RegisterDelayProtocol(BindTool.Bind2(self.OnAccpetableTaskList, self, protocol))
        return
    end
    self:SetFirstTaskByAcc(protocol)

    TaskGuide.Instance:InitTask(true)
    self:TaskUpdateing(false)
    TaskWGData.Instance:SetTaskCapAcceptedIdList(protocol.task_can_accept_id_list)

    self:SetHideNpcIdList(0, true)
    if self.npc_dialog:IsOpen() then
        local npc_id = self.npc_dialog:GetNpcId()
        if npc_id ~= nil and type(npc_id) == "number" and npc_id > 0 then
            local task_cfg = TaskWGData.Instance:GetNpcOneExitsTask(npc_id)
            if task_cfg and task_cfg.task_id ~= self.npc_dialog:GetTaskId() then
                self.npc_dialog:Flush(0, "all", {npc_id})
            end
        end
    end

    --仙盟建设任务
    GuildWGCtrl.Instance:NeedAutoTask()
end

--放弃任务
function TaskWGCtrl:SendTaskGiveup(task_id)
    local cmd = ProtocolPool.Instance:GetProtocol(CSTaskGiveup)
    cmd.task_id = task_id
    cmd:EncodeAndSend()
end


--增加这个接口只是为了处理小飞鞋的情况，F2已没有小飞鞋这个道具以及相应业务逻辑
function TaskWGCtrl:SendFlyForFree(scene_id, pos_x, pos_y, item_index, is_auto_buy, sign)
    self:SendFlyByShoe(scene_id, pos_x, pos_y, item_index, is_auto_buy, sign, true)
end

--飞行到某地
function TaskWGCtrl:SendFlyByShoe(scene_id, pos_x, pos_y, item_index, is_auto_buy, sign, is_fly_free, fly_type)
    is_fly_free = is_fly_free or false
    if not self:IsCanGoToScene(scene_id, true) then
        return
    end

    -- if Scene.Instance:GetSceneType() ~= SceneType.Common then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotToTarget)
    --     return
    -- end
    local main_role = Scene.Instance:GetMainRole()
    if main_role and main_role:IsQingGong() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.QingGong.NoDeliveryQingGong)
        return
    end

    if main_role and main_role:IsMitsurugi() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.QingGong.NoDeliveryQingGong)
        return
    end
    -- local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    -- if scene_cfg ~= nil and scene_cfg.scene_type ~= SceneType.Common then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotToTarget)
    --     return
    -- end

    local can_free_fly = VipPower.Instance:GetHasPower(VipPowerId.scene_fly)
    if can_free_fly then
        item_index = -1
        is_auto_buy = false
    end

    -- if BossWGData.Instance:IsWorldBossScene(scene_id) then
    -- item_index = -1
    -- is_auto_buy = 0
    -- end

    local cmd = ProtocolPool.Instance:GetProtocol(CSFlyByShoe)
    cmd.scene_id = scene_id or -1
    cmd.pos_x = pos_x or -1
    cmd.pos_y = pos_y or -1
    cmd.item_index = (is_fly_free or not item_index) and -1 or item_index
    cmd.is_auto_buy = is_auto_buy and 1 or 0
    cmd.sign = sign or 0
    cmd.test_can_fly = 0
    cmd.fly_type = fly_type or CLIENT_FLY_REASON.NONE
    cmd:EncodeAndSend()

    GlobalEventSystem:Fire(ObjectEventType.FLY_TO_SCENE, scene_id)
end

--接受任务
function TaskWGCtrl:SendTaskAccept(task_id)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("SendTaskAccept", task_id)
    end

    -- 发协议时如果和NPC的距离超过了服务端的限制，那么就当作发送失败，不拦掉自动任务
    -- print_error("SendTaskAccept:::", task_id)
    if TaskWGData.Instance:IsCanAcceptTask(task_id) then
        local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
        if task_cfg ~= nil and task_cfg.accept_npc ~= nil and type(task_cfg.accept_npc) == "table" and next(task_cfg.accept_npc) then
            local main_role = Scene.Instance:GetMainRole()
            if main_role ~= nil and not main_role:IsDeleted() then
                local x, y = main_role:GetLogicPos()
                local dis = u3dpool.v2Sub({x = x, y = y}, {x = task_cfg.accept_npc.x, y = task_cfg.accept_npc.y})
                if Scene.Instance:GetSceneId() == task_cfg.accept_npc.scene and u3dpool.v2Length(dis, false) <= COMMON_CONSTS.NPC_TASK_LIMIT * COMMON_CONSTS.NPC_TASK_LIMIT then
                    self:TaskUpdateing(true)
                end
            else
                self:TaskUpdateing(true)
            end
        else
            self:TaskUpdateing(true)
        end
    end

    local cmd = ProtocolPool.Instance:GetProtocol(CSTaskAccept)
    cmd.task_id = task_id
    cmd:EncodeAndSend()
end

--完成任务
function TaskWGCtrl:SendTaskCommit(task_id)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("SendTaskCommit", task_id)
    end
    
    -- print_error("SendTaskCommit完成任务:::", task_id)
    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
    if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
        DebugLog("转职任务    完成任务提交>>>>>>>>>>")
    end

    -- 发协议时如果和NPC的距离超过了服务端的限制，那么就当作发送失败，不拦掉自动任务
    if task_cfg ~= nil and task_cfg.commit_npc ~= nil and type(task_cfg.commit_npc) == "table" and next(task_cfg.commit_npc) then
        local main_role = Scene.Instance:GetMainRole()
        if main_role ~= nil and not main_role:IsDeleted() then
            local x, y = main_role:GetLogicPos()
            local dis = u3dpool.v2Sub({x = x, y = y}, {x = task_cfg.commit_npc.x, y = task_cfg.commit_npc.y})
            if Scene.Instance:GetSceneId() == task_cfg.commit_npc.scene and u3dpool.v2Length(dis, false) <= COMMON_CONSTS.NPC_TASK_LIMIT * COMMON_CONSTS.NPC_TASK_LIMIT then
                self:TaskUpdateing(true)
            end
        else
            self:TaskUpdateing(true)
        end
    else
        self:TaskUpdateing(true)
    end

    local cmd = ProtocolPool.Instance:GetProtocol(CSTaskCommit)
    cmd.task_id = task_id
    cmd:EncodeAndSend()
end

--npc对话完成
function TaskWGCtrl:SendTaskNpcTalk(npc_id)
    -- 发协议时如果和NPC的距离超过了服务端的限制，那么就当作发送失败，不拦掉自动任务
    local obj = Scene.Instance:GetNpcByNpcId(npc_id)
    if obj ~= nil and not obj:IsDeleted() then
        local main_role = Scene.Instance:GetMainRole()
        if main_role ~= nil and not main_role:IsDeleted() then
            local x, y = main_role:GetLogicPos()
            local n_x, n_y = obj:GetLogicPos()
            local dis = u3dpool.v2Sub({x = x, y = y}, {x = n_x, y = n_y})
            if u3dpool.v2Length(dis, false) <= COMMON_CONSTS.NPC_TASK_LIMIT * COMMON_CONSTS.NPC_TASK_LIMIT then
                self:TaskUpdateing(true)
            end
        else
            self:TaskUpdateing(true)
        end
    else
        self:TaskUpdateing(true)
    end

    local cmd = ProtocolPool.Instance:GetProtocol(CSTaskTalkToNpc)
    cmd.npc_id = tonumber(npc_id)
    cmd:EncodeAndSend()
end

-- 发送悬赏p
function TaskWGCtrl:SendBountyTask(is_all, task_id)
    local cmd = ProtocolPool.Instance:GetProtocol(CSBountyCommitTask)
    cmd.commit_all = is_all or 0
    cmd.task_id = task_id or 0
    cmd:EncodeAndSend()
end

function TaskWGCtrl:CompleteTaskItemCommit(taskid, item_index)
    local cmd = ProtocolPool.Instance:GetProtocol(CSTaskItemCommit)
    cmd.taskid = taskid or 0
    cmd.item_index = item_index or 0
    cmd:EncodeAndSend()
end

-- 请求领取额外奖励
function TaskWGCtrl:SendReward(task_type, times)
    -- print_error("SendReward:::", task_type, times)
    local cmd = ProtocolPool.Instance:GetProtocol(CSTaskFetchReward)
    cmd.task_type = task_type
    cmd.times = times
    cmd:EncodeAndSend()
end

--游历请求
function TaskWGCtrl:SendYouLi(type, param1)
    -- print_error("SendYouLi:::", type, param1)
    local cmd = ProtocolPool.Instance:GetProtocol(CSYouLiReq)
    cmd.type = type
    cmd.param1 = param1 or 0
    cmd:EncodeAndSend()
end

--日常任务首次接受
function TaskWGCtrl:SendDailyTaskFirstAccept()
    -- print_error("SendDailyTaskFirstAccept:::", type, param1)
    local cmd = ProtocolPool.Instance:GetProtocol(CSDailyTaskFirstAccept)
    cmd:EncodeAndSend()
end

---------------------------------------------------------------
--操作跟踪任务栏里的任务
--------------------------------------------------------------
function TaskWGCtrl:OperateFollowTask(task_cfg, fly_shoes, is_auto)
    if fly_shoes == nil then
        fly_shoes = true
    end
    self.cur_zhixing_task_cfg = nil
    if task_cfg == nil then
        return
    end
    if type(task_cfg) ~= "table" then
        return
    end
    self.operate_follow_task_time = Status.NowTime
    local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
    -- print_error("TaskCFG_____:", task_cfg.task_id, task_status)

    if task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
        self:OperateOnCanAcceptStatus(task_cfg, fly_shoes)
    elseif task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
        self:OperateOnProcessStataus(task_cfg, fly_shoes, is_auto)
    elseif task_status == GameEnum.TASK_STATUS_COMMIT then
        self:OperateOnCommitStatus(task_cfg, fly_shoes)
    -- 如果是日常先加入到可接取列表里面
    elseif task_status == GameEnum.TASK_STATUS_NONE and task_cfg.task_type == GameEnum.TASK_TYPE_RI then
        TaskWGCtrl.Instance:SendTaskAccept(task_cfg.task_id)
    end

    if NEXT_CAN_AUTO_GUIDE_TIME  == -1 then
        NEXT_CAN_AUTO_GUIDE_TIME = 0
    end
end

--操作可接状态的任务
function TaskWGCtrl:OperateOnCanAcceptStatus(task_cfg, fly_shoes)
    -- print_error("----操作可接状态的任务-----", task_cfg and task_cfg.task_id)
    if task_cfg == nil then return end
    --记录当前任务类型并且清除人物可能存在的计时器
    self:RemberCurAuToTaskType(task_cfg.task_type)
    self:DelMainRoleAutoTakEvent()

    local target_t = task_cfg.accept_npc
    -- 如果有可接任务NPC
    if type(target_t) == "table" and next(target_t) then
        local npc_cfg = TaskWGData.Instance:GetNPCConfig(target_t.id)
        -- 如果是不可见NPC则直接打开对话面板
        if npc_cfg and 0 == npc_cfg.task_can_see then
            -- self:SendTaskAccept(task_cfg.task_id)
            if ViewManager.Instance:HasOpenView() then
				GuajiWGCtrl.Instance:SetCacheNpc(target_t.id)
                return
            end
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
            ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = target_t.id})
            return
        end

        local curr_sence_id = Scene.Instance:GetSceneId()
        if fly_shoes and curr_sence_id ~= target_t.scene and
            (task_cfg.task_type == GameEnum.TASK_TYPE_RI or
            task_cfg.task_type == GameEnum.TASK_TYPE_MENG or
            task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN or
            (task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and GameVoManager.Instance:GetMainRoleVo().level > TaskWGData.Instance.fly_task_level)
             or task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD) then
            -- 飞到目标场景再跑到NPC
            local call_back = function ()
                self:FlySceneToMove(target_t.scene, target_t.x, target_t.y, nil, nil, MoveEndType.NpcTask, target_t.id, task_cfg)
            end
            self:CheckCanChangeSceneToFollowTask(call_back)
        else
            -- 跑到NPC
            self:MoveToTarget(task_cfg, target_t, MoveEndType.NpcTask, fly_shoes)
        end
    -- 如果没有可接取NPC则直接发协议接受任务
    else
        self:SendTaskAccept(task_cfg.task_id)
    end
end

-- 自动任务时，预处理转职任务，防止小飞鞋过去后，又跑去其它点挂机
function TaskWGCtrl:BeforehandTransferTask(task_cfg)
    -- body
    if not task_cfg then return false end
    if GuajiCache.guaji_type ~= GuajiType.None then return false end
    if task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHUAN or
        not tonumber(task_cfg.c_param4) or not tonumber(task_cfg.c_param5) then
            return false
    end

    local list = MainuiWGData.Instance:GetMonstIDList(task_cfg.c_param4, task_cfg.c_param5)
    if 0 == #list then return false end
    local monster_list = Scene.Instance:GetMonsterList()

    for k,v in pairs(monster_list) do
        for l_k,l_v in pairs(list) do
            if l_v == v:GetMonsterId() then
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
                return true
            end
        end
    end
    return false
end

function TaskWGCtrl:BeforehandMengTask(task_cfg)
    local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
    if task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_13 then
        return true
    end
    return false
end

--操作正在进行中状态的任务
function TaskWGCtrl:OperateOnProcessStataus(task_cfg, fly_shoes, is_auto)
    -- print_error("----操作正在进行中状态的任务-----", task_cfg and task_cfg.task_id)
    if task_cfg == nil then
        return
    end

    --记录当前任务类型并且清除人物可能存在的计时器
    self:RemberCurAuToTaskType(task_cfg.task_type)
    self:DelMainRoleAutoTakEvent()
    if tonumber(task_cfg.c_param4) == 10000 then --参数4为10000时，参数5为引导id
        if ViewManager.Instance:HasOpenView() and is_auto then
            return
        end

        local need_guide = true
        if FunctionGuide.shangjin_id == tonumber(task_cfg.c_param5) then
            need_guide = TaskWGData.Instance:IsCanAcceptShangjing()
        end

        if need_guide then
            if NEXT_CAN_AUTO_GUIDE_TIME <= Status.NowTime
                or tonumber(task_cfg.c_param5) ~= FunctionGuide.Instance:GetLastGuideid() then
                if NewTeamWGData.Instance:IsGuideMatching(tonumber(task_cfg.c_param5)) then
                    if NEXT_CAN_AUTO_GUIDE_TIME == -1 then
                        SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.GuideTaskMatchLimit)
                    end
                    return
                end
                NEXT_CAN_AUTO_GUIDE_TIME = Status.NowTime + 30
                FunctionGuide.Instance:TriggerGuideById(tonumber(task_cfg.c_param5))
            end
            return
        end
    end

    if task_cfg.open_panel_name ~= nil and task_cfg.open_panel_name ~= "" then --直接打开面板
        if is_auto then
            return
        end

        if task_cfg.open_panel_name == GuideModuleName.ZhuanSheng then
            -- 打开对应转职页签
            local zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
            zhuan = zhuan + 1
            if zhuan > TransFerWGData.Instance:GetTransFerRewardCfgMax() then
                TaskWGCtrl.Instance:OpenTaskGainExpTipView()
            else
                ViewManager.Instance:Open(task_cfg.open_panel_name)
            end

        elseif string.find(task_cfg.open_panel_name,"ItemTipGetWay") then
            --根据物品id打开 物品tips的获取路径
            local t = Split(task_cfg.open_panel_name, "#")
            if t[2] then
                local item_id = tonumber(t[2])
                TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
            end
        elseif string.find(task_cfg.open_panel_name,"TaskGainExpTipView") then
            TaskWGCtrl.Instance:OpenTaskGainExpTipView()
        else
            if tonumber(task_cfg.c_param4) == 66798383 then--boss跳转
                local layer = math.floor(task_cfg.c_param5/100)
                if string.find(task_cfg.open_panel_name, FunName.BossVip) ~= nil then
                    layer = BossWGData.Instance:GetNowVipIndexByLayer(layer)
                end
                local index = task_cfg.c_param5%100
                BossWGData.Instance:SetBossTuJianIndex(nil, layer, index)
            end

            FunOpen.Instance:OpenViewNameByCfg(task_cfg.open_panel_name)
        end

        local open_panel_name = task_cfg.open_panel_name
        if not open_panel_name then return end
        local t = Split(open_panel_name, "#")
        local view_name = t[1]
        TaskGuide.Instance:AutoTaskOpenView(view_name)
        return
    end

    if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_5 then--条件是完成任务
        if task_cfg.c_param1 == GameEnum.TASK_TYPE_MENG then--仙盟特殊处理，
            local guild_task_cfg = TaskWGData.Instance:GetFirstTaskConfigByType(GameEnum.TASK_TYPE_MENG)
            if guild_task_cfg ~= nil then
                task_cfg = guild_task_cfg --模仿处理仙盟类型的任务
            end
        end
    end

    -- 在非普通场景点任务，直接挂机
    if Scene.Instance:GetSceneType() ~= SceneType.Common 
        and Scene.Instance:GetSceneType() ~= SceneType.WorldBoss  -- 世界boss允许传送做任务
        and Scene.Instance:GetSceneType() ~= SceneType.VIP_BOSS then         
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        return
    end

    local cur_select_target = nil
    local target_objcfg_list = task_cfg.target_obj    -- 配置表中的目標表
    if type(target_objcfg_list) ~= "table" then
        return
    end
    local default_target_objcfg = target_objcfg_list[1]
    local random_target_t = target_objcfg_list[math.floor(math.random(1, #target_objcfg_list))]

    local end_type = MoveEndType.Normal
    if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_0 or task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_24 then --与npc对话任务
        end_type = MoveEndType.NpcTask

    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 then --打怪任务
        local is_task_monster = false
        if IsEmptyTable(task_cfg.target_obj) then
            if task_cfg.c_param1 ~= "" then
                local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(task_cfg.c_param1)
                -- 判断是否是任务创建怪
                if monster_cfg and monster_cfg.sub_type == MONSTER_SUB_TYPE.TASK then
                    is_task_monster = true
                end
            end

            if not is_task_monster then
                print_error("配置：没有击杀目标", task_cfg.task_id)
                return
            end
        end

        GuajiCache.monster_id = task_cfg.c_param1
        end_type = MoveEndType.FightByMonsterId

        cur_select_target = MainuiWGData.Instance:GetTargetObj()--当前若已选将不再随机选出
        if default_target_objcfg == nil or not TaskWGData.Instance:GetIsTaskTarget(cur_select_target, task_cfg) then
            cur_select_target = nil
        end

        if cur_select_target == nil then
            cur_select_target = Scene.Instance:SelectMinDisMonster(task_cfg.c_param1)--选择最近的
        end

        -- 如果有选中怪，直接挂机，不需要走下面的逻辑
        if cur_select_target then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
            return
        else
            if is_task_monster then
                local monster_task_cfg = TaskWGData.Instance:GetTaskMonsterCfg(task_cfg.task_id, task_cfg.c_param1)
                if monster_task_cfg then
                    local pos = Split(monster_task_cfg.call_monster_pos, ",", true)
                    local main_role = Scene.Instance:GetMainRole()
                    local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(task_cfg.c_param1)
                    local go_pos_x, go_pos_y = pos[1] or 0, pos[2] or 0
                    if monster_cfg and main_role then
                        local role_x, role_y = main_role:GetLogicPos()
                        local range = math.min(monster_task_cfg.call_monster_range, monster_cfg.aoi_range)
                        range = math.max(1, range - 3)--容错
                        local cur_scene_id = Scene.Instance:GetSceneId()

                        if monster_task_cfg.scene_id == cur_scene_id and AStarFindWay:IsInRange(role_x, role_y, go_pos_x, go_pos_y, range) then
                            RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_CREATE_TASK_MONSTER, task_cfg.task_id)
                        else
                            go_pos_x, go_pos_y = AStarFindWay:GetTargetXY(role_x, role_y, go_pos_x, go_pos_y, range)
                            MoveCache.SetEndType(MoveEndType.FightByMonsterId)
                            GuajiCache.monster_id = task_cfg.c_param1
                            MoveCache.param1 = task_cfg.c_param1
                            local monster_range = BossWGData.Instance:GetMonsterRangeByid(task_cfg.c_param1)
                            GuajiWGCtrl.Instance:MoveToPos(monster_task_cfg.scene_id, go_pos_x, go_pos_y, monster_range)
                        end
                    end

                    return
                end
            end
        end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_11 then --悬赏任务
        -- task_target_type = SceneObjType.Monster
        local monster_list = Scene.Instance:GetMonsterList()
        for k,v in pairs(monster_list) do
            if v:GetVo() and not v:IsRealDead() and v:GetVo().level >= task_cfg.c_param4 and v:GetVo().level <= task_cfg.c_param5 then
                cur_select_target = v
                break
            end
        end
        -- print_error("task_cfg.c_param1",task_cfg.c_param1,task_cfg.c_param3, nil ~= cur_select_target)
         -- 如果有选中怪，直接挂机，不需要走下面的逻辑
        if cur_select_target then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            return
        end

        local temp_monst_id = MainuiWGData.Instance:GetRandomMonstID(task_cfg.c_param4, task_cfg.c_param5)
        if temp_monst_id > 0 then
            -- cur_select_target = Scene.Instance:GetMonstObjByMonstID(temp_monst_id)
            local temp_monst_cfg = TaskWGData.Instance:GetMonstTargetCfgList(temp_monst_id)
                if fly_shoes then
                    local target_t = {}
                    target_t.x = temp_monst_cfg.monst_x
                    target_t.y = temp_monst_cfg.monst_y
                    target_t.scene = temp_monst_cfg.scene_id
                    target_t.id = temp_monst_id
                    self:MoveToTarget(task_cfg, target_t, MoveEndType.FightByMonsterId, fly_shoes)
                else
                    local ok_func = function ()
                        self:FlySceneToMove(temp_monst_cfg.scene_id, temp_monst_cfg.monst_x, temp_monst_cfg.monst_y, nil, nil, MoveEndType.FightByMonsterId, temp_monst_id, task_cfg)
                    end
                     self:CheckCanChangeSceneToFollowTask(ok_func)
                end
                -- GuajiWGCtrl.Instance:FlyToScenePos(temp_monst_cfg.scene_id, temp_monst_cfg.monst_x, temp_monst_cfg.monst_y, fly_shoes)
                -- self.temp_task_monst = temp_monst_cfg
                return
            -- end
        end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_2 then --采集任务
        if IsEmptyTable(task_cfg.target_obj) then
            print_error("配置：没有采集目标  任务：", task_cfg.task_id)
            return
        end

        end_type = MoveEndType.GatherById
        cur_select_target = Scene.Instance:SelectMinDisGather(task_cfg.c_param1)--选择最近的
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_13 then -- 提交装备
        if is_auto then
            return
        end
        ViewManager.Instance:Open(GuideModuleName.TaskConsumeView)
        ViewManager.Instance:FlushView(GuideModuleName.TaskConsumeView,0, "task_data", task_cfg)
        TaskGuide.Instance:AutoTaskOpenView(GuideModuleName.TaskConsumeView)
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_14 then -- 收集物品
        -- TaskWGData.Instance.shang_rand_monst_id = 0
        local monster_list = Scene.Instance:GetMonsterList()
        for k,v in pairs(monster_list) do
            if v:GetVo() and not v:IsRealDead() and
                ((v:GetVo().level >= task_cfg.c_param4 and v:GetVo().level <= task_cfg.c_param5) or
                v:GetVo().monster_id == TaskWGData.Instance.shang_rand_monst_id) then
                cur_select_target = v
                break
            end
        end
        -- print_error("task_cfg.c_param1",task_cfg.c_param4,task_cfg.c_param5, nil ~= cur_select_target)
         -- 如果有选中怪，直接挂机，不需要走下面的逻辑
        if cur_select_target then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            return
        end
        local temp_monst_id = MainuiWGData.Instance:GetRandomMonstID(task_cfg.c_param4, task_cfg.c_param5)
        if temp_monst_id > 0 then
            local temp_monst_cfg = TaskWGData.Instance:GetMonstTargetCfgList(temp_monst_id)
            if fly_shoes then
                local target_t = {}
                target_t.x = temp_monst_cfg.monst_x
                target_t.y = temp_monst_cfg.monst_y
                target_t.scene = temp_monst_cfg.scene_id
                target_t.id = temp_monst_id
                self:MoveToTarget(task_cfg, target_t, MoveEndType.FightByMonsterId, fly_shoes)
            else
                local ok_func = function ()
                    self:FlySceneToMove(temp_monst_cfg.scene_id, temp_monst_cfg.monst_x, temp_monst_cfg.monst_y, nil, nil, MoveEndType.FightByMonsterId, temp_monst_id, task_cfg)
                end
                self:CheckCanChangeSceneToFollowTask(ok_func)
            end
            return
        end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_23 then -- 收集伪掉落
        GuajiCache.monster_id = task_cfg.c_param1
        end_type = MoveEndType.FightByMonsterId

        cur_select_target = MainuiWGData.Instance:GetTargetObj()--当前若已选将不再随机选出
        if default_target_objcfg == nil or not TaskWGData.Instance:GetIsTaskTarget(cur_select_target, task_cfg) then
            cur_select_target = nil
        end
        if cur_select_target == nil then
            cur_select_target = Scene.Instance:SelectMinDisMonster(task_cfg.c_param1)--选择最近的
        end

        -- 如果有选中怪，直接挂机，不需要走下面的逻辑
        if cur_select_target then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Monster)
            return
        end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 or
        task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_21 then -- 通关副本
        -- 上面判断过了 这里多余判断 先注释
        -- if Scene.Instance:GetSceneType() ~= 0 then
        --     return
        -- end
        if task_cfg.c_param1 == FUBEN_TYPE.XIN_MO_FB then --心魔副本需要做判斷
            if task_cfg.c_param3 ~= 0 then
                if not FuBenPanelWGData.Instance:GetXinMoFBRoleIsOpen(task_cfg.c_param3 - 1) then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.ErrorInfo_2721)
                    return
                end
            end
        end

        if task_cfg.c_param1 == FUBEN_TYPE.GUIDE_BOSS then
            if is_auto then
                return
            end
            
            if not ViewManager.Instance:IsOpen(GuideModuleName.Boss) then
                ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_world)
                TaskGuide.Instance:AutoTaskOpenView(GuideModuleName.Boss)
            end
        else
            if task_cfg.task_type == GameEnum.TASK_TYPE_RI or
                task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN or
            	task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD or
                task_cfg.task_type == GameEnum.TASK_TYPE_MENG then
                local target_t = task_cfg.accept_npc
                if type(target_t) == "table" and next(target_t) then
                    if fly_shoes or target_t.scene == Scene.Instance:GetSceneId() then
                        self:MoveToTarget(task_cfg, target_t, MoveEndType.NpcTask, fly_shoes)
                    else
                        self.cur_zhixing_task_cfg = task_cfg
                        local ok_func = function ()
                            self:FlySceneToMove(target_t.scene, target_t.x, target_t.y, nil, nil, MoveEndType.NpcTask, target_t.id, task_cfg)
                        end
                        self:CheckCanChangeSceneToFollowTask(ok_func)
                    end
                    return
                end
            else
                GuajiWGCtrl.Instance:StopGuaji(false, true)
                -- 如果是修真之路
                if task_cfg.c_param1 == SceneType.ShenMoZhiXiFB then
                    FuBenWGData.Instance:SetTXGEnter(true)
                end
                FuBenWGCtrl.Instance:SendEnterFB(task_cfg.c_param1, task_cfg.c_param3)
                -- if not ViewManager.Instance:IsOpen(GuideModuleName.TaskEnterFbTipsView) then
                --     ViewManager.Instance:Open(GuideModuleName.TaskEnterFbTipsView,0, "task_data", task_cfg)
                --     TaskGuide.Instance:AutoTaskOpenView(GuideModuleName.TaskEnterFbTipsView)
                -- end
            end
        end
        --FuBenWGCtrl.Instance:SendEnterFB(task_cfg.c_param1, task_cfg.c_param3)
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_4 then -- 通往某一个场景
    	-- print_error("task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_4",task_cfg,task_cfg.param1)
    	if task_cfg.c_param1 ~= nil then
    		GuajiWGCtrl.Instance:MoveToScene(task_cfg.c_param1)
    	end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_8 then
         if task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_HUSONG_TASK then
            local husong_cfg = YunbiaoWGData.Instance:GetYubiaoOtheraAtuo()
            if husong_cfg then
                local reason = TaskWGCtrl.Instance:JumpFly(husong_cfg.npc_scene1, husong_cfg.pos_x, husong_cfg.pos_y,true)
                YunbiaoWGCtrl.Instance:SetIsClickHuSong(true)
                if reason and reason == Fly_Shoe_Return_Type.LIMIT then
                    local role = Scene.Instance:GetMainRole()
                    local role_x,role_y = role:GetLogicPos()

                    if role_x == husong_cfg.pos_x and role_y == husong_cfg.pos_y then
                        YunbiaoWGCtrl.Instance:OpenWindow()
                    else
                        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                            YunbiaoWGCtrl.Instance:OpenWindow()
                        end)
                        GuajiWGCtrl.Instance:MoveToPos(husong_cfg.npc_scene1, husong_cfg.pos_x, husong_cfg.pos_y)
                    end
                    YunbiaoWGCtrl.Instance:SetIsClickHuSong(false)
                    return
                end
            end
        end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_9 then
        if task_cfg.c_param3 and task_cfg.c_param3 == FUBEN_TYPE.FBCT_EXPFB then --类型9拓展一个副本类型,特殊处理(这里处理了经验本)
            FuBenWGCtrl.Instance:SendEnterExpFB(true)
        end
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7 then
        if task_cfg.task_id == 17650 then
            --指引玩家第一次打Boss副本任务特殊处理
            local layer = BossWGData.Instance:GetLayerNumByShowIndex(BossViewIndex.WorldBoss)
            local scene_id = BossWGData.Instance:GetWorldBossSceneIdByIndex(layer - 1)
            local cur_scene_id = Scene.Instance:GetSceneId()

            local function move_to_kill()
                local real_cur_scene_id = Scene.Instance:GetSceneId()
                if real_cur_scene_id ~= scene_id then
                    return
                end

                local monster_list = BossWGData.Instance:GetWorldBossCfgsBySceneId(scene_id, 1)
                local distance = 10000
                local boss_id = 0
                local data
                local pos_x, pos_y = Scene.Instance:GetMainRole():GetLogicPos()
                --寻找最近的目标
                for k,v in pairs(monster_list) do
                    local dis = GameMath.GetDistance(v.x_pos, v.y_pos, pos_x, pos_y, false)
                    if dis <= distance then
                        distance = dis
                        boss_id = v.boss_id
                        data = v
                    end
                end
                GuajiCache.monster_id = boss_id
                MoveCache.param1 = boss_id
                MoveCache.SetEndType(MoveEndType.FightByMonsterId)
                MoveCache.task_id = task_cfg.task_id
                MoveCache.task_type = task_cfg.task_type
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
                    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
                end)

                if data == nil then
                    return
                end

                local range = BossWGData.Instance:GetMonsterRangeByid(boss_id)
                GuajiWGCtrl.Instance:MoveToPos(scene_id, data.x_pos, data.y_pos, range)
            end

            if cur_scene_id == scene_id then
                move_to_kill()
            else
                self:AddFlyUpList(move_to_kill)
                BossWGData.Instance:ClearCurSelectBossID()
                BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ENTER, layer - 1)
            end

            return
        elseif task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_66 then
            -- 游历任务
            if is_auto and not self:IsCanYouLi() then return end
            local target_t = task_cfg.accept_npc
            if not IsEmptyTable(target_t) then
                if fly_shoes or target_t.scene == Scene.Instance:GetSceneId() then
                    self:MoveToTarget(task_cfg, target_t, MoveEndType.NpcTask, fly_shoes)
                else
                    local ok_func = function ()
                        self:FlySceneToMove(target_t.scene, target_t.x, target_t.y, nil, nil, MoveEndType.NpcTask, target_t.id, task_cfg)
                    end
                    self:CheckCanChangeSceneToFollowTask(ok_func)
                end
                return
            end
            return
        elseif task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_73 then
            TaskWGData.Instance:DoShangJinTask(true)
        elseif task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_10 then
            if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
                local target_t = task_cfg.accept_npc
                self:MoveToTarget(task_cfg, target_t, MoveEndType.NpcTask, fly_shoes)
                return
            end
        elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_52 then   -- 竞技场
            Field1v1WGCtrl.Instance:SendField1V1Info()
        elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_TIANTI then
            ArenaTiantiWGCtrl.Instance:SendFieldTiantiInfo()
        end
    
    elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_26 or task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_27 then
        ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 进入: %s %s", task_cfg.task_id, task_cfg.condition))
        local main_role = Scene.Instance:GetMainRole()
        if not main_role then return end
        local cur_scene_id = Scene.Instance:GetSceneId()
        local role_x, role_y = main_role:GetLogicPos()
        
        local function task_27_func()
            local guide_cfg = FunctionGuide.Instance:GetGuideCfgByTaskID(task_cfg.task_id)
            ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 开始做27的表现: %s %s %s", task_cfg.task_id, task_cfg.condition, guide_cfg ~= nil))
            if not guide_cfg then
                return
            end

            local main_role = Scene.Instance:GetMainRole()
            if not main_role then
                return
            end

            if FunctionGuide.GetIsAlreadyGuide(guide_cfg.id) then
                RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TASK_BEAST_SKILL, task_cfg.c_param5)
                TaskGuide.Instance:SideTOStopTask(false)
                TaskGuide.Instance:SpecialConditions(true, 1)
                ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 提交任务: %s %s", task_cfg.task_id, task_cfg.condition))
            else
                main_role:SetDirectionByXY(task_cfg.c_param2, task_cfg.c_param3)
                main_role:SetIsGatherState(true, CaijiType.TaskForce)
                FunctionGuide.Instance:TriggerGuideById(guide_cfg.id)
                ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 触发引导: %s %s", task_cfg.task_id, task_cfg.condition))
            end
        end

        local need_go_pos = false
        if cur_scene_id ~= task_cfg.c_param1 then
            need_go_pos = true
        else
            if not AStarFindWay:IsInRange(role_x, role_y, task_cfg.c_param2, task_cfg.c_param3, task_cfg.c_param4) then
                need_go_pos = true
            else
                if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_26 then
                    TaskWGCtrl.SendTaskWalkToPos()-- 告诉服务器到目的地
                else
                    ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 到达目标点1: %s %s", task_cfg.task_id, task_cfg.condition))
                    TaskGuide.Instance:SideTOStopTask(true)
                    task_27_func()
                end
            end
        end

        if need_go_pos then
            TaskGuide.Instance:SideTOStopTask(true)
            GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 到达目标点2: %s %s", task_cfg.task_id, task_cfg.condition))
                if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_26 then
                    TaskWGCtrl.SendTaskWalkToPos()-- 告诉服务器到目的地
                    TaskGuide.Instance:SideTOStopTask(false)
                else
                    task_27_func()
                end
            end)

            GuajiWGCtrl.Instance:MoveToPos(task_cfg.c_param1, task_cfg.c_param2, task_cfg.c_param3, task_cfg.c_param4)
        end

        return
    end

    local target_t = nil
    if cur_select_target ~= nil and default_target_objcfg ~= nil then
        target_t = {}
        target_t.obj = cur_select_target
        target_t.obj_id = cur_select_target:GetObjId()
        target_t.x, target_t.y = cur_select_target:GetLogicPos()
        target_t.scene = default_target_objcfg.scene
        target_t.id = default_target_objcfg.id
    else
        target_t = random_target_t
    end
        -- print_error("OperateOnProcessStataus MoveToTarget",task_cfg,target_t,end_type,target_t)
    if target_t then
        local curr_sence_id = Scene.Instance:GetSceneId()
        if not fly_shoes and curr_sence_id ~= target_t.scene and
            (task_cfg.task_type == GameEnum.TASK_TYPE_RI or
            task_cfg.task_type == GameEnum.TASK_TYPE_MENG or
            task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN or
            (task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and GameVoManager.Instance:GetMainRoleVo().level > TaskWGData.Instance.fly_task_level)
            or task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD) then
             self.cur_zhixing_task_cfg = task_cfg
            local ok_func = function ()
                self:FlySceneToMove(target_t.scene, target_t.x, target_t.y, nil, nil, end_type, target_t.id, task_cfg)
            end
            self:CheckCanChangeSceneToFollowTask(ok_func)
        else
            self:MoveToTarget(task_cfg, target_t, end_type, fly_shoes)
        end
    end
end

-- 是否可游历
function TaskWGCtrl:IsCanYouLi()
    -- body
    if Scene.Instance:GetSceneType() ~= SceneType.Common then return false end
    if YunbiaoWGData.Instance:GetIsHuShong() then return false end
    if NewTeamWGData.Instance:GetIsMatching() then return false end
    if self:IsFly() then return false end

    local main_role = Scene.Instance:GetMainRole()
    if main_role:HasCantMoveBuff() or main_role:HasCantMoveBuffButCanShowMove() then
        return false
    end

    if main_role:IsAtkPlaying() then
        return false
    end

    if main_role:IsJump() then
        return false
    end

    local youli_cfg = ConfigManager.Instance:GetAutoConfig("newer_cfg_auto").youli[1]

    if main_role:GetVo().level < youli_cfg.level then
        return false
    end

    return true
end

--操作提交状态的任务
function TaskWGCtrl:OperateOnCommitStatus(task_cfg, fly_shoes)
    -- print_error("----操作提交状态的任务-----", task_cfg and task_cfg.task_id)
    if nil == task_cfg then
        return
    end

    --记录当前任务类型并且清除人物可能存在的计时器
    self:RemberCurAuToTaskType(task_cfg.task_type)
    self:DelMainRoleAutoTakEvent()

    --建设任务不做自动提交
    if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD  then
        local task_states = GuildWGData.Instance:GetGuildBuildTaskStates(task_cfg.task_id)
        if task_states == GameEnum.TASK_STATUS_COMMIT then
            return
        end
    end
    local curr_sence_id = Scene.Instance:GetSceneId()
    local target_t = task_cfg.commit_npc
    if IsEmptyTable(task_cfg.commit_npc) then --没配npc直接完成
        -- if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
        --     ActivityWGCtrl.Instance:SendTumoCommitTask(0,task_cfg.task_id,0)
        -- else
        -- param_4:1,购买物品；2,提交物品；3,骰子任务
        -- 当param_4为1或2时，param_5为奖励：“物品id，数量”
        -- if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and task_cfg.c_param4 == 3 then
        --     TaskGuide.Instance:CanAutoAllTask(false)
        --     TaskWGCtrl.Instance:OpenTaskDice(task_cfg)
        --     return
        -- end

        if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
            --需求 赏金任务达成领取条件后，只有第10环，第20环才弹出赏金任务界面，其他环数不弹
            local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_RI)
            if 0 == #task_list then
                ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
                return
            end
            local task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
            if not task_cfg then return end
            local curr_task = task_cfg
            local task_status = TaskWGData.Instance:GetTaskStatus(curr_task.task_id)
            if task_status == GameEnum.TASK_STATUS_COMMIT then
                TaskWGCtrl.Instance:SendTaskCommit(curr_task.task_id)
            elseif task_status == GameEnum.TASK_STATUS_CAN_ACCEPT then
                -- local task_data = DailyWGData.Instance:GetTaskTuMoData()
                self:ShangJinAutoTask()
            else
                self:ShangJinAutoTask()
            end
        elseif  task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
            self:SendTaskCommit(task_cfg.task_id)
            DujieWGCtrl.Instance:OperateZhuanShenTaskStatusChange()
        else
            self:SendTaskCommit(task_cfg.task_id)
        end
        -- end
    else
        local npc_cfg = TaskWGData.Instance:GetNPCConfig(target_t and target_t.id)
        if npc_cfg and 0 == npc_cfg.task_can_see then
            -- self:SendTaskCommit(task_cfg.task_id)
            if ViewManager.Instance:HasOpenView() then
                GuajiWGCtrl.Instance:SetCacheNpc(target_t.id)
                return
            end
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
            ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = target_t.id})
            return
        end

        if GuajiWGCtrl.Instance:CheakCanFly() and target_t and not IsEmptyTable(target_t) and not fly_shoes and curr_sence_id ~= target_t.scene and
            (task_cfg.task_type == GameEnum.TASK_TYPE_RI or
            task_cfg.task_type == GameEnum.TASK_TYPE_MENG or
            task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN or
            (task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and GameVoManager.Instance:GetMainRoleVo().level > TaskWGData.Instance.fly_task_level)
            or task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD) then
            self.cur_zhixing_task_cfg = task_cfg
            local ok_func = function ()
                self:FlySceneToMove(target_t.scene, target_t.x, target_t.y, nil, nil, MoveEndType.NpcTask, target_t.id, task_cfg)
            end
            self:CheckCanChangeSceneToFollowTask(ok_func)
        else
            self:MoveToTarget(task_cfg, task_cfg.commit_npc, MoveEndType.NpcTask, fly_shoes)
        end
    end
end

function TaskWGCtrl:ShangJinAutoTask()
    if FunctionGuide.Instance:GetIsGuide() then
        FunctionGuide.Instance:StartNextStep()
    end
    TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI)
    TaskGuide.Instance:CanAutoAllTask(true)
    TaskGuide.Instance:SideTOStopTask(false)
end

--仙盟建设需要和npc对话部分
function TaskWGCtrl:SetMoveToTargetInfo()
    --仙盟建设任务在免费传送的时候需要设置一些数据出发对话
    local task_cfg = self.cur_zhixing_task_cfg
    if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
        if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_6
        or task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 then
            GuajiWGCtrl.Instance:ClearAllOperate()
            GuajiWGCtrl.Instance:CancelSelect()
            MoveCache.SetEndType(MoveEndType.NpcTask)
            local npc_id = task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and task_cfg.accept_npc.id or task_cfg.commit_npc.id
            MoveCache.param1 = npc_id
            MoveCache.task_id = task_cfg.task_id
            MoveCache.task_type = task_cfg.task_type
            GuajiCache.target_obj_id = npc_id
        else
             MoveCache.SetEndType(MoveEndType.Auto)
        end
    end
    task_cfg = nil
end

function TaskWGCtrl:MoveToTarget(task_cfg, target_t, end_type, fly_shoes)
    -- GuajiWGCtrl.Instance:StopGuaji()
    local scene_logic = Scene.Instance:GetSceneLogic()
    if not scene_logic then return end
    if not task_cfg or not target_t then return end

    if nil ~= target_t and type(target_t) == "table" and next(target_t) ~= nil then
        -- Scene.Instance:ClearAllOperate()
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
        MoveCache.SetEndType(end_type)
        MoveCache.param1 = target_t.id
        MoveCache.task_id = task_cfg.task_id
        MoveCache.task_type = task_cfg.task_type
        GuajiCache.target_obj_id = target_t.id
        if end_type == MoveEndType.FightByMonsterId then
            GuajiCache.monster_id = target_t.id
        end

        local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
        -- if GuajiWGCtrl.Instance:CheakCanFly() and fly_shoes and (not GuajiWGCtrl.CheckRange(target_t.x, target_t.y, 40) or target_t.scene ~= Scene.Instance:GetSceneId()) then --点小飞鞋前往任务
        --    self:JumpFly(target_t.scene, target_t.x, target_t.y)
        -- else
            local range = COMMON_CONSTS.TASK_NPC_MAX_RANGE
            if end_type == MoveEndType.Gather or end_type == MoveEndType.GatherById then
                range = scene_logic:MoveToGatherRange()
            elseif end_type == MoveEndType.NpcTask or end_type == MoveEndType.ClickNpc then
                range = TaskWGData.Instance:GetNPCRange(target_t.id)

            elseif task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_1 then
                local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
                if task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
                    range = BossWGData.Instance:GetMonsterRangeByid(task_cfg.c_param1)
                end
            end

            -- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
            if TaskGuide.Instance:CurrTaskType() ~= task_cfg.task_type then
                TaskGuide.Instance:CanAutoAllTask(false)
            end


            GuajiWGCtrl.Instance:MoveToPos(target_t.scene, target_t.x, target_t.y, range, false, scene_key)
        -- end
    end
end

local is_jump_fly = false
-- local need_auto_task = false
function TaskWGCtrl:IsFly(fly_state)
    if nil == fly_state then
        return is_jump_fly
    end
    is_jump_fly = fly_state
end

function TaskWGCtrl:ResetJumpState()
    self:IsFly(false)
end

function TaskWGCtrl:OnSCCanFlyByShoeAck(protocol)
    if protocol.can_fly == 0 then
    elseif protocol.can_fly == 1 then
        self:__JumpFly(protocol.scene_id, protocol.pos_x, protocol.pos_y, false, false, protocol.sign, protocol.fly_type)
    end
end

function TaskWGCtrl:OnSCTaskCallInfo(protocol)
	local main_role = Scene.Instance:GetMainRole()
    local task_id = protocol.task_id
	if main_role then
		main_role:SetAttr("task_callinfo_id", task_id)
	end
end

function TaskWGCtrl:JumpFly(scene, x, y, is_free, is_auto_buy, sign, fly_type)
    local cmd = ProtocolPool.Instance:GetProtocol(CSFlyByShoe)
    cmd.scene_id = scene
    cmd.pos_x = x
    cmd.pos_y = y
    cmd.item_index = -1
    cmd.is_auto_buy = is_auto_buy and 1 or 0
    cmd.sign = sign or 0
    cmd.test_can_fly = 1
    cmd.fly_type = fly_type or CLIENT_FLY_REASON.NONE
    cmd:EncodeAndSend()
    --self:__JumpFly(scene, x, y, is_free, is_auto_buy, sign)
end

--向服务端请求确认后调用
function TaskWGCtrl:__JumpFly(scene, x, y, is_free, is_auto_buy, sign, fly_type)
    if Scene.Instance:IsChangeSceneIng() then return end
   -- if Scene.Instance:GetSceneType() ~= 0 then return end
    if not self:IsCanGoToScene(scene, true) then return end
    -- 打开护送面板期间禁止飞行
    if ViewManager.Instance:IsOpen(GuideModuleName.YunbiaoView) then
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
    	return
    end

    if main_role:IsJump() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoesLimit2)
        return
    end

    if main_role:IsInXunYou() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
        return
    end

    local scene_id = Scene.Instance:GetSceneId()

    if scene == scene_id and GuajiWGCtrl.CheckRange(x, y, 40) then --太近不给跳
        -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoesLimit)

        GuajiWGCtrl.Instance:MoveToPos(scene, x, y)
        return Fly_Shoe_Return_Type.LIMIT
    end

    local can_free_fly = VipPower.Instance:GetHasPower(VipPowerId.scene_fly)
    local item_index = can_free_fly and -1 or ItemWGData.Instance:GetItemIndex(COMMON_CONSTS.FLY_PROP_ID)
    --if not is_free and item_index < 0 and not can_free_fly then
    --    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoesLimit3)
    --    GuajiWGCtrl.Instance:MoveToPos(scene, x, y)
    --    return Fly_Shoe_Return_Type.LIMIT
    --end
    if is_free then
        item_index = -1
    end

    -- 按策划需求去掉传动点随机位置
    local r_x, r_y = x, y
    -- local rand = 0
    -- for i = 1, 8 do
    --     rand = math.random(1000) > 500 and 4 or -4
    --     r_x = x + rand
    --     rand = math.random(1000) > 500 and 4 or -4
    --     r_y = y + rand
    --     if not AStarFindWay:IsBlock(r_x, r_y) then
    --         break
    --     end
    -- end

    if scene_id == scene then
        self:ClearFlyUpList()
        GuajiWGCtrl.Instance:SetFlyCache(r_x, r_y, scene)
        TaskWGCtrl.Instance:SendFlyByShoe(scene, r_x, r_y, item_index, is_auto_buy)
        return
    end

    self:IsFly(true)
    MountWGCtrl.Instance:SendMountGoonReq(0)
    main_role:StopMove()

    -- 跳起
    local func = function()
            --正在挂机才stop guaji
            if GuajiType.None ~= GuajiCache.guaji_type then
                GuajiWGCtrl.Instance:StopGuaji()
            end
        end

    local main_role = Scene.Instance:GetMainRole()
    main_role:PlayFlyUpEffect(func) --播放飞天特效
    GlobalEventSystem:Fire(OtherEventType.JUMP_STATE_CHANGE, true)

    GlobalTimerQuest:AddDelayTimer(function() --延迟1秒传送
        --正在挂机才stop guaji
        if GuajiType.None ~= GuajiCache.guaji_type then
            GuajiWGCtrl.Instance:StopGuaji()
        end

        GuajiWGCtrl.Instance:SetFlyCache(r_x, r_y, scene)
        TaskWGCtrl.Instance:SendFlyByShoe(scene, r_x, r_y, item_index, is_auto_buy, sign)

        -- if scene == Scene.Instance:GetSceneId() then
        --     Scene.Instance:ShowLoadingView()
        -- end
     end, 1)
end

function TaskWGCtrl:CloseLoadingView(is_close_call_back, show_loading)
    if not show_loading then
        self:IsFly(false)
        return
    end

    local scene_type = Scene.Instance:GetSceneType()
    if CgManager.Instance:IsCgIng() and scene_type ~= SceneType.Field1v1 and scene_type ~= SceneType.Kf_OneVOne then --播放CG中
        self:IsFly(false)
        return
    end

    self:JumpEnd()
end

function TaskWGCtrl:JumpEnd()
    MainuiWGCtrl.Instance:LockCameraAutoRotate()
    self:JumpEndCallBack()
	-- 兼容旧的落下特效事件广播
    GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_FLY_DOWN_END)
end

--播放特效完成后的回调
function TaskWGCtrl:JumpEndCallBack()
    self:IsFly(false)
    local main_role = Scene.Instance:GetMainRole()
    if not main_role then return end
    -- main_role:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
    main_role:ChangeToCommonState()
    main_role:UpdateCameraFollowTarget()
    MainuiWGCtrl.Instance:UnLockCameraAutoRotate()

    if MoveCache.is_valid then
        GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.XunLu)
        local call_back = GuajiWGCtrl.Instance:GetMoveToPosCallBack()
        if (MoveCache.end_type == MoveEndType.NpcTask or MoveCache.end_type == MoveEndType.ClickNpc)
        and MoveCache.param1 and GuajiWGCtrl.CheckRange(MoveCache.x, MoveCache.y, MoveCache.range) then
            GuajiWGCtrl.Instance:SetFlytoNpcTalkCamera(MoveCache.param1, true)
        end

        local x, y
        if MoveCache.x ~= nil and MoveCache.y ~= nil and MoveCache.scene_id ~= nil and MoveCache.scene_id == Scene.Instance:GetSceneId() then
            if AStarFindWay:IsBlock(MoveCache.x, MoveCache.y) then
                x, y = AStarFindWay:GetAroundVaildXY(MoveCache.x, MoveCache.y, MoveCache.range, false)
            else
                x, y =  MoveCache.x , MoveCache.y
            end
        end

        if x ~= nil and y ~= nil then
            GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
            GuajiWGCtrl.Instance:MoveToPos(MoveCache.scene_id, x, y, 1)
        end

    elseif GuajiCache.event_guaji_type == GuajiType.Auto then
    else
        GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)
    end

    if self.fly_up_callback then
        for i,v in ipairs(self.fly_up_callback) do
            v()
        end
        self:ClearFlyUpList()
    end
end

--飞天的时候
function TaskWGCtrl:AddFlyUpList(call_back)
    if nil == self.fly_up_callback then
        self.fly_up_callback = {}
    end
    self.fly_up_callback[#self.fly_up_callback + 1] = call_back
end

function TaskWGCtrl:ClearFlyUpList()
    -- body
    self.fly_up_callback = nil
end

function TaskWGCtrl:CancelUpdateAllNpcStatus()
    if self.update_npc_status then
        GlobalTimerQuest:CancelQuest(self.update_npc_status)
        self.update_npc_status = nil
    end
end

function TaskWGCtrl:OnTaskDataList(reason)
    self:UpdateAllNpcStatus()

    if reason == TASK_LIST_CHANGE_REASON_CANACCEPT then
        local task_info = self.task_data.task_accepted_info_list
        for task_id,v in pairs(task_info) do
            self:SetTaskMonsterList(task_id, true)
        end
    end
end

function TaskWGCtrl:TaskDataChange(task_id, reason)
    self:UpdateAllNpcStatus()

    if reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
        self:SetTaskMonsterList(task_id, true)
    elseif reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
        self:SetTaskMonsterList(task_id, false)
    end
end

function TaskWGCtrl:UpdateAllNpcStatus()
    self:CancelUpdateAllNpcStatus()
    self.update_npc_status = GlobalTimerQuest:AddDelayTimer(function ()
        self:_UpdateAllNpcStatus()
    end, 0.3)
end

function TaskWGCtrl:_UpdateAllNpcStatus()
    local npc_list = Scene.Instance:GetNpcList()
    for _, v in pairs(npc_list) do
        local status = TaskWGData.Instance:GetNpcTaskStatus(v:GetVo().npc_id)
        v:SetTaskState(status)
    end
end

function TaskWGCtrl:UpdateAllNpcBubbleStatus(task_id, status_type)
    local npc_list = Scene.Instance:GetNpcList()
    for _, v in pairs(npc_list) do
        v:TaskToBubble(task_id, status_type)
    end
end

function TaskWGCtrl:OpenTaskHandView(panel_type, task_id)

end

function TaskWGCtrl:OpenAllRewardHandView(panel_type)

end

function TaskWGCtrl:OpenTaskGainExpTipView(pos, anchors)
    self.task_gain_exp_tip_view:SetRootPos(pos, anchors)
    self.task_gain_exp_tip_view:Open()
    -- TipWGCtrl.Instance:ShowSystemMsg("推荐任务暂时没做，自己用GM升级")
end

function TaskWGCtrl:OpenTaskGainCapaTipView(pos)
    self.task_gain_capability_tip_view:SetRootPos(pos)
    self.task_gain_capability_tip_view:Open()
end

function TaskWGCtrl:DoTask(task_id)
    -- body
    self.task_data:DoTask(task_id)
end

function TaskWGCtrl:UpdateTaskPanelShow()
    if self.task_follow then
        self.task_follow:UpdateTaskPanelShow()
    end
end

function TaskWGCtrl:SetGuideFbEff(index)
    if self.task_weather_eff and index and index > 0 then
        self.task_weather_eff:SetGuideFbEff(index)
    end
end

-- 任务发送/返回标记
function TaskWGCtrl:TaskUpdateing(value)
 
    -- body
    if nil == value then
        return self.task_updateing
    end
    self.task_updateing = value
end

-- 检测任务剧情文字显示
function TaskWGCtrl:CheckTaskStoryTextShow(show_cfg,task_id,gather_id)
    local zhu_task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id or zhu_task_list[1])
    if not task_cfg then return end
    local show_type = task_cfg.show_story_type or 0
    if TaskStoryTextType.CaiJi == show_type then
        if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_2 and gather_id == task_cfg.c_param1 then
            if not self.task_story_gatherinfo then
                self.task_story_gatherinfo = {}
            end
            if not self.task_story_gatherinfo[gather_id] then
                local task_info = TaskWGData.Instance:GetTaskInfo(task_cfg.task_id)
                self.task_story_gatherinfo[gather_id] = task_info.progress_num
            end
            if task_cfg.c_param2 == self.task_story_gatherinfo[gather_id] then
                --显示提示
                TipWGCtrl.Instance:ShowStoryTextTipView(task_cfg.task_id)
            else
                self.task_story_gatherinfo[gather_id] = self.task_story_gatherinfo[gather_id] + 1
            end
        end
    elseif TaskStoryTextType.Accept == show_type then
        if show_cfg == GameEnum.DATALIST_CHANGE_REASON_ADD then
            TipWGCtrl.Instance:ShowStoryTextTipView(task_cfg.task_id)
        end
    elseif TaskStoryTextType.Commit == show_type then
        if show_cfg == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
            TipWGCtrl.Instance:ShowStoryTextTipView(task_cfg.task_id)
        end
    elseif TaskStoryTextType.PassFuBen == show_type then
        if show_cfg == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
            TipWGCtrl.Instance:ShowStoryTextTipView(task_cfg.task_id)
        end
    end
end

function TaskWGCtrl:FlySceneToMove(scene_id, pos_x, pos_y, range, call_back, end_type, id, task_cfg)
    -- body
    range = range or 2
    local curr_sence_id = Scene.Instance:GetSceneId()
    local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    if not scene_cfg then return end

    if curr_sence_id ~= scene_id then
        if scene_cfg.scene_type ~= SceneType.Common then
            return
        end
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role ~= nil then
        if 0 < main_role:GetTaskToHeighMount() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.task_mount02)
            return
        end

        if 0 < main_role:GetTaskToBianShen() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.task_bianshen02)
            return
        end
    end

    if task_cfg and TaskGuide.Instance:CurrTaskType() ~= task_cfg.task_type then
        TaskGuide.Instance:CanAutoAllTask(false)
    end

    GuajiWGCtrl.Instance:ResetMoveCache()
    local function move_to_pos()
        self:SetMoveToTargetInfo()
        if end_type then
            MoveCache.SetEndType(end_type)
        end

        if id then
            MoveCache.param1 = id
        end

        if task_cfg then
            MoveCache.task_id = task_cfg.task_id
            MoveCache.task_type = task_cfg.task_type
        end

        GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
        GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, range)
    end

    if curr_sence_id == scene_id then
        move_to_pos()
    else
        self:JumpFly(scene_id, scene_cfg.scenex, scene_cfg.sceney, true)
        self:AddFlyUpList(move_to_pos)
    end
end

-- 场景是否可跳转
function TaskWGCtrl:IsCanGoToScene(target_scene_id, show_tip)
    local tip = nil
    local is_can_go = true

    local scene = ConfigManager.Instance:GetSceneConfig(target_scene_id)
    if scene then
        local level = scene.levellimit or 0
        if level > RoleWGData.Instance.role_vo.level then
            tip = string.format(Language.Map.level_limit_tip, RoleWGData.GetLevelString(level))
            is_can_go = false
        end
    end

    -- if is_can_go and Scene.Instance:GetSceneType() ~= 0 then
    --     is_can_go = false
    --     tip = Language.Map.TransmitLimitTip
    -- end

    if not is_can_go and show_tip and tip then
        SysMsgWGCtrl.Instance:ErrorRemind(tip)
    end

    return is_can_go
end
----记录当前的自动任务类型
function TaskWGCtrl:RemberCurAuToTaskType(task_type)
    local cur_task_type = self.task_data:GetCurAuToTaskType()
    if task_type == GameEnum.TASK_TYPE_ZHI or cur_task_type == task_type then
        return
    end
    self.task_data:RemberCurAuToTaskType(task_type)
    self:UpdateTaskPanelShow()

end

function TaskWGCtrl:AddShowGuide(task_id)
    self.cancle_guide_delay = self.cancle_guide_delay or GlobalTimerQuest:AddDelayTimer(function ()
        self.task_data:SetShowEffectTaskListById(task_id)
        MainuiWGCtrl.Instance:FlushTaskView()
        --界面刷新有可能给拦截，强制把气泡框隐藏
        MainuiWGCtrl.Instance:ClsoeTaskCellGuide(task_id)
        self.cancle_guide_delay = nil
    end, 60)
end

-- 清除mainrole的自动计时器(复活后自动做上次任务普通场景的)
function TaskWGCtrl:DelMainRoleAutoTakEvent()
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
        main_role:DelAutoTaskEvent()
    end
 end

 function TaskWGCtrl:CheckCanChangeSceneToFollowTask(ok_func)
    local cur_scene_type =  Scene.Instance:GetSceneType()
    if cur_scene_type == SceneType.WorldBoss or cur_scene_type == SceneType.VIP_BOSS then
        local has_demage = (BossAssistWGData.Instance:HasBossDamage() or BossAssistWGData.Instance:HasMainRoleHurt())
        if has_demage then
            local text_dec = Language.Boss.OutSceneTips
            TipWGCtrl.Instance:OpenConfirmAlertTips(text_dec, ok_func)
            return
        else
            ok_func()
            return
        end
    else
        ok_func()
    end
end

function TaskWGCtrl:OnOperateFlyShoeParam()
    GuajiWGCtrl.Instance:StopGuaji()
    TaskGuide.Instance:CanAutoAllTask(false)
    ViewManager.Instance:CloseAll()
    BossWGData.Instance:SetBossEnterFlag(false)
end

------------------------悬赏任务Start---------------------------
-- 悬赏任务操作请求
function TaskWGCtrl:SendCSBountyListOperate(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBountyListOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
    -- print_error("悬赏任务操作请求", protocol)
end

-- 悬赏任务接取任务（孔位位置）
function TaskWGCtrl:SendOperateTypeAccept(slot_pos)
    local server_slot_pos = self:GetServerBountySlotId(slot_pos)
	self:SendCSBountyListOperate(BOUNTY_OPERATE_TYPE.BOUNTY_OPERATE_TYPE_ACCEPT, server_slot_pos)
end

-- 悬赏任务获取手记奖励（手记id）
function TaskWGCtrl:SendOperateTypeGetNodeReward(note_id)
	self:SendCSBountyListOperate(BOUNTY_OPERATE_TYPE.BOUNTY_OPERATE_TYPE_GET_NOTE_REWARD, note_id)
end

-- 一键完成悬赏单（悬赏单槽位）
function TaskWGCtrl:SendOperateTypeOneKeyFinish(slot_pos)
    local server_slot_pos = self:GetServerBountySlotId(slot_pos)
	self:SendCSBountyListOperate(BOUNTY_OPERATE_TYPE.BOUNTY_OPERATE_TYPE_ONE_KEY_FINISH, server_slot_pos)
end

-- 悬赏榜单信息
function TaskWGCtrl:SendOperateTypeInfo()
	self:SendCSBountyListOperate(BOUNTY_OPERATE_TYPE.BOUNTY_OPERATE_TYPE_INFO)
end

-- 刷新悬赏榜单信息
function TaskWGCtrl:SendOperateTypeRefresh()
	self:SendCSBountyListOperate(BOUNTY_OPERATE_TYPE.BOUNTY_OPERATE_TYPE_REFRESH)
end

-- 悬赏接取成功
function TaskWGCtrl:OnBountyAcceptResult(result, bounty_id)
    -- print_error("悬赏接取成功", result, bounty_id)
    if result == 1 then
        local task_list = TaskWGData.Instance:GetBountyTaskListCfgById(bounty_id)
        if task_list and task_list[1] then
        end
    end
end

-- 悬赏任务信息
function TaskWGCtrl:OnSCBountyListInfo(protocol)
    self.task_data:SetBountyListInfo(protocol)
    self:FlushShangJingView()
    self:FlushShangJingStoryView()

    if self.task_data:IsAcceptBounty() then
        GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, nil, false)
    end
end

-- 获取服务器对应的槽位点
function TaskWGCtrl:GetServerBountySlotId(slot_pos)
    local server_slot_pos = slot_pos - 1
    if server_slot_pos <= 0 then
        server_slot_pos = 0
    end

    return server_slot_pos
end

-- 打开悬赏界面
function TaskWGCtrl:OpenShangJingView()
	if not self.task_shangjin_view:IsOpen() then
		self.task_shangjin_view:Open()
	else
		self.task_shangjin_view:Flush()
	end
end

-- 关闭悬赏界面
function TaskWGCtrl:CloseShangJingView()
	if self.task_shangjin_view:IsOpen() then
		self.task_shangjin_view:Close()
	end
end

-- 刷新悬赏界面
function TaskWGCtrl:FlushShangJingView()
	if self.task_shangjin_view:IsOpen() then
		self.task_shangjin_view:Flush()
	end
end

-- 悬赏界面快速完成任务弹窗
function TaskWGCtrl:GetQuickFinishAlertWindow()
	if self.task_shangjin_view:IsOpen() then
		return self.task_shangjin_view:GetQuickFinishAlertWindow()
	end

    return nil
end

-- 打开手记界面
function TaskWGCtrl:OpenShangJingStoryView()
	if not self.task_shangjin_stroy_view:IsOpen() then
		self.task_shangjin_stroy_view:Open()
	else
		self.task_shangjin_stroy_view:Flush()
	end
end

-- 刷新悬赏界面
function TaskWGCtrl:FlushShangJingStoryView()
	if self.task_shangjin_stroy_view:IsOpen() then
		self.task_shangjin_stroy_view:Flush()
	end
end

-- 关闭手记界面
function TaskWGCtrl:CloseShangJingStoryView()
	if self.task_shangjin_stroy_view:IsOpen() then
		self.task_shangjin_stroy_view:Close()
	end
end

function TaskWGCtrl.SendTaskWalkToPos()
	local protocol = ProtocolPool.Instance:GetProtocol(CSTaskWalkToPos)
	protocol:EncodeAndSend()
end

-- 设置需要移动检测的任务怪信息状态
-- add_or_remove: add = true
function TaskWGCtrl:SetTaskMonsterList(task_id, add_or_remove)
    if not add_or_remove then
        if not self.task_monster_cache_list then return end
        self.task_monster_cache_list[task_id] = nil
        if IsEmptyTable(self.task_monster_cache_list) then
            self.task_monster_cache_list = nil
        end
        return
    else
        if self.task_monster_cache_list then
            if self.task_monster_cache_list[task_id] then
                return
            end
        else
            self.task_monster_cache_list = {}
        end
    end

    local scene_id = Scene.Instance:GetSceneId()
    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
    local cfg = TaskWGData.Instance:GetTaskMonsterCfg(task_id, task_cfg and task_cfg.c_param1)
    if cfg then
        local pos = Split(cfg.call_monster_pos, ",", true)
        local data = {
            scene_id = cfg.scene_id,
            check_range = cfg.call_monster_range,
            check_pos_x = pos[1] or 0,
            check_pos_y = pos[2] or 0,
            can_send = true, -- 因为不信任后端协议同步，怕多发，导致被移除后又再被添加
        }
        self.task_monster_cache_list[task_id] = data
    end
end

-- 移动检测
function TaskWGCtrl:MainRoleAreaCheck(x, y)
    if not self.task_monster_cache_list then return end
    local scene_id = Scene.Instance:GetSceneId()

    local cur_task_id
    for task_id, data in pairs(self.task_monster_cache_list) do
        if data.scene_id == scene_id and data.can_send then
            local pos_x_sub = x - data.check_pos_x
            local pos_y_sub = y - data.check_pos_y
            local check_range = data.check_range - 1 -- 容错
            check_range = math.max(1, check_range)
            if pos_x_sub * pos_x_sub + pos_y_sub * pos_y_sub <= check_range * check_range then
                cur_task_id = task_id
                self.task_monster_cache_list[task_id].can_send = false
                break
            end
        end
    end

    if cur_task_id then
        RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_CREATE_TASK_MONSTER, cur_task_id)
    end
end

function TaskWGCtrl:GuideStepChange(guide_cfg, finsh_guide_step)
    if not guide_cfg or not finsh_guide_step then return end

    local is_finsh_guide = finsh_guide_step >= guide_cfg.finish_step
    ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 引导结束1: %s %s", finsh_guide_step, is_finsh_guide))
    if is_finsh_guide then
        if guide_cfg.trigger_type == GuideTriggerType.AcceptTask_NoAuto then
            local task_id = guide_cfg.trigger_param
            local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
            local main_task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
            ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 引导结束2: %s %s", task_id, main_task_cfg and main_task_cfg.task_id))
            if main_task_cfg then
                if task_id == main_task_cfg.task_id then
                    if main_task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_27 then
                        local main_role = Scene.Instance:GetMainRole()
                        GlobalTimerQuest:AddDelayTimer(function ()
                            if main_role then
                                main_role:SetIsGatherState(false, 0)
                            end
                            ClientCmdWGCtrl.Instance:AddPrintStr(string.format("疑难杂症 引导结束3: %s", task_id))
                            RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TASK_BEAST_SKILL, main_task_cfg.c_param5)
                            TaskGuide.Instance:SideTOStopTask(false)
                            TaskGuide.Instance:SpecialConditions(true, 0.5)
                        end, 2)
                    end
                end
            end
        end
    end
end





------------------------悬赏任务End---------------------------

------------------------NPC对话缓存------------------------------
TalkCache = {
    talk_audio_str = nil,           --配置表 talk_audio 字符串
    npc_talk_audio = nil,           --正在播放的对话音效
    is_play_audio = false,          --是否正在播对话
    talk_audio_list = {},
    talk_guide_audio_list = {},
    subsection = -1,                --对话索引
    is_play_guide_audio = false,
    cur_audio_time = 0,
    cur_guide_asset = "",
    cur_task_asset = "",
}

function TalkCache.SetTalkAudioStr(talk_audio , subsection)
    TalkCache.talk_audio_str = talk_audio
    TalkCache.subsection = subsection
end

function TalkCache.GetTalkAudioStr()
    return TalkCache.talk_audio_str, TalkCache.subsection
end

function TalkCache.SetNpcTalkAudio(talk_audio)
    TalkCache.npc_talk_audio = talk_audio
end

function TalkCache.GetNpcTalkAudio()
    return TalkCache.npc_talk_audio
end

function TalkCache.SetIsPlayingAudio(bo)
    TalkCache.is_play_audio = bo
end

function TalkCache.GetIsPlayingAudio()
    return TalkCache.is_play_audio
end

function TalkCache.StopCurIndexAudio()
    if TalkCache.GetNpcTalkAudio() then
        --print_error("TalkCache > StopCurIndexAudio")
        AudioManager.StopAudio(TalkCache.npc_talk_audio)
        TalkCache.SetIsPlayingAudio(false)
        TalkCache.SetNpcTalkAudio(nil)
        TalkCache.talk_audio_list = {}
    end
end

-- 播放NPC任务对话音效
function TalkCache.PlayNpcTalkAudio(talk_audio, subsection, prof)
    if not talk_audio then
        return
    end

    local bundle, asset = ResPath.GetNpcTalkVoiceRes(talk_audio, subsection, prof)
    if (TalkCache.is_play_audio or TalkCache.is_play_guide_audio) and TalkCache.cur_audio_time > Status.NowTime then
        if asset ~= TalkCache.cur_task_asset then
            table.insert(TalkCache.talk_audio_list, {talk_audio = talk_audio, subsection = subsection, prof = prof, time = Status.NowTime + 30})
        end
        return
    end

    if bundle and asset then
        TalkCache.cur_task_asset = asset
        TalkCache.StopCurIndexAudio()
        TalkCache.SetIsPlayingAudio(true)
        TalkCache.cur_audio_time = Status.NowTime + 30
        AudioManager.PlayAndForget(bundle, asset, nil, nil,
       function (call_back_audio, asset_name)
           TalkCache.SetNpcTalkAudio(call_back_audio)
       end,
       function (call_back_audio, asset_name)
            TalkCache.SetNpcTalkAudio(nil)
            TalkCache.SetIsPlayingAudio(false)
            local audio_info = TalkCache.GetNextAudioInfo(TalkCache.talk_audio_list)
            if audio_info then
                 TalkCache.PlayNpcTalkAudio(audio_info.talk_audio, audio_info.subsection, audio_info.prof)
            end
       end)
    end
end

-- 播放引导对话音效
function TalkCache.PlayGuideTalkAudio(bundle, asset)
    if not bundle or not asset then
        return
    end

    if TalkCache.is_play_guide_audio and TalkCache.cur_audio_time > Status.NowTime then
        if  TalkCache.cur_guide_asset ~= asset then
            table.insert(TalkCache.talk_guide_audio_list, {bundle = bundle, asset = asset, time = Status.NowTime + 30})
        end
        return
    end

    TalkCache.StopCurIndexAudio()
    TalkCache.is_play_guide_audio = true
    TalkCache.cur_audio_time = Status.NowTime + 30
    TalkCache.cur_guide_asset = asset
    AudioManager.PlayAndForget(bundle, asset, nil, nil,
    function (call_back_audio, asset_name)

    end,
    function (call_back_audio, asset_name)
       TalkCache.is_play_guide_audio = false
       local audio_info = TalkCache.GetNextAudioInfo(TalkCache.talk_guide_audio_list)
       if audio_info then
            TalkCache.PlayGuideTalkAudio(audio_info.bundle, audio_info.asset)
       else
            audio_info = TalkCache.GetNextAudioInfo(TalkCache.talk_audio_list)
            if audio_info then
                 TalkCache.PlayNpcTalkAudio(audio_info.talk_audio, audio_info.subsection, audio_info.prof)
            end
       end
    end)
end

-- 播放引导对话音效
function TalkCache.GetNextAudioInfo(list)
   if list[1] then
        local info = table.remove(list, 1)
        if info.time > Status.NowTime then
            return info
        else
            return TalkCache.GetNextAudioInfo(list)
        end
   else
        return nil
    end
end
