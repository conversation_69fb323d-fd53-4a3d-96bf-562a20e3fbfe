require("game/boss_assist/boss_assist_wg_data")
require("game/boss_assist/boss_assist_view")
require("game/boss_assist/boss_assist_info_view")
require("game/boss_assist/boss_assist_reward_view")
require("game/boss_assist/boss_assist_thanks_view")
require("game/boss_assist/boss_assist_finish")
require("game/boss_assist/boss_assist_list")
require("game/boss_assist/kanjia_assist")
require("game/boss_assist/boss_assist_help_tip")
require("game/boss_assist/boss_normal_hurt_view")
require("game/boss_assist/boss_down_tip_view")
--require("game/boss_assist/boss_get_energy_view")
require("game/boss/bosspanel/boss_shenyuan_box_reward")
require("game/boss_assist/boss_xianli_invate_tip")
require("game/boss/boss_xianli_quick_use")
require("game/boss_assist/boss_assist_dead_efficiency_view")

-- boss协助  boss体力相关
BossAssistWGCtrl = BossAssistWGCtrl or BaseClass(BaseWGCtrl)

function BossAssistWGCtrl:__init()
	if BossAssistWGCtrl.Instance then
		error("[BossAssistWGCtrl]:Attempt to create singleton twice!")
	end
	BossAssistWGCtrl.Instance = self

	self.data = BossAssistWGData.New()
	self.view = BossAssistView.New(GuideModuleName.BossAssist)
	self.reward_view = BossAssistRewardView.New()
	self.thinks_view = BossAssistThanksView.New()
	self.finish_view = BossAssistFinishView.New()
	self.info_view = BossAssistInfoView.New()
	self.list_view = BossAssistListView.New()
	self.kanjia_tip_view = KanJiaTipView.New()
	self.assist_help_tip = BossAssistHelpTip.New(GuideModuleName.BossAssistHelp)
    self.normal_hurt_view = BossNormalHurtView.New()
    self.shenyuan_box_reward = ShenyuanBossBoxReward.New()

    self.boss_down_tip_view = BossDownTipView.New() --体力不足的显示
    --self.boss_get_energy_view = BossGetEnergyView.New() --领取体力

    self.boss_xianli_invatetip = BossXianliInvateTip.New() --boss仙力提示增加
    self.boss_xianli_quick_use = BossXianliQuickUse.New()

	self.boss_dead_efficiency = BossDeadEfficiencyView.New()

	self:RegisterAllProtocals()
    self.xianli_protocol_received = false
    self.boss_assist_kanjia_view_init = false
end

function BossAssistWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.info_view:DeleteMe()
	self.info_view = nil

	self.reward_view:DeleteMe()
	self.reward_view = nil

	self.thinks_view:DeleteMe()
	self.thinks_view = nil

	self.finish_view:DeleteMe()
	self.finish_view = nil

	self.list_view:DeleteMe()
	self.list_view = nil

	self.kanjia_tip_view:DeleteMe()
	self.kanjia_tip_view = nil

	self.assist_help_tip:DeleteMe()
	self.assist_help_tip = nil

	self.normal_hurt_view:DeleteMe()
	self.normal_hurt_view = nil

	if self.boss_xianli_invatetip then
		self.boss_xianli_invatetip:DeleteMe()
		self.boss_xianli_invatetip = nil
    end
    
    if self.team_invite_alert then
		self.team_invite_alert:DeleteMe()
		self.team_invite_alert = nil
	end
    
	if self.fight_boss_alert then
		self.fight_boss_alert:DeleteMe()
		self.fight_boss_alert = nil
    end

    if self.boss_xianli_quick_use then
		self.boss_xianli_quick_use:DeleteMe()
		self.boss_xianli_quick_use = nil
    end

    if self.shenyuan_box_reward then
		self.shenyuan_box_reward:DeleteMe()
		self.shenyuan_box_reward = nil
    end
    
	if self.boss_down_tip_view then
		self.boss_down_tip_view:DeleteMe()
		self.boss_down_tip_view = nil
	end

	-- if self.boss_get_energy_view then
	-- 	self.boss_get_energy_view:DeleteMe()
	-- 	self.boss_get_energy_view = nil
	-- end

	if self.boss_dead_efficiency then
		self.boss_dead_efficiency:DeleteMe()
		self.boss_dead_efficiency = nil
	end

	if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
    end

    if self.time_delay_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.time_delay_timer)
        self.time_delay_timer = nil
    end
    
	BossAssistWGCtrl.Instance = nil
	self.boss_assist_kanjia_view_init = false
end

function BossAssistWGCtrl:RegisterAllProtocals()
	--self:RegisterProtocol(SCGuildAssistRoleInfo, "OnGuildAssistRoleInfo")
	self:RegisterProtocol(SCGuildAssistCallHelpListInfo, "OnGuildAssistCallHelpListInfo")
	--self:RegisterProtocol(SCGuildAssistBossDamageListInfo, "OnGuildAssistBossDamageListInfo") --世界boss 魔王巢穴（vip) 鸿蒙 
	--self:RegisterProtocol(SCGuildAssistRoleAssistDamageInfo, "OnGuildAssistRoleAssistDamageInfo")
	self:RegisterProtocol(SCGuildAssisBossRewardInfo, "OnGuildAssisBossRewardInfo")
	self:RegisterProtocol(SCWWorldBossRewardInfo, "OnWWorldBossRewardInfo")
	self:RegisterProtocol(SCGuildAssistThankRoleListInfo, "OnGuildAssistThankRoleListInfo")
	-- self:RegisterProtocol(SCGuildAssisThankLattertInfo, "OnGuildAssisThankLattertInfo") -- 梓刚说后端已经屏蔽了

	--self:RegisterProtocol(SCGuildAssisTeammateInviteInfo, "OnSCGuildAssisTeammateInviteInfo")
	self:RegisterProtocol(SCGuildAssisTeammateInviteClear, "OnSCGuildAssisTeammateInviteClear")
	self:RegisterProtocol(SCBossHurtInfo, "OnSCBossHurtInfo")  --神魔禁地（打宝）和上古遗迹  世界boss 魔王巢穴（vip) 鸿蒙 寻宝boss

	self:RegisterProtocol(SCKanJiaItemInfo, "SCKanJiaItemInfo")
	self:RegisterProtocol(SCAllKanJiaInfo, "SCAllKanJiaInfo")
	self:RegisterProtocol(SCRoleAllKanJiaInfo, "SCRoleAllKanJiaInfo")
	self:RegisterProtocol(SCRoleKanJiaItemInfo, "SCRoleKanJiaItemInfo")
	self:RegisterProtocol(SCRoleKanJiaDel, "SCRoleKanJiaDel")

	self:RegisterProtocol(SCWorldBossDropInfo, "OnSCWorldBossDropInfo")

	self:RegisterProtocol(SCBeginBeAttackByRole, "OnSCBeginBeAttackByRole")

    self:RegisterProtocol(SCVipBossXianLiInfo, "OnSCVipBossXianLiInfo")

	self:RegisterProtocol(SCMonsterHpRecordNotice, "OnSCMonsterHpRecordNotice")

	self:RegisterProtocol(CSRoleOpKanJiaItem)
	self.mainui_open_comlete = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

end

function BossAssistWGCtrl:Open(tab_index, param_t)
	tab_index = tab_index or self:GetOpenTabIndex()
	self.view:Open(tab_index)
end

function BossAssistWGCtrl:OpenShenyuanBoxReward(list)
    self.shenyuan_box_reward:SetRewardList(list)
    self.shenyuan_box_reward:Open()
end

function BossAssistWGCtrl:GetOpenTabIndex()
	if self.data:IsShowKanJiaRedPoint() == 1 then
		return TabIndex.kajia_xiezhu
	else
		return TabIndex.boss_xiezhu
	end
end

function BossAssistWGCtrl:MainuiOpenCreate()
	BossAssistWGCtrl.SendGuildAssistRoleInfoReq()
	BossAssistWGCtrl.SendGuildAssistCallHelpInfoReq()
end

-- 仙盟协助-请求个人信息
function BossAssistWGCtrl.SendGuildAssistRoleInfoReq()
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.INFO_REQ)
end

-- 仙盟协助-请求协助
function BossAssistWGCtrl.SendGuildAssistCallHelp()
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.CALL_HELP)
end

-- 仙盟协助-前往协助
function BossAssistWGCtrl.SendGuildAssistGoToHelp(role_id, scene_id, boss_id)
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.GO_TO_HELP, role_id, scene_id, boss_id)
end

-- 仙盟协助-取消协助
function BossAssistWGCtrl.SendGuildAssistCancelHelp()
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.CANCEL_HELP)
end

-- 仙盟协助-感谢别人
function BossAssistWGCtrl.SendGuildAssistThankOthers(dec_id, scene_id, boss_id)
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.THANK_OTHERS, dec_id, scene_id, boss_id)
end

-- 仙盟协助-领取感谢
function BossAssistWGCtrl.SendGuildAssistGetThankReward(role_id, scene_id, boss_id)
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.GET_THANK_REWARD, role_id, scene_id, boss_id)
end

-- 仙盟协助-请求协助呼叫信息列表
function BossAssistWGCtrl.SendGuildAssistCallHelpInfoReq()
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.CALL_HELP_INFO_REQ)
end

-- 仙盟协助-请求协助呼叫信息列表
function BossAssistWGCtrl.SendGuildAssistForceAttackReq(role_id, scene_id, boss_id)
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.FORCE_ATTACK, role_id, scene_id, boss_id)
end

-- 组队邀请--邀请前往
function BossAssistWGCtrl.SendInviteTeamGoto()
	BossAssistWGCtrl.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.INVITE_TEAMMATE)
end


-- 仙盟协助-请求操作
function BossAssistWGCtrl.SendGuildAssistOperateReq(operate_type, param_1, param_2, param_3)
	--print_error("assist", operate_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildAssistOperateReq)
	protocol.operate_type = operate_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end


-- 仙盟协助-个人信息-4471
function BossAssistWGCtrl:OnGuildAssistRoleInfo(protocol)
	if protocol.target_boss_id > 0 then
		local cfg = BossWGData.Instance:GetBossAllInfoByBossId(protocol.target_boss_id)
		local layer = cfg.layer or 0
		BossWGData.Instance:SetCurSelectBossID(0, layer, protocol.target_boss_id)
	end
	local old_assist_role_id = self.data.assist_info.assist_role_id
	self.data:SetGuildAssistRoleInfo(protocol)
	if protocol.owner_damage_to_boss > 0 then
        if not self.info_view:IsOpen() and BossWGData.IsBossScene(Scene.Instance:GetSceneType()) then      
			--self.info_view:Open()
		end
		self.list_view:Flush()
		GlobalEventSystem:Fire(OtherEventType.GUILD_ASSIST, true)
	else
		if self.info_view:IsOpen() then
            self.info_view:Close()
		end
		GlobalEventSystem:Fire(OtherEventType.GUILD_ASSIST, false)
	end
	if self.data:ShowAssistPanel() then
		if not self.list_view:IsOpen() then
			self.list_view:Open()
		end
		self.info_view:Flush()
	else
		if self.list_view:IsOpen() then
			self.list_view:Close()
		end
	end
	if protocol.assist_role_id > 0 then
		if  FuBenPanelCountDown.Instance:IsOpen() then
			FuBenPanelCountDown.Instance:Close()
		end
		if old_assist_role_id ~= protocol.assist_role_id and Scene.Instance:GetSceneId() == protocol.target_boss_scene_id then
			BossWGCtrl.Instance:InitGoToPos()
		end
	elseif old_assist_role_id ~= protocol.assist_role_id then
		if Scene.Instance:GetSceneType() == SceneType.WorldBoss then
			local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
			local wb_times_info = BossWGData.Instance:GetWorldBossEnterTimeInfo()
			local has_times = wb_other_cfg.world_boss_day_default_times + wb_times_info.world_boss_day_extra_kill_times + wb_times_info.world_boss_flush_can_kill_times
							- wb_times_info.world_boss_day_kill_times
			if has_times < 1 and not BossAssistWGData.Instance:IsAssist() and not FuBenPanelCountDown.Instance:IsOpen()  then
				FuBenPanelCountDown.Instance:SetTimerInfo(TimeWGCtrl.Instance:GetServerTime() + 30, function ()
					FuBenWGCtrl.Instance:SendLeaveFB()
				end)
			end
		elseif Scene.Instance:GetSceneType() == SceneType.VIP_BOSS then
			local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
			local hb_times_info = BossWGData.Instance:GetBossHomeEnterTimeInfo()
			local has_times = wb_other_cfg.boss_home_day_default_times + hb_times_info.boss_home_day_item_add_kill_times + hb_times_info.boss_home_day_vip_buy_kill_times
							- hb_times_info.boss_home_day_kill_times
			--if has_times < 1 and not BossAssistWGData.Instance:IsAssist() and not FuBenPanelCountDown.Instance:IsOpen()  then
			--	FuBenPanelCountDown.Instance:SetTimerInfo(TimeWGCtrl.Instance:GetServerTime() + 30, function ()
			--		FuBenWGCtrl.Instance:SendLeaveFB()
			--	end)
			--end
		end
	end
	GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
end

function BossAssistWGCtrl:FlushKanJia()
	if self.view:IsOpen() then
		self.view:KanJiaFlush()
	end
end

-- 仙盟协助-求助列表
function BossAssistWGCtrl:OnGuildAssistCallHelpListInfo(protocol)
	self.data:SetGuildAssistCallHelpListInfo(protocol)
	self.view:Flush()
	MainuiWGCtrl.Instance:FlushView(0, "call_help")
end

-- 仙盟协助-伤害列表
function BossAssistWGCtrl:OnGuildAssistBossDamageListInfo(protocol)
	--print_error("OnGuildAssistBossDamageListInfo 4473", protocol)
    self.data:SetGuildAssistBossDamageListInfo(protocol)
    local boss_id = protocol.target_boss_id
    if Scene.Instance:GetSceneType() == SceneType.VIP_BOSS or Scene.Instance:GetSceneType() == SceneType.WorldBoss then
        if boss_id ~= 0 then
            local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_id)
            --魔王巢穴和世界boss
            if boss_cfg then --归属类型为7，显示伤害第一
                if boss_cfg.drop_type and boss_cfg.drop_type == BOSS_DROP_OWN_TYPE.BOSS_DROP_OWN_TYPE_HURT_RANK and  not IsEmptyTable(protocol.role_damage_info_list)then
                    local name = protocol.role_damage_info_list[1].role_name
                    if SocietyWGData.Instance:GetIsInTeam() == 1 then
                        name = name .. Language.BossAssist.TeamFlag
                    end
                    GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION, name, Language.Common.AscriptionTitle2)
                end
            end
        end
    end
	self.info_view:Flush()
end

function BossAssistWGCtrl:CloseGuildAssistBossDamageListInfo()
	self.info_view:Close()
end

function BossAssistWGCtrl:ClearGuildAssistBossDamageListInfo()
	self.data:ClearBossAssistInfo()
	self.info_view:Close()
	self.list_view:Close()
end

-- 仙盟协助-协助者伤害列表
function BossAssistWGCtrl:OnGuildAssistRoleAssistDamageInfo(protocol)
	local old_list = self.data:GetGuildAssistRoleAssistDamageList()
	local check_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	if check_role_id ~= nil and old_list ~= nil then
		local check_flag = false
		for k,v in pairs(old_list) do
			if v.role_id == check_role_id then
				check_flag = true
				break
			end
		end

		if not check_flag then
			for k,v in pairs(protocol.role_damage_info_list) do
				if v.role_id == check_role_id then
					MainuiWGCtrl.Instance:StopAutoRevenge(true, Language.Fight.RevengeObjAlly, true)				
					break
				end
			end
		end	
	end	

	self.data:SetGuildAssistRoleAssistDamageInfo(protocol)
	self.list_view:Flush()
end

-- 仙盟协助-击杀Boss奖励展示
function BossAssistWGCtrl:OnGuildAssisBossRewardInfo(protocol)
    --print_error("old", protocol)
	self.data:SetGuildAssisBossRewardInfo(protocol)
	self.finish_view:Open()
end

-- 新手为世界魔王奖励
function BossAssistWGCtrl:OnWWorldBossRewardInfo(protocol)
	self.data:SetGuildAssisBossRewardInfo(protocol)
	--self.finish_view:Open()
end

--boss场景里面角色受到伤害
function BossAssistWGCtrl:OnSCBeginBeAttackByRole(protocol)
	self.data:SetBeAttackInfo(protocol)
    --刷新界面
    if Scene.Instance:GetSceneType() == SceneType.Shenyuan_boss then
        BossWGCtrl.Instance:FlushShenYuanHurtView()
    else
        self.info_view:Flush()
    end
end

-- 世界boss掉落返回
function BossAssistWGCtrl:OnSCWorldBossDropInfo(protocol)
    --print_error("drop", protocol.drop_count)
	self.data:SetWorldBossDropInfo(protocol)
	--self.finish_view:Open()
end

-- 仙盟协助-感谢协助者列表
function BossAssistWGCtrl:OnGuildAssistThankRoleListInfo(protocol)
	self.data:SetGuildAssistThankRoleListInfo(protocol)
	self.thinks_view:Open()
end

-- 仙盟协助-收到的感谢信
function BossAssistWGCtrl:OnGuildAssisThankLattertInfo(protocol)
	self.data:SetGuildAssisThankLattertInfo(protocol)
	self.reward_view:Open()
end

-- function BossAssistWGCtrl:OnSCGuildAssisTeammateInviteInfo(protocol)
-- 	--print_error("invite", protocol)
-- 	if protocol.reason == GUILD_ASSIST_REASON_TYPE.NORMAL then
-- 		self:HandleInviteInfo(protocol)
-- 	else
-- 		self:HandleFightInfo(protocol)
-- 	end
-- end

-- function BossAssistWGCtrl:HandleInviteInfo(protocol)
-- 	if not self.team_invite_alert then
-- 		self.team_invite_alert = Alert.New()
-- 		self.team_invite_alert:SetCancelString(Language.Guild.JUJUE)
-- 		self.team_invite_alert:SetCheckBoxText(Language.BossAssist.NoInvite)
-- 		self.team_invite_alert:SetShowCheckBox(true)
-- 		self.team_invite_alert:SetCancelFunc(function()
-- 			if self.team_invite_alert:GetIsNolongerTips() then
-- 				--拒绝
-- 				NewTeamWGCtrl.Instance:SendNoLongerOperateReq(NO_LONGER_TYPE.NO_LONGER_TYPE_ACCEPT_ASSIST, protocol.role_id)
-- 			end
-- 		end)
-- 	end

-- 	self.team_invite_alert:SetOkFunc(function()
-- 		self.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.GOTO_HELP_TEAMMATE, protocol.role_id,
-- 			protocol.target_boss_scene_id, protocol.target_boss_id)
-- 	end)
-- 	self.team_invite_alert:SetCheckBoxDefaultSelect(false)
-- 	self.team_invite_alert:ClearCheckHook()
-- 	local map_name = Config_scenelist[protocol.target_boss_scene_id].name
-- 	local boss_cfg = BossWGData.Instance:GetMonsterInfo(protocol.target_boss_id)
-- 	local str = string.format(Language.BossAssist.TeamInvite, protocol.role_name, map_name, boss_cfg.name)
-- 	self.team_invite_alert:SetLableString(str)

-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE_GOTO, 1, function()
-- 		self.team_invite_alert:Open()
-- 		--return true
-- 	end)
-- end

-- function BossAssistWGCtrl:HandleFightInfo(protocol)
-- 	if not BossWGData.Instance:CheckCanShowGuildHelpInfo(protocol.target_boss_scene_id) then
-- 		return
-- 	end

-- 	if not self.fight_boss_alert then
-- 		self.fight_boss_alert = Alert.New()
-- 		self.fight_boss_alert:SetCancelString(Language.Guild.JUJUE)
-- 		self.fight_boss_alert:SetOkString(Language.BossAssist.Goto)
-- 		self.fight_boss_alert:SetCancelFunc(BindTool.Bind(self.ClearTeamInviteInfo, self))
-- 	end
-- 	self.fight_boss_alert:SetCloseFunc(BindTool.Bind(self.SetTeamInviteTip, self, true))
-- 	self.fight_boss_alert:SetOkFunc(function()
-- 		local can_goto, str = self:CheckCanGotoTeamInvite(protocol.target_boss_scene_id)
-- 		if not can_goto and str then
-- 			SysMsgWGCtrl.Instance:ErrorRemind(str)
-- 			return
-- 		end

-- 		BossWGData.Instance:SetCurSelectBossID(0, 0, GameEnum.TeamInvite, SELECT_BOSS_REASON.ASSIST)
-- 		self.SendGuildAssistOperateReq(GUILD_ASSIST_OPERATE_TYPE.GOTO_HELP_TEAMMATE, protocol.role_id,
-- 			protocol.target_boss_scene_id, protocol.target_boss_id)
-- 	end)

-- 	local map = Config_scenelist[protocol.target_boss_scene_id]
-- 	map = map and map.name or ""

-- 	local boss_cfg = BossWGData.Instance:GetMonsterInfo(protocol.target_boss_id)
-- 	if boss_cfg then
-- 		boss_cfg = string.format(Language.BossAssist.TeamBossInfo, boss_cfg.boss_jieshu or 0, boss_cfg.name or "", boss_cfg.level or 0)
-- 	else
-- 		boss_cfg = ""
-- 	end

-- 	local str = string.format(Language.BossAssist.TeamAlertStr, protocol.role_name, map, boss_cfg)
-- 	self.fight_boss_alert:SetLableString(str)

-- 	local main_role = Scene.Instance:GetMainRole()
-- 	local is_rest = main_role:CheckIsRestState() and self:CheckCanGotoTeamInvite(protocol.target_boss_scene_id)

-- 	if is_rest and protocol.is_idle == 1 then
-- 		self.fight_boss_alert:Open()
-- 	else
-- 		self:SetTeamInviteTip(true)
-- 	end
-- end

function BossAssistWGCtrl:SetTeamInviteTip(state)
	local num = state and 1 or 0
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE_ALERT, num, function()
		self.fight_boss_alert:Open()
	end)
end

--清除组队邀请提示
function BossAssistWGCtrl:OnSCGuildAssisTeammateInviteClear(protocol)
	--print_error("clear", protocol)
	self:ClearTeamInviteInfo()
end

--清除组队邀请提示
function BossAssistWGCtrl:ClearTeamInviteInfo()
	self:SetTeamInviteTip(false)
	if self.fight_boss_alert and self.fight_boss_alert:IsOpen() then
		self.fight_boss_alert:SetCloseFunc(nil)
		self.fight_boss_alert:Close()
	end
end

function BossAssistWGCtrl:CheckCanGotoTeamInvite(target_scene_id)
	if Scene.Instance:GetSceneId() ~= target_scene_id and Scene.Instance:GetSceneType() ~= SceneType.Common then
		local cur_scene_cfg = Scene.Instance:GetSceneConfig()
		return false, string.format(Language.Common.CanNotEnterOther, cur_scene_cfg.name)
	end

	if YunbiaoWGData.Instance:GetIsHuShong() then
		return false, Language.YunBiao.CanNotDO
	end

	if MarryWGData.Instance:GetOwnIsXunyou() then
		return false, Language.Common.CanNotMoveInXunYou
	end

	if CgManager.Instance:IsCgIng() then
		return false, nil
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:IsQingGong() or main_role:IsMitsurugi() then
		return false, nil
	end

	if main_role:IsFightStateByRole() then
		return false, Language.Fight.FightDesc01
	end

	return true
end
-------------------------------------------------------------------------------------------
function BossAssistWGCtrl:OnSCBossHurtInfo(protocol)
	self.data:SetHurtInfo(protocol)
	
	if protocol and protocol.monster_id and TreasureBossWGData.Instance:IsTreasureBoss(protocol.monster_id) then
		WorldServerWGCtrl.Instance:FlushTreasureHurtBossInfoView()
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_DIVINE_DOMAIN then
		HolyHeavenlyDomainWGCtrl.Instance:OnHolyHeavenlyDomainFBBossHurtInfo(protocol.info_count > 0)
		return
	elseif scene_type == SceneType.CrossLongMai then
		CrossLongMaiWGCtrl.Instance:OnBossHurtInfo(protocol.info_count > 0)
		return
	elseif scene_type == SceneType.CROSS_LAND_WAR then
		PositionalWarfareWGCtrl.Instance:OnBossHurtInfo(protocol)
		return
	elseif scene_type == SceneType.SCENE_TYPE_LAND_WAR_FB then
		LandWarFbPersonWGCtrl.Instance:OnBossHurtInfo(protocol)
	end

	if protocol.info_count > 0 then
		if not self.normal_hurt_view:IsOpen() then
            self.normal_hurt_view:Open()
        end
        self.normal_hurt_view:Flush()
        local boss_id = protocol.monster_id
        MainuiWGCtrl.Instance:SetLightBossId(boss_id)
        MainuiWGCtrl.Instance:FlushView(0, "xianli_info")
        BossXiezhuWGCtrl.Instance:OpenBossXiezhuInfoView()
        if not self.boss_down_tip_view:IsOpen() then
            local cur_is_enough, is_vip_boss = self.data:JudgeIsEnoughBoss(boss_id)
            if is_vip_boss then
                -- local last_boss_id, is_enough = self.boss_down_tip_view:GetCurBossAndState()
                if not cur_is_enough then
                    self:JudgeCanOpenXianliTipView(boss_id)
                end
                -- if last_boss_id then
                --     if is_enough == cur_is_enough then --伤害没清零 状态相同 说明有可能是召唤出来的
                --     else
                --         self:OpenDownTipView(boss_id)
                --     end
                -- else --伤害清零过，说明第一次去打，需要显示
                --     self:OpenDownTipView(boss_id)
                -- end
            end
        end
	else
		if self.normal_hurt_view:IsOpen() then
            self.normal_hurt_view:Close()
        end

        if self.boss_down_tip_view:IsOpen() then
            self:CloseDownTipView()
        end

        BossXiezhuWGCtrl.Instance:CloseBossXiezhuInfoView()
        self.boss_down_tip_view:ClearBossId()
	end
end

function BossAssistWGCtrl:ClearNormalHurtList()
	self.data:ClearNormalHurtList()
	if self.normal_hurt_view:IsOpen() then
        self.normal_hurt_view:Close()
    end
end

function BossAssistWGCtrl:OpenNormalHurtList()
	if not self.normal_hurt_view:IsOpen() then
        self.normal_hurt_view:Open()
    end
    -- local mainui_ctrl = MainuiWGCtrl.Instance
	-- local parent = mainui_ctrl:GetTaskOtherContent()
	-- if parent and not parent:GetActive() then
	--     if self.normal_hurt_view.Init and self.normal_hurt_view:IsLoaded() then
	--     	self.normal_hurt_view:Init()
	--     end
	-- end
    self.normal_hurt_view:Flush()
end

function BossAssistWGCtrl:FlushNormalHurtXiezhu()
	if self.normal_hurt_view:IsOpen() and self.normal_hurt_view:IsLoaded() then
        self.normal_hurt_view:FlushXieZhuInfo()
    end
end

-------------------------------------------------------------------------------------------

-- 砍价协助信息返回
function BossAssistWGCtrl:SCKanJiaItemInfo(protocol)
	self.data:SetKanJiaInfo(protocol)
	if self.view:IsOpen() then
		self.view:KanJiaFlush()
	end

	if protocol.KanjianItemInfo.info.is_help_succ == 1 then
		self.kanjia_tip_view:Open()
		self.kanjia_tip_view:Flush(0, "ser_info", {info = protocol.KanjianItemInfo.info})
	end

	QuanMinBeiZhanWGCtrl.Instance:FlushKanJia()
end

-- 砍价协助列表信息返回
function BossAssistWGCtrl:SCAllKanJiaInfo(protocol)
	self.data:SetKanJiaListInfo(protocol)
	if self.view:IsOpen() then
		self.view:KanJiaFlush()
	end
end

-- 移除砍价协助信息
function BossAssistWGCtrl:SCDelKanJia(protocol)
	self.data:RemoveKanJiaInfo(protocol)
	if self.view:IsOpen() then
		self.view:KanJiaFlush()
	end
end

--玩家砍价礼包列表
function BossAssistWGCtrl:SCRoleAllKanJiaInfo(protocol)
	RoleWGData.Instance:SetKanJiaGiftList(protocol.gift_list)
	RoleWGData.Instance:SetTodayKanJiaCount(protocol.today_kanjia_cnt)
end

--玩家砍价礼包
function BossAssistWGCtrl:SCRoleKanJiaItemInfo(protocol)
	RoleWGData.Instance:SetTodayKanJiaCount(protocol.today_kanjia_cnt)
	RoleWGData.Instance:SetKanJiaGiftItem(protocol.gift_id, protocol.items_cnt)
	QuanMinBeiZhanWGCtrl.Instance:FlushKanJia()
end

--移除玩家砍价数据
function BossAssistWGCtrl:SCRoleKanJiaDel(protocol)
	RoleWGData.Instance:SetKanJiaGiftItem(protocol.gift_id, 0)
	QuanMinBeiZhanWGCtrl.Instance:FlushKanJia()
end

function BossAssistWGCtrl:CSRoleOpKanJiaItem(opera_type, param1, param2, param3, param4)
 	--print_error("-----------CSRoleOpKanJiaItem---------  opera_type = ",opera_type, "  param1 = ",param1, "  param2 = ",param2, "  param3 = ",param3, "  param4 = ",param4)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleOpKanJiaItem)
    protocol.op_type = opera_type
    protocol.kanjia_item_id = param1 or 0
    protocol.owner_uid = param2 or 0
    protocol.price_type = param3 or 0
    protocol.price = param4 or 0
    protocol:EncodeAndSend()
end



-- boss 体力相关
function BossAssistWGCtrl:JudgeCanOpenXianliTipView(boss_id)
    --self.boss_xianli_invatetip:JudgeCanOpenView(boss_id)
end

function BossAssistWGCtrl:CloseXianliInvateTip()
    self.boss_xianli_invatetip:Close()
end

function BossAssistWGCtrl:ClearSceneInfo()
    if self.boss_xianli_invatetip then
        self.boss_xianli_invatetip:ClearScenId()
    end
end

function BossAssistWGCtrl:JudgeCloseXianliTipView()
    if self.boss_xianli_invatetip then
        self.boss_xianli_invatetip:JudgeCloseXianliTipView()
    end
end

function BossAssistWGCtrl:OpenDownTipView(boss_id)
    self.boss_down_tip_view:SetCurBoss(boss_id)
	self.boss_down_tip_view:Open()
end

function BossAssistWGCtrl:CloseDownTipView()
	self.boss_down_tip_view:Close()
end

function BossAssistWGCtrl:OpenGetEnergyView(is_auto)
    if is_auto then
        self.need_invate_open_view = false
    end
	-- QiFuWGCtrl.Instance:OpenQiFuView(TabIndex.qifu_getengrgy)
	--self.boss_get_energy_view:Open()
	ViewManager.Instance:Open(GuideModuleName.QIFU, TabIndex.qifu_getengrgy)
end

function BossAssistWGCtrl:FlushGetEnergyView()
	--self.boss_get_energy_view:Flush()
	QiFuWGCtrl.Instance:FlushQiFuGetEnergyView()
end

-- function BossAssistWGCtrl:CloseGetEnergyView()
-- 	self.boss_get_energy_view:Close()
-- end

function BossAssistWGCtrl:OpenQuickUseEnergyView()
	local is_show_lingli = BossWGData.Instance:GetIsShowLingLi()
	if is_show_lingli < 1 then
		return
	end
	
    local data = {}
    local xianli_potion, xianli_potion1 = BossWGData.Instance:GetBossXianliAddItem()
    data.consume_item = xianli_potion
    local cfg = ItemWGData.Instance:GetItemConfig(xianli_potion)
    data.desc = cfg.description
    
    local data1 = {}
    data1.consume_item1 = xianli_potion1
    local cfg1 = ItemWGData.Instance:GetItemConfig(xianli_potion1)
    data1.desc1 = cfg1.description
    self.boss_xianli_quick_use:SetData(data, data1)
end

function BossAssistWGCtrl:OnSCVipBossXianLiInfo(protocol)
    self.data:SaveBossXianli(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.Boss, TabIndex.boss_vip, "flush_xianli")
    self.normal_hurt_view:Flush()
    local boss_id, is_enough = self.boss_down_tip_view:GetCurBossAndState()
    if boss_id and not is_enough then --仙力从不足到充足，弹一次界面
        local cur_is_enough, is_vip_boss = self.data:JudgeIsEnoughBoss(boss_id)
        if cur_is_enough and is_vip_boss then
            --self.boss_down_tip_view:Open()
        end
    end
    if self.boss_xianli_invatetip:IsOpen() then
        self.boss_xianli_invatetip:Flush()
    end

    MainuiWGCtrl.Instance:FlushView(0, "xianli_info")
    self:SetMainViewInvateTip()
    local can_get = BossWGData.Instance:JudgeCanGetBossXianli()
    if not self.xianli_protocol_received then
        self.xianli_protocol_received = true
        if can_get then
            BossAssistWGCtrl.Instance:JudgeCanOpenGetEnergyView()
        end
    end
    self:JudgeNeedTimer()
    RemindManager.Instance:Fire(RemindName.Boss_Vip)
    RemindManager.Instance:Fire(RemindName.QiFuGetEnergy)
	self:FlushGetEnergyView()
	--QiFuWGCtrl.Instance:FlushQiFuGetEnergyView()
end

function BossAssistWGCtrl:OnSCMonsterHpRecordNotice(protocol)
	-- print_error("OnSCMonsterHpRecordNotice------", protocol)
	local target_obj = SceneObj.select_obj
	if nil == target_obj or target_obj:IsDeleted() or target_obj:GetType() == SceneObjType.MainRole then
		return
	end

	local need_boss_efficiency = BossWGData.Instance:CheackNeedBossDeadEfficiency(protocol.monster_id)

	if need_boss_efficiency then
		self.data:SetBossHpRecordInfo(protocol)
		if not self.boss_dead_efficiency:IsOpen() then
			self.boss_dead_efficiency:Open()
		end
		self.boss_dead_efficiency:Flush()
	else
		if self.boss_dead_efficiency:IsOpen() then
			self.boss_dead_efficiency:Close()
		end
	end
end

function BossAssistWGCtrl:SendGetXianli(seq)
    --BossAssistWGCtrl.Instance:SendGetXianli()
    BossWGCtrl.Instance:SendVipBossReq(BossView.ReqType.VIP_BOSS_RECEIVE_XIANLI, seq, 0, 0)
end

function BossAssistWGCtrl:JudgeCanOpenGetEnergyView()
    if self.xianli_protocol_received then
        local can_get = BossWGData.Instance:JudgeCanGetBossXianli()
        if not can_get then
            return
        end

        -- 普通场景才弹
        local scene_type = Scene.Instance:GetSceneType()
        if not BossAssistWGData.Instance:GetCanOpenViewScene(scene_type) then
            self.need_invate_open_view = true
            return
        end
        BossAssistWGCtrl.Instance:OpenGetEnergyView(true)
    end
end

function BossAssistWGCtrl:OnSceneChangeComplete()
	if self.need_invate_open_view and BossAssistWGData.Instance:GetCanOpenViewScene(Scene.Instance:GetSceneType()) then
        self.need_invate_open_view = false
        self:JudgeCanOpenGetEnergyView()
    end
    self:JudgeCloseXianliTipView()
end

function BossAssistWGCtrl:SetMainViewInvateTip(can_get)
    --print_log("仙力领取状态SetMainViewInvateTip can_get",can_get, " JudgeCanGetBossXianli", BossWGData.Instance:JudgeCanGetBossXianli())
    local can_get = can_get or BossWGData.Instance:JudgeCanGetBossXianli()
    if can_get then
        --print_log("提示有仙力可领取")
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLI_CANGET, 1, function ()
			self:OpenGetEnergyView()
			return true
		end)
    else
        --print_log("提示仙力按钮隐藏")
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLI_CANGET, 0)
	end
end

function BossAssistWGCtrl:JudgeNeedTimer()
    if self.time_delay_timer ~= nil then
        return
    end
    local boss_xianli_fetch_tb = BossWGData.Instance:GetVipXianliFetchTimeCfg()
    local lunch = boss_xianli_fetch_tb[1]
    local dinner = boss_xianli_fetch_tb[2]
    if IsEmptyTable(lunch) or IsEmptyTable(dinner) then
        return
    end
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local time_delay
    if server_time < lunch.start_time then
        time_delay = lunch.start_time - server_time + 1
    elseif lunch.start_time <= server_time and server_time <= lunch.end_time then
        time_delay = lunch.end_time - server_time + 1
    elseif server_time < dinner.start_time then
        time_delay = dinner.start_time - server_time + 1
    elseif dinner.start_time <= server_time and server_time <= dinner.end_time then
        time_delay = dinner.end_time - server_time + 1
    elseif dinner.end_time < server_time then --
    end
    if time_delay ~= nil then
		self.time_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.XianliFecthStateChange, self), time_delay)
	end
end

function BossAssistWGCtrl:XianliFecthStateChange()
    if self.time_delay_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.time_delay_timer)
        self.time_delay_timer = nil
	end

    local can_get = BossWGData.Instance:JudgeCanGetBossXianli()
    RemindManager.Instance:Fire(RemindName.Boss_Vip)
    self:SetMainViewInvateTip(can_get)
    ViewManager.Instance:FlushView(GuideModuleName.Boss, TabIndex.boss_vip, "flush_xianli")
    MainuiWGCtrl.Instance:FlushView(0, "xianli_info")
    self.normal_hurt_view:Flush()
    if can_get then
        BossAssistWGCtrl.Instance:JudgeCanOpenGetEnergyView()
    end
    self:JudgeNeedTimer()
end

-- VipBoss击杀次数
function BossAssistWGCtrl:VipBossKillTimesChange(value)
    self.data:SetVipBossKillTimes(value)
end