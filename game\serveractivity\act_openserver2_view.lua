-- 开服比拼 上标签
ActOpenServertwoView = ActOpenServertwoView or BaseClass(SafeBaseView)

local UPDATE_BTN_LIST_EVENT = OPENSERVER_ACTIVITY_COMPETITION.UPDATE_BTN_LIST
local REFRESH_BTN_LIST_EVENT = OPENSERVER_ACTIVITY_COMPETITION.REFRESH_BTN_LIST
local REFRESH_VIEW_EVENT = OPENSERVER_ACTIVITY_COMPETITION.REFRESH_VIEW

function ActOpenServertwoView:__init()
	self:AddViewResource(0, "uis/view/act_tab_view_prefab", "layout_open_activity_panel")
	self.btns_change_flag = false
	self.select_index = nil
	self.data_list_classify = {}
	self.classify = 1
	self.cur_subview = nil
	self.order = 2
end

function ActOpenServertwoView:__delete()
end

function ActOpenServertwoView:ReleaseCallBack()
	if self.cur_subview then
		self.cur_subview:DeleteMe()
	end

	self.cur_subview = nil

	if self.btn_select_list1 then
		self.btn_select_list1:DeleteMe()
		self.btn_select_list1 = nil
	end 
	self.close_view = nil
	self.select_index = nil
	self.cur_opentipday = nil
	self:UnbindGlobalEvent()
	self.data_list_classify = nil
end

function ActOpenServertwoView:LoadCallBack()
	self.cur_subview = OpenServerCompetitionView.New()
	self:BindGlobalEvent()
	ServerActivityWGData.Instance:AddActOpeningType(ACTIVITY_TYPE.OPEN_SERVER)

	-- 这里的select_index ,rush_type, 目前都是一致的(若不一致， 小心修改)
	local cur_index = ServerActivityWGData.Instance:OpenCompareActivity()
	if TimeWGCtrl.Instance:GetCurOpenServerDay() == 8 then 
		cur_index = 7 
	end
	if self.select_index == nil or self.select_index > cur_index then
		self.select_index = cur_index
	end
	local is_opensever_end = ServerActivityWGData.Instance:IsOpenSeverActivityEnd()
	self:CreateOpenServerBtnList()
	
	if self.btn_select_list1 then
		self.btn_select_list1:SetDefaultSelectIndex(self.select_index)
	end
end

function ActOpenServertwoView:UnbindGlobalEvent()
	if self.global_event_handles then
		for _,v in pairs(self.global_event_handles) do
			GlobalEventSystem:UnBind(v)
		end
		self.global_event_handles = nil
	end
end

function ActOpenServertwoView:BindGlobalEvent()
	self:UnbindGlobalEvent()
	local t = {}
	table.insert(t,GlobalEventSystem:Bind(REFRESH_VIEW_EVENT, BindTool.Bind1(self.RefreshView, self)))
	table.insert(t,GlobalEventSystem:Bind(UPDATE_BTN_LIST_EVENT, BindTool.Bind1(self.UpdateBtnList, self)))
	table.insert(t,GlobalEventSystem:Bind(REFRESH_BTN_LIST_EVENT, BindTool.Bind1(self.RefershBtnList, self)))
	self.global_event_handles = t
end


function ActOpenServertwoView:SelectHandler(index)
	self.select_index = index
end

function ActOpenServertwoView:GetOpenServerBtnIndex(btn_select_index)
	self.classify = btn_select_index
end


function ActOpenServertwoView:CreateOpenServerBtnList()
	local open_act_cfg = ServerActivityWGData.Instance:GetShowOpenSprintActList(true, true)
	local classify_count = #open_act_cfg
	self.data_list_classify = {} 
	local k = 0
	local rushtype_cfg
	for i=1,classify_count do
		if open_act_cfg[i].classify == self.classify then
			k = k + 1
			rushtype_cfg = ServerActivityWGData.Instance:GetOpenCompareActivityCfg(k)
			self.data_list_classify[k] = __TableCopy(open_act_cfg[i])
			self.data_list_classify[k].open_day = rushtype_cfg.open_day_index
			self.data_list_classify[k].close_day = rushtype_cfg.close_day_index
			self.data_list_classify[k].rush_type = rushtype_cfg.rush_type
		end
	end

	if 0 == #self.data_list_classify then
		self:Close()
		return
	end
	-- local next_act_name = ServerActivityWGData.Instance:GetNextOpenSprintActName()
	-- if next_act_name ~= nil then
	-- 	table.insert(self.data_list_classify, {reason = next_act_name})
	-- end

	if self.node_list.ph_btn_select_list == nil then return end
	if not self.btn_select_list1 then 
		self.btn_select_list1 = AsyncListView.New(OpenServerTwoTabBtnItem,self.node_list.ph_btn_select_list)
		self.btn_select_list1:SetSelectCallBack(BindTool.Bind1(self.OnOpenViewType, self))
		
		self.btn_select_list1.SelectIndex = function(btn_select_list1, index)
			if self:OpenTipsFlag(index) then
				AsyncListView.SelectIndex(btn_select_list1, index)
			end
		end

		self.btn_select_list1.ListEventCallback = function(btn_select_list1, item_cell)
			local flag = ServerActivityWGData.Instance:IsOpenSprintActTwo(item_cell.data.id)
			if flag or TimeWGCtrl.Instance:GetCurOpenServerDay() == 8 then
				AsyncListView.ListEventCallback(btn_select_list1, item_cell)
			end
			self:OpenTipsFlag(item_cell.index)
		end
	end

	self.btn_select_list1:SetDataList(self.data_list_classify,3)
end

function ActOpenServertwoView:OpenTipsFlag(index)
	if TimeWGCtrl.Instance:GetCurOpenServerDay() == 8 then
		return true
	end
	-- local item_cell
	-- if self.btn_select_list1 then
	-- 	item_cell = self.btn_select_list1:GetItemAt(index)
	-- end
	
	local data_list  = self.data_list_classify[index]
	if data_list == nil then
		return
	end
	local id = ACT_RUSH_TYPE[data_list.id]
	local data_cell = ServerActivityWGData.Instance:GetOpenCompareActivityCfg(id)
	local flag = ServerActivityWGData.Instance:IsOpenSprintAct(data_list.id)
	if flag == false  then
		if TimeWGCtrl.Instance:GetCurOpenServerDay() < data_cell.open_day_index then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpenServer.NextOpenSprintActName, data_cell.open_day_index))
			return false
		else
			-- SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.NextOverSprintActName)
			return true
		end
		
	end
	return true
end

function ActOpenServertwoView:ShowIndexCallBack(index)
	ServerActivityWGCtrl.Instance:SendOpenGameActivityInfoReq()
	self:RefreshView()
end

function ActOpenServertwoView:OpenCallBack()
	if self.cur_subview and not self.cur_subview:IsOpen() then 
		self.cur_subview:Open()
	end
end

function ActOpenServertwoView:CloseCallBack()
	ServerActivityWGData.Instance:CleanUpTwoFinishAct()
	ServerActivityWGData.Instance:CleanActCloseType()
	self.btns_change_flag = true

	if self.cur_subview then 
		self.cur_subview:Close()
	end
end


function ActOpenServertwoView:UpdateBtnList()
	if not self:IsOpen() then return end
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()   -- 开服天数
	if open_day > 8 and not self.close_view then 
		self.close_view = true
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.RollActivityEnd)
		self:Close()
		return
	end
	if self.btn_select_list1 and next(self.data_list_classify) and self.cur_opentipday ~= open_day then
		self.select_index = open_day
		if self.select_index == 0 then
			self.select_index = 1
		end
	 	self.btn_select_list1:SetDefaultSelectIndex(self.select_index)
		self.btn_select_list1:SetDataList(self.data_list_classify,3)    --刷新列表
		self.cur_opentipday = open_day
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.OpenServer.OpenDay,open_day))
	end
end

function ActOpenServertwoView:RefershBtnList()
	if self.btn_select_list1 and next(self.data_list_classify) then
		self.btn_select_list1:SetDataList(self.data_list_classify,3)    --刷新列表
	end
end

function ActOpenServertwoView:SetExtraParams(params)
	if type(params) == "table" then
		if params.rush_type then
			self.select_index = params.rush_type
		elseif tonumber(params.sel_index) then
			self.select_index = tonumber(params.sel_index)
		end 
	end
end

function ActOpenServertwoView:OnOpenViewType(item, index)
 	if nil == self.btn_select_list1 or nil == item then
		return
	end

	ServerActivityWGCtrl.Instance:SendOpenGameActivityInfoReq()
	-- local item = self.btn_select_list1:GetItemAt(self.btn_select_list1:GetSelectIndex())  
	-- if item == nil then return end
	local data = self.data_list_classify[self.btn_select_list1:GetSelectIndex()]
	if data == nil then
		return
	end
	local open_act_cfg = data
	local act_id = open_act_cfg.id
	self.select_index = index

	if self.cur_subview == nil then
		return
	end

	self.cur_subview:SetActId(act_id)
	if not self.cur_subview:IsOpen() then
		-- print_error("ActOpenServertwoView self.cur_subview:Open()")
		self.cur_subview:Open()
	end
	
	self.cur_subview:RefreshView({flush_param = "change_index"})

end

function ActOpenServertwoView:RefreshView(param_t)
	if nil ~= self.cur_subview then
		self.cur_subview:RefreshView(param_t)
	end
	self:RefershBtnList()
end

function ActOpenServertwoView:GetCurView()
	return self.cur_subview
end



-- region OpenServerTwoTabBtnItem
-- 开服冲榜 页签按钮
OpenServerTwoTabBtnItem = OpenServerTwoTabBtnItem or BaseClass(BaseRender)
function OpenServerTwoTabBtnItem:__init()
	self.activity_name = ""
	self.node_list.img_red.image.enabled = false
end

function OpenServerTwoTabBtnItem:__delete()
	self.activity_name = nil
end

function OpenServerTwoTabBtnItem:ChangeOpenFlag()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_open = ServerActivityWGData.Instance:IsOpenningSprintActTwo(self.data.id)
	self.node_list["open_flag"]:SetActive(true)
	local bundle,asset
	local act_open_day = self.data.open_day
	local act_close_day = self.data.close_day
	local rush_type = self.data.rush_type
	if is_open then
		local str = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
		-- 晚上11点关闭
		if str.hour >= 23 and act_close_day == open_day then
			bundle,asset = ResPath.GetOpenserverActiveIcon("has_open")
		else
			bundle,asset = ResPath.GetOpenserverActiveIcon("openning")
		end
	else 
		if act_close_day < open_day then --已经结束（还有未开启）
			bundle,asset = ResPath.GetOpenserverActiveIcon("has_open")
		end
		XUI.SetButtonEnabled(self.view, true)
	end
	self.activity_name = (self.data.act_name)
	if bundle then
		self.node_list["open_flag"]:SetActive(true)
		self.node_list["open_flag"].image:LoadSprite(bundle,asset)
	else
		self.node_list["open_flag"]:SetActive(false)
	end
	local show_red_point = false
	if act_open_day <= open_day then
		show_red_point = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(rush_type)
	end
	self.node_list.img_red.image.enabled = show_red_point
end

function OpenServerTwoTabBtnItem:OnFlush()
	if nil == self.data then return end
	-- if self.data.reason ~= nil then
	-- 	self:SetReason(self.data.reason)
	-- 	self.activity_name = (self.data.reason)
	-- 	self:OnSelectChange(false)
	-- 	return
	-- end

	-- self:ChangeOpenFlag()
	-- self:OnSelectChange(self:IsSelectIndex())
end

function OpenServerTwoTabBtnItem:OnSelectChange(is_select)
	if is_select then
		self.node_list.img_hl:SetActive(true)
	else
		self.node_list.img_hl:SetActive(false)
	end
	self.node_list.lbl_activity_name.text.text = self.activity_name
end

function OpenServerTwoTabBtnItem:SetReason(reason)
	self.reason = reason
end

function OpenServerTwoTabBtnItem:GetReason()
	return self.reason
end

-- endregion OpenServerTwoTabBtnItem