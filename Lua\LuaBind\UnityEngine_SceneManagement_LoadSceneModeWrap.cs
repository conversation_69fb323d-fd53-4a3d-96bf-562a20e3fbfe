﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_SceneManagement_LoadSceneModeWrap
{
	public static void Register(LuaState L)
	{
		L.BeginEnum(typeof(UnityEngine.SceneManagement.LoadSceneMode));
		<PERSON><PERSON>("Single", get_Single, null);
		<PERSON><PERSON>("Additive", get_Additive, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.<PERSON>();
		TypeTraits<UnityEngine.SceneManagement.LoadSceneMode>.Check = CheckType;
		StackTraits<UnityEngine.SceneManagement.LoadSceneMode>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.SceneManagement.LoadSceneMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.SceneManagement.LoadSceneMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Single(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.SceneManagement.LoadSceneMode.Single);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Additive(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.SceneManagement.LoadSceneMode.Additive);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.SceneManagement.LoadSceneMode o = (UnityEngine.SceneManagement.LoadSceneMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

