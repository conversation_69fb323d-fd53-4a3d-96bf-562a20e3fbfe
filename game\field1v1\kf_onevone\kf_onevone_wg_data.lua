KFOneVOneWGData = KFOneVOneWGData or BaseClass()

KFOneVOneWGData.MatchState = {
	None = 0,		--没开始
	YuXuan = 1,		--预选赛
	YuXuanEnd = 2, 	--预选赛结算
	TaoTai = 3,		--淘汰赛
}

KFOneVOneWGData.KnockoutState = {
	None = 0,			--没开始
	Index16To8 = 1,		--16进8
	Index8To4 = 2, 		--8进4
	Index4To2 = 3,		--4进2
	Index2To1 = 4,		--2进1
	WinnerEnd = 5,		--已决出冠军
}

KUAFUONEVONE_STATUS =
{
	AWAIT = 0,					-- 等待
	PREPARE = 1,				-- 进行中
	END = 2,					-- 当轮结束
}
KFOneVOneWGData.MAX_WINNER_COUNT = 3

ONEVONE_SLIDER = {
	[1] = 0.18,
	[2] = 0.38,
	[3] = 0.58,
	[4] = 0.8,
	[5] = 1,
}

function KFOneVOneWGData:__init()
	if KFOneVOneWGData.Instance then
		ErrorLog("[KFOneVOneWGData] attempt to create singleton twice!")
		return
	end
	KFOneVOneWGData.Instance =self

	self.onevone_match_state = KFOneVOneWGData.MatchState.None    		--跨服1v1阶段	
	self.onevone_knockout_state = KFOneVOneWGData.KnockoutState.None    	--淘汰赛轮次的阶段
	self.knockout_item_list = {}
	self.onevone_rank_list = {}
	self.onevone_rank_key_list = {}
	self.winner_list = {}
	self.my_jingji_knockout = 0			--我晋级的轮次

	self.config = ConfigManager.Instance:GetAutoConfig("cross_1v1_auto")
	self.other_cfg = self.config.other[1]
	self.score_reward_cfg = ListToMap(self.config.score_reward,"seq")
	self.rank_reward_cfg = ListToMap(self.config.rank_reward,"seq")
	self.guess_reward_cfg = ListToMap(self.config.guess_reward,"round")
	self.match_reward_cfg = ListToMap(self.config.match_reward,"match_type")
	self.match_count_reward_cfg = ListToMap(self.config.match_count_reward,"seq")

	RemindManager.Instance:Register(RemindName.ActOneVSOnebArena, BindTool.Bind(self.IsShowKFOneVOneRedPoint, self))  -- 跨服1v1红点
end

function KFOneVOneWGData:__delete()
	KFOneVOneWGData.Instance = nil
end

function KFOneVOneWGData:SetSCCross1V1MatchSucc(protocol)
	self.onevone_opponent_info = protocol.opponent_info
	-- local uuid = self.onevone_opponent_info.uuid
	-- local key = uuid.temp_low --.. uuid.temp_high
	-- local avatar_key_big = self.onevone_opponent_info.avatar_key_big
	-- local avatar_key_small = self.onevone_opponent_info.avatar_key_small
	-- AvatarManager.Instance:SetAvatarKey(key, avatar_key_big, avatar_key_small)
end

function KFOneVOneWGData:SetSCCross1V1KnockoutMatchInfo(protocol)
	self.knockout_item_list = protocol.knockout_item_list
	self.indexes_16_to_8 = protocol.indexes_16_to_8
	self.indexes_8_to_4 = protocol.indexes_8_to_4
	self.indexes_4_to_2 = protocol.indexes_4_to_2
	self.indexes_16_to_8_index = protocol.indexes_16_to_8_index
	self.indexes_8_to_4_index = protocol.indexes_8_to_4_index
	self.indexes_4_to_2_index = protocol.indexes_4_to_2_index
	self.indexes_winner = protocol.indexes_winner
	self.cur_opponent_index = protocol.cur_opponent_index	--淘汰赛当前对手的下标
	--自己所在淘汰列表的当前索引
	if self.my_knockout_index == nil then
		local uuid = RoleWGData.Instance:GetUUid()
		for k,v in pairs(self.knockout_item_list) do
			if v.uuid == uuid then
				self.my_knockout_index = v.index
			end
		end
		-- Field1v1WGCtrl.Instance:PlayJingJiTips(KFOneVOneWGData.MatchState.YuXuanEnd,KFOneVOneWGData.MatchState.TaoTai)
	end

	for k,v in pairs(self.knockout_item_list) do
		if v.name ~= "" then
			self.has_knockout_match_info = true
		end
	end
end

function KFOneVOneWGData:GetHasKnockoutMatchInfo()
	return self.has_knockout_match_info
end

function KFOneVOneWGData:SetSCross1V1ScoreRank(protocol)
	self.my_rank_index = protocol.my_rank_index + 1
	self.onevone_rank_list = protocol.rank_list
	self.onevone_rank_key_list = protocol.rank_key_list
end

function KFOneVOneWGData:SetSCross1V1PersonInfo(protocol)
	self.onevone_person_info = {}
	self.onevone_person_info.score = protocol.score	
	self.onevone_person_info.knockout_rank = protocol.knockout_rank
	self.onevone_person_info.knockout_guess_winner_flag = protocol.knockout_guess_winner_flag
	self.onevone_person_info.knockout_guess_reward_fetch_flag = protocol.knockout_guess_reward_fetch_flag
	self.onevone_person_info.knockout_guess_winner_flag_num = protocol.knockout_guess_winner_flag_num
	self.onevone_person_info.knockout_guess_reward_fetch_flag_num = protocol.knockout_guess_reward_fetch_flag_num
	self.onevone_person_info.win_times = protocol.win_times
	self.onevone_person_info.lose_times = protocol.lose_times
	self.onevone_person_info.score_reward_fetch_flag = bit:d2b(protocol.score_reward_fetch_flag)
	self.onevone_person_info.rank_reward_fetch_flag = bit:d2b(protocol.rank_reward_fetch_flag)
	self.onevone_person_info.match_count_reward_fetch_flag = bit:d2b(protocol.match_count_reward_fetch_flag)
end

function KFOneVOneWGData:SetSCross1V1MatchInfo(protocol)
	self.onevone_match_state = protocol.match_state
	self.onevone_knockout_state = protocol.knockout_round
	self.prematch_round = protocol.prematch_round
	self.next_fight_start_time = protocol.next_fight_start_time
	self.is_prematch_all_matched = protocol.is_prematch_all_matched
end

--跨服1V1战斗开始
function KFOneVOneWGData:SetCross1v1FightStart(protocol)
	--战斗开始的状态
	self.fight_timestamp_type = protocol.timestamp_type
	self.fight_start_timestmap = protocol.fight_start_timestmap
end

function KFOneVOneWGData:SetSCCross1V1WinnerInfo(protocol)
	for i = 1, 3 do
		self.winner_list[i] = {}
		self.winner_list[i].winner_role_id = protocol.palyer_info_list[i].role_id or 0
		self.winner_list[i].winner_plat_type = protocol.palyer_info_list[i].plat_type or 0
	end
end

--获取跨服1v1的冠军
function KFOneVOneWGData:GetCross1V1Winner(index)
	if self.winner_list[index] then
		return self.winner_list[index].winner_role_id, self.winner_list[index].winner_plat_type
	else
		return nil, nil
	end
end

function KFOneVOneWGData:SetFightTimestampType(fight_type)
	self.fight_timestamp_type = fight_type
end

--在场景时的准备 或 开始 的类型
function KFOneVOneWGData:GetFightTimestampType()
	return self.fight_timestamp_type
end

--在场景时的准备 或 开始 的倒计时
function KFOneVOneWGData:GetFightStartTimestamp()
	return self.fight_start_timestmap
end

--淘汰赛轮次
function KFOneVOneWGData:GetOneVOneKnockoutState()
	return self.onevone_knockout_state
end

--1v1比赛进程状态
function KFOneVOneWGData:GetOneVOneMatchState()
	return self.onevone_match_state
end

--在准备场景里的 下一场战斗开始时间
function KFOneVOneWGData:GetNextFightStartTime()
	return self.next_fight_start_time or 0
end

--本轮预选赛是否已全部匹配完成
function KFOneVOneWGData:GetIsPrematchAllMatched()
	return self.is_prematch_all_matched and self.is_prematch_all_matched == 1
end

--获取预选赛轮次
function KFOneVOneWGData:GetPermatchRound()
	return self.prematch_round or 0
end

function KFOneVOneWGData:GetOneVOneRankList()
	return self.onevone_rank_list
end

function KFOneVOneWGData:GetOneVOnePersonInfo()
	return self.onevone_person_info
end

function KFOneVOneWGData:GetOneVOneMyRankPerson()
	local my_rank_index = self.my_rank_index
	if my_rank_index then
		return self.onevone_rank_list and self.onevone_rank_list[my_rank_index]
	end
end

function KFOneVOneWGData:GetOneVOneMyRankNum()
	return self.my_rank_index or 0
end

--获取对手信息
function KFOneVOneWGData:GetOneVOneOpponentInfo()
	return self.onevone_opponent_info
end

--获取对手在场景上的obj
function KFOneVOneWGData:GetOpponentSceneInfo()
	local oppo_info = self:GetMyEnemyInfo()
	if oppo_info then
		local role_obj = Scene.Instance:GetRoleByUUID(oppo_info.uuid)
		return role_obj
	end
end

--获取进入16名淘汰赛的角色信息
function KFOneVOneWGData:GetKnockoutItemInfo(index)
	return self.knockout_item_list and self.knockout_item_list[index] or {}
end

function KFOneVOneWGData:GetKnockoutIndexes16To8()
	return self.indexes_16_to_8
end

function KFOneVOneWGData:GetKnockoutIndexes8To4()
	return self.indexes_8_to_4
end

function KFOneVOneWGData:GetKnockoutIndexes4To2()
	return self.indexes_4_to_2
end

function KFOneVOneWGData:GetKnockoutIndexesWinner()
	return self.indexes_winner or 0         --加个容错
end

function KFOneVOneWGData:GetKnockoutWinnerInfo()
	local index = self:GetKnockoutIndexesWinner()
	local info = self:GetKnockoutItemInfo(index)
	return info
end

function KFOneVOneWGData:GetKnockoutIndexes16To8Info(info_index)
	return self.indexes_16_to_8_index and self.indexes_16_to_8_index[info_index]
end

function KFOneVOneWGData:GetKnockoutIndexes8To4Info(info_index)
	return self.indexes_8_to_4_index and self.indexes_8_to_4_index[info_index]
end

function KFOneVOneWGData:GetKnockoutIndexes4To2Info(info_index)
	return self.indexes_4_to_2_index and self.indexes_4_to_2_index[info_index]
end

--other配置
function KFOneVOneWGData:GetKFOneVOneOtherCfg()
	return self.other_cfg
end

--判断该轮次能否进行竞猜
function KFOneVOneWGData:GetCurKnockoutCanJingCai()
	local knockout = self:GetOneVOneKnockoutState()
	local match_state = self:GetOneVOneMatchState()
	if match_state == KFOneVOneWGData.MatchState.TaoTai then
		local num = KFOneVOneDuiZhenView.Lunci[knockout]
		if not num then
			local str = "当前轮次不存在:"..knockout .. " num:"..num
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			return false
		end
		local can_jingcai = self:GroupCanJingCai(knockout,num)
		if not can_jingcai then
			return false
		end
	end
	return true
end

function KFOneVOneWGData:GroupCanJingCai(knockout,num)
	for i=1,num do
		local data = self:GetKnockoutGroupItem(knockout,i)
		local info1 = data[1]
		local info2 = data[2]
		if not IsEmptyTable(info1) and not IsEmptyTable(info2) and info1.name ~= "" and info2.name ~= "" then
			return true
		end
	end
	return false
end

--根据轮次获取一组对打的信息
function KFOneVOneWGData:GetKnockoutGroupItem(knockout,index)
	local data = {}
	local info_index1 = index * 2 - 1
	local info_index2 = index * 2
	local item_info1 = {}
	local item_info2 = {}
	local indexes_to_list = {}

	if knockout == KFOneVOneWGData.KnockoutState.None or knockout == KFOneVOneWGData.KnockoutState.Index16To8 then
		if info_index1 ~= 0 then
			item_info1 = self:GetKnockoutItemInfo(info_index1)
		end
		if info_index2 ~= 0 then
			item_info2 = self:GetKnockoutItemInfo(info_index2)
		end
		table.insert(data,item_info1)
		table.insert(data,item_info2)
		return data
	elseif knockout == KFOneVOneWGData.KnockoutState.Index8To4 then
		indexes_to_list = self:GetKnockoutIndexes16To8()
	elseif knockout == KFOneVOneWGData.KnockoutState.Index4To2 then
		indexes_to_list = self:GetKnockoutIndexes8To4()
	elseif knockout == KFOneVOneWGData.KnockoutState.Index2To1 then
		indexes_to_list = self:GetKnockoutIndexes4To2()
	elseif knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
		local win_idnex = self:GetKnockoutIndexesWinner()
		indexes_to_list = {}
		indexes_to_list[1] = win_idnex
		indexes_to_list[2] = 0
	end
	if not IsEmptyTable(indexes_to_list) then
		local index1 = indexes_to_list[info_index1]
		local index2 = indexes_to_list[info_index2]
		if index1 ~= 0 then
			item_info1 = self:GetKnockoutItemInfo(index1)
		end
		if index2 ~= 0 then
			item_info2 = self:GetKnockoutItemInfo(index2)
		end
		table.insert(data,item_info1)
		table.insert(data,item_info2)
	end
	return data
end

--获取该组信息胜负的信息
function KFOneVOneWGData:GetKnockoutItemWinInfo(knockout,info_index)
	local data = {}
	if knockout == KFOneVOneWGData.KnockoutState.None then
		data = self:GetKnockoutIndexes16To8Info(info_index)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index16To8 then
		data = self:GetKnockoutIndexes16To8Info(info_index)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index8To4 then
		data = self:GetKnockoutIndexes8To4Info(info_index)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index4To2 then
		data = self:GetKnockoutIndexes4To2Info(info_index)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index2To1 then
		local indexes_winner = self:GetKnockoutIndexesWinner()
		return indexes_winner and indexes_winner == info_index
	elseif knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
		local indexes_winner = self:GetKnockoutIndexesWinner()
		return indexes_winner and indexes_winner == info_index
	end
	local is_win = false
	is_win = data and data.win or false
	return is_win
end
--竞猜特殊处理
function KFOneVOneWGData:GetKnockoutItemWinInfoJingCai(knockout,info_index1,info_index2)
	local data1 = {}
	local data2 = {}
	if knockout == KFOneVOneWGData.KnockoutState.None then
		data1 = self:GetKnockoutIndexes16To8Info(info_index1)
		data2 = self:GetKnockoutIndexes16To8Info(info_index2)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index16To8 then
		data1 = self:GetKnockoutIndexes16To8Info(info_index1)
		data2 = self:GetKnockoutIndexes16To8Info(info_index2)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index8To4 then
		data1 = self:GetKnockoutIndexes8To4Info(info_index1)
		data2 = self:GetKnockoutIndexes8To4Info(info_index2)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index4To2 then
		data1 = self:GetKnockoutIndexes4To2Info(info_index1)
		data2 = self:GetKnockoutIndexes4To2Info(info_index2)
	elseif knockout == KFOneVOneWGData.KnockoutState.Index2To1 then
		local indexes_winner = self:GetKnockoutIndexesWinner()
		if indexes_winner == 0 then
			return nil
		end
		return indexes_winner and indexes_winner == info_index1
	elseif knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
		local indexes_winner = self:GetKnockoutIndexesWinner()
		return indexes_winner and indexes_winner == info_index1
	end
	local is_win = nil
	if data2 and data2.win then
		is_win = false
	end
	if data1 and data1.win then
		is_win = true
	end
	return is_win
end

--判断是否竞猜到该角色
function KFOneVOneWGData:GetKnockoutIsGuessWinner(index,knockout)
	local guess_winner_flag = self.onevone_person_info and self.onevone_person_info.knockout_guess_winner_flag[knockout] or {}
	if not IsEmptyTable(guess_winner_flag) then
		return guess_winner_flag[33-index] == 1
	end
	return false
end

--获取当前竞猜了多少角色的数量
function KFOneVOneWGData:GetKnockoutIsGuessWinnerCount(knockout)
	local num = 0
	local flag_num = self.onevone_person_info and self.onevone_person_info.knockout_guess_winner_flag_num and
					self.onevone_person_info.knockout_guess_winner_flag_num[knockout] or 0
	num = bit:d2b1n(flag_num) or 0
	return num
end

--判断竞猜对的是否能领取奖励
function KFOneVOneWGData:GetKnockoutGuessIsReward(index,knockout)
	local guess_reward_fetch_flag = self.onevone_person_info and self.onevone_person_info.knockout_guess_reward_fetch_flag[knockout] or {}
	if not IsEmptyTable(guess_reward_fetch_flag) then
		return guess_reward_fetch_flag[33-index] == 1
	end
	return false
end

--跨服1v1红点
function KFOneVOneWGData:IsShowKFOneVOneRedPoint()
	local rank_flag = self:IsShowRankRewardRed()
	if rank_flag == 1 then
		return 1
	end

	-- local score_flag = self:IsShowScoreRewardRed()
	-- if score_flag == 1 then
	-- 	return 1
	-- end

	local match_flag = self:IsShowMatchRewardRed()
	if match_flag == 1 then
		return 1
	end

	local jingcai_flag = self:IsShowJingCaiRed()
	if jingcai_flag == 1 then
		return 1
	end

	return 0
end

--排名奖励红点
function KFOneVOneWGData:IsShowRankRewardRed()
	local rank_cfg = KFOneVOneWGData.Instance:GetRankRewardCfg()
	if rank_cfg then
		for k,v in pairs(rank_cfg) do
			local can_get = KFOneVOneWGData.Instance:GeRankRewardIsGet(v.seq)
			if can_get then
				return 1
			end
		end
	end
	return 0
end

--积分奖励红点
function KFOneVOneWGData:IsShowScoreRewardRed()
	local score_cfg = KFOneVOneWGData.Instance:GetScoreRewardCfg()
	if score_cfg then
		for k,v in pairs(score_cfg) do
			local can_get = KFOneVOneWGData.Instance:GetScoreRewardIsGet(v.seq)
			if can_get then
				return 1
			end
		end
	end
	return 0
end

--场次奖励红点
function KFOneVOneWGData:IsShowMatchRewardRed()
	local match_cfg = KFOneVOneWGData.Instance:GetMatchCountRewardCfg()
	if match_cfg then
		for k,v in pairs(match_cfg) do
			local can_get = KFOneVOneWGData.Instance:GeMatchCountRewardIsGet(v.seq)
			if can_get then
				return 1
			end
		end
	end
	return 0
end

--竞猜红点
function KFOneVOneWGData:IsShowJingCaiRed()
	for k,v in pairs(KFOneVOneDuiZhenView.Lunci) do
		for i=1,v do
			local group_data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(k,i)
			local data1 = group_data[1] or {}
			local data2 = group_data[2] or {}
			local info_index1 = data1.index or -1
			local info_index2 = data2.index or -1
			local is_win_index_list = {}
			is_win_index_list[1] = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(k,info_index1)
			is_win_index_list[2] = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(k,info_index2)
			local is_all_nil = (IsEmptyTable(data1) or data1.name == "") and (IsEmptyTable(data2) or data2.name == "")
			local can_show_win = is_win_index_list[1] or is_win_index_list[2] or is_all_nil
			for l=1,2 do
				local data = group_data[l] or {}
				local info_index = data.index or -1
				local is_guess = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info_index,k)
				local is_red = is_guess and can_show_win and is_win_index_list[l]
				local is_get = KFOneVOneWGData.Instance:GetKnockoutGuessIsReward(info_index,k)
				if not is_get and is_red then
					return 1
				end
			end
		end
	end
	return 0
end

function KFOneVOneWGData:GetKFOneVOneMsgRed()
	-- local score_flag = self:IsShowScoreRewardRed()
	-- if score_flag == 1 then
	-- 	return 1
	-- end

	-- local match_flag = self:IsShowMatchRewardRed()
	-- if match_flag == 1 then
	-- 	return 1
	-- end
	local jingcai_flag = self:IsShowJingCaiRed()
	if jingcai_flag == 1 then
		return 1
	end

	return 0
end

--加载人物前是否需要移动到某目标点
function KFOneVOneWGData:SetNeedGoPosFlag(flag)
	self.need_go_pos_flag = flag
end

function KFOneVOneWGData:GetNeedGoPosFlag()
	return self.need_go_pos_flag 
end

--积分奖励
function KFOneVOneWGData:GetScoreRewardCfg()
	return self.score_reward_cfg
end

function KFOneVOneWGData:GetScoreRewardCfgBySeq(seq)
	return self.score_reward_cfg[seq]
end

function KFOneVOneWGData:GetMaxScoreRewardCfg()
	local cfg = self.config.score_reward
	return cfg[#cfg]
end

--积分奖励是否已领取
function KFOneVOneWGData:GetScoreRewardFetchFlag(seq)
	local score_reward_fetch = self.onevone_person_info and self.onevone_person_info.score_reward_fetch_flag
	if not IsEmptyTable(score_reward_fetch) then
		return score_reward_fetch[32-seq] == 1
	end
	return false
end

--该积分奖励是否可领取
function KFOneVOneWGData:GetScoreRewardIsGet(seq)
	local cfg = self:GetScoreRewardCfgBySeq(seq)
	local person_info = self:GetOneVOnePersonInfo()
	if cfg then
		local score = person_info and person_info.score or 0
		local is_get = self:GetScoreRewardFetchFlag(seq)
		if score >= cfg.need_score and not is_get then
			return true
		end
	end
	return false
end

--排名奖励
function KFOneVOneWGData:GetRankRewardCfg()
	return self.config.rank_reward
end

function KFOneVOneWGData:GetRankRewardCfgBySeq(seq)
	return self.rank_reward_cfg[seq]
end

--排名奖励是否已领取
function KFOneVOneWGData:GetRankRewardFetchFlag(seq)
	local rank_reward_fetch = self.onevone_person_info and self.onevone_person_info.rank_reward_fetch_flag
	if not IsEmptyTable(rank_reward_fetch) then
		return rank_reward_fetch[32-seq] == 1
	end
	return false
end

--该排名奖励是否可领取
function KFOneVOneWGData:GeRankRewardIsGet(seq)
	local cfg = self:GetRankRewardCfgBySeq(seq)
	local person_info = self:GetOneVOnePersonInfo()
	if cfg then
		local knockout_rank = person_info and person_info.knockout_rank or -1
		local is_get = self:GetRankRewardFetchFlag(seq)
		local knockout = self:GetOneVOneKnockoutState() or 0
		local is_close = ActivityWGData.Instance:GetActivityIsClose(ACTIVITY_TYPE.KF_ONEVONE)
		local can_get = knockout >= KFOneVOneWGData.KnockoutState.WinnerEnd and is_close
		if knockout_rank > -1 and knockout_rank >= cfg.min_rank_pos and knockout_rank <= cfg.max_rank_pos and not is_get and can_get then
			return true
		end
	end
	return false
end

-- 获取奖励级别
function KFOneVOneWGData:GetRankRewardSeq(rank_value)
	local cfg_list = self:GetRankRewardCfg()
	if cfg_list and rank_value then
		for i,v in ipairs(cfg_list) do
			if rank_value >= (v.min_rank_pos + 1) and rank_value <= (v.max_rank_pos + 1) then
				return v.seq
			end
		end
	end
	return nil
end
--场次奖励
function KFOneVOneWGData:GetMatchCountRewardCfg()
	return self.match_count_reward_cfg
end

function KFOneVOneWGData:GetMatchCountRewardCfgBySeq(seq)
	return self.match_count_reward_cfg[seq]
end

--场次奖励是否已领取
function KFOneVOneWGData:GetMatchCountRewardFetchFlag(seq)
	local match_count_reward_fetch = self.onevone_person_info and self.onevone_person_info.match_count_reward_fetch_flag
	if not IsEmptyTable(match_count_reward_fetch) then
		return match_count_reward_fetch[32-seq] == 1
	end
	return false
end

--该场次奖励是否可领取
function KFOneVOneWGData:GeMatchCountRewardIsGet(seq)
	local cfg = self:GetMatchCountRewardCfgBySeq(seq)
	local person_info = self:GetOneVOnePersonInfo()
	if cfg then
		local win_times = person_info and person_info.win_times or 0
		local lose_times = person_info and person_info.lose_times or 0
		local match_count = win_times + lose_times
		local is_get = self:GetMatchCountRewardFetchFlag(seq)
		if match_count >= cfg.need_match and not is_get then
			return true
		end
	end
	return false
end

--自己所在淘汰列表的当前索引
function KFOneVOneWGData:GetMyKnockoutIndex()
	return self.my_knockout_index
end

--在淘汰赛获取对手的信息
function KFOneVOneWGData:GetMyKnockoutEnemy()
	local oppo_index = self.cur_opponent_index
	if oppo_index and oppo_index > 0 then
		local oppo_info = self:GetKnockoutItemInfo(oppo_index)
		return oppo_info
	end
end

--获取我对手的信息 统一调这个接口
function KFOneVOneWGData:GetMyEnemyInfo()
	local match_state = self:GetOneVOneMatchState()
	if match_state == KFOneVOneWGData.MatchState.TaoTai then
		return self:GetMyKnockoutEnemy()
	else
		return self:GetOneVOneOpponentInfo()
	end
end

--获取我对手的所在side
function KFOneVOneWGData:GetMyEnemyInfoSide()
	local enemy_info = self:GetMyEnemyInfo()
	local match_state = self:GetOneVOneMatchState()
	if match_state == KFOneVOneWGData.MatchState.TaoTai then
		local knockout_state = self:GetOneVOneKnockoutState()
		local my_knockout_index = self:GetMyKnockoutIndex() or 0
		local enemy_info_index = enemy_info and enemy_info.index or 0
		local side = my_knockout_index > enemy_info_index and 0 or 1
		return side
	else
		return enemy_info and enemy_info.side or 0
	end
end

--获取竞猜配置
function KFOneVOneWGData:GetGuessRewardCfgByRound(round)
	return self.guess_reward_cfg[round]
end

--活动结束清理一些数据
function KFOneVOneWGData:ClearLocalActInfo()
	self.my_knockout_index = nil
	self.has_knockout_match_info = nil
	self:SetOldScore(0)
	self:ClearShowAnimStateList()
	self:ClearJingCaiImgAnimFalg()
	self:ClearPlayJingJiAnim()
	self:SetEnterSceneKnockout(nil)
	self:SetEnterSceneEnemyIndex(nil)
	self:SetCanPlay16QiangAnim(nil)
end

function KFOneVOneWGData:GetResultRewardItemCfg(is_win)
	local match_state = self:GetOneVOneMatchState()
	local reward_item = {}
	local match_reward_cfg = self.match_reward_cfg[0]
	if match_state == KFOneVOneWGData.MatchState.TaoTai then
		match_reward_cfg = self.match_reward_cfg[1]
	end
	if is_win then
		reward_item = match_reward_cfg.win_reward_item
	else
		reward_item = match_reward_cfg.lose_reward_item
	end
	return reward_item
end

--判断我在淘汰赛是否已结束
function KFOneVOneWGData:GetMyInKnockoutIsOver()
	local info_index = self:GetMyKnockoutIndex()
	local knockout = self:GetOneVOneKnockoutState()
	local match_state = self:GetOneVOneMatchState()
	if match_state == KFOneVOneWGData.MatchState.TaoTai and info_index then
		local data = {}
		local enemy_info = KFOneVOneWGData.Instance:GetMyEnemyInfo()
		local is_win = self:GetKnockoutItemWinInfo(knockout,info_index)
		local emeny_index = enemy_info and enemy_info.index or -1
		local enemy_is_win = self:GetKnockoutItemWinInfo(knockout,emeny_index)
		local can_show_win = is_win or enemy_is_win

		if knockout == KFOneVOneWGData.KnockoutState.None then
			data = self:GetKnockoutItemInfo(info_index)
		elseif knockout == KFOneVOneWGData.KnockoutState.Index16To8 then
			data = self:GetKnockoutItemInfo(info_index)
		elseif knockout == KFOneVOneWGData.KnockoutState.Index8To4 then
			data = self:GetKnockoutIndexes16To8Info(info_index)
		elseif knockout == KFOneVOneWGData.KnockoutState.Index4To2 then
			data = self:GetKnockoutIndexes8To4Info(info_index)
		elseif knockout == KFOneVOneWGData.KnockoutState.Index2To1 then
			data = self:GetKnockoutIndexes4To2Info(info_index)
		elseif knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
			local win_idnex = self:GetKnockoutIndexesWinner()
			return win_idnex and win_idnex == info_index
		end
		if can_show_win then
			return is_win == false
		else
			return IsEmptyTable(data)
		end
		
	end
	return true

end

--判断我在淘汰赛是否轮空
function KFOneVOneWGData:GetMyInKnockoutIsLunKong()
	local info_index = self:GetMyKnockoutIndex()
	local knockout = self:GetOneVOneKnockoutState()
	local match_state = self:GetOneVOneMatchState()
	if match_state == KFOneVOneWGData.MatchState.TaoTai and info_index then
		local data = {}
		local indexes_to_list = {}
		if knockout == KFOneVOneWGData.KnockoutState.None then
			data = self:GetKnockoutItemInfo(info_index)
			indexes_to_list = self.knockout_item_list
		elseif knockout == KFOneVOneWGData.KnockoutState.Index16To8 then
			data = self:GetKnockoutItemInfo(info_index)
			indexes_to_list = self.knockout_item_list
		elseif knockout == KFOneVOneWGData.KnockoutState.Index8To4 then
			data = self:GetKnockoutIndexes16To8Info(info_index)
			indexes_to_list = self:GetKnockoutIndexes16To8()
		elseif knockout == KFOneVOneWGData.KnockoutState.Index4To2 then
			data = self:GetKnockoutIndexes8To4Info(info_index)
			indexes_to_list = self:GetKnockoutIndexes8To4()
		elseif knockout == KFOneVOneWGData.KnockoutState.Index2To1 then
			data = self:GetKnockoutIndexes4To2Info(info_index)
			indexes_to_list = self:GetKnockoutIndexes4To2()
		end
		if not IsEmptyTable(data) and not IsEmptyTable(indexes_to_list) then
			local other_index = data.index % 2 == 1 and data.index + 1 or data.index - 1
			local other_info = indexes_to_list[other_index]
			local is_null = false
			if other_info and type(other_info) == "table" then
				is_null = other_info.name == ""
			else
				is_null = other_info <= 0
			end
			return is_null
		end
	end
	return false

end

--奖励进度
function KFOneVOneWGData:GetCurSliderProgress()
	local reward_cfg = {}
	local cur_progress = 0
	local cfg = self:GetMatchCountRewardCfg()
	for k,v in pairs(cfg) do
		table.insert(reward_cfg, v)
	end
	local info = KFOneVOneWGData.Instance:GetOneVOnePersonInfo()
	if  IsEmptyTable(reward_cfg) or info == nil then
		return cur_progress
	end

	local cur_score = info.score
	local win_times = info.win_times or 0
	local lose_times = info.lose_times or 0
	local match_count = win_times + lose_times
	local progress_list = {0.222, 0.414, 0.602, 0.8, 1}			--对应的进度条值
	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = reward_cfg[seq] and reward_cfg[seq].need_match or 0
		local next_need = reward_cfg[seq + 1] and reward_cfg[seq + 1].need_match or reward_cfg[#reward_cfg].need_match
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if match_count > cur_need and match_count <= next_need then
			cur_progress = (match_count - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif match_count > reward_cfg[#reward_cfg].need_match then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end


function KFOneVOneWGData:SetJingCaiRoleList(list)
	self.select_role_list = list
end

function KFOneVOneWGData:GetJingCaiRoleList()
	return self.select_role_list or {}
end

function KFOneVOneWGData:ClearJingCaiRoleList()
	self.select_role_list = {}
end

--警告提示显示
function KFOneVOneWGData:SetOnlyOneShowAnim()
	if self.show_anim_state_list == nil then
		self.show_anim_state_list = {}
	end
	local match_state = self:GetOneVOneMatchState()
	local index = 0
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
	if act_info and act_info.status == ACTIVITY_STATUS.STANDY then
		index = 1
	elseif act_info and act_info.status == ACTIVITY_STATUS.OPEN and match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
		index = 2
	end
	if self.show_anim_state_list[index] == nil then
		self.show_anim_state_list[index] = true
	end
end

function KFOneVOneWGData:GetCanShowAnim()
	local match_state = self:GetOneVOneMatchState()
	local index = 0
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
	if act_info and act_info.status == ACTIVITY_STATUS.STANDY then
		index = 1
	elseif act_info and act_info.status == ACTIVITY_STATUS.OPEN and match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
		index = 2
	end
	if index <= 0 then
		return true
	end
	return self.show_anim_state_list and self.show_anim_state_list[index] or false
end

function KFOneVOneWGData:ClearShowAnimStateList()
	self.show_anim_state_list = nil
end

--竞猜动画播放标记
function KFOneVOneWGData:SetJingCaiImgAnimFlag(knockout,index)
	if self.jingcai_img_anim_falg_list == nil then
		self.jingcai_img_anim_falg_list = {}
	end
	if self.jingcai_img_anim_falg_list[knockout] == nil then
		self.jingcai_img_anim_falg_list[knockout] = {}
	end
	self.jingcai_img_anim_falg_list[knockout][index] = true
end


function KFOneVOneWGData:GetJingCaiImgAnimFlag(knockout,index)
	return self.jingcai_img_anim_falg_list and self.jingcai_img_anim_falg_list[knockout] 
		and self.jingcai_img_anim_falg_list[knockout][index] or false
end

function KFOneVOneWGData:ClearJingCaiImgAnimFalg()
	self.jingcai_img_anim_falg_list = nil
end

--获取决赛的角色信息
function KFOneVOneWGData:GetIndex2To1VSStr()
	local group_data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(KFOneVOneWGData.KnockoutState.Index2To1,1)
	local data1 = group_data[1] or {}
	local data2 = group_data[2] or {}
	local vs_str = ""
	local is_all_nil = (IsEmptyTable(data1) or data1.name == "") and (IsEmptyTable(data2) or data2.name == "")
	if is_all_nil then
		vs_str = Language.Kuafu1V1.NotPlayer
	else
		local name1 = data1.name and data1.name ~= "" and data1.name or Language.Kuafu1V1.NotPlayer
		local name2 = data2.name and data2.name ~= "" and data2.name or Language.Kuafu1V1.NotPlayer
		vs_str =  string.format(Language.Kuafu1V1.TaoTaiVS,name1,name2)
	end
	return vs_str
end

--获取决赛的角色信息
function KFOneVOneWGData:GetWinnerEndVSStr()
	local group_data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(KFOneVOneWGData.KnockoutState.WinnerEnd,1)
	local data1 = group_data[1] or {}
	local vs_str = ""
	local name1 = data1.name and data1.name ~= "" and data1.name or Language.Kuafu1V1.NotPlayer
	vs_str = name1
	return vs_str
end

function KFOneVOneWGData:SetMainRoleFlyDownFalg(flag)
	self.is_main_role_fly_down = flag
end

function KFOneVOneWGData:GetMainRoleFlyDownFalg()
	return self.is_main_role_fly_down or false
end

function KFOneVOneWGData:SetEnterSceneKnockout(knockout)
	self.enter_scene_knockout = knockout
end

function KFOneVOneWGData:GetEnterSceneKnockout()
	return self.enter_scene_knockout
end

function KFOneVOneWGData:SetEnterSceneEnemyIndex(index)
	self.enter_scene_enemy_index = index
end

function KFOneVOneWGData:GetEnterSceneEnemyIndex()
	return self.enter_scene_enemy_index
end

function KFOneVOneWGData:SetPlayJingJiAnim(index)
	if self.play_jingji_anim_list == nil then
		self.play_jingji_anim_list = {}
	end
	self.play_jingji_anim_list[index] = true 
end

function KFOneVOneWGData:GetPlayJingJiAnim(index)
	return self.play_jingji_anim_list and self.play_jingji_anim_list[index] or false
end

function KFOneVOneWGData:ClearPlayJingJiAnim()
	self.play_jingji_anim_list = nil
end

function KFOneVOneWGData:SetOldScore(score)
	self.onevone_old_score = score
end

function KFOneVOneWGData:GetOldScore()
	return self.onevone_old_score or 0
end

function KFOneVOneWGData:SetCanPlay16QiangAnim(flag)
	self.can_paly_16_qiang_anim = flag
end

function KFOneVOneWGData:GetCanPlay16QiangAnim()
	return self.can_paly_16_qiang_anim
end

--获取1v1淘汰赛结果排名
function KFOneVOneWGData:GetOneVOneResultData()
	local count = 0
	local result_data = {}
	local temp_data = {}
	local winner_data = {}
	local winner_index = self:GetKnockoutIndexesWinner() or 1
	winner_data.index = winner_index
	winner_data.rank_seq = 0
	winner_data.rank = 1
	table.insert(result_data,winner_data)
	temp_data[winner_index] = true
	local indexes_4_to_2 = self:GetKnockoutIndexes4To2()
	for k,v in pairs(indexes_4_to_2) do
		if not temp_data[v] and v > 0 then
			local data = {}
			data.index = v
			data.rank_seq = 1
			data.rank = #result_data + 1
			table.insert(result_data,data)
			temp_data[v] = true
		end
	end
	local indexes_8_to_4 = self:GetKnockoutIndexes8To4()
	for k,v in pairs(indexes_8_to_4) do
		if not temp_data[v] and v > 0 then
			local data = {}
			data.index = v
			data.rank_seq = 2
			data.rank = #result_data + 1
			table.insert(result_data,data)
			temp_data[v] = true
		end
	end
	local indexes_16_to_8 = self:GetKnockoutIndexes16To8()
	for k,v in pairs(indexes_16_to_8) do
		if not temp_data[v] and v > 0 then
			local data = {}
			data.index = v
			data.rank_seq = 3
			data.rank = #result_data + 1
			table.insert(result_data,data)
			temp_data[v] = true
		end
	end
	for k,v in pairs(self.knockout_item_list) do
		if not temp_data[v.index] and v.name ~= "" then
			local data = {}
			data.index = v.index
			data.rank_seq = 4
			data.rank = #result_data + 1
			table.insert(result_data,data)
			temp_data[v.index] = true
		end
	end
	return result_data
end

function KFOneVOneWGData:IsOneVOneScene()
	local scene_type = Scene.Instance:GetSceneType()
	local is_scene = false
	if scene_type == SceneType.Kf_OneVOne or
	   scene_type == SceneType.Kf_OneVOne_Prepare then
	   	local scene_loading_open = Scene.Instance:LoadingViewIsOpen()
	   	if scene_loading_open then
	   		is_scene = false
	   	else
	   		is_scene = true
	   	end
	end
	return is_scene
end

function KFOneVOneWGData:GetPrintLog()
	local tab_str = "KFOneVOneWGData:GetPrintLog\n"
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	tab_str = tab_str .. "knockout:"..knockout.."\n"
	local knockout_item_list = self.knockout_item_list
	if not IsEmptyTable(knockout_item_list) then
		for k,v in pairs(knockout_item_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end
	local indexes_16_to_8 = self:GetKnockoutIndexes16To8()
	tab_str = tab_str .. "indexes_16_to_8 = " ..  TableToChatStr(indexes_16_to_8) .. "\n"

	local indexes_8_to_4 = self:GetKnockoutIndexes8To4()
	tab_str = tab_str .. "indexes_8_to_4 = " ..  TableToChatStr(indexes_8_to_4) .. "\n"

	local indexes_4_to_2 = self:GetKnockoutIndexes4To2()
	tab_str = tab_str .. "indexes_4_to_2 = " ..  TableToChatStr(indexes_4_to_2) .. "\n"

	local indexes_winner = self:GetKnockoutIndexesWinner() or 0
	tab_str = tab_str .. "indexes_winner = " ..  indexes_winner
	return tab_str
end
