return {
    id = 8052,
    name = "昆仑玉虚",
    scene_type = 180,
    bundle_name = "scenes/map/a3_bs_wanyaoxianguo_main",
    asset_name = "A3_BS_WanYaoXianGuo_Main",
    width = 317,
    height = 346,
    origin_x = 23,
    origin_y = 9,
    levellimit = 150,
    is_forbid_pk = 0,
    skip_loading = 0,
    show_weather = 0,
    scene_broadcast = 1,
    scenex = 175,
    sceney = 18,
    npcs = {
    },
    monsters = {
    },
    doors = {
    },
    gathers = {
    },
    jumppoints = {
    },
    fences = {
    },
    effects = {
		{bundle="effects/prefab/environment/common/eff_zhilu_sanjiao_prefab", asset="eff_zhilu_sanjiao.prefab", offset={0, 0, 0}, rotation={0, -18, 0}, scale={1, 1, 1}, x=167, y=47},
		{bundle="effects/prefab/environment/common/eff_zhilu_sanjiao_prefab", asset="eff_zhilu_sanjiao.prefab", offset={0, 0, 0}, rotation={0, -50, 0}, scale={1, 1, 1}, x=140, y=57},
		{bundle="effects/prefab/environment/common/eff_zhilu_sanjiao_prefab", asset="eff_zhilu_sanjiao.prefab", offset={0, 0, 0}, rotation={0, 23, 0}, scale={1, 1, 1}, x=104, y=87},
		{bundle="effects/prefab/environment/common/eff_zhilu_sanjiao_prefab", asset="eff_zhilu_sanjiao.prefab", offset={0, 0, 0}, rotation={0, 23, 0}, scale={1, 1, 1}, x=144, y=177},
    },
    sounds = {
    },
    scene_way_points = {
	},
    mask = "XQAAQAAAAG/9//+jt/9HPkgVcjk7xjdYlTQvonMeSxlJbV/b7dWYUYbWYTYb0IIO4c0Gtou3MHPuB5SZcnizDolmSG7hi2bGhk/EFzkGoEpXejELfWWFFATzHJeHEsu7fqy2QXi8QhTUkzC/hdKQyUidc//tJoWD1Jxt/og0DDwIfPERie7g2IjAdpKXbnia4oQPa+6NACVKZfjFqdSibtubh2ad8QNSxSVRFPpb29IlQL+UoGpKZhy+qJaptOBiPVR9RN5FZhWWLhdpJCilRTTir4iOWOxn+AbX13OYZPvrCiQMmiQt+kDhYIkeP5F1HT/vOBjpX4rS5K5LZGPa63R0iKPfGYGTRrAQMPPtgpPiddyPgmZbykjoPKn3dkCT3ITmq7Ohu3iO74lgMlx3kvQM87X3ZBi9oHoEdTfqxNfH5ijIM34wh4wjTWoOsebQvmsneZDY1aVrJnW36J5eACeEB1CYGPigBZDycD/XkTsNgXWpqzn4RAuqK81sxl8XffCfvIA5QmDS1GjMEGrTNeVwNbmusQfwLptJSY2wEnL2Ac7IcMEhBDNkO0CfshXUXspiXIhZtpKzRHyjbDJJp+RiTcE4GEwcqybvIY6wlxcAJlEopt+J44vEo/JN0OKFMFpQSczW1jaUUa9varYQIrf3nW7sW0bOtULv4tg99iV2yix+qXMv55sCc1NTmUUpzHJGs+vM6OZ6grbwM4SQX+TzoAOZjMbAFwVqs3ZEZ8Rl/p838PckVO0WibWW9vU48XNg0oXynJhyj1BbJzV9xJACUnsiE2qCFSfo8RS7sZRVFkPWlPa04e9e/5lnN/FiCJuZcCuOQMbj6CStjjwLqZ0ZlKsubRpFvMc/Kg/zs0bfjCqK5XXVsFWwSuB/Xzbm9M2aVNrbz03uR4aCbgtWuWv5UAfzA5LJJCueCLHPvZVY0cl1TgkaEmY88hN6ZiVtmv3Wxq5iDMudnBVinptamX/xiADT6nAredERSCY2S8IgRUDp1wNk7rOOl7XcEckT8tMXASX3bx8SLuD00lSW6UGT/oxPiuL2/0FwPhKbFUSWsaK2xOhysyWnBxZLdFKTJ7iFpe1a4nvkqKuua2bejYENubL/OFsqGmoO0jGXO7TCVb2vx5bgJjo7Fc99J+oBJpOH/QBt7TazzqtLZoFj7w7li5+zoVe6T0pyYde+Tgs3dSE5wMcQItPAxJWT5/d+zarnGhEv2aUNg5QGaBwvIxdQ0cj+REtNtDXX5LG95gt1lY7O2vqRaf3yTcbC9wuqxbwhlklkGBlbB+O6ddKGCyGc8Li21IK0c0aRBfbGwSYsC1BcOvIGsRyLpRAV2M0ebRYXGbM4FMfOntd3IRTAxG/BlY+JJSRG5SAGXnV5RRJBeaD/Gf+SRgaUwnEfEdOa3mcI0T18o4J1qX/DaT7nSaz+rknMHe2Hm16kR/cmlKI9sQZajlsGHUVI4amzQm0sWUBxysYt+mz/p7vAdA==",
}