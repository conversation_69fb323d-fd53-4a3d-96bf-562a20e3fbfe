-- K-跨服结义充值榜.xls
local item_table={
[1]={item_id=57998,num=100,is_bind=1},
[2]={item_id=26464,num=1,is_bind=1},
[3]={item_id=26462,num=1,is_bind=1},
[4]={item_id=56405,num=1,is_bind=1},
[5]={item_id=56415,num=2,is_bind=1},
[6]={item_id=31259,num=3,is_bind=1},
[7]={item_id=57998,num=50,is_bind=1},
[8]={item_id=26463,num=1,is_bind=1},
[9]={item_id=56404,num=1,is_bind=1},
[10]={item_id=56414,num=1,is_bind=1},
[11]={item_id=31259,num=2,is_bind=1},
[12]={item_id=57998,num=30,is_bind=1},
[13]={item_id=26461,num=1,is_bind=1},
[14]={item_id=26674,num=1,is_bind=1},
[15]={item_id=26689,num=1,is_bind=1},
[16]={item_id=31259,num=1,is_bind=1},
[17]={item_id=57998,num=20,is_bind=1},
[18]={item_id=26459,num=1,is_bind=1},
[19]={item_id=26673,num=1,is_bind=1},
[20]={item_id=26688,num=1,is_bind=1},
[21]={item_id=57998,num=15,is_bind=1},
[22]={item_id=26455,num=1,is_bind=1},
[23]={item_id=26672,num=1,is_bind=1},
[24]={item_id=26687,num=1,is_bind=1},
[25]={item_id=23354,num=1,is_bind=1},
[26]={item_id=26104,num=1,is_bind=1},
[27]={item_id=56406,num=1,is_bind=1},
[28]={item_id=56416,num=3,is_bind=1},
[29]={item_id=31259,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
reward={
{},
{min_rank=2,max_rank=2,reach_value=10000,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{min_rank=3,max_rank=3,reach_value=4000,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[3],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},},
{min_rank=4,max_rank=5,reach_value=1000,reward_item={[0]=item_table[12],[1]=item_table[8],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15],[5]=item_table[16]},},
{min_rank=6,max_rank=10,reach_value=400,reward_item={[0]=item_table[17],[1]=item_table[3],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20],[5]=item_table[16]},},
{min_rank=11,max_rank=20,reach_value=200,reward_item={[0]=item_table[21],[1]=item_table[13],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24],[5]=item_table[16]},}
},

reward_meta_table_map={
},
grade={
{}
},

grade_meta_table_map={
},
other_default_table={minimum_capability=1,minimum_open_day=12,},

reward_default_table={grade=0,min_rank=1,max_rank=1,reach_value=20000,reward_item={[0]=item_table[25],[1]=item_table[26],[2]=item_table[2],[3]=item_table[8],[4]=item_table[27],[5]=item_table[28],[6]=item_table[29]},},

grade_default_table={start_day=1,end_day=9999,grade=0,}

}

