ModulesWGCtrl = ModulesWGCtrl or BaseClass()

function ModulesWGCtrl:__init(is_quick_login)
	if ModulesWGCtrl.Instance ~= nil then
		print_error("[ModulesWGCtrl] attempt to create singleton twice!")
		return
	end
	ModulesWGCtrl.Instance = self

	self:CreateCoreModule()
	self.is_quick_login = is_quick_login
	if not is_quick_login then
		self:CreateLoginModule()
	end

	self.ctrl_list = {}
	self.push_list = {}
	self.cur_index = 0

	if is_quick_login then
		self:Start()
		for k,v in ipairs(self.push_list) do
			table.insert(self.ctrl_list, v.New())
		end

		self:OnAllWGCtrlInited()
	end
end

function ModulesWGCtrl:__delete()
	self:DeleteLoginModule()
	self:DeleteWGCtrls()

	ReuseableHandleManager.Instance:DeleteMe()
	Hotupdate.Instance:DeleteMe()

	BaseCellManager.Instance:DeleteMe()
	QualityManager.Instance:DeleteMe()
	ShieldManager.Instance:DeleteMe()
	UiInstanceMgr.Instance:DeleteMe()
	ClientCmdWGCtrl.Instance:DeleteMe()
	TimeWGCtrl.Instance:DeleteMe()

	if ChatRecordMgr.Instance then
		ChatRecordMgr.Instance:DeleteMe()
	end

	AudioService.Instance:DeleteMe()
	LoadingPriorityManager.Instance:DeleteMe()
	AvatarManager.Instance:DeleteMe()
	RemindManager.Instance:DeleteMe()
	ViewManager.Instance:DeleteMe()
	CacheManager.Instance:DeleteMe()
	ConditionManager.Instance:DeleteMe()
	GameVoManager.Instance:DeleteMe()
	DynamicAssetCache.Instance:DeleteMe()
	TweenManager.Instance:DeleteMe()
	ViewRuleWGData.Instance:DeleteMe()
	SkillPreviewData.Instance:DeleteMe()
	HUDManager.Instance:DeleteMe()
	DayNightManager.Instance:DeleteMe()
	
	ModulesWGCtrl.Instance = nil
end

function ModulesWGCtrl:CreateCoreModule()
	GameVoManager.New()
	ConditionManager.New()
	CacheManager.New()
	ViewManager.New()
	RemindManager.New()
	HUDManager.New()
	AvatarManager.New()
	LoadingPriorityManager.New()
	AudioService.New()
	ChatRecordMgr.New()
	TimeWGCtrl.New()
	ClientCmdWGCtrl.New()
	UiInstanceMgr.New()
	ShieldManager.New()
	QualityManager.New()
	BaseCellManager.New()
	Hotupdate.New()
	ReuseableHandleManager.New()
	DynamicAssetCache.New()
	TweenManager.New()
	ViewRuleWGData.New()
	SkillPreviewData.New()
	DayNightManager.New()
end

function ModulesWGCtrl:Start(call_back)
	self.state_callback = call_back
	self.ctrl_list = {}
	self.cur_index = 0
	-- 把需要创建的WGCtrl加在这里
	self.push_list = {
		SysMsgWGCtrl,
		StepExcuteManager,			-- 分针执行管理器

		BagWGCtrl,				-- 背包数据
		RoleWGCtrl,				-- 人物
		RolePartSetToolsWGCtrl,
		Scene,					-- 场景
		ActivityWGCtrl,
		RoleBagWGCtrl,			-- 人物背包
		NewAppearanceWGCtrl,			-- 新形象
		GuideWGCtrl,
		FightWGCtrl,				-- 战斗逻辑
		FightRevengeWGCtrl,
		GuajiWGCtrl,				-- 挂机逻辑
		ReviveWGCtrl,
		StoryWGCtrl,
		TaskWGCtrl,
		SettingWGCtrl,			-- 设置面板
		TitleWGCtrl,
		BackgroundWGCtrl,			---奇境
		TipWGCtrl,
		MainuiWGCtrl,				-- 主界面
		EquipmentWGCtrl,
		OpenFunWGCtrl,
		SkillWGCtrl, 				-- 技能
        TianShuWGCtrl,			-- 天书技能
        XiuXianShiLianWGCtrl,     --修仙试炼
		FuBenPanelWGCtrl,
		FuBenWGCtrl,
		YunbiaoWGCtrl,			-- 运镖
		DayCounterWGCtrl,
		HomesWGCtrl, 				-- 家园
		GuildWGCtrl, 				-- 公会
		ZhuZaiShenDianWGCtrl,		-- 主宰神殿
		MapWGCtrl,				-- 地图
		MarryWGCtrl,				-- 结婚系统
		DuckRaceWGCtrl, 				-- 小鸭疾走
        NewTeamWGCtrl,
        CrossTeamWGCtrl,
		MasterWGCtrl,
		AppearanceWGCtrl,
		MountWGCtrl,				-- 坐骑系统
		ChatWGCtrl,
		AutoVoiceWGCtrl,
		PerfectLoverWGCtrl,       -- 完美情人
		ConsumeRankThreeWGCtrl,   -- 消费排行榜
		GuildBattleRankedWGCtrl,
		ActivityMainWGCtrl,
		SocietyWGCtrl,
		BiZuoWGCtrl,
		ShenShouWGCtrl,
		TotalChargeWGCtrl,            -- 累计充值
		OtherWGCtrl,
		ServerActivityWGCtrl,

		OperationActivityWGCtrl,		--动态运营活动
		ExchangeShopWGCtrl,
		OperationCtnRechargeWGCtrl,   --动态运营活动_连续充值
		OperationTaskChainWGCtrl,		--动态运营活动 任务链
		OperationFirstRechargeWGCtrl, --动态运营活动_每日首充
		OATurnTableWGCtrl,			-- 动态运营活动 - 兔女郎转盘
		OAFishWGCtrl,					-- 动态运营活动 - 辛运锦鲤
		XianShiMiaoShaWGCtrl, 		-- 动态运营活动 - 限时秒杀
		MoWuJiangLinWGCtrl,			-- 动态运营活动 - 魔物降临
		OperationMoWangWGCtrl,		-- 动态运营活动 - 魔王降临
		OperationJuanXianWGCtrl,		-- 动态运营活动 - 全服捐献
		ImageShowWGCtrl,				-- 动态运营活动 - 形象展示
		OperationActDuoBeiWGCtrl,     -- 动态运营活动 - 多倍活动
		WateringFlowersWGCtrl, 		-- 动态运营活动 - 种花浇水
		LoginRewardWGCtrl,            -- 动态运营活动 - 登录有礼
		ChuShenWGCtrl,                -- 动态运营活动 - 天才厨神
		FZGetRewardWGCtrl,			-- 动态运营活动 - 风筝夺宝
		OperationLeiChongRechargeWGCtrl, --动态运营活动_超值累充
		OperationHappyKuangHuanWGCtrl, --动态运营活动--幸福狂欢
		OpRechargeRankWGCtrl,		--动态运营活动--充值排行

		TransFerWGCtrl,
		MarketWGCtrl,
		AchievementWGCtrl,
		NewXunbaoWGCtrl,
		RechargeWGCtrl,
		VipWGCtrl,
		QiFuWGCtrl,
		SevenDayWGCtrl,
        RechargeRewardWGCtrl,
        MountLingchongEquipWGCtrl,    --骑宠装备
		WelfareWGCtrl,
		BossWGCtrl,
		BossAssistWGCtrl,
		DailyWGCtrl,
		RankWGCtrl,
		FlowerRankWGCtrl,            -- 跨服鲜花榜
		CollectBlessWGCtrl,          -- 翻牌集福
		JinyintaWGCtrl,              -- 天天向上（金銀塔）
		ComposeWGCtrl,
		ShopWGCtrl,
		--NewShopWGCtrl,               --新商店
		SupremeFieldsWGCtrl,
		BrowseWGCtrl,
		FlowerWGCtrl,
		WeddingWGCtrl,
		OfflineRestWGCtrl,
		ExperienceFbWgCtrl,
		Field1v1WGCtrl, 				--战斗面板
		KuafuOnevoneWGCtrl,
		KuafuPVPWGCtrl,
		WeGetMarriedWGCtrl,
		CrossServerWGCtrl,			--跨服
		FuhuoWGCtrl,
		HuanlezadanWGCtrl,			--换了砸蛋
		LotteryTreeWGCtrl,
		ActwishingWGCtrl,
		ActTreasureWGCtrl,
		CompetitionWGCtrl,
		Location,					--位置
		ActIvityHallWGCtrl,
		KuafuHonorhallWGCtrl,			--修罗塔
		KuafuYeZhanWangChengWGCtrl,
		HotSpringWGCtrl,
		GuildAnswerWGCtrl,
		TehuiShopWGCtrl,
		ConsumeDiscountWGCtrl,--连消特惠
		DailyConsumeWGCtrl,--每日累消
		ChargeRepayment2WGCtrl,--累充反馈
		HappyConsumeWGCtrl, --欢乐消费乐
		KuafuConsumptionRankWGCtrl,
		VipTyWGCtrl,
		RewardShowViewWGCtrl,
		FunctionChooseWGCtrl, 	--自选礼包
		CrazyMoneyTreeWGCtrl, 	--疯狂摇钱树
		EveryDayOneLoveWGCtrl, 	--每日一爱
		RechargeReturnRewardWGCtrl, --狂返元宝
		ProfessWallWGCtrl, --表白墙
		WorldServerWGCtrl,            -- 世界服
		ContinuousXiaoFeiWGCtrl, 		--累计消费
		XinFuTeMaiWGCtrl, 			--新服特卖
		MustBuyWGCtrl, 	  			--超值必买
		TianShenWGCtrl,
		TianShenLingHeWGCtrl,		--天神灵核
		TianShenHuamoWGCtrl,		--天神化魔
		TianShenShuangShengWGCtrl,	--天神双生
        TreasureHuntWGCtrl,           --寻宝 新
        FortuneCatWGCtrl,             --招财猫
		ShanHaiJingWGCtrl,			-- 山海经
		ShanHaiJingLSCWGCtrl,			-- 山海经-千秋绝艳图
		BootyBayWGCtrl,				-- 藏宝湾
		XiuZhenRoadWGCtrl,			--修真之路
		BaGuaMiZhenWGCtrl,   			--八卦迷阵
		ExpPoolWGCtrl, 				--经验池
		GodGetRewardWGCtrl, 			--天神夺宝
		TianshenRoadWGCtrl,			--天神之路
		KF3V3WGCtrl,					--新跨服3v3
		ZhanDuiWGCtrl, 				--3V3战队
		QuanMinBeiZhanWGCtrl,			--全民备战
		ActXianQiJieFengWGCtrl,		--仙器解封
        LayoutZeroBuyWGCtrl,			--零元购
        TianShenJuexingWGCtrl,		--天神觉醒
        LimitedTimeOfferWGCtrl,        --特惠直购
		YiBenWanLiWGCtrl,				--一本万利
		MingWenWGCtrl,				--铭文
		LongHunWGCtrl,				--龙魂
		OpenningWGCtrl, 				--开启仙途（新手进游戏弹出）
		CalendarWGCtrl, 				--周历
		BossOfferWGCtrl, 				--Boss悬赏
		OpenServerAssistWGCtrl,		--开服助力
		ZhanLingWGCtrl,				--战令
		FightSoulWGCtrl,				--战魂
		FairyLandEquipmentWGCtrl,		--仙界装备
		CapabilityContrastWGCtrl,		--战力对比
		VersionsAdvanceNoticeWGCtrl,	--版本预告
		WardrobeWGCtrl,				--衣橱
		HmGodWGCtrl,                --鸿蒙神藏
		GuiXuDreamWGCtrl,           --归墟梦演
		CustomizedSuitWGCtrl,		--定制套装

		PierreDirectPurchaseWGCtrl,	--臻品直购
        RebateActivityWGCtrl,             --返利活动
        YiYuanHaoLiWGCtrl,			--一元豪礼

		MergeActivityWGCtrl,			--合服活动
		MergeZhaoCaiMiaoWGCtrl,		-- 合服活动 - 招财猫/喵
        HeFuMiaoShaWGCtrl,			--合服活动 限时秒杀
		HeFuJuLingWGCtrl,				--合服聚灵助阵
		LoginYouLiWGCtrl,				--合服登录有礼
		MergeFirstRechargeWGCtrl,		--合服每日首充
		MergeHMWGCtrl,				--合服鸿蒙悟道
		MergeLeiChongRechargeWGCtrl,	--合服累充
		MergeDuoBeiWGCtrl,			--合服多倍
		MergeFireworksWGCtrl,			--合服烟花庆典
		HeFuWGCtrl,					--合服预告
		LieMoDaRenWGCtrl,				--合服活动 - 猎魔达人
        MergeExchangeShopWGCtrl,      --合服活动 - 兑换商店
        MergeSpecialRankWGCtrl,      --合服活动 - 排行榜

		-- FestivalZhaoCaiMiaoWGCtrl,		-- 节日活动 - 招财猫/喵
		-- FestivalGuildWarWGCtrl,          --节日活动--仙盟争霸
		-- FestivalJuLingWGCtrl,				--节日聚灵助阵
		-- FestivalFirstRechargeWGCtrl,		--节日每日首充
		-- FestivalHMWGCtrl,				--节日鸿蒙悟道
		-- FestivalLieMoDaRenWGCtrl,				--节日活动 - 猎魔达人


  		FestivalActivityWGCtrl,			--节日活动
		FestivalDuoBeiWGCtrl,			--节日多倍
		FestivalLoginYouLiWGCtrl,				--节日登录有礼
		FestivalLeiChongRechargeWGCtrl,	--节日累充
        FestivalExchangeShopWGCtrl,      --节日活动 - 兑换商店
        FestivalMoWangYouliWGCtrl,		-- 节日活动 魔王有礼
        FestivalChuShenWGCtrl,			--节日活动 天才厨神
        FestivalSpecialRankWGCtrl,      --节日活动 - 排行榜
   		--FestivalFireworksWGCtrl,			--节日烟花庆典
        FestivalMiaoShaWGCtrl,			--节日活动 限时秒杀
		FestivalTrunTableWGCtrl,      --节日活动 - 转盘

		FengShenBangWGCtrl,			--F2封神榜
		ZhouYiYunChengWGCtrl,			--F2周一运程
		XianLingGuZhenWGCtrl,			--F2灵犀六芒图
		QunXiongZhuLuWGCtrl,			--F2群雄逐鹿
		LimitTimeGiftWGCtrl,			--F2限时礼包
		GuDaoFuBenWGCtrl,			--F2孤岛激战
		NovicePopDialogWGCtrl,			--新手弹出对话
		ShiTuXiuLiWGCtrl,
		GuildInviteWGCtrl,			-- 仙盟争霸定级赛
		SceneScreenEffectWGCtrl,
		RoleModelTestWGCtrl,
		SubPackageWGCtrl,
		EternalNightWGCtrl,
		SceneAreaEffectWGCtrl, 		-- 场景区域特效
		CountryMapWGCtrl, 			-- 国家版图
		EquipTargetWGCtrl,            -- 装备目标
		ScreenShotWGCtrl, 			-- 风景拍照
		CommonSkillShowCtrl,			-- 通用技能展示
		SiXiangCallWGCtrl,			-- 四象召唤
		SiXiangTeDianWGCtrl,			-- 四象特典
		SecretAreaWGCtrl,               -- 秘境
		CrossLongMaiWGCtrl,				-- 跨服龙脉
		AlchemyWGCtrl,                  -- 炼丹房
		LongZhuWGCtrl,				-- 龙珠
		HiddenWeaponWGCtrl,			-- 暗器
		ChongBangTipWGCtrl,			--冲榜通用弹窗
		XianQiTeDianWGCtrl, 			--仙器特典
		ShenJiNoticeWGCtrl,			--神机功能预告
		AntiFraudCartoonWGCtrl,		--防骗漫画
		NeedGoldGiftWGCtrl,			--特推宝箱
		RareItemDropWGCtrl,			-- 珍惜掉落展示
		TianShen3v3WGCtrl, 			-- 天神3v3
		XianyuTrunTableWGCtrl,
		TianShenRoadMiaoShaWGCtrl,	--天神之路--秒杀活动
		BossXiezhuWGCtrl,				-- boss协助
		LingZhiWGCtrl,
		WorldsNO1WGCtrl, 				-- 天下第一
		CatExploreWGCtrl,               -- 小猫探险
		FlopDrawWGCtrl,                 -- 翻牌好礼
		ChainDrawWGCtrl,                -- 一线牵
		ChaoticPurchaseWGCtrl,          -- 洪荒直购
		SingleRechargeWgCtrl,            --单笔充值
		LuckyGiftBagWgCtrl,             -- 幸运大礼包
		LuckyGiftBagLoaclWgCtrl,		-- 幸运大礼包(本服)
		NiuDanWGCtrl,					--幸运砸蛋
		FiveElementsWGCtrl,             -- 五行系统
		CrossFlowerRankWGCtrl,			-- 跨服鲜花榜
		HongHuangGoodCoremonyWGCtrl,    -- 洪荒礼包
		HongHuangClassicWGCtrl,         -- 洪荒特典
		ChaoticVipSpecialWGCtrl,        -- vip特典
		ActivityLimitBuyWGCtrl,         -- 限时直购
		RebateGiftActivityWGCtrl,		-- 返利活动——绝版赠礼 烟花抽奖 充值立减
		DownLoadWebWGCtrl,              -- 下载有礼
		HelpRankWGCtrl,                 -- 冲榜助力
		TianShenPurchaseWGCtrl,         -- 天神直购
		GodPurchaseWGCtrl,              -- 神藏直购
		HolyDarkWeaponWGCtrl,           -- 圣暗器
		ArtifactWGCtrl,                 --仙魔神器
		VipServiceWindowWGCtrl,			-- vip客服窗口
		BossPrivilegeWGCtrl,			-- Boss特权- 再爆一次
		SwornWGCtrl,                    -- 结义金兰
		GodOfWealthWGCtrl,              -- 喜迎财神
		DIYDrawWGCtrl,					-- DIY抽奖
		CashPointWGCtrl,                -- 现金点
		RechargeVolumeWGCtrl,           -- 充值劵
		LongXiWGCtrl,          			-- 至尊龙玺/龙王令牌
		LevelRechargeWGCtrl,            -- 等级直购
		SystemCapRankWGCtrl,			-- 系统冲榜
		GloryCrystalWGCtrl,             -- 荣耀水晶
		YangLongSiWGCtrl,              -- 养龙寺
		XuYuanFreshPoolWGCtrl,			-- 许愿仙池
		JiangShanRuHuaWGCtrl,			-- 江山如画
		DragonTempleWGCtrl,             -- 龙神殿
		ShiTianSuitWGCtrl,				-- 弑天套装
		YinianMagicWGCtrl,              -- 一念神魔
		CheapShopPurchaseWGCtrl,        -- 特惠卖场
		DiZangRedPackWGCtrl,			-- 地藏红包
		OnlineRewardWGCtrl,				-- 在线奖励
		TodaySpecialWGCtrl,				-- 今日特惠2
		--AccumulativeLoginWGCtrl,		-- 累计登录
		OneDayRechargeWGCtrl,			-- 每日累充2
		HammerPlanWGCtrl,               -- 暴揍策划
		FireWorksDrawSecondWGCtrl,		-- 烟花抽奖2
		SpecialActivityWGCtrl,			-- 特殊抽奖1 幸运转盘
		SystemForceWGCtrl,              -- 系统预告
		SuperPurchaseWGCtrl,			-- 限时直购3
		HideGoldShopWGCtrl,				-- 藏金商铺
		MultiFunctionWGCtrl,            -- 符咒
		ActivityPrivilegeBuyWGCtrl,     -- 特权直购
		ActivityDragonSecretWGCtrl,		-- 神龙密藏
		-- HuanHuaFetterWGCtrl,            -- 幻化羁绊
		NewHuanHuaFetterWGCtrl,         -- 幻化羁绊
		ActivityAmountRechargedWGCtrl,  -- 跨服充值榜
		CrossConsumeRankWGCtrl,			-- 跨服消费榜
		ActivityCollectionWordWGCtrl,	-- 天师集字兑换商店
		CangMingChaseWGCtrl,            -- 沧溟直购 
		GoldStoneWGCtrl,            	-- 金石之言 
		PrivilegedGuidanceWGCtrl,       -- 特权引导 
		PremiumGiftWGCtrl,              -- 超值赠礼
		TianShiGodownHillWGCtrl,		-- 天师下山
		InterludePopDialogWGCtrl,		-- 剧情过场
		WeiWoDunZunWGCtrl,              -- 唯我独尊
		NewFightMountWGCtrl,            -- 新战斗坐骑
		HolyBeastCallWGCtrl,            -- 圣兽召唤
		BossMustFallPrivilegeWGCtrl,    -- boss必爆
		WuHunWGCtrl,					-- 武魂真身
		ActicityFoldFunWGCtrl,          -- 活动折叠功能
		PlaySkillFloatWordWGCtrl,       -- 技能飘字
		CultivationWGCtrl,              -- 修为
		CustomActionCtrl,				-- 自定义动作
		RoleCharmNoticWGCtrl,           -- 魅力榜
		PersonMaBiBossWGCtrl,           -- 个人麻痹boss
		ChessBoardTreasueCtrl,			-- 棋盘寻宝
		ShenNuTianZhuCtrl,				-- 神怒天诛
		BoundlessJoyWGCtrl,             -- 长乐未央
		CrossFlagGrabbingBattleFieldWGCtrl, --跨服夺旗战
		HolyHeavenlyDomainWGCtrl,       -- 圣天神域
		OdysseyPurchaseCtrl,			-- 西游直购
		LifeTimeOfLoveWGCtrl,       	-- 一生所爱
		MechaWGCtrl,                    -- 机甲系统
		CustomizedRumorsWGCtrl,         -- 定制传闻    
		ControlBeastsWGCtrl,			-- 驭兽系统
		ControlBeastsContractWGCtrl,	-- 驭兽抽奖
		CangJinShopWGCtrl,              -- 藏金商铺（功能）
		TreasurePalaceCtrl,				-- 臻宝殿
		UltimateBattlefieldWGCtrl,    -- 终极战场
		BulitInGMCtrl,
		MoRanXuanYuanWGCtrl,            -- 墨染轩辕
		PanaceaFurnaceWGCtrl,			-- 灵丹宝炉
		ReikiSeedWGCtrl,				-- 灵丹宝炉
		VientianeTianyinCtrl,			-- 万象天引
		ShiTianSuitStrengthenWGCtrl,	-- 灵皇套装养成
		TwinDirectPurchaseWGCtrl,		-- 双生直购
		SunRainbowWgCtrl,				-- 贯日长虹
		EverythingUnderTheSunWGCtrl,	-- 森罗万象
		KFCapRankWGCtrl,                -- 跨服提战榜
		BFCapRankWGCtrl,                -- 本服提战榜
		CapabilityUpGiftWGCtrl,			-- 战力飙升礼包
		TianxianPavilionWGCtrl,			-- 天仙宝阁
		KFAttributeStoneRankWGCtrl,		-- 跨服属性宝石榜
		DiscountPurchaseWGCtrl,			-- 一折礼包
		LoverPkWGCtrl,                  -- 仙侣PK
		JingHuaShuiYueWGCtrl,			-- 镜花水月
		CrossMarryRankWGCtrl,			-- 跨服仙侣充值榜
		SwornRechargeWGCtrl,			-- 跨服结义充值榜
		StrangeCatalogWGCtrl,			-- 奇闻异录
		LingYuActiveWGCtrl,				-- 超级锦鲤活动
		MysteryBoxWGCtrl,				-- 盲盒卡包活动
		PositionalWarfareWGCtrl,        -- 阵地战
		SecretRecordWGCtrl,        		-- 紫云秘录
		AssignmentWGCtrl,              	-- 委托任务
		MostVenerableWGCtrl,			-- 最强仙尊
		BossZhanLingWGCtrl,				-- Boss战令
		SkillBreakPurchaseWGCtrl,		-- 技能突破直购
		MengLingWGCtrl,                 -- 梦灵
		HitHamsterWGCtrl,				-- 打地鼠（活动）
		LongYunZhanLingWGCtrl,			-- 龙云战令
		ConquestWarWGCtrl,				-- 征战
		CapabilityWelfareWGCtrl,		-- 战力福利
		EquipBodyWGCtrl,                -- 装备肉身
		OpenServerInvestWGCtrl, 		-- 开服投资
		LordEveryDayShopWGCtrl,			-- 领主商店
		AuctionTipsWGCtrl,				-- 拍卖提示弹窗
		TripleRechargeWGCtrl, 			-- 三倍充值
		FashionExchangeShopWGCtrl,		-- 时装兑换商店
		PhantomDreamlandWGCtrl,			-- 幻梦秘境
		WorldTreasureWGCtrl,			-- 天财地宝
		HaoliWanzhangWGCtrl,			-- 豪礼万丈
		TotalRechargeGiftWGCtrl,		-- 累充豪礼
		HaoLiWanZhang3WGCtrl,			-- 豪礼万丈3 绝版套装
		LandWarFbPersonWGCtrl,          -- 阵地战 （单人副本）
		---[[ 新节日活动
		NewFestivalActivityWGCtrl,		-- 新节日活动
		NewFestivalTehuiShopWGCtrl, 	-- 新节日活动-特惠商店
		NewFestivalDengLuWGCtrl,   		-- 新节日活动-节日登录
		NewFestivalRaffleWGCtrl,   		-- 新节日活动-节日盛典
		NewFestivalPrayerWGCtrl,   		-- 新节日活动-节日祈愿
		NewFestivalConsumeRebateWGCtrl, -- 新节日活动-消费返利
		NewFestivalCollectItemWGCtrl, 	-- 新节日活动-节日收集
		NewFestivalCollectCardWGCtrl, 	-- 新节日活动-集福活动
		NewFestivalRechargeWGCtrl,		-- 新节日活动-活动累充
		NewFestivalBossDropWGCtrl, 		-- 新节日活动-节日掉落
		NewFestivalRankWGCtrl,     		-- 新节日活动-节日排行
		--]]

		ArenaTiantiWGCtrl,              --天梯争霸
		CrossAirWarWGCtrl,				-- 跨服空战
		DrawGiftWGCtrl,					-- 抽奖礼包
		BOSSInvasionWGCtrl,             -- boss入侵
		RedPacketRainWGCtrl,            -- 跨服红包天降
		BillionSubsidyWGCtrl,			-- 百亿补贴
		QTEWGCtrl,			-- QTE
		NewAppearanceDyeWGCtrl,			-- 时装染色
		RoleDiyAppearanceWGCtrl,        -- 角色捏脸设置
		DujieWGCtrl,					-- 渡劫
		RechargeRankWGCtrl,				-- 运营活动 豪掷千金
		XiuWeiWGCtrl,					-- 修为
		ConsumeRankWGCtrl,				-- 运营活动 本服消费榜
		YanYuGeWGCtrl,                  -- 烟雨阁
		ThunderManaWGCtrl,              -- 雷法
		DressingRoleDiyWGCtrl,          -- 易容
		LifeIndulgenceWGCtrl,           -- 终身特惠
		HundredEquipWGCtrl,             -- 百倍爆装
		ControlBeastsOADrawWGCtrl,      -- 幻兽抽奖
		ExpGuideWGCtrl,					-- 升级引导
		FuBenTeamCommonBossWGCtrl,      -- 副本组队通用
		FuBenTeamCommonTowerWGCtrl,     -- 副本组队爬塔通用
		PrivilegeCollectionWGCtrl,      -- 特权合集
		DragonTrialWGCtrl,				-- 龙神试炼
		GameAssistantWGCtrl,			-- 游戏小助手
		HalfPurchaseWGCtrl,				-- 五折直购
		CrossTreasureWGCtrl,			-- 跨服藏宝
		TreasureHuntThunderWGCtrl,      -- 寻宝--雷法抽奖
		MultiMountWGCtrl,               -- 双人坐骑
		OneSwordFrostbiteWGCtrl,		-- 一剑霜寒
	}

	if not self.is_quick_login then
		PushCtrl(self)
	end
end

function ModulesWGCtrl:Stop()

end

function ModulesWGCtrl:GetWGCtrlList()
	return self.ctrl_list
end

------------------------------------------------------------------------

function ModulesWGCtrl:Update(now_time, elapse_time)
	local total_count = #self.push_list
	for i = 1, 12 do
		if self.cur_index < total_count then
			self.cur_index = self.cur_index + 1
            if self.push_list[self.cur_index] == nil then
				print_error(string.format("【Big Bug】Ctrl丢失, index = %s, 上一个Ctrl为：%s", self.cur_index, GetClassName(self.push_list[self.cur_index - 1])))
			else
				table.insert(self.ctrl_list, self.push_list[self.cur_index].New())
            end
		end

		if self.cur_index >= total_count then
			self:OnAllWGCtrlInited()
			PopCtrl(self)
			break
		end
	end

	if self.state_callback then
		self.state_callback(self.cur_index / total_count)
	end
end

function ModulesWGCtrl:CreateLoginModule()
	LoginWGCtrl.New()
end

function ModulesWGCtrl:CreateGameModule()
	self:Start()
	for k,v in ipairs(self.push_list) do
		table.insert(self.ctrl_list, v.New())
	end

	self:OnAllWGCtrlInited()
end

function ModulesWGCtrl:OnAllWGCtrlInited()
	for k,v in pairs(self.ctrl_list) do
		if v.OnAllWGCtrlInited then
			v:OnAllWGCtrlInited()
		end
	end
end

function ModulesWGCtrl:DeleteLoginModule()
	if nil ~= LoginWGCtrl.Instance then
		LoginWGCtrl.Instance:DeleteMe()
	end
end

function ModulesWGCtrl:DeleteGameModule()
	local count = #self.ctrl_list
	for i = count, 1, -1 do
		self.ctrl_list[i]:DeleteMe()
	end
	self.ctrl_list = {}

	for k,v in ipairs(self.push_list) do
		if v.Instance then
			print_error("Forget to set Instance = nil int \"__delete()\"? WGCtrl  ->>", getmetatable(v.Instance).__index)
			v.Instance = nil
		end
	end
	self.push_list = {}
	self.cur_index = 0
end

function ModulesWGCtrl:DeleteWGCtrls()
	local count = #self.ctrl_list
	for i = count, 1, -1 do
		self.ctrl_list[i]:DeleteMe()
	end
	self.ctrl_list = {}
end
