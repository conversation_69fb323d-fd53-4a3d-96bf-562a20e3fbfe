local SHOW_ITEM_COUNT = 4
local TOWER_ITEM_BUNDLE = "uis/view/wuhunzhenshen_prefab"
local TOWER_ITEM_ASSET = "layout_wuhun_tower_item"

function WuHunView:WuHunTowerLoadCallBack()
    if not self.tower_type_list then
        self.tower_type_list = AsyncListView.New(WuHunTowerTypeRender, self.node_list.tower_type_list)
        self.tower_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectTowerTypeItemCB, self))
        self.tower_type_list:SetStartZeroIndex(true)
    end

    if not self.tower_list then
        self.tower_list = {}
        for i = SHOW_ITEM_COUNT, 1, -1 do
            local cell = self:CreateTowerCell(self.node_list.tower_list.transform, i)
            self.tower_list[i] = cell
        end
    end

    -- 初始化3孵化池
    if not self.tower_rank_list then
        self.tower_rank_list = {}
        
        for i = 1, 4 do
            local rank_obj = self.node_list.rank_list_root:FindObj(string.format("rank_item_0%d", i))
            if rank_obj then
                local cell = WuHunTowerRankRender.New(rank_obj, self)
                self.tower_rank_list[i] = cell
            end
        end
    end

    if not self.reward_one_item_list then
	    self.reward_one_item_list = AsyncListView.New(ItemCell, self.node_list.reward_one_item_list)
    end

    if not self.reward_ten_item_list then
	    self.reward_ten_item_list = AsyncListView.New(ItemCell, self.node_list.reward_ten_item_list)
    end

    if not self.challenge_alert then
        self.challenge_alert = Alert.New()
    end

    XUI.AddClickEventListener(self.node_list.btn_tower_goto, BindTool.Bind1(self.ClickTowerGoTo, self))
    XUI.AddClickEventListener(self.node_list.btn_fb_need_wuhun, BindTool.Bind1(self.ClickTowerWuHunGoTo, self))
    XUI.AddClickEventListener(self.node_list.btn_wuhun_tower_message, BindTool.Bind1(self.ClickTowerWuHunMessage, self))
end

-- 创建 render
function WuHunView:CreateTowerCell(parent, index)
    local tower_render = WuHunTowerRender.New()
    tower_render:LoadAsset(TOWER_ITEM_BUNDLE, TOWER_ITEM_ASSET, parent, nil)
    tower_render:SetIndex(index)

    return tower_render
end

---第一队列点击
function WuHunView:OnSelectTowerTypeItemCB(item)
	if nil == item or nil == item.data then
		return
	end

    local index = item.index
    if self.cur_tower_type == index and (not self.tower_type_force_refresh) then
        return
    end

    self.tower_type_force_refresh = nil
    self.cur_tower_type = index
    local bundle, assert = ResPath.GetF2RawImagesPNG(string.format("a2_wht_bg%d", self.cur_tower_type))
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    self.node_list.tower_effect_root:CustomSetActive(self.cur_tower_type == WUHUN_TOWER_TYPE.HELL)
    self.node_list.normal_type_effect:CustomSetActive(self.cur_tower_type ~= WUHUN_TOWER_TYPE.HELL)
    self.node_list.hell_type_effect:CustomSetActive(self.cur_tower_type == WUHUN_TOWER_TYPE.HELL)
    self:WuHunTowerRequestRankInfo()
    self:FlushTowerMessage()
end

function WuHunView:WuHunTowerRequestRankInfo()
    WuHunWGCtrl.Instance:SendRoleWuHunTowerOperate(
        WUHUN_TOWER_OPERATE_TYPE.WUHUN_TOWER_OPERATE_TYPE_RANK_INFO, 
        self.cur_tower_type
    )
end

function WuHunView:WuHunTowerReleseCallBack()
	if self.tower_type_list then
		self.tower_type_list:DeleteMe()
		self.tower_type_list = nil
	end

    if self.tower_list then
        for i = SHOW_ITEM_COUNT, 1, -1 do
            if self.tower_list[i] then
                self.tower_list[i]:DeleteMe()
                self.tower_list[i] = nil
            end
        end

        self.tower_list = nil
    end

    if self.reward_one_item_list then
		self.reward_one_item_list:DeleteMe()
		self.reward_one_item_list = nil
	end

    if self.reward_ten_item_list then
		self.reward_ten_item_list:DeleteMe()
		self.reward_ten_item_list = nil
	end

    if self.challenge_alert then
		self.challenge_alert:DeleteMe()
		self.challenge_alert = nil
	end

    if self.tower_rank_list and #self.tower_rank_list > 0 then
		for _, incubate_cell in ipairs(self.tower_rank_list) do
			incubate_cell:DeleteMe()
			incubate_cell = nil
		end

		self.tower_rank_list = nil
	end

    self.tower_type_force_refresh = nil
end

function WuHunView:WuHunTowerShowIndexCallBack()
end

function WuHunView:FlushWuHunTowerJump(param_t)
    self.tower_type_list:SetDataList(Language.WuHunZhenShen.WuhunTowerTypeGrop)

    if self.cur_tower_type == nil then
        self.tower_type_list:JumpToIndex(WUHUN_TOWER_TYPE.NORMAL, 4)
    else
        self.tower_type_force_refresh = true
        self.tower_type_list:JumpToIndex(self.cur_tower_type, 4)
    end
end

-- 刷新当前副本的全部信息
function WuHunView:FlushTowerMessage()
    if self.cur_tower_type == nil then
        return
    end

    local item_info = WuHunTowerWGData.Instance:GetCurrTowerItemInfo(self.cur_tower_type)
    if not item_info then
        return
    end

    -- 拿去四个，不够四个拿取最后四个
    local show_list, curr_data = WuHunTowerWGData.Instance:GetLevelCfgList(self.cur_tower_type, item_info.pass_seq + 1, SHOW_ITEM_COUNT)

    if self.tower_list ~= nil and show_list ~= nil then
        for i = SHOW_ITEM_COUNT, 1, -1 do
            if self.tower_list[i] and show_list[i] then
                self.tower_list[i]:SetData(show_list[i])
            else
                self.tower_list[i]:SetData({})
            end
        end

        self.cur_tower_data = curr_data
    end

    self:FlushTowerRightMessage()
    self:FlushTowerRightRewardMessage()
end

function WuHunView:FlushTowerRightMessage()
    if (not self.cur_tower_data) or (self.cur_tower_type == nil) then
        return
    end

    local cfg_data = self.cur_tower_data.cfg_data
    
    if not cfg_data then
        return
    end

    local level_str = string.format(Language.FbWushuang.RankValue, NumberToChinaNumber(cfg_data.seq))
    self.node_list.level_progress.text.text = string.format(Language.WuHunZhenShen.WuhunTowerProgress, ToColorStr(level_str, COLOR3B.GREEN)) 

    local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
    self.node_list.my_capability.text.text = string.format(Language.WuHunZhenShen.WuhunTowerMyPower, role_cap) 
    local color = role_cap >= cfg_data.need_cap and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.tuijian_capability.text.text = string.format(Language.WuHunZhenShen.WuhunTowerTuiJianPower, ToColorStr(cfg_data.need_cap, color)) 

    local role_lv = RoleWGData.Instance:GetRoleLevel()
    self.node_list.my_level.text.text = string.format(Language.WuHunZhenShen.WuhunTowerMylevel, role_lv) 
    color = role_lv >= cfg_data.need_level and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.tuijian_level.text.text = string.format(Language.WuHunZhenShen.WuhunTowerTuiJianlevel, ToColorStr(cfg_data.need_level, color)) 

    if cfg_data.need_wuhun ~= nil and cfg_data.need_wuhun ~= 0 then
        local wuhun_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(cfg_data.need_wuhun)
        local wuhun_data = WuHunWGData.Instance:GetWuHunSingleData(cfg_data.need_wuhun)
        local str = string.format(Language.WuHunZhenShen.WuhunTowerNeedWuHunStr, wuhun_cfg and wuhun_cfg.wh_name or "")
        local color = wuhun_data and wuhun_data.lock and COLOR3B.RED or COLOR3B.GREEN
        self.node_list.fb_need_wuhun.text.text = ToColorStr(str, color)
    end

    -- 红点刷新
    self.node_list.btn_tower_goto_red:CustomSetActive(WuHunTowerWGData.Instance:GetTowerTypeRed(self.cur_tower_type) > 0)
end

function WuHunView:FlushTowerRightRewardMessage()
    if (not self.cur_tower_data) or (self.cur_tower_type == nil) then
        return
    end

    local item_info = WuHunTowerWGData.Instance:GetCurrTowerItemInfo(self.cur_tower_type)
    if not item_info then
        return
    end
    
    local one_data, ten_data, is_max = WuHunTowerWGData.Instance:GetCurrLevelRewardCfg(self.cur_tower_type, item_info.pass_seq + 1)
    self:FlushTowerRightBtnStatus(is_max)
    self.node_list.reward_one_txt.text.text = string.format(Language.WuHunZhenShen.WuhunTowerTongGuanStr, one_data and one_data.seq or 1)
    self.node_list.reward_ten_txt.text.text = string.format(Language.WuHunZhenShen.WuhunTowerTongGuanStr, ten_data and ten_data.seq or 10)

    if self.reward_one_item_list and one_data then
        local real_reward_item = {}

        for _, reward_data in pairs(one_data.pass_reward_item) do
            if reward_data and reward_data.item_id then
                table.insert(real_reward_item, reward_data)
            end
        end

		self.reward_one_item_list:SetDataList(real_reward_item)
	end

    if self.reward_ten_item_list and ten_data then
        local real_reward_item = {}

        for _, reward_data in pairs(ten_data.pass_reward_item) do
            if reward_data and reward_data.item_id then
                table.insert(real_reward_item, reward_data)
            end
        end

		self.reward_ten_item_list:SetDataList(real_reward_item)
	end
end

-- 刷新当前按钮状态
function WuHunView:FlushTowerRightBtnStatus(is_max)
    self.node_list.my_level:CustomSetActive(self.cur_tower_type == WUHUN_TOWER_TYPE.NORMAL)
    self.node_list.my_rank:CustomSetActive(self.cur_tower_type == WUHUN_TOWER_TYPE.NORMAL)
    self.node_list.fb_need_wuhun:CustomSetActive(self.cur_tower_type == WUHUN_TOWER_TYPE.HELL)
    self.node_list.yitonguan:CustomSetActive(is_max)
    self.node_list.reward_ten_yitonguan:CustomSetActive(false)
    self.node_list.reward_one_yitonguan:CustomSetActive(false)
    self.node_list.btn_tower_goto:CustomSetActive(not is_max)
    self.node_list.reward_one_item_list:CustomSetActive(true)
    self.node_list.reward_ten_item_list:CustomSetActive(true)
end

-- 刷新武魂排行
function WuHunView:FlushWuHunTowerRank()
    if (not self.cur_tower_data) or (self.cur_tower_type == nil) then
        return
    end
    
    local rank_list = WuHunTowerWGData.Instance:GetTowerRankInfo(self.cur_tower_type)
    if rank_list and self.tower_rank_list and #self.tower_rank_list > 0 then
        for i, incubate_cell in ipairs(self.tower_rank_list) do
            if incubate_cell and rank_list[i] then
                incubate_cell:SetData(rank_list[i])
            end
		end
    end

    local my_rank_data = WuHunTowerWGData.Instance:GetTowerMyRankInfo(self.cur_tower_type)
    local cur_rank = my_rank_data and my_rank_data.rank or 1000
    local str = cur_rank > 100 and Language.KFAttributeStoneRank.NoRank or string.format(Language.Honorhalls.MyRank, cur_rank)
    self.node_list.my_rank_txt.text.text = str
end

----------------------------------------
-- 点击前往副本
function WuHunView:ClickTowerGoTo()
    if (not self.cur_tower_data) or (self.cur_tower_type == nil) then
        return
    end

    local cfg_data = self.cur_tower_data.cfg_data
    
    if not cfg_data then
        return
    end

    if not self.cur_tower_data.is_cur then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunTowerErrorTips)
        return
    end

    if self.cur_tower_data.is_full then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunTowerErrorTips3)
        return
    end

    local item_info = WuHunTowerWGData.Instance:GetCurrTowerItemInfo(self.cur_tower_type)
    if not item_info then
        return
    end

    local capability = GameVoManager.Instance:GetMainRoleVo().capability
	local role_lv = RoleWGData.Instance:GetRoleLevel()

    if role_lv < cfg_data.need_level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunTowerErrorTips2)
        return
    end

    if capability < cfg_data.need_cap and self.challenge_alert then
        self.challenge_alert:SetShowCheckBox(true, "WuHunTowerChallenge")
        local challenge_tips = ToColorStr(cfg_data.need_cap, COLOR3B.RED)
        self.challenge_alert:SetLableString(string.format(Language.WuHunZhenShen.WuHunTowerChallengeTips, challenge_tips))
        self.challenge_alert:SetOkFunc(BindTool.Bind(self.SendRoleWuHunTowerOperate, self))
        self.challenge_alert:SetOkString(Language.Common.Confirm)
        self.challenge_alert:SetCancelString(Language.Common.Cancel)
        self.challenge_alert:Open()
    else
        self:SendRoleWuHunTowerOperate()
    end
end

function WuHunView:SendRoleWuHunTowerOperate()
    local item_info = WuHunTowerWGData.Instance:GetCurrTowerItemInfo(self.cur_tower_type)
    if not item_info then
        return
    end

    WuHunWGCtrl.Instance:SendRoleWuHunTowerOperate(
        WUHUN_TOWER_OPERATE_TYPE.WUHUN_TOWER_OPERATE_TYPE_DARE, 
        self.cur_tower_type,
        item_info.pass_seq + 1
    )
    self:Close()
end

-- 点击去往对应的武魂
function WuHunView:ClickTowerWuHunGoTo()
    if (not self.cur_tower_data) or (self.cur_tower_type == nil) then
        return
    end

    local cfg_data = self.cur_tower_data.cfg_data
    
    if not cfg_data then
        return
    end

    local flush_data = {}
    flush_data.item_id = cfg_data.wuhun_item_id
    flush_data.open_param = "jump"
    self:ChangeToIndex(TabIndex.wuhun_details)
    self:Flush(TabIndex.wuhun_details, "tower", flush_data)
end

-- 武魂塔信息描述
function WuHunView:ClickTowerWuHunMessage()
    local rule_tip = RuleTip.Instance
	local rule_title = Language.WuHunZhenShen.WuhunTowerMessagetitle
	local rule_content = Language.WuHunZhenShen.WuhunTowerMessagetips
    rule_tip:SetTitle(rule_title)
	rule_tip:SetContent(rule_content, nil, nil, nil, true)
end

---------WuHunTowerTypeRender------------------
WuHunTowerTypeRender = WuHunTowerTypeRender or BaseClass(BaseRender)
function WuHunTowerTypeRender:OnFlush()
    if not self.data then
        return 
    end

    local str = self.data
    self.node_list.text_btn.text.text = str
    self.node_list.text_high_btn.text.text = str
    self.node_list.hell_tips:CustomSetActive(self.index == WUHUN_TOWER_TYPE.HELL)
    self.node_list.tower_type_red:CustomSetActive(WuHunTowerWGData.Instance:GetTowerTypeRed(self.index) > 0)
end

function WuHunTowerTypeRender:OnSelectChange(is_select)
    self.node_list.Image_hl:SetActive(is_select)
end

------------------------------------------------------------------------------------------------
WuHunTowerRender = WuHunTowerRender or BaseClass(BaseRender)

function WuHunTowerRender:LoadCallBack()
    if self.boss_model == nil then
        self.boss_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["dis_play"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.boss_model:SetRenderTexUI3DModel(display_data)
        -- self.boss_model:SetUI3DModel(self.node_list["dis_play"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    XUI.AddClickEventListener(self.node_list.btn_battle, BindTool.Bind(self.OnClickBtnBattle, self))		---套装部件炫彩
end

function WuHunTowerRender:__delete()
	if self.boss_model then
		self.boss_model:DeleteMe()
		self.boss_model = nil
	end

    self.show_moster_id = nil
end

function WuHunTowerRender:OnClickBtnBattle()
    WuHunWGCtrl.Instance:ClickTowerGoTo()
end

function WuHunTowerRender:OnFlush()
    if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    self.view:SetActive(true)

    if not self.data.cfg_data then
        return 
    end

    local cfg = self.data.cfg_data
    local is_show_left = cfg.seq % 2 == 0 
    self.node_list.left_root:CustomSetActive(is_show_left)
    self.node_list.level_txt.text.text = string.format(Language.FuBenPanel.ZhuShenTaFuCurLevel, NumberToChinaNumber(cfg.seq))

    -- 刷新怪物模型
    local model_cfg = WuHunTowerWGData.Instance:GetlevelMonsterModelCfgByTypeSeq(cfg.type, cfg.seq)
    if model_cfg then
        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.dis_play.rect, model_cfg.pos_x or 0, model_cfg.pos_y or 0, 200)
        RectTransform.SetLocalScale(self.node_list.dis_play.rect, model_cfg.display_scale or 1)
    end

    local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto")
    if monster_cfg and monster_cfg.monster_list and monster_cfg.monster_list[cfg.boss_id] then
        local boss_config = monster_cfg.monster_list[cfg.boss_id]
        local bundle, asset = ResPath.GetMonsterModel(boss_config.resid)

        if self.show_moster_id ~= boss_config.resid then
            self.boss_model:SetMainAsset(bundle, asset)
            self.boss_model:PlayMonsterAction()
            self.show_moster_id = boss_config.resid
        end

        self.node_list.stage_name.text.text = boss_config.name or ""
        self.node_list.stage_level.text.text = boss_config.level or 0
    end

    self.node_list.select:CustomSetActive(self.data.is_cur and (not self.data.is_full))
    self.node_list.btn_battle:CustomSetActive(self.data.is_cur and (not self.data.is_full))
    self.node_list.battle_spine:CustomSetActive(self.data.is_cur and (not self.data.is_full))
    -- self.node_list.stage_root:CustomSetActive(self.data.is_cur and (not self.data.is_full))
    XUI.SetGraphicGrey(self.node_list.level_root, (not self.data.is_cur) or self.data.is_full)
end

---------WuHunTowerRankRender------------------
WuHunTowerRankRender = WuHunTowerRankRender or BaseClass(BaseRender)
function WuHunTowerRankRender:OnFlush()
    if not self.data then
        return 
    end

    if self.data.uid == 0 then
        return
    end

    -- 转职
    local zz_str = RankWGData.Instance:GetProfNameByProf(self.data.prof)
    local str = string.format("%s\r\n%s", self.data.name, zz_str) 
    self.node_list.rank_name.text.text = str
end



