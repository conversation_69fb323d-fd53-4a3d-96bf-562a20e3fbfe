using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class SrpTerrainEditorWindow : EditorWindow
{
    [MenuItem("Window/UrpEngine/Terrain")]
    public static void ShowTerrainEditorWindow()
    {
        SrpTerrainEditorWindow window = (SrpTerrainEditorWindow)EditorWindow.GetWindowWithRect(typeof(SrpTerrainEditorWindow), new Rect(0, 0, 386, 582), false, "Terrain");
        //window.Init();
        window.Show();
    }

    SrpTerrainEditorGui srpTerrainEditorGui;
    private void OnEnable()
    {
        if (srpTerrainEditorGui == null) 
            srpTerrainEditorGui = new SrpTerrainEditorGui();
        srpTerrainEditorGui.OnEnable();
        SceneView.duringSceneGui -= OnSceneGUI;
        SceneView.duringSceneGui += OnSceneGUI;
       
    }
   
    private void OnGUI()
    {
        sceneTerrainFeature = SelectSceneMono();
        if (sceneTerrainFeature == null)
            return;

        srpTerrainEditorGui.OnGUI(sceneTerrainFeature.terrain,
            sceneTerrainFeature.terrainGpuParam,
            sceneTerrainFeature.srpTerrainSettingSo);
    }
    private SrpTerrainFeature sceneTerrainFeature;
    private SrpTerrainFeature SelectSceneMono()
    {
        SrpTerrainFeature temp = FunEditorWindow.GetSelectMonoBehaviour<SrpTerrainFeature>();
        if (temp != null)
            return temp;

        if (null != sceneTerrainFeature && sceneTerrainFeature.isActiveAndEnabled)
            return sceneTerrainFeature;

        sceneTerrainFeature = FunEditorWindow.GetMonoBehaviour<SrpTerrainFeature>();
        return sceneTerrainFeature;
    }

    private void OnSceneGUI(SceneView sceneView)
    {
        srpTerrainEditorGui.OnSceneGUI();
    }
    private void OnDisable()
    {
        if (srpTerrainEditorGui != null)
        {
            srpTerrainEditorGui.OnDisable();
            srpTerrainEditorGui = null;
        }
        SceneView.duringSceneGui -= OnSceneGUI;
    }
}
