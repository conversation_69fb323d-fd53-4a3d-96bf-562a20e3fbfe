FuBenPanelCopperSceneLogic = FuBenPanelCopperSceneLogic or BaseClass(CommonFbLogic)

function FuBenPanelCopperSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.total_wave = 4
	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
end

function FuBenPanelCopperSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
    end
    if self.delay_move then
        GlobalTimerQuest:CancelQuest(self.delay_move)
        self.delay_move = nil
    end
end

function FuBenPanelCopperSceneLogic:Enter(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		ViewManager.Instance:CloseAll()
		CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			FuBenPanelWGCtrl.Instance:OpenCopperTaskView()
        end)
    else
        self.delay_move = GlobalTimerQuest:AddDelayTimer(function()
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            self.delay_move = nil
        end, 1.5)
	end

	self.jump_end_event = GlobalEventSystem:Bind(OtherEventType.JUMP_STATE_CHANGE, BindTool.Bind(self.OnJumpEnd, self))
	if FuBenWGData.Instance:GetTXGEnter() then
		FuBenWGData.Instance:SetTXGEnter(false)
    end

    self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))
    self:SetPointBlock()
    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function FuBenPanelCopperSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end

function FuBenPanelCopperSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
    end

	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function FuBenPanelCopperSceneLogic:Out(old_scene_type, new_scene_type)
    if old_scene_type ~= new_scene_type then
        MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
            CommonFbLogic.Out(self)
			FuBenPanelWGCtrl.Instance:CloseCopperTaskView()
		end)
		ViewManager.Instance:CloseAll()	
    end
    GlobalEventSystem:UnBind(self.fly_down_end_event)
    self.fly_down_end_event = nil
    
	FuBenPanelWGCtrl.Instance:CloseStarAniView()
    
    if self.delay_move then
        GlobalTimerQuest:CancelQuest(self.delay_move)
        self.delay_move = nil
    end

	if self.jump_end_event then
		GlobalEventSystem:UnBind(self.jump_end_event)
		self.jump_end_event = nil
	end
    FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_copper)
    if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function FuBenPanelCopperSceneLogic:OpenFbSceneCd()

end

-- 设置第一个跳跃点不可走
function FuBenPanelCopperSceneLogic:SetPointBlock()
	-- body
	-- local scene_config = Scene.Instance:GetSceneConfig()
	-- if not scene_config or not scene_config.jumppoints or 0 == #scene_config.jumppoints then return end
	-- for k,v in pairs(scene_config.jumppoints) do
	-- 	if 1 == v.id then
	-- 		AStarFindWay:SetBlockInfo(v.x, v.y)
	-- 	end
	-- end
end

-- 显示跳跃点，并恢复跳跃点可走
function FuBenPanelCopperSceneLogic:ShowJumpPoint()


end

function FuBenPanelCopperSceneLogic:OnObjCreate(obj)
	local scene_config = Scene.Instance:GetSceneConfig()
	if not scene_config or not scene_config.jumppoints or 0 == #scene_config.jumppoints then return end
	local len = #scene_config.jumppoints
	local copper_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
	if copper_scence_info.cur_wave < self.total_wave then return end
	if obj and obj:GetVo() and obj:GetType() == SceneObjType.JumpPoint and len ~= obj:GetVo().id then
		obj:CanShowJump(1)
	end
end

-- function FuBenPanelCopperSceneLogic:GetCanJump()
-- 	-- body
-- 	local copper_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
-- 	return copper_scence_info.cur_wave >= self.total_wave
-- end

function FuBenPanelCopperSceneLogic:NeedJump()
	return true
end

function FuBenPanelCopperSceneLogic:OnJumpEnd(bo)
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsDeleted() or main_role:IsRealDead() then
    	return
    end
	if bo == false then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function FuBenPanelCopperSceneLogic:CanAutoPick()
	return true
end