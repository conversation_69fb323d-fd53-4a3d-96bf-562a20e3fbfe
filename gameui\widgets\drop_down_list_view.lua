DropDownView = DropDownView or BaseClass()
local MAX_LENGTH = 10		-- 默认最大长度10

function DropDownView:__init(big_item_render, sub_item_render, list_view, node_root_str, max_length, max_sub_length, sub_render_bundle)
	if nil == list_view then
		print_error("[AsyncListView] 请指定正确的参数, item_render, list_view")
	end

	self.data_list = nil
	self.big_cell_list = nil
	self.sub_cell_list = nil
	self.list_view = list_view
	self.big_item_render = big_item_render or DropDownBigTypeRender
	self.sub_item_render = sub_item_render or DropDownSubTypeRender
	self.select_callback = nil
	self.node_root_str = node_root_str
	self.max_length = max_length or MAX_LENGTH
	self.max_sub_length = max_sub_length or MAX_LENGTH
	self.sub_render_bundle = sub_render_bundle or "uis/view/common_panel_prefab"
	self.load_obj_cache = nil

	self.load_finish = false
	self.default_select_big_index = 1
	self.default_select_small_index = 1
end

function DropDownView:__delete()
	for k, v in pairs(self.big_cell_list) do
		v:DeleteMe()
	end

	for k, v in pairs(self.sub_cell_list) do
		for m, n in pairs(v) do
			n:DeleteMe()
		end
	end

	self.data_list = nil
	self.big_cell_list = nil
	self.sub_cell_list = nil
	self.list_view = nil
	self.big_item_render = nil
	self.sub_item_render = nil
	self.select_callback = nil
	self.max_length = nil
	self.max_sub_length = nil
	self.sub_render_bundle = nil
	self.node_root_str = nil
	self.default_select_big_index = nil
	self.default_select_small_index = nil
	self.load_finish = false
	self.load_obj_cache = nil

	self.select_big_type_callback = nil
	self.select_sub_type_callback = nil
	self:RemoveEffectDelayTimer()
end

-- 设置数据源(异步刷新数据)
-- {[1]={main_data = {}, sub_list = {}}}
function DropDownView:SetDataList(data_list)
	if (not self.max_length) or (not self.node_root_str) then
		print_error("请指定正确的参数, max_length, node_root_str")
		return
	end

	self.data_list = data_list
	if not self.big_cell_list then
		self.big_cell_list = {}
	end

	for i = 1, self.max_length do
		local data = data_list[i]
		local str = string.format("%s%s", self.node_root_str, i)
		local node = self.list_view:FindObj(str)

		if data then
			if self.big_cell_list[i] then
				self.big_cell_list[i]:SetVisible(true)
				self.big_cell_list[i]:SetData(data.main_data)
			else
				if node then
					node:CustomSetActive(true)
					local new_render = self.big_item_render.New(node)
					node.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickBigTypeSelect, self, i, data))
					self.big_cell_list[i] = new_render
					self.big_cell_list[i]:SetData(data.main_data)
					self.big_cell_list[i]:SetVisible(true)
				end
			end
		else
			if self.big_cell_list[i] then
				self.big_cell_list[i]:SetVisible(false)
			end

			if node then
				node:CustomSetActive(false)
			end
		end

		local list = self.list_view:FindObj(string.format("List_%d", i))--列表
		-- 设置子级数据	
		self:RefreshSubList(data, i, list)
	end
end

-- 刷新子级数据
function DropDownView:RefreshSubList(data, main_index, list)
	if not self.sub_cell_list then
		self.sub_cell_list = {}
	end

	if not self.sub_cell_list[main_index] then
		self.sub_cell_list[main_index] = {}
	end

	local set_fun = function()
		for i = 1, self.max_sub_length do
			local sub_data = data and data.sub_list[i]
			if sub_data then
				if self.sub_cell_list[main_index][i] then
					self.sub_cell_list[main_index][i]:SetVisible(true)
					self.sub_cell_list[main_index][i]:SetData(sub_data)
				else
					local obj = ResMgr:Instantiate(self.load_obj_cache)
					obj.name = string.format("big_%d__sub_%d", main_index, i)
					local obj_transform = obj.transform
					obj_transform:SetParent(list.transform, false)
					local item_render = self.sub_item_render.New(obj)
					item_render.view.toggle.group = list.toggle_group
					item_render:SetClickCallBack(BindTool.Bind(self.OnClickSubTypeSelect, self), true)
					item_render:SetData(sub_data) --里层按钮赋值信息
					self.sub_cell_list[main_index][i] = item_render
				end
			else
				if self.sub_cell_list[main_index][i] then
					self.sub_cell_list[main_index][i]:SetVisible(false)
				end
			end

			if main_index == self.max_length and i == self.max_sub_length then
				self.load_finish = true
				self:LoadFinishCallback()
				self:JumpToIndex(self.default_select_big_index, self.default_select_small_index)
			end
		end
	end

	if not self.load_obj_cache then
		self:RefreshSubListCell(main_index, set_fun)
	else
		set_fun()
	end
end

-- 获取一个对象
function DropDownView:RefreshSubListCell(main_index, callback)
	local res_async_loader = AllocResAsyncLoader(self, "dropdown_item" .. main_index)
	res_async_loader:Load(self.sub_render_bundle, "drop_down_render", nil, function(new_obj)
		self.load_obj_cache = new_obj
		if callback then
			callback()
		end
	end)
end

-- 加载完成
function DropDownView:LoadFinishCallback()
	if self.load_finish_callback then
		self.load_finish_callback()
	end
end

-- 设置大类型
function DropDownView:SetSelectBigTypeCallback(select_big_type_callback)
	self.select_big_type_callback = select_big_type_callback
end

-- 设置小类型
function DropDownView:SetSelectSubTypeCallback(select_sub_type_callback)
	self.select_sub_type_callback = select_sub_type_callback
end

-- 点击大按钮
function DropDownView:OnClickBigTypeSelect(big_type, data, isOn)
	if not isOn then
		return
	end

	if self.select_big_type_callback then
		self.select_big_type_callback(big_type, data)
	end
end

-- 点击小按钮
function DropDownView:OnClickSubTypeSelect(sub_cell)
	if self.select_sub_type_callback then
		self.select_sub_type_callback(sub_cell)
	end
end

-- 跳转到对应位置
function DropDownView:JumpToIndex(big_index, sub_index)
	if self.load_finish then
		if self.big_cell_list[big_index] then
			if self.big_cell_list[big_index].view.toggle.isOn then
				if self.select_big_type_callback then
					self.select_big_type_callback(big_index, nil, true)
				end
			end

			self.big_cell_list[big_index].view.toggle.isOn = true
		end
	
		if self.sub_cell_list[big_index] and self.sub_cell_list[big_index][sub_index] then
			if self.sub_cell_list[big_index][sub_index].view.toggle.isOn then
				self:OnClickSubTypeSelect(self.sub_cell_list[big_index][sub_index])
			else
				-- 这里延时0.1秒，父节点没激活情况下设置isOn不生效
				self:RemoveEffectDelayTimer()
				self.show_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
					if self.sub_cell_list[big_index] and self.sub_cell_list[big_index][sub_index] then
						self.sub_cell_list[big_index][sub_index].view.toggle.isOn = true
					end
				end, 0.1)
			end
		end
	else
		self.default_select_big_index = big_index
		self.default_select_small_index = sub_index
	end
end

--移除回调
function DropDownView:RemoveEffectDelayTimer()
    if self.show_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_delay_timer)
        self.show_delay_timer = nil
    end
end


----------------------下拉框---------------------------------
--大类型
DropDownBigTypeRender = DropDownBigTypeRender or BaseClass(BaseRender)
function DropDownBigTypeRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.text_btn.text.text = self.data.assistant_name
	self.node_list.text_high_btn.text.text = self.data.assistant_name
end

--小类型
DropDownSubTypeRender = DropDownSubTypeRender or BaseClass(BaseRender)
function DropDownSubTypeRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.text_name.text.text = self.data.assistant_name
	self.node_list.text_name_hl.text.text = self.data.assistant_name
end