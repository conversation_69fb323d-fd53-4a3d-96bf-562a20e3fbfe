CustomizedSkillShowView = CustomizedSkillShowView or BaseClass(SafeBaseView)

function CustomizedSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)

    local common_path = "uis/view/common_panel_prefab"
    local view_bundle = "uis/view/skill_show_ui_prefab"
    
    self:AddViewResource(0, common_path, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_customized_skill_show_view")
    self:AddViewResource(0, view_bundle, "layout_skill_desc_view")
    self:AddViewResource(0, common_path, "layout_a3_common_top_panel")
end

function CustomizedSkillShowView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShen.SkillPreview
    self:InitSkillInfo()

    self.cur_select_customized_suit = -1
    if not self.customized_show_list then
        self.customized_show_list = AsyncListView.New(CustomizedSuitRender, self.node_list["customized_list_view"])
        self.customized_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectCustomizedCallBack, self))
        self.customized_show_list:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
    end

    if not self.skill_list then
        self.skill_list = {}
        for i = 1, 5 do
            self.skill_list[i] = SkillShowSkillRender.New(self.node_list.skill_list:FindObj(string.format("skill_%d", i)))
            self.skill_list[i]:SetNeedChangeSkillBtnPos(false)
            self.skill_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self, self.skill_list[i]))
        end
    end

    if not self.skill_pre_view then
        self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage, true) 
        self.skill_pre_view:SetPreviewPlayEndCb(BindTool.Bind1(self.PreviewPlayEnd, self))
    end

    -- self:ChangeViewDisplay(nil, 18)
end

function CustomizedSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()
    self:CleanCustomizedRemoveCDTimer()

    self.cur_select_customized_suit = -1

    if self.customized_show_list then
        self.customized_show_list:DeleteMe()
        self.customized_show_list = nil
    end

    for i = 1, 5 do
        if self.skill_list[i] then
            self.skill_list[i]:DeleteMe()
            self.skill_list[i] = nil
        end
    end

    if self.skill_pre_view then
		self.skill_pre_view:DeleteMe()
		self.skill_pre_view = nil
	end

    self.skill_list = nil
end


function CustomizedSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end

function CustomizedSkillShowView:ShowIndexCallBack()
    self.skill_play_timestemp = 0
end

-- 列表选择返回
function CustomizedSkillShowView:OnSelectCustomizedCallBack(item)
    if self:IsLimitClick() then
        return
    end

	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    if self.cur_select_customized_suit == data.suit then
        return
    end

    local suit = data.suit
    self.cur_select_skill_data = nil
    self.cur_select_customized_suit = suit
    self.show_data.suit = data.suit

    self:ChangeAppearanceShowData()

    -- 刷新技能格子
    local skill_cfg_list = CustomizedSuitWGData.Instance:GetSelectSkillListBySuit(nil, suit)

    for i = 1, 5 do
        if self.skill_list[i] and skill_cfg_list[i] then
            self.skill_list[i]:SetData(skill_cfg_list[i])

            if i == 1 then
                self:FlushSkillInfo(skill_cfg_list[i].skill_id, skill_cfg_list[i].skill_level, skill_cfg_list[i].skill_name, skill_cfg_list[i].skill_desc)
            end
        end
    end

    if self.cur_select_skill_data == nil and self.skill_list[1] ~= nil then
        self:OnClickSkillBtn(self.skill_list[1])
    end
end

function CustomizedSkillShowView:ChangeAppearanceShowData()
    self.show_appearance_data = nil
	local sa_data = {}
	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.cur_select_customized_suit)
	if IsEmptyTable(show_list) then
		return
	end

    local body_res_id = AppearanceWGData.Instance:GetRoleResId()
	sa_data.fashion_body = body_res_id % 1000

    local res_id
    for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION then		-- 时装大类
            local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
            if fashion_cfg then
                res_id = fashion_cfg.resouce
                if data.param1 == SHIZHUANG_TYPE.BODY then
                    sa_data.fashion_body = res_id
                elseif data.param1 == SHIZHUANG_TYPE.HALO then	-- 光环
                    sa_data.fashion_guanghuan = res_id
                elseif data.param1 == SHIZHUANG_TYPE.WING then		-- 羽翼
                    sa_data.wing_appeid = res_id
                elseif data.param1 == SHIZHUANG_TYPE.FABAO then		-- 法宝
                    sa_data.fabao_appeid = res_id
                elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then	-- 剑阵
                    sa_data.jianzhen_appeid = res_id
                elseif data.param1 == SHIZHUANG_TYPE.SHENBING then	-- 武器
                    sa_data.shenwu_appeid = res_id
                end
            end
		end
    end

    self.show_appearance_data = sa_data
end

-- 点击技能
function CustomizedSkillShowView:OnClickSkillBtn(item)
    if not self.skill_pre_view then
		return
	end

    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self.cur_select_skill_data = data.skill_id
    self:FlushPlaySkill()

    self:CleanSkillBtnCDTimer()
    self:CleanCustomizedRemoveCDTimer()
    self.show_data.skill_id = data.skill_id
    self.show_data.level = data.skill_level
    -- self:ChangeViewDisplay(data.skill_id)


    -- self:SimulateSpecialSkillOpera()
    -- if self.show_data.skill_id == 7001 then
    --     self:ShowEnemyCustomizedHit()
    -- end

    self:FlushSkillInfo(data.skill_id, data.skill_level, data.skill_name, data.skill_desc)

    -- 技能按钮CD
    local skill_show_time = 6
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )
end

-- 技能按钮倒计时
function CustomizedSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

function CustomizedSkillShowView:SetAllSkillBtnCD(time, total_time)
    for i = 1, 5 do
        if self.skill_list[i] then
            self.skill_list[i]:SetSkillBtnCD(time, total_time)
        end
    end
end

function CustomizedSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end



function CustomizedSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local show_list = CustomizedSuitWGData.Instance:GetSuitShowList()
    if IsEmptyTable(show_list) then
        return
    end

    if self.customized_show_list then
        self.customized_show_list:SetDataList(show_list)
    end

    local show_suit = self.show_data.suit
    if self.cur_select_customized_suit ~= show_suit then
        local jump_index = 1
        for k,v in pairs(show_list) do
            if show_suit == v.suit then
                jump_index = k
                break
            end
        end

        self.customized_show_list:JumpToIndex(jump_index)
    end
end

-- 刷新人物模型
function CustomizedSkillShowView:FlushPlaySkill()
    if self.skill_pre_view:GetPreviewIsLoaded() then
		self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
	else
		self.skill_pre_view:SetPreviewLoadCb(function()
			self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
		end)
	end
end

-- 技能预览结束回调
function CustomizedSkillShowView:PreviewPlayEnd()
    self:RemovePreSkillDelayTimer()

	self.show_pre_skill_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.cur_select_skill_data then
			return
		end

		if self.skill_pre_view and self.skill_pre_view:GetPreviewIsLoaded() then
			self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
		end
	end, 4)
end

--移除回调
function CustomizedSkillShowView:RemovePreSkillDelayTimer()
    if self.show_pre_skill_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_pre_skill_delay_timer)
        self.show_pre_skill_delay_timer = nil
    end
end

-- 	end
-- end

-- 设置下跪
function CustomizedSkillShowView:ShowEnemyCustomizedHit()
    self.enemy_obj:CrossAction(SceneObjPart.Main, SceneObjAnimator.CustomizedHit5)
    self.enemy_obj:CharacterAnimatorEvent(nil, nil, SceneObjAnimator.CustomizedHit5.."/begin", true)
    self.customized_hit_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.enemy_obj:ChangeToCommonState()
    end, 5)
end

-- 延迟移除下跪
function CustomizedSkillShowView:CleanCustomizedRemoveCDTimer()
    if self.customized_hit_timer then
        GlobalTimerQuest:CancelQuest(self.customized_hit_timer)
        self.customized_hit_timer = nil
    end
end

function CustomizedSkillShowView:InitSkillInfo()
	self.message_root_tween = self.node_list.skill_desc_root:GetComponent(typeof(UGUITweenPosition))
	XUI.AddClickEventListener(self.node_list.bag_forward_button,
		BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, false))
	XUI.AddClickEventListener(self.node_list.bag_reverse_button,
		BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, true))
end


--技能描述展示按钮.
function CustomizedSkillShowView:PlayBeastsMessagePositionTween(is_forward)
	self.node_list.bag_forward_button:CustomSetActive(is_forward)
	self.node_list.bag_reverse_button:CustomSetActive(not is_forward)

	if not IsNil(self.message_root_tween) then
		if is_forward then
			self.message_root_tween:PlayForward()
		else
			self.message_root_tween:PlayReverse()
		end
	end
end

--刷新技能描述.
function CustomizedSkillShowView:FlushSkillInfo(skill_id, skill_level, skill_name, skill_desc)
	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	if client_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
	end

	self.node_list.skill_nane.text.text = skill_name
	self.node_list.skill_desc.text.text = skill_desc
end

--释放技能描述动画.
function CustomizedSkillShowView:ClearSkillInfo()
	if not IsNil(self.message_root_tween) then
		self.message_root_tween = nil
	end
end

--===================================================================
CustomizedSuitRender = CustomizedSuitRender or BaseClass(BaseRender)

function CustomizedSuitRender:OnFlush()
    if self.data == nil then
        return
    end

    local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.data.suit)
    self.node_list.text_name.text.text = suit_data.suit_name
    self.node_list.text_name_hl.text.text = suit_data.suit_name
    -- TODO 图片替换
    -- local bundle, asset = ResPath.GetSkillShowImg("a2_jndi_suit_" .. self.data.suit)
    -- self.node_list.cell_bg.image:LoadSprite(bundle, asset)
end

function CustomizedSuitRender:OnSelectChange(is_select)
    self.node_list.select_hl:SetActive(is_select)
    self.node_list.text_name_hl:SetActive(is_select)
    self.node_list.img_name_bg_hl:SetActive(is_select)
    self.node_list.text_name:SetActive(not is_select)
end