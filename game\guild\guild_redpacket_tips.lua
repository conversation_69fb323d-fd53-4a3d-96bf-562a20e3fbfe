GuildRedPacketTips = GuildRedPacketTips or BaseClass(SafeBaseView)
RED_TYPE = 7 --自动添加的红包，所以金额由服务器下发
function GuildRedPacketTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_red_tips")
	--self.red_pocket_num = 10
	self.yb_old_num = 0
	self.yb_new_num = 0
	self.yuanbao_num_isplay = false
	self.yuanbao_acc_isplay = false
	self.all_use_red = {}
end

function GuildRedPacketTips:__delete()
	
end

function GuildRedPacketTips:ReleaseCallBack()
	if nil ~= self.red_pocket_gold then
		self.red_pocket_gold:DeleteMe()
		self.red_pocket_gold  = nil
	end

	if nil ~= self.red_pocket_list then
		self.red_pocket_list:DeleteMe()
		self.red_pocket_list  = nil
	end

	if nil ~= self.pop_num then
		self.pop_num:DeleteMe()
		self.pop_num = nil
	end

	if self.vip_countdown_event_1 then
		GlobalTimerQuest:CancelQuest(self.vip_countdown_event_1)
		self.vip_countdown_event_1 = nil
	end

	if self.event_listener ~= nil then
		self.event_listener = nil
	end

	--self.red_pocket_num = 10
	self.red_pocket_max_num = 0
	self.yb_old_num = 0
	self.yb_new_num = 0
	self.yuanbao_num_isplay = nil
	self.yuanbao_acc_isplay = nil

	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function GuildRedPacketTips:LoadCallBack()
	-- self.pop_num = NumKeypad.New()
	-- self.pop_num:SetOkCallBack(BindTool.Bind1(self.OnOKCallBack, self))

	--self.node_list.lbl_red_num.text.text = self.red_pocket_num

	self:CreateRedPacketList()
	--self:CreateRedPacketNum()
	-- self.node_list.img9_red_bg.button:AddClickListener(BindTool.Bind(self.OnInputClickHandler, self))
	-- self.node_list.btn_pen.button:AddClickListener(BindTool.Bind(self.OnInputClickHandler, self))
	self.node_list.vip4_lingqu.button:AddClickListener(BindTool.Bind(self.GetRewardDouble, self))
	self.node_list["btn_red_state"].button:AddClickListener(BindTool.Bind1(self.OnClickState, self))
	--self.node_list["btn_vip_close"].button:AddClickListener(BindTool.Bind1(self.OnClickCloseVip, self))

	local all_cfg = WelfareWGData.Instance:GetWelfareCfg()
	local other_config = all_cfg.other_config[1]
	local vip = VipWGData.Instance:GetRoleVipLevel()
	local is_vip = VipWGData.Instance:IsVip()
	local can_lingqu = is_vip and vip >= other_config.double_fetch_vip
	XUI.SetButtonEnabled(self.node_list.vip4_lingqu, can_lingqu)
	self.node_list.vip4_btn_effect:SetActive(can_lingqu)
	self.node_list.jubao_effect:SetActive(can_lingqu)
	if is_vip and nil == self.vip_countdown_event_1 then
		self.vip_countdown_event_1 = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.VipCountDownUpdateCallBack, self), 1)
	end
	UITween.MoveLoop(self.node_list["big_bg"], Vector2(-5, 20), Vector2(-5, 40), 1)
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
	self.node_list["next_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOpenRedPacket, self))
end

function GuildRedPacketTips:ShowIndexCallBack()
end

function GuildRedPacketTips:CreateRedPacketList()
 	self.red_pocket_list = AsyncListView.New(RedPocketListItemRender, self.node_list["ph_fetch_list"])
	--self.red_pocket_list:SetSelectCallBack(BindTool.Bind1(self.SelectRedPocketListItemCallBack, self))
end

function GuildRedPacketTips:VipCountDownUpdateCallBack(elapse_time, total_time)
	local end_time = VipWGData.Instance:GetVipEndTime()
	local time = end_time - TimeWGCtrl.Instance:GetServerTime()
	local all_cfg = WelfareWGData.Instance:GetWelfareCfg()
	local other_config = all_cfg.other_config[1]
	local vip = GameVoManager.Instance:GetMainRoleVo().vip_level
	local is_vip = VipWGData.Instance:IsVip()
	XUI.SetButtonEnabled(self.node_list.vip4_lingqu, is_vip and time > 0 and vip >= other_config.double_fetch_vip)
	if time > 0 then
	else
		if self.vip_countdown_event_1 then
			GlobalTimerQuest:CancelQuest(self.vip_countdown_event_1)
			self.vip_countdown_event_1 = nil
		end
		if self.node_list and self.node_list.vip4_btn_effect and self.node_list.jubao_effect then
			self.node_list.vip4_btn_effect:SetActive(false)
			self.node_list.jubao_effect:SetActive(false)
		end
	end
end

function GuildRedPacketTips:OnFlush()
	if nil == self.data then return end
	local  all_info = {}
	if self.view_paper_type == 1 then
		all_info = WelfareWGData.Instance:GetGuildRedpaperAllInfo()
	else
		all_info = WelfareWGData.Instance:GetWorldRedpaperAllInfo()
	end
	for k,v in pairs(all_info) do
		if self.data.info_index == v.info_index then
			self.data = v
			break
		end
	end
	local data_base_cfg = WelfareWGData.Instance:GetWelfareCfg()
	local all_cfg = WelfareWGData.Instance:GetWelfareCfg()
	local other_config = all_cfg.other_config[1]
	local vip = GameVoManager.Instance:GetMainRoleVo().vip_level
	self.node_list.lbl_role_name.text.text = self.data.owner_game_name
	if self.data.paper_type == 0 then
		local data_base_cfg_1 = all_cfg.custom_redpaper
		for k,v in pairs(data_base_cfg_1) do
			if v.seq == self.data.paper_level then
				data_base_cfg = v
				self.node_list.lbl_red_detail_limai.text.text = self.data.str_des
				break
			end
		end
	else
		local data_base_cfg_1 = {}
		if self.view_paper_type == 1 then
			data_base_cfg_1 = all_cfg.guild_redpaper
		else
			data_base_cfg_1 = all_cfg.world_redpaper
		end

		for k,v in pairs(data_base_cfg_1) do
			if v.type == self.data.paper_type and v.level == self.data.paper_level then
				data_base_cfg = v
				self.node_list.lbl_red_detail_limai.text.text = v.descript
				break
			end
		end
	end
	local data_list = {}

	local data = __TableCopy(self.data.record_list)
	local count = 0
	local gold_count = 0
	local fetch_vip = 0
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	for k,v in pairs(data) do
		if v.uid > 0 then
			v.paper_type = self.data.paper_type
			v.paper_level = self.data.paper_level
			count = count + 1
			if v.uid == role_id then
				gold_count = v.fetch_num
				fetch_vip = v.fetch_vip
			end
			table.insert(data_list, v)
		end
	end

	if fetch_vip >= other_config.double_fetch_vip then
		local temp_num = math.ceil(gold_count / 2)
		self.node_list.lbi_red_gold.text.text = string.format(Language.Welfare.RedPaperPackageHaveMoney, gold_count,temp_num)
	else
		self.node_list.lbi_red_gold.text.text = gold_count
	end

	if not IsEmptyTable(data_base_cfg) then
		local bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")

		if data_base_cfg.gold and data_base_cfg.gold > 0 then
			bundle, asset = ResPath.GetCommonIcon("a3_huobi_xianyu")
		end

		if data_base_cfg.bind_gold and data_base_cfg.bind_gold > 0 then
			self.node_list.img_red_gold.image:LoadSpriteAsync(bundle, asset, function ()
				self.node_list.img_red_gold.image:SetNativeSize()
			end)
	
			self.node_list.rich_fetch_tips.text.text = string.format(Language.Welfare.RedPaperPackageHaveCountGole,count,data_base_cfg.num)
			self.node_list.rich_fetch_tips_1.text.text = string.format(Language.Welfare.RedPaperPackageHaveCountGole1,data_base_cfg.bind_gold)
		else
			self.node_list.img_red_gold.image:LoadSpriteAsync(bundle, asset, function ()
				self.node_list.img_red_gold.image:SetNativeSize()
			end)---原本是未绑的图标

			self.node_list.rich_fetch_tips.text.text = string.format(Language.Welfare.RedPaperPackageHaveCountTicket,count,data_base_cfg.num)
			self.node_list.rich_fetch_tips_1.text.text = string.format(Language.Welfare.RedPaperPackageHaveCountTicket1,data_base_cfg.silver_ticket)
		end

		WelfareWGData.Instance:SetCurRedPacketNum(count,data_base_cfg.num)
	end

	if #data_list >= 2 then
		table.sort( data_list, SortTools.KeyUpperSorter("fetch_num") )
		data_list[1].is_max = true
		table.insert(data_list,2,data_list[#data_list])
		table.remove(data_list,#data_list)
	end

	self.red_pocket_list:SetDataList(data_list,0)

	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local anim_end_obj = GuildWGData.Instance:GetRedPacketAnimEndObj()
	self.node_list.chuqianguan:SetActive(person_info.total_bind_gold > 0 or person_info.total_silver_ticket > 0 or anim_end_obj ~= nil)
	-- self.node_list.big_img:SetActive(person_info.total_bind_gold > 0 or person_info.total_silver_ticket > 0 or anim_end_obj ~= nil)
	local is_vip = VipWGData.Instance:IsVip()
	local can_lingqu = is_vip and vip >= other_config.double_fetch_vip and (person_info.total_bind_gold > 0 or person_info.total_silver_ticket > 0)
	XUI.SetButtonEnabled(self.node_list.vip4_lingqu, can_lingqu)
	self.node_list.vip4_btn_effect:SetActive(can_lingqu)
	self.node_list.jubao_effect:SetActive(can_lingqu)

	if vip >= other_config.double_fetch_vip and (person_info.total_bind_gold > 0 or person_info.total_silver_ticket > 0) then
		self.node_list.vip4_lingqu_text.text.text = Language.Welfare.VIP4LingQu1
	else
		self.node_list.vip4_lingqu_text.text.text = Language.Welfare.VIP4LingQu
	end
	local no_gold_man = person_info.total_bind_gold < other_config.total_gold_bind_limit
	local no_yinpiao_man = person_info.total_silver_ticket < other_config.total_silver_ticket
	-- self.node_list.gold_man:SetActive(not no_gold_man)
	-- self.node_list.yinpiao_man:SetActive(not no_yinpiao_man)

	local gold_str = ""
	if person_info.total_bind_gold >= other_config.total_gold_bind_limit then
		gold_str = other_config.total_gold_bind_limit
	else
		gold_str = person_info.total_bind_gold
	end
	local yinpiao_str = 0
	if person_info.total_silver_ticket >= other_config.total_silver_ticket then
		yinpiao_str = other_config.total_silver_ticket
	else
		yinpiao_str = person_info.total_silver_ticket
	end
	self.node_list.gold_lingqu.text.text = gold_str

	local temp_num = math.ceil(gold_count / 2)
	self.yb_old_num = yinpiao_str - temp_num
	self.yb_new_num = yinpiao_str
	local head_data = {}
	head_data.role_id = self.data.owner_uid
	head_data.prof = self.data.prof
	head_data.sex = self.data.sex
	self.head_cell:SetData(head_data)
	-- self:PlayYuanBaoFlyAnimation()
	-- self:PlayAccYuanBaoAnimation()
	local world_get,world_use = self:GetWorldCanUse()
	local guild_get,guild_use = self:GetGuildCanUse()
	local all_use_red = {}
	for k,v in pairs(world_get) do
		table.insert(all_use_red,v)
	end
	for k,v in pairs(world_use) do
		table.insert(all_use_red,v)
	end
	for k,v in pairs(guild_get) do
		table.insert(all_use_red,v)
	end
	for k,v in pairs(guild_use) do
		table.insert(all_use_red,v)
	end
	table.sort(all_use_red, SortTools.KeyUpperSorters("creat_timestamp"))
	self.all_use_red = all_use_red
	self.node_list["next_btn"]:SetActive(#self.all_use_red > 0)
	self.node_list["redpacket_num"].text.text = #self.all_use_red
end

function GuildRedPacketTips:PlayYuanBaoFlyAnimation()
	if not GuildWGData.Instance:GetRedPacketAnimShow() then
		if self.yuanbao_num_isplay then
			self.node_list.yinpiao_lingqu.text.text = self.yb_old_num or 0
		else
			self.node_list.yinpiao_lingqu.text.text = self.yb_new_num or 0
		end
		return
	end

	local show_guan = false
	local all_cfg = WelfareWGData.Instance:GetWelfareCfg()
	local other_config = all_cfg.other_config[1]
	local vip = VipWGData.Instance:GetRoleVipLevel()
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	if not IsEmptyTable(person_info) and not IsEmptyTable(other_config) then
		local can_lingqu = VipWGData.Instance:IsVip() and vip >= other_config.double_fetch_vip
		show_guan = (person_info.total_bind_gold > 0 or person_info.total_silver_ticket > 0) and not can_lingqu
	end

	if show_guan and self.node_list.yinpiao_icon then
		self.yuanbao_num_isplay = true
		GlobalTimerQuest:AddDelayTimer(function ()
			MainuiWGCtrl.Instance:PlayEffectByRechargeSecond("GuildRedPacketTips2", Vector3(0, 0, 0), self.node_list.yinpiao_icon, 5)
			self:YuanBaoGetAnimation()
		end, 0.5)
	elseif self.node_list.yinpiao_icon then
		--如果上述条件不满足就不会赋值了（容错）
		self.node_list.yinpiao_lingqu.text.text = self.yb_new_num or 0
	end

	GuildWGData.Instance:SetRedPacketAnimShow(false)
end

function GuildRedPacketTips:YuanBaoGetAnimation()
	if self.node_list.yinpiao_icon then
		if self.yb_new_num > self.yb_old_num then
			self.node_list.yinpiao_icon.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 1.5)
			local bundle_name, asset_name = ResPath.GetEffectUi("UI_qifu_shouji")
			EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.yinpiao_icon.transform, 1, nil, nil, nil, nil)
			self.node_list["yinpiao_lingqu"].text:DoNumberTo(self.yb_old_num, self.yb_new_num, 1.5, function ()
				self.yuanbao_num_isplay = false
				if self.node_list["yinpiao_lingqu"] then
					self.node_list["yinpiao_lingqu"].text.text = self.yb_new_num
				end
			end)
		else
			self.yuanbao_num_isplay = false
			if self.node_list["yinpiao_lingqu"] then
				self.node_list["yinpiao_lingqu"].text.text = self.yb_new_num
			end
		end
	end
end

function GuildRedPacketTips:PlayAccYuanBaoAnimation()
	local end_obj = GuildWGData.Instance:GetRedPacketAnimEndObj()

	if end_obj == nil or self.yuanbao_acc_isplay then return end
	self.yuanbao_acc_isplay = true
	if not IsNil(self.node_list.jubao_effect_pos.transform) then
		FightWGCtrl.Instance:DoMoneyEffect(GameEnum.NEW_MONEY_BAR.SILVER_TICKET, self.node_list.jubao_effect_pos.transform)
	end

	local temp_time = 0
	local interval = 0.1
	local fly_cd = 0.5
	local total_time = 1.8
	CountDownManager.Instance:AddCountDown("jubaopen_finish_countdown",
	function()
		temp_time = temp_time + 0.1
		if temp_time == fly_cd and self.node_list.jubao_effect_pos then
			MainuiWGCtrl.Instance:PlayEffectByRechargeSecond("RedPacketAcc", self.node_list.jubao_effect_pos, end_obj, 18)
		end
	end,
	function()
		if self.node_list.chuqianguan then
			self.node_list.chuqianguan:SetActive(false)
			-- self.node_list.big_img:SetActive(false)
			GuildWGData.Instance:SetRedPacketAnimEndObj(nil)
		end
		self.yuanbao_acc_isplay = false
	end, nil, total_time, interval)
end

function GuildRedPacketTips:GetRewardDouble()
	--if self.view_paper_type == 1 then
		WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GET_TOTAL,0,0)
	-- else
	-- end

end

function GuildRedPacketTips:SetDataDetailRecord(data,paper_type)
	self.data = data
	self.view_paper_type = paper_type
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function GuildRedPacketTips:OnClickState()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Welfare.RedBagTipTitle)
	role_tip:SetContent(Language.Welfare.RedBagTipState)

end

function GuildRedPacketTips:OnClickCloseVip()
	if self.node_list.chuqianguan then
		self.node_list.chuqianguan:SetActive(false)
		-- self.node_list.big_img:SetActive(false)
	end
end

function GuildRedPacketTips:GetWorldCanUse()
	local world_red_info = WelfareWGData.Instance:GetWorldRedpaperAllInfo()
	local world_can_get = {}
	local world_can_use = {}
	local data_base_cfg = {}
	local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local have_count = false

	for m,n in pairs(world_red_info) do
		if n.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(n.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperSeqCfg(n.paper_type,n.paper_level)
		end
		local is_get_over = false
		local have_get_count = #n.record_list

		for k,v in pairs(n.record_list) do

			if v.uid == role_id then
				is_get_over = true
				break
			end
		end
		local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		if data_base_cfg and data_base_cfg.bind_gold > 0 then
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.vip_gold_bind_max
			else
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.gold_bind_max
			end

		else
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.vip_sliver_ticket_max
			else
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.sliver_ticket_max
			end
		end
		if data_base_cfg and have_get_count < data_base_cfg.num and not is_get_over and have_count then
			local temp = __TableCopy(n)
			temp.is_use = 1
			table.insert(world_can_get,temp)
		end
	end

	local data_lsit = WelfareWGData.Instance:GetGuildSystemRedpaperCfg(2)
	for k,v in pairs(data_lsit) do
		if v.sort_index == 0 then
			local temp = __TableCopy(v)
		temp.is_use = 2
			table.insert(world_can_use,temp)
		end
	end

 	return world_can_get,world_can_use
end
function GuildRedPacketTips:GetGuildCanUse()
	if GuildWGData.Instance:IsHaveGuild() == 1 then
		return {},{}
	end
	local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local world_can_get = {}
	local world_can_use = {}
	local data_base_cfg = {}
	local have_count = false

	local red_all_info = WelfareWGData.Instance:GetGuildRedpaperAllInfo()
	for m,n in pairs(red_all_info) do
		if n.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(n.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(n.paper_type,n.paper_level)
		end

		local is_get_over = false
		local have_get_count = #n.record_list

		for k,v in pairs(n.record_list) do
			if v.uid == role_id then
				is_get_over = true
				break
			end
		end

		local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		if data_base_cfg and data_base_cfg.bind_gold > 0 then
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.vip_gold_bind_max
			else
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.gold_bind_max
			end

		else
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.vip_sliver_ticket_max
			else
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.sliver_ticket_max
			end
		end
		if data_base_cfg and have_get_count < data_base_cfg.num and not is_get_over and have_count then
			local temp = __TableCopy(n)
			temp.is_use = 1
			table.insert(world_can_get,temp)
		end
	end

	local data_lsit = WelfareWGData.Instance:GetSendRedPaperListInfo(true)
	for k,v in pairs(data_lsit) do
		if v.sort_index == 0 then
			local temp = __TableCopy(v)
		temp.is_use = 2
			table.insert(world_can_use,temp)
		end
	end

	return world_can_get,world_can_use
end

function GuildRedPacketTips:OnClickOpenRedPacket()
	if IsEmptyTable(self.all_use_red) then
		return
	end
	local data = self.all_use_red[1]
	if data.is_use == 1 then
		if data.is_guid_packet then
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_GUILD,data.info_index)
			GuildWGCtrl.Instance:OpenGuildRedPacketView(data,1)
		else
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_WORLD,data.info_index)
			GuildWGCtrl.Instance:OpenGuildRedPacketView(data,2)
		end
	else
		if data.type == SPECIAL_SEND_TYPE.GUILD_ANSWER then
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GUILD_SYSTEM_DISTRIBUTE,data.info_index)
		else
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM,data.info_index)
		end
	end
end
-----------------------------------------------------------
RedPocketListItemRender = RedPocketListItemRender or BaseClass(BaseRender)

function RedPocketListItemRender:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function RedPocketListItemRender:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function RedPocketListItemRender:CreateChild()

end

function RedPocketListItemRender:OnFlush()
	if not self.data then return end

	local cur_num,max_num = WelfareWGData.Instance:GetCurRedPacketNum()

	local data_base_cfg = {}
	if self.data.is_world_packet then
		data_base_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfgByTypeAndLevel(self.data.paper_type,self.data.paper_level)
	end

	if IsEmptyTable(data_base_cfg) then
		--if self.data.is_guid_packet then
		if self.data.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(self.data.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(self.data.paper_type,self.data.paper_level)
		end
	end

	if data_base_cfg then
		local bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")

		if data_base_cfg.gold and data_base_cfg.gold > 0 then
			bundle, asset = ResPath.GetCommonIcon("a3_huobi_xianyu")
		end

		self.node_list.img_red_type.image:LoadSpriteAsync(bundle, asset, function ()
			self.node_list.img_red_type.image:SetNativeSize()
		end)

		-- if data_base_cfg.bind_gold and data_base_cfg.bind_gold > 0 then
		-- 	self.node_list.img_red_type.image:LoadSpriteAsync(bundle, asset, function ()
		-- 		self.node_list.img_red_type.image:SetNativeSize()
		-- 	end)
		-- else
		-- 	self.node_list.img_red_type.image:LoadSpriteAsync(bundle, asset, function ()
		-- 		self.node_list.img_red_type.image:SetNativeSize()
		-- 	end)---原本是未绑的图标
		-- end
	end

	self.node_list.lbl_red_gold.text.text = self.data.fetch_num
	self.node_list.img_max:SetActive(self.data.is_max)
	if self.data.owner_game_name == GameVoManager.Instance:GetMainRoleVo().name then
		self.node_list.lbl_name.text.text = string.format(Language.Guild.RedRoleName, self.data.owner_game_name)
	else
		self.node_list.lbl_name.text.text = self.data.owner_game_name
	end

	local head_data = {}
	head_data.role_id = self.data.uid
	head_data.prof = self.data.prof
	head_data.sex = self.data.sex
	self.head_cell:SetData(head_data)
end

function RedPocketListItemRender:CreateSelectEffect()

end

RedPocketDaXieListItemRender = RedPocketDaXieListItemRender or BaseClass(BaseRender)

function RedPocketDaXieListItemRender:__init()

end

function RedPocketDaXieListItemRender:__delete()

end
function RedPocketDaXieListItemRender:LoadCallBack( ... )
	-- XUI.AddClickEventListener(self.node_list["daxie_content_btn"], BindTool.Bind1(self.D, self))

end
function RedPocketDaXieListItemRender:OnFlush()
	if nil == self.data then return end
	self.node_list.daxie_content_text.text.text = self.data.des
end
