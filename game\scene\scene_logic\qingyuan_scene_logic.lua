QingYuanSceneLogic = QingYuanSceneLogic or BaseClass(CommonFbLogic)

function QingYuanSceneLogic:__init()
	self.open_view = false
	self.is_first = true
end

function QingYuanSceneLogic:__delete()

end

function QingYuanSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	local mainui_ctrl = MainuiWGCtrl.Instance
    mainui_ctrl:SetTaskContents(false)
    mainui_ctrl:SetOtherContents(true)
	mainui_ctrl:SetFBNameState(true, Language.Marry.QingYuanFB)
	mainui_ctrl:SetTeamBtnState(false)

	-- 所有下坐骑(普通和战斗坐骑)
	MountWGCtrl.Instance:AllDownMount()
	MarryWGCtrl.Instance:OpenFbTaskView()
	self.not_set_time = true
	self.is_first = true
	local scene_id = Scene.Instance:GetSceneId()
	local param_t = {scene_id = scene_id}
	Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, nil, nil, 11, param_t)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function QingYuanSceneLogic:OpenFbSceneCd()
	local out_time = MarryWGData.Instance:GetFbTimeOut()
	if out_time > TimeWGCtrl.Instance:GetServerTime() then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time)
		self.not_set_time = false
	end
end

function QingYuanSceneLogic:SetStartTimeCutDown()
	local info = MarryWGData.Instance:GetQyFbSceneInfo()
	if info.prepare_end_timestamp > 0 and info.prepare_end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
		UiInstanceMgr.Instance:DoFBStartDown(info.prepare_end_timestamp, function ()
			if self.is_first then
				self:OpenFbSceneCd()
				self.is_first = false
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		end)
	else
		if self.is_first then
			self.is_first = false
			self:OpenFbSceneCd()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end
	local out_time = MarryWGData.Instance:GetFbTimeOut()
	if out_time > 0 and self.not_set_time then
		self:OpenFbSceneCd()
	end
end

function QingYuanSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	--FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function QingYuanSceneLogic:Out()
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	CommonFbLogic.Out(self)
	MarryWGCtrl.Instance:CloseFbTaskView()
	self.is_first = true
	self.not_set_time = true
    MarryWGCtrl.Instance:CloseFbChoose()
	if MarryWGData.Instance:IsNeedOpenFbView() then
		MarryWGCtrl.Instance:Open(MARRY_TAB_TYPE.FB)
	end

	if MarryWGData.Instance:IsSingleEnterFB() and 1 == SocietyWGData.Instance:GetIsInTeam() then
		NewTeamWGCtrl.Instance:ExitTeam()
	end
end

function QingYuanSceneLogic:GetGuajiCharacter()
	local target_obj = nil
	target_obj = self:GetBoss()
	if target_obj ~= nil then
		return target_obj
    else
        return self:GetMonster()
	end
end

function QingYuanSceneLogic:GetGuajiPos()
    local info = MarryWGData.Instance:GetQyFbSceneInfo()
    local wave_num = info.wave_num
	local boss_pos_x, boss_pos_y = MarryWGData.Instance:GetQingyuanFbMonsterPos(wave_num)
	if boss_pos_x then
		return boss_pos_x, boss_pos_y
	end
end

function QingYuanSceneLogic:GetBoss()
	local target_obj = nil
	local monster_list = Scene.Instance:GetMonsterList()
	for k,v in pairs(monster_list) do
		if v:IsDead() == false and v:IsBoss() then
			target_obj = v
			GuajiCache.target_obj = target_obj
			return target_obj
		end
	end
end

function QingYuanSceneLogic:GetMonster()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = 10000--COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	return Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
end
--
function QingYuanSceneLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj ~= nil and not target_obj:IsDeleted() and target_obj:GetType() == SceneObjType.Role then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end


-- 是否是挂机打怪的敌人
function QingYuanSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsDeleted() or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function QingYuanSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end

function QingYuanSceneLogic:CanAutoRotation()
  return false
end