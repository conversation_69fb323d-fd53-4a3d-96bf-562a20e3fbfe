require("game/operation_activity/recharge_rank/operation_activity_recharge_rank_wg_data")
require("game/operation_activity/recharge_rank/operation_activity_recharge_rank_tips")

--运营活动-充值排行
OpRechargeRankWGCtrl = OpRechargeRankWGCtrl or BaseClass(BaseWGCtrl)

function OpRechargeRankWGCtrl:__init()
	if OpRechargeRankWGCtrl.Instance then
		ErrorLog("[OpRechargeRankWGCtrl] Attemp to create a singleton twice !")
	end
	OpRechargeRankWGCtrl.Instance = self

    self.data = OpRechargeRankWGData.New()
	self.tips_view = OperationActReRankTipsView.New()

	self:BindGlobalEvent(AuditEvent.RECHARGE_CHANGE, BindTool.Bind1(self.RechargeChange, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/randactivityconfig_1_auto", BindTool.Bind(self.OnHotUpdate, self))

	self:RegisterAllProtocols()
end

function OpRechargeRankWGCtrl:__delete()
	OpRechargeRankWGCtrl.Instance = nil

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.tips_view then
		self.tips_view:DeleteMe()
		self.tips_view = nil
	end

end

function OpRechargeRankWGCtrl:OnHotUpdate()
	self.data:LoadConfig()
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_recharge_rank)
end

function OpRechargeRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRASpeedRechargeInfo, 'OnSCRASpeedRechargeInfo')
	self:RegisterProtocol(SCOAChongZhiRank2RankInfo, 'OnSCOAChongZhiRank2RankInfo')
end

function OpRechargeRankWGCtrl:SendOpRechargeRankReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function OpRechargeRankWGCtrl:OnSCRASpeedRechargeInfo(protocol)
	self.data:SetRechargeInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_recharge_rank)
	if self.tips_view:IsOpen() then
		self.tips_view:Flush()
	end
end

function OpRechargeRankWGCtrl:OnSCOAChongZhiRank2RankInfo(protocol)
	self.data:SetOpReRankSort(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_recharge_rank)
	if self.tips_view:IsOpen() then
		self.tips_view:Flush()
	end
end

function OpRechargeRankWGCtrl:OpenRankTipsView()
	self.tips_view:Open()
end

function OpRechargeRankWGCtrl:RechargeChange(acc_total_gold)
	OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.INFO)
	OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.RANK_INFO)
end