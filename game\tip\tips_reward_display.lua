TipsRewardDisplayView = TipsRewardDisplayView or BaseClass(SafeBaseView)

function TipsRewardDisplayView:__init()
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/rolebag_ui_prefab", "layout_reward_display")
end

function TipsRewardDisplayView:ReleaseCallBack()
    if nil ~= self.display_reward_list then
		self.display_reward_list:DeleteMe()
		self.display_reward_list = nil
	end

    self.id_list = nil
end

function TipsRewardDisplayView:LoadCallBack()
    self.display_reward_list = AsyncListView.New(DisplayRewardCell, self.node_list.display_reward_list)
    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind1(self.OnSure, self))
end

function TipsRewardDisplayView:SetData(id_list)
    self.id_list = id_list
end

function TipsRewardDisplayView:OnFlush()
    local data_list = self.id_list
    if not IsEmptyTable(data_list) then
        self.display_reward_list:SetDataList(data_list)
        self.display_reward_list:SetStartZeroIndex(true)
    end
end

function TipsRewardDisplayView:OnSure()
	self:Close()
end




DisplayRewardCell = DisplayRewardCell or BaseClass(BaseRender)
function DisplayRewardCell:ReleaseCallBack()
    if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end

function DisplayRewardCell:LoadCallBack()
    self.base_cell = ItemCell.New(self.node_list["cell"])
end

function DisplayRewardCell:OnFlush()
	if self.data and self.node_list.cell_name then
        self.base_cell:SetData(self.data)

		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_cfg then
			self.node_list.cell_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		else
			self.node_list.cell_name.text.text = ""
		end
	end
end