---------运势----------
function QiFuView:InitYunShiView()
	XUI.AddClickEventListener(self.node_list["change_yunshi_btn"], BindTool.Bind(self.ClickChangeYunShiBtn,self))
	XUI.AddClickEventListener(self.node_list["item_icon_btn"], BindTool.Bind(self.ClickItemIcon,self))
	XUI.AddClickEventListener(self.node_list["item_toggle"], BindTool.Bind(self.ClickItemToggle,self))

	XUI.AddClickEventListener(self.node_list["qiantong_img"], BindTool.Bind(self.OnChouQianBtn,self))

	self.yunshi_list_view = AsyncListView.New(YunShiListItem,self.node_list["yunshi_list_view"])
    self.yunshi_list_view:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize, self))

	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
	refresh_times = refresh_times + 1
	local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
	refresh_times = refresh_times >= max_timer and max_timer or refresh_times
	-- local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
	-- if not IsEmptyTable(refresh_cfg) then
	-- 	local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
	-- 	self.node_list["item_toggle"].toggle.isOn = has_num >= refresh_cfg.cost_item_num
	-- end

	--self.item_toggle_ison = self.node_list["item_toggle"].toggle.isOn
	self.item_toggle_ison = false
end

function QiFuView:DelYunShiView()
	if self.yunshi_list_view then
		self.yunshi_list_view:DeleteMe()
		self.yunshi_list_view = nil
	end

	if self.yunshi_alert then
		self.yunshi_alert:DeleteMe()
		self.yunshi_alert = nil
	end

	if self.beat_yunshi_alert then
		self.beat_yunshi_alert:DeleteMe()
		self.beat_yunshi_alert = nil
	end

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	self:CancelTween()
end

function QiFuView:YunShiCloseCallBack()
	QifuYunShiWGData.Instance:SetSendChouQianFlag(false)
	self:ClearChouQianTimer(true)
end

function QiFuView:FlushYunShiView()
	local has_fortune = QifuYunShiWGData.Instance:GetToDayHasFortune()
	self.node_list["chouqian_root"]:SetActive(not has_fortune)
	self.node_list["yunshi_root"]:SetActive(has_fortune)

	if has_fortune then
		self:FlushHasYunShi()
	end
end

local YunShiColor = {
	[1] = "#0098d1",
	[2] = "#8000bc",
	[3] = "#bc5400",
	[4] = "#f10c14",
}
function QiFuView:FlushHasYunShi()
	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local fortune_type = fortune_info.fortune_type
	local add_list = QifuYunShiWGData.Instance:GetCurMyFortuneAdditionCfg()
	self.yunshi_list_view:SetDataList(add_list)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_str = TimeUtil.FormarDaXieTimeYMD(server_time)
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
	if not IsEmptyTable(fortune_cfg) then
		self.node_list["yunshi_icon"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_qf_ys"..fortune_type))
		self.node_list["yunshi_icon"].raw_image:SetNativeSize()
		self.node_list["yunshi_name"].text.text = fortune_cfg.name --运势类型名字

		local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
		refresh_times = refresh_times + 1
		local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
		refresh_times = refresh_times >= max_timer and max_timer or refresh_times
		local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
		if not IsEmptyTable(refresh_cfg) then
			local item_cfg = ItemWGData.Instance:GetItemConfig(refresh_cfg.cost_item_id)
			if not IsEmptyTable(item_cfg) then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
				local num_color = has_num >= refresh_cfg.cost_item_num and COLOR3B.GREEN or COLOR3B.RED
				local need_num = refresh_cfg.cost_item_num
				local num_str = string.format("%s/%s",has_num,need_num)
				self.node_list["item_num_text"].text.text = num_str
				self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
			end
		end
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.item_num_root.rect)
	end
end

local LINE_SPACING = 10
function QiFuView:ChangeCellSize(data_index)
	local data_list = QifuYunShiWGData.Instance:GetCurMyFortuneAdditionCfg()
    if not data_list or not data_list[data_index + 1] then return 0 end

    local des_str = data_list[data_index + 1].des
    if not des_str then
        return 0
    end

    self.node_list["TestText"].text.text = des_str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function QiFuView:ClickChouQianBtn()
	local send_fun = function ()
		QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,0)
	end
	QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
	QifuYunShiWGData.Instance:SetFirstSendChouQianFlag(true)
	send_fun()
	-- self:PlayChouQianAnim(send_fun)
end

function QiFuView:ClickChangeYunShiBtn()
	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local refresh_times = fortune_info.refresh_times > 0 and fortune_info.refresh_times or 1
	refresh_times = refresh_times + 1
	local max_timer = QifuYunShiWGData.Instance:GetMaxRefreshTimer()
	refresh_times = refresh_times >= max_timer and max_timer or refresh_times
	local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(refresh_times)
	local fortune_type = fortune_info.fortune_type
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)

	local try_reflush_yunshi = function ()
		if not IsEmptyTable(refresh_cfg) then
			-- if self.item_toggle_ison then
			-- 	local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
			-- 	local need_num = refresh_cfg.cost_item_num or 0
			-- 	if has_num >= need_num then
			-- 		QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
			-- 		QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,0)
			-- 	else
			-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.YunShi.ItemNotEnough)
			-- 	end
			-- else
			local has_num = ItemWGData.Instance:GetItemNumInBagById(refresh_cfg.cost_item_id)
			local need_num = refresh_cfg.cost_item_num or 0
			if has_num >= need_num then
				QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
				QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,0)
			else
				local str = string.format(Language.YunShi.UseGoldChangeYunShi,refresh_cfg.cost_gold)
				local ok_fun = function ()
					local enough_gold = RoleWGData.Instance:GetIsEnoughUseGold(refresh_cfg.cost_gold)
					if not enough_gold then
						VipWGCtrl.Instance:OpenTipNoGold()
						return
					end
					local send_fun = function ()
						QiFuWGCtrl.Instance:SendCSFortuneOperate(FORTUNE_OPERATE_TYPE.FORTUNE_OPERATE_TYPE_REFRESH_TYPE,1)
					end
					QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
					send_fun()
					-- self:PlayChouQianAnim(send_fun)
				end
	
				if not self.yunshi_alert then
					self.yunshi_alert = Alert.New()
				end
				self.yunshi_alert:SetShowCheckBox(true, "qifu_yunshi_alert")
				self.yunshi_alert:SetOkFunc(ok_fun)
				self.yunshi_alert:SetLableString(str)
				self.yunshi_alert:SetCheckBoxDefaultSelect(false)
				self.yunshi_alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
				self.yunshi_alert:Open()
			end
			--end
		end
	end

	if fortune_cfg.is_best == 1 then
		if not self.beat_yunshi_alert then
			self.beat_yunshi_alert = Alert.New()
		end

		self.beat_yunshi_alert:SetShowCheckBox(true, "beat_yunshi_alert")
		self.beat_yunshi_alert:SetOkFunc(try_reflush_yunshi)
		self.beat_yunshi_alert:SetLableString(Language.YunShi.IsBestAlert)
		self.beat_yunshi_alert:SetCheckBoxDefaultSelect(false)
		self.beat_yunshi_alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
		self.beat_yunshi_alert:Open()
	else
		try_reflush_yunshi()
	end
end

function QiFuView:PlayChouQianAnim(callback)
	self.node_list["chouqian_root"]:SetActive(true)
	self.node_list["yunshi_root"]:SetActive(false)

	QifuYunShiWGData.Instance:SetSendChouQianFlag(true)
	local ignoire_anim_flag = QifuYunShiWGData.Instance:GetIgnoireAnimFlag()
	if ignoire_anim_flag then
		if callback then
    		callback()
    	end
		return
	end

	self:CancelTween()
	local tween = DG.Tweening.DOTween.Sequence()
    tween:SetEase(DG.Tweening.Ease.Linear)
	tween:OnComplete(function ()
		if self.ske_graphic_chouqian then
	    	self.ske_graphic_chouqian.AnimationState:SetAnimation(0, "action", true)
	    end
		TryDelayCall(self, function ()
			if callback then
	    		callback()
	    	end
		end, 1, "yunshi_chouqian_img_anim_delay")
	end)

	self.show_tweener = tween
end

function QiFuView:CancelTween()
    if self.show_tweener then
        self.show_tweener:Kill()
        self.show_tweener = nil
    end
end

function QiFuView:YunShiAnim()
	local qian_obj_start_rotation = u3dpool.vec3(0, 0, 10)

	local yunshi_anim_obj = self.node_list["yunshi_anim_root"].gameObject
	local yunshi_anim_obj_x, yunshi_anim_obj_y = yunshi_anim_obj.transform.anchoredPosition.x, yunshi_anim_obj.transform.anchoredPosition.y
	yunshi_anim_obj.transform.anchoredPosition = u3dpool.vec3(0, 740, 0)

	local yunshi_name_obj = self.node_list["yunshi_icon"].gameObject
	yunshi_name_obj:SetActive(false)

	local yunshi_anim_tween1 = yunshi_anim_obj.transform:DOAnchorPos(u3dpool.vec3(yunshi_anim_obj_x, yunshi_anim_obj_y, 0), 0.25)

	if self.anim_tween then
		self.anim_tween:Kill()
		self.anim_tween = nil
	end

	self.anim_tween = DG.Tweening.DOTween.Sequence()

	self.anim_tween:Append(yunshi_anim_tween1)
	self.anim_tween:SetEase(DG.Tweening.Ease.Linear)
	self.anim_tween:OnComplete(function ()
	local fortune_info = QifuYunShiWGData.Instance:GetFortuneInfo()
	local fortune_type = fortune_info.fortune_type
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(fortune_type)
	yunshi_name_obj:SetActive(true)
	UITween.ScaleShowPanel(yunshi_name_obj,u3dpool.vec3(5, 5, 5))
	if not IsEmptyTable(fortune_cfg) and fortune_cfg.is_best == 1 then
		self.paly_anim_end = true
	end
	-- local bundle, asset = ResPath.GetEffectUi("UI_yunshi_sg_001")
	-- EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["yunshi_anim_effect"].transform,1)
	end)
end

function QiFuView:PlayYunShiAnim()
	self:YunShiAnim()
end

function QiFuView:PlayYunShiAnimEffect()
	local ignoire_anim_flag = QifuYunShiWGData.Instance:GetIgnoireAnimFlag()
	if ignoire_anim_flag then
		local bundle, asset = ResPath.GetEffectUi("UI_yunshi_sg_001")
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["yunshi_anim_effect"].transform,1)
	end
end

function QiFuView:ClickIgnoreAnimToggle(ison)
	QifuYunShiWGData.Instance:SetIgnoireAnimFlag(ison)
end


function QiFuView:ClickItemToggle(ison)
	self.item_toggle_ison = ison
end

function QiFuView:ClickItemIcon()
	local refresh_cfg = QifuYunShiWGData.Instance:GetFortuneRefreshCfg(1)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = refresh_cfg.cost_item_id})
end

function QiFuView:SetYunShiActive(flag)
	self.node_list["yunshi_root"]:SetActive(flag)
end

function QiFuView:OnChouQianBtn()
	if not self.chouqian_ani_timer then
		self.node_list.chouqian_root.animator:SetBool("is_play", true)
		local ani_clip_length = self.node_list.chouqian_root.animator:GetAnimationClip("chouqian_ani").length or 1
		self.node_list.qiantong_img.button.interactable = false
		self.chouqian_ani_timer = GlobalTimerQuest:AddDelayTimer(function()
			self:ClickChouQianBtn()
			self:ClearChouQianTimer()
		end, ani_clip_length + 0.6, 1)
	end
end

function QiFuView:ClearChouQianTimer(is_need_seq)
	if self.chouqian_ani_timer then
        GlobalTimerQuest:CancelQuest(self.chouqian_ani_timer)
		self.chouqian_ani_timer = nil

		if is_need_seq then
			self:ClickChouQianBtn()
		end
	end
end

YunShiListItem = YunShiListItem or BaseClass(BaseRender)

function YunShiListItem:OnFlush()
	if not self.data then return end
	self.node_list["desc_text"].text.text = self.data.des

	GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list["yunshi_list_item"] and self.node_list["desc_text"] then
			self.node_list["yunshi_list_item"].layout_element.minHeight = self.node_list["desc_text"].rect.rect.height + 10
		end
	end, 0.1, 1)
end