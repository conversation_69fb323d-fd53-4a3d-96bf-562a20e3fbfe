FengShenBangFBView = FengShenBangFBView or BaseClass(SafeBaseView)

function FengShenBangFBView:__init(view_name)
	self.view_name = "FengShenBangFBView"
	self:AddViewResource(0, "uis/view/fengshenbang_prefab", "layout_fb_panel")
end

function FengShenBangFBView:LoadCallBack()
	self.save_pos_x = self.node_list.left_panel.rect.anchoredPosition.x
	self.show_or_hide = GlobalEventSystem:Bind(MainUIEventType.SHOW_OR_HIDE_TASK_BUTTON, BindTool.Bind(self.ShowOrHide, self))
end

function FengShenBangFBView:ReleaseCallBack()
	if self.show_or_hide then
        GlobalEventSystem:UnBind(self.show_or_hide)
        self.show_or_hide = nil
    end
end

function FengShenBangFBView:OnFlush(param_t)
	self:RefreshView()
end

function FengShenBangFBView:RefreshView()
	local scene_id = FengShenBangWGData.Instance:GetFSBSceneId()
	local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataBySceneID(scene_id)
	local monster_cfg = scene_cfg and BossWGData.Instance:GetMonsterInfo(scene_cfg.boss_id)
	if not monster_cfg then
		self.node_list.target_label.text.text = ""
	end
	local other_cfg = FengShenBangWGData.Instance:GetActCfgList("other")
	local limit_time = other_cfg.scene_limit_time or 0
	limit_time = TimeUtil.FormatSecondDHM2(limit_time)
	local count = 0
	local scene_info = FengShenBangWGData.Instance:GetSceneInfo()
	if scene_info and scene_info.pass then
		count = 1
	end
	self.node_list.target_label.text.text = string.format(Language.FengShenBang.FBTargetLable, monster_cfg.name, count, limit_time)

	local bundle,asset = ResPath.GetTitleModel(scene_cfg.title_id)
	self.node_list.title_img:ChangeAsset(bundle, asset)
end

function FengShenBangFBView:ShowOrHide(isOn)
    if isOn then
    	self.node_list.left_panel.rect:DOAnchorPosX(0, 0.3)
    else
    	local tween = self.node_list.left_panel.rect:DOAnchorPosX(self.save_pos_x, 0.35)
        tween:SetEase(DG.Tweening.Ease.Linear)
    end
end