﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class EnhancedUI_EnhancedScroller_EnhancedScrollerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(EnhancedUI.EnhancedScroller.EnhancedScroller), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("GetPositionBeforeEnum", GetPositionBeforeEnum);
		<PERSON><PERSON>RegFunction("GetPositionAfterEnum", GetPositionAfterEnum);
		L.RegFunction("GetCellView", GetCellView);
		<PERSON>.RegFunction("ReloadData", ReloadData);
		<PERSON><PERSON>RegFunction("RefreshActiveCellViews", RefreshActiveCellViews);
		<PERSON>.RegFunction("RefreshActiveCellViewAtIndex", RefreshActiveCellViewAtIndex);
		<PERSON><PERSON>RegFunction("RefreshAndReloadActiveCellViews", RefreshAndReloadActiveCellViews);
		<PERSON><PERSON>un<PERSON>("ClearAll", ClearAll);
		<PERSON><PERSON>Function("ClearActive", ClearActive);
		<PERSON><PERSON>Function("ClearRecycled", ClearRecycled);
		L.RegFunction("ToggleLoop", ToggleLoop);
		L.RegFunction("JumpToDataIndex", JumpToDataIndex);
		L.RegFunction("JumpToDataIndexForce", JumpToDataIndexForce);
		L.RegFunction("Snap", Snap);
		L.RegFunction("SnapTest", SnapTest);
		L.RegFunction("JumpToCellIndex", JumpToCellIndex);
		L.RegFunction("GetScrollPositionForCellViewIndex", GetScrollPositionForCellViewIndex);
		L.RegFunction("GetScrollPositionForDataIndex", GetScrollPositionForDataIndex);
		L.RegFunction("GetCellViewIndexAtPosition", GetCellViewIndexAtPosition);
		L.RegFunction("ResetItemSize", ResetItemSize);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("scrollDirection", get_scrollDirection, set_scrollDirection);
		L.RegVar("spacing", get_spacing, set_spacing);
		L.RegVar("padding", get_padding, set_padding);
		L.RegVar("snapping", get_snapping, set_snapping);
		L.RegVar("snapVelocityThreshold", get_snapVelocityThreshold, set_snapVelocityThreshold);
		L.RegVar("snapWatchOffset", get_snapWatchOffset, set_snapWatchOffset);
		L.RegVar("snapJumpToOffset", get_snapJumpToOffset, set_snapJumpToOffset);
		L.RegVar("snapCellCenterOffset", get_snapCellCenterOffset, set_snapCellCenterOffset);
		L.RegVar("snapUseCellSpacing", get_snapUseCellSpacing, set_snapUseCellSpacing);
		L.RegVar("snapTweenType", get_snapTweenType, set_snapTweenType);
		L.RegVar("snapTweenTime", get_snapTweenTime, set_snapTweenTime);
		L.RegVar("cellViewVisibilityChanged", get_cellViewVisibilityChanged, set_cellViewVisibilityChanged);
		L.RegVar("cellViewWillRecycle", get_cellViewWillRecycle, set_cellViewWillRecycle);
		L.RegVar("scrollerScrolled", get_scrollerScrolled, set_scrollerScrolled);
		L.RegVar("scrollerSnapped", get_scrollerSnapped, set_scrollerSnapped);
		L.RegVar("scrollerScrollingChanged", get_scrollerScrollingChanged, set_scrollerScrollingChanged);
		L.RegVar("scrollerTweeningChanged", get_scrollerTweeningChanged, set_scrollerTweeningChanged);
		L.RegVar("scrollerEndScrolled", get_scrollerEndScrolled, set_scrollerEndScrolled);
		L.RegVar("snapJumpToCenterScrolled", get_snapJumpToCenterScrolled, set_snapJumpToCenterScrolled);
		L.RegVar("reverseArrangement", get_reverseArrangement, set_reverseArrangement);
		L.RegVar("isAccordingToScrollDirSnap", get_isAccordingToScrollDirSnap, set_isAccordingToScrollDirSnap);
		L.RegVar("pivot", get_pivot, set_pivot);
		L.RegVar("textAnchor", get_textAnchor, set_textAnchor);
		L.RegVar("snapJumpToCenter", get_snapJumpToCenter, set_snapJumpToCenter);
		L.RegVar("isNeedCallSnap", get_isNeedCallSnap, set_isNeedCallSnap);
		L.RegVar("isValueChanging", get_isValueChanging, set_isValueChanging);
		L.RegVar("calcSnapTime", get_calcSnapTime, set_calcSnapTime);
		L.RegVar("limitCallSanpTime", get_limitCallSanpTime, set_limitCallSanpTime);
		L.RegVar("Delegate", get_Delegate, set_Delegate);
		L.RegVar("ScrollPosition", get_ScrollPosition, set_ScrollPosition);
		L.RegVar("ScrollSize", get_ScrollSize, null);
		L.RegVar("NormalizedScrollPosition", get_NormalizedScrollPosition, null);
		L.RegVar("Loop", get_Loop, set_Loop);
		L.RegVar("ScrollbarVisibility", get_ScrollbarVisibility, set_ScrollbarVisibility);
		L.RegVar("Velocity", get_Velocity, set_Velocity);
		L.RegVar("LinearVelocity", get_LinearVelocity, set_LinearVelocity);
		L.RegVar("IsScrolling", get_IsScrolling, null);
		L.RegVar("IsTweening", get_IsTweening, null);
		L.RegVar("StartCellViewIndex", get_StartCellViewIndex, null);
		L.RegVar("EndCellViewIndex", get_EndCellViewIndex, null);
		L.RegVar("StartDataIndex", get_StartDataIndex, null);
		L.RegVar("EndDataIndex", get_EndDataIndex, null);
		L.RegVar("NumberOfCells", get_NumberOfCells, null);
		L.RegVar("ScrollRect", get_ScrollRect, null);
		L.RegVar("ScrollRectSize", get_ScrollRectSize, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPositionBeforeEnum(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum o = obj.GetPositionBeforeEnum();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPositionAfterEnum(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum o = obj.GetPositionAfterEnum();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCellView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView arg0 = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScrollerCellView>(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView o = obj.GetCellView(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReloadData(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				obj.ReloadData();
				return 0;
			}
			else if (count == 2)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.ReloadData(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EnhancedUI.EnhancedScroller.EnhancedScroller.ReloadData");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshActiveCellViews(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.RefreshActiveCellViews();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshActiveCellViewAtIndex(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.RefreshActiveCellViewAtIndex(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshAndReloadActiveCellViews(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.RefreshAndReloadActiveCellViews(arg0);
				return 0;
			}
			else if (count == 3)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.RefreshAndReloadActiveCellViews(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EnhancedUI.EnhancedScroller.EnhancedScroller.RefreshAndReloadActiveCellViews");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearAll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.ClearAll();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearActive(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.ClearActive();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearRecycled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.ClearRecycled();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToggleLoop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.ToggleLoop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpToDataIndex(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				obj.JumpToDataIndex(arg0);
				return 0;
			}
			else if (count == 3)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.JumpToDataIndex(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.JumpToDataIndex(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				obj.JumpToDataIndex(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				obj.JumpToDataIndex(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 7)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				obj.JumpToDataIndex(arg0, arg1, arg2, arg3, arg4, arg5);
				return 0;
			}
			else if (count == 8)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				System.Action arg6 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 8);
				obj.JumpToDataIndex(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				return 0;
			}
			else if (count == 9)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				System.Action arg6 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 8);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 9);
				obj.JumpToDataIndex(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EnhancedUI.EnhancedScroller.EnhancedScroller.JumpToDataIndex");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpToDataIndexForce(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				obj.JumpToDataIndexForce(arg0);
				return 0;
			}
			else if (count == 3)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.JumpToDataIndexForce(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.JumpToDataIndexForce(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				obj.JumpToDataIndexForce(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				obj.JumpToDataIndexForce(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 7)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				obj.JumpToDataIndexForce(arg0, arg1, arg2, arg3, arg4, arg5);
				return 0;
			}
			else if (count == 8)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg4 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 6, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				System.Action arg6 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 8);
				obj.JumpToDataIndexForce(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EnhancedUI.EnhancedScroller.EnhancedScroller.JumpToDataIndexForce");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Snap(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.Snap();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SnapTest(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SnapTest(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpToCellIndex(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				obj.JumpToCellIndex();
				return 0;
			}
			else if (count == 2)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				obj.JumpToCellIndex(arg0);
				return 0;
			}
			else if (count == 3)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				obj.JumpToCellIndex(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.JumpToCellIndex(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: EnhancedUI.EnhancedScroller.EnhancedScroller.JumpToCellIndex");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetScrollPositionForCellViewIndex(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum arg1 = (EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum)ToLua.CheckObject(L, 3, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum));
			float o = obj.GetScrollPositionForCellViewIndex(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetScrollPositionForDataIndex(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum arg1 = (EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum)ToLua.CheckObject(L, 3, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.CellViewPositionEnum));
			float o = obj.GetScrollPositionForDataIndex(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCellViewIndexAtPosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			int o = obj.GetCellViewIndexAtPosition(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetItemSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 1);
			obj.ResetItemSize();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollDirection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollDirectionEnum ret = obj.scrollDirection;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollDirection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.spacing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_padding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.RectOffset ret = obj.padding;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index padding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.snapping;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapVelocityThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.snapVelocityThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapVelocityThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapWatchOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.snapWatchOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapWatchOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapJumpToOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.snapJumpToOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapJumpToOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapCellCenterOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.snapCellCenterOffset;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapCellCenterOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapUseCellSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.snapUseCellSpacing;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapUseCellSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapTweenType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType ret = obj.snapTweenType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapTweenType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapTweenTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.snapTweenTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapTweenTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cellViewVisibilityChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate ret = obj.cellViewVisibilityChanged;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewVisibilityChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cellViewWillRecycle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate ret = obj.cellViewWillRecycle;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewWillRecycle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollerScrolled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate ret = obj.scrollerScrolled;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerScrolled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollerSnapped(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate ret = obj.scrollerSnapped;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerSnapped on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollerScrollingChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate ret = obj.scrollerScrollingChanged;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerScrollingChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollerTweeningChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate ret = obj.scrollerTweeningChanged;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerTweeningChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollerEndScrolled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate ret = obj.scrollerEndScrolled;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerEndScrolled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapJumpToCenterScrolled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate ret = obj.snapJumpToCenterScrolled;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapJumpToCenterScrolled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reverseArrangement(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.reverseArrangement;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reverseArrangement on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isAccordingToScrollDirSnap(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.isAccordingToScrollDirSnap;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isAccordingToScrollDirSnap on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pivot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.pivot;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pivot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textAnchor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.TextAnchor ret = obj.textAnchor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textAnchor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapJumpToCenter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.snapJumpToCenter;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapJumpToCenter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isNeedCallSnap(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.isNeedCallSnap;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isNeedCallSnap on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isValueChanging(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.isValueChanging;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isValueChanging on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_calcSnapTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.calcSnapTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index calcSnapTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_limitCallSanpTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.limitCallSanpTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index limitCallSanpTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Delegate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.IEnhancedScrollerDelegate ret = obj.Delegate;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Delegate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.ScrollPosition;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.ScrollSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NormalizedScrollPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.NormalizedScrollPosition;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NormalizedScrollPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Loop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.Loop;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Loop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollbarVisibility(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollbarVisibilityEnum ret = obj.ScrollbarVisibility;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollbarVisibility on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Velocity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.Vector2 ret = obj.Velocity;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Velocity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LinearVelocity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.LinearVelocity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LinearVelocity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsScrolling(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.IsScrolling;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsScrolling on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsTweening(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool ret = obj.IsTweening;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsTweening on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_StartCellViewIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			int ret = obj.StartCellViewIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index StartCellViewIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EndCellViewIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			int ret = obj.EndCellViewIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EndCellViewIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_StartDataIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			int ret = obj.StartDataIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index StartDataIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EndDataIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			int ret = obj.EndDataIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EndDataIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NumberOfCells(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			int ret = obj.NumberOfCells;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NumberOfCells on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollRect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.UI.ScrollRect ret = obj.ScrollRect;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollRect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ScrollRectSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float ret = obj.ScrollRectSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollRectSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollDirection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollDirectionEnum arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollDirectionEnum)ToLua.CheckObject(L, 2, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollDirectionEnum));
			obj.scrollDirection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollDirection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_spacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.spacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_padding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.RectOffset arg0 = (UnityEngine.RectOffset)ToLua.CheckObject<UnityEngine.RectOffset>(L, 2);
			obj.padding = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index padding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.snapping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapVelocityThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.snapVelocityThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapVelocityThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapWatchOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.snapWatchOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapWatchOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapJumpToOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.snapJumpToOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapJumpToOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapCellCenterOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.snapCellCenterOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapCellCenterOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapUseCellSpacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.snapUseCellSpacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapUseCellSpacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapTweenType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType)ToLua.CheckObject(L, 2, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.TweenType));
			obj.snapTweenType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapTweenType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapTweenTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.snapTweenTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapTweenTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cellViewVisibilityChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate arg0 = (EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.CellViewVisibilityChangedDelegate>(L, 2);
			obj.cellViewVisibilityChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewVisibilityChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cellViewWillRecycle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate arg0 = (EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.CellViewWillRecycleDelegate>(L, 2);
			obj.cellViewWillRecycle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewWillRecycle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollerScrolled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate arg0 = (EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.ScrollerScrolledDelegate>(L, 2);
			obj.scrollerScrolled = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerScrolled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollerSnapped(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate arg0 = (EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.ScrollerSnappedDelegate>(L, 2);
			obj.scrollerSnapped = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerSnapped on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollerScrollingChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate arg0 = (EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.ScrollerScrollingChangedDelegate>(L, 2);
			obj.scrollerScrollingChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerScrollingChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollerTweeningChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate arg0 = (EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.ScrollerTweeningChangedDelegate>(L, 2);
			obj.scrollerTweeningChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerTweeningChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollerEndScrolled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate arg0 = (EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.ScrollerScrollingEndDelegate>(L, 2);
			obj.scrollerEndScrolled = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollerEndScrolled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapJumpToCenterScrolled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate arg0 = (EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate)ToLua.CheckDelegate<EnhancedUI.EnhancedScroller.ScrollerSnapJumpToCenterDelegate>(L, 2);
			obj.snapJumpToCenterScrolled = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapJumpToCenterScrolled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_reverseArrangement(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.reverseArrangement = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index reverseArrangement on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isAccordingToScrollDirSnap(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isAccordingToScrollDirSnap = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isAccordingToScrollDirSnap on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pivot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.pivot = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pivot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_textAnchor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.TextAnchor arg0 = (UnityEngine.TextAnchor)ToLua.CheckObject(L, 2, typeof(UnityEngine.TextAnchor));
			obj.textAnchor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textAnchor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapJumpToCenter(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.snapJumpToCenter = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapJumpToCenter on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isNeedCallSnap(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isNeedCallSnap = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isNeedCallSnap on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isValueChanging(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isValueChanging = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isValueChanging on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_calcSnapTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.calcSnapTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index calcSnapTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_limitCallSanpTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.limitCallSanpTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index limitCallSanpTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Delegate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.IEnhancedScrollerDelegate arg0 = (EnhancedUI.EnhancedScroller.IEnhancedScrollerDelegate)ToLua.CheckObject<EnhancedUI.EnhancedScroller.IEnhancedScrollerDelegate>(L, 2);
			obj.Delegate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Delegate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ScrollPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ScrollPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Loop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Loop = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Loop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ScrollbarVisibility(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollbarVisibilityEnum arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollbarVisibilityEnum)ToLua.CheckObject(L, 2, typeof(EnhancedUI.EnhancedScroller.EnhancedScroller.ScrollbarVisibilityEnum));
			obj.ScrollbarVisibility = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ScrollbarVisibility on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Velocity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.Velocity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Velocity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_LinearVelocity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScroller obj = (EnhancedUI.EnhancedScroller.EnhancedScroller)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.LinearVelocity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LinearVelocity on a nil value");
		}
	}
}

