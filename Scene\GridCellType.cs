﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

/// <summary>
/// The grid cell type.
/// </summary>
public enum GridCellType
{
    /// <summary>
    /// The cell is obstacle.
    /// </summary>
    Obstacle = 0,

    /// <summary>
    /// The cell is a way.
    /// </summary>
    Way = 1,

    /// <summary>
    /// The cell is a safe area.
    /// </summary>
    Safe = 2,

    /// <summary>
    /// The obstacle cell but support find way.
    /// </summary>
    ObstacleWay = 3,

    ///<summary>
    /// The cell is water.
    ///</summary>
    Water = 4,

    /// <summary>
    /// The road for path finding.
    /// </summary>
    Road = 5,

    /// <summary>
    /// 场景水区域显示人物水波纹
    /// </summary>
    WaterRipple = 6,

    /// <summary>
    /// The cell is a client block.
    /// </summary>
    ClientBlock = 7,

    Tunnel = 8,

    Border = 9,

    HighArea = 10,
}
