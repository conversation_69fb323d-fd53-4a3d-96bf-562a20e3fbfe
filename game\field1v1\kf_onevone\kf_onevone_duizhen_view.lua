KFOneVOneDuiZhenView = KFOneVOneDuiZhenView or BaseClass(SafeBaseView)

--对阵界面两个状态 一个对阵表 一个竞猜界面
KFOneVOneDuiZhenView.DuiZhenIndex = {
	DuiZhen = 1,
	JingCai = 2,
}

KFOneVOneDuiZhenView.Lunci = {
	[1] = 8,
	[2] = 4,
	[3] = 2,
	[4] = 1,
}

KFOneVOneDataList = 16
function KFOneVOneDuiZhenView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_onevone_duizhen")
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
end

function KFOneVOneDuiZhenView:LoadCallBack()
	self.cur_duizhen_index = KFOneVOneDuiZhenView.DuiZhenIndex.DuiZhen

	XUI.AddClickEventListener(self.node_list["jingcai_btn"],BindTool.Bind(self.ClickJingCaiBtn,self))
	XUI.AddClickEventListener(self.node_list["jingcai_parent"],BindTool.Bind(self.ClickCloseJingCai,self))
	XUI.AddClickEventListener(self.node_list["touzhu_btn"],BindTool.Bind(self.ClickTouZhuBtn,self))

	self.guanjun_head = BaseHeadCell.New(self.node_list["guanjun_head"])

	local duizhen_group_obj = self.node_list["duizhen_group"].transform
	self.duizhen_groups = {}
	self.duizhen_group_obj_list = {}
	for k,v in pairs(KFOneVOneDuiZhenView.Lunci) do
		self.duizhen_groups[k] = {}
		for i=1,v do
			local str = "group_"..k.."/group_"..k.."_"..i
			local group_obj = duizhen_group_obj:Find(str)
			local role_index = k == 1 and 1 or 2
			role_index = k == KFOneVOneWGData.KnockoutState.Index2To1 and 3 or role_index
		    local obj = ResMgr:Instantiate(self.node_list["role_group_prefab_"..role_index].gameObject)
			local obj_transform = obj.transform
			obj_transform.position = Vector3(0, 0, 0)
			obj_transform:SetParent(group_obj.transform, false)
			self.duizhen_groups[k][i] = KFOneVOneRoleGroupItem.New(obj)
			self.duizhen_groups[k][i]:SetKnockout(k)
			self.duizhen_groups[k][i]:SetIndex(i)
			self.duizhen_groups[k][i]:SetDuiZhenIndex(self.cur_duizhen_index)
			self.duizhen_groups[k][i]:SetClickRoleCallBack(BindTool.Bind(self.JingCaiClickRoleCallBack,self))
			self.duizhen_groups[k][i]:Flush()
		end
		self.duizhen_group_obj_list[k] = duizhen_group_obj:Find("group_"..k)
	end

end

function KFOneVOneDuiZhenView:ReleaseCallBack()

	if not IsEmptyTable(self.duizhen_groups) then
		for k,v in pairs(self.duizhen_groups) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
		end
		self.duizhen_groups = {}
	end

	if self.guanjun_head then
        self.guanjun_head:DeleteMe()
        self.guanjun_head = nil
    end

	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if CountDownManager.Instance:HasCountDown("onevone_duizhen_time") then
		CountDownManager.Instance:RemoveCountDown("onevone_duizhen_time")
	end

end

function KFOneVOneDuiZhenView:OpenCallBack()
	-- 请求淘汰赛对阵表
	local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_SCORE_RANK)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_MATCH_INFO)
end

function KFOneVOneDuiZhenView:OpenJingCai()
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local num = KFOneVOneDuiZhenView.Lunci[knockout] or 0
	local jingcai_count = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinnerCount(knockout)
	--self.node_list["duizhen_tabbtn"].toggle.isOn = true

	if jingcai_count >= num then
		return
	end
	self:ClickJingCaiBtn()
end

function KFOneVOneDuiZhenView:OnFlush(param)
	for k, v in pairs(param) do
		if "all" == k then
			self:FlushDuiZhenView()
		elseif "open_jingcai" == k then
			self:OpenJingCai()
		end
	end

	--local jingcai_flag = KFOneVOneWGData.Instance:IsShowJingCaiRed()
	--self.node_list["duizhen_tabbtn_red"]:SetActive(jingcai_flag == 1)
end

function KFOneVOneDuiZhenView:FlushDuiZhenView()
	local win_info = KFOneVOneWGData.Instance:GetKnockoutWinnerInfo()
	if not IsEmptyTable(win_info) then
		local uuid = win_info.uuid
		local key = uuid.temp_low --.. uuid.temp_high
		AvatarManager.Instance:SetAvatarKey(key, win_info.avatar_key_big, win_info.avatar_key_small)
		self.guanjun_head:SetData({role_id = key, sex = win_info.sex, prof = win_info.prof})

		local bundle = "uis/view/field1v1_ui/images_atlas"
		local asset = "a3_jjc_txkh"
		self.guanjun_head:ChangeBg(bundle, asset, true)

		local name = ""
		local server = ""
		if win_info.name and win_info.name ~= "" then
			local name_list = Split(win_info.name, "_")
			server = string.format(Language.Kuafu1V1.Server2,name_list[2])
			name = name_list[1]
		end

		self.node_list["guanjun_name"].text.text = name
		self.node_list["guanjun_server"].text.text = server
	else
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_WINNER_INFO)
	end

	local winner_index = KFOneVOneWGData.Instance:GetKnockoutIndexesWinner()
	self.node_list["guanjun"]:SetActive(winner_index and winner_index > 0)
	self.node_list["xuwei_img"]:SetActive(not winner_index or winner_index <= 0)

	-- if winner_index and winner_index > 0 then
	-- 	--self.node_list["onevone_msg_title"]:SetActive(true)
	-- 	local rank_reward_cfg = KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(0)
	-- 	if rank_reward_cfg then
	-- 		local title_id = rank_reward_cfg.title_id
	-- 		local title_cfg = TitleWGData.Instance:GetConfig(title_id)
	-- 		local b,a = ResPath.GetTitleModel(title_id)
	-- 		--self.node_list["onevone_msg_title"]:SetActive(true)
	-- 		--self.node_list["onevone_msg_title"]:ChangeAsset(b, a, false)
	-- 		if title_cfg.is_show_effid == 1 then
	-- 			if title_cfg.is_zhengui == 0 then
	-- 				self.node_list["onevone_msg_effect0"]:SetActive(true)
	-- 				self.node_list["onevone_msg_effect1"]:SetActive(false)
	-- 			else
	-- 				self.node_list["onevone_msg_effect0"]:SetActive(false)
	-- 				self.node_list["onevone_msg_effect1"]:SetActive(true)
	-- 			end
	-- 		else
	-- 			self.node_list["onevone_msg_effect0"]:SetActive(false)
	-- 			self.node_list["onevone_msg_effect1"]:SetActive(false)
	-- 		end
	-- 	end
	-- else
	-- 	self.node_list["onevone_msg_title"]:SetActive(false)
	-- end

	self:FlushJingCaiBtn()
	self:FlushDuiZhenGroups()

	if CountDownManager.Instance:HasCountDown("onevone_duizhen_time") then
		CountDownManager.Instance:RemoveCountDown("onevone_duizhen_time")
	end

	local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()

	local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if next_time > 0 and match_state == KFOneVOneWGData.MatchState.TaoTai then
		local other_cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
		local knockout_fight_sec = other_cfg.knockout_fight_sec
		next_time = next_time > server_time and next_time or next_time + knockout_fight_sec
		if next_time > server_time then
			local time = next_time - server_time
			self:JingCaiRefreshTime(server_time, next_time)
			CountDownManager.Instance:AddCountDown("onevone_duizhen_time", BindTool.Bind1(self.JingCaiRefreshTime, self), BindTool.Bind1(self.JingCaiTimeComplete, self), nil, time,1)
		else
			self:JingCaiTimeComplete(true)
		end
	end
end

function KFOneVOneDuiZhenView:FlushDuiZhenGroups(cur_group)
	if cur_group then
		if self.duizhen_groups[cur_group] then
			for i,v in pairs(self.duizhen_groups[cur_group]) do
				v:SetDuiZhenIndex(self.cur_duizhen_index)
				v:Flush()
			end
		end
	else
		for k,list in pairs(self.duizhen_groups) do
			for i,v in pairs(list) do
				v:SetDuiZhenIndex(self.cur_duizhen_index)
				v:Flush()
			end
		end
	end
end

function KFOneVOneDuiZhenView:JingCaiRefreshTime(now_time, total_time)
	local lerp_time = math.floor(total_time - now_time)
	local time_str = TimeUtil.MSTime(lerp_time)

	local onevone_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()

	if onevone_match_state == KFOneVOneWGData.MatchState.TaoTai and knockout <= KFOneVOneWGData.KnockoutState.Index2To1 then
		local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if next_time > server_time then
			self.node_list["jingcai_time_text"].text.text = string.format(Language.Kuafu1V1.JingCaiTimeStr1,time_str)
		else
			self.node_list["jingcai_time_text"].text.text = string.format(Language.Kuafu1V1.JingCaiTimeStr2,time_str)
		end
	else
		self.node_list["jingcai_time_text"].text.text = ""
	end
end

function KFOneVOneDuiZhenView:JingCaiTimeComplete(only_one)
	self.node_list["jingcai_time_text"].text.text = ""
	if not only_one then
		self:Flush()
	end
	if self.cur_duizhen_index == KFOneVOneDuiZhenView.DuiZhenIndex.JingCai then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiCloseTips)
		self:ClickCloseJingCai()
	end
end

function KFOneVOneDuiZhenView:ClickJingCaiBtn()
	local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()

	if knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then
		local winner_index = KFOneVOneWGData.Instance:GetKnockoutIndexesWinner()
		if winner_index > 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.HasChampion)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.NotHasChampion)
		end
		return
	end

	if match_state == KFOneVOneWGData.MatchState.TaoTai and knockout <= KFOneVOneWGData.KnockoutState.Index2To1 then
		local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if next_time < server_time then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiCloseTips)
			return
		end

		local can_jingcai = KFOneVOneWGData.Instance:GetCurKnockoutCanJingCai()
		if not can_jingcai then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.LunKongNotJingCai)
			return
		end
		self.cur_duizhen_index = KFOneVOneDuiZhenView.DuiZhenIndex.JingCai
		self:FlushJingCai(true)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiOpenTip3)
	end

	self:FlushJingCaiBtn()
	if self.duizhen_groups[4][1] then
		self.duizhen_groups[4][1]:Flush()
	end
end

function KFOneVOneDuiZhenView:ClickTouZhuBtn()
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()

	local num = KFOneVOneDuiZhenView.Lunci[knockout]
	local jingcai_count = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinnerCount(knockout)
	if num == nil or jingcai_count == nil then return end
	if jingcai_count >= num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiAllCount)
		return
	end

	if IsEmptyTable(self.select_role_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiNotSelect)
		return
	end
	local num = 0
	local empty_tab = bit:d2b(0)
	for k,v in pairs(self.select_role_list) do
		if v == true then
			num = num + 1
			empty_tab[33-k] = 1
		end
	end
	local flag = bit:b2d(empty_tab)

	local ok_fun = function ()
		--竞猜本轮胜利者 param1是下标索引标记
		local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_KNOCKOUT_GUESS_WINNER
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type,flag)
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiSucc)
		self:ClickCloseJingCai()
	end

	local round_cfg = KFOneVOneWGData.Instance:GetGuessRewardCfgByRound(knockout)
	if round_cfg then
		local coin = round_cfg.guess_need_gold * num
		local str = string.format(Language.Kuafu1V1.JingCaiAlertDesc,coin)
		if nil == self.alert_window then
            self.alert_window = Alert.New(nil, nil, nil, nil, true)
        end
        self.alert_window:SetLableString(str)
        self.alert_window:SetShowCheckBox(true,"onevone_jingcai")
        self.alert_window:SetOkFunc(ok_fun)
        self.alert_window:Open()
	else
		print_error(">>>>>>>>>>不可投注当前淘汰赛的轮次为:",knockout)
	end
end

function KFOneVOneDuiZhenView:ClickCloseJingCai()
	self.cur_duizhen_index = KFOneVOneDuiZhenView.DuiZhenIndex.DuiZhen
	self:FlushJingCai(false)
	self:FlushJingCaiBtn()
end

function KFOneVOneDuiZhenView:FlushJingCai(is_show)
	self.node_list["jingcai_parent"]:SetActive(self.cur_duizhen_index == KFOneVOneDuiZhenView.DuiZhenIndex.JingCai)
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	if is_show then
		if self.duizhen_group_obj_list[knockout] and knockout <= 4 then
			self.duizhen_group_obj_list[knockout]:SetParent(self.node_list["jingcai_parent"].transform)
		end
		self:ClearJingCaiInfo(knockout)
	else
		for k,v in pairs(self.duizhen_group_obj_list) do
			v:SetParent(self.node_list["duizhen_group"].transform)
		end
	end
	self:FlushDuiZhenGroups(knockout)
	self:FlushJingCaiConsume()
end

function KFOneVOneDuiZhenView:ClearJingCaiInfo(cur_group)
	KFOneVOneWGData.Instance:ClearJingCaiRoleList()
	self.select_role_list = {}
	if cur_group then
		if self.duizhen_groups[cur_group] then
			for i,v in pairs(self.duizhen_groups[cur_group]) do
				v:ClearSelectRoleIson()
			end
		end
	else
		for k,list in pairs(self.duizhen_groups) do
			for i,v in pairs(list) do
				v:ClearSelectRoleIson()
			end
		end
	end
end

function KFOneVOneDuiZhenView:FlushJingCaiBtn()
	self.node_list["jingcai_btn"]:SetActive(self.cur_duizhen_index == KFOneVOneDuiZhenView.DuiZhenIndex.DuiZhen)
	self.node_list["touzhu_btn"]:SetActive(self.cur_duizhen_index == KFOneVOneDuiZhenView.DuiZhenIndex.JingCai)
end

function KFOneVOneDuiZhenView:FlushJingCaiConsume()
	local num = 0
	if not IsEmptyTable(self.select_role_list) then
		for k,v in pairs(self.select_role_list) do
			if v == true then
				num = num + 1
			end
		end
	end

	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local round_cfg = KFOneVOneWGData.Instance:GetGuessRewardCfgByRound(knockout)
	local all_num = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinnerCount(knockout)
	self.node_list["consume2"]:SetActive(num > 0)

	if round_cfg then
		local coin = round_cfg.guess_need_gold * num
		local all_win_coin = round_cfg.guess_win_gold * (all_num + num)
		self.node_list["all_win_num"].text.text = all_win_coin
		self.node_list["consume_num"].text.text = coin
	else
		self.node_list["all_win_num"].text.text = 0
		self.node_list["consume_num"].text.text = 0
	end
end

function KFOneVOneDuiZhenView:JingCaiClickRoleCallBack(item, index, ison)
	local other_index = index % 2 == 1 and 2 or 1
	local data = {}
	if item then
		data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(item.knockout,item.index)
	else
		data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(4, 1)
	end

	local info1 = data[index]
	local info2 = data[other_index]
	if IsEmptyTable(info1) or IsEmptyTable(info2) or info1.name == "" or info2.name == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiLunKong)
		return
	end

	local is_guess1, is_guess2
	if item then
		is_guess1 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info1.index,item.knockout)
		is_guess2 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info2.index,item.knockout)
	else
		is_guess1 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info1.index,4)
		is_guess2 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info2.index,4)
	end

	if is_guess1 or is_guess2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.HasJingCai)
		return
	end

	if self.select_role_list == nil then
		self.select_role_list = {}
	end

	self.select_role_list[info1.index] = ison
	self.select_role_list[info2.index] = false
	KFOneVOneWGData.Instance:SetJingCaiRoleList(self.select_role_list)
	self:FlushJingCaiConsume()
end

--------------------------------KFOneVOneRoleGroupItem-------------------------------------
KFOneVOneRoleGroupItem = KFOneVOneRoleGroupItem or BaseClass(BaseRender)

function KFOneVOneRoleGroupItem:__init()
	self.select_role_ison = {}
	self.head_list = {}

	if self.node_list["reward_btn"] then
		XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.ClickRewardBtn, self))
	end

	for i = 1, 2 do
		XUI.AddClickEventListener(self.node_list["role_"..i], BindTool.Bind(self.ClickRoleBtn, self, i))
		if self.node_list["reward_btn_"..i] then
			XUI.AddClickEventListener(self.node_list["reward_btn_"..i], BindTool.Bind(self.ClickRewardBtnIndex, self, i))
		end

		self.select_role_ison[i] = false
		if not self.head_list[i] and self.node_list["head_"..i] then
			self.head_list[i] = BaseHeadCell.New(self.node_list["head_"..i])
			--self.head_list[i]:SetImgBg(false)
		end
	end

	self.cur_select_index = 0
end

function KFOneVOneRoleGroupItem:__delete()
	if not IsEmptyTable(self.head_list) then
		for i, v in pairs(self.head_list) do
			v:DeleteMe()
		end
		self.head_list = nil
	end
end

function KFOneVOneRoleGroupItem:ClearSelectRoleIson()
	self.select_role_ison[1] = false
	self.select_role_ison[2] = false
end

function KFOneVOneRoleGroupItem:SetKnockout(knockout)
	self.knockout = knockout
end

function KFOneVOneRoleGroupItem:SetIndex(index)
	self.index = index
end

function KFOneVOneRoleGroupItem:SetDuiZhenIndex(duizhen_index)
	self.duizhen_index = duizhen_index
end

function KFOneVOneRoleGroupItem:OnFlush()
	local group_data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(self.knockout,self.index)
	local onevone_knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local data1 = group_data[1] or {}
	local data2 = group_data[2] or {}
	local info_index1 = data1.index or -1
	local info_index2 = data2.index or -1
	local is_win_index_list = {}
	is_win_index_list[1] = KFOneVOneWGData.Instance:GetKnockoutItemWinInfoJingCai(self.knockout,info_index1,info_index2)
	is_win_index_list[2] = KFOneVOneWGData.Instance:GetKnockoutItemWinInfoJingCai(self.knockout,info_index2,info_index1)

	-- local get_color = function (uuid)
	-- 	local my_uuid = RoleWGData.Instance:GetUUid()
	-- 	local color = uuid and uuid == my_uuid and "#d2ff83" or COLOR3B.WHITE
	-- 	return color
	-- end

	--local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	--local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local is_all_nil = (IsEmptyTable(data1) or data1.name == "") and (IsEmptyTable(data2) or data2.name == "")

	local can_show_win = is_win_index_list[1] or is_win_index_list[2] or is_all_nil
	--local is_end = match_state == KFOneVOneWGData.MatchState.TaoTai or knockout == KFOneVOneWGData.KnockoutState.WinnerEnd
	-- if self.knockout == 2 then
	-- 	self.node_list["role_1"].transform.localPosition = Vector3(-8, 60, 0)
	-- 	self.node_list["role_2"].transform.localPosition = Vector3(-8, -87, 0)
	-- elseif self.knockout == 3 then
	-- 	self.node_list["role_1"].transform.localPosition = Vector3(-8, 194, 0)
	-- 	self.node_list["role_2"].transform.localPosition = Vector3(-8, -87, 0)
	-- elseif self.knockout == 4 then
	-- 	self.node_list["role_1"].transform.localPosition = Vector3(-116, 13, 0)
	-- 	self.node_list["role_2"].transform.localPosition = Vector3(67, 13, 0)
	-- end
	--transform.localPosition = Vector3(0, y, 0)
	local is_show_reward = false

	for i=1,2 do
		local data = group_data[i] or {}
		local info_index = data.index or -1
		local name = ""
        local server = ""
		if data.name and data.name ~= "" then
			local name_list = Split(data.name, "_")
			server = string.format(Language.Kuafu1V1.Server1,name_list[2])
            name = name_list[1]
			if self.head_list[i] then
				local head_data = {}
				head_data.role_id = data.uuid.temp_low
				head_data.prof = data.prof
				head_data.sex = data.sex
				--self.head_list[i]:SetImgBg(false)
				self.node_list["head_"..i]:SetActive(true)
				self.head_list[i]:SetData(head_data)

				local bundle = "uis/view/field1v1_ui/images_atlas"
				local asset = i == 1 and "a3_jjc_txkh" or "a3_jjc_txkl"
				self.head_list[i]:ChangeBg(bundle, asset, true)
			end
			if self.node_list["xuwei_"..i] then
				self.node_list["xuwei_"..i]:SetActive(false)
				self.node_list["name_bg_"..i]:SetActive(true)
			end
			self.node_list["lose_"..i]:SetActive(is_win_index_list[i] == false and info_index1 ~= 0)
		else
			if self.node_list["xuwei_"..i] then
				name = ""
                server = ""
				self.node_list["xuwei_"..i]:SetActive(true)
				self.node_list["name_bg_"..i]:SetActive(false)
			else
				name = ""
                server = ""
			end

			self.node_list["lose_"..i]:SetActive(false)

			if self.head_list[i] then
				--self.head_list[i]:SetImgBg(false)
				self.node_list["head_"..i]:SetActive(false)
			end
		end

		self.node_list["name_"..i].text.text = name
        self.node_list["server_"..i].text.text = server
		--self.node_list["win_"..i]:SetActive(is_win_index_list[i] or false)
		--self.node_list["bg_lose_"..i]:SetActive(can_show_win)
		--self.node_list["bg_win_"..i]:SetActive(is_win_index_list[i] or false)
		local is_guess = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info_index,self.knockout)
		local is_red = is_guess and can_show_win and is_win_index_list[i]
		local is_get = KFOneVOneWGData.Instance:GetKnockoutGuessIsReward(info_index,self.knockout)
		self.node_list["jingcai_img_"..i]:SetActive(is_guess and onevone_knockout == self.knockout and not is_red)
		local can_show = KFOneVOneWGData.Instance:GetJingCaiImgAnimFlag(self.knockout,info_index)
		if is_guess and onevone_knockout == self.knockout and not can_show then
			UITween.ScaleShowPanel(self.node_list["jingcai_img_"..i],u3dpool.vec3(3, 3, 3),0.3)
			KFOneVOneWGData.Instance:SetJingCaiImgAnimFlag(self.knockout,info_index)
		end

		is_show_reward = is_show_reward or (not is_get and is_red)

		if self.node_list["reward_red_"..i] and self.node_list["reward_btn_"..i] then
			self.node_list["reward_red_"..i]:SetActive(not is_get and is_red)
			self.node_list["reward_btn_"..i]:SetActive(not is_get and is_red)
		end

		--self.node_list["reward_open_"..i]:SetActive(is_get)
		self.node_list["role_"..i].button.interactable = self.duizhen_index == KFOneVOneDuiZhenView.DuiZhenIndex.JingCai
		self.node_list["select_img_"..i]:SetActive(is_guess and onevone_knockout == self.knockout)
	end

	if self.node_list["reward_red"] and self.node_list["reward_btn"] then
		self.node_list["reward_red"]:SetActive(is_show_reward)
		self.node_list["reward_btn"]:SetActive(is_show_reward)
	end
end

function KFOneVOneRoleGroupItem:SetClickRoleCallBack(call_back)
	self.click_role_callback = call_back
end

function KFOneVOneRoleGroupItem:ClickRoleBtn(index)
	local onevone_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()

	if onevone_match_state == KFOneVOneWGData.MatchState.TaoTai and knockout <= KFOneVOneWGData.KnockoutState.Index2To1 then
		local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if next_time < server_time then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiCloseTips)
			return
		end
	end

	local list = KFOneVOneWGData.Instance:GetJingCaiRoleList()
	local num = 0
	if not IsEmptyTable(list) then
		for k,v in pairs(list) do
			if v == true then
				num = num + 1
			end
		end
	end

	local other_index = index % 2 == 1 and 2 or 1
	local data =KFOneVOneWGData.Instance:GetKnockoutGroupItem(self.knockout,self.index)
	local info1 = data[index]
	local info2 = data[other_index]
	if IsEmptyTable(info1) or IsEmptyTable(info2) or info1.name == "" or info2.name == "" then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiLunKong)
		return
	end

	local is_guess1 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info1.index,self.knockout)
	local is_guess2 = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinner(info2.index,self.knockout)
	if is_guess1 or is_guess2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.HasJingCai)
		return
	end

	local ison = self.select_role_ison[index]
	self.select_role_ison[index] = not ison
	self.select_role_ison[other_index] = false

	self.node_list["select_img_"..index]:SetActive(self.select_role_ison[index])
	self.node_list["select_img_"..other_index]:SetActive(false)

	if self.click_role_callback then
		self.click_role_callback(self,index,self.select_role_ison[index])
	end
end

function KFOneVOneRoleGroupItem:ClickRewardBtnIndex(index)
	local group_data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(self.knockout,self.index)
	local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_FETCH_GUESS_REWARD
	local info_data = group_data[index]
	if not IsEmptyTable(info_data) then
		local index = info_data.index - 1
		local is_get = KFOneVOneWGData.Instance:GetKnockoutGuessIsReward(info_data.index,self.knockout)
		if is_get then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiRewardGet)
		else
			Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type,index,self.knockout)
		end
	end
end

function KFOneVOneRoleGroupItem:ClickRewardBtn()
	local group_data = KFOneVOneWGData.Instance:GetKnockoutGroupItem(self.knockout, self.index)
	local data1 = group_data[1] or {}
	local data2 = group_data[2] or {}
	local info_index1 = data1.index or -1
	local info_index2 = data2.index or -1
	local is_win_index_list = {}
	is_win_index_list[1] = KFOneVOneWGData.Instance:GetKnockoutItemWinInfoJingCai(self.knockout, info_index1, info_index2)
	is_win_index_list[2] = KFOneVOneWGData.Instance:GetKnockoutItemWinInfoJingCai(self.knockout, info_index2, info_index1)
	local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_FETCH_GUESS_REWARD

	for i = 1, 2 do
		if is_win_index_list[i] then
			local info_data = group_data[i]
			if not IsEmptyTable(info_data) then
				local index = info_data.index - 1
				local is_get = KFOneVOneWGData.Instance:GetKnockoutGuessIsReward(info_data.index, self.knockout)
				if is_get then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JingCaiRewardGet)
				else
					Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type, index, self.knockout)
				end
			end

			return
		end
	end
end