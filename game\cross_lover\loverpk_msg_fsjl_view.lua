function LoverPkMsgView:FsjlLoadIndexCallBack()
    if not self.rank_fs_list then
        self.rank_fs_list = AsyncListView.New(LoverPkFSJLItemRender, self.node_list.rank_fs_list)
    end

    if not self.my_fs_item_list then
        self.my_fs_item_list = AsyncListView.New(ItemCell, self.node_list.my_fs_item_list)
    end

    self.node_list.act_fs_time.text.text = Language.LoverPK.FSZZTipDesc
end

function LoverPkMsgView:FsjlShowIndexCallBack()
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.QUERY_KNOCKOUT_RANK)
end

function LoverPkMsgView:FsjlReleaseCallBack()
    if self.rank_fs_list then
        self.rank_fs_list:DeleteMe()
        self.rank_fs_list = nil
    end

    if self.my_fs_item_list then
        self.my_fs_item_list:DeleteMe()
        self.my_fs_item_list = nil
    end
end

function LoverPkMsgView:FsjlOnFlush()
    local knockout_rank_reward_list, my_rank_data = LoverPkWGData.Instance:GetKnockoutRankDataListAndMyInfo()
    self:FsjlFlushMyRankInfo(my_rank_data)
    self.rank_fs_list:SetDataList(knockout_rank_reward_list)
end

function LoverPkMsgView:FsjlFlushMyRankInfo(my_rank_data)
    local info_data = my_rank_data.info
    local has_data = not IsEmptyTable(info_data)
    self.node_list.not_my_fs_item:CustomSetActive(not has_data)
    self.node_list.my_fs_item:CustomSetActive(has_data)

    if has_data then
        local rank_id = info_data.rank_id or ""
        local server_id_s = (info_data.uuid1 or {}).temp_low or 0

        self.my_fs_item_list:SetDataList(my_rank_data.cfg.reward_list)
        self.node_list.my_fs_rank.text.text = string.format(Language.LoverPK.JBJLRankValueStr, rank_id)
        local server_id = UserVo.GetServerId(server_id_s)
        self.node_list.my_fs_server.text.text = string.format(Language.Common.ServerIdFormat, server_id)
    end
end

-----------------------------------------LoverPkFSJLItemRender---------------------------------------
LoverPkFSJLItemRender = LoverPkFSJLItemRender or BaseClass(BaseRender)

function LoverPkFSJLItemRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function LoverPkFSJLItemRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function LoverPkFSJLItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    
    local rank = self.data.rank_id
    if self.node_list["bg"] then
		self.node_list.bg:SetActive(rank % 2 == 1)
	end

	if rank <= 3 then
        self.node_list["quality_bg"].image:LoadSprite(ResPath.GetCommon("a2_xm_di_dj" .. rank))
    end
    
    self.node_list["quality_bg"]:SetActive(rank <= 3)
    self.reward_list:SetDataList(self.data.cfg.reward_item)
    self.node_list["rank"]:SetActive(self.index > 3)
    self.node_list["rank_img"]:SetActive(self.index <= 3)

    if rank <= 3 then
        self.node_list["rank_img"].image:LoadSprite(ResPath.GetCommonIcon("a2_pm_"..rank))
        self.node_list["rank_img"].image:SetNativeSize()
        self.node_list.rank_text.text.text = rank
    else
        self.node_list["rank"].text.text = rank
        self.node_list.rank_text.text.text = ""
    end

    local info = self.data.info
    local has_role_info = not IsEmptyTable(info)

    if has_role_info then
        self.node_list.role_name1.text.text =  info.name1 or ""
        self.node_list.role_name2.text.text =  info.name2 or ""
        local server = ((info or {}).uuid1 or {}).temp_low or 0
        local server_id = UserVo.GetServerId(server)
        server_id = server_id > 0 and server_id or RoleWGData.Instance:GetOriginServerId()
        self.node_list.server_name.text.text = string.format(Language.Common.ServerIdFormat, server_id)
		-- self.node_list.score.text.text = self.data.score or ""
    else
        self.node_list.role_name1.text.text =  Language.LoverPK.XiaHuaXian
        self.node_list.role_name2.text.text =  Language.LoverPK.XiaHuaXian
        self.node_list.server_name.text.text =  Language.LoverPK.XiaHuaXian
    end
end