ExpAdditionView = ExpAdditionView or BaseClass(SafeBaseView)

local tips_color = "#79FA82"

function ExpAdditionView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
						{vector2 = Vector2(0, 15), sizeDelta = Vector2(923, 570)})
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_expaddition")
end

function ExpAdditionView:__delete()

end

function ExpAdditionView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Role.WorldExpAddTitle
	self.node_list.tips_btn.button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn,self))
	if nil == self.list_view then
		local bundle = "uis/view/role_ui_prefab"
		local asset = "expadition_cell"
		self.list_view = AsyncBaseGrid.New()
		self.list_view:SetStartZeroIndex(false)
		self.list_view:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["list_view"],
									assetBundle = bundle, assetName = asset, itemRender = ExpAdditionCell})
	end

	self.world_level_change_event = GlobalEventSystem:Bind(OtherEventType.WORLD_LEVEL_CHANGE, BindTool.Bind(self.Flush, self))
end

-- 切换标签调用
function ExpAdditionView:ShowIndexCallBack()
	SkillWGCtrl.Instance:SendExpEfficiencyReq()
	RankWGCtrl.Instance:SendWorldLevelReq()
	self.do_exp_add_tween = true
end

function ExpAdditionView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if self.world_level_change_event then
		GlobalEventSystem:UnBind(self.world_level_change_event)
		self.world_level_change_event = nil
	end
end

function ExpAdditionView:CloseCallBack()
	self.from_view = nil
end

function ExpAdditionView:OnFlush()
	local value = SkillWGData.Instance:GetExpEfficiencyInfo()
	value = value and value.world_extra_percent or 0
	value = math.floor(value * 100 + 0.5)
	local data_list = nil
	if self.from_view == "offline_view" then
		data_list = ExpAdditionWGData.Instance:GetOfflineDataList()
	else
		data_list = ExpAdditionWGData.Instance:GetDataList()
	end
	local total_addition_list = ExpAdditionWGData.Instance:GetTotalAdditionValue()
	local pass_offline_rest_time = OfflineRestWGData.Instance.pass_offline_rest_time
	local min = math.ceil(pass_offline_rest_time/60)
	local add_exp = OfflineRestWGData.Instance.add_exp
	local off_line_exp, total_addition = ExpAdditionWGData.Instance:GetCurOffLineExpExtra()
	-- off_line_exp = CommonDataManager.ConverExpExtend(off_line_exp, true)
	--当前经验加成
	total_addition = math.ceil(total_addition * 100)
	self.list_view:SetDataList(data_list)
    local str = RoleWGData.GetLevelString(CrossServerWGData.Instance:GetServerMeanWorldLevel())
	self.node_list.cur_world_level.text.text = string.format(Language.ExpAddition.CurWorldLevel, tips_color, str)
	--世界等级经验加成
	self.node_list.world_level.text.text = string.format(Language.ExpAddition.WorldLevel, tips_color, value .. "%")
	--self.node_list.rank_value.text.text = total_addition_list.score_rank

	--离线经验效率
	local off_line_exp_num = ExpAdditionWGData.Instance:GetOffLineStdExp()
	local off_line_total_rate = 1 + ((total_addition + value) / 100)
	local off_line_exp_times = ExpAdditionWGData.Instance:GetOffLineStdExpTimes()
	local exp_xiaolv = CommonDataManager.ConverExp(off_line_exp_num * off_line_total_rate * off_line_exp_times)
	self.node_list.off_line_exp.text.text = string.format(Language.ExpAddition.OffLineExpExtra, tips_color, exp_xiaolv)

	--self.node_list.appraise_text.text.text = total_addition_list.score_show
	self.node_list.total_addition.text.text = string.format(Language.ExpAddition.TotalAddition, tips_color, total_addition)
	--self:DoViewAnimation()  --itemcell动画
end

function ExpAdditionView:OnClickTipsBtn()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.ExpAddition.TipsTitle)
	role_tip:SetContent(Language.ExpAddition.TipsDesc)
end
--设置离线挂机需要展示的数据
function ExpAdditionView:SetOfflineNeedInfo(from_view)
	self.from_view = from_view
	self:Open()
end

--------------------------------------------------ExpAdditionCell-----------------------------------------------
ExpAdditionCell = ExpAdditionCell or BaseClass(BaseRender)

function ExpAdditionCell:__init()

end

function ExpAdditionCell:__delete()
	if self.item_cell then
		self.item_cell:SetGraphicGreyCualityBg(false)
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ExpAdditionCell:LoadCallBack()
	self.node_list.add_btn.button:AddClickListener(BindTool.Bind(self.OnClickAddBtn,self))
	self.node_list.no_can_icon_bg.button:AddClickListener(BindTool.Bind(self.OnClickNoCanBtn,self))
	self:CreateChild()
end

function ExpAdditionCell:CreateChild()
	self.item_cell = ItemCell.New(self.node_list["can_icon_item"])
	self.item_cell:SetNeedItemGetWay(true)
end

function ExpAdditionCell:OnFlush()
	if not self.data then return end
	local data = self.data
	local is_grey = math.ceil(data.addition_value * 100) == 0
	if data.can_click == 0 then
		self.node_list.no_can_icon_bg:SetActive(true)
		self.node_list.can_icon_item:SetActive(false)
		local bundle, asset = ResPath.GetCommonImages(string.format("a3_ty_wpk_%d", GameEnum.ITEM_COLOR_ORANGE))
		self.node_list.no_can_icon_bg.image:LoadSprite(bundle, asset)

		local bundle, asset = ResPath.GetRoleUIImage("exp_add_icon" .. data.item_id)
    	self.node_list.no_can_icon_item.image:LoadSprite(bundle, asset, function()
        self.node_list.no_can_icon_item.image:SetNativeSize()
    	end)
	elseif data.can_click == 1 then
		self.node_list.no_can_icon_bg:SetActive(false)
		self.node_list.can_icon_item:SetActive(true)
		self.item_cell:SetNeedItemGetWay(self.data.tips_type == 0)
		local temp_data = self.data
		local equip_data = {}
		if self.data.name == Language.ExpAddition.ExpAddLeiXing[3] then
			equip_data = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_XIANZHUO)
		elseif self.data.name == Language.ExpAddition.ExpAddLeiXing[2] then
			equip_data = EquipWGData.Instance:GetGridData(GameEnum.EQUIP_INDEX_XIANJIE)
		end
		if not IsEmptyTable(equip_data) then
			temp_data = equip_data
		end
		self.item_cell:SetData(temp_data)
		self.item_cell:SetEffectRootEnable(false)
	end

	if data.addition_show then
		self.node_list.appraise_value.text.text = data.addition_show
	end

	self.node_list.name.text.text = data.name

    local cur_value = math.modf(data.addition_value * 100)
	local cur_value_text = cur_value .. "%"
	local recommend_value_text = data.recommend_value * 100 .. "%"
	self.node_list.cur_value.text.text = cur_value_text.."/"..recommend_value_text
	local value = string.format("%.2f", data.addition_value)  / data.recommend_value
	self.node_list.Slider.slider.value = value
	self.node_list.add_btn:SetActive(value < 1)
	self.node_list.prefect_img:SetActive(value >= 1)
end

function ExpAdditionCell:OnClickNoCanBtn()
	if not self.data then return end
	local data = self.data
	local x = Split(data.open_panel_name,"#")
	local view_name = x[1]
	local tab_index = x[2]

    if view_name == "shop" then
        tab_index = ShopWGData.ShowTypeToIndex[x[2]]
        local param = x[3]
        ShopWGCtrl.Instance:ShopJumpToItemByIDAndTabIndex(tonumber(param),tab_index)
    elseif view_name == "XiuXianShiLian" then
    	 local data_list = XiuXianShiLianWGData.Instance:GetChapterListInfo()
    	 local param = string.sub(x[3],4,4)
    	 local role_lv = RoleWGData.Instance:GetRoleLevel()
   		 if role_lv < data_list[tonumber(param)].open_level then
   		 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ExpAddition.XiuXianTip,data_list[tonumber(param)].open_level))
   		 else
   		 	 FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)
   		 end
    else
        FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)
    end

	ExpAdditionWGData.Instance:CloseView()
end

function ExpAdditionCell:OnClickAddBtn()
	local data = self.data
    local data_type = data.type

	--默认跳转
	local x = Split(data.open_panel_name,"#")
	local view_name = x[1]
	local tab_index = x[2]
	if view_name == "shop" then
		tab_index = ShopWGData.ShowTypeToIndex[x[2]]
		local param = x[3]
		ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = tonumber(param)})
	elseif GuideModuleName[view_name] then
		--如果打开被动技能，默认选中天记谱
		if tab_index == "skill_beidong" then
			FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)
		elseif view_name == "XiuXianShiLian" then
			local data_list = XiuXianShiLianWGData.Instance:GetChapterListInfo()
			local param = string.sub(x[3],4,4)
			local role_lv = RoleWGData.Instance:GetRoleLevel()
			
			local open_level = (data_list[tonumber(param)] or {}).open_level or 0
			if  role_lv < open_level then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ExpAddition.XiuXianTip, open_level))
			else
				FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)
			end
		else
			FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)
		end
	else
		if data_type == EXP_ADDITION_TYPE.BUBUGAOSHENG then
			if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPENSERVER_COMPETITION) then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.ExpAddition.CurActHadEnd)
				return
			end
		end
		FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)
	end
	ExpAdditionWGData.Instance:CloseView()
end