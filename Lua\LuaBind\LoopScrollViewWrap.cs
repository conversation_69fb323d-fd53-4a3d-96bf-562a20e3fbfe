﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class LoopScrollViewWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(LoopScrollView), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("GetChildSize", GetChildSize);
		<PERSON><PERSON>RegFunction("GetViewSize", GetViewSize);
		<PERSON><PERSON>RegFunction("GetNearestPagePos", GetNearestPagePos);
		L<PERSON>RegFunction("GetSingleChildNormalizePos", GetSingleChildNormalizePos);
		<PERSON><PERSON>RegFunction("AddFlushListener", AddFlushListener);
		<PERSON><PERSON>unction("FlushAllChild", FlushAllChild);
		L.RegFunction("Move", Move);
		<PERSON>.RegFunction("OnBeginDrag", OnBeginDrag);
		<PERSON><PERSON>RegFunction("OnEndDrag", OnEndDrag);
		<PERSON><PERSON>unction("Snap", Snap);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>Var("isVertical", get_isVertical, set_isVertical);
		L.RegVar("flushAction", get_flushAction, set_flushAction);
		L.RegVar("isPage", get_isPage, set_isPage);
		L.RegVar("snapTweenTime", get_snapTweenTime, set_snapTweenTime);
		L.RegVar("startSnapSpeed", get_startSnapSpeed, set_startSnapSpeed);
		L.RegVar("childCount", get_childCount, set_childCount);
		L.RegVar("showChildCount", get_showChildCount, set_showChildCount);
		L.RegVar("moveDistance", get_moveDistance, set_moveDistance);
		L.RegVar("firstGoIndex", get_firstGoIndex, set_firstGoIndex);
		L.RegVar("drapping", get_drapping, set_drapping);
		L.RegVar("childItemPrefab", get_childItemPrefab, set_childItemPrefab);
		L.RegVar("loopRoot", get_loopRoot, set_loopRoot);
		L.RegVar("goLinkedList", get_goLinkedList, set_goLinkedList);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetChildSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			float o = obj.GetChildSize();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetViewSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			float o = obj.GetViewSize();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNearestPagePos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			float o = obj.GetNearestPagePos();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSingleChildNormalizePos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			float o = obj.GetSingleChildNormalizePos();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddFlushListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			System.Action<UnityEngine.GameObject,int> arg0 = (System.Action<UnityEngine.GameObject,int>)ToLua.CheckDelegate<System.Action<UnityEngine.GameObject,int>>(L, 2);
			obj.AddFlushListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FlushAllChild(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			obj.FlushAllChild();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Move(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
				obj.Move();
				return 0;
			}
			else if (count == 2)
			{
				LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.Move(arg0);
				return 0;
			}
			else if (count == 3)
			{
				LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.Move(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				System.Action arg2 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 4);
				obj.Move(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: LoopScrollView.Move");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBeginDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnBeginDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnEndDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnEndDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Snap(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			LoopScrollView obj = (LoopScrollView)ToLua.CheckObject<LoopScrollView>(L, 1);
			obj.Snap();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isVertical(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			bool ret = obj.isVertical;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isVertical on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_flushAction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			System.Action<UnityEngine.GameObject,int> ret = obj.flushAction;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index flushAction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isPage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			bool ret = obj.isPage;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isPage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_snapTweenTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			float ret = obj.snapTweenTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapTweenTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_startSnapSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			float ret = obj.startSnapSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startSnapSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_childCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			int ret = obj.childCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index childCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showChildCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			int ret = obj.showChildCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showChildCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_moveDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			float ret = obj.moveDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index moveDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_firstGoIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			int ret = obj.firstGoIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index firstGoIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_drapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			bool ret = obj.drapping;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index drapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_childItemPrefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			UnityEngine.GameObject ret = obj.childItemPrefab;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index childItemPrefab on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_loopRoot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			UnityEngine.GameObject ret = obj.loopRoot;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index loopRoot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_goLinkedList(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			System.Collections.Generic.LinkedList<UnityEngine.GameObject> ret = obj.goLinkedList;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index goLinkedList on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isVertical(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isVertical = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isVertical on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_flushAction(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			System.Action<UnityEngine.GameObject,int> arg0 = (System.Action<UnityEngine.GameObject,int>)ToLua.CheckDelegate<System.Action<UnityEngine.GameObject,int>>(L, 2);
			obj.flushAction = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index flushAction on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isPage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isPage = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isPage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_snapTweenTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.snapTweenTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index snapTweenTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_startSnapSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.startSnapSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index startSnapSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_childCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.childCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index childCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showChildCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.showChildCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showChildCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_moveDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.moveDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index moveDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_firstGoIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.firstGoIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index firstGoIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_drapping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.drapping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index drapping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_childItemPrefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.childItemPrefab = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index childItemPrefab on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_loopRoot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.loopRoot = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index loopRoot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_goLinkedList(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			LoopScrollView obj = (LoopScrollView)o;
			System.Collections.Generic.LinkedList<UnityEngine.GameObject> arg0 = (System.Collections.Generic.LinkedList<UnityEngine.GameObject>)ToLua.CheckObject<System.Collections.Generic.LinkedList<UnityEngine.GameObject>>(L, 2);
			obj.goLinkedList = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index goLinkedList on a nil value");
		}
	}
}

