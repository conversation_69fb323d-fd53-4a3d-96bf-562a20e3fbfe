﻿Shader "Hidden/GPUInstancer/Billboard/AlbedoBake"
{
	Properties
	{
		_BaseMap ("Texture", 2D) = "white" {}
		_Color("Color", Color) = (1,1,1,1)
		_Cutoff("Cutoff" , Range(0,1)) = 0.3
		_GPUIBillboardCutoffOverride("Cutoff Override", Range(0, 1)) = 0.0
	}
	SubShader
	{
		Cull Off
        
		Pass
		{
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			
			#include "UnityCG.cginc"
			#include "../Include/GPUIBillboardInclude.cginc" 

			sampler2D _BaseMap;
			float _Cutoff;
			float4 _Color;
			float _GPUIBillboardBrightness;
			float _GPUIBillboardCutoffOverride;
						
			struct v2f
			{
				float2 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
			};

			v2f vert (float4 vertex : POSITION, float2 uv : TEXCOORD0)
			{
				v2f o;
				o.vertex = UnityObjectToClipPos(vertex);
				o.uv = uv.xy;
				return o;
			}
			
			float4 frag (v2f i) : SV_Target
			{
				if (_GPUIBillboardCutoffOverride > 0.0){
					_Cutoff = _GPUIBillboardCutoffOverride;
				}
				
				float4 c = tex2D(_BaseMap, i.uv);
				clip(c.a-_Cutoff); // discard if below cutoff
				// if (_Color.r > 0 || _Color.g > 0 || _Color.b > 0)
				// {
				// 	c.rgb = saturate(c * _Color).rgb;
				// }

				c.rgb = LinearToGamma(c.rgb);
				//c.a = step(0.1, c.a);
				c.a = 1;
				return c;
			}
			ENDCG
		}
	}
}
