XunbaoTipView = XunbaoTipView or BaseClass(SafeBaseView)
function XunbaoTipView:__init()
	self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "layout_xunbao_tip")
	self:SetMaskBg(true, true)
end

function XunbaoTipView:LoadCallBack()
    self.node_list["title1"].text.text = Language.Xunbao.TipTitle1
    self.node_list["title2"].text.text = Language.Xunbao.TipTitle2
    self.node_list["desc1"].text.text = Language.Xunbao.TipContent1
    self.node_list["desc2"].text.text = Language.Xunbao.TipContent2
end