require("game/sworn_recharge/sworn_recharge_view")
require("game/sworn_recharge/sworn_recharge_wg_data")
require("game/sworn_recharge/sworn_recharge_teamtip_view")

SwornRechargeWGCtrl = SwornRechargeWGCtrl or BaseClass(BaseWGCtrl)

function SwornRechargeWGCtrl:__init()
	if SwornRechargeWGCtrl.Instance then
		ErrorLog("[SwornRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	SwornRechargeWGCtrl.Instance = self

	self.data = SwornRechargeWGData.New()
	self.view = SwornRechargeView.New(GuideModuleName.SwornRechargeView)
	self.teamtip = SwornRechargeTeamTipsView.New()

	self:RegisterAllProtocols()
end

function SwornRechargeWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.teamtip:DeleteMe()
	self.teamtip = nil

	SwornRechargeWGCtrl.Instance = nil
end

function SwornRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCrossJieyiChongZhiRankInfo, "OnSCCrossJieyiChongZhiRankInfo")
end

function SwornRechargeWGCtrl:OpenTeamTipsView()
	self.teamtip:Open()
end

function SwornRechargeWGCtrl:OpenSwornView()
	ViewManager.Instance:Open(GuideModuleName.SwornView, TabIndex.sworn_suit)
end

function SwornRechargeWGCtrl:SendCrossCapRankReq(opera_type, param_1, param_2, param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_SWORN_RECHARGE_RANK
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0

	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

function SwornRechargeWGCtrl:OnSCCrossJieyiChongZhiRankInfo(protocol)
	self.data:SetSwornRechargeInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end
