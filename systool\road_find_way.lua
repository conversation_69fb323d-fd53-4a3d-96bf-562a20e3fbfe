RoadFindWay = RoadFindWay or BaseClass()

function RoadFindWay:__init()
	if RoadFindWay.Instance ~= nil then
		error("[RoadFindWay] attempt to create singleton twice!")
		return
	end

	RoadFindWay.Instance = self
end

function RoadFindWay:__delete()
	RoadFindWay.Instance = nil
end

function RoadFindWay:Init(way_points)
	self.way_points = way_points
	self.roads = {}
	if nil ~= way_points then
		for _, v1 in pairs(way_points) do
			local start_id = v1.id
			for _, target_id in pairs(v1.target_id) do
				local key = string.format("%s##%s", target_id, start_id)
				if nil == self.roads[key] then
					key = string.format("%s##%s", start_id, target_id)
					local target_point = way_points[target_id]
					self.roads[key] = {point_a = u3d.vec2(v1.x, v1.y), point_b = u3d.vec2(target_point.x, target_point.y),
						begin_id = start_id, end_id = target_id, is_double_direction = false, key = key}
				else
					self.roads[key].is_double_direction = true
				end
			end
		end
	end
end

function RoadFindWay:Update(now_time, elapse_time)

end

function RoadFindWay:FindNearestRoad(center_point, call_back)
	local nearest_pos = nil
	local nearest_road = nil
	local min_sqrt_distance = math.huge
	local x, y = center_point.x, center_point.y
	for _, road in pairs(self.roads) do
		local x1, y1 = road.point_a.x, road.point_a.y
		local x2, y2 = road.point_b.x, road.point_b.y
		local sqrt_distance
		local pos

		local cross = (x2 - x1) * (x - x1) + (y2 - y1) * (y - y1)
		if cross <= 0 then
			sqrt_distance = (x - x1) * (x - x1) + (y - y1) * (y - y1)
			pos = u3d.vec2(x1, y1)
		else
			local d2 = (x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1)
			if cross >= d2 then
				sqrt_distance = (x - x2) * (x - x2) + (y - y2) * (y - y2)
				pos = u3d.vec2(x2, y2)
			else
				local r = cross / d2
				local px = x1 + (x2 - x1) * r
				local py = y1 + (y2 - y1) * r
				sqrt_distance = (x - px) * (x - px) + (py - y) * (py - y)
				pos = u3d.vec2(px, py)
			end
		end

		if sqrt_distance < min_sqrt_distance then
			if AStarFindWay:IsWayLine(x, y, pos.x, pos.y) then
				min_sqrt_distance = sqrt_distance
				nearest_road = road
				nearest_pos = pos
			end
		end
	end

	call_back(nearest_pos, nearest_road)
end

local function CompareRoad(road_id, road)
	if road_id == road.begin_id then
		return true, road.begin_id
	elseif road.is_double_direction and road_id == road.end_id then
		return true, road.end_id
	end
	return false
end

function RoadFindWay:GenerateRoadPath(start_road, end_road)
	if start_road.key == end_road.key then
		return true, {}
	end

	local hash_set = {}
	local stack = {}
	local road_path_list = {}

	local i = 0
	local cur_id = start_road.end_id
	while i < 1000 do
		i = i + 1
		if nil == cur_id then
			local info = stack[#stack]
			if nil == info then
				break
			end

			info.index = info.index + 1
			cur_id = self.way_points[info.id].target_id[info.index]
			if nil == cur_id then
				local info = table.remove(stack)
				if info then
					hash_set[info.id] = nil
				end
			end
		end

		if nil ~= cur_id and not hash_set[cur_id] then
			local succ, end_road_id = CompareRoad(cur_id, end_road)
			if succ then
				local road_path = {}
				for k,v in ipairs(stack) do
					table.insert(road_path, v.id)
				end
				table.insert(road_path, end_road_id)
				if #road_path >= 2 then
					if road_path[2] == start_road.begin_id then
						table.remove(road_path, 1)
					end
				end
				table.insert(road_path_list, road_path)
				local info = table.remove(stack)
				if info then
					hash_set[info.id] = nil
				end
				cur_id = nil
			else
				hash_set[cur_id] = true
				local next_id = self.way_points[cur_id].target_id[1]
				if nil ~= next_id then
					table.insert(stack, {id = cur_id, index = 1})
				end
				cur_id = next_id
			end
		else
			cur_id = nil
		end
	end

	if #road_path_list > 0 then
		local min_step = math.huge
		local road_path
		for k,v in pairs(road_path_list) do
			if #v < min_step then
				min_step = #v
				road_path = v
			end
		end

		return true, road_path
	else
		return false
	end
end

function RoadFindWay:FindWay(start_pos, end_pos, call_back)
	self:FindNearestRoad(start_pos, function (start_road_pos, start_road)
		if nil == start_road_pos then
			call_back(false)
			return
		end
		self:FindNearestRoad(end_pos, function (end_road_pos, end_road)
			if nil == end_road_pos then
				call_back(false)
				return
			end

			local succ, road_path = self:GenerateRoadPath(start_road, end_road)
			if not succ then
				call_back(false)
				return
			end

			local pos_list = {}
			for k,v in ipairs(road_path) do
				local point = self.way_points[v]
				table.insert(pos_list, {x = point.x, y = point.y})
			end

			-- 找到去road最近的切入点
			if #road_path > 0 then
				start_road_pos = self:FindBestPoint(start_pos, start_road_pos, pos_list[1])
			else
				start_road_pos = self:FindBestPoint(start_pos, start_road_pos, end_road_pos)
			end
			table.insert(pos_list, 1, start_road_pos)

			-- 找到去road最近的切入点
			if #road_path > 0 then
				end_road_pos = self:FindBestPoint(end_pos, end_road_pos, pos_list[#pos_list])
			else
				end_road_pos = self:FindBestPoint(end_pos, end_road_pos, start_road_pos)
			end
			table.insert(pos_list, end_road_pos)

			table.insert(pos_list, end_pos)

			call_back(true, pos_list)
		end)
	end)
end

-- 用二分查找法找到最近的切入点
local function GetBestPos(start_x, start_y, x1, y1, x2, y2, depth)
	depth = depth + 1
	if depth > 5 or (x1 == x2 and y1 == y2) then
		return x1, y1
	end

	local x = math.floor((x2 - x1) / 2) + x1
	local y = math.floor((y2 - y1) / 2) + y1

	if AStarFindWay:IsWayLine(start_x, start_y, x, y) then
		return GetBestPos(start_x, start_y, x, y, x2, y2, depth)
	else
		return GetBestPos(start_x, start_y, x1, y1, x, y, depth)
	end
end

function RoadFindWay:FindBestPoint(start_pos, pos_a, pos_b)
	if AStarFindWay:IsWayLine(start_pos.x, start_pos.y, pos_b.x, pos_b.y) then
		return pos_b
	end

	local best_x, best_y = GetBestPos(start_pos.x, start_pos.y, pos_a.x, pos_a.y, pos_b.x, pos_b.y, 0)
	return u3d.vec2(best_x, best_y)
end