RechargeVolumeLimitView = RechargeVolumeLimitView or BaseClass(SafeBaseView)

local ARROW_ROTATION = {[0] = 90, 45, 0, -45, -90}
local TASK_NUM = 4 --任务列表数量

function RechargeVolumeLimitView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/recharge_volume_ui_prefab", "recharge_volume_limit_view")
end

function RechargeVolumeLimitView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_volume_limit_go_to, BindTool.Bind(self.OnClickGotoGetLimitUp, self))

	if not self.task_list then
        self.task_list = {}
        for i = 0, TASK_NUM - 1 do
			local cell = RechargeVolumeLimitItemRender.New(self.node_list["task_item" .. i])
			cell:SetIndex(i)
			self.task_list[i] = cell
        end
    end
end

function RechargeVolumeLimitView:OpenCallBack()
	RechargeVolumeWGCtrl.Instance:SetVolumeMainViewTitleState(false)
end

function RechargeVolumeLimitView:CloseCallBack()
	RechargeVolumeWGCtrl.Instance:SetVolumeMainViewTitleState(true)
end

function RechargeVolumeLimitView:ReleaseCallBack()
    if self.task_list and #self.task_list > 0 then
        for i, cell in ipairs(self.task_list) do
            cell:DeleteMe()
        end

        self.task_list = nil
    end
end

function RechargeVolumeLimitView:OnFlush()
	local is_all_finish = RechargeVolumeWGData.Instance:GetIsFinishAllTask()
	self.node_list.limit_content:SetActive(is_all_finish)
	self.node_list.task_content:SetActive(not is_all_finish)

	self:FlushTaskContent()
	self:FlushLimitContent()
end

function RechargeVolumeLimitView:FlushTaskContent()
	local progress = RechargeVolumeWGData.Instance:GetFinishTaskProgress()
	local limit_value = RechargeVolumeWGData.Instance:GetFinishTaskLimitValue()
	local cur_task_cfg = RechargeVolumeWGData.Instance:GetCurTaskCfg()
	local all_task_cfg = RechargeVolumeWGData.Instance:GetAllTaskCfg()
	local cur_task_progress = RechargeVolumeWGData.Instance:GetCurTaskProgress()
	local add_num_list = RechargeVolumeWGData.Instance:GetTaskAddNumDataList()

	if IsEmptyTable(all_task_cfg) then
		return
	end

	for i = 1, 4 do
		self.node_list["slider_value" .. i]:SetActive(i <= progress)
		if add_num_list[i] then
			self.node_list["add_num" .. i]:SetActive(true)
			self.node_list["add_num" .. i].text.text = add_num_list[i]
		else
			self.node_list["add_num" .. i]:SetActive(false)
		end
	end

	self.node_list.arrow_img.transform.localRotation = Quaternion.Euler(0, 0, ARROW_ROTATION[progress])
	self.node_list.limit_value.text.text = limit_value


	for i = 0, TASK_NUM - 1 do
		if all_task_cfg and all_task_cfg[i] then
			self.node_list["task_item" .. i]:SetActive(true)
			self.task_list[i]:SetData(all_task_cfg[i])
		else
			self.node_list["task_item" .. i]:SetActive(false)
		end
	end
end

function RechargeVolumeLimitView:FlushLimitContent()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0   -- 开服天数
	local limit_day = RechargeVolumeWGData.Instance:GetOneBossQuotaLimitOpenDay() or 0
	self.node_list.func_open_root:CustomSetActive(open_day >= limit_day)
	self.node_list.func_not_open_root:CustomSetActive(open_day < limit_day)

	if open_day >= limit_day then
		local type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME
		-- 计算每日使用额度
		local time_limit_num = RechargeWGData.Instance:GetVirtualGoldLimitTimeLimitedByBoss(type)
		-- 计算每日可获得的额度
		local day_limit = RechargeVolumeWGData.Instance:GetEveryDayLimit()
		local get_color = time_limit_num >= day_limit and COLOR3B.GREEN or COLOR3B.WHITE
		-- 每日获得额度展示
		-- local time_limit_str = ToColorStr(string.format("%s/%s", time_limit_num, day_limit), get_color)
		self.node_list.one_limit_use_str.text.text = string.format("%s/%s", time_limit_num, day_limit)

		local target_quotalimit = RechargeVolumeWGData.Instance:GetOneBossQuotaLimit() or 0
		self.node_list.limit_up_txt.text.text = string.format(Language.RechargeVolume.DescKillBossGetVolume, target_quotalimit)
	else
		self.node_list.open_day_limit.text.text = limit_day
	end
end

function RechargeVolumeLimitView:OnClickGotoGetLimitUp()
	FunOpen.Instance:OpenViewNameByCfg(GuideModuleName.Boss)
end

----------------------------------任务item-----------------------
RechargeVolumeLimitItemRender = RechargeVolumeLimitItemRender or BaseClass(BaseRender)
function RechargeVolumeLimitItemRender:__init()

end

function RechargeVolumeLimitItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_goto_finish, BindTool.Bind(self.OnClickGotoFinish, self))
	XUI.AddClickEventListener(self.node_list.btn_receive, BindTool.Bind(self.OnClickReceiveTask, self))
end

function RechargeVolumeLimitItemRender:ReleaseCallBack()

end

function RechargeVolumeLimitItemRender:OnFlush()
    if not self.data then return end

	local cur_task_progress = RechargeVolumeWGData.Instance:GetTaskProgressBySeq(self.data.task_seq)
	local is_finish = RechargeVolumeWGData.Instance:GetTaskFinishFlag(self.data.task_seq)
	self.node_list.task_desc.text.text = string.format(self.data.task_desc, self.data.task_condition)
	self.node_list.cur_add_num.text.text = string.format(Language.RechargeVolume.TaskAddNumDesc, self.data.add_num)

	local is_receive = cur_task_progress >= 1
	self.node_list.btn_goto_finish:SetActive(not is_receive)
	self.node_list.btn_receive:SetActive(is_receive and is_finish ~= 1)
	self.node_list.image_ylq:SetActive(is_finish == 1)
end

function RechargeVolumeLimitItemRender:OnClickGotoFinish()
	local cur_task_cfg = RechargeVolumeWGData.Instance:GetTaskCfgBySeq(self.data.task_seq)
	FunOpen.Instance:OpenViewNameByCfg(cur_task_cfg.open_panel)
end

function RechargeVolumeLimitItemRender:OnClickReceiveTask()
	if self.data.task_seq >= 0 then
		RechargeWGCtrl.Instance:OnSendVirualGold2Req(VIRUAL_GOLD_OPERA_TYPE.FinishTask, self.data.task_seq)
	end
end