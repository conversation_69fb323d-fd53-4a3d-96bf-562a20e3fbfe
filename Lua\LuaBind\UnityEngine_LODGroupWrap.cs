﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_LODGroupWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.LODGroup), typeof(UnityEngine.Component));
		<PERSON><PERSON>Function("RecalculateBounds", RecalculateBounds);
		<PERSON><PERSON>("GetLODs", GetLODs);
		<PERSON><PERSON>("SetLODs", SetLODs);
		<PERSON><PERSON>unction("ForceLOD", ForceLOD);
		L.RegFunction("New", _CreateUnityEngine_LODGroup);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("localReferencePoint", get_localReferencePoint, set_localReferencePoint);
		<PERSON>.<PERSON>ar("size", get_size, set_size);
		<PERSON><PERSON>("lodCount", get_lodCount, null);
		<PERSON><PERSON>("fadeMode", get_fadeMode, set_fadeMode);
		<PERSON><PERSON>("animateCrossFading", get_animateCrossFading, set_animateCrossFading);
		L.RegVar("enabled", get_enabled, set_enabled);
		L.RegVar("crossFadeAnimationDuration", get_crossFadeAnimationDuration, set_crossFadeAnimationDuration);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_LODGroup(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.LODGroup obj = new UnityEngine.LODGroup();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.LODGroup.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RecalculateBounds(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)ToLua.CheckObject<UnityEngine.LODGroup>(L, 1);
			obj.RecalculateBounds();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetLODs(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)ToLua.CheckObject<UnityEngine.LODGroup>(L, 1);
			UnityEngine.LOD[] o = obj.GetLODs();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLODs(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)ToLua.CheckObject<UnityEngine.LODGroup>(L, 1);
			UnityEngine.LOD[] arg0 = ToLua.CheckStructArray<UnityEngine.LOD>(L, 2);
			obj.SetLODs(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceLOD(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)ToLua.CheckObject<UnityEngine.LODGroup>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.ForceLOD(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_localReferencePoint(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			UnityEngine.Vector3 ret = obj.localReferencePoint;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index localReferencePoint on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_size(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			float ret = obj.size;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index size on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lodCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			int ret = obj.lodCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lodCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fadeMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			UnityEngine.LODFadeMode ret = obj.fadeMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_animateCrossFading(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			bool ret = obj.animateCrossFading;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index animateCrossFading on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enabled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			bool ret = obj.enabled;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enabled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_crossFadeAnimationDuration(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.LODGroup.crossFadeAnimationDuration);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_localReferencePoint(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.localReferencePoint = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index localReferencePoint on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_size(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.size = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index size on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fadeMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			UnityEngine.LODFadeMode arg0 = (UnityEngine.LODFadeMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.LODFadeMode));
			obj.fadeMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fadeMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_animateCrossFading(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.animateCrossFading = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index animateCrossFading on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enabled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.LODGroup obj = (UnityEngine.LODGroup)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enabled = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enabled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_crossFadeAnimationDuration(IntPtr L)
	{
		try
		{
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.LODGroup.crossFadeAnimationDuration = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

