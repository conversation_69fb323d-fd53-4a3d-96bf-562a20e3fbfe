OpenServerAssistWGData = OpenServerAssistWGData or BaseClass()

function OpenServerAssistWGData:__init()
	if OpenServerAssistWGData.Instance ~= nil then
		ErrorLog("[OpenServerAssistWGData] attempt to create singleton twice!")
		return
	end

	OpenServerAssistWGData.Instance = self

	self.luxury_gift_login_flag = {}
	self.luxury_gift_reward_fetch_flag = {}
	self.hp_task_info = {}
	self.high_point_count = 0
	self.hp_reach_reward_flag = {}
	self.hp_reward_fetch_flag = {}

	local gdjz_all_cfg = ConfigManager.Instance:GetAutoConfig("islet_fierce_battle_auto")
    local rand_act_open_cfg = ConfigManager.Instance:GetAutoConfig("randactivityopencfg_auto")
	self.luxury_gift_all_cfg = ConfigManager.Instance:GetAutoConfig("login_luxury_gift_auto")
	self.high_point_all_cfg = ConfigManager.Instance:GetAutoConfig("haidian_auto")
	self.high_point_other_cfg = self.high_point_all_cfg and self.high_point_all_cfg.other[1]
	self.gdjz_other_cfg = gdjz_all_cfg and gdjz_all_cfg.other[1]
	self.gu_dao_been_click = false

    self.open_cfg_to_map = ListToMap(rand_act_open_cfg.open_cfg, "activity_type")
    RemindManager.Instance:Register(RemindName.OSA_Luxury_Gift, BindTool.Bind(self.GetLuxuryGiftRemind, self))
    RemindManager.Instance:Register(RemindName.OSA_High_Point, BindTool.Bind(self.GetHighPointRemind, self))
    RemindManager.Instance:Register(RemindName.OSA_Gu_Dao, BindTool.Bind(self.GetGuDaoRemind, self))
end

function OpenServerAssistWGData:__delete()
	OpenServerAssistWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.OSA_Luxury_Gift)
    RemindManager.Instance:UnRegister(RemindName.OSA_High_Point)
    RemindManager.Instance:UnRegister(RemindName.OSA_Gu_Dao)
end

-- 开服助力活动列表
function OpenServerAssistWGData:GetServerAssistActivityList()
    if not self.activity_list then
        self.activity_list = {
            -- 登录豪礼
            {tab_index = TabIndex.luxury_gift,
			activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT,
			remind_name = RemindName.OSA_Luxury_Gift},

            -- 开服嗨点
			-- {tab_index = TabIndex.high_point,
			-- activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT,
			-- remind_name = RemindName.OSA_High_Point},

			-- 孤岛激战
			{tab_index = TabIndex.gudao_jizhan,
			activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN,
			tab_name = Language.GuDaoJiZhan.FbName,
			remind_name = RemindName.OSA_Gu_Dao},
        }
    end

    return self.activity_list
end

function OpenServerAssistWGData:GetViewDefaultIndex()
	local act_list = self:GetServerAssistActivityList()
	if IsEmptyTable(act_list) then
		return 0
	end

	for k, v in ipairs(act_list) do
		local can_show = OpenServerAssistWGData.Instance:GetRandActivityCanShowByType(v.activity_type)
		if can_show then
			return v.tab_index
		end
	end

	return 0
end

-- 开服助力界面红点列表
function OpenServerAssistWGData:GetActivityRemindList()
    local act_list = self:GetServerAssistActivityList()

    -- 目前是 VerTab名列表  后面需要HorTab再拓展
    local bigtab_name_list = {}
    local view_remind_list = {}
    if IsEmptyTable(act_list) then
        return bigtab_name_list, view_remind_list
    end

    for k, v in ipairs(act_list) do
        local key_one = math.floor(v.tab_index / 10)
        local key_two = (v.tab_index % 10) + 1
        local open_cfg = self:GetRandActivityOpenCfgByType(v.activity_type)
        if not bigtab_name_list[key_one] then
            bigtab_name_list[key_one] = open_cfg and open_cfg.activity_name or v.tab_name or ""
        end

        if not view_remind_list[key_one] then
            view_remind_list[key_one] = {}
        end

        view_remind_list[key_one][key_two] = v.remind_name
    end

    return bigtab_name_list, view_remind_list
end

-- 属于开服助力活动
function OpenServerAssistWGData:IsServerAssistActivity(act_type)
    local act_list = self:GetServerAssistActivityList()
    if IsEmptyTable(act_list) then
        return false
    end

    for k, v in ipairs(act_list) do
        if v.activity_type == act_type then
            return true
        end
    end

    return false
end

function OpenServerAssistWGData:GetRandActivityCanShowByType(act_type)
	local can_show = false

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local is_show = false
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if nil ~= activity_info and (ACTIVITY_STATUS.OPEN == activity_info.status
	or ACTIVITY_STATUS.STANDY == activity_info.status) then
		is_show = true
	end

	local limit_level = 0
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT then
		local other = self:GetLuxuryGiftOtherCfg()
		if other and other.open_level then
			limit_level = other.open_level
		end
	elseif act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT then
		local all_cfg = self:GetHighPointAllCfg()
		local other_cfg = all_cfg and all_cfg.other and all_cfg.other[1]
		if other_cfg and other_cfg.open_level then
			limit_level = other_cfg.open_level
		end
	elseif act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN then
		local other = self:GetGuDaoJiZhanOtherCfg()
		limit_level = other and other.open_level or 0
	end

	can_show = is_show and role_level >= limit_level
    return can_show
end

function OpenServerAssistWGData:GetRandActivityOpenCfgByType(act_type)
    return self.open_cfg_to_map[act_type]
end

function OpenServerAssistWGData:GetOpenSererActIsOpen(act_type)
    local act_cfg = self:GetRandActivityOpenCfgByType(act_type)
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_cfg == nil or act_info == nil then
		return false
    end

    local cfg_data = ServerActivityWGData.Instance:GetActLimitTimeCfg(act_type)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if not cfg_data or role_level < (cfg_data.level_limit or 0) then
		return false
	end
    -- local open_day_time = TimeWGCtrl.Instance:GetServerRealStartTime() --开服时间戳
    -- local end_time1 = math.floor(act_cfg.end_time / 100) * 60 * 60 + act_cfg.end_time % 100 * 60
    -- local begin_time1 = math.floor(act_cfg.begin_time / 100) * 60 * 60 + act_cfg.begin_time % 100 * 60
    -- local end_time = open_day_time + (act_cfg.end_day_idx - 1) * 86400 + end_time1
    -- local open_time = open_day_time + (act_cfg.begin_day_idx - 1) * 86400 + begin_time1
    -- local cur_time = TimeWGCtrl.Instance:GetServerTime()
    -- print_error("act_type",act_type,"end_time",os.date("%H:%M:%S",end_time),
    -- "cur_time",os.date("%H:%M:%S",cur_time),"open_time",os.date("%H:%M:%S",open_time))
    -- --return end_time > cur_time and open_time <= cur_time
    return act_info.next_time > TimeWGCtrl.Instance:GetServerTime()
end

-- 随机活动  获得时间描述（X月X日00:00 ———— X月X日00:00）
function OpenServerAssistWGData:GetRandActTimeDesc(act_type)
	local act_cfg = self:GetRandActivityOpenCfgByType(act_type)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	local time_desc = ""

	if act_cfg == nil or act_info == nil then
		return time_desc
	end

	local one_day_second = 60 * 60 * 24
	local show_begin_time = act_info.start_time
	local show_begin_time_date = TimeUtil.FormatUnixTime2Date(show_begin_time)
	local show_end_time = act_info.start_time + (one_day_second * (act_cfg.end_day_idx - act_cfg.begin_day_idx))
	local show_end_time_date = TimeUtil.FormatUnixTime2Date(show_end_time)

	local function get_time_desc(time)
		local time_str = "00:00"
		if time > 0 then
			local hour = math.floor(time / 100)
			local minute = time % 100
			local hour_str = hour < 10 and "0" .. hour or tostring(hour)
			local minute_str = minute < 10 and "0" .. minute or tostring(minute)
			time_str = hour .. ":" .. minute_str
		end
		return time_str
	end

	local begin_day_time = get_time_desc(act_cfg.begin_time)
	local end_day_time = get_time_desc(act_cfg.end_time)
	local show_begin_str = string.format(Language.Common.XXMXXD, show_begin_time_date.month, show_begin_time_date.day)
	local show_end_str = string.format(Language.Common.XXMXXD, show_end_time_date.month, show_end_time_date.day)
	time_desc = show_begin_str .. begin_day_time .. " —— " .. show_end_str .. end_day_time

	return time_desc
end

function OpenServerAssistWGData:GetActivityTodayRemind(act_type)
	local key = self:GetActivityRemindKey(act_type)
	local cache_value = PlayerPrefsUtil.GetInt(key)
	return cache_value
end

function OpenServerAssistWGData:SetActivityTodayRemind(act_type)
	local key = self:GetActivityRemindKey(act_type)
	PlayerPrefsUtil.SetInt(key, 1)
end

function OpenServerAssistWGData:GetLuxuryGiftRemind()
	local is_show = self:GetRandActivityCanShowByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT)
	if not is_show then
		return 0
	end

	local cfg = self.luxury_gift_all_cfg.reward_cfg
	if IsEmptyTable(cfg) then
		return 0
	end

	for k, v in ipairs(cfg) do
		local reward_state = self:GetLuxuryGiftRewardStateByDay(v.act_day)
		if reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

    return 0
end

function OpenServerAssistWGData:GetHighPointRemind()
	local act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT
	local is_show = self:GetRandActivityCanShowByType(act_type)
	if not is_show then
		return 0
	end

	local today_remind = self:GetActivityTodayRemind(act_type)
	if today_remind == 0 then
		return 1
	end

	local cfg = self.high_point_all_cfg.reward_cfg
	if IsEmptyTable(cfg) then
		return 0
	end

	for k, v in ipairs(cfg) do
		local reward_state = self:GetHighPointRewardState(v.reward_grade)
		if reward_state == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

    return 0
end


function OpenServerAssistWGData:GetGuDaoRemind()
	local gudao_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUDAO_JIZHAN)
	if gudao_status.status == ACTIVITY_STATUS.OPEN and not self.gu_dao_been_click then
		return 1
	end
	return 0
end

function OpenServerAssistWGData:SetGuDaoClickStatus(status)
	self.gu_dao_been_click = status
	RemindManager.Instance:Fire(RemindName.OSA_Gu_Dao)
end


----------------------------- 登录豪礼 start ------------------------------------
function OpenServerAssistWGData:SetLuxuryGiftInfo(protocol)
	self.luxury_gift_login_flag = bit:d2b(protocol.login_flag)
	self.luxury_gift_reward_fetch_flag = bit:d2b(protocol.reward_fetch_flag)
end

function OpenServerAssistWGData:GetLuxuryGiftOtherCfg()
	return self.luxury_gift_all_cfg
			and self.luxury_gift_all_cfg.other
			and self.luxury_gift_all_cfg.other[1]
end

function OpenServerAssistWGData:GetLuxuryGiftRewardStateByDay(day, pass_day)
	local login_flag = self.luxury_gift_login_flag[32 - day]
	local fetch_flag = self.luxury_gift_reward_fetch_flag[32 - day]
	local reward_state = REWARD_STATE_TYPE.UNDONE

	if not login_flag or not fetch_flag then
		return reward_state
	end

	if login_flag == 1 and fetch_flag == 0 then
		reward_state = REWARD_STATE_TYPE.CAN_FETCH
	elseif login_flag == 1 and fetch_flag == 1 then
		reward_state = REWARD_STATE_TYPE.FINISH
	elseif pass_day and login_flag == 0 and pass_day >= day then
		reward_state = REWARD_STATE_TYPE.MISSED
	end

	return reward_state
end

function OpenServerAssistWGData:GetLuxuryGiftRewardList()
	local cfg = self.luxury_gift_all_cfg.reward_cfg
	local reward_list = {}

	if IsEmptyTable(cfg) then
		return reward_list
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT)
	if nil == activity_info then
		return reward_list
	end

	local one_day_second = 60 * 60 * 24
	local act_start_time = TimeUtil.GetEarlyTime(activity_info.start_time)
	local today_time = TimeUtil.GetEarlyTime(TimeWGCtrl.Instance:GetServerTime())
	local pass_day
	if act_start_time and today_time then
		pass_day = (today_time - act_start_time) / one_day_second
	end

	for k, v in ipairs(cfg) do
		local data = {}
		local show_time = activity_info.start_time + (one_day_second * (v.act_day - 1))
		local show_time_date = TimeUtil.FormatUnixTime2Date(show_time)
		data.show_date = string.format(Language.Common.XXMXXD, show_time_date.month, show_time_date.day)
		data.act_day = v.act_day
		data.get_state = self:GetLuxuryGiftRewardStateByDay(v.act_day, pass_day)
		local sort_add = (data.get_state == REWARD_STATE_TYPE.FINISH or data.get_state == REWARD_STATE_TYPE.MISSED) and 999 or 0
		data.sort = v.act_day + sort_add
		data.reward_list = {}
		local reward_item_list = v.reward_item
		for i = 0, #v.reward_item do
			table.insert(data.reward_list, reward_item_list[i])
		end
		table.insert(reward_list, data)
	end

	table.sort(reward_list, SortTools.KeyLowerSorter("sort"))
	return reward_list
end
----------------------------- 登录豪礼 end --------------------------------------


----------------------------- 开服嗨点 start ------------------------------------
function OpenServerAssistWGData:SetHighPointInfo(protocol)
	-- print_error("-----开服嗨点协议----", protocol)
	self.hp_task_info = protocol.hp_task_info
	self.high_point_count = protocol.haidian_count
	self.hp_reach_reward_flag = bit:d2b(protocol.reach_reward_flag)			-- 达标奖励标记
	self.hp_reward_fetch_flag = bit:d2b(protocol.reward_fetch_flag)			-- 达标奖励标记
end

function OpenServerAssistWGData:GetHighPointCount()
	return self.high_point_count
end

function OpenServerAssistWGData:GetHighPointOtherCfg()
	return self.high_point_other_cfg
end

function OpenServerAssistWGData:GetHighPointRewardState(grade)
	local reach_flag = self.hp_reach_reward_flag[32 - grade]
	local fetch_flag = self.hp_reward_fetch_flag[32 - grade]
	local reward_state = REWARD_STATE_TYPE.UNDONE

	if not reach_flag or not fetch_flag then
		return reward_state
	end

	if reach_flag == 1 and fetch_flag == 0 then
		reward_state = REWARD_STATE_TYPE.CAN_FETCH
	elseif reach_flag == 1 and fetch_flag == 1 then
		reward_state = REWARD_STATE_TYPE.FINISH
	end

	return reward_state
end

function OpenServerAssistWGData:GetHighPointTaskFinishCount(task_type)
	return self.hp_task_info[task_type] or 0
end

function OpenServerAssistWGData:GetHighPointAllCfg()
	return self.high_point_all_cfg
end

function OpenServerAssistWGData:GetHighPointRewardList(is_finish_sort)
	local cfg = self.high_point_all_cfg.reward_cfg
	local reward_list = {}

	if IsEmptyTable(cfg) then
		return reward_list
	end

	for k, v in ipairs(cfg) do
		local data = {}
		data.reward_grade = v.reward_grade
		data.get_state = self:GetHighPointRewardState(v.reward_grade)
		data.need_hp = v.unlock_haidian
		data.reward_list = {}
		local reward_item_list = v.reward_item
		for i = 0, #reward_item_list do
			table.insert(data.reward_list, reward_item_list[i])
		end

		data.sort = v.unlock_haidian
		data.sort2 = 0

		if is_finish_sort then
			data.sort2 = data.get_state == REWARD_STATE_TYPE.CAN_FETCH and 0
				or data.get_state == REWARD_STATE_TYPE.UNDONE and 1
				or data.get_state == REWARD_STATE_TYPE.FINISH and 2
				or 3
		end

		table.insert(reward_list, data)
	end

	table.sort(reward_list, SortTools.KeyLowerSorter("sort2", "sort"))
	return reward_list
end

function OpenServerAssistWGData:GetHighPointTaskList()
	local task_cfg = self.high_point_all_cfg.haidian_cfg
	local task_list = {}

	if IsEmptyTable(task_cfg) then
		return task_list
	end

	for k, v in ipairs(task_cfg) do
		local data = {}
		local finish_count = self:GetHighPointTaskFinishCount(v.task_type)
		local is_finish = finish_count >= v.times_limit
		local add_sort = is_finish and 999 or 0
		data.task_cfg = v
		data.finish_count = finish_count
		data.is_finish = is_finish
		data.sort = v.task_type + add_sort

		table.insert(task_list, data)
	end

	table.sort(task_list, SortTools.KeyLowerSorter("sort"))
	return task_list
end

function OpenServerAssistWGData:GetActivityRemindKey(act_type)
	local role_uuid = RoleWGData.Instance:GetUUid()
	local role_id = role_uuid.temp_low .. role_uuid.temp_high--RoleWGData.Instance:GetOriginUid()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	local key = ""
	if act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT then
		key = role_id .. "osa_hp" .. server_day
	else
		key = role_id .. "osa" .. server_day
	end

	return key
end
----------------------------- 开服嗨点 end --------------------------------------


----------------------------- 孤岛激战 start ------------------------------------
function OpenServerAssistWGData:GetGuDaoJiZhanOtherCfg()
	return self.gdjz_other_cfg
end

-- 获得时间描述（X月X日00:00 ———— X月X日00:00）
function OpenServerAssistWGData:GetGuDaoJiZhanActTimeDesc()
	local other_cfg = self:GetGuDaoJiZhanOtherCfg()
	local time_desc = "00:00"

	if other_cfg == nil then
		return time_desc
	end

	local one_day_second = 60 * 60 * 24
	local s_real_start_time = TimeWGCtrl.Instance:GetServerRealStartTime()

	local show_begin_time = s_real_start_time + ((other_cfg.open_activity_day - 1) * one_day_second)
	local show_begin_time_date = TimeUtil.FormatUnixTime2Date(show_begin_time)
	local show_end_time = s_real_start_time + ((other_cfg.end_activity_day - 1) * one_day_second)
	local show_end_time_date = TimeUtil.FormatUnixTime2Date(show_end_time)

	local time_str = "00:00"
	local show_begin_str = string.format(Language.Common.XXMXXD, show_begin_time_date.month, show_begin_time_date.day)
	local show_end_str = string.format(Language.Common.XXMXXD, show_end_time_date.month, show_end_time_date.day)
	time_desc = show_begin_str .. time_str .. " —— " .. show_end_str .. time_str

	return time_desc
end
