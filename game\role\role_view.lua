------------------------------------------------------------
--人物相关主View
------------------------------------------------------------
RoleView = RoleView or BaseClass(SafeBaseView)

local show_bg_index =
{
	TabIndex.role_refining,
	TabIndex.jingjie,
	TabIndex.jingmai,
}

function RoleView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	local bundle1 = "uis/view/common_panel_prefab"
	local bundle2 = "uis/view/role_ui_prefab"
	local bundle3 = "uis/view/role_ui/jingjie_ui_prefab"
	self:AddViewResource(show_bg_index, bundle1, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.role_intro, bundle2, "layout_intro_panel")
	self:AddViewResource(TabIndex.role_refining, bundle2, "layout_role_refining_dan")
	self:AddViewResource(TabIndex.role_change, bundle2, "layout_change_roles_view")
	self:AddViewResource(TabIndex.jingjie, bundle3, "layout_jingjie_new")
	self:AddViewResource(TabIndex.jingmai, bundle3, "layout_jingjie_jingmai")
	self:AddViewResource(0, bundle1, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self.default_index = TabIndex.role_intro
	self.show_head_remind = true
	self.change_role_attr_event = BindTool.Bind3(self.ChangeRoleAttrEvent, self)

	self:SetTabShowUIScene(TabIndex.role_intro, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.JUESE_INFO})
	self:SetTabShowUIScene(TabIndex.role_change, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.JUESE_HUANJUE})

	self.tab_sub = {}
	self.remind_tab = {
		{ RemindName.Role_Info_Tab },
		{ RemindName.Role_LianDan },
		nil,
		{RemindName.JingJie},
		{RemindName.JingMai},
	}

	self.attr_t = { "shengming_max", "gongji", "fangyu", "pojia", "yuansu_sh", "yuansu_hj", "move_speed" }
	self.select_xianjie_lv = 0
	self.is_first_advanced = true
	self.all_num = 0

	self.open_tween = nil
	self.close_tween = nil

	self:JingJieInit()
	self:JingMaiInit()
end

function RoleView:__delete()
	self.is_first_advanced = nil
	self.show_head_remind = nil
	self.all_num = 0
end

function RoleView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Role.TabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	--功能引导注册
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.RoleView, self.tabbar)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.RoleView, self.get_guide_ui_event)

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.data_change_event then
		self.data_change_event = BindTool.Bind(self.ItemChangeCallback, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.data_change_event)
	end
end

function RoleView:ReleaseCallBack()
	self:DeleteIntroView()
	self:DeleteRefiningView()
	self:ReleaseChangeRolesView()
	self:JingJieReleaseCallBack()
	self:JingMaiReleaseCallBack()

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self.select_xianjie_lv = 0
	if self.task_btn_effect then
		self.task_btn_effect:DeleteMe()
		self.task_btn_effect = nil
	end

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.RoleView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.data_change_event)
	self.data_change_event = nil
end

function RoleView:LoadIndexCallBack(index)
	if index == TabIndex.role_intro then
		self:InitIntroView()
	elseif index == TabIndex.role_refining then
		self:InitRefiningView()
	elseif index == TabIndex.role_change then
		self:InitChangeRolesView()
	elseif index == TabIndex.jingjie then
		self:JingJieLoadCallBack()
	elseif index == TabIndex.jingmai then
		self:JingMaiLoadCallBack()
	end

	-- SkillWGCtrl.Instance:SendExpEfficiencyReq()--经验效率请求
end

function RoleView:OpenCallBack()
	RoleWGData.Instance:AddListener(RoleWGData.ATTR_EVENT, self.change_role_attr_event)
	RankWGCtrl.Instance:SendWorldLevelReq()
end

function RoleView:ShowIndexCallBack(index)
	if self.node_list["right_container"] then
		self.node_list["right_container"]:SetActive(false)
	end

	local is_jpg = false
	local bg_res = "a3_ty_bg1"
	if index == TabIndex.role_intro then
		self:ViewAnimation()
	elseif index == TabIndex.jingmai then
		bg_res = "a3_lm_bj"
		is_jpg = true
		self:JingMaiShowIndexCallBack()
	elseif index == TabIndex.jingjie then
		bg_res = "a3_xj_bg"
		self:JingJieShowIndexCallBack()
		self:JingMaiCloseCallBack()
	end

	if self.node_list.RawImage_tongyong then
		local bundle, asset
		if is_jpg then
			bundle, asset = ResPath.GetRawImagesJPG(bg_res)
		else
			bundle, asset = ResPath.GetRawImagesPNG(bg_res)
		end
		-- 加载纹理图
		self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.RawImage_tongyong.raw_image:SetNativeSize()
		end)
	end

	if self.money_bar then
		self.money_bar:SetVisible(index ~= TabIndex.jingjie)
	end

	self:Flush(index)
	self.node_list.title_view_name.text.text = Language.Role.TabGrop[index / 10]
end

function RoleView:OnFlush(param_t, index)
	local pairs = pairs
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.role_intro then
				self:FluchRoleIntroViewAll()
				-- self:InitIntroView()
			elseif index == TabIndex.role_refining then
				self:FulshRefiningView(v)
			elseif index == TabIndex.role_change then
				self:OnFlushChangeRoles()
			elseif index == TabIndex.jingjie then
				self:JingJieOnFlush(param_t)
			elseif index == TabIndex.jingmai then
				self:JingMaiOnFlush(param_t)
			end

			for k1, v1 in pairs(v) do
				if "sub_view_name" == k1 then
					RoleWGCtrl.Instance:Open(nil, v)
				end
			end
		elseif "flushattr" == k then
			for k1, v1 in pairs(v) do
				if k1 == "prof" then
					self:ApperanceChange()
				end

				if "avatar_key_big" == k1 then
					self:UpdateHeadImg()
				end

				if "min_gong_ji" == k1 then
					self:FlushRoleAttrView(k1, v[k1])
				end

				if "capability" == k1 then
					if self.node_list["layout_intro_panel"] then
						self:SetCapability(v[k1])
					end
				end

				for k2, v2 in pairs(self.attr_t) do
					if k1 == v2 then
						self:FlushRoleAttrView(v2, v[v2])
					end
				end
			end
		elseif "exp_extra" == k then
			self:UpdateExpExtraPer()
		end
	end
end

function RoleView:CloseCallBack(is_all)
	RoleWGCtrl.Instance:CloseEquipAttr()
	if RoleWGCtrl.Instance.change_head_view then
		RoleWGCtrl.Instance.change_head_view:Close()
	end
	MainuiWGCtrl.Instance:CacheTableClearData()
	RoleWGData.Instance:RemoveListener(RoleWGData.ATTR_EVENT, self.change_role_attr_event)

	self:JingMaiCloseCallBack()
end

function RoleView:RoleDataChangeCallback(key, value)
	local role_attr_list = self:GetFlushParam().role_attr_list or {}
	role_attr_list[key] = value
	self:GetFlushParam().role_attr_list = role_attr_list
	self:Flush(0, "flushattr", { [key] = value })
end

function RoleView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.RoleJingjie then
		return
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function RoleView:ItemChangeCallback(item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.show_index == TabIndex.jingjie then
			if not self.jj_item_cell or not self.jj_item_cell:GetData() or not self.jj_item_cell:GetData().item_id or self.jj_item_cell:GetData().item_id <= 0 then
				return
			end

			if JingJieWGData.Instance:IsJingJieStuff(item_id) then
				self:JingJieOnFlush()
			end
		elseif self.show_index == TabIndex.jingmai then
			local is_break_up_item = JingJieWGData.Instance:IsJingMaiBreakItem(item_id)
			if is_break_up_item then
				self:JingMaiOnFlush()
			end
		end
	end
end