﻿using System;
using UnityEngine;

namespace Nirvana
{
	public sealed class UI3DDisplayRecord10086 : MonoBehaviour
	{
		internal void Initialize(Render<PERSON> renderer, UI3DDisplayCamera10086 camera)
		{
			this.attachRenderer = renderer;
			this.displayCamera = camera;
			this.layer = renderer.gameObject.layer;
			this.visible = renderer.enabled;
			bool flag = null != renderer.sharedMaterial;
			if (flag)
			{
				this.texture = renderer.sharedMaterial.mainTexture;
				bool flag2 = this.texture != null;
				if (flag2)
				{
					this.oldMipMapBias = this.texture.mipMapBias;
					this.texture.mipMapBias = -5f;
				}
			}
		}

		public void ManualDestroy()
		{
			bool flag = !this.attachRenderer;
			if (!flag)
			{
				this.attachRenderer.enabled = this.visible;
				base.gameObject.layer = this.layer;
				bool flag2 = this.texture != null;
				if (flag2)
				{
					this.texture.mipMapBias = this.oldMipMapBias;
				}
				this.attachRenderer = null;
				this.texture = null;
			}
		}

		private static bool IsParentOf(Transform obj, Transform parent)
		{
			bool flag = obj == parent;
			bool result;
			if (flag)
			{
				result = true;
			}
			else
			{
				bool flag2 = obj.parent == null;
				result = (!flag2 && UI3DDisplayRecord10086.IsParentOf(obj.parent, parent));
			}
			return result;
		}

        /*
		 * 当Transform的父级发生变化时调用。
		 * 如果当前对象的父级不再是displayCamera的子级，则销毁当前组件。
		 */
        private void OnTransformParentChanged()
		{
			bool flag = this.displayCamera == null || !UI3DDisplayRecord10086.IsParentOf(base.transform, this.displayCamera.transform);
			if (flag)
			{
				UnityEngine.Object.Destroy(this);
			}
		}

		private void OnDestroy()
		{
			this.ManualDestroy();
		}

		private int layer;

		private bool visible;

		private Renderer attachRenderer;

		private UI3DDisplayCamera10086 displayCamera;

		private Texture texture;

		private float oldMipMapBias = 0f;
	}
}
