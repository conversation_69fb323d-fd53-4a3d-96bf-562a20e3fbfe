-- 淬火数据
-------------------------------------------INIT_START---------------------------------------
function EquipmentWGData:InitEquipmengStrengthData()
    self.strength_level = {}					 -- 强化部位等级
    self.total_strength_level = {}               -- 普通装备强化等级
	self.xianqi_total_strength_level = {}        -- 饰品装备强化等级

    RemindManager.Instance:Register(RemindName.Equipment_Strength, BindTool.Bind(self.GetEquipStrengthRemindNum, self))
end

function EquipmentWGData:DeleteEquipmengStrengthData()
    RemindManager.Instance:UnRegister(RemindName.Equipment_Strength)
end
--------------------------------------------INIT_END-----------------------------------------

-----------------------------------------REMIND_START-----------------------------------------
-- 淬火大红点
function EquipmentWGData:GetEquipStrengthRemindNum()
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()

	if not IsEmptyTable(total_equip_body_data_list) then
		for k, v in pairs(total_equip_body_data_list) do
			if self:GetSthEquipBodyRemind(v.seq) > 0 then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_STRENGTH, 1, function ()
					FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_strength)
				end)

				return 1
			end
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_STRENGTH, 0)
	return 0
end

-- 装肉肉身淬火红点
function EquipmentWGData:GetSthEquipBodyRemind(equip_body_seq)
	local level_remind = self:GetEquipBodyLevelRemind(equip_body_seq)

	if level_remind >0 then
		return 1
	end

	local equip_data_list = self:GetRoleEquipStrengthListData(equip_body_seq)

	if not IsEmptyTable(equip_data_list) then
		for i, u in pairs(equip_data_list) do
			if self:IsCanStrengthEquip(equip_body_seq, u.index) then
				return 1
			end
		end
	end

	return 0
end

-- 装备肉身 淬火加成红点
function EquipmentWGData:GetEquipBodyLevelRemind(equip_body_seq)
	-- 普通装备
	local shenzhu_total_level = self:GetTotalStrengthLevel(equip_body_seq)
	local active_level = self:GetEquipStrengthLV(equip_body_seq)
	local total_cfg, next_total_cfg = self:GetEquipTotalStrengthCfg(equip_body_seq, active_level)
	local param = nil

	if next_total_cfg and next_total_cfg.total_level <= shenzhu_total_level and active_level <= next_total_cfg.total_level then
		local open_func = function ()
			-- 普通装备
			local jump_index = self:GetEquipBodyActiveEquipIndex(equip_body_seq, GameEnum.EQUIP_BIG_TYPE_NORMAL)
			if jump_index and jump_index >= 0 then
				param = {item_index = jump_index}
			end

			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_strength, param)
			RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STREHGTH_TIP, equip_body_seq)
			RoleWGCtrl.Instance:OpenEquipAttr()
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STRENGTH_ATTR_ACTIVE , 1,open_func)
		return 1
	end

	local shenzhu_total_level2 = self:GetTotalXianQiStrengthLevel(equip_body_seq)
	local active_level2 =  self:GetXianQiStrengthLV(equip_body_seq)
	local total_cfg2, next_total_cfg2 = self:GetEquipXianQiTotalStrengthCfg(equip_body_seq, active_level2)
	if next_total_cfg2 and next_total_cfg2.total_level <= shenzhu_total_level2 and active_level2 <= next_total_cfg2.total_level then
		local open_func = function ()
			local jump_index = self:GetEquipBodyActiveEquipIndex(equip_body_seq, GameEnum.EQUIP_BIG_TYPE_XIANQI)
			if jump_index and jump_index >= 0 then
				param = {item_index = jump_index}
			end

			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_strength, param)
			RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STREHGTH_TIP, equip_body_seq)
			RoleWGCtrl.Instance:OpenEquipAttr()
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STRENGTH_ATTR_ACTIVE , 1,open_func)
		return 1
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STRENGTH_ATTR_ACTIVE , 0)
	return 0
end

function EquipmentWGData:GetEquipBodyActiveEquipIndex(equip_bpdy_seq, equip_type)
	local item_index = -1
	local equip_body_index

	for i = 0, 15 do
		equip_body_index = equip_bpdy_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + i
		local data = EquipWGData.Instance:GetGridData(equip_body_index)
		local equip_big_type = EquipmentWGData.GetEquipSuitTypeByPartType(i)
		if data and data.item_id > 0 and equip_big_type == equip_type then
			return data.index
		end
	end

	return item_index
end

------------------------------------------REMIND_END------------------------------------------

----------------------------------------PROTOCOL_START----------------------------------------
function EquipmentWGData:SetEquipGridStrAllInfo(protocol)
	self.total_strength_level = protocol.total_strength_level                   --普通装备强化总等级
	self.xianqi_total_strength_level = protocol.xianqi_total_strength_level     --仙器装备强化总等级
	self.strength_level = protocol.strength_level                               -- 装备肉身 375件装备 level  每肉身15件装备
end

-- index 为装备肉身的375 序号
function EquipmentWGData:GetStrengthLevelByIndex(equip_body_index)
	return self.strength_level[equip_body_index] or 0
end

-- 获取普通装备强化总等级
function EquipmentWGData:GetTotalStrengthLevel(equip_body_seq)
	return self.total_strength_level[equip_body_seq] or 0
end

--获取仙器装备强化总等级
function EquipmentWGData:GetTotalXianQiStrengthLevel(equip_body_seq)
	return self.xianqi_total_strength_level[equip_body_seq] or 0
end
----------------------------------------PROTOVCOL_END-----------------------------------------

------------------------------------------INFO_START------------------------------------------
function EquipmentWGData:GetRoleEquipStrengthListData(equip_body_seq)
	local data_list = EquipWGData.Instance:GetDataList()
	local equip_data = {}
	
	if IsEmptyTable(data_list) then
		return equip_data
	end

	local index_offset = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
	for index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local data = data_list[index + index_offset]

		if not IsEmptyTable(data) then
			data.can_strength = self:IsCanStrengthEquip(equip_body_seq, data.index)
			data.sort_index = EquipmentWGData.GetSortIndex(index)
			local strengthen_level = self:GetStrengthLevelByIndex(data.index)
			data.strengthen_level = strengthen_level
			data.param.strengthen_level = strengthen_level
		end

		equip_data[index] = data
	end

	return equip_data
end

-- 普通装备强化套装
function EquipmentWGData:GetEquipTotalStrengthCfg(equip_body_seq, level)
	local curr_cfg = nil
	local next_cfg = nil
	local max_level = EquipBodyWGData.Instance:GetForgeBonusLimit(equip_body_seq) -- 最高级别
	local target_max_level = math.min(level, max_level)
	
	for i = #self.total_strength, 1, -1 do
		local total_level = self.total_strength[i].total_level

		if total_level <= target_max_level then
			curr_cfg = self.total_strength[i]

			if not IsEmptyTable(self.total_strength[i + 1]) then
				if self.total_strength[i + 1].total_level <= max_level then
					next_cfg = self.total_strength[i + 1]
				end
			else
				next_cfg = nil
			end

			break
		end

		if i == 1 then
			curr_cfg = nil
			next_cfg = self.total_strength[i]
		end
	end

	return curr_cfg, next_cfg
end

-- 仙器装备强化套装
function EquipmentWGData:GetEquipXianQiTotalStrengthCfg(equip_body_seq, level)
	local curr_cfg = nil
	local next_cfg = nil
	local max_level = EquipBodyWGData.Instance:GetXianQiForgeBonusLimit(equip_body_seq) -- 最高级别
	local target_max_level = math.min(level, max_level)

	for i = #self.xianqi_total_strength, 1, -1 do
		local total_level = self.xianqi_total_strength[i].total_level

		if total_level <= target_max_level then
			curr_cfg = self.xianqi_total_strength[i]

			if not IsEmptyTable(self.xianqi_total_strength[i + 1]) then
				if self.xianqi_total_strength[i + 1].total_level <= max_level then
					next_cfg = self.xianqi_total_strength[i + 1]
				end
			else
				next_cfg = nil
			end
			
			break
		end

		if i == 1 then
			curr_cfg = nil
			next_cfg = self.xianqi_total_strength[i]
		end
	end
	
	return curr_cfg, next_cfg
end

-- 根据类型获取玩家当前所有的强化，和下一级激活所需等级          
-- 类型： 普通0、 仙器1               equip_body_seq 选中的肉身装备
function EquipmentWGData:GetTotalQHLevelAndNextNeedByType(equip_type, equip_body_seq)
	equip_type = equip_type or 0
	local cur_progress = 0
	local next_need_level = 0
	local cur_total_level = 0   -- 当前总等级
	local cur_act_level = 0     -- 当前需激活等级
	local is_max = false
	local slider_desc = ""
	local total_cfg, next_total_cfg
	local level_str = ""
	if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
		cur_total_level = self:GetTotalStrengthLevel(equip_body_seq)
		cur_act_level = self:GetEquipStrengthLV(equip_body_seq)
		total_cfg, next_total_cfg = self:GetEquipTotalStrengthCfg(equip_body_seq, cur_act_level)
		level_str = string.format(Language.Equip.NormalEquipJiaChengCurLevel, cur_total_level)
	else
		cur_total_level = self:GetTotalXianQiStrengthLevel(equip_body_seq)
		cur_act_level = self:GetXianQiStrengthLV(equip_body_seq)
		total_cfg, next_total_cfg = self:GetEquipXianQiTotalStrengthCfg(equip_body_seq, cur_act_level)
		level_str = string.format(Language.Equip.ShiPinEquipJiaChengCurLevel, cur_total_level)
	end

	local temp_str = ""
	if next_total_cfg then
		local need_level = next_total_cfg.total_level
		cur_progress = cur_total_level / need_level
		next_need_level = need_level - cur_total_level
		if next_need_level <= 0 then
			temp_str = Language.Equip.JiaChengCanAct
		else
			temp_str = string.format(Language.Equip.JiaChengNextNeed, cur_total_level, need_level)
		end
	else
		cur_progress = 1
		temp_str = Language.Equip.JiaChengIsMax
	end

	slider_desc = level_str .. temp_str

	return cur_progress, slider_desc
end

function EquipmentWGData:GetNewStrengthCfgByLevel(equip_index, strength_level)
	if self.new_strength[equip_index] and self.new_strength[equip_index][strength_level] then
		return self.new_strength[equip_index][strength_level]
	end
	return nil
end
-------------------------------------------INFO_END------------------------------------------

------------------------------------------CAL_START------------------------------------------
-- 装备能否淬火
function EquipmentWGData:IsCanStrengthEquip(equip_body_seq, equip_body_index)
	if not self:IsEquipStrengthMaxLevel(equip_body_seq, equip_body_index) then
		local now_level = self:GetStrengthLevelByIndex(equip_body_index)
		local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(equip_body_index)
		local next_equip_cfg = self:GetNewStrengthCfgByLevel(equip_part, now_level + 1)

		if not IsEmptyTable(next_equip_cfg) then
			local stuff_enough = false

			if next_equip_cfg.stuff_num > 0 then --需要材料
				local num = ItemWGData.Instance:GetItemNumInBagById(next_equip_cfg.stuff_id)
				if num >= next_equip_cfg.stuff_num then
					stuff_enough = true
				end
			end

			local enough_coin = RoleWGData.Instance:GetIsEnoughUseCoin(next_equip_cfg.coin)
		
			if stuff_enough and enough_coin then
				return true
			end
		end

		-- local equip_cfg, next_equip_cfg = self:GetNewStrengthCfgByIndex(equip_body_index)
		-- local is_have_stuff = true

		-- if equip_cfg.stuff_num > 0 then --需要材料
		-- 	local num = ItemWGData.Instance:GetItemNumInBagById(equip_cfg.stuff_id)
		-- 	if num < equip_cfg.stuff_num then
		-- 		is_have_stuff = false
		-- 	end
		-- end

		-- local enough_coin = RoleWGData.Instance:GetIsEnoughUseCoin(equip_cfg.coin)
		-- return is_have_stuff and enough_coin
	end

	return false
end

-- 是否是最大淬火等级  equip_index 0 - 374
function EquipmentWGData:IsEquipStrengthMaxLevel(equip_body_seq, equip_body_index, level)
	local now_level = level or self:GetStrengthLevelByIndex(equip_body_index)
	local max_cuihuo_level = EquipBodyWGData.Instance:GetForgeLevelLimit(equip_body_seq)

	local _, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local next_equip_cfg = EquipmentWGData.Instance:GetNewStrengthCfgByLevel(equip_part, now_level + 1)
	return now_level >= max_cuihuo_level or IsEmptyTable(next_equip_cfg)
end

--计算属性强化加成的系数.
--type:0=武器,1=饰品.
function EquipmentWGData:CalcEquipStrengthAttrAddPer(cfg, type, equip_body_seq)
	local add_per = 0
	if type == 0 then
		local active_level = EquipmentWGData.Instance:GetEquipStrengthLV(equip_body_seq)
		local total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipTotalStrengthCfg(equip_body_seq, active_level)
		if total_cfg then
			add_per = total_cfg.per_zhuangbeiqianghua / 10000			--万分比.
		end
	else
		local active_level = EquipmentWGData.Instance:GetXianQiStrengthLV(equip_body_seq)
		local total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipXianQiTotalStrengthCfg(equip_body_seq, active_level)
		if total_cfg then
			add_per = total_cfg.per_shipinqianghua / 10000				--万分比.
		end
	end

	for key, value in pairs(cfg) do
		value.attr_value = math.floor(value.attr_value * add_per + value.attr_value)
		value.add_value = math.floor(value.add_value * add_per + value.add_value)
	end

	return cfg
end
--------------------------------------------CAL_END------------------------------------------