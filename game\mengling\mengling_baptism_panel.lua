function MengLingView:InitMengLingBaptismPanel()
    if not self.mengling_baptism_item then
        self.mengling_baptism_item = ItemCell.New(self.node_list.mengling_baptism_item)
        -- self.mengling_baptism_item:SetItemTipFrom(ItemTip.FROM_MENGLING_EQUIP)
    end

    if not self.mengling_baptism_cost_item then
        self.mengling_baptism_cost_item = ItemCell.New(self.node_list.mengling_baptism_cost_item)
    end

    if not self.mengling_baptism_slider_list then
        self.mengling_baptism_slider_list = {}

        for i = 0, 4 do
            self.mengling_baptism_slider_list[i] = MengLingBaptismSliderItemRender.New(self.node_list["mengling_baptism_shuxing_" .. i])
        end
    end

    self.mengling_baptism_cap_up = false
    self.node_list.mengling_baptism_gou:CustomSetActive(MengLingWGData.Instance:GetMengLingBaptismCostState())
    XUI.AddClickEventListener(self.node_list.mengling_baptism_lock_click, BindTool.Bind(self.OnCLickMengLingBaptismLockClick, self))
    XUI.AddClickEventListener(self.node_list.btn_mengling_baptism, BindTool.Bind(self.OnCLickMengLingBaptism, self))
    XUI.AddClickEventListener(self.node_list.btn_mengling_baptism_accept, BindTool.Bind(self.OnCLickMengLingAccept, self))
    XUI.AddClickEventListener(self.node_list.btn_mengling_baptism_back, BindTool.Bind(self.OnCLickMengLingBack, self))
    XUI.AddClickEventListener(self.node_list.btn_mengling_baptism_tupo, BindTool.Bind(self.OnCLickMengLingTuPo, self))
end

function MengLingView:DeleteMengLingBaptismPanel()
    if self.mengling_baptism_item then
        self.mengling_baptism_item:DeleteMe()
        self.mengling_baptism_item = nil
    end

    if self.mengling_baptism_cost_item then
        self.mengling_baptism_cost_item:DeleteMe()
        self.mengling_baptism_cost_item = nil
    end

    if self.mengling_baptism_slider_list then
        for k, v in pairs(self.mengling_baptism_slider_list) do
            v:DeleteMe()
        end

        self.mengling_baptism_slider_list = nil
    end

    self.mengling_baptism_cap_up = nil
end

function MengLingView:FlushMengLingBaptismPanel()
    if self:MengLingCanShowOperaPanel() then
        self.node_list.mengling_no_wearequip:CustomSetActive(true)
        self.node_list.mengling_baptism_panel_root:CustomSetActive(false)
        return
    end

    self.node_list.mengling_no_wearequip:CustomSetActive(false)
    self.node_list.mengling_baptism_panel_root:CustomSetActive(true)

    local data_info = MengLingWGData.Instance:GetMengLingEquipCellInfo(self.select_equip_suit_seq, self.select_equip_item_slot)
    self.mengling_baptism_item:SetData(data_info)

    local refine_level = data_info.refine_level
    local is_max_attr = true
    local can_tupo = true
    local has_change_attr = data_info.has_refine_change_value
    local refine_level_cfg = MengLingWGData.Instance:GetmengLingRefineLevelCfg(self.select_equip_suit_seq, refine_level)
    local next_level_cfg = MengLingWGData.Instance:GetmengLingRefineLevelCfg(self.select_equip_suit_seq, refine_level + 1)


    if IsEmptyTable(next_level_cfg) then
        can_tupo = false
    end

    local cur_attr_list = {}
    local next_attr_list = {}
    local attr_index = 1

    local other_cfg = MengLingWGData.Instance:GetMengLingOtherCfg()
    local limit_color = other_cfg and other_cfg.refine_color_limit or GameEnum.ITEM_COLOR_WHITE
    local can_baptism = data_info.color >= limit_color

    if not can_baptism then
        can_tupo = false
    end

    local refine_break_num = other_cfg and other_cfg.refine_break_num or 0
    local refine_num = 0

    for i = 0, 4 do
        local attr_cfg = MengLingWGData.Instance:GetMengLingRefineAttrCfg(self.select_equip_suit_seq, i)

        local data = {}
        local next_refine_value = data_info.next_refresh_list[i] or 0

        data.seq = self.select_equip_suit_seq
        data.slot = self.select_equip_item_slot
        data.cur_value = data_info.refine_list[i] or 0
        data.next_value = has_change_attr and next_refine_value or data.cur_value
        data.is_add = data.next_value > data.cur_value
        data.is_decrease = data.next_value < data.cur_value
        data.is_change = data.next_value ~= data.cur_value
        data.attr_id = attr_cfg.attr_id
        data.is_max_attr = data.cur_value >= refine_level_cfg.break_limit
        data.limit_value = refine_level_cfg.break_limit

        if data.is_change then
            cur_attr_list[attr_index] = {attr_id = attr_cfg.attr_id, attr_value = data.cur_value}
            next_attr_list[attr_index] = {attr_id = attr_cfg.attr_id, attr_value = data.next_value}
            attr_index = attr_index + 1
        end

        if not data.is_max_attr then
            is_max_attr = false
        else
            refine_num = refine_num + 1
        end

        self.mengling_baptism_slider_list[i]:SetData(data)
    end

    if refine_num < refine_break_num then
        can_tupo = false
    end

    self.node_list.mengling_limit_baptism:CustomSetActive(not can_baptism)
    self.node_list.mengling_baptism_max_flag:CustomSetActive(can_baptism and is_max_attr and not can_tupo)
    self.node_list.mengling_baptism_tupo:CustomSetActive(can_baptism and can_tupo)
    self.node_list.mengling_baptism_result:CustomSetActive(can_baptism and not is_max_attr and has_change_attr)
    self.node_list.mengling_baptism_cost_panel:CustomSetActive(can_baptism and not is_max_attr and not has_change_attr and not can_tupo)

    if can_baptism then
        if is_max_attr then
            if can_tupo then
                local break_limit = next_level_cfg.break_limit
                self.node_list.text_mengling_baptism_tupo.text.text = string.format(Language.MengLing.MengLingRefineAttrValue, break_limit)
            end
        else
            if has_change_attr then
                local cur_attribute = AttributePool.AllocAttribute()
                local next_attribute = AttributePool.AllocAttribute()
    
                for i = 1, #cur_attr_list do
                    local attr_id = cur_attr_list[i].attr_id
                    local attr_cur_value = cur_attr_list[i].attr_value
                    local attr_next_value = next_attr_list[i].attr_value
    
                    if attr_id and attr_id > 0 then
                        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
    
                        if attr_cur_value > 0 and cur_attribute[attr_str] then
                            cur_attribute[attr_str] = cur_attribute[attr_str] + attr_cur_value
                        end
                        
                        if attr_next_value > 0 and next_attribute[attr_str] then
                            next_attribute[attr_str] = next_attribute[attr_str] + attr_next_value
                        end
                    end
                end
    
                local cur_cap = AttributeMgr.GetCapability(cur_attribute)
                local next_cap = AttributeMgr.GetCapability(next_attribute)
                local is_add = next_cap > cur_cap
                local diff_value = math.floor(next_cap - cur_cap)
                local color = is_add and COLOR3B.GREEN or COLOR3B.RED
                local cap_value = (is_add and "+ " or "- ") .. math.abs(diff_value)
                local cap_desc = ToColorStr(cap_value, color)
                self.mengling_baptism_cap_up = is_add
                self.node_list.text_mengling_baptism_result.text.text = string.format(Language.MengLing.MengLingBaptismCapDesc, cap_desc)
            else
                self.node_list.mengling_baptism_up_desc.text.text = Language.MengLing.MengLingBaptismUpDesc
                local baptism_cost_cfg = MengLingWGData.Instance:GetDreamSpriteCfgBySeq(self.select_equip_suit_seq)
    
                if not IsEmptyTable(baptism_cost_cfg) then
                    local cost_item_id = baptism_cost_cfg.refine_stuff_id
                    local cost_item_num = baptism_cost_cfg.refine_stuff_num
                    local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
                    local enough = item_num >= cost_item_num
                    self.mengling_baptism_cost_item:SetFlushCallBack(function ()
                        local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
                        self.mengling_baptism_cost_item:SetRightBottomColorText(right_text)
                        self.mengling_baptism_cost_item:SetRightBottomTextVisible(true)
                    end)
                    self.mengling_baptism_cost_item:SetData({item_id = cost_item_id})
                end
            end
        end
    else
        local color = ToColorStr(Language.Common.ColorName[limit_color], ITEM_COLOR[limit_color])
        self.node_list.mengling_limit_baptism_desc.text.text = string.format(Language.MengLing.MengLingBaptismLimitDesc, color)
    end
end

function MengLingView:OnCLickMengLingBaptismLockClick()
    local state = MengLingWGData.Instance:GetMengLingBaptismCostState()
    if not state then
        TipWGCtrl.Instance:OpenAlertTips(Language.MengLing.MengLingBaptismAutoBuyStuff, function ()
            MengLingWGData.Instance:SetMengLingBaptismCostState(true)
            self.node_list.mengling_baptism_gou:CustomSetActive(true)
        end,
        function ()
            self.node_list.mengling_baptism_gou:CustomSetActive(false)
        end,
        nil,
        function ()
            self.node_list.mengling_baptism_gou:CustomSetActive(false)
        end
    )
    else
        MengLingWGData.Instance:SetMengLingBaptismCostState(false)
        self.node_list.mengling_baptism_gou:CustomSetActive(false)
    end
end

function MengLingView:OnCLickMengLingBaptism()
    if IsEmptyTable(self.select_equip_item_data) then
        return
    end

    if MengLingWGData.Instance:GetMengLingBaptismCostState() then
        MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_REFINE, 
            self.select_equip_suit_seq, self.select_equip_item_slot, 1)
    else
        local baptism_cost_cfg = MengLingWGData.Instance:GetDreamSpriteCfgBySeq(self.select_equip_suit_seq)

        if not IsEmptyTable(baptism_cost_cfg) then
            local cost_item_id = baptism_cost_cfg.refine_stuff_id
            local cost_item_num = baptism_cost_cfg.refine_stuff_num
            local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    
            if item_num >= cost_item_num then
                MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_REFINE, 
                    self.select_equip_suit_seq, self.select_equip_item_slot, 0)
            else
                TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
            end
        end
    end
end

function MengLingView:OnCLickMengLingAccept()
    if IsEmptyTable(self.select_equip_item_data) then
        return
    end

    local ok_func = function(...)
        MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_REFINE_RESULT, 
            self.select_equip_suit_seq, self.select_equip_item_slot, 1)
    end

    if  self.mengling_baptism_cap_up then
        ok_func()
    else
        TipWGCtrl.Instance:OpenAlertTips(Language.MengLing.MengLingBaptismCapDownDesc, ok_func, nil, nil, nil, nil, nil, nil, nil, Language.MengLing.MengLingBaptismCapDownConfirm)
    end
end

function MengLingView:OnCLickMengLingBack()
    if IsEmptyTable(self.select_equip_item_data) then
        return
    end

    MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_REFINE_RESULT, 
        self.select_equip_suit_seq, self.select_equip_item_slot, 0)
end

function MengLingView:OnCLickMengLingTuPo()
    if IsEmptyTable(self.select_equip_item_data) then
        return
    end

    MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.DREAM_SPIRIT_OPERATE_TYPE_REFINE_UP_LEVEL, 
        self.select_equip_suit_seq, self.select_equip_item_slot)
end

----------------------------------MengLingBaptismSliderItemRender------------------------------
MengLingBaptismSliderItemRender = MengLingBaptismSliderItemRender or BaseClass(BaseRender)

function MengLingBaptismSliderItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local data = self.data
    local is_add = data.is_add
    local is_decrease = data.is_decrease
    local is_change = data.is_change
    local is_max_attr = data.is_max_attr
    local red_slider_value = 0
    local green_slider_value = 0
    local orange_slider_value = 0
    local green_slider_target_value = -1
    local orange_slider_targetA_value = -1
    local value = data.cur_value / data.limit_value
    local attr_id = data.attr_id

    if is_max_attr and not is_change then
        orange_slider_value = 1
    else
        orange_slider_value = value

        if is_change then

            local target_value = data.next_value / data.limit_value

            if is_add then
                green_slider_value = value
                green_slider_target_value = target_value
            else
                red_slider_value = value
                orange_slider_targetA_value = target_value
            end
        end
    end

    self.node_list.slider_green.slider.value = green_slider_value
    self.node_list.slider_yellow.slider.value = orange_slider_value
    self.node_list.slider_red.slider.value = red_slider_value

    if green_slider_target_value >= 0 then
        self.node_list.slider_green.slider:DOValue(green_slider_target_value, 0.5)
    end

    if orange_slider_targetA_value >= 0 then
        self.node_list.slider_yellow.slider:DOValue(orange_slider_targetA_value, 0.5)
    end

    self.node_list.txt_percent_main.text.text = string.format("%0.2f%%", value * 100)
    self.node_list.icon_arrow_up:CustomSetActive(is_add)
    self.node_list.txt_percent1:CustomSetActive(is_add)
    self.node_list.icon_arrow_down:CustomSetActive(is_decrease)
    self.node_list.txt_percent2:CustomSetActive(is_decrease)

    local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
    local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, true)
    local value_str  = AttributeMgr.PerAttrValue(attr_str, data.cur_value)
    self.node_list.txt_base_attr_1.text.text = attr_name
    self.node_list.txt_plus.text.text = value_str

    if is_add then
        local change_value = AttributeMgr.PerAttrValue(attr_str, data.next_value - data.cur_value)
        self.node_list.txt_percent1.text.text = change_value
    end

    if is_decrease then
        local change_value = AttributeMgr.PerAttrValue(attr_str, data.cur_value - data.next_value)
        self.node_list.txt_percent2.text.text = change_value
    end
end