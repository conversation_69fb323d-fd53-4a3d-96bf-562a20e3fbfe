-- F2升级礼包

function WelfareView:InitUpGradeView()
	if not self.upgrade_list then
		self.upgrade_list = AsyncListView.New(WelfareUpLevelGift, self.node_list.ph_sj_list)
	end
--[[ 	self.node_list.ph_sj_tip_btn.button:AddClickListener(function ()
		RuleTip.Instance:SetTitle(Language.Welfare.LevelGiftTipsTitle)
		RuleTip.Instance:SetContent(Language.Welfare.LevelGiftTipsDesc)
	end) ]]
end

function WelfareView:DescoryUpGrade()
	if self.upgrade_list then
		self.upgrade_list:DeleteMe()
		self.upgrade_list = nil
	end
end

function WelfareView:OnFlushUpGrade()
	local cfg_list = WelfareWGData.Instance:GetUpLevelGiftCfgList()
	local ser_data_list = WelfareWGData.Instance:GetUpLevelAndVIPGiftInfo()
	if cfg_list and ser_data_list then
		local fetch_flag = ser_data_list.level_gift_fetch_flag
		local server_count = ser_data_list.server_count
		local data_list = {}
		for i=1,#cfg_list do
			if fetch_flag[i] == 1 or server_count[i] <= 0 then
				data_list[100 + i] = cfg_list[i]
			else
				data_list[i] = cfg_list[i]
			end
		end
		data_list = SortTableKey(data_list)
		self.upgrade_list:SetDataList(data_list)
	end
end

---------------------------------------------------------------------------------------

WelfareUpLevelGift = WelfareUpLevelGift or BaseClass(BaseRender)

function WelfareUpLevelGift:__init()
	self.item_list = {}
end

function WelfareUpLevelGift:__delete()
	for k,v in pairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = {}
end

function WelfareUpLevelGift:LoadCallBack()
	--self.save_btn_pos = self.node_list.btn_lingqu.rect.anchoredPosition
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickGetBtn, self))
end

function WelfareUpLevelGift:OnFlush()
	local data = self:GetData()
	local gift_info = WelfareWGData.Instance:GetGiftInfoByIndex(GITT_LINGQU_TYPE.UPGRADE, data.index)
	if not data or not gift_info then
		return
	end

	--self.node_list.reward_desc.text.text = not gift_info.is_infinite and string.format(Language.Welfare.LevelGiftRemain, gift_info.ser_count) or ""
	--self.node_list.btn_lingqu.rect.anchoredPosition = gift_info.is_infinite and Vector2(self.save_btn_pos.x, 0) or self.save_btn_pos
	
	self:FlushRewardList(data.level_gift_item, gift_info)
	self:FlushGiftItem(data, gift_info)
	self:FlushCanExchangeCount(data, gift_info)
end

function WelfareUpLevelGift:FlushCanExchangeCount(data, gift_info)
	local desc = ""-- Language.Welfare.GiftNoCountLimit
	if data.server_count > 0 then
		local color = gift_info.ser_count > 0 and COLOR3B.L_GREEN or COLOR3B.RED
		desc = string.format(Language.Welfare.GiftCanExchange, ToColorStr(gift_info.ser_count .. "/" .. data.server_count, color))
	end

	self.node_list.desc_exchange_limit.text.text = desc
end

function WelfareUpLevelGift:FlushGiftItem(data, gift_info)
	--self.node_list.get_desc.text.text = string.format(Language.Welfare.LevelGiftTarget, data.level_limit)
	if self.node_list.djjl_levellimit_text then
			self.node_list.djjl_levellimit_text.text.text = string.format(Language.Welfare.LevelGift, data.level_limit)
	end
	self.node_list.yilingqu_img:SetActive(gift_info.fetch_flag)
	self.node_list.not_enough_img:SetActive(gift_info.ser_count <= 0 and not gift_info.fetch_flag)
	self.node_list.btn_lingqu:SetActive(gift_info.ser_count > 0 and not gift_info.fetch_flag)
	self.node_list.remind:SetActive(gift_info.ser_count > 0 and gift_info.can_fetch)
	XUI.SetButtonEnabled(self.node_list.btn_lingqu, gift_info.ser_count > 0 and gift_info.can_fetch)
end

function WelfareUpLevelGift:FlushRewardList(reward_list, gift_info)
	reward_list = OperationActivityWGData.Instance:SortDataByItemColor(reward_list)

	local item_list = self.item_list
	if #reward_list > #item_list then
		local cell_parent = self.node_list.cell_group
		for i=1,#reward_list do
			item_list[i] = item_list[i] or ItemCell.New(cell_parent)
		end
		self.item_list = item_list
	end

	for i=1,#item_list do
		if reward_list[i] then
			item_list[i]:SetFlushCallBack(function ()
				item_list[i]:SetLingQuVisible(gift_info.fetch_flag)
			end)
			item_list[i]:SetData(reward_list[i])
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end

	self.node_list.reward_rect.scroll_rect.horizontalNormalizedPosition = 0
	--self.node_list.reward_rect.scroll_rect.horizontal = #reward_list >= 5
end

function WelfareUpLevelGift:OnClickGetBtn()
	local data = self:GetData()
	if not IsEmptyTable(data) then
		WelfareWGCtrl.Instance:RequestLevelAndVIPGift(data.index, GITT_LINGQU_TYPE.UPGRADE)
	end
end