-------------------------------------
-- 诸神-文物
-------------------------------------
local TOGGLE_MAX = 5
local SHENSHI_NUM = 8
function TianShenView:ULInitView()
	self.ul_select_ts_index = -1
	self.ul_jump_remind = nil -- 是否是跳转
	self.need_show_compose_attr_effect = false
	-- if not self.ul_list_view then
	--     self.ul_list_view = AsyncListView.New(LingHeULListRender, self.node_list["ul_list_view"])
	--     self.ul_list_view:SetSelectCallBack(BindTool.Bind(self.OnULSelectTianShenItemHandler, self))
	-- end

	if not self.ul_role_model then
		self.ul_role_model = RoleModel.New()
		self.ul_role_model:SetUISceneModel(self.node_list["ul_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.ul_role_model, TabIndex.tianshen_linghe_uplevel)
	end

	if not self.ts_ss_list_view then
		self.ts_ss_list_view = AsyncListView.New(TianShenSSItemRender, self.node_list.ts_ss_select_list_view) -- 天神选择列表
		self.ts_ss_list_view:SetSelectCallBack(BindTool.Bind(self.OnULSelectTianShenItemHandler, self))
	end

	if not self.ss_star_list then
		self.ss_star_list = {}
		for i=1,5 do
			self.ss_star_list[i] = self.node_list["ss_star_" .. i]
		end
	end

	-- 槽位list
	if not self.lh_slot_list then
		self.lh_slot_list = {}
		local parent_node = self.node_list["ul_linghe_list"]
		for i = 0, TianShenLingHeWGData.MAX_SLOT - 1 do
			local cell = LingHeULSlotRender.New(parent_node:FindObj("ul_slot_" .. i))
			cell:SetIndex(i)
			cell:AddClickEventListener(BindTool.Bind(self.OnClickULSlotCallBack, self))
			self.lh_slot_list[i] = cell
		end
	end

	-- 属性列表
	if not self.ul_attr_list then
		self.ul_attr_list = {}
		local parent_node = self.node_list["ul_attr_list"]
		local attr_num = parent_node.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
			cell:SetIndex(i)
			self.ul_attr_list[i] = cell
		end
	end

	-- 合成属性列表
	if not self.compose_attr_list then
		self.compose_attr_list = {}
		local parent_node = self.node_list["compose_attr_list"]
		local attr_num = parent_node.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
			cell:SetIndex(i)
			self.compose_attr_list[i] = cell
		end
	end

	if not self.ul_show_linghe_item then
		self.ul_show_linghe_item = BaseLingHeCell.New(nil, self.node_list["ul_show_linghe_node"])
	end

	if not self.ul_cost_item then
		self.ul_cost_item = ItemCell.New(self.node_list["ul_cost_item_node"])
	end

	if not self.compose_left_cell then
		self.compose_left_cell = BaseLingHeCell.New(nil, self.node_list["compose_node_1"])
	end
	
	if not self.compose_right_cell then
		self.compose_right_cell = BaseLingHeCell.New(nil, self.node_list["compose_node_2"])
	end
	
	if not self.compose_cost_item then
		self.compose_cost_item = ItemCell.New(self.node_list["compose_cost_item_node"])
	end

	XUI.AddClickEventListener(self.node_list["ul_btn_change"], BindTool.Bind(self.OnClickWearAll, self))
	XUI.AddClickEventListener(self.node_list["ul_btn_uplevel"], BindTool.Bind(self.OnClickUpLevelBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_resove"], BindTool.Bind(self.OnClickResoveBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_draw"], BindTool.Bind(self.OnClickDrawBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_compose_block"], BindTool.Bind(self.OnClickComposeClockBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_compose"], BindTool.Bind(self.OnClickComposeBtn, self))
end

function TianShenView:ULReleaseCallBack()
	self.ul_jump_remind = nil
	self.ul_select_ts_index = nil
	self.ul_select_ts_data = nil
	self.ul_select_slot_index = nil
	self.ul_select_slot_data = nil
	self.ul_select_seq = nil

	self.linghe_slot_cache = nil
	self.linghe_slot_level_cache = nil

	self.need_show_compose_attr_effect = nil
	-- if self.ul_list_view then
	--     self.ul_list_view:DeleteMe()
	--     self.ul_list_view = nil
	-- end

	self.ss_star_list = nil

	if self.ts_ss_list_view then
		self.ts_ss_list_view:DeleteMe()
		self.ts_ss_list_view = nil
	end

	if self.lh_slot_list then
		for k, v in pairs(self.lh_slot_list) do
			v:DeleteMe()
		end
		self.lh_slot_list = nil
	end

	if self.ul_attr_list then
		for k, v in pairs(self.ul_attr_list) do
			v:DeleteMe()
		end
		self.ul_attr_list = nil
	end

	if self.compose_attr_list then
		for k, v in pairs(self.compose_attr_list) do
			v:DeleteMe()
		end
		self.compose_attr_list = nil
	end

	if self.ul_show_linghe_item then
		self.ul_show_linghe_item:DeleteMe()
		self.ul_show_linghe_item = nil
	end

	if self.ul_cost_item then
		self.ul_cost_item:DeleteMe()
		self.ul_cost_item = nil
	end

	if self.compose_left_cell then
		self.compose_left_cell:DeleteMe()
		self.compose_left_cell = nil
	end

	if self.compose_right_cell then
		self.compose_right_cell:DeleteMe()
		self.compose_right_cell = nil
	end

	if self.compose_cost_item then
		self.compose_cost_item:DeleteMe()
		self.compose_cost_item = nil
	end

	if self.ul_role_model then
		self.ul_role_model:DeleteMe()
		self.ul_role_model = nil
	end

	if self.compose_alert_view then
		self.compose_alert_view:DeleteMe()
		self.compose_alert_view = nil
	end
end

function TianShenView:ULShowIndexCallBack()
	self.ul_jump_remind = true
end

-- 神饰界面刷新
function TianShenView:ShenShiULOnFlush(force_jump_ts_index)
	self.ss_new_data_flush = true
	local data_list = TianShenLingHeWGData.Instance:GetUpLevelShowList()
	self.ts_ss_list_view:SetDataList(data_list)

	local jump_cell_index = nil
	local jump_ts_index = force_jump_ts_index or self.ul_select_ts_index
	if jump_ts_index >= 0 then
		for i, v in ipairs(data_list) do
			if v.index == jump_ts_index then
				jump_cell_index = i
			end
		end
	end

	if not jump_cell_index then --  先获取当前上阵
		for i, v in ipairs(data_list) do
			local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(v.ts_index)
			if tianshen_info and tianshen_info.zhan_index ~= -1 then
				jump_cell_index = i
			end
		end
	end

	local max_series = 0
	if not jump_cell_index then -- 高品质优先
		for i, v in ipairs(data_list) do
			if max_series < v.series then
				max_series = v.series
				jump_cell_index = i
			end
		end
	end

	jump_cell_index = jump_cell_index or 1
	self.ts_ss_list_view:JumpToIndex(jump_cell_index)
	local used_num = TianShenLingHeWGData.Instance:GetBagItemUsedNum()
	self.node_list.btn_resove_remind:SetActive(used_num >= TianShenLingHeWGData.MAX_BAG)
end

-- 列表选择回调
function TianShenView:OnULSelectTianShenItemHandler(item)
	-- print_error("", is_click)
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	local is_same_ts = self.ul_select_ts_index == data.ts_index
	self.ul_select_ts_index = data.ts_index
	self.ul_select_ts_data = data
	self:FlushULTianShenCtrlPart()
	-- 天神展示
	if not is_same_ts then
		self:FlushULModelDisPlay()
	end
	-- 选择槽位
	local jump_slot = nil
	local slot_list = data.slot_list
	-- 页签切换和天神切换的情况下需要跳转活刷新
	if self.ul_jump_remind or not is_same_ts then
		for i = 0, #slot_list do
			if slot_list[i].is_remind then
				jump_slot = i
				break
			end
		end
	end
	if not jump_slot then
		jump_slot = self.ul_select_slot_index or 0
	end
	if self.ul_select_slot_index ~= jump_slot then
		self.lh_slot_list[jump_slot]:OnClick()
	else
		self.ul_select_slot_index = jump_slot
		self.ul_select_slot_data = slot_list[jump_slot]
		self.ul_select_seq = self.ul_select_ts_index * 10 + self.ul_select_slot_index
		self:FlushULSlotCtrlPart()
	end
	self.ul_jump_remind = nil
end

-- 槽位选择回调
function TianShenView:OnClickULSlotCallBack(item)
	-- print_error("---- 槽位选择回调 ----")
	if nil == item or nil == item.data then
		return
	end

	local data = item.data
	self.ul_select_slot_index = data.slot
	self.ul_select_slot_data = data

	local select_seq = self.ul_select_ts_index * 10 + self.ul_select_slot_index
	if self.ul_select_seq ~= select_seq then
		-- 选中刷新
		for k, v in pairs(self.lh_slot_list) do
			if v.data then
				v:OnSelectChange(self.ul_select_slot_index == v.data.slot)
			end
		end
		self.ul_select_seq = select_seq
		self:FlushULSlotCtrlPart()
	else
		self:OnClickULChangeBtn()
		--[[ 合成界面合并，只需打开背包
		if data.item_id ~= 0 then
			local target_id = data.item_id + 1
			if TianShenLingHeWGData.Instance:GetHaveBetterLingHe(self.ul_select_ts_index, self.ul_select_slot_index) then
				self:OnClickULChangeBtn()
			else
				local cfg = TianShenLingHeWGData.Instance:GetItemComposeCfg(target_id)
				if cfg then -- 存在下一级合成
					TianShenLingHeWGCtrl.Instance:SetShenShiComposeData(target_id)
					TianShenLingHeWGCtrl.Instance:OpenShenShiComposeView()
				else
					self:OnClickULChangeBtn()
				end
			end
		else
			self:OnClickULChangeBtn()
		end
		]]
	end
end

--[[ 刷新
function TianShenView:SelectFlushULView(force_jump_ts_index)
    local data_list = TianShenLingHeWGData.Instance:GetUpLevelShowList()
    local jump_ts_index
    if self.ul_jump_remind or not self.ul_list_view:GetDataList() then
        for k,v in ipairs(data_list) do
            if force_jump_ts_index then
                if v.ts_index == force_jump_ts_index then
                    jump_ts_index = k
                    break
                end
            elseif v.is_remind then
                jump_ts_index = k
                break
            end
        end

        jump_ts_index = jump_ts_index or 1
    end

    self.ul_list_view:SetDataList(data_list)
    if jump_ts_index then
        self.ul_list_view:JumpToIndex(jump_ts_index)
    else
        -- 刷新
        local new_data
        for k,v in ipairs(data_list) do
            if v.ts_index == self.ul_select_ts_index then
                new_data = v
                break
            end
        end

        self.ul_select_ts_data = new_data
        self:FlushULTianShenCtrlPart(true)
        self:FlushULSlotCtrlPart()
    end
end
]]

function TianShenView:FlushULModelDisPlay()
	local data = self.ul_select_ts_data
	if data then
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(data.ts_index)
		local audio = tianshen_cfg.show_audio
		-- 添加化魔展示
		local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(data.ts_index) or tianshen_cfg.appe_image_id
		if self.show_effect_loader then
			self.show_effect_loader:Destroy()
			self.show_effect_loader = nil
		end

		self.ul_role_model:SetTianShenModel(appe_image_id, data.ts_index, true, audio, SceneObjAnimator.Rest)
	end
end

-- 刷新天神选择影响部分
function TianShenView:FlushULTianShenCtrlPart(no_jump_flush)
	if self.ul_select_ts_data then
		-- 槽位
		local slot_list = self.ul_select_ts_data.slot_list
		local no_jump_flush_data
		for k, v in pairs(self.lh_slot_list) do
			v:SetData(slot_list[k])
			if slot_list[k].slot == self.ul_select_slot_index then
				no_jump_flush_data = slot_list[k]
			end
		end

		if no_jump_flush then
			self.ul_select_slot_data = no_jump_flush_data
		end

		-- 战力
		self.node_list["ul_cap_value"].text.text = TianShenLingHeWGData.Instance:GetULCapability(self.ul_select_ts_index)
	end
end

-- 刷新槽位选择影响部分
function TianShenView:FlushULSlotCtrlPart()
	local data = self.ul_select_slot_data
	if data == nil then
		return
	end

	local slot = data.slot -- id
	local level = data.level

	local has_equip = data.item_id > 0
	data.default_show_item_id = TianShenLingHeWGData.Instance:GetLingHeDefaultShowItem(slot) or 0
	data.is_need_gray = not has_equip
	self.ul_show_linghe_item:SetData(data)
	local show_item_id = has_equip and data.item_id or data.default_show_item_id
	self.node_list["ul_name"].text.text = ItemWGData.Instance:GetItemName(show_item_id)
	self.node_list["ul_level"].text.text = has_equip and string.format(Language.TianShenLingHe.Level, level) or Language.TianShenLingHe.ULNoEquip
	if has_equip then
		local score = TianShenLingHeWGData.Instance:GetShenShiScore(self.ul_select_ts_index, data.slot)
		self.node_list["lbl_ss_score"].text.text = string.format(Language.TianShenLingHe.ShenShiScore, score)
	else
		self.node_list.tog_sz_upgrade.toggle.isOn = true
		self.node_list.tog_sz_compose.toggle.isOn = false
		self.node_list["lbl_ss_score"].text.text = Language.TianShenLingHe.LingHeNoEquip
	end

	-- icon
	-- self.node_list["img_ul_icon"]:SetActive(has_equip)
	-- if has_equip then
	-- 	local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(data.item_id, true)
	-- 	self.node_list["img_ul_icon"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["img_ul_icon"].raw_image:SetNativeSize()
	-- 	end)
	-- end

	self.node_list["btn_compose_block"]:SetActive(not has_equip);

	-- 消耗
	local level_cfg = TianShenLingHeWGData.Instance:GetSlotUplevelCfg(slot, level + 1)
	if level_cfg then
		self.ul_cost_item:SetData({ item_id = level_cfg.stuff_id })
		local had_num = TianShenLingHeWGData.Instance:GetItemNum(level_cfg.stuff_id)
		local need_num = level_cfg.stuff_num
		local num_color = had_num >= need_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list["ul_cost_desc"].text.text = string.format("%s/%s", ToColorStr(had_num, num_color), need_num)
		self.node_list["ul_btn_uplevel_text"].text.text = Language.TianShenLingHe.UpLevel
		XUI.SetButtonEnabled(self.node_list["ul_btn_uplevel"], true)
	else
		self.ul_cost_item:ClearData()
		self.ul_cost_item:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
		self.ul_cost_item:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
		self.node_list["ul_cost_desc"].text.text = "--/--"
		self.node_list["ul_btn_uplevel_text"].text.text = Language.TianShenLingHe.MaxLevel
		XUI.SetButtonEnabled(self.node_list["ul_btn_uplevel"], false)
	end

	-- 属性
	local attr_list = TianShenLingHeWGData.Instance:GetUpLevelShowAttr(slot, level, data.item_id)

	local need_show_effect = false
	if nil ~= self.linghe_slot_cache and nil ~= self.linghe_slot_level_cache then
		if (self.linghe_slot_cache == slot) and ((level - self.linghe_slot_level_cache == 1)) then
			need_show_effect = true
		end
	end

	self.linghe_slot_cache = slot
	self.linghe_slot_level_cache = level

	for k, v in ipairs(self.ul_attr_list) do
		v:SetData(attr_list[k])
		v:SetRealHideNext(level_cfg == nil)

		if need_show_effect then
			v:PlayAttrValueUpEffect()
		end
	end

	-- 按钮
	local best_bag_list = TianShenLingHeWGData.Instance:GetBestBagList()
	self.node_list["ul_btn_change_text"].text.text = IsEmptyTable(best_bag_list) and Language.TianShenLingHe.LingHeGetBtnTip or Language.TianShenLingHe.Inlay

	-- 红点
	local change_remind = TianShenLingHeWGData.Instance:GetHaveBetterLingHe(self.ul_select_ts_index, slot)
	self.node_list["ul_btn_change_remind"]:SetActive(change_remind)
	local uplevel_remind = TianShenLingHeWGData.Instance:GetUpLevelRemind(self.ul_select_ts_index, slot)
	self.node_list["ul_btn_uplevel_remind"]:SetActive(uplevel_remind)
	self.node_list["sz_tog_uplevel_remind"]:SetActive(change_remind or uplevel_remind)
	local compose_remind = TianShenLingHeWGData.Instance:GetSlotComposeRemind(self.ul_select_ts_index, slot)
	self.node_list["compose_btn_remind"]:SetActive(compose_remind) 
	self.node_list["sz_tog_compose_remind"]:SetActive(compose_remind) 

	self:FlushULComposePart()
end

function TianShenView:FlushULComposePart()
	local data = self.ul_select_slot_data
	if data == nil then
		return
	end
	self.compose_left_cell:SetData({item_id = data.item_id})

	local compose_item_cfg = TianShenLingHeWGData.Instance:GetItemComposeCfgByStuff(data.item_id)
	self.node_list.compose_node_effect_root:CustomSetActive(compose_item_cfg ~= nil)
	-- 消耗
	if compose_item_cfg then
		self.compose_right_cell:SetData({item_id = compose_item_cfg.product_id})
		self.compose_cost_item:SetData({item_id = compose_item_cfg.stuff_id})
		local had_num = TianShenLingHeWGData.Instance:GetItemNum(compose_item_cfg.stuff_id) + 1
		local need_num = compose_item_cfg.stuff_count
		local num_color = had_num >= need_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list["compose_cost_desc"].text.text = string.format("%s/%s", ToColorStr(had_num, num_color), need_num)
		self.node_list["compose_btn_text"].text.text = Language.TianShenLingHe.BtnComposeText
		XUI.SetButtonEnabled(self.node_list["btn_compose"], true)
	else
		self.compose_right_cell:ClearData()
		self.compose_cost_item:ClearData()
		self.compose_cost_item:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
		self.compose_cost_item:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
		self.node_list["compose_cost_desc"].text.text = "--/--"
		self.node_list["compose_btn_text"].text.text = Language.TianShenLingHe.MaxLevel
		XUI.SetButtonEnabled(self.node_list["btn_compose"], false)
	end
	
	-- 属性
	local attr_list = TianShenLingHeWGData.Instance:GetComposeShowAttr(data.item_id)
	for k, v in ipairs(self.compose_attr_list) do
		v:SetData(attr_list[k])
		v:SetRealHideNext(compose_item_cfg == nil)

		if self.need_show_compose_attr_effect then
			v:PlayAttrValueUpEffect()
		end
	end
	self.need_show_compose_attr_effect = false
end

-- 替换
function TianShenView:OnClickULChangeBtn()
	if not self.ul_select_slot_data then
		return
	end

	local have_list = TianShenLingHeWGData.Instance:GetSlotBagList(self.ul_select_slot_index)
	TianShenLingHeWGCtrl.Instance:OpenLingHeBagView(have_list, self.ul_select_ts_index, self.ul_select_slot_index)
end

-- 一键镶嵌
function TianShenView:OnClickWearAll()
	if not self.ul_select_slot_data then
		return
	end

	local best_bag_list = TianShenLingHeWGData.Instance:GetBestBagList()
	if IsEmptyTable(best_bag_list) then
		ViewManager.Instance:Open(GuideModuleName.TianShenLingHeDrawView)
		return
	end

	local slot_list = self.ul_select_ts_data.slot_list
	local best_item_list = {}
	for i = 0, TianShenLingHeWGData.MAX_SLOT - 1 do
		local slot_data = slot_list[i]
		local slot_data_color = slot_data and slot_data.color or -1
		local best_bag_slot_data = best_bag_list[i]
		if best_bag_slot_data and best_bag_slot_data.color > slot_data_color then
			best_item_list[i] = best_bag_slot_data.item_id
		else
			best_item_list[i] = -1
		end
	end

	if not IsEmptyTable(best_item_list) then
		TianShenLingHeWGCtrl.Instance:SendLingHeOneClick(self.ul_select_ts_index, best_item_list)
	end
end

-- 升级
function TianShenView:OnClickUpLevelBtn()
	local data = self.ul_select_slot_data
	if data then
		local slot = data.slot
		local level = data.level
		local level_cfg = TianShenLingHeWGData.Instance:GetSlotUplevelCfg(slot, level + 1)
		if level_cfg then
			local had_num = TianShenLingHeWGData.Instance:GetItemNum(level_cfg.stuff_id)
			local need_num = level_cfg.stuff_num
			if had_num >= need_num and data.item_id > 0 then
				self:ShowUpLevelSucessEffect()
			end
		end
	end

	TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.UPLEVEL, self.ul_select_ts_index, self.ul_select_slot_index)
end

function TianShenView:ShowUpLevelSucessEffect()
	if self.node_list["ul_uplevel_success_root"] then
		TipWGCtrl.Instance:ShowEffect({
			effect_type = UIEffectName.s_shengji,
			is_success = true,
			pos = Vector2(0, 0),
			parent_node = self.node_list["ul_uplevel_success_root"]
		})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

function TianShenView:OnClickResoveBtn()
	TianShenLingHeWGCtrl.Instance:OpenShenShiResoveView()
end

function TianShenView:OnClickDrawBtn()
	ViewManager.Instance:Open(GuideModuleName.TianShenLingHeDrawView)
end

function TianShenView:OnClickComposeClockBtn()
	TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenLingHe.LingHeEquipTip)
end

-- 点击合成
function TianShenView:OnClickComposeBtn()
	local data = self.ul_select_slot_data
	if data == nil then return end

	local compose_item_cfg = TianShenLingHeWGData.Instance:GetItemComposeCfgByStuff(data.item_id)
	if IsEmptyTable(compose_item_cfg) then return end

	local is_enough, cost_list = TianShenLingHeWGData.Instance:GetItemComposeCostList(compose_item_cfg.product_id)
    if is_enough then
		if not self.compose_alert_view then
			self.compose_alert_view = Alert.New(nil, nil, nil, nil, true)
			self.compose_alert_view:SetShowCheckBox(true, "linghe_compose")
			self.compose_alert_view:SetCheckBoxDefaultSelect(false)
			self.compose_alert_view:SetCheckBoxText(Language.TreasureHunt.NoRemind)
		end
		local str = ""
		for i, v in ipairs(cost_list) do
			local item_name = ItemWGData.Instance:GetItemName(v.item_id, nil, true)
			str = str .. item_name  .. "*" .. v.num
			if i ~= #cost_list then
				str = str .. Language.TianShenLingHe.LingHeAnd
			end
		end

		self.compose_alert_view:SetLableString(string.format(Language.TianShenLingHe.LingHeComposeTip, str))
		self.compose_alert_view:SetOkFunc(BindTool.Bind1(self.UlSendCompose, self))
		self.compose_alert_view:Open()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenLingHe.LingHeComposeNotEnough)
    end
end

function TianShenView:UlSendCompose()
	self.need_show_compose_attr_effect = true
	self:ULShowComposeSucessEffect()
	TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.COMPOSE, self.ul_select_ts_index, self.ul_select_slot_index)
end

-- 合成成功特效
function TianShenView:ULShowComposeSucessEffect()
	if self.node_list["ul_cp_success_root"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true,
                                        pos = Vector2(0, 0), parent_node = self.node_list["ul_cp_success_root"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

---------------------------------------- 
-- TianShenSSItemRender 天神列表
----------------------------------------
TianShenSSItemRender = TianShenSSItemRender or BaseClass(BaseRender)

function TianShenSSItemRender:OnFlush()
	if self.data == nil then return end
	self.node_list.remind:SetActive(self.data.is_remind)
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.ts_index)
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.ts_index)
	self.node_list.battle_flag:CustomSetActive(tianshen_info.zhan_index ~= -1)

	local color = self.data.series + 2
	self.node_list.tianshen_name.text.text = ToColorStr(tianshen_cfg.bianshen_name, ITEM_COLOR[color])
	self.node_list.tianshen_lv.text.text = tianshen_info.level

	local bundle, asset = ResPath.GetTianShenNopackImg("ts_ring_" .. self.data.ts_index)
    self.node_list.tianshen_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.tianshen_skill_icon.image:SetNativeSize()
    end)

	local bundle, asset = ResPath.GetF2TianShenImage(string.format("a3_ts_small_yq0%d", color))
	self.node_list.img_bg.image:LoadSprite(bundle, asset)

	local star_res_list = GetStarImgResByStar(tianshen_info.star)
	for i = 1, 5 do
		self.node_list[string.format("star%d", i)].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
	end
end

function TianShenSSItemRender:OnSelectChange(is_select)
	self.node_list["tianshen_select"]:SetActive(is_select)
end

----------------------------------------------------------
-- 槽位格子
----------------------------------------------------------
LingHeULSlotRender = LingHeULSlotRender or BaseClass(BaseRender)
function LingHeULSlotRender:__init()
	self.ling_cell = BaseLingHeCell.New(nil, self.node_list["linghe_node"])
	self.ling_cell:SetIsShowTips(false)
	self.ling_cell:SetUseButton(false)
	-- self.ling_cell:SetQualityBgShow(false, false)

	--[[
	if not self.arrow_tweener then
		self.arrow = self.node_list["remind"]
		self.arrow_tweener = self.arrow.gameObject.transform:DOAnchorPosY(-5, 0.45)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end]]
end

function LingHeULSlotRender:__delete()
	if self.ling_cell then
		self.ling_cell:DeleteMe()
	end

	--[[
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end]]
end

function LingHeULSlotRender:OnFlush()
	if self.data == nil then
		return
	end

	self.ling_cell:SetData({ item_id = self.data.item_id })
	self.node_list["remind"]:SetActive(self.data.is_remind)
	self.node_list["add_flag"]:SetActive(self.data.item_id <= 0)
	local level_str = self.data.item_id > 0 and string.format(Language.TianShenLingHe.Level, self.data.level) or ""
	self.node_list["txt_linghe_level"].text.text = level_str
end

function LingHeULSlotRender:OnSelectChange(is_select)
	self.node_list["select_hl"]:SetActive(is_select)
end

--[[
----------------------------------------------------------
-- 列表格子 [Obsolete]
----------------------------------------------------------
LingHeULListRender = LingHeULListRender or BaseClass(BaseRender)
function LingHeULListRender:__init()

end

function LingHeULListRender:__delete()

end

function LingHeULListRender:OnFlush()
	if self.data == nil then
		return
	end

	local bundle, asset = ResPath.GetNoPackPNG("ts_" .. self.data.ts_index)
	self.node_list.item_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.item_icon.image:SetNativeSize()
	end)

	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.ts_index)
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.ts_index)
	self.node_list.lbl_ml_item_name.text.text = tianshen_cfg.bianshen_name
	self.node_list.level_txt.text.text = tianshen_info.level
	local active = tianshen_info and tianshen_info.zhan_index >= 0 or false
	self.node_list.img_battle:SetActive(active)
	bundle, asset = ResPath.GetF2TianShenImage(TianShenWGData.TianShenQualityImg[tianshen_cfg.series])
	self.node_list.img_quality.image:LoadSprite(bundle, (asset .. ".png"))
	self.node_list["remind"]:SetActive(self.data.is_remind)

	-- 特殊外框
	self.node_list.special_bg:SetActive(tianshen_cfg.show_outline_img ~= "")
	if tianshen_cfg.show_outline_img and tianshen_cfg.show_outline_img ~= "" then
		bundle, asset = ResPath.GetF2TianShenImage(tianshen_cfg.show_outline_img)
		self.node_list.special_bg.image:LoadSprite(bundle, asset, function()
			self.node_list.special_bg.image:SetNativeSize()
		end)
	end
	-- local color_list = TianShenLingHeWGData.Instance:GetSlotColorListData(self.data.ts_index)
	-- for i = 0, TianShenLingHeWGData.MAX_SLOT - 1 do
	--     if color_list[i + 1] then
	--         self.node_list["icon_" .. i].image:LoadSpriteAsync(ResPath.GetTianShenLingHeImage("a1_lh_" .. color_list[i + 1]))
	--     else
	--         self.node_list["icon_" .. i].image.enabled = false
	--     end
	-- end
end

function LingHeULListRender:OnSelectChange(is_select)
	self.node_list["select_bg"]:SetActive(is_select)
end
]]