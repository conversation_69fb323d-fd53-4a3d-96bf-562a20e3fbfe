CrossTreasureResultLoserView = CrossTreasureResultLoserView or BaseClass(SafeBaseView)
function CrossTreasureResultLoserView:__init()
	self:AddViewResource(0, "uis/view/cross_treasure_ui_prefab", "cross_treasure_result_loser")
	self:SetMaskBg(true)
end

function CrossTreasureResultLoserView:LoadCallBack()
	if not self.show_reward_list then
		self.show_reward_list = AsyncListView.New(CrossTreasureResultRender, self.node_list.show_reward_list)
		self.show_reward_list:SetStartZeroIndex(true)
	end
end

function CrossTreasureResultLoserView:ReleaseCallBack()
	if self.show_reward_list then
		self.show_reward_list:DeleteMe()
		self.show_reward_list = nil
	end
end

function CrossTreasureResultLoserView:SetShowData(show_data)
	self.show_data = show_data
end

function CrossTreasureResultLoserView:OnFlush()
	if not self.show_data then
		return
	end

	local cfg = CrossTreasureWGData.Instance:GetBeastPoolCfgByIdSeq(self.show_data.pool_id, self.show_data.seq)
	local str = ""

	if cfg then
		local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(cfg.beast_id)
		if beast_cfg then
			str = string.format(Language.CrossTreasure.BeastGatherResultLoser, beast_cfg.beast_name)
		end
	end

	self.node_list.loser_tips.text.text = str
	self.show_reward_list:SetDataList(cfg.fail_reward_item)
end