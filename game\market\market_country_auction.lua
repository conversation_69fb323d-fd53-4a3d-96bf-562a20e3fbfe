
-- 市场-拍卖-国家拍卖
local COUNTRY_AUCTION_TOGGLE_MAX = 1

local time_key = "market_country_auction_flush"


function MarketView:CountryAuctionLoadCallBack()
	self.cur_ca_data_list = {}--当前展示信息
	self.old_ca_show_info = nil--用作保存上一次刷新数据的位置信息
	self.ca_search_name = ""

	self.node_list.all_select_btn.button:AddClickListener(BindTool.Bind(self.OnClickAllTypeBtn, self))
	self.node_list.gj_search_btn.button:AddClickListener(BindTool.Bind(self.CAOnClickSearchBtn, self))
	self.node_list.gj_search_input.input_field.onSelect:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, true, MarketViewIndex.GJPM))
	self.node_list.gj_search_input.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, false, MarketViewIndex.GJPM))

	--tab页签的箭头显示.
	self.node_list.market_country_auction_list.scroller.scrollerEndScrolled = BindTool.Bind(self.CountryAictionScrollerEndScrolled, self)

	self.country_aiction_item_list = AsyncListView.New(MarketAuctionCommonRender, self.node_list.gj_item_list)

	self.country_aiction_tab_list = AsyncListView.New(MarketAuctionTabRender, self.node_list["market_country_auction_list"])
	self.country_aiction_tab_list:SetSelectCallBack(BindTool.Bind1(self.OnClickCountryAcutionTabCallBack, self))
	self.country_aiction_tab_list:SetDefaultSelectIndex(nil)
	self.country_aiction_tab_list:SetDataList(MarketWGData.Instance:GetAuctionTabList(AUCTION_INFO_BIG_TYPE.Country))

	self:OnClickAllTypeBtn()

	self.node_list.gj_buttom_tips.text.text = Language.Market.Auction_Buttom_Tips_1
end

function MarketView:CountryAuctionReleaseCallBack()
	self:CleanCountryTimer()
	if self.country_aiction_item_list then
		self.country_aiction_item_list:DeleteMe()
		self.country_aiction_item_list = nil
	end

	self.cur_ca_data_list = nil
	self.ca_search_name = nil
	self.old_ca_show_info = nil

	self.cur_big_type = nil
	self.cur_sub_type = nil

	if self.country_aiction_tab_list ~= nil then
		self.country_aiction_tab_list:DeleteMe()
		self.country_aiction_tab_list = nil
	end
end

function MarketView:CountryAuctionShowIndexCallBack()

end

--tab页签的箭头显示.
function MarketView:CountryAictionScrollerEndScrolled()
	local val = self.node_list.market_country_auction_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.market_country_auction_l_img:SetActive(val ~= 0 and val > 0.1)
	self.node_list.market_country_auction_r_img:SetActive(val ~= 0 and val < 0.9)
end

--设置首次打开的跳转
function MarketView:CountryAuctionJumpIndexShow(big_type, sub_type)
	self.cur_big_type = big_type
	self.cur_sub_type = sub_type
end

function MarketView:CountryAuctionOnFlush(param_t)
	if self.cur_big_type and self.cur_sub_type then
		self:OnClickLeftBtnCallBack(self.cur_big_type, self.cur_sub_type, true)
	else
		self:OnClickAllTypeBtn(true)
	end
end

function MarketView:FlushCountryAuctionAllPart()

end

function MarketView:OnClickCountryAcutionTabCallBack(item)
	if IsEmptyTable(item.data) then
		return
	end

	self.node_list.all_hl:SetActive(false)
	self:OnClickLeftBtnCallBack(item.data.type, item.data.sub_type)
end

function MarketView:OnClickLeftBtnCallBack(big_type, sub_type, force_flush)
	if self.ca_search_name == "" and not force_flush and self.cur_big_type and self.cur_sub_type then
		if self.cur_big_type == big_type and self.cur_sub_type == sub_type then
			return
		end
	else
		self.cur_big_type = big_type
		self.cur_sub_type = sub_type
	end
	self.cur_big_type = big_type
	self.cur_sub_type = sub_type

	local show_acution_list_type = AUCTION_TYPE.Country
	if big_type == 2 then
		show_acution_list_type = AUCTION_TYPE.System
	end

	--根据类型取得竞拍物品展示配置
	self.cur_ca_data_list = MarketWGData.Instance:GetNewAuctionItemInfoByType(show_acution_list_type, sub_type)
	if not force_flush then-- 普通点击页签,清空搜索状态以及搜索栏
		self.node_list["gj_search_input"].input_field.text = ""
		self.ca_search_name = ""
	end
	if self.ca_search_name ~= "" then-- 还在搜索列表中
		self:CAOnClickSearchBtn(true)
	else
		self:CAOnFlushGoodsList(force_flush)
	end
end

--点击全部按钮
function MarketView:OnClickAllTypeBtn(force_flush)
	if self.ca_search_name == "" and not force_flush and self.node_list.all_hl.gameObject.activeSelf then
		return
	end
	self.cur_big_type = nil
	self.cur_sub_type = nil

	self.node_list.all_hl:SetActive(true)
	self.cur_ca_data_list = MarketWGData.Instance:GetAllAuctionByType(AUCTION_TYPE.Country)

	if not force_flush then-- 普通点击页签,清空搜索状态以及搜索栏
		self.node_list["gj_search_input"].input_field.text = ""
		self.ca_search_name = ""
	end

	if self.ca_search_name ~= "" then-- 还在搜索列表中
		self:CAOnClickSearchBtn(true)
	else
		self:CAOnFlushGoodsList(force_flush)
	end

	self.country_aiction_tab_list:CancelSelect()
end

--刷新国家拍卖商品列表
--specil_flag 特殊需求:不是点击页签刷新时,列表数据有变化,按照原来的列表顺序刷新
function MarketView:CAOnFlushGoodsList(specil_flag)
	self:CleanCountryTimer()
	self.node_list.gj_empty_tips:SetActive(false)
	self.node_list.gj_item_list:SetActive(true)

	-- 筛选，在拍卖时间内的才显示
	local temp_list 
	local next_time = 0
	if self.cur_ca_data_list and not IsEmptyTable(self.cur_ca_data_list) then
		temp_list, next_time = MarketWGData.Instance:FiterAuction(self.cur_ca_data_list)
		self.cur_ca_data_list = temp_list
	end

	if self.cur_ca_data_list and not IsEmptyTable(self.cur_ca_data_list) then
		if specil_flag and self.old_ca_show_info and not IsEmptyTable(self.old_ca_show_info) then
			self.cur_ca_data_list = MarketWGData.Instance:SortAuctionInfoListByOld(self.cur_ca_data_list, self.old_ca_show_info)
		else--点击页签切换时,才按时间重新排序
			-- table.sort(self.cur_ca_data_list, SortTools.KeyLowerSorters("end_time", "index"))
			table.sort(self.cur_ca_data_list, MarketWGData.Instance:AuctionDataListSort())
		end
		self.old_ca_show_info = self.cur_ca_data_list



		self.country_aiction_item_list:SetDataList(self.cur_ca_data_list)
		if not specil_flag then
			self.country_aiction_item_list:JumpToTop()
		end
	else
		self.node_list.gj_empty_tips:SetActive(true)
		self.node_list.gj_item_list:SetActive(false)
	end

	if next_time ~= 0 then

	end

	if next_time > 0 then
        local complete_time = next_time -- 完成的时间戳（与 total_time 选一即可）
        CountDownManager.Instance:AddCountDown(time_key,
            nil,
            -- 倒计时完成回调方法
            function()
				self:CAOnFlushGoodsList()
            end,
			complete_time)
    end
end

-- 清除倒计时器
function MarketView:CleanCountryTimer()
	if CountDownManager.Instance:HasCountDown(time_key) then
		CountDownManager.Instance:RemoveCountDown(time_key)
	end
end

--国家拍卖--点击搜索按钮
--flush_flag:刷新标志,用于搜索后列表信息变更刷新
function MarketView:CAOnClickSearchBtn(flush_flag)
	if not self.cur_ca_data_list and IsEmptyTable(self.cur_ca_data_list) then
		return
	end

	if not flush_flag then
		local new_search_name = self.node_list["gj_search_input"].input_field.text
		if self.ca_search_name == new_search_name then
			return
		else--二次搜索:根据当前所在的 左页签类型 进行搜索
			if self.cur_big_type and self.cur_sub_type then
				local show_acution_list_type = AUCTION_TYPE.Country
				if self.cur_big_type == 2 then
					show_acution_list_type = AUCTION_TYPE.System
				end
				--根据类型取得竞拍物品展示配置
				self.cur_ca_data_list = MarketWGData.Instance:GetNewAuctionItemInfoByType(show_acution_list_type, self.cur_sub_type)
			else
				self.cur_ca_data_list = MarketWGData.Instance:GetAllAuctionByType(AUCTION_TYPE.Country)
			end
		end
		self.ca_search_name = new_search_name
		if self.ca_search_name == "" then
			self:CountryAuctionOnFlush()
			return
		end
	end

	local temp_list = {}
	for k, v in pairs(self.cur_ca_data_list) do
		if self:CASearchGoodsInfo(v.item_id) then
			table.insert(temp_list, v)
		end
	end
	self.cur_ca_data_list = temp_list
	self:CAOnFlushGoodsList()
end

--搜索商品信息
function MarketView:CASearchGoodsInfo(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then return false end

	if self.ca_search_name and self.ca_search_name ~= "" then
		if not string.find(item_cfg.name, self.ca_search_name) then
			return false
		end
	end
	return true
end
