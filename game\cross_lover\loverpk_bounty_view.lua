LoverPkBountyView = LoverPkBountyView or BaseClass(SafeBaseView)

function LoverPkBountyView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_flowers_bounty")
end

function LoverPkBountyView:OpenCallBack()
    LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.SEND_FLOWERS_COUNT)
end

function LoverPkBountyView:LoadCallBack()
    if not self.now_item_list then
        self.now_item_list = AsyncListView.New(ItemCell, self.node_list.now_item_list)
        self.now_item_list:SetStartZeroIndex(true)
    end

    if not self.next_item_list then
        self.next_item_list = AsyncListView.New(ItemCell, self.node_list.next_item_list)
        self.next_item_list:SetStartZeroIndex(true)
    end

    self.node_list.desc_tip.text.text = Language.LoverPK.FlowerBountyTip
    XUI.AddClickEventListener(self.node_list["seed_flower"], BindTool.Bind(self.ClickSendFlowerBtn,self))
end

function LoverPkBountyView:ReleaseCallBack()
    if self.now_item_list then
        self.now_item_list:DeleteMe()
        self.now_item_list = nil
    end

    if self.next_item_list then
        self.next_item_list:DeleteMe()
        self.next_item_list = nil
    end
end

function LoverPkBountyView:OnFlush()
    local total_flower_count, cur_reward_cfg, next_reward_cfg = LoverPkWGData.Instance:GetBountyDataInfo()
    local is_max_stage = IsEmptyTable(next_reward_cfg)
    self.node_list.flower_num.text.text = total_flower_count
    self.node_list.img_line:CustomSetActive(not is_max_stage)
    self.node_list.now_item_list:CustomSetActive(not is_max_stage)
    self.node_list.now_bounty:CustomSetActive(not is_max_stage)
    self.next_item_list:SetDataList(is_max_stage and cur_reward_cfg.reward_item or next_reward_cfg.reward_item)
    self.node_list.need_num.text.text = is_max_stage and cur_reward_cfg.flowers_count_begin or next_reward_cfg.flowers_count_begin

    if not is_max_stage then
        self.now_item_list:SetDataList(cur_reward_cfg.reward_item)
    end
end

function LoverPkBountyView:ClickSendFlowerBtn()
    LoverPkWGCtrl.Instance:OpenLoverPKBlessingView()
end