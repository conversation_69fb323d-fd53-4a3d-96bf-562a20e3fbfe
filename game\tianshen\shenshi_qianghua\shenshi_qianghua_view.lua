-- 天神神饰强化主面板（强化、继承、套装）
ShenShiQiangHuaView = ShenShiQiangHuaView or BaseClass(SafeBaseView)
function ShenShiQiangHuaView:__init()
	
	self.view_name = GuideModuleName.ShenShiQiangHuaView
	self.default_index = TabIndex.shenshi_qianghua
	self:SetMaskBg(true, true)


	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(TabIndex.shenshi_qianghua, "uis/view/tianshen/shenshi_qianghua_prefab",
		"layout_tianshen_qianghua")
	self:AddViewResource(TabIndex.shenshi_jicheng, "uis/view/tianshen/shenshi_qianghua_prefab", "layout_tianshen_jicheng")
	self:AddViewResource(TabIndex.shenshi_taozhuang, "uis/view/tianshen/shenshi_qianghua_prefab",
		"layout_tianshen_taozhuang")
	self:AddViewResource(0, "uis/view/tianshen/shenshi_qianghua_prefab", "HorizontalTabbar")
end

function ShenShiQiangHuaView:__delete()

end

function ShenShiQiangHuaView:SetData(tianshen_index)
	if not tianshen_index then
		print_error("传入参数为空！ tianshen_index: ", tianshen_index)
		tianshen_index = 1
	end
	self.select_tainshen_cfg = TianShenWGData.Instance:GetTianShenCfg(tianshen_index)
end

function ShenShiQiangHuaView:OpenCallBack()
	if self.tabbar then
		self.tabbar:SetToggleVisible(TabIndex.shenshi_qianghua, TianShenWGData.Instance:HasShenShiEquip())
	end
	self:JiChengOpenCallBack()
end

function ShenShiQiangHuaView:CloseCallBack()
	if self:IsLoadedIndex(TabIndex.shenshi_jicheng) then
		-- self:JiChengCloseCallBack()
	end
end

function ShenShiQiangHuaView:LoadCallBack()
	self:SetSecondView(Vector2(1278, 718), nil, Vector2(-28, -4))

	self:InitTabbar()
	self.tabbar:SetToggleVisible(TabIndex.shenshi_qianghua, TianShenWGData.Instance:HasShenShiEquip())
end

function ShenShiQiangHuaView:InitTabbar()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(nil, Language.TianShen.ShenShiQiangHuaTabGrop, nil, "uis/view/tianshen/shenshi_qianghua_prefab")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.FlushTabbarRemind, self))
	end
end

function ShenShiQiangHuaView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	self:QiangHuaReleaseCallBack()
	self:JiChengReleaseCallBack()
	self:TaoZhuangReleaseCallBack()
end

function ShenShiQiangHuaView:LoadIndexCallBack(index)
	if index == TabIndex.shenshi_qianghua then   -- 神饰强化-强化
		self:QiangHuaLoadCallBack()
	elseif index == TabIndex.shenshi_jicheng then -- 神饰强化-继承
		self:JiChengLoadCallBack()
	elseif index == TabIndex.shenshi_taozhuang then -- 神饰强化-套装
		self:TaoZhuangLoadCallBack()
	end
end

function ShenShiQiangHuaView:ShowIndexCallBack(index)
	if index == TabIndex.shenshi_qianghua then -- 神饰强化-强化
		self:QiangHuaShowIndexCallBack()
		self.node_list.title_view_name.text.text = Language.TianShen.ShenShiQiangHuaTitle
	elseif index == TabIndex.shenshi_jicheng then -- 神饰强化-继承
		self.node_list.title_view_name.text.text = Language.TianShen.ShenShiJiChengTitle
	elseif index == TabIndex.shenshi_taozhuang then -- 神饰强化-套装
		self.node_list.title_view_name.text.text = Language.TianShen.ShenShiTaoZhuangTitle
	end
end

function ShenShiQiangHuaView:OnFlush(param_list, index)
	if index == TabIndex.shenshi_qianghua then   -- 神饰强化-强化
		self:QiangHuaOnFlush(param_list)
	elseif index == TabIndex.shenshi_jicheng then -- 神饰强化-继承
		self:JiChengOnFlush(param_list)
	elseif index == TabIndex.shenshi_taozhuang then -- 神饰强化-套装
		self:TaoZhuangOnFlush(param_list)
	end
	self:FlushTabbarRemind()
end

function ShenShiQiangHuaView:FlushTabbarRemind()
	local ver_cell_list = self.tabbar:GetVerCellList()
	local qianghua_ver_cell = ver_cell_list[math.floor(TabIndex.shenshi_qianghua / 10)]
	if qianghua_ver_cell then
		qianghua_ver_cell:ShowRemind(TianShenWGData.Instance:CheckUpgrade(self.select_tainshen_cfg.index) > 0)
	end

	local taozhuang_ver_cell = ver_cell_list[math.floor(TabIndex.shenshi_taozhuang / 10)]
	if taozhuang_ver_cell then
		taozhuang_ver_cell:ShowRemind(TianShenWGData.Instance:CheckTaoZhuang(self.select_tainshen_cfg.index) > 0)
	end
end
