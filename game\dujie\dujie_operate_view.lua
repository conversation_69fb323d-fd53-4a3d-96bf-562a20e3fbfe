DujieOperateView = DujieOperateView or BaseClass(SafeBaseView)

local WARNING_MAX  = 2
local THUNDER_MAX  = 3
local HIT_MAX  = 2
local SKILL_MAX  = 3

local THUNDER_WARNING_TIME = 1.2

local CG_STAGE_TYPE = 
{
    BEGIN = 1,
    ING = 2,
    END = 3,
}

function DujieOperateView:__init()
    self.view_style = ViewStyle.Window
    self.active_close = false
	self:SetMaskBg(false, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_operate_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

    self.is_cg_loaded = false

end

function DujieOperateView:OpenCallBack()
    ViewManager.Instance:AddCanInactiveView(GuideModuleName.MainUIView)
    SafeBaseView.SetOnlyView(GuideModuleName.DujieOperateView)
    DujieWGCtrl.Instance:StopBgMusic()
end

function DujieOperateView:LoadCallBack()
    self.skill_cd_timer_list = {}
    self.show_skill_list = {}
    for i = 1, 3 do
        self.show_skill_list[i] = SkillShowSkillRender.New(self.node_list["skill_icon_"..i])
        self.show_skill_list[i]:SetNeedChangeSkillBtnPos(false)
        self.show_skill_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self, i))

        self.node_list["text_skill_name_"..i].tmp.text = Language.Dujie.SkillName[i]
    end

    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickBtnClose, self))
    XUI.AddClickEventListener(self.node_list.btn_start_dujie, BindTool.Bind(self.OnClickBtnStartDujie, self))
    XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind(self.OnClickBtnInvite, self))
end

function DujieOperateView:ReleaseCallBack()

    TweenManager.Instance:SetIsOpenTween(true)
    self:CleanDujieingTimer()

    if self.harm_tweener then
		self.harm_tweener:Kill()
		self.harm_tweener = nil
	end

    if self.view_shake_tween then
		self.view_shake_tween:Kill()
		self.view_shake_tween = nil
	end
end


function DujieOperateView:ShowIndexCallBack()
    self:FlushSkill()
    FightText.Instance:SetActive(false)

    -- MainuiWGCtrl.Instance:FlushView(0, "dujie_main_ui_state", {true})
    -- SafeBaseView.OpenScreenShot()

    self.node_list.invite_layer:SetActive(false)

    self.node_list.dujie_layer:SetActive(false)
    self:FlushInvateLayer()
    self:FlushReduce()

    -- 技能
    for i = 1, 3 do
        local dujie_skill_id = DujieWGData.Instance:GetOtherCfg("dujie_skill_"..i)
        self.show_skill_list[i]:SetData({skill_id = dujie_skill_id, skill_level = 1,skill_name = Language.Dujie.SkillName[i]})
        self.show_skill_list[i]:SetSkillBtnCD()
    end

    

    local cg_bundle = "cg/a3_cg_dujie/new_system_prefab"
    local cg_asset = "A3_CG_DJSystem_Nan1"
    local role_sex, role_prof = RoleWGData.Instance:GetRoleSexProf()
    if role_sex == GameEnum.FEMALE then
        cg_asset = "A3_CG_DJSystem_Nv1"
    end
    CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset),
        function()
            -- ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView)  -- 功能引导表
            -- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end,
        function(cg_obj)
            self.is_cg_loaded = true
        end,
    nil, nil, true, true,
    function()
        local dujie_cg = CgManager.Instance:GetCurCg()
        if self.cur_cg_stage == CG_STAGE_TYPE.BEGIN then
            dujie_cg:SetNodeVisible("stage_begin", false)
            dujie_cg:SetNodeVisible("stage_ing", true)
            self.node_list.invite_layer.canvas_group.blocksRaycasts = true
            DujieWGCtrl.Instance:OpenDujieInviteView()
        elseif self.cur_cg_stage == CG_STAGE_TYPE.END then
            self:Close()
        end
    end)
    self.cur_cg_stage = CG_STAGE_TYPE.BEGIN

    self:ShowDujieLayer(2)

    self:HideHarmNode()
end

function DujieOperateView:CloseCallBack()
    DujieWGCtrl.Instance:RePlayMusic()
    self:CleanDujieingTimer()
    ViewManager.Instance:RemoveCanInactiveView(GuideModuleName.MainUIView)
    SafeBaseView.SetOnlyView(nil)
    self.cur_cg_stage = nil
    CgManager.Instance:OnPlayEnd()
    self.is_cg_loaded = false
    -- MainuiWGCtrl.Instance:FlushView(0, "dujie_main_ui_state", {false})
    -- SafeBaseView.CloseScreenShot()
    FightText.Instance:SetActive(true)

    TweenManager.Instance:SetIsOpenTween(false)

    DujieWGCtrl.Instance:OpenDujieView()
end

function DujieOperateView:OnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "flush_hp" then

        elseif k == "start_dujie" then
            self.cur_cg_stage = CG_STAGE_TYPE.ING
            self:StartDujie()
        elseif k == "flush_dujie_ing" then
            self:FlushDujieingView()
            if v.harm_num and v.harm_num ~= nil then
                self:HarmAnim(v.harm_num)
            end

        elseif k == "show_thunder" then
            -- print_error("雷电出现")
            if self.next_thunder_type then
                self:SetThunderEffectShow(self.next_thunder_type)
            end
            -- if v.harm_num ~= 0 then
            --     self:HarmAnim(v.harm_num)
            -- end

        elseif k == "show_skill" then
            local ordeal_info = DujieWGData.Instance:GetOrdealInfo()
            local ordeal_base_info = DujieWGData.Instance:GetOrdealBaseInfo()
            
            self:CleanSkillShowTimer()

            local server_time = TimeWGCtrl.Instance:GetServerTime()
            local skill_cfg = DujieWGData.Instance:GetSkillCfg(ordeal_base_info.level + 1, ordeal_info.skill_seq)
            if not IsEmptyTable(skill_cfg)  and skill_cfg.type ~= 2 then
                local duration_ms = 0
                if skill_cfg.type == 1 then
                    duration_ms = skill_cfg.param3
                elseif skill_cfg.type == 3 then
                    duration_ms = skill_cfg.param1
                end
                if duration_ms ~= 0 then
                    local duration_time = ordeal_info.skill_active_time + (duration_ms/1000) - server_time
    
                    if duration_time > 0 then
                        self.skill_show_timer = GlobalTimerQuest:AddTimesTimer(function ()
                            self:SetSkillEffectShow(nil,false)
                        end, duration_time , 1)
                        self:SetSkillEffectShow(ordeal_info.skill_seq + 1, true)
                    end
                end
            end
        elseif k == "flush_watch" then
            self:FlushInvateLayer()
        end
    end
end

-- 点击返回
function DujieOperateView:OnClickBtnClose()
    if DujieWGData.Instance:IsInDujie() then
        local next_cfg = DujieWGData.Instance:GetNextDujieCfg()
        local time_show_str = TimeUtil.FormatSecondDHM7(next_cfg.fail_damage_time)
        TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Dujie.CannelTips, time_show_str or 0), function ()
            DujieWGCtrl.Instance:DujieCannel()
        end)
    else
        if self.cur_cg_stage ~= CG_STAGE_TYPE.END then
            self.node_list.invite_layer:SetActive(false)
            self.cur_cg_stage = CG_STAGE_TYPE.END
            if self.is_cg_loaded then
                local dujie_cg = CgManager.Instance:GetCurCg()
                if not IsEmptyTable(dujie_cg) then
                    dujie_cg:SetNodeVisible("stage_begin", false)
                    dujie_cg:SetNodeVisible("stage_ing", false)
                    dujie_cg:SetNodeVisible("stage_end", true)
                end
            end
            self:HideDujieLayer()
        end

    end
end

function DujieOperateView:HideDujieLayer(time)
    self.node_list.invite_layer.canvas_group:DoAlpha(1, 0, time or 1)
    self.node_list.invite_layer.canvas_group.blocksRaycasts = false

    self.node_list.other_layer.canvas_group:DoAlpha(1, 0, time or 1)
    self.node_list.other_layer.canvas_group.blocksRaycasts = false
end

function DujieOperateView:ShowDujieLayer(time)
    self.node_list.invite_layer.canvas_group:DoAlpha(0, 1, time or 1)
    self.node_list.invite_layer.canvas_group.blocksRaycasts = false

    self.node_list.invite_layer:SetActive(true)

    self.node_list.other_layer.canvas_group:DoAlpha(0, 1, time or 1)
    self.node_list.other_layer.canvas_group.blocksRaycasts = true
end

-- 点击邀请
function DujieOperateView:OnClickBtnInvite()
    -- local is_can_invite = DujieWGData.Instance:SetLastInviteTime()
    DujieWGCtrl.Instance:OpenDujieInviteView()
end


-- 开始渡劫
function DujieOperateView:OnClickBtnStartDujie()
 
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.Common then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotFindPath)
        return
    end
    if not DujieWGData.Instance:IsInDujieArea() then
        TipWGCtrl.Instance:OpenAlertTips(Language.Dujie.DujieAreaTips,function ()
            DujieWGData.Instance:SetIsNeedOpenDujie(true)
            self:Close()
            DujieWGCtrl.Instance:GotoDujieArea()
        end,nil,nil,nil,5,nil,Language.Dujie.Join)
        return
    end

    MountWGCtrl.Instance:SendMountGoonReq(0)
    DujieWGCtrl.Instance:DujieStart()
end



function DujieOperateView:CleanDujieingTimer()
    self:CleanDujieStarTimer()
    self:CleanWarningTimer()
    self:CleanThunderTimer()
    self:CleanSkillShowTimer()
    self:CleanSkillCDTimer()
    self:CleanHpAnimTimer()
    self:CleanFailReduceTimer()
end

function DujieOperateView:CleanDujieStarTimer()
    if self.dujie_star_timer and CountDown.Instance:HasCountDown(self.dujie_star_timer) then
        CountDown.Instance:RemoveCountDown(self.dujie_star_timer)
        self.dujie_star_timer = nil
    end
end

function DujieOperateView:CleanHpAnimTimer()
    if self.hp_anim_timer and CountDown.Instance:HasCountDown(self.hp_anim_timer) then
        CountDown.Instance:RemoveCountDown(self.hp_anim_timer)
        self.hp_anim_timer = nil
    end
end
function DujieOperateView:CleanWarningTimer()
    if self.warning_thunder_timer then
        GlobalTimerQuest:CancelQuest(self.warning_thunder_timer)
        self.warning_thunder_timer = nil
    end
end

function DujieOperateView:CleanThunderTimer()
    if self.thunder_timer then
        GlobalTimerQuest:CancelQuest(self.thunder_timer)
        self.thunder_timer = nil
    end
end

function DujieOperateView:CleanSkillShowTimer()
    if self.skill_show_timer then
        GlobalTimerQuest:CancelQuest(self.skill_show_timer)
        self.skill_show_timer = nil
    end
    -- 移过来的，可能是为了下次加载时特效显示正常
    -- 先屏蔽，如果后续需要启用，需要改下调用实际，回收的伤害cg已经没了
    -- self:SetSkillEffectShow(nil,false)
end

function DujieOperateView:CleanSkillCDTimer(index)
    if nil == index then
        for i = 1, 3 do
            if self.skill_cd_timer_list[i] and CountDown.Instance:HasCountDown(self.skill_cd_timer_list[i]) then
                CountDown.Instance:RemoveCountDown(self.skill_cd_timer_list[i])
                self.skill_cd_timer_list[i] = nil
            end
        end
    else
        
        if self.skill_cd_timer_list[index]and CountDown.Instance:HasCountDown(self.skill_cd_timer_list[index]) then
            CountDown.Instance:RemoveCountDown(self.skill_cd_timer_list[index])
            self.skill_cd_timer_list[index] = nil
        end
    end
end

function DujieOperateView:CleanFailReduceTimer()
    if self.fail_reduce_timer and CountDown.Instance:HasCountDown(self.fail_reduce_timer) then
        CountDown.Instance:RemoveCountDown(self.fail_reduce_timer)
        self.fail_reduce_timer = nil
    end
end



 -- 渡劫开始
function DujieOperateView:StartDujie()
    self.node_list.invite_layer:SetActive(false)
    self.node_list.dujie_layer:SetActive(true)

    self.node_list.img_start_time:SetActive(true)
    self.node_list.img_start_time.rect.anchoredPosition = Vector2(6, -300)
    self.node_list.img_start_time.canvas_group:DoAlpha(0,1,0.5)
    self.node_list.img_start_time.rect:DOLocalMoveY(118, 0.5)
    self.node_list.text_start_time.tmp.text = 5
    self:CleanDujieStarTimer()
    self:FlushDujieingView()
    self.dujie_star_timer = CountDown.Instance:AddCountDown(5, 1,
        -- 回调方法
        function(elapse_time, total_time)
            self.node_list.text_start_time.tmp.text = math.ceil(total_time - elapse_time)
        end,
        -- 倒计时完成回调方法
        function()
            self.node_list.img_start_time:SetActive(false)
            self.node_list.thunder_group:SetActive(true)
            self:FlushDujieingView()
        end
    )
end

-- 刷新邀请界面
function DujieOperateView:FlushInvateLayer()
    local order_range_count = DujieWGData.Instance:GetOrderRangeCount()
    local added_strength = DujieWGData.Instance:GetAddedStrength(order_range_count)

    self.node_list.text_watch_count.text.text = string.format(Language.Dujie.WatchCount, order_range_count)
    self.node_list.text_strength_value.text.text = string.format(Language.Dujie.Strngth, added_strength)
    
    self:FlushHp()
end

function DujieOperateView:FlushHp()
    local order_range_count = DujieWGData.Instance:GetOrderRangeCount()
    local added_strength = DujieWGData.Instance:GetAddedStrength(order_range_count)

    local zhuanzhi_level = RoleWGData.Instance:GetZhuanZhiNumber()
    local cultivation_level = CultivationWGData.Instance:GetXiuWeiState()
    -- 仙修
    local strength_cfg = DujieWGData.Instance:GetStrengthCfg(cultivation_level)
    -- 转职
    local vitality_cfg = DujieWGData.Instance:GetVitalityCfg(zhuanzhi_level)

    local total_vitality = vitality_cfg.vitality + added_strength

    -- 血量先到目标值
    self.node_list.img_vitality_slider_down.slider.value = 1
    self.node_list.img_added_strength_slider.slider.value = 1
    self.node_list.img_strenght_slider_down.slider.value = 1
    self.node_list.img_vitality_slider.slider.value = vitality_cfg.vitality / total_vitality
    self.node_list.img_strenght_slider.slider.value = 1


    if added_strength == 0 then
        self.node_list.text_vitality.tmp.text = string.format("%s/%s", vitality_cfg.vitality, total_vitality)
    else
        self.node_list.text_vitality.tmp.text = string.format("%s<color=#6cff95>+%s</color>/%s", vitality_cfg.vitality,added_strength, total_vitality)
    end

    self.node_list.text_strength.tmp.text = string.format("%s/%s", strength_cfg.strength, strength_cfg.strength)
end

-- 刷新渡劫中界面
function DujieOperateView:FlushDujieingView()
    ---------------------------血量start--------------------------

    -- 转职
    local zhuanzhi_level = RoleWGData.Instance:GetZhuanZhiNumber()
    local vitality_cfg = DujieWGData.Instance:GetVitalityCfg(zhuanzhi_level)

    -- 渡劫增加的护盾
    local order_range_count = DujieWGData.Instance:GetOrderRangeCount()
    local added_strength = DujieWGData.Instance:GetAddedStrength(order_range_count)

    local ordel_info = DujieWGData.Instance:GetOrdealInfo()

    -- 总护盾：配置值+额外值
    local total_vitality = vitality_cfg.vitality + added_strength
    local not_strength_value = math.min(vitality_cfg.vitality, ordel_info.vitality)


    local old_slider_value_vitality = self.node_list.img_vitality_slider.slider.value
    local old_added_strength_slider_value = self.node_list.img_added_strength_slider.slider.value

    local slider_diff_vitality = old_slider_value_vitality - (not_strength_value / total_vitality)
    local slider_diff_added_strength = old_added_strength_slider_value - (ordel_info.vitality / total_vitality)


    -- 血量先到目标值
    self.node_list.img_vitality_slider.slider.value = not_strength_value / total_vitality
    self.node_list.img_added_strength_slider.slider.value = ordel_info.vitality / total_vitality

    self:CleanHpAnimTimer()
    self.hp_anim_timer = CountDown.Instance:AddCountDown(0.5, 0.02,
        -- 回调方法
        function(elapse_time, total_time)
            local new_value_strength = old_added_strength_slider_value - slider_diff_added_strength * (elapse_time / total_time)

            self.node_list.img_vitality_slider_down.slider.value = new_value_strength
            self.node_list.text_vitality.tmp.text = string.format("%s/%s", math.floor(new_value_strength * total_vitality), total_vitality)
            
        end,
        -- 倒计时完成回调方法
        function()
            self.node_list.img_vitality_slider_down.slider.value = ordel_info.vitality / total_vitality
            self.node_list.text_vitality.tmp.text = string.format("%s/%s", ordel_info.vitality, total_vitality)
        end
    )
    ---------------------------血量end--------------------------
    local thunder_info = DujieWGData.Instance:GetFirstThunderInfo()
    if thunder_info then
        local thunder_cfg = DujieWGData.Instance:GetThunderCfgBySeq(thunder_info.seq)

        if thunder_cfg then
            -- print_error("开始雷电倒计时:",thunder_info.time)
            self:CleanWarningTimer()
            if thunder_info.time - THUNDER_WARNING_TIME > 0 then
                -- 预警倒计时
                self.warning_thunder_timer = GlobalTimerQuest:AddTimesTimer(function ()
                    -- print_error("预警出现")
                    self:SetWarningEffectShow(thunder_cfg.type)
                    self.node_list.img_warning:SetActive(true)
                end, thunder_info.time - THUNDER_WARNING_TIME , 1)
                self.node_list.img_warning:SetActive(false)

            else
                self.node_list.img_warning:SetActive(true)
            end

            self.next_thunder_type = thunder_cfg.type
            self.node_list.text_next_thunder.tmp.text = DujieWGData.Instance:GetThunderName(thunder_cfg.type)
            -- 雷电倒计时 因为时间误差 刷新时间比客户端倒计时快，所以直接在刷新的时候播放
        else
            self.node_list.img_warning:SetActive(false)
        end
    end

    local count = DujieWGData.Instance:GetThunderCount()
    self.node_list.text_thunder_count.tmp.text = string.format(Language.Dujie.NeedThunderCount,count)

    -- 渡劫保护
    self:FlushReduce()
    
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    -- 技能
    for i = 1, 3 do
        local skill_cfg = DujieWGData.Instance:GetSkillCfg(base_info.level+1, i - 1)
        local cd_time = DujieWGData.Instance:GetSkillCD(i)
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        self.show_skill_list[i]:SetSkillBtnCD(math.ceil(cd_time-server_time),skill_cfg.cd_time)

        if cd_time-server_time > 0 then
            self:CleanSkillCDTimer(i)
            local time = tonumber(string.format("%.1f", cd_time-server_time))
            self.skill_cd_timer_list[i] = CountDown.Instance:AddCountDown(time, 0.02,
                -- 回调方法
                function(elapse_time, total_time)
                    local server_time = TimeWGCtrl.Instance:GetServerTime()
                    self.show_skill_list[i]:SetSkillBtnCD(math.ceil(cd_time-server_time),skill_cfg.cd_time)
                end,
                -- 倒计时完成回调方法
                function()
                    local server_time = TimeWGCtrl.Instance:GetServerTime()
                    self.show_skill_list[i]:SetSkillBtnCD(math.ceil(cd_time-server_time),skill_cfg.cd_time)
                end
            )
        end
        
    end
end

-- 渡劫保护
function DujieOperateView:FlushReduce()
    
    local base_info = DujieWGData.Instance:GetOrdealBaseInfo()
    -- 渡劫保护
    if base_info.fail_reduce_time > 0 then
        self.node_list.img_reduce:SetActive(true)

        local server_time = TimeWGCtrl.Instance:GetServerTime()


        self.node_list.text_reduce.tmp.text = TimeUtil.FormatSecondDHM9(base_info.fail_reduce_time - server_time)

        self:CleanFailReduceTimer()
        self.fail_reduce_timer = CountDown.Instance:AddCountDown(base_info.fail_reduce_time - server_time, 1,
            -- 回调方法
            function(elapse_time, total_time)
                self.node_list.text_reduce.tmp.text = TimeUtil.FormatSecondDHM9(total_time - elapse_time)
            end,
            -- 倒计时完成回调方法
            function()
                self.node_list.img_reduce:SetActive(false)
            end
        )
    else
        self.node_list.img_reduce:SetActive(false)
    end
end

function DujieOperateView:HideHarmNode()
    self.node_list.img_wudi:SetActive(false)
    self.node_list.add_num.text.text = ""
    self.node_list.harm_num.text.text = ""
end

-- 伤害动画
function DujieOperateView:HarmAnim(harm_num)
    if harm_num == nil then
        return
    end

    local anim_node = nil

    if harm_num == 0 then
        self.node_list.img_wudi:SetActive(true)
        anim_node = self.node_list.img_wudi
    elseif harm_num < 0 then
        anim_node = self.node_list.add_num
        anim_node.text.text = "+"..math.abs(harm_num)
    else
        anim_node = self.node_list.harm_num
        anim_node.text.text = "-"..harm_num
    end
	anim_node.rect.anchoredPosition = Vector2(133, 25)

	if self.harm_tweener then
        self.harm_tweener:Kill()
        self.harm_tweener = nil
    end
	

	self.harm_tweener = anim_node.rect:DOLocalMoveY(60, 0.5):OnComplete(function()
		self:HideHarmNode()
	end):SetEase(DG.Tweening.Ease.Linear)
    -- 添加震屏
    if self.view_shake_tween then
		self.view_shake_tween:Kill()
		self.view_shake_tween = nil
	end
    if harm_num > 0 then
        self:ShakeNodeAnimiBurst(self.node_list.dujie_layer, self.view_shake_tween)
    end

end

-- 震动
function DujieOperateView:ShakeNodeAnimiBurst(trans, sequence)
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	local pos = trans.transform.localPosition
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, 15, 0), 0.01)) 	--右5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, 15, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, -15, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, -15, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOLocalMove(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, 15, 0), 0.01))	--左5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, 15, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, -15, 0), 0.02))	--下5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, -15, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOLocalMove(pos, 0.02))	--恢复 0
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, -15, 0), 0.01))	--下5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, 15, 0), 0.02)) 	--右5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(-15, 15, 0), 0.02))	--左5
	sequence:Append(trans.transform:DOLocalMove(pos + u3dpool.vec3(15, -15, 0), 0.02))	--右5
	sequence:Append(trans.transform:DOLocalMove(pos, 0.02))	--恢复 0
    sequence:SetEase(DG.Tweening.Ease.Linear)
end

function DujieOperateView:OnClickSkillBtn(index)

    -- 渡劫中释放技能
    if self.cur_cg_stage == CG_STAGE_TYPE.ING then
        local skill_open_level = DujieWGData.Instance:GetOtherCfg("skill_open_level_"..index)
        local level = DujieWGData.Instance:GetDujieLevel()

        if level >= skill_open_level then
            DujieWGCtrl.Instance:UseDujieSkill(index - 1)
        end

    else
        -- 非渡劫显示技能tips
        local skill_id = DujieWGData.Instance:GetOtherCfg("dujie_skill_"..index)
        local skill_desc = SkillWGData.Instance:GetSKillDescBySkillId(skill_id)
        local show_data = {
            icon = skill_id,
            top_text = Language.Dujie.SkillName[index],					-- 技能名
            body_text = skill_desc,							-- 当前等级技能描述
            x = 0,
            y = 0,
            set_pos = true,
            is_active_skill = true,
        }
    
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)

        -- local skill_open_level = DujieWGData.Instance:GetOtherCfg("skill_open_level_"..index)
        -- local level = DujieWGData.Instance:GetDujieLevel()

        -- if level < skill_open_level then
        --     SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Dujie.SkillLimitStr,skill_open_level - level))
        -- end
    end

    local skill_open_level = DujieWGData.Instance:GetOtherCfg("skill_open_level_"..index)
    local level = DujieWGData.Instance:GetDujieLevel()

    if level < skill_open_level then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Dujie.SkillLimitStr,skill_open_level - level))
    end

end

function DujieOperateView:FlushSkill()
    local skill_open_level_1 = DujieWGData.Instance:GetOtherCfg("skill_open_level_1")
    local skill_open_level_2 = DujieWGData.Instance:GetOtherCfg("skill_open_level_2")
    local skill_open_level_3 = DujieWGData.Instance:GetOtherCfg("skill_open_level_3")

    local level = DujieWGData.Instance:GetDujieLevel()

    self.node_list.skill_bg:SetActive(level >= skill_open_level_1 or level >= skill_open_level_2  or level >= skill_open_level_3 )

    self.node_list.img_lock_mask_1:SetActive(level < skill_open_level_1)
    self.node_list.img_lock_mask_2:SetActive(level < skill_open_level_2)
    self.node_list.img_lock_mask_3:SetActive(level < skill_open_level_3)

    local unlock_bg_name = "a3_dz_xzy"
    local lock_bg_name = "a3_dz_xzy2"
    self.node_list.img_skill_bg_1.image:LoadSprite(ResPath.GetDujieImg(level < skill_open_level_1 and unlock_bg_name or lock_bg_name))
    self.node_list.img_skill_bg_2.image:LoadSprite(ResPath.GetDujieImg(level < skill_open_level_2 and unlock_bg_name or lock_bg_name))
    self.node_list.img_skill_bg_3.image:LoadSprite(ResPath.GetDujieImg(level < skill_open_level_3 and unlock_bg_name or lock_bg_name))
end
-------------------------操作cg-----------------------

--移除回调
function DujieOperateView:RemoveCommonTopDelayTimer()
    if self.show_common_top_timer then
        GlobalTimerQuest:CancelQuest(self.show_common_top_timer)
        self.show_common_top_timer = nil
    end
end

-- 显示预警
function DujieOperateView:SetWarningEffectShow(type)
    if self.is_cg_loaded then
        local dujie_cg = CgManager.Instance:GetCurCg()
        for i = 1, WARNING_MAX do
            dujie_cg:SetNodeVisible("yujing_"..i, false)
        end
        if type >= 3 then
            type = 2
        else
            type = 1
        end
        dujie_cg:SetNodeVisible("yujing_"..type, true)
    end
end

-- 显示雷电
function DujieOperateView:SetThunderEffectShow(index)
    if self.is_cg_loaded then
        local dujie_cg = CgManager.Instance:GetCurCg()
        for i = 1, THUNDER_MAX do
            dujie_cg:SetNodeVisible("shandian_"..i, false)
        end
        for i = 1, HIT_MAX do
            dujie_cg:SetNodeVisible("hit_"..i, false)
        end
        
        dujie_cg:SetNodeVisible("shandian_"..index, true)
        AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(index %2 == 1 and AudioUrl.Dujie_Jin or AudioUrl.Dujie_Hong, true, true))
    
        -- local hit_index = index %2 == 0 and 2 or 1
        -- dujie_cg:SetNodeVisible("hit_"..hit_index, true)
    end
end

-- 显示技能
function DujieOperateView:SetSkillEffectShow(index,is_show)
    if self.is_cg_loaded then
        local dujie_cg = CgManager.Instance:GetCurCg()
        for i = 1, SKILL_MAX do
            dujie_cg:SetNodeVisible("hudun_"..i, false)
        end
        if is_show then
            dujie_cg:SetNodeVisible("hudun_"..index, true)
        end
    end
end