TSShenLingDianSelectView = TSShenLingDianSelectView or BaseClass(SafeBaseView)

function TSShenLingDianSelectView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(926, 572)})
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_select_tianshen_shendian")
end

function TSShenLingDianSelectView:ReleaseCallBack()
	if self.select_list_view then
		self.select_list_view:DeleteMe()
		self.select_list_view = nil
	end

	self.now_tianshen_index = nil
	self.now_shendian_seq = nil
	self.now_slot_index = nil
end

function TSShenLingDianSelectView:SetData(shendian_seq, tianshen_index, slot_index)
	self.now_shendian_seq = shendian_seq
	self.now_tianshen_index = tianshen_index
	self.now_slot_index = slot_index
	self:Open()
end

function TSShenLingDianSelectView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShen.TSSelect

	if not self.select_list_view then
		local bundle = "uis/view/tianshen_prefab"
        local asset = "ts_select_shendian_itemrender"
        self.select_list_view = AsyncBaseGrid.New()
        self.select_list_view:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_select_baoshi_list"],
            assetBundle = bundle, assetName = asset, itemRender = ShenLingDaianBattleRender})
		self.select_list_view:SetStartZeroIndex(false)
	end
end

function TSShenLingDianSelectView:OnFlush(param_t)
	local has_list = TSShenLingDianData.Instance:GetCanSlotAllTianShen(self.now_tianshen_index, self.now_shendian_seq, self.now_slot_index, true)
	self.node_list.th_tips:CustomSetActive(#has_list <= 0)
	self.select_list_view:SetDataList(has_list)
end

-------------------------------------------------------------------------------------------------------------------
ShenLingDaianBattleRender = ShenLingDaianBattleRender  or BaseClass(BaseRender)

function ShenLingDaianBattleRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_chuzhan, BindTool.Bind(self.OnClickChuZhan, self))
	XUI.AddClickEventListener(self.node_list.btn_xiazhan, BindTool.Bind(self.OnClickXiaZhen, self))
end

function ShenLingDaianBattleRender:OnFlush()
	if (not self.data) or (not self.data.cfg_data) then 
		return 
	end

	local cfg = self.data.cfg_data
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.index)
	local tianshen_item = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.data.index)
	local tianshen_color = ItemWGData.Instance:GetItemConfig(tianshen_item.act_item_id)
	self.node_list.lbl_name.text.text = ToColorStr(cfg.bianshen_name, ITEM_COLOR[tianshen_color.color])
	self.node_list.level_Text.text.text = string.format(Language.Common.Level1, tianshen_info.level)
	self.node_list.cap_value.text.text = TianShenWGData.Instance:GetActivationZhanLi(self.data.index, true)
	local bundle, asset = ResPath.GetCommonImages(TianShenWGData.TianShenQualityImg[self.data.series])
	self.node_list.img_quality.image:LoadSprite(bundle, asset)
	self.node_list.btn_chuzhan:CustomSetActive(self.data.select_index ~= self.data.index)
	self.node_list.btn_xiazhan:CustomSetActive(self.data.select_index == self.data.index)
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.select_index)
	local star_num = tianshen_info and tianshen_info.star or 0
	self.node_list.change_remind:CustomSetActive(self.data.star > star_num)
	self.node_list.btn_chuzhan_text.text.text = self.data.select_index == -1 and Language.Pet.BtnName_13 or Language.ContralBeasts.SelectError3
	self:SetItemIcon()
end

function ShenLingDaianBattleRender:SetItemIcon()
	if (not self.data) or (not self.data.cfg_data) then 
		return 
	end

	local tianshen_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.data.index)
	local item_cfg = ItemWGData.Instance:GetItemConfig(tianshen_item_cfg.act_item_id)
	self.node_list.item_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
end

function ShenLingDaianBattleRender:OnClickChuZhan()
	if (not self.data) or (not self.data.cfg_data) then 
		return 
	end

	TianShenWGCtrl.Instance:RequestTianShenHallGoHall(self.data.select_shendian_seq, self.data.select_slot_index, self.data.index)
	TianShenWGCtrl.Instance:CloseTSShenLingDianSelectView()
end

function ShenLingDaianBattleRender:OnClickXiaZhen()
	if (not self.data) or (not self.data.cfg_data) then 
		return 
	end

	TianShenWGCtrl.Instance:RequestTianShenHallGoHall(self.data.select_shendian_seq, self.data.select_slot_index, -1)
	TianShenWGCtrl.Instance:CloseTSShenLingDianSelectView()
end


