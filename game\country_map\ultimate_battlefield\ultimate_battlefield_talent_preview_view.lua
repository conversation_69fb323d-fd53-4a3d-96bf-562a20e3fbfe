UltimateBattleTalentPerviewView = UltimateBattleTalentPerviewView or BaseClass(SafeBaseView)

local TALENT_TYPE = {
    TALENT_LOWER = 1,
    TALENT_UPER = 2,
}

function UltimateBattleTalentPerviewView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg()
    self.default_index = TabIndex.ultimate_battlefield_camp
    self.is_safe_area_adapter = true
    local bundle_name = "uis/view/country_map_ui/ultimate_battlefield_prefab"

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
    self:AddViewResource(TabIndex.ultimate_battlefield_talent_lower, bundle_name, "layout_ultimate_talent_preview")
    self:AddViewResource(TabIndex.ultimate_battlefield_talent_uper, bundle_name, "layout_ultimate_talent_preview")
    self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function UltimateBattleTalentPerviewView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.UltimateBattlefield.TitleTalentName
    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list,"VerticalTabbarUltimateTalent")
		self.tabbar:Init(Language.UltimateBattlefield.TabGropTalent, nil, nil, nil, nil)
        -- 标签选择回调绑定
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
	end
end

function UltimateBattleTalentPerviewView:ReleaseCallBack()
    if self.talent_list_view then
        self.talent_list_view:DeleteMe()
        self.talent_list_view = nil
    end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end
end


function UltimateBattleTalentPerviewView:LoadIndexCallBack(index)
    if index == 0 then
        return
    end

    if not self.talent_list_view then
        self.talent_list_view = AsyncListView.New(TalentSelectRender, self.node_list.list_view)
    end
end

function UltimateBattleTalentPerviewView:ShowIndexCallBack(index)
end

function UltimateBattleTalentPerviewView:OnFlush(param_t, index)
    self:FlushTalentList(index)
end

function UltimateBattleTalentPerviewView:FlushTalentList(index)
    if index == 0 then
        return
    end

    local data_list = nil

    if index == TabIndex.ultimate_battlefield_talent_lower then
        data_list = UltimateBattlefieldWGData.Instance:GetTalentListByType(TALENT_TYPE.TALENT_LOWER)
	elseif index == TabIndex.ultimate_battlefield_talent_uper then
        data_list = UltimateBattlefieldWGData.Instance:GetTalentListByType(TALENT_TYPE.TALENT_UPER)
    end

    if data_list then
        self.talent_list_view:SetDataList(data_list)
    end
end