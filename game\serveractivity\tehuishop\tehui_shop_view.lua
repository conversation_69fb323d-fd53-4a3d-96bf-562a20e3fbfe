----------------------------------------------------
-- 特惠秒杀
----------------------------------------------------
TehuiShopView = TehuiShopView or BaseClass(SafeBaseView)

function TehuiShopView:__init()
	self.view_name = GuideModuleName.TeHuiShop
	self.is_modal = true
	self:SetMaskBg(false, false)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self:AddViewResource(0, "uis/view/tehui_shop_prefab", "layout_act_preferential_view")
end

function TehuiShopView:__delete()
	
end

function TehuiShopView:ReleaseCallBack()
	if self.act_item_list ~= nil then 
		self.act_item_list:DeleteMe()
		self.act_item_list = nil
	end

	if CountDownManager.Instance:HasCountDown("tehuishop_left_time") then
		CountDownManager.Instance:RemoveCountDown("tehuishop_left_time")
	end

	if CountDownManager.Instance:HasCountDown("tehui_shop_next_time") then
		CountDownManager.Instance:RemoveCountDown("tehui_shop_next_time")
	end
end

function TehuiShopView:LoadCallBack()
	local bundle,asset = ResPath.GetActivityTitle("TeHuiMiaoSha")
	self.node_list["img_title"].image:LoadSprite(bundle,asset,function()
		self.node_list["img_title"].image:SetNativeSize()
	end)
	self.node_list.bg_title.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("bg_activity_title"))
	local bundle = "uis/view/tehui_shop_prefab"
	local asset = "ph_tehui_render"
	self.act_item_list = AsyncBaseGrid.New()
	self.act_item_list:SetStartZeroIndex(false)
	self.act_item_list:CreateCells({
		col = 2,
		change_cells_num = 1,
		assetName = asset,
		assetBundle = bundle,
		itemRender = TehuiShopItemRender, 
		list_view = self.node_list.ph_list
	})
	XUI.AddClickEventListener(self.node_list.btn_next_panic, BindTool.Bind(self.OpenNextPanicView,self))
end

function TehuiShopView:ShowIndexCallBack()
end

function TehuiShopView:OpenCallBack()
	self:SendInfoReq()
end

function TehuiShopView:OnFlush()
    local data_list = TehuiShopWGData.Instance:GetTeHuiShopCfg()
    self.act_item_list:SetDataList(data_list,3)

	local batch = TehuiShopWGData.Instance:GetRAPanicBuyBatch()

	-- 活动剩余时间
	local act_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ND_ACTIVITY_TYPE_PANIC_BUY)
	if CountDownManager.Instance:HasCountDown("tehuishop_left_time") then
		CountDownManager.Instance:RemoveCountDown("tehuishop_left_time")
	end
	
	local mul_time = act_data.next_time - (TimeWGCtrl.Instance:GetServerTime() or 0)
	if ACTIVITY_STATUS.OPEN == act_data.status and mul_time > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
			CountDownManager.Instance:AddCountDown("tehuishop_left_time", BindTool.Bind1(self.UpdateOpenCountDownTime, self), BindTool.Bind1(self.CompleteOpenCountDownTime, self), nil, mul_time, 1)
	else
		self:CompleteOpenCountDownTime()
	end

	--下次刷新时间
	if CountDownManager.Instance:HasCountDown("tehui_shop_next_time") then
		CountDownManager.Instance:RemoveCountDown("tehui_shop_next_time")
	end

	local time = TehuiShopWGData.Instance:GetNextTeHuiShopFreshTime(batch)
	if time > 0 then
		XUI.SetButtonEnabled(self.node_list.btn_next_panic, true)
	elseif act_data.end_time - TimeWGCtrl.Instance:GetServerTime() < 86400 then
		XUI.SetButtonEnabled(self.node_list.btn_next_panic, false)
	end
	if time > 0 then
		self:NextUpdateCallBack(1, time)
		CountDownManager.Instance:AddCountDown("tehui_shop_next_time", BindTool.Bind1(self.NextUpdateCallBack, self), BindTool.Bind1(self.NextCompleteCallBack, self), nil, time, 1)	
	else
		self:NextCompleteCallBack()
	end
	--先屏蔽，回頭看看是什麽
	self.node_list.lbl_next_time_title:SetActive(time > 0)
end

function TehuiShopView:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 and self.node_list.lbl_activity_time then
		self.node_list.lbl_activity_time.text.text = (TimeUtil.FormatSecondDHM2(total_time - elapse_time))
	end
end

function TehuiShopView:CompleteOpenCountDownTime()
	self.node_list.lbl_activity_time.text.text = ("")
end

function TehuiShopView:NextUpdateCallBack(elapse_time, total_time)
	if self.node_list.lbl_fresh_time then
		self.node_list.lbl_fresh_time.text.text = (TimeUtil.FormatSecondDHM8(total_time - elapse_time))
	end
end

function TehuiShopView:NextCompleteCallBack()
	self.node_list.lbl_fresh_time.text.text = ("")
	self:SendInfoReq()
end

function TehuiShopView:SendInfoReq()
	TehuiShopWGCtrl.Instance:SendRAPanicBuyOperaReq(RA_PANICBUY_OPERA_TYPE.RA_PANICBUY_OPERA_INFO)
end

function TehuiShopView:OpenNextPanicView()
	TehuiShopWGCtrl.Instance:OpenNextTehuiView()
end
-------------------------------------------------------------------------------------------------
-- ItemRender
TehuiShopItemRender = TehuiShopItemRender or BaseClass(BaseRender)
function TehuiShopItemRender:__init()
	self:AddClickEventListener()
end

function TehuiShopItemRender:__delete()
    if self.cell then 
        self.cell:DeleteMe()
        self.cell = nil
    end
end

function TehuiShopItemRender:LoadCallBack()
    self.cell = ItemCell.New(self.node_list.ph_cell_1)
end

function TehuiShopItemRender:OnFlush()
	if self.data == nil then return end
	local can_buy_flag = TehuiShopWGData.Instance:GetCanBuyTimes()
	self.bought_times = can_buy_flag[self.data.seq + 1] or 0
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    self.node_list.lbl_item_name.text.text = (item_cfg.name)
    self.node_list.lbl_item_cost.text.text = (self.data.need_gold)
	self.node_list.lbl_acount_num.text.text = (string.format(Language.OpenServer.EveryDayShopZhe, self.data.discount))
    self.cell:SetData(self.data.reward_item)

	if self.data.total_count > 0 then
		self.node_list.rich_item_left.text.text = string.format(Language.OpenServer.HotSellNum, self.data.total_count - self.bought_times)
	else
		self.node_list.rich_item_left.text.text = ""
	end
	local bought_tiems_list = TehuiShopWGData.Instance:GetHaveBoughtTimes()
	local bought_tiems = bought_tiems_list[self.index] or 0
	if bought_tiems and self.data.total_count > 0 then  -- 元宝购买不显示限制购买次数
		local str = self.data.limit_buy - bought_tiems .. "/" .. self.data.limit_buy
		self.cell:SetRightBottomColorText(str)
    	self.cell:SetRightBottomTextVisible(true)
	end
end

function TehuiShopItemRender:CreateSelectEffect()
end

function TehuiShopItemRender:OnClick()
	BaseRender.OnClick(self)
	local bought_tiems_list = TehuiShopWGData.Instance:GetHaveBoughtTimes()
	local bought_tiems = bought_tiems_list[self.index] or 0
	-- TipWGCtrl.Instance:OpenItem(self.data.reward_item, ItemTip.FROM_TEHUI_SHOP, {seq = self.data.seq})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
	ShopTip.Instance:SetData(item_cfg, 2, GameEnum.TEHUI_SHUP, nil, self.data.seq,nil,self.data.limit_buy - bought_tiems)
end
