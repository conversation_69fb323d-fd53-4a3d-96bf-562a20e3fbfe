EveryDayOneLoveView = EveryDayOneLoveView or BaseClass(SafeBaseView)

function EveryDayOneLoveView:__init()
	self.view_name = GuideModuleName.EveryDayOneLove
	self.is_modal = true
	self:SetMaskBg(true,true)

	--self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/everyday_onelove_ui_prefab", "everyday_onelove_view")
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
end

function EveryDayOneLoveView:__delete()
	
end

function EveryDayOneLoveView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("dailylove_time") then
		CountDownManager.Instance:RemoveCountDown("dailylove_time")
	end
end

function EveryDayOneLoveView:LoadCallBack()
	EveryDayOneLoveWGData.Instance:SetRemindFlage(false)
	XUI.AddClickEventListener(self.node_list.gocharge_btn, BindTool.Bind(self.ClickGoChargeBtn,self))
	-- 2021.12.22已经跟策划沟通是配置的，策划说先按照切图作死，后面他自己该配置自己换图，挺离谱的~ 
	self.node_list.num.text.text = (EveryDayOneLoveWGData.Instance:GetConfigValue() or 0) .."%"
	RemindManager.Instance:Fire(RemindName.EveryDayOneLove)
end

function EveryDayOneLoveView:OpenCallBack()
	
end

function EveryDayOneLoveView:OnFlush()
	local act_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ACTIVITY_DAILY_LOVE)
	if not act_data then return end
	if CountDownManager.Instance:HasCountDown("dailylove_time") then
		CountDownManager.Instance:RemoveCountDown("dailylove_time")
	end

	local mul_time = act_data.next_time - (TimeWGCtrl.Instance:GetServerTime() or 0)
	if ACTIVITY_STATUS.OPEN == act_data.status and mul_time > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("dailylove_time", BindTool.Bind1(self.UpdateOpenCountDownTime, self), BindTool.Bind1(self.CompleteOpenCountDownTime, self), nil, mul_time, 1)
	else
		self:CompleteOpenCountDownTime()
	end
end

function EveryDayOneLoveView:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 and self.node_list.time then
		self.node_list.time.text.text = (TimeUtil.FormatSecondDHM8(total_time - elapse_time))
	end
end

function EveryDayOneLoveView:CompleteOpenCountDownTime()
	self.node_list.time.text.text = ""
end

function EveryDayOneLoveView:ClickGoChargeBtn()
	FunOpen.Instance:DoOpenViewByName(GuideModuleName.Recharge, TabIndex.recharge_cz)
	--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_effect_skill)
	--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.time.transform, nil, Vector3(0, -250, 0))
end