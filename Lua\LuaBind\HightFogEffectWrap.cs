﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class HightFogEffectWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(HightFogEffect), typeof(PostEffectBase));
		<PERSON><PERSON>Function("SetHightFog", SetHightFog);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L<PERSON>Function("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetHightFog(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 9);
			HightFogEffect obj = (HightFogEffect)ToLua.CheckObject<HightFogEffect>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Color arg1 = ToLua.ToColor(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
			float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
			UnityEngine.Vector3 arg5 = ToLua.ToVector3(L, 7);
			float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
			float arg7 = (float)LuaDLL.luaL_checknumber(L, 9);
			obj.SetHightFog(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

