CustomizedSuitPartXuanCaiView = CustomizedSuitPartXuanCaiView or BaseClass(SafeBaseView)

function CustomizedSuitPartXuanCaiView:__init()
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/customized_suit_ui_prefab", "layout_customized_xuancai")
end

function CustomizedSuitPartXuanCaiView:__delete()

end

function CustomizedSuitPartXuanCaiView:LoadCallBack()
	if not self.part_list then
		self.part_list = AsyncListView.New(CustomizedSuitPartRender, self.node_list.part_list)
		self.part_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSuitPartCB, self))
		self.part_list:SetStartZeroIndex(true)
		self.part_list.default_select_index = -1
		self.part_list.cur_select_index = -1
	end

	if not self.xuancai_list then
		self.xuancai_list = {}

        for i = 1, 3 do
            local xuancai_obj = self.node_list.xuancai_list_view:FindObj(string.format("xuancai_itemrender_0%d", i))
            if xuancai_obj then
                local cell = XuanCaiItemRender.New(xuancai_obj, self)
				cell:SetClickCallBack(BindTool.Bind1(self.OnSelectXuanCaiItemCB, self))
                cell:SetIndex(i)
                self.xuancai_list[i] = cell
            end
        end
	end

	if self.show_model == nil then
		self.show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.show_model:SetRenderTexUI3DModel(display_data)
		-- self.show_model:SetUI3DModel(self.node_list["display"].transform, self.node_list["EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.show_model)
	end
end


function CustomizedSuitPartXuanCaiView:ReleaseCallBack()
	if self.part_list then
		self.part_list:DeleteMe()
		self.part_list = nil
	end

    if self.xuancai_list and #self.xuancai_list > 0 then
		for _, incubate_cell in ipairs(self.xuancai_list) do
			incubate_cell:DeleteMe()
			incubate_cell = nil
		end

		self.xuancai_list = nil
	end

	if self.show_model then
		self.show_model:DeleteMe()
		self.show_model = nil
	end

	-- if self.use_update then
	-- 	Runner.Instance:RemoveRunObj(self)
	-- 	self.use_update = nil
	-- end

	self.model_res_id = nil

	self.old_fashion_role_res = nil
    self.old_fashion_weapon_res = nil
    self.old_fashion_action_name = nil
    -- self.next_create_fashion_footprint_time = nil
end

function CustomizedSuitPartXuanCaiView:CloseCallBack()
	self.cur_xuancai_index = nil
	self.cur_xuancai_data = nil
	self.select_suit_part = nil
	self.select_suit_part_index = nil
	self.suit = nil
	self.part = nil
end

-- 设置套装id和部件
function CustomizedSuitPartXuanCaiView:SetSuitPart(suit, part)
	self.suit = suit
	self.part = part
end

function CustomizedSuitPartXuanCaiView:OnSelectXuanCaiItemCB(xuancai_item, cell_index, is_default, is_click)
    if nil == xuancai_item or nil == xuancai_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = xuancai_item:GetIndex()
	if self.cur_xuancai_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

	self.cur_xuancai_index = cell_index
	self.cur_xuancai_data = xuancai_item.data
	self:FlushMessage()
	self:ChangeXuanCaiSelect()
end

function CustomizedSuitPartXuanCaiView:ChangeSelectPartColorful(xuancai_item)
	self:OnSelectXuanCaiItemCB(xuancai_item)
end

function CustomizedSuitPartXuanCaiView:FlushXuanCailistForServer()
	self.cur_xuancai_index = nil
	self.cur_xuancai_data = nil
	-- 刷新炫彩列表
	self:FlushXuanCailist()
end

---套装列表点击
function CustomizedSuitPartXuanCaiView:OnSelectSuitPartCB(part_item)
	if nil == part_item or nil == part_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = part_item:GetIndex()
	if self.select_suit_part_index == cell_index then
		return
	end

	self.select_suit_part = part_item.data.part
	self.select_suit_part_index = cell_index
	-- 切换部件需要清掉当前炫彩选择
	self.cur_xuancai_index = nil
	self.cur_xuancai_data = nil
	-- 刷新炫彩列表
	self:FlushXuanCailist()
end

-- 炫彩选择回调
function CustomizedSuitPartXuanCaiView:SetXuanCaiChangeData(cur_gou_xuan_data)
	self.cur_gou_xuan_data = cur_gou_xuan_data
end

-- 刷新入口
function CustomizedSuitPartXuanCaiView:OnFlush(param_t)
	if (not self.suit) or (not self.part) then
		return
	end

	-- 刷新大列表
	local suit_data = CustomizedSuitWGData.Instance:GetCustomizedSuitInfoBySuit(self.suit)
	if IsEmptyTable(suit_data) then
		return
	end

	self.part_list:SetDataList(suit_data.part_list)

	local jump_index = 1
	if self.select_suit_part == nil then
		for index, temp_data in pairs(suit_data.part_list) do
			if temp_data and temp_data.part == self.part then
				jump_index = index
				break
			end
		end

		self.part_list:JumpToIndex(jump_index, 2)
	end
end

-- 刷新炫彩列表
function CustomizedSuitPartXuanCaiView:FlushXuanCailist()
	if (not self.suit) or (not self.select_suit_part) then
		return
	end

	local prat_data = WardrobeWGData.Instance:GetSuitPartSingtonData(self.suit, self.select_suit_part)
	local list = self:AssembleXuanCaiList(prat_data)

	if not list then
		return
	end

	for _, data in ipairs(list) do
		if data and data.cfg_data then
			local suit_level = data.cfg_data.star_level or 0
			local preview_cfg = WardrobeWGData.Instance:GetXuanCaiBySuitLevel(self.suit, suit_level)

			if preview_cfg then
				data.name_change = preview_cfg.name_change
			end
		end
	end

	local temp_xuancai_index = 1
	if self.cur_xuancai_index == nil then
		for xuancai_index, xuancai_data in ipairs(list) do
			if xuancai_data and xuancai_data.is_select then
				temp_xuancai_index = xuancai_index
			end
		end
	end

	if self.xuancai_list then
		for index, xuancai_cell in ipairs(self.xuancai_list) do
			if xuancai_cell and list[index] then
				xuancai_cell:SetData(list[index])

				if self.cur_xuancai_index == nil and index == temp_xuancai_index then
					self:OnSelectXuanCaiItemCB(xuancai_cell)
				end
			end
		end
	end
end

-- 组装炫彩列表
function CustomizedSuitPartXuanCaiView:AssembleXuanCaiList(prat_data)
	if not prat_data then
		return nil
	end
	local star_level = CustomizedSuitWGData.Instance:GetSuitPartStar(prat_data)
	local list = NewAppearanceColorfulWGData.Instance:AssembleColorfulList(prat_data.type, prat_data.param1, prat_data.param2, star_level)
	return list
end

-- 刷新炫彩选中
function CustomizedSuitPartXuanCaiView:ChangeXuanCaiSelect()
	if (not self.cur_xuancai_index) or (not self.xuancai_list) then
		return
	end

	for index, xuancai_cell in ipairs(self.xuancai_list) do
		if xuancai_cell then
			xuancai_cell:SelectChange(index == self.cur_xuancai_index)
		end
	end
end

-- 刷新模型相关
function CustomizedSuitPartXuanCaiView:FlushMessage()
	if (not self.cur_xuancai_data) or (not self.cur_xuancai_data.cfg_data) then
		return
	end

	local prat_data = WardrobeWGData.Instance:GetSuitPartSingtonData(self.suit, self.select_suit_part)
	if not prat_data then
		return
	end

	local cfg = self.cur_xuancai_data.cfg_data

	if prat_data.type == WARDROBE_PART_TYPE.FASHION then			-- 时装
		self:FlushFashionModel(prat_data.param1, cfg.resouce)
	elseif prat_data.type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
		self:ShowQiChongModel(prat_data.type, cfg.appe_image_id)
	elseif prat_data.type == WARDROBE_PART_TYPE.LING_CHONG then	-- 灵宠
		self:ShowQiChongModel(prat_data.type, cfg.appe_image_id)
	elseif prat_data.type == WARDROBE_PART_TYPE.HUA_KUN then		-- 鲲
		self:ShowQiChongModel(prat_data.type, cfg.appe_image_id)
	elseif prat_data.type == WARDROBE_PART_TYPE.XIAN_WA then		-- 崽崽
	end
end

function CustomizedSuitPartXuanCaiView:FlushFashionModel(part_type, res_id)
    -- self:ClearFashionFootPrint()
    self.show_model:ClearLoadComplete()
    self.show_model:RemoveWaist()
    self.show_model:RemoveMask()
    self.show_model:RemoveTail()
    self.show_model:RemoveShouHuan()
    self.show_model:RemoveJianZhen()
	self.show_model:RemoveFootTrail()
    self.model_need_weapon = false
    if part_type == SHIZHUANG_TYPE.BODY then                            -- 时装
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type, res_id)

    elseif part_type == SHIZHUANG_TYPE.FOOT then                        -- 足迹
        self:ShowRoleFashionModel(part_type, nil, SceneObjAnimator.Move)
		self.show_model:SetFootTrailModel(res_id)
		self.show_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
        self.show_model:PlayRoleAction(SceneObjAnimator.Move)

        -- self.is_show_foot_print = true
        -- self.fashion_foot_effect_id = res_id

		-- if not self.use_update then
		-- 	Runner.Instance:AddRunObj(self, 8)
		-- 	self.use_update = true
		-- end
    elseif part_type == SHIZHUANG_TYPE.HALO then                        -- 光环
        self:ShowFashionMainModel(ResPath.GetHaloModel, res_id)

    elseif part_type == SHIZHUANG_TYPE.WING then                        -- 仙翼
        self.show_model:SetLoadComplete(function ()
            self.show_model:SetTrigger(SceneObjAnimator.Rest, true)
        end)
        self:ShowFashionMainModel(ResPath.GetWingModel, res_id)

    elseif part_type == SHIZHUANG_TYPE.FABAO then                       -- 法宝
        self:ShowFashionMainModel(ResPath.GetFaBaoModel, res_id)

    elseif part_type == SHIZHUANG_TYPE.SHENBING then                    -- 神兵
        local weapon_res_id = RoleWGData.GetFashionWeaponId(nil, nil, res_id)
        self:ShowFashionMainModel(ResPath.GetWeaponModelRes, weapon_res_id)
    elseif part_type == SHIZHUANG_TYPE.MASK then                        -- 面饰
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetMaskResid(res_id)

    elseif part_type == SHIZHUANG_TYPE.BELT then                        -- 腰饰
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetWaistResid(res_id)

    elseif part_type == SHIZHUANG_TYPE.WEIBA then                       -- 尾巴
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetTailResid(res_id)

    elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then                    -- 手环
        self.model_need_weapon = true
        self:ShowRoleFashionModel(part_type)
        self.show_model:SetShouHuanResid(res_id)

    elseif part_type == SHIZHUANG_TYPE.JIANZHEN then                    -- 剑阵
        self.show_model:SetLoadComplete(function ()
			self.show_model:SetTrigger(SceneObjAnimator.Rest, false)
        end)
        self:ShowFashionMainModel(ResPath.GetJianZhenModel, res_id)
    end

    if not self.model_need_weapon then
        self.show_model:RemoveWeapon()
    end

    -- 矫正
    if part_type ~= SHIZHUANG_TYPE.PHOTOFRAME
    and part_type ~= SHIZHUANG_TYPE.BUBBLE
    and part_type ~= SHIZHUANG_TYPE.WEIBA
    and part_type ~= SHIZHUANG_TYPE.FOOT then
        self.show_model:FixToOrthographic(self.root_node_transform)
    end

	if not part_type == SHIZHUANG_TYPE.SHENBING then                    -- 神兵
		self.show_model:PlayRoleAction(SceneObjAnimator.UiIdle)
	end
end

-- 角色模型
function CustomizedSuitPartXuanCaiView:ShowRoleFashionModel(part_type, fashion_res, action_name)
    local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
    if fashion_res then
        role_res_id = AppearanceWGData.GetFashionBodyResIdByResViewId(fashion_res)
    end

    -- 武器
    if not self.model_need_weapon then
        weapon_res_id = 0
    end

    action_name = action_name or SceneObjAnimator.UiIdle


    if role_res_id ~= self.old_fashion_role_res or weapon_res_id ~= self.old_fashion_weapon_res then
        self.old_fashion_role_res = role_res_id
        self.old_fashion_weapon_res = weapon_res_id
        self.old_fashion_action_name = action_name

		local extra_role_model_data = {
			weapon_res_id = weapon_res_id,
			animation_name = action_name,
		}
        self.show_model:SetRoleResid(role_res_id, nil, extra_role_model_data)
    else
        if action_name ~= self.old_fashion_action_name then
            self.old_fashion_action_name = action_name
            self.show_model:PlayRoleAction(action_name)
            self.show_model:FakeHideDelayShow()
        end
    end

    -- 角度
    if part_type == SHIZHUANG_TYPE.FOOT then
        self.show_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
    elseif part_type == SHIZHUANG_TYPE.WEIBA then
        self.show_model:SetRotation(MODEL_ROTATION_TYPE.WEIBA)
    end
end

-- 主模型
function CustomizedSuitPartXuanCaiView:ShowFashionMainModel(res_path, res_id, callback)
    self.old_fashion_role_res = nil
    self.old_fashion_weapon_res = nil
    self.old_fashion_action_name = 0
    local bundle, asset = res_path(res_id)
    self.show_model:SetMainAsset(bundle, asset, callback)
end

-- ----[[ 足迹
-- function CustomizedSuitPartXuanCaiView:Update(now_time, elapse_time)
-- 	if not self.is_show_foot_print then
-- 		return
-- 	end

-- 	if self.next_create_fashion_footprint_time == 0 then
--         self:CreateFashionFootPrint()
--         self.next_create_fashion_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end

--     if self.next_create_fashion_footprint_time == nil then --初生时也是位置改变，不播
--         self.next_create_fashion_footprint_time = 0
--     end

--     if self.next_create_fashion_footprint_time > 0 and now_time >= self.next_create_fashion_footprint_time then
--         self.next_create_fashion_footprint_time = 0
--     end

--     self:UpdateFashionFootprintPos()
-- end

-- -- 时装足迹 - 清除
-- function CustomizedSuitPartXuanCaiView:ClearFashionFootPrint()
-- 	if not IsEmptyTable(self.fashion_footprint_list) then
-- 		for k,v in pairs(self.fashion_footprint_list) do
-- 			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
-- 				v.role_model:OnRemoveGameObject(v.obj)
-- 				v.obj:SetActive(false)
-- 			end
-- 		end

--         self.fashion_footprint_list = {}
-- 	end

--     self.is_show_foot_print = nil
--     self.fashion_foot_effect_id = nil
-- end

-- -- 时装足迹 - 创建
-- function CustomizedSuitPartXuanCaiView:CreateFashionFootPrint()
-- 	if nil == self.fashion_foot_effect_id then
-- 		return
-- 	end

-- 	if nil == self.fashion_footprint_list then
-- 		self.fashion_footprint_list = {}
-- 	end

--     local pos = self.show_model.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.fashion_foot_effect_id)
-- 	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
-- 		if obj then
-- 			if nil ~= obj and self.fashion_footprint_list then
-- 				if self.show_model then
-- 					obj.transform.localPosition = Vector3.zero
-- 					obj:SetActive(false)
-- 					obj:SetActive(true)
-- 					table.insert(self.fashion_footprint_list, {obj = obj, role_model = self.show_model})
-- 					self.show_model:OnAddGameobject(obj)
-- 				else
-- 					ResPoolMgr:Release(obj)
-- 				end
-- 			end
-- 		end
--    	end)

-- 	if #self.fashion_footprint_list > 2 then
-- 		local obj = table.remove(self.fashion_footprint_list, 1)
-- 		obj.role_model:OnRemoveGameObject(obj.obj)
-- 		if not IsNil(obj.obj) then
-- 			obj.obj:SetActive(false)
-- 		end
-- 	end
-- end

-- -- 时装足迹 - 位移
-- function CustomizedSuitPartXuanCaiView:UpdateFashionFootprintPos()
-- 	if nil == self.fashion_footprint_list then
-- 		return
-- 	end

-- 	for k,v in pairs(self.fashion_footprint_list) do
-- 		if not IsNil(v.obj) then
-- 			local pos = v.obj.transform.localPosition
-- 			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 1.5)
-- 		end
-- 	end
-- end

-- 骑宠模型
function CustomizedSuitPartXuanCaiView:ShowQiChongModel(qc_type, appe_id)
    if not appe_id then
        return
    end

    -- self:ClearFashionFootPrint()
    self.old_fashion_action_name = nil
    self.old_fashion_weapon_res = 0
    self.show_model:ClearLoadComplete()
    self.show_model:ClearPartCrossFadeAnimCache()
    self.show_model:RemoveAllModel()

    if qc_type == WARDROBE_PART_TYPE.MOUNT then                  -- 坐骑
        self:ShowFashionMainModel(ResPath.GetMountModel, appe_id)
        self.show_model:PlayMountAction()
    elseif qc_type == WARDROBE_PART_TYPE.LING_CHONG then          -- 灵宠
        self:ShowFashionMainModel(ResPath.GetPetModel, appe_id)
        self.show_model:PlaySoulAction()
    elseif qc_type == WARDROBE_PART_TYPE.HUA_KUN then                -- 仙鲲
        self:ShowFashionMainModel(ResPath.GetMountModel, appe_id)
        self.show_model:PlayMountAction()
    end

    self.show_model:FixToOrthographic(self.root_node_transform)
end
--]]

------------------------------------------------------
----------------------------------灵兽切换大奖item-----------------------
XuanCaiItemRender = XuanCaiItemRender or BaseClass(BaseRender)
function XuanCaiItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.select_button, BindTool.Bind(self.OnClickSelectChange, self))		---套装部件炫彩
end

function XuanCaiItemRender:__delete()
end

function XuanCaiItemRender:OnClickSelectChange()
	if not self.data then return end
	if not self.data.cfg_data then return end
	local cfg = self.data.cfg_data

	if self.data.is_select then
		return
	end

	if self.data.appearance_type == WARDROBE_PART_TYPE.FASHION then			-- 时装
		if cfg.part_type == SHIZHUANG_TYPE.WING then			-- 羽翼
			NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, cfg.resouce, cfg.index)
		elseif cfg.part_type == SHIZHUANG_TYPE.FABAO then		-- 法宝
			NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, cfg.resouce, cfg.index)
		elseif cfg.part_type == SHIZHUANG_TYPE.JIANZHEN then	-- 剑阵
			NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, cfg.resouce, cfg.index)
		elseif cfg.part_type == SHIZHUANG_TYPE.SHENBING then	-- 武器
			NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, cfg.resouce, cfg.index)
		else
			NewAppearanceWGCtrl.Instance:OnUseFashion(cfg.part_type, cfg.index, 1)
		end
	elseif self.data.appearance_type == WARDROBE_PART_TYPE.MOUNT then			-- 坐骑
		NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, cfg.appe_image_id, cfg.image_id or 0)
	elseif self.data.appearance_type == WARDROBE_PART_TYPE.LING_CHONG then	-- 灵宠
		NewAppearanceWGCtrl.Instance:SendLingChongReq(MOUNT_OPERA_TYPE.USE_IMAGE, cfg.appe_image_id)
	elseif self.data.appearance_type == WARDROBE_PART_TYPE.HUA_KUN then		-- 鲲
		NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, cfg.appe_image_id)
	elseif self.data.appearance_type == WARDROBE_PART_TYPE.XIAN_WA then		-- 崽崽
	end

	-- CustomizedSuitWGCtrl.Instance:ChangeSelectPartColorful(self)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.XuanCaiChange)
end

function XuanCaiItemRender:OnFlush()
    if not self.data then return end

	local cfg = self.data.cfg_data
	self.node_list.select_button:CustomSetActive(self.data.is_unlock)
	self.node_list.select_hl:CustomSetActive(self.data.is_select)
	self:SelectChange(self.data.is_select)
	
	if cfg then
		self.node_list.lbl_name.text.text = self.data.name_change
		local color = self.data.is_unlock and COLOR3B.D_GREEN or "#28160C"
		local str = self.data.is_unlock and Language.Customized.XuanCaiUnLock or string.format(Language.Customized.XuanCaiLevel, cfg.star_level or 0)
		self.node_list.desc.text.text = ToColorStr(str, color)
	end
end

function XuanCaiItemRender:SelectChange(is_select)
	self.node_list.select_bg:CustomSetActive(is_select)
end