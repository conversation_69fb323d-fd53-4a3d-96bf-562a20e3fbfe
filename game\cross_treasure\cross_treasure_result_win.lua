CrossTreasureResultWinView = CrossTreasureResultWinView or BaseClass(SafeBaseView)
function CrossTreasureResultWinView:__init()
	self:AddViewResource(0, "uis/view/cross_treasure_ui_prefab", "cross_treasure_result_win")
	self:SetMaskBg(true)
end

function CrossTreasureResultWinView:LoadCallBack()
	if not self.show_reward_list then
		self.show_reward_list = AsyncListView.New(CrossTreasureResultRender, self.node_list.show_reward_list)
		self.show_reward_list:SetStartZeroIndex(true)
	end
end

function CrossTreasureResultWinView:ReleaseCallBack()
	if self.show_reward_list then
		self.show_reward_list:DeleteMe()
		self.show_reward_list = nil
	end
end

function CrossTreasureResultWinView:SetShowData(show_data)
	self.show_data = show_data
end

function CrossTreasureResultWinView:OnFlush()
	if not self.show_data then
		return
	end

	local cfg = CrossTreasureWGData.Instance:GetBeastPoolCfgByIdSeq(self.show_data.pool_id, self.show_data.seq)
	local str = ""

	if cfg then
		local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(cfg.beast_id)
		if beast_cfg then
			str = string.format(Language.CrossTreasure.BeastGatherResultWin, string.format(Language.CrossTreasure.BeastGatherTitleTips, beast_cfg.beast_name))
		end
	end

	self.node_list.win_tips.text.text = str
	self.show_reward_list:SetDataList(cfg.succ_reward_item)
end


-----------------------CrossTreasureReward-----------------
CrossTreasureResultRender = CrossTreasureResultRender or BaseClass(BaseRender)
function CrossTreasureResultRender:__init()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_pos)
	end
end

function CrossTreasureResultRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function CrossTreasureResultRender:OnFlush()
	if not self.data then return end 
	self.item_cell:SetData(self.data)

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg then
        self.node_list.item_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    end
end