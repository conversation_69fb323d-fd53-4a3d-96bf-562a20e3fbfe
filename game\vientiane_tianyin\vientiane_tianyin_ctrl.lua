require("game/vientiane_tianyin/vientiane_tianyin_data")
require("game/vientiane_tianyin/vientiane_tianyin_view")

VientianeTianyinCtrl = VientianeTianyinCtrl or BaseClass(BaseWGCtrl)
function VientianeTianyinCtrl:__init()
    if VientianeTianyinCtrl.Instance then
		error("[VientianeTianyinCtrl]:Attempt to create singleton twice!")
	end

    VientianeTianyinCtrl.Instance = self
    self.data = VientianeTianyinData.New()

    self.view = VientianeTianyinView.New(GuideModuleName.VientianeTianyinView)
    self:RegisterAllProtocols()
end

function VientianeTianyinCtrl:__delete()
    VientianeTianyinCtrl.Instance = nil

    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function VientianeTianyinCtrl:OpenVientianeTianyinView()
    self.view:Open()
end

function VientianeTianyinCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAWanXiangTianYinInfo, "OnOAWanXiangTianYinInfo")

    self.act_change = BindTool.Bind(self.OnActivityChange, self)
    ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function VientianeTianyinCtrl:OnOAWanXiangTianYinInfo(protocol)
    self.data:SetAllVientianeInfo(protocol)
    MainuiWGCtrl.Instance:FlushView(0, "vientiane_tianyin_tip")
    self:FlushView()
    RemindManager.Instance:Fire(RemindName.RemindVientiane)
end

function VientianeTianyinCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.VientianeTianyinView)
end

function VientianeTianyinCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN and (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.CLOSE) then
        MainuiWGCtrl.Instance:FlushView(0, "vientiane_tianyin_tip")
    end
end

function VientianeTianyinCtrl:OnRoleAttrChange(attr_name, value, old_value)
    if attr_name == "level" then
        local show_info = self.data:GetOtherCfg()
        local tianyin_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN)
        if (old_value < show_info.open_level and value >= show_info.open_level) and tianyin_is_open then
            MainuiWGCtrl.Instance:FlushView(0, "vientiane_tianyin_tip")
        end
    end
end