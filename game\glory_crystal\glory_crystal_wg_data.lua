GloryCrystalWGData = GloryCrystalWGData or BaseClass()

GloryCrystalWGData.SHOW_REWARD_POOL_COUNT = 14 -- 显示数量

--奖励类型.
GloryCrystalWGData.BIG_REWARD_SHOW_ITEM_TYPE =
{
	SpBig = 0,			--极品大奖.
	Big = 1,			--大奖.
	Normal = 2,			--普通奖励.
}

function GloryCrystalWGData:__init()
	if GloryCrystalWGData.Instance ~= nil then
		Error<PERSON><PERSON>("[GloryCrystalWGData] attempt to create singleton twice!")
		return
	end

	GloryCrystalWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
	self.draw_times = 0 --次数
	self.times_reward_flag = {}					-- 次数奖励领取状态
	self.free_draw_times = 0					--免费抽奖次数.
	self.convert_times_list = {} 				--兑换次数 限购 0:每日 1:每周 2:终身.
	self.collect_task_list = {}					--收集任务列表.key:task_seq.
	self.cache_draw_btn_index = 1
	self.result_reward_list = {}
    self.baodi_item = {}
	self.select_cell_info =						--当前选中的商品信息.
	{
		seq = 1,
		cell_idx = 1,
	}

	self.grade_collect_reward_flag = {}			-- 每档的任务领取标记

	self.daily_task_list = {}
	self.rmb_gift_count_list = {}
	self.rmb_one_key_flag = 0
	self.rmb_one_key_reward_flag = 0
	self.acc_recharge = 0
	self.acc_recharge_flag = {}
	self.next_level_recharge = 0
	self.cur_show_model_cfg = nil
	self.skip_spine_cache = false

    RemindManager.Instance:Register(RemindName.GloryCrystal, BindTool.Bind(self.ShowGloryCrystalRemind, self))
    RemindManager.Instance:Register(RemindName.GloryCrystalDailyTask, BindTool.Bind(self.GetDailyTaskRed, self))
    RemindManager.Instance:Register(RemindName.GloryCrystalPurchase, BindTool.Bind(self.GetPurchaseRed, self))
    RemindManager.Instance:Register(RemindName.GloryCrystalAccRecharge, BindTool.Bind(self.GetAccRechargeRed, self))
end

function GloryCrystalWGData:__delete()
	GloryCrystalWGData.Instance = nil
	self.grade = nil
	self.draw_times = nil
	self.times_reward_flag = nil
	self.convert_times_list = nil
	self.collect_task_list = nil
	self.cache_draw_btn_index = nil
	self.result_reward_list = nil
	self.baodi_item = nil
	self.daily_task_list = nil
	self.rmb_gift_count_list = nil
	self.acc_recharge_flag = nil
	self.cur_show_model_cfg = nil
	self.cur_daily_task_cfgs = nil
	self.grade_collect_reward_flag = nil

    RemindManager.Instance:UnRegister(RemindName.GloryCrystal)
end

function GloryCrystalWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_glory_crystal_auto")
	self.times_reward_cfg = ListToMap(cfg.times_reward, "grade", "seq")
	self.reward_pool_cfg = ListToMap(cfg.reward_pool, "grade", "seq")
	self.mode_cfg = ListToMap(cfg.mode, "mode")
	self.baodi_cfg = ListToMap(cfg.baodi, "grade")
	self.baodi_cfg_2 = ListToMapList(cfg.baodi, "grade")
	self.convert_cfg = ListToMap(cfg.convert, "grade", "seq")
	self.type_item_cfg = ListToMap(cfg.convert, "grade", "buy_type", "seq")
	self.stuff_cfg = ListToMap(cfg.convert, "stuff_id_1")
	self.item_random_desc = ListToMapList(cfg.item_random_desc, "grade", "grid")
	self.collect_cfg = ListToMapList(cfg.collect, "grade", "task_seq")
	self.collect_reward_cfg = ListToMap(cfg.collect_reward, "grade", "task_seq")
	self.display_model_cfg = ListToMap(cfg.display_model, "grade", "show_item_type")
	self.daily_task_cfg = cfg.daily_task
	self.rmb_gift_cfg = ListToMapList(cfg.rmb_gift, "grade")
	self.rmb_gift_cfg_map = ListToMap(cfg.rmb_gift, "grade", "seq")
	self.rmb_one_key_cfg = ListToMap(cfg.rmb_one_key, "grade")
	self.acc_recharge_cfg = ListToMapList(cfg.total_recharge, "grade")
	self.view_type_cfg = ListToMap(cfg.view_type, "grade")
	self.open_day_cfg = ListToMap(cfg.open_day, "grade")
	self.other_cfg = cfg.other[1]
end

--全部信息
function GloryCrystalWGData:SetAllGloryCrystalInfo(protocol)
	self.grade = protocol.grade
	-- print_error("SetAllGloryCrystalInfo self.grade = ", self.grade)
	self.draw_times = protocol.draw_times
	self.times_reward_flag = protocol.times_reward_flag
	self.free_draw_times = protocol.free_draw_times
	self.collect_task_list = protocol.collect_task_list
	self.convert_times_list = protocol.convert_times_list
	self.grade_collect_reward_flag = protocol.grade_collect_reward_flag
	self:SetDailyTask(protocol.daily_task or {})
	self:SetAccRecharge(protocol)
	self:SetPurchase(protocol)
end

--奖励结果
function GloryCrystalWGData:SetResultData(protocol)
	self.result_reward_list = protocol.result_reward_list
    self.baodi_item = protocol.baodi_item
end

--获取奖励对应的配置
function GloryCrystalWGData:CalDrawRewardList(protocol)
    local data_list = {}
    local item_ids = {}
	if not protocol or not protocol.count or protocol.count <= 0 then
		return data_list, item_ids
	end

	data_list = protocol.result_reward_list

	for i, v in pairs(protocol.baodi_item) do
		if v.item_id > 0 then
			table.insert(item_ids, v.item_id)
		end
	end

	return data_list, item_ids
end

function GloryCrystalWGData:GetRewardById(seq)
    return self.reward_pool_cfg[self.grade][seq]
end

-- 当前界面样式
function GloryCrystalWGData:GetCurViewStyle()
	return self.view_type_cfg and self.view_type_cfg[self.grade]
end

--档次
function GloryCrystalWGData:GetCurGrade() 
	return self.grade
end

--次数奖励领取状态
function GloryCrystalWGData:GetTimesRewardState(seq) 
	return self.times_reward_flag[seq] or 0
end

function GloryCrystalWGData:GetAllRewardState()
	return self.times_reward_flag
end

function GloryCrystalWGData:CheckTaskIsGet(seq)
	local task_flag_list = self.grade_collect_reward_flag[self.grade] or {}
	return task_flag_list[seq] == 1
end

--抽奖次数
function GloryCrystalWGData:GetDrawTimes()
	return self.draw_times
end

--兑换次数
function GloryCrystalWGData:GetConvertTimesBySeq(seq, type)
	return self.convert_times_list[seq][type] or 0
end

--免费抽奖次数.
function GloryCrystalWGData:GetFreeDrawTimes()
	return self.free_draw_times or 0
end

--收集任务列表.
function GloryCrystalWGData:GetCollectTaskList()
	local task_list = {}

	for task_seq, v in pairs(self.collect_task_list) do
		local collect_cfg = self:GetCollectTaskListByTaskSeq(task_seq)
		if collect_cfg then
			local process = 0
			for i = 1, #collect_cfg do
				if v.process_flag[i] == 1 then
					process = process + 1
				end
			end
			local data = {}
			data.task_seq = task_seq
			data.task_num = #collect_cfg	--任务数量.
			data.task_process = process		--任务完成进度.
			data.flag = v.flag				--收集奖励标记 0: 未完成 1: 可领取 2: 已领取 SYSTEM_FORCESHOW_TASK_STATUS
			if self:CheckTaskIsGet(task_seq) then -- 已领取过
				data.flag = SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH
				data.task_process = data.task_num
			end
			table.insert(task_list, data)
		end
	end

	return task_list
end

function GloryCrystalWGData:ShowCollectTaskRemind()
	local task_list = GloryCrystalWGData.Instance:GetCollectTaskList()
	if IsEmptyTable(task_list) then
		return false
	end

	for k, v in pairs(task_list) do
		if v.flag == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH then
			return true
		end
	end
end

function GloryCrystalWGData:GetCollectTaskListByTaskSeq(task_seq)
	return (self.collect_cfg[self.grade] or {})[task_seq]
end

function GloryCrystalWGData:GetCollectRewardListByTaskSeq(task_seq)
	return (self.collect_reward_cfg[self.grade] or {})[task_seq]
end

--获取保底item
function GloryCrystalWGData:GetBaodItem() 
	return self.baodi_item
end

--获取抽奖奖励
function GloryCrystalWGData:GetResultRewardList() 
	return self.result_reward_list
end

function GloryCrystalWGData:GetOtherCfg()
	return self.other_cfg
end

function GloryCrystalWGData:GetOpenDayCfg()
	-- print_error("GetOpenDayCfg self.grade = ", self.grade)
	return self.open_day_cfg and self.open_day_cfg[self.grade] or {}
end

function GloryCrystalWGData:GetMoneyCfg()
	local money_split = Split(self.other_cfg.buy_item_id, "|")
	local money_list = {}
	for k, v in pairs(money_split) do
		money_list[k] = tonumber(v)
	end

	return money_list
end

function GloryCrystalWGData:GetShopCfgSeq(seq)
	return (self.convert_cfg[self.grade] or {})[seq]
end

function GloryCrystalWGData:GetModeCfgByMode(mode)
	return self.mode_cfg[mode]
end

function GloryCrystalWGData:GetBaoDiCfgByGrade(grade)
	return self.baodi_cfg[grade]
end

function GloryCrystalWGData:GetBaoDiNeedDrawNum()
	local need_num = 0
	local baodi_cfg = self.baodi_cfg_2[self.grade]
	if baodi_cfg then
		local draw_times = self:GetDrawTimes()
		for k, v in pairs(baodi_cfg) do
			if draw_times < v.guarantee_count then
				need_num = v.guarantee_count - draw_times
				break
			end
		end
	end

	return need_num
end

function GloryCrystalWGData:GetAllTimesRewardCfg()
	return self.times_reward_cfg[self.grade] or {}
end

function GloryCrystalWGData:GetAllTimesRewardInfo()
	local sort_list = {}
	local cur_grade_times_reward_cfg = self.times_reward_cfg[self.grade]
	local index = 0
	if not IsEmptyTable(cur_grade_times_reward_cfg) then
		for k, v in pairs(cur_grade_times_reward_cfg) do
			index = index + 1
	 		sort_list[index] = {}
			sort_list[index].grade = v.grade
			sort_list[index].seq = v.seq
			sort_list[index].need_draw_times = v.need_draw_times
			sort_list[index].item = v.item
			local state = self:GetTimesRewardState(v.seq)
			if state == 1 then -- 已经领取奖励
				sort_list[index].state = REWARD_STATE_TYPE.FINISH
			elseif state == 0 and self.draw_times >= v.need_draw_times then --可领取
				sort_list[index].state = REWARD_STATE_TYPE.CAN_FETCH
			else --不可领取
				sort_list[index].state = REWARD_STATE_TYPE.UNDONE
			end
		end
	end

	return sort_list
end

--奖励进度
function GloryCrystalWGData:GetCurSliderProgress()
	local reward_cfg = {}
	local cur_progress = 0
	local reward_cfg = self:GetAllTimesRewardInfo()
	local draw_times = self:GetDrawTimes()
	if IsEmptyTable(reward_cfg) or draw_times == nil then
		return cur_progress
	end

	local progress_list = { 0.12, 0.27, 0.42, 0.58, 0.73, 0.88, 1 } --对应的进度条值
	for k, v in pairs(progress_list) do
		local seq = k - 1
		local reward_seq = k - 1
		local length = #progress_list
		local cur_need = reward_cfg[reward_seq] and reward_cfg[reward_seq].need_draw_times or 0
		local next_need = reward_cfg[reward_seq + 1] and reward_cfg[reward_seq + 1].need_draw_times or reward_cfg[#reward_cfg].need_draw_times
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if draw_times > cur_need and draw_times <= next_need then
			cur_progress = (draw_times - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif draw_times > reward_cfg[#reward_cfg].need_draw_times then
			cur_progress = progress_list[length]
			break
		end
	end

	return cur_progress
end

function GloryCrystalWGData:GetShowRewardPool()
	local show_list = {}
	local cur_grade_reward_pool_cfg = self.reward_pool_cfg[self.grade]
	if not IsEmptyTable(cur_grade_reward_pool_cfg) then
		for k, v in pairs(cur_grade_reward_pool_cfg) do
			if v.show_item == 1 then
				table.insert(show_list, v)
			end
		end
	end

	return show_list
end

function GloryCrystalWGData:GetShowRewardListByType(show_item_type)
	local show_list = {}
	local cur_grade_reward_pool_cfg = self.reward_pool_cfg[self.grade]
	if not IsEmptyTable(cur_grade_reward_pool_cfg) then
		for k, v in pairs(cur_grade_reward_pool_cfg) do
			if v.show_item == show_item_type then
				table.insert(show_list, v.item)
			end
		end
	end

	return show_list
end

function GloryCrystalWGData:GetShowConvertData()
	local sort_list = {}
	local cur_grade_convert_cfg = self.convert_cfg[self.grade] or {}
	local index = 0
	if not IsEmptyTable(cur_grade_convert_cfg) then
		for k, v in pairs(cur_grade_convert_cfg) do
			index = index + 1
	 		sort_list[index] = {}
			sort_list[index].grade = v.grade
			sort_list[index].seq = v.seq
			sort_list[index].time_limit = v.time_limit
			sort_list[index].item = v.item
			sort_list[index].stuff_id_1 = v.stuff_id_1  --策划说只用一种材料
			sort_list[index].stuff_num_1 = v.stuff_num_1
			sort_list[index].sort_index = 0
			local cur_convert_times = self:GetConvertTimesBySeq(v.seq, CONVERT_TYPE.DAILY)
			sort_list[index].cur_convert_times = cur_convert_times
			if cur_convert_times >= v.time_limit then --兑换次数达到上限
				sort_list[index].sort_index = 100
			else
				sort_list[index].sort_index = 10
			end
		end

		table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "seq"))
	end

	return sort_list
end

--兑换材料
function GloryCrystalWGData:GetIsStuffItem(item_id)
    return self.stuff_cfg[item_id] ~= nil
end

--模型配置.
function GloryCrystalWGData:GetModelCfgByType(type)
	return self.display_model_cfg and self.display_model_cfg[self.grade] and self.display_model_cfg[self.grade][type]
end

--获取模型配置染色方案.
function GloryCrystalWGData:GetDyeIndex(type, index)
	local dye_index = 1

	local model_cfg = self:GetModelCfgByType(type)
	if not model_cfg or not model_cfg.color_pro then
		return dye_index
	end

	local dye_list = Split(model_cfg.color_pro, "|")
	if dye_list[index] then
		dye_index = tonumber(dye_list[index])
	end

	return dye_index
end

--获取抽奖的选项
function GloryCrystalWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end


-- enable_anim为false表示跳过动画(奖励界面)
function GloryCrystalWGData:SetRewardAnimToggleData(enable_anim)
	self.reward_anim_toggle_enable = enable_anim
end

-- 返回false表示跳过动画(奖励界面)
function GloryCrystalWGData:GetRewardAnimToggleData()
	return self.reward_anim_toggle_enable or false
end

--抽獎界面动画（true 为跳过动画）
function GloryCrystalWGData:GetJumpAni()
	return self.jump_ani or false
end

function GloryCrystalWGData:SetJumpAni()
	self.jump_ani = not self.jump_ani
end

--是否跳过动画对应的延时
function GloryCrystalWGData:GetDelayTime()
    --是否跳过动画
    if self.jump_ani then
        return 0
    else
        return 2
    end
end

function GloryCrystalWGData:ShowGloryCrystalRemind()
	-- 活动没开不检测红点
	local status = ActivityWGData.Instance:GetSomeActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL)
	if not status then
		return 0
	end

	--累计抽奖次数奖励红点
	local info = self:GetAllTimesRewardInfo()
	for i, v in pairs(info) do
		if v.state == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

	local task_red = self:ShowCollectTaskRemind()
	if task_red then
		return 1
	end

	--可以免费抽.
	local free_draw_times = GloryCrystalWGData.Instance:GetFreeDrawTimes()
	if free_draw_times > 0 then
		return 1
	end

	--抽奖消耗道具数量足够.
	local mode_cfg = GloryCrystalWGData.Instance:GetModeCfgByMode(1)
	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	local item_cfg = ItemWGData.Instance:GetItemConfig(open_day_cfg.cost_item_id)
	local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
	if item_count >= mode_cfg.cost_item_num then
		return 1
	end

	-- 天裳豪礼
	if self:GetHaoLiRed() == 1 then
		return 1
	end

	return 0
end

function GloryCrystalWGData:GetGaiLvInfo()
	return self.item_random_desc[self.grade] or {}
end

function GloryCrystalWGData:SetSelectCellInfo(data)
	self.select_cell_info = data
end

function GloryCrystalWGData:GetSelectCellInfo()
	return self.select_cell_info
end

function GloryCrystalWGData:GetItemDataList(index)
	local shop_type = index

	local list = {}
	local item_list = (self.type_item_cfg[self.grade] or {})[shop_type]

	if not item_list then
		return list
	end

	for k, v in pairs(item_list) do
		table.insert(list, v)
	end

	if not IsEmptyTable(list) then
		local function check_limit(data)
			local is_can_buy = self:CanBuyExchangeShopItem(data.seq)
			local limit = 0
			--不可购买排序到最后.
			if not is_can_buy then
				limit = 1
			end
			return limit
		end

		table.sort(list, function (a, b)
			local order_a = 100000
			local order_b = 100000

			local limit_a = check_limit(a)
			local limit_b = check_limit(b)
			if limit_a > limit_b then
				order_a = order_a + 10000
			elseif limit_a < limit_b then
				order_b = order_b + 10000
			end

			-- if a.sort_id > b.sort_id then
			-- 	order_a = order_a + 1000
			-- elseif a.sort_id < b.sort_id then
			-- 	order_b = order_b + 1000
			-- end
			
			if a.seq > b.seq then
				order_a = order_a + 100
			elseif a.seq < b.seq then
				order_b = order_b + 100
			end

			return order_a < order_b
		end)
	end

	return list
end

function GloryCrystalWGData:GetBuyLimitInfo(seq)
	local buy_limit_list = {}
	local buy_times_list = {}

	local item_list = (self.convert_cfg[self.grade] or {})[seq]
	if not item_list then
		return buy_limit_list, buy_times_list
	end

	local buy_limit_split = Split(item_list.buy_limit, "|")				--每日|每周|终身.
	
	for k, v in pairs(buy_limit_split) do
		buy_limit_list[k - 1] = tonumber(v)
	end

	for i = 0, 2 do
		buy_times_list[i] = self:GetConvertTimesBySeq(seq, i)
	end

	return buy_limit_list, buy_times_list
end

--是否可以购买商品（限购）.
function GloryCrystalWGData:CanBuyExchangeShopItem(seq)
	local is_can_buy = true

	local buy_limit_list, buy_times_list = self:GetBuyLimitInfo(seq)
	if IsEmptyTable(buy_limit_list) or IsEmptyTable(buy_times_list) then
		return is_can_buy
	end

	local limit_num = 0

	--每日.
	limit_num = buy_limit_list[CONVERT_TYPE.DAILY]
	if limit_num > 0 and is_can_buy then
		is_can_buy =  buy_times_list[CONVERT_TYPE.DAILY] < limit_num
	end

	--每周.
	limit_num = buy_limit_list[CONVERT_TYPE.WEEK]
	if limit_num > 0 and is_can_buy then
		is_can_buy =  buy_times_list[CONVERT_TYPE.WEEK] < limit_num
	end

	--终身.
	limit_num = buy_limit_list[CONVERT_TYPE.LIFE]
	if limit_num > 0 and is_can_buy then
		is_can_buy =  buy_times_list[CONVERT_TYPE.LIFE] < limit_num
	end

	return is_can_buy
end

function GloryCrystalWGData:ContrastNum(limit_type, total_num, num)
	local can_buy_flag = total_num > num
	local color = ""
	if can_buy_flag then
		color = COLOR3B.C8
	else
		color = COLOR3B.C3
	end

	local str = ""
	str = string.format(Language.GloryCrystal.BuyDes3, color, total_num - num)
	str = Language.GloryCrystal["BuyDes" .. limit_type] .. str
	return can_buy_flag, str
end

--返回商品购买提示.
function GloryCrystalWGData:ExplainComposeStr(seq)
	local can_buy_flag, str = nil, nil

	local buy_limit_list, buy_times_list = self:GetBuyLimitInfo(seq)
	if IsEmptyTable(buy_limit_list) or IsEmptyTable(buy_times_list) then
		return can_buy_flag
	end

	local limit_num = 0

	local t_type = 0
	local list = {}

	--每日
	limit_num = buy_limit_list[CONVERT_TYPE.DAILY]
	if limit_num > 0  then
		t_type = CONVERT_TYPE.DAILY
		can_buy_flag, str = self:ContrastNum(t_type, limit_num, buy_times_list[CONVERT_TYPE.DAILY])
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end

	--每周
	limit_num = buy_limit_list[CONVERT_TYPE.WEEK]
	if limit_num > 0  then
		t_type = CONVERT_TYPE.WEEK
		can_buy_flag, str = self:ContrastNum(t_type, limit_num, buy_times_list[CONVERT_TYPE.WEEK])
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end

	--终身
	limit_num = buy_limit_list[CONVERT_TYPE.LIFE]
	if limit_num > 0  then
		t_type = CONVERT_TYPE.LIFE
		can_buy_flag, str = self:ContrastNum(t_type, limit_num, buy_times_list[CONVERT_TYPE.LIFE])
		list[t_type] = {}
		list[t_type].flag = can_buy_flag
		list[t_type].str = str
		list[t_type].t_type = t_type
	end

	--空表即无限制，则不限购.
	if IsEmptyTable(list) then
		return list
	end

	local tab = {}
	for index, value in pairs(list) do
		if value.flag then
			tab = value
		end
		break
	end

	return tab
end

-- 己售馨标识
function GloryCrystalWGData:ItemEmpty(seq)
	local buy_limit_list, buy_times_list = self:GetBuyLimitInfo(seq)

	local limit_num = 0

	--每日
	limit_num = buy_limit_list[CONVERT_TYPE.DAILY]
	if 0 < limit_num and limit_num <= buy_times_list[CONVERT_TYPE.DAILY] then
		return true, string.format(Language.FashionExchangeShop.CanBuyDes1, 0)
	end

	--终身
	limit_num = buy_limit_list[CONVERT_TYPE.LIFE]
	if 0 < limit_num and limit_num <= buy_times_list[CONVERT_TYPE.LIFE] then
		return true, string.format(Language.FashionExchangeShop.CanBuyDes2, 0)
	end

	return false
end

function GloryCrystalWGData:GetMaxBuyNum(seq)
	--不限购.
	local limit_str_list = self:ExplainComposeStr(seq)
	if IsEmptyTable(limit_str_list) then
		return 999
	end

	local buy_limit_list, buy_times_list = self:GetBuyLimitInfo(seq)

	local limit_num = 0
	local max = 0

	--终身
	limit_num = buy_limit_list[CONVERT_TYPE.LIFE]
	max = limit_num - buy_times_list[CONVERT_TYPE.LIFE]

	--无终身限购再判断每日限购. ex-终身限购大于每日限购也判断每日限购
	local day_limit = buy_limit_list[CONVERT_TYPE.DAILY]
	if limit_num <= 0 or (day_limit > 0 and limit_num > day_limit) then
		-- 个人每日
		max = day_limit - buy_times_list[CONVERT_TYPE.DAILY]
	end

	max = math.max(0, max)
	max = math.min(max, 999)

	return max
end

function GloryCrystalWGData:GetShowRewardPerviewList()
	local title_reward_item_data = {}

	local cfg = self.item_random_desc[self.grade] or {}
	if not cfg then
		return title_reward_item_data
	end

	for k, v in pairs(cfg) do
		if not title_reward_item_data[k] then
			title_reward_item_data[k] = {}
			title_reward_item_data[k].title_text = Language.GloryCrystal.RewardPreviewTitle[k]
			title_reward_item_data[k].reward_item_list = {}
		end

		for k2, v2 in pairs(v) do
			local item_data = {}
			item_data.item = { item_id = v2.item_id }
			item_data.probability_text = string.format(Language.GloryCrystal.Probability, v2.random_count)
	
			table.insert(title_reward_item_data[k].reward_item_list, item_data)
		end
	end

	return title_reward_item_data
end

-- 天裳豪礼红点
function GloryCrystalWGData:GetHaoLiRed()
	-- 每日任务
	if self:GetDailyTaskRed() == 1 then
		return 1
	end

	-- 直购
	if self:GetPurchaseRed() == 1 then
		return 1
	end

	-- 累充
	if self:GetAccRechargeRed() == 1 then
		return 1
	end

	return 0
end

function GloryCrystalWGData:SetSkipSpineStatus(is_skip)
    is_skip = is_skip or false
    self.skip_spine_cache = is_skip
end

function GloryCrystalWGData:GetSkipSpineStatus()
    return self.skip_spine_cache or false
end

--------------- 每日任务 ----------------------
function GloryCrystalWGData:SetDailyTask(daily_task)
	self:InitDailyTask()
	-- 排序
	self.daily_task_list = {}
	for _, v in pairs(daily_task) do
		local cfg = self:GetDailyTaskBySeq(v.seq)
		if cfg then
			v.cfg = cfg

			if v.fetch_flag ~= 0 then -- 已领取
				v.status = SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH
				v.sort = 2
			elseif v.process >= cfg.param1 then
				v.status = SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH
				v.sort = 0
			else
				v.status = SYSTEM_FORCESHOW_TASK_STATUS.NONE
				v.sort = 1
			end
			table.insert(self.daily_task_list, v)
		end
	end
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local comp = SortTools.KeyLowerSorters("sort", "seq")
	table.sort(self.daily_task_list, comp)
end

-- 根据天数初始化任务数据
function GloryCrystalWGData:InitDailyTask()
	self.cur_daily_task_cfgs = {}
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for _, value in pairs(self.daily_task_cfg) do
		if open_server_day >= value.start_day and open_server_day <= value.end_day then
			self.cur_daily_task_cfgs[value.task_seq] = value
		end
	end
end

function GloryCrystalWGData:GetDailyTask()
	return self.daily_task_list
end

function GloryCrystalWGData:GetDailyTaskBySeq(seq)
	return self.cur_daily_task_cfgs[seq]
end

function GloryCrystalWGData:GetDailyTaskRed()
	for _, v in pairs(self.daily_task_list) do
		if v.status == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH then
			return 1
		end
	end

	return 0
end
--------------- 每日任务end ----------------------

--------------- 累计充值 ----------------------
function GloryCrystalWGData:SetAccRecharge(info)
	self.acc_recharge = info.total_recharge_num or self.acc_recharge
	self.acc_recharge_flag = info.total_recharge_reward_flag or self.acc_recharge_flag
end

function GloryCrystalWGData:GetCurAccRecharge()
	return self.acc_recharge
end

function GloryCrystalWGData:GetAccRecharge()
	return self.acc_recharge_cfg[self.grade] or {}
end

function GloryCrystalWGData:GetAccRechargeFlag(seq)
	return self.acc_recharge_flag[seq]
end

function GloryCrystalWGData:GetAccRechargeRed()
	local list = self:GetAccRecharge()
	for _, cfg in pairs(list) do
		if self.acc_recharge >= cfg.real_recharge_num and self:GetAccRechargeFlag(cfg.seq) == 0 then
			return 1
		end
	end

	return 0
end

function GloryCrystalWGData:GetCurShowModelCfg()
	if self.cur_show_model_cfg and self.acc_recharge < self.next_level_recharge then
		return self.cur_show_model_cfg
	end

	local list = self:GetAccRecharge()
	table.sort(list, SortTools.KeyLowerSorters("real_recharge_num"))
	for _, v in ipairs(list) do
		if self.acc_recharge < v.real_recharge_num then
			self.cur_show_model_cfg = v
			return v
		end
	end

	self.cur_show_model_cfg = list[#list]
	return self.cur_show_model_cfg
end
--------------- 累计充值end ----------------------

--------------- 直购 ----------------------
function GloryCrystalWGData:SetPurchase(info)
	self.rmb_gift_count_list = info.rmb_gift_count or self.rmb_gift_count_list
	self.rmb_one_key_flag = info.rmb_one_key_flag or self.rmb_one_key_flag
	self.rmb_one_key_reward_flag = info.rmb_one_key_reward_flag or self.rmb_one_key_reward_flag
end

function GloryCrystalWGData:GetPurchase()
	return self.rmb_gift_cfg[self.grade] or {}
end

function GloryCrystalWGData:GetPurchaseExAward()
	return self.rmb_one_key_cfg[self.grade] or {}
end

function GloryCrystalWGData:GetPurchaseBuyCount(seq)
	return self.rmb_gift_count_list[seq] or -1
end

function GloryCrystalWGData:CheckIsRMB(seq)
	if self.rmb_gift_cfg_map then
		local cfg = self.rmb_gift_cfg_map[self.grade] and self.rmb_gift_cfg_map[self.grade][seq]
		return cfg and cfg.rmb_type ~= 0
	end

	return false
end

function GloryCrystalWGData:HasBuy()
	for k, v in pairs(self.rmb_gift_count_list) do
		if self:CheckIsRMB(k) and v > 0 then
			return true
		end
	end

	return false
end

function GloryCrystalWGData:IsOneKeyBuy()
	return self.rmb_one_key_flag == 1
end

function GloryCrystalWGData:IsBuyAll()
	local is_buy_all = true
	local list = self:GetPurchase()
	for _, cfg in pairs(list) do
		local count = self:GetPurchaseBuyCount(cfg.seq)
		if cfg.price > 0 and count < cfg.limit_num then
			is_buy_all = false
			break
		end
	end

	return is_buy_all
end

function GloryCrystalWGData:IsGotExReward()
	return self.rmb_one_key_reward_flag == 1
end

function GloryCrystalWGData:GetPurchaseRed()
	local free_num = self:GetPurchaseBuyCount(0)
	if free_num == 0 then
		return 1
	end

	local is_buy_all = true
	if not self:IsOneKeyBuy() and not self:IsBuyAll() then
		is_buy_all = false
	end

	if is_buy_all and not self:IsGotExReward() then
		return 1
	end

	return 0
end
--------------- 直购end ----------------------