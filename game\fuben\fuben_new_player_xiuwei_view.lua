FuBenNewPlayerXiuWeiView = FuBenNewPlayerXiuWeiView or BaseClass(SafeBaseView)

function FuBenNewPlayerXiuWeiView:__init()
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_newplayer_fuben_xiuwei")
end

function FuBenNewPlayerXiuWeiView:__delete()

end

function FuBenNewPlayerXiuWeiView:ReleaseCallBack()
	self.time_down_callback = nil
	self.tips_str = nil
	self.tips_node = nil
	self.fb_cfg = nil
	self:CleanTimeDown()
	self:RemoveEffectDelayTimer()
end

function FuBenNewPlayerXiuWeiView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.xiuwei_bianshen_btn_left,BindTool.Bind(self.OnClickXiuWeiBian<PERSON>hen, self))
	XUI.AddClickEventListener(self.node_list.xiuwei_bianshen_btn_right,BindTool.Bind(self.OnClickXiuWeiBianShen, self))
end

function FuBenNewPlayerXiuWeiView:OnFlush()
	local fuben_data = FuBenWGData.Instance:GetNewPlayerFBInfo()
	if not fuben_data or not next(fuben_data) then return end
	self.fb_cfg = FuBenWGData.Instance:GetNewPlayerFbCfg(fuben_data.fb_type)
	if self.fb_cfg == nil or self.fb_cfg.temp_xiuwei_index == -1 then
		return
	end

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if (not main_role_vo) or (main_role_vo.special_appearance ~= 0) then
		return
	end

	self.node_list.xiuwei_bianshen_show_root:CustomSetActive(self.fb_cfg.temp_xiuwei_index ~= -1)
	if self.fb_cfg.temp_xiuwei_index ~= -1 then
		self:FlushSpecialXiuWeiOperate(self.fb_cfg.temp_xiuwei_index)
	end
end

-- 副本修为变身特殊操作
function FuBenNewPlayerXiuWeiView:FlushSpecialXiuWeiOperate(xiuwei_index)
	-- 暂时没有操作
	self.tips_str = Language.FuBenPanel.NewPlayerXiuWei
	self.tips_node = self.node_list.xiuwei_bianshen_time_down
	self.time_down_callback = BindTool.Bind(self.OnClickXiuWeiBianShen, self)
	self:FlushTimeCountDown(5)
end

-----------------活动时间倒计时-------------------
function FuBenNewPlayerXiuWeiView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("new_player_xiuwei_choose_time_down") then
		CountDownManager.Instance:RemoveCountDown("new_player_xiuwei_choose_time_down")
	end
end

function FuBenNewPlayerXiuWeiView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > 0 then
		self:CleanTimeDown()
		self:UpdateNodeRoot(invalid_time)
		CountDownManager.Instance:AddCountDown("new_player_xiuwei_choose_time_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), nil, invalid_time, 1)
	end
end

function FuBenNewPlayerXiuWeiView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self:UpdateNodeRoot(valid_time)
	end
end

function FuBenNewPlayerXiuWeiView:UpdateNodeRoot(num)
	local temp_num = math.ceil(num)
	if self.tips_node then
		self.tips_node.text.text = string.format(self.tips_str, temp_num)
	end
end

function FuBenNewPlayerXiuWeiView:OnComplete()
	if self.time_down_callback then
		self.time_down_callback()
		self.time_down_callback = nil
	end
end

-- 请求模拟修为变身
function FuBenNewPlayerXiuWeiView:OnClickXiuWeiBianShen()
	--直接让玩家变身
	if self.fb_cfg == nil or self.fb_cfg.temp_xiuwei_index == -1 then
		return
	end

	self.node_list.xiuwei_bianshen_show_root:CustomSetActive(false)
	--GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	self:RemoveEffectDelayTimer()
	self.show_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end, 2)
	self:Close()

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if (not main_role_vo) or (main_role_vo.special_appearance ~= 0) then
		return
	end

	CultivationWGCtrl.Instance:SpecialBianShen(self.fb_cfg.temp_xiuwei_index)
end


--移除回调
function FuBenNewPlayerXiuWeiView:RemoveEffectDelayTimer()
    if self.show_effect_timer then
        GlobalTimerQuest:CancelQuest(self.show_effect_timer)
        self.show_effect_timer = nil
    end
end