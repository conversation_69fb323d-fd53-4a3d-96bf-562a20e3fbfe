return {
    id = 1000,
    name = "心魔幻境",
    scene_type = 0,
    bundle_name = "scenes/map/a3_fb_xinshoutiyan_main",
    asset_name = "A3_FB_XinShouTiYan_Main",
    width = 220,
    height = 310,
    origin_x = -120,
    origin_y = 0,
    levellimit = 0,
    is_forbid_pk = 0,
    skip_loading = 0,
    show_weather = 0,
    scene_broadcast = 0,
    scenex = 111,
    sceney = 20,
    npcs = {
		{id=10001, x=111, y=40, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10002, x=116, y=66, rotation_y = 180, is_walking = 0, paths = {}},
		{id=10003, x=111, y=219, rotation_y = 180, is_walking = 0, paths = {}},
    },
    monsters = {
    },
    doors = {
		{id=10000, type=0, level=0, target_scene_id=1004, target_door_id=10002, offset={0, 0, 0}, rotation={0, 0, 0}, x=139, y=155},
		{id=10001, type=0, level=0, target_scene_id=1003, target_door_id=10003, offset={0, 0, 0}, rotation={0, 0, 0}, x=148, y=160},
    },
    gathers = {
		{id=1000, x=110, y=68, disappear_after_gather=0},
    },
    jumppoints = {
		{id=0, target_id=1, range=5, x=110, y=78, jump_type=0, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=1, target_id=-1, range=5, x=107, y=143, jump_type=0, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=2, target_id=-1, range=5, x=110, y=67, jump_type=0, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
		{id=3, target_id=2, range=5, x=108, y=127, jump_type=0, air_craft_id=0, is_show=0, jump_speed=2, jump_act=0,jump_tong_bu=0,jump_time=1,camera_fov=0,camera_rotation=0,offset={0,0,0},play_cg=0,cgs={}},
    },
    fences = {
    },
    effects = {
    },
    sounds = {
    },
    scene_way_points = {
	},
    mask = "XQAAQAAAAG/9//+jt/9HPkgVcjlhUbPU06CPJG6eTUNgJzHD4I1/9uwqZuZ9zRq1PISyzGcvhd31uawWPh4b2qxIlnlqNYJvAV6los4QpSMBmIdzdHBwgSTpzIlHbF9A9MzMs4z7GGi/c/8cf3+k+2xBz4KpAVTJItLzaY5n0lP/jtSC+Ip2/RnSsuoiU54Z9xWJyqTs54jno6vNempzG0FJ99776dbqpQjgOsDnfHQOmjUkGnxFlIbbjZjZP7AVjhcPn+Pz4Xol4qB40du8jLEMI4mk8XnuTrLNE4KPwgz8fqP697S+66OAoUIXl3dYy8Mi8OpLSF1ITeR+JhgFieMANRjg6qf7giMRq7ECWuCDwYKoRuIp4MwQaw9R3r5urao6WiDrbhcBR8+BZNw2ZAWOnkQ0c4FIOPN48d7drXW5o6TEJkn2g8mC9cJWPpYznONVaqfqA2v//fLxSjwE4kJ5Wo/qiNxnjBiagS6QRjffKUw5rIZfEfSGpxhNWCF6RoGncifUXF1crySwa7HP1nA66m5ZUZ1qbCmQfIPAWywozTYV5ZHDLYhHnir/82ybyqFBOXC9/gqqNDCrboZz3JdYx1r8RFRAGqltHqu+Vzhf5pEd3E4btxrKfPVnZXAyMNRJ5GL5tfiT2Ksf1SGxUatzZllWz9jEwhPo6m+tga1MKe/+Zm0GvPzw1DFAKCt8HoiBIuV9rjizQ53NPZsiVCr0PJHrJ8UDhgyZMplbLb9CwL5yOcKgydj+9sZe2PNckEJy43cu0gQ8hiL8+QDy0YvR3uS+HSjW18Zz9VlGdX9tIkNJcmnP+3aJu4iWa4llIMU+ev//BZKdAA==",
}