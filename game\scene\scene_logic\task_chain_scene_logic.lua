TaskChainSceneLogic = TaskChainSceneLogic or BaseClass(CrossServerSceneLogic)

function TaskChainSceneLogic:__init()
	self.is_need_check = false
	self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE,BindTool.Bind(self.ObjCreate,self))
end

function TaskChainSceneLogic:__delete()
	if self.obj_create_event then
		GlobalEventSystem:UnBind(self.obj_create_event)
		self.obj_create_event = nil
	end
end

function TaskChainSceneLogic:Enter(old_scene_type, new_scene_type)
	self.is_need_check = true
	if old_scene_type ~= nil and OperationTaskChainWGData and OperationTaskChainWGData.Instance:GetIsTaskChainTaskScene(old_scene_type) then
		local info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
		if info == nil or info.cur_task_status == OPERATION_TASK_CHAIN_ACT_STATUS.OPEN then
			self.is_need_check = false
		end
	end

	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetSkillShowState(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
	end)

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
	if self.is_need_check and OperationTaskChainWGCtrl and OperationTaskChainWGCtrl.Instance then
		OperationTaskChainWGCtrl.Instance:CheckOpenAcceptView()
	end
end

function TaskChainSceneLogic:Out()
	CommonFbLogic.Out(self)
	
	self.is_need_check = false
	MainuiWGCtrl.Instance:SetSkillShowState(true)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	OperationTaskChainWGData:ResetTaskChainActInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()

	ViewManager.Instance:Close(GuideModuleName.OperationTaskChainFbInfoView)
	ViewManager.Instance:Close(GuideModuleName.OperationTaskChainScheduleView)

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end
end

function TaskChainSceneLogic:IsMonsterEnemy(target_obj, main_role)
	return false
end

function TaskChainSceneLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TaskChainSceneLogic:IsEnemy(target_obj, main_role, ignore_table, test)
	return false
end

function TaskChainSceneLogic:GetIsNeedCheck()
	return self.is_need_check
end

function TaskChainSceneLogic:SetIsNeedCheck(value)
	self.is_need_check = value
end

function TaskChainSceneLogic:GetGuajiPos()
end

function TaskChainSceneLogic:CanGetMoveObj()
	return false
end

function TaskChainSceneLogic:GetGuajiCharacter()
	local is_need_stop = true
	local all_info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
	if all_info ~= nil then
		local obj = Scene.Instance:GetObj(all_info.npc_objid)
		local main_role = Scene.Instance:GetMainRole()
		
		if obj ~= nil and not obj:IsDeleted() then
			if main_role ~= nil and not main_role:IsFollowState() then
				obj:FollowMe(main_role, OperationTaskChainWGData.Instance:GetFollowDis(), OBJ_FOLLOW_TYPE.TASK_CHAIN)
			end
		else
			local pos = OperationTaskChainWGData.Instance:GetMainSceneFollowPos()
			if pos ~= nil and not main_role:IsMove() and not main_role:IsFightState() then
				local m_x, m_y = main_role:GetLogicPos()
				local dis = GameMath.GetDistance(m_x, m_y, pos.x, pos.y, false)
				if dis > 1 then
					GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), pos.x, pos.y, 1)
				end
			end
		end
	end

	return nil, nil, is_need_stop
end

function TaskChainSceneLogic:CheckObjIsIgnoreSelect(target_obj, select_type)
	if SceneWGData:TargetSelectIsScene(select_type) then
		if target_obj ~= nil and not target_obj:IsDeleted() and target_obj:IsMonster() then
			local info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
			if info ~= nil and info.npc_objid ~= nil then
				local obj_id = target_obj:GetObjId()
				return obj_id == info.npc_objid
			else
				return false
			end		
		else
			return false
		end
	else
		return false
	end
end

function TaskChainSceneLogic:ObjCreate(obj)
	--这个场景不需要展示血条
	if obj ~= nil and not obj:IsDeleted() and obj:IsMonster() then
		local follow_ui = obj:GetFollowUi()
		if follow_ui ~= nil then
			follow_ui:GetHpBar():AddShieldRule(ShieldRuleWeight.High, function()
				return true
			end)
		end
	end
end
