local index_map_color_list = {
	5,  --红色
	4, 	--橙色
	3,	--紫色
	2,	--蓝色
	1,	--绿色
}

ShenShouDeComposeView = ShenShouDeComposeView or BaseClass(SafeBaseView)

function ShenShouDeComposeView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -8), sizeDelta = Vector2(814, 578)})
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_shenshou_decompose_view")
end

function ShenShouDeComposeView:LoadCallBack()
    if not self.bag_grid then
		self.bag_grid = ShenShouGrid.New()
		self.bag_grid:CreateCells({
            col = 9,
            cell_count = 45,
			change_cells_num = 2,
			list_view = self.node_list["item_grid"],
			itemRender = <PERSON>ShouDeComposeBagCell
		})
		self.bag_grid:SetIsMultiSelect(true)
		self.bag_grid:SetIsShowTips(false)
		self.bag_grid:SetStartZeroIndex(false)
		self.bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))
	end

    if not self.color_list then
		self.color_list = AsyncListView.New(ShenShouDeComposePinZhiListRender, self.node_list.order_list)
		self.color_list:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))

		self.cur_select_color_index = RoleWGData.GetRolePlayerPrefsInt("shenshou_decompose_select_pinzhi_color")
		if self.cur_select_color_index < 1 then
			self.cur_select_color_index = 1 -- 默认
			RoleWGData.SetRolePlayerPrefsInt("shenshou_decompose_select_pinzhi_color", self.cur_select_color_index)
		end
	
		self.color_list:SetDefaultSelectIndex(self.cur_select_color_index)
		self.color_list:SetDataList(Language.ShenShou.SoulRingEquipDeComposeNameList)
		self.node_list.cur_color_text.text.text = Language.ShenShou.SoulRingEquipDeComposeNameList[self.cur_select_color_index]
	end

	self.is_show_color_part = false
    self:OnClickSelectColor()
    XUI.AddClickEventListener(self.node_list.btn_select_color, BindTool.Bind(self.OnClickSelectColor, self))
	XUI.AddClickEventListener(self.node_list.close_order_list_part, BindTool.Bind(self.OnClickSelectColor, self))
    XUI.AddClickEventListener(self.node_list.btn_melting, BindTool.Bind(self.OnClickMeltingBtn, self))
	XUI.AddClickEventListener(self.node_list.target_icon, BindTool.Bind(self.OnClickTargetIconBtn, self))
	XUI.AddClickEventListener(self.node_list.button_auto_tunshi, BindTool.Bind(self.OnTunShiToggleClick, self))

	local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()
	if other_cfg and other_cfg.spar_id then
		local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(other_cfg.spar_id))
		self.node_list.target_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.target_icon.image:SetNativeSize()
		end)
	end

	self.node_list.title_view_name.text.text = Language.ShenShou.DeComposeViewTitleViewName
end

function ShenShouDeComposeView:ReleaseCallBack()
    if self.bag_grid then
        self.bag_grid:DeleteMe()
        self.bag_grid = nil
    end

    if self.color_list then
        self.color_list:DeleteMe()
        self.color_list = nil
    end

	if self.auto_tunshi_alert then
		self.auto_tunshi_alert:DeleteMe()
		self.auto_tunshi_alert = nil
	end
end

function ShenShouDeComposeView:OnFlush()
	local bag_data_list = ShenShouWGData.Instance:GetShenShouDeComposeBagDataList()
	self.bag_grid:SetDataList(bag_data_list)
    self.bag_grid:CancleAllSelectCell()

	local color = index_map_color_list[self.cur_select_color_index] or 0
    self.bag_grid:GetAllCellExceptCondition(color)
    self:FlushInfo()
	self:SetAutoTunShiSelect()
end

function ShenShouDeComposeView:SelectShenShouBagCellCallBack(cell)
	self:FlushInfo()
end

function ShenShouDeComposeView:SelectColorCallBack(cell)
    if nil == cell then
		return
	end

	local index = cell:GetIndex()
	self.cur_select_color_index = index
	self.node_list.cur_color_text.text.text = Language.ShenShou.SoulRingEquipDeComposeNameList[self.cur_select_color_index]
    
    self:OnClickSelectColor()

    local color = index_map_color_list[self.cur_select_color_index] or 0
    self.bag_grid:GetAllCellExceptCondition(color)
	self:FlushInfo()
end


function ShenShouDeComposeView:FlushInfo()
    local exp = 0
	local select_list = self.bag_grid:GetAllSelectCell()
	
	if not IsEmptyTable(select_list) then
		for k,v in pairs(select_list) do
            local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(v.item_id)

            if shenshou_equip_cfg.is_equip == 0 then
                -- 灵石
                exp = exp + shenshou_equip_cfg.contain_shulian
            else
                -- 装备  装备是带强化等级得 等不不在槽位上
                local strength_shenshou_cfg = ShenShouWGData.Instance:GetShenshouLevelList(shenshou_equip_cfg.slot_index, v.strength_level)
                if strength_shenshou_cfg then
                    if v.strength_level < 1 then
                        exp = exp + shenshou_equip_cfg.contain_shulian + strength_shenshou_cfg.contain_shulian
                    end
                end
            end
		end
	end

	self.node_list.melting_value.text.text = exp
end

function ShenShouDeComposeView:OnClickSelectColor()
	self.is_show_color_part = not self.is_show_color_part
	self.node_list["order_list_part"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_down"]:SetActive(self.is_show_color_part)
	self.node_list["color_arrow_up"]:SetActive(not self.is_show_color_part)
end

function ShenShouDeComposeView:OnClickMeltingBtn()
    local selected_cells = self.bag_grid:GetAllSelectCell()

	if IsEmptyTable(selected_cells) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ExtraTip8)
		return
	end

	local quality_tip = false
	local equip_index_list = {}
	for k, v in pairs(selected_cells) do
		equip_index_list[#equip_index_list + 1] = v.index

		local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(v.item_id)
		local star_count = ShenShouWGData.Instance:GetStarCount(v, shenshou_equip_cfg)
		if star_count >= 3 and shenshou_equip_cfg.quality >= 3 then
			quality_tip = true
		end
	end

    local double_flag = 0
	if quality_tip then
		TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.ShenShou.ChectQHEquip, function ()
			-- ShenShouWGCtrl.Instance:SendSHenshouReqStrength(self.select_soul_ring_seq, self.select_equip_data.slot_index, double_flag, #selected_cells, equip_index_list)

            ShenShouWGCtrl.Instance:OnCSShenshouDecompos(double_flag, #selected_cells, selected_cells)
		end, "ShenShouDeCompose", Language.ShenShou.CheckBoxText, nil, Language.ShenShou.CancelText)

		return
	end

    ShenShouWGCtrl.Instance:OnCSShenshouDecompos(double_flag, #selected_cells, selected_cells)
end

function ShenShouDeComposeView:OnClickTargetIconBtn()
	local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()
	if other_cfg and other_cfg.spar_id then
		TipWGCtrl.Instance:OpenItem({item_id = other_cfg.spar_id})
	end
end

function ShenShouDeComposeView:OnTunShiToggleClick()
	local flag = ShenShouWGCtrl.Instance:GetIsAutoTunShi()

	if flag then
		ShenShouWGCtrl.Instance:SetIsAutoTunShi()
		self:SetAutoTunShiSelect()
	else
		if not self.auto_tunshi_alert then
			local alert = Alert.New()
			alert:SetOkFunc(function ()
				ShenShouWGCtrl.Instance:SetIsAutoTunShi()
				self:SetAutoTunShiSelect()
				ShenShouWGCtrl.Instance:AutoTunShiEquip()
			end)
			alert:SetCancelString(Language.ShenShou.SoulRingEquipNotResolveDesc)
			alert:SetOkString(Language.ShenShou.SoulRingEquipResolveBtnDesc)
			alert:SetLableString(Language.ShenShou.SoulRingEquipAutoTunShiTxt)
			self.auto_tunshi_alert = alert
		end

		self.auto_tunshi_alert:Open()
	end
end

function ShenShouDeComposeView:SetAutoTunShiSelect()
	local flag = ShenShouWGCtrl.Instance:GetIsAutoTunShi()
	self.node_list.image_tunshi_slt:CustomSetActive(flag)
end

-----------------------------ShenShouDeComposeBagCell-------------------------------
ShenShouDeComposeBagCell = ShenShouDeComposeBagCell or BaseClass(ShenShouEquipCell)
function ShenShouDeComposeBagCell:__init()
	--self:NeedDefaultEff(false)
	self:UseNewSelectEffect(true)
	--self:SetFlagIcon(false)
end

function ShenShouDeComposeBagCell:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return
	end

	ShenShouEquipCell.SetSelectEffect(self, is_select)
end

----------------------------------------------ShenShouDeComposePinZhiListRender------------------------------------------
ShenShouDeComposePinZhiListRender = ShenShouDeComposePinZhiListRender or BaseClass(BaseRender)
function ShenShouDeComposePinZhiListRender:OnFlush()
	self.node_list.lbl_pinjie_name.text.text = self.data
	self.node_list.select_pinjie_bg:SetActive(self.is_select)
end

function ShenShouDeComposePinZhiListRender:OnSelectChange(is_select)
	if self.node_list.select_pinjie_bg then
		self.node_list.select_pinjie_bg:SetActive(is_select)
	end
end