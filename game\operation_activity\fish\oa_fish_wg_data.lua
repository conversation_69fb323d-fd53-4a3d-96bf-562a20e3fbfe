OAFishWGData = OAFishWGData or BaseClass()

OAFishWGData.REWARD_TYPE = {
	BIG = 1,
	NORMAL = 2,
	LOW = 3
}
-- 显示标识
OAFishWGData.SHOW_REWARD_TYPE = 1
OAFishWGData.Mark_Tween_Str = "OAFish_Tween_Name"
function OAFishWGData:__init()
	if OAFishWGData.Instance then
		ErrorLog("[OAFishWGData] Attemp to create a singleton twice !")
	end
	OAFishWGData.Instance = self

	self.record = {}

	self:LoadCfg()
	self.act_change = GlobalEventSystem:Bind(OPERATION_ACTIVITY.ACT_STATE_CHANGE, BindTool.Bind(self.ClearActCache, self))
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_FISH, {[1] = OPERATION_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetOAFishIsOpen, self),BindTool.Bind(self.IsShowFishRedPoint, self))
	RemindManager.Instance:Register(RemindName.OAFish, BindTool.Bind(self.IsShowFishRedPoint, self))
end

function OAFishWGData:LoadCfg()
	self.act_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_luck_charm_auto")
	self.grade_cfg = ListToMapList(self.act_cfg.grade, "grade")
	self.consume_cfg = ListToMapList(self.act_cfg.consume, "consume")
	self.reward_cfg = ListToMapList(self.act_cfg.reward, "reward")
	self.reward_cfg2 = ListToMap(self.act_cfg.reward, "reward", "reward_id")
	self.rebate_cfg = ListToMapList(self.act_cfg.rebate, "rebate")
	self.interface_cfg = ListToMap(self.act_cfg.interface, "interface")
	self.role_sp_guarantee_cfg = ListToMapList(self.act_cfg.role_sp_guarantee, "reward")
	self.turntable_cfg = self.act_cfg.show_reward
	self:RegisterRewardRemindInBag(RemindName.OAFish)
end

function OAFishWGData:__delete()
	if self.act_change then
		GlobalEventSystem:UnBind(self.act_change)
		self.act_change = nil
	end

	RemindManager.Instance:UnRegister(RemindName.OAFish)

	OAFishWGData.Instance = nil
end

function OAFishWGData:ClearActCache(act_type)
	if act_type ~= ACTIVITY_TYPE.OPERA_ACT_FISH then
		return
	end

	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt("oa_fish_str" .. role_id, 0)
	self.act_base_cfg = nil
	self.draw_num = nil
end

function OAFishWGData:GetRewardCfgList(reward)
	return self.reward_cfg and self.reward_cfg[reward]
end

-- 获取奖池配置
function OAFishWGData:GetRewardCfg(reward,reward_id)
	return self.reward_cfg2[reward] and self.reward_cfg2[reward][reward_id]
end

--已经抽到的奖励
function OAFishWGData:SaveDrawInfo(protocol)
    self.had_draw_list = protocol.reward_info
end

-- 获取奖池概率列表
function OAFishWGData:GetFishGaiLvList()
	local data_list = {}
	local grade_cfg = self:GetCurActGradeCfg()
	if not grade_cfg then
		return data_list
	end
	local reward_cfg_list = self:GetRewardCfgList(grade_cfg.reward)
	if not reward_cfg_list then
		return data_list
	end
	for i, v in ipairs(reward_cfg_list) do
		local data = {number = v.reward_id, item_id = v.reward_item.item_id, random_count = v.rewrad_rare_show}
		data_list[i - 1] = data
	end
	return data_list
end

-- 获取奖池所有奖励预览
function OAFishWGData:GetFishRewardPreviewList()
	local reward_list = {}
	local grade_cfg = self:GetCurActGradeCfg()
	if not grade_cfg then
		return reward_list
	end
	local reward_cfg_list = self:GetRewardCfgList(grade_cfg.reward)
	if not reward_cfg_list then
		return reward_list
	end
	for i, v in ipairs(reward_cfg_list) do
		table.insert(reward_list, v.reward_item)
	end
	return reward_list
end

-- -- 获取当前奖池展示的保底物品
-- function OAFishWGData:GetGuaranteeList(reward)
-- 	local data_list = {}
-- 	if not reward then 
-- 		return data_list
-- 	end

-- 	local list = self.role_sp_guarantee_cfg[reward] or {}
-- 	print_error(reward, #list,list)
-- 	for i,v in ipairs(list) do
-- 		if v.show_icon == 1 then
-- 			local reward_cfg = self:GetRewardCfg(v.reward, v.reward_id)
-- 			if reward_cfg then
-- 				table.insert(data_list, reward_cfg)
-- 			end
-- 		end
--     end

--     local temp_list = {}
--     local temp_count_list = {}
--     for k, v in pairs(data_list) do
--         local temp = {}
--         temp.reward_item = v.reward_item
--         temp.count = v.guarantee_reward_limit
--         if not IsEmptyTable(self.had_draw_list) then
--             for k1, v1 in pairs(self.had_draw_list) do
--                 if v.reward_id == v1.reward_id then
--                     temp.count = v.guarantee_reward_limit - v1.get_count 
--                 end
--             end
--         end
--         table.insert(temp_count_list, temp) 
--     end
--     for k2, v2 in pairs(temp_count_list) do
--         if v2.count > 0 then
--             table.insert(temp_list, v2.reward_item)
--         end
--     end
--     temp_list = self:SortDataList(temp_list)
-- 	return temp_list 
-- end

-- 获取当前奖池展示的保底物品
-- 2022/01/08 策划雪威要求奖励展示 去掉reward_id筛选  直接拿奖池对应 reward 的 reward_type为 1 和 2 的所有奖励  并且去掉数量判断
function OAFishWGData:GetGuaranteeList(reward)
	local data_list = {}
	if not reward then 
		return data_list
	end

	local all_list =  self.reward_cfg2[reward]
	for i,v in ipairs(all_list) do
		if v.reward_type < 3 then
			table.insert(data_list, v.reward_item)
		end
    end

	data_list = self:SortDataList(data_list)
	return data_list 
end

--珍稀外观使用类型
local user_type_tb ={
    [13] = true, --骑宠
    [18] = true, --化形类
}

function OAFishWGData:SortDataList(data_list)
	if data_list and not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
            if v.item_id and v.item_id > 0  then
                local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
                local use_type = item_cfg and item_cfg.use_type
                if use_type and user_type_tb[use_type] then
                    v.is_zhenxi = 1
                else
                    v.is_zhenxi = 0
                end
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
                v.is_equip = 0
	            if big_type and big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
                    v.is_equip = 1
                    v.sort_star = v.param and v.param.star_level or 0
                else
                    v.sort_star = 0
                end
                v.color = item_cfg and item_cfg.color or 1
                v.order = item_cfg.order or 0
            else
                v.is_zhenxi = 0
                v.is_equip = 0
                v.color = 0
                v.sort_star = 0
                v.order = 0
            end
		end
		table.sort(data_list, SortTools.KeyUpperSorters( "is_zhenxi", "color","is_equip","sort_star","order"))
    end
    return data_list
end

-- 获取保底次数
function OAFishWGData:GetGuaranteeListCount(reward)
	if not reward then
		return 0
	end

	local num = self.role_sp_guarantee_cfg[reward] and #self.role_sp_guarantee_cfg[reward] or 0
	return num
end

function OAFishWGData:GetCurActTimeCfg()
    local act_base_cfg
    local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_FISH)
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_FISH)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
    for i, v in pairs(self.act_cfg.config_param) do
        if open_day >= v.start_server_day and open_day < v.end_server_day and week == v.week_index then
            act_base_cfg = v
            break
        end
    end
	
	return act_base_cfg
end

function OAFishWGData:GetOAFishIsOpen()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_FISH) then
		return false
	end

	local act_cfg = self:GetCurActTimeCfg()
	if act_cfg == nil then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = act_cfg.open_level
	return role_level >= need_level
end

function OAFishWGData:IsShowFishRedPoint()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_FISH) then
		return 0
	end

	local act_cfg = self:GetCurActTimeCfg()
	if act_cfg == nil then
		return 0
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = act_cfg.open_level
	if role_level < need_level then
		return 0
	end

	if self:GetFanliCanReceive() then
		return 1
	end

	local grade_cfg = self:GetCurActGradeCfg()
	if not grade_cfg then
		return 0
	end

    local sp_guarantee_x,sp_guarantee_n = OAFishWGData.Instance:GetSpGuarantee()
	if grade_cfg.sp_guarantee_finish == 1 and grade_cfg.sp_guarantee_n > 0 and
		sp_guarantee_n >= grade_cfg.sp_guarantee_n then
		return 0
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.consume_item)
	local consume_cfg = self:GetCurConsumeCfg(grade_cfg)
	if consume_cfg[1] then
		if has_num >= consume_cfg[1].consume_count then
			return 1
		end
		--2020/04/25 22:39 策划需求去掉钱够显示红点
		-- if RoleWGData.Instance:GetRoleVo().gold >= grade_cfg.complement_num * consume_cfg[1].consume_count then
		-- 	return 1
		-- end
	else
		return 0
	end

	return 0
end

function OAFishWGData:RegisterRewardRemindInBag(remind_name)
    local map = {}

    local pool_cfg = self.act_cfg.grade
    for _, cfg in pairs(pool_cfg) do
        map[cfg.consume_item] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end
---------------------------------------------------------------------------------------------
function OAFishWGData:GetCurActGradeCfg()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_FISH)
	if act_info == nil then
		return nil ,nil, nil
	end

	if act_info.status ~= ACTIVITY_STATUS.OPEN then
		return nil ,nil ,nil
	end

	local base_cfg = self:GetCurActTimeCfg()
	if not base_cfg then
		return nil, nil, nil
	end

	local cfg = self.grade_cfg[base_cfg.grade]
	if not cfg then
		return nil, nil, nil
	end

	-- 当前活动开启时长
	local time = TimeWGCtrl.Instance:GetServerTime() - act_info.start_time
	local grade_cfg
	-- 当前周期结束时间
	local act_time = 0
	for i, v in ipairs(cfg) do
		act_time = act_time + v.cycle_duration * 3600
		if act_time > time then
			grade_cfg = v
			break
		end
	end
	if not grade_cfg then
		return nil, nil, nil
	end

	-- 当前周期倒计时计算
	local has_next = #cfg > grade_cfg.cycle
	if not has_next then
		act_time = act_info.end_time 
	else
		act_time = act_time + act_info.start_time
	end

	return grade_cfg, has_next, act_time 
end



function OAFishWGData:GetCurConsumeCfg(grade_cfg)
	grade_cfg = grade_cfg or self:GetCurActGradeCfg()
	if not grade_cfg then
		return nil
	end

	local consume_cfg = self.consume_cfg[grade_cfg.consume]
	return consume_cfg
end

function OAFishWGData:GetFanliList(grade_cfg)
	grade_cfg = grade_cfg or self:GetCurActGradeCfg()
	if not grade_cfg then
		return {}
	end

	local cfg = self.rebate_cfg[grade_cfg.rebate] or {}
    local draw_num = self:GetCurDrawNum()
	local fanli_list = {}
	local has_receive
	local last_count = #cfg
	local has_index_list = {}
	local t = {}
	for i, v in ipairs(cfg) do
		has_receive = self:GetFanliHasReceive(v.index)
		if (not has_receive and draw_num >= v.lotto_num) then--同一个界面上最多显示5个
			t = {}
			t.cfg = v
			t.has_receive = has_receive
			has_index_list[v.index] = true
			table.insert(fanli_list, t)
		end

		if #fanli_list >= OperationActivityView.FISH_FANLI_SHOW_NUM then
			break
		end
	end

	if #fanli_list < OperationActivityView.FISH_FANLI_SHOW_NUM then
		for i, v in ipairs(cfg) do
			if draw_num < v.lotto_num then--同一个界面上最多显示5个
				has_receive = self:GetFanliHasReceive(v.index)
				t = {}
				t.cfg = v
				t.has_receive = has_receive
				has_index_list[v.index] = true
				table.insert(fanli_list, t)
			end

			if #fanli_list >= OperationActivityView.FISH_FANLI_SHOW_NUM then
				break
			end
		end
	end

	if #fanli_list < OperationActivityView.FISH_FANLI_SHOW_NUM then
		for i=#cfg,1,-1 do
			if not has_index_list[cfg[i].index] then
				has_receive = self:GetFanliHasReceive(cfg[i].index)
				t = {}
				t.cfg = cfg[i]
				t.has_receive = has_receive
				has_index_list[cfg[i].index] = true
				table.insert(fanli_list, t)
			end
			if #fanli_list >= OperationActivityView.FISH_FANLI_SHOW_NUM then
				break
			end
		end
	end

	return fanli_list
end

function OAFishWGData:GetFanliHasReceive(index)
	index = index - 1
	if not self.fanli_flag then
		return false
	end
	return self.fanli_flag[index] and self.fanli_flag[index] == 1 
end

function OAFishWGData:GetFanliCanReceive()
	local grade_cfg = self:GetCurActGradeCfg()
    local draw_num = self:GetCurDrawNum()
	if not grade_cfg then
		return false
	end

	local cfg = self.rebate_cfg[grade_cfg.rebate] or {}
	local has_receive
	for i, v in ipairs(cfg) do
		has_receive = self:GetFanliHasReceive(v.index)
		if not has_receive and draw_num >= v.lotto_num then
			return true
		end
	end

	return false
end

function OAFishWGData:GetSortFanliList()
	local grade_cfg = self:GetCurActGradeCfg()
    local draw_num = self:GetCurDrawNum()
	if not grade_cfg then
		return nil
	end

	local cfg = self.rebate_cfg[grade_cfg.rebate] or {}
	local has_receive
	local count = 0
	local pos
	local yilingqu_list = {}
	local kelingqu_list = {}
	local bukelingqu_list = {}

	for i, v in ipairs(cfg) do
		has_receive = self:GetFanliHasReceive(v.index)
		-- 已领取
		if has_receive then
			table.insert(yilingqu_list,v)
		-- 可领取
		elseif not has_receive and draw_num >= v.lotto_num then
			table.insert(kelingqu_list,v)
		-- 不可领取
		else
			table.insert(bukelingqu_list,v)
		end
	end

	for i,v in ipairs(bukelingqu_list) do
		kelingqu_list[#kelingqu_list + 1] = v
	end

	for i,v in ipairs(yilingqu_list) do
		kelingqu_list[#kelingqu_list + 1] = v
	end

	return kelingqu_list
end

function OAFishWGData:GetCellList(grade_cfg)
	local mid_list = {}
	local both_list = {}

	grade_cfg = grade_cfg or self:GetCurActGradeCfg()
	if not grade_cfg then
		return mid_list, both_list
	end

	local cfg = self.reward_cfg[grade_cfg.reward] or {}
	for i, v in pairs(cfg) do
		if v.reward_show == OAFishWGData.SHOW_REWARD_TYPE then
			if v.reward_type ~= OAFishWGData.REWARD_TYPE.LOW then
                table.insert(mid_list, v)
            end
            table.insert(both_list, v)
		end
    end
    local both_list2 = {}
    local total = table.getn(both_list)
    while total > 0 do
        local i = math.random(1, total)
        table.insert(both_list2, both_list[i])
        table.remove(both_list, i)
        total = total - 1
    end
    table.sort(mid_list, function(a, b)
        local item_cfg1, item_cfg2
        if a and a.reward_item and a.reward_item.item_id then
            item_cfg1 = ItemWGData.Instance:GetItemConfig(a.reward_item.item_id)
        end
        if b and b.reward_item and b.reward_item.item_id then
            item_cfg2 = ItemWGData.Instance:GetItemConfig(b.reward_item.item_id)
        end
        local color1 = item_cfg1 and item_cfg1.color or 0
        local color2 = item_cfg2 and item_cfg2.color or 0
        return color1 > color2
    end)
	return mid_list, both_list2
end

function OAFishWGData:GetCellTweenTime()
	return 7 --self.act_cfg.other[1].cell_tween or
end

function OAFishWGData:GetDrawFishAnimTime()
	return 2.5 --self.act_cfg.other[1].cell_tween or
end

function OAFishWGData:GetNoticeDelayTime()
	local time
    local no_tween = self:GetNoTweenFlag() == 1
	if no_tween then
		time = 0
	else
		time = self:GetDrawFishAnimTime()
	end
	return time
end

function OAFishWGData:GetCurInterfaceCfg(grade_cfg)
	grade_cfg = grade_cfg or self:GetCurActGradeCfg()
	if not grade_cfg then
		return nil
	end

	return self.interface_cfg[grade_cfg.interface] or self.interface_cfg[0]
end

function OAFishWGData:SetNewRecordCount(count)
	self.new_record_count = count
end

function OAFishWGData:GetNewRecordCount()
	return self.new_record_count or 0
end

function OAFishWGData:UpdateRecordCount()
	self.near_record_time = TimeWGCtrl.Instance:GetServerTime()
	RoleWGData.SetRolePlayerPrefsInt("fish_record_time", self.near_record_time)
	self:SetNewRecordCount(0)
end

function OAFishWGData:SetDrawInfo(protocol)
	self.fanli_flag = bit:ll2b_two(protocol.leiji_reward_fetch_high_flag, protocol.leiji_reward_fetch_low_flag) 
	-- print_error('self.fanli_flag',protocol.leiji_reward_fetch_high_flag, protocol.leiji_reward_fetch_low_flag,self.fanli_flag)
	self.draw_num = protocol.person_draw_count
	self.cur_cycle = protocol.cur_cycle
	self.is_skip_comic = protocol.is_skip_comic
    self.sp_guarantee_x = protocol.sp_guarantee_x
	self.sp_guarantee_n = protocol.sp_guarantee_n
	self.sp_enter_num = protocol.sp_enter_num
end

function OAFishWGData:GetSpGuarantee()
	return self.sp_guarantee_x or 0 , self.sp_guarantee_n or 0, self.sp_enter_num or 0
end

function OAFishWGData:GetNoTweenFlag()
	return self.is_skip_comic or 0
end

function OAFishWGData:GetCurDrawNum()
	return self.draw_num or -1
end

function OAFishWGData:SetRecordInfo(protocol)
	self.record = protocol.record_list
end

function OAFishWGData:GetRecordInfo()
	local record_list = self.record or {}
	local record_show = {}
	for i,v in ipairs(record_list) do
		table.insert(record_show,1,v)
	end

	return record_show
end

function OAFishWGData:GetDrawRecord()
    local record_data = {}

    if not IsEmptyTable(self.record) then
        for k, v in pairs(self.record) do
            record_data[k] = {}
            record_data[k].item_data = {}
            record_data[k].item_data.item_id = v.item_id
            record_data[k].item_data.num = v.num
            record_data[k].consume_time = v.draw_time
            record_data[k].role_name = v.role_name
        end
    end

    if not IsEmptyTable(record_data) then
        table.sort(record_data, SortTools.KeyUpperSorter("consume_time"))
    end

    return record_data
end

function OAFishWGData:CalNewRecordNum()
	self.near_record_time = RoleWGData.GetRolePlayerPrefsInt("fish_record_time")
	local count = 0
	for i, v in pairs(self.record) do
		if v.draw_time > self.near_record_time then
			count = count + 1
		end
	end
	self:SetNewRecordCount(count)
end

function OAFishWGData:CalDrawRewardList(protocol)
	protocol = protocol or self.draw_reward_protocal
	self.draw_reward_protocal = protocol

	local data_list = {}
	local fish_num_list = {0,0,0}
	if not protocol or not protocol.count or protocol.count <= 0 then
		return data_list, fish_num_list
	end

    local zhenxi_item = nil
	for i,v in ipairs(protocol.reward_list) do
		local cfg = self:GetRewardCfg(v.reward_pool_id, v.reward_id)
        if cfg then
            local temp = {}
            temp.is_zhenxi = v.is_zhenxi
            temp.reward = cfg.reward
            temp.reward_item = cfg.reward_item
            temp.reward_type = cfg.reward_type
            temp.rewrad_rare_show = cfg.rewrad_rare_show
            if temp.is_zhenxi and protocol.count == 50 then
                zhenxi_item = temp
            else
                table.insert(data_list, temp)
            end
			fish_num_list[cfg.reward_type] = fish_num_list[cfg.reward_type] + 1
		else
			print_error("错误数据 请检查奖励配置 reward_pool_id,reward_id: ", v.reward_pool_id, v.reward_id)
		end
    end
    local temp_data_list
    if zhenxi_item then
        local zhenxi_index
        temp_data_list = {}
        if protocol.count == 50 then
            zhenxi_index = math.random(22, 28)
        end
        for k, v in ipairs(data_list) do
            if k < zhenxi_index then
                temp_data_list[k] = v
            else
                if k == zhenxi_index then
                    temp_data_list[zhenxi_index] = zhenxi_item
                    temp_data_list[k+1] = v
                else
                    temp_data_list[k+1] = v
                end
            end
        end
    end

	return temp_data_list or data_list, fish_num_list
end

function OAFishWGData:GetShowCalDrawRewardList(item_list)
	local zhenxi_list, reward_list = {}, {}
	for i, v in ipairs(item_list) do
		if v.is_zhenxi then
			table.insert(zhenxi_list, v)
		else
			table.insert(reward_list, v)
		end
	end

	return zhenxi_list, reward_list
end

function OAFishWGData:CacheOrGetFishDrawCfg(grade_cfg)
	if grade_cfg then
		self.cache_draw_grade_cfg = grade_cfg
	end

	return self.cache_draw_grade_cfg
end

function OAFishWGData:CacheOrGetFishDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

function OAFishWGData:GetItemsProbility()
    local str, temp_str = "", ""
    local item_cfg
    local item_list = self:GetDataList()
    if not IsEmptyTable(item_list) then
        for k, v in pairs(item_list) do
            item_cfg = ItemWGData.Instance:GetItemConfig(v.reward_item.item_id)
            if item_cfg then
                temp_str = ToColorStr(item_cfg.name..": "..v.rewrad_rare_show * 100 .."%", ITEM_COLOR[item_cfg.color])
                str = str.. "\n" ..temp_str
            end
        end
    end
    return str
end

function OAFishWGData:GetDataList(grade_cfg)
	local data_list = {}
	grade_cfg = grade_cfg or self:GetCurActGradeCfg()
	if not grade_cfg then
		return data_list
	end

	local cfg = self.reward_cfg[grade_cfg.reward] or {}
	for i, v in pairs(cfg) do
		if v.reward_show == OAFishWGData.SHOW_REWARD_TYPE then
			table.insert(data_list, v)
		end
    end
    table.sort(data_list, function(a, b)
        if a.rewrad_rare_show ~= b.rewrad_rare_show then
            return a.rewrad_rare_show < b.rewrad_rare_show
        end
        local item_cfg1, item_cfg2
        if a and a.reward_item and a.reward_item.item_id then
            item_cfg1 = ItemWGData.Instance:GetItemConfig(a.reward_item.item_id)
        end
        if b and b.reward_item and b.reward_item.item_id then
            item_cfg2 = ItemWGData.Instance:GetItemConfig(b.reward_item.item_id)
        end
        local color1 = item_cfg1 and item_cfg1.color or 0
        local color2 = item_cfg2 and item_cfg2.color or 0
        return color1 > color2
    end)
	return data_list
end

function OAFishWGData:GetTurnTableReward()
	return self.turntable_cfg
end