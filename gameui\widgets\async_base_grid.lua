local flush_type =
{
	refresh = 1,
	refresh_all_act_cells = 2,
	update_one_cell = 3,
	update_select_state = 4,
}

local flush_type_list = {}
for k,v in pairs(flush_type) do
	flush_type_list[v] = k
end

AsyncBaseGrid = AsyncBaseGrid or BaseClass()
function AsyncBaseGrid:__init()
	self.select_callback = nil						-- 选中某个格子回调
	self.item_render = nil							-- 创建的item类型
	self.is_show_tips = nil							-- 是否显示tips

	-- self.is_set_open_count = false					-- 是否设置了开启格子数
	-- self.max_open_index = 0							-- 已开启的最大格子索引（物品格子专用）

	self.grid_name = ""								-- 格子名称
	self.cell_data_list = {}						-- 格子数据列表
	self.create_callback = nil						-- 创建完成回调
	self.max_cell_index = 0							-- 最大索引
	self.is_multi_select = false 					-- 是否多选
	------------

	self.cell_list = {}
	self.first_time_load = true						-- 是否第一次加载
	self.start_zero = true 							-- 是否从0开始
	self.select_tab = {[1] = {}} 					-- 选择项保存表
	self.cur_index = nil 							-- 当前选择的格子
	self.change_cells_num = 0						-- 是否固定格子数
	self.min_row = nil 								-- 最少行数
	self.max_cell = nil 							-- 最多格子
	self.asset_bundle = nil
	self.asset_name = nil
	self.has_data_max_index = 0						--最大有数据的格子索引
	self.is_no_data_hide = false  					--没有数据时是否隐藏空格子
	self.flush_param_t = {}
	self.cur_multi_select_num = 0					-- 多选数量
	self.complement_col_item = false				-- 补足横向格子数据
	self.is_can_deselect = false					-- 是否可以取消选择
end

function AsyncBaseGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
end

-- 设置数据源
function AsyncBaseGrid:SetDataList(data)
	self.cell_data_list = data
	self:ManageDataList(data)
	self:__Flush(flush_type.refresh, {0, 0})
end

function AsyncBaseGrid:ReloadData(percent)
	self:__Flush(flush_type.refresh, {1, percent})
end

function AsyncBaseGrid:RefreshActiveCellViews()
	self:__Flush(flush_type.refresh_all_act_cells)
end

function AsyncBaseGrid:RefreshSelectCellState()
	self:__DoRefreshSelectState()
end

-- 分配数据
function AsyncBaseGrid:ManageDataList(data, refresh)
	self.has_data_max_index = 0
	if self.change_cells_num == 2 then
		self.data_list = {}
 	else
 		for k, v in pairs(self.data_list) do
			self.data_list[k] = {}
		end
 	end

	for i, v in pairs(data) do
		i = self.start_zero and i or i - 1
		local row = math.floor(i / self.columns)
		local col = i % self.columns
		col = (col == 0) and 1 or (col + 1)
		if not self.data_list[row] then
			self.data_list[row] = {}
		end
		if self.max_cell and self.max_cell < (row * self.columns) + col then
			break
		end
		self.data_list[row][col] = v
		self.has_data_max_index = self.has_data_max_index + 1
	end

	if self.change_cells_num == 1 then
		 self.row = math.ceil((self.start_zero and (#data + 1) or #data) / self.columns)

	elseif self.change_cells_num == 2 or self.change_cells_num == 0 then
		if self.min_row and #self.data_list + 1 > self.min_row then
			self.row = #self.data_list + 1
		else
			self.row = self.min_row and self.min_row or self.row
			for row = 0, (self.row - 1) do
				for col = 1, self.columns do
					if not self.data_list[row] then
						self.data_list[row] = {}
					end

					if self.max_cell and self.max_cell < (row * self.columns) + col then
						break
					else
						if not self.data_list[row][col] then
							self.data_list[row][col] = {}
						end
					end
				end
			end
		end
	end

	-- 补足横向格子数据
	if self.complement_col_item then
		for i = 0, #self.data_list do
			local list = self.data_list[i]
			if #list < self.columns then
				for col_index = #list + 1, self.columns do
					self.data_list[i][col_index] = {}
				end
			end
		end
	end
end

-- 创建网格 {t.cell_count, t.col, t.row, t.itemRender, t.change_cells_num, t.asset_bundle, t.asset_name, t.list_view}
--- t.itemRender 可为nil，默认为ItemCell
-- asset_bundle 和 asset_name 创建自己的预制物（不传默认创建ItemCell）
--[[change_cells_num 可为nil，默认为0
	0(固定格子，格子数为cell_count)
	1(格子数为data长度，可不传cell_count)
	2(有最少格子数，数据超过最少格子数会扩展格子)
--]]
-- complement_col_item 每一行格子会创建满，无数据的格子赋值为空表，可依此做全屏适配格子占位。(空表数据格子添加逻辑隐藏显示内容，保留父节点占位)
function AsyncBaseGrid:CreateCells(t)
	if t.change_cells_num then
		self.change_cells_num = t.change_cells_num
	elseif t.cell_count then
		self.change_cells_num = 0
		self.max_cell = t.cell_count
	end

	self.is_no_data_hide = t.is_no_data_hide

	if t.cell_count then
		self.row = math.ceil(t.cell_count / t.col)
	elseif t.row then
		self.row = t.row
	else
		self.row = 1
	end

	if self.change_cells_num == 2 then
		self.min_row = self.row
	end

	if t.assetBundle and t.assetName then
		self.asset_bundle = t.assetBundle
		self.asset_name = t.assetName
	end

	self.columns = t.col
	self.complement_col_item = t.complement_col_item

	local function get_length_tab(length)
		local length_tab = {}

		for i = 0, (length - 1) do
			local tab = {}
			table.insert(length_tab, i, tab)
		end
		return length_tab
	end
	self.data_list = get_length_tab(self.row)

	self.item_render = t.itemRender or ItemCell
	self.list_view = t.list_view

	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)
		self.first_time_load = false
	end

	if nil ~= self.create_callback then
		self.create_callback()
	end
end

function AsyncBaseGrid:SetIsMultiSelect(is_multi_select)
	self.is_multi_select = is_multi_select
end

-- 数据开始下标是否0开始
function AsyncBaseGrid:SetStartZeroIndex(start_zero)
	self.start_zero = start_zero
end

-- 选中后回调
function AsyncBaseGrid:SetSelectCallBack(callback)
	self.select_callback = callback
end

-- 设置选中某个格子
function AsyncBaseGrid:SetSelectCellIndex(cell_index)
	self.select_tab[1] = {}
	self.select_tab[1][cell_index] = true
	self:__Flush(flush_type.update_select_state)
end

-- 设置选中某个格子 可多选
function AsyncBaseGrid:SetSelectCellIndexMore(cell_index)
	if not self.select_tab[1] then
		self.select_tab[1] = {}
	end
	self.select_tab[1][cell_index] = true
	self:__Flush(flush_type.update_select_state)
end

--获得格子数
function AsyncBaseGrid:GetListViewNumbers()
	return self.row
end

--刷新格子
function AsyncBaseGrid:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	if not item_cell then
		item_cell = BaseGridItemRender.New(cell.gameObject)
		item_cell:SetSelectTab(self.select_tab)
		item_cell:SetAssetBundle(self.asset_bundle, self.asset_name)
		item_cell:SetItemRender(self.item_render)
		item_cell:SetColumns(self.columns)
		item_cell:SetHideEmptyCell(self.is_no_data_hide)
		item_cell:SetClickCallBack(BindTool.Bind1(self.SelectCellHandler, self))
		if nil ~= self.is_show_tips then
			item_cell:SetIsShowTips(self.is_show_tips)
		end
		self.cell_list[cell] = item_cell
	end

	item_cell:SetRows(cell_index)
	item_cell:SetData(self.data_list[cell_index])

	self:__TrySelectIndex(cell_index, item_cell)
end

-- 格子尝试选择
function AsyncBaseGrid:__TrySelectIndex(cell_index, select_cell)
	local is_select_cell_index = false
	if nil ~= self.jump_to_select_cell_index then
		local index = self.jump_to_select_cell_index
		index = self.start_zero and index or (index - 1)
		local row = math.floor(index / self.columns)	-- 行
		is_select_cell_index = cell_index == row
	end

	if not is_select_cell_index then
		return
	end

	local cur_select_index = self.jump_to_select_cell_index
	self.jump_to_select_cell_index = nil
	self:__OnSelectIndex(cur_select_index, select_cell)
end

-- 选中跳转格子
function AsyncBaseGrid:__OnSelectIndex(cell_index, select_cell)
	if nil == cell_index or nil == select_cell or nil == self.select_callback then
		return
	end

	local cell = select_cell:GetCell(cell_index)
	if nil ~= cell then
		self:SelectCellHandler(cell)
	end
end

function AsyncBaseGrid:IsCanDeselect(bool)
	self.is_can_deselect = bool
end

-- 选择某个格子回调
function AsyncBaseGrid:SelectCellHandler(cell)
	local cell_index = cell:GetIndex()
	if self:IsSelectMultiNumLimit(cell_index) then
		return
	end

	self.cur_index = cell_index
	local is_select = false
	if self.is_multi_select then
		if not self.select_tab[1][cell_index] then
			is_select = true
			self.select_tab[1][cell_index] = true
			self.cur_multi_select_num = self.cur_multi_select_num + 1
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil
			self.cur_multi_select_num = self.cur_multi_select_num - 1
		else
			is_select = true
			self.select_tab[1][cell_index] = true
			self.cur_multi_select_num = self.cur_multi_select_num + 1
		end

		cell:SetSelect(is_select, true)
	else
		-- 能改变已选中效果
		if self.is_can_deselect then
			if not self.select_tab[1][cell_index] then
				self.select_tab[1] = {}
				self.select_tab[1][cell_index] = true
				is_select = true
			elseif self.select_tab[1] and self.select_tab[1][cell_index] then
				self.select_tab[1][cell_index] = nil
			end
	
			self:__DoRefreshSelectState()
		else
			if not self.select_tab[1][cell_index] then
				self.select_tab[1] = {}
				self.select_tab[1][cell_index] = true
				is_select = true
				self:__DoRefreshSelectState()
			end
		end
	end

	if nil ~= self.select_callback then
		self.select_callback(cell, is_select)
	end
end

-- 多选数量限制（可重写）
function AsyncBaseGrid:IsSelectMultiNumLimit(cell_index)
	--[[
	if not self.select_tab[1][cell_index] then
		if self.cur_multi_select_num >= 限制数 then
			SysMsgWGCtrl.Instance:ErrorRemind("限制提示")
			return true
		end
	end
	--]]

	return false
end

-- 设置创建完成回调函数(在执行CreateCells之前设置)
function AsyncBaseGrid:SetCreateCallback(create_callback)
	self.create_callback = create_callback
end

-- 刷新所有Cell最后回调 scroll_obj, start_idx, end_idx
function AsyncBaseGrid:SetEndScrolledCallBack(callback)
	if self.list_view then
		self.list_view.scroller.scrollerEndScrolled = callback
	end
end

-- 取消格子的选中状态
function AsyncBaseGrid:CancleAllSelectCell()
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0
	self:__Flush(flush_type.update_select_state)
end

-- 设置全选所有物品
function AsyncBaseGrid:SetAllSelectCell(total_num)
	self.select_tab[1] = {}
	local start_index = self.start_zero and 0 or 1
	for i = start_index, total_num do
		self.select_tab[1][i] = true
	end
	self:__Flush(flush_type.update_select_state)
end

function AsyncBaseGrid:SetIsShowTips(is_show_tips)
	self.is_show_tips = is_show_tips
	for k, v in pairs(self.cell_list) do
		if v.SetIsShowTips then
			v:SetIsShowTips(is_show_tips)
		end
	end
end

-- -- 刷新某个格子里的数据
function AsyncBaseGrid:UpdateOneCell(index, data)
	self:__Flush(flush_type.update_one_cell, {index, data})

	index = self.start_zero and index or (index - 1)
	local row = math.floor(index / self.columns)	--行
	local col = index % self.columns				-- 列
	col = col + 1
	self.data_list[row][col] = data
end

--获取所有高亮格子数据(兼容旧baseGrid代码)
function AsyncBaseGrid:GetAllSelectCell()
	local all_cell_data = {}
	-- for k,v in pairs(self.select_tab[1]) do
	-- 	table.insert(all_cell_data,self.cell_data_list[k])
	-- end
	for k,v in pairs(self.select_tab[1]) do
		if self.start_zero then
			table.insert(all_cell_data,self.cell_data_list[k - 1])
		else
			table.insert(all_cell_data,self.cell_data_list[k])
		end
	end
	return all_cell_data
end

function AsyncBaseGrid:GetCellIsSelect(cell_index)
	return self.select_tab[1][cell_index]
end

-- 当面板显示全部item才用(该方法不可靠，没有特殊情况不要再用)
function AsyncBaseGrid:GetAllCell()
	local all_cell = {}
	for k, v in pairs(self.cell_list) do
		local cell_tab = v:GetAllCell()
		for k, v in pairs(cell_tab) do
			all_cell[v:GetIndex()] = v
		end
	end
	return all_cell
end

-- 获得指定的格子
-- 当面板显示全部item才用(该方法不可靠，没有特殊情况不要再用)
function AsyncBaseGrid:GetCell(cell_index)
	local index = self.start_zero and cell_index or (cell_index - 1)
	local row = math.floor(index / self.columns)	-- 行
	for k, v in pairs(self.cell_list) do
		if v:GetActive() and row == v:GetRows() then
			return v:GetCell(cell_index)
		end		
	end

	return nil
end

--跳转到某个cell 新增
function AsyncBaseGrid:JumpToIndex(index , min_show_len)
	local percent = 0
	local min = min_show_len or 4

	if index > min then
		local len = self:__GetCellLength()
		len = len == 0 and 1 or len
		percent = index / len
		self:__Flush(flush_type.refresh, {1, percent})
	else
		self:__Flush(flush_type.refresh, {1, 0})
	end
end

function AsyncBaseGrid:JumptToPrecent(percent)
	self:__Flush(flush_type.refresh, {1, percent})
end

--跳转到某个cell并且选中
function AsyncBaseGrid:JumpToIndexAndSelect(index , min_show_len, cell_count)
	local percent = 0
	local min = min_show_len or 4
	self.jump_to_select_cell_index = index
	if index > min then
		local len = cell_count or self:__GetCellLength()
		len = len == 0 and 1 or len
		percent = index / len
		self:__Flush(flush_type.refresh, {1, percent})
	else
		self:__Flush(flush_type.refresh, {1, 0})
	end
end

-- 获得数据列表
function AsyncBaseGrid:GetDataList()
	return self.cell_data_list
end

function AsyncBaseGrid:__GetCellLength()
	if not self.cell_data_list then
		return 0
	end
	local length = #self.cell_data_list
	length = self.cell_data_list[0] and (length + 1) or length
	return length
end

function AsyncBaseGrid:__DoRefresh(refresh_type, percent)
	if IsNil(self.list_view.scroller) then
		return
	end
	if self.list_view.scroller.isActiveAndEnabled then
		if refresh_type == 0 then
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		elseif refresh_type == 1 then
			self.list_view.scroller:ReloadData(percent)
		elseif refresh_type == 2 then
			self.list_view.scroller:RefreshActiveCellViews()
		end
	end

	-- print_error("[AsyncBaseGrid]###############__DoRefresh>>>>>>>>>>>>", refresh_type, percent)
	if is_develop then
		self:__CheckRefreshCall()
	end
end

function AsyncBaseGrid:__CheckRefreshCall()
	local now_frame = UnityEngine.Time.frameCount
	self.last_refresh_frame = self.last_refresh_frame or now_frame
	self.refresh_times_in_frame = self.refresh_times_in_frame or 0
	if self.last_refresh_frame ~= UnityEngine.Time.frameCount then
		self.last_refresh_frame = now_frame
		self.refresh_times_in_frame = 1
	else
		self.refresh_times_in_frame = self.refresh_times_in_frame + 1
		if self.refresh_times_in_frame >= 2 then
			print_error(string.format("[AsyncBaseGrid] 在1帧里执行了%s次__DoRefresh，理论上不可能！", self.refresh_times_in_frame))
		end
	end
end

function AsyncBaseGrid:__DoUpdateOneCell(index, data)
	local cell = self:GetCell(index)
	if cell then
		cell:SetData(data)
	end
end

function AsyncBaseGrid:__DoRefreshSelectState()
	for k,v in pairs(self.cell_list) do
		v:RefreshSelectState()
	end
end

function AsyncBaseGrid:__Flush(key, value)
	self.flush_param_t[key] = value
	TryDelayCall(self, function ()
		self:__OnFlush()
	end, 0, "flush")
end

function AsyncBaseGrid:__OnFlush()
	for i,_ in ipairs(flush_type_list) do
		local v = self.flush_param_t[i]
		if nil ~= v then
			if i == flush_type.refresh then
				self:__DoRefresh(v[1], v[2])
				self.flush_param_t[flush_type.refresh_all_act_cells] = nil
				self.flush_param_t[flush_type.update_one_cell] = nil
				self.flush_param_t[flush_type.update_select_state] = nil
			end

			if i == flush_type.refresh_all_act_cells then
				self:__DoRefresh(2, 0)
			end

			if i == flush_type.update_one_cell then
				self:__DoUpdateOneCell(v[1], v[2])
			end

			if i == flush_type.update_select_state then
				self:__DoRefreshSelectState()
			end
		end
	end

	self.flush_param_t = {}
end
