FortunecatDrawRecordView = FortunecatDrawRecordView or BaseClass(SafeBaseView)

function FortunecatDrawRecordView:__init()
	self.view_name = "FortunecatDrawRecordView"
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", { sizeDelta = Vector2(706, 488) })
	self:AddViewResource(0, "uis/view/fortunecat_ui_prefab", "layout_fortune_cat_draw_record_panel")
end

function FortunecatDrawRecordView:ReleaseCallBack()
	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end

	self.world_list = nil
	self.person_list = nil
end

function FortunecatDrawRecordView:LoadCallBack()
	self.toggle_index = 1

	self.node_list.title_view_name.text.text = Language.FortuneCat.RecordTitleStr
	self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 1))
	self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 2))
	self.record_list = AsyncListView.New(FortuneCatRecordCell, self.node_list["role_list"])

	self.node_list.btn_all.toggle.isOn = true
end

function FortunecatDrawRecordView:SetData(world_list, person_list)
	self.world_list = world_list
	self.person_list = person_list
end

function FortunecatDrawRecordView:OnClickSwitch(state, is_on)
	if is_on and state then
		self.toggle_index = state
		self:FlushRecordList()
	end
end

function FortunecatDrawRecordView:OnFlush()
	self:FlushRecordList()
end

function FortunecatDrawRecordView:FlushRecordList()
	local data_list = self.toggle_index == 1 and self.world_list or self.person_list
	local is_show_list = not IsEmptyTable(data_list)
	if is_show_list then
		self.record_list:SetDataList(data_list)
	end
	self.node_list["role_list"]:SetActive(is_show_list)
	self.node_list["no_invite"]:SetActive(not is_show_list)
end

---------------------------------FortuneCatRecordCell---------------------------------
FortuneCatRecordCell = FortuneCatRecordCell or BaseClass(BaseRender)

function FortuneCatRecordCell:__init()
end

function FortuneCatRecordCell:__delete()
end

function FortuneCatRecordCell:OnFlush()
	local index = self:GetIndex()
	local mark = (index % 2) == 1

	if not self.data then
		return
	end

	self.node_list["root_bg"].image.enabled = mark

	local value = self.data.multiple_value / 100
	local str1, num
	local item_name = Language.Common.CangJingScore
	local time = ""
	if self.data.is_world then
		str1 = string.format(Language.FortuneCat.TxtRecord1, self.data.name)
		num = string.format(Language.FortuneCat.TxtRecord2, value)
	else
		time = os.date("%m-%d  %X", self.data.timestamp)

		str1 = Language.FortuneCat.TxtRecord1_2

		local other_cfg = FortuneCatWGData.Instance:GeOtherCfg()
		if other_cfg.show_num == 1 then
			num = string.format(Language.FortuneCat.TxtRecord2_2, value, self.data.gold_value)
		else
			num = string.format(Language.FortuneCat.TxtRecord2, value)
		end
	end

	self.node_list["desc"].text.text = str1
	self.node_list["txt_btn"].text.text = item_name
	self.node_list["num"].text.text = num
	self.node_list["time"].text.text = time
end
