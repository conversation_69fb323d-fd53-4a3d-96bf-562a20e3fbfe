-- Y-运营活动-任务链.xls
local item_table={
[1]={item_id=26355,num=1,is_bind=1},
[2]={item_id=26347,num=1,is_bind=1},
[3]={item_id=26203,num=5,is_bind=1},
[4]={item_id=46044,num=2,is_bind=1},
[5]={item_id=26358,num=1,is_bind=1},
[6]={item_id=26359,num=1,is_bind=1},
[7]={item_id=26409,num=2,is_bind=1},
[8]={item_id=26914,num=5,is_bind=1},
[9]={item_id=29615,num=2,is_bind=1},
[10]={item_id=22012,num=1,is_bind=1},
[11]={item_id=46041,num=1,is_bind=1},
[12]={item_id=26121,num=1,is_bind=1},
[13]={item_id=26191,num=1,is_bind=1},
[14]={item_id=48071,num=2,is_bind=1},
[15]={item_id=26122,num=1,is_bind=1},
[16]={item_id=26415,num=4,is_bind=1},
[17]={item_id=26200,num=5,is_bind=1},
[18]={item_id=27611,num=5,is_bind=1},
[19]={item_id=22011,num=1,is_bind=1},
[20]={item_id=26416,num=5,is_bind=1},
}

return {
server={
{},
{start_server_day=4,end_server_day=10,grade=1,},
{start_server_day=11,end_server_day=17,grade=2,},
{start_server_day=18,end_server_day=24,grade=3,},
{start_server_day=25,end_server_day=31,grade=4,},
{start_server_day=32,end_server_day=38,grade=5,},
{start_server_day=39,end_server_day=45,grade=6,},
{start_server_day=46,end_server_day=52,grade=7,},
{start_server_day=53,end_server_day=59,grade=8,},
{start_server_day=60,end_server_day=66,grade=9,},
{start_server_day=67,end_server_day=73,grade=10,},
{start_server_day=74,end_server_day=999,grade=11,}
},

server_meta_table_map={
},
grade={
{},
{day_index=2,},
{day_index=3,},
{grade=1,task_reward=2,level_reward=2,mingwang_reward=2,},
{day_index=2,},
{day_index=3,},
{grade=2,task_reward=3,level_reward=3,mingwang_reward=3,},
{day_index=2,},
{day_index=3,},
{grade=3,task_reward=4,level_reward=4,mingwang_reward=4,},
{day_index=2,},
{day_index=3,},
{grade=4,task_reward=5,level_reward=5,mingwang_reward=5,},
{day_index=2,},
{day_index=3,},
{grade=5,task_reward=6,level_reward=6,mingwang_reward=6,},
{day_index=2,},
{day_index=3,},
{grade=6,task_reward=7,level_reward=7,mingwang_reward=7,},
{day_index=2,},
{day_index=3,},
{grade=7,task_reward=8,level_reward=8,mingwang_reward=8,},
{day_index=2,},
{day_index=3,},
{grade=8,task_reward=9,level_reward=9,mingwang_reward=9,},
{day_index=2,},
{day_index=3,},
{grade=9,task_reward=10,level_reward=10,mingwang_reward=10,},
{day_index=2,},
{day_index=3,},
{grade=10,task_reward=11,level_reward=11,mingwang_reward=11,},
{day_index=2,},
{day_index=3,},
{grade=11,task_reward=12,level_reward=12,mingwang_reward=12,},
{day_index=2,},
{day_index=3,}
},

grade_meta_table_map={
[23]=22,	-- depth:1
[29]=28,	-- depth:1
[32]=31,	-- depth:1
[27]=25,	-- depth:1
[33]=32,	-- depth:2
[26]=27,	-- depth:2
[24]=23,	-- depth:2
[30]=29,	-- depth:2
[18]=16,	-- depth:1
[20]=19,	-- depth:1
[35]=34,	-- depth:1
[17]=18,	-- depth:2
[15]=13,	-- depth:1
[14]=15,	-- depth:2
[12]=10,	-- depth:1
[11]=12,	-- depth:2
[9]=7,	-- depth:1
[8]=9,	-- depth:2
[6]=4,	-- depth:1
[5]=6,	-- depth:2
[21]=20,	-- depth:2
[36]=35,	-- depth:2
},
task_reward={
{},
{task_num=2,},
{task_num=3,},
{task_reward=2,},
{task_num=2,},
{task_reward=2,},
{task_reward=3,},
{task_num=2,},
{task_reward=3,},
{task_reward=4,},
{task_num=2,},
{task_reward=4,},
{task_reward=5,},
{task_num=2,},
{task_reward=5,},
{task_reward=6,},
{task_reward=6,},
{task_reward=6,},
{task_reward=7,},
{task_reward=7,},
{task_reward=7,},
{task_reward=8,},
{task_reward=8,},
{task_reward=8,},
{task_reward=9,},
{task_num=2,},
{task_reward=9,},
{task_reward=10,},
{task_reward=10,},
{task_reward=10,},
{task_reward=11,},
{task_reward=11,},
{task_reward=11,},
{task_reward=12,},
{task_reward=12,},
{task_reward=12,}
},

task_reward_meta_table_map={
[23]=2,	-- depth:1
[29]=23,	-- depth:2
[33]=3,	-- depth:1
[27]=33,	-- depth:2
[26]=27,	-- depth:3
[24]=27,	-- depth:3
[32]=26,	-- depth:4
[30]=24,	-- depth:4
[18]=30,	-- depth:5
[20]=32,	-- depth:5
[35]=20,	-- depth:6
[17]=35,	-- depth:7
[15]=18,	-- depth:6
[14]=15,	-- depth:7
[12]=15,	-- depth:7
[11]=12,	-- depth:8
[9]=12,	-- depth:8
[8]=9,	-- depth:9
[6]=9,	-- depth:9
[5]=6,	-- depth:10
[21]=6,	-- depth:10
[36]=21,	-- depth:11
},
level_reward={
{},
{level=2,mingwang=180,},
{level=3,mingwang=140,},
{level=4,mingwang=120,},
{level=5,mingwang=100,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=3,},
{task_num=3,},
{task_num=3,},
{task_num=3,},
{task_num=3,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=2,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{level_reward=3,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{level_reward=4,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{task_num=2,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=5,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=6,},
{level_reward=7,},
{level_reward=7,},
{level_reward=7,},
{level_reward=7,},
{level_reward=7,},
{level_reward=7,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=2,},
{task_num=3,},
{level_reward=7,},
{level_reward=7,},
{level_reward=7,},
{level_reward=7,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{task_num=2,},
{task_num=2,},
{level_reward=8,},
{task_num=2,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{level_reward=8,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{task_num=2,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=9,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{task_num=2,},
{task_num=2,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=10,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{task_num=2,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=11,},
{level_reward=12,},
{level_reward=12,},
{level_reward=12,},
{level_reward=12,},
{level_reward=12,},
{level_reward=12,},
{level_reward=12,},
{task_num=2,},
{task_num=2,},
{level_reward=12,},
{level_reward=12,},
{task_num=3,},
{task_num=3,},
{level_reward=12,},
{level_reward=12,}
},

level_reward_meta_table_map={
[171]=6,	-- depth:1
[86]=11,	-- depth:1
[141]=171,	-- depth:2
[41]=86,	-- depth:2
[51]=141,	-- depth:3
[66]=51,	-- depth:4
[161]=41,	-- depth:3
[36]=66,	-- depth:5
[96]=36,	-- depth:6
[101]=96,	-- depth:7
[146]=101,	-- depth:8
[81]=96,	-- depth:7
[56]=146,	-- depth:9
[26]=56,	-- depth:10
[71]=26,	-- depth:11
[176]=71,	-- depth:12
[156]=81,	-- depth:8
[21]=156,	-- depth:9
[111]=21,	-- depth:10
[131]=176,	-- depth:13
[116]=131,	-- depth:14
[126]=131,	-- depth:14
[12]=2,	-- depth:1
[153]=3,	-- depth:1
[80]=5,	-- depth:1
[78]=153,	-- depth:2
[152]=2,	-- depth:1
[77]=152,	-- depth:2
[79]=4,	-- depth:1
[13]=3,	-- depth:1
[94]=79,	-- depth:2
[139]=94,	-- depth:3
[92]=77,	-- depth:3
[93]=78,	-- depth:3
[95]=80,	-- depth:2
[138]=93,	-- depth:4
[137]=92,	-- depth:4
[107]=137,	-- depth:5
[108]=138,	-- depth:5
[109]=139,	-- depth:4
[110]=95,	-- depth:3
[125]=110,	-- depth:4
[124]=109,	-- depth:5
[123]=108,	-- depth:6
[140]=125,	-- depth:5
[65]=140,	-- depth:6
[154]=124,	-- depth:6
[63]=123,	-- depth:7
[167]=107,	-- depth:6
[168]=63,	-- depth:8
[64]=154,	-- depth:7
[170]=65,	-- depth:7
[35]=170,	-- depth:8
[34]=64,	-- depth:8
[33]=168,	-- depth:9
[32]=167,	-- depth:7
[10]=5,	-- depth:1
[20]=35,	-- depth:9
[19]=34,	-- depth:9
[18]=33,	-- depth:10
[17]=32,	-- depth:8
[15]=10,	-- depth:2
[14]=4,	-- depth:1
[9]=14,	-- depth:2
[8]=13,	-- depth:2
[169]=19,	-- depth:10
[122]=17,	-- depth:9
[49]=169,	-- depth:11
[48]=18,	-- depth:11
[47]=122,	-- depth:10
[50]=20,	-- depth:10
[155]=50,	-- depth:11
[7]=12,	-- depth:2
[62]=47,	-- depth:11
[158]=8,	-- depth:3
[174]=169,	-- depth:11
[127]=7,	-- depth:3
[128]=158,	-- depth:4
[129]=174,	-- depth:12
[130]=10,	-- depth:2
[150]=15,	-- depth:3
[178]=168,	-- depth:9
[177]=167,	-- depth:7
[175]=130,	-- depth:3
[157]=127,	-- depth:4
[173]=178,	-- depth:10
[133]=178,	-- depth:10
[132]=177,	-- depth:8
[145]=175,	-- depth:4
[142]=157,	-- depth:5
[163]=133,	-- depth:11
[164]=14,	-- depth:2
[165]=150,	-- depth:4
[162]=132,	-- depth:9
[172]=142,	-- depth:6
[160]=165,	-- depth:5
[147]=162,	-- depth:10
[148]=163,	-- depth:12
[135]=165,	-- depth:5
[149]=164,	-- depth:3
[134]=149,	-- depth:4
[144]=149,	-- depth:4
[159]=144,	-- depth:5
[143]=148,	-- depth:13
[90]=135,	-- depth:6
[119]=134,	-- depth:5
[67]=172,	-- depth:7
[60]=90,	-- depth:7
[59]=119,	-- depth:6
[58]=148,	-- depth:13
[57]=147,	-- depth:11
[55]=60,	-- depth:8
[54]=59,	-- depth:7
[53]=58,	-- depth:14
[52]=57,	-- depth:12
[45]=60,	-- depth:8
[44]=59,	-- depth:7
[43]=58,	-- depth:14
[42]=57,	-- depth:12
[40]=45,	-- depth:9
[39]=44,	-- depth:8
[38]=43,	-- depth:15
[37]=42,	-- depth:13
[30]=45,	-- depth:9
[29]=44,	-- depth:8
[28]=43,	-- depth:15
[27]=42,	-- depth:13
[25]=30,	-- depth:10
[24]=29,	-- depth:9
[23]=28,	-- depth:16
[22]=27,	-- depth:14
[68]=23,	-- depth:17
[120]=30,	-- depth:10
[69]=24,	-- depth:10
[72]=27,	-- depth:14
[118]=28,	-- depth:16
[117]=72,	-- depth:15
[115]=120,	-- depth:11
[114]=69,	-- depth:11
[113]=118,	-- depth:17
[112]=117,	-- depth:16
[105]=120,	-- depth:11
[104]=29,	-- depth:9
[103]=118,	-- depth:17
[102]=117,	-- depth:16
[100]=105,	-- depth:12
[99]=104,	-- depth:10
[98]=103,	-- depth:18
[97]=102,	-- depth:17
[179]=104,	-- depth:10
[89]=179,	-- depth:11
[88]=103,	-- depth:18
[87]=102,	-- depth:17
[85]=100,	-- depth:13
[84]=89,	-- depth:12
[83]=88,	-- depth:19
[82]=87,	-- depth:18
[75]=105,	-- depth:12
[74]=89,	-- depth:12
[73]=88,	-- depth:19
[70]=75,	-- depth:13
[180]=75,	-- depth:13
},
mingwang_reward={
{},
{mingwang=120,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},},
{mingwang=200,reward_item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8]},},
{mingwang=280,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},},
{mingwang=360,reward_item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16]},},
{mingwang_reward=2,},
{mingwang_reward=2,},
{mingwang_reward=2,},
{mingwang_reward=2,},
{mingwang_reward=2,},
{mingwang_reward=3,},
{mingwang_reward=3,},
{mingwang_reward=3,},
{mingwang_reward=3,},
{mingwang_reward=3,},
{mingwang_reward=4,},
{mingwang_reward=4,},
{mingwang_reward=4,},
{mingwang_reward=4,},
{mingwang_reward=4,},
{mingwang_reward=5,},
{mingwang_reward=5,},
{mingwang_reward=5,},
{mingwang_reward=5,},
{mingwang_reward=5,},
{mingwang_reward=6,},
{mingwang_reward=6,},
{mingwang_reward=6,},
{mingwang_reward=6,},
{mingwang_reward=6,},
{mingwang_reward=7,},
{mingwang_reward=7,},
{mingwang_reward=7,},
{mingwang_reward=7,},
{mingwang_reward=7,},
{mingwang_reward=8,},
{mingwang_reward=8,},
{mingwang_reward=8,},
{mingwang_reward=8,},
{mingwang_reward=8,},
{mingwang_reward=9,},
{mingwang_reward=9,},
{mingwang_reward=9,},
{mingwang_reward=9,},
{mingwang_reward=9,},
{mingwang_reward=10,},
{mingwang_reward=10,},
{mingwang_reward=10,},
{mingwang_reward=10,},
{mingwang_reward=10,},
{mingwang_reward=11,},
{mingwang_reward=11,},
{mingwang_reward=11,},
{mingwang_reward=11,},
{mingwang_reward=11,},
{mingwang_reward=12,},
{mingwang_reward=12,},
{mingwang_reward=12,},
{mingwang_reward=12,},
{mingwang_reward=12,}
},

mingwang_reward_meta_table_map={
[37]=2,	-- depth:1
[38]=3,	-- depth:1
[54]=4,	-- depth:1
[39]=54,	-- depth:2
[40]=5,	-- depth:1
[53]=38,	-- depth:2
[42]=37,	-- depth:2
[52]=42,	-- depth:3
[43]=53,	-- depth:3
[44]=39,	-- depth:3
[45]=40,	-- depth:2
[55]=45,	-- depth:3
[57]=52,	-- depth:4
[47]=57,	-- depth:5
[48]=43,	-- depth:4
[49]=44,	-- depth:4
[58]=48,	-- depth:5
[50]=55,	-- depth:4
[30]=50,	-- depth:5
[34]=49,	-- depth:5
[7]=47,	-- depth:6
[8]=58,	-- depth:6
[9]=34,	-- depth:6
[10]=30,	-- depth:6
[12]=7,	-- depth:7
[13]=8,	-- depth:7
[14]=9,	-- depth:7
[15]=10,	-- depth:7
[17]=12,	-- depth:8
[18]=13,	-- depth:8
[35]=15,	-- depth:8
[19]=14,	-- depth:8
[22]=17,	-- depth:9
[23]=18,	-- depth:9
[24]=19,	-- depth:9
[25]=35,	-- depth:9
[27]=22,	-- depth:10
[28]=23,	-- depth:10
[29]=24,	-- depth:10
[59]=29,	-- depth:11
[32]=27,	-- depth:11
[33]=28,	-- depth:11
[20]=25,	-- depth:10
[60]=20,	-- depth:11
},
gameplay={
{task_chain_id=1,},
{day_index=2,week_index=2,},
{day_index=3,week_index=3,task_chain_id=3,},
{day_index=4,week_index=4,},
{day_index=5,week_index=5,},
{day_index=6,week_index=6,},
{day_index=7,week_index=7,}
},

gameplay_meta_table_map={
[5]=1,	-- depth:1
[7]=3,	-- depth:1
},
task_chain={
{ts_appe_image_id=10020,},
{task_chain_id=2,task_ids="4|5|6",scene_id=114,born_pos="519|177",pic_1="task_chain_act_bg_2",pic_2="task_chain_act_desc_2",title_1="哪吒闹海",title_2="闹海入口",btn_str="降服魔龙",task_chain_name="哪吒闹海",desc="龙太子敖丙追求大道途中走火坠魔，为祸一方，天帝派遣太乙真人及其徒弟哪吒前往唤醒其神志！帮助哪吒击败魔龙敖丙！",tips1="每日14:00.16:30可前往降龙",res_id=6047001,npc_view_pos="-304|0",npc_name="哪吒",ready_name="闹海准备",end_content="已成功击败魔龙敖丙了，感谢各位仙友的帮助！",act_notice="哪吒闹海降服魔龙",},
{task_chain_id=3,task_ids="7|8|9",scene_id=118,born_pos="363|343",pic_1="task_chain_act_bg_3",pic_2="task_chain_act_desc_3",title_1="嫦娥奔月",title_2="奔月入口",btn_str="保护嫦娥",task_chain_name="嫦娥奔月",desc="嫦娥受逄蒙蒙蔽，被骗取飞仙丹，封印法力，后羿大战逄蒙无法脱身！协助嫦娥寻回仙丹，快行动起来吧！",tips1="每日14:00.16:30可前往奔月",res_id=6123001,npc_view_pos="-275|20",npc_name="嫦娥",ready_name="奔月准备",end_content="嫦娥已成功寻回仙丹了，感谢各位仙友的帮助！",act_notice="嫦娥奔月击败逄蒙",}
},

task_chain_meta_table_map={
},
task={
{task_type=5,continue_time=180,level_desc="运输评分",scene_type=138,},
{task_id=2,task_type=6,task_name="守护紫霞",icon="taskicon_2",story_content="<color=#95d12b>奇遇背景：</color>求仙者们保护紫霞脱离途中，遭遇牛魔追兵，受困牛魔势力范围\n<color=#95d12b>奇遇目标：</color>抵御追兵，守护紫霞并击退牛魔下属，莫让至尊宝追悔万年！\n\n",task_desc="守护紫霞，击败牛魔王手下！",continue_time=480,npc_model=53059,npc_title_img="shijian_8",npc_start_pos="118|170",npc_end_pos="131|126",user_pos="175|20",progress_desc="保护紫霞",level_desc="守护评分",scene_type=139,},
{task_id=3,task_name="击败牛魔",icon="taskicon_7",story_content="<color=#95d12b>奇遇背景：</color>所有的一切都是圈套，牛魔王故意放走紫霞，欲将与之为敌的修仙者一网打尽，成就当代魔尊\n<color=#95d12b>奇遇目标：</color>啥都不说了，打一架吧！\n\n",task_desc="限时内击败混世牛魔！",npc_model=53060,npc_title_img="shijian_1",npc_pos="-276|250",npc_scale=2,npc_start_pos="175|20",npc_end_pos="242|47",user_pos="285|172",progress_desc="击败牛魔王",},
{task_id=4,task_type=3,task_name="采集灵石",icon="taskicon_3",story_content="<color=#95d12b>奇遇背景：</color>龙太子敖丙追求大道途中走火坠魔，为祸一方，天帝派遣太乙真人及其徒弟哪吒前往唤醒其神志，遭遇魔气迷雾屏障，发布悬赏收集破阵灵石\n<color=#95d12b>奇遇目标：</color>收集破阵灵石！\n\n",task_desc="采集破阵灵石给太乙真人！",continue_time=300,npc_model=53061,npc_title_img="shijian_2",npc_pos="-287|180",npc_scale=1.8,npc_start_pos="520|176",npc_end_pos="486|222",npc_user_dist_min=1,user_pos="454|240",progress_desc="收集破阵石",level_desc="采集进度",scene_type=136,},
{task_id=5,task_type=4,task_name="破逐浪阵",icon="taskicon_4",story_content="<color=#95d12b>奇遇背景：</color>被魔障侵袭的敖丙设下九龙逐浪大阵，求仙者们需要躲避阵法冲击，等待哪吒找到魔龙真身破解阵法\n<color=#95d12b>奇遇目标：</color>为哪吒拖延时间，躲避阵法冲击\n\n",task_desc="根据预警躲避逐浪阵法！",continue_time=180,npc_model=53062,npc_title_img="shijian_3",npc_start_pos="454|240",npc_end_pos="415|225",npc_user_dist_min=3,user_pos="415|225",progress_desc="躲避逐浪阵",level_desc="躲避积分",scene_type=137,},
{task_id=6,dungeon_id=2,task_name="击败魔龙",icon="taskicon_9",story_content="<color=#95d12b>奇遇背景：</color>魔化敖丙和哪吒大战现出魔龙真身，魔气袅绕幻魔侵蚀人心，求仙者需辨认魔龙真身\n<color=#95d12b>奇遇目标：</color>协助哪吒击败魔龙，唤醒敖丙神志，解救东海！\n\n",task_desc="限时内击败敖丙真身！",npc_model=53063,npc_title_img="shijian_4",npc_pos="-287|180",npc_scale=1.8,npc_start_pos="415|225",npc_end_pos="350|222",npc_user_dist_min=3,user_pos="255|134",progress_desc="击败魔龙",},
{task_id=7,task_type=1,task_name="护送嫦娥",icon="taskicon_5",story_content="<color=#95d12b>奇遇背景：</color>嫦娥受逄蒙蒙蔽，被骗取飞仙丹，封印法力，后羿大战逄蒙无法脱身\n<color=#95d12b>奇遇目标：</color>求仙者前往协助失去法力的嫦娥前往奔月台，击退前来骚扰的逄蒙手下！\n\n",task_desc="保护嫦娥前往飞仙之路！",continue_time=480,npc_model=53064,npc_title_img="shijian_5",npc_start_pos="363|343",npc_end_pos="396|274",user_pos="215|56",progress_desc="护送嫦娥",level_desc="护送积分",scene_type=134,},
{task_id=8,task_type=2,task_name="搜索仙丹",icon="taskicon_6",story_content="<color=#95d12b>奇遇背景：</color>逄蒙为了防止后羿夺回飞仙丹，将其灵力分割多份藏于时间回廊，派遣虚空刺客守护\n<color=#95d12b>奇遇目标：</color>求仙者需绕过巡逻取得封印盒交于嫦娥复原飞仙丹！\n\n",task_desc="躲避逄蒙手下，搜索仙丹！",continue_time=300,npc_model=53065,npc_title_img="shijian_6",npc_start_pos="215|56",npc_end_pos="212|94",npc_user_dist_min=5,user_pos="202|133",progress_desc="搜索仙丹",level_desc="搜索进度",scene_type=135,},
{task_id=9,dungeon_id=3,task_name="击败逄蒙",icon="taskicon_8",story_content="<color=#95d12b>奇遇背景：</color>嫦娥已复原飞仙丹，前往奔月台，才发现与后羿纠缠的不过是逄蒙分神术的分身之一，本体早已在此等待多时\n<color=#95d12b>奇遇目标：</color>协助嫦娥击败逄蒙本体！\n\n",task_desc="限时内击败逄蒙！",npc_model=53066,npc_title_img="shijian_7",npc_start_pos="202|133",npc_end_pos="160|160",npc_user_dist_min=5,user_pos="230|90",progress_desc="击败逄蒙",}
},

task_meta_table_map={
[2]=3,	-- depth:1
[5]=4,	-- depth:1
},
other={
{}
},

other_meta_table_map={
},
reward_exp={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

reward_exp_meta_table_map={
},
airwall={
{},
{scene_id=111,airwall_type=2,airwall_pos="117|217",airwall_radius=8,airwall_effect="",},
{scene_id=112,airwall_pos="132|126",},
{scene_id=113,airwall_pos="242|47",},
{scene_id=114,airwall_type=2,airwall_pos="519|177",airwall_length=25,airwall_width=25,airwall_angle=57,airwall_radius=8,airwall_effect="effect_LiuYeJu_mccj",},
{scene_id=115,airwall_pos="486|222",airwall_angle=57,},
{scene_id=117,airwall_pos="350|222",airwall_angle=148,},
{scene_id=118,airwall_pos="363|343",airwall_angle=19,airwall_effect="effects_LanRuoSi_kqq_01",},
{scene_id=119,airwall_pos="40|72",airwall_angle=79,airwall_effect="effects_LanRuoSi_kqq_01",},
{scene_id=120,airwall_pos="212|102",airwall_angle=105,airwall_effect="effects_LanRuoSi_kqq_01",},
{scene_id=121,airwall_pos="160|160",airwall_angle=81,airwall_effect="effects_LanRuoSi_kqq_01",}
},

airwall_meta_table_map={
},
interface={
{},
{interface=1,item_id=91111,}
},

interface_meta_table_map={
},
server_default_table={start_server_day=1,end_server_day=3,lv_limit=160,grade=0,interface=0,},

grade_default_table={grade=0,day_index=1,task_reward=1,level_reward=1,mingwang_reward=1,},

task_reward_default_table={task_reward=1,task_num=1,lose_reward_item={},win_reward_item={},},

level_reward_default_table={level_reward=1,task_num=1,level=1,mingwang=280,},

mingwang_reward_default_table={mingwang_reward=1,mingwang=60,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20]},},

gameplay_default_table={day_index=1,week_index=1,task_chain_start="1400|1630",task_chain_ready_time=180,task_chain_id=2,},

task_chain_default_table={task_chain_id=1,task_ids="1|2|3",scene_id=110,born_pos="118|263",born_radius=6,pic_1="task_chain_act_bg_1",pic_2="task_chain_act_desc_1",rule_1="神话奇遇丰富多彩，可不要错过哦！",rule_2="1.活动期间，每日<color=#95d12b>14:00、16:30</color>开启神话奇遇，仙友可寻路前往委托人参与奇遇\n2.进入活动后，仙友<color=#95d12b>跟随委托人</color>前往各奇遇\n3.仙友需齐心协力，共同达成奇遇目标，奇遇成功，并获得成功报酬；否则失败\n4.各仙友根据奇遇中自身表现，获得<color=#95d12b>奇遇评级</color>，并根据评级获得神话名望\n5.达成神话名望进度可领取<color=#95d12b>阶段报酬</color>，每日<color=#95d12b>0点</color>神话名望及阶段报酬刷新重置",title_1="大圣抢亲",title_2="抢亲入口",btn_str="守护紫霞",task_chain_name="大圣抢亲",desc="牛魔势大，强夺至尊宝女友，紫霞被困蛮牛囚牢阵中！帮助大圣解救紫霞，就能拿到获得大圣的嘉奖，快行动起来吧！",tips1="每日14:00.16:30可前往抢亲",npc_id=10311,res_id=6122001,ts_appe_image_id=0,npc_view_pos="-284|0",npc_view_scale=1,npc_name="齐天大圣",npc_scene=1003,ready_name="抢亲准备",end_content="大圣抢亲成功结束了，感谢各位仙友的帮助！",act_notice="大圣抢亲守护紫霞",},

task_default_table={task_id=1,task_type=7,dungeon_id=1,task_name="潜入牛府",icon="taskicon_1",story_content="<color=#95d12b>奇遇背景：</color>牛魔势大，强夺至尊宝女友，紫霞被困蛮牛囚牢阵中，为营救紫霞，求仙者以送礼的名义，使用嫁妆中丹药灵力为紫霞仙子解除枷锁\n<color=#95d12b>奇遇目标：</color>躲避蛮牛冲撞，护送嫁妆！\n\n",task_desc="躲避蛮牛冲撞，运送嫁妆！",ready_time=20,continue_time=600,npc_model=53058,npc_title_img="shijian_9",npc_pos="-284|0",npc_scale=1,npc_speed=900,npc_start_pos="118|263",npc_end_pos="117|218",npc_user_dist_limit=15,npc_user_dist_min=2,user_pos="118|170",user_radius=3,progress_desc="解救紫霞",level_desc="伤害积分",scene_type=140,},

other_default_table={add_exp_interval=10,},

reward_exp_default_table={},

airwall_default_table={scene_id=110,airwall_type=1,airwall_pos="118|263",airwall_length=20,airwall_width=20,airwall_angle=0,airwall_radius=0,airwall_effect="effects_zhucheng_kqq_01",},

interface_default_table={interface=0,pic_1="right_special_bg",pic_2="fish_line",pic_3="special_icon_bg",pic_4="icon_name_bg",pic_5="zhuangshi_1",pic_6="baoxiang_2",pic_7="baoxiang_1",pic_8="baoxiang_3",pic_9="slider_img",pic_10="btn_big_04",pic_11="think_gift_img",pic_12="gradual_bg",pic_13="flag_icon",item_id=91110,pic1_1="bg_think",pic1_2="img_think",pic1_3="img_tip_bg",pic1_4="btn_close_05",pic1_5="btn_recharge",pic2_1="special_bg",pic2_2="zhuangshi_2",}

}

