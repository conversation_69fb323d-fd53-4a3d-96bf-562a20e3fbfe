DragonTempleLongShenLevelBuyView = DragonTempleLongShenLevelBuyView or BaseClass(SafeBaseView)

function DragonTempleLongShenLevelBuyView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_longshen_level_buy")
end

function DragonTempleLongShenLevelBuyView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["level_up_btn"], BindTool.Bind(self.OnClickLongShenBuy, self))
    XUI.AddClickEventListener(self.node_list.skill_icon, BindTool.Bind(self.OnSKillClick, self))
end

function DragonTempleLongShenLevelBuyView:ReleaseCallBack()

end

function DragonTempleLongShenLevelBuyView:OnFlush()
    self:FlushSkillPanel()
    local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
	if IsEmptyTable(cur_level_cfg) then
		return
	end

	local is_max_level = IsEmptyTable(next_level_cfg)
	self.node_list["level_up_btn"]:SetActive(not is_max_level)
	self.node_list["max_level"]:SetActive(is_max_level)
	local price = RoleWGData.GetPayMoneyStr(cur_level_cfg.price, cur_level_cfg.rmb_type, cur_level_cfg.rmb_seq)	
	self.node_list.up_condition_text.text.text = price --购买价格
	self:FlushLevelAttr(longshen_level, cur_level_cfg, next_level_cfg)
end

function DragonTempleLongShenLevelBuyView:FlushSkillPanel()
    local other_cfg = DragonTempleWGData.Instance:GetOtherCfg()

    local bundle, asset = ResPath.GetSkillIconById(other_cfg.longshen_skill_icon)
    self.node_list.skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.skill_icon.image:SetNativeSize()
    end)

    self.node_list.skill_name.text.text = other_cfg.longshen_skill_name

    local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
    local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
    if not cur_level_cfg then
        return
    end

    self.node_list.skill_desc1.text.text = cur_level_cfg.longshen_skill_des_1
	self.node_list.skill_desc2.text.text = cur_level_cfg.longshen_skill_des_2
end

function DragonTempleLongShenLevelBuyView:FlushLevelAttr(longshen_level, cur_level_cfg, next_level_cfg)
	self.node_list.cur_level.text.text = "LV." .. longshen_level
	self.node_list.cur_level_desc.text.text = cur_level_cfg.show_desc

    local is_max_level = IsEmptyTable(next_level_cfg)
    self.node_list.next_level_panel:SetActive(not is_max_level)
    if not is_max_level then
    	self.node_list.next_level.text.text = "LV." .. longshen_level + 1
		self.node_list.next_level_desc.text.text = next_level_cfg.show_desc
    end
end

function DragonTempleLongShenLevelBuyView:OnClickLongShenBuy()
	local longshen_level = DragonTempleWGData.Instance:GetLongShenLevel()
	local cur_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level)
	local next_level_cfg = DragonTempleWGData.Instance:GetLongShenLevelCfg(longshen_level + 1)
	if IsEmptyTable(next_level_cfg) or IsEmptyTable(cur_level_cfg) then
		return
	end

	if nil == self.buy_longshen_alert then
		self.buy_longshen_alert = Alert.New(nil, nil, nil, nil, true)
		self.buy_longshen_alert:SetShowCheckBox(true, "longshen_purchase_temple")
		self.buy_longshen_alert:SetCheckBoxDefaultSelect(false)
	end

	local price = RoleWGData.GetPayMoneyStr(cur_level_cfg.price, cur_level_cfg.rmb_type, cur_level_cfg.rmb_seq)
	self.buy_longshen_alert:SetLableString(string.format(Language.DragonTemple.BuyLongShenTips, price))
	self.buy_longshen_alert:SetOkFunc(function()
		RechargeWGCtrl.Instance:Recharge(cur_level_cfg.price, cur_level_cfg.rmb_type, cur_level_cfg.rmb_seq)
	end)
	
	self.buy_longshen_alert:Open()
end

function DragonTempleLongShenLevelBuyView:OnSKillClick()
    DragonTempleWGCtrl.Instance:OpenDragonTempleUpGradeView()
end
