LoginView = LoginView or BaseClass(SafeBaseView)
LoginViewStatus = {
	Account = 0,		-- 账号页
	HomePage = 1,		-- 登录主页
	SelectServer = 2,	-- 选服界面
	CreateRole = 3,		-- 创角
	SelectRole = 4,		-- 选角
}

-- 服务器特效
local select_server_effect = {
	[1] = "UI_xuanqu1",
	[2] = "UI_xuanqu2",
	[3] = "UI_xuanqu3",
}

local UnitySysInfo = UnityEngine.SystemInfo
function LoginView:__init()
	self.self_control_rendring = true
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	
	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_login_main")
	self:ClearViewTween()

	self:InitParam()
	self:Init__SelectRole()
	self:Init__CreateRole()
end

function LoginView:InitParam()
	self.load_callback = nil
	self.select_sex = -1
	self.select_prof = -1
	self.view_show_status = LoginViewStatus.Account
	self.click_start_game_interval_time = 0
	self.scene_cache = {}
end

function LoginView:__delete()
end

function LoginView:ReleaseCallBack()
	self:ReleaseCallBack__HomePage()
	self:ReleaseCallBack__CreateRole()
	self:ReleaseCallBack__SelectRole()

	self.scene_camera = nil
	self.urp_camera_comp = nil
	self.current_cg_obj = nil
	self.current_cg_chuchang = nil
	self.cg_ctrl = nil
end

function LoginView:ReleaseCallBack__HomePage()
	self.wave_effect_obj = nil
	self.click_start_game_interval_time = 0
	self.view_show_status = 0
end

function LoginView:LoadCallBack()
	self:LoadCallBack__HomePage()
	self:LoadCallBack__CreateRole()
	self:LoadCallBack__SelectRole()

	self.view_show_status = LoginViewStatus.Account
	self:ChangeViewShowStatus()
	self:PlayLoginMusic()

	if self.load_callback then
		self.load_callback()
	end
end

function LoginView:SetLoadCallBack(load_callback)
	self.load_callback = load_callback
end

function LoginView:LoadCallBack__HomePage()
	self:InitBgAndLogo()

	self.node_list["login_btn"].button:AddClickListener(BindTool.Bind(self.ShowLogin, self))
	self.node_list["start_game"].button:AddClickListener(BindTool.Bind(self.OnStartGameClick, self))
	self.node_list["login_notic_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGongGao, self))
    self.node_list["default_return_click"].button:AddClickListener(BindTool.Bind(self.OnDefaultReturnClick, self))
	self.node_list["default_service"].button:AddClickListener(BindTool.Bind(self.OnSelectServerClick, self))

	-- 同意用户协议和隐私政策
	local is_agree_toggle_on = false
	local show_abide_protocol = GLOBAL_CONFIG.param_list.switch_list.show_abide_protocol
	if show_abide_protocol then
		if PlayerPrefsUtil.HasKey("agree_abide_protocol") then
			is_agree_toggle_on = true
		end
	end
	self.node_list["agree_toggle"].toggle.isOn = is_agree_toggle_on
	self.node_list["agree_toggle_part"]:SetActive(show_abide_protocol)
	self.node_list["old_img"]:SetActive(show_abide_protocol)

	local app_filing_url = GLOBAL_CONFIG.param_list.app_filing_url
	self.node_list.app_filing_btn:SetActive(app_filing_url and app_filing_url ~= "")
	XUI.AddClickEventListener(self.node_list["app_filing_btn"], BindTool.Bind2(self.OnClickOpenAppFilingUrl, self))

	XUI.AddClickEventListener(self.node_list["user_xieyi"], BindTool.Bind2(self.OnClickOpenWarningTips, self, 1))
	XUI.AddClickEventListener(self.node_list["user_zhengce"], BindTool.Bind2(self.OnClickOpenWarningTips, self, 2))
	XUI.AddClickEventListener(self.node_list["old_img"], BindTool.Bind2(self.OnClickOpenOldWarningTips, self))

	-- 设置版本号
	self.node_list["app_version"].text.text = string.format("V%s", GLOBAL_CONFIG.local_package_info.version)
	self.node_list["asset_version"].text.text = GLOBAL_CONFIG.local_assets_info.version

	-- 渠道资质信息
	local copyright = GLOBAL_CONFIG.param_list.copyright
	if not IsEmptyTable(copyright) then
		self.node_list["warn_text"].text.text = string.format(Language.Login.Qualifications, copyright.copyright_holder, copyright.registration_number,
												copyright.publisher, copyright.approval_number, copyright.copyright_number, copyright.operating_unit)
	else
		self.node_list["warn_text"].text.text = Language.Login.HeXieGameTips
	end
end

-- 背景 logo
function LoginView:InitBgAndLogo()
	--[[
	local is_ipad = false
	local device = UnitySysInfo.deviceModel
	if device ~= nil then
		local device_str = string.sub(UnitySysInfo.deviceModel, 1, 3)
		if device_str ~= nil and device_str == "ipa" then
			is_ipad = true
		end
	end
	]]

	-- 显示Logo
	-- 检查SDK是否存在特殊的Logo，如果存在则使用SDK的Logo
	if ResMgr.ExistedInStreaming("sdk_res/logo.png") then
		self.node_list["default_logo"]:SetActive(true)
		self.node_list["logoURL"]:SetActive(true)
		self.node_list["title"]:SetActive(false)

		local url = ResUtil.GetAgentAssetPath("sdk_res/logo.png")
		local raw_image_url = self.node_list["logoURL"]:GetComponent(typeof(Nirvana.LoadRawImageURL))
		raw_image_url.autoFitNativeSize = true
		raw_image_url.URL = url
	else
		self.node_list["default_logo"]:SetActive(false)
		self.node_list["logoURL"]:SetActive(false)
		self.node_list["title"]:SetActive(true)
	end

	local has_agent_bg = ResMgr.ExistedInStreaming("sdk_res/login_bg.png")
	if has_agent_bg then
		self.node_list.bg_normal:SetActive(false)
		local url = ResUtil.GetAgentAssetPath("sdk_res/login_bg.png")
		self.node_list["backgroundURL"]:SetActive(true)
		local raw_image_url = self.node_list["backgroundURL"]:GetComponent(typeof(Nirvana.LoadRawImageURL))
		raw_image_url.autoFitNativeSize = true
		raw_image_url.URL = url
	else
		self.node_list["backgroundURL"]:SetActive(false)
		self.node_list.bg_normal:SetActive(true)
	end

	self:ShowLogin()
end

-- 设置登录背景
function LoginView:SetLoginURL(url)
	if url ~= nil and url ~= "" then
		self.bg_url = url
	end
end

-- 第一个 ‘开始游戏按钮’ 点击
function LoginView:ShowLogin()
	if not self:IsOpen() then
		return
	end

	--直接选角的话跳过
	local select_role_state = UtilU3d.GetCacheData("select_role_state")
	if select_role_state == 1 then
		-- self.node_list["login_btn"]:SetActive(false)
		local uservo = GameVoManager.Instance:GetUserVo()
		uservo.account_user_id = UtilU3d.GetCacheData("utilu3d_cahce_account_user_id")
		local last_server = UtilU3d.GetCacheData("select_role_state_plat_server_id")
		if not last_server or last_server == "" then	
			last_server = LoginWGData.Instance:ViewSelectServerID()
		end

		local ip = LoginWGData.Instance:GetGetServerIP(last_server)
		local port = LoginWGData.Instance:GetGetServerPort(last_server)
		GameNet.Instance:SetLoginServerInfo(ip, port)
		GameVoManager.Instance:GetUserVo().plat_server_id = last_server
		GameVoManager.Instance:GetUserVo().plat_server_name = LoginWGData.Instance:GetServerName(last_server)
		-- 预加载场景
		self:PreloadScene(chuangjue_bundle, chuangjue_asset, function()
			-- PreloadManager.Instance:WaitComplete(function()
				self.view_show_status = LoginViewStatus.SelectRole
				self:ChangeViewShowStatus()

				-- ReportManager:Step(Report.STEP_CONNECT_LOGIN_SERVER)
				GameNet.Instance:AsyncConnectLoginServer(5)
			-- end)
		end)
		return
	end

	-- SDK登录
	ReportManager:ReportBuriedCounter(BURIED_COUNTER_TYPE.showLogin)
	AgentAdapter.Instance:ShowLogin(function(is_succ)
		if is_succ then
			self.view_show_status = LoginViewStatus.HomePage
			self:ChangeViewShowStatus()

			if SettingWGCtrl ~= nil and SettingWGCtrl.Instance ~= nil then
				SettingWGCtrl.Instance:SendNoticeRequest()
			end

			if AgentAdapter ~= nil and AgentAdapter.Instance ~= nil then
				AgentAdapter.Instance:QuaryGetUserMoney()
			end

			local last_server_list = LoginWGData.Instance:GetViewGetLastLoginServerList(true)	--最近登陆服务器
			local last_server = tonumber(last_server_list[1])
			if not last_server then
				local server_list = LoginWGData.Instance:GetViewShowShowServerList()
				last_server = tonumber(server_list[#server_list].id)
			end

			LoginWGData.Instance:ViewSelectServerID(last_server)
			local ip = LoginWGData.Instance:GetGetServerIP(last_server)
			local port = LoginWGData.Instance:GetGetServerPort(last_server)
			-- local name = LoginWGData.Instance:GetShowServerNameById(last_server)
			self:SetCurServerInfo()
			--self.default_sever_text.text.text = name

			GameNet.Instance:SetLoginServerInfo(ip, port)
			GameVoManager.Instance:GetUserVo().plat_server_id = last_server
			GameVoManager.Instance:GetUserVo().plat_server_name = LoginWGData.Instance:GetServerName(last_server)

			ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.login, BURIED_EVENT_PARAM.loginSuccess)
		else
			if not self.fali_to_login_num then
				self.fali_to_login_num = 1
			else
				self.fali_to_login_num = self.fali_to_login_num + 1
			end

			if self.fali_to_login_num >= 4 then
			else
				-- ReportManager:Step(Report.STEP_LOGIN_FAIL)
			end

			self.view_show_status = LoginViewStatus.Account
			self:ChangeViewShowStatus()
		end
	end)
end

-- 点击开始按钮
function LoginView:OnStartGameClick()
	-- 选角创角界面 时间间隔
	if self.select_role_item_click_time and (Status.NowTime - self.select_role_item_click_time < 1) then
		return
	end

	-- 是否同意用户协议
	local show_abide_protocol = GLOBAL_CONFIG.param_list.switch_list.show_abide_protocol
	if show_abide_protocol and not self.node_list.agree_toggle.toggle.isOn then
		--SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.AgreeTip)
		local str = Language.Login.AgreeTip
		local ok_func = function ()
		end

		TipWGCtrl.Instance:OpenAlertTips(str, ok_func)
		return
	end

	local cur_server_id = LoginWGData.Instance:ViewSelectServerID()
	-- 登录限制
	if not LoginWGData.Instance:IsCanLoginServer(cur_server_id) then
		return
	end

	-- 点击间隔限制
	local now_time = Status.NowTime
	if now_time < self.click_start_game_interval_time + 3 then
		return
	end

	self.click_start_game_interval_time = now_time
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.clickLoginStartGame)

	self:WaitViewShow()

	if not GameNet.Instance:IsLoginServerConnected() then
		GameNet.Instance:AsyncConnectLoginServer(5)
	else
		LoginWGCtrl.SendLoginReq()
	end

	LoginWGData.Instance:CahcePreviousServer()
end

function LoginView:OnSelectServerClick()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.clickServer)
    self.view_show_status = LoginViewStatus.SelectServer
    self:ChangeViewShowStatus()

	LoginWGCtrl.Instance:OpenSelectServerView()
end

function LoginView:OnDefaultReturnClick()
	if Status.NowTime < self.click_start_game_interval_time + 3 then
		return
	end

	GlobalEventSystem:Fire(LoginEventType.LOGOUT)
end

function LoginView:BackLoginView()
	-- self:OnCreateReturnClick()
	LoginWGData.Instance:SetCurrSelectRoleId(-1)
	GameNet.Instance:ResetLoginServer()
	self.view_show_status = LoginViewStatus.Account
	self:ChangeViewShowStatus()
	self:ShowLogin()
end

function LoginView:SelectServerViewBack()
	self.view_show_status = LoginViewStatus.HomePage
    self:ChangeViewShowStatus()
	self:SetCurServerInfo()
end

function LoginView:ChangeViewShowStatus()
	local status = self.view_show_status
	self.node_list["login_root"]:SetActive(status == LoginViewStatus.Account or status == LoginViewStatus.HomePage or status == LoginViewStatus.SelectServer)
	-- self.node_list["login_btn"]:SetActive(status == LoginViewStatus.Account)
	self.node_list["default_login"]:SetActive(status == LoginViewStatus.HomePage)
	self.node_list["create_role"]:SetActive(status == LoginViewStatus.CreateRole)
	self.node_list["select_role"]:SetActive(status == LoginViewStatus.SelectRole)

	local both_cs = status == LoginViewStatus.SelectRole
	self.node_list["prof_desc"]:SetActive(both_cs)
	self.node_list["return_btn"]:SetActive(both_cs)

	local prof_desc_rect = self.node_list.prof_desc.rect
	if status == LoginViewStatus.CreateRole then
		prof_desc_rect.anchorMin = Vector2(0, 1)
		prof_desc_rect.anchorMax = Vector2(0, 1)
		prof_desc_rect.pivot = Vector2(0, 1)
		prof_desc_rect.anchoredPosition = Vector2(0, 0)
		self.node_list.prof_name.rect.anchoredPosition = Vector2(-55, 116)
		self.node_list.prof_desc_name_bg.rect.anchoredPosition = Vector2(-117, -126)
		self.node_list.desc_name_1.rect.anchoredPosition = Vector2(-113, -149)
		self.node_list.desc_name_2.rect.anchoredPosition = Vector2(-143, -149)
	else
		prof_desc_rect.anchorMin = Vector2(1, 1)
		prof_desc_rect.anchorMax = Vector2(1, 1)
		prof_desc_rect.pivot = Vector2(1, 1)
		prof_desc_rect.anchoredPosition = Vector2(0, 0)
		self.node_list.prof_name.rect.anchoredPosition = Vector2(98, 166)
		self.node_list.prof_desc_name_bg.rect.anchoredPosition = Vector2(36, -76)
		self.node_list.desc_name_1.rect.anchoredPosition = Vector2(40, -100)
		self.node_list.desc_name_2.rect.anchoredPosition = Vector2(10, -100)
	end

	self:SetSceneWatersNodeShow(status ~= LoginViewStatus.CreateRole)

	if status == LoginViewStatus.CreateRole then
		self:CloseSceneCamera()
	else
		self:ChangeToSceneCamera()
	end

	-- 脚底的水波纹
	if IsNil(self.wave_effect_obj) then
		self.wave_effect_obj = UnityEngine.GameObject.Find("Effects/wave_effect")
	end
	
	if not IsNil(self.wave_effect_obj) then
		self.wave_effect_obj:SetActive(status ~= LoginViewStatus.CreateRole)
	end
end

function LoginView:WaitViewShow()
	self.node_list["login_root"]:SetActive(true)
	self.node_list["default_login"]:SetActive(false)
	self.node_list["create_role"]:SetActive(false)
	self.node_list["select_role"]:SetActive(false)
	self.node_list["prof_desc"]:SetActive(false)
	self.node_list["return_btn"]:SetActive(false)
end

-- 服务器停止注册，恢复默认显示
function LoginView:WaitViewShowEndByStopRegister()
	self.node_list["default_login"]:SetActive(true)
end

function LoginView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "flush_select_role_view" then
			self:FlushSelectRoleView()
		elseif k == "back_login_view" then
			self:ResetSelectBeforeParam()
			self:ResetCreateBeforeParam()
			self:BackLoginView()
		elseif k == "flush_gonggao" and self.node_list["login_notic_btn"] then
			self.node_list["login_notic_btn"]:SetActive(v[1])
			self:FlushGongGaoRed()
		elseif k == "flush_gonggao_red" then
			self:FlushGongGaoRed()
		elseif k == "select_server_back" then
			self:SelectServerViewBack()
		end
	end
end

-- 当前所选服的信息
function LoginView:SetCurServerInfo()
	local cur_server_id = LoginWGData.Instance:ViewSelectServerID()
	local bundle_bundle, bundle_asset = LoginWGData.GetServerStateAsset(LoginWGData.Instance:GetServerFlag(cur_server_id))
	self.node_list["busy_point"].image:LoadSprite(bundle_bundle, bundle_asset)
	self.node_list["default_server_text"].text.text = LoginWGData.Instance:GetShowServerNameById(cur_server_id)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.default_service.rect)

	local server_list = GLOBAL_CONFIG.server_info.server_list
	for k, v in pairs(server_list) do
		if cur_server_id == v.id then
			if v.style and v.style ~= 0 then
				self.node_list.enter_game_effect:SetActive(true)
				local effect_asset, effect_bundle = ResPath.GetA2Effect(select_server_effect[v.style])
				if effect_asset and effect_bundle then
					self.node_list.enter_game_effect:ChangeAsset(effect_asset, effect_bundle)
				end
			else
				self.node_list.enter_game_effect:SetActive(false)
			end
		end
	end
end

-- 播放BGM
function LoginView:PlayLoginMusic()
	if not self.is_playing_login_bgm then
		AudioService.Instance:PlayBgm("audios/musics/bgmlogin", "F1bgm2")
		self.is_playing_login_bgm = true
	end
end

function LoginView:StopLoginMusic()
	self.is_playing_login_bgm = nil
	AudioService.Instance:StopBgm()
end

-- 加载场景
local SceneManager = UnityEngine.SceneManagement.SceneManager
local load_mode_single = UnityEngine.SceneManagement.LoadSceneMode.Single
function LoginView:PreloadScene(bundle, asset, callback)
	bundle = bundle or chuangjue_bundle
	asset = asset or chuangjue_asset
	local key = bundle .. asset

	self.scene_cache = self.scene_cache or {}
	if self.scene_cache[key] then
		callback()
		return
	end

	ResMgr:LoadLevelAsync(
		bundle,
		asset,
		load_mode_single,
		function()
			Scheduler.Delay(function()
				local scene = SceneManager.GetSceneByName(asset)
				if not IsNil(scene) and scene:IsValid() and scene.isLoaded then
					local success, roots = pcall(function()
						return scene:GetRootGameObjects()
					end)
					
					if success and roots then
						self.scene_cache[key] = {
							scene = scene,
							roots = roots,
						}

						local objs = self.scene_cache[key].roots
						for i = 0, objs.Length - 1 do
							if objs[i] and objs[i].gameObject.name == "Main" then
								local camera_obj = objs[i].gameObject.transform:Find("Camera")
								self.scene_camera = camera_obj and camera_obj:GetComponent(typeof(UnityEngine.Camera)) or nil
								self.urp_camera_comp = camera_obj and camera_obj:GetComponent(typeof(URPCamera)) or nil
								if not IsNil(self.scene_camera) and not IsNil(self.urp_camera_comp) then
									break
								end
							end
						end

						callback()
					else
						print_error("Error: LoginView PreloadScene Failed to get root game objects from scene: " .. asset)
						callback()
					end
				else
					print_error("Error: LoginView PreloadScene scene is invalid or not loaded: " .. asset)
					callback()
				end
			end)
		end
	)
end

-- 改变场景
local load_mode_additive = UnityEngine.SceneManagement.LoadSceneMode.Additive
function LoginView:ChangeScene(bundle, asset, callback)
	local key = bundle .. asset
	-- 激活/加载当前场景
	self.scene_cache = self.scene_cache or {}
	for k,v in pairs(self.scene_cache) do
		if k ~= key then
			local objs = v.roots
			for i = 0, objs.Length - 1 do
				local obj = objs[i]
				if obj then
					obj:SetActive(false)
				end
			end
		end
	end
	
	local cache_scene = self.scene_cache[key]
	if cache_scene ~= nil then
		local objs = cache_scene.roots
		for i = 0, objs.Length - 1 do
			local obj = objs[i]
			if obj then
				obj:SetActive(true)
			end
		end

		callback()
		return
	end

	ResMgr:LoadLevelAsync(
		bundle,
		asset,
		load_mode_additive,
		function()
			Scheduler.Delay(function()
				local scene = SceneManager.GetSceneByName(asset)
				if not IsNil(scene) and scene:IsValid() and scene.isLoaded then
					SceneManager.SetActiveScene(scene)
					local success, roots = pcall(function()
						return scene:GetRootGameObjects()
					end)
					
					if success and roots then
						self.scene_cache[key] = {
							scene = scene,
							roots = roots
						}
						callback()
					else
						print_error("Error: LoginView ChangeScene Failed to get root game objects from scene: " .. asset)
						callback()
					end
				else
					print_error("Error: LoginView ChangeScene scene is invalid or not loaded: " .. asset)
					callback()
				end
			end)
		end
	)
end

function LoginView:ClearScenes()
	if nil ~= self.create_role_scene_camera
	and not IsNil(self.create_role_scene_camera.gameObject)
	and not self.create_role_scene_camera.gameObject.activeSelf then
		self.create_role_scene_camera.gameObject:SetActive(true)
		self.create_role_scene_camera = nil
	end

	-- 清空缓存的场景
	for k,v in pairs(self.scene_cache) do
		-- 检查场景是否有效再卸载
		if v.scene and not IsNil(v.scene) and v.scene:IsValid() then
			local success, error_msg = pcall(function()
				SceneManager.UnloadSceneAsync(v.scene)
			end)
			if not success then
				print_error("LoginView:ClearScenes 卸载场景失败:", k, error_msg)
			end
		else
			-- print_log("LoginView:ClearScenes 跳过无效场景:", k)
		end
	end

	self.scene_cache = {}

	-- -- 清空CG实例
	-- self:ClearCGInstanceList()

	self.current_cg_obj = nil
	self.current_cg_chuchang = nil
	self.cg_ctrl = nil

	-- -- 清理绘制物体
	-- self:DestroyDrawObj()
end








--=============================== 公告 =====================================
-- 点击公告
function LoginView:OnClickGongGao()
	--SettingWGCtrl.Instance:SetLoginState(true)
	SettingWGCtrl.Instance:SendNoticeRequest()
	ViewManager.Instance:Open(GuideModuleName.LoginNoticView)
end

-- 公告红点
function LoginView:FlushGongGaoRed()
	if self.node_list["login_notic_red"] then
		-- local flag = SettingWGData.Instance:GetAllNoticHasRemind()
		self.node_list["login_notic_red"]:SetActive(false)
	end
end


--=============================== 用户协议 =====================================
function LoginView:OnClickOpenWarningTips(index)
	local plat_id = CHANNEL_AGENT_ID
	local condition_list = LoginWGData.Instance:GetAgentCondition()
	local is_xieyi = index == 1
	local data = {}
	data.title = is_xieyi and condition_list[1].server_title or condition_list[1].secret_title -- 默认
	data.desc = is_xieyi and condition_list[1].server_decs or condition_list[1].secret_decs
	data.warning_btn_text = is_xieyi and Language.Login.AgreeBtnText or Language.Login.OKBtnText

	for i,v in ipairs(condition_list) do
		if plat_id == v.spid then
			data.title = is_xieyi and v.server_title or v.secret_title
			data.desc = is_xieyi and v.server_decs or v.secret_decs
		end
	end

	LoginWGCtrl.Instance:OpenSeverWarningTips(data)
end

function LoginView:OnClickOpenOldWarningTips(index)
	local plat_id = CHANNEL_AGENT_ID
	local condition_list = LoginWGData.Instance:GetAgentCondition()
	local data = {}
	data.title = condition_list[1].age_title -- 默认
	data.desc = condition_list[1].age_decs
	data.warning_btn_text = Language.Login.OKBtnText

	for i,v in ipairs(condition_list) do
		if plat_id == v.spid then
			data.title = v.age_title
			data.desc = v.age_decs
		end
	end

	LoginWGCtrl.Instance:OpenSeverWarningTips(data)
end

-- 备案号
function LoginView:OnClickOpenAppFilingUrl()
	local app_filing_url = GLOBAL_CONFIG.param_list.app_filing_url
	if app_filing_url and app_filing_url ~= "" then
		UnityEngine.Application.OpenURL(app_filing_url)
	end
end