FuBenTeamCommonBossTaskView = FuBenTeamCommonBossTaskView or BaseClass(SafeBaseView)

local level_show_img = {
    [0] = "a3_fb_ysz_4",
    [1] = "a3_fb_ysz_3",
    [2] = "a3_fb_ysz_2",
    [3] = "a3_fb_ysz_1",
}

local level_show_effect = {
    [0] = "UI_pingfen_A",
    [1] = "UI_pingfen_S",
    [2] = "UI_pingfen_SS",
    [3] = "UI_pingfen_SSS",
}

function FuBenTeamCommonBossTaskView:__init()
	self.is_safe_area_adapter = true
	self.view_name = "FuBenTeamCommonBossTaskView"
    self.view_layer = UiLayer.MainUIHigh
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_team_common_task_info")
end

function FuBenTeamCommonBossTaskView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function FuBenTeamCommonBossTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function FuBenTeamCommonBossTaskView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
		self.reward_list:SetStartZeroIndex(true)
	end

    if not self.rank_list then
        self.rank_list = AsyncListView.New(FuBenTeamCommonBossRankCell, self.node_list["rank_list"])
    end

    self.node_list["caiji_gather_btn"].button:AddClickListener(BindTool.Bind(self.OnClickCaiJiGather, self))
    self.node_list["go_npc_pos_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFindNpc, self))

    for i = 1, 2 do
        XUI.AddClickEventListener(self.node_list["mvp_like_btn_" .. i], BindTool.Bind(self.OnClickMvpLikeType, self, i))
    end

    self.star_img_tween = self.node_list.level_img:GetComponent(typeof(UGUITweenScale))

    self.node_list.mvp_like_group:SetActive(false)
end

function FuBenTeamCommonBossTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function FuBenTeamCommonBossTaskView:ReleaseCallBack()
    self:CleanRatingTime()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

    if self.change_state_anim then
        self.change_state_anim:Kill()
        self.change_state_anim = nil
    end

    if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

    if CountDownManager.Instance:HasCountDown("fuben_team_alert_time") then
        CountDownManager.Instance:RemoveCountDown("fuben_team_alert_time")
    end

    if CountDownManager.Instance:HasCountDown("fuben_team_show_mvp_time") then
        CountDownManager.Instance:RemoveCountDown("fuben_team_show_mvp_time")
    end

    if self.mvp_like_count_down then
		CountDown.Instance:RemoveCountDown(self.mvp_like_count_down)
		self.mvp_like_count_down = nil
	end

    if self.like_anim_1 then
		GlobalTimerQuest:CancelQuest(self.like_anim_1)
		self.like_anim_1 = nil
	end

    if self.mvp_like_delay_timer then
        GlobalTimerQuest:CancelQuest(self.mvp_like_delay_timer)
        self.mvp_like_delay_timer = nil
    end

	if self.mvp_head_cell then
		self.mvp_head_cell:DeleteMe()
		self.mvp_head_cell = nil
	end

    self.join_alert_state = nil
    self.star_img_tween = nil
    self.mvp_like_tween_status = nil
end

function FuBenTeamCommonBossTaskView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function FuBenTeamCommonBossTaskView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
		if k == "all" then
            local scene_id = Scene.Instance:GetSceneId()
            local scene_fuben_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenSceneCfg(scene_id)
            if not scene_fuben_cfg then
                return
            end
        
            self:FlushRatingInfo(scene_fuben_cfg)
            self:FlushStageInfo(scene_fuben_cfg)
            self:FlushTaskInfo(scene_fuben_cfg)
            self:FlushRankList()
		elseif k == "rank_info" then
            self:FlushRankList()
        elseif k == "show_mvp_info" then
            self:FlushShowMvpInfo()
        elseif k == "mvp_like" then
            self:FlushShowMvpLike()
		end
	end
end

function FuBenTeamCommonBossTaskView:FlushRatingInfo(scene_fuben_cfg)
    local fb_seq = scene_fuben_cfg.seq
    local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if IsEmptyTable(fb_scene_info) then
		return
	end

	if fb_scene_info.is_end ~= 0 then
        self:CleanRatingTime()
		return
	end

    if not self.fuben_team_rating_timer_quest then
		self:UpdateRatingTime()
		self.fuben_team_rating_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateRatingTime, self), 0.5)
	end
end

function FuBenTeamCommonBossTaskView:CleanRatingTime()
    if self.fuben_team_rating_timer_quest then
        GlobalTimerQuest:CancelQuest(self.fuben_team_rating_timer_quest)
        self.fuben_team_rating_timer_quest = nil
    end
end

function FuBenTeamCommonBossTaskView:UpdateRatingTime()
    local scene_id = Scene.Instance:GetSceneId()
    local scene_fuben_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenSceneCfg(scene_id)
    if not scene_fuben_cfg then
        return
    end

    local fb_seq = scene_fuben_cfg.seq
    local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return
	end

    local pass_reward_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenPassRewardCfg(fb_seq)
    if not pass_reward_cfg then
        return
    end

	if fb_scene_info.is_end ~= 0 then
        self:CleanRatingTime()
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- 刷怪到现在的时间(总秒数)
	local pass_time = server_time - fb_scene_info.fb_start_time
    local cur_pass_reward_cfg = {}
    for k, v in pairs(pass_reward_cfg) do
        if v.pass_time >= pass_time then
            cur_pass_reward_cfg = v
            break
        end
    end

    if IsEmptyTable(cur_pass_reward_cfg) then
        return
    end

    self.reward_list:SetDataList(cur_pass_reward_cfg.pass_reward_item)
    local next_star_change_time =  math.ceil(cur_pass_reward_cfg.pass_time - pass_time)
    self.node_list.fuben_pass_desc.text.text = string.format(cur_pass_reward_cfg.desc, math.max(0, next_star_change_time))
    local bundle, asset = ResPath.GetCommonImages(level_show_img[cur_pass_reward_cfg.star_num] or level_show_img[0])
    self.node_list.level_img.image:LoadSprite(bundle, asset, function()
        self.node_list.level_img.image:SetNativeSize()
    end)
    
    if next_star_change_time < 5 then
        self.star_img_tween.duration = 0.15
    else
        self.star_img_tween.duration = 0.3
    end
    local eff_bundle, eff_asset = ResPath.GetUIEffect(level_show_effect[cur_pass_reward_cfg.star_num])
    self.node_list.level_effct:ChangeAsset(eff_bundle, eff_asset)

    self:FlushAlertInfo(next_star_change_time)
    local stage_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenStageCfg(fb_seq, fb_scene_info.stage)
    if stage_cfg then
        if stage_cfg.time > 0 then
            local start_stage_time = fb_scene_info.start_stage_time
            local stage_pass_time = server_time - fb_scene_info.start_stage_time
            local stage_remain_time = math.floor(stage_cfg.time - stage_pass_time)

            self.node_list.next_stage_desc.text.text = string.format(stage_cfg.desc, math.max(0, stage_remain_time))
        else
            self.node_list.next_stage_desc.text.text = stage_cfg.desc
        end
    else
        self.node_list.next_stage_desc.text.text = ""  
    end
end

--警惕效果
function FuBenTeamCommonBossTaskView:FlushAlertInfo(next_star_change_time)
    local other_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenOtherCfg()
    if next_star_change_time > 0 and next_star_change_time <= other_cfg.alert_time then
        self.node_list.top_time:SetActive(true)
        self.node_list.alert_effct:SetActive(true)
        self.join_alert_state = true
        self:FlushAlertTime(next_star_change_time)
    else
        self.node_list.top_time:SetActive(false)
        self.node_list.alert_effct:SetActive(false)
        if self.join_alert_state then
            if self.change_state_anim then
                self.change_state_anim:Kill()
                self.change_state_anim = nil
            end
            
            self.node_list.level_parent_node.transform.localScale = u3dpool.vec3(3, 3, 3)
            self.change_state_anim = DG.Tweening.DOTween.Sequence()
            self.change_state_anim:Append(self.node_list.level_parent_node.rect:DOScale(Vector3(1, 1, 1), 1.2):SetEase(DG.Tweening.Ease.OutBack))
            self.node_list.tanchu_effect:SetActive(false)
            self.node_list.tanchu_effect:SetActive(true)
            self.change_state_anim:OnComplete(function ()
                self.node_list.tanchu_effect:SetActive(false)
            end)
        end
        self.join_alert_state = false

        if CountDownManager.Instance:HasCountDown("fuben_team_alert_time") then
            CountDownManager.Instance:RemoveCountDown("fuben_team_alert_time")
        end
    end
end

function FuBenTeamCommonBossTaskView:FlushAlertTime(next_star_change_time)
    if CountDownManager.Instance:HasCountDown("fuben_team_alert_time") then
        CountDownManager.Instance:RemoveCountDown("fuben_team_alert_time")
    end

    if next_star_change_time > 0 then
        CountDownManager.Instance:AddCountDown("fuben_team_alert_time", 
        BindTool.Bind(self.FinalUpdateAlertTimeCallBack, self), 
        BindTool.Bind(self.OnCompleteAlertTimeCallBack, self), 
        nil, next_star_change_time, 0.05)
    else
        self:OnCompleteAlertTimeCallBack()
    end

end

function FuBenTeamCommonBossTaskView:FinalUpdateAlertTimeCallBack(now_time, total_time)
    local time_tab = TimeUtil.Format2TableDHM3(total_time - now_time)
    self.node_list.alert_time.text.text = string.format(string.format("%02d:%02d:%02d",time_tab.min, time_tab.sec, time_tab.milliseccond / 10))
end

function FuBenTeamCommonBossTaskView:OnCompleteAlertTimeCallBack()
    self.node_list.alert_time.text.text = "00:00:00"
end

function FuBenTeamCommonBossTaskView:FlushStageInfo(scene_fuben_cfg)
    local fb_seq = scene_fuben_cfg.seq
    local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return
	end

    local all_stage_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenStageCfg(fb_seq)
    if not all_stage_cfg then
        return
    end

    self.node_list.cur_stage_desc.text.text = string.format(Language.FuBenTeamCommonBoss.CurStageDescent, fb_scene_info.stage, #all_stage_cfg)
end

function FuBenTeamCommonBossTaskView:FlushTaskInfo(scene_fuben_cfg)
    local fb_seq = scene_fuben_cfg.seq
    local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return
	end

    local cur_stage = fb_scene_info.stage
    local stage_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenStageCfg(fb_seq, cur_stage)
    if not stage_cfg then
        return
    end

    self.node_list.montert_kill_desc:SetActive(stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Monster)
    self.node_list.caiji_num_desc:SetActive(stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Gather)
    self.node_list.npc_task_desc:SetActive(stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Npc)
    if stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Monster then
        local monster_seq = stage_cfg.param0
        local fuben_monster_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenMonsterCfg(monster_seq)
        local all_montser_num = fuben_monster_cfg and fuben_monster_cfg.monster_num or 0
        self.node_list.montert_kill_desc.text.text = string.format(Language.FuBenTeamCommonBoss.MonsterKillDesc, fb_scene_info.kill_monster_count, all_montser_num)
    elseif stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Gather then
        self.node_list.caiji_num_desc.text.text = string.format(Language.FuBenTeamCommonBoss.CaijiNumDesc, fb_scene_info.has_gather_times, fb_scene_info.need_gather_times)
    elseif stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Npc then
        local scene_id = Scene.Instance:GetSceneId()
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
        local npc_id = stage_cfg.param0
        local scene_npc_cfg = {}
        if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
            for i, j in pairs(scene_cfg.npcs) do
                if j.id == npc_id then
                    scene_npc_cfg = j
                    break
                end
            end
        end

        self.node_list.npc_task_desc.text.text = string.format(Language.FuBenTeamCommonBoss.GoNpcDesc, scene_npc_cfg.x, scene_npc_cfg.y)
    end
end

function FuBenTeamCommonBossTaskView:OnClickCaiJiGather()
    local scene_id = Scene.Instance:GetSceneId()
    local scene_fuben_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenSceneCfg(scene_id)
    if not scene_fuben_cfg then
        return
    end

    local fb_seq = scene_fuben_cfg.seq
    local fb_scene_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return
	end

    local cur_stage = fb_scene_info.stage
    local stage_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenStageCfg(fb_seq, cur_stage)
    if not stage_cfg then
        return
    end

    if stage_cfg.type ~= FuBenTeamCommonBossWGData.TaskType.Gather then
        return
    end

    local gather_obj = FuBenTeamCommonBossWGData.Instance:SelectRandGatherObj()
    if not gather_obj then
        return
    end

    local target_x, target_y = gather_obj:GetLogicPos()
	MoveCache.SetEndType(MoveEndType.Gather)
    MoveCache.param1 = gather_obj.gather_config.id
    MoveCache.target_obj = gather_obj
    GuajiCache.target_obj_id = gather_obj.gather_config.id
    GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), target_x, target_y, 3.5)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function FuBenTeamCommonBossTaskView:OnClickFindNpc()
    FuBenTeamCommonBossWGCtrl.Instance:FindTaskNpc()
end

function FuBenTeamCommonBossTaskView:FlushRankList()
    local hurt_list = FuBenTeamCommonBossWGData.Instance:GetAllTeamCommonBossHurtInfo()
    if not IsEmptyTable(hurt_list) then
        self.rank_list:SetDataList(hurt_list)
        local my_hurt_count = 0
        for k, v in pairs(hurt_list) do
            if v.uuid == RoleWGData.Instance:GetUUid() then
                my_hurt_count = v.hurt_count
                break
            end
        end
        self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(my_hurt_count)
        self.node_list.per_bg.slider.value = my_hurt_count / FuBenTeamCommonBossWGData.Instance:GetNormalHurtInfoMaxValue()
    else
        self.node_list.my_damate.text.text = 0
        self.node_list.per_bg.slider.value = 0
    end
end

function FuBenTeamCommonBossTaskView:FlushShowMvpInfo()
    self.node_list.show_mvp_info:SetActive(false)
    local mvp_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonShowMvpInfo()
    if IsEmptyTable(mvp_info) then
        return
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local end_show_time = mvp_info.mvp_show_time - server_time 
    if end_show_time > 0 then
        self.node_list.show_mvp_info:SetActive(true)
        for i = 1, 2 do
            self.node_list["mvp_like_btn_" .. i]:SetActive(true)
        end

        if not self.mvp_head_cell then
			self.mvp_head_cell = BaseHeadCell.New(self.node_list.mvp_head_cell)
		end

        ----头像
		local data = {}
		if mvp_info and mvp_info.mvp_is_robot == 1 then
			data.role_id = 0
		else
			data.role_id = mvp_info.mvp_uuid.temp_low
		end

		data.prof = mvp_info.prof
		data.sex = mvp_info.sex
		data.fashion_photoframe = mvp_info.shizhuang_photoframe
		self.mvp_head_cell:SetImgBg(true)
		self.mvp_head_cell:SetData(data)

        self.node_list.mvp_desc.text.text = string.format(Language.FuBenTeamCommonBoss.MvpDesc, mvp_info.mvp_name)

        if CountDownManager.Instance:HasCountDown("fuben_team_show_mvp_time") then
            CountDownManager.Instance:RemoveCountDown("fuben_team_show_mvp_time")
        end

        CountDownManager.Instance:AddCountDown("fuben_team_show_mvp_time", 
            nil, 
            BindTool.Bind(self.OnCompleteShowMvpTimeCallBack, self), 
            nil, end_show_time, 1)
    else
        self:OnCompleteShowMvpTimeCallBack()
    end
end

function FuBenTeamCommonBossTaskView:OnCompleteShowMvpTimeCallBack()
    self.node_list.show_mvp_info:SetActive(false)
end

function FuBenTeamCommonBossTaskView:OnClickMvpLikeType(like_type)
    local mvp_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonShowMvpInfo()
    if IsEmptyTable(mvp_info) then
        return
    end

    --策划骚需求 点完后客户端操作隐藏
    if RoleWGData.Instance:GetUUid() == mvp_info.mvp_uuid then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FuBenTeamCommonBoss.LikeError)
    else
        RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TEAM_COMMON_FB_LIKE, like_type)
        --TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.FuBenTeamCommonBoss.LikeDesc2[like_type], mvp_info.mvp_name))
        self.node_list["mvp_like_btn_" .. like_type]:SetActive(false)
    end
end

function FuBenTeamCommonBossTaskView:FlushShowMvpLike()
    if not self.node_list or self.is_out_fb then
        return
    end

    if self.mvp_like_tween_status then
        return
	end

    if self.mvp_like_count_down then
		CountDown.Instance:RemoveCountDown(self.mvp_like_count_down)
		self.mvp_like_count_down = nil
	end

    if self.mvp_like_delay_timer then
        GlobalTimerQuest:CancelQuest(self.mvp_like_delay_timer)
        self.mvp_like_delay_timer = nil
    end

    local can_show, mvp_like_info = FuBenTeamCommonBossWGData.Instance:GetTeamCommonMvpLikeInfo()
    if not can_show then
        if self.node_list.mvp_like_group then
            self.node_list.mvp_like_group:SetActive(false)
        end
        return
    end
        
    local function time_func(elapse_time, total_time)
        if elapse_time >= total_time then
            if self.node_list and self.node_list.mvp_like_group then
                self.node_list.mvp_like_group:SetActive(false)
            end

            self.mvp_like_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
                if self.node_list and not self.is_out_fb then
                    self.mvp_like_tween_status = false
                    self:FlushShowMvpLike()
                end
                self.mvp_like_delay_timer = nil
            end, 0.1)
            return
        end
    end

    self.mvp_like_count_down = CountDown.Instance:AddCountDown(2, 1, time_func)
    self.node_list.mvp_like_group:SetActive(true)

    self.mvp_like_tween_status = true
    
    local like_img_name = mvp_like_info.type == 1 and "a3_zd_anz" or "a3_zd_anh"
    local bundle, asset = ResPath.GetFuBenImage(like_img_name)
    self.node_list.like_type_img.image:LoadSprite(bundle, asset, function()
        self.node_list.like_type_img.image:SetNativeSize()
    end)

    self.node_list.like_desc.text.text = string.format(Language.FuBenTeamCommonBoss.LikeDesc[mvp_like_info.type], mvp_like_info.player_name)
    self:PlayLikeAnim()
end

function FuBenTeamCommonBossTaskView:PlayLikeAnim()
    self.node_list.like_type_bg.transform.anchoredPosition = Vector2(0, 0)
    self.node_list.like_bg.transform.anchoredPosition = Vector2(-100, 0)
    local like_bg_canvas_group = self.node_list.like_bg:GetComponent(typeof(UnityEngine.CanvasGroup))
    like_bg_canvas_group.alpha = 0
    local like_desc_canvas_group = self.node_list.like_desc:GetComponent(typeof(UnityEngine.CanvasGroup))
    like_desc_canvas_group.alpha = 0

    self.node_list["like_type_bg"].transform:DOLocalMoveX(-157, 0.3)
    self.node_list["like_bg"].transform:DOLocalMoveX(0, 0.4)
    like_bg_canvas_group:DoAlpha(0, 1, 0.4)

    self.like_anim_1 = GlobalTimerQuest:AddDelayTimer(function ()
        like_desc_canvas_group:DoAlpha(0, 1, 0.3)
    end, 0.4)
end
-------------------------FuBenTeamCommonBossRankCell-----------------
FuBenTeamCommonBossRankCell = FuBenTeamCommonBossRankCell or BaseClass(BaseRender)
function FuBenTeamCommonBossRankCell:__init()
end

function FuBenTeamCommonBossRankCell:__delete()

end

function FuBenTeamCommonBossRankCell:OnFlush()
    if not self.data then
        return
    end

    self.node_list.name.text.text = self.data.role_name

    local damage = CommonDataManager.ConverNumber(self.data.hurt_count)
	self.node_list.damage.text.text = damage
    self.node_list.per_bg.slider.value = self.data.hurt_count / FuBenTeamCommonBossWGData.Instance:GetNormalHurtInfoMaxValue()

    local bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_4")

	if self.index < 4 then
        bg_bundle, bg_asset = ResPath.GetCommonImages("a3_hurt_list_bg_" .. self.index)
		self.node_list["icon"]:SetActive(true)
		self.node_list["icon"].image:LoadSprite(ResPath.GetCommonImages("a3_hurt_list_rank_" .. self.index))
		self.node_list.num.text.text = ""
	else
		self.node_list["icon"]:SetActive(false)
		self.node_list.num.text.text = self.index
    end

    self.node_list.per_bg.image:LoadSprite(bg_bundle, bg_asset)
end
