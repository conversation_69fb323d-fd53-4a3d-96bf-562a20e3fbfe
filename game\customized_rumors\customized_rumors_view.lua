CustomizedRumorsView = CustomizedRumorsView or BaseClass(SafeBaseView)

local CUSTOMIZED_RUMORS_BG = {
	[10] = "a2_dzcw_bg1",
	[20] = "a2_dzcw_bg7",
}

function CustomizedRumorsView:__init()
	self.default_index = TabIndex.customized_rumors
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
    local bundle = "uis/view/customized_rumors_ui_prefab"
    self:AddViewResource(0, bundle, "layout_customized_rumors_bg")
    self:AddViewResource(TabIndex.customized_rumors, bundle, "layout_customized_rumors_view")
    self:AddViewResource(TabIndex.customized_rumors_broadcast, bundle, "layout_customized_rumors_broadcast_view")
    self:AddViewResource(0, bundle, "VerticalTabbar")
    self:AddViewResource(0, bundle, "layout_customized_rumors_top_panel")


    self.tab_sub = {}
	self.remind_tab = {
        {RemindName.CustomizedRumors},
		{RemindName.CustomizedRumorsBroadcast},
	}

    self.last_show_index = -1
end

function CustomizedRumorsView:LoadCallBack()
    if not self.tabbar then
        local ver_path = "uis/view/customized_rumors_ui_prefab"
		self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetVerTabbarNameImgRes(ResPath.GetCustomizedRumorsImg(), "a2_dzcw_nav_icon")
		self.tabbar:Init(Language.CustomizedRumors.TabGrop, self.tab_sub, ver_path, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.CustomizedRumorsView, self.tabbar)
	end
end

function CustomizedRumorsView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self.last_show_index = nil
	self.bg_res_cache = nil

    self:RumorsReleaseCallBack()
    self:BroadcastReleaseCallBack()
end

function CustomizedRumorsView:LoadIndexCallBack(index)
    if index == TabIndex.customized_rumors then
        self:RumorsLoadIndexCallBack()
    elseif index == TabIndex.customized_rumors_broadcast then
        self:BroadcastLoadIndexCallBack()
    end
end

function CustomizedRumorsView:ShowIndexCallBack(index)
    self.node_list.title_view_name.text.text = Language.CustomizedRumors.TabGrop[index / 10]

    self:ChangeCustomizedRumorsBg(index)

    if index == TabIndex.customized_rumors then
        self:RumorsShowIndexCallBack()
    elseif index == TabIndex.customized_rumors_broadcast then
        self:BroadcastShowIndexCallBack()
    end

    if index ~= self.last_show_index then
        self:ChangeIndexCallBack(index)
    end
end

function CustomizedRumorsView:ChangeIndexCallBack(index)
    if self.last_show_index == TabIndex.customized_rumors then
        self:RumorsChangeIndexCallBack()
    elseif self.last_show_index == TabIndex.customized_rumors_broadcast then
        self:BroadcastChangeIndexCallBack()
    end

    self.last_show_index = index
end

function CustomizedRumorsView:ChangeCustomizedRumorsBg(index)
	local bg_str = CUSTOMIZED_RUMORS_BG[index] or CUSTOMIZED_RUMORS_BG[11]

	if nil == self.bg_res_cache or self.bg_res_cache ~= bg_str then
		self.bg_res_cache = bg_str
		local bundle, asset = ResPath.GetRawImagesPNG(bg_str)
		self.node_list.customized_rumors_big_bg.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.customized_rumors_big_bg.raw_image:SetNativeSize()
		end)
	end
end

function CustomizedRumorsView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.customized_rumors then
                self:RumorsOnFlush()
            elseif index == TabIndex.customized_rumors_broadcast then
                self:BroadcastOnFlush()
            end
        end
    end
end

function CustomizedRumorsView:RightPanleShowTween(right_node, canvas_group_node)
	RectTransform.SetAnchoredPositionXY(right_node.rect, 500, 0)
	right_node.rect:DOAnchorPos(Vector2(0, 0), 0.8)
	canvas_group_node.canvas_group.alpha = 0
	canvas_group_node.canvas_group:DoAlpha(0, 1, 0.3)
end