local Star_X = -99
local Desc_X = 69

FuBenStarView = FuBenStarView or BaseClass(SafeBaseView)

function FuBenStarView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_star_view")
	self.active_close = false

	self.fb_star = -1
	self.data = nil
	self.begin_time = 0
	self.over_time = 0
	self.tweener_list = {}
	self.is_move_trans_active = false
	self.is_bg_active = false
	self.close_tween = nil
end

function FuBenStarView:ReleaseCallBack()
    if self.arrow_click_event then
		GlobalEventSystem:UnBind(self.arrow_click_event)
		self.arrow_click_event = nil
	end
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	for i, v in ipairs(self.tweener_list) do
		v:Kill()
	end
	self.tweener_list = {}

	self.is_move_trans_active = false
	self.is_bg_active = false

	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

function FuBenStarView:LoadCallBack()
    self.arrow_click_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ArrowClickChange, self))

	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera, self.node_list.pos_2.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.layout_fuben.rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	self.star_move_pos_1 = self.node_list.pos_1.transform.position.y
	self.star_move_pos_2 = local_position_tbl.y--self.node_list.pos_2.transform.position.y
	self.star_move_pos_3 = self.node_list.pos_3.transform.position.y
	self.node_list.move_trans.transform.position = self.node_list.pos_3.transform.position

	--初始显隐
	self.node_list.move_trans:SetActive(false)
	self.node_list.bg:SetActive(false)
	self.is_move_trans_active = false
	self.is_bg_active = false
end

function FuBenStarView:_SetActiveMoveTrans(is_active)
	if self.is_move_trans_active == is_active then
		return
	end
	self.is_move_trans_active = is_active
	self.node_list.move_trans:SetActive(is_active)
end

function FuBenStarView:_SetActiveBg(is_active)
	if self.is_bg_active == is_active then
		return
	end
	self.is_bg_active = is_active
	self.node_list.bg:SetActive(is_active)
end

function FuBenStarView:ShowIndexCallBack()
	self:Flush()
end

function FuBenStarView:OpenCallBack()
    self.need_ani = true
end

function FuBenStarView:SetData(data)
	self.data = data
end

function FuBenStarView:OnFlush()
    if nil == self.data then
        return
    end
    local scene_info = FuBenWGData.Instance:GetStarViewFbSceneInfo()
	if IsEmptyTable(scene_info) then
		return
	end
	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	if scene_info.all_monster_dead then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
			self.node_list["per_boss_tip_2"].text.text = ""
		end
		return
	end

	self.begin_time = scene_info.flush_timestamp
	self.over_time = scene_info.time_out_stamp
	self.next_star_timestamp = scene_info.next_star_timestamp

	if self.timer_quest == nil then
		self:UpdateTime()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateTime, self), 1)
	end
end

function FuBenStarView:UpdateTime()
	local scene_info = FuBenWGData.Instance:GetStarViewFbSceneInfo()
	if nil == scene_info then
		return
	end

	if scene_info.is_finish ~= 0 then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
		end
		return
	end

	if scene_info.all_monster_dead then
		if self.timer_quest then
			GlobalTimerQuest:CancelQuest(self.timer_quest)
			self.timer_quest = nil
			self.node_list["per_boss_tip_2"].text.text = ""
		end
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.need_ani and self.begin_time <= server_time then
		self:DoAni()
		self.need_ani = false
		--else
		--	self:_SetActiveBg(true)
		--	self:_SetActiveMoveTrans(true)
	end

	-- 刷怪到现在的时间(总秒数)
	local pass_time = server_time - self.begin_time
	local star = self:GetStar()
	self.star_num_record = star

	for i = 1, 3 do
		if star < i then
			self.node_list["img_star_" .. i]:SetActive(false)
		else
			self.node_list["img_star_" .. i]:SetActive(true)
		end
	end

	local cur_star_total_time = self.data["time" .. star]
	if cur_star_total_time then
		--当前经过的星星时间
		local star_time = self:GetTime(star)
		--当前星星的经过时间
		local cur_pass_time = pass_time - star_time
		if star > 0 then
			self.node_list["img_star_" .. star].image.fillAmount = (cur_star_total_time - cur_pass_time) / cur_star_total_time
		end

		local time_value = 0
		if self.next_star_timestamp and self.next_star_timestamp > 0 and self.next_star_timestamp - server_time > 0 then
			time_value = self.next_star_timestamp - server_time
		else
			time_value = cur_star_total_time - cur_pass_time
		end

		time_value = time_value <= 0 and 0 or time_value
		if time_value > 0 then
			self.node_list["per_boss_tip_2"].text.text = string.format(Language.FuBenPanel.StarSecond, time_value)
		else
			self.node_list["per_boss_tip_2"].text.text = ""
		end
	end

	self:UpdateStr(star)
end

function FuBenStarView:GetTime(star)
	local total_time = 0
	for i = 0, 3 do
		if star < i then
			total_time = total_time + self.data["time" .. i]
		end
	end
	return total_time
end

function FuBenStarView:UpdateStr(star)
	local per = self.data["per" .. star]
	if per then
		self.node_list["per_boss_tip_3"].text.text = string.format(Language.FuBenPanel.CommonFBDropDesc, Language.FuBenPanel.StarC[star], per)
	end
end

function FuBenStarView:GetStar()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local pass_time = server_time - self.begin_time
	local star = 3
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.HIGH_TEAM_EQUIP_FB then
		local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
		star = scene_info.cur_star_num
	elseif scene_type == SceneType.COPPER_FB then
		local scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
		star = scene_info.cur_star_num
	elseif scene_type == SceneType.FakePetFb or scene_type == SceneType.PET_FB then
		local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
		if pet_scence_info and not IsEmptyTable(pet_scence_info) then
			star = pet_scence_info.cur_star_num
		end
	elseif scene_type == SceneType.TIAN_SHEN_FB then
		if pass_time < self.data.time3 then
			star = 3
		elseif pass_time < self.data.time2 + self.data.time3 then
			star = 2
		elseif pass_time < self.data.time1 + self.data.time2 + self.data.time3 then
			star = 1
		elseif pass_time < self.data.time0 + self.data.time1 + self.data.time2 + self.data.time3 then
			star = 0
		end
	else
		if pass_time < self.data.time3 then
			star = 3
		elseif pass_time < self.data.time2 + self.data.time3 then
			star = 2
		elseif pass_time < self.data.time1 + self.data.time2 + self.data.time3 then
			star = 1
		elseif self.data.time0 and pass_time < self.data.time0 + self.data.time1 + self.data.time2 + self.data.time3 then
			star = 0
		else
			star = 0
		end
	end
	return star
end

function FuBenStarView:ArrowClickChange(ison)
	if not self.node_list or not self.node_list.layout_fuben then
		return
	end
	self.node_list.layout_fuben:SetActive(not ison)
end

function FuBenStarView:DoAni()
	if not self:IsLoadedIndex(0) then self.is_need_playani = true return end

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:AppendCallback(function()
		self:_SetActiveMoveTrans(true)
		self.node_list.move_trans.transform.localScale = Vector3(2, 2, 2)
		self.node_list.star_scale.transform.localScale = Vector3(1.7, 1.7, 1.7)
		local tweener = self.node_list.move_trans.rect:DOScale(Vector3(1, 1, 1), 0.3):SetEase(DG.Tweening.Ease.OutBack) --0.1 > 从最大回到原来大小的时间
		table.insert(self.tweener_list, tweener)
    end)
	self.sequence:AppendInterval(0.6)
	self.sequence:AppendCallback(function()
    	local tweener1 = self.node_list.move_trans.transform:DOLocalMoveY(self.star_move_pos_2, 0.5):SetEase(DG.Tweening.Ease.Linear):OnComplete(function()
			self:_SetActiveBg(true)
		end)
		local tweener2 = self.node_list.star_scale.transform:DOScale(Vector3(1, 1, 1), 0.3)
		table.insert(self.tweener_list, tweener1)
		table.insert(self.tweener_list, tweener2)
    end)
	self.sequence:AppendInterval(0.8)
	self.sequence:AppendCallback(function()
    	local tweener1 = self.node_list.star_trans.transform:DOLocalMoveX(Star_X, 0.2):SetEase(DG.Tweening.Ease.Linear)
		local tweener2 = self.node_list.desc_trans.transform:DOLocalMoveX(Desc_X, 0.2):SetEase(DG.Tweening.Ease.Linear)
		table.insert(self.tweener_list, tweener1)
		table.insert(self.tweener_list, tweener2)
    end)
end

function FuBenStarView:GetCurStarNum()
	return self.star_num_record or 0
end