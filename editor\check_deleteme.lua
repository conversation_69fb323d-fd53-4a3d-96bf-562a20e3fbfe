local CheckDeleteMe = {}

local del_view_name = ""
local un_deleted_list = {}

local IgnoreCheckKeys = {
    ["__userdata__"] = true,
    ["__raw_image_loader"] = true,
    ["__attach_gameobj_loader"] = true,
    ["__loader_owner"] = true,
   	["view_loader"] = true,
   	["view_effect"] = true,
   	["view_render"] = true,
}

function CheckDeleteMe:Update()
	self:OutputReport()
end

function CheckDeleteMe:IsCfgObj(obj)
	local metable = getmetatable(obj)
	return nil ~= metable and nil ~= rawget(metable, "__pairs")
end

function CheckDeleteMe:OnReleaseView(view)
	if nil ~= view.view_name and "" ~= view.view_name then
		del_view_name = view.view_name
	elseif nil ~= view.ui_config then
		del_view_name = view.ui_config[1]
	end

	self:CheckChildDelete(view)
	del_view_name = ""
end

function CheckDeleteMe:OnDeleteObj(obj)
	self:CheckChildDelete(obj)
	obj.__is_deleted = true
end

function CheckDeleteMe:ShouldIgnoreObject(obj, key)
	return "table" ~= type(obj) or
		   obj.is_component or
		   IgnoreCheckKeys[key] or
		   self:IsCfgObj(obj)
end

function CheckDeleteMe:RecordUnDeletedObject(obj, key_path)
	if not obj.__is_deleted then
		obj.__view_name = del_view_name
		table.insert(un_deleted_list, {obj, key_path, debug.traceback()})
	end
end

function CheckDeleteMe:CheckChildDelete(obj, parent_key_path)
	if IsGameStop or nil == obj or self:ShouldIgnoreObject(obj, nil) then
		return
	end

	parent_key_path = parent_key_path or ""

	for key, child in pairs(obj) do
		if not self:ShouldIgnoreObject(child, key) then
			local current_key_path = parent_key_path == "" and key or string.format("%s[%s]", parent_key_path, tostring(key))
			if nil ~= child.DeleteMe then
				self:RecordUnDeletedObject(child, current_key_path)
			else
				self:CheckChildDelete(child, current_key_path)
			end
		end
	end
end

function CheckDeleteMe:OutputReport()
	if #un_deleted_list > 0 then
		for _, v in pairs(un_deleted_list) do
			if nil ~= v[1] and not v[1].__is_deleted then
				local msg = string.format(" Do you remember call 'DeleteMe' ? key = %s\n%s", v[2], v[3] or v[3] or "")
				if "" ~= v[1].__view_name and nil ~= v[1].__view_name then
					msg = string.format("view_name %s, %s", v[1].__view_name, msg)
				else
					msg = msg .. "\nBecause not found view_name, so print detail :\n"
					for k2, v2 in pairs(v[1]) do
						msg = msg .. k2 .. '=' .. tostring(v2) .. "	"
					end
				end

				UnityEngine.Debug.LogError(msg)
			end
		end

		un_deleted_list = {}
	end
end

function CheckDeleteMe:OnGameStop()
	self:OutputReport()
end

-- 测试函数：验证复杂嵌套结构的检测能力
function CheckDeleteMe:TestComplexNesting()
	-- 模拟一个复杂的嵌套结构
	local test_obj = {
		simple_item = {DeleteMe = function() end, __is_deleted = false},  -- 单个对象
		array_items = {  -- 数组
			[0] = {DeleteMe = function() end, __is_deleted = false},
			[1] = {DeleteMe = function() end, __is_deleted = false},
		},
		nested_arrays = {  -- 数组套数组
			level1 = {
				[0] = {DeleteMe = function() end, __is_deleted = false},
				level2 = {
					[0] = {DeleteMe = function() end, __is_deleted = false},
					[1] = {DeleteMe = function() end, __is_deleted = false},
				}
			}
		},
		mixed_structure = {  -- 混合结构
			normal_obj = {DeleteMe = function() end, __is_deleted = false},
			array_in_obj = {
				[0] = {DeleteMe = function() end, __is_deleted = false},
				nested_obj = {
					deep_item = {DeleteMe = function() end, __is_deleted = false}
				}
			}
		}
	}

	-- 设置测试环境
	del_view_name = "TestView"
	un_deleted_list = {}

	-- 执行检测
	self:CheckChildDelete(test_obj)

	-- 输出结果
	print_error("=== 复杂嵌套结构检测测试 ===")
	for i, v in ipairs(un_deleted_list) do
		print_error(string.format("发现未删除对象 #%d: %s", i, v[2]))
	end
	print_error("=== 测试完成 ===")

	-- 清理测试环境
	del_view_name = ""
	un_deleted_list = {}
end

return CheckDeleteMe
