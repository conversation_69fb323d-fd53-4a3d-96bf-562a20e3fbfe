local CheckDeleteMe = {}

local del_view_name = ""
local un_deleted_list = {}

local IgnoreCheckKeys = {
    ["__userdata__"] = true,
    ["__raw_image_loader"] = true,
    ["__attach_gameobj_loader"] = true,
    ["__loader_owner"] = true,
   	["view_loader"] = true,
   	["view_effect"] = true,
   	["view_render"] = true,
}

function CheckDeleteMe:Update()
	self:OutputReport()
end

function CheckDeleteMe:IsCfgObj(obj)
	local metable = getmetatable(obj)
	return nil ~= metable and nil ~= rawget(metable, "__pairs")
end

function CheckDeleteMe:OnReleaseView(view)
	if nil ~= view.view_name and "" ~= view.view_name then
		del_view_name = view.view_name
	elseif nil ~= view.ui_config then
		del_view_name = view.ui_config[1]
	end

	self:CheckChildDelete(view)
	del_view_name = ""
end

function CheckDeleteMe:OnDeleteObj(obj)
	self:CheckChildDelete(obj)
	obj.__is_deleted = true
end

function CheckDeleteMe:ShouldIgnoreObject(obj, key)
	return "table" ~= type(obj) or
		   obj.is_component or
		   IgnoreCheckKeys[key] or
		   self:IsCfgObj(obj)
end

function CheckDeleteMe:RecordUnDeletedObject(obj, key_path)
	if not obj.__is_deleted then
		obj.__view_name = del_view_name
		table.insert(un_deleted_list, {obj, key_path, debug.traceback()})
	end
end

function CheckDeleteMe:CheckChildDelete(obj, parent_key_path, visited)
	if IsGameStop or nil == obj or self:ShouldIgnoreObject(obj, nil) then
		return
	end

	-- 防止循环引用导致的无限递归
	visited = visited or {}
	if visited[obj] then
		return
	end
	visited[obj] = true

	parent_key_path = parent_key_path or ""

	for key, child in pairs(obj) do
		if not self:ShouldIgnoreObject(child, key) then
			-- 安全的路径构建
			local current_key_path
			if parent_key_path == "" then
				current_key_path = tostring(key)
			else
				current_key_path = string.format("%s[%s]", parent_key_path, tostring(key))
			end

			if nil ~= child.DeleteMe then
				self:RecordUnDeletedObject(child, current_key_path)
			else
				self:CheckChildDelete(child, current_key_path, visited)
			end
		end
	end

	-- 清理访问标记（避免影响其他分支的检查）
	visited[obj] = nil
end

function CheckDeleteMe:OutputReport()
	if #un_deleted_list > 0 then
		for _, v in pairs(un_deleted_list) do
			if nil ~= v[1] and not v[1].__is_deleted then
				local msg = string.format(" Do you remember call 'DeleteMe' ? key = %s\n%s", v[2], v[3] or v[3] or "")
				if "" ~= v[1].__view_name and nil ~= v[1].__view_name then
					msg = string.format("view_name %s, %s", v[1].__view_name, msg)
				else
					msg = msg .. "\nBecause not found view_name, so print detail :\n"
					for k2, v2 in pairs(v[1]) do
						msg = msg .. k2 .. '=' .. tostring(v2) .. "	"
					end
				end

				UnityEngine.Debug.LogError(msg)
			end
		end

		un_deleted_list = {}
	end
end

function CheckDeleteMe:OnGameStop()
	self:OutputReport()
end



return CheckDeleteMe
