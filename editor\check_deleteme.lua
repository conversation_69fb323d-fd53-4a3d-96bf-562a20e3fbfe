local CheckDeleteMe = {}

local del_view_name = ""
local un_deleted_list = {}

local IgnoreCheckKeys = {
    ["__userdata__"] = true,
    ["__raw_image_loader"] = true,
    ["__attach_gameobj_loader"] = true,
    ["__loader_owner"] = true,
   	["view_loader"] = true,
   	["view_effect"] = true,
   	["view_render"] = true,
}

function CheckDeleteMe:Update()
	self:OutputReport()
end

function CheckDeleteMe:IsCfgObj(obj)
	local metable = getmetatable(obj)
	return nil ~= metable and nil ~= rawget(metable, "__pairs")
end

function CheckDeleteMe:OnReleaseView(view)
	if nil ~= view.view_name and "" ~= view.view_name then
		del_view_name = view.view_name
	elseif nil ~= view.ui_config then
		del_view_name = view.ui_config[1]
	end

	self:CheckChildDelete(view)
	del_view_name = ""
end

function CheckDeleteMe:OnDeleteObj(obj)
	self:CheckChildDelete(obj)
	obj.__is_deleted = true
end

function CheckDeleteMe:CheckChildDelete(obj)
	if IsGameStop or nil == obj or "table" ~= type(obj) or self:IsCfgObj(obj) then
		return
	end

	for k1, v1 in pairs(obj) do
		if "table" == type(v1) and not v1.is_component and not IgnoreCheckKeys[k1] and not self:IsCfgObj(v1) then
			if nil ~= v1.DeleteMe then
				if not v1.__is_deleted then
					v1.__view_name = del_view_name
					table.insert(un_deleted_list, {v1, k1, debug.traceback()})
				end
			else
				-- 检查是否是数组类型的table（包含有DeleteMe方法的对象）
				local has_deleteme_objects = false
				for k2, v2 in pairs(v1) do
					if "table" == type(v2) and nil ~= v2.DeleteMe and not IgnoreCheckKeys[k2] then
						has_deleteme_objects = true
						if not v2.__is_deleted then
							v2.__view_name = del_view_name
							table.insert(un_deleted_list, {v2, string.format("%s[%s]", k1, tostring(k2)), debug.traceback()})
						end
					end
				end

				-- 如果不是包含DeleteMe对象的数组，则继续递归检查
				if not has_deleteme_objects then
					for k2, v2 in pairs(v1) do
						if not IgnoreCheckKeys[k2] then
							self:CheckChildDelete(v2)
						end
					end
				end
			end
		end
	end
end

function CheckDeleteMe:OutputReport()
	if #un_deleted_list > 0 then
		for _, v in pairs(un_deleted_list) do
			if nil ~= v[1] and not v[1].__is_deleted then
				local msg = string.format(" Do you remember call 'DeleteMe' ? key = %s\n%s", v[2], v[3] or v[3] or "")
				if "" ~= v[1].__view_name and nil ~= v[1].__view_name then
					msg = string.format("view_name %s, %s", v[1].__view_name, msg)
				else
					msg = msg .. "\nBecause not found view_name, so print detail :\n"
					for k2, v2 in pairs(v[1]) do
						msg = msg .. k2 .. '=' .. tostring(v2) .. "	"
					end
				end

				UnityEngine.Debug.LogError(msg)
			end
		end

		un_deleted_list = {}
	end
end

function CheckDeleteMe:OnGameStop()
	self:OutputReport()
end

return CheckDeleteMe
