local CheckDeleteMe = {}

local del_view_name = ""
local un_deleted_list = {}

local IgnoreCheckKeys = {
    ["__userdata__"] = true,
    ["__raw_image_loader"] = true,
    ["__attach_gameobj_loader"] = true,
    ["__loader_owner"] = true,
   	["view_loader"] = true,
   	["view_effect"] = true,
   	["view_render"] = true,
}

function CheckDeleteMe:Update()
	self:OutputReport()
end

function CheckDeleteMe:IsCfgObj(obj)
	local metable = getmetatable(obj)
	return nil ~= metable and nil ~= rawget(metable, "__pairs")
end

function CheckDeleteMe:OnReleaseView(view)
	if nil ~= view.view_name and "" ~= view.view_name then
		del_view_name = view.view_name
	elseif nil ~= view.ui_config then
		del_view_name = view.ui_config[1]
	end

	self:CheckChildDelete(view)
	del_view_name = ""
end

function CheckDeleteMe:OnDeleteObj(obj)
	self:CheckChildDelete(obj)
	obj.__is_deleted = true
end

-- 检查对象是否应该被忽略
function CheckDeleteMe:ShouldIgnoreObject(obj, key)
	return "table" ~= type(obj) or
		   obj.is_component or
		   IgnoreCheckKeys[key] or
		   self:IsCfgObj(obj)
end

-- 记录未删除的对象
function CheckDeleteMe:RecordUnDeletedObject(obj, key_path)
	if not obj.__is_deleted then
		obj.__view_name = del_view_name
		table.insert(un_deleted_list, {obj, key_path, debug.traceback()})
	end
end

-- 检查数组类型的table，返回是否包含DeleteMe对象
function CheckDeleteMe:CheckArrayObjects(array_table, parent_key)
	local has_deleteme_objects = false

	for key, obj in pairs(array_table) do
		if not self:ShouldIgnoreObject(obj, key) and nil ~= obj.DeleteMe then
			has_deleteme_objects = true
			self:RecordUnDeletedObject(obj, string.format("%s[%s]", parent_key, tostring(key)))
		end
	end

	return has_deleteme_objects
end

-- 递归检查子对象
function CheckDeleteMe:RecursiveCheckChildren(parent_table)
	for key, child in pairs(parent_table) do
		if not IgnoreCheckKeys[key] then
			self:CheckChildDelete(child)
		end
	end
end

function CheckDeleteMe:CheckChildDelete(obj)
	-- 早期返回：跳过无效或应忽略的对象
	if IsGameStop or nil == obj or self:ShouldIgnoreObject(obj, nil) then
		return
	end

	for key, child in pairs(obj) do
		-- 跳过应忽略的子对象
		if self:ShouldIgnoreObject(child, key) then
			goto continue
		end

		-- 检查单个对象是否有DeleteMe方法
		if nil ~= child.DeleteMe then
			self:RecordUnDeletedObject(child, key)
		else
			-- 检查是否为包含DeleteMe对象的数组
			local has_deleteme_objects = self:CheckArrayObjects(child, key)

			-- 如果不是DeleteMe对象数组，则递归检查
			if not has_deleteme_objects then
				self:RecursiveCheckChildren(child)
			end
		end

		::continue::
	end
end

function CheckDeleteMe:OutputReport()
	if #un_deleted_list > 0 then
		for _, v in pairs(un_deleted_list) do
			if nil ~= v[1] and not v[1].__is_deleted then
				local msg = string.format(" Do you remember call 'DeleteMe' ? key = %s\n%s", v[2], v[3] or v[3] or "")
				if "" ~= v[1].__view_name and nil ~= v[1].__view_name then
					msg = string.format("view_name %s, %s", v[1].__view_name, msg)
				else
					msg = msg .. "\nBecause not found view_name, so print detail :\n"
					for k2, v2 in pairs(v[1]) do
						msg = msg .. k2 .. '=' .. tostring(v2) .. "	"
					end
				end

				UnityEngine.Debug.LogError(msg)
			end
		end

		un_deleted_list = {}
	end
end

function CheckDeleteMe:OnGameStop()
	self:OutputReport()
end

return CheckDeleteMe
