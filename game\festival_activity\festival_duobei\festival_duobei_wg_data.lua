FestivalActDuoBeiWGData = FestivalActDuoBeiWGData or BaseClass()

function FestivalActDuoBeiWGData:__init()
	if FestivalActDuoBeiWGData.Instance then
		ErrorLog("[FestivalActDuoBeiWGData] Attemp to create a singleton twice !")
	end
	FestivalActDuoBeiWGData.Instance = self
	self:LoadConfig()

	FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_DUOBEI_2, {[1] = MERGE_EVENT_TYPE.LEVEL},
		BindTool.Bind(self.GetActCanOpen, self))
end

function FestivalActDuoBeiWGData:__delete()
	FestivalActDuoBeiWGData.Instance = nil
end

function FestivalActDuoBeiWGData:GetDuoBeiCfg()
	return self.duobei_cfg
end

function FestivalActDuoBeiWGData:LoadConfig()
	self.other_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_duobei_cfg_auto").other
	self.duobei_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_duobei_cfg_auto").duobei
	self.param_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_duobei_cfg_auto").config_param
	self.param_cur_cfg = ListToMap(self.param_cfg, "grade")
end


function FestivalActDuoBeiWGData:GetCurParamCfg()
    local grade = self.duobei_data.grade or 0
    local cfg = self.param_cur_cfg[grade]
    return cfg or {}
end

--是否开启
function FestivalActDuoBeiWGData:GetActCanOpen()
	if nil == self.duobei_data or nil == self.duobei_data.grade then
        return false
    end

	local grade = self.duobei_data.grade or 0
	local cfg = self.param_cur_cfg[grade]
    if nil == cfg then
        
		return false
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= cfg.open_role_level then
		return true
	end
	return false
end

function FestivalActDuoBeiWGData:SetDuoBeiInfo(protocol)
	self.duobei_data = protocol.data
	MainuiWGCtrl.Instance:FlushDuoBei()
end

function FestivalActDuoBeiWGData:GetDuoBeiInfo()
	if not self.duobei_data then
		return nil
	end

	local grade = self.duobei_data.grade or 1
	local cfg = self:GetDuoBeiCfg()
	-- local day = FestivalActivityWGData.Instance:GetMergeDayInfo()
	local day = self.duobei_data.day
	local list = {}

	for k,v in pairs(cfg) do
		if v.grade == grade and v.day == day then
			local info = {}
			info.cfg = v
			info.cur_finish_num = 0
			if self.duobei_data.task_info[v.task_type] then
				info.cur_finish_num = self.duobei_data.task_info[v.task_type].cur_finish_num
			end
			table.insert(list, info)
		end
	end
	return list
end

--获取活动的结束时间
function FestivalActDuoBeiWGData:GetActivityInValidTime()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_OA_DUOBEI_2)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return  0
end

--获取副本是否有多倍活动开启
function FestivalActDuoBeiWGData:GetHasDuoBeiInCopy()
	for i=RATSDUOBEI_TASK.FANRENXIUXIAN, RATSDUOBEI_TASK.HAIDIFEIXU do
		if self:GetDuoBeiTimes(i) > 0 then
			return true
		end
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.WABAO) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANGUDIAN) > 0 then
		return true
	end
	
	return false
end

--获取魔王是否有boss开启
function FestivalActDuoBeiWGData:GetHasDuoBeiInBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.SHIJIEMOWANG) > 0 then
		return true
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.MOWANGCHAOXUE) > 0 then
		return true
	end 

	return false 
end

--获取世界服是否有boss开启
function FestivalActDuoBeiWGData:GetHasDuoBeiInWorldBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.HONGMENGSHENYU) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANSHENSHOU) > 0 then
		return true
	end
	return false 
end

function FestivalActDuoBeiWGData:GetDuoBeiTimes(task_type)
	if self.duobei_data ~= nil and self:GetActivityState() then
		local cfg = self:GetDuoBeiCfg()
		if not cfg then
			return 0
		end
		
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day and v.task_type == task_type then
				if FunOpen.Instance:GetFunIsOpened(v.module_name) then
					return v.reward_mult
				end
			end
		end
	end
	return 0
end

function FestivalActDuoBeiWGData:GetItemDuoBeiTimes(task_type , item_id)
	if self.duobei_data ~= nil and self:GetActivityState() then
		local cfg = self:GetDuoBeiCfg()
		if not cfg then
			return 0
		end
		
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day and v.task_type == task_type then
				for k2,v2 in pairs(v.reward_item) do
					if v2.item_id == item_id then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

--获取活动是否开启
function FestivalActDuoBeiWGData:GetActivityState()
	return FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_DUOBEI_2)
end