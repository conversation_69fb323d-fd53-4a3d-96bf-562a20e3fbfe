-- 离线卡使用
----------------------------------
BatchUseChooseGiftView = BatchUseChooseGiftView or BaseClass(SafeBaseView)

function BatchUseChooseGiftView:__init()
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
	self.default_num = nil
	self.cur_num = 0
	self.bag_index = nil
end

function BatchUseChooseGiftView:LoadConfig()
	-- self.default_index = 1
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 440)})
	self:AddViewResource(0, "uis/view/choose_gift_prefab", "batch_use_choose_gift")
	self.offline_id = {
		[22536] = 3 * 3600,
		[22537] = 6 * 3600,
		[22538] = 5 * 3600,
	}
end

function BatchUseChooseGiftView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.default_num = nil
	self.cur_num = 0
	self.default_show_num = 0
	self.bag_index = nil
end

function BatchUseChooseGiftView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Bag.PiLiangHuoqu
	-- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(-10, 16.5)
	-- self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(738, 416.7)
	self.node_list.slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function BatchUseChooseGiftView:SetData(gift_id,item_id,num,show_num,bag_index)
	self.gift_id = gift_id
	self.item_id = item_id
	self.default_num = num
	self.default_show_num = show_num
	self.bag_index = bag_index
	self:Open()
end

function BatchUseChooseGiftView:ShowIndexCallBack()
	self:Flush()
end


function BatchUseChooseGiftView:OnFlush()
	local item_num = 0
	if self.bag_index then
		item_num = ItemWGData.Instance:GetItemNumInBagByIndex(self.bag_index, self.gift_id)
	else
		item_num = ItemWGData.Instance:GetItemNumInBagById(self.gift_id)
	end

	local select_gift_data = ItemWGData.Instance:GetItemListInGift(self.gift_id)
	
	local data_currect = false
	local star_count = 0
	for k,v in pairs(select_gift_data) do
		if v.item_id == self.item_id then
			data_currect = true
			star_count = v.star_count
			break
		end
	end
	
	if not data_currect then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.BagDataChange)
		self:Close()
		return
	end

	self.item_cell:SetData({item_id = self.item_id, num = item_num, is_bind = 1, star_level = star_count})

	local gift_cfg, _ = ItemWGData.Instance:GetItemConfig(self.gift_id)
	local cur_show_num = self.default_show_num or 1
	if gift_cfg.use_daytimes and gift_cfg.use_daytimes ~= 0 then
		local can_user_num = gift_cfg.use_daytimes - ItemWGData.Instance:GetItemUseTimes(self.gift_id)
		self.max = item_num <= can_user_num and item_num or can_user_num
		if self.max >= 999 then
			self.max = 999
		end
	else
		self.max = item_num
		if self.max >= 999 then
			self.max = 999
		end
	end

	self.node_list.slider.slider.maxValue = self.max
	self.node_list.slider.slider.minValue = 1
	self.node_list.slider.slider.value = cur_show_num

	if self.default_num then
		self.num = self.default_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.default_num
	else
		self.num = item_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.max
	end

	local name = ItemWGData.Instance:GetItemName(self.item_id)
	local color = ItemWGData.Instance:GetItemColor(self.item_id)
	self.node_list.lbl_item_name.text.text = ToColorStr(name,color)
end

function BatchUseChooseGiftView:SetConfirmCallBack(callback)
	self.confitm_callback = callback
end

function BatchUseChooseGiftView:OnClickConfirm()
	if self.confitm_callback then
		local cur_num = tonumber(self.node_list.lbl_num.text.text)
		self.confitm_callback(cur_num)
		self.confitm_callback = nil
	end
	self:Close()
end

function BatchUseChooseGiftView:OnClickCancel()
	self:Close()
end

function BatchUseChooseGiftView:OnClickSub()
	if self.max == 0 then return end
	self.cur_num = self.node_list.lbl_num.text.text - 1
	if self.cur_num <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
	self.cur_num = self.cur_num <= 1 and 1 or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function BatchUseChooseGiftView:OnClickAdd()
	if self.max == 0 then return end
	self.cur_num = self.node_list.lbl_num.text.text + 1
	if self.cur_num >= self.max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
	self.cur_num = self.cur_num >= self.max and self.max or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function BatchUseChooseGiftView:OnSoundValueChange(float_param)
	self.node_list.lbl_num.text.text = float_param

end

