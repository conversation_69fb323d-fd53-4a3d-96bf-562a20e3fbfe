---------------------------------道行Start------------------------------------
CSTaoistOperate = CSTaoistOperate or BaseClass(BaseProtocolStruct)
function CSTaoistOperate:__init()
	self.msg_type = 16100
end

function CSTaoistOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

local function GetMsgTaoistKeLingItem(slot)
	local data = {}
	data.item_id = MsgAdapter.ReadUShort()
	data.is_open = MsgAdapter.ReadShort()
	data.slot = slot
	return data
end

local function GetMsgTaoistCarveItem()
	local data = {}
	data.level = MsgAdapter.ReadInt()
	return data
end

local function GetMsgTaoistSlotItem(slot)
	local data = {}
	data.slot = slot
	data.item_id = MsgAdapter.ReadUShort()

	data.color = GameEnum.ITEM_COLOR_WHITE
	if data.item_id > 0 then
		local cfg_data = ItemWGData.Instance:GetItemConfig(data.item_id)

		if cfg_data and cfg_data.color then
			data.color = cfg_data.color
		end
	end

	data.level = MsgAdapter.ReadShort()
	data.grade = MsgAdapter.ReadInt()
	local carve_item_list = {}

	for i = 1, 3 do
		carve_item_list[i] = GetMsgTaoistCarveItem()
	end

	data.carve_item_list = carve_item_list

	local already_kaiguang = false
	local per_kaiguang_attr_value_list = {}

	for i = 0, 4 do
		per_kaiguang_attr_value_list[i] = MsgAdapter.ReadInt()

		if per_kaiguang_attr_value_list[i] ~= 0 then
			already_kaiguang = true
		end
	end
	data.has_kaiguang_value = already_kaiguang

	data.per_kaiguang_attr_value_list = per_kaiguang_attr_value_list
	local has_kaiguang_change_value = false
	local per_kaiguang_attr_value_list_temp = {}

	for i = 0, 4 do
		per_kaiguang_attr_value_list_temp[i] = MsgAdapter.ReadInt()
		if per_kaiguang_attr_value_list_temp[i] ~= 0 then
			already_kaiguang = true
			has_kaiguang_change_value = true
		end
	end

	data.already_kaiguang = already_kaiguang
	data.has_kaiguang_change_value = has_kaiguang_change_value
	data.per_kaiguang_attr_value_list_temp = per_kaiguang_attr_value_list_temp

	return data
end

SCTaoistInfo = SCTaoistInfo or BaseClass(BaseProtocolStruct)
function SCTaoistInfo:__init()
	self.msg_type = 16101
end

function SCTaoistInfo:Decode()
	self.rmb_buy_level = MsgAdapter.ReadInt()
	self.enhance_gongming_level = MsgAdapter.ReadInt()
	self.carve_gongming_level = MsgAdapter.ReadInt()
	self.inlay_four_gongming = MsgAdapter.ReadInt()
	self.inlay_eight_gongming = MsgAdapter.ReadInt()
	self.inlay_tw_gongming = MsgAdapter.ReadInt()

	local keling_item_list = {}

	for i = 0, 11 do                                   
		keling_item_list[i] = GetMsgTaoistKeLingItem(i)
	end

	self.keling_item_list = keling_item_list

	local slot_item_list = {}

	for i = 0, 7 do
		slot_item_list[i] = GetMsgTaoistSlotItem(i)
	end

	self.slot_item_list = slot_item_list

	self.suit_id_eight_item = MsgAdapter.ReadInt()
	self.touch_rmb_time = MsgAdapter.ReadInt()
	self.m_qu_id = MsgAdapter.ReadInt()
	self.rmb_buy_flag = MsgAdapter.ReadLL()
	self.touch_num = MsgAdapter.ReadInt()
end

SCTaoistSlotUpdate = SCTaoistSlotUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistSlotUpdate:__init()
	self.msg_type = 16102
end

function SCTaoistSlotUpdate:Decode()
	self.slot = MsgAdapter.ReadInt()
	self.slot_item = GetMsgTaoistSlotItem(self.slot)
end

SCTaoistHoleUpdate = SCTaoistHoleUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistHoleUpdate:__init()
	self.msg_type = 16103
end

function SCTaoistHoleUpdate:Decode()
	self.hole = MsgAdapter.ReadInt()
	self.inlay_item = GetMsgTaoistKeLingItem(self.hole)
end

SCTaoistRMBBuyUpdate = SCTaoistRMBBuyUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistRMBBuyUpdate:__init()
	self.msg_type = 16104
end

function SCTaoistRMBBuyUpdate:Decode()
	self.rmb_buy_level = MsgAdapter.ReadInt()
end

CSTaiostUseStone = CSTaiostUseStone or BaseClass(BaseProtocolStruct)
function CSTaiostUseStone:__init()
	self.msg_type = 16108
end

function CSTaiostUseStone:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.count)

	for i = 1, self.count do
		MsgAdapter.WriteUShort(self.msg_item[i].item_id or 0)
		MsgAdapter.WriteShort(self.msg_item[i].bag_index or -1)
		MsgAdapter.WriteInt(self.msg_item[i].hole or 0)
	end
end

CSTaoistDecompsEquip = CSTaoistDecompsEquip or BaseClass(BaseProtocolStruct)
function CSTaoistDecompsEquip:__init()
	self.msg_type = 16109
end

function CSTaoistDecompsEquip:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.count)

	for i = 1, self.count do
		MsgAdapter.WriteUShort(self.msg_item[i].item_id or 0)
		MsgAdapter.WriteShort(self.msg_item[i].bag_index or -1)
		MsgAdapter.WriteInt(self.msg_item[i].count or 0)
	end
end

local function GetMsgBagGrid()
	local grid_item = {}
	grid_item.index = MsgAdapter.ReadInt()
	grid_item.item_id = MsgAdapter.ReadUShort()
	grid_item.reserve_sh = MsgAdapter.ReadShort()
	grid_item.num = MsgAdapter.ReadInt()
	grid_item.is_bind = MsgAdapter.ReadChar()
	grid_item.param1 = MsgAdapter.ReadChar()
	grid_item.param2 = MsgAdapter.ReadChar()
	grid_item.param3 = MsgAdapter.ReadChar()

	grid_item.color = GameEnum.ITEM_COLOR_WHITE
	if grid_item.item_id > 0 then
		local cfg_data = ItemWGData.Instance:GetItemConfig(grid_item.item_id)
		if cfg_data and cfg_data.color then
			grid_item.color = cfg_data.color
		end
	end

	return grid_item
end

SCMsgBag = SCMsgBag or BaseClass(BaseProtocolStruct)
function SCMsgBag:__init()
    self.msg_type = 16112
end

function SCMsgBag:Decode()
	self.grid_count = MsgAdapter.ReadInt()
    local grid_list = {}

	for i = 1, self.grid_count do
        local grid_item = GetMsgBagGrid()
		grid_list[grid_item.index] = grid_item
    end

    self.grid_list = grid_list
end

SCMsgGrid = SCMsgGrid or BaseClass(BaseProtocolStruct)
function SCMsgGrid:__init()
    self.msg_type = 16113
end

function SCMsgGrid:Decode()
    self.grid_info = GetMsgBagGrid()
end

SCTaoistGongMingUpdate = SCTaoistGongMingUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistGongMingUpdate:__init()
	self.msg_type = 16114
end

function SCTaoistGongMingUpdate:Decode()
	self.enhance_gongming_level = MsgAdapter.ReadInt()
	self.carve_gongming_level = MsgAdapter.ReadInt()
end

SCTaoistInlayGongMingUpdate = SCTaoistInlayGongMingUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistInlayGongMingUpdate:__init()
	self.msg_type = 16117
end

function SCTaoistInlayGongMingUpdate:Decode()
	self.inlay_four_gongming = MsgAdapter.ReadInt()
	self.inlay_eight_gongming = MsgAdapter.ReadInt()
	self.inlay_tw_gongming = MsgAdapter.ReadInt()
end

SCTaoistTouchUpdate = SCTaoistTouchUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistTouchUpdate:__init()
	self.msg_type = 16118
end

function SCTaoistTouchUpdate:Decode()
	self.touch_time = MsgAdapter.ReadInt()
	self.touch_num = MsgAdapter.ReadInt()
end

SCTaoistQueUpdate = SCTaoistQueUpdate or BaseClass(BaseProtocolStruct)
function SCTaoistQueUpdate:__init()
	self.msg_type = 16119
end

function SCTaoistQueUpdate:Decode()
	self.que_id = MsgAdapter.ReadInt()
	self.result = MsgAdapter.ReadShort()
	self.choose = MsgAdapter.ReadShort()
end
---------------------------------道行End------------------------------------

-- 大表情操作
CSDabiaoqingOperate = CSDabiaoqingOperate or BaseClass(BaseProtocolStruct)
function CSDabiaoqingOperate:__init()
	self.msg_type = 16105
end

function CSDabiaoqingOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate)
	MsgAdapter.WriteInt(self.param1)
end

-- 大表情信息
SCDabiaoqingInfo = SCDabiaoqingInfo or BaseClass(BaseProtocolStruct)
function SCDabiaoqingInfo:__init()
	self.msg_type = 16106
end

function SCDabiaoqingInfo:Decode()
	self.dan_num_list = {}
	for i = 0, 8 do
		self.dan_num_list[i] = MsgAdapter.ReadInt()
	end
end

-- 神兵操作
CSShenbingOperate = CSShenbingOperate or BaseClass(BaseProtocolStruct)
function CSShenbingOperate:__init()
	self.msg_type = 16110
	self.operate = 0
	self.param1 = 0
	self.param2 = 0
	self.param3 = 0
	self.param4 = 0
end

function CSShenbingOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

-- 神兵信息
SCShenbingInfo = SCShenbingInfo or BaseClass(BaseProtocolStruct)
function SCShenbingInfo:__init()
	self.msg_type = 16111
	self.level_list = {}
end

function SCShenbingInfo:Decode()
	self.level_list = {}
	for i = 0, GameEnum.MAX_SHENBING_PART - 1 do
		self.level_list[i] = MsgAdapter.ReadShort()
	end
end

-- 五行神将操作
CSWuXingShenJiangOperate = CSWuXingShenJiangOperate or BaseClass(BaseProtocolStruct)
function CSWuXingShenJiangOperate:__init()
	self.msg_type = 16115
end

function CSWuXingShenJiangOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate or 0)
	MsgAdapter.WriteInt(self.param1 or 0)
end

-- 五行神将信息反馈
SCWuXingShenJiangInfo = SCWuXingShenJiangInfo or BaseClass(BaseProtocolStruct)
function SCWuXingShenJiangInfo:__init()
	self.msg_type = 16116
end

function SCWuXingShenJiangInfo:Decode()
	self.kill_boss_seq = MsgAdapter.ReadUInt()
end

--[[-- 好友祝福操作
CSFriendblessOperate = CSFriendblessOperate or BaseClass(BaseProtocolStruct)
function CSFriendblessOperate:__init()
	self.msg_type = 16125
end

function CSFriendblessOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate or 0)
	MsgAdapter.WriteInt(self.param1 or 0)
	MsgAdapter.WriteInt(self.param2 or 0)
end--]]

--[[-- 好友祝福通知
SCFriendblessNotice = SCFriendblessNotice or BaseClass(BaseProtocolStruct)
function SCFriendblessNotice:__init()
	self.msg_type = 16126
end

function SCFriendblessNotice:Decode()
	self.notice_type = MsgAdapter.ReadInt()
	self.operate_role_id = MsgAdapter.ReadInt()
	self.operate_role_name = MsgAdapter.ReadStrN(32)
	self.param1 = MsgAdapter.ReadInt()
end--]]


CSFootLightOperate = CSFootLightOperate or BaseClass(BaseProtocolStruct)
function CSFootLightOperate:__init()
	self.msg_type = 16124
end

function CSFootLightOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCFootLightInfo = SCFootLightInfo or BaseClass(BaseProtocolStruct)
function SCFootLightInfo:__init()
	self.msg_type = 16125 
end

function SCFootLightInfo:Decode()
	self.use_foot_id = MsgAdapter.ReadInt()
	self.all_foot_light_list = {}
	for i=0, SUPREME_FIELDS_NUMTYPE.MAX_FOOT_LIGHT do
		self.all_foot_light_list[i] = {}
		self.all_foot_light_list[i].is_use = self.use_foot_id == i
		self.all_foot_light_list[i].slots = {}
		for j=1, SUPREME_FIELDS_NUMTYPE.COUNT_FOOT_LIGHT_STONE do
			local vo = {}
			vo.itemId = MsgAdapter.ReadItemId()
			vo.level = MsgAdapter.ReadShort()
			self.all_foot_light_list[i].slots[j] = vo
		end
	end

	self.all_skill_slots = {}
	for i=0, SUPREME_FIELDS_NUMTYPE.MAX_FOOT_SKILL_NUM do
		local vo = {}
		vo.is_open = MsgAdapter.ReadInt() == 1
		vo.skill_ids = {}
		for j=1,2 do
			vo.skill_ids[j] = MsgAdapter.ReadUShort() 
		end
		self.all_skill_slots[i] = vo
	end
end

SCFootLightItemInfo = SCFootLightItemInfo or BaseClass(BaseProtocolStruct)
function SCFootLightItemInfo:__init()
	self.msg_type = 16126 
end

function SCFootLightItemInfo:Decode()
	self.foot_light_list = {}
	self.foot_id = MsgAdapter.ReadInt()
	for i=1, SUPREME_FIELDS_NUMTYPE.COUNT_FOOT_LIGHT_STONE do
		local vo = {}
		vo.itemId = MsgAdapter.ReadItemId()
		vo.level = MsgAdapter.ReadShort()
		self.foot_light_list[i] = vo
	end
end

SCFootLightCur = SCFootLightCur or BaseClass(BaseProtocolStruct)
function SCFootLightCur:__init()
	self.msg_type = 16127 
end

function SCFootLightCur:Decode()
	self.use_foot_id = MsgAdapter.ReadInt()
end

SCFootLightSkill = SCFootLightSkill or BaseClass(BaseProtocolStruct)
function SCFootLightSkill:__init()
	self.msg_type = 16128 
end

function SCFootLightSkill:Decode()
	self.all_skill_slots = {}
	for i=0, SUPREME_FIELDS_NUMTYPE.MAX_FOOT_SKILL_NUM do
		local vo = {}
		vo.is_open = MsgAdapter.ReadInt() == 1
		vo.skill_ids = {}
		for j=1,2 do
			vo.skill_ids[j] = MsgAdapter.ReadUShort()
		end
		
		self.all_skill_slots[i] = vo
	end
end

CSFootLightSKillAll = CSFootLightSKillAll or BaseClass(BaseProtocolStruct)
function CSFootLightSKillAll:__init()
	self.msg_type = 16129
end

function CSFootLightSKillAll:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.count)
	for i = 1, self.count do
		MsgAdapter.WriteShort(self.data_list[i].hole_id)
		MsgAdapter.WriteShort(self.data_list[i].foot_id)
		MsgAdapter.WriteInt(self.data_list[i].level)
	end
end


-- 法阵二技能 服务器通知播放
SCFootLightPlaySkillEff = SCFootLightPlaySkillEff or BaseClass(BaseProtocolStruct)
function SCFootLightPlaySkillEff:__init()
	self.msg_type = 16130
end

function SCFootLightPlaySkillEff:Decode()
	self.inc_id = MsgAdapter.ReadInt()			-- 自增id
	self.skill_id = MsgAdapter.ReadShort()
	self.obj_id = MsgAdapter.ReadShort()		-- 施法者的obj id
end

-- 法阵二技能 请求造成伤害
CSFootLightSkillAttack = CSFootLightSkillAttack or BaseClass(BaseProtocolStruct)
function CSFootLightSkillAttack:__init()
	self.msg_type = 16131
	self.inc_id = 0								-- 自增id
end

function CSFootLightSkillAttack:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.inc_id)
end






SCFootLightActive = SCFootLightActive or BaseClass(BaseProtocolStruct)
function SCFootLightActive:__init()
	self.msg_type = 16133 
end

function SCFootLightActive:Decode()
	self.foot_id = MsgAdapter.ReadInt()
end

-- -- 好友邀请操作
-- CSFriendinviteOperate = CSFriendinviteOperate or BaseClass(BaseProtocolStruct)
-- function CSFriendinviteOperate:__init()
-- 	self.msg_type = 16130 
-- end

-- function CSFriendinviteOperate:Encode()
-- 	MsgAdapter.WriteBegin(self.msg_type)
-- 	MsgAdapter.WriteInt(self.operate)	
-- 	MsgAdapter.WriteInt(self.param1)	
-- 	MsgAdapter.WriteInt(self.param2)	
-- end

-- -- 好友邀请通知
-- SCFriendinviteNotice = SCFriendinviteNotice or BaseClass(BaseProtocolStruct)
-- function SCFriendinviteNotice:__init()
-- 	self.msg_type = 16131 
-- end

-- function SCFriendinviteNotice:Decode()
-- 	self.notice_type = MsgAdapter.ReadInt()
-- 	self.operate_role_id = MsgAdapter.ReadInt()
-- 	self.operate_role_name = MsgAdapter.ReadStrN(32)
-- end

-- 好友邀请全部信息
SyncFriendInviteInfo = SyncFriendInviteInfo or BaseClass(BaseProtocolStruct)
function SyncFriendInviteInfo:__init()
	self.msg_type = 16132 
	self.invite_list = {}
end

function SyncFriendInviteInfo:Decode()
	self.active_flag = MsgAdapter.ReadInt()
	self.fetch_flag	 = MsgAdapter.ReadInt()
	
	local invite_list_len = MsgAdapter.ReadInt()
	self.invite_list = {}
	for i = 1, invite_list_len do
		local vo = {}
		vo.role_id = MsgAdapter.ReadInt()
		vo.role_name = MsgAdapter.ReadStrN(32)
		vo.level = MsgAdapter.ReadInt()
		self.invite_list[i] = vo
	end	
end

-----领域直购-----
SCOAFootLightInfo = SCOAFootLightInfo or BaseClass(BaseProtocolStruct)
function SCOAFootLightInfo:__init()
    self.msg_type = 16134
end

function SCOAFootLightInfo:Decode()
	self.buy_tmb_count = MsgAdapter.ReadInt()
    self.is_buy_free = MsgAdapter.ReadShort()
    self.reserve = MsgAdapter.ReadShort()
end

------------------------------------------------------

-- -- 特殊表情操作
-- CSDabiaoqingTwoOperate = CSDabiaoqingTwoOperate or BaseClass(BaseProtocolStruct)
-- function CSDabiaoqingTwoOperate:__init()
-- 	self.msg_type = 16140
-- end

-- function CSDabiaoqingTwoOperate:Encode()
-- 	MsgAdapter.WriteBegin(self.msg_type)
-- 	MsgAdapter.WriteInt(self.operate)
-- 	MsgAdapter.WriteInt(self.param1)
-- end

-- -- 特殊表情信息
-- SCDabiaoqingTwoInfo = SCDabiaoqingTwoInfo or BaseClass(BaseProtocolStruct)
-- function SCDabiaoqingTwoInfo:__init()
-- 	self.msg_type = 16141
-- end

-- function SCDabiaoqingTwoInfo:Decode()
-- 	self.active_flag = MsgAdapter.ReadInt()
-- 	self.bless_list = {}
-- 	for i = 1, GameEnum.MAX_DABIAOQING2_MAX_NUM do
-- 		self.bless_list[i] = MsgAdapter.ReadUShort()
-- 	end
-- end

--沧溟直购
SCOAWaistLightInfo =  SCOAWaistLightInfo or BaseClass(BaseProtocolStruct)
function  SCOAWaistLightInfo:__init()
    self.msg_type = 16145
end

function  SCOAWaistLightInfo:Decode()
    self.is_buy_rmb = MsgAdapter.ReadShort()  
    self.is_buy_free = MsgAdapter.ReadShort() 
end

-----------------------------------------------------

-- 背景操作
CSBackOperate = CSBackOperate or BaseClass(BaseProtocolStruct)
function CSBackOperate:__init()
	self.msg_type = 16120
end

function CSBackOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.oper_type)
	MsgAdapter.WriteInt(self.back_id)
end

--背景信息
SCBackInfo = SCBackInfo or BaseClass(BaseProtocolStruct)
function SCBackInfo:__init()
	self.msg_type = 16121
	self.use_back_id = 0
	self.all_back_count = 0
	self.back_item_list = {}
end

function SCBackInfo:Decode()
	self.use_back_id = MsgAdapter.ReadInt()
	self.all_back_count = MsgAdapter.ReadInt()
	for i = 1, self.all_back_count do
		self.back_item_list[i] = ProtocolStruct:ReadBackInfo()
	end
end

--同步背景的当前使用id
SCBackUse = SCBackUse or BaseClass(BaseProtocolStruct)
function SCBackUse:__init()
	self.msg_type = 16123
	self.use_back_id = 0
end

function SCBackUse:Decode()
	self.use_back_id = MsgAdapter.ReadInt()
end

--激活新背景
SCActiveNewBackInfo = SCActiveNewBackInfo or BaseClass(BaseProtocolStruct)
function SCActiveNewBackInfo:__init()
	self.msg_type = 16122
	self.active_new_back_id = 0
	self.notice_type = 0
end

function SCActiveNewBackInfo:Decode()
	self.active_new_back_id = MsgAdapter.ReadInt()
	self.notice_type = MsgAdapter.ReadInt()
end

-----------------------------------------------------

CSTianShenRuMoOpera = CSTianShenRuMoOpera or BaseClass(BaseProtocolStruct)
function CSTianShenRuMoOpera:__init()
    self.msg_type = 16136
    self.request = 0
    self.param_1 = 0
    self.param_2 = 0
    self.param_3 = 0
end

function CSTianShenRuMoOpera:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.request)
    MsgAdapter.WriteInt(self.param_1)
    MsgAdapter.WriteInt(self.param_2)
    MsgAdapter.WriteInt(self.param_3)
end

CSTianShenRuMoSkillOpera = CSTianShenRuMoSkillOpera or BaseClass(BaseProtocolStruct)
function CSTianShenRuMoSkillOpera:__init()
    self.msg_type = 16137
    self.request = 0
    self.tianshen_id = 0
	self.demon_level = 0
    self.skills = {0,0,0,0,0}
end

function CSTianShenRuMoSkillOpera:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.request)
    MsgAdapter.WriteInt(self.tianshen_id)
	MsgAdapter.WriteInt(self.demon_level)
    for i = 1, 5 do
        MsgAdapter.WriteInt(self.skills[i]) 
    end
end

--仙灵入魔信息--全部或个别
SCTianShenRuMoInfo = SCTianShenRuMoInfo or BaseClass(BaseProtocolStruct)
function SCTianShenRuMoInfo:__init()
    self.msg_type = 16138
end

function SCTianShenRuMoInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.tianshen_data = {}
    for i = 1, self.count do
        self.tianshen_data[i] = {}
        self.tianshen_data[i].tianshen_id = MsgAdapter.ReadUShort()
        self.tianshen_data[i].demon_level = MsgAdapter.ReadUShort()
        self.tianshen_data[i].huanhua_id = MsgAdapter.ReadUInt()

		local skill_data = {}
		for lv = 1, 3 do	---最大类型固定了3（一切解释权给服务器）
			skill_data[lv] = {}
			for count = 1, 5 do	---技能个数固定了5
				skill_data[lv][count] = {}
				skill_data[lv][count].skill_id = MsgAdapter.ReadUShort()
				skill_data[lv][count].skill_lv = MsgAdapter.ReadUShort()
			end
		end

        self.tianshen_data[i].skills = skill_data
		self.tianshen_data[i].star_data = {}
		for j = 1, 3 do
			self.tianshen_data[i].star_data[j] = MsgAdapter.ReadUShort()
		end

		MsgAdapter.ReadUShort()
	end
end

-----------------五行沧溟-----------------
CSWaistLightOperate = CSWaistLightOperate or BaseClass(BaseProtocolStruct)
function CSWaistLightOperate:__init()
	self.msg_type = 16139
end

function CSWaistLightOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCWaistLightInfo = SCWaistLightInfo or BaseClass(BaseProtocolStruct)
function SCWaistLightInfo:__init()
	self.msg_type = 16140
end

function SCWaistLightInfo:Decode()
	self.use_waist_id = MsgAdapter.ReadShort()
	self.skill_id = MsgAdapter.ReadShort()
	self.all_waist_lights_list = {}
	self.all_daohun_list = {}
	for i = 1, WAIST_LIGHT_NUMTYPE.MAX_WAIST_LIGHT do
		self.all_waist_lights_list[i] = {}
		self.all_waist_lights_list[i].is_use = self.use_waist_id == i
		self.all_waist_lights_list[i].slots = {}
		for j = 1, WAIST_LIGHT_NUMTYPE.COUNT_WAIST_LIGHT_STONE do
			local vo = {}
			vo.itemId = MsgAdapter.ReadUShort()
			vo.level = MsgAdapter.ReadShort()
			self.all_waist_lights_list[i].slots[j] = vo
		end

		self.all_daohun_list[i] = {}
		self.all_daohun_list[i].is_active = MsgAdapter.ReadInt() == 1
		self.all_daohun_list[i].daohun_level = MsgAdapter.ReadInt()
	end
end

SCWaistLightItemInfo = SCWaistLightItemInfo or BaseClass(BaseProtocolStruct)
function SCWaistLightItemInfo:__init()
	self.msg_type = 16141 
end

function SCWaistLightItemInfo:Decode()
	self.waist_id = MsgAdapter.ReadInt()
	self.waist_light = {}
	for i = 1, WAIST_LIGHT_NUMTYPE.COUNT_WAIST_LIGHT_STONE do
		local vo = {}
		vo.itemId = MsgAdapter.ReadUShort()
		vo.level = MsgAdapter.ReadShort()
		self.waist_light[i] = vo
	end
end

SCWaistLightActive = SCWaistLightActive or BaseClass(BaseProtocolStruct)
function SCWaistLightActive:__init()
	self.msg_type = 16142 
end

function SCWaistLightActive:Decode()
	self.waist_id = MsgAdapter.ReadInt()
end

SCWaistLightCur = SCWaistLightCur or BaseClass(BaseProtocolStruct)
function SCWaistLightCur:__init()
	self.msg_type = 16143 
end

function SCWaistLightCur:Decode()
	self.use_waist_id = MsgAdapter.ReadInt()
end

SCWaistLightSkill = SCWaistLightSkill or BaseClass(BaseProtocolStruct)
function SCWaistLightSkill:__init()
	self.msg_type = 16135 
end

function SCWaistLightSkill:Decode()
	self.skill_id = MsgAdapter.ReadInt()
end

SCWaistLightSKillState = SCWaistLightSKillState or BaseClass(BaseProtocolStruct)
function SCWaistLightSKillState:__init()
    self.msg_type = 16144
end

function SCWaistLightSKillState:Decode()
	self.num = MsgAdapter.ReadInt()
end

-----------------五行沧溟End-----------------

------------------------------------超值赠礼---------------------------------
SCOAOverflowGiftInfo = SCOAOverflowGiftInfo or BaseClass(BaseProtocolStruct)

function SCOAOverflowGiftInfo:__init()
    self.msg_type = 16146
end

function SCOAOverflowGiftInfo:Decode()
    self.grade = MsgAdapter.ReadInt()
    self.main_long_get = MsgAdapter.ReadChar()
    self.resvrvech = MsgAdapter.ReadChar()
    self.resvrvesh= MsgAdapter.ReadShort()
    local sub_reward_flag = MsgAdapter.ReadLL()
    self.sub_reward_flag = bit:d2b_l2h(sub_reward_flag, nil, true)
    local main_status_flag = MsgAdapter.ReadLL()
    self.main_status_flag = bit:d2b_l2h(main_status_flag, nil, true)

    self.sub_all_flag_list = {}
	local flag = 0
	for i = 0, 9 do
		flag = MsgAdapter.ReadLL()
		self.sub_all_flag_list[i] = bit:d2b_l2h(flag, nil, true)
	end
end

--特惠卖场
SCOALimitRmbShopInfo = SCOALimitRmbShopInfo or BaseClass(BaseProtocolStruct)
function SCOALimitRmbShopInfo:__init()
    self.msg_type = 16147
end

function SCOALimitRmbShopInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.rmb_buy_times_list = {}
    for i = 0, 9 do
        self.rmb_buy_times_list[i] = MsgAdapter.ReadInt()
    end
end

----------唯我独尊
SCRAWeiWoDuZunInfo = SCRAWeiWoDuZunInfo or BaseClass(BaseProtocolStruct)
function SCRAWeiWoDuZunInfo:__init()
    self.msg_type = 16148
end

function SCRAWeiWoDuZunInfo:Decode()
	self.task_list = {}
    for i = 0, 19 do
		self.task_list[i] = {}
        self.task_list[i].task_id = i
		self.task_list[i].is_fecth_role_id = MsgAdapter.ReadInt()
		self.task_list[i].fecth_role_name = MsgAdapter.ReadName()
		self.task_list[i].reward_flag = MsgAdapter.ReadInt()
    end
end

SCRAWeiWoDuZunUpdate = SCRAWeiWoDuZunUpdate or BaseClass(BaseProtocolStruct)
function SCRAWeiWoDuZunUpdate:__init()
    self.msg_type = 16149
end

function SCRAWeiWoDuZunUpdate:Decode()
	local data = {}
	data.task_id = MsgAdapter.ReadInt()
	data.is_fecth_role_id = MsgAdapter.ReadInt()
	data.fecth_role_name = MsgAdapter.ReadName()
	data.reward_flag = MsgAdapter.ReadInt()
	self.change_data = data
end

-------------------------经脉---------------------------------
CSRoleJingMaiReq = CSRoleJingMaiReq or BaseClass(BaseProtocolStruct)
function CSRoleJingMaiReq:__init()
	self.msg_type = 16150
end

function CSRoleJingMaiReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.opera_type)
	MsgAdapter.WriteShort(self.param1)
end

--返回
SCRoleJingMai = SCRoleJingMai or BaseClass(BaseProtocolStruct)
function SCRoleJingMai:__init()
    self.msg_type = 16151
end

function SCRoleJingMai:Decode()
    self.whole_body_id = MsgAdapter.ReadInt()		--通体ID.
	self.meridians_id = MsgAdapter.ReadInt()		--经脉ID.
	self.acupoint_id = MsgAdapter.ReadInt()			--穴位ID.
	self.longhun_exp = MsgAdapter.ReadInt()			--龙魂经验值.
	self.break_state = MsgAdapter.ReadChar()		--突破状态.
	self.re_ch = MsgAdapter.ReadChar()
	self.reservel = MsgAdapter.ReadShort()
	-- self.condensate_per = MsgAdapter.ReadInt()
	-- self.skill_state = MsgAdapter.ReadShort()
	self.is_success = MsgAdapter.ReadShort()
	self.opera_type = MsgAdapter.ReadShort()
end
-----------------经脉End-----------------

---------------------------洛神赋start--------------------------
CSDrawingsComposeOperate = CSDrawingsComposeOperate or BaseClass(BaseProtocolStruct)

function CSDrawingsComposeOperate:__init()
	self.msg_type = 16152
end

function CSDrawingsComposeOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

local function GetMsgDrawingsItem()
	local chips_list = {}
	for i = 0, 7 do
		chips_list[i] = MsgAdapter.ReadInt()
	end

	return chips_list
end

SCDrawingsComposeInfo = SCDrawingsComposeInfo or BaseClass(BaseProtocolStruct)

function SCDrawingsComposeInfo:__init()
    self.msg_type = 16153
end

function SCDrawingsComposeInfo:Decode()
	local drawings_list = {}
	local i = 0
	for j = 0, 63 do
		drawings_list[i] = drawings_list[i] or {}
		drawings_list[i][j] = GetMsgDrawingsItem()
	end

	self.drawings_list = drawings_list
end

SCDrawingsComposeUpdate = SCDrawingsComposeUpdate or BaseClass(BaseProtocolStruct)

function SCDrawingsComposeUpdate:__init()
    self.msg_type = 16154
end

function SCDrawingsComposeUpdate:Decode()
	self.type = MsgAdapter.ReadInt()
	self.rawings_id = MsgAdapter.ReadInt()
	self.slot_idx = MsgAdapter.ReadInt()
	self.level = MsgAdapter.ReadInt()
end

-----------------------------洛神赋end---------------------------

---------------------------圣兽召唤start----------------------------
SCOAShengShouDrawInfo = SCOAShengShouDrawInfo or BaseClass(BaseProtocolStruct)

function SCOAShengShouDrawInfo:__init()
    self.msg_type = 16167
end

function SCOAShengShouDrawInfo:Decode()
	self.rmb_num = MsgAdapter.ReadLL()
	self.draw_times = MsgAdapter.ReadInt()
	self.draw_limit = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadShort()
	self.is_acquire = MsgAdapter.ReadChar()
end

local function GetMsgResultItem()
	local data = {}
	data.num = MsgAdapter.ReadInt()
	data.item_id = MsgAdapter.ReadUShort()
	data.is_bind = MsgAdapter.ReadChar()
	data.reserve = MsgAdapter.ReadChar()
	return data
end

SCOAShengShouDrawResult = SCOAShengShouDrawResult or BaseClass(BaseProtocolStruct)

function SCOAShengShouDrawResult:__init()
    self.msg_type = 16168
end

function SCOAShengShouDrawResult:Decode()
	self.count = MsgAdapter.ReadInt()

	local result_item_list = {}
	for i = 1, self.count do
		result_item_list[i] = GetMsgResultItem()
	end

	self.result_item_list = result_item_list
end
---------------------------圣兽召唤end----------------------------end


---------------------------棋盘寻宝start--------------------------
SCOARechargeScoreOperate = SCOARechargeScoreOperate or BaseClass(BaseProtocolStruct)

function SCOARechargeScoreOperate:__init()
	self.msg_type = 16171
end

function SCOARechargeScoreOperate:Decode()
    self.score = MsgAdapter.ReadLL()				-- 积分
	self.grade = MsgAdapter.ReadUInt()				-- 档次
	self.round_num = MsgAdapter.ReadUInt()			-- 轮数
	self.reward_flag = {}							-- 最大轮数
	for i = 0, 4 do
		local flag = MsgAdapter.ReadUInt()
		self.reward_flag[i] = bit:d2b_l2h(flag, nil, true)
	end
	-- print_error("积分=",self.score, "档次=",self.grade, "轮数=",self.round_num)
end

---------------------------棋盘寻宝end--------------------------


-----------------------------福星高照start-------------------------
CSRoleFuXingGaoZhaoReq = CSRoleFuXingGaoZhaoReq or BaseClass(BaseProtocolStruct)

function CSRoleFuXingGaoZhaoReq:__init()
	self.msg_type = 16155
end

function CSRoleFuXingGaoZhaoReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
end

local function GetMsgResultItem()
	local data = {}
	data.item_id = MsgAdapter.ReadUShort()
	data.reserve = MsgAdapter.ReadChar()
	data.is_bind = MsgAdapter.ReadChar()
	data.num = MsgAdapter.ReadInt()

	return data
end

SCRoleFuXingGaoZhaoDrawResult = SCRoleFuXingGaoZhaoDrawResult or BaseClass(BaseProtocolStruct)

function SCRoleFuXingGaoZhaoDrawResult:__init()
    self.msg_type = 16156
end

function SCRoleFuXingGaoZhaoDrawResult:Decode()
	self.reward_size = MsgAdapter.ReadInt()
	local result_item_list = {}

	for i = 1, self.reward_size do
		result_item_list[i] = GetMsgResultItem()
	end

	self.result_item_list = result_item_list
end

SCRoleFuXingGaoZhaoInfo = SCRoleFuXingGaoZhaoInfo or BaseClass(BaseProtocolStruct)

function SCRoleFuXingGaoZhaoInfo:__init()
    self.msg_type = 16157
end

function SCRoleFuXingGaoZhaoInfo:Decode()
	self.has_draw_times = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt()
	self.baodi_flag = MsgAdapter.ReadLL()
end

local function GetFuXingGaoZhaoWorldRecord()
	local data = {}
	data.role_name =  MsgAdapter.ReadStrN(32)
	data.item_id = MsgAdapter.ReadUShort()
	data.reserve = MsgAdapter.ReadShort()
	data.num = MsgAdapter.ReadInt()
	return data
end

SCRoleFuXingGaoZhaoWorldRecord = SCRoleFuXingGaoZhaoWorldRecord or BaseClass(BaseProtocolStruct)

function SCRoleFuXingGaoZhaoWorldRecord:__init()
    self.msg_type = 16158
end

function SCRoleFuXingGaoZhaoWorldRecord:Decode()
	self.count = MsgAdapter.ReadInt()

	local record_list = {}
	for i = 1, self.count do
		record_list[i] = GetFuXingGaoZhaoWorldRecord()
	end
	self.record_list = record_list
end

-----------------------------福星高照end-------------------------

-----------------------------仙灵武魂----------------------------
CSRoleWuHunReq = CSRoleWuHunReq or BaseClass(BaseProtocolStruct)
function CSRoleWuHunReq:__init()
    self.msg_type = 16159
end

function CSRoleWuHunReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.reserve1)
end

SCRoleSingleWuHunInfo = SCRoleSingleWuHunInfo or BaseClass(BaseProtocolStruct)
function SCRoleSingleWuHunInfo:__init()
    self.msg_type = 16160
end

function SCRoleSingleWuHunInfo:Decode()
	self.wuhun = ProtocolStruct:ReadWuHunObjInfo()
	self.opera_type = MsgAdapter.ReadUShort()
	MsgAdapter.ReadUShort()
end

SCRoleWuHunInfoResp = SCRoleWuHunInfoResp or BaseClass(BaseProtocolStruct)
function SCRoleWuHunInfoResp:__init()
    self.msg_type = 16161
end

function SCRoleWuHunInfoResp:Decode()
	self.wuhun_list_size = MsgAdapter.ReadInt()
	self.wuhun_prerogative_level = MsgAdapter.ReadInt()		--武魂特权等级.
	self.wuhun_prerogative_flag = MsgAdapter.ReadInt()		--武魂特权领取标识,0:未领取,1:已领取.
	self.wuhun_list = {}
	for i = 1, self.wuhun_list_size do
		self.wuhun_list[i] = ProtocolStruct:ReadWuHunObjInfo()
	end
end

SCRoleWuHunUseSkillResp = SCRoleWuHunUseSkillResp or BaseClass(BaseProtocolStruct)
function SCRoleWuHunUseSkillResp:__init()
    self.msg_type = 16162
end

function SCRoleWuHunUseSkillResp:Decode()
	self.wuhun_skill_index = MsgAdapter.ReadInt()
	self.wuhun_target_id = MsgAdapter.ReadUShort()
	MsgAdapter.ReadShort()		--偏移两字节
end

SCRoleWuHunAuraId = SCRoleWuHunAuraId or BaseClass(BaseProtocolStruct)
function SCRoleWuHunAuraId:__init()
    self.msg_type = 16163
end

function SCRoleWuHunAuraId:Decode()
	self.wuhun_id = MsgAdapter.ReadInt()
end

---------------------------仙灵武魂end----------------------------

-----------------------------修为START----------------------------
CSRoleXiuWeiOperate = CSRoleXiuWeiOperate or BaseClass(BaseProtocolStruct)
function CSRoleXiuWeiOperate:__init()
    self.msg_type = 16169
end

function CSRoleXiuWeiOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.opera_type)
	MsgAdapter.WriteShort(self.param1)
	MsgAdapter.WriteInt(self.param2)
end

local function GetMsgXiuWeiNuQiLevel()
	local data = {}
	data.nuqi_upgrade_level = MsgAdapter.ReadInt()
	data.nuqi_buff_level = MsgAdapter.ReadInt()
	data.nuqi_buff_exp = MsgAdapter.ReadInt()
	data.nuqi_skill_level = {}
	for i=0,3 do
		data.nuqi_skill_level[i] = MsgAdapter.ReadInt()
	end
	return data
end

SCRoleXiuWeiInfo = SCRoleXiuWeiInfo or BaseClass(BaseProtocolStruct)
function SCRoleXiuWeiInfo:__init()
    self.msg_type = 16170
end

function SCRoleXiuWeiInfo:Decode()
	self.stage = MsgAdapter.ReadUShort()
	self.level = MsgAdapter.ReadUShort()
	self.lingli_exp = MsgAdapter.ReadLL()		    	--	灵力经验
	-- 协议定义的16个
	local lingli_exp_add = {}
	for i=0,15 do
		local temp = MsgAdapter.ReadUInt()
		lingli_exp_add[i] = bit:d2b_l2h(temp,nil,true)
	end
	self.lingli_exp_add = lingli_exp_add				--	灵力经验加成
	local use_dan_item_num = {}
	for i=1,3 do
		use_dan_item_num[i] = MsgAdapter.ReadUInt()
	end
	self.use_dan_item_num = use_dan_item_num		--	增加灵力经验丹药道具每日使用数量
	self.next_tequan_end_time = MsgAdapter.ReadUInt()	--	特权结束时间
	local stage_reward_flag = MsgAdapter.ReadUInt()		
	self.stage_reward_flag = bit:d2b_l2h(stage_reward_flag,nil,true)		--	境界奖励标记
	self.every_day_flag = MsgAdapter.ReadUInt()			--	特权每日奖励标记
	self.current_nuqi = MsgAdapter.ReadUShort()			--	当前怒气值
	self.nuqi_bianshen_flag = MsgAdapter.ReadUShort()	--  当前怒气变身状态
	-- self.active_nuqi_flag = MsgAdapter.ReadInt()		--  当前激活境界怒气类型 0人,1仙,2魔
	self.nuqi_type = MsgAdapter.ReadInt()				--  当前选择境界怒气类型 0人,1仙,2魔 -1没有激活
	local capability = {}
	for i=0,13 do
		capability[i] = MsgAdapter.ReadLL()
	end
	self.capability = capability		--	各系统总战力
	
	local nuqi_level = {}
	for i=0,2 do
		nuqi_level[i] = GetMsgXiuWeiNuQiLevel()
	end
	self.nuqi_level = nuqi_level       -- 怒气等级
	self.nuqi_open_flag = MsgAdapter.ReadInt() -- 怒气变身开启标志
end

SCRoleXiuWeiBaseInfo = SCRoleXiuWeiBaseInfo or BaseClass(BaseProtocolStruct)
function SCRoleXiuWeiBaseInfo:__init()
    self.msg_type = 16186
end

function SCRoleXiuWeiBaseInfo:Decode()
	self.stage = MsgAdapter.ReadUShort()				--	境界
	self.level = MsgAdapter.ReadUShort()				--	等级
	self.lingli_exp = MsgAdapter.ReadLL()				--	灵力经验
	-- 协议定义的16个
	local lingli_exp_add = {}
	for i=0,15 do
		local temp = MsgAdapter.ReadUInt()
		lingli_exp_add[i] = bit:d2b_l2h(temp,nil,true)
	end
	self.lingli_exp_add = lingli_exp_add				--	灵力经验加成
	local use_dan_item_num = {}
	for i=1,3 do
		use_dan_item_num[i] = MsgAdapter.ReadUInt()
	end
	self.use_dan_item_num = use_dan_item_num		--	增加灵力经验丹药道具每日使用数量
	self.next_tequan_end_time = MsgAdapter.ReadUInt()	--	特权结束时间
	self.every_day_flag = MsgAdapter.ReadUInt()			--	特权每日奖励标记
	local stage_reward_flag = MsgAdapter.ReadUInt()		
	self.stage_reward_flag = bit:d2b_l2h(stage_reward_flag,nil,true)		--	境界奖励标记
	local capability = {}
	for i=0,13 do
		capability[i] = MsgAdapter.ReadLL()
	end
	self.capability = capability		--	各系统总战力
end

SCRoleXiuWeiNuqiInfo = SCRoleXiuWeiNuqiInfo or BaseClass(BaseProtocolStruct)
function SCRoleXiuWeiNuqiInfo:__init()
    self.msg_type = 16187
end

function SCRoleXiuWeiNuqiInfo:Decode()
	self.current_nuqi = MsgAdapter.ReadUShort()			--	当前怒气值
	self.nuqi_bianshen_flag = MsgAdapter.ReadUShort()	--  当前怒气变身状态
	-- self.active_nuqi_flag = MsgAdapter.ReadInt()		--  当前激活境界怒气类型 0人,1仙,2魔 -1没有激活
	self.nuqi_type = MsgAdapter.ReadInt()				--  当前选择境界怒气类型 0人,1仙,2魔 
end

-------------------------------修为END----------------------------

------------------------通知服务器完成当前副本----------------------
CSClientPassSpeciakInfo = CSClientPassSpeciakInfo or BaseClass(BaseProtocolStruct)
function CSClientPassSpeciakInfo:__init()
    self.msg_type = 16172
end

function CSClientPassSpeciakInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.scene_id)
	MsgAdapter.WriteInt(self.scene_type)
end

----------------------客户端请求领取直购全部买完后的每日额外奖励----------------------
CSMtWealthGodExtendReward = CSMtWealthGodExtendReward or BaseClass(BaseProtocolStruct)
function CSMtWealthGodExtendReward:__init()
    self.msg_type = 16173
end

function CSMtWealthGodExtendReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

----------------------------新战斗坐骑---------------------
CSFightMount2Operate = CSFightMount2Operate or BaseClass(BaseProtocolStruct)
function CSFightMount2Operate:__init()
    self.msg_type = 16174
end

function CSFightMount2Operate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.opera_type)
    MsgAdapter.WriteInt(self.param_1)
    MsgAdapter.WriteInt(self.param_2)
    MsgAdapter.WriteInt(self.param_3)
end

--战斗坐骑 - 返回全部数据
SCFightMount2Info = SCFightMount2Info or BaseClass(BaseProtocolStruct)
function SCFightMount2Info:__init()
    self.msg_type = 16175
end

function SCFightMount2Info:Decode()
	self.skill_level_list = {}
	self.appearance_level_list = {}
	for i = 0, 19 do
		self.skill_level_list[i] = MsgAdapter.ReadInt()
	end

	for i = 0, 19 do
		self.appearance_level_list[i] = MsgAdapter.ReadInt()
	end

	self.use_mount_id = MsgAdapter.ReadInt()
	self.use_dragon_id = MsgAdapter.ReadInt()
	self.use_skill_id = MsgAdapter.ReadInt()
end


SCFightMount2GoonMount = SCFightMount2GoonMount or BaseClass(BaseProtocolStruct)
function SCFightMount2GoonMount:__init()
    self.msg_type = 16176
end

function SCFightMount2GoonMount:Decode()
	self.use_mount_id = MsgAdapter.ReadInt()
	self.use_dragon_id = MsgAdapter.ReadInt()
	self.use_skill_id = MsgAdapter.ReadInt()
end

SCFightMount2Time = SCFightMount2Time or BaseClass(BaseProtocolStruct)
function SCFightMount2Time:__init()
    self.msg_type = 16177
end

function SCFightMount2Time:Decode()
	self.use_skill_end_time = MsgAdapter.ReadUInt()
	self.next_use_skill_time = MsgAdapter.ReadUInt()
	self.use_skill_start_time = MsgAdapter.ReadUInt()
end

SCFightMount2OneInfo = SCFightMount2OneInfo or BaseClass(BaseProtocolStruct)
function SCFightMount2OneInfo:__init()
    self.msg_type = 16178
end

function SCFightMount2OneInfo:Decode()
	local data = {}
	data.mount_id = MsgAdapter.ReadInt()
	data.skill_level = MsgAdapter.ReadInt()
	data.appearance_level = MsgAdapter.ReadInt()
	self.change_data = data
end
-----

SCGoldTalkInfo = SCGoldTalkInfo or BaseClass(BaseProtocolStruct)
function SCGoldTalkInfo:__init()
    self.msg_type = 16179
end

function SCGoldTalkInfo:Decode()
	self.is_buy_rmb = MsgAdapter.ReadShort()
	self.is_buy_free = MsgAdapter.ReadShort()
end

CSGoldTalkOperate = CSGoldTalkOperate or BaseClass(BaseProtocolStruct)
function CSGoldTalkOperate:__init()
    self.msg_type = 16180
end

function CSGoldTalkOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
end


--------------------------须弥神域定制技能------------------
--客户端请求激活套装选择技能
CSWardrobeSkillClientOperate = CSWardrobeSkillClientOperate	or BaseClass(BaseProtocolStruct)
function CSWardrobeSkillClientOperate:__init()
    self.msg_type = 16181
end

function CSWardrobeSkillClientOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.type)
	MsgAdapter.WriteShort(self.param1)
	MsgAdapter.WriteShort(self.param2)
	MsgAdapter.WriteShort(self.param3)

	for _, attr_id in ipairs(self.attr1_id) do
		MsgAdapter.WriteShort(attr_id)
	end

	MsgAdapter.WriteShort(self.attr2_id)
end

---全部套装定制信息
SCWardrobeSkillAllInfo = SCWardrobeSkillAllInfo or BaseClass(BaseProtocolStruct)
function SCWardrobeSkillAllInfo:__init()
    self.msg_type = 16182
end

function SCWardrobeSkillAllInfo:Decode()
	self.wardrobe_list = {}
	for i = 1, 10 do
		local wardrobe_info = ProtocolStruct:ReadWardrobeSkillInfo()
		self.wardrobe_list[i] = wardrobe_info
	end
end

---单个套装定制信息
SCWardrobeSkillOneInfo = SCWardrobeSkillOneInfo or BaseClass(BaseProtocolStruct)
function SCWardrobeSkillOneInfo:__init()
    self.msg_type = 16183
end

function SCWardrobeSkillOneInfo:Decode()
	self.wardrobe_info = ProtocolStruct:ReadWardrobeSkillInfo()
end

-------------------------------------------------------------------------------------
-- 养龙寺开始刷新怪物时间
SCYangLongStartRefreshBossTime = SCYangLongStartRefreshBossTime or BaseClass(BaseProtocolStruct)
function SCYangLongStartRefreshBossTime:__init()
    self.msg_type = 16188
end

function SCYangLongStartRefreshBossTime:Decode()
end

-------------------------------------------------------------------------------------
-- 神怒天诛信息
SCOAShenNuTianZhuInfo = SCOAShenNuTianZhuInfo or BaseClass(BaseProtocolStruct)
function SCOAShenNuTianZhuInfo:__init()
    self.msg_type = 16189
end

function SCOAShenNuTianZhuInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	local reward_flag = MsgAdapter.ReadInt()
	self.reward_flag = bit:d2b_l2h(reward_flag, nil, true)
end


-- 长乐未央信息
SCHappyForeverInfo = SCHappyForeverInfo or BaseClass(BaseProtocolStruct)
function SCHappyForeverInfo:__init()
    self.msg_type = 16190
end

function SCHappyForeverInfo:Decode()
	local MAX_POOL_COUNT = 10
	self.is_acquipre_reward = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())

	self.choose_pool_seq_list = {}
	for i = 0, MAX_POOL_COUNT - 1 do
		local choose_pool_seq_list_flag = MsgAdapter.ReadUShort()
		self.choose_pool_seq_list[i] = bit:d2b_l2h(choose_pool_seq_list_flag, nil, false)
	end

	self.buy_times = {}
	for i = 0, MAX_POOL_COUNT - 1 do
		self.buy_times[i] = MsgAdapter.ReadShort()
	end

	self.is_acquipre_free_reward = MsgAdapter.ReadChar()
	self.is_all_buy = MsgAdapter.ReadChar()
end


SCHappyForeverResult = SCHappyForeverResult or BaseClass(BaseProtocolStruct)
function SCHappyForeverResult:__init()
    self.msg_type = 16191
end

function SCHappyForeverResult:Decode()
    self.count = MsgAdapter.ReadInt()
    local item_list = {}
    for i = 1, self.count do
        local item = {
            item_id = MsgAdapter.ReadUShort(),
            is_bind = MsgAdapter.ReadUShort(),
            num  = MsgAdapter.ReadInt(),
        }

        item_list[i] = item
    end

    self.result_reward_list = item_list
end

--长乐未央请求
CSHappyForeverOperate = CSHappyForeverOperate or BaseClass(BaseProtocolStruct)
function CSHappyForeverOperate:__init()
	self.msg_type = 16192
	self.operate = 0
	self.param1 = 0
	self.param2 = 0
end

function CSHappyForeverOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
end

--------------------------驭兽天下协议开始------------------
-- 驭兽操作
CSRoleBeastOperate = CSRoleBeastOperate or BaseClass(BaseProtocolStruct)
function CSRoleBeastOperate:__init()
	self.msg_type = 16193
	self.opera_type = 0
	self.param1 = 0
	self.param2 = 0
end

function CSRoleBeastOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
end

-- 驭兽合成
CSRoleBeastCompose = CSRoleBeastCompose or BaseClass(BaseProtocolStruct)
function CSRoleBeastCompose:__init()
	self.msg_type = 16194
	self.compose_data = nil
end

function CSRoleBeastCompose:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	local data = self.compose_data
	MsgAdapter.WriteShort(data.aim_beast.bag_type)
	MsgAdapter.WriteShort(data.aim_beast.bag_index)
	MsgAdapter.WriteInt(-1)

	MsgAdapter.WriteInt(data.spend_count)
	for j = 1, data.spend_count do	--最多10个材料
		if data.stuff_list[j] then
			local stuff_data = data.stuff_list[j]
			MsgAdapter.WriteShort(stuff_data.bag_type)
			MsgAdapter.WriteShort(stuff_data.bag_index)
			MsgAdapter.WriteInt(stuff_data.item_num or -1)
		else
			MsgAdapter.WriteShort(-1)
			MsgAdapter.WriteShort(-1)
			MsgAdapter.WriteInt(-1)
		end
	end
end

-- 未孵化背包信息
SCAllBeastEggBagInfo = SCAllBeastEggBagInfo or BaseClass(BaseProtocolStruct)
function SCAllBeastEggBagInfo:__init()
    self.msg_type = 16195
end

function SCAllBeastEggBagInfo:Decode()
	self.egg_bag = {}
	for i = 1, BEAST_DEFINE.BEAST_PACK_COUNT_MAX do
		self.egg_bag[i] = ProtocolStruct:ReadBeastEggBagInfo()
	end
end

-- 批量合成
CSBeastBatchCompose = CSBeastBatchCompose or BaseClass(BaseProtocolStruct)
function CSBeastBatchCompose:__init()
    self.msg_type = 16196
	self.count = 0
	self.compose_batch_list = {}
end

function CSBeastBatchCompose:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.count)
	for i = 1, 20 do	--最大20个批次
		if self.compose_batch_list[i] then
			local data = self.compose_batch_list[i]
			MsgAdapter.WriteShort(data.aim_beast.bag_type)
			MsgAdapter.WriteShort(data.aim_beast.bag_index)
			MsgAdapter.WriteInt(-1)
			MsgAdapter.WriteInt(data.spend_count)
			for j = 1, 10 do
				if data.stuff_list[j] then
					local stuff_data = data.stuff_list[j]
					MsgAdapter.WriteShort(stuff_data.bag_type)
					MsgAdapter.WriteShort(stuff_data.bag_index)
					MsgAdapter.WriteInt(stuff_data.item_num or -1)
				else
					MsgAdapter.WriteShort(-1)
					MsgAdapter.WriteShort(-1)
					MsgAdapter.WriteInt(-1)
				end
			end
		end
	end
end

-- 全部孵化槽信息
SCAllBreedingInfo = SCAllBreedingInfo or BaseClass(BaseProtocolStruct)
function SCAllBreedingInfo:__init()
    self.msg_type = 16197
end

function SCAllBreedingInfo:Decode()
	self.breeding_slot_list = {}
	for i = 1, BEAST_DEFINE.BEAST_BREEDING_COUNT_MAX do
		self.breeding_slot_list[i] = ProtocolStruct:ReadBeastBreedingSlot()
	end
end

-- 单个孵化槽信息
SCSingleBreedingInfo = SCSingleBreedingInfo or BaseClass(BaseProtocolStruct)
function SCSingleBreedingInfo:__init()
    self.msg_type = 16198
end

function SCSingleBreedingInfo:Decode()
	self.breeding_index = MsgAdapter.ReadInt()
	self.breeding_slot = ProtocolStruct:ReadBeastBreedingSlot()
end

-- 已孵化背包信息
SCAllBeastBagInfo = SCAllBeastBagInfo or BaseClass(BaseProtocolStruct)
function SCAllBeastBagInfo:__init()
    self.msg_type = 16199
end

function SCAllBeastBagInfo:Decode()
	self.beast_bag = {}
	for i = 1, BEAST_DEFINE.BEAST_BORN_COUNT_MAX do
		self.beast_bag[i] = ProtocolStruct:ReadBeastBagInfo(i)
	end
end
--------------------------驭兽天下协议161结束------------------
