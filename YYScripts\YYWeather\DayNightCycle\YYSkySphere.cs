﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[ExecuteInEditMode]
public class YYSkySphere : MonoBehaviour {

    public YYSkyProfile yYSkyProfile;

    //private readonly WeatherMakerMaterialCopy skySphereMaterial = new WeatherMakerMaterialCopy();
    private void OnEnable()
    {
        if(YYCommandBufferManager.Instance != null)
        {
            YYCommandBufferManager.Instance.RegisterPreCull(CameraPreCull, this);
        }
        
    }


    private void CameraPreCull(Camera camera)
    {
        if (yYSkyProfile != null && camera != null && isActiveAndEnabled && YYLightManager.Instance != null)
        {
            yYSkyProfile.UpdateSkySphere(camera, null, gameObject, YYLightManager.Instance.SunPerspective);
        }
    }


    private void OnDestroy()
    {
        if (YYCommandBufferManager.Instance != null)
        {
            YYCommandBufferManager.Instance.UnregisterPreCull(this);
        }
    }
}
