BossKillEveryWGData = BossKillEveryWGData or BaseClass()
function BossKillEveryWGData:__init()
    if BossKillEveryWGData.Instance ~= nil then
		print_error("[BossKillEveryWGData] attempt to create singleton twice!")
		return
	end
	BossKillEveryWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("boss_kill_every_cfg_auto")
    self.other_cfg = cfg.other
    self.condition_cfg = cfg.condition

	self.show_boss_list = {}

	self.open_flag = 0
	self.level = 0
	self.day_count = 0
	self.condition_num = 0
	self.ext_count = 0
	self.ext_emp_count = 0

	self:InitShowBossList()
end

function BossKillEveryWGData:__delete()
	BossKillEveryWGData.Instance = nil
end

function BossKillEveryWGData:SetBossKillEveryInfo(protocol)
	self.level = protocol.level
	self.day_count = protocol.day_count
	self.condition_num = protocol.condition_num
	self.open_flag = protocol.kill_boss_open_flag
	self.auto_kill_boss_flag = protocol.auto_kill_boss_flag
	self.ext_count = protocol.ext_count
	self.ext_emp_count = protocol.ext_emp_count
end

function BossKillEveryWGData:GetMiaoShaOpenFlag()
	return self.open_flag
end

function BossKillEveryWGData:GetAutoKillBossFlag()
	return self.auto_kill_boss_flag
end

function BossKillEveryWGData:GetMiaoShaOpenFlag()
	return self.open_flag
end

function BossKillEveryWGData:GetDayExtCount()
	return self.ext_count
end

function BossKillEveryWGData:GetDayExtEmpCount()
	return self.ext_emp_count
end

function BossKillEveryWGData:GetCurLevel()
	return self.level
end

function BossKillEveryWGData:GetCurDayKillCount()
	return self.day_count
end

function BossKillEveryWGData:GetCurConditionNum()
	return self.condition_num
end

function BossKillEveryWGData:GetOtherCfg()
	return self.other_cfg[1]
end

function BossKillEveryWGData:GetCurLevelCfg(level)
	return self.condition_cfg[level]
end

function BossKillEveryWGData:InitShowBossList()
	local tab_index_str = self.other_cfg[1].tab_index
	local tab_list = Split(tab_index_str, "|")
	for index, value in ipairs(tab_list) do
		self.show_boss_list[index] = tonumber(value)
	end
end

function BossKillEveryWGData:GetShowBossList()
	return self.show_boss_list
end

function BossKillEveryWGData:CheckShowBossList(show_index)
	local is_show = false
	for index, value in ipairs(self.show_boss_list) do
		if show_index == value then
			is_show = true
			break
		end
	end

	return is_show
end