TeamView = TeamView or BaseClass(SafeBaseView)
local YuanGuanCanSeeCount = 7 --目前展开远古仙殿可见数量
local TeamWorldInvite = "team_world_invite" --喊话
local RefreshTeamList = "refresh_team_list" --刷新队伍列表按钮

function TeamView:PingTaiLoadCallBack()
	local pt_btn_sel_fb_uiname_table = U3DNodeList(self.node_list["pt_btn_sel_fb_type"].uiname_table, self)
	self.pt_btn_sel_fb_type_nor = pt_btn_sel_fb_uiname_table["pt_btn_sel_fb_type_nor"]
	self.pt_btn_sel_fb_type_hl = pt_btn_sel_fb_uiname_table["pt_btn_sel_fb_type_hl"]
    self.node_list["pt_layout_blank_tip2"]:SetActive(false)
    -- self.node_list["lbl_bg"]:SetActive(false)
	self:PingTaiInitBtnList()
	self.invite_list = AsyncListView.New(PingTaiQuickListItem, self.node_list["pt_ph_quick_list"])
	XUI.AddClickEventListener(self.node_list["pt_btn_auto_match"], BindTool.Bind1(self.PingTaiOnClickAutoMatch, self)) 			--自动匹配 （只有队长或者不在队伍的单人 可以显示）
	XUI.AddClickEventListener(self.node_list["pt_btn_flush"], BindTool.Bind(self.PingTaiOnClickFlush, self, true)) 				--刷新列表
	XUI.AddClickEventListener(self.node_list["pt_btn_create_team"], BindTool.Bind1(self.PingTaiOnClickCrateTeam, self)) 		--创建队伍
	XUI.AddClickEventListener(self.node_list["pt_btn_goto_target"], BindTool.Bind1(self.PingTaiOnClickGoToTarget, self)) 		--前往目标
	XUI.AddClickEventListener(self.node_list["pt_btn_auto_req"], BindTool.Bind1(self.PingTaiAutoRequest, self))					--未组队：一键申请， 已组队：退出队伍
	XUI.AddClickEventListener(self.node_list["pt_btn_change_level_limit"], BindTool.Bind1(self.PingTaiOnClickChangeGoal, self)) --改变平台等级筛选
	XUI.AddClickEventListener(self.node_list["pt_level_limit_bg"], BindTool.Bind1(self.PingTaiOnClickChangeGoal, self)) 		--改变平台等级筛选
	XUI.AddClickEventListener(self.node_list["pt_btn_apply"], BindTool.Bind1(self.PingTaiOnClickApplyList, self)) 				--申请列表
	XUI.AddClickEventListener(self.node_list["pt_btn_speak"], BindTool.Bind1(self.PingTaiOnClickWorldTalk, self)) 				--喊话（世界， 跨服）
    XUI.AddClickEventListener(self.node_list["pt_btn_add_times"], BindTool.Bind1(self.PingTaiOnClickAddTimes, self)) 			--增加剩余奖励次数
	XUI.AddClickEventListener(self.node_list["pt_btn_sel_fb_type"], BindTool.Bind1(self.PingTaiOnClickSelFubenType, self))
	XUI.AddClickEventListener(self.node_list["pt_btn_close_type_list"], BindTool.Bind1(self.PingTaiOnClickCloseTypeList, self))
	XUI.AddClickEventListener(self.node_list["pt_btn_my_team"], BindTool.Bind1(self.PingTaiOnClickGotoMyTeam, self))
	XUI.AddClickEventListener(self.node_list["pt_level_limit_btn"], BindTool.Bind1(self.PingTaiOnClickLevLimit, self))
	XUI.AddClickEventListener(self.node_list["pt_btn_show_can_join"], BindTool.Bind1(self.PingTaiOnClickShowCanJoinTeam, self))

	self.change_filtrate_level_event = GlobalEventSystem:Bind(OtherEventType.SetTeamFiltrateLevel, BindTool.Bind(self.OnChangeFiltrateLevelCallBack, self))
	self.pingtai_day_count_change = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.OnPingTaiDayCountChange, self))
	self:OnTeamChange()
	self.is_only_show_can_join_team_list = false -- 这个可加入，只筛选了队伍有没有满
end

function TeamView:PingTaiReleaseCallBack()
    if self.invite_list then
		self.invite_list:DeleteMe()
		self.invite_list = nil
	end
	self.pingtai_cur_select_data = nil
	self.pingtai_select_toggle1 = nil
	if self.pingtai_team_goal_list then
		for k,v in pairs(self.pingtai_team_goal_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.pingtai_team_goal_list = nil
	end
	self.pingtai_has_specail_load = false
	self.pingtai_need_speail_load = false
	self.pingtai_btn_index = nil
	self.is_first_select_yg = nil
	self.pt_has_change_btn_gray = nil
	self.pingtai_left_btn_gameObject_list = nil

	OperateFrequency.ClearOperate(TeamWorldInvite)
	OperateFrequency.ClearOperate(RefreshTeamList)

    if self.change_filtrate_level_event ~= nil then
        GlobalEventSystem:UnBind(self.change_filtrate_level_event)
        self.change_filtrate_level_event = nil
    end

	if self.pingtai_day_count_change ~= nil then
        GlobalEventSystem:UnBind(self.pingtai_day_count_change)
        self.pingtai_day_count_change = nil
    end

	if self.pt_cancel_match_alert then
		self.pt_cancel_match_alert:DeleteMe()
		self.pt_cancel_match_alert = nil
	end

	if self.pt_toggle_list then
		self.pt_toggle_list = {} -- 可下拉菜单列表
	end

	self.pt_zhedie_index_list = nil
	self.pt_btn_sel_fb_type_nor = nil
	self.pt_btn_sel_fb_type_hl = nil
end

function TeamView:PingTaiShowIndexCallBack()
    self:PingTaiFlsuhLeftSelect()
end

function TeamView:PingTaiCloseCallBack()
	self.pingtai_btn_index = nil
	self.is_first_select_yg = nil
end

function TeamView:OnFlushPingTai(key, value)
	self:_InternalFlushList(key)
	if key == "HasZuDuiInfoChange" then
		if SocietyWGData.Instance:GetIsInTeam() == 1 then --如果有队伍
			self.node_list.pt_btn_auto_req_text.text.text = Language.NewTeam.QuitTeamBtnText 		--退出队伍
		else
			self.node_list.pt_btn_auto_req_text.text.text = Language.NewTeam.AutoReqBtnText 		--一键申请
			-- self.node_list["pt_btn_speak"]:CustomSetActive(false)
		end
		self:PingTaiFlushLevelLimit()
	end

	self:PingTaiFlushBtn()

	if key == "FlushRewardTimes" then
		self:PingTaiFlushRewardTimes()
	end

	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local is_no_goal = NewTeamWGData.Instance:GetIsNoGoal(now_team_type)
	local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
	local is_show_talk = is_in_team and SocietyWGData.Instance:GetTeamMemberCount() < (GoalQingYuanFbMaxCount[now_team_type] or 5)
	if is_in_team then
        -- if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
        --     self.node_list["pt_btn_apply"]:CustomSetActive(true)
        -- else
        --     self.node_list["pt_btn_apply"]:CustomSetActive(false)
        -- end
        self.node_list["pt_btn_my_team"]:CustomSetActive(true)
	else
		-- self.node_list["pt_btn_apply"]:CustomSetActive(false)
		self.node_list["pt_btn_my_team"]:CustomSetActive(false)
	end

	local pingtai_team_type , pingtai_team_fb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
	if GoalCanButTimesType[pingtai_team_type] then
		self.node_list["pt_btn_add_times"]:SetActive(true)
	else
		self.node_list["pt_btn_add_times"]:SetActive(false)
	end

	if self.pingtai_cur_select_data then
		if is_in_team then --有队伍
			self.node_list["pt_btn_create_team"]:CustomSetActive(false)
			-- self.node_list["pt_btn_auto_match"]:CustomSetActive(SocietyWGData.Instance:GetIsTeamLeader() == 1 
			-- 													and (self.pingtai_cur_select_data.team_type ~= 0 
			-- 													and self.pingtai_cur_select_data.team_type ~= -1))
		else --没队伍
			self.node_list["pt_btn_create_team"]:CustomSetActive(true)
			-- self.node_list["pt_btn_auto_match"]:CustomSetActive(self.pingtai_cur_select_data.team_type ~= 0 
			-- 													and self.pingtai_cur_select_data.team_type ~= -1)
		end
	end

	if NotShowBtnEnter[pingtai_team_type] then
		--self.node_list["pt_btn_auto_match"]:SetActive(false)
	end

	self:PTRemindState()
end

function TeamView:PTRemindState()
	local apply_list = SocietyWGData.Instance:GetReqTeamList()
	if # apply_list > 0 then
		self.node_list["PTRemind"]:SetActive(true)
		else
		self.node_list["PTRemind"]:SetActive(false)
	end
end

function TeamView:_InternalFlushList(key)
	if key == "PingTaiFlushList" then
		local team_list = NewTeamWGData.Instance:GetTargetTeamList()
		if #team_list == 0 then
			self.node_list["pt_layout_blank_tip2"]:SetActive(true)
			self.node_list["quick_list_bg"]:SetActive(false)
			local team_type, team_fb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
			local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, team_fb_mode)
			if team_type == -1 and team_fb_mode == -1 then
				self.node_list.pt_lbl_tips.text.text = Language.NewTeam.CurNotTeamTip
			else
				self.node_list.pt_lbl_tips.text.text = goal_info.empty_tips
			end
		else
			self.node_list["pt_layout_blank_tip2"]:SetActive(false)
			self.node_list["quick_list_bg"]:SetActive(true)
		end

		local _team_list = {}
		local is_my_team = false
		local my_uid = RoleWGData.Instance:InCrossGetOriginUid()

		if not self.is_only_show_can_join_team_list then
			for k,v in pairs(team_list) do
				for i, v1 in ipairs(v.member_info_list) do
					if v1.uid == my_uid then
						is_my_team = true
					end
				end
				if v.cur_member_num < MaxTeamCount then --  and not is_my_team 
					table.insert(_team_list, v)
				end
			end
		else
			_team_list = team_list
		end

		self.invite_list:SetDataList(_team_list)
		-- self.node_list["lbl_bg"]:SetActive(#team_list > 0)
	end
end

function TeamView:OnChangeFiltrateLevelCallBack()
	if self:IsLoadedIndex(TabIndex.team_pingtai) then
		self:_InternalFlushList("PingTaiFlushList")
        self:PingTaiFlushLevelLimit()
	end
end

function TeamView:PingTaiOnClickApplyList()
	NewTeamWGCtrl.Instance:OpenApplyView()
end

function TeamView:PingTaiOnClickQuick()
	if 0 == SocietyWGData.Instance:GetIsInTeam() then
		NewTeamWGCtrl.Instance:OpenQuickView()
	else
		NewTeamWGCtrl.Instance:ExitTeam()
	end
end

function TeamView:PingTaiFlushLevelLimit()
    local fil_min_level, fil_max_level = NewTeamWGData.Instance:GetCurFiltrateLevelLimit()
	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
    local str
    if fil_min_level and fil_max_level then
        str = string.format(Language.NewTeam.PTLevelLimitTop, fil_min_level, fil_max_level)
    else
        str = string.format(Language.NewTeam.PTLevelLimitTop, min_level, max_level)
    end
	EmojiTextUtil.ParseRichText(self.node_list["pt_level_limit_text"].emoji_text, str, 20, COLOR3B.DEFAULT)
end

function TeamView:PingTaiFlushRewardTimes()
    if self.pingtai_cur_select_data then
        --self.pingtai_cur_select_data.team_type, self.pingtai_cur_select_data.fb_mode
		--全部队伍 和 野外挂机 不需要显示
		if self.pingtai_cur_select_data.team_type == 0 or self.pingtai_cur_select_data.team_type == -1 then
			--self.node_list["pt_right_top"]:SetActive(false)
		else
			--self.node_list["pt_right_top"]:SetActive(true)
		end
		local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(self.pingtai_cur_select_data.team_type)
		if total_count == 0 then
			--self.node_list["pt_right_top"]:SetActive(false)
		end

		local remain_count = total_count - cur_enter_count
		local color = remain_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list.pt_reward_times_value.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, remain_count, total_count)
		self.node_list.pt_xiezhu:SetActive(false)--(GoalHasXieZhuType[self.pingtai_cur_select_data.team_type] and remain_count <= 0) or false)

		local xiezhu_times, total_xiezhu_times = NewTeamWGData.Instance:GetXieZhuTimesByTeamType(self.pingtai_cur_select_data.team_type)
		local color = total_xiezhu_times - xiezhu_times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list.pt_xiezhu_times_value.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, total_xiezhu_times - xiezhu_times, total_xiezhu_times)
    end
end

function TeamView:PingTaiFlushBtn()
	local pingtai_team_type , pingtai_team_fb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
	local is_match = NewTeamWGData.Instance:GetIsMatching()
	if is_match and now_team_type == pingtai_team_type then
		self.node_list["pt_btn_auto_match_text"].text.text = (Language.NewTeam.IsMatching)
	else
		self.node_list["pt_btn_auto_match_text"].text.text = (Language.NewTeam.AutoMatch)
	end
end

--初始化左侧按钮列表
function TeamView:PingTaiInitBtnList()
	if not self.pt_toggle_list then
		self.pt_toggle_list = {} -- 可下拉菜单列表
	end

	if not self.pt_zhedie_index_list then
		self.pt_zhedie_index_list = {} -- 下拉菜单的index列表
	end

	if not self.pingtai_left_btn_gameObject_list then
		self.pingtai_left_btn_gameObject_list = {} -- 左侧大按钮列表
	end
	self.pingtai_team_goal_list = {} 				--所有目标render

	local goal_list = NewTeamWGData.Instance:GetMenuBtnList(true)

	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    local btn_index = 1
	self.pingtai_total_complete_count = #goal_list 	--加载总数
	self.pingtai_cur_complete_count = 0 			--当前加载数
	local callback = function(btn, specail_list)
		local res_async_loader = AllocResAsyncLoader(self, "fb_item")
		res_async_loader:Load("uis/view/new_team_ui_prefab", "fb_item", nil, function(new_obj)
			for i,v in ipairs(specail_list) do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(btn, false)
				self.pingtai_team_goal_list[btn_index] = GoalListItemRender.New(obj)
				self.pingtai_team_goal_list[btn_index]:SetData(v)
				self.pingtai_team_goal_list[btn_index].view.button:AddClickListener(BindTool.Bind(self.PingTaiOnClickTeamGoal, self, self.pingtai_team_goal_list[btn_index], true))
				self.pingtai_team_goal_list[btn_index]:SetBtnIndex(btn_index)
				--记录当前组队类型的 index
				if not self.pt_zhedie_index_list[v.team_type] then
					self.pt_zhedie_index_list[v.team_type] = {}
				end

				table.insert(self.pt_zhedie_index_list[v.team_type], btn_index)
				if i == #specail_list then
					self.pingtai_has_specail_load = true
					if self.pingtai_need_speail_load then
						if self.pt_toggle_list and self.pt_toggle_list[now_team_type] then
							if self.pt_toggle_list[now_team_type].toggle.isOn == true then
								self.pt_toggle_list[now_team_type].toggle.isOn = false
								TryDelayCall(self,function ()
									self.pt_toggle_list[now_team_type].toggle.isOn = true
								end,0,"pt_toggle_list")
							else
								TryDelayCall(self,function ()
									self.pt_toggle_list[now_team_type].toggle.isOn = true
								end,0,"pt_toggle_list")
							end
						end
						self:PingTaiInitBtnSelectShow(v, true)
					end
				end
				btn_index = btn_index + 1
			end
		end)
	end

	for i, v in ipairs(goal_list) do
		local is_zhedie, zhedie_btn_count = NewTeamWGData.Instance:GetIsZheDieBtn(v.team_type)
		if is_zhedie then
			local async_loader = AllocAsyncLoader(self, "Content" .. i)
			async_loader:SetParent(self.node_list["pt_viewport"].transform)
			async_loader:Load("uis/view/new_team_ui_prefab", "Content", function(obj)
				self.pingtai_left_btn_gameObject_list[i] = obj
				self.pingtai_cur_complete_count = self.pingtai_cur_complete_count + 1
				local btn = obj.transform:Find("List1").transform
				local specail_list = NewTeamWGData.Instance:GetListByTeamType(v.team_type)
				if not self.pt_toggle_list[v.team_type] then
					self.pt_toggle_list[v.team_type] = {}
				end
				self.pt_toggle_list[v.team_type].btn_obj = obj.transform:Find("SelectBtn1/BtnObj"):GetComponent(typeof(UnityEngine.UI.Button))
				self.pt_toggle_list[v.team_type].toggle = obj.transform:Find("SelectBtn1"):GetComponent(typeof(UnityEngine.UI.Toggle))
				self.pt_toggle_list[v.team_type].toggle:AddValueChangedListener(BindTool.Bind(self.PingTaiInitBtnSelectShow, self, v))
				self.pt_toggle_list[v.team_type].btn_obj:AddClickListener(BindTool.Bind(self.PingTaiClickMenuSubButton, self, v, self.pt_toggle_list[v.team_type].toggle))

				callback(btn, specail_list)
			end)
		else
			local async_loader = AllocAsyncLoader(self, "btn_team_common" .. i)
			async_loader:SetParent(self.node_list["pt_viewport"].transform)
			async_loader:Load("uis/view/new_team_ui_prefab", "btn_team_common", function(obj)
				self.pingtai_left_btn_gameObject_list[i] = obj
				self.pingtai_cur_complete_count = self.pingtai_cur_complete_count + 1
				self.pingtai_team_goal_list[btn_index] = GoalListItemRender.New(obj)
				self.pingtai_team_goal_list[btn_index]:SetData(v)
				self.pingtai_team_goal_list[btn_index]:SetBtnIndex(btn_index)
				self.pingtai_team_goal_list[btn_index].view.button:AddClickListener(BindTool.Bind(self.PingTaiOnClickTeamGoal, self, self.pingtai_team_goal_list[btn_index], true))
				if now_team_type >= 0 and v.team_type == now_team_type and v.fb_mode == now_fb_mode then
					self:PingTaiOnClickTeamGoal(self.pingtai_team_goal_list[btn_index])
				elseif v.team_type == -1 and v.fb_mode == -1 then
					self:PingTaiOnClickTeamGoal(self.pingtai_team_goal_list[btn_index])
				end
                btn_index = btn_index + 1
			end)
		end
	end
end

--用来控制Toggle高亮
function TeamView:PingTaiClickMenuSubButton(data, toggle)
	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		if data.team_type ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CanNotChangeGoal)
			return
		end
	end
	--if NewTeamWGData.Instance:GetIsMatching() then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
	--	return
	--end
	--if toggle.isOn == false then
		toggle.isOn = not toggle.isOn
	--end
end

--左侧按钮排序(按照列表的顺序排序，具体的排序规则，交给Data排序)
function TeamView:PingTaiSortBtnSiblingIndex()
	if IsEmptyTable(self.pingtai_left_btn_gameObject_list) then
		return
	end
	for i, v in ipairs(self.pingtai_left_btn_gameObject_list) do
		v.transform:SetSiblingIndex(i - 1)
	end
	self.pingtai_left_btn_gameObject_list = nil
end

--点击左侧可折叠菜单
function TeamView:PingTaiInitBtnSelectShow(data ,is_on)
	if is_on then
		if self.pingtai_has_specail_load then
			local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
			local default_goal_index = NewTeamWGData.Instance:GetDefaultGoalByTeamType(data.team_type)
			local specail_list = NewTeamWGData.Instance:GetListByTeamType(data.team_type)
			local state = false
			for i,v in ipairs(specail_list) do
				if v.team_type == now_team_type and v.fb_mode == now_fb_mode then --default_goal_index == i then--
					self:PingTaiOnClickTeamGoal(self.pingtai_team_goal_list[self.pt_zhedie_index_list[v.team_type][i]])
					state = true
					break
				end
			end
			if not state then
				local default_goal_index = NewTeamWGData.Instance:GetDefaultGoalByTeamType(data.team_type)
				self:PingTaiOnClickTeamGoal(self.pingtai_team_goal_list[self.pt_zhedie_index_list[data.team_type][default_goal_index]])
			end
		else
			self.pingtai_need_speail_load = true
		end
	end
end

--is_click：是否是手动点击, 匹配中手动点击需要弹错误码, 匹配中自动调用， 只需要高亮显示按钮
function TeamView:PingTaiOnClickTeamGoal(cell, is_click)
    -- 2019/9/9 加上btn_index判断，避免频繁请求和刷新
    local btn_index = cell:GetBtnIndex()
    if is_click and self.pingtai_btn_index ~= nil and self.pingtai_btn_index == btn_index then
        return
    end

	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		if is_click and cell.data.team_type ~= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CanNotChangeGoal)
			return
		end
	end

    self.pingtai_btn_index = btn_index
	self.pingtai_cur_select_data = cell.data
	self:PingTaiFlushRewardTimes()

	self.node_list["pt_sel_fb_type_name_nor"].text.text = self.pingtai_cur_select_data.team_type_name
	self.node_list["pt_sel_fb_type_name_hl"].text.text = self.pingtai_cur_select_data.team_type_name

    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
    local top_lv = cell.data.role_max_level or top_user_level
	NewTeamWGData.Instance:SetCurFiltrateLevelLimit(cell.data.role_min_level, top_lv, false)

    self:PingTaiFlushLevelLimit()
	self:PingTaiOnClickSelectTeam(cell.data)
	self:PingTaiFlushAllHighLight(cell.data)
	self:FlushBtnActive()
	NewTeamWGData.Instance:SetPingTaiTeamTypeAndMode(cell.data.team_type, cell.data.fb_mode)
end

function TeamView:FlushBtnActive()
	local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
	if is_in_team then --有队伍
		--local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
		self.node_list["pt_btn_create_team"]:CustomSetActive(false)
		-- self.node_list["pt_btn_auto_match"]:CustomSetActive(is_leader
		-- 													and (self.pingtai_cur_select_data.team_type ~= 0 
		-- 													and self.pingtai_cur_select_data.team_type ~= -1))
		-- self.node_list["pt_level_limit_btn"]:CustomSetActive(is_leader)
		-- self.node_list["pt_btn_goto_target"]:CustomSetActive(is_leader
		-- 													and (self.pingtai_cur_select_data.team_type ~= 0 
		-- 													and self.pingtai_cur_select_data.team_type ~= -1))
	else --没队伍
		-- self.node_list["pt_level_limit_btn"]:CustomSetActive(false)
		-- self.node_list["pt_btn_goto_target"]:CustomSetActive((self.pingtai_cur_select_data.team_type ~= 0 
		-- 													and self.pingtai_cur_select_data.team_type ~= -1))
		self.node_list["pt_btn_create_team"]:CustomSetActive(true)
		-- self.node_list["pt_btn_auto_match"]:CustomSetActive(self.pingtai_cur_select_data.team_type ~= 0 
		-- 													and self.pingtai_cur_select_data.team_type ~= -1)
	end
	-- if self.pingtai_cur_select_data and self.pingtai_cur_select_data.team_type and NotShowBtnEnter[self.pingtai_cur_select_data.team_type] then
	-- 	self.node_list["pt_btn_auto_match"]:CustomSetActive(false)
	-- end
end

function TeamView:PingTaiFlushAllHighLight(data)
	for k,v in pairs(self.pingtai_team_goal_list) do
		local state = v.data.team_type == data.team_type and v.data.fb_mode == data.fb_mode
		v:OnSelectChange(state)
	end

	local is_zhedie = NewTeamWGData.Instance:GetIsZheDieBtn(data.team_type)
	if is_zhedie then --如果当前选中折叠菜单
		for i, v in pairs(self.pt_toggle_list) do
			if i == data.team_type then
				if v.toggle.isOn ~= true then
					v.toggle.isOn = true
				end
			else
				if v.toggle.isOn ~= false then
					v.toggle.isOn = false
				end
			end
		end
	else
		for i, v in pairs(self.pt_toggle_list) do
			if v.toggle then
				if v.toggle.isOn ~= false then
					v.toggle.isOn = false
				end
			end
		end
	end
end

function TeamView:PingTaiFlsuhLeftSelect()
	--local goal_list, specail_list = NewTeamWGData.Instance:GetTeamGoalSpecial(true)
	local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	if now_team_type >= 0 then
		for i,v in pairs(self.pingtai_team_goal_list) do
			if NewTeamWGData.Instance:GetIsZheDieBtn(v.team_type) then
				local sp_list = NewTeamWGData.Instance:GetListByTeamType(v.team_type)
				for i, v in pairs(sp_list) do
					if v.data and v.data.team_type == now_team_type and v.data.fb_mode == now_fb_mode then
						self:PingTaiOnClickTeamGoal(v)
						break
					end
				end
			else
				if v.data and v.data.team_type == now_team_type and v.data.fb_mode == now_fb_mode then
					self:PingTaiOnClickTeamGoal(v)
					break
				end
			end
		end
		--if self.pingtai_select_toggle1 and now_team_type == 4 then
		--	self.pingtai_select_toggle1.isOn = true
		--end
		if self.pt_toggle_list[now_team_type] then
			self.pt_toggle_list[now_team_type].toggle.isOn = true
		end
	elseif self.pingtai_team_goal_list[1] then
		self:PingTaiOnClickTeamGoal(self.pingtai_team_goal_list[1])
	end
end

function TeamView:PingTaiOnClickSelectTeam(data)
	if data == nil then return end
	local info = data
	---- 这里暂时写40 等副本完成再改
	--if info.fb_type == -1 and RoleWGData.Instance.role_vo.level >= FunOpen.Instance:GetFunByName(FunName.FubenpanelEquip).close_trigger_param then
	--	self.node_list["pt_btn_create_team"]:SetActive(false)
	--else
	--	self.node_list["pt_btn_create_team"]:SetActive(true)
	--end

	--没在队伍，才会去设置当前选中的类型和模式， 防止已经队伍，修改这个，会直接修改我的队伍的模式和类型
	--有队伍，就不设置了，单纯的选中，请求协议刷新列表
	if SocietyWGData.Instance:GetIsInTeam() == 0 and info.team_type >= 0 and info.fb_mode >= 0 then
		NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
	end
	SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode)
end

function TeamView:PingTaiOnClickWorldTalk()
	NewTeamWGCtrl.Instance:ShowTalkView()
	--if CountDownManager.Instance:HasCountDown("team_word_talk") then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CDWorldTalk)
	--	return
	--end
	--if MAX_TEAM_MEMBER_NUMS == #SocietyWGData.Instance:GetTeamMemberList() then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.ManyTower.TeamIsMaxMember)
	--	return
	--end
	--
	--local is_no_goal = NewTeamWGData.Instance:GetIsNoGoal(now_team_type)
	--if is_no_goal then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.TalkTip1)
	--	ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
	--	return
	--end
	--local function callback()
	--	GlobalEventSystem:Fire(TeamWorldTalk.MAIN_WORLD_TALK)
	--	XUI.SetGraphicGrey(self.node_list.pt_btn_speak,true)
	--	self.node_list.pt_btn_speak_text.text.text = string.format(Language.NewTeam.WorldTalk5,10)
	--end
	--OperateFrequency.Operate(callback, TeamWorldInvite, 10)
end

--点击增加奖励次数
function TeamView:PingTaiOnClickAddTimes()
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
	if team_type == GoalTeamType.Exp_DuJieXianZhou then 			-- 渡劫仙舟
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_WUJINJITAN)
	elseif team_type == GoalTeamType.QingYuanFb then				-- 情缘副本
        NewTeamWGCtrl.Instance:OnClickQingyuanAddTimes()
	elseif team_type == GoalTeamType.YuanGuXianDian then			-- 远古仙殿
		--FuBenPanelWGCtrl.Instance:OpenPetBuy(false, FUBEN_TYPE.HIGH_TEAM_EQUIP)
       -- FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.HIGH_TEAM_EQUIP)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.CanNoBuyTimes)
	--elseif team_type == GoalTeamType.ZhuShenTa then 				-- 诛神塔 --诸神塔不能购买次数
	elseif team_type == GoalTeamType.Exp_FuMoZhanChuan then 		-- 伏魔战船
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.LINGHUNGUANGCHANG)
	elseif team_type == GoalTeamType.ManHuangGuDian then 			-- 蛮荒古殿
		--FuBenPanelWGCtrl.Instance:OpenManHuangGuDianBuy()
        FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB)
	end
end

function TeamView:PingTaiOnClickSelFubenType()
	self:SetSelFubenTypeState()
end

function TeamView:PingTaiOnClickCloseTypeList()
	self:SetSelFubenTypeState(false)
end

function TeamView:PingTaiOnClickGotoMyTeam()
	self:ChangeToIndex(TabIndex.team_my_team)
end

function TeamView:PingTaiOnClickShowCanJoinTeam()
	self.is_only_show_can_join_team_list = not self.is_only_show_can_join_team_list
	self.node_list["pt_show_can_join_gou"]:SetActive(self.is_only_show_can_join_team_list)
	self:_InternalFlushList("PingTaiFlushList")
end

function TeamView:PingTaiOnClickLevLimit()
	if NewTeamWGData.Instance:GetIsMatching() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.AlertTipOK)
		return
	end
	if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
		return
	end
	NewTeamWGCtrl.Instance:OpenChangeGoalView(false)
end

function TeamView:SetSelFubenTypeState(is_show_btn_list)
	if nil == is_show_btn_list then
		self.pt_btn_sel_fb_type_nor:SetActive(not self.pt_btn_sel_fb_type_nor.gameObject.activeInHierarchy)
		self.pt_btn_sel_fb_type_hl:SetActive(not self.pt_btn_sel_fb_type_hl.gameObject.activeInHierarchy)
		self.node_list.left_list_bg:SetActive(not self.node_list.pt_left_list.gameObject.activeInHierarchy)
		self.node_list.pt_btn_close_type_list:SetActive(self.node_list.pt_left_list.gameObject.activeInHierarchy)
	else
		self.pt_btn_sel_fb_type_nor:SetActive(not is_show_btn_list)
		self.pt_btn_sel_fb_type_hl:SetActive(is_show_btn_list)
		self.node_list.left_list_bg:SetActive(is_show_btn_list)
		self.node_list.pt_btn_close_type_list:SetActive(is_show_btn_list)
	end
end

function TeamView:PingTaiOnClickChangeGoal()
	--if NewTeamWGData.Instance:GetIsMatching() then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchTip)
	--	return
	--end
	--if 1 == SocietyWGData.Instance:GetIsInTeam() and 0 == SocietyWGData.Instance:GetIsTeamLeader() then
	--	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
	--	return
	--end
	NewTeamWGCtrl.Instance:OpenChangeGoalView(true)
end

--自动匹配
function TeamView:PingTaiOnClickAutoMatch()
	local info = self.pingtai_cur_select_data
	if not info then
		return
    end
     
	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	--如果没在匹配中，才进行一些操作
	if not is_match then
		if info.team_type == GoalTeamType.QingYuanFb then
			MarryWGCtrl.Instance:SendQingyuanReqInfo()
		end
         --如果选中的是全部队伍，自动创建无目标
        if info.team_type == -1 and info.fb_mode == -1 then
            info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
        end

        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
        local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(info.team_type)
		local remain_count = total_count - cur_enter_count
		--组队的最大等级改为等级排行的第一名
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
        local top_lv = info.role_max_level or top_user_level
        if is_not_in_team then
            NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
            SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表
            if remain_count == 0 then 
				SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
				self:OnClickAddTimes()
				return
			end
        end
		NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
		NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, top_lv)
		NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)
		
		if remain_count == 0 and 1 == SocietyWGData.Instance:GetIsTeamLeader()  then 
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
			self:OnClickAddTimes()
			return
		end
		NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
	else
		local pingtai_team_type , pingtai_team_fb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
		local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
		if now_team_type == pingtai_team_type then
			if not self.pt_cancel_match_alert then
				self.pt_cancel_match_alert = Alert.New()
			end
			self.pt_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
			self.pt_cancel_match_alert:SetOkFunc(function()
				NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
			end)
			self.pt_cancel_match_alert:Open()
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
		end
	end
end

function TeamView:PingTaiOnClickFlush(bo)
	local function callback()
		self:PingTaiOnClickSelectTeam(self.pingtai_cur_select_data)
	end
	if bo then --只有点击按钮的时候，才有操作频繁判断
		OperateFrequency.Operate(callback, RefreshTeamList, 5)
	else
		callback()
	end
end

-- 已组队：前往目标
function TeamView:PingTaiOnClickGoToTarget()
	--if SocietyWGData.Instance:GetIsInTeam() == 1 then
	--	if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
	--		NewTeamWGCtrl.Instance:StartOrCancelTeamFB(0)
	--	else
	--		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotLeaderTip)
	--	end
	--end

	--跳转目标玩法入口界面
	local team_type, team_fb_mode = NewTeamWGData.Instance:GetPingTaiTeamTypeAndMode()
	if team_type == GoalTeamType.YuanGuXianDian then
		NewTeamWGData.Instance:SetPingTaiJumpYuanGuIndex(team_fb_mode)
	end
	local view_name, tab_index = NewTeamWGData.Instance:GetJumpViewNameAndIndex(team_type, team_fb_mode)
	FunOpen.Instance:OpenViewByName(view_name, tab_index)
end

-- 未组队：创建队伍
function TeamView:PingTaiOnClickCrateTeam()
	if SocietyWGData.Instance:GetIsInTeam() ~= 1 then
		local info = self.pingtai_cur_select_data
		if not info then
			return
		end
         --如果选中的是全部队伍，自动创建无目标
        if info.team_type == -1 and info.fb_mode == -1 then
            info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
        end

		--NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
		--NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, info.role_max_level)
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级
        local top_lv = info.role_max_level or top_user_level
		NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
		SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表
        NewTeamWGCtrl.Instance:Open({team_type = info.team_type, fb_mode = info.fb_mode, is_match = false})
	end
end


function TeamView:PingTaiAutoRequest()
	--未组队：一键申请， 已组队：退出队伍
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		self:PingTaiOnClickQuick()
	else
		self:PingTaiOnClickYiJianShenQing()
	end
end

--一键申请
function TeamView:PingTaiOnClickYiJianShenQing()
	local team_list = NewTeamWGData.Instance:GetTargetTeamList()
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
		return
	end
	for i, v in pairs(team_list) do
		SocietyWGCtrl.Instance:SendReqJoinTeam(v.team_index)
	end
	NewTeamWGCtrl.Instance:PingTaiClickFlush()
end

function TeamView:OnPingTaiDayCountChange(day_counter_id)
	if self:IsLoadedIndex(TabIndex.team_pingtai) then
		self:PingTaiFlushRewardTimes()
	end
end



------------------itemRender-----------------
PingTaiQuickListItem = PingTaiQuickListItem or BaseClass(BaseRender)

function PingTaiQuickListItem:__init()
	XUI.AddClickEventListener(self.node_list["btn_apply"], BindTool.Bind1(self.OnClickApply, self))
	XUI.AddClickEventListener(self.node_list["btn_invate"], BindTool.Bind1(self.OnClickInvate, self))
	XUI.AddClickEventListener(self.node_list["btn_fenshen_join"], BindTool.Bind1(self.OnClickFenShenApply, self))
end

function PingTaiQuickListItem:__delete()
	if nil ~= self.member_info_list then
		for i, v in pairs(self.member_info_list) do
			v:DeleteMe()
		end
		self.member_info_list = nil
	end
end

function PingTaiQuickListItem:OnFlush()
	-- if self.index % 2 == 1 then
	-- 	self.node_list["img9_render_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_5"))
	-- else
	-- 	self.node_list["img9_render_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	-- end

	--local leader = nil
	--是否是自己的队伍
	local is_my_team = false
	local member_count = 0
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	table.sort(self.data.member_info_list, SortTools.KeyUpperSorters("is_leader", "uid"))

	for i, v in ipairs(self.data.member_info_list) do
		--if v.is_leader == 1 then
			--leader = v
		if v.uid == my_uid then
			is_my_team = true
		end
		--头像信息相关
		if not self.member_info_list then
			self.member_info_list = {}
		end
		if not self.member_info_list[i] then
			self.member_info_list[i] = MemberHeadInfoRender.New(self.node_list["info" .. i])
		end
		self.member_info_list[i]:SetData(v)
		--end

		if v.uid ~= 0 then
			member_count = member_count + 1
			--self.node_list["prof"..i].image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(v.prof, v.sex)))
		else
			--self.node_list["prof"..i].image:LoadSprite(ResPath.GetCommonImages("a2_zudui_zhiyedi_kong"))
		end
	end
	--目标名（副本名）
	local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(self.data.team_type, self.data.team_fb_mode)
	local color = member_count == 5 and COLOR3B.GREEN or COLOR3B.D_PINK
	self.node_list["fb_name"].text.text = string.format(Language.NewTeam.TeamTypeTarget, info.team_type_name, color, member_count)
	-- if leader then
	-- 	--队长等级
	-- 	local level_str = string.format(Language.NewTeam.PTLevel2, leader.level)
	-- 	EmojiTextUtil.ParseRichText(self.node_list["lbl_leader_level"].emoji_text, level_str, 20, COLOR3B.WHITE)
	-- 	--队长名字
	-- 	self.node_list["team_name"].text.text = leader.name
	-- end

	--我的队伍标记
	self.node_list["my_team_flag"]:CustomSetActive(false)
	self.node_list["match_text"]:CustomSetActive(is_my_team and NewTeamWGData.Instance:GetIsMatching())
	--申请按钮
	self.node_list["btn_apply"]:CustomSetActive(not is_my_team)
	-- self.node_list["btn_fenshen_join"]:CustomSetActive(not is_my_team)

	--等级限制
	local str = string.format(Language.NewTeam.PTLevelLimit, self.data.min_limit, self.data.max_limit)
	EmojiTextUtil.ParseRichText(self.node_list["level_limit"].emoji_text, str, 18, COLOR3B.DEFAULT_NUM)
	--情缘副本只显示两个头像框
	local is_qingyuan = self.data.team_type == GoalTeamType.QingYuanFb
	if self.member_info_list[3] then
		self.member_info_list[3]:SetActive(not is_qingyuan)
	end
	---[[ 情缘副本满人暂时隐藏申请按钮,等后端处理满人队伍后可以删掉
	local count = self.data.cur_member_num or 0
	self.node_list["btn_apply"]:CustomSetActive(not is_my_team and (not is_qingyuan or count < QingYuanLimitCount))
	--]]
	local is_maxqingyuan = self.data.team_type == GoalTeamType.QingYuanFb and (count < QingYuanLimitCount)
	local is_maxmember = not is_qingyuan and count < MaxTeamCount
	self.node_list["btn_invate"]:CustomSetActive(is_my_team and (is_maxqingyuan or is_maxmember) and not NewTeamWGData.Instance:GetIsMatching())

	XUI.SetGraphicGrey(self.node_list["btn_apply"], SocietyWGData.Instance:GetIsInTeam() == 1)
end

function PingTaiQuickListItem:OnClickApply()
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.AlreadyInTeam)
		return
	end

	local role_level = RoleWGData.Instance.role_vo.level
	if role_level < self.data.min_limit or role_level > self.data.max_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyTeamError)
		return
	end

	SocietyWGCtrl.Instance:SendReqJoinTeam(self.data.team_index, 0)
	--NewTeamWGCtrl.Instance.quick_view:OnClickFlush()
	NewTeamWGCtrl.Instance:PingTaiClickFlush()
end

function PingTaiQuickListItem:OnClickFenShenApply()
    local is_fenshen_open = NewTeamWGData.Instance:GetFenshenFuncIsOpen()
	if not is_fenshen_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FenShenNotOpen)
		return
	end

	local role_level = RoleWGData.Instance.role_vo.level
	if role_level < self.data.min_limit or role_level > self.data.max_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.ApplyTeamError)
		return
	end

	SocietyWGCtrl.Instance:SendReqJoinTeam(self.data.team_index, 1)
	--NewTeamWGCtrl.Instance.quick_view:OnClickFlush()
	NewTeamWGCtrl.Instance:PingTaiClickFlush()
end

function PingTaiQuickListItem:OnClickInvate()
	NewTeamWGCtrl.Instance:OpenInviteView()
end

---------------------------------MemberHeadInfoRender----------------------------------------
---单条队伍列表单个头像信息
MemberHeadInfoRender = MemberHeadInfoRender or BaseClass(BaseRender)
function MemberHeadInfoRender:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end
function MemberHeadInfoRender:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end
function MemberHeadInfoRender:OnFlush()
	if not self.data then return end

	self.node_list.leader_img:CustomSetActive(self.data.is_leader == 1)
	--没有队员信息，隐藏相关信息
	--self.node_list.no_data_hide:CustomSetActive(self.data.uid > 0)
	--self.node_list.lbl_camp:CustomSetActive(false)--self.data.uid > 0)
	--self.node_list.label_camp:CustomSetActive(false)-- self.data.uid > 0)
	self.node_list.lbl_no_people:CustomSetActive(self.data.uid <= 0)
	self.node_list.have_people:CustomSetActive(self.data.uid > 0)
	local level_str = string.format(Language.NewTeam.PTLevel2, self.data.level)
	self.node_list.team_name.text.text = self.data.name
	EmojiTextUtil.ParseRichText(self.node_list.lbl_level.emoji_text, level_str, 18, COLOR3B.WHITE)

	if self.data.uid > 0 then
		--Vip等级
		--self.node_list.vip_level:SetActive(self.data.vip_level > 0)
		--self.node_list.vip_level.text.text = string.format(Language.NewTeam.VipLevel,  self.data.vip_level)

		-- local vip_res_name = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag) and "vip_hide" or "vip"..self.data.vip_level
		-- local bundle, asset = ResPath.GetVipIcon(vip_res_name)
		-- self.node_list.vip_image:SetActive(self.data.vip_level > 0)
		-- self.node_list.vip_image.image:LoadSpriteAsync(bundle, asset, function ()  		
		-- 	self.node_list.vip_image.image:SetNativeSize()
		-- end)
		--人物等级
		local str = string.format(Language.NewTeam.PTLevel, self.data.level)
		EmojiTextUtil.ParseRichText(self.node_list["role_level"].emoji_text, str, 21, COLOR3B.DEFAULT)
		self.node_list.lbl_camp.text.text = self.data.capability
		----头像
		local data = {}
		data.role_id = self.data.uid
		data.prof = self.data.prof
		data.sex = self.data.sex
		local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, self.data.shizhuang_photoframe)
		data.fashion_photoframe = image_cfg and image_cfg.resouce or 0
		self.head_cell:SetData(data)
	end
end