--龙战于野
DragonWildBuyTip = DragonWildBuyTip or BaseClass(BaseRender)

function DragonWildBuyTip:DoLoad(parent)
    self:LoadAsset("uis/view/dragon_temple_ui_prefab", "layout_dragon_wild_buy_tip", parent.transform)
end

function DragonWildBuyTip:__delete()
    if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end
end

function DragonWildBuyTip:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    XUI.AddClickEventListener(self.node_list.click, BindTool.Bind(self.OnClickGetBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickClose, self))
end

function DragonWildBuyTip:OnFlush()
    local list = DragonTempleWGData.Instance:GetShowBuyTipList()
    if IsEmptyTable(list) then
        return
    end

    for i, v in ipairs(list) do
        local is_buy = DragonTempleWGData.Instance:GetWildIsBuyFlag(v.seq)
        if not is_buy then
            self.cur_show_seq = i
            break
        end
    end

    self.cur_show_seq =  self.cur_show_seq or 1
    self.cur_show_data = list[self.cur_show_seq]

    self:FlushModel()
end

function DragonWildBuyTip:FlushModel()
    if not self.cur_show_data then
        return
    end

    local display_data = {}
    display_data.should_ani = true
    if self.cur_show_data.model_show_itemid ~= 0 and self.cur_show_data.model_show_itemid ~= "" then
        local split_list = string.split(self.cur_show_data.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = self.cur_show_data.model_show_itemid
        end
    end
    
    display_data.hide_model_block = true
    display_data.bundle_name = self.cur_show_data["model_bundle_name"]
    display_data.asset_name = self.cur_show_data["model_asset_name"]
    local model_show_type = tonumber(self.cur_show_data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    
    self.model_display:SetData(display_data)
    local scale = self.cur_show_data["display_scale_xiao"]
    Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
    if self.cur_show_data.display_pos and self.cur_show_data.display_pos_xiao ~= "" then
        local pos_list = string.split(self.cur_show_data.display_pos_xiao, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.display_model.rect, pos_x, pos_y)

    if self.cur_show_data.rotation and self.cur_show_data.rotation ~= "" then
        local rotation_tab = string.split(self.cur_show_data.rotation,"|")
        self.node_list["display_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
    end
end

function DragonWildBuyTip:OnClickGetBtn()
    ViewManager.Instance:Open(GuideModuleName.DragonTempleWildBuyView)
end

function DragonWildBuyTip:OnClickClose()
    DragonTempleWGData.Instance:SetIsShowTip(false)
    MainuiWGCtrl.Instance:FlushView(0, "wild_buy_tip")
end