function ServerActivityWGData:InitActBiPinCfg()
	local oga_rmb_buy_cfg = self.opengameactivity_auto.oga_rmb_buy
	self.all_oga_rmb_buy_cfg = oga_rmb_buy_cfg
	self.oga_rmb_buy_cfg = ListToMap(oga_rmb_buy_cfg, "rmb_type", "rmb_seq")
	self.oga_rmb_buy_day_cfg = ListToMap(oga_rmb_buy_cfg, "start_open_day", "rmb_seq")

	local oga_draw_cfg = self.opengameactivity_auto.oga_draw
	self.all_oga_draw_cfg = oga_draw_cfg
	self.oga_draw_cfg = ListToMap(oga_draw_cfg, "start_open_day", "reward_id")

	local oga_task_cfg = self.opengameactivity_auto.oga_task
	self.all_oga_task = self.opengameactivity_auto.oga_task
	self.oga_task = ListToMap(oga_task_cfg, "start_open_day", "seq")

	local oga_cumulate_recharge_cfg = self.opengameactivity_auto.oga_cumulate_recharge
	self.all_oga_cumulate_recharge = oga_cumulate_recharge_cfg
	self.oga_cumulate_recharge = ListToMap(oga_cumulate_recharge_cfg, "start_open_day", "seq")

	local all_oga_other_cfg = self.opengameactivity_auto.oga_other
	self.oga_other_cfg = {}
	for key, value in pairs(all_oga_other_cfg) do
		if not self.oga_other_cfg[value.map_type] then
			self.oga_other_cfg[value.map_type] = {}
		end
		table.insert(self.oga_other_cfg[value.map_type], value)
	end

	self:InitOGAParams()
end

function ServerActivityWGData:InitOGAParams()
	self.oga_daily_reward_flag = 0
	self.oga_rmb_buy_info = {}
	self.oga_task_fetch_flag_list = {}
	self.oga_task_process_list = {}
	self.oga_cumulate_recharge_fetch_flag_list = {}
	self.oga_recharge_num = 0

	self.is_skip_oga_lottery_anim = false
end

function ServerActivityWGData:ChangeIsSkipOGALotteryAnim()
    self.is_skip_oga_lottery_anim = not self.is_skip_oga_lottery_anim
end

function ServerActivityWGData:GetIsSkipOGALotteryAnim()
    return self.is_skip_oga_lottery_anim
end

function ServerActivityWGData:GetOgaDrawTurnTableRewardCfg(day)
	day = day or -1
	local tb_ret = {}
	for key, value in pairs(self.all_oga_draw_cfg) do
		if value.start_open_day	<= day and day <= value.close_open_day then
			table.insert(tb_ret, value)
		end
	end

	if not IsEmptyTable(tb_ret) then
		table.sort(tb_ret, SortTools.KeyLowerSorter("reward_id"))
	end
	return tb_ret
end

function ServerActivityWGData:GetOgaRmbCfgByDay(day)
	day = day or -1
	local tb_ret = {}
	for key, value in pairs(self.all_oga_rmb_buy_cfg) do
		if value.start_open_day	<= day and day <= value.close_open_day then
			table.insert(tb_ret, value)
		end
	end
	return tb_ret
end

function ServerActivityWGData:GetOgaLotteryTaskByDay(day)
	day = day or -1
	local tb_ret = {}
	local fetch_state, sort_val = 0, -1
	local recharge_num, cur_value = self:GetOGARechargeNum()
	for key, value in pairs(self.all_oga_task) do
		if value.start_open_day	<= day and day <= value.close_open_day then
			cur_value = self:GetOGATaskProcessValueByTaskType(value.type)
			fetch_state = self:GetOGATaskFetchInfoBySeq(value.seq)
			sort_val = cur_value >= value.param1 and -1 or 0
			sort_val = fetch_state == 1 and 1 or sort_val
			table.insert(tb_ret, {
				cfg_data = value,
				seq = value.seq,
				process_val = cur_value,
				sort_val = sort_val
			})
		end
	end

	if not IsEmptyTable(tb_ret) then
		table.sort(tb_ret, SortTools.KeyLowerSorters("sort_val", "seq"))
	end
	return tb_ret
end

function ServerActivityWGData:GetOgaRechargeTaskByDay(day)
	day = day or -1
	local tb_ret = {}
	local fetch_state, sort_val = 0, -1
	local recharge_num = self:GetOGARechargeNum()
	for key, value in pairs(self.all_oga_cumulate_recharge) do
		if value.start_open_day	<= day and day <= value.close_open_day then
			fetch_state = self:GetOGARechargeFetchInfoBySeq(value.seq)
			sort_val = recharge_num >= value.num and -1 or 0
			sort_val = fetch_state == 1 and 1 or sort_val
			table.insert(tb_ret, {
				cfg_data = value,
				seq = value.seq,
				sort_val = sort_val
			})
		end
	end

	if not IsEmptyTable(tb_ret) then
		table.sort(tb_ret, SortTools.KeyLowerSorters("sort_val", "seq"))
	end
	return tb_ret
end


function ServerActivityWGData:SetOGAExtendAllInfo(protocol)
	self.oga_daily_reward_flag 					= protocol.daily_reward_flag
	self.oga_rmb_buy_info 						= protocol.rmb_buy_info
	self.oga_task_fetch_flag_list 				= protocol.task_fetch_flag_list
	self.oga_task_process_list 					= protocol.task_process_list
	self.oga_cumulate_recharge_fetch_flag_list 	= protocol.cumulate_recharge_fetch_flag_list
	self.oga_recharge_num						= protocol.recharge_num
end

function ServerActivityWGData:UpdateOGARmbBuyInfo(protocol)
	local ret_info = protocol.ret_info
	if ret_info then
		self.oga_rmb_buy_info[ret_info.seq] = ret_info.buy_times
	end
end

function ServerActivityWGData:UpdateOGATaskProcessInfo(protocol)
	local ret_info = protocol.ret_info
	if ret_info then
		self.oga_task_process_list[ret_info.type] = ret_info.task_process
	end
end

function ServerActivityWGData:UpdateOGATaskFetchFlagInfo(protocol)
	local ret_info = protocol.ret_info
	if ret_info then
		self.oga_task_fetch_flag_list = ret_info
	end
end

function ServerActivityWGData:UpdateOGACumulateRechargeInfo(protocol)
	local ret_info = protocol.ret_info
	if ret_info then
		self.oga_cumulate_recharge_fetch_flag_list = ret_info.cumulate_recharge_fetch_flag_list
		self.oga_recharge_num = ret_info.recharge_num
	end
end

function ServerActivityWGData:GetOGARmbBuyInfoBySeq(seq)
	return self.oga_rmb_buy_info[seq]
end

function ServerActivityWGData:GetOGATaskProcessValueByTaskType(task_type)
	return self.oga_task_process_list[task_type] or 0
end

function ServerActivityWGData:GetOGATaskFetchInfoBySeq(seq)
	return self.oga_task_fetch_flag_list[seq]
end

function ServerActivityWGData:GetOGARechargeFetchInfoBySeq(seq)
	return self.oga_cumulate_recharge_fetch_flag_list[seq]
end

function ServerActivityWGData:GetOGARechargeNum()
	return self.oga_recharge_num
end

function ServerActivityWGData:GetOARechargeRewardRemind()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local all_task_info = self:GetOgaRechargeTaskByDay(open_day)
	local have_recharge_num = self:GetOGARechargeNum()
    local is_can_get = false
    for index, value in ipairs(all_task_info) do
        local cfg_data = value.cfg_data
        local target_num = cfg_data.num or 0
        local flag_status = self:GetOGARechargeFetchInfoBySeq(cfg_data.seq)
        is_can_get = have_recharge_num >= target_num and flag_status == 0
        if is_can_get then
            break
        end
    end

	return is_can_get
end

function ServerActivityWGData:GetOGADailyRechargeModelCfg()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local target_cfg = self.oga_other_cfg[0]
	local tb_ret = {}
	if target_cfg then
		for key, value in pairs(target_cfg) do
			if value.start_open_day	<= open_day and open_day <= value.close_open_day then
				tb_ret = value
			end
		end
	end
	return tb_ret
end

function ServerActivityWGData:GetOGACloudBuyCfg()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local target_cfg = self.oga_other_cfg[1]
	local tb_ret = {}
	if target_cfg then
		for key, value in pairs(target_cfg) do
			if value.start_open_day	<= open_day and open_day <= value.close_open_day then
				tb_ret = value
			end
		end
	end
	return tb_ret
end

function ServerActivityWGData:GetOGADrawCostItemId()
	if not self.draw_cost_item_id and (not self.draw_cost_num)then
		local other_cfg = self.opengameactivity_auto.other[1] or {}
		if other_cfg.draw_item then
			self.draw_cost_item_id = other_cfg.draw_item.item_id
			self.draw_cost_num = other_cfg.draw_item.num
		end
	end
	return self.draw_cost_item_id, self.draw_cost_num
end

function ServerActivityWGData:GetOGARechargeRemind()
	local ret_num = 0
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = self:GetOgaRechargeTaskByDay(open_day)
	if not IsEmptyTable(cfg) then
		for key, value in pairs(cfg) do
			if value.sort_val == -1 then
				ret_num = 1	
				break
			end
		end
	end
	return ret_num
end

function ServerActivityWGData:GetOGALotteryRemind()
	local ret_num = 0
	if ret_num <= 0 then
		local cost_item_id, draw_cost_num = self:GetOGADrawCostItemId()
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
		ret_num = have_num < draw_cost_num and 0 or 1
	end

	if ret_num <= 0 then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local cfg = self:GetOgaLotteryTaskByDay(open_day)
		if not IsEmptyTable(cfg) then
			for key, value in pairs(cfg) do
				if value.sort_val == -1 then
					ret_num = 1	
					break
				end
			end
		end
	end
	return ret_num
end

function ServerActivityWGData:GetOGADailyRewardFlag()
	return self.oga_daily_reward_flag
end

--策划要求，屏蔽每日奖励.
function ServerActivityWGData:GetOGADailyRewardRemind()
	-- local ret_num = 0
	-- if ret_num <= 0 then
	-- 	ret_num = self.oga_daily_reward_flag == 0 and 1 or 0
	-- end
	return 0
end

function ServerActivityWGData:GetOGAExtendIsShow()
	if nil == self.show_oga_extend_part then
		self.show_oga_extend_part = false
		local other_cfg = self.opengameactivity_auto.other[1] or {}
		if other_cfg.show_oga_extend_part then
			self.show_oga_extend_part = other_cfg.show_oga_extend_part == 1
		end
	end
	return self.show_oga_extend_part
end