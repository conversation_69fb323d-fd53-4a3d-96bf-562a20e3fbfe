-- 模型调节面板
GundamModelTestView = GundamModelTestView or BaseClass(SafeBaseView)

function GundamModelTestView:__init()
    self.view_style = ViewStyle.Full
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/role_model_test_ui_prefab", "layout_gundam_model_test")
end

function GundamModelTestView:__delete()
end

function GundamModelTestView:LoadCallBack()
    self.gundam_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["display"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = false,
    }
    
    self.gundam_model:SetRenderTexUI3DModel(display_data)
	-- self.gundam_model:SetUI3DModel(self.node_list["display"].transform, self.node_list["tigger_listen"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)

    self.cur_select_data = nil
	self.test_model_btn_list = AsyncListView.New(TestGundamModelBtn, self.node_list["btn_list"])
	self.test_model_btn_list:SetSelectCallBack(BindTool.Bind(self.OnClickBtn, self))
end

function GundamModelTestView:ReleaseCallBack()
	if self.gundam_model then
		self.gundam_model:DeleteMe()
		self.gundam_model = nil
	end

    if self.test_model_btn_list then
		self.test_model_btn_list:DeleteMe()
		self.test_model_btn_list = nil
        self.cur_select_data = nil
	end
end

function GundamModelTestView:OnClickBtn(cell)
    if not cell or not cell.data then return end

    self.cur_select_data = cell.data
    self:FlushModel()
end

function GundamModelTestView:OnFlush()
    local data_list = self:GetShowDalist()
    self.test_model_btn_list:SetDataList(data_list)
    self.test_model_btn_list:JumpToIndex(1, 22)
end

function GundamModelTestView:GetPartMapCfg1(mechan_seq, part, color, weapon_type)
    if not self.part_map_cfg1 then
        local cfg = ConfigManager.Instance:GetAutoConfig("mechan_cfg_auto").part
        self.part_map_cfg1 = ListToMap(cfg, "mechan_seq", "part", "sort", "weapon_type")
    end

    return (((self.part_map_cfg1[mechan_seq] or {})[part] or {})[color] or {})[weapon_type]
end

function GundamModelTestView:GetShowDalist()
    local data_list = {}
    local cfg = ConfigManager.Instance:GetAutoConfig("mechan_cfg_auto")
    local base_cfg = cfg.base
    local color_num = 3
    local weapon_type_num = 2
    local part_num = 8
    local name_str = "%s%d 武器%d"

    for i = 0, #base_cfg do
        local cell_data = base_cfg[i]
        for weapon_type = 0, weapon_type_num - 1 do
            for j = 1, color_num do
                local data = {}
                data.seq = cell_data.seq
                data.name = string.format(name_str, cell_data.name, j, weapon_type)
                data.part_res_list = {}
                for part = 0, part_num - 1 do
                    local new_weapon_type = part ~= MECHA_PART_TYPE.WEAPON and 0 or weapon_type
                    local part_cfg = self:GetPartMapCfg1(cell_data.seq, part, j, new_weapon_type)
                    data.part_res_list[part] = part_cfg and part_cfg.res_id or 0
                end

                table.insert(data_list, data)
            end
        end
    end

    return data_list
end

function GundamModelTestView:FlushModel()
    if not self.cur_select_data then
        return
    end

    local part_list = self.cur_select_data.part_res_list

    local part_info = {
		gundam_seq = self.cur_select_data.seq,
		gundam_body_res = part_list[MECHA_PART_TYPE.BODY],
        gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON],
		gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND],
        gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND],
		gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT],
        gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT],
		gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING],
        gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING],
	}

    self.gundam_model:SetGundamModel(part_info)
end











----------------------------------------------------------------------------------
TestGundamModelBtn = TestGundamModelBtn or BaseClass(BaseRender)

function TestGundamModelBtn:__init()

end

function TestGundamModelBtn:OnFlush()
	self.node_list["btn_name"].text.text = self.data.name
end

function TestGundamModelBtn:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
end