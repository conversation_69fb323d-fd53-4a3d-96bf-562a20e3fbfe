LongHunView = LongHunView or BaseClass(SafeBaseView)

function LongHunView:InitXiangQian()
	self.node_list["btn_longhun_insert"].button:AddClickListener(BindTool.Bind(self.OnClickLongHunCompose,self))
	self.node_list["btn_longhun_upgrade"].button:AddClickListener(BindTool.Bind(self.OnClickLongHunFenJie,self))
	self.node_list["longhun_getway"].button:AddClickListener(BindTool.Bind(self.OpenLongHunShowWay,self))
	self.node_list["all_attr_btn"].button:AddClickListener(BindTool.Bind(self.OpenLongHunAttr,self))
	self.node_list["AttrPanelBtn"].button:AddClickListener(BindTool.Bind(self.CloseLongHunAttr,self))
	self.node_list["btn_question"].button:AddClickListener(BindTool.Bind(self.Click<PERSON>ian<PERSON>ian<PERSON><PERSON><PERSON>,self))
	self.node_list["btn_once_up"].button:AddClickListener(BindTool.Bind(self.OnClickQuickUpGrade,self))
	
	
	for i= 1,3 do 
		self.node_list["money_btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickMoney,self,i))
	end
	
	self.node_list["cancle_bg"].button:AddClickListener(BindTool.Bind(self.OutSelectMode,self))

	self.longhun_bag_list = AsyncListView.New(LongHunRuneCell, self.node_list.longhun_bag_list)
	self.xianqian_cell_list = {}
	for i= 1,7 do
		self.xianqian_cell_list[i] = LongHunXiangQianCell.New(self.node_list["longhun_point"..i])
		self.xianqian_cell_list[i]:SetIndex(i)
		self.xianqian_cell_list[i]:SetSelectCallBack(BindTool.Bind(self.LongHunEquipSelect,self))
	end
	self.cur_select_index = 1
	for i = 1, 7 do
		self.node_list["black_mask"..i]:SetActive(false)
		self.node_list["rune_select"..i]:SetActive(false)
	end
	self.need_quick_upgrade = false
	self.node_list["up_grade_text"].text.text = Language.LongHunView.UpgradeText[1]
	if not self.long_hun_equip_change then
		self.long_hun_equip_change = GlobalEventSystem:Bind(OtherEventType.LONG_HUN_EQUIP_CHANGE,BindTool.Bind(self.CheckContiuteUpGrade2,self))
	end
	self:OutSelectMode()
end

function LongHunView:DeleteXiangQian()
	
end

function LongHunView:ReleaseXiangQian()
	if self.longhun_bag_list then
		self.longhun_bag_list:DeleteMe()
		self.longhun_bag_list = nil
	end

	if self.xianqian_cell_list then
		for i= 1,7 do
			self.xianqian_cell_list[i]:DeleteMe()
		end
		self.xianqian_cell_list = nil
	end

	if self.longhun_attr_list then
		self.longhun_attr_list:DeleteMe()
		self.longhun_attr_list = nil
	end

	if self.delay_up_grade_timer then
		GlobalTimerQuest:CancelQuest(self.delay_up_grade_timer)
		self.delay_up_grade_timer = nil
	end

	if self.long_hun_equip_change then
		GlobalEventSystem:UnBind(self.long_hun_equip_change)
		self.long_hun_equip_change = nil
	end

	self:CanFenJieCleAni()
end

function LongHunView:OpenLongHunAttr()
	local attr_name_table,attr_num_table,attr_is_pre = LongHunWGData.Instance:GetAllEquipAttr()
	local all_data = {}
	local num = 0
	for k,v in pairs(attr_name_table) do
		num = num + 1
		all_data[num] = {}
		all_data[num].attr_name = v
		all_data[num].attr_value = attr_num_table[k]
		all_data[num].is_pre = attr_is_pre[k]
	end

	self.node_list["no_attr"]:SetActive(num == 0)

	if nil == self.longhun_attr_list then
		self.longhun_attr_list = AsyncListView.New(LongHunAttrCell, self.node_list.attr_list)
	end
	self.longhun_attr_list:SetDataList(all_data)
	self.node_list["AttrPanel"]:SetActive(true)

end

function LongHunView:CloseLongHunAttr()
	self.node_list["AttrPanel"]:SetActive(false)
end

function LongHunView:OpenLongHunShowWay()
	ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_bootybay)
	--LongHunWGCtrl.Instance:OpenLongHunShowWay()
end


function LongHunView:OnClickLongHunFenJie()
	LongHunWGCtrl.Instance:OpenLongHunFenJie()
end


function LongHunView:OnClickLongHunCompose()
	ViewManager.Instance:Open(GuideModuleName.Compose,TabIndex.other_compose_longhu)
	--self:Close()
end

function LongHunView:ClickXianQianQuestion()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetContent(Language.LongHunView.XianQianRule, Language.LongHunView.XianQianRuleTitle)
	end
end

function LongHunView:OnClickMoney(index)
	local item_id1, item_id2, item_id3 = LongHunWGData.Instance:GeiCostMoneyID()
	if index == 1 and item_id1 ~= 0 then
		TipWGCtrl.Instance:OpenItem({item_id = item_id1})
	elseif index == 2 and item_id2 ~= 0 then
		TipWGCtrl.Instance:OpenItem({item_id = item_id2})
	elseif index == 3 and item_id3 ~= 0 then
		TipWGCtrl.Instance:OpenItem({item_id = item_id3})
	end
end

function LongHunView:CheckContiuteUpGrade2()
	self.is_need_result_come_back = false
	self:CheckContiuteUpGrade()
end

function LongHunView:CheckContiuteUpGrade()
	if self.need_quick_upgrade then
		self:DoQuickUpGrade()
	end
end

function LongHunView:OnClickQuickUpGrade()
	self.need_quick_upgrade = not self.need_quick_upgrade
	if self.need_quick_upgrade then
		self.node_list["up_grade_text"].text.text = Language.LongHunView.UpgradeText[2]
		self:DoQuickUpGrade()
	else
		self.node_list["up_grade_text"].text.text = Language.LongHunView.UpgradeText[1]
	end

end

function LongHunView:DoQuickUpGrade()
	if self.is_need_result_come_back then
		return
	end

	if self.delay_up_grade_timer then
		return
	end

	self.delay_up_grade_timer = GlobalTimerQuest:AddDelayTimer(function ()
		GlobalTimerQuest:CancelQuest(self.delay_up_grade_timer)
		self.delay_up_grade_timer = nil
		self:CheckContiuteUpGrade()
	end,0.2)
		
	local can_up_grade = 0
	local next_up_grade = -1
	local next_up_level = -1
	local is_special = -1
	for i =1, 7 do
		can_up_grade = LongHunWGData.Instance:LongHunCanUpGrade(i)
		if can_up_grade == 1 and next_up_grade == -1 then
			next_up_grade = i
			local equip_cfg = LongHunWGData.Instance:GetEquipPosyDataBySlot(i)
			next_up_level = equip_cfg.lifesoul.level
		elseif can_up_grade == 1 then
			local equip_cfg = LongHunWGData.Instance:GetEquipPosyDataBySlot(i)
			if next_up_level > equip_cfg.lifesoul.level then
				next_up_grade = i
				next_up_level = equip_cfg.lifesoul.level
			end
		end
	end
	if next_up_grade ~= -1 then
		is_special = next_up_grade > LongHunWGData.Instance:GetNormalSlotNum() and 1 or 0
		if is_special == 1 then
			next_up_grade = next_up_grade - LongHunWGData.Instance:GetNormalSlotNum() - 1
		else
			next_up_grade = next_up_grade - 1
		end
		self.is_need_result_come_back = true
		LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_UP_LEVEL,next_up_grade,is_special)
	else
		if self.need_quick_upgrade then
			self:OnClickQuickUpGrade()
		end
		TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.NoOneCanUpGrade)
	end
end


function LongHunView:FlushXiangQian()
	self:FlushXiangQianRight()
	self:FlushXiangQianLeft()
end
function LongHunView:FlushXiangQianRight()
	local all_data,all_grid_num = LongHunWGData.Instance:GetPosyBagListData()
	self.node_list["longhun_bag_list"]:SetActive(all_grid_num > 0)
	self.node_list["blank_tips"]:SetActive(all_grid_num == 0)
	if all_grid_num == 0 then

	else
		for k,v in pairs(all_data) do
			v.select_callback = BindTool.Bind(self.ClickLongHunCallBack,self)
		end
		self.longhun_bag_list:SetDataList(all_data)
	end
	self.node_list["fenjie_red"]:SetActive(LongHunWGData.Instance:IsShowLongHunFenJieRedPoint() == 1)
end

function LongHunView:FlushFenjieRed()
	self.node_list["fenjie_red"]:SetActive(LongHunWGData.Instance:IsShowLongHunFenJieRedPoint() == 1)
end

function LongHunView:FlushXiangQianLeft()
	for k,v in pairs(self.xianqian_cell_list) do
		v:Flush()
	end
	self:FLushXiangQianCellHL()
	local money1,money2 = LongHunWGData.Instance:GetLongHunMoney()
	local item_id1, item_id2, item_id3 = LongHunWGData.Instance:GeiCostMoneyID()
	local num2 = ItemWGData.Instance:GetItemNumInBagById(item_id2)
	local num3 = ItemWGData.Instance:GetItemNumInBagById(item_id3)
	self.node_list["longhun_money1"].text.text = money1
	self.node_list["longhun_money2"].text.text = num2
	self.node_list["longhun_money3"].text.text = num3
	self.node_list["once_up_red"]:SetActive(false)
	for i = 1 , 7 do
		local can_up_grade = LongHunWGData.Instance:LongHunCanUpGrade(i)
		if can_up_grade == 1 then
			self.node_list["once_up_red"]:SetActive(true)
			break
		end
	end
end

function LongHunView:FLushXiangQianCellHL()
	for k,v in pairs(self.xianqian_cell_list) do
		v:FlushHL(self.cur_select_index)
	end
end

function LongHunView:ChangeInToSelectMode(hight_light_num,hight_light_list,data)
	self.longhun_in_xiangqian_mode = true
	self.longhun_bag_list:RefreshActiveCellViews()
	TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.PleaseSelectEquip)
	for i = 1, 7 do
		self.node_list["black_mask"..i]:SetActive(true)
		self.node_list["rune_select"..i]:SetActive(false)
		self.node_list["up_down"..i]:SetActive(false)
	end
	local a_data = {}
	a_data.item_id = data.item_id
	a_data.level = 1
	local _,power_1 = LongHunWGData.Instance:GetEquipLongHunAttrListByItemId(a_data)
	local b_data = {}
	for k,v in pairs(hight_light_list) do
		self.node_list["black_mask"..v]:SetActive(false)
		self.node_list["rune_select"..v]:SetActive(true)
		b_data = LongHunWGData.Instance:GetEquipPosyDataBySlot(v)
		if b_data and b_data.lifesoul and b_data.lifesoul.item_id > 0 then
			local _,power_2 = LongHunWGData.Instance:GetEquipLongHunAttrListByItemId({item_id = b_data.lifesoul.item_id,level = 1})
			if power_2 < power_1 then
				self.node_list["up_down"..v]:SetActive(true)
			end
		end
	end
	self.select_mode_data = data
	self.hight_light_list = hight_light_list
	self.node_list["cancle_bg"]:SetActive(true)
end

function LongHunView:OutSelectMode()
	self.node_list["cancle_bg"]:SetActive(false)
	self.longhun_in_xiangqian_mode = false
	LongHunWGData.Instance:SetLongHunBagSelectIndex(-1)
	self.longhun_bag_list:RefreshActiveCellViews()
	self.select_mode_data = nil
	for i = 1, 7 do
		self.node_list["black_mask"..i]:SetActive(false)
		self.node_list["rune_select"..i]:SetActive(false)
		self.node_list["up_down"..i]:SetActive(false)
	end
	self.hight_light_list = nil
end

function LongHunView:ClickLongHunCallBack(data)
	-- TipWGCtrl.Instance:OpenItem(data,ItemTip.FROME_LONGHUN_CHECK,nil,nil,btn_callback_event)
	-- 	btn_callback_event[1] = {btn_text = Language.MountLingChongEquip.Btn_text[self.show_type], callback = function()
	-- 	self:OnOpenEquip()
	-- 		end}

	if not IsEmptyTable(data) and data.type > 0 then

		if self.longhun_in_xiangqian_mode then
			self:OutSelectMode()
		end
		local btn_index = 0
		local click_call_back = {}
		LongHunWGData.Instance:SetLongHunBagSelectIndex(data.index)
		local index = self.cur_select_index
		if data.type > 1 then
			local hight_light_num,hight_light_list,wear_type = LongHunWGData.Instance:JudjementReallyPutIndex(data,index)
			local remind = LongHunWGData.Instance:GetBagIndexHaveRed(data.index)
			btn_index = btn_index + 1
			click_call_back[btn_index] = {}
			click_call_back[btn_index].show_red = remind and 1 or 0
			click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[1]
			if wear_type == 1 then
				click_call_back[btn_index].callback = function ()
					local is_special = 0
					is_special = hight_light_list[1] == 7 and 1 or 0
					if is_special == 0 then
						hight_light_list[1] = hight_light_list[1] - 1
					else
						hight_light_list[1] = 0
					end
					LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_PUT_SLOT,hight_light_list[1],data.index,is_special)
				end
			else
				click_call_back[btn_index].callback = function ()
					self:ChangeInToSelectMode(hight_light_num,hight_light_list,data)
				end
			end
			-- if finally_index == -1 then
			-- 	click_call_back[1].callback = function ()
			-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.HaveSameAttr)
			-- 	end
			-- elseif finally_index == -2 then
			-- 	click_call_back[1].callback = function ()
			-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.HaveOtherAttr)
			-- 	end
			-- elseif finally_index == -3 then
			-- 	local is_special = LongHunWGData.Instance:IsSpecialPosyType(data.type)
			-- 	click_call_back[1].callback = function ()
			-- 		if is_special then
			-- 			TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.NeedUnEquipSpecial)
			-- 		else
			-- 			TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.NeedUnEquipOne)
			-- 		end
			-- 	end
			-- else
			-- 	index = finally_index
			-- 	local is_special = 0
			-- 	if index > LongHunWGData.Instance:GetNormalSlotNum() then
			-- 		index = index - LongHunWGData.Instance:GetNormalSlotNum() - 1
			-- 		is_special = 1
			-- 	else
			-- 		index = index - 1
			-- 		is_special = 0
			-- 	end
			-- 	click_call_back[1].callback = function ()
			-- 		LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_PUT_SLOT,index,data.index,is_special)			
			-- 	end
			-- end
		end

		-- if data.quality >=3 and data.type > 1 then
		-- 	btn_index = btn_index + 1
		-- 	click_call_back[btn_index] = {}
		-- 	click_call_back[btn_index].show_red = 0
		-- 	click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[2]
		-- 	click_call_back[btn_index].callback = function ()
		-- 		ViewManager.Instance:Open(GuideModuleName.Compose,TabIndex.other_compose_longhu)
		-- 		--self:Close()
		-- 	end
		-- end
		
		btn_index = btn_index +1
		click_call_back[btn_index] = {}
		click_call_back[btn_index].show_red = 0
		click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[3]
		local compose_data = LongHunWGData.Instance:GetItemComposeData(data.item_id)
		if not IsEmptyTable(compose_data) then
			local compose_longhun = Split(compose_data.lifesoul_id_vec,"|")
			if tonumber(compose_longhun[1]) <= 0 then
				click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[6]
			else
				click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[3]
			end

		else
			click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[6]
		end
		

		if data.is_lock == 1 then
			click_call_back[btn_index].callback = function ()
				TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.IsLocked)
			end
		elseif data.type == 1 then
			local count = 1
			local list = {}
			list[1] = data.index
			click_call_back[btn_index].callback = function ()
				LongHunWGCtrl.Instance:SendLongHunDeCompose(count, list)
				--self:PlayFenJieAni()
			end
		else
			click_call_back[btn_index].callback = function ()
				LongHunWGCtrl.Instance:OpenFenJieResultTips(data) --分解确认展示
				--LongHunWGCtrl.Instance:SendLongHunDeCompose(count, list)
			end
		end

		-- if data.type > 1 then
		-- 	btn_index = btn_index + 1
		-- 	click_call_back[btn_index] = {}
		-- 	click_call_back[btn_index].show_red = 0
		-- 	if data.is_lock == 1 then
		-- 		click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[5]
		-- 	else
		-- 		click_call_back[btn_index].btn_text = Language.LongHunView.LongHunOpera1[4]
		-- 	end
		-- 	click_call_back[btn_index].callback = function ()
		-- 		LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_LOCK_EVEN,LIFESOUL_INDEX_TYPE_BAG,data.index)
		-- 	end
		-- end

		local need_flush_and_hold = false
		LongHunWGCtrl.Instance:OpenLongHunOpera(data,ItemTip.FROM_LONGHUN_BAG,click_call_back,need_flush_and_hold)
	else
		if self.longhun_in_xiangqian_mode then
			self:OutSelectMode()
		end
	end
end

function LongHunView:LongHunEquipSelect(data,index)
	if self.longhun_in_xiangqian_mode and self.select_mode_data then
		local equip_able = false
		for k,v in pairs(self.hight_light_list) do
			if v == index then
				equip_able = true
			end
		end
		
		if not equip_able then
			TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.SelectUnWearable)
			return 
		end

		local is_special = 0
		if index > LongHunWGData.Instance:GetNormalSlotNum() then
			index = index - LongHunWGData.Instance:GetNormalSlotNum() - 1
			is_special = 1
		else
			index = index - 1
			is_special = 0
		end
		LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_PUT_SLOT,index,self.select_mode_data.index,is_special)	
		self:OutSelectMode()
		return
	end

	if self.cur_select_index ~= index then
		self.cur_select_index = index
		self:FLushXiangQianCellHL()
	end

	local is_special = 0
	if index > LongHunWGData.Instance:GetNormalSlotNum() then
		index = index - LongHunWGData.Instance:GetNormalSlotNum() - 1
		is_special = 1
	else
		index = index - 1
		is_special = 0
	end

	if not IsEmptyTable(data) and data.lifesoul.type > 0 then
		local click_index = 0
		local click_call_back = {}
		click_index = click_index + 1
		--if is_special == 0 then --升级
			click_call_back[click_index] = {}
			click_call_back[click_index].btn_text = Language.LongHunView.LongHunOpera2[1]
			click_call_back[click_index].show_red = LongHunWGData.Instance:LongHunCanUpGrade(self.cur_select_index)
			click_call_back[click_index].callback = function ()
				LongHunWGCtrl.Instance:OpenUpGradeView(self.cur_select_index)				
			end
		--end

		click_index = click_index + 1
		click_call_back[click_index] = {} --卸下
		click_call_back[click_index].btn_text = Language.LongHunView.LongHunOpera2[2]
		click_call_back[click_index].show_red = 0
		click_call_back[click_index].callback = function ()
			LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_DOWN_SLOT, index,is_special)
		end
		-- if data.lifesoul.quality > 2 then
		-- 	click_index = click_index + 1
		-- 	click_call_back[click_index] = {} -- 合成
		-- 	click_call_back[click_index].btn_text = Language.LongHunView.LongHunOpera2[3]
		-- 	click_call_back[click_index].show_red = 0
		-- 	click_call_back[click_index].callback = function ()
		-- 		ViewManager.Instance:Open(GuideModuleName.Compose,TabIndex.other_compose_longhu)
		-- 		--self:Close()
		-- 	end
		-- end

		-- click_index = click_index + 1
		-- click_call_back[click_index] = {} --锁定
		-- click_call_back[click_index].show_red = 0
		-- if data.lifesoul.is_lock == 1 then
		-- 	click_call_back[click_index].btn_text = Language.LongHunView.LongHunOpera2[5]
		-- else
		-- 	click_call_back[click_index].btn_text = Language.LongHunView.LongHunOpera2[4]
		-- end
		-- local equip_type = is_special == 1 and LIFESOUL_INDEX_TYPE_SPECIAL_SLOT or LIFESOUL_INDEX_TYPE_SLOT
		-- click_call_back[click_index].callback = function ()
		-- 	LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_LOCK_EVEN,equip_type,index)
		-- end

		local need_flush_and_hold = true
		local equip_data = data.lifesoul
		equip_data.slot_index = index
		LongHunWGCtrl.Instance:OpenLongHunOpera(equip_data,ItemTip.FROM_LONGHUN_EQUIP,click_call_back,need_flush_and_hold)
	else
		--空槽位推荐穿戴
		local tuijian_index = LongHunWGData.Instance:SelectLongHunAndPut(self.cur_select_index)
		if tuijian_index ~= -1 then
			LongHunWGCtrl.Instance:SendLongHunOperaReq(LIFESOUL_OPERATOR_TYPE.LIFESOUL_OPERATOR_TYPE_PUT_SLOT,index,tuijian_index,is_special)
		end
		--TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.PleaseSelectOne)
	end

end

function LongHunView:PlayFenJieAni()
	-- if not self.node_list["fenjie_effect"] then return end
	-- self:CanFenJieCleAni()
	-- GlobalTimerQuest:AddDelayTimer(function ()
	-- 	self.node_list["fenjie_effect"]:SetActive(true)
	-- end,0)
	
	-- self.fenjie_delay = GlobalTimerQuest:AddDelayTimer(function ()
	-- 	self.node_list["fenjie_effect"]:SetActive(false)
	-- 	GlobalTimerQuest:CancelQuest(self.fenjie_delay)
	-- 	self.fenjie_delay = nil
	-- end,2)
end

function LongHunView:CanFenJieCleAni()
	self.node_list["fenjie_effect"]:SetActive(false)
	if self.fenjie_delay then
		GlobalTimerQuest:CancelQuest(self.fenjie_delay)
		self.fenjie_delay = nil
	end
end

--------------------------背包大格子---------------------------
LongHunRuneCell = LongHunRuneCell or BaseClass(BaseRender)

function LongHunRuneCell:__init()
	self.longhun_big_cell = {}
	for i=1, 5 do 
		self.longhun_big_cell[i] = LongHunSmallCell.New(self.node_list["longhun_bag_cell"..i])
		self.longhun_big_cell[i]:SetSelectCallBack(BindTool.Bind(self.LongHunCellSelect,self))
		self.longhun_big_cell[i]:SetIndex(i)
	end
end

function LongHunRuneCell:__delete()
	for i = 1, 5 do
		self.longhun_big_cell[i]:DeleteMe()
	end
	self.longhun_big_cell = nil
end

function LongHunRuneCell:OnFlush()
	if not IsEmptyTable(self.data) then
		for i = 1, 5 do 
			if self.data[i] then
				self.longhun_big_cell[i]:SetData(self.data[i])
				self.longhun_big_cell[i]:ShowHightLight(self.data[i].index == LongHunWGData.Instance:GetLongHunBagSelectIndex())
			else
				self.longhun_big_cell[i]:SetData({})
				self.longhun_big_cell[i]:ShowHightLight(false)
			end	
		end
	end
end

function LongHunRuneCell:LongHunCellSelect(index)
	if self.data then
		self.data.select_callback(self.data[index] or {})
	end
end

------------------------背包小格子------------------------
LongHunSmallCell = LongHunSmallCell or BaseClass(BaseRender)

function LongHunSmallCell:__init()
	self.node_list["click_area"].button:AddClickListener(BindTool.Bind(self.OnClickRuneCell,self))
end

function LongHunSmallCell:__delete()

end

function LongHunSmallCell:OnFlush()
	if not IsEmptyTable(self.data) then
		self.node_list["item_pos"]:SetActive(true)
		local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
 		local bundle, asset = ResPath.GetItem(cfg.icon_id)
 		self.node_list["item_pos"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["item_pos"].image:SetNativeSize()
		end)

 		self.node_list["longhun_level_text"].text.text = "lv."..self.data.level
 		--self.node_list["bag_lock"]:SetActive(self.data.is_lock == 1)
 		self.node_list["red_point"]:SetActive(LongHunWGData.Instance:GetBagIndexHaveRed(self.data.index))
 		--self.node_list["effect_pos"]:SetActive(true)
 	else
 		self.node_list["item_pos"]:SetActive(false)
		self.node_list["longhun_level_text"].text.text = ""
		--self.node_list["bag_lock"]:SetActive(false)
		self.node_list["red_point"]:SetActive(false)
		--self.node_list["effect_pos"]:SetActive(false)
	end
end

function LongHunSmallCell:OnClickRuneCell()
	if self.select_call_back then
		self.select_call_back(self.index)
	end
end

function LongHunSmallCell:ShowHightLight(value)
	self.node_list["hight_light"]:SetActive(value)
end



LongHunXiangQianCell = LongHunXiangQianCell or BaseClass(BaseRender)

function LongHunXiangQianCell:__init()
	self.node_list["click_button"].button:AddClickListener(BindTool.Bind(self.OnClickCell,self))
end

function LongHunXiangQianCell:__delete()

end

function LongHunXiangQianCell:OnFlush()
	local is_open,open_level= LongHunWGData.Instance:GetEquipSlotIsOpen(self.index)
	self.is_open = is_open

	self.node_list["rune_lock"]:SetActive(not is_open)
	self.data = LongHunWGData.Instance:GetEquipPosyDataBySlot(self.index)

	self.node_list["rune_text"].text.text = ""
	self.node_list["rune_level_text"].text.text = ""
	--self.node_list["plus"]:SetActive(false)
	self.node_list["rune_text_bg"]:SetActive(true)
	self.node_list["rune_name_bg"]:SetActive(true)
	--self.node_list["equip_lock"]:SetActive(false)
	self.node_list["cell_pos"]:SetActive(false)
	--self.node_list["effect_pos"]:SetActive(false)
	if self.is_open then
		self.node_list["rune_open_text"].text.text = ""
		if not IsEmptyTable(self.data) and  self.data.lifesoul.type > 0 then
			if not self.old_item_id or self.old_item_id ~= self.data.lifesoul.item_id then

			elseif self.old_item_level < self.data.lifesoul.level then
				self:PlayUpGradeEffect()
			end
			self.old_item_id = self.data.lifesoul.item_id
			self.old_item_level = self.data.lifesoul.level

			self.node_list["cell_pos"]:SetActive(true)
			local base_cfg = LongHunWGData.Instance:GetMingWenBaseCfgByID(self.data.lifesoul.item_id)
			local cfg = ItemWGData.Instance:GetItemConfig(self.data.lifesoul.item_id)
 			local bundle, asset = ResPath.GetItem(cfg.icon_id)
 			self.node_list["cell_pos"].image:LoadSpriteAsync(bundle, asset,function ()
				self.node_list["cell_pos"].image:SetNativeSize()
			end)

			self.node_list["rune_text"].text.text = ToColorStr(base_cfg.name,ITEM_COLOR[base_cfg.quality])
			self.node_list["rune_level_text"].text.text = "lv."..self.data.lifesoul.level
			--self.node_list["equip_lock"]:SetActive(self.data.lifesoul.is_lock == 1)
			--self.node_list["effect_pos"]:SetActive(true)
		else
			--self.node_list["plus"]:SetActive(true)
			self.node_list["rune_text_bg"]:SetActive(false)
			self.node_list["rune_name_bg"]:SetActive(false)
			--self.node_list["equip_lock"]:SetActive(false)
			self.old_item_id = 0
			self.old_item_level = 0
		end
	else
		local is_dianfeng, world_level = RoleWGData.Instance:GetDianFengLevel(open_level)
		if is_dianfeng then
			self.node_list["rune_open_text"].text.text = string.format(Language.Common.LevelFeiXian,world_level..Language.LongHunView.OpenText)
		else
			self.node_list["rune_open_text"].text.text = world_level..Language.LongHunView.OpenText
		end
		self.old_item_id = 0
		self.old_item_level = 0
	end
	self:FLushXianQianRed()
end

function LongHunXiangQianCell:PlayUpGradeEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos2"].transform)
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0)})
end


function LongHunXiangQianCell:FLushXianQianRed()
	local is_red = LongHunWGData.Instance:GetSlotRedByIndex2(self.index)
	self.node_list["rune_red"]:SetActive(is_red == 1)
end

function LongHunXiangQianCell:OnClickCell()
	if not self.is_open then
		local is_open,open_level= LongHunWGData.Instance:GetEquipSlotIsOpen(self.index)
		local is_dianfeng, world_level = RoleWGData.Instance:GetDianFengLevel(open_level)
		local str = ""
		if is_dianfeng then
			str = string.format(Language.Common.LevelFeiXian,world_level..Language.LongHunView.LongHunOpenLevel)
		else
			str = open_level..Language.LongHunView.LongHunOpenLevel
		end
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return
	end
	if self.select_call_back then
		self.select_call_back(self.data,self.index)
	end
end

function LongHunXiangQianCell:FlushHL(index)
	self.node_list["rune_select"]:SetActive(self.index == index)
end




LongHunAttrCell = LongHunAttrCell or BaseClass(BaseRender)

function LongHunAttrCell:__init()

end

function LongHunAttrCell:__delete()

end

function LongHunAttrCell:OnFlush()
	if self.data then
		self.node_list["attr_text"].text.text = self.data.attr_name
		if self.data.is_pre then
			self.node_list["attr_num"].text.text = ToColorStr("+"..(self.data.attr_value/100).."%",COLOR3B.GREEN)
		else
			self.node_list["attr_num"].text.text = ToColorStr("+"..self.data.attr_value,COLOR3B.GREEN)
		end
	else
		self.node_list["attr_text"].text.text = ""
		self.node_list["attr_num"].text.text = ""
	end
end

