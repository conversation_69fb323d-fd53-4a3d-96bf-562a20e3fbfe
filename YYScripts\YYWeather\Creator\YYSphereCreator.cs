﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[ExecuteInEditMode]
[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class YYSphereCreator : MonoBehaviour {


#if UNITY_EDITOR

    /// <summary>Resolution of sphere. The higher the more triangles.</summary>
    [Header("Generation of sphere")]
    [Range(2, 6)]
    [Tooltip("Resolution of sphere. The higher the more triangles.")]
    public int Resolution = 6;

    [UnityEngine.HideInInspector]
    [UnityEngine.SerializeField]
    private int lastResolution = -1;

    /// <summary>UV mode for sphere generation</summary>
    [Tooltip("UV mode for sphere generation")]
    public UVMode UVMode = UVMode.Sphere;

    [UnityEngine.HideInInspector]
    [UnityEngine.SerializeField]
    private UVMode lastUVMode = (UVMode)int.MaxValue;

    private void DestroyMesh()
    {
        if (MeshFilter.sharedMesh != null)
        {
            GameObject.DestroyImmediate(MeshFilter.sharedMesh, false);
            MeshFilter.sharedMesh = null;
        }
    }

#endif

  

    private void Awake()
    {
        MeshFilter = GetComponent<MeshFilter>();
        MeshRenderer = GetComponent<MeshRenderer>();
    }

    private void Update()
    {
#if UNITY_EDITOR

        if (Resolution != lastResolution)
        {
            lastResolution = Resolution;
            DestroyMesh();
        }
        if (UVMode != lastUVMode)
        {
            lastUVMode = UVMode;
            DestroyMesh();
        }
        Mesh mesh = MeshFilter.sharedMesh;
        if (mesh == null)
        {
            MeshFilter.sharedMesh = YYCreatorUlit.Create(gameObject.name, Resolution, UVMode);
        }

#endif
    }

    public MeshFilter MeshFilter { get; private set; }
    public MeshRenderer MeshRenderer { get; private set; }
    public Material Material { get { return (MeshRenderer == null ? null : MeshRenderer.sharedMaterial); } }
}
