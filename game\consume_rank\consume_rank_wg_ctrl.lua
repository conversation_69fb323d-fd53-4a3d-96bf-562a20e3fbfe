require("game/consume_rank/consume_rank_view")
require("game/consume_rank/consume_rank_wg_data")

ConsumeRankWGCtrl = ConsumeRankWGCtrl or BaseClass(BaseWGCtrl)

function ConsumeRankWGCtrl:__init()
	if ConsumeRankWGCtrl.Instance then
        error("[ConsumeRankWGCtrl]:Attempt to create singleton twice!")
	end

	ConsumeRankWGCtrl.Instance = self
	self.data = ConsumeRankWGData.New()
	self.view = ConsumeRankView.New(GuideModuleName.ConsumeRankView)

	self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function ConsumeRankWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
	self.rolecharm_notice_view = nil
	self.rolecharm_notice_reward_view = nil

	self:CancelRequesFlushConsumeRank()

	ConsumeRankWGCtrl.Instance = nil
end

function ConsumeRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAConsumeGoldRankBaseInfo, "OnSCOAConsumeGoldRankBaseInfo")
end

function ConsumeRankWGCtrl:OnSCOAConsumeGoldRankBaseInfo(protocol)
	self.data:SetConsumeRankBaseInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ConsumeRankView, nil, "BaseInfo", { BaseInfo = true })
	RemindManager.Instance:Fire(RemindName.LocalConsumeRank)
	self:SendConsumeRankInfo()
end

-- 请求消费榜数据
function ConsumeRankWGCtrl:SendConsumeRankInfo()
	self:SendConsumeRankReq(OA_CONSUME_GOLD_RANK_OPERATE_TYPE.OA_CONSUME_GOLD_RANK_OPERATE_TYPE_RANK_INFO)
end

-- 发送协议（新）
function ConsumeRankWGCtrl:SendConsumeRankReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol.param_3 = param3 or 0
	protocol:EncodeAndSend()
end

function ConsumeRankWGCtrl:GetCacularPos(node)
	if nil == node then return nil end
	local rolecharm_notice_view = self.rolecharm_notice_view
	if nil == rolecharm_notice_view or not rolecharm_notice_view:IsOpen() then
		return
	end
	local parent_rect= rolecharm_notice_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
	local screen_pos_tbl
	if node.gameObject then
		screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	else
		screen_pos_tbl = node
	end
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	if local_position_tbl then
		x = local_position_tbl.x + 100 + (node.rect and node.rect.sizeDelta.x or 0)
		x = math.max(x, -parent_rect.sizeDelta.x / 2)
		x = math.min(x, parent_rect.sizeDelta.x / 2 - 133)
		y = local_position_tbl.y + 80
		y = math.max(y, -parent_rect.sizeDelta.y / 2 +280)
		y = math.min(y, parent_rect.sizeDelta.y / 2 + 50)
		return Vector2(x, y)
	end
end

function ConsumeRankWGCtrl:CancelRequesFlushConsumeRank()
	if self.mainui_consume_rank_info_timer then
		GlobalTimerQuest:CancelQuest(self.mainui_consume_rank_info_timer)
		self.mainui_consume_rank_info_timer = nil
	end
end

-- 消费榜实时刷新显示排行数据
function ConsumeRankWGCtrl:RuningRequesFlushConsumeRankInfo()
	local activity_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK)
	if not activity_is_open then
		self:CancelRequesFlushConsumeRank()
		return
	end
	local is_show = self.data:GetMainUiIsShowConsumeRank()
	if is_show then
		if not self.mainui_consume_rank_info_timer then
			self.mainui_consume_rank_info_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.SendConsumeRankInfo, self), 60)
		end
	else
		self:CancelRequesFlushConsumeRank()
	end
	self:FlushMainUiConsumeRankInfo()
end

function ConsumeRankWGCtrl:FlushMainUiConsumeRankInfo()
	local is_show = self.data:GetMainUiIsShowConsumeRank()
	local rank_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONSUME_RANK)
	if rank_act_btn then
		local data_info = {}
		if is_show then
			local my_rank = self.data:GetSelfRankNum()
			local rank_str = my_rank > 0 and ToColorStr(string.format(Language.ConsumeRank.MainUIRank, my_rank), "#3c8552") or ToColorStr(Language.ConsumeRank.NoRank, "#a93c3c")
			local rank_label = string.format(Language.ConsumeRank.MyRank, rank_str)
			data_info[1] = { is_show = is_show, rank_label = rank_label }

			if my_rank ~= 1 then
				local show_str = my_rank == 0 and Language.ConsumeRank.MainUIRankTips1 or Language.ConsumeRank.MainUIRankTips2
				local need_value = self.data:GetUpRankCondition()
				local rank_tip = string.format(show_str, need_value)
				data_info[2] = { is_show = is_show, rank_label = rank_tip }
			end
		else
			data_info[1] = {is_show = is_show}
		end
		rank_act_btn:Flush("SetRankInfo", data_info)
	end
end

function ConsumeRankWGCtrl:MainuiOpenCreateCallBack()
	self:SendConsumeRankInfo()
end

-- 天数改变
function ConsumeRankWGCtrl:OnDayChange()
	self.data:OnDayChange()
end