
function EveryDayRechargeView:YJReleaseCallBack()
	if self.yj_reward_list then
		for k, v in pairs(self.yj_reward_list) do
			v:DeleteMe()
		end
		self.yj_reward_list = nil
	end

	if self.yj_task_list then
		self.yj_task_list:DeleteMe()
		self.yj_task_list = nil
	end

	self.is_turning = nil
	-- if self.yj_daily_item then
	-- 	self.yj_daily_item:DeleteMe()
	-- 	self.yj_daily_item = nil
	-- end

	if self.random_show_effect_timer then
		GlobalTimerQuest:CancelQuest(self.random_show_effect_timer)
		self.random_show_effect_timer = nil
	end

	if self.show_hl_quest then
		GlobalTimerQuest:CancelQuest(self.show_hl_quest)
		self.show_hl_quest = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.yj_item_change_callback)
	self.yj_item_change_callback = nil

	RemindManager.Instance:UnBind(self.yj_remind_callback)
	self.yj_remind_callback = nil
end

function EveryDayRechargeView:YJInitTurnTableView()
	if not self.yj_reward_list then
		self.yj_reward_list = {}
		for i = 1, 8 do
			self.yj_reward_list[i] = ItemCell.New(self.node_list["yj_item_pos"..i])
			--self.node_list["yj_item_pos"..i].button:AddClickListener(BindTool.Bind(self.OnClickYJRewardItem,self,i))
		end

	end
	--self.yj_daily_item = ItemCell.New(self.node_list["yj_daily_reward_item"])

	if not self.yj_task_list then
		self.yj_task_list = AsyncListView.New(YinJiTaskCell, self.node_list.yj_task_list)
	end
	--self.node_list.yj_roll_father.transform.localEulerAngles = Vector3(0, 0, 0)

	if nil == self.yj_item_change_callback then
		self.yj_item_change_callback = BindTool.Bind(self.YJItemChangeCallBack,self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.yj_item_change_callback)
	end

	self.node_list["yj_turn_btn1"].button:AddClickListener(BindTool.Bind(self.OnClickYJTurn,self,1))
	self.node_list["yj_turn_btn2"].button:AddClickListener(BindTool.Bind(self.OnClickYJTurn,self,2))
	self.node_list["yj_record_btn"].button:AddClickListener(BindTool.Bind(self.OnClickYJRecord,self))
	--self.node_list["yj_jump_ani"].button:AddClickListener(BindTool.Bind(self.OnClickYJJumpAni,self))
	self.node_list["yj_daily_reward_btn"].button:AddClickListener(BindTool.Bind(self.OnClickYJDailyReward,self))

	self.node_list["yj_cost_item1"].button:AddClickListener(BindTool.Bind(self.OnClickYJCostItem,self))
	self.node_list["yj_cost_item2"].button:AddClickListener(BindTool.Bind(self.OnClickYJCostItem,self))

	self.node_list["yj_goto_yinji_btn"].button:AddClickListener(BindTool.Bind(self.OnClickYJGoToYinJI,self))
	self.node_list["yinji_gailv_btn"].button:AddClickListener(BindTool.Bind(self.OnClickYinJiGaiLv,self))

	self.is_turning = false
	EveryDayYinJiTurnTableWGData.Instance:SetFirstOpen(false)

	-- if not self.random_show_effect_timer then
	-- 	self.random_show_effect_timer = GlobalTimerQuest:AddRunQuest(function ()
	-- 		self:ShowRandomEffect()
	-- 	end,3)
	-- end

	self.yj_remind_callback = BindTool.Bind(self.FlushYJJumpRed, self)
	RemindManager.Instance:Bind(self.yj_remind_callback, RemindName.Equipment_NewYinJi)

end

function EveryDayRechargeView:OnClickYJGoToYinJI()
    local bag_data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表
    local equip_list = {}
    if not bag_data_list then
    	SysMsgWGCtrl.Instance:ErrorRemind(Language.YinJiTurnTable.NeedWearEquip)
        return
	end

	for k, v in pairs(bag_data_list) do
		if v.index < GameEnum.EQUIP_INDEX_HUNJIE then
            local is_active, log_type, limit_cfg = NewYinJiJiChengWGData.Instance:GetEquipIsEnoughLimitCondition(v)
            if is_active then
				ViewManager.Instance:Open(GuideModuleName.EquipmentMarkView)
            	return
            end
        end
	end

	SysMsgWGCtrl.Instance:ErrorRemind(Language.YinJiTurnTable.NeedWearEquip)
end

function EveryDayRechargeView:YJShowIndexCallBack()
	self.old_roll_effect_index = -1
	--self:PlayOpenYJTableAnim()
end

function EveryDayRechargeView:OnClickYinJiGaiLv()
	local info = EveryDayYinJiTurnTableWGData.Instance:GetRandomGaiLvinfo()
	TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

function EveryDayRechargeView:PlayOpenYJTableAnim()
	for i = 1, 16 do
		local time = 0.1
		time = time * (i-1)
		self.node_list["yj_light_effect"..i]:SetActive(false)
		local effect_name = EveryDayYinJiTurnTableWGData.Instance:GetRewardIDLightEffectName(i) --一直亮的特效
		if effect_name ~= "" then 
			local bundle,asset = ResPath.GetUIEffect(effect_name)
			self.node_list["yj_light_effect"..i]:ChangeAsset(bundle, asset)
			GlobalTimerQuest:AddDelayTimer(function ()
				if self.node_list["yj_light_effect"..i] then
    				self.node_list["yj_light_effect"..i]:SetActive(true)
    			end
    		end, time)
    	end
	end
end

function EveryDayRechargeView:ShowRandomEffect()
	local index = math.random(1, 16)
	if not self.is_turning then
		local effect_name = EveryDayYinJiTurnTableWGData.Instance:GetRewardIDLightEffectName(index)
		if effect_name ~= "" then
			local bundle,asset = ResPath.GetUIEffect(effect_name)
			EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["yj_item_pos"..index].transform,0.5)
		end
	end
end

function EveryDayRechargeView:OnClickYJJumpAni()
	local is_jump_ani = EveryDayYinJiTurnTableWGData.Instance:GetIsJumpAnim()
	EveryDayYinJiTurnTableWGData.Instance:SetIsJumpAnim(not is_jump_ani)
	--self:FlushJumpAnim()
end

-- function EveryDayRechargeView:FlushJumpAnim()
-- 	self.is_jump_ani = EveryDayYinJiTurnTableWGData.Instance:GetIsJumpAnim()
-- 	self.node_list["yj_jump_toggle"]:SetActive(self.is_jump_ani)
-- end

function EveryDayRechargeView:OnClickYJRecord()
	ServerActivityWGCtrl.Instance:OpenYinJiTurnTableRecordView()
end

function EveryDayRechargeView:YJPlayRollAnimal(index)
	--local item_cfg,index = ZhouYiYunChengWGData.Instance:GetRollResult()
	local index = index or 10
	if index <= 0 then
		self.is_turning = false
		return
	end
	local time = 3
	local draw_time = 0
	local draw_count = EveryDayYinJiTurnTableWGData.Instance:GetYinJiCurDrawCount()
	if draw_count == 1 then
		time = 1
	elseif draw_count == 10 then
		time = 3
	else
		self.is_turning = false
		return
	end
	draw_time = time
	self.yj_turn_time = 0
	self.old_roll_effect_index = -1
	--self.node_list.yj_roll_father.transform.localEulerAngles = Vector3(0, 0, 0)
	if self.is_jump_ani then
		time = 0
		draw_time = 0
	end
	self.tween = self.node_list.zhen.transform:DORotate(Vector3(0, 0, -360 * time - 45 * (index - 1)), draw_time,
		DG.Tweening.RotateMode.FastBeyond360)
	self.tween:SetEase(DG.Tweening.Ease.OutQuad)
	self.tween:OnComplete(function()
		if draw_count == 1 then
			local again_func = function ()
				local cur_count = EveryDayYinJiTurnTableWGData.Instance:GetYinJiCurDrawCount()
				cur_count = cur_count == 1 and 1 or 2
				TipWGCtrl.Instance:CloseShowGetReward()
				self:OnClickYJTurn(cur_count)
			end

			EveryDayYinJiTurnTableWGData.Instance:ShowGetRewardPanel(again_func)
		end
	end)

	if draw_count == 10 then
		local again_func = function ()
			local cur_count = EveryDayYinJiTurnTableWGData.Instance:GetYinJiCurDrawCount()
			cur_count = cur_count == 1 and 1 or 2
			TipWGCtrl.Instance:CloseShowGetReward()
			self:OnClickYJTurn(cur_count)
		end

		EveryDayYinJiTurnTableWGData.Instance:ShowGetRewardPanel(again_func)
	end
	--self:YJCancleHLQuest()
	
	-- GlobalTimerQuest:AddDelayTimer(function ()
	-- 	self.is_turning = false
	-- 	EveryDayYinJiTurnTableWGData.Instance:ShowGetRewardPanel(again_func)
	-- end,0.5)
	self.is_turning = false
	
	--self:YJShowRollHightLight()

	--self:YJCancleHLQuest()
	-- if self.is_jump_ani then
	-- 	return
	-- end

	-- self.show_hl_quest = GlobalTimerQuest:AddRunQuest(function ()
	-- 	self:YJShowRollHightLight()
	-- end,0.02)

	--self:YJShowRollHightLight()
end

function EveryDayRechargeView:YJShowRollHightLight()
	if not self.node_list or not self.node_list.yj_roll_father then return end
	local father_rotation = self.node_list.yj_roll_father.transform.localEulerAngles.z
	local index = (16 - math.floor((father_rotation + 11.25) / 22.5)) % 16 + 1
	if self.old_roll_effect_index == -1 or index ~= self.old_roll_effect_index then
		self.old_roll_effect_index = index
		if index == 1 then
			self.yj_turn_time = self.yj_turn_time + 1
		end
		if self.reward_pool_list[index] and self.reward_pool_list[index].reward_item then
			local cur_cell_item_id = self.reward_pool_list[index].reward_item.item_id
			local is_select,is_last = EveryDayYinJiTurnTableWGData.Instance:GetNeedShowHLEffect(self.yj_turn_time,index)
			if is_select then
				local time = 0.5
				if is_last then
					time = 1.5
				end
				local name = EveryDayYinJiTurnTableWGData.Instance:GetRewardIDGetEffectName(index)
				local bundle,asset = ResPath.GetUIEffect(name)
				if self.is_turning then
					EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["yj_item_pos"..index].transform,0.5)
				end	
			--print_error("抽中了！",cur_cell_item_id)
			end
		end
	end

	-- for i =1,16 do
	-- 	self.node_list["yj_highlight"..i]:SetActive(i == index)
	-- end
end

function EveryDayRechargeView:YJCancleHLQuest()
	if self.show_hl_quest then
		GlobalTimerQuest:CancelQuest(self.show_hl_quest)
		self.show_hl_quest = nil
	end
	self.old_roll_effect_index = -1
	for i =1,16 do
		self.node_list["yj_highlight"..i]:SetActive(false)
	end
end

function EveryDayRechargeView:OnClickYJTurn(value)
	if self.is_turning then 
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YinJiTurnTable.IsTurning)
		return 
	end
	local cost_item = EveryDayYinJiTurnTableWGData.Instance:GetCostItem()
	if cost_item > 0 then
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_item)
		local cost_num1 = EveryDayYinJiTurnTableWGData.Instance:GetOneCostNum()
		local cost_num2 = EveryDayYinJiTurnTableWGData.Instance:GetTenCostNum()
		if value == 1 then
			local ok_func = function ()
				--EveryDayYinJiTurnTableWGData.Instance:SaveTurnTimeAndIsJump(1,self.is_jump_ani)
				ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_DRAW,1)
			end
			if have_num >= cost_num1 then
				--EveryDayYinJiTurnTableWGData.Instance:SaveTurnTimeAndIsJump(1,self.is_jump_ani)
				ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_DRAW,0)
			else
				self:ConfirmCostXianYuTurn(cost_num1 - have_num , ok_func)
			end
		elseif value == 2 then
			local ok_func = function ()
				--EveryDayYinJiTurnTableWGData.Instance:SaveTurnTimeAndIsJump(10,self.is_jump_ani)
				ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_DRAW_MULTI,1)
			end
			if have_num >= cost_num2 then
				--EveryDayYinJiTurnTableWGData.Instance:SaveTurnTimeAndIsJump(10,self.is_jump_ani)
				ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_DRAW_MULTI,0)
			else
				self:ConfirmCostXianYuTurn(cost_num2 - have_num , ok_func)
			end
		else
			return
		end
	end
end

function EveryDayRechargeView:ConfirmCostXianYuTurn(num,ok_func)
	local one_need = EveryDayYinJiTurnTableWGData.Instance:GetOneCostXianYu()
	local all_cost = num * one_need
	local str = string.format(Language.YinJiTurnTable.ConstConfirmStr,all_cost)
	TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, "yj_turn_confirm", nil)
end

function EveryDayRechargeView:OnClickYJDailyReward()
	local left_count = EveryDayYinJiTurnTableWGData.Instance:GetLeftCanGetCount()
	if left_count > 0 then
		ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_GET_LEICHOU_REWARD)
	else
		local need_count = EveryDayYinJiTurnTableWGData.Instance:GetNeedTurnTimesToGet()
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YinJiTurnTable.HaveNoGetCount,need_count))
		local reward_item = EveryDayYinJiTurnTableWGData.Instance:GetCountRewardItem()
		TipWGCtrl.Instance:OpenItem(reward_item)
	end
end

function EveryDayRechargeView:FlushYJTurnTableView()
	self:FlushPoolList()
	self:FlushRechargeTaskList()
	self:FlushCostPanel()
	--self:FlushJumpAnim()
	self:FlushYJTimes()
	self:FlushYJJumpRed()
	
end

function EveryDayRechargeView:FlushYJJumpRed()
	local show_jump_red = NewYinJiJiChengWGData.Instance:GetEquipYinJiRemind()
	self.node_list["yj_jump_red"]:SetActive(show_jump_red == 1)
end

function EveryDayRechargeView:FlushYJTimes()
	--local count_draw = EveryDayYinJiTurnTableWGData.Instance:GetYinJiDrawCount()
	--local get_count_leichou = EveryDayYinJiTurnTableWGData.Instance:GetYinJiGetDrawCount()
	-- self.node_list["yj_all_draw_count"].text.text = Language.YinJiTurnTable.TotalCountText..count_draw
	-- self.node_list["yj_get_reward_count"].text.text = Language.YinJiTurnTable.CurCountText..get_count_leichou
	--local temp_data = EveryDayYinJiTurnTableWGData.Instance:GetCountRewardItem()
	--self.yj_daily_item:SetData(temp_data)

	local left_count = EveryDayYinJiTurnTableWGData.Instance:GetLeftCanGetCount()
	local need_count = EveryDayYinJiTurnTableWGData.Instance:GetNeedTurnTimesToGet()
	local total_count = EveryDayYinJiTurnTableWGData.Instance:GetTotalTurnTimesToGet()
	local cur_count = total_count - need_count --当前已抽的次数
	self.node_list["yj_box_red"]:SetActive(left_count > 0)
	self.node_list["yj_can_get_count"]:SetActive(left_count <= 0)
	if left_count > 0 then
		self.node_list["yj_can_get_count"].text.text = ""
		self.node_list["yj_get_desc"].text.text = Language.YinJiTurnTable.GetCount1
	else
		self.node_list["yj_get_desc"].text.text = Language.YinJiTurnTable.GetCount2
		self.node_list["yj_can_get_count"].text.text = string.format(Language.YinJiTurnTable.RewardCount, cur_count, total_count)
	end

	--星星阶段次数
	local star_value = EveryDayYinJiTurnTableWGData.Instance:GetStarValue()
	for i = 1, 10 do
		self.node_list["nl_count_" .. i].text.text = star_value[i]
		self.node_list["hl_count_" .. i].text.text = star_value[i]
		self.node_list["hl_bg_" .. i]:SetActive(cur_count >= star_value[i])
	end
end

function EveryDayRechargeView:YJItemChangeCallBack()
	self:FlushCostPanel()
	RemindManager.Instance:Fire(RemindName.DailyRecharge_YinJI)
end

function EveryDayRechargeView:OnClickYJCostItem()
	local cost_item = EveryDayYinJiTurnTableWGData.Instance:GetCostItem()
	if cost_item > 0 then
		TipWGCtrl.Instance:OpenItem({item_id = cost_item})
	end
end

function EveryDayRechargeView:FlushCostPanel()
	local cost_item = EveryDayYinJiTurnTableWGData.Instance:GetCostItem()
	if cost_item > 0 then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item)
		local bundle,asset = ResPath.GetItem(item_cfg.icon_id)
		self.node_list["yj_cost_item1"].image:LoadSprite(bundle,asset)
		self.node_list["yj_cost_item2"].image:LoadSprite(bundle,asset)
		local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_item)
		local cost_num1 = EveryDayYinJiTurnTableWGData.Instance:GetOneCostNum()
		local cost_num2 = EveryDayYinJiTurnTableWGData.Instance:GetTenCostNum()
		local color1 = cost_num1 <= have_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		local color2 = cost_num2 <= have_num and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		self.node_list["yj_one_red"]:SetActive(cost_num1 <= have_num)
		self.node_list["yj_ten_red"]:SetActive(cost_num2 <= have_num)
		self.node_list["yj_cost_text1"].text.text = ToColorStr(have_num.."/"..cost_num1,color1)
		self.node_list["yj_cost_text2"].text.text = ToColorStr(have_num.."/"..cost_num2,color2)
	end
end

function EveryDayRechargeView:FlushRechargeTaskList()
	local task_data_list = EveryDayYinJiTurnTableWGData.Instance:GetRechargeTaskListCfg()
	self.yj_task_list:SetDataList(task_data_list)
end

function EveryDayRechargeView:OnClickYJRewardItem(index)
	if self.reward_pool_list and self.reward_pool_list[index] then
		TipWGCtrl.Instance:OpenItem(self.reward_pool_list[index].reward_item)
	end
end

function EveryDayRechargeView:FlushPoolList()
	self.reward_pool_list = EveryDayYinJiTurnTableWGData.Instance:GetYinJiTurnTableRewardPool()
	for i = 1, 8 do
		self.yj_reward_list[i]:SetData(self.reward_pool_list[i].reward_item)
		self.yj_reward_list[i]:SetRightBottomTextVisible(false)
	end
end

function EveryDayRechargeView:DealWithDrawResultBack()
	self:FlushYJTimes()
	local roll_index = EveryDayYinJiTurnTableWGData.Instance:GetLastRewardIndex()
	if roll_index == -1 then
		local again_func = function ()
			local cur_count = EveryDayYinJiTurnTableWGData.Instance:GetYinJiCurDrawCount()
			cur_count = cur_count == 1 and 1 or 2
			TipWGCtrl.Instance:CloseShowGetReward()
			self:OnClickYJTurn(cur_count)
		end
		EveryDayYinJiTurnTableWGData.Instance:ShowGetRewardPanel(again_func)
		return
	end

	self:YJPlayRollAnimal(roll_index)
end

YinJiTaskCell = YinJiTaskCell or BaseClass(BaseRender)

function YinJiTaskCell:__init()
	self.node_list["gobutton"].button:AddClickListener(BindTool.Bind(self.OnClickTaskCell,self))
	self.item_cell = ItemCell.New(self.node_list["icon"])
end

function YinJiTaskCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function YinJiTaskCell:OnFlush()
	if self.data and not IsEmptyTable(self.data) then

		if self.data.id then
			self.node_list["task_desc2"].text.text = Language.YinJiTurnTable.TaskCellText[1]
			self.node_list["task_desc3"].text.text = Language.YinJiTurnTable.TaskCellText[2]

			self.item_cell:SetData(self.data.reward_item)
			local recharge_num = EveryDayYinJiTurnTableWGData.Instance:GetYinJiRechargeCount()
			local color = recharge_num >= self.data.price and COLOR3B.L_GREEN or COLOR3B.L_RED
			recharge_num = recharge_num >= self.data.price and self.data.price or recharge_num
			self.node_list["task_desc"].text.text = ToColorStr(recharge_num,color).."/"..self.data.price
			if recharge_num >= self.data.price then
				self.node_list["red_point"]:SetActive(true)
				self.node_list["btn_text"].text.text = Language.YinJiTurnTable.TaskCellBtn[1]
			else
				self.node_list["btn_text"].text.text = Language.YinJiTurnTable.TaskCellBtn[2]
				self.node_list["red_point"]:SetActive(false)
			end
	
			if EveryDayYinJiTurnTableWGData.Instance:GetTaskIsGet(self.data.id) then
				self.node_list["have_get"]:SetActive(true)
				self.node_list["gobutton"]:SetActive(false)
			else
				self.node_list["have_get"]:SetActive(false)
				self.node_list["gobutton"]:SetActive(true)
			end
		else
			self.item_cell:SetData(self.data[0])
			self.node_list["task_desc2"].text.text = Language.YinJiTurnTable.TaskCellText[3]
			self.node_list["task_desc3"].text.text = Language.YinJiTurnTable.TaskCellText[4]
			self.node_list["btn_text"].text.text = Language.YinJiTurnTable.TaskCellBtn[1]
			self.node_list["task_desc"].text.text = ToColorStr("1/1", COLOR3B.L_GREEN)
			self.node_list["red_point"]:SetActive(EveryDayYinJiTurnTableWGData.Instance:GetYinJiDailyRewardFlag() ~= 1)
			if EveryDayYinJiTurnTableWGData.Instance:GetYinJiDailyRewardFlag() == 1 then
				self.node_list["have_get"]:SetActive(true)
				self.node_list["gobutton"]:SetActive(false)
			else
				self.node_list["have_get"]:SetActive(false)
				self.node_list["gobutton"]:SetActive(true)
			end
		end
	end
end

function YinJiTaskCell:OnClickTaskCell()
	if self.data and self.data.id then
		local recharge_num = EveryDayYinJiTurnTableWGData.Instance:GetYinJiRechargeCount()
		if recharge_num >= self.data.price then
			ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_GET_DAILY_REWARD,self.data.id) 
		else
			ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
		end
	else
		ServerActivityWGCtrl.Instance:SendYinJiTurnTableOpera(OP_YIN_JI_ZHUAN_PAN_TYPE.OP_YIN_JI_ZHUAN_PAN_TYPE_GET_LOGIN_REWARD) 
	end
end

