CheckClassCount = {}

local class_type_obj_list = {}
local total_count = 0

function CheckClassCount:NewClassObj(obj)
	total_count = total_count + 1

	local class_type = obj._class_type
	local tbl = class_type_obj_list[class_type]
	if nil == tbl then
		local class_name = GetClassName(class_type)
		tbl = {class_name = class_name, total_count = 0, new_count = 1, obj_list = {}}
		class_type_obj_list[class_type] = tbl
	else
		tbl.new_count = tbl.new_count + 1
	end

	tbl.obj_list[obj] = true
end

function CheckClassCount:DeleteClassObj(obj)
	total_count = total_count - 1

	local class_type = obj._class_type
	local tbl = class_type_obj_list[class_type]
	tbl.new_count = tbl.new_count - 1
	tbl.obj_list[obj] = nil
end

local last_time_stamp = 0
function CheckClassCount:WriteAllStack()
	local delta_time = GlobalUnityTime - last_time_stamp
	last_time_stamp = GlobalUnityTime

	local tbl = {}
	for k,v in pairs(class_type_obj_list) do
		if v.new_count > 1 or v.total_count > 10 then
			table.insert(tbl, v)
		end
	end

	table.sort(tbl, function (a, b)
		return a.new_count > b.new_count
	end)

	local tbl2 = {}
	table.insert(tbl2, string.format("TotalClassObj:%s \n", total_count))
	table.insert(tbl2, string.format("DeltaTime:%0.1fS \n\n", delta_time))

	for k,v in ipairs(tbl) do
		v.total_count = v.total_count + v.new_count
		table.insert(tbl2, string.format("[%s] TotalCount:%s NewCount:%s\n", v.class_name, v.total_count, v.new_count))
		v.new_count = 0

		local map = {}
		for obj,_ in pairs(v.obj_list) do
			local traceback = obj._init_traceback
			map[traceback] = map[traceback] or 0
			map[traceback] = map[traceback] + 1
		end

		local sort_tbl = {}
		for traceback, count in pairs(map) do
			table.insert(sort_tbl, {traceback = traceback, count = count})
		end

		table.sort(sort_tbl, function (a, b)
			return a.count > b.count
		end)

		for k2, info in ipairs(sort_tbl) do
			local percent = info.count / v.total_count
			if percent < 0.01 and k2 > 3 then
				break
			end

			table.insert(tbl2, string.format("\tCount:%s Percent:%0.1f%% %s \n\n", info.count, percent * 100, info.traceback))
		end
	end

	local file_path = "ClassObjCount.txt"
	local file = io.open(file_path, "w")
	local content = table.concat(tbl2)
	file:write(content)
	file:close()
end

function CheckClassCount:Update(now_time, elapse_time)
end

function CheckClassCount:GetAllObjByClassType(class_type)
	local tbl = class_type_obj_list[class_type]
	if nil ~= tbl then
		return tbl.obj_list
	end
end

function CheckClassCount:GetTraceBackByObj(obj)
	if obj then
		return obj._init_traceback
	end
end

return CheckClassCount