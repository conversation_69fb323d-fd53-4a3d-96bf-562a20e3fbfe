CameraBubbleView = CameraBubbleView or BaseClass(SafeBaseView)
function CameraBubbleView:__init()
	self:LoadConfig()
end

-- 加载配置
function CameraBubbleView:LoadConfig()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/main_ui_prefab", "camera_bubble")
end

function CameraBubbleView:ReleaseCallBack()

end

function CameraBubbleView:ShowIndexCallBack()
	self.close_camera_bubble_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self:Close()
		end, 3)
end

function CameraBubbleView:CloseCallBack()
	if self.close_camera_bubble_timer then
		GlobalTimerQuest:CancelQuest(self.close_camera_bubble_timer)
		self.close_camera_bubble_timer = nil
	end
end

function CameraBubbleView:OnFlush()

end