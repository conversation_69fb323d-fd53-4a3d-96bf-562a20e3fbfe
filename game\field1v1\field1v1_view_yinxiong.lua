Field1v1YinXiongView = Field1v1YinXiongView or BaseClass(SafeBaseView)

function Field1v1YinXiongView:__init()
	self.view_name = GuideModuleName.ActJjc_yingxiong
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_field1v1_yx")
	self.view_yx_top_word = nil
end

function Field1v1YinXiongView:__delete()
	
end

function Field1v1YinXiongView:ReleaseCallBack()
	self.ctrl = nil
	if self.item_list then 
		for i,v in pairs(self.item_list) do
			v:DeleteMe()
		end
	end
	self.item_list = {}
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view  = nil
	end
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
	self.medal = nil
end

function Field1v1YinXiongView:LoadCallBack()
	self.ctrl = Field1v1WGCtrl.Instance
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(947,668)
	self.view = self.node_list.layout_field1v1_yx
	local param = self.node_list.ph_yx_list
	if param then
		self.list_view = AsyncListView.New(Field1v1YinXiongItem, self.node_list["ph_yx_list"])
		self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnClickItemHandler, self))
	end
	self:CreateRoleModel()

	self.item_list = {}
	for i = 1, 2 do
		self.item_list[i] = ItemCell.New(self.node_list["ph_show_"..i])
		
	end

	XUI.AddClickEventListener(self.node_list.btn_yx_help, BindTool.Bind1(self.ctrl.OpenYingXionTipPopPanel, self.ctrl))
	XUI.AddClickEventListener(self.node_list.btn_yx_lingqu, BindTool.Bind1(self.ctrl.ReqGetShengWang, self.ctrl))
	XUI.AddClickEventListener(self.node_list.layout_reward, BindTool.Bind1(self.ctrl.OpenRewardInfo, self.ctrl))
end

function Field1v1YinXiongView:OnClickItemHandler(item)
	local data = item.data
	if nil == data then return end
	if data.is_robot == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors)
	end

	if data.user_id == GameVoManager.Instance:GetMainRoleVo().role_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors1)
	else
		BrowseWGCtrl.Instance:OpenWithUid(data.user_id)
	end
end

function Field1v1YinXiongView:ShowIndexCallBack()
	self.list_view:SetSelectItemToTop(1)
end


--创建角色模型列表
function Field1v1YinXiongView:CreateRoleModel()
	self:Flush()
end

function Field1v1YinXiongView:OnFlush()
	local rank_info = Field1v1WGData.Instance:GetRankinfo()

	if rank_info then	
		self.list_view:SetDataList(rank_info)	
	end

	local info = Field1v1WGData.Instance:GetUserinfo()
	if nil == info then
		return
	end
	
	if Field1v1WGData.Instance:Get1v1DayRewardRemind() == 1 then
		self.node_list.img_yx_btntips:SetActive(true)
		XUI.SetButtonEnabled(self.node_list.btn_yx_lingqu,true)	
		self.node_list.have_get_reward:SetActive(false)
	else
		self.node_list.img_yx_btntips:SetActive(false)
		XUI.SetButtonEnabled(self.node_list.btn_yx_lingqu,false)
		self.node_list.have_get_reward:SetActive(false)
	end
	local reward_info = Field1v1WGData.Instance:GetUserinfo()
	--local reward_cfg = Field1v1WGData.Instance:GetNextJieShuanShengWangByRank(info.rank_pos)
	self.item_list[1]:SetData({item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, num = 0, is_bind = 1})
	local txt 
	if reward_info.shengwang > 0 then
		if reward_info.shengwang < 10000 then
			txt = reward_info.shengwang
		else
			txt = string.format("%.1f", tonumber(reward_info.shengwang) / 10000) .. Language.Common.Wan
		end
		self.item_list[1]:SetRightBottomTextVisible(true)
		self.item_list[1]:SetRightBottomText(txt)
		self.item_list[2]:SetData({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = reward_info.reward_coin, is_bind = 1})
	else
		local base_cfg = Field1v1WGData.Instance:GetRankLastReward()
		if base_cfg.reward_shengwang < 10000 then
			txt = base_cfg.reward_shengwang
		else
			txt = string.format("%.1f", tonumber(base_cfg.reward_shengwang) / 10000) .. Language.Common.Wan
		end
		self.item_list[1]:SetRightBottomTextVisible(true)
		self.item_list[1]:SetRightBottomText(txt)
		self.item_list[2]:SetData({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = base_cfg.reward_coin, is_bind = 1})
	end
	

	if info.rank <= 3 then

		local bundle,asset = ResPath.GetRankImage("rank_num_"..info.rank)
		self.node_list.myself_rank_image.image:LoadSprite(bundle,asset)
		self.node_list.myself_rank_image:SetActive(true)
		self.node_list.label_my_rank.text.text = info.rank
	else
		self.node_list.myself_rank_image:SetActive(false)
		self.node_list.label_my_rank.text.text = info.rank
		
	end

	local role_vo = RoleWGData.Instance.role_vo
	if role_vo then
		self.node_list.label_my_name.text.text = role_vo.role_name
		self.node_list.label_my_zl.text.text = role_vo.capability
		--self.node_list.img_vip.text.text = "V"..role_vo.vip_level
		self.node_list.img_vip:SetActive(false)
		
		--self.node_list.img_vip:SetActive(role_vo.vip_level > 0)
		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(role_vo.level)
		--self.node_list.Image_fire_role:SetActive(is_vis)
		self.node_list.role_level_high:SetActive(is_vis)
		self.node_list.role_level_low:SetActive(not is_vis)

		if is_vis then
			self.node_list["role_level"].text.text = " "..role_level
		else
			self.node_list["role_level_low"].text.text = "Lv."..role_level
		end
		local zhuan_num = math.floor(role_vo.prof / 10)
		local rolr_prof = role_vo.prof % 10
		local pro_str = TransFerWGData.Instance:GetRoleProfNameCfg(zhuan_num, rolr_prof)
		self.node_list["role_profess"].text.text = pro_str--Language.Common.ProfName[role_vo.prof]
	end
end

function Field1v1YinXiongView:UpdataRoleList()
	
end

-----------------------------------------------------
-- Field1v1YinXiongItem
-----------------------------------------------------
Field1v1YinXiongItem = Field1v1YinXiongItem or BaseClass(BaseRender)
function Field1v1YinXiongItem:__init()
	
	self.medal = nil
	 XUI.AddClickEventListener(self.node_list.img_look, BindTool.Bind1(self.OnClickChakan, self))
	 XUI.AddClickEventListener(self.node_list.btn_tiaozhan, BindTool.Bind1(self.OnClickTiaoZhan,self))
	
end

function Field1v1YinXiongItem:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end


function Field1v1YinXiongItem:CreateChild()
	
	
end

function Field1v1YinXiongItem:OnClickChakan()
	if self.data.is_robot == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors)
	end

	if self.data.user_id == GameVoManager.Instance:GetMainRoleVo().role_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors1)
	else
		BrowseWGCtrl.Instance:OpenWithUid(self.data.user_id)
	end
end

function Field1v1YinXiongItem:OnClickTiaoZhan()
	if nil == self.data then
		return
	end

	local info = Field1v1WGData.Instance:GetUserinfo()
	if nil == info then
		return
	end

	if info.rank > 100 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors3)
		return
	end
	local info = Field1v1WGData.Instance:GetUserinfo()
	local temp_cd_time = info.challenge_cd_timer -TimeWGCtrl.Instance:GetServerTime()
	if temp_cd_time > 1800 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.ReduceCDTips)

		
	return
	end

	if self.data.user_id == GameVoManager.Instance:GetMainRoleVo().role_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.OtherErrors2)
		return
	end
	local uid = self.data.user_id
	if uid ~= "" then
		local data = {}

		data.opponent_uid = tonumber(uid)

		if Field1v1WGData.Instance:GetResidueTiaoZhanNum() > 0 then
			data.is_auto_buy = 0
			Field1v1WGCtrl.Instance:ResetFieldFightReq(data)
		else
			data.is_auto_buy = 1
			if nil == self.alert then
				self.alert = Alert.New()
				self.alert:SetShowCheckBox(true)
			end
			self.alert:SetLableString(string.format(Language.Field1v1.AddNumTip, Field1v1WGData.Instance:GetChallengeFieldBuyConsume()))
			self.alert:SetOkFunc(BindTool.Bind1(Field1v1WGCtrl.Instance.FieldBuyJoinTimes, Field1v1WGCtrl.Instance))
			self.alert:Open()
		end		
	end
end

function Field1v1YinXiongItem:OnFlush()
	if nil == self.data then
		return
	end
	local data = self.data
	self.node_list.rich_name.text.text = data.target_name

	if data.rank <= 3 then
		local bundle,asset = ResPath.GetRankImage("rank_num_"..data.rank)
		self.node_list.rank_image.image:LoadSprite(bundle,asset)
		self.node_list.rank_image:SetActive(true)
		--self.node_list.label_rank.text.text = data.rank
	else
		self.node_list.rank_image:SetActive(false)
	end
	if data.rank > 3 then
		self.node_list.lbl_rank.text.text = data.rank
	else
		self.node_list.lbl_rank.text.text = ""
	end
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	--self.node_list.Image_fire:SetActive(is_vis)
	self.node_list.role_level_high:SetActive(is_vis)
	self.node_list["lbl_level"]:SetActive(not is_vis)

	if is_vis then
		self.node_list["role_level"].text.text = role_level
	else
		self.node_list["lbl_level"].text.text = "Lv."..role_level
	end
	local zhuan_num = math.floor(self.data.prof / 10)
	local rolr_prof = self.data.prof % 10
	local pro_str = TransFerWGData.Instance:GetRoleProfNameCfg(zhuan_num, rolr_prof)

	self.node_list["lbl_profess"].text.text = pro_str--Language.Common.ProfName[self.data.prof]

	self.node_list.label_zhanli_num.text.text = data.capability
	-- self.node_list.img_vip.text.text = "V"..self.data.vip_level
	
	-- self.node_list.img_vip:SetActive(self.data.vip_level > 0)
	self.node_list.img_vip:SetActive(false)
end

function Field1v1YinXiongItem:CreateSelectEffect()

end

function Field1v1YinXiongItem:OnSelectChange(is_select)
	self.node_list.select_img:SetActive(is_select)
end