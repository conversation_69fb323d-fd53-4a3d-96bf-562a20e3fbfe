BiZuoGuildTaskFinishTip = BiZuoGuildTaskFinishTip or BaseClass(SafeBaseView)

function BiZuoGuildTaskFinishTip:__init()
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_guild_weekly_task_finish")
	self.view_layer = UiLayer.PopTop
	--self:SetMaskBg(false)
end

function BiZuoGuildTaskFinishTip:__delete()

end

function BiZuoGuildTaskFinishTip:OpenCallBack()

end

function BiZuoGuildTaskFinishTip:LoadCallBack(index, loaded_times)

end

function BiZuoGuildTaskFinishTip:ReleaseCallBack()
	if self.delay_close then
		GlobalTimerQuest:CancelQuest(self.delay_close)
		self.delay_close = nil
	end
end

function BiZuoGuildTaskFinishTip:CloseCallBack()

end

function BiZuoGuildTaskFinishTip:OnFlush()
	if self.delay_close then
		return
	end

	self.delay_close = GlobalTimerQuest:AddDelayTimer(function()
		GlobalTimerQuest:CancelQuest(self.delay_close)
		self.delay_close = nil
		self:Close()
	end, 3)
end