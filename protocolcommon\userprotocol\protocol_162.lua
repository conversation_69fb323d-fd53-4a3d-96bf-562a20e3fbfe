--------------------------驭兽天下协议开始------------------
-- 单个驭兽信息
SCSingleBeastBagInfo = SCSingleBeastBagInfo or BaseClass(BaseProtocolStruct)
function SCSingleBeastBagInfo:__init()
    self.msg_type = 16200
end

function SCSingleBeastBagInfo:Decode()
	self.beast_bag_index = MsgAdapter.ReadInt()
	self.beast_bag_info = ProtocolStruct:ReadBeastBagInfo(self.beast_bag_index)
end

-- 所有出战位信息
SCAllStandBySlotInfo = SCAllStandBySlotInfo or BaseClass(BaseProtocolStruct)
function SCAllStandBySlotInfo:__init()
    self.msg_type = 16201
end

function SCAllStandBySlotInfo:Decode()
	self.stand_by_slot_list = {}
	for i = 1, BEAST_DEFINE.BEAST_FIGHTING_COUNT_MAX do
		self.stand_by_slot_list[i] = ProtocolStruct:ReadBeastStandBySlot()
	end
end

-- 更新单个出战位信息
SCSingleStandBySlotInfo = SCSingleStandBySlotInfo or BaseClass(BaseProtocolStruct)
function SCSingleStandBySlotInfo:__init()
    self.msg_type = 16202
end

function SCSingleStandBySlotInfo:Decode()
	self.stand_by_slot_index = MsgAdapter.ReadInt()
	self.stand_by_slot = ProtocolStruct:ReadBeastStandBySlot()
end

-- 更新单个出战位信息
SCBeastBaseInfo = SCBeastBaseInfo or BaseClass(BaseProtocolStruct)
function SCBeastBaseInfo:__init()
    self.msg_type = 16203
end

function SCBeastBaseInfo:Decode()
	self.total_exp = MsgAdapter.ReadLL()
	self.exp_to_be_acquired = MsgAdapter.ReadLL()
	self.beast_king_level = MsgAdapter.ReadInt()
	self.total_hand_up_time = MsgAdapter.ReadUInt()
end

-- 未孵化背包单个信息
SCSingleBeastEggBagInfo = SCSingleBeastEggBagInfo or BaseClass(BaseProtocolStruct)
function SCSingleBeastEggBagInfo:__init()
    self.msg_type = 16205
end

function SCSingleBeastEggBagInfo:Decode()
	self.egg_bag_index = MsgAdapter.ReadInt()
	self.egg_bag_info = ProtocolStruct:ReadBeastEggBagInfo()
end

-- 驭兽资质变换
CSRoleBeastChangeFlair = CSRoleBeastChangeFlair or BaseClass(BaseProtocolStruct)
function CSRoleBeastChangeFlair:__init()
	self.msg_type = 16206
	self.beast_index = 0
	self.material_list_count = 0
	self.material_list = {}
	self.material_item_num = 0	--资质道具数量
end

function CSRoleBeastChangeFlair:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.beast_index)
	MsgAdapter.WriteInt(self.material_item_num)
	MsgAdapter.WriteInt(self.material_list_count)
	for _, bag_id in ipairs(self.material_list) do
		MsgAdapter.WriteInt(bag_id)
	end
end

-- 所有宝物信息
SCAllTreasureInfo = SCAllTreasureInfo or BaseClass(BaseProtocolStruct)
function SCAllTreasureInfo:__init()
    self.msg_type = 16208
end

function SCAllTreasureInfo:Decode()
	self.obj_list = {}
	for i = 1, BEAST_DEFINE.BEAST_CHANGE_TREASURE_COUNT_MAX do
		self.obj_list[i] = MsgAdapter.ReadInt()
	end
end

-- 所有星阵信息
SCAllStarCircleInfo = SCAllStarCircleInfo or BaseClass(BaseProtocolStruct)
function SCAllStarCircleInfo:__init()
    self.msg_type = 16209
end

function SCAllStarCircleInfo:Decode()
	self.circle_list = {}
	for i = 1, BEAST_DEFINE.BEAST_FIGHTING_COUNT_MAX do
		self.circle_list[i] = ProtocolStruct:ReadBeastStarCircle()
	end

end

-- 更新单个星阵信息
SCSingleStarCircleInfo = SCSingleStarCircleInfo or BaseClass(BaseProtocolStruct)
function SCSingleStarCircleInfo:__init()
    self.msg_type = 16210
end

function SCSingleStarCircleInfo:Decode()
	self.circle_index = MsgAdapter.ReadInt()		-- 轮回数
	self.circle_info = ProtocolStruct:ReadBeastStarCircle()
end

-- 驭兽 - 抽奖信息
SCBeastDrawInfo = SCBeastDrawInfo or BaseClass(BaseProtocolStruct)
function SCBeastDrawInfo:__init()
    self.msg_type = 16211
end

function SCBeastDrawInfo:Decode()
	self.draw_times = MsgAdapter.ReadInt()			-- 大奖抽奖次数
	self.total_draw_times = MsgAdapter.ReadInt()	-- 大奖抽奖次数
	self.deed_level = MsgAdapter.ReadShort()		-- 神锲等级
	self.selected_index = MsgAdapter.ReadShort()	-- 当前大奖下标
end

-- 驭兽 - 组合技能可以释放
SCBeastTriggerSkill = SCBeastTriggerSkill or BaseClass(BaseProtocolStruct)
function SCBeastTriggerSkill:__init()
    self.msg_type = 16212
end

function SCBeastTriggerSkill:Decode()
	self.skill_id = MsgAdapter.ReadUShort()			-- 技能id
	MsgAdapter.ReadShort()
end

--------------------------驭兽天下协议结束------------------

-----------------------天神神装一键镶嵌start----------------

CSTianShenLingHeQuickUse = CSTianShenLingHeQuickUse or BaseClass(BaseProtocolStruct)
function CSTianShenLingHeQuickUse:__init()
	self.msg_type = 16204
end

function CSTianShenLingHeQuickUse:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.tianshen_index)
	for i = 0, 7 do
		MsgAdapter.WriteInt(self.best_bag_list[i] or -1)
	end
end

-----------------------天神神装一键镶嵌end------------------


-----------------------一生所爱活动start------------------

CSOAALifeLongLoveOperate = CSOAALifeLongLoveOperate or BaseClass(BaseProtocolStruct)
function CSOAALifeLongLoveOperate:__init()
	self.msg_type = 16222
end

function CSOAALifeLongLoveOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCAALifeLongLoveInfo = SCAALifeLongLoveInfo or BaseClass(BaseProtocolStruct)
function SCAALifeLongLoveInfo:__init()
    self.msg_type = 16223
end

function SCAALifeLongLoveInfo:Decode()
	self.grade = MsgAdapter.ReadUShort()				-- 档位
	self.big_reward_flag = MsgAdapter.ReadUShort()		-- 大奖领取标记 1为领取
	self.task_progress = {}
	local MAX_TASK_NUM = 16
	for i = 0, MAX_TASK_NUM - 1 do
		self.task_progress[i] = MsgAdapter.ReadChar()
	end

	self.task_reward_count = {}
	for i = 0, MAX_TASK_NUM - 1 do
		self.task_reward_count[i] = MsgAdapter.ReadChar()
	end
end

-----------------------一生所爱活动end------------------

-----------------------一生所爱西游直购start---------------------
SCOAALifeLongLoveRmbInfo = SCOAALifeLongLoveRmbInfo or BaseClass(BaseProtocolStruct)
function SCOAALifeLongLoveRmbInfo:__init()
	self.msg_type = 16221
end

function SCOAALifeLongLoveRmbInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.every_day_reward_flag = MsgAdapter.ReadInt()

	local MAX_BUY_FLAG_NUM = 8
	self.buy_flag = {}
	for i = 0, MAX_BUY_FLAG_NUM - 1 do
		self.buy_flag[i] = MsgAdapter.ReadChar()
	end
end

-----------------------一生所爱西游直购end---------------------

----------------------------------定制广播Start-------------------------------------
local DIY_CHUANGWEN_TYPE = 5   -- 自定义传闻数量（1上线、2击杀、3复活）
local DIY_CHUANGWEN_DESC_COUNT = 90 --自定义文字数量

CSDiyChuanWenClientReq = CSDiyChuanWenClientReq or BaseClass(BaseProtocolStruct)
function CSDiyChuanWenClientReq:__init()
	self.msg_type = 16225
end

function CSDiyChuanWenClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteStrN(self.param_str, 90)
end

SCDiyChuanWenAllInfo = SCDiyChuanWenAllInfo or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenAllInfo:__init()
    self.msg_type = 16213
end

function SCDiyChuanWenAllInfo:Decode()
	self.grade_flag = MsgAdapter.ReadUShort()
	self.desc_active_flag = MsgAdapter.ReadUShort()

	local grade_reward_flag = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		grade_reward_flag[i] = MsgAdapter.ReadChar()
	end
	self.grade_reward_flag = grade_reward_flag

	local desc_state = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		desc_state[i] = MsgAdapter.ReadChar()
	end
	self.desc_state = desc_state

	local choose_skin = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		choose_skin[i] = MsgAdapter.ReadChar()
	end
	self.choose_skin = choose_skin

	local choose_desc = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		choose_desc[i] = MsgAdapter.ReadChar()
	end
	self.choose_desc = choose_desc

	local desc_str_tab = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		desc_str_tab[i] =  MsgAdapter.ReadStrN(DIY_CHUANGWEN_DESC_COUNT)
	end
	self.desc_str_tab = desc_str_tab

	local close_state = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		close_state[i] = MsgAdapter.ReadChar()
	end
	self.close_state = close_state
end

SCDiyChuanWenGradeFlagInfo = SCDiyChuanWenGradeFlagInfo or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenGradeFlagInfo:__init()
    self.msg_type = 16214
end

function SCDiyChuanWenGradeFlagInfo:Decode()
	self.grade_flag = MsgAdapter.ReadUShort()
end

SCDiyChuanWenGradeRewardFlagInfo = SCDiyChuanWenGradeRewardFlagInfo or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenGradeRewardFlagInfo:__init()
    self.msg_type = 16215
end

function SCDiyChuanWenGradeRewardFlagInfo:Decode()
	local grade_reward_flag = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		grade_reward_flag[i] = MsgAdapter.ReadUChar()
	end

	self.grade_reward_flag = grade_reward_flag
end

SCDiyChuanWenChooseDescInfo = SCDiyChuanWenChooseDescInfo or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenChooseDescInfo:__init()
    self.msg_type = 16216
end

function SCDiyChuanWenChooseDescInfo:Decode()
	local choose_skin = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		choose_skin[i] = MsgAdapter.ReadChar()
	end
	self.choose_skin = choose_skin

	local choose_desc = {}
	for i = 1, DIY_CHUANGWEN_TYPE do
		choose_desc[i] = MsgAdapter.ReadChar()
	end
	self.choose_desc = choose_desc
end

SCDiyChuanWenDiyDescInfo = SCDiyChuanWenDiyDescInfo or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenDiyDescInfo:__init()
    self.msg_type = 16217
end

function SCDiyChuanWenDiyDescInfo:Decode()
	self.desc_type = MsgAdapter.ReadInt()
	self.diy_desc_str = MsgAdapter.ReadStrN(DIY_CHUANGWEN_DESC_COUNT)
	self.desc_type_state = MsgAdapter.ReadChar()
end

SCDiyChuanWenDiyDescShow = SCDiyChuanWenDiyDescShow or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenDiyDescShow:__init()
    self.msg_type = 16218
end

function SCDiyChuanWenDiyDescShow:Decode()
	self.desc_type = MsgAdapter.ReadInt()
	self.role_name = MsgAdapter.ReadStrN(32)
	self.kill_role_name = MsgAdapter.ReadStrN(32)
	self.usid = MsgAdapter.ReadUniqueServerID()
	self.death_role_usid = MsgAdapter.ReadUniqueServerID()
	self.choose_skin = MsgAdapter.ReadInt()
	self.choose_desc = MsgAdapter.ReadInt()
	self.diy_desc_str = MsgAdapter.ReadStrN(DIY_CHUANGWEN_DESC_COUNT)
end

SCDiyChuanWenRoleSendRewad = SCDiyChuanWenRoleSendRewad or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenRoleSendRewad:__init()
    self.msg_type = 16219
end

function SCDiyChuanWenRoleSendRewad:Decode()
	self.role_name = MsgAdapter.ReadStrN(32)
	self.reward_idx = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt()
end

SCDiyChuanWenActiveSkin = SCDiyChuanWenActiveSkin or BaseClass(BaseProtocolStruct)
function SCDiyChuanWenActiveSkin:__init()
    self.msg_type = 16220
end

function SCDiyChuanWenActiveSkin:Decode()
	self.desc_active_flag = MsgAdapter.ReadUShort()
end

SCDiyChuanWenRecharge = SCDiyChuanWenRecharge or BaseClass(BaseProtocolStruct) 
function SCDiyChuanWenRecharge:__init()
    self.msg_type = 16224
end

function SCDiyChuanWenRecharge:Decode()
	self.rmb_recharge = MsgAdapter.ReadUInt()
end

SCDiyChuanWenCloseInfo = SCDiyChuanWenCloseInfo or BaseClass(BaseProtocolStruct) 
function SCDiyChuanWenCloseInfo:__init()
    self.msg_type = 16259
end

function SCDiyChuanWenCloseInfo:Decode()
	self.desc_type = MsgAdapter.ReadInt()
	self.close_state = MsgAdapter.ReadInt()
end
------------------------------------定制广播End-------------------------------------


-----------------------臻宝殿Start-----------------------
CSZhenBaoDianClientReq = CSZhenBaoDianClientReq or BaseClass(BaseProtocolStruct)
function CSZhenBaoDianClientReq:__init()
	self.msg_type = 16232
end

function CSZhenBaoDianClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCZhenBaoDianAllInfo = SCZhenBaoDianAllInfo or BaseClass(BaseProtocolStruct)
function SCZhenBaoDianAllInfo:__init()
	self.msg_type = 16233
end

function SCZhenBaoDianAllInfo:Decode()
	self.history_rmb_num = MsgAdapter.ReadUInt()		--活动历史真冲数
	local history_reward_flag = MsgAdapter.ReadUInt()
	self.history_reward_flag = bit:d2b_l2h(history_reward_flag, nil, true)	--奖励标记

	self.today_rmb_num = MsgAdapter.ReadUInt()			--活动每日真冲数
	local today_reward_flag = MsgAdapter.ReadUInt()
	self.today_reward_flag = bit:d2b_l2h(today_reward_flag, nil, true)	--每日奖励标识

	self.enter_game_reward = MsgAdapter.ReadUInt()
	local vip_gift = {}
	for	i = 1, 64 do
		vip_gift[i] = MsgAdapter.ReadUChar()
	end
	self.vip_gift = vip_gift

	local server_gift_count = {}
	for	i = 1, 64 do
		server_gift_count[i] = MsgAdapter.ReadShort()
	end
	self.server_gift_count = server_gift_count
end

SCZhenBaoDianEnterGameRewardInfo = SCZhenBaoDianEnterGameRewardInfo or BaseClass(BaseProtocolStruct)
function SCZhenBaoDianEnterGameRewardInfo:__init()
	self.msg_type = 16277
end

function SCZhenBaoDianEnterGameRewardInfo:Decode()
	self.enter_game_reward = MsgAdapter.ReadUInt()
end

SCZhenBaoDianVipGiftInfo = SCZhenBaoDianVipGiftInfo or BaseClass(BaseProtocolStruct)
function SCZhenBaoDianVipGiftInfo:__init()
	self.msg_type = 16278
end

function SCZhenBaoDianVipGiftInfo:Decode()
	local vip_gift = {}
	for	i = 1, 64 do
		vip_gift[i] = MsgAdapter.ReadUChar()
	end
	self.vip_gift = vip_gift   --vip礼包获取次数

	local server_gift_count = {}
	for	i = 1, 64 do
		server_gift_count[i] = MsgAdapter.ReadShort()
	end
	self.server_gift_count = server_gift_count
end

SCZhenBaoDianRmbNumInfo = SCZhenBaoDianRmbNumInfo or BaseClass(BaseProtocolStruct)
function SCZhenBaoDianRmbNumInfo:__init()
	self.msg_type = 16234
end

function SCZhenBaoDianRmbNumInfo:Decode()
	self.history_rmb_num = MsgAdapter.ReadUInt()
	self.today_rmb_num = MsgAdapter.ReadUInt()
end

SCZhenBaoDianRewardInfo = SCZhenBaoDianRewardInfo or BaseClass(BaseProtocolStruct)
function SCZhenBaoDianRewardInfo:__init()
    self.msg_type = 16235
end

function SCZhenBaoDianRewardInfo:Decode()
	local history_reward_flag = MsgAdapter.ReadUInt()
	self.history_reward_flag = bit:d2b_l2h(history_reward_flag, nil, true)

	local today_reward_flag = MsgAdapter.ReadUInt()
	self.today_reward_flag = bit:d2b_l2h(today_reward_flag, nil, true)
end

-----------------------臻宝殿End-----------------------

-----------------------灵丹宝炉Start-----------------------
SCOAGloryCrystalInfo2 = SCOAGloryCrystalInfo2 or BaseClass(BaseProtocolStruct)
function SCOAGloryCrystalInfo2:__init()
	self.msg_type = 16236
end

function SCOAGloryCrystalInfo2:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.draw_times = MsgAdapter.ReadLL()
	self.lucky = MsgAdapter.ReadLL()
	local times_reward_state = MsgAdapter.ReadLL()
	self.times_reward_flag = bit:d2b_l2h(times_reward_state, nil, true)
	self.convert_times_list = {}
    for i = 0, 39 do
        self.convert_times_list[i] = MsgAdapter.ReadUChar()
    end
end

SCOAGloryCrystalDrawResult2 = SCOAGloryCrystalDrawResult2 or BaseClass(BaseProtocolStruct)
function SCOAGloryCrystalDrawResult2:__init()
	self.msg_type = 16237
end

function SCOAGloryCrystalDrawResult2:Decode()
	self.mode = MsgAdapter.ReadInt()
	local baodi_item = {
        item_id = MsgAdapter.ReadUShort(),
        reversh = MsgAdapter.ReadChar(),
        is_bind = MsgAdapter.ReadChar(),
        num  = MsgAdapter.ReadInt(),
    }
	self.baodi_item = baodi_item

	self.count = MsgAdapter.ReadInt()
	local item_list = {}
	for i = 1, self.count do
		local item = {
			item_id = MsgAdapter.ReadUShort(),
			reversh = MsgAdapter.ReadChar(),
			is_bind = MsgAdapter.ReadChar(),
			num = MsgAdapter.ReadInt(),
		}

		item_list[i] = item
	end

	self.result_item_list = item_list
end
-----------------------灵丹宝炉End-----------------------

-----------------------灵气之种Start-----------------------
SCOAGloryCrystalInfo3 = SCOAGloryCrystalInfo3 or BaseClass(BaseProtocolStruct)
function SCOAGloryCrystalInfo3:__init()
	self.msg_type = 16238
end

function SCOAGloryCrystalInfo3:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.draw_times = MsgAdapter.ReadLL()
	self.lucky = MsgAdapter.ReadLL()
	local times_reward_state = MsgAdapter.ReadLL()
	self.times_reward_flag = bit:d2b_l2h(times_reward_state, nil, true)
	self.convert_times_list = {}
    for i = 0, 39 do
        self.convert_times_list[i] = MsgAdapter.ReadUChar()
    end
end

SCOAGloryCrystalDrawResult3 = SCOAGloryCrystalDrawResult3 or BaseClass(BaseProtocolStruct)
function SCOAGloryCrystalDrawResult3:__init()
	self.msg_type = 16239
end

function SCOAGloryCrystalDrawResult3:Decode()
	self.mode = MsgAdapter.ReadInt()
	local baodi_item = {
        item_id = MsgAdapter.ReadUShort(),
        reversh = MsgAdapter.ReadChar(),
        is_bind = MsgAdapter.ReadChar(),
        num  = MsgAdapter.ReadInt(),
    }
	self.baodi_item = baodi_item

	self.count = MsgAdapter.ReadInt()
	local item_list = {}
	for i = 1, self.count do
		local item = {
			item_id = MsgAdapter.ReadUShort(),
			reversh = MsgAdapter.ReadChar(),
			is_bind = MsgAdapter.ReadChar(),
			num = MsgAdapter.ReadInt(),
		}

		item_list[i] = item
	end

	self.result_item_list = item_list
end
-----------------------灵气之种End-----------------------

----------------------------------机甲Start-------------------------------------

local MAX_MECHAN_HELP_FIGHT_NUM = 3  -- 机甲助战位置数量
local MAX_MECHAN_PART_NUMBER = 1280  -- 机甲部件最大数量
local MAX_PART_TYPE = 8              -- 机甲部件类型
local MAX_MECHAN_NUM = 16            -- 机甲最大数量

CSMechanClientReq = CSMechanClientReq or BaseClass(BaseProtocolStruct)
function CSMechanClientReq:__init()
	self.msg_type = 16240
end

function CSMechanClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)

    for i = 0 ,7 do
        if self.param_list[i] then
            MsgAdapter.WriteInt(self.param_list[i])
        else
            MsgAdapter.WriteInt(-1)
        end
	end
end

local function GetMechanPuttonParam()
	local data = {}
	for i = 0, MAX_PART_TYPE - 1 do
		data[i] = MsgAdapter.ReadShort()
	end

	return data
end

SCMechanAllInfo = SCMechanAllInfo or BaseClass(BaseProtocolStruct) 
function SCMechanAllInfo:__init()
    self.msg_type = 16241
end

function SCMechanAllInfo:Decode()
	local puton_list = {}
	for i = 0, MAX_MECHAN_NUM - 1 do
		puton_list[i] = GetMechanPuttonParam()
	end
	self.puton_list = puton_list

	local part_star = {}
	for i = 0, MAX_MECHAN_PART_NUMBER - 1 do
		part_star[i] = MsgAdapter.ReadChar()
	end
	self.part_star = part_star

	local help_fight_battle = {}
	for i = 0, MAX_MECHAN_HELP_FIGHT_NUM - 1 do
		help_fight_battle[i] = MsgAdapter.ReadInt()
	end
	self.help_fight_battle = help_fight_battle
	
	self.help_fight_open = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)

	self.choose_wing_skill_id = MsgAdapter.ReadInt()
	self.bianshen_seq = MsgAdapter.ReadInt() 
	self.bianshen_end_time = MsgAdapter.ReadInt()
	self.bianshen_start_time = MsgAdapter.ReadInt()
end

SCMechanPutonInfo = SCMechanPutonInfo or BaseClass(BaseProtocolStruct) 
function SCMechanPutonInfo:__init()
    self.msg_type = 16242
end

function SCMechanPutonInfo:Decode()
	self.mechan_seq = MsgAdapter.ReadInt()
	local part_puton = {}
	for i = 0, MAX_PART_TYPE - 1 do
		part_puton[i] = MsgAdapter.ReadShort()
	end
	self.part_puton = part_puton
end

SCMechanPartStarInfo = SCMechanPartStarInfo or BaseClass(BaseProtocolStruct) 
function SCMechanPartStarInfo:__init()
    self.msg_type = 16243
end

function SCMechanPartStarInfo:Decode()
	self.part_seq = MsgAdapter.ReadInt()
	self.star = MsgAdapter.ReadInt()
end

SCMechanMainFightInfo = SCMechanMainFightInfo or BaseClass(BaseProtocolStruct) 
function SCMechanMainFightInfo:__init()
    self.msg_type = 16244
end

function SCMechanMainFightInfo:Decode()
	self.bianshen_seq = MsgAdapter.ReadInt()
end

SCMechanHelpFightOpenInfo = SCMechanHelpFightOpenInfo or BaseClass(BaseProtocolStruct) 
function SCMechanHelpFightOpenInfo:__init()
    self.msg_type = 16245
end

function SCMechanHelpFightOpenInfo:Decode()
	self.help_fight_open = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
end

SCMechanHelpFightBattleInfo = SCMechanHelpFightBattleInfo or BaseClass(BaseProtocolStruct)
function SCMechanHelpFightBattleInfo:__init()
    self.msg_type = 16246
end

function SCMechanHelpFightBattleInfo:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.mechan_seq = MsgAdapter.ReadInt()
end

SCMechanChooseWingSkillInfo = SCMechanChooseWingSkillInfo or BaseClass(BaseProtocolStruct) 
function SCMechanChooseWingSkillInfo:__init()
    self.msg_type = 16247
end

function SCMechanChooseWingSkillInfo:Decode()
	self.choose_wing_skill_id = MsgAdapter.ReadInt()   --已经选择的翅膀技能id  暂时无用
end

SCMechanBianshenInfo = SCMechanBianshenInfo or BaseClass(BaseProtocolStruct) 
function SCMechanBianshenInfo:__init()
    self.msg_type = 16248
end

function SCMechanBianshenInfo:Decode()
	self.bianshen_seq = MsgAdapter.ReadInt()
	self.bianshen_end_time = MsgAdapter.ReadInt()
	self.bianshen_start_time = MsgAdapter.ReadInt()
end

------------------------------------机甲End-------------------------------------------------------------------------定制广播End-------------------------------------

--------------------------藏金商铺--------------------------------
local CANGJINSHANGPU_MAX_SUIT_SHOP_IDX = 64  -- 套装商店的最大下标数量
local CANGJINSHANGPU_MAX_LIMIT_SHOP_IDX = 64 -- 限购商店的最大下标数量
local CANGJINSHANGPU_MAX_CONVERT_IDX = 64    -- 兑换商店的最大下标数量

CSCangJinShangPuClientReq = CSCangJinShangPuClientReq or BaseClass(BaseProtocolStruct)
function CSCangJinShangPuClientReq:__init()
    self.msg_type = 16231
end

function CSCangJinShangPuClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end

SCCangJinShangPuAllInfo = SCCangJinShangPuAllInfo or BaseClass(BaseProtocolStruct)
function SCCangJinShangPuAllInfo:__init()
    self.msg_type = 16230
end

function SCCangJinShangPuAllInfo:Decode()
	self.score = MsgAdapter.ReadUInt()
	self.tequan_flag = bit:d2b_l2h(MsgAdapter.ReadUInt(), nil, true)
	self.tequan_every_day_reward = bit:d2b_l2h(MsgAdapter.ReadUInt(), nil, true)
	self.tequan_choose_attr1_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.tequan_choose_attr2_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.suit_shop = {}
	for i = 0, CANGJINSHANGPU_MAX_SUIT_SHOP_IDX - 1 do
		self.suit_shop[i] = MsgAdapter.ReadUChar()
	end

	self.limit_shop_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.refresh_limit_count = MsgAdapter.ReadUInt()

	self.limit_shop = {}
	for i = 0, CANGJINSHANGPU_MAX_LIMIT_SHOP_IDX - 1 do
		self.limit_shop[i] = MsgAdapter.ReadUChar()
	end

	self.limit_shop_refresh_count = MsgAdapter.ReadInt()

	local convert = {}
	for i = 0, CANGJINSHANGPU_MAX_CONVERT_IDX - 1 do
		convert[i] = MsgAdapter.ReadUInt()
	end
	self.convert = convert
	self.grade = MsgAdapter.ReadUInt()

	-- 真充数
	self.real_recharge_num = MsgAdapter.ReadUInt()
	self.tequan_active_reward_flag = bit:d2b_l2h(MsgAdapter.ReadUInt(), nil, true)
end

SCCangJinShangPuSuitShopInfo = SCCangJinShangPuSuitShopInfo or BaseClass(BaseProtocolStruct)
function SCCangJinShangPuSuitShopInfo:__init()
    self.msg_type = 16226
end

function SCCangJinShangPuSuitShopInfo:Decode()
	self.suit_shop = {}
	for i = 0, CANGJINSHANGPU_MAX_SUIT_SHOP_IDX - 1 do
		self.suit_shop[i] = MsgAdapter.ReadUChar()
	end
end

SCCangJinShangPuLimitShopInfo = SCCangJinShangPuLimitShopInfo or BaseClass(BaseProtocolStruct)
function SCCangJinShangPuLimitShopInfo:__init()
    self.msg_type = 16227
end

function SCCangJinShangPuLimitShopInfo:Decode()
	self.limit_shop_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.refresh_limit_count = MsgAdapter.ReadUInt()
	self.limit_shop = {}
	for i = 0, CANGJINSHANGPU_MAX_LIMIT_SHOP_IDX - 1 do
		self.limit_shop[i] = MsgAdapter.ReadUChar()
	end

	self.limit_shop_refresh_count = MsgAdapter.ReadInt()
end

SCCangJinShangPuTeQuanInfo = SCCangJinShangPuTeQuanInfo or BaseClass(BaseProtocolStruct)
function SCCangJinShangPuTeQuanInfo:__init()
    self.msg_type = 16228
end

function SCCangJinShangPuTeQuanInfo:Decode()
	self.tequan_flag = bit:d2b_l2h(MsgAdapter.ReadUInt(), nil, true)
	self.tequan_every_day_reward = bit:d2b_l2h(MsgAdapter.ReadUInt(), nil, true)
	self.tequan_choose_attr1_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.tequan_choose_attr2_flag = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
	self.tequan_active_reward_flag = bit:d2b_l2h(MsgAdapter.ReadUInt(), nil, true)
end

SCCangJinShangPuScoreInfo = SCCangJinShangPuScoreInfo or BaseClass(BaseProtocolStruct)
function SCCangJinShangPuScoreInfo:__init()
    self.msg_type = 16229
end

function SCCangJinShangPuScoreInfo:Decode()
	self.score = MsgAdapter.ReadUInt()
	self.reason_type = MsgAdapter.ReadInt()
end

SSCANGJINSHANGPUConvertInfo = SSCANGJINSHANGPUConvertInfo or BaseClass(BaseProtocolStruct)
function SSCANGJINSHANGPUConvertInfo:__init()
    self.msg_type = 16257
end

function SSCANGJINSHANGPUConvertInfo:Decode()
	local convert = {}
	for i = 0, CANGJINSHANGPU_MAX_CONVERT_IDX - 1 do
		convert[i] = MsgAdapter.ReadUInt()
	end
	self.convert = convert
end
--------------------------藏金商铺End--------------------------------

-----------------------------Boss血量记录 Start----------------------------
SCMonsterHpRecordNotice = SCMonsterHpRecordNotice or BaseClass(BaseProtocolStruct)

function SCMonsterHpRecordNotice:__init()
	self.msg_type = 16258
end

function SCMonsterHpRecordNotice:Decode()
	local MAX_HP_RECORD_COUNT = 3
	self.obj_id = MsgAdapter.ReadObjId()
	self.monster_id = MsgAdapter.ReadUShort()
	self.monster_hp_record_list = {}

	for i = 1, MAX_HP_RECORD_COUNT do
		self.monster_hp_record_list[i] = MsgAdapter.ReadLL()
	end
end

------------------------------Boss血量记录 End-----------------------------------

-----------------------------万象天引start----------------------------
SCOAWanXiangTianYinInfo = SCOAWanXiangTianYinInfo or BaseClass(BaseProtocolStruct)

function SCOAWanXiangTianYinInfo:__init()
	self.msg_type = 16268
end

function SCOAWanXiangTianYinInfo:Decode()
    self.score = MsgAdapter.ReadLL()				-- 积分
	self.grade = MsgAdapter.ReadUInt()				-- 档次
	self.round_num = MsgAdapter.ReadUInt()			-- 轮数
	self.reward_flag = {}							-- 最大轮数
	for i = 0, 4 do
		local flag = MsgAdapter.ReadUInt()
		self.reward_flag[i] = bit:d2b_l2h(flag, nil, true)
	end
	self.every_day_flag = MsgAdapter.ReadUInt()		-- 每日奖励
end

------------------------------万象天引end-----------------------------------

--------------------------永世炫彩Start-------------------------------------
SCWardrobeXuancaiList = SCWardrobeXuancaiList or BaseClass(BaseProtocolStruct)

function SCWardrobeXuancaiList:__init()
	self.msg_type = 16255
end

function SCWardrobeXuancaiList:Decode()
	self.wardrobe_xuancai_list = {}
	for i = 1, 10 do
		self.wardrobe_xuancai_list[i] = {}
		self.wardrobe_xuancai_list[i].seq = MsgAdapter.ReadInt()
		local info = {}

		for j = 1, 8 do
			info[j] = {}
			info[j].part = MsgAdapter.ReadInt()
			info[j].xuancai_id = MsgAdapter.ReadInt()
		end

		self.wardrobe_xuancai_list[i].wardrobe_xuancai_ids = info
	end
end

SCWardrobeXuancaiUpdate = SCWardrobeXuancaiUpdate or BaseClass(BaseProtocolStruct)

function SCWardrobeXuancaiUpdate:__init()
	self.msg_type = 16256
end

function SCWardrobeXuancaiUpdate:Decode()
	self.seq = MsgAdapter.ReadUShort()
	self.part = MsgAdapter.ReadUShort()
	self.appen_image_id = MsgAdapter.ReadInt()
end
--------------------------永世炫彩end-------------------------------------

--------------------------江山如画Start-------------------------------------
SCRAJiangShanRuHuaInfo = SCRAJiangShanRuHuaInfo or BaseClass(BaseProtocolStruct)
function SCRAJiangShanRuHuaInfo:__init()
    self.msg_type = 16260
end

function SCRAJiangShanRuHuaInfo:Decode()
    self.grade = MsgAdapter.ReadInt()                   --阶段
    self.person_draw_count = MsgAdapter.ReadInt()       --个人抽奖次数
    self.leiji_reward_fetch_flag = MsgAdapter.ReadLL()  --累计奖励领取标记
    self.cur_cycle = MsgAdapter.ReadInt()               --当前周期？
    self.is_skip_comic = MsgAdapter.ReadInt()           --跳过动画？
    self.sp_guarantee_x = MsgAdapter.ReadInt()          --特殊保底次数？
    self.sp_guarantee_n = MsgAdapter.ReadInt()          --特殊保底轮数？
    self.sp_enter_num = MsgAdapter.ReadInt()            --进入保底库次数？
    self.gather_small_count = MsgAdapter.ReadInt()      --小宝箱采集次数
    self.gather_big_count = MsgAdapter.ReadInt()        --大宝箱采集次数
end

SCRAJiangShanRuHuaRecordListInfo = SCRAJiangShanRuHuaRecordListInfo or BaseClass(BaseProtocolStruct)
function SCRAJiangShanRuHuaRecordListInfo:__init()
    self.msg_type = 16261
end

function SCRAJiangShanRuHuaRecordListInfo:Decode()
    self.record_count = MsgAdapter.ReadInt()
    self.record_list = {}
    for i = 1, self.record_count do
        self.record_list[i] = {}
        self.record_list[i].draw_time = MsgAdapter.ReadUInt()
        self.record_list[i].role_name = MsgAdapter.ReadStrN(32)
        self.record_list[i].item_id = MsgAdapter.ReadInt()
        self.record_list[i].num = MsgAdapter.ReadInt()
    end
end

SCRAJiangShanRuHuaDrawRewardInfo = SCRAJiangShanRuHuaDrawRewardInfo or BaseClass(BaseProtocolStruct)
function SCRAJiangShanRuHuaDrawRewardInfo:__init()
    self.msg_type = 16262
end

function SCRAJiangShanRuHuaDrawRewardInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.reward_list = {}
    for i = 1, self.count do
        self.reward_list[i] = {}
        self.reward_list[i].reward_flag = MsgAdapter.ReadShort() --0 普通，1保底
        self.reward_list[i].reward_pool_id = MsgAdapter.ReadShort()
        self.reward_list[i].reward_id = MsgAdapter.ReadInt()
        --self.reward_list[i].is_zhenxi = self.reward_list[i].reward_flag == 1
    end
end

SCRAJiangShanRuHuaBaoDiRewardDrawInfo = SCRAJiangShanRuHuaBaoDiRewardDrawInfo or BaseClass(BaseProtocolStruct)
function SCRAJiangShanRuHuaBaoDiRewardDrawInfo:__init()
    self.msg_type = 16263
end

function SCRAJiangShanRuHuaBaoDiRewardDrawInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.reward_info = {}
    for i = 1, self.count do
        local temp = {}
        temp.reward_id = MsgAdapter.ReadShort()
        temp.get_count = MsgAdapter.ReadShort()
        self.reward_info[i] = temp
    end
end
--------------------------江山如画End--------------------------------

--------------------------墨染轩辕Start-------------------------------------
SCRAMoRanXuanYuanInfo = SCRAMoRanXuanYuanInfo or BaseClass(BaseProtocolStruct)
function SCRAMoRanXuanYuanInfo:__init()
    self.msg_type = 16264
end

function SCRAMoRanXuanYuanInfo:Decode()
    self.grade = MsgAdapter.ReadInt()                   --阶段
    self.person_draw_count = MsgAdapter.ReadInt()       --个人抽奖次数
	self.leiji_reward_fetch_flag = MsgAdapter.ReadLL() --累计奖励领取标记
    self.cur_cycle = MsgAdapter.ReadInt()               --当前周期？
    self.is_skip_comic = MsgAdapter.ReadInt()           --跳过动画？
    self.sp_guarantee_x = MsgAdapter.ReadInt()          --特殊保底次数？
    self.sp_guarantee_n = MsgAdapter.ReadInt()          --特殊保底轮数？
    self.sp_enter_num = MsgAdapter.ReadInt()            --进入保底库次数？
    self.gather_small_count = MsgAdapter.ReadInt()      --小宝箱采集次数
    self.gather_big_count = MsgAdapter.ReadInt()        --大宝箱采集次数
end

SCRAMoRanXuanYuanRecordListInfo = SCRAMoRanXuanYuanRecordListInfo or BaseClass(BaseProtocolStruct)
function SCRAMoRanXuanYuanRecordListInfo:__init()
    self.msg_type = 16265
end

function SCRAMoRanXuanYuanRecordListInfo:Decode()
    self.record_count = MsgAdapter.ReadInt()
    self.record_list = {}
    for i = 1, self.record_count do
        self.record_list[i] = {}
        self.record_list[i].draw_time = MsgAdapter.ReadUInt()
        self.record_list[i].role_name = MsgAdapter.ReadStrN(32)
        self.record_list[i].item_id = MsgAdapter.ReadInt()
        self.record_list[i].num = MsgAdapter.ReadInt()
    end
end

SCRAMoRanXuanYuanDrawRewardInfo = SCRAMoRanXuanYuanDrawRewardInfo or BaseClass(BaseProtocolStruct)
function SCRAMoRanXuanYuanDrawRewardInfo:__init()
    self.msg_type = 16266
end

function SCRAMoRanXuanYuanDrawRewardInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.reward_list = {}
    for i = 1, self.count do
        self.reward_list[i] = {}
        self.reward_list[i].reward_flag = MsgAdapter.ReadShort() --0 普通，1保底
        self.reward_list[i].reward_pool_id = MsgAdapter.ReadShort()
        self.reward_list[i].reward_id = MsgAdapter.ReadInt()
    end
end

SCRAMoRanXuanYuanBaoDiRewardDrawInfo = SCRAMoRanXuanYuanBaoDiRewardDrawInfo or BaseClass(BaseProtocolStruct)
function SCRAMoRanXuanYuanBaoDiRewardDrawInfo:__init()
    self.msg_type = 16267
end

function SCRAMoRanXuanYuanBaoDiRewardDrawInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.reward_info = {}
    for i = 1, self.count do
        local temp = {}
        temp.reward_id = MsgAdapter.ReadShort()
        temp.get_count = MsgAdapter.ReadShort()
        self.reward_info[i] = temp
    end
end
--------------------------墨染轩辕End--------------------------------

-----------------------弑天套装拓展Start-----------------------
CSShiTianStrengthenReq = CSShiTianStrengthenReq or BaseClass(BaseProtocolStruct)
function CSShiTianStrengthenReq:__init()
	self.msg_type = 16269
end

function CSShiTianStrengthenReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.req_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

local MAX_SHITIAN_SUIT_NUM = 10
local MAX_SHITIANSTONE_EQUIP_NUM = 12
local MAX_SHITIANSTONE_SLOT_COUNT = 6

SCShiTianStoneInfo = SCShiTianStoneInfo or BaseClass(BaseProtocolStruct)
function SCShiTianStoneInfo:__init()
	self.msg_type = 16270
end

function SCShiTianStoneInfo:Decode()
	local stone_data = {}
	self.is_compose = MsgAdapter.ReadInt()					-- 是否是合成
	self.suit_seq = MsgAdapter.ReadInt()
	self.part = MsgAdapter.ReadInt()
	self.shitian_stone_total_level = MsgAdapter.ReadInt()	-- 宝石的总等级阶段
	for i = 0, MAX_SHITIANSTONE_SLOT_COUNT - 1 do
		stone_data[i] = MsgAdapter.ReadUShort()				-- 宝石id
		local reserve_short = MsgAdapter.ReadShort()		--对齐协议
	end

	self.stone_data = stone_data
end

SCShiTianEquipInfo = SCShiTianEquipInfo or BaseClass(BaseProtocolStruct)
function SCShiTianEquipInfo:__init()
	self.msg_type = 16271
end

function SCShiTianEquipInfo:Decode()
	self.suit_seq = MsgAdapter.ReadInt()
	self.part = MsgAdapter.ReadInt()
	self.shitian_strength_total_level = MsgAdapter.ReadInt()	-- 强化总等级阶段
	self.strengthen_level = MsgAdapter.ReadInt()		-- 单件装备的强化等级
end

SCShiTianEquipFuMoInfo = SCShiTianEquipFuMoInfo or BaseClass(BaseProtocolStruct)
function SCShiTianEquipFuMoInfo:__init()
	self.msg_type = 16272
end

function SCShiTianEquipFuMoInfo:Decode()
	self.suit_seq = MsgAdapter.ReadInt()
	self.part = MsgAdapter.ReadInt()
	self.shitian_fumo_total_quality = MsgAdapter.ReadInt()	-- 附魔总品质阶段
	self.fumo_quality = MsgAdapter.ReadInt()			-- 单件装备的附魔品质
	self.fumo_level = MsgAdapter.ReadInt()				-- 单件装备的附魔等级
end

SCShiTianAdvanceSuit = SCShiTianAdvanceSuit or BaseClass(BaseProtocolStruct)
function SCShiTianAdvanceSuit:__init()
	self.msg_type = 16273
end

function SCShiTianAdvanceSuit:Decode()
	local all_list = {}
	for i = 0, MAX_SHITIAN_SUIT_NUM - 1 do
		all_list[i] = {}
		all_list[i].suit_seq = MsgAdapter.ReadInt()
		all_list[i].shitian_stone_total_level = MsgAdapter.ReadInt()
		all_list[i].shitian_strength_total_level = MsgAdapter.ReadInt()
		all_list[i].shitian_fumo_total_quality = MsgAdapter.ReadInt()
		all_list[i].shitian_equip_part_list = {}
		for part = 0, MAX_SHITIANSTONE_EQUIP_NUM - 1 do
			local part_data = {}
			part_data.strengthen_level = MsgAdapter.ReadInt()
			part_data.fumo_quality = MsgAdapter.ReadInt()
			part_data.fumo_level = MsgAdapter.ReadInt()

			local stone_data = {}
			for stoen_part = 0, MAX_SHITIANSTONE_SLOT_COUNT - 1 do
				stone_data[stoen_part] = MsgAdapter.ReadUShort()
				local reserve_short = MsgAdapter.ReadShort()		--对齐协议
			end

			part_data.stone_data = stone_data
			all_list[i].shitian_equip_part_list[part] = part_data
		end
	end

	self.all_list = all_list
end

SCShiTianActiveSuit = SCShiTianActiveSuit or BaseClass(BaseProtocolStruct)
function SCShiTianActiveSuit:__init()
	self.msg_type = 16274
end

function SCShiTianActiveSuit:Decode()
	self.suit_seq = MsgAdapter.ReadInt()
	self.shitian_stone_total_level = MsgAdapter.ReadInt()
	self.shitian_strength_total_level = MsgAdapter.ReadInt()
	self.shitian_fumo_total_quality = MsgAdapter.ReadInt()
	local reserve_short = MsgAdapter.ReadShort()		--对齐协议
end

CSShiTianOneKeyInlayReq = CSShiTianOneKeyInlayReq or BaseClass(BaseProtocolStruct)
function CSShiTianOneKeyInlayReq:__init()
	self.msg_type = 16279
end

function CSShiTianOneKeyInlayReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.suit_seq)
	MsgAdapter.WriteInt(self.part)
	MsgAdapter.WriteInt(self.count)
	for k,v in ipairs(self.slot_info) do
		MsgAdapter.WriteInt(v.slot_index)
		MsgAdapter.WriteInt(v.bag_index)
	end
end
-----------------------弑天套装拓展End-----------------------

-----------------------双生神灵Start-----------------------
SCTianshenAvatarInfo = SCTianshenAvatarInfo or BaseClass(BaseProtocolStruct)
function SCTianshenAvatarInfo:__init()
    self.msg_type = 16275
end

function SCTianshenAvatarInfo:Decode()
    self.index = MsgAdapter.ReadInt()
	self.avatar_item = {}
	self.avatar_item.curr_aura_id = MsgAdapter.ReadShort()
	self.avatar_item.star_level = MsgAdapter.ReadShort()
end

SCTianshenAvatarAllInfo = SCTianshenAvatarAllInfo or BaseClass(BaseProtocolStruct)
function SCTianshenAvatarAllInfo:__init()
    self.msg_type = 16276
end

function SCTianshenAvatarAllInfo:Decode()
	self.avatar_list = {}
	for i = 1, TIANSHEN_MAX_MAGIC_IMG_COUNT do
		local avatar_item = {}
		avatar_item.curr_aura_id = MsgAdapter.ReadShort()
		avatar_item.star_level = MsgAdapter.ReadShort()
		self.avatar_list[i] = avatar_item
	end
end
-----------------------双生神灵End-----------------------

-----------------------双生直购Start-----------------------
SCTianshenAvatarRMBBuyInfo = SCTianshenAvatarRMBBuyInfo or BaseClass(BaseProtocolStruct)
function SCTianshenAvatarRMBBuyInfo:__init()
    self.msg_type = 16288
end

function SCTianshenAvatarRMBBuyInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.rmb_buy_flag = {}
	for i = 0, 29 do
		self.rmb_buy_flag[i] = MsgAdapter.ReadChar()
	end

	self.video_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
end
-----------------------双生直购End-----------------------

-----------------------贯日长虹Start-----------------------
SCSunRainbowInfo = SCSunRainbowInfo or BaseClass(BaseProtocolStruct)							-- 发送角色信息
function SCSunRainbowInfo:__init()
	self.msg_type = 16285
end

function SCSunRainbowInfo:Decode()
	self.grade = MsgAdapter.ReadInt()							-- 档位
	self.person_draw_count = MsgAdapter.ReadInt()				-- 个人抽奖次数
	self.is_skip_comic = MsgAdapter.ReadInt()					-- 跳过动画？
	self.into_sp_reward_pool_num = MsgAdapter.ReadInt()			-- 下一次进去大奖的次数
	self.leiji_reward_fetch_flag = MsgAdapter.ReadLL()			-- 累计奖励领取标志
	self.point = MsgAdapter.ReadInt()							-- 积分值
	self.redemption_list = {}

	for i = 0, 49 do
		self.redemption_list[i] = MsgAdapter.ReadInt()			-- 根据索引确认奖池的兑换状态
	end
end

local function get_item_data()
	local item_data = {
		item_id = MsgAdapter.ReadInt(),
	}
	return item_data
end 

SCRASunRainbowRewardInfo = SCRASunRainbowRewardInfo or BaseClass(BaseProtocolStruct)			-- 奖品信息
function SCRASunRainbowRewardInfo:__init()
    self.msg_type = 16286
end

function SCRASunRainbowRewardInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	-- self.big_reward_id = MsgAdapter.ReadInt()

	local MAX_RESULT_RAODI_ITEM_COUNT = 10
    local baodi_item = {}
    for i = 1, MAX_RESULT_RAODI_ITEM_COUNT do
        baodi_item[i] = get_item_data()
    end
    self.baodi_item = baodi_item

	self.reward_list = {}
	for i = 1, self.count do
        self.reward_list[i] = {}
        self.reward_list[i].reward_flag = MsgAdapter.ReadInt()
        self.reward_list[i].reward_id = MsgAdapter.ReadInt()
    end
end

SCRASunRainbowRecordListInfo = SCRASunRainbowRecordListInfo or BaseClass(BaseProtocolStruct)	-- 抽奖记录
function SCRASunRainbowRecordListInfo:__init()
    self.msg_type = 16287
end

function SCRASunRainbowRecordListInfo:Decode()
    self.record_count = MsgAdapter.ReadInt()
    self.record_list = {}
    for i = 1, self.record_count do
        self.record_list[i] = {}
        self.record_list[i].draw_time = MsgAdapter.ReadUInt()
        self.record_list[i].role_name = MsgAdapter.ReadStrN(32)
        self.record_list[i].item_id = MsgAdapter.ReadInt()
        self.record_list[i].num = MsgAdapter.ReadInt()
    end
end
-----------------------贯日长虹end-----------------------------------

-----------------------爬塔一键Start-----------------------
CSTianXianGeOneKey =  CSTianXianGeOneKey or BaseClass(BaseProtocolStruct)
function  CSTianXianGeOneKey:__init()
	self.msg_type = 16289
end

function  CSTianXianGeOneKey:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.param1)
end
-----------------------爬塔一键End-----------------------

-----------------------跨服提战榜Start-----------------------
SCCrossCapRankInfo = SCCrossCapRankInfo or BaseClass(BaseProtocolStruct)

function SCCrossCapRankInfo:__init()
	self.msg_type = 16290
end

function SCCrossCapRankInfo:Decode()
	self.self_rank_value = MsgAdapter.ReadLL()
	self.total_capability = MsgAdapter.ReadLL()
	self.count = MsgAdapter.ReadInt()
	local role_uuid = RoleWGData.Instance:GetUUid()
	self.self_rank = 0
	local rank_list = {}
	for i = 1, self.count do
		local data = {
			uuid = MsgAdapter.ReadUUID(),
			usid = MsgAdapter.ReadUniqueServerID(),
			name = MsgAdapter.ReadName(),
			level = MsgAdapter.ReadInt(),
			vip_level = MsgAdapter.ReadShort(),
			sex = MsgAdapter.ReadChar(),
			is_hide_vip = MsgAdapter.ReadChar(),
			rank_value = MsgAdapter.ReadLL(), 
			rank = MsgAdapter.ReadInt(),
		}

		if role_uuid == data.uuid then
			self.self_rank = data.rank
		end

		rank_list[data.rank] = data
	end

	self.rank_item_list = rank_list
end

-------------------------------2329本服幸运大礼包Start------------------------------------
SCCrossLuckyGiftLocalBaseInfo = SCCrossLuckyGiftLocalBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossLuckyGiftLocalBaseInfo:__init()
    self.msg_type = 16280
end

function SCCrossLuckyGiftLocalBaseInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.get_rate_count = MsgAdapter.ReadUInt()
end

-- --单个大奖增加  有新增则下发
-- SCCrossLuckyGiftRateRecordAdd = SCCrossLuckyGiftRateRecordAdd or BaseClass(BaseProtocolStruct)
-- function SCCrossLuckyGiftRateRecordAdd:__init()
--     self.msg_type = 16051
-- end

-- function SCCrossLuckyGiftRateRecordAdd:Decode()
-- 	local rate_record_list_item = {}
-- 	rate_record_list_item.uuid = MsgAdapter.ReadLL()
-- 	rate_record_list_item.usid = MsgAdapter.ReadLL()
-- 	rate_record_list_item.name =  MsgAdapter.ReadStrN(32)
-- 	rate_record_list_item.level = MsgAdapter.ReadInt()
-- 	rate_record_list_item.vip_level = MsgAdapter.ReadShort()
-- 	rate_record_list_item.sex = MsgAdapter.ReadChar()
-- 	rate_record_list_item.is_hide_vip = MsgAdapter.ReadChar()
-- 	rate_record_list_item.item_id = MsgAdapter.ReadUShort()
-- 	rate_record_list_item.is_bind = MsgAdapter.ReadChar()
-- 	rate_record_list_item.reverch = MsgAdapter.ReadChar()
-- 	rate_record_list_item.num = MsgAdapter.ReadInt()
-- 	self.rate_record_list = rate_record_list_item
-- end

--购买结果返回
SCCrossLuckyGiftLocalBuyResult = SCCrossLuckyGiftLocalBuyResult or BaseClass(BaseProtocolStruct)
function SCCrossLuckyGiftLocalBuyResult:__init()
    self.msg_type = 16281
end

function SCCrossLuckyGiftLocalBuyResult:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.seq = MsgAdapter.ReadInt()
	self.mode = MsgAdapter.ReadInt()
	self.add_lucky = MsgAdapter.ReadInt()
	self.item_count = MsgAdapter.ReadInt()

	local item_list = {}
	for i = 1, self.item_count do
		local list_item = {}
		list_item.item_id = MsgAdapter.ReadUShort()
		list_item.reversh = MsgAdapter.ReadChar()
		list_item.is_bind = MsgAdapter.ReadChar()
		list_item.num = MsgAdapter.ReadInt()
		item_list[i] = list_item
	end
	self.item_list = item_list
end

SCCrossLuckyGiftLocalInfo = SCCrossLuckyGiftLocalInfo or BaseClass(BaseProtocolStruct)
function SCCrossLuckyGiftLocalInfo:__init()
    self.msg_type = 16282
end

function SCCrossLuckyGiftLocalInfo:Decode()
	self.lucky_value = MsgAdapter.ReadInt()
	self.daily_reward_flag = MsgAdapter.ReadInt()
	self.lucky_grade_reward_flag = MsgAdapter.ReadLL()
	self.rmb_buy_flag = MsgAdapter.ReadLL()
	self.lucky_double_grade = MsgAdapter.ReadInt()
	self.chongzhi_add_lucky = MsgAdapter.ReadInt()
end

SCCrossLuckyGiftLocalRateRecordInfo = SCCrossLuckyGiftLocalRateRecordInfo or BaseClass(BaseProtocolStruct)
function SCCrossLuckyGiftLocalRateRecordInfo:__init()
    self.msg_type = 16283
end

function SCCrossLuckyGiftLocalRateRecordInfo:Decode()
	self.rate_record_count = MsgAdapter.ReadInt()

	local rate_record_list = {}
	for i = 1, self.rate_record_count do
		local rate_record_list_item = {}
		rate_record_list_item.name = MsgAdapter.ReadStrN(32)
		rate_record_list_item.uid = MsgAdapter.ReadInt()
		rate_record_list_item.num = MsgAdapter.ReadInt()
		rate_record_list_item.item_id = MsgAdapter.ReadUShort()
		rate_record_list_item.is_bind = MsgAdapter.ReadChar()
		rate_record_list_item.reverch = MsgAdapter.ReadChar()
		rate_record_list[i] = rate_record_list_item
	end
	self.rate_record_list = rate_record_list
end

SCCrossLuckyGiftLocalRankInfo = SCCrossLuckyGiftLocalRankInfo or BaseClass(BaseProtocolStruct)
function SCCrossLuckyGiftLocalRankInfo:__init()
    self.msg_type = 16284
end

function SCCrossLuckyGiftLocalRankInfo:Decode()
	self.count = MsgAdapter.ReadInt()

	local rank_item_list = {}
	for i = 1, self.count do
		local rank_item_list_item = {}
		rank_item_list_item.uuid = MsgAdapter.ReadInt()
		rank_item_list_item.usid = MsgAdapter.ReadLL()
		rank_item_list_item.name = MsgAdapter.ReadStrN(32)
		rank_item_list_item.level = MsgAdapter.ReadInt()
		rank_item_list_item.vip_level = MsgAdapter.ReadShort()
		rank_item_list_item.sex = MsgAdapter.ReadChar()
		rank_item_list_item.is_hide_vip = MsgAdapter.ReadChar()
		rank_item_list_item.lucky_value = MsgAdapter.ReadInt()
		-- 后端是排好序发下来的, 没再发排名id,直接顺序赋值
		rank_item_list_item.rank_id = i
		rank_item_list[i] = rank_item_list_item
	end
	self.rank_item_list = rank_item_list
end

-------------------------------2329幸运大礼包End------------------------------------

-------------------------------------------情缘推荐---------------------------------------
CSGetRandomSingleRoleList = CSGetRandomSingleRoleList or BaseClass(BaseProtocolStruct)
function CSGetRandomSingleRoleList:__init()
    self.msg_type = 16299
end

function CSGetRandomSingleRoleList:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

SCRandomSingleRoleList = SCRandomSingleRoleList or BaseClass(BaseProtocolStruct)
function SCRandomSingleRoleList:__init()
    self.msg_type = 16300
end

function SCRandomSingleRoleList:Decode()
	self.count = MsgAdapter.ReadInt()

	local role_list = {}
	for i = 1, self.count do
		role_list[i] = NearRoleStruct.RoleInfoRead()
	end

	self.role_list = role_list
end