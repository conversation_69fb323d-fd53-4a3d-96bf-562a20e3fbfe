HMSYSceneLogic = HMSYSceneLogic or BaseClass(CrossServerSceneLogic)
function HMSYSceneLogic:__init()

end

function HMSYSceneLogic:__delete()
	self.has_get_team_target = nil
end

function HMSYSceneLogic:Enter(old_scene_type, new_scene_type)
	self.has_get_team_target = -1
    CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
    BossWGCtrl.Instance:EnterSceneCallback()
	BaseFbLogic.SetLeaveFbTip(true)
	self:GoToPos()
end

function HMSYSceneLogic:Out(old_scene_type, new_scene_type)
	self.has_get_team_target = -1
	CommonFbLogic.Out(self)
    BossWGData.Instance:SetCurHMSYSceneIndex()
	BossWGCtrl.Instance:OutSceneCallback()
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)

	if BossWGData.Instance:GetIsEnterInScene() then
		BossWGCtrl.Instance:OpenBossViewByScene(old_scene_type, new_scene_type)
	end
end

function HMSYSceneLogic:OnMainRoleRelive()
end

function HMSYSceneLogic:GoToPos()
	if IS_ON_CROSSSERVER then
		return
	end

	local _, _, cur_bossId = BossWGData.Instance:GetCurSelectBossID()
  	local data = BossWGData.Instance:GetHMSYBossCfgByBossId(cur_bossId)

  	if data then
  		local sence_id = Scene.Instance:GetSceneId()
  		if not data.x_pos or not data.y_pos then return end
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = cur_bossId
		MoveCache.param1 = cur_bossId

		local range = BossWGData.Instance:GetMonsterRangeByid(cur_bossId)
		GuajiWGCtrl.Instance:MoveToPos(sence_id,data.x_pos, data.y_pos, range)
  	end
end


-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function HMSYSceneLogic:GetGuajiCharacter()
	local target_obj
	local is_need_stop = false

	target_obj, is_need_stop = self:GetNormalRole()

	if target_obj ~= nil then
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		return self:GetMonster(), nil, is_need_stop
	end
end

function HMSYSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function HMSYSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function HMSYSceneLogic:GetNextSelectTargetId()
	-- 切换目标时，挂机模式需要变成自动
	if GuajiCache.guaji_type == GuajiType.Monster then
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
	return BaseSceneLogic.GetNextSelectTargetId(self)
end

function HMSYSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end

function HMSYSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

-- function HMSYSceneLogic:AtkTeamLeaderTarget()
-- 	local team_leader_info = SocietyWGData.Instance:GetTeamLeader() or {}
-- 	local leader = Scene.Instance:GetRoleByRoleId(team_leader_info.role_id)
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

-- 	if not leader then
-- 		return
-- 	end

-- 	self.has_get_team_target = leader:GetVo() and leader:GetVo().obj_id or -1

-- 	if not leader:IsAtkPlaying() then
-- 		return
-- 	end

-- 	local target_obj
-- 	if leader:IsAtkPlaying() then
-- 		target_obj = leader:GetAttackTarget()
-- 		if not target_obj then
-- 			return
-- 		end
-- 	else
-- 		self.has_get_team_target = -1
-- 		return
-- 	end

-- 	if self:IsEnemy(target_obj) then
-- 		self.has_get_team_target = -1
-- 		GuajiWGCtrl.Instance:StopGuaji()
-- 		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
-- 	end
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end

function HMSYSceneLogic:GetTeamTargetFlag()
	return self.has_get_team_target or -1
end

function HMSYSceneLogic:Update(now_time, elapse_time)
	CrossServerSceneLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end