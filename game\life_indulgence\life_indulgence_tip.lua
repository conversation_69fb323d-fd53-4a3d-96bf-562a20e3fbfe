LifeIndulgenceTip = LifeIndulgenceTip or BaseClass(BaseRender)

function LifeIndulgenceTip:DoLoad(parent)
	self:LoadAsset("uis/view/life_indulgence_prefab", "life_indulgence_tip", parent.transform)
end

function LifeIndulgenceTip:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.click_img, BindTool.Bind(self.OnClickGetBtn, self))
end

function LifeIndulgenceTip:OnClickGetBtn()
	ViewManager.Instance:Open(GuideModuleName.LifeIndulgenceView)
end