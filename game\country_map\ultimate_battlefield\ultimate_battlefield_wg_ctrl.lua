require("game/country_map/ultimate_battlefield/ultimate_battlefield_wg_data")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_shop_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_reward_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_score_reward_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_camp_reward_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_talent_preview_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_talent_select_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_answer_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_answer_result_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_guess_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_follow")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_explain_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_person_score_reward_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_score_rank_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_jiesuan_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_guess_history_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_barrage_view")
require("game/country_map/ultimate_battlefield/ultimate_battlefield_battle_kill_view")


UltimateBattlefieldWGCtrl = UltimateBattlefieldWGCtrl or BaseClass(BaseWGCtrl)

function UltimateBattlefieldWGCtrl:__init()
	if UltimateBattlefieldWGCtrl.Instance ~= nil then
		ErrorLog("[UltimateBattlefieldWGCtrl] attempt to create singleton twice!")
		return
	end

	UltimateBattlefieldWGCtrl.Instance = self
    self.data = UltimateBattlefieldWGData.New()
	self.shop_view = UltimateBattlefieldShopView.New()
	self.reward_view = UltimateBattlefieldRewardView.New(GuideModuleName.UltimateBattlefieldReward)
	self.talent_select_view = UltimateBattleTalentSelectView.New()
	self.talent_preview_view = UltimateBattleTalentPerviewView.New()
	self.answer_view = UltimateBattleAnswerView.New()
	self.answer_result_view = UltimateBattleAnswerResultView.New()
	self.follow_view = UltimateBattlefieldFollow.New()	-- 展示UI，这里使用了紫荆之巅的UI，因为UI完全一样
	self.guess_view = UltimateBattlefieldGuessView.New()
	self.explain_view = UltimateBattlefieldExplainView.New()
	self.person_reward_view = UltimateBattlefieldPersonScoreRewardView.New()
	self.score_rank_view = UltimateBattlefieldScoreRankView.New()
	self.jiesuan_view = UltimateBattlefieldJiesuanView.New()
	self.guess_history_view = UltimateBattlefieldGuessHistoryView.New()
	self.barrage_view = UltimateBattlefieldBarrageView.New()
	self.battle_kill_view = UltimateBattleKillView.New()

	self.is_kill_ed = false
	self:RegisterAllProtocols()
	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneLoadingQuite, self))
end

function UltimateBattlefieldWGCtrl:__delete()
	UltimateBattlefieldWGCtrl.Instance = nil
    if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.shop_view then
		self.shop_view:DeleteMe()
		self.shop_view = nil
	end

	if self.reward_view then
		self.reward_view:DeleteMe()
		self.reward_view = nil
	end

	if self.talent_select_view then
		self.talent_select_view:DeleteMe()
		self.talent_select_view = nil
	end

	if self.talent_preview_view then
		self.talent_preview_view:DeleteMe()
		self.talent_preview_view = nil
	end

	if self.answer_view then
		self.answer_view:DeleteMe()
		self.answer_view = nil
	end

	if self.answer_result_view then
		self.answer_result_view:DeleteMe()
		self.answer_result_view = nil
	end

	if self.follow_view then
		self.follow_view:DeleteMe()
		self.follow_view = nil
	end

	if self.guess_view then
		self.guess_view:DeleteMe()
		self.guess_view = nil
	end

	if self.explain_view then
		self.explain_view:DeleteMe()
		self.explain_view = nil
	end

	if self.person_reward_view then
		self.person_reward_view:DeleteMe()
		self.person_reward_view = nil
	end

	if self.score_rank_view then
		self.score_rank_view:DeleteMe()
		self.score_rank_view = nil
	end

	if self.jiesuan_view then
		self.jiesuan_view:DeleteMe()
		self.jiesuan_view = nil
	end

	if self.guess_history_view then
		self.guess_history_view:DeleteMe()
		self.guess_history_view = nil
	end

	if self.barrage_view then
		self.barrage_view:DeleteMe()
		self.barrage_view = nil
	end

	if self.battle_kill_view then
		self.battle_kill_view:DeleteMe()
		self.battle_kill_view = nil
	end

	self:RemoveRoundOverDelayTimer()
end

function UltimateBattlefieldWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCross1VNOperate)
	self:RegisterProtocol(SCCross1VNRoomInfo, "OnSCCross1VNRoomInfo")
	self:RegisterProtocol(SCCross1VNScoreRankInfo, "OnSCCross1VNScoreRankInfo")
	self:RegisterProtocol(SCCross1VNOtherBaseInfo, "OnSCCross1VNOtherBaseInfo")
	self:RegisterProtocol(SCCross1VNStandySceneInfo, "OnSCCross1VNStandySceneInfo")
	self:RegisterProtocol(SCCross1VNSceneInfo, "OnSCCross1VNSceneInfo")
	self:RegisterProtocol(SCCross1VNSceneBaseInfo, "OnSCCross1VNSceneBaseInfo")
	self:RegisterProtocol(SCCross1VNSceneCampChange, "OnSCCross1VNSceneCampChange")
	self:RegisterProtocol(SCCross1VNBaseInfo, "OnSCCross1VNBaseInfo")
	self:RegisterProtocol(SCCross1VNWorshipInfo, "OnSCCross1VNWorshipInfo")
	self:RegisterProtocol(SCCross1VNGuessFlagUpdate, "OnSCCross1VNGuessFlagUpdate")
	self:RegisterProtocol(SCCross1VNWorshipItemUpdate, "OnSCCross1VNWorshipItemUpdate")

	self:RegisterProtocol(CSCross1VNBarrageChat)
	self:RegisterProtocol(SCCross1VNBarrageChatForward, "OnSCCross1VNBarrageChatForward")
	self:RegisterProtocol(SCCross1VNBarrageChatOpen, "OnSCCross1VNBarrageChatOpen")
end

-- 场景加载完成
function UltimateBattlefieldWGCtrl:OnSceneLoadingQuite()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_ULTIMATE_BATTLE_READY or scene_type == SceneType.CROSS_ULTIMATE_BATTLE then
		return
	end

	-- 场景加载完成获取一下房间信息
	self:RequestRoomInfo()
	self:CloseJieSuanView()
	self:RequestOtherBaseInfo()
end

--------------------------------------协议---------------------------------------------
-- 1VN 操作请求
function UltimateBattlefieldWGCtrl:SendCSCross1VNOperate(opera_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCross1VNOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	-- print_error("1VN 操作请求", protocol)
	protocol:EncodeAndSend()
end

-- 1VN 弹幕发送
function UltimateBattlefieldWGCtrl:SendCSCross1VNBarrageChat(content)
	-- print_error("发送弹幕SendCSCross1VNBarrageChat", content)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCross1VNBarrageChat)
	protocol.content = ChatFilter.Instance:Filter(content)
	protocol:EncodeAndSend()
end

-- 请求房间信息
function UltimateBattlefieldWGCtrl:RequestRoomInfo()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_ROOM_INFO)
end

-- 请求其他基础信息
function UltimateBattlefieldWGCtrl:RequestOtherBaseInfo()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_OTHER_BASE_INFO)
end

-- 请求排行信息
function UltimateBattlefieldWGCtrl:RequestRankInfo()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_RANK_INFO)
end

-- 请求答题
function UltimateBattlefieldWGCtrl:RequestAnswer(seq)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_ANSWER, seq)
end

-- 请求选择天赋
function UltimateBattlefieldWGCtrl:RequestChooseTalent(seq)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_CHOOSE_TALENT, seq)
end

-- 请求刷新天赋
function UltimateBattlefieldWGCtrl:RequestRefreshTalent(seq)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_REFRESH_TALENT, seq)
end

-- 请求竞猜
function UltimateBattlefieldWGCtrl:RequestStageGuess(camp)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_STAGE_GUESS, camp)
end

-- 请求购买天赋
function UltimateBattlefieldWGCtrl:RequestBuyTalent(seq)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_BUY_TALENT, seq)
end

-- 请求兑换
function UltimateBattlefieldWGCtrl:RequestConvert(seq)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_CONVERT, seq)
end

-- 请求基础信息
function UltimateBattlefieldWGCtrl:RequestBaseInfo()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_BASE_INFO)
end

-- 请求膜拜
function UltimateBattlefieldWGCtrl:RequestWorShip(param1)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_WORSHIP, param1)
end

-- 请求膜拜信息
function UltimateBattlefieldWGCtrl:RequestWorShipInfo()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_WORSHIP_INFO)
end

-- 领取被膜拜奖励
function UltimateBattlefieldWGCtrl:RequestWorShipReward()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_FETCH_BE_WORSHIP_REWARD)
end

-- 请求释放天赋技能技能
-- param1:talent_seq param2:target_id	param3:pos_x	param4:pos_y
function UltimateBattlefieldWGCtrl:RequestTalentSkill(param1, param2, param3, param4)
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_PERFORM_SKILL, param1, param2, param3, param4)
end

-- 请求释放天赋技能技能
-- param1:worship_index
function UltimateBattlefieldWGCtrl:RequestBarrageReward()
	self:SendCSCross1VNOperate(CROSS_1VN_OPERATE_TYPE.CROSS_1VN_OPERATE_TYPE_FETCH_BARRAGE_REWARD)
end

-- 房间信息返回
function UltimateBattlefieldWGCtrl:OnSCCross1VNRoomInfo(protocol)
	-- print_error("房间信息返回", protocol)
	self.data:SetRoomInfo(protocol)
end

-- 玩家积分排行榜信息
function UltimateBattlefieldWGCtrl:OnSCCross1VNScoreRankInfo(protocol)
	-- print_error("玩家积分排行榜信息", protocol)
	self.data:SetScoreRankInfo(protocol)

	if Scene.Instance:GetSceneType() == SceneType.CROSS_ULTIMATE_BATTLE then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic then
			scene_logic:ChangeSpecialOtherRoleId()
		end
	end

	if self.data:CheckActIsGuessTime() then
		self:FlushGuessView()
	else
		self:FlushFollowView()
	end
end

-- 玩家玩法相关信息 主动请求 有变更会推送(包含答题，天赋，竞猜结果)
function UltimateBattlefieldWGCtrl:OnSCCross1VNOtherBaseInfo(protocol)
	-- print_error("玩家玩法相关信息", protocol, protocol.correct_answer_times)
	self.data:SetOtherBaseInfo(protocol)
	self.data:SetQuestionRightMessage(protocol.correct_answer_times)
	-- 刷新界面
	self:FlushTalentMessage()
	self:FlushFollowView()
	self:FlushTalentSelectView()
end

-- 答题信息
function UltimateBattlefieldWGCtrl:OnSCCross1VNStandySceneInfo(protocol)
	-- print_error("答题信息", protocol)
	self.data:SetQuestionInfo(protocol)
	if protocol and protocol.question_seq == -1 then
		self:CheckIsQuestionIsFinish()
		return
	end

	if protocol.question_standby_time == 0 and protocol.question_seq ~= -1 then
		self:OpenAnswerView()
	end
end

-- 答题结果
function UltimateBattlefieldWGCtrl:CheckIsQuestionResult(question_num)
	local base_cfg = self.data:GetBaseCfg()
	if not base_cfg then
		return
	end

	local max_question_num = base_cfg.question_num or 0

	-- 最后一个题目展示结算结果
	if max_question_num == question_num then
		local right_number = self.data:GetQuestionRightNumber()
		self:OpenAnswerResultView(right_number)
	end
end

-- 检测是否答完题目
function UltimateBattlefieldWGCtrl:CheckIsQuestionIsFinish()
	local question_info = self.data:GetQuestionInfo()
	local question_num = question_info.question_num

	local base_cfg = self.data:GetBaseCfg()
	if not base_cfg then
		return false
	end

	local max_question_num = base_cfg.question_num or 0
	if max_question_num == question_num then
		-- 打开天赋选择
		local right_number = self.data:GetQuestionRightNumber()
		local talent_seq = self.data:GetPlayerTalentSeq()
		if right_number ~= 0 and talent_seq == -1 then
			self:OpenAnswerResultView(right_number)
		elseif right_number == 0 and talent_seq == -1 then
			self:OpenTalentSelectView()
		end

		local show_data = {}
		-- 刷新倒计时
		local act = ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE or nil
		local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)
		local end_time = act_status.end_time or 0
		show_data.end_time = end_time
		show_data.end_str = Language.UltimateBattlefield.WaitSelectTalentTime
		show_data.show_type = CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_TALENT
		self:OpenFollowView(show_data)
	else
		self:OpenAnswerView()
	end
end

-- 对战场景信息
function UltimateBattlefieldWGCtrl:OnSCCross1VNSceneInfo(protocol)
	-- print_error("对战场景信息", protocol)
	self.data:SetSceneInfo(protocol)
	self.is_kill_ed = false

	if Scene.Instance:GetSceneType() == SceneType.CROSS_ULTIMATE_BATTLE then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic then
			scene_logic:SetActStatus()
			scene_logic:SetFBNameStr(string.format(Language.UltimateBattlefield.RoundStr, NumberToChinaNumber(protocol.stage + 1)) )
		end
	end

	local stage_cfg = UltimateBattlefieldWGData.Instance:GetStageCfgBySeq(protocol.stage)
	if stage_cfg == nil or protocol.stage_guess_end_time == 0 then
		self:OpenJieSuanView()
	else
		-- 展示竞猜
		if self.follow_view:IsOpen() then
			if protocol.stage == 0 then
				self:RefreshBattleTime(protocol.stage, protocol.next_stage_time)
				self:OpenGuessView(protocol.stage)
			else
				-- 展示回合结束
				self.follow_view:ShowRoundOver(true)
				self.follow_view:ClearTimeDownShow()
				self:RemoveRoundOverDelayTimer()
				self.round_over_timer = GlobalTimerQuest:AddDelayTimer(function ()
					self:RefreshBattleTime(protocol.stage, protocol.next_stage_time)
					self.follow_view:ShowRoundOver(false)
					self:RemoveRoundOverDelayTimer()
					self:OpenGuessView(protocol.stage)
				end, 3.2)
			end

			-- 关闭挂机
			if Scene.Instance:GetSceneType() == SceneType.CROSS_ULTIMATE_BATTLE then
				local scene_logic = Scene.Instance:GetSceneLogic()
				if scene_logic then
					scene_logic:SetStopGuaji()
				end
			end
		end
	end
end

-- 刷新对战时间
function UltimateBattlefieldWGCtrl:RefreshBattleTime(stage, end_time)
	local end_time = end_time or 0
	local show_data = {}
	show_data.end_time = end_time
	show_data.end_str = string.format(Language.UltimateBattlefield.WaitBattleRoundTime, NumberToChinaNumber(stage + 1)) 
	-- print_error("对战场景展示倒计时", show_data.end_str)
	show_data.show_type = CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_BATTLE
	self:OpenFollowView(show_data)
end

-- 玩家天赋购买相关信息
function UltimateBattlefieldWGCtrl:OnSCCross1VNSceneBaseInfo(protocol)
	-- print_error("玩家天赋选择相关信息", protocol)
	self.data:SetTalentData(protocol)
	self:FlushFollowTalentSkillMessage()
end

-- 玩家场景阵营变化
function UltimateBattlefieldWGCtrl:OnSCCross1VNSceneCampChange(protocol)
	-- print_error("玩家场景阵营变化", protocol)
	if protocol and protocol.item_list then
		-- 第一步先改变主角的阵营
		for _, item_data in ipairs(protocol.item_list) do
			local role = Scene.Instance:GetObj(item_data.obj_id)

			if role and role:GetVo() then
				role:GetVo().special_param = item_data.camp
			end
		end
		
		-- 第二步设置名称上的文本
		for _, item_data in ipairs(protocol.item_list) do
			if Scene.Instance:GetSceneType() == SceneType.CROSS_ULTIMATE_BATTLE then
				local scene_logic = Scene.Instance:GetSceneLogic()
				if scene_logic then
					scene_logic:ChangeSpecialParam(item_data.obj_id)
				end
			end
		end
	end
end

-- 玩家相关兑换信息
function UltimateBattlefieldWGCtrl:OnSCCross1VNBaseInfo(protocol)
	-- print_error("玩家相关信息", protocol)
	self.data:SetBaseInfo(protocol)
	self:FlushShopView()
	-- 刷新膜拜信息
	self:FlushRewardButton()
	RemindManager.Instance:Fire(RemindName.CrossUltimate)
end

-- 膜拜信息
function UltimateBattlefieldWGCtrl:OnSCCross1VNWorshipInfo(protocol)
	-- print_error("膜拜信息", protocol)
	self.data:SetWorshipInfoList(protocol)
end

-- 玩家竞猜数据更新
function UltimateBattlefieldWGCtrl:OnSCCross1VNGuessFlagUpdate(protocol)
	-- print_error("玩家竞猜数据更新", protocol)
	self.data:GuessFlagUpdate(protocol)
	if self.data:CheckActIsGuessTime() then
		self:FlushGuessView()
	end
end

-- 膜拜信息更新
function UltimateBattlefieldWGCtrl:OnSCCross1VNWorshipItemUpdate(protocol)
	-- print_error("膜拜信息更新", protocol)
	self.data:UpdateWorshipInfo(protocol.index, protocol.worship_item, true)
	-- 刷新膜拜信息
    RemindManager.Instance:Fire(RemindName.CrossUltimate)
	ViewManager.Instance:FlushView(GuideModuleName.CountryMapMapView, TabIndex.country_map_ultimate_battlefield, "all")
end

-- 玩家答题结果
function UltimateBattlefieldWGCtrl:UserAnswerResult(result, question_seq, answer_seq, correct_answer_times)
	self.data:SetQuestionRightMessage(correct_answer_times)
end

-- 弹幕回包
function UltimateBattlefieldWGCtrl:OnSCCross1VNBarrageChatForward(protocol)
	-- print_error("弹幕回包", protocol)
	if protocol and protocol.content then
		-- local filter_content = ChatFilter.Instance:Filter(protocol.content)
		self:AddOneSpecialDanMu(protocol.content, protocol.time, protocol.uid)
	end
end

-- 弹幕窗口
function UltimateBattlefieldWGCtrl:OnSCCross1VNBarrageChatOpen(protocol)
	-- print_error("弹幕窗口", protocol)
	self.data:SetBarrageChatOpenData(protocol)

	if self.data:CheckCanGetReward() then
		self:OpenBarrageView()
	end
end

--------------------------------------------------------------------------------------------------

-- 打开商店界面
function UltimateBattlefieldWGCtrl:OpenShopView()
	if self.shop_view:IsOpen() then
		self.shop_view:Flush()
	else
		self.shop_view:Open()
	end
end

-- 刷新商店界面
function UltimateBattlefieldWGCtrl:FlushShopView()
	if self.shop_view:IsOpen() then
		self.shop_view:Flush()
	end
end

-- 打开天赋界面
function UltimateBattlefieldWGCtrl:OpenTalentSelectView()
	local talent_seq = self.data:GetPlayerTalentSeq()
	if talent_seq ~= -1 then
		return
	end
	
	if self.talent_select_view:IsOpen() then
		self.talent_select_view:Flush()
	else
		self.talent_select_view:Open()
	end
end

-- 刷新天赋界面
function UltimateBattlefieldWGCtrl:FlushTalentSelectView()
	if self.talent_select_view:IsOpen() then
		self.talent_select_view:Flush()
	end
end

-- 打开天赋预览界面
function UltimateBattlefieldWGCtrl:OpenTalentPreviewView()
	if self.talent_preview_view:IsOpen() then
		self.talent_preview_view:Flush()
	else
		self.talent_preview_view:Open()
	end
end

-- 刷新天赋预览界面
function UltimateBattlefieldWGCtrl:FlushTalentPreviewView()
	if self.talent_preview_view:IsOpen() then
		self.talent_preview_view:Flush()
	end
end

-- 打开答题界面
function UltimateBattlefieldWGCtrl:OpenAnswerView()
	if self.answer_view:IsOpen() then
		self.answer_view:Flush()
	else
		self.answer_view:Open()
	end
end

-- 刷新答题界面
function UltimateBattlefieldWGCtrl:FlushAnswerView()
	if self.answer_view:IsOpen() then
		self.answer_view:Flush()
	end
end

-- 打开答题结果界面
function UltimateBattlefieldWGCtrl:OpenAnswerResultView(data)
	self.answer_result_view:SetData(data)

	if self.answer_result_view:IsOpen() then
		self.answer_result_view:Flush()
	else
		self.answer_result_view:Open()
	end
end

-- 打开主界面展示信息界面
function UltimateBattlefieldWGCtrl:OpenFollowView(show_data)
	self.follow_view:SetShowData(show_data)

	if self.follow_view:IsOpen() then
		self.follow_view:Flush()
	else
		self.follow_view:Open()
	end
end

-- 打开主界面展示信息界面
function UltimateBattlefieldWGCtrl:ClsoeFollowView()
	if self.follow_view:IsOpen() then
		self.follow_view:Close()
	end
end

-- 刷新主界面展示信息界面
function UltimateBattlefieldWGCtrl:FlushFollowView()
	if self.follow_view:IsOpen() then
		self.follow_view:Flush(0, "player_score")
	end
end

-- 刷新主界面天赋技能信息
function UltimateBattlefieldWGCtrl:FlushFollowTalentSkillMessage()
	if self.is_kill_ed then
		return
	end

	if self.follow_view:IsOpen() then
		self.follow_view:FlushTalentSkillMessage()
	end
end

-- 刷新主界面展示天赋信息界面
function UltimateBattlefieldWGCtrl:FlushTalentMessage()
	if self.follow_view:IsOpen() then
		self.follow_view:FlushTalentMessage()
	end
end

-- 关闭技能
function UltimateBattlefieldWGCtrl:CloseTalentSkill()
	if self.follow_view:IsOpen() then
		self.follow_view:CloseTalentSkill()
	end
end

-- 打开竞猜界面
function UltimateBattlefieldWGCtrl:OpenGuessView(stage)
	self.guess_view:SetCurrStage(stage)

	if self.guess_view:IsOpen() then
		self.guess_view:Flush()
	else
		self.guess_view:Open()
	end
end

-- 刷新竞猜界面
function UltimateBattlefieldWGCtrl:FlushGuessView()
	if self.guess_view:IsOpen() then
		self.guess_view:Flush()
	end
end

-- 打开图解
function UltimateBattlefieldWGCtrl:OpenExplainView()
	if self.explain_view:IsOpen() then
		self.explain_view:Flush()
	else
		self.explain_view:Open()
	end
end

-- 打开个人积分奖励和积分排名预览
function UltimateBattlefieldWGCtrl:OpenPersonRewardView()
	if self.person_reward_view:IsOpen() then
		self.person_reward_view:Flush()
	else
		self.person_reward_view:Open()
	end
end

-- 打开个人积分奖励和积分排名预览
function UltimateBattlefieldWGCtrl:OpenScoreRankView()
	if self.score_rank_view:IsOpen() then
		self.score_rank_view:Flush()
	else
		self.score_rank_view:Open()
	end
end

-- 打开个人积分奖励和积分排名预览
function UltimateBattlefieldWGCtrl:OpenJieSuanView()
	if self.jiesuan_view:IsOpen() then
		self.jiesuan_view:Flush()
	else
		self.jiesuan_view:Open()
	end
end

-- 打开个人积分奖励和积分排名预览
function UltimateBattlefieldWGCtrl:CloseJieSuanView()
	if self.jiesuan_view:IsOpen() then
		self.jiesuan_view:Close()
	end
end

-- 打开竞猜历史
function UltimateBattlefieldWGCtrl:OpenGuessHistoryView()
	if self.guess_history_view:IsOpen() then
		self.guess_history_view:Flush()
	else
		self.guess_history_view:Open()
	end
end

-- 打开弹窗界面
function UltimateBattlefieldWGCtrl:OpenBarrageView()
	if self.barrage_view:IsOpen() then
		self.barrage_view:ShowMainRoot(true)
		self.barrage_view:Flush()
	else
		self.barrage_view:Open()
	end
end

-- 添加一个弹幕
function UltimateBattlefieldWGCtrl:AddOneSpecialDanMu(content, time, uid)
	if self.barrage_view:IsOpen() then
		self.barrage_view:AddOneSpecialDanMu(content, time, uid)
	end
end

-- 刷新弹幕按钮状态
function UltimateBattlefieldWGCtrl:FlushRewardButton()
	if self.barrage_view:IsOpen() then
		self.barrage_view:FlushRewardButton()
	end
end

--移除回合结束
function UltimateBattlefieldWGCtrl:RemoveRoundOverDelayTimer()
    if self.round_over_timer then
        GlobalTimerQuest:CancelQuest(self.round_over_timer)
        self.round_over_timer = nil
    end
end

-- 打开陨落界面
function UltimateBattlefieldWGCtrl:OpenFuhuoView()
	self.is_kill_ed = true
	self:CloseTalentSkill()

	if self.battle_kill_view:IsOpen() then
		self.battle_kill_view:Flush()
	else
		self.battle_kill_view:Open()
	end
end

-- 设置击杀玩家
function UltimateBattlefieldWGCtrl:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid)
	self.battle_kill_view:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid)

	if self.battle_kill_view:IsOpen() then
		self.battle_kill_view:Flush()
	else
		self.battle_kill_view:Open()
	end
end