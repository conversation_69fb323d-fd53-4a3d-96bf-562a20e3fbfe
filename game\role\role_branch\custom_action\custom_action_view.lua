function RoleBranchView:ActionInitView()
    if nil == self.show_model then
		self.show_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ca_model_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.show_model:SetRenderTexUI3DModel(display_data)
		-- self.show_model:SetUI3DModel(self.node_list["ca_model_root"].transform, self.node_list["ca_model_root"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.show_model)
	end

    local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
    local extra_role_model_data = {
		weapon_res_id = weapon_res_id,
    }
    self.show_model:SetRoleResid(role_res_id, nil, extra_role_model_data)

    if not self.ca_stuff_item then
        self.ca_stuff_item = ItemCell.New(self.node_list["ca_stuff_item"])
    end

    if self.action_star_list == nil then
        self.action_star_list = {}
        for i = 1, 5 do
            self.action_star_list[i] = self.node_list["ca_star_" .. i]
        end
    end

    -- 属性列表
    if not self.action_attr_list then
        self.action_attr_list = {}
        local parent_node = self.node_list["ca_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.action_attr_list[i] = cell
        end
    end

    -- 标签列表
    self:CreatActionToggleList()
    XUI.AddClickEventListener(self.node_list["ca_btn_active"], BindTool.Bind(self.OnClickActionUpStar, self))
    XUI.AddClickEventListener(self.node_list["ca_btn_all_attr"], BindTool.Bind(self.OnClickActionAllAttr, self))
end

-- 创建标签列表
function RoleBranchView:CreatActionToggleList()
    -- print_error("---创建标签---")
    if nil == self.action_accordion_list then
        self.action_small_type_list = {}
        self.action_accordion_list = {}
        self.action_big_type_toggle_list = {}
        self.load_action_cell_complete = false
        local toggle_list = CustomActionData.Instance:GetActionShowList()
        local toggle_length = #toggle_list
        for i = 1, toggle_length do
            self.action_accordion_list[i] = self.node_list["ca_List" .. i]
            local big_btn_cell = CustomActionBigTypeRender.New(self.node_list["ca_SelectBtn" .. i])
            big_btn_cell:SetIndex(i)
            big_btn_cell:SetOnlyClickCallBack(BindTool.Bind(self.OnClickActionBigTypeToggle, self))
            self.action_big_type_toggle_list[i] = big_btn_cell

            local small_num = #toggle_list[i].child_list
            self:LoadActionSmallCellList(i, toggle_length, small_num)
        end
    end
end

-- 加载子标签
function RoleBranchView:LoadActionSmallCellList(index, big_type_num, small_num)
    -- print_error("---加载子标签---", index, big_type_num, small_num)
    local res_async_loader = AllocResAsyncLoader(self, "custom_action_small" .. index)
	res_async_loader:Load("uis/view/custom_action_ui_prefab", "custom_action_cell", nil, function(new_obj)
		local item_vo_list = {}
		for i = 1, small_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.action_accordion_list[index].transform, false)

			local item_render = CustomActionSmallTypeRender.New(obj)
            item_render:SetClickCallBack(BindTool.Bind(self.OnClickActionSmallType, self))
            item_render:SetIndex(i)
			item_vo_list[i] = item_render

			if index == big_type_num and i == small_num then
				self.load_action_cell_complete = true
			end
		end

		self.action_small_type_list[index] = item_vo_list
		if self.load_action_cell_complete then
            -- 设置数据
            self:FlushActionToggleAllData()

            -- 加载完 是否要选择标签
            if self.action_flush_wait_flag then
                self:ActionSelectToggle()
            end
		end
	end)
end

function RoleBranchView:ActionReleaseCallBack()
    self.action_small_is_pro_click = nil
    self.action_struff_data = nil
    self.action_star_list = nil
    self.action_accordion_list = nil
    self.load_action_cell_complete = nil
    self.action_flush_wait_flag = nil
    self.action_force_big_type = nil
    self.action_force_small_type = nil
    self.action_select_big_index = nil
    self.action_select_small_index = nil
    self.action_select_action_id = nil
    self.action_stop_cur = nil
    self.cur_action_show_data = nil

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    if self.ca_stuff_item then
        self.ca_stuff_item:DeleteMe()
        self.ca_stuff_item = nil
    end

    if self.action_big_type_toggle_list ~= nil then
        for k, v in ipairs(self.action_big_type_toggle_list) do
            v:DeleteMe()
        end
        self.action_big_type_toggle_list = nil
    end

    if self.action_small_type_list then
        for k, v in pairs(self.action_small_type_list) do
            for k1, v1 in pairs(v) do
                v1:DeleteMe()
            end
            self.action_small_type_list[k] = nil
        end
        self.action_small_type_list = nil
    end

    if self.action_attr_list then
        for k,v in pairs(self.action_attr_list) do
            v:DeleteMe()
        end
        self.action_attr_list = nil
    end
end

function RoleBranchView:ActionCloseCallBack()
    if self.show_model then
        self.show_model:RemoveActorTriggerEffectsAndSounds()
    end
end

function RoleBranchView:ActionShowIndexCallBack()
end

-- 刷新标签数据
function RoleBranchView:FlushActionToggleAllData()
    -- print_error("---刷新标签数据---")
    if IsEmptyTable(self.action_big_type_toggle_list) then
        return
    end

    if IsEmptyTable(self.action_small_type_list) then
        return
    end

    local toggle_list = CustomActionData.Instance:GetActionShowList()
    for k,v in pairs(self.action_big_type_toggle_list) do
        local data = toggle_list[k]
        v:SetData(toggle_list[k])
        if data then
            local small_list = self.action_small_type_list[k]
            local suit_list_data = data.child_list
            for k1,v1 in pairs(small_list) do
                v1:SetData(suit_list_data[k1])
            end
        end
    end
end

-- 标签选择
function RoleBranchView:ActionSelectToggle(force_big_type, force_small_type, stop_cur)
    -- print_error("---标签选择---", force_big_type, force_small_type, stop_cur)
    if force_big_type then
        self.action_force_big_type = force_big_type
    end

    if force_small_type then
        self.action_force_small_type = force_small_type
    end

    if not self.load_action_cell_complete then
        return
    else
        self.action_flush_wait_flag = nil
    end

    local jump_index
    if self.action_force_big_type then
        local toggle_list = CustomActionData.Instance:GetActionShowList()
        for k,v in ipairs(toggle_list) do
            if v.cfg and v.cfg.action_type == self.action_force_big_type then
                jump_index = k
                break
            end
        end

        self.action_force_big_type = nil
    elseif stop_cur then
        self.action_stop_cur = true
        jump_index = self.action_select_big_index
    else
        local toggle_list = CustomActionData.Instance:GetActionShowList()
        for k,v in ipairs(toggle_list) do
            if v.is_remind then                           -- 跳红点
                jump_index = k
                break
            end
        end
    end

    jump_index = jump_index or 1
    if self.action_select_big_index ~= jump_index then
        self.action_big_type_toggle_list[jump_index]:SetAccordionElementState(true)
    else
        self:OnClickActionBigTypeToggle(self.action_big_type_toggle_list[jump_index])
    end
end

-- 大标签回调
function RoleBranchView:OnClickActionBigTypeToggle(cell)
    -- print_error("【----点击 大 回调-----】：", cell:GetIndex(), self.action_stop_cur)
	if cell == nil then
		return
	end

    local index = cell:GetIndex()
    local data = cell:GetData()
    if data == nil then
        return
    end

    self.action_select_big_index = index
    local jump_small_index
    if self.action_force_small_type then
        for k,v in ipairs(data.child_list) do
            if v.action_id == self.action_force_small_type then
                jump_small_index = k
                break
            end
        end

        self.action_force_small_type = nil
        self.action_stop_cur = nil
    elseif self.action_stop_cur then
        jump_small_index =  self.action_select_small_index
        self.action_small_is_pro_click = true
        self.action_stop_cur = nil
    else
        for k,v in ipairs(data.child_list) do
            if v.is_remind then                               -- 跳红点
                jump_small_index = k
                break
            end
        end
    end

    jump_small_index = jump_small_index or 1
    local small_type_cell = ((self.action_small_type_list or {})[index] or {})[jump_small_index]
	if small_type_cell then
        small_type_cell:OnClick()
	end
end

-- 子标签回调
function RoleBranchView:OnClickActionSmallType(cell)
    -- print_error("【----点击 子 回调-----】：", cell:GetData().type, cell:GetData().name)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    if data.cfg.is_stay_tuned == 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.CustomAction.StayTunedStr)
        return
    end

    local action_id = data.action_id
    if self.action_select_action_id and not self.action_small_is_pro_click and self.action_select_action_id == action_id then
        return
    end

    self.action_select_action_id = action_id
    self.action_select_small_index = cell:GetIndex()
    self.cur_action_show_data = data
    local list = self.action_small_type_list[self.action_select_big_index]
    if list then
        for k,v in pairs(list) do
            v:OnSelectActionChange(action_id)
        end
    end

    self:FlushActionView()
    if self.action_small_is_pro_click then
        self.action_small_is_pro_click = nil
    else
        self:DoModelLoopAction(data.cfg.ctrl_action_name)
    end
end

function RoleBranchView:FlushActionView()
    if self.cur_action_show_data == nil then
        return
    end

    local cfg = self.cur_action_show_data.cfg
    if cfg == nil then
        return
    end

    local action_id = cfg.action_id
    local level = CustomActionData.Instance:GetRoleActionLevel(action_id)
    local is_act = level >= 0
    local is_max = false
    local stuff_item_id, stuff_num, had_num = 0, 0, 0

    local level_cfg, next_level_cfg
    next_level_cfg = CustomActionData.Instance:GetActionLevelCfg(action_id, level + 1)
    is_max = not next_level_cfg

	if not is_act then
        had_num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
        stuff_item_id = cfg.item_id
        stuff_num = cfg.item_num
    else
        level_cfg = CustomActionData.Instance:GetActionLevelCfg(action_id, level)
        if level_cfg ~= nil then
            had_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.item_id)
            stuff_item_id = level_cfg.item_id
            stuff_num = level_cfg.item_num
        end
	end

    -- 属性
    local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 6)
    for k,v in ipairs(self.action_attr_list) do
        v:SetData(attr_list[k])
        v:SetRealHideNext(true)
    end

    -- 星
    self.node_list.ca_stars_list:SetActive(is_act)
    if is_act then
        local star_res_list = GetStarImgResByStar(level)
        for k,v in pairs(self.action_star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end
    end

    -- print_error("---材料消耗---", stuff_item_id, stuff_num)
	-- 材料消耗
	if stuff_item_id > 0 and stuff_num > 0 then
		self.ca_stuff_item:SetData({item_id = stuff_item_id})
		local is_have_stuff = had_num >= stuff_num
		self.action_struff_data = {item_id = stuff_item_id, need_num = stuff_num, is_have_stuff = is_have_stuff}

		self.ca_stuff_item:SetRightBottomColorText(had_num .. "/" .. stuff_num, is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
		self.ca_stuff_item:SetRightBottomTextVisible(true)
	else
        self.action_struff_data = nil
		self.ca_stuff_item:ClearData()
		self.ca_stuff_item:SetRightBottomTextVisible(false)
		self.ca_stuff_item:SetItemIcon(ResPath.GetCommonImages("a2_ty_suo"))
	end

    -- 描述
    self.node_list.ca_desc.text.text = cfg.desc

    -- 按钮
    self.node_list.ca_btn_active:SetActive(not is_max)
    self.node_list.ca_flag_green:SetActive(is_max)
    if not is_max then
        local btn_idx = is_act and 2 or 1
        self.node_list.ca_btn_active_text.text.text = Language.CustomAction.BtnText[btn_idx]
        self.node_list.ca_btn_active_red:SetActive(self.cur_action_show_data.is_remind)
    end

    -- 战力
    self.node_list.ca_cap_value.text.text = CustomActionData.Instance:GetActionCapality(action_id)
end

function RoleBranchView:DoModelLoopAction(action_name)
    self.show_model:DoLoopRoleAction(action_name, false)

    if self.cur_action_show_data == nil then
        return
    end

    local cfg = self.cur_action_show_data.cfg
    if cfg == nil then
        return
    end

    local pos_str = cfg.model_root_pos
    if pos_str and pos_str ~= "" then
        local pos = Split(pos_str, "|")
        RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ca_model_root.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    local rot_str = cfg.model_root_rot
    if rot_str and rot_str ~= "" then
        local rot = Split(rot_str, "|")
        self.node_list.ca_model_root.transform.localRotation = Quaternion.Euler(rot[1] or 0, rot[2] or 0, rot[3] or 0)
    end

    local scale = cfg.model_root_scale or 1
    if scale and scale ~= "" then
    	RectTransform.SetLocalScale(self.node_list.ca_model_root.rect, scale)
    end
end

function RoleBranchView:OnClickActionUpStar()
    if self.cur_action_show_data == nil then
        return
    end

    if not self.action_struff_data then
        return
    end

    if not self.action_struff_data.is_have_stuff then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.action_struff_data.item_id})
        return
    end

    local action_id = self.cur_action_show_data.action_id
    local level = CustomActionData.Instance:GetRoleActionLevel(action_id)
    if level < 0 then
        CustomActionCtrl.Instance:SendOperateReq(ROLEACTION_OPERA_TYPE.ACTIVE, action_id)
    else
        CustomActionCtrl.Instance:SendOperateReq(ROLEACTION_OPERA_TYPE.UPGRADE, action_id)
    end
end

function RoleBranchView:OnClickActionAllAttr()
    local attr_list = CustomActionData.Instance:GetActionAllAttrList()
    if IsEmptyTable(attr_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomAction.AllAttrErrorInfo)
        return
    end

    local tips_data = {
        title_text = Language.CustomAction.AllAttrTitle,
        attr_data = attr_list,
        prefix_text = "+"
    }
    TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

-- 特效
function RoleBranchView:ShowActionSucessEffect(effect_type)
	if self.node_list["ca_level_effct"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = effect_type, is_success = true,
                                        pos = Vector2(0, 0), parent_node = self.node_list["ca_level_effct"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
    end
end



--======================================================================
-- 大类型toggle
--======================================================================
CustomActionBigTypeRender = CustomActionBigTypeRender or BaseClass(BaseRender)
function CustomActionBigTypeRender:__init()
    self.view.accordion_element:AddClickListener(BindTool.Bind(self.OnClickAccordion, self))
end

function CustomActionBigTypeRender:__delete()
end

function CustomActionBigTypeRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function CustomActionBigTypeRender:OnClickAccordion(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function CustomActionBigTypeRender:SetAccordionElementState(is_on)
    self.view.accordion_element.isOn = is_on
end

function CustomActionBigTypeRender:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    self.node_list.normal_text.text.text = self.data.cfg.name
    self.node_list.select_text.text.text = self.data.cfg.name
    self.node_list["red"]:SetActive(self.data.is_remind)
    self.view:SetActive(true)
end


--======================================================================
-- 小类型toggle
--======================================================================
CustomActionSmallTypeRender = CustomActionSmallTypeRender or BaseClass(BaseRender)
function CustomActionSmallTypeRender:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    local bundle, asset = ResPath.GetCustomActionImg("a3_dzdz_icon_" .. self.data.action_id)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)

    local level = CustomActionData.Instance:GetRoleActionLevel(self.data.action_id)
    self.node_list["name"].text.text = self.data.cfg.name
    self.node_list["red"]:SetActive(self.data.is_remind)

    local is_stay_tuned = self.data.cfg.is_stay_tuned == 1
    self.node_list["lock"]:SetActive(not is_stay_tuned and level < 0)
    self.node_list["stay_tuned_flag"]:SetActive(is_stay_tuned)

    self.view:SetActive(true)
end

function CustomActionSmallTypeRender:OnSelectActionChange(suit_type)
    if self.data == nil then
        return
    end

    local is_select = suit_type == self.data.action_id
    self.node_list["select"]:SetActive(is_select)
end
