require("game/dizang_redpack/dizang_redpack_wg_data")
require("game/dizang_redpack/dizang_redpack_view")
require("game/dizang_redpack/dizang_redpacket_pop")

DiZangRedPackWGCtrl = DiZangRedPackWGCtrl or BaseClass(BaseWGCtrl)

function DiZangRedPackWGCtrl:__init()
    if DiZangRedPackWGCtrl.Instance then
		error("[DiZangRedPackWGCtrl]:Attempt to create singleton twice!")
	end
	DiZangRedPackWGCtrl.Instance = self

    self.data = DiZangRedPackWGData.New()
    self.view = DiZangRedPackView.New(GuideModuleName.DiZangRedPackView)
    self.dizang_redpacket_pop = DiZangRedpacketPop.New(GuideModuleName.DiZangRedpacketPop)
    self:RegisterAllProtocols()

    self.red_pack_open_status = false
    self.red_pack_remind_status = false
    self.no_first_protocol = false

    self.role_data_change = BindTool.Bind(self.CheckRedpackOpen, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
    
    if not self.window_close_evt then
        self.window_close_evt = GlobalEventSystem:Bind(OtherEventType.VIEW_CLOSE, BindTool.Bind(self.OpenRedpacketPop, self))
    end

    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.OpenRedpacketPop, self))
end

function DiZangRedPackWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
    end
    
    if self.window_close_evt then
        GlobalEventSystem:UnBind(self.window_close_evt)
        self.window_close_evt = nil
    end

    DiZangRedPackWGCtrl.Instance = nil
end

function DiZangRedPackWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSJizoGiftOperate)
    self:RegisterProtocol(SCJizoGiftInfo, "OnJizoGiftInfo")
    self:RegisterProtocol(SCJizoGiftRecordInfo, "OnJizoGiftRecordInfo")
    self:RegisterProtocol(SCJizoGiftRecordAdd, "OnJizoGiftRecordAdd")
end

function DiZangRedPackWGCtrl:OnJizoGiftInfo(protocol)
    -- print_error("---信息---", protocol)
    self.data:SetInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "flush_num")
    end

    self:CheckRedpackOpen()

    self.no_first_protocol = true
end

function DiZangRedPackWGCtrl:CheckRedpackOpen()
    -- 后端定义该值 > 0，满足开启条件
    local status = self.data:GetStatus()
    status = status and self.data:CheckShowRedpack()
    local is_show = status and not self.data:GetIsFetch()
    local is_remind = self.data:GetRemind()
    local is_first = self.no_first_protocol and (is_show ~= self.red_pack_open_status) and is_show

    if is_show ~= self.red_pack_open_status or is_remind ~= self.red_pack_remind_status then
        self.red_pack_open_status = is_show
        self.red_pack_remind_status = is_remind
        if self.no_first_protocol and is_show then
            self:PlayRedpackAudio()
            self:OpenRedpacketPop()
        end
        MainuiWGCtrl.Instance:FlushView(0, "dizang_redpack", {is_show = is_show, is_remind = is_remind, is_first = is_first})
    end
end

function DiZangRedPackWGCtrl:OnJizoGiftRecordInfo(protocol)
    -- print_error("---记录---", protocol)
    self.data:SetRecordInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "flush_record")
    end
end

function DiZangRedPackWGCtrl:OnJizoGiftRecordAdd(protocol)
    -- print_error("---add记录---", protocol)
    self.data:SetSingleRecordInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(0, "flush_record")
    end
end

function DiZangRedPackWGCtrl:SendReq(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSJizoGiftOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function DiZangRedPackWGCtrl:PlayRedpackAudio()
    --print_error("播放红包来了语音")
    local audio_name = self.data:GetOtherCfgData("audio")
    if audio_name then
        --print_error("播放红包来了语音 ====》 ", audio_name)
        local bundle, asset = ResPath.GetNpcTalkVoiceResByResName(audio_name)
        AudioManager.PlayAndForget(bundle, asset)
    end
end

function DiZangRedPackWGCtrl:OpenRedpacketPop()
    if not self.red_pack_open_status or 
    Scene.Instance:GetSceneType() ~= SceneType.Common or
    not SafeBaseView.CheckIsAllClose() then
        return
    end

    -- 每日首次
    local role_id = RoleWGData.Instance:InCrossGetOriginUid()
    local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local set_key = "dizang_redpacket_pop" .. role_id
    if PlayerPrefsUtil.GetInt(set_key) == day then
        return
    end
    PlayerPrefsUtil.SetInt(set_key, day)

    -- 打开弹窗
    ViewManager.Instance:Open(GuideModuleName.DiZangRedpacketPop)
end