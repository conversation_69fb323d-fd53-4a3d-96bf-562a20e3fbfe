CaveBossSceneLogic = CaveBossSceneLogic or BaseClass(CommonFbLogic)

function CaveBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function CaveBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function CaveBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
end

function CaveBossSceneLogic:Out()
	CommonFbLogic.Out(self)
end

function CaveBossSceneLogic:OpenFbSceneCd()

end