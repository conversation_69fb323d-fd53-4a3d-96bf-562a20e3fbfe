require("game/competition/competition_wg_data")
-- require("game/competition/competition_ui_cfg")
-- 开服比拼活动
CompetitionWGCtrl = CompetitionWGCtrl or BaseClass(BaseWGCtrl)

function CompetitionWGCtrl:__init()
	if CompetitionWGCtrl.Instance ~= nil then
		ErrorLog("[CompetitionWGCtrl] Attemp to create a singleton twice !")
	end
	CompetitionWGCtrl.Instance = self
	-- self.view = CompetitionView.New()
	self.data = CompetitionWGData.New()

	-- self.is_capability_change = false

	self.now_time = 0
	self.next_refresh_time = 0

	self:RegisterAllProtocols()
end

function CompetitionWGCtrl:__delete()
	-- self.view:DeleteMe()
	-- self.view = nil

	self.data:DeleteMe()
	self.data = nil

	CompetitionWGCtrl.Instance = nil
end

function CompetitionWGCtrl:Open()
	-- self.view:Open()
end

function CompetitionWGCtrl:RegisterAllProtocols()
	-- 注册接收到的协议
	self:RegisterProtocol(SCRABipinCapabilityInfo, "OnRABipinCapabilityInfo")

	-- 注册发送的协议
	--self:RegisterProtocol()
end

function CompetitionWGCtrl:OnRABipinCapabilityInfo(protocol)
	self.data:SetRABipinCapabilityInfo(protocol)
	self.next_refresh_time = self.now_time + 0.3
end

function CompetitionWGCtrl:CheckCompetitionRemind()
	return self.data:GetRemindNum()
end