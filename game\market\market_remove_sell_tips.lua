MarketRemoveSellTips = MarketRemoveSellTips or BaseClass(SafeBaseView)

function MarketRemoveSellTips:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel_2", {sizeDelta = Vector2(892, 582)})
	self:AddViewResource(0, "uis/view/market_ui_prefab", "market_remove_sell_tips")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function MarketRemoveSellTips:__delete()
end

function MarketRemoveSellTips:OpenCallBack()
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_data.item_id)
	MarketWGCtrl.Instance:SendCSRoleAuctionItem(cfg.big_id, cfg.small_id)
end

function MarketRemoveSellTips:LoadCallBack(index, loaded_times)
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(932, 588)
    -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(0, 0)

	self.node_list.title_view_name.text.text = Language.Market.Tab4

	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	
	self.other_same_goods_list = AsyncListView.New(MarketSellTipsSameGoods, self.node_list["other_same_goods_list"])

	XUI.AddClickEventListener(self.node_list["remove_sell_btn"], BindTool.Bind1(self.OnClickRemoveSell, self))
end

function MarketRemoveSellTips:ReleaseCallBack()
	if nil ~= self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end	

	if self.other_same_goods_list then
		self.other_same_goods_list:DeleteMe()
		self.other_same_goods_list = nil
	end
end

function MarketRemoveSellTips:SetData(data)
	self.data = data
end

function MarketRemoveSellTips:OnFlush(param_list, index)
	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	local item_data = {}
	item_data.item_id = self.data.item_data.item_id
	item_data.is_bind = self.data.item_data.is_bind
	local item_count = (other_cfg.gold_bind_id == self.data.item_data.item_id) and (self.data.item_data.num * other_cfg.min_shelves_gond_bind_num) or self.data.item_data.num
	item_data.num = item_count
	item_data.star_level = ((self.data.item_data or {}).param or {}).star_level or 0
	self.item_cell:SetData(item_data)

	local samp_goods_list_info = MarketWGData.Instance:GetGoodsInfoByItemid(self.data.item_data.item_id)
	self.other_same_goods_list:SetDataList(samp_goods_list_info)
	self.node_list["empty_tips"]:SetActive(IsEmptyTable(samp_goods_list_info))
	
	-- 物品名称
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_data.item_id, nil, false)
	-- 单价
	local num = self.data.item_data.num > 0 and self.data.item_data.num or 1 					-- 防止num为0
	self.node_list["unit_price"].text.text = math.ceil(self.data.total_price / num)
	-- 总价
	self.node_list["total_price"].text.text = self.data.total_price
	-- 数目
	self.node_list["amount"].text.text = self.data.item_data.num
	-- Vip税率说明
	self.node_list["vip_tax"].text.text = string.format(Language.Market.VipTaxDesc, VipWGData.Instance:GetRoleVipLevel(), MarketWGData.Instance:GetTax() * 100)

	-- 货币类型
	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_data.item_id)
	local auction_price_type = cfg.auction_price_type
	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		self.node_list.total_price_img.image:LoadSprite(bundel ,asset, function ()
			self.node_list.total_price_img.image:SetNativeSize()
		end)

		self.node_list.unit_price_img.image:LoadSprite(bundel, asset, function ()
			self.node_list.unit_price_img.image:SetNativeSize()
		end)
	end

end


function MarketRemoveSellTips:ShowIndexCallBack()

end

-- 点击下架
function MarketRemoveSellTips:OnClickRemoveSell()
	MarketWGCtrl.Instance:SendRemoveGoods(self.data.auction_index)
	self:Close()
end

