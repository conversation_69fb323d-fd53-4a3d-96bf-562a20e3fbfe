HolyBeastCallProbabilityView  = HolyBeastCallProbabilityView  or BaseClass(SafeBaseView)

function HolyBeastCallProbabilityView :__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(650, 400)})
    self:AddViewResource(0, "uis/view/holy_beast_call_ui_prefab", "layout_holy_beast_call_probability")
end

function HolyBeastCallProbabilityView :ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function HolyBeastCallProbabilityView :LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HolyBeastCall.ProbabilityTitle

     if not self.probability_list then
        self.probability_list = AsyncListView.New(HolyBeastCallProItemRende, self.node_list.ph_pro_list) 
    end
end

function HolyBeastCallProbabilityView :OnFlush()
    local info = HolyBeastCallWGData.Instance:GetItemRangeDesc()
    self.probability_list:SetDataList(info)
end

--------------------------------------HolyBeastCallProItemRende--------------------------------------------
HolyBeastCallProItemRende = HolyBeastCallProItemRende or BaseClass(BaseRender)

function HolyBeastCallProItemRende:OnFlush()
    if not self.data then
        return
    end

    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color)
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
