﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class LuaConsoleWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("LuaConsole");
		<PERSON><PERSON>unction("SendLuaCmdToCSharp", SendLuaCmdToCSharp);
		<PERSON><PERSON>("szContent", get_szContent, set_szContent);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SendLuaCmdToCSharp(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			LuaConsole.SendLuaCmdToCSharp(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_szContent(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, LuaConsole.szContent);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_szContent(IntPtr L)
	{
		try
		{
			string arg0 = ToLua.CheckString(L, 2);
			LuaConsole.szContent = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

