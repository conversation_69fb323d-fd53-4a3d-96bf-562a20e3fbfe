CheckObsolete = {}

local obsolete_list = require("editor/obsolete_list")
local stack = {}
local isObsolete = false
local new_global_obj_list = {}
local warning_cache = {}

local IgnoreClass = {
	Event = true,
	BaseViewRender = true,
	BaseViewEffect = true,
	BaseViewLoader = true,
}

local function MarkObsolete(stack)
	local class_name = GetClassName(stack.class_type)
	if IgnoreClass[class_name] then
		return
	end

	stack.vtbl.__is_obsolete = true
	for k,v in ipairs(stack.children) do
		MarkObsolete(v)
	end
end

local function IsObsolete(class_type, vtbl)
	if vtbl.__is_obsolete then
		return true
	end

	local class_name = GetClassName(class_type)
	if obsolete_list[class_name] then
		return true
	end

	return false
end

function CheckObsolete:StartCallNew(class_type, vtbl)
	table.insert(stack, {class_type = class_type, vtbl = vtbl})
	if IsObsolete(class_type, vtbl) then
		isObsolete = true
	end
end

function CheckObsolete:EndCallNew(class_type, vtbl)
	if self:CollatingStack(class_type, vtbl) then
		if isObsolete then
			MarkObsolete(stack[1])
		end

		stack = {}
		isObsolete = false
	end
end

-- local depth = 0
-- function CheckObsolete:StartCallNew(class_type, vtbl)
-- 	if isObsolete or IsObsolete(class_type, vtbl) then
-- 		depth = depth + 1
-- 		isObsolete = true
-- 		MarkObsolete({class_type = class_type, vtbl = vtbl, children = {}})
-- 	end
-- end

-- function CheckObsolete:EndCallNew(class_type, vtbl)
-- 	depth = depth - 1
-- 	if depth <= 0 then
-- 		isObsolete = false
-- 	end
-- end

function CheckObsolete:CollatingStack(class_type, vtbl)
	local info = {class_type = class_type, vtbl = vtbl, children = {}}
	while #stack > 0 do
		local t = table.remove(stack)
		if t.class_type == class_type then
			table.insert(stack, info)
			break
		else
			table.insert(info.children, t)
		end
	end

	return #stack == 1
end

function CheckObsolete:TryCallObsoleteFunction(class_type, name, file_path)
	local info = debug.getinfo(3, "Sln")
	-- 外部调用才弹出报错
	if info and info.short_src ~= file_path and not string.match(info.short_src, "^editor/.+") then
		local class_name = GetClassName(class_type)
		local key = class_name .. name .. info.source .. info.currentline
		if not warning_cache[key] then
			local msg = string.format("函数【<color=#79fa82>%s</color>】: %s() 已经弃用，请删除%s:%s 对它的调用！", class_name, name, info.source, info.currentline)
			if info.source == "@gamenet/gamenet" then
				msg = string.format("函数【<color=#79fa82>%s</color>】: %s() 已经弃用，请不要注册协议", class_name, name)
			elseif info.source == "@protocolcommon/baseprotocolstruct" then
				msg = string.format("函数【<color=#79fa82>%s</color>】: %s() 已经弃用，请不要发送协议", class_name, name)
			elseif info.source == "@systool/timerquest" then
				msg = string.format("函数【<color=#79fa82>%s</color>】: %s() 已经弃用，请不要使用计时器", class_name, name)
			elseif info.source == "@gameui/common/remind_manager" then
				msg = string.format("函数【<color=#79fa82>%s</color>】: %s() 已经弃用，请不要注册红点系统", class_name, name)
			elseif info.source == "@systool/event" or info.source == "@game/common/base_event" then
				msg = string.format("函数【<color=#79fa82>%s</color>】: %s() 已经弃用，请不要注册事件", class_name, name)
			end
			table.insert(new_global_obj_list, msg)
			warning_cache[key] = true
		end
	end
end

function CheckObsolete:TryUseObsoleteIndex(class_type, name, file_path)
	local info = debug.getinfo(3, "Sln")
	-- 外部调用才弹出报错
	if info.short_src ~= file_path and not string.match(info.short_src, "^editor/.+") then
		local class_name = GetClassName(class_type)
		local key = class_name .. name .. info.source .. info.currentline
		if not warning_cache[key] then
			local msg = string.format("对象<color=#79fa82>%s[%s]</color> 已经弃用，请删除%s:%s对它的引用！", class_name, name, info.source, info.currentline)
			table.insert(new_global_obj_list, msg)
			warning_cache[key] = true
		end
	end
end

function CheckObsolete:Update(now_time, elapse_time)
	if #new_global_obj_list > 0 then
		for _, msg in pairs(new_global_obj_list) do
			UnityEngine.Debug.LogError(msg)
		end

		new_global_obj_list = {}
	end
end

return CheckObsolete