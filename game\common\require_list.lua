-- 需要require的文件列表
local lua_file_list = {
	"systool/systool",
	"utils/bit",
	"utils/utf8_sub",
	"utils/timeutil",
	"utils/operate_frequency",
	"utils/xmlutil",
	"utils/playerprefs_util",
	"systool/test_tool",
	"systool/track_tool",
	"systool/skill_range_tool",
	"systool/dynamic_asset_cache",

	"gamenet/gamenet",

	"game/common/game_enum",
	"game/common/act_auto_join_enum",
	"core/draw_obj",
	"core/gamemap_helper",
	"core/visible_obj",

	"manager/preload_manager",

	"game/common/common_const",

	"game/common/base_wg_ctrl",
	"game/common/base_event",

	"game/common/global_events",
	"game/common/modules_wg_ctrl",
	"game/common/res_path",
	"game/common/common_struct",
	"game/common/color_def",
	"config/config_manager",
	"language/language",
	"gameui/game_ui",


	"config/config_client_test",
	"config/config_chatfilter",				-- 聊天内容过滤
	"config/config_usernamefilter",			-- 用户名过滤
	"config/config_decoration",
	"config/config_jump",
	"config/config_other",
	"config/config_map",
	"config/config_robert_chat",
	"config/multi_mount_pos_config",
	"config/errorcode",
	"config/gm_command_list",

	-- 常用大配置表(常驻内存)
	-- 现在配置表都是常驻内存了，不需要放在这里了


	--需要放在ctrl 类前，common 后

	-- CreateCoreModule 中需要new出来的
	"manager/avatarmanager",
	"manager/loading_priority_manager",
	"manager/audioservice",
	"game/autovoice/voice_wg_ctrl",
	"game/clientcmd/client_cmd_wg_ctrl",
	"game/time/time_wg_ctrl",
	"quality/shield_manager",
	"quality/quality_manager",
	"quality/reuseable_handle_mgr",
	"quality/reuseable_shield_handle",

	"manager/effect_manager",

	"core/gamemap_helper",
	"game/scene/actor/actor_wg_ctrl",
	"game/scene/actor/actor_trigger",
	"game/scene/actor/actor_trigger_base",
	"game/scene/actor/actor_trigger_effect",
	"utils/operate_frequency",
	"utils/fps_sample_util",

	"manager/countdownmanager",
	"manager/hotupdate",
	"manager/location",

	-- 模块列表
	"game/new_appearance/new_appearance_wg_ctrl",
	"game/tip/tip_wg_ctrl",
	"game/sharedata/gamevo_manager",
	"game/sharedata/condition_manager",
	"game/sharedata/cache_manager",
	"game/sysmsg/sysmsg_wg_ctrl",
	"game/task/task_wg_ctrl",
	"game/login/login_wg_ctrl",
	"game/guide/guide_wg_ctrl",
	"game/activity/activity_wg_ctrl",
	"game/mainui/mainui_wg_ctrl",
	"game/guide/open_fun_wg_ctrl",

	"game/scene/scene",
	"game/scene/actor/actor_config_wg_data",
	"game/fight/fight_wg_ctrl",
	"gameui/common/hud_def",
	"game/guaji/guaji_wg_ctrl",
	"game/role/role_wg_ctrl",
	"game/story/story_wg_ctrl",
	"game/story/ui_cg",
	"game/setting/setting_wg_ctrl",
	"game/map/map_wg_ctrl",
	"game/rolebag/rolebag_wg_ctrl",
	"game/rolebag/equip_target/equip_target_wg_ctrl",

	"game/role/bag/bag_wg_ctrl",
	"game/role/skill/skill_wg_ctrl",
	"game/other/other_wg_ctrl",
	"game/mount/mount_wg_ctrl",
	"game/shanhaijing/shanhaijing_wg_ctrl",
	"game/shanhaijing/shanhaijing_luoshence_wg_ctrl",
	"game/role_part_set_tools/role_part_set_tools_wg_ctrl",
	
	-- --副本
	"game/fubenpanel/fubenpanel_wg_ctrl",
	"game/fuben/fuben_wg_ctrl",
	"game/chat/chat_wg_ctrl",
	"game/society/society_wg_ctrl",
	 "game/flower/flower_wg_ctrl",
	"game/equipment/equipment_wg_ctrl",
	"game/marry/marry_wg_ctrl",
	"game/rank/rank_wg_ctrl",
	"game/society/master/master_wg_ctrl",
	"game/marry/profess_wall/profess_wall_wg_ctrl",

	"game/appearance/appearance_wg_ctrl",
	"game/activity/activity_main_panel/activity_main_wg_ctrl",
	"game/compose/compose_wg_ctrl",
	"game/vip/vip_wg_ctrl",
	"game/guild/guild_wg_ctrl",
	"game/supreme_fields/supreme_fields_wg_ctrl",
	"game/daycounter/daycounter_wg_ctrl",
    --"game/xunbao/xunbao_wg_ctrl",
    "game/homes/homes_wg_ctrl",
    "game/treasurehunt/treasure_hunt_wg_ctrl",
	"game/new_xunbao/new_xunbao_wg_ctrl",
	"game/fuhuo/fuhuo_wg_ctrl",
	"game/market/market_wg_ctrl",
	"game/market/shop/shop_wg_ctrl",
	"game/duck_race/duck_race_wg_ctrl",
	"gameui/ui_instance_mgr",
	"game/welfare/welfare_wg_ctrl",
	"gameui/ui_instance_mgr",
	"game/browse/browse_wg_ctrl",
	"game/role/title/title_wg_ctrl",
	"game/role/role_branch/background/background_wg_ctrl",
	"game/yunbiao/yunbiao_wg_ctrl",
	"game/crossserver/crossserver_common/crossserver_wg_ctrl",
	"game/crossserver/kuafu_honorhalls/kf_honorhalls_wg_ctrl",
	"game/crossserver/kuafu_yezhanwangcheng/kf_yezhanwangcheng_wg_ctrl",
	"game/crossserver/kuafu_onevone/kf_onevone_wg_ctrl",
	"game/crossserver/kuafu_pvp/kf_pvp_wg_ctrl",
	"game/hot_spring/hot_spring_wg_ctrl",
	"game/guild_answer/guild_answer_wg_ctrl",
	"game/daily/daily_wg_ctrl",
	"game/worldserver/worldserver_wg_ctrl",
	"game/boss/boss_wg_ctrl",
	"game/boss_assist/boss_assist_wg_ctrl",
	"game/recharge/recharge_wg_ctrl",
	"game/rechargereward/rechargereward_wg_ctrl",
	"game/field1v1/field1v1_wg_ctrl",
	"game/serveractivity/daily_consume/daily_consume_wg_ctrl",
	"game/serveractivity/serveractivity_wg_ctrl",
	"game/serveractivity/gift_choose/choose_gift_wg_ctrl",
	"game/serveractivity/jinyinta/jinyinta_wg_ctrl",
	"game/serveractivity/collect_bless/collect_bless_wg_ctrl",
	"game/serveractivity/lottery_tree/lottery_tree_wg_ctrl",
	"game/serveractivity/consume_discount/consume_discount_wg_ctrl",
	"game/serveractivity/total_recharge/total_recharge_wg_ctrl",
	"game/serveractivity/huanlezadan/huanlezadan_wg_ctrl",
	"game/serveractivity/kuafu_consumption/kuafu_consumption_wg_ctrl",
	"game/serveractivity/crazy_money_tree/crazy_moneytree_wg_ctrl",
	"game/wedding/wedding_wg_ctrl",
	"game/competition/competition_wg_ctrl",
	"game/serveractivity/we_get_married/we_getmarried_wg_ctrl",
	"game/activity_hall/activity_hall_wg_ctrl",
	"game/bizuo/bizuo_wg_ctrl",
    "game/cross_team/cross_team_wg_ctrl",
	"game/new_team/new_team_wg_ctrl",
	"game/common_reward_show/common_reward_show_ctrl",
	"game/serveractivity/perfect_lover/perfect_lover_wg_ctrl",
	"game/offlinerest/offlinerest_wg_ctrl",
	"game/offlinerest/experience_fb/experience_fb_wg_ctrl",
	"game/zhuzaishendian/zhuzaishendian_wg_ctrl",
	"game/battlefield/guildbattleranked/guild_battle_ranked_wg_ctrl",
	"game/qifu/qifu_wg_ctrl",
	"game/sevenday/seven_day_wg_ctrl",
	"game/shenshou/shenshou_wg_ctrl",
	"game/transfer/transfer_wg_ctrl",
    "game/mount_lingchong/equip/mount_lingchong_equip_wg_ctrl",
	"game/serveractivity/tianshu/tianshu_wg_ctrl",
	"game/achievement/achievement_wg_ctrl",
	"game/serveractivity/charge_repayment2/charge_repayment2_wg_ctrl",
	"game/serveractivity/happy_conseme/happyconsume_wg_ctrl",
	"game/serveractivity/flower_rank/flower_rank_wg_ctrl",
	"game/serveractivity/act_wishing/act_wishing_wg_ctrl",
	"game/serveractivity/act_treasure/act_treasure_wg_ctrl",
	"game/serveractivity/tehuishop/tehui_shop_wg_ctrl",
	"game/serveractivity/consume_rank_three/consume_rank_three_wg_ctrl",
	"game/serveractivity/everyday_onelove/everyday_onelove_wg_ctrl",
	"game/baguamizhen/baguazhen_wg_ctrl",
	"game/exp_pool/exp_pool_wg_ctrl",
	"game/openserver_assist/openserver_assist_wg_ctrl",
	"game/zhanling/zhanling_wg_ctrl",
	"game/role_model_test/role_model_test_wg_ctrl",
	"game/fight_soul/fight_soul_wg_ctrl",
	"game/fairy_land_equipment/fairy_land_equipment_wg_ctrl",
	"game/capability_contrast/capability_contrast_wg_ctrl",
	"game/versions_advance_notice/versions_advance_notice_wg_ctrl",
    "game/wardrobe/wardrobe_wg_ctrl",
    "game/hm_god/hm_god_wg_ctrl",
    "game/guixu_dream/guixu_dream_wg_ctrl",
	"game/customized_suit/customized_suit_wg_ctrl",
	"game/pierre_direct_purchase/pierre_direct_purchase_wg_ctrl",
	"game/yiyuanhaoli/yiyuanhaoli_wg_ctrl",
	"game/offlinerest/assignment_wg_ctrl",

	---[[ 合F1的运营活动
	"game/operation_activity/operation_activity_wg_ctrl",
	"game/operation_activity/xianshi_miaosha/xianshi_miaosha_wg_ctrl",
	"game/operation_activity/operation_activity_wg_ctrl",
	"game/operation_activity/ctn_recharge/operation_ctn_recharge_wg_ctrl",
	"game/operation_activity/task_chain/operation_task_chain_wg_ctrl",
	"game/operation_activity/operation_activity_first_recharge/activity_first_recharge_wg_ctrl",
	"game/operation_activity/mowu_jianglin/mowu_jianglin_wg_ctrl",
	"game/operation_activity/mowang_youli/operation_mowang_wg_ctrl",
	"game/operation_activity/tiancai_chushen/tiancai_chushen_wg_ctrl",
	"game/operation_activity/duobei/activity_duobei_wg_ctrl",
	"game/operation_activity/image_show/image_show_wg_ctrl",
	"game/operation_activity/exchange_shop/exchange_shop_wg_ctrl",

	"game/operation_activity/watering_flowers/watering_flower_wg_ctrl",
	"game/operation_activity/operation_activity_leichong_recharge/activity_leichong_recharge_wg_ctrl",
	"game/operation_activity/operation_quanfu_juanxian/operation_juanxian_wg_ctrl",
	"game/operation_activity/denglu_youli/login_reward_wg_ctrl",
	"game/operation_activity/operation_happy_kuanghuan/operation_happy_kuanghuan_wg_ctrl",
	"game/operation_activity/fengzheng_duobao/fengzheng_get_reward_wg_ctrl",

	"game/operation_activity/turntable/oa_turntable_wg_ctrl",
	"game/operation_activity/fish/oa_fish_wg_ctrl",
    --]]

    "game/rebate_activity/rebate_activity_wg_ctrl",

    ---[[  合F1的合服活动
    "game/merge_activity/merge_activity_wg_ctrl",
    "game/merge_activity/zhaocai_miaomiao/merge_zhaocaimiao_wg_ctrl",
	"game/merge_activity/login_youli/login_youli_wg_ctrl",
	"game/merge_activity/merge_hm/merge_hm_wg_ctrl",
	"game/merge_activity/hefu_juling_view/hefu_juling_wg_ctrl",
	"game/merge_activity/hefu_miaosha_view/hefu_miaosha_wg_ctrl",
    "game/merge_activity/day_first_chongzhi/merge_activity_first_recharge_wg_ctrl",
	"game/merge_activity/merge_leichong/merge_leichong_recharge_wg_ctrl",
	"game/merge_activity/duobei/merge_duobei_wg_ctrl",
	"game/merge_activity/merge_fireworks/merge_fireworks_wg_ctrl",
	"game/merge_activity/merge_exchange_shop/merge_exchange_shop_wg_ctrl",

	"game/merge_activity/liemo_daren/liemo_daren_wg_ctrl",
	"game/merge_activity/merge_special_rank/merge_special_rank_wg_ctrl",
	"game/hefu/hefu_wg_ctrl",
    --]]

    ---[[  节日活动
    "game/festival_activity/festival_activity_wg_ctrl",
	"game/festival_activity/festival_login_youli/festival_login_youli_wg_ctrl",
	"game/festival_activity/festival_miaosha_view/festival_miaosha_wg_ctrl",
	"game/festival_activity/festival_leichong/festival_leichong_recharge_wg_ctrl",
	"game/festival_activity/festival_duobei/festival_duobei_wg_ctrl",
	"game/festival_activity/festival_fireworks/festival_fireworks_wg_ctrl",
	"game/festival_activity/festival_exchange_shop/festival_exchange_shop_wg_ctrl",
	"game/festival_activity/festival_mowang_youli/festival_mowang_wg_ctrl",
	"game/festival_activity/festival_special_rank/festival_special_rank_wg_ctrl",
	"game/festival_activity/festival_turn_table/festival_turn_table_wg_ctrl",
	"game/festival_activity/festival_chushen/festival_chushen_wg_ctrl",
    --]]

	"game/dizang_redpack/dizang_redpack_wg_ctrl",
	"game/online_reward/online_reward_wg_ctrl",
	"game/recharge_return_reward/recharge_return_reward_wg_ctrl",	-- --全屏视图(不是全屏的UI写在上面分开下啊)------------------------
    "game/fortunecat/fortunecat_wg_ctrl",
	"game/revive/revive_wg_ctrl",
	"game/recharge/vipty_wg_ctrl",
	"game/serveractivity/continuexiaofei/continue_xiaofei_wg_ctrl",
	"game/xinfutemai/xinfutemai_wg_ctrl",
	"game/serveractivity/must_buy/must_buy_wg_ctrl",
	"game/tianshen/tianshen_wg_ctrl",
	"game/tianshen/tianshen_linghe/tianshen_linghe_wg_ctrl",
	"game/tianshen_huamo/tianshen_huamo_wg_ctrl",
	"game/tianshen_shuangsheng/tianshen_shuangsheng_wg_ctrl",
	"game/bossxuanshang/bossoffer_wg_ctrl",
	"game/bootybay/bootybay_wg_ctrl",
	"game/xiuzhen_road/xiuzhen_road_wg_ctrl",
	"game/serveractivity/god_get_reward/god_get_reward_wg_ctrl",
    "game/baguamizhen/baguazhen_wg_ctrl",
	"game/tianshen_road/tianshen_road_wg_ctrl",
	"game/tianshen_road/tianshenroad_miaosha_view/tianshenroad_miaosha_wg_ctrl",
	"game/crossserver/kuafu_3v3/kf_3v3_wg_ctrl",
	"game/crossserver/kuafu_3v3/zhandui/zhandui_wg_ctrl",
	"game/quanmin_beizhan/quanmin_beizhan_wg_ctrl",
	"game/act_xianqi_jiefeng/act_xianqi_jiefeng_wg_ctrl",
    "game/zero_buy/layout_zero_buy_wg_ctrl",
    "game/tianshenjuexing/tianshen_juexing_wg_ctrl",
    "game/limited_time_offer/limited_time_offer_wg_ctrl",
    "game/xiuxianshilian/xiuxianshilian_wg_ctrl",
	"game/yibenwanli/yibenwanli_wg_ctrl",
	"gameui/common/step_excute_manager",
	"game/ming_wen/ming_wen_wg_ctrl",
	"game/long_hun/long_hun_wg_ctrl",
	"game/openning/openning_wg_ctrl",
	"game/calendar/calendar_wg_ctrl",
	"game/fengshenbang/fengshenbang_wg_ctrl",
	"game/serveractivity/xianlingguzhen/xianling_guzhen_wg_ctrl",
	"game/serveractivity/zhouyi_yuncheng/zhouyi_yuncheng_wg_ctrl",
	"game/gudaojizhanfuben/gudao_jizhan_follow_wg_ctrl",
	"game/qxzl/qunxiongzhulu_wg_ctrl",
	"game/novice_pop_dialog/novice_pop_dialog_wg_ctrl",
	"game/shituxiuli/shituxiuli_wg_ctrl",
	"game/guild/guild_invite/guild_invite_wg_ctrl",
	"game/scene_screen_effect/scene_screen_effect_wg_ctrl",
	"game/subpackage/subpackage_wg_ctrl",
	"game/eternal_night/eternal_night_wg_ctrl",
	"game/scene_area_effect/scene_area_effect_wg_ctrl",
	"game/limit_time_gift/limit_time_gift_wg_ctrl",
	"game/country_map/country_map_wg_ctrl",
	"game/country_map/secret/secret_area_wg_ctrl",
	"game/country_map/alchemy/alchemy_wg_ctrl",
	"game/cross_longmai/corss_longmai_wg_ctrl",
	"game/screen_shot/screen_shot_wg_ctrl",
	"game/common_skill_show/common_skill_show_ctrl",
	"game/sixiang_call/sixiang_call_wg_ctrl",
	"game/sixiang_tedian/sixiang_tedian_wg_ctrl",
	"game/long_zhu/long_zhu_wg_ctrl",
	"game/hidden_weapon/hidden_weapon_wg_ctrl",
	"game/chongbang_tip/chongbang_tip_wg_ctrl",
	"game/xianqi_tedian/xianqi_tedian_wg_ctrl",
	"game/shenji_notice/shenji_notice_wg_ctrl",
	"game/festival_activity/festival_activity_wg_ctrl",
	"game/anti_fraud_cartoon/anti_fraud_cartoon_wg_ctrl",
	"game/need_gold_gift/need_gold_gift_wg_ctrl",
	"game/rare_item_drop/rare_item_drop_wg_ctrl",
	"game/tianshen_3v3/tianshen_3v3_wg_ctrl",
	"game/boss_xiezhu/boss_xiezhu_wg_ctrl",
	"game/serveractivity/xianyu_trun_table/xianyu_trun_table_wg_ctrl",
	"game/lingzhi/linzhi_wg_ctrl",
	"game/worlds_no1/worlds_no1_wg_ctrl",
	"game/cat_explore/cat_explore_wg_ctrl",
	"game/flop_draw/flop_draw_wg_ctrl",
	"game/chain_draw/chain_draw_wg_ctrl",
	"game/chaotic_purchase/chaotic_purchase_wg_ctrl",
	"game/singlerecharge/single_recharge_wg_ctrl",
	"game/lucky_gift_bag/lucky_gift_bag_wg_ctrl",
	"game/lucky_gift_local_bag/lucky_gift_bag_local_wg_ctrl",
	"game/niudan/niudan_wg_ctrl",
	"game/five_elements/five_elements_wg_ctrl",
	"game/cross_flower_rank/cross_flower_rank_wg_ctrl",
	"game/honghuang_good_ceremony/honghuang_good_ceremony_wg_ctrl",
	"game/honghuan_classic/honghuang_classic_wg_ctrl",
	"game/chaotic_vip_special/chaotic_vip_special_wg_ctrl",
	"game/activity_limit_buy/activity_limit_buy_wg_ctrl",
	"game/rebate_gift/rebate_gift_activity_wg_ctrl",
	"game/downloadweb/download_web_wg_ctrl",
	"game/help_rank/help_rank_wg_ctrl",
	"game/tianshen_purchase/tianshen_purchase_wg_ctrl",
	"game/god_purchase/god_purchase_wg_ctrl",
	"game/holy_dark_weapon/holy_dark_weapon_wg_ctrl",
	"game/glory_crystal/glory_crystal_wg_ctrl",
	"game/vip_service/vip_service_wg_ctrl",
	"game/boss/boss_privilege_wg_ctrl",
	"game/sworn/sworn_wg_ctrl",
	"game/diy_draw/diy_draw_wg_ctrl",
	"game/god_of_wealth/godofwealth_wg_ctrl",
	"game/cash_point/cash_point_wg_ctrl",
	"game/recharge_volume/recharge_volume_wg_ctrl",
	"game/longxi/long_xi_wg_ctrl",
	"game/level_recharge/level_recharge_wg_ctrl",
	"game/country_map/yanglongsi/yanglongsi_wg_ctrl",
	"game/system_cap_rank/system_cap_rank_wg_ctrl",
	"game/operation_activity/recharge_rank/operation_activity_recharge_rank_wg_ctrl",
	"game/xuyuan_freshpool/xuyuan_freshpool_wg_ctrl",
	"game/jiangshan_ruhua/jiangshan_ruhua_wg_ctrl",
	"game/dragon_temple/dragon_temple_wg_ctrl",
	"game/artifact/artifact_wg_ctrl",
	"game/shitian_suit/shitian_suit_wg_ctrl",
	"game/today_special/today_special_wg_ctrl",
	"game/hammer_plan/hammer_plan_wg_ctrl",
	--"game/accumulative_login/accumulative_login_wg_ctrl",
	"game/system_force/system_force_wg_ctrl",
	"game/oneday_recharge/oneday_recharge_wg_ctrl",
	"game/fireworks_draw2/fireworks_draw2_wg_ctrl",
	"game/special_draw/special_draw_wg_ctrl",
	"game/super_purchase/super_purchase_wg_ctrl",
	"game/hidegold_shop/hidegold_shop_wg_ctrl",
	"game/multi_func/multi_function_wg_ctrl",
	"game/activity_privilege_purchase/activity_privilege_buy_wg_ctrl",
	"game/huanhua_fetter/huanhua_fetter_wg_ctrl",
	"game/huanhua_fetter/new_huanhua_fetter_wg_ctrl",
	--"game/yinian_magic/yinian_magic_wg_ctrl",
	"game/cheap_shop_purchase/cheap_shop_purchase_wg_ctrl",
	"game/activity_dragon_secret/activity_dragon_secret_wg_ctrl",
	"game/activity_amount_accumulatively_recharged/activity_amount_recharged_wg_ctrl",
	"game/cross_consume_rank/cross_consume_rank_wg_ctrl",
	"game/activity_collection_word/activity_collection_word_wg_ctrl",
	"game/cangmingchase/cangming_chase_wg_ctrl",
	"game/goldstone/gold_stone_wg_ctrl",
	"game/privileged_guidance/privileged_guidance_wg_ctrl",
	"game/premium_gift/premium_gift_wg_ctrl",
	"game/tianshi_godownhill/tianshi_godownhill_wg_ctrl",
	"game/weiwoduzun/weiwo_duzun_wg_ctrl",
	"game/new_fight_mount/new_fight_mount_wg_ctrl",
	"game/interlude_pop_dialog/interlude_pop_dialog_wg_ctrl",
	"game/holy_beast_call/holy_beast_call_wg_ctrl",
	"game/boss_must_fall_privilege/boss_must_fall_privilege_wg_ctrl",
	"game/wuhunzhenshen/wuhun_wg_ctrl",
	"game/activity_fold_fun/activity_fold_fun_wg_ctrl",
	"game/play_skill_float_word/play_skill_float_word_wg_ctrl",
	"game/cultivation/cultivation_wg_ctrl",
	"game/holy_heavenly_domain/holy_heavenly_domain_wg_ctrl",
	"game/rolecharmnotice/rolecharm_notic_wg_ctrl",
	"game/person_mabiboss/person_mabiboss_wg_ctrl",
	"game/chessboard_treasures/chessboard_treasue_ctrl",
	"game/shennutianzhu/shennutianzhu_wg_ctrl",
	"game/cross_flag_grabbing_battlefield/cross_flag_grabbing_battlefield_wg_ctrl",
	"game/customized_rumors/customized_rumors_wg_ctrl",
	"game/boundless_joy/boundless_joy_wg_ctrl",
	"game/lifetime_of_love/lifetime_of_love_wg_ctrl",
	"game/control_beasts/control_beasts_wg_ctrl",
	"game/mecha/mecha_wg_ctrl",
	"game/role/role_branch/custom_action/custom_action_ctrl",
	"game/control_beasts_contract/control_beasts_contract_wg_ctrl",
	"game/cangjin_shop/cangjin_shop_wg_ctrl",
	"game/treasure_palace/treasure_palace_ctrl",
	"game/odyssey_purchase/odyssey_purchase_ctrl",
	"game/country_map/ultimate_battlefield/ultimate_battlefield_wg_ctrl",
	"game/bulit_in_gm/bulit_in_gm_ctrl",
	"game/moran_xuanyuan/moran_xuanyuan_wg_ctrl",
	"game/panacea_furnace/panacea_furnace_wg_ctrl",
	"game/shitian_suit/shitian_suit_strengthen/shitian_suit_strengthen_wg_ctrl",
	"game/reiki_seed/reiki_seed_wg_ctrl",
	"game/vientiane_tianyin/vientiane_tianyin_ctrl",
	"game/twin_direct_purchase/twin_direct_purchase_wg_ctrl",
	"game/sun_rainbow/sun_rainbow_wg_ctrl",
	"game/everything_under_the_sun/everything_under_the_sun_wg_ctrl",
	"game/kf_cap_rank/kf_cap_rank_wg_ctrl",
	"game/capability_up_gift/capability_up_gift_wg_ctrl",
	"game/tianxian_pavilion/tianxian_pavilion_wg_ctrl",
	"game/bf_cap_rank/bf_cap_rank_wg_ctrl",
	"game/kf_attribute_stone_rank/kf_attribute_stone_rank_wg_ctrl",
	"game/discount_purchase/discount_purchase_wg_ctrl",
	"game/cross_lover/loverpk_wg_ctrl",
	"game/jinghuashuiyue/jinghuashuiyue_wg_ctrl",
	"game/yinian_magic/yinian_magic_wg_ctrl",
	"game/cross_marry_rank/cross_marry_rank_wg_ctrl",
	"game/sworn_recharge/sworn_recharge_wg_ctrl",
	"game/strange_catalog/strange_catalog_wg_ctrl",
	"game/lingyu_active/lingyu_active_wg_ctrl",
	"game/mystery_box/mystery_box_wg_ctrl",
	"game/positional_warfare/positional_warfare_wg_ctrl",
	"game/role/secret_record/secret_record_wg_ctrl",
	"game/most_venerable/most_venerable_wg_ctrl",
	"game/boss/boss_zhanling_wg_ctrl",
	"game/skill_break_purchase/skill_break_purchase_wg_ctrl",
	"game/mengling/mengling_wg_ctrl",
	"game/hit_hamster/hit_hamster_wg_ctrl",
	"game/equip_body/equip_body_wg_ctrl",
	"game/longyun_zhanling/longyun_zhanling_wg_ctrl",
	"game/conquest_war/conquest_war_wg_ctrl",
	"game/capability_welfare/capability_welfare_wg_ctrl",
	"game/openserver_invest/openserver_invest_wg_ctrl",
	"game/lord_every_day_shop/lord_every_day_shop_wg_ctrl",
	"game/auction_tips/auction_tips_wg_ctrl",
	"game/triple_recharge/triple_recharge_wg_ctrl",
	"game/fashion_exchange_shop/fashion_exchange_shop_wg_ctrl",
	"game/phantom_dreamland/phantom_dreamland_wg_ctrl",
	"game/world_treasure/world_treasure_wg_ctrl",
	"game/haoli_wanzhang/haoli_wanzhang_wg_ctrl",
	"game/total_recharge_gift/total_recharge_gift_wg_ctrl",
	"game/haoli_wanzhang3/haoli_wanzhang3_wg_ctrl",
	"game/land_war_fb_person/land_war_fb_person_wg_ctrl",
	"game/red_packet_rain/red_packet_rain_wg_ctrl",
	---[[  节日活动
	"game/new_festival_activity/new_festival_activity_wg_ctrl",
	"game/new_festival_activity/new_festival_prayer/new_festival_prayer_wg_ctrl",
	"game/new_festival_activity/new_festival_denglu/new_festival_denglu_wg_ctrl",
	"game/new_festival_activity/new_festival_collect_item/new_festival_collect_item_wg_ctrl",
	"game/new_festival_activity/new_festival_collect_card/new_festival_collect_card_wg_ctrl",
	"game/new_festival_activity/new_festival_tehui_shop/new_festival_tehui_shop_wg_ctrl",
	"game/new_festival_activity/new_festival_consume_rebate/new_festival_consume_rebate_wg_ctrl",
	"game/new_festival_activity/new_festival_boss_drop/new_festival_boss_drop_wg_ctrl",
	"game/new_festival_activity/new_festival_rank/new_festival_rank_wg_ctrl",
	"game/new_festival_activity/new_festival_raffle/new_festival_raffle_wg_ctrl",
	"game/new_festival_activity/new_festival_recharge/new_festival_recharge_wg_ctrl",
 	--]]

	"game/arena_tianti/arena_tianti_wg_ctrl",
	"game/cross_air_war/cross_air_war_wg_ctrl",
	"game/draw_gift/draw_gift_wg_ctrl",
	"game/boss_invasion/boss_invasion_wg_ctrl",
	"game/billion_subsidy/billion_subsidy_wg_ctrl",
	"game/qte/qte_wg_ctrl",
	"game/new_appearance_dye/new_appearance_dye_wg_ctrl",
	"game/role_diy_appearance/role_diy_appearance_wg_ctrl",
	"game/dujie/dujie_wg_ctrl",
	"game/recharge_rank/recharge_rank_wg_ctrl",
	"game/xiuwei/xiuwei_wg_ctrl",
	"game/consume_rank/consume_rank_wg_ctrl",
	"game/yanyuge/yanyuge_wg_ctrl",
	"game/thunder_mana/thunder_mana_wg_ctrl",
	"game/dressing_role_diy/dressing_role_diy_wg_ctrl",
	"game/life_indulgence/life_indulgence_wg_ctrl",
	"game/hundred_equip/hundred_equip_wg_ctrl",
	"game/control_beasts/control_beasts_oa_draw/control_beasts_oa_draw_wg_ctrl",
	"game/exp_guide/exp_guide_wg_ctrl",
	"game/fuben_team_common_boss/fuben_team_common_boss_wg_ctrl",
	"game/fuben_team_common_tower/fuben_team_common_tower_wg_ctrl",
	"game/privilege_collection/privilege_collection_wg_ctrl",
	"game/dragon_trial/dragon_trial_wg_ctrl",
	"game/game_assistant/game_assistant_wg_ctrl",
	"game/half_purchase/half_purchase_wg_ctrl",
	"game/cross_treasure/cross_treasure_wg_ctrl",
	"game/treasurehunt/treasure_thunder/treasure_hunt_thunder_wg_ctrl",
	"game/new_appearance/multi_mount/multi_mount_wg_ctrl",
	"game/onesword_frostbite/onesword_frostbite_wg_ctrl",
}

return lua_file_list
