JinyintaExchangeView = JinyintaExchangeView or BaseClass(SafeBaseView)

function JinyintaExchangeView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/jinyinta_ui_prefab", "layout_show_exchange")
end

function JinyintaExchangeView:__delete()

end

function JinyintaExchangeView:ReleaseCallBack()
	if nil ~= self.exchange_item_list then
		self.exchange_item_list:DeleteMe()
		self.exchange_item_list = nil
	end

	if self.alert_consumer then
		self.alert_consumer:DeleteMe()
		self.alert_consumer = nil
	end
end

function JinyintaExchangeView:LoadCallBack()
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(810, 580)
	self.node_list.title_view_name.text.text = Language.Common.ChangeShopName
	if nil == self.alert_consumer then
		self.alert_consumer = Alert.New(nil, nil, nil, nil, true)
		self.alert_consumer:SetOkFunc(BindTool.Bind1(self.SendScoreReq, self))
	end
	local data_list = JinyintaWGData.Instance:GetXunBaoScoreCfg()
	self.exchange_item_list = AsyncBaseGrid.New()
	self.exchange_item_list:SetStartZeroIndex(false)
	self.exchange_item_list:CreateCells({cell_count = #data_list, col = 4
											, itemRender = JinyintaExchangeItemRender
											, assetBundle = self.ui_config[1]
											, assetName = 'ph_exchange_item'
											, list_view = self.node_list.ph_grid})
	-- 点击事件先屏蔽
	self.exchange_item_list:SetSelectCallBack(BindTool.Bind(self.ClickScoreItemHandler, self))
	self.exchange_item_list:SetDataList(data_list,3)
end

function JinyintaExchangeView:OpenCallBack()

end

function JinyintaExchangeView:ShowIndexCallBack(index)
	self:Flush()
end

function JinyintaExchangeView:OnFlush()
	local jifen_num = JinyintaWGData.Instance:GetXunBaoScore()
	self.node_list.lbl_jifen.text.text = (jifen_num)
end

function JinyintaExchangeView:ClickScoreItemHandler(item)
	if nil == item:GetData() then
		return
	end
	self.item_seq = item:GetData().seq
	
	-- 二次提示框
	if nil ~= self.alert_consumer then
		local need_score = item:GetData().consume_score
		self.alert_consumer:SetLableString(string.format(Language.OpenServer.AlertString, need_score))
		self.alert_consumer:Open()
	end
end

function JinyintaExchangeView:SendScoreReq()
	JinyintaWGCtrl.Instance:SendXunbaoReq(1, ACTIVITY_TYPE.RAND_JINYINTA, self.item_seq)
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_JINYINTA, opera_type = RendActOperaType.ONLINE_TIME_INFO})
end

--------------------------------------------
--JinyintaExchangeItemRender
--------------------------------------------

JinyintaExchangeItemRender = JinyintaExchangeItemRender or BaseClass(BaseRender)
function JinyintaExchangeItemRender:__init()
end

function JinyintaExchangeItemRender:__delete()
	if self.exchange_cell then
		self.exchange_cell:DeleteMe()
		self.exchange_cell = nil
	end
end

function JinyintaExchangeItemRender:LoadCallBack()
	self.exchange_cell = ItemCell.New(self.node_list.ph_cell)
	XUI.AddClickEventListener(self.node_list.btn_duihuan, BindTool.Bind1(self.OnClick, self))
end

function JinyintaExchangeItemRender:OnFlush()
	if nil == self.data then
		return
	end
	-- local jifen_num = JinyintaWGData.Instance:GetXunBaoScore()
	-- self.node_list.btn_duihuan:setEnabled(jifen_num >= self.data.consume_score)
	self.exchange_cell:SetData(self.data.reward_item)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
	local color = ItemWGData.Instance:GetItemColor(self.data.reward_item.item_id, self.data)
	-- self.node_list.label_exchange_item_name:setColor(color)
	self.node_list.label_exchange_item_name.text.text = ToColorStr(item_cfg.name,color)
	self.node_list.rich_exchange_item_jifen.text.text = string.format(Language.Xunbao.ExchangeJiFen, self.data.consume_score)   --, 20, COLOR3B.WHITE)
end
