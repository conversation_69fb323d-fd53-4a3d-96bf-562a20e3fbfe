require("game/boss/boss_zhanling_view")
require("game/boss/boss_zhanling_unlock_view")
require("game/boss/boss_zhanling_buy_level_view")
require("game/boss/boss_zhanling_wg_data")

BossZhanLingWGCtrl = BossZhanLingWGCtrl or BaseClass(BaseWGCtrl)
function BossZhanLingWGCtrl:__init()
    if BossZhanLingWGCtrl.Instance then
		ErrorLog("[BossZhanLingWGCtrl] Attemp to create a singleton twice !")
	end

    BossZhanLingWGCtrl.Instance = self
    self.view = BossZhanLingView.New(GuideModuleName.BossZhanLingView)
    self.zhanling_unlock_view = BossZhanLingUnlockView.New()
    self.zhanling_buy_level_view = BossZhanLingBuyLevelView.New()
	self.data = BossZhanLingWGData.New()

	self:RegisterAllProtocols()
end

function BossZhanLingWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.zhanling_unlock_view:DeleteMe()
	self.zhanling_unlock_view = nil

    self.zhanling_buy_level_view:DeleteMe()
	self.zhanling_buy_level_view = nil

	BossZhanLingWGCtrl.Instance = nil
end

-- 注册协议
function BossZhanLingWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSHuntMonsterOrderClientReq)
    self:RegisterProtocol(SCHuntMonsterOrderInfo, "OnAllInfo")
end

function BossZhanLingWGCtrl:OpenZhanLingView()
    if self.view then
        self.view:Open()
    end
end

function BossZhanLingWGCtrl:OpenZhanLingBuyLevelView()
    if self.zhanling_buy_level_view then
        self.zhanling_buy_level_view:Open()
    end
end

function BossZhanLingWGCtrl:OpenZhanLingUnLockView()
    if self.zhanling_unlock_view then
        self.zhanling_unlock_view:Open()
    end
end

function BossZhanLingWGCtrl:SendBossZhanLingReq(operate_type, param1, param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSHuntMonsterOrderClientReq)
	send_protocol.operate_type = operate_type or 0
	send_protocol.param1 = param1 or 0
    send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end

function BossZhanLingWGCtrl:OnAllInfo(protocol)
	self.data:SetAllInfo(protocol)

    self.view.old_gd_show_level = -1
    if self.view:IsOpen() then
		self.view:Flush()
	end

    if self.zhanling_buy_level_view:IsOpen() then
		self.zhanling_buy_level_view:Flush()
	end

    if self.zhanling_unlock_view:IsOpen() then
		self.zhanling_unlock_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.Boss_ZhanLing)
end