------------------------------------------------------------
--人物相关主View
------------------------------------------------------------
local GET_LIST_APPEARANCE = {
	[10] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_JIANZHEN,
	[20] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_FABAO,
	[30] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_MASK,
	[40] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_WING,
	[50] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_BELT,
	[60] = NEW_WARDROBE_FASHION_TYPE.WARDROBE_FASHION_HALO,
}

RolePartSetToolsView = RolePartSetToolsView or BaseClass(SafeBaseView)

function RolePartSetToolsView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/role_part_set_tools_ui_prefab", "role_part_set_tools_view")
	self:AddViewResource(0, "uis/view/role_part_set_tools_ui_prefab", "role_part_set_tools_grid")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.JUESE_HUANJUE})
	self:AddViewResource(0, "uis/view/role_part_set_tools_ui_prefab", "VerticalTabbar")
	self.default_index = 10
end

function RolePartSetToolsView:LoadCallBack()
	self.node_list.title_view_name.text.text = "这是一个工具，配置挂点"
	self.select_sex = nil
	self.select_prof = nil
	for sex = GameEnum.FEMALE, GameEnum.MALE do
		for prof = GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_4 do
			local node = string.format("toggle_%s_%s", sex, prof)
			if self.node_list[node] then
				XUI.AddClickEventListener(self.node_list[node],
					BindTool.Bind(self.ChangeRolesOnClickSelectRole, self, sex, prof))
			end
		end
	end

	if nil == self.model_display then
		self.model_display = RoleModel.New()
		self.model_display:SetUISceneModel(self.node_list["role_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.model_display, 0)
	end

	local TabGrop = {
		"背饰", "法器", "脸饰", "仙翼", "腰饰", "灵息"
	}

	if not self.tabbar then
		local view_bundle = "uis/view/role_part_set_tools_ui_prefab"
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:Init(TabGrop, nil, view_bundle, nil, nil)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
    end

	self:InitAllRolePartGrid()


	self.node_list["pos_x_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["pos_y_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["pos_z_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))

	self.node_list["rot_x_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["rot_y_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["rot_z_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))

	self.node_list["sca_x_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["sca_y_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))
	self.node_list["sca_z_input"].input_field.onEndEdit:AddListener(BindTool.Bind(self.SearchInputChange, self))

	XUI.AddClickEventListener(self.node_list.submit_btn, BindTool.Bind(self.SaveForPrefabs, self))
end

-- 创建已孵化背包
function RolePartSetToolsView:InitAllRolePartGrid()
	if not self.role_part_bag_list_grid then
        self.role_part_bag_list_grid = AsyncBaseGrid.New()
        self.role_part_bag_list_grid:CreateCells({
										col = 3, 
										cell_count = 1000, 
										list_view = self.node_list["role_part_set_tools_grid_grid"], 
										itemRender = RolePartBagMessageItem,
										change_cells_num = 1,
										assetBundle = "uis/view/role_part_set_tools_ui_prefab",
										assetName = "role_part_bag_item",
		})
		self.role_part_bag_list_grid:SetStartZeroIndex(false)
        self.role_part_bag_list_grid:SetSelectCallBack(BindTool.Bind(self.SelectRolePartBagCellCallBack, self))
	end
end

-- 背包点击
function RolePartSetToolsView:SelectRolePartBagCellCallBack(role_part_bag_cell)
    if not role_part_bag_cell.data then return end
	local data = role_part_bag_cell.data
    self.select_part_bag_index = role_part_bag_cell.index
    self.select_part_bag_data = role_part_bag_cell.data
	local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.part_type, data.index)
	self:ChangeRolesFlushModel(cfg.resouce)
end

-- 关闭前调用
function RolePartSetToolsView:CloseCallBack()
	self.select_sex = nil
	self.select_prof = nil
	self.role_res_id = nil
end

function RolePartSetToolsView:ReleaseCallBack()
	self.select_sex = nil
	self.select_prof = nil
	self.select_part_bag_index = nil
    self.select_part_bag_data = nil
	self.role_res_id = nil
	self.bundle = nil
	self.asset = nil
	self.now_attach_obj = nil

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

	if self.role_part_bag_list_grid then
		self.role_part_bag_list_grid:DeleteMe()
		self.role_part_bag_list_grid = nil
	end
end

function RolePartSetToolsView:OnFlush(param_t, index)
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local tab_index = GET_LIST_APPEARANCE[index]
	local list = NewAppearanceWGData.Instance:GetShowListByTabIndex(tab_index, true)
	self.role_part_bag_list_grid:SetDataList(list)
	self.role_part_bag_list_grid:JumpToIndexAndSelect(1, 14)

	if self.select_sex == nil or self.select_prof == nil then
		local node = string.format("toggle_%s_%s", sex, prof)
		if self.node_list[node] then
			self.node_list[node].toggle.isOn = true
			self:ChangeRolesOnClickSelectRole(sex, prof, true)
		end
	else
		local res_id = nil
		if self.select_part_bag_data then
			local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.select_part_bag_data.part_type, self.select_part_bag_data.index)
			res_id = cfg.resouce
		end

		self:ChangeRolesFlushModel(res_id)
	end
end

function RolePartSetToolsView:ChangeRolesOnClickSelectRole(sex, prof, isOn)
	if not isOn or (self.select_sex == sex and self.select_prof == prof) then
		return
	end

	self.select_sex = sex
	self.select_prof = prof

	local res_id = nil
	if self.select_part_bag_data then
		local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.select_part_bag_data.part_type, self.select_part_bag_data.index)
		res_id = cfg.resouce
	end
	self:ChangeRolesFlushModel(res_id)
end

function RolePartSetToolsView:ChangeRolesFlushModel(change_id)
	if self.select_sex == nil or self.select_prof == nil then
		return
	end

	local role_res_id = RoleWGData.GetJobModelId(self.select_sex, self.select_prof)
	if self.role_res_id == role_res_id and self.model_display then
		self:ChangeSelectPart(change_id)
		self.model_display:PlayRoleAction(SceneObjAnimator.UiIdle)
		return
	end

	local weapon_res_id = RoleWGData.GetJobWeaponId(self.select_sex, self.select_prof)
	local face_res, hair_res, body_res = ChangeRolesWGData.Instance:GetRoleSetDIYInfo(self.select_sex, self.select_prof)

	local extra_role_model_data = {
		prof = self.select_prof,
		sex = self.select_sex,
        d_face_res = face_res,
        d_hair_res = hair_res,
		d_body_res = body_res,
		weapon_res_id = weapon_res_id,
    }
	self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	self.model_display:FixToOrthographicOnUIScene()
	self.role_res_id = role_res_id
	self:ChangeSelectPart(change_id)
end

function RolePartSetToolsView:RemoveAllPart()
	self.model_display:RemoveWaist()
	self.model_display:RemoveMask()
	self.model_display:RemoveWing()
	self.model_display:RemoveBaoJu()
	self.model_display:RemoveJianZhen()
	self.model_display:RemoveHalo()
end

function RolePartSetToolsView:ChangeSelectPart(change_id)
	self:RemoveAllPart()
	
	if not change_id then
		return
	end

	if self.show_index == 10 then
		self.bundle, self.asset = ResPath.GetJianZhenModel(change_id)
		self.model_display:SetJianZhenResid(change_id, BindTool.Bind(self.ChangeModelPartCallBack, self))
	elseif self.show_index == 20 then
		self.bundle, self.asset = ResPath.GetFaBaoModel(change_id)
		self.model_display:SetBaoJuResid(change_id, BindTool.Bind(self.ChangeModelPartCallBack, self))
	elseif self.show_index == 30 then
		self.bundle, self.asset = ResPath.GetMaskModel(change_id)
		self.model_display:SetMaskResid(change_id, BindTool.Bind(self.ChangeModelPartCallBack, self))
	elseif self.show_index == 40 then
		self.bundle, self.asset = ResPath.GetWingModel(change_id)
		self.model_display:SetWingResid(change_id, true, BindTool.Bind(self.ChangeModelPartCallBack, self))
	elseif self.show_index == 50 then
		self.bundle, self.asset = ResPath.GetBeltModel(change_id)
		self.model_display:SetWaistResid(change_id, BindTool.Bind(self.ChangeModelPartCallBack, self))
	elseif self.show_index == 60 then
		self.bundle, self.asset = ResPath.GetHaloModel(change_id)
		self.model_display:SetHaloResid(change_id, BindTool.Bind(self.ChangeModelPartCallBack, self))
	end
end

-- 加载完成回调
function RolePartSetToolsView:ChangeModelPartCallBack(part_obj)
	if part_obj ~= nil then
		self.now_attach_obj = part_obj.gameObject:GetComponent(typeof(AttachObject))
		local prof_index = self:GetConfigProfIndex()

		if not IsNil(self.now_attach_obj) then
			local now_pos = self.now_attach_obj:GetPhysiqueConfigPos(prof_index)
			local now_rot = self.now_attach_obj:GetPhysiqueConfigRot(prof_index)
			local now_sca = self.now_attach_obj:GetPhysiqueConfigSca(prof_index)

			self.node_list["pos_x_input"].input_field.text = now_pos.x
			self.node_list["pos_y_input"].input_field.text = now_pos.y
			self.node_list["pos_z_input"].input_field.text = now_pos.z
		
			self.node_list["rot_x_input"].input_field.text = now_rot.x
			self.node_list["rot_y_input"].input_field.text = now_rot.y
			self.node_list["rot_z_input"].input_field.text = now_rot.z

			self.node_list["sca_x_input"].input_field.text = now_sca.x
			self.node_list["sca_y_input"].input_field.text = now_sca.y
			self.node_list["sca_z_input"].input_field.text = now_sca.z
		end
	end
end

local RoleProfSexId = {
	[0] = { [1] = 1002, [2] = 1005, [3] = 1004, },
	[1] = { [1] = 1001, [3] = 1003, },
}

local RoleProfIdToIndex = {
	[1001] = 0, [1002] = 1, [1003] = 2, [1004] = 3, [1005] = 4, [1006] = 5, [1007] = 6,
}


function RolePartSetToolsView:GetConfigProfIndex()
	if self.select_sex == nil or self.select_prof == nil then
		return
	end

	local prof_sex_id = RoleProfSexId[self.select_sex][self.select_prof]
	local prof_index = RoleProfIdToIndex[prof_sex_id]
	return prof_index
end

----------------------------------------------------------------------------------------------------
-- 按钮方法
function RolePartSetToolsView:SearchInputChange(str)
	local pos_x_str = self.node_list["pos_x_input"].input_field.text
	local pos_y_str = self.node_list["pos_y_input"].input_field.text
	local pos_z_str = self.node_list["pos_z_input"].input_field.text

	local rot_x_str = self.node_list["rot_x_input"].input_field.text
	local rot_y_str = self.node_list["rot_y_input"].input_field.text
	local rot_z_str = self.node_list["rot_z_input"].input_field.text

	local sca_x_str = self.node_list["sca_x_input"].input_field.text
	local sca_y_str = self.node_list["sca_y_input"].input_field.text
	local sca_z_str = self.node_list["sca_z_input"].input_field.text

	local pos_x = tonumber(pos_x_str) or 0
	local pos_y = tonumber(pos_y_str) or 0
	local pos_z = tonumber(pos_z_str) or 0
	local pos_vec = Vector3(pos_x, pos_y, pos_z)

	local rot_x = tonumber(rot_x_str) or 0
	local rot_y = tonumber(rot_y_str) or 0
	local rot_z = tonumber(rot_z_str) or 0
	local rot_vec = Vector3(rot_x, rot_y, rot_z)

	local sca_x = tonumber(sca_x_str) or 0
	local sca_y = tonumber(sca_y_str) or 0
	local sca_z = tonumber(sca_z_str) or 0
	local sca_vec = Vector3(sca_x, sca_y, sca_z)

	local prof_index = self:GetConfigProfIndex()
	
	if not IsNil(self.now_attach_obj) then
		self.now_attach_obj:ChangePhysiqueConfig(prof_index, pos_vec, rot_vec, sca_vec)
	end
end

function RolePartSetToolsView:SaveForPrefabs()
	if (not self.bundle) or (not self.asset) then
		print_error("保存路径有误，请检查代码")
		return
	end

	if not IsNil(self.now_attach_obj) then
		self.now_attach_obj:SaveSelfForPrefabs(self.bundle, self.asset)
	end
end

-- new string[] { "男剑", "女剑", "男道士", "女道士", "女刺客", "男待定", "女待定", "其他" };
-- new string[] { "1001", "1002", "1003", "1004", "1005", "1006", "1007", "0" };
----------------------------------------------------------------------
RolePartBagMessageItem = RolePartBagMessageItem or BaseClass(BaseRender)
function RolePartBagMessageItem:LoadCallBack()
	if not self.beast_item then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
		self.item_cell:SetIsShowTips(false)
		self.item_cell:SetClickCallBack(BindTool.Bind(self.OnClick, self))
    end
end

--删除写在ReleaseCallBack里的东西
function RolePartBagMessageItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function RolePartBagMessageItem:OnFlush()
    if not self.data then
        return
    end

	self.item_cell:SetData({item_id = self.data.active_stuff_id})
	local cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(self.data.part_type, self.data.index)
	local name = cfg and cfg.name or ""
	local quality = cfg and cfg.quality or 0
	self.node_list.item_name.text.text = ToColorStr(name, ITEM_COLOR[quality])
end

function RolePartBagMessageItem:OnClick()
	BaseRender.OnClick(self)
end

-- 设置是否选中
function RolePartBagMessageItem:SetSelect(is_select, item_call_back)
	local bundle, asset = ResPath.GetCommon("a3_ty_xz1")
	self.item_cell:SetSelectSpEffectImageRes(bundle, asset)
	self.item_cell:SetSelectEffectSp(is_select)	
end