SunRecordView = SunRecordView or BaseClass(SafeBaseView)

function SunRecordView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(1078, 608)})
    self:AddViewResource(0, "uis/view/sun_rainbow_ui_prefab", "sun_rainbow_record")
end

function SunRecordView:LoadCallBack()
    if not self.record_list then
        self.record_list = AsyncListView.New(SunRainbowRender, self.node_list["role_list"])
    end
end

function SunRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function SunRecordView:OpenCallBack()
    SunRainbowWgCtrl.Instance:SendReq(RA_GUANRICHANGHONG_OP_TYPE.RECORD_INFO)
end

function SunRecordView:OnFlush()
    self:FlushRecordList()
    self:FlushTitle()
end

function SunRecordView:FlushRecordList()
    local data_list = SunRainbowWgData.Instance:GetRecordInfo()
    local is_show_list = not IsEmptyTable(data_list)
    if data_list then
        self.record_list:SetDataList(data_list)
    end

    print_error("展示数据", data_list)
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end

function SunRecordView:FlushTitle()
    self.node_list.title_view_name.text.text = Language.SunRainbow.RecordTitle
end




----------------------SunRainbowRender----------------------
SunRainbowRender = SunRainbowRender or BaseClass(BaseRender)
function SunRainbowRender:LoadCallBack()
    self.node_list["txt_btn"].button:AddClickListener(BindTool.Bind(self.OnclickItem, self))
end

function SunRainbowRender:OnFlush()
    local data = self:GetData()

    local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not item_cfg then
        return
    end

    local color = ITEM_COLOR[item_cfg.color]
    local role_name = data.role_name or RoleWGData.Instance:GetAttr("name")
    local str1 = string.format(Language.FiveElementsTreasury.TxtRecord3, role_name)
    local name = string.format(Language.FiveElementsTreasury.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.FiveElementsTreasury.TxtRecord1_3, color, data.num)

    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
    self.node_list["time"].text.text = os.date("%m-%d   %X", data.record_time)
end

function SunRainbowRender:OnclickItem()
    local data = self:GetData()
    if data == nil then
        return
    end

    TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
end