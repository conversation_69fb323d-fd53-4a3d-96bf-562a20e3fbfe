EquipmentWGData = EquipmentWGData or BaseClass()

-- EquipmentWGData.ACTIVATION_TYPE = {
-- 	OK = 1,							--可以激活
-- 	ACTIVATED = 2,					--已激活
-- 	RANK_DEFICIENT = 3,				--阶数或等级不足
-- 	NOT_BEFORE = 4,					--前一件没有激活
-- 	BETTER_ACTIVATION = 5,			--已激活更好
-- }

-- EquipmentWGData.EQUIP_ACT_TYPE = {
-- 	CAN_ACTIVE = 1,                 -- 可以激活
-- 	IS_ACYIVE  = 2,					-- 已激活
-- 	RANK_DEFICIENT = 3,				-- 品质或星级 不足
-- }

-- local SEQUI_SUIT_TYPE =
-- {
-- 	SUIT_TYPE_1 = 2,	--2件套
-- 	SUIT_TYPE_2 = 4,	--4件套
-- 	SUIT_TYPE_3 = 7,	--7件套
-- }

function EquipmentWGData:InitSuitData()
	local suit_cfg_auto = ConfigManager.Instance:GetAutoConfig("suit_cfg_auto")
	self.single_equip_active_cfg = ListToMap(suit_cfg_auto.one_equip_act, "sex", "equip_body_seq", "equip_part")
	self.xianqi_equip_suit_attr_cfg = ListToMap(suit_cfg_auto.xianqi_suit_attr, "equip_body_seq", "same_order_num")
	self.equip_suit_attr_cfg = ListToMap(suit_cfg_auto.suit_stone_attr, "sex", "equip_body_seq", "same_order_num")
	self.suit_name_show_cfg = ListToMap(suit_cfg_auto.suit_name_show, "equip_body_seq")
	self.suit_equip_active_limit_cfg = suit_cfg_auto.limit
	self.suit_preview = ListToMapList(suit_cfg_auto.suit_preview, "suit_order")
	self.suit_gift_cfg = suit_cfg_auto.suit_gift

	self.order_flag_list = {} -- 套装物品激活标记   25套转位标记
	self.active_total_equip_strength_lv = {}
	self.active_total_xianqi_strength_lv = {}

	self.part_equip_stuff_num_list = {}
	self.btn_index = 1
	self.top_btn_index = 1

	-- self.suit_order_list = {}
	-- self.equip_suit_type_list = {}
	-- self.suit_part_order = {}
	-- self.xianqi_suit_attr = ListToMap(suit_cfg_auto.xianqi_suit_attr, "suit_index", "same_order_num")
	-- self.one_equip_act = ListToMapList(suit_cfg_auto.one_equip_act, "sex", "equip_body_seq", "equip_part") -- 需要处理
	-- self.suit_single_act_sex_cfg = ListToMap(suit_cfg_auto.one_equip_act, "sex", "suit_index", "equip_order", "equip_part")
	-- self.suit_stone_attr_cfg = ListToMap(suit_cfg_auto.suit_stone_attr, "sex", "equip_body_seq", "same_order_num")
	-- self.suit_preview_set = ListToMapList(suit_cfg_auto.suit_preview, "set")
	-- self.suit_limit_cfg = suit_cfg_auto.limit --装备条件限制配置

	-- self.strong_mark_list = {}
end

------------------------------------------------------REMIND_START----------------------------------------------------
-- function EquipmentWGData:GetSuitRemindByIndex(index)
-- 	local fun_is_open = FunOpen.Instance:GetFunIsOpened("equipment_suit")
-- 	if not fun_is_open then
-- 		return 0
-- 	end

-- 	local equment_data = self:GetSuitDataListData((index % 10) - 1)

-- 	self.strong_mark_list[index] = 0
-- 	for k, v in pairs(equment_data) do
-- 		if self:GetSuitCanUpLevelByData(v) then
-- 			self.strong_mark_list[index] = 1
-- 			break
-- 		end
-- 	end

-- 	local strong_key = 0
-- 	for k,v in pairs(self.strong_mark_list) do
-- 		if v == 1 then
-- 			strong_key = k
-- 			break
-- 		end
-- 	end

-- 	if strong_key > 0 then
-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Tao, 1, function ()
-- 					FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, strong_key)
-- 					return 1
-- 				end)
-- 	else
-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Tao, 0)
-- 	end


-- 	return self.strong_mark_list[index]
-- end

function EquipmentWGData:GetEquipSuitRemind()
	local fun_is_open = FunOpen.Instance:GetFunIsOpened("equipment_suit")
	if not fun_is_open then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Tao, 0)
		return 0
	end

	local total_equip_body_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()

	if not IsEmptyTable(total_equip_body_list) then
		for k, v in pairs(total_equip_body_list) do
			if self:GetSuitEquipBodyRemind(v.seq) > 0 then
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Tao, 1, function ()
					FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_suit)
					return 1
				end)

				return 1
			end
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Equip_Tao, 0)
	return 0
end

function EquipmentWGData:GetSuitEquipBodyRemind(equip_body_seq)
	local data_list = self:GetSuitEquipBodyEquipDataList(equip_body_seq)

	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if self:GetSuitEquipBodyEquipRemind(equip_body_seq, v.index) > 0 then
				return 1
			end
		end
	end

	return 0
end

function EquipmentWGData:GetSuitEquipBodyEquipRemind(equip_body_seq, equip_body_index)
	local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(equip_body_index)
	local dz_flag = self:GetEquipSuitOrderFlag(equip_body_seq, equip_part)

	if dz_flag == 1 then
		return 0
	end

	local equip_active_suit_limit_cfg = self:GetEquipSuitEquipActiveCfg(equip_body_seq)

	if not IsEmptyTable(equip_active_suit_limit_cfg) then
		local equip_data = EquipWGData.Instance:GetGridData(equip_body_index)

		if not IsEmptyTable(equip_data) then
			local suit_act_cfg = EquipmentWGData.Instance:GetEquipmenSuitEquipActCfg(equip_body_seq, equip_part)

			local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
			local target_color = equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL and equip_active_suit_limit_cfg.color or equip_active_suit_limit_cfg.xianqi_color
			local target_star = equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL and equip_active_suit_limit_cfg.star_num or equip_active_suit_limit_cfg.xianqi_star_num

			local _, cur_equip_color = ItemWGData.Instance:GetItemColor(equip_data.item_id)
			local cur_star = equip_data.param.star_level or 0

			if cur_equip_color >= target_color and cur_star >= target_star then
				local stuff_enough = true

				for i = 1, 3 do
					local need_num = tonumber(suit_act_cfg["stuff_".. i .."_num"]) or 0
					local need_item_id = tonumber(suit_act_cfg["stuff_".. i .."_id"]) or 0

					if need_num > 0 and need_item_id > 0 then
						local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)

						if stuff_item_num < need_num then
							local _,gift_stuff_num = ItemWGData.Instance:GetOptionalGiftItem(need_item_id)

							if stuff_item_num + gift_stuff_num >= need_num then

							else
								stuff_enough = false
								break
							end

							-- stuff_enough = false
						end
					end
				end

				if stuff_enough then
					return 1
				end
			end
		end
	end

	return 0
end
------------------------------------------------------REMIND_END-----------------------------------------------------

------------------------------------------------------PROTOCOL_START-------------------------------------------------
function EquipmentWGData:SetSuitAllInfo(protocol)					--激活到第几件
	-- self:InitEquipmentSuitCfg()
	-- self.equip_suit_type_list = protocol.part_is_suit  -- 
	-- self.suit_part_order = protocol.part_order         --

	self.order_flag_list = protocol.order_flag_list                                  -- 25 equip_body  bit flag
	self.active_total_equip_strength_lv = protocol.active_total_equip_strength_lv    -- 25 equip_body
	self.active_total_xianqi_strength_lv = protocol.active_total_xianqi_strength_lv  -- 25 equip_body
	self.active_total_equip_upquality_lv = protocol.active_total_equip_upquality_lv
	self.active_total_xianqi_upquality_lv = protocol.active_total_xianqi_upquality_lv

	-- self:CalcSameOrderCount()
end

-- function EquipmentWGData:InitEquipmentSuitCfg()
-- 	if not self.suit_single_act_sex or not self.suit_stone_attr then
-- 		local role_sex = RoleWGData.Instance:GetRoleSex()
-- 		self.suit_single_act_sex = self.one_equip_act[role_sex] or {}
-- 		self.suit_stone_attr = self.suit_stone_attr_cfg[role_sex] or {}
-- 	end
-- end

-- 套装装备激活标记  equip_body_seq 装备肉身  equip_part 装备部位
function EquipmentWGData:GetEquipSuitOrderFlag(equip_body_seq, equip_part)
	return (self.order_flag_list[equip_body_seq] or {})[equip_part] or 0
end

function EquipmentWGData:GetEquipSuitOrderFlagByIndex(equip_body_index)
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	return self:GetEquipSuitOrderFlag(equip_body_seq, equip_part)
end

function EquipmentWGData:GetEquipStrengthLV(equip_body_seq)
	return self.active_total_equip_strength_lv[equip_body_seq] or 0
end

function EquipmentWGData:GetXianQiStrengthLV(equip_body_seq)
	return self.active_total_xianqi_strength_lv[equip_body_seq] or 0
end

function EquipmentWGData:GetXianQiStrengthLVData()
	return self.active_total_xianqi_strength_lv
end

function EquipmentWGData:GetEquipUpQualityLV()
	return self.active_total_equip_upquality_lv or 0
end

function EquipmentWGData:GetXianQiUpQualityLV()
	return self.active_total_xianqi_upquality_lv or 0
end
------------------------------------------------------PROTOCOL_END---------------------------------------------------

------------------------------------------------------CFG_GET_START-------------------------------------------------
-- function EquipmentWGData:GetEquipmenSuitActivation(suit_index)
-- 	return self.suit_limit_cfg[suit_index + 1]
-- end

function EquipmentWGData:GetEquipSuitEquipActiveCfg(equip_body_seq)
	return self.suit_equip_active_limit_cfg[equip_body_seq]
end

-- 单个装备激活配置 equip_part 装备部位 0 - 9
function EquipmentWGData:GetEquipmenSuitEquipActCfg(equip_body_seq, equip_part)
	local role_sex = RoleWGData.Instance:GetRoleSex()
	return (((self.single_equip_active_cfg or {})[role_sex] or {})[equip_body_seq] or {})[equip_part] or {}
end

function EquipmentWGData:GetXianQiEquipSuitAttrListCfg(equip_body_seq)
	return self.xianqi_equip_suit_attr_cfg[equip_body_seq]
end

function EquipmentWGData:GetXianQiEquipSuitAttrCfg(equip_body_seq, same_order_num)
	return (self.xianqi_equip_suit_attr_cfg[equip_body_seq] or {})[same_order_num]
end

function EquipmentWGData:GetNormalEquipSuitAttrListCfg(equip_body_seq)
	local role_sex = RoleWGData.Instance:GetRoleSex()
	return (self.equip_suit_attr_cfg[role_sex] or {})[equip_body_seq]
end

function EquipmentWGData:GetNormalEquipSuitAttrCfg(equip_body_seq, same_order_num)
	local role_sex = RoleWGData.Instance:GetRoleSex()
	return ((self.equip_suit_attr_cfg[role_sex] or {})[equip_body_seq] or {})[same_order_num]
end

--获取装备名字
function EquipmentWGData:GetEquipmenSuitTitleName(equip_body_seq, equip_type)
	local title_name = ""

	if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		title_name = Language.Equip.XianQiSuit
	else
		local suit_name_show_cfg = self:GetNormalEquipSuitShowNameCfg(equip_body_seq)
		title_name = suit_name_show_cfg and suit_name_show_cfg.suit_name or ""
	end

	return title_name
end

function EquipmentWGData:GetNormalEquipSuitShowNameCfg(equip_body_seq)
	return self.suit_name_show_cfg[equip_body_seq]
end

--获取装备属性
function EquipmentWGData:GetEquipmenSuitStoneAttr(item_cfg, equip_body_index, is_get_browse_data)
	if item_cfg == nil or equip_body_index == nil then
		return {}
	end

	local cur_cfg
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)

	if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		cur_cfg = self:GetXianQiEquipSuitAttrListCfg(equip_body_seq)
	else
		cur_cfg = self:GetNormalEquipSuitAttrListCfg(equip_body_seq)
	end

	if not cur_cfg then
		return {}
	end

	local active_suit_equip_num = is_get_browse_data and BrowseWGData.Instance:GetDZSuitDataInfo(equip_type) or self:GetSuitActiveSameEquipNum(equip_body_seq, equip_type)

	local data_list = {}
	for k, v in pairs(cur_cfg) do
		local seq = #data_list + 1
		data_list[seq] = {}
		data_list[seq].same_order_num = v.same_order_num
		data_list[seq].is_open = v.same_order_num <= active_suit_equip_num
		data_list[seq].attr_list = {}
		local tem_index = 1
		local attr_list = AttributeMgr.GetAttributteValueByClass(v)
		for name, value in pairs(attr_list) do
			data_list[seq].attr_list[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
			tem_index = tem_index + 1
		end

		if not IsEmptyTable(data_list[seq].attr_list) then
			table.sort(data_list[seq].attr_list, SortTools.KeyLowerSorter("sort_index"))
		end
	end

	if not IsEmptyTable(data_list) then
		table.sort(data_list, SortTools.KeyLowerSorter("same_order_num"))
	end

	return data_list

	-- local suit_order_info = nil
	-- if is_get_browse_data then
	-- 	suit_order_info = BrowseWGData.Instance:GetSuitOrderListByData(suit_index, equip_type, order)
	-- else
	-- 	suit_order_info = self:GetSuitOrderListByData(suit_index, equip_type, order)
	-- end

	-- local act_suit_list = suit_order_info and suit_order_info.act_suit_list or {}
	-- local data_list = {}

	-- for k, v in pairs(cur_cfg) do
	-- 	local seq = #data_list + 1
	-- 	data_list[seq] = {}
	-- 	data_list[seq].same_order_num = v.same_order_num
	-- 	data_list[seq].is_open = act_suit_list[v.same_order_num]
	-- 	data_list[seq].attr_list = {}
	-- 	local tem_index = 1
	-- 	local attr_list = AttributeMgr.GetAttributteValueByClass(v)
	-- 	for name, value in pairs(attr_list) do
	-- 		data_list[seq].attr_list[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
	-- 		tem_index = tem_index + 1
	-- 	end

	-- 	if not IsEmptyTable(data_list[seq].attr_list) then
	-- 		table.sort(data_list[seq].attr_list, SortTools.KeyLowerSorter("sort_index"))
	-- 	end
	-- end

	-- if not IsEmptyTable(data_list) then
	-- 	table.sort(data_list, SortTools.KeyLowerSorter("same_order_num"))
	-- end

	-- return data_list
end

function EquipmentWGData:GetSuitGiftItemCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k, v in pairs(self.suit_gift_cfg) do
		if v.open_day <= open_day and open_day <= v.end_day then
			return v
		end
	end

	return {}
end
------------------------------------------------------CFG_GET_END---------------------------------------------------

------------------------------------------------------CAL_START-------------------------------------------------
--获取装备数据
function EquipmentWGData:GetSuitEquipBodyEquipDataList(equip_body_seq, not_need_sort)
	local data_list = __TableCopy(EquipWGData.Instance:GetDataList())

	local suit_list = {}
	if IsEmptyTable(data_list) then
		return suit_list
	end

	local start_equip_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    local end_equip_body_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + GameEnum.EQUIP_INDEX_XIANZHUO

	for k, v in pairs(data_list) do
		if v.item_id > 0 and v.index >= start_equip_index and v.index <= end_equip_body_index then
			local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(v.index)
			v.item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)			--装备信息配置
			suit_list[equip_part] = v

			-- v.item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)			--装备信息配置
			-- v.suit_index = 0 										         --点击的套装id  取消了三个套装 当前只有一个套装 0
			-- v.suit_open_level = self:GetSuitInfoByPartType(v.index)			--这个部位的等级（当前什么套）
			-- v.is_activation = self:CalEquipmenSuitActivation(equip_body_seq, v)				--按钮显示
			-- v.suit_open_num = self:GetEquipmenSuitOpenNum(v)				--显示等级的激活数量
			-- v.is_suit_open = v.suit_index < v.suit_open_level
			-- local item_oder = v.item_cfg and v.item_cfg.order or 0
			-- v.suit_item_cfg = self:GetEquipmenSuitItemCfg(v.suit_index, item_oder, v.index)
			-- v.complete_suit_num = self:GetEquipmenSuitALLNum(v.index)
			-- suit_list[v.index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM] = v
			-- table.insert(suit_list, v)
		end
	end

	return suit_list
end

-- function EquipmentWGData:CalEquipmenSuitActivation(equip_body_seq, equip_data)
-- 	local equip_part = ItemWGData.GetEquipPartByItemId(equip_data.item_id)
-- 	local active_flag = self:GetEquipSuitOrderFlag(equip_body_seq, equip_part)
	
-- 	-- 已经激活
-- 	if active_flag == 1 then
-- 		return EquipmentWGData.EQUIP_ACT_TYPE.IS_ACYIVE
-- 	end
	
-- 	local item_cfg = equip_data.item_cfg
-- 	local limit_cfg = self:GetEquipSuitEquipActiveCfg(equip_body_seq)
-- 	local legend_num = equip_data.param and equip_data.param.star_level or 0

-- 	-- 阶数或者星级不足
-- 	if EquipmentWGData.GetEquipSuitTypeByPartType(equip_part) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
-- 		if item_cfg.color < limit_cfg.xianqi_color or legend_num < limit_cfg.xianqi_star_num then
-- 			return EquipmentWGData.EQUIP_ACT_TYPE.RANK_DEFICIENT
-- 		end
-- 	else
-- 		if item_cfg.color < limit_cfg.color or legend_num < limit_cfg.star_num then
-- 			return EquipmentWGData.EQUIP_ACT_TYPE.RANK_DEFICIENT
-- 		end
-- 	end

-- 	-- 可激活
-- 	return EquipmentWGData.EQUIP_ACT_TYPE.CAN_ACTIVE
-- end

function EquipmentWGData:GetSuitCanUpLevelByData(data)
	if data == nil then
		return false, 0
	end

	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(data.index)
	local active_flag = self:GetEquipSuitOrderFlag(equip_body_seq, equip_part)
	
	-- 已经激活
	if active_flag == 1 then
		return false, 0
	end

	local item_cfg = data.item_cfg
	local limit_cfg = self:GetEquipSuitEquipActiveCfg(equip_body_seq)
	local legend_num = data.param and data.param.star_level or 0

	-- 阶数或者星级不足
	if EquipmentWGData.GetEquipSuitTypeByPartType(equip_part) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		if item_cfg.color < limit_cfg.xianqi_color or legend_num < limit_cfg.xianqi_star_num then
			return false, 0
		end
	else
		if item_cfg.color < limit_cfg.color or legend_num < limit_cfg.star_num then
			return false, 0
		end
	end

	local suit_item_cfg = self:GetEquipmenSuitEquipActCfg(equip_body_seq, equip_part)
	if not IsEmptyTable(suit_item_cfg) then
		local is_ok = true
		local stuff_id = 0
		local need_gift_num = 0
		local need_stuff_num = 0
		for k = 1, 3 do
			stuff_id = suit_item_cfg["stuff_".. k .."_id"]
			need_stuff_num = suit_item_cfg["stuff_".. k .."_num"]
			if stuff_id and stuff_id > 0 and is_ok then
				local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
				if stuff_item_num < need_stuff_num then
					local _,gift_stuff_num = ItemWGData.Instance:GetOptionalGiftItem(stuff_id)
					if stuff_item_num + gift_stuff_num >= need_stuff_num then
						need_gift_num = need_gift_num + need_stuff_num - stuff_item_num
					else
						is_ok = false
						break
					end
				end
			end
		end

		if is_ok then
			return true, need_gift_num
		end
	end

	return false, 0
	
	-- if data.is_activation == EquipmentWGData.EQUIP_ACT_TYPE.CAN_ACTIVE and data.suit_item_cfg ~= nil then
	-- 	local suit_item_cfg = data.suit_item_cfg
	-- 	local is_ok = true
	-- 	local stuff_id = 0
	-- 	local need_gift_num = 0
	-- 	local need_stuff_num = 0
	-- 	for k = 1, 3 do
	-- 		stuff_id = suit_item_cfg["stuff_".. k .."_id"]
	-- 		need_stuff_num = suit_item_cfg["stuff_".. k .."_num"]
	-- 		if stuff_id and stuff_id > 0 and is_ok then
	-- 			local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	-- 			if stuff_item_num < need_stuff_num then
	-- 				local _,gift_stuff_num = ItemWGData.Instance:GetOptionalGiftItem(stuff_id)
	-- 				if stuff_item_num + gift_stuff_num >= need_stuff_num then
	-- 					need_gift_num = need_gift_num + need_stuff_num - stuff_item_num
	-- 				else
	-- 					is_ok = false
	-- 					break
	-- 				end
	-- 			end
	-- 		end
	-- 	end

	-- 	if is_ok then
	-- 		return true, need_gift_num
	-- 	end
	-- end
	-- return false, 0
end

--获取装备属性  GetEquipmenSuitStoneAttr
function EquipmentWGData:GetEquipmenSuitAttrCfg(equip_body_seq, equip_part, equip_type)
	local target_equip_type = equip_type or EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
	local cur_cfg = {}

	if target_equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		cur_cfg = self:GetXianQiEquipSuitAttrListCfg(equip_body_seq)
	else
		cur_cfg = self:GetNormalEquipSuitAttrListCfg(equip_body_seq)
	end

	if IsEmptyTable(cur_cfg) then
		return cur_cfg
	end

	local cur_active_same_equip_num = self:GetSuitActiveSameEquipNum(equip_body_seq, target_equip_type)

	local data_list = {}
	for k, v in pairs(cur_cfg) do
		local index = #data_list + 1
		data_list[index] = {}
		data_list[index].same_order_num = v.same_order_num
		data_list[index].is_open = v.same_order_num <= cur_active_same_equip_num
		data_list[index].attr_list = {}
		local tem_index = 1

		local attr_list = AttributeMgr.GetAttributteValueByClass(v)
		for name, value in pairs(attr_list) do
			data_list[index].attr_list[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
			tem_index = tem_index + 1
		end

		if not IsEmptyTable(data_list[index].attr_list) then
			table.sort(data_list[index].attr_list, SortTools.KeyLowerSorter("sort_index"))
		end
	end

	if not IsEmptyTable(data_list) then
		table.sort(data_list, SortTools.KeyLowerSorter("same_order_num"))
	end

	return data_list

	-- local cur_cfg
	-- local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
	-- if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 	cur_cfg = self.xianqi_suit_attr[suit_index]
	-- else
	-- 	cur_cfg = self.suit_stone_attr[suit_index] and self.suit_stone_attr[suit_index][order]
	-- end

	-- if not cur_cfg then
	-- 	return {}
	-- end

	-- local suit_order_info = nil
	-- if is_get_browse_data then
	-- 	suit_order_info = BrowseWGData.Instance:GetSuitOrderListByData(suit_index, equip_type, order)
	-- else
	-- 	suit_order_info = self:GetSuitOrderListByData(suit_index, equip_type, order)
	-- end

	-- local act_suit_list = suit_order_info and suit_order_info.act_suit_list or {}
	-- local data_list = {}

	-- for k, v in pairs(cur_cfg) do
	-- 	local seq = #data_list + 1
	-- 	data_list[seq] = {}
	-- 	data_list[seq].same_order_num = v.same_order_num
	-- 	data_list[seq].is_open = act_suit_list[v.same_order_num]
	-- 	data_list[seq].attr_list = {}
	-- 	local tem_index = 1
	-- 	local attr_list = AttributeMgr.GetAttributteValueByClass(v)
	-- 	for name, value in pairs(attr_list) do
	-- 		data_list[seq].attr_list[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
	-- 		tem_index = tem_index + 1
	-- 	end

	-- 	if not IsEmptyTable(data_list[seq].attr_list) then
	-- 		table.sort(data_list[seq].attr_list, SortTools.KeyLowerSorter("sort_index"))
	-- 	end
	-- end

	-- if not IsEmptyTable(data_list) then
	-- 	table.sort(data_list, SortTools.KeyLowerSorter("same_order_num"))
	-- end

	-- return data_list
end

-- 获取 普通/仙器 已经激活的的装备数量    equip_type   GameEnum.EQUIP_BIG_TYPE_XIANQI/GameEnum.EQUIP_BIG_TYPE_NORMAL
function EquipmentWGData:GetSuitActiveSameEquipNum(equip_body_seq, equip_type)
	local active_num = 0
	local equip_data_list = EquipWGData.Instance:GetDataList()

	if IsEmptyTable(equip_data_list) then
		return active_num
	end

	local start_equip_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    local end_equip_body_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + GameEnum.EQUIP_INDEX_XIANZHUO

	for k, v in pairs(equip_data_list) do
		if v.item_id > 0 and v.index >= start_equip_index and v.index <= end_equip_body_index then
			local equip_part = ItemWGData.GetEquipPartByItemId(v.item_id)
			local cur_equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)

			if cur_equip_type == equip_type then
				local flag = self:GetEquipSuitOrderFlag(equip_body_seq, equip_part)

				if flag == 1 then
					active_num = active_num + 1
				end
			end
		end
	end

	return active_num
end

function EquipmentWGData:GetEquipmenSuitAllActiveNum(equip_type)
	if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
		return GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT
	elseif equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		return GameEnum.EQUIP_BIG_TYPE_XIANQI_COUNT
	end

	return 0
end

-- 套装总览数据
function EquipmentWGData:GetToTalEquipSuitZongLangData(equip_body_seq)
	local normal_equip_zonglan_data, xianqi_equip_zonglan_data, cap = {}, {}, 0
	local all_attr = AttributePool.AllocDirtyAttribute()

	local normal_active_equip_num = self:GetSuitActiveSameEquipNum(equip_body_seq, GameEnum.EQUIP_BIG_TYPE_NORMAL)
	local normal_equip_cfg_list = self:GetNormalEquipSuitAttrListCfg(equip_body_seq)

	local nor_index = 1
	for k, v in pairs(normal_equip_cfg_list) do
		local data = {}
		data.cfg = v
		data.equip_body_seq = equip_body_seq
		data.equip_type = GameEnum.EQUIP_BIG_TYPE_NORMAL
		data.active_num = normal_active_equip_num
		data.active = v.same_order_num <= normal_active_equip_num

		if data.active then
			all_attr = AttributeMgr.AddAttributeAttr(all_attr, v)
		end

		normal_equip_zonglan_data[nor_index] = data
		nor_index = nor_index + 1
	end

	-- all_attr = AttributeMgr.AddAttributeAttr(all_attr, attr)

	local xianqi_active_equip_num = self:GetSuitActiveSameEquipNum(equip_body_seq, GameEnum.EQUIP_BIG_TYPE_XIANQI)
	local xianqi_equip_cfg_list = self:GetXianQiEquipSuitAttrListCfg(equip_body_seq)

	local xianqi_index = 1
	for k, v in pairs(xianqi_equip_cfg_list) do
		local data = {}
		data.cfg = v
		data.equip_body_seq = equip_body_seq
		data.equip_type = GameEnum.EQUIP_BIG_TYPE_XIANQI
		data.active_num = xianqi_active_equip_num
		data.active = v.same_order_num <= xianqi_active_equip_num

		if data.active then
			all_attr = AttributeMgr.AddAttributeAttr(all_attr, v)
		end

		xianqi_equip_zonglan_data[xianqi_index] = data
		xianqi_index = xianqi_index + 1
	end

	cap = AttributeMgr.GetCapability(all_attr)
	return normal_equip_zonglan_data, xianqi_equip_zonglan_data, cap
end

--判断切换装备是否打开返还材料的提示  @true 需要打开  @false 不需要打开
--注意：1、此时身上的该部位已经是套装装备
function EquipmentWGData:CheckIsLimitTips(item_id, item_index)
	local i_cfg = ItemWGData.Instance:GetItemConfig(item_id) --要穿戴的装备配置
    local i_data = ItemWGData.Instance:GetGridData(item_index) --当前穿戴的装备数据 i_data.param.star_level  背包中的数据
	if nil == i_cfg or nil == i_data then
		return true
	end

	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(i_cfg)
	-- 穿戴的数据
	local e_data = EquipWGData.Instance:GetGridData(equip_body_index)
	if IsEmptyTable(e_data) then
		return true
	end

	local e_cfg = ItemWGData.Instance:GetItemConfig(e_data.item_id) 	--身上穿戴的装备配置
	if nil == e_cfg then
		return true
	end

	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	
	-- 要穿戴的装备不满足继承条件需要弹窗
	local limit_cfg = self:GetEquipSuitEquipActiveCfg(equip_body_seq)
	local legend_num = i_data.param and i_data.param.star_level or 0
	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)

	if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
		if i_cfg.color < limit_cfg.xianqi_color or legend_num < limit_cfg.xianqi_star_num then
			return true
		end
	else
		if i_cfg.color < limit_cfg.color or legend_num < limit_cfg.star_num then
			return true
		end
	end

	return false

	-- local e_data = EquipWGData.Instance:GetGridData(EquipWGData.Instance:GetEquipIndexByType(i_cfg.sub_type)) --穿戴的装备数据

	-- local e_cfg = ItemWGData.Instance:GetItemConfig(e_data.item_id) 	--身上穿戴的装备配置
	-- if nil == e_cfg then
	-- 	return true
	-- end

	-- --1、该装备能否被锻造成完美/史诗装备
	-- local equip_index = EquipWGData.Instance:GetEquipIndexByType(i_cfg.sub_type) --要穿戴的部位
	-- local suit_index = self:GetSuitInfoByPartType(equip_index)

	-- local limit_cfg = self:GetEquipmenSuitActivation(suit_index)
	-- local legend_num = i_data.param and i_data.param.star_level or 0

	-- --2、排除仙器套装
	-- if EquipmentWGData.GetEquipSuitTypeByPartType(equip_index) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 	--1、该装备是否能继承当前的套装： 判断装备阶数是否 ~= 当前当前穿戴的装备的阶数 true表示不能继承
	-- 	if i_cfg.order ~= e_cfg.order then
	-- 		return true
	-- 	end
	-- else
	-- 	if i_cfg.order < limit_cfg.min_order or i_cfg.color < limit_cfg.color or legend_num < limit_cfg.star_num then 	--阶数 颜色 传奇属性要求
	-- 		return true
	-- 	end
	-- end

	-- return false
end

--判断对应的部位是否是套装
--要装备的装备id，该装备在格子中的位置
function EquipmentWGData:CheckIsActiveSuitEquip(item_id)
	local i_cfg = ItemWGData.Instance:GetItemConfig(item_id) --要穿戴的装备配置
	if nil == i_cfg then
		return false
	end

	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(i_cfg)
	local active_suit_flag = self:GetEquipSuitOrderFlagByIndex(equip_body_index)
	return active_suit_flag > 0
end

function EquipmentWGData:GetStrengthActiveLevelChange(equip_body_seq)
	local active_data = self:GetXianQiStrengthLVData()

	if nil == self.old_active_total_equip_strength_lv then
		self.old_active_total_equip_strength_lv = active_data
		return false
	end

	local level_change = false
	if active_data then
        for k, v in pairs(active_data) do
            local old_lv = self.old_active_total_equip_strength_lv[k]

            if old_lv and old_lv < v then
                level_change = true 
				break
            end
        end
    end

	self.old_active_total_equip_strength_lv = active_data

	return level_change
end

function EquipmentWGData:GetUpQualityActiveLevelChange()
	if nil == self.old_active_total_equip_upquality_lv then
		self.old_active_total_equip_upquality_lv = self.active_total_equip_upquality_lv or 0
	end

	if nil == self.old_active_total_xianqi_upquality_lv then
		self.old_active_total_xianqi_upquality_lv = self.active_total_xianqi_upquality_lv or 0
	end

	if (self.active_total_equip_upquality_lv and self.old_active_total_equip_upquality_lv < self.active_total_equip_upquality_lv) or (self.active_total_xianqi_upquality_lv and self.old_active_total_xianqi_upquality_lv < self.active_total_xianqi_upquality_lv) then
		self.old_active_total_equip_upquality_lv = self.active_total_equip_upquality_lv
		self.old_active_total_xianqi_upquality_lv = self.active_total_xianqi_upquality_lv
		return true
	end

	return false
end
------------------------------------------------------CAL_END---------------------------------------------------

-- --获取装备是否可以激活
-- function EquipmentWGData:IsEquipmenSuitActivation(equip_data)
-- 	local limit_cfg = self:GetEquipmenSuitActivation(equip_data.suit_index)
-- 	local item_cfg = equip_data.item_cfg
-- 	local legend_num = equip_data.param and equip_data.param.star_level or 0
-- 	local part = ItemWGData.GetEquipPartByItemId(equip_data.item_id)   -- 部位

-- 	--仙器装备
-- 	if EquipmentWGData.GetEquipSuitTypeByPartType(part) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
-- 		if item_cfg.order < limit_cfg.xianqi_min_order
-- 			or item_cfg.color < limit_cfg.xianqi_color
-- 			or legend_num < limit_cfg.xianqi_star_num then 	--阶数 颜色 星级
-- 			return EquipmentWGData.ACTIVATION_TYPE.RANK_DEFICIENT
-- 		end
-- 	else
-- 		if item_cfg.order < limit_cfg.min_order
-- 		or item_cfg.color < limit_cfg.color
-- 		or legend_num < limit_cfg.star_num then 	--阶数 颜色 传奇属性要求
-- 			return EquipmentWGData.ACTIVATION_TYPE.RANK_DEFICIENT
-- 		end
-- 	end

-- 	if equip_data.suit_index == equip_data.suit_open_level + 1 then
-- 		return EquipmentWGData.ACTIVATION_TYPE.OK
-- 	elseif equip_data.suit_index < equip_data.suit_open_level + 1 then
-- 		return EquipmentWGData.ACTIVATION_TYPE.BETTER_ACTIVATION
-- 	elseif equip_data.suit_index > equip_data.suit_open_level + 1 then
-- 		return EquipmentWGData.ACTIVATION_TYPE.NOT_BEFORE
-- 	end
-- end

-- function EquipmentWGData:GetEquipmenSuitOpenNum(equip_data)
-- 	if nil == equip_data or nil == equip_data.item_cfg then
-- 		return 0
-- 	end

-- 	local equip_index = EquipWGData.Instance:GetEquipIndexByType(equip_data.item_cfg.sub_type)
-- 	local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_index)

-- 	local order = equip_data.item_cfg.order
-- 	local info = self:GetSuitOrderListByData(equip_data.suit_index, equip_type, order)
-- 	return info and info.count or 0
-- end

-- function EquipmentWGData:GetSuitXianQiAttrCfg(suit_index, same_order_num)
-- 	return self.xianqi_suit_attr[suit_index] and self.xianqi_suit_attr[suit_index][same_order_num]
-- end

-- function EquipmentWGData:SetJumpSuitPreviewTab(suit_order, equip_part)
-- 	local suit_set = 0
-- 	if self.suit_preview[suit_order] then
-- 		for k,v in ipairs(self.suit_preview[suit_order]) do
-- 			if v.suit_part == equip_part then
-- 				suit_set = v.set
-- 			end
-- 		end
-- 	end

-- 	local set_list = self:GetSuitPreviewTopTab()
-- 	for k,v in ipairs(set_list) do
-- 		if v == suit_set then
-- 			self.top_btn_index = k
-- 		end
-- 	end

-- 	local btn_list = self:GetSuitPreviewTab()
-- 	for k,v in ipairs(btn_list) do
-- 		if v == suit_order then
-- 			self.btn_index = k
-- 		end
-- 	end
-- end

--获取装备数据
--idx == 1 : 防具，装备索引从 0 1 2 3 4 5 7
--idx == 2 : 仙器，装备索引从 6 8 9
--idx == nil : 获取所有装备数据
-- function EquipmentWGData:GetSuitDataListData(suit_index, idx)
-- 	if nil == suit_index then
-- 		return
-- 	end

-- 	local data_list = __TableCopy(EquipWGData.Instance:GetDataList()) --获取角色身上的装备
-- 	local suit_list = {}
-- 	if not data_list or IsEmptyTable(data_list) then
-- 		return suit_list
-- 	end

-- 	local not_need_sort = idx == nil
-- 	for k,v in pairs(data_list) do
-- 		if v.item_id > 0 and v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
-- 			v.suit_index = suit_index 										--点击的套装id
-- 			v.suit_open_level = self:GetSuitInfoByPartType(v.index)			--这个部位的等级（当前什么套）
-- 			v.item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)			--装备信息配置
-- 			v.is_activation = self:IsEquipmenSuitActivation(v)				--按钮显示
-- 			v.suit_open_num = self:GetEquipmenSuitOpenNum(v)				--显示等级的激活数量
-- 			v.is_suit_open = suit_index < v.suit_open_level
-- 			local item_oder = v.item_cfg and v.item_cfg.order or 0
-- 			v.suit_item_cfg = self:GetEquipmenSuitItemCfg(suit_index, item_oder, v.index)
-- 			v.complete_suit_num = self:GetEquipmenSuitALLNum(v.index)

-- 			--排序规则指定
-- 			if not not_need_sort then
-- 				v.sort_index = -1
-- 				local can_up = false
-- 				if v.is_activation == EquipmentWGData.ACTIVATION_TYPE.OK then					 -- 可锻造
-- 					v.sort_index = EquipmentWGData.GetSortIndex(v.index) * 1222
-- 					if self:GetSuitCanUpLevelByData(v) then
-- 						v.sort_index = EquipmentWGData.GetSortIndex(v.index) * 111
-- 					end
-- 				elseif v.is_activation == EquipmentWGData.ACTIVATION_TYPE.BETTER_ACTIVATION or
-- 					v.is_activation == EquipmentWGData.ACTIVATION_TYPE.ACTIVATED then			 -- 已锻造
-- 					v.sort_index = EquipmentWGData.GetSortIndex(v.index) * 13333

-- 				elseif v.is_activation == EquipmentWGData.ACTIVATION_TYPE.NOT_BEFORE then		 -- 需先锻造xx装备
-- 					v.sort_index = EquipmentWGData.GetSortIndex(v.index) * 144444

-- 				elseif v.is_activation == EquipmentWGData.ACTIVATION_TYPE.RANK_DEFICIENT then	 --阶数不足
-- 					v.sort_index = EquipmentWGData.GetSortIndex(v.index) * 1555555
-- 				end
-- 			end
-- 		end
-- 	end

-- 	for i, v in pairs(data_list) do
-- 		if idx == 1 and EquipmentWGData.GetEquipSuitTypeByPartType(v.index) == GameEnum.EQUIP_BIG_TYPE_NORMAL then --普通装备
-- 			table.insert(suit_list, v)
-- 		elseif idx == 2 and EquipmentWGData.GetEquipSuitTypeByPartType(v.index) == GameEnum.EQUIP_BIG_TYPE_XIANQI then --仙器装备
-- 			table.insert(suit_list, v)
-- 		elseif idx == nil then
-- 			suit_list[i + 1] = v
-- 		end
-- 	end

-- 	--不需要排序
-- 	if not_need_sort then
-- 		return suit_list
-- 	end

-- 	table.sort(suit_list, function(a, b)
-- 		if nil == a or nil == b or a == b then
-- 			return false
-- 		end
-- 		return a.sort_index < b.sort_index
-- 	end )

-- 	return suit_list
-- end

-- function EquipmentWGData:GetSuitOrderBySuitAndPart(part)
-- 	return self.suit_part_order and self.suit_part_order[part] or 0
-- end

-- 当前激活的是什么套   
-- function EquipmentWGData:GetSuitInfoByPartType(part_type)
-- 	return self.equip_suit_type_list and self.equip_suit_type_list[part_type] or -1
-- end

-- 统计不同装备类型 已锻造 满足阶数数量 以及激活状态
-- function EquipmentWGData:CalcSameOrderCount()
-- 	self.suit_order_list = {}

-- 	-- 初始化
-- 	for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 		self.suit_order_list[suit] = {}
-- 		for equip_type = 0, GameEnum.EQUIP_BIG_TYPE_XIANQI do
-- 			self.suit_order_list[suit][equip_type] = {}
-- 			for order = 0, COMMON_CONSTS.EQUIP_MAX_ORDER do
-- 				self.suit_order_list[suit][equip_type][order] = {count = 0, act_suit_list = {}}
-- 			end
-- 		end
-- 	end

-- 	-- for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 	-- 	print_error("套装：", suit, "----普通初始化---", self.suit_order_list[suit][0])
-- 	-- 	print_error("套装：", suit, "----仙器初始化---", self.suit_order_list[suit][1])
-- 	-- end

-- 	-- 阶数倒序
-- 	local order_list = {}
-- 	for k, v in pairs(self.suit_part_order) do
-- 		local cur_suit = self:GetSuitInfoByPartType(k)
-- 		local data = {part = k, order = v, cur_suit = cur_suit}
-- 		table.insert(order_list, data)
-- 	end
-- 	table.sort(order_list, SortTools.KeyUpperSorter("order"))

-- 	-- 已锻造的阶数计数
-- 	for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 		for part = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
-- 			local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part)
-- 			local cur_order = self:GetSuitOrderBySuitAndPart(part)
-- 			local cur_suit = self:GetSuitInfoByPartType(part)
-- 			if suit <= cur_suit then
-- 				if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
-- 					local count = self.suit_order_list[suit][equip_type][cur_order].count
-- 					self.suit_order_list[suit][equip_type][cur_order].count = count + 1
-- 				end
-- 			end

-- 			-- 仙器不考虑阶数限制
-- 			if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI and suit == cur_suit and cur_order > 0 then
-- 				for i = suit, 0, -1 do
-- 					for k, v in pairs(self.suit_order_list[i][equip_type]) do
-- 						v.count = v.count + 1
-- 					end
-- 				end
-- 			end
-- 		end
-- 	end

-- 	-- for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 	-- 	print_error("套装：", suit, "----普通 已锻造的阶数计数---", self.suit_order_list[suit][0])
-- 	-- 	print_error("套装：", suit, "----仙器 已锻造的阶数计数---", self.suit_order_list[suit][1])
-- 	-- end

-- 	-- 普通装备  高阶向低阶兼容
-- 	for i = #order_list, 1, -1 do
-- 		local part = order_list[i].part
-- 		local cur_order = order_list[i].order
-- 		local cur_suit = order_list[i].cur_suit
-- 		local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part)
-- 		if cur_order > 0 then
-- 			if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
-- 				for order = cur_order - 1, 1, -1 do
-- 					for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 						local count = self.suit_order_list[suit][equip_type][order].count
-- 						if suit <= cur_suit and count > 0 then
-- 							self.suit_order_list[suit][equip_type][order].count = count + 1
-- 						end
-- 					end
-- 				end
-- 			end
-- 		end
-- 	end

-- 	-- for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 	-- 	print_error("套装：", suit, "----普通 高阶向低阶兼容---", self.suit_order_list[suit][0])
-- 	-- 	print_error("套装：", suit, "----仙器 高阶向低阶兼容---", self.suit_order_list[suit][1])
-- 	-- end

-- 	-- -- 普通激活 套件由高往低激活，只激活一种
-- 	local equip_type = GameEnum.EQUIP_BIG_TYPE_NORMAL
-- 	local cache_normal_act = {}
-- 	for suit = GameEnum.SUIT_TYPE_MAX - 1, 0, -1  do
-- 		local limit_cfg = self:GetEquipmenSuitActivation(suit)
-- 		for order = COMMON_CONSTS.EQUIP_MAX_ORDER, 0, -1 do
-- 			local cur_count = 0
-- 			if order >= limit_cfg.min_order then
-- 				cur_count = self.suit_order_list[suit][equip_type][order].count
-- 			end

-- 			for i = cur_count, 0, -1 do
-- 				local suit_cfg = self.suit_stone_attr[suit]
-- 								and self.suit_stone_attr[suit][order]
-- 								and self.suit_stone_attr[suit][order][i]
-- 				if suit_cfg and not cache_normal_act[i] then
-- 					self.suit_order_list[suit][equip_type][order].act_suit_list[i] = true
-- 					cache_normal_act[i] = true
-- 				end
-- 			end
-- 		end
-- 	end

-- 	-- 仙器激活 不考虑阶数限制  套件由高往低激活，只激活一种
-- 	local cache_xianqi_act = {}
-- 	local equip_type = GameEnum.EQUIP_BIG_TYPE_XIANQI
-- 	-- local limit_cfg = self:GetEquipmenSuitActivation(0)

-- 	for suit = GameEnum.SUIT_TYPE_MAX - 1, 0, -1  do
-- 		for k, v in pairs(self.suit_order_list[suit][equip_type]) do
-- 			local cur_count = v.count
-- 			for i = cur_count, 0, -1 do
-- 				local suit_cfg = self:GetSuitXianQiAttrCfg(suit, i)

-- 				if suit_cfg and ((not cache_xianqi_act[i]) or (cache_xianqi_act[i][suit])) then
-- 					v.act_suit_list[i] = true
-- 					if not cache_xianqi_act[i] then
-- 						cache_xianqi_act[i] = {}
-- 					end
-- 					cache_xianqi_act[i][suit] = true
-- 				end
-- 			end
-- 		end
-- 	end

-- 	-- for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 	-- 	print_error("套装：", suit, "----普通 汇总---", self.suit_order_list[suit][0])
-- 	-- 	print_error("套装：", suit, "----仙器 汇总---", self.suit_order_list[suit][1])
-- 	-- end

-- 	self.equip_suit_zonglang_data = {}
-- 	self.xianqi_suit_zonglang_data = {}
-- 	for suit = 0, GameEnum.SUIT_TYPE_MAX - 1 do
-- 		local equip_list = self.suit_order_list[suit][0]
-- 		for i=0,#equip_list do
-- 			local tab = equip_list[i]
-- 			if tab.count > 0 and tab.act_suit_list and not IsEmptyTable(tab.act_suit_list) then
-- 				for k,v in pairs(tab.act_suit_list) do
-- 					if v == true then
-- 						local data = {}
-- 						data.suit_index = suit
-- 						data.order = i
-- 						data.count = k
-- 						data.equip_type = GameEnum.EQUIP_BIG_TYPE_NORMAL
-- 						table.insert(self.equip_suit_zonglang_data,data)
-- 					end
-- 				end
-- 			end
-- 		end

-- 		local xianqi_list = self.suit_order_list[suit][1]
-- 		local tab = xianqi_list[0]
-- 		if tab.count > 0 and tab.act_suit_list and not IsEmptyTable(tab.act_suit_list) then
-- 			for k,v in pairs(tab.act_suit_list) do
-- 				if v == true then
-- 					local data = {}
-- 					data.suit_index = suit
-- 					--data.order = i
-- 					data.count = k
-- 					data.equip_type = GameEnum.EQUIP_BIG_TYPE_XIANQI
-- 					table.insert(self.xianqi_suit_zonglang_data,data)
-- 				end
-- 			end
-- 		end
-- 	end

-- 	table.sort( self.equip_suit_zonglang_data, SortTools.KeyLowerSorter("count") )
-- 	table.sort( self.xianqi_suit_zonglang_data, SortTools.KeyLowerSorter("count") )
-- end

--获取套装普通装备的总览信息
-- function EquipmentWGData:GetEquipSuitZongLangData()
-- 	return self.equip_suit_zonglang_data or {}
-- end

--获取套装仙器装备的总览信息
-- function EquipmentWGData:GetXianQiSuitZongLangData()
-- 	return self.xianqi_suit_zonglang_data or {}
-- end

-- function EquipmentWGData:GetXianQiSuitZongLangAllCap()
-- 	local all_attr = AttributePool.AllocDirtyAttribute()
-- 	for k,v in pairs(self.equip_suit_zonglang_data) do
-- 		local cfg = self.suit_stone_attr[v.suit_index] and self.suit_stone_attr[v.suit_index][v.order] and self.suit_stone_attr[v.suit_index][v.order][v.count]
-- 		local attr = AttributeMgr.GetAttributteByClass(cfg)
-- 		all_attr = AttributeMgr.AddAttributeAttr(all_attr, attr)
-- 	end
-- 	for k,v in pairs(self.xianqi_suit_zonglang_data) do
-- 		local cfg = self:GetSuitXianQiAttrCfg(v.suit_index,v.count)
-- 		local attr = AttributeMgr.GetAttributteByClass(cfg)
-- 		all_attr = AttributeMgr.AddAttributeAttr(all_attr, attr)
-- 	end
-- 	local cap = AttributeMgr.GetCapability(all_attr)
-- 	return cap
-- end

-- function EquipmentWGData:GetSuitOrderListByData(suit, equip_type, order)
-- 	return self.suit_order_list[suit] and self.suit_order_list[suit][equip_type]
-- 			and self.suit_order_list[suit][equip_type][order]
-- end

-- function EquipmentWGData:GetSuitAllInfo()
-- 	return self.equip_suit_type_list
-- end

-- function EquipmentWGData:GetEquipmenSuitItemCfg(suit_index, equip_order, equip_part)
-- 	local empty_table = {}
-- 	return (((self.suit_single_act_sex or empty_table)[suit_index] or empty_table)[equip_order]
-- 		or empty_table)[equip_part]
-- end

-- function EquipmentWGData:GetSuitStoneAttrCfg()
-- 	return self.suit_stone_attr
-- end

-- function EquipmentWGData:GetSuitStoneAttrCfgBy(suit_index,equip_order,same_order_num)
-- 	local cfg = self.suit_stone_attr[suit_index] and self.suit_stone_attr[suit_index][equip_order] and self.suit_stone_attr[suit_index][equip_order][same_order_num]
-- 	if IsEmptyTable(cfg) then return end
-- 	local attr_list = AttributeMgr.GetAttributteValueByClass(cfg)
-- 	local attr_data = {}
-- 	local tem_index = 1
-- 	for name, value in pairs(attr_list) do
-- 		attr_data[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
-- 		tem_index = tem_index + 1
-- 	end

-- 	if not IsEmptyTable(attr_data) then
-- 		table.sort(attr_data, SortTools.KeyLowerSorter("sort_index"))
-- 	end
-- 	return attr_data
-- end

-- function EquipmentWGData:GetXianQiSuitAttrCfgBy(suit_index,same_order_num)
-- 	local cfg = self:GetSuitXianQiAttrCfg(suit_index,same_order_num)
-- 	if IsEmptyTable(cfg) then return end
-- 	local attr_list = AttributeMgr.GetAttributteValueByClass(cfg)
-- 	local attr_data = {}
-- 	local tem_index = 1
-- 	for name, value in pairs(attr_list) do
-- 		attr_data[tem_index] = {attr_type = name, value = value, sort_index = AttributeMgr.GetSortAttributeIndex(name)}
-- 		tem_index = tem_index + 1
-- 	end

-- 	if not IsEmptyTable(attr_data) then
-- 		table.sort(attr_data, SortTools.KeyLowerSorter("sort_index"))
-- 	end
-- 	return attr_data
-- end

--获取装备名字
-- function EquipmentWGData:GetEquipmenSuitName(equip_order)
-- 	local suit_cfg_auto = ConfigManager.Instance:GetAutoConfig("suit_cfg_auto")
-- 	for k,v in ipairs(suit_cfg_auto.suit_name_show) do
-- 		if v.suit_order == equip_order then
-- 			return v.suit_name
-- 		end
-- 	end
-- 	return ""
-- end

--获取装备类型数量
-- function EquipmentWGData:GetEquipmenSuitALLNum(equip_index)
-- 	if EquipmentWGData.GetEquipSuitTypeByPartType(equip_index) == GameEnum.EQUIP_BIG_TYPE_NORMAL then
-- 		return GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT
-- 	elseif EquipmentWGData.GetEquipSuitTypeByPartType(equip_index) == GameEnum.EQUIP_BIG_TYPE_XIANQI then
-- 		return GameEnum.EQUIP_BIG_TYPE_XIANQI_COUNT
-- 	end

-- 	return 0
-- end


-- --判断对应的部位是否是套装
-- --要装备的装备id，该装备在格子中的位置
-- function EquipmentWGData:CheckIsSuit(item_id)
-- 	local i_cfg = ItemWGData.Instance:GetItemConfig(item_id) --要穿戴的装备配置
-- 	if nil == i_cfg then
-- 		return false
-- 	end

-- 	local equip_index = EquipWGData.Instance:GetEquipIndexByType(i_cfg.sub_type) --要穿戴的部位
-- 	local suit_index = self:GetSuitInfoByPartType(equip_index)
-- 	if suit_index < 0 then --没有激活完美/史诗套装
-- 		return false
-- 	end

-- 	return true
-- end

------------------------------------------功能大改 后续功能重启需要重新处理_START--------------------------------------------------
function EquipmentWGData:GetSuitPreviewTopTab()
	local suit_top_btn = {}
	for k,v in pairs(self.suit_preview_set) do
		table.insert(suit_top_btn, k)
	end

	return suit_top_btn
end

-- 统计套装部位拆解材料数量
function EquipmentWGData:CountSuitPartStuffNum()
	self.suit_had_act_stuff_num_list = {}
	self.part_equip_stuff_num_list = {}
	local wear_equip_list = EquipWGData.Instance:GetDataList()
	if IsEmptyTable(wear_equip_list) then
		return
	end

	local stuff_id, stuff_num
	for k, v in pairs(wear_equip_list) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(v.index)
			local act_cost_cfg = self:GetEquipmenSuitEquipActCfg(equip_body_seq, equip_part)
			if not IsEmptyTable(act_cost_cfg) then
				for stuff = 1, 3 do
					stuff_id = act_cost_cfg["stuff_" .. stuff .. "_id"] or 0
					stuff_num = act_cost_cfg["stuff_" .. stuff .. "_num"] or 0
					if stuff_id > 0 and stuff_num > 0 then
						self.suit_had_act_stuff_num_list[stuff_id] = self.suit_had_act_stuff_num_list[stuff_id]
																	and self.suit_had_act_stuff_num_list[stuff_id] + stuff_num
																	or stuff_num
						if not self.part_equip_stuff_num_list[v.index] then
							self.part_equip_stuff_num_list[v.index] = {}
						end
						if not self.part_equip_stuff_num_list[v.index][stuff_id] then
							self.part_equip_stuff_num_list[v.index][stuff_id] = 0
						end
						self.part_equip_stuff_num_list[v.index][stuff_id] = self.part_equip_stuff_num_list[v.index][stuff_id] + stuff_num
					end
				end
			end

			-- local cur_suit = self:GetSuitInfoByPartType(v.index)
			-- for i = cur_suit, 0, -1 do
			-- 	local act_cost_cfg = self:GetEquipmenSuitItemCfg(i, item_cfg.order, v.index)
			-- 	if not IsEmptyTable(act_cost_cfg) then
			-- 		for stuff = 1, 3 do
			-- 			stuff_id = act_cost_cfg["stuff_" .. stuff .. "_id"] or 0
			-- 			stuff_num = act_cost_cfg["stuff_" .. stuff .. "_num"] or 0
			-- 			if stuff_id > 0 and stuff_num > 0 then
			-- 				self.suit_had_act_stuff_num_list[stuff_id] = self.suit_had_act_stuff_num_list[stuff_id]
			-- 															and self.suit_had_act_stuff_num_list[stuff_id] + stuff_num
			-- 															or stuff_num
			-- 				if not self.part_equip_stuff_num_list[v.index] then
			-- 					self.part_equip_stuff_num_list[v.index] = {}
			-- 				end
			-- 				if not self.part_equip_stuff_num_list[v.index][stuff_id] then
			-- 					self.part_equip_stuff_num_list[v.index][stuff_id] = 0
			-- 				end
			-- 				self.part_equip_stuff_num_list[v.index][stuff_id] = self.part_equip_stuff_num_list[v.index][stuff_id] + stuff_num
			-- 			end
			-- 		end
			-- 	end
			-- end
		end
	end
end

function EquipmentWGData:GetCountSuitPartStuffNum()
	return self.suit_had_act_stuff_num_list
end

function EquipmentWGData:GetPartEquipUseSuitCount(part)
	return self.part_equip_stuff_num_list[part] or {}
end

function EquipmentWGData:GetStuffActList(equip_order, equip_part)
	local sex = RoleWGData.Instance:GetRoleSex()
	local stuff_list = {}
	if self.one_equip_act[equip_order] then
		for k,v in ipairs(self.one_equip_act[equip_order]) do
			if equip_part == v.equip_part and sex == v.sex then
				table.insert(stuff_list, v)
			end
		end
	end
	return stuff_list
end

function EquipmentWGData:GetSuitPreviewTab()
	local cur_can_cfg = EquipWGData.Instance:GetRoleCanEquipMaxOrder()
	local max_order = COMMON_CONSTS.EQUIP_MAX_ORDER
	local order = cur_can_cfg and cur_can_cfg.equip_order + 2 or max_order
	order = order >= max_order and max_order or order

	local suit_btn = {}
	for k,v in pairs(self.suit_preview) do
		if order >= k then
			table.insert(suit_btn, k)
		end
	end

	return suit_btn
end

function EquipmentWGData:GetSuitPreviewList(suit_order, suit_set)
	local equip_data = {}
	local num = 0
	if self.suit_preview[suit_order] then
		for k,v in ipairs(self.suit_preview[suit_order]) do
			if v.set == suit_set then
				equip_data[num] = v
				num = num + 1
				--table.insert(equip_data, v)
			end
		end
	end
	return equip_data
end

function EquipmentWGData:GetJumpSuitPreviewTab()
	return self.btn_index, self.top_btn_index
end
------------------------------------------功能大改 后续功能重启需要重新处理_END--------------------------------------------------