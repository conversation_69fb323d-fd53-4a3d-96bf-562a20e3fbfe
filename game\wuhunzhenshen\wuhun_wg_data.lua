WuHunWGData = WuHunWGData or BaseClass()
---这里的是表示武魂之力属性的下标，没有意义，任何数字都可以但是不能和真实属性id一样
WUHUNZHILIINDEX = 100000

function WuHunWGData:__init()
	if WuHunWGData.Instance ~= nil then
		error("[WuHunWGData] attempt to create singleton twice!")
		return
	end

	WuHunWGData.Instance = self
	self.wuhun_active_cfg = nil				--激活表
	self.wuhun_map_breach_cfg = nil			--突破表
	self.wuhun_map_breach_sort_cfg = nil	--突破排序表
	self.wuhun_map_level_cfg = nil			--等级表
	self.wuhun_map_purgatory_cfg = nil		--炼魂表
	self.wuhun_exp_cfg = nil
	self.wuhun_type_cfg = nil
	self.wuhun_list = nil
	self.wuhun_huanhua_id = nil
	self.wuhun_shuxingdan_cfg = nil         --武魂属性丹表
	self.wuhun_propertyPellets = nil        --服务器下发的属性丹数据
	self.wuhun_star_level_cfg = nil         --武魂升星表
	self.wuhun_material_buy_cfg = {}       	--武魂材料直购表.
	self.wuhun_material_buy_cfg_by_id = {}
	self.wuhun_prerogative_buy_cfg = {}		--武魂特权直购.
	self.wuhun_prerogative_draw_cfg = {}	--武魂特权抽奖概率.
	self.wuhun_prerogative_draw_mode_cfg = {}--武魂特权抽奖.
	self.wuhun_prerogative_client_cfg = {}	--武魂特权客户端.
	self.wuhun_prerogative_level = 0		--武魂特权等级.
	self.wuhun_prerogative_flag = 1			--武魂特权领取标识,0:未领取,1:已领取.
	self.wuhun_prerogative_draw_info = {}	--武魂特权抽奖结果.

	self:InitConfig()
	self:InitParam()

	-- 红点注册
	local remindmanager_instance = RemindManager.Instance
	remindmanager_instance:Register(RemindName.WuHunDetails, BindTool.Bind(self.GetWuHunRed,self))
end

function WuHunWGData:__delete()
	WuHunWGData.Instance = nil

	self.wuhun_active_cfg = nil				--激活表
	self.wuhun_map_breach_cfg = nil			--突破表
	self.wuhun_map_breach_sort_cfg = nil	--突破排序表
	self.wuhun_map_level_cfg = nil			--等级表
	self.wuhun_map_purgatory_cfg = nil		--炼魂表
	self.wuhun_exp_cfg = nil
	self.wuhun_type_cfg = nil
	self.wuhun_list = nil
	self.wuhun_huanhua_id = nil
	self.wuhun_shuxingdan_cfg = nil
	self.wuhun_propertyPellets = nil
	self.wuhun_star_level_cfg = nil
	self.wuhun_shuxingdan_cfg = nil
	self.wuhun_propertyPellets = nil
	self.wuhun_star_level_cfg = nil
	self.wuhun_material_buy_cfg = {}
	self.wuhun_material_buy_cfg_by_id = {}
	self.wuhun_prerogative_buy_cfg = {}
	self.wuhun_prerogative_draw_cfg = {}
	self.wuhun_prerogative_draw_mode_cfg = {}
	self.wuhun_prerogative_client_cfg = {}
	self.wuhun_prerogative_draw_info = {}

	local remindmanager_instance = RemindManager.Instance
	remindmanager_instance:UnRegister(RemindName.WuHunDetails)
end

-- 初始化配置
function WuHunWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("wuhun_cfg_auto")
	local skill_cfg = ConfigManager.Instance:GetAutoConfig("roleskill_auto")

	if cfg then
		self.wuhun_active_cfg = cfg.wuhun
		self.wuhun_shuxingdan_cfg = cfg.wuhun_dan
		self.wuhun_other_cfg = cfg.other[1]
		self.wuhun_map_breach_cfg = ListToMap(cfg.wuhun_break, "wuhun_id", "wh_level")
		self.wuhun_map_level_cfg = ListToMap(cfg.wuhun_level, "wuhun_id", "wh_level")
		self.wuhun_map_purgatory_cfg = ListToMap(cfg.wuhun_soul, "wuhun_id", "order")
		self.wuhun_map_shuxingdan_cfg = ListToMap(cfg.wuhun_dan, "id", "index")
		self.wuhun_exp_cfg = cfg.item_exp
		self.wuhun_type_cfg = cfg.wuhun_typename
		self.wuhun_star_level_cfg = ListToMap(cfg.wuhun_star_level, "wuhun_id", "wh_star_level")
		self.wuhun_material_buy_cfg = ListToMap(cfg.rmb_buy, "wuhun_id", "grade", "is_discount")
		self.wuhun_material_buy_cfg_by_id = ListToMap(cfg.rmb_buy, "wuhun_id")
		self.wuhun_prerogative_buy_cfg = ListToMap(cfg.right_rmb_buy, "level")
		self.wuhun_prerogative_draw_cfg = cfg.draw
		self.wuhun_prerogative_draw_mode_cfg = cfg.mode
		self.wuhun_prerogative_client_cfg = cfg.wuhun_client[1]
	end

	self:InitSortConfig()
end

function WuHunWGData:InitSortConfig()
	self.wuhun_map_breach_sort_cfg = {}

	for wuhun_id, breach_wuhun_data in pairs(self.wuhun_map_breach_cfg) do
		self.wuhun_map_breach_sort_cfg[wuhun_id] = {}
        
		if breach_wuhun_data then
			for _, breach_data in pairs(breach_wuhun_data) do
				if breach_data and breach_data.wh_level then
					table.insert(self.wuhun_map_breach_sort_cfg[wuhun_id], breach_data)
				end
			end
        end

		table.sort(self.wuhun_map_breach_sort_cfg[wuhun_id], function (a, b)
			return a.wh_level < b.wh_level
		end)
    end
end

-- 默认参数
function WuHunWGData:InitParam()
    self.wuhun_list = {}

	if self.wuhun_active_cfg then
		for wuhun_id, active_cfg_data in pairs(self.wuhun_active_cfg) do
			local temp_data = {}
			temp_data.wh_name = active_cfg_data.wh_name
			temp_data.wh_type = active_cfg_data.wh_type
			temp_data.wuhun_icon = active_cfg_data.wuhun_icon
			temp_data.wuhun_story = active_cfg_data.wuhun_story
			temp_data.skynumber_show = active_cfg_data.skynumber_show
			temp_data.level_show = active_cfg_data.level_show
			temp_data.wuhun_id = wuhun_id
			temp_data.breach_lv = 0
			temp_data.slot_index = -1
			temp_data.breach_up = false
			temp_data.level = 0
			temp_data.level_up = false
			temp_data.lock = true
			temp_data.active_red = false
			temp_data.order = 0
			temp_data.exp = 0
			temp_data.promotion_state = 0
			temp_data.purgatory_up = false
			temp_data.show_red = false
			temp_data.break_times = 0
			temp_data.star = 0
			self.wuhun_list[wuhun_id] = temp_data
		end
	end
end

--物品改变刷新界面背包
function WuHunWGData:ChceckItemChangeRefresh(change_item_id)
	if not self.wuhun_active_cfg then
		return false
	end

	local is_need = false
	for _, wuhun_active_data in pairs(self.wuhun_active_cfg) do
		if wuhun_active_data and wuhun_active_data.item and wuhun_active_data.item == change_item_id then
			is_need = true
			break
		end
	end

	for wuhun_id, breach_wuhun_data in pairs(self.wuhun_map_breach_cfg) do
		if breach_wuhun_data then
			for _, breach_data in pairs(breach_wuhun_data) do
				if breach_data and breach_data.item == change_item_id then
					is_need = true
					break
				end
			end
        end
    end

	if not is_need then
		for _, exp_cfg_data in pairs(self.wuhun_exp_cfg) do
			if exp_cfg_data and exp_cfg_data.item_id == change_item_id then
				is_need = true
				break
			end
		end
	end

	if not is_need then
		for k_1, v_1 in pairs(self.wuhun_shuxingdan_cfg) do
			if v_1.use_item_id ~= 0 and v_1.use_item_id == change_item_id then
				is_need = true
				break
			end
		end
	end

	return is_need
end

function WuHunWGData:GetWuHunRed()
	local peiyang_red = self:GetPeiYangRed()
	if peiyang_red == 1 then
		return 1
	end

	local skill_data = self:GetWuHunSkillData()

	if skill_data then
		for _, skill in ipairs(skill_data) do
			if skill.is_can_add_skill then
				return 1
			end
		end

		if self:CheckWuhunPrerogativeIsShowRemind() then
			return 1
		end

	end

	return 0
end

function WuHunWGData:GetPeiYangRed()
	if self.wuhun_list then
		for index, wuhun_data in pairs(self.wuhun_list) do
			if wuhun_data then
				if wuhun_data.lock and wuhun_data.active_red then
					return 1
				elseif wuhun_data.show_red then
					return 1
				end
			end
		end
	end

	return 0
end

function WuHunWGData:GetAllWuhunlist()
	local return_table = {}

	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level

	if self.wuhun_list then
		for _, active_cfg_data in pairs(self.wuhun_list) do
			local can_show = cur_open_day >= active_cfg_data.skynumber_show and role_level >= active_cfg_data.level_show
			can_show = can_show or active_cfg_data.active_red or (not active_cfg_data.lock)	-- 可激活或者达到展示条件才展示
			if active_cfg_data and active_cfg_data.wuhun_id and can_show then
				table.insert(return_table, active_cfg_data)
			end
		end
	end

	table.sort(return_table, function (a, b)
		return a.wuhun_id < b.wuhun_id
	end)

	return return_table
end

-- 获取所有武魂之力的列表
function WuHunWGData:GetAllWuhunZhiLilist()
	local wuhun_list = self:GetAllWuhunlist()
	local return_table = {}

	for _, wuhun_data in ipairs(wuhun_list) do
		local cur_wuhun_li = self:GetPurgatoryWuHunLi(wuhun_data.wuhun_id, wuhun_data.order)

		if not wuhun_data.lock then
			cur_wuhun_li = cur_wuhun_li + self:GetOtherWuHunLi(wuhun_data.wuhun_id, wuhun_data.level, wuhun_data.lock, wuhun_data.promotion_state, wuhun_data.break_times)
		end

		if not return_table[wuhun_data.wh_type] then
			return_table[wuhun_data.wh_type] = {}
			return_table[wuhun_data.wh_type].wh_type = wuhun_data.wh_type
			return_table[wuhun_data.wh_type].wuhun_id = wuhun_data.wuhun_id
			return_table[wuhun_data.wh_type].wuhun_li = cur_wuhun_li
		else
			return_table[wuhun_data.wh_type].wuhun_li = return_table[wuhun_data.wh_type].wuhun_li + cur_wuhun_li
		end
	end

	local aim_table = {}
	for k, data in pairs(return_table) do
		if data and data.wh_type then
			table.insert(aim_table, data)
		end
	end

	table.sort(aim_table, function (a, b)
		return a.wuhun_id < b.wuhun_id
	end)

	return aim_table
end


function WuHunWGData:GetWuHunSpendList()
	if not self.wuhun_exp_cfg then
		return nil
	end
	
	local return_table = {}
	for _, exp_cfg_data in pairs(self.wuhun_exp_cfg) do
		if exp_cfg_data and exp_cfg_data.item_id then
			table.insert(return_table, exp_cfg_data)
		end
	end

	return return_table
end

function WuHunWGData:GetWuHunBreachList(wuhun_id, level)
	local breach_cfg = self:GetWuHunBreachCfg(wuhun_id, level)
	local return_table = {}

	if breach_cfg then
		local data = {}
		data.item_id = breach_cfg.item
		data.item_num = breach_cfg.item_num
		table.insert(return_table, data)
	end

	return return_table
end

--根据武魂ID获取对应的武魂丹的数据  会有3组
function WuHunWGData:GetWuHunShuXingDan(wuhun_id)
	return self.wuhun_map_shuxingdan_cfg[wuhun_id] or {}
end

---获取一个武魂的突破配置
function WuHunWGData:GetWuHunBreachCfg(wuhun_id, breach_lv)
	local enemy = {}

	if breach_lv then
		return ((self.wuhun_map_breach_cfg or enemy)[wuhun_id] or enemy)[breach_lv] or nil
	else
		return (self.wuhun_map_breach_cfg or enemy)[wuhun_id] or nil
	end
end

---获取一个武魂的突破配置(排序的)
function WuHunWGData:GetWuHunBreachSortCfg(wuhun_id)
	local enemy = {}
	return (self.wuhun_map_breach_sort_cfg or enemy)[wuhun_id] or nil
end

---获取一个武魂的突破配置
function WuHunWGData:GetWuHunBreachCfgByWuHunLv(wuhun_id, wuhun_lv)
	local breach_wuhun_cfg = self:GetWuHunBreachSortCfg(wuhun_id)
	local return_cfg = nil

	if not breach_wuhun_cfg then
		return return_cfg
	end

	for _, cfg in ipairs(breach_wuhun_cfg) do
		if cfg.wh_level <= wuhun_lv then
			return_cfg = cfg
		end

		if cfg.wh_level > wuhun_lv then
			break
		end
	end

	return return_cfg
end

---获取一个武魂的突破配置
function WuHunWGData:GetWuHunBreachCfgByBreakTimes(wuhun_id, break_times)
	local breach_wuhun_cfg = self:GetWuHunBreachSortCfg(wuhun_id)
	local return_cfg = nil

	if not breach_wuhun_cfg then
		return return_cfg
	end

	for _, cfg in ipairs(breach_wuhun_cfg) do
		if cfg.break_time <= break_times then
			return_cfg = cfg
		end

		if cfg.break_time > break_times then
			break
		end
	end

	return return_cfg
end

---获取一个武魂的等级配置
function WuHunWGData:GetWuHunLevelCfg(wuhun_id, level)
	local enemy = {}
	return ((self.wuhun_map_level_cfg or enemy)[wuhun_id] or enemy)[level] or nil
end

---获取一个武魂的炼魂配置
function WuHunWGData:GetWuHunPurgatoryCfg(wuhun_id, purgatory_lv)
	local enemy = {}
	return ((self.wuhun_map_purgatory_cfg or enemy)[wuhun_id] or enemy)[purgatory_lv] or nil
end

---获取一个武魂的激活配置
function WuHunWGData:GetWuHunActiveCfg(wuhun_id)
	local enemy = {}
	return (self.wuhun_active_cfg or enemy)[wuhun_id] or nil
end

---获取一个武魂的激活配置
function WuHunWGData:GetWuHunSkillActiveCfg(wuhun_skill_id)
	if self.wuhun_active_cfg then
		for _, active_data in pairs(self.wuhun_active_cfg) do
			if active_data and active_data.skill_id and active_data.skill_id == wuhun_skill_id then
				return active_data
			end
		end
	end

	return nil
end

---获取武魂的客户端技能配置
function WuHunWGData:GetWuHunClientSkillCfg(skill_id, skill_level)
	return SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
end

---获取武魂的技能配置
function WuHunWGData:GetWuHunSkillCfg(skill_id, skill_level)
	return SkillWGData.Instance:GetWuHunSkillById(skill_id, skill_level)
end

--获得武魂升星表的配置
function WuHunWGData:GetWuhunStarLevelCfg(wuhun_id, star)
	return (self.wuhun_star_level_cfg[wuhun_id] or {})[star]
end

---获取武魂的名字的配置
function WuHunWGData:GetWuHunLiCfg(wuhun_li_type)
	local enemy = {}
	return (self.wuhun_type_cfg or enemy)[wuhun_li_type] or nil
end

function WuHunWGData:GetWuHunSkillLevel(wuhun_id, level, promotion_state)
	local breach_cfg = self:GetWuHunBreachCfg(wuhun_id)
	local skill_level = 1

	if breach_cfg then
		for _, breach_data in pairs(breach_cfg) do
			if breach_data and breach_data.wh_level then
				if level > breach_data.wh_level then
					if breach_data.skill_level > skill_level then
						skill_level = breach_data.skill_level
					end
				elseif level == breach_data.wh_level and promotion_state ~= 1 then
					skill_level = breach_data.skill_level
				end
			end
		end
	end

	return skill_level
end

---获取一份武魂的本地缓存数据
function WuHunWGData:GetWuHunSingleData(wuhun_id)
	local enemy = {}
	return (self.wuhun_list or enemy)[wuhun_id] or nil
end

---获取挂在的武魂技能数据
function WuHunWGData:GetWuHunSkillData()
	local skill_data = {}

	if not self.wuhun_list then
		return nil
	end

	local skill_red = false
	local no_open_wuhun = true

	for wuhun_id, wuhun_data in pairs(self.wuhun_list) do
		if not wuhun_data.lock then
			no_open_wuhun = false
			
			if wuhun_data.slot_index and wuhun_data.slot_index ~= -1 then
				local index = wuhun_data.slot_index - 3	--(去掉普攻3个槽位)
				skill_data[index] = {}		-- 服务器从0开始的
				skill_data[index].wuhun_id = wuhun_id
				skill_data[index].wh_type = wuhun_data.wh_type
				skill_data[index].slot_index = wuhun_data.slot_index
				skill_data[index].level = wuhun_data.level
				skill_data[index].order = wuhun_data.order
				skill_data[index].promotion_state = wuhun_data.promotion_state
				skill_data[index].break_times = wuhun_data.break_times
			else	-- 激活了但是没有使用技能
				skill_red = true
			end
		end
	end

	local role_skill_data = SkillWGData.Instance:GetSkillListByType(1)   -- 主动技能列表

	for i = 1, 4 do
		if skill_data[i] then
			skill_data[i].is_can_add_skill = false
			skill_data[i].is_have_skill = true
		else
			skill_data[i] = {}
			skill_data[i].is_can_add_skill = skill_red
			skill_data[i].is_have_skill = false
		end

		skill_data[i].no_open_wuhun = no_open_wuhun

		if role_skill_data then
			skill_data[i].role_skill_data = role_skill_data[i] or nil
		end
	end
	
	return skill_data
end

function WuHunWGData:GetCurrWuHunBindSkill(skill_index)
	local skill_datas = self:GetWuHunSkillData()
	if skill_datas and skill_datas[skill_index] then
		local skill_data = skill_datas[skill_index]
		local active_cfg = WuHunWGData.Instance:GetWuHunActiveCfg(skill_data.wuhun_id)
		local skill_level = WuHunWGData.Instance:GetWuHunSkillLevel(skill_data.wuhun_id, skill_data.level, skill_data.promotion_state)
	
		if not active_cfg then
			return false, nil
		end
	
		local wuhun_skill_client_cfg = WuHunWGData.Instance:GetWuHunClientSkillCfg(active_cfg.skill_id, skill_level)
	
		if not wuhun_skill_client_cfg then
			return false, nil
		end

		return true, wuhun_skill_client_cfg.icon_resource
	end

	return false, nil
end


function WuHunWGData:GetCurrCanUseSkillList(skill_data)
	local skill_list = {}

	if not self.wuhun_list then
		return nil
	end

	local check_used = function (wuhun_id)
		for _, skill_item in ipairs(skill_data) do
			if skill_item and skill_item.wuhun_id and skill_item.wuhun_id == wuhun_id then
				return true
			end
		end

		return false
	end

	for wuhun_id, wuhun_data in pairs(self.wuhun_list) do
		if not wuhun_data.lock then
			local temp_data = {}
			temp_data.wuhun_id = wuhun_id
			temp_data.level = wuhun_data.level
			temp_data.promotion_state = wuhun_data.promotion_state
			temp_data.used = check_used(wuhun_id)
			table.insert(skill_list, temp_data)
		end
	end

	table.sort(skill_list, function (a, b)
		return a.wuhun_id < b.wuhun_id
	end)

	return skill_list
end

---同步服务器给到的全部数据
function WuHunWGData:initServerData(protocol)
	for _, wuhun_info in ipairs(protocol.wuhun_list) do
		self:RefreshWuHunData(wuhun_info)
	end
	self:RefreshWuHunRedActiveState()

	self.wuhun_prerogative_level = protocol.wuhun_prerogative_level
	self.wuhun_prerogative_flag = protocol.wuhun_prerogative_flag
end

-- 同步当前的幻化武魂id
function WuHunWGData:UpdateWunId(protocol)
	if protocol then
		self.wuhun_huanhua_id = protocol.wuhun_id
	end
end

--同步服务器给到的单个武魂数据
function WuHunWGData:RefreshSingleData(protocol)
	local is_active = self:RefreshWuHunData(protocol.wuhun)
	self:RefreshWuHunRedActiveState()
	return is_active
end

function WuHunWGData:RefreshWuHunData(wuhun_data)
	local is_active = false

	if (not wuhun_data) or (not wuhun_data.wuhun_id) or wuhun_data.wuhun_id == -1 then
		return false
	end

	if self.wuhun_list and self.wuhun_list[wuhun_data.wuhun_id] then
		self.wuhun_list[wuhun_data.wuhun_id].level = wuhun_data.level
		self.wuhun_list[wuhun_data.wuhun_id].order = wuhun_data.lianhua_order
		self.wuhun_list[wuhun_data.wuhun_id].exp = wuhun_data.exp
		self.wuhun_list[wuhun_data.wuhun_id].slot_index = wuhun_data.slot_index
		self.wuhun_list[wuhun_data.wuhun_id].promotion_state = wuhun_data.promotion_state
		self.wuhun_list[wuhun_data.wuhun_id].break_times = wuhun_data.break_times
		self.wuhun_list[wuhun_data.wuhun_id].shuxing_dan1 = wuhun_data.shuxing_dan1
		self.wuhun_list[wuhun_data.wuhun_id].shuxing_dan2 = wuhun_data.shuxing_dan2
		self.wuhun_list[wuhun_data.wuhun_id].shuxing_dan3 = wuhun_data.shuxing_dan3
		self.wuhun_list[wuhun_data.wuhun_id].star = wuhun_data.star
		self.wuhun_list[wuhun_data.wuhun_id].material_buy_end_time = wuhun_data.material_buy_end_time
		self.wuhun_list[wuhun_data.wuhun_id].material_buy_grade = wuhun_data.material_buy_grade

		if self.wuhun_list[wuhun_data.wuhun_id].lock then
			self.wuhun_list[wuhun_data.wuhun_id].lock = false
			is_active = true
		end
	end

	return is_active
end

function WuHunWGData:RefreshWuHunRedActiveState()
	if not self.wuhun_list then
		return
	end

	for _, wuhun_data in pairs(self.wuhun_list) do
		if wuhun_data.lock then
			wuhun_data.active_red = self:GetActiveRed(wuhun_data)
		else
			wuhun_data.active_red = false
			wuhun_data.level_up = self:GetLevelConditionRed(wuhun_data, wuhun_data.promotion_state)
			wuhun_data.purgatory_up = self:GetPurgatoryConditionRed(wuhun_data)

			if not wuhun_data.level_up then	-- 可升级不可突破
				wuhun_data.breach_up = self:GetBreachConditionRed(wuhun_data, wuhun_data.promotion_state)
			end

			wuhun_data.starup_red = self:GetWuhunStarUpRedShow(wuhun_data.wuhun_id)
			local has_property_pellet = self:GetPropertyPelletRed(wuhun_data)
			local red_state = wuhun_data.level_up or wuhun_data.purgatory_up or wuhun_data.breach_up or has_property_pellet or wuhun_data.starup_red
			wuhun_data.show_red = red_state
		end
	end
end

--武魂属性的红点判断
function WuHunWGData:GetPropertyPelletRed(wuhun_data)
	if wuhun_data.lock or not self.wuhun_map_shuxingdan_cfg then
		return false
	end

	local all_data = self:GetWuHunShuXingDan(wuhun_data.wuhun_id)
	for i = 0, 2 do
		local one_data = all_data[i]
		local num = ItemWGData.Instance:GetItemNumInBagById(one_data.use_item_id)
		local use_num = wuhun_data["shuxing_dan"..i + 1]
		if use_num < one_data.use_limit_num and num > 0 then
			return true
		end
	end

	return false
end

function WuHunWGData:GetActiveRed(wuhun_data)
	if (not wuhun_data) or (not self.wuhun_active_cfg) then
		return false
	end

	local active_cfg = self:GetWuHunActiveCfg(wuhun_data.wuhun_id)

	if active_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(active_cfg.item)
		if item_num >= active_cfg.item_num then
			return true
		end
	end

	return false
end

function WuHunWGData:GetBreachConditionRed(wuhun_data, promotion_state)
	if (not wuhun_data) or (not self.wuhun_map_breach_cfg) then
		return false
	end

	if promotion_state ~= 1 then
		return false
	end
	
	local breach_cfg = self:GetWuHunBreachCfg(wuhun_data.wuhun_id, wuhun_data.level)

	if breach_cfg then		---这个为空表示突破不了
		local item_num = ItemWGData.Instance:GetItemNumInBagById(breach_cfg.item)
		if item_num >= breach_cfg.item_num then
			return true
		end
	end

	return false
end

function WuHunWGData:GetLevelConditionRed(wuhun_data, promotion_state)
	if (not wuhun_data) or (not self.wuhun_map_level_cfg) or (not self.wuhun_exp_cfg) then
		return false
	end

	---先查看是否需要突破
	if promotion_state == 1 then
		return false
	end

	local level_cfg = self:GetWuHunLevelCfg(wuhun_data.wuhun_id, wuhun_data.level)
	local level_cfg_next = self:GetWuHunLevelCfg(wuhun_data.wuhun_id, wuhun_data.level + 1)

	if not level_cfg_next then	---没有下一级
		return false
	end

	local have_exp = 0 

	for item_id, exp_data in pairs(self.wuhun_exp_cfg) do
		local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
		have_exp = have_exp + item_num * exp_data.add_exp
	end

	if level_cfg then	
		if have_exp >= level_cfg.next_exp - wuhun_data.exp then	-- 去掉已拥有的经验值
			return true
		end
	end

	return false
end

function WuHunWGData:GetPurgatoryConditionRed(wuhun_data)
	if (not wuhun_data) or (not self.wuhun_map_purgatory_cfg) then
		return false
	end

	--默认是零，检测红点默认加1
	local purgatory_cfg = self:GetWuHunPurgatoryCfg(wuhun_data.wuhun_id, wuhun_data.order + 1)

	if purgatory_cfg and wuhun_data.level >= purgatory_cfg.need_level then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(purgatory_cfg.item)
		if item_num >= purgatory_cfg.item_num then
			return true
		end
	end

	return false
end

function WuHunWGData:GetPurgatoryWuHunLi(wuhun_id, order, is_simple)
	if (not self.wuhun_map_purgatory_cfg) or (not self.wuhun_map_purgatory_cfg[wuhun_id]) then
		return 0
	end

	local simple_value = 0
	local now_value = 0

	for cfg_order, purgatory_data in pairs(self.wuhun_map_purgatory_cfg[wuhun_id]) do
		if cfg_order <= order then
			now_value = now_value + purgatory_data.wh_value

			if cfg_order == order then
				simple_value = purgatory_data.wh_value
			end
		end
	end

	if is_simple then
		return simple_value
	else
		return now_value
	end
end


function WuHunWGData:GetOtherWuHunLi(wuhun_id, wuhun_level, is_lock, promotion_state, break_times)
	local wuhun_level_cfg = self:GetWuHunLevelCfg(wuhun_id, wuhun_level)
	local wuhun_value = 0

	if is_lock then
		return wuhun_value
	end
	-- 本身的等级
	if wuhun_level_cfg then
		---添加武魂之力显示
		wuhun_value = wuhun_level_cfg["wh_value"]
	end

	local wuhun_breach_cfg = self:GetWuHunBreachCfgByBreakTimes(wuhun_id, break_times)
	if wuhun_breach_cfg then
		---添加武魂之力显示
		wuhun_value = wuhun_value + wuhun_breach_cfg["wh_value"]
	end

	local wuhun_data = self:GetWuHunSingleData(wuhun_id) or {}
	local star_cfg = self:GetWuhunStarLevelCfg(wuhun_id, wuhun_data.star or 0)
	if star_cfg then
		wuhun_value = wuhun_value + star_cfg["wh_value"]
	end

	return wuhun_value
end


function WuHunWGData:GetWuhunAttrlist(wuhun_id, wuhun_level, is_lock, promotion_state)
	local cur_table = {}
	local wuhun_level_cfg = self:GetWuHunLevelCfg(wuhun_id, wuhun_level)
	local wuhun_level_next_cfg = self:GetWuHunLevelCfg(wuhun_id, wuhun_level + 1)

	-- 本身的等级
	if wuhun_level_cfg then
		---添加武魂之力显示
		local key = wuhun_level_cfg["wh_value"]
		if key then
			--这里的100000 没有任何意义，只是表示一个索引可以为任何数字，
			cur_table[WUHUNZHILIINDEX] = {}
			cur_table[WUHUNZHILIINDEX].attr_str = -100
			cur_table[WUHUNZHILIINDEX].attr_value = wuhun_level_cfg["wh_value"]

			if wuhun_level_next_cfg and (not is_lock) then
				cur_table[WUHUNZHILIINDEX].add_value = wuhun_level_next_cfg["wh_value"] - cur_table[WUHUNZHILIINDEX].attr_value
			end
		end

		for i = 1, 6 do
			local key = wuhun_level_cfg["attr_id" .. i]

			if key then
				cur_table[key] = {}
				cur_table[key].attr_str = key
				cur_table[key].attr_value = wuhun_level_cfg["attr_value" .. i]
	
				if wuhun_level_next_cfg and (not is_lock) then
					cur_table[key].add_value = wuhun_level_next_cfg["attr_value" .. i] - cur_table[key].attr_value
				end
			end
		end
	end

	--突破不计算百分比加成  所以属性丹比突破先计算
	cur_table = self:AddPropertyPelletsAttr(cur_table, wuhun_id)

	--突破计算属性
	if promotion_state == 1  then
		cur_table = self:AddBreachAttr(cur_table, wuhun_id, wuhun_level, true)
		wuhun_level = wuhun_level - 1
	end

	cur_table = self:AddBreachAttr(cur_table, wuhun_id, wuhun_level)


	local return_table = {}

	for _, attr_data in pairs(cur_table) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

--计算属性丹的属性
function WuHunWGData:AddPropertyPelletsAttr(cur_table, wuhun_id, is_starup)
	local wuhun_data = self:GetWuHunSingleData(wuhun_id)
	if not cur_table or not wuhun_data or wuhun_data.lock then
		return cur_table
	end

	--对应的武魂的三个的属性丹配置
	local all_cfgs = self:GetWuHunShuXingDan(wuhun_id)
	for i = 1, 3 do
		local use_num = wuhun_data["shuxing_dan" .. i]
		local pro_cfg = all_cfgs[i - 1]
		local add_propvalue = 1 + pro_cfg.base_attr_per * use_num/10000

		if use_num > 0 and pro_cfg then --要已经使用了属性丹
			if not is_starup then
				for j = 1, 6 do
					local attr_key = pro_cfg["attr_id" .. j]
					local attr_value = pro_cfg["attr_value" .. j]
					if attr_key and cur_table[attr_key] and attr_value > 0 then
						cur_table[attr_key].attr_value = cur_table[attr_key].attr_value + attr_value * use_num
					end
				end
			end

			--计算百分比加成
			if pro_cfg.base_attr_per > 0 then
				for attr_key, t_value in pairs(cur_table) do
					if attr_key ~= WUHUNZHILIINDEX and not EquipmentWGData.Instance:GetAttrIsPer(t_value.attr_str) then --有些属性不加算加成
						t_value.attr_value = math.floor(t_value.attr_value * add_propvalue)
						if t_value.add_value then
							t_value.add_value = math.floor(t_value.add_value * add_propvalue)
						end
					end
				end
			end
		end
	end

	return cur_table
end

function WuHunWGData:PackageLianhunAttr(aim_table, wuhun_id, order, is_next, star)
	local lianhun_attr_list = self:AddLianhunAttr(nil, wuhun_id, order, is_next)
	local up_satr_attr_list = self:GetWuhunStarUpAttrlistByCfg(wuhun_id, star)

	if lianhun_attr_list and aim_table then
		for _, attr_data in pairs(lianhun_attr_list) do
			if attr_data and attr_data.attr_str then
				table.insert(aim_table, attr_data)
			end
		end
	end

	local add_attr = function(temp_attr_data)
		local is_same = false
		for k, attr_data in pairs(aim_table) do
			if attr_data and attr_data.attr_str == temp_attr_data.attr_str then
				is_same = true
				attr_data.attr_value = attr_data.attr_value + temp_attr_data.attr_value
				break
			end
		end

		if not is_same then
			table.insert(aim_table, temp_attr_data)
		end
	end

	if up_satr_attr_list and aim_table then
		for _, attr_data in pairs(up_satr_attr_list) do
			add_attr(attr_data)
		end
	end

	return aim_table
end

function WuHunWGData:AddBreachAttr(cur_table, wuhun_id, wuhun_level, is_add)
	local wuhun_breach_cfg = self:GetWuHunBreachCfgByWuHunLv(wuhun_id, wuhun_level)
	local wuhun_breach_cfg_last = self:GetWuHunBreachCfgByWuHunLv(wuhun_id, wuhun_level - 1)
	
	if wuhun_breach_cfg and cur_table then

		local key = wuhun_breach_cfg["wh_value"]
		if key and cur_table[WUHUNZHILIINDEX] then
			if is_add then
				cur_table[WUHUNZHILIINDEX].add_value = wuhun_breach_cfg["wh_value"]

				if wuhun_breach_cfg_last then
					cur_table[WUHUNZHILIINDEX].add_value = wuhun_breach_cfg["wh_value"] - wuhun_breach_cfg_last["wh_value"]
				end
			else
				cur_table[WUHUNZHILIINDEX].attr_value = cur_table[WUHUNZHILIINDEX].attr_value + wuhun_breach_cfg["wh_value"]
			end
		end

		for i = 1, 6 do
			local key = wuhun_breach_cfg["attr_id" .. i]
			if key and cur_table[key] then
				if is_add then
					cur_table[key].add_value = wuhun_breach_cfg["attr_value" .. i]

					if wuhun_breach_cfg_last then
						cur_table[key].add_value = wuhun_breach_cfg["attr_value" .. i] - wuhun_breach_cfg_last["attr_value" .. i]
					end
				else
					cur_table[key].attr_value = cur_table[key].attr_value + wuhun_breach_cfg["attr_value" .. i]
				end
			end
		end
	end
	return cur_table
end

-- 添加炼魂属性
function WuHunWGData:AddLianhunAttr(cur_table, wuhun_id, order, is_next)
	local wuhun_purgatory_cfg = self:GetWuHunPurgatoryCfg(wuhun_id, order)

	if not cur_table then
		cur_table = {}
	end
	
	if wuhun_purgatory_cfg then
		for i = 1, 1 do
			local key = wuhun_purgatory_cfg["attr_id" .. i]
			if key and cur_table[key] then
				cur_table[key].attr_value = cur_table[key].attr_value + wuhun_purgatory_cfg["attr_value" .. i]
			elseif key then
				cur_table[key] = {}
				cur_table[key].attr_str = key
				cur_table[key].attr_value = wuhun_purgatory_cfg["attr_value" .. i]
			end
		end
	end

	return cur_table
end

-- 获取炼魂属性
function WuHunWGData:GetLianHunAttrData(wuhun_id, order, wh_name)
	local now_cfg = self:GetWuHunPurgatoryCfg(wuhun_id, order)
	local next_cfg = self:GetWuHunPurgatoryCfg(wuhun_id, order + 1)

	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(now_cfg, next_cfg, "attr_id", "attr_value", 1, 5, true)
	local cur_wuhun_li = self:GetPurgatoryWuHunLi(wuhun_id, order)
	local next_wuhun_li = self:GetPurgatoryWuHunLi(wuhun_id, order + 1, true)

	local li_attr_data = {
		attr_name = string.format("<color=#f1f031>%s</color>", wh_name),
		attr_value = cur_wuhun_li or 0,
		add_value = next_wuhun_li or 0,
		is_per = false,
	}
	table.insert(attr_list, 1, li_attr_data)
	return attr_list
end

---获取一个武魂的战力
function WuHunWGData:GetWuHunAttrCap(wuhun_attr_list)
	if not wuhun_attr_list then
		return 0
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end

    for index, attr_cell in ipairs(wuhun_attr_list) do
		if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
			add_tab(attr_str, attr_cell.attr_value)
		end
    end

	local cap = AttributeMgr.GetCapability(attribute)
	return cap, attribute
end

--- 转换一下属性列表
function WuHunWGData:ConvertList(wuhun_attr_list)
	local return_list = {}
	for _, data in pairs(wuhun_attr_list) do
		if data.attr_str and return_list[data.attr_str] then
			return_list[data.attr_str] = return_list[data.attr_str] + data.attr_value
		else
			return_list[data.attr_str] = data.attr_value
		end
	end

	return return_list
end

--- 获取一个武魂物体的属性配置根据物品id
function WuHunWGData:GetWuHunAttrCfgByItemID(item_id)
	if not self.wuhun_active_cfg then
		return nil
	end

	for _, active_data in pairs(self.wuhun_active_cfg) do
        if active_data and active_data.item and active_data.item == item_id then
			return self:GetWuHunLevelCfg(active_data.wuhun_id, 0)
        end
    end

	return nil
end

--获取一个武魂丹的属性配置
function WuHunWGData:GetWuHunShuXingDanAttrCfgByItemID(item_id)
	for _, cfg in pairs(self.wuhun_shuxingdan_cfg) do
        if cfg.use_item_id and cfg.use_item_id == item_id then
			return cfg
        end
    end

	return nil
end

--获取一个武魂丹的配置
function WuHunWGData:GetPropertyDanCfgByItemId(item_id)
	for k_1, v_1 in pairs(self.wuhun_shuxingdan_cfg) do
		if v_1.use_item_id == item_id then
			return v_1
        end
	end

	return {}
end

--- 获取一个武魂配置根据物品id
function WuHunWGData:GetWuhunResByItemId(item_id)
	if not self.wuhun_active_cfg then
		return nil
	end

	for _, active_data in pairs(self.wuhun_active_cfg) do
        if active_data and active_data.item and active_data.item == item_id then
			return active_data
        end
    end

	return nil
end

--- 获取一个武魂物体形象根据物品id(这个换成level换成突破次数)
function WuHunWGData:GetWuhunBreachResByIdLv(wuhun_id, wuhun_lv)
	-- 突破的属性
	local wuhun_breach_cfg = self:GetWuHunBreachCfgByBreakTimes(wuhun_id, wuhun_lv)
	return wuhun_breach_cfg
end

--- 获取一个武魂是否激活
function WuHunWGData:IsWuHunActive(wuhun_active_cfg)
	local data = self:GetWuHunSingleData(wuhun_active_cfg.wuhun_id)
	if data then
		return not data.lock
	end

	return false
end

--判断一个武魂是否激活
function WuHunWGData:GetWuHunIsActive(wuhun_id)
	local data = self:GetWuHunSingleData(wuhun_id)

	if data then
		return not data.lock
	end

	return false
end



---------------------------------------------武魂升星相关 开始---------------------------------------------
-- 获取升星属性
function WuHunWGData:GetWuhunStarUpAttrlistByCfg(wuhun_id, star)
	local cur_table = {}
	local star_cfg = self:GetWuhunStarLevelCfg(wuhun_id, star)
	local star_next_cfg = self:GetWuhunStarLevelCfg(wuhun_id, star + 1)
	-- 本身的等级
	if star_cfg then
		---添加武魂之力显示
		local key = star_cfg["wh_value"]
		if key then
			--这里的100000 没有任何意义，只是表示一个索引可以为任何数字，
			cur_table[WUHUNZHILIINDEX] = {}
			cur_table[WUHUNZHILIINDEX].attr_str = -100
			cur_table[WUHUNZHILIINDEX].attr_value = star_cfg["wh_value"]

			if star_next_cfg then
				cur_table[WUHUNZHILIINDEX].add_value = star_next_cfg["wh_value"] - cur_table[WUHUNZHILIINDEX].attr_value
			end
		end

		for i = 1, 5 do
			local key = star_cfg["attr_id" .. i]

			if key and key > 0 then
				cur_table[key] = {}
				cur_table[key].attr_str = key
				cur_table[key].attr_value = star_cfg["attr_value" .. i]
				if star_next_cfg then
					cur_table[key].add_value = star_next_cfg["attr_value" .. i] - cur_table[key].attr_value
				end
			end
		end
	end

	return cur_table
end

function WuHunWGData:GetWuhunStarUpAttrlist(wuhun_id, star)
	local cur_table = self:GetWuhunStarUpAttrlistByCfg(wuhun_id, star)
	cur_table = self:AddPropertyPelletsAttr(cur_table, wuhun_id, true)
	local return_table = {}
	for _, attr_data in pairs(cur_table) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

--武魂升星红点
function WuHunWGData:GetWuhunStarUpRedShow(wuhun_id)
	if not self:GetWuHunIsActive(wuhun_id) then
		return false
	end

	local wuhun_data = self:GetWuHunSingleData(wuhun_id) or {}
	local star = wuhun_data.star or 0
	local star_next_cfg = self:GetWuhunStarLevelCfg(wuhun_id, star + 1)
	if not star_next_cfg then
		return false
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(star_next_cfg.item)
	if num >= star_next_cfg.item_num then
		return true
	end

	return false
end

---------------------------------------------武魂升星相关 结束---------------------------------------------


---------------------------------------------武魂材料直购 开始---------------------------------------------
function WuHunWGData:SetWuhunMaterialBuyInfo(protocol)
	self.wuhun_list[protocol.wuhun_id].material_buy_grade = protocol.grade
	self.wuhun_list[protocol.wuhun_id].material_buy_end_time = protocol.discount_finish_time
end

function WuHunWGData:GetWuhunMaterialCfg(id)
	local is_discount = self:CheckWuhunMaterialIsDiscount(id) and 1 or 0
	return self:GetWuhunMaterialCfgByDis(id, is_discount)
end

function WuHunWGData:GetWuhunMaterialCfgByDis(id, is_discount)
	local grade = self.wuhun_list[id].material_buy_grade
	local max_grade = (((self.wuhun_material_buy_cfg_by_id or {})[id] or {}).grade) or 0

	if grade > max_grade then
		grade = max_grade
	end

	return ((self.wuhun_material_buy_cfg or {})[id] or {})[grade][is_discount] or {}
end

function WuHunWGData:GetWuhunMaterialTime(id)
	local time = self.wuhun_list[id].material_buy_end_time or 0
	return time
end

--是否可以购买.
function WuHunWGData:CheckWuhunMaterialIsCanBuy(id)
	local is_can_buy = true
	local max_grade = (((self.wuhun_material_buy_cfg_by_id or {})[id] or {}).grade) or 0
	local grade = (self.wuhun_list[id].material_buy_grade) or max_grade + 1

	if grade > max_grade then
		is_can_buy = false
	end

	return is_can_buy
end

--是否打折.
function WuHunWGData:CheckWuhunMaterialIsDiscount(id)
	local is_discount = false
	local end_time = self.wuhun_list[id].material_buy_end_time or 0

	if end_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
		is_discount = true
	end

	return is_discount
end

function WuHunWGData:CheckWuhunMaterialHasDanItem(id)
	local is_has = false
	for k, v in pairs(self.wuhun_shuxingdan_cfg) do
		if v.use_item_id ~= 0 and v.use_item_id == id then
			is_has = true
			break
		end
	end
	return is_has
end
---------------------------------------------武魂材料直购 结束---------------------------------------------


---------------------------------------------武魂特权 开始---------------------------------------------
function WuHunWGData:SetWuhunPrerogativeUpdate(protocol)
	self.wuhun_prerogative_level = protocol.wuhun_prerogative_level
	self.wuhun_prerogative_flag = protocol.wuhun_prerogative_flag
end

function WuHunWGData:SetWuhunPrerogativeDraw(protocol)
	self.wuhun_prerogative_draw_info.mode = protocol.mode
	self.wuhun_prerogative_draw_info.count = protocol.count
	self.wuhun_prerogative_draw_info.result_item_list = protocol.result_item_list
end

function WuHunWGData:GetWuhunPrerogativeDraw()
	return self.wuhun_prerogative_draw_info
end

function WuHunWGData:GetWuhunPrerogativeCurCfg()
	return self:GetWuhunPrerogativeCfg(self.wuhun_prerogative_level > 0 and self.wuhun_prerogative_level or 1)
end

function WuHunWGData:GetWuhunPrerogativeCfg(lv)
	return ((self.wuhun_prerogative_buy_cfg or {})[lv]) or {}
end

function WuHunWGData:GetWuhunPrerogativeFreeList()
	local prerogative_cfg = self:GetWuhunPrerogativeCurCfg()
	if IsEmptyTable(prerogative_cfg) then
		return
	end

	local free_list = {}
	for i = 0, #prerogative_cfg.daily_reward do
		free_list[i + 1] = prerogative_cfg.daily_reward[i]
	end

	return free_list
end

function WuHunWGData:GetWuhunPrerogativeLevel()
	return self.wuhun_prerogative_level
end

function WuHunWGData:GetWuhunPrerogativeMaxLevel()
	return self.wuhun_prerogative_buy_cfg[#self.wuhun_prerogative_buy_cfg].level
end

function WuHunWGData:GetWuhunPrerogativeFlag()
	return self.wuhun_prerogative_flag == 0 and true or false
end

function WuHunWGData:GetWuhunPrerogativeClientcfg()
	return self.wuhun_prerogative_client_cfg
end

function WuHunWGData:GetWuhunPrerogativeDrawCostItem()
	return ((self.wuhun_other_cfg or {}).cost_item_id) or 0
end

--抽奖模式
function WuHunWGData:GetWuhunPrerogativeDrawModeCfg()
	return self.wuhun_prerogative_draw_mode_cfg
end

--是否可以购买.
function WuHunWGData:CheckWuhunPrerogativeIsCanBuy()
	local is_can_buy = true
	local lv = WuHunWGData.Instance:GetWuhunPrerogativeLevel()
	local max_lv = WuHunWGData.Instance:GetWuhunPrerogativeMaxLevel()

	if lv == max_lv then
		is_can_buy = false
	end

	return is_can_buy
end

--是否激活武魂特权.
function WuHunWGData:CheckWuhunPrerogativeIsActivate()
	return self.wuhun_prerogative_level > 0
end

function WuHunWGData:GetWuhunPrerogativeBuyInfo()
	local price, rmb_type, rmb_seq
	if self:CheckWuhunPrerogativeIsCanBuy() then
		local cfg = self:GetWuhunPrerogativeCfg(self.wuhun_prerogative_level + 1)
		price = cfg.price
		rmb_type = cfg.rmb_type
		rmb_seq = cfg.rmb_seq
	end

	return price, rmb_type, rmb_seq
end

function WuHunWGData:GetWuhunPrerogativeDrawItemList()
	local list_data = self:GetWuhunPrerogativeDrawAllItemList()
	local item_list = {}
	local idx = 1
	for key, value in ipairs(list_data) do
		if value.min_level > 0 then
			local data = {}
			data.item = value.item
			data.level = value.min_level
			data.cur_level = self.wuhun_prerogative_level
			item_list[idx] = data
			idx = idx + 1
		end
	end

	return item_list
end

function WuHunWGData:GetWuhunPrerogativeDrawAllItemList()
	local list_data = self.wuhun_prerogative_draw_cfg

	--排序.
	table.sort(list_data, function(a, b)
		if a.min_level < b.min_level then
			return true
		end

		local color1, color_num1 = ItemWGData:GetItemColor(a.item.item_id)
		local color2, color_num2 = ItemWGData:GetItemColor(b.item.item_id)

		if a.min_level == b.min_level and color_num1 > color_num2 then
			return true
		end

		if a.min_level == b.min_level and color_num1 == color_num2 and a.item.num < b.item.num then
			return true
		end

		return false
	end)

	return list_data
end

--获取武魂特权抽奖概率.
function WuHunWGData:GetWuhunPrerogativeDrawProbabilityCfg()
	local list_data = self.wuhun_prerogative_draw_cfg

	--排序.
	table.sort(list_data, function(a, b)
		if a.random_count < b.random_count then
			return true
		end

		local color1, color_num1 = ItemWGData:GetItemColor(a.item.item_id)
		local color2, color_num2 = ItemWGData:GetItemColor(b.item.item_id)

		if a.random_count == b.random_count and color_num1 > color_num2 then
			return true
		end

		if a.random_count == b.random_count and color_num1 == color_num2 and a.item.num < b.item.num then
			return true
		end

		return false
	end)

	return list_data
end

--是否能抽奖.
function WuHunWGData:GetWuhunPrerogativeIsDraw()
	local item_id = WuHunWGData.Instance:GetWuhunPrerogativeDrawCostItem()
	local num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	local cfg = self.wuhun_prerogative_draw_mode_cfg
	if nil == cfg or nil == cfg[1] then
		return
	end

	cfg = cfg[1]

	return num >= (cfg.cost_item_num or 0)
end

--是否在主界面显示红点.
function WuHunWGData:CheckWuhunPrerogativeIsShowRemind()
	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.WuHunPrerogativeView)
	if not is_open then
		return false
	end
	local is_can_draw = self:GetWuhunPrerogativeIsDraw()
	local is_activate = self:CheckWuhunPrerogativeIsActivate()
	local is_can_get_free_reward = self:GetWuhunPrerogativeFlag()

	return (is_activate and is_can_get_free_reward) or is_can_draw
end

-- 达到指定等级的武魂数量
function WuHunWGData:GetCountByLevel(level)
	local wuhun_list = self:GetAllWuhunlist()
	local count = 0
	if wuhun_list then
		for key, value in pairs(wuhun_list) do
			if not value.lock and value.level >= level then
				count = count + 1
			end
		end
	end
	return count
end
---------------------------------------------武魂特权 结束---------------------------------------------