require("game/onesword_frostbite/onesword_frostbite_view")
require("game/onesword_frostbite/onesword_frostbite_wg_data")
require("game/onesword_frostbite/onesword_frostbite_task_view")
require("game/onesword_frostbite/onesword_frostbite_reward_yulan_view")

OneSwordFrostbiteWGCtrl = OneSwordFrostbiteWGCtrl or BaseClass(BaseWGCtrl)
function OneSwordFrostbiteWGCtrl:__init()
    if OneSwordFrostbiteWGCtrl.Instance then
		ErrorLog("[OneSwordFrostbiteWGCtrl] Attemp to create a singleton twice !")
	end

    OneSwordFrostbiteWGCtrl.Instance = self
    self.data = OneSwordFrostbiteWGData.New()
    self.view = OneSwordFrostbiteView.New(GuideModuleName.OneSwordFrostbiteView)
    self.task_view = OneSwordFrostbiteTaskView.New(GuideModuleName.OneSwordFrostbiteTaskView)
    self.reward_yulan_view = OneSwordFrostbiteRewardYulanView.New()

	self:RegisterAllProtocols()

	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function OneSwordFrostbiteWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.task_view:DeleteMe()
	self.task_view = nil

    self.reward_yulan_view:DeleteMe()
	self.reward_yulan_view = nil

	if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

	OneSwordFrostbiteWGCtrl.Instance = nil
end

-- 注册协议
function OneSwordFrostbiteWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOASwordFrostbiteInfo, "OnOASwordFrostbiteInfo")
    self:RegisterProtocol(SCOASwordFrostbiteTaskUpdate, "OnOASwordFrostbiteTaskUpdate")
    self:RegisterProtocol(SCOASwordFrostbiteBoxUpdate, "OnOASwordFrostbiteBoxUpdate")
end

function OneSwordFrostbiteWGCtrl:OpenTaskView()
	self.task_view:Open()
end

function OneSwordFrostbiteWGCtrl:OpenRewardYulanView()
	self.reward_yulan_view:Open()
end

function OneSwordFrostbiteWGCtrl:SendOneSwordFrostbiteReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function OneSwordFrostbiteWGCtrl:OnOASwordFrostbiteInfo(protocol)
	local old_round = OneSwordFrostbiteWGData.Instance:GetCurRound()
	self.data:SetAllInfo(protocol)
	local new_round = OneSwordFrostbiteWGData.Instance:GetCurRound()
	if old_round > 0 and old_round ~= new_round and self.view:IsOpen() then
		self.view:PlayAnim()
	end

	if self.view:IsOpen() then
		self.view:Flush()
	end

    RemindManager.Instance:Fire(RemindName.OneSwordFrostbiteView)
	RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
	ViewManager.Instance:FlushView(GuideModuleName.MingWenView)
	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt)
end

function OneSwordFrostbiteWGCtrl:OnOASwordFrostbiteTaskUpdate(protocol)
	self.data:SetTaskInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end

    if self.task_view:IsOpen() then
		self.task_view:Flush()
	end

    RemindManager.Instance:Fire(RemindName.OneSwordFrostbiteView)
	RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
	ViewManager.Instance:FlushView(GuideModuleName.MingWenView)
	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt)
end

function OneSwordFrostbiteWGCtrl:OnOASwordFrostbiteBoxUpdate(protocol)
	self.data:SetBoxInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end

    RemindManager.Instance:Fire(RemindName.OneSwordFrostbiteView)
	RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
	ViewManager.Instance:FlushView(GuideModuleName.MingWenView)
	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt)
end

function OneSwordFrostbiteWGCtrl:PlayVideo()
	if self.view:IsOpen() then
		self.view:PlayVideo()
	end
end

-- 活动改变
function OneSwordFrostbiteWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE and status == ACTIVITY_STATUS.CLOSE then
		if self.view:IsOpen() then
			self.view:Close()
		end
    end
end