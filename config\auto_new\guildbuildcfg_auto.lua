-- X-仙盟建设任务.xls
local item_table={
[1]={item_id=36422,num=90,is_bind=0},
[2]={item_id=90050,num=1,is_bind=0},
[3]={item_id=26349,num=5,is_bind=1},
[4]={item_id=36538,num=556,is_bind=0},
[5]={item_id=36422,num=113,is_bind=0},
[6]={item_id=26349,num=6,is_bind=1},
[7]={item_id=36538,num=694,is_bind=0},
[8]={item_id=36422,num=180,is_bind=0},
[9]={item_id=26349,num=10,is_bind=1},
[10]={item_id=36538,num=1111,is_bind=0},
[11]={item_id=28694,num=1,is_bind=1},
[12]={item_id=36422,num=225,is_bind=0},
[13]={item_id=26349,num=13,is_bind=1},
[14]={item_id=36538,num=1389,is_bind=0},
[15]={item_id=26349,num=12,is_bind=1},
[16]={item_id=26349,num=15,is_bind=1},
[17]={item_id=26349,num=8,is_bind=1},
[18]={item_id=26349,num=7,is_bind=1},
[19]={item_id=26349,num=9,is_bind=1},
[20]={item_id=26349,num=14,is_bind=1},
[21]={item_id=26349,num=18,is_bind=1},
[22]={item_id=26349,num=16,is_bind=1},
[23]={item_id=26349,num=20,is_bind=1},
[24]={item_id=26349,num=11,is_bind=1},
[25]={item_id=26349,num=23,is_bind=1},
[26]={item_id=26349,num=25,is_bind=1},
}

return {
guild_build_task_cfg={
{img_icon_name="guild_task_icon_1_1",reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},task_content="最近魔界企图进攻凌霄城，将这个消息传达给大陆上的居民。",},
{task_id=31002,},
{task_id=31003,},
{task_id=31004,},
{task_id=31005,},
{task_id=31006,},
{task_id=31007,},
{task_id=31008,},
{task_id=31009,},
{task_id=31010,},
{task_id=31051,},
{task_id=31052,},
{task_id=31053,},
{task_id=31054,},
{task_id=31055,},
{task_id=31056,},
{task_id=31057,},
{task_id=31058,},
{task_id=31059,},
{task_id=31060,},
{task_id=31101,},
{task_id=31102,task_content="最近魔界蠢蠢欲动，向居民们了解魔界动向。",},
{task_id=31103,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},},
{task_id=31104,},
{task_id=31105,},
{task_id=31106,},
{task_id=31107,},
{task_id=31108,},
{task_id=31109,},
{task_id=31110,},
{task_id=31151,},
{task_id=31152,},
{task_id=31153,},
{task_id=31154,},
{task_id=31155,},
{task_id=31156,},
{task_id=31157,},
{task_id=31158,},
{task_id=31159,},
{task_id=31160,},
{task_id=31201,},
{task_id=31202,},
{task_id=31203,},
{task_id=31204,},
{task_id=31205,},
{task_id=31206,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[6],[3]=item_table[7]},},
{task_id=31207,},
{task_id=31208,},
{task_id=31209,reward_item_1={[0]=item_table[8],[1]=item_table[2],[2]=item_table[9],[3]=item_table[10]},},
{task_id=31210,reward_item_1={[0]=item_table[11],[1]=item_table[12],[2]=item_table[2],[3]=item_table[13],[4]=item_table[14]},},
{task_id=31251,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[6],[3]=item_table[4]},},
{task_id=31252,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[6],[3]=item_table[4]},},
{task_id=31253,},
{task_id=31254,},
{task_id=31255,},
{task_id=31256,},
{task_id=31257,},
{task_id=31258,reward_item_1={[0]=item_table[8],[1]=item_table[2],[2]=item_table[15],[3]=item_table[10]},},
{task_id=31259,},
{task_id=31260,reward_item_1={[0]=item_table[11],[1]=item_table[12],[2]=item_table[2],[3]=item_table[16],[4]=item_table[14]},},
{task_id=31301,},
{task_id=31302,},
{task_id=31303,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[6],[3]=item_table[4]},},
{task_id=31304,},
{task_id=31305,},
{task_id=31306,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[17],[3]=item_table[7]},},
{task_id=31307,},
{task_id=31308,},
{task_id=31309,},
{task_id=31310,},
{task_id=31351,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[18],[3]=item_table[4]},},
{task_id=31352,task_content="最近魔界蠢蠢欲动，向居民们了解魔界动向。",},
{task_id=31353,},
{task_id=31354,},
{task_id=31355,},
{task_id=31356,},
{task_id=31357,},
{task_id=31358,},
{task_id=31359,},
{task_id=31360,},
{task_id=31401,},
{task_id=31402,},
{task_id=31403,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[18],[3]=item_table[4]},},
{task_id=31404,},
{task_id=31405,},
{task_id=31406,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[19],[3]=item_table[7]},},
{task_id=31407,},
{task_id=31408,},
{task_id=31409,reward_item_1={[0]=item_table[8],[1]=item_table[2],[2]=item_table[20],[3]=item_table[10]},},
{task_id=31410,reward_item_1={[0]=item_table[11],[1]=item_table[12],[2]=item_table[2],[3]=item_table[21],[4]=item_table[14]},},
{task_id=31451,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[17],[3]=item_table[4]},},
{task_id=31452,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[17],[3]=item_table[4]},},
{task_id=31453,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[17],[3]=item_table[4]},},
{task_id=31454,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[9],[3]=item_table[7]},},
{task_id=31455,},
{task_id=31456,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[22],[3]=item_table[7]},},
{task_id=31457,},
{task_id=31458,},
{task_id=31459,reward_item_1={[0]=item_table[8],[1]=item_table[2],[2]=item_table[22],[3]=item_table[10]},},
{task_id=31460,reward_item_1={[0]=item_table[11],[1]=item_table[12],[2]=item_table[2],[3]=item_table[23],[4]=item_table[14]},},
{task_id=31501,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[19],[3]=item_table[4]},},
{task_id=31502,task_content="最近魔界蠢蠢欲动，向居民们了解魔界动向。",},
{task_id=31503,},
{task_id=31504,},
{task_id=31505,},
{task_id=31506,},
{task_id=31507,},
{task_id=31508,},
{task_id=31509,},
{task_id=31510,},
{task_id=31551,},
{task_id=31552,},
{task_id=31553,reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[19],[3]=item_table[4]},},
{task_id=31554,},
{task_id=31555,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[24],[3]=item_table[7]},},
{task_id=31556,},
{task_id=31557,},
{task_id=31558,},
{task_id=31559,reward_item_1={[0]=item_table[8],[1]=item_table[2],[2]=item_table[21],[3]=item_table[10]},},
{task_id=31560,reward_item_1={[0]=item_table[11],[1]=item_table[12],[2]=item_table[2],[3]=item_table[25],[4]=item_table[14]},},
{task_id=31601,},
{task_id=31602,},
{task_id=31603,},
{task_id=31604,},
{task_id=31605,},
{task_id=31606,},
{task_id=31607,},
{task_id=31608,},
{task_id=31609,},
{task_id=31610,},
{task_id=31651,},
{task_id=31652,},
{task_id=31653,},
{task_id=31654,},
{task_id=31655,},
{task_id=31656,},
{task_id=31657,},
{task_id=31658,},
{task_id=31659,},
{task_id=31660,},
{task_id=31701,},
{task_id=31702,},
{task_id=31703,},
{task_id=31704,quality=2,reward_item_1={[0]=item_table[5],[1]=item_table[2],[2]=item_table[13],[3]=item_table[7]},},
{task_id=31705,},
{task_id=31706,},
{task_id=31707,},
{task_id=31708,},
{task_id=31709,},
{task_id=31710,},
{task_id=31751,img_icon_name="guild_task_icon_1_1",task_content="最近魔界企图进攻凌霄城，将这个消息传达给大陆上的居民。",},
{task_id=31752,task_content="最近魔界蠢蠢欲动，向居民们了解魔界动向。",},
{task_id=31753,img_icon_name="guild_task_icon_1_2",task_content="解决思成与姜大娘之间的矛盾，劝说思成。",},
{task_id=31754,},
{task_id=31755,},
{task_id=31756,},
{task_id=31757,},
{task_id=31758,},
{task_id=31759,},
{task_id=31760,},
{task_id=31801,},
{task_id=31802,},
{task_id=31803,},
{task_id=31804,},
{task_id=31805,},
{task_id=31806,},
{task_id=31807,},
{task_id=31808,},
{task_id=31809,},
{task_id=31810,quality=4,img_icon_name="guild_task_icon_4",reward_item_1={[0]=item_table[11],[1]=item_table[12],[2]=item_table[2],[3]=item_table[26],[4]=item_table[14]},task_content="情况紧急，魔界试图进攻凌霄城，我们要赶在他们进攻之前消灭他们！",},
{task_id=31851,},
{task_id=31852,},
{task_id=31853,},
{task_id=31854,},
{task_id=31855,},
{task_id=31856,},
{task_id=31857,quality=3,img_icon_name="guild_task_icon_3",reward_item_1={[0]=item_table[8],[1]=item_table[2],[2]=item_table[23],[3]=item_table[10]},task_content="为了仙盟的发展，需要收集一些稀有物品，麻烦你跑一趟。",},
{task_id=31858,},
{task_id=31859,},
{task_id=31860,}
},

guild_build_task_cfg_meta_table_map={
[145]=144,	-- depth:1
[146]=145,	-- depth:2
[86]=144,	-- depth:1
[85]=86,	-- depth:2
[84]=85,	-- depth:3
[76]=84,	-- depth:4
[75]=76,	-- depth:5
[74]=75,	-- depth:6
[152]=151,	-- depth:1
[154]=146,	-- depth:3
[66]=144,	-- depth:1
[65]=66,	-- depth:2
[155]=154,	-- depth:4
[156]=155,	-- depth:5
[56]=65,	-- depth:3
[55]=56,	-- depth:4
[143]=153,	-- depth:1
[94]=144,	-- depth:1
[95]=94,	-- depth:2
[96]=144,	-- depth:1
[126]=156,	-- depth:6
[125]=126,	-- depth:7
[124]=125,	-- depth:8
[123]=143,	-- depth:2
[122]=152,	-- depth:2
[121]=151,	-- depth:1
[131]=121,	-- depth:2
[132]=122,	-- depth:3
[133]=123,	-- depth:3
[54]=55,	-- depth:5
[134]=124,	-- depth:9
[115]=144,	-- depth:1
[114]=115,	-- depth:2
[135]=134,	-- depth:10
[136]=135,	-- depth:11
[106]=114,	-- depth:3
[105]=106,	-- depth:4
[104]=105,	-- depth:5
[141]=131,	-- depth:3
[142]=132,	-- depth:4
[116]=104,	-- depth:6
[161]=141,	-- depth:4
[64]=54,	-- depth:6
[46]=144,	-- depth:1
[166]=136,	-- depth:12
[36]=46,	-- depth:2
[35]=36,	-- depth:3
[34]=35,	-- depth:4
[16]=34,	-- depth:5
[175]=166,	-- depth:13
[162]=142,	-- depth:5
[26]=16,	-- depth:6
[25]=26,	-- depth:7
[24]=25,	-- depth:8
[171]=161,	-- depth:5
[14]=24,	-- depth:9
[172]=162,	-- depth:6
[173]=133,	-- depth:4
[174]=175,	-- depth:14
[165]=174,	-- depth:15
[164]=165,	-- depth:16
[176]=164,	-- depth:17
[6]=14,	-- depth:10
[45]=6,	-- depth:11
[44]=45,	-- depth:12
[163]=173,	-- depth:5
[5]=44,	-- depth:13
[4]=5,	-- depth:14
[15]=4,	-- depth:15
[63]=153,	-- depth:1
[23]=153,	-- depth:1
[22]=1,	-- depth:1
[21]=1,	-- depth:1
[53]=63,	-- depth:2
[52]=22,	-- depth:2
[113]=153,	-- depth:1
[101]=151,	-- depth:1
[102]=101,	-- depth:2
[103]=113,	-- depth:2
[51]=151,	-- depth:1
[3]=23,	-- depth:2
[2]=22,	-- depth:2
[112]=102,	-- depth:3
[111]=101,	-- depth:2
[93]=153,	-- depth:1
[92]=22,	-- depth:2
[91]=151,	-- depth:1
[62]=52,	-- depth:3
[41]=21,	-- depth:2
[33]=3,	-- depth:3
[32]=2,	-- depth:3
[42]=32,	-- depth:4
[71]=151,	-- depth:1
[72]=71,	-- depth:2
[82]=72,	-- depth:3
[81]=71,	-- depth:2
[31]=41,	-- depth:3
[11]=31,	-- depth:4
[12]=42,	-- depth:5
[13]=33,	-- depth:4
[61]=51,	-- depth:2
[43]=13,	-- depth:5
[83]=153,	-- depth:1
[73]=83,	-- depth:2
[178]=177,	-- depth:1
[138]=178,	-- depth:2
[160]=170,	-- depth:1
[159]=138,	-- depth:3
[158]=159,	-- depth:4
[157]=158,	-- depth:5
[130]=160,	-- depth:2
[150]=130,	-- depth:3
[137]=157,	-- depth:6
[149]=137,	-- depth:7
[147]=149,	-- depth:8
[167]=147,	-- depth:9
[168]=167,	-- depth:10
[169]=168,	-- depth:11
[140]=150,	-- depth:4
[139]=169,	-- depth:12
[148]=139,	-- depth:13
[129]=148,	-- depth:14
[90]=170,	-- depth:1
[127]=129,	-- depth:15
[58]=177,	-- depth:1
[57]=58,	-- depth:2
[50]=170,	-- depth:1
[49]=177,	-- depth:1
[48]=49,	-- depth:2
[47]=48,	-- depth:3
[40]=50,	-- depth:2
[39]=47,	-- depth:4
[38]=39,	-- depth:5
[37]=38,	-- depth:6
[59]=57,	-- depth:3
[30]=40,	-- depth:3
[28]=37,	-- depth:7
[27]=28,	-- depth:8
[20]=30,	-- depth:4
[19]=27,	-- depth:9
[18]=19,	-- depth:10
[17]=18,	-- depth:11
[10]=20,	-- depth:5
[9]=17,	-- depth:12
[8]=9,	-- depth:13
[7]=8,	-- depth:14
[29]=7,	-- depth:15
[128]=127,	-- depth:16
[60]=170,	-- depth:1
[68]=59,	-- depth:4
[120]=170,	-- depth:1
[119]=177,	-- depth:1
[118]=119,	-- depth:2
[117]=118,	-- depth:3
[110]=120,	-- depth:2
[109]=117,	-- depth:4
[108]=109,	-- depth:5
[107]=108,	-- depth:6
[100]=170,	-- depth:1
[99]=177,	-- depth:1
[67]=68,	-- depth:5
[98]=99,	-- depth:2
[179]=128,	-- depth:17
[89]=177,	-- depth:1
[88]=89,	-- depth:2
[87]=88,	-- depth:3
[80]=90,	-- depth:2
[79]=87,	-- depth:4
[78]=79,	-- depth:5
[77]=78,	-- depth:6
[70]=60,	-- depth:2
[69]=67,	-- depth:6
[97]=98,	-- depth:3
[180]=140,	-- depth:5
},
other={
{}
},

other_meta_table_map={
},
gold_xiaohao={
{}
},

gold_xiaohao_meta_table_map={
},
ran_dom_cfg={
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

ran_dom_cfg_meta_table_map={
},
guild_build_task_cfg_default_table={task_id=31001,quality=1,img_icon_name="guild_task_icon_2_1",reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[9],[3]=item_table[4]},task_content="最近仙界大陆不太平，作为仙界守护者，需要消除盘踞在大陆上的怪物。",},

other_default_table={free_num=4,day_max_task_num=5,refresh_s_times=3,},

gold_xiaohao_default_table={time_minimum=1,time_maximum=9999,need_gold=20,},

ran_dom_cfg_default_table={}

}

