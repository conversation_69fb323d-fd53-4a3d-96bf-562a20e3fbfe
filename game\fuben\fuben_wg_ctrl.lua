require("game/fuben/fuben_wg_data")
require("game/fuben/fuben_guild_mijing_logic_view")
require("game/fuben/fuben_next_view")
require("game/fuben/fuben_lose_view")

require("game/fuben/defensefuben/fuben_defense_fb_view")
require("game/fuben/defensefuben/fuben_defense_fb_tower_view")
require("game/fuben/defensefuben/fuben_defense_fb_tower_reform_view")
require("game/fuben/defensefuben/fuben_defense_fb_wg_data")
require("game/fuben/defensefuben/fuben_defense_fb_btn_view")
require("game/fuben/defensefuben/tower_attr_view")

require("game/fuben/wujinjitanfuben/fuben_wujin_jitan_wg_data")
require("game/fuben/wujinjitanfuben/fubenpanel_team_exp_info_view")
require("game/fuben/wujinjitanfuben/fubenpanel_team_exp_medicine_view")
require("game/fuben/wujinjitanfuben/fubenpanel_team_cheer_view")
require("game/fuben/team_equip_fb/team_equip_fb_wg_data")
require("game/fuben/fuben_common_win")
require("game/fuben/fuben_new_player_view")
require("game/fuben/fuben_new_player_xiuwei_view")
require("game/fubenpanel/yuanguxiandian_logic_view")
require("game/fuben/fuben_zhushenta_view")

require("game/fuben/fuben_layer_view")
require("game/fuben/manhuang_gudian_wg_data")
require("game/fuben/guild_shouhu_rank_view")
require("game/fuben/fuben_notice_view")


FuBenWGCtrl = FuBenWGCtrl or BaseClass(BaseWGCtrl)

function FuBenWGCtrl:__init()
	if FuBenWGCtrl.Instance then
		error("[FuBenWGCtrl]:Attempt to create singleton twice!")
	end
	FuBenWGCtrl.Instance = self

	self.data = FuBenWGData.New()
	self.win_view = FuBenCommonWinView.New()	-- 通用获胜界面
	self.lose_view = FuBenLoseView.New()		-- 通用失败界面

	self.next_view = FuBenNextView.New()
	self.fuben_mijing_view = FuBenMiJingView.New(GuideModuleName.FuBenMiJingView)					-- 仙盟守护
	self.guild_shouhu_rank_view = GuildShowHuRankView.New(GuideModuleName.GuildShowHuRankView)
	self.level_fb_alert = Alert.New()
	self.fuben_notice_view = FuBennoticeView.New()
	self.defense_fb_view = DefenseFbView.New()
	self.defense_fb_tower_view = DefenseFbTowerView.New()
	self.defense_fb_tower_feform_view = DefenseFbTowerReformView.New()
	self.defense_fb_data = DefenseFbWGData.New()
	self.defense_fb_btn = DefenseFbSceneBtnView.New()
	self.tower_attr_view = TowerAttrView.New()

	--多人经验本
	self.wujin_jitan_data = WuJinJiTanWGData.New()
	self.team_expfb_info_view = TeamExpFbInfoView.New()
	self.team_expfb_medicine_view = TeamExpFbMedicineView.New(GuideModuleName.TeamExpFBMedicineView)
	self.team_cheer_view = TeamFuBenCheerView.New()

	--多人装备本
	self.team_equip_fb_data = TeamEquipFbWGData.New()

	--新手副本
	self.fuben_new_player_view = FuBenNewPlayerView.New(GuideModuleName.FBNewPlayerView)
	self.fuben_new_player_xiuwei_view = FuBenNewPlayerXiuWeiView.New()

	-- 远古仙殿
	self.yuanguxiandian_logic_view = YuanGuXianDianLogicView.New(GuideModuleName.YuanGuXianDianLogicView)

	--诛神塔
	self.zhushenta_view = ZhuShenTaView.New()
	-- self.fuben_layer_view = FuBenLayerView.New()
	self.fuben_combine = FuBenCombine.New()
	self.fuben_saodang = FuBenSaoDang.New()

	self:InitLeaveFbAlert()
	self:RegisterAllProtocols()
	self:BindEvent()

	self.manhuang_gudian_data = ManHuangGuDianWGData.New()
end

function FuBenWGCtrl:BindEvent()
	self.play_star_event = self:BindGlobalEvent(SceneEventType.CLOSE_LOADING_VIEW,BindTool.Bind(self.SceneLoadComplete,self))
end

function FuBenWGCtrl:UnBindEvent()
	if self.play_star_event then
		self:UnBind(self.play_star_event)
		self.play_star_event = nil
	end

	if self.monitor_change_scene_win_fb_event then
		GlobalEventSystem:UnBind(self.monitor_change_scene_win_fb_event)
		self.monitor_change_scene_win_fb_event = nil
	end

	if self.monitor_change_scene_lose_fb_event then
		GlobalEventSystem:UnBind(self.monitor_change_scene_lose_fb_event)
		self.monitor_change_scene_lose_fb_event = nil
	end
end

function FuBenWGCtrl:SceneLoadComplete()
	self:ShowZhuZhanTips()
	if self.yuangu_guid_mark then
	   self:ShowYuanGuGuidWin()
	end
	if not self.guwu_timer then
		self.guwu_timer = GlobalTimerQuest:AddDelayTimer(function ()
			self.guwu_timer = nil
			self:CheckGuwuAuto()
		end, 0.5)
	end
end

function FuBenWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil
	self.win_view:DeleteMe()
	self.win_view = nil
	self.lose_view:DeleteMe()
	self.lose_view = nil

	self.next_view:DeleteMe()
    self.next_view = nil

	if self.fuben_combine then
		self.fuben_combine:DeleteMe()
		self.fuben_combine = nil
	end

	if self.fuben_saodang then
		self.fuben_saodang:DeleteMe()
		self.fuben_saodang = nil
	end

	if self.tower_attr_view then
		self.tower_attr_view:DeleteMe()
		self.tower_attr_view = nil
	end

	self.fuben_mijing_view:DeleteMe()
	self.fuben_mijing_view = nil

	if nil ~= self.guild_shouhu_rank_view then
		self.guild_shouhu_rank_view:DeleteMe()
		self.guild_shouhu_rank_view = nil
	end

	self.level_fb_alert:DeleteMe()
	self.level_fb_alert = nil

	self.defense_fb_data:DeleteMe()
	self.defense_fb_data = nil

	self.defense_fb_view:DeleteMe()
	self.defense_fb_view = nil

	self.defense_fb_btn:DeleteMe()
	self.defense_fb_btn = nil

	self.defense_fb_tower_view:DeleteMe()
	self.defense_fb_tower_view = nil

	self.defense_fb_tower_feform_view:DeleteMe()
	self.defense_fb_tower_feform_view = nil

	self.wujin_jitan_data:DeleteMe()
	self.wujin_jitan_data = nil

	self.manhuang_gudian_data:DeleteMe()
	self.manhuang_gudian_data = nil

	self.team_expfb_info_view:DeleteMe()
	self.team_expfb_info_view = nil

	self.team_expfb_medicine_view:DeleteMe()
	self.team_expfb_medicine_view = nil

	self.team_cheer_view:DeleteMe()
	self.team_cheer_view = nil

	self.team_equip_fb_data:DeleteMe()
	self.team_equip_fb_data = nil

	self.is_tafang_zhaohuan_fb = nil
	self.fuben_new_player_view:DeleteMe()
	self.fuben_new_player_view = nil

	self.fuben_new_player_xiuwei_view:DeleteMe()
	self.fuben_new_player_xiuwei_view = nil

	self.zhushenta_view:DeleteMe()
	self.zhushenta_view = nil

	self.yuanguxiandian_logic_view:DeleteMe()
	self.yuanguxiandian_logic_view = nil

	self.fuben_notice_view:DeleteMe()
	self.fuben_notice_view = nil

	-- if self.fuben_layer_view then
	-- 	self.fuben_layer_view:DeleteMe()
	-- 	self.fuben_layer_view = nil
	-- end

	if self.weapon_fb_timer then
		GlobalTimerQuest:CancelQuest(self.weapon_fb_timer)
		self.weapon_fb_timer = nil
	end

	if self.guwu_timer then
		GlobalTimerQuest:CancelQuest(self.guwu_timer)
		self.guwu_timer = nil
	end

	if self.team_exp_win_delay then
		GlobalTimerQuest:CancelQuest(self.team_exp_win_delay)
	end

	if self.team_exp_loser_delay then
		GlobalTimerQuest:CancelQuest(self.team_exp_loser_delay)
	end

	if self.zhushenta_loser_delay then
		GlobalTimerQuest:CancelQuest(self.zhushenta_loser_delay)
	end

	if self.team_equip_win_delay then
		GlobalTimerQuest:CancelQuest(self.team_equip_win_delay)
	end

	if self.team_equip_loser_delay then
		GlobalTimerQuest:CancelQuest(self.team_equip_loser_delay)
	end

	if self.fuben_task_timer then
		GlobalTimerQuest:CancelQuest(self.fuben_task_timer)
		self.fuben_task_timer = nil
	end

	self.change_scene_jiesuan_win_param = nil
	self.change_scene_jiesuan_lose_param = nil
    self.check_is_finish_pick = nil

    if self.gold_guwu_alert then
    	self.gold_guwu_alert:DeleteMe()
    	self.gold_guwu_alert = nil
    end

    self:UnBindEvent()
    GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
	FuBenWGCtrl.Instance = nil

end

function FuBenWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSEnterFB)
	self:RegisterProtocol(CSLeaveFB)
	self:RegisterProtocol(CSAutoFB)
	-- self:RegisterProtocol(CSBuyFBTimes)

	--副本场景信息通用
	self:RegisterProtocol(SCFBSceneLogicInfo, "OnFbSceneLogicInfo")
	self:RegisterProtocol(SCSceneFbTimeOut, "OnSceneFbTimeOut")

	self:RegisterProtocol(SCFbCanEnterResult, "OnFbCanEnterResult")

	--装备本
	self:RegisterProtocol(SCEquipFbSceneInfo, "OnEquipFbSceneInfo")

	--福利本
	self:RegisterProtocol(SCWelfareFbSceneInfo, "OnWelfareFbSceneInfo")

	--爬塔本
	self:RegisterProtocol(CSFBReqNextLevel)
	self:RegisterProtocol(CSTianXianGeOneKey)

	--多人-组队塔防
	self:RegisterProtocol(SCTeamTowerdefendFbInfo, "OnTeamTowerdefendFbInfo")
	-- self:RegisterProtocol(SCTeamTowerDefendWarning, "OnTeamTowerDefendWarning")
	self:RegisterProtocol(CSTeamTowerdefendFbOperate)
	--多人-迷宫仙府
	self:RegisterProtocol(SCMgxfTeamFbSceneLogicInfo, "OnMgxfTeamFbSceneLogicInfo")
	self:RegisterProtocol(CSMgxfTeamFbTouchDoor)
	--多人-无尽祭坛
	self:RegisterProtocol(SCTeamWujinjitanFbInfo, "OnTeamWujinjitanFbInfo")

	-- 个人塔防
	self:RegisterProtocol(SCTowerDefendInfo, "OnTowerDefendInfo")

	--宠装本
	self:RegisterProtocol(SCWeaponFbSceneInfo, "OnWeaponFbSceneInfo")

	--副本鼓舞
	self:RegisterProtocol(CSFbGuwu)

	--掉落
	self:RegisterProtocol(SCFbPickInfo, "SCFbPickInfo")

	--塔防副本信息
	self:RegisterProtocol(CSBuildTowerReq)
	self:RegisterProtocol(SCBuildTowerFBSceneLogicInfo, "OnBuildTowerFBSceneLogicInfo")

	-- 多人经验本
	self:RegisterProtocol(CSWuJinJiTanReq)
	self:RegisterProtocol(SCWuJinJiTanAllInfo, "OnSCWuJinJiTanAllInfo")
	self:RegisterProtocol(SCWuJinJiTanRoleInfo, "OnSCWuJinJiTanRoleInfo")

	self:RegisterProtocol(CSSetExpFBAutoGuwu)
	self:RegisterProtocol(SCSetExpFBAutoGuwu, "OnSCSetExpFBAutoGuwu")



	-- 多人装备本
	self:RegisterProtocol(SCTeamEquipFBInfo, "OnSCTeamEquipFBInfo")
	self:RegisterProtocol(SCTeamEquipFBRoleInfo, "OnSCTeamEquipFBRoleInfo")

	--副本掉落
	self:RegisterProtocol(SCFBDropInfo, "OnSCFBDropInfo")

	-- 新手副本信息
	self:RegisterProtocol(SCNewPlayerFBInfo, "OnSCNewPlayerFBInfo")
	--新手副本通关信息
	self:RegisterProtocol(SCNewPlayerFbPassInfo, "OnSCNewPlayerFbPassInfo")


	-- 远古仙殿高阶多人装备本
	self:RegisterProtocol(SCYuanGuFBInfo, "OnYuanGuFBInfo")
	-- self:RegisterProtocol(SCYuanGuFBRoleInfo, "OnYuanGuFBRoleInfo")
	self:RegisterProtocol(CSYuanGuFBReq)
	self:RegisterProtocol(CSSetSpecialTempReq)

	--诛神塔副本
	self:RegisterProtocol(SCKillGodTwoerBossInfo,"OnSCKillGodTwoerBossInfo")
	self:RegisterProtocol(SCKillGodTwoerFBInfo, "OnSCKillGodTwoerFBInfo")
	self:RegisterProtocol(SCKillGodTowerSceneInfo, "OnSCKillGodTowerSceneInfo")
	self:RegisterProtocol(SCKillGodFBResult, "OnSCKillGodFBResult")

	--场景中请求进入其他场景的操作返回
	self:RegisterProtocol(SCFbCanEnterOtherFBResult, "OnSCFbCanEnterOtherFBResult")
	self:RegisterProtocol(SCWuJinJiTanFirstTimeInfo, "OnSCWuJinJiTanFirstTimeInfo")


	self:RegisterProtocol(CSFBUseCombine)
	self:RegisterProtocol(SCFBUseCombine, "OnSCFBUseCombine")
	--仙盟守护退出倒计时
	self:RegisterProtocol(SCGuildFbStatus, "OnSCGuildFbStatus")


	self:RegisterProtocol(CSFBPlayedCG) --播放完CG后请求

	--八卦迷阵副本信息
	self:RegisterProtocol(SCBaGuaMiZhenInFbInfoRet, "OnBaGuaMiZhenInFbInfoRet")


	self:RegisterProtocol(SCFbGuWuInfo, "OnFbGuWuInfo")


	self:RegisterProtocol(CSManHuangGuDianOpera)
	self:RegisterProtocol(CSManHuangGuDianReq)										--请求蛮荒古殿 1:请求个人信息 2:请求排行榜信息
	self:RegisterProtocol(SCManHuangGuDianBaseInfo, "OnSCManHuangGuDianBaseInfo")	--蛮荒古殿基本信息（8892）
	self:RegisterProtocol(SCManHuangGuDianRankAck, "OnSCManHuangGuDianRankAck")		--蛮荒古殿排行信息（8893）
	self:RegisterProtocol(SCManHuangGuDianFBInfo, "OnSCManHuangGuDianFBInfo") 		--蛮荒古殿副本场景信息（8890）
    self:RegisterProtocol(SCLingHunSquareEnterTimes, "OnSCLingHunSquareEnterTimes") 		--是否进入过经验本
	----------------------------------------------------
	--副本道具使用（boss刷新卡，boss疲劳药水）
	self:RegisterProtocol(CSFBUseItemReq)
	self:RegisterProtocol(CSDecBossTired)
	-----------------------------
end

---[[通用获胜界面 场景,奖励,经验,金币,星级,结束时间,杀敌数,是否下一关
function FuBenWGCtrl:SetCommonWinData(scene_type, data_list, exp, gold, star_num, fb_end_time, skill_index, is_enter_next, reward_type, close_call_back)
	--print_error("===============",scene_type,"data_list",data_list,"exp",exp,"gold",gold,"star_num",star_num)
	self.win_view:SetCommonWinData(scene_type, data_list, exp, gold, star_num, fb_end_time, skill_index, is_enter_next, nil, reward_type, close_call_back)
	FuBenPanelWGCtrl.Instance.tafang_saodang:Close()
end

--不需要考虑主动离开标记是否被 CheckLeaveOpenView 清掉，服务器先下发结算，在离开场景的
function FuBenWGCtrl:OpenWin(scene_type, data_list, exp, gold, star_num, fb_end_time, skill_index, is_enter_next, reward_type)
	local func = function ()
		if FuBenWGData.Instance:GetFuBenInitiativeToLeave(scene_type) then -- 主动点击退出需要切完场景再弹出来
			if self.monitor_change_scene_win_fb_event then
				GlobalEventSystem:UnBind(self.monitor_change_scene_win_fb_event)
				self.monitor_change_scene_win_fb_event = nil
			end
			self.change_scene_jiesuan_win_param = {}
			self.change_scene_jiesuan_win_param.scene_type = scene_type
			self.change_scene_jiesuan_win_param.data_list = data_list
			self.change_scene_jiesuan_win_param.exp = exp
			self.change_scene_jiesuan_win_param.gold = gold
			self.change_scene_jiesuan_win_param.star_num = star_num
			self.change_scene_jiesuan_win_param.fb_end_time = fb_end_time
			self.change_scene_jiesuan_win_param.skill_index = skill_index
			self.change_scene_jiesuan_win_param.is_enter_next = is_enter_next
			self.change_scene_jiesuan_win_param.reward_type = reward_type
			self.monitor_change_scene_win_fb_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW,BindTool.Bind(self.WinFbChangeSceneLoadComplete,self))
		else
			--非主动点击退出，直接弹出来
			self:SetCommonWinData(scene_type, data_list, exp, gold, star_num, fb_end_time, skill_index, is_enter_next, reward_type)
		end
	end

	RareItemDropWGCtrl.Instance:AddEndShowRareItemCallBack(func)
end

function FuBenWGCtrl:FlushWinView()
	if self.win_view:IsOpen() then
		self.win_view:Flush()
	end
end
--]]

---[[ 通用失败界面
function FuBenWGCtrl:OpenFuBenLoseView(close_delay, scene_type)
	self.lose_view:SetSceneType(scene_type)
	self.lose_view:FbCloseTime(close_delay or 10)
	self.lose_view:Open()
end

function FuBenWGCtrl:CloseLoseView()
	if self.lose_view:IsOpen() then
		self.lose_view:Close()
	end
end

function FuBenWGCtrl:OpenLose(scene_type, param)
	FuBenWGData.Instance:SetLoseSceneTypeChche(scene_type)
	--主动点击退出需要切完场景再弹出来
	if FuBenWGData.Instance:GetFuBenInitiativeToLeave(scene_type) then
		if self.monitor_change_scene_lose_fb_event then
			GlobalEventSystem:UnBind(self.monitor_change_scene_lose_fb_event)
			self.monitor_change_scene_lose_fb_event = nil
		end

		self.change_scene_jiesuan_lose_param = param
		self.monitor_change_scene_lose_fb_event = GlobalEventSystem:Bind(SceneEventType.SCENE_ALL_LOAD_COMPLETE,BindTool.Bind(self.LoseFbChangeSceneLoadComplete,self,scene_type))
	else
		--非主动点击退出，直接弹出来
		if scene_type == SceneType.ZHUANZHI_FB then
			self:OpenFuBenLoseView(15, SceneType.ZHUANZHI_FB)
		else
			self:OpenFuBenLoseView()
		end
	end
end

--失败界面点击变强按钮
function FuBenWGCtrl:LoseViewCilckLeaveFB(click_type)
	if Scene.Instance:GetSceneType() ~= SceneType.Common then
		FuBenWGData.Instance:SetFuBenInitiativeToLeave(Scene.Instance:GetSceneType(), true)
		FuBenWGCtrl.Instance:SendLeaveFB()
		if self.monitor_change_scene_lose_fb_event then
			GlobalEventSystem:UnBind(self.monitor_change_scene_lose_fb_event)
			self.monitor_change_scene_lose_fb_event = nil
		end
		self.monitor_change_scene_lose_fb_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW,BindTool.Bind(self.LoseViewClickOpenView, self, click_type))
	else
		self:LoseViewClickOpenView(click_type)
	end
end

--click_type 1幻兽提升 2幻兽奇遇 3气衍万劫 4每日礼包 5首冲
function FuBenWGCtrl:LoseViewClickOpenView(click_type)
	if self.monitor_change_scene_lose_fb_event then
		GlobalEventSystem:UnBind(self.monitor_change_scene_lose_fb_event)
		self.monitor_change_scene_lose_fb_event = nil
	end
	local str = ""
	local tab_index = nil
	if click_type == 5 then
		str = GuideModuleName.FirstRechargeView
	elseif click_type == 4 then
		str = GuideModuleName.BillionSubsidy
		tab_index = "billion_subsidy_dailygift"
	elseif click_type == 3 then
		str = GuideModuleName.XiuWeiView
	elseif click_type == 2 then
		str = GuideModuleName.ControlBeastsPrizeDrawWGView
	elseif click_type == 1 then
		str = GuideModuleName.ControlBeastsView
	end
	FunOpen.Instance:OpenViewByName(str, tab_index)
end

function FuBenWGCtrl:LoseFbChangeSceneLoadComplete(scene_type)
	if self.monitor_change_scene_lose_fb_event then
		GlobalEventSystem:UnBind(self.monitor_change_scene_lose_fb_event)
		self.monitor_change_scene_lose_fb_event = nil
	end
	if self.change_scene_jiesuan_lose_param then
		GlobalTimerQuest:AddDelayTimer(function()
			self:OpenFuBenLoseView(self.change_scene_jiesuan_lose_param, scene_type)
			self.change_scene_jiesuan_lose_param = nil
		end,0.5)
	else
		self:OpenFuBenLoseView()
	end
end
--]]

--自动鼓舞
function FuBenWGCtrl:CheckGuwuAuto()
	--主角信息是否下发
	if RoleWGCtrl.Instance:GetIsHasRoleData() then
		if FuBenWGData.Instance:GetSceneGuwuType() > 0 then
			local flag1,flag2 = FuBenWGData.Instance:GetGuWuData()

			if flag1 == 1 or flag2 == 1 then
				local other_cfg = WuJinJiTanWGData.GetOtherCfg()
				local per_gold_consume = other_cfg.gold_guwu_cost
				local per_coin_consume = other_cfg.coin_guwu_cost
				local main_role_vo = RoleWGData.Instance:GetRoleInfo()
				local all_gold = main_role_vo.gold + main_role_vo.bind_gold
				local all_coin = main_role_vo.coin
				local can_buy_gold_count = math.floor(all_gold/per_gold_consume)
				local can_buy_coin_count = math.floor(all_coin / per_coin_consume)
				can_buy_gold_count = can_buy_gold_count <= 5 and can_buy_gold_count or 5
				can_buy_coin_count = can_buy_coin_count <= 5 and can_buy_coin_count or 5
				if (can_buy_gold_count + can_buy_coin_count) <= 0 then return end

				local role_info = FuBenWGData.Instance:GetFbGuWuInfo()
				if role_info and not IsEmptyTable(role_info) then
					local gold_guwu_count = role_info.gold_guwu_count
					local coin_guwu_count = role_info.coin_guwu_count
					FuBenWGCtrl.Instance:SendFbGuwu(-1,
						flag2 == 1 and math.min(can_buy_gold_count, 5 - gold_guwu_count) or 0,
						flag1 == 1 and math.min(can_buy_coin_count, 5 - coin_guwu_count) or 0)
				end
			end
		end
	else
		if not self.guwu_timer then
			self.guwu_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self.guwu_timer = nil
				self:CheckGuwuAuto()
			end, 0.5)
		end
	end
end

function FuBenWGCtrl:SetTowerIndex(index)
	self.tower_attr_view:SetIndex(index)
end

function FuBenWGCtrl:SendBossRebirth(opera_type, param1, param2, role_id)
	--print_error("use-1363", opera_type, param1, param2, role_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFBUseItemReq)
	protocol.opera_type = opera_type or 1
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.role_id = role_id or 0
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:SendDecBossTired(dec_type)
	local cfg = MainuiWGData.Instance:GetBossDecTiredCfgByDecType(dec_type)
	if not cfg then
		return
	end

	local dec_value_limit = cfg.dec_value_limit or -1
	--小于0则不限制
	if dec_value_limit >= 0 then
		if dec_type == BOSS_TASK_TYPE.WORLD then
			local world_times_info = BossWGData.Instance:GetWorldBossEnterTimeInfo()
			if not world_times_info or world_times_info.world_boss_tired_day_add_count >= dec_value_limit then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.WorldBossDeclimit)
				return
			end	
		end
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSDecBossTired)
	protocol.dec_type = dec_type or 0
	protocol:EncodeAndSend()
end

function FuBenWGCtrl.SendWTSReward(type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWTSReward)
	protocol.type = type
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:OnBaGuaMiZhenInFbInfoRet(protocol)
	self.data:SetBaGuaMiZhenFBStatus(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BaGuaMiZhenTaskView)
end

function FuBenWGCtrl:SendFbGuwu(is_gold,gold_count,coin_count)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFbGuwu)
	protocol.is_gold = is_gold
	protocol.gold_count = gold_count or 0
	protocol.coin_count = coin_count or 0
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:OpenFuBenNextView(data)
	local open_guide_data = FuBenPanelWGData.Instance:GetFuBenJiangHuGuideFlag()
	if open_guide_data ~= nil then
		local guide_data = Split(open_guide_data, "|")
		if guide_data[3] ~= nil and tonumber(guide_data[3]) == 4 and tonumber(guide_data[2] or 0) + 15 * 60 > TimeWGCtrl.Instance:GetServerTime() then
			local guide_cfg = FunctionGuide.Instance:GetGuideCfgByTrigger(GuideTriggerType.FuBenPanelResult, tonumber(guide_data[1] or 0))
			if guide_cfg ~= nil then
				FunctionGuide.Instance:SetCurrentGuideCfg(guide_cfg)
			end
		end
	end
	self.next_view:FbCloseTime(10)
	self.next_view:SetEndData(data)
	self.next_view:Open()
end

function FuBenWGCtrl:OpenZhuShenTaNextView(data)
	self.next_view:FbCloseTime(5)
	self.next_view:Open()
end

function FuBenWGCtrl:OpenNewPlayerView()
	self.fuben_new_player_view:Open()
end

function FuBenWGCtrl:CloseNewPlayerView()
	self.fuben_new_player_view:Close()
end

function FuBenWGCtrl:OpenNewPlayerXiuWeiView()
	self.fuben_new_player_xiuwei_view:Open()
end

-- 天仙阁符文解锁面板
function FuBenWGCtrl:OpenFuWenView(data)
	-- self.fuwen_jiesuo_view:SetEndData(data)
	-- self.fuwen_jiesuo_view:Open()
end

function FuBenWGCtrl:SetDataForFuWenView(data)
	-- self.fuwen_jiesuo_view:SetDataForNextView(data)
end

-- 下一层结算界面
function FuBenWGCtrl:CloseFuBenNextView()
	self.next_view:Close()
end

--日常副本结算界面
function FuBenWGCtrl:OpenFuBenStarView(data)
	-- self.star_view:SetEndData(data)
	-- self.star_view:Open()
end

--妖兽广场锁妖塔等副本结算界面
function FuBenWGCtrl:OpenFuBenEndView(data)
	-- self.end_view:SetEndData(data)
	-- self.end_view:Open()
end

--妖兽广场锁妖塔等副本结算界面
function FuBenWGCtrl:CloseFuBenEndView()
	-- self.end_view:Close()
end

-- 个人塔防技能面板
function FuBenWGCtrl:OpenTowerDownView()
	-- self.fuben_tower_down:Open()
end

-- 个人塔防技能面板
function FuBenWGCtrl:CloseTowerDownView()
	-- self.fuben_tower_down:Close()
end

--鼓舞界面
function FuBenWGCtrl:OpenFuBenCheerView()
	-- self.cheer_view:Open()
end

--剧情副本结算界面
function FuBenWGCtrl:OpenFuBenCardView(data)
	-- self.settlement_view:SetFinishData(data)
	-- self.settlement_view:Open()
end

--打开塔防副本界面
function FuBenWGCtrl:OpenDefenseFuBenView()
	self.defense_fb_view:Open()
	self.defense_fb_btn:Open()

end
--关闭塔防副本界面
function FuBenWGCtrl:CloseDefenseFuBenView()
	self.defense_fb_view:Close()
	self.defense_fb_btn:Close()
end

function FuBenWGCtrl:ShowBossBtnAction(state)
	self.defense_fb_btn:ShowBossBtnAction(state)
end

function FuBenWGCtrl:ShowBuildTowerTips(is_active)
	self.defense_fb_btn:ShowBuildTip(is_active)
end

--打开塔防副本建造界面
function FuBenWGCtrl:OpenDefenseTowerFuBenView(pos_index,bool)
	if bool then
		self.defense_fb_tower_view:SetTargetObjData(pos_index)
		self.defense_fb_tower_view:Open()
	else
		self.defense_fb_tower_feform_view:SetTargetObjData(pos_index)
		self.defense_fb_tower_feform_view:Open()
	end
end
--关闭塔防副本建造界面
function FuBenWGCtrl:CloseDefenseTowerFuBenView()
	self.defense_fb_tower_view:Close()
end

--打开塔防副本建造界面
function FuBenWGCtrl:OpenDefenseTowerFuBenFeformView(pos_index)
	self.defense_fb_tower_feform_view:SetTargetObjData(pos_index)
	self.defense_fb_tower_feform_view:Open()
end
--关闭塔防副本建造界面
function FuBenWGCtrl:CloseDefenseTowerFuBenFeformView()
	self.defense_fb_tower_feform_view:Close()
end

function FuBenWGCtrl:SetFuBenCardData(data)
	-- self.settlement_view:SetFinishData(data)
end

--副本任务面板
function FuBenWGCtrl:OpenTaskFollow()
	-- local root_layout = MainuiWGCtrl.Instance:GetRootLayout()
	-- self.fuben_task_view:Init(root_layout)
end

function FuBenWGCtrl:CloseTaskFollow()
	-- self.fuben_task_view:DestroyTaskView()
end

function FuBenWGCtrl:UpdataTaskFollow()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then
		self:OpenTaskFollow()
		local fuben_info = self.data:GetFormatTaskData()
		-- self.fuben_task_view:SetTaskFollowData(fuben_info)
	end
end

--副本场景信息
function FuBenWGCtrl:OnFbSceneLogicInfo(protocol)
	self.data:OnFbSceneLogicInfo(protocol)
	self:UpdataTaskFollow()
	--结束
	if 1 == protocol.is_finish then
		--通用结束面板
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = protocol.scene_type
		pass_vo.is_pass = protocol.is_pass
		pass_vo.reward_list = self.data:GetFbFinishRewardItem() or {}
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		pass_vo.tip2 = Language.FuBen.TongGuanReward
		--没通关没奖励
		if 0 == protocol.is_pass then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		--进入 下一关
		if protocol.scene_type == SceneType.GaoZhanFb then
			pass_vo.tip3 = Language.FuBen.NextLevel
			self:OpenFuBenNextView(pass_vo)
		elseif protocol.scene_type == SceneType.Fb_Welkin then
			pass_vo.tip3 = Language.FuBen.NextLevel
			self:OpenFuBenNextView(pass_vo)
			FuBenPanelWGCtrl.Instance:FlushFanRenTaskView()

		elseif protocol.scene_type == SceneType.ExpFb then
			self:OpenFuBenStarView(pass_vo)
		elseif protocol.scene_type == SceneType.CoinFb then
			pass_vo.tip2 = Language.FuBen.GetReward .. HtmlTool.GetHtml(tostring(protocol.coin), COLOR3B.GREEN , 24)
			self:OpenFuBenStarView(pass_vo)
		end
	else
		if protocol.scene_type == SceneType.ZHUANZHI_FB then
			self:SetOutFbTime(protocol.time_out_stamp)
			FuBenPanelWGData.Instance:SetOutTimer(protocol.time_out_stamp)
			FuBenWGData.Instance:GetSetFBPrepareTime(protocol.flush_timestamp)
			UiInstanceMgr.Instance:DoFBStartDown(protocol.flush_timestamp,function()
				if Scene.Instance:GetSceneType() == SceneType.ZHUANZHI_FB then
					MainuiWGCtrl.Instance:SetShowTimeTextState(true)
				end
			end)
		end
	end

	if protocol.scene_type == SceneType.CoinFb then
		-- UiInstanceMgr.Instance:SetRewardActionNum(protocol.coin)
	elseif protocol.scene_type == SceneType.ExpFb then
		-- UiInstanceMgr.Instance:SetRewardActionNum(protocol.exp)
	elseif protocol.scene_type == SceneType.DEVILCOME_FB or protocol.scene_type == SceneType.GAINT_FB
		or protocol.scene_type == SceneType.PROFAIRY_FB or protocol.scene_type == SceneType.KUNLUNTRIAL_FB
		or protocol.scene_type == SceneType.SCENE_TYPE_TASKFB_ZHILIAO or  protocol.scene_type == SceneType.DEMONS_FB then
		if 1 == protocol.is_finish then
			FuBenPanelCountDown.Instance:SetTimerInfo(protocol.kick_timestamp)
		end
		FuBenPanelWGCtrl.Instance:FlushBountyTaskView(protocol)
	elseif protocol.scene_type == SceneType.ZHUANZHI_FB then
		ViewManager.Instance:FlushView(GuideModuleName.TransFerDemon)
		if protocol.is_pass == 0 and protocol.is_finish == 1 then
			--self:OpenFuBenLoseView()
			self:OpenLose(SceneType.ZHUANZHI_FB)
		elseif protocol.is_pass == 1 and protocol.is_finish == 1 then
			-- 通关成功
			local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
			prof_zhuan = prof_zhuan + 1
			local data = TransFerWGData.Instance:GetTransFerSingleRewardList(prof_zhuan)
			self:OpenWin(SceneType.ZHUANZHI_FB, data, 0, 0, 0, 10)

		end
	elseif protocol.scene_type == SceneType.PERSON_BOSS then    -- 个人boss
		GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, protocol,true)
		--ViewManager.Instance:FlushView(GuideModuleName.FubenStarAniView)
		BossWGData.Instance:SetKillBossSucc(protocol.is_pass == 1)
		local time = math.floor(protocol.kick_timestamp - TimeWGCtrl.Instance:GetServerTime())
		if protocol.is_pass == 1 then
			if time <= 30 and time > 0 then
				-- UiInstanceMgr.Instance:OpenLeaveSceneCountDown(protocol.kick_timestamp - TimeWGCtrl.Instance:GetServerTime(), false)
				local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + time
				-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
				FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
				end, {x = 0, y = -50})
				BossWGData.Instance:SetFubenPassTime(protocol.pass_time_s)

			end
		else
			if protocol.is_finish == 0 then
				if protocol.time_out_stamp == 0 then
					UiInstanceMgr.Instance:DoFBStartDown(protocol.flush_timestamp)
				else
					--self:SetOutFbTime(protocol.time_out_stamp)
					FuBenPanelWGData.Instance:SetOutTimer(protocol.time_out_stamp)
					FuBenWGData.Instance:GetSetFBPrepareTime(protocol.flush_timestamp)
				end
			else
				local server_time =  TimeWGCtrl.Instance:GetServerTime()
				local time = math.floor(protocol.kick_timestamp - server_time)
				if time <= 30 and time > 0 and not FuBenWGData.Instance:GetFuBenInitiativeToLeave(SceneType.PERSON_BOSS) then
					FuBenPanelCountDown.Instance:SetTimerInfo(protocol.kick_timestamp)
				end
			end
		end
	elseif protocol.scene_type == SceneType.GUIDE_BOSS then    -- 引导boss
		local time = math.floor(protocol.kick_timestamp - TimeWGCtrl.Instance:GetServerTime())
		if time <= 30 and time > 0 and protocol.is_pass == 1 then
			-- UiInstanceMgr.Instance:OpenLeaveSceneCountDown(protocol.kick_timestamp - TimeWGCtrl.Instance:GetServerTime(), false)
			local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + time
			-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
			FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
			end)
		end
		FuBenPanelWGCtrl.Instance:FlushGuideBossView()
	elseif protocol.scene_type == SceneType.BaGuaMiZhen_FB then    -- 八卦迷阵副本
		--kick_timestamp 踢出副本时间
		-- local server_time =  TimeWGCtrl.Instance:GetServerTime()
		-- local time = math.floor(protocol.kick_timestamp - server_time)
		-- if protocol.flush_timestamp < server_time and protocol.is_finish ~= 1 then
		-- 	print_error('显示倒计时--------------')
		-- 	FuBenPanelCountDown.Instance:SetTimerInfo(protocol.kick_timestamp,function()
		-- 	end)
		-- end
		-- --到达踢出时间前30秒,并且通关
		-- if time <= 30 and time > 0 and protocol.is_pass == 1 then
		-- 	-- UiInstanceMgr.Instance:OpenLeaveSceneCountDown(protocol.kick_timestamp - TimeWGCtrl.Instance:GetServerTime(), false)
		-- 	local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + time
		-- 	-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
		-- 	--退出场景倒计时
		-- 	FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
		-- 	end)
		-- end
		BaGuaMiZhenWGCtrl.Instance:FlushTaskView()
		UiInstanceMgr.Instance:DoFBStartDown(protocol.flush_timestamp,function()
			if Scene.Instance:GetSceneType() == SceneType.BaGuaMiZhen_FB then
				MainuiWGCtrl.Instance:SetShowTimeTextState(true)
			end
		end)

		if protocol.is_finish == 1 then
			self:SetOutFbTime(0, true, true)
			if protocol.is_pass == 1 then
				--奖励
				local info = self.data:GetDropInfo(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB)
				local item_cfg = {}
				local sort_index = 0
				for k,v in pairs(info.drop_item_list) do
					sort_index = 0
					item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
					sort_index = sort_index + item_cfg.color * 100
					if  item_cfg.search_type == 503 then
						sort_index = sort_index + 50
					end
					v.sort_index = sort_index
				end

				if not IsEmptyTable(info.drop_item_list) then
					table.sort(info.drop_item_list,SortTools.KeyUpperSorters("sort_index"))
				end

				self:OpenWin(SceneType.BaGuaMiZhen_FB, info.drop_item_list, protocol.exp, 0, 3, 10)
			else
				self:OpenLose(SceneType.BaGuaMiZhen_FB)
			end
		else
			self:SetOutFbTime(protocol.time_out_stamp, true, true)
		end
	elseif protocol.scene_type == SceneType.TIAN_SHEN_FB then    -- 天神副本_OK
		local time = math.ceil(protocol.kick_timestamp - TimeWGCtrl.Instance:GetServerTime())
		-- if time <= 30 and time > 0 and protocol.is_pass == 1 then
		-- 	local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + time
		-- 	FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
		-- 	end, {x = 0, y = -50})
		-- end
		FuBenPanelWGCtrl.Instance:FlushTianShenLogicView()
		if protocol.is_finish == 1 then
			self:SetOutFbTime(0, true, true) --结算的时候，这里传一个0进去，为了隐藏退出按钮下的倒计时
			if protocol.is_pass == 1 then
				local cur_star_num = FuBenPanelWGCtrl.Instance:GetCurStarNum()
				local info = self.data:GetDropInfo(FUBEN_TYPE.FBCT_TIANSHEN_FB)
				self:OpenWin(SceneType.TIAN_SHEN_FB, info.drop_item_list, protocol.exp, protocol.coin, cur_star_num, time)
			else
				--self:OpenFuBenLoseView()
				self:OpenLose(SceneType.TIAN_SHEN_FB)
			end
			FuBenPanelWGCtrl.Instance:CloseStarAniView()
		else
			self:SetOutFbTime(protocol.time_out_stamp, true, true)
			--print_error(protocol.time_out_stamp, protocol.time_out_stamp - TimeWGCtrl.Instance:GetServerTime())
			UiInstanceMgr.Instance:DoFBStartDown(protocol.flush_timestamp)
			FuBenPanelWGCtrl.Instance:FlushStarAniView()
			FuBenPanelWGCtrl.Instance:FlushTianShenLogicView()
		end
	elseif protocol.scene_type == SceneType.HUNDRED_EQUIP then    -- 百倍爆装
		HundredEquipWGCtrl.Instance:FlushHundredLogicView(protocol.param1, protocol.param2)

		if protocol.is_finish == 1 then
			local rank_info_list, my_rank, my_wave = HundredEquipWGData.Instance:GetFuBenRankInfoList()
			local reward_list =  HundredEquipWGData.Instance:GetFuBenRewardList()
			self:OpenWin(SceneType.HUNDRED_EQUIP, reward_list, 0, 0, 0, 10)
		end

		UiInstanceMgr.Instance:DoFBStartDown(protocol.flush_timestamp, function()
			if Scene.Instance:GetSceneType() == SceneType.HUNDRED_EQUIP then
				MainuiWGCtrl.Instance:SetShowTimeTextState(true)
			end
		end)
	end
end

-- 个人塔防本
function FuBenWGCtrl:OnTowerDefendInfo(protocol)
	self.data:OnTowerDefendInfo(protocol)
	self:UpdataTaskFollow()
	-- self.fuben_tower_down:Flush()
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = SceneType.TowerDefend
		pass_vo.is_pass = protocol.is_pass
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		pass_vo.reward_list = protocol.pick_drop_list
		pass_vo.tip2 = Language.FuBen.TongGuanReward
		if 0 == protocol.is_pass then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end

		self:OpenFuBenStarView(pass_vo)
		if Scene.Instance:GetSceneType() == SceneType.TowerDefend then
			local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 20
			-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
			FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
			end)
		end
	end
end


-- 武器副本场景信息
function FuBenWGCtrl:OnWeaponFbSceneInfo(protocol)
	self.data:OnPeteqFbInfo(protocol)
	self:UpdataTaskFollow()
	if self.weapon_fb_timer then
		GlobalTimerQuest:CancelQuest(self.weapon_fb_timer)
		self.weapon_fb_timer = nil
	end
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = SceneType.WeaponFb
		pass_vo.is_pass = protocol.is_pass
		if protocol.is_pass == 1 then
			local scene_id = Scene.Instance:GetSceneId() or 0
			pass_vo.star = DailyWGData.Instance:GetPeteqStarBySceneId(scene_id, protocol.pass_time)
		end
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time), COLOR3B.GREEN , 24)
		pass_vo.reward_list = self.data:GetPickRewardList(SceneType.WeaponFb) or {}
		if 0 == #pass_vo.reward_list then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		self:OpenFuBenStarView(pass_vo)
	else
		self.weapon_fb_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind2(self.UpdataWeaponFbSceneInfo, self, protocol), 1)
	end
end
-- 武器副本场景信息
function FuBenWGCtrl:UpdataWeaponFbSceneInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.WeaponFb and protocol.is_finish == 0 then
		protocol.pass_time = protocol.pass_time + 1
		self:OnWeaponFbSceneInfo(protocol)
	end
end

-- 装备副本场景信息
function FuBenWGCtrl:OnEquipFbSceneInfo(protocol)
	self.data:OnEquipFbInfo(protocol)
	self:UpdataTaskFollow()
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = SceneType.EquipFb
		pass_vo.is_pass = protocol.is_pass
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time), COLOR3B.GREEN , 24)
		pass_vo.reward_list = self.data:GetPickRewardList(SceneType.EquipFb) or {}
		if 0 == #pass_vo.reward_list then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		self:OpenFuBenStarView(pass_vo)
	end

end


-- 福利副本场景信息
function FuBenWGCtrl:OnWelfareFbSceneInfo(protocol)
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = SceneType.WelfareFb
		pass_vo.is_pass = protocol.is_pass
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time), COLOR3B.GREEN , 24)
		pass_vo.reward_list = self.data:GetPickRewardList(SceneType.WelfareFb) or {}
		if 0 == #pass_vo.reward_list then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		self:OpenFuBenStarView(pass_vo)
		if Scene.Instance:GetSceneType() == SceneType.TowerDefend then
			local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 20
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
		end
	end

end

--离开场景时间（通用）
function FuBenWGCtrl:OnSceneFbTimeOut(protocol)
	-- print_error("OnSceneFbTimeOut", protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.SPECIAL_PERSONAL_BOSS
	or scene_type == SceneType.GHOST_FB_PERSON 
	or scene_type == SceneType.WUHUN_TOWER_FB then
		return
	end

	if scene_type ~= SceneType.GuildBoss
		and scene_type ~= SceneType.CROSS_LIEKUN
		and scene_type ~= SceneType.DEMONS_FB
		and scene_type ~= SceneType.Wujinjitan
		and scene_type ~= SceneType.LingHunGuangChang
		and scene_type ~= SceneType.COPPER_FB
		and scene_type ~= SceneType.ZHUANZHI_FB
		and scene_type ~= SceneType.Fb_Welkin
		and scene_type ~= SceneType.ZHUSHENTA_FB
		and scene_type ~= SceneType.BaGuaMiZhen_FB
		and scene_type ~= SceneType.Kf_OneVOne_Prepare
		and scene_type ~= SceneType.Kf_PvP_Prepare
		and scene_type ~= SceneType.Common
		and scene_type ~= SceneType.YEZHANWANGCHENGFUBEN
		and scene_type ~= SceneType.ETERNAL_NIGHT
		and scene_type ~= SceneType.ETERNAL_NIGHT_FINAL
		and scene_type ~= SceneType.TIANSHEN_JIANLIN
		and scene_type ~= SceneType.WORLD_TREASURE_JIANLIN
		and scene_type ~= SceneType.FBCT_NEWPLAYERFB then
		self:SetOutFbTime(protocol.time_out)
		FuBenPanelWGData.Instance:SetOutTimer(protocol.time_out)
	end
end

function FuBenWGCtrl:OnFbCanEnterResult(protocol)
--	print_error("副本进入返回结果", protocol)
	 if 1 == protocol.can_enter then
    --self.view:Close()
	-- 	Scene.Instance:GetMainRole():SpecialJumpUpSceneEffect(protocol.fb_type)
	-- 	if PlayEffectFuBenType[protocol.fb_type] == 1 then
	-- 			Scene.Instance:GetMainRole():JumpUpToFuBenSceneChange(function ()
	-- 			Scene.Instance:SetRoleChangeSceneEff(true)
	-- 		end)
	-- 	else
	-- 		Scene.Instance:GetMainRole():JumpUpSceneChange(function ()
	-- 			Scene.Instance:SetRoleChangeSceneEff(true)
	-- 		end)
	-- 	end
	 end
end

local SetLevelTimeTable = {
	[SceneType.YaoShouPlaza] = true,
	-- [SceneType.Wujinjitan] = true,
	-- [SceneType.LingHunGuangChang] = true,
	[SceneType.SuoYaoTa] = true,
	[SceneType.Kf_OneVOne] = true,
	[SceneType.Kf_Honorhalls] = true,
	[SceneType.DefenseFb] = true,
	[SceneType.QingYuanFB] = true,
	[SceneType.HIGH_TEAM_EQUIP_FB] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_1] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_2] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_3] = true,
	[SceneType.TEAM_COMMON_TOWER_FB_1] = true,
	-- [SceneType.TIAN_SHEN_FB] = true,
	--[SceneType.ZHUSHENTA_FB] = true,
}

--设置副本离开场景时间（外部可调用）
function FuBenWGCtrl:SetOutFbTime(out_fb_time, enable, not_show_end_countdown)

	self.data:SetOutFbTime(out_fb_time)

	local scene_type = Scene.Instance:GetSceneType()
	if SetLevelTimeTable[scene_type] == nil then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time, enable, nil, not_show_end_countdown)
		if scene_type == SceneType.ZHUSHENTA_FB then
			MainuiWGCtrl.Instance:SetShowTimeTextState( false )
		end
	end
end

function FuBenWGCtrl:ClearFbSceneLogicInfo()
	self.data:ResetFbSceneLogicInfo()
	self:SetOutFbTime(0)
end

--进入副本
function FuBenWGCtrl:SendEnterFB(fb_type, param_1, param_2, param_3)
	-- TaskGuide.Instance:ClearAll()
	if TaskWGCtrl.Instance:IsFly() then
		TaskWGCtrl.Instance:JumpEnd()
--		print_error("hahahahahaha")
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FlyShoeCanNotDo)
		-- return
	end

	if not self.CanEnterFuben() then
		return
	end

	TaskGuide.Instance:SpecialConditions(true, 3)
	FuBenWGData.Instance:DefFBState(true)
	-- print_error("FuBenWGCtrl:SendEnterFB-->>", fb_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEnterFB)
	protocol.fb_type = fb_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

-- 进入经验副本
function FuBenWGCtrl:SendEnterExpFB(is_guide)
	-- local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
	-- local team_type = is_linghunguangchang_fb and GoalTeamType.Exp_FuMoZhanChuan or GoalTeamType.Exp_DuJieXianZhou
	-- local fb_mode =  0
	-- NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type, fb_mode, 5, is_guide or self.is_first_enter_exp_fb)

	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.LINGHUNGUANGCHANG)
end

-- 请求离开副本
function FuBenWGCtrl:SendLeaveFB()
	-- if IS_ON_CROSSSERVER then
	-- 	CrossServerWGCtrl.ConnectBack()
	-- else
		self:SendCSLeaveFB()
	-- end
end

function FuBenWGCtrl:SendCSLeaveFB()
    CrossServerWGData.LAST_CROSS_TYPE = 0
	local protocol = ProtocolPool.Instance:GetProtocol(CSLeaveFB)
	protocol:EncodeAndSend()
end

-- 退出副本提示内容
function FuBenWGCtrl:SetLevaeFbContent(str, ignore)
	if nil ~= str and "" ~= str then
		self.level_fb_alert:SetLableString(str)
		self.data:SetLeaveFbStr(str, ignore)
	end
end
-- 退出副本二次确认框
function FuBenWGCtrl:InitLeaveFbAlert()
	local callback_func =  function ()
		self:SendLeaveFB()
	end
	self.level_fb_alert:SetOkFunc(callback_func)
end

function FuBenWGCtrl:OnLeaveFBHandler()
	local str = #SocietyWGData.Instance:GetTeamMemberList() > 0 and Language.Dungeon.LevelFBHasTeam or Language.Dungeon.ConfirmLevelFB
	self.level_fb_alert:SetLableString(str)
	self.level_fb_alert:Open()
end

function FuBenWGCtrl:CloseLevelFBPanel()
	self.level_fb_alert:Close()
end

-- 扫荡副本
function FuBenWGCtrl:SendSwipeFB(fb_type, param_1, param_2, param_3, param_4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAutoFB)
	protocol.fb_type = fb_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol.param_4 = param_4 or 0
	protocol:EncodeAndSend()
end

------------------日常本---------------
--请求进入日常副本
function FuBenWGCtrl:SendEnterDailyFb(dailyfb_type, is_usegold_to_enter)
	self:SendEnterFB(FUBEN_TYPE	.FBCT_DAILY_FB, dailyfb_type, is_usegold_to_enter, 0)
end


-----------------灵玉副本---------------
--请求进入灵玉副本
function FuBenWGCtrl:SendEnterLingyuFb(level, is_usegold_to_enter)
	self:SendEnterFB(FUBEN_TYPE.FBCT_LINGYUFB, level, is_usegold_to_enter, 0)
end

------------------爬塔本---------------
function FuBenWGCtrl:SendFBReqNextLevel(param)
    local protocol = ProtocolPool.Instance:GetProtocol(CSFBReqNextLevel)
    protocol.param = param or 0
	protocol:EncodeAndSend()
end
-- 快速爬塔
function FuBenWGCtrl:SendFBReqQucikFinish(param)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTianXianGeOneKey)
    protocol.param1 = param or 0
	protocol:EncodeAndSend()
end
--请求进入爬塔本
function FuBenWGCtrl:SendEnterPaTaFb()
	self:SendEnterFB(FUBEN_TYPE.FBCT_PATA)
end

--请求进入天仙阁
function FuBenWGCtrl:SendEnterWelkinFb(is_guide)
    FuBenPanelWGData.Instance:SetXiuZhenRoadGuideFlag(is_guide)
	self:SendEnterFB(FUBEN_TYPE.FBCT_WELKIN, is_guide and 1 or 0)
end

--请求进入剧情本
function FuBenWGCtrl:SendEnterStoryFb(chapter, level, is_usegold_to_enter)
	self:SendEnterFB(FUBEN_TYPE.FBCT_STORYFB, chapter, level, is_usegold_to_enter)
end

--请求进入铜币本
function FuBenWGCtrl:SendTongBiFb(fb_index)
	self:SendEnterFB(FUBEN_TYPE.FBCT_TONGBIBEN, fb_index)
end

--请求进入宠物本
function FuBenWGCtrl:SendPETFb(fb_index)
	self:SendEnterFB(FUBEN_TYPE.FBCT_PETBEN, fb_index)
end


---------------------------------
--多人--组队塔防
---------------------------------
function FuBenWGCtrl:OnTeamTowerdefendFbInfo(protocol)
	-- self.data:OnTeamTowerDefendInfo(protocol)
	-- self:SetOutFbTime(protocol.time_out_stamp)
	-- self:UpdataTaskFollow()
	-- if 1 == protocol.is_finish then
	-- 	local pass_vo = FuBenWGData.CreateCommonPassVo()
	-- 	pass_vo.scene_type = SceneType.ManyTowerFB
	-- 	pass_vo.is_pass = protocol.is_pass
	-- 	pass_vo.reward_list = {}
	-- 	pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
	-- 	pass_vo.tip2 = Language.FuBen.TongGuanReward

	-- 	if 0 == #pass_vo.reward_list then
	-- 		pass_vo.reward_list = {}
	-- 		pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
	-- 	end
	-- 	self:OpenFuBenStarView(pass_vo)
	-- end
end

-- function FuBenWGCtrl:OnTeamTowerDefendWarning(protocol)
-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Dungeon.TowerBeAttack)
-- end

function FuBenWGCtrl:SendTeamTowerDefendNextWave()
	self:SendTeamTowerdefendFbOperate(TEAM_TOWERD_OPERA_TYPE.NEXT_WAVE)
end

function FuBenWGCtrl:SendTeamTowerdefendFbOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTeamTowerdefendFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

---------------------------------
--多人--无尽祭坛
---------------------------------
function FuBenWGCtrl:OnTeamWujinjitanFbInfo(protocol)
	--print_error("OnTeamWujinjitanFbInfo")
	self.data:OnWujinjitanSceneInfo(protocol)
	self:SetOutFbTime(protocol.time_out_stamp)
	self:UpdataTaskFollow()
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = SceneType.Wujinjitan
		pass_vo.is_pass = protocol.is_pass
		pass_vo.reward_list = self.data:GetWujinjitanTeamFbFinishReward() or {}
		local max_wave = self.data:GetWujinjitanMaxWave()
		pass_vo.tip1 = HtmlTool.GetHtml(string.format(Language.FuBen.FubenJindu, protocol.cur_wave + 1, max_wave + 1), COLOR3B.GREEN , 24)
		pass_vo.tip2 = Language.FuBen.TongGuanReward

		if #pass_vo.reward_list == 0 then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end

		local times, times2= DailyWGData.Instance:GetTeamFbLeftTimes(1)
		if 0 < times2 then
			self:OpenFuBenStarView(pass_vo)
		else
			self:SendLeaveFB()
		end
	end
end

---------------------------------
--多人--迷宫仙府
---------------------------------
function FuBenWGCtrl:OnMgxfTeamFbSceneLogicInfo(protocol)
	Log("-------------OnMgxfTeamFbSceneLogicInfo")
	self.data:OnMgxfTeamFbSceneLogicInfo(protocol)
	self:SetOutFbTime(protocol.time_out_stamp)
	self:UpdataTaskFollow()
	if 1 == protocol.is_finish then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = SceneType.MiGongXianFu
		pass_vo.is_pass = protocol.is_pass
		pass_vo.reward_list = self.data:GetMgxfTeamFbFinishReward() or {}
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		pass_vo.tip2 = Language.FuBen.TongGuanReward
		if 0 == protocol.is_pass then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		self:OpenFuBenStarView(pass_vo)
	end

	if SceneType.MiGongXianFu ~= Scene.Instance:GetSceneType() then
		return
	end
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic.UpdateSceneLogic then
		scene_logic:UpdateSceneLogic()
	end
end
--点击传送点
function FuBenWGCtrl:SendMgxfTeamFbTouchDoor(layer, door_index)
	Log("---------FuBenWGCtrl:SendMgxfTeamFbTouchDoor", layer, door_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMgxfTeamFbTouchDoor)
	protocol.layer = layer
	protocol.door_index = door_index
	protocol:EncodeAndSend()
end


--副本中拾取信息
function FuBenWGCtrl:SCFbPickInfo(protocol)
	self.data:SetPickInfo(protocol)
end

----------------------塔防副本信息---------------------
function FuBenWGCtrl:SendBuildTowerReq(operate_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBuildTowerReq)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:OnBuildTowerFBSceneLogicInfo(protocol)
--	print_error("塔防>>>",protocol.data.can_call_extra_monster)
	self.defense_fb_data:SetBuildTowerFBSceneLogicInfo(protocol)
	self.defense_fb_view:Flush()
	self.defense_fb_tower_feform_view:Flush()
	self.defense_fb_tower_view:Flush()
	self.defense_fb_btn:Flush()

	-- local time = protocol.data.finish_timestamp - TimeWGCtrl.Instance:GetServerTime()
	 -- print_error("设置倒计时=====",protocol.data.finish_timestamp,TimeWGCtrl.Instance:GetServerTime())
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.data.finish_timestamp)

	if protocol.data.notify_reason == DefenseFbWGData.NOTIFY_REASON.NOTIFY_MONSTER_WAVE then
		if protocol.data.cur_wave + 1 > 1 and self.defense_fb_data:GetIsAutoCallBoss() then
			self:SendBuildTowerReq(BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_CALL)
		end
	end
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic and scene_logic.FlushDefenseObjVisible then
		scene_logic:FlushDefenseObjVisible()
	end

	local drop_info = self.data:GetDropInfo(FUBEN_TYPE.FBCT_TAFANG)
	if  protocol.data.is_pass == 1 and protocol.data.is_finish == 1 and drop_info.is_sweep == 0 then
		if Scene.Instance:GetSceneType() == SceneType.DefenseFb then
			local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
			-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
			-- self:SetOutFbTime(out_fb_time)
			-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
			-- FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()

   --  		end)
		end

		-- if self.tafang_delay then
		-- 	GlobalTimerQuest:CancelQuest(self.tafang_delay)
		-- end
		-- self.tafang_delay = GlobalTimerQuest:AddDelayTimer(function ()
		-- 	self:SetCommonWinData(SceneType.DefenseFb, drop_info.drop_item_list, protocol.data.exp, protocol.data.reward_xianhunshi_num, 3, 10)
		-- 	end, 1.5)
	end

	if protocol.data.is_pass == 0 and protocol.data.is_finish == 1
		and protocol.data.notify_reason == DefenseFbWGData.NOTIFY_REASON.NOTIFY_FB_END then

		local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
		-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_fb_time)
		self:SetOutFbTime(out_fb_time)
		FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
			GlobalTimerQuest:AddDelayTimer(function()
				self:OpenFuBenLoseView()
			end,1.5)
    	end)
	end
	
	RemindManager.Instance:Fire(RemindName.LiuDaoLunHui)
end

--副本中拾取信息
function FuBenWGCtrl:OnSCFBDropInfo(protocol)
	if protocol.is_sweep == 1 and protocol.fb_type == FUBEN_TYPE.FBCT_TAFANG then
		local drop_info = self.data:GetDropInfo(FUBEN_TYPE.FBCT_TAFANG)
		local filtrate_drop = self.data:FiltrateDefenseDrop(drop_info.drop_item_list) -- 塔防本掉落筛选并排序
		self:OpenWin(SceneType.DefenseFb, filtrate_drop, drop_info.get_exp, drop_info.flexible_int1, 3, 10)
		if FuBenPanelWGCtrl.Instance.tafang_saodang:IsOpen() then
			FuBenPanelWGCtrl.Instance.tafang_saodang:Flush()
		end
	end

	self.data:SetDropInfo(protocol)
	if protocol.is_sweep == 1 and protocol.fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then
		local drop_info = self.data:GetDropInfo(FUBEN_TYPE.FBCT_TIANSHEN_FB)
		self:OpenWin(SceneType.TIAN_SHEN_FB, drop_info.drop_item_list, drop_info.get_exp, drop_info.flexible_int1, 3, 10)
		--if FuBenPanelWGCtrl.Instance.tianshen_saodang_view:IsOpen() then
		--	FuBenPanelWGCtrl.Instance.tianshen_saodang_view:Flush()
		--end
	end

	if protocol.is_sweep == 1 and protocol.fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then
		local drop_info = self.data:GetDropInfo(FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB)
		self:OpenWin(SceneType.TIAN_SHEN_FB, drop_info.drop_item_list, drop_info.get_exp, drop_info.flexible_int1, 3, 10)
    end

    -- if protocol.fb_type == FUBEN_TYPE.FBCT_SHENYUAN_BOSS then --深渊boss结算特殊处理，弃用
    -- 	local reward_type = protocol.flexible_int1
	-- 	self:SetCommonWinData(SceneType.Shenyuan_boss, protocol.drop_item_list, protocol.get_exp, 0, 0, 10, nil, nil, reward_type)
    -- end

    if protocol.fb_type == FUBEN_TYPE.FBCT_PERSON_BOSS then
        if self.check_is_finish_pick then
            GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
            self.check_is_finish_pick = nil
        end
        self.check_is_finish_pick = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FinishPickPersonBoss, self, protocol, SceneType.PERSON_BOSS), 1)
        --策划要求先捡东西再弹结算面板，，服务器不想改，，
		--self:OpenWin(SceneType.PERSON_BOSS, protocol.drop_item_list, protocol.get_exp, 0, 0, 10)
	end

	if protocol.fb_type == FUBEN_TYPE.PERSON_SPECIAL_BOSS then
        if self.check_is_finish_pick then
            GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
            self.check_is_finish_pick = nil
        end

        self.check_is_finish_pick = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FinishPickSpecialBoss, self, protocol, SceneType.SPECIAL_PERSONAL_BOSS), 1)
	end
end

function FuBenWGCtrl:FinishPickSpecialBoss(protocol, scene_type)
    if Scene.Instance:GetSceneType() ~= scene_type then --在捡的过程中退出了场景，直接弹结算
        self:OpenWin(scene_type, protocol.drop_item_list, protocol.get_exp, 0, 0, 10)
        GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
        self.check_is_finish_pick = nil
    else
        local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem) or {}
        local is_show_rare_item = RareItemDropWGCtrl.Instance:IsShowRareItemView()
        if not next(fall_item_list) and not is_show_rare_item then
            self:OpenWin(scene_type, protocol.drop_item_list, protocol.get_exp, 0, 0, 10)
            GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
            self.check_is_finish_pick = nil
        end
    end
end

function FuBenWGCtrl:FinishPickPersonBoss(protocol, scene_type)
    if Scene.Instance:GetSceneType() ~= scene_type then --在捡的过程中退出了场景，直接弹结算
        self:OpenWin(scene_type, protocol.drop_item_list, protocol.get_exp, 0, 0, 10)
        GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
        self.check_is_finish_pick = nil
    else
        local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem) or {}
        local is_show_rare_item = RareItemDropWGCtrl.Instance:IsShowRareItemView()
        if not next(fall_item_list) and not is_show_rare_item then
            self:OpenWin(scene_type, protocol.drop_item_list, protocol.get_exp, 0, 0, 10)
            GlobalTimerQuest:CancelQuest(self.check_is_finish_pick)
            self.check_is_finish_pick = nil
        end
    end
end
----------------------新手副本信息-------------------
function FuBenWGCtrl:OnSCNewPlayerFBInfo(protocol)
	self.data:SetNewPlayerFBInfo(protocol)
	-- print_error(protocol)
	self.fuben_new_player_view:Flush()
	if protocol.new_player_data.is_finish == 1 then
		if protocol.new_player_data.fb_finish_timestamp < protocol.new_player_data.kick_out_timestamp then
			self:SetOutFbTime(protocol.new_player_data.fb_finish_timestamp)
		else
			self:SetOutFbTime(protocol.new_player_data.kick_out_timestamp)
		end
	else
		self:SetOutFbTime(protocol.new_player_data.fb_finish_timestamp)
	end
end

function FuBenWGCtrl:OnSCNewPlayerFbPassInfo(protocol)
	self.data:SetNewPlayerFBPassInfo(protocol)
end

function FuBenWGCtrl:OnFbGuWuInfo(protocol)
	self.data:SetFbGuWuInfo(protocol)
	if self.team_cheer_view:IsOpen() then
	 	self.team_cheer_view:Flush(0, "cheer")
	end

	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		GlobalEventSystem:Fire(OtherEventType.FB_GUWU_CHANGE, protocol)
	else
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			local t = {}
			t.scene_type = protocol.scene_type
			t.coin_guwu_count = protocol.coin_guwu_count
			t.gold_guwu_count = protocol.gold_guwu_count
			GlobalEventSystem:Fire(OtherEventType.FB_GUWU_CHANGE, t)
		end)
	end

end

----------------------无尽塔信息---------------------
function FuBenWGCtrl:SendWuJinJiTanReq(req_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWuJinJiTanReq)
	protocol.req_type = req_type
	protocol:EncodeAndSend()
end


function FuBenWGCtrl:SendSetExpFBAutoGuwu(op_type,param_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSetExpFBAutoGuwu)
	protocol.op_type = op_type ---- 0取消，1勾选
	protocol.type = param_type  -- 0铜币，1元宝
	protocol.guwu_fb_type = self.data:GetSceneGuwuType()
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:OnSCSetExpFBAutoGuwu(protocol)
	self.data:SetGuWuData(protocol)
end

--炼妖洞窟
function FuBenWGCtrl:OnSCWuJinJiTanAllInfo(protocol)
	 --print_error("OnSCWuJinJiTanAllInfo", protocol.data.finish_timestamp - TimeWGCtrl.Instance:GetServerTime(), protocol.data.prepare_end_timestamp- TimeWGCtrl.Instance:GetServerTime())
	 self.wujin_jitan_data:SetWuJinJiTanAllInfo(protocol)
	 -- FuBenWGData.Instance:GetSetFBPrepareTime( protocol.data.prepare_end_timestamp )
	if TimeWGCtrl.Instance:GetServerTime() > protocol.data.prepare_end_timestamp then
		if not self.set_wujin_jitan or not MainuiWGCtrl.Instance:IsCountDowning() then
			self.set_wujin_jitan = true
			-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.data.finish_timestamp)
			-- print_error("经验本 >>>>>>>>> ", protocol.data.finish_timestamp, protocol.data.finish_timestamp - TimeWGCtrl.Instance:GetServerTime())
			self:SetOutFbTime(protocol.data.finish_timestamp, true, true)
		end
	end
	-- print_error(protocol.data.finish_timestamp, protocol.data.finish_timestamp - TimeWGCtrl.Instance:GetServerTime())
	if self.team_expfb_info_view:IsOpen() then
	 	self.team_expfb_info_view:Flush()
	end
	if self.team_cheer_view:IsOpen() then
	 	self.team_cheer_view:Flush(0, "cheer")
	end
end

function FuBenWGCtrl:OnSCWuJinJiTanRoleInfo(protocol)
	self.wujin_jitan_data:SetWuJinJiTanRoleInfo(protocol)
	if 1 == protocol.data.is_finish and 1 == protocol.data.is_pass then
		local role_info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
		local get_exp = role_info.fetch_exp or 0
		local get_coin = role_info.fetch_coin or 0
		-- if self.team_exp_win_delay then
		-- 	GlobalTimerQuest:CancelQuest(self.team_exp_win_delay)
		-- end
		-- self.team_exp_win_delay = GlobalTimerQuest:AddDelayTimer(function ()
			local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
			self:OpenWin(is_linghunguangchang_fb and SceneType.LingHunGuangChang or SceneType.Wujinjitan, role_info.drop_items, get_exp, get_coin, 3, 10)
		-- end, 1.5)
		-- local out_fb_time = TimeWGCtrl.Instance:GetServerTime() + 10
		-- FuBenPanelCountDown.Instance:SetTimerInfo(out_fb_time,function()
		-- end, {x = 0, y = -50})
	end

	if 1 == protocol.data.is_finish and 0 == protocol.data.is_pass then
		--local fb_info = FuBenWGData.Instance:GetFbEndInfo(0, protocol.data.fetch_exp)
		local role_info = WuJinJiTanWGData.Instance:GetWuJinJiTanRoleInfo()
		local get_exp = role_info.fetch_exp or 0
		local get_coin = role_info.fetch_coin or 0
		-- if self.team_exp_loser_delay then
		-- 	GlobalTimerQuest:CancelQuest(self.team_exp_loser_delay)
		-- end
		-- self.team_exp_loser_delay = GlobalTimerQuest:AddDelayTimer(function ()
							--self:OpenFuBenLoseView()
		local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
		self:OpenWin(is_linghunguangchang_fb and SceneType.LingHunGuangChang or SceneType.Wujinjitan, {}, get_exp, get_coin, 3, 10)
			-- end, 1.5)
	end
	if self.team_cheer_view:IsOpen() then
	 	self.team_cheer_view:Flush(0, "cheer")
	end
	if self.team_expfb_info_view:IsOpen() then
		self.team_expfb_info_view:Flush()
	end
end

--请求进入塔防副本
function FuBenWGCtrl:SendTaFangFb()
	self:SendEnterFB(FUBEN_TYPE.FBCT_TAFANG)
end

-- 购买副本进入次数
function FuBenWGCtrl:SendBuyFBEnterTimes(fb_type, param_1)
	-- local protocol = ProtocolPool.Instance:GetProtocol(CSBuyFBTimes)
	-- protocol.fb_type = fb_type
	-- protocol.param_1 = param_1 or 0
	-- protocol:EncodeAndSend()
end

--多人经验本
function FuBenWGCtrl:OpenTeamExpInfoView()
	self.set_wujin_jitan = false
	self.team_expfb_info_view:Open()
end

function FuBenWGCtrl:CloseTeamExpInfoView()
	self.set_wujin_jitan = false
	self.team_expfb_info_view:Close()
end

function FuBenWGCtrl:SetTeamFbBubbleVisible(is_visible)
	if self.team_expfb_info_view:IsOpen() then
		self.team_expfb_info_view:SetAddHurtBubbleVisit(is_visible)
	end
end

function FuBenWGCtrl:OpenTeamExpMdeicineView()
	if not self.team_expfb_medicine_view:IsOpen() then
		self.team_expfb_medicine_view:Open()
	end
end

function FuBenWGCtrl:CloseTeamExpMdeicineView()
	self.team_expfb_medicine_view:Close()
end

function FuBenWGCtrl:OpenTeamExpCheerView()
	self.team_cheer_view:Open()
end

function FuBenWGCtrl:CloseTeamExpCheerView()
    if self.team_cheer_view:IsOpen() then
        self.team_cheer_view:Close()
    end
end

function FuBenWGCtrl:OnSCTeamEquipFBInfo(protocol)
	self.team_equip_fb_data:SetTeamEquipFBInfo(protocol)
	self:SetOutFbTime(protocol.finish_timestamp)
	if protocol.kick_out_timestamp ~= 0 then
		self:SetOutFbTime(protocol.kick_out_timestamp)
	end
end

function FuBenWGCtrl:OnSCTeamEquipFBRoleInfo(protocol)
	self.team_equip_fb_data:SetTeamEquipRoleInfo(protocol)
	if 1 == protocol.is_fb_info and 0 == protocol.is_pass then
		local fb_info = FuBenWGData.Instance:GetFbEndInfo(protocol.get_coin, protocol.get_exp)
		if self.team_equip_loser_delay then
			GlobalTimerQuest:CancelQuest(self.team_equip_loser_delay)
		end
		self:OpenLose(SceneType.TEAM_EQUIP_FB)
		return
	end

	if 1 == protocol.is_pass then
		self:OpenWin(SceneType.TEAM_EQUIP_FB,protocol.reward_item_list , protocol.get_exp, protocol.get_coin, protocol.star_num, 10)
	end
	RemindManager.Instance:Fire(RemindName.YuanGuXianDian)
end

-----------------------------------------------
-- 远古仙殿
-----------------------------------------------
function FuBenWGCtrl:CloseHtePanel()
	if self.yuanguxiandian_logic_view:IsOpen() then
		self.yuanguxiandian_logic_view:Close()
	end
end
function FuBenWGCtrl:OpenHtePanel()
	if not self.yuanguxiandian_logic_view:IsOpen() then
		self.yuanguxiandian_logic_view:Open()
	end
end

function FuBenWGCtrl:OnYuanGuFBInfo(protocol)
	local fb_info = {}
	fb_info.layer = protocol.layer
    fb_info.is_finish = protocol.is_finish
    fb_info.is_pass = protocol.is_pass
    fb_info.curr_wave_index = protocol.curr_wave_index
    fb_info.max_wave_count = protocol.max_wave_count
    fb_info.cur_star_num = protocol.cur_star_num
    fb_info.is_helper = protocol.is_helper
    fb_info.prepare_end_timestamp = protocol.prepare_end_timestamp
    fb_info.finish_timestamp = protocol.finish_timestamp
    fb_info.next_star_timestamp = protocol.next_star_timestamp
    fb_info.next_wave_refresh_timestamp = protocol.next_wave_refresh_timestamp
    fb_info.kick_out_timestamp = protocol.kick_out_timestamp
    fb_info.pass_time_s = protocol.pass_time_s
    fb_info.assist_time = protocol.assist_time
    fb_info.kill_monster_num = protocol.kill_monster_num
	fb_info.cur_wave_monster_num = protocol.cur_wave_monster_num
	--if TeamEquipFbWGData.Instance:GetHteFbLevelNum() ~= protocol.cur_level_num then
	--	self:ShowLevelTips(protocol.cur_level_num)
	--end
	--print_error("远古仙殿场景信息 >>>>>>>>>>>>>>>>> ", protocol)
	TeamEquipFbWGData.Instance:SetHTEFbInfo(fb_info)
	ViewManager.Instance:FlushView(GuideModuleName.YuanGuXianDianLogicView)
	--星星动画
	--FuBenPanelWGCtrl.Instance:OpenStarAniView({})
	FuBenPanelWGCtrl.Instance:FlushStarAniView()
	--self.fuben_hte_panel:SetData(fb_info)
	-- FuBenWGData.Instance:GetSetFBPrepareTime( fb_info.prepare_end_timestamp + 1 )
	if fb_info.prepare_end_timestamp > 0 and fb_info.prepare_end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
		-- UiInstanceMgr.Instance:ShowFBStartDown2(fb_info.prepare_end_timestamp)
		UiInstanceMgr.Instance:DoFBStartDown(fb_info.prepare_end_timestamp + 1,function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
	end

	MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.finish_timestamp)

	if protocol.is_finish == 1 then
		if protocol.is_pass == 1 then
			local table_reward = {}
			for i= #protocol.reward_item_list,1,-1 do
				local data = protocol.reward_item_list[i]
				local item_config = ItemWGData.Instance:GetItemConfig(protocol.reward_item_list[i].item_id)
				if item_config then
					data.color = item_config.color
				else
					data.color = 1
				end
				data.star = data.star or 0
				table.insert(table_reward, data)
			end
			table.sort(table_reward, SortTools.KeyUpperSorters("color","star"))
			self:OpenWin(SceneType.HIGH_TEAM_EQUIP_FB, table_reward, protocol.get_exp, 0, fb_info.cur_star_num, 10)-------------------
		else
			self:OpenLose(SceneType.HIGH_TEAM_EQUIP_FB)
		end
		self:SetOutFbTime(0, true, true)
	else
		self:SetOutFbTime(protocol.finish_timestamp, true, true)
	end
end

--[[
function FuBenWGCtrl:OnYuanGuFBRoleInfo(protocol)
	-- print_error("远古仙殿",protocol)
	TeamEquipFbWGData.Instance:SetHTEFbTimes(protocol.have_got_reward_times)
	local table_reward = {}
	for i= #protocol.reward_item_list,1,-1 do
		table.insert(table_reward,protocol.reward_item_list[i])
	end

	if protocol.is_fb_info ~= 1 then return end
	if self.team_equip_win_delay then
		GlobalTimerQuest:CancelQuest(self.team_equip_win_delay)
	end
	local count = TeamEquipFbWGData.Instance:GetHTEFbTimes()
	-- print_error("远古仙殿 >>>>> ", protocol.is_pass, protocol.reward_item_list)
	if  count <= 0 and protocol.honor > 0 then
		self:SetZhuZhanTips(protocol.honor)
	else
		if not self.yuangu_guid_mark then
			-- self.team_equip_win_delay = GlobalTimerQuest:AddDelayTimer(
			-- 	function ()
			if protocol.is_pass == 1 then
				self:OpenWin(SceneType.HIGH_TEAM_EQUIP_FB, table_reward, protocol.get_exp, protocol.get_coin, 3, 10)
			else
				--GlobalTimerQuest:AddDelayTimer(function ()
				--self:OpenFuBenLoseView()
				self:OpenLose(SceneType.HIGH_TEAM_EQUIP_FB)
				--end, 1.5)
						--self:OpenFuBenLoseView()
			end
				-- end, 1.5)
		else
			self.yuangu_guid_info = protocol
		end

	end
	RemindManager.Instance:Fire(RemindName.YuanGuXianDian)
end
--]]

function FuBenWGCtrl:SendYuanGuFBReq(req_type,param1,param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSYuanGuFBReq)
	protocol.req_type = req_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-------诛神塔------------------

--副本场景信息
function FuBenWGCtrl:OnSCKillGodTwoerFBInfo(protocol)
	-- print_error('--诛神塔副本场景信息',protocol)
	FuBenWGData.Instance:SetZhushenTaFbInfo(protocol)
	-- print_error(protocol.kick_out_timestamp) --xxxx
	--FuBenWGData.Instance:GetSetFBPrepareTime( protocol.prepare_end_timestamp + 1 )
	if protocol.kick_out_timestamp ~= 0 then
		self:SetZhuShenTaBossFuHuoImage(false)
		self:SetOutFbTime(protocol.kick_out_timestamp) --退出副本倒计时
		FuBenPanelCountDown.Instance:SetTimerInfo(protocol.kick_out_timestamp)
	end
	if protocol.finish_timestamp -  TimeWGCtrl.Instance:GetServerTime() > 0 then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.finish_timestamp )
	end
	if self.zhushenta_view:IsOpen() then
		self.zhushenta_view:Flush()
	end
end

function FuBenWGCtrl:FlushZhuShenTaView()
	return self.zhushenta_view:Flush()
end
function FuBenWGCtrl:FlushZhuShenTaViewIsOpen()
	return self.zhushenta_view:IsOpen()
end
function FuBenWGCtrl:ZhuShenTaStarAni()
	self.zhushenta_view:InitStartPosition()
	self.zhushenta_view:FirstMoveAni()
end
function FuBenWGCtrl:SetZhuShenTaBossFuHuoImage(is_active)
	self.zhushenta_view:SetBossFuHuoImage(is_active)
end

function FuBenWGCtrl:OpenZhuShenTaView()
	if not self.zhushenta_view:IsOpen() then
		self.zhushenta_view:Open()
	end
	return self.zhushenta_view
end

function FuBenWGCtrl:CloseZhuShenTaView()
	self.zhushenta_view:Close()
end

function FuBenWGCtrl:OnSCKillGodTwoerBossInfo(protocol)
	self.data:SetIsCountDownKillBoss(true)
	self.data:SetKillBossTimeStamp(protocol.cd_timestamp)
end
function FuBenWGCtrl:OnSCKillGodFBResult(protocol)
	--print_error(protocol)
	GlobalTimerQuest:AddDelayTimer(function ()
			if Scene.Instance:GetSceneType() == SceneType.Common then
				local moli = protocol.add_lingli > 0 and protocol.add_lingli or 0
				local list = {}-- __TableCopy(protocol.reward_item_list)
				if moli <= 0 then
					--self:OpenFuBenLoseView()
					self:OpenLose(SceneType.ZHUSHENTA_FB)
				else
					table.insert(list,{item_id = 90595,num = moli})
					self:OpenWin(SceneType.ZHUSHENTA_FB, list, 0, moli, protocol.star_num, 10, nil, false)

				end
			end
		end,2)
end
--诛神塔副本结束结算
function FuBenWGCtrl:OnSCKillGodTowerSceneInfo(protocol)
--	 print_error('--诛神塔副本结束结算',protocol)
	if self.zhushenta_loser_delay then
		GlobalTimerQuest:CancelQuest(self.zhushenta_loser_delay)
	end
	if protocol.can_get_reaward == 1 then
		self:SetZhuZhanTips(protocol.honor)

	else
		--**********************奖励面板暂时走的是7309协议***************************
		-- self.zhushenta_loser_delay = GlobalTimerQuest:AddDelayTimer(
		-- 	function ()
		-- 		if protocol.is_pass == 1 or protocol.lingli > 0 then
		-- 			print_error(protocol.lingli,protocol.enterfb_reward_lingli)
		-- 			if Scene.Instance:GetSceneType() == SceneType.Common then
		-- 				local moli = protocol.lingli > 0 and protocol.lingli or 0
		-- 				local list = __TableCopy(protocol.reward_item_list)
		-- 				table.insert(list,{item_id = 90595,num = moli})
		-- 				self:SetCommonWinData(SceneType.ZHUSHENTA_FB, list, 0, moli, protocol.star_num, 10, nil, false)
		-- 			end
		-- 		else
		-- 			self:OpenFuBenLoseView()
		-- 		end
		-- 	end, 1.5)
	end
	if protocol.enter_next_timestamp > 0 then
		-- local scene_logic = Scene.Instance:GetSceneLogic()
		-- scene_logic:SetZuShenTaNextEnterLevelTime(protocol)
		-- self:OpenZhuShenTaNextView()
		local moli = protocol.lingli > 0 and protocol.lingli or 0
		local item_list = __TableCopy(protocol.reward_item_list)
		table.insert(item_list,{item_id = 90595,num = moli})
	--	self:OpenWin(SceneType.ZHUSHENTA_FB, item_list, 0, moli, protocol.star_num, math.floor(protocol.enter_next_timestamp - TimeWGCtrl.Instance:GetServerTime()), nil,true)
		-- print_error("倒计时进入下一层",protocol.enter_next_timestamp - TimeWGCtrl.Instance:GetServerTime())
		MainuiWGCtrl.Instance:SetShowTimeTextState( false )
		self.data:SetIsCountDownKillBoss(false)
	end

	if protocol.lingli > 0 then
		-- SystemHint.Instance:FloatingLabel(string.format(Language.FuBenPanel.GetMoLi, protocol.lingli))
	end
end

function FuBenWGCtrl:OpenGuildMiJingView()
	self.fuben_mijing_view:Open()
end

function FuBenWGCtrl:CloseGuildMiJingView()
	self.fuben_mijing_view:Close()
end

function FuBenWGCtrl:GetGuildMiJingView()
	return self.fuben_mijing_view
end

function FuBenWGCtrl:GetGuildShouHuRankView()
	return self.guild_shouhu_rank_view
end

-- 刷新仙盟Boss数据
function FuBenWGCtrl:ShowBossEndReward(data)
	local end_data = {}
	end_data.star_num = data.star_num --通关星级
	end_data.hurt = data.hurt 		  --通关伤害
	end_data.rank = data.rank         --通关个人排名
	end_data.is_success = data.is_part == 1  --是否通关
	end_data.part_time = data.part_time	 --通关时间(/s)
	end_data.is_boss = 1 				--作为标识区别守护与boss
	GuildWGCtrl.Instance:OpenJiwSuanView(end_data)
end

-- 刷新仙盟守护数据
function FuBenWGCtrl:SetGuildMjViewData(data)
	if 1 == data.is_finish then
		if CountDownManager.Instance:HasCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE) then
			CountDownManager.Instance:RemoveCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE)
		end
		-- if self.fuben_mijing_view:IsOpen() then
		-- 	self.fuben_mijing_view:SetNextWaveActive(false)
		-- end

		local end_data = {}
		end_data.guild_rank_pos = data.guild_rank_pos --服务器从0开始
		end_data.is_success = data.is_pass == 1
		end_data.pass_time = data.pass_time
		end_data.curr_wave = data.curr_wave
		end_data.is_shouhu = 1
		--print_error("结束 >>>>>>>>> ", end_data)
		GuildWGCtrl.Instance:OpenJiwSuanView(end_data)

	elseif data.notify_reason == GuildFbNotifyReason.WAIT and data.next_wave_time > 0 then
		local seconds = data.next_wave_time - TimeWGCtrl.Instance:GetServerTime()
		self:OpenMjNextWaveCountDown(seconds)
	    if self.boss_tips then
            GlobalTimerQuest:CancelQuest(self.boss_close_time)
            self.boss_close_time = nil
        end
		self.boss_tips = GlobalTimerQuest:AddDelayTimer(function()
			if self.fuben_mijing_view:IsOpen() then
				self.fuben_mijing_view:SetBossComingTips()
			end
		end,seconds)
	elseif data.notify_reason == GuildFbNotifyReason.ENTER and data.curr_wave == 0 and data.next_wave_time > 0 then
			GlobalTimerQuest:AddDelayTimer(function()
				if self.fuben_mijing_view:IsOpen() then
					self.fuben_mijing_view:Flush(nil, "part", {hide_title_btn = true})
				end

				local re_time = data.next_wave_time - TimeWGCtrl.Instance:GetServerTime()
				UiInstanceMgr.Instance:DoFBStartDown(data.next_wave_time, function()
					if self.fuben_mijing_view:IsOpen() then
						self.fuben_mijing_view:Flush(nil, "part", {show_title_btn = true})
					end
			 	end)
			end,2)
	elseif data.notify_reason == GuildFbNotifyReason.GUWU then
	 	-- print_error("鼓舞下发 >>>>>> ", data)
	 -- 	GuildWGCtrl.Instance:FlushGuWuView()
 	-- 	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		-- 	local t = {}
		-- 	t.scene_type = SceneType.GuildMiJingFB
		-- 	t.coin_guwu_count = 0
		-- 	t.gold_guwu_count = 0
		-- 	GlobalEventSystem:Fire(OtherEventType.FB_GUWU_CHANGE, t)
		-- end)
	end

	if self.fuben_mijing_view:IsOpen() then
		self.fuben_mijing_view:Flush()
	end
end

 --   打开下一波UI
function FuBenWGCtrl:OpenMjNextWaveCountDown(seconds)
	if CountDownManager.Instance:HasCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE) then
		return
	end
	-- if self.fuben_mijing_view:IsOpen() then
	-- 	self.fuben_mijing_view:SetNextWaveActive(seconds > 0)
	-- end

	-- local count_down_func = function(elapse_time, total_time)
	-- 	self:UpdateMjSceneCountDown(CountDownManager.Instance:GetRemainTime(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE))
	-- end

	-- local complete_func = function()
	-- 	CountDownManager.Instance:RemoveCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE)
		-- if self.fuben_mijing_view:IsOpen() then
		-- 	self.fuben_mijing_view:SetNextWaveActive(false)
		-- end
	-- end

	-- if seconds > 0 and (not CountDownManager.Instance:HasCountDown(COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE)) then
	-- 	CountDownManager.Instance:AddCountDown(
	-- 		COWNDOWN_TYPE.GUILD_FB_NEXT_WAVE,
	-- 		count_down_func,
	-- 		complete_func,
	-- 		nil,
	-- 		seconds,
	-- 		1)
	-- 	count_down_func(0, seconds)

	-- elseif seconds <= 0 then
	-- 	if self.fuben_mijing_view:IsOpen() then
	-- 		self.fuben_mijing_view:SetNextWaveActive(false)
	-- 	end
	-- end
end

-- 更新怪物波数倒计时
function FuBenWGCtrl:UpdateMjSceneCountDown(remain_time)
	-- if self.fuben_mijing_view:IsOpen() then
	-- 	self.fuben_mijing_view:SetNextWaveTime(remain_time)
	-- end
end

function FuBenWGCtrl:OnSCFbCanEnterOtherFBResult()
	-- local scene_name = Scene.Instance:GetSceneName()
	-- local str = string.format(Language.FuBenPanel.FuBenEnterFBLimit,scene_name)
	-- SysMsgWGCtrl.Instance:ErrorRemind(str)

	--取消传送限制，飞鞋失败清空结束状态
	TaskWGCtrl.Instance:ClearFlyUpList()
	if MoveCache.end_type == MoveEndType.NpcTask then
		GuajiWGCtrl.Instance:ResetMoveCache()
	end
end

function FuBenWGCtrl:OnSCWuJinJiTanFirstTimeInfo(protocol)
	-- body
	-- print_error("protocol.first_time:::", protocol.first_time)
	self.data:SetEXPFbIsFirstEnter(protocol.first_time)
	GlobalEventSystem:Fire(FuBenEvent.WuJinJiTanFirstTimeEvent, protocol.first_time)
end

function FuBenWGCtrl:GetTowerNotBuildNum()
	return self.defense_fb_data:GetTowerNotBuildNum()
end

function FuBenWGCtrl:GetIsNoTowerBuild()
	return self.defense_fb_data:GetIsNoTowerBuild()
end

function FuBenWGCtrl:GetTowerIsBuildByPosIndex(pos_index)
	return self.defense_fb_data:GetTowerIsBuildByPosIndex(pos_index)
end

function FuBenWGCtrl:GetDefneseGuajiPos()
	local other_cfg = self.defense_fb_data:GetDefenseTowerOtherCfg()
	return other_cfg.pos_x, other_cfg.pos_y
end

function FuBenWGCtrl.CanEnterFuben()
	if IS_ON_MATCHING then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.NoEnterFB)
		TaskGuide.Instance:CanAutoAllTask(false)
		return false
	end

	if MarryWGData.Instance:GetOwnIsXunyou() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInXunYou)
		return false
	end
	
	return true
end

function FuBenWGCtrl:YanShiStopGuaJi()
	if self.fuben_task_timer then
		GlobalTimerQuest:CancelQuest(self.fuben_task_timer)
		self.fuben_task_timer = nil
	end
	self.fuben_task_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.FuBenStopGuaJi,self), 1)--延时处理
end

function FuBenWGCtrl:FuBenStopGuaJi()
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
       	 GuajiWGCtrl.Instance:StopGuaji()
    	end)
end

function FuBenWGCtrl:SetPlayStarOnSceneLoadComplete(bo)
	self.is_should_play_start_action = bo
end

function FuBenWGCtrl:SetPlayLoseActionOnSceneLoadComplete(bo)
	self.is_should_play_lose_action = bo
end

--远古仙殿 诛神塔助战后的tips弹窗
function FuBenWGCtrl:SetZhuZhanTips(num)
	self.is_show_zhuzhan_tips = true
	self.show_zhuzhan_tips_des = string.format(Language.FuBen.Honor_Num,num)
end

function FuBenWGCtrl:ShowZhuZhanTips()
	if self.is_show_zhuzhan_tips then
		self.is_show_zhuzhan_tips = nil
		if self.show_zhuzhan_tips_des then
			SysMsgWGCtrl.Instance:ErrorRemind(self.show_zhuzhan_tips_des)
		end
	end
end

function FuBenWGCtrl:GetTowerBtnNode()
	if self.defense_fb_btn and self.defense_fb_btn:IsOpen() then
		local node = self.defense_fb_btn:GetTowerBtnNode()
		return node
	end
end

function FuBenWGCtrl:SetTaFangZhaoHuanMark(state)
	self.is_tafang_zhaohuan_fb = state
end

function FuBenWGCtrl:GetTaFangZhaoHuanMark()
	return self.is_tafang_zhaohuan_fb or false
end

--策划要求屏蔽第几关显示 2021/08/21
function FuBenWGCtrl:OpenFuBenLayerView(layer)
	-- self.fuben_layer_view:SetFBLayer(layer)
	-- self.fuben_layer_view:Open()
end

function FuBenWGCtrl:OnSCFBUseCombine(protocol)
	FuBenWGData.Instance:SetCombineMark(protocol)
	FuBenPanelWGCtrl.Instance:FlushCurIndex()
	ViewManager.Instance:FlushView(GuideModuleName.TeamPrepareView)
end

function FuBenWGCtrl:SendFBUseCombine(is_combine, fb_type)
	local times = FuBenWGData.Instance:GetEnterFbCombineCount(fb_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFBUseCombine)
	protocol.use_combine = is_combine or 0
	protocol.fb_type = fb_type
	protocol.times = times
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:ShowCombinePanel(fb_type, callback_func)
	if nil == self.fuben_combine or nil == fb_type then
		return
	end
	self.fuben_combine:SetBasData(fb_type,callback_func)
end

function FuBenWGCtrl:FlushCombinePanel()
	self.fuben_combine:Flush()
end

function FuBenWGCtrl:ShowSaoDangPanel(fb_type, param1, param2, callback_func)
	if nil == self.fuben_saodang or nil == fb_type then
		return
	end
	self.fuben_saodang:SetBasData(fb_type, param1, param2, callback_func)
end

function FuBenWGCtrl:FlushSaoDangPanel()
	self.fuben_saodang:Flush()
end

function FuBenWGCtrl:GetTaFangText()
	local nide_text =self.defense_fb_btn:GetTimeShowText()
	return nide_text
end

function FuBenWGCtrl:OnSCGuildFbStatus(protocol)
	self.data:SetGuildShoeHuEndTime(protocol)
end

function FuBenWGCtrl:CSFBPlayedCG()
	local protocol = ProtocolPool.Instance:GetProtocol(CSFBPlayedCG)
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:SetYuanGuGuidMark(Mark)
	if Mark then
		self.yuangu_guid_mark = Mark
		return
	end
	return self.yuangu_guid_mark
end

function FuBenWGCtrl:ShowYuanGuGuidWin()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then return end
	self.yuangu_guid_mark = nil
	local table_reward = {}
	for i= #self.yuangu_guid_info.reward_item_list,1,-1 do
		table.insert(table_reward,self.yuangu_guid_info.reward_item_list[i])
	end
	if self.team_equip_win_delay then
		GlobalTimerQuest:CancelQuest(self.team_equip_win_delay)
	end
	self:OpenWin(SceneType.YUGUXIANDIANYINDAO, table_reward, self.yuangu_guid_info.get_exp, self.yuangu_guid_info.get_coin, 3, 10)

end

function FuBenWGCtrl:SendSpecialTempReq(fb_type,is_robert,name_nv,name_nan)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSetSpecialTempReq)
	protocol.fb_type = fb_type
	protocol.bacceot_robot = is_robert
	protocol.robert_name1 = name_nv
	protocol.robert_name2 = name_nan
	protocol:EncodeAndSend()
end

--检测离开副本的时候是否打开界面
function FuBenWGCtrl:CheckLeaveOpenView(scene_type, tab_index)
	--如果不是主动点击退出按钮离开，就弹出界面
	if not FuBenWGData.Instance:GetFuBenInitiativeToLeave(scene_type) then
		--新手期任务不打开界面
		if TaskWGData.Instance:GetCurTaskState(scene_type) then
			--GlobalTimerQuest:AddDelayTimer(function()
				--ViewManager.Instance:Open(GuideModuleName.FuBenPanel, tab_index)
			--end, 2)
		end
		FuBenWGData.Instance:SetFuBenInitiativeToLeave(scene_type, nil)
	end
end

function FuBenWGCtrl:TryCloseCurFuHuoView()
	FuhuoWGCtrl.Instance:CloseCurView()
end

function FuBenWGCtrl:WinFbChangeSceneLoadComplete()
	if self.monitor_change_scene_win_fb_event then
		GlobalEventSystem:UnBind(self.monitor_change_scene_win_fb_event)
		self.monitor_change_scene_win_fb_event = nil
	end
	if self.change_scene_jiesuan_win_param and not IsEmptyTable(self.change_scene_jiesuan_win_param) then
		GlobalTimerQuest:AddDelayTimer(function()
			local data = self.change_scene_jiesuan_win_param
			self:SetCommonWinData(data.scene_type, data.data_list, data.exp, data.gold, data.star_num, data.fb_end_time, data.skill_index,data.is_enter_next)
			self.change_scene_jiesuan_win_param = nil
		end,0.5)
	end
end

function FuBenWGCtrl:OnSCManHuangGuDianBaseInfo(protocol)
	--print_error("蛮荒古殿基本信息", protocol.max_wave, protocol)
	ManHuangGuDianWGData.Instance:SetManHuangBaseInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_manhuanggudian)
	--刷新首通奖励界面
	ViewManager.Instance:FlushView(GuideModuleName.ManHuangGuDianFirstPassView)
	FuBenPanelWGCtrl.Instance:FlushManHuangLogicView()
	RemindManager.Instance:Fire(RemindName.ManHungGuDian)
end

function FuBenWGCtrl:OnSCManHuangGuDianRankAck(protocol)
	--print_error("蛮荒古殿排行信息", protocol)
	ManHuangGuDianWGData.Instance:SetManHuangRankInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_manhuanggudian)
	FuBenWGCtrl.Instance:FlushWinView()
end

function FuBenWGCtrl:OnSCLingHunSquareEnterTimes(protocol)
    FuBenPanelWGData.Instance:SetExpFubenTimes(protocol.enter_times)
end

function FuBenWGCtrl:OnSCManHuangGuDianFBInfo(protocol)
	ManHuangGuDianWGData.Instance:SetManHuangFBSceneInfo(protocol)

	FuBenPanelWGCtrl.Instance:FlushManHuangLogicView()
	--print_error(protocol.prepare_end_timestamp - TimeWGCtrl.Instance:GetServerTime())
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	--print_error("蛮荒古殿场景信息", protocol.prepare_end_timestamp - server_time, protocol)
	if protocol.prepare_end_timestamp > server_time then
		UiInstanceMgr.Instance:DoFBStartDown(protocol.prepare_end_timestamp + 1,function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
	end

	if protocol.is_finish == 1 then
		--第1波都没过，弹出失败面板
		if protocol.is_pass == 1 then
			local table_reward = {}
			for i, v in ipairs(protocol.reward_item_list) do
				local data = v
				local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
				if item_cfg then
					data.color = item_cfg.color
				else
					data.color = 1
				end
				table.insert(table_reward, data)
			end
			local exp_item = {}
			exp_item.item_id = COMMON_CONSTS.VIRTUAL_ITEM_EXP
			exp_item.num = protocol.get_exp
			exp_item.color = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.VIRTUAL_ITEM_EXP).color
			table.insert(table_reward, exp_item)
			table.sort(table_reward, SortTools.KeyUpperSorters("color"))
			--胜利界面
			self:OpenWin(SceneType.ManHuangGuDian_FB, table_reward, 0, 0, 0, 10)
		else
			--失败界面
			self:OpenLose(SceneType.ManHuangGuDian_FB)
		end
		self:SetOutFbTime(0, true, true)
	else
		--print_error("倒计时>>>>> ", protocol.finish_timestamp - TimeWGCtrl.Instance:GetServerTime())
		self:SetOutFbTime(protocol.finish_timestamp, true, true)
	end
end

function FuBenWGCtrl:SendManHuangGuDianReq(req_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSManHuangGuDianReq)
	protocol.req_type = req_type or MANHUANGGUDIAN_REQ_TYPE.RANK_INFO
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:SendManHuangGuDianOperate(req_type, param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSManHuangGuDianOpera)
	protocol.req_type = req_type
	protocol.param = param or 0
	protocol:EncodeAndSend()
end

function FuBenWGCtrl:CheckCanEnterFuBen()
	--3v3匹配中不可进入副本
	if KF3V3WGData.Instance:GetIsMatching() then
		return false, Language.CanNotEnterFBTips.KF3V3Matching
	end
	--护送中不可进入副本
	if YunbiaoWGData.Instance:GetIsHuShong() then
		return false, Language.CanNotEnterFBTips.HuSong
	end
	return true, ""
end

--是否可以进入副本
function FuBenWGCtrl:GetCanEnterFuBen()
	local can_enter, tip = self:CheckCanEnterFuBen()
	if not can_enter then
		if tip and tip ~= "" then
			SysMsgWGCtrl.Instance:ErrorRemind(tip)
		end
		return false
	end
	return true
end

function FuBenWGCtrl:OpenExpFuBenGoldGuWuTip(confirm_func)
	if not self.gold_guwu_alert then
		self.gold_guwu_alert = Alert.New()
		self.gold_guwu_alert:SetCheckBoxText(Language.Common.NoticeIsClose)
		self.gold_guwu_alert:SetCurLoginNoRemindKey("fb_gold_gw_login_remind")
		self.gold_guwu_alert:SetLableString(Language.FuBen.GuWuLackOFBangYu)
		self.gold_guwu_alert:SetShowCheckBox(true)
		self.gold_guwu_alert:SetCheckBoxDefaultSelect(false)
	end
	self.gold_guwu_alert:SetOkFunc(confirm_func)
	self.gold_guwu_alert:Open()
end

function FuBenWGCtrl:OpenFuBenNoticeView(data)
	if self.fuben_notice_view then
		self.fuben_notice_view:SetFuBenData(data)
		self.fuben_notice_view:Open()
		self.fuben_notice_view:Flush()
	end
end
