

--------------------------------------------------------------
-- vip,world,dabao boss itemrender boss列表item
--------------------------------------------------------------
BossFollowRender = BossFollowRender or BaseClass(BaseRender)

function BossFollowRender:__init(instance, parent)
	XUI.AddClickEventListener(self.node_list.BtnSelf,BindTool.Bind(self.OnClickBossRender, self))
    XUI.AddClickEventListener(self.node_list.bx_img,BindTool.Bind(self.OnClickRewardBox, self))
	self.boss_text = nil
	self.refresh_event = nil
	self.parent = parent
    self.had_dead = false
    self.is_show_guide = false
end

function BossFollowRender:__delete()
	self.boss_text = nil
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
    end

    if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
    end

	self.parent = nil
end

function BossFollowRender:OnClickRewardBox()
	local scene_type = Scene.Instance:GetSceneType()
	local can_get = BossWGData.Instance:IsShowRedPoint(self.data.boss_id)
	if can_get then
		local tab_index = -1
		local layer = -1
		if scene_type == SceneType.WorldBoss then
			tab_index = TabIndex.boss_world
			local boss_info = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
			if boss_info then
				layer = boss_info.layer
			end
		elseif scene_type == SceneType.VIP_BOSS then
			tab_index = TabIndex.boss_vip
			layer = BossWGData.Instance:GetVipBossLayerIndexByBossId(self.data.boss_id)
		end
		BossWGCtrl.Instance:OpenFirstKillView(true, tab_index, layer, self.data.boss_id)
	else
		BossWGCtrl.Instance:OpenFirstKillView()
	end
end

function BossFollowRender:OnClickBossRender()
    if self.is_show_guide then
        BossWGData.Instance:SetIsAlredyGuide()
    end
    local boss_data = self.data
    if nil ~= boss_data then
        local role_level = RoleWGData.Instance:GetRoleLevel()
        local is_xiezhu = BossXiezhuWGData.Instance:IsGotoXiezhu()
        local xiezhu_state = BossXiezhuWGData.Instance:GetXiezhuStatus()
        if boss_data.boss_level ~= nil and boss_data.max_delta_level ~= nil and role_level - boss_data.boss_level >= boss_data.max_delta_level then
            if nil == self.alert_window then
                self.alert_window = Alert.New(nil, nil, nil, nil, true)
            end
            local scene_type = Scene.Instance:GetSceneType()
            self.alert_window:SetLableString(Language.Boss.KillLevelHighTips)
            self.alert_window:SetShowCheckBox(true,"Boss"..scene_type)
            self.alert_window:SetBtnDislocation(true)
            self.alert_window:SetCancelString(Language.Boss.Cancel)
            self.alert_window:SetOkString(Language.Boss.QianWang)
            self.alert_window:SetOkFunc(
                function()
                    self:ClickBossRender(boss_data)
                end)
            self.alert_window:Open()
        elseif (is_xiezhu or xiezhu_state == ASSIST_STATUS.ASSIST_STATUS_INVOKE) and GuajiCache.monster_id ~= boss_data.boss_id then
        	local name = Language.BossXiezhu.XieZhuState[xiezhu_state] or ""
        	local tip_str = string.format(Language.BossXiezhu.CueStateGoBoss,name)
        	if nil == self.alert_window then
                self.alert_window = Alert.New(nil, nil, nil, nil, true)
            end
            self.alert_window:SetLableString(tip_str)
            self.alert_window:SetShowCheckBox(true,"Boss_Xiezhu_Tip")
            self.alert_window:SetCheckBoxDefaultSelect(false)
            self.alert_window:SetOkFunc(
                function()
                    self:ClickBossRender(boss_data)
                end)
            self.alert_window:Open()
        else
            self:ClickBossRender(boss_data)
        end
    end
	if self.parent then
		self.parent:SetCurIndex(self.data.boss_id)
        MainuiWGCtrl.Instance:FlushView(0, "xianli_info")
        local cur_is_enough, is_vip_boss = BossAssistWGData.Instance:JudgeIsEnoughBoss(self.data.boss_id)
        if is_vip_boss then
            if not cur_is_enough then
                BossAssistWGCtrl.Instance:JudgeCanOpenXianliTipView(self.data.boss_id)
            end
        end
	end
end

function BossFollowRender:ClickBossRender(boss_data)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
    BossWGCtrl.Instance:CancelSelectXianJieEnemy()

    if boss_data.type == BossWGData.MonsterType.Gather or boss_data.type == BossWGData.MonsterType.HideBoss then
        return SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.GatherTips)
    end

    local sence_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x,role_y = role:GetLogicPos()

    BossWGData.Instance:SetCurSelectBossID(0 , 0, boss_data.boss_id)

    if role_x == boss_data.x_pos and role_y == boss_data.y_pos then
        local scene_logic = Scene.Instance:GetSceneLogic()
    	if scene_logic ~= nil then
    		scene_logic:SetGuaJiInfoPos(role_x, role_y, nil, boss_data.boss_id)
    	end

		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
		local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_data.boss_id)
		if monster_cfg == nil then
			return
		end
        
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:ClearGuaJiInfo()
		end

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = boss_data.boss_id
		MoveCache.param1 = boss_data.boss_id
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			-- 切换挂机模式，以优先选择玩家 --
			if scene_logic ~= nil then
				local mian_role = Scene.Instance:GetMainRole()
				if mian_role ~= nil and not mian_role:IsDeleted() then
					local pos_x, pos_y = mian_role:GetLogicPos()
					if GameMath.GetDistance(boss_data.x_pos, boss_data.y_pos, pos_x, pos_y, false) <= range * range then
						scene_logic:SetGuaJiInfoPos(pos_x, pos_y, range, boss_data.boss_id)
					end
				end
			end

			GuajiWGCtrl.Instance:StopGuaji()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, boss_data.x_pos, boss_data.y_pos, range)
	end
end

function BossFollowRender:OnFlush()
	if next(self.data) == nil and not self.data then return end
	local str = ""
	if self.data.boss_name ~= nil and self.data.boss_name ~= "" then
		str = self.data.boss_name
	else
		str = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id].name
	end

	local cur_scene = Scene.Instance:GetSceneType()
	-- if self.data.type == BossWGData.MonsterType.Gather and cur_scene == SceneType.KF_BOSS then
	-- 	self.node_list["TextDesc"].rect.anchoredPosition = Vector2(10, -1)
	-- 	self.node_list["TimeDesc"].rect.anchoredPosition = Vector2(34, 14)
	-- else
	-- 	self.node_list["TextDesc"].rect.anchoredPosition = Vector2(58, -1)
    --     self.node_list["TimeDesc"].rect.anchoredPosition = Vector2(65, 14)
	-- end
	if self.data.type == BossWGData.MonsterType.Gather then
		-- 策划说不再显示场景有几个采集点.
		-- local boss_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
		-- local left_num = 0
		-- if boss_info == nil then
			
		-- 	left_num = BossWGData.Instance:GetShangGuBossSceneOtherInfo(self.data.layer + 1, self.data.type)
		-- else
		-- 	left_num = boss_info.left_num
		-- end
		-- left_num = (nil == left_num) and 0 or left_num
		-- str = str.."  (" .. left_num .. ")"
		self.node_list.TextDesc.text.text = str
		self.node_list.TimeDesc.text.text = ""
	elseif self.data.type == BossWGData.MonsterType.HideBoss then
		if self.data.left_num > 0 then
			self.node_list["TextDesc"].text.text = self.data.boss_name .. "  (" .. self.data.left_num .. ")"
			self.node_list.TimeDesc.text.text = ""
		else
			self.node_list["TextDesc"].text.text = self.data.boss_name
			self.node_list.TimeDesc.text.text = ToColorStr(Language.Boss.NoFlush, "#dd4b4c")
		end
	elseif cur_scene == SceneType.KF_BOSS and self.data.type == BossWGData.MonsterType.Boss then
        -- str = self.data.boss_name.." Lv.".. self.data.boss_level
        str = self.data.boss_name
    elseif cur_scene == SceneType.XianJie_Boss then
    	str = self.data.boss_name..RoleWGData.Instance:TransToDianFengLevelStr(self.data.boss_level)
		 -- and self.data.type == BossWGData.MonsterType.Monster   不要的判斷條件
	elseif cur_scene == SceneType.SG_BOSS or
			cur_scene == SceneType.WorldBoss or
			cur_scene == SceneType.DABAO_BOSS or
			cur_scene == SceneType.HONG_MENG_SHEN_YU or
			cur_scene == SceneType.VIP_BOSS or
            cur_scene == SceneType.CROSS_EVERYDAY_RECHARGE_BOSS or
            cur_scene == SceneType.HUNDRED_EQUIP then
		str = str..RoleWGData.Instance:TransToDianFengLevelStr(ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id].level)
		-- self.node_list.TextTaskType.text.text = ""
	end
	self.boss_text = str

	if cur_scene == SceneType.WorldBoss or cur_scene == SceneType.VIP_BOSS then
		local can_get = BossWGData.Instance:IsShowRedPoint(self.data.boss_id)
		local is_show_global, can_get_golbal = BossWGData.Instance:GetIsWorldFirstKilledByBossId(self.data.boss_id)
		self.node_list["bx_img"]:SetActive(is_show_global)
		self.node_list["bx_img_kl"]:SetActive(can_get)
		self.node_list["bx_img_bkl"]:SetActive(not can_get)
	else
		self.node_list["bx_img"]:SetActive(false)
		self.node_list["personal_firstkill"]:SetActive(false)
	end

    local is_drop = self.data.is_drop_jewelry and self.data.is_drop_jewelry == 1
	if self.node_list.flag_shipin then
		self.node_list.flag_shipin:SetActive(is_drop)
	end

    self.node_list["guide"]:SetActive(false)
    if cur_scene == SceneType.VIP_BOSS then
        self.is_show_guide = BossWGData.Instance:GetIsShowBossNoticeGuide(self.data.boss_id)
        -- self.node_list["guide"]:SetActive(self.is_show_guide)
        -- if not self.tween_weapon then
        --     UITween.MoveLoop(self.node_list["guide"].gameObject,u3dpool.vec3(70, 0, 0), u3dpool.vec3(80, 0, 0), 1)
        --     self.tween_weapon = true
        -- end
    end

	self:RefreshRemainTime()
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
	end
	self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),0.5)
end

function BossFollowRender:RefreshRemainTime()
	if self.data then
		local cur_scene = Scene.Instance:GetSceneType()
		if self.data.type == BossWGData.MonsterType.Gather then
			-- 策划说不再显示场景有几个采集点.
			-- local boss_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
			-- if boss_info == nil then
			-- 	local left_num = BossWGData.Instance:GetShangGuBossSceneOtherInfo(self.data.layer + 1, self.data.type)
			-- 	self.boss_text = self.data.boss_name .. "(" .. left_num .. ")"
			-- else
			-- 	self.boss_text = self.data.boss_name .. "(" .. boss_info.left_num ..")"
			-- end
			self.boss_text = self.data.boss_name
		end

        local scene_index
        if cur_scene == SceneType.HONG_MENG_SHEN_YU then
            scene_index = BossWGData.Instance:GetCurHMSYSceneIndex()
        end

        -- local change_time_rect = false
        if self.data.slot_name and self.data.slot_page_limit then
            local page_str2 = XianJieBossWGData.Instance:GetPageStr(self.data.slot_page_limit, true)
            self.node_list["jieshu_di"]:SetActive(true)
            self.node_list["jieshu_text"].text.text = string.format(Language.XianJieBoss.SlotSNoPoint, self.data.slot_name, page_str2)
        else
            local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
            if monster_info and monster_info.boss_jieshu > 0 and self.data.type ~= BossWGData.MonsterType.Gather then
                self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, monster_info.boss_jieshu)
                self.node_list.jieshu_di:SetActive(true)
            else
                self.node_list.jieshu_text.text.text = ""
                self.node_list.jieshu_di:SetActive(false)
                -- change_time_rect = true
            end
        end
        
        if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
            self.node_list.jieshu_di:SetActive(false)
        end

        -- self.node_list.TimeDesc.rect.sizeDelta = change_time_rect and u3dpool.vec2(138, 30) or u3dpool.vec2(84, 30)

		local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id, scene_index)
		if boss_info == nil then
			boss_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
        end

        local time_txt
        
        if cur_scene == SceneType.XianJie_Boss then 
            boss_info = XianJieBossWGData.Instance:GetBossCfgById(self.data.boss_id)
            if boss_info and (boss_info.next_refresh_time or 0) -  TimeWGCtrl.Instance:GetServerTime() > 1 then
                time_txt =  ToColorStr(TimeUtil.FormatSecond(boss_info.next_refresh_time - TimeWGCtrl.Instance:GetServerTime(), 3),COLOR3B.RED)
            else
                time_txt = ToColorStr(Language.Boss.BossRefresh, "#9DF5A7")
            end
        elseif cur_scene == SceneType.CROSS_EVERYDAY_RECHARGE_BOSS then
            boss_info = BossWGData.Instance:GetERBBossInfoByBossId(self.data.boss_id)

            if not IsEmptyTable(boss_info) then
                if boss_info.alive then
                    time_txt = ToColorStr(Language.Boss.BossRefresh, "#9DF5A7")
                else
                    local is_special_boss = BossWGData.Instance:IsERBSpecialBoss(self.data.boss_id)
    
                    if is_special_boss then
                        time_txt = ToColorStr(Language.Boss.ErbBossDeathStatus, COLOR3B.RED)
                    else
                        time_txt = ToColorStr(TimeUtil.FormatSecond(boss_info.next_refresh_time - TimeWGCtrl.Instance:GetServerTime(), 3),COLOR3B.RED)
                    end
                end
            end
        elseif cur_scene ~= SceneType.XianJie_Boss then
            if boss_info == nil or self.data.type == BossWGData.MonsterType.HideBoss then
                if self.refresh_event then
                    GlobalTimerQuest:CancelQuest(self.refresh_event)
                    self.refresh_event = nil
                end
                return
            end
            
            if (boss_info.next_refresh_time or 0) -  TimeWGCtrl.Instance:GetServerTime() > 1 then
                if self.data.type == BossWGData.MonsterType.Gather and cur_scene == SceneType.KF_BOSS then
                    time_txt =  ToColorStr(TimeUtil.FormatSecond(boss_info.next_refresh_time - TimeWGCtrl.Instance:GetServerTime(), 3)..Language.Boss.LiveTime,COLOR3B.RED)
                else
                    time_txt =  ToColorStr(TimeUtil.FormatSecond(boss_info.next_refresh_time - TimeWGCtrl.Instance:GetServerTime(), 3),COLOR3B.RED)
                end
                self.had_dead = true
            else

                -- 出现了已刷新与怪物实际未刷新的情况 怀疑是因为服务端数据的下发并未成功刷新怪物列表
                -- if self.refresh_event then
                -- 	GlobalTimerQuest:CancelQuest(self.refresh_event)
                -- 	self.refresh_event = nil
                -- end

                -- if self.had_dead then
                --     self.had_dead = false
                --     local scene_type = Scene.Instance:GetSceneType()
                --     if scene_type == SceneType.VIP_BOSS then
                --         GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
                --     end
                -- end

                time_txt = ToColorStr(Language.Boss.BossRefresh, "#9DF5A7")
            end
        elseif cur_scene == SceneType.HUNDRED_EQUIP then
            time_txt = self.data.state == 0 and Language.HundredEquip.FuBenName3 or Language.HundredEquip.FuBenName2
        end
           

		-- str从未被使用
		-- local str
		-- if self.data.type == BossWGData.MonsterType.Gather then
		-- 	local boss_info = BossWGData.Instance:GetCrossBossById(self.data.boss_id)
		-- 	if boss_info == nil then
		-- 		local left_num = BossWGData.Instance:GetShangGuBossSceneOtherInfo(self.data.layer + 1, self.data.type)
		-- 		str = str.."  ("..left_num..")"
		-- 	else
		-- 		str = self.data.boss_name.."  ("..boss_info.left_num..")"
		-- 	end
		-- elseif Scene.Instance:GetSceneType() == SceneType.SG_BOSS and self.data.type == BossWGData.MonsterType.Monster then
		-- 	str = str.." Lv.".. ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id].level
		-- end

		self.node_list.TextDesc.text.text = self.boss_text
		self.node_list.TimeDesc.text.text = time_txt
	end
end

function BossFollowRender:FlushHl()
	if self.node_list["SelectLigth"] then
		self.node_list["SelectLigth"]:SetActive(self.parent:GetCurIndex() == self.data.boss_id)
	end
end

-- 不创建选中特效
function BossFollowRender:CreateSelectEffect()
end

function BossFollowRender:IsShowBossNotice()
    return self.is_show_guide
end

--打宝boss小怪列表item
EliteFollowRender = EliteFollowRender or BaseClass(BaseRender)

function EliteFollowRender:__init(instance, parent)
    XUI.AddClickEventListener(self.node_list.BtnSelf,BindTool.Bind(self.OnClickBossRender, self))
    XUI.AddClickEventListener(self.node_list.BtnSelf1,BindTool.Bind(self.OnClickBossRender, self))
	self.parent = parent
end

function EliteFollowRender:__delete()
    self.parent = nil
    self.boss_text = nil
	if self.refresh_event then
		GlobalTimerQuest:CancelQuest(self.refresh_event)
		self.refresh_event = nil
    end
end

function EliteFollowRender:LoadCallBack()
	
end

function EliteFollowRender:OnClickBossRender()
    local boss_data = self.data
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

    if boss_data.type == BossWGData.MonsterType.Gather then
        return SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.GatherTips)
    end

    local sence_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x,role_y = role:GetLogicPos()

    if  role_x == boss_data.x_pos and role_y == boss_data.y_pos then
        local scene_logic = Scene.Instance:GetSceneLogic()
    	if scene_logic ~= nil then
    		scene_logic:SetGuaJiInfoPos(role_x, role_y)
    	end

		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
		local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(boss_data.boss_id)
		if monster_cfg == nil then
			return
		end
        
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:ClearGuaJiInfo()
		end

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = boss_data.boss_id
		MoveCache.param1 = boss_data.boss_id
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			-- 切换挂机模式，以优先选择玩家 --
			if scene_logic ~= nil then
				local mian_role = Scene.Instance:GetMainRole()
				if mian_role ~= nil and not mian_role:IsDeleted() then
					local pos_x, pos_y = mian_role:GetLogicPos()
					if GameMath.GetDistance(boss_data.x_pos, boss_data.y_pos, pos_x, pos_y, false) <= range * range then
						scene_logic:SetGuaJiInfoPos(pos_x, pos_y, range, boss_data.boss_id)
					end
				end
			end

			GuajiWGCtrl.Instance:StopGuaji()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, boss_data.x_pos, boss_data.y_pos, range)
    end
    if self.parent then
        self.parent:SetCurIndex(self.data.boss_id)
        self.parent:FlushMonsterHl()
    end
end

function EliteFollowRender:OnFlush()
    self.is_dabao = Scene.Instance:GetSceneType() == SceneType.DABAO_BOSS
    self.node_list.dabao_elite:SetActive(self.is_dabao) --神魔禁地小怪
    self.node_list.world_elite:SetActive(not self.is_dabao) --世界boss小怪

    if next(self.data) == nil and not self.data then return end
    local monster_data =  ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
    if self.is_dabao then
        local str = ""
        self.node_list.TextDesc.text.text =  monster_data.name
        self.node_list.TimeDesc.text.text = " Lv.".. monster_data.level
    else
       self:FlushWorldElite()
    end
end

function EliteFollowRender:FlushWorldElite()
    local monster_data =  ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
    local str = monster_data.name
    str = str..RoleWGData.Instance:TransToDianFengLevelStr(monster_data.level)
    self.boss_text = str

    self:RefreshRemainTime()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
    self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),0.5)
end

function EliteFollowRender:RefreshRemainTime()
	if self.data then
		local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data.boss_id)
		if boss_info == nil then
			if self.refresh_event then
				GlobalTimerQuest:CancelQuest(self.refresh_event)
				self.refresh_event = nil
			end
			return
		end
		local time_txt
		if (boss_info.next_refresh_time or 0) -  TimeWGCtrl.Instance:GetServerTime() > 1 then
			time_txt = ToColorStr(TimeUtil.FormatSecond(boss_info.next_refresh_time - TimeWGCtrl.Instance:GetServerTime(), 3),COLOR3B.RED)
		else
			time_txt = ToColorStr(Language.Boss.BossRefresh, "#9DF5A7")
		end
		self.node_list.TextDesc1.text.text = self.boss_text
		self.node_list.TimeDesc1.text.text = time_txt
	end
end


function EliteFollowRender:FlushHl()
    self.is_dabao = Scene.Instance:GetSceneType() == SceneType.DABAO_BOSS
    if self.is_dabao then
        if self.node_list["SelectLigth"] then
            self.node_list["SelectLigth"]:SetActive(self.parent:GetCurIndex() == self.data.boss_id)
        end
    else
        if self.node_list["Light"] then
            self.node_list["Light"]:SetActive(self.parent:GetCurIndex() == self.data.boss_id)
        end
    end
end
