--个人、深渊boss购买次数 （vipboss弃用）
BossVipTimesView = BossVipTimesView or BaseClass(DayCountChangeView)

function BossVipTimesView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal
	self.ui_config = {"uis/view/boss_ui_prefab", "BossUi"}
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_vip_times")  --world_boss_buytimes_view 使用相同预制体 修改注意
end

function BossVipTimesView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
    if self.vip_change_event then
        GlobalEventSystem:UnBind(self.vip_change_event)
        self.vip_change_event = nil
    end
end

function BossVipTimesView:LoadCallBack()
    DayCountChangeView.LoadCallBack(self)
    self.node_list.title_view_name.text.text = Language.Boss.AddCount
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	--self.node_list["btn_enter"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self, true))
    self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))
    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))
    XUI.AddClickEventListener(self.node_list["btn_xufei"], BindTool.Bind(self.OnClickXuFei, self))

	self.node_list.item_num_des:CustomSetActive(false)
	self.node_list.btn_item_add:CustomSetActive(false)
	self.node_list.text_buy.tmp.text = Language.Boss.Enter
	
end

function BossVipTimesView:OnClickXuFei()
	-- VipWGCtrl.Instance:OpenVipRenewView()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_vip)
end

function BossVipTimesView:SetVipType(vip_type)
	self.vip_type = vip_type
end

function BossVipTimesView:ShowIndexCallBack()
	self:Flush()
end

function BossVipTimesView:OnVipInfoChange()
    self:Flush()
end

function BossVipTimesView:OnFlush()
    local is_vip = VipWGData.Instance:IsVip()
	self.node_list.btn_xufei:SetActive(not is_vip)
    self.node_list.btn_cancel:SetActive(is_vip)
    self.node_list.btn_buy_count:SetActive(is_vip)

    local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = VipPower.Instance:GetPowerCfg(self.vip_type)
	local copper_info,cur_max_times = 0, 0
	local buy_count = 0
	local cost = 0
    local cost_text = Language.Common.GoldText
	if self.vip_type == VipPowerId.vat_person_boss_extra_buy_times then
		copper_info, cur_max_times = BossWGData.Instance:GetPersonalBossEnterInfo()
		buy_count = BossWGData.Instance:GetPersonBossBuyCount()
		cost = BossWGData.Instance:GetPersonBossBuyTimesExtra(buy_count + 1)
        if bind_gold_num >= cost then
            cost_text = Language.Common.BindGoldText
        end
		self.node_list.rich_buy_desc.tmp.text = string.format(Language.Boss.VipBuyTimesText, cost_text, cost, Language.Boss.TabSub1[2])
	elseif self.vip_type == VipPowerId.boss_home_buy_times then
		local vipboss_times_info = BossWGData.Instance:GetBossHomeEnterTimeInfo()
		local wb_other_cfg =ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
		cur_max_times = wb_other_cfg.boss_home_day_default_times + vipboss_times_info.boss_home_day_item_add_kill_times + vipboss_times_info.boss_home_day_vip_buy_kill_times
		copper_info = cur_max_times - vipboss_times_info.boss_home_day_kill_times
		buy_count = vipboss_times_info.boss_home_day_vip_buy_kill_times
		cost= BossWGData.Instance:GetVIPBossBuyTimesExtra(buy_count + 1)
        if bind_gold_num >= cost then
            cost_text = Language.Common.BindGoldText
        end
        self.node_list.rich_buy_desc.tmp.text = string.format(Language.Boss.VipBuyTimesText, cost_text, cost, Language.Boss.TabSub1[3])
    elseif self.vip_type == VipPowerId.vat_shenyuanboss_buy_times then
		buy_count = BossWGData.Instance:GetShenYuanBuyJoinTimes()
		cost = BossWGData.Instance:GetShenyuanBossBuyTimesExtra(buy_count + 1)
        if bind_gold_num >= cost then
            cost_text = Language.Common.BindGoldText
        end
		self.node_list.rich_buy_desc.tmp.text = string.format(Language.Boss.VipBuyTimesText, cost_text, cost, Language.WorldServer.ShenyuanJoin)
	end

	local next_vip = 0
	local can_buy = vip_buy_cfg["param_" .. role_vip] or 0
	for i=role_vip,12 do
		if vip_buy_cfg["param_" .. i] > can_buy then
			next_vip = i
			break
		end
	end
	--self.node_list.btn_enter:SetActive(buy_count >= can_buy)
	--self.node_list.can_buy:SetActive(buy_count < can_buy)
	--self.node_list.rich_buy_desc:SetActive(buy_count < can_buy)

	if next_vip < 1 then
		next_vip = role_vip
    end
    local color = can_buy - buy_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
	self.node_list["rich_buy_des_1"].tmp.text = string.format(Language.Boss.CopperBuyTips3, color, can_buy - buy_count,can_buy)
	if next_vip == role_vip then
		self.node_list["rich_buy_des_2"].tmp.text = Language.Boss.CopperBuyTips4
	else
		self.node_list["rich_buy_des_2"].tmp.text = string.format(Language.Boss.CopperBuyTips2,vip_buy_cfg["param_" .. next_vip])
	end

    if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].tmp.text = "V"..role_vip
		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..next_vip
			self.node_list["layout_next_vip"]:SetActive(true)
		else
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..role_vip
			self.node_list["layout_next_vip"]:SetActive(false)
		end
	end
end

function BossVipTimesView:OnClinkCancelHandler(show_tips)
	if show_tips then
		local vip_level = VipWGData.Instance:GetRoleVipLevel()
		local max_level = VipWGData.Instance:GetMaxVIPLevel()
		if vip_level == max_level then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.AddCountMax)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.VipTips)
		end

	end
	self:Close()
end

function BossVipTimesView:OnClinkBuyCount()
    local max_vip_level = VipWGData.Instance:GetMaxVIPLevel()
    local vip_buy_cfg = VipPower.Instance:GetPowerCfg(self.vip_type)
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local now_count = vip_buy_cfg["param_" .. role_vip] or 0
    local buy_times = 0
    if self.vip_type == VipPowerId.vat_person_boss_extra_buy_times then
        buy_times = BossWGData.Instance:GetPersonBossBuyCount()
    elseif self.vip_type == VipPowerId.vat_shenyuanboss_buy_times then
        buy_times = BossWGData.Instance:GetShenYuanBuyJoinTimes()
    end
	local times = now_count - buy_times
	local is_max_count = vip_buy_cfg["param_" .. max_vip_level] == buy_times

	if 0 == times then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.BuyMaxCount)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.VIPTooLow)
		end
	else
		if self.vip_type == VipPowerId.vat_person_boss_extra_buy_times then
            BossWGCtrl.SendPersonBossBuyTimesReq()
        elseif self.vip_type == VipPowerId.vat_shenyuanboss_buy_times then
            BossWGCtrl.SendShenyuanBossBuyTimesReq()
        end
	end
end

