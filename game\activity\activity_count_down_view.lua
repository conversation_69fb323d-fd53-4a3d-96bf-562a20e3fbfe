ActivityCountDownView = ActivityCountDownView or BaseClass(SafeBaseView)

function ActivityCountDownView:__init(view_name)
	self.view_name = "ActivityCountDownView"
	self.view_layer = UiLayer.MainUI
	self.calc_active_close_ui_volume = false
	self:AddViewResource(0, "uis/view/act_count_down_prefab", "act_count_down")

	self:InitParam()
end

function ActivityCountDownView:LoadCallBack()
	self.is_show_panel = false
	self.node_list.count_down_root:SetActive(false)
end

function ActivityCountDownView:ReleaseCallBack()
	if self.slider_tween then
		self.slider_tween:Kill()
		self.slider_tween = nil
	end
	self:InitParam()
	CountDownManager.Instance:RemoveCountDown("activity_view_count_down")
end

function ActivityCountDownView:CloseCallBack()
	self:InitParam()
end

function ActivityCountDownView:InitParam()
	self.act_type = nil
	self.timestamp = 0
	self.calculate_time = 0
	self.count_down_time = 0
	self.end_call_back = nil
	self.is_auto_close = true
	self.raw_image_res_name = nil
end

function ActivityCountDownView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "count_down_info" then
			self.act_type = v.act_type
			self.timestamp = v.timestamp or 0
			self.count_down_time = v.count_down_time
			self.calculate_time = v.calculate_time
			self.end_call_back = v.end_call_back
			self.is_auto_close = v.is_auto_close
			self.raw_image_res_name = v.image_name
			self:FlushCountDown()
			self:FlushActRawImage()
		end
	end
end

function ActivityCountDownView:GetCurActType()
	return self.act_type
end

function ActivityCountDownView:FlushCountDown()
	CountDownManager.Instance:RemoveCountDown("activity_view_count_down")

	local count_down_time = 0
	if self.count_down_time then
		count_down_time = self.count_down_time
	elseif self.timestamp then
		local ser_time = TimeWGCtrl.Instance:GetServerTime()
		count_down_time = math.floor(self.timestamp - ser_time)
	end

	if count_down_time > 0 then
		if count_down_time <= self.calculate_time then
			self.node_list.count_down_label.text.text = count_down_time
			self.node_list.count_down_root:SetActive(true)
			self.is_show_panel = true
			--self:PlaySliderTween((self.calculate_time - count_down_time) / self.calculate_time, count_down_time)
		elseif self.is_show_panel then
			self.node_list.count_down_root:SetActive(false)
			self.is_show_panel = false
		end
		CountDownManager.Instance:AddCountDown(
			"activity_view_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.CountDownComplete, self),
			nil,
			count_down_time,
			1
		)
	end
end

function ActivityCountDownView:FlushActRawImage()
	if self.raw_image_res_name and self.raw_image_res_name ~= "" then
		self.node_list.act_raw_img.raw_image:LoadSprite(ResPath.GetRawImagesPNG(self.raw_image_res_name))
		self.node_list.act_raw_img:SetActive(false)
	else
		self.node_list.act_raw_img:SetActive(false)
	end
end

function ActivityCountDownView:UpdateCountDown(elapse_time, total_time)
	local sub_time = GameMath.Round(total_time - elapse_time)
	if sub_time <= self.calculate_time then
		self.node_list.count_down_label.text.text = sub_time
		--self:PlaySliderTween(0, sub_time)
		if not self.is_show_panel then
			self.node_list.count_down_root:SetActive(true)
			self.is_show_panel = true
		end
	end
end

function ActivityCountDownView:PlaySliderTween(value, sub_time)
	if self.slider_tween then
		return
	end
	self.node_list.count_down_slider.image.fillAmount = value or 0
	self.slider_tween = self.node_list.count_down_slider.image:DOFillAmount(1, sub_time)
	self.slider_tween:SetEase(DG.Tweening.Ease.Linear)
	self.slider_tween:OnComplete(function ()
		self.slider_tween = nil
	end)
end

function ActivityCountDownView:CountDownComplete()
	if self.end_call_back then
		self.end_call_back()
	end
	if self.is_auto_close then
		self:Close()
	else
		self.node_list.count_down_root:SetActive(false)
		self.is_show_panel = false
	end
end