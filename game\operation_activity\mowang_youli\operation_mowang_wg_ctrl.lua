require("game/operation_activity/mowang_youli/operation_mowang_wg_data")

OperationMoWangWGCtrl = OperationMoWangWGCtrl or BaseClass(BaseWGCtrl)

function OperationMoWangWGCtrl:__init()
	if OperationMoWangWGCtrl.Instance then
		ErrorLog("[OperationMoWangWGCtrl] Attemp to create a singleton twice !")
	end
	OperationMoWangWGCtrl.Instance = self

	self.data = OperationMoWangWGData.New()

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
    self:BindGlobalEvent(OtherEventType.BossInfoChange, BindTool.Bind(self.OnBossInfoChange, self))
    OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_mowangyouli_auto", BindTool.Bind(self.OnHotUpdate, self))
    self:RegisterAllProtocols()
end

function OperationMoWangWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    OperationMoWangWGCtrl.Instance = nil
end

function OperationMoWangWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSOAMoWangYouLiDropLimitItemInfoReq)
    self:RegisterProtocol(SCOAMoWangYouLiDropLimitItemInfo,'OnSCOAMoWangYouLiDropLimitItemInfo')
end

function OperationMoWangWGCtrl:OnPassDay()
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowang_youli)
end

function OperationMoWangWGCtrl:OnBossInfoChange(boss_type)
    local old_enter_times, old_max_times = self.data:GetBossTimesInfo(boss_type)
    local enter_times, max_times = BossWGData.Instance:GetBossTimesInfo(boss_type)
    if old_enter_times ~= enter_times or old_max_times ~= max_times then
        self.data:SetBossTimesInfo(boss_type, enter_times, max_times)
        OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowang_youli, "boss_times")
    end
end

function OperationMoWangWGCtrl:OnHotUpdate()
	self.data:LoadConfig()
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowang_youli, "mowang_youli_hot_update")
end

function OperationMoWangWGCtrl:OnSCOAMoWangYouLiDropLimitItemInfo(protocol)
	self.data:SetItemInfoData(protocol)
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_mowang_youli,"mowang_youli_times")
end

function OperationMoWangWGCtrl:ReqCOAMoWangYouLiDropLimitItemInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSOAMoWangYouLiDropLimitItemInfoReq)
 	protocol:EncodeAndSend()
end