require("game/operation_activity/denglu_youli/login_reward_wg_data")

--运营活动-登录有礼
LoginRewardWGCtrl = LoginRewardWGCtrl or BaseClass(BaseWGCtrl)

function LoginRewardWGCtrl:__init()
	if LoginRewardWGCtrl.Instance then
		ErrorLog("[LoginRewardWGCtrl] Attemp to create a singleton twice !")
	end
	LoginRewardWGCtrl.Instance = self

    self.data = LoginRewardWGData.New()

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.LOGINREWARDInfoReq, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/operation_activity_new_login_reward_auto", BindTool.Bind(self.OnHotUpdate, self))

	self:RegisterAllProtocols()
end

function LoginRewardWGCtrl:LOGINREWARDInfoReq()
	self:SendActivityRewardOp(OPERATION_LOGIN_GIFT_OP_TYPE.INFO)
end

function LoginRewardWGCtrl:__delete()
	LoginRewardWGCtrl.Instance = nil

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

end

function LoginRewardWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOALoginRewardRet,'SCOALoginRewardRet')
end

--登录奖励信息
function LoginRewardWGCtrl:SCOALoginRewardRet(protocol)
	self.data:SetLoginRewardInfo(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_login_reward, "RefreshLogin")
end

function LoginRewardWGCtrl:OnHotUpdate()
	self.data:LoadConfig()
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_login_reward)
end

function LoginRewardWGCtrl:SendActivityRewardOp(opera_type,param1)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSOALoginReward)
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol:EncodeAndSend()
end

function LoginRewardWGCtrl:ShowLoginDayReward(day_index)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_login_reward, "login_reward",{day_index})
end