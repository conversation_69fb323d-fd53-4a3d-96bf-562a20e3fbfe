BoundlessJoyWGData = BoundlessJoyWGData or BaseClass()
function BoundlessJoyWGData:__init()
    if BoundlessJoyWGData.Instance then
        Error<PERSON><PERSON>("[HappyForeverWGData] attempt to create singleton twice!")
        return
    end
    BoundlessJoyWGData.Instance = self

    local boundless_joy_cfg = ConfigManager.Instance:GetAutoConfig("happy_forever_auto")
    self.other_cfg = boundless_joy_cfg.other
    self.shop_reward_cfg = boundless_joy_cfg.product

	RemindManager.Instance:Register(RemindName.BoundlessJoy, BindTool.Bind(self.IsShowBoundlessJoyRedPoint, self))
end

function BoundlessJoyWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.BoundlessJoy)
    BoundlessJoyWGData.Instance = nil
end

--全部数据
function BoundlessJoyWGData:SetHappyForeverAllInfo(protocol)
    self.is_acquipre_reward = protocol.is_acquipre_reward
    self.gift_buy_times = protocol.buy_times
	self.is_all_buy = protocol.is_all_buy == 0
    self.is_buy_free = protocol.is_acquipre_free_reward == 0
    self.choose_pool_seq_list = protocol.choose_pool_seq_list
end

--获取免费商品配置
function BoundlessJoyWGData:GetCurFreeShopCfg()
	return self.other_cfg[1]
end

--获取每日礼包领取状态
function BoundlessJoyWGData:GetShopIsBuyFlag()
	return self.is_buy_free
end

--获取一键购买状态
function BoundlessJoyWGData:GetAllCostBtnIsBuyFlag()
	return self.is_all_buy
end

--获取免费商品领取状态
function BoundlessJoyWGData:GetFreeShopFlag(seq)
    return self.is_acquipre_reward and self.is_acquipre_reward[seq] or 0
end

function BoundlessJoyWGData:GetRewardPoolSeqList(seq)
    return self.choose_pool_seq_list and self.choose_pool_seq_list[seq] or {}
end

--奖池
function BoundlessJoyWGData:GetALLRewardPoolCfg()
    return self.shop_reward_cfg
end

function BoundlessJoyWGData:GetChooseRewardCfg(seq)
    return self.shop_reward_cfg[seq]
end

--获取礼包购买次数
function BoundlessJoyWGData:GetGiftBuyTimes(seq)
    return self.gift_buy_times and self.gift_buy_times[seq] or 0
end

--获取已选择奖励奖池
function BoundlessJoyWGData:GetChooseRewardPool(seq, is_sort)
    local choose_reward_data = {}
    local choose_pool_seq_list = self:GetRewardPoolSeqList(seq)
    local choose_reward_cfg = self:GetChooseRewardCfg(seq)
    for k, v in ipairs(choose_pool_seq_list) do
        if v == 1 then
            local data = {}
            local reward_cfg = choose_reward_cfg.reward_item[k]
            data.item_id = reward_cfg and reward_cfg.item_id or 0
            data.num = reward_cfg and reward_cfg.num or 0
            data.is_bind = reward_cfg and reward_cfg.is_bind or 0
            data.reward_index = k
            if is_sort then
                local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
                data.sort = item_cfg and item_cfg.color * 100 + (99 - k) or k
            end

            table.insert(choose_reward_data, data)
        end
    end

    if is_sort then
        table.sort(choose_reward_data, SortTools.KeyUpperSorter("sort"))
    end

    return choose_reward_data
end

--红点显示
function BoundlessJoyWGData:IsShowBoundlessJoyRedPoint()
    local buytimes_list = self.gift_buy_times
    local is_acquipre_reward_list = self.is_acquipre_reward
    for k, v in pairs(buytimes_list) do
        if v == 7 then
            if is_acquipre_reward_list[k] == 0 then
                return 1
            end
        end
    end

    local is_buy_free = self:GetShopIsBuyFlag()
	if is_buy_free then
		return 1
	end

    return 0
end