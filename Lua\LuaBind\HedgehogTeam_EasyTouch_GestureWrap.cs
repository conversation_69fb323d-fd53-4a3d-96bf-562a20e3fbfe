﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class HedgehogTeam_EasyTouch_GestureWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(HedgehogTeam.EasyTouch.Gesture), typeof(HedgehogTeam.EasyTouch.BaseFinger));
		<PERSON><PERSON>unction("<PERSON>lone", <PERSON>lone);
		<PERSON><PERSON>unction("GetTouchToWorldPoint", GetTouchToWorldPoint);
		<PERSON><PERSON>RegFunction("GetSwipeOrDragAngle", GetSwipeOrDragAngle);
		<PERSON>.RegFunction("NormalizedPosition", NormalizedPosition);
		<PERSON><PERSON>RegFunction("IsOverUIElement", IsOverUIElement);
		<PERSON><PERSON>RegFunction("IsOverRectTransform", IsOverRectTransform);
		L.RegFunction("GetCurrentFirstPickedUIElement", GetCurrentFirstPickedUIElement);
		<PERSON><PERSON>unction("GetCurrentPickedObject", GetCurrentPickedObject);
		<PERSON><PERSON>unction("New", _CreateHedgehogTeam_EasyTouch_Gesture);
		<PERSON><PERSON>ction("__tostring", ToLua.op_ToString);
		L.RegVar("swipe", get_swipe, set_swipe);
		L.RegVar("swipeLength", get_swipeLength, set_swipeLength);
		L.RegVar("swipeVector", get_swipeVector, set_swipeVector);
		L.RegVar("deltaPinch", get_deltaPinch, set_deltaPinch);
		L.RegVar("twistAngle", get_twistAngle, set_twistAngle);
		L.RegVar("twoFingerDistance", get_twoFingerDistance, set_twoFingerDistance);
		L.RegVar("type", get_type, set_type);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateHedgehogTeam_EasyTouch_Gesture(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				HedgehogTeam.EasyTouch.Gesture obj = new HedgehogTeam.EasyTouch.Gesture();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: HedgehogTeam.EasyTouch.Gesture.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
			object o = obj.Clone();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTouchToWorldPoint(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<float>(L, 2))
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				float arg0 = (float)LuaDLL.lua_tonumber(L, 2);
				UnityEngine.Vector3 o = obj.GetTouchToWorldPoint(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UnityEngine.Vector3>(L, 2))
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 o = obj.GetTouchToWorldPoint(arg0);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.Gesture.GetTouchToWorldPoint");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSwipeOrDragAngle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
			float o = obj.GetSwipeOrDragAngle();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int NormalizedPosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
			UnityEngine.Vector2 o = obj.NormalizedPosition();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsOverUIElement(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
			bool o = obj.IsOverUIElement();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsOverRectTransform(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				bool o = obj.IsOverRectTransform(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3)
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
				UnityEngine.Camera arg1 = (UnityEngine.Camera)ToLua.CheckObject(L, 3, typeof(UnityEngine.Camera));
				bool o = obj.IsOverRectTransform(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.Gesture.IsOverRectTransform");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurrentFirstPickedUIElement(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				UnityEngine.GameObject o = obj.GetCurrentFirstPickedUIElement();
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 2)
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				UnityEngine.GameObject o = obj.GetCurrentFirstPickedUIElement(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.Gesture.GetCurrentFirstPickedUIElement");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurrentPickedObject(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				UnityEngine.GameObject o = obj.GetCurrentPickedObject();
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 2)
			{
				HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)ToLua.CheckObject<HedgehogTeam.EasyTouch.Gesture>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				UnityEngine.GameObject o = obj.GetCurrentPickedObject(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.Gesture.GetCurrentPickedObject");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_swipe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection ret = obj.swipe;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_swipeLength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float ret = obj.swipeLength;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeLength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_swipeVector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			UnityEngine.Vector2 ret = obj.swipeVector;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeVector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_deltaPinch(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float ret = obj.deltaPinch;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index deltaPinch on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_twistAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float ret = obj.twistAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twistAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_twoFingerDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float ret = obj.twoFingerDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twoFingerDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_type(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			HedgehogTeam.EasyTouch.EasyTouch.EvtType ret = obj.type;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index type on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_swipe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection arg0 = (HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection)ToLua.CheckObject(L, 2, typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeDirection));
			obj.swipe = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_swipeLength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.swipeLength = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeLength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_swipeVector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.swipeVector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeVector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_deltaPinch(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.deltaPinch = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index deltaPinch on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_twistAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.twistAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twistAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_twoFingerDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.twoFingerDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twoFingerDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_type(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.Gesture obj = (HedgehogTeam.EasyTouch.Gesture)o;
			HedgehogTeam.EasyTouch.EasyTouch.EvtType arg0 = (HedgehogTeam.EasyTouch.EasyTouch.EvtType)ToLua.CheckObject(L, 2, typeof(HedgehogTeam.EasyTouch.EasyTouch.EvtType));
			obj.type = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index type on a nil value");
		}
	}
}

