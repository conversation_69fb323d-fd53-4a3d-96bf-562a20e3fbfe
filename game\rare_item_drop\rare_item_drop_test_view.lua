RareItemDropTestView = RareItemDropTestView or BaseClass(SafeBaseView)

function RareItemDropTestView:__init(view_name)
	self.view_name = "RareItemDropTestView"
	self.view_layer = UiLayer.PopWhite
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(900,500)})
	self:AddViewResource(0, "uis/view/rare_item_drop_ui_prefab", "layout_rare_item_test")
	self:SetMaskBg(true, true)
end

function RareItemDropTestView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function RareItemDropTestView:CloseCallBack()
	if not IsEmptyTable(self.item_id_list) then
		for i=1,7 do
			PlayerPrefsUtil.SetInt("RareItem_item_id_flag" .. i, self.item_id_list[i] or 0)
			PlayerPrefsUtil.SetInt("RareItem_star_num_flag" .. i, self.star_num_list[i] or 0)
			PlayerPrefsUtil.SetInt("RareItem_item_type_flag" .. i, self.item_type_list[i] or 0)
		end
	end
end

function RareItemDropTestView:InitParam()
	self.item_id_list = {}
	self.star_num_list = {}
	self.item_type_list = {}
end

function RareItemDropTestView:InitPanel()

end

function RareItemDropTestView:OnFlush()
	for i=1,7 do
		local item_id = PlayerPrefsUtil.GetInt("RareItem_item_id_flag" .. i)
		if item_id > 0 then
			self.node_list["item_id_" .. i].input_field.text = item_id
		end
		local star_num = PlayerPrefsUtil.GetInt("RareItem_star_num_flag" .. i)
		if star_num > 0 then
			self.node_list["star_" .. i].input_field.text = star_num
		end
		local item_type = PlayerPrefsUtil.GetInt("RareItem_item_type_flag" .. i)
		if item_type > 0 then
			self.node_list["type_" .. i].dropdown.value = item_type
		end
	end
end

function RareItemDropTestView:InitListener()
	XUI.AddClickEventListener(self.node_list.test_btn, BindTool.Bind1(self.OnClickTestBtn, self))
end

function RareItemDropTestView:OnClickTestBtn()
	local item_list = {}
	for i=1,7 do
		local str = self.node_list["item_id_" .. i].input_field.text
		item_list[i] = tonumber(str) or 0
	end
	self.item_id_list = item_list

	local star_list = {}
	for i=1,7 do
		local str = self.node_list["star_" .. i].input_field.text
		star_list[i] = tonumber(str) or 0
	end
	self.star_num_list = star_list

	local type_list = {}
	for i=1,7 do
		local str = self.node_list["type_" .. i].dropdown.value
		type_list[i] = tonumber(str) or 0
	end
	self.item_type_list = type_list

	local min_color, max_color = RareItemDropWGData.Instance:GetLimitColor()
	local min_star, max_star = RareItemDropWGData.Instance:GetLimitStar()

	local bag_list = {}

	for i=1,#type_list do
		if item_list[i] > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(item_list[i])
			if not item_cfg then
				SysMsgWGCtrl.Instance:ErrorRemind("找不到配置id" .. item_list[i])
				return
			end

			local type = type_list[i] + 1
			if type == RareItemType.Equip then
				if item_cfg.color < min_color and item_cfg.color > max_color then
					SysMsgWGCtrl.Instance:ErrorRemind("物品id" .. item_list[i] .. "品质不对")
					return
				elseif star_list[i] < min_star or star_list[i] > max_star then
					SysMsgWGCtrl.Instance:ErrorRemind("物品id" .. item_list[i] .. "星级不对")
					return
				end
				local temp = {item_id = item_list[i], star_level = star_list[i], rare_item_type = RareItemType.Equip}
				bag_list[#bag_list + 1] = temp
			elseif type == RareItemType.Fashion then
				if not item_cfg.is_display_role or item_cfg.is_display_role <= 0 then
					SysMsgWGCtrl.Instance:ErrorRemind("外显物品id" .. item_list[i] .. "配置不对")
					return
				end 
				local temp = {item_id = item_list[i], star_level = star_list[i], rare_item_type = RareItemType.Fashion}
				bag_list[#bag_list + 1] = temp
			elseif type == RareItemType.Special then
				local special_cfg = RareItemDropWGData.Instance:GetSpecialRareItemCfg(item_list[i])
				if not special_cfg then
					SysMsgWGCtrl.Instance:ErrorRemind("特殊物品id" .. item_list[i] .. "配置未找到")
					return
				end
				local temp = {item_id = item_list[i], rare_item_type = RareItemType.Special}
				bag_list[#bag_list + 1] = temp
			end
		end
	end

	RareItemDropWGData.Instance.bag_rare_item_list = bag_list

	RareItemDropWGCtrl.Instance:ShowRareItemView()

	self:Close()
end