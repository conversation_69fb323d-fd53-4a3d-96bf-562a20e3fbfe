-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local develop_mode = require("editor/develop_mode")

local localPosition = Vector3(0, 0, 0)
local localRotation = Quaternion.Euler(0, 0, 0)
local localScale = Vector3(1, 1, 1)
local DefaultLayer = UnityEngine.LayerMask.NameToLayer("Default")
local InvisibleLayer = UnityEngine.LayerMask.NameToLayer("Invisible")

local M = ResUtil.create_class()
function M:_init(root, v_act_root, full_path)
	self.v_root = root
	self.v_act_root = v_act_root
	self.full_path = full_path or "none"

	self.v_cache_count = 0
	self.v_cache_gos = {}

	self.v_orginal_transform_info = nil
	self.v_cache_time = 0
	self.v_use_times = 0
	self.v_ref_count = 0
	self.v_is_valid_pool = true
	self.v_release_policy = ResPoolReleasePolicy.Default
end

function M:CacheOrginalTransformInfo(prefab)
	if nil == prefab then
		print_error("[GameObjPool] CacheOrginalTransformInfo big bug, prefab is nil", self.full_path)
		return
	end

	if nil == self.v_orginal_transform_info then
		self.v_orginal_transform_info = {prefab.transform.localPosition, prefab.transform.localRotation, prefab.transform.localScale}
	end
end

function M:GetCacheCount()
	return self.v_cache_count
end

function M:Release(gameobj, release_policy)
	if not self.v_is_valid_pool then
		print_error("[GameObjPool] Release big bug, the pool is invalid")
		ResMgr:__Destroy(gameobj)
		return false
	end

	self.v_ref_count = self.v_ref_count - 1
	if self.v_ref_count < 0 then
		print_error("[GameObjPool] Release big bug, v_ref_count less 0")
		return false
	end

	if self.v_cache_count >= 64 then
		ResMgr:__Destroy(gameobj)
		return true
	end

	self.v_release_policy = release_policy or ResPoolReleasePolicy.Defualt
	self.v_cache_count = self.v_cache_count + 1
	self.v_cache_time = self:CalcCacheTime(self.v_use_times)
	self.v_cache_gos[gameobj] = GlobalUnityTime + self.v_cache_time

	if self.v_release_policy == ResPoolReleasePolicy.Culling then

		gameobj.transform:SetParent(self.v_act_root, false)
	else
		gameobj:SetActive(false)
		gameobj.transform:SetParent(self.v_root, false)
	end

	if develop_mode:IsDeveloper() then
		develop_mode:OnLuaCall("push_gameobj_pool", self, gameobj)
	end

	return true
end

-- 根据使用次数来控制释放时间，有些使用次数少的可以快些释放
function M:CalcCacheTime(use_times)
	local cache_time = 0
	if self.v_release_policy == ResPoolReleasePolicy.NotDestroy then
		cache_time = 999999
	elseif IsLowMemSystem then
		cache_time = 10
	else
		cache_time = 15 + (use_times / 20) * 60
		if cache_time > 60 then
			cache_time = 60
		end
	end

	return cache_time
end

function M:GetGameObjIsCache(gameobj)
	return nil ~= self.v_cache_gos[gameobj]
end

function M:ReleaseInObjId(cid)
	if not self.v_is_valid_pool then
		print_error("[GameObjPool] ReleaseInObjId big bug, the pool is invalid")
		return false
	end

	self.v_ref_count = self.v_ref_count - 1
	if self.v_ref_count < 0 then
		print_error("[GameObjPool] Release big bug, v_ref_count less 0")
		return false
	end

	return true
end

function M:TryPop()
	if not self.v_is_valid_pool then
		print_error("[GameObjPool] TryPop big bug, the pool is invalid")
		return false
	end

	self.v_ref_count = self.v_ref_count + 1
	if self.v_cache_count <= 0 then
		return nil
	end

	local gameobj = next(self.v_cache_gos)
	if self.v_release_policy == ResPoolReleasePolicy.Culling then
		
	else
		gameobj:SetActive(true)
	end

	if nil ~= self.v_orginal_transform_info then
		gameobj.transform.localPosition = self.v_orginal_transform_info[1]
		gameobj.transform.localRotation = self.v_orginal_transform_info[2]
		gameobj.transform.localScale = self.v_orginal_transform_info[3]
	else
		gameobj.transform.localPosition = localPosition
		gameobj.transform.localRotation = localRotation
		gameobj.transform.localScale = localScale
	end

	self.v_cache_gos[gameobj] = nil
	self.v_cache_count = self.v_cache_count - 1
	self.v_use_times = self.v_use_times + 1

	if develop_mode:IsDeveloper() then
		develop_mode:OnLuaCall("pop_gameobj_pool", self, gameobj)
	end

	return gameobj
end

function M:Update(now_time)
	if not self.v_is_valid_pool then
		return false
	end
	
	local flag = false
	for k, v in pairs(self.v_cache_gos) do
		if now_time >= v then
			flag = true
			self.v_cache_count = self.v_cache_count - 1
			self.v_cache_gos[k] = nil
			ResMgr:__Destroy(k)
			break
		end
	end

	if flag and self.v_cache_count <= 0 and self.v_ref_count <= 0 then
		self.v_is_valid_pool = false
	end

	return not self.v_is_valid_pool
end

function M:Clear()
	local flag = false
	for k,v in pairs(self.v_cache_gos) do
		flag = true
		ResMgr:__Destroy(k)
	end

	self.v_cache_gos = {}
	self.v_cache_count = 0
	self.v_use_times = 0

	if flag and self.v_cache_count <= 0 and self.v_ref_count <= 0 then
		self.v_is_valid_pool = false
	end

	return not self.v_is_valid_pool
end

function M:OnDestroy()
	self:Clear()
end

function M:GetDebugStr()
	local debug_str = string.format("%s   count=%s\n", self.full_path, self.v_cache_count)

	return debug_str
end

return M
