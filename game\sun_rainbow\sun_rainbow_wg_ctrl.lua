require("game/sun_rainbow/sun_rainbow_total_view")
require("game/sun_rainbow/sun_rainbow_view")
require("game/sun_rainbow/sun_rainbow_wg_data")
require("game/sun_rainbow/sun_library_view")
require("game/sun_rainbow/sun_probability_view")
require("game/sun_rainbow/sun_shop_view")
require("game/sun_rainbow/sun_record_view")

SunRainbowWgCtrl = SunRainbowWgCtrl or BaseClass(BaseWGCtrl)
function SunRainbowWgCtrl:__init()
    if SunRainbowWgCtrl.Instance then
        ErrorLog("[SunRainbowWgCtrl] Attemp to create a singleton twice !")
    end

    SunRainbowWgCtrl.Instance = self
    self.view = SunRainbowView.New(GuideModuleName.SunRainbowView)
    self.data = SunRainbowWgData.New()

    self.sun_library_view = SunLibraryView.New()                        -- 奖励展示
    self.sun_probability_view = SunProbabilityView.New()                -- 概率公示
    -- self.sun_shop_view = SunShopView.New(GuideModuleName.SunShopView)	-- 积分商城
    self.sun_record_view = SunRecordView.New()							-- 抽奖记录

    self:RegisterAllProtocols()

    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
end

function SunRainbowWgCtrl:__delete()
    SunRainbowWgCtrl.Instance = nil

    self.view:DeleteMe()
    self.view = nil

    self.data:DeleteMe()
    self.data = nil

    self.sun_library_view:DeleteMe()
    self.sun_library_view = nil

    self.sun_probability_view:DeleteMe()
    self.sun_probability_view = nil

    -- self.sun_shop_view:DeleteMe()
    -- self.sun_shop_view = nil

    self.sun_record_view:DeleteMe()
    self.sun_record_view = nil

    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

--请求
function SunRainbowWgCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUANRI_CHANGHONG,
		opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function SunRainbowWgCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCSunRainbowInfo,'OnSCSunRainbowInfo')
    self:RegisterProtocol(SCRASunRainbowRecordListInfo, "OnSCRASunRainbowRecordListInfo")
    self:RegisterProtocol(SCRASunRainbowRewardInfo, "OnSCRASunRainbowRewardInfo")
end

function SunRainbowWgCtrl:OnSCSunRainbowInfo(protocol)
	-- print_error("====AllInfo=====",protocol)
    self.data:SetAllInfo(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end

	-- if self.sun_shop_view:IsOpen() then
    --     self.sun_shop_view:Flush()
    -- end
	
	RemindManager.Instance:Fire(RemindName.SunRainbow)
end

function SunRainbowWgCtrl:OnSCRASunRainbowRecordListInfo(protocol)
	-- print_error("====Record=====",protocol)
	self.data:SetRecord(protocol.record_list)

	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function SunRainbowWgCtrl:OnSCRASunRainbowRewardInfo(protocol)
    -- print_error("====Reward=====" ,protocol)
    local btn_index = self.data:CacheOrGetDrawIndex()
    if not btn_index then
        return
    end

    local consume_cfg = self.data:GetConsumeCfg()
    local btn_cfg = consume_cfg[btn_index]
    if not btn_cfg then
        return
    end

    local number_str = NumberToChinaNumber(btn_cfg.onekey_draw_num)
    local str = string.format(Language.SunRainbow.BtnStr2, number_str)
    local ok_func = function ()
		ViewManager.Instance:FlushView(GuideModuleName.SunRainbowView)
		self:ClickUse(btn_index, function()
            self:SendReq(RA_GUANRICHANGHONG_OP_TYPE.DRAW, btn_cfg.onekey_draw_num, 1)
        end)
    end

	local item_cfg = ItemWGData.Instance:GetItemConfig(btn_cfg.draw_item.item_id)
	if IsEmptyTable(item_cfg) then
		return
	end

	local cost_item_num = btn_cfg.draw_item.num
	local has_num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id) --拥有的材料数量
	if has_num < cost_item_num then
		cost_item_num = consume_cfg[1].consume_price
	end

    local data_list, item_ids = self.data:CalDrawRewardList(protocol)

    local other_info = {}
    other_info.again_text = str
    other_info.stuff_id = btn_cfg.draw_item.item_id
    other_info.times = btn_cfg.draw_item.num
    other_info.spend = cost_item_num
	other_info.cost_type = btn_cfg.consume_type
    local best_data = {}
    if IsEmptyTable(item_ids) then
        best_data = nil
    else
        best_data.item_ids = item_ids
    end
    other_info.best_data = best_data

    TipWGCtrl.Instance:ShowGetCommonReward(data_list, ok_func, other_info)

    -- 神品大奖弹窗
    local data_cfg_list = {}
    for index, value in ipairs(item_ids) do
        local cfg = TipWGData.Instance:GetBigRewardCfgByItemId(value)
        if not IsEmptyTable(cfg) then
            table.insert(data_cfg_list, cfg)
        end
    end
    if not IsEmptyTable(data_cfg_list) then
        TipWGCtrl.Instance:OpenBigRewardView(data_cfg_list)
    end
end

function SunRainbowWgCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE  then
        local check_list = self.data:GetItemDataChangeList()
        for i, v in pairs(check_list) do
            if v == change_item_id then
                self.view:Flush()
                RemindManager.Instance:Fire(RemindName.SunRainbow)
            end
        end
    end
end

function SunRainbowWgCtrl:ClickUse(index, func)
    --数量检测
   local cfg = self.data:GetConsumeCfg()
   local tips_data = {}
   tips_data.item_id = cfg[index].draw_item.item_id
   tips_data.price = cfg[1].consume_price
   tips_data.draw_count = cfg[index].draw_item.num
   tips_data.has_checkbox = true
   tips_data.checkbox_str = string.format("sun_rainbow_fresh_%d", index)
    local consume_type = cfg[index].consume_type
    local cost_type = 1
    if consume_type == 1 then
        cost_type = COST_TYPE.LINGYU
    elseif consume_type == 2 then
        cost_type = COST_TYPE.YANYUGE_SCORE
    end

   tips_data.cost_type =  cost_type
   TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, func, nil)
end

function SunRainbowWgCtrl:OpenSunRainbowView()
    self.view:Open()
end

function SunRainbowWgCtrl:OpenSunLibraryView()
    self.sun_library_view:Open()
end

function SunRainbowWgCtrl:OpenSunProbabilityView()
    self.sun_probability_view:Open()
end

-- function SunRainbowWgCtrl:OpenSunShopView()
--     self.sun_shop_view:Open()
-- end

function SunRainbowWgCtrl:OpenSunRecordView()
    self.sun_record_view:Open()
end


