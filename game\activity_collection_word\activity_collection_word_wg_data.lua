ActivityCollectionWordWGData = ActivityCollectionWordWGData or BaseClass()

function ActivityCollectionWordWGData:__init()
    if ActivityCollectionWordWGData.Instance then
        error("[ActivityCollectionWordWGData] Attempt to create singleton twice!")
    end

    ActivityCollectionWordWGData.Instance = self

    self.grade = 0
    self.convert_flag_list = {}

    --初始化配置信息
    self:InitConfig()
    --红点注册
    RemindManager.Instance:Register(RemindName.RemindCollectionWord, BindTool.Bind(self.GetRemind, self))
    self:RegisterItemRemindInBag()
end

function ActivityCollectionWordWGData:__delete()
    --红点销毁
    RemindManager.Instance:UnRegister(RemindName.RemindCollectionWord)

    ActivityCollectionWordWGData.Instance = nil
end

function ActivityCollectionWordWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_exchange_shop_auto")

    --通过档次拿到配置表
    self.convert_cfg = ListToMapList(cfg.convert, "grade")
end

function ActivityCollectionWordWGData:RegisterItemRemindInBag()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_exchange_shop_auto").convert[1]
    if not cfg then
        return
    end

	local item_id_list = {}
    local item_id = 0
    for i = 1, 4 do
        item_id = cfg["stuff_id_" .. i] or 0
        if item_id > 0 then
            table.insert(item_id_list, item_id)
        end
    end

	BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.RemindCollectionWord, item_id_list)
end

--数据存储
function ActivityCollectionWordWGData:SetCollWordInfo(protocol)
    self.grade = protocol.grade
    self.convert_flag_list = protocol.convert_flag_list  --根据索引获取每个奖励已经兑换了的次数
end

--返回当前档次
function ActivityCollectionWordWGData:GetActCollWordRound()
    return self.grade or 0
end

--根据索引拿到对应的兑换按钮的已经兑换的次数
function ActivityCollectionWordWGData:GetExchangeNum(seq)
    return self.convert_flag_list[seq] or 0
end

--根据当前档次返回配置表奖励数据
function ActivityCollectionWordWGData:GetRewardList()
    return self.convert_cfg[self.grade] or {}
end

--拿到配置表第一行的数据
function ActivityCollectionWordWGData:GetFirstInfo()
    local data_list = {}
    local cfg_list = self:GetRewardList()
    if cfg_list[1] == nil then
        return data_list
    end

    table.insert(data_list, cfg_list[1])
   -- print_error("获取到第一个的信息", data_list)
    return data_list
end

--判断红点,兑换按钮是否出现红点
function ActivityCollectionWordWGData:GetIsCanExchang()
    --判断活动是否开启
    local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACTIVITY_TYPE_OA_EXCHANGE_SHOP)
  --  print_error("act_is_open", act_is_open)
    if not act_is_open then
        return false
    end

    --拿到配置数据
    local cfg_list = self:GetRewardList()
    --对应物品的id
    local item_id = 0
    --对应物品需要的数量
    local wupin_item_num = 0
    --保存背包物品对应的数量
    local bag_item_num = 0
    local can_exchange = false

    --拿到本地保存的判断兑换提醒按钮的信息
    local flag = PlayerPrefsUtil.GetInt("OpenServerCollWordToggle") or 0
    local for_remind_flag = bit:d2b(flag)
    --print_error("for_remind_flag", for_remind_flag)
    for k, v in ipairs(cfg_list) do
        --拿到协议发过来的对应的已兑换的次数
        local change_num = self:GetExchangeNum(v.seq)
        --计算剩余兑换次数
        local remain_exchang_num = v.time_limit - change_num
      --  print_error("remain_exchang_num", remain_exchang_num)
        --判断兑换按钮是否开启
        if for_remind_flag[k] ~= 1 then
            local is_can = true
         --   print_error("for_remind_flag[k]", v.seq)
            --记录当前状态
            for i = 1, 4 do
                --拿到对应物品id
                item_id = v["stuff_id_" .. i] or 0
                --拿到对应物品所需数量
                wupin_item_num = v["stuff_num_" .. i] or 0
                if item_id > 0 and wupin_item_num > 0 then
                    --拿到背包中对应物品的数量
                    bag_item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
                  -- print_error("remain_exchang_num", remain_exchang_num)
                 --  print_error("bag_item_num", bag_item_num,"wupin_item_num", wupin_item_num)
                    if remain_exchang_num <= 0 or bag_item_num < wupin_item_num then
                            is_can = false
                    end
                end
            end
            if is_can then
                can_exchange = is_can
            end
        end
    end

    return can_exchange
end


function ActivityCollectionWordWGData:GetRemind()
  --  print_error("self:GetIsCanExchang()", self:GetIsCanExchang())
    --需要根据兑换提醒是否开启进行判断
    if  self:GetIsCanExchang() then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.COLLECTION_WORD_TIP, 1, function ()
            ActivityCollectionWordWGCtrl.Instance:OpenCollectionWord()
            return true
        end)

        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.COLLECTION_WORD_TIP, 0)
    return 0
end