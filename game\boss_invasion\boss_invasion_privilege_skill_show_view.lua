BOSSInvasionPrivilegeSkillShowView = BOSSInvasionPrivilegeSkillShowView or BaseClass(SafeBaseView)

function BOSSInvasionPrivilegeSkillShowView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_privilege_skill_show_view")
end

function BOSSInvasionPrivilegeSkillShowView:LoadCallBack()
    self.show_skill_index = 1

    XUI.AddClickEventListener(self.node_list["left_btn"], BindTool.Bind(self.ClickChangePageBtn,self, -1))
    XUI.AddClickEventListener(self.node_list["right_btn"], BindTool.Bind(self.ClickChangePageBtn,self, 1))
    XUI.AddClickEventListener(self.node_list["btn_page_1"], BindTool.Bind(self.ClickJumpToPageBtn,self, 1))
    XUI.AddClickEventListener(self.node_list["btn_page_2"], BindTool.Bind(self.ClickJumpToPageBtn,self, 2))
    XUI.AddClickEventListener(self.node_list["btn_page_3"], BindTool.Bind(self.ClickJumpToPageBtn,self, 3))
    XUI.AddClickEventListener(self.node_list["btn_page_4"], BindTool.Bind(self.ClickJumpToPageBtn,self, 4))
    XUI.AddClickEventListener(self.node_list["btn_page_5"], BindTool.Bind(self.ClickJumpToPageBtn,self, 5))
    XUI.AddClickEventListener(self.node_list["btn_page_6"], BindTool.Bind(self.ClickJumpToPageBtn,self, 6))
    XUI.AddClickEventListener(self.node_list["btn_page_7"], BindTool.Bind(self.ClickJumpToPageBtn,self, 7))
    XUI.AddClickEventListener(self.node_list["btn_page_8"], BindTool.Bind(self.ClickJumpToPageBtn,self, 8))
    XUI.AddClickEventListener(self.node_list["btn_page_9"], BindTool.Bind(self.ClickJumpToPageBtn,self, 9))
    XUI.AddClickEventListener(self.node_list["btn_page_10"], BindTool.Bind(self.ClickJumpToPageBtn,self, 10))
end

function BOSSInvasionPrivilegeSkillShowView:ReleaseCallBack()
    self.show_skill_index = nil
end

function BOSSInvasionPrivilegeSkillShowView:OnFlush()
    local skill_list = BOSSInvasionWGData.Instance:GetRuleImageCfg()
    local skill_page_num = #skill_list

    local skill_data = skill_list[self.show_skill_index]
    self.node_list.skill_name.text.text = skill_data.title

    self.node_list["left_btn"]:CustomSetActive(self.show_skill_index > 1)
    self.node_list["right_btn"]:CustomSetActive(self.show_skill_index < skill_page_num)

    local bundle, asset = ResPath.GetRawImagesPNG(skill_data.image)
    self.node_list["target_image"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["target_image"].raw_image:SetNativeSize()
    end)

    for i = 1, 10 do
        self.node_list["btn_page_" .. i]:CustomSetActive(i <= skill_page_num)
        self.node_list["btn_page_hl_" .. i]:CustomSetActive(i == self.show_skill_index)
    end

    local title_bundle, title_asset = ResPath.GetRawImagesPNG(skill_data.title)
    self.node_list["title_image"].raw_image:LoadSprite(title_bundle, title_asset, function()
        self.node_list["title_image"].raw_image:SetNativeSize()
    end)
end

function BOSSInvasionPrivilegeSkillShowView:ClickChangePageBtn(add_page_value)
    self.show_skill_index = self.show_skill_index + add_page_value
    self:Flush()
end

function BOSSInvasionPrivilegeSkillShowView:ClickJumpToPageBtn(page_index)
    self.show_skill_index = page_index
    self:Flush()
end