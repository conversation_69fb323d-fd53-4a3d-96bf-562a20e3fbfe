FightSoulExpPoolView = FightSoulExpPoolView or BaseClass(SafeBaseView)

local ATTR_NUM = 4
local Percent_0 = -318
local Percent_1 = -9
function FightSoulExpPoolView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_exp_pool")
end

function FightSoulExpPoolView:__delete()

end

function FightSoulExpPoolView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_upgrade, BindTool.Bind(self.OnClickUpgrade, self))
end

function FightSoulExpPoolView:ReleaseCallBack()
    if self.buy_remind_alert then
		self.buy_remind_alert:DeleteMe()
		self.buy_remind_alert = nil
	end
end

function FightSoulExpPoolView:ShowIndexCallBack()

end

function FightSoulExpPoolView:OnFlush(param_t, index)
	self:FlushView()
	for k, v in pairs(param_t) do
		if k == "play_effect" then
			self:PlayEffect()
		end
	end
end


-- 刷新界面
local sg_effect_list = {
	[288] = "UI_sihun_xianyu_1", [432] = "UI_sihun_xianyu",}
function FightSoulExpPoolView:FlushView()

	-- 经验池
	local exp = FightSoulWGData.Instance:GetExp()
	local max_exp = FightSoulWGData.Instance:GetMaxExp()
    local percent = exp / max_exp
    --self.node_list.pool_progress.image.fillAmount = percent
	percent = percent > 1 and 1 or percent
    local width = (Percent_1 - Percent_0) * percent
    --self.node_list.progress_bg.rect.anchoredPosition = Vector2(0, width + Percent_0)

	self.node_list.slider_progress.image.fillAmount = percent


	local exp_str = string.format("%s/%s", CommonDataManager.ConverNumber(exp), CommonDataManager.ConverNumber(max_exp))
	self.node_list.text_pool_val.text.text = exp_str

	local level = FightSoulWGData.Instance:GetExpPoolLevel()
	local is_max_level = FightSoulWGData.Instance:IsMaxExpPoolLevel()
    local no_max = not is_max_level
    local level_cfg, next_level_cfg
    level_cfg = FightSoulWGData.Instance:GetExpPoolCfg(level)
	if level_cfg == nil then
		return
	end

    if no_max then
        next_level_cfg = FightSoulWGData.Instance:GetExpPoolCfg(level + 1)
    end

	self.node_list.cur_level_txt.text.text = string.format("LV.%s", level)

	-- 标题
	self.node_list.title_img1.text.text = string.format(Language.Common.GoldGift, level_cfg.day_value)

	self.node_list.title_img2.text.text = level_cfg.recovery_day .. "天"

	-- 经验池上限
    if no_max then
        self.node_list.desc_1.text.text = string.format(Language.FightSoul.ExpToolDesc1, next_level_cfg.exp_pool_limit)
    else
        self.node_list.desc_1.text.text = string.format(Language.FightSoul.ExpToolDesc1, level_cfg.exp_pool_limit)
    end

    --[[ 活跃度
    self.node_list.desc_2.text.text = string.format(Language.FightSoul.ExpToolDesc2, 1, level_cfg.huoyuedu_to_exp)
    self.node_list.desc_2_arrow:SetActive(no_max)
    self.node_list.desc_2_next:SetActive(no_max)
    if no_max then
        self.node_list.desc_2_next.text.text = string.format(Language.FightSoul.ExpToolDesc0, next_level_cfg.huoyuedu_to_exp)
    end
	]]

    -- 打坐时间
    if no_max then
        self.node_list.desc_3.text.text = string.format(Language.FightSoul.ExpToolDesc3, next_level_cfg.dazuo_to_exp)
    else
        self.node_list.desc_3.text.text = string.format(Language.FightSoul.ExpToolDesc3, level_cfg.dazuo_to_exp)
    end
    self.node_list.desc_4.text.text = string.format(Language.FightSoul.ExpToolDesc4, level_cfg.dazuo_to_exp)

    self.node_list.btn_content:SetActive(no_max)
    self.node_list.is_upgrade:SetActive(is_max_level)
    if no_max then
        -- 升级消耗
        self.node_list.btn_upgrade_text.text.text = string.format(Language.FightSoul.ExpPoolUpgrade, next_level_cfg.need_gold)
    end

    -- 属性
    local attr_level = is_max_level and level or level + 1
    local attr_lsit = FightSoulWGData.Instance:GetExpPoolShowAttr(attr_level)
    local attr, attr_name, attr_value, attr_data, is_per, per_desc, name_str, value_str
    for i = 1, ATTR_NUM do
        attr = string.format("attr_%s", i)
        attr_name = string.format("txt_attr_name_%s", i)
        attr_value = string.format("txt_attr_value_%s", i)
        attr_data = attr_lsit[i]
        if attr_data then
            is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(attr_data.attr_str)
            per_desc = is_per and "%" or ""
            value_str = is_per and attr_data.attr_value / 100 or attr_data.attr_value
            name_str = Language.Common.TipsAttrNameList[attr_data.attr_str] or ""
            self.node_list[attr_name].text.text = string.format("%s", name_str)
            self.node_list[attr_value].text.text = value_str
            self.node_list[attr]:SetActive(true)
        else
            self.node_list[attr]:SetActive(false)
        end
    end
end

-- 点击升级
function FightSoulExpPoolView:OnClickUpgrade()
	local is_max_level = FightSoulWGData.Instance:IsMaxExpPoolLevel()
	if is_max_level then
		return
	end

    if nil == self.buy_remind_alert then
        self.buy_remind_alert = Alert.New(nil, nil, nil, nil, true)
        self.buy_remind_alert:SetCheckBoxDefaultSelect(false)
    end

    local level = FightSoulWGData.Instance:GetExpPoolLevel()
    local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
    local next_level_cfg = FightSoulWGData.Instance:GetExpPoolCfg(level + 1)
    local cost_value = next_level_cfg and next_level_cfg.need_gold or 0

    self.buy_remind_alert:SetLableString(string.format(Language.FightSoul.UpPoolTips, cost_value))
    self.buy_remind_alert:SetOkFunc(function()
        if role_gold < cost_value then
            VipWGCtrl.Instance:OpenTipNoGold()
        else
			if self.node_list.title_img2_effect then
				self.node_list.title_img2_effect:SetActive(false)
			end
            FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.EXP_POOL_UPLEVEL)
        end
    end)

    self.buy_remind_alert:Open()
end

-- 特效
function FightSoulExpPoolView:PlayEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true,
		pos = Vector2(0, 0), parent_node = self.node_list.effect_node})
end
