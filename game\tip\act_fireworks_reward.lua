ActYanHuaRewardView = ActYanHuaRewardView or BaseClass(SafeBaseView)

local VECTOR3_THREE = Vector3(3, 3, 3)
local VECTOR3_ONE = Vector3(1, 1, 1)

function ActYanHuaRewardView:__init()
	self:AddViewResource(0, "uis/view/fireworks_prefab", "layout_firework_reward")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function ActYanHuaRewardView:__delete()
	
end

function ActYanHuaRewardView:ReleaseCallBack()
	self.load_callback = nil
	self.need_flush = nil

	if self.item_cell then
		for k,v in pairs(self.item_cell) do
			v:DeleteMe()
		end
		self.item_cell = nil
	end

	if self.one_cell then
		self.one_cell:DeleteMe()
		self.one_cell = nil
	end

	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

function ActYanHuaRewardView:LoadCallBack()
	self.item_cell = {}

	self.node_list["btn_close_1"].button:AddClickListener(BindTool.Bind(self.OnClickClose,self))
	self.node_list["btn_again"].button:AddClickListener(BindTool.Bind(self.OnClickAgain,self))

	for i=1,10 do
		self.item_cell[i] = ItemCell.New(self.node_list["ten_pos_" .. i])
		self.item_cell[i]:SetActive(false)
	end

	self.load_callback = true
	if self.need_flush then
		self:Flush()
	end
end

function ActYanHuaRewardView:SetData(data)
	self.data = ServerActivityWGData.Instance:GetYanhuaReward(data)
	self:Flush()
end

function ActYanHuaRewardView:OnFlush()
	if not self.load_callback then
		self.need_flush = true
		return
	end

	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end

	for i=1,10 do
		self.item_cell[i]:SetActive(false)
	end

	self.node_list["layout_xunbao_one"]:SetActive(false)

	if #self.data == 1 then
		self.node_list["layout_xunbao_one"]:SetActive(true)
		if not self.one_cell then
			self.one_cell = ItemCell.New(self.node_list["img_one_fuwen"])
		end
		self.one_cell:SetData(self.data[1])
		self.one_cell.view.transform.localScale = VECTOR3_THREE
		self.one_cell.view.transform:DOScale(VECTOR3_ONE,0.2)
	else
		local count = 1
		self.timer_quest = GlobalTimerQuest:AddTimesTimer(function()
			if self.data[count] then
				local cell = self.item_cell[count]
				cell:SetData(self.data[count])
				cell:SetActive(true)
				cell.view.transform.localScale = VECTOR3_THREE
				cell.view.transform:DOScale(VECTOR3_ONE,0.2)
			end
			count = count + 1
		end,0.2,#self.data)
	end
	-- for i=1,10 do
	-- 	if self.node_list["ten_pos_" .. i] and self.data[i] then
	-- 		self.item_cell[i]:SetData(self.data[i])
	-- 	end
	-- end
end

function ActYanHuaRewardView:OnClickClose()
	self:Close()
end

function ActYanHuaRewardView:OnClickAgain()
	GlobalEventSystem:Fire(YanhuaEventType.AGAIN)
end
