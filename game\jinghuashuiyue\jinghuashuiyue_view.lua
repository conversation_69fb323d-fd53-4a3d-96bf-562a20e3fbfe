Jing<PERSON><PERSON>ShuiYueView = Jing<PERSON>uaShuiYueView or BaseClass(SafeBaseView)

local MAX_BIG_TOGGLE_NUM = 8
local ITEM_POS = {
    [6] = {
        [1] = { x = -282, y = -46 }, [2] = { x = -267, y = -210 }, [3] = { x = -79, y = -271 }, [4] = { x = 101, y = -271 },
        [5] = { x = 288, y = -210 }, [6] = { x = 307, y = -46 }
    },
    [7] = {
        [1] = { x = -282, y = -6 }, [2] = { x = -267, y = -170 }, [3] = { x = -129, y = -231 }, [4] = { x = 16, y = -281 },
        [5] = { x = 161, y = -231 }, [6] = { x = 288, y = -170 }, [7] = { x = 307, y = -6 }
    },
    [8] = {
        [1] = { x = -255, y = 119 }, [2] = { x = -282, y = -46 }, [3] = { x = -267, y = -210 }, [4] = { x = -79, y = -271 },
        [5] = { x = 101, y = -271 }, [6] = { x = 288, y = -210 }, [7] = { x = 307, y = -46 }, [8] = { x = 284, y = 119 }
    },
    [9] = {
        [1] = { x = -345, y = 32 }, [2] = { x = -333, y = -100 }, [3] = { x = -258, y = -224 }, [4] = { x = -133, y = -270 },
        [5] = { x = -4, y = -293 }, [6] = { x = 123, y = -270 }, [7] = { x = 248, y = -224 }, [8] = { x = 327, y = -99 },
        [9] = { x = 358, y = 32 }
    },
}

function JingHuaShuiYueView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg()

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_background_common_panel")
    self:AddViewResource(0, "uis/view/jinghuashuiyue_ui_prefab", "layout_jinghuashuiyue_view")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")
    self.background_loader = nil
end

function JingHuaShuiYueView:LoadCallBack()
    self.select_big_type = -1
    self.select_small_type = -1

    self.node_list.title_view_name.text.text = Language.JingHuaShuiYue.TitleText

    if not self.show_item_list then
        self.show_item_list = {}
        local item_list = self.node_list.item_list
        for i = 1, 9 do
            self.show_item_list[i] = JingHuaShuiYueItem.New(item_list:FindObj("show_item" .. i))
        end
    end

    if not self.attr_list then
        self.attr_list = {}
        local attr_list_content = self.node_list.attr_list_content
        for i = 1, 8 do
            local attr_render = CommonAddAttrRender.New(attr_list_content:FindObj("attr" .. i))
            self.attr_list[i] = attr_render
        end
    end

    if not self.left_tab_list then
        self.left_tab_list = AsyncListView.New(JingHuaShuiYueLeftRender, self.node_list["left_tab_list"])
        self.left_tab_list:SetSelectCallBack(BindTool.Bind(self.OnClickSmallTypeToggle, self))
        self.left_tab_list:SetStartZeroIndex(true)
    end

    if nil == self.role_model then
        self.role_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ph_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.role_model:SetRenderTexUI3DModel(display_data)
        -- self.role_model:SetUI3DModel(self.node_list["ph_display"].transform,
        --     self.node_list["EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.role_model)
    end

    self.node_list.btn_rule_tips:SetActive(true)
    XUI.AddClickEventListener(self.node_list.active_btn, BindTool.Bind(self.OnClickActiveOrUpStarBtn, self, true))
    XUI.AddClickEventListener(self.node_list.up_star_btn, BindTool.Bind(self.OnClickActiveOrUpStarBtn, self, false))
    XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickRuleBtn, self, false))
end

function JingHuaShuiYueView:ReleaseCallBack()
    if self.cell_list then
        for k, v in pairs(self.cell_list) do
            for _, cell in pairs(v) do
                cell:DeleteMe()
            end
        end

        self.cell_list = nil
    end

    if self.show_item_list then
        for k, v in pairs(self.show_item_list) do
            v:DeleteMe()
        end

        self.show_item_list = nil
    end

    if self.attr_list then
        for k, v in pairs(self.attr_list) do
            v:DeleteMe()
        end

        self.attr_list = nil
    end

    if self.role_model then
        self.role_model:DeleteMe()
        self.role_model = nil
    end

    if self.left_tab_list then
        self.left_tab_list:DeleteMe()
        self.left_tab_list = nil
    end

    if self.pet_model then
        self.pet_model:DeleteMe()
        self.pet_model = nil
    end

    self.background_loader = nil
end

function JingHuaShuiYueView:OnFlush(param_t)
    for k, v in pairs(param_t) do
        if k == "all" then
            self:FlushBg()
            self:FlushLeftPanel()
            self:FlushMidPanel()
            self:FlushRightPanel()
        elseif k == "level_up" then
            self:FlushLeftPanel()
            self:FlushMidPanel(true)
            self:FlushRightPanel()
        elseif k == "data_update" then
            self:FlushLeftPanel()
            self:FlushMidPanel(true)
            self:FlushRightPanelTopDes()
            self:FlushBtnsState()
        end
    end
end

function JingHuaShuiYueView:FlushBg()
    local small_type_cfg = JingHuaShuiYueWGData.Instance:GetSmallTypeToggleCfg(self.select_small_type)
    if IsEmptyTable(small_type_cfg) then
        return
    end

    local bundle, asset = ResPath.GetF2RawImagesPNG(small_type_cfg.background)
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset)

    local effect_bundle, effect_asset = ResPath.GetA2Effect(small_type_cfg.effect)
    self.node_list.effect:CustomSetActive(true)
    self.node_list.effect:ChangeAsset(effect_bundle, effect_asset)
end

--刷新左边列表全部红点
function JingHuaShuiYueView:FlushLeftPanel()
    local dataList = JingHuaShuiYueWGData.Instance:GetSmallTypeShowCfg()
    if not IsEmptyTable(dataList) then
        self.left_tab_list:SetDataList(dataList)
    end
end

--刷新左边单个列表和列表内的红点
function JingHuaShuiYueView:FlushLeftListRemind(index)
    if not index or index < 0 or not self.accordion_list[index] then
        return
    end

    self.accordion_list[index]:ShowBigTypeRemind()

    local cell_list = self.cell_list[index]
    if not cell_list then
        return
    end

    for k, v in pairs(cell_list) do
        v:ShowSmallTypeRemind()
    end
end

function JingHuaShuiYueView:FlushMidPanel(is_update)
    local item_data_list = JingHuaShuiYueWGData.Instance:GetShowItemList(self.select_small_type)
    if IsEmptyTable(item_data_list) then
        return
    end

    --刷新数据
    for k, v in pairs(self.show_item_list) do
        v:SetData(item_data_list[k])
    end

    --外部更新数据，只刷新显示，没必要调整位置
    if is_update then
        return
    end

    --刷新位置
    local small_type_num = #item_data_list
    local pos_info = ITEM_POS[small_type_num]
    if pos_info then
        for i = 1, small_type_num do
            local pos_x = pos_info[i].x or 0
            local pos_y = pos_info[i].y or 0
            RectTransform.SetAnchoredPositionXY(self.show_item_list[i]:GetView().rect, pos_x, pos_y)
        end
    end
end

function JingHuaShuiYueView:FlushRightPanel()
    local jhsy_cfg = JingHuaShuiYueWGData.Instance:GetSmallTypeToggleCfg(self.select_small_type)
    if IsEmptyTable(jhsy_cfg) then
        return
    end

    self.node_list.title_txt.text.text = jhsy_cfg.small_type_name
    self.node_list.des_txt.text.text = jhsy_cfg.des

    local cap_value = JingHuaShuiYueWGData.Instance:GetSuitCapability(self.select_small_type)
    self.node_list.cap_value.text.text = cap_value

    self:FlushRightPanelTopDes()
    self:FlushAttrList()
    self:FlushBtnsState()
end

function JingHuaShuiYueView:FlushRightPanelTopDes()
    local is_max_progress = JingHuaShuiYueWGData.Instance:IsMaxActiveProgress(self.select_small_type)
    local is_max_star_level = JingHuaShuiYueWGData.Instance:IsMaxStarLevel(self.select_small_type)
    local cur_progress = 0
    local next_progress = 0
    local target_num = 0
    if is_max_progress then
        target_num = JingHuaShuiYueWGData.Instance:GetSuitStarLevel(self.select_small_type) + 1
        cur_progress, next_progress = JingHuaShuiYueWGData.Instance:GetSuitStarEnoughNum(self.select_small_type)
    else
        cur_progress = JingHuaShuiYueWGData.Instance:GetSuitActiveNum(self.select_small_type)
        next_progress = JingHuaShuiYueWGData.Instance:GetNextSuitActiveNum(self.select_small_type)
        target_num = next_progress
    end

    local no_max_str = is_max_progress and Language.JingHuaShuiYue.UpStarText or Language.JingHuaShuiYue.ActiveText
    local can_active = JingHuaShuiYueWGData.Instance:GetSingleSuitRemind(self.select_small_type)
    local color = can_active and COLOR3B.DEFAULT_NUM or COLOR3B.RED
    local attr_top_txt = is_max_star_level and Language.JingHuaShuiYue.MaxStarLevel or
    string.format(no_max_str, target_num, color, cur_progress, next_progress)
    self.node_list.attr_top_txt.text.text = attr_top_txt
end

function JingHuaShuiYueView:FlushAttrList()
    local data_list = JingHuaShuiYueWGData.Instance:GetShowAttrListBySuitId(self.select_small_type)
    if IsEmptyTable(data_list) then
        return
    end

    for k, v in pairs(self.attr_list) do
        v:SetData(data_list[k])
    end

    self.node_list.attr_scroll_view.scroll_rect.verticalNormalizedPosition = 1
end

function JingHuaShuiYueView:FlushBtnsState()
    if self.select_small_type < 0 then
        return
    end

    local is_max_progress = JingHuaShuiYueWGData.Instance:IsMaxActiveProgress(self.select_small_type)
    local can_active, remind_type = JingHuaShuiYueWGData.Instance:GetSingleSuitRemind(self.select_small_type)

    self.node_list.active_btn:SetActive(not is_max_progress)
    self.node_list.up_star_btn:SetActive(is_max_progress)
    self.node_list.active_btn_remind:SetActive(remind_type == JHSY_REMIND_TYPE.Active)
    self.node_list.up_star_btn_remind:SetActive(remind_type == JHSY_REMIND_TYPE.UpStar)
end

function JingHuaShuiYueView:OnClickSmallTypeToggle(item)
    local data = item:GetData()
    if IsEmptyTable(data) or self.select_small_type == data.small_type then
        return
    end

    self.select_small_type = data.small_type

    self:FlushMidPanel()
    self:FlushRightPanel()

    if data.is_show_model == 1 then
        self:FlushSuitModel(data)
    else
        if self.role_model then
            self.role_model:RemoveAllModel()
        end

        if self.pet_model then
            self.pet_model:RemoveAllModel()
        end
    end

    if data.is_show_skycurtain == 1 then
        self.node_list.effect:CustomSetActive(false)
        self:FlushTianmu()
    else
        self:FlushBg()
    end
    self.node_list.background_root:SetActive(data.is_show_skycurtain == 1)
end

function JingHuaShuiYueView:OnClickActiveOrUpStarBtn(is_active)
    if self.select_small_type < 0 then
        return
    end

    local is_max = JingHuaShuiYueWGData.Instance:IsMaxStarLevel(self.select_small_type)
    if is_max then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.JingHuaShuiYue.MaxStarLevel)
        return
    end

    local can_active = JingHuaShuiYueWGData.Instance:GetSingleSuitRemind(self.select_small_type)
    if not can_active then
        local error_str = is_active and Language.JingHuaShuiYue.ActiveErrorStr or Language.JingHuaShuiYue.UpStarErrorStr
        SysMsgWGCtrl.Instance:ErrorRemind(error_str)
        return
    end

    local operate_type = is_active and JINGHUASHUIYUE_OPERA.ACTIVE or JINGHUASHUIYUE_OPERA.UPSTAR
    JingHuaShuiYueWGCtrl.Instance:SendJingHuaShuiYueOperateReq(operate_type, self.select_small_type)
end

function JingHuaShuiYueView:OnClickRuleBtn()
    RuleTip.Instance:SetContent(Language.JingHuaShuiYue.RuleContent, Language.JingHuaShuiYue.RuleTitle)
end

function JingHuaShuiYueView:GetBigTypeBySmallType(suit_id)
    local small_type_data = JingHuaShuiYueWGData.Instance:GetSmallTypeData(suit_id)
    if not IsEmptyTable(small_type_data) then
        return small_type_data.from_big_type
    end

    return 1
end

function JingHuaShuiYueView:FlushAllListSelectState(select_data)
    for k, v in pairs(self.cell_list) do
        for k1, v1 in pairs(v) do
            v1:OnSelectChange(false)
        end
    end

    self:OnClickSmallTypeToggle(select_data)
end

---------------------------------优化 新增中间模型 配置的道具里有坐骑与时装就显示中间模型 有天幕显示天幕-----------------------------------

function JingHuaShuiYueView:FlushTianmu()
    local show_list = JingHuaShuiYueWGData.Instance:GetActiveCfgBySuitId(self.select_small_type)
    if IsEmptyTable(show_list) then
        return
    end

    for k, data in pairs(show_list) do
        if data.sys_id == JHSY_SYSTEM_TYPE.Tianmu then -- 天幕
            local asset, bundle = ResPath.BackgroundShow(data.item_id)

            if not self.background_loader then
                local background_loader = AllocSyncLoader(self, "base_tip_back_cell")
                background_loader:SetIsUseObjPool(true)
                background_loader:SetParent(self.node_list["background_root"].transform)
                self.background_loader = background_loader
            end
            self.background_loader:Load(asset, bundle)
        end
    end
end

function JingHuaShuiYueView:FlushSuitModel(trans_data)
    local show_list = JingHuaShuiYueWGData.Instance:GetActiveCfgBySuitId(self.select_small_type)
    if IsEmptyTable(show_list) then
        return
    end
    --清理掉回调
    self.role_model:RemoveAllModel()

    local body_res_id = AppearanceWGData.Instance:GetRoleResId()
    local mount_res_id = 0
    local mount_action = ""

    local fashion_cfg
    for k, data in pairs(show_list) do
        if data.sys_id == JHSY_SYSTEM_TYPE.Fashion and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
            fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
            if fashion_cfg then                                                          -- 时装
                local prof = GameVoManager.Instance:GetMainRoleVo().prof
                body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
            end
        elseif data.sys_id == JHSY_SYSTEM_TYPE.Mount then -- 坐骑
            fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(MOUNT_LINGCHONG_APPE_TYPE.MOUNT,
                data.param1)
            if fashion_cfg then
                mount_res_id = fashion_cfg.appe_image_id
                mount_action = MOUNT_RIDING_TYPE[1]
                local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(mount_res_id)
                if not IsEmptyTable(action_cfg) then
                    mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
                end
            end
        elseif data.sys_id == JHSY_SYSTEM_TYPE.Pet then -- 宠物
            fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
            if fashion_cfg then
                self:SetPetModelData(fashion_cfg.appe_image_id)
            end
        end
    end

    local extra_role_model_data = {
		no_need_do_anim = true,
    }

    if mount_res_id > 0 then
        self.role_model:SetRoleResid(body_res_id, nil, extra_role_model_data)
    else
        self.role_model:SetRoleResid(body_res_id, function()
            if mount_res_id == 0 then
                self.role_model:PlayIdleAni()
            end
        end, extra_role_model_data)
    end

    if mount_res_id > 0 then
        self.role_model:SetMountResid(mount_res_id)
        self.role_model:PlayStartAction(mount_action)
    end

    for k, v in pairs(show_list) do
        self:ShowModelByData(v)
    end

    self:SetModelTrans(trans_data)
end

function JingHuaShuiYueView:SetPetModelData(res_id)
    if nil == self.pet_model then
        self.pet_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["pet_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.pet_model:SetRenderTexUI3DModel(display_data)
        -- self.pet_model:SetUI3DModel(self.node_list["pet_display"].transform,
        --     nil, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.pet_model)
    else
        if self.pet_model then
            self.pet_model:ClearModel()
        end
    end

    local bundle, asset = ResPath.GetPetModel(res_id)

    self.pet_model:SetMainAsset(bundle, asset, function()
        self.pet_model:PlaySoulAction()
    end)

    self.pet_model:FixToOrthographic(self.root_node_transform)
end

function JingHuaShuiYueView:SetModelTrans(trans_data)
    if IsEmptyTable(trans_data) then
        return
    end

    ----设置人物模型transform
    local pos_x, pos_y = 0, 0
    if trans_data.display_position and trans_data.display_position ~= "" then
        local pos_list = string.split(trans_data.display_position, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.ph_display.rect, pos_x, pos_y)

    if trans_data.display_rotation and trans_data.display_rotation ~= "" then
        local rotation_tab = string.split(trans_data.display_rotation, "|")
        self.node_list["ph_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
            rotation_tab[3])
    end

    if trans_data.display_scale then
        RectTransform.SetLocalScale(self.node_list["ph_display"].transform, trans_data.display_scale)
    end

    --设置宠物模型transform
    pos_x, pos_y = 0, 0
    if trans_data.pet_pos and trans_data.pet_pos ~= "" then
        local pos_list = string.split(trans_data.pet_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.pet_display.rect, pos_x, pos_y)

    if trans_data.pet_rot and trans_data.pet_rot ~= "" then
        local rotation_tab = string.split(trans_data.pet_rot, "|")
        self.node_list["pet_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
            rotation_tab[3])
    end

    if trans_data.lc_scale then
        RectTransform.SetLocalScale(self.node_list["pet_display"].transform, trans_data.lc_scale)
    end
end

function JingHuaShuiYueView:ShowModelByData(data)
    if IsEmptyTable(data) then
        return
    end
    local res_id, fashion_cfg
    if data.sys_id == JHSY_SYSTEM_TYPE.Fashion then -- 时装大类
        fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
        if fashion_cfg then
            res_id = fashion_cfg.resouce

            if data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
                self.role_model:SetWingResid(res_id, true)
            elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
                self.role_model:SetBaoJuResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
                self.role_model:SetJianZhenResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
                self.role_model:SetHaloResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
                res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
                self.role_model:SetWeaponResid(res_id)
            end
        end
    end
end

------------------------------------------------------------------------------------------------------
-------------------------JingHuaShuiYueLeftRender左边列表render---------------------
JingHuaShuiYueLeftRender = JingHuaShuiYueLeftRender or BaseClass(BaseRender)

function JingHuaShuiYueLeftRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.name.text.text = self.data.small_type_name
    self.node_list["icon"].image:LoadSprite(ResPath.GetJHSYImg("a2_jhsy_tx" .. (self.data.small_type + 1)))

    self:ShowRemind()
end

function JingHuaShuiYueLeftRender:OnSelectChange(is_select)
    self.node_list.icon_hl:CustomSetActive(is_select)
end

function JingHuaShuiYueLeftRender:ShowRemind()
    if not self.data then
        return
    end

    local remind = JingHuaShuiYueWGData.Instance:GetSingleSuitRemind(self.data.small_type)
    self.node_list.remind:SetActive(remind)
end

------------------JingHuaShuiYueItem中间面板显示item
JingHuaShuiYueItem = JingHuaShuiYueItem or BaseClass(BaseRender)
function JingHuaShuiYueItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
end

function JingHuaShuiYueItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function JingHuaShuiYueItem:OnFlush()
    local is_empty_data = IsEmptyTable(self.data)
    self.view:SetActive(not is_empty_data)
    if is_empty_data then
        return
    end

    self.node_list.star_bg:SetActive(self.data.is_act)
    self.item_cell:SetData({ item_id = self.data.item_id })
    self.item_cell:MakeGray(not self.data.is_act)

    if self.data.is_act then
        local star_res_list = GetStarImgResByStar(self.data.star_level)
        for i = 1, 5 do
            local bundle, asset = ResPath.GetCommon(star_res_list[i])
            self.node_list["star" .. i].image:LoadSprite(bundle, asset)
        end
    end
end
