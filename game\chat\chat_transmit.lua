local ChatTransmitPopViewIndex = {
	LocalServer = 1,  --本服喇叭
	AcrossServer = 2, --跨服喇叭
}

ChatTransmitPopView = ChatTransmitPopView or BaseClass(SafeBaseView)

local old_param = ""
function ChatTransmitPopView:__init()
	self:SetMaskBg(true)
	self.cur_win_type = -1 		--当前窗口类型
	self.local_gold = 10
	self.cross_gold = 30
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -28), sizeDelta = Vector2(782, 476)})
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_transmit_pop")
	--默认选择喇叭类型
	self.cur_index = 1
end

function ChatTransmitPopView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.QianLiChuanYinTitle																	
	
	self:RegisterTransmitEvent()
	self:ShowHornMsg()
	-- EmojiTextUtil.ParseRichText(self.rich_transmit, des, 20, nil)

	self.pop_alert = Alert.New()
	self.cross_alert = Alert.New()
	ChatTransmitPopView.residue_num = self.node_list["residue_num"]
	ChatTransmitPopView.input_field = self.node_list["input_field"]
	ChatTransmitPopView.input_field.input_field.onValueChanged:AddListener(BindTool.Bind1(self.OnInputFieldValueChange))

	--默认选择喇叭类型
	self.cur_index = 1
end

function ChatTransmitPopView:OnInputFieldValueChange(param)
	local num = string.len(param)
	if num > 120 then
		ChatTransmitPopView.input_field.input_field.text = old_param
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end
	old_param = param
end

function ChatTransmitPopView:ReleaseCallBack()
	if nil ~= self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
	if nil ~= self.cross_alert then
		self.cross_alert:DeleteMe()
		self.cross_alert = nil
	end
	self:ClearCdTimer()

	ChatTransmitPopView.residue_num = nil
	ChatTransmitPopView.input_field = nil
end

--下面提示文字
function ChatTransmitPopView:ShowHornMsg()
	local des = ""

	if ChatTransmitPopViewIndex.LocalServer == self.cur_index then
		self.node_list["rich_1"].text.text = Language.Transmit.Small1
		self.node_list["rich_2"].text.text = Language.Transmit.Small2
		self.node_list["rich_3"].text.text = Language.Transmit.Small3
	elseif ChatTransmitPopViewIndex.AcrossServer == self.cur_index then
		self.node_list["rich_1"].text.text = Language.Transmit.Big1
		self.node_list["rich_2"].text.text = Language.Transmit.Big2
		self.node_list["rich_3"].text.text = Language.Transmit.Big3
	end
end

function ChatTransmitPopView:RegisterTransmitEvent()
	--self.node_list["btn_close"].button:AddClickListener(BindTool.Bind1(self.CloseTransmitPop, self))
	self.node_list["layout_chat_pop_face"].button:AddClickListener(BindTool.Bind1(ChatWGCtrl.Instance.OpenFace, ChatWGCtrl.Instance))
	self.node_list["btn_send"].button:AddClickListener(BindTool.Bind1(self.SendTransmitMsg, self))
	self.node_list["cur_server"].toggle:AddValueChangedListener(BindTool.Bind2(self.OnClickChange, self, 1))
	self.node_list["corss_server"].toggle:AddValueChangedListener(BindTool.Bind2(self.OnClickChange, self, 2))
end

function ChatTransmitPopView:OnClickChange(index, is_on)
	-- if index == 2 then
	-- 	if not IS_ON_CROSSSERVER then
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Transmit.NoOnCross)
	-- 	end
	-- 	return
	-- end
	if is_on and self.cur_index ~= index then
		self.cur_index = index
		self:Flush()
	end
end

function ChatTransmitPopView:AddEditText(text)
	self.node_list["input_field"].input_field.text = self.node_list["input_field"].input_field.text .. text
end

function ChatTransmitPopView:GetHornInput()
	return self.node_list["input_field"] or nil 
end

function ChatTransmitPopView:OpenTransmitPop(type)
	--self.cur_win_type = type
	self:Open()
end

function ChatTransmitPopView:ShowIndexCallBack(index)
	self.transmit_is_popup = true
	-- self.tabbar:SetToggleVisible(2, self:GetIsPingBiKuaFuBool())
	self:Flush()
end

--随机活动超级vip是否显示
function ChatTransmitPopView:GetIsPingBiKuaFuBool()
	local cfg = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto").agent_adapt
	if nil ~= cfg then
		local plat_id = AgentAdapter:GetSpid()
		for k,v in pairs(cfg) do
			if v.spid == plat_id and v.is_pingbi == 1 then
				return false
			end
		end
	end
	return true
end

function ChatTransmitPopView:CloseCallBack()
	self.transmit_is_popup = false
	ChatWGCtrl.Instance:CloseFace()
	self:ClearInput()
end

function ChatTransmitPopView:CloseTransmitPop()
	self:Close()
end

function ChatTransmitPopView:GetEditText()
	if nil ~= self.node_list["input_field"] then
		return self.node_list["input_field"].input_field.text
	end
	return nil
end

function ChatTransmitPopView:ClearInput()
	if nil ~= self.node_list["input_field"] then
		self.node_list["input_field"].input_field.text = ""
	end
	ChatWGData.Instance:ClearInput()
end

function ChatTransmitPopView:SendTransmitMsg()
	if nil == self.node_list["input_field"] then
		return
	end

	local tmp_time = self:GetCdTime()
	if tmp_time > 0 and self.cd_timer then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end


	local textc = self.node_list["input_field"].input_field.text
	local len = string.len(textc)

	if len <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end

	--超出文字
	if len > 122 then --COMMON_CONSTS.MAX_CHAT_MSG_LEN
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end

	if ChatWGData.ExamineEditText(textc, 0) == false then 
		return 
	end

	local message = ChatWGData.Instance:FormattingMsg(textc, 0)
	if 2 == self.cur_index then
		local item_index = ItemWGData.Instance:GetItemIndex(26150)
		if -1 ~= item_index then
			ChatWGCtrl.Instance:SendCurrentTransmit(0, message, nil, SPEAKER_TYPE.SPEAKER_TYPE_CROSS)--CHANNEL_TYPE.CROSS

			self:CloseTransmitPop()
			self:ClearInput()
			self:SetCdTime()
		else
			self.cross_alert:SetOkFunc(BindTool.Bind2(self.SendCrossTransmitUseGold, self, message))
			local des = string.format(Language.Chat.SendSrossByGold, self.cross_gold)
			self.cross_alert:SetLableString(des)
			self.cross_alert:SetShowCheckBox(true)
			self.cross_alert:Open()
		end
	else
		local item_index = ItemWGData.Instance:GetItemIndex(26151)

		if -1 ~= item_index then
			ChatWGCtrl.Instance:SendCurrentTransmit(0, message, nil, SPEAKER_TYPE.SPEAKER_TYPE_LOCAL)

			self:CloseTransmitPop()
			self:ClearInput()
			self:SetCdTime()
		else
			self.pop_alert:SetOkFunc(BindTool.Bind2(self.SendTransmitUseGold, self, message))
			local des = string.format(Language.Chat.SendByGold, self.local_gold)
			self.pop_alert:SetLableString(des)
			self.pop_alert:SetShowCheckBox(true)
			self.pop_alert:Open()
		end
	end
end

function ChatTransmitPopView:SendTransmitUseGold(message)
	local tmp_time = self:GetCdTime()
	if tmp_time > 0 and self.cd_timer then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end

	if not RoleWGData.Instance:GetIsEnoughUseGold(self.local_gold) then
		UiInstanceMgr.Instance:ShowChongZhiView()
		return
	end

	if ChatWGData.Instance:GetIsInSilent() then --静默状态
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.IsInSlient)
		return
	end

	ChatWGCtrl.Instance:SendCurrentTransmit(1, message, nil, SPEAKER_TYPE.SPEAKER_TYPE_LOCAL)
	self:CloseTransmitPop()
	self:ClearInput()
	self:SetCdTime()
end

function ChatTransmitPopView:SendCrossTransmitUseGold(message)
	local tmp_time = self:GetCdTime()
	if tmp_time > 0 and self.cd_timer then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.CanNotChat, math.ceil(tmp_time)))
		return
	end

	if not RoleWGData.Instance:GetIsEnoughUseGold(self.cross_gold) then
		UiInstanceMgr.Instance:ShowChongZhiView()
		return
	end
	ChatWGCtrl.Instance:SendCurrentTransmit(1, message, nil, SPEAKER_TYPE.SPEAKER_TYPE_CROSS)
	self:CloseTransmitPop()
	self:ClearInput()
	self:SetCdTime()
end

function ChatTransmitPopView:IsTransmitOpen()
	return self.transmit_is_popup
end

function ChatTransmitPopView:OnFlush(param_list, index)
	self:ShowHornMsg()
end

-- 喇叭CD
function ChatTransmitPopView:GetCdTime()
	local end_time, is_clear_time = ChatWGData.Instance:GetLaBaCdEndTime()
	if (end_time - Status.NowTime) > 0 then
		return math.ceil(end_time - Status.NowTime)
	else
		return math.floor(end_time - Status.NowTime)
	end
end

function ChatTransmitPopView:SetCdTime()
	ChatWGData.Instance:SetLaBaCdEndTime()
	local end_time, is_clear_time = ChatWGData.Instance:GetLaBaCdEndTime()
	if end_time <= Status.NowTime then
		return
	end
	self:ShowBtnCd()
end

function ChatTransmitPopView:ShowBtnCd()
	if nil == self.node_list["left_time"] then
		return
	end

	self:ClearCdTimer()
	local cd_time = self:GetCdTime()
	self:UpdateBtnCd()

	if cd_time > 0 then
		self.cd_timer = GlobalTimerQuest:AddTimesTimer(BindTool.Bind1(self.UpdateBtnCd, self), 1, cd_time)
	end
end

function ChatTransmitPopView:ClearCdTimer()
	if nil ~= self.cd_timer then
		GlobalTimerQuest:CancelQuest(self.cd_timer)
		self.cd_timer = nil
	end
end

function ChatTransmitPopView:UpdateBtnCd()
	local cd_time = self:GetCdTime()
	if cd_time < 0 then
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Send
		end
		self:ClearCdTimer()
	else
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Wait .. "(" .. cd_time .. ")"
		end
	end
end

function ChatTransmitPopView:ClearLaBaCd() --未合相关代码功能
	local end_time, is_clear_time = ChatWGData.Instance:GetLaBaCdEndTime()
	if is_clear_time then
		self:ClearCdTimer()
		if nil ~= self.node_list["left_time"] then
			self.node_list["left_time"].text.text = Language.Chat.Send
		end
	end
end