--蛮荒古殿
FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

function FuBenPanelView:InitManHuangGuDian()
    self.manhuang_rank_list = AsyncListView.New(ManHuangRankItemRender, self.node_list["manhaung_rank_list"])
    self.manhuang_reward_list = AsyncListView.New(ItemCell, self.node_list["manhuang_reward_list"])
    self.node_list["manhuang_reward_list"].scroll_rect.enabled = false
    XUI.AddClickEventListener(self.node_list.btn_manhuang_baoming_enter, BindTool.Bind(self.OnClickManHuangBaoMingEnter, self))
    XUI.AddClickEventListener(self.node_list.btn_manhuang_pingtai, BindTool.Bind(self.OnClickManHuangPingTai, self))
    XUI.AddClickEventListener(self.node_list.btn_manhuang_fb_rule, BindTool.Bind(self.OnClickManHuangRule, self))
    XUI.AddClickEventListener(self.node_list.btn_manhuang_add_count, BindTool.Bind(self.OnClickManHuangAddCount, self))
    XUI.AddClickEventListener(self.node_list.btn_manhuang_first_pass_fetch, BindTool.Bind(self.OnClickManHuangFirstPass, self)) --点击宝箱打开 首通奖励界面
    self.node_list["layout_manhuang_combine_mark"].button:AddClickListener(BindTool.Bind(self.OnClickManHuangCombine, self))

    self.manhaung_day_count_change_event = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.OnManHuangDayCounterChange, self))
end

function FuBenPanelView:DeleteManHuangGuDian()
    if self.manhuang_rank_list then
        self.manhuang_rank_list:DeleteMe()
        self.manhuang_rank_list = nil
    end

    if self.manhuang_reward_list then
        self.manhuang_reward_list:DeleteMe()
        self.manhuang_reward_list = nil
    end

    if self.manhuang_cancel_match_alert then
		self.manhuang_cancel_match_alert:DeleteMe()
		self.manhuang_cancel_match_alert = nil
	end

    if self.manhaung_day_count_change_event then
		GlobalEventSystem:UnBind(self.manhaung_day_count_change_event)
		self.manhaung_day_count_change_event = nil
	end
end

function FuBenPanelView:OnManHuangDayCounterChange(day_counter_id)
    if DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_ENTER_TIMES == day_counter_id or
            DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_VIP_BUY_TIMES == day_counter_id or
            DAY_COUNT.DAYCOUNT_ID_MANHUANGGUDIAN_FB_HELP_TIMES == day_counter_id then
        self:FlushMHTimes()
    end
end

function FuBenPanelView:FlushMHTimes()
    local mh_data = ManHuangGuDianWGData.Instance
    --次数
    local enter_times = mh_data:GetManHuangEnterTimes()
    local total_enter_times = mh_data:GetManHuangTotalEnterTimes()
    local buy_times = mh_data:GetManHuangBuyTimes()
    local param = VipPower.Instance:GetParam(VipPowerId.reward_manhuanggudian_fb_buy_times)
    local total_times = total_enter_times + buy_times
    local remain_times = total_times - enter_times
    local remain_buy_times = param - buy_times
    --print_error(enter_times, total_enter_times, buy_times, remain_times, total_times, string.format(Language.FuBenPanel.ManHuangTimesStr, remain_times, total_times))
    --self.node_list.lbl_manhuang_count.text.text = string.format(Language.FuBenPanel.ManHuangTimesStr, remain_times, total_times)

    if self.node_list["lbl_manhuang_count"] then
        remain_times = remain_times < 0 and 0 or remain_times
        if remain_times > 0 then
            self.node_list["lbl_manhuang_count"].text.text = string.format(Language.FuBenPanel.HTEFbEnterTimes2, remain_times, total_times)
        else
            self.node_list["lbl_manhuang_count"].text.text = string.format(Language.FuBenPanel.HTEFbEnterTimesNotEnough2, remain_times, total_times)
        end
    end
    --次数特效
    self.node_list.manhuang_effect:SetActive(remain_times <= 0 and remain_buy_times > 0 and FuBenPanelWGData.Instance:CheckVipCondition())
end

function FuBenPanelView:ShowIndexManHuangGuDian()
    --print_error("请求排行信息")
    FuBenWGCtrl.Instance:SendManHuangGuDianReq(MANHUANGGUDIAN_REQ_TYPE.BASE_INFO)
    FuBenWGCtrl.Instance:SendManHuangGuDianReq(MANHUANGGUDIAN_REQ_TYPE.RANK_INFO)
end

function FuBenPanelView:OnFlushManHuangGuDian(param_t)
    self:_InternalFlushManHuangRankList()
    self:_InternalFlushManHuangRewardCell()
    self:_InternalFlushManHuangOther()
    self:FlushMHCombineMask()
end

--刷新副本奖励
function FuBenPanelView:_InternalFlushManHuangRewardCell()
    local data_list = ManHuangGuDianWGData.Instance:GetManHuangItemReward()
    --print_error("data_list >>>>>>>>>>>>> ",data_list)
    if not data_list or IsEmptyTable(data_list) then
        return
    end
    self.manhuang_reward_list:SetDataList(data_list)
end

--刷新排行
function FuBenPanelView:_InternalFlushManHuangRankList()
    local rank_info = ManHuangGuDianWGData.Instance:GetManHuangRankInfo()
    if not rank_info then
        return
    end
    local show_list,num = ManHuangGuDianWGData.Instance:GetManHuangRankShowList()
    self.node_list["manhuang_empty_tips"]:SetActive(num == 0)

    --列表
    self.manhuang_rank_list:SetDataList(show_list)
    --自己的排行数据
    local self_rank = rank_info.self_rank
    if self_rank == 0 then
        self.node_list.manhuang_my_rank.text.text = Language.FuBenPanel.ManHuangNotRankStr
        self.node_list.manhuang_rank_img:SetActive(false)
    else
        self.node_list.manhuang_rank_img:SetActive(self_rank <= 3)
        self.node_list.manhuang_my_rank:SetActive(self_rank > 3)
        if self_rank <= 3 then
            local b,a = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self_rank)
            self.node_list.manhuang_rank_img.image:LoadSprite(b, a, function()
                    XUI.ImageSetNativeSize(self.node_list.manhuang_rank_img)
            end)
        end
        self.node_list.manhuang_my_rank.text.text = self_rank
    end
    local rank_data = rank_info.rank_list[self_rank]
    if rank_data then
        self.node_list.manhuang_my_name.text.text = rank_data.name
        self.node_list.manhuang_my_wave.text.text = rank_data.max_wave
        self.node_list.manhuang_my_time.text.text = TimeUtil.MSTime(rank_data.max_wave_pass_time)
    else
        self.node_list.manhuang_my_name.text.text = GameVoManager.Instance:GetMainRoleVo().name
        self.node_list.manhuang_my_wave.text.text = Language.FuBenPanel.ManHuangNotData
        self.node_list.manhuang_my_time.text.text = Language.FuBenPanel.ManHuangNotData
    end
end

--刷新其他
function FuBenPanelView:_InternalFlushManHuangOther()
    --按钮文本
	local is_match = NewTeamWGData.Instance:GetIsMatching()
    local team_type, fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
	self.node_list.btn_manhuang_pingtai_text.text.text = (is_match and team_type == GoalTeamType.ManHuangGuDian) and Language.NewTeam.IsMatching or Language.NewTeam.AutoMatch
    self.node_list.manhuang_pingtai_effect:SetActive(is_match and team_type == GoalTeamType.ManHuangGuDian)

    local mh_data = ManHuangGuDianWGData.Instance
    --次数、特效
    self:FlushMHTimes()

    --基本信息协议数据
    local base_info = ManHuangGuDianWGData.Instance:GetManHuangBaseInfo()
    if not base_info or IsEmptyTable(base_info) then return end
    --波数
    local wave = base_info.manhuang_max_wave
    self.node_list.lbl_manhuang_cur_boshu.text.text = string.format(Language.FuBenPanel.CengStr, wave)
    --通关时间
    local pass_time = base_info.manhuang_pass_time
    self.node_list.lbl_manhuang_pass_time:SetActive(false)--pass_time > 0)
    --self.node_list.lbl_manhuang_pass_time.text.text = string.format(Language.FuBenPanel.ManHuangPassTime, TimeUtil.MSTime(pass_time))
    --红点
    local is_show_red_point = ManHuangGuDianWGData.Instance:GetIsCanFetch()
    self.node_list.manhuang_red_point:SetActive(is_show_red_point)
    self:ShowManHuangAni(is_show_red_point)
end

function FuBenPanelView:FlushMHCombineMask()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local pre_show_level = combine_cfg[FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN + 1].pre_show_level
    self.node_list.layout_manhuang_combine_mark_root:SetActive(role_level >= pre_show_level)
end

function FuBenPanelView:ShowManHuangAni(is_ani)
	if self.node_list["manhuang_box_icon"] then
		self.node_list["manhuang_box_icon"].animator:SetBool("is_shake", is_ani)
	end
end

function FuBenPanelView:OnClickManHuangBaoMingEnter()
    if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end

    local team_type = GoalTeamType.ManHuangGuDian
    --local data_list = TeamEquipFbWGData.Instance:GetEquipFbList()
	--local cur_cfg = data_list[self.cur_page]
	local fb_mode = 0
    local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(team_type)
    local remain_count = total_count - cur_enter_count
    if remain_count == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
        self:OnClickManHuangAddCount()
        return
    end
    NewTeamWGCtrl.Instance:F2SendTeamFuBenEnter(team_type,fb_mode, 5)

	-- local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	-- if not info then
	-- 	return
	-- end

	-- local is_match = NewTeamWGData.Instance:GetIsMatching()
	-- local operate = is_match and 1 or 0
	-- --如果没在匹配中，才进行一些操作
	-- if not is_match then
 --         --如果选中的是全部队伍，自动创建无目标
 --        if info.team_type == -1 and info.fb_mode == -1 then
 --            info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
 --        end
 --        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
 --        local cur_enter_count, total_count = NewTeamWGData.Instance:GetTimesByTeamType(info.team_type)
 --        local remain_count = total_count - cur_enter_count

 --        if is_not_in_team then
 --            NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, info.role_max_level)
 --            SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表
 --            if remain_count == 0 then
 --                SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
 --                self:OnClickManHuangAddCount()
 --                return
 --            end

 --        end
	-- 	NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
	-- 	NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, info.role_max_level)
	-- 	NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, info.role_max_level)

        

 --        if remain_count == 0 and 1 == SocietyWGData.Instance:GetIsTeamLeader()  then 
 --            SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBen.HaceNoTimes)
 --            self:OnClickManHuangAddCount()
 --            return
 --        end
	-- 	NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
	-- else
 --        local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
 --        if now_team_type == GoalTeamType.ManHuangGuDian then
 --           -- NewTeamWGCtrl.Instance:OpenBaoMingEnterView()
 --            ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
 --        else
 --            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo2)
 --        end
	-- end
end

function FuBenPanelView:OnClickManHuangPingTai()
    if NewTeamWGData.Instance:GetIsInRoomScene() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.InFbStateTip)
		return
	end
    local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetMatchingTeamTypeAndMode()
    local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	if is_match and now_team_type == GoalTeamType.ManHuangGuDian then
        if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
            if not self.manhuang_cancel_match_alert then
                self.manhuang_cancel_match_alert = Alert.New()
            end
            self.manhuang_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
            self.manhuang_cancel_match_alert:SetOkFunc(function()
                    NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.ManHuangGuDian, 0)
            end)
            self.manhuang_cancel_match_alert:Open()
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanDo)
        end
	else
        NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.ManHuangGuDian, 0)
		ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
	end
end

function FuBenPanelView:OnClickManHuangRule()
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.FuBenPanel.RuleTitle[9])
	rule_tip:SetContent(Language.FuBen.ManHuangContentStr)
end

function FuBenPanelView:OnClickManHuangFirstPass()
    ViewManager.Instance:Open(GuideModuleName.ManHuangGuDianFirstPassView)
end

--点击增加次数
function FuBenPanelView:OnClickManHuangAddCount()
    --FuBenPanelWGCtrl.Instance:OpenManHuangGuDianBuy()
    FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB)
end

function FuBenPanelView:OnClickManHuangCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN + 1].level_limit
    if need_level > role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips,level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end
    local total_times = ManHuangGuDianWGData.Instance:GetTotalTimes()
    local remain_times = ManHuangGuDianWGData.Instance:GetRemainTimes()
    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
    local vas = self.node_list.layout_manhuang_combine_hook:GetActive()
    local is_combine = vas == true and 0 or 1
    --print_error(is_combine)
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine,FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN)
         self.node_list.layout_manhuang_combine_hook:SetActive(not vas)
    else
        local callback_func = function()
            self.node_list.layout_manhuang_combine_hook:SetActive(true)
        end
        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN,callback_func)
    end
end

function FuBenPanelView:SendFBUseCombineManHuang()
    local vas = self.node_list.layout_manhuang_combine_hook:GetActive()
    if not vas then return end
    FuBenWGCtrl.Instance:SendFBUseCombine(1,FB_COMBINE_TYPE.MANG_HUANG_GU_DIAN)
end

ManHuangRankItemRender = ManHuangRankItemRender or BaseClass(BaseRender)
function ManHuangRankItemRender:__init()
end

function ManHuangRankItemRender:__delete()
end

function ManHuangRankItemRender:OnFlush()
    local is_danshu = self.index % 2 == 1

    self.node_list["bg1"]:SetActive(is_danshu)
    self.node_list["bg2"]:SetActive(not is_danshu)

    if IsEmptyTable(self.data) then
        self.node_list.rank:SetActive(false)
        self.node_list.rank_img:SetActive(false)
        self.node_list.name.text.text = ""
        self.node_list.wave.text.text = ""
        self.node_list.time.text.text = ""
        return
    end

    self.node_list.rank_img:SetActive(self.index <= 3)
	self.node_list.rank:SetActive(self.index > 3)
	if self.index <= 3 then
		local b,a = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.index)
		self.node_list.rank_img.image:LoadSprite(b, a, function()
				XUI.ImageSetNativeSize(self.node_list.rank_img)
		end)
	end

    self.node_list.rank.text.text = self.index
    self.node_list.name.text.text = self.data.name
    self.node_list.wave.text.text = self.data.max_wave
    self.node_list.time.text.text = TimeUtil.MSTime(self.data.max_wave_pass_time)
end
