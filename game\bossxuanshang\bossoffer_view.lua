BossOfferReward = BossOfferReward or BaseClass(SafeBaseView)

function BossOfferReward:__init()
    self:SetMaskBg()
    local bundle_name = "uis/view/boss_xuanshang_prefab"
    self:AddViewResource(0, bundle_name, "layout_boss_xuanshang")
    self.grade = 0
    self.count = 0
    self.need_count = 0
end

function BossOfferReward:__delete()
 
end

function BossOfferReward:ReleaseCallBack()
    if self.boss_list then
        self.boss_list:DeleteMe()
        self.boss_list = nil
    end

    if self.item_cell then
        for i,v in ipairs(self.item_cell) do
            v:DeleteMe()
        end
        self.item_cell = nil
    end

    self.grade = 0
    self.count = 0
    self.need_count = 0

end

function BossOfferReward:LoadCallBack()
    if not self.boss_list then
        self.boss_list = AsyncListView.New(OfferBossRender, self.node_list["boss_list"])
        --self.boss_list:SetSelectCallBack(BindTool.Bind1(self.BossLsitSelectCallBack, self))
    end
    self.item_cell = {}
    self.node_list.btn_go.button:AddClickListener(BindTool.Bind(self.OnClickToGo, self))
    self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function BossOfferReward:OnFlush()
    local is_last = false  
    self.grade = BossOfferWGData.Instance:GetBossGradeID()
    local is_remind = BossOfferWGData.Instance:GetAwakenRemind()
    local boss_cfg = BossOfferWGData.Instance:GetBossListByGrade(self.grade)
    local reward_flag = BossOfferWGData.Instance:GetRewardFlag(self.grade)
    local is_show = BossOfferWGData.Instance:GetBossOfferAwakenIsCompleted()
    self.need_count = BossOfferWGData.Instance:GetBossCfgCountByGrade(self.grade)
    self.count = BossOfferWGData.Instance:GetBossKillCount(self.grade)
    self.boss_list:SetDataList(boss_cfg, 3)
    self:SetItemCellList(self.grade)
    self.node_list.remind:SetActive(is_remind == 1)
    self.node_list.get_reward_img:SetActive(is_remind ~= 1 and reward_flag)
    XUI.SetButtonEnabled(self.node_list.btn_lingqu, reward_flag)
    self.node_list.btn_lingqu:SetActive(not is_show)
    self.node_list.yijihuo:SetActive(is_show)
    self.node_list.add_value:SetActive(not is_show)
    self.node_list.get_over_img:SetActive(is_show)
    self.node_list.btn_go:SetActive(not is_show)
    self:FlushBossDescPanel(self.grade, self.count, self.need_count)
end

function BossOfferReward:OpenCallBack()
    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.BOSS_XUAN_SHANG) then
        BossOfferWGCtrl.Instance:SendReq(1)
    end
end

function BossOfferReward:SetItemCellList(grade)
    if nil == grade then return end
    local data = BossOfferWGData.Instance:GetCfgByGrade(grade)--self.select_item_data
    if nil == data and data.reward_item == nil then return end
    for i = 0, #data.reward_item do
        if not self.item_cell[i] then
            self.item_cell[i] = ItemCell.New(self.node_list["item_list"])
        end
         self.item_cell[i]:SetData(data.reward_item[i])
    end
end

function BossOfferReward:BossLsitSelectCallBack(cell)
    self.select_item_data = cell.data
end

function BossOfferReward:FlushBossDescPanel(grade, count, need_count)
    local title_num = BossOfferWGData.Instance:GetCfgByGrade(grade).boss_id
    local title = Language.Boss.BossOfferMap[title_num]
    local data = BossOfferWGData.Instance:GetCfgByGrade(grade)
    local color 
    if count >= need_count then
        color = COLOR3B.GREEN
    else
        color = COLOR3B.RED
    end

    self.node_list.kill_count.text.text = ToColorStr(string.format(Language.Boss.OfferNum, count, need_count), color)
    self.node_list.kill_des.text.text = string.format(Language.Boss.OfferTips2, title, data.boss_level_min)
end

function BossOfferReward:OnClickToGo()
    local boss_id = BossOfferWGData.Instance:GetFirstBossCfgByGrade(self.grade)
    BossOfferWGCtrl.Instance:JumToByID(boss_id)
end

function BossOfferReward:OnClickGetReward()
    BossOfferWGCtrl.Instance:SendReq(0, self.grade)
end


----------------------------------------------------------------------------------------
OfferBossRender = OfferBossRender or BaseClass(BaseRender)

function OfferBossRender:__init()
    self.str = ""
    self.node_list.zhezhao.button:AddClickListener(BindTool.Bind(self.OnClickTips, self))
    self.node_list.btn_go.button:AddClickListener(BindTool.Bind(self.OnClickToGo, self))
    self.node_list.boss_suo.button:AddClickListener(BindTool.Bind(self.OnClickTips, self))
    self.select_mark = nil
end

function OfferBossRender:__delete()
    self.select_mark = nil
    self.str = ""
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end
end

function OfferBossRender:OnFlush()
    local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. self.data[1].big_icon)
    self.node_list["xsboss"].image:LoadSprite(bundle, asset, function()
        self.node_list.xsboss.image:SetNativeSize()
    end)
    self.node_list["level"].text.text = string.format(Language.Boss.Ji1, self.data[1].boss_level)
    
    --BossWGData.Instance:GetBossLevelLimitBySceneId()
    self:ShowLockInfo()
end

function OfferBossRender:ShowLockInfo()
    --local boss_data = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    --local list = BossWGData.Instance:GetBossOfferCfg(self.data.boss_type, self.data.boss_id)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if self.data[1].is_open then
        if self.data[1].need_role_level and role_level < self.data[1].need_role_level then
            if self.refresh_event then
                GlobalTimerQuest:CancelQuest(self.refresh_event)
                self.refresh_event = nil
            end
            self.node_list.zhezhao:SetActive(false)
            self.node_list.boss_suo:SetActive(true)
            self.node_list.lbl_time:SetActive(false)
            -- self.node_list["xsboss_render"].button.interactable = false
            self.str = string.format(Language.Boss.OfferTips1, self.data[1].boss_name, self.data[1].need_role_level)
        else 
            self:RefreshRemainTime()
            if self.refresh_event == nil then
                self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime,self),1)
            end
        end 
    else
        self.str = Language.Boss.OfferTips3
        --self.node_list["xsboss_render"].button.interactable = false
        self.node_list.zhezhao:SetActive(false)
        self.node_list.boss_suo:SetActive(true)
        self.node_list.lbl_time:SetActive(false)
    end
end

function OfferBossRender:OnClickTips()
    SysMsgWGCtrl.Instance:ErrorRemind(self.str)
end

function OfferBossRender:OnClickToGo()
    BossOfferWGCtrl.Instance:JumToByID(self.data[1].boss_id)
end

function OfferBossRender:OnSelectChange(is_select)
  --[[  if self.select_mark ~= is_select then
        self.node_list.select_bg:SetActive(is_select)
        self.select_mark = is_select
    end--]]
end

function OfferBossRender:RefreshRemainTime()
    local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.data[1].boss_id)

    if boss_server_info == nil then
        boss_server_info = BossWGData.Instance:GetCrossBossById(self.data[1].boss_id)
    end
    --local boss_data = BossWGData.Instance:GetBossInfoByBossId(self.data.boss_id)
    --local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local time = 0
    if boss_server_info ~= nil then
        time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
    end

    local state = time > 1
    if state then
        self.node_list.zhezhao:SetActive(true)
        self.node_list.boss_suo:SetActive(false)
        --self.node_list["xsboss_render"].button.interactable = false
        self.str = Language.Boss.OfferTips
        self.node_list.lbl_time:SetActive(true)
        self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(time)
        --SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.OfferTips)
    else
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
         --self.node_list["xsboss_render"].button.interactable = true
        self.node_list.zhezhao:SetActive(false)
        self.node_list.boss_suo:SetActive(false)
        self.node_list.lbl_time:SetActive(false)
    end
end