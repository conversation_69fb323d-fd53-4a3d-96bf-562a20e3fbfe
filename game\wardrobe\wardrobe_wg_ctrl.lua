require("game/wardrobe/wardrobe_wg_data")
require("game/wardrobe/wardrobe_item")
require("game/wardrobe/wardrobe_view")
require("game/wardrobe/wardrode_total_attr")
require("game/wardrobe/wardrobe_suit_view")
require("game/wardrobe/wardrobe_fashion_view")
require("game/wardrobe/wardrobe_jewelry_view")
require("game/wardrobe/wardrobe_effect_view")
require("game/wardrobe/wardrobe_casting_skill_view")
require("game/wardrobe/wardrobe_casting_view")
require("game/wardrobe/wardrobe_dressing_up_view")
require("game/wardrobe/wardrobe_casting_upgrade_view")

-- 【衣橱】
WardrobeWGCtrl = WardrobeWGCtrl or BaseClass(BaseWGCtrl)

function WardrobeWGCtrl:__init()
	if nil ~= WardrobeWGCtrl.Instance then
		ErrorLog("[WardrobeWGCtrl] attempt to create singleton twice!")
		return
	end
	WardrobeWGCtrl.Instance = self

    self.data = WardrobeWGData.New()
	self.view = WardrobeView.New(GuideModuleName.WardrobeView)
	self.total_attr_view = WardrobeTotalAttrView.New()
	self.wardrobe_casting_skill_view = WardrobeCastingSkillView.New()
	self.wardrobe_casting_view = WardrobeCastingView.New()
	self.wardrobe_dressing_up_view = WardrobeDressingUpView.New()
	self.wardrobe_casting_upgrade_view = WardrobeCastingUpgradeView.New()

    self:RegisterAllProtocols()
	self.day_pass_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnOpenConditionChange, self))
	self.role_attr_data_change = BindTool.Bind1(self.RoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_attr_data_change, {"level"})
end

function WardrobeWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

	self.total_attr_view:DeleteMe()
	self.total_attr_view = nil

	if self.wardrobe_casting_skill_view then
		self.wardrobe_casting_skill_view:DeleteMe()
		self.wardrobe_casting_skill_view = nil
	end

	if self.wardrobe_casting_view then
		self.wardrobe_casting_view:DeleteMe()
		self.wardrobe_casting_view = nil
	end

	if self.wardrobe_dressing_up_view then
		self.wardrobe_dressing_up_view:DeleteMe()
		self.wardrobe_dressing_up_view = nil
	end

	if self.wardrobe_casting_upgrade_view then
		self.wardrobe_casting_upgrade_view:DeleteMe()
		self.wardrobe_casting_upgrade_view = nil
	end

	if self.day_pass_event then
		GlobalEventSystem:UnBind(self.day_pass_event)
		self.day_pass_event = nil
	end

	if self.role_attr_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_attr_data_change)
		self.role_attr_data_change = nil
	end

    WardrobeWGCtrl.Instance = nil
end

function WardrobeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSWardrobeOperate)
	self:RegisterProtocol(SCWardrobeItemInfo, "OnSCWardrobeItemInfo")
	self:RegisterProtocol(SCWardrobeItemUpdate, "OnSCWardrobeItemUpdate")

	self:RegisterNewAllProtocols()
end

function WardrobeWGCtrl:OnSCWardrobeItemInfo(protocol)
	-- print_error("---【衣橱】全部套装信息----", protocol)
	self.data:SetAllSuitStateInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Wardrobe)
	RemindManager.Instance:Fire(RemindName.HmGodView)
	RemindManager.Instance:Fire(RemindName.GuiXuDreamView)

	ViewManager.Instance:FlushView(GuideModuleName.HmGodView)
	ViewManager.Instance:FlushView(GuideModuleName.GuiXuDreamView)
	CustomizedSuitWGCtrl.Instance:FlushView()
	FunOpen.Instance:DoOpenFun(FunOpen.Instance:GetFunByName(FunName.CustomizedSuitView), true)
	local attr_view = GuiXuDreamWGCtrl.Instance:GetGuiXuDreamAttrView()
	if attr_view and attr_view:IsOpen() then
		attr_view:Flush()
	end

	self:FlushCurShowView()
end

function WardrobeWGCtrl:OnSCWardrobeItemUpdate(protocol)
	-- print_error("---【衣橱】单个信息更新----", protocol)
	self.data:SetSingleSuitStateInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Wardrobe)
	RemindManager.Instance:Fire(RemindName.HmGodView)
	RemindManager.Instance:Fire(RemindName.GuiXuDreamView)
	ViewManager.Instance:FlushView(GuideModuleName.GuiXuDreamView)
	ViewManager.Instance:FlushView(GuideModuleName.HmGodView, nil, "protocol_change")
	CustomizedSuitWGCtrl.Instance:FlushView()
	FunOpen.Instance:DoOpenFun(FunOpen.Instance:GetFunByName(FunName.CustomizedSuitView), true)
	local attr_view = GuiXuDreamWGCtrl.Instance:GetGuiXuDreamAttrView()
	if attr_view and attr_view:IsOpen() then
		attr_view:Flush()
	end
	self:FlushCurShowView()
end

function WardrobeWGCtrl:SendWardrobeRequest(operate_type, param1, param2)
	-- print_error("---【衣橱】请求----", operate_type, param1, param2)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSWardrobeOperate)
 	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol:EncodeAndSend()
end

function WardrobeWGCtrl:OnPartActiveResult(result, suit, part)
	-- print_error("---【衣橱】激活返回----", result, suit, part)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:DoActiveEffect(suit, part)
		end

		if ViewManager.Instance:IsOpen(GuideModuleName.HmGodView) then
			HmGodWGCtrl.Instance:OnPartActiveResult(result, suit, part)
		end

		if ViewManager.Instance:IsOpen(GuideModuleName.CustomizedSuitView) then
			CustomizedSuitWGCtrl.Instance:OnPartActiveResult(result, suit, part)
		end

		local attr_view = GuiXuDreamWGCtrl.Instance:GetGuiXuDreamAttrView()
		if attr_view and attr_view:IsOpen() then
			GuiXuDreamWGCtrl.Instance:OnPartActiveResult(result, suit, part)
		end
	end
end

function WardrobeWGCtrl:OpenTotalAttrView()
	self.total_attr_view:Open()
end

function WardrobeWGCtrl:OnOpenConditionChange()
	self.data:GetSuitShowList()
	if self.view:IsOpen() then
		self.view:Flush(0, "level_day_change")
	end
	RemindManager.Instance:Fire(RemindName.Wardrobe)
end

function WardrobeWGCtrl:RoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" and value > old_value then
		self:OnOpenConditionChange()
	end
end

function WardrobeWGCtrl:FlushCurShowView(param_t, key)
	if self.view:IsOpen() then
		self.view:FlushCurShowView(param_t, key)
	end

	local rm = RemindManager.Instance
	rm:Fire(RemindName.WardrobeFashion)
	rm:Fire(RemindName.WardrobeJewelry)
	rm:Fire(RemindName.WardrobeEffect)
	rm:Fire(RemindName.WardrobeSuit)
end

function WardrobeWGCtrl:SetNowCacheUseList(project_cache_use_id)
	if self.view:IsOpen() then
		self.view:SetNowCacheUseList(project_cache_use_id)
		self:FlushCurShowView()
	end
end
---------------------------------------------------
------------新衣橱---------------------------------
-- 注册协议
function WardrobeWGCtrl:RegisterNewAllProtocols()
	self:RegisterProtocol(CSShiZhuangProjectSave)
	self:RegisterProtocol(CSShizhuangForgeOper)
	self:RegisterProtocol(SCShiZhuangProjectInfo, "OnSCShiZhuangProjectInfo")
	self:RegisterProtocol(SCShizhuangForgeInfo, "OnSCShizhuangForgeInfo")
	self:RegisterProtocol(SCShizhuangForgeUpdateInfo, "OnSCShizhuangForgeUpdateInfo")
end

-- 预览时装保存
function WardrobeWGCtrl:SendShiZhuangProjectRequest(project_id, part_list)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSShiZhuangProjectSave)
 	protocol.project_id = project_id or 0
 	protocol.part_list = part_list or {}
 	protocol:EncodeAndSend()
end

-- 天赏打造请求
function WardrobeWGCtrl:SendShiZhuangForgeOperRequest(operate_type, param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSShizhuangForgeOper)
 	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
 	protocol:EncodeAndSend()
end

-- 天赏打造升星
function WardrobeWGCtrl:SendShiZhuangForgeUpStar(seq, is_use_item)
	self:SendShiZhuangForgeOperRequest(WARDROBE_NEW_PART_TYPE.FORGE_OPER_TYPE_UP_STA, seq, is_use_item)
end

-- 天赏打造升阶
function WardrobeWGCtrl:SendShiZhuangForgeUpGrade(seq)
	self:SendShiZhuangForgeOperRequest(WARDROBE_NEW_PART_TYPE.FORGE_OPER_TYPE_UP_GRADE, seq)
end

-- 天赏打造镶嵌天石
function WardrobeWGCtrl:SendShiZhuangForgePutStone(seq, stone_seq)
	self:SendShiZhuangForgeOperRequest(WARDROBE_NEW_PART_TYPE.FORGE_OPER_TYPE_PUT_ON_STONE, seq, stone_seq)
end

-- 天赏打造合成天石
function WardrobeWGCtrl:SendShiZhuangForgeCpmposeStone(seq, stone_part)
	self:SendShiZhuangForgeOperRequest(WARDROBE_NEW_PART_TYPE.FORGE_OPER_TYPE_COMPOSE_STONE, seq, stone_part)
end

-- 时装方案信息
function WardrobeWGCtrl:OnSCShiZhuangProjectInfo(protocol)
	-- print_error("时装方案信息", protocol)
	self.data:SetShiZhuangProjectInfo(protocol)
	self:FlushDressingUpView()
end

-- 时装天赏信息
function WardrobeWGCtrl:OnSCShizhuangForgeInfo(protocol)
	-- print_error("时装天赏信息", protocol)
	self.data:SetShizhuangForgeInfo(protocol)
	self:FlushCastingView()
	self:FlushCurShowView()
end

-- 时装天赏信息(单个)
function WardrobeWGCtrl:OnSCShizhuangForgeUpdateInfo(protocol)
	-- print_error("时装天赏信息(单个)", protocol)
	self.data:SetShizhuangForgeUpdateInfo(protocol)
	self:FlushCastingView()
end

-- 打开时装穿搭
function WardrobeWGCtrl:OpenDressingUpView(dressing_up_data)
	self.wardrobe_dressing_up_view:SetNowCacheData(dressing_up_data)
	if not self.wardrobe_dressing_up_view:IsOpen() then
		self.wardrobe_dressing_up_view:Open()
	else
		self.wardrobe_dressing_up_view:Flush()
	end
end

-- 刷新时装穿搭
function WardrobeWGCtrl:FlushDressingUpView()
	if self.wardrobe_dressing_up_view:IsOpen() then
		self.wardrobe_dressing_up_view:Flush()
	end
end

-- 打开天赏打造
function WardrobeWGCtrl:OpenCastingView(casting_up_data)
	self.wardrobe_casting_view:SetNowCacheData(casting_up_data)
	if not self.wardrobe_casting_view:IsOpen() then
		self.wardrobe_casting_view:Open()
	else
		self.wardrobe_casting_view:Flush()
	end
end

-- 刷新天赏打造
function WardrobeWGCtrl:FlushCastingView(is_item_change)
	if self.wardrobe_casting_view:IsOpen() then
		if is_item_change then
			self.wardrobe_casting_view:Flush(0, "item_change")
		else
			self.wardrobe_casting_view:Flush()
		end
	end
end

-- 打开天赏打造技能
function WardrobeWGCtrl:OpenCastingSkillView(cache_data)
	self.wardrobe_casting_skill_view:SetNowCacheData(cache_data)

	if not self.wardrobe_casting_skill_view:IsOpen() then
		self.wardrobe_casting_skill_view:Open()
	else
		self.wardrobe_casting_skill_view:Flush()
	end
end

-- 刷新天赏打造
function WardrobeWGCtrl:FlushCastingSkillView()
	if self.wardrobe_casting_skill_view:IsOpen() then
		self.wardrobe_casting_skill_view:Flush()
	end
end

-- 打开天赏天石升品界面
function WardrobeWGCtrl:OpenCastingUpgradeView(show_data)
	self.wardrobe_casting_upgrade_view:SetShowData(show_data)
	if not self.wardrobe_casting_upgrade_view:IsOpen() then
		self.wardrobe_casting_upgrade_view:Open()
	else
		self.wardrobe_casting_upgrade_view:Flush()
	end
end


-- 更新UI场景数据
function WardrobeWGCtrl:UpdateUISceneShowState()
	if self.view:IsOpen() then
		self.view:FlushCheckUISceneShow()
		self:FlushCurShowView()
	end
end

