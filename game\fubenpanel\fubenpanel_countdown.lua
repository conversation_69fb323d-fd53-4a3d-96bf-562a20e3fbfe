FuBenPanelCountDown = FuBenPanelCountDown or BaseClass(SafeBaseView)
-- 副本信息面板
function FuBenPanelCountDown:__init()
	if FuBenPanelCountDown.Instance then
		ErrorLog("[FuBenPanelCountDown] Attemp to create a singleton twice !")
	end
	FuBenPanelCountDown.Instance = self
	self.view_layer = UiLayer.MainUIHigh
	self.view_name = "FuBenPanelCountDown"
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_countdown")
	self.timer_num = 0
	self.countdown_num = 0
end

function FuBenPanelCountDown:__delete()
	FuBenPanelCountDown.Instance = nil
end

function FuBenPanelCountDown:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("fb_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("fb_txt_end_countdown")
	end

	if self.lbl_end_time then
		self.lbl_end_time:DeleteMe()
		self.lbl_end_time = nil
	end
	self.title_type = nil
	self.is_specical_deal = nil
	self.cur_time = nil
end

function FuBenPanelCountDown:CloseCallBack()
	self.countdown_num = 0
	if CountDownManager.Instance:HasCountDown("fb_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("fb_txt_end_countdown")
	end
	if self.tmp_call_back ~= nil then
		self.tmp_call_back = nil
	end
end

function FuBenPanelCountDown:LoadCallBack()
	--self.node_list.Img:SetActive(false)
	--默认状态
	self.node_list.endtime_type_1:SetActive(true)
	self.node_list.endtime_type_2:SetActive(false)
	self.is_specical_deal = false
	if self.title_type then
		if self.title_type == GameEnum.FU_BEN_NEXT_LEVEL then -- 2 下一层
			--self.node_list.tip_image.image:LoadSprite("uis/view/fubenpanel/images_atlas","zi_xiayiceng")
			self.node_list.tip_txt.text.text = Language.FuBen.FuBenTitleTxt[0]
		elseif self.title_type == GameEnum.FU_HUO_DAOJISHI then --1 复活
			--self.node_list.tip_image.image:LoadSprite("uis/view/fubenpanel/images_atlas","fuhuo")
			self.node_list.tip_txt.text.text = Language.FuBen.FuBenTitleTxt[1]
		elseif self.title_type == GameEnum.FU_BEN_OUT_SCENE then --3退出
			--self.node_list.tip_image.image:LoadSprite("uis/view/fubenpanel/images_atlas","zi_tuichu")
			self.node_list.tip_txt.text.text = Language.FuBen.FuBenTitleTxt[2]
		elseif self.title_type == GameEnum.XIU_LUO_TA_NEXT_LAYER then
			--self.node_list.tip_image.image:LoadSprite("uis/view/fubenpanel/images_atlas","zi_xiayiceng")
			self.node_list.tip_txt.text.text = Language.FuBen.FuBenTitleTxt[0]
			--self.node_list.Img:SetActive(true)
		elseif self.title_type == GameEnum.FU_BEN_ZHUNBEI then
			--self.node_list.tip_image.image:LoadSprite("uis/view/fubenpanel/images_atlas","act_open_countdown")
			self.node_list.tip_txt.text.text = Language.FuBen.FuBenTitleTxt[3]
			if SCENE_STARETIME_TYPE[Scene.Instance:GetSceneType()] then
				self.is_specical_deal = true
				self.node_list.endtime_type_1:SetActive(false)
				self.node_list.endtime_type_2:SetActive(true)
				-- if self.node_list.start_image then
    -- 				self.node_list.start_image:SetActive(false)
    -- 			end
				-- self:SetDotweenAni(true)
			end
		end
		--XUI.ImageSetNativeSize(self.node_list.tip_image)
	end
	self.title_type = nil
	self.endtime_type_1_pos = self.node_list.endtime_type_1.transform.localPosition
end

function FuBenPanelCountDown:ShowIndexCallBack(index)
	if self.off then
		self.node_list.endtime_type_1.transform.localPosition = Vector2(self.endtime_type_1_pos.x + self.off.x, self.endtime_type_1_pos.y + self.off.y)
	else
		self.node_list.endtime_type_1.transform.localPosition = self.endtime_type_1_pos
	end
end

function FuBenPanelCountDown:SetTipTxtStr( str ,enume)
	self.str = str
	self.title_type = enume
end

function FuBenPanelCountDown:UpdataNextTime(elapse_time, total_time)
	if total_time - elapse_time >= 0 and self.node_list.time_text then
		local time = math.floor(total_time - elapse_time)
		self.node_list["time_text"].text.text = time
		if self.is_specical_deal then
			self:DoTweenScaleContentFightStart(time)
		end
	end
end

function FuBenPanelCountDown:CompleteNextTime()
	FuBenPanelWGCtrl.Instance.is_show_end_time = false
	if self.lbl_end_time then
		self.lbl_end_time:SetNumber(0)
	end
	if self.tmp_call_back ~= nil then
		self.tmp_call_back()
		self.tmp_call_back = nil
	end
	self:CloseViewHandler()

end

-- 传入数据
function FuBenPanelCountDown:SetTimerInfo(countdown_num,call_back, off)
	if self.countdown_num ~= 0 then return end                  -- 协议多次下发导致秒数跳
	self.countdown_num = countdown_num - TimeWGCtrl.Instance:GetServerTime()
	self:OpenPanel()
	--引导退出
	self.off = off

	self.tmp_call_back = call_back
	if CountDownManager.Instance:HasCountDown("fb_txt_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("fb_txt_end_countdown")
	end
	self:UpdataNextTime(0, self.countdown_num)
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(0)	--关闭mainui的倒计时
	CountDownManager.Instance:AddCountDown("fb_txt_end_countdown", BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind1(self.CompleteNextTime, self), nil, self.countdown_num, 0.02)
end

function FuBenPanelCountDown:OpenPanel()
	if self.countdown_num > 0 then
		if not self:IsOpen() then
			self:Open()
		end
	else
		self:CloseViewHandler()
	end
end

-- 关闭面板
function FuBenPanelCountDown:CloseViewHandler()
	--关闭引导退出显示
	self.countdown_num = 0
	self:Close()
end

--跟竞技场一样，前天做的又说不要 7.28
function FuBenPanelCountDown:SetDotweenAni(bool)

	if nil == self.node_list.left_red_ani then return end
	if bool  then
		self.node_list.left_red_ani:SetActive(true)
		self.node_list.right_blue_ani:SetActive(true)
		local tween1 = self.node_list.left_red_ani.rect:DOAnchorPosX(-333, 0.2)
	    tween1:SetEase(DG.Tweening.Ease.Linear)
	    local tween2 = self.node_list.right_blue_ani.rect:DOAnchorPosX(333, 0.2)
	    tween2:SetEase(DG.Tweening.Ease.Linear)
    else
    	if self.node_list.start_image then
    		self.node_list.start_image:SetActive(false)
    	end
	    local tween1 = self.node_list.left_red_ani.rect:DOAnchorPosX(-1600, 0.1)
	    tween1:SetEase(DG.Tweening.Ease.Linear)
	    local tween2 = self.node_list.right_blue_ani.rect:DOAnchorPosX(1600, 0.1)
	    tween2:SetEase(DG.Tweening.Ease.Linear)
	    self.node_list.left_red_ani:SetActive(false)
		self.node_list.right_blue_ani:SetActive(false)
    end
end


function FuBenPanelCountDown:DoTweenScaleContentFightStart(time)
	if self.cur_time and self.cur_time == time then return end
	self.cur_time = time
	if time <= 0 then
		self:EndStartTimeWayShow(false)
		local scale = Vector3(1,1,1)
		-- self.node_list.star_fight.rect.localScale = Vector3(3,3,1)
		-- self.node_list.star_fight.rect:DOScale(scale,0.3)
	else
		self:EndStartTimeWayShow(true)
		local scale = Vector3(1,1,1)
		self.node_list.text_progress_number.text.text = time
		self.node_list.text_progress1.rect.localScale = Vector3(3,3,1)
		self.node_list.text_progress1.rect:DOScale(scale,0.3)
	end
	EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
end


function FuBenPanelCountDown:EndStartTimeWayShow(bool)
	self.node_list.effect_point:SetActive(bool)
	self.node_list.text_progress1:SetActive(bool)
	--self.node_list.star_fight:SetActive(not bool)
end



