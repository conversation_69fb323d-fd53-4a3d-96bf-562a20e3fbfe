CultivationBuffView = CultivationBuffView or BaseClass(SafeBaseView)

local TOGGLE_MAX = 10

function CultivationBuffView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 2), sizeDelta = Vector2(872, 574)})
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_buff_view")
end

function CultivationBuffView:LoadCallBack()

    self.big_toggle_list = nil
	self.small_toggle_list = nil
	self.buff_cell_list = {}
    
	self.btn_within = 0
	self.btn_outer = 1
	self.is_can_flush = true --是否可以刷新列表，因为在场景内战斗时会影响协议问题所以
	self.is_zidong = false --手否自动点击外层按钮

    -- 多脚本共用变量
	self.choose_type_sort = nil
    self.choose_client_sort = nil

	self:CreateAccordion()
    -- self.node_list.desc_privilege_tip.text.text = Language.Cultivation.PrivilegeViewTips

	if not self.buff_list then
		self.buff_list = AsyncListView.New(CultivationBuffItemRender, self.node_list.buff_list)
	end
end

function CultivationBuffView:ShowIndexCallBack()
    -- self:PlayTween()
end

function CultivationBuffView:ReleaseCallBack()
    if self.buff_list then
        self.buff_list:DeleteMe()
        self.buff_list = nil
    end

end

function CultivationBuffView:OnFlush()
	self:SetRightList()
	self:FlushTotalAdd()
	self:FlushRender()
end

function CultivationBuffView:FlushRender()
	if self.big_toggle_list then
		for key, value in pairs(self.big_toggle_list) do
			value.list_item_cell:FlushData()
		end
	end
	if self.buff_cell_list[self.btn_within] then
		for key, value in pairs(self.buff_cell_list[self.btn_within]) do
			value:FlushData()
		end
	end
end

function CultivationBuffView:SetRightList()
	local type_sort = self:GetChooseTypeSort()
	local client_sort = self:GetChooseClientSort()
	if type_sort == nil or client_sort == nil then
		return
	end
    local buff_list = CultivationWGData.Instance:GetCultivationBuffTable(type_sort, client_sort)

	table.sort(buff_list, function(a, b)
		local a_state = CultivationWGData.Instance:IsReceivedBuffByIndex(a.type, a.index) and 3 or 2
		local b_state = CultivationWGData.Instance:IsReceivedBuffByIndex(b.type, b.index) and 3 or 2
		if a_state == b_state and a_state ~= 3 then
			a_state = CultivationWGData.Instance:IsRichBuffByData(a) and 1 or 2
			b_state = CultivationWGData.Instance:IsRichBuffByData(b) and 1 or 2
			return a_state < b_state
		end
		return a_state < b_state
	end)
    -- 无限列表
	if self.buff_list then
		self.buff_list:SetDataList(buff_list)
	end
end

function CultivationBuffView:FlushTotalAdd()
	local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
	self.node_list.text_number.text.text = string.format(Language.Cultivation.ExpAddTotalStr,number)
	self.node_list.text_rate.text.text = string.format(Language.Cultivation.ExpAddTotalStr2,rate)

end
------------------------------------------------------------------------
-- 创建扩展列表
function CultivationBuffView:CreateAccordion()
	if self.big_toggle_list then return end
	if nil == self.buff_cell_list then return end
	--成就目录
	local accordion_tab = CultivationWGData.Instance:GetAccordionTable()
	-- print_error("获取成就目录",accordion_tab)

	self.big_toggle_list = {}
	self.small_toggle_list = {}
	for i = 1, TOGGLE_MAX do
		local content_node = self.node_list["buff_content"]
		self.small_toggle_list[i] = content_node:FindObj("List" .. i)
		self.small_toggle_list[i].list_item_cell = {}

		self.big_toggle_list[i] = content_node:FindObj("SelectBtn" .. i)
		self.big_toggle_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler, self, i,
			accordion_tab[i].type_sort, true))
		local big_btn_cell = CultivationBuffTypeToggleRender.New(content_node:FindObj("SelectBtn" .. i))
		big_btn_cell:SetIndex(i)
		self.big_toggle_list[i].list_item_cell = big_btn_cell

		if nil ~= accordion_tab[i] and nil ~= accordion_tab[i].child then
			self.big_toggle_list[i].list_item_cell:SetData(accordion_tab[i])
			self:LoadCJCell(i, accordion_tab[i].child)
		end
	end
end

function CultivationBuffView:LoadCJCell(index, accor_data)
	if nil == accor_data then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "buff_accordion_item" .. index)
	res_async_loader:Load("uis/view/cultivation_ui_prefab", "buff_accordion_item", nil,
		function(new_obj)
			local item_vo = {}
			local count = -1
			for key, value in pairs(accor_data) do
				count = count + 1
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.small_toggle_list[index].transform, false)
				obj:GetComponent("Toggle").group = self.small_toggle_list[index].toggle_group
				self.small_toggle_list[index].list_item_cell[count] = obj
				local item_render = CultivationBuffListItemRender.New(obj)
				item_render:SetData(value) --里层按钮赋值信息
				item_render.parent_view = self
				item_vo[count] = item_render
			end
			-- for i = 1, #accor_data do
			-- 	local obj = ResMgr:Instantiate(new_obj)
			-- 	local obj_transform = obj.transform
			-- 	obj_transform:SetParent(self.small_toggle_list[index].transform, false)
			-- 	obj:GetComponent("Toggle").group = self.small_toggle_list[index].toggle_group
			-- 	self.small_toggle_list[index].list_item_cell[i] = obj
			-- 	local item_render = CultivationBuffListItemRender.New(obj)
			-- 	item_render:SetData(accor_data[i]) --里层按钮赋值信息
			-- 	item_render.parent_view = self
			-- 	item_vo[i] = item_render
			-- end
			self.buff_cell_list[index] = item_vo
			if index == TOGGLE_MAX then
				self:IsBtnRedShow()
			end
		end)
end

--刷新手风琴数据
function CultivationBuffView:SetAccordionData(is_click)
	local accordion_tab = CultivationWGData.Instance:GetAccordionTable()
	if nil == accordion_tab then return end
	if nil == self.buff_cell_list then return end
	for k, v in pairs(self.buff_cell_list) do
		local child_data = accordion_tab[k].child
		if nil ~= accordion_tab[k] or nil ~= child_data then
			for k1, v1 in pairs(v) do
				v1:SetData(child_data[k1])
			end
		end
	end
end

--外层按钮
function CultivationBuffView:OnClickExpandHandler(index, sort, istrue, is_on)
	if not self.buff_cell_list[index] then
		return
	end
	if self.is_zidong then
		local show_list = {}
		for i, v in ipairs(self.buff_cell_list[index]) do
			show_list = CultivationWGData.Instance:GetCultivationBuffTable(index, v.data.client_sort)
			v:SetVisible(#show_list > 0)
		end
		return
	end

	if nil == sort or nil == is_on or false == is_on or self.is_zidong then return end

	if istrue and (#self.buff_cell_list[index] < self.btn_outer or self.btn_within ~= index or (self.btn_within == index and self.up_btn_outer == self.btn_outer)) then
		self.btn_outer = 0
	end
	self.btn_within = index
	self:SetChooseTypeSort(sort)
	local obj = self.small_toggle_list[index].list_item_cell[self.btn_outer]
	obj:GetComponent("Toggle").isOn = true

	-- 内层按钮显隐
	local show_list = {}
	local show_index
	for i, v in pairs(self.buff_cell_list[index]) do
		show_list = CultivationWGData.Instance:GetCultivationBuffTable(index, v.data.client_sort)
		v:SetVisible(#show_list > 0)
		if #show_list > 0 and not show_index then
			show_index = i
		end
	end

	if self.big_toggle_list[index].accordion_element.isOn then
		if not self.is_zidong then
			self.buff_cell_list[index][show_index]:OnClickItem(true)
		end
	end
end

--外层按钮(为了区分手动和自动点击)
function CultivationBuffView:CaseOnClickExpandHandler(index, sort, istrue, is_on)
	if nil == sort or nil == is_on or false == is_on then return end
	if not self.buff_cell_list[index] then
		return
	end
	if istrue and (#self.buff_cell_list[index] < self.btn_outer or self.btn_within ~= index or (self.btn_within == index and self.up_btn_outer == self.btn_outer)) then
		self.btn_outer = 1
	end
	self.btn_within = index
	self:SetChooseTypeSort(sort)
	local obj = self.small_toggle_list[index].list_item_cell[self.btn_outer]
	obj:GetComponent("Toggle").isOn = true
	if self.big_toggle_list[index].accordion_element.isOn then
		if self.is_zidong then
			self.buff_cell_list[index][self.btn_outer]:OnClickItem(true)
			self.is_zidong = false
		end
	end
end

--内层按钮
function CultivationBuffView:OnClickProductHandler(cell_data)
	self.btn_outer = cell_data.client_sort
	self.is_can_flush = true
	if self.up_btn_outer == nil or self.up_btn_outer ~= self.btn_outer then
		self.up_btn_outer = self.btn_outer
	end

	self:SetChooseClientSort(cell_data.client_sort)

	-- 刷新右侧列表
	self:SetRightList()
end

function CultivationBuffView:SetChooseClientSort(value)
	self.choose_client_sort = value
end

function CultivationBuffView:GetChooseClientSort()
	return self.choose_client_sort
end

function CultivationBuffView:SetChooseTypeSort(value)
	self.choose_type_sort = value
end

function CultivationBuffView:GetChooseTypeSort()
	return self.choose_type_sort
end

function CultivationBuffView:XuanZeBtn()
    self:XuanZeOneBtn()
	-- if CultivationWGData.Instance:IsShowCultivationBuffParentRedPoint() == 1 then
	-- 	self:SkipView()
	-- else
	-- 	self:XuanZeOneBtn()
	-- end
end

--默认选中第一个下拉列表
function CultivationBuffView:XuanZeOneBtn()
	if not self.buff_cell_list[TOGGLE_MAX] then return end
	self.btn_within = 1
	self.btn_outer = 1
	local accordion_tab = CultivationWGData.Instance:GetAccordionTable()
	self.big_toggle_list[self.btn_within].accordion_element.isOn = true
	self:OnClickExpandHandler(self.btn_within, accordion_tab[self.btn_within].type_sort, false, true)
end

--判断按钮红点是否显示
function CultivationBuffView:IsBtnRedShow()
	local red_is_show = false
	local accordion_tab = CultivationWGData.Instance:GetAccordionTable()
	for i = 1, #self.big_toggle_list do
		-- local accor_data = nil
		if nil ~= accordion_tab[i] and nil ~= accordion_tab[i].child then
			self.big_toggle_list[i].list_item_cell:SetData(accordion_tab[i])
			-- accor_data = accordion_tab[i].child
			-- for k, v in pairs(accor_data) do
			-- 	if v.remind_tip > 0 then
			-- 		red_is_show = true
			-- 	end
			-- end
		end
	end
	if self.buff_cell_list[TOGGLE_MAX] then
		if not red_is_show then
			self:XuanZeBtn()
		else
			-- if accordion_tab ~= nil and not IsEmptyTable(accordion_tab) and self.btn_outer ~= nil and self.btn_within ~= nil then
			-- 	if accordion_tab[self.btn_within] ~= nil and accordion_tab[self.btn_within].child ~= nil and accordion_tab[self.btn_within].child[self.btn_outer] ~= nil then
			-- 		local child_data = accordion_tab[self.btn_within].child[self.btn_outer]
			-- 		if child_data ~= nil and child_data.remind_tip > 0 then
			-- 			return
			-- 		end
			-- 	end
			-- end

			self:SkipView()
		end
	end
end

--自动跳转界面判断
function CultivationBuffView:SkipView()
	if not self.buff_cell_list[TOGGLE_MAX] then return end
	local accordion_tab = CultivationWGData.Instance:GetAccordionTable()
	for i = 1, #self.big_toggle_list do
		local accor_data = nil
		if nil ~= accordion_tab[i] and nil ~= accordion_tab[i].child then
			accor_data = accordion_tab[i].child
			for k, v in pairs(accor_data) do
				if v.remind_tip > 0 then
					if i ~= self.btn_within then
						self.btn_within = i
						self.btn_outer = k
						self.is_zidong = true
						self.big_toggle_list[i].accordion_element.isOn = true
						self:CaseOnClickExpandHandler(i, accordion_tab[i].type_sort, false, true)
					elseif i == self.btn_within and self.btn_outer ~= k then
						self.btn_within = i
						self.btn_outer = k
						self.is_zidong = true
						self.big_toggle_list[i].accordion_element.isOn = true
						self:CaseOnClickExpandHandler(i, accordion_tab[i].type_sort, false, true)
					end
					return
				end
			end
		end
	end
	self.btn_outer = 1
end

----------------------------------------------------------------------------
-- 大类型toggle
--CultivationBuffBigTypeToggleRender
----------------------------------------------------------------------------
CultivationBuffTypeToggleRender = CultivationBuffTypeToggleRender or BaseClass(BaseRender)
function CultivationBuffTypeToggleRender:__init()

end

function CultivationBuffTypeToggleRender:__delete()
end

function CultivationBuffTypeToggleRender:OnFlush()
	self.node_list.text_normal.text.text = self.data.type_str
	self.node_list.text_hl.text.text = self.data.type_str
	self.node_list.text_num.text.text = CultivationWGData.Instance:GetExpAddCountByType(self.data.type_sort)
	self.node_list.remind:CustomSetActive(CultivationWGData.Instance:GetBuffRemindByType(self.data.type_sort))
end

function CultivationBuffTypeToggleRender:FlushData()
	self.node_list.text_num.text.text = CultivationWGData.Instance:GetExpAddCountByType(self.data.type_sort)
	self.node_list.remind:CustomSetActive(CultivationWGData.Instance:GetBuffRemindByType(self.data.type_sort))
end

----------------------------------------------------------------------------
-- 小类型toggle
-- CultivationBuffListItemRender
----------------------------------------------------------------------------
CultivationBuffListItemRender = CultivationBuffListItemRender or BaseClass(BaseRender)
function CultivationBuffListItemRender:__init()
end

function CultivationBuffListItemRender:__delete()
	self.parent_view = nil
end

function CultivationBuffListItemRender:LoadCallBack()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function CultivationBuffListItemRender:OnClickItem(is_on)
	if nil == is_on then return end
	if true == is_on then
		for k, v in pairs(self.parent_view.buff_cell_list) do
			for k1, v1 in pairs(v) do
				v1:OnSelectChange(false)
			end
		end
		self.parent_view:OnClickProductHandler(self.data) --最里层按钮点击
		self:OnSelectChange(true)
	else
		self:OnSelectChange(false)
	end
end

function CultivationBuffListItemRender:OnFlush()
	if self.data then
		self.node_list.text_name.text.text = self.data.client_type_str
		self.node_list.text_name_hl.text.text = self.data.client_type_str
		self.node_list.img_remind:CustomSetActive(CultivationWGData.Instance:GetBuffRemindBySubType(self.data.client_sort))
	end

end

function CultivationBuffListItemRender:FlushData()
	self.node_list.img_remind:CustomSetActive(CultivationWGData.Instance:GetBuffRemindBySubType(self.data.client_sort))
end

function CultivationBuffListItemRender:CreateSelectEffect()

end

function CultivationBuffListItemRender:OnSelectChange(is_select)
	if self.node_list["highlight"] and self.node_list["text_name"] then
		local name = self.node_list.text_name.text.text
		self.node_list.text_name.text.text = name
	end
end


-------------------------------------------------------------------------------------------------

CultivationBuffItemRender = CultivationBuffItemRender or BaseClass(BaseRender)

function CultivationBuffItemRender:LoadCallBack()

    XUI.AddClickEventListener(self.node_list.btn_receive,BindTool.Bind(self.OnClickBtnReceive, self))
end

function CultivationBuffItemRender:__delete()

end

function CultivationBuffItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end 
	--显示百分比
	if data.value_int == "" then
		self.node_list.text_add.text.text=(data.value_per*100).."%"
		self.node_list.text_add.text.color = Str2C3b("#fff8bb")
		local cur_bundle, cur_asset = ResPath.GetCultivationImg("a3_jj_wz_huang")
		self.node_list.img_bg.image:LoadSprite(cur_bundle, cur_asset, function ()
			self.node_list.img_bg.image:SetNativeSize()
		end)
		self.node_list.text_type.text.text = Language.Cultivation.ExpAddType2
	else
		self.node_list.text_add.text.text = data.value_int
		self.node_list.text_add.text.color = Str2C3b("#cbfbfd")
		self.node_list.text_type.text.text = Language.Cultivation.ExpAddType1
	end
	
	local name, desc = CultivationWGData.Instance:GetCorrectNameBySubType(data.type)
	self.node_list.text_name.text.text = name
	local new_desc, is_rich = self:GetDesc(desc)
	self.node_list.text_target.text.text = new_desc
	
	if CultivationWGData.Instance:IsReceivedBuffByIndex(data.type, data.index) then
		self.node_list.btn_receive:CustomSetActive(false)
		self.node_list.img_received:CustomSetActive(true)
	else
		self.node_list.btn_receive:CustomSetActive(true)
		self.node_list.img_received:CustomSetActive(false)
		XUI.SetButtonEnabled(self.node_list.btn_receive, is_rich)
	end
	
	
end

-- 领取
function CultivationBuffItemRender:OnClickBtnReceive()
	if self.data then
		CultivationWGCtrl.Instance:ReceiveCultivationExpAdd(self.data.type, self.data.index)
	end
end

-- 获取描述,条件是否满足判断 不同类型 获取不同数据
function CultivationBuffItemRender:GetDesc(desc)
	local is_rich = false
	local new_desc = ""
	local color_text = ""
	if self.data then
		local type = self.data.type

		if type == CULTIVATION_EXP_ADD_TYPE.ROLE_LEVEL then
			local level = RoleWGData.Instance:GetRoleLevel()
			is_rich = level >=self.data.target
			color_text = self:SetDescColor(level, self.data.target, is_rich)
		-- elseif type == CULTIVATION_EXP_ADD_TYPE.WUHUN then
		-- 	local count = WuHunWGData.Instance:GetCountByLevel(self.data.target)
		-- 	is_rich = count >= self.data.target_param
		-- 	color_text = self:SetDescColor(count, self.data.target_param, is_rich )
		-- 	new_desc = string.format(desc, self.data.target_param, self.data.target, color_text)

		elseif type >= CULTIVATION_EXP_ADD_TYPE.EQUIP and type <= CULTIVATION_EXP_ADD_TYPE.MONSTER then
			local capability = CultivationWGData.Instance:GetCapability(type)
			is_rich = capability >=self.data.target
			color_text = self:SetDescColor(CommonDataManager.ConverNumber(capability), CommonDataManager.ConverNumber(self.data.target), is_rich )

		elseif type == CULTIVATION_EXP_ADD_TYPE.APPEARANCE then
			local quality = self.data.target
			local need_count = self.data.target_param
			local active_count = NewAppearanceWGData.Instance:GetActivaCountByQuality(quality)
			is_rich = active_count >= need_count
			color_text = self:SetDescColor(active_count, need_count, is_rich )
			new_desc = string.format(desc,Language.Cultivation.QualityName[quality], need_count, color_text)

		elseif type == CULTIVATION_EXP_ADD_TYPE.VIP_LEVEL then
			local level = VipWGData.Instance:GetRoleVipLevel()
			is_rich = level >=self.data.target
			color_text = self:SetDescColor(level, self.data.target, is_rich )
		end
		

		if type ~= CULTIVATION_EXP_ADD_TYPE.APPEARANCE then
			if type >= CULTIVATION_EXP_ADD_TYPE.EQUIP and type <= CULTIVATION_EXP_ADD_TYPE.MONSTER then 
				new_desc = string.format(desc, CommonDataManager.ConverNumber(self.data.target), color_text)
			else
				new_desc = string.format(desc, self.data.target, color_text)
			end
		end
		return new_desc, is_rich
	end
	return new_desc, is_rich
end

function CultivationBuffItemRender:SetDescColor(cur_value, target_value, is_rich)
	return string.format("<color=%s>%s/%s</color>",is_rich and "#99ffbb" or "#ff9292" , cur_value,target_value)
end