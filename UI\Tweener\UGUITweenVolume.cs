﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[AddComponentMenu("UGUI/Tween/UGUI Tween Volume")]
[RequireComponent(typeof(AudioSource))]
public class UGUITweenVolume : UGUITweener {

	[Range(0f, 1f)] public float from = 1f;
	[Range(0f, 1f)] public float to = 1f;

	AudioSource mSource;

	public AudioSource audioSource
	{
		get
		{
			if (mSource == null)
			{
				mSource = GetComponent<AudioSource>();

				if (mSource == null)
				{
					mSource = GetComponent<AudioSource>();

					if (mSource == null)
					{
						Debug.LogError("TweenVolume needs an AudioSource to work with", this);
						enabled = false;
					}
				}
			}
			return mSource;
		}
	}

	[System.Obsolete("Use 'value' instead")]
	public float volume { get { return this.value; } set { this.value = value; } }

	public float value
	{
		get
		{
			return audioSource != null ? mSource.volume : 0f;
		}
		set
		{
			if (audioSource != null) mSource.volume = value;
		}
	}

	protected override void OnUpdate (float factor, bool isFinished)
	{
		value = from * (1f - factor) + to * factor;
		mSource.enabled = (mSource.volume > 0.01f);
	}

	static public UGUITweenVolume Begin (GameObject go, float duration, float targetVolume)
	{
		UGUITweenVolume comp = UGUITweener.Begin<UGUITweenVolume>(go, duration);
		comp.from = comp.value;
		comp.to = targetVolume;

		if (targetVolume > 0f)
		{
			var s = comp.audioSource;
			s.enabled = true;
			s.Play();
		}
		return comp;
	}

	public override void SetStartToCurrentValue () { from = value; }
	public override void SetEndToCurrentValue () { to = value; }
}
