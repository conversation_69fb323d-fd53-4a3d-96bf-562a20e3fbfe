NewFestivalConsumeRebateWGData = NewFestivalConsumeRebateWGData or BaseClass()
function NewFestivalConsumeRebateWGData:__init()
	if NewFestivalConsumeRebateWGData.Instance then
		ErrorLog("[NewFestivalConsumeRebateWGData] Attemp to create a singleton twice !")
	end

	NewFestivalConsumeRebateWGData.Instance = self
end

function NewFestivalConsumeRebateWGData:__delete()
    NewFestivalConsumeRebateWGData.Instance = nil
end

function NewFestivalConsumeRebateWGData:SetConsumeRebateInfo(protocol)
    self.rebate_reward = protocol.rebate_reward
end

function NewFestivalConsumeRebateWGData:GetConsumeRebateNum()
    local rebate_reward = 0
    rebate_reward = self.rebate_reward
    return rebate_reward
end


