DujieReceiveInviteView = DujieReceiveInviteView or BaseClass(SafeBaseView)
function DujieReceiveInviteView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel") --, {vector2 = Vector2(0, 0), sizeDelta = Vector2(556, 366)}
    self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_receive_invite_view")
end

function DujieReceiveInviteView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickBtnCancel, self))
	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnClickBtnOK, self))

    if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_scroll)
	end
    
    self.node_list.title_view_name.text.text = Language.Dujie.IviteTitle2
end


function DujieReceiveInviteView:ReleaseCallBack()

	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end


function DujieReceiveInviteView:CloseCallBack()
    if self.node_list and self.node_list.common_checkbox_and_text then
        if self.node_list.common_checkbox_and_text.toggle.isOn then
            local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
            PlayerPrefsUtil.SetInt("dujie_receive_tips", open_day)
        end
    end

end

function DujieReceiveInviteView:OnFlush()
    self.node_list.rich_dialog.text.text = string.format(Language.Dujie.InviteName,self.data.invite_name)

    local invite_show_reward = DujieWGData.Instance:GetOtherCfg("invite_show_reward")
    self.reward_item_list:SetDataList(invite_show_reward)
    
end

function DujieReceiveInviteView:SetData(data)
    self.data = data
end

function DujieReceiveInviteView:OnClickBtnCancel()
    self:Close()
end



function DujieReceiveInviteView:OnClickBtnOK()
    DujieWGCtrl.Instance:GotoDujieArea()
    self:Close()
end

