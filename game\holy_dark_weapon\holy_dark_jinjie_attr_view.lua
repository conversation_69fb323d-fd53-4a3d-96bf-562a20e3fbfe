HolyDarkJinJieAttrView = HolyDarkJinJieAttrView or BaseClass(SafeBaseView)

function HolyDarkJinJieAttrView:__init()
    self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "holy_dark_jinjie_attr")
end

function HolyDarkJinJieAttrView:LoadCallBack()
    self.cur_attr_list = {}
	self.next_attr_list = {}
    for i = 1, 5 do
		self.cur_attr_list[i] = HolyDarkJinJieAttrRender.New(self.node_list.layout_cur_attr_add:FindObj("rich_cur_" .. i))
		self.next_attr_list[i] = HolyDarkJinJieAttrRender.New(self.node_list.layout_next_attr_add:FindObj("rich_next_" .. i))
	end

    self.node_list["left_active_btn"].button:AddClickListener(BindTool.Bind(self.OnClickActive, self))
end

function HolyDarkJinJieAttrView:ReleaseCallBack()
    if self.cur_attr_list then
		for k,v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
	    self.cur_attr_list = nil
	end

	if self.next_attr_list then
		for k,v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
	    self.next_attr_list = nil
	end

    self.item_data = nil

end

function HolyDarkJinJieAttrView:SetDataAndOpen(item_data)
    self.item_data = item_data
    self:Open()
end

function HolyDarkJinJieAttrView:OnClickActive()
    self:Close()
end

function HolyDarkJinJieAttrView:OnFlush()
    if not self.item_data then
        return
    end

    local cur_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeAttrList(self.item_data.relic_seq, self.item_data.slot_index, self.item_data.grade)
    local next_attr_list = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeAttrList(self.item_data.relic_seq, self.item_data.slot_index, self.item_data.grade + 1)

    self.node_list["no_attr_tip"]:SetActive(IsEmptyTable(cur_attr_list))
    self.node_list["max_attr_tip"]:SetActive(IsEmptyTable(next_attr_list))
    self.node_list["now_level"].text.text = string.format(Language.HolyDarkWeapon.JieNum, self.item_data.grade)
    self.node_list["next_level"].text.text = IsEmptyTable(next_attr_list) and 
                                    Language.HolyDarkWeapon.MaxGrade or string.format(Language.HolyDarkWeapon.JieNum, self.item_data.grade + 1)

    for i = 1, 5 do
        self.cur_attr_list[i]:SetData(cur_attr_list[i])
		self.next_attr_list[i]:SetData(next_attr_list[i])
    end
end

HolyDarkJinJieAttrRender = HolyDarkJinJieAttrRender or BaseClass(BaseRender)
function HolyDarkJinJieAttrRender:OnFlush()
	if not IsEmptyTable(self.data) then
        local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
        local per_desc = is_per and "%" or ""
        local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
        self.node_list.name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
        self.node_list.value.text.text = string.format("%s%s", value_str, per_desc)
        self.view:SetActive(true)
	else
		self.view:SetActive(false)
	end
end