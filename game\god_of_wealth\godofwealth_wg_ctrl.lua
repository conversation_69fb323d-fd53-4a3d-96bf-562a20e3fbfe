require("game/god_of_wealth/godofwealth_view")
require("game/god_of_wealth/godofwealth_blessing")
require("game/god_of_wealth/godofwealth_luckystar_view")
require("game/god_of_wealth/godofwealth_wg_data")
require("game/god_of_wealth/godofwealth_luckystar_wg_ctrl")
require("game/god_of_wealth/godofwealth_luckystar_wg_data")
require("game/god_of_wealth/yijianzhaocai_view")
require("game/god_of_wealth/godofwealth_records_tip")
GodOfWealthWGCtrl = GodOfWealthWGCtrl or BaseClass(BaseWGCtrl)

function GodOfWealthWGCtrl:__init()
    if GodOfWealthWGCtrl.Instance then
        error("[GodOfWealthWGCtrl]:Attempt to create singleton twice!")
    end
    GodOfWealthWGCtrl.Instance = self

    self.data = GodOfWealthWGData.New()
    self.view = GodOfWealthView.New(GuideModuleName.GodOfWealthView)
    self.yijian_view = YiJianZhaoCaiView.New(GuideModuleName.YiJianZhaoCaiView)
    self.blessing_view = GodOfWealthBlessingView.New()
    --self.luckkystar_view = GodOfWealthLuckyStarView.New()
    self.records_tip_view = GodOfWealthRecordsTipView.New()

    self:RegisterProtocol(CSWealthGodOperate)
    self:RegisterProtocol(CSMtWealthGodExtendReward)
    self:RegisterProtocol(SCWealthGodInfo, "OnSCWealthGodInfo")
    self:RegisterProtocol(SCWealthGodPoolInfo, "OnSCWealthGodPoolInfo")
    self:RegisterProtocol(SCWealthGodRankInfo, "OnSCWealthGodRankInfo")
    self:RegisterProtocol(SCMtWealthGoldExtendRewardInfo, "OnSCMtWealthGoldExtendRewardInfo")

    -- 天数改变
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayChange, self))
    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER,
    BindTool.Bind(self.OpenFunEventChange, self))

    self:InitLuckyDrawCtrl()
end

function GodOfWealthWGCtrl:__delete()
    GodOfWealthWGCtrl.Instance = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.yijian_view then
        self.yijian_view:DeleteMe()
        self.yijian_view = nil
    end

    if self.blessing_view then
        self.blessing_view:DeleteMe()
        self.blessing_view = nil
    end

    if self.records_tip_view then
        self.records_tip_view:DeleteMe()
        self.records_tip_view = nil
    end

    GlobalEventSystem:UnBind(self.open_fun_change)
    self:DeleteLuckyDrawCtrl()
end

function GodOfWealthWGCtrl:SendOperaReq(operate_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSWealthGodOperate)
    protocol.operate_type = operate_type or 0
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol.param_3 = param_3 or 0
    protocol:EncodeAndSend()
end

function GodOfWealthWGCtrl:SendAllGetReward()
    local protocol = ProtocolPool.Instance:GetProtocol(CSMtWealthGodExtendReward)
    protocol:EncodeAndSend()
end

function GodOfWealthWGCtrl:OnSCWealthGodInfo(protocol)
    self.data:SetWealthGodInfo(protocol)
    RemindManager.Instance:Fire(RemindName.Wealth_god_fanyu)

    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.blessing_view:IsOpen() then
        self.blessing_view:Flush()
    end

    RechargeVolumeWGCtrl.Instance:FlushRechargeVolumeView()
end

function GodOfWealthWGCtrl:OnSCWealthGodPoolInfo(protocol)
    local pool_summary_time = self.data:GetPoolSummaryTime()
    local time_change_flag = pool_summary_time ~= protocol.pool_summary_time
    self.data:SetWealthGodPoolInfo(protocol)

    if self.view:IsOpen() then
        self.view:Flush()

        if time_change_flag then
            self.view:SetRewardPoolTime()
        end
    end
end

function GodOfWealthWGCtrl:OnSCWealthGodRankInfo(protocol)
    self.data:SetWealthGodRankInfo(protocol)

    if self.view:IsOpen() then
        self.view:Flush()
    end
end

function GodOfWealthWGCtrl:OnSCMtWealthGoldExtendRewardInfo(protocol)
    self.data:SetAllBuyRewardFlag(protocol)
    RemindManager.Instance:Fire(RemindName.Wealth_god_fanyu)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    RechargeVolumeWGCtrl.Instance:FlushRechargeVolumeView()
end

-- 抽奖结果返回
function GodOfWealthWGCtrl:OnWarlthGodDraw(protocol)
    --// 喜迎财神抽奖 result(1 succ, 0 fail) param1=draw_time param2=per --包含这次已经抽奖次数result      倍数param2
    if protocol.result == 1 then
        self.data:SetWarlthGodDraw(protocol)
        self.view:ReceiveProtocolPlayAnim()
    else
        GodOfWealthWGData.Instance:SetDrawState(false)
        self.view:FlushPer()
    end
end

function GodOfWealthWGCtrl:SetDelayShowGetGold(num)
    local delay_time = 5
    local item_cfg = ItemWGData.Instance:GetItemConfig(65534)
    local str = string.format(Language.Bag.GetItemTxt, item_cfg.name, num)

    if self.data:GetSkipAniFlag() then
        SysMsgWGCtrl.Instance:ErrorRemind(str)
    else
        TryDelayCall(self, function()
            SysMsgWGCtrl.Instance:ErrorRemind(str)
        end, delay_time, "god_of_wealth_delay_get_gold")
    end
end

function GodOfWealthWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all then
        is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.GodOfWealthView)
    end

    if check_all or fun_name == FunName.GodOfWealthView then
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GOD_OF_WEALTH, state)
    end
end

function GodOfWealthWGCtrl:OnDayChange()
    if FunOpen.Instance:GetFunIsOpened(GuideModuleName.GodOfWealthView) then
        --if self.data:IsCanShowRechargePanel() then
            if self.view:IsOpen() then
                self.view:Flush()
            end

            if self.blessing_view:IsOpen() then
                self.blessing_view:Flush()
            end
        end
    --end
end

function GodOfWealthWGCtrl:OpenBlessingView()
    self.blessing_view:Open()
end

function GodOfWealthWGCtrl:OpenRecordsTipView()
    self.records_tip_view:Open()
end

-----------------------------------福星高照--------------------------------
function GodOfWealthWGCtrl:OpenLuckyStarView()
    if self.luckkystar_view and not self.luckkystar_view:IsOpen() then
        self.luckkystar_view:Open()
    end
end

function GodOfWealthWGCtrl:FlushLuckyStarView()
    if self.luckkystar_view and self.luckkystar_view:IsOpen() then
        self.luckkystar_view:Flush()
    end
end
