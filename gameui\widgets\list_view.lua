-----------------------------------------------------
--基础list组件
--提供增删等基础操作，结合ListViewItem 操作
--1.CreateView 创建list对象，并提供item渲染器
--2.设置数据源SetDataList
----------------------------------------------------
ListView = ListView or BaseClass()

ListView.Top = 0
ListView.Right = 1
ListView.Bottom = 2
ListView.Left = 3

XuiListEventType = {
	Began = 0,
	Refresh = 1,
	Ended = 2,
	Canceled = 3,
}

local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

function ListView:__init(item_render, list_view)
	self.items = {}
	self.data_list = {}
	self.list_view = nil
	self.item_render = nil
	self.cur_select_item = nil
	self.select_callback = nil

	self.ui_config = nil
	self.jump_top_index = nil
	self.jump_horizontal = false
	self.jump_direction = nil						-- 跳转方向
	self.default_index = nil
	self.refresh_is_asc = true						-- 是否升序刷新
	self.is_use_step_calc = true 					-- 默认使用分步计算
	self.need_set_clicklistener = false
	self.first_time_load = true
	self.cell_list = {}

	self.select_index = nil
	self.select_tab = {}

	self.is_show_item_special_title_ui = false

	if item_render and list_view then
		self.item_render = item_render
		self.list_view = list_view
	end

end

function ListView:__delete()
	for k, v in pairs(self.items) do
		v:DeleteMe()
	end
	self.items = {}

	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
	self.select_callback = nil
	self.is_show_item_special_title_ui = false
end

function ListView:GetView()
	return self.list_view
end

-- 根据参数构建基础list显示对象
-- @param_t 结构如 {width=100, height=100, direction=1, itemRender=ListViewItem, gravity=2, bounce=true}
function ListView:CreateView(t)
	return self:Create(t.itemRender,t.ui_config)
end

function ListView:Create(item_render, list_view)

	if nil ~= self.list_view then
		return
	end

	self.item_render = item_render
	self.list_view = list_view

end

-- 获得数据源
function ListView:GetDataList()
	return self.data_list
end

-- 设置数据源
function ListView:SetData(data_list, refresh, percent)
	if nil == refresh then
		refresh = 0
	end
	self:SetDataList(data_list, refresh, percent)
end

-- 设置数据源
-- 0 刷新跳到顶部  1 刷新调到底部  2 刷新可视格子  3 刷新和重新加载listview
function ListView:SetDataList(data_list, refresh_type, percent)
	self.data_list = data_list
	if self.first_time_load then
		local list_delegate = self.list_view.list_simple_delegate
		list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetListViewNumbers, self)
		list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshListViewCells, self)
		self.first_time_load = false
	end

	self:__DoRefresh(refresh_type, percent)
end

-- 0 刷新跳到顶部  1 刷新调到底部  2 刷新可视格子  3 刷新和重新加载listview
function ListView:__DoRefresh(refresh_type, percent)
	percent = percent or 0
	if refresh_type == 0 then
		self.list_view.scroller:ReloadData(percent)
	elseif refresh_type == 1 then
		self.list_view.scroller:ReloadData(1)
	elseif refresh_type == 2 then
		self.list_view.scroller:RefreshActiveCellViews()
	elseif refresh_type == 3 then
		if self.list_view.scroller.isActiveAndEnabled then
			self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
		end
	end

	if is_develop then
		self:__CheckRefreshCall()
	end
end

function ListView:__CheckRefreshCall()
	local now_frame = UnityEngine.Time.frameCount
	self.last_refresh_frame = self.last_refresh_frame or now_frame
	self.refresh_times_in_frame = self.refresh_times_in_frame or 0
	if self.last_refresh_frame ~= now_frame then
		self.last_refresh_frame = now_frame
		self.refresh_times_in_frame = 1
	else
		self.refresh_times_in_frame = self.refresh_times_in_frame + 1
		if self.refresh_times_in_frame > 1 then
			print_error(string.format("[ListView] 在1帧里执行了%s次__DoRefresh，请整理代码或者改用AsyncListView", self.refresh_times_in_frame))
		end
	end
end

function ListView:SetCellSizeDel(callback)
	-- body
	if not self.list_view then return end
	local list_delegate = self.list_view.list_simple_delegate
	list_delegate.CellSizeDel = callback
end

--更换index
function ListView:ChangeDefaultIndex()
	self.default_index = self.select_index or 1
end

--获得格子数
function ListView:GetListViewNumbers()
	return #self.data_list
end

--刷新格子
function ListView:RefreshListViewCells(cell, cell_index)
	local item_cell = self.cell_list[cell]
	cell_index = cell_index + 1
	if not item_cell then
		local toggle = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Toggle))
		local button = cell.gameObject:GetComponent(typeof(UnityEngine.UI.Button))
		item_cell = self.item_render.New(cell.gameObject)
		self.cell_list[cell] = item_cell
		if toggle then
			item_cell:SetToggleGroup(self.list_view.toggle_group)
			toggle:AddClickListener(BindTool.Bind(self.ListEventCallback, self, item_cell))
		elseif button then
			button:AddClickListener(
				BindTool.Bind(self.ListEventCallback, self, item_cell))
		end

		if self.is_show_item_special_title_ui then
			item_cell:IsShowSpecialTitleUI(true)
		end
	end
	local item_data = self.data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetData(item_data)

	if self.default_index and self.default_index == cell_index then
		self.default_index = nil
		self:ListEventCallback(item_cell, true)
	end
	item_cell:SetSelectIndex(self.select_index)

	if self.need_set_clicklistener and self.select_index == cell_index then --新增调用
		self:ListEventCallbackNew(item_cell)
	end
	if self.refresh_callback then
		self.refresh_callback(item_cell, cell_index)
	end
end

--list刷新回调
function ListView:SetRefreshCallback(refresh_callback)
	self.refresh_callback = refresh_callback
end

--list事件回调
function ListView:ListEventCallback(item_cell, is_default)
	self.select_index = item_cell:GetIndex()
	if self.select_callback then
		self.select_callback(item_cell, self.select_index, is_default, true)
		item_cell:ChangeSelectIndex(self.select_index)
	end

	self:__DoRefresh(2)
	if self.jump_direction then
		self:AutoJump()
	end
end

-- 设置当前选中index，有回调
function ListView:SelectIndex(index)
	local item = self:GetItemAt(index)
	self:SetSelectItem(item)

	if item ~= nil and self.select_callback ~= nil then
		self.select_callback(item, index)
	end
end

--设置当前选中item
function ListView:SetSelectItem(item)
	if item == nil then return end
	self.select_index = item:GetIndex()
	self:__DoRefresh(2)
end

-- 获得数据源
function ListView:GetData()
	return self.data_list
end

-- 设置选中回调函数
function ListView:SetSelectCallBack(callback)
	self.select_callback = callback
end

--再看怎么改
function ListView:RemoveAt(index)
	-- if index <= 0 then
	-- 	return
	-- end

	-- local item = self:GetItemAt(index)
	-- if nil == item then
	-- 	return
	-- end

	-- if self.cur_select_item == item then
	-- 	self.cur_select_item = nil
	-- end

	-- self.list_view:removeItemByIndex(index - 1)
	-- item:DeleteMe()

	-- table.remove(self.items, index)
end

function ListView:RemoveItem(item)
	-- self:RemoveAt(self:GetItemIndex(item))
end

-- 获得某个索引下的item (cell复用导致index错乱 慎用！)
function ListView:GetItemAt(index)
	for k, v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy and v:GetIndex() == index then
			return v
		end
	end
	return nil
end

--获取当前选中的格子的数据
function ListView:GetCurItemData()
	return self.data_list[self:GetSelectIndex()]
end

-- 获得item在列表中的索引(cell复用导致index错乱 慎用！)
function ListView:GetItemIndex(item_cell)
	for k, v in pairs(self.cell_list) do
		if v == item_cell then
			return v:GetIndex()
		end
	end
end

-- 当面板能看到全部item的时候才用
function ListView:GetAllItems()
	local item_tab = {}
	for k, v in pairs(self.cell_list) do
		if v.view.gameObject.activeInHierarchy then
			item_tab[v:GetIndex()] = v
		end
	end
	return item_tab
end

-- 清除所有item
function ListView:RemoveAllItem()
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
	self.cur_select_item = nil
end

-- 设置默认选中index
function ListView:SetDefaultSelectIndex(index)
	self.default_index = index
end

-- 设置当前选中index，无回调
--
function ListView:ChangeToIndex(index)
	self:SetSelectItem(self:GetItemAt(index))
end

--获得当前选择的index (GetSelectItem 合到这里)
function ListView:GetSelectIndex()
	return self.select_index
end

function ListView:SetJumpHorizontal(boo)
	self.jump_horizontal = boo
end

-- 第index项置顶（竖）
function ListView:SetSelectItemToTop(index)
	self.jump_top_index = index
	local remove_tab = table.remove(self.data_list, index)
	table.insert(self.data_list, 1, remove_tab)
end

-- 取消选中
function ListView:CancelSelect()
	self.select_index = nil
	self:__DoRefresh(2)
end

-- 单元间隔
function ListView:SetItemsInterval(interval)
	-- self.list_view:setItemsInterval(interval)
end

-- 获取item数量
function ListView:GetCount()
	return #self.data_list
end

-- 刷新某个格子里的数据
function ListView:UpdateOneCell(index, data)
	for k, v in pairs(self.cell_list) do
		if v:GetIndex() == index then
			v:SetData(data)
		end
	end
	self.data_list[index] = data
end

-- 移动第一条到最后
function ListView:MoveFrontToLast()
	local remove_tab = table.remove(self.data_list, 1)
	table.insert(self.data_list, remove_tab)
	self:__DoRefresh(0)
end

function ListView:JumpToTop()
	self:__DoRefresh(0)
end

-- 设置跳转方向，设置之后每次刷新都会自动跳转
function ListView:SetJumpDirection(jump_direction)
	self.jump_direction = jump_direction
	self:AutoJump()
end

-- 自动跳转，SetJumpDirection之后才有效
function ListView:AutoJump()
	if nil == self.list_view then
		return
	end

	if nil ~= self.jump_top_index then
		self:SetSelectItemToTop(self.jump_top_index)
		return
	end

	if self.jump_direction == ListView.Top or self.jump_direction == ListView.Left then
		self:__DoRefresh(0)
	elseif self.jump_direction == ListView.Bottom or self.jump_direction == ListView.Right then
		self:__DoRefresh(1)
	end
end

--跳转到某个cell 新增
function ListView:JumpToIndex(index , min_show_len)
	local cell = self:GetItemAt(index)
	self.need_set_clicklistener = true
	self.select_index = index
	local cell_is_active = cell and cell.view and cell.view.gameObject.activeInHierarchy

	if (min_show_len == nil or index - min_show_len < 1) and cell_is_active then--表示该数据可见

	else
		local percent = 0
		local min = min_show_len or 4
		if index > min then
			local len = self:GetListViewNumbers()
			len = len == 0 and 1 or len
			percent = index / len
			self:__DoRefresh(0, percent)
		else
			self:__DoRefresh(0)
		end
	end

	--???为什么这里还要再进行全部刷新？？？
	if self.list_view.scroller.isActiveAndEnabled then
		self:__DoRefresh(3)
	end
end

-- 新增
function ListView:JustSetIndex( index )
	self.select_index = index
end

function ListView:ListEventCallbackNew( item_cell )
	if self.select_callback then
		self.select_callback(item_cell, self.select_index, nil, true)
	end
	self.need_set_clicklistener = false
end

--ItemCell专用，设置特殊title ui
function ListView:SetIsShowItemSpecialTitleUI(is_show_item_special_title_ui)
	self.is_show_item_special_title_ui = is_show_item_special_title_ui
end