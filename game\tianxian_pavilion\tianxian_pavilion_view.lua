TianxianPavilionView = TianxianPavilionView or BaseClass(SafeBaseView)

function TianxianPavilionView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true


    self:AddViewResource(0, "uis/view/tianxian_pavilion_ui_prefab", "layout_tianxian_pavilion")
end

function TianxianPavilionView:ReleaseCallBack()
    self.select_idx = nil

    if self.show_toggle_list then
        self.show_toggle_list:DeleteMe()
        self.show_toggle_list = nil
    end

    if self.convert_grid_list then
        self.convert_grid_list:DeleteMe()
        self.convert_grid_list = nil
    end

    if self.display_model then
        self.display_model:DeleteMe()
        self.display_model = nil
    end

    if CountDownManager.Instance:HasCountDown("tianxian_end_time") then
        CountDownManager.Instance:RemoveCountDown("tianxian_end_time")
    end
end

function TianxianPavilionView:LoadCallBack()
    self:FlushEndTime()
    self.select_idx = 1
    self.is_now_img = true

    if not self.show_toggle_list then
        self.show_toggle_list = AsyncListView.New(ShowToggleCell, self.node_list.show_list)
        self.show_toggle_list:SetSelectCallBack(BindTool.Bind(self.OnClickToggleCell, self))

        local data_list = TianxianPavilionWGData.Instance:GetRoundInterfaceCfg()
        if not IsEmptyTable(data_list) then
            self.show_toggle_list:SetDataList(data_list)
        end
    end

    if not self.convert_grid_list then
        self.convert_grid_list = AsyncBaseGrid.New()
        local bundle = "uis/view/tianxian_pavilion_ui_prefab"
        local asset = "convert_render"
        self.convert_grid_list:CreateCells({
            col = 3,
            change_cells_num = 1,
            list_view = self.node_list["convert_list"],
            assetBundle = bundle,
            assetName = asset,
            itemRender = ConvertItemCell,
        })
        self.convert_grid_list:SetStartZeroIndex(false)
    end

    self.node_list.go_get_prop.button:AddClickListener(BindTool.Bind(self.OnClickGetProp, self))
    self.node_list.preview_btn.button:AddClickListener(BindTool.Bind(self.OnClickHeraid, self))
    for i = 1, 2 do
        self.node_list["prop_tip_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickItemTip, self, i))
    end

    self:InitModel()
    self:InitImage()
end

function TianxianPavilionView:FlushEndTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local next_time = TianxianPavilionWGData.Instance:GetEndTime()
    local time = next_time - server_time

    if CountDownManager.Instance:HasCountDown("tianxian_end_time") then
        CountDownManager.Instance:RemoveCountDown("tianxian_end_time")
    end

    if time > 0 then
        CountDownManager.Instance:AddCountDown("tianxian_end_time",
            BindTool.Bind(self.UpdateCountDown, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function TianxianPavilionView:InitModel()
    if not self.display_model then
        self.display_model = OperationActRender.New(self.node_list["display_model"])
        self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end
end

function TianxianPavilionView:InitImage()
    local heraid_cfg = TianxianPavilionWGData.Instance:GetHeraidCfg()
    if not IsEmptyTable(heraid_cfg) then
        local heraid_bundle, heraid_asset = ResPath.GetRawImagesPNG(heraid_cfg.now_round_bg)
        self.node_list.top_bg.raw_image:LoadSprite(heraid_bundle, heraid_asset, function()
        end)
    end
end

function TianxianPavilionView:OnFlush()
    local show_list = TianxianPavilionWGData.Instance:GetConvertShowCfg(self.select_idx)
    if not IsEmptyTable(show_list) then
        self.convert_grid_list:SetDataList(show_list)
    end

    local show_info = TianxianPavilionWGData.Instance:GetInterfaceCfg(self.select_idx)
    if not IsEmptyTable(show_info) then
        for i = 1, 2 do
            local prop_item = show_info["prop_" .. i]
            if prop_item ~= "" and prop_item ~= 0 then
                local item_bundle, item_asset = ItemWGData.Instance:GetTipsItemIcon(prop_item)

                self.node_list["prop_icon_" .. i].image:LoadSprite(item_bundle, item_asset, function()
                    self.node_list["prop_icon_" .. i].image:SetNativeSize()
                end)

                local prop_num = ItemWGData.Instance:GetItemNumInBagById(prop_item)
                self.node_list["prop_num_" .. i].text.text = prop_num
            end
        end
    end

    self:FlushModel()
end

function TianxianPavilionView:FlushModel()
    local show_info = TianxianPavilionWGData.Instance:GetInterfaceCfg(self.select_idx)
    if IsEmptyTable(show_info) then
        return
    end

    local display_data = {}
    display_data.should_ani = true
    if show_info.model_show_itemid ~= 0 and show_info.model_show_itemid ~= "" then
        local split_list = string.split(show_info.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = show_info.model_show_itemid
        end
    end

    display_data.bundle_name = show_info["model_bundle_name"]
    display_data.asset_name = show_info["model_asset_name"]
    local model_show_type = tonumber(show_info["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

    self.display_model:SetData(display_data)
    local scale = show_info["display_scale"]
    Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
    if show_info.display_pos and show_info.display_pos ~= "" then
        local pos_list = string.split(show_info.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.display_model.rect, pos_x, pos_y)

    if show_info.rotation and show_info.rotation ~= "" then
        local rotation_tab = string.split(show_info.rotation, "|")
        self.node_list["display_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
            rotation_tab[3])
    end
end

function TianxianPavilionView:OnClickToggleCell(cell)
    local index = cell:GetIndex()
    if self.select_idx == index then
        return
    end

    self.select_idx = index
    self:Flush()
end

function TianxianPavilionView:OnClickGetProp()
    TianxianPavilionWGCtrl.Instance:OpenTaskView()
end

function TianxianPavilionView:OnClickHeraid()
    TianxianPavilionWGCtrl.Instance:OpenHeraidView()
end

function TianxianPavilionView:OnClickItemTip(index)
    local show_info = TianxianPavilionWGData.Instance:GetInterfaceCfg(self.select_idx)
    if not IsEmptyTable(show_info) then
        local prop_item = show_info["prop_" .. index]
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = prop_item })
    end
end

function TianxianPavilionView:UpdateCountDown(elapse_time, total_time)
    local time = math.ceil(total_time - elapse_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["activity_time"].text.text = string.format(Language.TianxianText.TianxianActTime, time_str)

    if self.is_now_img and time < 60 * 60 * 24 then
        self.is_now_img = false
        self.node_list.preview_btn:SetActive(true)
        local heraid_cfg = TianxianPavilionWGData.Instance:GetHeraidCfg()
        if not IsEmptyTable(heraid_cfg) and self.node_list and self.node_list.top_bg then
            local heraid_bundle, heraid_asset = ResPath.GetRawImagesPNG(heraid_cfg.next_round_bg)
            self.node_list.top_bg.raw_image:LoadSprite(heraid_bundle, heraid_asset, function()

            end)
        end
    end
end

function TianxianPavilionView:OnComplete()
    self.node_list.activity_time.text.text = ""
    self:Close()
end

--------------------ShowToggleCell-----------------------
ShowToggleCell = ShowToggleCell or BaseClass(BaseRender)
function ShowToggleCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local tab_name = self.data.tab_name
    self.node_list.normal_lbl.text.text = tab_name
    self.node_list.hight_lbl.text.text = tab_name
end

function ShowToggleCell:OnSelectChange(is_select)
    self.node_list.hight_bg:SetActive(is_select)
end

--------------------ConvertItemCell--------------------
ConvertItemCell = ConvertItemCell or BaseClass(BaseRender)
function ConvertItemCell:LoadCallBack()
    self.node_list.btn_exchange.button:AddClickListener(BindTool.Bind(self.OnClickExchange, self))

    if not self.item_info then
        self.item_info = ItemCell.New(self.node_list.item)
        self.item_info:SetIsUseRoundQualityBg(true)
        self.item_info:SetEffectRootEnable(false)
        self.item_info:SetCellBgEnabled(false)
    end
end

function ConvertItemCell:__delete()
    if self.item_info then
        self.item_info:DeleteMe()
        self.item_info = nil
    end
end

function ConvertItemCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:FlushPropsNum()
    self:FlushCanExchange()
    local reward_item = self.data.reward_item
    local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item[0].item_id)
    if not IsEmptyTable(item_cfg) then
        self.item_info:SetData({ item_id = reward_item[0].item_id })

        local effect_name = BaseCell_Ui_Circle_Effect[item_cfg.color]
        if effect_name then
            local bundle1, assert1 = ResPath.GetWuPinKuangEffectUi(effect_name)
            self.node_list["effect_root"]:ChangeAsset(bundle1, assert1)
            self.node_list["effect_root"]:SetActive(true)
        else
            self.node_list["effect_root"]:SetActive(false)
        end

        self.node_list.name.text.text = item_cfg.name
    end
end

function ConvertItemCell:FlushPropsNum()
    local round, seq = self.data.round, self.data.seq
    local consume_cfg = TianxianPavilionWGData.Instance:GetPropsNum(round, seq)
    if IsEmptyTable(consume_cfg) then
        return
    end

    for i = 1, 2 do
        local data = consume_cfg[i]
        self.node_list["need_prop_" .. i]:SetActive(not IsEmptyTable(data))

        if data then
            local item_bundle, item_asset = ItemWGData.Instance:GetTipsItemIcon(data.item_id)
            self.node_list["icon" .. i].image:LoadSprite(item_bundle, item_asset, function()
                self.node_list["icon" .. i].image:SetNativeSize()
            end)

            self.node_list["cost" .. i].text.text = data.need_num
        end
    end
end

function ConvertItemCell:FlushCanExchange()
    local limit = self.data.limit
    local change_num = TianxianPavilionWGData.Instance:GetExchangeNum(self.data.seq)
    local exchange_num = limit - change_num
    self.node_list.word_sold_out:SetActive(exchange_num == 0)
    self.node_list.btn_exchange:SetActive(exchange_num ~= 0)

    local exchange_color = exchange_num ~= 0 and COLOR3B.L_GREEN or COLOR3B.L_RED
    self.node_list.exchange_num.text.text = string.format(Language.TianxianText.ExchangeStr, exchange_color, exchange_num,
        limit)
end

function ConvertItemCell:OnClickExchange()
    local round, seq = self.data.round, self.data.seq
    local can_exchange = TianxianPavilionWGData.Instance:GetCanExchange(round, seq)
    if not can_exchange then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TianxianText.NoActive)
        return
    end

    local desc = Language.TianxianText.Consumption
    local ok_func = function()
        TianxianPavilionWGCtrl.Instance:SendTianXianBaoGeClientReq(
        TAINXIANBAOGE_OPERA_TYPE.TAINXIANBAOGE_OPERA_TYPE_CONVERT, seq)
    end

    TipWGCtrl.Instance:OpenAlertTips(desc, ok_func)
end
