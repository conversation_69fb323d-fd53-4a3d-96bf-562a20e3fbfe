TARGET_TYPE = {
    WUQI = 0, --武器外观
    TIANSHEN = 1,--天神
    ROLE = 2, --人物模型
}

TARGET_SUIT_ACTIVE_REWARD_TYPE = {
    CONSUMABLES = 0,
    SKILL =  1,
}

--8件装备时的位置
local NormalCellPosTb = {
    [1] = Vector2(-291, 22),
    [2] = Vector2(-327, -101),
    [3] = Vector2(209, -43),
    [4] = Vector2(-313, -242),
    [5] = Vector2(184, -176),
    [6] = Vector2(-180, 114),
    [7] = Vector2(99, -280),
    [8] = Vector2(175, 83),
}
local NormalGetWayPosTb = {
    [1] = Vector2(-314, 108),
    [2] = Vector2(-354, -24),
    [3] = Vector2(363, 32),
    [4] = Vector2(-348, -171), 
    [5] = Vector2(338, -109),
    [6] = Vector2(-207, 191),
    [7] = Vector2(257, -212),
    [8] = Vector2(332, 161),
}

--6件装备时的位置
local SpecialCellPosTb = {
    [1] = Vector2(-316, -36),
    [2] = Vector2(-327, -179),
    [3] = Vector2(185, -104),
    [4] = Vector2(0, 0), --不显示, 无用
    [5] = Vector2(139, -235),
    [6] = Vector2(-209, 76),
    [7] = Vector2(0, 0), --不显示, 无用
    [8] = Vector2(179, 36),
}
local SpecialGetWayPosTb = {
    [1] = Vector2(-345, 35),
    [2] = Vector2(-356, -109),
    [3] = Vector2(345, -28),
    [4] = Vector2(0, 0), --不显示, 无用
    [5] = Vector2(295, -164),
    [6] = Vector2(-239, 151),
    [7] = Vector2(0, 0), --不显示, 无用
    [8] = Vector2(338, 116),
}

local role_rotate = {
	[0] = {[1] = { x = -3, y = 178, z = 0 }, [2] = { x = -3, y = 178, z = 0 }, [3] = { x = -3, y = 178, z = 0 }},
	[1] = {[1] = { x = -3, y = 178, z = 0 }, [2] = { x = -3, y = 178, z = 0 }, [3] = { x = -3, y = 178, z = 0 }},
}

local PROGRESS_WIDTH = 468

function CultivationView:ReleaseCallBack__EquipTarget()
    if nil ~= self.et_equip_type_list then
        self.et_equip_type_list:DeleteMe()
        self.et_equip_type_list = nil
    end

    if nil ~= self.et_equip_obj_list then
        for k,v in pairs(self.et_equip_obj_list) do
            v:DeleteMe()
        end
        self.et_equip_obj_list = nil
    end

    if nil ~= self.et_equip_obj_list1 then
        for k,v in pairs(self.et_equip_obj_list1) do
            v:DeleteMe()
        end
        self.et_equip_obj_list1 = nil
    end

    if self.et_suit_attr_item_list then
        for k,v in pairs(self.et_suit_attr_item_list) do
            v:DeleteMe()
        end
        self.et_suit_attr_item_list = nil
    end

    if self.equip_role_model then
        self.equip_role_model:DeleteMe()
        self.equip_role_model = nil
    end

    if self.et_item_cell then
        self.et_item_cell:DeleteMe()
        self.et_item_cell = nil
    end

    self:CancelWeaponTween()
    self.et_origin_display_pos = nil
    self.et_suit_index = 0
    self.et_display_type = 0
    self.et_show_model = 0
    self.equip_index = nil
    self.task_line_tabs = nil
end

function CultivationView:LoadCallBack__EquipTarget()
    self:InitEquipList()

    self.et_equip_type_list = AsyncListView.New(EquipTypeRender, self.node_list.et_ph_btn_listview)
    self.et_equip_type_list:SetSelectCallBack(BindTool.Bind1(self.OnClickEquipType, self))
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()
    self.et_equip_type_list:SetDataList(type_data)

    self.et_item_cell = ItemCell.New(self.node_list.et_ph_item_cell)

    self.node_list.et_btn_act.button:AddClickListener(BindTool.Bind(self.OnClickAct, self))
    self.node_list.et_skill_icon.button:AddClickListener(BindTool.Bind(self.OnClickSkillIcon, self))
    self.node_list.btn_go_way1.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 1))
    self.node_list.btn_go_way2.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 2))
    self.node_list.btn_go_way3.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 3))
    self.node_list.btn_go_way4.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 4))
    self.node_list.btn_goto.button:AddClickListener(BindTool.Bind(self.OnClickGoTo, self))
    self.node_list.btn_compose.button:AddClickListener(BindTool.Bind(self.OnClickBtnCompose, self))
    self.node_list.btn_equip_target_attr.button:AddClickListener(BindTool.Bind(self.OnClickBtnEquipTargetAttr, self))
    
    if self.node_list.et_display and not self.et_origin_display_pos then
        self.et_origin_display_pos = self.node_list.et_display.rect.anchoredPosition
    end

    self.et_suit_index = 0
    self.et_display_type = 0
    self.et_show_model = 0
    self.et_suit_attr_item_list = {}
    self.task_line_tabs = {}
end

function CultivationView:StopSequence_EquipTarget()
    self:CancelTween()
end

function CultivationView:ShowIndexCallBack__EquipTarget()
    self.is_first_enter_equip = true
    self:FlushEquipTargetModel()
    -- self:PlayModelTween()
end

function CultivationView:FlushEquipTargetModel()
	if not self:IsLoaded() then
		return
	end

	if nil == self.equip_role_model then
		self.equip_role_model = RoleModel.New()
		self.equip_role_model:SetUISceneModel(nil, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.equip_role_model, {TabIndex.equip_target})

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_weapon = true}
		self.equip_role_model:ClearCustomDisplayTranfromData()
		self.equip_role_model:SetPositionAndRotation(nil,nil,Vector3(1.6, 1.6, 1.6))
		self.equip_role_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Sit_Idle)
		self.equip_role_model:SetWingResid(0)
		self.equip_role_model:SetJianZhenResid(0)
	end

	if self.equip_role_model then
		self.equip_role_model:PlaySitAction()
		if self.is_show_daonian then
			self.equip_role_model:SetRotation({ x = 0, y = 180, z = 0 })
			self.equip_role_model:FixToOrthographicOnUIScene()
		else
			local model_transform = self.equip_role_model:GetModelPosNode()
			model_transform:DOLocalMoveX(1.56, 0)

			local effects_transform = self:GetUISceneEffectsTransform()
			effects_transform:DOLocalMoveX(1.28, 0)
		end
	end
end

function CultivationView:GetInitIndex()
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()
    local active_count = 0
    for k_1, v_1 in ipairs(type_data) do
        local list = EquipTargetWGData.Instance:GetEquipList(v_1.suit_index)
        for k_2, v_2 in ipairs(list) do
            if v_2 and tonumber(v_2) > 0 then
                local state = EquipTargetWGData.Instance:GetEquipSortStateByIndex(v_1.suit_index, k_2, true)
                if state then
                    active_count = active_count + 1
                    return k_1
                end

            end
        end

        if k_1 == #type_data then
            return k_1
        end
    end

    return 1
end

function CultivationView:JumpFlushETView(to_ui_param)
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()
    self.et_equip_type_list:SetDataList(type_data)
    if to_ui_param then
        self:JumpToTab(to_ui_param)
    else
        if self.is_first_equip then
            self.is_first_equip = false
            local init_index = self:GetInitIndex()
            self.et_equip_type_list:JumpToIndex(init_index)
        else
            self:FlushETView()
        end
    end
end

function CultivationView:OnFlush__EquipTarget(param_t, index)
	-- for k, v in pairs(param_t) do
	-- 	if "all" == k then
    --         self:JumpFlushETView(v.to_ui_param)
	-- 	end
	-- end
    for k, v in pairs(param_t) do
        if "all" == k then
			-- self:FlushETView()
            self.et_equip_type_list:RefreshActiveCellViews()
            if self.is_first_enter_equip then
                local index = self:GetInitIndex()
                self:JumpFlushETView(index)
                self.is_first_enter_equip = false
            else
                self:FlushETView()
            end

        elseif k == "flush_model" then
            self:FlushEquipTargetModel()
        end
    end
end

function CultivationView:FlushETView()
    local capability = EquipTargetWGData.Instance:GetEquipAttrCap(self.et_suit_index)
    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    local info = EquipTargetWGData.Instance:GetSuitInfo(self.et_suit_index)
    local active_num = EquipTargetWGData.Instance:GetEquipStateNumByIndex(self.et_suit_index)
    local remind = EquipTargetWGData.Instance:GetEquipTargetRemind(self.et_suit_index)
    local str, skill_icon, skill_name = EquipTargetWGData.Instance:GetSuitSkillDes(cfg.suit_active_reward, self.et_display_type, self.et_suit_index)

    local is_has_skill_cfg = EquipTargetWGData.Instance:GetSuitSkillActive(cfg.suit_active_reward)
    local name = ""
    if self.et_item_cell then
        if TARGET_TYPE.TIANSHEN == self.et_display_type then
            local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.suit_active_reward)
            self.et_item_cell:SetData({item_id = cfg.suit_active_reward})
            name = item_cfg and item_cfg.name or ""
        elseif TARGET_TYPE.WUQI == self.et_display_type and is_has_skill_cfg then
                local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(cfg.suit_active_reward)
                name = skill_data.name
                XUI.SetSkillIcon(self.node_list.et_skill_bg, self.node_list.et_skill_icon, skill_icon)
        elseif TARGET_TYPE.ROLE == self.et_display_type or (TARGET_TYPE.WUQI == self.et_display_type and not is_has_skill_cfg) then  
            local item_data = ItemWGData.Instance:GetItemConfig(cfg.suit_active_reward)
            name = item_data.name
            self.et_item_cell:SetData({item_id = cfg.suit_active_reward})
        end
    end

    self.node_list.et_skill_desc.text.text = str
    self.node_list.et_name.text.text = name
    -- self:FlushSuitAttrListView()
    self:FlushEquipListView()
    self.node_list.et_btn_act:SetActive(info.suit_active_reward == 0)
    self.node_list.et_img_max:SetActive(info.suit_active_reward == 1)
    self.node_list.et_btn_act_red:SetActive(remind)
    self.node_list.et_cap_value.text.text = capability

    -- local max_count = 0
    local max_count = EquipTargetWGData.Instance:GetEquipListCount(self.et_suit_index)
    -- for k_1, v_1 in pairs(list) do
    --     if tonumber(v_1) > 0 then
    --         max_count = max_count + 1
    --     end
    -- end

    -- self.node_list.eight_line:SetActive(tonumber(max_count) > 6)

    -- self.node_list.et_equipment_colect_value.image:DOFillAmount(tonumber(active_num)/tonumber(max_count),UITween_CONSTS.EquipTargetSys.ToTargetProgress)
    self.node_list.et_jindu_text.text.text = string.format("%s/%s", active_num, max_count)

    local attr_list_data = EquipTargetWGData.Instance:GetEquipmenSuitAttr(self.et_suit_index)

    if IsEmptyTable(attr_list_data) then
        return 
    end

    local is_open_attr_num = 0
    local can_open_attr_num = 0
    local max_attr_num = #attr_list_data

    for i, v in ipairs(attr_list_data) do
        if active_num >= v.equip_num then
            can_open_attr_num = can_open_attr_num + 1
            is_open_attr_num = v.is_open ~= 0 and (is_open_attr_num + 1) or is_open_attr_num
        end
    end

    local spe_show_num = (can_open_attr_num > is_open_attr_num) and (is_open_attr_num + 1) or is_open_attr_num
    self.node_list.slider_progress.slider.value = spe_show_num / max_attr_num
    self.node_list.slider_progress_1.slider.value = is_open_attr_num / max_attr_num

    for index = 1, max_attr_num do
        self.node_list["img_line_"..index]:CustomSetActive(index <= max_attr_num - 1)
        local pos = (PROGRESS_WIDTH / max_attr_num) * index
        RectTransform.SetAnchoredPositionXY(self.node_list["img_line_"..index].rect, pos, -10)
    end

    local btn_name = remind and Language.EquipTarget.ActiveStr2 or Language.EquipTarget.ActiveStr1
    self.node_list.txt_act.text.text = btn_name

    self.node_list.text_goto.text.text = cfg.skip_name
    local open_param = cfg.is_open_skip or 1
    local is_open_skip = open_param == 1
    self.node_list.btn_goto:SetActive(is_open_skip)
end

function CultivationView:DoEquipTargetTween()

end

function CultivationView:EquipSequenceAni(obj)
    local scale = 1
    if self.display_type == TARGET_TYPE.WUQI then
        scale = 1.1
    else
        scale = 1.1
    end

    obj.transform.localRotation = Quaternion.Euler(0, 0, 40)
    obj.transform.localScale = Vector3(0.4, 0.4, 0.4)

    self:CancelWeaponTween()
    obj.canvas_group.alpha = 0

    local tween = obj.transform:DOScale(Vector3(scale, scale, scale), 0.3)
    tween:OnComplete(function ()
        obj.transform:DOScale(Vector3(1, 1, 1), 0.4)
    end)

    obj.canvas_group:DoAlpha(0, 1, 1)

    local tween2 = obj.transform:DOLocalRotate(Vector3(0, 0, 0), 0.7)
    tween2:OnComplete(function ()

    end)
    tween2:SetEase(DG.Tweening.Ease.InOutQuint)
end

function CultivationView:CancelEquipSequenceAni(obj)
    obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
    obj.transform.localScale = Vector3(1, 1, 1)
end   

function CultivationView:InitEquipList()
    self.et_equip_obj_list = {}
    local item
    for i=1, GameEnum.MAX_EQUIP_TARGET_NUM do
        item = EquipListRender.New(self.node_list['et_equip_' .. i])
        item:SetIndex(i)
        table.insert(self.et_equip_obj_list, item)
    end

    self.et_equip_obj_list1 = {}

end

function CultivationView:OnClickEquipType(cell)
    if cell == nil or cell:GetIndex() == self.equip_index then
        return
    end
    
    local data = cell:GetData()
    self.skill_index = data.suit_active_reward
    self.et_display_type = TARGET_TYPE.WUQI--data.display_type
    self.et_suit_index = data.suit_index
    self.equip_index = cell:GetIndex()
    -- local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    -- local is_show_itencell = TARGET_TYPE.WUQI == self.et_display_type and not EquipTargetWGData.Instance:GetSuitSkillActive(cfg.suit_active_reward)
    --self.node_list.et_tianshen:SetActive(TARGET_TYPE.TIANSHEN == self.et_display_type or TARGET_TYPE.ROLE == self.et_display_type or is_show_itencell)
    self.node_list.et_tianshen:SetActive(data.suit_active_reward_type == TARGET_SUIT_ACTIVE_REWARD_TYPE.CONSUMABLES)
    self.node_list.et_equip_content:SetActive(true)--TARGET_TYPE.ROLE ~= self.et_display_type)

    --self.node_list.et_skill:SetActive(TARGET_TYPE.WUQI == self.et_display_type)
    self.node_list.et_skill:SetActive(data.suit_active_reward_type == TARGET_SUIT_ACTIVE_REWARD_TYPE.SKILL)
    self.node_list.et_role_fishon:SetActive(TARGET_TYPE.ROLE == self.et_display_type)
    EquipTargetWGCtrl.Instance:SendEquipTarget(0, data.suit_index)
    self:SetCellsPos()

    local node = self.node_list.et_equip_content
    self:CancelEquipSequenceAni(node)
    self:EquipSequenceAni(node)
    self:FlushETView()
end

--设置位置
function CultivationView:SetCellsPos()
    local count = EquipTargetWGData.Instance:GetEquipListCount(self.et_suit_index)
    local is_special = count == 6  -- self.et_suit_index > GameEnum.EQUIP_TARGET_SPECIAL_SUIT_START
    local cell_pos_tab = is_special and SpecialCellPosTb or NormalCellPosTb
    local getway_pos_tab = is_special and SpecialGetWayPosTb or NormalGetWayPosTb
    for i,v in ipairs(self.et_equip_obj_list) do
        local is_inactive = is_special and (i == EquipTargetWGData.SuitPartNecklace or i == EquipTargetWGData.SuitPartDrop)
        v:SetViewParams(is_inactive, cell_pos_tab[i], getway_pos_tab[i])
    end
end

--刷新装备栏
function CultivationView:FlushEquipListView()
    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    local list = EquipTargetWGData.Instance:GetEquipList(self.et_suit_index)
    local info = EquipTargetWGData.Instance:GetSuitInfo(self.et_suit_index)
    self.et_display_type = cfg.display_type
    self.et_show_model = cfg.show_model


    local count = 0
    self.now_way_tab = {}
    --if self.et_display_type == TARGET_TYPE.WUQI then
        for i, v in ipairs(self.et_equip_obj_list) do
            if list[i] and tonumber(list[i]) > 0 then
                local item_id = tonumber(list[i])
                v:SetData({item_id = item_id, suit_index = self.et_suit_index})
                 
                local way_group = EquipTargetWGData.Instance:GetEquipGetawayById(item_id)
                if not IsEmptyTable(way_group) then
                    for key, way in pairs(way_group) do
                        local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, i, true)
                        if ((way and not self.now_way_tab[way.acquisition_path] ) or 
                            (state and way and self.now_way_tab[way.acquisition_path] ))
                        and count < 4 then
                            if not self.now_way_tab[way.acquisition_path] then
                                local num = 0
                                for k, v in pairs(self.now_way_tab) do
                                    num = num + 1
                                end
                                count = num + 1
                            else
                                count = self.now_way_tab[way.acquisition_path].index
                            end
                            self.now_way_tab[way.acquisition_path] = {}
                            self.now_way_tab[way.acquisition_path].name = way.acquisition_path
                            self.now_way_tab[way.acquisition_path].item_id = item_id
                            self.now_way_tab[way.acquisition_path].index = count
                            self.now_way_tab[way.acquisition_path].show_index = i
                            self.now_way_tab[way.acquisition_path].get_type = way.get_type
                            -- local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, i, true)
                            self.now_way_tab[way.acquisition_path].star_level = star_level
                        end
                    end
                end
            else
                --v:SetData({})
            end
        end
    -- else
    --     for i,v in ipairs(self.et_equip_obj_list1) do
    --         if list[i] then
    --             local item_id = tonumber(list[i])
    --             v:SetData({item_id = item_id, suit_index = self.et_suit_index})
    --         else
    --             v:SetData({})
    --         end
    --     end
    -- end

    for i = 1, 4, 1 do
        self.node_list["btn_go_way" .. i]:SetActive(false)
    end

    for k_1, v_1 in pairs(self.now_way_tab) do
        self.node_list["btn_go_way" .. v_1.index]:SetActive(true)
        self.node_list["text_go_way" .. v_1.index].text.text = v_1.name

        local can_to = false
        local base_info = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
        local is_limit = false
        if base_info.compose_red_limit_index ~= -1 and not EquipTargetWGData.Instance:IsAllActive(base_info.compose_red_limit_index) then
            is_limit = true
        end
        if v_1.get_type == 1 and not is_limit then
            -- 可合成红点
            local cur_num, max_num = EquipTargetWGData.Instance:GetEquipStateNumBySuitIndex(self.et_suit_index)
            -- if cur_num < max_num then
            --     can_to = EquipmentWGData.Instance:GetCESingleRemindByData(v_1.item_id, v_1.star_level)
            -- end

            if cur_num < max_num then
                local list = EquipTargetWGData.Instance:GetEquipList(self.et_suit_index)
                for k_1, v_1 in pairs(list) do
                    local item_id = tonumber(v_1)
                    if item_id > 0 then
                        local is_show, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, k_1, true)
                        if is_show and EquipmentWGData.Instance:GetCESingleRemindByData(item_id, star_level) then
                            can_to = true
                        end
                    end
                end
            end
        end
        self.node_list["image_can_hecheng_" .. v_1.index]:SetActive(can_to)
        self.node_list["hecheng_remind_".. v_1.index]:SetActive(can_to)
    end
end

function CultivationView:OnClickWayGoTo(index)
    local item_id = nil
    local show_index = 0
    for k_1, v_1 in pairs(self.now_way_tab) do
        if v_1.index == index then
            item_id = v_1.item_id
            show_index = v_1.show_index
            break
        end
    end
    if not item_id then
        return
    end

    local way_group = EquipTargetWGData.Instance:GetEquipGetawayById(item_id)
    local way = way_group[index]
    if not IsEmptyTable(way) then
        if way and way.jump_panel_spc and way.jump_panel_spc ~= ""  then
            local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, show_index, true)
            local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
            local tab_index = flag and TabIndex.other_compose_eq_hecheng_one or TabIndex.other_compose_eq_hecheng_two
            local cur_type = flag and EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE or EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE
            local can_compose, open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeCfgByProductId(item_id, cur_type,star_level)
            if can_compose then
                FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_index, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param})
            else
                FunOpen.Instance:OpenViewByName(way.jump_panel_spc, tab_index)
            end
            return
        end

        local boss_id = way.jump_aim
        if not boss_id then
            return 
        end
        BossOfferWGCtrl.Instance:JumToByID(boss_id)

    else
        print_error("物品没有获取路径item_id = ", self.item_id)
    end
end

-- 刷新属性列表
-- function CultivationView:FlushSuitAttrListView()
--     local list_data = EquipTargetWGData.Instance:GetEquipmenSuitAttr(self.et_suit_index)
--     if IsEmptyTable(list_data) then
--         return
--     end

--     for i,v in ipairs(list_data) do
--         if self.et_suit_attr_item_list[i] then
--             self.et_suit_attr_item_list[i]:SetData(v)
--         else
--             local item_vo = {}
--             local obj = ResMgr:Instantiate(self.node_list["et_attri_num_render"].gameObject)--AllocAsyncLoader(self, "equiptarget_view" .. i)
--             obj:SetActive(true)
--             local obj_transform = obj.transform
--             obj_transform:SetParent(self.node_list.et_suit_attr_list.transform, false)
--             local item_render = EquipRichRenderNew.New(obj)
--             item_render:SetData(v)
--             self.et_suit_attr_item_list[i] = item_render
--         end
--     end
-- end


--跳转红点按钮
function CultivationView:JumpToTab(force_index)
    local index = 0
    local _, is_show = EquipTargetWGData.Instance:EqTargetIsShow()
    local num = EquipTargetWGData.Instance:JumpToRemind()
    local _, indexs = EquipTargetWGData.Instance:GetEquipTargetAllRemind()
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()

    if indexs > 0 then
        index = indexs
    else
        index = num
    end
    
    index = is_show and 6 or index

    force_index = tonumber(force_index)
    if force_index then
        if force_index <= #type_data then
            index = force_index
        end
    end

    --添加容错
    if index > 0 and not IsEmptyTable(type_data) and index > #type_data then
        index = 1
    end

    if index > 0 then
        if self.et_equip_type_list then
            self.et_equip_type_list:JumpToIndex(index, 7)
        end
    else
        self:FlushETView()
    end
end

---模型动画
function CultivationView:PlayEquipModelTween(time)
    local cultivation_time = time or 0.5
    -- model
    self:CancelTween()
    self.equip_sequence = DG.Tweening.DOTween.Sequence()
    local model_transform = self.equip_role_model:GetModelPosNode()
    self.equip_sequence:Join(model_transform:DOLocalMoveX(0, 0):SetEase(DG.Tweening.Ease.Linear))

	local model_transform = self.equip_role_model:GetModelPosNode()
	self.equip_sequence:Join(model_transform:DOLocalMoveX(0.9, cultivation_time):SetEase(DG.Tweening.Ease.Linear))

	local effects_transform = self:GetUISceneEffectsTransform()
	self.equip_sequence:Join(effects_transform:DOLocalMoveX(0.54, cultivation_time):SetEase(DG.Tweening.Ease.Linear))
	
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local rotate = role_rotate[sex][prof]
	self.equip_sequence:Join(model_transform:DOLocalRotate(rotate, cultivation_time):SetEase(DG.Tweening.Ease.Linear))
end

function CultivationView:CancelTween()
    if self.equip_sequence then
        self.equip_sequence:Kill()
        self.equip_sequence = nil
    end
end

function CultivationView:CancelWeaponTween()
    if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
        local tween_root = self.node_list["et_display"]
        if tween_root then
            tween_root.rect.anchoredPosition = self.et_origin_display_pos
        end
    end
end


--技能弹窗
function CultivationView:OnClickSkillIcon()
    if self.et_display_type ~= TARGET_TYPE.WUQI then
        return
    end

    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    local str, skill_icon = EquipTargetWGData.Instance:GetSuitSkillDes(cfg.suit_active_reward, self.et_display_type, self.et_suit_index)

    local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(self.skill_index)
    local capability = EquipTargetWGData.Instance:GetSkillAttrCap(self.skill_index)

    if skill_data then
        local show_data = {
            icon = skill_icon,
            top_text = skill_data.name,
            body_text = str,

            x = -49,
            y = -90,
            set_pos = true,
            capability = capability,
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

--激活套装
function CultivationView:OnClickAct()
    local remind = EquipTargetWGData.Instance:GetEquipTargetRemind(self.et_suit_index)
    local gaer_list, cur_gaer = EquipTargetWGData.Instance:GetEquipJinDu(self.et_suit_index)
    if not remind then
        --SysMsgWGCtrl.Instance:ErrorRemind(Language.EquipTarget.Tips)
        FunOpen.Instance:OpenViewByName(FunName.Boss, TabIndex.boss_vip)
        return
    end

    for i = 1, cur_gaer do
        if gaer_list[i-1] <= 0 then
            EquipTargetWGCtrl.Instance:SendEquipTarget(1, self.et_suit_index)
            return
        end
    end
end

function CultivationView:OnClickGoTo()
    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    FunOpen.Instance:OpenViewNameByCfg(cfg.skip_panel)
end

function CultivationView:OnClickBtnCompose()
    EquipTargetWGCtrl.Instance:OpenEquipTargetCompose()
end

function CultivationView:OnClickBtnEquipTargetAttr()
    CultivationWGCtrl.Instance:OpenEquipTargetAttrView(self.et_suit_index)
end


