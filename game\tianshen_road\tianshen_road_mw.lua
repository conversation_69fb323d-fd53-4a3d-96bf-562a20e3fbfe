
function TianshenRoadView:InitMWView()
	self.drop_reward_item_list = {}

	local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_mowang)
	if theme_cfg ~= nil then
		self.node_list.mw_tip_label.text.text = string.format(Language.TianShenRoad.Space,theme_cfg.rule_desc)
	end

	if not self.mowang_model then
		self.mowang_model = OperationActRender.New(self.node_list["mowang_model"])
		self.mowang_model:SetModelType(MODEL_CAMERA_TYPE.BASE)
	end

	self:MWTimeCountDown()
	self:FlushMWView()
end

function TianshenRoadView:ReleaseMWView()
	if self.mowang_model then
		self.mowang_model:DeleteMe()
		self.mowang_model = nil
	end

	if self.drop_reward_item_list then
		for _,v in pairs(self.drop_reward_item_list) do
			v:DeleteMe()
		end
		self.drop_reward_item_list = nil
	end

	CountDownManager.Instance:RemoveCountDown("tianshenroad_mowang_count_down")
end

function TianshenRoadView:ShowMWView()
	TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_MOWANG_YOULI)
	self:DoTRMWAnim()
end

function TianshenRoadView:FlushMWView()
	self:FlushMoWangModel()
	self:FlushMWDropRewardList()
end

function TianshenRoadView:FlushMoWangModel()
	local init_cfg = TianshenRoadWGData.Instance:GetActModelCfgById(ACTIVITY_TYPE.GOD_MOWANG_YOULI)
	if not init_cfg then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if init_cfg.model_show_itemid ~= 0 and init_cfg.model_show_itemid ~= "" then
		local split_list = string.split(init_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = init_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = init_cfg["model_bundle_name"]
	display_data.asset_name = init_cfg["model_asset_name"]
	local model_show_type = tonumber(init_cfg["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	self.mowang_model:SetData(display_data)

	if init_cfg.model_scale and init_cfg.model_scale ~= "" then
		local scale = init_cfg.model_scale
		Transform.SetLocalScaleXYZ(self.node_list["mowang_model"].transform, scale, scale, scale)
	end

	local pos_x, pos_y = 0, 0
	if init_cfg.model_pos and init_cfg.model_pos ~= "" then
		local pos_list = string.split(init_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.mowang_model.rect, pos_x, pos_y)

	if init_cfg.model_rot and init_cfg.model_rot ~= "" then
		local rotation_tab = string.split(init_cfg.model_rot, "|")
		self.node_list["mowang_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
			rotation_tab[3])
	end
end

function TianshenRoadView:FlushMWDropRewardList()
	local data_list = TianshenRoadWGData.Instance:GetMoWangYouLiDropCountList()
	local item_list = self.drop_reward_item_list

	if #item_list < #data_list then
		local item_root = self.node_list.mw_special_reward
		for i = #item_list + 1, #data_list do
			item_list[i] = MWDropRewardItem.New(item_root)
		end
		self.drop_reward_item_list = item_list
	end

	for i=1,#item_list do
		item_list[i]:SetData(data_list[i])
	end
end

--有效时间倒计时
function TianshenRoadView:MWTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_mowang_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_mowang)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.mw_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_mowang_count_down", BindTool.Bind1(self.UpdateMWCountDown, self), BindTool.Bind1(self.MWTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.mw_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.mw_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function TianshenRoadView:UpdateMWCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.mw_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function TianshenRoadView:DoTRMWAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["ts_mowan_root"])
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["ts_mowan_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end
----------------------------------------------------------------------------

MWTaskItemRender = MWTaskItemRender or BaseClass(BaseRender)

function MWTaskItemRender:LoadCallBack()
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnClickGo, self))
end

function MWTaskItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local cfg = TianshenRoadWGData.Instance:GetMWTaskItemForID(data.ID)
	if not cfg then
		return
	end

	local left_times,max_times = BossWGData.Instance:GetBossTimesInfo(data.boss_type)
	local can_kill_boss = left_times > 0 or max_times < 0
	local color = can_kill_boss and COLOR3B.GREEN or COLOR3B.RED
	local res_name = string.gsub(cfg.icon, "btn", "act")
	local bundle, asset = ResPath.GetF2MainUIImage(res_name)

	self.node_list.icon.image:LoadSpriteAsync(bundle, asset)
	self.node_list.name.text.text = cfg.task_name
	self.node_list.btn:SetActive(can_kill_boss)
	self.node_list.btn_ylq:SetActive(not can_kill_boss)

	if cfg.task_num_limit == 0 or max_times < 0 then
		self.node_list.finish_times.text.text = Language.TianShenRoad.MoWangStr2
	else
		self.node_list.finish_times.text.text = string.format(Language.TianShenRoad.MoWangStr1, color, left_times, max_times)
	end

	XUI.SetButtonEnabled(self.node_list.btn, cfg.panel ~= "")

	self.panel = cfg.panel
end

function MWTaskItemRender:OnClickGo()
	FunOpen.Instance:OpenViewNameByCfg(self.panel)
end

--------------------------------------------------------------------------------

MWDropRewardItem = MWDropRewardItem or BaseClass(BaseRender)

function MWDropRewardItem:__init(instance)
	if not instance then
		return
	end
	self:LoadAsset("uis/view/tianshenroad_ui_prefab", "mw_special_reward", instance.transform)
end

function MWDropRewardItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function MWDropRewardItem:LoadCallBack()
	if self.node_list.cell_root and not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell_root)
	end
end

function MWDropRewardItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	self.item_cell:SetData({item_id = data.item_id})
	self.node_list.drop_count_lbl.text.text = string.format(Language.TianShenRoad.MWTodayFinishNum, data.now_count, data.max_count)
end