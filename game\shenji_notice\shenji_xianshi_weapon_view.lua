ShenJiXianShiWeaponView = ShenJiXianShiWeaponView or BaseClass(SafeBaseView)

function ShenJiXianShiWeaponView:__init()
    self:AddViewResource(0, "uis/view/shenji_notice_prefab", "shenji_notice_xianshi_weapon")
end

function ShenJiXianShiWeaponView:ReleaseCallBack()
    if self.weapon_model then
        self.weapon_model:DeleteMe()
        self.weapon_model = nil
    end
    if self.model_1_self_tweener then
        self.model_1_self_tweener:Kill()
        self.model_1_self_tweener = nil
    end
end

function ShenJiXianShiWeaponView:LoadCallBack()
    self.weapon_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["RoleDisplay_1"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }
    
    self.weapon_model:SetRenderTexUI3DModel(display_data)
    -- self.weapon_model:SetUI3DModel(
    --     self.node_list["RoleDisplay_1"].transform,
    --     self.node_list["ph_display_left_1"].event_trigger_listener,
    --     0,
    --     false,
    --     MODEL_CAMERA_TYPE.BASE
    -- )
    self:AddUiRoleModel(self.weapon_model)

    local tween_info = UITween_CONSTS.ShenJiNotice.XianShi
    self.model_1_self_tweener = self.node_list["model_1_self_tween"].transform:DOAnchorPosY(
    tween_info.ModelSelf_FloatDistance, tween_info.ModelSelf_FloatTime):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
end

function ShenJiXianShiWeaponView:ShowIndexCallBack()
    local tween_info = UITween_CONSTS.ShenJiNotice.XianShi
    --右侧模型
    UITween.KillMoveAlphaTween("ShenJiXianShiWeaponView", self.node_list["model_1"].gameObject)
    UITween.MoveAlphaShowPanelByEndPos("ShenJiXianShiWeaponView", self.node_list["model_1"].gameObject,
        Vector2(-59.28, -138), Vector2(-59.28, -32.25), tween_info.Model_MoveTime)
end

function ShenJiXianShiWeaponView:OnFlush()
    -- local resource_cfg = ShenJiNoticeWGData.Instance:GetXianShiBtnResourceCfg()
    local resource_cfg = XianQiTeDianWGData.Instance:GetLingJiaBtnShowCfg(-1)
    local model_str_table = Split(resource_cfg.model, "#")
    local bundle, asset
    bundle, asset = ResPath.GetShenJiModel(model_str_table[1])
    self.weapon_model:SetMainAsset(bundle, asset)
end
