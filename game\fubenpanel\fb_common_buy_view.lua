
FBCommonBuyView = FBCommonBuyView or BaseClass(DayCountChangeView)

function FBCommonBuyView:__init()
    self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_tianshen_msg")
end

function FBCommonBuyView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
    if self.vip_change_event then
        GlobalEventSystem:UnBind(self.vip_change_event)
        self.vip_change_event = nil
    end
end

function FBCommonBuyView:LoadCallBack()
    DayCountChangeView.LoadCallBack(self)
	self.node_list["title_view_name"].tmp.text = Language.ViewName.CiShuZengJia
    --Vip信息改变
    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))
    XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind(self.OnClickCancel, self))
    XUI.AddClickEventListener(self.node_list["btn_buy_count"], BindTool.Bind(self.OnClickBuyCount, self))
	XUI.AddClickEventListener(self.node_list["btn_xufei"], BindTool.Bind(self.OnClickXuFei, self))
end

--fb_type 副本类型
function FBCommonBuyView:SetData(fb_type)
    self.data = {}
	self.data.fb_type = fb_type
    self:Open()
end

function FBCommonBuyView:OnVipInfoChange()
    self:Flush()
end

function FBCommonBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipCfg(self.data.fb_type)
	local now_count, buy_times, next_vip, next_count = FuBenPanelWGData.Instance:GetNextVipParam(self.data.fb_type)
	local remain_count = vip_buy_cfg["param_" .. role_vip] - buy_times
	local color = remain_count > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
   	local des = string.format(Language.FuBenPanel.FuBenBuyTips, color, now_count - buy_times, now_count)
	local comsume_gold = self:GetNeedGold(buy_times + 1)
	if comsume_gold then
		local comsume_str = string.format(Language.FuBenPanel.CopperBuyNum, comsume_gold)
		self.node_list["rich_buy_desc"].tmp.text = comsume_str
	end
	self.node_list["rich_buy_des_1"].tmp.text = des
	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if role_vip < max_vip and vip_buy_cfg["param_" .. role_vip + 1] then
		if vip_buy_cfg["param_" .. role_vip + 1] > vip_buy_cfg["param_" .. role_vip] then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip + 1])
		else
			des = string.format(Language.FuBenPanel.CopperBuyTips2, next_count)
		end
	else
		des = Language.FuBenPanel.CopperBuyTips3
	end
	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end
	self.node_list["rich_buy_des_2"].tmp.text = des
	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].tmp.text = "V"..role_vip
		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..next_vip
			self.node_list["layout_next_vip"]:SetActive(true)
		else
			self.node_list["img_vip_nexlevel"].tmp.text = "V"..role_vip
			self.node_list["layout_next_vip"]:SetActive(false)
		end
	end
	
	local is_vip = VipWGData.Instance:IsVip()
	self.node_list.btn_xufei:SetActive(not is_vip)
	self.node_list.btn_cancel:SetActive(is_vip)
	self.node_list.btn_buy_count:SetActive(is_vip)
end

function FBCommonBuyView:OnClickCancel()
	self:Close()
end

function FBCommonBuyView:OnClickXuFei()
	-- VipWGCtrl.Instance:OpenVipRenewView()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
	self:Close()
end

function FBCommonBuyView:OnClickBuyCount()
	local max_vip_level = VipWGData.Instance:GetMaxVIPLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipCfg(self.data.fb_type)
	local now_count, buy_times, next_vip, next_count = FuBenPanelWGData.Instance:GetNextVipParam(self.data.fb_type)
	local times = now_count - buy_times
	local is_max_count = vip_buy_cfg["param_" .. max_vip_level] == buy_times

	if 0 == times then
		if is_max_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.BuyMaxCount)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.VIPTooLow)
		end
	else
		--send buy protocal
		self:SendBuy()
	end
end

--发送购买协议
function FBCommonBuyView:SendBuy()
	local fb_type = self.data.fb_type
	if fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then			--铜币本
		FuBenPanelWGCtrl.Instance:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_BUY_FB_TIMES)
	elseif fb_type == FUBEN_TYPE.FBCT_PETBEN then			--宠物本
		FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_BUY_TIMES)
	elseif fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then		--天神本
		FuBenPanelWGCtrl.Instance:TianShenFbBuyTimesReq()
	elseif fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then 	--八卦迷阵
		FuBenPanelWGCtrl.Instance:BaGuaMiZhenFbBuyTimesReq()
	elseif fb_type == FUBEN_TYPE.FBCT_WUJINJITAN then		--经验本
		FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.BUY_ENTER_COUNT)
	elseif fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then		--经验本（灵魂广场）
		FuBenWGCtrl.Instance:SendWuJinJiTanReq(TEAM_EXP_TYPE.LINGHUNSQUA_REQ_TYPE_BUY_ONCE)
	elseif fb_type == FUBEN_TYPE.HIGH_TEAM_EQUIP then 		--装备本
		FuBenWGCtrl.Instance:SendYuanGuFBReq(FB_HIGH_REQ_TYPE.FB_HIGH_REQ_TYPE_BUY_TIMES)
	elseif fb_type == FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB then 	--蛮荒古殿
		FuBenWGCtrl.Instance:SendManHuangGuDianOperate(MANHUANGGUDIAN_OPERA_TYPE.BUY_TIMES)
	end
end

--购买需要消耗
function FBCommonBuyView:GetNeedGold(buy_times)
	local need_gold = 60
	local fb_type = self.data.fb_type
	if fb_type == FUBEN_TYPE.FBCT_TONGBIBEN then			--铜币本
		need_gold = CopperFbWGData.Instance:GetBuyTimesComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.FBCT_PETBEN then			--宠物本
		need_gold = FuBenPanelWGData.Instance:GetPetComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.FBCT_TIANSHEN_FB then		--天神本
		need_gold = FuBenPanelWGData.Instance:GetTianShenBuyComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.FBCT_BAGUAMIZHEN_FB then 	--八卦迷阵
		need_gold = FuBenPanelWGData.Instance:GetBaGuaMiZhenBuyComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.FBCT_WUJINJITAN then		--经验本
		need_gold = WuJinJiTanWGData.Instance:GetBuyTimesComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.LINGHUNGUANGCHANG then		--经验本（灵魂广场）
		need_gold = WuJinJiTanWGData.Instance:GetBuyTimesComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.HIGH_TEAM_EQUIP then 		--装备本
		need_gold = TeamEquipFbWGData.Instance:GetBuyTimesComsumeGold(buy_times)
	elseif fb_type == FUBEN_TYPE.FB_MANHUANG_GUDIAN_FB then 	--蛮荒古殿
		need_gold = ManHuangGuDianWGData.Instance:GetBuyTimesComsumeGold(buy_times)
	end
	return need_gold
end