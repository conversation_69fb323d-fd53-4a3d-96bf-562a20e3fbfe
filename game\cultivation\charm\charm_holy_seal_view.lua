--/jy_gm XiuWeiGmOperate:0 5  --境界等级GM

local DEFAULT_BAG_CELL_NUM = 280
local SLOT_NUM = 11

function CultivationView:LoadHolySealCallBack()
	if not self.holy_seal_list then
		local bundle = "uis/view/cultivation_ui/charm_prefab"
		local asset = "charm_bag_cell"

		self.holy_seal_list = AsyncBaseGrid.New()
		self.holy_seal_list:CreateCells({
			col = 4,
			cell_count = DEFAULT_BAG_CELL_NUM,
			list_view = self.node_list.holy_seal_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = CharmBagCell
		})
		self.holy_seal_list:SetStartZeroIndex(false)
		self.holy_seal_list:SetIsMultiSelect(false)
	end

	XUI.AddClickEventListener(self.node_list.btn_holyseal_compose, BindTool.Bind(self.OnClickHolySealCompose, self))
	XUI.AddClickEventListener(self.node_list.btn_tianhua, BindTool.Bind(self.OnClickTianZuiBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_charm_set_attr, BindTool.Bind(self.OnClickHolySealSuitAttr, self))
	XUI.AddClickEventListener(self.node_list.btn_holyseal_onekey_wear, BindTool.Bind(self.OnClickHolySealOneKeyWear, self))
	XUI.AddClickEventListener(self.node_list.btn_total_attr, BindTool.Bind(self.OnClickHolySealAllAttr, self))
	XUI.AddClickEventListener(self.node_list.btn_get_equip, BindTool.Bind(self.OnClicBtnkGetEquip, self))
	
	-- self.node_list["ph_tog_bag"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickCharmToggle, self, 1))
	-- self.node_list["ph_tog_suit_attr"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickCharmToggle, self, 2))
	-- self.node_list["ph_tog_bag"].toggle.isOn = true

	if not self.charm_slot_list then
		self.charm_slot_list = {}
		for i = 1, SLOT_NUM do
			local slot_item = CharmHolySealSlotItem.New(self.node_list["charm_slot_" .. i])
			slot_item:SetIndex(i)
			self.charm_slot_list[i] = slot_item
		end
	end

	-- 总属性列表
	if not self.charm_general_attr_list then
		self.charm_general_attr_list = AsyncListView.New(CharmGeneralAttrListRender, self.node_list.charm_general_attr_list)
	end

    self.holy_seal_remind_callback = BindTool.Bind(self.HolySealOtherRemindCallBack, self)
	RemindManager.Instance:Bind(self.holy_seal_remind_callback, RemindName.Charm_LingZhu)
end

function CultivationView:ReleaseHolySealCallBack()

	if self.seal_role_model then
		self.seal_role_model:DeleteMe()
		self.seal_role_model = nil
	end

	if self.holy_seal_list then
		self.holy_seal_list:DeleteMe()
		self.holy_seal_list = nil
	end

	if self.cambered_list then
		self.cambered_list:DeleteMe()
		self.cambered_list = nil
	end

	if self.charm_slot_list then
		for _, v in pairs(self.charm_slot_list) do
			v:DeleteMe()
		end
		self.charm_slot_list = nil
	end

	if self.charm_general_attr_list then
		self.charm_general_attr_list:DeleteMe()
		self.charm_general_attr_list = nil
	end

	if self.seal_ph_display then
		self.seal_ph_display:DeleteMe()
		self.seal_ph_display = nil
	end

	self.show_nuqi_type = nil

	RemindManager.Instance:UnBind(self.holy_seal_remind_callback)
end

function CultivationView:ShowHolySealCallBack()
	self:FlushSealModel()
end

function CultivationView:OnFlushHolySeal(param)
	-- self.node_list.holy_seal_cap_value.text.text = CultivationWGData.Instance:GetHolySealCap()
    for k, v in pairs(param) do
        if "all" == k then
			self:FlushHolySealRight()
			self:FlushLongZhuToggle()
			self:FlushSealModel()
        elseif k == "flush_model" then
            self:FlushSealModel()

        end
    end
end

-- 红点变化回调
function CultivationView:HolySealOtherRemindCallBack(remind_name, num)
	if remind_name == RemindName.Charm_LingZhu then
		self.node_list.btn_tianhua_remind:CustomSetActive(num > 0)
    end
end

-- 刷新槽位
function CultivationView:FlushLongZhuToggle()
	local item_list = self.charm_slot_list
	for i = 1, #item_list do
		item_list[i]:Flush()
	end
end

function CultivationView:OnClickCharmToggle(index, is_on)
	self.node_list["page_bag"]:SetActive(index == 1 and is_on)
	self.node_list["page_attr"]:SetActive(index == 2 and is_on)
end

function CultivationView:FlushHolySealRight()
	-- 背包
	local data_list = CultivationWGData.Instance:GetCharmBagSortDataList()
	self.holy_seal_list:SetDataList(data_list)

	if IsEmptyTable(data_list) then
		self.node_list.btn_holyseal_compose:CustomSetActive(false)
		self.node_list.btn_holyseal_onekey_wear:CustomSetActive(false)
		self.node_list.btn_get_equip:CustomSetActive(true)
	else
		self.node_list.btn_holyseal_compose:CustomSetActive(true)
		self.node_list.btn_holyseal_onekey_wear:CustomSetActive(true)
		self.node_list.btn_get_equip:CustomSetActive(false)
	end

	-- 总属性
	local data_list = CultivationWGData.Instance:GetCharmGeneralAttrDataList()
	local has_data = not IsEmptyTable(data_list)
	self.node_list.no_attr_tip:CustomSetActive(not has_data)
	self.node_list.charm_general_attr_list:CustomSetActive(has_data)
	if has_data then
		self.charm_general_attr_list:SetDataList(data_list)
	end

	-- 红点
	local one_key_wear_remind = CultivationWGData.Instance:GetCharmOneKeyWearRemind()
	self.node_list.btn_holyseal_onekey_wear_remind:CustomSetActive(one_key_wear_remind)

	local compose_remind = CultivationWGData.Instance:GetCharmComposeRemind()
	self.node_list.btn_holyseal_compose_remind:CustomSetActive(compose_remind)

	-- 模型
	-- self:FlushSealModel()
end

function CultivationView:StopSequence_Seal()
	self:CancelSealTween()
end

function CultivationView:CancelSealTween()
    if self.seal_show_sequence then
        self.seal_show_sequence:Kill()
        self.seal_show_sequence = nil
    end
end

---模型动画
function CultivationView:PlaySealModelTween(time)
    local cultivation_time = time or 0.5
    -- model

	self:CancelSealTween()
	self.seal_show_sequence = DG.Tweening.DOTween.Sequence()
	local model_transform = self.seal_role_model:GetModelPosNode()
	self.seal_show_sequence:Join(model_transform:DOLocalMoveX(0.9, 0):SetEase(DG.Tweening.Ease.Linear))

	local effects_transform = self:GetUISceneEffectsTransform()
	self.seal_show_sequence:Join(effects_transform:DOLocalMoveX(0.54, 0):SetEase(DG.Tweening.Ease.Linear))

    self.seal_show_sequence:Join(model_transform:DOLocalMoveX(0, cultivation_time):SetEase(DG.Tweening.Ease.Linear))
    self.seal_show_sequence:Join(effects_transform:DOLocalMoveX(0, cultivation_time):SetEase(DG.Tweening.Ease.Linear))
end

function CultivationView:FlushSealModel()

	if not self:IsLoaded() then
		return
	end

	if nil == self.seal_role_model then
		self.seal_role_model = RoleModel.New()
		self.seal_role_model:SetUISceneModel(nil, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.seal_role_model, {TabIndex.tiandao_stone})

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_weapon = true}
		self.seal_role_model:SetPositionAndRotation(nil,nil,Vector3(1.6, 1.6, 1.6))
		self.seal_role_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Sit_Idle)
		self.seal_role_model:SetWingResid(0)
	end

	if self.seal_role_model then
		self.seal_role_model:PlaySitAction()
		self.seal_role_model:SetRotation({ x = 0, y = 180, z = 0 })
		self.seal_role_model:FixToOrthographicOnUIScene()
	end
end

-- 刷新模型
function CultivationView:FlushSealDisplay()

	if nil == self.seal_ph_display then
		self.seal_ph_display = RoleModel.New()
		self.seal_ph_display:SetUISceneModel(self.node_list["holy_seal_ph_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.seal_ph_display, {TabIndex.tiandao_stone})
	end
	local show_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()

	if self.seal_ph_display and show_nuqi_type > -1 then
		self.seal_ph_display:ExecuteAransformationAction(show_nuqi_type)
	end
	
	if self.show_nuqi_type == show_nuqi_type then
		return
	end
	
	if self.seal_ph_display then
		local show_nuqi_lv = CultivationWGData.Instance:GetAngerLevel(show_nuqi_type)
		local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(show_nuqi_type, show_nuqi_lv)
		if cfg ~= nil then
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			local special_status_table = {ignore_halo = true, ignore_wing = true}
			self.seal_role_model:SetModelResInfo(role_vo, special_status_table)
			self.seal_role_model:SetAngerImage(show_nuqi_type, true, cfg.default_body, cfg.default_face, cfg.default_hair)
		end
	end

	self.show_nuqi_type = show_nuqi_type
end



function CultivationView:OnClickHolySealCompose()
	CultivationWGCtrl.Instance:OpenCharmComposeView()
end

function CultivationView:OnClickHolySealOneKeyWear()
	if IsEmptyTable(CultivationWGData.Instance:GetCharmBagDataList()) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Charm.CharmInlaidNoEquip)
		return
	end

	local count, data_list = CultivationWGData.Instance:GetCharmOneKeyWearDataList()

	if not IsEmptyTable(data_list) then
		CultivationWGCtrl.Instance:OnCSCharmEquipWear(count, data_list)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Charm.CharmInlaidNoEquip)
	end
end

function CultivationView:OnClickHolySealSuitAttr()
	CultivationWGCtrl.Instance:OpenCharmSuitOverView()
end

function CultivationView:OnClickTianZuiBtn()
	CultivationWGCtrl.Instance:OpenCharmTianZuiView()
end


function CultivationView:OnClickHolySealAllAttr()
    CultivationWGCtrl.Instance:OpenCharmAttrView()
end

function CultivationView:OnClicBtnkGetEquip()
    ViewManager.Instance:Open(GuideModuleName.Boss, BossViewIndex.DabaoBoss)
end


-----------------------------------------------
-- 槽位ItemRender
-----------------------------------------------
CharmHolySealSlotItem = CharmHolySealSlotItem or BaseClass(BaseRender)
function CharmHolySealSlotItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_cell_node"])
	self.item_cell:SetUseButton(false)
	self.item_cell:IsCanDJ(false)

	XUI.AddClickEventListener(self.node_list.cell_node, BindTool.Bind(self.OnClick, self))
end

function CharmHolySealSlotItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function CharmHolySealSlotItem:OnClick()
	local solt = self:GetIndex() - 1
	local slot_info = CultivationWGData.Instance:GetCharmSoltDataBySolt(solt)
	if not IsEmptyTable(slot_info) then
		if slot_info.is_block == 0 then
			local slot_cfg = CultivationWGData.Instance:GetCharmHolySealSoltCfgBySolt(solt)
			local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(slot_cfg.stage)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Cultivation.TDTipStr2, stage_cfg.stage_title))
		elseif slot_info.item_id > 0 then
			TipWGCtrl.Instance:OpenItem({
				item_id = slot_info.item_id
			})
		else
			local slot_cfg = CultivationWGData.Instance:GetCharmHolySealSoltCfgBySolt(solt)
			local show_item_id = slot_cfg and slot_cfg.show_itemid or 0
			if show_item_id > 0 then
				TipWGCtrl.Instance:OpenItem({
					item_id = show_item_id
				})
			end
		end
	end
end

function CharmHolySealSlotItem:SetIndex(index)
	self.index = index
end

function CharmHolySealSlotItem:OnFlush()
	local index = self:GetIndex()
	local slot = index - 1

	local slot_cfg = CultivationWGData.Instance:GetCharmHolySealSoltCfgBySolt(slot)
	self.node_list["txt_slot_tag"].text.text = slot_cfg.tips
	if slot~=0 then
		local icon_bundle, icon_asset = ResPath.GetCultivationImg("a3_tds_jytb" .. slot_cfg.paper)
		self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function()
			self.node_list.icon.image:SetNativeSize()
		end)
	end

	local slot_info = CultivationWGData.Instance:GetCharmSoltDataBySolt(slot)
	if not IsEmptyTable(slot_info) then
		self.node_list["img_locked"]:CustomSetActive(slot_info.is_block == 0)
		local is_equipped = slot_info.item_id > 0
		self.node_list["item_cell_node"]:CustomSetActive(is_equipped)
		-- slot==0时不隐藏背景 隐藏道具底
		self.node_list.icon:CustomSetActive(not is_equipped)
		self.node_list.bg:CustomSetActive(not is_equipped or slot == 0)
		if is_equipped then
			local item_cfg = ItemWGData.Instance:GetItemConfig(slot_info.item_id)
			if not IsEmptyTable(item_cfg) then
				if slot == 0 then
					-- if self.item_cell then
					-- 	self.item_cell:DeleteMe()
					-- 	self.item_cell = nil
					-- end
					-- self.item_cell = DiamondItemCell.New(self.node_list["item_cell_node"])
					-- self.item_cell:SetUseButton(false)
					-- self.item_cell:IsCanDJ(false)
					self.item_cell:SetShowCualityBg(false)
					self.item_cell:NeedDefaultEff(false)
					self.item_cell:SetCellBgEnabled(false)
					
				end
				self.item_cell:SetData({
					item_id = item_cfg.icon_id
				})


				local data_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(slot_info.item_id)
				local order_str = string.format(Language.Charm.CharmOrderLevel, data_cfg.order)
				self.item_cell:SetRightTopImageText(order_str)
			end
		else
			self.item_cell:SetData(nil)
		end
	end
end

-----------------------------------------------
-- 属性Render
-----------------------------------------------
CharmGeneralAttrListRender = CharmGeneralAttrListRender or BaseClass(BaseRender)
function CharmGeneralAttrListRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = self.data.attr_name
	self.node_list.value.text.text = self.data.value_str
end

