BossHMSYTipView = BossHMSYTipView or BaseClass(SafeBaseView)

local CellHigh = 106
local Spcaing = 8
local high_1 = 200
local high_2 = 150

function BossHMSYTipView:__init()
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_shenyu_info_tip1")
    self:SetMaskBg(true, true)
end

function BossHMSYTipView:ReleaseCallBack()
    if self.boss_list then
        self.boss_list:DeleteMe()
        self.boss_list = nil
    end
end

function BossHMSYTipView:LoadCallBack()
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.boss_list = AsyncListView.New(BossHMSYTipRender, self.node_list["info_list"])
end

function BossHMSYTipView:OpenCallBack()
    if self.node_list["info_list"] then
        self.node_list["info_list"].scroller:ReloadData(0)
    end
end

function BossHMSYTipView:OnFlush(param)
    local _scene_id, scene_index, select_layer = BossWGData.Instance:GetHMSYSelectScene()
    local str = select_layer == 1 and Language.HMSY.LayerName1 or Language.HMSY.LayerName2
    self.node_list.title_view_name.text.text = string.format(str, NumberToChinaNumber(select_layer - 1))
    local info = BossWGData.Instance:GetHMSYCurSelectSceneBossList()
    local boss_list = info.hmsy_boss_list or {}

    --自适应~
    local num = #boss_list
    if num > 4 then
        self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
        self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    elseif num ~= 0 then
        local high = CellHigh * num + Spcaing * (num - 1)--106x3 = 318 + 16 = 334
        self.node_list.info_list.rect.sizeDelta = Vector2(882,high)
        self.node_list.info_list.rect.localPosition = Vector2(0, high / 2)
        local hi = num == 1 and high_1 or high_2
        self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2( self.node_list.size.rect.sizeDelta.x, high + hi)
        self.node_list.layout_commmon_second_root.rect.anchoredPosition =  Vector2(1,24)
    end

    self.boss_list:SetDataList(boss_list, 3)
end

----------------------------------------------------------------------------------
BossHMSYTipRender = BossHMSYTipRender or BaseClass(BaseRender)
function BossHMSYTipRender:__delete()
    if self.flush_timer then
        GlobalTimerQuest:CancelQuest(self.flush_timer)
        self.flush_timer = nil
    end
     if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function BossHMSYTipRender:LoadCallBack()
    self.node_list["btn_check"].button:AddClickListener(BindTool.Bind(self.OnClickCheck, self))
    self.item_list = AsyncListView.New(ItemCell, self.node_list["ph_cell_list"])
    self.node_list["go_kill_btn"].button:AddClickListener(BindTool.Bind(self.OnClickEnter, self))
end

function BossHMSYTipRender:SetDropItemList()
    local cfg_1 = BossWGData.Instance:NewGetHMSYBossCfgByBossId(self.data.boss_id)
    if cfg_1.drop_item_list ~= nil then
        local drop_list = Split(cfg_1.drop_item_list,"|")
        local drop_item_list = {}
        for k,v in pairs(drop_list) do
            local info = {}
            info.item_id = tonumber(v)
            drop_item_list[k] = info
        end
        self.item_list:SetDataList(drop_item_list)
    end
end

function BossHMSYTipRender:OnFlush()

    local cfg = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
    self.node_list["boss_ifno"].text.text = cfg.name
    self.node_list["text_boss_level"].text.text = "LV." .. cfg.level
    self.node_list["jieshu_text"].text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(cfg.boss_jieshu))
    self:SetDropItemList()

    local scene_cfg = BossWGData.Instance:GetHMSYCurSelectSceneType()
    local _, select_scene_index = BossWGData.Instance:GetHMSYSelectScene()
    local img_yes_status
    if scene_cfg.scene_type == WorldServerView.SCENETYPE.COMMON then
        img_yes_status = BossWGData.Instance:GetHMSYCommonBossFlag(scene_cfg.layer - 1, self.index - 1)
    else
        img_yes_status = BossWGData.Instance:GetHMSYInvadeBossFlag(scene_cfg.layer - 1, select_scene_index + 1, self.index - 1)
    end
    self.node_list["img_check"]:SetActive(img_yes_status)
    self.node_list["focus_icon"]:SetActive(img_yes_status)
    if self.flush_timer then
        GlobalTimerQuest:CancelQuest(self.flush_timer)
        self.flush_timer = nil
    end
    self:ShowTimeText()
    self.flush_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.ShowTimeText, self), 0.3)
    local bundle,asset = ResPath.GetBossIcon("wrod_boss_"..cfg.small_icon)
    self.node_list["img_icon"].image:LoadSprite(bundle, asset, function()
        --self.node_list.img_icon.image:SetNativeSize()
    end)
end

function BossHMSYTipRender:ShowTimeText()
    local time = TimeWGCtrl.Instance:GetServerTime()
    time = self.data.next_refresh_time - time
    if time > 0 then
        self.node_list["time"].text.text = ToColorStr(TimeUtil.FormatSecond(time, 2), COLOR3B.RED)
    else
        if self.flush_timer then
            GlobalTimerQuest:CancelQuest(self.flush_timer)
            self.flush_timer = nil
        end
        self.node_list["time"].text.text = ToColorStr(Language.Boss.HasRefresh, COLOR3B.GREEN)
    end
end

function BossHMSYTipRender:OnClickCheck()
    local oper_type = WorldServerView.SYOPERATE.CONCERN
    local scene_cfg = BossWGData.Instance:GetHMSYCurSelectSceneType()
    local _, select_scene_index = BossWGData.Instance:GetHMSYSelectScene()
    if scene_cfg.scene_type == WorldServerView.SCENETYPE.COMMON then
        if BossWGData.Instance:GetHMSYCommonBossFlag(scene_cfg.layer - 1, self.index - 1) then
            oper_type = WorldServerView.SYOPERATE.UNCONCERN
        end
    else
        if BossWGData.Instance:GetHMSYInvadeBossFlag(scene_cfg.layer - 1, select_scene_index + 1, self.index - 1) then
            oper_type = WorldServerView.SYOPERATE.UNCONCERN
        end
    end
    BossWGCtrl.Instance:SendHMSYOpera(oper_type, scene_cfg.scene_id, self.data.boss_id, select_scene_index)
end

function BossHMSYTipRender:OnSelectChange(is_select)
    --self.node_list["img_bg"]:SetActive(not is_select)
    --self.node_list["img_high"]:SetActive(is_select)
end

function BossHMSYTipRender:OnClickEnter()
    local scene_id, scene_index, layer = BossWGData.Instance:GetHMSYSelectScene()
    -- if self:CheckBossSceneGoto(scene_id) then
    --     return
    -- end

    BossWGCtrl.Instance:SendHMSYEnterReq(scene_id, scene_index)
    BossWGData.Instance:GetSetLastSelectInfo(self.sy_select_layer,self.index)
end

function BossHMSYTipRender:CheckBossSceneGoto(scene_id)
    local scene_type = Scene.Instance:GetSceneType()
    local same_scene = Scene.Instance:GetSceneId() == scene_id

    if Scene.Instance:GetSceneId() == scene_id then
        ViewManager.Instance:Close(GuideModuleName.BossHMSYTip)
        ViewManager.Instance:Close(GuideModuleName.WorldServer)
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.InSameScene)
        return true
    else
        if BossWGData.IsBossScene(scene_type) then
            local str = BossWGData.Instance:GetBossSceneTip(scene_id, same_scene)
            SysMsgWGCtrl.Instance:ErrorRemind(str)
            return true
        end
    end

    return false
end
