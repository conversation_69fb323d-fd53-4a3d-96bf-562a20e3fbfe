--骑宠装备快捷升品界面
MountLingChongEquipQuickSp = MountLingChongEquipQuickSp or BaseClass(SafeBaseView)

function MountLingChongEquipQuickSp:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(782, 500)})
    self:AddViewResource(0, "uis/view/qichong_equip_ui_prefab", "layout_quick_enhance_quality")
    if self.view_name == GuideModuleName.LingChongQuickSpView then
        self.cur_show_type = MOUNT_PET_EQUIP_TYPE.PET
    elseif self.view_name == GuideModuleName.MountEquipQuickSpView then   
        self.cur_show_type = MOUNT_PET_EQUIP_TYPE.MOUNT
    elseif self.view_name == GuideModuleName.HuaKunEquipQuickSpView then   
        self.cur_show_type = MOUNT_PET_EQUIP_TYPE.HUAKUN
    end
    self:SetMaskBg(true)
end

function MountLingChongEquipQuickSp:ReleaseCallBack()
    self:CancleShengPinTimes()
    if self.item_list then
        self.item_list:DeleteMe()
    end
    self.item_list = nil
    self.is_click_sure = false
end

function MountLingChongEquipQuickSp:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.MountPetEquip.QuickEnhanceQuality
    self.item_list = AsyncListView.New(ShengPinRender, self.node_list["ph_item_list"])
    XUI.AddClickEventListener(self.node_list["sure_btn"], BindTool.Bind(self.OnClickSure, self))
    self.node_list.tip_text.text.text = Language.MountPetEquip.QuickDes
    self.node_list.lbl_tips.text.text =  Language.MountPetEquip.QuickHasNoData
    self.node_list.all_mark.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickAllSelect, self))
end

function MountLingChongEquipQuickSp:OnClickAllSelect(is_on)
    local item_list = MountLingChongEquipWGData.Instance:GetShengpinMutiList(self.cur_show_type)
    if not is_on then
        MountLingChongEquipWGData.Instance:ResetMutiSelect()
    else
        if #item_list > 0 then
            for i = 1, #item_list do
                MountLingChongEquipWGData.Instance:SaveMutiSelect(i, true)
            end
        end
    end
    self:Flush()
end

function MountLingChongEquipQuickSp:ShowIndexCallBack(index)
    MountLingChongEquipWGData.Instance:ResetMutiSelect()
    self.node_list.all_mark.toggle.isOn = true
    self:OnClickAllSelect(true)
end

function MountLingChongEquipQuickSp:OnClickSure()
    local muti_list = MountLingChongEquipWGData.Instance:GetMutiSelect()
    local be_select_list = {}
    for k, v in pairs(muti_list) do
        if v then
            be_select_list[#be_select_list+1] = k
        end
    end
    if #be_select_list == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.LimitOne)
        return
    end
    local data_list = MountLingChongEquipWGData.Instance:GetShengpinMutiList(self.cur_show_type)
    for k, v in pairs(be_select_list) do
        local info = data_list[v]
        if not IsEmptyTable(info) then
            local equip_cfg, show_type, part = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(info.cur_item_id)
            local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(info.cur_item_id)
            if info.use_wear_part then
                part = part
            else
                part = -1
            end
            local consume_grid_index_list = {}
            for k, v in pairs(info.stuff_item_list) do
                consume_grid_index_list[#consume_grid_index_list+1] = v.cur_grid_index
            end
            self:CancleShengPinTimes()
            self.is_click_sure = true
            self.node_list.all_mark.toggle.isOn = false
            MountLingchongEquipWGCtrl.Instance:SendMountPetEquipShengPinReq(self.cur_show_type, 
            target_cfg.compose_equip_id, part, consume_grid_index_list)
        end
    end
    MountLingChongEquipWGData.Instance:ResetMutiSelect()
end

function MountLingChongEquipQuickSp:OnFlush(params)
    for k, v in pairs(params) do
        if k == "from_protocol" then
            MountLingChongEquipWGData.Instance:ResetMutiSelect()
            self.node_list.all_mark.toggle.isOn = true
            self:OnClickAllSelect(true)
        end
    end
    local data_list = MountLingChongEquipWGData.Instance:GetShengpinMutiList(self.cur_show_type)
    self.item_list:SetDataList(data_list)
    self.node_list.img_no_record:SetActive(#data_list==0)
    self.node_list.all_mark:SetActive(#data_list > 0)
end

function MountLingChongEquipQuickSp:GetIsClickSure()
    return self.is_click_sure
end

function MountLingChongEquipQuickSp:CancleShengPinTimes()
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
    end
    self.is_play_effect = false
    self.delay_timer = nil
end

function MountLingChongEquipQuickSp:PlayShengPinSuccess()
    if self.node_list.shengpin_success_root then
        if not self.is_play_effect then
            self.is_play_effect = true
            self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
                self.is_play_effect = false
                self.delay_timer = nil
                self.is_click_sure = false
            end, 1)
            self.node_list.shengpin_success_root:SetActive(true)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengpin, is_success = true, pos = Vector2(0, 0),
                parent_node = self.node_list["shengpin_success_root"]})
        end
    end
end

-------------------------------------------------------------------------------------------
ShengPinRender = ShengPinRender or BaseClass(BaseRender)

function ShengPinRender:__init(instance)

end

function ShengPinRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if self.target_item then
        self.target_item:DeleteMe()
        self.target_item = nil
    end

    if self.must_item then
        self.must_item:DeleteMe()
        self.must_item = nil
    end
end

function ShengPinRender:LoadCallBack()
    self.item_list = AsyncListView.New(ItemCell, self.node_list.scroll)
    self.target_item = ItemCell.New(self.node_list["target_item"])
    self.must_item = ItemCell.New(self.node_list["must_item"])
    self.node_list.check_mark.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self))
end

function ShengPinRender:OnClickToggle(is_on)
    MountLingChongEquipWGData.Instance:SaveMutiSelect(self.index, is_on)
end

function ShengPinRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_on = MountLingChongEquipWGData.Instance:GetMutiSelectIndex(self.index)
    self.node_list.check_mark.toggle.isOn = is_on
    self.node_list.wear:SetActive(self.data.use_wear_part)
    local must = {}
    must.item_id = self.data.cur_item_id
    self.must_item:SetData(must)
    local target_cfg = MountLingChongEquipWGData.Instance:GetComposeCfgByItemId(self.data.cur_item_id)

    if not IsEmptyTable(target_cfg) then
        local target = {}
        target.item_id = target_cfg.compose_equip_id
        self.target_item:SetData(target)
        self.item_list:SetDataList(self.data.stuff_item_list)

        if self.node_list.scroll and self.node_list.scroll.scroll_rect then
            self.node_list.scroll.scroll_rect.horizontalNormalizedPosition = 0
        end
    end
end