function QiFuView:InitWelfareQiFuView()
	self.qifu_item = {}
	for i = 1, 2 do
		self.qifu_item[i] = WelfareNewQiFuRender.New(self.node_list["ph_qifu_cell_"..i])
		self.qifu_item[i]:SetMoneyBar(self.money_bar)

		self.qifu_item[i]:SetIndex(i)
	end

	self.node_list.vip_show_tips_tbn.button:AddClickListener(BindTool.Bind(self.OnClickOpenAllPrivilegeTipsBtn, self))
	self.node_list.btn_qifu_goto.button:AddClickListener(BindTool.Bind(self.OnClickOpenVipBtn, self))

	--self:InitCJTweenState()
	self:Flush()
end

function QiFuView:DelWelfareQiFuView()
	if self.qifu_item then
		for k,v in ipairs(self.qifu_item)do
			v:DeleteMe()
		end

		self.qifu_item = nil
	end
end

-- function QiFuView:InitCJTweenState()
-- 	self.do_qi_fu_tween = true
-- 	RectTransform.SetAnchoredPositionXY(self.node_list.lu.rect, 218, 180)
-- 	RectTransform.SetAnchoredPositionXY(self.node_list.ding.rect, -226, 180)
-- 	--RectTransform.SetAnchoredPositionXY(self.node_list.VerticalTabbarContent.rect, 35, 125)
-- 	UITween.FakeHideShow(self.node_list.ding)
-- 	UITween.FakeHideShow(self.node_list.lu)
-- 	UITween.FakeHideShow(self.node_list.root0)
-- 	UITween.FakeHideShow(self.node_list.root1)
-- 	--UITween.FakeHideShow(self.node_list.VerticalTabbarContent)
-- end

-- function QiFuView:ReShowCJBeHiden()
-- 	local tween_info = UITween_CONSTS.AchievementSys
-- 	UITween.AlphaShow(GuideModuleName.QIFU,self.node_list.root0, 0, 1, tween_info.AlphaTime)
-- end

-- function QiFuView:ReShowCJBeHiden1()
-- 	local tween_info = UITween_CONSTS.AchievementSys
-- 	UITween.AlphaShow(GuideModuleName.QIFU,self.node_list.root1, 0, 1, tween_info.AlphaTime)
-- end

-- function QiFuView:QiFuViewAnimation()
-- 	if not self.do_qi_fu_tween then
-- 		return
-- 	end
-- 	UITween.CleanAllTween(GuideModuleName.QIFU)
-- 	local tween_info = UITween_CONSTS.QiFuSys
-- 	local Ver_Tween = UITween_CONSTS.QiFuSys.VerTween
-- 	self.node_list.ding.rect:DOAnchorPos(Vector2(-226, 29), tween_info.MoveTime)
-- 	--self.node_list.VerticalTabbarContent.rect:DOAnchorPos(Vector2(35, -70), Ver_Tween.MoveTweenTime)
-- 	UITween.AlphaShow(GuideModuleName.QIFU,self.node_list.ding, 0, 1, tween_info.AlphaTime, nil, BindTool.Bind(self.ReShowCJBeHiden, self))

-- 	ReDelayCall(self, function()
-- 		UITween.AlphaShow(GuideModuleName.QIFU,self.node_list.lu, 0, 1, tween_info.AlphaTime, nil, BindTool.Bind(self.ReShowCJBeHiden1, self))
-- 		self.node_list.lu.rect:DOAnchorPos(Vector2(218, 12), tween_info.MoveTime)
-- 	end, tween_info.NextDoDelay, "qi_fu1")

-- 	ReDelayCall(self, function()
-- 		UITween.AlphaShow(GuideModuleName.QIFU,self.node_list.VerticalTabbarContent, 0, 1, Ver_Tween.AlphaTweenTime)
-- 	end, Ver_Tween.NextDoDelay, "qi_fu")

-- 	self.do_qi_fu_tween = false
-- end

function QiFuView:FlushWelfareQiFuView()
	local exe_level = VipWGCtrl.Instance:GetQiFuLevel() or 0
	local qifu_level_max = ConfigManager.Instance:GetAutoConfig("vip_auto").other[1].qifu_level_max
	local data_list = QiFuWGData.Instance:GetWelfareQiFuDataList()

	if not data_list or IsEmptyTable(data_list) then
		return
	end

	for k,v in ipairs(self.qifu_item)do
		v:SetData(data_list[k])
	end

	local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	local qifu_tequan = LongXiWGData.Instance:GetIsShowPrivilegeDesc()
	--self:QiFuViewAnimation()
end

-- 打开特权总览界面.
function QiFuView:OnClickOpenAllPrivilegeTipsBtn()
	RechargeWGCtrl.Instance:OpenAllPrivilegeTips()
end

-- 打开特权总览界面.
function QiFuView:OnClickOpenVipBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
end

function QiFuView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
    if ui_name == "btn_qifu_goto" then
        local fun = function()
            self:OnClickOpenVipBtn()
        end

        return self.node_list[ui_name], fun
    else
        return self.node_list[ui_name]
    end
end

--------------------------------------------------------------

WelfareNewQiFuRender = WelfareNewQiFuRender or BaseClass(BaseRender)
function WelfareNewQiFuRender:__init()

end

function WelfareNewQiFuRender:__delete()
	if CountDownManager.Instance:HasCountDown("welfart_qifu_cion_free") then
		CountDownManager.Instance:RemoveCountDown("welfart_qifu_cion_free")
	end
	if self.gold_buy_alert ~= nil then
		self.gold_buy_alert:DeleteMe()
		self.gold_buy_alert = nil
	end

	if self.alert_all_tips then
		self.alert_all_tips:DeleteMe()
		self.alert_all_tips = nil
	end

	if self.alert_all_tips1 then
		self.alert_all_tips1:DeleteMe()
		self.alert_all_tips1 = nil
	end

	if self.btn_effect then
		self.btn_effect = nil
	end
	if self.baoji_effect then
		self.baoji_effect = nil
	end
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
	if self.exp_buy_alert then
		self.exp_buy_alert:DeleteMe()
		self.exp_buy_alert = nil
	end
	self.layout_gold_buy = nil
	self.money_bar = nil
	if self.add_delay_money_effect_event then
		GlobalTimerQuest:CancelQuest(self.add_delay_money_effect_event)
		self.add_delay_money_effect_event = nil
	end
	--[[if self.money_change_event then
		GlobalEventSystem:UnBind(self.money_change_event)
		self.money_change_event = nil
	end--]]
	if self.vip_countdown_event_1 then
		GlobalTimerQuest:CancelQuest(self.vip_countdown_event_1)
		self.vip_countdown_event_1 = nil
	end

	if self.skeleton_graphic_yao_time_quest then
		GlobalTimerQuest:CancelQuest(self.skeleton_graphic_yao_time_quest)
		self.skeleton_graphic_yao_time_quest = nil
	end


	if self.delay_timer then
		GlobalTimerQuest:CancelQuest(self.delay_timer)
		self.delay_timer = nil
	end

	if self.delay_timer1 then
		GlobalTimerQuest:CancelQuest(self.delay_timer1)
		self.delay_timer1 = nil
	end

	if self.delay_timer2 then
		GlobalTimerQuest:CancelQuest(self.delay_timer2)
		self.delay_timer2 = nil
	end

end

function WelfareNewQiFuRender:CreateAlert()
	if not self.alert_all_tips then
		self.alert_all_tips = Alert.New()
	end

	if not self.alert_all_tips1 then
		self.alert_all_tips1 = Alert.New()
	end

end

function WelfareNewQiFuRender:LoadCallBack()
	self.layout_gold_buy = self.node_list.layout_gold_buy_text
	XUI.AddClickEventListener(self.node_list.btn_qifu_receive, BindTool.Bind1(self.OnClickQifuReceive, self))
	--[[if not self.money_change_event then
		self.money_change_event = GlobalEventSystem:Bind(OtherEventType.QiFu_Gold_Change_Event,BindTool.Bind(self.QiFuMoneyChangeBack, self))
	end--]]

	-- -- 创建提示框
	self:CreateAlert()
end

function WelfareNewQiFuRender:OnFlush()
	-- self.node_list.img_remind:SetActive(false)
	local data = self:GetData()
	self.node_list.img_qf_remind:SetActive(false)

	if data == nil then return end

	local qf_reward_text = ""
	local server_time = math.floor(TimeWGCtrl.Instance:GetServerTime())
	local temp_vip = VipWGData.Instance:GetRoleVipLevel()
	if self.index == 1 then
		local num = QiFuWGData.Instance:GetWelfareQiFuCoinReward()
		qf_reward_text = num .. Language.Common.Coin
		if VipWGData.Instance:IsVip() then
			if nil == self.vip_countdown_event_1 then
				self.vip_countdown_event_1 = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.VipCountDownUpdateCallBack, self), 1)
			end
		end

	elseif self.index == 2 then
		local num, max_level = QiFuWGData.Instance:GetWelfareQiFuExpReward()
		local str = CommonDataManager.ConverExpByThousand(num, nil, false)
		qf_reward_text = str .. Language.Common.Exp
		local vo = GameVoManager.Instance:GetMainRoleVo()
		local new_vo_exp = vo.exp + num
		local max_exp = RoleWGData.GetRoleExpCfgByLv(vo.level).exp
		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level >= max_level then
			self.node_list.label_qf_reward_desc.text.text = ""
			self.node_list.qifu_exp_add_1:SetActive(false)
			self.node_list.qifu_exp_add_2:SetActive(false)
		else
			if new_vo_exp >= max_exp then
				local enough_exp = new_vo_exp - max_exp
				local add_level = 1
				for i=1, 50 do
					local max_exp = RoleWGData.GetRoleExpCfgByLv(vo.level + add_level).exp
					if enough_exp > max_exp then
						enough_exp = enough_exp - max_exp
						add_level = add_level + 1
					else
						break
					end
				end
				if vo.level + add_level >= max_level then
					add_level = max_level - vo.level
				end
				--self.node_list.qifu_exp_add_percent_level.text.text = add_level
				self.node_list.qifu_exp_add_1.text.text = string.format(Language.QiFu.QiFuWishUpLevel, add_level)

				self.node_list.qifu_exp_add_1:SetActive(true)
				self.node_list.qifu_exp_add_2:SetActive(false)
			else
				self.node_list.qifu_exp_add_1:SetActive(false)
				self.node_list.qifu_exp_add_2:SetActive(true)
				local per_exp = math.ceil((100 * new_vo_exp) /max_exp)
				--self.node_list.qifu_exp_add_percent.text.text = per_exp --string.format(Language.Welfare.QiFuAddPerExp,per_exp)
				self.node_list.qifu_exp_add_2.text.text = string.format(Language.QiFu.QiFuWishExp,per_exp)
			end
		end

	end
	self.node_list.label_qf_reward.text.text = string.format(Language.QiFu.QiFuGet, qf_reward_text)

	if server_time > data.free_buycoin_time and self.index == 1 then
		self.node_list.layout_gold_buy_text:SetActive(false)
		self.node_list.img_free_buycoin:SetActive(true)
		self.node_list.img_qf_remind:SetActive(true)
		self.node_list.btn_qifu_text.text.text = Language.QiFu.Btn_QF_Name2
	else
		self.node_list.layout_gold_buy_text:SetActive(true)
		self.node_list.img_free_buycoin:SetActive(false)
		local num_data, gold = 0, 0
		local buy_cost_data = QiFuWGData.Instance:GetWelfareQiFuBuyCost(data.gold_buy_times + 1)
		if self.index == 1 then
			local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
			local qifu_tequan = LongXiWGData.Instance:GetIsShowPrivilegeDesc()
			num_data = VipPower.Instance:GetParam(VipPowerId.pray_coin_times,temp_vip)
			if data.is_active > 0 and qifu_tequan then
				gold = buy_cost_data.active_longxi_buy_silver_cost
			else
				gold = buy_cost_data.buy_silver_cost
			end
			self.node_list.btn_qifu_text.text.text = Language.QiFu.Btn_QF_Name
		elseif self.index == 2 then
			num_data = VipPower.Instance:GetParam(VipPowerId.pray_exp_times,temp_vip)
			gold = buy_cost_data.buy_exp_cost
		end

		self.end_show_num = num_data - data.gold_buy_times > 0 and num_data - data.gold_buy_times or 0
		self.node_list.label_qf_gold_num.text.text = self.end_show_num .. "/" .. num_data
		self.node_list.label_qf_gold.text.text = gold
	end


	local function flushQFFreeTimeShow(elapse_time, total_time)
		local time = total_time - elapse_time
		if time > 0 then
			self.node_list.label_qf_free.text.text = TimeUtil.FormatSecond2HMS(time)..Language.MiJingTaoBao.HouMianFei
		end
	end

	local function completeQFFreeTimeShow()
		RemindManager.Instance:Fire(RemindName.WelfareQiFu)--祈福
		--self:Flush()
		self.node_list.layout_gold_buy_text:SetActive(false)
		self.node_list.img_free_buycoin:SetActive(true)
		self.node_list.img_qf_remind:SetActive(true)
		self.node_list.btn_qifu_text.text.text = Language.QiFu.Btn_QF_Name2
		self.node_list.label_qf_free.text.text = ""
		return
	end

	if CountDownManager.Instance:HasCountDown("welfart_qifu_cion_free") and self.index == 1 then
		CountDownManager.Instance:RemoveCountDown("welfart_qifu_cion_free")
	end

	if server_time < data.free_buycoin_time and self.index == 1 then
		local total_time = data.free_buycoin_time - server_time
		flushQFFreeTimeShow(0, total_time)
		CountDownManager.Instance:AddCountDown("welfart_qifu_cion_free", flushQFFreeTimeShow, completeQFFreeTimeShow, data.free_buycoin_time, nil, 1)
	else
		RemindManager.Instance:Fire(RemindName.WelfareQiFu)--祈福
		self.node_list.label_qf_free.text.text = ""
	end
end

function WelfareNewQiFuRender:VipCountDownUpdateCallBack(elapse_time, total_time)
	local end_time = VipWGData.Instance:GetVipEndTime()
	local time = end_time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then

	else
		if self.vip_countdown_event_1 then
			GlobalTimerQuest:CancelQuest(self.vip_countdown_event_1)
			self.vip_countdown_event_1 = nil
		end
		self:Flush()
	end
end

function WelfareNewQiFuRender:OnClickQifuReceive1()
	TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.Welfare)
	if self.money_bar and self.index == 1 then
	--[[	FightWGCtrl.Instance:DoMoneyEffect(GameEnum.NEW_MONEY_BAR.COIN, qifu_effect_1)
		self.delay_timer1 = GlobalTimerQuest:AddDelayTimer(function()
		local bundle, asset = ResPath.GetEffectUi(MONEY_BAR_EFFECT[GameEnum.NEW_MONEY_BAR.COIN].FLY)
		local end_obj = self.money_bar:GetSlicketNode(GameEnum.NEW_MONEY_BAR.COIN)
		if end_obj then
			TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Welfare, bundle, asset, self.node_list.qifu_effect_1_1, end_obj, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, 18, 200, nil, false, true)
		end
		GlobalTimerQuest:CancelQuest(self.delay_timer1)
		self.delay_timer1 = nil
		end, 0.5)--]]

	elseif self.index == 2 then
		local exp_view = MainuiWGCtrl.Instance:GetView():GetExpView()
		self.delay_timer2 = GlobalTimerQuest:AddDelayTimer(function()
			if exp_view and exp_view.exp_effect then
				local end_pos = exp_view.exp_effect
				local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jinyanqiu_guangqiu)
    			TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Welfare, bundle_name, asset_name, self.node_list.qifu_effect_2, end_pos, DG.Tweening.Ease.OutCubic, 1, nil, nil, nil, 200)
    			AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectJingYanQiFu, false, true))
    		end
    	GlobalTimerQuest:CancelQuest(self.delay_timer2)
		self.delay_timer2 = nil
		end, 0.8)
	end

	--if nil == self.delay_timer then
		--self.node_list.UI_mao.animator:SetTrigger("play")
		--self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		--self.node_list.UI_mao.animator:SetBool("play", true)
		--self.node_list.glow:SetActive(false)
		--GlobalTimerQuest:CancelQuest(self.delay_timer)
		--self.delay_timer = nil
		--end, 0.8)
	--end
end

function WelfareNewQiFuRender:QiFuMoneyChangeBack()
	if self.index ~= 1 then return end
	local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanbaodiaoluo")

	if self.add_delay_money_effect_event then
		GlobalTimerQuest:CancelQuest(self.add_delay_money_effect_event)
		self.add_delay_money_effect_event = nil
		TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.Welfare)
	end

	EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.qifu_effect_1.transform, 1, nil, nil, nil)
	-- AudioManager.PlayAndForget(ResPath.GetUisVoiceRes(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectXianYuHuoDe)))
	QiFuWGData.Instance:SetTodayDoFlag("QiFuGold")
end

function WelfareNewQiFuRender:OnClickQifuReceive()
	local data = self:GetData()
	if data == nil then return end
	local server_time = math.floor(TimeWGCtrl.Instance:GetServerTime())
	if server_time > data.free_buycoin_time and self.index == 1 then
		WelfareWGCtrl.Instance:SendQiFuReq(WELFARE_QIFU_TYPE.QIFU_TYPE_BUYCOIN)
		-- self:OnClickQifuReceive1()
		--免费祈福
		return
	end

	local is_active = VipWGData.Instance:IsVip()
	if not(self.end_show_num and self.end_show_num > 0) and not is_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.BaoZhuNotActive)
		return
	end

	local num_data = 0
	local buy_cost_data = QiFuWGData.Instance:GetWelfareQiFuBuyCost(data.gold_buy_times + 1)
	if not buy_cost_data then
		return
	end

	local buy_type = 0
	local yuanbao, xianyu = 0, 0
	if self.index == 1 then
		local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
		local qifu_tequan = LongXiWGData.Instance:GetIsShowPrivilegeDesc()
		num_data = VipPower.Instance:GetParam(VipPowerId.pray_coin_times)
		self.gold = buy_cost_data.buy_silver_cost

		if data.is_active > 0 and qifu_tequan then
			yuanbao = buy_cost_data.active_longxi_buy_silver_cost
		else
			yuanbao = buy_cost_data.buy_silver_cost
		end

		buy_type = WELFARE_QIFU_TYPE.QIFU_TYPE_BUYCOIN
	elseif self.index == 2 then
		num_data = VipPower.Instance:GetParam(VipPowerId.pray_exp_times)
		self.gold = buy_cost_data.buy_exp_cost
		xianyu = buy_cost_data.buy_exp_cost
		buy_type = WELFARE_QIFU_TYPE.QIFU_TYPE_BUYEXP
	end

	if data.gold_buy_times >= num_data then
		if  self.index == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.TBVipTips)
		else
			local vip = VipWGData.Instance:GetRoleVipLevel()
			local maxvip = VipWGData.Instance:GetMaxVIPLevel()
			--local maxlevel = 1
			if vip < maxvip then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.TBVipTips)
				return
			end
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.EXPVipTips)
		end
		return
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local num, max_level = QiFuWGData.Instance:GetWelfareQiFuExpReward()
	if self.index ~= 1 and role_level >= max_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.EXPMaxLevelTips)
		return
	end

	local is_cun = QiFuWGCtrl.Instance:GetWelfareQiFuCheckBox()
	local is_cun1 = QiFuWGCtrl.Instance:GetWelfareQiFuCheckBox1()
	if not is_cun and self.index == 1 then
		self:OnClickQifu(buy_type)
		return
	elseif self.index == 2 and not is_cun1 then
		self:OnClickQifu(buy_type)
		return
 	end

	if self.index == 1 then
		self.alert_all_tips:SetOkFunc(BindTool.Bind(self.OnClickQifu, self, buy_type))
		self.alert_all_tips:SetLableString(string.format(Language.Welfare.WelfareRender13, yuanbao))
		self.alert_all_tips:SetShowCheckBox(true)
		self.alert_all_tips:SetCheckBoxDefaultSelect(false)
		self.alert_all_tips:Open()
	else
		self.alert_all_tips1:SetOkFunc(BindTool.Bind(self.OnClickQifu, self, buy_type))
		self.alert_all_tips1:SetLableString(string.format(Language.Welfare.WelfareRender14, xianyu))
		self.alert_all_tips1:SetShowCheckBox(true)
		self.alert_all_tips1:SetCheckBoxDefaultSelect(false)
		self.alert_all_tips1:Open()
	end
end

function WelfareNewQiFuRender:OnClickTeXiao()
--添加特效部分
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	if main_role_vo.gold >= self.gold then
		--if self.index == 1 then
			-- self:OnClickQifuReceive1()
		--elseif self.index == 2 then
			--[[local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jinyanqiu_zhakai)
			EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.qifu_effect_2.transform, 3)--]]
			--[[local bundle, asset = ResPath.UiseRes("effect_jingyanqifu")
			AudioManager.PlayAndForget(bundle, asset)--]]
			QiFuWGData.Instance:SetTodayDoFlag("QiFuExp")
		--end
	end
end

function WelfareNewQiFuRender:OnClickQifu(buy_type)
	local check_box = self.alert_all_tips:SetCheckBoxState()
	local check_box1 = self.alert_all_tips1:SetCheckBoxState()
	if check_box then
		QiFuWGCtrl.Instance:SetWelfareQiFuCheckBox(not check_box)
	end
	if check_box1 then
		QiFuWGCtrl.Instance:SetWelfareQiFuCheckBox1(not check_box1)
	end
	self:OnClickTeXiao()
    WelfareWGCtrl.Instance:SendQiFuReq(buy_type)
end

function WelfareNewQiFuRender:CreateSelectEffect()

end
function WelfareNewQiFuRender:SetMoneyBar(money_bar)
	self.money_bar = money_bar
end
