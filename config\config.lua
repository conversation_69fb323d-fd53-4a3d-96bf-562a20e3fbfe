
Config = Config or {}

local lua_file_list = {
	"config/config_client_test",
	"config/config_chatfilter",				-- 聊天内容过滤
	"config/config_usernamefilter",			-- 用户名过滤
	"config/config_decoration",
	"config/config_jump",
	"config/config_other",
	"config/config_map",
	"config/config_robert_chat",
	"config/multi_mount_pos_config",

	-- 常用大配置表(常驻内存)
	"config/auto_new/item/buff_auto",
	"config/auto_new/item/equipment_auto",
	"config/auto_new/item/expense_auto",
	"config/auto_new/item/gift_auto",
	"config/auto_new/item/other_auto",
	"config/auto_new/item/virtual_auto",
	"config/auto_new/effect_config_auto",
	"config/auto_new/equipforge_auto",
	"config/auto_new/globalconfig_auto",
	"config/auto_new/monstercard_auto",
	"config/auto_new/monster_auto",
	"config/auto_new/npc_talk_list_auto",
	"config/auto_new/qingyuanconfig_auto",
	"config/auto_new/randactivityconfig2_auto",
	"config/auto_new/randactivityconfig2_ios_auto",
	"config/auto_new/randactivityconfig_1_auto",
	"config/auto_new/randactivityconfig_2_auto",
	"config/auto_new/randactivityconfig_3_auto",
	"config/auto_new/randactivityconfig_auto",
	"config/auto_new/randactivityconfig_ios_auto",
	"config/auto_new/roleexp_auto",
	"config/auto_new/roleskill_auto",
	"config/auto_new/story_auto",
	"config/auto_new/tasklist_auto",
	"config/auto_new/npc_auto",
	"config/auto_new/juhun_cfg_auto",
	"config/auto_new/caishencifuconfig_auto",
	"config/auto_new/caishencifuconfig_auto",
}

return lua_file_list