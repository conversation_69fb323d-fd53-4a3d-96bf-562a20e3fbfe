{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8010_attack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8010/boss8010_attack_prefab", "AssetName": "boss8010_attack", "AssetGUID": "ba2ebf2fb1468f34a8c741757a6711e9", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": -0.5, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8010_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8010/boss8010_skill_prefab", "AssetName": "boss8010_skill", "AssetGUID": "0af8d91cd4289444588fbcd80f041f56", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": -1.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": true}], "sounds": [], "cameraShakes": [], "radialBlurs": []}}