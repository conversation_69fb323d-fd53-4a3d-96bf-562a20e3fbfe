-- T-淘宝.xls
local item_table={
[1]={item_id=27700,num=5,is_bind=0},
[2]={item_id=26200,num=3,is_bind=0},
[3]={item_id=26203,num=3,is_bind=0},
[4]={item_id=26415,num=10,is_bind=0},
[5]={item_id=26344,num=3,is_bind=0},
[6]={item_id=26346,num=3,is_bind=0},
[7]={item_id=26355,num=2,is_bind=0},
[8]={item_id=26356,num=2,is_bind=0},
[9]={item_id=26503,num=1,is_bind=0},
[10]={item_id=26367,num=3,is_bind=0},
[11]={item_id=26201,num=2,is_bind=0},
[12]={item_id=26204,num=2,is_bind=0},
[13]={item_id=29101,num=1,is_bind=0},
[14]={item_id=26368,num=3,is_bind=0},
[15]={item_id=26608,num=1,is_bind=0},
[16]={item_id=27719,num=1,is_bind=0},
[17]={item_id=26347,num=2,is_bind=0},
[18]={item_id=26668,num=1,is_bind=0},
[19]={item_id=26578,num=1,is_bind=0},
[20]={item_id=27702,num=1,is_bind=0},
[21]={item_id=48044,num=1,is_bind=0},
[22]={item_id=26345,num=1,is_bind=0},
[23]={item_id=26201,num=3,is_bind=0},
[24]={item_id=26349,num=5,is_bind=0},
[25]={item_id=26653,num=1,is_bind=0},
[26]={item_id=36242,num=1,is_bind=0},
[27]={item_id=28029,num=1,is_bind=0},
[28]={item_id=26415,num=20,is_bind=0},
[29]={item_id=27611,num=5,is_bind=0},
[30]={item_id=26358,num=3,is_bind=0},
[31]={item_id=27820,num=3,is_bind=0},
[32]={item_id=26350,num=2,is_bind=0},
[33]={item_id=27612,num=2,is_bind=0},
[34]={item_id=26350,num=3,is_bind=0},
[35]={item_id=26204,num=3,is_bind=0},
[36]={item_id=27612,num=3,is_bind=0},
[37]={item_id=27821,num=3,is_bind=0},
[38]={item_id=26359,num=3,is_bind=0},
[39]={item_id=27720,num=1,is_bind=0},
[40]={item_id=26548,num=1,is_bind=0},
[41]={item_id=26533,num=1,is_bind=0},
[42]={item_id=26638,num=1,is_bind=0},
[43]={item_id=26201,num=5,is_bind=0},
[44]={item_id=26204,num=5,is_bind=0},
[45]={item_id=27712,num=1,is_bind=0},
}

return {
layer={
{},
{draw_times="6,10",consume_item_num=2,},
{draw_times="11,15",consume_item_num=3,},
{draw_times="16,20",consume_item_num=4,},
{draw_times="21,25",consume_item_num=5,},
{layer=1,consume_item_id=29101,shop_seq=10021,},
{layer=1,},
{layer=1,},
{draw_times="16,20",consume_item_num=4,},
{draw_times="21,25",consume_item_num=5,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=3,},
{draw_times="6,10",consume_item_num=2,},
{draw_times="11,15",consume_item_num=3,},
{layer=3,},
{layer=3,}
},

layer_meta_table_map={
[16]=6,	-- depth:1
[15]=5,	-- depth:1
[12]=2,	-- depth:1
[13]=3,	-- depth:1
[14]=4,	-- depth:1
[18]=16,	-- depth:2
[17]=18,	-- depth:3
[10]=6,	-- depth:1
[9]=10,	-- depth:2
[8]=18,	-- depth:3
[7]=17,	-- depth:4
[19]=9,	-- depth:3
[20]=10,	-- depth:2
},
reward={
{reward_item=item_table[1],},
{reward_id=2,reward_item=item_table[2],pos=12,},
{reward_id=3,reward_item=item_table[3],pos=23,},
{reward_id=4,reward_item=item_table[4],pos=34,},
{reward_id=5,reward_item=item_table[5],pos=2,},
{reward_id=6,reward_item=item_table[6],pos=10,},
{reward_id=7,reward_item=item_table[7],pos=11,effect_level=2,},
{reward_id=8,reward_item=item_table[8],pos=22,effect_level=2,},
{reward_id=9,reward_item=item_table[9],pos=33,big_reward=5,},
{reward_id=10,pos=44,big_reward=8,is_shake=1,},
{reward_id=11,reward_item=item_table[10],pos=3,effect_level=2,},
{reward_id=12,reward_item=item_table[11],pos=14,effect_level=2,},
{reward_id=13,reward_item=item_table[12],pos=25,effect_level=2,},
{reward_id=14,reward_item=item_table[13],pos=36,},
{reward_id=15,reward_item=item_table[14],pos=9,effect_level=2,},
{reward_id=16,reward_item=item_table[15],pos=20,big_reward=6,},
{reward_id=17,reward_item=item_table[16],pos=31,big_reward=9,is_shake=1,effect_level=2,},
{reward_id=18,reward_item=item_table[17],pos=42,effect_level=2,},
{reward_id=19,reward_item=item_table[18],pos=37,},
{reward_id=20,reward_item=item_table[19],pos=41,big_reward=7,},
{reward_id=21,reward_item=item_table[20],pos=27,effect_level=3,},
{reward_id=22,reward_item=item_table[21],pos=17,big_reward=10,is_shake=1,effect_level=3,},
{reward_id=23,reward_item=item_table[22],pos=38,effect_level=2,},
{reward_id=24,reward_item=item_table[23],pos=29,effect_level=2,},
{reward_id=25,pos=40,},
{layer=1,reward_item=item_table[24],},
{layer=1,},
{layer=1,},
{layer=1,},
{layer=1,},
{layer=1,},
{reward_id=7,reward_item=item_table[16],pos=11,big_reward=7,},
{layer=1,},
{layer=1,reward_item=item_table[25],big_reward=2,},
{layer=1,big_reward=6,},
{layer=1,},
{layer=1,},
{layer=1,},
{layer=1,},
{layer=1,},
{layer=1,},
{layer=1,big_reward=9,},
{layer=1,big_reward=3,},
{layer=1,},
{layer=1,big_reward=4,},
{layer=1,big_reward=5,},
{layer=1,reward_item=item_table[26],pos=39,effect_level=4,},
{layer=1,reward_id=23,reward_item=item_table[27],pos=27,big_reward=8,is_shake=1,effect_level=2,},
{layer=1,effect_level=2,},
{layer=1,effect_level=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,big_reward=9,},
{layer=2,},
{layer=2,reward_id=12,reward_item=item_table[11],pos=14,},
{layer=2,reward_id=13,reward_item=item_table[12],pos=25,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,big_reward=4,},
{layer=2,},
{layer=2,big_reward=7,},
{layer=2,big_reward=8,},
{layer=2,},
{layer=2,},
{layer=2,},
{layer=2,reward_id=24,reward_item=item_table[23],pos=29,},
{reward_id=25,pos=40,},
{layer=3,},
{layer=3,reward_item=item_table[28],},
{layer=3,reward_item=item_table[29],},
{layer=3,reward_item=item_table[30],effect_level=2,},
{reward_id=5,reward_item=item_table[31],pos=35,},
{reward_id=6,reward_item=item_table[32],pos=43,},
{layer=3,big_reward=2,},
{layer=3,reward_item=item_table[33],},
{layer=3,big_reward=3,},
{layer=3,big_reward=7,},
{layer=3,reward_item=item_table[34],},
{layer=3,reward_item=item_table[35],},
{layer=3,reward_item=item_table[36],},
{layer=3,reward_item=item_table[23],},
{layer=3,reward_item=item_table[37],},
{reward_id=16,reward_item=item_table[38],pos=20,},
{layer=3,reward_item=item_table[39],big_reward=8,effect_level=3,},
{reward_id=18,reward_item=item_table[40],pos=42,big_reward=4,},
{layer=3,reward_id=19,pos=16,},
{layer=3,reward_item=item_table[41],pos=37,big_reward=5,},
{layer=3,reward_id=21,reward_item=item_table[42],big_reward=6,},
{layer=3,},
{layer=3,big_reward=9,},
{layer=3,reward_id=24,reward_item=item_table[43],pos=18,},
{layer=3,reward_id=25,reward_item=item_table[44],pos=29,}
},

reward_meta_table_map={
[51]=1,	-- depth:1
[76]=26,	-- depth:1
[89]=14,	-- depth:1
[39]=89,	-- depth:2
[52]=2,	-- depth:1
[53]=3,	-- depth:1
[54]=4,	-- depth:1
[55]=5,	-- depth:1
[64]=14,	-- depth:1
[75]=63,	-- depth:1
[77]=2,	-- depth:1
[78]=3,	-- depth:1
[87]=62,	-- depth:1
[56]=6,	-- depth:1
[37]=87,	-- depth:2
[25]=13,	-- depth:1
[27]=77,	-- depth:2
[28]=78,	-- depth:2
[71]=21,	-- depth:1
[68]=18,	-- depth:1
[65]=15,	-- depth:1
[73]=23,	-- depth:1
[9]=10,	-- depth:1
[79]=4,	-- depth:1
[80]=79,	-- depth:2
[81]=79,	-- depth:2
[83]=8,	-- depth:1
[85]=10,	-- depth:1
[86]=11,	-- depth:1
[88]=13,	-- depth:1
[90]=15,	-- depth:1
[91]=90,	-- depth:2
[94]=21,	-- depth:1
[16]=10,	-- depth:1
[61]=11,	-- depth:1
[29]=79,	-- depth:2
[50]=100,	-- depth:1
[40]=90,	-- depth:2
[44]=94,	-- depth:2
[38]=88,	-- depth:2
[36]=86,	-- depth:2
[35]=10,	-- depth:1
[49]=99,	-- depth:1
[60]=10,	-- depth:1
[31]=81,	-- depth:3
[33]=83,	-- depth:2
[20]=10,	-- depth:1
[30]=80,	-- depth:3
[19]=16,	-- depth:2
[57]=7,	-- depth:1
[58]=8,	-- depth:1
[41]=91,	-- depth:3
[34]=9,	-- depth:2
[95]=20,	-- depth:2
[84]=34,	-- depth:3
[96]=20,	-- depth:2
[93]=85,	-- depth:2
[59]=9,	-- depth:2
[43]=93,	-- depth:3
[66]=16,	-- depth:2
[69]=19,	-- depth:3
[70]=20,	-- depth:2
[46]=96,	-- depth:3
[45]=95,	-- depth:3
[32]=48,	-- depth:1
[82]=32,	-- depth:2
[92]=17,	-- depth:1
[47]=22,	-- depth:1
[67]=17,	-- depth:1
[97]=47,	-- depth:2
[98]=48,	-- depth:1
[72]=22,	-- depth:1
[42]=92,	-- depth:2
},
performance={
{}
},

performance_meta_table_map={
},
layer_default_table={layer=0,draw_times="1,5",consume_item_id=29100,consume_item_num=1,shop_seq=10020,raw_bg="bg_xunbao_long",},

reward_default_table={layer=0,reward_id=1,reward_item=item_table[45],pos=1,big_reward=0,is_shake=0,effect_level=1,},

performance_default_table={to_center_time=1.5,card_rot_time=0.5,fire_dur_time=2.5,select_scale=1.3,scale_time=0.2,shadow_dis=5,shadow_alpha=0.2,break_time=1,check_yes_time=1,dif_pixel=1,shake_frame_num=4,shake_pixel=5,delay_time=0.2,layer_tween_time=0.5,layer_interval_time=0.2,shuffle_delay=3,}

}

