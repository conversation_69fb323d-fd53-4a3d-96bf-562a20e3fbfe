--仙界装备--强化界面
function FairyLandEquipmentView:StrengthenReleaseCallBack()
    if not IsEmptyTable(self.strengthen_stuff_item_list) then
        for k, v in pairs(self.strengthen_stuff_item_list) do
            if v.cell then
                v.cell:DeleteMe()
            end
        end
        self.strengthen_stuff_item_list = nil
    end

    if nil ~= self.strengthen_product_item then
        self.strengthen_product_item:DeleteMe()
        self.strengthen_product_item = nil
    end

    if nil ~= self.strengthen_attr_list then
        for k,v in pairs(self.strengthen_attr_list) do
            v:DeleteMe()
        end
        self.strengthen_attr_list = nil
    end
end

function FairyLandEquipmentView:StrengthenLoadCallBack()
    if self.strengthen_stuff_item_list == nil then
        self.strengthen_stuff_item_list = {}
        for i = 1, XIANJIE_EQUIP_STRENGTHEN_MAX_STUFF do
            self.strengthen_stuff_item_list[i] = {}
            self.strengthen_stuff_item_list[i].cell = ItemCell.New(self.node_list["qh_stuff_item_" .. i])
            self.strengthen_stuff_item_list[i].cell_root = self.node_list["qh_stuff_item_" .. i]
        end
    end

    if nil == self.strengthen_product_item then
        self.strengthen_product_item = ItemCell.New(self.node_list["qh_product_item"])
    end

    if self.strengthen_attr_list == nil then
        self.strengthen_attr_list = {}
        for i = 1, 2 do
            self.strengthen_attr_list[i] = CommonAddAttrRender.New(self.node_list["qh_attrs_list"]:FindObj("attr_" .. i))
            self.strengthen_attr_list[i]:SetIndex(i)
        end
    end

    XUI.AddClickEventListener(self.node_list["qh_btn_strength"], BindTool.Bind(self.StrengthenOnClickStrength, self))
    XUI.AddClickEventListener(self.node_list["qh_btn_one_key_strength"], BindTool.Bind(self.StrengthenOnClickOneKeyStrength, self))
	XUI.AddClickEventListener(self.node_list["btn_strength_master"], BindTool.Bind(self.StrengthenOnClickStrengthMaster, self))
end

function FairyLandEquipmentView:StrengthenFlushRightPanel()
    local cur_select_slot = self:GetCurSelectSlot()
    local part_data = FairyLandEquipmentWGData.Instance:GetFLEStrengthenInfoBySoltPart(cur_select_slot, self.select_equip_part)
    local item_data = part_data and part_data.part_info
    local item_id = item_data and item_data.item_id or 0
    if item_id <= 0 then
        self.node_list["qh_no_data"]:SetActive(true)
        self.node_list["qh_had_data"]:SetActive(false)
    	return
    end

        self.node_list["qh_no_data"]:SetActive(false)
        self.node_list["qh_had_data"]:SetActive(true)

        local level = part_data.part_level
        local is_max = FairyLandEquipmentWGData.Instance:GetFLEStrengthenIsMaxLevel(cur_select_slot, self.select_equip_part, level)
        local no_max = not is_max

        self.node_list.qh_level.text.text = string.format(Language.FairyLandEquipment.StrengthenLevel, level)
        self.node_list.next_qh_level.text.text = string.format(Language.FairyLandEquipment.StrengthenLevel, level + 1)
        self.node_list.next_qh_level:SetActive(no_max)
        self.node_list.qh_level_arrow:SetActive(no_max)

        --选中强化部位单独展示
        self.strengthen_product_item:SetData(item_data)
        self.node_list["qh_product_item_name"].text.text = ItemWGData.Instance:GetItemName(item_id)

        local attr_list = FairyLandEquipmentWGData.Instance:GetFLEStrengthenAttr(cur_select_slot, self.select_equip_part, level)
        for k,v in pairs(self.strengthen_attr_list) do
            v:SetData(attr_list[k])
        end

        -- 材料
        local level_cfg
        if is_max then
            level_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenCfgBySlotPartLv(cur_select_slot, self.select_equip_part, level)
        else
            level_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenCfgBySlotPartLv(cur_select_slot, self.select_equip_part, level + 1)
        end

        local is_have_stuff = false
        local need_stuff, enough_stuff = 0, 0
        if level_cfg then
            local item_str = ""
            local str_color, has_num, consume_item, consume_num
            for i, v in ipairs(self.strengthen_stuff_item_list) do
                consume_item = level_cfg["stuff_id_" .. i]
                consume_num = level_cfg["stuff_num_" .. i]
                if consume_item and consume_num and consume_item > 0 and consume_num > 0 then
                    need_stuff = need_stuff + 1
                    v.cell:SetData({item_id = consume_item})
                    if no_max then
                        has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
                        is_have_stuff = has_num >= consume_num
                        str_color = is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
                        item_str = has_num .. "/" .. consume_num
                        if is_have_stuff then
                            enough_stuff = enough_stuff + 1
                        end
                    end
                    v.cell:SetRightBottomColorText(item_str, str_color)
                    v.cell:SetRightBottomTextVisible(true)
                    v.cell_root:SetActive(true)
                else
                    v.cell_root:SetActive(false)
                end
            end
        end

        -- 按钮
        self.node_list["qh_btn_strength"]:SetActive(no_max)
		self.node_list["qh_btn_one_key_strength"]:SetActive(no_max)
        self.node_list["qh_max_flag"]:SetActive(is_max)
        self.node_list["qh_btn_strength_remind"]:SetActive(need_stuff > 0 and enough_stuff == need_stuff)
        self.node_list["qh_btn_master_remind"]:SetActive(FairyLandEquipmentWGData.Instance:GetStrengthenMasterRedPointBySlot(cur_select_slot))
		self.node_list["qh_btn_one_key_strength_remind"]:SetActive(FairyLandEquipmentWGData.Instance:GetStrengthenPartRedPointBySlot(cur_select_slot))
end

function FairyLandEquipmentView:StrengthenOnClickStrength()
    local cur_select_slot = self:GetCurSelectSlot()
    local part_data = FairyLandEquipmentWGData.Instance:GetFLEStrengthenInfoBySoltPart(cur_select_slot, self.select_equip_part)
    if IsEmptyTable(part_data) then
    	return
    end

    local level = part_data.part_level
    local is_max = FairyLandEquipmentWGData.Instance:GetFLEStrengthenIsMaxLevel(cur_select_slot, self.select_equip_part, level)
    if is_max then
        return
    end

    local level_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenCfgBySlotPartLv(cur_select_slot, self.select_equip_part, level + 1)
    if level_cfg then
        local has_num, consume_item, consume_num, item_cfg, item_name
        for i = 1, XIANJIE_EQUIP_STRENGTHEN_MAX_STUFF do
            consume_item = level_cfg["stuff_id_" .. i]
            consume_num = level_cfg["stuff_num_" .. i]
            if consume_item and consume_num and consume_item > 0 and consume_num > 0 then
                has_num = ItemWGData.Instance:GetItemNumInBagById(consume_item)
                if consume_num > has_num then
                    item_cfg = ItemWGData.Instance:GetItemConfig(consume_item)
                    if item_cfg then
                        item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
                        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FairyLandEquipment.StrengthenNoEnoughTips, item_name))
                        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = consume_item})
                        return
                    end

                    SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.StrengthenStuffNotEnough)
                    return --物品不足
                end
            end
        end
    end

    FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.EQUIP_UP_LEVEL, cur_select_slot, self.select_equip_part)
end

--一键强化
function FairyLandEquipmentView:StrengthenOnClickOneKeyStrength()
    local cur_select_slot = self:GetCurSelectSlot()
	local remind = FairyLandEquipmentWGData.Instance:GetStrengthenPartRedPointBySlot(cur_select_slot)
	if remind then
        --请求操作
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.ONE_KEY_STRENGTHEN, cur_select_slot)
	else
		local level_cfg = FairyLandEquipmentWGData.Instance:GetFLEStrengthenCfgBySlotPartLv(cur_select_slot, self.select_equip_part, 1)
		if level_cfg then
			local item_cfg = ItemWGData.Instance:GetItemConfig(level_cfg.stuff_id_1)
			if item_cfg then
				local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FairyLandEquipment.StrengthenNoEnoughTips, item_name))
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = level_cfg.stuff_id_1})
			end
		end
	end
end

function FairyLandEquipmentView:StrengthenOnClickStrengthMaster()
    -- print_error("点击强化大师按钮")
    FairyLandEquipmentWGCtrl.Instance:OpenFLEStrengthenMasterView()
end

function FairyLandEquipmentView:StrengthenPlayStengthEffect()
	if self.node_list["strengthen_succ_pos"] then
        local bundle, asset = ResPath.GetUIEffect(UIEffectName.f_qianghuabaokai)
        EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["strengthen_succ_pos"].transform, 3)
	end
end
