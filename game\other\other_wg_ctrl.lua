-- require("game/other/agent_promote_view")
OtherWGCtrl = OtherWGCtrl or BaseClass(BaseWGCtrl)

MODULE_OPERATE_TYPE = {
	OP_LINGYU_INLAY = 1,						-- 灵玉镶嵌
	OP_LINGYU_LEVEL_UP = 2,						-- 灵玉升级
	OP_STONE_REFINE = 3,						-- 灵玉精炼
	OP_COMMIT_GUILD_BATTLE_LINGSHI = 4,			-- 仙盟战-提交灵石 param1(仙灵值)
	OP_TIANSHEN_3V3_JOIN_REWARD_RESULT = 5, 	-- 天神3v3参与奖励领取返回
	OP_Wing_UPEVOL = 6,							-- 羽翼进化
	OP_EQUIP_STRENGTHEN_SUCC = 7,				-- 装备强化成功
	OP_JINGLIAN_SUCC = 8 ,						-- 精炼升级成功
	OP_FISH_POOL_EXTEND_CAPACITY_SUCC = 9,		-- 鱼池扩展成功
	OP_WING_UPGRADE_SUCC = 10,
	OP_MOUNT_FLYUP = 11,						-- 坐骑飞升
	OP_WUSHAUNG_EQUIP_DAZAO = 13,				-- 无双装备打造
	OP_MULTI_MOUNT_UPGRADE = 14,				-- 双人坐骑进阶
	OP_EQUIP_HECHENG_SUCC = 15,					-- 装备合成成功
	OP_JINGLING_UPGRADE = 17,					-- 精灵升阶
	OP_SHENZHUANG_JINJIE = 18,					-- 神装进阶
	OP_MARRY_JINJIE = 19,						-- 情缘进阶

	OP_PET_HALO_UPLEVEL = 20,					-- 宠物光环升级
	OP_PET_UPGRADE = 21,						-- 宠物进阶
	OP_PET_EQUIP_STRENGTH_SUCC = 22, 			-- 宠物装备强化成功
	OP_FABAO_UPGRADE = 23,						-- 法宝进阶
	OP_FABAO_LIANQI = 24,						-- 法宝炼器
	OP_PET_HALO_BAOJI = 25,						-- 宠物光环暴击
	OP_WING_JINHUA_BAOJI = 26,					-- 羽翼进化暴击
	OP_PET_UPFLYLEVEL = 27,						-- 宠物飞升升级成功
	OP_SHITU_UPLEVEL = 28,						-- 师徒升级成功
	OP_BABY_JIE_UPGRADE = 29,					-- 宝宝进阶成功
	OP_SHENJIANG_JIE_UPGRADE = 30,				-- 神将进阶
	OP_HUASHEN_UPLEVEL = 31,					-- 化神进阶成功
	OP_PET_FAZHEN = 32,							-- 宠物法阵进阶成功
	OP_SHENJIANG_HALO = 34,						-- 神将光环进阶成功
	OP_FAIRYTREE_UPGRADE = 35,					-- 仙树升级
	OP_EQUIP_SHENZHOU_FORGING = 36,				-- 神州六器锻造升级
	OP_MAGIC_UTENSIL_FORGING = 37,				-- 魔器一键锻造升级

	OP_SHENJIANG_FLYUP_UPGRADE = 38,			-- 神将飞升进阶成功
	OP_WUSHANG_EQUIP_UPSTAR = 39,				-- 无上神兵升星
	OP_ADVANCED_UPGRADE = 40,					-- 进阶类型
	OP_LOVE_MARK_AUTO_UPGRADE = 41, 			-- 爱情印记一键进阶
	OP_BAOSHI_JL_AUTO_UPGRADE = 43, 			-- 宝石精炼一键进阶
	OP_STONE_LEVEL_UP = 45, 					-- 宝石升级
	OP_ITEM_USE = 46,							-- 物品使用
	OP_BAGUA_SHOP_FRESH = 47,					-- 八卦商店刷新
	OP_EQUIP_JICHENG = 48,						-- 印记继承操作返回

	OP_XIANQI_UPGRADE = 49,          			-- 仙器升阶
    OP_XIANQI_REFINE = 50,          			-- 仙器真炼
    OP_XIANQI_UPSTAR = 51,          			-- 仙器升星
    OP_XIANQI_UPQUALITY = 52,        			-- 仙器升品

	OP_HUAKUN_UPGRADE = 53,                     -- 化鲲进阶
    OP_HUAKUN_UPLEVEL = 54,                     -- 化鲲升级
    OP_EQUIP_UPQUALITY = 55,                    -- 装备升品
    OP_HUAKUN_UPSTAR = 56,                      -- 化鲲升星
    OP_PET_UPSTAR = 57,                         -- 宠物升星
    OP_HUAKUN_ACTIVE = 58,                      -- 化鲲激活
    OP_EQUIP_BAPTIZE = 59,                      -- 装备洗练

	OP_STONE_INLAY = 62,						-- 宝石镶嵌
	OP_WABAO_TAME_STATUS = 63,					-- 挖宝是否可以继续前往
	OP_SUIT_FORGE = 64,							-- 套装锻造
	OP_TIANSHEN_ACTIVE = 65,                	--天神激活
    OP_POSY_COMPOSE = 66,						-- 铭文合成结果
	OP_TIANSHEN_SKILL_ACTIVE = 67,				--天神技能解锁
	OP_TIANSHEN_SHENQI_ACTIVE = 68,         --天神神器激活
	OP_TIANSHEN_SHENSHI_ACTIVE = 69,		--天神神饰激活
	OP_TIANSHEN_FULING_ACTIVE = 70,			--天神附灵
	OP_LONGHUN_COMPOSE = 71,				--龙魂合成结果
	OP_MINGWEN_UPGRADE = 72,				--铭文升级返回
    SEND_CREATE_TEAM_FAIL = 73,               --请求组队返回
    OP_TIANSHEN_SHENTONG_UP_LV = 74, 		-- 天神神通升级返回
    OP_TIANSHEN_SHENTONG_ACT = 75, 			-- 天神神通激活返回
    OP_ZHANLING_TASK_FINISH = 76, 			-- 战令任务完成返回
	OP_WARDROBE_ACTIVE = 77,				-- 衣橱部位激活
	OP_DAILY_TREASURE_HELP_REFRESH = 78,	-- 每日宝箱帮助刷新结果 result(1 succ, 0 fail) param1=old_quality param2=new_quality
	
	OP_PIERRE_DIRECT_PURCHASE = 84,			-- 臻品直购
	OP_MEIRILEICHONG_REWARD_GET = 85,			-- 每日累充
	OP_LINGZHI_SKILL_XIULIAN_UPLEVEL = 86,		-- 灵智修炼升级
    OP_CREATE_CROSS_TEAM = 87,					-- 创建跨服队伍返回 result(1 succ, 0 fail)
    OP_ONE_MONEY_BUY_RESULT = 88,			-- 一元豪礼操作返回
	OP_EQUIPMENT_INTEGRATION_RESULT = 90,		-- 锻灵入体套装激活返回
	OP_CROSS_DRAGON_VEIN_SHOP_REFRESH = 91,		--跨服龙脉商店刷新
	OP_CROSS_LONGMAI_GATHER = 92,				-- 跨服龙脉场景采集回调

	OP_WEALTH_GOD_DRAW = 93,                -- 喜迎财神
	OP_FETCH_INITIAL_VIP = 94,					--领取初始VIP	result(1 succ, 0 fail) param1=old_vip_level param2=vip_level
	OP_RELIC_OPERATE_RESULT = 95,				--圣器操作返回 result(1 succ, 0 fail) param1=operate_type
	OP_RELIC_DECOMPS_EQUIP = 96,				-- 圣器装备分解 result(1 succ, 0 fail)
	OP_TITLE_LEVELUP = 97,						-- 称号升级		result(1 succ, 0 fail) param1=title_id param2=level
	OP_DRAGON_TEMPLE_OPERATE_RESULT = 98,		-- 龙神殿操作返回 result(1 succ, 0 fail) param1=operate_type
	OP_DRAGON_TEMPLE_RMB_BUY = 99,				-- 龙神殿直购返回 result(1 succ, 0 fail) param1=level
	OP_ARTIFACT_UP_LEVEL_RESULT = 101,			-- 双修 - 升级 result(1 succ, 0 fail) param1=seq  param2=level
	OP_ARTIFACT_UP_STAR_LEVEL_RESULT = 102,		-- 双修 - 升星 result(1 succ, 0 fail) param1=seq  param2=star_level
	OP_ARTIFACT_UP_AWAKE_LEVEL_RESULT = 103,	-- 双修 - 觉醒 result(1 succ, 0 fail) param1=seq
	OP_EQUIP_COLLECT_SUIT_UPLEVEL_RESULT = 104,	 -- 装备收集 - 套装升级 result(1 succ, 0 fail) param1=suit_index
	OP_HUANHUA_FETTER_ACTIVE = 105,				-- 激活幻化羁绊 result(1 succ, 0 fail) param1 = seq param2 = part

	OP_BACKGROUND_UP_LEVEL_RESULT = 106,		--背景（奇境）升星返回
	OP_TIANSHEN_HUAMO_RESULT = 107,				-- 天神化魔操作返回
	OP_TIANSHEN_HUAMO_Skill = 108,				-- 天神化魔技能操作
	OP_FIGHT_MOUNT2_LEVEL = 109,                -- 战斗坐骑形象升级    result(1 succ, 0 fail) param1=mount_id param2=level
	OP_FIGHT_MOUNT2_SKILL_LEVEL = 110,          -- 战斗坐骑技能升级    result(1 succ, 0 fail) param1=mount_id param2=level
	OP_FIGHT_MOUNT2_YU_LONG = 111,				--  御龙		result(1 succ, 0 fail) param1=skill_id
	OP_TIANSHEN_HEJI_ACTIVE = 112,				-- 天神合击		result(1 succ, 0 fail) param1=skill_id
	OP_BEAST_OPERATE_TYPE = 114,				-- 驭兽操作
	OP_CULTIVATION_DUJIE_TYPE = 115,            -- 修为渡劫结果返回
	OP_DISCOUNT_PURCHASE_TYPE = 116,            -- 一折弹窗礼包返回
	OP_CROSS_1VN_QUESTION = 117,			    -- 跨服1vn答题结果 

	OP_GOD_OR_DEMON_UPGRADE = 118,              -- 一念神魔升阶    result(1 succ, 0 fail) param1=grade
	OP_GOD_OR_DEMON_UPLEVEL = 119,              -- 一念神魔升级    result(1 succ, 0 fail) param1=level
	OP_WUHUN_TOWERAUTO_DARE = 120,              -- 武魂塔一键挑战  result(1 succ, 0 fail) param1=old_pass_seq param2=new_pass_seq param3=type

	OP_LOVERPK_GETMATCH_REWARD = 121,           -- 仙侣PK获得场次奖励
	OP_LOVERPK_GATHER_REWARD = 122,             -- 仙侣PK

	OP_WUHUN_FRONT_GRADE = 123,					-- 魂阵升阶提示 result(1 succ, 0 fail) param1=grade
	OP_HANGDBOOK_XILIAN = 124,							-- 用作奇闻异物自动洗练
	OP_EQUIP_BAPTIZE_UPGRADE = 125,				-- 装备洗炼进阶
	-- OP_TIANSHEN_DIRECT_ACTIVE = 126,			-- 天神自动激活 result(1 succ, 0 fail) param1=天神Index

	OP_COUNTRY_FETCH_TASK_RENWARD = 127,		-- 领取龙云战令任务奖励 result(1 succ, 0 fail) paraml=task_index
	OP_MENGLING_COMPOSE = 128,                  -- 梦灵合成状态
	OP_EVERY_DAY_LINGZHU_SHOP_REFRESH = 129,    -- 领主每日商店刷新 魔王仙藏   result(1 succ, 0 fail) 
	OP_ESOTERICA_PERFORM_SKILL = 131,			-- 秘籍释放技能			result(1 succ, 0 fail) param1=seq
	OP_BEAST_DRAW_CONVERT = 133,				-- 幻兽抽奖兑换 result(1 succ, 0 fail) param1=type
	OP_OGA_LOTTERY_REWARD = 135,				-- 开服冲榜拓展 抽奖结果
	OP_CROSS_BOSS_STRIKE_QUESTION = 136,	    -- 跨服BOSS来袭答题 result(1 succ, 0 fail) param1(0 fail 1 succ 2 not)
	OP_CREATE_TASK_MONSTER = 137,				-- 召唤任务怪物 result(1 succ, 0 fail) param1=task_id
	OP_CROSS_BOSS_STRIKE_MONSTER_SKILL_RESULT = 138,--  跨服BOSS怪物技能命中 result(1 succ, 0 fail) param1=skill_id
	OP_MONSTER_SEC_KILL = 139,					-- boss秒杀 result(1 succ, 0 fail) param1=type param2=obj_id

	OP_ORDEAL_RESULT = 141,						--  渡劫结果 result(1 succ, 0 fail) param1=level
	OP_ZHUANZHI_EQUIP_COLLECT = 142,			-- 转职装备收集 result(1 succ, 0 fail) param1=suit_index * 100 + pos param2=item_id param3=star
	OP_BEAST_SKIN_UPLEVEL = 143,				-- 幻兽皮肤升级 result(1 succ, 0 fail) param1=skin_seq param2=skin_level
	OP_THUNDER_UP_LEVEL = 145,				    -- 雷法槽位升级 result(1 succ, 0 fail) param1=seq param2=part param3=level
	OP_THUNDER_UP_GRADE = 146,				    -- 雷法槽位升阶 result(1 succ, 0 fail) param1=seq param2=part param3=grdae
	OP_THUNDER_ACTIVE_SUIT = 147,				-- 雷法激活套装 result(1 succ, 0 fail) param1=seq param2=index
	OP_BEAST_HOLY_LINK = 148,					-- 幻兽链接 result(1 succ, 0 fail) param1=bag_index param2=link_bag_index
	OP_BEAST_HOLY_SPIRIT_UPLEVEL = 149,			-- 幻兽圣魂升级 result(1 succ, 0 fail) param1=bag_index param2=level
	OP_HUNDREDFOLD_DROP_UPLEVEL = 150,			-- 百倍爆装升级 result(1 succ, 0 fail) 
	OP_EQUIP_AUTO_BAPTIZE = 151,			    -- 装备自动洗练 result(1 succ, 0 fail) param1=target_quality_id param2=times param3=cost_item_num
	OP_OA_DRAGON_TRIAL_AUTO_DARE = 152,			-- 龙神试炼一键挑战  result(1 succ, 0 fail) param1=old_pass_seq param2=new_pass_seq
	OP_FB_GUIDE_PERFORM_SKILL = 153,			--// 副本引导释放技能 result(1 succ, 0 fail) param1=skill_id param2=scene_type param3=monster_id
	OP_FB_GUIDE_MONSTER_ENTER = 154,			--// 副本引导怪物出场 result(1 succ, 0 fail) param1=monster_id param2=scene_type
	OP_CROSS_TREASURE_BEAST_GATHER_RESULT = 155,	--// 跨服幻兽捕捉结果 result(1 succ, 0 fail) param1=pool_id * 1000 + seq param2=is_special param3=is_rampage
	OP_CROSS_TREASURE_GATHER_RESULT = 156,		--// 跨服藏宝采集结果 result(1 succ, 0 fail) param1=index param1=seq
	OP_AO_COLLECT_CARD_SEND = 157,				--// 运营活动 - 集卡赠送 result(1 succ, 0 fail) param1=uid param2=seq
	OP_ARTIFACT_FAVOR = 159,					-- 双修 - 好感 result(1 succ, 0 fail) param1=seq param2=favor_value
	OP_BEAST_EQUIP_WORDS_REFRESH = 160,			--// 幻兽内丹 - 洗练  result(1 succ, 0 fail) 
	OP_BEAST_EQUIP_WORDS_INHERITANCE = 161,		--// 幻兽内丹 - 传承	result(1 succ, 0 fail) 
	OP_LEVEL_RMB_BUY = 163,						-- 等级直购 result(1 succ, 0 fail) param1=seq
}

function OtherWGCtrl:__init()
	OtherWGCtrl.Instance = self
	-- self.agent_promote_view = AgentPromoteView.New()

	self:RegisterAllProtocals()
	self:RegisterEvent()
end

function OtherWGCtrl:__delete()
	-- self.agent_promote_view:DeleteMe()
	-- self.agent_promote_view = nil
	OtherWGCtrl.Instance = nil
end

function OtherWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCOperateResult, "OnOperateResult")
end

function OtherWGCtrl:RegisterEvent()
	self:BindGlobalEvent(KnapsackEventType.KNAPSACK_LECK_ITEM, BindTool.Bind1(self.OpenGetItemView, self))
end

function OtherWGCtrl:OnOperateResult(protocol)
	-- print_error('OnOperateResult------------',protocol)
	local operate_type = protocol.operate
	if MODULE_OPERATE_TYPE.OP_EQUIP_STRENGTHEN_SUCC == operate_type then
		if nil ~= EquipmentWGCtrl then
			EquipmentWGCtrl.Instance:OnEquipStrengthResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_HUAKUN_UPGRADE == operate_type then
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_kun_upgrade)
		end

	elseif MODULE_OPERATE_TYPE.OP_PET_UPGRADE == operate_type then    -- 宠物进阶
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.PetView,TabIndex.pet_xiangqing,nil,
				{show_jj_effect = true})
		end
	elseif MODULE_OPERATE_TYPE.OP_PET_UPSTAR == operate_type then    -- 宠物升星
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.PetView,TabIndex.pet_xiangqing,nil,
				{show_sx_effect = true})
		end
	elseif MODULE_OPERATE_TYPE.OP_HUAKUN_UPSTAR == operate_type then    -- 化鲲升星
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_kun_upgrade)
		end

	elseif MODULE_OPERATE_TYPE.OP_HUAKUN_ACTIVE == operate_type then    -- 化鲲激活
		if protocol.result == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_kun_upstar)
		end

	elseif MODULE_OPERATE_TYPE.OP_MARRY_JINJIE == operate_type then
		if nil ~= MarryWGCtrl then
			MarryWGCtrl.Instance:OnRingResult(protocol.result)
		end

	elseif MODULE_OPERATE_TYPE.OP_BABY_JIE_UPGRADE == operate_type then
		if nil ~= MarryWGCtrl then
			MarryWGCtrl.Instance:OnBabyUpgradeResult(protocol.result)
		end
	elseif  MODULE_OPERATE_TYPE.OP_FISH_POOL_EXTEND_CAPACITY_SUCC == operate_type then
		if nil ~= SocietyWGCtrl then
			SocietyWGCtrl.Instance:OnPoolExtendSuccess(protocol.result)
		end

	elseif MODULE_OPERATE_TYPE.OP_LOVE_MARK_AUTO_UPGRADE == operate_type then
		if nil ~= MarryWGCtrl then
			MarryWGCtrl.Instance:OnLoveMarkUpGradeResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_BAOSHI_JL_AUTO_UPGRADE == operate_type then
		if nil ~= EquipmentWGCtrl then
			EquipmentWGCtrl.Instance:OnEquipBaoShiJLResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_STONE_LEVEL_UP == operate_type then
		if nil ~= EquipmentWGCtrl then
			EquipmentWGCtrl.Instance:BaoShiUpGradeResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_EQUIP_JICHENG == operate_type then
		if nil ~= EquipmentWGCtrl then
			EquipmentWGCtrl.Instance:OnEquipJiChengResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_ITEM_USE == operate_type then
		local time_tab = TimeUtil.Format2TableDHMS(protocol.param1)
		local str
		if time_tab.hour > 0 then
			str = string.format(Language.Common.CoolingTip_1,time_tab.hour)
		elseif time_tab.min > 0 then
			str = string.format(Language.Common.CoolingTip_2,time_tab.min)
		elseif time_tab.s > 0 then
			str = string.format(Language.Common.CoolingTip_3,time_tab.s)
		end
		if str then
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end

	elseif MODULE_OPERATE_TYPE.OP_XIANQI_UPGRADE == operate_type then
		if nil ~= ComposeWGCtrl.Instance then
			ComposeWGCtrl.Instance:XianQiShengJieResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_XIANQI_REFINE == operate_type then
		if nil ~= ComposeWGCtrl.Instance then
			ComposeWGCtrl.Instance:XianQiLianZhenResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_XIANQI_UPSTAR == operate_type then
		if nil ~= ComposeWGCtrl.Instance then
			ComposeWGCtrl.Instance:XianQiShengXingResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_XIANQI_UPQUALITY == operate_type then
		if nil ~= ComposeWGCtrl.Instance then
			ComposeWGCtrl.Instance:XianQiShengPinResult(protocol.result)
		end
	elseif MODULE_OPERATE_TYPE.OP_EQUIP_UPQUALITY == operate_type then
		EquipmentWGCtrl.Instance:FlushUpQualityEffect(protocol.result, protocol.param1, protocol.param2)
		-- ViewManager.Instance:FlushView(GuideModuleName.Equipment)
	elseif MODULE_OPERATE_TYPE.OP_EQUIP_BAPTIZE == operate_type then
		EquipmentWGCtrl.Instance:FlushBaptizeEffect(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_STONE_INLAY == operate_type then
		EquipmentWGCtrl.Instance:StoneInlayCallBack(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_WABAO_TAME_STATUS == operate_type then --挖宝是否可以继续
		BootyBayWGCtrl.Instance:ReqGoToZangBaoPos(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_SUIT_FORGE == operate_type then
		EquipmentWGCtrl.Instance:SuitForgeCallBack(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_POSY_COMPOSE == operate_type then
		MingWenWGCtrl.Instance:ComposeResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_ACTIVE == operate_type then
		TianShenWGCtrl.Instance:FlushActiveEffect(protocol)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_SKILL_ACTIVE == operate_type then
		TianShenWGCtrl.Instance:FlushSkillActiveEffect(protocol)
	elseif MODULE_OPERATE_TYPE.OP_LONGHUN_COMPOSE == operate_type then
		ComposeWGCtrl.Instance:LongHunComposeResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_SHENQI_ACTIVE == operate_type then
		TianShenWGCtrl.Instance:FlushShenQiActiveEffect(protocol)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_SHENSHI_ACTIVE == operate_type then
		TianShenWGCtrl.Instance:FlushTaozhuangActiveEffect(protocol)
    elseif MODULE_OPERATE_TYPE.OP_MINGWEN_UPGRADE == operate_type then
        MingWenWGCtrl.Instance:UpGradeResult(protocol.result)
    elseif MODULE_OPERATE_TYPE.SEND_CREATE_TEAM_FAIL == operate_type then
        SocietyWGCtrl.Instance:DoOpearte(protocol.result)
    elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_SHENTONG_UP_LV == operate_type then
        TianShenWGCtrl.Instance:OnShenTongUpgradeResult(protocol.result)
    elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_SHENTONG_ACT == operate_type then
        TianShenWGCtrl.Instance:OnShenTongActiveResult(protocol.result)
    elseif MODULE_OPERATE_TYPE.OP_BAGUA_SHOP_FRESH == operate_type then
    	TianShenWGCtrl.Instance:BaGuaShopFLushReturn(protocol.result)
    elseif MODULE_OPERATE_TYPE.OP_ZHANLING_TASK_FINISH == operate_type then
    	ZhanLingWGCtrl.Instance:ZhanLingTaskFinishReturn(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_WARDROBE_ACTIVE == operate_type then
		WardrobeWGCtrl.Instance:OnPartActiveResult(protocol.result, protocol.param1, protocol.param2)
    elseif MODULE_OPERATE_TYPE.OP_DAILY_TREASURE_HELP_REFRESH == operate_type then
    	GuildWGCtrl.Instance:GuildViewFlush(TabIndex.guild_baoxiang)
	elseif MODULE_OPERATE_TYPE.OP_LINGYU_INLAY == operate_type then
		EquipmentWGCtrl.Instance:LingYuInlayResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_LINGYU_LEVEL_UP == operate_type then
		EquipmentWGCtrl.Instance:LingYuUpGradeResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_COMMIT_GUILD_BATTLE_LINGSHI == operate_type then
		GuildBattleRankedWGCtrl.Instance:ShowGetLingShiVlaue(protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_3V3_JOIN_REWARD_RESULT == operate_type then
		TianShen3v3WGCtrl.Instance:OnJoinFetchedResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_PIERRE_DIRECT_PURCHASE == operate_type then
		PierreDirectPurchaseWGCtrl.Instance:OnBuyCallBack(protocol)
	elseif MODULE_OPERATE_TYPE.OP_MEIRILEICHONG_REWARD_GET == operate_type then
		ServerActivityWGCtrl.Instance:OnGetEveryDayRechargeReward(protocol)
	elseif MODULE_OPERATE_TYPE.OP_LINGZHI_SKILL_XIULIAN_UPLEVEL == operate_type then
        LingZhiWGCtrl.Instance:FlushLingZhiView({play_effect = protocol.result})
    elseif MODULE_OPERATE_TYPE.OP_CREATE_CROSS_TEAM == operate_type then
        CrossTeamWGCtrl.Instance:DoOpearte(protocol.result)
    elseif MODULE_OPERATE_TYPE.OP_ONE_MONEY_BUY_RESULT == operate_type then
        YiYuanHaoLiWGCtrl.Instance:OnGetYiYuanHaoLiReward(protocol)
	elseif MODULE_OPERATE_TYPE.OP_EQUIPMENT_INTEGRATION_RESULT == operate_type then
		EquipTargetWGCtrl.Instance:OnEISuitActiveCallBack(protocol)
	elseif MODULE_OPERATE_TYPE.OP_CROSS_DRAGON_VEIN_SHOP_REFRESH == protocol.operate then
		CrossLongMaiWGCtrl.Instance:LongMaiShopFLushReturn(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_CROSS_LONGMAI_GATHER == operate_type then
		CrossLongMaiWGCtrl.Instance:SceneGatherCompelete(protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_WEALTH_GOD_DRAW == operate_type then
		GodOfWealthWGCtrl.Instance:OnWarlthGodDraw(protocol)
	elseif MODULE_OPERATE_TYPE.OP_FETCH_INITIAL_VIP == operate_type then
		VipWGCtrl.Instance:OnInintialVipResult(protocol.result, protocol.param1,protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_RELIC_OPERATE_RESULT == operate_type then
		HolyDarkWeaponWGCtrl.Instance:OnRelicOperateResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_RELIC_DECOMPS_EQUIP == operate_type then
		HolyDarkWeaponWGCtrl.Instance:OnRelicDecomsEquipResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_TITLE_LEVELUP == operate_type then
		TitleWGCtrl.Instance:OnTitleLevelUpResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_DRAGON_TEMPLE_OPERATE_RESULT == operate_type then
		DragonTempleWGCtrl.Instance:OnDragonTempleOperateResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_DRAGON_TEMPLE_RMB_BUY == operate_type then
		DragonTempleWGCtrl.Instance:OnDragonTemplePurchaseResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_ARTIFACT_UP_LEVEL_RESULT == operate_type then
		ArtifactWGCtrl.Instance:OnUpLevelResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_ARTIFACT_UP_STAR_LEVEL_RESULT == operate_type then
		ArtifactWGCtrl.Instance:OnUpStarResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_ARTIFACT_UP_AWAKE_LEVEL_RESULT == operate_type then
		ArtifactWGCtrl.Instance:OnAwakeResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_EQUIP_COLLECT_SUIT_UPLEVEL_RESULT == operate_type then
		EquipTargetWGCtrl.Instance:OnEISuitUpLevelResult(protocol.result, protocol.param1)
		--RoleBagWGCtrl.Instance:OnEISuitUpLevelResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_HUANHUA_FETTER_ACTIVE == operate_type then
		NewHuanHuaFetterWGCtrl.Instance:OnPartActiveResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_BACKGROUND_UP_LEVEL_RESULT == operate_type then
		BackgroundWGCtrl.Instance:OnUpLevelResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_HUAMO_RESULT == operate_type then
		TianShenHuamoWGCtrl.Instance:OnOperateResult(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_HUAMO_Skill == operate_type then
		TianShenHuamoWGCtrl.Instance:OnOperateSkillResult(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_FIGHT_MOUNT2_LEVEL == operate_type then
		DragonTempleWGCtrl.Instance:OnMountLevelResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_FIGHT_MOUNT2_SKILL_LEVEL == operate_type then
		NewFightMountWGCtrl.Instance:OnNewFightMountSkillLevelResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_FIGHT_MOUNT2_YU_LONG == operate_type then
		NewFightMountWGCtrl.Instance:OnNewFightMountYuLongResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_HEJI_ACTIVE == operate_type then
		--TianShenWGCtrl.Instance:OnTianShenHeJiSkillActResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_BEAST_OPERATE_TYPE == operate_type then
		ControlBeastsWGCtrl.Instance:OnOperateResult(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_CULTIVATION_DUJIE_TYPE == operate_type then
		CultivationWGCtrl.Instance:OnOperateResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_DISCOUNT_PURCHASE_TYPE == operate_type then
		DiscountPurchaseWGCtrl.Instance:OnOperateResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_CROSS_1VN_QUESTION == operate_type then
		-- print_error("跨服1vn答题结果", protocol.result, protocol.param1, protocol.param2, protocol.param3)
		UltimateBattlefieldWGCtrl.Instance:UserAnswerResult(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_GOD_OR_DEMON_UPGRADE == operate_type then
		YinianMagicWGCtrl.Instance:OnUpGradeResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_GOD_OR_DEMON_UPLEVEL == operate_type then
		YinianMagicWGCtrl.Instance:OnUpLevelResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_WUHUN_TOWERAUTO_DARE == operate_type then
		WuHunWGCtrl.Instance:QuickFinishWuHunTower(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_LOVERPK_GETMATCH_REWARD == operate_type then
		LoverPkWGCtrl.Instance:OnGetMatchCountReward(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_LOVERPK_GATHER_REWARD == operate_type then
		LoverPkWGCtrl.Instance:OnGetLoverPKGatherReward(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_WUHUN_FRONT_GRADE == operate_type then
		WuHunWGCtrl.Instance:FrontGemGradeResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_HANGDBOOK_XILIAN == operate_type then
		StrangeCatalogWGCtrl.Instance:OnUpLevelResult(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_EQUIP_BAPTIZE_UPGRADE == operate_type then
		EquipmentWGCtrl.Instance:FlushBaptizeUpgradeEffect(protocol)
	-- elseif MODULE_OPERATE_TYPE.OP_TIANSHEN_DIRECT_ACTIVE == operate_type then
	-- 	TianShenWGCtrl.Instance:FlushTianshenDirectActive(protocol)
	elseif MODULE_OPERATE_TYPE.OP_COUNTRY_FETCH_TASK_RENWARD == operate_type then
		LongYunZhanLingWGCtrl.Instance:SendCSCountryOperate(COUNTRY_OPERATE_TYPE.COUNTRY_OPERATE_TYPE_TASK_INFO)
	elseif MODULE_OPERATE_TYPE.OP_MENGLING_COMPOSE == operate_type then
		MengLingWGCtrl.Instance:OnComposeResuit(protocol)
	elseif MODULE_OPERATE_TYPE.OP_EVERY_DAY_LINGZHU_SHOP_REFRESH == operate_type then
		LordEveryDayShopWGCtrl.Instance:LordEveryDayShopFlush(protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_OGA_LOTTERY_REWARD == operate_type then
		ServerActivityWGCtrl.Instance:OnGetOGALotteryReward(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_ESOTERICA_PERFORM_SKILL == operate_type then
		MainuiWGCtrl.Instance:PlayUseEsotericaSkillEffect(protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_BEAST_DRAW_CONVERT == operate_type then
		ControlBeastsWGCtrl.Instance:OnBeastDrawConvertResult(protocol.result, protocol.param1)
	elseif MODULE_OPERATE_TYPE.OP_CROSS_BOSS_STRIKE_QUESTION == operate_type then
		BOSSInvasionWGCtrl.Instance:OnQuestionInfo(protocol)
	elseif MODULE_OPERATE_TYPE.OP_CROSS_BOSS_STRIKE_MONSTER_SKILL_RESULT == operate_type then
		BOSSInvasionWGCtrl.Instance:OnMonsterSkillResult(protocol)
	elseif MODULE_OPERATE_TYPE.OP_MONSTER_SEC_KILL == operate_type then
		BossWGCtrl.Instance:TriggerSecKillMonster(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_ORDEAL_RESULT == operate_type then    -- 渡劫
		if protocol.result == 1 then --成功
			DujieWGCtrl.Instance:OpenDujieSuccessView()
		else
			DujieWGCtrl.Instance:OpenDujieFailView()
		end
	elseif MODULE_OPERATE_TYPE.OP_ZHUANZHI_EQUIP_COLLECT == operate_type then
		TransFerWGCtrl.Instance:PlayTipsFlyIcon(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_BEAST_SKIN_UPLEVEL == operate_type then
		ControlBeastsWGCtrl.Instance:OnBeastSkinResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_THUNDER_UP_LEVEL == operate_type then
		ThunderManaWGCtrl.Instance:OnUpLevelResult(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_THUNDER_UP_GRADE == operate_type then
		ThunderManaWGCtrl.Instance:OnUpGradeResult(protocol.result, protocol.param1, protocol.param2, protocol.param3)
	elseif MODULE_OPERATE_TYPE.OP_THUNDER_ACTIVE_SUIT == operate_type then
		ThunderManaWGCtrl.Instance:OnSuitRewardResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_BEAST_HOLY_LINK == operate_type then
		ControlBeastsWGCtrl.Instance:OnBeastHolyLinkResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_BEAST_HOLY_SPIRIT_UPLEVEL == operate_type then
		ControlBeastsWGCtrl.Instance:OnBeastSpiritUpLevelResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_OA_DRAGON_TRIAL_AUTO_DARE == operate_type then
		DragonTrialWGCtrl.Instance:QuickFinishDragonTrialTower(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_HUNDREDFOLD_DROP_UPLEVEL == operate_type then
		MainuiWGCtrl.Instance:FlushView(0, "flush_hundred_num")
		HundredEquipWGCtrl.Instance:HundredDropUpLevel()
	elseif MODULE_OPERATE_TYPE.OP_EQUIP_AUTO_BAPTIZE == operate_type then
		EquipmentWGCtrl.Instance:EquipBaptizeResultCallBack(protocol)
	elseif MODULE_OPERATE_TYPE.OP_FB_GUIDE_PERFORM_SKILL == operate_type then
		if protocol.result == 1 then --成功
			local logic = Scene.Instance:GetSceneLogic()
			if logic ~= nil and logic.TriggerBossSkillGuide ~= nil then
				logic:TriggerBossSkillGuide(protocol.param1, protocol.param2, protocol.param3)
			end
		end
	elseif MODULE_OPERATE_TYPE.OP_FB_GUIDE_MONSTER_ENTER == operate_type then
		if protocol.result == 1 then --成功
			local logic = Scene.Instance:GetSceneLogic()
			if logic ~= nil and logic.TriggerBossEnterGuide ~= nil then
				logic:TriggerBossEnterGuide(protocol.param1, protocol.param2)
			end
		end
	elseif MODULE_OPERATE_TYPE.OP_CROSS_TREASURE_BEAST_GATHER_RESULT == operate_type or
	MODULE_OPERATE_TYPE.OP_CROSS_TREASURE_GATHER_RESULT == operate_type then
		CrossTreasureWGCtrl.Instance:OnCrossTreasureBeastOperareCallBack(operate_type, protocol)
	elseif MODULE_OPERATE_TYPE.OP_AO_COLLECT_CARD_SEND == operate_type then				--// 运营活动 - 集卡赠送 result(1 succ, 0 fail) param1=uid param2=seq
		ServerActivityWGCtrl.Instance:OnSCAOCollectCardSendCardBack(protocol)
	elseif MODULE_OPERATE_TYPE.OP_ARTIFACT_FAVOR == operate_type then
		ArtifactWGCtrl.Instance:OnAffectionChangeResult(protocol.result, protocol.param1, protocol.param2)
	elseif MODULE_OPERATE_TYPE.OP_BEAST_EQUIP_WORDS_REFRESH == operate_type or MODULE_OPERATE_TYPE.OP_BEAST_EQUIP_WORDS_INHERITANCE == operate_type then
		ControlBeastsCultivateWGCtrl.Instance:OnSCBeastEquipOperateCallBack(operate_type, protocol.result)
	elseif MODULE_OPERATE_TYPE.OP_LEVEL_RMB_BUY == operate_type then
		if protocol.result == 1 then
			LevelRechargeWGData.Instance:SetIsBuy(protocol.param1)
		end
	end
end


function OtherWGCtrl:OpenGetItemView(item_id, item_count, shop_seq)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if nil == item_cfg then
		Log("物品不存在，请更新配置-->> id : ", item_id)
		return
	else
		if 31 == item_cfg.search_type then	--勾玉不弹获取提示
			return
		end
		if 90071 == item_id then
			SysMsgWGCtrl.Instance:ErrorRemind("荣誉不足")
			return
		end
		if item_id >= 26371 and item_id <= 26394 or 26978 == item_id  then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
			return
		end
		-- if 26914 == item_id then
		-- 	ViewManager.Instance:Open(GuideModuleName.VipTip)
		-- 	return
		-- end
		--指定id，商店配置里该id物品不可购买时，防止弹购买窗口
		-- if item_id == 26412 then
		-- 	local is_can_buy = ShopWGData.Instance:IsCanBuyByItemId(item_id)
		-- 	if not is_can_buy then
		-- 		return
		-- 	end
		-- end
	end


	if IS_FREE_VERSION then
		print_error("OtherWGCtrl:OpenGetItemView     直接被返回了")
		return
	end

	self:OpenShopTipView(item_cfg,item_id, item_count, shop_seq)
end

function OtherWGCtrl:OpenShopTipView(item_cfg,item_id, item_count, shop_seq)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end


function OtherWGCtrl:OpenAgentPromoteView()
	-- self.agent_promote_view:Open()
end
