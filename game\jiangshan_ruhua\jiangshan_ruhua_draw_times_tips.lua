JSRHDrawTimesTips = JSRHDrawTimesTips or BaseClass(SafeBaseView)

local MAX_NUM = 200

function JSRHDrawTimesTips:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(602, 428)})
	self:AddViewResource(0, "uis/view/jiangshan_ruhua_ui_prefab", "jsrh_draw_times_tips")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function JSRHDrawTimesTips:__delete()
end

function JSRHDrawTimesTips:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.JiangShanRuHua.SelectTimesTip
	
	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind1(self.OnClickPlus, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind1(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["btn_cancel"], BindTool.Bind1(self.OnClickCancel, self))
	XUI.AddClickEventListener(self.node_list["amount_btn"], BindTool.Bind1(self.OnClickSetAmount, self))
	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind1(self.OnClickOK, self))

	self.amount = JiangShanRuHuaWGData.Instance:GetDrawTimes()
	local cfg = JiangShanRuHuaWGData.Instance:GetConsumeCfg()
	self.price = cfg.consume_count
	self.node_list["txt_des"].text.text = string.format(Language.JiangShanRuHua.SelectDes, self.price)
end

function JSRHDrawTimesTips:ReleaseCallBack()

end

function JSRHDrawTimesTips:SetData(data)
	self.data = data
end

function JSRHDrawTimesTips:OnFlush(param_list, index)
	self:FlushTotalCost()
	self:FlushAmount()
	self:FlushAmountBtnStatus()
end

function JSRHDrawTimesTips:FlushAmount()
	self.node_list["amount"].text.text = self.amount
end

function JSRHDrawTimesTips:FlushTotalCost()
    if self.node_list["txt_total_cost"] then
		local cost_num_str = ToColorStr(self.amount * self.price, COLOR3B.GREEN)
		self.node_list["txt_total_cost"].text.text = string.format(Language.JiangShanRuHua.TotalPrice, cost_num_str)
	end
end

function JSRHDrawTimesTips:FlushAmountBtnStatus()
	XUI.SetGraphicGrey(self.node_list["btn_plus"], self.amount >= MAX_NUM)
	XUI.SetGraphicGrey(self.node_list["btn_minus"], self.amount <= 1)
end

function JSRHDrawTimesTips:OnClickPlus()
	self:ChangeAmount(math.min(self.amount + 10, MAX_NUM))
end

function JSRHDrawTimesTips:OnClickMinus()
	self:ChangeAmount(math.max(self.amount - 10, 1))
end

function JSRHDrawTimesTips:ChangeAmount(new_amount)
	self.amount = new_amount
	self:FlushAmount()
	self:FlushAmountBtnStatus()
	self:FlushTotalCost()
end

-- 点击设置数量
function JSRHDrawTimesTips:OnClickSetAmount()
	local function callback(input_num)
		self:ChangeAmount(input_num)
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.amount)
	num_keypad:SetMaxValue(MAX_NUM)
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(callback)
end

function JSRHDrawTimesTips:OnClickOK()
	if self.data and self.data.ok_func then
		self.data.ok_func(self.amount)
	end
	self:Close()
end

function JSRHDrawTimesTips:OnClickCancel()
	if self.data and self.data.cancel_func then
		self.data.cancel_func()
	end
	self:Close()
end