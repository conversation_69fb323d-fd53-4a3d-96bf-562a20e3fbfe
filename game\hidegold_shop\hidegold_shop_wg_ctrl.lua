require("game/hidegold_shop/hidegold_shop_wg_data")
require("game/hidegold_shop/hidegold_shop_main_view")
require("game/hidegold_shop/hidegold_shopitem_show")
require("game/hidegold_shop/hidegold_shop_buy_view")

HideGoldShopWGCtrl = HideGoldShopWGCtrl or BaseClass(BaseWGCtrl)
function HideGoldShopWGCtrl:__init()
	if HideGoldShopWGCtrl.Instance then
		ErrorLog("[HideGoldShopWGCtrl] attempt to create singleton twice!")
		return
	end

	HideGoldShopWGCtrl.Instance = self
	self.data = HideGoldShopWGData.New()
    self.main_view = HideGoldMainView.New(GuideModuleName.HideGoldMainView)
    self.shop_item_show = HideGoldShopItemShowView.New()
    self.shop_buy_view = HideGoldShopBuyView.New()
    self:RegisterAllProtocols()

end

function HideGoldShopWGCtrl:__delete()
	HideGoldShopWGCtrl.Instance = nil

	self.main_view:DeleteMe()
	self.main_view = nil

    self.shop_item_show:DeleteMe()
	self.shop_item_show = nil

    self.shop_buy_view:DeleteMe()
	self.shop_buy_view = nil

    self.data:DeleteMe()
	self.data = nil

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function HideGoldShopWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCHideGoldShopInfo,"OnSCHideGoldShopInfo")
end

function HideGoldShopWGCtrl:ReqActivityHideGoldShopInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HIDE_GOLD_SHOP
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function HideGoldShopWGCtrl:OnSCHideGoldShopInfo(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetAllInfo(protocol)

    if self.main_view:IsOpen() then
        self.main_view:Flush()
    end

	if self.shop_item_show:IsOpen() then
        self.shop_item_show:Flush()
    end

	if self.shop_buy_view:IsOpen() then
        self.shop_buy_view:Flush()
    end
end

--打开商店所有道具显示
function HideGoldShopWGCtrl:OpenShopItemShowView()
    self.shop_item_show:Open()
end

--打开店铺
function HideGoldShopWGCtrl:OpenShopBuyView()
    self.shop_buy_view:Open()
end

function HideGoldShopWGCtrl:OpenBuyShopItemTips(func)
	if not self.alert then
		self.alert = Alert.New()
	end
	self.alert:ClearCheckHook()
	self.alert:SetShowCheckBox(true, "hidegold_shop")
	self.alert:SetCheckBoxDefaultSelect(false)

	local str = string.format(Language.HideGoldShop.BuyShopTips)
	self.alert:SetLableString(str)
	self.alert:SetOkFunc(func)
	self.alert:Open()
end