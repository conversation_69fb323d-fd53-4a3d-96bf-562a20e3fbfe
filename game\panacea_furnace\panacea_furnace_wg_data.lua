PanaceaFurnaceWGData = PanaceaFurnaceWGData or BaseClass()
function PanaceaFurnaceWGData:__init()
	if PanaceaFurnaceWGData.Instance then
		error("[PanaceaFurnaceWGData] Attempt to create singleton twice!")
		return
	end

    PanaceaFurnaceWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.PanaceaFurnace, BindTool.Bind(self.ShowPanaceaFurnaceRemind, self))
end

function PanaceaFurnaceWGData:__delete()
    self.grade = nil
	self.draw_times = nil
	self.lucky = nil
	self.times_reward_flag = nil
	self.convert_times_list = nil
	self.cache_draw_btn_index = nil
	self.result_reward_list = nil
    self.baodi_item = nil
    PanaceaFurnaceWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.PanaceaFurnace)
end

function PanaceaFurnaceWGData:InitParam()
    self.grade = 1 -- 档次
	self.draw_times = 0 --次数
	self.lucky = 0 -- 幸运值
	self.times_reward_flag = {} -- 次数奖励领取状态
	self.convert_times_list = {} -- 兑换次数
	self.cache_draw_btn_index = 1
	self.result_reward_list = {}
    self.baodi_item = {}
end

function PanaceaFurnaceWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_glory_crystal2_auto")
	self.times_reward_cfg = ListToMap(cfg.times_reward, "grade", "seq")
	self.reward_pool_cfg = ListToMap(cfg.reward_pool, "grade", "seq")
	self.mode_cfg = ListToMap(cfg.mode, "mode")
	self.baodi_cfg = ListToMap(cfg.baodi, "grade")
	self.convert_cfg = ListToMap(cfg.convert, "grade", "seq")
	self.stuff_cfg = ListToMap(cfg.convert, "stuff_id_1")
    self.item_random_desc = ListToMapList(cfg.item_random_desc, "grade")
	self.other_cfg = cfg.other[1]
end

function PanaceaFurnaceWGData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.draw_times = protocol.draw_times
    self.lucky = protocol.lucky
    self.times_reward_flag = protocol.times_reward_flag
    self.convert_times_list = protocol.convert_times_list
end

function PanaceaFurnaceWGData:SetResultData(protocol)
    self.result_reward_list = protocol.result_item_list
    self.baodi_item = protocol.baodi_item
end

--档次
function PanaceaFurnaceWGData:GetCurGrade()
	return self.grade
end

--次数奖励领取状态
function PanaceaFurnaceWGData:GetTimesRewardState(seq)
	return self.times_reward_flag[seq] or 0
end

function PanaceaFurnaceWGData:GetAllRewardState()
	return self.times_reward_flag
end

--抽奖次数
function PanaceaFurnaceWGData:GetDrawTimes()
	return self.draw_times
end

--获取幸运值
function PanaceaFurnaceWGData:GetLuckyValue()
	return self.lucky
end

--兑换次数
function PanaceaFurnaceWGData:GetConvertTimesBySeq(seq)
	return self.convert_times_list[seq] or 0
end

--获取保底item
function PanaceaFurnaceWGData:GetBaodItem()
	return self.baodi_item
end

--获取抽奖奖励
function PanaceaFurnaceWGData:GetResultRewardList()
	return self.result_reward_list
end

function PanaceaFurnaceWGData:GetOtherCfg()
	return self.other_cfg
end

function PanaceaFurnaceWGData:GetModeCfgByMode(mode)
	return self.mode_cfg[mode]
end

function PanaceaFurnaceWGData:GetBaoDiCfgByGrade(grade)
	return self.baodi_cfg[grade]
end

function PanaceaFurnaceWGData:GetAllTimesRewardCfg()
	return self.times_reward_cfg[self.grade] or {}
end

function PanaceaFurnaceWGData:GetAllTimesRewardInfo()
	local sort_list = {}
	local cur_grade_times_reward_cfg = self.times_reward_cfg[self.grade]
	local index = 0
	if not IsEmptyTable(cur_grade_times_reward_cfg) then
		for k, v in pairs(cur_grade_times_reward_cfg) do
			index = index + 1
	 		sort_list[index] = {}
			sort_list[index].grade = v.grade
			sort_list[index].seq = v.seq
			sort_list[index].need_draw_times = v.need_draw_times
			sort_list[index].item = v.item
			sort_list[index].sort_index = 0
			local state = self:GetTimesRewardState(v.seq)
			if state == 1 then -- 已经领取奖励
				sort_list[index].sort_index = 1000
				sort_list[index].state = REWARD_STATE_TYPE.FINISH
			elseif state == 0 and self.draw_times >= v.need_draw_times then --可领取
				sort_list[index].sort_index = 10
				sort_list[index].state = REWARD_STATE_TYPE.CAN_FETCH
			else --不可领取
				sort_list[index].sort_index = 100
				sort_list[index].state = REWARD_STATE_TYPE.UNDONE
			end
		end

		table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "seq"))
	end

	return sort_list
end

function PanaceaFurnaceWGData:GetShowRewardPool()
	local show_list = {}
	local cur_grade_reward_pool_cfg = self.reward_pool_cfg[self.grade]
	if not IsEmptyTable(cur_grade_reward_pool_cfg) then
		for k, v in pairs(cur_grade_reward_pool_cfg) do
			if v.show_item == 1 then
				table.insert(show_list, v)
			end
		end
	end

	return show_list
end

function PanaceaFurnaceWGData:GetShowConvertData()
	local sort_list = {}
	local cur_grade_convert_cfg = self.convert_cfg[self.grade] or {}
	local index = 0
	if not IsEmptyTable(cur_grade_convert_cfg) then
		for k, v in pairs(cur_grade_convert_cfg) do
			index = index + 1
	 		sort_list[index] = {}
			sort_list[index].grade = v.grade
			sort_list[index].seq = v.seq
			sort_list[index].time_limit = v.time_limit
			sort_list[index].item = v.item
			sort_list[index].stuff_id_1 = v.stuff_id_1  --策划说只用一种材料
			sort_list[index].stuff_num_1 = v.stuff_num_1
			sort_list[index].sort_index = 0
			local cur_convert_times = self:GetConvertTimesBySeq(v.seq)
			sort_list[index].cur_convert_times = cur_convert_times
			if cur_convert_times >= v.time_limit then --兑换次数达到上限
				sort_list[index].sort_index = 100
			else
				sort_list[index].sort_index = 10
			end
		end

		table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "seq"))
	end

	return sort_list
end

--兑换材料
function PanaceaFurnaceWGData:GetIsStuffItem(item_id)
    return self.stuff_cfg[item_id] ~= nil
end

--获取抽奖的选项
function PanaceaFurnaceWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end


-- enable_anim为false表示跳过动画(奖励界面)
function PanaceaFurnaceWGData:SetRewardAnimToggleData(enable_anim)
	self.reward_anim_toggle_enable = enable_anim
end

-- 返回false表示跳过动画(奖励界面)
function PanaceaFurnaceWGData:GetRewardAnimToggleData()
	return self.reward_anim_toggle_enable or false
end

--抽獎界面动画（true 为跳过动画）
function PanaceaFurnaceWGData:GetJumpAni()
	return self.jump_ani or false
end

function PanaceaFurnaceWGData:SetJumpAni()
	self.jump_ani = not self.jump_ani
end

--是否跳过动画对应的延时
function PanaceaFurnaceWGData:GetDelayTime()
    --是否跳过动画
    if self.jump_ani then
        return 0
    else
        return 2
    end
end

function PanaceaFurnaceWGData:ShowPanaceaFurnaceRemind()
	--累计抽奖次数奖励红点
	local info = self:GetAllTimesRewardInfo()
	for i, v in pairs(info) do
		if v.state == REWARD_STATE_TYPE.CAN_FETCH then
			return 1
		end
	end

    local cost_item_id = PanaceaFurnaceWGData.Instance:GetOtherCfg().cost_item_id
    local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    if item_num > 0 then
        return 1
    end

	return 0
end

function PanaceaFurnaceWGData:GetGaiLvInfo()
	return self.item_random_desc[self.grade] or {}
end