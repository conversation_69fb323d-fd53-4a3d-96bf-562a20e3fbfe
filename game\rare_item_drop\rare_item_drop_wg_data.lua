RareItemDropWGData = RareItemDropWGData or BaseClass()

RareItemType = {
	Equip = 1,		-- 装备
	Fashion = 2,	-- 时装
	Special = 3,	-- 特殊珍稀物品
}

function RareItemDropWGData:__init()
	if RareItemDropWGData.Instance then
		error("[RareItemDropWGData]:Attempt to create singleton twice!")
		return
	end
	RareItemDropWGData.Instance = self
	self:InitParam()
end

function RareItemDropWGData:__delete()
	RareItemDropWGData.Instance = nil
end

function RareItemDropWGData:InitParam()
	self.limit_min_level = nil
	self.limit_max_level = nil
	self.limit_min_star = nil
	self.limit_max_star = nil
	self.limit_min_color = nil
	self.limit_max_color = nil
	self.special_rare_item_cfg_list = nil
	self.bag_rare_item_list = {}	-- 走背包的珍稀物品
	self.bag_rare_equip_list = {}   -- 走背包的珍稀装备
end

function RareItemDropWGData:AddBagRareItem(item_id, data, add_num)
	if self.bag_rare_item_list[item_id] then
		local temp_data = self.bag_rare_item_list[item_id]
		if not temp_data.item_num then
			temp_data.item_num = add_num + 1
		else
			temp_data.item_num = add_num + temp_data.item_num
		end
	else
		data.item_num = add_num
		self.bag_rare_item_list[item_id] = data
	end 
end

function RareItemDropWGData:SetBagRareItem(index, data)
	index = index or #self.bag_rare_item_list + 1
	self.bag_rare_item_list[index] = data
end

function RareItemDropWGData:SetBagRareEquipItem(index, data)
	table.insert(self.bag_rare_equip_list, data)
end

function RareItemDropWGData:GetBagRareItemList()
	return self.bag_rare_item_list
end

function RareItemDropWGData:GetBagRareEquipList()
	return self.bag_rare_equip_list
end

function RareItemDropWGData:CleanCahceItemList()
	self.bag_rare_item_list = {}
	self.bag_rare_equip_list = {}
end

function RareItemDropWGData:HasRareItem()
	if not IsEmptyTable(self.bag_rare_item_list) or not IsEmptyTable(self.bag_rare_equip_list) then
		return true
	end
	return false
end

-- 排序：外观（主动使用类）>四象>神兽装备>图鉴、锤子、混天绫（被动使用类）>粉金石头>装备
function RareItemDropWGData:GetSortRareItemList()
	local data_list = {}

	local bag_rare_item_list = self:GetBagRareItemList() -- 背包里的
	if not IsEmptyTable(bag_rare_item_list) then
		local temp_list = OperationActivityWGData.Instance:SortDataByItemColor(bag_rare_item_list, true)
		for k,v in pairs(temp_list) do
			if v.rare_item_type == RareItemType.Fashion then			-- 时装
				data_list[900 + k] = v
			elseif v.rare_item_type == RareItemType.Special then		-- 特殊珍稀物品
				data_list[500 + k] = v
			elseif v.rare_item_type == RareItemType.Equip then			-- 装备
				data_list[100 + k] = v
			end
		end
	end

	local bag_rare_equip_list = self:GetBagRareEquipList() -- 背包里的装备
	if not IsEmptyTable(bag_rare_equip_list) then
		local temp_list = OperationActivityWGData.Instance:SortDataByItemColor(bag_rare_equip_list, true)
		for i=1,#temp_list do
			data_list[100 + i] = temp_list[i]
		end
	end

	return SortTableKey(data_list, true)
end

-- 特殊珍稀物品
function RareItemDropWGData:GetSpecialRareItemCfg(item_id)
	if not self.special_rare_item_cfg_list then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
		local cfg_list = other_cfg and other_cfg.best_item_model or {}
		local temp_list = {}
		for i=1,#cfg_list do
			temp_list[cfg_list[i].item] = cfg_list[i]
		end
		self.special_rare_item_cfg_list = temp_list
	end
	return self.special_rare_item_cfg_list[item_id]
end

function RareItemDropWGData:GetLimitLevel()
	if not self.limit_min_level then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
		local cfg_list = other_cfg and other_cfg.best_equipment_tips or {}
		local cfg = cfg_list and cfg_list[1] or {}
		self.limit_min_level = cfg.min_level or 0
		self.limit_max_level = cfg.max_level or 0
	end
	return self.limit_min_level, self.limit_max_level
end

function RareItemDropWGData:GetLimitColor()
	if not self.limit_min_color then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
		local cfg_list = other_cfg and other_cfg.best_equipment_tips or {}
		local cfg = cfg_list and cfg_list[1] or {}
		local quality = cfg and Split(cfg.quality, "|")
		self.limit_min_color = quality and tonumber(quality[1]) or 0
		self.limit_max_color = quality and tonumber(quality[2]) or 0
	end
	return self.limit_min_color, self.limit_max_color
end

function RareItemDropWGData:GetLimitStar()
	if not self.limit_min_star then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
		local cfg_list = other_cfg and other_cfg.best_equipment_tips or {}
		local cfg = cfg_list and cfg_list[1] or {}
		local star_list = cfg and Split(cfg.star, "|")
		self.limit_min_star = star_list and tonumber(star_list[1]) or 0
		self.limit_max_star = star_list and tonumber(star_list[2]) or 0
	end
	return self.limit_min_star, self.limit_max_star
end