require("game/skill_break_purchase/skill_break_purchase_wg_data")
require("game/skill_break_purchase/skill_break_purchase_view")
require("game/skill_break_purchase/skill_break_purchase_skill_pre_view")

SkillBreakPurchaseWGCtrl = SkillBreakPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function SkillBreakPurchaseWGCtrl:__init()
	if SkillBreakPurchaseWGCtrl.Instance then
		ErrorLog("[SkillBreakPurchaseWGCtrl] attempt to create singleton twice!")
		return
	end

	SkillBreakPurchaseWGCtrl.Instance = self
	self.data = SkillBreakPurchaseWGData.New()
	self.view = SkillBreakPurchaseView.New(GuideModuleName.SkillBreakPurchase)
	self.skill_pre_view = SkillBreakPurchaseSkillPreView.New()

	self:RegisterAllProtocols()
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnPassDay, self))

	-- 活动改变
	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function SkillBreakPurchaseWGCtrl:__delete()
	if self.act_change then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
		self.act_change = nil
	end

	self.skill_pre_view:DeleteMe()
	self.skill_pre_view = nil

	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	SkillBreakPurchaseWGCtrl.Instance = nil
end

function SkillBreakPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCSkillBreakRmbBuyInfo, "OnSCSkillBreakRmbBuyInfo")
end

function SkillBreakPurchaseWGCtrl:RequestInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.SKILL_BREAK_PURCHASE
	protocol.opera_type = OA_SKILL_BREAK_RMB_BUY_OPER_TYPE.OA_SKILL_BREAK_RMB_BUY_OPER_TYPE_INFO
	protocol.param_1 = 0
	protocol.param_2 = 0
	protocol:EncodeAndSend()
end

function SkillBreakPurchaseWGCtrl:OnSCSkillBreakRmbBuyInfo(protocol)
	self.data:SetPurchaseInfo(protocol)
	-- local is_purchase = self:CheckIsPurchase()
	if self.view:IsOpen() then
		self.view:Flush()
		-- if is_purchase then
		-- 	self.view:Close()
		-- else
		-- 	self.view:Flush(TabIndex.skill_break_purchase)
		-- end
	end
end

-- 活动改变
function SkillBreakPurchaseWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.SKILL_BREAK_PURCHASE and status == ACTIVITY_STATUS.OPEN then
		self:CheckIsPurchase()
	end
end

-- 检查是否购买
function SkillBreakPurchaseWGCtrl:CheckIsPurchase()
	local purchase_info = self.data:GetPurchaseInfo()
	if IsEmptyTable(purchase_info) then
		return false
	end
	local is_purchase = purchase_info.reward_flat == 1
	if is_purchase then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SKILL_BREAK_PURCHASE, ACTIVITY_STATUS.CLOSE)
	end
	return is_purchase
end

--跨天
function SkillBreakPurchaseWGCtrl:OnPassDay()
	GlobalTimerQuest:AddDelayTimer(function()
		self:RequestInfo()
	end, 5)
end

-- 打开技能预览
function SkillBreakPurchaseWGCtrl:OpenRoleSkillPreView(item_index)
	self.skill_pre_view:SetNowShowIndex(item_index)

	if not self.skill_pre_view:IsOpen() then
		self.skill_pre_view:Open()
	else
		self.skill_pre_view:Flush()
	end
end