FishpondAllInfoView = FishpondAllInfoView or BaseClass(SafeBaseView)

local STORGE_BAG_MAX_GRID_NUM = 20		--最大格子数
local STORGE_BAG_PAGE_NUM = 2				-- 页数
local STORGE_BAG_PAGE_COUNT = 10			-- 每页个数
local STORGE_BAG_ROW = 2					-- 行数
local STORGE_BAG_COLUMN = 5


function FishpondAllInfoView:__init()
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_fishinfo")
	self.fish_data = {}
	self.item_list = {}
	self.is_modal = true
	-- self.max_page = 0
	-- self.page_count = 8 --一页可放个数
end

function FishpondAllInfoView:ReleaseCallBack()
	if nil ~= self.alert_boot then
		self.alert_boot:DeleteMe()
		self.alert_boot = nil
	end
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
	if nil ~= self.page_fishinfo_list then

		self.page_fishinfo_list = nil
	end
	for k,v in pairs(self.item_list) do
		v:DeleteMe()
	end
	self.extend_success_eff = nil
	self.item_list = {}
	self.page_fishinfo_list = nil
	self.btn_fishpond_extend = nil
	self.btn_close = nil
	self.label_fishinfo_tip = nil
	self.level_txt = nil
	self.exp_txt = nil
	self.num_txt = nil
	self.lbl_no_fish_tip = nil
end

function FishpondAllInfoView:LoadCallBack()
	--self.ph_fishinfo_list = self.node_list["ph_fishinfo_list"]
	self.page_fishinfo_list = self.node_list["ph_fishinfo_list"]

	local page_fishinfo_list_delegate = self.page_fishinfo_list.page_simple_delegate
	page_fishinfo_list_delegate.NumberOfCellsDel = BindTool.Bind(self.StorgeGetNumberOfCells, self)
	page_fishinfo_list_delegate.CellRefreshDel = BindTool.Bind(self.StorgeBagRefreshCell, self)

	self.btn_fishpond_extend = self.node_list.layout_extend
	self.btn_close = self.node_list.btn_close
	self.label_fishinfo_tip = self.node_list.label_fishinfo_tip
	self.level_txt = self.node_list.rich_pool_level
	self.exp_txt = self.node_list.lbl_fish_total_exp
	self.num_txt = self.node_list.rich_fish_total_num
	self.lbl_no_fish_tip = self.node_list.lbl_no_fish_tip

	XUI.AddClickEventListener(self.btn_fishpond_extend, BindTool.Bind1(self.FishpondExtend, self))
	XUI.AddClickEventListener(self.node_list.btn_yijian_catch, BindTool.Bind1(self.FishpondYijianCatch, self))
	XUI.AddClickEventListener(self.btn_close, BindTool.Bind1(self.Close, self))
end

function FishpondAllInfoView:FishpondYijianCatch()
	local uid = SocietyWGData.Instance:GetShowPond()
	local fish_list = SocietyWGData.Instance:GetFishpondFishInfoNoGuard(uid)

	for i,v in ipairs(fish_list) do
		local fish_time = SocietyWGData.Instance:GetCanHarvestTimeByType(v.fish_type)
		--print_error(fish_time,TimeWGCtrl.Instance:GetServerTime() - v.raise_timestamp)
		if math.floor(TimeWGCtrl.Instance:GetServerTime() - v.raise_timestamp) >= fish_time then
			--print_error(v)
			SocietyWGCtrl.Instance:SendFishPoolHarvest(v.fish_objid)
		end
	end
end

function FishpondAllInfoView:OnFlush(param_t)
	local data = SocietyWGData.Instance
	local uid = data:GetShowPond() --当前显示鱼塘的uid
	if nil == uid or 0 >= uid then return end
	if data:CurIsOwnPond() then
		self.is_can_harvest= true
		self.btn_fishpond_extend:SetActive(true)
		self.label_fishinfo_tip:SetActive(true)
		--self.node_list.btn_yijian_catch.enabled(true)
	else
		self.btn_fishpond_extend:setVisible(false)
		self.label_fishinfo_tip:setVisible(false)
		--self.node_list.btn_yijian_catch.enabled(false)
	end

	local all_info = data:GetFishpondAllInfo(uid)
	if nil == all_info then return end
	self:UpdateNormal(all_info)
	local fish_list = data:GetFishpondFishInfoNoGuard(uid)
	self.fish_cfg_data = __TableCopy(fish_list)
	self.fish_cfg_data[0] = table.remove(self.fish_cfg_data, 1)
	--print_error(fish_list,#fish_list)
	if fish_list and #fish_list > 0 and nil == self.page_fishinfo_list then
		self:CreateItemList(fish_list)
		--print_error("重新reload0")
	elseif fish_list and #fish_list > 0 and nil ~= self.page_fishinfo_list then


		local fish_count = #self.fish_cfg_data
		self.lbl_no_fish_tip:SetActive(false)

		--print_error("重新reload3")

		self.page_fishinfo_list.list_view:Reload(function()
			self.page_fishinfo_list.list_page_scroll2:JumpToPageImmidate(0)
		end)

		--self.page_fishinfo_list:SetDataList(fish_cfg)
	else
	--print_error("重新reload2")
		self.page_fishinfo_list.list_view:Reload(function()
			self.page_fishinfo_list.list_page_scroll2:JumpToPageImmidate(0)
		end)
		self.lbl_no_fish_tip:SetActive(true)
	end

	self.node_list.img_pool_extend_remind:SetActive(data:GetFishExtendRemind() > 0) --灵池扩建提醒
	local fish_ripe_count = 0
	local fish_list = data:GetFishpondFishInfoNoGuard(RoleWGData.Instance.role_vo.role_id)
	for i,v in ipairs(fish_list) do
		local fish_time = data:GetCanHarvestTimeByType(v.fish_type)
		if math.floor(TimeWGCtrl.Instance:GetServerTime() - v.raise_timestamp) >= fish_time then
			fish_ripe_count = fish_ripe_count + 1
		end
	end
	self.node_list.img_yijian_catch_remind:SetActive(data:CurIsOwnPond() and fish_ripe_count >= #fish_list and  #fish_list ~= 0 )
end

function FishpondAllInfoView:StorgeGetNumberOfCells()
	local temp_num = 0
	return STORGE_BAG_MAX_GRID_NUM
end

function FishpondAllInfoView:CreateItemList(fish_list)
	--print_error(fish_list)
	self.lbl_no_fish_tip:SetActive(false)

	if #fish_list == 0 then
		self.lbl_no_fish_tip:SetActive(true)
		return
	end
	self.fish_cfg_data = __TableCopy(fish_list)
end

function FishpondAllInfoView:StorgeBagRefreshCell(index, cellObj)
	--print_error(index)
	-- 构造Cell对象.
	local cell = self.page_fishinfo_list[cellObj]
	if nil == cell then
		cell = FishInfoItem.New(cellObj)
		--cell:SetToggleGroup(self.storge_bag_list_view.toggle_group)
		self.page_fishinfo_list[cellObj] = cell
	end
	cell:SetIndex(index)
	local page = math.floor(index / STORGE_BAG_PAGE_COUNT)
	local cur_colunm = math.floor(index / STORGE_BAG_ROW) + 1 - page * STORGE_BAG_COLUMN
	local cur_row = math.floor(index % STORGE_BAG_ROW) + 1
	local grid_index = (cur_row - 1) * STORGE_BAG_COLUMN - 1 + cur_colunm  + page * STORGE_BAG_ROW * STORGE_BAG_COLUMN
	cell:SetData(self.fish_cfg_data[grid_index])
	--print_error(index,"     ",grid_index)

end

function FishpondAllInfoView:RemoveAllItem()
	if self.item_list then
		for k, v in pairs(self.item_list) do
			v:GetView():removeFromParent()
		end
		self.item_list = {}
	end
end

-- 更新基本信息
function FishpondAllInfoView:UpdateNormal(info)
	if nil == info or nil == info.normal_info then return end

	local normal_info = info.normal_info
	local data = SocietyWGData.Instance

	self.level_txt.text.text = normal_info.pool_level
	local lv_cfg = data:GetFishpondCfgByLv(normal_info.pool_level)
	if nil == lv_cfg then return end

	if data:IsFishpondMaxLv(normal_info.pool_level) then
		local fish_level_content = string.format(Language.Fishpond.FishPondMaxLv)
		self.exp_txt.text.text = 0 .. "/" .. 0
	else
		local persent = normal_info.pool_exp  / lv_cfg.max_exp * 100
		self.exp_txt.text.text = normal_info.pool_exp .. "/" .. lv_cfg.max_exp
	end

	local common_fish_capacity = lv_cfg.common_fish_capacity or 0
	local max_capacity = common_fish_capacity + normal_info.extend_capacity
	local fish_num = data:GetRewardFishNum(normal_info.owner_uid)
	local content = string.format(Language.Fishpond.HasFishNum, fish_num, max_capacity)
	self.num_txt.text.text = content
end

-- 点击扩展
function FishpondAllInfoView:FishpondExtend()
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local normal_info = SocietyWGData.Instance:GetFishpondNormalInfo(role_id)
	if nil == normal_info then return end
	local pood_cfg =  SocietyWGData.Instance:GetExtendCapacityByNum(normal_info.extend_capacity + 1)
	if nil == pood_cfg then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fishpond.ExtendMaxTip)
		return
	end
	if pood_cfg.use_coin_need_pool_level <= normal_info.pool_level then
		if nil == self.alert_window then
			self.alert_window = Alert.New()
		end
		local content = string.format(Language.Fishpond.ExtendOneMoreTipByCoin, pood_cfg.coin_cost)
		self.alert_window:SetLableString(content)
		self.alert_window:SetOkFunc(function()
			SocietyWGCtrl.Instance:SendFishPoolExtendCapacity(1)
		end)
		self.alert_window:Open()
	else

		if nil == self.alert_window then
			self.alert_window = Alert.New()
		end
		local content = string.format(Language.Fishpond.ExtendTip, pood_cfg.use_coin_need_pool_level, pood_cfg.gold_cost)
		self.alert_window:SetLableString(content)
		self.alert_window:SetOkFunc(function()
			--self:Close()
			content = string.format(Language.Fishpond.ExtendOneMoreTipByGold, pood_cfg.gold_cost)
			self.alert_window:SetLableString(content)
			self.alert_window:SetOkFunc(function()
				SocietyWGCtrl.Instance:SendFishPoolExtendCapacity(2)
			end)
			GlobalTimerQuest:AddDelayTimer(function()
				self.alert_window:Open()
			end, 0)
		end)
		self.alert_window:Open()
	end
 end

--鱼池扩展成功特效
function FishpondAllInfoView:PlayPoolExtendResultEffect(effect_id)

end

------------------------------------
--item
------------------------------------
FishInfoItem = FishInfoItem or BaseClass(BaseRender)

function FishInfoItem:__init()
	self.icon = self.node_list["icon"]
	--self.exp_param = self.node_list["lbl_fish_exp"]
	--self.coin_param = self.node_list["lbl_fish_coin"]

	self.reward_txt = self.node_list.lbl_fish_coin
	self.exp_txt = self.node_list.lbl_fish_exp
	self.harvest_btn = self.node_list.btn_catch

	--self.boot_btn = self.node_list.btn_fishpond_boot
	--self.boot_btn:SetActive(false)
	XUI.AddClickEventListener(self.harvest_btn, BindTool.Bind1(self.ClickHarvestHandler, self))
	--XUI.AddClickEventListener(self.boot_btn, BindTool.Bind1(self.ClickBootHandler, self), true)
end

function FishInfoItem:__delete()
	if nil ~= self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
	self.icon = nil
	self.reward_txt = nil
	self.exp_txt = nil
	self.harvest_btn = nil
end

function FishInfoItem:OnFlush()
	--print_error(self.data)
	if nil == self.data then
		self.node_list.root_shouhuo_item:SetActive(false)
		return

	else
	 self.node_list.root_shouhuo_item:SetActive(true)
	end
	--print_error(self.data)
	local data_instance = SocietyWGData.Instance
	local lv_cfg = data_instance:GetFishLvCfgByTypeAndTime(self.data.fish_type, self.data.raise_timestamp) or {}
	local ccc = SocietyWGData.Instance:GetFishCfgByType(self.data.fish_type)
	if nil == lv_cfg then return end

	local fish_cfg = data_instance:GetFishCfgByType(self.data.fish_type)
	if nil == fish_cfg then return end

	self.harvest_btn:SetActive(data_instance:CurIsOwnPond())
	--print_error(ccc)

	local bundle,asset = ResPath.GetItem(fish_cfg.fish_id)
	self.node_list["icon"].image:LoadSprite(bundle,asset)
	local reward_content = data_instance:GetFishRewardDesc(lv_cfg)
	self.reward_txt.text.text = reward_content

	self.exp_txt.text.text = ""
	if lv_cfg.harvest_pool_exp ~= nil then
		self.exp_txt.text.text = lv_cfg.harvest_pool_exp .. Language.Fishpond.Exp
	end

	local fish_time = data_instance:GetCanHarvestTimeByType(self.data.fish_type)
	if math.floor(TimeWGCtrl.Instance:GetServerTime() - self.data.raise_timestamp) >= fish_time then
		self.node_list.img_catch_remind:SetActive(data_instance:CurIsOwnPond() and true)
	else
		self.node_list.img_catch_remind:SetActive(false)
	end

end

function FishInfoItem:ClickHarvestHandler()
	if nil == self.data then return end

	SocietyWGCtrl.Instance:SendFishPoolHarvest(self.data.fish_objid)
end

function FishInfoItem:ClickBootHandler()
	if nil ~= self.data then
		if self.data.is_buy_boot == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Fishpond.FishBootTis)
			return
		else
			if nil == self.alert_boot then
				self.alert_boot = Alert.New(nil, nil, nil, nil, false)
			end
			self.alert_boot:SetLableString(string.format(Language.Fishpond.FishInfoTis, SocietyWGData.Instance:GetBuyProtectPrice()))
			self.alert_boot:SetOkFunc(BindTool.Bind1(function ()
				SocietyWGCtrl.Instance:SendBuyFishBoot(self.data.fish_objid)
				end, self))
			self.alert_boot:Open()
		end
	else
		return
	end
end
