require("game/rolebag/equip_target/equip_target_view")
require("game/rolebag/equip_target/equipment_integration_view")
require("game/rolebag/equip_target/equip_target_wg_data")
require("game/rolebag/equip_target/equipment_integration_wg_data")
require("game/rolebag/equip_target/equip_target_tips_view")
require("game/rolebag/equip_target/equip_target_compose_view")
require("game/rolebag/equip_target/equip_target_show")
require("game/rolebag/equip_target/boss_notice_target_view")
require("game/rolebag/equip_target/boss_notice_target_wg_data")
require("game/rolebag/equip_target/equipment_bag_reslove_view")
require("game/rolebag/equip_target/equipment_integration_upgrade_view")

EquipTargetWGCtrl = EquipTargetWGCtrl or BaseClass(BaseWGCtrl)

function EquipTargetWGCtrl:__init()
    if EquipTargetWGCtrl.Instance then
        error("[EquipTargetWGCtrl]:Attempt to create singleton twice!")
    end

    EquipTargetWGCtrl.Instance = self

    self.view = EquipTargetView.New(GuideModuleName.EquipTargetView)
    self.data = EquipTargetWGData.New()
    self.get_equip_show = GetEquipTargetIconShow.New()
    self.tips_view = EquipTargetTipsView.New()
    self.boss_notice_view = BossNoticeTargetTipsView.New()
    self.boss_notice_data = BossNoticeTargetTipsWGData.New()
    self.ei_upgrade_view = EquipmentIntegrationUpgradeView.New()
    self.reslove_view = EquipmentBagResloveView.New(GuideModuleName.EquipmentBagResloveView)
    self.equip_compose_view = EquipTargetComposeView.New(GuideModuleName.EquipTargetComposeView)
    
    self:RegisterAllProtocals()
    self:RegisterAllEvents()
    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))

    self.is_flish = true
    self.is_show_boss_notice_task = false
    self.is_first_bag_clean = true
end

function EquipTargetWGCtrl:__delete()
    self:UnRegisterAllEvents()

    self.data:DeleteMe()
    self.data = nil
    self.is_flish = true
    self.boss_notice_view:DeleteMe()
    self.boss_notice_data:DeleteMe()
    self.boss_notice_view = nil
    self.boss_notice_data = nil

    if self.reslove_view then
        self.reslove_view:DeleteMe()
        self.reslove_view = nil
    end

    if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
	end

    EquipTargetWGCtrl.Instance = nil 
end

function EquipTargetWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCEquipTargetAllInfo, "OnSCEquipTargetAllInfo")
    self:RegisterProtocol(SCEquipTargetInfo, "OnSCEquipTargetInfoChange")
    self:RegisterProtocol(SCEquipTargetKillBoss, "SCEquipTargetKillBoss")
    self:RegisterProtocol(SCRoleNoticeBossStatus,"OnSCRoleNoticeBossStatus")
    self:RegisterProtocol(SCRoleNoticBossSaveClientInfo,"OnSCRoleNoticBossSaveClientInfo")
    
    self:RegisterProtocol(CSEquipTargetOpera)
    self:RegisterProtocol(CSBossNoticeTaskStatus)
    
    self:RegisterEIProtocols()
end


function EquipTargetWGCtrl:Open(tab_index, param_t)
    ViewManager.Instance:Open(GuideModuleName.Bag, tab_index, nil, param_t)
end

-- 刷新界面
function EquipTargetWGCtrl:FlushView(index, key, param_t)
    --RoleBagWGCtrl.Instance:Flush(index, key, param_t)
    self.view:Flush()
    ViewManager.Instance:FlushView(GuideModuleName.CultivationView, TabIndex.equip_target)
end

--切换标签请求
function EquipTargetWGCtrl:SendEquipTarget(req_type, param1, param2)
    -- print_error("SendEquipTarget", req_type, param1)
    local protocol = ProtocolPool.Instance:GetProtocol(CSEquipTargetOpera)
    protocol.req_type = req_type or 0
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol:EncodeAndSend()
end

function EquipTargetWGCtrl:OnSCEquipTargetAllInfo(protocol)
    --print_error("OnSCEquipTargetAllInfo protocol.system_open_flag", protocol.system_open_flag, protocol)
    local old_status = self.data:GetOldDataFetchedList()
    if not IsEmptyTable(old_status) then
        local cfg = EquipTargetWGData.Instance:GetEquipinfo(old_status.suit_index)
        --print_error(old_status.suit_active_reward)
        --print_error(protocol.m_all_param[old_status.suit_index + 1].suit_active_reward == 1)
        if old_status.suit_active_reward == 0 and protocol.m_all_param[old_status.suit_index + 1].suit_active_reward == 1 then
            if cfg.display_type == TARGET_TYPE.WUQI and cfg.suit_active_reward_type == TARGET_SUIT_ACTIVE_REWARD_TYPE.SKILL then                                           --激活技能
                local other_cfg = self.data:GetEquipinfo(old_status.suit_index)
                local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(other_cfg.suit_active_reward)
                if not IsEmptyTable(skill_data) then
                    local data = {name = skill_data.name, desc = skill_data.desc, res_fun = ResPath.GetSkillIconById, icon = skill_data.icon}
                    TipWGCtrl.Instance:ShowGetNewSkillView2(data)
                end
            end
        end
    end

    self.data:SetAllEquipInfo(protocol.m_all_param)
    self.data:SetSystemOpenFlag(protocol.system_open_flag)
    --print_error(protocol.item_id)
    if protocol.put_on_item_id > 0 then
        self:OnCheckPutEquipItem(protocol.put_on_suit_index + 1, protocol.put_on_item_id)--装备目标检测
    elseif protocol.put_on_item_id <= 0 or self.is_flish then
        self.data:SetOldEquipListInfo()
        self:FlushTargetTip()
    end

    self.is_flish = false

    --self:FlushView(TabIndex.rolebag_Target)
    self.view:Flush()
    ViewManager.Instance:FlushView(GuideModuleName.CultivationView, TabIndex.equip_target)
    RemindManager.Instance:Fire(RemindName.EquipTarget)
    MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
        MainuiWGCtrl.Instance.view:EquipCollectFlushInfo()
    end)
end

function EquipTargetWGCtrl:SCEquipTargetKillBoss(protocol)
    -- print_error("SCEquipTargetKillBoss++++++++", protocol.trait_index)
    self.data:SetEquipKillBossInfo(protocol.trait_index)
    --self:FlushView(TabIndex.rolebag_Target)
    self.view:Flush()
end

--切换标签返回
function EquipTargetWGCtrl:OnSCEquipTargetInfoChange(protocol)
    -- print_error("切换标签返回--------", protocol)
    self.data:SetEquipInfoChange(protocol)
    --self:FlushView(TabIndex.rolebag_Target)
    self.view:Flush()
    RemindManager.Instance:Fire(RemindName.EquipTarget)
end

--穿上装备动画播放完了，回调事件
function EquipTargetWGCtrl:PutEquipAnimComplete()
    self.data:SetOldEquipListInfo()
    if self.tips_view and self.tips_view:IsOpen() then
        self.tips_view:Flush()
        self.tips_view:CheckAutoClose(1)
    end
end

function EquipTargetWGCtrl:ShowGetItem(item_data)
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
    if item_cfg then
        self.get_equip_show:Show(item_data)
    end
end

--穿上装备后判断是否目标装备
function EquipTargetWGCtrl:OnCheckPutEquipItem(suit_index, item_id)
    if self.boss_notice_view and self.boss_notice_view:IsOpen() then
        return
    end

    -- VIPBoss 一，二层常驻时不处理非指定套装
    local is_in, show_suit_index = EquipTargetWGData.Instance:IsVIPBossScene()
    -- print_error("suit_index:",suit_index)
    if is_in and (suit_index -1) ~= show_suit_index then 
        -- 高星的激活的 必定可以激活低星的 这时不能return 不然会不刷新
        if show_suit_index == 0 and (suit_index -1) == 1 then  
        elseif show_suit_index == 2 and (suit_index -1) == 3 then
        else
            return
        end
        
    end

    local is_eq, equip_id = EquipTargetWGData.Instance:CheckEquipChange(suit_index, item_id)
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    local is_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.EquipTargetView)
    if not is_eq or fb_scene_cfg.frame_show == 0 or not is_show then return end

    local next_open_func = function ()
        local show_func = function ()
            local item_data = {}
            item_data.item_id = equip_id
            item_data.anim_complete_fun = BindTool.Bind(self.PutEquipAnimComplete,self)
            self:ShowGetItem(item_data)
        end
        if self.boss_notice_view and self.boss_notice_view:IsOpen() then
            return
        end
        if self.tips_view then
            if self.tips_view:IsOpen() then
                show_func()
            else
                self.tips_view:SetLoadCallBack(show_func())
            end
        end
        self:OnPutEquipEvent()    
    end
    TipWGCtrl.Instance:OpenDropEquipShow2(next_open_func)
end

function EquipTargetWGCtrl:OpenEquipTargetCompose()
    if not self.equip_compose_view:IsOpen() then
        self.equip_compose_view:Open()
    end
end

--打开主界面装备栏
function EquipTargetWGCtrl:OnPutEquipEvent()
    if not self.tips_view:IsOpen() then
        self.tips_view:Open()
    else
        self.tips_view:CheckAutoClose()
    end
end

--关闭主界面装备栏
function EquipTargetWGCtrl:CloseEquipTipsView()
    if self.tips_view and self.tips_view:IsOpen() then
        self.tips_view:Close()
    end
end

--获取装备栏装备对应位置
function EquipTargetWGCtrl:GetEquipBtnNode()
    if self.tips_view and self.tips_view:IsOpen() then
        local node = self.tips_view:GetEquipBtnNode()
        return node
    end
end

-- --打开目标装备界面
-- function EquipTargetWGCtrl:OpenEquipTargetView()
--     if not self.view:IsOpen() then
--         self.view:Open()
--     end
-- end

function EquipTargetWGCtrl:CheckTargetTipIsOpen()
    if self.tips_view then
        return self.tips_view:IsOpen()
    end

    return false
end

function EquipTargetWGCtrl:FlushTargetTip()
    if self.tips_view and self.tips_view:IsOpen() then
        self.tips_view:Flush()
    end
end


function EquipTargetWGCtrl:OpenBossNoticeTip()
    if self.tips_view and self.tips_view:IsOpen() then
        self.tips_view:Close()
    end

    if self.boss_notice_view then
        self.boss_notice_view:Open()
    end
end

function EquipTargetWGCtrl:CheckNeedBossNoticeTip()
    -- local suit_index = self.boss_notice_data:GetCurGradeStatus()
    -- if suit_index ~= -1 and self.boss_notice_data:CheckBossNoticeSuitEquipFlag(suit_index) == 0 then
    --     return true
    -- end
    return false
end

function EquipTargetWGCtrl:CloseBossNoticeTip()
    if self.boss_notice_view and self.boss_notice_view:IsOpen() then
        self.boss_notice_view:Close()
        MainuiWGCtrl.Instance:FlushTaskView()
    end
end

function EquipTargetWGCtrl:PlayBossTargetAnim(cell_index)
    if self.boss_notice_view and self.boss_notice_view:IsOpen() then
        self.boss_notice_view:PlayBossTargetAnim(cell_index)
    end
end

function EquipTargetWGCtrl:GetBossTargetCellNode(cell_index)
    if self.boss_notice_view and self.boss_notice_view:IsOpen() then
        local node = self.boss_notice_view:GetBossTargetCellNode(cell_index)
        return node
    end
end

function EquipTargetWGCtrl:CompleteBossTargetAnim(cell_index)
    if self.boss_notice_view and self.boss_notice_view:IsOpen() then
        self.boss_notice_view:CompleteBossTargetAnim(cell_index)
    end
end

function EquipTargetWGCtrl:CheckIsShowBossNoticeTask()
    return false
    --return self.is_show_boss_notice_task
end

function EquipTargetWGCtrl:ClickBossNoticeTask()
    local can_commit = BossNoticeTargetTipsWGData.Instance:GetIsCanCommit()
    if can_commit then
        self.is_show_boss_notice_task = false
        MainuiWGCtrl.Instance:FlushTaskView()
    else
        ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
    end
end

function EquipTargetWGCtrl:OnSCRoleNoticeBossStatus(protocol)
    self.is_show_boss_notice_task = true
    self.boss_notice_data:OnSCRoleNoticeBossStatus(protocol)
    MainuiWGCtrl.Instance:FlushTaskView()
end

function EquipTargetWGCtrl:OnSCRoleNoticBossSaveClientInfo(protocol)
    self.boss_notice_data:OnSCRoleNoticBossSaveClientInfo(protocol)
    self:CheckNeedShowBossNotice()
end

function EquipTargetWGCtrl:CheckNeedShowBossNotice()
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.VIP_BOSS and self:CheckNeedBossNoticeTip() then
        self:OpenBossNoticeTip()
    end
end

function EquipTargetWGCtrl:CompleteBossNoticeTask(suit_index)
    local protocol = ProtocolPool.Instance:GetProtocol(CSBossNoticeTaskStatus)
    protocol.suit_index = suit_index or 0
    protocol:EncodeAndSend()
    --self.is_show_boss_notice_task = false
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.VIP_BOSS then
        GlobalEventSystem:Fire(OtherEventType.BOSS_CHANGE)
    end
end

function EquipTargetWGCtrl:TryCompleteBossNoticeTask(suit_index)
    if self.boss_notice_view and self.boss_notice_view:IsOpen() then
        return
    end
    self:CompleteBossNoticeTask(suit_index)
end

--生成变强按钮
function EquipTargetWGCtrl:CreatMainUiActbtn(mainbtn_type,is_creat)
	-- if is_creat == 0 then
    --     MainuiWGCtrl.Instance:InvateTip(mainbtn_type , 0)
	-- else
    --     MainuiWGCtrl.Instance:InvateTip(mainbtn_type , 1, function ()
    --         FunOpen.Instance:OpenViewByName(GuideModuleName.CultivationView, TabIndex.equip_target)
    --         -- FunOpen.Instance:OpenViewByName(GuideModuleName.EquipTargetView)
    --         return true
    --     end)
	-- end
end

function EquipTargetWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all then
        MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
            MainuiWGCtrl.Instance.view:EquipCollectFlushInfo()
        end)
	elseif fun_name == FunName.EquipTargetView then
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
            MainuiWGCtrl.Instance.view:EquipCollectFlushInfo()
        end)
    end
end


--===================================================================================
-----------------------------------【装备集成Ctrl】-----------------------------------
--===================================================================================
function EquipTargetWGCtrl:RegisterEIProtocols()
    -- self:RegisterProtocol(CSEquipmentIntegrationReq)
	-- self:RegisterProtocol(SCEquipmentIntegrationInfo, "OnSCEquipmentIntegrationInfo")
    -- 装备集成
    self:RegisterProtocol(SCEquipIntegrationAllInfo, "OnSCEquipIntegrationAllInfo")
    self:RegisterProtocol(SCEquipIntegrationSingleInfo, "OnSCEquipIntegrationSingleInfo")
    self:RegisterProtocol(SCEquipCollectSuitLevelUpdate, "OnSCEquipCollectSuitLevelUpdate")
    self:RegisterProtocol(CSEquipIntegrationInfo)
    self:RegisterProtocol(CSEquipCollectDecomps)
end

function EquipTargetWGCtrl:RegisterAllEvents()
    -- 物品改变
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

    -- 角色属性改变
    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function EquipTargetWGCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

-- 请求操作
function EquipTargetWGCtrl:SendEIOperateReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSEquipIntegrationInfo)
    protocol.opera_type = opera_type
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol.param_3 = param_3 or 0
    protocol:EncodeAndSend()
end

-- 请求分解操作
function EquipTargetWGCtrl:SendEIResloveItemList(reslove_list)
    local protocol = ProtocolPool.Instance:GetProtocol(CSEquipCollectDecomps)
    protocol.reslove_list = reslove_list or {}
	protocol:EncodeAndSend()
end

-- 装备集成-所有激活信息
function EquipTargetWGCtrl:OnSCEquipIntegrationAllInfo(protocol)
    --print_error("-----所有激活信息-----",protocol)
    self.data:SetEIAllInfo(protocol)
    self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "protocol_change")
    RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
end

-- 装备集成-单个槽位激活
function EquipTargetWGCtrl:OnSCEquipIntegrationSingleInfo(protocol)
    --print_error("-----单个槽位激活-----", protocol)
    self.data:SetEISingleInfo(protocol)
    self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "protocol_change")
    RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
end

-- 装备集成-单个套装等级改变
function EquipTargetWGCtrl:OnSCEquipCollectSuitLevelUpdate(protocol)
    --print_error("-----单个套装等级-----", protocol)
    self.data:SetEISingleSuitLevelInfo(protocol)
    self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "protocol_change")

    if self.ei_upgrade_view:IsOpen() then
        self.ei_upgrade_view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
end

-- 刷新装备集成界面
function EquipTargetWGCtrl:FlushEquipIntegrationView(index, key, param_t)
    RoleBagWGCtrl.Instance:Flush(index, key, param_t)
end

function EquipTargetWGCtrl:OpenEIUpgradeView(data)
    self.ei_upgrade_view:SetData(data)
    self.ei_upgrade_view:Open()
end

function EquipTargetWGCtrl:UpdateAllRemind()
    if self.is_first_bag_clean then
        self.is_first_bag_clean = false
        return
    end

    self.data:SetEIToggleList()
    self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "item_change")
    RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
end

-- 物品变化
function EquipTargetWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num, need_old_param)
    --print_error("----物品变化----", change_item_id, change_item_index, change_reason, old_num, new_num)
    if change_item_index == nil or (change_item_index >= COMMON_CONSTS.MAX_BAG_COUNT and change_item_index < (COMMON_CONSTS.MAX_BAG_COUNT * 2)) then
        return
    end

    if not self.data:GetEIIsStuffItemID(change_item_id) and not self.data:GetEIIsLevelStuffItemID(change_item_id) then
        return
    end

    local is_add = change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	                (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num)
    local is_remove = change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE

    if not is_add and not is_remove then
        return
    end

    if self.data:GetEIIsStuffItemID(change_item_id) then
        local item_data = ItemWGData.Instance:GetGridData(change_item_index)
        local star = (item_data and item_data.param and item_data.param.star_level) or 0
        if is_remove then
            star = need_old_param.star
        end
        
        local cfg = self.data:GetEIIsStuffItemIDByStar(change_item_id, star)
        if cfg == nil then
            return
        end
        
        -- 物品数量增加
        if is_add then
            if self.data:GetEIItemIdCanAct(change_item_id, star, true) then
                self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "item_change")
                RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
            end
        else
            local act_flag = self.data:GetSlotIsAct(cfg.suit_index, cfg.slot_index)
            if not act_flag then
                self.data:GetEIItemIdCanAct(change_item_id, star, false)
                self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "item_change")
                RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
            end
        end

        if self.reslove_view:IsOpen() then
            self.reslove_view:Flush()
        end
    elseif self.data:GetEIIsLevelStuffItemID(change_item_id) then
        self.data:GetEIItemIdCanUpSuitLevel()
        self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "item_change")
        RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
    end
end

-- 角色属性改变
function EquipTargetWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
        local level_list = self.data:GetEIShowLevelList()
        for k,v in pairs(level_list) do
            if old_value < k and value >= k then
                self.data:SetEIToggleList()
                self:FlushEquipIntegrationView(TabIndex.rolebag_Integration, "lv_change")
                RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
                return
            end
        end
	end
end

-- 装备集成套装激活
function EquipTargetWGCtrl:OnEISuitActiveCallBack(protocol)
	if protocol.result == 1 then
        local act_cfg = EquipTargetWGData.Instance:GetEISuitActCfg(protocol.param1)
        if act_cfg then
            local data = {name = act_cfg.name, icon = act_cfg.icon}
            TipWGCtrl.Instance:ShowGetNewSkillView2(data)
        end
    end
end

function EquipTargetWGCtrl:OnEISuitUpLevelResult(result, sit_index)
	if 0 == result then
		self.ei_upgrade_view:StopLevelOperator()
	elseif 1 == result  then
        if self.ei_upgrade_view:IsAutoUpLevel() then
            self.ei_upgrade_view:AutoUpLevelUpOnce()
        end

        self.ei_upgrade_view:PlayEiUseEffect(UIEffectName.s_shengji)
	end
end