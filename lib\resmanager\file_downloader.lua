-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local UnityWebRequest = UnityEngine.Networking.UnityWebRequest
local UnityYield = UnityEngine.Yield
local UnityTime = UnityEngine.Time
local SysFile = System.IO.File

local _sformat = string.format

local SPEED_SAMPLE_INTERVAL = 0.5

local M = ResUtil.create_class()

local zeroInt64 = int64.new(0, 0)
local zeroUInt64 = uint64.zero
local fhInt64 = int64.new(0, 400)

function M:_init(url, update_callback, complete_callback, cache_path, bundle_name, bundle_hash, check_hash)
	self:ResetSample()

	self.v_progress = 0
	self.v_downloaded_bytes = 0
	self.v_update_callback = update_callback
	self.v_complete_callback = complete_callback

	coroutine.start(function()
		if ResUtil.log_debug then
			ResUtil.Log("[FileDownloader] start download file:", url)
		end

		self.v_request = UnityWebRequest.Get(url)
		local www = self.v_request:SendWebRequest()
		coroutine.www(www)

		if IsGameStop then
			self.v_request:Dispose()
			return
		end

		local net_error = self.v_request.result == UnityWebRequest.Result.ConnectionError or self.v_request.result == UnityWebRequest.Result.DataProcessingError
		local http_error = self.v_request.result == UnityWebRequest.Result.ProtocolError
		local err
		if net_error then
			err = _sformat("[FileDownloader]download load fail, network error: %s %s", url,  self.v_request.error)
		elseif http_error then
			err = _sformat("[FileDownloader]download load fail, http error: %s", url)
		elseif self.v_request.responseCode < zeroInt64 or  self.v_request.responseCode >= fhInt64 then
			err = _sformat("[FileDownloader]download load fail, code error: %s %s", url,  self.v_request.responseCode)
		elseif self.v_request.downloadedBytes <= zeroUInt64 then
			err = _sformat("[FileDownloader]download load fail, bytes error: %s %s", url,  self.v_request.downloadedBytes)
		end

		local callback = function (_error)
			if nil ~= _error then
				print_error("[FileDownloader] " .. _error)
			end

			if ResUtil.log_debug then
				ResUtil.Log("[FileDownloader] download file complete:", url)
			end
			complete_callback(_error, self.v_request)

			self.v_request:Dispose()
			self:Destroy()
		end

		self:CheckComplete(callback, cache_path, bundle_name, err, bundle_hash, check_hash)
	end)
end

function M:CheckComplete(callback, cache_path, bundle_name, err, bundle_hash, check_hash)
	-- 如果AssetBundleMgr也在下载，可能出现两边都在写入同一个文件，从而导致一方会报错
	-- 所以这里应该等待AssetBundleMgr
	if AssetBundleMgr:IsAssetBundleDownloading(cache_path) then
		local finish_callback = function (is_succ)
			if is_succ then
				callback(nil)
			else
				callback("AssetBundleMgr download fail")
			end
		end
		AssetBundleMgr:WaitAssetBundleDownloaded(cache_path, bundle_name, finish_callback)
		return
	end

	if nil == err then
		if not RuntimeAssetHelper.TryWriteWebRequestData(cache_path, self.v_request) then
			if SysFile.Exists(cache_path) then
				os.remove(cache_path)
			end

			err = _sformat("download load fail, write file error %s", cache_path)
		elseif check_hash and nil ~= bundle_hash and nil ~= tonumber(bundle_hash) and SysFile.Exists(cache_path) then
			local cal_md5 = MD5.GetMD5FromFile(cache_path)
			if tonumber(bundle_hash) ~= cal_md5 then
				os.remove(cache_path)
				err = _sformat("download load fail, md5 not match %s expect:%s cal:%s", cache_path, bundle_hash, cal_md5)
			end
		end
	end

	callback(err)
end

function M:Update()
	if self.v_destroy then
		return false
	end

	if self.v_request then
		local download_bytes = self.v_request:GetByteDownloads()
		self.v_sample_bytes = self.v_sample_bytes + download_bytes - self.v_downloaded_bytes
		self.v_downloaded_bytes = download_bytes
		self.v_progress = self.v_request.downloadProgress

		local interval = UnityTime.unscaledTime - self.v_sample_time
		self.v_sample_speed = self.v_downloaded_bytes / interval

		if self.v_update_callback then
			self.v_update_callback(
				self.v_progress,
				self.v_sample_speed,
				self.v_downloaded_bytes,
				0)
		end

		if interval >= SPEED_SAMPLE_INTERVAL then
			self:ResetSample()
		end
	end

	return true
end

function M:ResetSample()
	self.v_sample_bytes = 0
	self.v_sample_speed = 0
	self.v_sample_time = UnityTime.unscaledTime
end

function M:Destroy()
	self.v_destroy = true
	self.v_request = nil
	self.v_update_callback = nil
	self.v_complete_callback = nil
end

return M

