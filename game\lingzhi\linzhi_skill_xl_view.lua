function LinZhiSkillView:XLReleaseCallBack()
	if self.xl_attr_list then
		self.xl_attr_list:DeleteMe()
		self.xl_attr_list = nil
	end

	if self.xl_upcell_list then
		for i,v in ipairs(self.xl_upcell_list) do
			v:DeleteMe()
		end
		self.xl_upcell_list = nil
	end

	if self.xl_eff_obj then
		ResPoolMgr:Release(self.xl_eff_obj)
		self.xl_eff_obj = nil
	end

	self.xl_eff_asset = nil

	if self.xl_role_model then
		self.xl_role_model:DeleteMe()
		self.xl_role_model = nil
	end
end

function LinZhiSkillView:XLShowIndexCallBack()
end

function LinZhiSkillView:XLLoadCallBack()
	self.xl_select_baodi = false

	-- 属性列表
	if not self.xl_attr_list then
		self.xl_attr_list = AsyncListView.New(LinZhiSkillAttrItem, self.node_list["xl_attr_list"])
	end

	-- 修炼道具
	self.xl_upcell_list = {}
	for i=1,2 do
		self.xl_upcell_list[i] = ItemCell.New(self.node_list['xl_upgrade_pos_' .. i])
		-- self.xl_upcell_list[i]:SetIsShowBigBottomText(true)
		self.xl_upcell_list[i]:SetNeedItemGetWay(true)
	end

	--XUI.AddClickEventListener(self.node_list.xl_baojie_check, BindTool.Bind(self.OnClickXlCheck, self))
	XUI.AddClickEventListener(self.node_list.btn_xl, BindTool.Bind(self.OnClickXl, self))
	XUI.AddClickEventListener(self.node_list.btn_xl_lc, BindTool.Bind(self.OnClickXlLC, self))

    self.xl_role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["xl_model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.xl_role_model:SetRenderTexUI3DModel(display_data)
    -- self.xl_role_model:SetUI3DModel(self.node_list["xl_model"].transform, self.node_list["xl_model"].event_trigger_listener,
    -- 1, false, MODEL_CAMERA_TYPE.BASE)
end

-- 打开成就面板
function LinZhiSkillView:OnClickXlLC()
	local lingzhi_type = self.lingzhi_view_type
	LingZhiWGCtrl.Instance:OpenLingZhiAchView({lingzhi_type = lingzhi_type})
end

function LinZhiSkillView:OnClickXl()
	-- local need_baodi = self.xl_select_baodi and 1 or 0

	if self.is_can_xl then
		local lingzhi_type = self.lingzhi_view_type
		LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_XIULIAN_UPLEVEL, lingzhi_type)
	else
		self:OnClickItem(self.xl_stuff_id)
	end
end

function LinZhiSkillView:OnClickXlCheck()
	self.xl_select_baodi = not self.xl_select_baodi
	self.node_list.xl_baojie_yes:SetActive(self.xl_select_baodi == true)
end

function LinZhiSkillView:XLFlushLeft()
	local lingzhi_type = self.lingzhi_view_type
	local name = Language.LingZhi.NameList[lingzhi_type]
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end

	local has_num = server_info.history_lingzhi
	self.node_list.xl_get_lingzhi_txt.text.text = string.format(Language.LingZhi.Tip1, has_num , name)

end

function LinZhiSkillView:XLFlushRight()
	local lingzhi_type = self.lingzhi_view_type
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end

	local grade = server_info.xiulian_grade ~= 0 and server_info.xiulian_grade or 1
	local cfg = LingZhiSkillWGData.Instance:GetXiuLianCfg(lingzhi_type, grade)
	local nex_cfg = LingZhiSkillWGData.Instance:GetXiuLianCfg(lingzhi_type, grade + 1)
	local show_grade = cfg.grade_show or 1
	local color = cfg and cfg.color or 1
	local is_max_level = nex_cfg == nil
	self:FlushXlModel(cfg)

	-- self.node_list.xl_grade_bg.image:LoadSprite(ResPath.GetLingZhiImg("lz_name_bg_" .. cfg.color))
	local is_protect = cfg.is_protect
	local fail_drop_grade_rate = cfg.fail_drop_grade_rate
	if is_protect == 0 and fail_drop_grade_rate > 0 then
		local jie_num = cfg.grade - 1
		local name = cfg.grade_name .. jie_num
		self.node_list.xl_tips_txt.text.text = string.format(Language.LingZhi.XLTipStr2,name)
	else
		self.node_list.xl_tips_txt.text.text = Language.LingZhi.XLTipStr1
	end

	-- 名字刷新
	self.node_list.xl_name.text.text = cfg.type_name or ''--ToColorStr(cfg.type_name or '', ITEM_COLOR[color]) 
	self.node_list.xl_advanced_name.text.text = string.format(Language.LingZhi.Jie, (cfg.grade_name or '') , NumberToChinaNumber(show_grade) .. Language.Common.Jie) 
	color = ITEM_COLOR[color]
	--self.node_list.xl_cur_grade.text.text = ToColorStr(string.format(Language.LingZhi.Jie, (cfg.grade_name or '') , show_grade .. Language.Common.Jie), color) 
	self.node_list.xl_cur_grade.text.text = string.format(Language.LingZhi.Jie, (cfg.grade_name or '') , show_grade .. Language.Common.Jie)
	self.node_list.xl_nex_grade:SetActive(not is_max_level)
	self.node_list["xl_tips"]:SetActive(not is_max_level)
	if not is_max_level then
		color = ITEM_COLOR[nex_cfg.color]
		--self.node_list.xl_nex_grade.text.text = ToColorStr(string.format(Language.LingZhi.Jie, (nex_cfg.grade_name or '') , nex_cfg.grade_show .. Language.Common.Jie), color) 
		self.node_list.xl_nex_grade.text.text = string.format(Language.LingZhi.Jie, (nex_cfg.grade_name or '') , nex_cfg.grade_show .. Language.Common.Jie)
	end

	local history_lingzhi = server_info.history_lingzhi or 0
	self.node_list.btn_xl_lc_qp_txt.text.text = string.format(Language.LingZhi.Tip2, history_lingzhi, Language.LingZhi.NameList[lingzhi_type]) 
	self.node_list["btn_xl_lc_qp"]:SetActive(history_lingzhi > 0)

	-- 属性
	local data_list, cur_cap = LingZhiSkillWGData.Instance:GetXLAttrList(server_info)
	self.xl_attr_list:SetDataList(data_list)
	self.node_list.xl_power_num.text.text = cur_cap

	-- 升级道具
	local stuff_id = cfg.stuff_id or 0
	self.xl_stuff_id = stuff_id
	local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	local need_num = cfg.stuff_count or 0
	color = has_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
	self.is_can_xl = has_num >= need_num
	local remind = has_num >= need_num and not is_max_level
	self.xl_upcell_list[1]:SetFlushCallBack(function ()
		self.xl_upcell_list[1]:SetRightBottomText(ToColorStr(is_max_level and has_num or (has_num .. "/" .. need_num),color))
		self.xl_upcell_list[1]:SetRightBottomTextVisible(true)
	end)
	self.xl_upcell_list[1]:SetData({item_id = stuff_id})

	self.xl_upcell_list[1]:SetVisible(not is_max_level)

	-- 暂时屏蔽 
	-- stuff_id = cfg.protect_stuff_id or 0
	-- has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	-- need_num = cfg.protect_stuff_count or 0
	-- color = has_num >= need_num and COLOR3B.GREEN or COLOR3B.RED
	-- self.xl_upcell_list[2]:SetFlushCallBack(function ()
	-- 	self.xl_upcell_list[2]:SetRightBottomText(ToColorStr(is_max_level and has_num or (has_num .. "/" .. need_num),color))
	-- 	self.xl_upcell_list[2]:SetRightBottomTextVisible(true)
	-- end)
	-- self.xl_upcell_list[2]:SetData({item_id = stuff_id})
	-- self.node_list.xl_baojie_yes:SetActive(self.xl_select_baodi == true)

	-- 成功率
	if is_max_level then
		self.node_list.xl_success_text.text.text = ""
	else
		self.node_list.xl_success_text.text.text = string.format(Language.LingZhi.Tip4, cfg.succ_rate/100) 
	end

	self.node_list.btn_xl:SetActive(not is_max_level)
	self.node_list.xl_max:SetActive(is_max_level)
	self.node_list.btn_xl_remind:SetActive(remind)
end


function LinZhiSkillView:FlushXlModel(feishen_cfg)
	local lingzhi_type = self.lingzhi_view_type
	local asset = feishen_cfg.model
	local bundle = feishen_cfg.model_path
	if lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
		local prof = RoleWGData.Instance:GetRoleProf()
		local asset_list = Split(asset, "|")
		local bundle_list = Split(bundle, "|")
		asset = asset_list[prof]
		bundle = bundle_list[prof]
		
	--	local start_pos = u3dpool.vec3(301, -54, 0)
	--	local end_pos = u3dpool.vec3(301, 6, 0)
	--	local tween_time = 1
	--	UITween.MoveLoop(self.node_list["xl_model_root"], start_pos, end_pos, tween_time)
	else
	--	UITween.KillMoveLoop(self.node_list["xl_model_root"])
	--	local start_pos = u3dpool.vec3(301,-43,0)
	--	self.node_list["xl_model_root"].transform.anchoredPosition = start_pos
	end
	
	if self.xl_eff_asset == asset then
		return
	end

	if self.xl_eff_obj then
		ResPoolMgr:Release(self.xl_eff_obj)
		self.xl_eff_obj = nil
	end

    self.xl_role_model:SetMainAsset(bundle,asset)

	self.xl_eff_asset = asset

end

function LinZhiSkillView:XLFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v["play_effect"] then
				local effect_type = v["play_effect"] == 1 and UIEffectName.s_xiulian or UIEffectName.f_xiulian

				TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
					is_success = v["play_effect"] == 1,pos = Vector2(0, 0), parent_node = self.node_list["layout_lingzhi_xl_eff"]})
			end
		end
	end

	self:XLFlushRight()
	self:XLFlushLeft()

	self.node_list.btn_xl_lc_remind:SetActive(LingZhiSkillWGData.Instance:GetCJRemind(self.lingzhi_view_type))
end

-- 天神属性render
LinZhiSkillAttrItem = LinZhiSkillAttrItem  or BaseClass(BaseRender)

function LinZhiSkillAttrItem:OnFlush()
	local data = self.data
	if not data then
		return 
	end

	self.node_list.name.text.text = data.name or ""

	self.node_list.add:SetActive(data.has_nex == true)
	self.node_list.add_img:SetActive(data.has_nex == true)
	if data.has_nex then
		self.node_list.add.text.text = data.add_value or ''
	end

	self.node_list["limit"]:SetActive(data.limit ~= nil)
	if data.limit then
		self.node_list["limit"].text.text = data.limit
	end

	local start_pos = u3dpool.vec3(269, -4, 0)
	local end_pos = u3dpool.vec3(269, 5, 0)
	local tween_time = 1
	--UITween.MoveLoop(self.node_list.add_img,start_pos,end_pos,tween_time)
end
