YiBenWanLiView = YiBenWanLiView or BaseClass(SafeBaseView)

function YiBenWanLiView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg()

	self:AddViewResource(0, "uis/view/yibenwanli_ui_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.daily_gift, "uis/view/yibenwanli_ui_prefab", "layout_daily_gift_view")
	self:AddViewResource(TabIndex.lianchong_gift, "uis/view/yibenwanli_ui_prefab", "layout_lianchong_gift_view")
	-- self:AddViewResource(TabIndex.yueka, "uis/view/yibenwanli_ui_prefab", "layout_month_card")
	self:AddViewResource(0, "uis/view/yibenwanli_ui_prefab", "layout_zhuangshi_view")

	self.remind_tab = {
		{ RemindName.YiBenWanLi_DailyGift },
		{ RemindName.YiBenWanLi_LianChongGift },
	}
	self.tab_sub = {}
	self.need_check_redPoint = true
	self.default_index = TabIndex.daily_gift
end

function YiBenWanLiView:__delete()

end

function YiBenWanLiView:LoadCallBack()
	if not self.init_view then
		self.init_view = true
		self:InitView()
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end

	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn, self))
	self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function YiBenWanLiView:LoadIndexCallBack(index, loaded_times)
	if index == TabIndex.daily_gift then
		self:InitDailyGift(index, loaded_times)
	elseif index == TabIndex.lianchong_gift then
		self:InitLianChongGift(index, loaded_times)
	end
end

function YiBenWanLiView:InitView()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabVisible, self))
		self.tabbar:Init(Language.YiBneWanLi.TabName, nil, "uis/view/yibenwanli_ui_prefab", nil, self.remind_tab)
	end
end

function YiBenWanLiView:SetTabVisible()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local role_level = RoleWGData.Instance.role_vo.level
	local other_cfg = YiBenWanLiWGData.Instance:GetOtherCfg()
	--每日礼包开服天数限制	连续充值开服天数限制	月卡基金开服天数限制
	--		limit1					limit2					limit3

	self.tabbar:SetVerToggleVisble(TabIndex.daily_gift,
		open_server_day >= other_cfg.limit1 and role_level >= other_cfg.limit4)
	self.tabbar:SetVerToggleVisble(TabIndex.lianchong_gift,
		open_server_day >= other_cfg.limit2 and role_level >= other_cfg.limit5)
	-- self.tabbar:SetVerToggleVisble(TabIndex.yueka, open_server_day >= other_cfg.limit3 and role_level >= other_cfg.limit6)
end

function YiBenWanLiView:OnPassDay()
	if self.tabbar then
		local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local role_level = RoleWGData.Instance.role_vo.level
		local other_cfg = YiBenWanLiWGData.Instance:GetOtherCfg()
		--每日礼包开服天数限制	连续充值开服天数限制	月卡基金开服天数限制
		--		limit1					limit2					limit3
		self.tabbar:SetVerToggleVisble(TabIndex.daily_gift,
			open_server_day >= other_cfg.limit1 and role_level >= other_cfg.limit4)
		self.tabbar:SetVerToggleVisble(TabIndex.lianchong_gift,
			open_server_day >= other_cfg.limit2 and role_level >= other_cfg.limit5)
		-- self.tabbar:SetVerToggleVisble(TabIndex.yueka, open_server_day >= other_cfg.limit3 and role_level >= other_cfg.limit6)
	end
end

-- 切换标签调用
function YiBenWanLiView:ShowIndexCallBack(index)
	if index == TabIndex.daily_gift then
		self:InitDailyGift()
	elseif index == TabIndex.lianchong_gift then
		self:InitLianChongGift()
		-- elseif index == TabIndex.yueka then
		-- 	self:InitMonthCardView()
		-- 	if RechargeWGData.Instance:GetMonthCardFirstDay() then
		-- 		RechargeWGData.Instance:GetMonthCardFirstDay(true)
		-- 		RemindManager.Instance:Fire(RemindName.Vip_Month)--VIP月卡投资
		-- 	end
	end
	self:Flush(index)
	self:PlayOpenViewTween(index)
end

function YiBenWanLiView:ReleaseCallBack()
	if self.pass_day_event then
		GlobalEventSystem:UnBind(self.pass_day_event)
		self.pass_day_event = nil
	end
	self:DeleteDailyGift()
	self:DeleteLianChongGift()
	self:DeleteMonthCard()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
	self.init_view = nil
end

function YiBenWanLiView:OnFlush(param_list, index)
	if 0 == index then return end
	for k, v in pairs(param_list) do
		if k == "all" then
			if self.show_index == TabIndex.daily_gift then
				self:FlushDailyGift()
			elseif self.show_index == TabIndex.lianchong_gift then
				self:FlushLianChongGift()
				-- elseif self.show_index == TabIndex.yueka then
				-- 	self:FlushMonthCard()
			end
		end
	end
end

function YiBenWanLiView:OnClickCloseBtn(...)
	self:Close()
end

function YiBenWanLiView:PlayOpenViewTween(index)
	if TabIndex.daily_gift == index and self.node_list.daily_gift_img then
		UITween.DoUpDownCrashTween(self.node_list.daily_gift_img)
		-- elseif TabIndex.yueka == index and self.node_list.multiple_ten_num and self.node_list.multiple_eight_num then
		-- 	UITween.DoUpDownCrashTween(self.node_list.multiple_ten_num)
		-- 	UITween.DoUpDownCrashTween(self.node_list.multiple_eight_num)
	end
end
