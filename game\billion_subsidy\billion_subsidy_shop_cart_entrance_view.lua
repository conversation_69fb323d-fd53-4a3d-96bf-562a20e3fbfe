-----------------------------------
-- 百亿补贴-购物车入口
-----------------------------------
function BillionSubsidyView:SCLoadIndexCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.shop_cart_item_icon)
    end
    XUI.AddClickEventListener(self.node_list.btn_open_shop_cart, BindTool.Bind(self.OnClickOpenSCBagBtn, self))
end

function BillionSubsidyView:SCReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function BillionSubsidyView:SCCloseCallBack()

end

function BillionSubsidyView:SCShowIndexCallBack()

end

function BillionSubsidyView:SCOnFlush(param_t, index)

end

function BillionSubsidyView:OnClickOpenSCBagBtn()
    BillionSubsidyWGCtrl.Instance:OpenShopCartView()
end

function BillionSubsidyView:PlaySCAddItemAnim(item_id, start_pos, target_pos)
    self.item_cell:SetData({item_id = item_id})

    target_pos = not target_pos and {x = -431, y = 348} or target_pos
    RectTransform.SetAnchoredPositionXY(self.node_list.shop_cart_item_icon.rect, start_pos.x, start_pos.y)
    RectTransform.SetLocalScaleXYZ(self.node_list.shop_cart_item_icon.rect, 1, 1, 1)
    -- local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(item_id)
    -- self.node_list.shop_cart_item_icon.image:LoadSprite(bundle, asset, function ()
    --     self.node_list.shop_cart_item_icon.image:SetNativeSize()
    -- end)

    self.node_list.shop_cart_item_icon:SetActive(true)
    local move_tween_1 = self.node_list.shop_cart_item_icon.rect:DOAnchorPos(Vector2(target_pos.x, target_pos.y), 0.6)
	local scale_tween_1 = self.node_list.shop_cart_item_icon.rect:DOScale(Vector3(0.05, 0.05, 0.05), 0.6)

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

	self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:Append(scale_tween_1)
	self.sequence:Join(move_tween_1)
	self.sequence:OnComplete(function ()
		self.node_list.shop_cart_item_icon:SetActive(false)
	end)
end