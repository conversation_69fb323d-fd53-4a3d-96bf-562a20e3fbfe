------------------------------------------------------------------------
--	F2开服活动
------------------------------------------------------------------------
KfActivityView = KfActivityView or BaseClass(SafeBaseView)

function KfActivityView:__init()
	self:SetMaskBg()
	self.view_style = ViewStyle.Half
	-- self.is_safe_area_adapter = true
	local kf_bundle = "uis/view/open_server_activity_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, kf_bundle, "layout_open_server_bipin")
	--self:AddViewResource(TabIndex.act_sevenday_recharge, kf_bundle, "layout_seven_day_recharge")
	-- self:AddViewResource(TabIndex.act_bipin_purchase, kf_bundle, "layout_op_ser_direct_purchase")
	-- self:AddViewResource(TabIndex.act_daily_recharge, kf_bundle, "layout_op_ser_daily_recharge")
	-- self:AddViewResource(TabIndex.act_equip_cloud_buy, kf_bundle, "layout_op_ser_cloud_buy")
	-- self:AddViewResource(TabIndex.act_equip_lottery, kf_bundle, "layout_op_ser_lottery")

	self:AddViewResource(0, kf_bundle, "layout_op_ser_top")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	--self:AddViewResource(0, kf_bundle, "VerticalTabbar")
end

function KfActivityView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    self.jump_rush_type = nil

	self:BiPinReleaseCallBack()
	self:ReleaseOGAPurchaseCallBack()
	self:ReleaseOGADailyRechargeCallBack()
	self:ReleaseOGACloudBuyCallBack()
	self:ReleaseOGALotteryCallBack()
end

function KfActivityView:CloseCallBack()
	self.is_play_left_btn_anim = nil

	self:CloseOGAPurchaseCallBack()
	self:CloseOGADailyRechargeCallBack()
	self:CloseOGACloudBuyCallBack()
	self:CloseOGALotteryCallBack()
end

function KfActivityView:LoadCallBack()
	self.is_play_left_btn_anim = nil
	self.node_list.title_view_name.text.text = Language.OpenServer.Title

	self:InitMoneyBar()
	self:BiPinLoadCallBack()
	local bundle, assert = ResPath.GetRawImagesPNG("a3_kfcb_bg")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

	--self.node_list.btn_rule_tips:SetActive(true)
	--XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickTipsBtn, self))
end

function KfActivityView:InitMoneyBar()
	self.money_bar = MoneyBar.New()
	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
end

function KfActivityView:PlayMoneyBarEffct(money_type, rect, view_name)
	self.money_bar:PlayEffectByRecharge(money_type, rect, view_name)
end

function KfActivityView:LoadIndexCallBack(index)
	-- if index == TabIndex.act_bipin_purchase then
	-- 	self:LoadOGAPurchaseCallBack()
	-- elseif index == TabIndex.act_daily_recharge then
	-- 	self:LoadOGADailyRechargeCallBack()
	-- elseif index == TabIndex.act_equip_cloud_buy then
	-- 	self:LoadOGACloudBuyCallBack()
	-- elseif index == TabIndex.act_equip_lottery then
	-- 	self:LoadOGALotteryCallBack()
	-- end
end

function KfActivityView:ShowIndexCallBack(index)
	UITween.CleanAllTween(GuideModuleName.KfActivityView)
	if not self.is_play_left_btn_anim then
		self.is_play_left_btn_anim = true
		self:PlayKFAVOpenAnim()
	end

	ServerActivityWGCtrl.Instance:SendOpenGameActivityInfoReq()

	self:ShowBiPinRightPanel(index == TabIndex.act_bipin_normal)
	self:ShowOGATopPanel()

	self:BiPinShowCallBack()
	-- if index == TabIndex.act_bipin_purchase then
	-- 	self:FlushOGAPurchaseCallBack()
	-- elseif index == TabIndex.act_daily_recharge then
	-- 	self:FlushOGADailyRechargeCallBack()
	-- elseif index == TabIndex.act_equip_cloud_buy then
	-- 	self:FlushOGACloudBuyCallBack()
	-- elseif index == TabIndex.act_equip_lottery then
	-- 	self:FlushOGALotteryCallBack()
	-- end
end

function KfActivityView:OnFlush(param_t, index)
	self:BiPinOnFlush(param_t)
	--策划要求，屏蔽每日奖励.
	-- self:FlushOGADailyReward()

	-- if index == TabIndex.act_bipin_purchase then
	-- 	self:FlushOGAPurchaseCallBack()
	-- elseif index == TabIndex.act_daily_recharge then
	-- 	self:FlushOGADailyRechargeCallBack()
	-- elseif index == TabIndex.act_equip_cloud_buy then
	-- 	self:FlushOGACloudBuyCallBack()
	-- elseif index == TabIndex.act_equip_lottery then
	-- 	self:FlushOGALotteryCallBack()
	-- end
end

function KfActivityView:OpenHasRemindTab()
	local index = 0
	for _,v in ipairs(ServerActivityTabCfg) do
		if v.key == "kf_act" then
			if v:show_redpoint_func() == 1 then
				index = v.table_index
				break
			elseif v:can_open_func() and (v.table_index < index or index == 0) then
				index = v.table_index
			end
		end
	end
	if index > 0 then
		self:Open(index)
	end
end

function KfActivityView:OpenByKey(index, param_t)
	if index then
		for _,v in pairs(ServerActivityTabCfg) do
			if v.key == "kf_act" and v.table_index == index and v:can_open_func() then
	            self:Open(index)
        		if index == TabIndex.act_bipin then
        			self:BiPinSelectTopBtn(param_t and param_t.rush_type)
				end
				return
			end
		end
	end

	self:OpenHasRemindTab()
end

function KfActivityView:PlayKFAVOpenAnim()
	if self.node_list["kaifu_title_img"] then
		UITween.DoUpDownCrashTween(self.node_list["kaifu_title_img"])
	end

	local tween_info = UITween_CONSTS.KfActivityView
	UITween.FakeHideShow(self.node_list["VerticalTabbar_Mask"])
	UITween.AlphaShow(GuideModuleName.KfActivityView, self.node_list["VerticalTabbar_Mask"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

-- function KfActivityView:OnClickTipsBtn()
-- 	local rule_tip = RuleTip.Instance
-- 	rule_tip:SetTitle(Language.OpenServer.TipsTitle)
-- 	rule_tip:SetContent(Language.OpenServer.TipsContent, nil, nil, nil, true)
-- end