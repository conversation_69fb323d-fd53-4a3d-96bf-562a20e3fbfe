CheckFuntionUseMem = {}

local total_info = {}
local key_map = {}
local depth = 0
local mem_diff_list = {[0] = 0}
local last_time_stamp = 0
local last_system_mem_count = 0
local start_collect = false

function CheckFuntionUseMem:StartCollectMem()
	start_collect = true
	last_time_stamp = GlobalUnityTime
	collectgarbage("collect")
	last_system_mem_count = collectgarbage("count")
	collectgarbage("stop")
end

function CheckFuntionUseMem:StopCollectMem()
	start_collect = false
	LuaGC:StartSample()
end

function CheckFuntionUseMem:IsCollecting()
	return start_collect
end

function CheckFuntionUseMem:StartCall(class_name, func_name)
	if not start_collect then
		return
	end

	depth = depth + 1
	mem_diff_list[depth] = 0

	if nil == key_map[class_name] then
		key_map[class_name] = {}
	end

	local key = key_map[class_name][func_name]
	if nil == key then
		key = string.format("%s:%s", class_name, func_name)
		key_map[class_name][func_name] = key
	end

	local info = total_info[key]
	if nil == info then
		info = {class_name = key, mem_count = 0, self_mem_count = 0, count = 0, stack = {1, 1, 1, 1}, index = 0}
		total_info[key] = info
	end

	local mem_count = collectgarbage("count")
	info.index = info.index + 1
	info.stack[info.index] = mem_count
end

function CheckFuntionUseMem:EndCall(class_name, func_name)
	if not start_collect then
		return
	end

	if nil == key_map[class_name] then
		return
	end

	local key = key_map[class_name][func_name]
	local info = total_info[key]
	if nil ~= info then
		local old_mem_count = info.stack[info.index]
		info.index = info.index - 1
		info.count = info.count + 1
		local mem_count = collectgarbage("count")
		local diff = mem_count - old_mem_count
		info.mem_count = info.mem_count + diff
		info.self_mem_count = info.self_mem_count + diff - mem_diff_list[depth]

		depth = depth - 1
		mem_diff_list[depth] = mem_diff_list[depth] + diff
	end
end

function CheckFuntionUseMem:Update(now_time, elapse_time)
end

function CheckFuntionUseMem:WriteAllStack()
	local delta_time = GlobalUnityTime - last_time_stamp
	last_time_stamp = GlobalUnityTime

	local system_mem_count = collectgarbage("count")
	local diff_system_mem_count = system_mem_count - last_system_mem_count
	last_system_mem_count = system_mem_count

	local total_mem_count = 0
	local tbl = {}
	for k,v in pairs(total_info) do
		total_mem_count = total_mem_count + v.self_mem_count
		if v.mem_count > 10 then
			table.insert(tbl, v)
		end
	end

	table.sort(tbl, function (a, b)
		return a.mem_count > b.mem_count
	end)

	local tbl2 = {}
	table.insert(tbl2, string.format("TotalLuaMem:%0.1fKB DiffLuaMem:%0.1fKB TotalClassMem:%0.1fKB\n",
		system_mem_count, diff_system_mem_count, total_mem_count))

	table.insert(tbl2, string.format("DeltaTime:%0.1fS AverageMem:%0.1fKB/S AverageMem:%0.1fKB/S\n\n", delta_time,
		diff_system_mem_count / delta_time, total_mem_count / delta_time))

	for k,v in ipairs(tbl) do
		table.insert(tbl2, string.format("[%s] TotalMem:%0.1fKB SelfMem:%0.1fKB Count:%s Average:%0.2fKB Percent:%0.1f%%\n",
			v.class_name, v.mem_count, v.self_mem_count, v.count, v.mem_count / v.count, v.mem_count / total_mem_count * 100))
	end

	local file_path = ResUtil.GetCachePath("LuaFunctionCostMem.txt")
	local file = io.open(file_path, "w")
	local content = table.concat(tbl2)
	file:write(content)
	file:close()

	total_info = {}
end

return CheckFuntionUseMem