RoleWGData = RoleWGData or BaseClass()

function RoleWGData:InitRoleTalentData()
	self:InitTalentSkillCtg()
	self.talent_level_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("role_talent_auto").talent_level_cfg, "talent_id", "talent_level")

	-- RemindManager.Instance:Register(RemindName.SkillView, BindTool.Bind(self.GetSkillMainRemind, self))			-- 红点
	RemindManager.Instance:Register(RemindName.TalentSkill, BindTool.Bind(self.GetSkillTalentRemind, self))

	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
	RemindManager.Instance:Bind(self.remind_change, RemindName.TalentSkill)
	self.old_talent_level_list = nil
end

function RoleWGData:DeleteRoleTalentData()
	--RemindManager.Instance:UnRegister(RemindName.SkillView)
	RemindManager.Instance:UnRegister(RemindName.TalentSkill)

	RemindManager.Instance:UnBind(self.remind_change)
	self.old_talent_level_list = nil
end

function RoleWGData:GetSkillMainRemind()
	if self:GetSkillTalentRemind() == 1 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_SKILL,1,function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.SkillView,TabIndex.skill_talent)
			return true
		end)
		return 1
	end
	
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_SKILL,0)

	if self:GetSkillUplevelRemind() == 1 then
		return 1
	end
	if self:GetSkillUpGradeRemind() == 1 then
		return 1
	end
	return 0
end

function RoleWGData:GetSkillTalentRemind()
	if self.talent_point and self.talent_point > 0 then
		for k,v in pairs(self.talent_skill_cfg) do
			for k1,v1 in pairs(v) do
				if self.talent_level_list[k][k1] ~= v1.max_level then
					local talent_cfg = RoleWGData.Instance:GetRoleTalentSkillCfg(v1.talent_id, self.talent_level_list[k][k1]+1)
					local other_cfg = RoleWGData.Instance:GetRoleTalentSkillResetItem()
					if (RoleWGData.Instance:GetRoleLevel() < other_cfg.open_proficient_talent_level or not TaskWGData.Instance:GetTaskIsCompleted(other_cfg.task_id)) and k == 4 then
						return 0
					end
					if talent_cfg and self.talent_point >= talent_cfg.xiaohao then
						return 1
					end
				end
			end
		end
	end
	return 0
end

function RoleWGData:InitTalentSkillCtg()
	self.talent_skill_cfg = {}
	local role_talent_auto = ConfigManager.Instance:GetAutoConfig("role_talent_auto")
	for k, v in ipairs(role_talent_auto.talent_level_max)do
		if self.talent_skill_cfg[v.talent_type] == nil then
			self.talent_skill_cfg[v.talent_type] = {}
		end
		table.insert(self.talent_skill_cfg[v.talent_type], v)
	end
end

function RoleWGData:GetRoleTalentSkillCfg(talent_id, talent_level)
	return CheckList(self.talent_level_cfg, talent_id, talent_level) or {}
end

function RoleWGData:SetRoleTelentInfo(protocol)
	self.talent_level_list = protocol.talent_level_list
	self.talent_point = protocol.talent_point
	if not self.old_talent_level_list then
		self.old_talent_level_list = __TableCopy(self.talent_level_list)
	end
	if self.talent_callback then
		self.talent_callback()
		self.talent_callback = nil
	end
end

function RoleWGData:GetRoleTalentPoint()
	return self.talent_point or 0
end

function RoleWGData:GetRoleTalentEffect()
	if self.old_talent_level_list and self.talent_level_list then
		for zu = 1, GameEnum.MAX_TELENT_TYPE_COUT do
			for i = 1,GameEnum.MAX_TELENT_INDEX_COUT do
				if self.talent_level_list[zu][i] ~= self.old_talent_level_list[zu][i] then
					self.old_talent_level_list[zu][i] = self.talent_level_list[zu][i]
					return true
				end
			end
		end
	end
	return false
end

function RoleWGData:SetRoleTalentLevelList(typt_id)
	if self.talent_level_list[typt_id] == nil or self.talent_skill_cfg[typt_id] == nil then return end
	local skill_cfg = __TableCopy(self.talent_skill_cfg[typt_id])
	local solt_list = {}
	for k, v in ipairs(skill_cfg)do
		v.level = self.talent_level_list[typt_id][k]
		solt_list[v.solt_index] = v
	end
	return solt_list
end

function RoleWGData:SetRoleTalentLevelByid(talent_type, talent_id)
	local list = self:SetRoleTalentLevelList(talent_type)
	if list then
		for k,v in pairs(list) do
			if v.talent_id == talent_id then
				return v
			end
		end
	end
end

--获取累计投入点数
function RoleWGData:GetActTalentCount(talent_type)
	local list = self:SetRoleTalentLevelList(talent_type)
	local total_count = 0
	if not list then return total_count end
	for k,v in pairs(list) do
		if v.level ~= 0 then
			for i=1,v.level do
				local cfg = self:GetRoleTalentSkillCfg(v.talent_id, i)
				if not IsEmptyTable(cfg) then
					total_count = total_count + cfg.xiaohao
				end
			end
		end
	end
	return total_count
end

function RoleWGData:GetRoleTalentSkillTypeLevel(typt_id)
	if self.talent_level_list[typt_id] == nil then return 0 end
	local all_level = 0
	for k,v in ipairs(self.talent_level_list[typt_id])do
		all_level = all_level + v
	end
	return all_level
end

function RoleWGData:GetRoleTalentAllSkillType()
	local all_level = 0
	for i = 1, 4 do
		local level = self:GetRoleTalentSkillTypeLevel(i)
		all_level = all_level + level
	end
	return all_level
end

function RoleWGData:AddTalentProtcalCallBack(fun)
	self.talent_callback = fun
end

function RoleWGData:GetRoleTalentSkillLevel(talent_id)
	if not self.talent_level_list then return end
	for talent_type, skill_list in ipairs(self.talent_skill_cfg)do
		for k,v in ipairs(skill_list)do
			if v.talent_id == talent_id then
				return self.talent_level_list[talent_type][k]
			end
		end
	end
end

function RoleWGData:GetRoleTalentSkillName(talent_id)
	local role_talent_auto = ConfigManager.Instance:GetAutoConfig("role_talent_auto")
	for k, v in ipairs(role_talent_auto.talent_level_max)do
		if v.talent_id == talent_id then
			return v.name
		end
	end
end

function RoleWGData:GetRoleTalentSkillResetItem()
	return ConfigManager.Instance:GetAutoConfig("role_talent_auto").other[1]
end

--获取全部属性激活的总数量
function RoleWGData:GetAllActTalentCount()
	local count = 0
	for i,v in pairs(self.talent_level_list) do
		for k,n in pairs(v) do
			count = count + n
		end
	end
	return count
end

--获取当前级到N级所需的点数
function RoleWGData:GetNeedTalentCount(talent_id, cur_level, level_num)
	local count = 0

	for i=1,level_num do
		local cfg = self:GetRoleTalentSkillCfg(talent_id, cur_level + 1)
		if cfg then
			count = count + cfg.xiaohao
		end
	end
	return count
end

--获取当前技能是否满足前置条件--判断是否满级
function RoleWGData:GetTalentIsSatisfyPreconditions(data)
	local show_state = 1
	local skill_cfg_1, skill_cfg_2
	local top_1, top_2

	if data.level == 0 then
		show_state = 0
		skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.max_level)
		skill_cfg_2 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.level + 1)
	elseif data.level >= data.max_level then
		show_state = 2
		skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.max_level)
	else
		skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.level)
		skill_cfg_2 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.level + 1)

	end

	if data.level >= data.max_level then return false end
	local show_skill_cfg = show_state ~= 0 and skill_cfg_1 or skill_cfg_2

	local open_condition_1, open_condition_2
	local limit_desc = ""

	if show_skill_cfg.pre_talent_type ~= 0 and show_skill_cfg.pre_talent_type_level ~= 0 then
		local type_all_level = RoleWGData.Instance:GetActTalentCount(show_skill_cfg.pre_talent_type)
		open_condition_2 = type_all_level >= show_skill_cfg.pre_talent_type_level

		local text_color = type_all_level < show_skill_cfg.pre_talent_type_level and "#ff9292" or COLOR3B.DEFAULT_NUM
		limit_desc = Language.RoleTalent.TalentNameListThree[show_skill_cfg.pre_talent_type] .. ToColorStr(type_all_level .. "/" .. show_skill_cfg.pre_talent_type_level,text_color).. "\n"
	else
		open_condition_2 = true
	end

	if show_skill_cfg.pre_talent_id ~= 0 and show_skill_cfg.pre_talent_level ~= 0 then
		local skill_level = RoleWGData.Instance:GetRoleTalentSkillLevel(show_skill_cfg.pre_talent_id) or 0
		local skill_name = RoleWGData.Instance:GetRoleTalentSkillName(show_skill_cfg.pre_talent_id) or ""
		open_condition_1 =  skill_level >= show_skill_cfg.pre_talent_level

		local text_color = skill_level < show_skill_cfg.pre_talent_level and "#ff9292" or COLOR3B.DEFAULT_NUM
		local str = string.format(Language.RoleTalent.TalentLimit, ToColorStr(skill_name, COLOR3B.GLOD), ToColorStr(skill_level .. "/" .. show_skill_cfg.pre_talent_level ,text_color))
		limit_desc = limit_desc .. str
	else
		open_condition_1 = true
	end

	return open_condition_1 and open_condition_2, limit_desc
end

--获取当前技能是否满足前置条件--不判断是否满级
function RoleWGData:GetTalentIsSatisfyPreconditionsTwo(data)
	local show_state = 1
	local skill_cfg_1, skill_cfg_2
	local top_1, top_2

	if data.level == 0 then
		show_state = 0
		skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.max_level)
		skill_cfg_2 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.level + 1)
	elseif data.level >= data.max_level then
		show_state = 2
		skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.max_level)
	else
		skill_cfg_1 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.level)
		skill_cfg_2 = RoleWGData.Instance:GetRoleTalentSkillCfg(data.talent_id, data.level + 1)

	end

	-- if data.level >= data.max_level then return false end
	local show_skill_cfg = show_state ~= 0 and skill_cfg_1 or skill_cfg_2

	local open_condition_1, open_condition_2
	local limit_desc = ""
	if show_skill_cfg.pre_talent_id ~= 0 and show_skill_cfg.pre_talent_level ~= 0 then
		local skill_level = RoleWGData.Instance:GetRoleTalentSkillLevel(show_skill_cfg.pre_talent_id) or 0
		local skill_name = RoleWGData.Instance:GetRoleTalentSkillName(show_skill_cfg.pre_talent_id) or ""
		open_condition_1 =  skill_level >= show_skill_cfg.pre_talent_level

		local text_color = skill_level < show_skill_cfg.pre_talent_level and COLOR3B.PINK or COLOR3B.GREEN
		limit_desc =  skill_name .. '：' .. ToColorStr(skill_level .. "/" .. show_skill_cfg.pre_talent_level ,text_color)
	else
		open_condition_1 = true
	end

	if show_skill_cfg.pre_talent_type ~= 0 and show_skill_cfg.pre_talent_type_level ~= 0 then
		local type_all_level = RoleWGData.Instance:GetActTalentCount(show_skill_cfg.pre_talent_type)
		open_condition_2 = type_all_level >= show_skill_cfg.pre_talent_type_level

		local text_color = type_all_level < show_skill_cfg.pre_talent_type_level and COLOR3B.PINK or COLOR3B.GREEN
		limit_desc = Language.RoleTalent.TalentNameListThree[show_skill_cfg.pre_talent_type] .. ToColorStr(type_all_level .. "/" .. show_skill_cfg.pre_talent_type_level,text_color)
	else
		open_condition_2 = true
	end
	return open_condition_1 and open_condition_2, limit_desc
end

-- 天赋变强
function RoleWGData:RemindChangeCallBack(remind_name, num)
	local temp = num > 0 and 1 or 0
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.ROLE_SKILL, temp, function ()
		FunOpen.Instance:OpenViewByName(GuideModuleName.SkillView,TabIndex.skill_talent)
		return true
	end)
end