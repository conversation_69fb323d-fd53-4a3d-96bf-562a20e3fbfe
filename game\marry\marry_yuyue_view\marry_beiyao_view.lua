MarryBeiYaoView = MarryBeiYaoView or BaseClass(SafeBaseView)

function MarryBeiYaoView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_beyao_view")
end

function MarryBeiYaoView:__delete()

end

function MarryBeiYaoView:ReleaseCallBack()
	--if self.left_head_cell then
	--	self.left_head_cell:DeleteMe()
	--	self.left_head_cell = nil
    --end
    --if self.right_head_cell then
	--	self.right_head_cell:DeleteMe()
	--	self.right_head_cell = nil
    --end
end

function MarryBeiYaoView:CloseCallBack()
	MarryWGData.Instance:DelWeddingInviteAckInfo(true)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.MARRY_BEYAO,0)
end

function MarryBeiYaoView:LoadCallBack()
	self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.OnClickClose, self))

end

function MarryBeiYaoView:OnFlush()
	local auto_yaoqing_list = MarryWGData.Instance:GetWeddingInviteAckInfo()
	if #auto_yaoqing_list == 0 then
		self:Close()
		return
	end
    local data = auto_yaoqing_list[1]
    --if not self.left_head_cell then
    --    self.left_head_cell = BaseHeadCell.New(self.node_list["left_head_cell"])
    --end
    --if not self.right_head_cell then
    --    self.right_head_cell = BaseHeadCell.New(self.node_list["right_head_cell"])
    --end
    local data1 = {}
    data1.role_id = data.invite_role_id
    data1.prof = data.invite_prof
    data1.sex = data.invite_sex

    local data2 = {}
    data2.role_id = data.lover_role_id
    data2.prof = data.lover_prof
    data2.sex = data.lover_sex
    if data.invite_sex == 1 then
        --self.left_head_cell:SetData(data1)
        --self.left_head_cell:SetBgActive(true)
        --self.right_head_cell:SetData(data2)
        --self.right_head_cell:SetBgActive(true)
		self.node_list["role_name_1"].text.text = data.invite_name
		self.node_list["role_name_2"].text.text = data.lover_name
	else
        --self.left_head_cell:SetData(data2)
        --self.left_head_cell:SetBgActive(true)
        --self.right_head_cell:SetData(data1)
        --self.right_head_cell:SetBgActive(true)
		self.node_list["role_name_2"].text.text = data.invite_name
		self.node_list["role_name_1"].text.text = data.lover_name
	end

	local time = MarryWGData.Instance:GetShowYuYueTime(data.seq)
	self.node_list["desc_text"].text.text = string.format(Language.Marry.BeYaoHint,data.invite_name,time)
end

function MarryBeiYaoView:OnClickClose()
	 MarryWGData.Instance:DelWeddingInviteAckInfo(false)
	 self:Flush()
end

