PlaySkillNameWordView = PlaySkillNameWordView or BaseClass(SafeBaseView)

local TWEEN_STATUS = {
    NONE = 0,
    PLAYING = 1,
}

function PlaySkillNameWordView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)

    self.view_layer = UiLayer.SkillFloatWord
    self.view_cache_time = 0
    
    self:AddViewResource(0, "uis/view/play_skill_float_word_prefab", "layout_skill_name_float_word")
end

function PlaySkillNameWordView:__delete()
end

function PlaySkillNameWordView:OpenCallBack()

end

function PlaySkillNameWordView:CloseCallBack()
    self.curr_tween_status = nil
end

function PlaySkillNameWordView:LoadCallBack()
end

function PlaySkillNameWordView:TweenPlayFinish()
    -- 这里做一秒延时方便SetActive，在一帧内操作问题
    self.node_list.tween_skill_bg:CustomSetActive(false)

    self:CleanDelayTimer()
    self.delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.float_tween_status = TWEEN_STATUS.NONE
        self:Flush()
	end, 0.1)
end

function PlaySkillNameWordView:ReleaseCallBack()
    self.skill_name_info_list = nil
    self.skill_name_tween_tween = nil
    self:CleanDelayTimer()
    self:CleanDelayTimerList()
    self:CancelTween()
end

function PlaySkillNameWordView:SetDataAndOpen(info)
    if self.skill_name_info_list == nil then
		self.skill_name_info_list = {}
	end

    if self:CheckIsInCd(info.skill_id, info.cd_s) then   -- 去除短时间重复的
        return
    end

	table.insert(self.skill_name_info_list, info)
    if not self:IsOpen() then
        self:Open()
    else
        if self.float_tween_status == TWEEN_STATUS.NONE then
            self:Flush()
        end
    end
end

function PlaySkillNameWordView:OnFlush()
    if IsEmptyTable(self.skill_name_info_list) then
		return
	end

    self:SetPanelInfo()
end

function PlaySkillNameWordView:SetPanelInfo()
    local info = table.remove(self.skill_name_info_list, 1)
    self.float_tween_status = TWEEN_STATUS.PLAYING
    self.node_list.tween_skill_bg:CustomSetActive(true)
    self.node_list.skill_name.text.text = info.skill_name

	local from_scale = Vector3(2, 2, 1)
    local to_scale = Vector3(0.8, 0.8, 1)

    self:CancelTween()
    local show_tweener = DG.Tweening.DOTween.Sequence()
	local panel = self.node_list.skill_name_tween_root
	panel.transform.localScale = from_scale
    self.node_list.tween_skill_bg.transform.localScale = from_scale

    show_tweener:Append(panel.canvas_group:DoAlpha(1, 1, 0.75))
	show_tweener:Join(panel.rect:DOScale(to_scale, 0.25))
    show_tweener:Join(self.node_list.tween_skill_bg.rect:DOScale(to_scale, 0.25))
	show_tweener:SetEase(DG.Tweening.Ease.Linear)
	show_tweener:OnComplete(
	function()
		self:TweenPlayFinish()
	end)

    self.show_tweener = show_tweener
end

function PlaySkillNameWordView:CancelTween()
    if self.show_tweener then
        self.show_tweener:Kill()
        self.show_tweener = nil
    end
end

function PlaySkillNameWordView:CleanDelayTimer()
	if self.delay_timer then
		GlobalTimerQuest:CancelQuest(self.delay_timer)
		self.delay_timer = nil
	end
end

function PlaySkillNameWordView:CleanDelayTimerList()
	if self.delay_timer_list and #self.delay_timer_list > 0 then
        for _, value in pairs(self.delay_timer_list) do
            GlobalTimerQuest:CancelQuest(value)
        end
	end

    self.delay_timer_list = nil
end

function PlaySkillNameWordView:CheckIsInCd(skill_id, cd_s)
    if (not skill_id) or (not cd_s) then
        return true
    end

    local cd = (cd_s / 1000) - 1    -- 计算倒计时，误差秒
    if not self.delay_timer_list then
        self.delay_timer_list = {}
    end

    if self.delay_timer_list[skill_id] ~= nil then
        return true
    else
        self.delay_timer_list[skill_id] = GlobalTimerQuest:AddDelayTimer(function ()
            if self.delay_timer_list and self.delay_timer_list[skill_id] then
                self.delay_timer_list[skill_id] = nil
            end
        end, cd)

        return false
    end
end
