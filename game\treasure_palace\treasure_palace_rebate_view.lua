function TreasurePalaceView:ZBKRebateLoadIndexCallBack()
    
end

function TreasurePalaceView:ZBKRebateReleaseCallBack()
    
end

function TreasurePalaceView:ZBKRebateShowIndexCallBack()
    
end

function TreasurePalaceView:ZBKRebateOnFlush()
    local real_rechar_rmb = RechargeWGData.Instance:GetRealChongZhiRmb()
    self.node_list.desc_rebate_value.text.text = string.format(Language.TreasurePalace.DescRebate, real_rechar_rmb * 20 )
end