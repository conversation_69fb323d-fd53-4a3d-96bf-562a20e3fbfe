ZhuZaiRewardView = ZhuZaiRewardView or BaseClass(SafeBaseView)

function ZhuZaiRewardView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "ZhuZaiRewardView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(30, 12), sizeDelta = Vector2(922, 586)})
	self:AddViewResource(0, "uis/view/zhuzaishendian_ui_prefab", "layout_reward")
end

function ZhuZaiRewardView:__delete()

end

function ZhuZaiRewardView:ReleaseCallBack()
	if nil ~= self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
	if nil ~= self.zjreward_cells then
		self.zjreward_cells:DeleteMe()
		self.zjreward_cells = nil
	end
	if nil ~= self.reward_list_view_1 then
		self.reward_list_view_1:DeleteMe()
		self.reward_list_view_1 = nil
	end

	-- if nil ~= self.apply_list then
	-- 	self.apply_list:DeleteMe()
	-- 	self.apply_list = nil
	-- end

	if self.person_reward_list then
		for k,v in pairs(self.person_reward_list) do
			v:DeleteMe()
		end
		self.person_reward_list = nil
	end

	if self.fenpei_cell_list then
    	for k,v in pairs(self.fenpei_cell_list) do
    		v:DeleteMe()
    	end
    	self.fenpei_cell_list = {}
    end
    self.cur_select_cell = nil
end

function ZhuZaiRewardView:LoadCallBack()
	self:SetSecondView()
	self.node_list.btn_close_window:SetActive(false)
	self.node_list.title_view_name.text.text = Language.ZhuZaiShenDian.ViewNameLianSheng

	for i=1,3 do
		XUI.AddClickEventListener(self.node_list["tag_btn_"..i], BindTool.Bind(self.OnClickPanelTypeBtn, self,i))
		self.node_list["tag_btn_image_"..i]:SetActive(false)
		self.node_list["group_panel_"..i]:SetActive(false)
	end

	self.node_list.tag_btn_4:SetActive(false)

	self:InitZhuZaiRewardView()
	self:InitPersonRewardList()

	if self.panel_index then
		self:OnClickPanelTypeBtn(self.panel_index)
	else
		self:OnClickPanelTypeBtn(1)
	end

	ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRewardReq(GUILD_BATTLE_OP_TYPE.RANK_FIGHT_RESULT)

	GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_GUILD_RANK)
	GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_REQ_KEEP_INFO)

end

function ZhuZaiRewardView:SetIndex( index )
	self.panel_index = index
end

function ZhuZaiRewardView:ShowIndexCallBack()
	self:OnClickPanelTypeBtn(self.panel_index)
end

function ZhuZaiRewardView:OnClickPanelTypeBtn(index)
	for i=1,3 do
		self.node_list["tag_btn_image_"..i]:SetActive(index == i)
		self.node_list["group_panel_"..i]:SetActive(index == i)
	end
	self.panel_index = index
	self:Flush()
end

function ZhuZaiRewardView:InitZhuZaiRewardView()
	self.reward_list_view = AsyncListView.New(ReWardItemRender, self.node_list["ph_item_list"])
	self.reward_list_view_1 = AsyncListView.New(ReWardGuildItemRender, self.node_list["ph_guild_rank_list"])
	-- self.reward_list_view_2 = AsyncListView.New(ReWardPersonItemRender, self.node_list["ph_guild_person_list"])
	--self.node_list.guize_btn.button:AddClickListener(BindTool.Bind(self.TipsWar, self))
	self.node_list.btn_activity_states.button:AddClickListener(BindTool.Bind(self.TipsWarAct, self))

	--分配奖励
    --XUI.AddClickEventListener(self.node_list["tips_btn"],BindTool.Bind(self.ClickTipsBtn,self))
    --XUI.AddClickEventListener(self.node_list["close_apply_panel"],BindTool.Bind(self.CloseApplyPanel,self))
    self.fenpei_cell_list = {}
	self.fenpei_data_list = GuildBattleRankedWGData.Instance:GetFenPeiItemInfo()
    --local list_delegate = self.node_list["fenpei_list_view"].list_simple_delegate
	--list_delegate.NumberOfCellsDel = BindTool.Bind(self.GeFenPeitListViewNumbers, self)
	--list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshFenPeiListViewCells, self)

	--self.apply_list = AsyncListView.New(FeiPeiNameItem, self.node_list["apply_list"])
    --self.apply_list:SetSelectCallBack(BindTool.Bind(self.OnSelectName, self))
    --self.node_list["apply_panel"]:SetActive(false)
end

function ZhuZaiRewardView:InitPersonRewardList()
	local reward_list = {}
	local reward_content = self.node_list.person_reward_content
	local reward_list_root = {}

	for i=1,4 do
		local data_list = GuildBattleRankedWGData.Instance:GetBaseRewardCfg(math.ceil(i / 2) - 1)
		local str = "reward_list"..math.ceil(i/2)
		reward_list_root[i] = reward_content.transform:Find(str.."/reward_list_" .. i)
		local reward_item = ReWardPersonItemRender.New(reward_content:FindObj(str.."/reward_item_" .. i))
		reward_item:SetWinMark(i % 2 > 0)
		reward_item:SetData(data_list[1])
		reward_list[i] = reward_item
	end
	self.person_reward_list = reward_list

	local res_async_loader = AllocResAsyncLoader(self, "ph_guild_person_render")
	res_async_loader:Load("uis/view/zhuzaishendian_ui_prefab", "ph_guild_person_render", nil,
		function(new_obj)
			local data_list = nil
			local is_win = false
			local reward_list = self.person_reward_list
			for i=1,#reward_list_root do
				is_win = i % 2 > 0
				data_list = GuildBattleRankedWGData.Instance:GetBaseRewardCfg(math.ceil(i / 2) - 1)
				for j=2,#data_list do
					local obj = ResMgr:Instantiate(new_obj)
					obj.transform:SetParent(reward_list_root[i], false)
					obj.transform:SetSiblingIndex(j-2)
					local reward_item = ReWardPersonItemRender.New(obj)
					reward_item:SetWinMark(is_win)
					reward_item:SetData(data_list[j])
					reward_list[#reward_list + 1] = reward_item
				end
			end
			self.person_reward_list = reward_list
		end)
end

function ZhuZaiRewardView:GeFenPeitListViewNumbers()
	return #self.fenpei_data_list
end

function ZhuZaiRewardView:RefreshFenPeiListViewCells(cell, cell_index)
	local item_cell = self.fenpei_cell_list[cell]
	cell_index = cell_index + 1
	if not item_cell then
		item_cell = WarFenPeiItem.New(cell.gameObject)
		self.fenpei_cell_list[cell] = item_cell
		item_cell:SetClickSelectApply(BindTool.Bind(self.ItemClickSelectApply,self))
	end
	local item_data = self.fenpei_data_list[cell_index]
	item_cell:SetIndex(cell_index)
	item_cell:SetData(item_data)
end

function ZhuZaiRewardView:OnNorBossTips()

end

function ZhuZaiRewardView:OnClickExamine()

end

function ZhuZaiRewardView:GoToJoinAct()

end

function ZhuZaiRewardView:OnFlush(param_t, index)
	if nil == self.panel_index or self.panel_index<= 0 then return end
	if self.panel_index == 1 then
		local data_list_origin = GuildWGData.Instance:GetKFXianmengzhanGuildRank()
		local data_list = {}
		local num_rank = 0

		self.reward_list_view_1:SetDataList(data_list_origin)

		local own_guild_id = RoleWGData.Instance:GetRoleVo().guild_id
		local own_plat_type = RoleWGData.Instance.role_vo.plat_type
		for k,v in pairs(data_list_origin) do
			if own_guild_id == v.guild_id and own_plat_type == v.plat_type then
				num_rank = k
				break
			end
		end

		local guild_fight_state = GuildWGData.Instance:GetCurGuildWarState()
	    local guild_battle_limit_level,limit_day = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()

	    local show_state = (guild_fight_state == GuildWGData.GuildWarState.None
	    					or guild_fight_state == GuildWGData.GuildWarState.EndState)

		if num_rank > 0 and show_state then
			local str =  CommonDataManager.GetDaXie(num_rank)
			self.node_list.right_down_guild_desc_1.text.text = string.format(Language.GuildBattleRanked.BattleNotRank_1, str)
			--print_error(num_rank,str,self.node_list.right_down_guild_desc_1.text,text)
		else
			self.node_list.right_down_guild_desc_1.text.text = Language.GuildBattleRanked.BattleNotRank
		end

	elseif self.panel_index == 2 then
		-- local data = GuildBattleRankedWGData.Instance:GetBaseRewardCfg()
		-- self.reward_list_view_2:SetDataList(data,2)
	elseif self.panel_index == 3 then
		if self.reward_list_view then
			local data_list = ZhuZaiShenDianWGData.Instance:GetBaseLianShengCfg()--ZhuZaiShenDianWGData.Instance:GetTotalReWard()
			self.reward_list_view:SetDataList(data_list, 2)
		end
	elseif self.panel_index == 4 then
		self:InitZhuZaiEndRewardView()
	elseif self.panel_index == 5 then
		self:FlushFenPeiReward()
	end

		--分配按钮的显示
	local fenpei_remind	= GuildBattleRankedWGData.Instance:GetFenPeiRemind()
	self.node_list["fenpei_remind"]:SetActive(fenpei_remind == 1)
end

function ZhuZaiRewardView:TipsWar()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.ZhuZaiShenDian.BattleTips)
		role_tip:SetContent(Language.ZhuZaiShenDian.BattleTipContent, nil, nil, nil, true)
	else
		print_error("AppearanceView","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end

function ZhuZaiRewardView:TipsWarAct()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.ZhuZaiShenDian.BattleTipsAct)
		role_tip:SetContent(Language.ZhuZaiShenDian.BattleTipActContent, nil, nil, nil, true)
	else
		print_error("AppearanceView","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end

function ZhuZaiRewardView:OnClickCloseBtnHandler()
	self:Close()
end

function ZhuZaiRewardView:InitZhuZaiEndRewardView()
	if nil == self.zjreward_cells  then
		self.zjreward_cells = ItemCell.New()
		--self.zjreward_cells:SetInstanceParent(self.node_list["ph_zjreward"])
	end
	--self.node_list.img_sign:SetActive(false)
	local  guildbattle_result = ZhuZaiShenDianWGData.Instance:GetGuildBattleWinFirstRankFightResult()
	local WinStreak = GuildWGData.Instance:GetLianShen()
	
	--self.node_list.reward_img:SetActive(true)	
	--self.node_list.ph_zjreward:SetActive(true)	
	--self.node_list.buff_txt:SetActive(true)	
	--self.node_list.lbl_zhongjie_buff:SetActive(true)

	-- if not IsEmptyTable(guildbattle_result) and guildbattle_result.loser_keep_win_times > 1 and guildbattle_result.is_keep_win == 0 then  --终结
	-- 	--self.node_list.win_img:SetActive(false)
	-- 	--self.node_list.pass_img:SetActive(true)
	-- 	--self.node_list.win_count.text.text =Language.ZhuZaiShenDian.Zhong..CommonDataManager.GetDaXie(guildbattle_result.loser_keep_win_times)..Language.ZhuZaiShenDian.Lian
	-- 	--self.node_list.xianmeng_1.text.text = guildbattle_result.winner_guild_name
	-- 	--self.node_list.xianmeng_2.text.text = guildbattle_result.loser_guild_name
	-- else                                                                       --连胜
	-- 	--self.node_list.win_img:SetActive(true)
	-- 	--self.node_list.pass_img:SetActive(false)
	-- 	--self.node_list.win_count.text.text = CommonDataManager.GetDaXie(WinStreak)..Language.ZhuZaiShenDian.Lian
	-- 	local xmz_data = ZhuZaiShenDianWGData.Instance:GetGuildId()
	-- 	--self.node_list.xianmeng_1.text.text = xmz_data[1].guild_name
	-- 	--self.node_list.xianmeng_2.text.text = xmz_data[2].guild_name
	-- end

	local zjreward_1 = GuildBattleRankedWGData.Instance:GetGuildRoleOffsetCfgPanel(WinStreak)
	if zjreward_1 then
		self.zjreward_cells:SetData(zjreward_1.defeat_reward_item[0])
	end

	local buff0 = ""
	local buff1 = ""
	local buff2 = ""
	if WinStreak > 1 then
		local zjreward = ZhuZaiShenDianWGData.Instance:GetZhongJiecfg(WinStreak)
		if  zjreward.challenger_add_maxhp_per then
			buff0 = Language.ZhuZaiShenDian.Buff[0]..(zjreward.challenger_add_gongji_per / 100) ..Language.ZhuZaiShenDian.Fuhao
			buff1 = Language.ZhuZaiShenDian.Buff[1]..(zjreward.challenger_add_fangyu_per / 100) ..Language.ZhuZaiShenDian.Fuhao
			buff2 = Language.ZhuZaiShenDian.Buff[2]..(zjreward.challenger_add_maxhp_per / 100) ..Language.ZhuZaiShenDian.Fuhao
			--self.node_list.lbl_zhongjie_buff.text.text = buff0.."\n"..buff1.."\n"..buff2
		end
	else
		--self.node_list.lbl_zhongjie_buff.text.text = Language.ZhuZaiShenDian.NoBufff
	end

	--self.node_list["zhongjie_rule1"].text.text = Language.ZhuZaiShenDian.BattleTipContent
	--self.node_list["zhongjie_rule2"].text.text = Language.ZhuZaiShenDian.BattleTipContent2
end

--奖励分配
function ZhuZaiRewardView:FlushFenPeiReward()
	self.fenpei_data_list = GuildBattleRankedWGData.Instance:GetFenPeiItemInfo()
    local max_apply_num = GUILD_BATTLE_FENPEI_REWARD_LIMIT_NUM
    local get_num = GuildBattleRankedWGData.Instance:GetFenPeiGetNum() 
    local count_num = max_apply_num - get_num
	local color = get_num < max_apply_num and COLOR3B.GREEN or COLOR3B.RED
    --self.node_list["max_text"].text.text = string.format(Language.Guild.FenPeiReawrdMax,color,count_num,max_apply_num)
    --self.node_list["fenpei_list_view"].scroller:RefreshAndReloadActiveCellViews(true)
	--self.node_list["fenpei_end_time"].text.text = Language.Guild.FeiPenEndTimeStr
end

function ZhuZaiRewardView:ClickTipsBtn()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.FenPeiRuleTitle)
		role_tip:SetContent(Language.Guild.FenPeiRuleContent, nil, nil, nil, true)
	end
end

local RectTransUtility = UnityEngine.RectTransformUtility
function ZhuZaiRewardView:ItemClickSelectApply(cell)
	if cell then
		local name_data = GuildBattleRankedWGData.Instance:GetApplyRoleList(cell.data.item_id)
		if IsEmptyTable(name_data) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FenPeiNotApply)
			return
		end
		self.cur_select_cell = cell
		local perfab = cell:GetApplyPanel()
		if perfab then
			local screen_pos_tbl = RectTransUtility.WorldToScreenPoint(UICamera, perfab.rect.position)
			--local rect = self.node_list["list_view_panel"].rect
			--local _, local_position_tbl = RectTransUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))
			--local pos_y = local_position_tbl.y
			--local pos_x = self.node_list["apply_panel"].rect.anchoredPosition.x
			--self.node_list["apply_panel"].rect.anchoredPosition = Vector2(pos_x,pos_y)
			--self.node_list["apply_panel"]:SetActive(true)
		end
		--self.apply_list:SetDataList(name_data)
	end
end

function ZhuZaiRewardView:OnSelectName(item, cell_index, is_default, is_click)
	if is_default then return end
	local data = item:GetData()
	if not data then return end
	if self.cur_select_cell then
		self.cur_select_cell:SetSelectName(data.role_name)
		self.cur_select_cell:SetSelectApplyRoleId(data.role_id)
	end
	self:CloseApplyPanel()
end

function ZhuZaiRewardView:CloseApplyPanel()
	--self.node_list["apply_panel"]:SetActive(false)
	if self.cur_select_cell then
		self.cur_select_cell:CloseApplyPanel()
	end
end

-------------------------------连胜奖励Render--------------------------------------------------------
ReWardItemRender = ReWardItemRender or BaseClass(BaseRender)
function ReWardItemRender:__init()
	self.reward_list_view = AsyncListView.New(ItemCell,self.node_list.reward_list)
end

function ReWardItemRender:__delete()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
	self.is_active = nil
end

function ReWardItemRender:CreateChild()
	-- BaseRender.CreateChild(self)
end

function ReWardItemRender:OnFlush()
	if not self.data then return end

	local data = {}
	for i=0,#self.data.win_reward_item do
		table.insert(data,self.data.win_reward_item[i])
	end
	self.reward_list_view:SetDataList(data)

	local win_streak = GuildWGData.Instance:GetLianShen()
	self.node_list.lbl_lscs.text.text = win_streak.."/"..self.data.win_count
	local num_str = NumberToChinaNumber(self.data.win_count)

	self.node_list.liansheng_img.text.text = num_str .. Language.Guild.GuildWinningStreak
	self.node_list.yiidacheng:SetActive(win_streak >= self.data.win_count)
	self.node_list.weidacheng:SetActive(win_streak < self.data.win_count)

end

function ReWardItemRender:OnBtnTips()

end

function ReWardItemRender:GetIsActive()
	local temp_data = {}
	if self.data and #self.data > 1 then
		for k,v in pairs(self.data) do
			table.insert(temp_data,k - 1,v[0])
		end
	else
		temp_data = self.data
	end

	self.is_active = ZhuZaiShenDianWGData.Instance:GetIsActive(temp_data)
end

function ReWardItemRender:OpenFenPEI()
	local guild_post = GameVoManager.Instance:GetMainRoleVo().guild_post
	if guild_post ==  GUILD_POST.TUANGZHANG then
		local temp_data = {}
		if self.data and #self.data > 1 then
			for k,v in pairs(self.data) do
				table.insert(temp_data,k - 1,v[0])
			end
		else
			temp_data = self.data
		end
		-- ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiFenPei()
		ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleKeepWinRewardAllocateRecord()
		ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiShenDianFenPeiView(temp_data)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZhuZaiShenDian.FenPeiLimit)
	end
end

function ReWardItemRender:JoinFuBenHandler()

end

function ReWardItemRender:CreateSelectEffect()
end


----------------------------------------仙盟排行----------------------------------------------------------
ReWardGuildItemRender = ReWardGuildItemRender or BaseClass(BaseRender)

function ReWardGuildItemRender:__init()

end

function ReWardGuildItemRender:LoadCallBack()
	self.reward_list_view = AsyncListView.New(RewardPreviousCell,self.node_list.ph_reward_rank_list)
end

function ReWardGuildItemRender:__delete()
	if self.reward_list_view then
		self.reward_list_view:DeleteMe()
		self.reward_list_view = nil
	end
end

function ReWardGuildItemRender:OnFlush()
	if nil == self.data then return end

	local bg_bundle, bg_asset = nil, nil

	if self.index <= 3 then
		bg_bundle, bg_asset = ResPath.GetCommonImages("a2_xm_di_dj" .. self.index)
		local bundle, asset = ResPath.GetCommonIcon("a2_pm_"..self.index)
        self.node_list["rank_num_image"].image:LoadSprite(bundle, asset)
		self.node_list.top_text.text.text = self.index
		self.node_list.img9_reward_bg:SetActive(true)
	else
		bg_bundle, bg_asset = ResPath.GetCommonImages("a2_ty_xxd_5")
		self.node_list.img9_reward_bg:SetActive(self.index % 2 ~= 0)
	end

	self.node_list.img9_reward_bg.image:LoadSprite(bg_bundle, bg_asset)
    self.node_list["rank_num_image"]:SetActive(self.index <= 3)
	self.node_list.rank_text:SetActive(self.index > 3)
    self.node_list.rank_text.text.text = self.index

    local data_list =  GuildWGData.Instance:GetGuildItemRewardByRankNum(self.index)
    if data_list then
    	local temp_data = {}
    	local reward_id = data_list.reward_id
    	local id_list = Split(reward_id,"|")
    	for k,v in pairs(id_list) do
    		local cfg = GuildBattleRankedWGData.Instance:GetGuildRankRewardId(tonumber(v))
    		if cfg then
    			for i=0,#cfg.reward_item do
		    		table.insert(temp_data,cfg.reward_item[i])
		    	end
    		end
    	end
    	self.reward_list_view:SetDataList(temp_data)
    end

    local guild_fight_state = GuildWGData.Instance:GetCurGuildWarState()
    local guild_battle_limit_level,limit_day = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()

    local show_state = (guild_fight_state == GuildWGData.GuildWarState.None
    					or guild_fight_state == GuildWGData.GuildWarState.EndState)

	local role_name_color = self.index <= 3 and COLOR3B.WHITE or COLOR3B.GRAY
    if self.data.guild_id > 0 and show_state then
    	local server_str = string.format(Language.Common.ServerIdFormat,self.data.server_id)
		local complete_guild_name = server_str .. self.data.guild_name
    	self.node_list.guild_name.text.text = ToColorStr(complete_guild_name, role_name_color)
    else
    	self.node_list.guild_name.text.text =  ToColorStr(Language.Guild.Waitting, role_name_color)
    end
	-- TODO 需要改为新版处理方式
	-- self.node_list.guild_name.shadow.enabled = self.index <= 3
end


---------------------------------------------------------------------------------------------------
ReWardPersonItemRender = ReWardPersonItemRender or BaseClass(BaseRender)

function ReWardPersonItemRender:__init()
	self.is_win = false
	self.reward_item_list = nil
end

function ReWardPersonItemRender:__delete()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
end

function ReWardPersonItemRender:OnFlush()
	local data = self:GetData()
	-- local max_cfg = GuildBattleRankedWGData.Instance:GetMaxBaseRewardCfg(data.zone)
	if data.min_rank == data.max_rank then
		self.node_list.rank_label.text.text = data.min_rank
	else
		-- if data.max_rank == max_cfg.max_rank then
		-- 	self.node_list.rank_label.text.text = data.min_rank .. "+"
		-- else
		-- 	self.node_list.rank_label.text.text = data.min_rank .. "-" .. data.max_rank
		-- end
		self.node_list.rank_label.text.text = data.min_rank .. "-" .. data.max_rank
	end

	self:SetRewardList(self.is_win and data.show_reward_item or data.show_failure_reward_item)
end

function ReWardPersonItemRender:SetRewardList(reward_list)
	if not reward_list or self.reward_item_list then
		return
	end
	local reward_data = SortDataByItemColor(reward_list)
	local reward_item_list = {}
	local cell_root = self.node_list.reward_list_root
	for i=1,#reward_data do
		local item_cell = ItemCell.New(cell_root)
		item_cell:SetData(reward_data[i])
		reward_item_list[i] = item_cell
	end
	self.reward_item_list = reward_item_list
end

function ReWardPersonItemRender:SetWinMark(_bool)
	self.is_win = _bool
end
--[[
ReWardPersonItemRender = ReWardPersonItemRender or BaseClass(BaseRender)

function ReWardPersonItemRender:__delete()
	if self.reward_list_view_1 then
		self.reward_list_view_1:DeleteMe()
		self.reward_list_view_1 = nil
	end
end

function ReWardPersonItemRender:LoadCallBack()
	self.reward_list_view_1 = AsyncListView.New(RewardPreviousCell,self.node_list.ph_reward_rank_list_1)
	self.reward_list_view_2 = AsyncListView.New(RewardPreviousCell,self.node_list.ph_reward_rank_list_2)
end

function ReWardPersonItemRender:OnFlush()
	if nil == self.data then return end
	self.node_list.img9_reward_bg:SetActive(self.index%2 ~= 0)
	--if self.index <= 3 then
		for i=1,5 do
			self.node_list["rank_num_image_"..i]:SetActive(self.index == i)
		end
	--end
	local data_list_1 = {}
	for i=0,#self.data.show_reward_item do
		table.insert(data_list_1,self.data.show_reward_item[i] )
	end
	local data_list_2 = {}
	for i=0,#self.data.show_failure_reward_item do
		table.insert(data_list_2,self.data.show_failure_reward_item[i] )
	end
	self.reward_list_view_1:SetDataList(data_list_1,0)
	self.reward_list_view_2:SetDataList(data_list_2,0)
end
--]]