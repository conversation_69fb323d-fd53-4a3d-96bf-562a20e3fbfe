XianJieBossSceneLogic = Xian<PERSON>ieBossSceneLogic or BaseClass(CrossServerSceneLogic)

function Xian<PERSON>ieBossSceneLogic:__init()

end

function XianJieBossSceneLogic:__delete()
   
end

-- 进入场景
function Xian<PERSON>ieBossSceneLogic:Enter(old_scene_type, new_scene_type)
    CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
    -- MainuiWGCtrl.Instance:SetTaskButtonTrue()
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(false)
	BossWGCtrl.Instance:Close()
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
    MainuiWGCtrl.Instance:ShowMainuiMenu(false)
    MainuiWGCtrl.Instance:SetFbIconEndCountDown(-1)
    BossWGCtrl.Instance:EnterSceneCallback()
    BossWGCtrl.Instance:EnterXianJieSceneCallBack()
    MainuiWGCtrl.Instance:ResetLightBoss()
    BaseFbLogic.SetLeaveFbTip(true)
    FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
    self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDead, self))
    self.select_monster_event = GlobalEventSystem:Bind(ObjectEventType.ON_CLICKED, BindTool.Bind(self.OnSelectObjCallBack, self))
    self.change_attack_mode_event = GlobalEventSystem:Bind(SettingEventType.Change_Attack_Mode, BindTool.Bind1(self.OnChangeAttackMode, self))

    local tired_view = BossWGCtrl.Instance:GetXianJieTiredView()
	ViewManager.Instance:AddMainUIRightTopChangeList(tired_view)
	ViewManager.Instance:AddMainUIFuPingChangeList(tired_view)

	local equip_view = BossWGCtrl.Instance:GetXianJieEquipView()
	ViewManager.Instance:AddMainUIRightTopChangeList(equip_view)
	ViewManager.Instance:AddMainUIFuPingChangeList(equip_view)

	BossPrivilegeWGCtrl.Instance:EnterBossPrivilegeSceneCallBack()
end

function XianJieBossSceneLogic:OnObjDead(obj)
    if not obj then
		return
    end
    local enemy_info = XianJieBossWGData.Instance:GetCurEnemyInfo()
    if IsEmptyTable(enemy_info) then
        return
    end
    if obj and obj:GetVo() and obj:GetVo().uuid == enemy_info.uuid then
        BossWGCtrl.Instance:CancelSelectXianJieEnemy()
    end
end

function XianJieBossSceneLogic:OnSelectObjCallBack(obj)
	if obj ~= nil and not obj:IsDeleted() and obj:IsBoss() then
        local boss_id = obj:GetMonsterId()
        if XianJieBossWGData.Instance:GetBossCfgById(boss_id) then
            BossWGCtrl.Instance:CancelSelectXianJieEnemy()
            MainuiWGCtrl.Instance:SetLightBossId(boss_id)
            BossWGData.Instance:SetCurSelectBossID(0, 0, boss_id)
        end
	end
end

function XianJieBossSceneLogic:OnChangeAttackMode()
    BossWGCtrl.Instance:ReloadOtherFollowXianJieEquipImg()
    BossWGCtrl.Instance:ReloadAllAreaIndexEquipImg()  --策划说屏蔽掉势力显示
end

function XianJieBossSceneLogic:GetFbSceneMonsterListCfg(monsters_list_cfg)
	local monsters_list = MapWGData.Instance:GetSceneMonsterSort(monsters_list_cfg)
	return BossWGData.Instance:GetCurSceneAllMonster(monsters_list), true
end


function XianJieBossSceneLogic:GetFbSceneMonsterBossCfg()
	return BossWGData.Instance:GetCurSceneAllBoss()
end

function XianJieBossSceneLogic:GetFbSceneMonsterCfg()
	local list = BossWGData.Instance:GetCurSceneAllMonster(nil, BossWGData.MonsterType.Monster)
	return list,#list
end

-- 获取挂机打怪的位置
function XianJieBossSceneLogic:GetGuiJiMonsterPos()
	CommonFbLogic.GetGuiJiMonsterPos(self)
	local target_distance = 20 * 20
	local target_x = 0
    local target_y = 0
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	for k, v in pairs(obj_move_info_list) do
		local vo = v:GetVo()
		if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) then
			local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
			if distance < target_distance then
				target_x = vo.pos_x
                target_y = vo.pos_y
				target_distance = distance
			end
		end
	end

	return target_x, target_y
end


function XianJieBossSceneLogic:FuHuoCallBack(use_type)
	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack()
	else
		self:HereFuHuoCallBack()
	end
end


function XianJieBossSceneLogic:CommonMoveCallBack()
    local scene_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x,role_y = role:GetLogicPos()
    local _, _, boss_id = BossWGData.Instance:GetCurSelectBossID()
    local boss_data = XianJieBossWGData.Instance:GetBossCfgById(boss_id)
    if IsEmptyTable(boss_data) then
        return
    end
    self:ResetRoleAndCameraDir()
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
    BossWGCtrl.Instance:CancelSelectXianJieEnemy()
    if role_x == boss_data.x_pos and role_y == boss_data.y_pos then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
        local call_back = function()
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end
        GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)

		MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		GuajiCache.monster_id = boss_data.boss_id
		MoveCache.param1 = boss_data.boss_id
		local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, boss_data.x_pos, boss_data.y_pos, range)
    end
end

function XianJieBossSceneLogic:HereFuHuoCallBack()
	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
        local enemy_info = XianJieBossWGData.Instance:GetCurEnemyInfo()
        if not IsEmptyTable(enemy_info) then
            BossWGCtrl.Instance:XianJieFindAndFightEnemy(enemy_info)
        else
            self:CommonMoveCallBack()
        end
	end
end


function XianJieBossSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
    BossWGCtrl.Instance:OutSceneCallback()

    BossWGCtrl.Instance:OutXianJieSceneCallBack()
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    MainuiWGCtrl.Instance:ShowMainuiMenu(true)
    if self.change_attack_mode_event then
		GlobalEventSystem:UnBind(self.change_attack_mode_event)
		self.change_attack_mode_event = nil
    end
    if self.obj_dead_event then
		GlobalEventSystem:UnBind(self.obj_dead_event)
		self.obj_dead_event = nil
	end

	local tired_view = BossWGCtrl.Instance:GetXianJieTiredView()
	local equip_view = BossWGCtrl.Instance:GetXianJieEquipView()
	local view_manager = ViewManager.Instance
	view_manager:RemoveMainUIRightTopChangeList(tired_view)
	view_manager:RemoveMainUIFuPingChangeList(tired_view)
	view_manager:RemoveMainUIRightTopChangeList(equip_view)
	view_manager:RemoveMainUIFuPingChangeList(equip_view)

	BossPrivilegeWGCtrl.Instance:OutBossPrivilegeSceneCallBack()
end

-- 是否是挂机打怪的敌人
function XianJieBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

-- 获取挂机打怪的敌人(优先级： 优先打怪，如果点击前往击杀则优先打BOSS)
function XianJieBossSceneLogic:GetGuajiCharacter()
	local is_need_stop = false
    
    local target_obj = self:GetMonster()
	if target_obj ~= nil then
        is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function XianJieBossSceneLogic:Update(now_time, elapse_time)
	CrossServerSceneLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end

function XianJieBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function XianJieBossSceneLogic:IsMonsterEnemy( target_obj, main_role )
	return true
end

function XianJieBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end
