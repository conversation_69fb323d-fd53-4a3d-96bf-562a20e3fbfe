ShiTuXiuLiWGData = ShiTuXiuLiWGData or BaseClass()

function ShiTuXiuLiWGData:__init()
	if ShiTuXiuLiWGData.Instance then
		error("[ShiTuXiuLiWGData]:Attempt to create singleton twice!")
		return
	end
	ShiTuXiuLiWGData.Instance = self

	self:InitParam()
	self:InitCfgData()
	RemindManager.Instance:Register(RemindName.ShiTuXiuLiMain, BindTool.Bind(self.IsShowShituRemind, self))
end

function ShiTuXiuLiWGData:__delete()
	ShiTuXiuLiWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.ShiTuXiuLiMain)
end

function ShiTuXiuLiWGData:InitParam()
	self.teacher_student_baseinfo = nil
	self.student_taskinfo = nil
	self.student_helpinfo = nil
	self.all_student_info = nil
	self.all_success_nodeinfo = nil
	self.role_info_list = {}
	self.is_remind_student_apply = false
	self.remind_student_uid_list = {}
end

function ShiTuXiuLiWGData:InitCfgData()
	local cfg_list = ConfigManager.Instance:GetAutoConfig("teacherstudentpratice_auto")
	if not cfg_list then
		return
	end
	self.other_cfg_list = cfg_list.other and cfg_list.other[1]
	self.teacher_cfg_list = cfg_list.teacher_loginreward
	self.student_cfg_list = cfg_list.student_task
end

---[[ 协议数据设置
function ShiTuXiuLiWGData:SetTeacherStudentBaseInfo(protocol)
	local info_list = {}
	info_list.teacher_uid = protocol.teacher_uid
	info_list.student_uid = protocol.student_uid
	info_list.close_view_flag = protocol.close_view_flag
	info_list.study_login_day = protocol.study_login_day
	info_list.activity_open_timestamp = protocol.activity_open_timestamp
	info_list.activity_close_timestamp = protocol.activity_close_timestamp
	info_list.teacher_login_reward_flag = protocol.teacher_login_reward_flag
	info_list.is_online = protocol.is_online
	info_list.student_vip_title_reward_status = protocol.student_vip_title_reward_status
	info_list.study_vip_fetch_reward_flag = protocol.study_vip_fetch_reward_flag
	info_list.notify_acp_student_timestamp = protocol.notify_acp_student_timestamp
	info_list.cost_acp_student_timestam = protocol.cost_acp_student_timestam
	info_list.notify_req_teach_timestamp = protocol.notify_req_teach_timestamp
	self.teacher_student_baseinfo = info_list
end

function ShiTuXiuLiWGData:GetTeacherStudentBaseInfo()
	return self.teacher_student_baseinfo
end

function ShiTuXiuLiWGData:SetStudentTaskInfo(protocol)
	local task_info = {}
	task_info.study_task_end_timestamp = protocol.study_task_end_timestamp
	task_info.student_task_info = protocol.student_task_info
	self.student_taskinfo = task_info
end

function ShiTuXiuLiWGData:GetStudentTaskInfo()
	return self.student_taskinfo
end

function ShiTuXiuLiWGData:SetStudentHelpInfo(protocol)
	local help_info = {}
	help_info.student_help_info = protocol.student_help_info
	self.student_helpinfo = help_info
end

function ShiTuXiuLiWGData:GetStudentHelpInfo()
	return self.student_helpinfo
end

function ShiTuXiuLiWGData:SetAllStudentInfo(protocol)
	local all_student_info = {}
	all_student_info.student_info = protocol.student_info
	self.all_student_info = all_student_info
	self:FlushApplyStudentRemind()
end

function ShiTuXiuLiWGData:GetAllStudentInfo()
	return self.all_student_info
end

function ShiTuXiuLiWGData:SetAllSuccessNodeInfo(protocol)
	local all_success_nodeinfo = {}
	all_success_nodeinfo.node_info = protocol.node_info
	self.all_success_nodeinfo = all_success_nodeinfo
end

function ShiTuXiuLiWGData:GetAllSuccessNodeInfo()
	return self.all_success_nodeinfo
end
--]]

---[[ 表格数据
function ShiTuXiuLiWGData:GetOtherCfgList(key)
	if self.other_cfg_list and key then
		return self.other_cfg_list[key]
	end
end

function ShiTuXiuLiWGData:GetTeacherCfgList(index)
	if index and self.teacher_cfg_list then
		return self.teacher_cfg_list[index]
	else
		return self.teacher_cfg_list
	end
end

function ShiTuXiuLiWGData:GetStudentCfgList(index)
	if index and self.student_cfg_list then
		return self.student_cfg_list[index]
	else
		return self.student_cfg_list
	end
end

function ShiTuXiuLiWGData:GetStudentCfgByTaskid(taskid)
	if self.student_cfg_list then
		for k,v in pairs(self.student_cfg_list) do
			if v.taskid == taskid then
				return v
			end
		end
	end
end
--]]

-- 活动结束时间
function ShiTuXiuLiWGData:GetActCloseTime()
	local base_info = self:GetTeacherStudentBaseInfo()
	local task_info = self:GetStudentTaskInfo()
	local close_time = base_info and base_info.activity_close_timestamp or 0
	local task_time = task_info and task_info.study_task_end_timestamp or 0
	if task_time > close_time then
		close_time = task_time
	end
	return close_time, task_time
end

function ShiTuXiuLiWGData:ActIsClose()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local close_time = self:GetActCloseTime()
	return now_time > close_time
end

-- 师傅登录奖励标识 0 不可领取 1 可领取 2 已领取
function ShiTuXiuLiWGData:GetTeacherLoginRewardFlag(index)
	local base_info = self:GetTeacherStudentBaseInfo()
	local reward_flag_list = base_info and base_info.teacher_login_reward_flag or {}
	return reward_flag_list[index + 1] or 0
end

-- 徒弟任务信息
function ShiTuXiuLiWGData:GetStudentTaskInfoByTaskId(task_id)
	local task_info = self:GetStudentTaskInfo()
	local student_task_info = task_info and task_info.student_task_info
	if student_task_info then
		for i=1,#student_task_info do
			if student_task_info[i].task_id == task_id then
				return student_task_info[i]
			end
		end
	end
end

function ShiTuXiuLiWGData:IsShowShituRemind()
	if self:ActIsClose() then
		return 0
	elseif self:HasStudentTaskReward() then
		return 1
	elseif self:HasStudentLoginReward() then
		return 1
	elseif self:HasTeacherLoginReward() then
		return 1
	elseif self:HasTitleReward() then
		return 1
	elseif self:GetApplyStudentRemind() then
		return 1
	end
	return 0
end

function ShiTuXiuLiWGData:HasTeacherLoginReward()
	local base_info = self:GetTeacherStudentBaseInfo()
	if base_info then
		local reward_flag = base_info.teacher_login_reward_flag
		for i=1,#reward_flag do
			if reward_flag[i] == 1 then
				return true
			end
		end
	end
end

function ShiTuXiuLiWGData:HasStudentLoginReward()
	local base_info = self:GetTeacherStudentBaseInfo()
	if base_info and base_info.teacher_uid > 0 then
		local max_login_day = self:GetOtherCfgList("vip_task_login_day")
		if base_info.study_login_day >= max_login_day and base_info.study_vip_fetch_reward_flag == 0 then
			return true
		end
	end
end

-- 徒弟任务奖励(给师傅领的)
function ShiTuXiuLiWGData:HasStudentTaskReward()
	local base_info = self:GetTeacherStudentBaseInfo()
	local cfg_list = ShiTuXiuLiWGData.Instance:GetStudentCfgList()
	if cfg_list and base_info and base_info.student_uid > 0 then
		local task_info = nil
		for k,v in pairs(cfg_list) do
			task_info = self:GetStudentTaskInfoByTaskId(v.taskid)
			if task_info and task_info.reward_flag == 1 then
				return true
			end
		end
	end
end

function ShiTuXiuLiWGData:HasTitleReward()
	local base_info = self:GetTeacherStudentBaseInfo()
	if base_info and base_info.student_vip_title_reward_status == 1 then
		return true
	end
end

---[[ 徒弟申请红点
function ShiTuXiuLiWGData:FlushApplyStudentRemind()
	local data_list = self:GetAllStudentInfo()
	local student_list = data_list and data_list.student_info or {}
	local remind_list = self.remind_student_uid_list

	if #student_list > 0 then
		local is_remind = false
		for _,v in pairs(student_list) do
			if not remind_list[v.uid] then
				is_remind = true
				remind_list[v.uid] = true
			end
		end
		self.remind_student_uid_list = remind_list
		self.is_remind_student_apply = self.is_remind_student_apply or is_remind
	end
end

function ShiTuXiuLiWGData:GetApplyStudentRemind()
	return self.is_remind_student_apply
end

function ShiTuXiuLiWGData:SetApplyStudentRemind(_bool)
	self.is_remind_student_apply = _bool
end
--]]

function ShiTuXiuLiWGData:CanBaiShi(need_tip)
	local is_baishi = false
	local can_baishi = false
	--[[
	local data_list = self:GetAllStudentInfo()
	local student_list = data_list and data_list.student_info or {}
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	for i=1,#student_list do
		if student_list[i].uid == main_vo.role_id then
			is_baishi = true
			break
		end
	end
	--]]
	---[[ 改成拜师完后还可以点击发传闻
	local base_info = self:GetTeacherStudentBaseInfo()
	if base_info then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		is_baishi = server_time < base_info.notify_req_teach_timestamp
	end
	--]]
	
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local student_vip_max = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_max")
	local student_vip_min = ShiTuXiuLiWGData.Instance:GetOtherCfgList("student_vip_min")
	can_baishi = vip_level <= student_vip_max and vip_level >= student_vip_min

	local tip_str = nil
	if need_tip and not can_baishi then
		if vip_level > student_vip_max then
			tip_str = string.format(Language.ShiTuXiuLi.NoBaiShiTip, vip_level)
		-- elseif vip_level < student_vip_min then
			-- tip_str = string.format(Language.ShiTuXiuLi.NoBaiShiTeacher, vip_level, student_vip_min, student_vip_max)
		end
	end

	return can_baishi, is_baishi, tip_str
end

function ShiTuXiuLiWGData:StudentTaskIsPass()
	local task_info = self:GetStudentTaskInfo()
	local task_end_time = task_info and task_info.study_task_end_timestamp or 0
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	return now_time > task_end_time
end

-- 活动是否提早结束
-- 活动第三天，徒弟领完所有奖励，则把活动入口关闭
function ShiTuXiuLiWGData:ActCanClose()
	local base_info = ShiTuXiuLiWGData.Instance:GetTeacherStudentBaseInfo()
	if not base_info then
		return false
	end

	local need_close = true

	-- 如果是徒弟
	if base_info.teacher_uid ~= 0 then
		if need_close then
			-- 如果徒弟登录奖励没领取完，则不允许提早关闭
			local max_login_day = self:GetOtherCfgList("vip_task_login_day")
			if base_info.study_login_day < max_login_day or base_info.study_vip_fetch_reward_flag == 0 then
				need_close = false
			end
		end
		
		if need_close then
			-- 判断称号没领取，则不允许提早关闭
			if base_info.student_vip_title_reward_status < 2 then
				need_close = false
			end
		end

		if need_close then
			local task_all_done = true 		-- 徒弟任务是否都完成了标记
			local cfg_list = ShiTuXiuLiWGData.Instance:GetStudentCfgList()
			if cfg_list then
				local task_info = nil
				for k,v in pairs(cfg_list) do
					task_info = self:GetStudentTaskInfoByTaskId(v.taskid)
					if task_info and task_info.reward_flag == 0 then
						task_all_done = false
						break
					end
				end
			end

			-- 如果没完成则不允许提早关闭
			if not task_all_done then 
				need_close = false
			end
		end
	elseif base_info.student_uid ~= 0 then -- 如果是老师
		if need_close then
			-- 登录奖励
			local cfg_list = ShiTuXiuLiWGData.Instance:GetTeacherCfgList()
			local flag = 0
			for k,v in pairs(cfg_list) do
				flag = ShiTuXiuLiWGData.Instance:GetTeacherLoginRewardFlag(v.index)
				-- 如果有一个没领，则不允许提早关闭
				if flag ~= 2 then
					need_close = false
					break
				end
			end
		end

		if need_close then
			-- 如果称号没领取，则不允许提早关闭
			if base_info.student_vip_title_reward_status < 2 then
				need_close = false
			end
		end

		if need_close then
			local task_reward_all_done = true 		-- 徒弟任务是否都完成了并领取了奖励标记
			local cfg_list = ShiTuXiuLiWGData.Instance:GetStudentCfgList()
			if cfg_list then
				local task_info = nil
				for k,v in pairs(cfg_list) do
					task_info = self:GetStudentTaskInfoByTaskId(v.taskid)
					if task_info and task_info.reward_flag ~= 2 then
						task_reward_all_done = false
						break
					end
				end
			end

			-- 如果没完成则不允许提早关闭
			if not task_reward_all_done then 
				need_close = false
			end
		end
	else -- 如果自己不是徒弟也不是老师，则不能提早结束
		need_close = false
	end

	return need_close
end

---[[ 接受其他人物数据
function ShiTuXiuLiWGData:ClearRoleInfo()
	self.role_info_list = {}
end

function ShiTuXiuLiWGData:GetRoleInfo(role_id, call_back)
	local role_info = self.role_info_list[role_id]
	if role_info and call_back then
		call_back(role_info)
	elseif call_back then
		local main_vo = GameVoManager.Instance:GetMainRoleVo()
		if role_id == main_vo.role_id then
			call_back(main_vo)
			return
		end
		BrowseWGCtrl.Instance:SendQueryRoleInfoReq(role_id, BindTool.Bind(self.ReceiveRoleInfo, self, call_back))
	end
end

function ShiTuXiuLiWGData:ReceiveRoleInfo(call_back, protocol)
	local info_list = {}
	for k,v in pairs(protocol) do
		info_list[k] = v
	end
	self.role_info_list[info_list.role_id] = info_list
	call_back(info_list)
end
--]]

function ShiTuXiuLiWGData:SaveAllTeacherInfo(protocol)
	self.all_teacher_uid_list = protocol.uid_list
end

function ShiTuXiuLiWGData:GetAllTeacherUidList()
	return self.all_teacher_uid_list or {}
end