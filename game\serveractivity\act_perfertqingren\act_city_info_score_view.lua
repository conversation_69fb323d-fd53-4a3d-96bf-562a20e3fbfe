CityInfoRewardView = CityInfoRewardView or BaseClass(SafeBaseView)
function CityInfoRewardView:__init()
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "city_info_score_panel")
    self:SetMaskBg(true)
    self.view_name = "CityInfoRewardView"
end

function CityInfoRewardView:LoadCallBack()
	self.reward_list = AsyncListView.New(CityInfoRewardRender, self.node_list["ph_reward_show"])
end

function CityInfoRewardView:ReleaseCallBack()
	if nil ~= self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function CityInfoRewardView:OnFlush()
    local reward_data_list = ActivePerfertQingrenWGData.Instance:GetLoveCityRewardListInfo()
    self.reward_list:SetDataList(reward_data_list)
    local server_info = ActivePerfertQingrenWGData.Instance:GetServerLoveCityInfo()
    self.node_list["total_score"].text.text = string.format(Language.XiuZhenRoad.TipsDesc2, server_info.total_process)
end

-------------------------------------------------------------------------------------------
--热恋数量累积奖励
CityInfoRewardRender = CityInfoRewardRender or BaseClass(BaseRender)
function CityInfoRewardRender:__init()

end

function CityInfoRewardRender:__delete()
	if self.rewarditem_cells then
		for k,v in pairs(self.rewarditem_cells) do
			v:DeleteMe()
		end
		self.rewarditem_cells = nil
	end
end

function CityInfoRewardRender:LoadCallBack()
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	if nil == self.rewarditem_cells then
		self.rewarditem_cells = {}
		for i = 1, 6 do
            self.rewarditem_cells[i] = ItemCell.New(self.node_list["cell_list"])
        end
	end
end

function CityInfoRewardRender:OnFlush()
    if not self.data then return end
    local cell_count = 0
	for i = 1, 6 do	
        if self.data.reward_item[i - 1] then
            self.rewarditem_cells[i]:SetData(self.data.reward_item[i - 1])
            self.rewarditem_cells[i]:SetVisible(true)
            cell_count = cell_count + 1
        else
            self.rewarditem_cells[i]:SetVisible(false)
        end
	end
	-- self.node_list["title_name"].text.text = self.data.dec
	self.node_list["lbl_lscs"].text.text = string.format(Language.XiuZhenRoad.TipItemsDesc, self.data.need_process)
    self.node_list["btn_lingqu"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.KLQ)
    --self.node_list["can_get_effect"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.KLQ)
	self.node_list["image_wdc"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.WDC)
	self.node_list["image_ylq"]:SetActive(self.data.reward_states == XIUZHENZHILU_TASK_STATES.YLQ)
	XUI.SetButtonEnabled(self.node_list["btn_lingqu"], self.data.reward_states == XIUZHENZHILU_TASK_STATES.KLQ)
    self.node_list.scroll_rect.scroll_rect.enabled = cell_count > 2
    -- local width = self.node_list.scroll_rect.rect.sizeDelta.x
    -- self.node_list.cell_list.rect.anchoredPosition = Vector2(-width/2, 0)
end

function CityInfoRewardRender:OnClickLingQu()
	ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_PROCESS_REWARD,self.data.seq)
end
