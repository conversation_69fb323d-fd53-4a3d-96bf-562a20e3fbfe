-------------------------------------------
-- 基础副本逻辑
--------------------------------------------
BaseSceneLogic = BaseSceneLogic or BaseClass()
ChangeObjType = {
	None = -1,
	Role = 1,
	Boss = 2,
	Monster = 3,
}

-- 部分场景当作副本处理需要重设镜头和人物朝向
local NeedResetRoleAndCameraDirScene = {
	[9506] = 9506, -- 仙宫遗迹副本 
	[9507] = 9507, -- 仙宫遗迹副本 
	[9508] = 9508, -- 仙宫遗迹副本 
}

function BaseSceneLogic:__init()
	self.scene_type = nil

	self.next_get_move_obj_time = 0					-- 下次获取移动对象的时间

	self.auto_guaji_time = 0
	self.auto_guaji_rest_time = 5

	self.key_down = false
	self.key_up = false
	self.is_standby = false
	self.is_show_enter_scene_tip = true
	self.hold_beauty_show_time = 0

	self.gather_flag = false
	self.track_role_type = nil
	self.track_role_param = nil
	self.send_track_time = 0
	self.guaji_pos_info = nil
	self.check_guaji_pos_time = 0

	-- 攻击过主角的玩家列表
	self.be_attacked_role_list = {}


	self.scene_all_load_complete_event = GlobalEventSystem:Bind(SceneEventType.SCENE_ALL_LOAD_COMPLETE, BindTool.Bind1(self.OnSceneDetailLoadComplete, self))
end

function BaseSceneLogic:__delete()
	self.auto_guaji_time = nil
	self.next_get_move_obj_time = nil
	self.key_down = nil
	self.key_up = nil
	self.is_standby = nil

	if self.obj_leave_event then
		GlobalEventSystem:UnBind(self.obj_leave_event)
		self.obj_leave_event = nil
	end

	if self.scene_all_load_complete_event then
		GlobalEventSystem:UnBind(self.scene_all_load_complete_event)
		self.scene_all_load_complete_event = nil
    end


	self:ClearRules()

	self:ClearGuaJiInfo()
	self:ResetTrackRoleInfo()
	self:ResetGatherTimer()
end

function BaseSceneLogic:SetSceneType(scene_type)
	self.scene_type = scene_type
end

function BaseSceneLogic:GetSceneType()
	return self.scene_type
end

--播放背景音乐
function BaseSceneLogic:PlayBGM()
	local audio_cfg = ConfigManager.Instance:GetAutoConfig("audio_auto") or {}
	local scene_id = Scene.Instance:GetSceneId()
	local scene_audio_cfg = audio_cfg.scene or {}

	local show_audio_cfg = scene_audio_cfg[scene_id] or nil
	if show_audio_cfg ~= nil then
		local audio_id = show_audio_cfg.audio_id
		local bundle, asset = ResPath.GetBGMResPath(audio_id)
		AudioService.Instance:PlayBgm(bundle, asset)
	end
end

function BaseSceneLogic:OnSceneDetailLoadComplete()
end

-- 进入场景
function BaseSceneLogic:Enter(old_scene_type, new_scene_type)
	self:ClearGuaJiInfo()
	self:ResetTrackRoleInfo()
	GuajiWGCtrl.Instance:ResetRealiveFollowCache()
	GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
	Scene.Instance:ResetCheckLatelyGather(true)

	-- 进入特殊场景先停掉挂机
	if new_scene_type ~= SceneType.Common then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
		TaskGuide.Instance:CanAutoAllTask(false)
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:FlushView(0, "FlyTaskIsHide", {false})
		if self.scene_type ~= SceneType.Common then
			MainuiWGCtrl.Instance:PlayTopButtonTween(false)
		end
	end)

	ViewManager.Instance:Close(GuideModuleName.Map)
	-- 防止cg和场景错乱
	ViewManager.Instance:Close(GuideModuleName.DujieView)
	-- unity3d项目暂时屏蔽
	-- Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
	GlobalEventSystem:Fire(ObjectEventType.FIGHT_EFFECT_CHANGE, true)
	if self.scene_type ~= SceneType.Common then
		--非普通场景关闭聊天界面
		ViewManager.Instance:Close(GuideModuleName.Chat)
	end
	self:PlayBGM()
	GlobalEventSystem:Fire(MainUIEventType.PORTRAIT_TOGGLE_CHANGE, false)

	GlobalEventSystem:Fire(MainUIEventType.SHOW_MAIN_CHAT) --显示主UI聊天

	if not self.obj_leave_event then
		self.obj_leave_event = GlobalEventSystem:Bind(SceneEventType.OBJ_LEVEL_ROLE, BindTool.Bind(self.OnObjLeave, self))
	end

	Scene.Instance:SetEnterSceneCount()

	local scene_id = Scene.Instance:GetSceneId()
	if Scene.Instance:GetOldSceneId() ~= scene_id and self.is_show_enter_scene_tip then
		self:CheckShowEnterSceneTip(old_scene_type, new_scene_type)
	end

	self.before_act_mode = GameVoManager.Instance:GetMainRoleVo().attack_mode
	self:CheckEnterAutoGuajiType()

	if not self:CanShieldMonster() then
		self.shield_monster_rule = SimpleRule.New(ShieldObjType.Monster, ShieldRuleWeight.Middle, function (obj)
			return false
		end)
		self.shield_monster_rule:Register()
	end

	if self:ForceShieldPet() then
		self.shield_pet_rule = PetRule.New(ShieldObjType.Pet, ShieldRuleWeight.Middle, function (obj)
			return true
		end)
		self.shield_pet_rule:Register()
		self.shield_pet_follow_rule = PetRule.New(ShieldObjType.PetFollowUI, ShieldRuleWeight.Middle, function (obj)
			return true
		end)
		self.shield_pet_follow_rule:Register()
	end

	if self:ForceShieldBeast() then
		self.shield_beast_rule = PetRule.New(ShieldObjType.BeastObj, ShieldRuleWeight.Middle, function (obj)
			return true
		end)
		self.shield_beast_rule:Register()
	end

	if self:CanShowGatherFollow() then
		self.shield_gather_follow_rule = SimpleRule.New(ShieldObjType.GatherFollowUI, ShieldRuleWeight.Middle, function (obj)
			return false
		end)
		self.shield_gather_follow_rule:Register()
	end
	if self:ForceShowTitleFollow() then
		self.force_show_title_follow_rule = SimpleRule.New(ShieldObjType.FollowTitle, ShieldRuleWeight.Middle, function (obj)
			return false
		end)
		self.force_show_title_follow_rule:Register()
	end
	if self:ForceHideTitleFollow() then
		self.force_hide_title_follow_rule = SimpleRule.New(ShieldObjType.FollowTitle, ShieldRuleWeight.Middle, function (obj)
			return true
		end)
		self.force_hide_title_follow_rule:Register()
	end

	self:GuardRemind()

	local policy = self:GetSceneQualityPolicy()
	QualityManager.Instance:SetQualityPolicy(policy)
	if not self.team_info_change_event then
		self.team_info_change_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind(self.TeamInfoChange, self))
	end

	if not self.main_role_be_hit_event then
		self.main_role_be_hit_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_BE_HIT, BindTool.Bind1(self.MainRoleBeHit, self))
	end
	SceneWGData.Instance:ResumeCommonSceneGuajiTypeAndAutoTask()

    Scene.Instance:ForceCancleSkillRang()
    
    if BossAssistWGCtrl.Instance then
        BossAssistWGCtrl.Instance:OnSceneChangeComplete()
    end

    local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    MainuiWGCtrl.Instance:SetModeBtnActive('SwitchAttackMode', scene_cfg.is_show_zonheng_mode == 1)

    --进入跨服组队场景，清除原服组队信息
    if Scene.Instance:GetIsOpenCrossViewByScene() and CrossTeamWGCtrl.Instance then
        CrossTeamWGCtrl.Instance:ClearOriginTeamInfo()
    end

    if not IsNil(MainCameraFollow) then
		MainCameraFollow.AllowRotation = self:GetIsNotRotation()
    end

	-- 因为sceene_type为0 无法走到副本的摄像机设置逻辑，这里根据场景id特殊处理
	if nil ~= NeedResetRoleAndCameraDirScene[scene_id] then
		self:ResetRoleAndCameraDir(old_scene_type ~= new_scene_type)
	else
		if old_scene_type ~= 0 and new_scene_type == 0 then
			Scene.Instance:OnSceneTypeChangeToCommon()
		end
	end

	-- 组队跟随  跨服场景退出后  回到本服场景需要重新唤起跟随
	local need_follow_leader = NewTeamWGData.Instance:GetSetFollowLeaderFlag()
	if need_follow_leader then
		TaskWGCtrl.Instance:AddFlyUpList(function ()
			NewTeamWGData.Instance:GetSetFollowLeaderFlag(false)
			MainuiWGCtrl.Instance:OnClickFollowBtn()
		end)
	end
end

--退出场景
function BaseSceneLogic:Out(old_scene_type, new_scene_type)
	SceneWGData.Instance:CacheCommonSceneGuajiTypeAndAutoTask()
	MainuiWGCtrl.Instance:LockCameraAutoRotate()
	MainuiWGCtrl.Instance:SetClickFindLatelyGatherBtnShow(false)

	self:ClearGuaJiInfo()
	self:ResetTrackRoleInfo()

	-- SettingWGData.Instance:ResetAutoShieldRole()
	if old_scene_type ~= new_scene_type then
		GuajiWGCtrl.Instance:StopGuaji(false, true)
	end
	GlobalEventSystem:Fire(OtherEventType.FUBEN_QUIT, self.scene_type)
	GlobalTimerQuest:AddDelayTimer(function ()
		self:DelayOut(old_scene_type, new_scene_type)
	end, 0.5)

	if self.obj_leave_event then
		GlobalEventSystem:UnBind(self.obj_leave_event)
		self.obj_leave_event = nil
	end

	if self.team_info_change_event then
		GlobalEventSystem:UnBind(self.team_info_change_event)
		self.team_info_change_event = nil
	end

	if nil ~= self.main_role_be_hit_event then
		GlobalEventSystem:UnBind(self.main_role_be_hit_event)
		self.main_role_be_hit_event = nil
	end

	self:CancleAutoGuajiCheck()
	self:ClearRules()

	MainuiWGCtrl.Instance:PlayTopButtonTween(true, nil, true) -- 退出展开顶部按钮界面
	MainuiWGCtrl.Instance:SetMainRoleJumpState(false) 		-- 隐藏轻功跳跃的按钮

    --退出跨服组队场景，清除跨服组队信息
    if Scene.Instance:GetIsOpenCrossViewByScene(old_scene_type) and CrossTeamWGCtrl.Instance then
        CrossTeamWGCtrl.Instance:ClearCrossTeamInfo()
    end
    
	--离开场景 清除击杀传闻
	TipWGCtrl.Instance:ClearZCRuneMsgCenter()
	FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
end

function BaseSceneLogic:CheckShowEnterSceneTip(old_scene_type, new_scene_type)
	if old_scene_type == SceneType.FBCT_NEWPLAYERFB then
		return
	end

	if Scene.Instance:GetEnterSceneCount() < 2 then
		return
	end
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if fb_scene_cfg and fb_scene_cfg.is_scene_name_ani == 1 or new_scene_type == SceneType.Common then
		local scene_id = fb_scene_cfg.is_show_type
		if scene_id == nil or tonumber(scene_id) == 0 then
			scene_id = Scene.Instance:GetSceneId()
		end
		TipWGCtrl.Instance:ShowEneterCommonSceneView(scene_id)
	end
end

function BaseSceneLogic:DelayOut(old_scene_type, new_scene_type)

end

function BaseSceneLogic:Update(now_time, elapse_time)
	if self.track_role_type ~= nil then
		if self.track_role_type ~= OBJ_FOLLOW_TYPE.TASK_CHAIN and self.track_role_param ~= nil then
		local main_role = Scene.Instance:GetMainRole()
			if main_role ~= nil and not main_role:IsDeleted() then
				if self.send_track_time == nil or self.send_track_time < now_time then
					self.send_track_time = now_time + 5
					Scene.SendGetRoleMoveInfo(self.track_role_param, self.track_role_type)
				end
			end
		else
			self:ResetTrackRoleInfo()
		end
	end
end

function BaseSceneLogic:ClearRules()
	if self.shield_monster_rule then
		self.shield_monster_rule:DeleteMe()
		self.shield_monster_rule = nil
	end

	if self.shield_pet_rule then
		self.shield_pet_rule:DeleteMe()
		self.shield_pet_rule = nil
	end

	if self.shield_beast_rule then
		self.shield_beast_rule:DeleteMe()
		self.shield_beast_rule = nil
	end

	if self.shield_pet_follow_rule then
		self.shield_pet_follow_rule:DeleteMe()
		self.shield_pet_follow_rule = nil
	end

	if self.shield_gather_follow_rule then
		self.shield_gather_follow_rule:DeleteMe()
		self.shield_gather_follow_rule = nil
	end

	if self.force_show_title_follow_rule then
		self.force_show_title_follow_rule:DeleteMe()
		self.force_show_title_follow_rule = nil
	end

	if self.force_hide_title_follow_rule then
		self.force_hide_title_follow_rule:DeleteMe()
		self.force_hide_title_follow_rule = nil
	end
end

-- 是否可以拉取移动对象信息
function BaseSceneLogic:CanGetMoveObj()
	return Scene.Instance:GetSceneBroadcast()
end

-- 是否可以取消自动挂机
function BaseSceneLogic:CanCancleAutoGuaji()
	return true
end

function BaseSceneLogic:OnTouchScreen()
	self.auto_guaji_time = Status.NowTime + self.auto_guaji_rest_time
	self.is_standby = false
end

-- 是否自动设置挂机
function BaseSceneLogic:IsSetAutoGuaji()
	return false
end

--是否是在挂机中
function BaseSceneLogic:GetIsGuaiJiing()
	return self.is_standby
end

-- 是否在头上显示特殊图标
function BaseSceneLogic:GetIsShowSpecialImage(scene_obj)
	return false
end

-- 是否可以使用气血药
function BaseSceneLogic:CanUseHpDrug()
	return true
end

-- 是否可以移动
function BaseSceneLogic:CanMove()
	return true
end

-- 是否可以操作摇杆移动
function BaseSceneLogic:CanPlayJoystickDoMove()
	return true
end

-- 是否可以屏蔽怪物
function BaseSceneLogic:CanShieldMonster()
	return 1 ~= Scene.Instance:GetCurFbSceneCfg().monster_alwaysshow
end

-- 是否默认显示采集物名称
function BaseSceneLogic:CanShowGatherFollow()
	return true == ShowGatherFollowList[self.scene_type]
end

-- 是否强制显示称号
function BaseSceneLogic:ForceShowTitleFollow()
	return true == ShowTitleFollowList[self.scene_type]
end

-- 是否强制屏蔽称号
function BaseSceneLogic:ForceHideTitleFollow()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg.pb_chenhao and fb_scene_cfg.pb_chenhao == 1 then
        return true
    end
    return false
end

-- 是否强制屏蔽宠物
function BaseSceneLogic:ForceShieldPet()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg and fb_scene_cfg.pb_pet and  fb_scene_cfg.pb_pet == 1 then
        return true
    end
	return false
end

-- 是否强制屏蔽宠物
function BaseSceneLogic:ForceShieldBeast()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg and fb_scene_cfg.pb_beast and  fb_scene_cfg.pb_beast == 1 then
        return true
    end
	return false
end

-- 是否显示元神领域
function BaseSceneLogic:IsShowAransformationLingyu()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg and fb_scene_cfg.is_show_phtotmask and  fb_scene_cfg.is_show_phtotmask == 1 then
        return true
    end
	return false
end

-- 是否显示元神望气
function BaseSceneLogic:IsShowAransformationWangQi()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg and fb_scene_cfg.is_show_wangqi and  fb_scene_cfg.is_show_wangqi == 1 then
        return true
    end
	return false
end

-- 获取角色名
function BaseSceneLogic:GetRoleNameBoardText(role_vo,role)
	local name_color = role_vo.name_color or COLOR3B.WHITE
	local t = {}

	if role_vo.role_id == GameVoManager.Instance:GetMainRoleVo().role_id then
		t.color = name_color == EvilColorList.NAME_COLOR_WHITE and COLOR3B.ROLE_NAME_COLOR or COLOR3B.RED
	else
		local guild_id = GameVoManager.Instance:GetMainRoleVo().guild_id
		local color = COLOR3B.WHITE
		if guild_id == role_vo.guild_id and guild_id ~= 0 then
			color = COLOR3B.WHITE
		end

        local is_enemy = self:IsRoleEnemy(role,Scene.Instance:GetMainRole())
		if is_enemy then
			t.color = COLOR3B.RED
		else
			t.color = name_color
		end
	end

	t.text = role_vo.name or role_vo.role_name

	return t
end

-- 获取角色仙盟名
function BaseSceneLogic:GetGuildNameBoardText(role_vo)
	local t = {}
	local guild_name = role_vo.guild_name or ""

	if "" == guild_name then return t end

	local authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[role_vo.guild_post]
	local post_name = authority and authority.post or ""

	t[1] = {}
	t[1].color = COLOR3B.WHITE
	t[1].text = "【" .. guild_name .. "】" .. COMMON_CONSTS.POINT .. post_name

	return t
end

-- 是否友方
function BaseSceneLogic:IsFriend(target_obj, main_role)
	return not self:IsEnemy(target_obj, main_role)
end

local name_t = {}
local color_name = ""
-- 获得角色头上名字的颜色
function BaseSceneLogic:GetColorName(role)
	name_t = self:GetRoleNameBoardText(role.vo, role)
	return name_t.text, name_t.color
end

-- 获取怪物名
function BaseSceneLogic:GetMonsterName(monster)
	color_name = ""
	if monster.vo then
		color_name = monster.vo.name or ""
	end
	return color_name
end

-- 是否能走到该坐标
function BaseSceneLogic:GetIsCanMove(x, y)
	return true
end

function BaseSceneLogic:GetIsCanGather(obj)
	return true
end

-- 在不同模式是否敌方
function BaseSceneLogic:AttackModeIsEnemy(target_obj, main_role)
	if nil == target_obj or nil == main_role or not target_obj:IsCharacter() then
		return false
	end

    local b1
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        b1 = not CrossTeamWGData.Instance:GetTargetIsTeamMember(target_obj:GetUUID())
    else
        b1 = not SocietyWGData.Instance:IsTeamMember(target_obj:GetRoleId())
    end
	local b2 = main_role:GetVo().guild_id == 0 or main_role:GetVo().guild_id ~= target_obj:GetVo().guild_id

		-- 同盟模式伴侣不为敌人
	local lover_id = main_role:GetVo().lover_uid or 0
	local is_lover = lover_id ~= 0 and lover_id == target_obj:GetVo().role_id
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= nil and scene_type == SceneType.XianMengzhan then
		is_lover = false
    end

    local attack_mode = main_role:GetVo().attack_mode

    --策划要求同盟但是切换成全体模式之后不被屏蔽
    if attack_mode == ATTACK_MODE.ALL  then
        b2 = true
    end
	return b1 and b2 and not is_lover
end

-- 是否敌方
function BaseSceneLogic:IsEnemy(target_obj, main_role, ignore_table, test)
	main_role = main_role or Scene.Instance:GetMainRole()
	if nil == target_obj or nil == main_role or target_obj:IsDeleted() or not target_obj:IsCharacter() then
		return false
	end

	ignore_table = ignore_table or {}	-- 忽略列表

	if main_role:IsRealDead() then												-- 自己死亡
		return false, Language.Fight.SelfDead
	end

	if target_obj:IsRealDead() then												-- 目标死亡
		return false, Language.Fight.TargetDead
	end
	
	if main_role:IsQingGong() or main_role:IsMitsurugi() then
		return false, Language.QingGong.SelfQingGong
	end

	if main_role:IsInSafeArea() and not ignore_table[SceneIgnoreStatus.MAIN_ROLE_IN_SAFE]
	and not RevengeWGData.Instance:IsCurRevengeObj(target_obj) then						-- 自己在安全区
		return false, Language.Fight.InSafe
	end

	if target_obj:IsInSafeArea() and not ignore_table[SceneIgnoreStatus.OTHER_IN_SAFE]
	and not RevengeWGData.Instance:IsCurRevengeObj(target_obj) then						-- 目标在安全区
		return false, Language.Fight.TargetInSafe
	end

	-- 角色移动范围被限制，选中的目标已经超出了范围
	local t_x, t_y = target_obj:GetLogicPos()
	local pos_x, pos_y = main_role:GetLogicPos()
	local _, _, is_limit = main_role:GetMovePosByLimit(t_x, t_y, true)
	if is_limit then
		return false
	end

	local recovery_valid, recovery_x, recovery_y = GuajiWGCtrl.Instance:CheckIsRecoveryFollowVaild()
	if recovery_valid and recovery_x ~= nil and recovery_y ~= nil then
		if GameMath.GetDistance(recovery_x, recovery_y, pos_x, pos_y, false) >= 15 * 15 then
			return false
		end
	end

	if target_obj:GetType() == SceneObjType.Role then
		if Scene.Instance:GetSceneForbidPk() then
			return false, Language.Fight.SceneForbidPk
		end


		if main_role:GetVo().level < COMMON_CONSTS.XIN_SHOU_LEVEL then			-- 自己新手
			return false, Language.Fight.XinShou
		end

		if target_obj:GetVo().level < COMMON_CONSTS.XIN_SHOU_LEVEL then			-- 目标新手
			return false, Language.Fight.TargetXinShou
		end

		if target_obj:IsQingGong() or target_obj:IsMitsurugi() then
			return false, Language.QingGong.TargetQingGong
		end

		if target_obj:IsInvisible() then
			return false, ""
		end

		return self:IsRoleEnemy(target_obj, main_role)

	elseif target_obj:GetType() == SceneObjType.Pet then
		if Scene.Instance:GetSceneForbidPk() then
			return false, Language.Fight.SceneForbidPk
		end

		if main_role:GetVo().level < COMMON_CONSTS.XIN_SHOU_LEVEL then			-- 自己新手
			return false, Language.Fight.XinShou
		end

		if target_obj:IsPet() and target_obj:IsOnwerPet() then  				-- 自己的宠物
			return false, Language.Fight.OnwerPet
		end

	    local target_role = Scene.Instance:GetObjectByObjId(target_obj:GetVo().owner_objid)
		if target_role and target_role:GetVo().level < COMMON_CONSTS.XIN_SHOU_LEVEL then			-- 目标新手
			return false, Language.Fight.TargetXinShou
		end

		if target_role and target_role:IsQingGong() then
			return false, Language.QingGong.TargetQingGong
		end

		if target_role and target_role:IsMitsurugi() then
			return false, Language.QingGong.TargetQingGong
		end

		return self:IsPetEnemy(target_obj, main_role)

	elseif target_obj:GetType() == SceneObjType.Monster then
		if not BaseSceneLogic.IsAttackMonster(target_obj:GetMonsterId(), target_obj.vo) then	-- 是否可攻击的怪
			return false, Language.Fight.TargetNotAtk
		end

		local objvo = target_obj:GetVo()
		if objvo ~= nil and objvo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_CALL then
			if main_role ~= nil and not main_role:IsDeleted() and objvo.is_has_owner == main_role:GetObjId() then
				return false
			elseif main_role ~= nil and not main_role:IsDeleted() and objvo.is_has_owner ~= main_role:GetObjId() then
				local owner_id = objvo.is_has_owner
				if owner_id ~= nil and owner_id ~= "" and owner_id ~= objvo.obj_id then
					local owner_obj = Scene.Instance:GetObj(owner_id)
					if owner_obj ~= nil and not owner_obj:IsDeleted() and owner_obj:IsRole() then
						if Scene.Instance:GetSceneForbidPk() then
							return false
						end

						local is_role_enemy = self:IsRoleEnemy(owner_obj, main_role)
						return is_role_enemy, Language.Fight.TargetNotAtk		
					end
				end
			end
		elseif objvo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_TASK_CALL then
			return false
		end

		return self:IsMonsterEnemy(target_obj, main_role)
	end

	return false, Language.Fight.TargetNotAtk
end


-- 是否可攻击的怪
function BaseSceneLogic.IsAttackMonster(monster_id, objvo)
	local monster_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
	if nil ~= monster_config and 0 == monster_config.is_attacked then
		return false
	end

	-- 天神羁绊技能召唤出来的天神不能选中攻击
	if objvo.monster_type == SCENE_MONSTER_TYPE.MONSTER_TYPE_TIANSHEN then
		return false
	end

	return true
end

-- 是否达到指定条件搜索全地图怪
function BaseSceneLogic:ConditionScanMonster(vo)
	return true
end

function BaseSceneLogic:IsPetEnemy(target_obj, main_role)
	main_role = main_role or Scene.Instance:GetMainRole()
	if nil == target_obj or nil == main_role or not target_obj:IsCharacter() then
		return false
	end

	return true
end

function BaseSceneLogic:CheckIsBossAssistForceId(target_obj)
	if IS_ON_CROSSSERVER then
		return BossAssistWGData.Instance:IsForceAttack(target_obj:GetVo().origin_uid)
	else
		return BossAssistWGData.Instance:IsForceAttack(target_obj:GetVo().role_id)
	end
end

-- 角色是否是敌人
function BaseSceneLogic:IsRoleEnemy(target_obj, main_role)
	main_role = main_role or Scene.Instance:GetMainRole()
	if nil == target_obj or nil == main_role or not target_obj:IsCharacter() then
		return false
	end

	--判断敌人是否已经死亡
	if target_obj:IsRealDead() then
		return false, Language.Fight.TargetDead
	end

	local main_role_vo = main_role:GetVo()
	local target_vo = target_obj:GetVo()
	if target_vo == nil or main_role_vo == nil then
		return
	end

	local attack_mode = main_role_vo.attack_mode
	if self:CheckIsBossAssistForceId(target_obj) then
		return true
	end

	if attack_mode == ATTACK_MODE.PEACE then
		return false, Language.Fight.AttackTips3-- 改成红名都不能攻击 target_obj.vo.name_color ~= EvilColorList.NAME_COLOR_WHITE, Language.Fight.IsPeace
	elseif attack_mode == ATTACK_MODE.TEAM then
		if Scene.Instance:GetIsOpenCrossViewByScene() then
            return not CrossTeamWGData.Instance:GetTargetIsTeamMember(target_obj:GetUUID())
        else
            return not SocietyWGData.Instance:IsTeamMember(target_obj:GetOriginId())
        end

    elseif attack_mode == ATTACK_MODE.GUILD then
        local b1
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            b1 = not CrossTeamWGData.Instance:GetTargetIsTeamMember(target_obj:GetUUID())
        else
            b1 = not SocietyWGData.Instance:IsTeamMember(target_obj:GetOriginId())
        end
		local b2 = main_role_vo.guild_id == 0 or main_role_vo.guild_id ~= target_vo.guild_id

		-- 同盟模式非仙盟战伴侣不为敌人
		local lover_id = main_role_vo.lover_uid or 0
		local is_lover = lover_id ~= 0 and lover_id == target_vo.role_id
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type ~= nil and scene_type == SceneType.XianMengzhan then
			is_lover = false
		end

		if is_lover then
			return false, Language.Fight.TargetMarrry
		end

		if b1 and b2 and not is_lover then
			return true
		end

		if not b1 then
			return b1, Language.Fight.TargetTeam
		end
		return b2, Language.Fight.TargetGuild
	elseif attack_mode == ATTACK_MODE.ALL then
		return true

	elseif attack_mode == ATTACK_MODE.NAMECOLOR then --跨服模式
		-- return target_obj:GetVo().name_color ~= GameEnum.NAME_COLOR_WHITE
		if target_vo.merge_plat_type ~= main_role_vo.merge_plat_type or target_vo.merge_server_id ~= main_role_vo.merge_server_id then
			if Scene.Instance:GetIsOpenCrossViewByScene() and CrossTeamWGData.Instance:GetTargetIsTeamMember(target_obj:GetUUID()) then
				return false
			else
				return true
			end
		else
			return false, Language.Common.NoCrossFight
		end
	-- elseif attack_mode == ATTACK_MODE.BAN_WAR then --禁战模式
    --     return false
    elseif attack_mode == ATTACK_MODE.AREA then --战区模式
		if main_role_vo.area_index == target_vo.area_index then
			return false, Language.Common.NotFightArea
		else
			return true
		end
	elseif attack_mode == ATTACK_MODE.ROLE_ENEMY then  -- 仇人模式
		-- 仇人模式只能攻击本服仇人列表里面的
		if target_vo ~= nil and  SocietyWGData.Instance:CheckisRoleEnemy(target_vo.origin_plat_type, target_vo.origin_uid) then
			return true
		else
			return false, Language.Common.NotFightEnemy
		end
	elseif attack_mode == ATTACK_MODE.CAMP then
		return main_role_vo.camp == 0 or (main_role_vo.camp ~= target_vo.camp)
	elseif attack_mode == ATTACK_MODE.JIEYI then
		-- 不可攻击结义成员
		local id_jieyi_menber = SwornWGData.Instance:IsJieYiMengberByUid(target_vo.origin_uid)
		return not id_jieyi_menber
	end

	return true
end

-- 怪物是否是敌人
function BaseSceneLogic:IsMonsterEnemy(target_obj, main_role)
	return true
end

function BaseSceneLogic:SpecialSelectEnemy()
	return false
end

function BaseSceneLogic:GetGuajiPos()
	-- body
end

function BaseSceneLogic:OnMainRoleRealive()
	-- body
end

-- 获得捡取掉物品的最大距离
function BaseSceneLogic:GetPickItemMaxDic(item_id)
	return 2
end

function BaseSceneLogic:FindScenePathNode(scene_id, node)
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if nil == scene_cfg then
		return false
	end
	self.checked_list[scene_id] = 1 --记录检查过的
	local cur_all_door = {}
	for k,v in pairs(scene_cfg.doors) do
		if v.target_scene_id ~= nil then
			local cur_node = {}
			cur_node.x = v.x
			cur_node.y = v.y
			cur_node.target_scene_id = v.target_scene_id
			cur_node.scene_id = scene_id
			cur_node.prve_node = node

			if v.target_scene_id == self.target_scene_id then --找到目的地
				self.node_list[#self.node_list + 1] = cur_node
				return true
			elseif self.checked_list[v.target_scene_id] ~= 1 then --不是目的地先记录下
				table.insert(cur_all_door,cur_node)
			end
		end
	end

	-- 递归遍历是否有目的地，没有的话把自身从node_list里移除
	for index, cur_node in ipairs(cur_all_door) do
		self.node_list[#self.node_list + 1] = cur_node
		if self:FindScenePathNode(cur_node.target_scene_id, cur_node) then
			return true
		else
			self.node_list[#self.node_list + 1] = nil
		end
	end
	return false
end

--获得场景之间可行走路径点（不考虑最短路径）
function BaseSceneLogic:GetScenePath(start_scene_id, target_scene_id)
	self.checked_list = {}
	self.node_list = {}
	self.target_scene_id = target_scene_id
	self:FindScenePathNode(start_scene_id)
	--从目标场景节点中往前推组成新的路径列表
	local cur_node = nil
	for k,v in pairs(self.node_list) do
		if v.target_scene_id == target_scene_id then
			cur_node = v
			break
		end
	end

	if cur_node == nil then
		return {}
	end

	local path_list = {}
	path_list[1] = cur_node
	local loop_count = 0
	if nil ~= cur_node then
		while loop_count < 200 do
			for k,v in pairs(self.node_list) do
				if v == cur_node.prve_node then
					table.insert(path_list, 1, v) --总是放在第1个
					cur_node = v
					if v.target_scene_id == start_scene_id then -- 返回到起始场景结束
						return path_list
					end
				end
			end
			loop_count = loop_count + 1
		end
	end
	return path_list
end

--获得场景目标点。
--找不到场景入口点的情况下则找该场景的传送点
--一个地图可能有多个传送点，需通过地图寻路找到路径上的传送点。
function BaseSceneLogic:GetTargetScenePos(scene_id)
	local scene_path = self:GetScenePath(Scene.Instance:GetSceneId(), scene_id)
	local scene_node = scene_path[#scene_path]
	if scene_node ~= nil then --最终获得所在目标场景上的传送点
		-- 实际上scene_id传进来是目标场景，所以放到第二个参数
		return Scene.Instance:GetSceneDoorPos(scene_node.target_scene_id, scene_node.scene_id)
	end

	return nil, nil
end

-- 自动挂机优先级,为ture的话则自动挂机优先于寻路
function BaseSceneLogic:GetAutoGuajiPriority()
	return false
end

function BaseSceneLogic:GetGuajiSelectObjDistance()
	return COMMON_CONSTS.SELECT_OBJ_DISTANCE
end

function BaseSceneLogic:IsAutoStopTaskOnGuide()
	return true
end

-- 是否能够在频率过低时自动设置
function BaseSceneLogic:IsCanSystemAutoSetting()
	return true
end

-- 待机5秒，设置自动挂机状态
function BaseSceneLogic:CheckAutoGuaji(now_time)
	local main_role = Scene.Instance:GetMainRole()
	if self:IsSetAutoGuaji() and main_role then
		if GuajiCache.guaji_type == GuajiType.None then
			if not main_role:IsMove() and not main_role:IsRealDead() then
				-- 鼠标点击事件
				if UnityEngine.Input.GetMouseButtonDown(0) then
					self.key_down = true
					self.key_up = false
				end
				if UnityEngine.Input.GetMouseButtonUp(0) then
					self.key_up = true
					self.key_down = false
				end

				if not self.key_up and self.key_down then
					self.auto_guaji_time = now_time + self.auto_guaji_rest_time
				end
				self.key_down = nil
				self.key_up = nil
				-- 移动端触屏事件
				if UnityEngine.Input.touchCount > 0 then
					self.auto_guaji_time = now_time + self.auto_guaji_rest_time
				end
				if self.auto_guaji_time <= 0 then
					self.auto_guaji_time = now_time + self.auto_guaji_rest_time
				elseif self.auto_guaji_time <= now_time then
					if not self.is_standby then
						GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
						-- TipWGCtrl.Instance:ShowOrHideStandbyMaskView(true)
						self.is_standby = true
					end
				end
			elseif not main_role:IsStand() and not main_role:IsRealDead() then
				self.auto_guaji_time = now_time + self.auto_guaji_rest_time
				self.is_standby = false
			end
		elseif main_role:IsMove() then
			self.auto_guaji_time = now_time + self.auto_guaji_rest_time
			self.is_standby = false
		end
	elseif not self:IsSetAutoGuaji() and self.is_standby then
		-- TipWGCtrl.Instance:ShowOrHideStandbyMaskView()
		self.is_standby = false
	end
end

-- 游泳区的高度
function BaseSceneLogic:GetWaterWayOffset()
	return 0
end

function BaseSceneLogic:AlwaysShowMonsterName()
	return false
end

--跳转到某一NPC身边不使用飞鞋
-- 弃用
function BaseSceneLogic:FlyToNpc(npc_id)
	local scene_npc_cfg = nil
	local scene_id = 0
	for k,v in pairs(Config_scenelist) do
		if v.sceneType == SceneType.Common and v.id ~= 1 then
			local scene_cfg = ConfigManager.Instance:GetSceneConfig(v.id)
			if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
				for i, j in pairs(scene_cfg.npcs) do
					if j.id == npc_id then
						scene_npc_cfg = j
						scene_id = v.id
						break
					end
				end
			end
			if scene_npc_cfg ~= nil then
				break
			end
		end
	end

	if scene_npc_cfg == nil then
		print_error("con't find npc id : "..npc_id)
		return
	end

	TaskWGCtrl.Instance:SendFlyByShoe(scene_id,scene_npc_cfg.x,scene_npc_cfg.y)
end

function BaseSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function BaseSceneLogic:GetRoleEnemy()
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return
	end

	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		if self:IsEnemy(v, main_role) then
			return v
		end
	end
end

function BaseSceneLogic:GetGuajiCharacter()
	local target_obj = nil
	local dis = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local is_need_stop = false

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj, dis, is_need_stop
	end

	local attack_mode = main_role:GetVo().attack_mode
	if attack_mode == ATTACK_MODE.PEACE then
		local obj = self:GetMonster()
		if obj ~= nil and self:IsEnemy(obj, main_role) then
			target_obj = obj
		end
	elseif attack_mode == ATTACK_MODE.GUILD or attack_mode == ATTACK_MODE.ALL then
		target_obj = self:GetRoleEnemy()
		if target_obj == nil then
			local obj = self:GetMonster()
			if obj ~= nil and self:IsEnemy(obj, main_role) then
				target_obj = obj
			end
		else
			is_need_stop = true
		end
	end

	return target_obj, dis, is_need_stop
end

function BaseSceneLogic:CharacterCanSelect(target_obj)
	return true
end

function BaseSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	return false
end

--"选择目标"
--主动根据情况决定是否行走过去
function BaseSceneLogic:SelectTargetObj(target_obj, is_auto)
	GuajiWGCtrl.Instance:MoveToObj(target_obj, 4)
end

function BaseSceneLogic:OnMonsterEnter(monster_vo)
	if not monster_vo then return end
	local monster_obj = Scene.Instance:GetObjectByObjId(monster_vo.obj_id)
	if not monster_obj then return end
	local obj_type = monster_obj:IsBoss() and ChangeObjType.Boss or ChangeObjType.Monster
	self:UpdateList(monster_vo.obj_id, obj_type)
end

function BaseSceneLogic:OnRoleEnter(obj_id)
	self:UpdateList(obj_id, ChangeObjType.Role)
end

local select_obj_list = {}
function BaseSceneLogic:OnObjLeave(obj_id)
	select_obj_list[obj_id] = nil
end


function BaseSceneLogic:GetNextSelectTargetId()
		-- 获取所有可选对象
	local obj_list = Scene.Instance:GetObjList()
	if not next(obj_list) then
		return -1
	end

	local temp_obj_list = {}
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local target_x, target_y = 0, 0

	local can_select = true
	local main_role = Scene.Instance:GetMainRole()
	local sort = 0
	for k, v in pairs(obj_list) do
		if v:IsCharacter() then
			can_select = Scene.Instance:IsEnemy(v, main_role)

			if can_select then
				sort = 10
				if v:IsRole() then
					sort = 1
				elseif v:IsMonster() and v:IsBoss() then
					sort = 2
				elseif v:IsPet() then
					sort = 11
				end
				target_x, target_y = v:GetLogicPos()
				table.insert(temp_obj_list, {obj = v, sort = sort, dis = GameMath.GetDistance(x, y, target_x, target_y, false)})
			end
		end
	end
	if not next(temp_obj_list) then
		return -1
	end
	--优先选人、再选boss、再选其它，多个选距离最近的
	table.sort(temp_obj_list, SortTools.KeyLowerSorter("sort", "dis"))

	-- 排除已选过的
	local select_obj = nil
	for i, v in ipairs(temp_obj_list) do
		if nil == select_obj_list[v.obj:GetObjId()] and v.obj ~= SceneObj.select_obj then
			select_obj = v.obj
			break
		end
	end

	-- 如果没有选中，选第一个，并清空已选列表
	if nil == select_obj then
		select_obj = temp_obj_list[1].obj
		select_obj_list = {}
	end
	if nil == select_obj then
		return -1
	end

	if select_obj:IsPet() then --排除宠物
		return -1
	end

	select_obj_list[select_obj:GetObjId()] = select_obj
	return select_obj:GetObjId()
end

function BaseSceneLogic:CanAutoRotation()
	if nil == FuBenWGData.Instance then return true end
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	local fb_angle = FuBenWGData.Instance:GetFbCamearCfg(scene_id, scene_type)
	if fb_angle and fb_angle.lock_rotation == 1 then
		return false
	end
	return true
end

function BaseSceneLogic:GetIsNotRotation()
	if nil == FuBenWGData.Instance then return true end
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	local fb_angle = FuBenWGData.Instance:GetFbCamearCfg(scene_id, scene_type)
	if fb_angle and fb_angle.not_rotation == 1 then
		return false
	end

	return true
end

function BaseSceneLogic:IsHideBOSSNameState()
	return true
end

function BaseSceneLogic:NeedJump()
	return true
end

function BaseSceneLogic:GetCanJump()
	-- body
	return true
end

-- 追逐的目标会移动，减少对应攻击距离
function BaseSceneLogic:MoveRange()
	return 6
end

-- 采集偏移距离
function BaseSceneLogic:MoveToGatherRange()
	return 2
end

--获取副本地图怪物在小地图上显示的位置配置
function BaseSceneLogic:GetFbSceneMonsterCfg()
	return nil, 0
end

--获取副本地图怪物在小地图上怪物列表的显示
-- false == 不需要特殊处理，直接加载默认怪物列表
-- true == 需要特殊处理，读取参数1中的怪物列表
function BaseSceneLogic:GetFbSceneMonsterListCfg()
	return nil, false
end

--获取副本地图boss形象再小地图中的显示
function BaseSceneLogic:GetFbSceneMonsterBossCfg()
	return nil
end

-- 获取传送门是否需要走自己的单独逻辑
function BaseSceneLogic:GetDoorNeedAloneLogic()
	return nil
end

-- 获取副本中npc是否需要走自己的单独逻辑
function BaseSceneLogic:GetNpcTaskNeedAloneLogic(npc_id)
	return nil
end

-- 获取副本是否能切换攻击模式
function BaseSceneLogic:CanChangeAttackMode()
	return true
end

-- 是否显示血量
function BaseSceneLogic:RoleCanShowHp(role)
	return true
end

-- 自动挂机检测挂机状态改变
function BaseSceneLogic:CheckEnterAutoGuajiType()
	if self:IsSetAutoGuaji() then
		self.change_guaji_type = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.ChangeGuajiType, self))
	end
end

-- 取消自动挂机检测挂机状态改变
function BaseSceneLogic:CancleAutoGuajiCheck()
	if self.change_guaji_type then
		GlobalEventSystem:UnBind(self.change_guaji_type)
		self.change_guaji_type = nil
	end
end

function BaseSceneLogic:ChangeGuajiType(guaji_type)
	if guaji_type == GuajiType.None then
		self.is_standby = false
		self.auto_guaji_time = Status.NowTime + self.auto_guaji_rest_time
	end
end

function BaseSceneLogic:ForceHideGuard()
	return false
end

function BaseSceneLogic:CanMonsterDoJump()
	return false
end

function BaseSceneLogic:GetMonsterJumpLogicPos(monster_obj)
	return nil,nil
end

function BaseSceneLogic:CanAutoPick()
	return false
end

--场景特殊不能移动状态
function BaseSceneLogic:GetSceneLogicMoveState()
	return true
end

-- 主角被攻击
function BaseSceneLogic:MainRoleBeHit(role)
	-- 保存攻击者
	if role and role.vo and role.vo.role_id and role.vo.role_id > 0 then
		self.be_attacked_role_list[role.vo.role_id] = true
		role:UpdatePriority()
	end
end

-- 角色是否需要进行优先级的计算
function BaseSceneLogic:IsRoleNeedCalculatePriortiy()
	return true
end

-- 角色显示的优先级
function BaseSceneLogic:GetRoleVisiblePriortiy(role)
	local priortiy = false
    local role_id = 0
    local uuid = MsgAdapter.InitUUID()
	if role and role.vo then
        role_id = role.vo.role_id or 0
        uuid = role.vo.uuid
	end

	-- 优先显示敌人
	if self:IsEnemyVisiblePriortiy() then
		local main_role = Scene.Instance:GetMainRole()
		priortiy = self:IsRoleEnemy(role, main_role) or false
	else
        -- 队员优先显示
        local is_member
        if Scene.Instance:GetIsOpenCrossViewByScene() then
            is_member = CrossTeamWGData.Instance:GetTargetIsTeamMember(uuid)
        else
            is_member = SocietyWGData.Instance:IsTeamMember(role_id)
        end
		if is_member then
			priortiy = true
		end
	end

	-- 优先显示攻击过主角的玩家(切换场景后会清空)
	if not priortiy then
		priortiy = self.be_attacked_role_list[role_id] or false
	end

	if priortiy then
		return SceneAppearPriority.High
	else
		return SceneAppearPriority.Middle
	end
end

-- 此场景优先保证单位品质
function BaseSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitQuality
end

-- 此场景优先显示敌人(false则优先显示队员)
function BaseSceneLogic:IsEnemyVisiblePriortiy()
	return false
end

function BaseSceneLogic:TeamInfoChange()
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		v:UpdatePriority()
	end
end

--是否创建跳跃点
function BaseSceneLogic:IsShowJumpPoint()
	return true
end

function BaseSceneLogic:CreateCustomBlockPoint()
	-- body
end

function BaseSceneLogic:CanOpenMail()
	return true
end

function BaseSceneLogic:IsCanCheckWaterArea()
	return false
end

--提醒穿戴小鬼
function BaseSceneLogic:GuardRemind()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if not fb_scene_cfg or fb_scene_cfg.scene_guard_type == 0 then
		return
	end

	local xiaogui_list = ItemWGData.Instance:GetXiaoGuiList(fb_scene_cfg.scene_guard_type)

	local temp_xiaogui_info
	local temp_info
	for k,v in pairs(xiaogui_list) do
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)

		if xiaogui_cfg then
			if not temp_xiaogui_info then
				temp_xiaogui_info = xiaogui_cfg
				temp_info = v
			else
				if temp_xiaogui_info.color < xiaogui_cfg.color then

					temp_xiaogui_info = xiaogui_cfg
					temp_info = v
				end
			end
		end
	end


	if not temp_xiaogui_info then
		return
	end

	local xiaogui_info = EquipWGData.Instance:GetmpGuardInfo()
	local weae_guard_info
	local weae_guard_id

	for i=1,2 do
		local data = xiaogui_info.item_wrapper[i]
		if data and data.item_id and data.item_id > 0 then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(data.item_id)
			if cfg.impguard_type == fb_scene_cfg.scene_guard_type then
				weae_guard_info = cfg
				weae_guard_id = data.item_id
				break
			end
		end
	end

	--是否穿戴着显示小鬼
	local has_xianshi = (weae_guard_id and weae_guard_id == 10101 and temp_info.item_id == 10100)

	if (weae_guard_info and temp_xiaogui_info.color <= weae_guard_info.color) and not has_xianshi then
		return
	end

	if temp_info then
		FunctionGuide.Instance:OpenKeyUseView(temp_info.item_id, temp_info.index)
	end
end

function BaseSceneLogic:ResetGatherTimer()
	if self.gather_delay_timer then
		GlobalTimerQuest:CancelQuest(self.gather_delay_timer)
		self.gather_delay_timer = nil
	end

	self.gather_flag = false
end

function BaseSceneLogic:SetIsSendGather(value)
	self.gather_flag = value

	if value then
		if self.gather_delay_timer then
			GlobalTimerQuest:CancelQuest(self.gather_delay_timer)
			self.gather_delay_timer = nil
		end

		self.gather_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.OnGatherDealy, self), 2)
	else
		if self.gather_delay_timer then
			GlobalTimerQuest:CancelQuest(self.gather_delay_timer)
			self.gather_delay_timer = nil
		end
	end
end

function BaseSceneLogic:GetIsSendGather()
	return self.gather_flag
end

function BaseSceneLogic:OnGatherDealy()
	self:ResetGatherTimer()
end

function BaseSceneLogic:OnSelectObj(target_obj, select_type)

end

-- 可以点击地面
function BaseSceneLogic:IsCanClickGround()
	return true
end

function BaseSceneLogic:ClearGuaJiInfo()
	self.guaji_pos_info = nil
end

function BaseSceneLogic:SetGuaJiInfoPos(x, y)
	self:ClearGuaJiInfo()

	if x == nil or y == nil then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	local pos_cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").hanging_range
	for k,v in pairs(pos_cfg) do
		if v.scene_type == scene_type then
			self.guaji_pos_info = {}
			self.guaji_pos_info.aoi_range = v.aoi_range
			self.guaji_pos_info.run_range = v.run_range
			self.guaji_pos_info.scene_type = v.scene_type
			self.guaji_pos_info.x = x
			self.guaji_pos_info.y = y
			self.guaji_pos_info.monster_id = monster_id
			self.guaji_pos_info.range = range
			if v.aoi_range >= v.run_range then
				print_error("BOSS 挂机配置有问题：", v)
			end

			break
		end
	end
end

function BaseSceneLogic:GetGuaJiInfo()
	return self.guaji_pos_info
end

function BaseSceneLogic:ResetCheckGuaJiPosTime()

end

function BaseSceneLogic:CheckGuaJiPosMove()
	if self.guaji_pos_info == nil then
		return
	end

	if self.check_guaji_pos_time > Status.NowTime then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= self.guaji_pos_info.scene_type then
		self:ClearGuaJiInfo()
		return
	end

	if GuajiCache.guaji_type == GuajiType.Temporary then
		return
	end

	if GuajiCache.guaji_type == GuajiType.None and self.track_role_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end

	if GuajiWGCtrl.Instance:IsInBossDeadTime() then
		return
	end

	local pick_x, pick_y = GuajiWGCtrl.Instance:GetPickPos()
	if pick_x ~= 0 or pick_y ~= 0 then
		return
	end

	self.check_guaji_pos_time = Status.NowTime + 2
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role:IsTeamFollowState() then
		return
	end

	if main_role ~= nil and not main_role:IsDeleted()
		and not (main_role:IsRealDead() or main_role:IsDead() or main_role:HasCantMoveBuff() or main_role:HasCantMoveBuffButCanShowMove()) then
		local pos_x, pos_y = main_role:GetLogicPos()
		local limit = GameMath.GetDistance(self.guaji_pos_info.x, self.guaji_pos_info.y, pos_x, pos_y, false) >= self.guaji_pos_info.run_range * self.guaji_pos_info.run_range
		if not limit and main_role:IsStand() and not main_role:IsAtkPlaying() and not main_role:IsFollowState() then
			-- 选中的目标是否超出了追击范围
			if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() then
				local target_pos_x, target_pos_y = GuajiCache.target_obj:GetLogicPos()
				limit = GameMath.GetDistance(self.guaji_pos_info.x, self.guaji_pos_info.y, target_pos_x, target_pos_y, false) >= self.guaji_pos_info.run_range * self.guaji_pos_info.run_range
			else
				-- AOI范围内如果不能找到怪，并且人物不在挂机原点，返回挂机原点
				local obj = self:GetGuajiCharacter()
				if obj == nil or obj:IsDeleted() then
					limit = GameMath.GetDistance(self.guaji_pos_info.x, self.guaji_pos_info.y, pos_x, pos_y, false) >= 4
				end
			end
		end

		if limit then
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)

			if self.guaji_pos_info.monster_id then
				MoveCache.end_type = MoveEndType.FightByMonsterId
				GuajiCache.monster_id = self.guaji_pos_info.monster_id
			end

			GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), self.guaji_pos_info.x, self.guaji_pos_info.y, self.guaji_pos_info.range or 1)
		end
	end
end

function BaseSceneLogic:SetTrackRoleData(track_type, target_param)
	self.track_role_type = track_type
	self.track_role_param = target_param
end

function BaseSceneLogic:GetTrackRoleInfo()
	return self.track_role_type, self.track_role_param
end

function BaseSceneLogic:ResetTrackRoleInfo()
	self.track_role_type = nil
	self.track_role_param = nil
end

function BaseSceneLogic:OnlyResetTeamTrackRoleInfo()
	if self.track_role_type ~= nil and OBJ_FOLLOW_TYPE.TEAM then
		self.track_role_type = nil
		self.track_role_param = nil		
	end
end

function BaseSceneLogic:CheckObjIsIgnoreSelect(target_obj, select_type)
	return false
end

function BaseSceneLogic:CheckObjIsIgnoreShowMainSelect(target_obj, select_type)
	return false
end

function BaseSceneLogic:GetGuaJiDir()
	return nil, nil
end

function BaseSceneLogic:ShowFindLately(gather_obj_id)
	return true
end

function BaseSceneLogic:HideMonster(special_param)
	return false
end

function BaseSceneLogic:IsNeedAutoRevenge()
	return false
end

function BaseSceneLogic:IsIgnoreResetGuaJi()
	return false
end

function BaseSceneLogic:IsNowHideRevengePanel()
	return false
end

function BaseSceneLogic:GetCheckLatelyGatherDis()
	return 0
end

function BaseSceneLogic:StopGatherCallBack()
end

function  BaseSceneLogic:ResetRoleAndCameraDir(is_need_cache_recover_data)
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	local fb_angle_cfg = FuBenWGData.Instance:GetFbCamearCfg(scene_id, scene_type)
	if fb_angle_cfg and fb_angle_cfg.rotation_x and fb_angle_cfg.rotation_y and not IsNil(MainCameraFollow) then
		local dis = nil
		if fb_angle_cfg.angle_distance ~= "" then
			dis = tonumber(fb_angle_cfg.angle_distance)
		end

		self:FlushMainRoleDefaultDir()
		local param_t = {scene_id = scene_id, is_recover = fb_angle_cfg.is_recover, is_need_cache_recover_data = is_need_cache_recover_data and 1 or 0}
		local camera_type = ADJUST_CAMERA_TYPE.CAN_CHANGE
		if scene_type == SceneType.EquipFb then
			camera_type = ADJUST_CAMERA_TYPE.FUNBEN
		end
		
		Scene.Instance:ChangeCamera(camera_type, fb_angle_cfg.rotation_x, fb_angle_cfg.rotation_y, dis, param_t)
	end
end

function BaseSceneLogic:FlushMainRoleDefaultDir()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()

	local fb_angle = FuBenWGData.Instance:GetFbCamearCfg(scene_id, scene_type)
	if fb_angle and fb_angle.rotation_x and fb_angle.rotation_y and not IsNil(MainCameraFollow) then
		if fb_angle.pos_x and fb_angle.pos_y then
			if fb_angle.pos_x ~= 0 and fb_angle.pos_y ~= 0 then
				local main_role = Scene.Instance:GetMainRole()
				if main_role then
					main_role:SetRotation(Vector3.zero)
					main_role:SetDirectionByXY(fb_angle.pos_x, fb_angle.pos_y, nil, true, 200)
				end
			end
		end
	end
end