ControlBeastsDesposeView = ControlBeastsDesposeView or BaseClass(SafeBaseView)

function ControlBeastsDesposeView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(816, 590)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_despose")
end

function ControlBeastsDesposeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName10

    if not self.beasts_despose_bag_grid then
        self.beasts_despose_bag_grid = AsyncBaseGrid.New()
        self.beasts_despose_bag_grid:CreateCells({col = 5, 
            cell_count = BEAST_DEFINE.BEAST_BORN_COUNT_MAX, 
            list_view = self.node_list["beasts_despose_bag_grid"], 
            change_cells_num = 1,
            itemRender = BeastsDesposeItem,
            assetBundle = "uis/view/control_beasts_ui_prefab",
            assetName = "layout_beasts_despose_bag_item",
        })
        self.beasts_despose_bag_grid:SetStartZeroIndex(false)
        self.beasts_despose_bag_grid:SetIsMultiSelect(true)       
        self.beasts_despose_bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectBeastDesposeCellCallBack, self))
	end

    -- 三份列表
    if not self.beasts_despose_culture_reward then
        self.beasts_despose_culture_reward = BeastsOperateDeposeRewardRender.New(self.node_list.beasts_despose_culture_reward)
    end

    if not self.beasts_despose_exp_reward then
        self.beasts_despose_exp_reward = BeastsOperateDeposeRewardRender.New(self.node_list.beasts_despose_exp_reward)
    end

    if not self.beasts_despose_other_reward then
        self.beasts_despose_other_reward = BeastsOperateDeposeRewardRender.New(self.node_list.beasts_despose_other_reward)
    end

    if not self.select_type_list then
		self.select_type_list = {}
			
		for i = 1, 3 do
			local incubate_obj = self.node_list.select_type_list:FindObj(string.format("select_button_%d", i))
			if incubate_obj then
				local cell = BeastsSelectTypeItemRender.New(incubate_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.SelectTypeCallBack, self))
				self.select_type_list[i] = cell
			end
		end
	end

	-- 品质选择查看列表
	if not self.color_list then
		self.color_list = AsyncListView.New(BeastsDesposeColorSelectItemRender, self.node_list.color_list)
		self.color_list:SetSelectCallBack(BindTool.Bind(self.ColorSelectCallBack, self))
        self.color_list:SetStartZeroIndex(true)
	end

    -- 星级选择查看列表
    if not self.star_list then
        self.star_list = AsyncListView.New(BeastsDesposeStarSelectItemRender, self.node_list.star_list)
		self.star_list:SetSelectCallBack(BindTool.Bind(self.StarSelectCallBack, self))
        self.star_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.beasts_despose_operate, BindTool.Bind2(self.BeastsDespose, self))
    XUI.AddClickEventListener(self.node_list.btn_select_color, BindTool.Bind2(self.ClickBtnSelectColor, self))
    XUI.AddClickEventListener(self.node_list.btn_select_star, BindTool.Bind2(self.ClickBtnSelectStar, self))
    XUI.AddClickEventListener(self.node_list.close_color_list_part, BindTool.Bind2(self.ClickCloseColorList, self))
    XUI.AddClickEventListener(self.node_list.close_star_list_part, BindTool.Bind2(self.ClickCloseStarList, self))
end

function ControlBeastsDesposeView:SelectBeastDesposeCellCallBack(depose_cell)
    self:FlusBeastDesposeOperateAllMessage()
end

-- 已孵化背包类型点击
function ControlBeastsDesposeView:SelectTypeCallBack(beast_type_cell)
	if self.beast_descompose_select_type_index == beast_type_cell.index then
		return
	end

	self.beast_descompose_select_type_index = beast_type_cell.index
    self:FlusBeastDesposeGrid()
end

-- 品质选择查看列表
function ControlBeastsDesposeView:ColorSelectCallBack(color_item, cell_index, is_default, is_click)
	if (not color_item) or (not color_item.data) then
		return
	end

	if self.color_select_index == cell_index then
		return
	end

	self.color_select_index = cell_index
    self.color_status = false
    self:FushSelectStatus()
	self:FlusBeastDesposeGrid()
end

-- 星级选择查看列表
function ControlBeastsDesposeView:StarSelectCallBack(star_item, cell_index, is_default, is_click)
	if (not star_item) or (not star_item.data) then
		return
	end

	if self.star_select_index == cell_index then
		return
	end

	self.star_select_index = cell_index
    self.star_status = false
    self:FushSelectStatus()
	self:FlusBeastDesposeGrid()
end

function ControlBeastsDesposeView:ReleaseCallBack()
    if self.beasts_despose_bag_grid then
        self.beasts_despose_bag_grid:DeleteMe()
        self.beasts_despose_bag_grid = nil
    end

    if self.beasts_despose_culture_reward then
        self.beasts_despose_culture_reward:DeleteMe()
        self.beasts_despose_culture_reward = nil
    end

    if self.beasts_despose_exp_reward then
        self.beasts_despose_exp_reward:DeleteMe()
        self.beasts_despose_exp_reward = nil
    end

    if self.beasts_despose_other_reward then
        self.beasts_despose_other_reward:DeleteMe()
        self.beasts_despose_other_reward = nil
    end

	if self.select_type_list and #self.select_type_list > 0 then
		for i = 1, 3 do
			if self.select_type_list[i] then
				self.select_type_list[i]:DeleteMe()
			end
		end

		self.select_type_list = nil
	end

    if self.color_list then
        self.color_list:DeleteMe()
        self.color_list = nil
    end

    if self.star_list then
        self.star_list:DeleteMe()
        self.star_list = nil
    end

    self.color_select_index = nil
	self.star_select_index = nil
    self.color_status = nil
    self.star_status = nil

    self:ResetAllDesposeItemList()
end

-- 重置三份材料列表
function ControlBeastsDesposeView:ResetAllDesposeItemList()
    self.beasts_despose_culture_list = nil
    self.beasts_despose_exp_list = nil
    self.beasts_despose_other_list = nil
end

-- 初始化三份材料列表
function ControlBeastsDesposeView:InitAllDesposeItemList()
    if not self.beasts_despose_culture_list then
        self.beasts_despose_culture_list = {}
    end

    if not self.beasts_despose_exp_list then
        self.beasts_despose_exp_list = {}
    end

    if not self.beasts_despose_other_list then
        self.beasts_despose_other_list = {}
    end
end

function ControlBeastsDesposeView:OnFlush(param_t)
    self.color_select_index = self.color_select_index or 0
	self.star_select_index = self.star_select_index or 0
    self.color_status = self.color_status or false
    self.star_status = self.star_status or false

    self.color_list:SetDataList(Language.ContralBeasts.PrizeDrawAutoDesposeColorNmae)
    self.star_list:SetDataList(Language.ContralBeasts.DesposeStarList)
    self:FushSelectStatus()
    self:FlusBeastDesposeGrid()
end

-- 刷新选中状态
function ControlBeastsDesposeView:FushSelectStatus()
    local cur_color_str = Language.ContralBeasts.PrizeDrawAutoDesposeColorNmae[self.color_select_index]
    local cur_star_str = Language.ContralBeasts.DesposeStarList[self.star_select_index]
    self.node_list.cur_color_text.text.text = ToColorStr(cur_color_str, ITEM_COLOR[self.color_select_index]) 
    self.node_list.cur_star_text.text.text = cur_star_str

    self.node_list.color_arrow_down:CustomSetActive(self.color_status)
    self.node_list.color_arrow_up:CustomSetActive(not self.color_status)
    self.node_list.color_list_part:CustomSetActive(self.color_status)

    self.node_list.star_arrow_down:CustomSetActive(self.star_status)
    self.node_list.star_arrow_up:CustomSetActive(not self.star_status)
    self.node_list.star_list_part:CustomSetActive(self.star_status)
end

-- 设置背包数据
function ControlBeastsDesposeView:FlusBeastDesposeGrid()
    if self.beast_descompose_select_type_index == nil then
        self.beast_descompose_select_type_index = 1
    end

	for index = 1, 3 do
		if self.select_type_list[index] then
			self.select_type_list[index]:FlushSelectHl(self.beast_descompose_select_type_index == index)
		end
	end

    local is_reset = self.beast_descompose_select_type_index == 1  	--是否为重置列表（培养过的可以重置）
    local is_despose = self.beast_descompose_select_type_index == 2   	--是否为分解列表（需要限制未培养过的品质颜色，sr及以下的才可以分解 color<=3）
    local is_back = self.beast_descompose_select_type_index == 3   	    --是否为回退列表（需要限制未培养过的已升过星红色的幻兽 星级 >=5 星）

    self.beasts_despose_bag_grid:CancleAllSelectCell()
	local grid_list = ControlBeastsWGData.Instance:GetCultureBeastsList2(is_reset, is_despose, is_back, true)

    local aim_table = {}
	for i, beasts_data in ipairs(grid_list) do
		local server_data = beasts_data.server_data
        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
        local beast_color = beast_cfg.beast_color or 0
        local beast_star = beast_cfg.beast_star or 0

        if self.star_select_index == 0 and self.color_select_index == 0 then
            table.insert(aim_table, beasts_data)
        else
            if (beast_color == self.color_select_index or self.color_select_index == 0) 
            and (beast_star == self.star_select_index or self.star_select_index == 0) then
                table.insert(aim_table, beasts_data)
            end
        end
	end

    local select_num = 0
    if self.beasts_despose_bag_grid then
		self.beasts_despose_bag_grid:SetDataList(aim_table)

        if self.star_select_index ~= 0 or self.color_select_index ~= 0 then
            for i, v in ipairs(aim_table) do
                self.beasts_despose_bag_grid.select_tab[1][i] = true --选中之前选择的
                select_num = select_num + 1

                if select_num >= 35 then
                    break
                end
            end
        end
	end

    local btn_str = nil
    local no_data_str = nil
    if is_reset then
        btn_str = Language.ContralBeasts.DeposeResetTips
        no_data_str = Language.ContralBeasts.DeposeResetTips2
    elseif is_despose then
        btn_str = Language.ContralBeasts.DeposeBreakTips
        no_data_str = Language.ContralBeasts.DeposeBreakTips2
    else
        btn_str = Language.ContralBeasts.DeposeBackTips
        no_data_str = Language.ContralBeasts.DeposeBackTips2
    end

    self.node_list.btn_despose_operate_tex.text.text = btn_str
    self.node_list.beasts_despose_no_data_despose.text.text = no_data_str
    self:FlusBeastDesposeOperateAllMessage()
end

-- 刷新分解操作按钮
function ControlBeastsDesposeView:FlusBeastDesposeOperateAllMessage()
    self:FlushBeastDesposeAllList()
end

-- 获取当前的展示的物品
function ControlBeastsDesposeView:FlushBeastDesposeAllList()
    local beasts_despose_culture_list = {}
    local beasts_despose_exp_list = {}
    local beasts_despose_other_list = {}
    beasts_despose_culture_list.reward_item = {}
    beasts_despose_exp_list.reward_item = {}
    beasts_despose_other_list.reward_item = {}
    local is_reset = self.beast_descompose_select_type_index == 1  	--是否为重置列表（培养过的可以重置）
    local is_despose = self.beast_descompose_select_type_index == 2   	--是否为分解列表（需要限制未培养过的品质颜色，sr及以下的才可以分解 color<=3）
    local is_back = self.beast_descompose_select_type_index == 3   	    --是否为回退列表（需要限制未培养过的已升过星红色的幻兽 星级 >=7 星）

    self:ResetAllDesposeItemList()
    self:InitAllDesposeItemList()
    local need_gold = 0

    local cell_datas = self.beasts_despose_bag_grid:GetAllSelectCell()
    self.node_list.beasts_despose_have_data:CustomSetActive(#cell_datas > 0)
    self.node_list.beasts_despose_no_data:CustomSetActive(#cell_datas <= 0)

    if #cell_datas <= 0 then
        self.beasts_despose_culture_reward:SetData(beasts_despose_culture_list)
        self.beasts_despose_exp_reward:SetData(beasts_despose_exp_list)
        self.beasts_despose_other_reward:SetData(beasts_despose_other_list)
        return 
    end

    for i, data in ipairs(cell_datas) do
        if data then
            self:SplitAllCanGetReward(data, is_reset, is_despose, is_back)
        end
    end

    local get_item_list = function(aim_list, list)
        for _, item_data in pairs(list) do
            if item_data and item_data.item_id then
                table.insert(aim_list, item_data)
            end
        end
    end

    get_item_list(beasts_despose_culture_list.reward_item, self.beasts_despose_culture_list)
    get_item_list(beasts_despose_exp_list.reward_item, self.beasts_despose_exp_list)
    get_item_list(beasts_despose_other_list.reward_item, self.beasts_despose_other_list)
    self.beasts_despose_culture_reward:SetData(beasts_despose_culture_list)
    self.beasts_despose_exp_reward:SetData(beasts_despose_exp_list)
    self.beasts_despose_other_reward:SetData(beasts_despose_other_list)
end

-- 切割数据
function ControlBeastsDesposeView:SplitAllCanGetReward(beast_data, is_reset, is_despose, is_back)
    if (not beast_data) or (not beast_data.server_data) then
        return false
    end

    local server_data = beast_data.server_data

    local set_list_data = function(list, item_id, num, is_bind)
        if list[item_id] then
            list[item_id].num = list[item_id].num + num
        else
            list[item_id] = {}
            list[item_id].item_id = item_id
            list[item_id].num = num
            list[item_id].is_bind = is_bind
        end
    end

    local set_list_data2 = function(list, item_id, num, is_bind)
        local data = {}
        data.item_id = item_id
        data.num = num
        data.is_bind = is_bind

        table.insert(list, data)
    end

    local cfg = ControlBeastsWGData.Instance:GetBeastDesposeMessageCfgById(server_data.beast_id)
    -- 切割养成材料
    if cfg and is_back then
        if not IsEmptyTable(cfg.beast_vir_item_list) then
            for k, item_data in pairs(cfg.beast_vir_item_list) do
                set_list_data2(self.beasts_despose_culture_list, item_data.item_id, item_data.num, item_data.is_bind)
            end
        end

        if not IsEmptyTable(cfg.back_item) then
            for k, item_data in pairs(cfg.back_item) do
                set_list_data2(self.beasts_despose_culture_list, item_data.item_id, item_data.num, item_data.is_bind)
            end
        end
    end

    -- 切割经验材料
    if cfg then
        if is_despose then
            if not IsEmptyTable(cfg.break_exp) then
                for _, item_data in pairs(cfg.break_exp) do
                    set_list_data(self.beasts_despose_exp_list, item_data.item_id, item_data.num, item_data.is_bind)
                end
            end
        elseif is_reset then
            local beast_level = server_data.beast_level
            local num = 0
            if beast_level <= 1 then
                num = server_data.exp
            else
                for i = 1, beast_level - 1 do
                    local lv_cfg = ControlBeastsWGData.Instance:GetBeastLevelCfgByLevel(i)
                    local lv_need_exp = lv_cfg and lv_cfg.need_exp or 0
                    num = num + lv_need_exp
                end

                num = num + server_data.exp
            end

            if num ~= 0 then
                set_list_data(self.beasts_despose_exp_list, COMMON_CONSTS.VIRTUAL_ITEM_BEAST_EXP, num, 1)
            end
        end
    end

    -- 分解直接拿取对应的列表
    if cfg and is_back then
        if cfg.back_beast ~= nil and cfg.back_beast ~= "" and cfg.back_beast > 0 then
            set_list_data(self.beasts_despose_other_list, cfg.back_beast, 1, 0)
        end
    end

    local refine_level = server_data.refine_level or 0
    local refine_item_num = server_data.refine_item_num or 0

    if refine_item_num ~= 0 and is_back then
        local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
        local refine_seq = best_base_cfg and best_base_cfg.refine_seq or -1
        local beast_star = best_base_cfg and best_base_cfg.beast_star or 6
        local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, beast_star)
        local consume_item_id = weight_cfg and weight_cfg.cost_item_id or 0

        set_list_data(self.beasts_despose_culture_list, consume_item_id, refine_item_num, true)
    end
end
--------------------------------------------------------------------------------------------------
--- 分解或者重置
function ControlBeastsDesposeView:BeastsDespose()
    local cell_datas = self.beasts_despose_bag_grid:GetAllSelectCell()
    local bag_id_list = {}

    if #cell_datas > 35 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip18)
        return
    end

    if #cell_datas >= 1 then
        for _, cell_data in ipairs(cell_datas) do
            local server_bag_id = ControlBeastsWGCtrl.Instance:GetServerBagId(cell_data.bag_id)
            table.insert(bag_id_list, server_bag_id)
        end
    end

    self.beasts_despose_bag_grid:CancleAllSelectCell()

    if not self.beast_descompose_select_type_index then
        return
    end

    local is_reset = self.beast_descompose_select_type_index == 1  	    --是否为重置列表（培养过的可以重置）
    local is_despose = self.beast_descompose_select_type_index == 2   	--是否为分解列表（需要限制未培养过的品质颜色，sr及以下的才可以分解 color<=3）
    local is_back = self.beast_descompose_select_type_index == 3   	    --是否为回退列表（需要限制未培养过的已升过星红色的幻兽 星级 >=7

    if is_reset then
        ControlBeastsWGCtrl.Instance:SendCSBeastResetListReq(bag_id_list)
    elseif is_despose then
        ControlBeastsWGCtrl.Instance:SendCSBeastBreakListReq(bag_id_list)
    elseif is_back then   
        ControlBeastsWGCtrl.Instance:SendCSBeastBackListReq(bag_id_list)
    end
end

-- 品质选择按钮点击
function ControlBeastsDesposeView:ClickBtnSelectColor()
    self.color_status = true
    self:FushSelectStatus()
end

-- 星级选择按钮点击
function ControlBeastsDesposeView:ClickBtnSelectStar()
    self.star_status = true
    self:FushSelectStatus()
end

-- 关闭品质选择框
function ControlBeastsDesposeView:ClickCloseColorList()
    self.color_status = false
    self:FushSelectStatus()
end

-- 关闭星级选择框
function ControlBeastsDesposeView:ClickCloseStarList()
    self.star_status = false
    self:FushSelectStatus()
end
---------------------------------------------------------------------------------------------------
----------奖励-------------
BeastsOperateDeposeRewardRender = BeastsOperateDeposeRewardRender or BaseClass(BaseRender)
function BeastsOperateDeposeRewardRender:__init()
    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    end
end

function BeastsOperateDeposeRewardRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function BeastsOperateDeposeRewardRender:OnFlush()
    if not self.data then
        return 
    end

    self.item_list:SetDataList(self.data.reward_item)
end

-----------------------------------------------------------------------------------
BeastsDesposeItem = BeastsDesposeItem or BaseClass(BaseRender)
function BeastsDesposeItem:LoadCallBack()
	if not self.beast_item then
        self.beast_item = ItemCell.New(self.node_list.item_pos)
		self.beast_item:SetIsShowTips(false)
		self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
        self.beast_item:UseNewSelectEffect(true)
    end
end

function BeastsDesposeItem:OnClick()
    self.beast_item:UseNewSelectEffect(true)
	BaseRender.OnClick(self)
end

--删除写在ReleaseCallBack里的东西
function BeastsDesposeItem:ReleaseCallBack()
    if self.beast_item then
        self.beast_item:DeleteMe()
        self.beast_item = nil
    end
end

function BeastsDesposeItem:OnFlush()
    if IsEmptyTable(self.data) then
		return
    end

	self.beast_item:SetData(self.data)

    if not self.data.is_egg and self.data then
        local server_data = self.data.server_data
        if server_data then
            self.beast_item:SetBeastLevel(server_data.beast_level)
        end
    end
end

function BeastsDesposeItem:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return 
    end
    self.beast_item:UseNewSelectEffect(true)
	self.beast_item:SetSelectEffect(is_select)	
end

--------------------------------背包幻兽类型item-----------------------
BeastsSelectTypeItemRender = BeastsSelectTypeItemRender or BaseClass(BaseRender)

-- 刷新选中状态
function BeastsSelectTypeItemRender:FlushSelectHl(is_select)
    self.node_list.Image:CustomSetActive(not is_select)
    self.node_list.HLImage:CustomSetActive(is_select)
end

-----------------------------------------------------------------------------------
BeastsDesposeColorSelectItemRender = BeastsDesposeColorSelectItemRender or BaseClass(BaseRender)
function BeastsDesposeColorSelectItemRender:OnFlush()
    if not self.data then
		return
    end

    self.node_list.part_item_name.text.text = ToColorStr(self.data, ITEM_COLOR[self.index]) 
end

-----------------------------------------------------------------------------------
BeastsDesposeStarSelectItemRender = BeastsDesposeStarSelectItemRender or BaseClass(BaseRender)
function BeastsDesposeStarSelectItemRender:OnFlush()
    if not self.data then
		return
    end

    self.node_list.part_item_name.text.text = self.data
end