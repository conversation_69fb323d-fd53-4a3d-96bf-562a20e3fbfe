SystemForceLongShenView = SystemForceLongShenView or BaseClass(SafeBaseView)

local COLOR_LIST = {[1] = COLOR3B.C4, [2] = COLOR3B.C7}

function SystemForceLongShenView:__init()
	self.view_style = ViewStyle.Half
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/system_force_ui_prefab", "layout_system_force_longshen")

	self.cur_select_seq = 0 -- 选择的功能seq
	self.select_tog_index = 1
end

function SystemForceLongShenView:OpenCallBack()
	--SystemForceWGCtrl.Instance:SendCSSystemForceRequest(SYSTEM_FORCESHOW_OPERATE_TYPE.ALL_INFO)
end

function SystemForceLongShenView:LoadCallBack()
	self.is_first_open = false
	self.open_view = false

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	-- if not self.task_list then
    --     self.task_list = AsyncListView.New(ForceLongShenTaskCell, self.node_list.task_list)
    -- end

	if not self.task_list then
        self.task_list = {}
        for i = 1, 4 do
            self.task_list[i] = ForceLongShenTaskCell.New(self.node_list["task_item" .. i])
			self.task_list[i]:SetIndex(i)
        end
    end

	if not self.page_toggle_list then
		self.page_toggle_list = {}
		local node_num = self.node_list.adnt_page_point_root.transform.childCount
		for i = 1, node_num do
			self.page_toggle_list[i] = self.node_list.adnt_page_point_root:FindObj("page_point_" .. i)
		end

		-- self.cur_select_seq, self.select_tog_index = SystemForceWGData.Instance:GetDefaultShowIndex()
		-- self.page_toggle_list[self.select_tog_index].toggle.isOn = true
	end

	if not self.role_display then
        self.role_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["role_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.role_display:SetRenderTexUI3DModel(display_data)
        -- self.role_display:SetUI3DModel(self.node_list["role_root"].transform, self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_display)
	end

	if self.all_reward_list == nil then
        self.all_reward_list = {}
        for i = 1, 4 do
            self.all_reward_list[i] = ItemCell.New(self.node_list["chapter_show_item" .. i])
            self.all_reward_list[i]:SetIndex(i)
        end
		self.all_reward_list[1]:SetTipClickCallBack(BindTool.Bind(self.OnClickGetChapter, self))
		self.all_reward_list[1]:SetIsShowTips(false)
    end

	if not self.fz_display then
        self.fz_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["fazhan_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.fz_display:SetRenderTexUI3DModel(display_data)
        -- self.fz_display:SetUI3DModel(self.node_list["fazhan_root"].transform, self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.fz_display)
	end

	if self.toggle_list == nil then
        self.toggle_list = AsyncListView.New(SystemForceToggleRender, self.node_list.toggle_list)
        self.toggle_list:SetSelectCallBack(BindTool.Bind(self.OnClickToggle, self))
    end

	XUI.AddClickEventListener(self.node_list.chapter_get_btn, BindTool.Bind(self.OnClickGetChapter, self))
	XUI.AddClickEventListener(self.node_list.right_btn, BindTool.Bind(self.ChangeSelectPanelIndex, self, true))
	XUI.AddClickEventListener(self.node_list.left_btn, BindTool.Bind(self.ChangeSelectPanelIndex, self, false))
	XUI.AddClickEventListener(self.node_list.btn_to_compose, BindTool.Bind(self.OnClickToCompose, self, false))
end

function SystemForceLongShenView:ReleaseCallBack()
	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

 	-- if self.task_list then
    --     self.task_list:DeleteMe()
    --     self.task_list = nil
    -- end

	if self.role_display then
        self.role_display:DeleteMe()
        self.role_display = nil
	end

	if self.all_reward_list then
		for k, v in pairs(self.all_reward_list) do
            v:DeleteMe()
        end
        self.all_reward_list = nil
	end

	if self.fz_display then
        self.fz_display:DeleteMe()
        self.fz_display = nil
	end

	if self.toggle_list then
        self.toggle_list:DeleteMe()
        self.toggle_list = nil
    end

	if self.task_list then
        for k,v in pairs(self.task_list) do
            v:DeleteMe()
        end
		self.task_list = nil
	end

   	if CountDownManager.Instance:HasCountDown("force_longshen_time") then
        CountDownManager.Instance:RemoveCountDown("force_longshen_time")
    end

	self.page_toggle_list = nil
end

function SystemForceLongShenView:ShowIndexCallBack()
	self.is_first_open = true
	self:InitPanelState()
	self:PlayAnim(self.node_list.task_list)
end

function SystemForceLongShenView:InitPanelState()
	local ui_type = SystemForceWGData.Instance:GetUITypeBySeq(self.cur_select_seq)

	local chapter_bg_bundle, chapter_bg_asset = ResPath.GetF2RawImagesPNG("a3_xtyg_di_" .. ui_type)
	self.node_list.chapter_bg.raw_image:LoadSprite(chapter_bg_bundle, chapter_bg_asset, function ()
		self.node_list.chapter_bg.raw_image:SetNativeSize()
	end)

	-- local chapter_bom_bg_bundle, chapter_bom_bg_asset = ResPath.GetF2RawImagesPNG("a2_hdyd_ht" .. ui_type)
	-- self.node_list.chapter_bom_bg.raw_image:LoadSprite(chapter_bom_bg_bundle, chapter_bom_bg_asset, function ()
	-- 	self.node_list.chapter_bom_bg.raw_image:SetNativeSize()
	-- end)
	
	local title_bundle, title_asset = ResPath.GetF2RawImagesPNG("a3_xtyg_slfc_" .. ui_type)
	self.node_list.title.raw_image:LoadSprite(title_bundle, title_asset, function ()
		self.node_list.title.raw_image:SetNativeSize()
	end)

	local desc_bg_bundle, desc_bg_asset = ResPath.GetNoPackPNG("a3_xtyg_dk_" .. ui_type)
	self.node_list.desc_bg.image:LoadSprite(desc_bg_bundle, desc_bg_asset)

	self.node_list.desc_get_tip.text.color = Str2C3b(COLOR_LIST[ui_type])
	-- local close_bundle, close_asset = ResPath.GetSyatemForcePNG("a2_hdyd_close" .. ui_type)
	-- self.node_list.btn_close_window.image:LoadSprite(close_bundle, close_asset, function ()
	-- 	self.node_list.btn_close_window.image:SetNativeSize()
	-- end)
	
	-- local box_bundle, box_asset = ResPath.GetSyatemForcePNG("a2_hdyd_bxx" .. ui_type)
	-- self.node_list.chapter_get_btn.image:LoadSprite(box_bundle, box_asset, function ()
	-- 	self.node_list.chapter_get_btn.image:SetNativeSize()
	-- end)

	--self:FlushEndTime()
end

function SystemForceLongShenView:FlushEndTime()
	local cur_cfg = SystemForceWGData.Instance:GetForceshowCfgBySeq(self.cur_select_seq)

	if IsEmptyTable(cur_cfg) then
		if CountDownManager.Instance:HasCountDown("force_longshen_time") then
			CountDownManager.Instance:RemoveCountDown("force_longshen_time")
		end
		
		self.node_list.totay_time:CustomSetActive(false)
		self.node_list.today_time_down.text.text = ""
		return
	end

	local close_day = cur_cfg.close_day
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	time = time + (close_day - cur_day) * 24 * 3600
	self.node_list.today_time_down.text.text = ToColorStr(TimeUtil.FormatSecondDHM8(time), COLOR3B.GREEN)

	if close_day >= cur_day and time > 0 then
		self.node_list.totay_time:CustomSetActive(true)
		if CountDownManager.Instance:HasCountDown("force_longshen_time") then
			CountDownManager.Instance:RemoveCountDown("force_longshen_time")
		end

		CountDownManager.Instance:AddCountDown("force_longshen_time",
		function (now_time, total_time)
			if self.node_list.today_time_down then
				local time = math.ceil(total_time - now_time)
				self.node_list.today_time_down.text.text = ToColorStr(TimeUtil.FormatSecondDHM8(time), COLOR3B.GREEN)
			end
		end,
		function ()
			if self.node_list.today_time_down then
				self.node_list.today_time_down.text.text = ""
			end
		end,
		nil, time, 1)
	else
		self.node_list.totay_time:CustomSetActive(false)
	end
end

function SystemForceLongShenView:OnFlush(param_t)
	local cur_cfg = SystemForceWGData.Instance:GetForceshowCfgBySeq(self.cur_select_seq)

	if IsEmptyTable(cur_cfg) then
		return
	end

	local data_list = SystemForceWGData.Instance:GetShowActDataList()
	-- for k, v in pairs(self.page_toggle_list) do
	-- 	v:CustomSetActive(k <= page_num)
	-- end
	self.toggle_list:SetDataList(data_list)
	for k, v in pairs(param_t) do
        if k == "jump_index" then
            if v.jump_index then
				self.toggle_list:JumpToIndex(v.jump_index)
                self.is_first_open = false
            end
        end
    end

	if self.is_first_open then
		self.is_first_open = false
		local jump_index = SystemForceWGData.Instance:GetDefaultShowIndex()
		self.toggle_list:JumpToIndex(jump_index)
	end

	self.node_list.desc_get_tip.text.text = cur_cfg.desc or ""
	-- 获取左右按钮 显示状态 及红点信息
	-- local left_active, left_remind = SystemForceWGData.Instance:GetLeftBtnState(self.cur_select_seq)
	-- self.node_list.left_btn:CustomSetActive(left_active)
	-- self.node_list.left_btn_remind:CustomSetActive(left_remind)

	-- local right_active, right_remind = SystemForceWGData.Instance:GetRightBtnState(self.cur_select_seq)
	-- self.node_list.right_btn:CustomSetActive(right_active)
	-- self.node_list.right_btn_remind:CustomSetActive(right_remind)

	--刷新任务面板
	local info = SystemForceWGData.Instance:GetSystemForceInfoBySeq(self.cur_select_seq)
	if IsEmptyTable(info) then
		return
	end

	local cur_chapter = SystemForceWGData.Instance:GetCurrentChapter(self.cur_select_seq)
	local chapter_cfg = SystemForceWGData.Instance:GetCurSeqChapterCfg(self.cur_select_seq, cur_chapter)

	if chapter_cfg then
		for k, v in pairs(self.all_reward_list) do
			v:SetData(chapter_cfg.reward_item[k - 1])
		end
		self:FlushModel(chapter_cfg)
		local task_info = SystemForceWGData.Instance:GetSortTaskData(self.cur_select_seq, cur_chapter)
		local chapter_flag = info.chapter_reward_flag[cur_chapter] == 1
		self.node_list.name.text.text = chapter_cfg.model_name

		if task_info then
			local complete_task_num = 0

			for k, v in pairs(task_info) do
				if v.status == SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH then
					complete_task_num = complete_task_num + 1
				end
			end

			--self.task_list:SetDataList(task_info)

			for i = 1, 4 do
				if i <= #task_info then
					self.node_list["task_item" .. i]:SetActive(true)
					self.task_list[i]:SetData(task_info[i])
				else
					self.node_list["task_item" .. i]:SetActive(false)
				end
			end

			local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local desc_chapter_progress = ""
			if not chapter_flag and  cur_day < cur_cfg.open_day + 1 and complete_task_num >= #task_info then
				desc_chapter_progress = Language.SystemForceLongShen.NextDayCanGet
			else
				local color = complete_task_num >= #task_info and COLOR3B.GREEN or COLOR3B.RED
				desc_chapter_progress = string.format(Language.SystemForceLongShen.TaskProgress, color, complete_task_num, #task_info)
			end

			self.node_list.desc_chapter_progress.text.text = desc_chapter_progress
			--self.node_list.desc_get_tip.text.text = string.format(Language.SystemForceLongShen.DescGetReward, cur_cfg.close_day)
		end

		--章节从0开始配的  做显示都加个1
		-- self.node_list.chapter_pro.text.text = string.format(Language.SystemForceLongShen.ChapterPro, cur_chapter + 1, max_chapter + 1)
		local chapter_state = SystemForceWGData.Instance:GetCurrentChapterState(self.cur_select_seq, cur_chapter)
		self.node_list.chapter_get_btn:CustomSetActive(chapter_state)
		self.node_list.chapter_get_btn_remind:CustomSetActive(chapter_state)
		self.node_list.chapter_has_get_flag:CustomSetActive(chapter_flag)
	end

end

function SystemForceLongShenView:FlushModel(chapter_data)
	local is_show_fazhen = chapter_data.if_showHalo == 1
	self.node_list.display_root:CustomSetActive(not is_show_fazhen)
	self.node_list.role_root:CustomSetActive(is_show_fazhen)
	self.node_list.fazhan_root:CustomSetActive(is_show_fazhen)
	self.node_list.EventTriggerListener:CustomSetActive(is_show_fazhen)

	if is_show_fazhen then
		self.role_display:RemoveAllModel()
		self.fz_display:RemoveAllModel()
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		self.role_display:SetModelResInfo(role_vo, nil, function()
			self.role_display:PlayRoleShowAction()
		end)
		self.role_display:FixToOrthographic(self.root_node_transform)
	
		local bundle, asset = ResPath.GetSkillFaZhenModel(chapter_data.type)
		self.fz_display:SetMainAsset(bundle, asset)
	else
		local display_data = {}
		if chapter_data["model_show_itemid" .. 1] ~= 0 and chapter_data["model_show_itemid" .. 1] ~= "" then
			local split_list = string.split(chapter_data["model_show_itemid" .. 1], "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = chapter_data["model_show_itemid" .. 1]
			end
		end

		display_data.should_ani = true
		display_data.bundle_name = chapter_data["model_bundle_name" .. 1]
		display_data.asset_name = chapter_data["model_asset_name" .. 1]
		local model_show_type = tonumber(chapter_data["model_show_type" .. 1]) or 1
		display_data.render_type = model_show_type - 1
		-- if chapter_data["model_show_itemid" .. 1] ~= "" then
		-- 	display_data.model_click_func = function ()
		-- 		TipWGCtrl.Instance:OpenItem({item_id = chapter_data["model_show_itemid" .. 1]})
		-- 	end
		-- end
		if chapter_data.rotation and chapter_data.rotation ~= "" then
			local rotation_tab = string.split(chapter_data.rotation,"|")
			display_data.model_adjust_root_local_rotation = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		end

		if chapter_data.display_scale and chapter_data.display_scale ~= "" then
			display_data.model_adjust_root_local_scale = chapter_data.display_scale
		end

		local pos_x, pos_y, pos_z = 0, 0, 0
		if chapter_data.display_pos and chapter_data.display_pos ~= "" then
			local pos_list = string.split(chapter_data.display_pos, "|")
			pos_x = tonumber(pos_list[1]) or pos_x
			pos_y = tonumber(pos_list[2]) or pos_y
			pos_z = tonumber(pos_list[3]) or pos_z
			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end

		self.model_display:SetData(display_data)
	end
end

function SystemForceLongShenView:OnClickGetChapter()
	local cur_chapter = SystemForceWGData.Instance:GetCurrentChapter(self.cur_select_seq)
	local chapter_state = SystemForceWGData.Instance:GetCurrentChapterState(self.cur_select_seq, cur_chapter)

	if chapter_state then
		self.open_view = true
		SystemForceWGCtrl.Instance:SendCSSystemForceRequest(SYSTEM_FORCESHOW_OPERATE_TYPE.FETCH_CHAPTER_REWARD, self.cur_select_seq, cur_chapter)
	else
		-- local chapter_cfg = SystemForceWGData.Instance:GetCurSeqChapterCfg(self.cur_select_seq, cur_chapter)

		-- if chapter_cfg then
		-- 	local title_reward_item_data = {}
		-- 	title_reward_item_data[1] = {}
		-- 	title_reward_item_data[1].title_text = Language.SystemForceLongShen.TaskRewardDesc
		-- 	title_reward_item_data[1].reward_item_list = ListIndexFromZeroToOne(chapter_cfg.task_reward_item)
		-- 	title_reward_item_data[2] = {}
		-- 	title_reward_item_data[2].title_text = Language.SystemForceLongShen.ChapterRewardDesc
		-- 	title_reward_item_data[2].reward_item_list = ListIndexFromZeroToOne(chapter_cfg.reward_item)

		-- 	local data_list =
		-- 	{
		-- 		view_type = RewardShowViewType.Title,
		-- 		title_reward_item_data = title_reward_item_data
		-- 	}
		-- 	RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
		-- end
	end
end

function SystemForceLongShenView:OnClickToCompose()
	local cur_cfg = SystemForceWGData.Instance:GetForceshowCfgBySeq(self.cur_select_seq)

	if IsEmptyTable(cur_cfg) then
		return
	end

	local item_info = cur_cfg.reward_show
	local item_id = item_info.item_id

	local config = ComposeWGData.Instance:GetComposeCfgByStuffId2(item_id)
	if config then
		local role_level = RoleWGData.Instance.role_vo.level
		if role_level < config.level then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Map.OpenLevel, RoleWGData.GetLevelString(config.level)))
			return
		end

		local index = config.big_type * 10 + config.type
		local remind_tab_list = {big_type = config.big_type, type = config.type, sub_type = config.sub_type, index = config.child_type}
		local jump_index = ComposeWGData.Instance:GetRealJumpIndexByType(remind_tab_list)
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, index, {open_param = config.sub_type, sub_view_name = jump_index})
	end
end

function SystemForceLongShenView:ChangeSelectPanelIndex(is_add)
	local data_list = SystemForceWGData.Instance:GetShowActDataList()
	local change_value = is_add and 1 or -1
	local next_select_tog =  self.select_tog_index + change_value
	local has_data = not IsEmptyTable(data_list[next_select_tog])
	self.select_tog_index = has_data and next_select_tog or self.select_tog_index
	self.cur_select_seq = has_data and data_list[next_select_tog].seq or self.cur_select_seq
	self.page_toggle_list[self.select_tog_index].toggle.isOn = true
	self:InitPanelState()
	self:Flush()
end

function SystemForceLongShenView:OnClickToggle(cell)
    if not cell or not cell.data or self.cur_select_seq == cell.data.seq then
        return
    end

    self.cur_select_seq = cell.data.seq
	self:InitPanelState()
    self:Flush()
	self:PlayAnim(self.node_list.task_list)
end

function SystemForceLongShenView:PlayAnim(node)
	UITween.FakeHideShow(node)
    ReDelayCall(self, function()
		local tween_info = {FromAlpha = 0.1, ToAlpha = 1, StartPosition = Vector3(76, 70, 0), EndPosition = Vector3(76, 30, 0),
		 					AlphaTweenType = DG.Tweening.Ease.Linear, AlphaTweenTime = 0.35, MoveTweenTime = 0.35}
		UITween.MoveAlphaShow(GuideModuleName.SystemForceLongShenView, node, tween_info)
    end, 0.1, "SystemForceLongShenView")
end

-----------------------ForceLongShenCellRender ----------------------
ForceLongShenCellRender = ForceLongShenCellRender or BaseClass(BaseRender)
function ForceLongShenCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_pos)
end

function ForceLongShenCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function ForceLongShenCellRender:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData({item_id = self.data.item_id})
end

--------------------------------任务格子----------------
ForceLongShenTaskCell = ForceLongShenTaskCell or BaseClass(BaseRender)

function ForceLongShenTaskCell:__init()
	-- if not self.reward_list then
	-- 	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	-- 	self.reward_list:SetStartZeroIndex(true)
	-- end

	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.item_cell:UseNewSelectEffect(true)

	self.node_list["go_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetTaskReward,self))
end

function ForceLongShenTaskCell:__delete()
	-- if self.reward_list then
	-- 	self.reward_list:DeleteMe()
	-- 	self.reward_list = nil
	-- end

	if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
     end
end

function ForceLongShenTaskCell:OnFlush()
    if not self.data then
        return
    end

	local ui_type = SystemForceWGData.Instance:GetUITypeBySeq(self.data.seq)
	local ui_index = 1
	if self.index > 2 then
		ui_index = 1
	else
		ui_index = 2
	end

	local bg_bundle, bg_asset = ResPath.GetF2RawImagesPNG("a3_xtyg_dk_task_" .. ui_type .. "_" .. ui_index)
	self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset, function ()
		self.node_list.bg.raw_image:SetNativeSize()
	end)

	local title_bg_bundle, title_bg_asset = ResPath.GetSyatemForcePNG("a3_xtyg_dk_title_" .. ui_type)
	self.node_list.title_bg.image:LoadSprite(title_bg_bundle, title_bg_asset, function ()
		self.node_list.title_bg.image:SetNativeSize()
	end)

	local go_btn_bundle, go_btn_asset = ResPath.GetSyatemForcePNG("a3_xtyg_btn_qw" .. ui_type)
	self.node_list.go_btn.image:LoadSprite(go_btn_bundle, go_btn_asset, function ()
		self.node_list.go_btn.image:SetNativeSize()
	end)

    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = SystemForceWGData.Instance:GetForceshowCfgBySeq(self.data.seq)
	-- local open_day = cfg.open_day
	-- local task_out_time = cur_day ~= open_day
	-- local task_out_time = cur_day > cfg.close_day - 1

	self.node_list.go_btn:SetActive(self.data.status == SYSTEM_FORCESHOW_TASK_STATUS.NONE and self.data.open_panel ~= "")-- and not task_out_time)
    self.node_list.get_btn:SetActive(self.data.status == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH) -- and not task_out_time)
	self.node_list.remind:SetActive(self.data.status == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH) -- and not task_out_time)
    --self.node_list.have_get:SetActive(self.data.status == SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH)
	-- self.node_list.task_out_flag:SetActive(task_out_time and self.data.status ~= SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH)

	self.item_cell:SetFlushCallBack(function ()
		self.item_cell:ResetSelectEffect()
		self.item_cell:SetSelectEffect(self.data.status == SYSTEM_FORCESHOW_TASK_STATUS.HAS_FETCH)
		self.item_cell:SetRedPointEff(self.data.status == SYSTEM_FORCESHOW_TASK_STATUS.CAN_FETCH)
	end)
	self.item_cell:SetData(self.data.reward_item[0])
	--self.reward_list:SetDataList(self.data.reward_item)
	
	self.node_list.title_text.text.text = self.data.task_title

	local count_str = ""
	local color_red = ui_type == 1 and COLOR3B.RED or COLOR3B.C3
	local color
	-- 招财进宝第一档 0 值为 0/0 特殊处理
	if self.data.task_type == 9 and self.data.param1 == 0 then
		local target_value = self.data.status ~= SYSTEM_FORCESHOW_TASK_STATUS.NONE and 1 or 0
		color = self.data.status ~= SYSTEM_FORCESHOW_TASK_STATUS.NONE and COLOR3B.GREEN or color_red
		count_str = ToColorStr(target_value, color)
	else
		color = self.data.progress_num >= self.data.param1 and COLOR3B.GREEN or color_red
		count_str = ToColorStr(self.data.progress_num, color)
	end

	local desc_go_btn = ""
	if self.data.task_type == 10 then
		desc_go_btn = RoleWGData.GetPayMoneyStr(self.data.param1, self.data.param2, self.data.param3)
	else
		desc_go_btn = Language.SystemForceLongShen.DescGoBtn
	end

	self.node_list.desc.text.text = string.format(self.data.task_decs, count_str)
	--self.node_list.desc_go_btn.text.text = desc_go_btn
	--count_str = ToColorStr(count_str, color)
	--self.node_list.desc_task_pro.text.text = count_str

end

function ForceLongShenTaskCell:OnClickGo()
 	if not self.data then
        return
    end

	-- 10直购 其余为跳转
	if self.data.task_type == 10 then
		RechargeWGCtrl.Instance:Recharge(self.data.param1, self.data.param2, self.data.param3)
	else
		if self.data.activity_id ~= "" then
			local is_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.activity_id)

			if not is_open then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
				return
			end
		end

		if self.data.open_panel ~= "" then
			FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
		end
	end
end

function ForceLongShenTaskCell:OnFinishGo()
	if self.data.buy_open_panel ~= "" then
		FunOpen.Instance:OpenViewNameByCfg(self.data.buy_open_panel)
	end
end

function ForceLongShenTaskCell:OnClickGetTaskReward()
	if not self.data then
        return
    end

    SystemForceWGCtrl.Instance:SendCSSystemForceRequest(SYSTEM_FORCESHOW_OPERATE_TYPE.FETCH_TASK_REWARD, self.data.seq, self.data.task_id)
	self:OnFinishGo()
end

SystemForceToggleRender = SystemForceToggleRender or BaseClass(BaseRender)

function SystemForceToggleRender:LoadCallBack()
end

function SystemForceToggleRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.normal_txt.text.text = self.data.label_name
    self.node_list.TextHL.text.text = self.data.label_name
    self:ShowRemind()
end

function SystemForceToggleRender:ShowRemind()
    if not self.data then
        return
    end

    local is_remind = SystemForceWGData.Instance:SystemForceLongRemind(self.data.seq)
    self.node_list.RedPoint:SetActive(is_remind == 1)
end

function SystemForceToggleRender:OnSelectChange(is_select)
    self.node_list.normal:SetActive(not is_select)
    self.node_list.HLImage:SetActive(is_select)
end