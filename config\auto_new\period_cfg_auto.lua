-- M-每周必买.xls

return {
other={
{}
},

other_meta_table_map={
},
period={
{},
{period_seq=2,open_day=15,close_day=21,},
{period_seq=3,open_day=22,close_day=28,},
{period_seq=4,open_day=19,close_day=35,},
{period_seq=5,open_day=36,close_day=42,},
{period_seq=6,open_day=43,close_day=49,}
},

period_meta_table_map={
},
period_reward={
{},
{day_index=2,reward_bind_gold=425,},
{day_index=3,reward_bind_gold=450,},
{day_index=4,reward_bind_gold=475,},
{day_index=5,reward_bind_gold=500,},
{day_index=6,reward_bind_gold=525,},
{day_index=7,reward_bind_gold=550,},
{period_seq=2,},
{period_seq=2,},
{period_seq=2,},
{period_seq=2,},
{period_seq=2,},
{period_seq=2,},
{period_seq=2,},
{period_seq=3,},
{period_seq=3,},
{period_seq=3,},
{period_seq=3,},
{period_seq=3,},
{period_seq=3,},
{period_seq=3,},
{period_seq=4,},
{period_seq=4,},
{period_seq=4,},
{period_seq=4,},
{period_seq=4,},
{period_seq=4,},
{period_seq=4,},
{period_seq=5,},
{period_seq=5,},
{period_seq=5,},
{period_seq=5,},
{period_seq=5,},
{period_seq=5,},
{period_seq=5,},
{period_seq=6,},
{period_seq=6,},
{period_seq=6,},
{period_seq=6,},
{period_seq=6,},
{period_seq=6,},
{period_seq=6,}
},

period_reward_meta_table_map={
[28]=7,	-- depth:1
[30]=2,	-- depth:1
[31]=3,	-- depth:1
[32]=4,	-- depth:1
[34]=6,	-- depth:1
[35]=28,	-- depth:2
[27]=34,	-- depth:2
[37]=30,	-- depth:2
[38]=31,	-- depth:2
[39]=32,	-- depth:2
[40]=5,	-- depth:1
[33]=40,	-- depth:2
[26]=33,	-- depth:3
[21]=35,	-- depth:3
[24]=38,	-- depth:3
[23]=37,	-- depth:3
[41]=27,	-- depth:3
[20]=41,	-- depth:4
[19]=26,	-- depth:4
[18]=39,	-- depth:3
[17]=24,	-- depth:4
[16]=23,	-- depth:4
[14]=21,	-- depth:4
[13]=20,	-- depth:5
[12]=19,	-- depth:5
[11]=18,	-- depth:4
[10]=17,	-- depth:5
[9]=16,	-- depth:5
[25]=11,	-- depth:5
[42]=14,	-- depth:5
},
other_default_table={level_limit=30,cycle=1,openday_limit=8,},

period_default_table={period_seq=1,open_day=8,close_day=14,buy_gold=480,reward_bind_gold=480,},

period_reward_default_table={period_seq=1,day_index=1,miss_reward=1,reward_bind_gold=400,}

}

