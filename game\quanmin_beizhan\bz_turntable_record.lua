BZTurnTableRecord = BZTurnTableRecord or BaseClass(SafeBaseView)

function BZTurnTableRecord:__init()
    self:AddViewResource(0, "uis/view/quanmin_beizhan_ui_prefab", "layout_ts_turnable_record")
	self:SetMaskBg(true, true)
end

function BZTurnTableRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
    self.data_list = nil
end

function BZTurnTableRecord:ShowIndexCallBack()
    QuanMinBeiZhanWGCtrl.Instance:SendOpera(RA_TUNVLANG_OPERA_TYPE.RECORD)
    BZTurnTableWGData.Instance:ClearRecordCount()
end

function BZTurnTableRecord:LoadCallBack()
    self.record_list = AsyncListView.New(BZTurnTableRecordRender, self.node_list["record_list"])
    self.record_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize,self))
end


local LINE_SPACING = 40
function BZTurnTableRecord:ChangeCellSize(data_index)
    local data = self.data_list and self.data_list[data_index + 1] 
    if not data then return 0 end

    local name = data.name
    local reward = BZTurnTableWGData.Instance:GetTurnTableRewardInfoById(data.reward_id)
    reward = reward.reward_item
    if not reward then
        return 0
    end

    local record_cfg = BZTurnTableWGData.Instance:GetTurnTableRecordByItemId(reward.item_id) 
    if not record_cfg then
        return 0
    end

    local str = record_cfg.record_copy
    local cfg = ItemWGData.Instance:GetItemConfig(reward.item_id)
    local layer_cfg = BZTurnTableWGData.Instance:GetLayerCfgByLayerNum(data.layer)
    str = string.format(str, name, layer_cfg.name, ITEM_COLOR[cfg.color], cfg.name, reward.num)

    self.node_list["TestText"].text.text = str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function BZTurnTableRecord:OnFlush()
    local data = BZTurnTableWGData.Instance:GetRecordInfo()
    self.data_list = data
    self.record_list:SetDataList(data, 3)
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data))
end

function BZTurnTableRecord:CloseCalBack()
    BZTurnTableWGData.Instance:ClearRecordCount()
end
-------------------------------------------------------------------------------------
BZTurnTableRecordRender = BZTurnTableRecordRender or BaseClass(BaseRender)
function BZTurnTableRecordRender:OnFlush()
    self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.timestamp)

    local name = self.data.name
    local reward = BZTurnTableWGData.Instance:GetTurnTableRewardInfoById(self.data.reward_id)
    reward = reward.reward_item
    if not reward then
        return
    end

    local record_cfg = BZTurnTableWGData.Instance:GetTurnTableRecordByItemId(reward.item_id) 
    if not record_cfg then
        return
    end

    local str = record_cfg.record_copy
    local cfg = ItemWGData.Instance:GetItemConfig(reward.item_id)
    local layer_cfg = BZTurnTableWGData.Instance:GetLayerCfgByLayerNum(self.data.layer)
    str = string.format(str, name, layer_cfg.name, ITEM_COLOR[cfg.color], cfg.name, reward.num)
    self.node_list["info"].text.text = str
end