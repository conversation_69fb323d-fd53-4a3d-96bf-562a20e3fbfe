YunbiaoWGData = YunbiaoWGData or BaseClass()
YunbiaoWGData.HUSONG_TASK_FAIL_TYPE =
{
    HUSONG_TASK_FAIL_TYPE_NPC_LONGER = 0, --距离npc过远
}
function YunbiaoWGData:__init()
	if YunbiaoWGData.Instance ~= nil then
		<PERSON>rror<PERSON>og("[YunbiaoWGData] attempt to create singleton twice!")
		return
	end
	YunbiaoWGData.Instance = self

	self.lingqucishu = 0
	self.goumaicishu = 0
	self.refreshfreetime = 0
	self.accept_in_activitytime = 0
	self.task_ids = 24001
	self.has_insurance = 0
	self.has_use_protect_skill = 0
	self.auto_cfg = ConfigManager.Instance:GetAutoConfig("husongcfg_auto")
	self.other_cfg = self.auto_cfg.other[1]
	self.husong_wei_cfg = ConfigManager.Instance:GetAutoConfig("husong_wei_cfg_auto")

	self.task_cfg = ListToMap(self.auto_cfg.task_cfg, "task_color")
	self.question_cfg = ListToMap(self.auto_cfg.question, "issue")
	self.answer_cfg = ListToMap(self.auto_cfg.answer, "index", "answer_seq")
	-- 坐标计算
	self.area_info = {}
	self.area_info.pos_1 = {}
	self.area_info.pos_2 = {}
	local location_list = Split(self.other_cfg.location,"|")
	local localtion_pos_1 = Split(location_list[1],",")
	local localtion_pos_2 = Split(location_list[2],",")
	self.area_info.pos_1.x = tonumber(localtion_pos_1[1])
	self.area_info.pos_1.y = tonumber(localtion_pos_1[2])
	self.area_info.pos_2.x = tonumber(localtion_pos_2[2])
	self.area_info.pos_2.y = tonumber(localtion_pos_2[2])
	--
	self.question_max = #self.question_cfg
	self:CleanRefreshConsumeInfo()
end

function YunbiaoWGData:__delete()
	YunbiaoWGData.Instance = nil
end

function YunbiaoWGData:SetLingQuCishu(value)
	if value then
		self.lingqucishu = value
	end
end

function YunbiaoWGData:GetTaskCfgByColor(task_color)
	return self.task_cfg[task_color]
end
function YunbiaoWGData:GetBeautyResid(task_color)
	local data = nil
	if YunbiaoWGData.Instance:IsWeiHusong() then
		data = self.husong_wei_cfg.task_cfg
	else
		data = self.auto_cfg.task_cfg
	end
	if data then
		for k,v in pairs(data) do
			if v.task_color == task_color then
				return v.resource_ID
			end
		end
	end
	return 0
end

function YunbiaoWGData:GetBeautyName(task_color)
	local data = nil
	if YunbiaoWGData.Instance:IsWeiHusong() then
		data = self.husong_wei_cfg.task_cfg
	else
		data = self.auto_cfg.task_cfg
	end
	if data then
		for k,v in pairs(data) do
			if v.task_color == task_color then
				return v.task_name
			end
		end
	end
	return 0
end

function YunbiaoWGData:GetBeautyModelPath(task_color)
	local data = nil
	if YunbiaoWGData.Instance:IsWeiHusong() then
		data = self.husong_wei_cfg.task_cfg
	else
		data = self.auto_cfg.task_cfg
	end
	if data then
		for k,v in pairs(data) do
			if v.task_color == task_color then
				return v.model_bundle_name , v.model_asset_name
			end
		end
	end
	return "", ""
end

function YunbiaoWGData:GetBeautyPosition(task_color)
	local data = nil
	if YunbiaoWGData.Instance:IsWeiHusong() then
		data = self.husong_wei_cfg.task_cfg
	else
		data = self.auto_cfg.task_cfg
	end
	if data then
		for k,v in pairs(data) do
			if v.task_color == task_color then
				return v.whole_display_pos
			end
		end
	end
	return 0 , 0
end

function YunbiaoWGData:GetBeautyScale(task_color)
	local data = nil
	if YunbiaoWGData.Instance:IsWeiHusong() then
		data = self.husong_wei_cfg.task_cfg
	else
		data = self.auto_cfg.task_cfg
	end
	if data then
		for k,v in pairs(data) do
			if v.task_color == task_color then
				return v.obj_scale
			end
		end
	end
	return 1
end

function YunbiaoWGData:GetBeautyModelCfg(task_color)
	local data = nil
	if YunbiaoWGData.Instance:IsWeiHusong() then
		data = self.husong_wei_cfg.task_cfg
	else
		data = self.auto_cfg.task_cfg
	end
	if data then
		for k,v in pairs(data) do
			if v.task_color == task_color then
				return v
			end
		end
	end
	return {}
end

function YunbiaoWGData:GetLingQuCishu()
	return self.lingqucishu
end

function YunbiaoWGData:SetGouMaiCishu(value)
	if value then
		self.goumaicishu = value
	end
end

function YunbiaoWGData:GetGouMaiCishu()
	return self.goumaicishu
end

function YunbiaoWGData:SetRefreshFreeTime(value)
	if value then
		self.refreshfreetime = value
	end
end

function YunbiaoWGData:GetRefreshFreeTime()
	return self.refreshfreetime
end

function YunbiaoWGData:GetTaskColor()
	local color = GameVoManager.Instance:GetMainRoleVo().husong_color
	return color < 2 and 2 or color
end

function YunbiaoWGData:GetTaskId()
	return GameVoManager.Instance:GetMainRoleVo().husong_taskid
end

function YunbiaoWGData:SetAcceptInActivitytime(value)
	if value then
		self.accept_in_activitytime = value
	end
end

function YunbiaoWGData:GetAcceptInActivitytime()
	return self.accept_in_activitytime
end

-- tips:未调用
function YunbiaoWGData:SetHasInsurance(has_insurance)
	self.has_insurance = has_insurance
end

function YunbiaoWGData:GetHasInsurance()
	return self.has_insurance
end

-- 技能保护
function YunbiaoWGData:SetHasProtectSkill(has_use_protect_skill)
	self.has_use_protect_skill = has_use_protect_skill
end

function YunbiaoWGData:GetHasProtectSkill()
	return self.has_use_protect_skill
end

function YunbiaoWGData:GetTaskIdByCamp()
	return self.task_ids
end

function YunbiaoWGData:GetIsHuShong()
	return self:GetTaskId() > 0
end

function YunbiaoWGData:GetYubiaoMeiRenLingId()
	return self.auto_cfg.other[1].flush_itemid
end

function YunbiaoWGData:GetItemPriceType()
	return self.auto_cfg.other[1].price_type_1 or 1, self.auto_cfg.other[1].price_type_2 or 1
end

function YunbiaoWGData:GetYubiaoPreTaskId()
	return self.auto_cfg.other[1].pretask_id
end
function YunbiaoWGData:GetYubiaoOtheraAtuo()
	return self.auto_cfg.other[1]
end
--获得余下可运送次数
function YunbiaoWGData:GetHusongRemainTimes()
	local buytimes = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT) --已购买次数
	local complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) --完成次数

	-- print_error(buytimes,complete_times)
	return self:GetTotalFreeHusonTimes() + buytimes - complete_times
end

function YunbiaoWGData:GetTotalFreeHusonTimes()
	return self.auto_cfg.other[1].husong_times
end

-- 获取剩余免费次数
function YunbiaoWGData:GetFreeHusongNum()
	return self.auto_cfg.other[1].husong_times - self:GetLingQuCishu()
end

-- 额外赠送元宝
function YunbiaoWGData:GetFreeGold()
	return self.auto_cfg.other[1].orange_reward_gold_bind or 0
end

-- 保险费
function YunbiaoWGData:GetInsuranceGold()
	return self.auto_cfg.other[1].insurance_gold or 0
end

-- 获取运镖自动提交任务ID
function YunbiaoWGData:GetAutoNpcId()
	return self.auto_cfg.other[1].npc_id or 0
end
-- 获取运镖 接任务NPC ID
function YunbiaoWGData:GetAcceptNpcId()
	return self.auto_cfg.other[1].npc_ed1 or 0
end

-- 获取剩余购买次数
function YunbiaoWGData:GetMaxGoumaiNum()
	local buy_time_limit = VipPower.Instance:GetParam(VipPowerId.husong_buy_times)
	return buy_time_limit - self:GetGouMaiCishu()
end

-- 获取免费刷新次数
function YunbiaoWGData:GetFreeRefreshNum()
	return self.auto_cfg.other[1].free_refresh_times - self:GetRefreshFreeTime()
end

--获取总的运镖次数
function YunbiaoWGData:GetTotalCount()
	local num = self.auto_cfg.other[1].husong_times + self:GetGouMaiCishu()
	return num
end

-- 获取消耗刷新牌数量
function YunbiaoWGData:GetRefreshNum(task_color)
	for k,v in pairs(self.auto_cfg.task_color_flush_prob) do
		if task_color == v.task_color then
			return v.need_flush_item_num
		end
	end
	return 1
end

-- 获取刷新消耗描述
function YunbiaoWGData:GetRefreshConsumeStr()
	local str = ''
	if self:GetFreeRefreshNum() <= 0 then
		local other_config = self.auto_cfg.other[1]
		if nil ~= other_config then
			local item_id = other_config.flush_itemid
			local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
			local color = item_num >= 1 and COLOR3B.GREEN or COLOR3B.RED
			str = item_num .. "/" .. self:GetRefreshNum(GameVoManager.Instance:GetMainRoleVo().husong_color)
			str = string.format("<color=#006a25>%s</color>",str)
		end
	else
		str = string.format(Language.YunBiao.MianFei, self:GetFreeRefreshNum())
	end
	return str
end
--获得相差美人令
function YunbiaoWGData:GetDifferItem()
	if self:GetFreeRefreshNum() <= 0 then
		local other_config = self.auto_cfg.other[1]
		if nil ~= other_config then
			local item_id = other_config.flush_itemid
			local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
			local color = item_num >= 1 and COLOR3B.GREEN or COLOR3B.RED
			return (self:GetRefreshNum(GameVoManager.Instance:GetMainRoleVo().husong_color) - item_num)
		end
	end
end

-- 获取刷新次数描述
function YunbiaoWGData:GetConsumeYbStr()
	local num = self:GetHusongRemainTimes()--GetSurplusTimes()
	local color = num > 0 and "#0f9c1c" or "#9c160f"
	local totalnum = self:GetTotalFreeHusonTimes()
	return string.format(Language.YunBiao.HuSongCiShu1, color, num, totalnum)
end
-- 获取剩余次数
function YunbiaoWGData:GetSurplusTimes()
	return (self:GetTotalFreeHusonTimes() + self:GetGouMaiCishu() - self:GetLingQuCishu())
end

-- 获取购买次数描述
function YunbiaoWGData:GetConsumeNumStr()
	local num = self:GetMaxGoumaiNum()
	return string.format(Language.YunBiao.KeGouMaiCiShu, num < 0 and 0 or num)
end

function YunbiaoWGData:GetRewardConfig()
	local husong_act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.HUSONG)
	local list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local kill_monster_exp = 0
	local cfg = TaskWGData.Instance:GetRoleLevelReward(role_level)
	if cfg then
		kill_monster_exp = cfg.kill_monster_exp
	end
	-- if reward_config then
		local reward_config, bind_coin, other, tb_temp
		local count, exp = 0, 0
		for i = 2, 5 do
			reward_config = self:GetCyrRewardCfg(role_level,i)
			if reward_config then
				exp = math.floor(kill_monster_exp * reward_config.exp_copy)
				bind_coin = reward_config.reward_bind_coin
				other = nil
				if reward_config.reward_item_id ~= 0 then
					other = {}
					other.item_id = reward_config.reward_item_id
					other.num = reward_config.reward_item_num
				end
				tb_temp = {{item_id = 90050, num = exp}, {item_id = 65536, num = bind_coin}, other}
				table.insert(list, tb_temp)
			end
		end
	-- end
	return list
end

function YunbiaoWGData:GetOtherRewardConfig()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local task_reward_item = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").task_reward_item
	local list = {}
	if task_reward_item then
		for i=1,4 do
			list[i] = {}
			for k,v in pairs(task_reward_item) do
				if v.task_color == (i + 1) and role_level >= v.min_limit_level and role_level <= v.max_limit_level then
					list[i]["item_id"] = v.reward_item_id
					list[i]["num"] = v.reward_item_num
				end
			end
		end
	end
	return list
end

--获取当前品质配置
function YunbiaoWGData:GetRewardCfgByLv(color)
	local husong_task_cfg = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").task_cfg
	for k,v in pairs(husong_task_cfg) do
		if color == v.task_color then
			return v
		end
	end
	return nil
end

function YunbiaoWGData:GetCyrRewardCfg(lv,color)
	local task_reward_item = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").task_reward_item
	for k,v in pairs(task_reward_item) do
		if color == v.task_color and lv >= v.min_limit_level and lv <= v.max_limit_level then
			return v
		end
	end
	return nil
end

--获得当前身上玩家接的护送任务对应的奖励
function YunbiaoWGData:GetCurExitTaskRewardCfg()
	local list = self:GetRewardConfig()
	return list[self:GetTaskColor()]
end

-- 寻路到NPC
function YunbiaoWGData:MoveToHuShongNpc()
	if self:GetIsHuShong() then
		self:MoveToHuShongTask()
	end
end

-- 寻路到交护送任务
function YunbiaoWGData:MoveToHuShongTask()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(self:GetTaskIdByCamp())
	if nil ~= task_cfg then
		GuajiWGCtrl.Instance:StopGuaji()
		TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_HU)
        TaskGuide.Instance:CanAutoAllTask(true)
		TaskWGCtrl.Instance:OperateFollowTask(task_cfg)
	end
end

-- 是否开放护
function YunbiaoWGData:IsOpenHuSong()
	return FunOpen.Instance:GetFunIsOpened(FunName.HusongTask)
end
--获取免费刷新剩余次数
function YunbiaoWGData:GetFreeNum()
	local aleardy_flush_num = self.free_refresh_count or 0
	return self.auto_cfg.other[1].free_refresh_times - aleardy_flush_num
end

function YunbiaoWGData:SetAleardyFreeNum(free_refresh_count)
	self.free_refresh_count = free_refresh_count
end

function YunbiaoWGData:SetHusongRefreshAck(protocol)
	self:SetAleardyFreeNum(protocol.free_refresh_count)
	self:SetCurHusongColor(protocol.refresh_color)
	self:SaveRefreshConsumeInfo(protocol)
end

function YunbiaoWGData:SaveRefreshConsumeInfo(protocol)
	local consume_info = self.consume_info
	consume_info.refresh_total_count = consume_info.refresh_total_count + protocol.refresh_total_count
	consume_info.refresh_consume_item_num = consume_info.refresh_consume_item_num + protocol.refresh_consume_item_num
	consume_info.refresh_consume_bind_gold = consume_info.refresh_consume_bind_gold + protocol.refresh_consume_bind_gold
	consume_info.refresh_consume_gold = consume_info.refresh_consume_gold + protocol.refresh_consume_gold
	consume_info.consume_buy_item_num = consume_info.consume_buy_item_num + protocol.consume_buy_item_num
	self.consume_info = consume_info
end

function YunbiaoWGData:GetRefreshConsumeInfo()
	return self.consume_info
end

function YunbiaoWGData:CleanRefreshConsumeInfo()
	local consume_info = {}
	consume_info.refresh_total_count = 0
	consume_info.refresh_consume_item_num = 0
	consume_info.refresh_consume_bind_gold = 0
	consume_info.refresh_consume_gold = 0
	consume_info.consume_buy_item_num = 0
	self.consume_info = consume_info
end


function YunbiaoWGData:SetCurHusongColor(color)
	self.task_color = color
end

function YunbiaoWGData:GetCurHusongColor()
	return self.task_color or 2
end

function YunbiaoWGData:GetCurHusongDesc()
	local color = self:GetCurHusongColor()
	local cfg = self:GetRewardCfgByLv(color)
	if cfg then
		return cfg.husong_desc
	end
	return ""
end

function YunbiaoWGData:IsWeiHusong()
	if GameVoManager.Instance:GetMainRoleVo().husong_taskid == self.husong_wei_cfg.other[1].task_id then
		return true
	end

	local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
	local curr_task_id = task_list[1]

	if not curr_task_id then return false end
	if curr_task_id <= self.husong_wei_cfg.other[1].task_id then
		return true
	end

	local task_cfg = TaskWGData.Instance:GetTaskConfig(curr_task_id)
	if not task_cfg then return false end

	return task_cfg.pretaskid <= self.husong_wei_cfg.other[1].task_id
end

function YunbiaoWGData:GetHuSongTaskId()
	local cur_task_id = self:GetTaskId()
	local task_id = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").other[1].husong_task_id or 0
	return cur_task_id == task_id
end

--获取护送的刷新提示（就一个提示而已）
function YunbiaoWGData:GetHuSongFlushHint(husong_protocol)
	--返回空做判空处理
	if nil == husong_protocol then
		return
	end

	--判断结束类型
	local str = nil
	local end_type = 0
	local xiaohao_num = husong_protocol.consume_buy_item_num
	local color = Language.YunBiao.HusongColor[husong_protocol.refresh_color] or ""

	if husong_protocol.refresh_consume_item_num == 0 and husong_protocol.refresh_consume_bind_gold == 0 and husong_protocol.refresh_consume_gold == 0 then
		end_type = HUSONG_END_TYPE.FREE_END
	elseif husong_protocol.refresh_consume_item_num > 0 and husong_protocol.refresh_consume_bind_gold == 0 and husong_protocol.refresh_consume_gold == 0 then
		end_type = HUSONG_END_TYPE.ITEM_END

	elseif husong_protocol.refresh_consume_item_num > 0 and husong_protocol.refresh_consume_bind_gold > 0 and husong_protocol.refresh_consume_gold > 0 then
		if xiaohao_num == husong_protocol.refresh_consume_item_num then
			end_type = HUSONG_END_TYPE.ALL_MONEY_BUY_END
		elseif xiaohao_num < husong_protocol.refresh_consume_item_num then
			end_type = HUSONG_END_TYPE.ALL_TYPE_END
		end

	elseif husong_protocol.refresh_consume_item_num > 0 and husong_protocol.refresh_consume_bind_gold > 0 then
		if xiaohao_num == husong_protocol.refresh_consume_item_num then
			end_type = HUSONG_END_TYPE.BINDGOLD_BUY_END
		elseif xiaohao_num < husong_protocol.refresh_consume_item_num then
			end_type = HUSONG_END_TYPE.ITEM_BINDGOLD_BUY_END
		end

	elseif husong_protocol.refresh_consume_item_num > 0 and husong_protocol.refresh_consume_gold > 0 then
		if xiaohao_num == husong_protocol.refresh_consume_item_num then
			end_type = HUSONG_END_TYPE.GOLD_BUY_END
		elseif xiaohao_num < husong_protocol.refresh_consume_item_num then
			end_type = HUSONG_END_TYPE.ITEM_GOLD_BUY_END
		end
	end

	if end_type == HUSONG_END_TYPE.FREE_END then
		str = string.format(Language.YunBiao.FlushHint3,husong_protocol.refresh_total_count,color)

	elseif end_type == HUSONG_END_TYPE.ITEM_END then
		str = string.format(Language.YunBiao.FlushHint5,husong_protocol.refresh_consume_item_num,husong_protocol.refresh_total_count,color)

	elseif end_type == HUSONG_END_TYPE.BINDGOLD_BUY_END then
		str = string.format(Language.YunBiao.FlushHint6, husong_protocol.refresh_consume_bind_gold,
					husong_protocol.refresh_consume_item_num, husong_protocol.refresh_total_count, color)

	elseif end_type == HUSONG_END_TYPE.ITEM_BINDGOLD_BUY_END then
		str = string.format(Language.YunBiao.FlushHint4, husong_protocol.refresh_consume_bind_gold,
					xiaohao_num, husong_protocol.refresh_consume_item_num, husong_protocol.refresh_total_count, color)

	elseif end_type == HUSONG_END_TYPE.GOLD_BUY_END then
		str = string.format(Language.YunBiao.FlushHint6_1, husong_protocol.refresh_consume_gold,
					husong_protocol.refresh_consume_item_num, husong_protocol.refresh_total_count, color)

	elseif end_type == HUSONG_END_TYPE.ITEM_GOLD_BUY_END then
		str = string.format(Language.YunBiao.FlushHint4_1, husong_protocol.refresh_consume_gold,
					xiaohao_num, husong_protocol.refresh_consume_item_num, husong_protocol.refresh_total_count, color)

	elseif end_type == HUSONG_END_TYPE.ALL_MONEY_BUY_END then
		str = string.format(Language.YunBiao.FlushHint6_2, husong_protocol.refresh_consume_gold, husong_protocol.refresh_consume_bind_gold,
					husong_protocol.refresh_consume_item_num, husong_protocol.refresh_total_count, color)

	elseif end_type == HUSONG_END_TYPE.ALL_TYPE_END then
		str = string.format(Language.YunBiao.FlushHint4_2, husong_protocol.refresh_consume_gold, husong_protocol.refresh_consume_bind_gold,
					xiaohao_num, husong_protocol.refresh_consume_item_num, husong_protocol.refresh_total_count, color)
	end

	return str
end

--护送可以继续次数，以完成为计算节点
function YunbiaoWGData:GetHuSongCanGoonNum()
	local buytimes = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT) or 0 --已购买次数
	local free_time = self:GetTotalFreeHusonTimes() or 0
	local finsh_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_FINISH_HUSONG_TASK_COUNT) or 0 --完成次数

	return free_time + buytimes - finsh_num
end

function YunbiaoWGData:GetYunbiaoScene()
	local task_id = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").other[1].task_id
	local data = TaskWGData.Instance:GetTaskConfig(task_id).accept_npc
	return data
end

-- 判断任务id是否是主线的护送任务
function YunbiaoWGData:IsZhuXianHuSong(task_id)
	local zhu_task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if zhu_task_cfg and zhu_task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7 
	    and zhu_task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_10 then
	    	return true
	end
	return false
end

-- 随机获取一个问题配置
function YunbiaoWGData:GetRandomAnswerCfg()
	local random = math.random(1, self.question_max) 
	return self.question_cfg[random],self.answer_cfg[random]
end

-- 获取护送问题范围
function YunbiaoWGData:GetAreaInfo()
	return self.area_info
end

-- 获取答题时间
function YunbiaoWGData:GetTopicTime()
	return self.other_cfg.topic_time or 0
end