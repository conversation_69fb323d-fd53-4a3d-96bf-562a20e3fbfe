RechargeVolumeRewardView = RechargeVolumeRewardView or BaseClass(SafeBaseView)

function RechargeVolumeRewardView:__init()
    self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
    self.full_screen = true
	self.is_safe_area_adapter = true
	self.is_big_view = true
    local common_bundle = "uis/view/common_panel_prefab"
    local bundle_name = "uis/view/recharge_volume_ui_prefab"
	self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
    self:AddViewResource(0, bundle_name, "recharge_volume_reward_view")
    self:AddViewResource(0, common_bundle, "layout_a3_light_common_top_panel")
end

function RechargeVolumeRewardView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.RechargeVolume.TitleName2
    local bundle, asset = ResPath.GetRawImagesPNG("a3_czj_bg")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    if nil == self.grade_reward_list then
        self.grade_reward_list = AsyncListView.New(CumulateRewardItemRender, self.node_list.reward_list)
        self.grade_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind(self.OnClickOneKeyGetReward, self))
end

function RechargeVolumeRewardView:ReleaseCallBack()
    if self.grade_reward_list then
        self.grade_reward_list:DeleteMe()
        self.grade_reward_list = nil
    end
end

function RechargeVolumeRewardView:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushCumulateRewardPart()
		end
	end
end

function RechargeVolumeRewardView:FlushCumulateRewardPart()
    local data_list = RechargeVolumeWGData.Instance:GetGiftRewardListByRound()
    self:ForceFlushShowPart()
    self.grade_reward_list:SetDataList(data_list)
    local tips_str = Language.RechargeVolume.RechargeGiftResetTips or ''
    self.node_list.reward_tips.text.text = tips_str
    self.node_list.reward_remind:CustomSetActive(RechargeVolumeWGData.Instance:GetTodayRewardRemind())
end

function RechargeVolumeRewardView:ForceFlushShowPart()
    if self.grade_reward_list then
        local length = self.grade_reward_list:GetListViewNumbers()
        local cell
        for idx = 0, length do
            cell = self.grade_reward_list:GetItemAt(idx)
            if cell then
                cell:ResetItemStatus()
            end
        end
    end
end

function RechargeVolumeRewardView:OnClickOneKeyGetReward()
    -- todo 判断是否有奖励可领取
    if RechargeVolumeWGData.Instance:GetTodayRewardRemind() then
        RechargeVolumeWGCtrl.Instance:SendOperateReq(CUMULATE_RECHARGE_OPERATE_TYPE.FETCH)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RechargeVolume.RechargeGiftGetTip)
    end
end
---------------------------- 档位奖励格子 start ----------------------------
local REWARD_STATUS = {
    NORMAL = 1,
    HAD_GET = 2,
    OUT_TIME = 3,
}
CumulateRewardItemRender = CumulateRewardItemRender or BaseClass(BaseRender)

function CumulateRewardItemRender:__delete()
    if self.day_reward_list then
        self.day_reward_list:DeleteMe()
        self.day_reward_list = nil
    end
    self.day_reward_data = nil
    self.cur_day = nil
    self.cur_item_grade = nil
end

function CumulateRewardItemRender:LoadCallBack()
    self.day_reward_data = nil
    self.cur_day = nil

    if not self.day_reward_list then
        self.day_reward_list = AsyncListView.New(CumulateDayRewardRender, self.node_list.day_reward_list)
    end
end

function CumulateRewardItemRender:ResetItemStatus()
    self.day_reward_data = nil
    self.cur_day = nil
end

function CumulateRewardItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return 
    end

    local recharge_num = self.data[1].recharge_num or 0
    local real_recharge_num = RechargeVolumeWGData.Instance:GetCurGiftRechargeNum()
    self.node_list.desc_price.text.text = recharge_num

    local grade = self.data[1].recharge_grade or 0
    local decorate_cfg = RechargeVolumeWGData.Instance:GetDecorateCfg(grade)
    if not IsEmptyTable(decorate_cfg) then
        local bundle, asset = ResPath.GetCumulateRechargeImage(decorate_cfg.grade_bg)
        if bundle and asset then
            self.node_list.desc_price_bg.image:LoadSprite(bundle, asset, function()
                self.node_list.desc_price_bg.image:SetNativeSize()
            end)
        end

        -- TODO 需要改为新版处理方式
        -- self.node_list.desc_price.gradient.Color1 = StrToColor(decorate_cfg.title_color_top)
		-- self.node_list.desc_price.gradient.Color2 = StrToColor(decorate_cfg.title_color_bom)

        self.node_list.desc_price.text.colorGradient = TMPro.VertexGradient(
            StrToColor(decorate_cfg.title_color_top),
            StrToColor(decorate_cfg.title_color_top),
            StrToColor(decorate_cfg.title_color_bom),
            StrToColor(decorate_cfg.title_color_bom)
        )

        -- local bundle_1, asset_1 = ResPath.GetRawImagesPNG(decorate_cfg.grade_txt_bg)
        -- if bundle_1 and asset_1 then
        --     self.node_list.title_bg.raw_image:LoadSprite(bundle_1, asset_1, function()
        --         self.node_list.title_bg.raw_image:SetNativeSize()
        --     end)
        -- end

        local cur_day = RechargeVolumeWGData.Instance:GetCurGiftRewardDay()
        local recharge_grade = self.data[1].recharge_grade or 0
        if self.cur_day ~= cur_day or self.cur_item_grade ~= recharge_grade then
            self.cur_day = cur_day
            self.cur_item_grade = recharge_grade
            self.day_reward_data = nil
        end

        if IsEmptyTable(self.day_reward_data) then
            self.day_reward_data = {}
            local t_fit, is_get, is_can_receive, sort_idx = false, false, false, REWARD_STATUS.OUT_TIME
            for key, value in pairs(self.data) do
                t_fit = self.cur_day == key

                is_get = RechargeVolumeWGData.Instance:GetGradeRewardStatusByDay(value.recharge_grade, key)
                if is_get then
                    sort_idx = REWARD_STATUS.HAD_GET
                elseif self.cur_day > key then
                    sort_idx = REWARD_STATUS.OUT_TIME
                else
                    sort_idx = REWARD_STATUS.NORMAL
                end

                if t_fit then
                    is_can_receive = real_recharge_num >= recharge_num and (not is_get)
                else
                    is_can_receive = false
                end

                table.insert(self.day_reward_data, {
                    reward = value.reward,
                    day_bg = decorate_cfg.grade_day_bg or '',
                    -- reward_line = decorate_cfg.grade_reward_line or '',
                    data_day = key,
                    sort_idx = sort_idx,
                    is_last = false,
                    is_can_receive = is_can_receive,
                })
            end
            table.sort(self.day_reward_data, SortTools.KeyLowerSorter("sort_idx", "data_day"))
            self.day_reward_data[#self.day_reward_data].is_last = true
        end
    end

    -- local show_str = string.format(Language.RechargeVolume.RechargeGiftTips, recharge_num) 
    -- self.node_list.title_txt.text.text = show_str

    self.day_reward_list:SetDataList(self.day_reward_data)
    local is_get_reward = RechargeVolumeWGData.Instance:GetGradeRewardStatusByDay(grade)

    self.node_list.red:CustomSetActive(real_recharge_num >= recharge_num and (not is_get_reward))
end
---------------------------- 档位奖励格子  end  ----------------------------

---------------------------- 档位天数奖励 start ----------------------------
CumulateDayRewardRender = CumulateDayRewardRender or BaseClass(BaseRender)

function CumulateDayRewardRender:LoadCallBack()
    if not self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_item_list:SetStartZeroIndex(true)
    end
end

function CumulateDayRewardRender:__delete()
    if self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end
end

function CumulateDayRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local chinese_str = NumberToChinaNumber(self.data.data_day or 1)
    local show_str = string.format(Language.RechargeVolume.RechargeGiftDayTips, chinese_str)
    self.node_list.day_txt.text.text = show_str

    local sort_idx = self.data.sort_idx or 1
    self.node_list.had_get_flag:SetActive(sort_idx == REWARD_STATUS.HAD_GET)
    self.node_list.no_reach_flag:SetActive(sort_idx == REWARD_STATUS.OUT_TIME)

    self.node_list.can_receive_effect:SetActive(self.data.is_can_receive)

    local day_bg = self.data.day_bg or ''
    if day_bg ~= '' then
        local bundle, asset = ResPath.GetRawImagesPNG(day_bg)
        self.node_list.day_reward_bg.raw_image:LoadSprite(bundle, asset, function()
            self.node_list.day_reward_bg.raw_image:SetNativeSize()
        end)
    end

    -- local reward_line = self.data.reward_line or ''
    -- if reward_line ~= '' then
    --     local bundle, asset = ResPath.GetCumulateRechargeImage(reward_line)
    --     self.node_list.under_line.image:LoadSprite(bundle, asset, function()
    --         self.node_list.under_line.image:SetNativeSize()
    --     end)
    -- end

    -- local is_last = self.data.is_last or false
    -- self.node_list.under_line:SetActive(not is_last)

    self.reward_item_list:SetDataList(self.data.reward)
end
---------------------------- 档位天数奖励  end  ----------------------------