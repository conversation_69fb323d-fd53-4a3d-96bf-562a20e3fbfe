-- Y-运营活动-连续充值.xls
local item_table={
[1]={item_id=38411,num=1,is_bind=1},
[2]={item_id=26191,num=1,is_bind=1},
[3]={item_id=27612,num=3,is_bind=1},
[4]={item_id=44071,num=1,is_bind=1},
[5]={item_id=27833,num=1,is_bind=1},
[6]={item_id=46380,num=5,is_bind=1},
[7]={item_id=37722,num=1,is_bind=1},
[8]={item_id=48090,num=1,is_bind=1},
[9]={item_id=27834,num=1,is_bind=1},
[10]={item_id=37622,num=1,is_bind=1},
[11]={item_id=26193,num=1,is_bind=1},
[12]={item_id=27613,num=1,is_bind=1},
[13]={item_id=44070,num=1,is_bind=1},
[14]={item_id=27835,num=1,is_bind=1},
[15]={item_id=46380,num=10,is_bind=1},
[16]={item_id=48088,num=1,is_bind=1},
[17]={item_id=30425,num=2,is_bind=1},
[18]={item_id=26200,num=30,is_bind=1},
[19]={item_id=26377,num=3,is_bind=1},
[20]={item_id=26354,num=2,is_bind=1},
[21]={item_id=28717,num=2,is_bind=1},
[22]={item_id=27613,num=2,is_bind=1},
[23]={item_id=22618,num=1,is_bind=1},
[24]={item_id=28808,num=1,is_bind=1},
[25]={item_id=26203,num=30,is_bind=1},
[26]={item_id=26123,num=1,is_bind=1},
[27]={item_id=26504,num=1,is_bind=1},
[28]={item_id=26372,num=2,is_bind=1},
[29]={item_id=28716,num=1,is_bind=1},
[30]={item_id=28853,num=1,is_bind=1},
[31]={item_id=26162,num=3,is_bind=1},
[32]={item_id=26415,num=10,is_bind=1},
[33]={item_id=48071,num=10,is_bind=1},
[34]={item_id=27909,num=5,is_bind=1},
[35]={item_id=36366,num=3,is_bind=1},
[36]={item_id=26161,num=1,is_bind=1},
[37]={item_id=26410,num=10,is_bind=1},
[38]={item_id=27861,num=1,is_bind=1},
[39]={item_id=29253,num=1,is_bind=1},
[40]={item_id=27611,num=15,is_bind=1},
[41]={item_id=27830,num=2,is_bind=1},
[42]={item_id=28718,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
day_recharge_reward={
{},
{index=2,seq=1,money=68,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},describe_mid="68元",},
{index=3,seq=2,activity_day=2,reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[3],[3]=item_table[8],[4]=item_table[9],[5]=item_table[6]},},
{index=4,seq=3,activity_day=2,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14],[5]=item_table[15]},},
{index=5,seq=4,activity_day=3,reward_item={[0]=item_table[7],[1]=item_table[10],[2]=item_table[11],[3]=item_table[16],[4]=item_table[12],[5]=item_table[15]},},
{index=6,seq=5,activity_day=3,money=68,describe_mid="68元",},
{index=7,seq=6,activity_day=4,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{index=8,seq=7,activity_day=4,reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[3],[3]=item_table[8],[4]=item_table[9],[5]=item_table[6]},},
{index=9,seq=8,activity_day=5,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14],[5]=item_table[15]},},
{index=10,seq=9,activity_day=5,reward_item={[0]=item_table[7],[1]=item_table[10],[2]=item_table[11],[3]=item_table[16],[4]=item_table[12],[5]=item_table[15]},},
{index=11,grade=1,},
{index=12,grade=1,},
{index=13,grade=1,},
{index=14,grade=1,},
{index=15,grade=1,},
{index=16,grade=1,},
{index=17,grade=1,},
{index=18,grade=1,},
{index=19,grade=1,},
{index=20,grade=1,},
{index=21,grade=2,},
{index=22,grade=2,},
{index=23,grade=2,},
{index=24,grade=2,},
{index=25,grade=2,},
{index=26,grade=2,},
{index=27,grade=2,},
{index=28,grade=2,},
{index=29,grade=2,},
{index=30,grade=2,},
{index=31,grade=3,},
{index=32,grade=3,},
{index=33,grade=3,},
{index=34,grade=3,},
{index=35,grade=3,},
{index=36,grade=3,},
{index=37,grade=3,},
{index=38,grade=3,},
{index=39,grade=3,},
{index=40,grade=3,},
{index=41,grade=4,},
{index=42,grade=4,},
{index=43,grade=4,},
{index=44,grade=4,},
{index=45,grade=4,},
{index=46,grade=4,},
{index=47,grade=4,},
{index=48,grade=4,},
{index=49,grade=4,},
{index=50,grade=4,},
{index=51,grade=5,},
{index=52,grade=5,},
{index=53,grade=5,},
{index=54,grade=5,},
{index=55,grade=5,},
{index=56,grade=5,},
{index=57,grade=5,},
{index=58,grade=5,},
{index=59,grade=5,},
{index=60,grade=5,},
{index=61,grade=6,},
{index=62,grade=6,},
{index=63,grade=6,},
{index=64,grade=6,},
{index=65,grade=6,},
{index=66,grade=6,},
{index=67,grade=6,},
{index=68,grade=6,},
{index=69,grade=6,},
{index=70,grade=6,},
{index=71,grade=7,},
{index=72,grade=7,},
{index=73,grade=7,},
{index=74,grade=7,},
{index=75,grade=7,},
{index=76,grade=7,},
{index=77,grade=7,},
{index=78,grade=7,},
{index=79,grade=7,},
{index=80,grade=7,},
{index=81,grade=8,},
{index=82,grade=8,},
{index=83,grade=8,},
{index=84,grade=8,},
{index=85,grade=8,},
{index=86,grade=8,},
{index=87,grade=8,},
{index=88,grade=8,},
{index=89,grade=8,},
{index=90,grade=8,},
{index=91,grade=9,},
{index=92,grade=9,},
{index=93,grade=9,},
{index=94,grade=9,},
{index=95,grade=9,},
{index=96,grade=9,},
{index=97,grade=9,},
{index=98,grade=9,},
{index=99,grade=9,},
{index=100,grade=9,},
{index=101,grade=10,},
{index=102,grade=10,},
{index=103,grade=10,},
{index=104,grade=10,},
{index=105,grade=10,},
{index=106,grade=10,},
{index=107,grade=10,},
{index=108,grade=10,},
{index=109,grade=10,},
{index=110,grade=10,},
{index=111,grade=11,},
{index=112,grade=11,},
{index=113,grade=11,},
{index=114,grade=11,},
{index=115,grade=11,},
{index=116,grade=11,},
{index=117,grade=11,},
{index=118,grade=11,},
{index=119,grade=11,},
{index=120,grade=11,}
},

day_recharge_reward_meta_table_map={
[45]=5,	-- depth:1
[93]=3,	-- depth:1
[49]=9,	-- depth:1
[107]=7,	-- depth:1
[53]=3,	-- depth:1
[55]=5,	-- depth:1
[57]=7,	-- depth:1
[89]=9,	-- depth:1
[105]=5,	-- depth:1
[59]=9,	-- depth:1
[119]=9,	-- depth:1
[83]=3,	-- depth:1
[63]=3,	-- depth:1
[65]=5,	-- depth:1
[67]=7,	-- depth:1
[103]=3,	-- depth:1
[69]=9,	-- depth:1
[97]=7,	-- depth:1
[73]=3,	-- depth:1
[87]=7,	-- depth:1
[75]=5,	-- depth:1
[77]=7,	-- depth:1
[79]=9,	-- depth:1
[99]=9,	-- depth:1
[85]=5,	-- depth:1
[109]=9,	-- depth:1
[47]=7,	-- depth:1
[95]=5,	-- depth:1
[17]=7,	-- depth:1
[43]=3,	-- depth:1
[23]=3,	-- depth:1
[115]=5,	-- depth:1
[25]=5,	-- depth:1
[15]=5,	-- depth:1
[13]=3,	-- depth:1
[27]=7,	-- depth:1
[29]=9,	-- depth:1
[117]=7,	-- depth:1
[113]=3,	-- depth:1
[19]=9,	-- depth:1
[39]=9,	-- depth:1
[35]=5,	-- depth:1
[33]=3,	-- depth:1
[37]=7,	-- depth:1
[92]=2,	-- depth:1
[96]=6,	-- depth:1
[76]=6,	-- depth:1
[16]=6,	-- depth:1
[82]=2,	-- depth:1
[8]=6,	-- depth:1
[86]=6,	-- depth:1
[12]=2,	-- depth:1
[4]=6,	-- depth:1
[10]=6,	-- depth:1
[22]=2,	-- depth:1
[116]=6,	-- depth:1
[46]=6,	-- depth:1
[36]=6,	-- depth:1
[52]=2,	-- depth:1
[112]=2,	-- depth:1
[106]=6,	-- depth:1
[56]=6,	-- depth:1
[72]=2,	-- depth:1
[32]=2,	-- depth:1
[42]=2,	-- depth:1
[62]=2,	-- depth:1
[26]=6,	-- depth:1
[66]=6,	-- depth:1
[102]=2,	-- depth:1
[118]=8,	-- depth:2
[100]=10,	-- depth:2
[104]=4,	-- depth:2
[108]=8,	-- depth:2
[110]=10,	-- depth:2
[114]=4,	-- depth:2
[98]=8,	-- depth:2
[60]=10,	-- depth:2
[90]=10,	-- depth:2
[14]=4,	-- depth:2
[18]=8,	-- depth:2
[20]=10,	-- depth:2
[24]=4,	-- depth:2
[28]=8,	-- depth:2
[30]=10,	-- depth:2
[34]=4,	-- depth:2
[38]=8,	-- depth:2
[40]=10,	-- depth:2
[44]=4,	-- depth:2
[94]=4,	-- depth:2
[48]=8,	-- depth:2
[54]=4,	-- depth:2
[58]=8,	-- depth:2
[64]=4,	-- depth:2
[68]=8,	-- depth:2
[70]=10,	-- depth:2
[74]=4,	-- depth:2
[78]=8,	-- depth:2
[80]=10,	-- depth:2
[84]=4,	-- depth:2
[88]=8,	-- depth:2
[50]=10,	-- depth:2
[120]=10,	-- depth:2
},
total_recharge_day_reward={
{reward_item={[0]=item_table[17]},},
{index=2,seq=1,total_day=5,reward_item={[0]=item_table[18]},},
{index=3,seq=2,money=68,reward_item={[0]=item_table[19]},card_img="ctn_bg_2_1",},
{index=4,seq=3,money=68,reward_item={[0]=item_table[20]},},
{index=5,grade=1,reward_item={[0]=item_table[21]},},
{index=6,grade=1,reward_item={[0]=item_table[22]},},
{index=7,grade=1,reward_item={[0]=item_table[23]},},
{index=8,grade=1,reward_item={[0]=item_table[24]},},
{index=9,grade=2,reward_item={[0]=item_table[25]},},
{index=10,grade=2,reward_item={[0]=item_table[26]},},
{index=11,grade=2,reward_item={[0]=item_table[27]},},
{index=12,grade=2,reward_item={[0]=item_table[28]},},
{index=13,grade=3,reward_item={[0]=item_table[29]},},
{index=14,grade=3,reward_item={[0]=item_table[30]},},
{index=15,grade=3,reward_item={[0]=item_table[31]},},
{index=16,grade=3,reward_item={[0]=item_table[32]},},
{index=17,grade=4,reward_item={[0]=item_table[33]},},
{index=18,grade=4,reward_item={[0]=item_table[34]},},
{index=19,grade=4,reward_item={[0]=item_table[35]},},
{index=20,grade=4,reward_item={[0]=item_table[36]},},
{index=21,grade=5,reward_item={[0]=item_table[37]},},
{index=22,grade=5,seq=1,total_day=5,},
{index=23,grade=5,reward_item={[0]=item_table[38]},},
{index=24,grade=5,reward_item={[0]=item_table[17]},},
{index=25,grade=6,reward_item={[0]=item_table[18]},},
{index=26,grade=6,reward_item={[0]=item_table[19]},},
{index=27,grade=6,reward_item={[0]=item_table[20]},},
{index=28,grade=6,reward_item={[0]=item_table[21]},},
{index=29,grade=7,reward_item={[0]=item_table[22]},},
{index=30,grade=7,reward_item={[0]=item_table[23]},},
{index=31,grade=7,reward_item={[0]=item_table[24]},},
{index=32,grade=7,reward_item={[0]=item_table[25]},},
{index=33,grade=8,reward_item={[0]=item_table[26]},},
{index=34,grade=8,reward_item={[0]=item_table[27]},},
{index=35,grade=8,reward_item={[0]=item_table[28]},},
{index=36,grade=8,reward_item={[0]=item_table[29]},},
{index=37,grade=9,reward_item={[0]=item_table[30]},},
{index=38,grade=9,reward_item={[0]=item_table[31]},},
{index=39,grade=9,reward_item={[0]=item_table[32]},},
{index=40,grade=9,reward_item={[0]=item_table[33]},},
{index=41,grade=10,reward_item={[0]=item_table[34]},},
{index=42,grade=10,reward_item={[0]=item_table[35]},},
{index=43,grade=10,reward_item={[0]=item_table[36]},},
{index=44,grade=10,reward_item={[0]=item_table[37]},},
{index=45,grade=11,},
{index=46,grade=11,reward_item={[0]=item_table[38]},},
{index=47,seq=2,money=68,card_img="ctn_bg_2_1",},
{index=48,grade=11,reward_item={[0]=item_table[38]},}
},

total_recharge_day_reward_meta_table_map={
[46]=22,	-- depth:1
[30]=22,	-- depth:1
[26]=22,	-- depth:1
[4]=2,	-- depth:1
[47]=45,	-- depth:1
[38]=22,	-- depth:1
[18]=22,	-- depth:1
[34]=22,	-- depth:1
[6]=22,	-- depth:1
[42]=22,	-- depth:1
[14]=22,	-- depth:1
[10]=22,	-- depth:1
[39]=3,	-- depth:1
[43]=3,	-- depth:1
[40]=4,	-- depth:2
[36]=4,	-- depth:2
[35]=3,	-- depth:1
[44]=4,	-- depth:2
[24]=4,	-- depth:2
[31]=3,	-- depth:1
[28]=4,	-- depth:2
[27]=3,	-- depth:1
[23]=3,	-- depth:1
[20]=4,	-- depth:2
[19]=3,	-- depth:1
[16]=4,	-- depth:2
[15]=3,	-- depth:1
[12]=4,	-- depth:2
[11]=3,	-- depth:1
[8]=4,	-- depth:2
[7]=3,	-- depth:1
[32]=4,	-- depth:2
[48]=4,	-- depth:2
},
config_param={
{},
{week_index=5,grade=1,slogan="ctn_title_1",selected_tag="ctn_toggle_hl_1",tag="ctn_toggle_normal_1",flag_1="ctn_bg_2_2",flag_2="ctn_bg_3_2",},
{start_server_day=22,end_server_day=29,grade=2,},
{week_index=5,grade=3,},
{start_server_day=29,end_server_day=36,grade=4,},
{week_index=5,grade=5,},
{start_server_day=36,end_server_day=43,grade=6,},
{week_index=5,grade=7,},
{start_server_day=43,end_server_day=50,grade=8,},
{week_index=5,grade=9,},
{start_server_day=50,end_server_day=57,grade=10,},
{week_index=5,grade=11,}
},

config_param_meta_table_map={
[4]=3,	-- depth:1
[6]=5,	-- depth:1
[8]=7,	-- depth:1
[10]=9,	-- depth:1
[12]=11,	-- depth:1
},
other_default_table={open_role_level=100,},

day_recharge_reward_default_table={index=1,grade=0,seq=0,activity_day=1,money=30,reward_item={[0]=item_table[39],[1]=item_table[2],[2]=item_table[40],[3]=item_table[4],[4]=item_table[41],[5]=item_table[6]},describe="今天累充#，可获奖励",describe_mid="30元",hearsay_type=964,},

total_recharge_day_reward_default_table={index=1,grade=0,seq=0,total_day=3,money=30,reward_item={[0]=item_table[42]},card_img="ctn_bg_3_1",hearsay_type=964,},

config_param_default_table={start_server_day=15,end_server_day=22,week_index=2,grade=0,tips_title="连续充值",tips_external="每天充值达规定金额可领取对应奖励",tips_within="每天充值达规定金额可领取对应奖励",button_effects="ui_lq_03",slogan="ctn_title",rope="ctn_shengzi",selected_tag="ctn_toggle_hl",tag="ctn_toggle_normal",day_button_text_color="#8A0000",reward_list="ctn_item_bg",ctn_big_bg="ctn_big_bg",flag_1="ctn_bg_2_1",flag_2="ctn_bg_3_1",}

}

