--------------------------
-- 陨落星辰
--------------------------
function MergeActivityView:DelFireworksView()
    --[[
    if self.fw_big_cell_list then
        self.fw_big_cell_list:DeleteMe()
        self.fw_big_cell_list = nil
    end
    ]]
    if self.merge_leiji_list then
        self.merge_leiji_list:DeleteMe()
        self.merge_leiji_list = nil
    end

    if self.zhenxi_list then
        for k, v in pairs(self.zhenxi_list) do
            v:DeleteMe()
        end
        self.zhenxi_list = {}
    end

    if self.zixuan_list then
        for k, v in pairs(self.zixuan_list) do
            v:DeleteMe()
        end
        self.zixuan_list = {}
    end

    -- if self.niudan_item_data_change then
    --     ItemWGData.Instance:UnNotifyDataChangeCallBack(self.niudan_item_data_change)
    --     self.niudan_item_data_change = nil
    -- end
    self.is_play_niudan_ani = false
    self:CancleAllNiuDanQuest()

    self:LimitGiftCleanTimer()
    self:KillFloatTweener()
end

function MergeActivityView:CancleAllNiuDanQuest()
    if self.play_niudan_ani then
        GlobalTimerQuest:CancelQuest(self.play_niudan_ani)
        self.play_niudan_ani = nil
        self.is_play_niudan_ani = false
        self.node_list["draw_effect"]:SetActive(false)
    end
end

function MergeActivityView:InitFireworksView()
	self.node_list["btn_fw_record"].button:AddClickListener(BindTool.Bind1(self.OnClickFwsRecord, self))
	self.node_list["btn_reward_preview"].button:AddClickListener(BindTool.Bind1(self.OnClickShowRewardPreview, self))
	self.node_list["btn_limited_gift"].button:AddClickListener(BindTool.Bind1(self.OnClickShowLimitedGift, self))

    XUI.AddClickEventListener(self.node_list["btn_reset_reward"], BindTool.Bind(self.OnClickZiXuanBtn, self))
    for i = 1, 3 do
        XUI.AddClickEventListener(self.node_list["btn_sel_reward_" .. i], BindTool.Bind(self.OnClickZiXuanBtn, self))
    end

	for i = 1, 3 do
		self.node_list["btn_fw_buy_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickFwsDraw, self, i))
	end
    self:CreateFwsShowCell()
    --self.node_list["niudan_btn_ignore_ani"].button:AddClickListener(BindTool.Bind(self.OnClickNiuDanIgnore, self))
    
    -- self.niudan_item_data_change = BindTool.Bind(self.OnFwsItemChange, self)
    -- ItemWGData.Instance:NotifyDataChangeCallBack(self.niudan_item_data_change)
end

--跳过
function MergeActivityView:OnClickNiuDanIgnore()
    local is_ignore = MergeFireworksWGData.Instance:GetSkipAnim() == 1
    MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.ANIM_FLAG, is_ignore and 0 or 1)
end

--展示的格子
function MergeActivityView:CreateFwsShowCell()
    --[[
    self.fw_big_cell_list = AsyncListView.New(MergeFireworksShowRender,self.node_list["fw_big_cell_list"])
    ]]
    self.merge_leiji_list = AsyncListView.New(MergeFireworksFanliRender, self.node_list["merge_leiji_list"])
    self.zhenxi_list = {}
    for i = 1, 5 do
        self.zhenxi_list[i] = ItemCell.New(self.node_list["item_pos_" .. i])
        self.zhenxi_list[i]:SetIsUseRoundQualityBg(true)
		self.zhenxi_list[i]:SetCellBgEnabled(false)
    end

    -- 自选格子
    self.zixuan_list = {}
    for i = 1, 3 do
        self.zixuan_list[i] = ItemCell.New(self.node_list["sel_item_pos_" .. i])
        self.zixuan_list[i]:SetIsUseRoundQualityBg(true)
		self.zixuan_list[i]:SetCellBgEnabled(false)
    end

    self:PlayFloatTween()
end


--更新日志和协议信息
function MergeActivityView:ShowIndexFireworks()
    self:SetOutsideRuleTips(Language.MergeFireworks.OutSideTip)
    self:SetFireworksRuleTip()
    MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
    MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
    MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BAODI_REWARD_INFO)
end

--参数刷新
function MergeActivityView:OnFlushFireworks(param)
    for i, v in pairs(param) do
        if v == "all" then
            self:FlushFwsViewShow()
        elseif v == "record" then
            self:FlushFwsRecordRed()
        elseif v == "sel_reward" then
            self:FlushSelectedRewardShow()
        elseif v == "play_ani" then
            --self:PlayNiuDanjiAni()
        -- elseif v == "reset_ani" then
        --     self.is_play_niudan_ani = false
        end
    end
end

function MergeActivityView:PlayNiuDanjiAni(callback)
    self:CancleAllNiuDanQuest()
    local ignore_anim = MergeFireworksWGData.Instance:GetSkipAnim() == 1
    if ignore_anim then
        return
    end

    self.is_play_niudan_ani = true
    self.node_list["draw_effect"]:SetActive(true)
    self.play_niudan_ani = GlobalTimerQuest:AddDelayTimer(function()
        self.node_list["draw_effect"]:SetActive(false)
        self.play_niudan_ani = nil
        self.is_play_niudan_ani = false
        if callback then callback() end
    end, 2.6)
end

function MergeActivityView:KillFloatTweener()
    if self.float_tweener_list then
        for i, v in ipairs(self.float_tweener_list) do
            v:Kill()
        end
        self.float_tweener_list = nil
    end
end

-- 浮动动画
function MergeActivityView:PlayFloatTween()
    self:KillFloatTweener()
    self.float_tweener_list = {}
    for i = 1, 8 do
		local tween_root = self.node_list["move_node_" .. i].rect
		local random_time = math.random(900, 1200)
		local tweener = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 10, random_time / 1000)
		tweener:SetEase(DG.Tweening.Ease.InOutSine)
		tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
        tweener.fullPosition = math.random() * random_time
        tweener:Play()
        self.float_tweener_list[i] = tweener
    end
end

--刷新界面
function MergeActivityView:FlushFwsViewShow()
    self:FlushIgnoreState()
    self:FlushFwsBtnShow()
    self:FlushFwsShowCell()
    self:FlushFwsBaoDiTimes()
    self:FlushSelectedRewardShow()
    self:FlushLimitGift()

    -- 引导
    local is_first_enter = MergeFireworksWGData.Instance:GetIsFirstEnter()
    if is_first_enter and is_first_enter == 0 then
        FunctionGuide.Instance:TriggerMergeFireworksGoGuide()
        MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.FIRST_ENTER)
        MergeFireworksWGData.Instance:SetIsFirstEnter()
    end
end

function MergeActivityView:FlushIgnoreState()
    local is_ignore = MergeFireworksWGData.Instance:GetSkipAnim() == 1
    self.node_list["niudan_ignore_img"]:SetActive(is_ignore)
end

--3个按钮刷新，传参数只刷新数量
function MergeActivityView:FlushFwsBtnShow(is_flush_num)
    local cfg = MergeFireworksWGData.Instance:GetConsumeCfg()
    local item_cfg
    local count, asset
    for i = 1, 3 do
        if cfg[i] then
            local item_id = cfg[i].yanhua_item.item_id
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then               
                    --道具图标
                    self.node_list["fw_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                    self.node_list["fw_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowNiuDanItemTips, self, item_id))
                     --特效
                    --asset = BaseCell_Ui_Circle_Effect[item_cfg.zhengui_effect]           
                end
                --按钮价格
                --self.node_list["txt_fw_buy_" .. i].text.text = string.format(Language.MergeFireworks.TxtBuy, cfg[i].onekey_lotto_num)
                --折扣
                --[[暂时不显示折扣
                self.node_list["fw_discount_" .. i]:SetActive(cfg[i].discount_text ~= "")
                if cfg[i].discount_text ~= "" then
                    self.node_list["txt_fw_discount_" .. i].text.text = cfg[i].discount_text .. "折"
                end
                ]]
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            --self.node_list["fw_red_" .. i]:SetActive(true)
            self.node_list["niudan_red_" .. i]:SetActive(count >= cfg[i].yanhua_item.num)
            local color = count >= cfg[i].yanhua_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
            local left_str = ToColorStr(count, color) 
            self.node_list["fw_red_num_" .. i].text.text = left_str .."/".. cfg[i].yanhua_item.num
        end
    end
end

function MergeActivityView:ShowNiuDanItemTips(item_id)
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end

function MergeActivityView:FlushFwsRecordRed()
	--大奖记录红点
    local read_time = RoleWGData.GetRolePlayerPrefsInt("merge_fireworks_record")
    local new_record_time = MergeFireworksWGData.Instance:GetRecordTime()
    self.node_list["fw_record_red"]:SetActive(new_record_time > read_time)
end

--展示格子刷新
function MergeActivityView:FlushFwsShowCell()
    local show_cell_list1, baodi_list = MergeFireworksWGData.Instance:GetShowCellList()
    local flag_list = MergeFireworksWGData.Instance:GetFanliListNew()
    --self.fw_big_cell_list:SetDataList(show_cell_list1) --珍稀奖励
    self.merge_leiji_list:SetDataList(flag_list) --累计砸蛋

    for k, v in pairs(self.zhenxi_list) do
        v:SetData(baodi_list[k].reward_item)
    end

    local draw_times = MergeFireworksWGData.Instance:GetCurDrawTimes()
    self.node_list["txt_draw_count"].text.text = draw_times
end

--保底次数显示
function MergeActivityView:FlushFwsBaoDiTimes()
    local next_times = MergeFireworksWGData.Instance:GetNextBaoDiTimes()
    self.node_list["txt_baodi_count"].text.text = string.format(Language.MergeFireworks.DrawTimes, next_times)
end

--刷新自选奖励
function MergeActivityView:FlushSelectedRewardShow()
    local reward_id_list = MergeFireworksWGData.Instance:GetSelectedRewardList()
    if not self.zixuan_list or IsEmptyTable(reward_id_list) then
        return
    end
    
    for i, v in ipairs(self.zixuan_list) do
        local select_reward_id = reward_id_list[i]
        self.node_list["sel_item_pos_"..i]:SetActive(select_reward_id ~= 0)
        self.node_list["btn_sel_reward_"..i]:SetActive(select_reward_id == 0)
        if select_reward_id ~= 0 then
            local cfg = MergeFireworksWGData.Instance:GetCurRewardById(select_reward_id)
            if cfg then
                v:SetData(cfg.reward_item)
            end
        end
    end
end

--点击自选
function MergeActivityView:OnClickZiXuanBtn()
    MergeFireworksWGCtrl.Instance:OpenSelectBaoDiView()
end

function MergeActivityView:FlushLimitGift()
    local is_show_limit_gift = false
    local show_data_list = LimitTimeGiftWGData.Instance:GetCurrShowDataList()
	if IsEmptyTable(show_data_list) then
        self.node_list.btn_limited_gift:SetActive(is_show_limit_gift)
		return
	end

    local end_time = 0
    for i, v in ipairs(show_data_list) do
        if v.popup_type == LIMIT_TIME_GIFT_POPUP_TYPE.AFTER_DRAW then
            is_show_limit_gift = true
            end_time = v.end_time
            break
        end
    end

    self.node_list.btn_limited_gift:SetActive(is_show_limit_gift)

    if is_show_limit_gift then
        -- 刷新倒计时
        self:FlushLimitGiftTimeCountDown(end_time)
    end
end

--ruletip
function MergeActivityView:SetFireworksRuleTip()
    local grade_cfg = MergeFireworksWGData.Instance:GetGradeCfg()
    if not IsEmptyTable(grade_cfg) then
        local desc = grade_cfg.tip_desc
        local title = grade_cfg.tip_title or Language.MergeFireworks.TipTitle
        local probility_str = MergeFireworksWGData.Instance:GetItemsProbility()
        desc = desc .. probility_str
        self:SetRuleInfo(desc,title)
    end
end

--打开记录
function MergeActivityView:OnClickFwsRecord()
    MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
    MergeFireworksWGData.Instance:UpdateRecordCount()
    local record_list = MergeFireworksWGData.Instance:GetRecordInfo()
    TipWGCtrl.Instance:OpenTipsRewardRecordView(record_list)
end

--打开奖励预览.
function MergeActivityView:OnClickShowRewardPreview()
    local data_list =
	{
        view_color = RewardShowViewColor.Purple,
		view_type = RewardShowViewType.Title,
		title_reward_item_data = MergeFireworksWGData.Instance:GetShowRewardPerviewList()
	}

    RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end

--打开折扣礼包.
function MergeActivityView:OnClickShowLimitedGift()
    LimitTimeGiftWGCtrl.Instance:OpenLimitTimeGiftPurchaseView()
end

--点击抽奖
function MergeActivityView:OnClickFwsDraw(draw_type)
    if self.is_play_niudan_ani then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeFireworks.PlayAni)
        return
    end
    
    local is_select_reward = MergeFireworksWGData.Instance:CheckIsSelectedBigReward()
    if not is_select_reward then
        MergeFireworksWGCtrl.Instance:OpenSelectBaoDiView()
        return
    end

	--check enough
    MergeFireworksWGData.Instance:CacheOrGetDrawIndex(draw_type)
	local cfg = MergeFireworksWGData.Instance:GetConsumeCfg()
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.yanhua_item.item_id)
	if num >= cfg.yanhua_item.num then
		--足够就关闭界面放特效
        self:PlayNiuDanjiAni(function()
            MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, cfg.onekey_lotto_num, 1)
        end)
	else
		--不足够买
        MergeFireworksWGCtrl.Instance:ClickUse(draw_type, function()
            self:OnClickFwsBuy(draw_type)
        end)
	end

    LimitTimeGiftWGCtrl.Instance:CheckAfterDrawPopupGift()
end

--点击购买
function MergeActivityView:OnClickFwsBuy(draw_type)
    local cfg = MergeFireworksWGData.Instance:GetConsumeCfg()
    local cur_cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.yanhua_item.item_id)
    local consume = cfg[1].consume_count * (cur_cfg.yanhua_item.num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        MergeFireworksWGData.Instance:CacheOrGetDrawIndex(draw_type)
        self:PlayNiuDanjiAni(function()
            MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, cfg[draw_type].onekey_lotto_num)
        end)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--物品监听刷新按钮显示
function MergeActivityView:OnFwsItemChange(item_id)
    local check_list = MergeFireworksWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == item_id then
            self:FlushFwsBtnShow(true)
            return
        end
    end
end

function MergeActivityView:LimitGiftCleanTimer()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
    end
end

function MergeActivityView:FlushLimitGiftTimeCountDown(end_time)
	self:LimitGiftCleanTimer()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local total_time = end_time - server_time
	if total_time <= 0 then
		return
	end

    self.node_list["txt_gift_countdown"].text.text = TimeUtil.FormatSecondDHM6(total_time)
    self.timer = CountDown.Instance:AddCountDown(total_time, 1, BindTool.Bind1(self.UpdateLimitGiftCountDown, self), BindTool.Bind1(self.OnCompleteLimitGift, self))
end

function MergeActivityView:UpdateLimitGiftCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list["txt_gift_countdown"].text.text = TimeUtil.FormatSecondDHM6(valid_time)
	end
end

function MergeActivityView:OnCompleteLimitGift()
    self.node_list["txt_gift_countdown"].text.text = ""
end

--------------------------------------------------------------------------------------------
MergeFireworksFanliRender = MergeFireworksFanliRender or BaseClass(BaseRender)
function MergeFireworksFanliRender:__delete()
    
end

function MergeFireworksFanliRender:LoadCallBack()
    self.node_list["btn_lq"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
    self.item_show_cell = ItemCell.New(self.node_list["item_cell_pos"])
end

function MergeFireworksFanliRender:ReleaseCallBack()
    if self.item_show_cell then
		self.item_show_cell:DeleteMe()
		self.item_show_cell = nil
    end
end

function MergeFireworksFanliRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data.data
    -- local reward = data.reward_item[0]
    -- local cfg = ItemWGData.Instance:GetItemConfig(reward.item_id)
    -- self.node_list["color_bg"].image:LoadSprite(ResPath.GetCommonBackGround('item_round_bg_' .. cfg.color))
    -- self.node_list["icon"].image:LoadSprite(ResPath.GetItem(cfg.icon_id))
    -- self.node_list["num"].text.text = reward.num
    local draw_times = MergeFireworksWGData.Instance:GetCurDrawTimes()
    local color = draw_times >= data.lotto_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    --local str = draw_times .. "/" .. data.lotto_num
    local desc = ToColorStr(data.lotto_num, color)
    self.node_list["get_flag"]:SetActive(self.data.has_get == 1)
    self.node_list["txt_times"].text.text = string.format(Language.MergeFireworks.TxtTimes2, desc, data.lotto_num)

    local is_show = data.lotto_num <= draw_times and self.data.has_get ~= 1
    self.node_list["remind"]:SetActive(is_show)
    self.node_list["btn_lq"]:SetActive(is_show)
    --XUI.SetButtonEnabled(self.node_list["btn_lq"], is_show)

    self.item_show_cell:SetData(data.reward_item[0])
    --[[
    local reward_data = {}
	if data.reward_item then
		for i=0,#data.reward_item do
			table.insert(reward_data,data.reward_item[i])
		end
	end
	reward_data = SortDataByItemColor(reward_data)
	self.item_list_view:SetDataList(reward_data)
    ]]
end

function MergeFireworksFanliRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = MergeFireworksWGData.Instance:GetCurDrawTimes()
    local data = self.data.data
    if draw_times < data.lotto_num or self.data.has_get == 1 then
        TipWGCtrl.Instance:OpenItem(data.reward_item[0])
    else
        MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.FETCH, data.index)
    end
end