
function SwornWGData:InitSuitData()
	self.jieyi_equip_map = ListToMap(self.jieyi_cfg.jieyi_equip, "part", "hole")
	self.jieyi_equip_map_hole = ListToMap(self.jieyi_cfg.jieyi_equip, "item_id")
	self.jieyi_equip_level_cfg = ListToMap(self.jieyi_cfg.jieyi_equip_level, "part", "level")
	self.jieyi_equip_star_cfg = ListToMap(self.jieyi_cfg.jieyi_equip_star, "part", "hole", "level")
	self.jieyi_equip_suit = ListToMap(self.jieyi_cfg.jieyi_equip_suit, "part", "seq")
	self.part_item_list = {}
	self.equip_uplevel_stuff_id_list = {}
	for k,v in pairs(self.jieyi_cfg.jieyi_equip_level) do
		self.equip_uplevel_stuff_id_list[v.cost_item_id] = true
	end
end

-- 是否是金兰装备
function SwornWGData:IsJinLanEquip(change_item_id)
	return self.jieyi_equip_map_hole[change_item_id] ~= nil
end

-- 根据金兰装备获取部位索引
function SwornWGData:GetJinLanEquipHole(item_id)
	local part, hole = 0, 0
	local data = self.jieyi_equip_map_hole[item_id]
	if data then
		part, hole = data.part, data.hole
	end

	return part, hole
end

-- 根据孔位获取物品id
function SwornWGData:GetJinLanEquipIDByHole(suit, hole)
	return ((self.jieyi_equip_map[suit] or {})[hole] or {})["item_id"] or 0
end

-- 根据套装获取孔位list
function SwornWGData:GetJinLanHoleList(suit)
	return self.jieyi_equip_map[suit] or {}
end

--所有套装信息
function SwornWGData:SetSuitAllInfo(protocol)
	self.part_item_list = protocol.part_item_list
end

--单个套装更新
function SwornWGData:UpdateSuitInfo(protocol)
	self.part_item_list[protocol.part] = protocol.part_item
end

-- 获取星级套装seq
function SwornWGData:GetStarSuitSeq(suit)
	return (self.part_item_list[suit] or {})["suit_active_flag"] or -1
end

-- 获取部位信息
function SwornWGData:GetHoleInfo(suit, hole)
	return (self.part_item_list[suit] or {})["hole_item_list"][hole]
end

--通过item_id 拿等级，星级
function SwornWGData:GetHoleInfoID(item_id)
	local part, hole = self:GetJinLanEquipHole(item_id)
	return self:GetHoleInfo(part, hole)
end

-- 获取部位是否激活
function SwornWGData:GetHoleIsAct(suit, hole)
	local info = self:GetHoleInfo(suit, hole)
	return info and info.star_level > 0
end

function SwornWGData:GetHoleIsActByItemId(item_id)
	local suit, hole = self:GetJinLanEquipHole(item_id)
	local info = self:GetHoleInfo(suit, hole)
	return info and info.star_level > 0
end

-- 套装是否有装备
function SwornWGData:GetSuitHadEquip(suit)
	local hole_list = self:GetJinLanHoleList(suit)
	for hole = 0, #hole_list do
		local data = hole_list[hole]
		if data and self:GetHoleIsAct(suit, hole) then
			return true
		end
	end

	return false
end

--星级等级配置
function SwornWGData:GetEquipStarCfg(suit, hole, star_level)
	return ((self.jieyi_equip_star_cfg[suit] or {})[hole] or {})[star_level]
end

-- 获取部位星级
function SwornWGData:GetHoleStarLevel(suit, hole)
	local info = self:GetHoleInfo(suit, hole)
	return info and info.star_level or 0
end

-- 获取部位最大星级
function SwornWGData:GetHoleMaxStarLevel(suit, hole)
	local star_level_cfg = (self.jieyi_equip_star_cfg[suit] or {})[hole] or {}
	local data = star_level_cfg[#star_level_cfg]
	return data and data.level or 0
end

function SwornWGData:GetIsStarMaxLevelByItemId(item_id)
	local suit, hole = self:GetJinLanEquipHole(item_id)
	local star_level = self:GetHoleStarLevel(suit, hole)
	local max_star_level = self:GetHoleMaxStarLevel(suit, hole)
	return star_level >= max_star_level
end

-- 获取部位等级
function SwornWGData:GetHoleLevel(suit, hole)
	local info = self:GetHoleInfo(suit, hole)
	return info and info.level or 0
end

--获取等级配置
function SwornWGData:GetEquipUpLevelCfg(suit, level)
	return (self.jieyi_equip_level_cfg[suit] or {})[level]
end

-- 获取最大等级
function SwornWGData:GetMaxLevel(suit)
	local level_cfg = self.jieyi_equip_level_cfg[suit] or {}
	return level_cfg[#level_cfg].level or 0
end

function SwornWGData:IsUpLevelStuffID(item_id)
	return self.equip_uplevel_stuff_id_list[item_id]
end

-- 套装红点 - 单个部位
function SwornWGData:GetHoleSuitRemind(suit, hole)
	local info = self:GetHoleInfo(suit, hole)
	if info and info.star_level <= 0 then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(info.item_id)
		return has_num >= 1
	end

	return false
end

-- 套装红点 - 单个套装
function SwornWGData:GetSuitSuitRemind(suit)
	local hole_list = self:GetJinLanHoleList(suit)
	for hole = 0, #hole_list do
		local data = hole_list[hole]
		if data and self:GetHoleSuitRemind(suit, hole) then
			return true
		end
	end

	-- 星级套装红点
	if self:GetStarSuitRemind(suit) then
		return true
	end

	return false
end

--  升星红点 -界面选择套装
function SwornWGData:GetViewSelectSuitRemind()
	local view_select_suit = SwornWGCtrl.Instance:GetViewSelectSuit()
	if self:GetSuitSuitRemind(view_select_suit) then
		return 1
	end

	return 0
end

-- 套装红点 - 全部
function SwornWGData:GetSuitAllRemind()
	for suit = 0, #self.part_item_list do
		if self:GetSuitSuitRemind(suit) then
			return true, suit
		end
	end

	return false, 0
end

-- 升星红点 - 单个部位
function SwornWGData:GetHoleUpStarRemind(suit, hole)
	local info = self:GetHoleInfo(suit, hole)
	if not info then
		return false
	end

	local is_act = info.star_level > 0
	if is_act then
		local max_star_level = self:GetHoleMaxStarLevel(suit, hole)
		local cur_star_level = info.star_level
		if cur_star_level >= max_star_level then
			return false
		end

		local star_level_cfg = self:GetEquipStarCfg(suit, hole, cur_star_level)
		if star_level_cfg then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(info.item_id)
			return has_num >= star_level_cfg.cost_item_num
		end
	end

	return false
end

-- 升星红点 - 单个套装
function SwornWGData:GetSuitUpStarRemind(suit)
	local hole_list = self:GetJinLanHoleList(suit)
	for hole = 0, #hole_list do
		local data = hole_list[hole]
		if data and self:GetHoleUpStarRemind(suit, hole) then
			return true, hole
		end
	end

	return false, 0
end

--  升星红点 -界面选择套装
function SwornWGData:GetViewSelectUpStarRemind()
	local view_select_suit = SwornWGCtrl.Instance:GetViewSelectSuit()
	if self:GetSuitUpStarRemind(view_select_suit) then
		return 1
	end

	return 0
end

-- 升星红点 - 全部
function SwornWGData:GetUpStarAllRemind()
	for suit = 0, #self.part_item_list do
		if self:GetSuitUpStarRemind(suit) then
			return true, suit
		end
	end

	return false, 0
end

-- 升级红点 - 单个部位
function SwornWGData:GetHoleUpLevelRemind(suit, hole)
	local info = self:GetHoleInfo(suit, hole)
	if not info then
		return false
	end

	local is_act = info.star_level > 0
	if is_act then
		local max_level = self:GetMaxLevel(suit)
		local cur_level = info.level
		if cur_level >= max_level then
			return false
		end

		local level_cfg = self:GetEquipUpLevelCfg(suit, cur_level)
		if level_cfg then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.cost_item_id)
			return has_num >= level_cfg.cost_item_num
		end
	end

	return false
end

-- 升级红点 - 单个套装
function SwornWGData:GetSuitUpLevelRemind(suit)
	local hole_list = self:GetJinLanHoleList(suit)
	for hole = 0, #hole_list do
		local data = hole_list[hole]
		if data and self:GetHoleUpLevelRemind(suit, hole) then
			return true, hole
		end
	end

	return false, 0
end

-- 升级红点 - 界面选择套装
function SwornWGData:GetViewSelectUpLevelRemind()
	local view_select_suit = SwornWGCtrl.Instance:GetViewSelectSuit()
	if self:GetSuitUpLevelRemind(view_select_suit) then
		return 1
	end

	return 0
end

-- 升级红点 - 全部
function SwornWGData:GetUpLevelAllRemind()
	for suit = 0, #self.part_item_list do
		if self:GetSuitUpLevelRemind(suit) then
			return true, suit
		end
	end

	return false, 0
end

-- 装备所有红点
function SwornWGData:GetEquipAllRemind()
	if self:GetSuitAllRemind() then
		return 1
	end

	if self:GetUpStarAllRemind() then
		return 1
	end

	if self:GetUpLevelAllRemind() then
		return 1
	end

	return 0
end

-- 所有套装的所有功能红点列表
function SwornWGData:GetAllSuitEquipRemindList()
	local list = {}
	for suit = 0, #self.part_item_list do
		list[suit] = self:GetSuitEquipAllRemind(suit)
	end

	return list
end

-- 套装装备的所有功能红点列表
function SwornWGData:GetSuitEquipAllRemind(suit)
	if self:GetSuitSuitRemind(suit) then
		return true, TabIndex.sworn_suit
	end

	if self:GetSuitUpStarRemind(suit) then
		return true, TabIndex.sworn_upstar
	end

	if self:GetSuitUpLevelRemind(suit) then
		return true, TabIndex.sworn_uplevel
	end

	return false, TabIndex.sworn_suit
end

-- 根据套装跳标签
function SwornWGData:GetSuitJumpIndex(suit)
	local is_had_equip = self:GetSuitHadEquip(suit)
	if not is_had_equip then
		return TabIndex.sworn_suit
	end

	local is_remind, table_index = self:GetSuitEquipAllRemind(suit)
	return table_index
end

-- 获取套装列表
function SwornWGData:GetSuitHoleList(suit)
	local hole_list = {}
	local cfg_hole_list = self:GetJinLanHoleList(suit)
	for hole, cfg in pairs(cfg_hole_list) do
		local data = {
			suit = suit,
			hole = hole,
			item_id = cfg.item_id,
			level = self:GetHoleLevel(suit, hole),
			star_level = self:GetHoleStarLevel(suit, hole),
		}
		hole_list[hole] = data
	end

	return hole_list
end

--当前套装激活配置
function SwornWGData:GetStarSuitCfg(suit, seq)
	return (self.jieyi_equip_suit[suit] or {})[seq]
end

-- 满足星级套装 对应星级的数量
function SwornWGData:GetStarSuitEnoughStarNum(suit, need_star_level)
	local enough_num = 0
	local cfg_hole_list = self:GetJinLanHoleList(suit)
	for hole, cfg in pairs(cfg_hole_list) do
		local star_level = self:GetHoleStarLevel(suit, hole)
		if star_level >= need_star_level then
			enough_num = enough_num + 1
		end
	end

	return enough_num
end

-- 星级套装红点
function SwornWGData:GetStarSuitRemind(suit)
	local star_suit_seq = self:GetStarSuitSeq(suit)
	local next_cfg = self:GetStarSuitCfg(suit, star_suit_seq + 1)
	if not next_cfg then
		return false
	end

	local enough_num = self:GetStarSuitEnoughStarNum(suit, next_cfg.need_level)
	return enough_num >= next_cfg.need_num
end

-- 升星 - 跳到孔位
function SwornWGData:GetSuitUpStarJumpHole(suit, hole_index)
	local hole_list = self:GetJinLanHoleList(suit)
	local jump_hole

	if hole_list[hole_index] then
		if self:GetHoleUpStarRemind(suit, hole_index) then
			return hole_index
		end
	end

	for hole = 0, #hole_list do
		local data = hole_list[hole]
		if data then
			if self:GetHoleUpStarRemind(suit, hole) then
				return hole
			end

			if jump_hole == nil and self:GetHoleIsAct(suit, hole) then
				jump_hole = hole
			end
		end
	end

	return jump_hole
end

-- 升级 - 跳到孔位
function SwornWGData:GetSuitUpLevelJumpHole(suit, select_hole)
	if select_hole >= 0 then
		if self:GetHoleUpLevelRemind(suit, select_hole) then
			return select_hole
		end
	end

	local hole_list = self:GetJinLanHoleList(suit)
	local jump_hole

	for hole = 0, #hole_list do
		local data = hole_list[hole]

		if data then
			if self:GetHoleUpLevelRemind(suit, hole) then
				return hole
			end

			if jump_hole == nil and self:GetHoleIsAct(suit, hole) then
				jump_hole = hole
			end
		end
	end

	return select_hole >= 0 and select_hole or jump_hole
end

--星级属性
function SwornWGData:GetJinLanEquipStarAttrList(item_id, star_level, no_sort)
	local attr_list = {}
	local part, hole = self:GetJinLanEquipHole(item_id)
	local cfg = self:GetEquipStarCfg(part, hole, star_level)
	if IsEmptyTable(cfg) then
        return attr_list
    end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
		
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

            table.insert(attr_list, data)
        end
    end

	if not no_sort and not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

-- 等级属性
function SwornWGData:GetJinLanEquipLevelAttrList(item_id, level, no_sort)
	local attr_list = {}
	local part, hole = self:GetJinLanEquipHole(item_id)
	local cfg = self:GetEquipUpLevelCfg(part, level)
	if IsEmptyTable(cfg) then
        return attr_list
    end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

            table.insert(attr_list, data)
        end
    end

	if not no_sort and not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

-- 星级套装属性
function SwornWGData:GetStarSuitAttrList(suit, suit_seq, no_sort)
	local attr_list = {}
	local star_suit_cfg = self:GetStarSuitCfg(suit, suit_seq)
	if not star_suit_cfg then
		return attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
		attr_id = star_suit_cfg["attr_id" .. i]
		attr_value = star_suit_cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
			local data = {
				attr_str = attr_str,
                attr_value = attr_value,
			}

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

			table.insert(attr_list, data)
		end
	end

	if not no_sort and not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

--获取当前套装总战力
function SwornWGData:GetSuitCapability(suit)
	local attribute = AttributePool.AllocAttribute()

	-- 套装激活属性
	local suit_seq = self:GetStarSuitSeq(suit)
	local attr_list = self:GetStarSuitAttrList(suit, suit_seq, true)
	for k,v in pairs(attr_list) do
		attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
	end

	--装备属性
	local hole_list = self:GetSuitHoleList(suit)
	for hole, data in pairs(hole_list) do
		if data.star_level > 0 then
			-- 星级属性
			local star_attr_list = self:GetJinLanEquipStarAttrList(data.item_id, data.star_level, true)
			for k,v in pairs(star_attr_list) do
				attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
			end

			-- 等级属性
			local level_attr_list = self:GetJinLanEquipLevelAttrList(data.item_id, data.level, true)
			for k,v in pairs(level_attr_list) do
				attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
			end
		end
	end

	return AttributeMgr.GetCapability(attribute)
end

