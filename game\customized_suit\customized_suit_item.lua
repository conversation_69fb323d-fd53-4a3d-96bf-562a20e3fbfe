
CustomizedSuitCell = CustomizedSuitCell or BaseClass(BaseRender)

function CustomizedSuitCell:__init()
end

function CustomizedSuitCell:LoadCallBack()

end

function CustomizedSuitCell:__delete()

end

function CustomizedSuitCell:OnFlush()
    if self.data == nil then
        return
    end

    -- TODO 图片替换
    -- local bundle, asset = ResPath.GetCustomizedImg("a2_jndi_suit_" .. self.data.suit)
    -- self.node_list.cell_icon.image:LoadSprite(bundle, asset)
    local suit_skill_red = CustomizedSuitWGData.Instance:GetOneThemeRed(self.data.suit, false)
    local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.data.suit)
    if suit_data then
        self.node_list.remind:SetActive((suit_data.can_act and self.data.state ~= REWARD_STATE_TYPE.UNDONE) or suit_skill_red)
    end

    self.node_list.text_name.text.text = suit_data.suit_name
    self.node_list.text_name_hl.text.text = suit_data.suit_name

	local cur_data = CustomizedSuitWGData.Instance:GetThemeSkillBySuit(self.data.suit)
	if not cur_data then
		return
	end

    local star_res_list = GetStarImgResByStar(cur_data.star)
    for i = 1, 5 do
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end
end

function CustomizedSuitCell:OnSelectChange(is_select)
    self.node_list.select_hl:SetActive(is_select)
    self.node_list.text_name_hl:SetActive(is_select)
    self.node_list.img_name_bg_hl:SetActive(is_select)
    self.node_list.text_name:SetActive(not is_select)
    self.node_list.root.rect.anchoredPosition = is_select and Vector2(20, 0) or Vector2(0, 0)
end

function CustomizedSuitCell:PalySuitItemAnim(item_index,select_index)
    if not self.node_list["root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.CustomizedSuitView.ListCellRender

    if select_index ~= nil and select_index == item_index then
        tween_info.EndPosition = Vector2(20, 0)
    else
        tween_info.EndPosition = Vector2(0, 0)
    end
    UITween.FakeHideShow(self.node_list["root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["root"] then
            UITween.MoveAlphaShow(GuideModuleName.CustomizedSuitView, self.node_list["root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "suit_item_" .. wait_index)
end
----------------
CustomizedSuitPartRender = CustomizedSuitPartRender or BaseClass(BaseRender)
function CustomizedSuitPartRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
        self.item_cell.is_showtip = false
    end
end

function CustomizedSuitPartRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function CustomizedSuitPartRender:OnFlush()
    if self.data == nil then
        return
    end

    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.data.suit_seq, self.data.part)
    
    if self.node_list.remind then
        self.node_list.remind:SetActive(part_data and part_data.red)
    end

    self.view:SetActive(true)
    self.item_cell:SetData({item_id = self.data.show_item_id})
    self.item_cell:MakeGray(self.data.state == REWARD_STATE_TYPE.UNDONE)

    -- 升级
    local star_level = CustomizedSuitWGData.Instance:GetSuitPartStar(self.data)
    self.node_list["stars_list"]:CustomSetActive(self.data.state ~= REWARD_STATE_TYPE.UNDONE)
    if self.data.state ~= REWARD_STATE_TYPE.UNDONE then
        local star_res_list = GetSuitStarImgResByStar(star_level)
        for i = 1, 5 do
            self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
        end
    end
end

function CustomizedSuitPartRender:OnSelectChange(is_select)
    self.item_cell:SetSelectEffectSp(is_select)
end

function CustomizedSuitPartRender:OnShowItemClick()
    if self.item_cell and self.data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.show_item_id)
		if nil == item_cfg then return end

        TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
		if self.need_item_get_way then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.show_item_id})
		else
			TipWGCtrl.Instance:OpenItem(self.item_cell.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil, nil, self.item_tips_btn_click_callback)
		end
    end
end
---
GuiCustomizedAttrRender = GuiCustomizedAttrRender or BaseClass(BaseRender)
local AtrrColorText = {
    [1] = "#79fa82",
    [2] = "#fab379",
    [3] = "#ff9292",
    [4] = "#ffe58c",
    [5] = "#fa7994",
    [6] = "#fa7994",
}
function GuiCustomizedAttrRender:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
    end
end

function GuiCustomizedAttrRender:__delete()
    self.attr_list = nil
end

function GuiCustomizedAttrRender:SetFromItemTip(bool)
    self.is_from_itemtip = bool
end

function GuiCustomizedAttrRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
	end

    local attri_color = COLOR3B.WHITE
    if data.is_act then
        attri_color = COLOR3B.D_GREEN --(AtrrColorText[self.index] or COLOR3B.DEFAULT_NUM)
    else
        attri_color = self.is_from_itemtip and TIPS_COLOR.ATTR_NAME or COLOR3B.BLUE_TITLE--COLOR3B.DEFAULT_NUM
    end

    local need_str
    if self.is_from_itemtip then
        if self.node_list.suit_icon then
            XUI.SetGraphicGrey(self.node_list.suit_icon, not data.is_act)
        end
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc1 or string.format(Language.Wardrobe.SuitNumCompany1, data.need_num)
    else
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc or string.format(Language.Wardrobe.SuitNumCompany, data.need_num)
    end

    self.node_list.need_num.text.text = ToColorStr(need_str, attri_color)
    local list = data.attr_list

    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = ""
            local attr_str = ""
            local value = ""
            if list[k].attr_type == "add_per" then
                name = Language.GuiXuDream.SpecialAttr
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                local format_str = self.is_from_itemtip and "%s   %s" or "%s  %s"
                attr_str = string.format(format_str, name, value)
            else
                name = EquipmentWGData.Instance:GetAttrName(list[k].attr_type, false)
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                if self.is_from_itemtip then
                    attr_str = string.format("%s   %s", name, value)         
                elseif is_per then
                    attr_str = string.format("%s      %s", name, value)
                else
                    attr_str = string.format("%s  %s", name, value)
                end
            end

            v.text.text = ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end