-- 道具不足弹窗，带获取途径
TipsNoItemView = TipsNoItemView or BaseClass(SafeBaseView)

function TipsNoItemView:__init()
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_no_item_tips_panel")
end

function TipsNoItemView:ReleaseCallBack()
    if self.way_btn_list then
        for index = 1, #self.way_btn_list do
            self.way_btn_list[index]:DeleteMe()
        end
        self.way_btn_list = nil
    end

    if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end
end

function TipsNoItemView:LoadCallBack()
    self.way_btn_list = {}

    self.max_way_num = self.node_list.way_btn_node.transform.childCount

    self.show_item = ItemCell.New(self.node_list.item_node)
end

function TipsNoItemView:SetData(item_id, other_info)
    self.item_id = item_id
    self.other_info = other_info
end

function TipsNoItemView:OnFlush()
    if not self.item_id then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.item_id)
    if not item_cfg then
        return
    end

    self.show_item:SetData({item_id = self.item_id})
    local name = ItemWGData.Instance:GetItemName(self.item_id, nil, true)
    local num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
    self.node_list.has_num.text.text = string.format(Language.TipsNoItem.ItemNum, name, num)
    self.node_list.tips.text.text = string.format(Language.TipsNoItem.WayTips)

    self:FlushWayBtn(self.item_id)
end

function TipsNoItemView:FlushWayBtn(item_id)
    if not self.max_way_num or not self.way_btn_list then
        return
    end

    local list = TipWGData.Instance:GetGetWayList(item_id)
    for index = 1, self.max_way_num do
        local btn = self.way_btn_list[index]
        if not btn then
            btn = WayBtnCell.New(self.node_list["btn_" .. index])
            self.way_btn_list[index] = btn
        end
        local data = list[index]
        if data then
            if self.other_info then
                data.other_info = self.other_info
            end
            btn:SetVisible(true)
            btn:SetData(data)
        else
            btn:SetVisible(false)
        end
    end
end

--------------------------- WayBtnCell ------------------------------
WayBtnCell = WayBtnCell or BaseClass(BaseRender)

function WayBtnCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.GoTo, self))
end

function WayBtnCell:OnFlush()
    local info = self.data and self.data.cfg
    if not info then
        return
    end

    self.node_list.btn_text.text.text = info.open_name or ""

    if info.icon and info.icon ~= "" then
        local bundle, asset = ResPath.GetCommonImages(info.icon)
        self.node_list.icon.image:LoadSprite(bundle, asset, function ()
            self.node_list.icon.image:SetNativeSize()
        end)
    end
end

function WayBtnCell:GoTo()
    local open_panel = self.data and self.data.cfg and self.data.cfg.open_panel
    if open_panel and open_panel ~= "" then
        if  self.data.other_info and  self.data.other_info.cb then
            self.data.other_info.cb()
        end
        ViewManager.Instance:OpenByCfg(open_panel)
        ViewManager.Instance:Close(GuideModuleName.TipsNoItemView)
    end
end