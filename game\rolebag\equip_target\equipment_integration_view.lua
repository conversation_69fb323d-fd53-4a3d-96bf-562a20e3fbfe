local LEVEL_DELT_TIME = 0.5 --0.5s升级一次
function RoleBagView:InitEIView()
    self.ei_select_big_type = -1
    self.ei_select_suit_data = nil
    self.ei_select_small_type = -1
    self.ei_select_slot_index = -1
    -- 装备列表
    if self.ei_equip_list == nil then
        self.ei_equip_list = {}
        local node_num = 8
        for i = 0, node_num - 1 do
            self.ei_equip_list[i] = EIEquipRender.New(self.node_list["equip_" .. i])
            self.ei_equip_list[i]:SetIndex(i)
            self.ei_equip_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectEISlotCallBack, self))
        end
    end

    -- 属性列表
    if self.ei_attr_list == nil then
        self.ei_attr_list = {}
        local node_num = self.node_list["ei_attr_list"].transform.childCount
        for i = 1, node_num do
            self.ei_attr_list[i] = CommonAddAttrRender.New(self.node_list["ei_attr_list"]:FindObj("attr_" .. i))
            self.ei_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    if self.ei_level_attr_list == nil then
        self.ei_level_attr_list = {}
        local node_num = self.node_list["ei_level_attr_list"].transform.childCount
        for i = 1, node_num do
            self.ei_level_attr_list[i] = CommonAddAttrRender.New(self.node_list["ei_level_attr_list"]:FindObj("attr_" .. i))
            self.ei_level_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    -- 消耗材料
    --self.ei_act_stuff_item = ItemCell.New(self.node_list["ei_act_stuff_item"])
    --套装升级材料
    self.ei_suit_level_stuff_item = ItemCell.New(self.node_list["ei_suit_level_stuff_item"])
    -- 标签列表
    self:CreatEIToggleList()

    --XUI.AddClickEventListener(self.node_list["ei_btn_act"], BindTool.Bind(BindTool.Bind(self.OnClickEIAct, self)))
    XUI.AddClickEventListener(self.node_list["ei_auto_level_btn"], BindTool.Bind(BindTool.Bind(self.AutoUpLevel, self)))
    XUI.AddClickEventListener(self.node_list["ei_reslove_btn"], BindTool.Bind(BindTool.Bind(self.OnClickGoReslove, self)))
    XUI.AddClickEventListener(self.node_list["ei_skill_icon"], BindTool.Bind(BindTool.Bind(self.OnClicEISkillBtn, self)))
    XUI.AddClickEventListener(self.node_list.btn_attr_show, BindTool.Bind(self.OnClickAttrShowBtn, self))
    XUI.AddClickEventListener(self.node_list.ei_upgrade_btn, BindTool.Bind(self.OnClickEIUpgradeBtn, self))

    self.is_auto_level = false
end

function RoleBagView:CreatEIToggleList()
    -- print_error("---创建标签---")
    if nil == self.ei_accordion_list then
        self.ei_suit_cell_group = {}
        self.ei_accordion_list = {}
        self.ei_big_type_toggle_list = {}
        self.load_ei_cell_complete = false
        local content_node = self.node_list["ei_toggle_content"]
        local toggle_list = EquipTargetWGData.Instance:GetEIToggleList()
        local toggle_length = #toggle_list
        for i = 1, toggle_length do
            self.ei_accordion_list[i] = content_node:FindObj("List" .. i)
            local big_btn_cell = EIBigTypeToggleRender.New(content_node:FindObj("SelectBtn" .. i))
            big_btn_cell:SetIndex(i)
            big_btn_cell:SetOnlyClickCallBack(BindTool.Bind(self.OnClickEIBigTypeToggle, self))
            self.ei_big_type_toggle_list[i] = big_btn_cell

            local suit_length = toggle_list[i].child_list and #toggle_list[i].child_list or 0
            self:LoadEISuitCellList(i, toggle_length, suit_length)
        end
    end
end

function RoleBagView:EIReleaseCallBack()
    self:CleanEIJumpWaitCDTime()

    self.ei_stop_cur = nil
    self.ei_flush_wait_flag = nil
    self.ei_force_big_type = nil
    self.ei_force_suit_type = nil
    self.ei_force_slot_index = nil
    self.ei_accordion_list = nil
    self.load_ei_cell_complete = nil
    self.ei_select_big_type = nil
    self.ei_select_suit_data = nil
    self.ei_select_small_type = nil
    self.ei_select_slot_index = nil
    self.ei_select_slot_data = nil
    self.is_auto_level = false

    if self.ei_equip_list then
        for k, v in pairs(self.ei_equip_list) do
            v:DeleteMe()
        end
        self.ei_equip_list = nil
    end

    if self.ei_attr_list then
        for k, v in pairs(self.ei_attr_list) do
            v:DeleteMe()
        end
        self.ei_attr_list = nil
    end

    if self.ei_level_attr_list then
        for k, v in pairs(self.ei_level_attr_list) do
            v:DeleteMe()
        end
        self.ei_level_attr_list = nil
    end

    -- if self.ei_act_stuff_item then
    --     self.ei_act_stuff_item:DeleteMe()
    --     self.ei_act_stuff_item = nil
    -- end

    if self.ei_suit_level_stuff_item then
        self.ei_suit_level_stuff_item:DeleteMe()
        self.ei_suit_level_stuff_item = nil
    end

    if self.ei_big_type_toggle_list ~= nil then
        for k, v in ipairs(self.ei_big_type_toggle_list) do
            v:DeleteMe()
        end
        self.ei_big_type_toggle_list = nil
    end

    if self.ei_suit_cell_group then
        for k, v in pairs(self.ei_suit_cell_group) do
            for k1, v1 in pairs(v) do
                v1:DeleteMe()
            end
            self.ei_suit_cell_group[k] = nil
        end
        self.ei_suit_cell_group = nil
    end

    if self.ei_active_alert then
		self.ei_active_alert:DeleteMe()
		self.ei_active_alert = nil
	end

    self:CancelAutoLevelTimer()
end

function RoleBagView:EIShowIndexCallBack()

end

-- 加载子标签
function RoleBagView:LoadEISuitCellList(index, big_type_num, suit_num)
    -- print_error("---加载子标签---", index)
    local res_async_loader = AllocResAsyncLoader(self, "ei_suit_item" .. index)
	res_async_loader:Load("uis/view/rolebag_ui/equip_target_prefab", "suit_seq_toggle", nil, function(new_obj)
		local item_vo_list = {}
		for i = 1, suit_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.ei_accordion_list[index].transform, false)
			-- obj:GetComponent("Toggle").group = self.ei_accordion_list[index].toggle_group

			local item_render = EISuitToggleRender.New(obj)
            item_render:SetClickCallBack(BindTool.Bind(self.OnClickEISuitToggle, self))
            item_render:SetIndex(i)
			item_vo_list[i] = item_render

			if index == big_type_num and i == suit_num then
				self.load_ei_cell_complete = true
			end
		end

		self.ei_suit_cell_group[index] = item_vo_list
		if self.load_ei_cell_complete then
            -- 设置数据
            self:FlushEIToggleAllData()

            -- 加载完 是否要选择标签
            if self.ei_flush_wait_flag then
                self:EISelectToggle()
            end
		end
	end)
end

function RoleBagView:CleanEIJumpWaitCDTime()
    if self.ei_jump_wait_quest ~= nil then
        GlobalTimerQuest:CancelQuest(self.ei_jump_wait_quest)
        self.ei_jump_wait_quest = nil
    end
end

-- 标签选择
function RoleBagView:EISelectToggle(force_big_type, force_suit_type, force_slot_index, stop_cur)
    -- print_error("---标签选择---", force_big_type, force_suit_type, force_slot_index)
    -- 等待加载完 执行列表跳转
    -- if not self.load_ei_cell_complete then
    --     if self.ei_jump_wait_quest == nil then
    --         self.ei_jump_wait_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.EISelectToggle, self), 0.1)
    --     end
    --     return
    -- else
    --     self:CleanEIJumpWaitCDTime()
    -- end

    if force_big_type then
        self.ei_force_big_type = force_big_type
    end

    if force_suit_type then
        self.ei_force_suit_type = force_suit_type
    end

    if force_slot_index then
        self.ei_force_slot_index = force_slot_index
    end

    if not self.load_ei_cell_complete then
        return
    else
        self.ei_flush_wait_flag = nil
    end

    local jump_index
    -- 如果没红点，停留当前
    if stop_cur then
        self.ei_stop_cur = true
        jump_index = self.ei_select_big_type
    end

    if self.ei_force_big_type then
        jump_index = self.ei_force_big_type
        self.ei_force_big_type = nil
    else
        local toggle_list = EquipTargetWGData.Instance:GetEIToggleList()

        -- 策划要求先完成当前面板
        local cur_remind_num = (toggle_list[self.ei_select_big_type] or {}).remind_num or 0

        if cur_remind_num > 0 then
            jump_index = self.ei_select_big_type
        else
            for k,v in ipairs(toggle_list) do
                if v.remind_num > 0 then                           -- 跳红点
                    jump_index = k
                    break
                elseif not jump_index and not v.is_all_act then    -- 跳未收集完
                    jump_index = k
                end
            end
        end
    end

    jump_index = jump_index or 1
    local suit_type = self.ei_select_suit_data and self.ei_select_suit_data.type or -1
    if self.ei_select_big_type == jump_index and suit_type == force_suit_type
    and self.ei_select_slot_index == force_slot_index then
        self:FlushEIView()
        return
    end

    if self.ei_select_big_type ~= jump_index then
        self.ei_big_type_toggle_list[jump_index]:SetAccordionElementState(true)
    else
        self:OnClickEIBigTypeToggle(self.ei_big_type_toggle_list[jump_index])
    end
end

-- 大标签回调
function RoleBagView:OnClickEIBigTypeToggle(cell)
    -- print_error("【----点击 大 回调-----】：", cell:GetIndex())
	if cell == nil then
		return
	end

    local index = cell:GetIndex()
    local data = cell:GetData()
    if data == nil then
        return
    end

    self.ei_select_big_type = index
    local jump_suit_index
    -- 如果没红点，停留当前
    if self.ei_stop_cur then
        local cur_suit_type = self.ei_select_suit_data and self.ei_select_suit_data.type
        for k,v in ipairs(data.child_list) do
            if v.type == cur_suit_type then
                jump_suit_index = k
                break
            end
        end

        self.ei_stop_cur = nil
    end

    if self.ei_force_suit_type then
        jump_suit_index = self.ei_force_suit_type
        self.ei_force_suit_type = nil
    else
        -- 策划要求先完成当前面板
        local cur_remind_num = (data.child_list[self.ei_select_small_type] or {}).remind_num or 0
        if cur_remind_num > 0 then
            jump_suit_index = self.ei_select_small_type
        else
            for k,v in ipairs(data.child_list) do
                if v.remind_num > 0 then                               -- 跳红点
                    jump_suit_index = k
                    break
                elseif not jump_suit_index and (not v.is_all_act or not v.is_max_level) then   -- 跳未收集完并且未满级
                    jump_suit_index = k
                end
            end
        end
    end

    jump_suit_index = jump_suit_index or 1
    local suit_cell = ((self.ei_suit_cell_group or {})[index] or {})[jump_suit_index]
	if suit_cell then
        suit_cell:OnClick()
	end

    local bundle, asset = ResPath.GetRawImagesJPG("a3_xjgy_bg" .. data.bg)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    bundle, asset = ResPath.GetRawImagesPNG("a3_xjgy_pic" .. data.bg)
	if self.node_list.ei_chahua then
		self.node_list["ei_chahua"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["ei_chahua"].raw_image:SetNativeSize()
		end)
	end
end

-- 子标签回调
function RoleBagView:OnClickEISuitToggle(cell)
    -- print_error("【----点击 子 回调-----】：", cell:GetData().type, cell:GetData().name)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    if self.ei_select_suit_data and data and self.ei_select_suit_data.name ~= data.name then
        self:StopLevelOperator()
    end

    self.ei_select_suit_data = data
    self.ei_select_small_type = cell:GetIndex()

    local type = data.type
    local list = self.ei_suit_cell_group[self.ei_select_big_type]
    if list then
        for k,v in pairs(list) do
            v:OnSelectSuitChange(type)
        end
    end

    self:FlushEIEquipList()
    local jump_slot_index
    if self.ei_force_slot_index then
        jump_slot_index = self.ei_force_slot_index
    else
        jump_slot_index = EquipTargetWGData.Instance:GetEIFirstJumpSlot(type)
    end

    if jump_slot_index and self.ei_equip_list[jump_slot_index] then
        self.ei_equip_list[jump_slot_index]:OnClickCellBtn()
    else
        -- for k,v in pairs(self.ei_equip_list) do
        --     v:FlushSelectHL(false)
        -- end

        self.ei_select_slot_index = nil
        self.ei_select_slot_data = nil
        self:FlushEIView()
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a3_xjgy_bj" .. data.ring_bg)
	if self.node_list.ring_bg then
		self.node_list["ring_bg"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["ring_bg"].raw_image:SetNativeSize()
		end)
	end

    if data.ring_effect and data.ring_effect ~= "" then
        self.node_list.effect:SetActive(true)
        local bundle_2, asset_2 = ResPath.GetUIEffect("UI_xjgy_" .. data.ring_effect)
        self.node_list.effect:ChangeAsset(bundle_2, asset_2)
    else
        self.node_list.effect:SetActive(false)
    end

end

function RoleBagView:OnSelectEISlotCallBack(cell)
    -- print_error("【----点击 槽位 回调-----】：", cell:GetIndex())
    if cell == nil then
        return
    end

    local index = cell:GetIndex()
    local data = cell:GetData()
    if index == self.ei_select_slot_index and self.ei_select_slot_data == data then
        return
    end
    
    self.ei_select_slot_index = index
    self.ei_select_slot_data = data
    -- for k,v in pairs(self.ei_equip_list) do
    --     v:FlushSelectHL(k == index)
    -- end

    self:FlushEIView()
end

-- 刷新全部 无战力
function RoleBagView:FlushEIViewAllNoCap()
    -- print_error("---刷新全部 无战力---")
    self:FlushEIToggleAllData()
    self:FlushEIEquipList()
    self:FlushEIView()
end

-- 刷新标签数据
function RoleBagView:FlushEIToggleAllData()
    -- print_error("---刷新标签数据---")
    if IsEmptyTable(self.ei_big_type_toggle_list) then
        return
    end

    if IsEmptyTable(self.ei_suit_cell_group) then
        return
    end

    local toggle_list = EquipTargetWGData.Instance:GetEIToggleList()
    for k,v in ipairs(toggle_list) do
        self.ei_big_type_toggle_list[k]:SetData(v)
        local suit_list = self.ei_suit_cell_group[k] or {}
        local suit_list_data = v.child_list
        for k1,v1 in ipairs(suit_list) do
            v1:SetData(suit_list_data[k1])
        end
    end
end

-- 刷新装备道具列表
function RoleBagView:FlushEIEquipList()
    --print_error("---刷新装备道具列表-----")
    if self.ei_select_suit_data == nil then
        return
    end

    local suit_index = self.ei_select_suit_data.type
    local slot_list = EquipTargetWGData.Instance:GetEISoltEquipList(suit_index)
    for k,v in pairs(self.ei_equip_list) do
        v:SetSelectSuitData(self.ei_select_suit_data)
        v:SetData(slot_list[k])
    end

    self:FlushEISkillPart()
end

function RoleBagView:FlushEIView()
    -- print_error("---刷新属性-----", self.ei_select_slot_data)
    if self.ei_select_suit_data == nil then
        return
    end

    local suit_index = self.ei_select_suit_data.type
    local is_all_act = self.ei_select_suit_data.is_all_act

    local show_level_panel = false
    local suit_level = EquipTargetWGData.Instance:GetSuitLevel(suit_index)
    local level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level)
    if is_all_act and level_cfg then
        show_level_panel = true
        --self:FlushEiUpLevelPanel()
    end

    local cur_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level + 1)

    local is_remind = false
    if next_level_cfg then
        local num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        is_remind = num >= cur_level_cfg.cost_item_num
    end

    self.node_list.ei_upgrade_remind:SetActive(is_remind)
    --self.node_list.ei_act_panel:SetActive(not show_level_panel)
    --self.node_list.ei_level_panel:SetActive(show_level_panel)
    self.node_list.ei_reslove_btn:SetActive(not IsEmptyTable(level_cfg))
    self.node_list.ei_upgrade_btn:SetActive(show_level_panel)
    --self.node_list.ei_level_line:SetActive(show_level_panel)
end

function RoleBagView:FlushEiUpLevelPanel()
    if self.ei_select_suit_data == nil then
        return
    end

    local suit_index = self.ei_select_suit_data.type
     -- 属性列表
    local attr_list = EquipTargetWGData.Instance:GetEISuitActAttrList(suit_index)
    local suit_level = EquipTargetWGData.Instance:GetSuitLevel(suit_index)
    local attr_level_list  = EquipTargetWGData.Instance:GetEISuitLevelAttrList(attr_list, suit_index, suit_level)
    for k,v in pairs(self.ei_level_attr_list) do
        v:SetData(attr_level_list[k])
    end

    local cur_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level + 1)
    self.node_list.ei_level_max:SetActive(not next_level_cfg)
    local is_remind = false
    if next_level_cfg then
        local item_data = {item_id = cur_level_cfg.cost_item_id}
        self.ei_suit_level_stuff_item:SetData(item_data)
        local num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        is_remind = num >= cur_level_cfg.cost_item_num
        local str_color = is_remind and COLOR3B.DEFAULT_NUM or COLOR3B.RED
        self.node_list["ei_level_need_num"].text.text = string.format("%s/%s", ToColorStr(num, str_color), cur_level_cfg.cost_item_num)
    else
        self.ei_suit_level_stuff_item:ClearData()
		self.ei_suit_level_stuff_item:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
        self.ei_suit_level_stuff_item:SetItemIcon(ResPath.GetCommonImages("a2_ty_suo"))
    end

    self.node_list.ei_level_text.text.text = string.format(Language.EquipTarget.LevelStr, suit_level)
    self.node_list.ei_level_need_num:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.ei_auto_level_btn:SetActive(not IsEmptyTable(next_level_cfg))
    self.node_list.ei_auto_level_remind:SetActive(is_remind)
end

function RoleBagView:FlushEICapability()
    -- print_error("----刷新战力----")
    self.node_list["ei_cap_value"].text.text = EquipTargetWGData.Instance:GetEIActCapability()
end

function RoleBagView:OnClickAttrShowBtn()
    local suit_index = self.ei_select_suit_data.type
    local attr_list = EquipTargetWGData.Instance:GetEISuitActAttrList(suit_index)
    local suit_level = EquipTargetWGData.Instance:GetSuitLevel(suit_index)
    local attr_level_list  = EquipTargetWGData.Instance:GetEISuitLevelAttrList(attr_list, suit_index, suit_level)

    -- if self.ei_select_slot_index then
    --     attr_list = EquipTargetWGData.Instance:GetEIAttrListAddData(attr_list, suit_index, self.ei_select_slot_index)
    -- end

    local tips_data = {
        title_text = string.format(Language.EquipTarget.TitleAttrName, self.ei_select_suit_data.name),
        attr_data = attr_level_list,   --  { attr_str, attr_value }
    }

    TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

function RoleBagView:OnClickEIUpgradeBtn()
    EquipTargetWGCtrl.Instance:OpenEIUpgradeView(self.ei_select_suit_data)
end

-- 立即激活
function RoleBagView:OnClickEIAct()
    if self.ei_select_suit_data == nil or self.ei_select_slot_index == nil then
        return
    end

    -- local level = RoleWGData.Instance:GetAttr("level")
    -- if level < self.ei_select_suit_data.open_level then
    if not self.ei_select_suit_data.is_open then
        local str = string.format(Language.FightSoul.WearLimit, RoleWGData.GetLevelString2(self.ei_select_suit_data.open_level))
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    local suit_index = self.ei_select_suit_data.type
    local slot_index = self.ei_select_slot_index
    local need_data = EquipTargetWGData.Instance:GetEISoltEquipId(suit_index, slot_index)
    if not need_data then
        return
    end

    local bag_index
    local need_item_id = need_data.item_id
    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(need_item_id)
    if item_cfg then
        if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
            local item_list = ItemWGData.Instance:GetComposeEquipListByData(item_cfg.order, item_cfg.color, need_data.star) or {}
            for k,v in ipairs(item_list) do
                if v.item_id == need_item_id then
                    bag_index = v.index
                    break
                end
            end
        else
            bag_index = ItemWGData.Instance:GetItemIndex(need_item_id)
        end
    end

    if bag_index == nil or bag_index == -1 then
        TipWGCtrl.Instance:OpenItem({item_id = self.ei_select_slot_data.item_id, param = {star_level = self.ei_select_slot_data.star}})
        return
    end

    if nil == self.ei_active_alert then
		self.ei_active_alert = Alert.New(nil, nil, nil, nil, true)
		self.ei_active_alert:SetShowCheckBox(true, "ei_active_alert")
		self.ei_active_alert:SetCheckBoxDefaultSelect(false)
	end

    local item_name = item_cfg and ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]) or ""
	self.ei_active_alert:SetLableString(string.format(Language.EquipTarget.ActiveStr, item_name))
	self.ei_active_alert:SetOkFunc(function()
        EquipTargetWGCtrl.Instance:SendEIOperateReq(EQUIP_COLLECT_OPERATE_TYPE.ACTIVE_HOLE, suit_index, slot_index, bag_index)
	end)

	self.ei_active_alert:Open()
end

function RoleBagView:OnBtnUpLevel(one_key, is_auto)
    if self.ei_select_suit_data == nil then
        return
    end
    
    local suit_index = self.ei_select_suit_data.type
    local suit_level = EquipTargetWGData.Instance:GetSuitLevel(suit_index)
    local cur_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level)
    local next_level_cfg = EquipTargetWGData.Instance:GetEISuitLevelCfg(suit_index, suit_level + 1)
	if next_level_cfg and cur_level_cfg then
		local cost_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		if cost_num >= cur_level_cfg.cost_item_num then
			EquipTargetWGCtrl.Instance:SendEIOperateReq(EQUIP_COLLECT_OPERATE_TYPE.SUIT_UPLEVEL, suit_index)
			if is_auto then
				self:SetLevelButtonEnabled(true)
			end
		else
			self:StopLevelOperator()
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_level_cfg.cost_item_id})
		end
	else
		self:StopLevelOperator()
	end
end

function RoleBagView:AutoUpLevel()
	if self:IsAutoUpLevel() then --正在自动升级则取消强化
		self:StopLevelOperator()
		return
	end

	self:OnBtnUpLevel(1, true)
end

--取消自动升级倒计时
function RoleBagView:CancelAutoLevelTimer( )
	if nil ~= self.auto_level_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_level_timer_quest)
		self.auto_level_timer_quest = nil
	end
end

-- 自动升级操作
function RoleBagView:AutoUpLevelUpOnce()
	self:CancelAutoLevelTimer()

    if self.ei_select_suit_data == nil then
        return
    end

	if self:IsAutoUpLevel() then
		self.auto_level_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnBtnUpLevel, self, 1, true), LEVEL_DELT_TIME)
	end
end

function RoleBagView:IsAutoUpLevel()
	return self.is_auto_level
end

function RoleBagView:StopLevelOperator()
	self:SetLevelButtonEnabled(false)
end

function RoleBagView:FlushEISkillPart()
    if self.ei_select_suit_data == nil then
        return
    end

    local suit_index = self.ei_select_suit_data.type
    local is_all_act = self.ei_select_suit_data.is_all_act
    local act_cfg = EquipTargetWGData.Instance:GetEISuitActCfg(suit_index)
    if act_cfg == nil then
        return
    end

    XUI.SetSkillIcon(self.node_list["ei_skill_bg"], self.node_list["ei_skill_icon"], act_cfg.icon)
    self.node_list["skill_active_desc"].text.text = not is_all_act and Language.EquipTarget.SkillLockDesc or Language.EquipTarget.SkillActiveDesc
    --self.node_list["ei_skill_name"].text.text = act_cfg.name
    --self.node_list["ei_skill_is_act"]:SetActive(is_all_act)
    --self.node_list["ei_skill_desc"].text.text = EquipTargetWGData.Instance:GetEISuitActAttrDesc(suit_index)
end

-- 展示技能
function RoleBagView:OnClicEISkillBtn()
    if self.ei_select_suit_data == nil then
        return
    end

    local suit_index = self.ei_select_suit_data.type
    local is_all_act = self.ei_select_suit_data.is_all_act
    local act_cfg = EquipTargetWGData.Instance:GetEISuitActCfg(suit_index)
    if act_cfg == nil then
        return
    end

    local body_text = EquipTargetWGData.Instance:GetEISuitActAttrDesc(suit_index)

    local cap = EquipTargetWGData.Instance:GetEISuitActAttrCap(suit_index)

    local limit_text = ""
    if not is_all_act then
        limit_text = Language.EquipTarget.LimitDesc1
    end

	local show_data = {
		icon = act_cfg.icon,
        top_text = act_cfg.name,
        hide_level = true,
        capability = cap,
        body_text = body_text,
		limit_text = limit_text,
		x = 0,
		y = 0,
		set_pos2 = true,
        hide_next = true,
	}

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

-- 设置强化按钮是否可用
function RoleBagView:SetLevelButtonEnabled(enabled)
	self.is_auto_level = enabled
	self:SetAutoLevelBtnNameStr(not enabled and Language.EquipTarget.AutoUpLevel or Language.EquipTarget.UpLevelBtnStop)
	if false == enabled then
		self:CancelAutoLevelTimer()
	end
end

function RoleBagView:SetAutoLevelBtnNameStr(strength_btn_str)
	if self.node_list["ei_auto_level_text"] then
		self.node_list["ei_auto_level_text"].text.text = strength_btn_str
	end
end

-- 使用特效
function RoleBagView:PlayEiUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["ei_effect_pos"]})
end

function RoleBagView:OnClickGoReslove()
    ViewManager.Instance:Open(GuideModuleName.EquipmentBagResloveView)
end


--======================================================================
-- 大类型toggle
--======================================================================
EIBigTypeToggleRender = EIBigTypeToggleRender or BaseClass(BaseRender)
function EIBigTypeToggleRender:__init()
    self.view.accordion_element:AddClickListener(BindTool.Bind(self.OnClickAccordion, self))
end

function EIBigTypeToggleRender:__delete()
end

function EIBigTypeToggleRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function EIBigTypeToggleRender:OnClickAccordion(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function EIBigTypeToggleRender:SetAccordionElementState(is_on)
    self.view.accordion_element.isOn = is_on
end

function EIBigTypeToggleRender:OnFlush()
	if self.data == nil or not self.data.is_show then
        self.view:SetActive(false)
		return
	end

    self.node_list.normal_text.text.text = self.data.name
    self.node_list.select_text.text.text = self.data.name
    local remind = EquipTargetWGData.Instance:GetBigTypeRemind(self.data.type)
    self.node_list["remind"]:SetActive(self.data.remind_num > 0)
    self.node_list.all_act:SetActive(self.data.is_all_act) -- 全收集后，主标签也显示全收集
    self.view:SetActive(true)

    local bundle, asset = ResPath.GetCommonButton("a3_ss_yq" .. self.data.quality)
	if self.node_list.bg then
		self.node_list["bg"].image:LoadSprite(bundle, asset, function()
			self.node_list["bg"].image:SetNativeSize()
		end)
	end
end


--======================================================================
-- 小类型toggle
--======================================================================
EISuitToggleRender = EISuitToggleRender or BaseClass(BaseRender)
function EISuitToggleRender:__init()
end

function EISuitToggleRender:__delete()
end

function EISuitToggleRender:OnFlush()
	if self.data == nil or not self.data.is_show then
        self.view:SetActive(false)
		return
	end

    local color = ITEM_COLOR[self.data.quality]
    self.node_list["normal_text"].text.text = ToColorStr(self.data.name, color)
    self.node_list["select_text"].text.text = ToColorStr(self.data.name, color)
    self.node_list["lock"]:SetActive(not self.data.is_open)
    self.node_list["gather_flag"]:SetActive(self.data.is_all_act)
    local remind = EquipTargetWGData.Instance:GetSmallTypeRemind(self.data.type)
    self.node_list["remind"]:SetActive(self.data.remind_num > 0)
    self.view:SetActive(true)

    local bundle, asset = ResPath.GetCommonButton("a3_ss_yqs" .. self.data.quality)
	if self.node_list.bg then
		self.node_list["bg"].image:LoadSprite(bundle, asset, function()
			self.node_list["bg"].image:SetNativeSize()
		end)
	end

    bundle, asset = ResPath.GetRoleBagImg("a3_xjgy_tx" .. self.data.bg)
	if self.node_list.tx_icon then
		self.node_list["tx_icon"].image:LoadSprite(bundle, asset, function()
			self.node_list["tx_icon"].image:SetNativeSize()
		end)
	end
end

function EISuitToggleRender:OnSelectSuitChange(suit_type)
    if self.data == nil then
        return
    end

    local is_select = suit_type == self.data.type
    self.node_list["normal_bg"]:SetActive(not is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end


--======================================================================
-- 装备格子
--======================================================================
EIEquipRender = EIEquipRender or BaseClass(BaseRender)
function EIEquipRender:__init()
    self.equip_cell = ItemCell.New(self.node_list["cell_pos"])
    self.equip_cell:SetIsUseRoundQualityBg(true)
    self.equip_cell:SetCellBgEnabled(false)
	XUI.AddClickEventListener(self.node_list["btn_cell_click"], BindTool.Bind(BindTool.Bind(self.OnClickEIAct, self)))
    XUI.AddClickEventListener(self.node_list["get_way_str"], BindTool.Bind(BindTool.Bind(self.OnClickGetWay, self)))

    self.ei_select_suit_data = nil
end

function EIEquipRender:__delete()
    if self.equip_cell then
        self.equip_cell:DeleteMe()
        self.equip_cell = nil
    end

    if self.ei_active_alert then
		self.ei_active_alert:DeleteMe()
		self.ei_active_alert = nil
	end

    self.item_click_callback = nil
end

function EIEquipRender:SetCellClickCallBack(call_back)
    self.item_click_callback = call_back
end

function EIEquipRender:SetSelectSuitData(data)
    self.ei_select_suit_data = data
end

function EIEquipRender:OnClickCellBtn()
    if self.item_click_callback then
        self.item_click_callback(self)
    end
end

function EIEquipRender:OnClickGetWay()
    if self.data == nil then
        return
    end

    FunOpen.Instance:OpenViewNameByCfg(self.data.cfg.get_way_open_view)
end

function EIEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    local cfg = self.data.cfg
    --local item_data = {item_id = self.data.item_id, param = {star_level = self.data.star}}
    local item_data = {item_id = self.data.item_id}
    self.equip_cell:SetData(item_data)
    self.equip_cell:Nodes("item_icon").transform.localScale = Vector3(0.7, 0.7, 0.7)
    self.equip_cell:SetEffectRootEnable(false)

    local is_act =  EquipTargetWGData.Instance:GetSlotIsAct(cfg.suit_index, cfg.slot_index)
    --self.node_list["is_has"]:SetActive(is_act)
    if not is_act then
        self.equip_cell:SetGraphicGreyCualityBg(true)
        self.equip_cell:SetDefaultEff(false)
    else
        self.equip_cell:SetGraphicGreyCualityBg(false)
    end

    self.node_list["can_active_flag"]:SetActive(EquipTargetWGData.Instance:GetSlotRemind(cfg.suit_index, cfg.slot_index))
    self.node_list["btn_cell_click"]:SetActive(EquipTargetWGData.Instance:GetSlotRemind(cfg.suit_index, cfg.slot_index))
    self.node_list["get_way_str"].text.text = string.format("%s\n————", cfg.get_way_name)

    self.node_list["remind"]:SetActive(EquipTargetWGData.Instance:GetSlotRemind(cfg.suit_index, cfg.slot_index))
end

-- function EIEquipRender:FlushSelectHL(is_select)
--     self.node_list["select_hl"]:SetActive(is_select)
-- end

function EIEquipRender:OnClickEIAct()
    if not self.data then
        return
    end

    -- local level = RoleWGData.Instance:GetAttr("level")
    -- if level < self.data.open_level then
    if not self.ei_select_suit_data.is_open then
        local str = string.format(Language.FightSoul.WearLimit, RoleWGData.GetLevelString2(self.ei_select_suit_data.open_level))
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

    local suit_index = self.ei_select_suit_data.type
    local slot_index = self.index
    local need_data = EquipTargetWGData.Instance:GetEISoltEquipId(suit_index, slot_index)
    if not need_data then
        return
    end

    local bag_index
    local need_item_id = need_data.item_id
    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(need_item_id)
    if item_cfg then
        if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
            local item_list = ItemWGData.Instance:GetComposeEquipListByData(item_cfg.order, item_cfg.color, need_data.star) or {}
            for k,v in ipairs(item_list) do
                if v.item_id == need_item_id then
                    bag_index = v.index
                    break
                end
            end
        else
            bag_index = ItemWGData.Instance:GetItemIndex(need_item_id)
        end
    end

    if bag_index == nil or bag_index == -1 then
        TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id, param = {star_level = self.data.star}})
        return
    end

    if nil == self.ei_active_alert then
		self.ei_active_alert = Alert.New(nil, nil, nil, nil, true)
		self.ei_active_alert:SetShowCheckBox(true, "ei_active_alert")
		self.ei_active_alert:SetCheckBoxDefaultSelect(false)
	end

    local item_name = item_cfg and ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]) or ""
	self.ei_active_alert:SetLableString(string.format(Language.EquipTarget.ActiveStr, item_name))
	self.ei_active_alert:SetOkFunc(function()
        EquipTargetWGCtrl.Instance:SendEIOperateReq(EQUIP_COLLECT_OPERATE_TYPE.ACTIVE_HOLE, suit_index, slot_index, bag_index)
	end)

	self.ei_active_alert:Open()
end