--[[
	/jy_gm equipbodygmoperate:101 0 1 0      -- EQUIP_BODY_GM_TYPE_SET_ACTIVE_FLAG,			// 设置激活标记 param1:seq	param2:is_active
]]--

EquipBodyWGData = EquipBodyWGData or BaseClass()

function EquipBodyWGData:__init()
    if EquipBodyWGData.Instance then
		print_error("[EquipBodyWGData] Attemp to create a singleton twice !")
	end

	EquipBodyWGData.Instance = self

	-- 装备肉身
	self.equip_body_cfg = ConfigManager.Instance:GetAutoConfig("equip_body_auto")
	self.equip_body_list_cfg = self.equip_body_cfg.body
	self.equip_tips_cfg = ListToMap(self.equip_body_cfg.equip_tips, "equip_order", "type")
	self.equip_body_type_cfg = self.equip_body_cfg.equip_body_type
	self.equip_body_active_flag = {}

	-- 装备阶级 可镶嵌的肉身seq
	self.equip_order_to_equip_seq_cache = {}
	self.equip_type_data_list_cache = {}
	self.transfer_unlock_equip_order = {}
	for k, v in pairs(self.equip_body_list_cfg) do
		self.equip_order_to_equip_seq_cache[v.equip_order] = v.seq

		self.equip_type_data_list_cache[v.equip_body_type] = self.equip_type_data_list_cache[v.equip_body_type] or {}
		table.insert(self.equip_type_data_list_cache[v.equip_body_type], v)

		if v.need_zhuanzhi ~= 0 then
			self.transfer_unlock_equip_order[v.need_zhuanzhi] = v.equip_order
		end
	end

	RemindManager.Instance:Register(RemindName.EquipBodyTuPo, BindTool.Bind(self.GetEquipBodyTuPoRemind, self))				-- 背包标签红点
end

function EquipBodyWGData:__delete()
    EquipBodyWGData.Instance = nil
end

function EquipBodyWGData:GetEquipBodyTuPoRemind()
	for k, v in pairs(self:GetTotalEquipBodyDataList()) do
		if self:IsCanEquipBodyUnLock(v.seq) then
			return 1
		end
	end

	return 0
end

-----------------------------------protocol_start--------------------------------------
function EquipBodyWGData:SetEquipBodyBaseInfo(protocol)
	local has_active_equip_body = false
	local old_equip_body_data = self.equip_body_active_flag

	self.equip_body_active_flag = bit:d2b_l2h(protocol.active_flag, nil, true)

	for k, v in pairs(self:GetTotalEquipBodyDataList()) do
		if old_equip_body_data[v.seq] == 0 and self.equip_body_active_flag[v.seq] == 1 then
			has_active_equip_body = true
		end
	end

	return has_active_equip_body
end

function EquipBodyWGData:GetEquipBodyBaseInfo()
	return self.equip_body_active_flag
end
-------------------------------------protocol_end-------------------------------------

---------------------------------------cfg_start--------------------------------------
function EquipBodyWGData:GetEquipBodyDataList(type)
    local target_data_list = {}

    if type == EQUIP_BODY_TYPE.NORMAL then
        for k, v in pairs(self.equip_body_list_cfg) do
            if self:IsCanEquipBodyShow(v.seq) and self:GetEquipBodyType(v.seq) == EQUIP_BODY_TYPE.NORMAL then
                table.insert(target_data_list, v)
            end
        end

		-- table.sort(target_data_list, SortTools.KeyLowerSorter("seq")) 
        -- return target_data_list
    elseif type == EQUIP_BODY_TYPE.SPECIAL then
        for k, v in pairs(self.equip_body_list_cfg) do
            if self:IsCanEquipBodyShow(v.seq) and self:GetEquipBodyType(v.seq) == EQUIP_BODY_TYPE.SPECIAL then
                table.insert(target_data_list, v)
            end
        end

		-- table.sort(target_data_list, SortTools.KeyLowerSorter("seq")) 
        -- return target_data_list
    end

	table.sort(target_data_list, self:RoleBagSortEquipBody())
	return target_data_list
end

function EquipBodyWGData:GetEquipBodyDataListByType(equip_body_type)
	local target_data_list = {}

	for k, v in pairs(self.equip_body_list_cfg) do
		if v.equip_body_type == equip_body_type and self:IsCanEquipBodyShow(v.seq) then
			table.insert(target_data_list, v)
		end
	end

	if not IsEmptyTable(target_data_list) then
		table.sort(target_data_list, self:RoleBagSortEquipBody())
	end

	return target_data_list
end

function EquipBodyWGData:RoleBagSortEquipBody()
	return function (a, b)
		local order_a = 0
		local order_b = 0
	
		if self:IsEquipBodyUnLock(a.seq) then
			order_a = order_a + 1000
		end
	
		if self:IsEquipBodyUnLock(b.seq) then
			order_b = order_b + 1000
		end

		if order_a == order_b then
			return a.seq < b.seq
		else
			return order_a > order_b
		end
	end
end

function EquipBodyWGData:GetAllEquipBodyDataList()
	return self.equip_body_list_cfg
end

function EquipBodyWGData:GetTotalEquipBodyDataList()
    local target_data_list = {}

    for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsCanEquipBodyShow(v.seq) then
            table.insert(target_data_list, v)
        end
    end

	table.sort(target_data_list, SortTools.KeyLowerSorter("seq")) 
	return target_data_list
end

function EquipBodyWGData:GetTotalUnlockEquipBodyDataList()
    local target_data_list = {}

    for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsCanEquipBodyShow(v.seq) and self:IsEquipBodyUnLock(v.seq) then
            table.insert(target_data_list, v)
        end
    end

	table.sort(target_data_list, SortTools.KeyLowerSorter("seq")) 
	return target_data_list
end

-- 锻造显示的肉身   
function EquipBodyWGData:GetDZEquipBodyDataList()
    local target_data_list = {}

    for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsCanEquipBodyShow(v.seq) then-- and self:IsEquipBodyUnLock(v.seq) and self:IsEquipBodyWearEquip(v.seq) then
            table.insert(target_data_list, v)
        end
    end

	-- table.sort(target_data_list, self:SortEquipBody())
	table.sort(target_data_list, SortTools.KeyLowerSorter("seq"))

	return target_data_list
end

-- 玉魄精炼显示肉身
function EquipBodyWGData:GetDZYPJLEquipBodyDataList()
	local target_data_list = {}

    for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsEquipBodyCanYPJL(v.seq) and self:IsCanEquipBodyShow(v.seq) then
            table.insert(target_data_list, v)
        end
    end

	-- table.sort(target_data_list, self:SortEquipBody())
	table.sort(target_data_list, SortTools.KeyLowerSorter("seq"))

	return target_data_list
end

-- 玄晶镶嵌显示肉身
function EquipBodyWGData:GetDZXJXQEquipBodyDataList()
	local target_data_list = {}

    for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsEquipBodyCanXJXQ(v.seq) and self:IsCanEquipBodyShow(v.seq) then
            table.insert(target_data_list, v)
        end
    end

	-- table.sort(target_data_list, self:SortEquipBody())
	table.sort(target_data_list, SortTools.KeyLowerSorter("seq"))

	return target_data_list
end

function EquipBodyWGData:SortEquipBody()
	return function (a, b)
		local order_a = 0
		local order_b = 0
	
		if self:IsEquipBodyUnLock(a.seq) then
			order_a = order_a + 100
		end
	
		if self:IsEquipBodyWearEquip(a.seq) then
			order_a = order_a + 10000
		end
	
		if self:IsEquipBodyUnLock(b.seq) then
			order_b = order_b + 100
		end
	
		if self:IsEquipBodyWearEquip(b.seq) then
			order_b = order_b + 10000
		end

		if order_a == order_b then
			return a.seq < b.seq
		else
			return order_a > order_b
		end
	end
end

-- 肉身是否穿戴装备
function EquipBodyWGData:IsEquipBodyWearEquip(seq)
	local data_list = EquipWGData.Instance:GetDataList()

	local index_offset = seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
	for index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local data = data_list[index + index_offset]
		
		if not IsEmptyTable(data) and data.item_id > 0 then
			return true
		end
	end

	return false
end

function EquipBodyWGData:GetEquipBodyCfgBySeq(seq)
	return self.equip_body_list_cfg[seq] or {}
end

function EquipBodyWGData:GetForgeLevelLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).forge_level_limit or 99999
end

function EquipBodyWGData:GetRefineLevelLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).refine_level_limit or 99999
end

function EquipBodyWGData:GetForgeBonusLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).forge_bonus_limit or 99999
end

function EquipBodyWGData:GetXianQiForgeBonusLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).xianqi_forge_bonus_limit or 99999
end

function EquipBodyWGData:GetBaptizeLevelLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).baptize_level_limit or 99999
end

function EquipBodyWGData:GetLingYuLevelLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).lingyu_level_limit or 99999
end

function EquipBodyWGData:GetEquipOrderToEquipBodySeq(equip_order)
	return self.equip_order_to_equip_seq_cache[equip_order]
end

function EquipBodyWGData:GetEquipStoneActiveLimitLevel(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).jade_bonus_limit or 99999     --玉魄共鸣上线需要处理
end

-- 每个阶级镶嵌宝石得最大等级限制
function EquipBodyWGData:GetEquipStoneJadeLevelLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).jade_level_limit or 99999
end

function EquipBodyWGData:GetEquipBaptizeLevelLimit(equip_body_seq)
	return (self.equip_body_list_cfg[equip_body_seq] or {}).equip_baptize_level_limit or 0
end

function EquipBodyWGData:GetEquipBodyTypeDataByType(equip_body_type)
	return self.equip_body_type_cfg[equip_body_type]
end

function EquipBodyWGData:GetEquipBodyTypeDataList()
	return self.equip_body_type_cfg
end

function EquipBodyWGData:GetEquipBodyTypeDataListCacheBuType(equip_body_type)
	return self.equip_type_data_list_cache[equip_body_type]
end
----------------------------------------cfg_end---------------------------------------

---------------------------------------cal_start--------------------------------------
function EquipBodyWGData:IsEquipBodyCanYPJL(equip_body_seq)
	return self:GetEquipBodyCfgBySeq(equip_body_seq).refine_body_show == 1
end

function EquipBodyWGData:IsEquipBodyCanXJXQ(equip_body_seq)
	return self:GetEquipBodyCfgBySeq(equip_body_seq).set_body_show == 1
end

function EquipBodyWGData:IsEquipBodyCanDuanZao(equip_body_seq)
	local is_unlocak = self:IsEquipBodyUnLock(equip_body_seq)
	local is_wear_equip = self:IsEquipBodyWearEquip(equip_body_seq)

	return is_unlocak and is_wear_equip, is_unlocak, is_wear_equip
end

function EquipBodyWGData:IsCanEquipBodyUnLock(seq)
	local is_unlock = self:IsEquipBodyUnLock(seq)

	if not is_unlock then
		local target_cfg = self:GetEquipBodyCfgBySeq(seq)

		if not IsEmptyTable(target_cfg) then
			local pre_unlock = true

			if target_cfg.need_active_seq >= 0 then
				pre_unlock = self:IsEquipBodyUnLock(target_cfg.need_active_seq)
			end

			if pre_unlock then
				local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
				local role_level = RoleWGData.Instance:GetAttr('level')
	
				if role_zhuan >= target_cfg.need_zhuanzhi and role_level >= target_cfg.need_level then
					return true
				end
			end
		end
	end

	return false
end

function EquipBodyWGData:IsEquipBodyUnLock(seq)
	local target_flag = (self.equip_body_active_flag or {})[seq] or 0
	return target_flag == 1
end

function EquipBodyWGData:IsCanEquipBodyShow(seq)
    local equip_body_cfg = self:GetEquipBodyCfgBySeq(seq)

    if not IsEmptyTable(equip_body_cfg) then
        local role_level = RoleWGData.Instance:GetRoleLevel()

        if role_level >= equip_body_cfg.need_level_show then
            return true
        end
    end

    return false
end

function EquipBodyWGData:GetEquipBodyType(seq)
    local equip_body_cfg = self:GetEquipBodyCfgBySeq(seq)
    return (equip_body_cfg.type == EQUIP_BODY_TYPE.NORMAL) and EQUIP_BODY_TYPE.NORMAL or EQUIP_BODY_TYPE.SPECIAL
end

function EquipBodyWGData:CalRoleEquipWearIndex(type, order)
    local equip_index = -1
    local equip_seq = self:GetEquipOrderToEquipBodySeq(order)

    if equip_seq then
        local equip_id_offsset = (equip_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM)

        if type == GameEnum.EQUIP_TYPE_TOUKUI then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_TOUKUI
		elseif type == GameEnum.EQUIP_TYPE_YIFU then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_YIFU
		elseif type == GameEnum.EQUIP_TYPE_KUZI then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_KUZI
		elseif type == GameEnum.EQUIP_TYPE_XIANLIAN then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_XIANLIAN
		elseif type == GameEnum.EQUIP_TYPE_XIEZI then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_XIEZI
		elseif type == GameEnum.EQUIP_TYPE_WUQI then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_WUQI
		elseif type == GameEnum.EQUIP_TYPE_XIANZHUI then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_XIANZHUI
		elseif type == GameEnum.EQUIP_TYPE_XIANFU then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_XIANFU
		elseif type == GameEnum.EQUIP_TYPE_XIANJIE then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_XIANJIE
		elseif type == GameEnum.EQUIP_TYPE_XIANZHUO then
			equip_index = equip_id_offsset + GameEnum.EQUIP_INDEX_XIANZHUO
        end
    end

    return equip_index
end

function EquipBodyWGData:IsHasCanYPJLEquipBody()
	for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsCanEquipBodyShow(v.seq) and self:IsEquipBodyUnLock(v.seq) and self:IsEquipBodyWearEquip(v.seq) and self:IsEquipBodyCanYPJL(v.seq) then
            return true
        end
    end

	return false
end

function EquipBodyWGData:IsHasCanXJXQEquipBody()
	for k, v in pairs(self.equip_body_list_cfg) do
        if self:IsCanEquipBodyShow(v.seq) and self:IsEquipBodyUnLock(v.seq) and self:IsEquipBodyWearEquip(v.seq) and self:IsEquipBodyCanXJXQ(v.seq) then
			return true
        end
    end

	return false
end

function EquipBodyWGData:GetCurEquipShowTip(equip_body_seq, equip_type)
	local target_item_id = 0
	local equip_tip_cfg = (self.equip_tips_cfg[equip_body_seq] or {})[equip_type] or {}

	if not IsEmptyTable(equip_tip_cfg) then
	    local prof = RoleWGData.Instance:GetRoleProf()
		local sex = RoleWGData.Instance:GetRoleSex()

		target_item_id = equip_tip_cfg["prof_".. sex .."_".. prof .."_id"] or 0
	end

	return target_item_id
end

function EquipBodyWGData:GetEquipSpecailOrderSignStr(equip_order)
	local seq = self:GetEquipOrderToEquipBodySeq(equip_order)

	if seq then
		local equip_body_cfg = self:GetEquipBodyCfgBySeq(seq)
		return equip_body_cfg.subscript
	end
end

function EquipBodyWGData:GetEquipIsSpecailOrder(equip_order)
	local seq = self:GetEquipOrderToEquipBodySeq(equip_order)

	if seq then
		local equip_body_cfg = self:GetEquipBodyCfgBySeq(seq)
		return equip_body_cfg.type == 1
	end

	return false
end

function EquipBodyWGData:GetEquipOrderByTransferLevel(transfer_level)
	return self.transfer_unlock_equip_order[transfer_level] or 0
end
----------------------------------------cal_end---------------------------------------