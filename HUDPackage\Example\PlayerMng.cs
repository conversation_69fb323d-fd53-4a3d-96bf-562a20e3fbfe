﻿using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using HUDProgramme;

public class PlayerMng : MonoBehaviour
{

    private static PlayerMng instance;
    public static PlayerMng Instance
    {
        get { return instance; }
    }

    private void Awake()
    {
        instance = this;
    }

    public Camera SceneCamera;

    Dictionary<int, Player> m_Players = new Dictionary<int, Player>();
    int m_nID = 0;
    int m_nMainID = 0;
    // Use this for initialization
    bool m_bShowHurt = false;
    bool m_bHideMesh = false;
    float m_fLastShowTime = 0.0f;
    void Start ()
    {
        HUDMesh.OnEnterGame();  // 启动游戏场景后调用
        Camera caMain = Camera.main;
        HUDMesh.SetHUDMainCamera(caMain);
        HUDMesh.SetHUDSceneCamera(SceneCamera);
    }
    void OnDestory()
    {
        HUDMesh.OnLeaveGame();
    }

    // 添加一个测试玩家
    public Player AddPlayer()
    {
        ++m_nID;
        GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
        obj.name = "Player" + m_nID.ToString();
        obj.transform.position = new Vector3(420.5f, 103.5931f, 411.5f);
        obj.transform.eulerAngles = new Vector3(0, 180, 0);
        obj.transform.localScale = new Vector3(1, 1, 1);
        Player p = obj.AddComponent<Player>();
        p.m_bMain = m_Players.Count == 0;
        p.m_nID = m_nID;
        m_Players[m_nID] = p;
        if (p.m_bMain)
            m_nMainID = m_nID;
        MeshRenderer mr = obj.GetComponent<MeshRenderer>();
        if (mr != null)
        {
            mr.enabled = !m_bHideMesh;
        }

        return p;
    }
    void DelPlayer()
    {
        foreach(var p in m_Players)
        {
            GameObject.Destroy(p.Value.gameObject);
        }
        m_Players.Clear();
    }
    void  OnChangeMeshRender()
    {
        foreach (var p in m_Players)
        {
            GameObject obj = p.Value.gameObject;
            MeshRenderer  mr = obj.GetComponent<MeshRenderer>();
            if(mr != null)
            {
                mr.enabled = !m_bHideMesh;
            }
        }
    }

    Vector3 RandomPos()
    {
        Vector3 vPos = Vector3.zero;

        vPos.x = Random.Range(-5, 5);
        vPos.z = Random.Range(-5, 5);

        return vPos;
    }

    void ShowHurt()
    {
        // 先显示主角的伤害
        foreach (var p in m_Players)
        {
            if (p.Value.m_bMain)
            {
                //for(int i = 0; i<5;++i)
                {
                    int nHurtHP = Random.Range(100, 50000);
                    int nExp = Random.Range(1000, 5000);
                    p.Value.ShowHurt(nHurtHP);
                    p.Value.ShowExp(nExp);
                }
            }
            else
            {
                //for (int i = 0; i < 5; ++i)
                {
                    int nHurtHP = Random.Range(100, 50000);
                    p.Value.ShowHurt(nHurtHP);
                }
            }
        }
    }
    void ShowExp()
    {

    }
	
	// Update is called once per frame
	//void Update ()
 //   {
 //       if(m_bShowHurt)
 //       {
 //           float fNow = Time.time;
 //           if(m_fLastShowTime + 0.3f < fNow)
 //           {
 //               m_fLastShowTime = fNow;
 //               ShowHurt();
 //               ShowExp();
 //           }
 //       }
	//}

    //void OnGUI()
    //{
    //    float fLeft = 10.0f;
    //    float fTop = 10.0f;

    //    if (GUI.Button(new Rect(fLeft, fTop, 100.0f, 20.0f), "添加角色"))
    //    {
    //        AddPlayer();
    //    }
    //    fLeft += 110;
    //    if (GUI.Button(new Rect(fLeft, fTop, 100.0f, 20.0f), "删除角色"))
    //    {
    //        DelPlayer();
    //    }
    //    fLeft += 110;
    //    if (GUI.Button(new Rect(fLeft, fTop, 100.0f, 20.0f), m_bShowHurt ? "停止显示" : "显示伤害"))
    //    {
    //        m_bShowHurt = !m_bShowHurt;
    //    }
    //    fLeft += 110;
    //    if (GUI.Button(new Rect(fLeft, fTop, 100.0f, 20.0f), m_bHideMesh ? "显示方块" : "隐匿方块"))
    //    {
    //        m_bHideMesh = !m_bHideMesh;
    //        OnChangeMeshRender();
    //    }
    //}
}
