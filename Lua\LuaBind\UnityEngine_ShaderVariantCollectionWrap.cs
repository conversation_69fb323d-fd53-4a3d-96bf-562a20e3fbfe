﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_ShaderVariantCollectionWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.ShaderVariantCollection), typeof(UnityEngine.Object));
		<PERSON><PERSON>unction("Clear", Clear);
		<PERSON><PERSON>unction("WarmUp", WarmUp);
		<PERSON><PERSON>unction("Add", Add);
		<PERSON><PERSON>unction("Remove", Remove);
		L<PERSON>Function("Contains", Contains);
		<PERSON><PERSON>RegFunction("New", _CreateUnityEngine_ShaderVariantCollection);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("shaderCount", get_shaderCount, null);
		<PERSON><PERSON>("variantCount", get_variantCount, null);
		<PERSON><PERSON>("isWarmedUp", get_isWarmedUp, null);
		<PERSON><PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_ShaderVariantCollection(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.ShaderVariantCollection obj = new UnityEngine.ShaderVariantCollection();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.ShaderVariantCollection.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clear(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)ToLua.CheckObject(L, 1, typeof(UnityEngine.ShaderVariantCollection));
			obj.Clear();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WarmUp(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)ToLua.CheckObject(L, 1, typeof(UnityEngine.ShaderVariantCollection));
			obj.WarmUp();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Add(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)ToLua.CheckObject(L, 1, typeof(UnityEngine.ShaderVariantCollection));
			UnityEngine.ShaderVariantCollection.ShaderVariant arg0 = StackTraits<UnityEngine.ShaderVariantCollection.ShaderVariant>.Check(L, 2);
			bool o = obj.Add(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Remove(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)ToLua.CheckObject(L, 1, typeof(UnityEngine.ShaderVariantCollection));
			UnityEngine.ShaderVariantCollection.ShaderVariant arg0 = StackTraits<UnityEngine.ShaderVariantCollection.ShaderVariant>.Check(L, 2);
			bool o = obj.Remove(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Contains(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)ToLua.CheckObject(L, 1, typeof(UnityEngine.ShaderVariantCollection));
			UnityEngine.ShaderVariantCollection.ShaderVariant arg0 = StackTraits<UnityEngine.ShaderVariantCollection.ShaderVariant>.Check(L, 2);
			bool o = obj.Contains(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shaderCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)o;
			int ret = obj.shaderCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shaderCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_variantCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)o;
			int ret = obj.variantCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index variantCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isWarmedUp(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ShaderVariantCollection obj = (UnityEngine.ShaderVariantCollection)o;
			bool ret = obj.isWarmedUp;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isWarmedUp on a nil value");
		}
	}
}

