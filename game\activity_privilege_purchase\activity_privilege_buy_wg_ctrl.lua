require("game/activity_privilege_purchase/activity_privilege_buy_view")
require("game/activity_privilege_purchase/activity_privilege_buy_wg_data")

ActivityPrivilegeBuyWGCtrl = ActivityPrivilegeBuyWGCtrl or BaseClass(BaseWGCtrl)

function ActivityPrivilegeBuyWGCtrl:__init()
    if ActivityPrivilegeBuyWGCtrl.Instance then
		error("[ActivityPrivilegeBuyWGCtrl]:Attempt to create singleton twice!")
	end
    -- 单例
	ActivityPrivilegeBuyWGCtrl.Instance = self

    self.view = ActivityPrivilegeBuyView.New(GuideModuleName.PrivilegeBuyView)
    self.data = ActivityPrivilegeBuyWGData.New()
    

    --注册协议
    self:RegisterAllProtocols()

    --注册监听
    self:RegisterAllEvents()
end

function ActivityPrivilegeBuyWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    ActivityPrivilegeBuyWGCtrl.Instance = nil
end

function ActivityPrivilegeBuyWGCtrl:RegisterAllProtocols()
    --接受协议
    self:RegisterProtocol(SCTequanShopInfo, "OnSCTequanShopInfo")
end

function ActivityPrivilegeBuyWGCtrl:RegisterAllEvents()
    -- 天数改变
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function ActivityPrivilegeBuyWGCtrl:OnDayChange()
    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.PRIVILEGEBUY) then
        self.data:ReCalcActDay()
        if self.view then
            self.view:Flush()
        end
    
        RemindManager.Instance:Fire(RemindName.RemindPrivilegeBuy)
    end
end

--接受协议
function ActivityPrivilegeBuyWGCtrl:OnSCTequanShopInfo(protocol)
    --数据存储
    self.data:SetInfo(protocol)
    --界面刷新
    if self.view then
        self.view:Flush()
    end
    --刷新红点
    RemindManager.Instance:Fire(RemindName.RemindPrivilegeBuy)
end