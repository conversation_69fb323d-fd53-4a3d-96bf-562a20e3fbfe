PierreDirectPurchaseAlert = PierreDirectPurchaseAlert or BaseClass(SafeBaseView)

local KeyMgr = {
	["ArenaJingJiChang"] = 0,
	["YunBiao_One"] = 0,
	["YunBiao_Two"] = 0,
	["YunBiao_Three"] = 0,
	["level_purchase_view"] = 0,
	["JingJiChang_HighPowerIsEnter"] = 0,
	["onevone_pei_dai_alert"] = 0,
	["Welfare_QiFu_Mark"] = 0,
}
local NORMALLABLERECTWIDTH = 500 --lable矩形框初始宽度
--is_btn_dislocation:按钮转换位置
function PierreDirectPurchaseAlert:__init(str, ok_func, cancel_func, close_func, has_checkbox, is_show_action, is_maskbg_button_click, is_btn_dislocation, is_control, view_layer)
	self.view_layer = view_layer and view_layer or UiLayer.PopTop
	self.self_control_rendring = is_control
	-- 需求: 蒙版点击无响应
	is_maskbg_button_click = nil == is_maskbg_button_click and true or is_maskbg_button_click
	self:SetMaskBg(is_maskbg_button_click, true)
	self.view_name = "PierreDirectPurchaseAlert"
	self:AddViewResource(0, "uis/view/pierre_direct_purchase_ui_prefab", "pierre_direct_purchase_dialog")

	self.is_async_load = false
	self.is_modal = true
	self.content_str = nil ~= str and str or ""
	self.ok_func = ok_func
	self.cancel_func = cancel_func
	self.close_func = close_func
	if has_checkbox == nil then
		has_checkbox = false
	end

	self.has_checkbox = has_checkbox
    self.is_nolonger_tips = true					-- 是否勾选不再提示
    self.is_show_bg = true                           --是否显示背景框
	self.checkbox_tip_text = Language.Common.DontTip
	self.ok_btn_text = Language.Common.BtnOK
	self.cancel_btn_text = Language.Common.BtnCancel
	self.title_view_name = Language.Common.AlertTitile
	self.data = nil
	self.record_not_tip = false
	self.auto_do_func = true
	self.check_box_default_select = true

	self.rich_auto_close = nil
	self.auto_close_time = -1

	self.close_type = -1 --0 确定关闭， 1 取消关闭， 2 其他地方面板关闭

	self.is_use_one = false
	self.is_no_closeBtn = false

	self.panel_type = nil

	self.label_rect_width = -1
	--提示文字是否需要左对齐
	self.is_left = true
	--提示文字对齐样式 如：UnityEngine.TextAnchor.MiddleLeft
	self.alignment_type = nil
	self.is_btn_dislocation = false

	self.is_show_text_link = false
end

function PierreDirectPurchaseAlert:SetMarkName(str)
	self.panel_type = str
end

function PierreDirectPurchaseAlert:OpenCallBack()
	self:AutoSetLabelRectWidth()
	self:AutoSetAlignment()
end

function PierreDirectPurchaseAlert:ReleaseCallBack()
	self.rich_auto_close = nil
	self.rich_dialog = nil
	self.dialog_line_spacing = nil
	self.image_bg = nil
	if CountDownManager.Instance:HasCountDown("common_alert_timer") then
		CountDownManager.Instance:RemoveCountDown("common_alert_timer")
	end
	if self.check_box_default_select then
		self.is_nolonger_tips = true
	end

	self.is_should_load_call = nil
	self.load_complete = nil
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.MainUIView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
	self.alignment_type = nil
	self.is_btn_dislocation = nil

	self.is_show_text_link = false
end

--外部调用：关闭取消按钮
function PierreDirectPurchaseAlert:SetUseOneSign( enable )
	self.is_use_one = enable
end

function PierreDirectPurchaseAlert:ShowIndexCallBack()
	self.node_list["btn_cancel"]:SetActive(true)

	if self.is_use_one then
		self:UseOne()
	end

	self:NoCloseButton()
end

function PierreDirectPurchaseAlert:LoadCallBack()
	self.rich_dialog = self.node_list["rich_dialog"]
	self.image_bg = self.node_list["img9_autoname_2"]
    self:SetLableString(self.content_str)
	if self.pos then
		self:SetRichDialogTxtPos(self.pos)
	end

	self:SetTextLineSpace(self.dialog_line_spacing)
    if self.is_kuozhan then
    	NORMALLABLERECTWIDTH = 500
	end
	self.node_list["btn_OK"]:FindObj("btn_text").tmp.text = self.ok_btn_text
	self.node_list["btn_cancel"]:FindObj("btn_text").tmp.text = self.cancel_btn_text
	self.node_list["layout_nolonger_tips"]:SetActive(self.has_checkbox)
	self.node_list["label_no_longer"].tmp.text = self.checkbox_tip_text
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind1(self.OnClickCancel, self))
	self.node_list["img_nohint_hook"]:SetActive(self.check_box_default_select)
	self.node_list["layout_nolonger_tips"].button:AddClickListener(BindTool.Bind1(self.OnClickCheckBox, self))

    self:FlushTextLinkShow()
	
	self.load_complete = true
	if self.is_should_load_call then
		self.is_should_load_call = nil
		self:AutoSetAlignment()
		self:SetAlignment(self.alignment_type)
		self:AutoSetLabelRectWidth()
	end

	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.MainUIView, self.get_guide_ui_event)

	if self.is_btn_dislocation then
		if self.node_list["btn_OK"] then
			self.node_list["btn_OK"].transform:SetSiblingIndex(0)
		end
	else
		if self.node_list["btn_OK"] then
			self.node_list["btn_OK"].transform:SetSiblingIndex(1)
		end
	end
end

function PierreDirectPurchaseAlert:AutoSetAlignment()
	if not self.load_complete then
		self.is_should_load_call = true
		return
	end
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["rich_dialog"].rect)
	local hight = math.ceil(self.node_list["rich_dialog"].rect.rect.height)
	 if self.alignment_type then
		self.rich_dialog.text.alignment = self.alignment_type
	else
		self.rich_dialog.text.alignment = UnityEngine.TextAnchor.MiddleCenter
	end
end

function PierreDirectPurchaseAlert:SetAlignment(alignment_type)
	-- body
	self.alignment_type = alignment_type
	if alignment_type and self.rich_dialog then
		self.rich_dialog.text.alignment = alignment_type
	end
end
function PierreDirectPurchaseAlert:SetRichDialogTxtPos(pos)
	if pos then
		self.pos = pos
		if self:IsLoadedIndex(0) then
			self.rich_dialog.transform.localPosition = pos
			self.pos = nil
		end
	end
end

function PierreDirectPurchaseAlert:OnClickCheckBox()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	self.node_list["img_nohint_hook"]:SetActive(not is_visible)
	self.is_nolonger_tips = not is_visible
end

function PierreDirectPurchaseAlert:SetCheckBoxDefaultSelect(visible)
	self.check_box_default_select = visible
	self.is_nolonger_tips = visible
	if self.node_list["img_nohint_hook"] then
		self.node_list["img_nohint_hook"]:SetActive(visible)
	end
end

function PierreDirectPurchaseAlert:OnClickOK()
	self.close_type = 0
	local can_close = true
    local vas = self.node_list["img_nohint_hook"]:GetActive()
	if vas then
		if self.panel_type and KeyMgr[self.panel_type] then
			KeyMgr[self.panel_type] = 1
		end

		if self.today_str then
	        local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	        local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	        PlayerPrefsUtil.SetInt(self.today_str .. main_role_id, open_day)
	    end
	end

	local login_key = self.cur_login_no_remind_key
	if login_key ~= nil then
		TipWGData.Instance:SetCurLoginNoRemindListByKey(login_key, self.is_nolonger_tips and self.has_checkbox)
	end

	if nil ~= self.ok_func then
		self.record_not_tip = self.is_nolonger_tips
		can_close = self.ok_func(self.is_nolonger_tips, self.data)
		if nil == can_close then can_close = true end
	end


	if can_close then
		self:Close()
	end
end

function PierreDirectPurchaseAlert:OnClickCancel()
	self.close_type = 1
	local can_close = true
	if nil ~= self.cancel_func then
		can_close = self.cancel_func()
		if nil == can_close then can_close = true end
	end

	if can_close then
		self:Close()
	end
end

function PierreDirectPurchaseAlert:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("common_alert_timer") then
		CountDownManager.Instance:RemoveCountDown("common_alert_timer")
	end
	if self.label_rect_width > 0 then
		self.label_rect_width = -1
	end

	if self.close_type == 2 then
		if nil ~= self.close_func then
			self.close_func()
		end
	end

	self:RestAlert()

    self.close_type = -1
    self.is_show_bg = true
end

-- 设置是否显示背景
function PierreDirectPurchaseAlert:SetImgBGSetActive(is_active)
    self.is_show_bg = is_active
    if self.node_list["img_bg"] then
        self.node_list["img_bg"].image.enabled = self.is_show_bg
    end
end

-- 设置自动关闭时间
function PierreDirectPurchaseAlert:SetAutoCloseTime(time)
	self.auto_close_time = time or self.auto_close_time
end

-- 设置内容
function PierreDirectPurchaseAlert:SetLableString(str)
	if nil ~= str and "" ~= str then
		self.content_str = str
		if self.rich_dialog then
			EmojiTextUtil.ParseRichText(self.rich_dialog.emoji_text, self.content_str, 26, COLOR3B.WHITE)
			self:AutoSetAlignment()
		end
	end
end

-- 设置Title内容
function PierreDirectPurchaseAlert:SetTextViewName(str)
	if nil ~= str and "" ~= str then
		self.title_view_name = str
		if nil ~= self.node_list["title_view_name"] then
			self.node_list["title_view_name"].tmp.text = self.title_view_name
		end
	end
end

-- 设置确定按钮文字
function PierreDirectPurchaseAlert:SetOkString(str)
	if nil ~= str and "" ~= str then
		self.ok_btn_text = str

		if nil ~= self.node_list["btn_OK"] then
			self.node_list["btn_OK"]:FindObj("btn_text").tmp.text = self.ok_btn_text
		end
	end
end

-- 设置取消按钮文字
function PierreDirectPurchaseAlert:SetCancelString(str)
	if nil ~= str and "" ~= str then
		self.cancel_btn_text = str

		if nil ~= self.node_list["btn_cancel"] then
			self.node_list["btn_cancel"]:FindObj("btn_text").tmp.text = self.cancel_btn_text
		end
	end
end

-- 设置复选框的显示文本
function PierreDirectPurchaseAlert:SetCheckBoxText(str)
	if nil ~= str and "" ~= str then
		self.checkbox_tip_text = str

		if nil ~= self.node_list["label_no_longer"] then
			self.node_list["label_no_longer"].tmp.text = self.checkbox_tip_text
		end
	end
end

-- 设置确定回调
function PierreDirectPurchaseAlert:SetOkFunc(ok_func)
	self.ok_func = ok_func
end

-- 设置取消回调
function PierreDirectPurchaseAlert:SetCancelFunc(cancel_func)
	self.cancel_func = cancel_func
end

-- 设置关闭回调
function PierreDirectPurchaseAlert:SetCloseFunc(close_func)
	self.close_func = close_func
end

function PierreDirectPurchaseAlert:SetData(value)
	self.data = value
end

-- 是否显示复选框, is_show是否显示，today_str今日不再提示关键字
function PierreDirectPurchaseAlert:SetShowCheckBox(is_show, today_str)
	self.today_str = today_str
	if self.has_checkbox ~= is_show then
		self.has_checkbox = is_show
		if nil ~= self.node_list["layout_nolonger_tips"] then
			self.node_list["layout_nolonger_tips"]:SetActive(is_show)
		end
	end
end

-- 当前登录不再提示
function PierreDirectPurchaseAlert:SetCurLoginNoRemindKey(key)
	self.cur_login_no_remind_key = key
end

function PierreDirectPurchaseAlert:GetIsNolongerTips()
	return self.is_nolonger_tips
end

function PierreDirectPurchaseAlert:Open()
	local is_no_open = self:GetIsNeedOpenViewFlag()

	if is_no_open then
		self.ok_func(self.is_nolonger_tips, self.data)
	else
		SafeBaseView.Open(self)
	end
end

function PierreDirectPurchaseAlert:SetAutoDoFunc(value)
	self.auto_do_func = value
end

function PierreDirectPurchaseAlert:Close()
	if self.close_type == -1 then
		self.close_type = 2
	end

	SafeBaseView.Close(self)
end

--设置让取消按钮消失
function PierreDirectPurchaseAlert:UseOne()
	self.is_use_one = true
	if nil ~= self.node_list and nil ~= self.node_list["btn_cancel"] and nil ~= self.node_list["btn_OK"] then
		self.node_list["btn_cancel"]:SetActive(false)
	end
end

function PierreDirectPurchaseAlert:SetOkCancelBtnImage(image_str_ok, image_str_cancel)
    if image_str_ok ~= nil and image_str_cancel ~= nil then
        self.ok_btn_image_name = image_str_ok
        self.cancel_btn_image_name = image_str_cancel
        if nil ~= self.node_list and nil ~= self.node_list["btn_cancel"] and nil ~= self.node_list["btn_OK"] then
            self.node_list["btn_OK"].image:LoadSprite(ResPath.GetCommonButton(self.ok_btn_image_name))
            self.node_list["btn_cancel"].image:LoadSprite(ResPath.GetCommonButton(self.cancel_btn_image_name))
        end
    end
end

--设置按钮转换
function PierreDirectPurchaseAlert:SetBtnDislocation(is_btn_dislocation)
	self.is_btn_dislocation = is_btn_dislocation
end

function PierreDirectPurchaseAlert:NoCloseButton(value)
	if nil ~= value then
		self.is_no_closeBtn = value
	end
end

function PierreDirectPurchaseAlert:GetIsShowTips()
	return not self.record_not_tip
end

function PierreDirectPurchaseAlert:ClearCheckHook()
	self.record_not_tip = false
end

function PierreDirectPurchaseAlert:OnClickCloseWindow()
	if self.close_before_func then
		self.close_before_func()
	end

	self:Close()
end

-- 点击遮罩关闭前 执行方法
function PierreDirectPurchaseAlert:SetCloseBeforeFunc(func)
	self.close_before_func = func
end

function PierreDirectPurchaseAlert:SetTextContent(content)
	if not content then
		content = ""
	end
	self.rich_dialog.tmp.text = content

end

function PierreDirectPurchaseAlert:SetTextLineSpace(num)
	self.dialog_line_spacing = num
	if self.rich_dialog and nil ~= self.dialog_line_spacing then
		self.rich_dialog.text.lineSpacing = self.dialog_line_spacing
	end
end
function PierreDirectPurchaseAlert:GetClickCheckBoxIsShow()
	local is_visible = self.node_list["img_nohint_hook"]:GetActive()
	return is_visible or false
end

function PierreDirectPurchaseAlert:SetLableRectWidth(width)
	self.label_rect_width = width
end

function PierreDirectPurchaseAlert:AutoSetLabelRectWidth()
	if not self.load_complete then
		self.is_should_load_call = true
		return
	end
	local height = self.rich_dialog.rect.rect.height
	if self.label_rect_width > 0 then
		self.rich_dialog.rect.sizeDelta = Vector2(self.label_rect_width, height)
	else
		self.rich_dialog.rect.sizeDelta = Vector2(NORMALLABLERECTWIDTH, height)
	end
end

function PierreDirectPurchaseAlert:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.AlertEnter then
		return self.node_list["btn_OK"], BindTool.Bind1(self.OnClickOK, self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function PierreDirectPurchaseAlert:SetTextIsLeft(states)
	if states ~= nil then
		self.is_left = states
	end
end

--获取复选框的状态
function PierreDirectPurchaseAlert:SetCheckBoxState()
	if self.node_list and self.node_list["img_nohint_hook"] then
		return self.node_list["img_nohint_hook"]:GetActive()
	end
	return false
end

--获取是否已经标记本次不在提示
function PierreDirectPurchaseAlert:GetIsNeedOpenViewFlag()
	local has_today_flag = false
	local has_login_flag = false

	if self.today_str then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		has_today_flag = PlayerPrefsUtil.GetInt(self.today_str .. main_role_id) == open_day
	end

	local login_key = self.cur_login_no_remind_key
	if not has_today_flag and login_key ~= nil then
		has_login_flag = TipWGData.Instance:GetCurLoginNoRemindListByKey(login_key)
	end

	if (self.record_not_tip and self.has_checkbox)
		or (self.panel_type and KeyMgr[self.panel_type] and KeyMgr[self.panel_type] == 1 )
		or has_today_flag
		or has_login_flag then

		if self.auto_do_func then
			return true
		end
	else
		return false
	end
end

function PierreDirectPurchaseAlert:SetCountDownFunc(count_down_time, updata_func, complete_func)
	CountDownManager.Instance:RemoveCountDown("common_alert_timer")
	self.updata_func = updata_func
	self.complete_func = complete_func
	if count_down_time > 0 then
		CountDownManager.Instance:AddCountDown(
			"common_alert_timer",
			BindTool.Bind(self.UpdataAlertTime, self),
			BindTool.Bind(self.CompleteAlertTime, self),
			nil,
			count_down_time,
			1
		)
	end
end

function PierreDirectPurchaseAlert:UpdataAlertTime(elapse_time, total_time)
	if self.updata_func then
		self.updata_func()
	end
end

function PierreDirectPurchaseAlert:CompleteAlertTime()
	if self.complete_func then
		self.complete_func()
	end
end

function PierreDirectPurchaseAlert:SetIsSceneLoading(is_scene_loading)
	self.view_layer = is_scene_loading and UiLayer.SceneLoadingPop or UiLayer.PopTop
end

-- 设置文本跳转回调
function PierreDirectPurchaseAlert:SetTextLink(str, text_link_func)
	self.text_link_desc = str
	self.text_link_func = text_link_func
	self.is_show_text_link = true
end

-- 设置文本跳转回调
function PierreDirectPurchaseAlert:FlushTextLinkShow()
	if not self.node_list["text_link"] then return end
	if self.is_show_text_link then
		if self.text_link_desc and self.node_list["text_link"].emoji_text then
			EmojiTextUtil.ParseRichText(self.node_list["text_link"].emoji_text, self.text_link_desc, 20, COLOR3B.DEFAULT)
		end
    	self.node_list["text_link"].button:AddClickListener(BindTool.Bind1(self.OnClickTextLink, self))
		self.node_list["text_link"]:SetActive(true)
	else
		self.node_list["text_link"]:SetActive(false)
    end
end

--点击跳转文本
function PierreDirectPurchaseAlert:OnClickTextLink()
	if nil ~= self.text_link_func then
		self.close_type = 1
		local can_close = self.text_link_func()
		if nil == can_close then can_close = true end
		if can_close then
			self:Close()
		end
	end
end

function PierreDirectPurchaseAlert:RestAlert()
	self.ok_btn_text = Language.Common.BtnOK
	self.cancel_btn_text = Language.Common.BtnCancel
	self.title_view_name = Language.Common.AlertTitile
	if nil ~= self.node_list["title_view_name"] then
		self.node_list["title_view_name"].tmp.text = self.title_view_name
	end
end