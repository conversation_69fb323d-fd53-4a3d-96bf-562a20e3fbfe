SocietyView = SocietyView or BaseClass(SafeBaseView)

local STORGE_BAG_MAX_GRID_NUM = 8		--最大格子数
local STORGE_BAG_PAGE_NUM = 2				-- 页数
local STORGE_BAG_PAGE_COUNT = 4			-- 每页个数
local STORGE_BAG_ROW = 1					-- 行数
local STORGE_BAG_COLUMN = 4


function SocietyView:InitShopView()
	--self.fish_list_view_cell = {} -- 商店鱼列表

	local fish_cfg = SocietyWGData.Instance.fish_type
	self.sho_fish_cfg = __TableCopy(fish_cfg)
	--print_error("333333333",fish_cfg)
	self.sho_fish_cfg[0] = table.remove(self.sho_fish_cfg, 1)
	--print_error(self.node_list["move_fish_icon"],"000000000000000")
	--print_error("shangdain 666666666666")
	XUI.AddClickEventListener(self.node_list["btn_return_fishpond"], BindTool.Bind1(self.CloseFishShop, self)) 	--转回鱼塘
	XUI.AddClickEventListener(self.node_list["btn_fish_shop"], BindTool.Bind1(self.OpenFishShop, self)) 	--转到商店
	XUI.AddClickEventListener(self.node_list["btn_shop_turnleft"], BindTool.Bind1(self.OnClickTrunLeft, self)) --商店物品左移
	XUI.AddClickEventListener(self.node_list["btn_shop_turnright"], BindTool.Bind1(self.OnClickTrunRight, self)) --商店物品右移

	self.page_fishinfo_list = self.node_list["ph_fishshop_list"]

	local page_fishinfo_list_delegate = self.page_fishinfo_list.page_simple_delegate
	      page_fishinfo_list_delegate.NumberOfCellsDel = BindTool.Bind(self.StorgeGetNumberOfCells11, self)
		  page_fishinfo_list_delegate.CellRefreshDel = BindTool.Bind(self.StorgeBagRefreshCell11, self)
	-- XUI.AddClickEventListener(self.node_t_list.btn_return_fishpond.node, BindTool.Bind1(self.CloseFishShop, self)) 	--转回鱼塘
	-- XUI.AddClickEventListener(self.node_t_list.btn_fish_shop.node, BindTool.Bind1(self.OpenFishShop, self)) 	--转到商店
	-- XUI.AddClickEventListener(self.node_t_list.btn_shop_turnleft.node, BindTool.Bind1(self.OnClickTrunLeft, self)) --商店物品左移
	-- XUI.AddClickEventListener(self.node_t_list.btn_shop_turnright.node, BindTool.Bind1(self.OnClickTrunRight, self)) --商店物品右移

	if self.change_callback ~= nil then
		RoleWGData.Instance:NotifyAttrChange(self.change_callback, {"coin", "bind_coin", "gold", "bind_gold"})
	end

	--local event_listener = self.node_list["fish_event_trigger"].event_trigger_listener
	-- if event_listener ~= nil then
	-- 	print_error("event_listener ~= nil")
	-- 	event_listener:AddPointerDownListener(BindTool.Bind1(self.OnPointDown, self))
	-- 	-- event_listener:AddPointerUpListener(BindTool.Bind1(self.OnPointUp, self))
	-- 	-- event_listener:AddMoveListener(BindTool.Bind1(self.OnTouchMove, self))
	-- 	event_listener:AddDragListener(BindTool.Bind1(self.OnDrag, self))
	-- 	event_listener:AddDropListener(BindTool.Bind1(self.OnDrop, self))
	-- 	event_listener:AddEndDragListener(BindTool.Bind1(self.OnEndDrag,self))
	-- end
end

function SocietyView:StorgeGetNumberOfCells11()
	return STORGE_BAG_MAX_GRID_NUM
end

-- function SocietyView:OnEndDrag(event_data)
-- 	 print_error("SocietyView OnEndDrag",event_data.position)
-- end

-- function SocietyView:OnDrop(event_data)
-- 	print_error("SocietyView OnDrop",event_data.position)
-- end

-- function SocietyView:OnDrag(event_data)
-- 	print_error("SocietyView OnDrag",event_data.position)
-- end

-- function SocietyView:OnPointDown(event_data)
-- 	 print_error("SocietyView:OnPointDown",event_data.position)
-- 	 if event_data.pressEventCamera ~= nil then
-- 	 	print_error("SocietyView:OnPointDown",event_data.pressEventCamera.name)
-- 	 end

-- 	 if event_data.enterEventCamera ~= nil then
-- 	 	 print_error("SocietyView:OnPointDown",event_data.enterEventCamera.name)
-- 	 end
-- end

-- function SocietyView:OnPointUp(event_data)
-- 	 print_error("SocietyView:OnPointUp",event_data.position)
-- end

-- function SocietyView:OnTouchMove(event_data)
-- 	 print_error("SocietyView:OnTouchMove",event_data)
-- end


function SocietyView:ShopViewDelete()
	if nil ~= self.page_fishshop_list then
		self.page_fishshop_list:DeleteMe()
		self.page_fishshop_list = nil
	end
	if RoleWGData.Instance and self.change_callback ~= nil then
		RoleWGData.Instance:UnNotifyAttrChange(self.change_callback)
	end
	self.drag_layout = nil
	self.page_fishinfo_list = nil
	self.sho_fish_cfg = {}
end

function SocietyView:RoleDataAttrChangeCallBack(attr_name, value)
	if attr_name == "coin" or attr_name == "bind_coin" or attr_name == "gold" or attr_name == "bind_gold" then
		self:FlushFishShop()
	end
end

-- 转到商店
function SocietyView:OpenFishShop()
	if SocietyWGData.Instance:CurIsOwnPond() then
		local role_id = RoleWGData.Instance.role_vo.role_id
		SocietyWGCtrl.Instance:ChangeShowPondUid(role_id)
		self.node_list["layout_fish_shop"]:SetActive(true)
		self.layout_fishpond_down:SetActive(false)
		if nil == self.page_fishshop_list then
			self:CreateFishListView()
		else
			self:FlushFishShop()
		end
	else
		self:CliskBulletHandler()
	end
end
function SocietyView:StorgeBagRefreshCell11(index, cellObj)
	-- 构造Cell对象.
	local cell = self.page_fishinfo_list[cellObj]
	if nil == cell then
		cell = FishShopListItem.New(cellObj)
		--cell:SetToggleGroup(self.storge_bag_list_view.toggle_group)
		self.page_fishinfo_list[cellObj] = cell
		cell:SetDragFishImg(self.node_list["move_fish_icon"],self.node_list["Mask"].rect,
		self.node_list["yangyufanwei"].rect,self.node_list["fish_tip_bg"],self.node_list["fish_tip_content"])
	end
	cell:SetIndex(index)
	local page = math.floor(index / STORGE_BAG_PAGE_COUNT)
	local cur_colunm = math.floor(index / STORGE_BAG_ROW) + 1 - page * STORGE_BAG_COLUMN
	local cur_row = math.floor(index % STORGE_BAG_ROW) + 1
	local grid_index = (cur_row - 1) * STORGE_BAG_COLUMN - 1 + cur_colunm  + page * STORGE_BAG_ROW * STORGE_BAG_COLUMN
	cell:SetData(self.sho_fish_cfg[grid_index])
end

-- 创建鱼列表
function SocietyView:CreateFishListView()
	self.page_fishinfo_list.list_view:Reload(function()
			self.page_fishinfo_list.list_page_scroll2:JumpToPageImmidate(0)
		end)
end

function SocietyView:OnDragStateHandler(is_begin, cell)

end

--翻向右
function SocietyView:OnClickTrunRight()
	self.page_fishinfo_list.list_page_scroll2:JumpToPage(1)
end

--翻向左
function SocietyView:OnClickTrunLeft()
	self.page_fishinfo_list.list_page_scroll2:JumpToPage(0)
end

-- 转回鱼塘
function SocietyView:CloseFishShop()
	self.node_list["layout_fish_shop"]:SetActive(false)
	self.layout_fishpond_down:SetActive(true)
end

-- 设置鱼图标是否可拖拽
function SocietyView:SetUiDragIsLock(value)
	if self.page_fishshop_list then
		for k,v in pairs(self.page_fishshop_list:GetAllCell()) do
			v:SetUiDragIsLock(value)
		end
	end
end

function SocietyView:FlushFishShop()

	if not self:IsOpen() or not self.node_list["layout_fish_shop"]:GetActive() then return end
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local all_info = SocietyWGData.Instance:GetFishpondAllInfo(role_id) or {}


	local page = self.page_fishinfo_list.list_page_scroll2:GetNowPage()
	--print_error(page)
	self.page_fishinfo_list.list_view:Reload(function()
			self.page_fishinfo_list.list_page_scroll2:JumpToPageImmidate(page)
		end)
end


function SocietyView:GetGuideFishShop(ui_name, param)
	local index = tonumber(param)
	if ui_name == GuideUIName.FishShopBtn then
		return self.node_t_list.btn_fish_shop.node, BindTool.Bind1(self.OpenFishShop, self)
	elseif ui_name == GuideUIName.FishShopCell then
		if self.page_fishshop_list then
			if self.page_fishshop_list then
				self.page_fishshop_list:ChangeToPage(0)
			end
			local cells = self.page_fishshop_list:GetAllCell()
			if cells[index - 1] then
				return cells[index - 1]:GetGuideFishShop()
			end
			return nil, nil
		end
	end
	return nil, nil
end

------------------itemRender-----------------
FishShopListItem = FishShopListItem or BaseClass(BaseRender)

function FishShopListItem:__init()
	self.node_list["move_icon"]:SetActive(false)
	self.base_rect = nil
	self.base_yang_yu_rect = nil
	self.origin_pos_x = 0
	self.origin_pos_y = 0
	self.tip_content_bg = nil
	self.tip_content_text = nil
	self.on_point_down = BindTool.Bind1(self.OnPointDown, self)
	self.on_point_up = BindTool.Bind1(self.OnPointUp, self)
	self.on_drag = BindTool.Bind1(self.OnDrag, self)
	self.on_end_drag = BindTool.Bind1(self.OnEndDrag, self)

	self.move_obj = nil

	--XUI.AddClickEventListener(self.node_list["icon"], BindTool.Bind1(self.CloseFishShop, self))
	self.event_listener = self.node_list["icon"].event_trigger_listener
	if self.event_listener ~= nil then

		self.event_listener:AddPointerDownListener(self.on_point_down)
		self.event_listener:AddPointerUpListener(self.on_point_up)
		-- event_listener:AddMoveListener(BindTool.Bind1(self.OnTouchMove, self))
		self.event_listener:AddDragListener(self.on_drag)
		--event_listener:AddDropListener(BindTool.Bind1(self.OnDrop, self))
		self.event_listener:AddEndDragListener(self.on_end_drag)
	end
end
function FishShopListItem:CloseFishShop()
	--print_error(self.data.fish_id)
end

function FishShopListItem:OnDrag(event_data)
	if self.move_obj then
		local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.base_rect,
			event_data.position, UICamera, Vector2(0, 0))

		self.move_obj.transform.localPosition = local_bullet_start_pos_tbl
	end

end
function FishShopListItem:OnPointUp(event_data)
	self.tip_content_bg:SetActive(false)
	--self.move_obj:SetActive(false)
	self.close_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SetFishIconBool, self), 0.5)
	-- print_error("SocietyView:OnPointUp",event_data.position)
end
function FishShopListItem:OnPointDown(event_data)
	--print_error(self.data.fish_type)
	self.tip_content_bg:SetActive(true)
	if self.move_obj then

		local bundle,asset = ResPath.GetItem(self.data.fish_id)
		self.move_obj.image:LoadSprite(bundle,asset)
		self.move_obj:SetActive(true)
		local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.base_rect, event_data.position, UICamera, Vector2(0, 0))
		self.origin_pos_x = local_bullet_start_pos_tbl.x
		self.origin_pos_y = local_bullet_start_pos_tbl.y
		self.move_obj.transform.localPosition = local_bullet_start_pos_tbl
		--print_error(local_bullet_start_pos_tbl,self.move_obj.transform.localPosition)
	end
	local fish_cfg = SocietyWGData.Instance:GetFishCfgByType(self.data.fish_type)
	local max_lv = SocietyWGData.Instance:GetFishMaxLvByType(self.data.fish_type) or 1
	local content = ""

	if self.data.fish_type == 1 then 	--守卫鱼
		local hour = math.floor(SocietyWGData.Instance.other_cfg.guard_fish_last_time_s / 3600)
		content = string.format(Language.Fishpond.GuardFishDragTips, hour)
	elseif self.data.fish_type == 2 or self.data.fish_type == 5 then 	--虚拟物品鱼
		local reward_cfg = SocietyWGData.Instance:GetRewardCfgByLv(RoleWGData.Instance.role_vo.level)
		local reward_str = ""
		local reward_time = SocietyWGData.Instance:GetCanHarvestTimeByType(self.data.fish_type) or 0
		reward_time = math.floor(reward_time / 3600)
		if reward_cfg and 2 == self.data.fish_type then --铜币
			reward_str = math.floor(reward_cfg.base_coin / max_lv) .. ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_COIN)

		elseif reward_cfg and 5 == self.data.fish_type then --绑元
			reward_str = math.floor(reward_cfg.base_xinhun / max_lv) .. ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL)
		end
		content = string.format(Language.Fishpond.ExpFishDragTips, reward_time, reward_str, max_lv)
	elseif self.data.fish_type == 3 or self.data.fish_type == 4 then 	--物品鱼
		local reward_time = SocietyWGData.Instance:GetCanHarvestTimeByType(self.data.fish_type) or 0
		reward_time = math.floor(reward_time / 3600)
		local lv_cfg = SocietyWGData.Instance:GetFishLvCfgByTypeAndLv(self.data.fish_type, max_lv)
		local item_str = ""
		local color = "ffff00"
		if lv_cfg then
			local item_name = ItemWGData.Instance:GetItemName(lv_cfg.harvest_item.item_id)
			local num = lv_cfg.harvest_item.num or 0
			item_str = item_name and item_name .. "x" .. num or ""
			color = ItemWGData.Instance:GetItemColor(lv_cfg.harvest_item.item_id)
		end
		content = string.format(Language.Fishpond.ItemFishDragTips, item_str)
	end
	self.tip_content_text.text.text = content
end

function FishShopListItem:OnEndDrag(event_data)

    local mask_x,mask_y =  self.base_rect.sizeDelta.x, self.base_rect.sizeDelta.y
    local without_yangyu_fanwei_y = self.base_yang_yu_rect.sizeDelta.y



	if self.move_obj then
		if math.abs(event_data.position.y) >= without_yangyu_fanwei_y and math.abs(event_data.position.y) <= (mask_y + 10) and
			math.abs(event_data.position.x) <= mask_x and math.abs(event_data.position.x) >= 113 then
			SocietyWGCtrl.Instance:SendFishPoolRaiseReq(self.data.fish_type)
			self.move_obj:SetActive(false)
		else
			local tween = self.move_obj.rect:DOAnchorPosX(self.origin_pos_x, 0.5)
			local tween1 = self.move_obj.rect:DOAnchorPosY(self.origin_pos_y, 0.5)
			tween:SetEase(DG.Tweening.Ease.Linear)
			tween1:SetEase(DG.Tweening.Ease.Linear)
			self.close_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SetFishIconBool, self), 0.5)
		end
	end
end

function FishShopListItem:SetFishIconBool()
	self.move_obj:SetActive(false)
end

function FishShopListItem:__delete()
	if nil ~= self.event_listener then
		if nil ~= self.on_point_down then
			self.event_listener:RemovePointerDownListener(self.on_point_down)
			self.on_point_down =  nil
		end
		if nil ~= self.on_point_up then
			self.event_listener:RemovePointerUpListener(self.on_point_up)
			self.on_point_up =  nil
		end
		if nil ~= self.on_drag then
			self.event_listener:RemoveDragListener(self.on_drag)
			self.on_drag =  nil
		end
		if nil ~= self.on_end_drag then
			self.event_listener:RemoveEndDragListener(self.on_end_drag)
			self.on_end_drag =  nil
		end
	end
	if self.item ~= nil then
		self.item:DeleteMe()
		self.item = nil
	end
	if nil ~= self.close_timer_quest then
		GlobalTimerQuest:EndQuest(self.close_timer_quest)
		self.close_timer_quest = nil
	end
end

function FishShopListItem:CreateChild()
	BaseRender.CreateChild(self)

	self:CreateFishCell()
	self.rich_limit = self.node_tree.rich_limit.node
	self.label_price_txt = self.node_tree.label_price_txt.node
	self.lbl_fish_price = self.node_tree.lbl_fish_price.node
	self.lbl_fish_name = self.node_tree.lbl_fish_name.node
end

function FishShopListItem:SetUiDragIsLock(value)
	if self.item then
		self.item:SetUiDragIsLock(value)
	end
end

function FishShopListItem:CreateFishCell()

end

function FishShopListItem:SetDragStateCallBack(drag_state_call_back)
	self.drag_state_call_back = drag_state_call_back
end

function FishShopListItem:OnDragStateHandler(is_begin, cell)
	if self.drag_state_call_back then
		self.drag_state_call_back(is_begin, cell)
	end
end

function FishShopListItem:OnFlush()
	if nil == self.data  then
		self.node_list.root_fishshop_rander_Item:SetActive(false)
		return
	else
		self.node_list.root_fishshop_rander_Item:SetActive(true)

	end
	local bundle,asset = ResPath.GetItem(self.data.fish_id)
	self.node_list["icon"].image:LoadSprite(bundle,asset)
	self.node_list["move_icon"].image:LoadSprite(bundle,asset)

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.fish_id)
	if item_cfg then
		self.node_list["lbl_fish_name"].text.text = item_cfg.name

	end

	local other_cfg = SocietyWGData.Instance:GetOtherCfg()

	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local all_info = SocietyWGData.Instance:GetFishpondAllInfo(role_id) or {}
	-- print_error(all_info)
	local record_info = all_info.day_raise_record_list
	-- 当天购买次数
	local day_raise_count = 0
	if record_info then
		for k, v in pairs(record_info) do
			if v and v.fish_type == self.data.fish_type then
				day_raise_count = v.day_raise_count
				break
			end
		end
	end

	local coin_name = ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_COIN) or " "
	local color = COLOR3B.YELLOW
	local fish_need_consume = 0
	if 0 ~= self.data.coin_price then
		fish_need_consume = self.data.coin_price
		coin_name = ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_COIN) or " "

	elseif 0 ~= self.data.bind_gold_price then
		fish_need_consume = self.data.bind_gold_price + other_cfg.add_bind_gold_price * day_raise_count
		coin_name = ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL) or " "

	elseif 0 ~= self.data.gold_price then
		fish_need_consume = self.data.gold_price + other_cfg.add_gold_price * day_raise_count
		coin_name = ItemWGData.Instance:GetItemName(COMMON_CONSTS.VIRTUAL_ITEM_GOLD) or " "

	end
	self.node_list["lbl_fish_price"].text.text = fish_need_consume .. coin_name



	local normal_info = all_info.normal_info or {}
	local pool_level = normal_info.pool_level or 0
	local need_pool_level = self.data.need_pool_level
	local content = ""
	if pool_level >= need_pool_level then
		local leave_num = 0
		if self.data.day_raise_limit_count > 0 then
			leave_num = math.floor(self.data.day_raise_limit_count - day_raise_count)
		else
			leave_num = Language.Daily.CapNoLimmit
		end
		local while_num = 0
		if self.data.fish_type == SocietyWGData.GuardFishVo.fish_type then --是否守卫鱼
			local normal_info = all_info.normal_info or {}
			local lv_cfg = SocietyWGData.Instance:GetFishpondCfgByLv(normal_info.pool_level) or {}
			local guard_fish_capacity = lv_cfg.guard_fish_capacity or 0
			while_num = guard_fish_capacity
		else
			while_num = self.data.pool_raise_limit_count
		end
		content = string.format(Language.Fishpond.ShopLimitNum, leave_num, while_num)

	else
		content = string.format(Language.Fishpond.ShopLvLimit, need_pool_level)

	end
	self.node_list["rich_limit"].text.text = content
	-- print_error(content)
	self.node_list["label_price_txt"]:SetActive(pool_level >= need_pool_level)
	self.node_list["lbl_fish_price"]:SetActive(pool_level >= need_pool_level)
end

function FishShopListItem:GetGuideFishShop()
	if self.item and self.item:GetData() then
		local item_id = self.item:GetData().item_id
		return self.item:GetView(), nil, ResPath.GetItem(item_id)
	end
	return nil, nil
end

function FishShopListItem:SetDragFishImg(drag_target,base_rct,yangyu_range,bg,tip_content)
	self.move_obj = drag_target
	self.base_rect = base_rct
	self.base_yang_yu_rect = yangyu_range
	self.tip_content_bg = bg
	self.tip_content_text = tip_content
end
