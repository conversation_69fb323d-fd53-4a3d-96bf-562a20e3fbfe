﻿using System;
using Nirvana;
using UnityEngine;

// 实现了多个弹道同时播放的逻辑，并在更新时检查所有弹道是否完成。
public sealed class ProjectileMulitiply : Projectile
{
    // 弹道对象
    [SerializeField]
    private ProjectileObject[] projectileObjects;

    private Transform target;           // 目标
    private Vector3 targetPosition;     // 目标位置
    private Action hited;               // 命中目标时的回调
    private Action complete;            // 播放完成时的回调

    public override void Play(
        Vector3 sourceScale,
        Transform target,
        int layer,
        Action hited,
        Action complete)
    {
        if (this.complete != null)
        {
            complete();
            return;
        }

        this.target = target;
        this.hited = hited;
        this.complete = complete;
        foreach (var projectileObj in this.projectileObjects)
        {
            projectileObj.Play(
                sourceScale, this.transform.position, layer);
        }
    }

    public void Update()
    {
        if (this.complete == null)
        {
            return;
        }

        if (this.target != null)
        {
            this.targetPosition = this.target.position;
        }

        bool isComplete = true;
        foreach (var projectileObj in this.projectileObjects)
        {
            projectileObj.Update(this.targetPosition);
            if (projectileObj.Playing)
            {
                isComplete = false;
            }
            else
            {
                if (this.hited != null)
                {
                    this.hited();
                    this.hited = null;
                }
            }
        }

        if (isComplete)
        {
            this.complete();
            this.complete = null;
        }
    }
}
