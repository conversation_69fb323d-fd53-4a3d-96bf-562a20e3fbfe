require("game/downloadweb/download_web_wg_data")
require("game/downloadweb/download_web_view")

DownLoadWebWGCtrl = DownLoadWebWGCtrl or BaseClass(BaseWGCtrl)

function DownLoadWebWGCtrl:__init()
	if DownLoadWebWGCtrl.Instance then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[DownLoadWebWGCtrl] attempt to create singleton twice!")
		return
	end

	DownLoadWebWGCtrl.Instance = self
	self.data = DownLoadWebWGData.New()
    self.view = DownLoadWebView.New(GuideModuleName.DownLoadWebView)
  
    self:RegisterAllProtocols()
end

function DownLoadWebWGCtrl:__delete()
	DownLoadWebWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function DownLoadWebWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSAgentRewardOperateReq)
	self:RegisterProtocol(SCAgentRewardInfo,"OnSCAgentRewardInfo")
end

-- 请求
function DownLoadWebWGCtrl:SendSDownLoadReq(operate_type, param_1, param_2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSAgentRewardOperateReq)
    protocol.operate_type = operate_type or 0
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol:EncodeAndSend()
end

function DownLoadWebWGCtrl:OnSCAgentRewardInfo(protocol)
	self.data:SetAgentRewardInfo(protocol)
	MainuiWGCtrl.Instance:FlushView(0, "down_load_web")
	ViewManager.Instance:FlushView(GuideModuleName.DownLoadWebView)
	RemindManager.Instance:Fire(RemindName.DownLoadWeb)
end

function DownLoadWebWGCtrl:OpenWebView()
	if GLOBAL_CONFIG.param_list.download_other_game_url ~= "" then
		WebView.Open(GLOBAL_CONFIG.param_list.download_other_game_url)
	end
end
