function WorldTreasureView:ReleaseCallBack_PinTu()
	if self.pintu_task_list_view then
		self.pintu_task_list_view:DeleteMe()
		self.pintu_task_list_view = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.js_final_reward_box_tween then
		self.js_final_reward_box_tween:Kill()
		self.js_final_reward_box_tween = nil
	end

	if self.timer_3 then
		GlobalTimerQuest:CancelQuest(self.timer_3)
		self.timer_3 = nil
	end

	if CountDownManager.Instance:HasCountDown("world_treasure_js_delay_time_count_down") then
        CountDownManager.Instance:RemoveCountDown("world_treasure_js_delay_time_count_down")
    end

	self.init_flag = nil
end

function WorldTreasureView:LoadIndexCallBack_PinTu()
	self.jigsaw_grade_change = true
	self.pintu_task_list_view = AsyncListView.New(WorldTreasurePinTuTaskItem, self.node_list.js_task_list_view)
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.js_display_model)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list.js_btn_get_reward, BindTool.Bind(self.OnClickJsGetRewardBtn, self))
	--XUI.AddClickEventListener(self.node_list.btn_upgrade_level, BindTool.Bind(self.OnClickUpGradeLevelBtn, self))

	self.init_flag = true
end

function WorldTreasureView:LoadPtImg()
	local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()

	local bundle, asset = ResPath.GetWorldTreasureRawImages("jsrh_bg_2")
	self.node_list.js_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.js_bg.raw_image:SetNativeSize()
	end)

    bundle, asset = ResPath.GetWorldTreasureImg("a3_xtyg_btd_4" .. cur_grade)
	self.node_list.final_reward_panel_bg.image:LoadSprite(bundle, asset)

    self.node_list.final_reward_desc.text.color = StrToColor(Language.WorldTreasure.GradeColorList1[cur_grade])
end

function WorldTreasureView:ShowIndexCallBack_PinTu()
	-- if self.node_list.node_effect_levelup then
	-- 	self.node_list.node_effect_levelup:SetActive(false)
	-- end
end

function WorldTreasureView:OnFlush_PinTu(param_t, index)
	if self.jigsaw_grade_change then
        self.jigsaw_grade_change = false
        self:LoadPtImg()
    end
	self:UpdatePinTuView()
end

function WorldTreasureView:UpdatePinTuView()
	local task_data_list = WorldTreasureWGData.Instance:GetPinTuTaskList()
	self.pintu_task_list_view:SetDataList(task_data_list)

	-- 积分进度
	local reward_state, cur_value, max_value = WorldTreasureWGData.Instance:GetPunTuFinalRewardState()
	--local new_value = cur_score / target_score
	local item_num_color = cur_value >= max_value and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.node_list.txt_progress.text.text = string.format(Language.WorldTreasure.PinTuBili, item_num_color, cur_value, max_value)
	-- self.node_list["progress_bg"]:SetActive(reward_state == TREASURE_OPERATE_TYPE.NOT_OVER)
	-- local old_value = self.node_list["img_shuijing_active"].image.fillAmount
	-- if self.init_flag or old_value == new_value then
	-- 	self.node_list["img_shuijing_active"].image.fillAmount = new_value
	-- else
	-- 	UITween.ImgFillDoValue(self.node_list["img_shuijing_active"], old_value, new_value, 0.3)
	-- end

	self.init_flag = false
	--self.node_list.btn_upgrade_level:SetActive(reward_state == TREASURE_OPERATE_TYPE.CAN_LINGQU)
	--self.node_list.no_get_active:SetActive(reward_state ~= TREASURE_OPERATE_TYPE.HAS_LINGQU)
	--self.node_list.js_btn_get_reward:SetActive(reward_state == TREASURE_OPERATE_TYPE.CAN_LINGQU)
	--self.node_list.js_display_model:SetActive(reward_state == TREASURE_OPERATE_TYPE.HAS_LINGQU)
	self:FlushTimeCountDown()

	local is_remind = reward_state == TREASURE_OPERATE_TYPE.CAN_LINGQU
	self.node_list.js_reward_get_flag:SetActive(reward_state == TREASURE_OPERATE_TYPE.HAS_LINGQU)
	self.node_list.js_btn_get_reward_red:SetActive(is_remind)
	if is_remind then
		if self.js_final_reward_box_tween then
			self.js_final_reward_box_tween:Restart()
		else
			if self.js_final_reward_box_tween then
				self.js_final_reward_box_tween:Kill()
				self.js_final_reward_box_tween = nil
			end

			self.js_final_reward_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.js_box_icon.transform, self.js_final_reward_box_tween, 1)
		end
	elseif self.js_final_reward_box_tween then
		self.js_final_reward_box_tween:Pause()
		self.node_list.js_box_icon.transform.localRotation = Quaternion.identity
	end

	--self.node_list.final_reward_panel:SetActive(reward_state ~= TREASURE_OPERATE_TYPE.HAS_LINGQU)
	self:SetPinTuModelShow()
end

--有效时间倒计时
function WorldTreasureView:FlushTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("world_treasure_js_delay_time_count_down")
	local delay_time = WorldTreasureWGData.Instance:GetPunTuFinalRewardReceiveDelayTime()
	if delay_time > 0 then
		self.node_list["delay_time_text"]:SetActive(true)
		self.node_list["delay_time_text"].text.text = string.format(Language.WorldTreasure.DelayTimeDesc, TimeUtil.FormatSecondDHM9(delay_time))
		CountDownManager.Instance:AddCountDown("world_treasure_js_delay_time_count_down",
        BindTool.Bind1(self.UpdateCountDown, self),
        BindTool.Bind1(self.CountDownOnComplete, self),
        nil, delay_time, 1)
	else
		self.node_list["delay_time_text"]:SetActive(false)
	end
end

function WorldTreasureView:UpdateCountDown(elapse_time, total_time)
	self.node_list["delay_time_text"].text.text = string.format(Language.WorldTreasure.DelayTimeDesc, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
end

function WorldTreasureView:CountDownOnComplete()
	self.node_list["delay_time_text"]:SetActive(false)
    CountDownManager.Instance:RemoveCountDown("world_treasure_js_delay_time_count_down")
	self:Flush()
end

function WorldTreasureView:SetPinTuModelShow()
	local model_cfg = WorldTreasureWGData.Instance:GetPinTuModelCfg()
	local display_data = {}
	display_data.item_id = model_cfg.model_show_itemid
	display_data.should_ani = true
	display_data.render_type = model_cfg.model_show_type - 1
	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.model_rt_type = ModelRTSCaleType.L

	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local position_tab = string.split(model_cfg.display_pos, "|")
		local rotation_tab = string.split(model_cfg.rotation, "|")
		local vector_pos = Vector3(position_tab[1], position_tab[2], position_tab[3])
		local vector_rot = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		display_data.position = vector_pos or Vector3(0, 0, 0)
		display_data.rotation = vector_rot or Vector3(0, 0, 0)
		local scale = model_cfg.display_scale or 1
		display_data.scale = Vector3(scale, scale, scale)
	end
	self.model_display:SetData(display_data)
end

function WorldTreasureView:OnClickOpenItemTips()
	local reward_cfg = WorldTreasureWGData.Instance:GetPinTuRewardCfg()
	local data_list =
	{
		view_type = RewardShowViewType.Normal,
		reward_item_list = reward_cfg.reward_item
	}
	RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end

function WorldTreasureView:OnClickJsGetRewardBtn()
	local reward_state = WorldTreasureWGData.Instance:GetPunTuFinalRewardState()
	if reward_state == TREASURE_OPERATE_TYPE.NOT_OVER then
		self:OnClickOpenItemTips()
		return
	elseif reward_state == TREASURE_OPERATE_TYPE.HAS_LINGQU then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WorldTreasure.PinTuDesc1)
		return
	end

	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WOYAOSHENQI_GRADE_REWARD)
end

-- function WorldTreasureView:OnClickUpGradeLevelBtn()
-- 	if self.timer_3 then
-- 		return
-- 	end
-- 	--self.node_list.no_get_active:SetActive(false)
-- 	--暂时屏蔽特效
-- 	--self.node_list.node_effect_levelup:SetActive(true)
-- 	--self.node_list.image_effect_mask:SetActive(true)
-- 	self.timer_3 = GlobalTimerQuest:AddTimesTimer(function()
-- 		if self:IsOpen() then
-- 			--self.node_list.node_effect_levelup:SetActive(false)
-- 			--self.node_list.image_effect_mask:SetActive(false)
-- 			WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WOYAOSHENQI_GRADE_REWARD)
-- 		end
-- 	end, 3, 1)
-- end
------------------------------------------------------
-- 拼图TaskItem
------------------------------------------------------
WorldTreasurePinTuTaskItem = WorldTreasurePinTuTaskItem or BaseClass(BaseRender)

function WorldTreasurePinTuTaskItem:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.reward_cell = ItemCell.New(self.node_list.reward_cell)
	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind1(self.OnClickReceive, self))
	XUI.AddClickEventListener(self.node_list["btn_goto"], BindTool.Bind1(self.OnClickGoToBtn, self))
end

function WorldTreasurePinTuTaskItem:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function WorldTreasurePinTuTaskItem:OnFlush()
	if not self.data then
		return
	end

	--self.node_list.txt_task_title.text.text = self.data.cfg.task_name
	local is_enough = self.data.task_data.param >= self.data.cfg.task_param1
	local color = is_enough and "#99FFBB" or "#bb3030"
	local cur_str = self.data.task_data.param
	self.node_list.task_condition.text.text = string.format(self.data.cfg.task_desc, cur_str)
	local progress_str = ""
	if self.data.cfg.task_condition_id == 26 then	--特殊任务：查看界面的处理
		local cur_progress = is_enough and 1 or 0
		progress_str = string.format(Language.WorldTreasure.PinTuBili, color, cur_progress, 1)
	else
		progress_str = string.format(Language.WorldTreasure.PinTuBili, color, cur_str, self.data.cfg.task_param1)
	end
	self.node_list.task_condition_progress.text.text = progress_str

	self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(self.data.task_data.gift_state == 2)
            item_cell:SetRedPointEff(self.data.task_data.gift_state == 1)
        end
    end)
	self.reward_list:SetDataList(self.data.reward_list)

	self.reward_cell:SetShowCualityBg(false)
    self.reward_cell:SetCellBgEnabled(false)
	self.reward_cell:SetData(self.data.special_reward)
	self.reward_cell:SetBindIconVisible(false)
	self.reward_cell:SetEffectRootEnable(false)
	self.node_list.can_receive_effect:SetActive(self.data.task_data.gift_state == 1)

	self.node_list.have_receive_flag:SetActive(self.data.task_data.gift_state == 2)
	self.node_list.get_flag:SetActive(self.data.task_data.gift_state == 2) -- 0: 不可领取 1: 可领取 2: 已领取
	self.node_list.btn_goto:SetActive(self.data.task_data.gift_state == 0)
	self.node_list.btn_receive:SetActive(self.data.task_data.gift_state == 1)

	local bundle, asset = ResPath.GetWorldTreasureRawImages("xtyg_yq_1")
	self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.bg.raw_image:SetNativeSize()
	end)
end

function WorldTreasurePinTuTaskItem:OnClickReceive()
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_WOYAOSHENQI_TASK_REWARD, self.data.grade,
					self.data.ID)
end

function WorldTreasurePinTuTaskItem:OnClickGoToBtn()
	if self.data.cfg.activity_id ~= "" then
		local is_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.cfg.activity_id)
		if is_open then
			FunOpen.Instance:OpenViewNameByCfg(self.data.cfg.panel)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NotOpenAct)
		end
	else
		if self.data.cfg.panel and self.data.cfg.panel ~= "" then
			local is_open = FunOpen.Instance:GetFunIsOpened(self.data.cfg.panel)
			if is_open then
				FunOpen.Instance:OpenViewNameByCfg(self.data.cfg.panel)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.FunOpenTip)
			end
		end
	end
end
