
XianLingGuZhenOHDJRewardView = XianLingGuZhenOHDJRewardView or BaseClass(SafeBaseView)

function XianLingGuZhenOHDJRewardView:__init()
	self.view_layer = UiLayer.Pop
	
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true,true)
    self.full_screen = true
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_ohdj_reward_view")
end

function XianLingGuZhenOHDJRewardView:ReleaseCallBack()
	if self.model_display_list then
		self.model_display_list:DeleteMe()
		self.model_display_list = nil
	end
end

function XianLingGuZhenOHDJRewardView:LoadCallBack()
    if not self.model_display_list then
        self.model_display_list = OperationActRender.New(self.node_list["model_display"])
        self.model_display_list:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

	self.node_list["confirm_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGo, self))
	self.node_list["notice_btn"].button:AddClickListener(BindTool.Bind(self.OnClicNotNotice, self))
end

function XianLingGuZhenOHDJRewardView:OnClicNotNotice()
	local cur_state = XianLingGuZhenWGData.Instance:GetNoOHDJTipFlag()
	XianLingGuZhenWGData.Instance:SetNoOHDJTipFlag(not cur_state)
	self:FlushNoLonger()
end

function XianLingGuZhenOHDJRewardView:OnClickGo()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN) then
		ViewManager.Instance:Open(GuideModuleName.XianLingGuZhen)
		self:Close()
	end
end

function XianLingGuZhenOHDJRewardView:OnFlush()
    local open_day_cfg = XianLingGuZhenWGData.Instance:GetCurOpenDayCfg()

    self:FlushModel(open_day_cfg)
    local item_id = ((open_day_cfg or {}).big_reward or {}).item_id or 0

    if item_id > 0 then
        local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
        local power_num = ItemShowWGData.CalculateCapability(item_id, false)
        self.node_list["cap_value"].text.text = power_num
        local item_name = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
		self.node_list.title.text.text = item_name
        local role_name = RoleWGData.Instance:GetAttr("name")
        self.node_list["desc"].text.text = string.format(Language.XianLingGuZhen.GetBigRewardDesc, role_name, item_name)
    end
end

function XianLingGuZhenOHDJRewardView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg)then
		return
	end

	-- 形象展示
	local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = model_cfg.model_bundle_name
	display_data.asset_name = model_cfg.model_asset_name
	display_data.render_type = model_cfg.model_show_type - 1
	display_data.need_wp_tween = true
	display_data.hide_model_block = false
	display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = model_cfg.model_show_itemid})
    end
	local scale = tonumber(model_cfg.model_scale) or 1
	local pos_x, pos_y, pos_z = 0, 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.model_display.rect, pos_x, pos_y, pos_z)

	if model_cfg.model_pos and model_cfg.model_pos ~= "" then
		local pos_list = string.split(model_cfg.model_pos, "|")
		local posx = tonumber(pos_list[1]) or 0
		local posy = tonumber(pos_list[2]) or 0
		local posz = tonumber(pos_list[3]) or 0
		display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rot_list = string.split(model_cfg.rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0
		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_cfg.model_scale and model_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_cfg.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L
	-- if model_cfg.rotation and model_cfg.rotation ~= "" then
	-- 	local rotation_tab = string.split(model_cfg.rotation,"|")
	-- 	self.node_list.model_display.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	-- end
	-- self.node_list.model_display.transform:SetLocalScale(scale, scale, scale)

	self.model_display_list:SetData(display_data)
end

function XianLingGuZhenOHDJRewardView:FlushNoLonger()
	local no_longer = XianLingGuZhenWGData.Instance:GetNoOHDJTipFlag()
	self.node_list["no_tip_flag"]:SetActive(no_longer)
end