ChaoticPurchaseWGData = ChaoticPurchaseWGData or BaseClass()
ChaoticPurchaseWGData.Max_Page_Count = 4 -- page数量

function ChaoticPurchaseWGData:__init()
	if ChaoticPurchaseWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[ChaoticPurchaseWGData] attempt to create singleton twice!")
		return
	end

	ChaoticPurchaseWGData.Instance = self

	self:InitCfg()

	self.buy_times_list = {}
	self.record_list = {}
end

function ChaoticPurchaseWGData:__delete()
	ChaoticPurchaseWGData.Instance = nil
	self.buy_times_list = nil
	self.record_list = nil
end

function ChaoticPurchaseWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("chaotic_gift_auto")
	self.rmb_buy_grade_cfg = ListToMapList(cfg.rmb_buy, "grade")
	self.rmb_buy_seq_cfg = ListToMap(cfg.rmb_buy, "seq")
end

--全部数据
function ChaoticPurchaseWGData:SetAllInfo(protocol)
	self.buy_times_list = protocol.buy_times_list
end

--单个数据变化
function ChaoticPurchaseWGData:SetSingleInfo(protocol)
  	local data = protocol.change_data
  	for i, v in pairs(self.buy_times_list) do
  		if v.seq == data.seq then
  			self.buy_times_list[i] = data
  			return
  		end
  	end
end

function ChaoticPurchaseWGData:GetShowBuyList()
  	local show_data_list = {}
  	if IsEmptyTable(self.record_list) then
	  	for i,v in ipairs(self.rmb_buy_grade_cfg) do
	  		show_data_list = v
	  		for k, t in ipairs(v) do
	  			if not self:GetBuyStateBySeq(t.seq) then
	  				return show_data_list
	  			end
	  		end
	  	end

  		return show_data_list
  	else
  		return self.record_list
  	end
end

--策划需求  当前单位买完不立即刷新新的单位数据,关闭界面后才重新获取,所以记录下面板目前拿的配置表数据
function ChaoticPurchaseWGData:SetRecordShowList(data_list)
	self.record_list = data_list
end

function ChaoticPurchaseWGData:GetBuyCountBySeq(seq)
	return self.buy_times_list[seq] and self.buy_times_list[seq].buy_times or 0
end

function ChaoticPurchaseWGData:GetBuyStateBySeq(seq) -- 获取索引物品是否买完
	local seq_all_buy = false
	if self.rmb_buy_seq_cfg[seq] and self.buy_times_list[seq] then
		local cfg_buy_times = self.rmb_buy_seq_cfg[seq].buy_times
		local pro_buy_times = self.buy_times_list[seq].buy_times
		if cfg_buy_times <= pro_buy_times then
			seq_all_buy = true
		end
	end

	return seq_all_buy
end

function ChaoticPurchaseWGData:GetIsAllBuy()  --获取所有的物品都买完了
	local all_buy = true
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay() -- 新增需求（增加天数判断）
	for k, v in pairs(self.rmb_buy_seq_cfg) do
		if not self:GetBuyStateBySeq(v.seq) and (server_day >= v.day) then
			all_buy = false
			break
		end
	end

	return all_buy
end

