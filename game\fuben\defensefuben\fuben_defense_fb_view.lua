--塔防副本下侧
DefenseFbView = DefenseFbView or BaseClass(SafeBaseView)

function DefenseFbView:__init()
	self:AddViewResource(0, "uis/view/defense_fb_ui_prefab", "layout_tower_end_time")
	self:AddViewResource(0, "uis/view/team_exp_fb_prefab", "layout_exp_xiaolv")
	
	self.active_close = false
end

function DefenseFbView:__delete()
end

function DefenseFbView:LoadCallBack()
	self.end_time_bar = self.node_list.end_time_bar
	self.node_list.layout_exp_xiaolv_root.rect.anchoredPosition3D = Vector3(0, 255, 0)
	local function callback(show_node)
		ResMgr:LoadGameobjSync("uis/view/defense_fb_ui_prefab", "layout_defense_data_show", 
			function (obj)
					obj.transform:SetParent(MainuiWGCtrl.Instance:GetTaskOtherContent().transform,false)
					obj = U3DObject(obj)
					self.data_node = FuBenTFTaskDataList.New(obj)
					for i = 0, 3 do
						self.data_node.node_list["img_tower_"..i].button:AddClickListener(BindTool.Bind(self.OnClickDefense, self,i))
						self.data_node.node_list["tower_desc_"..i].text.text = Language.DefenseFb.TowerDesc[i]
					end
					-- self.data_node.node_list.defense_root_node.rect
					MainuiWGCtrl.Instance:SetTaskPanel(false,152,-179)
					local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
					self.data_node.node_list["lbl_defense_douhun"].text.text = defense_data.douhun

					self.end_time_bar.text.text =defense_data.cur_wave + 1
					self.node_list.img_defense_fight_start:SetActive(false)
					if defense_data.cur_wave < 0 then
						FuBenWGCtrl.Instance:ShowBuildTowerTips(true)
					else
						FuBenWGCtrl.Instance:ShowBuildTowerTips(false)
						self.end_time_bar:SetActive(false)
					end
					self:Flush()
				end)
	end
	MainuiWGCtrl.Instance:GetTaskMaskRootNode(callback)
	-- for i = 0, 3 do
	-- 	self.data_node.node_list["img_tower_"..i].button:AddClickListener(BindTool.Bind(self.OnClickDefense, self,i))
	-- end
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	-- self.node_list["lbl_defense_douhun"].text.text = defense_data.douhun

	-- self.node_list["layout_defense_btn_start"].text.text = Language.DefenseFb.DefenseBossTip
	self.notify_reason = 0
	--local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	self.is_first_handle = false

	self.node_list.layout_exp_xiaolv_root:SetActive(false)
	XUI.AddClickEventListener(self.node_list.additon_btn, BindTool.Bind(self.OnClickAdditonBtn,self))

	if not self.times_timequest then
		self.times_timequest = GlobalTimerQuest:AddTimesTimer(function()
			--local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
			local show_exp = 0
			if self.pre_exp_eta ~= nil then
				show_exp = defense_data.exp - self.pre_exp_eta
			else
				show_exp = defense_data.exp
			end
			self.node_list.layout_exp_xiaolv_root:SetActive(show_exp > 0)
			self.node_list["jingyanxiaolv"].text.text = CommonDataManager.ConverExp(math.ceil(show_exp))
			self.pre_exp_eta = defense_data.exp
		end, 60, 15)
	end
end

function DefenseFbView:OnClickAdditonBtn()
	ViewManager.Instance:Open(GuideModuleName.ExpAdditionView)
end

function DefenseFbView:OnClickDefense(i)
	FuBenWGCtrl.Instance:SetTowerIndex(i)
end

function DefenseFbView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("defense_fb_time_tip") then
		CountDownManager.Instance:RemoveCountDown("defense_fb_time_tip")
	end

	if self.data_node then
		local obj = self.data_node.node_list.defense_root_node.gameObject
		self.data_node:DeleteMe()
		ResMgr:Destroy(obj)
		self.data_node = nil
	end

	self.boss_chuxian_tip = nil
	-- self.layout_boss_tip = nil
	self.notify_reason = 0
	self.end_time_bar = nil
	self.is_first_handle = false
	self.pre_exp_eta = nil
	if self.times_timequest ~= nil then
		GlobalTimerQuest:CancelQuest(self.times_timequest)
		self.times_timequest = nil
	end
end

function DefenseFbView:CloseCallBack()
	MainuiWGCtrl.Instance:ResetTaskPanel()
	if self.data_node then
		local obj = self.data_node.node_list.defense_root_node.gameObject
		self.data_node:DeleteMe()
		ResMgr:Destroy(obj)
		self.data_node = nil
	end
	self.is_first_handle = false
end

function DefenseFbView:ShowIndexCallBack(index)
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	if defense_data.cur_wave == -1 then
		--self.node_list["img_defense_fb_time_1"]:SetActive(true)
		self:DefenseFbTimePrompt(DefenseFbWGData.NOTIFY_REASON.NOTIFY_PREPARE_TIME)
	else
		self.node_list["img_defense_fb_time_1"]:SetActive(false)
	end
end

function DefenseFbView:OnFlush(param_list, index)
	if self.data_node == nil then return end
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	-- print_error(defense_data)
	if self.is_first_handle == false then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(defense_data.finish_timestamp)
		self.is_first_handle = true
	end
	local cur_wave = math.max(defense_data.cur_wave, 0)
	local d_count = FuBenWGCtrl.Instance.defense_fb_data:GetDedenseTowerWaveCount()
	self.data_node.node_list["lbl_defense_cur_wave"].text.text = tostring(cur_wave + 1).."/"..d_count
	self.data_node.node_list["lbl_defense_douhun"].text.text = defense_data.douhun
	self.data_node.node_list["lbl_defense_exp"].text.text = CommonDataManager.ConverExp(defense_data.exp)
	self.data_node.node_list["lbl_escape_monster_count"].text.text = defense_data.escape_monster_count


	if defense_data.cur_wave ~= -1 then
		self:DefenseFbTimePrompt(defense_data.notify_reason)
	end
end

function DefenseFbView:DefenseFbTimePrompt(notify_reason)
	self.node_list["img_defense_fb_time_1"]:SetActive(false)
	self.node_list["img_defense_fb_time_2"]:SetActive(false)
	self.end_time_bar:SetActive(false)
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	if notify_reason == nil and self.notify_reason ~= notify_reason then return end
	self.notify_reason = notify_reason

	if self.notify_reason == DefenseFbWGData.NOTIFY_REASON.NOTIFY_REASON_DEFAULT then			--正常
		return
	end

	if CountDownManager.Instance:HasCountDown("defense_fb_time_tip") then
		CountDownManager.Instance:RemoveCountDown("defense_fb_time_tip")
	end


	local fun = function ()
		self.end_time_bar:SetActive(false)
		MainuiWGCtrl.Instance:SetShowTimeTextState( true )
		MainuiWGCtrl.Instance:SetFbPrepareTimeTextMark(nil)
	end

	if self.notify_reason == DefenseFbWGData.NOTIFY_REASON.NOTIFY_PREPARE_TIME then		--准备开始倒计时
		MainuiWGCtrl.Instance:SetShowTimeTextState( false )
		MainuiWGCtrl.Instance:SetFbPrepareTimeTextMark(true)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local interval_time = defense_data.next_wave_timestamp - server_time
		if interval_time > 0 then
			CountDownManager.Instance:AddCountDown("defense_fb_time_tip", function(elapse_time, total_time)
				if total_time - elapse_time >= 1 then
					self.end_time_bar.text.text = math.floor(total_time - elapse_time)
					self:DoTweenScaleContent()
				elseif total_time - elapse_time < 1 and  total_time - elapse_time >= 0 then
					self:DoTweenScaleContentFightStart()
				end
			end, fun, defense_data.next_wave_timestamp, nil, 1)
		end
		self.end_time_bar.text.text = math.floor(interval_time)
		self:DoTweenScaleContent()
		self.end_time_bar:SetActive(false)
	elseif self.notify_reason == DefenseFbWGData.NOTIFY_REASON.NOTIFY_MONSTER_WAVE then
		self.end_time_bar:SetActive(false)
		FuBenWGCtrl.Instance:ShowBuildTowerTips(false)
		MainuiWGCtrl.Instance:SetShowTimeTextState( true )
		MainuiWGCtrl.Instance:SetFbPrepareTimeTextMark(nil)
		self.node_list.end_time_bar_1.text.text =defense_data.cur_wave + 1
		self.node_list["img_defense_fb_time_2"]:SetActive(true)
		self.close_num_wave = GlobalTimerQuest:AddDelayTimer(function ()
			self.node_list["img_defense_fb_time_2"]:SetActive(false)
			self.end_time_bar:SetActive(false)
			-- GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			if self.close_num_wave then
				GlobalTimerQuest:CancelQuest(self.close_num_wave)
				self.close_num_wave = nil
			end
		end,2)
	end
end

function DefenseFbView:DoTweenScaleContent()
	local scale = Vector3(1,1,1)
	self.node_list.end_time_bar.rect.localScale = Vector3(3,3,1)
	self.node_list.end_time_bar.rect:DOScale(scale,0.3)
end
function DefenseFbView:DoTweenScaleContentFightStart()
	self.end_time_bar:SetActive(false)
	FuBenWGCtrl.Instance:ShowBuildTowerTips(false)
	self.node_list.img_defense_fight_start:SetActive(true)
	local scale = Vector3(1,1,1)
	self.node_list.img_defense_fight_start.rect.localScale = Vector3(3,3,1)
	--self.star_high_img_list[index].obj.rect:DOAnchorPos(end_pos,0.5)
	self.node_list.img_defense_fight_start.rect:DOScale(scale,0.3):OnComplete(function()
		GlobalTimerQuest:AddDelayTimer(function()
						self.node_list.img_defense_fight_start:SetActive(false)
					end,0.5)
	end)
end

FuBenTFTaskDataList = FuBenTFTaskDataList or BaseClass(BaseRender)
function FuBenTFTaskDataList:__init()
end

function FuBenTFTaskDataList:__delete()
end

function FuBenTFTaskDataList:LoadCallBack()
end