CrossAirWarSceneView = CrossAirWarSceneView or BaseClass(SafeBaseView)
function CrossAirWarSceneView:__init()
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_scene_view")
end

-- 打开后调用（注意此打开是在加载之前调用）
function CrossAirWarSceneView:OpenCallBack()
	if self.is_load_complete then
		if self.task_root then
			self.task_root:SetActive(true)
		end
	end
end

function CrossAirWarSceneView:CloseCallBack()
	if self.task_root then
		self.task_root:SetActive(false)
		self.node_list.auction_last_time:CustomSetActive(false)
		self.node_list.out_fb_last_time:CustomSetActive(false)
	end
end


function CrossAirWarSceneView:LoadCallBack()
	local init_callback = function ()
		self:InitView()
	end

	local mainuictrl = MainuiWGCtrl.Instance
	mainuictrl:AddInitCallBack(nil,init_callback)
end

function CrossAirWarSceneView:InitView()
	self.task_root = self.node_list.task_root
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	self.task_root.transform:SetParent(parent.transform)
	self.task_root.transform:SetLocalScale(1, 1, 1)
	self.task_root.transform.anchoredPosition = Vector2(0, 0)

	XUI.AddClickEventListener(self.node_list.pos_message,BindTool.Bind(self.OnClickGoToPos, self))
	XUI.AddClickEventListener(self.node_list.reward_message,BindTool.Bind(self.OnClickOpenReward, self))
	self.is_load_complete = true
end


function CrossAirWarSceneView:ReleaseCallBack()
	if self.task_root then
		local obj = self.task_root.gameObject
		ResMgr:Destroy(obj)
		self.task_root = nil
	end

	self.is_load_complete = nil
	self.is_wait_status_tips = nil
	self:RemoveAirWarCountDown()
	self:RemoveStartShowTipsDelayTimer()
	self:RemoveStartAuctionCountDown()
	self:RemoveStartBoxTipsCountDown()
end

-- 设置等待状态
function CrossAirWarSceneView:SetWaitGameStartStatus()
	self.is_wait_status_tips = true
end

function CrossAirWarSceneView:OnFlush(param_t)
	self:FlushTaskMessage()

	for k,v in pairs(param_t) do
		if k == "flush_aution_status" then	-- 等待boss
			self:FlushAutionStatus()
		elseif k == "flush_box_time_message" then
			self:FlushBoxShowTime()
		elseif k == "flush_wait_start_message" then
			self:FlushStatusTips(true)
		elseif k == "flush_end_fb" then
			self:FlushStatusTips(true)
		elseif k == "flush_status_tips" then
			self:FlushStatusTips(false)
		end
	end

	if self.is_wait_status_tips then
		self.is_wait_status_tips = nil
		self:FlushStatusTips(true)
	end
end

-- 刷新任务信息面板
function CrossAirWarSceneView:FlushTaskMessage()
	local moster_seq, is_wait = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(moster_seq)
	local moster_pos = CrossAirWarWGData.Instance:GetMonsterPosBySeq(moster_seq)
	local cur_status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	self:RemoveAirWarCountDown()

	if not moster_cfg then
		return
	end

	-- 设置阶段
	local stage = moster_cfg.show_stage
	local phase_show_num = NumberToChinaNumber(stage)
	local stage_str = string.format(Language.CrossAirWar.PhaseStr1, phase_show_num)
	self.node_list.stage_txt.text.text = string.format("%s %s", stage_str, moster_cfg.stage_name)

	-- 设置目标
	local aim_num = moster_cfg.monster_num or 0
	local cur_num = CrossAirWarWGData.Instance:GetWarCurStageMosterNum()
	local finish_num = aim_num - cur_num

	if cur_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_START then
		finish_num = 0
	end

	local finish_num_str = ToColorStr(finish_num, COLOR3B.GREEN)
	local aim_index = (not is_wait) and finish_num or 0
	local progress_str = string.format("(%s/%d)", finish_num_str, aim_num)
	if is_wait then
		progress_str = ""
	end

	self.node_list.aim_message_desc.text.text = string.format(Language.CrossAirWar.PhaseStr2, ToColorStr(moster_cfg.target_desc, COLOR3B.GLOD_TITLE), progress_str)
	self.node_list.desc_message_desc.text.text = moster_cfg.detail_desc

	-- 设置目标位置
	local aim_index = (not is_wait) and finish_num + 1 or 1
	local pos = moster_pos[aim_index] or Vector2.zero
	local all_score = CrossAirWarWGData.Instance:GetPlayerWarScore()
	local pos_str = string.format("（%d，%d）", pos.x, pos.y)
	self.node_list.pos_message_desc.text.text = string.format(Language.CrossAirWar.PhaseStr3, ToColorStr(pos_str, COLOR3B.GLOD_TITLE))
	self.node_list.score_message_desc.text.text = string.format(Language.CrossAirWar.PhaseStr4, ToColorStr(all_score, COLOR3B.GLOD_TITLE))
	self.node_list.reward_message_desc.text.text = Language.CrossAirWar.PhaseStr5
end

-- 刷新任务信息面板
function CrossAirWarSceneView:FlushAutionStatus()
	local cur_status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	local status_time = CrossAirWarWGData.Instance:GetAirWarSceneNextStatusTime()

	if cur_status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_MONSTER then			--- 等待boss
		self:FlushAirWarItemCDTimer(status_time)
	end
end

-- 刷新结束时间
function CrossAirWarSceneView:FlushAirWarItemCDTimer(status_time)
	self:RemoveAirWarCountDown()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local remain_time = status_time - server_time

	if remain_time > 0 then
		self:FlushAirWarTitleMessageTime(TimeUtil.FormatSecondDHM9(remain_time))
		CountDownManager.Instance:AddCountDown("air_war_air_war_price_cd", 
			BindTool.Bind1(self.UpdateAirWarBidding, self),
			BindTool.Bind1(self.OnAirWarBiddingComplete, self), 
			nil, remain_time, 1)
	end
end

function CrossAirWarSceneView:UpdateAirWarBidding(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM9(time)
	self:FlushAirWarTitleMessageTime(time_str)
end

function CrossAirWarSceneView:FlushAirWarTitleMessageTime(time_str)
	local moster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(moster_seq)

	if not moster_cfg then
		return
	end

	-- 设置目标
	time_str = string.format("（%s）", time_str)
	self.node_list.aim_message_desc.text.text = string.format(Language.CrossAirWar.PhaseStr2, ToColorStr(moster_cfg.target_desc, COLOR3B.GLOD_TITLE), time_str)
end

function CrossAirWarSceneView:OnAirWarBiddingComplete()
end

function CrossAirWarSceneView:RemoveAirWarCountDown()
	if CountDownManager.Instance:HasCountDown("air_war_air_war_price_cd") then
		CountDownManager.Instance:RemoveCountDown("air_war_air_war_price_cd")
	end
end

----------------------------------------------------------------------
-- 获取当前的状态时间
function CrossAirWarSceneView:GetNowNextStatusTime()
	local status_time = CrossAirWarWGData.Instance:GetAirWarSceneNextStatusTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local remain_time = status_time - server_time
	return remain_time
end 

--- 刷新宝箱倒计时
function CrossAirWarSceneView:FlushBoxShowTime()
	local remain_time = self:GetNowNextStatusTime()
	self.node_list.auction_last_desc.text.text = string.format(Language.CrossAirWar.AirWarBoxTimeTip, TimeUtil.FormatSecondDHM9(remain_time))
	self.node_list.auction_last_time:CustomSetActive(true)
	self:RemoveStartAuctionCountDown()
	CountDownManager.Instance:AddCountDown("air_war_air_wait_auction_cd", 
	BindTool.Bind1(self.UpdateStartAuctionTime, self),
	BindTool.Bind1(self.OnStartAuctionTimelete, self), 
	nil, remain_time, 1)
end

function CrossAirWarSceneView:UpdateStartAuctionTime(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	self.node_list.auction_last_desc.text.text = string.format(Language.CrossAirWar.AirWarBoxTimeTip, TimeUtil.FormatSecondDHM9(time))

	if self.status_desc_str ~= nil then
		self.node_list.out_fb_time_txt.text.text = string.format(self.status_desc_str, ToColorStr(TimeUtil.FormatSecondDHM2(time), COLOR3B.GREEN))
	end
end

function CrossAirWarSceneView:OnStartAuctionTimelete()
	self.status_desc_str = nil
	self.node_list.auction_last_time:CustomSetActive(false)
	self.node_list.out_fb_last_time:CustomSetActive(false)
end

function CrossAirWarSceneView:RemoveStartAuctionCountDown()
	if CountDownManager.Instance:HasCountDown("air_war_air_wait_auction_cd") then
		CountDownManager.Instance:RemoveCountDown("air_war_air_wait_auction_cd")
	end
end
-------------------------------------------------------------------------------------------------------
-- 刷新副本状态提示
function CrossAirWarSceneView:FlushStatusTips(is_need_time)
	self:RemoveStartBoxTipsCountDown()
	self:RemoveStartShowTipsDelayTimer()

	if is_need_time then
		local remain_time = self:GetNowNextStatusTime()
		local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
		self.status_desc_str = status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_START and Language.CrossAirWar.AirWarBoxTimeTip1 or Language.CrossAirWar.AirWarExitFbTimeTip
		self.node_list.out_fb_last_time:CustomSetActive(true)
		self.node_list.out_fb_time_txt.text.text = string.format(self.status_desc_str, ToColorStr(TimeUtil.FormatSecondDHM2(remain_time), COLOR3B.GREEN))
	
		CountDownManager.Instance:AddCountDown("air_war_air_wait_box_tips_cd", 
		BindTool.Bind1(self.UpdateStartAuctionTime, self),
		BindTool.Bind1(self.OnStartAuctionTimelete, self), 
		nil, remain_time, 1)
	else
		local status_tips = CrossAirWarWGData.Instance:GetNowStageTips() 
		if status_tips ~= "" then
			self.node_list.out_fb_last_time:CustomSetActive(true)
			self.node_list.out_fb_time_txt.text.text = status_tips

			self.show_tips_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
				self.node_list.out_fb_last_time:CustomSetActive(false)
			end, 4)
		end
	end
end

function CrossAirWarSceneView:RemoveStartBoxTipsCountDown()
	if CountDownManager.Instance:HasCountDown("air_war_air_wait_box_tips_cd") then
		CountDownManager.Instance:RemoveCountDown("air_war_air_wait_box_tips_cd")
	end
end

--移除回调
function CrossAirWarSceneView:RemoveStartShowTipsDelayTimer()
    if self.show_tips_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_tips_delay_timer)
        self.show_tips_delay_timer = nil
    end
end

----------------------------------------------------------------------
--------------------------------------------------------
-- 寻路到目标点
function CrossAirWarSceneView:OnClickGoToPos()
	local cur_num = 1
	local moster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	local moster_pos = CrossAirWarWGData.Instance:GetMonsterPosBySeq(moster_seq)

	if not moster_pos then
		return
	end

	local pos = moster_pos[cur_num] or Vector2.zero
	local scene_id = Scene.Instance:GetSceneId()
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pos.x, pos.y, 5)
end

-- 打开积分
function CrossAirWarSceneView:OnClickOpenReward()
	CrossAirWarWGCtrl.Instance:OpenRewardPreview(true)
end