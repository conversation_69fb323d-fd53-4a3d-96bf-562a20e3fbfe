OATipsGetRewardView = OATipsGetRewardView or BaseClass(SafeBaseView)

local ani_ten_flag_t = {}
local ANI_SPEED = 0.15
local MAX_COUNT = 50
local MAX_LINE_COUNT = 5

function OATipsGetRewardView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/operation_fish_prefab", "layout_gift_result")
end

function OATipsGetRewardView:__delete()

end

function OATipsGetRewardView:ReleaseCallBack()
    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end

    if self.cell_list then
        for i,v in ipairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = {}
    end

    if nil ~= self.baodi_list then
		self.baodi_list:DeleteMe()
		self.baodi_list = nil
    end
end

function OATipsGetRewardView:LoadCallBack(index, loaded_times)
    self.cell_list = {}
    --local bundle, asset = ResPath.UiseRes("effect_getreward")
    --AudioManager.PlayAndForget(bundle, asset)

    XUI.AddClickEventListener(self.node_list.list_mask,BindTool.Bind(self.ImmPlayToBigReward,self))
    XUI.AddClickEventListener(self.node_list.btn_cancel,BindTool.Bind(self.Close,self))
    XUI.AddClickEventListener(self.node_list.fish_btn,BindTool.Bind(self.DrawAgain,self))

    self.baodi_list = AsyncListView.New(OABaodiRewardRender, self.node_list.ph_baodi_list)
end

function OATipsGetRewardView:ShowIndexCallBack()    
    self:Flush()
end

--初始化格子
local MAX_DELTA = 580
local SINGLE_DELTA = 105
local LINE_COUNT = 10
local ONE_LINE_DELTA = 200
function OATipsGetRewardView:FlushCell(id_list)
    if not id_list then 
        return
    end

    local zhenxi_list, reward_list = OAFishWGData.Instance:GetShowCalDrawRewardList(id_list)
    
    local length = math.ceil(#reward_list / 10)

    for i=1,MAX_COUNT do
        if reward_list[i] then
            if not self.cell_list[i] then
                self.cell_list[i] = OARewardSingleCell.New(self.node_list['single_cell_' .. i]) 
            end
            self.cell_list[i]:SetAlpha(false)
            self.cell_list[i]:SetVisible(true)
            -- 刷新格子
            self.cell_list[i]:SetData(reward_list[i])
        else
            if self.cell_list[i] then
                self.cell_list[i]:SetAlpha(false)
                self.cell_list[i]:SetVisible(false)
            end
        end
    end

    for i = 1, 5 do
        self.node_list["list_" .. i]:SetActive(length >= i)
    end

    for i = (length - 1) * LINE_COUNT + 1,length * LINE_COUNT do
        self.node_list['single_cell_' .. i]:SetActive(reward_list[i] ~= nil)
    end

    self.baodi_list:SetDataList({}) --数据清一下
    self.node_list.baodi_container:SetActive(not IsEmptyTable(zhenxi_list))
    if not IsEmptyTable(zhenxi_list) then
        self.baodi_list:SetDataList(zhenxi_list)
    end

    self.node_list.scroll_view.scroll_rect.verticalNormalizedPosition = 1
end

function OATipsGetRewardView:SetData(id_list)
    self.id_list = id_list
end

local Sort_Type = {
    [GameEnum.ITEM_BIGTYPE_EQUIPMENT] = 10,
    [GameEnum.ITEM_BIGTYPE_EXPENSE] = 9,
    [GameEnum.ITEM_BIGTYPE_GIF] = 8,
    [GameEnum.ITEM_BIGTYPE_OTHER] = 7,
    [GameEnum.ITEM_BIGTYPE_VIRTUAL] = 6,
}
function OATipsGetRewardView:SortItem(item_list)
    local item_cfg,item_type
    for i,v in ipairs(item_list) do
        item_cfg,item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
        v.color = item_cfg.color
        v.item_type = Sort_Type[item_type]
    end
    SortTools.SortDesc(item_list,"color","item_type")  
    return item_list
end

--刷新数据
function OATipsGetRewardView:OnFlush()
    self.node_list.list_mask:SetActive(true)
    local gift_info = self.id_list
    -- gift_info = self:SortItem(gift_info)

    if self.tween_time_quest then
        GlobalTimerQuest:CancelQuest(self.tween_time_quest)
        self.tween_time_quest = nil
    end

    self:FlushCell(gift_info)
    self:ChangeBlock(false)

    local no_tween = OAFishWGData.Instance:GetNoTweenFlag() == 1

    if no_tween then
        self.tween_index = 1
        self:ImmPlayToBigReward()
    else
        self.tween_index = 1
        self.tween_time_quest = GlobalTimerQuest:AddTimesTimer(function()
            if self.tween_index > MAX_COUNT then
                GlobalTimerQuest:CancelQuest(self.tween_time_quest)
                self.tween_time_quest = nil
                self:ChangeBlock(true)
                return
            end
            if self.cell_list[self.tween_index - 1] and not self.cell_list[self.tween_index - 1]:GetCanPlayNext() then
                return
            end

            if self.cell_list[self.tween_index] then
                self.cell_list[self.tween_index]:DoAnim()
                self.cell_list[self.tween_index]:SetAlpha(true)
            end
            self.tween_index = self.tween_index + 1

            if self.tween_index >= #gift_info then
                self.node_list.list_mask:SetActive(false)
                return
            end

        end, ANI_SPEED, #gift_info*3)
    end

    -- 刷新按钮
    self:FlushBtn()
end

-- 快速播放到下一个大奖
function OATipsGetRewardView:ImmPlayToBigReward()
    for i = self.tween_index, #self.cell_list do
        if self.id_list[i] then
            -- 如果是最后一个物品，需要隐藏遮罩
           
            -- if i == #self.id_list then
            --     self.node_list.list_mask:SetActive(false)
            --     self.tween_index = i
            -- end
            -- if self.id_list[i].reward_type ~= OAFishWGData.REWARD_TYPE.LOW then
            --     self.tween_index = i
            --     return
            -- end
            self.cell_list[i]:ImmShowItem()
        end
        self.tween_index = #self.id_list
        self.node_list.list_mask:SetActive(false)
    end
end

function OATipsGetRewardView:ChangeBlock(enable)
    for i=1,50 do
        self.node_list['single_cell_' .. i].canvas_group.blocksRaycasts = enable
    end
end

function OATipsGetRewardView:DrawAgain()
    if not self.draw_info then
        return
    end
    local btn_index = OAFishWGData.Instance:CacheOrGetFishDrawIndex()
    OAFishWGCtrl.Instance:OnClickBtn(btn_index)
    self:Close()
end

function OATipsGetRewardView:FlushBtn()
    local grade_cfg = OAFishWGData.Instance:CacheOrGetFishDrawCfg()
    local btn_index = OAFishWGData.Instance:CacheOrGetFishDrawIndex()
    if not grade_cfg or not btn_index then
        return
    end

    local btn_list = OAFishWGData.Instance:GetCurConsumeCfg(grade_cfg)
    local btn_cfg = btn_list and btn_list[btn_index] 
    if not btn_cfg then
        return
    end

    local need_count
    local send_num = btn_cfg.onekey_lotto_num
    local sp_guarantee_x,sp_guarantee_n,sp_enter_num = OAFishWGData.Instance:GetSpGuarantee()
    local reward_pool_id = grade_cfg.reward
    local sp_max_num = OAFishWGData.Instance:GetGuaranteeListCount(reward_pool_id)
    -- print_error('need_count',need_count, btn_cfg.onekey_lotto_num,sp_guarantee_x,sp_guarantee_n,(grade_cfg.sp_guarantee_x - sp_guarantee_x) ,send_num,
    --  sp_enter_num , (sp_max_num - 1)   )

    if grade_cfg.sp_guarantee_finish == 1 and sp_guarantee_n >= (grade_cfg.sp_guarantee_n - 1) and
        sp_enter_num == (sp_max_num - 1) and (grade_cfg.sp_guarantee_x - sp_guarantee_x) < send_num then
        
        need_count = grade_cfg.sp_guarantee_x - sp_guarantee_x
    end  
    if sp_guarantee_n >= grade_cfg.sp_guarantee_n then
        need_count = 0 
    end

    need_count = need_count or btn_cfg.onekey_lotto_num

    self.node_list.fish_btn_root:SetActive(need_count > 0)
    if need_count <= 0 then
        return
    end

    local is_enough, color
    local has_num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.consume_item)
    is_enough = has_num >= btn_cfg.consume_count or has_num >= need_count
    color = is_enough and "#72eba9" or "#ff5050"
    self.draw_info = {
        num = need_count,
        need_gold = is_enough and 0 or 1,
    }

    local has_num_str = ToColorStr(has_num, color)
    if need_count < send_num then
        local str = string.format(Language.OAFish.CI_2, need_count)                        
        self.node_list["fish_btn_txt"].text.text = str
        self.node_list["fish_btn_cost"].text.text = has_num_str .. "/" .. need_count
    else
        local str = string.format(Language.OAFish.CI_2, need_count)                        
        self.node_list["fish_btn_txt"].text.text = str
        self.node_list["fish_btn_cost"].text.text = has_num_str .. "/" .. btn_cfg.consume_count
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fish_btn_cost"].rect)

    local cfg = ItemWGData.Instance:GetItemConfig(grade_cfg.consume_item)
    local bundle, asset = ResPath.GetItem(cfg.icon_id)
    self.node_list["fish_cost_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["fish_cost_icon"].image:SetNativeSize()
    end)
    self.node_list["fish_cost_icon"].button:AddClickListener(BindTool.Bind(self.OnClickFishCostBtn, self, grade_cfg.consume_item))

    local show_discount = btn_cfg.discount_text ~= "" and need_count >= btn_cfg.onekey_lotto_num
    self.node_list.fish_btn_discount:SetActive(show_discount)
    if show_discount then
        self.node_list["fish_txt_discount"].text.text = btn_cfg.discount_text
    end

end

function OATipsGetRewardView:OnClickFishCostBtn(item_id)
    if not item_id then
        return
    end 
    TipWGCtrl.Instance:OpenItem({item_id = item_id, num = 1, is_bind = 1},ItemTip.FROM_NORMAL)
end

----------------------------------------------------------------------------------------------
OARewardSingleCell = OARewardSingleCell or BaseClass(BaseRender)
function OARewardSingleCell:LoadCallBack()
    self.base_cell = ItemCell.New(self.node_list["cell"])
    self.base_cell:SetItemTipFrom(ItemTip.FROM_GET_REWARD)
    self.graphic_raycast = self.node_list.cell:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster))
    self.node_list.cell.image.enabled = false
end

function OARewardSingleCell:ReleaseCallBack()
    if self.base_cell then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.graphic_raycast = nil
end

function OARewardSingleCell:SetAlpha(value)
    if self.graphic_raycast then
        self.graphic_raycast.enabled = value
    end
    self.view.canvas_group.alpha = value and 1 or 0
end

local scale1 = Vector3(1.4, 1.4, 1.4) --大小1
local scale2 = Vector3(0.9, 0.9, 0.9) --大小1
local effect_name = {

}
local start_effect_name = {
    [5] = "ui_jinjichenggong",
    [6] = "ui_jinjichenggong",
}

function OARewardSingleCell:GetCanPlayNext()
    return self.can_play_next or false
end


OARewardSingleCell.Time = 1
function OARewardSingleCell:DoAnim()
    self.can_play_next = false

    local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    if not cfg then
        return
    end
    local eff_level = cfg.color

    self.view.transform.localScale = scale1
    local scale_tween_2 = self.view.rect:DOScale(scale2,0.3)
    scale_tween_2:SetEase(DG.Tweening.Ease.InQuad)

    self.sequence = DG.Tweening.DOTween.Sequence()
        -- 不同类型的奖励，播放不同的特效
    if self.data.reward_type ~= OAFishWGData.REWARD_TYPE.LOW then
        self.sequence:AppendInterval(OARewardSingleCell.Time)
        local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower('UI_jinli_saoguang'))
        local asset = 'UI_jinli_saoguang'
        self.node_list.effect_attach.rect:SetAsLastSibling()
        self.node_list.effect_attach:SetActive(true)
        --self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end

    self.sequence:AppendCallback(function ()
        self.can_play_next = true
        self.node_list.effect_attach.rect:SetAsFirstSibling()
        self:PlayStartEffect()
    end)
    self.sequence:Append(scale_tween_2)
    self.sequence:AppendInterval(0.1)
    self.sequence:AppendCallback(function ()
        ani_ten_flag_t[#ani_ten_flag_t + 1] = true
        if not effect_name[eff_level] then
            self.node_list.effect_attach:SetActive(false)
            return
        end
        
        local bundle, asset = ResPath.GetEffectUi(effect_name[eff_level])

        self.node_list.effect_attach:SetActive(true)
        self.node_list.effect_attach:ChangeAsset(bundle,asset)
    end)

end

function OARewardSingleCell:ImmShowItem()
    self.can_play_next = true
    self:SetAlpha(true)
    self:FlushEffect()
end

function OARewardSingleCell:PlayStartEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    local eff_level = cfg.color
    
    if not start_effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end

    local bundle, asset = ResPath.GetEffectUi(start_effect_name[eff_level])

     self.node_list.effect_attach:SetActive(true)
     self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function OARewardSingleCell:FlushEffect()
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)
    local eff_level = cfg.color
    
    if not effect_name[eff_level] then
        self.node_list.effect_attach:SetActive(false)
        return
    end

    local bundle = string.format("effects2/prefab/ui/%s_prefab", string.lower(effect_name[eff_level]))
    local asset = effect_name[eff_level]
     if self.node_list.effect_attach and self.node_list.effect_attach.game_obj_attach then
         self.node_list.effect_attach.game_obj_attach.BundleName = nil
        self.node_list.effect_attach.game_obj_attach.AssetName = nil
     end
     self.node_list.effect_attach:SetActive(true)
     self.node_list.effect_attach:ChangeAsset(bundle,asset)
end

function OARewardSingleCell:OnFlush()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
    if not self.data then return end
    self.base_cell:SetData(self.data.reward_item)
    self.node_list.cell.image.enabled = self.data.is_zhenxi
end

----------------------------------------OABaodiRewardRender----------------------------------
OABaodiRewardRender = OABaodiRewardRender or BaseClass(BaseRender)

function OABaodiRewardRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function OABaodiRewardRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function OABaodiRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData(self.data.reward_item)
    self.node_list.desc_value.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.reward_item.item_id)
end