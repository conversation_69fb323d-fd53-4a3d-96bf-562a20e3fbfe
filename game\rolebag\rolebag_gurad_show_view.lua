GuradShowVeiw = GuradShowVeiw or BaseClass(SafeBaseView)

function GuradShowVeiw:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/gurad_invalid_time_prefab", "layout_gurad_show_view")
	self.view_layer = UiLayer.Pop
	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
end

function GuradShowVeiw:__delete()

end

function GuradShowVeiw:LoadCallBack()
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(760,500)
	self.node_list.title_view_name.text.text = Language.Gurad.GuradShowViewTitle

	self.list_cell = {}
	local list_view_delegate = self.node_list.list_view.list_simple_delegate
	list_view_delegate.NumberOfCellsDel  = BindTool.Bind(self.GetCellNum,self)
	list_view_delegate.CellRefreshDel = BindTool.Bind(self.CellRefresh,self)
	self.node_list.close_btn.button:AddClickListener(BindTool.Bind(self.OnCloseClick,self))
	self.gurad_data_change = BindTool.Bind1(self.OnGuradChangeCallBack, self)
	EquipWGData.Instance:NotifyXiaoGuiChangeCallBack(self.gurad_data_change)
	self.scene_loading_state_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnGuradChangeCallBack, self))
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	self:OnGuradChangeCallBack()
end

function GuradShowVeiw:ReleaseCallBack()
	if self.list_cell then
		for k,v in pairs(self.list_cell) do
			if v then
				v:DeleteMe()
			end
		end
		self.list_cell = {}
	end

	if self.gurad_data_change then
		EquipWGData.Instance:NotifyXiaoGuiChangeCallBack(self.gurad_data_change)
		self.gurad_data_change = nil
	end

	if self.scene_loading_state_quit then
		GlobalEventSystem:UnBind(self.scene_loading_state_quit)
		self.scene_loading_state_quit = nil
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function GuradShowVeiw:GetCellNum()
	local num = RoleBagWGData.Instance:GetGuradCfg()
	return #num
end

function GuradShowVeiw:CellRefresh(cell,data_index)
	data_index = data_index + 1
	local data = RoleBagWGData.Instance:GetGuradCfg()
	if not data or not next(data) then return end
	local list_cell = self.list_cell[cell]
	if nil == list_cell then
		list_cell = GuradListCell.New(cell.gameObject,self)
		self.list_cell[cell] = list_cell
	end
	self.list_cell[cell]:SetIndex(data_index)
	self.list_cell[cell]:SetData(data[data_index])
end

function GuradShowVeiw:OnFlush()

end

function GuradShowVeiw:OnGuradChangeCallBack()
	if self.node_list.list_view then
		self.node_list.list_view.scroller:RefreshActiveCellViews()
	end
end

-- 物品变化
function GuradShowVeiw:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_item_id == 0 or EquipmentWGData.GetXiaoGuiCfg(change_item_id) then
		self:OnGuradChangeCallBack()
	end
end

function GuradShowVeiw:OnCloseClick()
	self:Close()
end
-------------------------------------------------------------------------------------------

GuradListCell = GuradListCell or BaseClass(BaseRender)

function GuradListCell:__init(instance,parent)
	self.parent = parent
	self.alert_window = Alert.New(nil, nil, nil, nil, false)

end

function GuradListCell:LoadCallBack()
	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnGoClick,self))
	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function GuradListCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.parent = nil

	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function GuradListCell:OnFlush()
	if not self.data then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local str = Language.Gurad.FunctionType[self.data.impguard_type]
	local bundle , asset = ResPath.GetCommonOthers("gurad_dec_"..self.data.imp_type)

	self.item_cell:SetData({item_id = self.data.item_id})
	self.node_list.item_name.text.text = ToColorStr(str .. item_cfg.name,ITEM_TIP_COLOR[item_cfg.color])
	self.node_list.des.text.text = self.data.preview_des
	self.node_list.tips_img.image:LoadSprite(bundle , asset)

	local is_exist = RoleBagWGData.Instance:GetBagOrInventoryIsExistById(self.data.item_id)
	if is_exist then
		self.node_list.go_btn.button.interactable = false
		XUI.SetGraphicGrey(self.node_list.go_btn, true)
		self.node_list.go_btn_text.text.text = Language.CangBaoGe.Btn_State[2]
	else
		self.node_list.go_btn.button.interactable = true
		XUI.SetGraphicGrey(self.node_list.go_btn, false)
		self.node_list.go_btn_text.text.text = Language.CangBaoGe.Btn_State[1]
	end
end

function GuradListCell:OnGoClick()
	-- ViewManager.Instance:Open(GuideModuleName.Shop, nil, "select_item_id", {item_id = self.data.item_id}) --self.data.item_id

	if not self.data then return end
	local is_bind = 1
	local is_enough_bind
	if self.data.is_bind_gold > 0 then
		is_enough_bind = RoleWGData.Instance:GetIsEnoughBindGold(self.data.bind_gold_price)
	else
		is_bind = 0
	end
	if nil == self.alert_window then
		self.alert_window = Alert.New(nil, nil, nil, nil, false)
	end

	local str
	local item_data = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if is_bind == 1 then
		if not is_enough_bind then
			is_bind = 0
		end
		str = string.format(Language.Vip.VipOpenTips1,self.data.bind_gold_price ,item_data.name,self.data.gold_price)
	else
		str = string.format(Language.Vip.VipOpenTips,self.data.gold_price,item_data.name)
	end
	self.alert_window:SetLableString(str)
	self.alert_window:SetOkFunc(function ()
		ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, 1, 0, is_bind)
	end)
	self.alert_window:Open()
	-- ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, 1, 0, is_bind)
	-- self.parent:Close()
end