-- 神藏按钮
GodPurchaseTip = GodPurchaseTip or BaseClass(BaseRender)


function GodPurchaseTip:DoLoad(parent)
	self:LoadAsset("uis/view/god_purchase_ui_prefab", "layout_god_purchase_tip", parent.transform)
end

function GodPurchaseTip:__delete()
	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

end

function GodPurchaseTip:LoadCallBack()
	self:InitListener()
	self:InitModel()
end

function GodPurchaseTip:InitListener()
	XUI.AddClickEventListener(self.node_list.click, BindTool.Bind(self.OnClickGetBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickClose, self))
end

function GodPurchaseTip:InitModel()
	if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	self:FlushModel()
end

function GodPurchaseTip:FlushModel()
	local show_info = GodPurchaseWGData.Instance:GetTipShowShopCfg()
	if IsEmptyTable(show_info) then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if show_info.model_show_itemid ~= 0 and show_info.model_show_itemid ~= "" then
		local split_list = string.split(show_info.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_info.model_show_itemid
		end
	end
	display_data.bundle_name = show_info["model_bundle_name"]
    display_data.asset_name = show_info["model_asset_name"]
    local model_show_type = tonumber(show_info["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

    self.model_display:SetData(display_data)
    local scale = show_info["display_scale"]
    Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
	if show_info.display_pos and show_info.display_pos ~= "" then
		local pos_list = string.split(show_info.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display_model.rect, pos_x, pos_y)

	if show_info.rotation and show_info.rotation ~= "" then
		local rotation_tab = string.split(show_info.rotation,"|")
		self.node_list["display_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function GodPurchaseTip:OnClickGetBtn()
	ViewManager.Instance:Open(GuideModuleName.GodPchaseView)
end

function GodPurchaseTip:OnClickClose()
	GodPurchaseWGData.Instance:SetIsShowTip(false)
	MainuiWGCtrl.Instance:FlushView(0, "flush_god_purchase_tip")
end