SuperPurchaseView = SuperPurchaseView or BaseClass(SafeBaseView)
function SuperPurchaseView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/super_purchase_ui_prefab", "layout_super_purchase_view")
end

function SuperPurchaseView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_all_buy"], BindTool.Bind(self.OnClickAllBuy, self))

    if self.reward_grade_list == nil then
        self.reward_grade_list = AsyncListView.New(SuperPurchaseRender, self.node_list["reward_grade_list"])
    end
end

function SuperPurchaseView:ReleaseCallBack()
    if self.reward_grade_list then
        self.reward_grade_list:DeleteMe()
        self.reward_grade_list = nil
    end

    if CountDownManager.Instance:HasCountDown("super_purchase_time") then
        CountDownManager.Instance:RemoveCountDown("super_purchase_time")
    end
end

function SuperPurchaseView:OpenCallBack()
    SuperPurchaseWGCtrl.Instance:ReqActivityLimitBuy3Info(OA_LIMIT_RMB_BUY3_OPERATE_TYPE.INFO)
end

function SuperPurchaseView:OnClickAllBuy()
    local is_all_buy = SuperPurchaseWGData.Instance:GetAllBuyStateBySeq() --是否购买完
    local is_buy = SuperPurchaseWGData.Instance:GetRewardIsBuy() --是否购买过

    if is_all_buy or is_buy then
        return
    end

    local all_buy_cfg = SuperPurchaseWGData.Instance:GetGradeAllBuyCfg()
    if all_buy_cfg then
        RechargeWGCtrl.Instance:Recharge(all_buy_cfg.rmb_price, all_buy_cfg.rmb_type, all_buy_cfg.rmb_seq)
    end
end

function SuperPurchaseView:ShowIndexCallBack()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.SuperPurchaseView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY3)
end

function SuperPurchaseView:OnFlush(param_t)
    local seq_reward_list = SuperPurchaseWGData.Instance:GetGradeRewardList()
    if seq_reward_list then
        self.reward_grade_list:SetDataList(seq_reward_list)
    end

    local is_all_buy = SuperPurchaseWGData.Instance:GetAllBuyStateBySeq() --是否购买完
    local is_buy = SuperPurchaseWGData.Instance:GetRewardIsBuy() --是否购买过
    self.node_list["btn_all_buy"]:SetActive((not is_buy) and (not is_all_buy))
    self.node_list["all_is_buy"]:SetActive(is_all_buy)

    local all_buy_cfg = SuperPurchaseWGData.Instance:GetGradeAllBuyCfg()
    if all_buy_cfg then
        local price = RoleWGData.GetPayMoneyStr(all_buy_cfg.rmb_price, all_buy_cfg.rmb_type, all_buy_cfg.rmb_seq)
        -- self.node_list["all_price"].text.text = price

        self.node_list.text_all_buy.text.text = price..Language.SuperPurchase.BuyBtnStr
        self.node_list.zhekou_lbl.text.text = all_buy_cfg.discount..Language.SuperPurchase.Discount
    end
    
    
    self:FlushTimeCount()
end


function SuperPurchaseView:FlushTimeCount()
    if CountDownManager.Instance:HasCountDown("super_purchase_time") then
        CountDownManager.Instance:RemoveCountDown("super_purchase_time")
    end

    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        CountDownManager.Instance:AddCountDown("super_purchase_time", 
            BindTool.Bind(self.FinalUpdateTimeCallBack, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function SuperPurchaseView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["time_down"].text.text = time_str
end

function SuperPurchaseView:OnComplete()
    if self.node_list.act_time then
        self.node_list.act_time.text.text = ""
    end
end


SuperPurchaseRender = SuperPurchaseRender or BaseClass(BaseRender)
function SuperPurchaseRender:__init()
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickCellBtn, self))
    self.show_item = ItemCell.New(self.node_list["big_reward_pos"])

    if self.item_reward_list == nil then
        self.item_reward_list = {}
        for i = 1, 6 do
            self.item_reward_list[i] = ItemCell.New(self.node_list["reward_pos_" .. i])
        end
    end
end

function SuperPurchaseRender:__delete()
    if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end

    if self.item_reward_list then
        for k, v in pairs(self.item_reward_list) do
            v:DeleteMe()
        end
        self.item_reward_list = nil
    end
end

function SuperPurchaseRender:OnFlush()
    if self.data == nil then
        return
    end

    for k, v in pairs(self.item_reward_list) do
        if self.data.reward_item and self.data.reward_item[k] then
            v:SetData(self.data.reward_item[k])
        end
    end

    self.show_item:SetData(self.data.show_item)

    local price = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list["buy_text"].text.text = price

    local already_buy_times = SuperPurchaseWGData.Instance:GetBuyCountBySeq(self.data.seq)
    local times_color = (self.data.buy_times - already_buy_times) >= self.data.buy_times and COLOR3B.L_GREEN or COLOR3B.L_RED
    local str_col = ToColorStr((self.data.buy_times - already_buy_times) .. "/" .. self.data.buy_times, times_color)
    self.node_list["limit_buy"].text.text = string.format(Language.SuperPurchase.BuyTimesStr, str_col)

    local bundle, asset = ResPath.GetRawImagesPNG("a1_czxg_bg" .. self.data.seq + 1)
    self.node_list["bg"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["bg"].raw_image:SetNativeSize()
    end)

    for i = 1, 3 do
        --self.node_list["title_name_" .. i].text.text = self.data.name
        self.node_list["title_name_" .. i]:SetActive((self.data.seq + 1) == i)
    end

    local is_buy = SuperPurchaseWGData.Instance:GetBuyStateBySeq(self.data.seq + 1)
    self.node_list["btn_root"]:SetActive(not is_buy)
    self.node_list["is_buy"]:SetActive(is_buy)

    self.node_list["jiaobiao_lbl"].text.text = self.data.label_name
end

function SuperPurchaseRender:OnClickCellBtn()
    if self.data == nil then
        return
    end

    local is_buy = SuperPurchaseWGData.Instance:GetBuyStateBySeq(self.data.seq + 1)
    if not is_buy then
        RechargeWGCtrl.Instance:Recharge(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    end
end