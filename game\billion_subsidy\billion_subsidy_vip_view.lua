-----------------------------------
-- 百亿补贴-会员首页
-----------------------------------
function BillionSubsidyView:VIPLoadIndexCallBack()
    --self.tween_mask_bg = UITween.CanvasGroup(self.node_list["tween_mask_bg"].gameObject)
    XUI.AddClickEventListener(self.node_list.btn_open_buy_vip, BindTool.Bind(self.OnClickOpenBuyVipBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_one_key_expand, BindTool.Bind(self.OnClickOneKeyExpandBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_double_expand, BindTool.Bind(self.OnClickDoubleExpandBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_buy_vip_gift, BindTool.Bind(self.OnClickBuyVipGiftBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_receive_discount_coupon, BindTool.Bind(self.OnClickReceiveDCBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_receive_free_coupon, BindTool.Bind(self.OnClickReceiveFCBtn, self))

    if not self.vip_reward_list then
        self.vip_reward_list = AsyncListView.New(ItemCell, self.node_list.vip_reward_list)
        self.vip_reward_list:SetStartZeroIndex(true)
    end

    if not self.discount_coupon_list then
        self.discount_coupon_list = AsyncListView.New(BillionSubsidyDiscountCouponCell, self.node_list.discount_coupon_list)
        self.discount_coupon_list:SetStartZeroIndex(false)
    end
end

function BillionSubsidyView:VIPReleaseCallBack()
    if self.vip_reward_list then
        self.vip_reward_list:DeleteMe()
        self.vip_reward_list = nil
    end

    if self.discount_coupon_list then
        self.discount_coupon_list:DeleteMe()
        self.discount_coupon_list = nil
    end
end

function BillionSubsidyView:VIPCloseCallBack()

end

function BillionSubsidyView:VIPOpenIndexCallBack()
    local fetch_first_open_view_reward = BillionSubsidyWGData.Instance:GetIsFetchFirstOpenViewReward()
    local is_first_open = BillionSubsidyWGData.Instance:GetIsFirstOpenView()

    if fetch_first_open_view_reward == 0 and is_first_open ~= 0 and not self.is_first_open then
        --BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_FIRST_OPEN_VIEW_REWARD)
        local callback = function ()
            BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_FIRST_OPEN_VIEW_REWARD)
        end

        BillionSubsidyWGCtrl.Instance:PlayReceiveDCEffect(callback)
    end
end

function BillionSubsidyView:VIPShowIndexCallBack()

end

function BillionSubsidyView:VIPOnFlush(param_t, index)
    local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
    local total_saved_chongzhi_num = BillionSubsidyWGData.Instance:GetTotalSavedChongzhiNum()
    local daily_gift_cfg = BillionSubsidyWGData.Instance:GetDailyGiftCfg()
    local all_member_reward_fetch_flag = BillionSubsidyWGData.Instance:GetAllMemberRewardFetchFlag()
    local member_level_cfg = BillionSubsidyWGData.Instance:GetCurMemberLevelCfg()
    local max_level = BillionSubsidyWGData.Instance:GetMaxMemberLevel()
    local cumulate_order_reward_can_fetch_times = BillionSubsidyWGData.Instance:GetCumulateOrderRewardCanFetchTimes()
    local cumulate_order_num = BillionSubsidyWGData.Instance:GetCumulateOrderNum()
    local other_cfg = BillionSubsidyWGData.Instance:GetOtherCfg()
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local dc_all_data_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList()

    --累计节省
    self.node_list.vip_role_name.text.text = main_role_vo.role_name
    self.node_list.total_save_money_text.text.text = string.format(Language.BillionSubsidy.TotalSaveMoneyDesc, total_saved_chongzhi_num)

    --会员等级展示
    self.node_list.vip_name.text.text = Language.BillionSubsidy.VipNameList[member_level]
    local bundle, asset = ResPath.GetBillionSubsidyImg("a3_bybt_hz" .. member_level)
    self.node_list.vip_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.vip_icon.image:SetNativeSize()
    end)

    local bundle2, asset2 = ResPath.GetEffectUi("UI_bybt_huizhang_" .. member_level)
    self.node_list.vip_icon_effect:ChangeAsset(bundle2, asset2)

    --折扣券和膨胀按钮
    local is_can_show_vip_blank_tips = BillionSubsidyWGData.Instance:GetShowVipBlankTipsFlag()
    self.discount_coupon_list:SetDataList(dc_all_data_list)
    local is_empty = IsEmptyTable(dc_all_data_list)
    self.node_list.vip_blank_tips:SetActive(is_empty and is_can_show_vip_blank_tips)
    self.node_list.discount_coupon_list:SetActive(not is_empty)
    local is_can_one_key_expand = BillionSubsidyWGData.Instance:GetIsCanExpand(1)
    local is_can_double_expand = BillionSubsidyWGData.Instance:GetIsCanExpand(2)
    --self.node_list.btn_one_key_expand:SetActive(is_can_one_key_expand)
    local expand_remind = BillionSubsidyWGData.Instance:GetDCExpandRemind()
    self.node_list.btn_one_key_expand_remind:SetActive(is_can_one_key_expand)
    self.node_list.btn_double_expand_remind:SetActive(is_can_double_expand)

    --每日礼包
    self.node_list.daily_gift_desc.text.text = string.format(Language.BillionSubsidy.DailyGiftDesc2, daily_gift_cfg.buy_limit)
    self.node_list.gift_save_money_text.text.text = string.format(Language.BillionSubsidy.DailyGiftSaveMoneyText, daily_gift_cfg.subsidy)
    if member_level < daily_gift_cfg.need_member_level then
        self.node_list.vip_gift_price.text.text = string.format(Language.BillionSubsidy.DailyGiftLockDesc, Language.BillionSubsidy.VipNameList[daily_gift_cfg.need_member_level])
    else
        local price_str = RoleWGData.GetPayMoneyStr(daily_gift_cfg.price, daily_gift_cfg.rmb_type, daily_gift_cfg.rmb_seq)
        self.node_list.vip_gift_price.text.text = price_str
    end


    local daily_gift_buy_flag = BillionSubsidyWGData.Instance:GetBYBTVIPDailyShopSingleItemIsBuy()
    self.vip_reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(daily_gift_buy_flag)
        end
    end)
    self.vip_reward_list:SetDataList(daily_gift_cfg.reward)

    self.node_list.vip_gift_flag:SetActive(daily_gift_buy_flag)
    self.node_list.btn_buy_vip_gift:SetActive(not daily_gift_buy_flag)

    --领取券
    local try_ticket_num = member_level_cfg.daily_try_ticket_reward
    local ticket_num = BillionSubsidyWGData.Instance:GetAllCanReceiveDCNum()
    local discount_coupon_receive_flag = member_level == 1
    ticket_num = discount_coupon_receive_flag and 0 or ticket_num
    local all_num = all_member_reward_fetch_flag == 1 and 0 or (ticket_num + try_ticket_num)
    --self.node_list.discount_coupon_receive_title.text.text = string.format(Language.BillionSubsidy.DCReceiveTitle1, all_num)
    self.node_list.discount_coupon_receive_count.text.text = string.format(Language.BillionSubsidy.DCReceiveTitle1, all_num)

    --self.node_list.discount_coupon_receive_text2:SetActive(member_level < max_level)

    --self.node_list.free_coupon_receive_title.text.text = string.format(Language.BillionSubsidy.DCReceiveTitle2, cumulate_order_reward_can_fetch_times)
    -- local color = cumulate_order_num >= other_cfg.cumulate_order_num and COLOR3B.C2 or COLOR3B.C3
    -- self.node_list.free_coupon_receive_desc.text.text = string.format(Language.BillionSubsidy.DCReceiveDesc2, color,
    -- cumulate_order_num, other_cfg.cumulate_order_num)

    local cur_cumulate_order_num = (cumulate_order_reward_can_fetch_times * other_cfg.cumulate_order_num) +
                                    (cumulate_order_num % other_cfg.cumulate_order_num)
    local color = cur_cumulate_order_num >= other_cfg.cumulate_order_num and COLOR3B.C2 or COLOR3B.C2
    self.node_list.free_coupon_receive_desc.text.text = string.format(Language.BillionSubsidy.DCReceiveDesc2, color,
    cur_cumulate_order_num, other_cfg.cumulate_order_num)

    local dc_receive_remind = BillionSubsidyWGData.Instance:GetDCReceiveRemind()
    local fc_receive_remind = BillionSubsidyWGData.Instance:GetFCReceiveRemind()
    self.node_list.discount_coupon_receive_remind:SetActive(dc_receive_remind)
    self.node_list.free_coupon_receive_remind:SetActive(fc_receive_remind)
end

--打开会员购买界面
function BillionSubsidyView:OnClickOpenBuyVipBtn()
    BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
end

function BillionSubsidyView:SetVipBlankTipsShow(bool)
    self.node_list.vip_blank_tips:SetActive(bool)
end

function BillionSubsidyView:OnClickOneKeyExpandBtn()
    local is_can_one_key_expand = BillionSubsidyWGData.Instance:GetIsCanExpand(1)
    if is_can_one_key_expand then
        BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.ONE_KEY_DISCOUNT_EXPEND, 1)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.NoDCCanExtendTip)
    end
end

function BillionSubsidyView:OnClickDoubleExpandBtn()
    local member_level_cfg = BillionSubsidyWGData.Instance:GetCurMemberLevelCfg()
    if member_level_cfg.discount_ticket_extend_times <= 1 then
        --打开会员购买界面
        BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
    elseif BillionSubsidyWGData.Instance:GetDCExpandRemind() then
        BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.ONE_KEY_DISCOUNT_EXPEND, 2)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.NoDCCanExtendTip)
    end
end

function BillionSubsidyView:OnClickBuyVipGiftBtn()
    local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
    local daily_gift_cfg = BillionSubsidyWGData.Instance:GetDailyGiftCfg()
    if member_level < daily_gift_cfg.need_member_level then
        BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
        TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.BillionSubsidy.DailyGiftLockDesc, Language.BillionSubsidy.VipNameList[daily_gift_cfg.need_member_level]))
    else
        RechargeWGCtrl.Instance:Recharge(daily_gift_cfg.price, daily_gift_cfg.rmb_type, daily_gift_cfg.rmb_seq)
    end
end

function BillionSubsidyView:OnClickReceiveDCBtn()
    local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
    local today_member_reward_fetch_flag = BillionSubsidyWGData.Instance:GetTodayMemberRewardFetchFlag()
    if member_level == 1 then
        --打开会员购买界面
        BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
    elseif today_member_reward_fetch_flag == 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.DCAlreadyReceiveTip)
    else
        local callback = function ()
            BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_HIGNMEMBER_MEMBER_LEVEL_DAILY_REWARD)
        end

        BillionSubsidyWGCtrl.Instance:PlayReceiveDCEffect(callback)
    end
end

function BillionSubsidyView:OnClickReceiveFCBtn()
    local member_level_cfg = BillionSubsidyWGData.Instance:GetCurMemberLevelCfg()
    local cumulate_order_reward_can_fetch_times = BillionSubsidyWGData.Instance:GetCumulateOrderRewardCanFetchTimes()
    if member_level_cfg.can_fetch_cumulate_order_num_reward == 0 then
        --打开会员购买界面
        BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
    elseif cumulate_order_reward_can_fetch_times <= 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.FCUnableReceiveTip)
    else
        local callback = function ()
            BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_CUMULATE_ORDER_NUM_REWARD)
        end

        BillionSubsidyWGCtrl.Instance:PlayReceiveDCEffect(callback)
    end
end

-- function BillionSubsidyView:PlayReceiveDCEffect(callback)
--     if not self.tween_mask_bg then return end
-- 	self.tween_mask_bg.alpha = 1
--     local bundle_name, asset_name = ResPath.GetEffectUi("UI_bybt_zhekoujuan")
--     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos1"].transform, 2.6, nil)
--     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos2"].transform, 2.6, nil)
--     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos3"].transform, 2.6, nil, nil, nil, nil, function ()
--         self.tween_mask_bg.alpha = 0
--         if callback then
--             callback()
--         end
--     end)
-- end

BillionSubsidyDiscountCouponCell = BillionSubsidyDiscountCouponCell or BaseClass(BaseRender)
function BillionSubsidyDiscountCouponCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_expand_discount_coupon, BindTool.Bind(self.OnClickExpandDCBtn, self))
    XUI.AddClickEventListener(self.node_list.item_tips_btn, BindTool.Bind(self.OnClickItemTipsBtn, self))
end

function BillionSubsidyDiscountCouponCell:__delete()

end

function BillionSubsidyDiscountCouponCell:OnFlush()
	if self.data == nil then
		return
	end

    local member_level_cfg = BillionSubsidyWGData.Instance:GetCurMemberLevelCfg()
    local fc_num = BillionSubsidyWGData.Instance:GetUsedFreeTicketNum()
    local dc_num = BillionSubsidyWGData.Instance:GetUsedDiscountTicketNum()

    local have_fc_use_num = member_level_cfg.free_ticket_daily_use_limit - fc_num
    local have_dc_use_num = member_level_cfg.no_limit_discount_daily_use_limit - dc_num

    if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        self.node_list.free_discount_coupon_content:SetActive(true)
        self.node_list.other_discount_coupon_content:SetActive(false)
        self.node_list.free_limit_use_text.text.text = Language.BillionSubsidy.FreeDCQuotaLimit2

        if member_level_cfg.free_ticket_daily_use_limit <= 0 then
            local vip_level = BillionSubsidyWGData.Instance:GetUseFreeTicketVipLevel()
            self.node_list.bottom_limit_desc:SetActive(false)
            self.node_list.free_bottom_panel:SetActive(true)
            local bundle, asset = ResPath.GetBillionSubsidyImg("a3_bybt_hz" .. vip_level)
            self.node_list.vip_icon.image:LoadSprite(bundle, asset, function()
                self.node_list.vip_icon.image:SetNativeSize()
            end)
        else
            self.node_list.bottom_limit_desc.text.text = string.format(Language.BillionSubsidy.FreeDCQuotaLimit3, have_fc_use_num)
            self.node_list.bottom_limit_desc:SetActive(true)
            self.node_list.free_bottom_panel:SetActive(false)
        end
    else
        self.node_list.free_discount_coupon_content:SetActive(false)
        self.node_list.other_discount_coupon_content:SetActive(true)
        self.node_list.bottom_limit_desc:SetActive(true)
        self.node_list.bottom_limit_desc.text.text = Language.BillionSubsidy.DCNoQuotaLimit3
        self.node_list.name.text.text = Language.BillionSubsidy.DCNameList[self.data.type]
        self.node_list.save_money_text.text.text = string.format(Language.BillionSubsidy.DCReduceQuota, self.data.reduce_quota)
        if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
            self.node_list.limit_use_text.text.text = string.format(Language.BillionSubsidy.DCQuotaLimit2, have_dc_use_num)
        else
            self.node_list.limit_use_text.text.text = string.format(Language.BillionSubsidy.DCQuotaLimit, self.data.quota_limit)
        end

        if member_level_cfg.discount_ticket_extend_times > self.data.expend_times then
            if self.data.expend_times > 0 then
                self.node_list.expand_desc.text.text = string.format(Language.BillionSubsidy.ExpendTimesDesc, self.data.expend_times)
            else
                self.node_list.expand_desc.text.text = Language.BillionSubsidy.ExpendTimesDesc2
            end

            self.node_list.btn_expand_discount_coupon:SetActive(true)
            self.node_list.not_expand_desc:SetActive(false)
        else
            self.node_list.btn_expand_discount_coupon:SetActive(false)
            self.node_list.not_expand_desc:SetActive(true)
            self.node_list.not_expand_desc.text.text = string.format(Language.BillionSubsidy.ExpendTimesDesc, self.data.expend_times)
        end
    end
end

function BillionSubsidyDiscountCouponCell:OnClickExpandDCBtn()
    BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.DISCOUNT_EXPEND_ONCE, self.data.data_seq)
end

function BillionSubsidyDiscountCouponCell:OnClickItemTipsBtn()
    local other_cfg = BillionSubsidyWGData.Instance:GetOtherCfg()
    local is_free_discount = self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON
    local item_id = is_free_discount and other_cfg.free_ticket_click_item_id or other_cfg.full_discount_ticket_click_item_id
    TipWGCtrl.Instance:OpenItem({ item_id = item_id })
end