--[[
	/jy_gm esotericagmoperate:1 0 0 0 0

	1 = 重置秘笈		param1:seq
	2 = 设置等级		param1:seq	param2:level
	3 = 设置部位等级	param1:seq	param2:part	 param3:level
	4 = 设置经验值		param1:exp
	5 = 设置激活状态	param1:seq	param2:is_active
	6 = 设置技能状态	param1:seq	param2:is_reset
]]

CultivationWGCtrl = CultivationWGCtrl or BaseClass(BaseWGCtrl)
function CultivationWGCtrl:Esoterica__init()
	self:RegisterEsotericaProtocols()
end

function CultivationWGCtrl:Esoterica__delete()

end

function CultivationWGCtrl:RegisterEsotericaProtocols()
	self:RegisterProtocol(CSEsotericaOperate)
	self:RegisterProtocol(SCEsotericaItemInfo, "OnEsotericaItemInfo")
	self:RegisterProtocol(SCEsotericaItemUpdate, "OnEsotericaItemUpdate")
	self:RegisterProtocol(SCEsotericaBaseInfo, "OnEsotericaBaseInfo")
	self:RegisterProtocol(CSEsotericaEquipDecompos)
	self:RegisterProtocol(SCEsotericaBag, "OnEsotericaBag")
	self:RegisterProtocol(SCEsotericaBagGrid, "OnEsotericaBagGrid")
	self:RegisterProtocol(SCEsotericaSkillInfo, "OnEsotericaSkillInfo")
	self:RegisterProtocol(SCEsotericaSkillUpdate, "OnEsotericaSkillUpdate")
end

function CultivationWGCtrl:FlushEsotericaDetailView()
	-- if self.view:IsOpen() then
	-- 	self.view:Flush(TabIndex.esoterica)
	-- end

	if self.esoterica_detail_view:IsOpen() then
		self.esoterica_detail_view:Flush()
	end
end

function CultivationWGCtrl:FlushEsotericaPartView()
	if self.esoterica_part_view:IsOpen() then
		self.esoterica_part_view:Flush()
	end
end

function CultivationWGCtrl:FlushEsotericaView()
	if self.esoterica_view:IsOpen() then
		self.esoterica_view:Flush()
		self.esoterica_view:FlushEsotericaList()
	end
end

function CultivationWGCtrl:FlushEsotericaResloveView()
	if self.esoterica_reslove_view:IsOpen() then
		self.esoterica_reslove_view:Flush()
	end
end

-- 秘笈 - 槽位信息
function CultivationWGCtrl:OnEsotericaItemInfo(protocol)
	-- print_error("-- 秘笈 - 槽位信息")
	self.data:SetEsotericaSlotInfo(protocol)

	self:FlushEsotericaDetailView()
	self:FlushEsotericaView()
	self:FlushEsotericaPartView()
	self:FlushEsotericaResloveView()
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	RemindManager.Instance:Fire(RemindName.Esoterica)
	RemindManager.Instance:Fire(RemindName.XiuWei)
end

-- 秘笈 - 槽位更新
function CultivationWGCtrl:OnEsotericaItemUpdate(protocol)
	-- print_error("-- 秘笈 - 槽位更新", protocol)
	local old_esoterica_data = self.data:GetEsotericaSlotInfo(protocol.slot)
	self.data:UpdateEsotericaSlotInfo(protocol)
	local new_esoterica_data = protocol.esoterica_data

	if not old_esoterica_data.is_active and new_esoterica_data.is_active then
		local data = {}
		data.appe_type = ROLE_APPE_TYPE.ESOTERICA
		data.appe_image_id = protocol.slot
		AppearanceWGCtrl.Instance:AddAndOpenNewAppeInfoList(data)
	end

	self:FlushEsotericaDetailView()
	self:FlushEsotericaView()
	self:FlushEsotericaPartView()
	self:FlushEsotericaResloveView()
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	RemindManager.Instance:Fire(RemindName.Esoterica)
	RemindManager.Instance:Fire(RemindName.XiuWei)
	RemindManager.Instance:Fire(RemindName.EsotericaReslove)
end

-- 秘笈 - 基础信息
function CultivationWGCtrl:OnEsotericaBaseInfo(protocol)
	-- print_error("-- 秘笈 - 基础信息")
	local old_exp = self.data:GetEsotericaEXP()
	self.data:SetEsotericaBaseInfo(protocol)

	local new_exp = self.data:GetEsotericaEXP()
	if old_exp >= 0 and new_exp > old_exp then
		local item_cfg = ItemWGData.Instance:GetItemConfig(ESOTERICA_DEFINE.EXP_ITEM_ID)
		if item_cfg then
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, new_exp - old_exp))
		end
	end

	self:FlushEsotericaDetailView()
	self:FlushEsotericaView()
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	RemindManager.Instance:Fire(RemindName.Esoterica)
	RemindManager.Instance:Fire(RemindName.EsotericaReslove)
	RemindManager.Instance:Fire(RemindName.XiuWei)
end

-- 秘笈 - 背包信息
function CultivationWGCtrl:OnEsotericaBag(protocol)
	-- print_error("-- 秘笈 - 背包信息")
	self.data:SetEsotericaBagInfo(protocol)

	self:FlushEsotericaDetailView()
	self:FlushEsotericaView()
	self:FlushEsotericaResloveView()
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	RemindManager.Instance:Fire(RemindName.Esoterica)
	RemindManager.Instance:Fire(RemindName.EsotericaReslove)
	RemindManager.Instance:Fire(RemindName.XiuWei)
end

-- 秘笈 - 背包信息变化
function CultivationWGCtrl:OnEsotericaBagGrid(protocol)
	-- print_error("-- 秘笈 - 背包信息变化")
	self.data:UpdateEsotericaBagGrid(protocol)

	self:FlushEsotericaDetailView()
	self:FlushEsotericaView()
	self:FlushEsotericaResloveView()
	ViewManager.Instance:FlushView(GuideModuleName.XiuWeiView)
	RemindManager.Instance:Fire(RemindName.Esoterica)
	RemindManager.Instance:Fire(RemindName.EsotericaReslove)
	RemindManager.Instance:Fire(RemindName.XiuWei)
end

-- 秘笈 - 技能信息
function CultivationWGCtrl:OnEsotericaSkillInfo(protocol)
	-- print_error("-- 秘笈 - 技能信息")
	self.data:SetEsotericaSkillInfo(protocol)
	MainuiWGCtrl.Instance:FlushEsotericaSkill()
end

-- 秘笈 - 技能更新
function CultivationWGCtrl:OnEsotericaSkillUpdate(protocol)
	-- print_error("-- 秘笈 - 技能更新", protocol)
	self.data:UpdateEsotericaSkillInfo(protocol)
	MainuiWGCtrl.Instance:FlushEsotericaSkill()
end

--[[ ESOTERICA_OPERATE_TYPE
	INFO = 1,						-- 秘笈信息
	BASE = 2,						-- 基础信息
	WEAR = 3,						-- 穿戴装备		param1:seq  param2:part			param3:bag_index
	UPLEVEL = 4,					-- 秘笈升级		param1:seq	param2:is_auto
	PART_UPLEVEL =5,				-- 部位升级		param1:seq	param2:part
	ACTIVE = 6,						-- 秘笈激活		param1:seq
	SKILL_INFO = 7,					-- 技能信息
	PERFORM_SKILL = 8,				-- 释放技能		param1:seq  param2:target_id	param3:pos_x	param4:pos_y
]]
-- 秘笈 - 请求
function CultivationWGCtrl:OnEsotericaOperate(operate_type, param1, param2, param3, param4)
	-- print_error("-----秘笈 - 请求-----", operate_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEsotericaOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end

-- 秘笈 - 装备分解请求
function CultivationWGCtrl:OnEsotericaEquipDecompos(decompos_list)
	-- print_error("-------分解-------", decompos_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEsotericaEquipDecompos)
	protocol.decompos_list = decompos_list or {}
	protocol:EncodeAndSend()
end

-- 秘笈 物品变化
function CultivationWGCtrl:OnEsotericaItemDataChange(change_item_id, change_item_index, change_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then

		local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, new_num - old_num))

			self:FlushEsotericaPartView()
			-- 快速使用
			local change_data = self.data:GetEsotericaBagItem(change_item_index)
			self:AddEsotericaKeyUse(change_data)
		end
	end
end

-- 秘笈 快速使用
function CultivationWGCtrl:AddEsotericaKeyUse(data)
	if data == nil then
		return
	end

	if not self.data:EsotericaIsBetterWearByGirdData(data) then
		return
	end

	FunctionGuide.Instance:OpenEsotericaKeyUseView(data)
end

function CultivationWGCtrl:OpenEsotericaPartView(slot, part)
	self.esoterica_part_view:SetDataAndOpen(slot, part)
end

function CultivationWGCtrl:OpenEsotericaResloveView()
	self.esoterica_reslove_view:SetDataAndOpen()
end

function CultivationWGCtrl:JumpEsotericaView(slot)
	-- self.view:Flush(TabIndex.esoterica, "all", {slot = slot})
	-- if not self.view:IsOpen() then
	-- 	self.view:Open(TabIndex.esoterica)
	-- end

	self.esoterica_detail_view:Flush(0, "all", {slot = slot})
	if not self.esoterica_detail_view:IsOpen() then
		self.esoterica_detail_view:Open()
	end
end