-- 天下第一准备场景和战斗场景的面板
WorldsNO1SceneView = WorldsNO1SceneView or BaseClass(SafeBaseView)
function WorldsNO1SceneView:__init()
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1_scene_view")
	self.view_cache_time = 0
    self.view_layer = UiLayer.MainUI
    self.is_safe_area_adapter = true
    self.active_close = false
end

function WorldsNO1SceneView:ReleaseCallBack()
	ResMgr:Destroy(self.node_list["left_panel"].gameObject)
	ResMgr:Destroy(self.node_list["right_panel"].gameObject)
	ResMgr:Destroy(self.node_list["bet_btn"].gameObject)
	ResMgr:Destroy(self.node_list["btn_worlds_no1_btn"].gameObject)

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	CountDownManager.Instance:RemoveCountDown("WorldsNO1SceneView:FlushCountDown")

	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end

	if self.buff_cell_list then
		for i,v in ipairs(self.buff_cell_list) do
			v:DeleteMe()
		end
		self.buff_cell_list = nil
	end

	if self.flush_buff_list_timer then
		GlobalTimerQuest:CancelQuest(self.flush_buff_list_timer)
		self.flush_buff_list_timer = nil
	end
end

function WorldsNO1SceneView:LoadCallBack()
	-- 挂到主界面上
	local left_parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	local right_parent = MainuiWGCtrl.Instance:GetSkillRightOtherContent()
	self.node_list["left_panel"].transform:SetParent(left_parent.transform, false)
	self.node_list["left_panel"].rect.anchoredPosition = Vector2(145, -128)
	self.node_list["right_panel"].transform:SetParent(right_parent.transform, false)
	-- MainuiWGCtrl.Instance:SetTaskButtonTrue()

	self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.FlushPanelActive, self))

	-- 规则
	if WorldsNO1WGData.Instance:GetCurRoundType() == WORLDS_NO1_MATCH_TYPE.AUDITION then
		self.node_list["rule"].text.text = WorldsNO1WGData.Instance:GetOtherCfg().scene_view_left_audition_rule
	else
		self.node_list["rule"].text.text = WorldsNO1WGData.Instance:GetOtherCfg().scene_view_left_knockout_rule
	end

	-- 排名列表
	self.rank_list = AsyncListView.New(WorldsSceneRankItem, self.node_list["rank_list"])

	-- buff列表
	self.flush_buff_list_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushBuffList, self), 1)
	self.node_list["buff_cell_prefab"]:SetActive(false)
	self.buff_cell_list = {}

    XUI.AddClickEventListener(self.node_list["btn_worlds_no1_btn"], BindTool.Bind1(self.OnClickWorldsNO1Btn,self)) 	
	XUI.AddClickEventListener(self.node_list["bet_btn"], BindTool.Bind1(self.OnClickBetBtn, self)) 						-- 竞猜按钮
	MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list["btn_worlds_no1_btn"])
	MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list["bet_btn"])
end


function WorldsNO1SceneView:OpenCallBack()
	-- 请求获取轮次信息
	WorldsNO1WGCtrl.Instance:SendRequestRoundInfo()
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
end

function WorldsNO1SceneView:CloseCallBack()
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
end

function WorldsNO1SceneView:ShowIndexCallBack()
end

function WorldsNO1SceneView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushAllPanel()
		elseif k == "fly_to_buff_list" then
			self:FlyToBuffList()
		elseif k == "fly_to_hp_bar" then
			self:FlyToHpBar()
		end
	end
end

function WorldsNO1SceneView:FlushAllPanel()
	self:FlushPanelActive()
	self:FlushOthers()
	self:FlushCountDown()
	self:FlushRankList()
end

function WorldsNO1SceneView:FlushPanelActive()
	local cur_scene_id = Scene.Instance:GetSceneId()
	local in_prepare_scene = cur_scene_id == WorldsNO1WGData.Instance:GetOtherCfg().standby_scene_id
	
	self.node_list["fight_title"]:SetActive(not in_prepare_scene)
	self.node_list["prepare_title"]:SetActive(in_prepare_scene)
	if in_prepare_scene then
		self.node_list["rule_panel"]:SetActive(true)
		self.node_list["rank_panel"]:SetActive(false)
	end
	self.node_list["bet_btn"]:SetActive(WorldsNO1WGData.Instance:GetIsBetState()) 									-- 竞猜按钮
	self.node_list["btn_worlds_no1_btn"]:SetActive(Scene.Instance:GetSceneType() == SceneType.WorldsNO1Prepare)
end

function WorldsNO1SceneView:FlushOthers()
	-- 我的排名
	local rank = WorldsNO1WGData.Instance:GetSceneMyRank()
	self.node_list["my_rank"].text.text = string.format(Language.WorldsNO1.MainRoleRank, rank)

	-- 剩余生命
	local relive_amount = string.format(Language.WorldsNO1.ReliveAmount, WorldsNO1WGData.Instance:GetReliveAmount()) 
	self.node_list["relive_amount"].text.text = relive_amount
	self.node_list["relive_amount_2"].text.text = relive_amount

	-- 轮次名称
	local round_cfg = WorldsNO1WGData.Instance:GetCurRoundCfg()
	if round_cfg then
		self.node_list["round_name"].text.text = ToColorStr(round_cfg.round_name .. round_cfg.sub_round_name, COLOR3B.D_GREEN)
	else
		self.node_list["round_name"].text.text = ""
	end
end

function WorldsNO1SceneView:FlushRankList()
	local rank_info_list = WorldsNO1WGData.Instance:GetSceneRankInfoList()
	self.rank_list:SetDataList(rank_info_list)
end

-- 刷新比赛倒计时
function WorldsNO1SceneView:FlushCountDown()
	local scene_id = Scene.Instance:GetSceneId()
	local count_down_end_time = WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp()
	if CountDownManager.Instance:HasCountDown("WorldsNO1SceneView:FlushCountDown") then
		CountDownManager.Instance:RemoveCountDown("WorldsNO1SceneView:FlushCountDown")
	end
	self:UpdatePKCountDown()
	if count_down_end_time > TimeWGCtrl.Instance:GetServerTime() then
		CountDownManager.Instance:AddCountDown("WorldsNO1SceneView:FlushCountDown", BindTool.Bind(self.UpdatePKCountDown, self), nil, count_down_end_time, nil, 1)
	end
end

function WorldsNO1SceneView:UpdatePKCountDown()
	local str = Language.WorldsNO1.StartCountDown
	local scene_id = Scene.Instance:GetSceneId()
	local count_down_end_time = WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp()
	local subround_stage = WorldsNO1WGData.Instance:GetCurSubroundStage()
	local end_str = ""
	-- 准备阶段
	if subround_stage == WORLDS_NO1_SUBROUND_STAGE.PREPARE then
		str = Language.WorldsNO1.StartCountDown
		if WorldsNO1WGData.Instance:GetCurSubroundIsEmpty() then 				-- 轮空
			end_str = Language.WorldsNO1.IsEmpty
		end
	-- 开始阶段
	elseif subround_stage == WORLDS_NO1_SUBROUND_STAGE.PK then
		str = Language.WorldsNO1.EndCountDown
		if not WorldsNO1WGData.Instance:GetCanJoin() then 					-- 没有参赛资格
			end_str = Language.WorldsNO1.CantJoin
		elseif WorldsNO1WGData.Instance:GetCurSubroundIsMiss() then 			-- 错过参加时间
			end_str = Language.WorldsNO1.IsMiss
		elseif WorldsNO1WGData.Instance:GetCurSubroundIsEmpty() then 			-- 轮空
			end_str = Language.WorldsNO1.IsEmpty
		end
	end


	if self.node_list["countdown"] then
		local time_str = TimeUtil.MSTime(math.floor(count_down_end_time - TimeWGCtrl.Instance:GetServerTime()))
		time_str = ToColorStr(time_str, "#fb3b3b")
		self.node_list["countdown"].text.text = string.format(str, time_str) .. end_str
	end
end

-- 刷新buff列表
function WorldsNO1SceneView:FlushBuffList()
	for i, v in ipairs(self.buff_cell_list) do
		if self.buff_cell_list[i].view then
			self.buff_cell_list[i].view.gameObject:SetActive(false)
		end
	end

	local buff_info_list = WorldsNO1WGData.Instance:GetVaildBuffInfoList()
	for i, v in ipairs(buff_info_list) do
		if not self.buff_cell_list[i] then
			local prefab = self.node_list["buff_cell_prefab"].gameObject
			local obj = ResMgr:Instantiate(prefab)
			obj:SetActive(true)
			obj.transform:SetParent(self.node_list["buff_list"].transform, false)
			self.buff_cell_list[i] = WorldsNO1BuffIcon.New(obj)
		end

		self.buff_cell_list[i]:SetData(v)
		self.buff_cell_list[i].view.gameObject:SetActive(true)
	end
end

-- 播放飞行特效到buff列表
function WorldsNO1SceneView:FlyToBuffList()
	local bundle, asset = ResPath.GetUIEffect("Effect_guangqiu")
	TipWGCtrl.Instance:ShowFlyEffectManager("WorldsNO1SceneView:FlyToBuffList", bundle, asset, self.node_list["fly_effect_start_pos"], 
		self.node_list["buff_list_effect_pos"], DG.Tweening.Ease.OutCubic, 1, nil, nil, 1, 50, nil, true, nil, nil, 80)
end

-- 播放飞行特效到角色血条
function WorldsNO1SceneView:FlyToHpBar()
	local bundle, asset = ResPath.GetUIEffect("Effect_guangqiu")
	TipWGCtrl.Instance:ShowFlyEffectManager("WorldsNO1SceneView:FlyToHpBar", bundle, asset, self.node_list["fly_effect_start_pos"], 
		self.node_list["hp_bar_effect_pos"], DG.Tweening.Ease.OutCubic, 1, nil, nil, 1, 50, nil, true, nil, nil, 80)
end

function WorldsNO1SceneView:OnClickBetBtn()
	ViewManager.Instance:Open(GuideModuleName.WorldsNO1View, nil, "change_select_tab", {select_tab = WorldsNO1ViewTAB.KNOCKOUT, subround_index = WorldsNO1WGData.Instance:GetCurSubround()})
end

-- 点击准备场景天下第一按钮
function WorldsNO1SceneView:OnClickWorldsNO1Btn()
	local cur_round_cfg = WorldsNO1WGData.Instance:GetCurRoundCfg()
	local select_tab = WorldsNO1ViewTAB.AUDITION
	local subround_index = nil
	if cur_round_cfg and cur_round_cfg.type == WORLDS_NO1_MATCH_TYPE.KNOCKOUT then
		select_tab = WorldsNO1ViewTAB.KNOCKOUT
		subround_index = WorldsNO1WGData.Instance:GetCurSubround()
	end
	ViewManager.Instance:Open(GuideModuleName.WorldsNO1View, nil, "change_select_tab", {select_tab = select_tab, subround_index = subround_index})
end
----------------------------- 场景实时排名item  ------------------------------------
WorldsSceneRankItem = WorldsSceneRankItem or BaseClass(BaseRender)
function WorldsSceneRankItem:__init()
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind1(self.OnClickBtn, self)) 		-- 竞猜按钮
end

function WorldsSceneRankItem:__delete()

end


function WorldsSceneRankItem:OnFlush()
	if self.data then
		-- 排名
		self.node_list["rank"]:SetActive(self.data.rank > 3)
		self.node_list["rank_img"]:SetActive(self.data.rank <= 3)
		if self.data.rank <= 3 then
			local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank)
			self.node_list["rank_img"].image:LoadSpriteAsync(bundle, asset, function ()
				self.node_list["rank_img"].image:SetNativeSize()
			end)
		else
			self.node_list["rank"].text.text = self.data.rank
		end

		-- 角色名称
		local color = COLOR3B.WHITE
		if self.data.is_main_role then
			color = COLOR3B.D_GREEN
		end
		self.node_list["name"].text.text = ToColorStr(self.data.role_name, color)

		-- 积分
		self.node_list["score"].text.text = self.data.score

		-- 剩余生命
		self.node_list["live_amount"].text.text = self.data.live_amount

		-- 是否淘汰
		self.node_list["out_label"]:SetActive(self.data.is_out)
	end
end

function WorldsSceneRankItem:OnClickBtn()
	self:ToAtkRole()
end

function WorldsSceneRankItem:ToAtkRole()
	local role_obj = Scene.Instance:GetRoleByOrginalUUIDStr(self.data.uuid_str)
	if not role_obj or not role_obj.vo or not role_obj:IsRole() or role_obj:IsRealDead() then
		return 
	end

	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	GuajiWGCtrl.Instance:DoAttackTarget(role_obj)
	GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, role_obj, SceneTargetSelectType.SCENE)
end
------------------------ buff图标 -------------------------------------- 
WorldsNO1BuffIcon = WorldsNO1BuffIcon or BaseClass(BaseRender)
function WorldsNO1BuffIcon:__init()
	self.node_list["buff_panel"]:SetActive(false)
	XUI.AddClickEventListener(self.node_list["buff_icon"], BindTool.Bind(self.OnClickIcon, self))
	XUI.AddClickEventListener(self.node_list["block"], BindTool.Bind(self.OnClickBuffPanelBlock, self))
end

function WorldsNO1BuffIcon:__delete()

end

function WorldsNO1BuffIcon:OnFlush()
	if self.data then
		local buff_cfg = WorldsNO1WGData.Instance:GetBuffCfg(self.data.buff_type)
		if buff_cfg.buff_continue_time == 0 then
			buff_cfg.buff_continue_time = 1
		end
		local progress_value = (self.data.end_timestamp - TimeWGCtrl.Instance:GetServerTime()) / (buff_cfg.buff_continue_time / 1000)
		self.node_list["buff_slider"].slider:DOValue(progress_value, 1):SetEase(DG.Tweening.Ease.Linear)

		if self.last_icon ~= buff_cfg.buff_icon then
			self.node_list["buff_icon"].image:LoadSprite(ResPath.GetWorldsNO1Img(buff_cfg.buff_icon))
			self.node_list["buff_icon"].image:SetNativeSize()
		end
		self.last_icon = buff_cfg.buff_icon

		-- tips面板的buff图标
		self.node_list["buff_tips_icon"].image:LoadSpriteAsync(ResPath.GetSkillIconById(buff_cfg.buff_skill_icon_id or 0))

		-- buff描述
		self.node_list["buff_desc"].text.text = buff_cfg.buff_desc or ""
	end
end

function WorldsNO1BuffIcon:OnClickIcon()
	self.node_list["buff_panel"]:SetActive(true)
end

function WorldsNO1BuffIcon:OnClickBuffPanelBlock()
	self.node_list["buff_panel"]:SetActive(false)
end