require("game/crossserver/kuafu_honorhalls/kf_honorhalls_wg_data")
require("game/crossserver/kuafu_honorhalls/kf_honorhalls_view")
require("game/crossserver/kuafu_honorhalls/kf_honorhalls_win")
require("game/crossserver/kuafu_honorhalls/kf_honorhalls_shop")
require("game/crossserver/kuafu_honorhalls/kf_honorhalls_reward_view")
-- require("game/crossserver/kuafu_honorhalls/kf_honorhalls_follow")
-- require("game/crossserver/kuafu_honorhalls/kf_honorhalls_buy")

KuafuHonorhallWGCtrl = KuafuHonorhallWGCtrl or BaseClass(BaseWGCtrl)

function KuafuHonorhallWGCtrl:__init()
	if KuafuHonorhallWGCtrl.Instance then
		error("[KuafuHonorhallWGCtrl]:Attempt to create singleton twice!")
	end
	KuafuHonorhallWGCtrl.Instance = self
	self.can_open = false

	self.data = KuafuHonorhallWGData.New()
	self.follow_view = HonorhallsView.New(GuideModuleName.HonorhallsView)
	self.buy_view = KuafuHonorhallShop.New()
	self.win_view = KuafuHonorhallWin.New()
	self.reward_view = HonorhallsRewardView.New()
	
	self:RegisterAllProtocals()
end

function KuafuHonorhallWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.follow_view:DeleteMe()
	self.follow_view = nil

	self.buy_view:DeleteMe()
	self.buy_view = nil
	self.can_open = nil

	if self.reward_view then
		self.reward_view:DeleteMe()
		self.reward_view = nil
	end

	if self.win_view then
		self.win_view:DeleteMe()
		self.win_view = nil
	end

	if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
	KuafuHonorhallWGCtrl.Instance = nil
end

function KuafuHonorhallWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCCrossXiuluoTowerSelfActivityInfo, "OnCrossXiuluoTowerSelfActivityInfo")
	self:RegisterProtocol(SCCrossXiuluoTowerRankInfo, "OnCrossXiuluoTowerRankInfo")
	self:RegisterProtocol(SCCrossXiuluoTowerChangeLayerNotice, "OnCrossXiuluoTowerChangeLayerNotice")
	self:RegisterProtocol(SCCrossXiuluoTowerInfo, "OnCrossXiuluoTowerInfo")
	self:RegisterProtocol(SCCrossXiuluoTowerBuffInfo, "OnSCCrossXiuluoTowerBuffInfo")
	self:RegisterProtocol(SCXiuluoTowerCrossNotice, "OnSCXiuluoTowerCrossNotice")--跨服修罗塔协议返回
	self:RegisterProtocol(SCCrossXiuluoTowerUserResult, "OnSCCrossXiuluoTowerUserResult")
	self:RegisterProtocol(SCXiuLuoTaPassInfo, "OnSCXiuLuoTaPassInfo")
	self:RegisterProtocol(SCXiuLuoTaFastPassInfo, "OnSCXiuLuoTaFastPassInfo")
	self:RegisterProtocol(SCXiuLuoTaLayerInfo, "OnSCXiuLuoTaLayerInfo")
	
	self:RegisterProtocol(CSCrossXiuluoTowerJoinReq)
	self:RegisterProtocol(CSCrossXiuluoTowerBuyBuff)
	self:RegisterProtocol(CSXiuluoTowerEnterReq)--本服修罗塔协议请求
end

function KuafuHonorhallWGCtrl:OpenBuyView()
	-- KuafuHonorhallWGCtrl.Instance:SendCrossXiuluoTowerJoinReq(0)
	self.buy_view:Open()
end

function KuafuHonorhallWGCtrl:OpenRewardView()
	self.reward_view:Open()
end

function KuafuHonorhallWGCtrl:OpenBuyPreview()
	self.buy_view:SetIsOpenPreview(true)
	self.buy_view:Open()
end

function KuafuHonorhallWGCtrl:OpenShopView()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if scene_cfg then
		local npc = scene_cfg.npcs[1] -- 顶层就一个NPC
		if npc then
			MoveCache.SetEndType(MoveEndType.DoNothing)
			MoveCache.param1 = npc.id
			local range = TaskWGData.Instance:GetNPCRange(npc.id)
			GuajiWGCtrl.Instance:MoveToPos(scene_id, npc.x, npc.y, range, nil, nil, nil, function ()
				self:OpenBuyView()
			end)
		else
			print_error("-----【Why npc cfg no data】------", scene_id)
		end
	end
end

function KuafuHonorhallWGCtrl:GetFollowView()
	return self.follow_view
end

function KuafuHonorhallWGCtrl:FlushFollowView(...)
	self.follow_view:Flush(...)
end

function KuafuHonorhallWGCtrl:FlushShopView(...)
	self.buy_view:Flush(...)
end


function KuafuHonorhallWGCtrl:ActivityChangeCallback(activity_type, status, next_status_switch_time, open_type)
	if activity_type ~= ACTIVITY_TYPE.KF_HONORHALLS then
		return
    end
    if Scene.Instance:GetSceneType() == SceneType.Kf_Honorhalls then
        if status == ACTIVITY_STATUS.STANDY then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(next_status_switch_time, false, GameEnum.FU_BEN_ZHUNBEI)
        elseif status == ACTIVITY_STATUS.OPEN then
            MainuiWGCtrl.Instance:SetFbIconEndCountDown(next_status_switch_time, false, GameEnum.FU_BEN_NEXT_LEVEL)
        elseif status == ACTIVITY_STATUS.CLOSE then
            MainuiWGCtrl.Instance:SetFbIconEndCountDown(0, false, GameEnum.FU_BEN_OUT_SCENE)
        end
    elseif status == ACTIVITY_STATUS.CLOSE then
        MainuiWGCtrl.Instance:SetFbIconEndCountDown(0, false, GameEnum.FU_BEN_OUT_SCENE)
    end

    if status == ACTIVITY_STATUS.OPEN and Scene.Instance:GetSceneType() == SceneType.Kf_Honorhalls then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end
end

function KuafuHonorhallWGCtrl:FlushRightCountView()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not activity_info then
		return
	end

    if activity_info.status == ACTIVITY_STATUS.STANDY then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time, false, GameEnum.FU_BEN_ZHUNBEI)
	elseif activity_info.status == ACTIVITY_STATUS.OPEN then
        MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time, false, GameEnum.FU_BEN_NEXT_LEVEL)
    elseif activity_info.status == ACTIVITY_STATUS.CLOSE then
        MainuiWGCtrl.Instance:SetFbIconEndCountDown(0, false, GameEnum.FU_BEN_OUT_SCENE)
    end
end

-- 跨服修罗塔个人活动信息
function KuafuHonorhallWGCtrl:OnCrossXiuluoTowerSelfActivityInfo(protocol)
	self.data:SetRoleInfo(protocol)
	self.follow_view:Flush(0, "role_info")
end

-- 跨服修罗塔排行榜信息
function KuafuHonorhallWGCtrl:OnCrossXiuluoTowerRankInfo(protocol)
	self.data:SetRankInfo(protocol)
	if self.follow_view:IsOpen() and self.follow_view:IsLoaded() then
		self.follow_view:Flush(0, "rank")
	end

	if self.reward_view:IsOpen() and self.reward_view:IsLoaded() then
		self.reward_view:Flush()
	end
end

-- 跨服修罗塔改变层提示
function KuafuHonorhallWGCtrl:OnCrossXiuluoTowerChangeLayerNotice(protocol)
	if Scene.Instance:GetSceneType() == SceneType.Kf_Honorhalls then
		-- FuBenPanelWGCtrl.Instance:SetCountDowmType(nil, GameEnum.XIU_LUO_TA_NEXT_LAYER)
		-- FuBenPanelWGCtrl.Instance:SetCountDowmTimer(self.data:GetDelayNextLayerTime())
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Node)
	end
end

-- 跨服修罗塔属性加成
function KuafuHonorhallWGCtrl:OnCrossXiuluoTowerInfo(protocol)
	self.data:SetAttrInfo(protocol)
end

-- 获得BUFF广播
function KuafuHonorhallWGCtrl:OnSCCrossXiuluoTowerBuffInfo(protocol)
	local obj = Scene.Instance:GetRoleByObjId(protocol.obj_id)
	if nil ~= obj then
		obj:GetVo().special_param = protocol.buff_num
		obj:GetVo().special_param2 = protocol.next_time
		obj:UpdateNameBoard()
	 	Scene.Instance:SceneSpecialHandler(obj)
	end
end

--跨服修罗他协议返回
function KuafuHonorhallWGCtrl:OnSCXiuluoTowerCrossNotice(protocol)
	
end

function KuafuHonorhallWGCtrl:OnSCCrossXiuluoTowerUserResult(protocol)
	self.data:SetPassInfo(protocol)
	self.follow_view:Flush(0, "pass_info")
	self:OpenHonorhallsWin()
end

function KuafuHonorhallWGCtrl:OnSCXiuLuoTaPassInfo(protocol)
	self.data:SetActFinishInfo(protocol)
end

-- 跨服修罗塔快速通关协议
function KuafuHonorhallWGCtrl:OnSCXiuLuoTaFastPassInfo(protocol)
	-- print_error("快速通关协议", protocol)
	self.data:SetFastPassInfo(protocol)
	self.follow_view:Flush(0, "best_pass_info")
end

-- 跨服修罗塔当前层数据
function KuafuHonorhallWGCtrl:OnSCXiuLuoTaLayerInfo(protocol)
	self.data:SetCurLayerRoleInfo(protocol)
	self.follow_view:Flush(0, "layerplayer_info")
end

-- 跨服修罗塔报名
function KuafuHonorhallWGCtrl:SendCrossXiuluoTowerJoinReq(type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossXiuluoTowerJoinReq)
	send_protocol.req_type = type
	send_protocol:EncodeAndSend()
end

-- 跨服修罗塔购买buff
function KuafuHonorhallWGCtrl:SendCrossXiuluoTowerBuyBuff(is_buy_realive_count, is_use_gold_bind)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossXiuluoTowerBuyBuff)
	send_protocol.is_buy_realive_count = is_buy_realive_count
	send_protocol.is_use_gold_bind = is_use_gold_bind
	send_protocol:EncodeAndSend()
end

--本服修罗他协议请求
function KuafuHonorhallWGCtrl:SendCSXiuluoTowerEnterReq()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSXiuluoTowerEnterReq)
	send_protocol:EncodeAndSend()
end

--任务面板
function KuafuHonorhallWGCtrl:OpenHonorhallsFollow()
	ViewManager.Instance:CloseAll()
	self.follow_view:Open()
	self.buy_view:Close()
end

function KuafuHonorhallWGCtrl:CloseHonorhallsFollow()
	self.follow_view:Close()
	self.buy_view:Close()
	MainuiWGCtrl.Instance:SetButtonModeClick(true)
end

function KuafuHonorhallWGCtrl:OpenHonorhallsWin()
	if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end

	local act_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if act_data.status == ACTIVITY_STATUS.CLOSE then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Kf_Honorhalls then
			self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.OpenHonorhallsWin, self))
		elseif scene_type ~= SceneType.Common then
			return
		end
		
		self.win_view:Open()
	else
		self.win_view:Open()
	end
end

function KuafuHonorhallWGCtrl:EnterXiuLuoTower()
	--if self.data:IsKuaFu() then
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_HONORHALLS) -- 进入修罗塔防跨服
	--else
		--KuafuHonorhallWGCtrl.Instance:SendCSXiuluoTowerEnterReq() -- 进入修罗塔本服
	--end
end