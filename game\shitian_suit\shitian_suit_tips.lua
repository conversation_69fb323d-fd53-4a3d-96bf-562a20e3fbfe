ShiTianSuitTipsView = ShiTianSuitTipsView or BaseClass(SafeBaseView)
function ShiTianSuitTipsView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/shitian_suit_ui_prefab", "shitian_suit_tips")
end

function ShiTianSuitTipsView:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_root"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
end

function ShiTianSuitTipsView:ReleaseCallBack()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    self.suit_seq = nil
end

function ShiTianSuitTipsView:SetDataAndOpen(suit_seq)
	self.suit_seq = suit_seq
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function ShiTianSuitTipsView:OpenCallBack()
    ShiTianSuitWGCtrl.Instance:SendShiTianRequest(SHITIAN_SUIT_OPERATE_TYPE.INFO)
end

function ShiTianSuitTipsView:OnFlush()
    if not self.suit_seq then
        return
    end

    local bundle_title, asset_title = ResPath.GetF2RawImagesPNG("a2_lhtz_big_title_" .. self.suit_seq)
    self.node_list.title_bg.raw_image:LoadSprite(bundle_title, asset_title)

    local info = ShiTianSuitWGData.Instance:GetSuitPartInfo(self.suit_seq)
    local reward_seq = info and info.reward_seq or 0
    for i = 0, 3 do
        self.node_list["suit_active_flag_" .. i]:SetActive(false)
    end

    if reward_seq >= 0 then
        for i = 0, reward_seq do
            self.node_list["suit_active_flag_" .. i]:SetActive(true)
        end
    end

    self:FlushModel()
end

function ShiTianSuitTipsView:FlushModel()
    if not self.suit_seq then
        return
    end

    local model_item_list = ShiTianSuitWGData.Instance:GetRewardModelAllItemList(self.suit_seq)
    if model_item_list then
        local display_data = {}
        display_data.model_item_id_list = model_item_list
        display_data.render_type = 0
        display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

        self.model_display:SetData(display_data)
    end
end