function EquipmentView:InitImperialSpiritStrengthView()
	self.ist_select_equip_part = -1
	self.ist_select_hole = -1
	self.ist_select_hole_data = {}
	self.ist_select_equip_data = {}

	if not self.imperial_spirit_strength_list then
		self.imperial_spirit_strength_list = AsyncListView.New(EquipYuLingStrengthItemRender, self.node_list.imperial_spirit_strength_list)
		self.imperial_spirit_strength_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectImperialSpiritStrengthHandler, self))
	end

	if not self.imperial_spirit_strength_up_list then
		self.imperial_spirit_strength_up_list = AsyncListView.New(EquipYuLingStrengthUpItemRender, self.node_list.imperial_spirit_strength_up_list)
		self.imperial_spirit_strength_up_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectImperialSpiritStrengthItemHandler, self))
	end

	if not self.imperial_spirit_strength_item then
		self.imperial_spirit_strength_item = ItemCell.New(self.node_list.imperial_spirit_strength_item_cost_pos)
	end

	XUI.AddClickEventListener(self.node_list.btn_imperial_spirit_strength_upgrade, BindTool.Bind(self.OnClickImpiritStrengthUpGrade, self))
end

function EquipmentView:DeleteImperialSpiritStrengthView()
	if self.imperial_spirit_strength_list then
		self.imperial_spirit_strength_list:DeleteMe()
		self.imperial_spirit_strength_list = nil
	end

	if self.imperial_spirit_strength_up_list then
		self.imperial_spirit_strength_up_list:DeleteMe()
		self.imperial_spirit_strength_up_list = nil
	end

	if self.imperial_spirit_strength_item then
		self.imperial_spirit_strength_item:DeleteMe()
		self.imperial_spirit_strength_item = nil
	end
end

function EquipmentView:ShowIndexImperialSpiritStrengthView()
	self.imperial_spirit_strength_select = false
end

function EquipmentView:FlushEquipImperialSpiritStrengthView(param_t)
	local data_list = EquipmentWGData.Instance:GetQingHuaImperialSpiritList()
	local jump_index = EquipmentWGData.Instance:GetEquipYuLingJumpIndex(self.ist_select_equip_part)
	self.imperial_spirit_strength_list:SetDataList(data_list)
	self.imperial_spirit_strength_list:JumpToIndex(jump_index)

	self:FlushImperialSpiritStrengthMid()
	self:FlushImperialSpiritStrengthRight()
end

function EquipmentView:OnSelectImperialSpiritStrengthHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if item.data.index == self.ist_select_equip_part then
		return
	end

	self.ist_select_equip_data = item.data
	self.ist_select_equip_part = item.data.index
	self.ist_select_hole = -1

	self:FlushImperialSpiritStrengthMid()
end

function EquipmentView:FlushImperialSpiritStrengthMid()
	--local target_data = self.ist_select_equip_data and self.ist_select_equip_data.yuling and self.ist_select_equip_data.yuling.active_per_data or {}
	local target_data = EquipmentWGData.Instance:GetEquipActivePerData(self.ist_select_equip_part)

	if IsEmptyTable(target_data) then
		self.ist_select_hole = -1
		self.ist_select_hole_data = {}
	end

	local no_data = IsEmptyTable(target_data)
	self.node_list.imperial_spirit_strength_up_list_nodata:SetActive(no_data)
	self.imperial_spirit_strength_up_list:SetDataList(target_data)

	if no_data then
		self:FlushImperialSpiritStrengthRight()
	else
		local jump_index = EquipmentWGData.Instance:GetEquipYuLingStrengthCellJumpIndex(self.ist_select_equip_part, self.ist_select_hole)
		self.imperial_spirit_strength_up_list:JumpToIndex(jump_index)

		-- local jump_index = EquipmentWGData.Instance:GetEquipYuLingStrengthCellJump(self.ist_select_equip_part)
		-- need_jump_cell = need_jump_cell or false
		-- if need_jump_cell or self.ist_select_hole < 0 or jump_index > 0 then
		-- 	jump_index = jump_index > 0 and jump_index or 1
		-- 	self.imperial_spirit_strength_up_list:JumpToIndex(jump_index)
		-- end
	end
end

function EquipmentView:FlushImperialSpiritStrengthRight()
	local no_per_data =  self.ist_select_hole < 0 and IsEmptyTable(self.ist_select_hole_datalist)
	self.node_list.imperial_spirit_strength_cur_level:SetActive(not no_per_data)
	self.node_list.imperial_spirit_strength_next_level:SetActive(not no_per_data)

	if not no_per_data then
		self.node_list.imperial_spirit_strength_cur_level.text.text = string.format(Language.EquipmentImperialSpirit.CurrentImperialSpiritLevel, self.ist_select_hole_data.name)
		local add_per_weight = EquipmentWGData.Instance:GetImperialSpiritForgeAddPerWeight(self.ist_select_equip_part, self.ist_select_hole)
		self.node_list.imperial_spirit_strength_next_level.text.text = string.format(Language.EquipmentImperialSpirit.CurrentImperialSpiritDivineSealAdd, add_per_weight)
		
		local hole_data = EquipmentWGData.Instance:GetImperialSpiritHoleCfg(self.ist_select_equip_part, self.ist_select_hole)
		local new_hole_info = EquipmentWGData.Instance:GetYuLingHoleData(self.ist_select_equip_part, self.ist_select_hole)
		-- local is_max_level = self.ist_select_hole_data.add_per >= self.ist_select_hole_data.max_per
		local is_max_level = new_hole_info.add_per >= new_hole_info.max_per
		self.node_list.imperial_spirit_strength_max_state:SetActive(is_max_level)
		self.node_list.imperial_spirit_strength_item_cost_pos:SetActive(not is_max_level)
		self.node_list.imperial_spirit_strength_bangyu:SetActive(not is_max_level)
		self.node_list.btn_imperial_spirit_strength_upgrade:SetActive(not is_max_level)

		if not is_max_level then
			local forget_data = EquipmentWGData.Instance:GetImperialSpiritForgeCfg(self.ist_select_equip_part, self.ist_select_hole)
			local cost_item_id = forget_data.cost_item_id
			local cost_item_num = forget_data.cost_item_num
			local cost_coin = forget_data.cost_coin
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

			self.imperial_spirit_strength_item:SetFlushCallBack(function ()
				local enough = item_num >= cost_item_num
				local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
				self.imperial_spirit_strength_item:SetRightBottomColorText(right_text)
				self.imperial_spirit_strength_item:SetRightBottomTextVisible(true)
			end)
	
			self.imperial_spirit_strength_item:SetData({item_id = cost_item_id, num = cost_item_num, is_bind = 0})

			local color_str = ""
			local role_bind_gold = RoleWGData.Instance.role_info.bind_gold or 0
			local coin_str_color = cost_coin <= role_bind_gold and COLOR3B.GREEN or COLOR3B.PINK
			local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_bind_gold), coin_str_color)
			color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(cost_coin)
			self.node_list.imperial_spirit_strength_bangyu_desc.text.text = color_str
		end
	else
		self.node_list.imperial_spirit_strength_max_state:SetActive(false)
		self.node_list.imperial_spirit_strength_item_cost_pos:SetActive(false)
		self.node_list.imperial_spirit_strength_bangyu:SetActive(false)
		self.node_list.btn_imperial_spirit_strength_upgrade:SetActive(false)
	end
end

function EquipmentView:OnSelectImperialSpiritStrengthItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

	self.ist_select_hole = item.data.hole
	self.ist_select_hole_data = item.data
	self:FlushImperialSpiritStrengthRight()
end

function EquipmentView:OnClickImpiritStrengthUpGrade()
	if IsEmptyTable(self.ist_select_equip_data) or IsEmptyTable(self.ist_select_hole_data) or self.ist_select_hole < 0 then
		return	
	end

	local cfg = EquipmentWGData.Instance:GetImperialSpiritForgeCfg(self.ist_select_equip_part, self.ist_select_hole)
	local is_max_per = self.ist_select_hole_data.add_per >= self.ist_select_hole_data.max_per

	if not is_max_per then
		local role_bind_gold = RoleWGData.Instance.role_info.bind_gold or 0
	    local gold = RoleWGData.Instance.role_info.gold or 0
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)

		local item_enough = item_num >= cfg.cost_item_num
		local bind_gold_enough = role_bind_gold >= cfg.cost_coin
		local all_gold_enough = (gold + role_bind_gold) >= cfg.cost_coin

		local function ok_func()
			EquipmentWGCtrl.Instance:SendEquipYuLingOperate(EQUIP_YULING_OPERATE_TYPE.EQUIP_YULING_OPERATE_TYPE_FORGE, self.ist_select_equip_part, self.ist_select_hole)
		end

		if item_enough and bind_gold_enough then
			ok_func()
		elseif item_enough and not bind_gold_enough and all_gold_enough then
			TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
		elseif not item_enough then
			local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.cost_item_id)
			local name = string.format(Language.Common.ItemNoEnough, ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]))
			TipWGCtrl.Instance:ShowSystemMsg(name)
		elseif not bind_gold_enough and not all_gold_enough then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Shop.MoneyDes2)
		end
	end
end

EquipYuLingStrengthItemRender = EquipYuLingStrengthItemRender or BaseClass(BaseRender)
function EquipYuLingStrengthItemRender:__init()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.imperial_spirit_list_item_item)
		self.item_cell:SetItemTipFrom(ItemTip.FROM_EQUIPMENT_YULING)
		self.item_cell:SetIsShowTips(false)
	end
end

function EquipYuLingStrengthItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipYuLingStrengthItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local remind = EquipmentWGData.Instance:GetImperialSpiritStrengthRemind(self.data.index)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		self.node_list.imperial_spirit_list_item_name.text.text = item_name
	end
	self.node_list.imperial_spirit_list_item_remind:SetActive(remind)

	local imperial_sprit_data = EquipmentWGData.Instance:GetYuLingDataByEquipIndex(self.data.index)
	local num = #EquipmentWGData.Instance:GetEquipActivePerData(self.data.index)
	local show_num = num > 9 and 9 or num

	for i = 0, 9 do
		if i <= show_num then
			local item_data = imperial_sprit_data[i] or {}
			if not IsEmptyTable(item_data) and item_data.is_unlock == 1 then
				local asset_name = "a2_fl_bs_" .. item_data.show_icon
				local bundel, asset = ResPath.GetEquipmentIcon(asset_name)
				self.node_list["equip_imperial_spirit_list_item_icon_" .. i].image:LoadSprite(bundel, asset, function()
					self.node_list["equip_imperial_spirit_list_item_icon_" .. i].image:SetNativeSize()
				end)
	
				self.node_list["equip_imperial_spirit_list_item_suo_" .. i]:SetActive(false)
				self.node_list["equip_imperial_spirit_list_item_icon_" .. i]:SetActive(true)
			else
				self.node_list["equip_imperial_spirit_list_item_icon_" .. i]:SetActive(false)
				self.node_list["equip_imperial_spirit_list_item_suo_" .. i]:SetActive(true)
			end
			self.node_list["equip_yuling_item_" .. i]:SetActive(true)
		else
			self.node_list["equip_yuling_item_" .. i]:SetActive(false)
		end
	end
end

function EquipYuLingStrengthItemRender:OnSelectChange(is_select)
	self.node_list["imperial_spirit_list_item_bg"]:SetActive(not is_select)
	self.node_list["imperial_spirit_list_item_bg_hl"]:SetActive(is_select)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		self.node_list.hl_imperial_spirit_list_item_name.text.text = item_name
	end
end

EquipYuLingStrengthUpItemRender = EquipYuLingStrengthUpItemRender or BaseClass(BaseRender)
function EquipYuLingStrengthUpItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local remind = EquipmentWGData.Instance:GetImperialSpiritStrengthCellRemind(self.data.equip_index, self.data.hole)
	self.node_list.remind:SetActive(remind)

	local forget_data = EquipmentWGData.Instance:GetImperialSpiritForgeCfg(self.data.equip_index, self.data.hole)
	local hole_data = EquipmentWGData.Instance:GetImperialSpiritHoleCfg(self.data.equip_index, self.data.hole)
	self.node_list.lbl_name.text.text = forget_data.name

	local max_per = hole_data.max_attr_per / 100
	local per = (self.data.add_per / 100) / max_per
	self.node_list.lbl_max.text.text = string.format(Language.EquipmentImperialSpirit.UpperLimit, max_per)
	self.node_list.strength_up_slider.slider.value = per
	local strength_up_slider_text = string.format(Language.EquipmentImperialSpirit.PerWeight, self.data.add_per / 100, max_per)
	self.node_list.strength_up_slider_text.text.text = strength_up_slider_text

	local asset_name = "a2_fl_bs_" .. self.data.show_icon
	local bundel, asset = ResPath.GetEquipmentIcon(asset_name)
	self.node_list.store_icon.image:LoadSprite(bundel, asset, function()
		self.node_list.store_icon.image:SetNativeSize()
	end)
end

function EquipYuLingStrengthUpItemRender:OnSelectChange(is_select)
	--self.node_list["img9_item_bg"]:SetActive(not is_select)
	self.node_list["img9_item_bg_hl"]:SetActive(is_select)
end