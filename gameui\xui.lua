
XUI = XUI or {
	IS_PLIST = true,								-- 默认纹理加载方式

	async_ui_list = {},
	async_ui_key_list = {},
	curr_config = nil,
	curr_info_list = {},
	curr_info_index = 0,
	async_load_list = {},							-- 异步加资源列表
}

XuiTouchEventType = {
	Began = 0,
	Moved = 1,
	Ended = 2,
	Canceled = 3,
}

function XUI.Delete()
	XUI.async_ui_list = {}
	XUI.async_ui_key_list = {}
	XUI.curr_config = nil
	XUI.curr_info_list = {}
	XUI.curr_info_index = 0
	XUI.async_load_list = {}
end

-- 添加点击事件监听
-- @click_callback 点击回调
function XUI.AddClickEventListener(node, click_callback)
	-- if nil == node.button then
	-- 	node:GetOrAddComponent(typeof(UnityEngine.UI.Button))
	-- end
	if not node then
		print_error("【获取不到相关节点】")
		return
	end

	if node.button then
		node.button:AddClickListener(click_callback)
	elseif node.toggle then
		node.toggle:AddValueChangedListener(click_callback)
	end
end

--移除所有按钮点击事件
function XUI.RemoveAllListener(node)
	node.button:RemoveAllListeners()
end

-- 按钮置灰不可点  true 可点  false  不可点
function XUI.SetButtonEnabled(node, is_enable)
	if not node then
		return
	end
	
	XUI.SetGraphicGrey(node, not is_enable)
	if node.button then
		node.button.interactable = is_enable
	end
	if node.toggle then
		node.toggle.interactable = is_enable
	end
end

function XUI.ImageSetNativeSize(node)
	if not node then
		return
	end

	node.image:SetNativeSize()
end

local TypeUIGrey = typeof(UIGrey)
local greyMat = nil
-- 图片置灰 --tue:置灰 false：非置灰
function XUI.SetGraphicGrey(node, is_grey)
	if not node then
		return
	end

	local uiGrey = node:GetComponent(TypeUIGrey)
	if nil == uiGrey and is_grey then
		uiGrey = node:GetOrAddComponent(TypeUIGrey)

		if nil == greyMat then
			greyMat = ResPoolMgr:TryGetMaterial("misc/material", "UI_NormalGrey")
		end
	end

	if nil ~= uiGrey then
		uiGrey:SetIsGrey(is_grey, greyMat)
	end
end

function XUI.IsGraphicGrey(node)
	if not node then
		return false
	end

	local uiGrey = node:GetComponent(TypeUIGrey)
	if nil == uiGrey then
		return false
	end

	return uiGrey:GetIsGrey()
end


----------------------------------------------------------------------------
--更新头像：暂时时使用这个  以后还要通过role_id来从网络下载头像
function XUI.UpdateRoleHead(default_node, custom_node, role_id, role_sex, role_prof, is_gray, is_small, is_big_head)
	if not default_node or not custom_node then
		return
	end

	if is_gray ~= nil then
		XUI.SetGraphicGrey(default_node, is_gray)
		XUI.SetGraphicGrey(custom_node, is_gray)
    end

    local index = AvatarManager.Instance:GetAvatarKey(role_id, true)
	if index > GameEnum.CUSTOM_HEAD_ICON then
		AvatarManager.Instance:SetAvatar(role_id, custom_node, default_node, role_sex, role_prof)
	else
		if role_sex == nil or (tonumber(role_sex) ~= GameEnum.MALE and tonumber(role_sex) ~= GameEnum.FEMALE) then
			-- print_error(" role_sex = ",role_sex, role_id, "UpdateRoleHead加载头像数据有问题")
			return
		end

		local bundle, asset = RoleWGData.Instance:GetHeadIconResByIdnex(index, role_sex, role_prof, is_big_head)
		AvatarManager.Instance:CancelSetAvatar(custom_node)
		default_node.image:LoadSprite(bundle, asset, function()
			if not is_small then
				default_node.image:SetNativeSize()
			end

			default_node:SetActive(true)
			custom_node:SetActive(false)
		end)
	end
end

function XUI.UpdateMainRoleHead(use_index_big, use_index_small, default_node, custom_node, role_id, role_sex, role_prof, is_gray, is_small, is_big_head)
    if is_gray ~= nil then
		XUI.SetGraphicGrey(default_node, is_gray)
		XUI.SetGraphicGrey(custom_node, is_gray)
    end

	if use_index_big > GameEnum.CUSTOM_HEAD_ICON then
		if 0 == AvatarManager.Instance:GetAvatarKey(role_id, true) then
			AvatarManager.Instance:SetAvatarKey(role_id, use_index_big, use_index_small)
		end
		AvatarManager.Instance:SetAvatar(role_id, custom_node, default_node, role_sex, role_prof)
	else
		if role_sex == nil or (tonumber(role_sex) ~= GameEnum.MALE and tonumber(role_sex) ~= GameEnum.FEMALE) then
			print_error(" role_sex = ",role_sex, role_id, "UpdateMainRoleHead加载头像数据有问题")
		end

		local bundle, asset = RoleWGData.Instance:GetHeadIconResByIdnex(use_index_big, role_sex, role_prof, is_big_head)
		default_node.image:LoadSprite(bundle, asset, function()
			if not is_small then
				default_node.image:SetNativeSize()
			end

			default_node:SetActive(true)
			custom_node:SetActive(false)
		end)
	end
end

function XUI.ChangeGradientColor(node, color1, color2)
	local gradient = node:GetOrAddComponent(typeof(UIGradient))
	gradient.Color1 = Str2C3b(color1)
	gradient.Color2 = Str2C3b(color2)
end

-- 技能根据尺寸显示背景底
function XUI.SetSkillIcon(skill_bg_node, skill_icon_node, icon_id, other_res)
	if not skill_bg_node or not skill_bg_node.image
	or not skill_icon_node or not skill_icon_node.image
	or not skill_icon_node.rect then
		return
	end

	local res_fun = other_res or ResPath.GetSkillIconById
	local bundle, asset = res_fun(icon_id)
	--[[ 暂无技能不同形状底区分
	skill_icon_node.image:LoadSprite(bundle, asset, function()
		skill_icon_node.image:SetNativeSize()
		local size_delta = RectTransform.GetSizeDelta(skill_icon_node.rect)
		local bg_b, bg_a
		if size_delta and size_delta.x > 76 then
			bg_b, bg_a = ResPath.GetCommonImages("a2_jn_jnkuang")
		else
			bg_b, bg_a = ResPath.GetCommonImages("a1_ty_kuang_jineng")
		end

		skill_bg_node.image:LoadSprite(bg_b, bg_a, function()
			skill_bg_node.image:SetNativeSize()
		end)
	end)
	]]

	skill_icon_node.image:LoadSprite(bundle, asset, function()
		skill_icon_node.image:SetNativeSize()
	end)

	local bg_b, bg_a = ResPath.GetCommonImages("a3_ty_skill_bg")
	skill_bg_node.image:LoadSprite(bg_b, bg_a, function()
		skill_bg_node.image:SetNativeSize()
	end)
end

-- 加载图片.
function XUI.SetNodeImage(node, bundle, asset)
	if node then
		local node_img
		if node.image then
			node_img = node.image
		elseif node.raw_image then
			node_img = node.raw_image
		end

		node_img:LoadSprite(bundle, asset, function()
			node_img:SetNativeSize()
		end)
	end
end