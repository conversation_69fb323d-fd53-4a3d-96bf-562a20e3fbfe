
require("game/new_festival_activity/new_festival_boss_drop/new_festival_boss_drop_wg_data")

NewFestivalBossDropWGCtrl = NewFestivalBossDropWGCtrl or BaseClass(BaseWGCtrl)

function NewFestivalBossDropWGCtrl:__init()
	if NewFestivalBossDropWGCtrl.Instance then
		error("[NewFestivalBossDropWGCtrl]:Attempt to create singleton twice!")
	end

    NewFestivalBossDropWGCtrl.Instance = self

    self.data = NewFestivalBossDropWGData.New()

    self:RegisterAllProtocols()
end

function NewFestivalBossDropWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    NewFestivalBossDropWGCtrl.Instance = nil
end

function NewFestivalBossDropWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOAMoreDroppingInfo, "OnSCOAMoreDroppingInfo")
end

function NewFestivalBossDropWGCtrl:OnSCOAMoreDroppingInfo(protocol)
    self.data:SetNewJRBossDropAllInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2352)
end