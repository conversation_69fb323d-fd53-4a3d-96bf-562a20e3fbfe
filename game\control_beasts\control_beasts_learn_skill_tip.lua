ControlBeastsLearnSkillTip = ControlBeastsLearnSkillTip or BaseClass(SafeBaseView)

function ControlBeastsLearnSkillTip:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(42, 0),sizeDelta = Vector2(706, 488)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_learn_skill")
	self:SetMaskBg(true)

	self.tips_data = nil
end

function ControlBeastsLearnSkillTip:__delete()
	self.tips_data = nil
end

function ControlBeastsLearnSkillTip:ReleaseCallBack()
	self.tips_data = nil

	if self.skill_book_item then
		self.skill_book_item:DeleteMe()
		self.skill_book_item = nil
	end

	if self.skil_book_list then
		self.skil_book_list:DeleteMe()
		self.skil_book_list = nil
	end

	if self.flair_skill_grid and #self.flair_skill_grid > 0 then
		for _, flair_skill_cell in ipairs(self.flair_skill_grid) do
			flair_skill_cell:DeleteMe()
			flair_skill_cell = nil
		end

		self.flair_skill_grid = nil
	end

	self.is_force_refresh = false
	self.cur_skill_book_data = nil
	self.cur_skill_book_index = nil
	self.jump_select_skill_seq = nil
end

function ControlBeastsLearnSkillTip:CloseCallBack()
	if self.flair_skill_grid and #self.flair_skill_grid > 0 then
		for _, flair_skill_cell in ipairs(self.flair_skill_grid) do
			flair_skill_cell:ResetOldId()
		end
	end
end

function ControlBeastsLearnSkillTip:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName8

    if not self.skill_book_item then
		self.skill_book_item = ItemCell.New(self.node_list.item_cell)
	end

	if not self.skil_book_list then
		self.skil_book_list = AsyncListView.New(BeststsSkillBookItemRender, self.node_list.skil_book_list_view)
		self.skil_book_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSkillBookCB, self))
	end

    -- 拥有技能
    if self.flair_skill_grid == nil then
        self.flair_skill_grid = {}
        for i = 1, 9 do
            local attr_obj = self.node_list.skill_grid:FindObj(string.format("skill_0%d", i))
            if attr_obj then
                local cell = BeststsSkillItemRender.New(attr_obj)
                cell:SetIndex(i)
				cell:SetNotAdd()
                cell:SetClickCallBack(BindTool.Bind1(self.OnClickSkill, self))
                self.flair_skill_grid[i] = cell
            end
        end
    end

	XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind2(self.OnClinkOkHandler, self))
	-- XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind2(self.Close, self))
end


function ControlBeastsLearnSkillTip:SetTipsData(tips_data)
	self.tips_data = tips_data or self.tips_data
end


function ControlBeastsLearnSkillTip:OnSelectSkillBookCB(beasts_item, cell_index, is_default, is_click)
    if nil == beasts_item or nil == beasts_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = beasts_item:GetIndex()
	if self.cur_skill_book_index == cell_index and (not self.is_force_refresh) then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

    self.is_force_refresh = false
	self.cur_skill_book_data = beasts_item.data
	self.cur_skill_book_index = cell_index

	self:FlushSkillBookMessage()
end

-- 点击技能
function ControlBeastsLearnSkillTip:OnClickSkill(beststs_skill_item)
    if beststs_skill_item.data == nil then
		return
	end

    if beststs_skill_item.data.skill_id ~= -1 then
        local cfg = ControlBeastsWGData.Instance:GetSkillDataBySkillId(beststs_skill_item.data.skill_id)
        if cfg then
            local show_data = {
                icon = cfg.skill_icon,
                top_text = cfg.skill_name,
				capability = cfg.capability_inc,
                body_text = cfg.skill_desc,
                skill_level = cfg.skill_rate or 1,
                x = 0,
                y = -120,
                set_pos = true,
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

function ControlBeastsLearnSkillTip:OnFlush()
	if not self.tips_data then
		return
	end

	local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.tips_data.bag_id)
	if beast_data and beast_data.server_data then
		local server_data = beast_data.server_data

		if server_data.skill_ids then
			local is_record_red = false
			for index, bag_flair_cell in ipairs(self.flair_skill_grid) do
				if bag_flair_cell and server_data.skill_ids[index] then
					local data = {}
					data.skill_id = server_data.skill_ids[index]

					-- 只保持前一个存在红点就好
					if beast_data.is_can_learn_skill and (not is_record_red) and data.skill_id == -1 then
						data.remind = true
						is_record_red = true
					else
						data.remind = false
					end
					bag_flair_cell:SetData(data)
				end
			end
		end
	end

	if beast_data then
		self.node_list.remind:CustomSetActive(beast_data.is_can_learn_skill)
	end

	self:FlushSkillBookList()
end

-- 刷新技能书列表
function ControlBeastsLearnSkillTip:FlushSkillBookList()
	local skill_book_list_data = ControlBeastsWGData.Instance:GetAllSkillBookDataList()
	self.node_list.no_skillbook:CustomSetActive(not (skill_book_list_data and #skill_book_list_data > 0))
	self.node_list.item_root:CustomSetActive(skill_book_list_data and #skill_book_list_data > 0)
	self.node_list.item_list_select:CustomSetActive(skill_book_list_data and #skill_book_list_data > 0)
	
	if skill_book_list_data and #skill_book_list_data > 0 then
		if self.jump_select_skill_seq == nil then
			-- 这里筛选出一个优先选择的
			self.jump_select_skill_seq = self:GetOneSkillBookList(skill_book_list_data)
		end
	
		if self.skil_book_list ~= nil then
			self.skil_book_list:SetDataList(skill_book_list_data)
			if self.jump_select_skill_seq then
				if self.jump_select_skill_seq == 0 then
					self.skil_book_list:JumpToIndex(1, 2)
				elseif self.jump_select_skill_seq == 100 then      -- 这里是协议刷新(为了过滤相等id返回)
					local temp_index = 1
					if self.cur_skill_book_index then
						temp_index = self.cur_skill_book_index
					end

					if not skill_book_list_data[temp_index] then
						temp_index = 1
					end

					self.is_force_refresh = true
					self.skil_book_list:JumpToIndex(temp_index, 5)
				elseif self.jump_select_skill_seq ~= 100 then
					self.skil_book_list:JumpToIndex(self.jump_select_skill_seq, 2)
					self.jump_select_skill_seq = 100
				end
			end
		end
	else
		self.cur_skill_book_data = nil
		self.cur_skill_book_index = nil
	end
end

--- 筛选出一个当前选择的下标
function ControlBeastsLearnSkillTip:GetOneSkillBookList(skill_book_list_data)
	if (not self.cur_skill_book_data) or (not self.cur_skill_book_index) then
		return 1
	end

	local new_data = skill_book_list_data[self.cur_skill_book_index]
	if new_data then
		return 1
	end

	if self.cur_skill_book_data.skill_id == new_data.skill_id then
		return self.cur_skill_book_index
	else
		return 100
	end
end

-- 刷新中间文本
function ControlBeastsLearnSkillTip:FlushSkillBookMessage()
	if not self.cur_skill_book_data then
		return 
	end

	local skill_cfg = ControlBeastsWGData.Instance:GetSkillDataBySkillId(self.cur_skill_book_data.skill_id)
	if skill_cfg then
		self.node_list.item_name.text.text = skill_cfg.skill_name
		self.node_list.item_desc_1.text.text = self:GetDescForCfg(skill_cfg.attr_id1, skill_cfg.attr_value1)
		self.node_list.item_desc_2.text.text = self:GetDescForCfg(skill_cfg.attr_id2, skill_cfg.attr_value2)
	end

	self.skill_book_item:SetData({item_id = self.cur_skill_book_data.item_id})---这里需要其他的东西在加，看策划需求
end


function ControlBeastsLearnSkillTip:GetDescForCfg(attr_id, attr_value)
	local str = ""
	if (not attr_id) or (not attr_value) then
		return str
	end

	if attr_id == 0 or attr_value == 0 then
		return str
	end

	local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
	local per_desc = is_per and "%" or ""
	local value_str = is_per and attr_value / 100 or attr_value
	local attr_name = EquipmentWGData.Instance:GetAttrName(attr_id, false, false)
	str = string.format("%s+%s%s", attr_name, value_str, per_desc)

	return str
end

-- 升级成功或突破成功特效
function ControlBeastsLearnSkillTip:BeastOperateFinalEffect(ui_effect_type)
	TipWGCtrl.Instance:ShowEffect({effect_type = ui_effect_type,
						is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["operate_effect_root"]})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

----------------------------------------------------------------------------------------------------------
-- 打书
function ControlBeastsLearnSkillTip:OnClinkOkHandler()
	if (not self.tips_data) or (not self.cur_skill_book_data) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip11)
		return
	end

	ControlBeastsWGCtrl.Instance:SendOperateTypeAddSkill(self.tips_data.bag_id, self.cur_skill_book_data.skill_id)
end

------------------------------------------------------------------------------------

----------------------------------灵兽技能书item-----------------------
BeststsSkillBookItemRender = BeststsSkillBookItemRender or BaseClass(BaseRender)
function BeststsSkillBookItemRender:LoadCallBack()
    if not self.skill_book_item then
		self.skill_book_item = ItemCell.New(self.node_list.ph_item)
	end
end

function BeststsSkillBookItemRender:__delete()
	if self.skill_book_item then
		self.skill_book_item:DeleteMe()
		self.skill_book_item = nil
	end
end

function BeststsSkillBookItemRender:OnFlush()
    if not self.data then return end

	local skill_cfg = ControlBeastsWGData.Instance:GetSkillDataBySkillId(self.data.skill_id)
	if skill_cfg then
		self.node_list.lbl_name.text.text = skill_cfg.skill_name
		self.node_list.attr_str_1.text.text = self:GetDescForCfg(skill_cfg.attr_id1, skill_cfg.attr_value1)
		self.node_list.attr_str_2.text.text = self:GetDescForCfg(skill_cfg.attr_id2, skill_cfg.attr_value2)
	end

	self.skill_book_item:SetData({item_id = self.data.item_id})---这里需要其他的东西在加，看策划需求
	self.skill_book_item:SetRightBottomTextVisible(true)
	self.skill_book_item:SetRightBottomColorText(self.data.item_num)
end

function BeststsSkillBookItemRender:GetDescForCfg(attr_id, attr_value)
	local str = ""
	if (not attr_id) or (not attr_value) then
		return str
	end

	if attr_id == 0 or attr_value == 0 then
		return str
	end

	local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
	local per_desc = is_per and "%" or ""
	local value_str = is_per and attr_value / 100 or attr_value
	local attr_name = EquipmentWGData.Instance:GetAttrName(attr_id, false, false)
	str = string.format("%s+%s%s", attr_name, value_str, per_desc)

	return str
end

function BeststsSkillBookItemRender:OnSelectChange(is_select)
	self.node_list.normal_bg:CustomSetActive(not is_select)
	self.node_list.select_bg:CustomSetActive(is_select)
end


