CheapShopPurchaseView = CheapShopPurchaseView or BaseClass(SafeBaseView)

function CheapShopPurchaseView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/cheap_shop_purchase_ui_prefab", "layout_cheap_shop_view")
end

function CheapShopPurchaseView:OpenCallBack()
    CheapShopPurchaseWGCtrl.Instance:ReqActivityLimitShopInfo(OA_LIMIT_RMB_SHOP_OPERATE_TYPE.INFO)
end


function CheapShopPurchaseView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_all_buy"], BindTool.Bind(self.OnClickAllBuy, self))

    if self.cheap_buy_list == nil then
        self.cheap_buy_list = AsyncListView.New(CheapShopBuyRender, self.node_list["cheap_buy_list"])
    end
end

function CheapShopPurchaseView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("cheap_shop_purchase_time") then
        CountDownManager.Instance:RemoveCountDown("cheap_shop_purchase_time")
    end

    if self.cheap_buy_list then
        self.cheap_buy_list:DeleteMe()
        self.cheap_buy_list = nil
    end
end

function CheapShopPurchaseView:OnFlush(param_t, index)
    local data_list, all_buy_cfg = CheapShopPurchaseWGData.Instance:GetGradeRewardList()
    if not IsEmptyTable(data_list) then
        self.cheap_buy_list:SetDataList(data_list)
    end

    if not IsEmptyTable(all_buy_cfg) then
        local price = RoleWGData.GetPayMoneyStr(all_buy_cfg.rmb_price, all_buy_cfg.rmb_type, all_buy_cfg.rmb_seq)
        local discount = all_buy_cfg.discount
        local original_price = RoleWGData.GetPayMoneyStr(math.ceil(all_buy_cfg.rmb_price / (discount / 10)))
        self.node_list["discount_text"].text.text = string.format(Language.CheapShopPurchase.SpecialDiscount, discount)
        self.node_list["all_price"].text.text = string.format(Language.CheapShopPurchase.AllPrice, price)
        self.node_list["original_price"].text.text = string.format(Language.CheapShopPurchase.OriginalPrice, original_price)
    end

    local is_can_all_buy = self:GetIsCanAllBuy()
    self.node_list.btn_all_buy:SetActive(is_can_all_buy)
    self:FlushTimeCount()
end

function CheapShopPurchaseView:FlushTimeCount()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("cheap_shop_purchase_time") then
            CountDownManager.Instance:RemoveCountDown("cheap_shop_purchase_time")
        end

        CountDownManager.Instance:AddCountDown("cheap_shop_purchase_time",
            BindTool.Bind(self.FinalUpdateTimeCallBack, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function CheapShopPurchaseView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["act_time"].text.text = string.format(Language.CheapShopPurchase.ActivityTime, time_str) 
end

function CheapShopPurchaseView:OnComplete()
    self.node_list.act_time.text.text = ""
end

function CheapShopPurchaseView:GetIsCanAllBuy()
    local data_list, all_buy_cfg = CheapShopPurchaseWGData.Instance:GetGradeRewardList()
    if IsEmptyTable(data_list) or IsEmptyTable(all_buy_cfg) then
        return false, nil
    end

    for k, v in pairs(data_list) do
        if v.cur_buy_times > 0 then --有购买过物品
            return false, nil
        end
    end

    return true, all_buy_cfg
end

function CheapShopPurchaseView:OnClickAllBuy()
    local is_can_all_buy, all_buy_cfg = self:GetIsCanAllBuy()
    if is_can_all_buy and all_buy_cfg then
        RechargeWGCtrl.Instance:Recharge(all_buy_cfg.rmb_price, all_buy_cfg.rmb_type, all_buy_cfg.rmb_seq)
    end
end

CheapShopBuyRender = CheapShopBuyRender or BaseClass(BaseRender)
function CheapShopBuyRender:__init()
    if not self.big_reward_cell then
        self.big_reward_cell = ItemCell.New(self.node_list["big_reward_pos"])
    end

    self:CreateRewardCell()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))
end

function CheapShopBuyRender:__delete()
    if self.big_reward_cell then
        self.big_reward_cell:DeleteMe()
        self.big_reward_cell = nil
    end

    if nil ~= self.reward_cell_list then
		self.reward_cell_list:DeleteMe()
		self.reward_cell_list = nil
    end
end

function CheapShopBuyRender:CreateRewardCell()
    if not self.reward_cell_list then
        self.reward_cell_list = AsyncListView.New(ItemCell, self.node_list["reward_cell_list"])
        self.reward_cell_list:SetStartZeroIndex(false)
	end
end

function CheapShopBuyRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list["bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_xdms_di_" .. self.data.show_icon_ground))
    self.node_list.name.text.text = self.data.name

    self.big_reward_cell:SetData(self.data.reward_item[0])
    local price = RoleWGData.GetPayMoneyStr(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list["buy_text"].text.text = price

    local remain_times = self.data.buy_times - self.data.cur_buy_times
    local have_times = remain_times > 0
    local color = have_times and self.data.count_color or COLOR3B.L_RED
    local str = ToColorStr(remain_times .. "/" .. self.data.buy_times, color)
    self.node_list["limit_buy"].text.text = string.format(Language.CheapShopPurchase.BuyTimesStr, str)

    self.node_list.btn_buy:SetActive(have_times)
    self.node_list.limit_buy:SetActive(have_times)
    self.node_list.is_buy:SetActive(not have_times)

    self.reward_cell_list:SetDataList(self.data.reward_item)
end

function CheapShopBuyRender:OnClickBuy()
    if self.data == nil then
        return
    end

    local remain_times = self.data.buy_times - self.data.cur_buy_times
    if remain_times > 0 then
        RechargeWGCtrl.Instance:Recharge(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    end
end

