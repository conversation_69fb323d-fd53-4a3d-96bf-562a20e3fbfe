require("game/story/fight_robot_mgr")
require("game/story/robert")
require("game/story/robert_ai/base_robert_ai")
require("game/story/robert_ai/active_attack_robert_ai")

RobertManager = RobertManager or BaseClass()
function RobertManager:__init()
	if <PERSON><PERSON><PERSON><PERSON>.Instance ~= nil then
		ErrorLog("[<PERSON><PERSON><PERSON>] attempt to create singleton twice!")
		return
	end
	RobertManager.Instance = self

	self.fight_robot_mgr = FightRobertManager.New()

	self.robert_role_cfg = ConfigManager.Instance:GetAutoConfig("story_auto")["robert_role"]
	self.robert_monster_cfg = ConfigManager.Instance:GetAutoConfig("story_auto")["robert_monster"]

	self.obj_id_inc = 100000
	self.robert_dic = {}
	self.is_playing = false

	self.end_die_robert_list = {}	-- 一场战斗结束需哪些机器人死亡
	self.fight_end_callback = nil	-- 一场战斗结束的回调
	self.fight_id_inc = 0			-- 发生的第几场战斗
	self.is_fighting = false 		-- 战斗是否在进行中
	self.robert_hurt_list = {}
end

function Robert<PERSON>anager:__delete()
	for _, v in pairs(self.robert_dic) do
		v:DeleteMe()
	end
	self.robert_dic = {}

	self.fight_robot_mgr:DeleteMe()

	Runner.Instance:RemoveRunObj(self)
	RobertManager.Instance = nil
end

function RobertManager:Clear()
	print_log("RobertManager:Clear")
	self.end_die_robert_list = {}
	self.fight_end_callback = nil
	self.is_fighting = false
	self.is_pause = false
	self.fight_id_inc = 0
	self:DelAllRobert()
end

function RobertManager:Start()
	if not self.is_playing then
		self.is_playing = true
		self:Clear()
		-- self:CreateMainRoleRobert(self.robert_role_cfg[0])
		Runner.Instance:AddRunObj(self, 8)
	end
end

function RobertManager:Stop()
	if self.is_playing then
		self.is_playing = false
		self:Clear()
		Runner.Instance:RemoveRunObj(self)
	end
end

local old_guaji_type
function RobertManager:SetPause(value)
	self.is_pause = value
	if value then
		old_guaji_type = GuajiCache.guaji_type
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	elseif old_guaji_type ~= GuajiCache.guaji_type then
		GuajiWGCtrl.Instance:SetGuajiType(old_guaji_type)
	end
end

function RobertManager:IsPlaying()
	return self.is_playing
end

function RobertManager:Update(now_time, elapse_time)
	if self.is_pause then
		return
	end

	for _, v in pairs(self.robert_dic) do
		v:Update(now_time, elapse_time)
	end
end

function RobertManager:IsFighting()
	return self.is_fighting
end

-- 主角是否正在使用机器人属性，如果正在使用中，则只保存服务器发过来的属性值，在退出机器人状态时再更到最新
function RobertManager:IsMainRoleUseingRobertAttr(attr_key, attr_value)
	if not self.is_playing then
		return false
	end

	local robert = self.robert_dic[Scene.Instance:GetMainRole():GetObjId()]
	if nil == robert then
		return false
	end

	return robert:RefrehOldAttrValue(attr_key, attr_value)
end

function RobertManager:OnMainRoleCreate()
	if self.is_playing then
		self:CreateMainRoleRobert(self.robert_role_cfg[0])
	end
end

function RobertManager:GetRobertCfg(id)
	return self.robert_role_cfg[id] or self.robert_monster_cfg[id]
end

function RobertManager:CreateRobert(robert_id, just_play)
	if nil ~= self:GetRobertByRobertId(robert_id) then
		return
	end

	local robert_cfg = self.robert_role_cfg[robert_id]
	if nil ~= robert_cfg then
		self:CreateRoleRobert(robert_cfg, just_play)
	end

	robert_cfg = self.robert_monster_cfg[robert_id]
	if nil ~= robert_cfg then
		self:CreateMonsterRobert(robert_cfg, just_play)
	end
end

function RobertManager:CreateMainRoleRobert(robert_cfg)
	-- 不知道为啥要创建一个主角机器人
	self:DelRobertByRobertId(0)
	local main_role = Scene.Instance:GetMainRole()
	if nil ~= robert_cfg and main_role then
		local obj_id = main_role:GetObjId()
		self.robert_dic[obj_id] = Robert.New(Scene.Instance:GetMainRole(), robert_cfg)
	end
end

function RobertManager:CreateRoleRobert(robert_cfg, just_play)
	self.obj_id_inc = self.obj_id_inc + 1

	local role_vo = RoleVo.New()
	role_vo.role_id = self.obj_id_inc
	role_vo.obj_id = self.obj_id_inc
	role_vo.name = robert_cfg.name or ""
	role_vo.level = 500
	role_vo.sex = robert_cfg.sex
	role_vo.prof = robert_cfg.prof
	role_vo.pos_x = robert_cfg.born_x
	role_vo.pos_y = robert_cfg.born_y
	role_vo.move_speed = robert_cfg.move_speed
	role_vo.max_hp = robert_cfg.max_hp
	role_vo.hp = role_vo.max_hp
	role_vo.just_play = just_play == true
	role_vo.appearance = ProtocolStruct.RoleAppearance()
	role_vo.appearance.wuqi_id = robert_cfg.wuqi_id
	role_vo.appearance.mount_used_imageid = robert_cfg.mount_imageid
	role_vo.appearance.wing_used_imageid = robert_cfg.wing_imageid
	role_vo.name_color = 0 ~= robert_cfg.side and EvilColorList.NAME_COLOR_RED_1 or 0
	role_vo.npc_res = robert_cfg.npc_res

	if robert_cfg.model_res and robert_cfg.model_res ~= "" then
		role_vo.special_appearance = SPECIAL_APPEARANCE_TYPE.STORY_ROBERT
		role_vo.special_param = robert_cfg.model_res
	end

	local role = Scene.Instance:CreateRole(role_vo)
	if just_play then
		role:SetIsPerformer(true)
	end

	self.robert_dic[role_vo.obj_id] = Robert.New(role, robert_cfg)
end
function RobertManager:AddRoleRobert(main_role,main_role2,role_robert1,role_robert2)
	self.robert_dic[main_role.vo.obj_id] = Robert.New(main_role, role_robert1)
	self.robert_dic[main_role2.vo.obj_id] = Robert.New(main_role2, role_robert2)
	if not self.is_playing then
		self.is_playing = true
	end
	Runner.Instance:AddRunObj(self, 8)
end
function RobertManager:CreateMonsterRobert(robert_cfg, just_play)
	self.obj_id_inc = self.obj_id_inc + 1

	local monster_vo = MonsterVo.New()
	monster_vo.obj_id = self.obj_id_inc
	monster_vo.monster_id = robert_cfg.monster_id
	monster_vo.level = 500

	-- 怪物表里有对应配置就取对应的等级
	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
	if robert_cfg.monster_id and cfg and cfg[robert_cfg.monster_id] then
		monster_vo.level = cfg[robert_cfg.monster_id].level or 500
	end

	monster_vo.pos_x = robert_cfg.born_x
	monster_vo.pos_y = robert_cfg.born_y
	monster_vo.move_speed = robert_cfg.move_speed
	monster_vo.max_hp = robert_cfg.max_hp
	monster_vo.hp = monster_vo.max_hp
	monster_vo.just_play = just_play == true

	local monster = Scene.Instance:CreateMonster(monster_vo)
	if just_play then
		monster:SetIsPerformer(true)
	end
	self.robert_dic[monster_vo.obj_id] = Robert.New(monster, robert_cfg)
end

function RobertManager:DelAllRobert()
	for k, v in pairs(self.robert_dic) do
		v:DeleteMe()
	end

	self.robert_dic = {}
end

function RobertManager:DelRobert(obj_id)
	if nil ~= self.robert_dic[obj_id] then
		self.robert_dic[obj_id]:DeleteMe()
		self.robert_dic[obj_id] = nil
	end
end

function RobertManager:DelRobertByRobertId(robert_id)
	for k, v in pairs(self.robert_dic) do
		if v:GetRobertId() == robert_id then
			v:DeleteMe()
			self.robert_dic[k] = nil
			break
		end
	end
end

function RobertManager:GetRobert(obj_id)
	return self.robert_dic[obj_id]
end

function RobertManager:GetRobertByRobertId(robert_id)
	for _, v in pairs(self.robert_dic) do
		if v:GetRobertId() == robert_id then
			return v
		end
	end

	return nil
end

-- 开始一场战斗，提供战斗结束时需死亡哪些robert
function RobertManager:StartFight(end_die_robert_list, fight_end_callback)
	self.is_fighting = true
	self.fight_id_inc = self.fight_id_inc + 1
	self.end_die_robert_list = end_die_robert_list
	self.fight_end_callback = fight_end_callback
	self.robert_hurt_list = {}

	print("RobertManager, StartFight", self.fight_id_inc)
end

-- 直接结束一场战斗
function RobertManager:StopFight()
	self.end_die_robert_list = {}
	self:CheckFightEnd()
end

-- 检查战斗是否应该结束
function RobertManager:CheckFightEnd()
	if #self.end_die_robert_list > 0 then
		return
	end

	-- print("RobertManager, FightEnd", self.fight_id_inc)
	self.is_fighting = false
	-- GuajiWGCtrl.Instance:StopGuaji()
	if nil ~= self.fight_end_callback then
		self.fight_end_callback(self.fight_id_inc)
	end
end

-- 机器人移动到位置
function RobertManager:RobertMoveTo(robert_id, pos_x, pos_y)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:DoMove(pos_x, pos_y)
	end
end

-- 机器人攻击目标
function RobertManager:RobertAtkTarget(attacker_robert_id, target_robert_id)
	local target_robert = self:GetRobertByRobertId(target_robert_id)
	local attacker_robert = self:GetRobertByRobertId(attacker_robert_id)

	if nil ~= attacker_robert and nil ~= target_robert then
		attacker_robert:SetAtkTarget(target_robert)
	end
end

-- 机器人说话
function RobertManager:RobertSay(robert_id, content, say_time)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:Say(content, say_time)
	end
end

-- 改变机器人外观
function RobertManager:RobertChangeAppearance(robert_id, appearance_type, appearance_value)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:ChangeAppearance(appearance_type, appearance_value)
	end
end

-- 改变机器人称号
function RobertManager:RobertChangeTitle(robert_id, title_id)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:ChangeTitle(title_id)
	end
end

-- 机器人改变属性值
function RobertManager:RobertChangeAttrValue(robert_id, attr_key, attr_value)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:ChangeAttrValue(attr_key, attr_value)
	end
end

-- 机器人改变aoe范围
function RobertManager:RobertChangeAoeRange(robert_id, aoe_range)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:ChangeAoeRange(aoe_range)
	end
end

-- 机器人开始采集
function RobertManager:RobertStartGather(robert_id, gather_id)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:StartGather(Scene.Instance:GetSceneId(), gather_id)
	end
end

-- 机器人转向
function RobertManager:RobertRotateTo(robert_id, angle)
	local robert = self:GetRobertByRobertId(robert_id)
	if nil ~= robert then
		robert:SetLocalRotationY(angle)
	end
end

-- 机器人寻找机器人打
function RobertManager:FindEnemy(attacker_robert)
	if nil == attacker_robert or attacker_robert:IsDead() then
		return
	end

	local target_x, target_y, distance = 0, 0, 100000
	local finder_x, finder_y = attacker_robert:GetLogicPos()
	local target_robert = nil

	for _, v in pairs(self.robert_dic) do
		if v ~= attacker_robert and self:IsEnemy(attacker_robert, v) then
			target_x, target_y = v:GetLogicPos()
			local temp_distance = GameMath.GetDistance(finder_x, finder_y, target_x, target_y, false)
			if temp_distance < distance then
				target_robert = v
				distance = temp_distance
			end
		end
	end

	return target_robert
end

-- 寻找某个范围内的敌人列表
function RobertManager:FindEnemyList(attacker_robert, center_x, center_y, range, hit_count, skill_id)
	local enemy_list = {}

	if nil == attacker_robert or attacker_robert:IsDead() then
		return enemy_list
	end

	local target_x, target_y = 0, 0
	for _, v in pairs(self.robert_dic) do
		if v ~= attacker_robert and self:IsEnemy(attacker_robert, v) then
			target_x, target_y = v:GetLogicPos()
			local temp_distance = GameMath.GetDistance(center_x, center_y, target_x, target_y, true)
			if temp_distance <= range then
				table.insert(enemy_list, {v, attacker_robert, hit_count, skill_id})
			end
		end
	end

	return enemy_list
end

-- 是否是敌人
function RobertManager:IsEnemy(attacker_robert, target_robert)
	if attacker_robert == target_robert
		or nil == attacker_robert or attacker_robert:IsDead()
		or nil == target_robert or target_robert:IsDead() then

		return false
	end

	return attacker_robert:GetSide() ~= target_robert:GetSide()
end

-- 请求战斗，通过模防协议实现
function RobertManager:ReqFight(attacker_robert, hiter_robert, skill_id, skill_index)
	if attacker_robert == hiter_robert or nil == attacker_robert or nil == hiter_robert then
		return
	end
	local protocol = SCPerformSkill.New()
	protocol.character = attacker_robert:GetObjId()
	protocol.target = hiter_robert:GetObjId()
	protocol.skill = skill_id
	protocol.skill_data = skill_index or 0
	FightWGCtrl.Instance:OnPerformSkill(protocol)

	-- 部分技能要造成多次伤害(真实情况下是服务器发多次伤害，这里进行模拟)
	local hit_count = 1
	local hit_interval = 0.3
	-- if 121 == skill_id then
	-- 	hit_count = 4
	-- end

	local aoe_range = attacker_robert:GetAoeRange()
	local hiter_robert_list = {{hiter_robert, attacker_robert, hit_count, skill_id}}
	if aoe_range > 0 then
		local center_x, center_y = hiter_robert:GetLogicPos()
		hiter_robert_list = self:FindEnemyList(attacker_robert, center_x, center_y, aoe_range, hit_count, skill_id)
	end

	local function changeblood(attacker, hiter, hit_count, skill_id)
		if not self.is_playing
			or attacker:IsDeleted()
			or hiter:IsDeleted()
			or nil == self:GetRobert(attacker:GetObjId())
		 	or nil == self:GetRobert(hiter:GetObjId())
		 	or attacker:IsDead()
		 	or hiter:IsDead() then
			return
		end
		local hurt = 0
		local pet = nil
		local pet_hurt = 0
		local is_baoji = false
		if not attacker:IsPerformer() then
			local min_gongji = attacker:GetMinGongji() / hit_count
			local max_gongji = attacker:GetGongji() / hit_count
			local scene_type = Scene.Instance:GetSceneType()
			if attacker:IsMainRole() and Scene.Instance:GetMainRole() and scene_type ~= SceneType.Field1v1 and scene_type ~= SceneType.ARENA_TIANTI then
				local main_role = Scene.Instance:GetMainRole()
				local gongji = main_role.vo.gong_ji + main_role.vo.po_jia + main_role.vo.wuxing_gongji -- 攻击 + 五行攻击 + 破甲
				min_gongji = (gongji - gongji * 0.1) / hit_count
				max_gongji = (gongji + gongji * 0.1) / hit_count

				local attacker_obj = attacker:GetObj()
				if attacker_obj.pet_obj_list and attacker_obj.pet_obj_list[1] then
					pet = attacker_obj.pet_obj_list[1]
					pet_hurt = - GameMath.Rand(min_gongji, max_gongji) * 0.15
				end

				--变身伤害加2.5倍
				if main_role:IsTianShenAppearance() then
					min_gongji = min_gongji * 2.5
					max_gongji = max_gongji * 2.5
				end
			end

			hurt = GameMath.Rand(min_gongji, max_gongji)
			if hurt >= hiter:GetMaxHp() then
				hurt = hiter:GetMaxHp() - 1
			end
			hurt = -1 * math.min(hurt, hiter:GetObj():GetVo().hp)
		end

		local is_shield = false --天神护盾判断
		if hiter:IsMainRole() then
			local main_role = Scene.Instance:GetMainRole()
			if main_role and main_role:IsTianShenAppearance() and main_role.vo.bianshen_hp > 0 then
				is_shield = true
				local protocol = SCEffectBloodChange.New()
				protocol.obj_id = hiter:GetObjId()
				protocol.real_hurt = hurt
				protocol.left_hp = math.max(main_role.vo.bianshen_hp + hurt, 0) -- hurt为负值
				protocol.max_hp = main_role.vo.bianshen_max_hp
				protocol.protect_hp = 0
				protocol.product_id = 0
				protocol.protect_hp_per = 0
				RoleWGCtrl.Instance:OnSCEffectBloodChange(protocol)
			end
		end

		if pet then
			-- 血量变化
			local protocol = SCObjChangeBlood.New()
			protocol.deliverer = pet:GetObjId()
			protocol.skill = 400
			protocol.duan_atk = 0
			protocol.fighttype = FIGHT_TYPE.PET
			protocol.real_blood = pet_hurt
			protocol.product_method = 0
			protocol.obj_id = hiter:GetObjId()
			protocol.blood = pet_hurt
			protocol.now_blood = hiter:GetObj():GetVo().hp + hurt
			FightWGCtrl.Instance:OnObjChangeBlood(protocol, true)
		end

		if not is_shield then
			local protocol = SCObjChangeBlood.New()
			protocol.obj_id = hiter:GetObjId()
			protocol.deliverer = attacker:GetObjId()
			protocol.skill = skill_id
			protocol.fighttype = FIGHT_TYPE.NORMAL
			protocol.product_method = 0
			protocol.real_blood = hurt
			protocol.blood = hurt
			protocol.now_blood = hiter:GetObj():GetVo().hp + hurt
			protocol.passive_flag = 0
			FightWGCtrl.Instance:OnObjChangeBlood(protocol, true)
			hiter:OnBeHited()

			if not attacker:IsMonster() then
				if nil == self.robert_hurt_list[protocol.deliverer] then
					self.robert_hurt_list[protocol.deliverer] = {
						role_id = attacker:GetRoleId(),
						damage_value = -hurt,
						role_name = attacker:GetObj():GetName() or "",
						is_mine = attacker:IsMainRole()
					}
				else
					self.robert_hurt_list[protocol.deliverer].damage_value = self.robert_hurt_list[protocol.deliverer].damage_value - hurt
				end
			end
		end

		GlobalEventSystem:Fire(OtherEventType.ROBERT_ATTACK_ROBERT, attacker:GetRobertId(), hiter:GetRobertId())
		if hiter:IsMainRole() and hiter:GetObj()
		and (Scene.Instance:GetSceneType() == SceneType.GUIDE_BOSS or Scene.Instance:GetSceneType() == SceneType.FakeTianShenFb) then
			local main_role = hiter:GetObj()
			if main_role.vo.hp + hurt <= 0 then
				local protocol = SCRoleAttributeValue.New()
				protocol.obj_id = hiter:GetObjId()
				protocol.count = 1
				protocol.attr_notify_reason = 0
				protocol.attr_pair_list = {
				{attr_type = GameEnum.FIGHT_CHARINTATTR_TYPE_HP,
				attr_value = main_role.vo.max_hp}}
				RoleWGCtrl.Instance:OnRoleAttributeValue(protocol)
			end
		end
	end

	for _, v in ipairs(hiter_robert_list) do
		for i=1, v[3] do
			GlobalTimerQuest:AddDelayTimer(function ()
				changeblood(v[2], v[1], v[3], v[4])
			end, (i - 1) * hit_interval)
		end
	end
end

function RobertManager:GetRobertHurtList()
	return self.robert_hurt_list
end

function RobertManager:OnRobertDie(robert)
	if nil == robert then
		return
	end

	local len = #self.end_die_robert_list
	for i = len, 1, -1 do
		if self.end_die_robert_list[i] == robert:GetRobertId() then
			table.remove(self.end_die_robert_list, i)
		end
	end
	local pos = {}
	pos.x,pos.y = robert:GetLogicPos()

	GlobalEventSystem:Fire(OtherEventType.ROBERT_DIE, robert:GetRobertId(),#self.end_die_robert_list,pos)

	self:CheckFightEnd()
end