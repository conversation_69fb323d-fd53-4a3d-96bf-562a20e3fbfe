-- 终极战场PK战场

UltimateBattleSceneLogic = UltimateBattleSceneLogic or BaseClass(CommonFbLogic)
function UltimateBattleSceneLogic:__init()

end

function UltimateBattleSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

    MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        MainuiWGCtrl.Instance:SetTaskContents(false)
        MainuiWGCtrl.Instance:SetOtherContents(true)
        MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
        MainuiWGCtrl.Instance:SetTeamBtnState(false)

        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
    end)

    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function UltimateBattleSceneLogic:Out(old_scene_type, new_scene_type)
    CommonFbLogic.Out(self)

    MainuiWGCtrl.Instance:SetTaskContents(true)
    -- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end