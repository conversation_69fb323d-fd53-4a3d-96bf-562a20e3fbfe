
----------------------------------
BiZuoBatchView = BiZuoBatchView or BaseClass(SafeBaseView)

function BiZuoBatchView:__init()
	self:LoadConfig()
end

function BiZuoBatchView:LoadConfig()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(634, 500)})
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_use_batch")
end

function BiZuoBatchView:ReleaseCallBack()

end

function BiZuoBatchView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.WelfareBatchView
	self.node_list["slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValue<PERSON><PERSON>e, self))
	
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
end

function BiZuoBatchView:SetData(data)
	self.data = data
	self:Open()
end

function BiZuoBatchView:OnFlush()
	local all_vip_times = self.data.vip_times
	if self.data.btn_flag == 1 then
		-- 2 可元宝及免费找回 ,只有仙玉才能找回VIP次数
		all_vip_times =  self.data.plusvo.is_open == 2 and 0 or self.data.vip_times
	end

	--当前次数
	self.cur_times = self.data.times
	if self.data.times == 0 then
		self.cur_times = self.data.times + all_vip_times
	end
	--最大次数
	self.max_times = self.data.times + all_vip_times

	self.node_list.lbl_offline_num.text.text = self.cur_times

	local desc_str = ""
	if all_vip_times and all_vip_times <= 0  then
		desc_str = string.format(Language.Welfare.RetrieveName1, self.cur_times)
	else
		desc_str = string.format(Language.Welfare.RetrieveName, self.data.times, all_vip_times)
	end

	self.node_list.rich_fubenname.text.text = ToColorStr(self.data.plusvo.name, COLOR3B.WHITE) .. desc_str

	local scale = 0
	if self.max_times > 1 then
		scale = (self.cur_times - 1) / (self.max_times - 1)
	else
		scale = 1
	end
	self.node_list["slider"].slider.value = scale

	self:ShowXiaoHao()


end
function BiZuoBatchView:ShowXiaoHao()
	local retrieved_times = GameMath.Round(self.cur_times)
	local dailyfind_reward_cfg = WelfareWGData.Instance:GetNewDailyFindRewardCfg(self.data.big_type, self.data.plusvo.find_type , self.data.btn_flag - 1, self.data.role_level)

	if dailyfind_reward_cfg == nil then
		print_log("bizuo.dailyfind_reward_cfg is a nil value", self.data.big_type, self.data.plusvo.find_type , self.data.btn_flag - 1, self.data.role_level)
		return
	end

	self.gold_num = retrieved_times * dailyfind_reward_cfg.cost

	local desc_str = ""
	if self.data.btn_flag == 2 then
		if retrieved_times > self.data.times then
			local nomal_times_cost = self.data.times * dailyfind_reward_cfg.cost
			local vip_times_cost = (retrieved_times - self.data.times) * dailyfind_reward_cfg.vip_extra_cost
			local language_str = Language.Welfare.FreeGoldState
			desc_str = ToColorStr(Language.Welfare.ZongJia, COLOR3B.WHITE) .. string.format(language_str,
				(nomal_times_cost + vip_times_cost), dailyfind_reward_cfg.cost, dailyfind_reward_cfg.vip_extra_cost)
		else
			local nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
			local language_str = Language.Welfare.FreeGoldStateTwo
			desc_str = ToColorStr(Language.Welfare.ZongJia, COLOR3B.WHITE) .. string.format(language_str,
				nomal_times_cost, dailyfind_reward_cfg.cost)
		end
	else--免费找回
		desc_str = Language.Welfare.FreeCoinState
	end
	
	self.node_list.lbl_freenum.text.text = desc_str
end

function BiZuoBatchView:OnClickConfirm()
	local retrieved_times = GameMath.Round(self.cur_times)

	--仙玉
	local cur_xianyu_num = RoleWGData.Instance:GetRoleInfo().gold
	--绑玉
	local cur_bind_yu_num = RoleWGData.Instance:GetRoleInfo().bind_gold

	local money_num =  cur_xianyu_num + cur_bind_yu_num

	local dailyfind_reward_cfg = WelfareWGData.Instance:GetNewDailyFindRewardCfg(self.data.big_type, self.data.plusvo.find_type , self.data.btn_flag - 1, self.data.role_level)

	if dailyfind_reward_cfg == nil then
		print_log("bizuo.dailyfind_reward_cfg is a nil value", self.data.big_type, self.data.plusvo.find_type , self.data.btn_flag - 1, self.data.role_level)
		return
	end

	--日常找回
	if self.data.plusvo.daily_find == 1 then
		if retrieved_times - self.data.times > 0 then
			local vip_times = retrieved_times - self.data.times
			local nomal_times_cost = self.data.times * dailyfind_reward_cfg.cost
			local vip_times_cost = vip_times * dailyfind_reward_cfg.cost
			
			if dailyfind_reward_cfg.vip_extra_cost then
				vip_times_cost = vip_times * dailyfind_reward_cfg.vip_extra_cost
			end
			if money_num < (nomal_times_cost + vip_times_cost) and self.data.btn_flag == 2 then
				VipWGCtrl.Instance:OpenTipNoGold()
				self:Close()
				return
			end

			WelfareWGCtrl.Instance:SendGetDailyFindWelfare(self.data.small_type, self.data.btn_flag - 1, vip_times, IS_VIP_OR_NOT.IS_VIP)
			WelfareWGCtrl.Instance:SendGetDailyFindWelfare(self.data.small_type, self.data.btn_flag - 1, self.data.times, IS_VIP_OR_NOT.NOT_VIP)
		else
			local nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
			if money_num < nomal_times_cost and self.data.btn_flag == 2 then
				VipWGCtrl.Instance:OpenTipNoGold()
				self:Close()
				return
			end

			WelfareWGCtrl.Instance:SendGetDailyFindWelfare(self.data.small_type, self.data.btn_flag - 1, retrieved_times, IS_VIP_OR_NOT.NOT_VIP)
		end
		self:Close()
	else
		--活动找回
		local nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
		local is_vip = false
		if dailyfind_reward_cfg.vip_extra_cost and retrieved_times - self.data.times > 0 then
			is_vip = true
			nomal_times_cost = self.data.times * dailyfind_reward_cfg.cost
			local vip_times = retrieved_times - self.data.times
			local vip_times_cost = vip_times * dailyfind_reward_cfg.vip_extra_cost
			nomal_times_cost = nomal_times_cost + vip_times_cost
		end

		if money_num < nomal_times_cost and self.data.btn_flag == 2 then
			VipWGCtrl.Instance:OpenTipNoGold()
			self:Close()
			return
		end
		WelfareWGCtrl.Instance:SendWelfareActivityFind(self.data.small_type, self.data.btn_flag - 1, retrieved_times)
		self:Close()
	end

end

function BiZuoBatchView:OnClickCancel()
	self:Close()
end

function BiZuoBatchView:OnClickSub()
	local num = 0
	if  self.max_times  > 1  then
		if self.cur_times - 1 <= 1 then
			num = 1
		else
			num = self.cur_times - 1
		end
		self.cur_times = num
		self.node_list["slider"].slider.value = (self.cur_times - 1) / (self.max_times - 1 <= 0 and 1 or self.max_times - 1) --100 * (num - 1) / (self.max_times - 1)
		self.node_list.lbl_offline_num.text.text = GameMath.Round(num)
	end
end

function BiZuoBatchView:OnClickAdd()
	local num = 0
	if  self.max_times  > 1  then
		if self.cur_times + 1 >= self.max_times then
			num = self.max_times
		else
			num = self.cur_times + 1
		end
		self.cur_times = num
		self.node_list["slider"].slider.value = (self.cur_times - 1) / (self.max_times - 1) --100 * (num - 1) / (self.max_times - 1)
		self.node_list.lbl_offline_num.text.text = GameMath.Round(num)
	end
end

function BiZuoBatchView:OnSoundValueChange(value)
	local percent_num = 0
	if  self.max_times >= 1  then
		if value > 0 then
			self.cur_times = math.ceil(value * self.max_times)
		else
			self.cur_times = 1
		end
		self.node_list.lbl_offline_num.text.text = GameMath.Round(self.cur_times)
		self:ShowXiaoHao()
	end
end

function BiZuoBatchView:ShowIndexCallBack()
	self:Flush()
end

