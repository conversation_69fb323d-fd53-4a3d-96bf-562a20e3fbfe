GuildView = GuildView or BaseClass()

function GuildView:InitGuildActBossView()
	if self.node_list.boss_tip_btn then
		XUI.AddClickEventListener(self.node_list["boss_tip_btn"], BindTool.Bind(self.OnClickBossTipBtn, self))
	end

	if self.node_list.boss_goto_btn then
		XUI.AddClickEventListener(self.node_list["boss_goto_btn"], BindTool.Bind(self.OnClickGuildActBossGotoBtn, self))
	end

	if self.node_list.boss_reward_list then
		self.boss_reward_list = AsyncListView.New(ItemCell, self.node_list.boss_reward_list)
	end

	if nil == self.boss_display_model then
		self.boss_display_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["boss_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.boss_display_model:SetRenderTexUI3DModel(display_data)
		-- self.boss_display_model:SetUI3DModel(self.node_list["boss_model"].transform, self.node_list["boss_model"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end
end

function GuildView:DeleteGuildActBossView()
	if self.boss_reward_list then
		self.boss_reward_list:DeleteMe()
		self.boss_reward_list = nil
	end
	self.cache_resid = nil
	if self.boss_display_model then
		self.boss_display_model:DeleteMe()
		self.boss_display_model = nil
	end
end

function GuildView:ShowGuildActBossCallBack()

	GuildWGCtrl.Instance:SendReqBossInfo()

end

function GuildView:OnFlushGuildActBossView(param_t, index)
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.Boss)
	if not data or not data.cfg then
		return 
	end
	local str = string.format("%s %s-%s", data.act_hall_cfg.open_tips, data.act_hall_cfg.open_time, data.act_hall_cfg.close_time)
	str = ToColorStr(str, COLOR3B.D_GREEN)
	self.node_list.boss_kaiqi_label.text.text = string.format(Language.GuildBoss.OpenTimeStr, str)

	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local week_num = guild_bosss_info.open_times
	local have_guild_money = guild_bosss_info.guild_money
	local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)

	self.node_list.boss_jindu_value.text.text = have_guild_money
	self.node_list.boss_jindu_text.text.text = need_guild_money
	local slider_value = 0.77 * (have_guild_money / need_guild_money)
	self.node_list.hl_yuan:SetActive(slider_value >= 0.77)
	self.node_list.boss_jidun_slider.slider.value = slider_value
	if data.reward_list then
		self.boss_reward_list:SetDataList(data.reward_list)
    end
    local act_is_open = false
    act_is_open = GuildBossWGData.Instance:IsGuildBossOpen()
    self.node_list.boss_remind:SetActive(act_is_open)

    local boss_show_data =  GuildActivityWGData.Instance:GetGuildBossCfg()
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_show_data.bossid] --获取boss模型
	local rotation_tab = string.split(boss_show_data.rotation, "|")
	local rotation_pos = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	if nil ~= monster_cfg and self.boss_display_model then
		if self.cache_resid == monster_cfg.resid and self.boss_display_model then
			if self.boss_display_model:GetDrawObj() ~= nil then
				return
			end
		end

		self.boss_display_model:SetMainAsset(ResPath.GetMonsterModel(monster_cfg.resid))

		self.cache_resid = monster_cfg.resid
		self.boss_display_model:PlayMonsterAction(false)
		self.boss_display_model:SetRotation(rotation_pos)
	end
end

function GuildView:OnClickGuildActBossGotoBtn()
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.Boss)
	if not data or not data.cfg then
		return 
	end
	GuildBossWGCtrl.Instance:GuildActBossGoFunc()
end

--[[
function GuildView:GuildActBossGoFunc()
	-- 改成自动开启了
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	if is_open_act then	
		GuildBossWGCtrl.SendGuildBossEnterReq()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossTimeTips)
	end

	-- local role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- local guild_post = role_vo.guild_post
	-- local is_mengzhu = guild_post == GUILD_POST.FU_TUANGZHANG or guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.JiaMengZhu
	-- local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	-- local is_can = guild_bosss_info.active_is_open == 1
	-- local is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	-- local have_guild_money = guild_bosss_info.guild_money
	-- local week_num = guild_bosss_info.open_times
	-- local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)
	-- if is_open_act then
	-- 	GuildBossWGCtrl.SendGuildBossEnterReq()
	-- else
	-- 	local tody_num
	-- 	if is_open_act then
	-- 		tody_num = 1
	-- 	else
	-- 		tody_num = guild_bosss_info.today_open_num
    --     end
    --     local week_times = GuildBossWGData.Instance:GetGuildBossOpenTimes()
	-- 	local is_week_can_open = guild_bosss_info.open_times < week_times
	-- 	local money_can_open = have_guild_money >= need_guild_money
	-- 	local today_can_open = tody_num < GuildDataConst.TODAY_GUILD_MAX_BOSS
	-- 	if money_can_open then
	-- 		if is_mengzhu and is_week_can_open and today_can_open and money_can_open and is_can then
	-- 			if not self.alert_window then
	-- 				self.alert_window = Alert.New()
	-- 				self.alert_window:SetOkFunc(function()
	-- 					GuildBossWGCtrl.SendGuildBossStartReq()
	-- 				end)
	-- 			end
	-- 			self.alert_window:SetLableString(Language.GuildBoss.IsOpenTxt)
	-- 			self.alert_window:Open()
	-- 		else
	-- 			if not today_can_open then
	-- 				TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossDayTips)
	-- 			elseif not is_week_can_open then
	-- 				TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossWeekDayTips)
	-- 			elseif not is_mengzhu then
	-- 				TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMengZhuTips)
	-- 			else
	-- 				TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossTimeTips)
	-- 			end
	-- 		end
	-- 	else
	-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMoneyTips)
	-- 	end
	-- end
end
]]

function GuildView:OnClickBossTipBtn()
	local data = GuildActivityWGData.Instance:GetActDataByType(GuildActivityWGData.Act_Type.Boss)
	if not data or not data.cfg then
		return 
	end
	local cfg = data.cfg
	local tips_content = cfg.act_rule
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.Activity.HuoDongShuoMing)
	role_tip:SetContent(tips_content)
end
