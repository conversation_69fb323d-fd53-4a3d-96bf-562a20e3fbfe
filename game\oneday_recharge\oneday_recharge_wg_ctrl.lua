require("game/oneday_recharge/oneday_recharge_wg_data")
require("game/oneday_recharge/oneday_recharge_view")

OneDayRechargeWGCtrl = OneDayRechargeWGCtrl or BaseClass(BaseWGCtrl)
function OneDayRechargeWGCtrl:__init()
	if OneDayRechargeWGCtrl.Instance then
		ErrorLog("[OneDayRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	OneDayRechargeWGCtrl.Instance = self

    self.data = OneDayRechargeWGData.New()
	self.view = OneDayRechargeView.New(GuideModuleName.OneDayRechargeView)

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
    self:RegisterAllProtocols()
end

function OneDayRechargeWGCtrl:__delete()
	OneDayRechargeWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
end

function OneDayRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOADailyRechargeInfo,"OnSCOADailyRechargeInfo")
end

function OneDayRechargeWGCtrl:SendOneDayRechargeReq(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DAILY_RECHARGE
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function OneDayRechargeWGCtrl:OnSCOADailyRechargeInfo(protocol)
	--print_error("每日累充", protocol)
	self.data:SetAllInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.OneDayRecharge)
end

function OneDayRechargeWGCtrl:OnDayChange()
	if self.view:IsOpen() then
		self:SendOneDayRechargeReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DAILY_RECHARGE, OA_DAILY_RECHARGE_OPERATE_TYPE.INFO)
		self.view:Flush(0, "flush_model")
	end
end