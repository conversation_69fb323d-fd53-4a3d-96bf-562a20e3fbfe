require("game/privileged_guidance/privileged_guidance_view")
require("game/privileged_guidance/privileged_guidance_wg_data")

PrivilegedGuidanceWGCtrl = PrivilegedGuidanceWGCtrl or BaseClass(BaseWGCtrl)
function PrivilegedGuidanceWGCtrl:__init()
    if PrivilegedGuidanceWGCtrl.Instance then
        error("[PrivilegedGuidanceWGCtrl]:Attempt to create singleton twice!")
    end

    PrivilegedGuidanceWGCtrl.Instance = self
    self.data = PrivilegedGuidanceWGData.New()
    self.privileged_guidance_view = PrivilegedGuidanceView.New(GuideModuleName.PrivilegedGuidanceView)
    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

function PrivilegedGuidanceWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil
    GlobalEventSystem:UnBind(self.open_fun_change)
    self.privileged_guidance_view:DeleteMe()
    self.privileged_guidance_view = nil

    PrivilegedGuidanceWGCtrl.Instance = nil
end

function PrivilegedGuidanceWGCtrl:FlushGuidanceView()
    if nil ~= self.privileged_guidance_view and self.privileged_guidance_view:IsOpen() then
        self.privileged_guidance_view:Flush()
    end
end

function PrivilegedGuidanceWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.PrivilegedGuidanceView then 
        local is_open = FunOpen.Instance:GetFunIsOpened(FunName.PrivilegedGuidanceView)
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.PRIVILEGED_GUIDANCE_VIEW, state)
    end
end

function PrivilegedGuidanceWGCtrl:UpLevel(longxi_type)
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(longxi_type)
    LongXiWGCtrl.Instance:SendLongXiRequest(LONGXI_OPERATE_TYPE.LONGXI_OPERATE_TYPE_LEVEL_UP, base_data.seq)
end