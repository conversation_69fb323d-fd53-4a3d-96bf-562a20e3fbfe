﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using Nirvana;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// The extensions for <see cref="RawImage"/>.
/// </summary>
public static class RawImageExtensions
{
    private static readonly Nirvana.Logger logger = 
        LogSystem.GetLogger("RawImageExtensions");

    /// <summary>
    /// Load the sprite into this image.
    /// </summary>
    public static void LoadSprite(
        this RawImage image, string bundle, string asset, Action complete)
    {
        Debug.LogErrorFormat("Please do not use Rawimage:LoadSprite again, you can use UIVariableBindRawImage. {0}", asset);
    }

    /// <summary>
    /// Load the sprite into this image.
    /// </summary>
    public static void LoadSprite(
        this RawImage image, string imagePath, Action complete)
    {
        Scheduler.RunCoroutine(
            LoadSpriteImpl(image, imagePath, complete));
    }

    private static IEnumerator LoadSpriteImpl(
        RawImage image, string imagePath, Action complete)
    {
#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        if (!imagePath.StartsWith("file:///") && !imagePath.StartsWith("http://"))
        {
            imagePath = "file:///" + imagePath;
        }
#else
        if (!imagePath.StartsWith("file://") && !imagePath.StartsWith("http://"))
        {
            imagePath = "file://" + imagePath;
        }
#endif

        var www = new WWW(imagePath);
        yield return www;

        if (image == null)
        {
            yield break;
        }

        if (www.error != null)
        {
            logger.LogError(
                "LoadSprite {0} for RawIamge failed: {1}", 
                imagePath, 
                www.error);
            yield break;
        }

        Texture2D texture = www.texture;
        if (texture == null)
        {
            logger.LogError(
                "LoadSprite {0} for RawIamge is not a image.", 
                imagePath);
            yield break;
        }

        if (null != image.texture)
        {
            GameObject.Destroy(image.texture);
        }
        image.texture = texture;
        www.Dispose();

        complete();
    }
}
