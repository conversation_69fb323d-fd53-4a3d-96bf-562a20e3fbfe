
-------------------------------------- 每日首充------------------------------------------
function TianshenRoadView:InitShouChongView()
	self.init_shouchong = true

	XUI.AddClickEventListener(self.node_list.first_btn_receive, BindTool.Bind(self.OnShouChongBtnReceive<PERSON>lick<PERSON>nadler,self))

	local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.tianshenroad_first_recharge)
	if theme_cfg ~= nil then
		self.node_list.first_tip_desc.text.text = theme_cfg.rule_desc
	end 

	-- if not self.shouchong_reward_list then
	-- 	self.shouchong_reward_list = AsyncListView.New(ItemCell, self.node_list["first_reward_list"])
	-- end

	if not self.shouchong_reward_list then
        self.shouchong_reward_list = {}
        for i = 1, 5 do
            self.shouchong_reward_list[i] = TianshenRoadFrRewardRender.New(self.node_list["reward_" .. i])
			self.shouchong_reward_list[i]:SetIndex(i)
        end
    end

	self:SCTimeCountDown()
	self:FlushShouChongView()
end

function TianshenRoadView:ReleaseShouChongView()
	-- if self.shouchong_reward_list then
	-- 	self.shouchong_reward_list:DeleteMe()
	-- 	self.shouchong_reward_list = nil
	-- end

	if self.shouchong_reward_list then
        for k,v in pairs(self.shouchong_reward_list) do
            v:DeleteMe()
        end
		self.shouchong_reward_list = nil
	end

	CountDownManager.Instance:RemoveCountDown("tianshenroad_shouchong_count_down")
	CountDownManager.Instance:RemoveCountDown("tianshenroad_leichong_count_down")
end

function TianshenRoadView:ShowShouChongView()
	TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_LEIJI_HAOLI, TIANSHEN_THEME_SHOUCHONG_OP_TYPE.TYPE_INFO)
	self:DoTRShouChongAnim()
end

function TianshenRoadView:FlushShouChongView()
	if self.init_shouchong == nil then
		return
	end

	-- local is_active_last_day = TianshenRoadWGData.Instance:IsActivityLastDay(TabIndex.tianshenroad_first_recharge)
	local gift_state = TianshenRoadWGData.Instance:GetShouChongGiftState()
	local can_get = gift_state == TianShenRoadRewardState.KLQ
	local is_show_ylq = gift_state == TianShenRoadRewardState.YLQ -- and is_active_last_day

	self.node_list.first_btn_ylq:SetActive(is_show_ylq)
	self.node_list.first_btn_receive:SetActive(not is_show_ylq)
	self.node_list.first_btn_redpoint:SetActive(not is_show_ylq and can_get)
	if gift_state == TianShenRoadRewardState.BKL then
		self.node_list.first_btn_text.text.text = Language.TianShenRoad.BtnStr3
	else
		self.node_list.first_btn_text.text.text = Language.TianShenRoad.BtnStr4
	end

	self.node_list.first_desc.text.text = Language.TianShenRoad.first_recharge_desc
	self:FlushShouChongList()
end

function TianshenRoadView:FlushShouChongList()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.GOD_CHAOZHI_SHOUCHONG)
	local day_index = open_day - activity_open_day + 1

	local cfg = TianshenRoadWGData.Instance:GetShouChongRewardCfgByDay(day_index)
	if cfg ~= nil then
		--self.shouchong_reward_list:SetDataList(SortTableKey(cfg.reward_item))
		for i = 1, 5 do
			if i <= #cfg.reward_item + 1 then
				self.node_list["reward_" .. i]:SetActive(true)
				self.shouchong_reward_list[i]:SetData(cfg.reward_item[i - 1])
			else
				self.node_list["reward_" .. i]:SetActive(false)
			end
		end
		--self.node_list.first_recharge_num.text.text = cfg.daily_recharge_price or 0
	end
end

function TianshenRoadView:OnShouChongBtnReceiveClickHnadler()
	local gift_state = TianshenRoadWGData.Instance:GetShouChongGiftState()
	if gift_state == TianShenRoadRewardState.KLQ then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_CHAOZHI_SHOUCHONG, TIANSHEN_THEME_SHOUCHONG_OP_TYPE.TYPE_DRAW)
	elseif gift_state == TianShenRoadRewardState.YLQ then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShenRoad.ReChargeStr3)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	end
end

--有效时间倒计时
function TianshenRoadView:SCTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_shouchong_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_first_recharge)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.first_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_shouchong_count_down", BindTool.Bind1(self.UpdateSCCountDown, self), BindTool.Bind1(self.SCTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.first_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.first_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function TianshenRoadView:UpdateSCCountDown(elapse_time, total_time)
	self.node_list.first_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

function TianshenRoadView:DoTRShouChongAnim()
	self.node_list["first_reward_list"]:SetActive(false)
    local tween_info = UITween_CONSTS.TianshenRoadView
    ReDelayCall(self, function()
        UITween.CleanAlphaShow(GuideModuleName.TianShenRoadPanel)
        self.node_list["first_reward_list"]:SetActive(true)
        for i = 1, 5 do
            self.shouchong_reward_list[i]:PlayItemAnim(i)
        end
    end, 0, "tr_first_reward_list")
	-- local tween_info = UITween_CONSTS.TianshenRoadView
    -- UITween.FakeHideShow(self.node_list["first_recharge_root"])
    -- UITween.FakeHideShow(self.node_list["first_recharge_info_twen"])

    -- RectTransform.SetAnchoredPositionXY(self.node_list["img_recharge_bg"].rect, -5, 600)
    -- RectTransform.SetAnchoredPositionXY(self.node_list["first_btn_receive"].rect, -5, -400)
    
    -- self.node_list["img_recharge_bg"].rect:DOAnchorPos(Vector2(-5, 179), tween_info.MoveTime)
    -- self.node_list["first_btn_receive"].rect:DOAnchorPos(Vector2(-5, -190), tween_info.MoveTime)
    -- UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["first_recharge_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

    -- ReDelayCall(self, function()
	-- 	UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["first_recharge_info_twen"], 0, tween_info.ToAlpha, tween_info.AlphaTime)
	-- end, tween_info.AlphaDelay, "tianshen_road_shouchong_tween")
end

----------------------------------------------------------------------------------------
----------------------------------- 累计充值 --------------------------------------------

function TianshenRoadView:InitLeiChongView()
	if not self.leichong_model then
		self.leichong_model = OperationActRender.New(self.node_list["leichong_model"])
		self.leichong_model:SetModelType(MODEL_CAMERA_TYPE.BASE)
	end

	if not self.leichong_reward_list then
		self.leichong_reward_list = AsyncListView.New(LeiChongRender, self.node_list["total_reward_list"])
	end

	self:LCTimeCountDown()
	self:FlushLeiChongView()
end

function TianshenRoadView:ReleaseRechargeView()
	if self.leichong_model then
        self.leichong_model:DeleteMe()
        self.leichong_model = nil
    end

	if self.leichong_reward_list then
		self.leichong_reward_list:DeleteMe()
		self.leichong_reward_list = nil
	end
end

function TianshenRoadView:ShowLeiChongView()
	TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_LEIJI_HAOLI, TIANSHEN_THEME_LEICHONG_OP_TYPE.TYPE_INFO)
	self:DoTRLeiChongAnim()
end

function TianshenRoadView:FlushLeiChongView()
	if self.leichong_reward_list then
		local list = TianshenRoadWGData.Instance:GetLeiChongRewardList()
		self.leichong_reward_list:SetDataList(list)
	end

	self:FlushLeiChongModel()
end

function TianshenRoadView:FlushLeiChongModel()
	local init_cfg = TianshenRoadWGData.Instance:GetActModelCfgById(ACTIVITY_TYPE.GOD_LEIJI_HAOLI)
	if not init_cfg then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if init_cfg.model_show_itemid ~= 0 and init_cfg.model_show_itemid ~= "" then
		local split_list = string.split(init_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = init_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = init_cfg["model_bundle_name"]
	display_data.asset_name = init_cfg["model_asset_name"]
	local model_show_type = tonumber(init_cfg["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	self.leichong_model:SetData(display_data)

	if init_cfg.model_scale and init_cfg.model_scale ~= "" then
		local scale = init_cfg.model_scale
		Transform.SetLocalScaleXYZ(self.node_list["leichong_model"].transform, scale, scale, scale)
	end

	local pos_x, pos_y = 0, 0
	if init_cfg.model_pos and init_cfg.model_pos ~= "" then
		local pos_list = string.split(init_cfg.model_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.leichong_model.rect, pos_x, pos_y)

	if init_cfg.model_rot and init_cfg.model_rot ~= "" then
		local rotation_tab = string.split(init_cfg.model_rot, "|")
		self.node_list["leichong_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
			rotation_tab[3])
	end
end

--有效时间倒计时
function TianshenRoadView:LCTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("tianshenroad_leichong_count_down")
	local invalid_time = TianshenRoadWGData.Instance:GetActivityInValidTime(TabIndex.tianshenroad_total_recharge)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.total_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("tianshenroad_leichong_count_down", BindTool.Bind1(self.UpdateLCCountDown, self), BindTool.Bind1(self.LCTimeCountDown, self), invalid_time, nil, 1)
	else
		self.node_list.total_time_label.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.total_time_label.text.color = Str2C3b(COLOR3B.RED)
	end
end

function TianshenRoadView:UpdateLCCountDown(elapse_time, total_time)
	self.node_list.total_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

function TianshenRoadView:DoTRLeiChongAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView
    UITween.FakeHideShow(self.node_list["total_recharge_root"])
    UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["total_recharge_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
	self:DoTRLeiChongCellsAnim()
end

function TianshenRoadView:DoTRLeiChongCellsAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView.ListCellRender
	self.node_list["total_reward_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["total_reward_list"]:SetActive(true)
        local list =  self.leichong_reward_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyTSRLeiChongItemAnim(count)
        end
    end, tween_info.DelayDoTime, "TRLeiChong_Cell_Tween")
end

-----------------------------------------------------------------------------------------------------------------

LeiChongRender = LeiChongRender or BaseClass(BaseRender)

function LeiChongRender:LoadCallBack()
	self.reward_cell_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	XUI.AddClickEventListener(self.node_list["btn_lingqu"], BindTool.Bind1(self.OnClickRewardHnadler, self))
	XUI.AddClickEventListener(self.node_list["btn_go_target"], BindTool.Bind1(self.OnClickRewardHnadler, self))
end

function LeiChongRender:ReleaseCallBack()
	self.reward_cell_list:DeleteMe()
	self.reward_cell_list = nil
end

function LeiChongRender:OnFlush()
	local data = self:GetData()

	local cur_xianyu = TianshenRoadWGData.Instance:GetOwnXianYu()
	local is_finish = cur_xianyu >= data.cfg.stage_value
	local is_get_reward = data.receive_state == 2

	local color = is_finish and COLOR3B.C2 or COLOR3B.C3
	local cur_xianyu_str = ToColorStr(cur_xianyu, color)
	local stage_valu_str  = ToColorStr(data.cfg.stage_value, color)
	self.node_list.recharge_value.text.text = string.format("%s/%s", cur_xianyu_str, stage_valu_str)

	local reward_list = SortTableKey(data.cfg.reward_item)
	self.reward_cell_list:SetDataList(reward_list)
	self.reward_cell_list:SetRefreshCallback(function(item_cell, cell_index)
		if item_cell then
			item_cell:SetRedPointEff(is_finish and not is_get_reward)
			item_cell:SetLingQuVisible(is_finish and is_get_reward)
		end
	end)

	self.node_list["btn_lingqu"]:SetActive(not is_get_reward and is_finish)
	self.node_list["btn_go_target"]:SetActive(not is_get_reward and not is_finish)
	self.node_list["btn_yilingqu"]:SetActive(is_get_reward)
end

function LeiChongRender:OnClickRewardHnadler()
	local data = self:GetData()
	if data.receive_state == 1 then
		TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_LEIJI_HAOLI, TIANSHEN_THEME_LEICHONG_OP_TYPE.TYPE_DRAW,self.data.cfg.ID)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	end
end

function LeiChongRender:PalyTSRLeiChongItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.TianshenRoadView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "ts_leichong_item_" .. wait_index)
end

TianshenRoadFrRewardRender = TianshenRoadFrRewardRender or BaseClass(BaseRender)

function TianshenRoadFrRewardRender:__init()
	self.item_cell = ItemCell.New(self.node_list.item_cell)
	self.item_cell:UseNewSelectEffect(true)
end

function TianshenRoadFrRewardRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TianshenRoadFrRewardRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local gift_state = TianshenRoadWGData.Instance:GetShouChongGiftState()
	self.item_cell:SetFlushCallBack(function ()
		self.item_cell:ResetSelectEffect()
		self.item_cell:SetSelectEffect(gift_state == TianShenRoadRewardState.YLQ)
		self.item_cell:SetRedPointEff(gift_state == TianShenRoadRewardState.KLQ)
	end)

	self.item_cell:SetData(self.data)
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name.text.text = cfg and cfg.name or ""
end

function TianshenRoadFrRewardRender:PlayItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.TianshenRoadView.FirstRechargeListCellRender

    self.node_list.tween_root.canvas_group.alpha = 0
    ReDelayCall(self, function()
        UITween.AlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tween_root"], tween_info.FromAlpha, tween_info.ToAlpha, tween_info.AlphaTweenTime)
    end, tween_info.NextDoDelay * wait_index, "TianshenRoadFrRewardRender" .. wait_index)
end