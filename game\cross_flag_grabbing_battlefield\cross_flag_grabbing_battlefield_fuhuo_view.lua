CrossFlagGrabbingBattlefieldFuHuoView = CrossFlagGrabbingBattlefieldFuHuoView or BaseClass(SafeBaseView)

local CAMP_NAME_COLOR = {
	[0] = "#EED9A5",
	[1] = "#A5E6EE",
	[2] = "#A5E6EE",
}

function CrossFlagGrabbingBattlefieldFuHuoView:__init()
	self:SetMaskBg(false,false)
	self.view_layer = UiLayer.Normal
	self.fuhuo_type = FuHuoType.Here
	self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_fuhuo")
end

function CrossFlagGrabbingBattlefieldFuHuoView:__delete()
	self.fuhuo_type = FuHuoType.None
end

function CrossFlagGrabbingBattlefieldFuHuoView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("fgb_fohuo") then
		CountDownManager.Instance:RemoveCountDown("fgb_fohuo")
	end

	self.killer_name = nil
	self.fuhuo_common_callback = nil
	self.fuhuo_type = FuHuoType.None
end

function CrossFlagGrabbingBattlefieldFuHuoView:LoadCallBack()
	self.killer_name = ""
	XUI.AddClickEventListener(self.node_list.btn_fuhuo_common, BindTool.Bind1(self.OnClickFuhuoCommon, self))
	XUI.AddClickEventListener(self.node_list.btn_open_chat, BindTool.Bind1(self.OnClickOpenChat, self))
end

function CrossFlagGrabbingBattlefieldFuHuoView:OnFlush()
	for i = 1, 3 do
		local data = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendBySeq(i - 1)
		local info = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBContendInfoBySeq(data.seq)
		local area_bundle, area_asset = ResPath.GetCommonIcon(data.contend_icon)
		local area_node = self.node_list["area_icon" .. i]
		area_node.image:LoadSprite(area_bundle, area_asset, function ()
			area_node.image:SetNativeSize()
		end)

		self.node_list["area_name" .. i].text.text = data.contend_name
		local area_state_icon = "a3_gjzz_bq2"
		local area_state_name = Language.LandWarFbPersonView.Neutral
		local area_state_node = self.node_list["area_state" .. i]
		local area_name_node = self.node_list["area_state_name" .. i]
		local left_progress = (info.camp_value_list or {})[0] or 0
		local right_progress = (info.camp_value_list or {})[1] or 0
		local team_name_id = left_progress > right_progress and 0 or left_progress < right_progress and 1 or 2
		local team_camp_cfg = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCampCfgByCampId(team_name_id)
		area_state_icon = team_camp_cfg and team_camp_cfg.camp_icon or area_state_icon
		area_state_name = team_camp_cfg and team_camp_cfg.camp_name or area_state_name
		local area_state_bundle, area_state_asset = ResPath.GetCrossFGBPathImg(area_state_icon)
		area_name_node.text.text = area_state_name

		area_state_node.image:LoadSprite(area_state_bundle, area_state_asset, function ()
			area_state_node.image:SetNativeSize()
		end)
	end

	self.node_list.tips_1.text.text = string.format(Language.FlagGrabbingBattlefield.BeKillDesc, self.killer_name)
	self:DaoJiShi()
end

function CrossFlagGrabbingBattlefieldFuHuoView:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid)
	self.killer_name = killer_name or ""
end

function CrossFlagGrabbingBattlefieldFuHuoView:DaoJiShi()
	local auto_fuhuo_time = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBAutoFuHuoTime()
	local relive_time = TimeWGCtrl.Instance:GetServerTime() + auto_fuhuo_time
	-- local str = string.format(Language.Common.TimeStr8, auto_fuhuo_time)
	self.node_list["text_fuhuo_times"].text.text = auto_fuhuo_time

	CountDownManager.Instance:RemoveCountDown("fuhuo_boss_map")
	CountDownManager.Instance:AddCountDown("fuhuo_boss_map",
		BindTool.Bind1(self.UpdateCountDownTime, self),
		BindTool.Bind(self.CompleteCountDownTime, self, true),
		relive_time, nil, 0.5)
end

function CrossFlagGrabbingBattlefieldFuHuoView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["text_fuhuo_times"] then
		return
	end

    local last_time = math.floor(total_time - elapse_time)
	self.node_list.fuhuo_time_slider.image.fillAmount = last_time / total_time
	-- local str = string.format(Language.Common.TimeStr8, last_time)
	self.node_list["text_fuhuo_times"].text.text = last_time
end

function CrossFlagGrabbingBattlefieldFuHuoView:CompleteCountDownTime(is_auto_fuhuo)
	if not self.node_list["btn_fuhuo_common"] then
		return
	end

	self.node_list["text_fuhuo_times"].text.text = ""
    FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
end

function CrossFlagGrabbingBattlefieldFuHuoView:SetFuhuoCallback(common_callback)
	self.fuhuo_common_callback = common_callback
end

function CrossFlagGrabbingBattlefieldFuHuoView:FuhuoCallback()
	if self.fuhuo_common_callback and self.fuhuo_type == FuHuoType.Common then
		self.fuhuo_common_callback()
	end
	
	self:Close()
end

function CrossFlagGrabbingBattlefieldFuHuoView:OnClickFuhuoCommon()
	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
end

function CrossFlagGrabbingBattlefieldFuHuoView:GetUseFuHuoType()
	return self.fuhuo_type
end

function CrossFlagGrabbingBattlefieldFuHuoView:OnClickOpenChat()
	ChatWGCtrl.Instance:OpenChatWindow()
end