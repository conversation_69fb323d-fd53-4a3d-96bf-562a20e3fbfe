CustomizaedRumorsSetView = CustomizaedRumorsSetView or BaseClass(SafeBaseView)

function CustomizaedRumorsSetView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/customized_rumors_ui_prefab", "layout_customized_set_view")
end

function CustomizaedRumorsSetView:LoadCallBack()
    if not self.customized_set_list then
        self.customized_set_list = AsyncListView.New(CustomizedRumorsSetListItemRender, self.node_list.customized_set_list)
        self.customized_set_list:SetSelectCallBack(BindTool.Bind(self.OnSelectRumorsHandler, self))
        self.customized_set_list:SetDefaultSelectIndex(nil)
    end

    if not self.special_rumior_root then
        self.special_rumior_root = BaseSpecialRumorsCell.New(self.node_list.customized_set_rumors_root)
    end
    
    if not self.customized_set_msg_list then
        self.customized_set_msg_list = AsyncListView.New(CustomizedRumorsSetMsgListItemRender, self.node_list.customized_set_msg_list)
        self.customized_set_msg_list:SetSelectCallBack(BindTool.Bind(self.OnSelectRumorMsgHandler, self))
        self.customized_set_msg_list:SetStartZeroIndex(false)
        self.customized_set_msg_list:SetDefaultSelectIndex(nil)
    end

    self.node_list.desc_cr_input.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnCrInputChange, self))
    self.node_list.desc_cr_input.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnCrInputEndEdit, self))

    local other_cfg = CustomizedRumorsWGData.Instance:GetOtherCfg()
    self.node_list.desc_cr_input.input_field.characterLimit = other_cfg.desc_max_count

    self.select_rumor_type = -1
    self.select_gear_seq = -1
    self.select_msg_seq = 0
    self.select_rumor_bg_type = 1
    self.select_rumor_type_data = {}
    self.need_check_cr_input = true

    XUI.AddClickEventListener(self.node_list.btn_save_set, BindTool.Bind(self.OnClicSaveSet, self))
    XUI.AddClickEventListener(self.node_list.btn_rebuild, BindTool.Bind(self.OnClicRebuild, self))
end

function CustomizaedRumorsSetView:ReleaseCallBack()
    if self.customized_set_list then
        self.customized_set_list:DeleteMe()
        self.customized_set_list = nil
    end

    if self.special_rumior_root then
        self.special_rumior_root:DeleteMe()
        self.special_rumior_root = nil
    end

    if self.customized_set_msg_list then
        self.customized_set_msg_list:DeleteMe()
        self.customized_set_msg_list = nil
    end
end

function CustomizaedRumorsSetView:SetDataInfo(rumors_data, type_data)
    self.rumors_data = rumors_data    -- 默认传闻表大的desc_type 区分的list 3
    self.type_data = type_data          -- 默认传闻表大的desc_type gear_seq 区分的单独表 
end

function CustomizaedRumorsSetView:OnFlush()
    if IsEmptyTable(self.rumors_data) or IsEmptyTable(self.type_data) then
        return
    end
    local rumor_type = self.type_data.desc_type  
    self.select_rumor_type = rumor_type    -- 选择的传闻类型
    self.select_gear_seq = self.select_gear_seq > 0 and self.select_gear_seq or self.type_data.gear_seq     --传闻档位
    self.select_rumor_bg_type = self.type_data.rumor_type   --显示的传闻类型

    -- 选中的信息 seq
    local select_desc_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(rumor_type)
    local status = CustomizedRumorsWGData.Instance:IsCustomizedRumorByRumorType(rumor_type)
    self.node_list.flag_cur_select:CustomSetActive(select_desc_seq == -1 and status == DIYCHUANWEN_OPERA_STATUS.SUC)
    self.node_list.flag_audit_status:CustomSetActive(select_desc_seq == -1 and status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED)
    self.select_msg_seq = select_desc_seq
    
    -- 选择了自定义的
    if select_desc_seq == -1 then
        if status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED or status == DIYCHUANWEN_OPERA_STATUS.SUC then
            self.node_list.cur_select:CustomSetActive(true)
            local dingzhi_desc = CustomizedRumorsWGData.Instance:GetCustomizedRumorStr(rumor_type)
            self.node_list.desc_cr_input.input_field.text = dingzhi_desc
        end
    elseif select_desc_seq == 0 then
        --没选
        self.node_list.desc_cr_input.input_field.text = ""
    else
        self.node_list.desc_cr_input.input_field.text = ""
    end

    self.customized_set_list:SetDataList(self.rumors_data)
    self.customized_set_list:JumpToIndex(self.select_gear_seq)
    self.node_list.btn_rebuild:CustomSetActive(select_desc_seq ~= 0)
    self.node_list.btn_save_set:CustomSetActive(select_desc_seq == 0)
end

-- 选中皮肤  默认传闻表的一套数据
function CustomizaedRumorsSetView:OnSelectRumorsHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    local rumor_type = data.desc_type
    self.select_gear_seq = data.gear_seq
    self.select_rumor_bg_type = data.rumor_type  -- 皮肤
    self.select_rumor_type_data = data
    local data_list = CustomizedRumorsWGData.Instance:GetRumorPresetContentList(rumor_type, data.gear_seq)
    self.customized_set_msg_list:SetDataList(data_list)

    local reset_cost_itemid = data.reset_cost_itemid
    local reset_cost_item_num = data.reset_cost_item_num
    local item_cfg = ItemWGData.Instance:GetItemConfig(reset_cost_itemid)
    local item_bundle, item_asset = ResPath.GetItem(item_cfg.icon_id)
    self.node_list.br_cost_item.image:LoadSprite(item_bundle, item_asset, function ()
        self.node_list.br_cost_item.image:SetNativeSize()
    end)

    local has_num = ItemWGData.Instance:GetItemNumInBagById(reset_cost_itemid)
    local color = has_num >= reset_cost_item_num and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.br_cost_item_num.text.text = ToColorStr(has_num .. "/" .. reset_cost_item_num, color)

    if self.select_msg_seq == -1 then
        self:SetCustomizedInput()
    else
        if self.select_msg_seq == 0 then
            self.select_msg_seq = 1
        end

        self.customized_set_msg_list:JumpToIndex(self.select_msg_seq)
    end
end

-- 选中预设表 一条数据 
function CustomizaedRumorsSetView:OnSelectRumorMsgHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    self.node_list.cur_select:CustomSetActive(false)
    local data = item.data
    local rumor_type = data.rumor_type

    local select_desc_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(rumor_type)
    if select_desc_seq == -1 then
        local status = CustomizedRumorsWGData.Instance:IsCustomizedRumorByRumorType(rumor_type)
        -- 如果有选中则 重置内容
        if status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED or status == DIYCHUANWEN_OPERA_STATUS.SUC then
            local dingzhi_desc = CustomizedRumorsWGData.Instance:GetCustomizedRumorStr(rumor_type)
            self.need_check_cr_input = false
            self.node_list.desc_cr_input.input_field.text = dingzhi_desc
        end
    end

    local _, top, _ = CustomizedRumorsWGData.Instance:GetCustomizedRumorDescContent(data.rumor_type, data.gear_seq)
    self.select_msg_seq = data.desc_seq
    self:SetSpecialRumorData(self.select_rumor_bg_type, top .. data.desc)
end

function CustomizaedRumorsSetView:SetSpecialRumorData(show_rumor_type, desc)
    local rumor_data = {
        rumor_type = show_rumor_type,
        desc_rumors_content = desc,
        show_yoyo_tween = true,
    }

    self.special_rumior_root:SetData(rumor_data)
end

function CustomizaedRumorsSetView:OnCrInputChange()
    if not self.need_check_cr_input then
        self.need_check_cr_input = true
        return
    end

    self.customized_set_msg_list:CancelSelect()
    self:SetCustomizedInput()
    self.select_msg_seq = -1
end

function CustomizaedRumorsSetView:OnCrInputEndEdit()
    self.node_list.cur_select:CustomSetActive(true)
    self.customized_set_msg_list:CancelSelect()
    self.select_msg_seq = -1
    self:SetCustomizedInput()
end

function CustomizaedRumorsSetView:SetCustomizedInput()
    local desc_content = self.node_list.desc_cr_input.input_field.text
    
    if desc_content == "" then
        self:SetSpecialRumorData(self.select_rumor_bg_type, Language.CustomizedRumors.PlaeseInputDesc)
    else
        if ChatFilter.IsEmoji(desc_content) then
            TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
            self.node_list.desc_cr_input.input_field.text = ""
            desc_content = ""
        end

        local _, top, _ = CustomizedRumorsWGData.Instance:GetCustomizedRumorDescContent(self.select_rumor_type, self.select_gear_seq)
        self:SetSpecialRumorData(self.select_rumor_bg_type, top .. desc_content)
	end
end

function CustomizaedRumorsSetView:OnClicSaveSet()
    -- 是否激活传闻
    local active = CustomizedRumorsWGData.Instance:IsRumorTypeActive(self.select_rumor_type)
    if not active then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.PleaseActiveEveryRumor)
        return
    end

    local select_desc_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(self.select_rumor_type)

    -- 直接发请求
    if select_desc_seq == 0 then
        -- 选取模板
        if self.select_msg_seq > 0 then
            local desc = CustomizedRumorsWGData.Instance:GetRumorPresetContentData(self.select_rumor_type, self.select_gear_seq, self.select_msg_seq).desc

            local ok_func = function ()
                CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.CHOOSE_DESC, self.select_rumor_type, self.select_msg_seq)
            end
            
            TipWGCtrl.Instance:OpenAlertTips(string.format(Language.CustomizedRumors.ChangeRumorsDesc, self.type_data.rumor_gear_name, desc), ok_func)
        elseif self.select_msg_seq == -1 then
            --自定义文本
            -- local input_text = self.node_list.desc_cr_input_text

            local text =  self.node_list.desc_cr_input.input_field.text
            if nil ~= text then
                -- local text = input_text.text.text

                if text == "" then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.PlaeseInputDesc)
                    return
                end
    
                if string.len(text) <= 0 then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.PlaeseInputDesc)
                    return
                end
    
                if ChatFilter.Instance:IsIllegal(text, false) or ChatFilter.IsEmoji(text) then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
                    self.node_list.desc_cr_input.input_field.text = ""
                    return
                end
        
                local i, j = string.find(text, "*")
                if i ~= nil and j ~= nil then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
                    self.node_list.desc_cr_input.input_field.text = ""
                    return
                end
        
                local len = string.len(text)
                local qukong_text = string.gsub(text, "%s", "")
                local qukong_text_len = string.len(qukong_text)
                --判断输入的名字是否带空格
                if qukong_text_len ~= len then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
                    self.node_list.desc_cr_input.input_field.text = ""
                    return
                end
    
                local ok_func = function ()
                    CustomizedRumorsWGCtrl.Instance:SendRumorRequest(self.select_rumor_type, text, function ()
                        CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.CHOOSE_DESC, self.select_rumor_type, self.select_msg_seq, nil, text)
                    end)
                end
    
                TipWGCtrl.Instance:OpenAlertTips(string.format(Language.CustomizedRumors.ChangeRumorsDesc, self.type_data.rumor_gear_name, text), ok_func)
            end
        end
    elseif select_desc_seq == -1 then
        local status = CustomizedRumorsWGData.Instance:IsCustomizedRumorByRumorType(self.select_rumor_type)
        if status == DIYCHUANWEN_OPERA_STATUS.SUC then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.IsCustomized)
            return
        elseif status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.IsCustomizedNow)
            return
        else
            -- 状态在normal状态
            if not CustomizedRumorsWGData.Instance:IsCanCustomizesRumorByRumorType(self.select_rumor_type) then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.NotActiveThisRumor)
                return
            end
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.IsCustomized)
    end
end

function CustomizaedRumorsSetView:OnClicRebuild()
    local ok_func = function ()
        CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.CHOOSE_DESC, self.select_rumor_type, 0)
    end

    local rebuild_func = function ()
        -- 消耗
        local cost_item_id =  self.select_rumor_type_data.reset_cost_itemid 
        local cost_item_num =  self.select_rumor_type_data.reset_cost_item_num            
        local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

        if has_num >= cost_item_num then
            TipWGCtrl.Instance:OpenAlertTips(string.format(Language.CustomizedRumors.RebuildRumor,  self.select_rumor_type_data.rumor_gear_name), ok_func)
        else
            TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
        end
    end

    local select_desc_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(self.select_rumor_type)
    
    if select_desc_seq == -1 then
        local status = CustomizedRumorsWGData.Instance:IsCustomizedRumorByRumorType(self.select_rumor_type)

        if status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.IsCustomizedNow)
            return
        end

        if status == DIYCHUANWEN_OPERA_STATUS.SUC then
            rebuild_func()
        end
    else
        rebuild_func()
    end
end

------------------------------------------CustomizedRumorsSetListItemRender---------------------------------------
CustomizedRumorsSetListItemRender = CustomizedRumorsSetListItemRender or BaseClass(BaseRender)

function CustomizedRumorsSetListItemRender:LoadCallBack()
    if not self.special_rumor then
        self.special_rumor = BaseSpecialRumorsCell.New(self.node_list.special_rumor_root)
    end
end

function CustomizedRumorsSetListItemRender:__delete()
    if self.special_rumor then
        self.special_rumor:DeleteMe()
        self.special_rumor = nil
    end
end

function CustomizedRumorsSetListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rumor_cfg = CustomizedRumorsWGData.Instance:GetRumorShowInfoByRumorType(self.data.rumor_type)

    -- if rumor_cfg.rumor_bg then
    --     local bg_bundle, bg_asset = ResPath.GetRawImagesPNG(rumor_cfg.rumor_bg)
    --     self.node_list.icon.raw_image:LoadSprite(bg_bundle, bg_asset, function ()
    --         self.node_list.icon.raw_image:SetNativeSize()
    --     end)
    -- end

    local rumor_data = {
        rumor_type = rumor_cfg.rumor_type,
        desc_rumors_content = "",
        show_yoyo_tween = false,
    }

    self.special_rumor:SetData(rumor_data)
    local unlock = CustomizedRumorsWGData.Instance:IsRumorActiveByRumorType(self.data.seq)
    self.node_list.lock:CustomSetActive(not unlock)
end

function CustomizedRumorsSetListItemRender:OnSelectChange(is_select)
    self.node_list.bg_hl:CustomSetActive(is_select)
end


-------------------------------------CustomizedRumorsSetMsgListItemRender--------------------------------
CustomizedRumorsSetMsgListItemRender = CustomizedRumorsSetMsgListItemRender or BaseClass(BaseRender)
function CustomizedRumorsSetMsgListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_content.text.text = self.data.desc
    local select_desc_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(self.data.rumor_type)
    self.node_list.flag_is_select:CustomSetActive(self.data.desc_seq == select_desc_seq)
end

function CustomizedRumorsSetMsgListItemRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end