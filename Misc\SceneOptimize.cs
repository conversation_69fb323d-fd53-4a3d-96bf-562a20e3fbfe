﻿using LuaInterface;
using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.SceneManagement;

#if UNITY_EDITOR
using UnityEditor;
#endif

[ExecuteInEditMode]
public class SceneOptimize : MonoBehaviour
{
    private class MeshItem
    {
        public Mesh mesh;
        public int refCount;
        public List<GameObject> renderObjList;
    }

    private struct CullingItem
    {
        public Transform transform;
        public Renderer[] renderers;
        public bool active;
        public CameraCullingDisType cullingDisType;
        public Vector3 position;
    }

    private List<CullingItem> cullingList = new List<CullingItem>();
    private float[] cullingDistance = null;
    private float cullingDistanceFactor = 1;
    private bool isBatched = true; //先屏蔽静态合批，会导致lightmap错误
    private bool isSyncLightMap = false;
    private float nextCheckCullTime = 0;
    private Vector3 lastCheckCullPos = Vector3.zero;

    [HideInInspector] 
    public bool isCullEnable = true;
    [HideInInspector]
    public bool isIgnoreEffectCulling = false;
    [HideInInspector]
    public bool isAllowHighPerformance = false;
    [HideInInspector]
    public float minRecullingDistance = 0.5f;
    [HideInInspector]
    public float minRecullingElapsedTime = 0.1f;
    [NoToLua]
    [HideInInspector]
    public Action updateDelegate = null;

    public bool CullingByDisableGameObejct { get; set; } = true;

    private Transform centerPoint;
    private SceneOptimizeHelper sceneHelper = new SceneOptimizeHelper();
    private Dictionary<PlanarReflection, bool> planerRefletionDic;

    private int qualityLevel = 0;


    public void SetCenterPoint(Transform centerPoint)
    {
        this.centerPoint = centerPoint;
    }

    public void SetCullingDistances(float[] layerDistances)
    {
        this.cullingDistance = layerDistances;
    }

    public void SetCullingDistanceFactor(float factor)
    {
        cullingDistanceFactor = factor;
    }

    public void SetCullEnable(bool enable, bool immediately)
    {
        //先屏蔽掉运行时修改，后续cull都用lodgroup代替
        //isCullEnable = enable;
        if (!isCullEnable && immediately)
        {
            if (null == cullingDistance)
            {
                return;
            }

            int len = cullingList.Count;
            for (int i = 0; i <= len - 1; i++)
            {
                CullingItem item = cullingList[i];
                CameraCullingDisType cullingDisType = item.cullingDisType;
                if (CameraCullingDisType.CullNone == cullingDisType || (int)cullingDisType >= cullingDistance.Length)
                {
                    continue;
                }

                Transform transform = item.transform;
                if (null == transform)
                {
                    continue;
                }

                SetCullingState(i, true);
            }
        }
    }

    private void Awake()
    {
        if (this.gameObject.name != "Main")
        {
            Debug.LogError("请将SceneOptimize脚本挂在Main节点上");
            return;
        }

#if UNITY_EDITOR
        sceneHelper.CheckScene(this.isAllowHighPerformance, this.isIgnoreEffectCulling);
#endif

        cullingList.Clear();
        this.GetAllObjects(cullingList);
        this.ForbidEffectResortOrder();
    }

    private void Update()
    {
        if(isCullEnable)
            this.UpdateCull();

#if UNITY_EDITOR
        if (null != updateDelegate)
        {
            updateDelegate();
        }
#endif
    }

    private void UpdateCull()
    {
        if (null == cullingDistance || null == Camera.main)
        {
            return;
        }

        if (null == centerPoint)
        {
            centerPoint = Camera.main.transform;
            return;
        }

        if (Time.time - nextCheckCullTime < minRecullingElapsedTime)
        {
            return;
        }

        if (SqrDistance(lastCheckCullPos, Camera.main.transform.position) < minRecullingDistance * minRecullingDistance || Time.time - nextCheckCullTime < minRecullingElapsedTime)
        {
            return;
        }

        this.nextCheckCullTime = Time.time;
        lastCheckCullPos = Camera.main.transform.position;
        for (int i = 0; i <= cullingList.Count - 1; i++)
        {
            CullingItem item = cullingList[i];
            CameraCullingDisType cullingDisType = item.cullingDisType;
            if (CameraCullingDisType.CullNone == cullingDisType || (int)cullingDisType >= cullingDistance.Length)
            {
                continue;
            }

            Transform transform = item.transform;
            if (null == transform)
            {
                continue;
            }

            if (!isCullEnable)
            {
                SetCullingState(i, true);
                continue;
            }

            float maxDistance = (cullingDistance[(int)cullingDisType] * cullingDistanceFactor) * (cullingDistance[(int)cullingDisType] * cullingDistanceFactor);
            if (maxDistance == 0)
            {
                SetCullingState(i, true);
                continue;
            }

            var vector = item.position - centerPoint.position;
            if (vector.sqrMagnitude > maxDistance)
            {
                SetCullingState(i, false);
            }
            else
            {
                SetCullingState(i, true);
            }
        }
    }

    private float SqrDistance(Vector3 a, Vector3 b)
    {
        float sqr_x = (a.x - b.x) * (a.x - b.x);
        float sqr_y = (a.y - b.y) * (a.y - b.y);
        float sqr_z = (a.z - b.z) * (a.z - b.z);
        return sqr_x + sqr_y + sqr_z;
    }

    private void ForbidEffectResortOrder()
    {
        //         GameObject effectsObj = GameObject.Find("Main/Effects");
        //         if (null != effectsObj)
        //         {
        //             GameObjectAttach[] attaches = effectsObj.GetComponentsInChildren<GameObjectAttach>();
        //             for (int i = 0; i < attaches.Length; i++)
        //             {
        //                 if (null != attaches[i]) attaches[i].SetIsNeedResortOrder(false);
        //             }
        //         }
    }

    private void GetAllObjects(List<CullingItem> cullingList)
    {
        //GetObjects("Main/Models/Terrain", cullingList);
        GetObjects("Main/Models/Stone", cullingList);
        GetObjects("Main/Models/Plant", cullingList);
        GetObjects("Main/Models/TempObjs", cullingList);
        GetObjects("Main/Models/Others", cullingList);
        GetObjects("Main/Animations", cullingList);
        GetObjects("Main/Waters", cullingList);
        Debug.LogFormat("total CullingObj {0}", cullingList.Count);
    }

    private void GetObjects(string path, List<CullingItem> cullingList)
    {
        GameObject rootObj = GameObject.Find(path);
        if (null == rootObj)
        {
            //Debug.LogErrorFormat("找不到节点 {0}，可能将影响性能", path);
            return;
        }

        int childCount = rootObj.transform.childCount;
        CullingItem item = new CullingItem();
        for (int i = 0; i < childCount; i++)
        {
            Transform child = rootObj.transform.GetChild(i);
            if (null == child || child.GetComponent<OptimizeFlag>())
            {
                continue;
            }
            
            CameraCullingDisType cullDisType = CalcCullDistance(child.gameObject, out Renderer[] renderers, out Bounds bounds);
            if (cullDisType == CameraCullingDisType.MAX)
            {
                continue;
            }

            item.cullingDisType = cullDisType;
            item.transform = child;
            item.renderers = renderers;
            item.active = child.gameObject.activeInHierarchy;
            item.position = bounds.center;
            cullingList.Add(item);
        }
    }

    private CameraCullingDisType CalcCullDistance(GameObject gameObject, out Renderer[] renderers, out Bounds bounds)
    {
        int vertexCount;
        bool isAlphaTest;
        if (!GetObjectInfo(gameObject, out bounds, out vertexCount, out isAlphaTest, out renderers))
        {
            return CameraCullingDisType.MAX;
        }

        CameraCullingDisType cullDisType = CameraCullingDisType.CullNone;
        Vector3 size = bounds.size;
        float sqlDis = size.sqrMagnitude;

        // 根据距离,越小的物件越容易被裁剪掉
        if (sqlDis < 10 * 10) cullDisType = CameraCullingDisType.Cull30;            //100
        else if (sqlDis < 25 * 25) cullDisType = CameraCullingDisType.Cull50;       //625
        else if (sqlDis < 30 * 30) cullDisType = CameraCullingDisType.Cull70;       //900
        else if (sqlDis < 35 * 35) cullDisType = CameraCullingDisType.Cull90;       //1225
        else if (sqlDis < 40 * 40) cullDisType = CameraCullingDisType.Cull120;      //1600
        else if (sqlDis < 50 * 50) cullDisType = CameraCullingDisType.Cull150;      //2500
        else if (sqlDis < 60 * 60) cullDisType = CameraCullingDisType.Cull180;      //3600
        else if (sqlDis < 80 * 80) cullDisType = CameraCullingDisType.Cull200;      //6400
        else if (sqlDis < 100 * 100) cullDisType = CameraCullingDisType.Cull220;    //10000
        else if (sqlDis < 150 * 150) cullDisType = CameraCullingDisType.Cull240;    //22500
        else if (sqlDis < 200 * 200) cullDisType = CameraCullingDisType.Cull260;    //40000
        else if (sqlDis < 250 * 250) cullDisType = CameraCullingDisType.Cull280;    //62500
        else if (sqlDis < 300 * 300) cullDisType = CameraCullingDisType.Cull300;    //90000

        // 尺寸比例
        if (size.x > 0 && size.y > 0 && size.z > 0 && cullDisType < CameraCullingDisType.Cull120)
        {
            for (int factor = 4; factor >= 2; factor--)
            {
                if (size.x / size.y >= factor || size.y / size.x >= factor
             || size.x / size.z >= factor || size.z / size.x >= factor
             || size.y / size.z >= factor || size.z / size.y >= factor)
                {
                    cullDisType += (int)(factor / 2);
                    break;
                }
            }
        }

        // 如果是非AlphaTest则允许更远处看到
        if (!isAlphaTest && cullDisType < CameraCullingDisType.Cull70 && vertexCount < 3000)
        {
            cullDisType += 2;
        }

        // 顶点数太大则减少裁剪距离
        if (vertexCount > 8000 && cullDisType > CameraCullingDisType.Cull150 && isAlphaTest)
        {
            cullDisType -= 1;
        }

        // 某些顶点数太大，自动生成剔除距离又过远，可手动调整
        if (gameObject.layer == GameLayers.NotImportantCulling)
        {
            cullDisType -= 1;
        }
        if (gameObject.layer == GameLayers.NotImportantCulling2)
        {
            cullDisType -= 2;
        }

        // 由美术特别指定某个物体裁剪重要级别。
        if (gameObject.layer == GameLayers.ImportantCulling2)
        {
            cullDisType += 2;
        }
        // 这个慎用，指定obj不裁剪
        if (gameObject.layer == GameLayers.ImportantCulling3)
        {
            cullDisType = CameraCullingDisType.MAX;
        }

        if (cullDisType <= CameraCullingDisType.Cull30) cullDisType = CameraCullingDisType.Cull30;
        if (cullDisType >= CameraCullingDisType.MAX) cullDisType = CameraCullingDisType.CullNone;

        return cullDisType;
    }

    private bool GetObjectInfo(GameObject gameObject, out Bounds bounds, out int vertexCount, out bool isAlphaTest, out Renderer[] renderers)
    {
        //if (gameObject.name == "Plant"
        //    || gameObject.name == "Stone"
        //    || gameObject.name == "Building"
        //    || gameObject.name == "Others"
        //    || gameObject.name == "Terrain")
        //{
        //    size = Vector3.zero;
        //    vertexCount = 0;
        //    isAlphaTest = false;
        //    return false;
        //}

        renderers = gameObject.GetComponentsInChildren<Renderer>();
        if (renderers.Length == 0)
        {
            bounds = default;
            vertexCount = 0;
            isAlphaTest = false;
            return false;
        }

        // 检查GameObject位置是否有效
        if (!IsValidPosition(gameObject.transform.position))
        {
            bounds = default;
            vertexCount = 0;
            isAlphaTest = false;
            return false;
        }

        if (renderers.Length == 1)
        {
            // 检查单个渲染器的边界是否有效
            if (!IsValidBounds(renderers[0].bounds))
            {
                bounds = default;
                vertexCount = 0;
                isAlphaTest = false;
                return false;
            }

            bounds = renderers[0].bounds;
            vertexCount = GetRenderVertexCount(renderers[0]);
            isAlphaTest = false;
            if (renderers[0].sharedMaterial)
            {
                isAlphaTest = renderers[0].sharedMaterial.IsKeywordEnabled("ENABLE_ALPHA_TEST");
            }
        }
        else
        {
            vertexCount = 0;
            isAlphaTest = false;
            bounds = new Bounds(gameObject.transform.position, Vector3.zero);

            for (int i = 0; i < renderers.Length; i++)
            {
                // 检查每个渲染器的边界是否有效
                if (!IsValidBounds(renderers[i].bounds))
                {
                    continue; // 跳过无效的渲染器
                }

                vertexCount += GetRenderVertexCount(renderers[i]);
                bounds.Encapsulate(renderers[i].bounds);

                if (renderers[i].sharedMaterial && !isAlphaTest)
                {
                    isAlphaTest = renderers[i].sharedMaterial.IsKeywordEnabled("ENABLE_ALPHA_TEST");
                }
            }

            // 最终检查合并后的边界是否有效
            if (!IsValidBounds(bounds))
            {
                bounds = default;
                vertexCount = 0;
                isAlphaTest = false;
                return false;
            }
        }

        return vertexCount > 0;
    }

    private int GetRenderVertexCount(Renderer renderer)
    {
        MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
        if (null != meshFilter)
        {
            if (null == meshFilter.sharedMesh) return 0;
            return meshFilter.sharedMesh.vertexCount;
        }

        SkinnedMeshRenderer meshRender = renderer.GetComponentInChildren<SkinnedMeshRenderer>();
        if (null != meshRender)
        {
            if (null == meshRender.sharedMesh) return 0;
            return meshRender.sharedMesh.vertexCount;
        }

        return 0;
    }

    /// <summary>
    /// 检查3D位置是否有效（不包含NaN或Infinity）
    /// </summary>
    private bool IsValidPosition(Vector3 position)
    {
        return !float.IsNaN(position.x) && !float.IsNaN(position.y) && !float.IsNaN(position.z) &&
               !float.IsInfinity(position.x) && !float.IsInfinity(position.y) && !float.IsInfinity(position.z);
    }

    /// <summary>
    /// 检查边界框是否有效（不包含NaN或Infinity）
    /// </summary>
    private bool IsValidBounds(Bounds bounds)
    {
        return IsValidPosition(bounds.center) && IsValidPosition(bounds.size) &&
               bounds.size.x >= 0 && bounds.size.y >= 0 && bounds.size.z >= 0;
    }

    private void SetCullingState(int itemIndex, bool active)
    {
        CullingItem item = cullingList[itemIndex];
        if (CullingByDisableGameObejct)
        {
            
            // if (item.transform.gameObject.activeSelf != active)
            // {
            //     item.transform.gameObject.SetActive(active);
            // }
            
            if (item.active != active)
            {
                item.transform.gameObject.SetActive(active);

                item.active = active;
                cullingList[itemIndex] = item;
            }
        }
        else
        {
            if (item.active != active)
            {
                foreach (var renderer in item.renderers)
                {
                    renderer.forceRenderingOff = !active;
                }

                item.active = active;
                cullingList[itemIndex] = item;
            }
        }
    }

    public Dictionary<PlanarReflection, bool> CalcPlanerRefletionList()
    {
        if (this.planerRefletionDic == null)
        {
            this.planerRefletionDic = new Dictionary<PlanarReflection, bool>();
            GameObject[] rootObjs = new GameObject[] { GameObject.Find("Main/Waters") };
            for (int i = 0; i < rootObjs.Length; i++)
            {
                if (null == rootObjs[i])
                {
#if UNITY_EDITOR
                    Debug.LogErrorFormat("没有找处理对象Main/Waters，将影响性能，请检查！当前场景:{0}", SceneManager.GetActiveScene().name);
#endif
                    continue;
                }

                var planar = ListPool<PlanarReflection>.Get();
                rootObjs[i].GetComponentsInChildren<PlanarReflection>(true, planar);
                for (int m = 0; m < planar.Count; m++)
                {
                    if (!this.planerRefletionDic.ContainsKey(planar[m]))
                    {
                        this.planerRefletionDic.Add(planar[m], planar[m].enabled);
                    }
                }
                ListPool<PlanarReflection>.Release(planar);
            }
        }

        return this.planerRefletionDic;
    }

    public void UpdatePlanarReflectionShow(int level)
    {
        if (this.qualityLevel != level)
        {
            if (this.qualityLevel > 0 && level > 0)
            {
                this.qualityLevel = level;
                return;
            }
        }
        else
        {
            return;
        }

        this.qualityLevel = level;
        this.planerRefletionDic = CalcPlanerRefletionList();
        bool isShow = level == 0 ? true : false;
        List<PlanarReflection> l = new List<PlanarReflection>(this.planerRefletionDic.Keys);

        for (int i = 0; i < l.Count; i++)
        {
            bool v = false;
            if (this.planerRefletionDic.TryGetValue(l[i], out v))
            {
                if (v != isShow)
                {
                    l[i].enabled = isShow;
                    this.planerRefletionDic[l[i]] = isShow;
                }
            }
        }
    }

    public void SyncLODLightMap()
    {
        if (isSyncLightMap)
        {
            return;
        }

        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，将影响LOD光照贴图");
            return;
        }
        isSyncLightMap = true;


        LODGroup[] groups = rootObj.GetComponentsInChildren<LODGroup>();
        for (int i = 0; i < groups.Length; ++i)
        {
            var group = groups[i];
            if (group.lodCount < 2)
            {
                Debug.LogError(string.Format("{0}结点上的LOD组件异常", group.name));
                continue;
            }

            var lods = group.GetLODs();
            var lod0 = lods[0];
            if (lod0.renderers.Length < 1)
            {
                Debug.LogError(string.Format("{0}结点上的LOD组件异常", group.name));
                continue;
            }

            for (int j = 1; j < lods.Length; ++j)
            {
                var lod = lods[j];
                if (lod.renderers.Length < 1)
                {
                    Debug.LogError(string.Format("{0}结点上的LOD组件异常", group.name));
                    continue;
                }

                var renderer0 = lod0.renderers[0];
                var renderer = lod.renderers[0];

                renderer.lightmapIndex = renderer0.lightmapIndex;
                renderer.lightmapScaleOffset = renderer0.lightmapScaleOffset;
            }
        }
    }

    public void StaticBatch(int maxVertexCountInSameMesh, int maxVertexCountInOneMesh)
    {
        if (isBatched)
        {
            return;
        }

        GameObject rootObj = GameObject.Find("Main/Models");
        if (null == rootObj)
        {
            Debug.LogError("没有找到Main/Models标记，将影响静态合批，极大降低性能");
            return;
        }

        GameObject lightObj = GameObject.Find("Hero light");
        if (null != lightObj)
        {
            Light light = lightObj.GetComponent<Light>();
            light.shadows = LightShadows.None;
        }

        GameObject bakeLights = GameObject.Find("Lights");
        if (null != bakeLights)
        {
            bakeLights.SetActive(false);
        }

        Dictionary<Mesh, MeshItem> meshDic = new Dictionary<Mesh, MeshItem>();
        MeshFilter[] meshFilters = rootObj.GetComponentsInChildren<MeshFilter>();

        for (int i = 0; i < meshFilters.Length; i++)
        {
            MeshFilter meshFilter = meshFilters[i];
            Mesh mesh = meshFilter.sharedMesh;
            if (null == mesh)
            {
                continue;
            }

            MeshItem meshItem;
            if (!meshDic.TryGetValue(mesh, out meshItem))
            {
                meshItem = new MeshItem();
                meshItem.mesh = mesh;
                meshItem.refCount = 1;
                meshItem.renderObjList = new List<GameObject>() { meshFilter.gameObject };
                meshDic.Add(mesh, meshItem);
            }
            else
            {
                ++meshItem.refCount;
                meshItem.renderObjList.Add(meshFilter.gameObject);
            }
        }

        List<GameObject> batchList = new List<GameObject>();
        List<GameObject> lodBatchList = new List<GameObject>();
        var iter = meshDic.GetEnumerator();
        while (iter.MoveNext())
        {
            var kv = iter.Current;
            //if ((kv.Key.vertexCount >= 6000 && kv.Value.refCount >= 2)
            //  || (kv.Key.vertexCount >= 5000 && kv.Value.refCount >= 3))
            var count = kv.Key.vertexCount * kv.Value.refCount;
            if (maxVertexCountInSameMesh > 0 && count > maxVertexCountInSameMesh)
            {
                Debug.LogFormat("vertex count, {0},  {1}, {2} {3}", kv.Value.renderObjList[0], kv.Key.vertexCount, kv.Value.refCount, count);
                continue;
            }

            if (maxVertexCountInOneMesh > 0 && kv.Key.vertexCount >= maxVertexCountInOneMesh)  //显存顶不了了，，，，，DC换显存
            {
         //       Debug.LogFormat("not batch obj, {0},  {1}, {2}", kv.Value.renderObjList[0], kv.Key.vertexCount, kv.Value.refCount);
                continue;
            }

            var renderObjList = kv.Value.renderObjList;
            for (int i = 0; i < renderObjList.Count; i++)
            {
                if (kv.Key.name.Contains("_LOD"))
                {
                    lodBatchList.Add(renderObjList[i]);
                }
                else
                {
                    batchList.Add(renderObjList[i]);
                }
            }
        }

        UnityEngine.StaticBatchingUtility.Combine(batchList.ToArray(), rootObj);
        //     UnityEngine.StaticBatchingUtility.Combine(lodBatchList.ToArray(), rootObj);  ///显存顶不了了，，，，，DC换显存
        isBatched = true;

        meshDic.Clear();
    }


#if UNITY_EDITOR
    [NoToLua]
    public void QuickOptimize()
    {
        this.sceneHelper.QuickOptimize();
    }

    [NoToLua]
    public Dictionary<CameraCullingDisType, List<Transform>> GetFilterCullingObjects()
    {
        Dictionary<CameraCullingDisType, List<Transform>> cullingObjects = new Dictionary<CameraCullingDisType, List<Transform>>();
        List<CullingItem> cullingList = new List<CullingItem>();
        GetAllObjects(cullingList);
        foreach (var item in cullingList)
        {
            List<Transform> list;
            if (!cullingObjects.TryGetValue(item.cullingDisType, out list))
            {
                list = new List<Transform>();
                cullingObjects.Add(item.cullingDisType, list);
            }

            list.Add(item.transform);
        }

        return cullingObjects;
    }

    [NoToLua]
    public void AutoSetOcclusion()
    {
        List<CullingItem> cullingList = new List<CullingItem>();
        //GetObjects("Main/Models/Terrain", cullingList);
        GetObjects("Main/Models/Stone", cullingList);
        GetObjects("Main/Models/Plant", cullingList);
        GetObjects("Main/Models/Building", cullingList);
        GetObjects("Main/Models/Others", cullingList);
        GetObjects("Main/Waters", cullingList);
        foreach (var item in cullingList)
        {
            var flag = GameObjectUtility.GetStaticEditorFlags(item.transform.gameObject);
            if (item.cullingDisType >= CameraCullingDisType.Cull150)
            {
                flag = flag | StaticEditorFlags.OccluderStatic;
                flag = flag | StaticEditorFlags.OccludeeStatic;
                GameObjectUtility.SetStaticEditorFlags(item.transform.gameObject, flag);
            }
            else
            {
                flag = flag | StaticEditorFlags.OccludeeStatic;
                flag = flag & (~StaticEditorFlags.OccluderStatic);
                GameObjectUtility.SetStaticEditorFlags(item.transform.gameObject, flag);
            }
        }

        cullingList.Clear();
        GetObjects("Main/Animations", cullingList);
        GetObjects("Main/JumpWalk", cullingList);
        GetObjects("Colliders", cullingList);
        foreach (var item in cullingList)
        {
            var flag = GameObjectUtility.GetStaticEditorFlags(item.transform.gameObject);
            flag = flag & (~StaticEditorFlags.OccludeeStatic);
            flag = flag & (~StaticEditorFlags.OccluderStatic);
            GameObjectUtility.SetStaticEditorFlags(item.transform.gameObject, flag);
        }
        GetObjects("Main/Effects", cullingList);
    }

#endif
}
