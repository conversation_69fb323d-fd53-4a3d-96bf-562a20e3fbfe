ZhouYiYunChengWGData = ZhouYiYunChengWGData or BaseClass()
function ZhouYiYunChengWGData:__init()
	if ZhouYiYunChengWGData.Instance then
		error("[ZhouYiYunChengWGData] Attempt to create singleton twice!")
		return
	end
	ZhouYiYunChengWGData.Instance = self
	RemindManager.Instance:Register(RemindName.ZhouYi_YunCheng, BindTool.Bind(self.GetZhouYiYunChengRed, self))
	--self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	local all_cfg = ConfigManager.Instance:GetAutoConfig("ra_happy_monday_auto")
	self.task_cfg = ListToMap(all_cfg.task,"task_type")
	self.item_rewared_cfg = ListToMap(all_cfg.reward_level,"level")
	self.other_cfg = all_cfg.other[1]
	self.jump_yuncheng_ani = false

	self.notify_flush = BindTool.Bind(self.CheckActiveChange,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.notify_flush)

	self.first_login_flush_remind_flag = false
	self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function ZhouYiYunChengWGData:__delete()
	ZhouYiYunChengWGData.Instance = nil

	if self.notify_flush then
   		ActivityWGData.Instance:UnNotifyActChangeCallback(self.notify_flush)
    	self.notify_flush = nil
    end

	if self.remind_time_quest then
		GlobalTimerQuest:CancelQuest(self.remind_time_quest)
		self.remind_time_quest = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	self.first_login_flush_remind_flag = nil
end

function ZhouYiYunChengWGData:CheckActiveChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG and status == ACTIVITY_STATUS.OPEN then
		self:CheckNeedReqInfo()
	end
end

function ZhouYiYunChengWGData:GetZhouYiYunChengRed()
	--红点增加活动开启判断
	local is_fun_open = self:GetZhouYiYunChengFunOpen()
	if not is_fun_open then
		return 0
	end

	if self.task_finish_flag and self.draw_times_fetch_flag then
		local finish_flag = bit:d2b(self.task_finish_flag)
		local draw_flag = bit:d2b(self.draw_times_fetch_flag)
		for k,v in pairs(self.task_cfg) do
			if finish_flag[32-v.task_type] == 1 and draw_flag[32-v.task_type] == 0 then
				return 1
			end
		end
	end

	if not ZhouYiYunChengWGData.Instance:IsAvalibaleTime() then
		return 0
	end

	if self.can_draw_times and self.can_draw_times > 0 then 
		return 1
	end

	return 0
end

function ZhouYiYunChengWGData:CheckIfNeedRemindTimeQuest()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG)

	if activity_status and activity_status.status == ACTIVITY_STATUS.OPEN then
		self.remind_time_quest =  GlobalTimerQuest:AddRunQuest(function()
			RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
		end, 5)
	else
		if self.remind_time_quest then
			GlobalTimerQuest:CancelQuest(self.remind_time_quest)
			self.remind_time_quest = nil
		end
		RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
	end
end

function ZhouYiYunChengWGData:OnDayChange()
	-- local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- local index = -1
	-- for k,v in pairs(self.lingxi_item_cfg) do
	-- 	if k <= open_day then
	-- 		if index == -1 then
	-- 			index = k
	-- 		elseif index < k then
	-- 			index = k
	-- 		end
	-- 	end
	-- end
	-- self.cur_xianling_item_data = self.lingxi_item_cfg[index] or {}
end

function ZhouYiYunChengWGData:OnSCRAHappyMondayInfo(protocol)
	self.task_finish_flag = protocol.task_finish_flag --任务完成标记
	self.draw_times_fetch_flag = protocol.draw_times_fetch_flag -- 抽奖次数领取标记
	self.can_draw_times = protocol.can_draw_times --  可抽奖次数
	self.bounus_draw_times = protocol.bounus_draw_times -- 保底抽奖次数
	self.choose_reward_level = protocol.choose_reward_level -- 选择的奖励列表
	if nil == self.remind_time_quest then
		self:CheckIfNeedRemindTimeQuest()
	end
end

function ZhouYiYunChengWGData:GetLeftRollTimes()
	return self.can_draw_times or 0,self.bounus_draw_times or -1
end

function ZhouYiYunChengWGData:GetBonusTimes()
	return self.other_cfg.bonus_need_draw_times or 10
end

function ZhouYiYunChengWGData:CheckNeedReqInfo()
	if not self.task_finish_flag or not self.draw_times_fetch_flag or not self.choose_reward_level then
		ZhouYiYunChengWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_REQ_INFO)
		return true
	end
	return false
end

function ZhouYiYunChengWGData:OnSCRAHappyMondayDrawResult(protocol)
	self.reward_level = protocol.reward_level
	self.reward_index = protocol.reward_index
	self.result_item = self.rewared_item_list[self.reward_level + 1][self.reward_index + 1]
	local all_select_item = self:GetAllSelectItemList()
	self.roll_index = -1
	for k,v in pairs(all_select_item) do
		if tonumber(v.item_id) == tonumber(self.result_item.item_id) then
			self.roll_index = k
			break
		end
	end
end

function ZhouYiYunChengWGData:GetRollResult()
	return self.result_item,self.roll_index
end

function ZhouYiYunChengWGData:OnSCRAHappyMondayServerRecord(protocol)
	self.server_draw_record_count = protocol.server_draw_record_count
	self.server_draw_record_item_list = protocol.server_draw_record_item_list
end

function ZhouYiYunChengWGData:OnSCRAHappyMondayPersonRecord(protocol)
	self.person_draw_record_count = protocol.person_draw_record_count
	self.person_draw_record_item_list = protocol.person_draw_record_item_list
end

function ZhouYiYunChengWGData:GetWorldRecord()
	return self.server_draw_record_item_list or {} ,self.server_draw_record_count or 0
end

function ZhouYiYunChengWGData:GetPersonalRecord()
	return self.person_draw_record_item_list or {} ,self.person_draw_record_count or 0
end

function ZhouYiYunChengWGData:GetJumpAni()
	return self.jump_yuncheng_ani or false
end

function ZhouYiYunChengWGData:SetJumpAni()
	self.jump_yuncheng_ani = not self.jump_yuncheng_ani
end

function ZhouYiYunChengWGData:GetItemFlagList()
	if nil == self.choose_reward_level then return {} end
	local item_flag_list = {}
	local had_select = false
	for i = 1,4 do
		item_flag_list[i] = {}
		local flag_list = bit:d2b(self.choose_reward_level[i])
		for q=1,32 do
			item_flag_list[i][q] = flag_list[33-q] == 1
			had_select = had_select and had_select or flag_list[33-q] == 1
		end
	end
	if not had_select then
		self:InitAllSelectItem()
		return {}
	end
	return item_flag_list
end

function ZhouYiYunChengWGData:GetAllItemList()
	if nil == self.rewared_item_list then
		self.rewared_item_list= {}
		for i = 1, 4 do 
			self.rewared_item_list[i] = {}
			local item_data = Split(self.item_rewared_cfg[i-1].reward_item,"|")
			for k,v in pairs(item_data) do
				local item_detail = Split(v,":")
				self.rewared_item_list[i][k] = {}
				self.rewared_item_list[i][k].item_id = item_detail[1]
				self.rewared_item_list[i][k].num = item_detail[2]
				self.rewared_item_list[i][k].is_bind = item_detail[3]
				self.rewared_item_list[i][k].index1 = i
				self.rewared_item_list[i][k].index2 = k
			end
		end
	end
	return self.rewared_item_list or {}
end

function ZhouYiYunChengWGData:GetAllSelectItemList()
	local choose_item_list = {}
	local all_item = self:GetAllItemList()
	local item_flag = self:GetItemFlagList()
	if IsEmptyTable(item_flag) then return {} end
	local num = 0
	for i= 1,4 do
		for k,v in pairs(all_item[i]) do
			if item_flag[i][k] then
				num = num + 1
				choose_item_list[num] = v
			end
		end
	end
	return choose_item_list,num
end

function ZhouYiYunChengWGData:InitAllSelectItem()
	local default_select = {}
	for i = 1 , 4 do
		default_select[i] = {}
		for q = 1, 32 do
			local num = self.item_rewared_cfg[i-1].choose_count
			default_select[i][33-q] = num >= q and 1 or 0
		end
	end
	ZhouYiYunChengWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_CHOOSE_REWARD,bit:b2d(default_select[1]),bit:b2d(default_select[2]),bit:b2d(default_select[3]),bit:b2d(default_select[4]))
	return 
end

function ZhouYiYunChengWGData:GetAllTaskDetail()
	local finish_flag = {}
	local get_flag = {}
	if self.task_finish_flag and self.draw_times_fetch_flag then
		finish_flag =  bit:d2b(self.task_finish_flag)
		get_flag = bit:d2b(self.draw_times_fetch_flag)
	end
	local task_data_list = {}
	for k,v in pairs(self.task_cfg) do
		task_data_list[k] = {}
		task_data_list[k].task_type = v.task_type
		task_data_list[k].complete_param = v.complete_param
		task_data_list[k].des = v.des or ""
		task_data_list[k].finish_flag = finish_flag[32-k] == 1
		task_data_list[k].get_flag = get_flag[32-k] == 1
	end
	return task_data_list
end


function ZhouYiYunChengWGData:TryToSelectOrUnSelectThis(index1,index2)
	if not self.tem_select_list then return end
	local limite_num = self.item_rewared_cfg[index1-1].choose_count
	if self.tem_select_list[index1][index2] then
		self.tem_select_list[index1][index2] = not self.tem_select_list[index1][index2]
		self.tem_select_num_list[index1] = self.tem_select_num_list[index1] - 1
		return true
	elseif not self.tem_select_list[index1][index2] and limite_num > self.tem_select_num_list[index1] then
		self.tem_select_list[index1][index2] = not self.tem_select_list[index1][index2]
		self.tem_select_num_list[index1] = self.tem_select_num_list[index1] + 1
		return true
	end
	return false
end

function ZhouYiYunChengWGData:GetItemIsBeenSelect(index1,index2)
	return self.tem_select_list[index1][index2] or false
end

function ZhouYiYunChengWGData:CreateTempSelectList()
	self.tem_select_list = ZhouYiYunChengWGData.Instance:GetItemFlagList()
	self.tem_select_num_list = {}
	for i = 1, 4 do
		local num = 0
		for k,v in pairs(self.tem_select_list[i]) do
			if v then
				num = num + 1
			end
		end
		self.tem_select_num_list[i] = num
	end
end

function ZhouYiYunChengWGData:GetHaoManyWeSelect(index1)
	local limite_num = self.item_rewared_cfg[index1-1].choose_count
	local select_num = self.tem_select_num_list[index1]
	return select_num,limite_num
end

function ZhouYiYunChengWGData:GetTempSelectList()
	return self.tem_select_list or {}, self.tem_select_num_list or {}
end

function ZhouYiYunChengWGData:IsAvalibaleTime()
	local now_time = TimeWGCtrl.Instance:GetServerTimeFormat()
	local limite_time = Split(self.other_cfg.draw_time,",")
	if tonumber(now_time.wday) == 2 and tonumber(now_time.hour) >= tonumber(limite_time[1]) and tonumber(now_time.hour) < tonumber(limite_time[2]) then
		return true
	end
	return false 
end

function ZhouYiYunChengWGData:GetRewaredLevel(item_id)
	local all_item = self:GetAllItemList()
	for i = 1, 4 do
		for k,v in pairs(all_item[i]) do
			if tonumber(v.item_id) == tonumber(item_id) then
				return tonumber(v.index1)
			end
		end
	end
	return -1
end

--活动功能开启判断
function ZhouYiYunChengWGData:GetZhouYiYunChengFunOpen()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG)
	local level_limit = ActivityWGData.Instance:GetActivityLimitLevelById(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG)
	local my_level = RoleWGData.Instance.role_vo.level
	if not activity_status or activity_status.status ~= ACTIVITY_STATUS.OPEN or my_level < level_limit then
		return false
	end

	return true
end

function ZhouYiYunChengWGData:RoleLevelChange(attr_name, value)
	if attr_name == "level" then
		if not self.first_login_flush_remind_flag then
			local level_limit = ActivityWGData.Instance:GetActivityLimitLevelById(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG)
			if value >= level_limit then
				RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
				self.first_login_flush_remind_flag = true
				--刷新后移除等级监听
				if self.role_data_change then
					RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
					self.role_data_change = nil
				end
			end
		end
	end
end
