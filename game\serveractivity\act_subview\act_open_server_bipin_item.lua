local GoldName = {
	[1] = "灵玉",
	[2] = "元宝",
}

local BiPin_Time_Color = {
	select_color = "#B84F2A",
	normal_color = "#825F3A",
}
------------------------------------------- OpenServerBiPinTopBtnItem -------------------------------------------

OpenServerBiPinTopBtnItem = OpenServerBiPinTopBtnItem or BaseClass(BaseRender)

function OpenServerBiPinTopBtnItem:__init()
	self.bind_gift_is_remind = nil -- 绑玉礼包提示
end

function OpenServerBiPinTopBtnItem:OnSelectChange(is_select)
	self.node_list.img_hl:SetActive(is_select)
	self.node_list.end_time.text.color = Str2C3b(is_select and BiPin_Time_Color.select_color or BiPin_Time_Color.normal_color)
end

function OpenServerBiPinTopBtnItem:OnFlush()
	if not self.data then
		return
	end
	local data = self.data.cfg
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day < data.open_day_index then
		local open_name = string.format(Language.OpenServer.NotOpenName, NumberToChinaNumber(data.open_day_index))
		self.node_list.lbl_name.text.text = open_name
		self.node_list.lbl_activity_name.text.text = open_name
	else
		self.node_list.lbl_name.text.text = data.show_title
		self.node_list.lbl_activity_name.text.text = data.show_title
	end

	if open_day >= data.open_day_index and open_day <= data.close_day_index then
		self.node_list.end_time.text.text = string.format(Language.OpenServer.EndTimeDesc, NumberToChinaNumber(data.close_day_index))
	elseif open_day > data.close_day_index then
		self.node_list.end_time.text.text = Language.Common.ActivityIsEnd
	end

	self.node_list.end_time_root:SetActive(open_day >= data.open_day_index)

	self:OnSelectChange(self:IsSelectIndex())
	self:FlushRemind()
end

function OpenServerBiPinTopBtnItem:FlushRemind()
	local data = self.data.cfg
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if data and open_day >= data.open_day_index then
		local red_2 = false
		if open_day == data.close_day_index then
			if ServerActivityWGData.Instance:GetOGAExtendIsShow() then
				local ret_num = ServerActivityWGData.Instance:GetOGARechargeRemind()
				ret_num = ret_num + ServerActivityWGData.Instance:GetOGALotteryRemind()
				-- ret_num = ret_num + ServerActivityWGData.Instance:GetOGADailyRewardRemind()
				red_2 = ret_num > 0
			end
		end

		local red_1 = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(data.rush_type)

		local show_red_point = red_1
		local bind_gift_is_remind = ServerActivityWGData.Instance:BiPinBindXianYuGiftCanBuyRemind(data.rush_type)
		self.node_list.img_red:SetActive(show_red_point)
	else
		self.node_list.img_red:SetActive(false)
	end
end


------------------------------------------- OpenServerBiPinDailyBuyItem -------------------------------------------

OpenServerBiPinDailyBuyItem = OpenServerBiPinDailyBuyItem or BaseClass(BaseRender)

function OpenServerBiPinDailyBuyItem:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function OpenServerBiPinDailyBuyItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.cell_root)
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))
end

function OpenServerBiPinDailyBuyItem:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local item_data = data.daily_buy_item[0]
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if not item_cfg then
		return
	end

	self.item_cell:SetData(item_data)
	self.node_list.item_name_label.text.text = item_cfg.name

	if data.old_gold and data.need_gold and data.old_gold > 0 then
		local discount = math.floor(data.need_gold / data.old_gold * 10)
		discount = math.max(discount, 1)
		self.node_list.discount_label.text.text = CommonDataManager.GetDaXie(discount) .. Language.MustBuy.Discount
	end

	self.node_list.now_price_label.text.text = data.need_gold or 0

	local icon_name = data.gold_type == 2 and "a2_huobi_bangyu" or "a2_huobi_xianyu"
	local bundle,asset = ResPath.GetCommonIcon(icon_name)
	self.node_list.now_const_icon.image:LoadSprite(bundle, asset)

	self:FlushLimitBuyNum()
end

function OpenServerBiPinDailyBuyItem:FlushLimitBuyNum()
	local data = self:GetData()
	local buy_times = ServerActivityWGData.Instance:GetCompetitionDailyBuyTimes(data.rush_type, data.index)			-- 已购买次数
	self.node_list.limit_label:SetActive(not data.is_limit_time)
	if data.is_limit_time then
		self.node_list.limit_label.text.text = ""
	else
		self.node_list.limit_label.text.text = string.format(Language.OpenServer.DailyBuyLimit, buy_times, data.daily_buy_times)
	end

	local can_buy = buy_times < data.daily_buy_times
	self.node_list.limit_time_label:SetActive(data.is_limit_time)

	self.node_list.all_sell_img:SetActive(not can_buy)
	self.node_list.buy_btn:SetActive(can_buy)
end

function OpenServerBiPinDailyBuyItem:OnClickBuyBtn()
	local data = self:GetData()
	if not data or not data.need_gold then
		return
	end

	local buy_times = ServerActivityWGData.Instance:GetCompetitionDailyBuyTimes(data.rush_type, data.index)
	local item_data = data.daily_buy_item[0]
	local has_num = RoleWGData.Instance:GetMoney(data.gold_type)
	if has_num < data.need_gold then
		if data.gold_type == 1 then
			VipWGCtrl.Instance:OpenTipNoGold()
		else
			VipWGCtrl.Instance:OpenTipNoYuanBao()		
		end

		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.OpenServer.NotEnough, GoldName[data.gold_type]))
		return
	end

	local max_times = data.daily_buy_times - buy_times
	local can_buy_num = math.floor(has_num / data.need_gold)
	local max_num = math.min(max_times, can_buy_num)
	local icon_name = data.gold_type == 2 and "a2_huobi_bangyu" or "a2_huobi_xianyu"
	local asset, bundle = ResPath.GetCommonIcon(icon_name)
	local show_data = {
		item_data = item_data, has_num = has_num, is_show_icon = true, price = data.need_gold, max_num = max_num, icon_asset = asset, icon_bundle = bundle,
		ok_func = function(buy_count)
			if data.is_limit_time then
				ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_LIMIT_TIME_BUY, data.rush_type, data.open_day, item_data.item_id, buy_count)
			else
				ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_DAY_BUY, data.rush_type, data.index - 1, 0, buy_count)
			end
		end
	}

	TipWGCtrl.Instance:ShowTipCommonBuyView(show_data)
end


------------------------------------------- OpenServerRankActItem -------------------------------------------
OpenServerRankActItem = OpenServerRankActItem or BaseClass(BaseRender)
function OpenServerRankActItem:__init()
	self.map_index = 0
end

function OpenServerRankActItem:OnSelectChange(is_select)
	self.node_list.img_hl:SetActive(is_select)
end

function OpenServerRankActItem:OnFlush()
	if not self.data then return end

	self.map_index = self.data.tab_index or 0
	local tab_name = self.data.tab_name or ""
	self.node_list["lbl_name_1"].text.text = tab_name
	self.node_list["lbl_name_2"].text.text = tab_name

	local is_show_red = false
	if self.map_index == TabIndex.act_bipin_normal then
		local red_1 = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(self.data.rush_type)
		is_show_red = red_1
	elseif self.map_index == TabIndex.act_daily_recharge then
		is_show_red = ServerActivityWGData.Instance:GetOGARechargeRemind() > 0
	elseif self.map_index == TabIndex.act_equip_lottery then
		is_show_red = ServerActivityWGData.Instance:GetOGALotteryRemind() > 0
	end

	self.node_list.img_red:SetActive(is_show_red)
end


