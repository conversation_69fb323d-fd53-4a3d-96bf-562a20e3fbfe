-- 仙盟商店 砍价礼包
-- 添加仙盟币 /jy_gm gmroleguild:104 1000 0 0 0

function GuildView:GuildShopReleaseCallBack()
	if self.guild_shop_list then
		self.guild_shop_list:DeleteMe()
		self.guild_shop_list = nil
	end

	if self.bargain_gift_reward_list then
		self.bargain_gift_reward_list:DeleteMe()
		self.bargain_gift_reward_list = nil
	end

	if self.bargain_record_list then
		self.bargain_record_list:DeleteMe()
		self.bargain_record_list = nil
	end

	self:CleanBargainGiftTimer()
end

function GuildView:GuildShopShowIndexCallBack()

end

function GuildView:InitGuildShopView()
	if not self.guild_shop_list then
		self.guild_shop_list = AsyncBaseGrid.New()
		self.guild_shop_list:CreateCells({
			list_view = self.node_list["guild_shop_list"], 
			assetBundle = "uis/view/guild_ui_prefab", 
			assetName = "guild_shop_item", 
			itemRender = GuildStoreItem, 
			change_cells_num = 1, col = 3})
		self.guild_shop_list:SetStartZeroIndex(false)
	end

	if not self.bargain_gift_reward_list then
		self.bargain_gift_reward_list = AsyncListView.New(ItemCell, self.node_list["gift_reward_list"])
		self.bargain_gift_reward_list:SetStartZeroIndex(true)
	end

	if not self.bargain_record_list then
		self.bargain_record_list = AsyncListView.New(GuildBargainRecordItem, self.node_list["bargain_record_list"])
	end

	XUI.AddClickEventListener(self.node_list["btn_bargain"], BindTool.Bind1(self.OnClickBargainBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind1(self.OnClickBuyGiftBtn, self))
end

function GuildView:OnFlushGuildShopView(param_t)
	local list_data = GuildWGData.Instance:GetGuildShopList()
	self.guild_shop_list:SetDataList(list_data)

	self:FlushBargianGiftPart()
end


function GuildView:FlushBargianGiftPart(param_t)
	local bargain_gift_cfg = GuildWGData.Instance:GetCurBargainGiftCfg()
	if not bargain_gift_cfg then
		return
	end
	self.bargain_gift_reward_list:SetDataList(bargain_gift_cfg.reward_item)

	self.node_list["txt_original_price"].text.text = bargain_gift_cfg.price

	local bargain_value = GuildWGData.Instance:GetGuildGiftBargainValue()
	local bargain_price = bargain_gift_cfg.price - bargain_value
	self.node_list["txt_bargain_price"].text.text = bargain_price

	local bargain_times, member_count = GuildWGData.Instance:GetGuildBargainCount()
	self.node_list["txt_bargain_tips"].text.text = string.format(Language.GuildShop.BargainTips, bargain_times, member_count, bargain_value)

	local bargain_record_list = GuildWGData.Instance:GetGuildGiftBargainRecordList()
	self.bargain_record_list:SetDataList(bargain_record_list)

	local bargain_flag = GuildWGData.Instance:GetMyBargainFlag()
	local buy_flag = GuildWGData.Instance:GetBargainGiftBuyFlag()
	self.node_list["btn_bargain"]:SetActive(bargain_flag == 0)
	self.node_list["btn_buy"]:SetActive(bargain_flag ~= 0 and buy_flag == 0)
	self.node_list["buy_flag"]:SetActive(buy_flag ~= 0)

	-- 礼包倒计时
	self:CleanBargainGiftTimer()
	local time = TimeWGCtrl.Instance:GetServerTime()   
    local today_rest_time = TimeUtil.GetTodayRestTime(time)  --获取当天剩余时间
    self:UpdateBargainGiftTimeStr(today_rest_time)
    if today_rest_time > 0 then
        self.gift_refresh_timer = CountDown.Instance:AddCountDown(today_rest_time, 0.5,
        function(elapse_time, total_time)
            local rest_time = math.floor(total_time - elapse_time)
            self:UpdateBargainGiftTimeStr(rest_time)
        end,
        function()
            self:UpdateBargainGiftTimeStr(0)
        end)
    end
end

function GuildView:UpdateBargainGiftTimeStr(time)
    if self.node_list["txt_gift_refresh_time"] then
        self.node_list["txt_gift_refresh_time"].text.text = TimeUtil.FormatSecondDHM9(time)
    end
end

-- 清除倒计时器1
function GuildView:CleanBargainGiftTimer()
    if self.gift_refresh_timer and CountDown.Instance:HasCountDown(self.gift_refresh_timer) then
        CountDown.Instance:RemoveCountDown(self.gift_refresh_timer)
        self.gift_refresh_timer = nil
    end
end

function GuildView:OnClickBargainBtn()
	local bargain_flag = GuildWGData.Instance:GetMyBargainFlag()
	if bargain_flag ~= 0 then
		return
	end

	GuildWGCtrl.Instance:SendCSGuildOperateRequest(GUILD_OPERATE_TYPE.GIFT_BARGAIN)
end

function GuildView:OnClickBuyGiftBtn()
	local buy_flag = GuildWGData.Instance:GetBargainGiftBuyFlag()
	if buy_flag ~= 0 then
		return
	end
	local bargain_gift_cfg = GuildWGData.Instance:GetCurBargainGiftCfg()
	if not bargain_gift_cfg then
		return
	end
	local bargain_value = GuildWGData.Instance:GetGuildGiftBargainValue()
	local bargain_price = bargain_gift_cfg.price - bargain_value

	local alert_tips = string.format(Language.GuildShop.BuyGiftAlert, bargain_price)
	TipWGCtrl.Instance:OpenAlertTips(alert_tips, function()
		GuildWGCtrl.Instance:SendCSGuildOperateRequest(GUILD_OPERATE_TYPE.GIFT_BUY)
	end)
end


-----------------------------------
-- 仙盟商店Item
-----------------------------------
GuildStoreItem = GuildStoreItem or BaseClass(BaseRender)

function GuildStoreItem:__init()
end

function GuildStoreItem:LoadCallBack()

	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_root)
	end
	XUI.AddClickEventListener(self.node_list["btn_click"], BindTool.Bind(self.ClickExchange, self))
	XUI.AddClickEventListener(self.node_list["item_icon"], BindTool.Bind(self.ClickStuff, self))
end

function GuildStoreItem:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuildStoreItem:OnFlush()
	if self.data == nil then
		self.view:SetActive(false)
		return
	end
	self.view:SetActive(true)

	self.item_cell:SetData(self.data.reward_item)
	local name_str = ItemWGData.Instance:GetItemName(self.data.reward_item.item_id)
	self.node_list["name"].text.text = name_str
	
	local limit_buy_str = self.data.can_buy_time ..  "/" .. self.data.buy_times
	limit_buy_str = string.format(Language.GuildShop.LimitBuy[self.data.reset_type], limit_buy_str)
	self.node_list["txt_limit_times"].text.text = limit_buy_str

	local coin_num = GuildWGData.Instance:GetGuildCoin()
	self.node_list["cost"].text.text = coin_num .. "/" .. self.data.price

	--self.node_list["remind"]:SetActive(self.data.can_exchange_times > 0 and num >= self.data.stuff_count)

	local item_icon = ItemWGData.Instance:GetItemIconByItemId(COMMON_CONSTS.ITEM_GUILD_COIN) --拥有的数量
	local bundle, asset = ResPath.GetItem(item_icon)
	self.node_list["item_icon"].image:LoadSprite(bundle, asset, function ()
		self.node_list["item_icon"].image:SetNativeSize()
	end)

	local sold_out = self.data.is_sold_out

	local guild_level = GuildDataConst.GUILDVO.guild_level
	local is_level_limited = guild_level < self.data.guild_level_limit

	self.node_list["txt_limit_desc"]:SetActive(is_level_limited)
	self.node_list["txt_limit_desc"].text.text = string.format(Language.GuildShop.GuildLimiteLevel, self.data.guild_level_limit)
end

function GuildStoreItem:ClickExchange()
	if not self.data then
		return
	end

	if self.data.is_sold_out then
		TipWGCtrl.Instance:ShowSystemMsg(Language.GuildShop.Soldout)
		return
	end

	local guild_level = GuildDataConst.GUILDVO.guild_level
	local is_level_limited = guild_level < self.data.guild_level_limit
	if is_level_limited then
		TipWGCtrl.Instance:ShowSystemMsg(Language.GuildShop.GuildLevelLimited)
		return
	end

	local num = self.data.reward_item.num
	local empty = ItemWGData.Instance:GetEmptyNum()
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.reward_item.item_id)

	if empty < num and item_cfg and item_cfg.not_put_flag == 0 then
		RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
		RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.NORMAL)
		return
	end
	
	local buy_func = function(num)
		TipWGCtrl.Instance:ShowGetItem(self.data.reward_item)
		local buy_num = num or 1
		GuildWGCtrl.Instance:SendCSGuildOperateRequest(GUILD_OPERATE_TYPE.SHOP_BUY, self.data.seq, buy_num)
	end

	local tips_data = {}
	tips_data.title_view_name = Language.Common.BuyItemTipsTitle
	tips_data.item_id = self.data.reward_item.item_id
	tips_data.expend_item_id = COMMON_CONSTS.ITEM_GUILD_COIN
	tips_data.expend_item_num = self.data.price
	tips_data.max_buy_count = self.data.buy_times - self.data.buy_count
	tips_data.is_show_limit = true
	TipWGCtrl.Instance:OpenCustomBuyItemTipsView(tips_data, buy_func)
end

function GuildStoreItem:ClickStuff()
	TipWGCtrl.Instance:OpenItem({item_id = COMMON_CONSTS.ITEM_GUILD_COIN}, ItemTip.FROM_NORMAL, nil)
end

---------------------------------------
-- 砍价记录Item
---------------------------------------
GuildBargainRecordItem = GuildBargainRecordItem or BaseClass(BaseRender)

function GuildBargainRecordItem:OnFlush()
	if not self.data then
		return
	end

	self.node_list["txt_user_name"].text.text = self.data.name
	self.node_list["txt_bargain_value"].text.text = self.data.reduce_value

	local buy_str = ""
	local guild_role_info = GuildWGData.Instance:GetGuildMemberRole(self.data.uid)
	if guild_role_info and guild_role_info.gift_buy_flag ~= 0 then
		buy_str = Language.GuildShop.HasBuy
	end
	self.node_list["txt_buy_state"].text.text = buy_str
end
