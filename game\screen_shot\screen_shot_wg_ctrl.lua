require("game/screen_shot/screen_shot_view")
require("game/screen_shot/snapshot_view")
require("game/screen_shot/screen_shot_wg_data")

ScreenShotWGCtrl = ScreenShotWGCtrl or BaseClass(BaseWGCtrl)

function ScreenShotWGCtrl:__init()
    if nil ~= ScreenShotWGCtrl.Instance then
		ErrorLog("[ScreenShotWGCtrl]:Attempt to create singleton twice!")
	end
	ScreenShotWGCtrl.Instance = self

    self.data = ScreenShotWGData.New()
    self.view = ScreenShotView.New(GuideModuleName.ScreenShotView)
    self.snapshot_view = SnapShotView.New(GuideModuleName.SnapShotView)
    self.is_screen_shot = false

    self.shield_rules = {}
end

function ScreenShotWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    self.snapshot_view:DeleteMe()
    self.snapshot_view = nil

    self:ClearAllShieldRule()

    ScreenShotWGCtrl.Instance = nil
end

--进入拍照预览
function ScreenShotWGCtrl:ChangeToScreenShotState()
	if Scene.Instance:GetSceneType() == SceneType.GHOST_FB_GLOBAL then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuoGuiFuBen.LimitScreenShot)
		return
	end


    GuajiWGCtrl.Instance:ClearCurGuaJiInfo()

    self.old_camera_type = CAMERA_TYPE
    self.old_camera_distacne = MainCameraFollow and MainCameraFollow.Distance
    if MainCamera then
        local angle = MainCamera.transform.parent.transform.localEulerAngles
        self.old_camera_x = angle.x
        self.old_camera_y = angle.y
    end

    Scene.Instance:SetCameraMode(CameraType.Free)
    self.view:Open()

    --MainuiWGCtrl.Instance:SetSnapShotIsHideMainUi(true)
    self.is_screen_shot = true
end

--恢复
function ScreenShotWGCtrl:Resume()
    self.view:Close()
    self.snapshot_view:Close()

    if self.old_camera_type ~= nil then
        Scene.Instance:SetCameraMode(self.old_camera_type or CameraType.Free)

        if MarryWGData.Instance:GetOwnIsXunyou() then
            if self.old_camera_type == CameraType.Free and self.old_camera_distacne then
                Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.SCENE_POS, self.old_camera_x or 0, self.old_camera_y or 0, self.old_camera_distacne)
            end
        elseif self.old_camera_distacne then
            Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.SCENE_POS, self.old_camera_x or 0, self.old_camera_y or 0, self.old_camera_distacne)
        end
    end

    self.old_camera_distacne = nil
    self.old_camera_x = nil
    self.old_camera_y = nil
    --MainuiWGCtrl.Instance:SetSnapShotIsHideMainUi(false)
    self.is_screen_shot = false
end

function ScreenShotWGCtrl:IsSnapShotState()
    return self.is_screen_shot
end

function ScreenShotWGCtrl:OpenSnapShotView(texture)
    self.snapshot_view:SetTexture(texture)
    ViewManager.Instance:Open(GuideModuleName.SnapShotView)
end

function ScreenShotWGCtrl:CloseSnapShotView()
    ViewManager.Instance:Close(GuideModuleName.SnapShotView)
end

function ScreenShotWGCtrl:IsOpenScreenShotView()
    return ViewManager.Instance:IsOpen(GuideModuleName.ScreenShotView) or
    ViewManager.Instance:IsOpen(GuideModuleName.SnapShotView)
end

local shield_role_rule_flags = {}
function ScreenShotWGCtrl:RegisterShieldRule(shield_type, is_shield)
    if SnapShotShieldType.QuFu == shield_type then
        self.view:Flush(0, "qufu")
        return
    end

    if shield_type == SnapShotShieldType.XianLv or shield_type == SnapShotShieldType.Teammeat
        or shield_type == SnapShotShieldType.OtherRole or shield_type == SnapShotShieldType.GuildRole then

        shield_role_rule_flags[shield_type] = is_shield
        local rule = self.shield_rules["role"]
        if nil == rule then
            rule = SimpleRule.New(ShieldObjType.Role, ShieldRuleWeight.High, BindTool.Bind1(self.CalulateShiledRoleRule, self))
            rule:Register()
            self.shield_rules["role"] = rule
        end

        rule:RefreshRule()
    else
        if type(shield_type) == "table" then
            for k,v in pairs(shield_type) do
                self:RegisterOneShieldRule(v, is_shield)
            end
        else
            self:RegisterOneShieldRule(shield_type, is_shield)
        end
    end
end

function ScreenShotWGCtrl:RegisterOneShieldRule(shield_type, is_shield)
    if nil ~= self.shield_rules[shield_type] then
        self.shield_rules[shield_type]:DeleteMe()
        self.shield_rules[shield_type] = nil
    end

    local rule = nil
    if is_shield then
        rule = SimpleRule.New(shield_type, ShieldRuleWeight.High, function ()
            return true
        end)
    else
        if shield_type == SnapShotShieldType.FollowUI then
            rule = SimpleRule.New(shield_type, ShieldRuleWeight.High, BindTool.Bind1(self.CalulateShiledFollowUIRule, self))
        elseif shield_type == SnapShotShieldType.Pet then
            rule = SimpleRule.New(shield_type, ShieldRuleWeight.High, BindTool.Bind1(self.CalulatePetRule, self))
        elseif shield_type == SnapShotShieldType.Guard then
            rule = SimpleRule.New(shield_type, ShieldRuleWeight.High, BindTool.Bind1(self.CalulatePetRule, self))
        else
            rule = SimpleRule.New(shield_type, ShieldRuleWeight.High, function ()
                return false
            end)
        end
    end

    rule:Register()
    self.shield_rules[shield_type] = rule
end

function ScreenShotWGCtrl:ClearAllShieldRule()
    for k,v in pairs(self.shield_rules) do
        v:DeleteMe()
    end
    self.shield_rules = {}
end

function ScreenShotWGCtrl:CalulateShiledRoleRule(role)
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local is_teammeat = SocietyWGData.Instance:IsTeamMember(role.vo.role_id)
    local is_common_guild = main_role_vo.guild_id ~= 0 and main_role_vo.guild_id == role.vo.guild_id
    local is_xianlv = main_role_vo.lover_uid == role.vo.role_id

    if IS_ON_CROSSSERVER then
        if not is_xianlv then
            is_xianlv = main_role_vo.lover_uid == role.vo.origin_uid
        end

        if not is_teammeat and role.vo.uuid ~= nil then
            is_teammeat = SocietyWGData.Instance:IsTeamMemberByUuid(role.vo.uuid)
        end
    end

    -- 显示仙侣
    if not shield_role_rule_flags[SnapShotShieldType.XianLv] and is_xianlv then
        return false
    end

    -- 显示相同仙盟
    if not shield_role_rule_flags[SnapShotShieldType.GuildRole] and is_common_guild then
        return false
    end

    -- 显示队友
    if not shield_role_rule_flags[SnapShotShieldType.Teammeat] and is_teammeat then
        return false
    end

    if shield_role_rule_flags[SnapShotShieldType.OtherRole] then
        return true
    else
        return false
    end
end

function ScreenShotWGCtrl:CalulateShiledFollowUIRule(follow_ui)
    if nil == follow_ui.GetOwnerObj then
        if follow_ui.shield_obj_type == ShieldObjType.MainRoleFollowTitle or follow_ui.shield_obj_type == ShieldObjType.OthersFollowTitle then
            return false
        end

        return true
    end

    local is_shiled = true
    local secne_obj = follow_ui:GetOwnerObj()
    if secne_obj and not secne_obj:IsDeleted() and not secne_obj:IsMonster() and secne_obj:GetVisiable() then
        is_shiled = false
    end

    return is_shiled
end

function ScreenShotWGCtrl:CalulatePetRule(pet)
    if nil == pet.GetOwnerObj then
        return true
    end

    local is_shiled = true
    local secne_obj = pet:GetOwnerObj()
    if secne_obj and not secne_obj:IsDeleted() and secne_obj:GetVisiable() then
        is_shiled = false
    end

    return is_shiled
end