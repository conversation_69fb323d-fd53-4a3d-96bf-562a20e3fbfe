TaskGuide = TaskGuide or BaseClass()
TaskGuide.LAST_CLICK_SKILL_TIME = 0
local TASK_G_ID = {
	KILL_BOSS = 1, 			--脱离黑暗氛围
	CHARGE_TIP = 2,			--首充浮窗
	WSL_FB = 3,				--伪圣灵副本
	GATHER_VIS = 4,			--采集后出现的采集物
}
function TaskGuide:__init()
	if TaskGuide.Instance ~= nil then
		ErrorLog("[TaskGuide] attempt to create singleton twice!")
		return
	end
	TaskGuide.Instance = self

	self.init_task = 2 -- 返回可接/己接，就认为任务初始化完成
	--需要自动做的任务，并且按这个类型顺序去完成
	self.auto_task_type = {GameEnum.TASK_TYPE_ZHU,
						   GameEnum.TASK_TYPE_RI,
						   GameEnum.TASK_TYPE_MENG,
						   GameEnum.TASK_TYPE_ZHUAN,
						   GameEnum.TASK_TYPE_HU,
						  }
	-- 当前在做的任务类型
	self.curr_task_type = GameEnum.TASK_TYPE_ZHU
	-- 自动做全部任务标记
	self.can_auto_all_task = false
	-- 自动做指定类型的任务 --暂未用
	self.auto_appoint_task = false
	-- 用于检测自动去挂机升级
	self.check_task_num = 0

	-- 特殊条件限制自动任务 (时间限制)
	self.special_conditions = false
	-- 秒
	self.special_conditions_time = 0
	self.now_time = 0

	--任务更新时间
	self.task_update_time = 0
	--任务检测时间
	self.task_check_time = 0.1 		-- 每隔多长时间进行update

	-- 新手检测继续任务时间
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto")
	self.novice_check_time = (other_cfg and other_cfg.other and other_cfg.other[1] ~= nil and other_cfg.other[1].novice_check_time) and other_cfg.other[1].novice_check_time or 30
	-- 新手任务检测等级
	self.novice_check_level = 65
	-- 自动任务触发做任务的时间
	self.last_do_task_time = 0

	-- 配置表特殊任务，接取后需要停止任务
	-- 点击该任务并且关闭对应面板或者其它触发继续任务事件时，则该字段生命周期就结束
	self.side_task_id_flag = false
	-- 自动任务打开的面板（手动关闭后要停止任务）
	self.auto_view_name = nil

	Runner.Instance:AddRunObj(self, 6)
	-- self:CanAutoAllTask(true)

	--特殊任务引导
	self.task_special_guide_cfg = ConfigManager.Instance:GetAutoConfig("task_level_guide_auto").task_special_guide
	self.trigger_area = {}
	local param_t = Split(self.task_special_guide_cfg[TASK_G_ID.KILL_BOSS].trigger_param, "##")
	self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h = tonumber(param_t[1]), tonumber(param_t[2]), tonumber(param_t[3]), tonumber(param_t[4])
	self.scene_load_enter = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER,
		function ()
			self:ChangeSceneStyle()
			self.envirment_modifier = nil
			self.has_change_style = nil
		end)
	self.charge_tip_cfg = self.task_special_guide_cfg[TASK_G_ID.CHARGE_TIP]
	self.task_change_handle = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE, BindTool.Bind(self.OnTaskChange, self))
end

function TaskGuide:__delete()
	Runner.Instance:RemoveRunObj(self)
	TaskGuide.Instance = nil
	self.auto_task_type = nil
	if self.scene_load_enter then
		GlobalEventSystem:UnBind(self.scene_load_enter)
		self.scene_load_enter = nil
	end
	if self.task_change_handle then
		GlobalEventSystem:UnBind(self.task_change_handle)
		self.task_change_handle = nil
	end
end

-- 是否新手
function TaskGuide:IsNovice()
	local role_vo = RoleWGData.Instance.role_vo
	return role_vo and role_vo.level <= RoleWGData.GetRoleMaxLevel()
end

-- get set ignore_xs 忽略新手 不可乱用
function TaskGuide:CurrTaskType(value, ignore_xs)
    if CLIENT_DEBUG_LOG_STATE then
    	if value ~= nil then
    		print_error("CurrTaskType", self.curr_task_type, value, ignore_xs)
    	end
    end

	if value ~= nil and value ~= self.curr_task_type then
	    if IS_DEBUG_BUILD then
	       print_log("内网日志：自动任务类型变更", value, self.curr_task_type, ignore_xs)
	    end
	end

	if nil == value then
		return self.curr_task_type
	end

	if GameEnum.TASK_TYPE_ZHU ~= value and self:NoviceCheckTask() and not ignore_xs then 
		return 
	end
	self.curr_task_type = value
end

-- get set
function TaskGuide:CanAutoAllTask(value)
    if CLIENT_DEBUG_LOG_STATE then
    	if value ~= nil then
    		print_error("CanAutoAllTask", self.can_auto_all_task, value)
    	end
    end

	if value ~= nil and value ~= self.can_auto_all_task then
	    if IS_DEBUG_BUILD then
	       print_log("内网日志：自动任务状态变更", value, self.can_auto_all_task)
	    end
	end
	
	if nil == value then
		return self.can_auto_all_task
	end

	local is_change = false
	if self.can_auto_all_task ~= value then
		is_change = true
	end


	self.can_auto_all_task = value
	
	--取消自动的时候要清空标识
	if is_change and not value then
		TaskWGCtrl.Instance:RemberCurAuToTaskType(-1)
		--仙盟建设任务自动领取任务标识
		GuildWGData.Instance:ClearGuildBuildAutoFlag(false)
	end
end

-- 检查任务是否初始化完成（可接列表与已接列表下发之后则表示完成）
function TaskGuide:InitTask(value)
	-- body
	if nil == value then
		return self.init_task <= 0
	end
	self.init_task = self.init_task - 1
end

function TaskGuide:SideTOStopTask(value)
	-- body
	if nil == value then
		return self.side_task_id_flag
	end
	self.side_task_id_flag = value
end

function TaskGuide:OnTaskChange(task_event_type, task_id)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if not task_cfg then
		return
	end

	-- vip BOSS引导
	if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU
		and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7
		and task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_53 then
		if task_event_type ==TASK_EVENT_TYPE.CAN_COMMIT then
			TaskWGCtrl.Instance:OperateFollowTask(task_cfg, nil, true)

		-- elseif task_event_type == TASK_EVENT_TYPE.ACCEPTED then
		-- 	local boss_cfg = BossWGData.Instance:GetVipBossCfgByBossId(task_cfg.c_param4)
		-- 	if boss_cfg then
		-- 		BossWGData.Instance:SetCurSelectBossID(BossViewIndex.VipBoss, boss_cfg.level+1, boss_cfg.boss_id)
		-- 		BossWGCtrl.Instance:OnEnterVipBoss()
		-- 	else
		-- 		print_error("找不到配置的vip_boss 任务：",task_cfg.task_id)
		-- 	end

		elseif task_event_type ==TASK_EVENT_TYPE.COMPLETED then
			-- 切出任务面板
			-- MainuiWGCtrl.Instance:ShowTaskPanelInBossScene(true)
			MainuiWGCtrl.Instance:SetShrinkButtonIsOn(true)
			GuajiWGCtrl.Instance:StopGuaji(nil, true)

			-- 一定时间后继续做主线
			GlobalTimerQuest:AddDelayTimer(function()
				local cur_zhu_task_cfg = TaskWGData.Instance:GetTaskTypeZhuCfg()
				if cur_zhu_task_cfg then
					TaskWGCtrl.Instance:OperateFollowTask(cur_zhu_task_cfg, nil, true)
				end
			end, 5)
		end
	end

	-- 这个任务完成需要弹出一个恭喜获得，策划新提的需求，先用枚举固定一下，服务器估计也不好加这个东西，装备类的道具，一切问题后续跟进问策划	@子鹏
	if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU and task_id == GameEnum.TASK_ZHU_IMP_TASK_ID and task_event_type ==TASK_EVENT_TYPE.COMPLETED then
		local app_protocol = {appe_image_id = GameEnum.TASK_ZHU_IMP_ID, appe_type = ROLE_APPE_TYPE.IMP,}
		AppearanceWGCtrl.Instance:OnGetNewAppearance(app_protocol)
	end
	
	if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU 
		and task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7
		and task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_73 then
			if task_event_type ==TASK_EVENT_TYPE.CAN_COMMIT then
				-- 切回去做主线
				TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_ZHU, true)
		        TaskGuide.Instance:CanAutoAllTask(true)
		        TaskGuide.Instance:SideTOStopTask(false)
			end
	end
end

-- 检查打开某些面板的时候停止自动做任务
function TaskGuide:CheckView()
    if ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) or
    	ViewManager.Instance:IsOpen(GuideModuleName.ZhuanSheng) or
    	ViewManager.Instance:IsOpen(GuideModuleName.TipsShowChapterView) or
    	ViewManager.Instance:IsOpen(GuideModuleName.SelectCameraModeView) or
		ViewManager.Instance:IsOpen(GuideModuleName.FuBenPanel) or
		ViewManager.Instance:IsOpen(GuideModuleName.YunbiaoView) or
		ViewManager.Instance:IsOpen(GuideModuleName.TaskGuildRewardTip) or
		ViewManager.Instance:IsOpen(GuideModuleName.TaskDailyRewardTip) or
		ViewManager.Instance:IsOpen(GuideModuleName.TaskShangJinView) or
		ViewManager.Instance:IsOpen(GuideModuleName.YuBiaoJieSuan) or
		ViewManager.Instance:IsOpen(GuideModuleName.OpenningView) or 
		ViewManager.Instance:IsOpen(GuideModuleName.OpenFunFlyView) or 
		ViewManager.Instance:IsOpen(GuideModuleName.FirstRechargeView) or 
		ViewManager.Instance:IsOpen(GuideModuleName.FirstTSGetNewView) or 
        ViewManager.Instance:IsOpen(GuideModuleName.VipTip) or
        ViewManager.Instance:IsOpen(GuideModuleName.Marry_XunYouView) or
        -- ViewManager.Instance:IsOpen(GuideModuleName.VipInitialView) or
		ViewManager.Instance:IsOpen(GuideModuleName.InterludePopDialogView) or
		ViewManager.Instance:IsOpen(GuideModuleName.LimitTimeGiftPurchase) or
        ScreenShotWGCtrl.Instance:IsOpenScreenShotView() or 
		ViewManager.Instance:IsOpen(GuideModuleName.TreasureHunt) or 
		ViewManager.Instance:IsOpen(GuideModuleName.CultivationView) or 
		ViewManager.Instance:IsOpen(GuideModuleName.ControlBeastsView) or 
		ViewManager.Instance:IsOpen(GuideModuleName.AppearanceGetNew) or
		ViewManager.Instance:IsOpen(GuideModuleName.DujieView) or
		ViewManager.Instance:IsOpen(GuideModuleName.VipServiceWindowView) then
    	return true
    end

    return false
end

-- 检查NPC对话面板，如果NPC对话面板打开了，且玩家与NPC距离过大则关闭NPC对话面板
function TaskGuide:CheckNpcDialog()
	-- body
	if not ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) then return end
	local npc_id = NpcDialog.Instance:GetNpcId()
	local npc = Scene.Instance:GetNpcByNpcId(npc_id)
	if not npc then return end
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local nx, ny = npc:GetLogicPos()
	local dis = GameMath.GetDistance(x, y, nx, ny, false)
	if dis >= 50 then
		ViewManager.Instance:Close(GuideModuleName.TaskDialog)
	end
end

function TaskGuide:Update(now_time, elapse_time)
	----[[ 编辑器控制自动任务
		if not GLOBAL_AUTO_RUN_TASK_SWITCH then
			return
		end
	--]]

	if IS_ON_CROSSSERVER then
		self:CanAutoAllTask(false)
		return
	end
	
	self.now_time = now_time
	if UnityEngine.Input.GetMouseButtonUp(0) then
		self:UpdateTaskTime()
	end
	if self.task_update_time > self.now_time then return end
	self.task_update_time = self.task_update_time + self.task_check_time
	self:CheckNpcDialog() 																	-- 检查npc对话面板
	-- self:CheckSceneStyle()
	self:CheckNoviceAutoTask() 																-- 检查是否需要开始做新手任务
	if self.side_task_id_flag then return end
	if FunctionGuide.Instance:GetIsGuide() then return end
	if not CgManager.Instance:IsCanMoveCG() and CgManager.Instance:IsCgIng() then return end
	if not self.can_auto_all_task then return end 											-- 是否可以自动做任务
	if not self:InitTask() then return end 													-- 任务协议数据是否初始化完毕
	if TaskWGCtrl.Instance:TaskUpdateing() then return end 									-- 检查任务协议数据是否在更新中
	if TaskWGCtrl.Instance:IsFly() then return end
	if Scene.Instance:IsSceneLoading() then return end
	if Scene.Instance:IsChangeSceneIng() then return end
	if BossCamera.Instance:BossFollowCaneraShowStatus() then return end							-- 检测展示boss
	if not self:IsNovice() then return end 													-- 检查玩家等级
    if not self:NoviceCheckTask() and GuajiCache.guaji_type ~= GuajiType.None then return end
    if Scene.Instance:GetSceneType() ~= SceneType.Common then return end
    if self.special_conditions then 														-- 检查是否处于特殊操作中（自动做任务暂停x秒后恢复）
    	-- 检查是否已经到了恢复自动做任务时间
    	if self.special_conditions_time < self.now_time then
    		self:SpecialConditions(false)
    	end
    	return
    end

    if self:CheckView() then return end
	local main_role = Scene.Instance:GetMainRole()
	if not main_role or main_role:IsMove() or not main_role:CanDoMove() or main_role:HasCantMoveBuffButCanShowMove()
		or main_role:GetIsGatherState() or main_role:IsMitsurugi() then
			return
	end

	if GuajiWGCtrl.Instance:CheckCanPick(true) then return end 								-- 检查附近是否有可捡物品

	-- 根据当前自动做任务的任务类型获取任务配置
	local task_cfg = nil
	local task_list = TaskWGData.Instance:GetTaskListIdByType(self.curr_task_type)
	if 0 < #task_list then
		task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
		self:CheckMoveTOGuaJi(false)
	else-- 悬赏任务特殊一下
		if self.curr_task_type == GameEnum.TASK_TYPE_RI then
			local task_id = TaskWGData.Instance:GetCurrBountyTask(true)
			task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
		end
	end

	if not task_cfg or task_cfg.task_id == NpcDialog.LAST_COMMIT_TASK then return end

	if self.curr_task_type == GameEnum.TASK_TYPE_RI then
		if TaskWGData.Instance:GetCurrBountyTask() == nil then
			return
		end
	end
	-- if TaskWGCtrl.Instance:BeforehandTransferTask(task_cfg) or

	-- 在完成一个任务的时候，服务端会通知客户端这个任务移除，然后通知客户端这个任务完成
	-- 现在在两次之间，客户端会跑了很多无意义的逻辑，导致自动任务状态异常，这里根据这个移除标记，直接停住
	local cfg = TaskWGData.Instance:GetTaskInfo(task_cfg.task_id)
	if cfg ~= nil and cfg.is_remove ~= nil and cfg.is_remove then
		return
	end

	-- 自动做主线时，卡等级的时候会站着，但是有个自动做任务的标记，这个时候不会触发自动打坐，这里重置一下标记
	if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
		local vo = GameVoManager.Instance:GetMainRoleVo()
		if vo ~= nil and vo.level < task_cfg.min_level then
			self:CanAutoAllTask(false)
			return
		end
	end

	if TaskWGCtrl.Instance:BeforehandMengTask(task_cfg) then return end 						-- 如果是未完成的 提交物品类 任务，则返回
	TaskWGCtrl.Instance:OperateFollowTask(task_cfg, nil, true)
	self:UpdateTaskTime()
	-- local fly_shoes = self:CheckFlyShoes()
	-- TaskWGCtrl.Instance:OperateFollowTask(task_cfg, fly_shoes)
end

function TaskGuide:UpdateTaskTime()
	self.last_do_task_time = self.now_time + self.novice_check_time
end

-- 检测自动去挂机点
function TaskGuide:CheckMoveTOGuaJi(value)
	if not value then
		self.check_task_num = 0
		return
	end
	self.check_task_num = self.check_task_num + 1
	if self.check_task_num >= #self.auto_task_type then --去挂机升级，暂未实现
		self:CanAutoAllTask(false)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function TaskGuide:SpecialConditions(bool,times)
	self.special_conditions = bool
	self.special_conditions_time = bool and (self.now_time + times) or 0
end

-- 自动任务时，点击技能需要停止任务，等放完技能再继续任务
function TaskGuide:ClickSkillStopTask()
	if not self.can_auto_all_task then
		return 
	end

	if TaskGuide.LAST_CLICK_SKILL_TIME + 3 < Status.NowTime then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	main_role:StopMove()
	self:SpecialConditions(true, 0.5)
end

-- 检查是否需要开始自动做新手任务
function TaskGuide:CheckNoviceAutoTask()
	if not self:InitTask() then 
		
		return 
	end 													-- 初始化是否未完毕
	if self.can_auto_all_task then 
		return 
	end 												-- 能否已经可以自动做任务
	if self.last_do_task_time >= self.now_time then return end 								-- 玩家不操作未超过给定时间
	if not self:NoviceCheckTask() then return end 											-- 是否不需要自动做新手任务
	local main_role = Scene.Instance:GetMainRole()
	if not main_role then return end
	if main_role:GetIsGatherState() then return end 										-- 主角是否在采集
	if main_role:IsMitsurugi() then return end 												-- 主角是否在御剑
	if not main_role:IsStand() then return end 												-- 主角是否不在站立状态
	if MoveCache.task_id > 0 then return end
	self:CurrTaskType(GameEnum.TASK_TYPE_ZHU) 												-- 设置开始做主线（新手）任务
	self:CanAutoAllTask(true)
end

-- 检测是否需要自动新手任务
function TaskGuide:NoviceCheckTask()
	--护送中不可做新手任务(排除伪护送)
	if YunbiaoWGData.Instance and YunbiaoWGData.Instance:GetIsHuShong() and YunbiaoWGData.Instance:GetHuSongTaskId() then
		return false
	end

	local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
	local task_cfg = nil
	if 0 < #task_list then
		task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
	end
	if not task_cfg then
		task_cfg = TaskWGData.Instance:GetNextZhuTaskConfig()
	end

	if not TaskFollow.Instance:CanDoZhuTask() then
		return false
	end

	if not task_cfg then
		return false
	end

	return self:IsNoviceStage()
end

-- 是否处于新手阶段
function TaskGuide:IsNoviceStage()
	local other_cfg = TaskWGData.Instance:GetOtherInfo()
	local task_auto = other_cfg.task_auto or 0
	return not TaskWGData.Instance:GetTaskIsCompleted(task_auto)
end

-- 是否属于自动关闭恭喜获得等级区间
function TaskGuide:IsAutoCloseGetNewAppViewLevel()
	local other_cfg = TaskWGData.Instance:GetOtherInfo()
	local close_get_new_lv = other_cfg.close_get_new_lv or 0
	local lv = RoleWGData.Instance:GetRoleLevel()
	return lv <= close_get_new_lv
end

function TaskGuide:IsAutoTaskType(task_type)
	-- body
	for k,v in pairs(self.auto_task_type) do
		if task_type == v then
			return true
		end
	end
	return false
end

function TaskGuide:AutoTaskOpenView(view_name)
	-- body
	self.auto_view_name = view_name
end

function TaskGuide:CloseViewStopTask(view_name)
	-- body
	if self.auto_view_name ~= view_name then return end
	self:CanAutoAllTask(false)
	self.auto_view_name = nil
end

function TaskGuide:TaskGuajiLimit()
	-- body
	if Scene.Instance:GetSceneType() ~= SceneType.Common then return false end
	if not self:NoviceCheckTask() then return false end
	local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
	if 0 == #task_list then return false end

	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
	if not task_cfg then return false end

	local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
	return task_status ~= GameEnum.TASK_STATUS_ACCEPT_PROCESS
end

function TaskGuide:CheckFlyShoes()
	-- body
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local item_index = ItemWGData.Instance:GetItemIndex(COMMON_CONSTS.FLY_PROP_ID)

	return 0 < role_vip_level or VipWGData.Instance:IsVipTy() or -1 ~= item_index
end

function TaskGuide:CheckHideGather(gather_id)
	local hide_gather_cfg = self.task_special_guide_cfg[TASK_G_ID.GATHER_VIS]
	if hide_gather_cfg and hide_gather_cfg.trigger_param == gather_id and self:InitTask()
		and not TaskWGData.Instance:HasContentedTask(hide_gather_cfg.task_id, hide_gather_cfg.sections) then
		return true
	end
	return false
end

function TaskGuide:IsHideGatherId(gather_id)
	local hide_gather_cfg = self.task_special_guide_cfg[TASK_G_ID.GATHER_VIS]
	if hide_gather_cfg and hide_gather_cfg.trigger_param == gather_id then
		return true
	end
	return false
end

-- 是否显示伪圣灵副本
function TaskGuide:CheckShowFakeSlFb()
	return false --not TaskWGData.Instance:HasContentedTask(self.task_special_guide_cfg[TASK_G_ID.WSL_FB].task_id, self.task_special_guide_cfg[TASK_G_ID.WSL_FB].sections)
end

-- 设置场景风格
function TaskGuide:CheckSceneStyle()
	if self:InitTask() and not TaskWGData.Instance:HasContentedTask(self.task_special_guide_cfg[TASK_G_ID.KILL_BOSS].task_id,
		self.task_special_guide_cfg[TASK_G_ID.KILL_BOSS].sections) and self.task_special_guide_cfg[TASK_G_ID.KILL_BOSS].scene_id == Scene.Instance:GetSceneId() then
		local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
		if GameMath.IsInRect(role_x, role_y, self.trigger_area.x, self.trigger_area.y, self.trigger_area.w, self.trigger_area.h) then
			self:ChangeSceneStyle(true)
		else
			self:ChangeSceneStyle()
		end
	else
		self:ChangeSceneStyle()
	end
end

-- 设置场景风格
function TaskGuide:ChangeSceneStyle(is_change)
	if self.has_change_style == is_change then
		return
	end
	if nil == self.envirment_modifier then
		local scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene()
			if scene then
				local roots = scene:GetRootGameObjects()
			for i = 0,roots.Length-1 do
				local obj = roots[i]
				if obj then
					local envirment_modifier = obj:GetComponentsInChildren(typeof(RuntimeEnvironmentParamModifier))
					if envirment_modifier and envirment_modifier.Length > 0 then
						self.envirment_modifier = envirment_modifier[0]
						break
					end
				end
			end
		end
	end
	if nil == self.envirment_modifier then
		return
	end
	if is_change then
		self.envirment_modifier:ChangeEnvironmentParam()
	else
		self.envirment_modifier:RecoverEnvironmentParam()
	end
	self.has_change_style = is_change
end

function TaskGuide:CheckIsCanAutoTask()
	-- 根据当前自动做任务的任务类型获取任务配置
	local task_cfg = nil
	local task_list = TaskWGData.Instance:GetTaskListIdByType(self.curr_task_type)
	if 0 < #task_list then
		task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
	end

	if not task_cfg or task_cfg.task_id == NpcDialog.LAST_COMMIT_TASK then
		return false
	end

	-- 在完成一个任务的时候，服务端会通知客户端这个任务移除，然后通知客户端这个任务完成
	-- 现在在两次之间，客户端会跑了很多无意义的逻辑，导致自动任务状态异常，这里根据这个移除标记，直接停住
	local cfg = TaskWGData.Instance:GetTaskInfo(task_cfg.task_id)
	if cfg ~= nil and cfg.is_remove ~= nil and cfg.is_remove then
		return false
	end
	if TaskWGCtrl.Instance:BeforehandMengTask(task_cfg) then
		return false
	end

	return true
end