--Boss乱斗
ActBossFightView = ActBossFightView or BaseClass(SafeBaseView)

function ActBossFightView:__init()
	-- self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/boss_fight_ui_prefab", "layout_boss_fight")
	self.open_tween = nil
	self.close_tween = nil
end

function ActBossFightView:__delete()
	
end

function ActBossFightView:ReleaseCallBack()
	if self.boss_list then
		self.boss_list:DeleteMe()
		self.boss_list = nil
	end

	if CountDownManager.Instance:HasCountDown("boss_fight_time") then
		CountDownManager.Instance:RemoveCountDown("boss_fight_time")
	end

	self.list_data = nil
	self.need_flush = nil
	self.load_callback = nil
end

function ActBossFightView:LoadCallBack()
	self:GetActiveTime()
	self.node_list["btn_go_to"].button:AddClickListener(BindTool.Bind(self.OnclickGoTo, self))
	self.boss_list = AsyncListView.New(ActBossFightRender,self.node_list["ph_list_reward"])
	self.load_callback = true
	if self.need_flush then
		self:RefreshView()
		self.need_flush = nil
	end
end

function ActBossFightView:OnclickGoTo()
	CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_BOSS_FIGHT)
end

function ActBossFightView:GetActiveTime()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_BOSS_FIGHT)
	local star_str = ""
	local end_str = ""
	if act_info then
		star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
		end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
	end
	local str = string.format(Language.ActBossFight.TimeDesc, star_str .. "----" .. end_str)
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(ServerActClientId.KF_BOSS_FIGHT)
	self.node_list["activity_state"].text.text = str .. open_act_cfg.top_desc
end

function ActBossFightView:RefreshView()
	if not self.load_callback then
		self.need_flush = true
		return
	end
	local info = ServerActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.KF_BOSS_FIGHT)
	if info.status == ACTIVITY_STATUS.CLOSE then
		self.node_list["activity_state_ing"]:SetActive(false)
		self.node_list["activity_state_none"]:SetActive(true)
		self.node_list["img_activity_time"].text.text = Language.ActBossFight.ActStatus1
		self:RefreshCountDown(info.next_time)
	elseif info.status == ACTIVITY_STATUS.STANDY then
		self.node_list["activity_state_ing"]:SetActive(false)
		self.node_list["activity_state_none"]:SetActive(true)
		self.node_list["img_activity_time"].text.text = Language.ActBossFight.ActStatus2
		self:RefreshCountDown(info.next_time)
	elseif info.status == ACTIVITY_STATUS.OPEN then
		self.node_list["activity_state_ing"]:SetActive(true)
		self.node_list["activity_state_none"]:SetActive(false)
		if not self.list_data then
			self.list_data = true
			self.boss_list:SetDataList(ServerActivityWGData.Instance:GetBossFightWaveReward())
		end
	end
end

function ActBossFightView:RefreshCountDown( time )
	local CDM = CountDownManager.Instance
	if CDM:HasCountDown("boss_fight_time") then
		CDM:RemoveCountDown("boss_fight_time")
	end
	CDM:AddCountDown("boss_fight_time", BindTool.Bind(self.UpateCountDown,self),
		BindTool.Bind(self.CompleteCountDown, self),time,nil,0.4)
end

function ActBossFightView:UpateCountDown(elapse_time, total_time)
	self.node_list["lbl_end_time"].text.text = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
end

function ActBossFightView:CompleteCountDown()
	self:RefreshView()
end

function ActBossFightView:OpenCallBack()
	self:RefreshView()
end

function ActBossFightView:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ActBossFightView:CloseCallBack()
	self.open_tween = nil
end

-- ActBossFightRender -----------------------------------
ActBossFightRender = ActBossFightRender or BaseClass(BaseRender)
function ActBossFightRender:__init()

end

function ActBossFightRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.cell_list = nil
	end
end

function ActBossFightRender:LoadCallBack()
	self.cell_list = {}
end

function ActBossFightRender:OnFlush()
	if not self.data then
		return
	end
	self.node_list["Text"].text.text = string.format(Language.ActBossFight.BossWave, Language.Common.UpNum[self.index])
	for i=1,5 do
		if self.data[i-1] then
			if self.cell_list[i] then
				self.cell_list[i]:SetActive(true)
			else
				self.cell_list[i] = ItemCell.New(self.node_list["cell" .. i])
				self.cell_list[i]:SetData(self.data[i-1])
			end
		else
			if self.cell_list[i] then
				self.cell_list[i]:SetActive(false)
			end
		end
	end
end

function ActBossFightRender:OnClickBtnReceive()

end

-----------------------------------------------------------------------
