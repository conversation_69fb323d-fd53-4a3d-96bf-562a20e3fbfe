MechaSkillShowView = MechaSkillShowView or BaseClass(CommonSkillShowView)

function MechaSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_bg_scene")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_mecha_skill_show_view")
end

function MechaSkillShowView:SetShowDataAndOpen(data)
    self.show_data = {}
    self.show_data.gundam_seq = data.gundam_seq
    self.cur_select_mecha_weapon_type = data.weapon_type
	self:Open()
end

function MechaSkillShowView:ChangeShowGundamData(gundam_seq, sort)
    local appearance_param_extend = {}
    for i = MECHA_PART_TYPE.BODY, MECHA_PART_TYPE.WEAPON do
        local new_sort = i == MECHA_PART_TYPE.WEAPON and sort or 1
        local cfg = MechaWGData.Instance:GetMechaPartCellCfg(gundam_seq, i, new_sort)

        appearance_param_extend[i] = cfg and cfg.seq or 0
    end

    self.show_data = {}
    self.show_data.gundam_seq = gundam_seq
    self.show_data.appearance_param_extend = appearance_param_extend
    self.show_data.special_appearance = SPECIAL_APPEARANCE_TYPE.GUNDAM
end

function MechaSkillShowView:LoadCallBack()
    CommonSkillShowView.LoadCallBack(self)
	self:InitEnemy()

    self.cur_select_mecha_seq = -1

    if not self.mecha_list then
        self.mecha_list = AsyncListView.New(SkillShowMechaRender, self.node_list.mecha_list)
        self.mecha_list:SetDefaultSelectIndex(nil)
        self.mecha_list:SetStartZeroIndex(true)
        self.mecha_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMechaCallBack, self))
    end

    if not self.mecha_part_list then
        self.mecha_part_list = AsyncListView.New(SkillShowMechaPartListRender, self.node_list.mecha_part_list)
        self.mecha_part_list:SetDefaultSelectIndex(nil)
        self.mecha_part_list:SetStartZeroIndex(true)
        self.mecha_part_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMechaPartHandler, self))
    end

    -- 普攻
    XUI.AddClickEventListener(self.node_list.normal_attack_btn, BindTool.Bind(self.OnClickNormalAttackBtn, self))

    -- 技能
    if self.node_list.skill_list and self.skill_btn_list == nil then
        self.skill_btn_list = {}
        local node_num = self.node_list.skill_list.transform.childCount
        for i = 1, node_num do
            self.skill_btn_list[i] = SkillShowSkillRender.New(self.node_list.skill_list:FindObj("skill" .. i))
            self.skill_btn_list[i]:SetIndex(i)
            self.skill_btn_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self))
        end
    end

    self:ChangeViewDisplay()
    self:TrySetCamera()

    self.select_skill_id = -1

    self.message_root_tween = self.node_list.skill_desc_root:GetComponent(typeof(UGUITweenPosition))
    XUI.AddClickEventListener(self.node_list.bag_forward_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, false))
    XUI.AddClickEventListener(self.node_list.bag_reverse_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, true))
end

function MechaSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()
    CommonSkillShowView.ReleaseCallBack(self)

    if self.mecha_list then
        self.mecha_list:DeleteMe()
        self.mecha_list = nil
    end

    if self.skill_btn_list then
        for k, v in pairs(self.skill_btn_list) do
            v:DeleteMe()
        end
        self.skill_btn_list = nil
    end

    if self.mecha_part_list then
        self.mecha_part_list:DeleteMe()
        self.mecha_part_list = nil
    end

    self.message_root_tween = nil
end

function MechaSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local data_list = MechaWGData.Instance:GetMechaShowDataList()
    self.mecha_list:SetDataList(data_list)

    local show_gundam_seq = self.show_data.gundam_seq
    if self.cur_select_mecha_seq ~= show_gundam_seq then
        local jump_index = 1
        for k,v in pairs(data_list) do
            if show_gundam_seq == v.seq then
                jump_index = k
                break
            end
        end

        self.mecha_list:JumpToIndex(jump_index)
    end
end

function MechaSkillShowView:OnSelectMechaCallBack(item)
    if nil == item or nil == item.data then
		return
	end

    local data = item.data
    local is_same_mecha = self.cur_select_mecha_seq == data.seq
    if is_same_mecha then
        return
    end

    if self.cur_select_mecha_seq ~= -1 then
        self.cur_select_mecha_weapon_type = 0
    end

    self.cur_select_mecha_seq = data.seq
    local part_data_list = MechaWGData.Instance:GetMechaWeaponPartShowDataList(data.seq, MECHA_PART_TYPE.WEAPON)
    self.mecha_part_list:SetDataList(part_data_list)
    local jump_index, is_find = 1, false
    for k,v in pairs(part_data_list) do
        for i,j in pairs(v) do
            if self.cur_select_mecha_weapon_type == j.weapon_type then
                is_find = true
            end
            break
        end

        if is_find then
            jump_index = k
            break
        end
    end

    self.mecha_part_list:JumpToIndex(jump_index)
end

function MechaSkillShowView:OnSelectMechaPartHandler(item)
    if nil == item or not item.data then
        return
    end

    local data = nil
    for k,v in pairs(item.data) do
        data = v
        break
    end

    self.cur_select_mecha_weapon_type = data.weapon_type

    self:ChangeShowGundamData(self.cur_select_mecha_seq, data.sort)
    self:InitShower()
    self.select_skill_id = -1

    local skill_list = MechaWGData.Instance:GetMainUISkillOrder(self.show_data.appearance_param_extend)
    for k, v in pairs(self.skill_btn_list) do
        if self.select_skill_id < 0 then
            self.select_skill_id = skill_list[k]
            self:FlushSkillInfo()
        end
        v:SetData({skill_id = skill_list[k], skill_level = 1})
    end

    self.general_attack_list = MechaWGData.Instance:GetWeaponToNormalSkillInfo(self.show_data.appearance_param_extend[MECHA_PART_TYPE.WEAPON])
end

-- 点击普攻
function MechaSkillShowView:OnClickNormalAttackBtn()
    -- print_error("----点击普攻----", self.is_shower_loaded, self.is_enemy_loaded)
    if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

    if IsEmptyTable(self.general_attack_list) then
        return
    end

    if self:IsLimitClick() then
		return
	end

    local skill_index = 1
    self.normal_attack_play_timestemp = Status.NowTime
    for k,v in ipairs(self.general_attack_list) do
        self.normal_attack_play_timestemp = self.normal_attack_play_timestemp + self:GetRoleSkillTime(v, skill_index)
        skill_index = skill_index + 1
    end

    self.general_attack_index = 1
    self:SimulateTSGeneralAttackOpera()
end

function MechaSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

-- 模拟普攻
function MechaSkillShowView:SimulateTSGeneralAttackOpera()
	self:ClearDelaySimulateGeneralAttackOpera()
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local skill_id = self.general_attack_list[self.general_attack_index]
	if not skill_id then
		return
	end

    self:ChangeViewDisplay(skill_id)
	local do_next_action_time = self:GetRoleSkillTime(skill_id, self.general_attack_index)

    self:CleanSkillBtnCDTimer()
    self:SetAllSkillBtnCD(string.format("%.1f", do_next_action_time), do_next_action_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(do_next_action_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), do_next_action_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, do_next_action_time)
        end
    )

	self:SimulateSingleGeneralAttackOpera(skill_id, self.general_attack_index)

	if do_next_action_time > 0 then
		self.general_attack_timer = GlobalTimerQuest:AddTimesTimer(function ()
	        self.general_attack_index = self.general_attack_index + 1
			self:SimulateTSGeneralAttackOpera()
		end, do_next_action_time, 1)
	end
end

function MechaSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp or Status.NowTime < self.normal_attack_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

-- 点击技能
function MechaSkillShowView:OnClickSkillBtn(item)
    if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanSkillBtnCDTimer()
    self.select_skill_id = data.skill_id
    self.show_data.skill_id = data.skill_id
    self.show_data.level = data.skill_level
    self:ChangeViewDisplay(data.skill_id)

    self:FlushSkillInfo()

    -- 0.5s 等带特效后小半截播完
    local skill_show_time = self:GetRoleSkillTime(data.skill_id, 1) + 0.5
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )

    self:DelaySimulateSpecialSkillOpera(0)
end

function MechaSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn_list then
        for k,v in pairs(self.skill_btn_list) do
            v:SetSkillBtnCD(time, total_time)
        end
    end
end

function MechaSkillShowView:PlayBeastsMessagePositionTween(is_forward)
    self.node_list.bag_forward_button:CustomSetActive(is_forward)
    self.node_list.bag_reverse_button:CustomSetActive(not is_forward)

    if not IsNil(self.message_root_tween) then
        if is_forward then
            self.message_root_tween:PlayForward()
        else
            self.message_root_tween:PlayReverse()
        end
    end
end

function MechaSkillShowView:FlushSkillInfo()
    if self.select_skill_id > 0 then
        local client_cfg = SkillWGData.Instance:GetSkillClientConfig(self.select_skill_id, 3)
        local beast_cfg = SkillWGData.Instance:GetJiJiaSkillConfig(self.select_skill_id, 1)
        if client_cfg and beast_cfg then
            self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
            self.node_list.skill_nane.text.text = beast_cfg.skill_name
            self.node_list.skill_desc.text.text = client_cfg.description
        end
    end
end

------------------------------------------SkillShowMechaRender---------------------------------------------
SkillShowMechaRender = SkillShowMechaRender or BaseClass(BaseRender)

function SkillShowMechaRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetSkillShowImg(self.data.icon)
    self.node_list.mecha_icon.image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list.mecha_icon.image:SetNativeSize()
    end)

    self.node_list.desc_name.text.text = self.data.name
end

function SkillShowMechaRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_nor:CustomSetActive(not is_select)
    self.node_list.bg_select:CustomSetActive(is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end







------------------------------------SkillShowMechaPartListRender------------------------------
SkillShowMechaPartListRender = SkillShowMechaPartListRender or BaseClass(BaseRender)
function SkillShowMechaPartListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local part_data
    for k, v in pairs(self.data) do
        part_data = v
        break
    end

    local weapon_type = part_data.weapon_type
    local mecha_seq = part_data.mechan_seq
    local weapon_cfg = MechaWGData.MECHA_WEAPON_SHOW_CFG[weapon_type] or MechaWGData.MECHA_WEAPON_SHOW_CFG[0]

    local weapon_name = (Language.Mecha.MechaWeaponTypeName[mecha_seq] or {})[weapon_type] or ""
    self.node_list.desc_name.text.text = weapon_name
    self.node_list.desc_name_hl.text.text = weapon_name

    local nor_bundle, nor_asset = ResPath.GetSkillShowImg(weapon_cfg.icon)
    self.node_list.icon_nor.image:LoadSprite(nor_bundle, nor_asset, function ()
        self.node_list.icon_nor.image:SetNativeSize()
    end)

    local hl_bundle, hl_asset = ResPath.GetSkillShowImg(weapon_cfg.icon_hl)
    self.node_list.icon_hl.image:LoadSprite(hl_bundle, hl_asset, function ()
        self.node_list.icon_hl.image:SetNativeSize()
    end)
end

function SkillShowMechaPartListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end