 ------------------------------------------------------------
--山海经相关主View
------------------------------------------------------------
require("game/shenshou/shenshou_attr")
ShanHaiJingView = ShanHaiJingView or BaseClass(SafeBaseView)


function ShanHaiJingView:__init()
	self:SetMaskBg()
	self:LoadConfig()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	-- self.need_check_redPoint = true
end

function ShanHaiJingView:__delete()
end

function ShanHaiJingView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_handbook")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
end

function ShanHaiJingView:ReleaseCallBack()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ShanHaiJingView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil

	self:TJReleaseCallBack()
	self.base_data = nil
	self.base_ctrl = nil
	self.AddClickEventListener = nil
end

function ShanHaiJingView:OpenCallBack()

end

function ShanHaiJingView:CloseCallBack()
	self:CloseTJCallBack()
end

function ShanHaiJingView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.luoshence_btn, BindTool.Bind(self.OnClickOpenLSQView, self))
	XUI.AddClickEventListener(self.node_list.qiwenyilu_btn, BindTool.Bind(self.OnClickOpenQWYLView, self))

	self.node_list["title_view_name"].text.text = Language.ShanHaiJing.View_Title
	--self.node_list["view_name"].text.text = Language.ShanHaiJing.View_Title
	--self.node_list["view_name_1"].text.text = Language.ShanHaiJing.View_Title
  	local bundle, assert = ResPath.GetF2RawImagesPNG("a3_wtp_bg")
  	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)
  	self.base_data = ShanHaiJingWGData.Instance
	self.base_ctrl = ShanHaiJingWGCtrl.Instance
	self.AddClickEventListener = XUI.AddClickEventListener
	self:TJLoadCallBack()

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ShanHaiJingView, self.get_guide_ui_event)

	XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind(self.OnClickCloseBtn, self))
end

function ShanHaiJingView:OnClickCloseBtn()
	if self.is_show_tj then
		self:OnClickBack()
	else
		self:Close()
	end
end

function ShanHaiJingView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:TJFlush(param_t)
		end
	end
	local lsc_remind_flag = ShanHaiJingLSCWGData.Instance:GetShanHaiJingLuoShenCeRemind()
	self.node_list.lsc_remind:SetActive(lsc_remind_flag == 1)
	local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ShanHaiJingLSCView)
	self.node_list.luoshence_btn:SetActive(is_fun_open)
end

function ShanHaiJingView:OnClickSysMsg()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ClickStrengthen)
end

--打开千秋绝艳图界面
function ShanHaiJingView:OnClickOpenLSQView()
	ViewManager.Instance:Open(GuideModuleName.ShanHaiJingLSCView)
end

--打开奇闻异录界面
function ShanHaiJingView:OnClickOpenQWYLView()
	ViewManager.Instance:Open(GuideModuleName.StrangeCatalogView)
end

-- 引导升级
function ShanHaiJingView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "select_btn_2" then
		return self.node_list.select_btn_2, BindTool.Bind(self.OnClickSHJBarHandler, self, 2)
	elseif ui_name == "guide_node" then
		return self.node_list.guide_node, BindTool.Bind(function ()
			self.base_ctrl:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_ACTIVE,ui_param)
		end, self)
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end