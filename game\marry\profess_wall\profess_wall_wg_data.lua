ProfessWallWGData = ProfessWallWGData or BaseClass()
PROFESS_TYPE = {
	ALL_PROFESS = 1,            --公共表白信息			
	TOME_PROFESS = 2, 			-- 对我的表白
	MINE_PROFESS = 3,			-- 我的表白
}

PERSON_PROFESS_TYPE = {
	MINE_PROFESS = 0,			-- 我的表白
	TOME_PROFESS = 1, 			-- 对我的表白	
}

function ProfessWallWGData:__init()
	if ProfessWallWGData.Instance then
		print_error("[ProfessWallWGData] Attempt to create singleton twice!")
		return
	end
	ProfessWallWGData.Instance = self
	local marriage_cfg = ConfigManager.Instance:GetAutoConfig("qingyuanconfig_auto")
	self.profess_gift = marriage_cfg.profess_gift
	self.profess_gift_cfg = ListToMap(marriage_cfg.profess_gift, "gift_type")

	self.result = 0
	self.self_profess_list = {}
	self.gobal_profess_list = {}
	self.other_profess_list = {}
	self.role_info = {}
end

function ProfessWallWGData:__delete()
	ProfessWallWGData.Instance = nil
	 self.delfault_info = nil
	 self.is_load_show = nil
end


-------------------------------协议部分--------------------------------
--公共表白墙信息
function ProfessWallWGData:SetGlobalProfessWallInfo(protocol)
	self.gobal_record_number = protocol.record_number
	self:SetGobalProfessInfoList(protocol.profess_list)
end
--个人表白墙信息
function ProfessWallWGData:SetPersonProfessWallInfo(protocol)
	self.profess_type = protocol.profess_type
	if self.profess_type == PERSON_PROFESS_TYPE.MINE_PROFESS then
		--自己的表白                                
		self:SetSelfProfessInfoList(protocol.person_profess_list)
		self.mine_record_number = protocol.record_number
	elseif self.profess_type == PERSON_PROFESS_TYPE.TOME_PROFESS then
		--对我的表白
		self:SetOtherProfessInfoList(protocol.person_profess_list)
		self.forme_record_number = protocol.record_number
	end
end

--特效
function ProfessWallWGData:SetProfessWallEffectInfo(protocol)
	self.effect_type = protocol.effect_type
end

function ProfessWallWGData:SetQingYuanZhengHunNotice(protocol)
	self.user_id = protocol.user_id
	self.user_name = protocol.user_name
	self.sex = protocol.sex
	self.avatar_timestamp = protocol.avatar_timestamp
	self.prof = protocol.prof
	self.page = protocol.page
	self.zhenghun_info = protocol.zhenghun_info
end
-------------------------------数据处理部分--------------------------------
function ProfessWallWGData:GetQingYuanZhengHunNotice()
	local data = {}
	data.user_id = self.user_id
	data.user_name = self.user_name
	data.sex = self.sex
	data.avatar_timestamp = self.avatar_timestamp
	data.prof = self.prof
	data.page = self.page
	data.zhenghun_info = self.zhenghun_info

	return data
end

--设置公共表白墙信息
function ProfessWallWGData:SetGobalProfessInfoList(profess_list)
	self.gobal_profess_list = profess_list
	-- if profess_list == nil then 
	-- 	return
	-- end

	-- for k,v in pairs(profess_list) do
	-- 	local list_num = #self.gobal_profess_list
	-- 	if list_num >= 30 then
	-- 		table.remove(self.gobal_profess_list, 1)
	-- 	end
	-- 	table.insert(self.gobal_profess_list, v)
	-- end
end

--设置我的表白墙信息
function ProfessWallWGData:SetSelfProfessInfoList(profess_list)
	self.self_profess_list = profess_list
	-- if profess_list == nil then 
	-- 	return
	-- end

	-- for k,v in pairs(profess_list) do
	-- 	local list_num = #self.self_profess_list
	-- 	if list_num >= 12 then
	-- 		table.remove(self.self_profess_list, 1)
	-- 	end
	-- 	table.insert(self.self_profess_list, v)
	-- end
end

--设置别人对我的表白墙信息
function ProfessWallWGData:SetOtherProfessInfoList(profess_list)
	self.other_profess_list = profess_list
	-- if profess_list == nil then 
	-- 	return
	-- end

	-- for k,v in pairs(profess_list) do
	-- 	local list_num = #self.other_profess_list
	-- 	if list_num >= 12 then
	-- 		table.remove(self.other_profess_list, 1)
	-- 	end
	-- 	table.insert(self.other_profess_list, v)
	-- end
end


--公共表白墙信息
function ProfessWallWGData:GetGobalProfessInfoList()
	self.tidy_goble_profess_info = {}
	if self.gobal_profess_list == nil then
		return
	end
	for k,v in pairs(self.gobal_profess_list) do
		local gift_info = self.profess_gift_cfg[v.gift_type]
		local profess_info = {}
		profess_info.role_id_from = v.role_id_from
		profess_info.role_id_to = v.role_id_to
		profess_info.profess_time = v.profess_time
		profess_info.role_name_from = v.role_name_from
		profess_info.role_name_to = v.role_name_to
		profess_info.content = v.content
		profess_info.gift_type = v.gift_type
		profess_info.gift_id = gift_info.gift_id
		profess_info.exp = gift_info.exp
		profess_info.self_charm = gift_info.self_charm
		table.insert(self.tidy_goble_profess_info, profess_info)
	end
	table.sort(self.tidy_goble_profess_info, SortTools.KeyUpperSorter("profess_time"))
	return self.tidy_goble_profess_info
end

--我的表白墙信息
function ProfessWallWGData:GetMineProfessInfo()
	self.tidy_mine_profess_info = {}
	if self.self_profess_list == nil then 
		return self.tidy_mine_profess_info
	end
	if self.result == 1 then
		for k,v in pairs(self.self_profess_list) do
			if v.profess_time == self.delete_profess_time and v.other_role_id == self.delete_role_id then
				table.remove(self.self_profess_list, k)
			end
		end
	end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(self.self_profess_list) do
		local gift_info = self.profess_gift_cfg[v.gift_type]
		local profess_info = {}
		profess_info.role_name_from = vo.name
		profess_info.role_name_to = v.other_name
		profess_info.profess_time = v.profess_time
		profess_info.content = v.content
		profess_info.gift_type = v.gift_type
		profess_info.gift_id = gift_info.gift_id
		profess_info.exp = gift_info.exp
		profess_info.self_charm = gift_info.self_charm
		profess_info.profess_type = PERSON_PROFESS_TYPE.MINE_PROFESS
		profess_info.other_role_id = v.other_role_id
		table.insert(self.tidy_mine_profess_info, profess_info)
	end
	table.sort(self.tidy_mine_profess_info, SortTools.KeyUpperSorter("profess_time"))
	return self.tidy_mine_profess_info
end

--对我的表白墙信息
function ProfessWallWGData:GetOtherProfessInfo()
	self.tidy_forme_profess_info = {}
	if self.other_profess_list == nil then 
		return self.tidy_mine_profess_info
	end
	if self.result == 1 then
		for k,v in pairs(self.other_profess_list) do
			if v.profess_time == self.delete_profess_time and v.other_role_id == self.delete_role_id then
				table.remove(self.other_profess_list, k)
			end
		end
	end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(self.other_profess_list) do
		local gift_info = self.profess_gift_cfg[v.gift_type]
		local profess_info = {}
		profess_info.role_name_from = v.other_name
		profess_info.role_name_to = vo.name
		profess_info.profess_time = v.profess_time
		profess_info.content = v.content
		profess_info.gift_type = v.gift_type
		profess_info.gift_id = gift_info.gift_id
		profess_info.exp = gift_info.exp
		profess_info.self_charm = gift_info.self_charm
		profess_info.profess_type = PERSON_PROFESS_TYPE.TOME_PROFESS
		profess_info.other_role_id = v.other_role_id
		table.insert(self.tidy_forme_profess_info, profess_info)
	end
	table.sort(self.tidy_forme_profess_info, SortTools.KeyUpperSorter("profess_time"))
	return self.tidy_forme_profess_info
end

--获取表白信息的编号记录
function ProfessWallWGData:GetRecordNumber(tab_type)
	if tab_type == PROFESS_TYPE.ALL_PROFESS then
		return self.gobal_record_number
	elseif tab_type == PROFESS_TYPE.TOME_PROFESS then
		return self.forme_record_number
	elseif tab_type == PROFESS_TYPE.MINE_PROFESS then
		return self.mine_record_number
	end
end

function ProfessWallWGData:GetProfessGiftCfg()
	return self.profess_gift
end

--判断是否是情缘表白道具
function ProfessWallWGData:IsProfessGiftByItemId(id)
	for k, v in pairs(self.profess_gift) do
		if v.gift_id == id then
			return true
		end
	end

	return false
end

---------------------------------表白墙排行榜---------------------------------
--自己的排行榜信息
function ProfessWallWGData:SetSpecialProfessRankInfo(protocol)
	self.profess_to_num = protocol.profess_to_num
	self.profess_from_num = protocol.profess_from_num
	self.profess_score = protocol.profess_score
end

--我的排行榜信息
function ProfessWallWGData:GetSpecialProfessRankInfo()
	local self_rank = self:GetSelfRankNum()
	local reward_item = self:GetProfessRankRewardCfgByRank(self_rank)
	local person_profess_list = {}
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local rank_info = {}
	rank_info.uid = main_role_vo.role_id
	rank_info.user_name = main_role_vo.name
	rank_info.sex = main_role_vo.sex
	rank_info.prof = main_role_vo.prof
	rank_info.rank_value = self.profess_score
	rank_info.profess_to_num = self.profess_to_num
	rank_info.profess_from_num =self.profess_from_num
	rank_info.rank = self_rank
	rank_info.item_id = reward_item and reward_item.reward_item or 0
	rank_info.limit_score = reward_item and reward_item.limit_score or 0
	table.insert(person_profess_list, rank_info)
	return person_profess_list
end

function ProfessWallWGData:SetDeleteProfessInfoResult(protocol)
	self.result = protocol.result
	self.delete_profess_time = protocol.param1
	self.delete_role_id = protocol.param2
end


function ProfessWallWGData:InitProfessRankTypeList()
	self.profess_rank_type = {
		PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_MALE,		        -- 男榜
		PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_FEMALE,			-- 女榜
	}
end

-- 获取排行榜标签
function ProfessWallWGData:GetProfessRankTypeList()
	if self.profess_rank_type == nil then
		self:InitProfessRankTypeList()
	end
	return self.profess_rank_type or {}
end

--获取排行奖励
function ProfessWallWGData:GetProfessRankRewarkCfg()
	local rank_reward_cfg = {}
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if config == nil then
		return rank_reward_cfg
	end
	local rank_reward_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(config.profess_rank, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	return rank_reward_cfg
end

function ProfessWallWGData:GetProfessRankRewardCfgByRank(index)
	if index == nil or index == 0 then 
		return 
	end
	local rank_reward_cfg = self:GetProfessRankRewarkCfg()
	if rank_reward_cfg == nil then
		return
	end
	for i=1, #rank_reward_cfg do
		if rank_reward_cfg[i] and rank_reward_cfg[i].rank_index and index <= rank_reward_cfg[i].rank_index + 1 then
			return rank_reward_cfg[i]
		end
	end

end

--默认显示第一名奖励的时装
function ProfessWallWGData:GetProfessFashionImage()
	local reward_cfg = self:GetProfessRankRewardCfgByRank(1)
	if reward_cfg then
		return reward_cfg.reward_item
	end
end

--表白墙男榜信息
function ProfessWallWGData:SetProfessRankMaleInfo(rank_list)
	self.male_rank_list = rank_list
end

--表白墙女榜信息
function ProfessWallWGData:SetProfessRankFemaleInfo(rank_list)
	self.female_rank_list = rank_list
end

--根据类型获取表白墙信息
function ProfessWallWGData:GetProfessRankInfoByRankType(index)
	local rank_list = {}
	local profess_rank_list = {}
	if index == TabIndex.profess_wall_rank_male then
		rank_list = self.male_rank_list
	else
		rank_list = self.female_rank_list
	end

	if not rank_list or IsEmptyTable(rank_list) then 
		return profess_rank_list
	end
	for k,v in pairs(rank_list) do
		local reward_cfg = self:GetProfessRankRewardCfgByRank(k)
		local rank_info = {}
		rank_info.uid = v.user_id or -1
		rank_info.user_name = v.user_name or -1
		rank_info.sex = v.sex or -1
		rank_info.prof = v.prof or -1
		rank_info.rank_value = v.rank_value or -1
		rank_info.profess_time = v.flexible_int or -1
		rank_info.profess_to_num = v.flexible_ll or -1
		rank_info.profess_from_num = v.flexible_ll_low or -1
		rank_info.item_id = reward_cfg and reward_cfg.reward_item
		rank_info.limit_score = reward_cfg and reward_cfg.limit_score or 0
		rank_info.avatar_key_big = v.avatar_key_big
		rank_info.avatar_key_small = v.avatar_key_small
		table.insert(profess_rank_list, rank_info)
	end
	return profess_rank_list
end

--获取自己的排名
function ProfessWallWGData:GetSelfRankNum()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.sex == GameEnum.FEMALE then
		if self.female_rank_list == nil then
			return 0 
		end
		for i,v in ipairs(self.female_rank_list) do
			if v.user_id == main_role_vo.role_id then
				return i 
			end
		end
		return 0
	elseif main_role_vo.sex == GameEnum.MALE then
		if self.male_rank_list == nil then
			return 0
		end
		for i,v in ipairs(self.male_rank_list) do
			if v.user_id == main_role_vo.role_id then
				return i
			end
		end
		return 0
	end
end

--排行榜活动是否开启
function ProfessWallWGData:GetActivityIsOpen(k)
	if k == "male_rank_info" or k == "female_rank_info" then
		return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	else
		return true
	end
end

function ProfessWallWGData:SetTargetInfo(info)
	-- local role_info = {}
	self.role_info.gamename = info.role_name
	self.role_info.user_id = info.role_id
	self.role_info.prof = info.prof
	self.role_info.sex = info.sex
end

function ProfessWallWGData:GetTargetInfo()
	return self.role_info
end
function ProfessWallWGData:SetDefaultInfo(uid,is_load_show)
	if nil == uid then
		self.delfault_info = nil
		 self.is_load_show = is_load_show
		return
	end
	local friend_list = ProfessWallWGCtrl.Instance:GetMyFriendList()
	for k,v in pairs(friend_list) do
		if v.user_id == uid then
			self.delfault_info = v
			break
		end
	end
end
function ProfessWallWGData:GetDefaultInfo()
	return self.delfault_info ,self.is_load_show
end

function ProfessWallWGData:GetProfessGiftTypeInfoByType(gift_type)
	return self.profess_gift_cfg[gift_type]
end