## Unity + ToLua 客户端网络通信流程说明

本说明面向客户端 C# 侧，梳理 Lua 层通过调用 Nirvana.NetClient 相关 API 与服务器进行通信的整体流程与涉及脚本（忽略 RuntimeGUI 目录）。

---

### 总览
- 传输层：基于 TCP Socket 的异步非阻塞通信
- 消息协议：小端 4 字节长度前缀（uint32，payload 长度） + 原始消息数据
- 线程切换：所有 Socket I/O 回调通过 Nirvana.Scheduler.PostTask 切回 Unity 主线程，确保与 Lua/Unity API 的安全交互
- Lua 互通：通过 ToLua 生成的 Wrap（Nirvana_NetClientWrap）与扩展方法（NetClientExtensions）把 C# 的字节流转为 LuaByteBuffer 并向 Lua 暴露事件/方法

---

### 涉及的核心脚本与职责

（路径相对 Assets/Game/Scripts，外部 NirvanaRuntime 下的文件以名称标注）

- Nirvana.NetClient（外部插件脚本）
  - 职责：TCP 客户端，封装连接、收发、断开、事件派发、KeepAlive
  - 关键成员：
    - 事件：DisconnectEvent、ReceiveEvent
    - 方法：Connect、Disconnect、SendMsg、StartReceive、SetReadLength、SetKeepAlive、Clear
    - 内部：Flush/SendCallback/ReceiveCallback、ProcessReadBuffer、Socket/缓冲区/发送队列
- Extensions/NetClientExtensions.cs
  - 职责：为 NetClient 提供扩展方法，便于 Lua 端以 LuaByteBuffer 处理消息
  - 方法：ListenMessage(ReceiveMessageDelegate)、UnlistenMessage、ListenDisconnect、UnlistenDisconnect
- NetBuffer.cs
  - 职责：环形样式的读缓冲，支持 Prepare/Submit/Consume，对接 Socket BeginReceive
- WriteMessage.cs
  - 职责：发送消息包装与对象池；在待发数据前写入 4 字节长度（小端）
- Nirvana.Scheduler（外部插件脚本）
  - 职责：统一的主线程任务调度器；PostTask/Delay/每帧执行列表
- Lua/LuaBind/Nirvana_NetClientWrap.cs（ToLua 自动生成）
  - 职责：将 NetClient 的方法与事件注册到 Lua，如 Connect/StartReceive/SendMsg/ListenMessage/ListenDisconnect 等
- Lua/LuaBind/Nirvana_SchedulerWrap.cs（ToLua 自动生成）
  - 职责：将 Scheduler 的 PostTask/Delay 等能力暴露给 Lua
- Lua/LuaBind/DelegateFactory.cs（ToLua 自动生成）
  - 职责：注册 NetClient 的委托类型（ConnectDelegate/DisconnectDelegate/ReceiveDelegate/SendDelegate）供 Lua 创建/回调
- NetWorkState.cs（可选）
  - 职责：平台侧网络状态监听（Android），与通信流程无直接必然关系，但可用于网络可达性/重连策略

---

### 协议细节（接收/发送）
- 帧格式：
  - [0..3]：uint32（小端），表示 payload 长度 N
  - [4..4+N-1]：payload 原始字节
- 接收侧：
  - 累积读取到 NetBuffer，保证至少 4 字节后读出长度，如剩余不足则等待下次数据；足够时复制 N 字节至 messageBuffer，消费 4+N，触发 ReceiveEvent(bytes, length)
- 发送侧：
  - WriteMessage.SetData(data) 会写入 4 字节长度前缀 + data 到内部 MemoryStream；进入写队列并通过 Flush/BeginSend 分片发送，完成后（可）触发 SendDelegate

---

### 通信时序（高层）

1) 连接
- Lua 调用 NetClient.Connect(host, port, onComplete)
- C# 内部通过 Dns.BeginGetHostAddresses -> new Socket -> BeginConnect
- ConnectCallback 中将结果通过 Scheduler.PostTask 回到主线程并调用 onComplete(true/false)

2) 开始接收
- Lua 调用 NetClient.StartReceive()
- C#：计算 readBuffer.Prepare(ReadLength)，BeginReceive(readBuffer.Buffer, ...)

3) 接收回调与消息派发
- ReceiveCallback -> EndReceive 得到 length
  - length > 0：Scheduler.PostTask(() => { ProcessReadBuffer(length); if (socket.Connected) StartReceive(); })
  - length == 0：Scheduler.PostTask(() => { socket.Close(); DisconnectEvent?.Invoke(); })
- ProcessReadBuffer：按“长度前缀 + 内容”循环拆包，多包粘连时一次处理多条
- 若有监听：
  - 原生 C#：触发 ReceiveEvent(byte[] bytes, uint length)
  - Lua：通过 NetClientExtensions.ListenMessage，把 bytes 转为 LuaByteBuffer 并回调 Lua 侧处理函数

4) 发送
- Lua 准备好一段消息体（不含长度前缀），调用 NetClient.SendMsg(byte[] data, onSent)
- C#：
  - 若 socket 未连接：关闭并触发 DisconnectEvent
  - 否则：将 data 包装成 WriteMessage（含 4 字节长度前缀）入队；若未在写，立即 Flush -> BeginSend（可能多次回调直至发送完）
  - 发送完成：Scheduler.PostTask 回到主线程，释放 WriteMessage，调用 onSent（若提供），并继续 Flush 下一条

5) 断开/清理
- 主动断开：NetClient.Disconnect()
  - 若还有 writeQueue，打包剩余数据一次性 Socket.Send(list)，随后 Shutdown/Close 并触发 DisconnectEvent
- 被动断开/错误：在接收或发送异常路径中，都会 Close 并通过 Scheduler.PostTask 触发 DisconnectEvent
- 清理监听：NetClient.Clear() 会把 DisconnectEvent/ReceiveEvent 上已注册的委托全部移除

---

### Lua 与 C# 的交互 API（ToLua 暴露）
- 连接/收发/断开
  - Connect(string host, int port, ConnectDelegate onComplete)
  - StartReceive()
  - SendMsg(byte[] data, SendDelegate onSent = null)
  - Disconnect()
  - SetReadLength(int length)（调整单次准备读取的字节数，默认 32768）
  - SetKeepAlive(uint onOff, uint keepAliveTime, uint keepAliveInterval)
  - ReceiveTimeout / SendTimeout（属性）
- 事件监听（扩展方法）
  - ListenMessage(ReceiveMessageDelegate luaHandler) -> 返回句柄，用于 UnlistenMessage(handle)
    - 内部将 ReceiveEvent 的 byte[]/length 转为 LuaByteBuffer 给 Lua
  - ListenDisconnect(DisconnectDelegate luaHandler) / UnlistenDisconnect(handle)
- 清理
  - Clear()：移除所有事件委托（避免重复注册导致泄漏）

---

### 关键实现要点（摘录说明）
- 长度前缀收包（ProcessReadBuffer）：
  - 至少有 4 字节后读取长度 N，若 Payload < (4+N) 则等待更多数据
  - 足够时复制 N 字节到 messageBuffer 并触发 ReceiveEvent
- 发送打包（WriteMessage.SetData）：
  - 将 data.Length 写为小端 4 字节，再写入 data，自带对象池减少 GC（最多缓存 8 个）
- 线程切换（Scheduler）：
  - 所有回调（连接成功/失败、收到数据、对端关闭、发送完成/失败）都通过 Scheduler.PostTask 切回主线程

---

### 常见使用范式（Lua 视角概述）
- 创建/连接
  - new Nirvana.NetClient()
  - client:ListenDisconnect(function() ... end)
  - local handle = client:ListenMessage(function(luaByteBuffer) ... end)
  - client:Connect(host, port, function(ok) if ok then client:StartReceive() end end)
- 发送
  - client:SendMsg(bytes[, function() ... end])
- 解绑与清理
  - client:UnlistenMessage(handle)
  - client:Clear()
  - client:Disconnect()

---

### 注意事项
- 确保消息体构造与服务端一致（长度前缀为小端 uint32）
- StartReceive 需在连接成功后调用，否则不会进入接收循环
- 监听的委托需保存句柄以便 Unlisten；或在合适时机调用 Clear() 统一清理
- 出错路径都会触发 DisconnectEvent，Lua 侧应做好重连/提示逻辑
- 根据网络质量调节 ReceiveTimeout/SendTimeout/KeepAlive 参数

---

### 索引（脚本清单）
- 外部 Nirvana 运行时（插件）
  - Nirvana.NetClient
  - Nirvana.Scheduler
- 本项目内脚本
  - Extensions/NetClientExtensions.cs
  - NetBuffer.cs
  - WriteMessage.cs
  - Lua/LuaBind/Nirvana_NetClientWrap.cs
  - Lua/LuaBind/Nirvana_SchedulerWrap.cs
  - Lua/LuaBind/DelegateFactory.cs（含 NetClient 委托注册）
  - NetWorkState.cs（网络状态监听，辅助）

