ChongBangTipWGData = ChongBangTipWGData or BaseClass()

ChongBangTipWGData.ACT_TIP_TYPE = {
	KAIFU = 1,
	ACT = 2,
}

function ChongBangTipWGData:__init()
	if ChongBangTipWGData.Instance then
		ErrorLog("[ChongBangTipWGData]:Attempt to create singleton twice!")
	end

	ChongBangTipWGData.Instance = self

	self:InitCfg()
	self.add_act_jiesuan_list = {}
	self.open_tip_data_list = {}   	--打开主界面tips的数据列表
	self.is_first_check = true
	self.is_first_check_list = {}
	self.act_rank_has_info_list = {}   --冲榜活动服务端已下发数据的列表
end

function ChongBangTipWGData:__delete()
	ChongBangTipWGData.Instance = nil

	if self.jiesuan_timer_quest then
        GlobalTimerQuest:CancelQuest(self.jiesuan_timer_quest)
        self.jiesuan_timer_quest = nil
    end
    self.is_first_check = false
    self.is_first_check_list = {}
end

-- 初始化配置表
function ChongBangTipWGData:InitCfg()
	self.chongbang_tip_cfg = ListToMap(self:GetConfig().chongbang_tip,"act_tip_type","act_id")
end

function ChongBangTipWGData:GetConfig()
	return ConfigManager.Instance:GetAutoConfig("chongbang_tip_auto")
end

function ChongBangTipWGData:GetActTipCfg(act_tip_type,act_id)
	return self.chongbang_tip_cfg[act_tip_type] and self.chongbang_tip_cfg[act_tip_type][act_id]
end

function ChongBangTipWGData:GetIsRankActTip(act_tip_type,act_id)
	local cfg_list = self.chongbang_tip_cfg[act_tip_type]
	if not IsEmptyTable(cfg_list) then
		for k,v in pairs(cfg_list) do
			if v.act_id == act_id then
				return true
			end
		end
	end
	return false
end

--检查所有需要弹窗提示的冲榜活动
function ChongBangTipWGData:CheckAllActJieSuanTime()
	-- self.add_act_jiesuan_list = {}
	-- local cfg = self:GetConfig().chongbang_tip
	-- for k,v in pairs(cfg) do
	-- 	self:CheckSingleActJieSuanTime(v)
	-- end
	-- self.is_first_check = false
end

--这个接口的检查 要从冲榜数据有变化里传过来的，这样可以保证这个接口过来的都是排行榜信息已存在的
function ChongBangTipWGData:CheckEventActJieSuanTime(act_tip_type,act_id)
	if act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.KAIFU then
		local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfigByRankType(act_id)
		if opengame_cfg then
			local rush_type = opengame_cfg.rush_type
			local cfg = self.chongbang_tip_cfg[act_tip_type] and self.chongbang_tip_cfg[act_tip_type][rush_type]
			if cfg then
				self:CheckSingleActJieSuanTime(cfg)
				self.act_rank_has_info_list[cfg.index] = true
			end
		end
	else
		local cfg = self:GetActTipCfg(act_tip_type,act_id)
		if cfg then 
			local is_open = ActivityWGData.Instance:GetActivityIsOpen(cfg.act_id)
			if is_open then
				self:CheckSingleActJieSuanTime(cfg)
			end
			self.act_rank_has_info_list[cfg.index] = true
		end
	end
end

--该接口是根据活动状态变为开启时做的检查  这样可以保证活动是开启状态的
function ChongBangTipWGData:ActOpenCheckJieSuanTime(act_tip_type,act_id)
	if act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.ACT then
		local cfg = self:GetActTipCfg(act_tip_type,act_id)
		if cfg then
			self:CheckSingleActJieSuanTime(cfg)
		end
	end
end

function ChongBangTipWGData:GetCurRankActOpen(cfg)
	if IsEmptyTable(cfg) then return false end
	local open = false
	local client_open = false
	if cfg.act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.KAIFU then
		open = ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.OPENSERVER_COMPETITION, true)
	elseif cfg.act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.ACT then
		open = ActivityWGData.Instance:GetActivityIsOpen(cfg.act_id)
	end
	return open
end

function ChongBangTipWGData:GetCurRankActEndTimer(cfg)
	local jiesuan_time = 0
	if IsEmptyTable(cfg) then return jiesuan_time end
	local act_open = self:GetCurRankActOpen(cfg)
	if cfg.act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.KAIFU then
		if act_open then
			jiesuan_time = self:GetCurKaiFuActEndTimer(cfg.act_id,cfg.act_jiesuan_time)
		end
	elseif cfg.act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.ACT then
		if act_open then
			local _,end_time = ActivityWGData.Instance:GetActivityResidueTime(cfg.act_id)
			jiesuan_time = end_time - 3600 * tonumber(cfg.act_jiesuan_time)
		end
	end
	return jiesuan_time
end

function ChongBangTipWGData:CheckSingleActJieSuanTime(cfg)
	if IsEmptyTable(cfg) then return end
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local can_join = self:GetCurActCanJoinRank(cfg)
	local act_open = self:GetCurRankActOpen(cfg)
	local has_rank_info = self.act_rank_has_info_list[cfg.index]
	local act_check_flag = act_open and has_rank_info	--达到检查的条件 1.活动有开启 2.冲榜排行的数据已下发
	if can_join then
		local jiesuan_time = self:GetCurRankActEndTimer(cfg)
		if jiesuan_time > now_time then
			local data = {}
			data.act_id = cfg.act_id
			data.act_tip_type = cfg.act_tip_type
			data.jiesuan_time = jiesuan_time
			data.cfg = cfg
			local tip_time_list = Split(cfg.tip_time_list,"|")
			local cache_time = self:GetCacheShowActTipNum(cfg)
			local list = {}
			local need_now_show = false  --离线期间需要弹窗的标记 
			local check_over = true
			for k,num in pairs(tip_time_list) do
				local time = jiesuan_time - tonumber(num) * 60
				local flag = self.is_first_check_list[cfg.index] and act_check_flag
				if not flag then
					if cache_time == 0 or cache_time > tonumber(num) then
						list[tonumber(num)] = now_time > time
						if now_time > time then
							--离线期间需要弹窗  上线后需要恢复弹窗
							self:SetCacheShowActTipTime(cfg,tonumber(num))
							need_now_show = true
						end
					else
						list[tonumber(num)] = true
					end
				else
					list[tonumber(num)] = now_time > time
				end
				if now_time < time then 
					check_over = false
				end
			end
			
			data.tip_list = list     --提示时间的列表
			if not check_over then
				self.add_act_jiesuan_list[cfg.index] = data
			end

			if need_now_show then 
				--需要在主界面打开提示冲榜
				ChongBangTipWGCtrl.Instance:OpenTipView(data)
			end
		end
	else
		if self.add_act_jiesuan_list[cfg.index] then
			self.add_act_jiesuan_list[cfg.index] = nil
		end
	end

	if act_check_flag then
		self.is_first_check_list[cfg.index] = true
	end
	self:AddTimerRunQuest()
end

function ChongBangTipWGData:AddTimerRunQuest()
	if self.jiesuan_timer_quest then
        GlobalTimerQuest:CancelQuest(self.jiesuan_timer_quest)
        self.jiesuan_timer_quest = nil
    end
	if not IsEmptyTable(self.add_act_jiesuan_list) then
		self.jiesuan_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RunQuestTimerUpdate, self), 5)
	end
end

function ChongBangTipWGData:RunQuestTimerUpdate()
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	for k,data in pairs(self.add_act_jiesuan_list) do
		local cfg = data.cfg
		local tip_time_list = Split(cfg.tip_time_list,"|")
		local is_over = true
		for i,v in pairs(tip_time_list) do
			local time = data.jiesuan_time - tonumber(v) * 60
			local flag = data.tip_list[tonumber(v)]
			if not flag then
				if now_time >= time then
					--在主界面打开提示冲榜
					data.tip_list[tonumber(v)] = true
					self:SetCacheShowActTipTime(cfg,tonumber(v))
					ChongBangTipWGCtrl.Instance:OpenTipView(data)
				else
					is_over = false
					data.tip_list[tonumber(v)] = false
				end
			end			
		end
		if is_over then
			table.remove(self.add_act_jiesuan_list,k)
		end
	end
	if IsEmptyTable(self.add_act_jiesuan_list) then
		if self.jiesuan_timer_quest then
	        GlobalTimerQuest:CancelQuest(self.jiesuan_timer_quest)
	        self.jiesuan_timer_quest = nil
	    end
	end
end

--开服活动的冲榜活动结算时间
function ChongBangTipWGData:GetCurKaiFuActEndTimer(rush_type,act_jiesuan_time)
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
	local close_day = opengame_cfg.close_day_index
	local open_day_index = opengame_cfg.open_day_index
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	if open_day < open_day_index then
		return 0
	end

	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local end_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + close_day - open_day + 1,
		hour = 0,
		min = 0,
		sec = 0
	})
	local count_down_time = end_time - 3600 * tonumber(act_jiesuan_time) -- 23:00结算
	return count_down_time
end

--判断能否进入该冲榜活动的提示列表
--位居【榜单前X名】的玩家才会显示弹框
function ChongBangTipWGData:GetCurActCanJoinRank(cfg)
	if not cfg then return false end
	if cfg.rank_num == 0 then return true end
	local can_join = false
	local is_open = self:GetCurRankActOpen(cfg)
	if cfg.act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.KAIFU then
		if is_open then
			local rank_data = RankWGData.Instance:GetActMyRank(cfg.act_id)
			local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(cfg.act_id)
			if opengame_cfg then
				local rank_data = RankWGData.Instance:GetActMyRank(opengame_cfg.rank_type)
				local my_rank = rank_data and rank_data.my_rank or 0
				can_join = my_rank > 0 and my_rank <= cfg.rank_num
			end
		end
	elseif cfg.act_tip_type == ChongBangTipWGData.ACT_TIP_TYPE.ACT then
		if is_open then
			local rank = 0
			if cfg.act_id == ACTIVITY_TYPE.GOD_CHONGBANG then
				local act_rank, cap = TianshenRoadWGData.Instance:GetMyRankAndCap()
				rank = act_rank 
			elseif cfg.act_id == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK then
				local my_rank_info = QuanMinBeiZhanWGData.Instance:GetLongHunRankRoleInfo()
				local my_rank_num = my_rank_info.rank or 0
				rank = my_rank_num
			elseif cfg.act_id == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_RANK then
				local my_rank = MergeSpecialRankWGData.Instance:GetGoldRankInfo()
				rank = my_rank
			elseif cfg.act_id == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER then
				local person_rank = LieMoDaRenWGData.Instance:GetPersonRankInfo()
				rank = person_rank
			elseif cfg.act_id == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LONGHUNRANK then
				local my_rank_info = ActXianQiJieFengWGData.Instance:GetXianQiRankRoleInfo()
				local my_rank_num = my_rank_info.rank or 0
				rank = my_rank_num
			end
			can_join = rank > 0 and rank <= cfg.rank_num
		end
	end
	return can_join
end

--获取了已经显示了tips的时间
function ChongBangTipWGData:GetCacheShowActTipNum(cfg)
	if not cfg then return 0 end
	local num = 0
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = "chongbang_tip6" .. uuid_str .. cfg.act_tip_type .. ":" .. cfg.act_id
	local value = PlayerPrefsUtil.GetInt(key)
	if value ~= nil then
		num = value
	end
	return num
end

function ChongBangTipWGData:SetCacheShowActTipTime(cfg,time)
	if not cfg then return end
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = "chongbang_tip6" .. uuid_str .. cfg.act_tip_type .. ":" .. cfg.act_id
	PlayerPrefsUtil.SetInt(key,time)
end

function ChongBangTipWGData:SetNotTipActIndex(index,ison)
	if not self.not_tip_act_index then
		self.not_tip_act_index = {}
	end
	self.not_tip_act_index[index] = ison
	if ison then
		for k,v in pairs(self.add_act_jiesuan_list) do
			if v and v.cfg and v.cfg.index == index then
				table.remove(self.add_act_jiesuan_list,k)
				return
			end
		end
	end
end

function ChongBangTipWGData:GetNoTipActIndex(index)
	return self.not_tip_act_index and self.not_tip_act_index[index] or false
end

function ChongBangTipWGData:SetCurShowTipIndex(index)
	self.cur_view_show_index = index
end

function ChongBangTipWGData:GetCurShowTipIndex()
	return self.cur_view_show_index or 0
end

function ChongBangTipWGData:AddOpenTipData(data)
	local index = data and data.cfg and data.cfg.index or 0
	local cur_show_index = self:GetCurShowTipIndex()
	if index == cur_show_index then return end
	if not IsEmptyTable(self.open_tip_data_list) then
		for k,v in pairs(self.open_tip_data_list) do
			if v.cfg and v.cfg.index == index then 
				return
			end
		end
	end
	table.insert(self.open_tip_data_list,data)
end

function ChongBangTipWGData:GetOpenTipData()
	if not IsEmptyTable(self.open_tip_data_list) then
		local data = table.remove(self.open_tip_data_list,1)
		return data
	end
end

function ChongBangTipWGData:GetPrintLog()
	local tab_str = "ChongBangTipWGData:GetPrintLog\n"
	local data1 = self.add_act_jiesuan_list
	if not IsEmptyTable(data1) then
		for k,v in pairs(data1) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end
	local data2 = self.open_tip_data_list
	if not IsEmptyTable(data2) then
		for k,v in pairs(data2) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end
	return tab_str
end
