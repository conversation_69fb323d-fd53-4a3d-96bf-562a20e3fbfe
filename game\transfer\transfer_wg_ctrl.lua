require("game/transfer/transfer_new_view")
require("game/transfer/transfer_wg_data")
require("game/transfer/transfer_demon_view")
require("game/transfer/transfer_attr_tips_view")
require("game/transfer/transfer_god_and_demons_view")
require("game/transfer/transfer_god_and_demons_tips_view")
require("game/transfer/transfer_render")
require("game/transfer/transfer_equip_collect_tips_view")
require("game/transfer/transfer_success_view")


TransFerWGCtrl = TransFerWGCtrl or BaseClass(BaseWGCtrl)
function TransFerWGCtrl:__init()
	if TransFerWGCtrl.Instance ~= nil then
		ErrorLog("[TransFerWGCtrl] Attemp to create a singleton twice !")
	end
	TransFerWGCtrl.Instance = self

	-- self.view = TransFerView.New(GuideModuleName.ZhuanSheng)
	self.view = TransFerNewView.New(GuideModuleName.ZhuanSheng)
	self.data = TransFerWGData.New()

	self.transfer_skill_tips = TransFerSkillTips.New(GuideModuleName.TransFerSkillTip)
	self.transfer_demon_view = TransFerDemonView.New(GuideModuleName.TransFerDemon)
	self.god_and_demons_view = TransferGodAndDemonsView.New(GuideModuleName.GodAndDemons)
	self.transfer_attr_tips_view = TransFerAttrTipsView.New()
	self.transfer_god_and_demons_tips_view = TransFerGodAndDemonsTipsView.New()
	self.equip_collect_tips_view = TransFerEquipCollectTipsView.New()
	self.transfer_success_view = TransferSuccessView.New(GuideModuleName.TransferSuccess)

	self.is_auto_accept_task = true
	self.first_task_list = false

	--是否可以自动执行转职.
	self.transfer_is_can_auto_active_flag = false

	self:RegisterAllProtocols()

	RoleWGData.Instance:NotifyAttrChange(BindTool.Bind1(self.RoleDataChangeCallback, self), {"prof", "level"})

	-- 任务相关 
	-- 目前不在任务列表显示
	-- self.task_data_change = BindTool.Bind1(self.OnOneTaskDataChange, self)
	-- self.task_change_can_show_transfer = BindTool.Bind1(self.OnTaskListDataChange, self)
	-- TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_change, false)
	-- TaskWGData.Instance:NotifyDataChangeCallBack(self.task_change_can_show_transfer, true)

	FunOpen.Instance:NotifyFunOpen(self.fun_open_event)

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function TransFerWGCtrl:__delete()
	RoleWGData.Instance:UnNotifyAttrChange(BindTool.Bind1(self.RoleDataChangeCallback, self))

	-- 注销物品改变监听
	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	TransFerWGCtrl.Instance = nil

	-- 任务相关 
	-- 目前不在任务列表显示
	-- if TaskWGData.Instance then
	-- 	TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_change)
	-- 	TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_change_can_show_transfer)
	-- end

	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	if self.transfer_skill_tips then
		self.transfer_skill_tips:DeleteMe()
		self.transfer_skill_tips = nil
	end

	if self.transfer_demon_view then
		self.transfer_demon_view:DeleteMe()
		self.transfer_demon_view = nil
	end

	if self.god_and_demons_view then
		self.god_and_demons_view:DeleteMe()
		self.god_and_demons_view = nil
	end

	if self.transfer_attr_tips_view then
		self.transfer_attr_tips_view:DeleteMe()
		self.transfer_attr_tips_view = nil
	end

	if self.transfer_god_and_demons_tips_view then
		self.transfer_god_and_demons_tips_view:DeleteMe()
		self.transfer_god_and_demons_tips_view = nil
	end

	if self.transfer_success_view then
		self.transfer_success_view:DeleteMe()
		self.transfer_success_view = nil
	end

	if self.equip_collect_tips_view then
		self.equip_collect_tips_view:DeleteMe()
		self.equip_collect_tips_view = nil
	end
	
	if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
	end

	self.first_task_list = false

	FunOpen.Instance:UnNotifyFunOpen(self.fun_open_event)
	self.fun_open_event = nil
end

function TransFerWGCtrl:Open(tab_index, param_t)
	-- 可以优化下 走正常打开流程
	if self.data:GetGodAndDemonsType() == ZHUANZHI_GOD_AND_DEMONS_TYPE.NO then
		ViewManager.Instance:Open(GuideModuleName.GodAndDemons)
	else
		ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
	end
	
end

function TransFerWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSRoleZhuanZhiOper)
	self:RegisterProtocol(SCRoleZhuanZhiTaskInfo, "OnSCRoleZhuanZhiTaskInfo")
	self:RegisterProtocol(SCRoleZhuanZhiBaseInfo, "OnSCRoleZhuanZhiBaseInfo")
	self:RegisterProtocol(SCZhuanZhiEquipCollectInfo, "OnSCZhuanZhiEquipCollectInfo")
	self:RegisterProtocol(SCZhuanZhiEquipCollectUpdate, "OnSCZhuanZhiEquipCollectUpdate")
end

function TransFerWGCtrl:OnTaskListDataChange()
	self:CheckCanShowTransfer()
	if not self.first_task_list then
		self.first_task_list = true
		self:FireTrabsFerRemind()
	end
end

function TransFerWGCtrl:OnOneTaskDataChange(task_id,reason)

	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
		if reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
			self.view:Flush()
			-- self:CheckCanShowTransfer()
		end
		self:FireTrabsFerRemind()
	end
end

function TransFerWGCtrl:FireTrabsFerRemind()
	RemindManager.Instance:Fire(RemindName.TransFer)
end

local RoleZhuanZhiCGBundleName = "cg/a1_cg_zhuanzhi_prefab"
local RoleZhuanZhiCGAssetName = {
	[1] = {
		[0] = "A1_CG_ZhuanZhi_NvJian",
		[1] = "A1_CG_ZhuanZhi_NanJian",
	},
	[3] = {
		[0] = "A1_CG_ZhuanZhi_NvQiang",
		[1] = "A1_CG_ZhuanZhi_NanQiang",
	}
}
function TransFerWGCtrl:PlayZhuanZhiCG()
	-- local end_func = function ()
		local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
		local skill_cfg	= SkillWGData.Instance:GetZhuanZhiSkillCfg(prof_level)
		if skill_cfg then
			TipWGCtrl.Instance:ShowGetNewSkillView(skill_cfg.index, XINMO_FUBEN_SKILL_POS.XINMO_SKILL)
		end
	-- end

	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end

	-- local role_prof = RoleWGData.Instance:GetRoleProf()
	-- local role_sex = RoleWGData.Instance:GetRoleSex()
	-- CgManager.Instance:Play(BaseCg.New(RoleZhuanZhiCGBundleName, RoleZhuanZhiCGAssetName[role_prof][role_sex]), end_func, nil, false)

	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_zhuanzhi, is_success = true, pos = Vector2(0, 0)})
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end


function TransFerWGCtrl:RoleDataChangeCallback(attr_name, value, old_value)
	if attr_name == "prof" then
		local old_zhuan = math.floor(old_value / 10)
		local now_zhuan = math.floor(value / 10)
		if now_zhuan > old_zhuan then
			self:CheckCanShowTransfer()
			self:PlayZhuanZhiCG()
			self:FireTrabsFerRemind()
			MainuiWGCtrl.Instance:FlushTaskTopPanel()
		end
	elseif attr_name == "level" then
		if value > old_value then
			local show_suit_index = self.data:GetCurShowSuitIndex()
			if show_suit_index >= 0 then
				self:CheckEquipCollectTips()
			end
		end
	end
end

function TransFerWGCtrl:CheckCanShowTransfer()
	local all_fun_checked = FunOpen.Instance:GetAllFunChecked()
	if not all_fun_checked then return end

	local cur_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	if cur_zhuan == 0 then
		return
	end

	if cur_zhuan >= TransFerWGData.Instance:GetTransFerRewardCfgMax() then
		return
	end
	
	local level = RoleWGData.Instance:GetAttr('level')
	local need_zhuan = cur_zhuan + 1
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ZhuanSheng)
	--if can_show then
	local is_finish,cur_num, max_num = TransFerWGData.Instance:IsAllFinish()
		if is_finish then
			-- MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnTransFerView", true,true)
			if not is_open then
				FunOpen.Instance:ForceOpenFunByName(FunName.ZhuanSheng)
			end
		else
			if TaskWGData.Instance:GetKeJieZhuanZhiCfg() or TaskWGData.Instance:GetTaskZhuanZhiCfg() then
				-- MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnTransFerView", true,true)
				if not is_open then
					FunOpen.Instance:ForceOpenFunByName(FunName.ZhuanSheng)
				end
			-- else
			-- 	print_error(is_open)
			-- 	-- MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnTransFerView", false,true)
			-- 	if is_open then
			-- 		FunOpen.Instance:ForceCloseFunByName(FunName.ZhuanSheng)
			-- 	end
			end
		end
		
	-- else
	-- 	-- MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnTransFerView", false,true)
	-- 	if is_open then
	-- 		FunOpen.Instance:ForceCloseFunByName(FunName.ZhuanSheng)
	-- 	end
	-- end
end



-- 如果要加入任务列表需要修改替换
-- function TransFerWGCtrl:ChangeTaskState()
-- 	if self.view:IsOpen() then
-- 		self.view:ChangeTaskState()
-- 	end
-- end

function TransFerWGCtrl:OpenSkillTips(index )
	self.transfer_skill_tips:SetSkillData(index)
end

function TransFerWGCtrl:OpenAttrTipsView(index, stage)
	self.transfer_attr_tips_view:SetData(index, stage)
	self.transfer_attr_tips_view:Open()
end

function TransFerWGCtrl:OpenSelectTipsView(type)
	self.transfer_god_and_demons_tips_view:SetData(type)
	if self.transfer_god_and_demons_tips_view:IsOpen() then
		self.transfer_god_and_demons_tips_view:Flush()
	else
		self.transfer_god_and_demons_tips_view:Open()
	end
end

function TransFerWGCtrl:OpenTransferSuccessView(prof)
	self.transfer_success_view:SetProf(prof)
	if self.transfer_success_view then
		self.transfer_success_view:Open()
	end
end

function TransFerWGCtrl:OpenGodAndDemonsView()
	self.god_and_demons_view:Open()
end

function TransFerWGCtrl:ChangeAutoActiveFlag()
	self.transfer_is_can_auto_active_flag = true
end

function TransFerWGCtrl:FireAutoActive()
	if self.transfer_is_can_auto_active_flag then
		self.transfer_is_can_auto_active_flag = false

		local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
		if prof_zhuan == 0 then
			prof_zhuan = 1
		end
		local is_finish,cur_num, max_num = TransFerWGData.Instance:IsAllFinish()
		local desc_cfg_list = TransFerWGData.Instance:GetZhunaZhiDescCfg(prof_zhuan, cur_num)
		if IsEmptyTable(desc_cfg_list) then
			return
		end
		-- 当前阶段的第一个任务
		if #desc_cfg_list > 1 then
			return
		end

		local task_desc_cfg = desc_cfg_list[1]
		local task_cfg = TaskWGData.Instance:GetTaskConfig(task_desc_cfg.task_id)
		if not task_cfg then
			return
		end
		local is_complete = TaskWGData.Instance:GetTaskIsCompleted(task_cfg.task_id)
		if is_complete then
			--完成所有任务 转职请求
			RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_ZHUANZHI)
			return
		end
	end
end
-----------------------------------协议---------------------------------------------

-----------------发送协议，16432------------------------------
function TransFerWGCtrl:SendTransferOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleZhuanZhiOper)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function TransFerWGCtrl:OnSCRoleZhuanZhiTaskInfo(protocol)
	self.data:SetTaskInfo(protocol)
	self:FlushCultivationView()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
	ViewManager.Instance:FlushView(GuideModuleName.DujieView)
end

function TransFerWGCtrl:OnSCRoleZhuanZhiBaseInfo(protocol)
	local is_change = false
	if protocol.god_and_demons_type ~= ZHUANZHI_GOD_AND_DEMONS_TYPE.NO then
		ViewManager.Instance:Close(GuideModuleName.GodAndDemons)

		if self.data:GetGodAndDemonsType() ~= protocol.god_and_demons_type then
			is_change =true

		end
	end

	self.data:SetStarInfo(protocol)
	if is_change then
		ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
		ViewManager.Instance:FlushView(GuideModuleName.DujieView)
	end


	self:FlushCultivationView()
	MainuiWGCtrl.Instance:FlushTaskTopPanel()
end


------------------刷新View-----------------------------------

function TransFerWGCtrl:FlushCultivationView()
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end


------------------------------------------------------------

-- 物品变化
function TransFerWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    -- 物品数量增加
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        local item_id_list =  TransFerWGData.Instance:GetItemIdList()
		if item_id_list[change_item_id] then
			MainuiWGCtrl.Instance:FlushTaskTopPanel()
		end
	end
end

function TransFerWGCtrl:GoToTask(task_cfg)
	if IsEmptyTable(task_cfg) then
		return
	end
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end

	if task_cfg.task_id then
		MainuiWGCtrl.Instance:DoTask(task_cfg.task_id, TaskWGData.Instance:GetTaskStatus(task_cfg.task_id), true)
		return
	end

	local cfg = task_cfg
    if cfg.go_type == 1 then
        local param_tab = Split(cfg.go_param,"#")--FunOpen:.Instance:GetOpenParam(cfg.go_param)
        if param_tab ~= nil and param_tab[1] == FunName.Guild then --仙盟相关的功能,没加入仙盟认为没开启
            if RoleWGData.Instance.role_vo.guild_id == 0 then
                GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
            else
                FunOpen.Instance:OpenViewNameByCfg(cfg.go_param)
            end
		elseif param_tab ~= nil and param_tab[1] == GuideModuleName.Compose then
			if param_tab[2] and param_tab[2] == "sex" then
				local sex = RoleWGData.Instance:GetRoleSex()
				local flag = sex == GameEnum.MALE
				local tab_index = flag and TabIndex.other_compose_eq_hecheng_one or TabIndex.other_compose_eq_hecheng_two
				local item_id = 0
				if param_tab[3] then
					item_id = tonumber(param_tab[3])
				end
				local star_level = 1
				if param_tab[4] then
					star_level = tonumber(param_tab[4])
				end
				local can_compose, open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeCfgByProductId(item_id, sex,star_level)
				FunOpen.Instance:OpenViewByName(param_tab[1], tab_index,{ open_param = open_param, to_ui_param = to_ui_param })
			elseif param_tab[2] then
				FunOpen.Instance:OpenViewByName(param_tab[1], param_tab[2])
			else
				FunOpen.Instance:OpenViewByName(param_tab[1])
			end
			
        else
            FunOpen.Instance:OpenViewNameByCfg(cfg.go_param)
            if cfg.go_param == GuideModuleName.TaskShangJinView then
                ViewManager.Instance:Close(GuideModuleName.XiuXianShiLian)
            end
        end
    elseif cfg.go_type == 3 then --世界聊天特殊处理，，
        local txt = XiuXianShiLianWGData.Instance:GetRandomContent()
        ViewManager.Instance:Open(GuideModuleName.ChatView, 30, nil ,{open_param = txt} )
    end
end


----------------------转职装备收集-------------------------------
function TransFerWGCtrl:OnSCZhuanZhiEquipCollectInfo(protocol)
	-- print_error("=====所有数据=========",protocol)
	self.data:SetAllEquipCollectInfo(protocol)

	-- MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
    --     MainuiWGCtrl.Instance.view:FlushTransferEquipCollectInfo()
    -- end)
end

function TransFerWGCtrl:OnSCZhuanZhiEquipCollectUpdate(protocol)
	-- print_error("=====单更=========",protocol)
	self.data:UpdateEquipCollectInfo(protocol)

	-- MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
    --     MainuiWGCtrl.Instance.view:FlushTransferEquipCollectInfo()
    -- end)
end

function TransFerWGCtrl:OpenEquipCollectTips()
    if self.equip_collect_tips_view and not self.equip_collect_tips_view:IsOpen() then
		local show_suit_index = self.data:GetCurShowSuitIndex()
		if show_suit_index >= 0 then
			self.equip_collect_tips_view:Open()
		end
    end
end

function TransFerWGCtrl:CloseEquipCollectTips()
    if self.equip_collect_tips_view and self.equip_collect_tips_view:IsOpen() then
        self.equip_collect_tips_view:Close()
    end
end

function TransFerWGCtrl:PlayTipsFlyIcon(result, param1, param2, param3)
	--param1=suit_index * 100 + pos param2=item_id param3=star
	if result == 1 then
		if self.equip_collect_tips_view and self.equip_collect_tips_view:IsOpen() then
			local data = {}
			data.suit_index = math.floor(param1 / 100)
			data.part = (math.floor(param1 % 100) + 1) -- 服务端从0读取的
			data.item_id = param2
			data.star = param3

			self.equip_collect_tips_view:PlayTipFlyAnim(data)
		end
	end
end

function TransFerWGCtrl:GetEquipCollectTipCellNode(cell_index)
    if self.equip_collect_tips_view and self.equip_collect_tips_view:IsOpen() then
        local node = self.equip_collect_tips_view:GetEquipCollectTipCellNode(cell_index)
        return node
    end
end

function TransFerWGCtrl:CompleteEquipCollectTipsFlyAnim(cell_index)
    if self.equip_collect_tips_view and self.equip_collect_tips_view:IsOpen() then
        self.equip_collect_tips_view:CompleteEquipCollectTipsFlyAnim(cell_index)
    end
end

function TransFerWGCtrl:CheckEquipCollectTips()
	if self.data:IsVIPBossScene() then
		self:OpenEquipCollectTips()
	end

	-- MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
    --     MainuiWGCtrl.Instance.view:FlushTransferEquipCollectInfo()
    -- end)
end
