NewFestivalActivityView = NewFestivalActivityView or BaseClass(SafeBaseView)

function NewFestivalActivityView:__init()
	self.view_layer = UiLayer.Normal
	self.is_safe_area_adapter = true
	self.full_screen = true
	
	self:SetMaskBg()

	local bundle_name = "uis/view/new_festival_activity_ui_prefab"
	self:AddViewResource(0, bundle_name, "layout_new_fes_act_panel")

	self:AddViewResource(TabIndex.new_festival_activity_2344, bundle_name, "layout_new_fes_act_tehuishop")
	self:AddViewResource(TabIndex.new_festival_activity_2345, bundle_name, "layout_new_fes_act_denglu")
	self:AddViewResource(TabIndex.new_festival_activity_2346, bundle_name, "layout_new_fes_act_raffle")
	self:AddViewResource(TabIndex.new_festival_activity_2347, bundle_name, "layout_new_fes_act_prayer")
	self:AddViewResource(TabIndex.new_festival_activity_2348, bundle_name, "layout_new_fes_act_consume_rebate")
	self:AddViewResource(TabIndex.new_festival_activity_2349, bundle_name, "layout_new_fes_collect_item")
	self:AddViewResource(TabIndex.new_festival_activity_2350, bundle_name, "layout_new_fes_collect_card")
	self:AddViewResource(TabIndex.new_festival_activity_2351, bundle_name, "layout_new_fes_act_recharge" )-- 累充活动ui
	self:AddViewResource(TabIndex.new_festival_activity_2352, bundle_name, "layout_new_fes_boss_drop")
	self:AddViewResource(TabIndex.new_festival_activity_5009, bundle_name, "layout_new_fes_act_rank")

	self:AddViewResource(0, bundle_name, "VerticalTabbar")
	self:AddViewResource(0, bundle_name, "HorizontalTabbar")

	self.default_index = 0

	self.raw_bg = {
		[TabIndex.new_festival_activity_2344] = "bg_1",
		[TabIndex.new_festival_activity_2345] = "bg_2",
		[TabIndex.new_festival_activity_2346] = "bg_3",
		[TabIndex.new_festival_activity_2347] = "bg_1",
		[TabIndex.new_festival_activity_2348] = "bg_1",
		[TabIndex.new_festival_activity_2349] = "bg_1",
		[TabIndex.new_festival_activity_2350] = "bg_1",
		[TabIndex.new_festival_activity_2351] = "bg_1",
		[TabIndex.new_festival_activity_2352] = "bg_1",
		[TabIndex.new_festival_activity_5009] = "bg_1",
	}
end

function NewFestivalActivityView:__delete()
end

function NewFestivalActivityView:OpenCallBack()

end

function NewFestivalActivityView:LoadCallBack()
	self.tabbar_loading = false

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = true,
			show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end

	if not self.tabbar then
		local tab_index_name, sub_tab_name_list, remind_tab = NewFestivalActivityWGData.Instance:GetViewShowInfo()
		self.remind_tab = remind_tab
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
		self.tabbar:Init(tab_index_name, sub_tab_name_list, "uis/view/new_festival_activity_ui_prefab", "uis/view/new_festival_activity_ui_prefab", 
						remind_tab, NewFesActVerItemRender, NewFesActHorItemRender)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	self:SetNewFestivalCommonImg()
end

function NewFestivalActivityView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end 

	self.tabbar_loading = nil

	self:ReleaseCallBackTehuiShop()
	self:ReleaseCallBackDengLu()
	self:ReleaseCallBackRaffle()
	self:ReleasePrayer()
	self:ReleaseCallBackConsumeRebate()
	self:ReleaseCollectItem()
	self:ReleaseCollectCard()
	self:ReleaseRechargeActive()
	self:ReleaseBossDrop()
	self:ReleaseCallBackRank()
	
end

function NewFestivalActivityView:LoadIndexCallBack(index)
	if index == TabIndex.new_festival_activity_2344 then
		self:LoadIndexCallBackTehuiShop()
	elseif index == TabIndex.new_festival_activity_2345 then
		self:LoadIndexCallBackDengLu()
	elseif index == TabIndex.new_festival_activity_2346 then
		self:LoadIndexCallBackRaffle()
	elseif index == TabIndex.new_festival_activity_2347 then
		self:LoadIndexCallBackPrayer()
	elseif index == TabIndex.new_festival_activity_2348 then
		self:LoadIndexCallBackConsumeRebate()
	elseif index == TabIndex.new_festival_activity_2349 then
		self:LoadIndexCallBackCollectItem()
	elseif index == TabIndex.new_festival_activity_2350 then
		self:LoadIndexCallBackCollectCard()
	elseif index == TabIndex.new_festival_activity_2351 then
		self:LoadIndexCallBackRecharge()
	elseif index == TabIndex.new_festival_activity_2352 then
		self:LoadIndexCallBackBossDrop()
	elseif index == TabIndex.new_festival_activity_5009 then
		self:LoadIndexCallBackRank()

	end
end

function NewFestivalActivityView:ShowIndexCallBack(index)
	if index == TabIndex.new_festival_activity_2347 then
		self:ShowIndexCallBackPrayer()
	elseif index == TabIndex.new_festival_activity_5009 then
		self:ShowIndexCallBackRank()
	elseif index == TabIndex.new_festival_activity_2346 then
		self:ShowIndexCallBackRaffle()
	end

	local bg_res = self.raw_bg[index] or self.raw_bg[TabIndex.new_festival_activity_2347]
	local big_bg_bundle, big_bg_asset = ResPath.GetNewFestivalRawImages(bg_res)
	self.node_list["big_bg"].raw_image:LoadSprite(big_bg_bundle, big_bg_asset, function ()
		self.node_list["big_bg"].raw_image:SetNativeSize()
	end)
end

function NewFestivalActivityView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.new_festival_activity_2344 then
				self:OnFlushTehuiShop()
			elseif index == TabIndex.new_festival_activity_2345 then
				self:OnFlushDengLu()
			elseif index == TabIndex.new_festival_activity_2346 then
				self:OnFlushRaffle()
			elseif index == TabIndex.new_festival_activity_2347 then
				self:OnFlushPrayer()
			elseif index == TabIndex.new_festival_activity_2348 then
				self:OnFlushConsumeRebate()
			elseif index == TabIndex.new_festival_activity_2349 then
				self:OnFlushCollectItem()
			elseif index == TabIndex.new_festival_activity_2350 then
				self:OnFlushCollectCard(v)
			elseif index == TabIndex.new_festival_activity_2351 then
				self:OnFlushRecharge() -- 累充活动刷新
			elseif index == TabIndex.new_festival_activity_2352 then
				self:OnFlushBossDrop()
			elseif index == TabIndex.new_festival_activity_5009 then
				self:OnFlushRank(param_t)


			end
		elseif k == "play_dlani" then
			self:PlayAnimation()
		end
	end
end

-- 计算要显示的index（可重写）
function NewFestivalActivityView:CalcShowIndex()
	return NewFestivalActivityWGData.Instance:GetOneOpenTabIndex()
end

--不同节日的通用的底板图片设置
function NewFestivalActivityView:SetNewFestivalCommonImg()
	local yq_bg_bundle, yq_bg_asset = ResPath.GetNewFestivalRawImages("yq_bg")
	self.node_list["left_yq_bg"].raw_image:LoadSprite(yq_bg_bundle, yq_bg_asset, function ()
		self.node_list["left_yq_bg"].raw_image:SetNativeSize()
	end)

	local close_bundle, close_asset = ResPath.GetNewFestivalActImages("a3_jrhd_close")
	self.node_list["btn_close_window"].image:LoadSprite(close_bundle, close_asset, function ()
		self.node_list["btn_close_window"].image:SetNativeSize()
	end)

	local title_bg_bundle, title_bg_asset = ResPath.GetNewFestivalRawImages("bt")
	self.node_list["title_img"].raw_image:LoadSprite(title_bg_bundle, title_bg_asset, function ()
		self.node_list["title_img"].raw_image:SetNativeSize()
	end)

	local ver_line_bundle, ver_line_asset = ResPath.GetNewFestivalRawImages("line_2")
	self.node_list["ver_line"].raw_image:LoadSprite(ver_line_bundle, ver_line_asset, function ()
		self.node_list["ver_line"].raw_image:SetNativeSize()
	end)
end

function NewFestivalActivityView:SetTabbarInfo()
	self.tabbar_loading = true
	self:SetTabSate()
end

function NewFestivalActivityView:SetTabSate()
	if nil == self.tabbar or not self.tabbar_loading then
		return
	end

	local all_act_cfg = NewFestivalActivityWGData.Instance:GetAllFestivalActivityCfg()
	if IsEmptyTable(all_act_cfg) then
		return
	end

	-- 酌情来
	for k, v in pairs(all_act_cfg) do
		local tab_index = v.big_type * 10
		local sub_index = v.small_type

		local is_open = NewFestivalActivityWGData.Instance:GetActivityState(v.activity_type)
		self.tabbar:SetToggleVisible(tab_index + sub_index, is_open or false)
	end
end

------------------------NewFesActVerItemRender--------------------
NewFesActVerItemRender = NewFesActVerItemRender or BaseClass(VerItemRender)

function NewFesActVerItemRender:ReleaseCallBack()
	self.is_loaded_view = false
end

function NewFesActVerItemRender:SetIndex(index)
	VerItemRender.SetIndex(self, index)
	if self.is_loaded_view then
		self:LoadViewInfo()
	end
end

function NewFesActVerItemRender:LoadCallBack()
	self.is_loaded_view = true

	if self.index then
		self:LoadViewInfo()
	end
end

function NewFesActVerItemRender:OnFlush()
	VerItemRender.OnFlush(self)
end

function NewFesActVerItemRender:LoadViewInfo()
	if self.node_list["HLImage"] then
		local icon_hl_bundle, icon_hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_ver_xz")
	 	self.node_list["HLImage"].image:LoadSprite(icon_hl_bundle, icon_hl_asset, function ()
	 		self.node_list["HLImage"].image:SetNativeSize()
	  	end)
	end

	if self.node_list["Image"] then
		local icon_bundle, icon_asset = ResPath.GetNewFestivalActImages("a3_jrhd_ver_wxz")
	 	self.node_list["Image"].image:LoadSprite(icon_bundle, icon_asset, function ()
	 		self.node_list["Image"].image:SetNativeSize()
	  	end)
	end

	local cfg = NewFestivalActivityWGData.Instance:GetOtherCfg()
	if self.node_list["Text"] then
		self.node_list["Text"].text.color = Str2C3b(cfg.ver_text_color or COLOR3B.WHITE)
	end

	if self.node_list["TextHL"] then
		self.node_list["TextHL"].text.color = Str2C3b(cfg.ver_hl_text_color or COLOR3B.WHITE)
	end
end

------------------------NewFesActHorItemRender------------
NewFesActHorItemRender = NewFesActHorItemRender or BaseClass(HorItemRender)

function NewFesActHorItemRender:ReleaseCallBack()
	self.is_loaded_view = false
end

function NewFesActHorItemRender:SetIndex(index)
	HorItemRender.SetIndex(self, index)
	if self.is_loaded_view then
		self:LoadViewInfo()
	end
end

function NewFesActHorItemRender:LoadCallBack()
	self.is_loaded_view = true

	if self.index then
		self:LoadViewInfo()
	end
end

function NewFesActHorItemRender:OnFlush()
	HorItemRender.OnFlush(self)
end

function NewFesActHorItemRender:LoadViewInfo()
	if self.node_list["HLImage"] then
		local icon_hl_bundle, icon_hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_hor_xz")
	 	self.node_list["HLImage"].image:LoadSprite(icon_hl_bundle, icon_hl_asset, function ()
	 		self.node_list["HLImage"].image:SetNativeSize()
	  	end)
	end

	local cfg = NewFestivalActivityWGData.Instance:GetOtherCfg()
	if self.node_list["Text"] then
		self.node_list["Text"].text.color = Str2C3b(cfg.hor_text_color or COLOR3B.WHITE)
	end

	if self.node_list["Text_hl"] then
		self.node_list["Text_hl"].text.color = Str2C3b(cfg.hor_hl_text_color or COLOR3B.WHITE)
	end
end
