-- 商城
-- 很复杂，小心别乱动
-- 新的跳转去ctrl找，三种入口
ShopWGData = ShopWGData or BaseClass()

Shop_Money_Type = {
  Type1 = 1, -- 仙玉 （旧 元宝）
  Type2 = 2, -- 绑玉 （旧 绑元）
  Type3 = 3, -- 元宝 （旧 银票）新增的
  Type4 = 4, -- 声望
  Type5 = 5, -- 金币 （旧 铜币）
  Type7 = 7, -- 侠义值
}

Shop_Money = {
	{url = "a3_huobi_xianyu", scale = 1}, -- 仙玉
	{url = "a3_huobi_bangyu", scale = 1}, -- 绑玉
	{url = "a1_huobi_gongyu", scale = 1}, -- 元宝 --贡玉
	{url = "a3_huobi_honor", scale = 1}, -- 声望
	{url = "a3_huobi_tongqian", scale = 1}, -- 金币
	{url = "a3_huobi_shengwang", scale = 1}, -- 新声望
	{url = "a3_huobi_xiayi", scale = 1},     -- 侠义值
}

ItemTip_Shop_Money = {
	{url = "a3_huobi_xianyu", scale = 0.6}, -- 仙玉
	{url = "a3_huobi_bangyu", scale = 0.6}, -- 绑玉
	{url = "a1_huobi_gongyu", scale = 0.6}, -- 元宝 --贡玉
	{url = "a3_huobi_honor", scale = 0.6}, -- 声望
	{url = "a3_huobi_tongqian", scale = 0.6}, -- 金币
	{url = "a3_huobi_shengwang", scale = 0.6}, -- 新声望
	{url = "a3_huobi_xiayi", scale = 0.6},     -- 侠义值
}

COIN_ITEM_ID = {
	[1] = COMMON_CONSTS.VIRTUAL_ITEM_GOLD ,						--虚拟物品 仙玉
	[2] = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD,					--虚拟物品 绑玉
	[3] = COMMON_CONSTS.ITEM_GONGYU,							--虚拟物品 贡玉
	[4] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR ,					--虚拟物品 声望
	[6] = COMMON_CONSTS.VIRTUAL_ITEM_SHENGWANG,                 --虚拟物品 声望
	[7] = COMMON_CONSTS.VIRTUAL_ITEM_CHIVALROUS,                --虚拟物品 侠义值
}

--根据商城配置表定义
SHOP_BIG_TYPE = {
	SHOP_TYPE_1 = 1,	--一折限购
	SHOP_TYPE_2 = 2,	--限购商城
	SHOP_TYPE_3 = 3,	--常用道具
	SHOP_TYPE_4 = 4,	--时装外形
	SHOP_TYPE_5 = 5,	--绑玉商店
	SHOP_TYPE_6 = 6,	--声望商店
	SHOP_TYPE_8 = 8,	--侠义值商店
	SHOP_TYPE_9 = 9,	--青云商店
	SHOP_TYPE_10 = 10,	--季卡特权商店
	SHOP_TYPE_11 = 11,	--至尊特权商店
}

ShopWGData.Buy_Limit_Type = {
	day_limit_list = 1, -- 每日
	week_limit_list = 2, -- 每周
	two_week_limit_list = 3, -- 两周
	month_limit_list = 4, -- 每月
	whole_life_limit_list = 5, -- 终生
}

ShopWGData.ComposeStrList = {
	"day_limit_list", -- 每日
	"week_limit_list", -- 每周
	"two_week_limit_list", -- 两周
	"month_limit_list", -- 每月
	"whole_life_limit_list", -- 终生
}

ShopWGData.Limit_Type = {
	Limit_Type1 = 1, -- 个人
	Limit_Type2 = 2, -- 仙盟
	Limit_Type3 = 3, -- 全服
}

-- 所有购买类型
ShopWGData.Total_Limit_Type = {
	T_Type1 = 1, -- 个人今日可购
	T_Type2 = 2, -- 仙盟今日可购
	T_Type3 = 3, -- 全服今日可购
	T_Type4 = 4, -- 个人本周可购
	T_Type5 = 5, -- 仙盟本周可购
	T_Type6 = 6, -- 全服本周可购
	T_Type7 = 7, -- 个人两周可购
	T_Type8 = 8, -- 仙盟两周可购
	T_Type9 = 9, -- 全服两周可购
	T_Type10 = 10, -- 个人本月可购
	T_Type11 = 11, -- 仙盟本月可购
	T_Type12 = 12, -- 全服本月可购
	T_Type13 = 13, -- 个人终身可购
	T_Type14 = 14, -- 仙盟终身可购
	T_Type15 = 15, -- 全服终身可购
	Max_Type = 15
}
ShopWGData.TabIndex = {}
-- 如果要配置商城跳转，让策划自己算:(Tab_Shop..shop_type + 10 + page_type)
ShopWGData.Tab_Shop = "Tab_Shop"

ShopWGData.ShowShopType = {
	[1] = true, --屏蔽元宝商城
	[2] = true,
	[3] = true,
	[4] = true,
	[5] = true,
	[6] = true,
	[7] = true,
	[8] = true,
}

ShopWGData.ShowTypeToIndex = {
	Tab_Shop10 = 11,
	Tab_Shop20 = 12,
	Tab_Shop30 = 13,
	Tab_Shop40 = 14,
	Tab_Shop50 = 15,
	Tab_Shop60 = 16,
	Tab_Shop70 = 17,
	Tab_Shop80 = 18,
}

local ServerListShopItem = "ServerListShopItem"
function ShopWGData:__init()
	if ShopWGData.Instance then
		error("[ShopWGData] Attempt to create singleton twice!")
		return
	end
	ShopWGData.Instance = self
	self.limit_list = nil -- 解释商品限制
	self.shop_title1 = {}
	self.shop_title2 = {}
	self.shop_ver_mind = {}
	self.shop_hor_mind = {}
	self.shop_remind_group = {}
	self.all_shop_info = {}
	self.hor_tab_status = {}
	self.ver_tab_status = {}
	self.add_list = {} -- 添加的新商品
	self.shop_auto = ConfigManager.Instance:GetAutoConfig("shopconfig_auto")
	self.item_seq_cfg = ListToMap(self.shop_auto.item, "seq")
	self.item_cfg = ListToMap(self.shop_auto.item, "shop_type", "page_type", "seq")
	self.type_item_cfg = ListToMap(self.shop_auto.item, "shop_type", "seq")
	self.shop_type_cfg = ListToMap(self.shop_auto.shop_type, "shop_type")
	self.page_type_cfg = ListToMap(self.shop_auto.page_type, "shop_type", "page_type")
	self.condition_cfg = ListToMap(self.shop_auto.condition, "condition_id")
	self.item_seq_cfg_1 = ListToMap(self.shop_auto.item, "itemid","price_type")
	self.act_discount_cfg = ListToMap(self.shop_auto.activity_discount, "item_seq")
	self.other_cfg = self.shop_auto.other[1]
	self:ExplainCfg()
	self:ShopTitle()
	--RemindManager.Instance:Register(RemindName.Shop, BindTool.Bind(self.IsShowShopRedPoint, self))
	for k,v in pairs(self.shop_ver_mind) do
		RemindManager.Instance:Register(k, BindTool.Bind(self.GetShopTypeRedPoint, self, v))
	end

	for k,v in pairs(self.shop_hor_mind) do
		RemindManager.Instance:Register(k, BindTool.Bind(self.GetPageTypeRedPoint, self, v))
	end

	RemindManager.Instance:Register(RemindName.Shop, BindTool.Bind(self.MainUIShopRemind, self, 0))
	self:CheckAuditShow()
end

function ShopWGData:CheckAuditShow()
	-- 审核服
	if IS_AUDIT_VERSION then
		ShopWGData.ShowShopType[SHOP_BIG_TYPE.SHOP_TYPE_6] = false
		ShopWGData.ShowShopType[SHOP_BIG_TYPE.SHOP_TYPE_7] = false
 	end
end

function ShopWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.Shop)
	for k,v in pairs(self.shop_ver_mind) do
		RemindManager.Instance:UnRegister(k)
	end

	for k,v in pairs(self.shop_hor_mind) do
		RemindManager.Instance:UnRegister(k)
	end
	ShopWGData.Instance = nil
end

function ShopWGData:GetOtherCfg()
	return self.other_cfg
end

function ShopWGData:ExplainCfg()
	-- body
	self.limit_list = {}
	local function ChangeStr(list)
		-- body
		for i=1,#list do
			list[i] = tonumber(list[i])
		end
		return list
	end

	local list = nil
	for k,v in pairs(self.item_seq_cfg) do
		self.limit_list[v.seq] = {}
		if v.day_limit then
			list = Split(v.day_limit, "|")
			self.limit_list[v.seq].day_limit_list = ChangeStr(list)
		end

		if v.week_limit then
			list = Split(v.week_limit, "|")
			self.limit_list[v.seq].week_limit_list = ChangeStr(list)
		end

		if v.two_week_limit then
			list = Split(v.two_week_limit, "|")
			self.limit_list[v.seq].two_week_limit_list = ChangeStr(list)
		end

		if v.month_limit then
			list = Split(v.month_limit, "|")
			self.limit_list[v.seq].month_limit_list = ChangeStr(list)
		end

		if v.whole_life_limit then
			list = Split(v.whole_life_limit, "|")
			self.limit_list[v.seq].whole_life_limit_list = ChangeStr(list)
		end
	end
end

function ShopWGData:GetPageType(index, shop_type, page_type)
	-- body
	if index then
		shop_type = math.floor(index / 10)
		page_type = index % 10
	end
	return self.page_type_cfg[shop_type] and self.page_type_cfg[shop_type][page_type]
end

function ShopWGData:GetLimitCfg(seq)
	-- body
	return self.limit_list[seq]
end

function ShopWGData:IsLimit(seq)
	-- body
	local limit_cfg = self:GetLimitCfg(seq)
	if not limit_cfg then return false end

	for k,v in pairs(limit_cfg) do
		for s_k,s_v in pairs(v) do
			if 0 ~= s_v then
				return true
			end
		end
	end

	return false
end

-- 己售馨标识
function ShopWGData:ItemEmpty(seq)
	local limit_cfg = self:GetLimitCfg(seq)
	local shop_info = self.all_shop_info[seq]
	if not limit_cfg or not shop_info then return false end
	local limit_num = 0

	-- 全服终身
	limit_num = limit_cfg.whole_life_limit_list[3]
	if 0 < limit_num and limit_num <= shop_info.server_whole_life_buy_cnt then
		return true, Language.Shop.EmptyDes3
	end

	-- 仙盟终身
	limit_num = limit_cfg.whole_life_limit_list[2]
	if 0 < limit_num and limit_num <= shop_info.guild_whole_life_buy_cnt then
		return true, Language.Shop.EmptyDes2
	end

	-- 个人终身
	limit_num = limit_cfg.whole_life_limit_list[1]
	if 0 < limit_num and limit_num <= shop_info.role_whole_life_buy_cnt then
		return true, Language.Shop.EmptyDes1
	end

	-- 全服每月
	limit_num = limit_cfg.month_limit_list[3]
	if 0 < limit_num and limit_num <= shop_info.server_month_buy_cnt then
		return true, Language.Shop.EmptyDes3
	end

	-- 仙盟每月
	limit_num = limit_cfg.month_limit_list[2]
	if 0 < limit_num and limit_num <= shop_info.guild_month_buy_cnt then
		return true, Language.Shop.EmptyDes2
	end

	-- 全服两周
	limit_num = limit_cfg.two_week_limit_list[3]
	if 0 < limit_num and limit_num <= shop_info.server_week_buy_cnt then
		return true, Language.Shop.EmptyDes3
	end

	-- 仙盟两周
	limit_num = limit_cfg.two_week_limit_list[2]
	if 0 < limit_num and limit_num <= shop_info.guild_week_buy_cnt then
		return true, Language.Shop.EmptyDes2
	end

	-- 全服每周
	limit_num = limit_cfg.week_limit_list[3]
	if 0 < limit_num and limit_num <= shop_info.server_week_buy_cnt then
		return true, Language.Shop.EmptyDes3
	end

	-- 仙盟每周
	limit_num = limit_cfg.week_limit_list[2]
	if 0 < limit_num and limit_num <= shop_info.guild_week_buy_cnt then
		return true, Language.Shop.EmptyDes2
	end

	-- 全服每日
	limit_num = limit_cfg.day_limit_list[3]
	if 0 < limit_num and limit_num <= shop_info.server_day_buy_cnt then
		return true, Language.Shop.EmptyDes3
	end

	-- 仙盟每日
	limit_num = limit_cfg.day_limit_list[2]
	if 0 < limit_num and limit_num <= shop_info.guild_day_buy_cnt then
		return true, Language.Shop.EmptyDes2
	end

	return false
end

-- 是否可购买
function ShopWGData:IsCanCell(seq, back_reason)
	local limit_cfg = self:GetLimitCfg(seq)
	local shop_info = self.all_shop_info[seq]
	local is_can_cell = true
	local str = nil
	if not limit_cfg or not shop_info then
        return is_can_cell, str
    end

	local limit_num = 0
	-- 全服终身
	limit_num = limit_cfg.whole_life_limit_list[3]
	if 0 < limit_num and is_can_cell then
		is_can_cell =  limit_num > shop_info.server_whole_life_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell10
		end
	end

	-- 仙盟终身
	limit_num = limit_cfg.whole_life_limit_list[2]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.guild_whole_life_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell11
		end
	end

	-- 个人终身
	limit_num = limit_cfg.whole_life_limit_list[1]
	if 0 < limit_num and is_can_cell then
		is_can_cell =  limit_num > shop_info.role_whole_life_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.EmptyDes1
		end
	end

	-- 全服每月
	limit_num = limit_cfg.month_limit_list[3]
	if 0 < limit_num and is_can_cell then
		is_can_cell =  limit_num > shop_info.server_month_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell1
		end
	end

	-- 仙盟每月
	limit_num = limit_cfg.month_limit_list[2]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.guild_month_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell2
		end
	end

	-- 个人每月
	limit_num = limit_cfg.month_limit_list[1]
	if 0 < limit_num and is_can_cell then
		is_can_cell =  limit_num > shop_info.role_month_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell3
		end
	end

	-- 全服两周
	limit_num = limit_cfg.two_week_limit_list[3]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.server_twoweek_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell4
		end
	end

	-- 仙盟两周
	limit_num = limit_cfg.two_week_limit_list[2]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.guild_twoweek_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell5
		end
	end

	-- 个人两周
	limit_num = limit_cfg.two_week_limit_list[1]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.role_twoweek_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell6
		end
	end

	-- 全服每周
	limit_num = limit_cfg.week_limit_list[3]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.server_week_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell7
		end
	end

	-- 仙盟每周
	limit_num = limit_cfg.week_limit_list[2]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.guild_week_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell8
		end
	end

	-- 个人每周
	limit_num = limit_cfg.week_limit_list[1]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.role_week_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell9
		end
	end

	-- 全服每日
	limit_num = limit_cfg.day_limit_list[3]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.server_day_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell10
		end
	end

	-- 仙盟每日
	limit_num = limit_cfg.day_limit_list[2]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.guild_day_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell11
		end
	end

	-- 个人每日
	limit_num = limit_cfg.day_limit_list[1]
	if 0 < limit_num and is_can_cell then
		is_can_cell = limit_num > shop_info.role_day_buy_cnt
		if not is_can_cell and back_reason then
			str = Language.Shop.IsCanCell12
		end
	end

	return is_can_cell, str
end

function ShopWGData:ContrastNum(limit_type, buy_limit_type, total_num, num, t_type)
	local flag = total_num > num
	local color = nil
	if total_num <= num then
		color = COLOR3B.L_RED
	else
		color = COLOR3B.L_GREEN
	end

	local str = nil
	local is_every = self:IsEvery(t_type)
	if not is_every then
		-- str = string.format(Language.Shop["BuyDes13"], color, total_num - num)
		-- str = Language.Shop["BuyType" .. limit_type] .. str

		str = Language.Shop["BuyType" .. limit_type] .. string.format(Language.Shop["BuyDes13"], color, num, total_num)
	else
		str = string.format("%s<color=%s>%s</color>/%s", Language.Shop["BuyDes" .. buy_limit_type], color, num, total_num)
	end
	return flag, str
end

-- 返回商品购买提示
function ShopWGData:ExplainComposeStr(seq)
	local color = nil
	local t_type = nil
	local list = {}
	local flag, str = nil, nil
	local is_life, compose_list = self:ExplainCompose(seq)
	-- print_error("list:::", seq, list)
	if nil == is_life then return list end

	local is_sell_out = false -- 单个条件己卖完
	local shop_info = self.all_shop_info[seq]
	-- print_error("list:::", seq, list)
	if not shop_info then return list end

	local buy_limit_type = nil
	-- print_error("==========",is_life, compose_list)
	-- 是否终生
	if is_life then
		buy_limit_type = ShopWGData.Buy_Limit_Type.whole_life_limit_list
		for k,v in pairs(compose_list) do
			-- 个人终生
			if k == ShopWGData.Limit_Type.Limit_Type1 then
				t_type = ShopWGData.Total_Limit_Type.T_Type13
				flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_whole_life_buy_cnt, t_type)
			elseif k == ShopWGData.Limit_Type.Limit_Type2 and 0 == GuildWGData.Instance:IsHaveGuild() then
				t_type = ShopWGData.Total_Limit_Type.T_Type14
				flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.guild_whole_life_buy_cnt, t_type)
			elseif k == ShopWGData.Limit_Type.Limit_Type3 then
				t_type = ShopWGData.Total_Limit_Type.T_Type15
				flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.server_whole_life_buy_cnt, t_type)
			end

			if not flag then
				str = str .. Language.Shop.BuyDes11
			end
			list[t_type] = {}
			list[t_type].flag = flag
			list[t_type].str = str
			list[t_type].t_type = t_type
		end
	else
		for k,v in pairs(compose_list) do
			-- 个人
			if k == ShopWGData.Limit_Type.Limit_Type1 then
				-- 个人每日
				buy_limit_type = ShopWGData.Buy_Limit_Type.day_limit_list
				if 0 ~= v[buy_limit_type] then
					-- 需要判断每周，两周，每月
					if v[buy_limit_type] > shop_info.role_day_buy_cnt then
						-- 每日可购
						if (0 == v[ShopWGData.Buy_Limit_Type.week_limit_list] or shop_info.role_week_buy_cnt < v[ShopWGData.Buy_Limit_Type.week_limit_list]) and
							(0 == v[ShopWGData.Buy_Limit_Type.two_week_limit_list] or shop_info.role_twoweek_buy_cnt < v[ShopWGData.Buy_Limit_Type.two_week_limit_list]) and
							(0 == v[ShopWGData.Buy_Limit_Type.month_limit_list] or shop_info.role_month_buy_cnt < v[ShopWGData.Buy_Limit_Type.month_limit_list]) then
							t_type = ShopWGData.Total_Limit_Type.T_Type1
							flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_day_buy_cnt, t_type)
							-- print_error("=====1===", flag, str)
							list[t_type] = {}
							list[t_type].t_type = t_type
							list[t_type].flag = flag
							list[t_type].str = str
							if not flag then
								is_sell_out = true
							end
						end
					else
						-- 每日不可购
						t_type = ShopWGData.Total_Limit_Type.T_Type1
						flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_day_buy_cnt, t_type)
						-- print_error("====2====", flag, str)
						if not flag then
							is_sell_out = true
							str = str .. Language.Shop.BuyDes11
						end
						list[t_type] = {}
						list[t_type].t_type = t_type
						list[t_type].flag = flag
						list[t_type].str = str
					end
				end

				-- 个人每周
				buy_limit_type = ShopWGData.Buy_Limit_Type.week_limit_list
				if 0 ~= v[buy_limit_type] then
					-- 需要判断每月,两周
					-- 每周可购
					if v[buy_limit_type] > shop_info.role_week_buy_cnt then
						if (0 == v[ShopWGData.Buy_Limit_Type.two_week_limit_list] or shop_info.role_twoweek_buy_cnt < v[ShopWGData.Buy_Limit_Type.two_week_limit_list]) and
							(0 == v[ShopWGData.Buy_Limit_Type.month_limit_list] or shop_info.role_month_buy_cnt < v[ShopWGData.Buy_Limit_Type.month_limit_list]) then
							t_type = ShopWGData.Total_Limit_Type.T_Type4
							flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_week_buy_cnt, t_type)
							-- print_error("====3====", flag, str)
							list[t_type] = {}
							list[t_type].t_type = t_type
							list[t_type].flag = flag
							list[t_type].str = str
							if not flag then
								is_sell_out = true
							end
						end
					else
						-- 每周不可购
						t_type = ShopWGData.Total_Limit_Type.T_Type4
						flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_week_buy_cnt, t_type)
						-- print_error("====4====", flag, str)
						if not flag then
							is_sell_out = true
							str = str .. Language.Shop.BuyDes11
						end
						list[t_type] = {}
						list[t_type].t_type = t_type
						list[t_type].flag = flag
						list[t_type].str = str
					end
				end

				-- 个人两周
				buy_limit_type = ShopWGData.Buy_Limit_Type.two_week_limit_list
				if 0 ~= v[buy_limit_type] then
					-- 需要判断每月
					-- 两周可购
					if v[buy_limit_type] > shop_info.role_twoweek_buy_cnt then
						if 0 == v[ShopWGData.Buy_Limit_Type.month_limit_list] or shop_info.role_month_buy_cnt < v[ShopWGData.Buy_Limit_Type.month_limit_list] then
							t_type = ShopWGData.Total_Limit_Type.T_Type7
							flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_twoweek_buy_cnt, t_type)
							-- print_error("====3====", flag, str)
							list[t_type] = {}
							list[t_type].t_type = t_type
							list[t_type].flag = flag
							list[t_type].str = str
							if not flag then
								is_sell_out = true
							end
						end
					else
						-- 两周不可购
						t_type = ShopWGData.Total_Limit_Type.T_Type7
						flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_twoweek_buy_cnt, t_type)
						-- print_error("====4====", flag, str)
						if not flag then
							is_sell_out = true
							str = str .. Language.Shop.BuyDes11
						end
						list[t_type] = {}
						list[t_type].t_type = t_type
						list[t_type].flag = flag
						list[t_type].str = str
					end
				end

				-- 个人每月
				buy_limit_type = ShopWGData.Buy_Limit_Type.month_limit_list
				if 0 ~= v[buy_limit_type] then
					-- 每月可购
					if v[buy_limit_type] > shop_info.role_month_buy_cnt then
						t_type = ShopWGData.Total_Limit_Type.T_Type10
						flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_month_buy_cnt, t_type)
						-- print_error("====5====", flag, str)
						list[t_type] = {}
						list[t_type].t_type = t_type
						list[t_type].flag = flag
						list[t_type].str = str
						if not flag then
							is_sell_out = true
						end
					else
						-- 每月不可购
						t_type = ShopWGData.Total_Limit_Type.T_Type10
						flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.role_month_buy_cnt, t_type)
						-- print_error("=====6===", flag, str)
						if not flag then
							is_sell_out = true
							str = str .. Language.Shop.BuyDes11
						end
						list[t_type] = {}
						list[t_type].t_type = t_type
						list[t_type].flag = flag
						list[t_type].str = str
					end
				end
			elseif k == ShopWGData.Limit_Type.Limit_Type2 and 0 == GuildWGData.Instance:IsHaveGuild() then
				-- 仙盟每日
				buy_limit_type = ShopWGData.Buy_Limit_Type.day_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type2
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.guild_day_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

				-- 仙盟每周
				buy_limit_type = ShopWGData.Buy_Limit_Type.week_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type5
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.guild_week_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

				-- 仙盟两周
				buy_limit_type = ShopWGData.Buy_Limit_Type.two_week_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type8
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.guild_twoweek_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

				-- 仙盟每月
				buy_limit_type = ShopWGData.Buy_Limit_Type.month_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type11
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.guild_month_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

			elseif k == ShopWGData.Limit_Type.Limit_Type3 then
				-- 全服每日
				buy_limit_type = ShopWGData.Buy_Limit_Type.day_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type3
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.server_day_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

				-- 全服每周
				buy_limit_type = ShopWGData.Buy_Limit_Type.week_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type6
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.server_week_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

				-- 全服两周
				buy_limit_type = ShopWGData.Buy_Limit_Type.two_week_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type9
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.server_twoweek_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end

				-- 全服每月
				buy_limit_type = ShopWGData.Buy_Limit_Type.month_limit_list
				if 0 ~= v[buy_limit_type] then
					t_type = ShopWGData.Total_Limit_Type.T_Type12
					flag, str = self:ContrastNum(k, buy_limit_type, v[buy_limit_type], shop_info.server_month_buy_cnt, t_type)
					list[t_type] = {}
					list[t_type].t_type = t_type
					list[t_type].flag = flag
					list[t_type].str = str
					if not flag then
						is_sell_out = true
					end
				end
			end

		end
	end
	-- print_error("list:::", list)
	if IsEmptyTable(list) then return list end
	local start_index = 0
	local end_index = ShopWGData.Total_Limit_Type.Max_Type
	for i=1,end_index do
		if list[i] then
			start_index = i
			break
		end
	end

	-- print_error("list:::", start_index, end_index, #ShopWGData.Total_Limit_Type, list)
	local tab = {}
	local role_sell_out = false -- 个人限制己卖完
	-- 获取第一个不可购买的
	tab[1] = (list[ShopWGData.Total_Limit_Type.T_Type1] and not list[ShopWGData.Total_Limit_Type.T_Type1].flag and list[ShopWGData.Total_Limit_Type.T_Type1]) or nil
	tab[1] = tab[1] or (list[ShopWGData.Total_Limit_Type.T_Type4] and not list[ShopWGData.Total_Limit_Type.T_Type4].flag and list[ShopWGData.Total_Limit_Type.T_Type4]) or nil
	tab[1] = tab[1] or (list[ShopWGData.Total_Limit_Type.T_Type7] and not list[ShopWGData.Total_Limit_Type.T_Type7].flag and list[ShopWGData.Total_Limit_Type.T_Type7]) or nil
	tab[1] = tab[1] or (list[ShopWGData.Total_Limit_Type.T_Type10] and not list[ShopWGData.Total_Limit_Type.T_Type10].flag and list[ShopWGData.Total_Limit_Type.T_Type10]) or nil
	role_sell_out = nil ~= tab[1]
	-- print_error("tab:::", tab)
	-- 可购买时，从后往前取
	tab[1] = tab[1] or list[ShopWGData.Total_Limit_Type.T_Type1] or list[ShopWGData.Total_Limit_Type.T_Type4] or list[ShopWGData.Total_Limit_Type.T_Type7] or list[ShopWGData.Total_Limit_Type.T_Type10]
	tab[1] = tab[1] or list[start_index]
	start_index = tab[1].t_type
	-- print_error("start_index:::", start_index, is_sell_out, role_sell_out)

	if start_index == ShopWGData.Total_Limit_Type.T_Type1 or
		start_index == ShopWGData.Total_Limit_Type.T_Type4 or
		start_index == ShopWGData.Total_Limit_Type.T_Type7 or
		start_index == ShopWGData.Total_Limit_Type.T_Type10 then
		local s = 1
		local e = 1
		local space = 1
		if role_sell_out then
			-- s = end_index
			-- e = start_index
			-- space = -1
			s = start_index
			e = end_index
			space = 1
		else
			s = start_index
			e = end_index
			space = 1
		end
		-- 先判断范围购买己卖完
		for i=s,e,space do
		-- print_error("==1==", role_sell_out, tab[1], list[i])
			if list[i] and
				list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type1 and
				list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type4 and
				list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type7 and
				list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type10 and
				not list[i].flag then
				tab[2] = list[i]
				break
			end
		end

		-- 判断范围购买未卖完
		if not tab[2] then
			for i=s,e,space do
			-- print_error("==1==", role_sell_out, tab[1], list[i])
				if list[i] and
					(not role_sell_out or (role_sell_out and tab[1].flag ~= list[i].flag)) and
					list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type1 and
					list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type4 and
					list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type7 and
					list[i].t_type ~= ShopWGData.Total_Limit_Type.T_Type10 then
					tab[2] = list[i]
					break
				end
			end
		end
	end

	-- print_error("tab:::", tab)
	return tab
end

-- 是否个人购买
function ShopWGData:IsEvery(info)
	if not info then return false end
	local t_type = nil
	if type(info) == "number" then
		t_type = info
	else
		t_type = info.t_type
	end

	return t_type == ShopWGData.Total_Limit_Type.T_Type1 or
			t_type == ShopWGData.Total_Limit_Type.T_Type4 or
			t_type == ShopWGData.Total_Limit_Type.T_Type7 or
			t_type == ShopWGData.Total_Limit_Type.T_Type10 or
			t_type == ShopWGData.Total_Limit_Type.T_Type13
end

-- 返回
-- is_life 是否终生限购
-- compose_list 筛选出的四种限购数量
function ShopWGData:ExplainCompose(seq)
	-- body
	local is_life = nil -- 是否终生限购
	local limit_cfg = self:GetLimitCfg(seq)
	if not limit_cfg then return is_life end

	-- 收集所有限购数据
	local compose_list = {}

	local cfg = nil
	for i=1,#ShopWGData.ComposeStrList do
		cfg = limit_cfg[ShopWGData.ComposeStrList[i]]
		for k,v in pairs(cfg) do
			if not compose_list[k] then
				compose_list[k] = {}
			end
			-- print_error("v:::", k, v, type(v))
			table.insert(compose_list[k], v)
		end
	end

	-- print_error("compose_list:::", compose_list)
	-- 筛选限购类型
	local num = 0
	for k,v in pairs(compose_list) do
		num = 0
		for s_k,s_v in pairs(v) do
			num = num + s_v
		end
		if 0 == num then
			compose_list[k] = nil
		elseif nil == is_life then
			local buy_limit_type = ShopWGData.Buy_Limit_Type.whole_life_limit_list
			-- print_error("buy_limit_type:::", buy_limit_type, v)
			is_life = 0 ~= v[buy_limit_type]
		end
	end

	if nil == is_life then
		-- print_error("限购类型有错，请检查配置:::", seq)
		-- 购买无限制
		return is_life
	end

	return is_life, compose_list
end

function ShopWGData:ShopTitle()
	-- body
	for k, v in pairs(self.shop_type_cfg) do
		self.shop_title1[k] = v.shop_title
	end

	local index = 0
	local len = 0
	local group_list = nil
	local key = nil
	for i=1,#self.page_type_cfg do
		self.shop_title2[i] = {}
		group_list = {}
		len = #self.page_type_cfg[i]
		if 0 < len then
			for j=1,len do
				self.shop_title2[i][j] = self.page_type_cfg[i][j].page_title
				index = i + 10 + j
				key = ShopWGData.Tab_Shop .. index
				ShopWGData.TabIndex[key] = index
				group_list[j] = key
			end

			key = ShopWGData.Tab_Shop .. (i + 10)
			SetRemindGroud(key, group_list)
			SetRemindName(key, key)
			self.shop_ver_mind[key] = i
			self.shop_remind_group[i] = group_list
		end
	end

	for k,v in pairs(ShopWGData.TabIndex) do
		self.shop_hor_mind[k] = v
		SetTabIndex(k, v)
		SetRemindName(k, k)
	end
end

function ShopWGData:CheckOpenTab()
	local condition_cfg = nil
	local tab_cfg = nil
	local index = 0
	local status = true
	local len = 0
	self.hor_tab_status = {}
	self.ver_tab_status = {}

	for i=1,#self.page_type_cfg do
		status = true
		self.hor_tab_status[i] = {}
		len = #self.page_type_cfg[i]
		for j=1,len do
			tab_cfg = self.page_type_cfg[i][j]
			condition_cfg = self.condition_cfg[tab_cfg.open_condition]
			self.hor_tab_status[i][j] = not condition_cfg or ConditionManager.Instance:CheckSingle(condition_cfg)
			status = status and self.hor_tab_status[i][j]
		end
		self.ver_tab_status[i] = status
	end
end

function ShopWGData:GetHorTabStatus(ver_tab)
	-- body
	local shop_type = math.floor(ver_tab / 10)
	return self.hor_tab_status[shop_type]
end

function ShopWGData:AllShopInfo(value)
	if not value then
		return self.all_shop_info
	end

	for k,v in pairs(value.items) do
		self.all_shop_info[v.item_index] = v
	end

	-- print_error("all_shop_info:::", self.all_shop_info)
end

function ShopWGData:ChangeShopInfo(value)
	-- body
	self.all_shop_info[value.item.item_index] = value.item
end

function ShopWGData:GetAllShopInfoBySeq(seq)
    return self.all_shop_info[seq]
end

function ShopWGData:GetItemDataList(index, is_flush)
	local shop_type = index

	local list = {}
	local item_list = self.type_item_cfg[shop_type]

	if not item_list then
		return list
	end

	---[[ **策划又不用显示卡了
	-- if VipWGData.Instance:IsForeverVip() then 永久卡激活后移除卡购买
		local vip_card_liat = VipWGData.Instance:GetVipCardCfg()
		local key_list = {}
		for k,v in pairs(vip_card_liat) do
			key_list[v.item_id] = true
		end
		local data_list = {}
		for k,v in pairs(item_list) do
			if not key_list[v.itemid] then
				data_list[v.seq] = v
			end
		end
		item_list = data_list
	-- end
	--]]

	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(item_list) do
		if self:CheckItemCondition(v.seq, 2) and (5 == v.zhiye_show_limit or v.zhiye_show_limit == main_role_vo.prof) then
			table.insert(list, v)
		end
	end

	-- print_error("list:::", list)
	local function func(info1, info2)
		local can_cell1 = self:IsCanCell(info1.seq)
		local can_cell2 = self:IsCanCell(info2.seq)

		if can_cell1 ~= can_cell2 then
			return can_cell1
		elseif can_cell1 and can_cell2 then
			return info1.sort_id < info2.sort_id
		end

		return info1.sort_id < info2.sort_id
	end

	table.sort(list, func)
	-- print_error("list:::", list)
	if not is_flush then
		self:SetCacheItem(list)
	end
	return list
end

-- 基于CheckItemCondition 不满足时
-- 检测是否只有指定条件不满足
-- cond_type指定条件
function ShopWGData:CheckConditionState(seq, check_type, cond_type)
	-- body
	check_type = check_type or 1
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg or not cond_type then
		return false
	end

	local condition_id = nil
	if 1 == check_type then
		condition_id = shop_cfg.buy_condition
	elseif 2 == check_type then
		condition_id = shop_cfg.show_condition
	end
	local condition_cfg, condition_type = self:GetCondition(condition_id)
	if not condition_cfg or IsEmptyTable(condition_cfg) then
		return false
	end

	local flag_list = nil
	local flag
	if 0 == #condition_cfg then
		flag = ConditionManager.Instance:CheckSingle(condition_cfg)
		flag_list = {}
		flag_list[condition_cfg.condition_type] = flag
	else
		flag_list = ConditionManager.Instance:CheckConditionList(condition_cfg)
	end
	-- print_error("flag_list:::", cond_type, flag_list)
	for k,v in pairs(flag_list) do
		if not v and k ~= cond_type then
			return false
		end
	end

	return true
end

-- 检测条件
-- check_type 1：购买检测， 2：显示检测
-- back_reason Language里的枚举
function ShopWGData:CheckItemCondition(seq, check_type, back_reason)
	check_type = check_type or 1
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg then return false end
	local condition_id = nil
	if 1 == check_type then
		condition_id = shop_cfg.buy_condition
	elseif 2 == check_type then
		condition_id = shop_cfg.show_condition
	end
	local condition_cfg, condition_type = self:GetCondition(condition_id)
	if not condition_cfg or IsEmptyTable(condition_cfg) then return false end

	local flag, str = nil, nil
	if 0 == #condition_cfg then
		flag, str = ConditionManager.Instance:CheckSingle(condition_cfg, back_reason)
	else
		flag, str = ConditionManager.Instance:CheckList(condition_cfg, condition_type, back_reason)
	end

	return flag, str, condition_cfg.condition_type
end

function ShopWGData:GetConditionByItemId(item_id)
	local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(item_id)
	if shop_cfg then
		local condition_cfg = self:GetCondition(shop_cfg.buy_condition)
		return condition_cfg
	end
	return nil
end

function ShopWGData:GetCondition(condition_id)
	local condition_cfg = self.condition_cfg[condition_id]
	if not condition_cfg then return {} end

	if condition_cfg.condition_type == ConditionManager.Check_Type.Type1 or
		condition_cfg.condition_type == ConditionManager.Check_Type.Type2 then
		local list = {}
		-- 只能嵌套一层
		for i=1,3 do
			condition_id = condition_cfg["param" .. i]
			if 0 ~= condition_id then
				list[i] = self.condition_cfg[condition_id]
			end
		end
		SortTools.SortAsc(list,"sort")
		return list, condition_cfg.condition_type
	else
		return condition_cfg
	end
end

--是否VIP等级限制
function ShopWGData:IsVipLimit(seq)
	local shop_cfg = self.item_seq_cfg[seq]
	local condition_id = shop_cfg.buy_condition
	local condition_cfg, condition_type = self:GetCondition(condition_id)
	local check_cfg = nil
	local is_show = false
	local is_vip = false
	local str = nil
	local is_can_buy = self:CheckItemCondition(seq)
	if 0 == #condition_cfg then
		is_vip = condition_cfg.condition_type == ConditionManager.Check_Type.Type103
		is_show = is_vip
		check_cfg = condition_cfg
		return is_show, check_cfg.param1, is_can_buy, check_cfg.param1
	else
		for k,v in pairs(condition_cfg) do
			if not is_vip then
				is_vip = v.condition_type == ConditionManager.Check_Type.Type103
				check_cfg = v
			end

			if condition_type == ConditionManager.Check_Type.Type1 and is_vip then
				is_show = true
				return is_show, check_cfg.param1, is_can_buy, check_cfg.param1
			elseif condition_type == ConditionManager.Check_Type.Type2 then
				if not is_can_buy then
					is_show = true
				end

				if is_show and is_vip then
					break
				end
			end
		end
	end

	if is_show and not is_can_buy then
		str = check_cfg.param1
	end

	return is_show, str, is_can_buy, check_cfg.param1
end

function ShopWGData:GetMaxBuyNum(seq)
	-- body
	local shop_cfg = self.item_seq_cfg[seq]
	local shop_info = self.all_shop_info[seq]
	if not shop_cfg then return 1 end
	if not shop_info then return 999 end

	local is_life, compose_list = self:ExplainCompose(seq)
	-- print_error("is_life:::", is_life, compose_list)
	if nil ==  is_life or not compose_list then return 999 end
	local buy_limit_type = nil
	local max = 0
	if is_life then
		for i=1,3 do
			buy_limit_type = ShopWGData.Buy_Limit_Type.whole_life_limit_list
			if compose_list[i] and 0 < compose_list[i][buy_limit_type] then
				if i == ShopWGData.Limit_Type.Limit_Type1 then
					max = compose_list[i][buy_limit_type] - shop_info.role_whole_life_buy_cnt
					break
				elseif i == ShopWGData.Limit_Type.Limit_Type2 then
					max = compose_list[i][buy_limit_type] - shop_info.guild_whole_life_buy_cnt
					break
				elseif i == ShopWGData.Limit_Type.Limit_Type3 then
					max = compose_list[i][buy_limit_type] - shop_info.server_whole_life_buy_cnt
					break
				end
			end
		end
	else
		for i=1,3 do
			if compose_list[i] then
				buy_limit_type = ShopWGData.Buy_Limit_Type.day_limit_list
				if 0 < compose_list[i][buy_limit_type] then
					if i == ShopWGData.Limit_Type.Limit_Type1 then
						max = compose_list[i][buy_limit_type] - shop_info.role_day_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type2 then
						max = compose_list[i][buy_limit_type] - shop_info.guild_day_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type3 then
						max = compose_list[i][buy_limit_type] - shop_info.server_day_buy_cnt
						break
					end
				end

				buy_limit_type = ShopWGData.Buy_Limit_Type.week_limit_list
				if 0 < compose_list[i][buy_limit_type] then
					if i == ShopWGData.Limit_Type.Limit_Type1 then
						max = compose_list[i][buy_limit_type] - shop_info.role_week_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type2 then
						max = compose_list[i][buy_limit_type] - shop_info.guild_week_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type3 then
						max = compose_list[i][buy_limit_type] - shop_info.server_week_buy_cnt
						break
					end
				end

				buy_limit_type = ShopWGData.Buy_Limit_Type.two_week_limit_list
				if 0 < compose_list[i][buy_limit_type] then
					if i == ShopWGData.Limit_Type.Limit_Type1 then
						max = compose_list[i][buy_limit_type] - shop_info.role_twoweek_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type2 then
						max = compose_list[i][buy_limit_type] - shop_info.guild_twoweek_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type3 then
						max = compose_list[i][buy_limit_type] - shop_info.server_twoweek_buy_cnt
						break
					end
				end

				buy_limit_type = ShopWGData.Buy_Limit_Type.month_limit_list
				if 0 < compose_list[i][buy_limit_type] then
					if i == ShopWGData.Limit_Type.Limit_Type1 then
						max = compose_list[i][buy_limit_type] - shop_info.role_month_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type2 then
						max = compose_list[i][buy_limit_type] - shop_info.guild_month_buy_cnt
						break
					elseif i == ShopWGData.Limit_Type.Limit_Type3 then
						max = compose_list[i][buy_limit_type] - shop_info.server_month_buy_cnt
						break
					end
				end
			end
		end
	end
	-- print_error("max:::", max)
	max = math.max(0 , max)
	max = math.min(max, 999)
	return max
end

function ShopWGData.IsConsumeBind(consume_type)
	return false
	-- return consume_type == GameEnum.CONSUME_TYPE_BIND
end

function ShopWGData.GetItemGold(item_id, consume_type)
	-- local is_bind = ShopWGData.IsConsumeBind(consume_type)
	-- local cfg = ConfigManager.Instance:GetAutoConfig("shop_auto").item[item_id]
	-- if nil ~= cfg then
	-- 	return is_bind and cfg.bind_gold or cfg.gold
	-- end

	return 0
end

--获取物品价格 返回TAB
-- 己废弃
function ShopWGData.GetItemPrice(item_id)
	-- local  goods_cfg = ConfigManager.Instance:GetAutoConfig("shop_auto").fenqu
	-- local temp_tab = {}
	-- local has_bind = false
	-- for k,v in pairs(goods_cfg) do
	-- 	if v.item_id == item_id then
	-- 		if v.shop_type == 3 then
	-- 			has_bind = true
	-- 		end
	-- 	end
	-- end

	-- local cfg = ConfigManager.Instance:GetAutoConfig("shop_auto").item[item_id]
	-- if nil ~= cfg then
	-- 	if has_bind == true then
	-- 		temp_tab.bind_gold = cfg.bind_gold
	-- 	else
			-- temp_tab.bind_gold = 0
	-- 	end
		-- temp_tab.gold = cfg.gold
	-- end

	local temp_tab = {}
	temp_tab.bind_gold = 0
	temp_tab.gold = 0
	return temp_tab
end

function ShopWGData:IsHaveBindItem(item_id)
	for k,v in pairs(self.item_seq_cfg) do
		if item_id == v.itemid then
			return true
		end
	end

	return false
end

-- function ShopWGData:IsCanBuyByItemId(item_id)
-- 	local cfg = ConfigManager.Instance:GetAutoConfig("shop_auto").item[item_id]
-- 	if cfg ~= nil and cfg.bind_gold ~= 99999999 then
-- 		return true
-- 	end
-- 	return false
-- end

-- 只检测绑玉（旧 绑元）
function ShopWGData:CheckBangYuToBuy(seq, num, buy_price)
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg then
		return false
	end

	if shop_cfg.price_type ~= Shop_Money_Type.Type2 then
		return true
	end

	local role_info = RoleWGData.Instance:GetRoleInfo()
	local price = buy_price or shop_cfg.price
	return role_info.bind_gold >= price * num
end

function ShopWGData:CheckMoneyToBuy(seq, num, need_tips, show_vip, buy_price)
	local shop_cfg = self.item_seq_cfg[seq]
	if not shop_cfg then
		return false
	end

	local role_info = RoleWGData.Instance:GetRoleInfo()

	local flag = true
	local str = nil
	local price = buy_price or shop_cfg.price
	if shop_cfg.price_type == Shop_Money_Type.Type1 then
		if role_info.gold < price * num then
			flag = false
			if show_vip then
				UiInstanceMgr.Instance:ShowChongZhiView()
			elseif need_tips then
				str = Language.Shop.MoneyDes1
			end
		end
	elseif shop_cfg.price_type == Shop_Money_Type.Type2 then
		if role_info.bind_gold + role_info.gold < price * num then
			flag = false
			if need_tips then
				str = Language.Shop.MoneyDes2
			end
		end
	elseif shop_cfg.price_type == Shop_Money_Type.Type4 then
		if role_info.shengwang < price * num then
			flag = false
			if need_tips then
				str = Language.Shop.MoneyDes4
			end
		end
	elseif shop_cfg.price_type == Shop_Money_Type.Type5 then
		if role_info.coin < price * num then
			flag = false
			if need_tips then
				str = Language.Shop.MoneyDes5
			end
		end
	elseif shop_cfg.price_type == Shop_Money_Type.Type7 then
		if role_info.chivalrous < price * num then
			flag = false
			if need_tips then
				str = Language.Shop.MoneyDes7
			end
		end
	end

	if not flag and str then
		SysMsgWGCtrl.Instance:ErrorRemind(str)
	end

	return flag
end

function ShopWGData:IsShowShopRedPoint()
	-- self:CheckAllAddItem()
	if IsEmptyTable(self.add_list) then return 0 end
	return 1
end

function ShopWGData:GetShopTypeRedPoint(shop_type)
	-- body
	if IsEmptyTable(self.add_list) then return 0 end
	for k,v in pairs(self.add_list) do
		if v.shop_type == shop_type then
			return 1
		end
	end

	return 0
end

function ShopWGData:GetPageTypeRedPoint(index)
	-- body
	if IsEmptyTable(self.add_list) then return 0 end
	local shop_auto = math.floor(index / 10)
	local page_type = index % 10

	for k,v in pairs(self.add_list) do
		if shop_auto == v.shop_type and page_type == v.page_type then
			return 1
		end
	end

	return 0
end

function ShopWGData:GetNewItemRedPoint(seq)
	-- body
	if not self.copy_new_item_list or IsEmptyTable(self.copy_new_item_list) then return 0 end
	return self.copy_new_item_list[seq] and 1 or 0
end

function ShopWGData:CheckAllAddItem()
	-- body
	-- self.add_list = {}
	-- local list = {}
	-- for k,v in pairs(self.shop_auto.item) do
	-- 	if self:CheckItemCondition(v.seq, 2) then
	-- 		-- 要进缓存的，只保存少量数据
	-- 		list[v.seq] = {seq = v.seq, itemid = v.itemid, shop_type = v.shop_type, page_type = v.page_type}
	-- 	end
	-- end

	-- if not self.cache_attr then
	-- 	self:GetCacheAttr()
	-- end

	-- -- print_error("cache_attr:::", self.cache_attr)
	-- -- print_error("list:::", list)
	-- if not self.cache_attr or IsEmptyTable(self.cache_attr) then
	-- 	self.cache_attr = list
	-- 	self:SetCacheAttr()
	-- 	return
	-- end

	-- for k,v in pairs(list) do
	-- 	if not self.cache_attr[k] and ShopWGData.ShowShopType[v.shop_type] then
	-- 		self.add_list[v.seq] = v
	-- 	end
	-- end

end

-- 去掉新物品标识，并把它写进缓存
function ShopWGData:SetCacheItem(list)
	-- self.copy_new_item_list = {}
	-- if not list or IsEmptyTable(list) or IsEmptyTable(self.add_list) then
	-- 	return
	-- end

	-- for k,v in pairs(list) do
	-- 	for a_k,a_v in pairs(self.add_list) do
	-- 		if v.seq == a_v.seq then
	-- 			self.cache_attr[v.seq] = a_v
	-- 			self.copy_new_item_list[v.seq] = a_v
	-- 			self.add_list[a_k] = nil
	-- 			break
	-- 		end
	-- 	end
	-- end

	-- -- 清空新商品时，通知主红点
	-- if IsEmptyTable(self.add_list) then
	-- 	RemindManager.Instance:Fire(RemindName.Shop)
	-- end
	-- self:SetCacheAttr()
end

function ShopWGData:GetCacheAttr()
end

function ShopWGData:SetCacheAttr()
end

function ShopWGData:GetShopCfgSeq(seq)
	-- body
	return self.item_seq_cfg[seq]
end

--仅限于商品表itemid唯一的使用  不唯一慎用！！！
function  ShopWGData:GetShopCfgItemId(item_id)
	local item_seq_cfg = ConfigManager.Instance:GetAutoConfig("shopconfig_auto").item
	for k,v in pairs(item_seq_cfg) do
		if v.itemid == item_id then
			return v
		end
	end
	return nil
end
--物品id，价格类型(根据id和价格类型获取价格)
function  ShopWGData:GetShopCfgItemIdPrice(item_id,price_type)
	if nil == item_id or nil == price_type or nil == self.item_seq_cfg_1 then
		return 0
	end
	if self.item_seq_cfg_1[item_id] and self.item_seq_cfg_1[item_id][price_type] then
		return self.item_seq_cfg_1[item_id][price_type].price
	end
	return 0
end

-- 获取商品配置
function  ShopWGData:GetShopItemCfg(item_id, price_type)
	if nil == item_id or nil == price_type or nil == self.item_seq_cfg_1 then
		return nil
	end
	if self.item_seq_cfg_1[item_id] then
		return self.item_seq_cfg_1[item_id][price_type]
	end
	return nil
end

function ShopWGData:CheckOpenIndex()
	-- body
	local index = nil
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(self.shop_auto.page_type) do
		if v.min_level <= main_role_vo.level and main_role_vo.level <= v.max_level then
			index = v.shop_type + 10 + v.page_type
			break
		end
	end

	if not index then
		for k,v in pairs(self.shop_hor_mind) do
			if 1 == self:GetPageTypeRedPoint(v) then
				index = v
				break
			end
		end
	end

	return index or 21
end

function ShopWGData:GetShopList(item_id, buy_get_way)
	--[[
	local bit_list = bit:d2b_two(buy_get_way)
	local index = 1
	local shop_type_list = {}
	for i=0,#bit_list do
		if 1 == bit_list[i] and self.shop_auto.shop_buy_way[i + 1] then
			shop_type_list[index] = self.shop_auto.shop_buy_way[i + 1].shop_type
			index = index + 1
		end
	end
	--]]

	local item_list = {}
	for k,v in pairs(self.item_seq_cfg) do
		if item_id == v.itemid then
			table.insert(item_list, v)
		end
	end

	SortTools.SortAsc(item_list, "shop_type")

	return item_list
end

-- 获取商城跳转TabIndex
function ShopWGData:GetShopTabIndex(seq)
	-- body
	local shop_cfg = self:GetShopCfgSeq(seq)
	if not shop_cfg then return nil end
	return shop_cfg.shop_type + 10
end

-- 获取商城跳转TabIndex
function ShopWGData:GetShopTabNumIndex(item_id)
	local shop_cfg = self:GetShopCfgItemId(item_id)
	if not shop_cfg then return 0 end

	return shop_cfg.shop_type + 10
end

-- 检测功能开启的道具是否己买过
function ShopWGData:CheckOpenFunShop(item_id)
	local shop_cfg = self:GetShopCfgItemId(item_id)
	if not shop_cfg then
        return false
    end

	return not self:IsCanCell(shop_cfg.seq)
end

function ShopWGData:GetItemCfgBySeq(seq)
	return self.item_seq_cfg[seq]
end

function ShopWGData:GetShopTypeCfg()
	return self.shop_type_cfg or {}
end

function ShopWGData:GetPrivilegeShopBigType()
	local tab_index_list = {[10] = "season_privilege_shop", [11] = "sup_privilege_shop"}
	local name_table = {}
	local num = 1
	for k,v in pairs(self.shop_type_cfg) do
		for k1,v1 in pairs(tab_index_list) do
			if v.shop_type == k1 and FunOpen.Instance:GetFunIsOpened(v1) then
				local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(k1)
				if is_open then
					name_table[num] = {}
					name_table[num].type_data = v
					num = num + 1
				end
			end
		end
	end
	
	return name_table , num -1
end

function ShopWGData:GetItemSellTabIndex(item_id)
	if not IsEmptyTable(self.item_seq_cfg_1[item_id]) then
		for k,v in pairs(self.item_seq_cfg_1[item_id]) do
			if ShopWGData.ShowShopType[v.shop_type] then
				return v.shop_type + 10
			end
		end
	end

	for k, v in pairs(ShopWGData.ShowShopType) do
		if v then
			return k + 10
		end
	end
	return -1
end

function ShopWGData:GetCurIndex()
	return self.wait_jump_index or -1
end

function ShopWGData:SetCurIndex(index)
	self.wait_jump_index = index
end

function ShopWGData:ClearIndex()
	self.wait_jump_index = nil
end

function ShopWGData:GetDefaultIndex()
	for k, v in pairs(ShopWGData.ShowShopType) do
		if v then
			return k + 10
		end
	end
end

---[[ 活动期间打折物品
function ShopWGData:GetActDiscountByItemSeq(item_seq)
	local cfg = self.act_discount_cfg[item_seq]
	if not cfg then
		return 0
	end
	if cfg.activity_id == ACTIVITY_TYPE.OPEN_SERVER then
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPEN_SERVER) then
			local rush_type = cfg.param1
			local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
			local close_day = opengame_cfg.close_day_index
			local open_day_index = opengame_cfg.open_day_index
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			local now_time = TimeWGCtrl.Instance:GetServerTime()
			local format_time = os.date("*t", now_time)
			local end_time = os.time({
				year = format_time.year,
				month = format_time.month,
				day = format_time.day + close_day - open_day + 1,
				hour = 0,
				min = 0,
				sec = 0
			})
			-- 23:00结算
			if end_time - 3600 > now_time and open_day >= open_day_index then
				return cfg.discount_count
			end
		end
	end
	return 0
end
--]]

--商城充值红点每日上线显示一次
function ShopWGData:IsShowRechargeRedPoint()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "ShopRecharge")
	local remind_day = PlayerPrefsUtil.GetInt(key)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_day ~= remind_day then
		return true
	end
end

function ShopWGData:SetRechargeRedPoint()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "ShopRecharge")
	PlayerPrefsUtil.SetInt(key, cur_day)
end

function ShopWGData:MainUIShopRemind()
	if not FunOpen.Instance:GetFunIsOpened("shop") then
		return 0
	end
	if self:IsShowRechargeRedPoint() then
		return 1
	else
		return 0
	end
end

-- 是否是商店的货币道具
function ShopWGData:CheckIsMoneyExchangeItem(item_id)
	local cfg = self:GetShopTypeCfg()

	if cfg then
		for i, v in ipairs(cfg) do
			if v ~= nil and v.item_id == item_id then
				return true, v
			end
		end
	end

	return false, nil
end

-- 获取对应货币能够兑换的奖励展示
function ShopWGData:GetMoneyExchangeShowList(item_id)
	local final_list = nil
	local _, data = self:CheckIsMoneyExchangeItem(item_id)

	if data and data.exchange_item_show ~= nil and (not IsEmptyTable(data.exchange_item_show)) and data.exchange_title_tips ~= nil and 
	data.exchange_title_tips ~= "" then
		local final_list = {}
		final_list.suit_item_list_1 = {}
		final_list.suit_item_list_2 = {}
		local final_indx_1 = COMMON_CONSTS.NUMBER_ZERO
		local final_indx_2 = COMMON_CONSTS.NUMBER_ZERO

		for k, v in pairs(data.exchange_item_show) do
			if v and v.item_id then
				if final_indx_1 <= 3 then
					final_list.suit_item_list_1[final_indx_1] = v
					final_indx_1 = final_indx_1 + 1
				else
					final_list.suit_item_list_2[final_indx_2] = v
					final_indx_2 = final_indx_2 + 1
				end
			end
		end

		return final_list, data.exchange_title_tips
	end

	return final_list, ""
end