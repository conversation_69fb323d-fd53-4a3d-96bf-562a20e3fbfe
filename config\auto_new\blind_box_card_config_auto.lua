-- Y-运营活动-盲盒卡包.xls
local item_table={
[1]={item_id=50506,num=1,is_bind=1},
[2]={item_id=50512,num=1,is_bind=1},
[3]={item_id=50518,num=1,is_bind=1},
[4]={item_id=50501,num=1,is_bind=1},
[5]={item_id=50507,num=1,is_bind=1},
[6]={item_id=50513,num=1,is_bind=1},
[7]={item_id=50519,num=1,is_bind=1},
[8]={item_id=50502,num=1,is_bind=1},
[9]={item_id=50508,num=1,is_bind=1},
[10]={item_id=50514,num=1,is_bind=1},
[11]={item_id=50520,num=1,is_bind=1},
[12]={item_id=50503,num=1,is_bind=1},
[13]={item_id=50509,num=1,is_bind=1},
[14]={item_id=50515,num=1,is_bind=1},
[15]={item_id=50521,num=1,is_bind=1},
[16]={item_id=50504,num=1,is_bind=1},
[17]={item_id=50510,num=1,is_bind=1},
[18]={item_id=50516,num=1,is_bind=1},
[19]={item_id=50522,num=1,is_bind=1},
[20]={item_id=50505,num=1,is_bind=1},
[21]={item_id=50511,num=1,is_bind=1},
[22]={item_id=50517,num=1,is_bind=1},
[23]={item_id=50523,num=1,is_bind=1},
[24]={item_id=50524,num=1,is_bind=1},
[25]={item_id=50530,num=1,is_bind=1},
[26]={item_id=50536,num=1,is_bind=1},
[27]={item_id=50542,num=1,is_bind=1},
[28]={item_id=50525,num=1,is_bind=1},
[29]={item_id=50531,num=1,is_bind=1},
[30]={item_id=50537,num=1,is_bind=1},
[31]={item_id=50543,num=1,is_bind=1},
[32]={item_id=50526,num=1,is_bind=1},
[33]={item_id=50532,num=1,is_bind=1},
[34]={item_id=50538,num=1,is_bind=1},
[35]={item_id=50544,num=1,is_bind=1},
[36]={item_id=50527,num=1,is_bind=1},
[37]={item_id=50533,num=1,is_bind=1},
[38]={item_id=50539,num=1,is_bind=1},
[39]={item_id=50545,num=1,is_bind=1},
[40]={item_id=50528,num=1,is_bind=1},
[41]={item_id=50534,num=1,is_bind=1},
[42]={item_id=50540,num=1,is_bind=1},
[43]={item_id=50546,num=1,is_bind=1},
[44]={item_id=50529,num=1,is_bind=1},
[45]={item_id=50535,num=1,is_bind=1},
[46]={item_id=50541,num=1,is_bind=1},
[47]={item_id=50547,num=1,is_bind=1},
[48]={item_id=50548,num=1,is_bind=1},
[49]={item_id=50554,num=1,is_bind=1},
[50]={item_id=50560,num=1,is_bind=1},
[51]={item_id=50566,num=1,is_bind=1},
[52]={item_id=50549,num=1,is_bind=1},
[53]={item_id=50555,num=1,is_bind=1},
[54]={item_id=50561,num=1,is_bind=1},
[55]={item_id=50567,num=1,is_bind=1},
[56]={item_id=50550,num=1,is_bind=1},
[57]={item_id=50556,num=1,is_bind=1},
[58]={item_id=50562,num=1,is_bind=1},
[59]={item_id=50568,num=1,is_bind=1},
[60]={item_id=50551,num=1,is_bind=1},
[61]={item_id=50557,num=1,is_bind=1},
[62]={item_id=50563,num=1,is_bind=1},
[63]={item_id=50569,num=1,is_bind=1},
[64]={item_id=50552,num=1,is_bind=1},
[65]={item_id=50558,num=1,is_bind=1},
[66]={item_id=50564,num=1,is_bind=1},
[67]={item_id=50570,num=1,is_bind=1},
[68]={item_id=50553,num=1,is_bind=1},
[69]={item_id=50559,num=1,is_bind=1},
[70]={item_id=50565,num=1,is_bind=1},
[71]={item_id=50571,num=1,is_bind=1},
[72]={item_id=50572,num=1,is_bind=1},
[73]={item_id=50578,num=1,is_bind=1},
[74]={item_id=50584,num=1,is_bind=1},
[75]={item_id=50590,num=1,is_bind=1},
[76]={item_id=50573,num=1,is_bind=1},
[77]={item_id=50579,num=1,is_bind=1},
[78]={item_id=50585,num=1,is_bind=1},
[79]={item_id=50591,num=1,is_bind=1},
[80]={item_id=50574,num=1,is_bind=1},
[81]={item_id=50580,num=1,is_bind=1},
[82]={item_id=50586,num=1,is_bind=1},
[83]={item_id=50592,num=1,is_bind=1},
[84]={item_id=50575,num=1,is_bind=1},
[85]={item_id=50581,num=1,is_bind=1},
[86]={item_id=50587,num=1,is_bind=1},
[87]={item_id=50593,num=1,is_bind=1},
[88]={item_id=50576,num=1,is_bind=1},
[89]={item_id=50582,num=1,is_bind=1},
[90]={item_id=50588,num=1,is_bind=1},
[91]={item_id=50594,num=1,is_bind=1},
[92]={item_id=50577,num=1,is_bind=1},
[93]={item_id=50583,num=1,is_bind=1},
[94]={item_id=50589,num=1,is_bind=1},
[95]={item_id=50595,num=1,is_bind=1},
[96]={item_id=50596,num=1,is_bind=1},
[97]={item_id=50602,num=1,is_bind=1},
[98]={item_id=50608,num=1,is_bind=1},
[99]={item_id=50614,num=1,is_bind=1},
[100]={item_id=50597,num=1,is_bind=1},
[101]={item_id=50603,num=1,is_bind=1},
[102]={item_id=50609,num=1,is_bind=1},
[103]={item_id=50615,num=1,is_bind=1},
[104]={item_id=50598,num=1,is_bind=1},
[105]={item_id=50604,num=1,is_bind=1},
[106]={item_id=50610,num=1,is_bind=1},
[107]={item_id=50616,num=1,is_bind=1},
[108]={item_id=50599,num=1,is_bind=1},
[109]={item_id=50605,num=1,is_bind=1},
[110]={item_id=50611,num=1,is_bind=1},
[111]={item_id=50617,num=1,is_bind=1},
[112]={item_id=50600,num=1,is_bind=1},
[113]={item_id=50606,num=1,is_bind=1},
[114]={item_id=50612,num=1,is_bind=1},
[115]={item_id=50618,num=1,is_bind=1},
[116]={item_id=50601,num=1,is_bind=1},
[117]={item_id=50607,num=1,is_bind=1},
[118]={item_id=50613,num=1,is_bind=1},
[119]={item_id=50619,num=1,is_bind=1},
[120]={item_id=50620,num=1,is_bind=1},
[121]={item_id=50626,num=1,is_bind=1},
[122]={item_id=50632,num=1,is_bind=1},
[123]={item_id=50638,num=1,is_bind=1},
[124]={item_id=50621,num=1,is_bind=1},
[125]={item_id=50627,num=1,is_bind=1},
[126]={item_id=50633,num=1,is_bind=1},
[127]={item_id=50639,num=1,is_bind=1},
[128]={item_id=50622,num=1,is_bind=1},
[129]={item_id=50628,num=1,is_bind=1},
[130]={item_id=50634,num=1,is_bind=1},
[131]={item_id=50640,num=1,is_bind=1},
[132]={item_id=50623,num=1,is_bind=1},
[133]={item_id=50629,num=1,is_bind=1},
[134]={item_id=50635,num=1,is_bind=1},
[135]={item_id=50641,num=1,is_bind=1},
[136]={item_id=50624,num=1,is_bind=1},
[137]={item_id=50630,num=1,is_bind=1},
[138]={item_id=50636,num=1,is_bind=1},
[139]={item_id=50642,num=1,is_bind=1},
[140]={item_id=50625,num=1,is_bind=1},
[141]={item_id=50631,num=1,is_bind=1},
[142]={item_id=50637,num=1,is_bind=1},
[143]={item_id=50643,num=1,is_bind=1},
[144]={item_id=50644,num=1,is_bind=1},
[145]={item_id=50650,num=1,is_bind=1},
[146]={item_id=50656,num=1,is_bind=1},
[147]={item_id=50662,num=1,is_bind=1},
[148]={item_id=50645,num=1,is_bind=1},
[149]={item_id=50651,num=1,is_bind=1},
[150]={item_id=50657,num=1,is_bind=1},
[151]={item_id=50663,num=1,is_bind=1},
[152]={item_id=50646,num=1,is_bind=1},
[153]={item_id=50652,num=1,is_bind=1},
[154]={item_id=50658,num=1,is_bind=1},
[155]={item_id=50664,num=1,is_bind=1},
[156]={item_id=50647,num=1,is_bind=1},
[157]={item_id=50653,num=1,is_bind=1},
[158]={item_id=50659,num=1,is_bind=1},
[159]={item_id=50665,num=1,is_bind=1},
[160]={item_id=50648,num=1,is_bind=1},
[161]={item_id=50654,num=1,is_bind=1},
[162]={item_id=50660,num=1,is_bind=1},
[163]={item_id=50666,num=1,is_bind=1},
[164]={item_id=50649,num=1,is_bind=1},
[165]={item_id=50655,num=1,is_bind=1},
[166]={item_id=50661,num=1,is_bind=1},
[167]={item_id=50667,num=1,is_bind=1},
[168]={item_id=50668,num=1,is_bind=1},
[169]={item_id=50674,num=1,is_bind=1},
[170]={item_id=50680,num=1,is_bind=1},
[171]={item_id=50686,num=1,is_bind=1},
[172]={item_id=50669,num=1,is_bind=1},
[173]={item_id=50675,num=1,is_bind=1},
[174]={item_id=50681,num=1,is_bind=1},
[175]={item_id=50687,num=1,is_bind=1},
[176]={item_id=50670,num=1,is_bind=1},
[177]={item_id=50676,num=1,is_bind=1},
[178]={item_id=50682,num=1,is_bind=1},
[179]={item_id=50688,num=1,is_bind=1},
[180]={item_id=50671,num=1,is_bind=1},
[181]={item_id=50677,num=1,is_bind=1},
[182]={item_id=50683,num=1,is_bind=1},
[183]={item_id=50689,num=1,is_bind=1},
[184]={item_id=50672,num=1,is_bind=1},
[185]={item_id=50678,num=1,is_bind=1},
[186]={item_id=50684,num=1,is_bind=1},
[187]={item_id=50690,num=1,is_bind=1},
[188]={item_id=50673,num=1,is_bind=1},
[189]={item_id=50679,num=1,is_bind=1},
[190]={item_id=50685,num=1,is_bind=1},
[191]={item_id=50691,num=1,is_bind=1},
[192]={item_id=50692,num=1,is_bind=1},
[193]={item_id=50698,num=1,is_bind=1},
[194]={item_id=50704,num=1,is_bind=1},
[195]={item_id=50710,num=1,is_bind=1},
[196]={item_id=50693,num=1,is_bind=1},
[197]={item_id=50699,num=1,is_bind=1},
[198]={item_id=50705,num=1,is_bind=1},
[199]={item_id=50711,num=1,is_bind=1},
[200]={item_id=50694,num=1,is_bind=1},
[201]={item_id=50700,num=1,is_bind=1},
[202]={item_id=50706,num=1,is_bind=1},
[203]={item_id=50712,num=1,is_bind=1},
[204]={item_id=50695,num=1,is_bind=1},
[205]={item_id=50701,num=1,is_bind=1},
[206]={item_id=50707,num=1,is_bind=1},
[207]={item_id=50713,num=1,is_bind=1},
[208]={item_id=50696,num=1,is_bind=1},
[209]={item_id=50702,num=1,is_bind=1},
[210]={item_id=50708,num=1,is_bind=1},
[211]={item_id=50714,num=1,is_bind=1},
[212]={item_id=50697,num=1,is_bind=1},
[213]={item_id=50703,num=1,is_bind=1},
[214]={item_id=50709,num=1,is_bind=1},
[215]={item_id=50715,num=1,is_bind=1},
[216]={item_id=50716,num=1,is_bind=1},
[217]={item_id=50722,num=1,is_bind=1},
[218]={item_id=50728,num=1,is_bind=1},
[219]={item_id=50734,num=1,is_bind=1},
[220]={item_id=50717,num=1,is_bind=1},
[221]={item_id=50723,num=1,is_bind=1},
[222]={item_id=50729,num=1,is_bind=1},
[223]={item_id=50735,num=1,is_bind=1},
[224]={item_id=50718,num=1,is_bind=1},
[225]={item_id=50724,num=1,is_bind=1},
[226]={item_id=50730,num=1,is_bind=1},
[227]={item_id=50736,num=1,is_bind=1},
[228]={item_id=50719,num=1,is_bind=1},
[229]={item_id=50725,num=1,is_bind=1},
[230]={item_id=50731,num=1,is_bind=1},
[231]={item_id=50737,num=1,is_bind=1},
[232]={item_id=50720,num=1,is_bind=1},
[233]={item_id=50726,num=1,is_bind=1},
[234]={item_id=50732,num=1,is_bind=1},
[235]={item_id=50738,num=1,is_bind=1},
[236]={item_id=50721,num=1,is_bind=1},
[237]={item_id=50727,num=1,is_bind=1},
[238]={item_id=50733,num=1,is_bind=1},
[239]={item_id=50739,num=1,is_bind=1},
[240]={item_id=50740,num=1,is_bind=1},
[241]={item_id=50746,num=1,is_bind=1},
[242]={item_id=50752,num=1,is_bind=1},
[243]={item_id=50758,num=1,is_bind=1},
[244]={item_id=50741,num=1,is_bind=1},
[245]={item_id=50747,num=1,is_bind=1},
[246]={item_id=50753,num=1,is_bind=1},
[247]={item_id=50759,num=1,is_bind=1},
[248]={item_id=50742,num=1,is_bind=1},
[249]={item_id=50748,num=1,is_bind=1},
[250]={item_id=50754,num=1,is_bind=1},
[251]={item_id=50760,num=1,is_bind=1},
[252]={item_id=50743,num=1,is_bind=1},
[253]={item_id=50749,num=1,is_bind=1},
[254]={item_id=50755,num=1,is_bind=1},
[255]={item_id=50761,num=1,is_bind=1},
[256]={item_id=50744,num=1,is_bind=1},
[257]={item_id=50750,num=1,is_bind=1},
[258]={item_id=50756,num=1,is_bind=1},
[259]={item_id=50762,num=1,is_bind=1},
[260]={item_id=50745,num=1,is_bind=1},
[261]={item_id=50751,num=1,is_bind=1},
[262]={item_id=50757,num=1,is_bind=1},
[263]={item_id=50763,num=1,is_bind=1},
[264]={item_id=50764,num=1,is_bind=1},
[265]={item_id=50770,num=1,is_bind=1},
[266]={item_id=50776,num=1,is_bind=1},
[267]={item_id=50782,num=1,is_bind=1},
[268]={item_id=50765,num=1,is_bind=1},
[269]={item_id=50771,num=1,is_bind=1},
[270]={item_id=50777,num=1,is_bind=1},
[271]={item_id=50783,num=1,is_bind=1},
[272]={item_id=50766,num=1,is_bind=1},
[273]={item_id=50772,num=1,is_bind=1},
[274]={item_id=50778,num=1,is_bind=1},
[275]={item_id=50784,num=1,is_bind=1},
[276]={item_id=50767,num=1,is_bind=1},
[277]={item_id=50773,num=1,is_bind=1},
[278]={item_id=50779,num=1,is_bind=1},
[279]={item_id=50785,num=1,is_bind=1},
[280]={item_id=50768,num=1,is_bind=1},
[281]={item_id=50774,num=1,is_bind=1},
[282]={item_id=50780,num=1,is_bind=1},
[283]={item_id=50786,num=1,is_bind=1},
[284]={item_id=50769,num=1,is_bind=1},
[285]={item_id=50775,num=1,is_bind=1},
[286]={item_id=50781,num=1,is_bind=1},
[287]={item_id=50787,num=1,is_bind=1},
[288]={item_id=27661,num=1,is_bind=1},
[289]={item_id=27681,num=1,is_bind=1},
[290]={item_id=26666,num=1,is_bind=1},
[291]={item_id=26681,num=1,is_bind=1},
[292]={item_id=57601,num=1,is_bind=1},
[293]={item_id=27662,num=1,is_bind=1},
[294]={item_id=27682,num=1,is_bind=1},
[295]={item_id=26667,num=1,is_bind=1},
[296]={item_id=26682,num=1,is_bind=1},
[297]={item_id=57602,num=1,is_bind=1},
[298]={item_id=27663,num=1,is_bind=1},
[299]={item_id=27683,num=1,is_bind=1},
[300]={item_id=26668,num=1,is_bind=1},
[301]={item_id=26683,num=1,is_bind=1},
[302]={item_id=57603,num=1,is_bind=1},
[303]={item_id=27664,num=1,is_bind=1},
[304]={item_id=27684,num=1,is_bind=1},
[305]={item_id=26669,num=1,is_bind=1},
[306]={item_id=26684,num=1,is_bind=1},
[307]={item_id=57604,num=1,is_bind=1},
[308]={item_id=27665,num=1,is_bind=1},
[309]={item_id=27685,num=1,is_bind=1},
[310]={item_id=26670,num=1,is_bind=1},
[311]={item_id=26685,num=1,is_bind=1},
[312]={item_id=57605,num=1,is_bind=1},
[313]={item_id=27666,num=1,is_bind=1},
[314]={item_id=27686,num=1,is_bind=1},
[315]={item_id=26671,num=1,is_bind=1},
[316]={item_id=26686,num=1,is_bind=1},
[317]={item_id=57606,num=1,is_bind=1},
[318]={item_id=27660,num=1,is_bind=1},
[319]={item_id=27680,num=1,is_bind=1},
[320]={item_id=26665,num=1,is_bind=1},
[321]={item_id=26680,num=1,is_bind=1},
[322]={item_id=57600,num=1,is_bind=1},
[323]={item_id=26347,num=1,is_bind=1},
[324]={item_id=26348,num=1,is_bind=1},
[325]={item_id=26349,num=1,is_bind=1},
[326]={item_id=26350,num=1,is_bind=1},
[327]={item_id=26351,num=1,is_bind=1},
[328]={item_id=26352,num=1,is_bind=1},
[329]={item_id=26353,num=1,is_bind=1},
[330]={item_id=26354,num=1,is_bind=1},
[331]={item_id=50500,num=1,is_bind=1},
[332]={item_id=26346,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
grade={
{},
{grade=1,start_server_day=21,end_server_day=40,grade_name="十二星座",model_bundle_name="model/zuoqi/2108_prefab",model_asset_name=2108,model_show_itemid=23351,},
{grade=2,start_server_day=41,end_server_day=9999,grade_name="十二猎神",model_bundle_name="model/zuoqi/2109_prefab",model_asset_name=2109,model_show_itemid=23352,}
},

grade_meta_table_map={
},
active={
{},
{seq=1,reward_lib=2,seq_name="十二生肖·乙",},
{seq=2,reward_lib=3,seq_name="十二生肖·丙",},
{seq=3,reward_lib=4,seq_name="十二生肖·丁",},
{grade=1,reward_lib=5,seq_name="星座十二·甲",},
{grade=1,seq=1,reward_lib=6,seq_name="星座十二·乙",},
{grade=1,seq=2,reward_lib=7,seq_name="星座十二·丙",},
{grade=1,seq=3,reward_lib=8,seq_name="星座十二·丁",},
{grade=2,reward_lib=9,seq_name="奇剑利器·甲",},
{grade=2,seq=1,reward_lib=10,seq_name="奇剑利器·乙",},
{grade=2,seq=2,reward_lib=11,seq_name="奇剑利器·丙",},
{grade=2,seq=3,reward_lib=12,seq_name="奇剑利器·丁",}
},

active_meta_table_map={
},
reward={
{},
{reward_id=2,reward_item={[0]=item_table[1]},},
{reward_id=3,reward_item={[0]=item_table[2]},},
{reward_id=4,reward_item={[0]=item_table[3]},},
{reward_id=5,reward_item={[0]=item_table[4]},},
{reward_id=6,reward_item={[0]=item_table[5]},},
{reward_id=7,reward_item={[0]=item_table[6]},},
{reward_id=8,reward_item={[0]=item_table[7]},},
{reward_id=9,reward_item={[0]=item_table[8]},},
{reward_id=10,reward_item={[0]=item_table[9]},},
{reward_id=11,reward_item={[0]=item_table[10]},},
{reward_id=12,reward_item={[0]=item_table[11]},},
{reward_id=13,reward_item={[0]=item_table[12]},},
{reward_id=14,reward_item={[0]=item_table[13]},},
{reward_id=15,reward_item={[0]=item_table[14]},},
{reward_id=16,reward_item={[0]=item_table[15]},},
{reward_id=17,reward_item={[0]=item_table[16]},reward_type=1,player_guarantee=50,reward_limit=20,broadcast=1,},
{reward_id=18,reward_item={[0]=item_table[17]},},
{reward_id=19,reward_item={[0]=item_table[18]},},
{reward_id=20,reward_item={[0]=item_table[19]},},
{reward_id=21,reward_item={[0]=item_table[20]},},
{reward_id=22,reward_item={[0]=item_table[21]},},
{reward_id=23,reward_item={[0]=item_table[22]},},
{reward_id=24,reward_item={[0]=item_table[23]},player_guarantee=95,reward_limit=10,},
{reward_lib=2,reward_item={[0]=item_table[24]},},
{reward_lib=2,reward_item={[0]=item_table[25]},},
{reward_lib=2,reward_item={[0]=item_table[26]},},
{reward_lib=2,reward_item={[0]=item_table[27]},},
{reward_lib=2,reward_item={[0]=item_table[28]},},
{reward_lib=2,reward_item={[0]=item_table[29]},},
{reward_lib=2,reward_item={[0]=item_table[30]},},
{reward_lib=2,reward_item={[0]=item_table[31]},},
{reward_lib=2,reward_item={[0]=item_table[32]},},
{reward_lib=2,reward_item={[0]=item_table[33]},},
{reward_lib=2,reward_item={[0]=item_table[34]},},
{reward_lib=2,reward_item={[0]=item_table[35]},},
{reward_lib=2,reward_item={[0]=item_table[36]},},
{reward_lib=2,reward_item={[0]=item_table[37]},},
{reward_lib=2,reward_item={[0]=item_table[38]},},
{reward_lib=2,reward_item={[0]=item_table[39]},},
{reward_lib=2,reward_item={[0]=item_table[40]},},
{reward_lib=2,reward_item={[0]=item_table[41]},},
{reward_lib=2,reward_item={[0]=item_table[42]},},
{reward_lib=2,reward_item={[0]=item_table[43]},},
{reward_id=21,reward_item={[0]=item_table[44]},},
{reward_id=22,reward_item={[0]=item_table[45]},},
{reward_id=23,reward_item={[0]=item_table[46]},},
{reward_lib=2,reward_item={[0]=item_table[47]},},
{reward_lib=3,reward_item={[0]=item_table[48]},},
{reward_lib=3,reward_item={[0]=item_table[49]},},
{reward_lib=3,reward_item={[0]=item_table[50]},},
{reward_lib=3,reward_item={[0]=item_table[51]},},
{reward_lib=3,reward_item={[0]=item_table[52]},},
{reward_lib=3,reward_item={[0]=item_table[53]},},
{reward_lib=3,reward_item={[0]=item_table[54]},},
{reward_lib=3,reward_item={[0]=item_table[55]},},
{reward_lib=3,reward_item={[0]=item_table[56]},},
{reward_lib=3,reward_item={[0]=item_table[57]},},
{reward_lib=3,reward_item={[0]=item_table[58]},},
{reward_lib=3,reward_item={[0]=item_table[59]},},
{reward_lib=3,reward_item={[0]=item_table[60]},},
{reward_lib=3,reward_item={[0]=item_table[61]},},
{reward_lib=3,reward_item={[0]=item_table[62]},},
{reward_lib=3,reward_item={[0]=item_table[63]},},
{reward_lib=3,reward_item={[0]=item_table[64]},},
{reward_lib=3,reward_item={[0]=item_table[65]},},
{reward_lib=3,reward_item={[0]=item_table[66]},},
{reward_lib=3,reward_item={[0]=item_table[67]},},
{reward_id=21,reward_item={[0]=item_table[68]},},
{reward_id=22,reward_item={[0]=item_table[69]},},
{reward_id=23,reward_item={[0]=item_table[70]},},
{reward_lib=3,reward_item={[0]=item_table[71]},},
{reward_lib=4,reward_item={[0]=item_table[72]},},
{reward_lib=4,reward_item={[0]=item_table[73]},},
{reward_lib=4,reward_item={[0]=item_table[74]},},
{reward_lib=4,reward_item={[0]=item_table[75]},},
{reward_lib=4,reward_item={[0]=item_table[76]},},
{reward_lib=4,reward_item={[0]=item_table[77]},},
{reward_lib=4,reward_item={[0]=item_table[78]},},
{reward_lib=4,reward_item={[0]=item_table[79]},},
{reward_lib=4,reward_item={[0]=item_table[80]},},
{reward_lib=4,reward_item={[0]=item_table[81]},},
{reward_lib=4,reward_item={[0]=item_table[82]},},
{reward_lib=4,reward_item={[0]=item_table[83]},},
{reward_lib=4,reward_item={[0]=item_table[84]},},
{reward_lib=4,reward_item={[0]=item_table[85]},},
{reward_lib=4,reward_item={[0]=item_table[86]},},
{reward_lib=4,reward_item={[0]=item_table[87]},},
{reward_lib=4,reward_item={[0]=item_table[88]},},
{reward_lib=4,reward_item={[0]=item_table[89]},},
{reward_lib=4,reward_item={[0]=item_table[90]},},
{reward_lib=4,reward_item={[0]=item_table[91]},},
{reward_id=21,reward_item={[0]=item_table[92]},},
{reward_id=22,reward_item={[0]=item_table[93]},},
{reward_id=23,reward_item={[0]=item_table[94]},},
{reward_lib=4,reward_item={[0]=item_table[95]},},
{reward_lib=5,reward_item={[0]=item_table[96]},},
{reward_lib=5,reward_item={[0]=item_table[97]},},
{reward_lib=5,reward_item={[0]=item_table[98]},},
{reward_lib=5,reward_item={[0]=item_table[99]},},
{reward_lib=5,reward_item={[0]=item_table[100]},},
{reward_lib=5,reward_item={[0]=item_table[101]},},
{reward_lib=5,reward_item={[0]=item_table[102]},},
{reward_lib=5,reward_item={[0]=item_table[103]},},
{reward_lib=5,reward_item={[0]=item_table[104]},},
{reward_lib=5,reward_item={[0]=item_table[105]},},
{reward_lib=5,reward_item={[0]=item_table[106]},},
{reward_lib=5,reward_item={[0]=item_table[107]},},
{reward_lib=5,reward_item={[0]=item_table[108]},},
{reward_lib=5,reward_item={[0]=item_table[109]},},
{reward_lib=5,reward_item={[0]=item_table[110]},},
{reward_lib=5,reward_item={[0]=item_table[111]},},
{reward_lib=5,reward_item={[0]=item_table[112]},},
{reward_lib=5,reward_item={[0]=item_table[113]},},
{reward_lib=5,reward_item={[0]=item_table[114]},},
{reward_lib=5,reward_item={[0]=item_table[115]},},
{reward_lib=5,reward_item={[0]=item_table[116]},},
{reward_lib=5,reward_item={[0]=item_table[117]},},
{reward_lib=5,reward_item={[0]=item_table[118]},},
{reward_lib=5,reward_item={[0]=item_table[119]},},
{reward_lib=6,reward_item={[0]=item_table[120]},},
{reward_lib=6,reward_item={[0]=item_table[121]},},
{reward_lib=6,reward_item={[0]=item_table[122]},},
{reward_lib=6,reward_item={[0]=item_table[123]},},
{reward_lib=6,reward_item={[0]=item_table[124]},},
{reward_lib=6,reward_item={[0]=item_table[125]},},
{reward_lib=6,reward_item={[0]=item_table[126]},},
{reward_lib=6,reward_item={[0]=item_table[127]},},
{reward_lib=6,reward_item={[0]=item_table[128]},},
{reward_lib=6,reward_item={[0]=item_table[129]},},
{reward_lib=6,reward_item={[0]=item_table[130]},},
{reward_lib=6,reward_item={[0]=item_table[131]},},
{reward_lib=6,reward_item={[0]=item_table[132]},},
{reward_lib=6,reward_item={[0]=item_table[133]},},
{reward_lib=6,reward_item={[0]=item_table[134]},},
{reward_lib=6,reward_item={[0]=item_table[135]},},
{reward_lib=6,reward_item={[0]=item_table[136]},},
{reward_id=18,reward_item={[0]=item_table[137]},},
{reward_id=19,reward_item={[0]=item_table[138]},},
{reward_id=20,reward_item={[0]=item_table[139]},},
{reward_id=21,reward_item={[0]=item_table[140]},},
{reward_id=22,reward_item={[0]=item_table[141]},},
{reward_id=23,reward_item={[0]=item_table[142]},},
{reward_lib=6,reward_item={[0]=item_table[143]},},
{reward_lib=7,reward_item={[0]=item_table[144]},},
{reward_lib=7,reward_item={[0]=item_table[145]},},
{reward_lib=7,reward_item={[0]=item_table[146]},},
{reward_lib=7,reward_item={[0]=item_table[147]},},
{reward_lib=7,reward_item={[0]=item_table[148]},},
{reward_lib=7,reward_item={[0]=item_table[149]},},
{reward_lib=7,reward_item={[0]=item_table[150]},},
{reward_lib=7,reward_item={[0]=item_table[151]},},
{reward_lib=7,reward_item={[0]=item_table[152]},},
{reward_lib=7,reward_item={[0]=item_table[153]},},
{reward_lib=7,reward_item={[0]=item_table[154]},},
{reward_lib=7,reward_item={[0]=item_table[155]},},
{reward_lib=7,reward_item={[0]=item_table[156]},},
{reward_lib=7,reward_item={[0]=item_table[157]},},
{reward_lib=7,reward_item={[0]=item_table[158]},},
{reward_lib=7,reward_item={[0]=item_table[159]},},
{reward_lib=7,reward_item={[0]=item_table[160]},},
{reward_lib=7,reward_item={[0]=item_table[161]},},
{reward_lib=7,reward_item={[0]=item_table[162]},},
{reward_lib=7,reward_item={[0]=item_table[163]},},
{reward_id=21,reward_item={[0]=item_table[164]},},
{reward_id=22,reward_item={[0]=item_table[165]},},
{reward_id=23,reward_item={[0]=item_table[166]},},
{reward_lib=7,reward_item={[0]=item_table[167]},},
{reward_lib=8,reward_item={[0]=item_table[168]},},
{reward_lib=8,reward_item={[0]=item_table[169]},},
{reward_lib=8,reward_item={[0]=item_table[170]},},
{reward_lib=8,reward_item={[0]=item_table[171]},},
{reward_lib=8,reward_item={[0]=item_table[172]},},
{reward_lib=8,reward_item={[0]=item_table[173]},},
{reward_lib=8,reward_item={[0]=item_table[174]},},
{reward_lib=8,reward_item={[0]=item_table[175]},},
{reward_lib=8,reward_item={[0]=item_table[176]},},
{reward_lib=8,reward_item={[0]=item_table[177]},},
{reward_lib=8,reward_item={[0]=item_table[178]},},
{reward_lib=8,reward_item={[0]=item_table[179]},},
{reward_lib=8,reward_item={[0]=item_table[180]},},
{reward_lib=8,reward_item={[0]=item_table[181]},},
{reward_lib=8,reward_item={[0]=item_table[182]},},
{reward_lib=8,reward_item={[0]=item_table[183]},},
{reward_lib=8,reward_item={[0]=item_table[184]},},
{reward_lib=8,reward_item={[0]=item_table[185]},},
{reward_lib=8,reward_item={[0]=item_table[186]},},
{reward_lib=8,reward_item={[0]=item_table[187]},},
{reward_id=21,reward_item={[0]=item_table[188]},},
{reward_id=22,reward_item={[0]=item_table[189]},},
{reward_id=23,reward_item={[0]=item_table[190]},},
{reward_lib=8,reward_item={[0]=item_table[191]},},
{reward_lib=9,reward_item={[0]=item_table[192]},},
{reward_lib=9,reward_item={[0]=item_table[193]},},
{reward_lib=9,reward_item={[0]=item_table[194]},},
{reward_lib=9,reward_item={[0]=item_table[195]},},
{reward_lib=9,reward_item={[0]=item_table[196]},},
{reward_lib=9,reward_item={[0]=item_table[197]},},
{reward_lib=9,reward_item={[0]=item_table[198]},},
{reward_lib=9,reward_item={[0]=item_table[199]},},
{reward_lib=9,reward_item={[0]=item_table[200]},},
{reward_lib=9,reward_item={[0]=item_table[201]},},
{reward_lib=9,reward_item={[0]=item_table[202]},},
{reward_lib=9,reward_item={[0]=item_table[203]},},
{reward_lib=9,reward_item={[0]=item_table[204]},},
{reward_lib=9,reward_item={[0]=item_table[205]},},
{reward_lib=9,reward_item={[0]=item_table[206]},},
{reward_lib=9,reward_item={[0]=item_table[207]},},
{reward_lib=9,reward_item={[0]=item_table[208]},},
{reward_lib=9,reward_item={[0]=item_table[209]},},
{reward_lib=9,reward_item={[0]=item_table[210]},},
{reward_lib=9,reward_item={[0]=item_table[211]},},
{reward_id=21,reward_item={[0]=item_table[212]},},
{reward_id=22,reward_item={[0]=item_table[213]},},
{reward_lib=9,reward_item={[0]=item_table[214]},},
{reward_lib=9,reward_item={[0]=item_table[215]},},
{reward_lib=10,reward_item={[0]=item_table[216]},},
{reward_lib=10,reward_item={[0]=item_table[217]},},
{reward_lib=10,reward_item={[0]=item_table[218]},},
{reward_lib=10,reward_item={[0]=item_table[219]},},
{reward_lib=10,reward_item={[0]=item_table[220]},},
{reward_lib=10,reward_item={[0]=item_table[221]},},
{reward_lib=10,reward_item={[0]=item_table[222]},},
{reward_lib=10,reward_item={[0]=item_table[223]},},
{reward_lib=10,reward_item={[0]=item_table[224]},},
{reward_lib=10,reward_item={[0]=item_table[225]},},
{reward_lib=10,reward_item={[0]=item_table[226]},},
{reward_lib=10,reward_item={[0]=item_table[227]},},
{reward_lib=10,reward_item={[0]=item_table[228]},},
{reward_lib=10,reward_item={[0]=item_table[229]},},
{reward_lib=10,reward_item={[0]=item_table[230]},},
{reward_lib=10,reward_item={[0]=item_table[231]},},
{reward_lib=10,reward_item={[0]=item_table[232]},},
{reward_id=18,reward_item={[0]=item_table[233]},},
{reward_id=19,reward_item={[0]=item_table[234]},},
{reward_id=20,reward_item={[0]=item_table[235]},},
{reward_id=21,reward_item={[0]=item_table[236]},},
{reward_id=22,reward_item={[0]=item_table[237]},},
{reward_id=23,reward_item={[0]=item_table[238]},},
{reward_lib=10,reward_item={[0]=item_table[239]},},
{reward_lib=11,reward_item={[0]=item_table[240]},},
{reward_lib=11,reward_item={[0]=item_table[241]},},
{reward_lib=11,reward_item={[0]=item_table[242]},},
{reward_lib=11,reward_item={[0]=item_table[243]},},
{reward_lib=11,reward_item={[0]=item_table[244]},},
{reward_lib=11,reward_item={[0]=item_table[245]},},
{reward_lib=11,reward_item={[0]=item_table[246]},},
{reward_lib=11,reward_item={[0]=item_table[247]},},
{reward_lib=11,reward_item={[0]=item_table[248]},},
{reward_lib=11,reward_item={[0]=item_table[249]},},
{reward_lib=11,reward_item={[0]=item_table[250]},},
{reward_lib=11,reward_item={[0]=item_table[251]},},
{reward_lib=11,reward_item={[0]=item_table[252]},},
{reward_lib=11,reward_item={[0]=item_table[253]},},
{reward_lib=11,reward_item={[0]=item_table[254]},},
{reward_lib=11,reward_item={[0]=item_table[255]},},
{reward_lib=11,reward_item={[0]=item_table[256]},},
{reward_id=18,reward_item={[0]=item_table[257]},},
{reward_id=19,reward_item={[0]=item_table[258]},},
{reward_lib=11,reward_item={[0]=item_table[259]},},
{reward_lib=11,reward_item={[0]=item_table[260]},},
{reward_lib=11,reward_item={[0]=item_table[261]},},
{reward_id=23,reward_item={[0]=item_table[262]},},
{reward_lib=11,reward_item={[0]=item_table[263]},},
{reward_lib=12,reward_item={[0]=item_table[264]},},
{reward_lib=12,reward_item={[0]=item_table[265]},},
{reward_lib=12,reward_item={[0]=item_table[266]},},
{reward_lib=12,reward_item={[0]=item_table[267]},},
{reward_lib=12,reward_item={[0]=item_table[268]},},
{reward_lib=12,reward_item={[0]=item_table[269]},},
{reward_lib=12,reward_item={[0]=item_table[270]},},
{reward_lib=12,reward_item={[0]=item_table[271]},},
{reward_lib=12,reward_item={[0]=item_table[272]},},
{reward_lib=12,reward_item={[0]=item_table[273]},},
{reward_lib=12,reward_item={[0]=item_table[274]},},
{reward_lib=12,reward_item={[0]=item_table[275]},},
{reward_lib=12,reward_item={[0]=item_table[276]},},
{reward_lib=12,reward_item={[0]=item_table[277]},},
{reward_lib=12,reward_item={[0]=item_table[278]},},
{reward_lib=12,reward_item={[0]=item_table[279]},},
{reward_lib=12,reward_item={[0]=item_table[280]},},
{reward_id=18,reward_item={[0]=item_table[281]},},
{reward_id=19,reward_item={[0]=item_table[282]},},
{reward_id=20,reward_item={[0]=item_table[283]},},
{reward_lib=12,reward_item={[0]=item_table[284]},},
{reward_lib=12,reward_item={[0]=item_table[285]},},
{reward_lib=12,reward_item={[0]=item_table[286]},},
{reward_lib=12,reward_item={[0]=item_table[287]},}
},

reward_meta_table_map={
[146]=2,	-- depth:1
[147]=3,	-- depth:1
[148]=4,	-- depth:1
[149]=5,	-- depth:1
[150]=6,	-- depth:1
[159]=15,	-- depth:1
[152]=8,	-- depth:1
[153]=9,	-- depth:1
[154]=10,	-- depth:1
[155]=11,	-- depth:1
[156]=12,	-- depth:1
[157]=13,	-- depth:1
[158]=14,	-- depth:1
[160]=16,	-- depth:1
[151]=7,	-- depth:1
[247]=7,	-- depth:1
[136]=16,	-- depth:1
[249]=9,	-- depth:1
[122]=2,	-- depth:1
[123]=3,	-- depth:1
[124]=4,	-- depth:1
[125]=5,	-- depth:1
[126]=6,	-- depth:1
[127]=7,	-- depth:1
[128]=8,	-- depth:1
[129]=9,	-- depth:1
[130]=10,	-- depth:1
[248]=8,	-- depth:1
[131]=11,	-- depth:1
[133]=13,	-- depth:1
[134]=14,	-- depth:1
[135]=15,	-- depth:1
[246]=6,	-- depth:1
[254]=14,	-- depth:1
[253]=13,	-- depth:1
[252]=12,	-- depth:1
[251]=11,	-- depth:1
[250]=10,	-- depth:1
[132]=12,	-- depth:1
[245]=5,	-- depth:1
[175]=7,	-- depth:1
[243]=3,	-- depth:1
[200]=8,	-- depth:1
[201]=9,	-- depth:1
[202]=10,	-- depth:1
[203]=11,	-- depth:1
[204]=12,	-- depth:1
[205]=13,	-- depth:1
[206]=14,	-- depth:1
[207]=15,	-- depth:1
[208]=16,	-- depth:1
[199]=7,	-- depth:1
[230]=14,	-- depth:1
[228]=12,	-- depth:1
[227]=11,	-- depth:1
[226]=10,	-- depth:1
[225]=9,	-- depth:1
[224]=8,	-- depth:1
[223]=7,	-- depth:1
[218]=2,	-- depth:1
[219]=3,	-- depth:1
[220]=4,	-- depth:1
[229]=13,	-- depth:1
[244]=4,	-- depth:1
[198]=6,	-- depth:1
[196]=4,	-- depth:1
[242]=2,	-- depth:1
[170]=2,	-- depth:1
[171]=3,	-- depth:1
[172]=4,	-- depth:1
[173]=5,	-- depth:1
[174]=6,	-- depth:1
[255]=15,	-- depth:1
[176]=8,	-- depth:1
[177]=9,	-- depth:1
[197]=5,	-- depth:1
[178]=10,	-- depth:1
[180]=12,	-- depth:1
[181]=13,	-- depth:1
[182]=14,	-- depth:1
[183]=15,	-- depth:1
[184]=16,	-- depth:1
[232]=16,	-- depth:1
[231]=15,	-- depth:1
[194]=2,	-- depth:1
[195]=3,	-- depth:1
[179]=11,	-- depth:1
[256]=16,	-- depth:1
[111]=15,	-- depth:1
[64]=16,	-- depth:1
[54]=6,	-- depth:1
[55]=7,	-- depth:1
[56]=8,	-- depth:1
[57]=9,	-- depth:1
[58]=10,	-- depth:1
[59]=11,	-- depth:1
[60]=12,	-- depth:1
[61]=13,	-- depth:1
[62]=14,	-- depth:1
[63]=15,	-- depth:1
[278]=14,	-- depth:1
[277]=13,	-- depth:1
[276]=12,	-- depth:1
[275]=11,	-- depth:1
[274]=10,	-- depth:1
[273]=9,	-- depth:1
[272]=8,	-- depth:1
[53]=5,	-- depth:1
[271]=7,	-- depth:1
[52]=4,	-- depth:1
[50]=2,	-- depth:1
[26]=2,	-- depth:1
[27]=3,	-- depth:1
[28]=4,	-- depth:1
[29]=5,	-- depth:1
[30]=6,	-- depth:1
[31]=7,	-- depth:1
[32]=8,	-- depth:1
[33]=9,	-- depth:1
[34]=10,	-- depth:1
[35]=11,	-- depth:1
[36]=12,	-- depth:1
[37]=13,	-- depth:1
[38]=14,	-- depth:1
[39]=15,	-- depth:1
[40]=16,	-- depth:1
[280]=16,	-- depth:1
[279]=15,	-- depth:1
[51]=3,	-- depth:1
[74]=2,	-- depth:1
[222]=6,	-- depth:1
[76]=4,	-- depth:1
[107]=11,	-- depth:1
[98]=2,	-- depth:1
[99]=3,	-- depth:1
[100]=4,	-- depth:1
[75]=3,	-- depth:1
[101]=5,	-- depth:1
[112]=16,	-- depth:1
[221]=5,	-- depth:1
[110]=14,	-- depth:1
[109]=13,	-- depth:1
[102]=6,	-- depth:1
[103]=7,	-- depth:1
[104]=8,	-- depth:1
[105]=9,	-- depth:1
[106]=10,	-- depth:1
[266]=2,	-- depth:1
[267]=3,	-- depth:1
[108]=12,	-- depth:1
[269]=5,	-- depth:1
[270]=6,	-- depth:1
[77]=5,	-- depth:1
[88]=16,	-- depth:1
[87]=15,	-- depth:1
[86]=14,	-- depth:1
[85]=13,	-- depth:1
[78]=6,	-- depth:1
[79]=7,	-- depth:1
[80]=8,	-- depth:1
[81]=9,	-- depth:1
[82]=10,	-- depth:1
[84]=12,	-- depth:1
[268]=4,	-- depth:1
[83]=11,	-- depth:1
[19]=17,	-- depth:1
[24]=17,	-- depth:1
[23]=24,	-- depth:2
[22]=24,	-- depth:2
[20]=17,	-- depth:1
[18]=17,	-- depth:1
[21]=24,	-- depth:2
[262]=22,	-- depth:3
[261]=21,	-- depth:3
[260]=20,	-- depth:2
[257]=17,	-- depth:1
[233]=17,	-- depth:1
[285]=21,	-- depth:3
[240]=24,	-- depth:2
[239]=240,	-- depth:3
[259]=257,	-- depth:2
[264]=24,	-- depth:2
[263]=264,	-- depth:3
[238]=240,	-- depth:3
[286]=22,	-- depth:3
[237]=240,	-- depth:3
[235]=233,	-- depth:2
[234]=233,	-- depth:2
[281]=17,	-- depth:1
[282]=281,	-- depth:2
[283]=281,	-- depth:2
[284]=281,	-- depth:2
[236]=233,	-- depth:2
[258]=257,	-- depth:2
[144]=24,	-- depth:2
[215]=23,	-- depth:3
[117]=21,	-- depth:3
[116]=20,	-- depth:2
[115]=19,	-- depth:2
[114]=18,	-- depth:2
[113]=17,	-- depth:1
[96]=24,	-- depth:2
[95]=96,	-- depth:3
[94]=96,	-- depth:3
[93]=96,	-- depth:3
[92]=20,	-- depth:2
[91]=19,	-- depth:2
[90]=18,	-- depth:2
[89]=17,	-- depth:1
[72]=24,	-- depth:2
[71]=72,	-- depth:3
[70]=72,	-- depth:3
[69]=72,	-- depth:3
[68]=20,	-- depth:2
[67]=19,	-- depth:2
[66]=18,	-- depth:2
[65]=17,	-- depth:1
[48]=24,	-- depth:2
[47]=48,	-- depth:3
[46]=48,	-- depth:3
[45]=48,	-- depth:3
[44]=20,	-- depth:2
[43]=19,	-- depth:2
[42]=18,	-- depth:2
[41]=17,	-- depth:1
[118]=22,	-- depth:3
[216]=24,	-- depth:2
[119]=23,	-- depth:3
[137]=17,	-- depth:1
[214]=216,	-- depth:3
[213]=216,	-- depth:3
[212]=20,	-- depth:2
[211]=19,	-- depth:2
[210]=18,	-- depth:2
[209]=17,	-- depth:1
[192]=24,	-- depth:2
[191]=192,	-- depth:3
[190]=192,	-- depth:3
[189]=192,	-- depth:3
[188]=20,	-- depth:2
[187]=19,	-- depth:2
[186]=18,	-- depth:2
[185]=17,	-- depth:1
[168]=24,	-- depth:2
[167]=168,	-- depth:3
[166]=168,	-- depth:3
[165]=168,	-- depth:3
[164]=20,	-- depth:2
[163]=19,	-- depth:2
[162]=18,	-- depth:2
[161]=17,	-- depth:1
[287]=23,	-- depth:3
[143]=144,	-- depth:3
[142]=144,	-- depth:3
[141]=144,	-- depth:3
[140]=137,	-- depth:2
[139]=137,	-- depth:2
[138]=137,	-- depth:2
[120]=24,	-- depth:2
[288]=24,	-- depth:2
},
count_reward={
{},
{seq=1,need_count=50,reward_item={[0]=item_table[288],[1]=item_table[289],[2]=item_table[290],[3]=item_table[291],[4]=item_table[292]},},
{seq=2,need_count=100,reward_item={[0]=item_table[293],[1]=item_table[294],[2]=item_table[295],[3]=item_table[296],[4]=item_table[297]},},
{seq=3,need_count=200,reward_item={[0]=item_table[298],[1]=item_table[299],[2]=item_table[300],[3]=item_table[301],[4]=item_table[302]},},
{seq=4,need_count=300,reward_item={[0]=item_table[303],[1]=item_table[304],[2]=item_table[305],[3]=item_table[306],[4]=item_table[307]},},
{seq=5,need_count=400,reward_item={[0]=item_table[308],[1]=item_table[309],[2]=item_table[310],[3]=item_table[311],[4]=item_table[312]},},
{seq=6,need_count=500,reward_item={[0]=item_table[313],[1]=item_table[314],[2]=item_table[315],[3]=item_table[316],[4]=item_table[317]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

count_reward_meta_table_map={
[19]=5,	-- depth:1
[18]=4,	-- depth:1
[17]=3,	-- depth:1
[16]=2,	-- depth:1
[11]=18,	-- depth:2
[13]=6,	-- depth:1
[12]=19,	-- depth:2
[20]=13,	-- depth:2
[10]=17,	-- depth:2
[9]=16,	-- depth:2
[14]=7,	-- depth:1
[21]=14,	-- depth:2
},
handbook_task={
{reward_item={[0]=item_table[318],[1]=item_table[319],[2]=item_table[320],[3]=item_table[321],[4]=item_table[322]},},
{seq=1,need_suit_num=2,reward_item={[0]=item_table[288],[1]=item_table[289],[2]=item_table[290],[3]=item_table[291],[4]=item_table[292]},},
{seq=2,need_suit_num=3,},
{seq=3,need_suit_num=4,reward_item={[0]=item_table[298],[1]=item_table[299],[2]=item_table[300],[3]=item_table[301],[4]=item_table[302]},},
{seq=4,need_suit_num=5,reward_item={[0]=item_table[303],[1]=item_table[304],[2]=item_table[305],[3]=item_table[306],[4]=item_table[307]},},
{seq=5,need_suit_num=6,},
{seq=6,need_suit_num=7,reward_item={[0]=item_table[313],[1]=item_table[314],[2]=item_table[315],[3]=item_table[316],[4]=item_table[317]},},
{seq=7,need_suit_num=8,},
{seq=8,need_suit_num=9,},
{seq=9,need_suit_num=10,reward_item={[0]=item_table[293],[1]=item_table[294],[2]=item_table[295],[3]=item_table[296],[4]=item_table[297]},},
{seq=10,need_suit_num=11,},
{seq=11,need_suit_num=12,},
{seq=12,need_suit_num=13,},
{seq=13,need_suit_num=14,},
{seq=14,need_suit_num=15,},
{seq=15,need_suit_num=16,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

handbook_task_meta_table_map={
[33]=1,	-- depth:1
[17]=33,	-- depth:2
[47]=15,	-- depth:1
[22]=6,	-- depth:1
[31]=47,	-- depth:2
[45]=13,	-- depth:1
[29]=45,	-- depth:2
[16]=7,	-- depth:1
[14]=7,	-- depth:1
[12]=5,	-- depth:1
[11]=4,	-- depth:1
[9]=2,	-- depth:1
[8]=1,	-- depth:1
[3]=10,	-- depth:1
[38]=22,	-- depth:2
[36]=4,	-- depth:1
[37]=5,	-- depth:1
[44]=12,	-- depth:2
[40]=8,	-- depth:2
[41]=9,	-- depth:2
[42]=10,	-- depth:1
[43]=11,	-- depth:2
[46]=14,	-- depth:2
[39]=7,	-- depth:1
[35]=3,	-- depth:2
[24]=40,	-- depth:3
[32]=16,	-- depth:2
[30]=46,	-- depth:3
[28]=44,	-- depth:3
[27]=43,	-- depth:3
[26]=42,	-- depth:2
[25]=41,	-- depth:3
[23]=39,	-- depth:2
[21]=37,	-- depth:2
[20]=36,	-- depth:2
[19]=35,	-- depth:3
[18]=2,	-- depth:1
[34]=18,	-- depth:2
[48]=32,	-- depth:3
},
convert={
{},
{seq=1,reward_item={[0]=item_table[323]},},
{seq=2,reward_item={[0]=item_table[324]},},
{seq=3,reward_item={[0]=item_table[325]},},
{seq=4,reward_item={[0]=item_table[326]},},
{seq=5,reward_item={[0]=item_table[327]},},
{seq=6,need_score=50,limit=100,reward_item={[0]=item_table[328]},},
{seq=7,reward_item={[0]=item_table[329]},},
{seq=8,need_score=10,limit=10,reward_item={[0]=item_table[330]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

convert_meta_table_map={
[23]=5,	-- depth:1
[22]=4,	-- depth:1
[21]=3,	-- depth:1
[20]=2,	-- depth:1
[13]=22,	-- depth:2
[14]=23,	-- depth:2
[11]=20,	-- depth:2
[12]=21,	-- depth:2
[8]=9,	-- depth:1
[6]=7,	-- depth:1
[26]=8,	-- depth:2
[15]=6,	-- depth:2
[16]=7,	-- depth:1
[17]=26,	-- depth:3
[18]=9,	-- depth:1
[24]=15,	-- depth:3
[25]=16,	-- depth:2
[27]=18,	-- depth:2
},
handbook_suit={
{},
{need_handbook_id="4,5,6,7",},
{need_handbook_id="8,9,10,11",},
{need_handbook_id="12,13,14,15",},
{need_handbook_id="16,17,18,19",},
{need_handbook_id="20,21,22,23",},
{need_handbook_id="24,25,26,27",},
{need_handbook_id="28,29,30,31",},
{need_handbook_id="32,33,34,35",},
{need_handbook_id="36,37,38,39",},
{need_handbook_id="40,41,42,43",},
{need_handbook_id="44,45,46,47",},
{need_handbook_id="48,49,50,51",},
{need_handbook_id="52,53,54,55",},
{need_handbook_id="56,57,58,59",},
{need_handbook_id="60,61,62,63",},
{need_handbook_id="64,65,66,67",},
{need_handbook_id="68,69,70,71",},
{need_handbook_id="72,73,74,75",},
{need_handbook_id="76,77,78,79",},
{need_handbook_id="80,81,82,83",},
{need_handbook_id="84,85,86,87",},
{need_handbook_id="88,89,90,91",},
{need_handbook_id="92,93,94,95",},
{need_handbook_id="96,97,98,99",},
{need_handbook_id="100,101,102,103",},
{need_handbook_id="104,105,106,107",},
{need_handbook_id="108,109,110,111",},
{need_handbook_id="112,113,114,115",},
{need_handbook_id="116,117,118,119",},
{need_handbook_id="120,121,122,123",},
{need_handbook_id="124,125,126,127",},
{need_handbook_id="128,129,130,131",},
{need_handbook_id="132,133,134,135",},
{need_handbook_id="136,137,138,139",},
{need_handbook_id="140,141,142,143",},
{need_handbook_id="144,145,146,147",},
{need_handbook_id="148,149,150,151",},
{need_handbook_id="152,153,154,155",},
{need_handbook_id="156,157,158,159",},
{need_handbook_id="160,161,162,163",},
{need_handbook_id="164,165,166,167",},
{need_handbook_id="168,169,170,171",},
{need_handbook_id="172,173,174,175",},
{need_handbook_id="176,177,178,179",},
{need_handbook_id="180,181,182,183",},
{need_handbook_id="184,185,186,187",},
{grade=1,need_handbook_id="188,189,190,191",},
{need_handbook_id="192,193,194,195",},
{need_handbook_id="196,197,198,199",},
{need_handbook_id="200,201,202,203",},
{need_handbook_id="204,205,206,207",},
{need_handbook_id="208,209,210,211",},
{need_handbook_id="212,213,214,215",},
{need_handbook_id="216,217,218,219",},
{need_handbook_id="220,221,222,223",},
{grade=2,need_handbook_id="224,225,226,227",},
{need_handbook_id="228,229,230,231",},
{need_handbook_id="232,233,234,235",},
{need_handbook_id="236,237,238,239",},
{need_handbook_id="240,241,242,243",},
{need_handbook_id="244,245,246,247",},
{need_handbook_id="248,249,250,251",},
{need_handbook_id="252,253,254,255",},
{need_handbook_id="256,257,258,259",},
{need_handbook_id="260,261,262,263",},
{need_handbook_id="264,265,266,267",},
{need_handbook_id="268,269,270,271",},
{need_handbook_id="272,273,274,275",},
{need_handbook_id="276,277,278,279",},
{need_handbook_id="280,281,282,283",},
{need_handbook_id="284,285,286,287",}
},

handbook_suit_meta_table_map={
[56]=57,	-- depth:1
[55]=56,	-- depth:2
[50]=55,	-- depth:3
[53]=50,	-- depth:4
[52]=53,	-- depth:5
[51]=52,	-- depth:6
[58]=51,	-- depth:7
[54]=58,	-- depth:8
[59]=54,	-- depth:9
[65]=59,	-- depth:10
[61]=65,	-- depth:11
[62]=61,	-- depth:12
[63]=62,	-- depth:13
[64]=63,	-- depth:14
[66]=64,	-- depth:15
[67]=66,	-- depth:16
[68]=67,	-- depth:17
[69]=68,	-- depth:18
[70]=69,	-- depth:19
[49]=70,	-- depth:20
[60]=49,	-- depth:21
[36]=48,	-- depth:1
[46]=36,	-- depth:2
[25]=46,	-- depth:3
[26]=25,	-- depth:4
[27]=26,	-- depth:5
[28]=27,	-- depth:6
[29]=28,	-- depth:7
[30]=29,	-- depth:8
[31]=30,	-- depth:9
[32]=31,	-- depth:10
[33]=32,	-- depth:11
[34]=33,	-- depth:12
[35]=34,	-- depth:13
[71]=60,	-- depth:22
[37]=35,	-- depth:14
[38]=37,	-- depth:15
[39]=38,	-- depth:16
[40]=39,	-- depth:17
[41]=40,	-- depth:18
[42]=41,	-- depth:19
[43]=42,	-- depth:20
[44]=43,	-- depth:21
[45]=44,	-- depth:22
[47]=45,	-- depth:23
[72]=71,	-- depth:23
},
item_random_desc={
{random_count=0.01,},
{number=1,item_id=38734,random_count=0.02,},
{number=2,item_id=26570,random_count=0.03,},
{number=3,item_id=26459,random_count=0.13,},
{number=4,item_id=26460,random_count=0.15,},
{number=5,item_id=26461,random_count=0.31,},
{number=6,item_id=26569,},
{number=7,item_id=48441,random_count=1.03,},
{number=8,item_id=44185,random_count=0.52,},
{number=9,item_id=44184,},
{number=10,item_id=44180,},
{number=11,item_id=26193,random_count=10.31,},
{number=12,item_id=26191,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{grade=1,},
{seq=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{grade=1,},
{grade=1,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{grade=1,},
{seq=3,},
{seq=3,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{seq=1,},
{grade=2,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{grade=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{grade=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{seq=3,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{grade=2,}
},

item_random_desc_meta_table_map={
[27]=1,	-- depth:1
[105]=1,	-- depth:1
[40]=27,	-- depth:2
[53]=105,	-- depth:2
[14]=40,	-- depth:3
[36]=10,	-- depth:1
[37]=11,	-- depth:1
[131]=105,	-- depth:2
[92]=53,	-- depth:3
[49]=36,	-- depth:2
[111]=7,	-- depth:1
[50]=37,	-- depth:2
[144]=92,	-- depth:4
[79]=92,	-- depth:4
[59]=111,	-- depth:2
[62]=10,	-- depth:1
[63]=11,	-- depth:1
[46]=7,	-- depth:1
[24]=50,	-- depth:3
[33]=46,	-- depth:2
[66]=79,	-- depth:5
[23]=49,	-- depth:3
[118]=66,	-- depth:6
[13]=12,	-- depth:1
[115]=63,	-- depth:2
[114]=62,	-- depth:2
[20]=33,	-- depth:3
[76]=24,	-- depth:4
[75]=23,	-- depth:4
[72]=20,	-- depth:4
[150]=46,	-- depth:2
[141]=115,	-- depth:3
[153]=114,	-- depth:3
[85]=72,	-- depth:5
[140]=153,	-- depth:4
[89]=141,	-- depth:4
[137]=85,	-- depth:6
[113]=9,	-- depth:1
[110]=6,	-- depth:1
[109]=5,	-- depth:1
[108]=4,	-- depth:1
[107]=3,	-- depth:1
[106]=2,	-- depth:1
[88]=140,	-- depth:5
[154]=141,	-- depth:4
[116]=12,	-- depth:1
[102]=154,	-- depth:5
[101]=88,	-- depth:6
[117]=13,	-- depth:2
[98]=85,	-- depth:6
[127]=140,	-- depth:5
[128]=154,	-- depth:5
[112]=8,	-- depth:1
[61]=113,	-- depth:2
[64]=116,	-- depth:2
[65]=117,	-- depth:3
[42]=3,	-- depth:1
[41]=2,	-- depth:1
[39]=13,	-- depth:2
[38]=12,	-- depth:1
[35]=9,	-- depth:1
[34]=8,	-- depth:1
[32]=6,	-- depth:1
[31]=5,	-- depth:1
[30]=4,	-- depth:1
[29]=42,	-- depth:2
[28]=41,	-- depth:2
[26]=39,	-- depth:3
[25]=38,	-- depth:2
[22]=35,	-- depth:2
[21]=34,	-- depth:2
[19]=32,	-- depth:2
[18]=31,	-- depth:2
[17]=30,	-- depth:2
[16]=29,	-- depth:3
[15]=28,	-- depth:3
[43]=17,	-- depth:3
[44]=18,	-- depth:3
[124]=137,	-- depth:7
[45]=19,	-- depth:3
[56]=108,	-- depth:2
[54]=106,	-- depth:2
[57]=109,	-- depth:2
[52]=26,	-- depth:4
[51]=25,	-- depth:3
[55]=107,	-- depth:2
[58]=110,	-- depth:2
[60]=112,	-- depth:2
[47]=21,	-- depth:3
[48]=22,	-- depth:3
[151]=47,	-- depth:4
[152]=48,	-- depth:4
[149]=45,	-- depth:4
[148]=44,	-- depth:4
[125]=151,	-- depth:5
[126]=152,	-- depth:5
[147]=43,	-- depth:4
[145]=41,	-- depth:2
[138]=125,	-- depth:6
[143]=39,	-- depth:3
[142]=38,	-- depth:2
[129]=142,	-- depth:3
[130]=143,	-- depth:4
[139]=126,	-- depth:6
[132]=145,	-- depth:3
[133]=29,	-- depth:3
[134]=147,	-- depth:5
[136]=149,	-- depth:5
[146]=133,	-- depth:4
[135]=148,	-- depth:5
[78]=130,	-- depth:5
[122]=135,	-- depth:6
[67]=54,	-- depth:3
[68]=55,	-- depth:3
[69]=56,	-- depth:3
[70]=122,	-- depth:7
[71]=58,	-- depth:3
[73]=125,	-- depth:6
[74]=126,	-- depth:6
[77]=129,	-- depth:4
[155]=129,	-- depth:4
[80]=67,	-- depth:4
[81]=68,	-- depth:4
[82]=69,	-- depth:4
[83]=70,	-- depth:8
[84]=71,	-- depth:4
[123]=71,	-- depth:4
[86]=73,	-- depth:7
[90]=77,	-- depth:5
[91]=78,	-- depth:6
[93]=80,	-- depth:5
[94]=81,	-- depth:5
[95]=82,	-- depth:5
[96]=83,	-- depth:9
[97]=84,	-- depth:5
[99]=86,	-- depth:8
[100]=74,	-- depth:7
[103]=90,	-- depth:6
[104]=91,	-- depth:7
[119]=67,	-- depth:4
[120]=68,	-- depth:4
[121]=69,	-- depth:4
[87]=100,	-- depth:8
[156]=104,	-- depth:8
},
other_default_table={get_convert_score=1,score_item=65543,},

grade_default_table={grade=0,start_server_day=1,end_server_day=20,grade_name="十二生肖",model_show_type=1,model_bundle_name="model/zuoqi/2016_prefab",model_asset_name=2016,model_show_itemid=23350,special_show_name="",display_pos="-260|-15|0",display_scale=1,rotation="0|-10|0",show_item="37867|37683|37970|37143",},

active_default_table={grade=0,seq=0,reward_lib=1,consume_item_id=50096,consume_price=100,guarantee_remain_num=10,seq_name="十二生肖·甲",},

reward_default_table={reward_lib=1,reward_id=1,reward_item={[0]=item_table[331]},reward_type=2,player_guarantee=0,reward_limit=0,broadcast=0,},

count_reward_default_table={grade=0,seq=0,need_count=10,reward_item={[0]=item_table[318],[1]=item_table[319],[2]=item_table[320],[3]=item_table[321],[4]=item_table[322]},},

handbook_task_default_table={grade=0,seq=0,need_suit_num=1,reward_item={[0]=item_table[308],[1]=item_table[309],[2]=item_table[310],[3]=item_table[311],[4]=item_table[312]},},

convert_default_table={grade=0,seq=0,need_score=100,limit=9999,reward_item={[0]=item_table[332]},},

handbook_suit_default_table={grade=0,need_handbook_id="0,1,2,3",},

item_random_desc_default_table={grade=0,seq=0,number=0,item_id=38053,random_count=5.15,}

}

