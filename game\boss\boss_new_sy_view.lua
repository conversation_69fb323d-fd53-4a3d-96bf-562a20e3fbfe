WorldServerView.SYSERVERNUM = 5

WorldServerView.SYOPERATE = {
    GET_SCENE_INFO = 0, --请求场景信息    parma1场景id, param2场景索引（入侵服场景用）
    CONCERN = 1,        --关注    param1:场景id; param2:boss_id; param3 场景索引（入侵服场景用）
    UNCONCERN = 2,      --取消关注  param1:场景id; param2:boss_id; param3 场景索引（入侵服场景用）
    TOTAL_SCENE = 3,      --全部场景信息
}

WorldServerView.SCENETYPE = {
    ORI = 1,        -- 本服
    INVADE = 2,     -- 入侵服
    COMMON = 3      -- 公共服
}

WorldServerView.FLUSHPARAM = {
    SELF = 1,       -- 8845
    TOTAL = 2,      -- 8835
    COMMON = 3,     -- 8846的本服
}

function WorldServerView:DeleteNewSYBossView()
    self.sy_select_layer = nil
end

function WorldServerView:InitNewSYBossView()
    self.node_list["btn_sy_goto"]:SetActive(false)
    self.node_list["btn_sy_goto"].button:AddClickListener(BindTool.Bind(self.OnClickSYGoto, self))
    self.node_list["btn_hmsy_add"].button:AddClickListener(
            BindTool.Bind(self.OnClickKfBossAddTimes, self, SceneType.HONG_MENG_SHEN_YU))

    for i = 1, WorldServerView.SYSERVERNUM do
        self.node_list["btn_server_" .. i].button:AddClickListener(BindTool.Bind(self.OpenSYServerTip, self, i))
    end
    self.sy_select_server = 1
end

function WorldServerView:OnFlushNewSYBossView(param_t)
    for i, v in pairs(param_t) do
        if v[1] == WorldServerView.FLUSHPARAM.SELF then
            self:OnFlushConcern()
        elseif v[1] == WorldServerView.FLUSHPARAM.TOTAL then
            self:FlushTotalInfo()
        elseif v[1] == WorldServerView.FLUSHPARAM.COMMON and self.sy_select_layer == WorldServerView.SCENETYPE.ORI then
            self:OnFlushBossList()
        end
    end
    self:FlushSYTimes()
end

function WorldServerView:OnClickSyBtnLayer(index)
    local old_index_sy = self.sy_select_layer
    self.sy_select_layer = index
    local can_enter, need_level, need_day = BossWGData.Instance:GetHMSYLayerIsEnter(self.sy_select_layer)

    if not can_enter and need_level == nil and need_day == nil then
        return
    end

    if can_enter or self.sy_select_layer ~= 1 then
        if not can_enter then
            if need_level then
                need_level = RoleWGData.GetLevelString(need_level)
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel, need_level))
            elseif need_day then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.OpenDay, need_day))
            end

            if old_index_sy ~= self.sy_select_layer then
                local flag2 = BossWGData.Instance:GetHMSYLayerIsEnter(old_index_sy)
                if not flag2 then
                    old_index_sy = BossWGData.Instance:GetHMSYDefaultLayer()
                end
                self.layer_btn_list:SelectIndex(old_index_sy)
            end
            return
        end
    end
    self:OnFlushNewSyLayer()
end

function WorldServerView:OnFlushNewSyLayer()
    self:FlushLayerChange()
    self:SendInfoRequire()
    if self.sy_select_layer == WorldServerView.SCENETYPE.ORI then
        self:OnFlushBossList()
        self:SetSYModel(true)
    else
        self.select_item_data = nil
        self:SetSYModel(false)
        self:ChangeHighLight()
    end
    self:SendSYCommonInfoQuery()
    self:FlushTotalInfo()
end

function WorldServerView:FlushLayerChange()
    if self.sy_select_layer == 1 then
        self.node_list["sy_layer1"]:SetActive(true)
        self.node_list["sy_layer2"]:SetActive(false)
    else
        self.node_list["sy_raw_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("sy_boss_bg_" .. self.sy_select_layer-1))
        self.node_list["sy_layer1"]:SetActive(false)
        self.node_list["sy_layer2"]:SetActive(true)
        local index = self.sy_select_layer - 1
        for i = 1, WorldServerView.SYSERVERNUM do
            local str = string.format("sy_boss_select_%d_%d", index, i)
            self.node_list["btn_server_" .. i].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(str))
        end
    end
end

function WorldServerView:SendInfoRequire()
    BossWGCtrl.Instance:SendHMSYOpera(WorldServerView.SYOPERATE.TOTAL_SCENE)
end

function WorldServerView:OnClickSYGoto()
    if self.sy_select_layer == WorldServerView.SCENETYPE.ORI then
        if not self.sy_select_boss_data then
            return
        end

        local scene_id = self.sy_select_boss_data and self.sy_select_boss_data.scene_id

        if not scene_id then
            return
        end

        if self:CheckBossSceneGoto(scene_id) then
            return
        end

        BossWGCtrl.Instance:SendHMSYEnterReq(scene_id, self.sy_select_boss_data.scene_index)
        BossWGData.Instance:GetSetLastSelectInfo(self.sy_select_layer, self.list_index)
    else
        if self.no_open then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.HMSY.NoOpen)
            return
        end

        local scene_info = self.scene_info and self.scene_info[self.sy_select_server]

        if scene_info then
            if self:CheckBossSceneGoto(scene_info.scene_id) then
                return
            end

            BossWGCtrl.Instance:SendHMSYEnterReq(scene_info.scene_id, scene_info.scene_index)
        else
            local scene_list = BossWGData.Instance:GetHMSYSceneIdIndexByLayer(self.sy_select_layer)
            BossWGCtrl.Instance:SendHMSYEnterReq(scene_list[1].scene_id, scene_list[1].scene_index)
        end
        BossWGData.Instance:GetSetLastSelectInfo(self.sy_select_layer, self.list_index)
    end
end

function WorldServerView:BrowseHMSYPlayInfo()
	local des, title = Language.HMSY.PlayDes, Language.HMSY.PlayTitle
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(title)
	role_tip:SetContent(des)
end

function WorldServerView:OpenSYServerTip(i)
    local old_sy_select_server = self.sy_select_server
    self.sy_select_server = i
    if not self.scene_info or not self.scene_info[i] then
        self:AddDelayFunc(BindTool.Bind(self.OpenSYServerTip, self, i), "open_tip")
        return
    end
    self:ChangeHighLight(old_sy_select_server)
    if self.scene_info[i].server_id == 0 and i ~= 1 then
        self.no_open = true
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HMSY.NoOpen)
        return
    else
        self.no_open = false
    end
    BossWGData.Instance:SetHMSYSelectScene(self.scene_info[i].scene_id, self.scene_info[i].scene_index, self.sy_select_layer)
    BossWGCtrl.Instance:SendHMSYOpera(WorldServerView.SYOPERATE.GET_SCENE_INFO, self.scene_info[i].scene_id, self.scene_info[i].scene_index)
    ViewManager.Instance:Open(GuideModuleName.BossHMSYTip)
end

function WorldServerView:ChangeHighLight(old_high)
    if not self.sy_select_layer then
        return
    end

    local str
    if old_high then
        str = string.format("sy_boss_select_%d_%d", self.sy_select_layer - 1, old_high)
        self.node_list["btn_server_" .. old_high].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(str))
    end

    str = string.format("sy_boss_select_%d_%d_h", self.sy_select_layer - 1, self.sy_select_server)
    self.node_list["btn_server_" .. self.sy_select_server].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(str))
end

function WorldServerView:OnClickSYBossCell(cell)
    if not self.sy_select_layer == WorldServerView.SCENETYPE.ORI then
        return
    end
    self.sy_select_boss_data = cell.data
    self.node_list["Txt_goto_kill"].text.text = self.sy_select_boss_data.type == BossWGData.MonsterType.Gather and Language.Common.GotoCaiJi or Language.Common.GotoKillBoss
    BossWGData.Instance:SetCurSelectBossID(self:GetShowIndex(), self.sy_select_layer, self.select_item_data.boss_id)
end

function WorldServerView:FlushTotalInfo()
    if self.sy_select_layer and self.sy_select_layer > 1 then
        local scene_info = BossWGData.Instance:GetHMSYSceneInfoByLayer(self.sy_select_layer)
        self.scene_info = scene_info
        if self:HasDelayFunc("open_tip") then
            self:DoDelayFunc("open_tip")
        end
        local role_info = RoleWGData.Instance:GetRoleVo()
        for i = 1, WorldServerView.SYSERVERNUM do
            local str = i == 1 and Language.HMSY.LayerName1 or Language.HMSY.LayerName2
            self.node_list["txt_server_" .. i].text.text = string.format(str, NumberToChinaNumber(self.sy_select_layer-1))
            if scene_info[i] then
                local color = scene_info[i].monster_count > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
                if i == 1 then
                    str = string.format(Language.HMSY.KFNum, color, scene_info[i].monster_count)
                else
                    if scene_info[i].server_id == 0 then
                        str = Language.HMSY.NoOpen
                    elseif role_info.plat_type == scene_info[i].plat_type and role_info.origin_server_id == scene_info[i].server_id then
                        str = string.format(Language.HMSY.SelfNum, color,  scene_info[i].monster_count)
                    else
                        str = string.format(Language.HMSY.InvadeNum, NumberToChinaNumber(i-1), color, scene_info[i].monster_count)
                    end
                end

                self.node_list["txt_name_" .. i].text.text = str
            else
                self.node_list["txt_name_" .. i].text.text = Language.HMSY.NoOpen
            end
        end
    --else
    end
end

function WorldServerView:FlushSYTimes()
    local cfg = BossWGData.Instance:GetHMSYOtherCfg()
    local times, add_times = BossWGData.Instance:GetHMSYTimes()
    local is_ori = self.sy_select_layer == WorldServerView.SCENETYPE.ORI
    local str = string.format(Language.HMSY.Times2, cfg.reward_times - times, cfg.reward_times + add_times)

    local color
    if times >= cfg.reward_times then
        color = is_ori and COLOR3B.D_RED or COLOR3B.D_RED
    else
        color = is_ori and COLOR3B.D_GREEN or COLOR3B.D_GREEN
    end
    str = Language.HMSY.Times1 .. ToColorStr(str , color)

    if self.sy_select_layer == WorldServerView.SCENETYPE.ORI then
        self.node_list["txt_sy_times"].text.text = str
    else
        self.node_list["sy_boss_times"].text.text = str
    end
end

function WorldServerView:SendSYCommonInfoQuery()
    local common_scene_id = BossWGData.Instance:GetHMSYSceneIdIndexByLayer(self.sy_select_layer)
    for i, v in pairs(common_scene_id) do
        BossWGCtrl.Instance:SendHMSYOpera(WorldServerView.SYOPERATE.GET_SCENE_INFO, v.scene_id)
    end
end

function WorldServerView:OnClickSYCheck()
    local oper_type = WorldServerView.SYOPERATE.CONCERN
    if BossWGData.Instance:GetHMSYORIBossFlag(self.sy_select_boss_data.boss_index) then
        oper_type = WorldServerView.SYOPERATE.UNCONCERN
    end
    BossWGCtrl.Instance:SendHMSYOpera(oper_type, self.sy_select_boss_data.scene_id, self.sy_select_boss_data.boss_id)
end

function WorldServerView:SetSYModel(status)
    if self.boss_display_model then
        self.boss_display_model:SetVisible(status)
    end
end

function WorldServerView:AddDelayFunc(func, key)
    if not self.func_list then
        self.func_list = {}
    end
    self.func_list[key] = func
end

function WorldServerView:HasDelayFunc(key)
    return self.func_list and self.func_list[key] ~= nil
end

function WorldServerView:DoDelayFunc(key)
    self.func_list[key]()
    self.func_list[key] = nil
end
