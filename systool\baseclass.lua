local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()
local is_ope_profile = false

--保存类类型的虚表
local _class = {}
local lua_obj_count = 0
local class_file_path_table = {}

function BaseClass(super)
	-- 生成一个类类型
	local class_type = {}
	-- 在创建对象的时候自动调用
	class_type.__init = false
	class_type.__delete = false
	class_type.super = super
	class_type.New = function(...)
		if IS_CHECK_OBSOLETE then
			CheckObsolete:StartCallNew(class_type, _class[class_type])
		end

		lua_obj_count = lua_obj_count + 1
		-- 生成一个类对象
		local obj = {_class_type = class_type}

		if IS_CHECK_CLASS_OBJ_COUNT then
			local traceback = debug.traceback()
			obj._init_traceback = traceback
			CheckClassCount:NewClassObj(obj)
		end

		-- 在初始化之前注册基类方法
		setmetatable(obj, { __index = _class[class_type] })

		-- 调用初始化方法
		do
			local create
			create = function(c, ...)
				if c.super then
					create(c.super, ...)
				end
				if c.__init then
					c.__init(obj, ...)
				end
			end

			create(class_type, ...)
		end

		-- 注册一个delete方法
		obj.DeleteMe = function(self)
			if is_develop or GLOBAL_DELETE_ME_CHECK_SWITCH then
				if obj.__is_deleted__ then
					print_error("重复调用DeleteMe", debug.traceback())
				end
				obj.__is_deleted__ = true
			end

			if IS_CHECK_CLASS_OBJ_COUNT then
				CheckClassCount:DeleteClassObj(obj)
			end

			lua_obj_count = lua_obj_count - 1
			local now_super = self._class_type
			while now_super ~= nil do
				if now_super.__delete then
					now_super.__delete(self)
				end
				now_super = now_super.super
			end

			if obj.__gameobj_loaders then
				ReleaseGameobjLoaders(obj)
			end

			if obj.__res_loaders then
				ReleaseResLoaders(obj)
			end

			if obj.__delay_call_map then
				CancleAllDelayCall(obj)
			end

			if is_develop or GLOBAL_DELETE_ME_CHECK_SWITCH then
				develop_mode:OnDeleteObj(self)
			end
		end

		if is_develop or GLOBAL_DELETE_ME_CHECK_SWITCH then
			develop_mode:OnCreateObj(obj, class_type)
		end

		if IS_CHECK_OBSOLETE then
			CheckObsolete:EndCallNew(class_type, _class[class_type])
		end

		return obj
	end

	local vtbl = {}
	_class[class_type] = vtbl

	local meta = {}
	if IS_CHECK_FUNTION_COST_TIME or is_develop or UNITY_EDITOR or is_ope_profile or IS_CHECK_FUNTION_COST_MEM or IS_CHECK_OBSOLETE then
		meta.__newindex = function(t,k,v)
			if type(v) == "function" then
				if IS_CHECK_OBSOLETE and nil == class_file_path_table[class_type] then
					local info = debug.getinfo(v, "Sln")
					class_file_path_table[class_type] = info.short_src
				end

				if IS_CHECK_FUNTION_COST_TIME or is_ope_profile or IS_CHECK_OBSOLETE then
					vtbl[k] = function (...)
						if IS_CHECK_OBSOLETE and vtbl.__is_obsolete then
							CheckObsolete:TryCallObsoleteFunction(class_type, k, class_file_path_table[class_type])
						end

						local clas_name = GetClassName(class_type)
						if nil ~= clas_name then
							AddProfileBeginSample(clas_name .. "." .. k)
						end

						local t = {v(...)}

						if nil ~= clas_name then
							AddProfileEndSample()
						end
						return unpack(t)
					end
				elseif ClassPrintSwitch and TestViewLog then
					vtbl[k] = function (...)
						local flag = false
						if t.is_test_check_obj then
							LogFunc.SetInfo(k)
							flag = true
						end
						local t = {v(...)}
						if flag then
							LogFunc.SetInfo(k)
						end
						return unpack(t)
					end
				elseif IS_CHECK_FUNTION_COST_MEM then
					vtbl[k] = function (...)
						local class_name = GetClassName(class_type)

						if class_name then
							CheckFuntionUseMem:StartCall(class_name, k)
						end

						local a,b,c,d = v(...)

						if class_name then
							CheckFuntionUseMem:EndCall(class_name, k)
						end

						if nil ~= d then
							return a,b,c,d
						elseif nil ~= c then
							return a,b,c
						elseif nil ~= b then
							return a,b
						else
							return a
						end

					end
				else
					vtbl[k] = v
				end

			else
				vtbl[k] = v
			end
		end
	else
		meta.__newindex = function(t,k,v)
			vtbl[k] = v
		end
	end

	if IS_CHECK_OBSOLETE then
		meta.__index = function (t,k)
			if vtbl.__is_obsolete then
				CheckObsolete:TryUseObsoleteIndex(class_type, k, class_file_path_table[class_type])
			end
			return vtbl[k]
		end
	else
		meta.__index = vtbl
	end

	setmetatable(class_type, meta)

	if super then
		setmetatable(vtbl, {__index = function(t,k) return _class[super][k] end})
	end

	if is_develop or GLOBAL_DELETE_ME_CHECK_SWITCH then
		GlobalClassType = GlobalClassType or {}
		GlobalClassType[class_type] = vtbl
		develop_mode:OnCreateClass(class_type)
	end

	return class_type
end

function GetDebugLuaObjCount(t)
	t.lua_obj_count = lua_obj_count
end

local class_name_table = {}
function GetClassName(class_type)
	local name = class_name_table[class_type]
	if name == nil then
		for k,v in pairs(_G) do
			if v == class_type then
				class_name_table[class_type] = k
				name = k
			end
		end
	end
	return name
end

function AddProfileBeginSample(flag)
	if is_ope_profile and (UNITY_EDITOR or IS_LOCLA_WINDOWS_DEBUG_EXE) and nil ~= StringPool then
		ToLuaProfile.AddProfileBeginSample(StringPool.Get("lua:" .. flag))
	end
end

function AddProfileEndSample()
	if is_ope_profile and (UNITY_EDITOR or IS_LOCLA_WINDOWS_DEBUG_EXE) then
		ToLuaProfile.AddProfileEndSample()
	end
end