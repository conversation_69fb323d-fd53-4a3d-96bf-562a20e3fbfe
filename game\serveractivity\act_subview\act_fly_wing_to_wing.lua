function ServerActivityTabView:KFBiYiShuangLoadCallBack()
    self:InitLeftPanel()
    self.node_list.layer_marry:SetActive(true)
    self.node_list.layer_task:SetActive(false)
    -- self:InitRightPanel()
    ServerActivityWGData.Instance:SetLoginLoversRemind(false)
end

function ServerActivityTabView:KFBiYiShuangLoadCallBack2()
    -- self:InitLeftPanel()
    self:InitRightPanel()
    self.node_list.layer_marry:SetActive(false)
    self.node_list.layer_task:SetActive(true)
    ServerActivityWGData.Instance:SetLoginLoversRemind(false)
end

function ServerActivityTabView:KFBiYiShuangShowIndexCallBack()
    self.node_list.layer_marry:SetActive(true)
    self.node_list.layer_task:SetActive(false)
end

function ServerActivityTabView:KFBiYiShuangShowIndexCallBack2()
    self.node_list.layer_marry:SetActive(false)
    self.node_list.layer_task:SetActive(true)
end

function ServerActivityTabView:KFBiYiShuangFeiReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("QingYuanMission") then
        CountDownManager.Instance:RemoveCountDown("QingYuanMission")
	end

    if self.kf_act_bysf_lover_model then
		self.kf_act_bysf_lover_model:DeleteMe()
		self.kf_act_bysf_lover_model = nil
	end

    if self.kf_act_bysf_citylove_model then
		self.kf_act_bysf_citylove_model:DeleteMe()
		self.kf_act_bysf_citylove_model = nil
    end

    -- 任务
    if self.qy_task_list then
        self.qy_task_list:DeleteMe()
        self.qy_task_list = nil
    end

    -- 任务进度奖励
    if self.qy_task_reward_list then
        for i = 1, 5 do
            self.qy_task_reward_list[i]:DeleteMe()
            self.qy_task_reward_list[i] = nil
        end
        self.qy_task_reward_list = nil
    end

    self.ache_city_love_res = nil
end

function ServerActivityTabView:KFBiYiShuangFeiOnFlush(param_t)
    self:RefreshLeftPanel()
	self:FlushCountDownTime()
end

function ServerActivityTabView:KFBiYiShuangFeiOnFlush2(param_t)
    self:RefreshRightPanel()
	self:FlushCountDownTime()
end

-- 结婚
function ServerActivityTabView:InitLeftPanel()
	local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	local perfect_lover_cfg = opengame_cfg.perfect_lover[1]

	local title_bundle, title_asset = ResPath.GetRoleTitle(perfect_lover_cfg.title_id)
	self.node_list["kf_act_bysf_title_root"].image:LoadSprite(title_bundle, title_asset, function()
		self.node_list["kf_act_bysf_title_root"].image:SetNativeSize()
	end)

    local cap_value = ItemShowWGData.Instance.CalculateCapability(perfect_lover_cfg.reward_item[1].item_id, nil)
	self.node_list.kf_act_bysf_lover_lbl_zhanli.text.text = cap_value

	self.kf_act_bysf_lover_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["kf_act_bysf_lover_model_pos"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }
    
    self.kf_act_bysf_lover_model:SetRenderTexUI3DModel(display_data)
    -- self.kf_act_bysf_lover_model:SetUI3DModel(self.node_list["kf_act_bysf_lover_model_pos"].transform, self.node_list["kf_act_bysf_lover_model_pos"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.kf_act_bysf_lover_model)

	if self.kf_act_bysf_lover_model then
		local bundle, asset = ResPath.GetHaiZiModel(perfect_lover_cfg.resource_id)
		if bundle == nil and asset == nil then
			return
		end

		self.kf_act_bysf_lover_model:SetMainAsset(bundle, asset)
	end

    XUI.AddClickEventListener(self.node_list.btn_to_marry, BindTool.Bind(self.OnClickBtnToMarry, self))
    XUI.AddClickEventListener(self.node_list.wing_btn_show_item, BindTool.Bind(self.ShowLeftModelItem, self))

end

function ServerActivityTabView:OnClickBtnToMarry()
    ViewManager.Instance:Open(GuideModuleName.Marry)
end

function ServerActivityTabView:FlushCountDownTime()
    if CountDownManager.Instance:HasCountDown("QingYuanMission") then
        CountDownManager.Instance:RemoveCountDown("QingYuanMission")
	end

    local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY)
	if count_down_time > 0 then
		self.node_list.kf_act_bysf_timer_txt.text.text = TimeUtil.FormatSecondDHM8(count_down_time)
		CountDownManager.Instance:AddCountDown(
			"QingYuanMission",
			BindTool.Bind(self.CountDownTimeCallBack, self),
			BindTool.Bind(self.FlushCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.kf_act_bysf_timer_txt.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.kf_act_bysf_timer_txt.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:CountDownTimeCallBack(elapse_time, total_time)
    if self.node_list.kf_act_bysf_timer_txt then
        self.node_list.kf_act_bysf_timer_txt.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
    end
end

function ServerActivityTabView:RefreshLeftPanel()
	local marry_cfg = MarryWGData.Instance:GetMarryCfg() or {}
    local opengame_info = ServerActivityWGData.Instance:GetOpenServerData()
	local marry_flag = opengame_info.oga_marry_type_record_flag or 0 --当前已结婚次数
	local total_times = #marry_cfg --需要结婚的次数
	local is_finish = marry_flag >= total_times

    for i = 1, 3 do
        self.node_list["kf_act_bysf_marry_count_" .. i].image.enabled = marry_flag >= i

        local asset_res = marry_flag >= i and  "a3_kfkh_xljq_h_1" or "a3_kfkh_xljq_h_2"
        local bundle,asset =ResPath.GetKaiFuChongBangUi(asset_res)
        
        self.node_list["img_flower_"..i].image:LoadSprite(bundle, asset, function()
            self.node_list["img_flower_"..i].image:SetNativeSize()
        end)
    end
    -- self.node_list.kf_act_bysf_lover_slider_text.text.text = string.format(Language.Activity.ActivityProcess, marry_flag, 3)
    -- self.node_list.kf_act_bysf_marry_count_bg:SetActive(not is_finish)
    self.node_list.kf_act_bysf_lover_get_flag:SetActive(is_finish)
    self.node_list.btn_to_marry:SetActive(not is_finish)
end

-- 任务
function ServerActivityTabView:InitRightPanel()
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY, PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_INFO)
    XUI.AddClickEventListener(self.node_list.kf_act_bysf_btn_get_baby, BindTool.Bind(self.OnClickGetQYTaskBabyBtn, self))
    XUI.AddClickEventListener(self.node_list.wing_btn_show_item2, BindTool.Bind(self.ShowRightModelItem, self))

    
    -- 任务
    self.qy_task_list = AsyncListView.New(QingYuanMissionRender, self.node_list["marry_task_list"])

    -- 任务进度奖励
    self.qy_task_reward_list = {}
    for i = 1, 5 do
        local render = QingYuanMissionRewardItem.New(self.node_list["reward_item_" .. i])
        render:SetIndex(i)
        render:SetClickCallBack(BindTool.Bind(self.OpenCityInfoScoreRewardView, self))
        self.qy_task_reward_list[i] = render
    end
end

function ServerActivityTabView:ShowRightModelItem()
    local cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    
    TipWGCtrl.Instance:OpenItem({ item_id = cfg.loving_city_image_item.item_id })
end
function ServerActivityTabView:ShowLeftModelItem()
    local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	local perfect_lover_cfg = opengame_cfg.perfect_lover[1]
    TipWGCtrl.Instance:OpenItem({ item_id = perfect_lover_cfg.reward_item[1].item_id })
end


function ServerActivityTabView:RefreshRightPanel()
    --信息
    local city_info_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    local server_info = ActivePerfertQingrenWGData.Instance:GetServerLoveCityInfo()
    local data1 = {}
    data1.item_id = city_info_cfg.loving_city_image_item.item_id
    local cap_value = ItemShowWGData.Instance.CalculateCapability(data1.item_id, nil)
    self.node_list.kf_act_bysf_city_love_cap_value.text.text = cap_value
    local total_score = server_info.total_process
    local need_score = city_info_cfg.loving_city_convert_image_progress_value
    self.node_list.kf_act_bysf_city_love_progress.text.text = string.format(Language.OpenServer.QingyuanProgress, total_score, need_score)
	local num_flag = bit:d2b(server_info.process_reward_flag)
	self.node_list.kf_act_bysf_city_get_remind:SetActive(total_score >= need_score and num_flag[32] == 0)
    self.node_list.kf_act_bysf_btn_get_baby:SetActive(num_flag[32] == 0)
    self.node_list.kf_act_bysf_city_baby_get_flag:SetActive(num_flag[32] == 1)
    XUI.SetGraphicGrey(self.node_list.kf_act_bysf_btn_get_baby, total_score < need_score and num_flag[32] == 0)

    --模型
    if not self.kf_act_bysf_citylove_model then
        self.kf_act_bysf_citylove_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["kf_act_bysf_citylove_model_pos"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.kf_act_bysf_citylove_model:SetRenderTexUI3DModel(display_data)
        -- self.kf_act_bysf_citylove_model:SetUI3DModel(self.node_list["kf_act_bysf_citylove_model_pos"].transform, self.node_list["kf_act_bysf_citylove_model_pos"].event_trigger_listener,
        --  1, false, MODEL_CAMERA_TYPE.BASE)
    end
    local cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    if cfg.loving_city_resource_id and self.ache_city_love_res ~= cfg.loving_city_resource_id then
        local res_id = cfg.loving_city_resource_id
        self.ache_city_love_res = res_id
        local bundle, asset = ResPath.GetHaiZiModel(res_id)
        self.kf_act_bysf_citylove_model:SetMainAsset(bundle, asset,function()
            self.kf_act_bysf_citylove_model:PlaySoulAction()
        end)
    end

    -- TODO 原本获取积分按钮的红点
    -- self.node_list.btn_get_score_remind:SetActive(ActivePerfertQingrenWGData.Instance:GetCityLoveRemind() > 0)

    self:FlushTask()
end

function ServerActivityTabView:OnClickGetScoreBtn()
    local act_open = ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY)
	if not act_open then
		TipWGCtrl.Instance:ShowSystemMsg(Language.OdysseyPurchaseDesc.NoOpenBuyActTips)
		return
	end

    ServerActivityWGCtrl.Instance:OpenQingYuanMissionView()
end

function ServerActivityTabView:OnClickGetQYTaskBabyBtn()
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
	    PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_IMAGE)
end

function ServerActivityTabView:FlushTask()
    local qy_task_list_info = ActivePerfertQingrenWGData.Instance:GetLoveCityListInfo()
    self.qy_task_list:SetDataList(qy_task_list_info)

    local _, loving_city_process_reward = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianTaskCfg()
    local server_info = ActivePerfertQingrenWGData.Instance:GetServerLoveCityInfo()
    local need_process = ActivePerfertQingrenWGData.Instance:GetCurLoverProgress(loving_city_process_reward, server_info.total_process)
	self.node_list.qingyuan_mission_slider.slider.value = need_process

    local num_flag = bit:d2b(server_info.process_reward_flag)
    local index = 0
    for i = 1, 5 do
        local temp_data = {}
        temp_data.need_process = loving_city_process_reward[i].need_process
        temp_data.is_not_get = num_flag[32 - i] == 0
        temp_data.total_process = server_info.total_process
        self.qy_task_reward_list[i]:SetData(temp_data)

        if index == 0 and temp_data.is_not_get then
            index = i
        end
    end

    index = index == 1 and 0 or index
    self.node_list.wing_reward_scroll.scroll_rect.horizontalNormalizedPosition = index / 5
    self.node_list.marry_score_text.text.text = server_info.total_process
end

function ServerActivityTabView:HighTaskPointShowIndexCallBack()
    self:PlayHighTaskPointAnim()
end

function ServerActivityTabView:PlayHighTaskPointAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab
    UITween.FakeHideShow(self.node_list["marry_task_list"])
    UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["marry_task_list"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end
--------------------------------------------------------

QingYuanMissionRender = QingYuanMissionRender or BaseClass(BaseRender)

function QingYuanMissionRender:LoadCallBack()
    self.cell_list = {}
	for i = 1, 6 do
		self.cell_list[i] = ItemCell.New(self.node_list["item_pos"])

        
	end
	XUI.AddClickEventListener(self.node_list["btn_task_lingqu"], BindTool.Bind(self.GetRewardOrGoAct, self))
end

function QingYuanMissionRender:__delete()
    if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function QingYuanMissionRender:OnFlush()
    if nil == self.data then
		return
    end
	self.node_list.comple_flag:SetActive(self.data.sort_index == 2)
    self.node_list.remind:SetActive(self.data.sort_index == 0)
	self.node_list["btn_task_lingqu"]:SetActive(self.data.sort_index ~= 2)
	local num_1 = self.data.process >= self.data.task_process and self.data.task_process or self.data.process
	local is_show_str = self.data.process >= self.data.task_process and string.format(Language.Activity.ActivityProcess1,num_1) or string.format(Language.Activity.ActivityProcess2,num_1)
	--self.node_list.task_name.text.text = self.data.task_name
	self.node_list.task_des.text.text = string.format(self.data.task_desc, is_show_str)
    self.node_list.btn_txt.text.text = self.data.sort_index == 0 and Language.OpenServer.Get or Language.OpenServer.Goto

    local asset_res = self.data.sort_index == 0 and  "a3_kfkh_xljq_btn_1" or "a3_kfkh_xljq_btn_2"
    local bundle,asset =ResPath.GetKaiFuChongBangUi(asset_res)
    self.node_list.btn_task_lingqu.image:LoadSprite(bundle, asset)

    local cell_count = 0
    for i = 1, 6 do
        if self.data.reward_show[i - 1] then
            self.cell_list[i]:SetData(self.data.reward_show[i - 1])
            self.cell_list[i]:SetVisible(true)
            self.cell_list[i]:SetQualityIconVisible(false)
            self.cell_list[i]:SetEffectRootEnable(false)
            self.cell_list[i]:SetCellBgEnabled(false)
            cell_count = cell_count + 1
        else
            self.cell_list[i]:SetVisible(false)
        end
    end
    self.node_list.scroll.scroll_rect.enabled = cell_count > 2
    local width = self.node_list.scroll.rect.sizeDelta.x
    -- self.node_list.item_pos.rect.anchoredPosition = Vector2(-width/2, 0)
end

function QingYuanMissionRender:GetRewardOrGoAct()
	if self.data.sort_index == 0 then
		ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
			PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_TASK_REWARD,self.data.task_id)
	else
		FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
	end
end

---------------------
QingYuanMissionRewardItem = QingYuanMissionRewardItem or BaseClass(BaseRender)

function QingYuanMissionRewardItem:OnFlush()
    if not self.data then
        return
    end

    self.node_list.score_text.text.text = self.data.need_process
    if self.data.is_not_get then
        if self.data.total_process >= self.data.need_process then
            self.node_list.hl_img:SetActive(true)
            self.node_list.red_point:SetActive(true)
        else
            self.node_list.hl_img:SetActive(false)
            self.node_list.red_point:SetActive(false)
            self.node_list.mask:SetActive(false)
        end
    else
        self.node_list.hl_img:SetActive(false)
        self.node_list.red_point:SetActive(false)
        self.node_list.mask:SetActive(true)
    end
end