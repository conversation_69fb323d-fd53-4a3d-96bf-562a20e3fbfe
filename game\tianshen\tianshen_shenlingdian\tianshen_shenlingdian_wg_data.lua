TSShenLingDianData = TSShenLingDianData or BaseClass()
function TSShenLingDianData:__init()
	if TSShenLingDianData.Instance then
		error("[TSShenLingDianData] Attempt to create singleton twice!")
		return
	end
	
    TSShenLingDianData.Instance = self
    self:InitConfig()
    self:InitConfigCache()
	RemindManager.Instance:Register(RemindName.TianShen_Temple, BindTool.Bind(self.GetTianShenTempleAllRemind, self))
end

function TSShenLingDianData:__delete()
    self.order_cache = nil
	TSShenLingDianData.Instance = nil
end

-- 初始化配置
function TSShenLingDianData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_hall_cfg_auto")

   self.hall_cfg = cfg.hall
   self.hall_item_cfg = ListToMap(cfg.hall, "unlock_item_id")
   self.order_cfg = ListToMap(cfg.order, "seq", "order")
   self.order_item_cfg = ListToMap(cfg.order, "cost_item_id")
   self.star_cfg = ListToMapList(cfg.star, "seq")
   self.go_hall_cfg = ListToMapList(cfg.go_hall, "seq")
end

-- 初始化配置缓存
function TSShenLingDianData:InitConfigCache()
    self.order_cache = {}

    if not self.order_cfg then
        return
    end    

    for seq, order_list in pairs(self.order_cfg) do
        self.order_cache[seq] = {}

        for order, order_data in pairs(order_list) do
            if order_data and order_data.order ~= nil then
                local go_hall_index_str = order_data.go_hall_index or ""
                if go_hall_index_str ~= "" then
                    local go_hall_index_table = {}
                    local go_hall_list = Split(go_hall_index_str, ",")

                    for i, v in ipairs(go_hall_list) do
                        local slot_index = tonumber(v) or 0
                        go_hall_index_table[slot_index] = true
                    end

                    self.order_cache[seq][order] = go_hall_index_table
                end
            end
        end
    end
end

-- 总红点入口
function TSShenLingDianData:GetTianShenTempleAllRemind()
    if not self.hall_cfg then
        return 0
    end

    local item_id = 0
    local item_need_num = 0

    for _, hall_data in pairs(self.hall_cfg) do
        if self:GetTianShenTempleRemindBySeq(hall_data.seq) then
            return 1
        end
    end

    return 0
end

--判断一个神殿是否存在红点
function TSShenLingDianData:GetTianShenTempleRemindBySeq(seq)
    local cfg = self:GetHallCfgBySeq(seq)

    if not cfg then
        return false
    end

    local item_id = 0
    local item_need_num = 0
    local hall_info = self:GetClentTSHallItemInfo(seq)
    local now_order = hall_info and hall_info.order or 0
    local is_unlock = now_order > 0

    if is_unlock then-- 激活了可上阵
        if self:CheckTianShenTempleGoRemindBySeq(seq) then
            return true
        end

        -- 激活了可升阶
        if self:CheckTianShenTempleUpGradeRemindBySeq(seq, now_order) then
            return true
        end

        -- 激活了且可幻化
        if self:CheckTianShenTempleUpCanUseRemindBySeq(seq, now_order) then
            return true
        end
    else    -- 未激活可解锁
        item_id = cfg.unlock_item_id or 0
        item_need_num = cfg.unlock_item_num or 0
        local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
        if (not is_unlock) and item_num >= item_need_num then
            return true
        end
    end

    return false
end

-- 判断一个神殿是否可更换上阵红点
function TSShenLingDianData:CheckTianShenTempleGoRemindBySeq(seq, is_not_check_star)
    local hall_info = self:GetClentTSHallItemInfo(seq)
    local now_order = hall_info and hall_info.order or 0
    local has_list = self:GetCanSlotAllTianShen()
    local can_go_list = self:GetHallIndexListBySeqOrder(seq, now_order)
    local red = false

    if hall_info and hall_info.index_list then
        for i, now_index in ipairs(hall_info.index_list) do
            -- 神殿未激活或者未解锁孔位
            local is_lock = can_go_list[i - 1] == nil
            if not is_lock then
                if now_index == -1 then
                    if #has_list > 0 then
                        red = true
                        break
                    end
                else    -- 判断星数
                    if not is_not_check_star and self:CheckTianShenTempleGoRemindBySlot(now_index) then
                        red = true
                        break
                    end
                end
            end
        end
    end

    return red
end

-- 判断一个神殿上阵点位红点
function TSShenLingDianData:CheckTianShenTempleGoRemindBySlot(now_index, has_list)
    if has_list == nil then
        has_list = self:GetCanSlotAllTianShen()
    end

    if #has_list > 0 then
        local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(now_index)
        local star_num = tianshen_info and tianshen_info.star or 0

        for _, ts_data in ipairs(has_list) do
            if ts_data.star > star_num then
                return true
            end
        end
    end

    return false
end

-- 判断一个神殿升阶红点
function TSShenLingDianData:CheckTianShenTempleUpGradeRemindBySeq(seq, order)
    local cfg = self:GetOrderCfgBySeqOrder(seq, order)
    local next_cfg = self:GetOrderCfgBySeqOrder(seq, order + 1)
    local hall_info = self:GetClentTSHallItemInfo(seq)
    local now_order = hall_info and hall_info.order or 0

    if (not cfg) or (not next_cfg) then
        return false
    end

    local is_unlock = now_order > 0
    if not is_unlock then
        return false
    end

    local item_id = cfg.cost_item_id or 0
    local item_need_num = cfg.cost_item_num or 0
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

    if item_num >= item_need_num then
        return true
    end

    return false
end

-- 判断一个神殿可使用技能红点
function TSShenLingDianData:CheckTianShenTempleUpCanUseRemindBySeq(seq)
    local act_cfg = self:GetHallCfgBySeq(seq)
    local hall_info = self:GetClentTSHallItemInfo(seq)
    local now_order = hall_info and hall_info.order or 0
    local is_unlock = now_order > 0
    local can_go_list = self:GetHallIndexListBySeqOrder(seq, now_order)
    local need_go_hall_num = act_cfg and act_cfg.need_go_hall_num or 0
    local go_num = 0

    if hall_info and hall_info.index_list then
        for i, index_data in ipairs(hall_info.index_list) do
            -- 神殿未激活或者未解锁孔位
            local is_lock = (not is_unlock) or can_go_list[i - 1] == nil
            if index_data ~= nil then
                if index_data ~= -1 then
                    go_num = go_num + 1
                end
            end
        end
    end

    if go_num >= need_go_hall_num and self:GetCurrTSHallUseSeq() == -1 then
        return true
    end

    return false
end

-- 判断一个神殿可使用技能红点
function TSShenLingDianData:CheckIsUpItemId(item_id)
    if self.hall_item_cfg[item_id] then
        return true
    end

    if self.order_item_cfg[item_id] then
        return true
    end

    return false
end

-- 配置部分
-------------------------------- 分割 -----------------------------------
-- 获取殿堂信息
function TSShenLingDianData:GetHallCfgBySeq(seq)
    local empty = {}
    return (self.hall_cfg or empty)[seq]
end

-- 获取所有殿堂列表
function TSShenLingDianData:GetHallListCfg()
    return self.hall_cfg
end

-- 获取当前阶数信息
function TSShenLingDianData:GetOrderCfgBySeqOrder(seq, order)
    local empty = {}
    return ((self.order_cfg or empty)[seq] or empty)[order]
end

-- 获取当前阶数信息
function TSShenLingDianData:GetHallIndexListBySeqOrder(seq, order)
    local empty = {}
    return ((self.order_cache or empty)[seq] or empty)[order]
end

-- 获取当前星数对应的属性信息列表（获取两个，未解锁则为第一个和第二个，解锁了为当前的和下一个，满了则为最后一个和上一个）
function TSShenLingDianData:GetHallAttrListBySeqStar(seq, star)
    local empty = {}
    local star_list = (self.star_cfg or empty)[seq]
    local aim_table = {}

    if star_list then
        local is_find = false
        table.sort(star_list, SortTools.KeyLowerSorter("star_num"))

        for i, v in ipairs(star_list) do
            if is_find then
                table.insert(aim_table, v)
                break
            end

            if v.star_num > star then
                is_find = true
                table.insert(aim_table, v)

                -- 获得上一个为当前，上一个没有则未达到任何目标，循环取下一个
                if star_list[i - 1] ~= nil then
                    table.insert(aim_table, 1, star_list[i - 1])
                    break;
                end
            end
        end

        -- 没有找到下一个则取列表的最后一个
        if not is_find then
            local count = #star_list
            table.insert(aim_table, star_list[count])
        end
    end

    return aim_table
end

-- 获取上阵的对应个数及属性
function TSShenLingDianData:GetGoHallAttrListBySeqOrder(seq)
    local empty = {}
    return (self.go_hall_cfg or empty)[seq]
end

-------------------------------------------------------------------------
-- 协议部分
-------------------------------- 分割 -----------------------------------
--天神殿基础信息
function TSShenLingDianData:SetTSHallBaseInfo(protocol)
    self.use_seq = protocol.use_seq
end

-- 获取当前使用的神殿
function TSShenLingDianData:GetCurrTSHallUseSeq()
    return self.use_seq or -1
end

--天神殿神殿信息（全部）
function TSShenLingDianData:SetTSHallItemInfo(protocol)
    if protocol.hall_item_list then
        for i, v in ipairs(protocol.hall_item_list) do
            self:SetClentTSHallItemInfo(i, v)
        end
    end
end

--天神殿神殿信息（单个）
function TSShenLingDianData:SetTSHallItemUpdate(protocol)
    self:SetClentTSHallItemInfo(protocol.seq, protocol.hall_item, true)
end

-- 更新某个神灵殿信息
function TSShenLingDianData:SetClentTSHallItemInfo(seq, hall_item, is_client)
    if not self.hall_item_list then
        self.hall_item_list = {}
    end

    local client_seq = seq
    if is_client then
        client_seq = seq + 1
    end
    
    self.hall_item_list[client_seq] = hall_item
end

-- 获取某个神灵殿信息
function TSShenLingDianData:GetClentTSHallItemInfo(seq)
    local client_seq = seq + 1
    local empty = {}
    return (self.hall_item_list or empty)[client_seq]
end

---------------------------------------------------------------------------------
-- 配置为属性id 输出列表和战力(属性id字符串， 属性value字符串, 属性数量读取开始索引， 属性数量读取结束索引)
function TSShenLingDianData:OutStrAttrListAndCapalityByAttrId(cfg, next_cfg, type_key_str, value_key_str, read_start, read_end)
	local attr_list = {}
    local capability = 0
    if IsEmptyTable(cfg) or type_key_str == nil or value_key_str == nil then
        return attr_list, capability
    end

	read_start = read_start or 1
	read_end = read_end or 5

	local attr_list = {}
	local attribute = AttributePool.AllocAttribute()
	for i = read_start, read_end do
		local attr_id = cfg[type_key_str .. i]
		local attr_value = cfg[value_key_str .. i]
        local add_value = next_cfg and next_cfg[value_key_str .. i] or 0

		if attr_id and attr_value and attr_value > 0 then
			local data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			data.attr_str = attr_id
            data.attr_value = attr_value
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)

            if add_value ~= 0 then
                data.add_value = add_value
            end

			table.insert(attr_list, data)

			if attribute[attr_str] then
				attribute[attr_str] = attribute[attr_str] + attr_value
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    capability = AttributeMgr.GetCapability(attribute)
	return attr_list, capability
end

-- 获取当前神殿上已经上阵的天神
function TSShenLingDianData:GetAllSlotTianShenFlag()
    local flag_list = {}
    if not self.hall_item_list then
        return flag_list
    end

    for _, hall_item in ipairs(self.hall_item_list ) do
        if hall_item and hall_item.index_list then
            for _, tianshen_index in ipairs(hall_item.index_list) do
                if tianshen_index ~= -1 then
                    flag_list[tianshen_index] = true
                end
            end
        end
    end

    return flag_list
end

-- 获取当前已激活且未上阵的天神
function TSShenLingDianData:GetCanSlotAllTianShen(tianshen_index, shendian_seq, slot_index, include_self)
    local tianshen_info = TianShenWGData.Instance:GetNowTianShenInfo()
    local magic_image_cfg = TianShenWGData.Instance:GetTianShenImageCfg()
    local list = {}
    local flag_list = tianshen_info.active_image_flag
    local slot_flag = self:GetAllSlotTianShenFlag()

	local len = TIANSHEN_MAX - 1
	for i = 0, len do
		if tianshen_info.upgrade_list[i] and 1 == flag_list[i] and ((not slot_flag[i]) or (include_self and tianshen_index == magic_image_cfg[i].index)) then    -- 检测激活
            local data = {}
            data.select_index = tianshen_index
            data.select_shendian_seq = shendian_seq
            data.select_slot_index = slot_index
            data.star = tianshen_info.upgrade_list[i].star or 0
            data.series = magic_image_cfg[i] and magic_image_cfg[i].series or 0
            data.index = magic_image_cfg[i] and magic_image_cfg[i].index or 0
            data.cfg_data = magic_image_cfg[i]
            table.insert(list, data)
		end
	end
	
    local sort_func = function (a, b)
        if a.star > b.star then
            return true
        end

        if (not a.series) or not (b.series) then
            return true
        end

        if a.series ~= b.series then
            return a.series > b.series
        end

        return a.index > b.index
    end

    table.sort(list, sort_func)

	return list
end

-- 获取当前神殿所有已上阵的星数
function TSShenLingDianData:GetHallAllStarNumBySeq(shendian_seq)
    local hall_item = self:GetClentTSHallItemInfo(shendian_seq)
    local num_num = 0

    if hall_item and hall_item.index_list then
        for _, tianshen_index in ipairs(hall_item.index_list) do
            if tianshen_index ~= -1 then
                local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(tianshen_index)
                local star_num = tianshen_info and tianshen_info.star or 0
                num_num = num_num + star_num
            end
        end
    end

    return num_num
end
