-- 天裳豪礼-直购礼包
local PRICE_TYPE = {
	RMB = 1,
	LING_YU = 2
}

function GloryCrystalHaoLiView:ReleasePurchase()
	self:RemovePurchaseCountDown()

	if self.purchase_list then
		self.purchase_list:DeleteMe()
		self.purchase_list = nil
	end

	if self.ex_award_list then
		self.ex_award_list:DeleteMe()
		self.ex_award_list = nil
	end
end

function GloryCrystalHaoLiView:LoadPurchaseCallBack()
	if not self.purchase_list then
		self.purchase_list = AsyncListView.New(GloryCrystalPurchaseCell, self.node_list.purchase_list_view)
		self.purchase_list:SetRefreshCallback(BindTool.Bind(self.PurchaseListRefreshCallback, self))
	end

	if not self.ex_award_list then
		self.ex_award_list = AsyncListView.New(GloryCrystalExAwardItem, self.node_list.award_list)
		self.ex_award_list:SetStartZeroIndex(true)
	end

	self.node_list.buy_all_tip.text.text = Language.GloryCrystal.BuyAllTips
	self.flush_count = 0
	self:UpdateDirTag(0)

	self:SetPurchaseCountDown()

	XUI.AddClickEventListener(self.node_list.btn_buy_all, BindTool.Bind(self.OneKeyBuy, self))
	XUI.AddClickEventListener(self.node_list.btn_get_ex_award, BindTool.Bind(self.GetExAward, self))
end

function GloryCrystalHaoLiView:FlushPurchase()
	local list = GloryCrystalWGData.Instance:GetPurchase()
	-- 排序
	local show_list = {}
	for _, value in pairs(list) do
		local buy_num = GloryCrystalWGData.Instance:GetPurchaseBuyCount(value.seq)
		local data = {}
		data.cfg = value
		data.seq = value.seq
		data.sort = 1
		if buy_num >= value.limit_num then
			data.sort = 2
		end
		table.insert(show_list, data)
	end
	table.sort(show_list, SortTools.KeyLowerSorters("sort", "seq"))
	self.flush_count = 0
	self.purchase_list:SetDataList(show_list)

	self:FlushBuyAllBtn()
	self:FlushExAward()
end

function GloryCrystalHaoLiView:FlushPurchaseTime(left_time)
	if self.node_list and self.node_list.purchase_time_text then
		local time = TimeUtil.FormatSecondDHM9(left_time)
		self.node_list.purchase_time_text.text.text = string.format(Language.GloryCrystal.PurchaseTime, time)
	end
end

function GloryCrystalHaoLiView:RemovePurchaseCountDown()
	if self.purchase_count_down then
		CountDown.Instance:RemoveCountDown(self.purchase_count_down)
		self.purchase_count_down = nil
	end
end

function GloryCrystalHaoLiView:SetPurchaseCountDown()
	self:RemovePurchaseCountDown()
	
	-- 今天剩余时间
	local all_time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	local timer_func = function(elapse_time, total_time)
		self:FlushPurchaseTime(total_time - elapse_time)
	end
	
	timer_func(0, all_time)
	self.purchase_count_down = CountDown.Instance:AddCountDown(all_time, 1, timer_func, end_call_back)
end

-- 全部购买额外奖励
function GloryCrystalHaoLiView:FlushExAward()
	local ex_award_cfg = GloryCrystalWGData.Instance:GetPurchaseExAward()
	local list = ex_award_cfg.reward_item or {}
	self.ex_award_list:SetDataList(list)

	self.node_list.buy_all_sprice.text.text = string.format(Language.GloryCrystal.OneKeyText1, ex_award_cfg.price or 0)
	local is_show_discount = (ex_award_cfg.discount and ex_award_cfg.discount ~= "") and true or false
	self.node_list.discount:CustomSetActive(is_show_discount)
	self.node_list.discount_text.text.text = ex_award_cfg.discount or ""
end

function GloryCrystalHaoLiView:FlushBuyAllBtn()
	local show_btn = not GloryCrystalWGData.Instance:HasBuy()
	self.node_list.btn_buy_all:CustomSetActive(show_btn)

	-- 一键购买奖励：礼包全部卖完也可以领取
	local is_got = GloryCrystalWGData.Instance:IsGotExReward()
	show_btn = not is_got and (GloryCrystalWGData.Instance:IsOneKeyBuy() or GloryCrystalWGData.Instance:IsBuyAll())
	self.node_list.btn_get_ex_award:CustomSetActive(show_btn)

	self.node_list.buy_all_got_tag:CustomSetActive(is_got)
end

function GloryCrystalHaoLiView:OneKeyBuy()
	local ex_award_cfg = GloryCrystalWGData.Instance:GetPurchaseExAward()
	if ex_award_cfg then
		RechargeWGCtrl.Instance:Recharge(ex_award_cfg.price, ex_award_cfg.rmb_type, ex_award_cfg.rmb_seq)
	end
end

function GloryCrystalHaoLiView:GetExAward()
	local grade = GloryCrystalWGData.Instance:GetCurGrade()
	GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.RMB_ONEKEY_REWARD, grade)
end

function GloryCrystalHaoLiView:PurchaseListRefreshCallback(item_cell, cell_index)
	if self.flush_count < 3 then -- 列表初始刷新不处理
		self.flush_count = self.flush_count + 1
		return
	end

	self:UpdateDirTag(cell_index)
end

function GloryCrystalHaoLiView:UpdateDirTag(cell_index)
	self.node_list.purchase_left_tag:CustomSetActive(cell_index > 1)
	local show_list = GloryCrystalWGData.Instance:GetPurchase()
	self.node_list.purchase_right_tag:CustomSetActive(cell_index < #show_list)
end

--------------------------------------- GloryCrystalPurchaseCell ------------------------------------
GloryCrystalPurchaseCell = GloryCrystalPurchaseCell or BaseClass(BaseRender)
function GloryCrystalPurchaseCell:ReleaseCallBack()
	if self.award_list then
		self.award_list:DeleteMe()
		self.award_list = nil
	end

	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function GloryCrystalPurchaseCell:LoadCallBack()
	if not self.award_list then
		self.award_list = AsyncListView.New(GloryCrystalGoods, self.node_list.item_list)
	end

	if not self.show_item then
		self.show_item = ItemCell.New(self.node_list.show_item)
	end

	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.Buy, self))
end

function GloryCrystalPurchaseCell:OnFlush()
	local data = self:GetData()
	data = data and data.cfg
	if IsEmptyTable(data) then
		return
	end

	local list = data.reward_item
	self.award_list:SetDataList(list)
	self.show_item:SetData(list[0])

	self.node_list.title.text.text = data.gift_name or ""

	local buy_num = GloryCrystalWGData.Instance:GetPurchaseBuyCount(data.seq)
	local is_sold_out = buy_num >= data.limit_num
	local str = is_sold_out and Language.GloryCrystal.PurchaseLimit2 or Language.GloryCrystal.PurchaseLimit1
	self.node_list.limit_num.text.text = string.format(str, buy_num, data.limit_num)

	local is_free = data.price == 0
	local use_lingyu = data.price_type == PRICE_TYPE.LING_YU
	self.node_list.sprice2:CustomSetActive(use_lingyu)
	self.node_list.sprice:CustomSetActive(not use_lingyu)
	if use_lingyu then
		self.node_list.sprice2.text.text = data.price
	else
		local price_str = is_free and Language.GloryCrystal.Free or string.format(Language.GloryCrystal.Price, data.price)
		self.node_list.sprice.text.text = price_str
	end
	self.node_list.btn_buy:CustomSetActive(not is_sold_out)
	self.node_list.free_remind:CustomSetActive(is_free)
	self.node_list.sold_out:CustomSetActive(is_sold_out)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_tsxy_zglb_lb" .. data.goods_bg)
	if self.node_list.bg then
		self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.bg.raw_image:SetNativeSize()
		end)
	end
end

function GloryCrystalPurchaseCell:Buy()
	local data = self:GetData()
	data = data and data.cfg
	if IsEmptyTable(data) then
		return
	end

	if data.price == 0 then
		GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.FREE_REWARD, data.seq)
	elseif data.price > 0 then
		if data.price_type == PRICE_TYPE.LING_YU then
			local enough = RoleWGData.Instance:GetIsEnoughUseGold(data.price)
			if not enough then
				VipWGCtrl.Instance:OpenTipNoGold()
				return
			end
			GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.BUY_GIFT, data.seq)
		else
			RechargeWGCtrl.Instance:Recharge(data.price, data.rmb_type, data.rmb_seq)
		end
	end
end

------------------------------------------ GloryCrystalExAwardItem --------------------------------------
GloryCrystalExAwardItem = GloryCrystalExAwardItem or BaseClass(BaseRender)

function GloryCrystalExAwardItem:ReleaseCallBack()
	if not self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GloryCrystalExAwardItem:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_node)
	end
end

function GloryCrystalExAwardItem:OnFlush()
	local data = self:GetData()

	self.item_cell:SetData(data)
	local status = GloryCrystalWGData.Instance:IsGotExReward()
	self.item_cell:SetLingQuVisible(status)
end

------------------------------------------ GloryCrystalGoods --------------------------------------
GloryCrystalGoods = GloryCrystalGoods or BaseClass(BaseRender)

function GloryCrystalGoods:ReleaseCallBack()
	if not self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GloryCrystalGoods:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_node)
	end
end

function GloryCrystalGoods:OnFlush()
	local data = self:GetData()

	self.item_cell:SetData(data)
end