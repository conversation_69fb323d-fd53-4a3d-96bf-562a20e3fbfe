local ResUtil = require "lib/resmanager/res_util"
local M = ResUtil.create_class()
local TypeUnitySprite = typeof(UnityEngine.Sprite)
-- local TypeSpriteAtlas = typeof(UnityEngine.U2D.SpriteAtlas)
local _slower = string.lower
local _sfind = string.find
local _ssub = string.sub

function M:_init(asset_bundle)
	self.asset_bundle = asset_bundle
	self.sprites = nil
end

function M:Unload(unloadAllLoadedObjects)
	self.asset_bundle:Unload(unloadAllLoadedObjects or false)
	self.asset_bundle = nil
	self.sprites = nil
end

-- function M:LoadSpriteAtlas()
-- 	self.sprites = {}
-- 	local sprite_atlas_list = {}
-- 	local all_asset_names = self.asset_bundle:GetAllAssetNames()
-- 	for i = 0, all_asset_names.Length - 1 do
-- 		local asset_name = all_asset_names[i]
-- 		if _sfind(asset_name, "%.spriteatlas") then
-- 			local sprite_atlas = self.asset_bundle:LoadAsset(asset_name, TypeSpriteAtlas)
-- 			table.insert(sprite_atlas_list, sprite_atlas)

-- 			print_error("spriteatlas name:", asset_name)
-- 		end
-- 	end

-- 	for _, sprite_atlas in ipairs(sprite_atlas_list) do
-- 		local sprite_list = sprite_atlas:GetSprites()
-- 		for i = 0, sprite_list.Length - 1 do
-- 			local sprite = sprite_list[i]
-- 			local sprite_name = _slower(sprite.name)
-- 			local index = _sfind(sprite_name, "%(clone%)")
-- 			if index then
-- 				sprite_name = _ssub(sprite_name, 1, index - 1)
-- 			end

-- 			self.sprites[sprite_name .. ".png"] = sprite
-- 		end
-- 	end
-- end

function M:LoadAsset(asset_name, asset_type)
	if nil == asset_type then
		return self.asset_bundle:LoadAsset(asset_name)
	else--if TypeUnitySprite ~= asset_type then
		return self.asset_bundle:LoadAsset(asset_name, asset_type)
	-- else
	-- 	print_error("加载sprite", asset_name)
	-- 	if nil == self.sprites then
	-- 		self:LoadSpriteAtlas()
	-- 	end

	-- 	local sprite = self.sprites[asset_name]
	-- 	if nil == sprite then
	-- 		sprite = self.asset_bundle:LoadAsset(asset_name, asset_type)
	-- 	end

	-- 	return sprite
	end
end

function M:LoadAssetAsync(asset_name, asset_type)
	if nil == asset_type then
		return self.asset_bundle:LoadAssetAsync(asset_name)
	else--if TypeUnitySprite ~= asset_type then
		return self.asset_bundle:LoadAssetAsync(asset_name, asset_type)
	-- else
	-- 	print_error("Sprite暂时不支持异步")
	-- 	return nil
	end
end

return M