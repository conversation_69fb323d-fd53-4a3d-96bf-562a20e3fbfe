--------------------------------------------------------
--玩家身上的装备数据管理
--------------------------------------------------------
EquipWGData = EquipWGData or BaseClass()

--期限小鬼id
local QIXIAN_XIAOGUI = 10101
--期限小鬼类型
local QIXIAN_XIAOGUI_TYPE = 1

function EquipWGData:__init()
	-- 小鬼守护
	self.imp_guard_info = {
		used_imp_type_1 = 0,
		used_imp_type_2 = 0,
		item_wrapper = {}
	}
	self.grid_data_list = {}
	EquipWGData.Instance = self
	self.notify_data_change_callback_list = {}		--身上装备有更新变化时进行回调
	self.notify_datalist_change_callback_list = {} 	--身上装备列表有变化时回调，一般是整理时，或初始化物品列表时
	self.notify_xiaogui_change_callback_list = {}  	--小鬼数据变化回调
	self.stone_infos = {} 							--宝石孔列表
	self.fabao_info = {
		fabao_id = 0,
		fabao_gain_time = 0
	}

	self.active_total_star_list = {}

	self.CheckGuardQueue = {}
	-- self.old_target_guard = {}
	self.impguard_other = ConfigManager.Instance:GetAutoConfig("impguard_auto").other[1]
	self.equip_forge_cfg_part_order = ListToMap(ConfigManager.Instance:GetAutoConfig("equipforge_auto").equip_puton_limit, "equip_part", "equip_order")
	self.equip_big_type_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("equipforge_auto").equip_big_type, "equip_type")
end

function EquipWGData:__delete()
  	self.grid_data_list = nil
  	self.notify_data_change_callback_list = nil
  	self.notify_datalist_change_callback_list = nil
  	self.notify_xiaogui_change_callback_list = nil
  	EquipWGData.Instance = nil
end

function EquipWGData:SetDataList(datalist, fabao_info)
	self.grid_data_list = datalist                         -- 角色装备 15一套 对应装备肉身seq   以前最大 15  现在最大 375
	self.fabao_info.fabao_id = fabao_info.fabao_id or 0
	self.fabao_info.fabao_gain_time = fabao_info.fabao_gain_time
	self:CahceComposeRermindNeedData()
	for k,v in pairs(self.notify_datalist_change_callback_list) do  --物品有变化，通知观察者，不带消息体
		v()
	end

	--御灵数据
	EquipmentWGData.Instance:SetEquipYuLingData()
end

function EquipWGData:CahceComposeRermindNeedData()
	self.cache_compose_remind_need_data = {}
	local data = self.cache_compose_remind_need_data
	local temp_data = {}
	local calc_num_lsit = {}
	local need_num_list = {[0] = 6, 2}

	local xianjie_index = GameEnum.EQUIP_INDEX_XIANJIE
	local xianzhuo_index = GameEnum.EQUIP_INDEX_XIANZHUO
	local hunjie_index = GameEnum.EQUIP_INDEX_HUNJIE
	local normal_type = GameEnum.EQUIP_BIG_TYPE_NORMAL
	local xianqi_type = GameEnum.EQUIP_BIG_TYPE_XIANQI
	for i = 0, xianqi_type do
		data[i] = {order = 0, color = 0, star = 0}
		temp_data[i] = {order = 0, color = 0, star = 0}
		calc_num_lsit[i] = 0
	end

	local equip_type = 0
	local item_cfg
	local normal_num, shipin_num, need_normal_num, need_shipin_num = 0, 0, 6, 2
	local grid_list = self:GetDataList()
	for k, v in pairs(grid_list) do
		if v.item_id > 0 and v.index ~= xianjie_index and v.index ~= xianzhuo_index and v.index ~= hunjie_index then
			item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg then
				equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(v.index)
				if calc_num_lsit[equip_type] then
					calc_num_lsit[equip_type] = calc_num_lsit[equip_type] + 1
				end

				local data = temp_data[equip_type]
				if data then
					if data.color == 0 or item_cfg.color < data.color then
						data.color = item_cfg.color
					end

					if data.order == 0 or item_cfg.order < data.order then
						data.order = item_cfg.order
					end
				end
			end
		end
	end

	for i = 0, xianqi_type do
		if calc_num_lsit[i] >= need_num_list[i] then
			data[i] = temp_data[i]
		end
	end

	return self.cache_compose_remind_need_data
end

function EquipWGData:GetComposeRermindNeedData(equip_type)
	return (self.cache_compose_remind_need_data or {})[equip_type]
end

function EquipWGData:GetDataList()
	return self.grid_data_list
end

-- 获取法宝信息
function EquipWGData:GetFabaoInfo()
	return self.fabao_info
end

--获得某个格子的数据
function EquipWGData:GetGridData(index)
	return self.grid_data_list[index]
end

----获得同心锁以外装备
--function EquipWGData:GetOutTongXinSuoData()
--	local data = {}
--	local min_id,max_id = EquipmentWGData.Instance:GetTongXinSuoID()
--	if self.grid_data_list then
--		for k,v in pairs(self.grid_data_list) do
--			if not (v.item_id >= min_id and v.item_id <= max_id) then
--				table.insert(data,v)
--			end
--		end
--	end
--	return data
--end

--获取身上装备数量
function EquipWGData:GetDataCount()
	local count = 0
	for k,v in pairs(self.grid_data_list) do
		count = count + 1
	end
	return count
end

--获取身上装备数量
function EquipWGData:GetDataCountByAttr(zhuan, color)
	zhuan = zhuan or 0
	color = color or 0
	local count = 0
	for k,v in pairs(self.grid_data_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		-- if item_cfg and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANLIAN and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_HUNJIE and
		if item_cfg and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_XIANLIAN and
			item_cfg.order >= zhuan and item_cfg.color >= color then
			 count = count + 1
		end
	end
	return count
end

--改变某个格中的数据
function EquipWGData:ChangeDataInGrid(data)
	if data == nil then
		return
	end

	local change_reason = 2
	local change_item_id = data.item_id
	local change_item_index = data.index
	local t = self:GetGridData(data.index)
	if t ~= nil and data.num == 0 then --delete
		change_reason = 0
		change_item_id = t.item_id
		self.grid_data_list[data.index] = nil
	elseif t == nil	 then			   --add
		change_reason = 1
	end
	if change_reason ~= 0 then
		self.grid_data_list[data.index] = data
		
		--御灵数据
		EquipmentWGData.Instance:UpdateEquipYuLingData(data.index)
	end

	self:CahceComposeRermindNeedData()
	for k,v in pairs(self.notify_data_change_callback_list) do  --物品有变化，通知观察者，带消息体
		v(change_item_id, change_item_index, change_reason)
	end
end

-- 获取身上某个部位最高阶级的装备  GameEnum.EQUIP_INDEX_TOUKUI
function EquipWGData:GetMaxOrderPartEquip(equip_item_id)
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(equip_item_id)
	local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
	local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local item_id = 0

	local total_equip_body_data = EquipBodyWGData.Instance:GetAllEquipBodyDataList()
	if not IsEmptyTable(total_equip_body_data) then
		for i = #total_equip_body_data, 0, -1 do
			local data = total_equip_body_data[i]

			if data then
				local target_index = data.seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + equip_part
				local wear_equip_data = self:GetGridData(target_index)

				if not IsEmptyTable(wear_equip_data) and wear_equip_data.item_id > 0 then
					return wear_equip_data.item_id
				end
			end
		end
	end

	return item_id
end

function EquipWGData:OnEquipmentTihuanRet(change_equip_data)
	local equip_data = self.grid_data_list[change_equip_data.equip_index]
	local bag_data = ItemWGData.Instance:GetGridData(change_equip_data.down_bag_index) --RoleBagWGCtrl.Instance:GetBagGridCellData(change_equip_data.down_bag_index)
	local _, sub = self:GetIsBetterEquip(bag_data, true)
	if sub > 0 then
		return
	end

	local item_cfg, _ = ItemWGData.Instance:GetItemConfig(bag_data.item_id)
	if item_cfg.color < GameEnum.ITEM_COLOR_PINK then
		return
	end

	local legend_num = bag_data.param and bag_data.param.star_level or 0
	local decompose_cfg, is_can_decompos = EquipmentWGData.Instance:GetEquipDecomposeByID(item_cfg.id, legend_num)
	if not decompose_cfg or not is_can_decompos then
		return
	end

	local desc = EquipmentWGData.Instance:GetEquipDeComposeDescByItemid(item_cfg.id, legend_num, true)
	local index_list = {}
	index_list[1] = change_equip_data.down_bag_index

	TipWGCtrl.Instance:OpenAlertTips(desc, function()
		EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_DECOMPOSE
				, bag_data.item_id, decompose_cfg.decompose_equip_best_attr_num, index_list)
	end)
end

--绑定数据改变时的回调方法.用于任意物品有更新时进行回调
function EquipWGData:NotifyDataChangeCallBack(callback, notify_datalist)
	if notify_datalist == true then
		self:UnNotifyDataChangeCallBack(callback)
		self.notify_datalist_change_callback_list[#self.notify_datalist_change_callback_list + 1] = callback
	else
		self:UnNotifyDataChangeCallBack(callback)
		self.notify_data_change_callback_list[#self.notify_data_change_callback_list + 1] = callback
	end
end

--移除绑定回调
function EquipWGData:UnNotifyDataChangeCallBack(callback)
	for k,v in pairs(self.notify_data_change_callback_list) do
		if v == callback then
			self.notify_data_change_callback_list[k] = nil
			return
		end
	end
	for k,v in pairs(self.notify_datalist_change_callback_list) do
		if v == callback then
			self.notify_datalist_change_callback_list[k] = nil
			return
		end
	end
end

--通过装备类型获得可以放置的索引
function EquipWGData:GetEquipIndexByType(type)
	if type == GameEnum.EQUIP_TYPE_TOUKUI then
		return GameEnum.EQUIP_INDEX_TOUKUI
	elseif type == GameEnum.EQUIP_TYPE_YIFU then
		return GameEnum.EQUIP_INDEX_YIFU
	elseif type == GameEnum.EQUIP_TYPE_KUZI then
		return GameEnum.EQUIP_INDEX_KUZI
	elseif type == GameEnum.EQUIP_TYPE_XIANLIAN then
		return GameEnum.EQUIP_INDEX_XIANLIAN
	elseif type == GameEnum.EQUIP_TYPE_XIEZI then
		return GameEnum.EQUIP_INDEX_XIEZI
	elseif type == GameEnum.EQUIP_TYPE_WUQI then
		return GameEnum.EQUIP_INDEX_WUQI
	elseif type == GameEnum.EQUIP_TYPE_XIANZHUI then
		return GameEnum.EQUIP_INDEX_XIANZHUI
	elseif type == GameEnum.EQUIP_TYPE_XIANFU then
		return GameEnum.EQUIP_INDEX_XIANFU
	elseif type == GameEnum.EQUIP_TYPE_XIANJIE then
		return GameEnum.EQUIP_INDEX_XIANJIE
	elseif type == GameEnum.EQUIP_TYPE_XIANZHUO then
		return GameEnum.EQUIP_INDEX_XIANZHUO
	elseif type == GameEnum.EQUIP_TYPE_HUFU then
		return GameEnum.EQUIP_INDEX_HUFU
	elseif type == GameEnum.EQUIP_TYPE_MIANJIA then
		return GameEnum.EQUIP_INDEX_MIANJIA
	elseif type == GameEnum.EQUIP_TYPE_XIANGLIAN_1 then
		return GameEnum.EQUIP_INDEX_XIANGLIAN_1
	elseif type == GameEnum.EQUIP_TYPE_DIAOZHUI then
		return GameEnum.EQUIP_INDEX_DIAOZHUI
	elseif type == GameEnum.EQUIP_TYPE_XIAOGUI then
		return GameEnum.EQUIP_TYPE_XIAOGUI
	-- elseif type == GameEnum.EQUIP_TYPE_JINGLING then
	-- 	return GameEnum.EQUIP_INDEX_JINGLING

	---婚戒-----------------------------------------------------------------
	elseif type == GameEnum.EQUIP_TYPE_HUNJIE then
		return GameEnum.EQUIP_INDEX_HUNJIE

	---师徒装备类型---------------------------------------------------------
	elseif type == GameEnum.E_TYPE_SHITU_TOUKUI then
		return GameEnum.E_INDEX_SHITU_TOUKUI
	elseif type == GameEnum.E_TYPE_SHITU_YIFU then
		return GameEnum.E_INDEX_SHITU_YIFU
	elseif type == GameEnum.E_TYPE_SHITU_HUTUI then
		return GameEnum.E_INDEX_SHITU_HUTUI
	elseif type == GameEnum.E_TYPE_SHITU_XIEZI then
		return GameEnum.E_INDEX_SHITU_XIEZI
	elseif type == GameEnum.E_TYPE_SHITU_HUSHOU then
		return GameEnum.E_INDEX_SHITU_HUSHOU
	elseif type == GameEnum.E_TYPE_SHITU_XIANGLIAN then
		return GameEnum.E_INDEX_SHITU_XIANGLIAN
	elseif type == GameEnum.E_TYPE_SHITU_WUQI then
		return GameEnum.E_INDEX_SHITU_WUQI
	elseif type == GameEnum.E_TYPE_SHITU_JIEZHI then
		return GameEnum.E_INDEX_SHITU_JIEZHI

	----------------------------转生装备--------------------------------
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_1 then
		return GameEnum.E_TYPE_ZHUANSHENG_1
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_2 then
		return GameEnum.E_TYPE_ZHUANSHENG_2
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_3 then
		return GameEnum.E_TYPE_ZHUANSHENG_3
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_4 then
		return GameEnum.E_TYPE_ZHUANSHENG_4
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_5 then
		return GameEnum.E_TYPE_ZHUANSHENG_5
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_6 then
		return GameEnum.E_TYPE_ZHUANSHENG_6
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_7 then
		return GameEnum.E_TYPE_ZHUANSHENG_7
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_8 then
		return GameEnum.E_TYPE_ZHUANSHENG_8
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_9 then
		return GameEnum.E_TYPE_ZHUANSHENG_9
	elseif type == GameEnum.E_TYPE_ZHUANSHENG_10 then
		return GameEnum.E_TYPE_ZHUANSHENG_10
	end
	return -1
end

-- 获取人物装备在 装备肉身中 0-374 中的index序号
function EquipWGData:GetEquipBodyIndexByItemCfg(item_cfg)
	local type = item_cfg.sub_type
	local order = item_cfg.order or 0
	local part_index = -1

	if type == GameEnum.EQUIP_TYPE_TOUKUI then
		part_index = GameEnum.EQUIP_INDEX_TOUKUI
	elseif type == GameEnum.EQUIP_TYPE_YIFU then
		part_index = GameEnum.EQUIP_INDEX_YIFU
	elseif type == GameEnum.EQUIP_TYPE_KUZI then
		part_index = GameEnum.EQUIP_INDEX_KUZI
	elseif type == GameEnum.EQUIP_TYPE_XIANLIAN then
		part_index = GameEnum.EQUIP_INDEX_XIANLIAN
	elseif type == GameEnum.EQUIP_TYPE_XIEZI then
		part_index = GameEnum.EQUIP_INDEX_XIEZI
	elseif type == GameEnum.EQUIP_TYPE_WUQI then
		part_index = GameEnum.EQUIP_INDEX_WUQI
	elseif type == GameEnum.EQUIP_TYPE_XIANZHUI then
		part_index = GameEnum.EQUIP_INDEX_XIANZHUI
	elseif type == GameEnum.EQUIP_TYPE_XIANFU then
		part_index = GameEnum.EQUIP_INDEX_XIANFU
	elseif type == GameEnum.EQUIP_TYPE_XIANJIE then
		part_index = GameEnum.EQUIP_INDEX_XIANJIE
	elseif type == GameEnum.EQUIP_TYPE_XIANZHUO then
		part_index = GameEnum.EQUIP_INDEX_XIANZHUO
	end

	if part_index >= 0 and order > 0 then
		local equip_body_seq = EquipBodyWGData.Instance:GetEquipOrderToEquipBodySeq(order)

		if equip_body_seq then
			return equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + part_index
		end
	end

	return -1
end

--判断是否是神兽装备
function EquipWGData.GetEquipTypeIsShenShou(sub_type)
	return sub_type >= GameEnum.E_TYPE_SHENSHOU_PART_0 and sub_type <= GameEnum.E_TYPE_SHENSHOU_PART_4
end

--通过装备索引获得装备类型
function EquipWGData.GetEquipTypeByIndex(index)
	if index == GameEnum.EQUIP_INDEX_TOUKUI then
		return GameEnum.EQUIP_TYPE_TOUKUI
	elseif index == GameEnum.EQUIP_INDEX_YIFU then
		return GameEnum.EQUIP_TYPE_YIFU
	elseif index == GameEnum.EQUIP_INDEX_KUZI then
		return GameEnum.EQUIP_TYPE_KUZI
	elseif index == GameEnum.EQUIP_INDEX_XIANLIAN then
		return GameEnum.EQUIP_TYPE_XIANLIAN
	elseif index == GameEnum.EQUIP_INDEX_XIEZI then
		return GameEnum.EQUIP_TYPE_XIEZI
	elseif index == GameEnum.EQUIP_INDEX_WUQI then
		return GameEnum.EQUIP_TYPE_WUQI
	elseif index == GameEnum.EQUIP_INDEX_XIANZHUI then
		return GameEnum.EQUIP_TYPE_XIANZHUI
	elseif index == GameEnum.EQUIP_INDEX_XIANFU then
		return GameEnum.EQUIP_TYPE_XIANFU
	elseif index == GameEnum.EQUIP_INDEX_XIANJIE then
		return GameEnum.EQUIP_TYPE_XIANJIE
	elseif index == GameEnum.EQUIP_INDEX_XIANZHUO then
		return GameEnum.EQUIP_TYPE_XIANZHUO
	elseif index == GameEnum.EQUIP_INDEX_HUFU then
		return GameEnum.EQUIP_TYPE_HUFU
	elseif index == GameEnum.EQUIP_INDEX_MIANJIA then
		return GameEnum.EQUIP_TYPE_MIANJIA
	elseif index == GameEnum.EQUIP_INDEX_XIANGLIAN_1 then
		return GameEnum.EQUIP_TYPE_XIANGLIAN_1
	elseif index == GameEnum.EQUIP_INDEX_DIAOZHUI then
		return GameEnum.EQUIP_TYPE_DIAOZHUI
	end
	return -1
end

--获得装备部位名字
function EquipWGData:GetEquipPartNameByType(type)
	return Language.Common.EquipName[type] or ""
end

function EquipWGData:GetEquipCellNameByType(type)
	return Language.Common.EquipCellName[type] or ""
end

-- 根据物品ID获取人物身上的装备信息
function EquipWGData:GetEquipInfoFromRole(itemid)
	if itemid and nil ~= self.grid_data_list then
		for k, v in pairs(self.grid_data_list) do
			if itemid == v.item_id then
				return v
			end
		end
	end
end

-- 获得排序好的属性列表
function EquipWGData.GetSortAttrListByCfg(cfg)
	local attr_list = {}
	local temp_list = AttributeMgr.GetAttributteValueByClass(cfg)

	for k, v in pairs(temp_list) do
		local data = {}
		data.attr_str = k
		data.attr_value = v
		data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
		table.insert(attr_list, data)
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end


-- 获得排序好的属性列表(有下一级)
function EquipWGData.GetSortAttrListHaveNextByCfg(cur_cfg, next_cfg)
	local attr_list = {}
	local cur_temp_list = AttributeMgr.GetAttributteValueByClass(cur_cfg)
	local next_temp_list = AttributeMgr.GetAttributteValueByClass(next_cfg)

	local cur_no_data = IsEmptyTable(cur_temp_list)
	local temp_list = cur_no_data and next_temp_list or cur_temp_list
	for k, v in pairs(temp_list) do
		local data = {}
		data.attr_str = k
		data.attr_value = cur_no_data and 0 or v
		data.attr_next_value = next_temp_list[k] or 0
		data.add_value = data.attr_next_value - data.attr_value
		data.add_value = data.add_value > 0 and data.add_value or 0
		data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
		table.insert(attr_list, data)
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end



-- 获得排序好的属性列表(有下一级)
-- (属性id字符串， 属性value字符串, 属性数量读取开始索引， 属性数量读取结束索引)
function EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, type_key_str, value_key_str, read_start, read_end, not_sort)
	read_start = read_start or 1
	read_end = read_end or 4
	type_key_str = type_key_str or "attr_id"
	value_key_str = value_key_str or "attr_value"

	local attr_str
	local attr_list = {}
	local cur_attr_id, cur_attr_value, next_attr_id, next_attr_value, attr_id, attr_value = 0, 0, 0, 0, 0, 0
	for i = read_start, read_end do
		cur_attr_id = cur_cfg and cur_cfg[type_key_str .. i] or 0
		cur_attr_value = cur_cfg and cur_cfg[value_key_str .. i] or 0
		next_attr_id = next_cfg and next_cfg[type_key_str .. i] or 0
		next_attr_value = next_cfg and next_cfg[value_key_str .. i] or 0
		attr_id = cur_attr_id > 0 and cur_attr_id or next_attr_id
		attr_value = cur_attr_value > 0 and cur_attr_value or next_attr_value
		if attr_id > 0 and attr_value > 0 then
			local data = {}
			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			data.attr_str = attr_str
			data.attr_value = cur_attr_value
			data.attr_next_value = next_attr_value
			data.add_value = next_attr_value - cur_attr_value
			data.add_value = data.add_value > 0 and data.add_value or 0
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) and not not_sort then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

-- 获得排序好的属性列表(自己组装add_cfg)
function EquipWGData.GetSortAttrListHaveAddByCfg(cur_cfg, add_cfg)
	local attr_list = {}
	local cur_temp_list = AttributeMgr.GetAttributteValueByClass(cur_cfg)
	local next_temp_list = AttributeMgr.GetAttributteValueByClass(add_cfg)

	local cur_no_data = IsEmptyTable(cur_temp_list)
	local temp_list = cur_no_data and next_temp_list or cur_temp_list
	for k, v in pairs(temp_list) do
		local data = {}
		data.attr_str = k
		data.attr_value = cur_no_data and 0 or v
		data.attr_next_value = next_temp_list[k] or 0
		data.add_value = data.attr_next_value
		data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
		table.insert(attr_list, data)
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

-- 获得排序好的属性列表(属性id字符串， 属性value字符串, 属性数量读取开始索引， 属性数量读取结束索引)
function EquipWGData.GetSortAttrListByTypeCfg(cfg, type_key_str, value_key_str, read_start, read_end)
	read_start = read_start or 1
	read_end = read_end or 4

	local attr_list = {}
	local attr_str
	for i = read_start, read_end do
		local attr_id = cfg[type_key_str .. i]
		local attr_value = cfg[value_key_str .. i]
		if attr_id and attr_value and attr_value > 0 then
			local data = {}
			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			data.attr_str = attr_str
			data.attr_value = attr_value
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

--- 转换一下属性列表
function EquipWGData.GetConvertAttrList(attr_list)
	local new_attr_list = {}
	for _, data in pairs(attr_list) do
		if data.attr_str and new_attr_list[data.attr_str] then
			new_attr_list[data.attr_str] = new_attr_list[data.attr_str] + data.attr_value
		else
			new_attr_list[data.attr_str] = data.attr_value
		end
	end

	return new_attr_list
end

-- 合并属性列表
-- 之前的版本会排除掉attr_list1中没有的属性
-- 现在任意一个list有都会算
function EquipWGData.MergeAttrList(attr_list1,attr_list2)
	local new_attr_list = {}

	for _, data_1 in pairs(attr_list1) do
		local data = {}
		data.attr_str = data_1.attr_str
		data.attr_value = (data_1.attr_value or 0) 
		data.attr_next_value = (data_1.attr_next_value or 0) 
		data.add_value = (data_1.add_value or 0)
		data.attr_sort = data_1.attr_sort
		table.insert(new_attr_list, data)
	end
	for _, data_2 in pairs(attr_list2) do
		local have_attr = false
		for index, data_new in pairs(new_attr_list) do
			if data_new.attr_str == data_2.attr_str then
				data_new.attr_str = data_new.attr_str
				data_new.attr_value = (data_new.attr_value or 0) + (data_2.attr_value or 0)
				data_new.attr_next_value = (data_new.attr_next_value or 0) + (data_2.attr_next_value or 0)
				data_new.add_value = (data_new.add_value or 0) + (data_2.add_value or 0)
				data_new.attr_sort = data_new.attr_sort
				new_attr_list[index] = data_new
				have_attr = true
				break
			end
		end
		if not have_attr then
			local data = {}
			data.attr_str = data_2.attr_str
			data.attr_value = (data_2.attr_value or 0) 
			data.attr_next_value = (data_2.attr_next_value or 0) 
			data.add_value = (data_2.add_value or 0)
			data.attr_sort = data_2.attr_sort
			table.insert(new_attr_list, data)
		end
	end

	return new_attr_list

end

--检查装备是否能穿戴
function EquipWGData:GetEquipCanWear(item_id, ignore_prof_level)
	if item_id == nil or item_id == 0 then
		return false
	end

	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil or item_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return false
	end

	local equip_index = self:GetEquipIndexByType(item_cfg.sub_type)
	if equip_index >= GameEnum.EQUIP_INDEX_HUNJIE then
		return false
	end

	-- 需要转职
	-- local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	-- local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
	-- local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	-- local equip_body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(equip_body_seq)

	-- if not IsEmptyTable(equip_body_cfg) then
	-- 	if equip_body_cfg.need_zhuanzhi > role_zhuan then
	-- 		return false
	-- 	end
	-- end

	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local prof_meet = item_cfg.limit_prof == prof or item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT
	local sex_meet = item_cfg.limit_sex == sex or item_cfg.limit_sex == GameEnum.SEX_NOLIMIT
	if not (prof_meet and sex_meet) then
		return false
	end

	if not ignore_prof_level then
		if role_level < ItemWGData.Instance:GetEquipLimitLevel(item_cfg) then
			return false
		end
	end

	if item_cfg.is_limit_zhuanzhi_prof and item_cfg.is_limit_zhuanzhi_prof == 1
		and not ignore_prof_level then
		local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_index, item_cfg.order)
		local zhuanzhi_num = RoleWGData.Instance:GetZhuanZhiNumber()
		if mix_limit_prof > 0 and zhuanzhi_num < mix_limit_prof then
			return false
		end
	end

	return true
end

--检查是否有比该装备更好的装备    背包中一个装备与所有角色身上的装备比较
function EquipWGData:GetIsBetterEquip(item_data, ignore_prof_level)
	if item_data == nil then
		return false, 0
	end

	local can_wear = self:GetEquipCanWear(item_data.item_id, ignore_prof_level)
	if not can_wear then
		return false, 0
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	local equip_index = self:GetEquipIndexByType(item_cfg.sub_type)

	-- 装备肉身 equip_index
	if item_cfg.order then
		local equip_body_seq = EquipBodyWGData.Instance:GetEquipOrderToEquipBodySeq(item_cfg.order)

		if equip_body_seq then
			equip_index = equip_index + equip_body_seq * 15
		end
	end

	local grid_data = self.grid_data_list[equip_index]
	if grid_data ~= nil and grid_data.item_id > 0 then
		local grid_item_cfg = ItemWGData.Instance:GetItemConfig(grid_data.item_id)
		if (grid_item_cfg.order >= 7 and grid_item_cfg.color >= GameEnum.ITEM_COLOR_RED)
		and item_cfg.color <= GameEnum.ITEM_COLOR_ORANGE then
			return false, 0
		end

		local item_data_capability = EquipmentWGData.Instance:GetEquipPingfen(item_data)
		local grid_data_capability = EquipmentWGData.Instance:GetEquipPingfen(grid_data)
		local sub = item_data_capability - grid_data_capability
		return sub > 0, sub
	else
		return true, 0
	end
	return false, 0
end

function EquipWGData:GetJinjieItemScore(item_data)
	if item_data == nil then
		return 0
	end
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	return AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(item_cfg))
end

-- 武器类型
function EquipWGData.IsWQType(equip_type)
	return GameEnum.EQUIP_TYPE_WUQI == equip_type
end

-- 饰品类型
function EquipWGData.IsSPType(equip_type)
	return GameEnum.EQUIP_TYPE_XIANZHUI == equip_type
	or GameEnum.EQUIP_TYPE_XIANFU == equip_type
	or GameEnum.EQUIP_TYPE_XIANJIE == equip_type
	or GameEnum.EQUIP_TYPE_XIANZHUO == equip_type
end

-- 护甲类型
function EquipWGData.IsHJType(equip_type)
	return GameEnum.EQUIP_TYPE_TOUKUI == equip_type
	or GameEnum.EQUIP_TYPE_YIFU == equip_type
end

-- 精灵类型
function EquipWGData.IsJLType(equip_type)
	return GameEnum.EQUIP_TYPE_JINGLING == equip_type
end

-- 宝物类型
function EquipWGData.IsTreasureType(equip_type)
	return GameEnum.EQUIP_TYPE_HUFU == equip_type
	or GameEnum.EQUIP_TYPE_MIANJIA == equip_type
	or GameEnum.EQUIP_TYPE_XIANGLIAN_1 == equip_type
	or GameEnum.EQUIP_TYPE_DIAOZHUI == equip_type
end

function EquipWGData.GetEquipProfLimit(equip_part, equip_order)
	local _t = EquipWGData.Instance.equip_forge_cfg_part_order[equip_part]
	if nil ~= _t then
		local __t = _t[equip_order]
		if nil ~= __t then
			return __t.role_need_min_prof_level
		end
	end

	return 0
end

function EquipWGData:GetEquipForgeCfg(part)
	return self.equip_forge_cfg_part_order[part]
end

function EquipWGData:GetRoleCanEquipMaxOrder()
	local level = RoleWGData.Instance:GetRoleLevel()
	local zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	local cfg = self.equip_forge_cfg_part_order[0]
	for i=1,#cfg do
		if cfg[i].role_need_min_level > level or cfg[i].role_need_min_prof_level > zhuan then
			if i == 1 then
				return cfg[1]
			else
				return cfg[i-1]
			end
		end
	end
end

--检测是否是宝物
function EquipWGData.CheckIsTreasure(equip_type)
	if equip_type == GameEnum.EQUIP_TYPE_HUFU or
		equip_type == GameEnum.EQUIP_TYPE_MIANJIA or
		equip_type == GameEnum.EQUIP_TYPE_XIANGLIAN_1 or
		equip_type == GameEnum.EQUIP_TYPE_DIAOZHUI then
		return true
	end
	return false
end

function EquipWGData.GetEquipBigType(equip_type)
	return EquipWGData.Instance.equip_big_type_cfg and EquipWGData.Instance.equip_big_type_cfg[equip_type] and EquipWGData.Instance.equip_big_type_cfg[equip_type].big_type or nil
end

local legend_cfg_need_key_list = {"color", "big_type", "attr_type", "desc", "is_per", "is_star_attr"}
--仙品属性 need_add_baptize 是否加上洗炼加成
function EquipWGData.GetLegendAttr(item_data, need_add_baptize)
	local legend_attr_list = {}
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	local big_type = EquipWGData.GetEquipBigType(item_cfg.sub_type)
	if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and item_data.param and item_data.param.xianpin_type_list and big_type then
		local xianpin_list = item_data.param.xianpin_type_list
		for i = 1, #xianpin_list do
			local cfg_data = EquipmentWGData.Instance:GetEquipRandAttrByType(item_cfg.color, big_type, tonumber(xianpin_list[i]))
			local addition = 10000
			local quality = 0
			if need_add_baptize and item_data.param.baptize_list then
				addition = item_data.param.baptize_list[i - 1] or 0
				addition = addition == 0 and 10000 or addition
				quality = EquipmentWGData.Instance:GetEquipBaptizeQualityByAddition(addition)
			end

			if cfg_data then
				local tab = {}
				for k,v in ipairs(legend_cfg_need_key_list) do
					tab[v] = cfg_data[v]
				end
				
				local attr_value = cfg_data["attr_val_" .. item_cfg.order] or 0
				tab.orgin_value = attr_value
				tab.value = attr_value * (addition * 0.0001)
				tab.baptize_quality = quality
				tab.sort_index = cfg_data.Attributes_sorting or 999
				table.insert(legend_attr_list, tab)
			end
		end
	end

	if not IsEmptyTable(legend_attr_list) then
		table.sort(legend_attr_list, SortTools.KeyLowerSorter("sort_index"))
	end

	return legend_attr_list
end

-- 预览仙品属性
function EquipWGData.GetPreviewLegendAttr(item_data, star_level)
	local legend_attr_list = {}
	if not item_data or not item_data.item_id then
		return legend_attr_list
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)

	if not item_cfg then
		return legend_attr_list
	end

	local big_type = EquipWGData.GetEquipBigType(item_cfg.sub_type)
	-- 所有可能获得的属性
	local xianpin_list = EquipmentWGData.Instance:GetEquipRandAttrListByType(item_cfg.color, big_type)
	if IsEmptyTable(xianpin_list) then
		return legend_attr_list
	end

	local star_limit_list = {}
	for k, v in pairs(xianpin_list) do
		local star = v.star_level_limit
		if v.attr_weight > 0 and star_level >= star then
			local tab = __TableCopy(v)
			local cfg_value = v["attr_val_" .. item_cfg.order]
			if cfg_value and cfg_value > 0 then
				tab.value = cfg_value
				tab.orgin_value = tab.value
				table.insert(legend_attr_list, tab)

				for i = 1, GameEnum.MAX_XIANPIN_COUNT do
					if i >= star then
						if not star_limit_list[i] then
							star_limit_list[i] = 1
						else
							star_limit_list[i] = star_limit_list[i] + 1
						end
					end
				end

			end
		end
	end

	-- 推荐、 必出区分
	for k, v in pairs(legend_attr_list) do
		local num = star_limit_list[v.star_level_limit] or 0
		if star_level >= v.star_level_limit and star_level >= num then
			v.must_get = true
		else
			v.must_get = false
		end
	end

	if not IsEmptyTable(legend_attr_list) then
		table.sort(legend_attr_list, SortTools.KeyLowerSorter("Attributes_sorting"))
	end

	return legend_attr_list
end

--情缘装备战力对比
function EquipWGData:GetIsBetterQingYuanEquip(item_data)
	if item_data == nil then return false end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if item_cfg == nil then
		return false
	end

	local empty_cell_num = -1
	local qingyuan_list = MarryWGData.Instance:GetAllRingInfo()

	for k,v in pairs(qingyuan_list) do
		if v.item_id > 0 then
			-- local qingyuan_item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			local surplus_id = math.abs(item_cfg.id - v.item_id)  --情缘装备都一样，通过id段区分不同
			if surplus_id <= 9 then
				empty_cell_num = empty_cell_num - 1
				if ItemWGData.Instance:GetItemScore(item_data) > ItemWGData.Instance:GetItemScore(v) then
					return true
				end
			end
		end
	end

	if empty_cell_num > 0 then	-- 有对应的空格子
		return true
	else
		return false
	end
end

--绑定数据改变时的回调方法.用于小鬼有更新时进行回调
function EquipWGData:NotifyXiaoGuiChangeCallBack(callback)
	self:UnNotifyXiaoGuiChangeCallBack(callback)
	self.notify_xiaogui_change_callback_list[#self.notify_xiaogui_change_callback_list + 1] = callback
end

--移除绑定回调
function EquipWGData:UnNotifyXiaoGuiChangeCallBack(callback)
	for k,v in pairs(self.notify_xiaogui_change_callback_list) do
		if v == callback then
			self.notify_xiaogui_change_callback_list[k] = nil
			return
		end
	end
end

function EquipWGData:SetImpGuardInfo(protocol)
	-- print_error("设置小鬼信息",protocol.item_wrapper)
	self.imp_guard_info.used_imp_type_1 = protocol.used_imp_type_1 or 0
	self.imp_guard_info.used_imp_type_2 = protocol.used_imp_type_2 or 0
	self.imp_guard_info.item_wrapper = protocol.item_wrapper

	for k,v in pairs(self.notify_xiaogui_change_callback_list) do  --物品有变化，通知观察者，不带消息体
		v()
	end
	MainuiWGCtrl.Instance:SetXiaoGuiInfoFlag(1)
	MainuiWGCtrl.Instance:InsertNeedOpenView(NEED_OPEN_TIPS_TYPE.GURAD_INVAILD_TYPE,false,BindTool.Bind(self.NeedOpenView,self))
	-- MainuiWGCtrl.Instance:UpdateImpguardIconByOutside()
	MainuiWGCtrl.Instance:CheckLoginFirstOpenView()
	self:CheckCacheXiaoGuiCompseUse()
end

-- 获取小鬼
function EquipWGData:GetmpGuardInfo()
	return self.imp_guard_info
end

function EquipWGData:GetmpGuardInfoByid(item_id)
	local imp_guard_info = self.imp_guard_info
	if not imp_guard_info or not imp_guard_info.item_wrapper then return end

	for k,v in pairs(imp_guard_info.item_wrapper) do
		if v.item_id == item_id then
			return v
		end
	end
end

-- 获取小鬼
function EquipWGData:CanAutoPick(is_pb_shouhu)
	if is_pb_shouhu then
		return false
	end

	local other_cfg = self:GetImpguardOtherConfig()
	for i=1, 2 do
		local data = self.imp_guard_info.item_wrapper[i]
		if data and data.item_id then
			if other_cfg ~= nil and other_cfg.free_imp_item_id ~= "" and other_cfg.free_imp_item_id == data.item_id then
				if other_cfg.is_auto_pick == 1 then
					return true
				end
			else
				local cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(data.item_id)
				if cfg and cfg.is_auto_pick == 1 then
					return true
				end
			end
		end
	end
	return false
end

--优先从小鬼列表中选出经验小鬼,颜色第二
function EquipWGData:GetJingYanBaoBao(tmp_list)
	if IsEmptyTable(tmp_list) then return nil end
	local tmp_guard = nil
	local is_jingyan = false
	for k,v in ipairs(tmp_list) do 				--拿经验优先
		if v.item_id > 0 then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
			if cfg then
				if cfg.impguard_type == 1 then
					is_jingyan = true
					tmp_guard = v
					break
				end
				if is_jingyan == false then
					local item_config = ItemWGData.Instance:GetItemConfig(v.item_id)
					if tmp_guard ~= nil then
						local cfg = ItemWGData.Instance:GetItemConfig(tmp_guard.item_id)
						if cfg.color < item_config.color then
							tmp_guard = v
						end
					else
						tmp_guard = v
					end
				end
			end
		end
	end

	return tmp_guard
end

--从小鬼列表中经验宝宝
function EquipWGData:GetJingYanBaoBaoByXiaoGuiList(tmp_list)
	if IsEmptyTable(tmp_list) then return nil end
	local tmp_guard = nil
	local is_jingyan = false
	for k,v in ipairs(tmp_list) do 				--拿经验优先
		if v.item_id > 0 then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
			if cfg then
				if cfg.impguard_type == 1 then
					is_jingyan = true
					tmp_guard = v
				end
			end
		end
	end

	return tmp_guard
end

function EquipWGData:GetTwoBestGuard()
	local best1 = nil
	local best2 = nil
	local all_list = self:GetAllGuardList()
	local color1 = -99999
	local color2 = -99999
	for k,v in pairs(all_list) do
	 	local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
		if cfg then
			local item_config = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_config then
				if cfg.impguard_type == 1 then
					if color1 < item_config.color then
						color1 = item_config.color
						best1 = v
					end
				end
				if cfg.impguard_type == 2 then
					if color2 < item_config.color then
						color2 = item_config.color
						best2 = v
					end
				end
			end
		end
	end

	local tmp_table = {}
	table.insert(tmp_table,best2)
	table.insert(tmp_table,best1)

	return tmp_table
end

function EquipWGData:GetAllGuardList()
	local bag_list = self:GetBagGuardList()
	local equip_list = self:GetmpGuardInfo().item_wrapper
	-- local tmp_table = __TableCopy(equip_list)
	local tmp_table = {}
	for k, v in pairs(equip_list) do
		table.insert(tmp_table,v)
	end
	for k,v in pairs(bag_list) do
		table.insert(tmp_table,v)
	end
	return tmp_table
end

function EquipWGData:UpdateCheckGuard()
	local list = self:GetTwoBestGuard()
	if IsEmptyTable(list) then return end
	local all_list = self:GetAllGuardList()
	local guard = self:GetJingYanBaoBao(self:FindGuoQiList(all_list))

	local has_infinite_xiaogui = self:GetHasInfiniteXiaoGui()
	if guard and not has_infinite_xiaogui then
		local cfg = EquipmentWGData.GetXiaoGuiCfg(guard.item_id)
		local value = self:CheckGuardTime(guard)
		if cfg then
			if self.old_target_guard ~= guard and not self:GetIsOpenBig(cfg.impguard_type) then --
				local open_data = MainuiWGData.Instance:GetNeedOpenView(NEED_OPEN_TIPS_TYPE.GURAD_INVAILD_TYPE)
				local open_flag = open_data and open_data.first_flag or false
				if value == GuardTimeType.GuoQi and not open_flag then --and not self.is_guoqi
					MainuiWGCtrl.Instance:InsertNeedOpenView(NEED_OPEN_TIPS_TYPE.GURAD_INVAILD_TYPE,true,BindTool.Bind(self.NeedOpenView,self))
					self.old_target_guard = guard
					self:SetIsOpenBig(true,cfg.impguard_type)
					return
				end
			end
		end
	end
end

function EquipWGData:NeedOpenView()
	local all_list = self:GetAllGuardList()
	local guard = self:GetJingYanBaoBao(self:FindGuoQiList(all_list))
	if guard then
		MainuiWGCtrl.Instance:OpenGuradInvalidTimeView(guard)
	end
end

function EquipWGData:FindGuoQiList(list)
	local tmp_list = {}
	for k,v in pairs(list) do
		if v.item_id == QIXIAN_XIAOGUI and v.invalid_time - TimeWGCtrl.Instance:GetServerTime() < 0 then
			table.insert(tmp_list,v)
		end
	end
	return tmp_list
end

--获取是否有期限的小鬼
function EquipWGData:GetHasInfiniteXiaoGui()
	local has_xiaogui = false
	local list = self:GetAllGuardList()

	if not list or IsEmptyTable(list) then
		return has_xiaogui
	end

	for k,v in pairs(list) do
		local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
		if cfg and cfg.impguard_type == QIXIAN_XIAOGUI_TYPE and v.item_id ~= QIXIAN_XIAOGUI then
			has_xiaogui = true
			return has_xiaogui
		end
	end

	return has_xiaogui
end

function EquipWGData:SetIsOpenBig(bo,guard_type)
	if not self.open_big_list then
		self.open_big_list = {}
	end
	self.open_big_list[guard_type] = bo
end

function EquipWGData:GetIsOpenBig(guard_type)
	local has_xiaogui = 0
	local imp_guard_info = self.imp_guard_info
	if imp_guard_info and imp_guard_info.item_wrapper then
		for i,v in ipairs(imp_guard_info.item_wrapper) do
			if v.item_id > 0 and v.item_id ~= QIXIAN_XIAOGUI then
				local xiaogiu_cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
				if xiaogiu_cfg and xiaogiu_cfg.impguard_type == guard_type then
					has_xiaogui = has_xiaogui + 1
					break
				end
			end
		end
	end
	if has_xiaogui > 0 then
		return true
	end

	local data_list = ItemWGData.Instance:GetXiaoGuiList(guard_type)

	for k,v in pairs(data_list) do
		if v.item_id > 0 and v.item_id ~= QIXIAN_XIAOGUI then
			has_xiaogui = has_xiaogui + 1
			break
		end
	end
	return has_xiaogui > 0
end

--获取背包中小鬼列表
function EquipWGData:GetBagGuardList()
	local data_list = ItemWGData.Instance:GetBagItemDataList()
	local baobao_list = {}
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for k,v in pairs(data_list) do
		local item_config = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_config and item_config.sub_type == GameEnum.EQUIP_TYPE_XIAOGUI then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
		 	if cfg then
			 	table.insert(baobao_list,v)
		 	end
		end
	end
	return baobao_list
end

--获取背包中小鬼列表不包括过期
function EquipWGData:GetBagGuardListTwo()
	local data_list = ItemWGData.Instance:GetBagItemDataList()
	local baobao_list = {}
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for k,v in pairs(data_list) do
		local item_config = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_config and item_config.sub_type == GameEnum.EQUIP_TYPE_XIAOGUI then
			local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
		 	if cfg then
		 		if tonumber(v.item_id) == 10101 and v.invalid_time <= server_time then
			 		--table.insert(baobao_list,v)
			 	else
			 		table.insert(baobao_list,v)
			 	end
		 	end
		end
	end
	return baobao_list
end

--0=过期 ，1=即将过期
function EquipWGData:CheckGuardTime(guard_data)
	if guard_data == nil then
		return
	end

	local guard_time = guard_data.invalid_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	server_time = math.floor(server_time)

	if guard_time < server_time then
 		return GuardTimeType.GuoQi
 	end

 	return GuardTimeType.JiJiangGuoQi
end

-- 装备总星级
function EquipWGData:GetEquipTotalStar(equip_body_seq)
	local star_level = 0

	local start_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    local end_index = start_index + GameEnum.EQUIP_INDEX_XIANZHUO

	for k, v in pairs(self.grid_data_list) do
		if v.index >= start_index and v.index <= end_index and v.param and v.param.star_level > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			local equip_seq = EquipBodyWGData.Instance:GetEquipOrderToEquipBodySeq(item_cfg.order)

			if equip_body_seq == equip_seq then
				star_level = star_level + v.param.star_level
			end
		end
	end

	return star_level
end

function EquipWGData:SetEquipStarActiveLevel(active_total_star_list)
	self.active_total_star_list = active_total_star_list
end

function EquipWGData:GetEquipStarActiveLevel(equip_body_seq)
	return self.active_total_star_list[equip_body_seq] or 0
end

function EquipWGData:GetEquipStarActiveLevelData()
	return self.active_total_star_list
end

function EquipWGData:GetStarActiveLevelChange()
	local active_level = self:GetEquipStarActiveLevelData()

	if nil == self.old_equipment_star_active_level then
		self.old_equipment_star_active_level = active_level
		return false
	end

	local level_change = false
	if active_level then
        for k, v in pairs(active_level) do
            local old_lv = self.old_equipment_star_active_level[k]

            if old_lv and old_lv < v then
                level_change = true 
            end
        end
    end

	self.old_equipment_star_active_level = active_level

	return level_change
end

function EquipWGData:GetStaActiveLevelRemind()
	--[[
    local star_total_level = self:GetEquipTotalStar()
    local active_level = self:GetEquipStarActiveLevel()
    local total_cfg, next_total_cfg = EquipmentWGData.Instance:GetEquipTotalStarCfg(active_level)
    local next_total_level = next_total_cfg and next_total_cfg.total_star_level or 0
    if next_total_cfg and next_total_level <= star_total_level and active_level <= next_total_level then
    	local open_func = function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Bag, TabIndex.rolebag_bag_all)
			RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.UP_STAR_TIP)
			RoleWGCtrl.Instance:OpenEquipAttr(ROLE_EQUIP_ATTR_TIPS.UP_STAR_TIP)
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STAR_ATTR_ACTIVE , 1,open_func)
        return 1
    end
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STAR_ATTR_ACTIVE , 0)
	]]
    return 0
end

function EquipWGData:GetImpguardRemindEnable()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if self.imp_guard_info and self.imp_guard_info["used_imp_type_2"] < 0 and
		role_level >= self.impguard_other.open_level and
		RoleWGData.Instance:GetIsEnoughAllGold(self.impguard_other.open_cost) then
		return true
	end

	return false
end

function EquipWGData:GetImpguardOtherConfig()
	return self.impguard_other
end

function EquipWGData:CheckCacheXiaoGuiCompseUse()
	if self.cache_quick_xiaogui_use then
		for k,v in pairs(self.cache_quick_xiaogui_use) do
			self:NeedShowXiaoGuiQuickUse(v,k)
		end
		self.cache_quick_xiaogui_use = nil
	end
end

function EquipWGData:CheckIsXiaoGuiComposeItem(item_id)
	if not self.xiaogui_compose_stuff1 then
		local tab = Split(self.impguard_other.stuff_count1,"|")
		self.xiaogui_compose_stuff1 = {}
		for k,v in pairs(tab) do
			self.xiaogui_compose_stuff1[tonumber(v)] = true
		end
	end

	if not self.xiaogui_compose_stuff2 then
		local tab = Split(self.impguard_other.stuff_count2,"|")
		self.xiaogui_compose_stuff2 = {}
		for k,v in pairs(tab) do
			self.xiaogui_compose_stuff2[tonumber(v)] = true
		end
	end

	if self.xiaogui_compose_stuff1[item_id] then return true end

	if self.xiaogui_compose_stuff2[item_id] then return true end

	return false
end

function EquipWGData:NeedShowXiaoGuiQuickUse(item_id,index)
	if not self.impguard_other then return true end

	if IsEmptyTable(self.imp_guard_info.item_wrapper) then
		if not self.cache_quick_xiaogui_use then
			self.cache_quick_xiaogui_use = {}
		end
		self.cache_quick_xiaogui_use[index] = item_id
		return
	end
	if not self.xiaogui_compose_stuff1 then
		local tab = Split(self.impguard_other.stuff_count1,"|")
		self.xiaogui_compose_stuff1 = {}
		for k,v in pairs(tab) do
			self.xiaogui_compose_stuff1[tonumber(v)] = true
		end

	end

	if not self.xiaogui_compose_stuff2 then
		local tab = Split(self.impguard_other.stuff_count2,"|")
		self.xiaogui_compose_stuff2 = {}
		for k,v in pairs(tab) do
			self.xiaogui_compose_stuff2[tonumber(v)] = true
		end
	end

	local impguard_type = 0
	if self.xiaogui_compose_stuff1[item_id] then
		impguard_type = 1
		if self:NeedShowXiaoGuiQuickUseByType(impguard_type) then
			FunctionGuide.Instance:OpenKeyUseView(item_id,index)
			return true
		end
	end

	if self.xiaogui_compose_stuff2[item_id] then
		impguard_type = 2
		if self:NeedShowXiaoGuiQuickUseByType(impguard_type) then
			FunctionGuide.Instance:OpenKeyUseView(item_id,index)
			return true
		end
	end
	return false
end

function EquipWGData:NeedShowXiaoGuiQuickUseByType(impguard_type)
	local bag_hight_data = EquipmentWGData.Instance:GetHighestColorImp(impguard_type)
	if not IsEmptyTable(bag_hight_data) then
		local bag_hight_cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(bag_hight_data.item_id)

		if not bag_hight_cfg then
			return false
		end

		if bag_hight_cfg.product_id == 0 then
			return false
		end
	end

	if IsEmptyTable(self.imp_guard_info) then
		return true
	end

	local equip_1_cfg = {}
	local equip_2_cfg = {}

	if self.imp_guard_info.item_wrapper[1] then
		equip_1_cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(self.imp_guard_info.item_wrapper[1].item_id)
	end

	if self.imp_guard_info.item_wrapper[2] then
		equip_2_cfg = EquipmentWGData.Instance:GetGuardCfgByItemID(self.imp_guard_info.item_wrapper[2].item_id)
	end

	if not IsEmptyTable(equip_1_cfg) and equip_1_cfg.impguard_type == impguard_type then
		if equip_1_cfg.product_id == 0 then
			return false
		end
	end

	if not IsEmptyTable(equip_2_cfg) and equip_2_cfg.impguard_type == impguard_type then
		if equip_2_cfg.product_id == 0 then
			return false
		end
	end
	return true
end

function EquipWGData:GetEquipBaseAttrTab() --基础10件装备的基础属性
	local base_equip_attr_tab = {
		maxhp = 0,
		gongji = 0,
		fangyu = 0,
		pojia = 0,
	}

	if not IsEmptyTable(self.grid_data_list) then
		for k, v in pairs(self.grid_data_list) do
			if v.item_id > 0 then
				local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(v.index)
				local equip_body_unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(equip_body_seq)

				if equip_body_unlock and equip_part >= GameEnum.EQUIP_INDEX_TOUKUI and equip_part <= GameEnum.EQUIP_INDEX_XIANZHUO then
					local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

					if not IsEmptyTable(item_cfg) then
						base_equip_attr_tab["maxhp"] = base_equip_attr_tab["maxhp"] + ( item_cfg.hp or 0)
						base_equip_attr_tab["gongji"] = base_equip_attr_tab["gongji"] + (item_cfg.attack or 0)
						base_equip_attr_tab["fangyu"] = base_equip_attr_tab["fangyu"] + (item_cfg.fangyu or 0)
						base_equip_attr_tab["pojia"] = base_equip_attr_tab["pojia"] + (item_cfg.pojia or 0)
					end
				end
			end
		end
	end

	-- for i = 0,9 do
	-- 	if self.grid_data_list and self.grid_data_list[i] then
	-- 		if self.grid_data_list[i].item_id > 0 then
	-- 			local item_cfg = ItemWGData.Instance:GetItemConfig(self.grid_data_list[i].item_id)
	-- 			if not IsEmptyTable(item_cfg) then
	-- 				base_equip_attr_tab["maxhp"] = base_equip_attr_tab["maxhp"] + ( item_cfg.hp or 0)
	-- 				base_equip_attr_tab["gongji"] = base_equip_attr_tab["gongji"] + (item_cfg.attack or 0)
	-- 				base_equip_attr_tab["fangyu"] = base_equip_attr_tab["fangyu"] + (item_cfg.fangyu or 0)
	-- 				base_equip_attr_tab["pojia"] = base_equip_attr_tab["pojia"] + (item_cfg.pojia or 0)
	-- 			end
	-- 		end
	-- 	end
	-- end
	return base_equip_attr_tab
end

function EquipWGData:GetWeaponBaseAttrTab()
	local weapon_base_tab = {
		maxhp = 0,
		gongji = 0,
		fangyu = 0,
		pojia = 0,
	}

	if not IsEmptyTable(self.grid_data_list) then
		for k, v in pairs(self.grid_data_list) do
			if v.item_id > 0 then
				local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(v.index)
				local equip_body_unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(equip_body_seq)
				
				if equip_body_unlock and (equip_part == GameEnum.EQUIP_INDEX_WUQI or equip_part == GameEnum.EQUIP_INDEX_XIANFU) then
					local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

					if not IsEmptyTable(item_cfg) then
						weapon_base_tab["maxhp"] = weapon_base_tab["maxhp"] + (item_cfg.hp or 0)
						weapon_base_tab["gongji"] = weapon_base_tab["gongji"] + (item_cfg.attack or 0)
						weapon_base_tab["fangyu"] = weapon_base_tab["fangyu"] + (item_cfg.fangyu or 0)
						weapon_base_tab["pojia"] = weapon_base_tab["pojia"] + (item_cfg.pojia or 0)
					end
				end
			end
		end
	end

	-- if self.grid_data_list[GameEnum.EQUIP_INDEX_WUQI] and self.grid_data_list[GameEnum.EQUIP_INDEX_WUQI].item_id > 0 then
	-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(self.grid_data_list[GameEnum.EQUIP_INDEX_WUQI].item_id)
	-- 	if not IsEmptyTable(item_cfg) then
	-- 		weapon_base_tab["maxhp"] = weapon_base_tab["maxhp"] + (item_cfg.hp or 0)
	-- 		weapon_base_tab["gongji"] = weapon_base_tab["gongji"] + (item_cfg.attack or 0)
	-- 		weapon_base_tab["fangyu"] = weapon_base_tab["fangyu"] + (item_cfg.fangyu or 0)
	-- 		weapon_base_tab["pojia"] = weapon_base_tab["pojia"] + (item_cfg.pojia or 0)
	-- 	end
	-- end

	-- if self.grid_data_list[GameEnum.EQUIP_INDEX_XIANFU] and self.grid_data_list[GameEnum.EQUIP_INDEX_XIANFU].item_id > 0 then
	-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(self.grid_data_list[GameEnum.EQUIP_INDEX_XIANFU].item_id)
	-- 	if not IsEmptyTable(item_cfg) then
	-- 		weapon_base_tab["maxhp"] = weapon_base_tab["maxhp"] + (item_cfg.hp or 0)
	-- 		weapon_base_tab["gongji"] = weapon_base_tab["gongji"] + (item_cfg.attack or 0)
	-- 		weapon_base_tab["fangyu"] = weapon_base_tab["fangyu"] + (item_cfg.fangyu or 0)
	-- 		weapon_base_tab["pojia"] = weapon_base_tab["pojia"] + (item_cfg.pojia or 0)
	-- 	end
	-- end
	return weapon_base_tab
end

function EquipWGData:GetShiPinBaseAttrTab()
	local shipin_base_tab = {
		maxhp = 0,
		gongji = 0,
		fangyu = 0,
		pojia = 0,
	}

	if not IsEmptyTable(self.grid_data_list) then
		for k, v in pairs(self.grid_data_list) do
			if v.item_id > 0 then
				local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(v.index)
				local equip_body_unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(equip_body_seq)
				
				if equip_body_unlock and (equip_part == GameEnum.EQUIP_INDEX_XIANLIAN or equip_part == GameEnum.EQUIP_INDEX_XIANZHUI or equip_part == GameEnum.EQUIP_INDEX_XIANJIE 
					or equip_part == GameEnum.EQUIP_INDEX_XIANZHUO) then
					local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

					if not IsEmptyTable(item_cfg) then
						shipin_base_tab["maxhp"] = shipin_base_tab["maxhp"] + (item_cfg.hp or 0)
						shipin_base_tab["gongji"] = shipin_base_tab["gongji"] + (item_cfg.attack or 0)
						shipin_base_tab["fangyu"] = shipin_base_tab["fangyu"] + (item_cfg.fangyu or 0)
						shipin_base_tab["pojia"] = shipin_base_tab["pojia"] + (item_cfg.pojia or 0)
					end
				end
			end
		end
	end

	return shipin_base_tab
end

function EquipWGData:GetShouHuYouShiTime() --优势时间
	return self.impguard_other.adv_countdown or 5, self.impguard_other.adv_dayrequire or 3

end