Vector3Pool = Vector3Pool or {}
local vec3_auto_die_cache = {}
local vec3_died_cache = {}

function Vector3Pool.Get(_x, _y, _z)
	local t = next(vec3_died_cache)
	if nil ~= t then
		t.x = _x or 0
		t.y = _y or 0
		t.z = _z or 0
		vec3_died_cache[t] = nil
	else
		t = Vector3.zero
	end

	vec3_auto_die_cache[t] = t
	return t
end

function Vector3Pool.Update(now_time, elapse_time)
	for k,v in pairs(vec3_auto_die_cache) do
		vec3_died_cache[k] = v
		vec3_auto_die_cache[k] = nil
	end
end
