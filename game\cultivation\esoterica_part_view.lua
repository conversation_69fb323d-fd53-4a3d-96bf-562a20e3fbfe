EsotericaPartView = EsotericaPartView or BaseClass(SafeBaseView)

local quality_effect = {
    "UI_miji_tips_lv",
    "UI_miji_tips_lan",
    "UI_miji_tips_zi",
    "UI_miji_tips_cheng",
    "UI_miji_tips_hong",
    "UI_miji_tips_fen",
    "UI_miji_tips_jin",
}
function EsotericaPartView:__init()
	self:SetMaskBg(true)
    self:SetMaskBgAlpha(180/255)
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_esoterica_part_view")
end

function EsotericaPartView:SetDataAndOpen(slot, part)
	self.slot = slot
    self.part = part
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function EsotericaPartView:ReleaseCallBack()
    self.star_list = nil
    self.effect_show_color = nil

    if self.star_list then
        for k,v in pairs(self.star_list) do
            v:DeleteMe()
        end
        self.star_list = nil
    end

    if self.star_attr_list then
        for k,v in pairs(self.star_attr_list) do
            v:DeleteMe()
        end
        self.star_attr_list = nil
    end

    if self.esoterica_stuff_item then
        self.esoterica_stuff_item:DeleteMe()
        self.esoterica_stuff_item = nil
    end

    if self.es_part_list then
        for k,v in pairs(self.es_part_list) do
            v:DeleteMe()
        end
        self.es_part_list = nil
    end

    if self.level_attr_list then
        for k,v in pairs(self.level_attr_list) do
            v:DeleteMe()
        end
        self.level_attr_list = nil
    end

    self.lv_list = nil
    self.lv_effect = nil
end

function EsotericaPartView:LoadCallBack()
    self.select_index = 1

    -- 部位
    if not self.es_part_list then
        self.es_part_list = {}
        local parent_node = self.node_list["es_stuff_list"]
        local skill_num = parent_node.transform.childCount
        for i = 0, skill_num - 1 do
            local cell = EsotericaPartRender.New(parent_node:FindObj("stuff_" .. i))
            cell:SetIndex(i)
            self.es_part_list[i] = cell
        end
    end

    -- 星级
    if self.star_list == nil then
		self.star_list = {}
		local star_num = self.node_list.star_list.transform.childCount
		for i = 1, star_num do
			self.star_list[i] = self.node_list["star_" .. i]
		end
	end

    -- 升级属性列表
    if not self.level_attr_list then
        self.level_attr_list = {}
        local parent_node = self.node_list["level_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = EsotericaAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.level_attr_list[i] = cell
        end
    end

    -- 升星属性列表
    if not self.star_attr_list then
        self.star_attr_list = {}
        local parent_node = self.node_list["star_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = EsotericaAttrRender.New(parent_node:FindObj("attr_" .. i))
            --cell:SetAttrNameNeedSpace(true)
            cell:SetIndex(i)
            self.star_attr_list[i] = cell
        end
    end

    if nil == self.esoterica_stuff_item then
        self.esoterica_stuff_item = ItemCell.New(self.node_list.esoterica_stuff_item)
    end

    -- 等级
    if not self.lv_list then
        self.lv_list = {}
        self.lv_effect = {}

        for i = 1, 10 do
            self.lv_list[i] = self.node_list["lv_img_" .. i]
            self.lv_effect[i] = self.node_list["lv_effect_" .. i]
        end
    end

    XUI.AddClickEventListener(self.node_list["btn_upstar"], BindTool.Bind(self.OnClickUpStar, self))
    XUI.AddClickEventListener(self.node_list["btn_uplevel"], BindTool.Bind(self.OnClickUpLevel, self, true))
    XUI.AddClickEventListener(self.node_list.es_level_cost_item, BindTool.Bind(self.OnClickESLevelCostItem, self))

    for i = 1, 2 do
		self.node_list["toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self, i))
	end

    local uplevel_remind = CultivationWGData.Instance:GetEsotericaSlotUplevelRemind(self.slot)
    local jump_index = uplevel_remind and 2 or 1
    self.node_list["toggle_" .. jump_index].toggle.isOn = true
end

function EsotericaPartView:OnClickESLevelCostItem()
	local item_id = ESOTERICA_DEFINE.EXP_ITEM_ID
	TipWGCtrl.Instance:OpenItem({item_id = item_id})
end

function EsotericaPartView:OnClickToggle(index, is_on)
	if self.select_index ~= index and is_on then
		self.select_index = index
		self:Flush()
	end
end

function EsotericaPartView:OnFlush()
    self.node_list.upstar_content:SetActive(self.select_index == 1)
    self.node_list.uplevel_content:SetActive(self.select_index == 2)

    self:FlushCommonContent()
    self:FlushUpLevelContent()
    self:FlushUpStarContent()
end

function EsotericaPartView:FlushCommonContent()
    local slot_cfg = CultivationWGData.Instance:GetEsotericaCfg(self.slot)
    if not slot_cfg then
        return
    end

    local bundle, asset = ResPath.GetRawImagesPNG("a3_xf_rwlh" .. slot_cfg.img_id)
    self.node_list.esoterica_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["esoterica_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetRawImagesPNG("a3_xf_wb" .. slot_cfg.skill_img)
    self.node_list.skill_img.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["skill_img"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetA2Effect(slot_cfg.ui_effect_asset)
    self.node_list.effect:ChangeAsset(bundle, asset)

    local pos_x, pos_y = 0, 0
    if slot_cfg.img_pos and slot_cfg.img_pos ~= "" then
		local pos_list = string.split(slot_cfg.img_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
        RectTransform.SetAnchoredPositionXY(self.node_list.esoterica_img.rect, pos_x, pos_y)
	end

    local scale = slot_cfg.img_scale
    if scale and scale ~= "" then
        Transform.SetLocalScaleXYZ(self.node_list.esoterica_img.transform, scale, scale, scale)
	end

    -- 战力
    self.node_list.cap_value.text.text = CultivationWGData.Instance:GetEsotericaSlotCapability(self.slot)

    local uplevel_remind = CultivationWGData.Instance:GetEsotericaSlotUplevelRemind(self.slot)
    local upstar_remind = CultivationWGData.Instance:GetEsotericaSlotPartRemind(self.slot, self.part)
    self.node_list.uplevel_remind:SetActive(uplevel_remind)
    self.node_list.upstar_remind:SetActive(upstar_remind)
end

function EsotericaPartView:FlushUpLevelContent()
    local info = CultivationWGData.Instance:GetEsotericaSlotInfo(self.slot)
    if not info then
        return
    end

    local level = info.level
    local next_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.slot, level + 1)
    local is_max = not next_level_cfg
    self.node_list.uplevel_max_flag:SetActive(is_max)
    self.node_list.uplevel_no_max:SetActive(not is_max)
    self.node_list.level_desc_max:SetActive(is_max)
    self.node_list.level_desc_no_max:SetActive(not is_max)
    self.node_list.cur_level_desc.text.text = string.format(Language.Esoterica.LevelDesc2, level)

    local item_cfg = ItemWGData.Instance:GetItemConfig(ESOTERICA_DEFINE.EXP_ITEM_ID)
    if item_cfg then
        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list.es_level_cost_item.image:LoadSprite(bundle, asset, function ()
            self.node_list.es_level_cost_item.image:SetNativeSize()
        end)
    end

    local max_level = CultivationWGData.Instance:GetEsotericaMaxLevel(self.slot)
    self.node_list.es_text_level.text.text = string.format(Language.Esoterica.LevelDesc1, level, max_level)

    local last_lv = level % 10
    local last_lv_bai = level / 10

    if last_lv == 0 and last_lv_bai ~= 0 then
        last_lv = 10
    end

    for i, v in ipairs(self.lv_list) do
        local str = i <= last_lv and "a3_hs_huo1" or "a3_hs_huo2"
        local bundle, asset = ResPath.GetCultivationImg(str)
        v.image:LoadSprite(bundle, asset)

        if self.lv_effect[i] then
            self.lv_effect[i]:CustomSetActive(i <= last_lv)
        end
    end

    local cur_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.slot, level)
    if not cur_level_cfg then
        return
    end

    if is_max then
        self.node_list.btn_uplevel_remind:SetActive(false)
        self.node_list.max_level.text.text = level
    else
        self.node_list.cur_level.text.text = level
        self.node_list.next_level.text.text = level + 1

        local cur_exp = CultivationWGData.Instance:GetEsotericaEXP()
        local show_color = cur_exp >= cur_level_cfg.need_exp and COLOR3B.GREEN or COLOR3B.RED
        self.node_list.es_text_exp.text.text = string.format("%d/%d", cur_exp, cur_level_cfg.need_exp)

        self.node_list.es_slider_exp.slider.value = cur_exp / cur_level_cfg.need_exp
        local is_remind = false

        local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
        local stage_meet = cur_stage >= cur_level_cfg.jingjie_level_limit

        local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(cur_level_cfg.jingjie_level_limit)
        if stage_cfg then
            show_color = stage_meet and COLOR3B.GREEN or COLOR3B.RED
            self.node_list.stage_uplevel_need_str.text.text = string.format(Language.Esoterica.SlotUpLevelLimit, show_color, stage_cfg.stage_title)
        end

        self.node_list.btn_uplevel:CustomSetActive(stage_meet)
        self.node_list.stage_uplevel_need_str:CustomSetActive(not stage_meet)

        is_remind = stage_meet and cur_exp >= cur_level_cfg.need_exp
        self.node_list.btn_uplevel_remind:SetActive(is_remind)
    end

    -- 属性
    local level_attr_list = CultivationWGData.Instance:GetEsotericaUpLevelShowAttr(self.slot, level)

    for k,v in ipairs(self.level_attr_list) do
        v:SetData(level_attr_list[k])
        --v:SetRealHideNext(is_max)
    end
end

function EsotericaPartView:FlushUpStarContent()
	self:SetSelectActive()
    if self.es_part_list[self.part] then
        self.es_part_list[self.part]:SetSelectActive(true)
    end

    self:FlushEsotericaPartView()

    local list = CultivationWGData.Instance:GetEsotericaSkillLevelList(self.slot)
    local cur_skill_level = CultivationWGData.Instance:GetEsotericaSlotSkillLevel(self.slot)
    local node_num = self.node_list.star_desc_content.transform.childCount
    for i = 1, node_num do
        local data = list[i]
        local node = self.node_list.star_desc_content:FindObj("star_action_text" .. i)
        if data then
            local color1 = cur_skill_level >= i and COLOR3B.C8 or COLOR3B.C4
            node:SetActive(true)
            node.text.text = string.format(Language.Esoterica.SkillAction, color1, data.need_part_level, data.effect_desc)
        else
            node:SetActive(false)
        end
    end

    local info = CultivationWGData.Instance:GetEsotericaSlotPartInfo(self.slot, self.part)
    if not info then
        return
    end

    local item_id = info.item_id
    local is_inlay = item_id > 0
    --local cap = 0
    -- 预览
    if not is_inlay then
        self.node_list.btn_upstar_text.text.text = Language.Esoterica.UpStar[1]
        item_id = CultivationWGData.Instance:GetEsotericaPartMinActItemid(self.slot, self.part)
    else
        self.node_list.btn_upstar_text.text.text = Language.Esoterica.UpStar[2]
        --cap = CultivationWGData.Instance:GetEsotericaPartCapability(self.slot, self.part)
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if not item_cfg then
        return
    end

    if self.effect_show_color ~= item_cfg.color then
        self.effect_show_color = item_cfg.color
        local eff_bundle, eff_asset = ResPath.GetA2Effect(quality_effect[item_cfg.color])
        self.node_list["color_effect"]:ChangeAsset(eff_bundle, eff_asset)
    end

    self.node_list["name"].text.text = item_cfg.name
    local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
    self.node_list["item_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list["item_icon"].image:SetNativeSize()
    end)

    --self.node_list["cap_value"].text.text = cap

    local cur_part_level_cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(self.slot, self.part, info.level)
	local next_part_level_cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(self.slot, self.part, info.level + 1)
	local star_attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_part_level_cfg, next_part_level_cfg, "attr_id", "attr_value", 1, 5)
    local is_max = not next_part_level_cfg
    for k,v in ipairs(self.star_attr_list) do
        v:SetData(star_attr_list[k])
        --v:SetRealHideNext(is_max)
    end

    local star_level = cur_part_level_cfg and cur_part_level_cfg.show_star or 0
    self.node_list.cur_star_desc.text.text = string.format(Language.Esoterica.StarDesc1, star_level)

    local star_res_list = GetTwenTyStarImgResByStar(star_level)
    for k,v in pairs(self.star_list) do
        v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
    end

    self.node_list.upstar_max_flag:SetActive(is_max)
    self.node_list.upstar_no_max:SetActive(not is_max)
    --self.node_list.star_attr_list.transform.localPosition = is_max and Vector3(44,72,0) or Vector3(0,72,0)
    if not is_max then
        local stuff_item_id = is_inlay and cur_part_level_cfg.cost_item_id or item_id
        local stuff_num = is_inlay and cur_part_level_cfg.cost_item_num or 1

        local had_num = CultivationWGData.Instance:GetEsotericaItemNum(stuff_item_id)
        local is_remind = had_num >= stuff_num
        local show_color = is_remind and COLOR3B.GREEN or COLOR3B.RED

        self.node_list.esoterica_need_num.text.text=string.format("<color=%s>%d</color>/%d", show_color, had_num, stuff_num)
        self.esoterica_stuff_item:SetData({item_id = stuff_item_id})

        self.node_list.btn_upstar_remind:SetActive(is_remind)
    end
end

function EsotericaPartView:OnClickUpStar()
    local info = CultivationWGData.Instance:GetEsotericaSlotPartInfo(self.slot, self.part)
    if not info then
        return
    end

    if info.item_id <= 0 then
        local is_had_better, bag_index = CultivationWGData.Instance:GetEsotericaSlotPartHadBetter(self.slot, self.part)
        if is_had_better then
            CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.WEAR, self.slot, self.part, bag_index)
            return
        end

        local item_id = CultivationWGData.Instance:GetEsotericaPartMinActItemid(self.slot, self.part)
        TipWGCtrl.Instance:OpenItem({item_id = item_id})
        return
    end

    local cur_part_level_cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(self.slot, self.part, info.level)
    local next_part_level_cfg = CultivationWGData.Instance:GetEsotericaPartLevelCfg(self.slot, self.part, info.level + 1)
    if not cur_part_level_cfg or not next_part_level_cfg then
        return
    end

    local had_num = CultivationWGData.Instance:GetEsotericaItemNum(cur_part_level_cfg.cost_item_id)
    if had_num < cur_part_level_cfg.cost_item_num then
        TipWGCtrl.Instance:OpenItem({item_id = cur_part_level_cfg.cost_item_id})
        return
    end

    CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.PART_UPLEVEL, self.slot, self.part)
end

function EsotericaPartView:OnClickUpLevel(is_auto)
    local slot_info = CultivationWGData.Instance:GetEsotericaSlotInfo(self.slot)
    if not slot_info then
        return
    end

    if not slot_info.is_active then
        return
    end

    local cur_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.slot, slot_info.level)
    local next_level_cfg = CultivationWGData.Instance:GetEsotericaLevelCfg(self.slot, slot_info.level + 1)
    if not cur_level_cfg or not next_level_cfg then
        return
    end

    local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
	if cur_stage < cur_level_cfg.jingjie_level_limit then
        local stage_cfg = CultivationWGData.Instance:GetXiuWeiStageCfgByState(cur_level_cfg.jingjie_level_limit)
        if stage_cfg then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Esoterica.SlotUpLevelLimitTips, stage_cfg.stage_title))
        end
		return
	end

    local cur_exp = CultivationWGData.Instance:GetEsotericaEXP()
    if cur_exp < cur_level_cfg.need_exp then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Esoterica.ExpNoEnough)
        return
    end

    CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.UPLEVEL, self.slot, is_auto and 1 or 0)
end

--刷新部位
function EsotericaPartView:FlushEsotericaPartView()
    local part_list = CultivationWGData.Instance:GetEsotericaSlotPartInfoList(self.slot)
    for k,v in pairs(self.es_part_list) do
        v:SetIsNeedLine(false)
        v:SetData(part_list[k])
    end
end


function EsotericaPartView:SetSelectActive()
    if self.es_part_list then
        for key, cell in pairs(self.es_part_list) do
            cell:SetSelectActive(false)
        end
    end
end

EsotericaAttrRender = EsotericaAttrRender or BaseClass(BaseRender)
function EsotericaAttrRender:__init()
    self.real_hide_next = true
end

function EsotericaAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
	local attr_name = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, true)

    self.node_list.attr_name.text.text = attr_name
    self.node_list.attr_value.text.text = string.format("%s%s", value_str, per_desc)

    if self.data.attr_next_value and self.data.attr_next_value > 0 then
        self.node_list.next_img:SetActive(true)
        self.node_list.attr_next_value:SetActive(true)
        local next_value_str = is_per and self.data.attr_next_value / 100 or self.data.attr_next_value
        self.node_list.attr_next_value.text.text = next_value_str .. per_desc
    else
        self.node_list.next_img:SetActive(false)
        self.node_list.attr_next_value:SetActive(false)
    end
end