require("game/activity/activity_main_panel/activity_main_view")
ActivityMainWGCtrl = ActivityMainWGCtrl or BaseClass(BaseWGCtrl)

function ActivityMainWGCtrl:__init()
	if ActivityMainWGCtrl.Instance ~= nil then
		print("[ActivityMainWGCtrl]error:create a singleton twice")
	end
	ActivityMainWGCtrl.Instance = self
	self.view = ActivityMainView.New()	
end

function ActivityMainWGCtrl:__delete()
	ActivityMainWGCtrl.Instance = nil
	if self.view then 
		self.view:DeleteMe()
		self.view = nil
	end
end

function ActivityMainWGCtrl:Open()
	self.view:Open()
end
function ActivityMainWGCtrl:OpenPrefactLoverView()
	PerfectLoverWGCtrl.Instance:Open()
end
