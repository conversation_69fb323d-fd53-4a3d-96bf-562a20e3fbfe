ActorWGCtrl = ActorWGCtrl or BaseClass()
CUR_HURT_SOUND_NUM = 0
MAX_HURT_SOUND_NUM = 20
local TypeProjectile = typeof(Projectile)
local TypeEffectControl = typeof(EffectControl)

local HurtPositionEnum = {
	Root = 0,
	HurtPoint = 1,
}

local HurtRotationEnum = {
	Target = 0,
	HitDirection = 1,
}

function ActorWGCtrl:__init(actor_triggers)
	self.actor_triggers = actor_triggers
end

function ActorWGCtrl:__delete()
	self:StopHurtSound()
	self:StopHitSound()
	self.actor_triggers = nil
	self.is_deleted = true
end

function ActorWGCtrl:StopEffects()
	if self.actor_triggers then
		self.actor_triggers:StopEffects()
		self.actor_triggers = nil
	end
end

function ActorWGCtrl:SetPrefabData(data)
	self.prefab_data = data
end

function ActorWGCtrl:GetPrefabData()
	return self.prefab_data
end

function ActorWGCtrl:PlayProjectile(main_part_obj, action, root, hurt_point, cb, cbdata, offset_pos, key)
	local prefab_data = self:GetPrefabData()
	local find = false
	if prefab_data ~= nil and prefab_data.actorController ~= nil then
		local projectiles = prefab_data.actorController.projectiles
		for k, projectile in pairs(projectiles) do
			if projectile.Action == action and next(projectile.Projectile) ~= nil then
				self:PlayProjectileImpl(main_part_obj, projectile, root, hurt_point, cb, cbdata, key and k..key or k, offset_pos)
				find = true
				break
			end
		end
	end

	if not find then
		if cb then
			cb(cbdata)
		end
	end
end

local function DelayPlayProjectile(cbdata)
	local self = cbdata[1]
	local main_part_obj = cbdata[2]
	local projectile = cbdata[3]
	local hurt_point = cbdata[4]
	local from_position = cbdata[5]
	local cb = cbdata[6]
	local up_cbdata = cbdata[7]
	local key = cbdata[8]
	ActorWGCtrl.ReleaseCBData(cbdata)

	if self.is_deleted then
		return
	end
	self:PlayProjectileWithEffect(main_part_obj, projectile, hurt_point, from_position, cb, up_cbdata, key)
end

function ActorWGCtrl:PlayProjectileImpl(main_part_obj, projectile, root, hurt_point, cb, up_cbdata, key, offset_pos)
	if self.actor_triggers and self.actor_triggers:EnableEffect() and main_part_obj then
		local from_transform = nil
		local from_position = nil

		if projectile.FromPosHierarchyPath ~= nil and projectile.FromPosHierarchyPath ~= "" then
			from_transform = main_part_obj.transform:Find(projectile.FromPosHierarchyPath)
		end
		from_transform = from_transform or main_part_obj.transform
		from_position = from_transform.position
		if offset_pos then
			from_position = u3d.v3Sub(from_position, offset_pos)
		end

		if projectile.DelayProjectileEff <= 0 then
			self:PlayProjectileWithEffect(main_part_obj, projectile, hurt_point, from_position, cb, up_cbdata, key)
		else
			local cbdata = ActorWGCtrl.GetCBData()
			cbdata[1] = self
			cbdata[2] = main_part_obj
			cbdata[3] = projectile
			cbdata[4] = hurt_point
			cbdata[5] = from_position
			cbdata[6] = cb
			cbdata[7] = up_cbdata
			cbdata[8] = key
			GlobalTimerQuest:AddDelayTimer(DelayPlayProjectile, projectile.DelayProjectileEff, cbdata)
		end
	else
		self:PlayProjectileWithoutEffect(cb, up_cbdata)
	end
end

local function LoadProjectileCallBack(obj, cbdata)
	local async_loader = cbdata[1]
	local main_part_obj = cbdata[2]
	local projectile = cbdata[3]
	local hurt_point = cbdata[4]
	local fromPosition = cbdata[5]
	local cb = cbdata[6]
	local up_cbdata = cbdata[7]
	ActorWGCtrl.ReleaseCBData(cbdata)

	if IsNil(obj) then
		return
	end

	if nil == main_part_obj or IsNil(main_part_obj.transform) then
		async_loader:Destroy()
		return
	end

	local instance = obj:GetComponent(TypeProjectile)
	if instance == nil then
		async_loader:Destroy()
		print_warning("lua:PlayProjectileWithEffect not exist Projectile")
		if cb then
			cb(up_cbdata)
		end
		return
	end

	if not IsNil(hurt_point) and hurt_point.transform then
		local direction = fromPosition - hurt_point.transform.position
		direction.y = 0
		if direction ~= Vector3.zero then
			instance.transform:SetPositionAndRotation(fromPosition, Quaternion.LookRotation(direction))
		end
	else
		instance.transform.position = fromPosition
	end

	if IsNil(hurt_point) or IsNil(hurt_point.transform) then
		async_loader:Destroy()
		return
	end

	instance.transform.localScale = main_part_obj.transform.lossyScale

	instance:Play(
		main_part_obj.transform.lossyScale,
		hurt_point.transform,
		main_part_obj.gameObject.layer,
		function ()
			if cb then
				cb(up_cbdata)
			end
		end,
		function ()
			async_loader:SetObjAliveTime(projectile.DeleProjectileDelay)
		end)

	local effect_ctrl = obj:GetComponent(TypeEffectControl)
	if effect_ctrl ~= nil then
		effect_ctrl:WaitFinsh(function()
			async_loader:Destroy()
		end)
	end
end

function ActorWGCtrl:PlayProjectileWithEffect(main_part_obj, projectile, hurt_point, fromPosition, cb, up_cbdata, key)
	local asset_name = projectile.Projectile.AssetName
	local bundle_name = projectile.Projectile.BundleName
	if not asset_name or not bundle_name or asset_name == "" or bundle_name == "" then
		return
	end

	local async_loader = AllocAsyncLoader(self, "projectile" .. key)
	if async_loader then
		async_loader:SetIsUseObjPool(true)
		async_loader:SetIsOptimizeEffect(false)
		async_loader:SetObjAliveTime(5) --防止永久存在
		async_loader:SetParent(G_EffectLayer)
	
		local cbdata = ActorWGCtrl.GetCBData()
		cbdata[1] = async_loader
		cbdata[2] = main_part_obj
		cbdata[3] = projectile
		cbdata[4] = hurt_point
		cbdata[5] = fromPosition
		cbdata[6] = cb
		cbdata[7] = up_cbdata
		async_loader:Load(bundle_name, asset_name, LoadProjectileCallBack, cbdata)
	end
end

local function DelayPlayProjectileWithoutEffect(cbdata)
	local self = cbdata[1]
	local cb = cbdata[2]
	local up_cbdata = cbdata[3]
	ActorWGCtrl.ReleaseCBData(cbdata)

 	if self.is_deleted then
 		return
 	end

	if cb then
		cb(up_cbdata)
	end
end

function ActorWGCtrl:PlayProjectileWithoutEffect(cb, up_cbdata)
	local cbdata = ActorWGCtrl.GetCBData()
	cbdata[1] = self
	cbdata[2] = cb
	cbdata[3] = up_cbdata
	GlobalTimerQuest:AddDelayTimer(DelayPlayProjectileWithoutEffect, 0.5, cbdata)
end

function ActorWGCtrl:PlayHurtShow(skillAction, root, hurtPoint, prefab_data, cb, cbdata)
	local found = false
	prefab_data = prefab_data or self:GetPrefabData()
	if prefab_data ~= nil and prefab_data.actorController ~= nil then
		local hurts = prefab_data.actorController.hurts
		for _, hurt in pairs(hurts) do
			if hurt.Action == skillAction then
				if next(hurt.HurtEffect) ~= nil then
					self:PlayHurtEffect(hurt, root, hurtPoint)
				end

				if next(hurt.HurtSoundAudioAsset) ~= nil then
					self:PlayHurtSound(hurt, root, hurtPoint, cbdata)
				end

				if hurt.HitCount > 0 then
					self:PlayHitEffect(hurt, root, hurtPoint, cb, cbdata)
				else
					if cb then
						cb(cbdata)
					end
				end

				found = true
				break
			end
		end
	end

	if not found then
		if cb then
			cb(cbdata)
		end
	end
end

function ActorWGCtrl:PlayHurtEffect(data, root, hurtPoint)
	local asset_name = data.HurtEffect.AssetName
	local bundle_name = data.HurtEffect.BundleName

	local async_loader = AllocAsyncLoader(self, "hurt_effect")
	if async_loader then
		async_loader:SetIsUseObjPool(true)
		async_loader:SetIsOptimizeEffect(false)
		async_loader:SetObjAliveTime(5)
		async_loader:Load(bundle_name, asset_name, function(obj)
		if nil == obj then
			return
		end

		local instance = obj:GetOrAddComponent(TypeEffectControl)
		if instance == nil then
			async_loader:Destroy()
			return
		end
		instance:Reset()
		instance.enabled = true

		local targetPos = root
		if data.HurtPosition == HurtPositionEnum.HurtPoint then
			targetPos = hurtPoint
		end

		if data.HurtRotation == HurtRotationEnum.Target then
			instance.transform:SetPositionAndRotation(targetPos.position, targetPos.rotation)
		else
			local direction = targetPos.position - obj.transform.position
			direction.y = 0
			if direction ~= Vector3.zero then
				instance.transform:SetPositionAndRotation(targetPos.position, Quaternion.LookRotation(direction))
			end
		end

		instance:WaitFinsh(function()
			async_loader:Destroy()
		end)

		instance:Play()
	end)
	end
end

function ActorWGCtrl:StopHurtSound()
	if self.hurt_audio_player then
		AudioManager.StopAudio(self.hurt_audio_player)
        self.hurt_audio_player = nil
	end
end


function ActorWGCtrl:PlayHurtSound(data, root, hurtPoint, cbdata)
	if not data or data.HurtSoundAudioAsset.IsEmpty then
		return
	end

	local obj = cbdata[1]
	if obj == nil or obj:IsDeleted() or (obj.IsDead ~= nil and obj:IsDead()) then
		self:StopHurtSound()
		return
	end

	self:StopHurtSound()
	local bundle_name = data.HurtSoundAudioAsset.BundleName
	local asset_name = data.HurtSoundAudioAsset.AssetName
	AudioManager.PlayAndForget(bundle_name, asset_name, nil, hurtPoint.transform,
	function (audio_player)
		CUR_HURT_SOUND_NUM = CUR_HURT_SOUND_NUM + 1
		self.hurt_audio_player = audio_player
	end,
	function (audio_player, asset_name)
		CUR_HURT_SOUND_NUM = CUR_HURT_SOUND_NUM - 1
	end)
end



function ActorWGCtrl:PlayHitEffect(data, root, hurtPoint, cb, cbdata)
	if root == nil or hurtPoint == nil then
		return
	end

	local had_effect = next(data.HitEffect) ~= nil
	local had_sound = next(data.hitSounds) ~= nil

	local asset_name = data.HitEffect.AssetName
	local bundle_name = data.HitEffect.BundleName

	local hit_interval = Split(data.HitInterval or "", "|")

	function LoadEffectRes(index)
		local async_loader = AllocAsyncLoader(self, "hit_effect" .. index)
		if async_loader then
			async_loader:SetIsUseObjPool(true)
			async_loader:SetIsOptimizeEffect(false)
			async_loader:SetObjAliveTime(5)
			async_loader:Load(bundle_name, asset_name, function(obj)
			if nil == obj then
				return
			end

			local instance = obj:GetOrAddComponent(TypeEffectControl)
			if instance == nil then
				async_loader:Destroy()
				return
			end

			instance:Reset()
			instance.enabled = true

			local targetPos = root
			if data.HurtPosition == HurtPositionEnum.HurtPoint then
				targetPos = hurtPoint
			end

			if data.HurtRotation == HurtRotationEnum.Target then
				instance.transform:SetPositionAndRotation(targetPos.position, targetPos.rotation)
			else
				local direction = targetPos.position - obj.transform.position
				direction.y = 0
				if direction ~= Vector3.zero then
					instance.transform:SetPositionAndRotation(targetPos.position, Quaternion.LookRotation(direction))
				end
			end

			instance:WaitFinsh(function()
				async_loader:Destroy()
			end)
			instance:Play()
		end)
		end
	end

	for i = 1, data.HitCount do
		if had_effect or had_sound then
			GlobalTimerQuest:AddDelayTimer(function()
					if self.is_deleted then
						return
					end

					if had_effect then
						LoadEffectRes(i)
					end

					if had_sound then
						self:PlayHitSound(data, root, hurtPoint, cbdata)
					end

					if cb then
						cb(cbdata, i < data.HitCount)
					end

				end, tonumber(hit_interval[i]) or 0)
		end
	end
end

function ActorWGCtrl:StopHitSound()
	if self.hit_audio_player then
		AudioManager.StopAudio(self.hit_audio_player)
        self.hit_audio_player = nil
	end
end

function ActorWGCtrl:PlayHitSound(data, root, hurtPoint, cbdata)
	if not data then
		return
	end

	local obj = cbdata[1]
	if obj == nil or obj:IsDeleted() or (obj.IsDead ~= nil and obj:IsDead()) then
		self:StopHitSound()
		return
	end

	self:StopHitSound()
	local totalWeight = 0
	for k,v in pairs(data.hitSounds) do
		totalWeight = totalWeight + v.weight
	end

	local random = math.random(0, totalWeight)
	local current = 0
	for k,v in pairs(data.hitSounds) do
		current = current + v.weight
		if random <= current then
			if not v.soundAsset.IsEmpty then
				local bundle_name = v.soundAsset.BundleName
				local asset_name = v.soundAsset.AssetName
				AudioManager.PlayAndForget(bundle_name, asset_name, nil, hurtPoint.transform,
				function (audio_player)
					CUR_HURT_SOUND_NUM = CUR_HURT_SOUND_NUM + 1
					self.hit_audio_player = audio_player
				end,
				function (audio_player, asset_name)
					CUR_HURT_SOUND_NUM = CUR_HURT_SOUND_NUM - 1
				end)
			end

			break
		end
	end
end


-- 受击伤害计算
function ActorWGCtrl:PlayHurt(skillAction, prefab_data, perHit, cbdata)
	local found = false
	prefab_data = prefab_data or self:GetPrefabData()
	if prefab_data ~= nil and prefab_data.actorController ~= nil then
		local hurts = prefab_data.actorController.hurts
		for _, hurt in pairs(hurts) do
			if hurt.Action == skillAction then
				if hurt.HitCount > 0 then
					self:PlayHit(hurt, perHit, cbdata)
				else
					perHit(1, cbdata)
				end

				found = true
				break
			end
		end
	end

	if not found then
		perHit(1, cbdata)
	end
end

function ActorWGCtrl:PlayHit(hurts, perHit, cbdata)
	local hit_count = hurts.HitCount
	local hit_interval = Split(hurts.HitInterval or "", "|")
	local random = Split(hurts.HitProportion or "", "|")
	if #hit_interval > 0 and #hit_interval < hit_count then
		print_error("击中次数时间间隔与受击次数不匹配")
	end

	if #random > 0 and #random < hit_count then
		print_error("击中伤害占比次数与受击次数不匹配")
	end

	local def_per = 1 / hit_count
	local already_have = 0

	local function SubHit(fun, percent, cbdata, not_release_data)
		if percent > 1 or percent <= 0 then
			print_error("受击的伤害的每段占比为什么会大于1 或者小于等于0 ？", percent)
		end
		fun(percent, cbdata, not_release_data)
	end

	for i = 1, hit_count do
		local hit_per = random[i] and tonumber(random[i]) or def_per
		GlobalTimerQuest:AddDelayTimer(
			function()
				local obj = cbdata[1]
				local is_data_reslease = type(obj) == "boolean"
				if not is_data_reslease then
					local obj_del = obj == nil or obj:IsDeleted() or (obj.IsDead ~= nil and obj:IsDead())
					local not_release_data = i ~= hit_count
					if obj_del then
						hit_per = 1 - already_have
						not_release_data = false
					end
					SubHit(perHit, hit_per, cbdata, not_release_data)
				end

				already_have = already_have + hit_per
			end
		, tonumber(hit_interval[i]) or 0)
		
	end																																
end

function ActorWGCtrl:PlayBeHitEffect(asset_name, bundle_name, root, position, attached)
	local async_loader = AllocAsyncLoader(self, "behit_effect")
	if async_loader then
		async_loader:SetIsUseObjPool(true)
	async_loader:SetIsOptimizeEffect(false)
	async_loader:SetObjAliveTime(5)
	async_loader:Load(bundle_name, asset_name, function(obj)
		if nil == obj then
			return
		end

		local instance = obj:GetComponent(typeof(EffectControl))
		if instance == nil then
			async_loader:Destroy()
			return
		end

		instance:Reset()
		instance.enabled = true

		if position == nil then
			position = root.transform
		end

		if attached then
			instance.transform:SetParent(position, false)
		else
			instance.transform:SetPositionAndRotation(position.position, position.rotation)
		end

		instance:WaitFinsh(function()
			async_loader:Destroy()
		end)
		instance:Play()
	end)
	end
end

function ActorWGCtrl:TriggerCameraFOV(data)
	if MainCamera ~= nil and MainCamera.main.isActiveAndEnabled then
		self.fadeTime = 0
	end
end

function ActorWGCtrl:Blink(obj)
	if obj then
		local actorRender = obj.transform:GetOrAddComponent(typeof(ActorRender))
		actorRender:PlayBlinkEffect()
	end
end

ActorWGCtrl.cbdata_list = {}
function ActorWGCtrl.GetCBData()
    local cbdata = table.remove(ActorWGCtrl.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true, true, true, true, true, true}
    end

    return cbdata
end

function ActorWGCtrl.ReleaseCBData(cbdata)
    cbdata[1] = true
    cbdata[2] = true
    cbdata[3] = true
    cbdata[4] = true
    cbdata[5] = true
    cbdata[6] = true
    cbdata[7] = true
    cbdata[8] = true
    table.insert(ActorWGCtrl.cbdata_list, cbdata)
end