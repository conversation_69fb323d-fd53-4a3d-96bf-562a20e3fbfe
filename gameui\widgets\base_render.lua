
-----------------------------------------------------
-- BaseRender继承SubPanelView。实现根据配置进行构建，额外动态内容可通过继承自行实现
-- 具体的数据显示需要通过重写OnFlush
-----------------------------------------------------
BaseRender = BaseRender or BaseClass(SubPanelView)
function BaseRender:__init(instance)
	self.data = nil
	self.index = 0
	self.name = ""

	self.is_ui_created = false						-- 是否已经创建ui

	self.click_callback = nil						-- 点击回调
	self.select_call_back = nil						-- 点击回调

	self.is_use_step_calc = false								-- 是否使用分步计算
	self.is_add_step = false									-- 是否添加入分步计算池

	self.is_select = false							-- 是否选中
	self.select_effect = nil						-- 选中特效
	self.ignore_data_to_select = false
	self.select_index = 0
end

function BaseRender:__delete()
	self.data = nil
	self.ignore_data_to_select = false
	self.need_change = nil
	self:ReleaseCallBack()
end

function BaseRender:GetView()
	return self.view
end

function BaseRender:GetData()
	return self.data
end

function BaseRender:SetData(data)
	self.data = data
	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function BaseRender:ClearData()
	self:SetData(nil)
end

function BaseRender:GetIndex()
	return self.index
end

function BaseRender:SetIndex(index)
	self.index = index
end

function BaseRender:GetName()
	return self.name
end

function BaseRender:SetName(name)
	self.name = name
end

function BaseRender:IsUiCreated()
	return self.is_ui_created
end

function BaseRender:SetIgnoreDataToSelect(value)
	if self.ignore_data_to_select and not value then
		self:SetSelect(false)
	end
	self.ignore_data_to_select = value
end

function BaseRender:SetPosition(x, y)
	self.view.transform.position = Vector3(x, y, 0)
end

function BaseRender:SetLocalPosition(x, y)
	Transform.SetLocalPositionXYZ(self.view.transform, x, y, 0)
end

function BaseRender:SetAnchoredPosition(x, y)
	RectTransform.SetAnchoredPositionXY(self.view.rect, x, y)
end

function BaseRender:SetContentSize(w, h)
	self.view.rect.sizeDelta = Vector2(w, h)
end

function BaseRender:SetVisible(is_visible)
	if is_visible ~= nil and self.view then
		self.view:SetActive(is_visible)
	end
end

function BaseRender:AddClickEventListener(callback, is_toggle, is_click_scale)
    if not callback then
    	return
    end
    self.need_click_listen = false
	self.click_callback = callback
	-- if is_click_scale then self.view:setIsHittedScale(true) end
	local fun = function(isOn)
		self:OnClick(isOn)
	end
	if is_toggle then
		self.view.toggle:AddClickListener(fun)
	elseif self.view.button then
		self.view.button:AddClickListener(fun)
	end

end

function BaseRender:SetClickCallBack(callback, is_toggle)
	self.click_callback = callback
	if self.need_click_listen ~= false and self.view then
		self:AddClickEventListener(self.click_callback, is_toggle)
	end
end

-- 是否选中
function BaseRender:IsSelect()
	return self.is_select
end

-- 设置是否选中
function BaseRender:SetSelect(is_select, item_call_back)
	if self.select_call_back and not item_call_back then
		self.select_call_back(self.index, is_select)
	end

	if not self:CanSelect() then
		if self.is_select and self.select_effect then
			self.select_effect:SetActive(true)
			return
		end
	end


	self.is_select = is_select
	if self.is_select then
		if nil == self.select_effect then
			self:CreateSelectEffect()
		else
			self.select_effect:SetActive(true)
		end
	else
		if nil ~= self.select_effect then
			self.select_effect:SetActive(false)
		end
	end
	if not self.has_load then
		self.need_change = true
		return
	end
	self:OnSelectChange(self.is_select)
end

-- 是否使用分步计算
function BaseRender:SetIsUseStepCalc(is_use_step_calc)
	self.is_use_step_calc = is_use_step_calc
end

-- 外部通知刷新，调用此接口
-- function BaseRender:Flush()
-- 	if self.is_use_step_calc then
-- 		if not self.is_add_step then
-- 			self.is_add_step = true
-- 			StepPool.Instance:AddStep(self)
-- 		end
-- 	else
-- 		self:OnFlush()
-- 	end
-- end

-- 分步计算回调
function BaseRender:Step()
	self.is_add_step = false
	self:OnFlush()
end

function BaseRender:GetActive()
	if self.view and self.view.gameObject and not IsNil(self.view.gameObject) then
		return self.view.gameObject.activeInHierarchy
	end
	return false
end

----------------------------------------
--------------ListView（ItemRender）专用
----------------------------------------

function BaseRender:IsSelectIndex()
	return self.select_index == self.index
end

function BaseRender:SetSelectIndex(index)
	self.select_index = index
	local is_select = (self.select_index == self.index)
	self:OnSelectChange(is_select)
end

----------------------------------------------------
-- 可重写的接口 begin
----------------------------------------------------

-- 点击回调
function BaseRender:OnClick(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

-- 是否可选中
function BaseRender:CanSelect()
	return self.ignore_data_to_select or nil ~= self.data
end

-- 创建选中特效
function BaseRender:CreateSelectEffect()

end

-- 选择状态改变
function BaseRender:OnSelectChange(is_select)
end

-- 当前选中下标
function BaseRender:ChangeSelectIndex(index)
	self.select_index = index
end

function BaseRender:SetToggleGroup(toggle_group)
	if not self.view.toggle.group then
		self.view.toggle.group = toggle_group
	end
end

-- BaseGrid外层直接调用SetSelect，回调改变选择表的值
function BaseRender:SetSelectCallBack(select_call_back)
	self.select_call_back = select_call_back
end

--删除写在ReleaseCallBack里的东西
function BaseRender:ReleaseCallBack()

end

----------------------------------------------------
-- 可重写的接口 end
----------------------------------------------------
