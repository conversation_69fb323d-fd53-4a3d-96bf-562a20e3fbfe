-- 2355 幻兽抽奖data

ControlBeastsOADrawWGData = ControlBeastsOADrawWGData or BaseClass(BaseWGCtrl)

function ControlBeastsOADrawWGData:__init()
	if ControlBeastsOADrawWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[ControlBeastsOADrawWGData] attempt to create singleton twice!")
		return
	end

	ControlBeastsOADrawWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("oa_beast_draw_cfg_auto")
    self.draw_type_cfg = cfg.draw_type
	self.draw_type_item_cfg = ListToMap(cfg.draw_type, "cost_item_id")
    self.draw_mode_cfg = ListToMap(cfg.draw_mode, "type", "mode")
    self.draw_grade_cfg = ListToMapList(cfg.draw_grade, "type")
    self.draw_convert_cfg = ListToMap(cfg.draw_convert, "type", "grade")
	self.draw_reward_pool_cfg = ListToMapList(cfg.draw_reward_pool, "type", "grade")
	self.draw_model_show_cfg = cfg.draw_model_show
	self.draw_baodi_cfg = ListToMapList(cfg.draw_baodi, "grade")
	self.view_color_cfg = ListToMap(cfg.color_cfg, "color_type")

    self.draw_item_list = {}
	self.draw_skip_tween = false
end

function ControlBeastsOADrawWGData:__delete()
    ControlBeastsOADrawWGData.Instance = nil
end


----------------------------------REMIND_START--------------------------------
function ControlBeastsOADrawWGData:GetOABeastDrawModeRed()
	local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS)
	if not act_is_open then
		return 0
	end

    local draw_type_cfg = self:GetCurDrawTypeCfg()

    if IsEmptyTable(draw_type_cfg) then
        return 0
    end

    local type = draw_type_cfg.type
	-- 未开启处理
	if not self:IsCanShowDrawType(type) then
		return 0
	end

    -- 能够抽奖
    local draw_mode_cfg_list = self:GetDrawModeCfgListByType(type)
    for k, v in pairs(draw_mode_cfg_list) do
        if self:IsCanOADrawHasEnoughCost(type, v.mode) then
            return 1
        end
    end

    -- 能够兑换
	local day_draw_data, last_time = self:GetDrawGradeByType(type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local day_draw_data, last_time = self:GetDrawGradeByType(type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local cfg = self:GetDrawGradeConvertByTypeGrade(type, grade)
	local draw_data = self:GetOABeastDrawItemInfoByType(type)

	if cfg and draw_data then
		local need_lucky = cfg.need_lucky or 1

        if draw_data.lucky >= need_lucky then
            return 1
        end
	end

    return 0
end
----------------------------------REMIND_END--------------------------------

-------------------------------PROTOCOL_START------------------------------------
function ControlBeastsOADrawWGData:SetOABeastDrawItemInfo(protocol)
    self.draw_item_list = protocol.draw_item_list
end

function ControlBeastsOADrawWGData:OABeastDrawItemUpdate(protocol)
    self.draw_item_list[protocol.type] = protocol.draw_item
end

function ControlBeastsOADrawWGData:SetOABeastDrawRecordInfo(protocol)
    for i, v in pairs(protocol.record_data_list) do
		self:AddOneRecordInfo(protocol.type, v, false)
	end
end

function ControlBeastsOADrawWGData:SetOABeastDrawRecordAdd(protocol)
    self:AddOneRecordInfo(protocol.type, protocol.record_data, true)
end

function ControlBeastsOADrawWGData:AddOneRecordInfo(type, record_info, is_add)
	if not self.draw_record_info then
		self.draw_record_info = {}
	end

	if not self.draw_record_info[type] then
		self.draw_record_info[type] = {}
	end

	-- 封装一下
	local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local info = {}
	info.item_data = {}
	info.item_data.item_id = record_info.beast_id
	info.role_name = main_role_vo and main_role_vo.name or ""
	info.consume_time = record_info.time

	if is_add then
		table.insert(self.draw_record_info[type], 1, info)
	else
		table.insert(self.draw_record_info[type], info)
	end
end

-- 获取当前的抽奖记录
function ControlBeastsOADrawWGData:GetDrawRecordByType(draw_type)
	return (self.draw_record_info or {})[draw_type] or {}
end

function ControlBeastsOADrawWGData:GetOABeastDrawItemInfoByType(type)
    return self.draw_item_list[type]
end

-- function ControlBeastsOADrawWGData:SetDrawSkipTweenFlag(status)
-- 	self.draw_skip_tween = status
-- end

-- function ControlBeastsOADrawWGData:IsDrawSkipTween()
-- 	return self.draw_skip_tween
-- end

-------------------------------PROTOCOL_END------------------------------------

-----------------------------------CFG_START--------------------------------------
function ControlBeastsOADrawWGData:GetCurDrawTypeCfg()
    return self:GetDrawTypeCfgByType(0)
end

function ControlBeastsOADrawWGData:GetDrawTypeCfgByType(type)
    return self.draw_type_cfg[type]
end

function ControlBeastsOADrawWGData:GetDrawModeCfgListByType(type)
    return self.draw_mode_cfg[type]
end

function ControlBeastsOADrawWGData:GetDrawModeCfgByType(type, mode)
    return (self.draw_mode_cfg[type] or {})[mode]
end

function ControlBeastsOADrawWGData:GetDrawGradeCfgListByType(type)
    return self.draw_grade_cfg[type]
end

function ControlBeastsOADrawWGData:GetDrawGradeConvertByTypeGrade(draw_type, draw_grade)
	return (self.draw_convert_cfg[draw_type] or {})[draw_grade]
end

function ControlBeastsOADrawWGData:IsBeastsPrizeDrawItem(item_id)
	return nil ~= self.draw_type_item_cfg[item_id]
end

function ControlBeastsOADrawWGData:GetRewardPoolListByTypeGrade(draw_type, draw_grade)
	return ((self.draw_reward_pool_cfg or {})[draw_type] or {})[draw_grade]
end

function ControlBeastsOADrawWGData:GetBeastsDrawSpineShowCfg(spine_id)
	return (self.draw_model_show_cfg or {})[spine_id]
end
-------------------------------------CFG_END--------------------------------------

-----------------------------------CAL_START--------------------------------------
function ControlBeastsOADrawWGData:IsCanShowCurOADrawType()
    local cur_draw_type_cfg = self:GetCurDrawTypeCfg()
    return self:IsCanShowDrawType(cur_draw_type_cfg.type)
end

function ControlBeastsOADrawWGData:IsCanShowDrawType(draw_type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local draw_type_cfg = self:GetDrawTypeCfgByType(draw_type)

	if not IsEmptyTable(draw_type_cfg) then
		return draw_type_cfg.open_day <= open_day
	end

	return false
end

-- 获取当前的红点
function ControlBeastsOADrawWGData:IsCanOADrawHasEnoughCost(draw_type, mode_id)
	local base_cfg = self:GetDrawTypeCfgByType(draw_type)
	if base_cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id) --拥有的数量
		local mode_cfg = self:GetDrawModeCfgByType(draw_type, mode_id)
		local need_num = mode_cfg and mode_cfg.cost_item_num or 0
		
		if need_num <= 1 then
			return false
		end

		return has_num >= need_num
	end

	return false
end

-- 当前天数具体的档位
function ControlBeastsOADrawWGData:GetDrawGradeByType(draw_type)
	local list = self:GetDrawGradeCfgListByType(draw_type)
	local day_draw_data = nil
	local last_time = 0

	if list == nil or #list <= 0 then
		return day_draw_data, last_time
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local aim_data = nil

	for i, v in ipairs(list) do
		if v and open_day >= v.min_day and open_day <= v.max_day then
			aim_data = v
			break
		end
	end

	if aim_data then
		day_draw_data = aim_data
		local last_day = aim_data.max_day - open_day
		local last_day_time = last_day * 24 * 3600
	    -- 获取当天剩余时间
		local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
		last_time = last_day_time + time
	end

	return day_draw_data, last_time
end

-- 当前档次界面颜色配置
function ControlBeastsOADrawWGData:GetViewColor(color_type)
	return self.view_color_cfg and self.view_color_cfg[color_type]
end

-- 获取当前展示的
function ControlBeastsOADrawWGData:GetDrawShowListByCfg(day_draw_data)
	if not day_draw_data then
		return nil
	end

	local show_id = day_draw_data.show_id
	local show_spine_id = day_draw_data.show_spine_id
	if show_id == nil or show_id == "" or show_spine_id == nil or show_spine_id == "" then
		return nil
	end

	local draw_show_list = {}
	local show_id_list = Split(show_id, "|")
	local show_spine_id_list = Split(show_spine_id, "|")
	for i, v in ipairs(show_id_list) do
		local show_id_info = {}
		show_id_info.item_id = tonumber(v) or 0
		show_id_info.num = 1
		show_id_info.is_bind = 1
		show_id_info.spine_id = tonumber(show_spine_id_list[i] or 0)
		table.insert(draw_show_list, show_id_info)
	end

	return draw_show_list
end

function ControlBeastsOADrawWGData:IsBigRewardItem(draw_type, item_id)
	local day_draw_data, _ = self:GetDrawGradeByType(draw_type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local cfg_list = self:GetRewardPoolListByTypeGrade(draw_type, grade)
	
	if cfg_list and #cfg_list > 0 then
		for i, v in ipairs(cfg_list) do
			if v.item_id and item_id == v.item_id then
				return v.big_reward_type == 1
			end
		end
	end
	
	return false
end

function ControlBeastsOADrawWGData:GetDrawProListByType(draw_type)
	local day_draw_data, _ = self:GetDrawGradeByType(draw_type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local cfg_list = self:GetRewardPoolListByTypeGrade(draw_type, grade)
	local pro_table = {}

	for i, v in ipairs(cfg_list) do
		local data = {}
		data.number = i
		data.item_id = v.item_id
		data.random_count = v.random_count
		table.insert(pro_table, data)
	end

	return pro_table
end

-- 策划要求读取第一个
function ControlBeastsOADrawWGData:GetDrawBaoDiNum(grade)
	local cfg = self.draw_baodi_cfg and self.draw_baodi_cfg[grade] and self.draw_baodi_cfg[grade][1] or {}
	return cfg.times or 10
end
-----------------------------------CAL_END--------------------------------------