local DISTANCE_TO_MOVE = 30
local DEFAULT_WIDTH = 1334
local DEFAULT_RANGE = 380
local MAIN_CAMERA_MIN_DISTANCE = 2

-- 摇杆操作相关常量
local JOYSTICK_MIN_INPUT_THRESHOLD = 0.1          -- 最小输入阈值
local JOYSTICK_ANGLE_DIFF_THRESHOLD = 0.26        -- 角度差异阈值  
local JOYSTICK_MOVE_DISTANCE = 8                  -- 移动距离
local JOYSTICK_FALLBACK_OFFSET = 3                -- 备用路径偏移
local JOYSTICK_MIN_REMAIN_DISTANCE = 2            -- 最小剩余距离

function MainUIView:JoystickLoadCallBack()
	self.joystick_angle = 0
	self.joystick_last_target_pos = u3d.vec2(0, 0)
	self.is_touched = false
	self.is_joysticking = false

	self.touch_times = 0
	self.max_touch_interval = 0.1
	-- self.mati_up = self.node_list["MatiUp"]
	-- self.mati_down = self.node_list["MatiDown"]
	self.guide_mati = self.node_list["GuideMaTi"]
	-- self.img_joystick_1 = self.node_list["ImgJoystickBg1"]
	self.img_joystick_2 = self.node_list["ImgJoystickBg2"]
	self.img_joystick_2.canvas_group.alpha = 0.5

	self.joystick_finger_index = -1
	self.swipe_finger_index = -1
	self.is_drag = false
	self.can_drag_time = 0

	-- 0.1s后才能响应下一次揺杆
	self.can_joystick_time = 0
	self.check_joystick_time = 0.1
	self.check_touch_times = 0

	if self.node_list.joystick_region then
		self.node_list.joystick_region:SetActive(false)
	end

	self.node_list["Joystick"].joystick:AddDragBeginListener(
		BindTool.Bind(self.OnJoystickBegin, self))
	self.node_list["Joystick"].joystick:AddDragUpdateListener(
		BindTool.Bind(self.OnJoystickUpdate, self))
	self.node_list["Joystick"].joystick:AddDragEndListener(
		BindTool.Bind(self.OnJoystickEnd, self))
	self.node_list["Joystick"].joystick:AddIsTouchedListener(
		BindTool.Bind(self.OnJoystickTouched, self))

	local value = UnityEngine.Screen.width / DEFAULT_WIDTH
	self.node_list["Joystick"].joystick:SetDynamicRange(DEFAULT_RANGE * value)

	self.swipe_start_handle = BindTool.Bind(self.OnFingerSwipeStart, self)				-- 手指滑动 - 开始
	EasyTouch.On_SwipeStart = EasyTouch.On_SwipeStart + self.swipe_start_handle

	self.swipe_end_handle = BindTool.Bind(self.OnFingerSwipeEnd, self)					-- 手指滑动 - 结束
	EasyTouch.On_SwipeEnd = EasyTouch.On_SwipeEnd + self.swipe_end_handle

	self.swipe_handle = BindTool.Bind(self.OnFingerSwipe, self)							-- 手指滑动
	EasyTouch.On_Swipe = EasyTouch.On_Swipe + self.swipe_handle

	self.pinch_handle = BindTool.Bind(self.OnFingerPinch, self)							-- 多指滑动
	EasyTouch.On_Pinch = EasyTouch.On_Pinch + self.pinch_handle

	--功能引导监听
	self.getui_callback = BindTool.Bind(self.GetUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.MainUIView, self.getui_callback)

	self.update_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.JoystickUpdate, self), 0)
	
	self:InitDistanceChangeCallback()
	self.last_scene_bloom_status = false
	self.scene_volume_load_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_VOLUME_CREATE_COMPLETE,
									self.camera_distance_change_callback)
end

function MainUIView:InitDistanceChangeCallback()
	if nil == self.camera_distance_change_callback then
		self.camera_distance_change_callback = BindTool.Bind(self.CameraDistanceChangeCallback, self)
	end

	if not IsNil(MainCameraFollow) then
		MainCameraFollow.SetDistanceChangeCallback(MainCameraFollow, self.camera_distance_change_callback)
	end
end

function MainUIView:CameraDistanceChangeCallback()
	if not IsNil(MainCameraFollow) then
		--场景人物特写景深开关
		local is_active = MainCameraFollow.Distance <= MAIN_CAMERA_MIN_DISTANCE
		if self.last_scene_bloom_status ~= is_active then
			if Scene ~= nil and Scene.Instance ~= nil then
				Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.DEPTH, is_active)
			end

			GlobalEventSystem:Fire(ObjectEventType.SHIELD_OTHER_SCENEOBJ_FOLLOWUI_IN_DEPTH_OF_FIELD, is_active)
			self.last_scene_bloom_status = is_active
		end
	end
end

function MainUIView:Joystick__ReleaseCallBack()
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.MainUIView, self.getui_callback)
	end
	self.getui_callback = nil

	if self.swipe_start_handle ~= nil then
		EasyTouch.On_SwipeStart = EasyTouch.On_SwipeStart - self.swipe_start_handle
		self.swipe_start_handle = nil
	end

	if self.swipe_end_handle ~= nil then
		EasyTouch.On_SwipeEnd = EasyTouch.On_SwipeEnd - self.swipe_end_handle
		self.swipe_end_handle = nil
	end

	if self.swipe_handle ~= nil then
		EasyTouch.On_Swipe = EasyTouch.On_Swipe - self.swipe_handle
		self.swipe_handle = nil
	end

	if self.pinch_handle ~= nil then
		EasyTouch.On_Pinch = EasyTouch.On_Pinch - self.pinch_handle
		self.pinch_handle = nil
	end

	if self.update_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.update_timer)
		self.update_timer = nil
	end

	if self.scene_volume_load_complete then
		GlobalEventSystem:UnBind(self.scene_volume_load_complete)
		self.scene_volume_load_complete = nil
	end

	self.camera_distance_change_callback = nil
	self:CancelQuest()

	-- self.mati_up = nil
	-- self.mati_down = nil
	self.guide_mati = nil
	-- self.img_joystick_1 = nil
	self.img_joystick_2 = nil
	self:CancelOperationQuest()

	if not IsNil(MainCameraFollow) then
		MainCameraFollow.SetDistanceChangeCallback(MainCameraFollow, nil)
	end
end

local keycode_w = UnityEngine.KeyCode.W
local keycode_a = UnityEngine.KeyCode.A
local keycode_s = UnityEngine.KeyCode.S
local keycode_d = UnityEngine.KeyCode.D
local keycode_e = UnityEngine.KeyCode.E
local keycode_q = UnityEngine.KeyCode.Q
local keycode_escape = UnityEngine.KeyCode.Escape
function MainUIView:JoystickUpdate()
	--cg中不能移动操作
	if CgManager.Instance:IsCgIng() then
		return
	end

	self.dir_key_list = self.dir_key_list or {}
	if UnityEngine.Input.GetKey(keycode_w) then
		self.dir_key_list[keycode_w] = {x = 0, y = 1}
	else
		self.dir_key_list[keycode_w] = nil
	end

	if UnityEngine.Input.GetKey(keycode_a) then
		self.dir_key_list[keycode_a] = {x = -1, y = 0}
	else
		self.dir_key_list[keycode_a] = nil
	end

	if UnityEngine.Input.GetKey(keycode_s) then
		self.dir_key_list[keycode_s] = {x = 0, y = -1}
	else
		self.dir_key_list[keycode_s] = nil
	end

	if UnityEngine.Input.GetKey(keycode_d) then
		self.dir_key_list[keycode_d] = {x = 1, y = 0}
	else
		self.dir_key_list[keycode_d] = nil
	end

	--if UnityEngine.Input.GetKey(keycode_escape) then
	--	ViewManager.Instance:CloseAll()
	--end

	if nil ~= next(self.dir_key_list) then
		self:OnJoystickBegin()
		local dir = {x = 0, y = 0}
		for k, v in pairs(self.dir_key_list) do
			dir = { x = dir.x + v.x, y = dir.y + v.y}
		end
		local CIRCLE_ROUND = 40
		self:OnJoystickUpdate(dir.x * CIRCLE_ROUND, dir.y * CIRCLE_ROUND)
	else
		if self.touch_times > 0 then
			self:OnJoystickEnd(0, 0, true)
		end
	end

	self.camera_turn_left, self.camera_turn_right = 0, 0
	if UnityEngine.Input.GetKey(keycode_q) then
		self.camera_turn_left = -1
	else
		self.camera_turn_left = 0
	end

	if UnityEngine.Input.GetKey(keycode_e) then
		self.camera_turn_right = 1
	else
		self.camera_turn_right = 0
	end

	self:MainCameraFollowSwipe((self.camera_turn_left + self.camera_turn_right) * 2, 0)
	self:CheckCanJoystick()
end

function MainUIView:CheckCanJoystick()
	if Status.NowTime < self.can_joystick_time then
		return
	end

	self.can_joystick_time = 0
end

function MainUIView:OnJoystickSceneChangeBegin()
	self.dir_key_list = {}
	self.camera_turn_left = nil
	self.camera_turn_right = nil
end

-- 摇杆点击 - 开始
function MainUIView:OnJoystickBegin()
	self.touch_times = Status.NowTime
	self.check_touch_times = Status.NowTime
	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove() then
		return
	end

	if not ViewManager.Instance:IsOpen(GuideModuleName.NormalGuideView) then
		MainuiWGCtrl.Instance:SetToggleMenuIsOn(false)
	end

	TaskGuide.Instance:CanAutoAllTask(false)
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
	GuildBattleRankedWGCtrl.Instance:ClearWorshipData()
end

-- 检查是否可以处理摇杆输入
function MainUIView:CanProcessJoystickInput()
	if nil == MainCamera or IsNil(MainCamera) then
		return false
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return false
	end

	if main_role:CantPlayerDoMove() or main_role:IsSwordSprint() or self:IsInSprintCd() then
		return false
	end

	if main_role:IsJump() then
		main_role.move_oper_cache2 = nil
		return false
	end

	if 0 ~= self.can_joystick_time then
		return false
	end

	return true
end

-- 查找替代路径
function MainUIView:FindAlternatePath(main_role, x, y, fx, fy, ignore_high_area, ignore_block)
	local x_offset = fx >= 0 and JOYSTICK_FALLBACK_OFFSET or -JOYSTICK_FALLBACK_OFFSET
	local y_offset = fy >= 0 and JOYSTICK_FALLBACK_OFFSET or -JOYSTICK_FALLBACK_OFFSET
	
	local target_x, target_y = x, y
	
	if math.abs(fx) > math.abs(fy) then
		-- 优先尝试X方向移动
		target_x, target_y = AStarFindWay:GetLineEndXY(x, y, x + x_offset, y, ignore_high_area, ignore_block)
		if target_x == x and target_y == y then
			target_x, target_y = AStarFindWay:GetLineEndXY(x, y, x + x_offset, y + y_offset, ignore_high_area, ignore_block)
		end
		if target_x == x and target_y == y then
			target_x, target_y = AStarFindWay:GetLineEndXY(x, y, x, y + y_offset, ignore_high_area, ignore_block)
		end
	else
		-- 优先尝试Y方向移动
		target_x, target_y = AStarFindWay:GetLineEndXY(x, y, x, y + y_offset, ignore_high_area, ignore_block)
		if target_x == x and target_y == y then
			target_x, target_y = AStarFindWay:GetLineEndXY(x, y, x + x_offset, y + y_offset, ignore_high_area, ignore_block)
		end
		if target_x == x and target_y == y then
			target_x, target_y = AStarFindWay:GetLineEndXY(x, y, x + x_offset, y, ignore_high_area, ignore_block)
		end
	end
	
	return target_x, target_y
end

-- 执行移动操作
function MainUIView:ExecuteMovement(main_role, target_x, target_y, ignore_high_area, ignore_block)
	if self.joystick_last_target_pos.x ~= target_x or self.joystick_last_target_pos.y ~= target_y then
		if not main_role:IsJump() and not main_role.has_play_qinggong_land then
			self.is_joysticking = true
			GuajiWGCtrl.Instance:ResetMoveCache()
			TaskGuide.Instance:CanAutoAllTask(false)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)

			if main_role:DoMoveByClick(target_x, target_y, 0, nil, nil, ignore_high_area, nil, ignore_block, nil, true) ~= false then
				GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None, true)
				self.joystick_last_target_pos = u3d.vec2(target_x, target_y)
				return true
			end
		end
	end
	return false
end

-- 摇杆回调
function MainUIView:OnJoystickUpdate(fx, fy)
	-- 前置条件检查
	local can_process = self:CanProcessJoystickInput()
	if not can_process then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	-- 设置摇杆操作冷却时间
	self.can_joystick_time = self.check_joystick_time + Status.NowTime

	-- 检查移动距离是否足够
	if fx^2 + fy^2 <= DISTANCE_TO_MOVE^2 then
		GlobalEventSystem:Fire(LayerEventType.TOUCH_MOVED, 0, 0)
		-- 特定场景类型设置临时挂机状态
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.YEZHANWANGCHENGFUBEN or 
		   scene_type == SceneType.ETERNAL_NIGHT or 
		   scene_type == SceneType.ETERNAL_NIGHT_FINAL then
			GuajiWGCtrl.Instance:TrySetTemporary()
		end
		return
	end

	-- 检查输入阈值
	if math.abs(fx) < JOYSTICK_MIN_INPUT_THRESHOLD and math.abs(fy) < JOYSTICK_MIN_INPUT_THRESHOLD then
		return
	end

	-- 计算实际移动方向
	fx, fy = self:ActuallyMoveDir(fx, fy)
	local angle = math.atan2(fy, fx)
	
	-- 获取移动相关配置
	local ignore_high_area = main_role:IsCanUseQingGong()
	local ignore_block = main_role:IsMitsurugi()
	
	-- 检查是否需要重新计算路径
	if main_role:GetMoveRemainDistance() <= JOYSTICK_MIN_REMAIN_DISTANCE or 
	   math.abs(angle - self.joystick_angle) >= JOYSTICK_ANGLE_DIFF_THRESHOLD then
		
		self.joystick_angle = angle
		local dir = u3d.v2Normalize(u3d.vec2(fx, fy))
		local x, y = main_role:GetLogicPos()
		
		-- 尝试直线路径
		local target_x, target_y = AStarFindWay:GetLineEndXY(x, y, 
			x + dir.x * JOYSTICK_MOVE_DISTANCE, 
			y + dir.y * JOYSTICK_MOVE_DISTANCE, 
			ignore_high_area, ignore_block)
		
		-- 如果直线路径被阻挡，寻找替代路径
		if target_x == x and target_y == y then
			target_x, target_y = self:FindAlternatePath(main_role, x, y, fx, fy, ignore_high_area, ignore_block)
		end

		-- 执行移动
		self:ExecuteMovement(main_role, target_x, target_y, ignore_high_area, ignore_block)
	end

	GlobalEventSystem:Fire(LayerEventType.TOUCH_MOVED, fx, fy)
end

function MainUIView:OnJoystickEnd(fx, fy, is_update)
	if IS_DEBUG_BUILD and not is_update and self.check_touch_times ~= nil and Status.NowTime - self.check_touch_times < 0.1 then
		local name = ""
		if GameVoManager ~= nil and GameVoManager.Instance ~= nil then
			local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
			if main_role_vo ~= nil then
				name = main_role_vo.name
			end
		end

		local show_log = string.format("内网日志：你在0.1秒内点了一下摇杆，这个操作会影响自动做任务或者挂机，你想清楚是不是自己不小心点的：%s", name)
		print_log(show_log)
	end

	if not is_update then
		self.check_touch_times = 0
	end

	if nil == MainCamera then
		return
	end

	self.is_joysticking = false

	self.joystick_angle = -720
	self.joystick_last_target_pos = u3d.vec2(0, 0)

	-- local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- if main_role_vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 then

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

    if main_role:IsJump() or main_role:CantPlayerDoMove() then
		return
	end

	-- local main_role = Scene.Instance:GetMainRole()
	if (not main_role:IsAtkPlaying()) then
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
		MoveCache.SetEndType(MoveEndType.Normal)
		main_role:ChangeToCommonState()
	end

	TaskGuide.Instance:CanAutoAllTask(false)
	self.touch_times = 0
end

function MainUIView:GetIsJoystick()
	return self.is_joysticking
end

-- 实际移动方向
function MainUIView:ActuallyMoveDir(fx, fy)
	if nil == MainCamera then
		return 0, 0
	end

	if nil == self.quat then
		self.quat = Quaternion()
		self.screen_forward = Vector3(0, 0, 0)
		self.screen_input = Vector3(0, 0, 0)
		self.euler_angles = Vector3(0, 0, 0)
	end

	self.screen_forward.z = 1
	self.screen_input.x = fx
	self.screen_input.z = fy

	self.quat:SetFromToRotation(self.screen_forward, self.screen_input)

	self.euler_angles.x = self.quat.eulerAngles.x
	self.euler_angles.y = self.quat.eulerAngles.y
	self.quat.eulerAngles = self.euler_angles
	local camera_forward = MainCamera.transform.forward
	camera_forward.y = 0

	local move_dir = self.quat * camera_forward
	return move_dir.x, move_dir.z
end

-- 手指滑动 - 开始
function MainUIView:OnFingerSwipeStart(gesture)
	if gesture.fingerIndex ~= self.joystick_finger_index and not self.is_drag then
		self.is_drag = true
		self.swipe_finger_index = gesture.fingerIndex
		self.can_drag_time = Status.NowTime + 0.05
	end
end

-- 手指滑动 - 结束
function MainUIView:OnFingerSwipeEnd(gesture)
	if gesture.fingerIndex == self.swipe_finger_index then
		self.is_drag = false
		self.swipe_finger_index = -1
	end
	if not IsNil(MainCameraFollow) then
		MainuiWGData.UserOperation = true
		MainCameraFollow.AutoRotation = false
		self:CancelOperationQuest()
		local auto_time = 0

		if Scene and Scene.Instance then
			auto_time = Scene.Instance:GetCameraAutoTime()
		end

		self.user_operation_time = GlobalTimerQuest:AddDelayTimer(function ()
			MainuiWGData.UserOperation = false
			self:SetAutoRotation()
		end, auto_time)
	end
end

function MainUIView:CancelOperationQuest()
	if self.user_operation_time then
		GlobalTimerQuest:CancelQuest(self.user_operation_time)
		self.user_operation_time = nil
	end
end

-- 手指滑动
function MainUIView:OnFingerSwipe(gesture)
	if not self.is_drag or (self.is_touched and gesture.fingerIndex == self.joystick_finger_index) then
		return
	end

	if Status.NowTime >= self.can_drag_time and not IsNil(MainCameraFollow) then
		local x = gesture.swipeVector.x
		if x > 0 then
			x = math.min(20, x)
		else
			x = math.max(-20, x)
		end
		self:MainCameraFollowSwipe(x, gesture.swipeVector.y)

		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil then
			main_role:ResetCollideParam()
		end
	end
end

-- 扫屏
function MainUIView:MainCameraFollowSwipe(x, y)
	if IsNil(MainCameraFollow) or (x == 0 and y == 0) then
		return
	end

	local can_opera, can_change_x = Scene.Instance:CheckCanOperaCamera()
	if not can_opera and not can_change_x then
		return
	end

	MainCameraFollow:Swipe(x, y)
	GlobalEventSystem:Fire(MainUIEventType.CAMERA_HANDLE_CHANGE)
	self:CancelQuest()
	self.delay_time = GlobalTimerQuest:AddDelayTimer(function ()
		self:UpdateCameraSetting()
	end, 0.5)
end

-- 多指滑动
function MainUIView:OnFingerPinch(gesture)
	if RoleWGData.Instance:IsHoldAngle() then
		return
	end

	if ViewManager.Instance:IsOpen(GuideModuleName.NewAppearanceDyeView) then
		return
	end

	if not self.is_touched and not IsNil(MainCameraFollow) then
		if not Scene.Instance:CheckCanOperaCamera() then
			return
		end

		self.can_drag_time = Status.NowTime + 0.05
		local pinch = gesture.deltaPinch >= 33 and 33 or (gesture.deltaPinch <= -33 and -33 or gesture.deltaPinch)
		MainCameraFollow:Pinch(pinch)

		GlobalEventSystem:Fire(MainUIEventType.CAMERA_HANDLE_CHANGE)
		self:CancelQuest()
		self.delay_time = GlobalTimerQuest:AddDelayTimer(function ()
			self:UpdateCameraSetting()
		end, 0.5)
	end
end

function MainUIView:OnJoystickTouched(is_touched, finger_index)
	if self.node_list.joystick_region then
		self.node_list.joystick_region:SetActive(is_touched)
	end

	-- if self.node_list.joystick_light_img then
	-- 	self.node_list.joystick_light_img.image.enabled = is_touched
	-- end

	if self.node_list.joystic_light then
		self.node_list.joystic_light.image.enabled = is_touched
	end

	if self.img_joystick_2 then
		self.img_joystick_2.canvas_group.alpha = self.is_touched and 0.5 or 1
	end
	-- self.mati_up.canvas_group.alpha = self.is_touched and 0.5 or 1

	self.joystick_finger_index = finger_index or -1
	self.is_touched = is_touched
	if not self.is_touched then
		self.is_joysticking = false
	end
end

function MainUIView:CancelQuest()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
		self.delay_time = nil
	end
end

-- 上传镜头参数
function MainUIView:UpdateCameraSetting()
	if CAMERA_TYPE == CameraType.Free then
		if not IsNil(MainCameraFollow) and not IsNil(MainCamera) then
			local angle = MainCamera.transform.parent.transform.localEulerAngles
			local distance = MainCameraFollow.Distance
			local list = {[1] = {HOT_KEY.CAMERA_ROTATION_X, angle.x},
						[2]	 = {HOT_KEY.CAMERA_ROTATION_Y, angle.y},
						[3] = {HOT_KEY.CAMERA_DISTANCE, distance}}
			SettingWGCtrl.Instance:SendChangeHotkeyReq(list)
			SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_X, angle.x)
			SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_Y, angle.y)
			SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_DISTANCE, distance)
		end
	end
end

function MainUIView:GetUiCallBack(ui_name, ui_param)

end

--婚宴巡游极简模式下是需要隐藏摇杆
function MainUIView:HideJoystick(enabled)
	if self.node_list["Joystick"] then
		self.node_list["Joystick"]:SetActive(enabled)
	end
end