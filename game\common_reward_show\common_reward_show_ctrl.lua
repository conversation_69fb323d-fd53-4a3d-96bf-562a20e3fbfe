require("game/common_reward_show/common_reward_show_view")
require("game/common_reward_show/common_reward_show_grid")
require("game/common_reward_show/common_reward_show_item_render")

RewardShowViewWGCtrl = RewardShowViewWGCtrl or BaseClass(BaseWGCtrl)

RewardShowViewType =
{
	Normal = 1,						--默认，只有Item.
	Normal_Probability = 2,			--默认样式 + Item显示概率.
	Title = 3,						--含标题，Item列表分组.
	Title_Probability = 4,			--Title样式 + Item显示概率.
	Normal_Center = 5,				-- 默认样式，居中显示
	Title_Probability_2 = 6			-- Title样式 + Item显示概率. 每行5个
}

RewardShowViewColor =
{
	Orange = "orange",					--默认，橙色.
	Purple = "purple",					--基佬紫.
	Green = "green",					--绿色.
}

function RewardShowViewWGCtrl:__init()
	RewardShowViewWGCtrl.Instance = self

	self.content_type = nil
	self.color_type = nil
end

function RewardShowViewWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.batch_use_choose_gift_view then
		self.batch_use_choose_gift_view:DeleteMe()
		self.batch_use_choose_gift_view = nil
	end

	RewardShowViewWGCtrl.Instance = nil
end

--[[
local reward_data = {
	view_type,									--类型，默认RewardShowViewType.Normal.
	view_color,									--颜色，默认RewardShowViewColor.Orange.
	reward_item_list,							--Normal类型：奖励列表.
	normal_probability_reward_item_data =		--Normal_Probability类型：Normal类型 + 奖励列表与概率的数据.
	{
		{
			item,						--道具.
			probability_text			--概率.
		},
	},
	title_reward_item_data =					--Title类型：包含标题与奖励列表的数据.
	{
		{
			title_text,							--标题.
			reward_item_list =					--奖励列表.
			{
				item,
			}
		},
	},
	title_probability_reward_item_data =		--Title_Probability类型：Title类型 + 奖励列表与概率的数据.
	{
		{
			title_text,							--标题.
			reward_item_list =					--包含概率的奖励列表数据.
			{
				{
					item,						--道具.
					probability_text			--概率.
				},
			}
		},
	}
	other_tips,									--提示字符串
}
--]]
function RewardShowViewWGCtrl:SetRewardShowData(reward_data)
	local view_color = reward_data.view_color or RewardShowViewColor.Orange
	local view_type = reward_data.view_type or RewardShowViewType.Normal
	if self.color_type ~= view_color or self.content_type ~= view_type then
		self.color_type = view_color
		self.content_type = view_type

		if nil ~= self.view then
			self.view:DeleteMe()
			self.view = nil
		end

		self.view = RewardShowView.New(self.color_type)
	end

	if self.view then
		self.view:SetData(reward_data)
	end
end

function RewardShowViewWGCtrl:Flush()
	if self.view then
		self.view:Flush()
	end
end