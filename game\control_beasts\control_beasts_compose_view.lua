local BEASTS_STAR_VIRTUAL_ITEM = {
    [1] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_ONE,
    [2] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWO,
	[3] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_THREE,
	[4] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FOUR,
	[5] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FIVE,
	[6] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SIX,
	[7] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SEVEN,			--任意7星幻 兽
	[8] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_EIGHT,			--任意8星幻 兽
	[9] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_NINE,			--任意9星幻 兽
	[10] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TEN,			--任意10星幻兽
	[11] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_ELEVEN,		--任意11星幻兽
	[12] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWELVE,		--任意12星幻兽
	[13] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_THIRTEEN,		--任意13星幻兽
	[14] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FOURTEEN,		--任意14星幻兽
	[15] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_FIFTEEN,		--任意15星幻兽
	[16] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SIXTEEN,		--任意16星幻兽
	[17] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_SEVENTEEN,		--任意17星幻兽
	[18] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_EIGHTEEN,		--任意18星幻兽
	[19] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_NINETEEN,		--任意19星幻兽
	[20] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY,		--任意20星幻兽
	[21] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_ONE,	--任意21星幻兽
	[22] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_TWO,	--任意23星幻兽
	[23] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_THREE,	--任意22星幻兽
	[24] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_FOUR,	--任意24星幻兽
	[25] = COMMON_CONSTS.VIRTUAL_ITEM_BEAST_STAR_TWENTY_FIVE,	--任意25星幻兽
}

function ControlBeastsWGView:LoadComposeViewCallBack()
    if self.left_star_beast_item == nil then
        self.left_star_beast_item = ItemCell.New(self.node_list.left_star_beast_item)
    end

    if self.right_star_beast_item == nil then
        self.right_star_beast_item = ItemCell.New(self.node_list.right_star_beast_item)
    end

    if not self.beasts_compose_star_list then
		self.beasts_compose_star_list = {}
		for i = 1, 5 do
			self.beasts_compose_star_list[i] = self.node_list["beasts_compose_star" .. i]
		end
	end

    if not self.compose_attr_level then
        self.compose_attr_level = CommonAddAttrRender.New(self.node_list.compose_attr_level)
    end

    if not self.compose_attr_flair then
        self.compose_attr_flair = CommonAddAttrRender.New(self.node_list.compose_attr_flair)
    end

    -- 资质属性
    if self.compose_attrlist == nil then
        self.compose_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.compose_attrlist:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetAttrNameNeedSpace(true)
                cell:SetIndex(i)
                self.compose_attrlist[i] = cell
            end
        end
    end

    -- 资质属性
    if self.compose_now_skill_list == nil then
        self.compose_now_skill_list = {}
        for i = 1, 10 do
            local attr_obj = self.node_list.compose_now_skill_list:FindObj(string.format("compose_skill_render_%d", i))
            if attr_obj then
                local cell = BeastCultueSkillItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.compose_now_skill_list[i] = cell
            end
        end
    end

    -- 特殊一个
    if not self.beasts_compose_aim then
        self.beasts_compose_aim = BeststsStarUpItemRender.New(self.node_list.beasts_compose_aim)
        self.beasts_compose_aim:SetClickCallBack(BindTool.Bind1(self.OnSelectStarUpItem, self))
        self.beasts_compose_aim:SetIsSpecial(true)
    end

    -- 升星材料
    if not self.beasts_cell_list then
        self.beasts_cell_list = {}

        for i = 1, 3 do
            self.beasts_cell_list[i] = {}
            local cell = BeststsStarUpItemRender.New(self.node_list[string.format("beasts_compose_cell_%d", i)])
            cell:SetClickCallBack(BindTool.Bind1(self.OnSelectStarUpItem, self))
            cell:SetIsSpecial(false)
            self.beasts_cell_list[i].cell = cell
            self.beasts_cell_list[i].root = self.node_list[string.format("beasts_compose_cell_root_%d", i)]
            self.beasts_cell_list[i].cell_txt = self.node_list[string.format("beasts_compose_cell_txt_%d", i)]
        end
    end

    -- if not self.beasts_compose_cell then
    --     self.beasts_compose_cell = BeststsStarUpItemRender.New(self.node_list.beasts_compose_cell)
    --     self.beasts_compose_cell:SetClickCallBack(BindTool.Bind1(self.OnSelectStarUpItem, self))
    --     self.beasts_compose_cell:SetIsSpecial(false)
    -- end

    if not self.compose_beast_model then
		self.compose_beast_model = RoleModel.New()
		self.compose_beast_model:SetUISceneModel(self.node_list["compose_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.compose_beast_model, TabIndex.beasts_compose)
	end

    if not self.compose_holy_beast_item then
        self.compose_holy_beast_item = ItemCell.New(self.node_list["compose_holy_beast_cell_root"])
        self.compose_holy_beast_item:SetIsShowTips(false)
    end

    XUI.AddClickEventListener(self.node_list.compose_beasts_skill_show, BindTool.Bind2(self.ComposeSkillTips, self))
    XUI.AddClickEventListener(self.node_list.btn_star_up, BindTool.Bind2(self.ComposeStartUpClick, self))
    XUI.AddClickEventListener(self.node_list.compose_look_btn, BindTool.Bind2(self.ComposeEvolvePreview, self))
    XUI.AddClickEventListener(self.node_list.compose_batch_btn, BindTool.Bind2(self.OpenBeastsQuickSpView, self))
    XUI.AddClickEventListener(self.node_list.btn_quick_star_up, BindTool.Bind2(self.ComposeQuickStartUpClick, self))
    XUI.AddClickEventListener(self.node_list.btn_compose_beast_select, BindTool.Bind(self.OnClickBeastsContractBtn, self))
end

function ControlBeastsWGView:OpenComposeViewCallBack()
    self.cur_spend_list = nil
end

function ControlBeastsWGView:CloseComposeViewCallBack()
end

function ControlBeastsWGView:ShowComposeViewCallBack()
end

function ControlBeastsWGView:ReleaseComposeViewCallBack()
    if self.left_star_beast_item then
        self.left_star_beast_item:DeleteMe()
        self.left_star_beast_item = nil
    end

    if self.right_star_beast_item then
        self.right_star_beast_item:DeleteMe()
        self.right_star_beast_item = nil
    end

    if self.compose_attrlist and #self.compose_attrlist > 0 then
		for _, compose_attr_cell in ipairs(self.compose_attrlist) do
			compose_attr_cell:DeleteMe()
			compose_attr_cell = nil
		end

		self.compose_attrlist = nil
	end

    if self.compose_now_skill_list and #self.compose_now_skill_list > 0 then
		for _, now_skill_cell in ipairs(self.compose_now_skill_list) do
			now_skill_cell:DeleteMe()
			now_skill_cell = nil
		end

		self.compose_now_skill_list = nil
	end

    if self.beasts_compose_aim then
        self.beasts_compose_aim:DeleteMe()
        self.beasts_compose_aim = nil
    end

    if self.beasts_cell_list and #self.beasts_cell_list > 0 then
        for i, v in ipairs(self.beasts_cell_list) do
            if v and v.cell then
                v.cell:DeleteMe()
                v.cell = nil
            end
        end

        self.beasts_cell_list = nil
    end

    if self.compose_beast_model then
        self.compose_beast_model:DeleteMe()
        self.compose_beast_model = nil
    end

    if self.compose_attr_level then
        self.compose_attr_level:DeleteMe()
        self.compose_attr_level = nil
    end

    if self.compose_attr_flair then
        self.compose_attr_flair:DeleteMe()
        self.compose_attr_flair = nil
    end

    if self.compose_holy_beast_item then
        self.compose_holy_beast_item:DeleteMe()
        self.compose_holy_beast_item = nil
    end

    self.compose_beast_model_res_id = nil
    self.beasts_compose_star_list = nil
end

-- 合成背包点击
function ControlBeastsWGView:SelectComposeBeastCellCallBack()
    -- 初始化两个表
    self.cur_spend_list = {}
    self.cur_spend_list.special_list = {}
    self.cur_spend_list.same_list = {}
    self:FlushComposeBeastsMessage()
end

-- 升星材料点击
function ControlBeastsWGView:OnSelectStarUpItem(beast_cell)
    if not beast_cell.data then return end
    if not self:CheckHaveDataAndServerData(self.select_beast_data) or not self:CheckHaveDataAndServerData(self.select_beast_data.link_beast_data) then
        return
    end

    -- 碎片过滤
    if beast_cell:GetIsChip() then
        return
    end

    local server_data = self.select_beast_data.link_beast_data.server_data
    local show_data = {}
    show_data.aim_beast_id = server_data.beast_id
    show_data.aim_bag_id = self.select_beast_data.link_beast_data.bag_id
    show_data.need_beast_id = beast_cell.data.item_id
    show_data.special_list = self.cur_spend_list and self.cur_spend_list.special_list or nil
    show_data.same_list = self.cur_spend_list and self.cur_spend_list.same_list or nil
    show_data.is_special = beast_cell:GetIsSpecial()
    show_data.is_element, show_data.element = beast_cell:GetIsElement()
    show_data.star_num = beast_cell:GetStarNum()

    ControlBeastsWGCtrl.Instance:SetSelectBeastsBatchOkCallBack(BindTool.Bind1(self.OnBatchOkCallBack, self))
    ControlBeastsWGCtrl.Instance:OpenBeastsBatchSelectView(show_data)
end

-- 材料选择回调
--  self.ok_callback(self.show_data.is_special, self.show_data.is_element, self.show_data.element, self.show_data.star_num, data_list)

function ControlBeastsWGView:OnBatchOkCallBack(is_special, is_element, element, star_num, select_list)
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return
    end

    if not self.cur_spend_list then
        return
    end

    local index = BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR
    if is_element then
        index = BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT
    end

    if element == nil then
        element = BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT
    end

    if is_special then
        self.cur_spend_list.special_list = {}
    else
        if not self.cur_spend_list.same_list then
            self.cur_spend_list.same_list = {}
        end

        if self.cur_spend_list.same_list[index] == nil then
            self.cur_spend_list.same_list[index] = {}
        end
    
        if self.cur_spend_list.same_list[index][element] == nil then
            self.cur_spend_list.same_list[index][element] = {}
        end

        self.cur_spend_list.same_list[index][element][star_num] = {}
    end

    if select_list then
        for _, select_data in ipairs(select_list) do
            if select_data.is_card then
                table.insert(self.cur_spend_list.same_list[index][element][star_num], select_data)
            else
                if select_data.beast_data then
                    local beast_data = select_data.beast_data
                    if is_special then
                        local temp_data = ControlBeastsWGData.Instance:CreateSelectBeastData(beast_data, select_data.is_egg)
                        table.insert(self.cur_spend_list.special_list, temp_data)
                    else
                        local temp_data = ControlBeastsWGData.Instance:CreateSelectBeastData(beast_data, select_data.is_egg)
                        table.insert(self.cur_spend_list.same_list[index][element][star_num], temp_data)
                    end
                end
            end
        end
    end

    self:FlushComposeButtonStatus()
    self:FlushComposeBeastSpendMessage()
end

function ControlBeastsWGView:FlushComposeViewCallBack(param_t)
    -- 初始化两个表
    self.cur_spend_list = {}
    self.cur_spend_list.special_list = {}
    self.cur_spend_list.same_list = {}
    self.node_list.compose_batch_red:CustomSetActive(ControlBeastsWGData.Instance:GetBeastsComposeRemind() > 0)
    self:FlushComposeBeastsMessage()
end


---刷新详情信息
function ControlBeastsWGView:FlushComposeBeastsMessage()
    self:FlushComposeBeastModel()
    self:FlushComposeBeastlevelMessage()
    self:FlushComposeBeastFlairMessage()
    self:FlushComposeBeastSpendMessage()
end

-- 刷新模型
function ControlBeastsWGView:FlushComposeBeastModel()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return
    end

    local server_data = self.select_beast_data.server_data
    local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(server_data.beast_id, server_data.use_skin)

    if self.compose_beast_model_res_id ~= res_id then
        self.compose_beast_model_res_id = res_id
        local bundle, asset = ResPath.GetBeastsModel(res_id)

        if self.compose_beast_model then
            self.compose_beast_model:SetMainAsset(bundle, asset)
            self.compose_beast_model:PlayRoleAction(SceneObjAnimator.Rest)
        end
    end
end

-- 刷新中间当前选中目标
function ControlBeastsWGView:FlushComposeBeastlevelMessage()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return
    end

    local server_data = self.select_beast_data.server_data
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    if beast_cfg then
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
        self.node_list.beasts_compose_color_img.image:LoadSprite(bundle, asset, function()
            self.node_list.beasts_compose_color_img.image:SetNativeSize()
        end)

        if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
            bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
            self.node_list.beasts_compose_color_img:ChangeAsset(bundle, asset)
        end

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
        local no_have_star = "a3_ty_xx_zc0"
        for k,v in pairs(self.beasts_compose_star_list) do
            v:CustomSetActive(star_res_list[k] ~= no_have_star)
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end

        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
        self.node_list.beasts_compose_element_img.image:LoadSprite(bundle, asset, function()
            self.node_list.beasts_compose_element_img.image:SetNativeSize()
        end)

        -- 刷新技能
        local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
        if map_data then
            if map_data.beast_data then
                local skill_id = map_data.beast_data.skill_id
                self.node_list.compose_beasts_skill_show:CustomSetActive(skill_id ~= 0)

                if skill_id ~= 0 then
                    local skill_level = map_data.beast_data.beast_star or 0
                    --去技能数据类查
                    local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    
                    if client_cfg then
                        self.node_list.compose_beasts_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
                    end

                    bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_jnd_%d", beast_cfg.skill_type_id))
                    self.node_list.compose_beasts_skill_di.image:LoadSprite(bundle, asset, function()
                        self.node_list.compose_beasts_skill_di.image:SetNativeSize()
                    end)
            
                    self.node_list.compose_beasts_skill_name.text.text = beast_cfg.skill_des
                end
            end
        end

        self.node_list.beasts_compose_name.text.text = beast_cfg.beast_name
        self.node_list.beasts_compose_level.text.text = server_data.beast_level
    end
end

-- 刷新右边资质预览
function ControlBeastsWGView:FlushComposeBeastFlairMessage()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return false
    end

    local server_data = self.select_beast_data.server_data
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)

    if not beast_cfg then
        return
    end

    local is_full_satr = beast_cfg.starup_beast_id == 0           --没有升星目标表示以达到满星   
    self.node_list.btn_star_up_remind:CustomSetActive(self.select_beast_data.is_can_compose)
    self.node_list.btn_quick_star_up_remind:CustomSetActive(self.select_beast_data.is_can_compose)
    self.node_list.beasts_compose_message_root:CustomSetActive(not is_full_satr)
    self.node_list.beasts_compose_full_root:CustomSetActive(is_full_satr)

    if is_full_satr then
        return
    end
    
    -- 刷新预升星可能到达资质
    local starup_beast_id = beast_cfg.starup_beast_id
    local beast_star_lv = beast_cfg and beast_cfg.beast_star or 0
    self.left_star_beast_item:SetData({item_id = server_data.beast_id, is_beast = true, bag_id = self.select_beast_data.bag_id})---这里需要其他的东西在加，看策划需求
    self.right_star_beast_item:SetData({item_id = starup_beast_id, is_beast = false})---这里需要其他的东西在加，看策划需求
    local attr_list = ControlBeastsWGData.Instance:GetBeastLevelAttrListNew(server_data.beast_id, server_data.beast_level)
    local next_attr_list = ControlBeastsWGData.Instance:GetBeastLevelAttrListNew(starup_beast_id, server_data.beast_level)

    -- 计算等级上限
    local beast_star = beast_cfg and beast_cfg.beast_star or 0
    local next_beast_star = beast_star + 1
    local level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(beast_star)
    local now_level_limit = level_limit_cfg and level_limit_cfg.beast_max_level or 0
    local next_level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(next_beast_star)
    local next_level_limit = next_level_limit_cfg and next_level_limit_cfg.beast_max_level or 0
    local level_limit_data = {}
    level_limit_data.attr_str = 0
    level_limit_data.attr_value = now_level_limit
    level_limit_data.add_value = next_level_limit - now_level_limit
    self.compose_attr_level:SetRealHideNext(level_limit_data.attr_value == 0)
    self.compose_attr_level:SetData(level_limit_data)
    self.compose_attr_level:ResetName(Language.Common.AttrNameList.strength_max_lv)
    self.node_list.left_star_beast_txt.text.text = string.format(Language.Common.StarStr, CommonDataManager.GetDaXie(beast_star))
    self.node_list.right_star_beast_txt.text.text = string.format(Language.Common.StarStr, CommonDataManager.GetDaXie(next_beast_star))

    -- 计算等级上限
    local refine_seq = beast_cfg and beast_cfg.refine_seq or -1
    self.node_list.compose_attr_flair:CustomSetActive(refine_seq ~= -1)

    if refine_seq ~= -1 then
        local flair_limit_data = {}
        flair_limit_data.is_per = true
        flair_limit_data.attr_str = 0
        local refine_level = beast_cfg.beast_star
        local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)
        local next_weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level + 1)
        local now_max = weight_cfg and weight_cfg.rand_right or 0
        local next_max = next_weight_cfg and next_weight_cfg.rand_right or 0
        flair_limit_data.attr_value = now_max
        flair_limit_data.add_value = (next_max - now_max)

        self.compose_attr_flair:SetRealHideNext(next_max == 0)
        self.compose_attr_flair:SetData(flair_limit_data)
        self.compose_attr_flair:ResetName(Language.Common.AttrNameList.flair_max_lv)
    end

    -- 计算插值算出升星提升属性
    for index, attr_data in ipairs(next_attr_list) do
        local now_data = attr_list[index]

        if attr_data.attr_str == now_data.attr_str then
            now_data.add_value = attr_data.attr_value - now_data.attr_value
        end
    end

    -- 属性值
    for i, compose_attr_cell in ipairs(self.compose_attrlist) do
        compose_attr_cell:SetVisible(attr_list[i] ~= nil)

        if attr_list[i] ~= nil then
            compose_attr_cell:SetData(attr_list[i])
        end
    end

    local group_seq = beast_cfg and beast_cfg.skill_group_seq or 0
    local best_star_skill_group = ControlBeastsWGData.Instance:GetBeastStarSkillGroupCfgBySeq(group_seq)
    self.node_list.compose_now_skill_root:CustomSetActive(best_star_skill_group ~= nil)
    self.node_list.compose_not_have_skill:CustomSetActive(best_star_skill_group == nil)

    if best_star_skill_group == nil then
        return
    end

    local star_list = {}
    local star_skill_list = {}
    local desc_table = {}

    if best_star_skill_group then
        if best_star_skill_group.star_group and best_star_skill_group.star_group ~= "" then
            local star_str_list = Split(best_star_skill_group.star_group, "|")
            for _, str in ipairs(star_str_list) do
                local star = tonumber(str) or 0
                table.insert(star_list, star)
            end
        end
    
        if best_star_skill_group.star_skill_group and best_star_skill_group.star_skill_group ~= "" then
            local star_group_str_list = Split(best_star_skill_group.star_skill_group, "|")
            for _, str in ipairs(star_group_str_list) do
                local star_skill = str
                table.insert(star_skill_list, star_skill)
            end
        end
    end

    for i, star in ipairs(star_list) do
        local info = {}
        info.star = star
        local color = beast_star_lv < star and "#e5e5e5" or nil 
        info.desc = star_skill_list and star_skill_list[i] or ""

        if color ~= nil then
            local str = RemoveRichTextColorTags(info.desc)
            info.desc = ToColorStr(str, color)
        end

        table.insert(desc_table, info)
    end

    for i, now_skill_cell in ipairs(self.compose_now_skill_list) do
        now_skill_cell:SetVisible(desc_table[i] ~= nil)

        if desc_table[i] ~= nil then
            now_skill_cell:SetData(desc_table[i])
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.compose_now_skill_list.rect)
end

-- 刷新消耗物
function ControlBeastsWGView:FlushComposeBeastSpendMessage()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return
    end

    local server_data = self.select_beast_data.server_data
    -- 未缔结圣兽特殊展示
    local is_need_show_contract = self.select_beast_data.is_holy_beast and server_data.holy_spirit_link_index == -1
    self.node_list["spend_panel"]:SetActive(not is_need_show_contract)
    self.node_list["contract_panel"]:SetActive(is_need_show_contract)
    if is_need_show_contract then
        self.compose_holy_beast_item:SetData(self.select_beast_data)
        local can_contract = ControlBeastsWGData.Instance:GetHolyBeastCanContract(self.select_beast_data)
        self.node_list["compose_remind_can_contract"]:SetActive(can_contract)
        return
    end

    -- 圣兽使用链接对象的升星素材
    local aim_beast_id = self.select_beast_data.link_beast_data.server_data.beast_id
    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(aim_beast_id)
    local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(aim_beast_id)
    if not starup_beast_ids then
        return
    end

    -- 特殊一个
    local empty = {}
    local first_index = 1
    local find_index = 1
    local now_sp_num = 0
    local not_get_tips = ""
    self.node_list.beasts_compose_aim_root:CustomSetActive(false)

    for i, v in ipairs(self.beasts_cell_list) do
        if v and v.root then
            v.root:CustomSetActive(false)
        end
    end

    for i, data in ipairs(starup_beast_ids) do
        if first_index > #self.beasts_cell_list then
            break
        end

		if data.beast_id ~= nil then	-- 特殊id
            self.node_list.beasts_compose_aim_root:CustomSetActive(true)
            local list = (self.cur_spend_list or empty).special_list or {}
            local aim_data = list[first_index] or nil
            self.beasts_compose_aim:ResetCache()
            self.beasts_compose_aim:SetIsSpecial(true)
    
            if aim_data ~= nil then
                aim_data.item_id = data.beast_id
                self.beasts_compose_aim:SetData(aim_data)
            else
                self.beasts_compose_aim:SetData({item_id = data.beast_id})
            end

            now_sp_num = list and #list or 0
            local color = now_sp_num >= data.num and COLOR3B.GREEN or COLOR3B.RED
            local progress_str = ToColorStr(string.format("%d/%d", now_sp_num, data.num), color)
            self.node_list.beasts_compose_aim_txt.text.text = progress_str

            if now_sp_num < data.num then
                local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(data.beast_id)
                if cfg then
                    local str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[cfg.beast_color], data.num, string.format(Language.ContralBeasts.SelectTips2, cfg.beast_star, cfg.beast_name))
                    not_get_tips = string.format("%s(%s)", str, progress_str) 
                end
            end
		elseif data.element ~= nil then
            local list = ((((self.cur_spend_list or empty).same_list or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT] or empty)[data.element] or empty)[data.star] or {}
            local aim_data = list[first_index] or nil
            
            if self.beasts_cell_list[find_index] then
                self.beasts_cell_list[find_index].root:CustomSetActive(true)
                self.beasts_cell_list[find_index].cell:ResetCache()
                self.beasts_cell_list[find_index].cell:SetStar(data.star)
                self.beasts_cell_list[find_index].cell:SetIsElement(true, data.element)

                if aim_data ~= nil then
                    self.beasts_cell_list[find_index].cell:SetData(aim_data)
                else
                    self.beasts_cell_list[find_index].cell:SetData({item_id = BEASTS_STAR_VIRTUAL_ITEM[data.star]})
                end
            end
            
            now_sp_num = list and #list or 0
            local color = now_sp_num >= data.num and COLOR3B.GREEN or COLOR3B.RED
            local progress_str = ToColorStr(string.format("%d/%d", now_sp_num, data.num), color)
            self.beasts_cell_list[find_index].cell_txt.text.text = progress_str

            if now_sp_num < data.num then
                local str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[aim_cfg.beast_color], data.num, string.format(Language.ContralBeasts.SelectTips1, data.star, Language.ContralBeasts.GMTypeList[data.element]))
                not_get_tips = string.format("%s(%s)", str, progress_str) 
            end

            find_index = find_index + 1
		elseif data.star ~= nil then
            local list = ((((self.cur_spend_list or empty).same_list or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR] or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT] or empty)[data.star] or {}
            local aim_data = list[first_index] or nil
            
            if self.beasts_cell_list[find_index] then
                self.beasts_cell_list[find_index].root:CustomSetActive(true)
                self.beasts_cell_list[find_index].cell:ResetCache()
                self.beasts_cell_list[find_index].cell:SetStar(data.star)

                if aim_data ~= nil then
                    self.beasts_cell_list[find_index].cell:SetData(aim_data)
                else
                    self.beasts_cell_list[find_index].cell:SetData({item_id = BEASTS_STAR_VIRTUAL_ITEM[data.star]})
                end
            end

            now_sp_num = list and #list or 0
            local color = now_sp_num >= data.num and COLOR3B.GREEN or COLOR3B.RED
            local progress_str = ToColorStr(string.format("%d/%d", now_sp_num, data.num), color)
            self.beasts_cell_list[find_index].cell_txt.text.text = progress_str

            if now_sp_num < data.num then
                local str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[aim_cfg.beast_color], data.num, string.format(Language.ContralBeasts.SelectTips1, data.star, ""))
                not_get_tips = string.format("%s(%s)", str, progress_str) 
            end

            find_index = find_index + 1
        elseif data.chip_id ~= nil then
            self.beasts_cell_list[find_index].root:CustomSetActive(true)
            now_sp_num = ItemWGData.Instance:GetItemNumInBagById(data.chip_id)
            self.beasts_cell_list[find_index].cell:ResetCache()
            self.beasts_cell_list[find_index].cell:SetIsChip(true)
            self.beasts_cell_list[find_index].cell:SetData({item_id = data.chip_id, is_chip = true})
            
            local chip_name = ItemWGData.Instance:GetItemName(data.chip_id)
            local color = now_sp_num >= data.num and COLOR3B.GREEN or COLOR3B.RED
            local progress_str = ToColorStr(string.format("%d/%d", now_sp_num, data.num), color)
            self.beasts_cell_list[find_index].cell_txt.text.text = progress_str

            if now_sp_num < data.num then
                local str = string.format(Language.MountPetEquip.ShengPinNeedDes, ITEM_COLOR[aim_cfg.beast_color], data.num, chip_name)
                not_get_tips = string.format("%s(%s)", str, progress_str) 
            end
            
            find_index = find_index + 1
		end
	end

    self.node_list.beasts_compose_aim_tips.text.text = not_get_tips
    self.node_list.btn_star_up:CustomSetActive(not_get_tips == "")
    self.node_list.btn_quick_star_up:CustomSetActive(not_get_tips ~= "")
end

-- 刷新按钮状态
function ControlBeastsWGView:FlushComposeButtonStatus(can_spend)
    if not can_spend then
        can_spend = self:FlushComposeCanSpenStatus()
        self.can_spend = can_spend
    end
end

-- 获取新的是否可合成状态
function ControlBeastsWGView:FlushComposeCanSpenStatus()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) or not self:CheckHaveDataAndServerData(self.select_beast_data.link_beast_data)  then
        return false
    end

    if (not self.cur_spend_list) or (not self.cur_spend_list.special_list) or (not self.cur_spend_list.same_list) then
        return false
    end

    local server_data = self.select_beast_data.link_beast_data.server_data
    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(server_data.beast_id)
    local empty = {}

    if aim_cfg and starup_beast_ids then
        -- 检测是否满足条件
        local final_check_satisfy_fun = function()
            for i, data in ipairs(starup_beast_ids) do
                if data.beast_id ~= nil and data.num ~= #self.cur_spend_list.special_list then
                    return false
                elseif data.element ~= nil and data.star ~= nil then
                    local list = ((((self.cur_spend_list or empty).same_list or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT] or empty)[data.element] or empty)[data.star] or nil
                    if list == nil or #list ~=  data.num then
                        return false
                    end      
                elseif data.star ~= nil then
                    local list = ((((self.cur_spend_list or empty).same_list or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR] or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT] or empty)[data.star] or nil
                    if list == nil or #list ~=  data.num then
                        return false
                    end       
                end
            end

            return true
        end

        -- 检测是否存在碎片
        local is_satisfy_chip = ControlBeastsWGData.Instance:CheckComposeSatisfyChipFun(starup_beast_ids)

        if is_satisfy_chip and final_check_satisfy_fun() then
            return true
        end
    end

    return false
end
--------------------------------------------------------------------------------------
-- 升星
function ControlBeastsWGView:ComposeStartUpClick()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) or not self:CheckHaveDataAndServerData(self.select_beast_data.link_beast_data) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip9)
        return
    end

    if (not self.cur_spend_list) or (not self.cur_spend_list.same_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip10)
        return
    end

    local server_data = self.select_beast_data.link_beast_data.server_data --圣兽使用连接对象
    local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(server_data.beast_id)
    local empty = {}

    if aim_cfg and starup_beast_ids then
       -- 检测是否满足条件
       local final_check_satisfy_fun = function()
            for i, data in ipairs(starup_beast_ids) do
                if data.beast_id ~= nil and data.num ~= #self.cur_spend_list.special_list then
                    return false
                elseif data.element ~= nil and data.star ~= nil then
                    local list = ((((self.cur_spend_list or empty).same_list or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT] or empty)[data.element] or empty)[data.star] or nil
                    if list == nil or #list ~=  data.num then
                        return false
                    end      
                elseif data.star ~= nil then
                    local list = ((((self.cur_spend_list or empty).same_list or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR] or empty)[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT] or empty)[data.star] or nil
                    if list == nil or #list ~=  data.num then
                        return false
                    end       
                end
            end

            return true
        end

        -- 检测是否存在碎片
        local is_satisfy_chip = ControlBeastsWGData.Instance:CheckComposeSatisfyChipFun(starup_beast_ids)
        if (not final_check_satisfy_fun()) or (not is_satisfy_chip) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip10)
            return
        end
    end
    local target_bag_id = self.select_beast_data.is_holy_beast and self.select_beast_data.link_beast_data.bag_id or self.select_beast_data.bag_id
    local server_data = ControlBeastsWGData.Instance:CreateComposeData({is_egg = false, bag_id = target_bag_id}, self.cur_spend_list, starup_beast_ids)
    ControlBeastsWGCtrl.Instance:SendCSRoleBeastCompose(server_data.aim_beast, server_data.compose_list)
end

-- 升星(一键添加)
function ControlBeastsWGView:ComposeQuickStartUpClick()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) or not self:CheckHaveDataAndServerData(self.select_beast_data.link_beast_data) then
        return
    end

    local server_data = self.select_beast_data.link_beast_data.server_data --圣兽使用连接对象
    local is_compose, compose_list = ControlBeastsWGData.Instance:GetSingleBeastComposeList(self.select_beast_data.link_beast_data.bag_id, server_data.beast_id, true)
    self.cur_spend_list = compose_list or {}
    self.can_spend = is_compose

    if not is_compose then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip10)
    end

    self:FlushComposeButtonStatus(self.can_spend)
    self:FlushComposeBeastSpendMessage()
end

-- 进化预览
function ControlBeastsWGView:ComposeEvolvePreview()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip9)
        return
    end

    local server_data = self.select_beast_data.server_data
    ControlBeastsWGCtrl.Instance:OpenBeastsEvolvePreview(server_data.beast_id)
end

-- 批次升星
function ControlBeastsWGView:OpenBeastsQuickSpView()
    ControlBeastsWGCtrl.Instance:OpenBeastsQuickSpView()
end

-- 技能提示
function ControlBeastsWGView:ComposeSkillTips()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return
    end

    local server_data = self.select_beast_data.server_data
    ControlBeastsWGData.Instance:ShowBeastSkill(server_data.beast_id)
end

----------------------------------灵兽升星材料item-----------------------
BeststsStarUpItemRender = BeststsStarUpItemRender or BaseClass(BaseRender)
function BeststsStarUpItemRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetClickCallBack(BindTool.Bind1(self.OnClick, self))
        self.beast_item:SetIsShowTips(false)
	end

    if not self.item_aim_cell then
		self.item_aim_cell = ItemCell.New(self.node_list.item_aim_cell)
        self.item_aim_cell:SetClickCallBack(BindTool.Bind1(self.OnClick, self))
        self.item_aim_cell:SetIsShowTips(false)
	end
end

-- 点击回调
function BeststsStarUpItemRender:OnClick(isOn)
    BaseRender.OnClick(self, isOn)
end

function BeststsStarUpItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end

    if self.item_aim_cell then
		self.item_aim_cell:DeleteMe()
		self.item_aim_cell = nil
	end

    self.is_special = nil
    self.is_element = nil
    self.element = nil
    self.is_chip = nil
    self.star_num = nil
end

-- 重置数据
function BeststsStarUpItemRender:ResetCache()
    self.item_aim_cell:SetIsShowTips(false)
    self.is_special = nil
    self.is_element = nil
    self.element = nil
    self.is_chip = nil
    self.star_num = nil

    if self.node_list and self.node_list.add_btn then
        self.node_list.add_btn:CustomSetActive(true)
    end
end

-- 设置为特殊位置
function BeststsStarUpItemRender:SetIsSpecial(is_special)
    self.is_special = is_special
end

-- 设置为特殊位置
function BeststsStarUpItemRender:GetIsSpecial()
    return self.is_special
end

-- 设置为特殊元素
function BeststsStarUpItemRender:SetIsElement(is_element, element)
    self.is_element = is_element
    self.element = element
end

-- 设置为特殊元素
function BeststsStarUpItemRender:GetIsElement()
    return self.is_element, self.element
end

-- 设置为碎片
function BeststsStarUpItemRender:SetIsChip(is_chip)
    self.is_chip = is_chip

    if self.is_chip then
        if self.node_list and self.node_list.add_btn then
            self.node_list.add_btn:CustomSetActive(false)
        end

        self.item_aim_cell:SetIsShowTips(true)
    end
end

-- 设置为碎片
function BeststsStarUpItemRender:GetIsChip()
    return self.is_chip
end

-- 设置星级
function BeststsStarUpItemRender:SetStar(star_num)
    self.star_num = star_num
end

-- 设置星级
function BeststsStarUpItemRender:GetStarNum()
    return self.star_num
end

function BeststsStarUpItemRender:OnFlush()
    if IsEmptyTable(self.data) then
       return 
    end

    self.node_list.item_pos:CustomSetActive(self.data.server_data ~= nil)
    self.node_list.add_btn:CustomSetActive(self.data.server_data == nil and (not self.data.is_chip) and (not self.data.is_card))
    self.node_list.item_aim_cell:CustomSetActive(self.data.server_data == nil)

    if self.data.server_data then
        self.beast_item:SetData({item_id = self.data.beast_id, is_beast = not self.data.is_egg})---这里需要其他的东西在加，看策划需求
    else
        self.item_aim_cell:SetData({item_id = self.data.item_id})---这里需要其他的东西在加，看策划需求
        self.item_aim_cell:SetBeastStarMessage(self.star_num)
        self.item_aim_cell:SetBeastElementMessage(self.element)
  
        if self.star_num == nil then
            local cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.item_id)
            if cfg then
                self.item_aim_cell:SetBeastStarMessage(cfg.beast_star)
            end
        end
    end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end
