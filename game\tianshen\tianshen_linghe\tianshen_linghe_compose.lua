-- 已废弃未执行
function TianShenView:CPInitView()
    if not self.cp_target_item then
        self.cp_target_item = ItemCell.New(self.node_list["cp_target_item"])
    end

    if not self.cp_target_linghe then
        self.cp_target_linghe = BaseLingHeCell.New(nil, self.node_list["cp_target_linghe"])
    end

    if not self.cp_stuff_item_list then
        self.cp_stuff_item_list = {}
        for i = 1, 4 do
            local cell = ItemCell.New(self.node_list["cp_stuff_item" .. i])
            if i > 1 then
                cell:ClearData()
                cell:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
                cell:SetItemIcon(ResPath.GetCommonImages("a2_ty_suo"))
            end

            self.cp_stuff_item_list[i] = cell
        end
    end

    if not self.cp_stuff_linghe_1 then
        self.cp_stuff_linghe_1 = BaseLingHeCell.New(nil, self.node_list["cp_stuff_linghe1"])
    end

    -- 标签列表
    self:CreatCPToggleList()

    XUI.AddClickEventListener(self.node_list["btn_cp_compose"], BindTool.Bind(self.OnClickCPCompose, self))
    XUI.AddClickEventListener(self.node_list["btn_quick_compose"], BindTool.Bind(self.OnClickCPCompose, self, true))
end

-- 创建标签列表
function TianShenView:CreatCPToggleList()
    -- print_error("---创建标签---")
    if nil == self.cp_accordion_list then
        self.cp_small_type_list = {}
        self.cp_accordion_list = {}
        self.cp_big_type_toggle_list = {}
        self.load_cp_cell_complete = false
        local content_node = self.node_list["cp_toggle_content"]
        local toggle_list = TianShenLingHeWGData.Instance:GetComposeToggleList()
        local toggle_length = #toggle_list
        for i = 1, toggle_length do
            self.cp_accordion_list[i] = content_node:FindObj("cp_small_type_list" .. i)
            local big_btn_cell = TSLHCPBigTypeToggleRender.New(content_node:FindObj("cp_big_type_toggle" .. i))
            big_btn_cell:SetIndex(i)
            big_btn_cell:SetOnlyClickCallBack(BindTool.Bind(self.OnClickCPBigTypeToggle, self))
            self.cp_big_type_toggle_list[i] = big_btn_cell

            local small_num = toggle_list[i].child_list and #toggle_list[i].child_list or 0
            self:LoadCPSmallCellList(i, toggle_length, small_num)
        end
    end
end

-- 加载子标签
function TianShenView:LoadCPSmallCellList(index, big_type_num, small_num)
    -- print_error("---加载子标签---", index, big_type_num, small_num)
    local res_async_loader = AllocResAsyncLoader(self, "linghe_cp_small" .. index)
	res_async_loader:Load("uis/view/tianshen_linghe_ui_prefab", "linghe_compose_list_cell", nil, function(new_obj)
		local item_vo_list = {}
		for i = 1, small_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.cp_accordion_list[index].transform, false)

			local item_render = TSLHCPSmallTypeRender.New(obj)
            item_render:SetClickCallBack(BindTool.Bind(self.OnClickCPSmallType, self))
			item_vo_list[i] = item_render

			if index == big_type_num and i == small_num then
				self.load_cp_cell_complete = true
			end
		end

		self.cp_small_type_list[index] = item_vo_list
		if self.load_cp_cell_complete then
            -- 设置数据
            self:FlushCPToggleAllData()

            -- 加载完 是否要选择标签
            if self.cp_flush_wait_flag then
                self:CPSelectToggle()
            end
		end
	end)
end

function TianShenView:CPReleaseCallBack()
    self.cp_accordion_list = nil
    self.load_cp_cell_complete = nil
    self.cp_flush_wait_flag = nil
    self.cp_force_big_type = nil
    self.cp_force_small_type = nil
    self.cp_select_big_type = nil
    self.cp_select_small_type = nil
    self.cp_stop_cur = nil

    if self.cp_target_item then
        self.cp_target_item:DeleteMe()
        self.cp_target_item = nil
    end

    if self.cp_target_linghe then
        self.cp_target_linghe:DeleteMe()
        self.cp_target_linghe = nil
    end

    if self.cp_stuff_item_list ~= nil then
        for k, v in pairs(self.cp_stuff_item_list) do
            v:DeleteMe()
        end
        self.cp_stuff_item_list = nil
    end

    if self.cp_stuff_linghe_1 then
        self.cp_stuff_linghe_1:DeleteMe()
        self.cp_stuff_linghe_1 = nil
    end

    if self.cp_big_type_toggle_list ~= nil then
        for k, v in ipairs(self.cp_big_type_toggle_list) do
            v:DeleteMe()
        end
        self.cp_big_type_toggle_list = nil
    end

    if self.cp_small_type_list then
        for k, v in pairs(self.cp_small_type_list) do
            for k1, v1 in pairs(v) do
                v1:DeleteMe()
            end
            self.cp_small_type_list[k] = nil
        end
        self.cp_small_type_list = nil
    end
end

function TianShenView:CPShowIndexCallBack()
end

-- 刷新标签数据
function TianShenView:FlushCPToggleAllData()
    -- print_error("---刷新标签数据---")
    if IsEmptyTable(self.cp_big_type_toggle_list) then
        return
    end

    if IsEmptyTable(self.cp_small_type_list) then
        return
    end

    local toggle_list = TianShenLingHeWGData.Instance:GetComposeToggleList()
    for k,v in pairs(toggle_list) do
        self.cp_big_type_toggle_list[k]:SetData(v)
        local small_list = self.cp_small_type_list[k]
        local suit_list_data = v.child_list
        for k1,v1 in pairs(small_list) do
            v1:SetData(suit_list_data[k1])
        end
    end
end

-- 标签选择
function TianShenView:CPSelectToggle(force_big_type, force_small_type, stop_cur)
    -- print_error("---标签选择---", force_big_type, force_suit_type, force_slot_index)
    if force_big_type then
        self.cp_force_big_type = force_big_type
    end

    if force_small_type then
        self.cp_force_small_type = force_small_type
    end

    if not self.load_cp_cell_complete then
        return
    else
        self.cp_flush_wait_flag = nil
    end

    local jump_index
    if self.cp_force_big_type then
        jump_index = self.cp_force_big_type
        self.cp_force_big_type = nil
    elseif stop_cur then
        self.cp_stop_cur = true
        jump_index = self.cp_select_big_type
    else
        local toggle_list = TianShenLingHeWGData.Instance:GetComposeToggleList()
        for k,v in ipairs(toggle_list) do
            if v.is_remind then                           -- 跳红点
                jump_index = k
                break
            end
        end

        jump_index = jump_index or self.cp_select_big_type
    end

    jump_index = jump_index or 1
    if self.cp_select_big_type ~= jump_index then
        self.cp_big_type_toggle_list[jump_index]:SetAccordionElementState(true)
    else
        self:OnClickCPBigTypeToggle(self.cp_big_type_toggle_list[jump_index])
    end
end

-- 大标签回调
function TianShenView:OnClickCPBigTypeToggle(cell)
    -- print_error("【----点击 大 回调-----】：", cell:GetIndex(), self.cp_stop_cur)
	if cell == nil then
		return
	end

    local index = cell:GetIndex()
    local data = cell:GetData()
    if data == nil then
        return
    end

    self.cp_select_big_type = index

    local jump_small_index
    if self.cp_force_small_type then
        jump_small_index = self.cp_force_small_type
        self.cp_force_small_type = nil
        self.cp_stop_cur = nil
    elseif self.cp_stop_cur then
        jump_small_index =  self.cp_select_small_type
        self.cp_stop_cur = nil
    else
        for k,v in ipairs(data.child_list) do
            if v.is_remind then                               -- 跳红点
                jump_small_index = k
                break
            end
        end

        jump_small_index = jump_small_index or self.cp_select_small_type
    end

    jump_small_index = jump_small_index or 1
    local small_type_cell = ((self.cp_small_type_list or {})[index] or {})[jump_small_index]
	if small_type_cell then
        small_type_cell:OnClick()
	end
end

-- 子标签回调
function TianShenView:OnClickCPSmallType(cell)
    -- print_error("【----点击 子 回调-----】：", cell:GetData().type, cell:GetData().name)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    local type = data.type
    self.cp_select_small_type = type
    local list = self.cp_small_type_list[self.cp_select_big_type]
    if list then
        for k,v in pairs(list) do
            v:OnSelectSuitChange(type)
        end
    end

    self:FlushComposeView()
end

function TianShenView:FlushComposeView()
    local big_type = self.cp_select_big_type
    local small_type = self.cp_select_small_type
    if big_type == nil or small_type == nil then
        return
    end

    local traget_cfg = TianShenLingHeWGData.Instance:GetItemComposeCfgByType(big_type, small_type)
    if traget_cfg == nil then
        return
    end

    local target_id = traget_cfg.product_id
    local stuff_id = traget_cfg.stuff_id
    local had_num = TianShenLingHeWGData.Instance:GetItemNum(stuff_id)
    local stuff_count = traget_cfg.stuff_count
    local target_data = {item_id = target_id}
    local stuff_data = {item_id = stuff_id}
    local target_is_linghe = TianShenLingHeWGData.Instance:GetLingGHeCfgByItemId(target_id) ~= nil
    local stuff_is_linghe = TianShenLingHeWGData.Instance:GetLingGHeCfgByItemId(stuff_id) ~= nil
    -- 目标
    self.node_list["cp_target_linghe"]:SetActive(target_is_linghe)
    self.node_list["cp_target_item"]:SetActive(not target_is_linghe)
    self.node_list["cp_linghe_attr_desc"]:SetActive(target_is_linghe)
    self.node_list["cp_item_attr_desc"]:SetActive(not target_is_linghe)
    local item_cfg = ItemWGData.Instance:GetItemConfig(target_id)
    if item_cfg then
        self.node_list["cp_item_name"].text.text = item_cfg.name
        self.node_list["cp_item_attr_desc"].text.text = item_cfg.description
    end

    if target_is_linghe then
        self.cp_target_linghe:SetData(target_data)
        self.node_list["cp_linghe_attr_desc"].text.text = ItemShowWGData.Instance:OnlyGetAttrDesc(target_id, COLOR3B.BLUE_TITLE, COLOR3B.DEFAULT_NUM)
    else
        self.cp_target_item:SetData(target_data)
    end

    -- 材料
    self.node_list["cp_stuff_linghe1"]:SetActive(stuff_is_linghe)
    self.node_list["cp_stuff_item1"]:SetActive(not stuff_is_linghe)
    local stuff_num_color = had_num >= stuff_count and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    local stuff_num_str = had_num .. "/" .. stuff_count
    if stuff_is_linghe then
        self.cp_stuff_linghe_1:SetData(stuff_data)
        self.node_list["cp_stuff_linghe1_desc"].text.text = ToColorStr(stuff_num_str, stuff_num_color)
    else
        self.cp_stuff_item_list[1]:SetData(stuff_data)
        self.cp_stuff_item_list[1]:SetRightBottomColorText(stuff_num_str, stuff_num_color)
		self.cp_stuff_item_list[1]:SetRightBottomTextVisible(true)
    end

    self.node_list["btn_cp_compose_remind"]:SetActive(had_num >= stuff_count)
    self.node_list["btn_quick_compose_remind"]:SetActive(had_num >= stuff_count)
end

function TianShenView:OnClickCPCompose(Quick_compose)
    local big_type = self.cp_select_big_type
    local small_type = self.cp_select_small_type
    if big_type == nil or small_type == nil then
        return
    end

    local traget_cfg = TianShenLingHeWGData.Instance:GetItemComposeCfgByType(big_type, small_type)
    if traget_cfg == nil then
        return
    end

    local had_num = TianShenLingHeWGData.Instance:GetItemNum(traget_cfg.stuff_id)
    if had_num >= traget_cfg.stuff_count then
        self:ShowComposeSucessEffect()
    end

    if Quick_compose then
        TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.QUICKCOMPOSE, traget_cfg.product_id)
    else
        TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.COMPOSE, traget_cfg.product_id)
    end
end

-- 合成成功特效
function TianShenView:ShowComposeSucessEffect()
	if self.node_list["cp_success_root"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true,
                                        pos = Vector2(0, 0), parent_node = self.node_list["cp_success_root"]})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end



--======================================================================
-- 大类型toggle
--======================================================================
TSLHCPBigTypeToggleRender = TSLHCPBigTypeToggleRender or BaseClass(BaseRender)
function TSLHCPBigTypeToggleRender:__init()
    self.view.accordion_element:AddClickListener(BindTool.Bind(self.OnClickAccordion, self))
end

function TSLHCPBigTypeToggleRender:__delete()
end

function TSLHCPBigTypeToggleRender:SetOnlyClickCallBack(callback)
    self.click_callback = callback
end

function TSLHCPBigTypeToggleRender:OnClickAccordion(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function TSLHCPBigTypeToggleRender:SetAccordionElementState(is_on)
    self.view.accordion_element.isOn = is_on
end

function TSLHCPBigTypeToggleRender:OnFlush()
	if self.data == nil or not self.data.is_show then
        self.view:SetActive(false)
		return
	end

    self.node_list.normal_text.text.text = self.data.name
    self.node_list.select_text.text.text = self.data.name
    self.node_list["remind"]:SetActive(self.data.is_remind)
    self.view:SetActive(true)
end


--======================================================================
-- 小类型toggle
--======================================================================
TSLHCPSmallTypeRender = TSLHCPSmallTypeRender or BaseClass(BaseRender)
function TSLHCPSmallTypeRender:__init()
end

function TSLHCPSmallTypeRender:__delete()
end

function TSLHCPSmallTypeRender:OnFlush()
	if self.data == nil then
        self.view:SetActive(false)
		return
	end

    self.node_list["normal_text"].text.text = self.data.name
    self.node_list["select_text"].text.text = self.data.name
    self.node_list["remind"]:SetActive(self.data.is_remind)
    self.view:SetActive(true)
end

function TSLHCPSmallTypeRender:OnSelectSuitChange(suit_type)
    if self.data == nil then
        return
    end

    local is_select = suit_type == self.data.type
    self.node_list["normal"]:SetActive(not is_select)
    self.node_list["select"]:SetActive(is_select)
end