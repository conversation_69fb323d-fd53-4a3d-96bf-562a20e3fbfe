--[[
26105	上线·先锋
26106	上线·传奇
26107	上线·统帅
26108	击杀·先锋
26109	击杀·传奇
26110	击杀·统帅
26111	复活·先锋
26112	复活·传奇
26113	复活·统帅
]]

CustomizedRumorsWGData = CustomizedRumorsWGData or BaseClass()

function CustomizedRumorsWGData:__init()
	if CustomizedRumorsWGData.Instance ~= nil then
		ErrorLog("[CustomizedRumorsWGData] Attemp to create a singleton twice !")
	end
	CustomizedRumorsWGData.Instance = self

	local chuanwen_cfg = ConfigManager.Instance:GetAutoConfig("diy_chuanwen_cfg_auto")
	self.rumor_show_info_cfg = chuanwen_cfg.rumor_show_info
	self.rumor_random_table_cfg = chuanwen_cfg.rumor_random_table
	self.regional_barrage_random_table_cfg = chuanwen_cfg.regional_barrage_random_table
	self.grade_cfg = chuanwen_cfg.grade
	self.other_cfg = chuanwen_cfg.other[1]
	self.desc_cfg = ListToMap(chuanwen_cfg.desc, "desc_type", "gear_seq")
	self.desc_seq_cfg = ListToMap(chuanwen_cfg.desc, "seq")
	self.rumor_preset_content_cfg = ListToMapList(chuanwen_cfg.rumor_preset_content, "rumor_type", "gear_seq")
	self.desc_cost_item_cfg = ListToMap(chuanwen_cfg.desc, "cost_itemid")

	RemindManager.Instance:Register(RemindName.CustomizedRumors, BindTool.Bind(self.GetRumorViewRemind, self))
	RemindManager.Instance:Register(RemindName.CustomizedRumorsBroadcast, BindTool.Bind(self.GetRumorBroadcastViewRemind, self))

	self.rumor_remind = false
	self.rumor_remind_tab = {}

	self.broadcast_remind = false
	self.broadcast_remind_tab = {}
	self.rumor_active_item_cache = {}
	self.desc_active_flag = {}
	self.rmb_recharge = 0
	self.choose_desc = {}
	self.desc_state = {}
	self.desc_str_tab = {}

	self:InitRumorDataCache()
end

function CustomizedRumorsWGData:__delete()
	self.rumor_active_item_cache = nil
	RemindManager.Instance:UnRegister(RemindName.CustomizedRumors)
	RemindManager.Instance:UnRegister(RemindName.CustomizedRumorsBroadcast)
	CustomizedRumorsWGData.Instance = nil
end

function CustomizedRumorsWGData:InitRumorDataCache()
	for k, v in pairs(self:GetRumorTypeShowInfoList()) do
		for i, u in pairs(v) do
			self.rumor_active_item_cache[u.cost_itemid] = u.cost_itemid
		end
	end
end

function CustomizedRumorsWGData:IsRumorActiveItem(change_item_id)
	return nil ~= self.rumor_active_item_cache[change_item_id]
end

function CustomizedRumorsWGData:GetABarrageMessage()
	return {desc_content = (self.rumor_random_table_cfg[math.random(1, #self.rumor_random_table_cfg)] or {}).conect or "",
			need_random_color = false,
			color_data_list = ITEM_COLOR_DARK}
end

function CustomizedRumorsWGData:GetBullScreenBarrageMessage()
	return {desc_content = (self.regional_barrage_random_table_cfg[math.random(1, #self.regional_barrage_random_table_cfg)] or {}).conect or "",
			need_random_color = false,
			move_speed = 200,
			color_data_list = ITEM_COLOR_DARK}
end

function CustomizedRumorsWGData:GetBullScreenBarrageMessageDesc()
	return (self.regional_barrage_random_table_cfg[math.random(1, #self.regional_barrage_random_table_cfg)] or {}).conect
end

function CustomizedRumorsWGData:GetRumorTypeShowInfoList()
	return self.desc_cfg
end

function CustomizedRumorsWGData:GetRumorGearShowInfoList(rumor_type)
	return self.desc_cfg[rumor_type]
end

function CustomizedRumorsWGData:GetRumorGearShowInfo(rumor_type, gear_seq)
	return (self.desc_cfg[rumor_type] or {})[gear_seq] 
end

function CustomizedRumorsWGData:GetRumorShowInfoByRumorType(rumor_type)
	return self.rumor_show_info_cfg[rumor_type]
end

function CustomizedRumorsWGData:GetRumorBRNavDataList()
	return self.grade_cfg
end

function CustomizedRumorsWGData:GetBroadcastGradeCfgByGrade(grade)
	return self.grade_cfg[grade]
end

function CustomizedRumorsWGData:GetOtherCfg()
	return self.other_cfg
end

function CustomizedRumorsWGData:GetRumorPresetContentList(rumor_type, gear_seq)
	return (self.rumor_preset_content_cfg[rumor_type] or {})[gear_seq]
end

function CustomizedRumorsWGData:GetRumorPresetContentData(rumor_type, gear_seq, desc_seq)
	return ((self.rumor_preset_content_cfg[rumor_type] or {})[gear_seq] or {})[desc_seq]
end

-- 档位是否激活
function CustomizedRumorsWGData:IsRumorActiveByRumorType(seq)
	return self.desc_active_flag[seq] == 1
end

-- 大传闻类型是否激活
function CustomizedRumorsWGData:IsRumorTypeActive(rumor_type)
	local gear_list = self:GetRumorGearShowInfoList(rumor_type)
	local active = false

	if not IsEmptyTable(gear_list) then
		for k, v in pairs(gear_list) do
			if self:IsRumorActiveByRumorType(v.seq) then
				return true
			end
		end
	end

	return active
end

-- 是否定制 ，定制内容
function CustomizedRumorsWGData:IsCustomizedRumorByRumorType(rumor_type)
	return self.desc_state[rumor_type] or DIYCHUANWEN_OPERA_STATUS.NORMAL
end

-- 获取传闻显示内容
function CustomizedRumorsWGData:GetCustomizedRumorDescContent(rumor_type, gear_seq)
	-- 如果有定制 显示定制 如果没有 选取默认
	local dingzhi_seq =  self:GetRumorChooseRumorType(rumor_type)
	local desc_cfg = self:GetRumorGearShowInfo(rumor_type, gear_seq)

	local desc = ""
	local server_id = RoleWGData.Instance:GetCurServerId()
	local role_name = RoleWGData.Instance:GetAttr("name")
	-- 击杀传闻特殊处理
	if rumor_type == 2 then
		local other_cfg = self:GetOtherCfg()
		desc = string.format(desc_cfg.rumor_top_desc, server_id, role_name, server_id, other_cfg.kill_name)
	else
		desc = string.format(desc_cfg.rumor_top_desc, server_id, role_name)
	end

	local top = desc
	local content = ""

	if dingzhi_seq == -1 then
		content = self:GetCustomizedRumorStr(rumor_type)
	else
		local target_seq = dingzhi_seq == 0 and 1 or dingzhi_seq
		local preset_cfg = self:GetRumorPresetContentData(rumor_type, gear_seq, target_seq)
		content = preset_cfg.desc
	end

	desc = desc .. content
	return desc, top, content
end

function CustomizedRumorsWGData:GetCustomizedRumorStr(rumor_type)
	return self.desc_str_tab[rumor_type]
end

-- 传闻当前选择的皮肤
function CustomizedRumorsWGData:GetRumorTypeSelectGearSeq(rumor_type)
	return self.choose_skin[rumor_type] or 0
end

--已选择的传闻文字下标   三种挡位 选中的模板 （默认是0，选择自己的是-1）
function CustomizedRumorsWGData:GetRumorChooseRumorType(rumor_type)
	return self.choose_desc[rumor_type]
end

-- 能否定制传闻
function CustomizedRumorsWGData:IsCanCustomizesRumorByRumorType(rumor_type)
	local status = self:IsCustomizedRumorByRumorType(rumor_type)

	-- 在正常状态下 且有一个已激活
	if status == DIYCHUANWEN_OPERA_STATUS.NORMAL then
		for k, v in pairs(self:GetRumorGearShowInfoList(rumor_type)) do
			if self:IsRumorActiveByRumorType(v.seq) then
				return true
			end
		end
	end

	return false
end

-- 霸屏奖励领取状态 0位置是无效的  对应档位表grade
function CustomizedRumorsWGData:IsGetGradeReward(grade)
	return (self.grade_flag or {})[grade] == 1
end

function CustomizedRumorsWGData:GetGradeSendTime(grade)
	return (self.grade_reward_flag or {})[grade] or 0
end

-- 元
function CustomizedRumorsWGData:GetRumorRealRecharge()
	return self.rmb_recharge
end

function CustomizedRumorsWGData:GetDescCfgBySeq(seq)
	return self.desc_seq_cfg[seq]
end

function CustomizedRumorsWGData:GetRumorCfgByItemId(item_id)
	return self.desc_cost_item_cfg[item_id]
end

function CustomizedRumorsWGData:GetRumorCloseState(rumor_type)
	return self.close_state[rumor_type]
end

function CustomizedRumorsWGData:IsRumorIsUseNow(rumor_type)
	local is_use_now = false
	local dingzhi_seq = self:GetRumorChooseRumorType(rumor_type)
	local select_skin_seq = self:GetRumorTypeSelectGearSeq(rumor_type)

	if dingzhi_seq == -1 then
		local status = self:IsCustomizedRumorByRumorType(rumor_type)

		if status == DIYCHUANWEN_OPERA_STATUS.SUC then
			if select_skin_seq > 0 then
				is_use_now = true
			end
		end
	else
		if dingzhi_seq ~= 0 then
			if select_skin_seq > 0 then
				is_use_now = true
			end
		end
	end

	return is_use_now
end

-----------------------------------------------remind------------------------------------------------
function CustomizedRumorsWGData:GetRumorViewRemind()
	if self:GetRumorRemind() then
		return 1
	end

	return 0
end

function CustomizedRumorsWGData:GetRumorBroadcastViewRemind()
	if self:GetBroadCastRemind() then
		return 1
	end

	return 0
end

function CustomizedRumorsWGData:CalRumorRemind()
	local rumor_remind_tab = {}
	local rumor_remind = false

	for k, v in pairs(self:GetRumorTypeShowInfoList()) do
		local rumot_type_remind = false
		local rumot_type_remind_tab = {}
		local rumor_type = 1

		for i, u in pairs(v) do
			rumor_type = u.desc_type
			local active = self:IsRumorActiveByRumorType(u.seq)
			local remind = false
			local has_gear_active = self:IsCanCustomizesRumorByRumorType(rumor_type)

			if not active then
				--未激活 能解锁 就有红点
				local han_item_num = ItemWGData.Instance:GetItemNumInBagById(u.cost_itemid)
				if han_item_num >= u.cost_item_num then
					remind = true
				end
			else
				-- 当未选择皮肤时候
				-- 已经通过审核
				local dingzhi_seq =  self:GetRumorChooseRumorType(rumor_type)
				local select_skin_seq = self:GetRumorTypeSelectGearSeq(rumor_type)

				if dingzhi_seq > 0 then
					if select_skin_seq == 0 then
						remind = true
					end
				elseif dingzhi_seq == -1 then
					local status = self:IsCustomizedRumorByRumorType(rumor_type)
					if status == DIYCHUANWEN_OPERA_STATUS.SUC then
						if select_skin_seq == 0 then
							remind = true
						end
					end
				elseif dingzhi_seq == 0 then
					if has_gear_active then
						remind = true
					end
				end
			end

			rumot_type_remind_tab[u.gear_seq] = remind

			if remind then
				rumot_type_remind = true
			end
		end

		if nil == rumor_remind_tab[rumor_type] then
			rumor_remind_tab[rumor_type] = {}
		end

		rumor_remind_tab[rumor_type].remind = rumot_type_remind
		rumor_remind_tab[rumor_type].grea_remind = rumot_type_remind_tab

		if rumot_type_remind then
			rumor_remind = true
		end
	end

	self.rumor_remind = rumor_remind
	self.rumor_remind_tab = rumor_remind_tab
end

function CustomizedRumorsWGData:CalBroadCastRemind()
	local broadcast_remind = false
	local broadcast_remind_tab = {}
	local recharge_num = self:GetRumorRealRecharge()

	for k, v in pairs(self:GetRumorBRNavDataList()) do
		broadcast_remind_tab[k] = {}
		local reward_remind = false
		local send_remind = false

		if recharge_num >= v.need_rmb_num then
			if not self:IsGetGradeReward(k) then
				reward_remind = true
				broadcast_remind = true
			end

			local send_time = self:GetGradeSendTime(k)
			if send_time < v.free_count then
				send_remind = true
				broadcast_remind = true
			end
		end

		broadcast_remind_tab[k].reward_remind = reward_remind
		broadcast_remind_tab[k].send_remind = send_remind
		broadcast_remind_tab[k].remind = reward_remind or send_remind
	end

	self.broadcast_remind = broadcast_remind
	self.broadcast_remind_tab = broadcast_remind_tab
end

function CustomizedRumorsWGData:GetRumorRemind()
	return self.rumor_remind
end

function CustomizedRumorsWGData:GetRumorTypeRemind(rumor_type)
	return (self.rumor_remind_tab[rumor_type] or {}).remind or false
end

function CustomizedRumorsWGData:GetRumorGearRemind(rumor_type, gear_type)
	return ((self.rumor_remind_tab[rumor_type] or {}).grea_remind or {})[gear_type] or false
end

function CustomizedRumorsWGData:GetBroadCastRemind()
	return self.broadcast_remind
end

function CustomizedRumorsWGData:GetBroadCastGradeRemind(grade)
	return self.broadcast_remind_tab[grade]
end
-----------------------------------------------protocol------------------------------------------------
function CustomizedRumorsWGData:SetRumorAllInfo(protocol)
	self.grade_flag = bit:d2b_l2h(protocol.grade_flag, nil, true)    -- 已领取档位标识  32位   1标识已领取  累充奖励的领取状态
	self.grade_reward_flag = protocol.grade_reward_flag              -- 档位 每日下发全服奖励标识  已经使用的次数   已领取才能发
	self.desc_active_flag = bit:d2b_l2h(protocol.desc_active_flag, nil, true)  -- 默认传闻表seq为下标  九个挡位是否激活
	self.desc_state = protocol.desc_state    --传闻定制状态（默认是0，定制中是1, 定制成功是2， 3失败）  三种类型传闻定制状态
	self.choose_skin = protocol.choose_skin   -- 已选择的传闻皮肤下标 （默认是0）   三种传闻选中挡位  对应默认传闻表  gear_seq
	self.choose_desc = protocol.choose_desc  --已选择的传闻文字下标   三种挡位 选中的模板 （默认是0，选择自己的是-1）
	self.desc_str_tab = protocol.desc_str_tab  --自定义传闻内容
	self.close_state = protocol.close_state

	self:CalRumorRemind()
	self:CalBroadCastRemind()
end

function CustomizedRumorsWGData:SetRMBRecharge(protocol)
	self.rmb_recharge = protocol.rmb_recharge
	self:CalBroadCastRemind()
end

function CustomizedRumorsWGData:UpdateActiveSkin(protocol)
	self.desc_active_flag = bit:d2b_l2h(protocol.desc_active_flag, nil, true)
	self:CalRumorRemind()
end

-- 主动请求结果返回   只针对自定义
function CustomizedRumorsWGData:UpdateDiyDescInfo(protocol)
	local rumor_type = protocol.desc_type
	local desc_type_state = protocol.desc_type_state
	self.desc_state[rumor_type] = desc_type_state
	self.desc_str_tab[rumor_type] = protocol.diy_desc_str
end

function CustomizedRumorsWGData:UpdateRewardFlagInfo(protocol)
	self.grade_reward_flag = protocol.grade_reward_flag   -- 已经使用的次数   已领取才能发   档位每日下发全服奖励标识
	self:CalBroadCastRemind()
end

function CustomizedRumorsWGData:UpdateGradeFlagInfo(protocol)
	self.grade_flag = bit:d2b_l2h(protocol.grade_flag, nil, true)
	self:CalBroadCastRemind()
end

function CustomizedRumorsWGData:UpdateChooseDescInfo(protocol)
	self.choose_skin = protocol.choose_skin
	self.choose_desc = protocol.choose_desc
	self:CalRumorRemind()
end

function CustomizedRumorsWGData:UpdataCloseInfo(protocol)
	local rumor_type = protocol.desc_type
	local state = protocol.close_state
	self.close_state[rumor_type] = state
end