using LuaInterface;
using System.Collections;
using System.Collections.Generic;
using SceneEnvironment;
using Unity.VisualScripting.FullSerializer;
using UnityEngine;
using static UISceneController;
using Game;

public class UISceneController : MonoBehaviour
{
    // 缓存关键引用，避免重复查找
    private Camera _mainCamera;
    private EnvironmentController _environmentController;
    private Transform _mainTransform;

    private Camera MainCamera
    {
        get
        {
            if (_mainCamera == null)
            {
                var _camera = transform.Find("Camera")?.GetComponent<Camera>();
                _mainCamera = _camera == null ? Camera.main : _camera;
            }
            return _mainCamera;
        }
    }

    private EnvironmentController EnvironmentController
    {
        get
        {
            if (_environmentController == null)
            {
                GameObject env = GameObject.Find("Main/Environment");
                if (env != null)
                    _environmentController = env.GetComponent<EnvironmentController>();
            }
            return _environmentController;
        }
    }

    private Transform MainTransform
    {
        get
        {
            if (_mainTransform == null)
                _mainTransform = this.transform;
            return _mainTransform;
        }
    }

    /// <summary>
    /// 安全获取组件，避免空引用
    /// </summary>
    private T SafeGetComponent<T>(GameObject obj) where T : Component
    {
        if (obj == null) return null;
        return obj.GetComponent<T>();
    }

    /// <summary>
    /// 安全设置材质，检查数组有效性并过滤null元素
    /// </summary>
    private void SafeSetMaterials(MeshRenderer renderer, Material[] materials)
    {
        if (renderer == null || materials == null || materials.Length == 0) return;
        
        // 过滤null元素，确保材质数组的安全性
        var validMaterials = new List<Material>();
        foreach (var material in materials)
        {
            if (material != null)
            {
                validMaterials.Add(material);
            }
        }
        
        // 如果过滤后没有有效材质，则不设置
        if (validMaterials.Count > 0)
        {
            renderer.sharedMaterials = validMaterials.ToArray();
        }
    }

    /// <summary>
    /// 安全转换材质列表为数组，过滤null元素
    /// </summary>
    private Material[] SafeConvertMaterialsToArray(List<Material> materials)
    {
        if (materials == null || materials.Count == 0) return null;
        
        var validMaterials = new List<Material>();
        foreach (var material in materials)
        {
            if (material != null)
            {
                validMaterials.Add(material);
            }
        }
        
        return validMaterials.Count > 0 ? validMaterials.ToArray() : null;
    }

    [NoToLua]
    [System.Serializable]
    public class TransformConfiguration
    {
        public GameObject obj;
        public Vector3 position;
        public Quaternion rotation;
        public Vector3 scale;
    }

    [NoToLua]
    [System.Serializable]
    public class ActiveStateConfiguration
    {
        public GameObject obj;
        public bool isActive;
    }

    [NoToLua]
    [System.Serializable]
    public class MeshRendererMaterialConfig
    {
        public GameObject obj;
        public List<Material> materials;
    }
    
    [NoToLua]
    [System.Serializable]
    public class CameraConfiguration
    {
        public float fieldOfView = 30.0F;
    }

    [System.Serializable]
    public class SceneConfiguration
    {
        public string configName;
        public List<TransformConfiguration> transformConfigs = new List<TransformConfiguration>();
        public List<ActiveStateConfiguration> activeStateConfigs = new List<ActiveStateConfiguration>();
        public List<MeshRendererMaterialConfig> meshRendererMaterialConfigs = new List<MeshRendererMaterialConfig>();
        public CameraConfiguration cameraConfig = new CameraConfiguration();
        public EnvironmentData environmentData;
        public CharacterData characterData;
        public Material[] waterMats;
        public Material[] bgPanelMats;

        public SceneConfiguration(string name)
        {
            configName = name;
        }
    }

    [NoToLua]
    public List<SceneConfiguration> configurations = new List<SceneConfiguration>();
    public int currentConfigIndex = 0;

    public void SetConfiguration(int configIndex)
    {
        if (configurations == null || configIndex < 0 || configIndex >= configurations.Count)
        {
            Debug.LogError($"无效的UIScene配置索引: {configIndex}, 配置数量: {configurations?.Count ?? 0}");
            return;
        }

        SceneConfiguration config = configurations[configIndex];
        if (config == null)
        {
            Debug.LogError($"UIScene配置为空，索引: {configIndex}");
            return;
        }

        currentConfigIndex = configIndex;
        SetConfiguration(config);
    }

    public MeshRenderer GetWaterCommonMeahRender()
    {
        if (configurations == null || currentConfigIndex < 0 || currentConfigIndex >= configurations.Count)
        {
            Debug.LogWarning("当前配置索引无效，无法获取Water_Common渲染器");
            return null;
        }

        var config = configurations[currentConfigIndex];
        if (config?.activeStateConfigs == null) return null;

        foreach (var activeConfig in config.activeStateConfigs)
        {
            if (activeConfig?.obj != null && activeConfig.isActive && activeConfig.obj.name == "Water_Common")
            {
                var srpWaterFeature = SafeGetComponent<SrpWaterFeature>(activeConfig.obj);
                if (srpWaterFeature?.planars != null)
                {
                    foreach (var obj in srpWaterFeature.planars)
                    {
                        var meshRenderer = SafeGetComponent<MeshRenderer>(obj);
                        if (meshRenderer != null)
                            return meshRenderer;
                    }
                }
            }
        }
        return null;
    }

    public MeshRenderer GetBgPanelCommonMeahRender()
    {
        if (configurations == null || currentConfigIndex < 0 || currentConfigIndex >= configurations.Count)
        {
            Debug.LogWarning("当前配置索引无效，无法获取BgPanel_Common渲染器");
            return null;
        }

        var config = configurations[currentConfigIndex];
        if (config?.activeStateConfigs == null) return null;

        foreach (var activeConfig in config.activeStateConfigs)
        {
            if (activeConfig?.obj != null && activeConfig.isActive && activeConfig.obj.name == "BgPanel_Common")
            {
                return SafeGetComponent<MeshRenderer>(activeConfig.obj);
            }
        }
        return null;
    }

    public GameObjectAttach GetEffectCommonGameObjectAttach()
    {
        if (configurations == null || currentConfigIndex < 0 || currentConfigIndex >= configurations.Count)
        {
            Debug.LogWarning("当前配置索引无效，无法获取Effect_Common组件");
            return null;
        }

        var config = configurations[currentConfigIndex];
        if (config?.activeStateConfigs == null) return null;

        foreach (var activeConfig in config.activeStateConfigs)
        {
            if (activeConfig?.obj != null && activeConfig.isActive && activeConfig.obj.name == "Effect_Common")
            {
                return SafeGetComponent<GameObjectAttach>(activeConfig.obj);
            }
        }
        return null;
    }

    public GameObjectAttach GetEffectCommonSmokeGameObjectAttach()
    {
        if (configurations == null || currentConfigIndex < 0 || currentConfigIndex >= configurations.Count)
        {
            Debug.LogWarning("当前配置索引无效，无法获取Effect_Common_Smoke组件");
            return null;
        }

        var config = configurations[currentConfigIndex];
        if (config?.activeStateConfigs == null) return null;

        foreach (var activeConfig in config.activeStateConfigs)
        {
            if (activeConfig?.obj != null && activeConfig.isActive && activeConfig.obj.name == "Effect_Common_Smoke")
            {
                return SafeGetComponent<GameObjectAttach>(activeConfig.obj);
            }
        }
        return null;
    }

    public Transform GetEffectsTransform()
    {
        if (MainTransform == null) return null;
        return MainTransform.Find("Effects");
    }

    public SceneConfiguration GetSceneConfiguration(int configIndex)
    {
        if (configurations == null || configIndex < 0 || configIndex >= configurations.Count)
        {
            Debug.LogError($"无效的UIScene配置索引: {configIndex}, 配置数量: {configurations?.Count ?? 0}");
            return null;
        }

        return configurations[configIndex];
    }

    public void SetConfiguration(SceneConfiguration config)
    {
        if (config == null)
        {
            Debug.LogError("SceneConfiguration配置为空，无法应用");
            return;
        }

        // 应用 transform 配置
        if (config.transformConfigs != null)
        {
            foreach (var transConfig in config.transformConfigs)
            {
                if (transConfig?.obj != null && transConfig.obj.transform != null)
                {
                    var transform = transConfig.obj.transform;
                    transform.position = transConfig.position;
                    transform.rotation = transConfig.rotation;
                    transform.localScale = transConfig.scale;
                }
            }
        }

        // 应用 active state 配置
        if (config.activeStateConfigs != null)
        {
            foreach (var activeConfig in config.activeStateConfigs)
            {
                if (activeConfig?.obj != null)
                {
                    if (activeConfig.isActive)
                    {
                        if (activeConfig.obj.name == "Water_Common")
                        {
                            // 因为用的是同一个对象，sharedMaterials更改材质不会实时刷新，先隐藏，再显示会重新触发刷新渲染
                            activeConfig.obj.SetActive(false);

                            var srpWaterFeature = SafeGetComponent<SrpWaterFeature>(activeConfig.obj);
                            if (srpWaterFeature?.planars != null && config.waterMats != null)
                            {
                                foreach (var obj in srpWaterFeature.planars)
                                {
                                    var meshRender = SafeGetComponent<MeshRenderer>(obj);
                                    SafeSetMaterials(meshRender, config.waterMats);
                                }
                            }
                        }
                        else if (activeConfig.obj.name == "BgPanel_Common")
                        {
                            if (config.bgPanelMats != null)
                            {
                                var meshRender = SafeGetComponent<MeshRenderer>(activeConfig.obj);
                                SafeSetMaterials(meshRender, config.bgPanelMats);
                            }
                        }
                    }
                    activeConfig.obj.SetActive(activeConfig.isActive);
                }
            }
        }

        // 应用 meshRenderer 材质配置
        if (config.meshRendererMaterialConfigs != null)
        {
            foreach (var materialConfig in config.meshRendererMaterialConfigs)
            {
                if (materialConfig?.obj != null && materialConfig.materials != null)
                {
                    var meshRenderer = SafeGetComponent<MeshRenderer>(materialConfig.obj);
                    if (meshRenderer != null)
                    {
                        var materialArray = SafeConvertMaterialsToArray(materialConfig.materials);
                        if (materialArray != null)
                        {
                            SafeSetMaterials(meshRenderer, materialArray);
                        }
                    }
                }
            }
        }
        
        // 应用 camera 配置
        if (config.cameraConfig != null && MainCamera != null)
        {
            MainCamera.fieldOfView = config.cameraConfig.fieldOfView;
        }
        else if (MainCamera == null)
        {
            Debug.LogWarning("主相机未找到，无法应用相机配置");
        }
        
        // 应用 渲染环境 配置
        var envCtrl = EnvironmentController;
        if (envCtrl != null)
        {
            if (config.environmentData != null)
                envCtrl.sharedEnvironmentData = config.environmentData;
            if (config.characterData != null)
                envCtrl.sharedCharacterData = config.characterData;
        }
        else
        {
            Debug.LogWarning("环境控制器未找到，无法应用环境配置");
        }
    }

    #region 配置数据添加更新
    // 遍历添加配置数据。（前提：缓存数据全清）
    private void SetConfigurationData(SceneConfiguration config)
    {
        if (config == null)
        {
            Debug.LogError("配置对象为空，无法设置配置数据");
            return;
        }

        var mainTransform = MainTransform;
        if (mainTransform == null)
        {
            Debug.LogError("主Transform为空，无法设置配置数据");
            return;
        }

        // 遍历 Main 节点的一级子节点, 记录一级子节点的配置信息
        foreach (Transform firstLevelChild in mainTransform)
        {
            if (firstLevelChild == null) continue;

            config.transformConfigs.Add(new TransformConfiguration
            {
                obj = firstLevelChild.gameObject,
                position = firstLevelChild.localPosition,
                rotation = firstLevelChild.localRotation,
                scale = firstLevelChild.localScale
            });

            config.activeStateConfigs.Add(new ActiveStateConfiguration
            {
                obj = firstLevelChild.gameObject,
                isActive = firstLevelChild.gameObject.activeSelf
            });

            // 遍历一级子节点的二级子节点, 记录二级子节点的配置信息
            foreach (Transform secondLevelChild in firstLevelChild)
            {
                if (secondLevelChild == null) continue;

                if (secondLevelChild.gameObject.activeSelf && secondLevelChild.gameObject.name == "Water_Common")
                {
                    var srpWaterFeature = SafeGetComponent<SrpWaterFeature>(secondLevelChild.gameObject);
                    if (srpWaterFeature?.planars != null)
                    {
                        foreach (var obj in srpWaterFeature.planars)
                        {
                            var meshRender = SafeGetComponent<MeshRenderer>(obj);
                            if (meshRender?.sharedMaterials != null)
                            {
                                config.waterMats = meshRender.sharedMaterials;
                                break; // 只需要获取第一个有效的材质
                            }
                        }
                    }
                }
                else if (secondLevelChild.gameObject.activeSelf && secondLevelChild.gameObject.name == "BgPanel_Common")
                {
                    var meshRender = SafeGetComponent<MeshRenderer>(secondLevelChild.gameObject);
                    if (meshRender?.sharedMaterials != null)
                    {
                        config.bgPanelMats = meshRender.sharedMaterials;
                    }
                }

                config.transformConfigs.Add(new TransformConfiguration
                {
                    obj = secondLevelChild.gameObject,
                    position = secondLevelChild.localPosition,
                    rotation = secondLevelChild.localRotation,
                    scale = secondLevelChild.localScale
                });

                config.activeStateConfigs.Add(new ActiveStateConfiguration
                {
                    obj = secondLevelChild.gameObject,
                    isActive = secondLevelChild.gameObject.activeSelf
                });
            }
        }

        // 设置相机配置
        if (MainCamera != null)
        {
            config.cameraConfig.fieldOfView = MainCamera.fieldOfView;
        }

        // 设置环境配置
        var envCtrl = EnvironmentController;
        if (envCtrl != null)
        {
            if (envCtrl.sharedEnvironmentData != null)
                config.environmentData = envCtrl.sharedEnvironmentData;
            if (envCtrl.sharedCharacterData != null)
                config.characterData = envCtrl.sharedCharacterData;
        }
    }

    [NoToLua]
    // 将当前状态保存到配置
    public void UpdateCurrentConfiguration(SceneConfiguration config)
    {
        if (config == null)
        {
            Debug.LogError("配置对象为空，无法更新配置");
            return;
        }

        config.transformConfigs.Clear();
        config.activeStateConfigs.Clear();
        SetConfigurationData(config);
    }

    [NoToLua]
    // 将当前状态保存为新配置
    public void SaveCurrentConfiguration(string configName)
    {
        if (string.IsNullOrEmpty(configName))
        {
            Debug.LogError("配置名称为空，无法保存配置");
            return;
        }

        if (configurations == null)
        {
            configurations = new List<SceneConfiguration>();
        }

        SceneConfiguration newConfig = new SceneConfiguration(configName);
        SetConfigurationData(newConfig);
        configurations.Add(newConfig);
    }

    [NoToLua]
    // 配置增量更新所有
    public void IncrementalUpdateAllConfiguration()
    {
        if (configurations == null)
        {
            Debug.LogWarning("配置列表为空，无法进行增量更新");
            return;
        }

        foreach (var config in configurations)
        {
            if (config != null)
            {
                IncrementalUpdateConfiguration(config);
            }
        }
    }

    // 配置增量更新
    private void IncrementalUpdateConfiguration(SceneConfiguration config)
    {
        if (config == null)
        {
            Debug.LogError("配置对象为空，无法进行增量更新");
            return;
        }

        HashSet<GameObject> currentSceneObjects = new HashSet<GameObject>();
        var mainTransform = MainTransform;
        if (mainTransform != null)
        {
            foreach (Transform firstLevelChild in mainTransform)
            {
                if (firstLevelChild?.gameObject != null)
                {
                    currentSceneObjects.Add(firstLevelChild.gameObject);

                    foreach (Transform secondLevelChild in firstLevelChild)
                    {
                        if (secondLevelChild?.gameObject != null)
                        {
                            currentSceneObjects.Add(secondLevelChild.gameObject);
                        }
                    }
                }
            }
        }

        // 确保配置列表已初始化
        if (config.transformConfigs == null)
            config.transformConfigs = new List<TransformConfiguration>();
        if (config.activeStateConfigs == null)
            config.activeStateConfigs = new List<ActiveStateConfiguration>();
        if (config.meshRendererMaterialConfigs == null)
            config.meshRendererMaterialConfigs = new List<MeshRendererMaterialConfig>();

        // 检查新增的节点
        foreach (var sceneObject in currentSceneObjects)
        {
            bool existsInConfig = config.transformConfigs.Exists(t => t?.obj == sceneObject);

            if (!existsInConfig && sceneObject != null)
            {
                config.transformConfigs.Add(new TransformConfiguration
                {
                    obj = sceneObject,
                    position = sceneObject.transform.localPosition,
                    rotation = sceneObject.transform.localRotation,
                    scale = sceneObject.transform.localScale
                });

                config.activeStateConfigs.Add(new ActiveStateConfiguration
                {
                    obj = sceneObject,
                    isActive = sceneObject.activeSelf
                });
            }
        }

        // 检查已删除的节点
        config.transformConfigs.RemoveAll(t => t?.obj == null || !currentSceneObjects.Contains(t.obj));
        config.activeStateConfigs.RemoveAll(a => a?.obj == null || !currentSceneObjects.Contains(a.obj));
        config.meshRendererMaterialConfigs.RemoveAll(m => m?.obj == null || !currentSceneObjects.Contains(m.obj));
    }

    #endregion
}
