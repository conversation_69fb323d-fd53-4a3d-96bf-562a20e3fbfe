﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_Universal_UniversalRendererWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.Universal.UniversalRenderer), typeof(UnityEngine.Rendering.Universal.ScriptableRenderer));
		<PERSON><PERSON>unction("SupportedCameraStackingTypes", SupportedCameraStackingTypes);
		<PERSON><PERSON>RegFunction("Setup", Setup);
		<PERSON><PERSON>RegFunction("SetupLights", SetupLights);
		<PERSON><PERSON>RegFunction("SetupCullingParameters", SetupCullingParameters);
		<PERSON><PERSON>Function("FinishRendering", FinishRendering);
		<PERSON><PERSON>unction("GameSwapColorBuffer", GameSwapColorBuffer);
		<PERSON><PERSON>RegFunction("GetNestBuffer4Dest", GetNestBuffer4Dest);
		<PERSON><PERSON>RegFunction("GetCurDestBuffder", GetCurDestBuffder);
		<PERSON><PERSON>unction("New", _CreateUnityEngine_Rendering_Universal_UniversalRenderer);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ar("depthPrimingMode", get_depthPrimingMode, set_depthPrimingMode);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Rendering_Universal_UniversalRenderer(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Rendering.Universal.UniversalRendererData arg0 = (UnityEngine.Rendering.Universal.UniversalRendererData)ToLua.CheckObject<UnityEngine.Rendering.Universal.UniversalRendererData>(L, 1);
				UnityEngine.Rendering.Universal.UniversalRenderer obj = new UnityEngine.Rendering.Universal.UniversalRenderer(arg0);
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Rendering.Universal.UniversalRenderer.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SupportedCameraStackingTypes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			int o = obj.SupportedCameraStackingTypes();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Setup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 2);
			UnityEngine.Rendering.Universal.RenderingData arg1 = StackTraits<UnityEngine.Rendering.Universal.RenderingData>.Check(L, 3);
			obj.Setup(arg0, ref arg1);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetupLights(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.ScriptableRenderContext arg0 = StackTraits<UnityEngine.Rendering.ScriptableRenderContext>.Check(L, 2);
			UnityEngine.Rendering.Universal.RenderingData arg1 = StackTraits<UnityEngine.Rendering.Universal.RenderingData>.Check(L, 3);
			obj.SetupLights(arg0, ref arg1);
			ToLua.PushValue(L, arg1);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetupCullingParameters(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.ScriptableCullingParameters arg0 = StackTraits<UnityEngine.Rendering.ScriptableCullingParameters>.Check(L, 2);
			UnityEngine.Rendering.Universal.CameraData arg1 = StackTraits<UnityEngine.Rendering.Universal.CameraData>.Check(L, 3);
			obj.SetupCullingParameters(ref arg0, ref arg1);
			ToLua.PushValue(L, arg0);
			ToLua.PushValue(L, arg1);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FinishRendering(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			obj.FinishRendering(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GameSwapColorBuffer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			obj.GameSwapColorBuffer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNestBuffer4Dest(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			UnityEngine.Rendering.RenderTargetIdentifier o = obj.GetNestBuffer4Dest(arg0);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurDestBuffder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.Universal.UniversalRenderer));
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			UnityEngine.Rendering.RenderTargetIdentifier o = obj.GetCurDestBuffder(arg0);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_depthPrimingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)o;
			UnityEngine.Rendering.Universal.DepthPrimingMode ret = obj.depthPrimingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index depthPrimingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_depthPrimingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.Universal.UniversalRenderer obj = (UnityEngine.Rendering.Universal.UniversalRenderer)o;
			UnityEngine.Rendering.Universal.DepthPrimingMode arg0 = (UnityEngine.Rendering.Universal.DepthPrimingMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.Universal.DepthPrimingMode));
			obj.depthPrimingMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index depthPrimingMode on a nil value");
		}
	}
}

