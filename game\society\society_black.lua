function SocietyView:InitBlackView()
    self.list_blacklist = self.node_list["list_blacklist"]

    XUI.AddClickEventListener(self.node_list["btn_addblack"], BindTool.Bind1(self.<PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
    XUI.AddClickEventListener(self.node_list["btn_removeblack"], BindTool.Bind1(self.RemoveBlackHand<PERSON>, self))

    self:CreateAutoFriendList()
end

function SocietyView:DeleteBlackView()
	self.list_blacklist = nil

	if self.blacklist  then
		self.blacklist:DeleteMe()
		self.blacklist = nil
	end
end

function SocietyView:AddBlackHandler()
    -- print_error("AddBlackHandler_______________ ")
    SocietyWGCtrl.Instance:OpenAddBlacklistView()
end

function SocietyView:RemoveBlackHandler()
    -- print_error("RemoveBlackHandler----------------------------- ")
    local items = self.blacklist:GetAllItems() or {}
	for k,v in pairs(items) do
		if v.is_choose == true and v:GetData() ~= nil then
			ChatWGCtrl.Instance:SendDeleteBlackReq(v:GetData().user_id)
		end
	end
end

function SocietyView:CreateAutoFriendList()
    self.blacklist = AsyncListView.New(BlackViewlistRender, self.list_blacklist)

    self:FlushBlackView()
end
-- function SocietyView:OnFlush()
-- 	self:FlushBlackView()
-- end

function SocietyView:FlushBlackView()
	local data = ChatWGData.Instance:GetBlacklist()
	--print_error("11",data)
	if nil ~= data and nil ~= self.blacklist then
		self.blacklist:SetDataList(data, 0)
	end
end





----------------------------------------------------------------------------------------------------
-- 鮟大錐蜊品tem
----------------------------------------------------------------------------------------------------
BlackViewlistRender = BlackViewlistRender or BaseClass(BaseRender)
function BlackViewlistRender:__init()
	self.is_choose = false
	self.role_avatar = RoleHeadCell.New(false)
	self.head_cell = BaseHeadCell.New(self.node_list.img_cell)
	XUI.AddClickEventListener(self.node_list["img_cell"], BindTool.Bind1(self.RoleInfoList, self))
end

function BlackViewlistRender:__delete()
	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function BlackViewlistRender:RoleInfoList()
	self.role_avatar:OpenMenu(nil,SocietyWGCtrl.Instance:GetCacularPos(self.node_list["default_head_icon"]), nil, MASK_BG_ALPHA_TYPE.Normal)--Vector2(-141, -82))
end

function BlackViewlistRender:SetKuaFuState()
	-- body
end
function BlackViewlistRender:OnFlush()
	local plat_type = self.data.uuid and self.data.uuid.temp_low or -1
	local server_id = self.data.uuid and self.data.uuid.temp_high or -1

	local role_info = {
		role_id = self.data.user_id,
		role_name = self.data.gamename,
		prof = self.data.prof,
		sex = self.data.sex,
		is_online = true,
		plat_type = plat_type,
		server_id = server_id,
	}
	self.role_avatar:SetRoleInfo(role_info)
	self.role_avatar:RemoveItems(Language.Menu.AddFriend,Language.Menu.SendMail,
	Language.Menu.InviteTeam,
	Language.Menu.GiveFlower,
	Language.Menu.InviteGuild,Language.Menu.Blacklist)
	self.role_avatar:AddItems(Language.Menu.BlackRemove)
	local is_online_flag = self.data.is_online == ONLINE_TYPE.ONLINE_TYPE_ONLINE or self.data.is_online == ONLINE_TYPE.ONLINE_TYPE_CROSS


	self.node_list["lbl_camp_username"].text.text = self.data.gamename--ToColorStr(self.data.gamename, SEX_COLOR[self.data.sex][3])
	-- XUI.SetGraphicGrey(self.node_list["vip_text"], not is_online_flag)
	-- XUI.SetGraphicGrey(self.node_list["img_rank_ten"], not is_online_flag)
	-- self.node_list["img_rank_ten"]:SetActive(SocietyWGData.Instance:ChackZhanliRank(self.data.user_id) <= 10)
	local fashion_photoframe = self.data.fashion_photoframe or 0
	local data = {role_id = self.data.user_id, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = fashion_photoframe}
	self.head_cell:SetData(data)
	self.head_cell:SetGray(not is_online_flag)
	local bundle, asset = ResPath.GetProfIcon(self.data.sex, self.data.prof%10, true)
    self.node_list.prof_icon.image:LoadSprite(bundle, asset, function()        
    	-- self.node_list.prof_icon.image:SetNativeSize()      
    end)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.img9_fire:SetActive(is_vis)
	if is_vis then
		self.node_list["label_level"].text.text = role_level
		--TODO 对齐修改成TMP的格式
		-- self.node_list["label_level"].text.alignment = UnityEngine.TextAnchor.MiddleLeft
	else
		self.node_list["label_level"].text.text = "LV."..role_level--RoleWGData.GetLevelString(self.data.level)
		-- self.node_list["label_level"].text.alignment = UnityEngine.TextAnchor.MiddleCenter
	end

	self.node_list["img_vip"]:CustomSetActive(false)
	self.node_list["img_vip_bg"]:CustomSetActive(false)
	self.node_list["layout_qinmidu"]:CustomSetActive(false)
end

function BlackViewlistRender:OnSelectChange(is_select)
	self.node_list.img_choose:CustomSetActive(is_select)
end
