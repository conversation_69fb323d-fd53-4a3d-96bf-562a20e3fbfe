function HolyDarkWeaponView:InitHolyDarkJinJieView()
	if not self.jinjie_show_list then
		self.jinjie_show_list = AsyncListView.New(HolyDarkJinjieRender, self.node_list["jinjie_equip_list"])
		self.jinjie_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectJinJ<PERSON>C<PERSON>, self))
		self.jinjie_show_list:SetDefaultSelectIndex(nil)
	end

   	if nil == self.jinjie_show_model then
        self.jinjie_show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["jinjie_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.jinjie_show_model:SetRenderTexUI3DModel(display_data)
        -- self.jinjie_show_model:SetUI3DModel(self.node_list["jinjie_display"].transform,
        --                             self.node_list["jinjie_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.jinjie_show_model)
    end

    XUI.AddClickEventListener(self.node_list["month_buy_btn"], BindTool.Bind(self.ClickMonthBuy, self))
    XUI.AddClickEventListener(self.node_list["up_power_btn"], BindTool.Bind(self.ClickUpPowerLevel, self))
    XUI.AddClickEventListener(self.node_list["once_get_btn"], BindTool.Bind(self.ClickOnceGetBtn, self))
    XUI.AddClickEventListener(self.node_list["jinjie_btn"], BindTool.Bind(self.ClickJinJieBtn, self))
    XUI.AddClickEventListener(self.node_list["grade_attr_btn"], BindTool.Bind(self.ClickJinJieAttrBtn, self))

    self.power_obj_list = {}
    self.jinjie_once = false
end

function HolyDarkWeaponView:DeleteHolyDarkJinJieView()
	if self.jinjie_show_list then
		self.jinjie_show_list:DeleteMe()
		self.jinjie_show_list = nil
	end

	if self.jinjie_show_model then
        self.jinjie_show_model:DeleteMe()
        self.jinjie_show_model = nil
    end

    if self.power_obj_list then
		for k,v in pairs(self.power_obj_list) do
			v:DeleteMe()
		end
		self.power_obj_list = {}
	end

	self.jinjie_select_data = nil
	self.jinjie_select_index = nil
	self.jinjie_once = nil
end

function HolyDarkWeaponView:FlushHolyDarkJinJieView()
	local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
		local wear_all_data = HolyDarkWeaponWGData.Instance:GetHolyDarkEquipWearList(relic_seq)
		local data = {}
		for i = 0, 8 do
			if wear_all_data[i] and wear_all_data[i].item_id > 0 then
				table.insert(data, wear_all_data[i])
			end
		end

		self.jinjie_show_list:SetDataList(data)
		local click_cell_index = self.jinjie_select_index or 1
		if not self.jinjie_select_index then
			for i, v in ipairs(data) do
				if HolyDarkWeaponWGData.Instance:GetRelicSlotCanUpGrade(v) then
					click_cell_index = i
					break
				end
			end
		end

		self.jinjie_show_list:JumpToIndex(click_cell_index)
    end
end

function HolyDarkWeaponView:OnSelectJinJieCell(cell)
	if nil == cell and nil == cell.data then
		return
	end

	self.jinjie_select_data = cell.data
	self.jinjie_select_index = cell.index
	self:FlushJinJieLeftPanel()
	self:FlushJinJieShowModel()
end

function HolyDarkWeaponView:FlushJinJieLeftPanel()
	if self.jinjie_select_data == nil then
		return
	end 

	local data = self.jinjie_select_data
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	self.node_list.jinjie_weapon_name.text.text = item_cfg and string.format(Language.HolyDarkWeapon.EquipGradeName, item_cfg.name, data.grade) or ""
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(data.relic_seq)
	local info = HolyDarkWeaponWGData.Instance:GetRelicPowerItemInfoBySeq(relic_cfg.type)
	if not IsEmptyTable(info) then
		local cur_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicPowerLevelCfg(relic_cfg.type, info.power_level)
		self.node_list.daily_power.text.text = string.format(Language.HolyDarkWeapon.DailyPower, info.daily_power, cur_level_cfg.max_power)
		self:FlushJinJieSlideView(info)
		self:FlushJinJiePowerList(info)
		local is_can_get = HolyDarkWeaponWGData.Instance:GetRelicPowerCanGet(relic_cfg.type)
		self.node_list.once_get_btn:SetActive(is_can_get)
	end
end

function HolyDarkWeaponView:FlushJinJieSlideView(info)
	if self.jinjie_select_data == nil then
		return
	end 

	local data = self.jinjie_select_data
	if not IsEmptyTable(info) then
		local cur_grade_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeCfg(data.relic_seq, data.slot_index, data.grade)
		local next_grade_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeCfg(data.relic_seq, data.slot_index, data.grade + 1)
		if cur_grade_cfg then
			local is_max = IsEmptyTable(next_grade_cfg)
			self.node_list.jinjie_btn:SetActive(not is_max)
			self.node_list.max_jinjie:SetActive(is_max)
			self.node_list.need_power_num.text.text = is_max and " " or info.total_power .. "/" .. (cur_grade_cfg.need_power - data.grade_power)
			self.node_list.jinjie_red:SetActive(not is_max and info.total_power > 0)
			if is_max then
				self.node_list.slider_progress.image.fillAmount = 1
				self.node_list.add_slider_progress.image.fillAmount = 0
			else
				self.node_list.slider_progress.image.fillAmount = data.grade_power / cur_grade_cfg.need_power
				self.node_list.add_slider_progress.image.fillAmount = (data.grade_power + info.total_power) / cur_grade_cfg.need_power
			end
		end
	end
end

function HolyDarkWeaponView:FlushJinJiePowerList(info)
	if not IsEmptyTable(info) then
		local power_list_data = info.power_list
		if not IsEmptyTable(power_list_data) then
			for index = 0, #power_list_data do
				if self.power_obj_list[index] == nil then
					local go = ResMgr:Instantiate(self.node_list["power_cell"].gameObject)
					go:SetActive(false)
					go.transform:SetParent(self.node_list["power_list"].transform, false)
					self.power_obj_list[index] = HloDarkPowerCell.New(go, self)
					self.power_obj_list[index]:SetIndex(index)
				end

				local obj = self.power_obj_list[index]
				if not obj:GetActive() then
					local rand_num_x = 0
					local rand_num_y = 0
					local min_x = -246
					local max_x = 246
					while rand_num_x <= max_x and rand_num_x >= min_x do
						rand_num_x = GameMath.Rand(-365, 370)
					end

					rand_num_y = GameMath.Rand(-235, 235)
					obj:SetLocalPosition(rand_num_x, rand_num_y)
					obj:PlayUpTween()
				end

				obj:SetData(power_list_data[index])
			end
		else	
			for k,v in pairs(self.power_obj_list) do
				v:SetActive(false)
			end
		end
	end 
end

function HolyDarkWeaponView:FlushJinJieShowModel()
	if self.jinjie_select_data == nil then
		return
	end 

	local data = self.jinjie_select_data
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(data.relic_seq)
	local bundle, asset = ResPath.GetRelicModel(relic_cfg.model_id)
    self.jinjie_show_model:SetMainAsset(bundle, asset)
    self.jinjie_show_model:FixToOrthographic(self.root_node_transform)
end

function HolyDarkWeaponView:ClickMonthBuy()
	if self.jinjie_select_data == nil then
		return
	end 

	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(self.jinjie_select_data.relic_seq)
	if not IsEmptyTable(relic_cfg) then
		HolyDarkWeaponWGCtrl.Instance:OpenHolyDarkMonthBuyView(relic_cfg.type)
	end
end

function HolyDarkWeaponView:ClickUpPowerLevel()
	if self.jinjie_select_data == nil then
		return
	end 

	local data = self.jinjie_select_data
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(data.relic_seq)
	local info = HolyDarkWeaponWGData.Instance:GetRelicPowerItemInfoBySeq(relic_cfg.type)
	if not IsEmptyTable(info) then
		local cur_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicPowerLevelCfg(relic_cfg.type, info.power_level)
		local next_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicPowerLevelCfg(relic_cfg.type, info.power_level + 1)
		if IsEmptyTable(next_level_cfg) or IsEmptyTable(cur_level_cfg) then
			TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.MaxLevel)
            return
		else
            local text_dec = string.format(Language.HolyDarkWeapon.PowerUpLevelTips, cur_level_cfg.need_gold)
			local is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(cur_level_cfg.need_gold)
			local ok_func = function ()
				if is_enough_money then
					HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.POWER_UPLEVEL, relic_cfg.type)
	        	else
	            	VipWGCtrl.Instance:OpenTipNoGold()
	        	end
			end

		    TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
		end
	end
end

function HolyDarkWeaponView:ClickOnceGetBtn()
	if self.jinjie_select_data == nil then
		return
	end 

	if self.jinjie_once then
		return
	end

	self.jinjie_once = true
	local data = self.jinjie_select_data
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(data.relic_seq)
	local is_can_get = HolyDarkWeaponWGData.Instance:GetRelicPowerCanGet(relic_cfg.type)
	if is_can_get then
		for k,v in pairs(self.power_obj_list) do
			v:PlayOnceGetTween()
		end

		ReDelayCall(self, function()
			self.jinjie_once = false
			self:PlayPowerEffct()
			HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.POWER_FETCH, relic_cfg.type, -1)
        end, 1, "holydrak_jinjie_power")
	end
end

function HolyDarkWeaponView:ClickJinJieBtn()
	if self.jinjie_select_data == nil then
		return
	end 

	local data = self.jinjie_select_data
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(data.relic_seq)
	local info = HolyDarkWeaponWGData.Instance:GetRelicPowerItemInfoBySeq(relic_cfg.type)
	if not IsEmptyTable(info) then
		local cur_grade_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeCfg(data.relic_seq, data.slot_index, data.grade)
		local next_grade_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeCfg(data.relic_seq, data.slot_index, data.grade + 1)
		local is_max = IsEmptyTable(next_grade_cfg)
		if is_max then
			TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.MaxGrade)
            return
		end

		if info.total_power > 0 then
			HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.SLOT_UPGRADE, data.relic_seq, data.slot_index)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.PowerNoNum)
            return
		end
	end
end

function HolyDarkWeaponView:ClickJinJieAttrBtn()
	if self.jinjie_select_data == nil then
		return
	end 

    HolyDarkWeaponWGCtrl.Instance:OpenJinJieAttrView(self.jinjie_select_data)
end

function HolyDarkWeaponView:PlayPowerEffct()
	local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhuanpanka_huitan_cheng")
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.power_effct.transform, 0.3, nil, nil, nil)
end

function HolyDarkWeaponView:PlayJinJieEffect()
	if self.node_list["jinjie_succ_pos"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jinjie, is_success = true, pos = Vector2(0, 0),
								parent_node = self.node_list["jinjie_succ_pos"]})
	end
end

function HolyDarkWeaponView:DoHolyDarkJinJieViewAnim()
	local tween_info = UITween_CONSTS.HolyDark
	RectTransform.SetAnchoredPositionXY(self.node_list.jinjie_right_root.rect, 600, 0)
	local canvas_group = self.node_list.month_buy_btn.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))

	canvas_group.alpha = 0
	local right_tween = self.node_list.jinjie_right_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
	right_tween:OnComplete(function()
		self.node_list.month_buy_btn.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
	end)
end


-----------------------------
HolyDarkJinjieRender = HolyDarkJinjieRender or BaseClass(BaseRender)

function HolyDarkJinjieRender:LoadCallBack()
	self.equip_item = ItemCell.New(self.node_list["equip_cell"])
    self.equip_item:SetIsShowTips(false)
	self.equip_item:SetClickCallBack(BindTool.Bind(self.OnItemClick, self))
end

function HolyDarkJinjieRender:__delete()
	if self.equip_item then
        self.equip_item:DeleteMe()
        self.equip_item = nil
    end
end

function HolyDarkJinjieRender:OnFlush()
	if not self.data then
		return
	end

	self.equip_item:SetData({item_id = self.data.item_id})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name_text.text.text = item_cfg and item_cfg.name or ""
	self.node_list.jinjie_grade.text.text = string.format(Language.HolyDarkWeapon.JinJieStr, self.data.grade) 
	self.node_list.hl_name_text.text.text = item_cfg and item_cfg.name or ""
	self.node_list.hl_jinjie_grade.text.text = string.format(Language.HolyDarkWeapon.JinJieStr, self.data.grade)
	local is_up = HolyDarkWeaponWGData.Instance:GetRelicSlotCanUpGrade(self.data)
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(self.data.relic_seq)
	local is_can_get = HolyDarkWeaponWGData.Instance:GetRelicPowerCanGet(relic_cfg.type)
	self.node_list.remind:SetActive(is_up or is_can_get)
end

function HolyDarkJinjieRender:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.nor_img:SetActive(not is_select)
	self.node_list.hl_img:SetActive(is_select)
end

function HolyDarkJinjieRender:OnItemClick()
	if not self.data then
		return
	end

	TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_RELIC_EQUIP)
end

-----
local Tween_Time = {
	[0] = 0.8,
	[1] = 1,
	[2] = 1.25,
	[3] = 1.5,
	[4] = 2,
}

HloDarkPowerCell = HloDarkPowerCell or BaseClass(BaseGridRender)
function HloDarkPowerCell:__init(instance, parent)
	self.is_move_tween = false
	XUI.AddClickEventListener(self.node_list["click"], BindTool.Bind(self.ClickCell, self))
	self.parent = parent
end

function HloDarkPowerCell:__delete()
	if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end

    if self.up_tween then
		self.up_tween:Kill()
		self.up_tween = nil
	end

	if self.once_move_tween then
        self.once_move_tween:Kill()
        self.once_move_tween = nil
    end

	self.is_move_tween = nil
	self.parent = nil
end

function HloDarkPowerCell:OnFlush()
	if not self.data then
		return
	end

	self.node_list.power_value.text.text = self.data.power_value
	self.node_list.power_cell:SetActive( self.data.power_value > 0)
end

function HloDarkPowerCell:ClickCell()
	if not self.data or self.data.power_value <= 0 then
		return
	end

	if self.is_move_tween then
		return
	end

	if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end

    if self.up_tween then
        self.up_tween:Kill()
        self.up_tween = nil
    end

    if self.once_move_tween then
        self.once_move_tween:Kill()
        self.once_move_tween = nil
    end

    self.is_move_tween = true
	self.move_tween = self.node_list["power_cell"].rect:DOAnchorPos(Vector2(0, 0), 1):SetEase(DG.Tweening.Ease.Linear):OnComplete(function()
		self.is_move_tween = false
		self.node_list.power_cell:SetActive(false)
		self.parent:PlayPowerEffct()
		HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.POWER_FETCH, self.data.power_type, self.data.index)
	end)
end

function HloDarkPowerCell:PlayUpTween()
	if self.up_tween then
        self.up_tween:Kill()
        self.up_tween = nil
    end

    local tween_root = self.node_list.power_cell.rect
	local tween_yoyo = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 10, Tween_Time[(self.index % 4)])
	tween_yoyo:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	self.up_tween = tween_yoyo
end

function HloDarkPowerCell:PlayOnceGetTween()
	if not self.data or self.data.power_value <= 0 then
		return
	end

	if self.move_tween then
        self.move_tween:Kill()
        self.move_tween = nil
    end

    if self.up_tween then
        self.up_tween:Kill()
        self.up_tween = nil
    end

    if self.once_move_tween then
        self.once_move_tween:Kill()
        self.once_move_tween = nil
    end

    self.is_move_tween = true
    self.once_move_tween = self.node_list["power_cell"].rect:DOAnchorPos(Vector2(0, 0), 1):SetEase(DG.Tweening.Ease.Linear):OnComplete(function()
		self.is_move_tween = false
		self.node_list.power_cell:SetActive(false)
	end)
end