-- 已屏蔽未执行
function TianShenView:HeJiLoadCallBack()
	if not self.ts_heji_list then
		self.ts_heji_list = AsyncListView.New(TianShenHeJiItem, self.node_list.ts_heji_list)
        self.ts_heji_list:SetSelectCallBack(BindTool.Bind(self.OnClickTianShenHeJiItem, self))
	end

    if not self.heji_skill_cost_cell then
		self.heji_skill_cost_cell = ItemCell.New(self.node_list["heji_cost_item_cell"])
	end

    if not self.ts_heji_model_list then
        self.ts_heji_model_list = {}
        for i = 1, 5 do
            self.ts_heji_model_list[i] = TianShenHeJiModelRender.New(self.node_list["ts_hj_model_" .. i], self)
            self.ts_heji_model_list[i]:SetIndex(i)
        end
    end

    XUI.AddClickEventListener(self.node_list["heji_skill_act_btn"], BindTool.Bind(self.OnClickHeJiSkillAct, self))
    XUI.AddClickEventListener(self.node_list["heji_skill_chuzhan_btn"], BindTool.Bind(self.OnClickHeJiSkillChuZhan, self))
    XUI.AddClickEventListener(self.node_list["heji_skill_show_btn"], BindTool.Bind(self.OnClickHeJiSkillShow, self))
end

function TianShenView:HeJiReleseCallBack()
	if self.ts_heji_list then
		self.ts_heji_list:DeleteMe()
		self.ts_heji_list = nil
	end

    if self.heji_skill_cost_cell then
		self.heji_skill_cost_cell:DeleteMe()
		self.heji_skill_cost_cell = nil
	end

    if self.ts_heji_model_list then
		for k, v in pairs(self.ts_heji_model_list) do
			v:DeleteMe()
		end
	end
    self.ts_heji_model_list = nil

    self.select_ts_heji_seq = nil
    self.select_ts_heji_data = nil
end

function TianShenView:HeJiShowIndexCallBack()

end

function TianShenView:HeJiInfoViewOnFlush()
    local list_info = TianShenWGData.Instance:GetTianShenHeJiList(true)
    self.ts_heji_list:SetDataList(list_info)
    if not self.select_ts_heji_seq then
        local jump_index = 1
        for k, v in ipairs(list_info) do
            if TianShenWGData.Instance:GetTianShenHaJiOneRemind(v.seq) then
                jump_index = k
            end
        end
        self.ts_heji_list:JumpToIndex(jump_index)
    else
        local jump_index = self.ts_heji_list:GetSelectIndex()
        for k, v in pairs(list_info) do
            if v.seq == self.select_ts_heji_seq then
                self.select_ts_heji_data = v
                jump_index = k
                break
            end
        end
        self.ts_heji_list:JumpToIndex(jump_index)
    end

    self:FlushHeJiRightPanel()
    self:FlushHeJiSkillModelInfo()
end

function TianShenView:OnClickTianShenHeJiItem(item)
	if nil == item or nil == item.data then
		return
	end

    local data = item.data

    if self.select_ts_heji_seq == data.seq then
        return
    end

    self.select_ts_heji_seq = data.seq
    self.select_ts_heji_data = data

    self:FlushHeJiRightPanel()
    self:FlushHeJiSkillModelInfo()
end

function TianShenView:FlushHeJiRightPanel()
    if not self.select_ts_heji_data then
        return
    end

    self:FlushHeJiJBanSkill()
    self:FlushHeJiSkillInfo()
    self:FlushHeJiSkillChuZhanInfo()
end

function TianShenView:FlushHeJiJBanSkill()
    if not self.select_ts_heji_data then
        return
    end

    local bundle, asset = ResPath.GetSkillIconById(self.select_ts_heji_data.skill_icon)
    self.node_list.heji_jiban_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.heji_jiban_skill_icon.image:SetNativeSize()
    end)

    self.node_list.heji_jiban_skill_desc.text.text = self.select_ts_heji_data.skill_describe
    local color = self.select_ts_heji_data.jiban_flag and COLOR3B.D_GREEN or COLOR3B.D_RED
    local state_str = ToColorStr(self.select_ts_heji_data.jiban_flag and Language.TianShen.HeJiState[1] or Language.TianShen.HeJiState[2], color)
    self.node_list.heji_jiban_skill_name.text.text = string.format(Language.TianShen.HeJiSkillName, self.select_ts_heji_data.skill_name, state_str)
    self.node_list.heji_jiban_skill_lock:SetActive(not self.select_ts_heji_data.jiban_flag)
end

function TianShenView:FlushHeJiSkillInfo()
    if not self.select_ts_heji_data then
        return
    end

    local icon_id = SkillWGData.Instance:GetSkillIconId(self.select_ts_heji_data.tianshen_heji_skill_id)
	local bundle, asset = ResPath.GetSkillIconById(icon_id)
    self.node_list.heji_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.heji_skill_icon.image:SetNativeSize()
    end)

    self.node_list.heji_skill_lock:SetActive(not self.select_ts_heji_data.skill_flag)
    local color = self.select_ts_heji_data.skill_flag and COLOR3B.D_GREEN or COLOR3B.D_RED
    local skill_cfg = SkillWGData.Instance:GetTianShenHeJiSkillById(self.select_ts_heji_data.tianshen_heji_skill_id, 1)
    local skill_info = SkillWGData.Instance:GetSkillClientConfig(self.select_ts_heji_data.tianshen_heji_skill_id)
    local state_str = ToColorStr(self.select_ts_heji_data.skill_flag and Language.TianShen.HeJiState[1] or Language.TianShen.HeJiState[2], color)
    local skill_name = skill_cfg and skill_cfg.skill_name
    self.node_list.heji_skill_name.text.text = string.format(Language.TianShen.HeJiSkillName, skill_name, state_str)
    self.node_list.heji_skill_desc.text.text = skill_info and skill_info.description
end

function TianShenView:FlushHeJiSkillChuZhanInfo()
    if not self.select_ts_heji_data then
        return
    end

	local active_num = 0
	local ts_idx_list = {}
	local temp_data = Split(self.select_ts_heji_data.tianshen_list or "", "|")
	if not IsEmptyTable(temp_data) then
		for i, v in ipairs(temp_data) do
			ts_idx_list[i] = {}
			ts_idx_list[i].ts_index = tonumber(v)

			if TianShenWGData.Instance:IsActivation(tonumber(v)) then
				active_num = active_num + 1
			end
		end
	end

    local act_color = active_num >= #temp_data and COLOR3B.D_GREEN or COLOR3B.D_RED
    local num_str = ToColorStr(active_num .. "/" .. #temp_data, act_color)
    self.node_list.heji_skill_act_desc.text.text = string.format(Language.TianShen.HeJiJiBanAct, num_str)

    self.node_list.heji_skill_act:SetActive(self.select_ts_heji_data.skill_flag)
    self.node_list.heji_skill_not_act:SetActive(not self.select_ts_heji_data.skill_flag)
    if self.select_ts_heji_data.skill_flag then
        self.node_list.heji_skill_chuzhan_flag:SetActive(self.select_ts_heji_data.is_use)
        self.node_list.heji_skill_chuzhan_btn:SetActive(not self.select_ts_heji_data.is_use)
    else
        self.heji_skill_cost_cell:SetData({item_id = self.select_ts_heji_data.heji_item_id})
        local item_num = ItemWGData.Instance:GetItemNumInBagById(self.select_ts_heji_data.heji_item_id)
        local color = item_num >= self.select_ts_heji_data.need_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        local cost_str = item_num .. "/" .. self.select_ts_heji_data.need_item_num
        self.node_list.heji_cost_item_num.text.text = ToColorStr(cost_str, color)
        local is_red = TianShenWGData.Instance:GetTianShenHaJiOneRemind(self.select_ts_heji_data.seq)
        self.node_list.heji_skill_act_red:SetActive(is_red)
    end
end

function TianShenView:FlushHeJiSkillModelInfo()
    if not self.select_ts_heji_data then
        return
    end

    local temp_data = Split(self.select_ts_heji_data.tianshen_list or "", "|")
    local scale_data = Split(self.select_ts_heji_data.tianshen_list_scale or "", "|")
    local pos_uix_data = Split(self.select_ts_heji_data.pos_ui_x or "", "|")
    local pos_uiy_data = Split(self.select_ts_heji_data.pos_ui_y or "", "|")
    local pos_modelx_data = Split(self.select_ts_heji_data.pos_model_x or "", "|")
    local pos_modely_data = Split(self.select_ts_heji_data.pos_model_y or "", "|")
    local pos_modelz_data = Split(self.select_ts_heji_data.pos_model_z or "", "|")
    local model_list = {}
    if not IsEmptyTable(temp_data) then
		for i, v in ipairs(temp_data) do
			model_list[i] = {}
			model_list[i].ts_index = tonumber(v)
            model_list[i].active = TianShenWGData.Instance:IsActivation(tonumber(v))
            model_list[i].model_scale = tonumber(scale_data[i]) or 0.5
            model_list[i].pos_ui_x = tonumber(pos_uix_data[i]) or 0
            model_list[i].pos_ui_y = tonumber(pos_uiy_data[i]) or 0
            model_list[i].pos_model_x = tonumber(pos_modelx_data[i]) or 0
            model_list[i].pos_model_y = tonumber(pos_modely_data[i]) or 0
            model_list[i].pos_model_z = tonumber(pos_modelz_data[i]) or 0
		end
	end

    for i = 1, #self.ts_heji_model_list do
		self.ts_heji_model_list[i]:SetData(model_list[i])
	end
end

function TianShenView:OnClickHeJiSkillAct()
    if not self.select_ts_heji_data or self.select_ts_heji_data.skill_flag then
        return
    end

    local active_num = 0
	local ts_idx_list = {}
	local temp_data = Split(self.select_ts_heji_data.tianshen_list or "", "|")
	if not IsEmptyTable(temp_data) then
		for i, v in ipairs(temp_data) do
			ts_idx_list[i] = {}
			ts_idx_list[i].ts_index = tonumber(v)

			if TianShenWGData.Instance:IsActivation(tonumber(v)) then
				active_num = active_num + 1
			end
		end
	end

    if active_num < #temp_data then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.HeJISkillNotAct)
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.select_ts_heji_data.heji_item_id)
    if item_num >= self.select_ts_heji_data.need_item_num then
        TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type22, self.select_ts_heji_data.seq)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.select_ts_heji_data.heji_item_id})
    end
end

function TianShenView:OnClickHeJiSkillChuZhan()
    if not self.select_ts_heji_data then
        return
    end

    if not self.select_ts_heji_data.skill_flag then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.HeJISkillNotUse)
        return
    end

    if self.select_ts_heji_data.is_use then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.HeJISkillHaveUse)
        return
    end

    TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type23, self.select_ts_heji_data.seq, 0) --默认传0
end

function TianShenView:HeJiSkillEffectActive()
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["ts_heji_effect"]})
end

function TianShenView:OnClickHeJiSkillShow()
    if not self.select_ts_heji_data then
        return
    end

    local data = {}
    data.jiban_seq = self.select_ts_heji_data.seq

    CommonSkillShowCtrl.Instance:SetTianShenHeJiSkillViewDataAndOpen(data)
end

------------------------- TianShenHeJiItem -------------------------
TianShenHeJiItem = TianShenHeJiItem or BaseClass(BaseRender)

function TianShenHeJiItem:__init()

end

function TianShenHeJiItem:__delete()

end

function TianShenHeJiItem:OnFlush()
    if self.data == nil then 
        return 
    end

    local bg_bundle, bg_asset = ResPath.GetNoPackPNG("a2_ts_hj_icon_" .. self.data.seq)
    self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset, function()
		self.node_list.bg.image:SetNativeSize()
 	end)

    local bq_bundle, bq_asset
    local str = ""
    if self.data.is_use then
        bq_bundle, bq_asset = ResPath.GetF2TianShenImage("a2_ts_bq3")
        str = Language.TianShen.HeJiState[0]
    elseif self.data.skill_flag then
        bq_bundle, bq_asset = ResPath.GetF2TianShenImage("a2_ts_bq1")
        str = Language.TianShen.HeJiState[1]
    else
        bq_bundle, bq_asset = ResPath.GetF2TianShenImage("a2_ts_bq2")
        str = Language.TianShen.HeJiState[2]
    end

    self.node_list.state_img.image:LoadSprite(bq_bundle, bq_asset, function()
		self.node_list.state_img.image:SetNativeSize()
 	end)

    self.node_list.state_text.text.text = str

    local is_red = TianShenWGData.Instance:GetTianShenHaJiOneRemind(self.data.seq)
    self.node_list.remind:SetActive(is_red)
end

function TianShenHeJiItem:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.img_hl:SetActive(is_select)
end

---------TianShenHeJiModelRender--------
TianShenHeJiModelRender = TianShenHeJiModelRender or BaseClass(BaseRender)

function TianShenHeJiModelRender:__init()
    if not self.role_model then
        self.role_model = RoleModel.New()
        self.role_model:SetVisible(true)
        local display_data = {
            parent_node = self.node_list["model_pos"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.role_model:SetRenderTexUI3DModel(display_data)
        -- self.role_model:SetUI3DModel(self.node_list["model_pos"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
    end
end

function TianShenHeJiModelRender:__delete()
    if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

    self.old_appe_image_id = nil
end

function TianShenHeJiModelRender:OnFlush()
    if self.data == nil then 
        self.old_appe_image_id = nil
		self.role_model:RemoveMain()
        self.node_list.item_root:SetActive(false)
        self.node_list["model_pos"]:SetActive(false)
        return 
    end


    local ts_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.ts_index)
    if not ts_cfg then
        return
    end

    self.node_list.item_root:SetActive(true)
    self.node_list["model_pos"]:SetActive(true)
    self.node_list.ts_name.text.text = ts_cfg.bianshen_name
    self.node_list.no_act:SetActive(not self.data.active)

    local shenshi_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.data.ts_index)
    local bundle, asset = ResPath.GetCommon("a2_quality_text_" .. shenshi_data.jingshen + 1)
    local bundel1, asset1 = ResPath.GetCommon("a2_sl_di_" .. shenshi_data.jingshen + 1)
    self.node_list.quality_img.image:LoadSprite(bundle, asset, function()
        self.node_list.quality_img.image:SetNativeSize()
    end)

    self.node_list.sl_img.image:LoadSprite(bundel1, asset1, function()
        self.node_list.sl_img.image:SetNativeSize()
    end)

    self:FlushModelDisPlay(ts_cfg)

    Transform.SetLocalScaleXYZ(self.node_list["model_pos"].transform, self.data.model_scale, self.data.model_scale, self.data.model_scale)
    RectTransform.SetAnchoredPositionXY(self.node_list.item_root.rect, self.data.pos_ui_x , self.data.pos_ui_y)
    RectTransform.SetAnchoredPosition3DXYZ(self.node_list.model_pos.rect, self.data.pos_model_x , self.data.pos_model_y, self.data.pos_model_z)
end

--刷新模型展示
function TianShenHeJiModelRender:FlushModelDisPlay(ts_cfg)
	local appe_image_id = ts_cfg.appe_image_id
	if not appe_image_id then
		return
	end

	if self.old_appe_image_id == appe_image_id then
		return
	end

	self.old_appe_image_id = appe_image_id
	self.role_model:SetTianShenModel(appe_image_id, ts_cfg.index, true, nil, SceneObjAnimator.Rest)
end
