TaskGainCapabilityTipView = TaskGainCapabilityTipView or BaseClass(SafeBaseView)
function TaskGainCapabilityTipView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/activity_ui_prefab", "layout_actbtn_list")
    self.caozuolist={}
    self.view_name = "TaskGainCapabilityTipView"
	
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.root_pos = Vector2(460, 150)
end

function TaskGainCapabilityTipView:__delete()
end

function TaskGainCapabilityTipView:LoadCallBack()

end

function TaskGainCapabilityTipView:ShowIndexCallBack()

end

function TaskGainCapabilityTipView:ReleaseCallBack()
	for k,v in pairs(self.caozuolist) do
		v:DeleteMe()
	end
	self.caozuolist ={}
end

function TaskGainCapabilityTipView:OpenCallBack()
	self:Flush()
end

function TaskGainCapabilityTipView:CloseCallBack()
	self.root_pos = Vector2(460, 150)
end

function TaskGainCapabilityTipView:SetRootPos(pos)
	if pos then
		self.root_pos = pos
	end
end

function TaskGainCapabilityTipView:OnFlush()
	local btn_data_list = TaskWGData.Instance:GetGainCapaTipBtnList()
	for i = 1, #btn_data_list do
		if self.caozuolist[i] ~= nil then
			if 0 < self.node_list.ph_btn_list.transform.childCount then
				self.node_list.ph_btn_list.transform:GetChild(i-1).gameObject:SetActive(true)
			end
		else
			self.caozuolist[i] = TaskGainCapabilityTipItem.New()
			self.caozuolist[i]:SetIndex(i)
			self.caozuolist[i]:SetParent(self.node_list.ph_btn_list.transform)
		end
	end

	for i=#btn_data_list + 1, #self.caozuolist do
		if 0 < self.node_list.ph_btn_list.transform.childCount then
			self.node_list.ph_btn_list.transform:GetChild(i-1).gameObject:SetActive(false)
		end
	end
	for i = 1,#btn_data_list do
		self.caozuolist[i]:SetTuiJianVis(btn_data_list[i].tuijian)
		self.caozuolist[i]:SetName(btn_data_list[i].cfg.text)
		self.caozuolist[i]:FlushRedRemind(false)
		self:AddClick(self.caozuolist[i], btn_data_list[i])
	end
end
	--end
	--print_error("列表长度",#self.caozuo_data_list,"身份",RoleWGData.Instance.role_vo.guild_post,"生成按钮个数",#self.caozuolist)



function TaskGainCapabilityTipView:AddClick(obj, data)
	obj:AddClickListener(BindTool.Bind(self.OnClickOpen, self, data))
end


function TaskGainCapabilityTipView:OnClickOpen(data)
	if not data then
		return
	end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if data.cfg.id == UP_LV_GUIDE_ID.RI_CHANG then
		if RoleWGData.Instance.role_vo.level < 52 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuo.DailyTip)
			return
		end

		--比较极端的情况下会出现任务刚完成协议没过来打开了界面，按钮存在点击无反应
		if TaskWGData.Instance:DailyTaskIsFinsh() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuo.DailyFinishTip)
			self:Close()
			return
		end
		local ok_func = function()
			TaskWGData.Instance:DoShangJinTask()
		end
		TaskWGCtrl.Instance:CheckCanChangeSceneToFollowTask(ok_func)
	elseif data.cfg.id == UP_LV_GUIDE_ID.GUA_JI then
		GuajiWGCtrl.Instance:TryGoToSit()

    elseif data.cfg.id == UP_LV_GUIDE_ID.WORLD_BOSS then
        local cur_times, _ = BossWGData.Instance:GetWorldBossTimes() --有次数跳世界boss，没次数跳vipboss
        if cur_times > 0 then
            FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, TabIndex.boss_world)
        else
            FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, TabIndex.boss_vip)
        end
	else
		FunOpen.Instance:OpenViewByName(data.cfg.view_name, data.cfg.tab_index)
	end
	self:Close()
end

------------------------------TaskGainCapabilityTipItem-------------------------------------------------
TaskGainCapabilityTipItem = TaskGainCapabilityTipItem or BaseClass(BaseRender)--借用帮派里边的预制体

function TaskGainCapabilityTipItem:__init(instance)
	self.tuijian_vis = 0
end

function TaskGainCapabilityTipItem:__delete()
	self.name = nil
	self.callback = nil
	self.falg  = nil
end

function TaskGainCapabilityTipItem:GetWidgets(res_name)
	return "uis/view/rolebag_ui_prefab", res_name
end

function TaskGainCapabilityTipItem:SetParent(parent)
	if not parent then return end
	if nil == self.root_node then
		local bundle, asset = self:GetWidgets("btn_item")

        local async_loader = AllocAsyncLoader(self, "btn_item")
		async_loader:SetParent(parent)
		async_loader:Load(bundle, asset, function(obj)
        	if not obj then
        		print_error("can not find the asset!!")
        		return
        	end
			self.name_table = obj:GetComponent(typeof(UINameTable))			-- 名字绑定
			self.node_list = U3DNodeList(self.name_table, self)
			self.obj = obj
			self.obj.transform.localScale = Vector3(1,1,1)
			self.obj.transform.localPosition = Vector3(0,0,0)
			self:SetTuiJianVis(self.tuijian_vis)
			self:SetName(self.name)
			self:AddClickListener(self.callback)
			self:FlushRedRemind(self.falg)
        end)
	end
end

function TaskGainCapabilityTipItem:SetName(value)
	self.name = value
	if self.node_list then
		self.node_list.lbl_caozuo_name.text.text = self.name
	end
end

function TaskGainCapabilityTipItem:AddClickListener(callback)
	if not self.node_list then
		self.callback = callback
	else
		XUI.AddClickEventListener(self.node_list.btn,callback)
	end
end

	-- 刷新红点
function TaskGainCapabilityTipItem:FlushRedRemind(flag)
	if not self.node_list then
		self.falg = flag
	else
		self.node_list.red_remind.image.enabled = flag
	end
end

function TaskGainCapabilityTipItem:SetTuiJianVis(value)
	if not self.node_list then
		self.tuijian_vis = value
	else
		self.node_list.img_tuijian:SetActive(value == 1) 		-- 推荐标签
		self.node_list.img_exp:SetActive(value == 2) 			-- 经验标签
	end
end
