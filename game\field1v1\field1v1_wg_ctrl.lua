require("game/field1v1/field1v1_wg_data")
require("game/field1v1/field1v1_view")
require("game/field1v1/field1v1_finish")
require("game/field1v1/field1v1_view_yinxiong")
require("game/field1v1/field1v1_view_tiaozhan_record")
require("game/field1v1/field1v1_view_tiaozhan")
require("game/field1v1/field1v1_reward_info_view")
-- require("game/crossserver/kuafu_onevone/kf_onevone_view")
require("game/field1v1/kfzhanchang_infoview")
require("game/crossserver/kuafu_onevone/kf_prepare_panel")
require("game/crossserver/kuafu_onevone/one_vs_one_rank_item")

--新 跨服1v1
require("game/field1v1/kf_onevone/kf_onevone_wg_ctrl")


Field1v1WGCtrl = Field1v1WGCtrl or BaseClass(BaseWGCtrl)
function Field1v1WGCtrl:__init()
	if Field1v1WGCtrl.Instance then
		ErrorLog("[Field1v1WGCtrl] Attemp to create a singleton twice !")
	end
	Field1v1WGCtrl.Instance = self
	self.data = Field1v1WGData.New()

	self.view = Field1v1View.New(GuideModuleName.ActJjc)						-- 挑战主面板
	-- self.view_jl = Field1v1JiLuView.New()									-- 挑战记录
	self.field_buy_view = FieldBuyView.New()
	self.view_yx = Field1v1YinXiongView.New(GuideModuleName.ActJjc_yingxiong)	-- 英雄榜
	self.view_finish = FieldFinish.New(GuideModuleName.FieldFinish)				-- 挑战结果
	self.reward_view = Field1v1RewardInfoView.New()								-- 排名奖励
	self.field_record = FieldChangellengeRecordPanel.New()                      -- 挑战记录

	self.rank_view = FieldPvPRankView.New(GuideModuleName.PvPRank)				-- 跨服pvp排行
	self.aim_view = FieldPvPAimView.New(GuideModuleName.PvPSeasonReward)		-- 跨服pvp赛季奖励
	-- self.gongxun_view = FieldPvPGongXunView.New(GuideModuleName.PvPGongXunReward)	-- 跨服pvp功勋奖励
	self.kf_pk_info_view = KFZhanChangInfoView.New()                            -- 活动开启提示
	self.kf_onevone_prepare_view = KfOneVSOnePrepareInfoPanel.New()             -- 跨服1v1准备场景排行显示
	self.kf_pvp_prepare_view = KfPVPPrepareInfoPanel.New()             			-- 跨服3v3准备场景排行显示

	self:RegisterAllProtocols()
	self:InitKFOneVOneWGCtrl()
end

function Field1v1WGCtrl:__delete()
	Field1v1WGCtrl.Instance = nil

	if self.field_buy_view then
		self.field_buy_view:DeleteMe()
		self.field_buy_view = nil
	end

	if self.kf_pk_info_view then
		self.kf_pk_info_view:DeleteMe()
		self.kf_pk_info_view = nil
	end

	if self.kf_onevone_prepare_view then
		self.kf_onevone_prepare_view:DeleteMe()
		self.kf_onevone_prepare_view = nil
	end

	if self.kf_pvp_prepare_view then
		self.kf_pvp_prepare_view:DeleteMe()
		self.kf_pvp_prepare_view = nil
	end

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.rank_view:DeleteMe()
	self.rank_view = nil

	self.aim_view:DeleteMe()
	self.aim_view = nil

	-- self.gongxun_view:DeleteMe()
	-- self.gongxun_view = nil

	self.view_yx:DeleteMe()
	self.view_yx = nil

	self.view_finish:DeleteMe()
	self.view_finish = nil

	self.reward_view:DeleteMe()
	self.reward_view = nil

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	if self.field_record then
		self.field_record:DeleteMe()
		self.field_record  = nil
	end

	self:DeleteKFOneVOneWGCtrl()
end

function Field1v1WGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCChallengeFieldStatus, "OnSCChallengeFieldStatus")
	self:RegisterProtocol(SCChallengeFieldUserInfo, "OnSCChallengeFieldUserInfo")
	self:RegisterProtocol(SCChallengeFieldRewardGoldNotice, 'OnSCChallengeFieldRewardGoldNotice')
	self:RegisterProtocol(SCChallengeFieldReportInfo, "OnSCChallengeFieldReportInfo")
	self:RegisterProtocol(SCChallengeFieldRankInfo, "OnSCChallengeFieldRankInfo")
	self:RegisterProtocol(SCChallengeFieldOpponentInfo, 'OnSCChallengeFieldOpponentInfo')

	self:RegisterProtocol(CSChallengeFieldGetRankInfo)
	self:RegisterProtocol(CSChallengeFieldGetUserInfo)
	self:RegisterProtocol(CSChallengeFieldResetOpponentList)
	self:RegisterProtocol(CSChallengeFieldFightReq)
	self:RegisterProtocol(CSChallengeFieldFetchJifenRewardReq)
	self:RegisterProtocol(CSChallengeFieldBuyJoinTimes)
	self:RegisterProtocol(CSChallengeFieldFetchShengwang)
	self:RegisterProtocol(CSChallengeFieldGetOpponentInfo)
	self:RegisterProtocol(CSChallengeFieldClearCdTime)
	self:RegisterProtocol(CSCrossEnterActivtyReq)
	self:RegisterProtocol(CSCrossActivtyTurnBackReq)
	self:RegisterProtocol(CSChallengeFieldSkipFighting)
	self:RegisterProtocol(CS1V1ReturnReadySceneReq)

	self:RegisterProtocol(CSSweepChallengeFieldReq)
	self:RegisterProtocol(SCSweepChallengeFieldResault, 'OnSCSweepChallengeFieldResault')


	self:RegisterProtocol(SCCross1V1StandbySceneRank, 'FlushOneVOnePrepareRankInfo')
	self:RegisterProtocol(SCCross3V3StandbySceneRank, 'FlushPVPPrepareRankInfo')

	self:RegisterProtocol(SCChallengeFieldSkipResult, "OnSCChallengeFieldSkipResult") --跳过战斗返回

	self:RegisterProtocol(CSChallengeFieldOperate)
	self:RegisterProtocol(SCChallengeRankListInfo, 'OnSCChallengeRankListInfo')
	self:RegisterProtocol(SCChallengeFieldRewardInfo, 'OnSCChallengeFieldRewardInfo')
	self:RegisterProtocol(SCChallengeFieldInfo, 'OnSCChallengeFieldInfo')
end

function Field1v1WGCtrl:Open(tab_index, param_t)
	self.view:Open(tab_index)
	self:ResetOpponentList()
	self:ReqOtherRoleInfo(0)
end
function Field1v1WGCtrl:FlushOneVOnePrepareRankInfo(protocol)
	self.data:SetOneVOnePrepareInfo(protocol.rank_list)
	--if self.kf_onevone_prepare_view and self.kf_onevone_prepare_view:IsOpen() then
		self.kf_onevone_prepare_view:Flush()
	--end
end
function Field1v1WGCtrl:OpenPreparePanwl(active_type)
	if active_type == ACTIVITY_TYPE.KF_ONEVONE then
		self.kf_onevone_prepare_view:Open()
	else
		self.kf_pvp_prepare_view:Open()
	end
end
function Field1v1WGCtrl:ClosePreparePanwl(active_type)
	if active_type == ACTIVITY_TYPE.KF_ONEVONE then
		self.kf_onevone_prepare_view:Close()
	else
		self.kf_pvp_prepare_view:Close()
	end
end
function Field1v1WGCtrl:FlushPVPPrepareRankInfo(protocol)
	self.data:SetPvpPrepareInfo(protocol.rank_list)
	if self.kf_pvp_prepare_view and self.kf_pvp_prepare_view:IsOpen() then
		self.kf_pvp_prepare_view:Flush()
	end
end
function Field1v1WGCtrl:OpenTiaoZhanPanel(tab_index, param_t)
	local boo = Field1v1WGData.Instance:GetGuideFlag()
	if boo ~= nil then
		if boo == true then
			Field1v1WGData.Instance:SetGuideFlag(false)
			return
		else
			self.view:Open(tab_index)
		end
	else
		self.view:Open(tab_index)
	end
	self:ResetOpponentList()
	self:ReqOtherRoleInfo(0)
end

function Field1v1WGCtrl:CloseTiaoZhanPanel()
	self.view:OnClosePanelAndHall()
end

--排行榜奖励
function Field1v1WGCtrl:OpenYinXiongPanel()
	self.view_yx:Open()
	self:ReqFieldGetRankInfo()
	self:ReqOtherRoleInfo(1)
end

-- 新加荣誉商店按钮
function Field1v1WGCtrl:OpenHonorShopPanel()
	ViewManager.Instance:Open(GuideModuleName.Market, ShopWGData.ShowTypeToIndex.Tab_Shop60)
end

function Field1v1WGCtrl:OpenYingXionTipPopPanel()
	RuleTip.Instance:SetContent(Language.Field1v1.YingXionBangTip, Language.Field1v1.YingXionBangTitle)
end

function Field1v1WGCtrl:OpenRewardInfo()
	self.reward_view:Open()
end

-- 刷新挑战对手
function Field1v1WGCtrl:ResetOpponentList()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldResetOpponentList)
	send_protocol:EncodeAndSend()
end

-- 领取积分奖励
function Field1v1WGCtrl:ResetGetJiFenReward(seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldFetchJifenRewardReq)
	send_protocol.reward_seq = seq
	send_protocol:EncodeAndSend()
end

-- 请求进入挑战
function Field1v1WGCtrl:ResetFieldFightReq(data)
	if IS_ON_MATCHING then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.NoEnterFB)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Field1v1 then
		self.view:Close()
	end
		-- 请求进入副本
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldFightReq)
	send_protocol.opponent_index = data.opponent_index or 0
	send_protocol.ignore_rank_pos = 1
	send_protocol.rank_pos = data.rank_pos or 0
	send_protocol.is_skip = data.is_skip or 0
	send_protocol.opponent_uid = data.opponent_uid
	send_protocol:EncodeAndSend()
--		print_error("请求进入副本")
	-- Scene.Instance:GetMainRole():JumpUpSceneChange(function ()
	-- 	Scene.Instance:SetRoleChangeSceneEff(true)

	-- 	-- 请求进入副本
	-- 	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldFightReq)
	-- 	send_protocol.opponent_index = data.opponent_index or 0
	-- 	send_protocol.ignore_rank_pos = 1
	-- 	send_protocol.rank_pos = data.rank_pos or 0
	-- 	send_protocol.is_auto_buy = data.is_auto_buy or 0
	-- 	send_protocol.opponent_uid = data.opponent_uid
	-- 	send_protocol:EncodeAndSend()
	-- end)
end

function Field1v1WGCtrl:SendField1V1Info()
	OperateFrequency.Operate(function()
		Field1v1WGCtrl.Instance:ReqFieldGetDetailRankInfo()
		Field1v1WGCtrl.Instance:ResetOpponentList()
		Field1v1WGCtrl.Instance:ReqOtherRoleInfo(0)
	end, "Field1v1WGCtrl:SendInfo", 2, "")
	if self.pick_delay_timer then
		GlobalTimerQuest:CancelQuest(self.pick_delay_timer)
		self.pick_delay_timer = nil
	end
	self.pick_delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.PickEnemyFieldFight, self), 0.5)
end

-- 选一个排名低的对手进入挑战
function Field1v1WGCtrl:PickEnemyFieldFight()
	local info = Field1v1WGData.Instance:GetUserinfo()
	if nil == info then
		self:SendField1V1Info()
		return
	end

	local sorted_list = info.rank_list
	table.sort(sorted_list, SortTools.KeyLowerSorter("rank"))

	local role_rank = info.rank
	local role_cap = RoleWGData.Instance:GetAttr("capability") or 0
	local rank_index = sorted_list[#sorted_list]
	if not IsEmptyTable(sorted_list) then
		for i = 1, #sorted_list do
			local data = sorted_list[i]
			local target_info = Field1v1WGData.Instance:GetRoleInfoByUid(data.user_id)
			if target_info and role_cap >= target_info.capability and role_rank > data.rank_pos then
				rank_index = data
				break
			end
		end
	end

	if nil == rank_index or nil == rank_index.user_id or rank_index.user_id == 0 then
		self:SendField1V1Info()
		return
	end

	local tz_info = Field1v1WGData.Instance:GetRoleTiaoZhanInfoByUid(rank_index.user_id)
	local data = {}
	data.opponent_index = tz_info.index
	data.rank_pos = tz_info.rank_pos
	data.opponent_uid = rank_index.user_id
	data.is_skip = 0
	self:ResetFieldFightReq(data)
end

-- 购买次数
function Field1v1WGCtrl:FieldBuyJoinTimes()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldBuyJoinTimes)
	send_protocol:EncodeAndSend()
end

-- 请求减少CD
function Field1v1WGCtrl:OnCSChallengeFieldClearCdTime()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldClearCdTime)
	send_protocol:EncodeAndSend()
end

-- 领取声望
function Field1v1WGCtrl:ReqGetShengWang(reward_type,reward_seq)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldFetchShengwang)
	send_protocol.reward_type = reward_type or 0
	send_protocol.reward_seq = reward_seq or 0
	send_protocol:EncodeAndSend()
	AudioService.Instance:PlayRewardAudio()
end

-- 请求英雄榜信息
function Field1v1WGCtrl:ReqFieldGetRankInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldGetRankInfo)
	send_protocol:EncodeAndSend()
end
--获取玩家挑战详细信息
function Field1v1WGCtrl:ReqFieldGetDetailRankInfo()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldGetUserInfo)
	send_protocol:EncodeAndSend()
end

-- 请求其它玩家详细信息
function Field1v1WGCtrl:ReqOtherRoleInfo(type)
	--print_error(type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldGetOpponentInfo)
	send_protocol.type = type
	send_protocol:EncodeAndSend()
end

-- 场景用户信息
function Field1v1WGCtrl:OnSCChallengeFieldStatus(protocol)
	-- print_error("---场景用户信息----", protocol.status)
	self.data.scene_user_list = protocol.scene_user_list
	self.data.scene_status = protocol.status
	self.data.scene_next_time = protocol.next_time
	if self.data.scene_status == FIELD1V1_STATUS.OVER then
		self:OpenFinish(self.data:GetResultData())
	end
	GlobalEventSystem:Fire(Field1v1Type.FIELD_STATUS_CHANGE, protocol.status)
end

function Field1v1WGCtrl:OpenFinish(data, from, is_skip)
	GlobalEventSystem:Fire(OtherEventType.BEFORE_OUT_SCENE)
	self.view_finish:OpenFinish(data, from, is_skip)
	if self.view_finish:IsOpen() then
		self.view_finish:Flush()
	else
		self.view_finish:Open()
	end

	--RobertManager.Instance:StopFight()
end

-- 挑战列表信息和个人信息
function Field1v1WGCtrl:OnSCChallengeFieldUserInfo(protocol)
	self.data:SetUserinfo(protocol.user_info)
	self.view:Flush()

	if self.view and self.view:IsOpen() then
		self.view:OnFlushTiaoZhan()
		self.view:BuyCountEnterFBCallBack()
	end

	if self.field_buy_view:IsOpen() then
		self.field_buy_view:Flush()
	end

	BiZuoWGCtrl.Instance:Flush()
	if self.reward_view:IsOpen() then
		self.reward_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.ActJjc)
	RemindManager.Instance:Fire(RemindName.ActJjcArena)
end

-- 通关结束奖励元宝
function Field1v1WGCtrl:OnSCChallengeFieldRewardGoldNotice(protocol)
	self.data.user_info_gold = protocol.user_info_gold
end

-- 返回玩家详细信息
function Field1v1WGCtrl:OnSCChallengeFieldOpponentInfo(protocol)
	self.view:Flush()
	if self.view and self.view:IsOpen() then
		self.view:OnFlushTiaoZhan()
	end

	self.view_yx:Flush()
end

-- 战报
function Field1v1WGCtrl:OnSCChallengeFieldReportInfo(protocol)
	self.data:SetReportinfo(protocol.report_info)
	self.field_record:Flush()
end

-- 英雄榜
function Field1v1WGCtrl:OnSCChallengeFieldRankInfo(protocol)
	self.data:SetRankinfo(protocol.rank_info)
	self.view_yx:Flush()
end


function Field1v1WGCtrl:OpenJiLuPanelByRemind()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FIELD1V1_FAIL, 0)
end

function Field1v1WGCtrl:GouMaiTimes()
	if nil == self.alert then
		self.alert = Alert.New()
		self.alert:SetShowCheckBox(true)
	end
	local ok_func = function()
		Field1v1WGCtrl.Instance:FieldBuyJoinTimes()
	end
	self.alert:SetOkFunc(ok_func)
	local jingjichang_fb_info = Field1v1WGData.Instance:GetUserinfo()
	if nil == jingjichang_fb_info then
		return
	end
	local vip_power_id = VipPowerId.jingjichang_buy_times
	if VipPower.Instance:GetHasPower(vip_power_id, true, jingjichang_fb_info.buy_join_times + 1, jingjichang_fb_info.buy_join_times) then
		self.alert:SetLableString(string.format(Language.Field1v1.AddNumTip, Field1v1WGData.Instance:GetChallengeFieldBuyConsume()))
		self.alert:Open()
	end
end

function Field1v1WGCtrl:ResetSweepChallengeFieldReq(opponent_uid, times)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSweepChallengeFieldReq)
	protocol.opponent_uid = opponent_uid
	protocol.times = times
	protocol:EncodeAndSend()
end

function Field1v1WGCtrl:OnSCSweepChallengeFieldResault(protocol)
	local data = FuBenWGData.CreatePassVo()
	data.fb_type = Scene.Instance:GetSceneType()
	data.is_passed = 1
	data.tip1 = ""
	data.get_exp = protocol.add_exp
	local user_info = self.data:GetUserinfo()

	if user_info ~= nil and self.data.last_rank ~= nil then
		data.tip1 = string.format(Language.Field1v1.ResultTip1, user_info.rank, self.data.last_rank)
	end

	data.tip2 = protocol.add_honor
	data.reward_item_list = protocol.item_id_list
	self:OpenFinish(data)
end

function Field1v1WGCtrl:Flush1v1RankView(index)
	self.view:Flush(index)
end

function Field1v1WGCtrl:FlushLiuJieRedPoint()
	self.view:IsHaveReward()
end

function Field1v1WGCtrl:FlushGongXunView(type1)
	-- if not self.gongxun_view:IsOpen() then return end
	-- self.gongxun_view:FlushRankGongXunPanel(type1)
end

function Field1v1WGCtrl:OpenPKInfoView(active_type,status)
	-- 普通场景才弹活动提示
    if Scene.Instance:GetSceneType() ~=	SceneType.Common then
    	return
    end

	if self.kf_pk_info_view then
		if status == ACTIVITY_STATUS.OPEN then
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.Kf_OneVOne_Prepare then
				local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
				MainuiWGCtrl.Instance:SetFbIconEndCountDown( activity_info.next_time )
				GlobalEventSystem:Fire(OtherEventType.KF1V1SceneInfoMatch)
			else
				self.kf_pk_info_view:SetShowData(active_type)
			end
		else
			self.kf_pk_info_view:Close()
		end
	end
end

function Field1v1WGCtrl:CheckIsCanOut()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_OneVOne_Prepare then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
		if activity_info.status == ACTIVITY_STATUS.STANDY then
			return false
		else
			return true
		end
	end
	return true
end
function Field1v1WGCtrl:LeaveZhanChangScene()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossActivtyTurnBackReq)
	send_protocol:EncodeAndSend()
end

-- 1v1 返回准备场景
function Field1v1WGCtrl:Send1V1ReturnReadyScene()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CS1V1ReturnReadySceneReq)
	send_protocol:EncodeAndSend()
end

--进入准备场景
function Field1v1WGCtrl:EnterFieldPrepareScene(active_type)
	--print_error("进入准备场景", active_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossStartReq)
	send_protocol.cross_activity_type = active_type
	send_protocol:EncodeAndSend()
end
--结束战斗请求
function Field1v1WGCtrl:EndFightArna()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldSkipFighting)
	send_protocol:EncodeAndSend()
end

function Field1v1WGCtrl:SetMatchBtnFalse()
	self.view:SetMatchBtnFalse()
	self.kf_onevone_prepare_view:SetMatchBtnFalse()
end

function Field1v1WGCtrl:SetMatchBtnFalsePVP()
	self.view:SetMatchBtnFalsePVP()
	self.kf_pvp_prepare_view:SetMatchBtnFalsePVP()
end

function Field1v1WGCtrl:OnClickMatchBtn(activity_typ)
	if nil == self.view then
		return
	end

	if activity_typ == ACTIVITY_TYPE.KF_ONEVONE then
		self.view:MatchingHandler()
	elseif activity_typ == ACTIVITY_TYPE.KF_PVP then
		self.view:MatchingHandlerPvP()
	end
end

function Field1v1WGCtrl:FlushPrepareScenePanel(activity_typ)
	if activity_typ == ACTIVITY_TYPE.KF_ONEVONE then
		if self.kf_onevone_prepare_view then
			self.kf_onevone_prepare_view:Flush()
		end
	elseif activity_typ == ACTIVITY_TYPE.KF_PVP then
		if self.kf_pvp_prepare_view then
			self.kf_pvp_prepare_view:Flush()
		end
	end
end

function Field1v1WGCtrl:OnSCChallengeFieldSkipResult(protocol)
	Field1v1WGData.Instance:SetRoleLevel(RoleWGData.Instance.role_vo.level)
	self:OpenFinish(self.data:GetResultData(protocol.is_win), nil, true)
end

function Field1v1WGCtrl:FlushTzInfo()
	if self.view:IsOpen() then
		self.view:RequestFlushChallengesList()
	end
end

function Field1v1WGCtrl:CloseView()
	self.view:Close()
end

function Field1v1WGCtrl:FieldBuyView()
	self.field_buy_view:Open()
end

function Field1v1WGCtrl:OpenChangellengePanel()
	if self.field_record then
		self.field_record:Open()
	end
end

-----------------------------------------竞技场1v1排行榜 start-----------------------------------------
function Field1v1WGCtrl:SendChallengeField(operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChallengeFieldOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function Field1v1WGCtrl:OnSCChallengeRankListInfo(protocol)
	self.data:SetChallengeRankListInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function Field1v1WGCtrl:OnSCChallengeFieldRewardInfo(protocol)
	self.data:SetChallengeFieldRewardInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end

	if self.reward_view:IsOpen() then
		self.reward_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.ActJjc)
	RemindManager.Instance:Fire(RemindName.ActJjcArena)
end

function Field1v1WGCtrl:OnSCChallengeFieldInfo(protocol)
	self.data:SetChallengeFieldInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end

	if self.reward_view:IsOpen() then
		self.reward_view:Flush()
	end
end

function Field1v1WGCtrl:GetField1v1RewardTabType()
	if self.reward_view:IsOpen() then
		return self.reward_view:GetField1v1RewardTabType()
	end

	return 1
end
-----------------------------------------竞技场1v1排行榜 end-----------------------------------------