﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ActorAttachmentWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(ActorAttachment), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("GetAttachPoint", GetAttachPoint);
		<PERSON><PERSON>unction("HasMount", HasMount);
		<PERSON><PERSON>Function("GetMount", GetMount);
		<PERSON><PERSON>Function("SetMountUpTriggerEnable", SetMountUpTriggerEnable);
		<PERSON>.RegFunction("AddMount", AddMount);
		<PERSON><PERSON>RegFunction("AddFightMount", AddFightMount);
		<PERSON><PERSON>unction("RemoveMount", RemoveMount);
		L.RegFunction("JumpUp", JumpUp);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("attachPoints", get_attachPoints, set_attachPoints);
		<PERSON><PERSON>("Prof", get_Prof, null);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAttachPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.Transform o = obj.GetAttachPoint(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HasMount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			bool o = obj.HasMount();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			UnityEngine.GameObject o = obj.GetMount();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMountUpTriggerEnable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetMountUpTriggerEnable(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddMount(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				obj.AddMount(arg0);
				return 0;
			}
			else if (count == 3)
			{
				ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				string arg1 = ToLua.CheckString(L, 3);
				obj.AddMount(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: ActorAttachment.AddMount");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddFightMount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.AddFightMount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveMount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			obj.RemoveMount();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpUp(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ActorAttachment obj = (ActorAttachment)ToLua.CheckObject(L, 1, typeof(ActorAttachment));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.JumpUp(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_attachPoints(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachment obj = (ActorAttachment)o;
			UnityEngine.Transform[] ret = obj.attachPoints;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index attachPoints on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Prof(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachment obj = (ActorAttachment)o;
			int ret = obj.Prof;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Prof on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_attachPoints(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ActorAttachment obj = (ActorAttachment)o;
			UnityEngine.Transform[] arg0 = ToLua.CheckObjectArray<UnityEngine.Transform>(L, 2);
			obj.attachPoints = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index attachPoints on a nil value");
		}
	}
}

