---------------
--活动基类
---------------
ActBaseViewTwo = ActBaseViewTwo or BaseClass(SafeBaseView)

function ActBaseViewTwo:__init(act_id)

end

function ActBaseViewTwo:__delete()

end

function ActBaseViewTwo:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
		end
		self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = Language.OpenServer.ActShuoMing .. (open_act_cfg.top_desc)
	end
end

function ActBaseViewTwo:UpdateCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	local tip_str = self.act_status.status == ACTIVITY_STATUS.CLOSE and Language.Activity.ActClosing or Language.Activity.ActPreparing
end

function ActBaseViewTwo:CompleteCountDownTime(is_auto_fuhuo)
	self:RefreshView()
end

function ActBaseViewTwo:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ActBaseViewTwo:CloseCallBack()
	self.open_tween = nil
end