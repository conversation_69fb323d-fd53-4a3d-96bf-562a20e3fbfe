
--每日累充--直购返利Data
EveryDayRechargeRebateWGData = EveryDayRechargeRebateWGData or BaseClass()

function EveryDayRechargeRebateWGData:__init()
    if nil ~= EveryDayRechargeRebateWGData.Instance then
        ErrorLog("[EveryDayRechargeRebateWGData]:Attempt to create singleton twice!")
    end
    EveryDayRechargeRebateWGData.Instance = self
    self.recharge_value = 0
    self.reward_flag = {}
    self.first_login_remind_flag = 1
    self.open_group = 0
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.DailyRecharge_ZhiGou, BindTool.Bind(self.IsShowRebateZhiGouRedPoint, self))
    self.global_day_change = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind1(self.RechargeActivityDayChange, self))
end

function EveryDayRechargeRebateWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.DailyRecharge_ZhiGou)
    if self.global_day_change then
        GlobalEventSystem:UnBind(self.global_day_change)
        self.global_day_change = nil
    end

    EveryDayRechargeRebateWGData.Instance = nil
end

function EveryDayRechargeRebateWGData:InitConfig()
    self.zhichong_cfg = ConfigManager.Instance:GetAutoConfig("zhichong_cfg_auto")
    self.reward_day_cfg = ListToMap(self.zhichong_cfg.reward_cfg, "group", "day_id")
    self.zhichong_open_cfg = self.zhichong_cfg.open_cfg
    self.zhichong_other_cfg = self.zhichong_cfg.other_cfg[1]
end

function EveryDayRechargeRebateWGData:CheckNeedCloseTab()
    return false
end

function EveryDayRechargeRebateWGData:GetActIsOpen()
    return true
end

function EveryDayRechargeRebateWGData:GetRebateListData()
    local cur_group = self:GetSCActZhiChongOpenGroup()
    return self.reward_day_cfg[cur_group]
end

-- 不要当前选中的数据 只显示第一个
function EveryDayRechargeRebateWGData:GetRebateListDataByDay(day)
    local cur_cfg = self:GetRebateListData()
    return cur_cfg[day]
end

function EveryDayRechargeRebateWGData:IsShowRebateZhiGouRedPoint()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.everyday_recharge_zhigou)
	if not is_open then
		return 0
	end

    local cur_cfg = self:GetRebateListData()
    if IsEmptyTable(cur_cfg) then 
        return 0
    end


    --每日首次登陆显示一次红点
    -- if RoleWGData.Instance:GetDayFirstLoginFlag() and self:GetTodayFirstLoginRed() then
    --     return 1
    -- end

    for k,v in pairs(cur_cfg) do
       if self:GetThisDayCanGet(v.day_id) == 1 then
           return 1
       end
    end
    return 0
end

function EveryDayRechargeRebateWGData:SetTodayFirstLoginRed(flag)
    self.first_login_remind_flag = flag
end

--返回值:true 显示红点
function EveryDayRechargeRebateWGData:GetTodayFirstLoginRed()
    return self.first_login_remind_flag == 1
end

function EveryDayRechargeRebateWGData:OnEDActZhiChongInfo(protocol)
    -- print_error("FFF==== protocol", protocol)
   self.open_group = protocol.open_group
   self.recharge_value = protocol.recharge_value
   self.reward_flag = bit:d2b(protocol.reward_flag)

   --刷新首充入口
   ServerActivityWGData.Instance:SetFirstRechargeMainUiIconVisible()
end

function EveryDayRechargeRebateWGData:GetSCActZhiChongOpenGroup()
    return self.open_group or 0
end

function EveryDayRechargeRebateWGData:GetNeedRechargeCount()
    return self.zhichong_other_cfg.recharge_value or 0
end

function EveryDayRechargeRebateWGData:GetIsEnoughRecharge()
    local need_count = self:GetNeedRechargeCount()
    if need_count > 0 then
        return self.recharge_value >= need_count
    end
    return false
end

-- -1充值不足  0天数不足  1可领取  2已领取
--传入天数,判断时根据reward_index来
function EveryDayRechargeRebateWGData:GetThisDayCanGet(day_id)
    if not self:GetIsEnoughRecharge() then
        return -1
    end
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local reward_index = self:GetRebateRewardIndexByDay(day_id)
    if cur_day >= day_id then
        reward_index = reward_index - 1--索引0开始
        if self.reward_flag[32-reward_index] and self.reward_flag[32-reward_index] == 0 then
            return 1
        else
            return 2
        end
    else
        return 0
    end
end

function EveryDayRechargeRebateWGData:GetRebateRewardIndexByDay(day)
    local reward_index = 1
    local cur_day_cfg = self:GetRebateListDataByDay(day)
    if not IsEmptyTable(cur_day_cfg) then
        reward_index = cur_day_cfg.reward_index or 1
    end
    return reward_index
end

function EveryDayRechargeRebateWGData:GetAlreadyRecharge()
    return self.recharge_value or 0
end

function EveryDayRechargeRebateWGData:GetRebateRechargeRule()
    return self.zhichong_other_cfg.message or "" 
end

function EveryDayRechargeRebateWGData:RechargeActivityDayChange()
    RemindManager.Instance:Fire(RemindName.DailyRecharge_ZhiGou)
end

-- 2022/3/1 增加页签屏蔽逻辑， 功能不开启，但是已购买玩家仍然可视
function EveryDayRechargeRebateWGData:GetTabIsShow()
    local is_open = FunOpen.Instance:GetFunIsOpened("everyday_recharge_zhigou")
    if not is_open then
        local need_money = self:GetNeedRechargeCount()
        local already_recharge = self:GetAlreadyRecharge()
        if already_recharge < need_money then
            return false
        end
    end

    local cur_cfg = self:GetRebateListData()
    if IsEmptyTable(cur_cfg) then
        return false
    end

    for k,v in pairs(cur_cfg) do
        if self:GetThisDayCanGet(v.day_id) ~= 2 then
            return true
        end
     end

    return false
end

function EveryDayRechargeRebateWGData:GetFirstSelectCell(cur_data)
    local first_idx = 1
    if not IsEmptyTable(cur_data) then
        for i, v in ipairs(cur_data) do
            if v.day_id then
                if self:GetThisDayCanGet(v.day_id) == 1 then
                    first_idx = i
                    break
                end
            end
            
        end
    end
    return first_idx
end

function EveryDayRechargeRebateWGData:GetRebateOpenCfg()
    local zhichong_open_cfg = self.zhichong_open_cfg
    local cur_open_group = self:GetSCActZhiChongOpenGroup()
    if not IsEmptyTable(zhichong_open_cfg) then
        for k, v in pairs(zhichong_open_cfg) do
            if v.group == cur_open_group then
                return v
            end
        end
    end
    return nil
end