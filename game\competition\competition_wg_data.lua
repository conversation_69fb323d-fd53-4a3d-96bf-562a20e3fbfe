CompetitionWGData = CompetitionWGData or BaseClass()

CompetitionWGData.BP_TYPE_T = {2080, 2079, 2077, 2076, 2078, 2075}

function CompetitionWGData:__init()
	if CompetitionWGData.Instance ~= nil then
		ErrorLog("[CompetitionWGData] Attemp to create a singleton twice !")
	end
	CompetitionWGData.Instance = self

	self.competition_info = {}

	self.flag_list = {}
end

function CompetitionWGData:__delete()
	CompetitionWGData.Instance = nil
end

function CompetitionWGData:GetBipinAllCfg(activity_type, flag)
	local bipin_all_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().bipin_all

	if nil == activity_type or nil == flag then return nil end
	local cfg = nil
	local max_seq_cfg = nil
	local can_get = true
	for k,v in pairs(bipin_all_cfg) do
		if activity_type == v.activity_type then 
			if (nil == max_seq_cfg or v.seq > max_seq_cfg.seq) then
				max_seq_cfg = v
			end

			if (0 == (bit:_and(flag, bit:_lshift(1, v.seq)))) and (nil == cfg or v.seq < cfg.seq) then
				cfg = v
			end
		end
	end
	if nil == cfg then
		cfg = max_seq_cfg
		can_get = false
	end

	return cfg, can_get
end

function CompetitionWGData:GetBipinRankCfg(activity_type)
	local bipin_rank_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().bipin_rank

	for k, v in pairs(bipin_rank_cfg) do
		if activity_type == v.activity_type then
			return v
		end
	end
	return nil
end

function CompetitionWGData:GetRABipinCapabilityInfo()
	return self.competition_info
end

function CompetitionWGData:SetRABipinCapabilityInfo(info)
	self.competition_info.bipin_activity_type = info.bipin_activity_type
	self.competition_info.capability = info.capability
	self.competition_info.fetch_reward_flag = info.fetch_reward_flag
	self.competition_info.all_camp_top_user_info_list = info.all_camp_top_user_info_list

	self.flag_list[info.bipin_activity_type] = {flag = info.fetch_reward_flag, capability = info.capability}
end

function CompetitionWGData:GetRemainingTiem(activity_type)
	local bipin_rank_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().bipin_rank

	local reward_day = 0
	for k, v in pairs(bipin_rank_cfg) do
		if activity_type == v.activity_type then
			reward_day = v.reward_day
			break
		end
	end
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	if nil == activity_status then
		return TimeUtil.Format2TableDHM(0)
	end
	local end_time = activity_status.start_time + reward_day * 60 * 60 * 24
	local format_time = os.date("*t", end_time)
	local end_zero_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=0, min = 0, sec=0}
	local time_left = end_zero_time - TimeWGCtrl.Instance:GetServerTime()
	if time_left < 0 then
		time_left = 0
	end
	return TimeUtil.Format2TableDHM(time_left)
end

function CompetitionWGData:GetAccountTimeNum()
	local bipin_rank_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().bipin_rank

	local num = 1
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.BP_CAPABILITY_TOTAL)
	if nil == activity_status then
		return num
	end
	local start_time = activity_status.start_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	for k, v in pairs(bipin_rank_cfg) do
		local end_time = start_time + v.reward_day * 60 * 60 * 24
		local format_time = os.date("*t", end_time)
		local end_zero_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=0, min = 0, sec=0}
		local time_left = end_zero_time - server_time
		if time_left < 0 then
			time_left = 0
		end
		if time_left == 0 then
			num = num + 1
		end
	end
	return num
end

function CompetitionWGData.IsBipinType(act_type)
	for k,v in pairs(CompetitionWGData.BP_TYPE_T) do
		if act_type == v then 
			return true
		end
	end
	return false
end

function CompetitionWGData:GetRemindNum()
	local info = self:GetRABipinCapabilityInfo()
	if info == nil then return 0 end
	for k,v in pairs(self.flag_list) do
		local all_cfg, can_get = CompetitionWGData.Instance:GetBipinAllCfg(k, v.flag)
		if not can_get then return 0 end
		local need_capability = all_cfg.need_capability
		if v.capability >= need_capability and can_get then
			return 1, k
		end
	end
	return 0
end