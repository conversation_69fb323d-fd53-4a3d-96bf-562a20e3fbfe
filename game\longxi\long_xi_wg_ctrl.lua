require("game/longxi/long_xi_view")
require("game/longxi/super_dragon_seal_view")
require("game/longxi/dragon_king_token_view")
require("game/longxi/long_xi_item_tips")
require("game/longxi/long_xi_wg_data")

LongXiWGCtrl = LongXiWGCtrl or BaseClass(BaseWGCtrl)

function LongXiWGCtrl:__init()
	if LongXiWGCtrl.Instance then
		ErrorLog("[LongXiWGCtrl] attempt to create singleton twice!")
		return
	end

	LongXiWGCtrl.Instance = self
	self.data = LongXiWGData.New()
	self.view = LongXiView.New(GuideModuleName.LongXiView)
    --self.super_dragon_seal_view = SuperDragonSealView.New(GuideModuleName.SuperDragonSealView)
	--self.dragon_king_token_view = DragonKingTokenView.New(GuideModuleName.DragonKingTokenView)
	self.long_xi_item_tips = LongXiItemTips.New()

	self:RegisterAllProtocals()
	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function LongXiWGCtrl:__delete()
	LongXiWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.long_xi_item_tips then
		self.long_xi_item_tips:DeleteMe()
		self.long_xi_item_tips = nil
	end

	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function LongXiWGCtrl:OpenLongXiItemTips(longxi_activiti_type)
	self.long_xi_item_tips:SetDataAndOpen(longxi_activiti_type)
end

function LongXiWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSLongXiOperate)
	self:RegisterProtocol(SCLongXiItemInfo, "OnSCLongXiItemInfo")
	self:RegisterProtocol(SCLongXiItemUpdate, "OnSCLongXiItemUpdate")
	self:RegisterProtocol(SCLongXiOtherInfo, "OnSCLongXiOtherInfo")
end

-- 通用请求请求操作
function LongXiWGCtrl:SendLongXiRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLongXiOperate)
	protocol.operate_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function LongXiWGCtrl:OnSCLongXiOtherInfo(protocol)
	--print_error("任务信息改变", protocol)
	LongXiWGData.Instance:SetBossKillCount(protocol)

	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.dragon_king_token) then
        ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.dragon_king_token)
    end

    RemindManager.Instance:Fire(RemindName.DragonKingToken)
end

function LongXiWGCtrl:OnSCLongXiItemInfo(protocol)
	self.data:SetLongXiItemInfo(protocol)
	RemindManager.Instance:Fire(RemindName.SuperDragonSeal)
	RemindManager.Instance:Fire(RemindName.DragonKingToken)
	RoleBagWGCtrl.Instance:FlushLongXiState()

	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.super_dragon_seal) then
		ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.super_dragon_seal)
	end

	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.dragon_king_token) then
        ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.dragon_king_token)
    end

	local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
	local is_show_main = LongXiWGData.Instance:GetIsShowMainViewTips()
	if is_show_main and data.is_active < 1 then
		MainuiWGCtrl.Instance:FlushView(0, "king_token_tip")
	end
end

function LongXiWGCtrl:OnSCLongXiItemUpdate(protocol)
	local seq = protocol.seq
	local old_data = self.data:GetLongXiDataByType(seq)
	local level_change = false
	local active_change = false

	if not IsEmptyTable(old_data) then
		level_change = protocol.item.level > 0 and (protocol.item.level - old_data.level == 1)
		active_change = old_data.is_active ~= protocol.item.is_active
	end

	self.data:UpdateLongXiItem(protocol)

	if active_change then
		RoleBagWGCtrl.Instance:FlushLongXiState()
	end

	if seq == LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL then
		RemindManager.Instance:Fire(RemindName.SuperDragonSeal)
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.super_dragon_seal) then
			ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.super_dragon_seal)

			if level_change then
				self.view:StartSuperDragonUpLevelAni()
			end
		end
	else
		RemindManager.Instance:Fire(RemindName.DragonKingToken)
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.dragon_king_token) then
			ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.dragon_king_token)

			if level_change then
				self.view:DkStartUpLevelAni()
			end
		end
	end

	RemindManager.Instance:Fire(RemindName.PrivilegedGuidance)
	ViewManager.Instance:FlushView(GuideModuleName.PrivilegeShop)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.QIFU,TabIndex.qifu_qf) then
		ViewManager.Instance:FlushView(GuideModuleName.QIFU, TabIndex.qifu_qf)
	end
end

function LongXiWGCtrl:OnRoleAttrChange(attr_name)
	if attr_name == "level" then
		local data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
		if data.is_active > 0 then
			return
		end

		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.dragon_king_token) then
			ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.dragon_king_token)
        end

        RemindManager.Instance:Fire(RemindName.DragonKingToken)
	end
end

function LongXiWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num < old_num) then
       return
    end

	if self.data:CheckIsDragonKingTokenItem(change_item_id) or change_item_id == self.data.task_cfg.cost_item_id then
		RemindManager.Instance:Fire(RemindName.DragonKingToken)
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.dragon_king_token) then
			ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.dragon_king_token)
		end
	end

	if self.data:CheckIsSuperDragonSealItem(change_item_id) then
		RemindManager.Instance:Fire(RemindName.SuperDragonSeal)
		if ViewManager.Instance:IsOpenByIndex(GuideModuleName.LongXiView,TabIndex.super_dragon_seal) then
			ViewManager.Instance:FlushView(GuideModuleName.LongXiView,TabIndex.super_dragon_seal)
		end
	end
end

function LongXiWGCtrl:SetLongXIOpraterFlag(longxi_type)
	if longxi_type == LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN then
		if self.data:GetOpraterFlag(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN) then
			self.data:SetOpraterFlag(LONGXI_ACTIVITY_TYPE.DRAGONKINGTOKEN)
			RemindManager.Instance:Fire(RemindName.DragonKingToken)
			MainuiWGCtrl.Instance:FlushView(0, "longxi_notice")
		end
	elseif longxi_type == LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL then
		if self.data:GetOpraterFlag(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL) then
			self.data:SetOpraterFlag(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
			RemindManager.Instance:Fire(RemindName.SuperDragonSeal)
			MainuiWGCtrl.Instance:FlushView(0, "longxi_notice")
		end
	end
end