﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class DG_Tweening_PathTypeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(DG.Tweening.PathType));
		<PERSON><PERSON>("Linear", get_Linear, null);
		<PERSON><PERSON>("CatmullRom", get_CatmullRom, null);
		<PERSON><PERSON>("CubicBezier", get_CubicBezier, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<DG.Tweening.PathType>.Check = CheckType;
		StackTraits<DG.Tweening.PathType>.Push = Push;
	}

	static void Push(IntPtr L, DG.Tweening.PathType arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(DG.Tweening.PathType), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Linear(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathType.Linear);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CatmullRom(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathType.CatmullRom);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CubicBezier(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathType.CubicBezier);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		DG.Tweening.PathType o = (DG.Tweening.PathType)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

