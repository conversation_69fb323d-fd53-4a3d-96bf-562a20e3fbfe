-- 新形象格子
NewAppearanceRender = NewAppearanceRender or BaseClass(BaseRender)
function NewAppearanceRender:LoadCallBack()
	self.star_list = {}
	for i = 1, 5 do
		self.star_list[i] = self.node_list["star" .. i]
	end

    self.show_cell = ItemCell.New(self.node_list["item_pos"])
end

function NewAppearanceRender:__delete()
	self.star_list = nil

    if self.show_cell then
        self.show_cell:DeleteMe()
        self.show_cell = nil
    end
end

function NewAppearanceRender:OnSelectChange(is_select)
	self.node_list["bg"]:CustomSetActive(not is_select)
	self.node_list["hl_bg"]:CustomSetActive(is_select)
end





-- 时装格子
NewAppearanceFashionRender = NewAppearanceFashionRender or BaseClass(NewAppearanceRender)
function NewAppearanceFashionRender:OnFlush()
	if nil == self.data then
		return
	end

    local index = self.data.index
    local part_type = self.data.part_type
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index)
    self.show_cell:SetData({item_id = self.data.active_stuff_id})

    local is_remind = self.data.is_remind
    self.node_list["remind"]:CustomSetActive(is_remind)
    local is_act = NewAppearanceWGData.Instance:GetFashionIsAct(part_type, index)
    local level = NewAppearanceWGData.Instance:GetFashionLevel(part_type, index)

    self.node_list["no_act_flag"]:CustomSetActive(not is_act)
    self.node_list["stars_list"]:CustomSetActive(is_act)-- and level > 1)
    if is_act then
        -- local star_res_list = GetStarImgResByStar(level - 1)
        -- for k,v in pairs(self.star_list) do
        --     v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        -- end

        local star_res_list = GetTwenTyStarImgResByStar(level - 1)
        for k,v in pairs(self.star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end
    else
        local show_flag_bg = true
        local act_desc
        if is_remind then
            act_desc = ToColorStr(Language.NewAppearance.ActDesc[3], COLOR3B.GREEN)
            show_flag_bg = false
        else
            local role_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
            if fashion_cfg and role_zhuan < fashion_cfg.zhuanzhi_level then
                act_desc = string.format(Language.NewAppearance.ActDesc[2], CommonDataManager.GetDaXie(fashion_cfg.zhuanzhi_level))
            else
                act_desc = Language.NewAppearance.ActDesc[1]
            end
        end

        self.node_list["no_act_flag_text"].text.text = act_desc
        self.node_list["no_act_flag"].image.enabled = show_flag_bg
        local asset_name = is_remind and "a1_biaoqian_ls" or "a1_biaoqian_hs"
        --self.node_list["no_act_flag"].image:LoadSprite(ResPath.GetCommonImages(asset_name))
    end

    local part_use_idnex = NewAppearanceWGData.Instance:GetFashionUseIndex(part_type)
    local is_use = part_use_idnex == index
    self.node_list["used_flag"]:CustomSetActive(is_use)

    self.node_list["name"].text.text = fashion_cfg.name
end





-- 骑宠格子
NewAppearanceQiChongRender = NewAppearanceQiChongRender or BaseClass(NewAppearanceRender)
function NewAppearanceQiChongRender:OnFlush()
    if self.data == nil then
        return
    end

    local qc_type = self.data.qc_type
    local image_id = self.data.image_id
    local func_type = self.data.func_type
    self.show_cell:SetData({item_id = self.data.active_item_id})
    local is_remind = self.data.is_remind
    local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(qc_type, image_id)
    if is_act then
        local star_level = NewAppearanceWGData.Instance:GetSpecialQiChongStarLevel(qc_type, image_id)
        self.node_list["stars_list"]:CustomSetActive(true)--star_level > 0)
        
        local star_res_list = GetTwenTyStarImgResByStar(star_level - 1)
        for k,v in pairs(self.star_list) do
            v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
        end

        self.node_list["no_act_flag"]:CustomSetActive(false)
    else
        local show_flag_bg = true
        local asset_name
        if is_remind and func_type == QICHONG_FUNC_TYPE.UPSTAR then
            asset_name = "a1_biaoqian_ls"
            self.node_list["no_act_flag_text"].text.text = ToColorStr(Language.NewAppearance.ActDesc[3], COLOR3B.GREEN)
            show_flag_bg = false
        else
            asset_name = "a1_biaoqian_hs"
            self.node_list["no_act_flag_text"].text.text = Language.NewAppearance.ActDesc[1]
        end

        self.node_list["stars_list"]:CustomSetActive(false)
        self.node_list["no_act_flag"]:CustomSetActive(true)
        self.node_list["no_act_flag"].image.enabled = show_flag_bg

        --self.node_list["no_act_flag"].image:LoadSprite(ResPath.GetCommonImages(asset_name))
    end

    local is_use = false
    local appe_id = 0
    local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(qc_type, image_id)
    if act_cfg then
        appe_id = act_cfg.appe_image_id or act_cfg.active_id

        local item_name = ItemWGData.Instance:GetItemName(act_cfg.active_item_id or act_cfg.active_need_item_id)
        self.node_list["name"].text.text = item_name
    end

    if qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        local all_info = NewAppearanceWGData.Instance:GetKunInfo(image_id)
        is_use = all_info and all_info.is_used
    else
        local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(qc_type)
        is_use = all_info and all_info.used_imageid == appe_id
    end

    self.node_list["used_flag"]:CustomSetActive(is_use)
    self.node_list["remind"]:CustomSetActive(is_remind)
end

-- 骑宠属性
QiChongAddAttrRender = QiChongAddAttrRender or BaseClass(CommonAddAttrRender)
function QiChongAddAttrRender:OnFlush()
    if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    local is_per = self.data.is_per or EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and self.data.cur_value / 100 or self.data.cur_value
	local attr_name = self.data.attr_name or EquipmentWGData.Instance:GetAttrName(self.data.attr_str, self.name_need_space, self.need_mao_hao)
    self.node_list.attr_name.text.text = attr_name
    self.node_list.attr_value.text.text = value_str .. per_desc

    -- self.node_list.attr_name.text.text = self.data.attr_name
    -- self.node_list.attr_value.text.text = self.data.cur_value
    self.node_list.arrow:SetActive(self.data.hide_arrow ~= true)
    self.node_list.add_value:SetActive(true)
    self.node_list.arrow.image.enabled = true
    if self.data.add_value then
        value_str = is_per and self.data.add_value / 100 or self.data.add_value
        self.node_list.add_value.text.text = value_str .. per_desc
    else
        if self.real_hide_next then
            self.node_list.arrow:SetActive(false)
            self.node_list.add_value:SetActive(false)
        else
            self.node_list.arrow.image.enabled = false
            self.node_list.add_value.text.text = ""
        end
    end

    -- 处理红点显示
	if self.node_list.remind then
		local show_remind = self.data.is_remind == true
		self.node_list.remind:SetActive(show_remind)
	end

	-- 处理锁显示
	if self.node_list.attr_lock then
		local show_lock = self.data.is_lock == true
		self.node_list.attr_lock:SetActive(show_lock)
	end

    -- 处理特效显示
    if self.node_list.effect then
        local is_show_effect = self.data.show_effect == true
        self.node_list.effect:SetActive(is_show_effect)
    end

    -- 处理点击响应节点显示
	local is_show_click_block = self.data.click_func ~= nil
	if self.node_list.click_block then
		self.node_list.click_block:SetActive(is_show_click_block)
	end

    self.view:SetActive(true)
end

-- 骑宠技能
QiChongSkillRender = QiChongSkillRender or BaseClass(BaseRender)
function QiChongSkillRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSkill, self))
end

function QiChongSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end
    
    local cfg = self.data.cfg
    self.view:SetActive(true)
    local bundel, asset = ResPath.GetSkillIconById(cfg.skill_icon)
    self.node_list["icon"].image:LoadSpriteAsync(bundel, asset, function ()
		self.node_list["icon"].image:SetNativeSize()
	end)
    
    self.node_list["skill_level"].text.text = self.data.skill_level
    self.node_list["level_part"]:CustomSetActive(self.data.is_act and self.data.skill_level > 0)
    self.node_list["remind"]:CustomSetActive(self.data.is_remind)
    self.node_list["skill_lock"]:CustomSetActive(not self.data.is_act)
end

function QiChongSkillRender:OnClickSkill()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local limit_text = ""
	if not self.data.is_act then
        if self.data.qc_type then
            local upstar_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(self.data.qc_type, cfg.active_star_level)
            limit_text = string.format(Language.NewAppearance.SkillGradeActTips2, upstar_cfg.grade_num, upstar_cfg.star_num)
        elseif self.data.ad_type then
            limit_text = string.format(Language.NewAppearance.SkillGradeActTips3, cfg.active_level)
        end
	end

    local view = ViewManager.Instance:GetView(GuideModuleName.NewAppearanceWGView)
    local parent_rect = view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    local capability = NewAppearanceWGData.Instance:GetSingleSkillCap(cfg)
    local type = self.data.ad_type or self.data.qc_type
    local show_data = {
        skill_box_type = self.data.qc_type ~= nil and SKILL_BOX_TYPE.QI_CHONG_SKILL or SKILL_BOX_TYPE.NORMAL,
        type = type,
        skill_id = self.data.skill_id,
		icon = cfg.skill_icon,
		top_text = cfg.skill_name,
		body_text = cfg.skill_describe,
		limit_text = limit_text,
        set_pos2 = true,
        is_up_operate = true,
        -- hide_level = self.data.qc_type ~= nil,
        is_lock = not self.data.is_act,
		x = -236,
		y = 0,
		capability = capability,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function QiChongSkillRender:SetPosflag(flag)
    self.flag = flag
end



-- 珍稀骑宠技能
SpecialQiChongSkillRender = SpecialQiChongSkillRender or BaseClass(BaseRender)
function SpecialQiChongSkillRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSkill, self))
end

function SpecialQiChongSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end
    
    local cfg = self.data.cfg
    local image_id = cfg.image_id or cfg.kun_id
    self.view:SetActive(true)
    local bundel, asset = ResPath.GetSkillIconById(cfg.skill_icon)
    self.node_list["icon"].image:LoadSpriteAsync(bundel, asset, function ()
		self.node_list["icon"].image:SetNativeSize()
	end)

    local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongSkillIsAct(self.data.qc_type, image_id, cfg.skill_id)
    self.node_list["skill_lock"]:CustomSetActive(not is_act)
end

function SpecialQiChongSkillRender:OnClickSkill()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    local image_id = cfg.image_id or cfg.kun_id
    local is_act = NewAppearanceWGData.Instance:GetSpecialQiChongSkillIsAct(self.data.qc_type, image_id, cfg.skill_id)
    local limit_text = ""
	if not is_act then
		limit_text = string.format(Language.NewAppearance.SkillGradeActTips, cfg.active_grade)
	end

    local capability = 0
    if self.data.qc_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        capability = NewAppearanceWGData.Instance:GetKunSkillAttrCap(cfg)
    else
        capability = NewAppearanceWGData.Instance:GetSingleSkillCap(cfg)
    end

    local view = ViewManager.Instance:GetView(GuideModuleName.NewAppearanceWGView)
    local parent_rect = view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    local show_data = {
		icon = cfg.skill_icon,
		top_text = cfg.skill_name,
		body_text = cfg.skill_describe,
		limit_text = limit_text,
        set_pos2 = true,
        hide_next = true,
        hide_level = true,
        is_lock = not is_act,
		x = -236,
		y = 0,
		capability = capability,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end





NewAppearanceSXDRender = NewAppearanceSXDRender or BaseClass(CommonAddAttrRender)
function NewAppearanceSXDRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSXD, self))
end

function NewAppearanceSXDRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    local item_id = self.data.cfg.shuxingdan_id
    self.node_list["used_num"].text.text = self.data.used_num
    if self.data.is_remind then
        self.node_list["can_add_num"].text.text = "+" .. self.data.had_num
    else
        self.node_list["can_add_num"].text.text = ""
    end
    self.node_list["arrow"]:SetActive(self.data.is_remind)
    self.node_list["effect"]:SetActive(self.data.is_remind)

    self.view:SetActive(true)
    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(item_id)
    self.node_list["icon"].image:LoadSprite(bundle, asset)
end

function NewAppearanceSXDRender:OnClickSXD()
    if IsEmptyTable(self.data) then
        return
    end

    local cfg = self.data.cfg
    if self.data.is_remind then
        if self.data.ad_type then
            NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_SHUXINGDAN, self.data.ad_type, cfg.slot_idx, self.data.had_num)
        elseif self.data.qc_type then
            if self.data.qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
                NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_SHUXINGDAN, cfg.slot_idx, self.data.had_num)
            elseif self.data.qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
                NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_SHUXINGDAN, cfg.slot_idx, self.data.had_num)
            end
        end

        -- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_shuxingdan)
		-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["icon"].transform)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.shuxingdan_id})
    end
end
