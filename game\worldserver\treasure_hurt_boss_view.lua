TreasureBossHurtView = TreasureBossHurtView or BaseClass(SafeBaseView)

function TreasureBossHurtView:__init()
	self.default_index = 1
	self.active_close = false
	self.view_layer = UiLayer.MainUILow
	self.view_name = "TreasureBossHurtView"
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_treasure_hurt_boss_info")
	self.view_cache_time = 0
	self.open_tween = nil
	self.close_tween = nil
    self.next_call_time = 0
    self.is_safe_area_adapter = true
end

function TreasureBossHurtView:__delete()
end

function TreasureBossHurtView:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
    self.next_call_time = 0

    if self.next_call_timer then
		GlobalTimerQuest:CancelQuest(self.next_call_timer)
		self.next_call_timer = nil
	end
end

function TreasureBossHurtView:ShowIndexCallBack()
    local init_callback = function ()
        self:Init()
    end
    local mainuictrl = MainuiWGCtrl.Instance
    mainuictrl:AddInitCallBack(nil,init_callback)
    self:Flush()
end

function TreasureBossHurtView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_qiangduo, BindTool.Bind1(self.OnClickQiangduo, self))
    -- XUI.AddClickEventListener(self.node_list.boss_count_add, BindTool.Bind1(self.OnClickAddShenyuan, self))
    self:InitList()
    --self:OnClickQiangduo()
end

-- function TreasureBossHurtView:OnClickAddShenyuan()
--     BossWGCtrl.Instance:OpenBossVipTimesView(VipPowerId.vat_shenyuanboss_buy_times)
-- end

function TreasureBossHurtView:InitList()
    self.rank_list = StrengthenAsyncListView.New(TreasureHurtRender, self.node_list.TaskList1)
end

function TreasureBossHurtView:Init()
	local mainui_ctrl = MainuiWGCtrl.Instance

	local parent = mainui_ctrl:GetTaskOtherContent()
	self.node_list.task_root_view.transform:SetParent(parent.transform)
    self.node_list.task_root_view.transform.anchoredPosition = Vector3(0, 0, 0)
	self.node_list.task_root_view.transform.localScale = Vector3(1, 1, 1)

	mainui_ctrl:SetTaskPanel(false,151,-136.1, true)
	-- mainui_ctrl.view:SetTaskCallBack(function (ison)
	-- 	if self.node_list.task_root_view then
	-- 		self.node_list.task_root_view:SetActive(ison)
	-- 	end

	-- end)
	mainui_ctrl:SetOtherContents(true)
end

function TreasureBossHurtView:CloseCallBack()
    if self.node_list.task_root_view then
        self.node_list.task_root_view.transform:SetParent(self.root_node_transform, false)
    end
end

function TreasureBossHurtView:OnClickQiangduo()
    local scene_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
    local role_x,role_y = role:GetLogicPos()
    local boss_data = TreasureBossWGData.Instance:GetHuntBossInfoByScene(scene_id)
    if IsEmptyTable(boss_data) then
        print_error("xunbao boss_data == nil scene_id =",scene_id)
        return
    end
    local pos = Split(boss_data.boss_pos, ",") 
    if role_x == tonumber(pos[1]) and role_y == tonumber(pos[2]) then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    else
        GuajiWGCtrl.Instance:StopGuaji()
        GuajiWGCtrl.Instance:ClearTemporary()
        MoveCache.SetEndType(MoveEndType.FightByMonsterId)
        GuajiCache.monster_id = boss_data.boss_id
        MoveCache.param1 = boss_data.boss_id
        GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
            -- 切换挂机模式，以优先选择玩家 --
            GuajiWGCtrl.Instance:StopGuaji()
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end)
        local range = BossWGData.Instance:GetMonsterRangeByid(boss_data.boss_id)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, tonumber(pos[1]), tonumber(pos[2]), range)
    end
end

function TreasureBossHurtView:OnFlush()
    self.node_list.des_text_info.text.text = Language.Boss.TreasureHurtBossLeftDes
    local scene_id = Scene.Instance:GetSceneId()
    local boss_data = TreasureBossWGData.Instance:GetHuntBossInfoByScene(scene_id)
    local boss_info = TreasureBossWGData.Instance:GetTreasureBossInfoByBossID(boss_data.boss_id)
    local is_dead = boss_info.status == 0

    local join_time = TreasureBossWGData.Instance:GetHaveJoinTimes()
    local max_join_time = TreasureBossWGData.Instance:GetMaxJoinTimes()
    local kill_time = TreasureBossWGData.Instance:GetHaveKillTimes()
    local max_kill_time = TreasureBossWGData.Instance:GetMaxKillTimes()

    local left_time1 = max_join_time >= join_time and (max_join_time - join_time) or 0
    local left_time2 = max_kill_time >= kill_time and (max_kill_time - kill_time) or 0

    local color1 = join_time >= max_join_time and COLOR3B.D_RED or COLOR3B.D_GREEN
    local color2 = kill_time >= max_kill_time and COLOR3B.D_RED or COLOR3B.D_GREEN

    local str1 = ToColorStr(string.format("%d/%d",left_time1, max_join_time), color1)
    local str2 = ToColorStr(string.format("%d/%d",left_time2, max_kill_time), color2)

    self.node_list.boss_count_text.text.text = str1
    self.node_list.boss_belong_count_text.text.text = str2

    local all_info = BossAssistWGData.Instance:GetHurtInfo()
	local all_hurt_info = all_info.hurt_info or {}
    local main_role_hurt = all_info.main_role_hurt
    if is_dead then
        all_hurt_info = {}
        main_role_hurt = 0
    end

    self.node_list.tips:SetActive(IsEmptyTable(all_hurt_info))

    if self.rank_list ~= nil then
        self.rank_list:SetDataList(all_hurt_info, 3)
    else
        self:InitList()
        self.rank_list:SetDataList(all_hurt_info, 3)
    end

	self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(main_role_hurt)

    self.node_list.per_bg.slider.value = main_role_hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()
    local index = all_info.main_role_rank
    if main_role_hurt == 0 then
        -- self.node_list["my_rank_icon"]:SetActive(false)
        self.node_list.my_rank_txt.text.text = ""
    else
        -- if index < 4 then
        --     self.node_list["my_rank_icon"]:SetActive(true)
        --     self.node_list["my_rank_icon"].image:LoadSprite(ResPath.GetCommonImages("icon_paiming" .. index))
        --     self.node_list.my_rank_txt.text.text = ""
        -- else
        --     self.node_list["my_rank_icon"]:SetActive(false)
            self.node_list.my_rank_txt.text.text = index
        -- end
    end
end
--------------------------------------------------------------------------------
TreasureHurtRender = TreasureHurtRender or BaseClass(BaseRender)

function TreasureHurtRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.attk, BindTool.Bind(self.OnClickAttk, self))
    XUI.AddClickEventListener(self.node_list.btn_bg, BindTool.Bind(self.OnClickAttackBG, self))
end

function TreasureHurtRender:OnClickAttk()
    if nil == self.data then return end
    local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
    if obj then
        local is_enemy, str = Scene.Instance:IsEnemy(obj)
        if is_enemy then
            local guaji_state = GuajiCache.guaji_type == GuajiType.Auto
            GuajiWGCtrl.Instance:CancelSelect()
            GuajiWGCtrl.Instance:StopGuaji()
            GuajiWGCtrl.Instance:ClearTemporary()
            MoveCache.SetEndType(MoveEndType.AttackTarget)
            GuajiCache.target_obj = obj
            GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
            if guaji_state then
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            end
        else
            if str then
                SysMsgWGCtrl.Instance:ErrorRemind(str)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.ModeCantAttack2)
            end
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.NotInHorizon)
    end
end

function TreasureHurtRender:OnClickAttackBG()
    local main_role_id = RoleWGData.Instance:InCrossGetOriginUid()
    if nil == self.data then return end
    local role_id = self.data.uuid.temp_low
    if role_id == main_role_id then
        return
    end
    local obj = Scene.Instance:GetRoleByUUID(self.data.uuid)
    local is_enemy = false
    if obj then
        is_enemy = Scene.Instance:IsEnemy(obj)
    end

    if is_enemy then
        self:OnClickAttk()
        return
    end

    local my_server_id = RoleWGData.Instance:GetOriginServerId()
    local my_plat_type = RoleWGData.Instance:GetPlatType()
    local is_cross = my_server_id ~= self.data.server_id or my_plat_type ~= self.data.plat_type

    local member_count = SocietyWGData.Instance:GetTeamMemberCount()
    local str_tip, oprate
    local items = {
        Language.Menu.ShowInfo,
        Language.Menu.GiveFlower,
        --Language.Menu.Profess, 
    }
    if not is_cross then
        if SocietyWGData.Instance:GetIsMyFriend(role_id) then
            table.insert(items, Language.Menu.PrivateChat)
        else
            table.insert(items, Language.Menu.AddFriend)
        end
    end
    if not is_cross then
        if self.data.team_index <= 0 and member_count >= 0 then
            table.insert(items, Language.Menu.InviteTeam)
        elseif self.data.team_index > 0 and member_count <= 0 then
            table.insert(items, Language.Menu.ApplyTeam)
        elseif self.data.team_index > 0 and member_count > 0 then
            table.insert(items, Language.Menu.MergeTeam)
        end
    end

    if items == nil then
        return
    end
    local team_index = self.data.team_index
    BrowseWGCtrl.Instance:ShowOtherRoleInfo(role_id, self:GetPos(self.node_list["btn_bg"]), is_cross, self.data.plat_type, items, 
    BindTool.Bind(self.MenuClickCallBack, self, is_cross, team_index, items))
end

function TreasureHurtRender:GetPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.MainUIView)
    if nil == main_view or not main_view:IsOpen() then
        return
    end
    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
    local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    x = local_position_tbl.x + 320
    y = local_position_tbl.y
    return Vector2(x, y)
end

function TreasureHurtRender:MenuClickCallBack(is_cross, team_index, items, index, sender, param, item_data)
    local menu_text = items[index]
    if menu_text == Language.Menu.ShowInfo then
        local role_id = param.role_id
        BrowseWGCtrl.Instance:OpenWithUid(role_id,nil,nil,param.plat_type,1)
    elseif menu_text == Language.Menu.GiveFlower then
        if not is_cross then
            FlowerWGCtrl.Instance:OpenSendFlowerView(param.role_id, param.role_name, param.sex, param.prof)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
        end
    -- elseif menu_text == Language.Menu.Profess then
    --     if not is_cross then
    --         local is_friend = SocietyWGData.Instance:CheckIsFriend(param.role_id)
    --         if is_friend then
    --             ProfessWallWGData.Instance:SetDefaultInfo(param.role_id,nil)
    --             --ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
    --         else
    --             local data = {}
    --             data.role_id = param.role_id
    --             data.role_name = param.role_name
    --             data.sex = param.sex
    --             data.prof = param.prof
    --             SocietyWGCtrl.Instance:OpenAddTipsPanel(data)
    --         end
    --     else
    --         TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
    --     end
    elseif menu_text == Language.Menu.PrivateChat or menu_text == Language.Menu.AddFriend then
        if not is_cross then
            local role_id = param.role_id
            if SocietyWGData.Instance:GetIsMyFriend(role_id) then
                SocietyWGCtrl.Instance:Flush("find_role_id",{role_id})
            else
                SocietyWGCtrl.Instance:IAddFriend(param.role_id)
            end
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.Rank.NoOperate)
        end
    elseif menu_text == Language.Menu.InviteTeam or menu_text == Language.Menu.ApplyTeam or menu_text == Language.Menu.MergeTeam then
        if item_data ~= nil and item_data[1] ~= nil and team_index ~= nil then
            local role_id = param.role_id
            if menu_text == Language.Menu.InviteTeam then
                if 0 == SocietyWGData.Instance:GetIsInTeam() then
                    local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
                    NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
                end
                NewTeamWGCtrl.Instance:SendInviteUser(role_id, 0, 1)
            elseif menu_text == Language.Menu.ApplyTeam and team_index > 0 then
                SocietyWGCtrl.Instance:SendReqJoinTeam(team_index)
            else
                NewTeamWGCtrl.Instance:SendTeamMergeReq(role_id)
            end  
        end     
    end
end


function TreasureHurtRender:OnClickFlag()
    if self.index <= 3 then
        -- BossWGCtrl.Instance:ShowShenYuanHurtFlag(self.index)
        BossWGCtrl.Instance:SetShenYuanFlagIndex(self.index)
        BossWGCtrl.Instance:OpenShenYuanHurtFlag()
    end
end

function TreasureHurtRender:OnFlush()
    if not self.data then
        return
    end
    
    local name = self.data.name
    local have_cache, new_name = TreasureHuntWGData.Instance:GetCacheSubName(name)

    if have_cache then
        name = new_name
    else
        local s_index = string.find(name, "_")
        if s_index then
            name = string.sub(name, 1, s_index - 1)
        end

        TreasureHuntWGData.Instance:CacheSubName(self.data.name, name)
    end

    if self.data.is_in_team and self.data.is_in_team == 1 then
        name = name..Language.BossAssist.TeamFlag
    end
    
    self.node_list.name.text.text = name
	local damage = CommonDataManager.ConverNumber(self.data.hurt)
	self.node_list.damage.text.text = damage
    self.node_list.per_bg.slider.value = self.data.hurt / BossAssistWGData.Instance:GetNormalHurtInfoMaxValue()
    
    self.node_list.num:SetActive(true)
	-- if self.index < 4 then
	-- 	self.node_list["icon"]:SetActive(true)
	-- 	self.node_list["icon"].image:LoadSprite(ResPath.GetCommonImages("icon_paiming" .. self.index))
	-- 	self.node_list.num.text.text = ""
	-- else
	-- 	self.node_list["icon"]:SetActive(false)
    self.node_list.num.text.text = self.index
	-- end

    self:FlushBtnState()
end

function TreasureHurtRender:FlushBtnState()
    local is_show = true
    local cur_obj = Scene.Instance:GetObjByUId(self.data.uid)
    local main_role = Scene.Instance:GetMainRole()
    local attack_mode = main_role:GetVo().attack_mode
    if cur_obj then
        local lover_id = main_role:GetVo().lover_uid or 0
        local is_lover = lover_id ~= 0 and lover_id == cur_obj:GetVo().role_id
        if is_lover and (attack_mode == ATTACK_MODE.GUILD or attack_mode == ATTACK_MODE.PEACE) then
           is_show = false
        end
        if cur_obj:IsRole() then
            local is_team = SocietyWGData.Instance:IsTeamMember(cur_obj:GetOriginId())
            if is_team then
                is_show = false
            end
        end
    end
	self.node_list["attk"]:SetActive(is_show and not self.data.is_mine)
end

function TreasureHurtRender:OnSelectChange(is_select)
	self.node_list["high"]:SetActive(is_select)
end

