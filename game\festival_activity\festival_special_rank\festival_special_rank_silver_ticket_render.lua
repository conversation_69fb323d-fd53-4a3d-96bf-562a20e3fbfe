FestivalSPRankSilverTicketRender = FestivalSPRankSilverTicketRender or BaseClass(BaseRender)

function FestivalSPRankSilverTicketRender:LoadCallBack()
    self.item_list = AsyncListView.New(FestivalSPRankItem, self.node_list.item_list)
    self.is_load_complete = true

	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_list)
	if self.parent_scroll_rect then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end

function FestivalSPRankSilverTicketRender:__delete()
    if self.nested_scroll_rect then
		self.nested_scroll_rect:DeleteMe()
		self.nested_scroll_rect = nil
	end
    self.is_load_complete = nil
    self.parent_scroll_rect = nil

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function FestivalSPRankSilverTicketRender:OnFlush()
    if not self.data then
        return
    end

    local is_show_bg = self.index % 2 ~= 0
    self.node_list.bg:SetActive(is_show_bg)

    --奖励格子列表
    self.item_list:SetDataList(self.data.item_list)

    if self.data.rank <= 3 then
        local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
    else
        self.node_list["rank_text"].text.text = self.data.rank
    end
    self.node_list["rank_img"]:SetActive(self.data.rank <= 3)
    self.node_list["rank_text"]:SetActive(self.data.rank > 3)

    if self.data.is_empty_value then
        self.node_list.name.text.text = Language.MergeSPRank.XuWeiYiDai
        self.node_list.rank_value.text.text = string.format(Language.MergeSPRank.NeedGetSilverTicket, self.data.reach_value)
        return
    end

    if not FestivalSpecialRankWGData.Instance:GetIsEnd() then
        self.node_list.name.text.text = Language.MergeSPRank.BaoMi
    else
        self.node_list.name.text.text = self.data.name
    end
    self.node_list.rank_value.text.text = self.data.rank_value
end

function FestivalSPRankSilverTicketRender:SetParentScrollRect(scroll_rect)
	self.parent_scroll_rect = scroll_rect

	if self.is_load_complete then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end