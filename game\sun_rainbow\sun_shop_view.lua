
function SunRainbowView:LoadCallBack_Shop()
    if not self.point_grid_list then
        self.point_grid_list = AsyncBaseGrid.New()
        local bundle = "uis/view/sun_rainbow_ui_prefab"
        local asset = "point_render"
        self.point_grid_list:CreateCells({col = 4,
                            change_cells_num = 1,
                            list_view = self.node_list["point_list"],
                            assetBundle = bundle,
                            assetName = asset,
                            itemRender = PointItemCell,
                        })
        self.point_grid_list:SetStartZeroIndex(false)
    end
end

function SunRainbowView:ReleaseCallBack_Shop()
    if self.point_grid_list then
        self.point_grid_list:DeleteMe()
        self.point_grid_list = nil
    end
end

function SunRainbowView:OnFlush_Shop()
    local info = SunRainbowWgData.Instance:GetSunShopCfg()
    if not IsEmptyTable(info) then
        self.point_grid_list:SetDataList(info)
    end
end



--------------------PointItemCell--------------------
PointItemCell = PointItemCell or BaseClass(BaseRender)

function PointItemCell:LoadCallBack()
    self.node_list.btn_exchange.button:AddClickListener(BindTool.Bind(self.OnClickExchange, self))

    if not self.item_info then
        self.item_info = ItemCell.New(self.node_list.item)
    end
end

function PointItemCell:__delete()
    if self.item_info then
        self.item_info:DeleteMe()
        self.item_info = nil
    end
end

function PointItemCell:OnFlush()
    if IsEmptyTable(self.data) then
		return
    end
    self.btn_res = "a3_kfkh_cfcj_btn_4"
    self:FlushPoint()
    self:FlushRedemptionTime()
    local bundle, asset = ResPath.GetSunRainbowImg(self.btn_res)
    self.node_list.btn_exchange.image:LoadSprite(bundle, asset, function()
        self.node_list.btn_exchange.image:SetNativeSize()
    end)

    local reward_item = self.data.reward_item
    local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
    self.item_info:SetData({item_id = reward_item.item_id})
    self.node_list.name.text.text = item_cfg.name
end

function PointItemCell:FlushPoint()
    local total_point = SunRainbowWgData.Instance:GetCurPoint()
    local cur_point = self.data.points_num
    local color = total_point >= cur_point and COLOR3B.WHITE or COLOR3B.C10
    if total_point < cur_point then
        self.btn_res = "a3_kfkh_cfcj_btn_3"
    end
    
    local str = string.format(Language.SunRainbow.Point, total_point .. "/" .. cur_point)
    self.node_list["cost"].text.text = ToColorStr(str, color)
end

function PointItemCell:FlushRedemptionTime()
    local limit = self.data.num_limit
    local change_num = SunRainbowWgData.Instance:GetExchangeNum(self.data.reward_id)
    local exhange_num = limit - change_num
    self.node_list.word_sold_out:SetActive(exhange_num == 0)
    self.node_list.cost:SetActive(exhange_num ~= 0)
    self.node_list.btn_exchange.button.interactable = exhange_num ~= 0

    local exchange_color = exhange_num ~= 0 and COLOR3B.C2 or COLOR3B.C3
    self.node_list.exchange_num.text.text = string.format(Language.SunRainbow.ExchangeStr, exchange_color, exhange_num, limit)

    if exhange_num == 0 then
        self.btn_res = "a3_kfkh_cfcj_btn_3"
    end

end

function PointItemCell:OnClickExchange()
    local num = SunRainbowWgData.Instance:GetCurPoint()
    if num < self.data.points_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.SunRainbow.NoActive)
		return
    else
        TipWGCtrl.Instance:ShowGetItem(self.data.reward_item)
        SunRainbowWgCtrl.Instance:SendReq(5, self.data.reward_id)
	end
end