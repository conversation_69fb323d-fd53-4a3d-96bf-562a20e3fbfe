VipDailyGiftShowView = VipDailyGiftShowView or BaseClass(SafeBaseView)

function VipDailyGiftShowView:__init(view_name)
	self.view_name = "VipDailyGiftShowView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_vip_daily_gift_show")
	self:SetMaskBg(true, true)
end

function VipDailyGiftShowView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function VipDailyGiftShowView:ReleaseCallBack()
	if self.item_cell_list then
		self.item_cell_list:DeleteMe()
		self.item_cell_list = nil
	end
	self.call_back = nil
end

function VipDailyGiftShowView:InitParam()
	self.data_list = {}
	self.call_back = nil
	self.is_gray = nil
end

function VipDailyGiftShowView:InitPanel()
	self.item_cell_list = AsyncListView.New(ItemCell, self.node_list.item_root)
end

function VipDailyGiftShowView:InitListener()
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function VipDailyGiftShowView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self.data_list = v.data_list
			self.call_back = v.call_back
			self.is_gray = v.is_gray
		end
	end
	self:RefreshView()
end

function VipDailyGiftShowView:RefreshView()
	self.item_cell_list:SetDataList(self.data_list)

	local can_get = VipWGData.Instance:CanGetDailyGiftReward()
	XUI.SetGraphicGrey(self.node_list.get_btn, self.is_gray)
	-- self.node_list.get_btn:SetActive(self.is_gray or can_get)
	-- self.node_list.get_reward_img:SetActive(not self.is_gray and not can_get)
end

function VipDailyGiftShowView:OnClickGetBtn()
	if self.call_back then
		self.call_back()
	end
	self:Close()
end