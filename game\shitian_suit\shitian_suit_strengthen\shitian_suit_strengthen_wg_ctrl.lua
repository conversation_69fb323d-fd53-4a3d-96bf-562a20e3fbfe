require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_strengthen_wg_data")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_strengthen_view")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_strengthen")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_infuse_soul")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_gemstone")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_resonance_view")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_inlay_stone_view")
require("game/shitian_suit/shitian_suit_strengthen/shitian_suit_stone_compose_view")

ShiTianSuitStrengthenWGCtrl = ShiTianSuitStrengthenWGCtrl or BaseClass(BaseWGCtrl)

function ShiTianSuitStrengthenWGCtrl:__init()
	if ShiTianSuitStrengthenWGCtrl.Instance then
		error("[ShiTianSuitStrengthenWGCtrl]:Attempt to create singleton twice!")
	end

    ShiTianSuitStrengthenWGCtrl.Instance = self

    self.data = ShiTianSuitStrengthenWGData.New()
    self.view = ShiTianSuitStrengthenView.New(GuideModuleName.ShiTianSuitStrengthenView)
    self.resonance_view = ShiTianSuitResonanceView.New()
    self.stone_compose_view = ShiTianSuitStoneComposeView.New()
    self.inlay_stone_view = ShiTianInlayStoneView.New()

    self:RegisterAllProtocols()
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self.role_data_change = BindTool.Bind1(self.RoleCoinChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"coin"})
end

function ShiTianSuitStrengthenWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

    self.resonance_view:DeleteMe()
    self.resonance_view = nil

    self.stone_compose_view:DeleteMe()
    self.stone_compose_view = nil

    self.inlay_stone_view:DeleteMe()
    self.inlay_stone_view = nil

    if self.bs_cost_prop_alert then
		self.bs_cost_prop_alert:DeleteMe()
		self.bs_cost_prop_alert = nil
	end

	if self.bs_cost_gold_alert then
		self.bs_cost_gold_alert:DeleteMe()
		self.bs_cost_gold_alert = nil
	end

    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

    ShiTianSuitStrengthenWGCtrl.Instance = nil
end

function ShiTianSuitStrengthenWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if (ShiTianSuitStrengthenWGData.Instance:IsShiTianStone(change_item_id) or
			ShiTianSuitStrengthenWGData.Instance:IsShiTianInfuseSoulItem(change_item_id) or
			ShiTianSuitStrengthenWGData.Instance:IsShiTianStrengthenItem(change_item_id)) and old_num < new_num then
        ViewManager.Instance:FlushView(GuideModuleName.ShiTianSuitStrengthenView, nil, "item_add")
        RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
		ShiTianSuitWGCtrl.Instance:OnAllStrengthenDataChange()
    end
end

function ShiTianSuitStrengthenWGCtrl:RoleCoinChange(key, new_value, old_value)
	if new_value > old_value then
		ViewManager.Instance:FlushView(GuideModuleName.ShiTianSuitStrengthenView, nil, "coin_add")
		RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
		ShiTianSuitWGCtrl.Instance:OnAllStrengthenDataChange()
	end
end

function ShiTianSuitStrengthenWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSShiTianStrengthenReq)
    self:RegisterProtocol(CSShiTianOneKeyInlayReq)

    self:RegisterProtocol(SCShiTianStoneInfo, "OnSCShiTianStoneInfo")
    self:RegisterProtocol(SCShiTianEquipInfo, "OnSCShiTianEquipInfo")
    self:RegisterProtocol(SCShiTianEquipFuMoInfo, "OnSCShiTianEquipFuMoInfo")
    self:RegisterProtocol(SCShiTianAdvanceSuit, "OnSCShiTianAdvanceSuit")
    self:RegisterProtocol(SCShiTianActiveSuit, "OnSCShiTianActiveSuit")
end

function ShiTianSuitStrengthenWGCtrl:SendCSShiTianStrengthenReq(req_type, param_1, param_2, param_3, param_4)
    local protocol = ProtocolPool.Instance:GetProtocol(CSShiTianStrengthenReq)
	protocol.req_type = req_type or 0
	protocol.param1 = param_1 or 0
	protocol.param2 = param_2 or 0
	protocol.param3 = param_3 or 0
	protocol.param4 = param_4 or 0
	protocol:EncodeAndSend()
end

function ShiTianSuitStrengthenWGCtrl:OnSCShiTianStoneInfo(protocol)
    self.data:SetGemStoneInfo(protocol)
    self:FlushResonanceView("flush_info")
    ViewManager.Instance:FlushView(GuideModuleName.ShiTianSuitStrengthenView, nil, "update_stone")
    RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
	ShiTianSuitWGCtrl.Instance:OnAllStrengthenDataChange()
end

function ShiTianSuitStrengthenWGCtrl:OnSCShiTianEquipInfo(protocol)
    self.data:SetStengthenInfo(protocol)
    self:FlushResonanceView("flush_info")
    ViewManager.Instance:FlushView(GuideModuleName.ShiTianSuitStrengthenView, nil, "update_strengthen")
    RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
	ShiTianSuitWGCtrl.Instance:OnAllStrengthenDataChange()
end

function ShiTianSuitStrengthenWGCtrl:OnSCShiTianEquipFuMoInfo(protocol)
    self.data:SetInfuseSoulInfo(protocol)
    self:FlushResonanceView("flush_info")
    ViewManager.Instance:FlushView(GuideModuleName.ShiTianSuitStrengthenView, nil, "update_infuse_soul")
    RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
	ShiTianSuitWGCtrl.Instance:OnAllStrengthenDataChange()
end

function ShiTianSuitStrengthenWGCtrl:OnSCShiTianAdvanceSuit(protocol)
    self.data:SetAllInfo(protocol)
    RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
end

function ShiTianSuitStrengthenWGCtrl:OnSCShiTianActiveSuit(protocol)
    self.data:SetTotalLevelInfo(protocol)
    self:FlushResonanceView("flush_info")
    ViewManager.Instance:FlushView(GuideModuleName.ShiTianSuitStrengthenView, nil, "update_total_level")
    RemindManager.Instance:Fire(RemindName.ShiTianSuitAllStrengthen)
	ShiTianSuitWGCtrl.Instance:OnAllStrengthenDataChange()
end

function ShiTianSuitStrengthenWGCtrl:SendCSShiTianOneKeyInlayReq(suit_seq, part, count, slot_info)
    local protocol = ProtocolPool.Instance:GetProtocol(CSShiTianOneKeyInlayReq)
	protocol.suit_seq = suit_seq or 0
	protocol.part = part or 0
	protocol.count = count or 0
	protocol.slot_info = slot_info
	protocol:EncodeAndSend()
end

function ShiTianSuitStrengthenWGCtrl:OpenStrengthenView(select_type)
	ViewManager.Instance:Open(GuideModuleName.ShiTianSuitStrengthenView, nil, "all", {select_type = select_type})
end

function ShiTianSuitStrengthenWGCtrl:OpenResonanceView(show_data)
    if not self.resonance_view:IsOpen() then
        self.resonance_view:SetShowData(show_data)
        self.resonance_view:Open()
    end
end

function ShiTianSuitStrengthenWGCtrl:FlushResonanceView(flush_key)
    if self.resonance_view:IsOpen() then
        self.resonance_view:Flush(nil, flush_key)
    end
end

function ShiTianSuitStrengthenWGCtrl:OpenStoneComposeView(show_data)
    if not self.stone_compose_view:IsOpen() then
        self.stone_compose_view:SetShowData(show_data)
        self.stone_compose_view:Open()
    end
end

function ShiTianSuitStrengthenWGCtrl:OpenInlayStoneView(show_data)
    if not self.inlay_stone_view:IsOpen() then
        self.inlay_stone_view:SetShowData(show_data)
        self.inlay_stone_view:Open()
    end
end

function ShiTianSuitStrengthenWGCtrl:InitBaoShiUpGradeAlertTips(call_back)
	if not self.bs_cost_prop_alert then
		local alert = Alert.New(nil, nil, nil, nil, true)
		alert:SetShowCheckBox(true, "shitian_ston_upgrade")
		alert:SetCheckBoxDefaultSelect(false)
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
		self.bs_cost_prop_alert = alert
	end

	if not self.bs_cost_gold_alert then
		local alert = Alert.New()
		alert:SetOkFunc(call_back)
		alert:SetCancelString(Language.Equip.NoEnter)
		alert:SetOkString(Language.Equip.Enter)
		self.bs_cost_gold_alert = alert
	end
end

function ShiTianSuitStrengthenWGCtrl:SetBaoShiUpGradeAlertTips(is_gold, str)
	if self.bs_cost_prop_alert and not is_gold then
		self.bs_cost_prop_alert:SetLableString(str)
	end

	if self.bs_cost_gold_alert and is_gold then
		self.bs_cost_gold_alert:SetLableString(str)
	end
end

function ShiTianSuitStrengthenWGCtrl:OpenBaoShiUpGradeAlertTips(is_gold)
	if self.bs_cost_prop_alert and not is_gold then
		self.bs_cost_prop_alert:Open()
	end

	if self.bs_cost_gold_alert and is_gold then
		self.bs_cost_gold_alert:Open()
	end
end