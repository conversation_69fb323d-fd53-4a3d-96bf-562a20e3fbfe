require("game/boss_invasion/boss_invasion_wg_data")
require("game/boss_invasion/boss_invasion_scene_view")
require("game/boss_invasion/boss_invasion_boss_quality_view")
require("game/boss_invasion/boss_invasion_privilege_view")
require("game/boss_invasion/boss_invasion_guwu_view")
require("game/boss_invasion/boss_invasion_privilege_skill_show_view")
require("game/boss_invasion/boss_invasion_rank_reward_view")
require("game/boss_invasion/boss_invasion_boss_quality_add_tip")
require("game/boss_invasion/boss_invasion_main_quality_tip")
require("game/boss_invasion/boss_invasion_result_view")

BOSSInvasionWGCtrl = BOSSInvasionWGCtrl or BaseClass(BaseWGCtrl)

function BOSSInvasionWGCtrl:__init()
	if BOSSInvasionWGCtrl.Instance then
		print_error("[BOSSInvasionWGCtrl]:Attempt to create singleton twice!")
	end
	BOSSInvasionWGCtrl.Instance = self

    self.data = BOSSInvasionWGData.New()
    self.scene_view = BOSSInvasionSceneView.New(GuideModuleName.BOSSInvasionSceneView)
    self.boss_quality_view = BOSSInvasionBossQualityView.New()
    self.guwu_view = BOSSInvasionGuWuView.New()
    self.privilege_view = BOSSInvasionPrivilegeView.New()
    self.privilege_skill_show_view = BOSSInvasionPrivilegeSkillShowView.New()
    self.rank_reward_view = BOSSInvasionRankRewardView.New()
    self.quality_add_tip = BOSSInvasionBossQualityAddTip.New()
    self.main_quality_tip = BOSSInvasionMainQualityTip.New()
    self.result_view = BOSSInvasionResultView.New()

	self:RegisterProtocol(CSCrossBossStrikeOperate)
	self:RegisterProtocol(SCCrossBossStrikeBaseInfo,"OnSCCrossBossStrikeBaseInfo")
    self:RegisterProtocol(SCCrossBossStrikeSceneInfo,"OnSCCrossBossStrikeSceneInfo")
    self:RegisterProtocol(SCCrossBossStrikeRoleInfo,"OnSCCrossBossStrikeRoleInfo")
    self:RegisterProtocol(SCCrossBossStrikeRoleBaseInfo,"OnSCCrossBossStrikeRoleBaseInfo")
    self:RegisterProtocol(SCCrossBossStrikeHurtRankInfo, "OnSCCrossBossStrikeHurtRankInfo")

    if not self.gather_enter_visible then
		self.gather_enter_visible = GlobalEventSystem:Bind(ObjectEventType.VISIBLE_OBJ_ENTER_GATHER, BindTool.Bind(self.OnGatherEnterVisible, self))
	end

    if not self.change_guaji_type then
        self.change_guaji_type = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.ChangeGuajiType, self))
    end

    self.time_index = 1
    if not self.day_pass then
        self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
    end

    -- self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	-- RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"guild_id"})

    -- self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    -- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
end

function BOSSInvasionWGCtrl:__delete()
    BOSSInvasionWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.scene_view then
        self.scene_view:DeleteMe()
        self.scene_view = nil
    end

    if self.boss_quality_view then
        self.boss_quality_view:DeleteMe()
        self.boss_quality_view = nil
    end

    if self.guwu_view then
        self.guwu_view:DeleteMe()
        self.guwu_view = nil
    end

    if self.privilege_view then
        self.privilege_view:DeleteMe()
        self.privilege_view = nil
    end

    if self.privilege_skill_show_view then
        self.privilege_skill_show_view:DeleteMe()
        self.privilege_skill_show_view = nil
    end

    if self.rank_reward_view then
        self.rank_reward_view:DeleteMe()
        self.rank_reward_view = nil
    end

    if self.quality_add_tip then
        self.quality_add_tip:DeleteMe()
        self.quality_add_tip = nil
    end

    if self.main_quality_tip then
        self.main_quality_tip:DeleteMe()
        self.main_quality_tip = nil
    end

    if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end

    if self.role_data_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
        self.role_data_change = nil
    end

    if self.gather_enter_visible then
		GlobalEventSystem:UnBind(self.gather_enter_visible)
		self.gather_enter_visible = nil
	end

    if self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

    if self.change_guaji_type then
        GlobalEventSystem:UnBind(self.change_guaji_type)
        self.change_guaji_type = nil
    end

    if  self.result_view then
        self.result_view:DeleteMe()
        self.result_view = nil
    end

    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end

    self.time_index = nil
end

-------------------------------------PROTOCOL_START------------------------------------
function BOSSInvasionWGCtrl:SendBOSSInvasionReq(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossBossStrikeOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 因为场景外不知道status ，但是需要用到，后端加在这了
function BOSSInvasionWGCtrl:OnSCCrossBossStrikeBaseInfo(protocol)
    -- print_error("----------protocol--------",protocol)
    self.data:SetCrossBossStrikeBaseInfo(protocol)
    self:CheckShowMainQualityTip()
    
    if self.boss_quality_view and self.boss_quality_view:IsOpen() then
        self.boss_quality_view:Flush()
    end

    self:FlushRoleHuDun()

    MainuiWGCtrl.Instance:FlushView(0, "boss_invasion_quality")
end

-- 检测主界面质量弹窗
function BOSSInvasionWGCtrl:CheckShowMainQualityTip()
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end

    local is_max_quality = self.data:IsBossMaxQuality()
    if is_max_quality then
        if self.main_quality_tip and self.main_quality_tip:IsOpen() then
            self.main_quality_tip:Close()
        end

        return
    end

    local tip_show_time_cfg = self.data:GetTipShowTimeCfg()

    if self.time_index > #tip_show_time_cfg then
        return
    end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local now_day_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)

    for i = self.time_index, #tip_show_time_cfg do
        local data = tip_show_time_cfg[i]

        local start_time = now_day_start_time + math.floor(data.start_time / 100) * 60 * 60 + (data.start_time % 100) * 60
        local end_time = now_day_start_time + math.floor(data.end_time / 100) * 60 * 60 + (data.end_time % 100) * 60

        -- 等待开启
        if server_time < start_time then
            self.delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
                self:CheckShowMainQualityTip()
            end, start_time - server_time + 1)

            break
        elseif server_time >= start_time and server_time < end_time then
            --显示开启
            self:OpenMainQualityTip(data)
            self.time_index = self.time_index + 1
            break
        end
    end
end

function BOSSInvasionWGCtrl:OnSCCrossBossStrikeSceneInfo(protocol)
    -- print_error("----------protocol--------",protocol)
    local old_state = self.data:GetCurActStatus()
    self.data:SetCrossBossStrikeSceneInfo(protocol)
    self:FlushRoleHuDun()

    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush()
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.BOSS_INVASION then
        MainuiWGCtrl.Instance:FlushXunLuStates()

        if old_state == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_BOSS and protocol.status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
            self:PlayBossBornCG()
            self:PlaySceneBgBGM()
        end

        if old_state == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS and protocol.status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END then
            local boss_end_guide_id = self.data:GetOtherCfgDataByAttrName("boss_end_guide_id")
            local boss_born_notice = NovicePopDialogWGData.Instance:GetConfigById(boss_end_guide_id)
            ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = boss_born_notice})  -- 功能引导表
        end

        local scene_logic = Scene.Instance:GetSceneLogic()

        if scene_logic and scene_logic.SetAnswerAreaEffectState then
            scene_logic:SetAnswerAreaEffectState(protocol.status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION or protocol.status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW)
        end

        if self.data:IsGetPrivilege() then
            -- 特权 自动移动到
            if (old_state == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_QUESTION or old_state == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW) and protocol.status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION then
                if GuajiCache.guaji_type == GuajiType.Auto or GuajiCache.guaji_type == GuajiType.None then
                    self:PrivilegeAutoMoveToRightAnswerArea()
                end
            end
        end
    end

    -- local status = protocol.status
	-- if status == CROSS_BOSS_STRIKE_STATUS.STATUS_END then
	-- 	print_error("场景内活动结束")
	-- elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_QUESTION then
	-- 	-- 等待答题
	-- 	print_error("等待答题")
	-- elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION then
	-- 	-- 答题中
	-- 	print_error("答题中")
	-- elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION_SHOW then
	-- 	-- 答题展示 1题后 展示 会切回大体中
	-- 	print_error("答题展示 1题后 展示 会切回大体中")
	-- elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_WAIT_BOSS then
	-- 	-- 等待boss刷新
	-- 	print_error("等待boss刷新")
	-- elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
	-- 	-- 
	-- 	print_error("boss 剩余存活时间")
	-- elseif status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS_END then
	-- 	-- 
	-- 	print_error("boss结束展示  宝箱时间 最后跳回 end")
	-- end
end

function BOSSInvasionWGCtrl:PlaySceneBgBGM()
    -- 神龙出现后播放音乐
    local audio_clip = self.data:GetOtherCfgDataByAttrName("audio_clip")

    if nil ~= audio_clip and "" ~= audio_clip then
        local bundle, asset = ResPath.GetBGMResPath(audio_clip)
        AudioService.Instance:PlayBgm(bundle, asset)
    end
end

function BOSSInvasionWGCtrl:PrivilegeAutoMoveToRightAnswerArea()
    local cur_boss_cfg = self.data:GetCurBossCfg()
    local question_cfg = self.data:GetCurQuestionCfg()
    if cur_boss_cfg and question_cfg then
        local pos_cfg = cur_boss_cfg["answer".. question_cfg.right_answer .."_pos"]

        if pos_cfg then
            local main_role = Scene.Instance:GetMainRole()
            local role_pos_x, role_pos_y = main_role:GetLogicPos()
            local pos_list = string.split(pos_cfg, ",")
            local pos_x, pos_y = pos_list[1], pos_list[2]
            local distance = GameMath.GetDistance(pos_x, pos_y, role_pos_x, role_pos_y, true)

            if distance > cur_boss_cfg.answer_range then
                GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
                    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
                end)
                GuajiWGCtrl.Instance:MoveToPos(cur_boss_cfg.scene_id, pos_x, pos_y)
            end
        end
    end
end

function BOSSInvasionWGCtrl:PlayBossBornCG()
    local cg_bundle = "cg/a3_cg_xinshoucun_prefab"
	local cg_asset = "A3_CG_XinShouCun3"

	CgManager.Instance:Play(BaseCg.New(cg_bundle, cg_asset),
		function()
            local boss_show_guide_id = self.data:GetOtherCfgDataByAttrName("boss_show_guide_id")
            local boss_born_notice = NovicePopDialogWGData.Instance:GetConfigById(boss_show_guide_id)

            if not IsEmptyTable(boss_born_notice) then
                ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = boss_born_notice})  -- 功能引导表
            end

            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end,
		function(cg_obj)
		end,
	nil)
end

function BOSSInvasionWGCtrl:OnSCCrossBossStrikeRoleInfo(protocol)
    -- print_error("----------protocol--------", protocol)
    local old_exp = self.data:GetCurExp()
    local old_xiuwei = self.data:GetCurXiuWei()
    local old_beast_exp = self.data:GetBeastExp()
    local old_coin = self.data:GetCurCoin()

    self.data:SetCrossBossStrikeRoleInfo(protocol)

    local status = self.data:GetCurActStatus()

    if status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
        local show_get_effect = false
        -- 获得特效
        if old_exp < protocol.get_exp then
            show_get_effect = true
        end
    
        if old_xiuwei < protocol.get_xiuwei then
            show_get_effect = true
        end
    
        if old_beast_exp < protocol.get_beast_exp then
            show_get_effect = true
        end
    
        if old_coin < protocol.get_coin then
            show_get_effect = true
        end
    
        if show_get_effect then
            if self.scene_view and self.scene_view:IsOpen() then
                self.scene_view:Flush(0, "show_get_effect")
            end
        end
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.BOSS_INVASION then
        MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
            local t = {}
            t.scene_type = SceneType.BOSS_INVASION
            t.coin_guwu_count = protocol.coin_guwu_times
            t.gold_guwu_count = protocol.gold_guwu_times
            GlobalEventSystem:Fire(OtherEventType.FB_GUWU_CHANGE, t)
        end)
    end

    if self.guwu_view and self.guwu_view:IsOpen() then
        self.guwu_view:Flush()
    end

    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush()
    end
end

function BOSSInvasionWGCtrl:OnSCCrossBossStrikeRoleBaseInfo(protocol)
    -- print_error("----------protocol--------",protocol)
    self.data:SetCrossBossStrikeRoleBaseInfo(protocol)

    if self.privilege_view and self.privilege_view:IsOpen() then
        self.privilege_view:Flush()
    end

    if protocol.rmb_buy_flag == 1 then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type == SceneType.BOSS_INVASION then
            local status = self.data:GetCurActStatus()
            if status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
                local main_role = Scene.Instance:GetMainRole()

                if main_role then
                    main_role:SetBossInvasionHuDunVisiable(true)
                end
            end
        end
    end

    MainuiWGCtrl.Instance:FlushXunLuStates()
    MainuiWGCtrl.Instance:FlushView(0, "boss_invasion_privilege")
end

function BOSSInvasionWGCtrl:OnQuestionInfo(protocol)
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.BOSS_INVASION then
        return
    end

    if protocol.result == 1 then
        if protocol.param1 == 0 then
            -- 失败
            -- SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.AnswerFalse)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.f_bslx_hdcw, is_success = false, pos = Vector2(0, 0)})
        elseif protocol.param1 == 1 then
            --成功
            -- SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.AnswerTrue)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_bslx_hdzq, is_success = true, pos = Vector2(0, 0)})

            if self.scene_view and self.scene_view:IsOpen() then
                self.scene_view:Flush(0, "answer_suc")
            end
        elseif protocol.param1 == 2 then
            --未参与
            SysMsgWGCtrl.Instance:ErrorRemind(Language.BOSSInvasion.AnswerNotInArea)
        end
    end
end

function BOSSInvasionWGCtrl:OnSCCrossBossStrikeHurtRankInfo(protocol)
    -- print_error("---------伤害列表---------", protocol)
    self.data:SetCrossBossStrikeHurtRankInfo(protocol)

    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush()
    end

    if self.rank_reward_view and not self.rank_reward_view:IsOpen() then
        self.rank_reward_view:Flush()
    end
end

function BOSSInvasionWGCtrl:OnGatherEnterVisible(vo, obj)
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.BOSS_INVASION then
        return
    end

    local color_cfg = self.data:GetCurBossColorCfg()
    if color_cfg and color_cfg.gather_id and vo.gather_id == color_cfg.gather_id then
        self.data:SetHasGatherTimes(vo.has_gather_times)

        local is_gather_flag = self.data:IsGatherFlag()

        if not is_gather_flag then
            local can_gather_flag = self.data:GetCanGatherFlag()
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

            if can_gather_flag then
                GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                    MoveCache.SetEndType(MoveEndType.Gather)
                    MoveCache.param1 = vo.gather_id
                    MoveCache.target_obj = obj
                    GuajiCache.target_obj_id = vo.obj_id
                end)
                    
                local scene_id = Scene.Instance:GetSceneId()
                GuajiWGCtrl.Instance:MoveToPos(scene_id, vo.pos_x, vo.pos_y, 4)
            end
        end
    end

    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Flush()
    end
end

function BOSSInvasionWGCtrl:OpenResultView(protocol)
    -- print_error("排名结算",protocol.param, protocol.item_list)
    if protocol.param <= 0 then
        return
    end

    if self.result_view then
        self.result_view:SetDataListAndOpen(protocol.param, protocol.item_list)
    end
end

-- 跨服BOSS怪物技能命中 result(1 succ, 0 fail) param1=skill_id  param2:monster_skill_times   参数2表示次数  param2 == 1表示每次活动首次收到技能影响
function BOSSInvasionWGCtrl:OnMonsterSkillResult(protocol)
    local skill_id = protocol.param1

    if nil ~= skill_id then
        local skill_cfg = BOSSInvasionWGData.Instance:GetCurSkillCfgBySkillId(skill_id)
        
        if skill_cfg and skill_cfg.tips_txt and "" ~= skill_cfg.tips_txt then
             TipWGCtrl.Instance:ShowZCRuneMsg(skill_cfg.tips_txt)
        end

        if protocol.param2 and protocol.param2 == 1 then
            if self.scene_view and self.scene_view:IsOpen() then
                self.scene_view:Flush(0, "first_affectrd_by_skill")
            end
        end
    end
end

function BOSSInvasionWGCtrl:FlushRoleHuDun()
    local main_role = Scene.Instance:GetMainRole()
    if main_role then
        local is_get_privilege = self.data:IsGetPrivilege()

        if is_get_privilege then
            local scene_type = Scene.Instance:GetSceneType()
            if scene_type == SceneType.BOSS_INVASION then
                local status = self.data:GetCurActStatus()
                
                if status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS then
                    main_role:SetBossInvasionHuDunVisiable(true)
                else
                    main_role:SetBossInvasionHuDunVisiable(false)
                end
            end
        else
            main_role:SetBossInvasionHuDunVisiable(false)
        end
    end
end
-------------------------------------PROTOCOL_END--------------------------------------

-------------------------------------VIEW_OPEA_START-----------------------------------
function BOSSInvasionWGCtrl:OpenSceneView()
    if self.scene_view and not self.scene_view:IsOpen() then
        self.scene_view:Open()
    end
end

function BOSSInvasionWGCtrl:CloseSceneView()
    if self.scene_view and self.scene_view:IsOpen() then
        self.scene_view:Close()
    end
end

function BOSSInvasionWGCtrl:GetSceneView()
    return self.scene_view
end

function BOSSInvasionWGCtrl:OpenPrivilegeView()
    if self.privilege_view and not self.privilege_view:IsOpen() then
        self.privilege_view:Open()
    end
end

function BOSSInvasionWGCtrl:OpenRankRewardView()
    if self.rank_reward_view and not self.rank_reward_view:IsOpen() then
        self.rank_reward_view:Open()
    end
end

function BOSSInvasionWGCtrl:OpenPrivilegeSkillShowView()
    if self.privilege_skill_show_view and not self.privilege_skill_show_view:IsOpen() then
        self.privilege_skill_show_view:Open()
    end
end

function BOSSInvasionWGCtrl:OpenQualityView()
    if self.boss_quality_view and not self.boss_quality_view:IsOpen() then
        self.boss_quality_view:Open()
    end
end

function BOSSInvasionWGCtrl:OpenQualityAddTip()
    if self.quality_add_tip and not self.quality_add_tip:IsOpen() then
        self.quality_add_tip:Open()
    end
end

function BOSSInvasionWGCtrl:ShowBossInvasionDialog()
    local data = NovicePopDialogWGData.Instance:GetConfigById(4)
    if not IsEmptyTable(data) then
        ViewManager.Instance:Open(GuideModuleName.NovicePopDialogView, nil, "all", {data = data})
    end
end

function BOSSInvasionWGCtrl:OpenGuwuView()
    if self.guwu_view and not self.guwu_view:IsOpen() then
        self.guwu_view:Open()
    end
end

function BOSSInvasionWGCtrl:OpenMainQualityTip(time_data)
    local scene_type = Scene.Instance:GetSceneType()
    
    if scene_type == SceneType.Common then
        local act_level_enough = ActivityWGData.Instance:CheckActOpenByRoleLevel(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_BOSS_INVASION)

        if act_level_enough then
            if self.main_quality_tip then
                self.main_quality_tip:SetDataAndOpen(time_data)
            end
        end
    end

end
-------------------------------------VIEW_OPEA_END-------------------------------------

-------------------------------------EVENT_START---------------------------------------
function BOSSInvasionWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "guild_id" then

	end
end

function BOSSInvasionWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        
	end
end

function BOSSInvasionWGCtrl:OnDayPass()
    self.time_index = 1
    self:CheckShowMainQualityTip()
end

function BOSSInvasionWGCtrl:ChangeGuajiType(guaji_type)
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.BOSS_INVASION then
        if self.data:IsGetPrivilege() then
            local status = self.data:GetCurActStatus()
    
            if status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION then
                if guaji_type == GuajiType.None or guaji_type == GuajiType.Auto then
                    self:PrivilegeAutoMoveToRightAnswerArea()
                end
            end  
        end
    end
end
-------------------------------------EVENT_END-----------------------------------------