-- 奖励找回

DAILYFIND_TYPE =
{
	OFFLINE_EXP = 1, 								-- 离线经验
	TASK = 2, 										-- 日常任务找回
	ACTIVITY = 3, 									-- 活动找回
}

DAILYFIND_TASK_TYPE =
{
	TUMO = 0,										-- 屠魔
	HUSONG = 1,										-- 护送
	GUILD_TASK = 2,									-- 仙盟任务
	QUESTION = 3,									-- 答题

	MAX = 4,
}

DAILYFIND_ACTIVITY_TYPE = {
	DAILYFIND_TYPE_TUMO = 0,                  		-- 屠魔（日常任务）
  	DAILYFIND_TYPE_HUSONG = 1,                  	-- 护送
  	DAILYFIND_TYPE_COIN_FB = 2,                  	-- 龙王宝藏
  	DAILYFIND_TYPE_LINGCHONG_FB = 3,              	-- 仙灵圣殿
  	DAILYFIND_TYPE_TEAM_EQUIP_FB = 4,              	-- 炼狱洞窟
  	DAILYFIND_TYPE_BUILD_TOWER_FB = 5,              -- 六道轮回
  	DAILYFIND_TYPE_BATTLE_FIELD_FB = 6,             -- 竞技场
  	DAILYFIND_TYPE_YUANGU_XIANDIAN_FB = 7,          -- 高级装备本(远古仙殿)
  	DAILYFIND_TYPE_LINGHUNSQUARE_FB = 8,            -- 高级经验本(灵魂广场)
  	DAILYFIND_TYPE_KILLGODTOWER = 9,				-- 诸神塔奖励
}

function BiZuoView:InitDailyFindView()
	self.cur_cell = {}
	self.daily_find_datas = {}
	self.btn_flag = 2
	if not self.daily_find_list then
		self.daily_find_list = AsyncBaseGrid.New()
		self.daily_find_list:CreateCells({col = 2,change_cells_num = 1, list_view = self.node_list["ph_dailyfind_list"],
		assetBundle = "uis/view/bizuo_ui_prefab", assetName = "cel_daily_find_cell",  itemRender = DailyFindRender})
	end
	self.daily_find_list:SetStartZeroIndex(false)

	--铜币按钮
	self.node_list.btn_coin.button:AddClickListener(BindTool.Bind(self.OnClickBtnCoin, self))
	--元宝按钮
	self.node_list.btn_gold.button:AddClickListener(BindTool.Bind(self.OnClickBtnGold, self))
	--一键找回
	self.node_list.all_find_btn.button:AddClickListener(BindTool.Bind(self.OnClickAllFindBtn, self))

	self.item_index = 0

	self.select_find_type = 1

	if self.select_find_type == 1 then
		self.node_list.img_coin_hook:SetActive(false)
		self.node_list.img_gold_hook:SetActive(true)
	else
		self.node_list.img_coin_hook:SetActive(true)
		self.node_list.img_gold_hook:SetActive(false)
	end

	-- 不确定策划到时后加不加回来
	-- local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	-- if PlayerPrefsUtil.HasKey("find_type"..role_id) then
	-- 	if tonumber(PlayerPrefsUtil.GetString("find_type"..role_id)) == 1 then
	-- 		self.node_list.img_coin_hook:SetActive(false)
	-- 		self.node_list.img_gold_hook:SetActive(true)
	-- 	else
	-- 		self.node_list.img_coin_hook:SetActive(true)
	-- 		self.node_list.img_gold_hook:SetActive(false)
	-- 	end
	-- end
end

function BiZuoView:DescoryDailyFind()
	if self.daily_find_list then
		self.daily_find_list:DeleteMe()
		self.daily_find_list = nil
	end

	self.reward_data = nil
	self.item_index= nil

	self.daily_find_datas = {}
	self.select_find_type = 0
	self.btn_flag = 0
end

function BiZuoView:OnFlushDailyFind()
	if self.node_list.img_coin_hook:GetActive() then
		self.btn_flag = 1 --铜币
	end

	if self.node_list.img_gold_hook:GetActive() then
		self.btn_flag = 2 --元宝
	end

	WelfareWGData.Instance:CacheFindBtnFlag(self.btn_flag)
	self.daily_find_datas = WelfareWGData.Instance:ResetDailyFindList()

	for i,v in ipairs(self.daily_find_datas) do
		v.btn_flag = self.btn_flag
	end

	self.node_list.lbl_complete:SetActive(#self.daily_find_datas == 0)
	-- if #daily_find_datas <= 0 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.ShowHint)
	-- end

	self.daily_find_list:SetDataList(self.daily_find_datas)

	local is_all_find = WelfareWGData.Instance:IsDailyFindList(self.daily_find_datas)
	self.node_list.all_find_btn:SetActive(is_all_find == 1)
end

--铜币按钮
function BiZuoView:OnClickBtnCoin()
	if self.node_list.img_coin_hook:GetActive() then
		return
	end
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	-- PlayerPrefsUtil.SetString("find_type"..role_id, 0)
	self.select_find_type = 0
	self.node_list.img_gold_hook:SetActive(false)
	self.node_list.img_coin_hook:SetActive(true)
	self:OnFlushDailyFind()
end

--元宝按钮
function BiZuoView:OnClickBtnGold()
	if self.node_list.img_gold_hook:GetActive() then
		return
	end
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	self.select_find_type = 1
	-- PlayerPrefsUtil.SetString("find_type"..role_id, 1)
	self.node_list.img_coin_hook:SetActive(false)
	self.node_list.img_gold_hook:SetActive(true)
	self:OnFlushDailyFind()
end

function BiZuoView:OnClickAllFindBtn()
	local ok_func = function ()
		self:FindKeyBiZuo()
	end
	local total_money = WelfareWGData.Instance:DailyFindListTotalMoney(self.daily_find_datas)
	local language_desc = self.btn_flag == 1 and Language.BiZuo.DailyFindTips2 or Language.BiZuo.DailyFindTips
	local str = string.format(language_desc, total_money)
	local check_string = "dailyfind"

	local chect_text_str = Language.Common.DontTip2
	TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string, nil)
end

function BiZuoView:FindKeyBiZuo()
	if IsEmptyTable(self.daily_find_datas) then
		return
	end

	--仙玉
	local cur_xianyu_num = RoleWGData.Instance:GetRoleInfo().gold
	--绑玉
	local cur_bind_yu_num = RoleWGData.Instance:GetRoleInfo().bind_gold

	local money_num =  cur_xianyu_num + cur_bind_yu_num

	local total_money = WelfareWGData.Instance:DailyFindListTotalMoney(self.daily_find_datas)

	for k, v in ipairs(self.daily_find_datas) do
		local vip_times = v.vip_times
		if v.btn_flag == 1 then
			vip_times =  v.plusvo.is_open == 2 and 0 or v.vip_times
		end
		local cur_times = v.times
		if v.times == 0 then
			cur_times = v.times + vip_times
		end

		local retrieved_times = GameMath.Round(cur_times)

		if v.plusvo.daily_find == 1 then
			-- if retrieved_times - v.times > 0 then
			-- 	local vip_times = retrieved_times - v.times

			-- 	if money_num < total_money and v.btn_flag == 2 then
			-- 		VipWGCtrl.Instance:OpenTipNoGold()
			-- 		return
			-- 	end
			-- 	WelfareWGCtrl.Instance:SendGetDailyFindWelfare(v.small_type, v.btn_flag - 1, vip_times, IS_VIP_OR_NOT.IS_VIP)
			-- 	WelfareWGCtrl.Instance:SendGetDailyFindWelfare(v.small_type, v.btn_flag - 1, v.times, IS_VIP_OR_NOT.NOT_VIP)
			-- else
			-- 	if money_num < total_money and v.btn_flag == 2 then
			-- 		VipWGCtrl.Instance:OpenTipNoGold()
			-- 		return
			-- 	end
			-- 	WelfareWGCtrl.Instance:SendGetDailyFindWelfare(v.small_type, v.btn_flag - 1, retrieved_times, IS_VIP_OR_NOT.NOT_VIP)
			-- end

			if v.times > 0 or vip_times > 0 then
				if money_num < total_money and v.btn_flag == 2 then
					VipWGCtrl.Instance:OpenTipNoGold()
					return
				end
				if vip_times > 0 then
					WelfareWGCtrl.Instance:SendGetDailyFindWelfare(v.small_type, v.btn_flag - 1, v.times, IS_VIP_OR_NOT.NOT_VIP)
					WelfareWGCtrl.Instance:SendGetDailyFindWelfare(v.small_type, v.btn_flag - 1, vip_times, IS_VIP_OR_NOT.IS_VIP)
				else
					WelfareWGCtrl.Instance:SendGetDailyFindWelfare(v.small_type, v.btn_flag - 1, v.times, IS_VIP_OR_NOT.NOT_VIP)
				end
			 	
			end
		else
			if money_num < total_money and v.btn_flag == 2 then
				VipWGCtrl.Instance:OpenTipNoGold()
				return
			end
			WelfareWGCtrl.Instance:SendWelfareActivityFind(v.small_type, v.btn_flag - 1, retrieved_times)
		end
	end
end
