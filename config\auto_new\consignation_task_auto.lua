-- W-委托任务.xls
local item_table={
[1]={item_id=26368,num=1,is_bind=1},
[2]={item_id=26344,num=2,is_bind=1},
[3]={item_id=22530,num=1,is_bind=1},
[4]={item_id=26378,num=1,is_bind=1},
[5]={item_id=26376,num=2,is_bind=1},
[6]={item_id=22622,num=1,is_bind=1},
[7]={item_id=26379,num=1,is_bind=1},
[8]={item_id=26415,num=2,is_bind=1},
[9]={item_id=26200,num=3,is_bind=1},
[10]={item_id=26203,num=3,is_bind=1},
[11]={item_id=39104,num=150,is_bind=1},
[12]={item_id=30423,num=3,is_bind=1},
[13]={item_id=26501,num=1,is_bind=1},
[14]={item_id=22624,num=1,is_bind=1},
[15]={item_id=26367,num=1,is_bind=1},
[16]={item_id=26344,num=3,is_bind=1},
[17]={item_id=22530,num=3,is_bind=1},
[18]={item_id=26415,num=3,is_bind=1},
[19]={item_id=26376,num=3,is_bind=1},
[20]={item_id=26516,num=1,is_bind=1},
[21]={item_id=39153,num=1,is_bind=1},
[22]={item_id=22071,num=2,is_bind=1},
[23]={item_id=26416,num=1,is_bind=1},
[24]={item_id=30423,num=2,is_bind=1},
[25]={item_id=26376,num=4,is_bind=1},
[26]={item_id=26344,num=4,is_bind=1},
[27]={item_id=22576,num=1,is_bind=1},
[28]={item_id=39104,num=200,is_bind=1},
[29]={item_id=30424,num=1,is_bind=1},
[30]={item_id=22531,num=1,is_bind=1},
[31]={item_id=26344,num=5,is_bind=1},
[32]={item_id=26376,num=5,is_bind=1},
[33]={item_id=22010,num=1,is_bind=1},
[34]={item_id=30447,num=1,is_bind=1},
[35]={item_id=30443,num=1,is_bind=1},
[36]={item_id=30777,num=1,is_bind=1},
[37]={item_id=39153,num=2,is_bind=1},
[38]={item_id=22072,num=1,is_bind=1},
[39]={item_id=26200,num=5,is_bind=1},
[40]={item_id=26203,num=5,is_bind=1},
[41]={item_id=39104,num=300,is_bind=1},
[42]={item_id=30424,num=2,is_bind=1},
[43]={item_id=26516,num=2,is_bind=1},
[44]={item_id=26367,num=2,is_bind=1},
[45]={item_id=26378,num=2,is_bind=1},
[46]={item_id=26345,num=1,is_bind=1},
[47]={item_id=26517,num=1,is_bind=1},
[48]={item_id=26502,num=1,is_bind=1},
[49]={item_id=26200,num=8,is_bind=1},
[50]={item_id=26203,num=8,is_bind=1},
[51]={item_id=22532,num=1,is_bind=1},
[52]={item_id=30778,num=1,is_bind=1},
[53]={item_id=22590,num=1,is_bind=1},
[54]={item_id=26344,num=1,is_bind=1},
[55]={item_id=26376,num=1,is_bind=1},
[56]={item_id=22531,num=2,is_bind=1},
[57]={item_id=26200,num=1,is_bind=1},
[58]={item_id=26200,num=2,is_bind=1},
[59]={item_id=26203,num=1,is_bind=1},
[60]={item_id=26203,num=2,is_bind=1},
[61]={item_id=48127,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
exp_generator={
[0]={fight_tianshen_num=0,},
[1]={fight_tianshen_num=1,exp=56,},
[2]={fight_tianshen_num=2,exp=72,},
[3]={fight_tianshen_num=3,exp=84,},
[4]={fight_tianshen_num=4,exp=98,}
},

exp_generator_meta_table_map={
},
task={
{cost_exp=800,time=1800,quick_finish_cost_money=90,color=1,descirpt="桃天村的酒庄在一天夜里丢失了一坛珍藏美酒，希望有人找回。",name="酒庄小偷",},
{id=1,cost_exp=800,time=1800,reward={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3]},quick_finish_cost_money=90,color=1,},
{id=2,conditions="4,0,1,1|4,0,2,1|4,0,3,1",reward={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6]},descirpt="神灵仙岛出现罕见海啸，附近岛屿遭遇淹没，抵御天灾迫在眉睫。",name="仙岛海啸",},
{id=3,conditions="4,0,2,1|4,0,0,1|4,0,0,1",reward={[0]=item_table[7],[1]=item_table[5],[2]=item_table[3]},descirpt="传言鲸落经常有怪声响起，当地居民聘请能人去调查。",name="鲸落逸闻",},
{id=4,conditions="4,0,3,1|4,0,0,1|4,0,0,1",reward={[0]=item_table[8],[1]=item_table[9],[2]=item_table[6]},descirpt="有人在参观阴阳棋局时失踪，可能误入棋局迷阵，望有人救援。",name="阴阳失踪",},
{id=5,conditions="4,0,1,1|4,0,0,1|4,0,0,1",reward={[0]=item_table[8],[1]=item_table[10],[2]=item_table[3]},descirpt="万佛窟出现密洞，疑是千年古墓，希望有人前往探险。",name="万佛探险",},
{id=6,conditions="4,0,0,2|4,0,0,2|4,0,0,2",reward={[0]=item_table[11],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14]},descirpt="皇城有一枚金印被盗，窃贼留下一支兰花，希望有人前去破案。",name="皇城失窃",},
{id=7,reward={[0]=item_table[15],[1]=item_table[16],[2]=item_table[14],[3]=item_table[17]},descirpt="月牙湾的绿洲因常年不下雨导致枯竭，希望有人前往解决。",name="月牙枯竭",},
{id=8,conditions="4,0,1,2|4,0,1,2|4,0,0,2",reward={[0]=item_table[18],[1]=item_table[9],[2]=item_table[10],[3]=item_table[14]},descirpt="翡翠汤的温泉出现异样，泉眼被冻结，希望有人前去调查解决。",name="翡翠冰点",},
{id=9,conditions="4,0,2,2|4,0,2,2|4,0,0,2",reward={[0]=item_table[4],[1]=item_table[19],[2]=item_table[14],[3]=item_table[17]},descirpt="春风里一位花主得了一种怪病，卧床不起，重金请人治病",name="春风怪病",},
{id=10,conditions="4,0,3,2|4,0,3,2|4,0,0,2",reward={[0]=item_table[18],[1]=item_table[20],[2]=item_table[9],[3]=item_table[14]},descirpt="天池在月圆之夜会出现一个孤魂灵魄，希望有人前去调查。",name="天池残灵",},
{id=11,conditions="4,0,1,2|4,0,2,2|4,0,0,2",reward={[0]=item_table[1],[1]=item_table[16],[2]=item_table[14],[3]=item_table[17]},descirpt="千叶莲池最近灵力波动，不知是不是凶兆，希望有人前去调查。",name="莲池异样",},
{id=12,conditions="4,0,1,2|4,0,3,2|4,0,0,2",reward={[0]=item_table[21],[1]=item_table[22],[2]=item_table[14],[3]=item_table[23]},descirpt="无涯书院出了一道谜题难倒了众书生，希望有人能前往解答。",name="书院谜题",},
{id=13,conditions="4,0,2,2|4,0,3,2|4,0,0,2",reward={[0]=item_table[7],[1]=item_table[19],[2]=item_table[14],[3]=item_table[17]},descirpt="流波花海的地脉动荡，灵力匮散，大片的花海枯萎，需要救助。",name="花之枯竭",},
{id=14,conditions="4,0,1,2|4,0,2,2|4,0,3,2",reward={[0]=item_table[11],[1]=item_table[24],[2]=item_table[13],[3]=item_table[3]},descirpt="所有人都在上早八，你为什么十点才到，问你话呢。",name="老板问话",},
{id=15,reward={[0]=item_table[21],[1]=item_table[22],[2]=item_table[3],[3]=item_table[23]},descirpt="昆仑雪域发生雪崩，剑来镇居受困无法采购物资，急需救济。",name="雪山救济",},
{id=16,conditions="4,0,0,3|4,0,0,3|4,0,0,3",reward={[0]=item_table[15],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},descirpt="魔晶矿发生坍塌，几位矿民身陷灾区，希望有人前往救助。",name="魔晶矿难",},
{id=17,conditions="4,0,1,3|4,0,1,3|4,0,1,3",reward={[0]=item_table[1],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},descirpt="枫林晚出现剧毒瘴气，许多无辜人已中毒倒下，需要净化。",name="林枫除瘴",},
{id=18,conditions="4,0,2,3|4,0,2,3|4,0,2,3",reward={[0]=item_table[4],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},descirpt="摘星阁在研究星象时遇到无解难题，希望能人异士前去解决。",name="摘星困扰",},
{id=19,conditions="4,0,3,3|4,0,3,3|4,0,3,3",reward={[0]=item_table[7],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},descirpt="一只商队横穿西沙净土士竟然离奇失踪，急需人手来调查寻人。",name="荒漠寻人",},
{id=20,conditions="4,0,4,3|4,0,4,3|4,0,4,3",cost_exp=2100,time=3600,reward={[0]=item_table[13],[1]=item_table[9],[2]=item_table[10],[3]=item_table[27]},rate=60,quick_finish_cost_money=180,color=3,descirpt="霜叶镇因闹鬼事件导致无人前往，影响镇民收入，希望有人救助。",name="霜叶萧条",},
{id=21,conditions="4,0,5,3|4,0,5,3|4,0,5,3",reward={[0]=item_table[28],[1]=item_table[29],[2]=item_table[18],[3]=item_table[30]},descirpt="家父张二河儿子在城中闹事，希望有人前往抓拿。",name="叫他道歉",},
{id=22,conditions="4,0,2,4|4,0,3,4|4,0,4,4",reward={[0]=item_table[15],[1]=item_table[1],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},descirpt="有人在流光洞听到诡异歌声，闹得人心惶惶，希望有人前去调查。",name="流光歌声",},
{id=23,conditions="4,0,1,4|4,0,3,4|4,0,5,4",reward={[0]=item_table[4],[1]=item_table[7],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33]},descirpt="万佛窟出现密洞，疑是千年古墓，希望有人前往探险。",name="万佛探险",},
{id=24,conditions="4,0,1,4|4,0,2,4|4,0,4,4",reward={[0]=item_table[34],[1]=item_table[35],[2]=item_table[30],[3]=item_table[36],[4]=item_table[33]},descirpt="天池在月圆之夜会出现一个孤魂灵魄，希望有人前去调查。",name="天池残灵",},
{id=25,conditions="4,0,4,4|4,0,5,4|4,0,5,4",cost_exp=2600,time=5400,reward={[0]=item_table[37],[1]=item_table[38],[2]=item_table[39],[3]=item_table[40],[4]=item_table[33]},rate=35,quick_finish_cost_money=270,color=4,},
{id=26,conditions="4,0,4,4|4,0,4,4|4,0,5,4",reward={[0]=item_table[41],[1]=item_table[42],[2]=item_table[18],[3]=item_table[43],[4]=item_table[33]},descirpt="神灵仙岛出现罕见海啸，附近岛屿遭遇淹没，抵御天灾迫在眉睫。",name="仙岛海啸",},
{id=27,conditions="4,0,5,5|4,0,5,5|4,0,5,5",cost_exp=3000,time=10800,reward={[0]=item_table[44],[1]=item_table[45],[2]=item_table[46],[3]=item_table[38],[4]=item_table[29]},rate=30,quick_finish_cost_money=540,color=5,},
{id=28,conditions="4,0,4,5|4,0,4,5|4,0,4,5",reward={[0]=item_table[47],[1]=item_table[48],[2]=item_table[18],[3]=item_table[49],[4]=item_table[50]},descirpt="有人在参观阴阳棋局时失踪，可能误入棋局迷阵，望有人救援。",name="阴阳失踪",},
{id=29,conditions="4,0,4,6|4,0,5,6|4,0,0,6",reward={[0]=item_table[34],[1]=item_table[35],[2]=item_table[51],[3]=item_table[52],[4]=item_table[53]},descirpt="千叶莲池最近灵力波动，不知是不是凶兆，希望有人前去调查。",name="莲池异样",}
},

task_meta_table_map={
[16]=15,	-- depth:1
[8]=7,	-- depth:1
[5]=2,	-- depth:1
[4]=2,	-- depth:1
[3]=2,	-- depth:1
[6]=2,	-- depth:1
[28]=4,	-- depth:2
[27]=26,	-- depth:1
[25]=26,	-- depth:1
[24]=26,	-- depth:1
[23]=26,	-- depth:1
[20]=21,	-- depth:1
[19]=21,	-- depth:1
[18]=21,	-- depth:1
[17]=21,	-- depth:1
[29]=28,	-- depth:3
[22]=21,	-- depth:1
[30]=28,	-- depth:3
},
rent={
{},
{color=2,},
{color=3,reward={[0]=item_table[54],[1]=item_table[55]},},
{color=4,reward={[0]=item_table[2],[1]=item_table[5]},},
{color=5,reward={[0]=item_table[2],[1]=item_table[5],[2]=item_table[17]},},
{color=6,reward={[0]=item_table[16],[1]=item_table[19],[2]=item_table[17]},},
{color=7,reward={[0]=item_table[16],[1]=item_table[19],[2]=item_table[30]},},
{color=8,reward={[0]=item_table[16],[1]=item_table[19],[2]=item_table[56]},}
},

rent_meta_table_map={
},
borrow={
{},
{color=2,},
{color=3,reward={[0]=item_table[57],[1]=item_table[6]},},
{color=4,reward={[0]=item_table[58],[1]=item_table[59]},},
{color=5,reward={[0]=item_table[58],[1]=item_table[60]},},
{color=6,reward={[0]=item_table[9],[1]=item_table[60]},},
{color=7,reward={[0]=item_table[9],[1]=item_table[10]},},
{color=8,reward={[0]=item_table[9],[1]=item_table[10],[2]=item_table[14]},}
},

borrow_meta_table_map={
},
special_task={
{},
{id=1,cost_exp=200,time=3,reward={[0]=item_table[11],[1]=item_table[12],[2]=item_table[61],[3]=item_table[14]},color=2,descirpt="皇城有一枚金印被盗，窃贼留下一支兰花，希望有人前去破案。",name="皇城失窃",},
{id=2,cost_exp=300,time=5,reward={[0]=item_table[15],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},color=3,descirpt="魔晶矿发生坍塌，几位矿民身陷灾区，希望有人前往救助。",name="魔晶矿难",}
},

special_task_meta_table_map={
},
other_default_table={max_lilian_exp=20000,free_refresh_times=1,refresh_item_id=57741,refresh_item_num=1,refresh_cost_money=30,high_quality_task_min_color=3,max_rent_num=20,borrow_times=5,init_exp=2500,guild_max_borrow_num=200,borrow_time_limit=21600,min_cost_exp=2000,},

exp_generator_default_table={fight_tianshen_num=0,exp=42,},

task_default_table={id=0,conditions="4,0,0,1|4,0,0,1|4,0,0,1",cost_exp=1500,time=2400,reward={[0]=item_table[15],[1]=item_table[2],[2]=item_table[6]},rate=70,quick_finish_cost_money=120,color=2,descirpt="有一对情侣在同心岛因为出轨争吵起来，希望有人扶我去观看。",name="同心异梦",},

rent_default_table={color=1,reward={[0]=item_table[54]},},

borrow_default_table={color=1,reward={[0]=item_table[6]},},

special_task_default_table={id=0,conditions="4,2,3,2",cost_exp=100,time=1,reward={[0]=item_table[15],[1]=item_table[2],[2]=item_table[6]},quick_finish_cost_money=0,color=1,descirpt="桃天村的酒庄在一天夜里丢失了一坛珍藏美酒，希望有人找回。",name="酒庄小偷",}

}

