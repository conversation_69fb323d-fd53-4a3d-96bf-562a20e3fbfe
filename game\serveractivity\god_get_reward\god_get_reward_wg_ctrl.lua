require("game/serveractivity/god_get_reward/god_get_reward_wg_data")
require("game/serveractivity/god_get_reward/god_kite_render")
require("game/serveractivity/god_get_reward/god_get_reward_record")
require("game/serveractivity/god_get_reward/god_get_reward_result")
require("game/serveractivity/god_get_reward/god_get_reward_new_view")
require("game/serveractivity/god_get_reward/god_get_reward_exchange_view")

GodGetRewardWGCtrl = GodGetRewardWGCtrl or BaseClass(BaseWGCtrl)

function GodGetRewardWGCtrl:__init()
    if GodGetRewardWGCtrl.Instance ~= nil then
		ErrorLog("[GodGetRewardWGCtrl] attempt to create singleton twice!")
		return
	end
	GodGetRewardWGCtrl.Instance = self
	self.data = GodGetRewardWGData.New()
	self.new_view = GodGetRewardNewView.New(GuideModuleName.GodGetRewardNewView)
	self.record_view = GodGetRewardRecord.New(GuideModuleName.GodGetRewardRecord)
	self.exchange_view = GodGetRewardExchangeView.New()
	self.result_view = GodGetRewardResult.New()

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.DayChange, self))
    self:RegisterAllProtocols()
end

function GodGetRewardWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if nil ~= self.new_view then 
		self.new_view:DeleteMe()
		self.new_view = nil
	end
    if nil ~= self.record_view then
		self.record_view:DeleteMe()
		self.record_view = nil
	end
	if nil ~= self.result_view then
		self.result_view:DeleteMe()
		self.result_view = nil
	end
	GodGetRewardWGCtrl.Instance = nil
end

function GodGetRewardWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCRATianshenXunbaoLayerInfo,'OnSCRATianshenXunbaoLayerInfo')
    self:RegisterProtocol(SCRATianshenXunbaoDrawRecord,'OnSCRATianshenXunbaoDrawRecord')
    self:RegisterProtocol(SCRATianshenXunbaoDrawResult,'OnSCRATianshenXunbaoDrawResult')
    self:RegisterProtocol(SCRATianShenXunBaoItemExchangeInfo,'OnSCRATianShenXunBaoItemExchangeInfo')
end

function GodGetRewardWGCtrl:SendAllLayerRequire()
	local layer_cfg = self.data:GetLayerCfg()
	local layer_num = #layer_cfg
	for i = 1, layer_num do
		self:SendOpera(TS_XUNBAO_OPERA_TYPE.LAYER_INFO, i - 1)
		self:SendOpera(TS_XUNBAO_OPERA_TYPE.EXCHANGE_INFO, i - 1)
	end
end

function GodGetRewardWGCtrl:OnSCRATianshenXunbaoLayerInfo(protocol)
	self.data:ClearLayerCache(protocol.layer)
	local flag = self.data:GetResultFlagByLayer(protocol.layer)
    self.data:SetLayerInfo(protocol)
	if flag ~= 0 and protocol.draw_result_flag == 0 then
		self.new_view:Flush(0, "layer_info")
	end
	RemindManager.Instance:Fire(RemindName.GodGetReward)
end

function GodGetRewardWGCtrl:OnSCRATianshenXunbaoDrawRecord(protocol)
    self.data:SetRecordInfo(protocol)
	self.data:CalNewRecordNum()
	-- ViewManager.Instance:FlushView(GuideModuleName.GodGetReward, nil, "record")
	-- ViewManager.Instance:FlushView(GuideModuleName.GodGetRewardRecord)
	
    local data_list = self.data:GetRecordInfo()
	local tips_data_list = {}
    if not IsEmptyTable(data_list) then
		for i, v in ipairs(data_list) do
			local temp_data = {}
			local reward = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(v.reward_id)
			temp_data.item_data = reward.reward_item
			temp_data.consume_time = v.timestamp
			temp_data.role_name = v.name
			table.insert(tips_data_list, temp_data)
		end
    end

	TipWGCtrl.Instance:OpenTipsRewardRecordView(tips_data_list)
end

function GodGetRewardWGCtrl:OnSCRATianshenXunbaoDrawResult(protocol)
    self.data:SetResultInfo(protocol)
	if self.new_view:IsOpen() then
		self.new_view:Flush(0, "reward", {hit_slot = protocol.hit_slot})
	end
	RemindManager.Instance:Fire(RemindName.GodGetReward)
end

function GodGetRewardWGCtrl:OnSCRATianShenXunBaoItemExchangeInfo(protocol)
	self.data:SetExchangeInfo(protocol)
	self.new_view:Flush(0, "exchange", {protocol_id = 2588})
	self.exchange_view:Flush(0, "exchange", {protocol_id = 2588})
	RemindManager.Instance:Fire(RemindName.GodGetReward)
end

function GodGetRewardWGCtrl:SendOpera(opera_type, param_1, param_2, param_3)
    local t = {}
    t.rand_activity_type = ACTIVITY_TYPE.GOD_XUNBAO
    t.opera_type = opera_type
    t.param_1 = param_1
    t.param_2 = param_2
    t.param_3 = param_3
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function GodGetRewardWGCtrl:OpenResultView(again_call_back, close_call_back)
	self.result_view:SetAgainCallBack(again_call_back)
	self.result_view:SetCloseCallBack(close_call_back)
	self.result_view:Open()
end

function GodGetRewardWGCtrl:OpenExchangeView()
	self.exchange_view:Open()
end

function GodGetRewardWGCtrl:OpenView(tab_index, key, values)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_XUNBAO)
	if nil == act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OperationActivity.ActivityNoOpenHint2)
		return
	end

	local page_index = values and tonumber(values.open_param)
	if page_index then
		local is_open,tip_str = self.data:CheckLayerIsOpenByPageIndex(page_index, true)
		if not is_open then
			if tip_str then
				SysMsgWGCtrl.Instance:ErrorRemind(tip_str)
			end
			return
		end
	end

	self.new_view:Open()
	self.new_view:Flush(0, nil, values)
end

function GodGetRewardWGCtrl:FlushView(...)
	self.new_view:Flush(...)
end

function GodGetRewardWGCtrl:ViewIsOpen()
	return self.new_view:IsOpen() and not self.exchange_view:IsOpen()
end

function GodGetRewardWGCtrl:DayChange()
	self.data:ClearLayerInfo()
	self:SendAllLayerRequire()
	self.new_view:Flush(0, "day_change")
end