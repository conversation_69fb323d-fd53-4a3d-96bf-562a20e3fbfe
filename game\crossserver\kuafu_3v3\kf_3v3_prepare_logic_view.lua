local MatchingType = {
    Single 	= 1,    --单人匹配
    Team 	= 2,    --战队匹配
}
--3v3准备场景任务栏信息界面
KF3V3PrepareLogicView = KF3V3PrepareLogicView or BaseClass(SafeBaseView)
function KF3V3PrepareLogicView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_3v3_prepare_info")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "prepare_match_btn")
	self.view_cache_time = 1
    self.view_layer = UiLayer.MainUILow
    self.is_safe_area_adapter = true
end

function KF3V3PrepareLogicView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
	self.is_out_fb = nil

	if self.alone_enter_alert then
		self.alone_enter_alert:DeleteMe()
		self.alone_enter_alert = nil
	end
	if self.zhandui_enter_alert then
		self.zhandui_enter_alert:DeleteMe()
		self.zhandui_enter_alert = nil
	end
	if self.scene_info_change_event then
		GlobalEventSystem:UnBind(self.scene_info_change_event)
		self.scene_info_change_event = nil
	end
	if self.zhan_dui_info_change_event then
		GlobalEventSystem:UnBind(self.zhan_dui_info_change_event)
		self.zhan_dui_info_change_event = nil
	end
	if self.team_info_change_event then
		GlobalEventSystem:UnBind(self.team_info_change_event)
		self.team_info_change_event = nil
	end
	if self.cancel_macth_alert then
		self.cancel_macth_alert:DeleteMe()
		self.cancel_macth_alert = nil
	end
	self.wait_to_invite_list = {}
end

function KF3V3PrepareLogicView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_danren_match, BindTool.Bind(self.DanRenMatch, self))
	XUI.AddClickEventListener(self.node_list.btn_zhandui_match, BindTool.Bind(self.ZhanDuiMatch, self))
	XUI.AddClickEventListener(self.node_list.btn_auto_team, BindTool.Bind(self.AutoTeam, self))
	XUI.AddClickEventListener(self.node_list.btn_add_member1, BindTool.Bind(self.ClickAddMember, self))
	XUI.AddClickEventListener(self.node_list.btn_add_member2, BindTool.Bind(self.ClickAddMember, self))
	
	self.scene_info_change_event = GlobalEventSystem:Bind(OtherEventType.KF3V3PrepareSceneInfoChange, BindTool.Bind(self.OnSceneInfoChange, self))
	self.zhan_dui_info_change_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Info_Change, BindTool.Bind(self.OnZhanDuiInfoChange, self)) --战队信息改变
	self.team_info_change_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.OnTeamChange, self))         -- 队伍
	--玩家名称
	self.node_list.my_name.text.text = GameVoManager.Instance:GetMainRoleVo().name
end

function KF3V3PrepareLogicView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	self.obj = self.node_list["layout_3v3_prepare_info_root"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform)
	self.obj.transform.anchoredPosition = Vector2(0,0)
	self.obj.transform.localScale = Vector3.one
	mainui_ctrl:SetTaskPanel(false)
	-- mainui_ctrl:ChangeTaskBtnName(Language.KuafuPVP.KuaFuZhanDuiInfo)
	if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil
    self:RefreshBtnTime()
end
function KF3V3PrepareLogicView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end
function KF3V3PrepareLogicView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj.transform:SetParent(self.root_node_transform.transform)
        self.obj:SetActive(false)
    end

	-- MainuiWGCtrl.Instance:SetTaskAndTeamCallBack(nil, nil)
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text2)
end

function KF3V3PrepareLogicView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	self:Flush()
end

--刷新匹配按钮状态
function KF3V3PrepareLogicView:RefreshBtnTime(is_force_hide)
	if not self:IsLoaded() then
		return
	end
	--是否在匹配中
	if KF3V3WGData.Instance:GetIsMatching() and not is_force_hide then
		--单人匹配
		if SocietyWGData.Instance:GetIsInTeam() ~= 1 then 		
			self.node_list.match_danren:SetActive(true)
			self.node_list.match_zhandui:SetActive(false)
		--战队匹配
		else	
			self.node_list.match_danren:SetActive(false)
			self.node_list.match_zhandui:SetActive(true)
		end
		self.node_list.duanwei_icon:SetActive(false)
		self.node_list.time_danren:SetActive(true)
		--self.node_list.time_danren.text.text = string.format(Language.KuafuPVP.MatchingTime, TimeUtil.MSTime(KF3V3WGData.Instance:GetMatchTime()))
		self.node_list.time_danren.text.text = string.format(Language.KuafuPVP.MatchingTime, KF3V3WGData.Instance:GetMatchTime())
	else
		self.node_list.duanwei_icon:SetActive(true)
		self.node_list.time_danren:SetActive(false)
		self.node_list.match_danren:SetActive(false)
		self.node_list.match_zhandui:SetActive(false)
	end
end

--刷新战队战令
function KF3V3PrepareLogicView:FlushZhanZhanLing()
    local zhan_dui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
	local grade_cfg = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhan_dui_info.score)
	ZhanDuiWGCtrl.SetZhanDuiDuanWeiImage(self.node_list.duanwei_icon, grade_cfg)
end

function KF3V3PrepareLogicView:OnFlush()
	local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
	--战队令牌、战队令牌文字
	self:FlushZhanZhanLing()

	--战队名字
	self.node_list.zhandui_name.text.text = zhandui_info.name
	--战队段位
	local grade_cfg, next_score, is_max = KF3V3WGData.Instance:GetDuanWeiCfgAll(zhandui_info.score)
	self.node_list.zhandui_duanwei.text.text = grade_cfg.tier_name

	ZhanDuiWGCtrl.SetZhanDuiDuanWeiImage(self.node_list.zhanling_icon, grade_cfg)

	-- 可获得积分剩余场次
    local role_info = ZhanDuiWGData.Instance:GetRoleInfo()
    local cfg = KF3V3WGData.Instance:GetPKRewardCfg()
    local times = role_info.today_match_times
    local max_times = cfg.score_reward_time_limit
    local surplus_times = math.max(0, max_times - times)
    local format_str = surplus_times == 0 and Language.KuafuPVP.TodayAddScoreTips2 or Language.KuafuPVP.TodayAddScoreTips1
    self.node_list["surplus_add_score_text"].text.text = string.format(format_str, surplus_times, max_times)

	--段位分数进度
	if is_max then
		self.node_list.progress_text.text.text = ""
		self.node_list.progress.slider.value = 1
	else
		self.node_list.progress_text.text.text = zhandui_info.score  .. "/" .. next_score
		self.node_list.progress.slider.value = zhandui_info.score / next_score
	end

	--成员信息
	self:FlushMemberState()
	--获得经验
	--self:FlushExp()
end
function KF3V3PrepareLogicView:FlushExp()
	-- local prepare_scene_info = KF3V3WGData.Instance:GetPrepareSceneInfo()
	-- if prepare_scene_info then
	-- 	self.node_list.get_exp.text.text = prepare_scene_info.interval_reward_exp
	-- end
end
function KF3V3PrepareLogicView:FlushMemberState()
	local other_member_list = ZhanDuiWGData.Instance:GetZhanDuiOtherMemberList()
	if not other_member_list then
		return
	end
	local scene_id = Scene.Instance:GetSceneId()
	for i = 1, 2 do
		self.node_list["member_name" .. i]:SetActive(i <= #other_member_list)
		self.node_list["state" .. i]:SetActive(i <= #other_member_list)
		self.node_list["btn_add_member" .. i]:SetActive(i > #other_member_list)
	end
	local offline_count = 0
	local in_my_team_count = 0
	local online_count = 0
	local color = COLOR3B.WHITE
	local wait_to_invite_start_cross_list = {}
	local number_num = math.min(2, #other_member_list)
	for i = 1, number_num do
		color = COLOR3B.WHITE
		local data = other_member_list[i]
		if data then
			if data.scene_id == 0 then
				--离线
				self.node_list["state" .. i].text.text = Language.KuafuPVP.OffLine3
				offline_count = offline_count + 1
			elseif data.scene_id == scene_id then
				--附近
				self.node_list["state" .. i].text.text = Language.KuafuPVP.Near
				local is_my_team = SocietyWGData.Instance:GetTargetIsTeamMember(data.uid)
				if is_my_team then
					in_my_team_count = in_my_team_count + 1
				else
					table.insert(wait_to_invite_start_cross_list, data)
				end
				online_count = online_count + 1
			elseif data.scene_id ~= scene_id then
				--远离
				self.node_list["state" .. i].text.text = Language.KuafuPVP.Far
				local is_my_team = SocietyWGData.Instance:GetTargetIsTeamMember(data.uid)
				if is_my_team then
					in_my_team_count = in_my_team_count + 1
				else
					table.insert(wait_to_invite_start_cross_list, data)
				end
				online_count = online_count + 1
			end

			self.node_list["member_name" .. i].text.text = ToColorStr(data.name, color)
		end
	end

	self.wait_to_invite_start_cross_list = wait_to_invite_start_cross_list
	--都离线了不显示按钮
	if offline_count >= #other_member_list then
		-- 都不在我队伍了
		self.node_list.btn_auto_team:SetActive(false)
	else
		-- 其中一个不在我队伍了
		self.node_list.btn_auto_team:SetActive(in_my_team_count < online_count)
	end

end

--一键组队
function KF3V3PrepareLogicView:AutoTeam()
	if SocietyWGData.Instance:GetIsInTeam() == 0 then
		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
		NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
	end
	for i, v in ipairs(self.wait_to_invite_start_cross_list) do
		CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq,EnumInviteStartCrossReason.Zhandui3V3Match, v.uid)
	end
	SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.SendCreateTeamInvite)
end

function KF3V3PrepareLogicView:ClickAddMember()
	ZhanDuiWGCtrl.Instance:OpenInviteView()
end

function KF3V3PrepareLogicView:OnSceneInfoChange()
	if not self:IsOpen() then return end
	self:FlushExp()
end

function KF3V3PrepareLogicView:OnZhanDuiInfoChange()
	if not self:IsOpen() then return end
	self:FlushMemberState()
	self:FlushZhanZhanLing()
end

function KF3V3PrepareLogicView:OnTeamChange()
	if not self:IsOpen() then return end
	self:FlushMemberState()
end

function KF3V3PrepareLogicView:DanRenMatch()
	if KF3V3WGData.Instance:GetIsMatching() then
		if SocietyWGData.Instance:GetIsInTeam() ~= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MatchingCanNotRepeat)
			KF3V3WGCtrl.Instance:OpenMatchView()
		else
			--是否取消战队匹配
			self:SetCancelMatchAlert(MatchingType.Team)
		end
		return
	end
	if SocietyWGData.Instance:GetIsInTeam() == 1 then
		if SocietyWGData.Instance:GetTeamMemberCount() > 1 then
			if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
				if not self.alone_enter_alert then
					self.alone_enter_alert = Alert.New()
				end
				self.alone_enter_alert:SetLableString(Language.KuafuPVP.DanRenMatch)
				self.alone_enter_alert:SetOkString(Language.KuafuPVP.ZhanDuiBtn)
				self.alone_enter_alert:SetCancelString(Language.KuafuPVP.DanRenBtn)
				self.alone_enter_alert:SetCancelFunc(function()
					self:ExitTeamAndMatch()
				end)
				self.alone_enter_alert:SetOkFunc(function()
					KF3V3WGCtrl.Instance:ReqStartMatch()
				end)
				self.alone_enter_alert:Open()
			else
				self:ExitTeamAndMatch()
			end
		elseif SocietyWGData.Instance:GetTeamMemberCount() == 1 then
			self:ExitTeamAndMatch()
		end
	else
		KF3V3WGCtrl.Instance:ReqStartMatch()
	end
end

function KF3V3PrepareLogicView:ZhanDuiMatch()
	if KF3V3WGData.Instance:GetIsMatching() then
		if SocietyWGData.Instance:GetIsInTeam() == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MatchingCanNotRepeat)
			KF3V3WGCtrl.Instance:OpenMatchView()
		else
			--是否取消单人匹配
			self:SetCancelMatchAlert(MatchingType.Single)
		end
		return
	end

	local other_member_list = ZhanDuiWGData.Instance:GetZhanDuiOtherMemberList()
	if SocietyWGData.Instance:GetIsInTeam() == 1 then--是否在队伍中
		if SocietyWGData.Instance:GetIsTeamLeader() == 1 then--是否为队长
			if SocietyWGData.Instance:GetTeamMemberCount() == 1 then--队伍成员数判断
				self:HandleMemberCountOne()
			else
				self:HandleOtherMemberCanNot()
			end
		else
			self:HandleMemberClickMatch()
		end
	else
		self:HandleNotTeamClickZhanDuiMatch()
	end
end

function KF3V3PrepareLogicView:ExitTeamAndMatch()
	SocietyWGCtrl.Instance:SendExitTeam()
	--组队状态保存在原服，离队后会同步状态给跨服有一个延迟
	GlobalTimerQuest:AddDelayTimer(function()
		KF3V3WGCtrl.Instance:ReqStartMatch()
	end, 1)
end

-- c.队长，队伍人数=1
function KF3V3PrepareLogicView:HandleMemberCountOne()
	if not self.zhandui_enter_alert then
		self.zhandui_enter_alert = Alert.New()
	end

	self.zhandui_enter_alert:SetLableString(string.format(Language.KuafuPVP.ZhanDuiMatch2))
	self.zhandui_enter_alert:SetCancelString(Language.KuafuPVP.InvitTeamMember)
	self.zhandui_enter_alert:SetOkString(Language.KuafuPVP.StartPiPei)
	self.zhandui_enter_alert:SetCancelFunc(function()
		for i, v in ipairs(self.wait_to_invite_start_cross_list) do
			CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq, EnumInviteStartCrossReason.Zhandui3V3Match, v.uid)
		end
		SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.SendCreateTeamInvite)
	end)

	self.zhandui_enter_alert:SetOkFunc(function()
		KF3V3WGCtrl.Instance:ReqStartMatch()
	end)
	self.zhandui_enter_alert:Open()

	if not self.zhandui_enter_alert then
		self.zhandui_enter_alert = Alert.New()
	end
end

-- a.队长，存在队员离线
-- b.队长，存在队员未处于备战场景
function KF3V3PrepareLogicView:HandleOtherMemberCanNot()
	-- a.队长，存在队员离线
	-- b.队长，存在队员未处于备战场景
	local other_member_list = SocietyWGData.Instance:GetTeamOtherMemberList()
	local scene_id = Scene.Instance:GetSceneId()
	local has_other_scene = false
	local other_scene_member_list = {}
	local offline_member_list = {}
	for i, v in ipairs(other_member_list) do
		if v.is_online == 1 and v.scene_id ~= scene_id then
			has_other_scene = true
			table.insert(other_scene_member_list, v)
		elseif v.is_online == 0 then
			table.insert(offline_member_list, v)
		end
	end

	--存在离线玩家
	if #offline_member_list > 0 then
		local str = ""
		if #offline_member_list > 1 then
			str = string.format(Language.KuafuPVP.ZhanDuiMatchOffline2, offline_member_list[1].name, offline_member_list[2].name)
		elseif #offline_member_list == 1 then
			str = string.format(Language.KuafuPVP.ZhanDuiMatchOffline, offline_member_list[1].name)
		end
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	--存在其他场景
	if #other_scene_member_list > 0 then
		if not self.zhandui_enter_alert then
			self.zhandui_enter_alert = Alert.New()
		end
		if #other_scene_member_list > 1 then
			self.zhandui_enter_alert:SetLableString(string.format(Language.KuafuPVP.ZhanDuiMatchOtherScene2, other_scene_member_list[1].name, other_scene_member_list[2].name))
		elseif #other_scene_member_list == 1 then
			self.zhandui_enter_alert:SetLableString(string.format(Language.KuafuPVP.ZhanDuiMatchOtherScene, other_scene_member_list[1].name))
		end
		self.zhandui_enter_alert:SetOkString(Language.KuafuPVP.InviteGoTo)
		self.zhandui_enter_alert:SetCancelString(Language.Common.BtnCancel)
		self.zhandui_enter_alert:SetCancelFunc(function()
		end)
		self.zhandui_enter_alert:SetOkFunc(function()
			if #other_scene_member_list > 1 then
				CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq,EnumInviteStartCrossReason.Zhandui3V3Match, other_scene_member_list[1].orgin_role_id)
				CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq,EnumInviteStartCrossReason.Zhandui3V3Match, other_scene_member_list[2].orgin_role_id)
			elseif #other_scene_member_list == 1 then
				CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq,EnumInviteStartCrossReason.Zhandui3V3Match, other_scene_member_list[1].orgin_role_id)
			end
		end)
		self.zhandui_enter_alert:Open()
		return
	end

	--d.队长，不存在队员未处于备战场景，队伍人数＞1，开始战队匹配
	KF3V3WGCtrl.Instance:ReqStartMatch()
end

-- e.队员，提示“仅队长可报名战队匹配”
function KF3V3PrepareLogicView:HandleMemberClickMatch()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.OnlyTeamLeaderCanMatch)
end

-- f.没有队伍的时候点战队匹配
function KF3V3PrepareLogicView:HandleNotTeamClickZhanDuiMatch()
	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)

	if not self.zhandui_enter_alert then
		self.zhandui_enter_alert = Alert.New()
	end
	
	self.zhandui_enter_alert:SetLableString(Language.KuafuPVP.ZhanDuiMatch2)
	self.zhandui_enter_alert:SetOkString(Language.KuafuPVP.StartPiPei)
	self.zhandui_enter_alert:SetCancelString(Language.KuafuPVP.InvitTeamMember)
	self.zhandui_enter_alert:SetCancelFunc(function()
		for i, v in ipairs(self.wait_to_invite_start_cross_list) do
			CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq, EnumInviteStartCrossReason.Zhandui3V3Match, v.uid)
		end
		SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.SendCreateTeamInvite)
	end)

	self.zhandui_enter_alert:SetOkFunc(function()
		KF3V3WGCtrl.Instance:ReqStartMatch()
	end)
	self.zhandui_enter_alert:Open()
end

--取消匹配提示框
function KF3V3PrepareLogicView:SetCancelMatchAlert(show_type)
	if not self.cancel_macth_alert then
		self.cancel_macth_alert = Alert.New()
	end

	local str = ""
	if show_type == MatchingType.Single then
		str = Language.KuafuPVP.CancelDanRenMatch
	else
		str = Language.KuafuPVP.CancelZhanDuiMatch
		if SocietyWGData.Instance:GetIsTeamLeader() ~= 1 then
			str = Language.KuafuPVP.CancelZhanDuiMatch2
		end
	end
	
	self.cancel_macth_alert:SetLableString(str)
	self.cancel_macth_alert:SetOkString(Language.KuafuPVP.Confirm)
	self.cancel_macth_alert:SetCancelString(Language.KuafuPVP.Cancel)
	-- self.cancel_macth_alert:SetCancelFunc(function()

	-- end)
	self.cancel_macth_alert:SetOkFunc(function()
		if show_type == MatchingType.Single then
			KF3V3WGCtrl.Instance:ReqCancelMatch()
			GlobalTimerQuest:AddDelayTimer(function()
				self:ZhanDuiMatch()
			end, 1)
		else
			KF3V3WGCtrl.Instance:ReqCancelMatch()
			self:ExitTeamAndMatch()
		end
	end)
	self.cancel_macth_alert:Open()
end