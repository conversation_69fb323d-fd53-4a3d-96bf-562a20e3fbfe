﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Experimental_Rendering_GraphicsFormatWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(UnityEngine.Experimental.Rendering.GraphicsFormat));
		<PERSON><PERSON>("None", get_None, null);
		<PERSON><PERSON>("R8_SRGB", get_R8_SRGB, null);
		<PERSON><PERSON>("R8G8_SRGB", get_R8G8_SRGB, null);
		<PERSON><PERSON>("R8G8B8_SRGB", get_R8G8B8_SRGB, null);
		<PERSON><PERSON>("R8G8B8A8_SRGB", get_R8G8B8A8_SRGB, null);
		<PERSON><PERSON>("R8_UNorm", get_R8_UNorm, null);
		<PERSON><PERSON>("R8G8_UNorm", get_R8G8_UNorm, null);
		<PERSON><PERSON>("R8G8B8_UNorm", get_R8G8B8_UNorm, null);
		<PERSON><PERSON>("R8G8B8A8_UNorm", get_R8G8B8A8_UNorm, null);
		<PERSON><PERSON>("R8_SNorm", get_R8_SNorm, null);
		<PERSON><PERSON><PERSON><PERSON>ar("R8G8_SNorm", get_R8G8_SNorm, null);
		L.RegVar("R8G8B8_SNorm", get_R8G8B8_SNorm, null);
		L.RegVar("R8G8B8A8_SNorm", get_R8G8B8A8_SNorm, null);
		L.RegVar("R8_UInt", get_R8_UInt, null);
		L.RegVar("R8G8_UInt", get_R8G8_UInt, null);
		L.RegVar("R8G8B8_UInt", get_R8G8B8_UInt, null);
		L.RegVar("R8G8B8A8_UInt", get_R8G8B8A8_UInt, null);
		L.RegVar("R8_SInt", get_R8_SInt, null);
		L.RegVar("R8G8_SInt", get_R8G8_SInt, null);
		L.RegVar("R8G8B8_SInt", get_R8G8B8_SInt, null);
		L.RegVar("R8G8B8A8_SInt", get_R8G8B8A8_SInt, null);
		L.RegVar("R16_UNorm", get_R16_UNorm, null);
		L.RegVar("R16G16_UNorm", get_R16G16_UNorm, null);
		L.RegVar("R16G16B16_UNorm", get_R16G16B16_UNorm, null);
		L.RegVar("R16G16B16A16_UNorm", get_R16G16B16A16_UNorm, null);
		L.RegVar("R16_SNorm", get_R16_SNorm, null);
		L.RegVar("R16G16_SNorm", get_R16G16_SNorm, null);
		L.RegVar("R16G16B16_SNorm", get_R16G16B16_SNorm, null);
		L.RegVar("R16G16B16A16_SNorm", get_R16G16B16A16_SNorm, null);
		L.RegVar("R16_UInt", get_R16_UInt, null);
		L.RegVar("R16G16_UInt", get_R16G16_UInt, null);
		L.RegVar("R16G16B16_UInt", get_R16G16B16_UInt, null);
		L.RegVar("R16G16B16A16_UInt", get_R16G16B16A16_UInt, null);
		L.RegVar("R16_SInt", get_R16_SInt, null);
		L.RegVar("R16G16_SInt", get_R16G16_SInt, null);
		L.RegVar("R16G16B16_SInt", get_R16G16B16_SInt, null);
		L.RegVar("R16G16B16A16_SInt", get_R16G16B16A16_SInt, null);
		L.RegVar("R32_UInt", get_R32_UInt, null);
		L.RegVar("R32G32_UInt", get_R32G32_UInt, null);
		L.RegVar("R32G32B32_UInt", get_R32G32B32_UInt, null);
		L.RegVar("R32G32B32A32_UInt", get_R32G32B32A32_UInt, null);
		L.RegVar("R32_SInt", get_R32_SInt, null);
		L.RegVar("R32G32_SInt", get_R32G32_SInt, null);
		L.RegVar("R32G32B32_SInt", get_R32G32B32_SInt, null);
		L.RegVar("R32G32B32A32_SInt", get_R32G32B32A32_SInt, null);
		L.RegVar("R16_SFloat", get_R16_SFloat, null);
		L.RegVar("R16G16_SFloat", get_R16G16_SFloat, null);
		L.RegVar("R16G16B16_SFloat", get_R16G16B16_SFloat, null);
		L.RegVar("R16G16B16A16_SFloat", get_R16G16B16A16_SFloat, null);
		L.RegVar("R32_SFloat", get_R32_SFloat, null);
		L.RegVar("R32G32_SFloat", get_R32G32_SFloat, null);
		L.RegVar("R32G32B32_SFloat", get_R32G32B32_SFloat, null);
		L.RegVar("R32G32B32A32_SFloat", get_R32G32B32A32_SFloat, null);
		L.RegVar("B8G8R8_SRGB", get_B8G8R8_SRGB, null);
		L.RegVar("B8G8R8A8_SRGB", get_B8G8R8A8_SRGB, null);
		L.RegVar("B8G8R8_UNorm", get_B8G8R8_UNorm, null);
		L.RegVar("B8G8R8A8_UNorm", get_B8G8R8A8_UNorm, null);
		L.RegVar("B8G8R8_SNorm", get_B8G8R8_SNorm, null);
		L.RegVar("B8G8R8A8_SNorm", get_B8G8R8A8_SNorm, null);
		L.RegVar("B8G8R8_UInt", get_B8G8R8_UInt, null);
		L.RegVar("B8G8R8A8_UInt", get_B8G8R8A8_UInt, null);
		L.RegVar("B8G8R8_SInt", get_B8G8R8_SInt, null);
		L.RegVar("B8G8R8A8_SInt", get_B8G8R8A8_SInt, null);
		L.RegVar("R4G4B4A4_UNormPack16", get_R4G4B4A4_UNormPack16, null);
		L.RegVar("B4G4R4A4_UNormPack16", get_B4G4R4A4_UNormPack16, null);
		L.RegVar("R5G6B5_UNormPack16", get_R5G6B5_UNormPack16, null);
		L.RegVar("B5G6R5_UNormPack16", get_B5G6R5_UNormPack16, null);
		L.RegVar("R5G5B5A1_UNormPack16", get_R5G5B5A1_UNormPack16, null);
		L.RegVar("B5G5R5A1_UNormPack16", get_B5G5R5A1_UNormPack16, null);
		L.RegVar("A1R5G5B5_UNormPack16", get_A1R5G5B5_UNormPack16, null);
		L.RegVar("E5B9G9R9_UFloatPack32", get_E5B9G9R9_UFloatPack32, null);
		L.RegVar("B10G11R11_UFloatPack32", get_B10G11R11_UFloatPack32, null);
		L.RegVar("A2B10G10R10_UNormPack32", get_A2B10G10R10_UNormPack32, null);
		L.RegVar("A2B10G10R10_UIntPack32", get_A2B10G10R10_UIntPack32, null);
		L.RegVar("A2B10G10R10_SIntPack32", get_A2B10G10R10_SIntPack32, null);
		L.RegVar("A2R10G10B10_UNormPack32", get_A2R10G10B10_UNormPack32, null);
		L.RegVar("A2R10G10B10_UIntPack32", get_A2R10G10B10_UIntPack32, null);
		L.RegVar("A2R10G10B10_SIntPack32", get_A2R10G10B10_SIntPack32, null);
		L.RegVar("A2R10G10B10_XRSRGBPack32", get_A2R10G10B10_XRSRGBPack32, null);
		L.RegVar("A2R10G10B10_XRUNormPack32", get_A2R10G10B10_XRUNormPack32, null);
		L.RegVar("R10G10B10_XRSRGBPack32", get_R10G10B10_XRSRGBPack32, null);
		L.RegVar("R10G10B10_XRUNormPack32", get_R10G10B10_XRUNormPack32, null);
		L.RegVar("A10R10G10B10_XRSRGBPack32", get_A10R10G10B10_XRSRGBPack32, null);
		L.RegVar("A10R10G10B10_XRUNormPack32", get_A10R10G10B10_XRUNormPack32, null);
		L.RegVar("D16_UNorm", get_D16_UNorm, null);
		L.RegVar("D24_UNorm", get_D24_UNorm, null);
		L.RegVar("D24_UNorm_S8_UInt", get_D24_UNorm_S8_UInt, null);
		L.RegVar("D32_SFloat", get_D32_SFloat, null);
		L.RegVar("D32_SFloat_S8_UInt", get_D32_SFloat_S8_UInt, null);
		L.RegVar("S8_UInt", get_S8_UInt, null);
		L.RegVar("RGBA_DXT1_SRGB", get_RGBA_DXT1_SRGB, null);
		L.RegVar("RGBA_DXT1_UNorm", get_RGBA_DXT1_UNorm, null);
		L.RegVar("RGBA_DXT3_SRGB", get_RGBA_DXT3_SRGB, null);
		L.RegVar("RGBA_DXT3_UNorm", get_RGBA_DXT3_UNorm, null);
		L.RegVar("RGBA_DXT5_SRGB", get_RGBA_DXT5_SRGB, null);
		L.RegVar("RGBA_DXT5_UNorm", get_RGBA_DXT5_UNorm, null);
		L.RegVar("R_BC4_UNorm", get_R_BC4_UNorm, null);
		L.RegVar("R_BC4_SNorm", get_R_BC4_SNorm, null);
		L.RegVar("RG_BC5_UNorm", get_RG_BC5_UNorm, null);
		L.RegVar("RG_BC5_SNorm", get_RG_BC5_SNorm, null);
		L.RegVar("RGB_BC6H_UFloat", get_RGB_BC6H_UFloat, null);
		L.RegVar("RGB_BC6H_SFloat", get_RGB_BC6H_SFloat, null);
		L.RegVar("RGBA_BC7_SRGB", get_RGBA_BC7_SRGB, null);
		L.RegVar("RGBA_BC7_UNorm", get_RGBA_BC7_UNorm, null);
		L.RegVar("RGB_PVRTC_2Bpp_SRGB", get_RGB_PVRTC_2Bpp_SRGB, null);
		L.RegVar("RGB_PVRTC_2Bpp_UNorm", get_RGB_PVRTC_2Bpp_UNorm, null);
		L.RegVar("RGB_PVRTC_4Bpp_SRGB", get_RGB_PVRTC_4Bpp_SRGB, null);
		L.RegVar("RGB_PVRTC_4Bpp_UNorm", get_RGB_PVRTC_4Bpp_UNorm, null);
		L.RegVar("RGBA_PVRTC_2Bpp_SRGB", get_RGBA_PVRTC_2Bpp_SRGB, null);
		L.RegVar("RGBA_PVRTC_2Bpp_UNorm", get_RGBA_PVRTC_2Bpp_UNorm, null);
		L.RegVar("RGBA_PVRTC_4Bpp_SRGB", get_RGBA_PVRTC_4Bpp_SRGB, null);
		L.RegVar("RGBA_PVRTC_4Bpp_UNorm", get_RGBA_PVRTC_4Bpp_UNorm, null);
		L.RegVar("RGB_ETC_UNorm", get_RGB_ETC_UNorm, null);
		L.RegVar("RGB_ETC2_SRGB", get_RGB_ETC2_SRGB, null);
		L.RegVar("RGB_ETC2_UNorm", get_RGB_ETC2_UNorm, null);
		L.RegVar("RGB_A1_ETC2_SRGB", get_RGB_A1_ETC2_SRGB, null);
		L.RegVar("RGB_A1_ETC2_UNorm", get_RGB_A1_ETC2_UNorm, null);
		L.RegVar("RGBA_ETC2_SRGB", get_RGBA_ETC2_SRGB, null);
		L.RegVar("RGBA_ETC2_UNorm", get_RGBA_ETC2_UNorm, null);
		L.RegVar("R_EAC_UNorm", get_R_EAC_UNorm, null);
		L.RegVar("R_EAC_SNorm", get_R_EAC_SNorm, null);
		L.RegVar("RG_EAC_UNorm", get_RG_EAC_UNorm, null);
		L.RegVar("RG_EAC_SNorm", get_RG_EAC_SNorm, null);
		L.RegVar("RGBA_ASTC4X4_SRGB", get_RGBA_ASTC4X4_SRGB, null);
		L.RegVar("RGBA_ASTC4X4_UNorm", get_RGBA_ASTC4X4_UNorm, null);
		L.RegVar("RGBA_ASTC5X5_SRGB", get_RGBA_ASTC5X5_SRGB, null);
		L.RegVar("RGBA_ASTC5X5_UNorm", get_RGBA_ASTC5X5_UNorm, null);
		L.RegVar("RGBA_ASTC6X6_SRGB", get_RGBA_ASTC6X6_SRGB, null);
		L.RegVar("RGBA_ASTC6X6_UNorm", get_RGBA_ASTC6X6_UNorm, null);
		L.RegVar("RGBA_ASTC8X8_SRGB", get_RGBA_ASTC8X8_SRGB, null);
		L.RegVar("RGBA_ASTC8X8_UNorm", get_RGBA_ASTC8X8_UNorm, null);
		L.RegVar("RGBA_ASTC10X10_SRGB", get_RGBA_ASTC10X10_SRGB, null);
		L.RegVar("RGBA_ASTC10X10_UNorm", get_RGBA_ASTC10X10_UNorm, null);
		L.RegVar("RGBA_ASTC12X12_SRGB", get_RGBA_ASTC12X12_SRGB, null);
		L.RegVar("RGBA_ASTC12X12_UNorm", get_RGBA_ASTC12X12_UNorm, null);
		L.RegVar("YUV2", get_YUV2, null);
		L.RegVar("RGBA_ASTC4X4_UFloat", get_RGBA_ASTC4X4_UFloat, null);
		L.RegVar("RGBA_ASTC5X5_UFloat", get_RGBA_ASTC5X5_UFloat, null);
		L.RegVar("RGBA_ASTC6X6_UFloat", get_RGBA_ASTC6X6_UFloat, null);
		L.RegVar("RGBA_ASTC8X8_UFloat", get_RGBA_ASTC8X8_UFloat, null);
		L.RegVar("RGBA_ASTC10X10_UFloat", get_RGBA_ASTC10X10_UFloat, null);
		L.RegVar("RGBA_ASTC12X12_UFloat", get_RGBA_ASTC12X12_UFloat, null);
		L.RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.Experimental.Rendering.GraphicsFormat>.Check = CheckType;
		StackTraits<UnityEngine.Experimental.Rendering.GraphicsFormat>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.Experimental.Rendering.GraphicsFormat arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.Experimental.Rendering.GraphicsFormat), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_None(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.None);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8A8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8A8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8A8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8A8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8A8_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8A8_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8A8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8A8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R8G8B8A8_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8A8_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16A16_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16A16_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16A16_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16A16_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16A16_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16A16_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16A16_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16A16_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32B32_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32B32_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32B32A32_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32B32A32_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32B32_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32B32_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32B32A32_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32B32A32_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R16G16B16A16_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R16G16B16A16_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32B32_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32B32_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R32G32B32A32_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R32G32B32A32_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8A8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8A8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8A8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8A8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8A8_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8A8_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8A8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8A8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B8G8R8A8_SInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B8G8R8A8_SInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R4G4B4A4_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R4G4B4A4_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B4G4R4A4_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B4G4R4A4_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R5G6B5_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R5G6B5_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B5G6R5_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B5G6R5_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R5G5B5A1_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R5G5B5A1_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B5G5R5A1_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B5G5R5A1_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A1R5G5B5_UNormPack16(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A1R5G5B5_UNormPack16);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_E5B9G9R9_UFloatPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.E5B9G9R9_UFloatPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_B10G11R11_UFloatPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.B10G11R11_UFloatPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2B10G10R10_UNormPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2B10G10R10_UNormPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2B10G10R10_UIntPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2B10G10R10_UIntPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2B10G10R10_SIntPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2B10G10R10_SIntPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2R10G10B10_UNormPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2R10G10B10_UNormPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2R10G10B10_UIntPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2R10G10B10_UIntPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2R10G10B10_SIntPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2R10G10B10_SIntPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2R10G10B10_XRSRGBPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2R10G10B10_XRSRGBPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A2R10G10B10_XRUNormPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A2R10G10B10_XRUNormPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R10G10B10_XRSRGBPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R10G10B10_XRSRGBPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R10G10B10_XRUNormPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R10G10B10_XRUNormPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A10R10G10B10_XRSRGBPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A10R10G10B10_XRSRGBPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_A10R10G10B10_XRUNormPack32(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.A10R10G10B10_XRUNormPack32);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_D16_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.D16_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_D24_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.D24_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_D24_UNorm_S8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.D24_UNorm_S8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_D32_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.D32_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_D32_SFloat_S8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.D32_SFloat_S8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_S8_UInt(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.S8_UInt);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_DXT1_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_DXT1_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_DXT1_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_DXT1_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_DXT3_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_DXT3_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_DXT3_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_DXT3_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_DXT5_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_DXT5_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_DXT5_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_DXT5_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R_BC4_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R_BC4_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R_BC4_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R_BC4_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RG_BC5_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RG_BC5_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RG_BC5_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RG_BC5_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_BC6H_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_BC6H_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_BC6H_SFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_BC6H_SFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_BC7_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_BC7_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_BC7_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_BC7_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_PVRTC_2Bpp_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_PVRTC_2Bpp_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_PVRTC_2Bpp_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_PVRTC_2Bpp_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_PVRTC_4Bpp_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_PVRTC_4Bpp_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_PVRTC_4Bpp_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_PVRTC_4Bpp_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_PVRTC_2Bpp_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_PVRTC_2Bpp_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_PVRTC_2Bpp_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_PVRTC_2Bpp_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_PVRTC_4Bpp_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_PVRTC_4Bpp_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_PVRTC_4Bpp_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_PVRTC_4Bpp_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_ETC_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_ETC_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_ETC2_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_ETC2_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_ETC2_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_ETC2_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_A1_ETC2_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_A1_ETC2_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGB_A1_ETC2_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGB_A1_ETC2_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ETC2_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ETC2_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ETC2_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ETC2_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R_EAC_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R_EAC_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_R_EAC_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.R_EAC_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RG_EAC_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RG_EAC_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RG_EAC_SNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RG_EAC_SNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC4X4_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC4X4_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC4X4_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC4X4_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC5X5_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC5X5_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC5X5_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC5X5_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC6X6_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC6X6_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC6X6_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC6X6_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC8X8_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC8X8_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC8X8_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC8X8_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC10X10_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC10X10_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC10X10_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC10X10_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC12X12_SRGB(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC12X12_SRGB);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC12X12_UNorm(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC12X12_UNorm);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_YUV2(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.YUV2);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC4X4_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC4X4_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC5X5_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC5X5_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC6X6_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC6X6_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC8X8_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC8X8_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC10X10_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC10X10_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RGBA_ASTC12X12_UFloat(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Experimental.Rendering.GraphicsFormat.RGBA_ASTC12X12_UFloat);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.Experimental.Rendering.GraphicsFormat o = (UnityEngine.Experimental.Rendering.GraphicsFormat)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

