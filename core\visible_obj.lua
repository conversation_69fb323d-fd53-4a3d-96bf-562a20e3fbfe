VisibleObj = VisibleObj or BaseClass()

local develop_mode = require("editor/develop_mode")
local is_develop = UNITY_EDITOR or IS_LOCLA_WINDOWS_DEBUG_EXE or develop_mode:IsDeveloper()

function VisibleObj:__init()
	self.shield_obj_type = ShieldObjType.InValid
	self.shield_rule_list = nil
	self.is_deleted = false
	self.need_calculate_priortiy = false
	self.scene_appear_priority = SceneAppearPriority.Middle
	self.quality_level_offset = 0
	self.refresh_priortiy_time_stamp = 0

	if is_develop then
		self.init_traceback = debug.traceback()
	end
end

function VisibleObj:__delete()
	if self.is_deleted then
		return
	end
	self.is_deleted = true

	if is_develop then
		self.delete_traceback = debug.traceback()
	end

	if ShieldManager.Instance and self.shield_handle then
        ShieldManager.Instance:UnRegisterObj(self.shield_handle)
        self.shield_handle = nil
    end

    if self.shield_rule_list then
	    for _, rule in pairs(self.shield_rule_list) do
	    	rule:DeleteMe()
	    end
    	self.shield_rule_list = nil
	end

    self.shield_rule = nil
    self.vo = nil
end

function VisibleObj:IsDeleted()
	return self.is_deleted
end

function VisibleObj:CreateShieldHandle()
	if is_develop then
		self.call_create_handle = true
		if self.is_deleted or nil == ShieldManager.Instance then
			self.create_handle_error_traceback = string.format("is_deleted:%s ShieldManager is nil:%s log:%s",
				self.is_deleted, nil == ShieldManager.Instance, debug.traceback())
		end
	end

	if self.is_deleted then
		return
	end

	if ShieldManager.Instance and nil == self.shield_handle then
		self.shield_handle = ShieldManager.Instance:RegisterObj(self.shield_obj_type, self)
	end
end

function VisibleObj:SetVisible(is_visible)
	if is_develop then
		local info = debug.getinfo(2, "Sln")
		if "quality/shield_object_handle" ~= info.short_src then
			print_error("不允许外部直接调用SetVisible！")
		end
	end

	if self.is_deleted then
		return
	end

	is_visible = is_visible or false
	if self.is_visible ~= is_visible then
		self.is_visible = is_visible
		self:VisibleChanged(is_visible)
	end
end

function VisibleObj:GetVisiable()
	return self.is_visible
end

function VisibleObj:RefreshVisiable()
	if self.shield_handle then
		self.shield_handle:RefreshVisible()
	end
end

function VisibleObj:NeedCalculatePriortiy()
	return self.need_calculate_priortiy
end

function VisibleObj:UpdatePriority()
	if self.refresh_priortiy_time_stamp <= GlobalUnityTime then
		self.refresh_priortiy_time_stamp = GlobalUnityTime + 1
		local scene_priority = self:CalculatePriortiy()
		self:SetScenePriority(scene_priority)
	end
end

function VisibleObj:AddShieldRule(weight, shield_func)
	if self.is_deleted then
		return
	end

	local rule = nil
	if self.shield_handle then
		rule = SimpleRule.New(nil, weight, shield_func)
		self.shield_rule_list = self.shield_rule_list or {}
		self.shield_rule_list[rule] = rule
		self.shield_handle:AddRule(rule)
		self.shield_handle:RefreshVisible()
	else
		print_error("shield_handle is nil")
		if is_develop then
			if not self.call_create_handle then
				print_error("没有调用CreateShieldHandle")
			elseif nil ~= self.create_handle_error_traceback then
				print_error(self.create_handle_error_traceback)
			end

			if self.init_traceback then
				print_error("init_traceback: ", self.init_traceback)
			end

			if self.delete_traceback then
				print_error("delete_traceback: ", self.delete_traceback)
			end
		end
	end

	return rule
end

function VisibleObj:RemoveShieldRule(rule)
	if nil == rule or self.is_deleted then
		return
	end

	if self.shield_rule_list then
		self.shield_rule_list[rule] = nil
	end

	if self.shield_handle then
		self.shield_handle:RemoveRule(rule)
		rule:DeleteMe()
		self.shield_handle:RefreshVisible()
	else
		print_error("shield_handle is nil")
		if is_develop then
			if not self.call_create_handle then
				print_error("没有调用CreateShieldHandle")
			elseif nil ~= self.create_handle_error_traceback then
				print_error(self.create_handle_error_traceback)
			end

			if self.init_traceback then
				print_error("init_traceback: ", self.init_traceback)
			end

			if self.delete_traceback then
				print_error("delete_traceback: ", self.delete_traceback)
			end
		end
	end
end

function VisibleObj:AddShieldRuleByRule(rule)
	if rule == nil then
		return
	end

	self.shield_rule_list = self.shield_rule_list or {}

	if self.shield_rule_list[rule] ~= nil then
		return
	end

	if self.shield_handle then
		self.shield_rule_list[rule] = rule
		self.shield_handle:AddRule(rule)
		self.shield_handle:RefreshVisible()
	else
		print_error("shield_handle is nil")
	end
end

function VisibleObj:RemoveShieldRuleNoDel(rule)
	if nil == rule or self.is_deleted then
		return
	end

	if nil == self.shield_rule_list or self.shield_rule_list[rule] == nil then
		return
	end

	self.shield_rule_list[rule] = nil
	if self.shield_handle then
		self.shield_handle:RemoveRule(rule)
		self.shield_handle:RefreshVisible()
	else
		print_error("shield_handle is nil")
	end
end

local function ForceShowFunc()
	return false
end

local function ForceHideFunc()
	return true
end

function VisibleObj:ForceSetVisible(visible)
	self:CancelForceSetVisible()
	if visible then
		self.shield_rule = self:AddShieldRule(ShieldRuleWeight.High, ForceShowFunc)
	else
		self.shield_rule = self:AddShieldRule(ShieldRuleWeight.High, ForceHideFunc)
	end
end

function VisibleObj:CancelForceSetVisible()
	if self.shield_rule then
		self:RemoveShieldRule(self.shield_rule)
		self.shield_rule = nil
	end
end

function VisibleObj:RefreshPriority()
	if self.shield_handle then
		self.shield_handle:RefreshPriority()
	end
end

function VisibleObj:SetScenePriority(scene_priority)
	if self.scene_appear_priority ~= scene_priority then
		self.scene_appear_priority = scene_priority or SceneAppearPriority.Middle
		self:RefreshPriority()
	end
end

function VisibleObj:GetSceneAppearPriority()
	return self.scene_appear_priority
end

function VisibleObj:VisibleChanged(visible)
	-- override
end

function VisibleObj:CalculatePriortiy()
	return SceneAppearPriority.Middle
end

function VisibleObj:SetQualityLevelOffset(offset)
	self.quality_level_offset = offset
	self:RefreshVisiable()
end

function VisibleObj:GetQualityLevelOffset()
	return self.quality_level_offset
end