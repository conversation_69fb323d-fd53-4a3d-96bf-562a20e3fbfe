ZCResultView = ZCResultView or BaseClass(SafeBaseView)
ZCResultView.FromView = {
    YZWC = 10,
}

function ZCResultView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true, true)
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
    self:AddViewResource(0, "uis/view/zc_result_ui_prefab", "layout_end_result_view")
    self.my_reward_list = {}
end

function ZCResultView:__delete()

end

function ZCResultView:ReleaseCallBack()
    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

    if self.my_reward_list then
        for i, v in pairs(self.my_reward_list) do
            v:DeleteMe()
        end
        self.my_reward_list = {}
    end

    if CountDownManager.Instance:HasCountDown("zc_result_view") then
        CountDownManager.Instance:RemoveCountDown("zc_result_view")
    end
end

function ZCResultView:LoadCallBack()
    self.node_list["btn_ok"].button:AddClickListener(BindTool.Bind(self.OnClickBtnOk, self))
    self.rank_list = AsyncListView.New(ZCResultRender, self.node_list["rank_list"])
    XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind(self.Close, self))
    
    self.node_list.victory:SetActive(true)
    self.node_list.lose:SetActive(false)
	local bundle, asset = ResPath.GetCommonPanel("a3_gxhd_lbl_pm")
    self.node_list["title_img"].image:LoadSprite(bundle, asset, function()
        self.node_list["title_img"].image:SetNativeSize()
    end)
end

function ZCResultView:ShowIndexCallBack()
    if CountDownManager.Instance:HasCountDown("zc_result_view") then
        CountDownManager.Instance:RemoveCountDown("zc_result_view")
    end
    self:UpdateCount(0, 20)
    CountDownManager.Instance:AddCountDown("zc_result_view", BindTool.Bind(self.UpdateCount, self),
        BindTool.Bind(self.CompleteCount, self),nil, 10, 1)
end

function ZCResultView:UpdateCount(el_time, to_time)
    local time = math.ceil(to_time - el_time)
    self.node_list["close_text"].text.text = string.format(Language.Common.AutoCloseTimerTxt, time)
end

function ZCResultView:CompleteCount()
    self:Close()
end

function ZCResultView:OnClickBtnOk()
    self:Close()
end

function ZCResultView:OnFlush(param)
    for i, v in pairs(param) do
        if i == ZCRankView.FromView.YZWC then
            self:FlushYeZhanWangCheng(v.stage)
        end
    end
end

function ZCResultView:FlushYeZhanWangCheng(stage)
    if stage == nil then
        return
    end

    local cur_turn_info = KuafuYeZhanWangChengWGData.Instance:GetResultScoreInfo(stage)
    -- self.node_list.victory:SetActive(cur_turn_info.is_win ~= 0)
    -- self.node_list.lose:SetActive(cur_turn_info.is_win == 0)
    local rank_list = KuafuYeZhanWangChengWGData.Instance:GetResultInfoByStage(stage)
    self.rank_list:SetDataList(rank_list)
    local self_info1 = KuafuYeZhanWangChengWGData.Instance:GetSetSelfInfo()
    local self_info2 = self:GetRewardRoleInfoByUserKey(self_info1.user_key, rank_list)

    local role_info = RoleWGData.Instance:GetRoleInfo()

    if not IsEmptyTable(self_info2) then
        --[[ MVP
        local str
        self.node_list["self_mvp"]:SetActive(self_info2.mvp == 1)
        if self_info2.mvp == 1 then
            str = self_info2.is_red_side == 1 and "a2_zzjd_mvp1" or "a2_zzjd_mvp2"
            --self.node_list["self_mvp"].rect.localPosition = Vector2(-516, 5)
        else
            str = self_info2.is_red_side == 1 and "a2_zzjd_mvp1" or "a2_zzjd_mvp2"
            --self.node_list["self_mvp"].rect.localPosition = Vector2(-516, 5)
        end
        local bundle, asset = ResPath.GetCommonIcon(str)
        self.node_list["self_mvp"].image:LoadSprite(bundle, asset, function()
            self.node_list["self_mvp"].image:SetNativeSize()
        end)
        ]]
        if self_info2.rank < 4 then
            self.node_list["self_rank"].text.text = ""
            self.node_list["self_img_rank"]:SetActive(true)
            self.node_list["self_img_rank"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self_info2.rank))
        else
            self.node_list["self_img_rank"]:SetActive(false)
            self.node_list["self_rank"].text.text = self_info2.rank
        end
        self.node_list["self_name"].text.text = role_info.name
        --self.node_list["self_guild_name"].text.text = role_info.guild_name    旧帮派名字
        self.node_list["self_guild_name"].text.text = self_info2.capability
        self.node_list["self_kill_num"].text.text = self_info2.kill_role_num
        self.node_list["self_assist_num"].text.text = self_info2.assist_role_num
        self.node_list["self_dead_num"].text.text = self_info2.die_num
        self.node_list["self_score"].text.text = self_info2.score

        if not IsEmptyTable(self_info2.reward_list) then
            table.sort(self_info2.reward_list, function (a,b)
                if a and b and a.item_id and b.item_id then
                    local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
                    local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
                    return a_cfg.color > b_cfg.color
                end
                return false
            end)
        end
        self:SetMyReward(self_info2.reward_list or {})
    end

    self.node_list["red_score"].text.text = string.format(Language.YeZhanWangCheng.RedScore, (cur_turn_info.red_score or 0))
    self.node_list["blue_score"].text.text = string.format(Language.YeZhanWangCheng.BlueScore, (cur_turn_info.blue_score or 0))
    self.node_list["yellow_score"].text.text = string.format(Language.YeZhanWangCheng.YellowScore, (cur_turn_info.yellow_score or 0))
    -- if cur_turn_info.is_win == 1 then
    --     self.node_list["self_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_zsl"))
    -- else
    --     self.node_list["self_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_zsb"))
    -- end
end

function ZCResultView:SetMyReward(list)
    for i = 1, #list do
        if not self.my_reward_list[i] then
            self.my_reward_list[i] = ItemCell.New(self.node_list["self_reward_list"])
        end

        self.my_reward_list[i]:SetActive(true)
        self.my_reward_list[i]:SetData(list[i])
    end

    for i = #list + 1, #self.my_reward_list do
        self.my_reward_list[i]:SetActive(false)
    end
end

function ZCResultView:GetRewardRoleInfoByUserKey(user_key, list)
    for i, v in pairs(list) do
        if v.user_key == user_key then
            return v
        end
    end
    return KuafuYeZhanWangChengWGData.Instance:GetRewardRoleInfoByUserKey(user_key)
end

-----------------------------------------------------------------------------------------------------------------
ZCResultRender = ZCResultRender or BaseClass(BaseRender)
function ZCResultRender:__init()
    self.reward_list = {}
end

function ZCResultRender:__delete()
    if self.reward_list then
        for i, v in pairs(self.reward_list) do
            v:DeleteMe()
        end
        self.reward_list = {}
    end
end

function ZCResultRender:OnFlush()
    if not self.data then
        return
    end
    if self.data.rank < 4 then
        self.node_list["img_rank"]:SetActive(true)
        self.node_list["bg_rank"]:SetActive(true)
        self.node_list["img_rank"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
        self.node_list["bg_rank"].image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. self.index))
        self.node_list["rank"].text.text = ""
    else
        self.node_list["img_rank"]:SetActive(false)
        self.node_list["bg_rank"]:SetActive(false)
        self.node_list["rank"].text.text = self.data.rank
    end

    --[[ mvp
    local str
    self.node_list["mvp_flag"]:SetActive(self.data.mvp == 1)
    if self.data.mvp == 1 then
        str = self.data.is_red_side == 1 and "a2_zzjd_mvp1" or "a2_zzjd_mvp2"
        --self.node_list["mvp_flag"].rect.anchoredPosition = Vector2(56, -30)
    else
        str = self.data.is_red_side == 1 and "a2_zzjd_mvp1" or "a2_zzjd_mvp2"
        --self.node_list["mvp_flag"].rect.anchoredPosition = Vector2(56, -30)
    end
    local bundle, asset = ResPath.GetCommonIcon(str)
    self.node_list["mvp_flag"].image:LoadSprite(bundle, asset, function()
        self.node_list["mvp_flag"].image:SetNativeSize()
    end)
    ]]
    
    local name = BiZuoWGData.Instance:GetSetverNameFormat(self.data.user_name)
    self.node_list["name"].text.text = name
    --self.node_list["guild_name"].text.text = self.data.guild_name 旧帮派名字
    self.node_list["guild_name"].text.text = self.data.capability
    self.node_list["kill_num"].text.text = self.data.kill_role_num
    self.node_list["assist_num"].text.text = self.data.assist_role_num
    self.node_list["dead_num"].text.text = self.data.die_num
    self.node_list["score"].text.text = self.data.score

    if not IsEmptyTable(self.data.reward_list) then
        table.sort( self.data.reward_list, function (a,b)
            if a and b and a.item_id and b.item_id then
                local a_cfg = ItemWGData.Instance:GetItemConfig(a.item_id)
                local b_cfg = ItemWGData.Instance:GetItemConfig(b.item_id)
                return a_cfg.color > b_cfg.color
            end
            return false
        end)
    end

    self:SetMyReward(self.data.reward_list or {})
end

function ZCResultRender:SetMyReward(list)
    for i = 1, #list do
        if not self.reward_list[i] then
            self.reward_list[i] = ItemCell.New(self.node_list["reward_list"])
        end

        self.reward_list[i]:SetActive(true)
        self.reward_list[i]:SetData(list[i])
    end

    for i = #list + 1, #self.reward_list do
        self.reward_list[i]:SetActive(false)
    end
end
