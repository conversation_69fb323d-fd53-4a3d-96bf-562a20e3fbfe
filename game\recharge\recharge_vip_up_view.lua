VipUpView = VipUpView or BaseClass(SafeBaseView)

function VipUpView:__init()
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_vip_uplevel")
end

function VipUpView:ReleaseCallBack()
	GlobalTimerQuest:CancelQuest(self.close_timer)
	self:CancelTween()
end

function VipUpView:LoadCallBack()
	self.close_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
		end, 5)
end

function VipUpView:CloseCallBack()
	GlobalTimerQuest:CancelQuest(self.close_timer)
end

function VipUpView:OnFlush(param_t)
	self:FlushVipLevel()
	self:PlayOpenTween()
	self:LoadVipUpEffect()
end

function VipUpView:FlushVipLevel()
	-- local is_vip = VipWGData.Instance:IsVip()
	local vip_level = VipWGData.Instance:GetVipLevel()
	self.node_list.vip_level_text.tmp.text = vip_level
end

function VipUpView:PlayOpenTween()
	self.node_list.vip_level_node.canvas_group.alpha = 0
	UITween.DoUpDownCrashTween(self.node_list.vip_bg)
	local tween = self.node_list.vip_level_node.canvas_group:DoAlpha(0, 1, 0.5)
	tween:SetEase(DG.Tweening.Ease.OutCubic)

	self:CancelTween()
	local sequence = DG.Tweening.DOTween.Sequence()
	sequence:Append(tween)
	sequence:AppendInterval(1)
	sequence:AppendCallback(function ()
		self:Close()
	end)

	self.sequence = sequence
end

function VipUpView:CancelTween()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
end

function VipUpView:LoadVipUpEffect()
	--[[
	-- local is_vip = VipWGData.Instance:IsVip()
	local effect_root = self.node_list.active_eff.transform
	local bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_Vip_up_jingse)
	local async_loader = AllocAsyncLoader(self, "vip_up_effect")
	async_loader:SetParent(effect_root)
	async_loader:SetIsUseObjPool(true)
	async_loader:Load(bundle, asset,
		function (obj)
			local spine_obj = obj.transform:Find("UI_guizu_shengji_jinse/active_eff")
			local spine_ani = spine_obj and spine_obj.gameObject:GetComponent("SkeletonGraphic")
			if spine_ani and spine_ani.AnimationState then
				spine_ani.AnimationState:SetAnimation(0, "animation", false)
				
			end
		end)
	]]
	local bundle, asset = ResPath.GetEffectUi("ui_jinjichenggong")
    EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list.active_eff.transform, 1,
            nil, nil, Vector3(1, 1, 1))
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Levelup))
end