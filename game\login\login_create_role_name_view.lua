LoginCreateRoleNameView = LoginCreateRoleNameView or BaseClass(SafeBaseView)

function LoginCreateRoleNameView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_login_select_name")

	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self.view_cache_time = 0
end

function LoginCreateRoleNameView:__delete()
end

--{prof = 0, sex = 0， diy_appearance_info = {}, country = 1}
function LoginCreateRoleNameView:SetDataAndOpen(data)
    if not data then
        return
    end

    self.data = data
    self:Open()
end

function LoginCreateRoleNameView:OpenCallBack()

end

function LoginCreateRoleNameView:CloseCallBack()

end

function LoginCreateRoleNameView:LoadCallBack()
	self:InitRanDomName()
	self.input_name = self.node_list["name_input"]
	self.input_name.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputNameChange, self))

	XUI.AddClickEventListener(self.node_list["crap_btn"], BindTool.Bind(self.OnCrapsClick, self))
	XUI.AddClickEventListener(self.node_list["sure_btn"], BindTool.Bind(self.OnClickCreateRole, self))
end

function LoginCreateRoleNameView:ReleaseCallBack()
	self.input_name = nil
	self.data = nil
end


function LoginCreateRoleNameView:ShowIndexCallBack()
end

function LoginCreateRoleNameView:OnFlush()
	if not self.data then
		return
	end

	self:RandomName()
end

function LoginCreateRoleNameView:InitRanDomName()
    self.re_count = 0
	self.first_list = {}
	self.last_list = {}

	local name_cfg = ConfigManager.Instance:GetAutoConfig("randname_auto").random_name[1]
	if not name_cfg then
        return
    end

	local the_list_1 = {}
	local the_list_2 = {}
	the_list_1[GameEnum.FEMALE] = name_cfg.common_first
	the_list_2[GameEnum.FEMALE] = name_cfg.female_last
	the_list_1[GameEnum.MALE] = name_cfg.common_first
	the_list_2[GameEnum.MALE] = name_cfg.male_last

	self.first_list = __TableCopy(the_list_1)
	self.last_list = __TableCopy(the_list_2)
end

function LoginCreateRoleNameView:OnCrapsClick()
	self:RandomName()
end

function LoginCreateRoleNameView:RandomName()
    local sex = self.data.sex or 0
	local first_list = self.first_list[sex]
	local last_list = self.last_list[sex]

	if nil == first_list or nil == last_list then
		return
	end

	local first_index = math.random(1, #first_list)
	local last_index = math.random(1, #last_list)
	local name = first_list[first_index] .. last_list[last_index]
	local isill = ChatFilter.Instance:IsIllegal(name, true)
	-- 存在敏感字
	if isill and self.re_count <= 7 then
		self.re_count = self.re_count + 1
		table.remove(first_list, first_index)
		table.remove(last_list, last_index)
		self:RandomName()
	else
		self.input_name.input_field.text = name
		self.re_count = 0
	end
end

-- 名字输入
function LoginCreateRoleNameView:OnInputNameChange()
	local role_name = self.input_name.input_field.text
	if role_name == "" then
		return
	end
	
	if ChatFilter.IsEmoji(role_name) then
		self.input_name.input_field.text = ""
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
	end
end

-- 创建角色按钮
function LoginCreateRoleNameView:OnClickCreateRole()
	if not self.data then
		return
	end

	local role_name = self.input_name.input_field.text
	if role_name == "" then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent)
		return
	end

	if ChatFilter.Instance:IsIllegal(role_name, true) or ChatFilter.IsEmoji(role_name) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
		return
	end

	local name_length_ch = CheckCharactersHaveCh(role_name)
	local name_length_ev = CheckCharactersHaveBigEV(role_name)
    --一个汉字长度是3,但是策划要把它当成2来处理
    --一个大写英文长度是1,但是策划要把它当成2来处理
	if string.len(role_name) + name_length_ev - name_length_ch > 12 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent3)
		return
	end

    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.clickLoginEnterGame)
	LoginWGCtrl.SendCreateRole(role_name, self.data.prof, self.data.sex, self.data.country, self.data.diy_appearance_info)
end