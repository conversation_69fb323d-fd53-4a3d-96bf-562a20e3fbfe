MarryTitleView = MarryTitleView or BaseClass(SafeBaseView)

function MarryTitleView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true,true)

	--self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_jiehun_title_panel")
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_title")

	self.cur_select_index = 1
	self.marry_title_cfg = {}
end

function MarryTitleView:__delete()
end

function MarryTitleView:ReleaseCallBack()
	if self.marry_title_list then
		self.marry_title_list:DeleteMe()
		self.marry_title_list = nil
	end
	self.marry_title_cfg = nil
end

function MarryTitleView:LoadCallBack()
	--self.node_list["text_view_name"].text.text = Language.Marry.ViewNameXianLvChengHao
	--local ph = self.node_list["ph_title_list"]
	--self.marry_title_list = AsyncListView.New(MarryTitleItemRender, ph)
	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind(self.OnMarryTitleLingqu, self))
	XUI.AddClickEventListener(self.node_list.img_title_icon, BindTool.Bind(self.OpenItemTips, self))
	self.marry_title_cfg = MarryWGData.Instance:GetMarryTitleCfg()
	--初始化列表
	if self.marry_title_list == nil then
		local bundle, asset = "uis/view/marry_ui_prefab", "ph_marry_title_item"
		self.marry_title_list = AsyncBaseGrid.New()
		self.marry_title_list:CreateCells(
			{col = 2,
			change_cells_num = 1,
			--cell_count = #marry_title_cfg,
			list_view = self.node_list.ph_title_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = MarryTitleItemRender}
		)
		self.marry_title_list:SetStartZeroIndex(false)
		self.marry_title_list:SetSelectCallBack(BindTool.Bind(self.OnClickMarryTitle, self))
		self.marry_title_list:SetSelectCellIndex(1)
	end
end

function MarryTitleView:OnFlush()
	self.marry_title_cfg = MarryWGData.Instance:GetMarryTitleCfg()
	if self.marry_title_cfg and self.marry_title_list then
		self.marry_title_list:SetDataList(self.marry_title_cfg, 0)
		self:FlushLeft()
	end
end

function MarryTitleView:OnClickMarryTitle(cell)
	self.cur_select_index = cell:GetIndex()
	self:FlushLeft()
end

function MarryTitleView:FlushLeft()
	local data = self.marry_title_cfg[self.cur_select_index]
	if not data then return end
	-- 亲密度
	local condition_3 = data.intimacy >= data.need_intimacy
	local color_vale = condition_3 and COLOR3B.L_GREEN or COLOR3B.L_RED
	self.node_list["rich_condition_3"].text.text = string.format(Language.Marry.MarryTitleIntimacy, color_vale, data.intimacy, data.need_intimacy)
	-- 等级
	local role_equip_level = MarryWGData.Instance:GetMarryEquipLevel()
	local condition_1 = role_equip_level >= data.need_equip_level
    local need_str = string.format(Language.Marry.JieShuAndStar, math.floor(data.need_equip_level / 10), 10)
	local color_tip = condition_1 and COLOR3B.L_GREEN or COLOR3B.L_RED
	self.node_list["rich_condition_1"].text.text = string.format(Language.Marry.MarryTitleEquip, color_tip, need_str)
	-- 激活
	self.node_list["rich_condition_2"]:SetActive(data.baby_limit ~= 2)
	local condition_2 = true
	if data.baby_limit ~= 2 then
		local baby_name_cfg = MarryWGData.Instance:GetBabyNameCfg(data.baby_limit, data.baby_id)
		local baby_flag = MarryWGData.Instance:GetBabyListFlag(data.baby_limit)
		condition_2 = baby_flag[32 - data.baby_id] == 1
		local color_flag = condition_2 and COLOR3B.L_GREEN or COLOR3B.L_RED
        self.node_list["rich_condition_2"].text.text = string.format(Language.Marry.MarryTitleBaby, color_flag, baby_name_cfg.baby_name)
	end

	local enable = condition_1 and condition_2 and condition_3
	self.node_list.img_locked:SetActive(not enable)
	self.node_list.btn_lingqu:SetActive(data.title_flag == 0 and enable)
	self.node_list.img_unlocked:SetActive(data.title_flag == 1)
	local bundle, asset = ResPath.GetTitle(data.title_show)
	self.node_list["img_title_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list.img_title_icon.image:SetNativeSize()
    end)
end

function MarryTitleView:OnMarryTitleLingqu()
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_FETCH_LOVER_TITLE, self.marry_title_cfg[self.cur_select_index].index)
	AudioService.Instance:PlayRewardAudio()
end

function MarryTitleView:OpenItemTips()
	TipWGCtrl.Instance:OpenItem({item_id = self.marry_title_cfg[self.cur_select_index].reward_itemid}, ItemTip.FROM_NORMAL, nil)
end

----MarryTitleItemRender
MarryTitleItemRender = MarryTitleItemRender or BaseClass(BaseRender)
function MarryTitleItemRender:__init()
end

function MarryTitleItemRender:__delete()
end

function MarryTitleItemRender:LoadCallBack()
	--self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind(self.OnMarryTitleLingqu, self))
	--self.node_list["img_title_icon"].button:AddClickListener(BindTool.Bind(self.OpenItemTips, self))
end

function MarryTitleItemRender:OnFlush()
	if not self.data then return end
	local condition_3 = self.data.intimacy >= self.data.need_intimacy
	local role_equip_level = MarryWGData.Instance:GetMarryEquipLevel()
	local condition_1 = role_equip_level >= self.data.need_equip_level
	local baby_flag = MarryWGData.Instance:GetBabyListFlag(self.data.baby_limit)
	local condition_2 = self.data.baby_limit == 2 or baby_flag[32 - self.data.baby_id] == 1
	local enable = condition_1 and condition_2 and condition_3
	self.node_list.title_remind:SetActive(self.data.title_flag == 0 and condition_1 and condition_2 and condition_3)
	self.node_list.mask_locked:SetActive(self.data.title_flag == 0)
	local bundle, asset = ResPath.GetTitle(self.data.title_show)
	self.node_list["img_title_icon"].image:LoadSprite(bundle, asset, function()
        self.node_list.img_title_icon.image:SetNativeSize()
    end)
end

-- 选择状态改变
function MarryTitleItemRender:OnSelectChange(is_select)
	self.node_list.img_highlight:SetActive(is_select)
end

-- function MarryTitleItemRender:OnMarryTitleLingqu()
-- 	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_FETCH_LOVER_TITLE, self.data.index)
-- 	AudioService.Instance:PlayRewardAudio()
-- end

function MarryTitleItemRender:CreateSelectEffect()

end

-- function MarryTitleItemRender:OpenItemTips()
-- 	TipWGCtrl.Instance:OpenItem({item_id = self.data.reward_itemid}, ItemTip.FROM_NORMAL, nil)
-- end