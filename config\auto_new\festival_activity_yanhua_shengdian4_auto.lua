-- J-节日活动-山海绘卷.xls
local item_table={
[1]={item_id=29697,num=1,is_bind=1},
[2]={item_id=29698,num=1,is_bind=1},
[3]={item_id=29699,num=1,is_bind=1},
[4]={item_id=37273,num=1,is_bind=1},
[5]={item_id=26380,num=1,is_bind=1},
[6]={item_id=26363,num=1,is_bind=1},
[7]={item_id=26378,num=1,is_bind=1},
[8]={item_id=26379,num=1,is_bind=1},
[9]={item_id=26361,num=1,is_bind=1},
[10]={item_id=26362,num=1,is_bind=1},
[11]={item_id=26502,num=1,is_bind=1},
[12]={item_id=26517,num=1,is_bind=1},
[13]={item_id=26500,num=1,is_bind=1},
[14]={item_id=26515,num=1,is_bind=1},
[15]={item_id=22531,num=1,is_bind=1},
[16]={item_id=26127,num=1,is_bind=1},
[17]={item_id=26126,num=1,is_bind=1},
[18]={item_id=26125,num=1,is_bind=1},
[19]={item_id=26415,num=2,is_bind=1},
[20]={item_id=22010,num=1,is_bind=1},
[21]={item_id=26200,num=3,is_bind=1},
[22]={item_id=26203,num=3,is_bind=1},
[23]={item_id=26352,num=2,is_bind=1},
[24]={item_id=26353,num=1,is_bind=1},
[25]={item_id=26354,num=1,is_bind=1},
[26]={item_id=26376,num=2,is_bind=1},
[27]={item_id=26377,num=1,is_bind=1},
[28]={item_id=44182,num=1,is_bind=1},
[29]={item_id=44183,num=1,is_bind=1},
[30]={item_id=44184,num=1,is_bind=1},
[31]={item_id=44185,num=1,is_bind=1},
[32]={item_id=26197,num=10,is_bind=1},
[33]={item_id=26197,num=30,is_bind=1},
[34]={item_id=26197,num=50,is_bind=1},
[35]={item_id=44495,num=1,is_bind=1},
[36]={item_id=44496,num=1,is_bind=1},
[37]={item_id=29696,num=1,is_bind=1},
[38]={item_id=26197,num=100,is_bind=1},
}

return {
config_param={
{}
},

config_param_meta_table_map={
},
grade={
{}
},

grade_meta_table_map={
},
consume={
{},
{consume=2,},
{consume=3,}
},

consume_meta_table_map={
},
reward={
{reward_type=1,player_guarantee=1000,guarantee_reward_limit=1,broadcast=1,reward_show=1,reward_big_show=3,},
{reward_id=2,reward_item=item_table[1],guarantee_reward_limit=10,},
{reward_id=3,reward_item=item_table[2],},
{reward_id=4,reward_item=item_table[3],},
{reward_id=5,reward_item=item_table[4],player_guarantee=1000,guarantee_reward_limit=10,},
{reward_id=6,reward_item=item_table[5],},
{reward_id=7,reward_item=item_table[6],reward_type=1,player_guarantee=100,guarantee_reward_limit=30,broadcast=1,reward_show=1,},
{reward_id=8,reward_item=item_table[7],},
{reward_id=9,reward_item=item_table[8],},
{reward_id=10,reward_item=item_table[9],},
{reward_id=11,reward_item=item_table[10],},
{reward_id=12,reward_item=item_table[11],reward_type=2,reward_show=1,},
{reward_id=13,reward_item=item_table[12],},
{reward_id=14,reward_item=item_table[13],reward_type=2,},
{reward_id=15,reward_item=item_table[14],},
{reward_id=16,reward_item=item_table[15],},
{reward_id=17,reward_item=item_table[16],},
{reward_id=18,reward_item=item_table[17],},
{reward_id=19,reward_item=item_table[18],},
{reward_id=20,reward_item=item_table[19],},
{reward_id=21,reward_item=item_table[20],},
{reward_id=22,reward_item=item_table[21],},
{reward_id=23,reward_item=item_table[22],},
{reward_id=24,reward_item=item_table[23],},
{reward_id=25,reward_item=item_table[24],},
{reward_id=26,reward_item=item_table[25],},
{reward_id=27,reward_item=item_table[26],},
{reward_id=28,reward_item=item_table[27],},
{reward_id=29,reward_item=item_table[28],},
{reward_id=30,reward_item=item_table[29],},
{reward_id=31,reward_item=item_table[30],},
{reward_id=32,reward_item=item_table[31],}
},

reward_meta_table_map={
[13]=14,	-- depth:1
[11]=12,	-- depth:1
[10]=12,	-- depth:1
[9]=12,	-- depth:1
[8]=12,	-- depth:1
[6]=7,	-- depth:1
[5]=7,	-- depth:1
[4]=1,	-- depth:1
[3]=4,	-- depth:2
[2]=1,	-- depth:1
},
rebate={
{reward_item={[0]=item_table[32]},},
{index=2,lotto_num=300,reward_item={[0]=item_table[33]},},
{index=3,lotto_num=600,reward_item={[0]=item_table[34]},},
{index=4,lotto_num=1200,},
{index=5,lotto_num=2500,},
{index=6,lotto_num=3500,reward_item={[0]=item_table[35]},},
{index=7,lotto_num=5000,reward_item={[0]=item_table[36]},}
},

rebate_meta_table_map={
},
item_random_desc={
{random_count=0.01,},
{number=2,item_name="蛇尾毒匕",item_id=29697,random_count=0.02,},
{number=3,item_name="嗜血环镖",item_id=29698,random_count=0.03,},
{number=4,item_name="烈火强弩",item_id=29699,random_count=0.13,},
{number=5,item_name="炽羽流焰",item_id=37273,random_count=0.15,},
{number=6,item_name="诛剑石",item_id=26380,random_count=0.31,},
{number=7,item_name="诛武石",item_id=26363,random_count=0.13,},
{number=8,item_name="碧剑石",item_id=26378,random_count=1,},
{number=9,item_name="凌剑石",item_id=26379,random_count=2,},
{number=10,item_name="斗武石",item_id=26361,random_count=3,},
{number=11,item_name="凌武石",item_id=26362,},
{number=12,item_name="3级攻击玉魄",item_id=26502,random_count=0.5,},
{number=13,item_name="3级生命玉魄",item_id=26517,random_count=1.5,},
{number=14,item_name="1级攻击玉魄",item_id=26500,},
{number=15,item_name="1级生命玉魄",item_id=26515,random_count=7,},
{number=16,item_name="幻兽经验(中)",item_id=22531,random_count=15,},
{number=17,item_name="豪华游艇",item_id=26127,random_count=1,},
{number=18,item_name="火箭",item_id=26126,},
{number=19,item_name="香槟",item_id=26125,random_count=5,},
{number=20,item_name="洗炼瓶",item_id=26415,random_count=1,},
{number=21,item_name="100万铜钱",item_id=22010,random_count=2,},
{number=22,item_name="淬火灰矿",item_id=26200,random_count=3,},
{number=23,item_name="淬火灰矿·饰品",item_id=26203,random_count=3,},
{number=24,item_name="良品·神武器灵",item_id=26352,},
{number=25,item_name="珍宝·神武器灵",item_id=26353,random_count=12,},
{number=26,item_name="秘藏·神武器灵",item_id=26354,random_count=10,},
{number=27,item_name="良品·灵剑器灵",item_id=26376,random_count=12.22,},
{number=28,item_name="珍宝·灵剑器灵",item_id=26377,random_count=0.01,},
{number=29,item_name="绿色秘籍礼包",item_id=44182,random_count=0.02,},
{number=30,item_name="蓝色秘籍礼包",item_id=44183,random_count=0.04,},
{number=31,item_name="紫色秘籍礼包",item_id=44184,random_count=0.2,},
{number=32,item_name="橙色秘籍礼包",item_id=44185,random_count=0.25,}
},

item_random_desc_meta_table_map={
},
model_show={
{}
},

model_show_meta_table_map={
},
role_sp_guarantee={
{},
{sp_player_guarantee_id=2,reward_id=2,sp_guarantee_weight="1,2,0|3,3,2|4,4,80|5,5,20",},
{sp_player_guarantee_id=3,reward_id=3,sp_guarantee_weight="1,1,0|2,2,80|3,3,20|4,4,20|5,5,20",},
{sp_player_guarantee_id=4,reward_id=4,sp_guarantee_weight="1,1,0|2,2,20|3,3,80|4,5,20",},
{sp_player_guarantee_id=5,reward_id=5,sp_guarantee_weight="1,1,100|2,5,20",},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,}
},

role_sp_guarantee_meta_table_map={
[7]=2,	-- depth:1
[8]=3,	-- depth:1
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
config_param_default_table={start_server_day=1,end_server_day=999,grade=0,open_level=100,},

grade_default_table={grade=0,consume=1,reward_unbind=1,sp_guarantee_x=50,sp_guarantee_n=0,sp_guarantee_finish=0,rebate=1,model_item=29696,tip_desc="扭蛋中的奖励概率：",tip_title="幸运扭蛋",left_text_color="#f1edc6",down_text_color="#6d2f25",zanwu_text_color="#563a31",res_name="a3_zjm_icon_shhj",consume_item=26197,},

consume_default_table={consume=1,consume_count=100,cost_item_id=26197,},

reward_default_table={reward=1,reward_id=1,reward_item=item_table[37],reward_type=3,player_guarantee=0,guarantee_reward_limit=0,broadcast=0,reward_show=0,rewrad_rare_show=0,reward_big_show=0,},

rebate_default_table={rebate=1,index=1,lotto_num=100,reward_item={[0]=item_table[38]},rebate_icon=1,},

item_random_desc_default_table={grade=0,number=1,item_name="流火沙漏",item_id=29696,random_count=5,},

model_show_default_table={grade=0,model_show_itemid=29696,show_type=3,model_bundle_name="uis/rawimages/a2_shj_tu438",model_asset_name="a2_shj_tu438.png",display_pos="-45|40|0",display_scale=0.8,rotation="0|0|0",item_id_1=62004,image_name_1="a3_jsrh_jl_1",item_id_2=44495,image_name_2="a3_jsrh_jl_2",image_effect_bundle="effects/prefab/ui/ui_shhj_icon1_prefab",image_effect_asset="UI_shhj_icon1",},

role_sp_guarantee_default_table={reward=1,sp_player_guarantee_id=1,reward_id=1,sp_guarantee_weight="1,3,0|4,4,2|5,5,80",show_icon=1,}

}

