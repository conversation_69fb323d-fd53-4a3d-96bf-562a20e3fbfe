FightSoulUnlockTips = FightSoulUnlockTips or BaseClass(SafeBaseView)

function FightSoulUnlockTips:__init()
    self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 18)})
	self:AddViewResource(0, "uis/view/item_unlock_tips_prefab", "layout_tianshen_fight_alert")
end

function FightSoulUnlockTips:ReleaseCallBack()
	if nil ~= self.stuff_cell then
		self.stuff_cell:DeleteMe()
		self.stuff_cell = nil
	end

    self.slot = nil
end

function FightSoulUnlockTips:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FightSoul.UnlockSlotTitle

    if nil == self.stuff_cell then
    	self.stuff_cell = ItemCell.New(self.node_list["cell"])
    	self.stuff_cell:SetNeedItemGetWay(true)
    end

    self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
end

function FightSoulUnlockTips:SetDataAndOpen(slot)
    self.slot = slot
    self:Open()
end

function FightSoulUnlockTips:OnFlush()
    local slot_cfg = FightSoulWGData.Instance:GetFightslotCfgBySlot(self.slot)
    if IsEmptyTable(slot_cfg) then
    	return
    end

    local stuff_id = slot_cfg.stuff_id
    local stuff_num = slot_cfg.stuff_num
    if stuff_id <= 0 or stuff_num <= 0 then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(stuff_id)
    if item_cfg ~= nil then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
        local color = item_num >= stuff_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        self.stuff_cell:SetData({item_id = stuff_id})
        local string_num = item_num .. "/" .. stuff_num
        self.stuff_cell:SetRightBottomColorText(string_num, color)
        self.stuff_cell:SetRightBottomTextVisible(true)
        self.node_list["rich_des_1"].text.text = string.format(Language.FightSoul.UnlockDesc1,
                            stuff_num, ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]))
        local unlock_num, max_num = FightSoulWGData.Instance:GetUnlockSlotNum()
        self.node_list["rich_des_2"].text.text = string.format(Language.FightSoul.UnlockDesc2, unlock_num, max_num)
    end
end

function FightSoulUnlockTips:OnClinkOkHandler()
    local slot_cfg = FightSoulWGData.Instance:GetFightslotCfgBySlot(self.slot)
    if IsEmptyTable(slot_cfg) then
        return
    end

    local stuff_id = slot_cfg.stuff_id
    local stuff_num = slot_cfg.stuff_num
    if stuff_id <= 0 or stuff_num <= 0 then
        return
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
    if item_num < stuff_num then
        local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(stuff_id)
        if item_cfg then
            local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.NoEnoughTips, item_name))
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_id})
        end
        return
    end

	FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.SLOT_UNLOCK, self.slot)
	self:Close()
end
