function HolyDarkWeaponView:InitHolyDarkEquipBagView()
    if nil == self.equip_bag_list then
        self.equip_bag_list = AsyncBaseGrid.New()
        self.equip_bag_list:SetStartZeroIndex(false)
        self.equip_bag_list:CreateCells({
            col = 4,
            cell_count = 32,
            list_view = self.node_list["equip_bag_grid"],
            itemRender = HolyDarkEquipBagCell,
            assetBundle = "uis/view/holy_dark_ui_prefab",
            assetName = "holy_equip_bag_cell",
            change_cells_num = 2,
            complement_col_item = true,
        })
    end

    XUI.AddClickEventListener(self.node_list["btn_equip_suit"], BindTool.Bind(self.ClickEquipSuit, self))
    XUI.AddClickEventListener(self.node_list["btn_gather_equip"], BindTool.Bind(self.OnClickGetEquipWay, self))
end

function HolyDarkWeaponView:DeleteHolyDarkEquipBagView()
    if nil ~= self.equip_bag_list then
        self.equip_bag_list:DeleteMe()
        self.equip_bag_list = nil
    end
end

function HolyDarkWeaponView:FlushHolyDarkEquipBagView()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        local equip_bag_info = HolyDarkWeaponWGData.Instance:GetRelicEquipBagListBySeq(relic_seq)
        self.equip_bag_list:SetDataList(equip_bag_info)
        local is_active = HolyDarkWeaponWGData.Instance:GetRelicSuitRemind(relic_seq)
        self.node_list.equip_suit_red:SetActive(is_active)
    end
end

function HolyDarkWeaponView:ClickEquipSuit()
    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
        HolyDarkWeaponWGCtrl.Instance:OpenSuitAttrView(relic_seq)
    end
end

--
function HolyDarkWeaponView:OnClickGetEquipWay()
    local weapon_type = HolyDarkWeaponWGData.Instance:GetCurWeaponType()
    local activity_name
    local view_name
    if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
        activity_name = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW
        view_name = GuideModuleName.DIYDrawOneView
    else
        activity_name = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY2_DRAW
        view_name = GuideModuleName. DIYDrawTwoView
    end

    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(activity_name)
    if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
        ViewManager.Instance:Open(view_name)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.HolyDarkWeapon.ActEnd)
    end 
end

------------------
HolyDarkEquipBagCell = HolyDarkEquipBagCell or BaseClass(BaseRender)
function HolyDarkEquipBagCell:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.cell_node)
        self.item_cell:SetIsShowTips(false)
        self.item_cell:SetClickCallBack(BindTool.Bind(self.OnItemClick, self))
        local weapon_type = HolyDarkWeaponWGData.Instance:GetCurWeaponType()
        local bundle, asset = ResPath.GetFairyLandEquipImages("a1_zzlz_tbdi")
        self.item_cell:SetCellBg(bundle, asset)
    end
end

function HolyDarkEquipBagCell:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function HolyDarkEquipBagCell:OnFlush()
    if IsEmptyTable(self.data)  or self.data.item_id <= 0 or self.data.num <= 0 then
        self.item_cell:ClearData()
        return
    end

    local data = self.data
    local item_data = {item_id = data.item_id, num = data.num, is_bind = data.is_bind}
    self.item_cell:SetData(item_data)
     local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq < 0 then
        return
    end
    local is_better = HolyDarkWeaponWGData.Instance:RelicEquipIsBetterWear(relic_seq, self.data)
    self.item_cell:SetUpFlagIconVisible(is_better)
end

function HolyDarkEquipBagCell:OnItemClick()
    if IsEmptyTable(self.data) then
        return
    end

    local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq < 0 then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local btn_callback_event

    if not IsEmptyTable(item_cfg) then
        btn_callback_event = {}
        btn_callback_event[1] = {btn_text = Language.HolyDarkWeapon.Equip, callback = function()
            local is_better = HolyDarkWeaponWGData.Instance:RelicEquipIsBetterWear(relic_seq, self.data)
            if is_better then
                HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.EQUIP, relic_seq, self.data.slot, self.data.index)
            else
                TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.NeedBetterColorEquip)
            end
        end}
    end

    TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_RELIC_EQUIP_WEAR, nil, nil, btn_callback_event)
end
