-- Y-运营活动-小猫探险.xls
local item_table={
[1]={item_id=48079,num=1,is_bind=1},
[2]={item_id=47216,num=1,is_bind=1},
[3]={item_id=48080,num=1,is_bind=1},
[4]={item_id=47217,num=1,is_bind=1},
[5]={item_id=44183,num=1,is_bind=1},
[6]={item_id=47218,num=1,is_bind=1},
[7]={item_id=22753,num=1,is_bind=1},
[8]={item_id=47219,num=1,is_bind=1},
[9]={item_id=26450,num=1,is_bind=1},
[10]={item_id=47220,num=1,is_bind=1},
[11]={item_id=26455,num=1,is_bind=1},
[12]={item_id=47221,num=1,is_bind=1},
[13]={item_id=26460,num=1,is_bind=1},
[14]={item_id=47222,num=1,is_bind=1},
[15]={item_id=26461,num=1,is_bind=1},
[16]={item_id=47223,num=1,is_bind=1},
[17]={item_id=26462,num=1,is_bind=1},
[18]={item_id=26463,num=1,is_bind=1},
[19]={item_id=48147,num=1,is_bind=1},
[20]={item_id=26464,num=1,is_bind=1},
[21]={item_id=26351,num=2,is_bind=1},
[22]={item_id=47416,num=1,is_bind=1},
[23]={item_id=26351,num=5,is_bind=1},
[24]={item_id=47417,num=1,is_bind=1},
[25]={item_id=47418,num=10,is_bind=1},
[26]={item_id=26380,num=1,is_bind=1},
[27]={item_id=47419,num=1,is_bind=1},
[28]={item_id=47420,num=1,is_bind=1},
[29]={item_id=47421,num=1,is_bind=1},
[30]={item_id=47422,num=1,is_bind=1},
[31]={item_id=47423,num=1,is_bind=1},
[32]={item_id=27910,num=2,is_bind=1},
[33]={item_id=27910,num=4,is_bind=1},
[34]={item_id=27910,num=6,is_bind=1},
[35]={item_id=27613,num=2,is_bind=1},
[36]={item_id=27613,num=4,is_bind=1},
[37]={item_id=27613,num=6,is_bind=1},
[38]={item_id=44184,num=1,is_bind=1},
[39]={item_id=44185,num=1,is_bind=1},
[40]={item_id=26459,num=1,is_bind=1},
[41]={item_id=48134,num=1,is_bind=1},
[42]={item_id=48120,num=1,is_bind=1},
[43]={item_id=48454,num=1,is_bind=1},
[44]={item_id=29615,num=2,is_bind=1},
[45]={item_id=44182,num=1,is_bind=1},
[46]={item_id=29615,num=4,is_bind=1},
[47]={item_id=26444,num=1,is_bind=1},
[48]={item_id=29615,num=6,is_bind=1},
[49]={item_id=48117,num=1,is_bind=1},
[50]={item_id=26158,num=3,is_bind=1},
[51]={item_id=26158,num=6,is_bind=1},
[52]={item_id=26158,num=8,is_bind=1},
[53]={item_id=26158,num=12,is_bind=1},
[54]={item_id=26158,num=14,is_bind=1},
[55]={item_id=26158,num=26,is_bind=1},
[56]={item_id=26158,num=4,is_bind=1},
[57]={item_id=26158,num=7,is_bind=1},
[58]={item_id=26158,num=13,is_bind=1},
[59]={item_id=26158,num=9,is_bind=1},
[60]={item_id=26158,num=18,is_bind=1},
[61]={item_id=26158,num=27,is_bind=1},
[62]={item_id=26158,num=16,is_bind=1},
[63]={item_id=26158,num=20,is_bind=1},
[64]={item_id=26158,num=40,is_bind=1},
[65]={item_id=26158,num=60,is_bind=1},
[66]={item_id=26158,num=21,is_bind=1},
[67]={item_id=26158,num=54,is_bind=1},
[68]={item_id=26158,num=81,is_bind=1},
[69]={item_id=26158,num=24,is_bind=1},
[70]={item_id=26158,num=36,is_bind=1},
[71]={item_id=26437,num=1,is_bind=1},
[72]={item_id=26158,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=6,end_day=10,grade=2,},
{start_day=11,end_day=15,grade=3,},
{start_day=16,end_day=20,grade=4,},
{start_day=21,end_day=25,grade=5,},
{start_day=26,end_day=30,grade=6,},
{start_day=31,end_day=9999,grade=7,}
},

open_day_meta_table_map={
},
reward={
{},
{step=2,item=item_table[1],},
{step=3,item=item_table[2],},
{step=4,item=item_table[3],},
{step=5,item=item_table[4],},
{step=6,item=item_table[5],},
{step=7,item=item_table[6],},
{step=8,item=item_table[7],},
{step=9,item=item_table[8],},
{step=10,item=item_table[9],},
{step=11,item=item_table[10],},
{step=12,item=item_table[11],},
{step=13,item=item_table[12],},
{step=14,item=item_table[13],},
{step=15,item=item_table[14],},
{step=16,item=item_table[15],},
{step=17,item=item_table[16],},
{step=18,item=item_table[17],},
{step=19,item=item_table[18],},
{step=20,item=item_table[19],},
{step=21,item=item_table[20],},
{grade=2,},
{grade=2,item=item_table[21],},
{grade=2,item=item_table[22],},
{grade=2,item=item_table[23],},
{grade=2,item=item_table[24],},
{grade=2,},
{grade=2,item=item_table[25],},
{grade=2,item=item_table[26],},
{grade=2,item=item_table[27],},
{grade=2,},
{grade=2,item=item_table[28],},
{grade=2,},
{grade=2,item=item_table[29],},
{grade=2,},
{grade=2,item=item_table[30],},
{grade=2,},
{grade=2,item=item_table[31],},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,item=item_table[32],},
{grade=3,},
{grade=3,item=item_table[33],},
{grade=3,},
{grade=3,},
{grade=3,item=item_table[34],},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,item=item_table[35],},
{grade=4,},
{grade=4,item=item_table[36],},
{grade=4,},
{grade=4,},
{grade=4,item=item_table[37],},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,item=item_table[38],},
{grade=5,},
{grade=5,item=item_table[39],},
{grade=5,},
{grade=5,item=item_table[40],},
{grade=5,},
{grade=5,item=item_table[41],},
{grade=5,},
{grade=5,item=item_table[42],},
{grade=5,item=item_table[43],},
{grade=5,step=19,},
{grade=5,step=20,},
{grade=5,},
{grade=6,},
{grade=6,item=item_table[44],},
{grade=6,item=item_table[45],},
{grade=6,item=item_table[46],},
{grade=6,item=item_table[5],},
{grade=6,item=item_table[47],},
{grade=6,item=item_table[48],},
{grade=6,item=item_table[49],},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,}
},

reward_meta_table_map={
[104]=19,	-- depth:1
[105]=21,	-- depth:1
[109]=4,	-- depth:1
[108]=3,	-- depth:1
[103]=18,	-- depth:1
[110]=5,	-- depth:1
[111]=6,	-- depth:1
[112]=7,	-- depth:1
[107]=2,	-- depth:1
[102]=18,	-- depth:1
[100]=16,	-- depth:1
[113]=8,	-- depth:1
[99]=15,	-- depth:1
[98]=14,	-- depth:1
[97]=13,	-- depth:1
[96]=12,	-- depth:1
[95]=11,	-- depth:1
[94]=10,	-- depth:1
[93]=9,	-- depth:1
[92]=113,	-- depth:2
[91]=112,	-- depth:2
[90]=111,	-- depth:2
[89]=110,	-- depth:2
[88]=109,	-- depth:2
[87]=108,	-- depth:2
[101]=17,	-- depth:1
[114]=93,	-- depth:2
[118]=97,	-- depth:2
[116]=95,	-- depth:2
[145]=103,	-- depth:2
[144]=102,	-- depth:2
[143]=101,	-- depth:2
[142]=100,	-- depth:2
[141]=99,	-- depth:2
[140]=98,	-- depth:2
[139]=118,	-- depth:3
[138]=96,	-- depth:2
[137]=116,	-- depth:3
[136]=94,	-- depth:2
[135]=114,	-- depth:3
[134]=92,	-- depth:3
[133]=91,	-- depth:3
[132]=90,	-- depth:3
[131]=89,	-- depth:3
[130]=88,	-- depth:3
[129]=87,	-- depth:3
[128]=107,	-- depth:2
[126]=105,	-- depth:2
[86]=128,	-- depth:3
[125]=104,	-- depth:2
[124]=145,	-- depth:3
[122]=143,	-- depth:3
[121]=142,	-- depth:3
[120]=141,	-- depth:3
[119]=140,	-- depth:3
[117]=138,	-- depth:3
[115]=136,	-- depth:3
[123]=144,	-- depth:3
[74]=137,	-- depth:4
[83]=125,	-- depth:3
[50]=134,	-- depth:4
[49]=7,	-- depth:1
[48]=132,	-- depth:4
[47]=131,	-- depth:4
[46]=4,	-- depth:1
[45]=129,	-- depth:4
[44]=2,	-- depth:1
[42]=126,	-- depth:3
[41]=83,	-- depth:4
[40]=124,	-- depth:4
[39]=123,	-- depth:4
[38]=17,	-- depth:1
[37]=121,	-- depth:4
[36]=15,	-- depth:1
[35]=119,	-- depth:4
[34]=13,	-- depth:1
[33]=117,	-- depth:4
[32]=11,	-- depth:1
[31]=115,	-- depth:4
[30]=9,	-- depth:1
[29]=8,	-- depth:1
[28]=7,	-- depth:1
[27]=48,	-- depth:5
[26]=5,	-- depth:1
[25]=4,	-- depth:1
[24]=3,	-- depth:1
[23]=2,	-- depth:1
[51]=135,	-- depth:4
[84]=42,	-- depth:4
[52]=31,	-- depth:5
[54]=33,	-- depth:5
[82]=40,	-- depth:5
[81]=39,	-- depth:5
[80]=122,	-- depth:4
[79]=37,	-- depth:5
[78]=120,	-- depth:4
[77]=35,	-- depth:5
[76]=139,	-- depth:4
[75]=54,	-- depth:6
[146]=41,	-- depth:5
[73]=52,	-- depth:6
[72]=51,	-- depth:5
[71]=50,	-- depth:5
[70]=7,	-- depth:1
[69]=27,	-- depth:6
[68]=47,	-- depth:5
[67]=4,	-- depth:1
[66]=45,	-- depth:5
[65]=2,	-- depth:1
[63]=84,	-- depth:5
[62]=146,	-- depth:6
[61]=82,	-- depth:6
[60]=81,	-- depth:6
[59]=80,	-- depth:5
[58]=79,	-- depth:6
[57]=78,	-- depth:5
[56]=77,	-- depth:6
[55]=76,	-- depth:5
[53]=74,	-- depth:5
[147]=63,	-- depth:6
},
consume={
{},
{step=2,cost_item_num=3,},
{step=3,cost_item_num=6,},
{step=4,cost_item_num=10,},
{step=5,cost_item_num=15,},
{step=6,cost_item_num=20,},
{step=7,cost_item_num=25,},
{step=8,cost_item_num=30,},
{step=9,cost_item_num=40,},
{step=10,cost_item_num=50,},
{step=11,cost_item_num=60,},
{step=12,cost_item_num=70,},
{step=13,cost_item_num=80,},
{step=14,cost_item_num=95,},
{step=15,cost_item_num=110,},
{step=16,cost_item_num=130,},
{step=17,cost_item_num=150,},
{step=18,cost_item_num=170,},
{step=19,cost_item_num=190,},
{step=20,cost_item_num=210,},
{step=21,cost_item_num=230,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,step=9,},
{grade=5,cost_item_num=60,},
{grade=5,cost_item_num=70,},
{grade=5,cost_item_num=80,},
{grade=5,cost_item_num=95,},
{grade=5,cost_item_num=110,},
{grade=5,cost_item_num=130,},
{grade=5,step=16,},
{grade=5,cost_item_num=170,},
{grade=5,cost_item_num=190,},
{grade=5,cost_item_num=210,},
{grade=5,cost_item_num=230,},
{grade=5,cost_item_num=250,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,step=7,},
{grade=6,step=8,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,}
},

consume_meta_table_map={
[104]=20,	-- depth:1
[105]=21,	-- depth:1
[109]=4,	-- depth:1
[108]=3,	-- depth:1
[103]=19,	-- depth:1
[110]=5,	-- depth:1
[111]=6,	-- depth:1
[112]=8,	-- depth:1
[107]=2,	-- depth:1
[102]=18,	-- depth:1
[100]=17,	-- depth:1
[113]=9,	-- depth:1
[99]=15,	-- depth:1
[98]=14,	-- depth:1
[97]=13,	-- depth:1
[96]=12,	-- depth:1
[95]=11,	-- depth:1
[94]=10,	-- depth:1
[93]=10,	-- depth:1
[92]=113,	-- depth:2
[91]=112,	-- depth:2
[90]=111,	-- depth:2
[89]=110,	-- depth:2
[88]=109,	-- depth:2
[87]=108,	-- depth:2
[101]=17,	-- depth:1
[114]=93,	-- depth:2
[118]=97,	-- depth:2
[116]=95,	-- depth:2
[145]=103,	-- depth:2
[144]=102,	-- depth:2
[143]=101,	-- depth:2
[142]=100,	-- depth:2
[141]=99,	-- depth:2
[140]=98,	-- depth:2
[139]=118,	-- depth:3
[138]=96,	-- depth:2
[137]=116,	-- depth:3
[136]=94,	-- depth:2
[135]=114,	-- depth:3
[134]=92,	-- depth:3
[133]=91,	-- depth:3
[132]=90,	-- depth:3
[131]=89,	-- depth:3
[130]=88,	-- depth:3
[129]=87,	-- depth:3
[128]=107,	-- depth:2
[126]=105,	-- depth:2
[86]=128,	-- depth:3
[125]=104,	-- depth:2
[124]=145,	-- depth:3
[122]=143,	-- depth:3
[121]=142,	-- depth:3
[120]=141,	-- depth:3
[119]=140,	-- depth:3
[117]=138,	-- depth:3
[115]=136,	-- depth:3
[123]=144,	-- depth:3
[74]=137,	-- depth:4
[83]=125,	-- depth:3
[50]=134,	-- depth:4
[49]=133,	-- depth:4
[48]=132,	-- depth:4
[47]=131,	-- depth:4
[46]=130,	-- depth:4
[45]=129,	-- depth:4
[44]=86,	-- depth:4
[42]=126,	-- depth:3
[41]=83,	-- depth:4
[40]=124,	-- depth:4
[39]=123,	-- depth:4
[38]=122,	-- depth:4
[37]=121,	-- depth:4
[36]=120,	-- depth:4
[35]=119,	-- depth:4
[34]=139,	-- depth:4
[33]=117,	-- depth:4
[32]=74,	-- depth:5
[31]=115,	-- depth:4
[30]=135,	-- depth:4
[29]=50,	-- depth:5
[28]=49,	-- depth:5
[27]=48,	-- depth:5
[26]=47,	-- depth:5
[25]=46,	-- depth:5
[24]=45,	-- depth:5
[23]=44,	-- depth:5
[51]=30,	-- depth:5
[84]=42,	-- depth:4
[52]=31,	-- depth:5
[54]=33,	-- depth:5
[82]=40,	-- depth:5
[81]=39,	-- depth:5
[80]=38,	-- depth:5
[79]=37,	-- depth:5
[78]=36,	-- depth:5
[77]=35,	-- depth:5
[76]=34,	-- depth:5
[75]=54,	-- depth:6
[146]=41,	-- depth:5
[73]=52,	-- depth:6
[72]=51,	-- depth:6
[71]=29,	-- depth:6
[70]=28,	-- depth:6
[69]=27,	-- depth:6
[68]=26,	-- depth:6
[67]=25,	-- depth:6
[66]=24,	-- depth:6
[65]=23,	-- depth:6
[63]=84,	-- depth:5
[62]=146,	-- depth:6
[61]=82,	-- depth:6
[60]=81,	-- depth:6
[59]=80,	-- depth:6
[58]=79,	-- depth:6
[57]=78,	-- depth:6
[56]=77,	-- depth:6
[55]=76,	-- depth:6
[53]=32,	-- depth:6
[147]=63,	-- depth:6
},
task={
{grade=1,task_type=1,param1=30,des="获得活跃点",target=30,open_panel="bizuo#bizuo_bizuo",},
{grade=1,task_id=2,},
{grade=1,task_id=3,},
{grade=1,task_id=4,},
{grade=1,task_id=5,},
{grade=1,task_id=6,},
{grade=1,task_id=7,},
{grade=1,task_id=8,},
{grade=1,task_id=9,},
{grade=1,task_id=10,},
{grade=1,task_id=11,},
{grade=1,task_id=12,},
{grade=1,task_id=13,},
{grade=1,task_id=14,},
{grade=1,task_id=15,},
{grade=1,task_id=16,},
{grade=1,task_id=17,},
{grade=1,task_id=18,},
{grade=1,task_id=19,},
{grade=1,task_id=20,},
{grade=1,task_id=21,},
{grade=1,task_id=22,},
{grade=1,task_id=23,},
{grade=1,task_id=24,},
{grade=1,task_id=25,},
{grade=1,task_id=26,},
{grade=1,task_id=27,},
{grade=1,task_id=28,},
{grade=1,task_id=29,},
{grade=1,task_id=30,},
{grade=1,task_id=31,},
{grade=1,task_id=32,},
{grade=1,task_id=33,},
{grade=1,task_id=34,},
{grade=1,task_id=35,},
{grade=1,task_id=36,},
{grade=1,task_id=37,},
{grade=1,task_id=38,},
{grade=1,task_id=39,},
{grade=1,task_id=40,},
{grade=1,task_id=41,},
{grade=1,task_id=42,},
{grade=1,task_id=43,},
{grade=2,task_id=44,},
{grade=2,task_id=45,},
{grade=2,task_id=46,},
{grade=2,task_id=47,},
{grade=2,task_id=48,},
{grade=2,task_id=49,},
{grade=2,task_id=50,},
{grade=2,task_id=51,},
{grade=2,task_id=52,},
{grade=2,task_id=53,},
{grade=2,task_id=54,},
{grade=2,task_id=55,},
{grade=2,task_id=56,},
{grade=2,task_id=57,},
{grade=2,task_id=58,},
{grade=2,task_id=59,},
{grade=2,task_id=60,},
{grade=2,task_id=61,},
{grade=2,task_id=62,},
{grade=2,task_id=63,},
{grade=2,task_id=64,},
{grade=2,task_id=65,},
{grade=2,task_id=66,},
{grade=2,task_id=67,},
{grade=2,task_id=68,},
{grade=2,task_id=69,},
{grade=2,task_id=70,},
{grade=2,task_id=71,},
{grade=2,task_id=72,},
{grade=2,task_id=73,},
{grade=2,task_id=74,},
{grade=2,task_id=75,},
{grade=2,task_id=76,},
{grade=2,task_id=77,},
{grade=2,task_id=78,},
{grade=2,task_id=79,},
{grade=2,task_id=80,},
{grade=2,task_id=81,},
{grade=2,task_id=82,},
{grade=2,task_id=83,},
{grade=2,task_id=84,},
{grade=2,task_id=85,},
{grade=2,task_id=86,},
{grade=3,task_id=87,},
{grade=3,task_id=88,},
{grade=3,task_id=89,},
{grade=3,task_id=90,},
{grade=3,task_id=91,},
{grade=3,task_id=92,},
{grade=3,task_id=93,},
{grade=3,task_id=94,},
{grade=3,task_id=95,},
{grade=3,task_id=96,},
{grade=3,task_id=97,},
{grade=3,task_id=98,},
{grade=3,task_id=99,},
{grade=3,task_id=100,},
{grade=3,task_id=101,},
{grade=3,task_id=102,},
{grade=3,task_id=103,},
{grade=3,task_id=104,},
{grade=3,task_id=105,},
{grade=3,task_id=106,},
{grade=3,task_id=107,},
{grade=3,task_id=108,},
{grade=3,task_id=109,},
{grade=3,task_id=110,},
{grade=3,task_id=111,},
{grade=3,task_id=112,},
{grade=3,task_id=113,},
{grade=3,task_id=114,},
{grade=3,task_id=115,},
{grade=3,task_id=116,},
{grade=3,task_id=117,},
{grade=3,task_id=118,},
{grade=3,task_id=119,},
{grade=3,task_id=120,},
{grade=3,task_id=121,},
{grade=3,task_id=122,},
{grade=3,task_id=123,},
{grade=3,task_id=124,},
{grade=3,task_id=125,},
{grade=3,task_id=126,},
{grade=3,task_id=127,},
{grade=3,task_id=128,},
{grade=3,task_id=129,},
{grade=4,task_id=130,},
{grade=4,task_id=131,},
{grade=4,task_id=132,},
{grade=4,task_id=133,},
{grade=4,task_id=134,},
{grade=4,task_id=135,},
{grade=4,task_id=136,},
{grade=4,task_id=137,},
{grade=4,task_id=138,},
{grade=4,task_id=139,},
{grade=4,task_id=140,},
{grade=4,task_id=141,},
{grade=4,task_id=142,},
{grade=4,task_id=143,},
{grade=4,task_id=144,},
{grade=4,task_id=145,},
{grade=4,task_id=146,},
{grade=4,task_id=147,},
{grade=4,task_id=148,},
{grade=4,task_id=149,},
{grade=4,task_id=150,},
{grade=4,task_id=151,},
{grade=4,task_id=152,},
{grade=4,task_id=153,},
{grade=4,task_id=154,},
{grade=4,task_id=155,},
{grade=4,task_id=156,},
{grade=4,task_id=157,},
{grade=4,task_id=158,},
{grade=4,task_id=159,},
{grade=4,task_id=160,},
{grade=4,task_id=161,},
{grade=4,task_id=162,},
{grade=4,task_id=163,},
{grade=4,task_id=164,},
{grade=4,task_id=165,},
{grade=4,task_id=166,},
{grade=4,task_id=167,},
{grade=4,task_id=168,},
{grade=4,task_id=169,},
{grade=4,task_id=170,},
{grade=4,task_id=171,},
{grade=4,task_id=172,},
{grade=5,task_id=173,},
{grade=5,task_id=174,},
{grade=5,task_id=175,},
{grade=5,task_id=176,},
{grade=5,task_id=177,},
{grade=5,task_id=178,},
{grade=5,task_id=179,},
{grade=5,task_id=180,},
{grade=5,task_id=181,},
{grade=5,task_id=182,},
{grade=5,task_id=183,},
{grade=5,task_id=184,},
{grade=5,task_id=185,},
{grade=5,task_id=186,},
{grade=5,task_id=187,},
{grade=5,task_id=188,},
{grade=5,task_id=189,},
{grade=5,task_id=190,},
{grade=5,task_id=191,},
{grade=5,task_id=192,},
{grade=5,task_id=193,},
{grade=5,task_id=194,},
{grade=5,task_id=195,},
{grade=5,task_id=196,},
{grade=5,task_id=197,},
{grade=5,task_id=198,},
{grade=5,task_id=199,},
{grade=5,task_id=200,},
{grade=5,task_id=201,},
{grade=5,task_id=202,},
{grade=5,task_id=203,},
{grade=5,task_id=204,},
{grade=5,task_id=205,},
{grade=5,task_id=206,},
{grade=5,task_id=207,},
{grade=5,task_id=208,},
{grade=5,task_id=209,},
{grade=5,task_id=210,},
{grade=5,task_id=211,},
{grade=5,task_id=212,},
{grade=5,task_id=213,},
{grade=5,task_id=214,},
{grade=5,task_id=215,},
{grade=6,task_id=216,},
{grade=6,task_id=217,},
{grade=6,task_id=218,},
{grade=6,task_id=219,},
{grade=6,task_id=220,},
{grade=6,task_id=221,},
{grade=6,task_id=222,},
{grade=6,task_id=223,},
{grade=6,task_id=224,},
{grade=6,task_id=225,},
{grade=6,task_id=226,},
{grade=6,task_id=227,},
{grade=6,task_id=228,},
{grade=6,task_id=229,},
{grade=6,task_id=230,},
{grade=6,task_id=231,},
{grade=6,task_id=232,},
{grade=6,task_id=233,},
{grade=6,task_id=234,},
{grade=6,task_id=235,},
{grade=6,task_id=236,},
{grade=6,task_id=237,},
{grade=6,task_id=238,},
{grade=6,task_id=239,},
{grade=6,task_id=240,},
{grade=6,task_id=241,},
{grade=6,task_id=242,},
{grade=6,task_id=243,},
{grade=6,task_id=244,},
{grade=6,task_id=245,},
{grade=6,task_id=246,},
{grade=6,task_id=247,},
{grade=6,task_id=248,},
{grade=6,task_id=249,},
{grade=6,task_id=250,},
{grade=6,task_id=251,},
{grade=6,task_id=252,},
{grade=6,task_id=253,},
{grade=6,task_id=254,},
{grade=6,task_id=255,},
{grade=6,task_id=256,},
{grade=6,task_id=257,},
{grade=6,task_id=258,},
{task_id=259,param1=30,target=30,},
{task_id=260,task_type=1,param1=50,des="获得活跃点",open_panel="bizuo#bizuo_bizuo",},
{task_id=261,param1=90,target=90,},
{task_id=262,param1=120,target=120,},
{task_id=263,param1=160,target=160,},
{task_id=264,param1=200,target=200,},
{task_id=265,task_type=2,item_list={[0]=item_table[50]},des="充值任意金额",target=1,open_panel="vip#recharge_cz",},
{task_id=266,task_type=3,des="每日登录",target=1,open_panel="",},
{task_id=267,task_type=4,param1=5,target=500,},
{task_id=268,task_type=4,param1=6,target=1000,},
{task_id=269,task_type=4,param1=7,target=1500,},
{task_id=270,task_type=4,param1=8,target=2000,},
{task_id=271,task_type=4,param1=9,target=2500,},
{task_id=272,task_type=4,param1=10,target=3000,},
{task_id=273,task_type=4,param1=11,target=3500,},
{task_id=274,task_type=4,param1=12,target=4000,},
{task_id=275,task_type=4,param1=13,target=4500,},
{task_id=276,task_type=4,param1=14,target=5000,},
{task_id=277,param1=3280,item_list={[0]=item_table[51]},target=3280,},
{task_id=278,param1=6480,item_list={[0]=item_table[52]},target=6480,},
{task_id=279,param1=10000,item_list={[0]=item_table[53]},target=10000,},
{task_id=280,param1=20000,item_list={[0]=item_table[54]},target=20000,},
{task_id=281,task_type=5,param1=30000,item_list={[0]=item_table[55]},des="累计充值灵玉",target=30000,open_panel="vip#recharge_cz",},
{task_id=282,param1=3280,item_list={[0]=item_table[50]},target=3280,},
{task_id=283,task_type=6,param1=6480,item_list={[0]=item_table[56]},des="累计消费灵玉",target=6480,open_panel="shop#Tab_Shop30",},
{task_id=284,param1=10000,item_list={[0]=item_table[51]},target=10000,},
{task_id=285,param1=20000,item_list={[0]=item_table[57]},target=20000,},
{task_id=286,param1=30000,item_list={[0]=item_table[58]},target=30000,},
{task_id=287,param2=10,item_list={[0]=item_table[56]},target=10,},
{task_id=288,param2=50,item_list={[0]=item_table[57]},des="流光秘宝寻宝",open_panel="TreasureHunt#treasurehunt_equip",},
{task_id=289,param2=100,item_list={[0]=item_table[59]},target=100,},
{task_id=290,param2=200,item_list={[0]=item_table[60]},target=200,},
{task_id=291,param2=350,item_list={[0]=item_table[61]},target=350,},
{task_id=292,param2=10,item_list={[0]=item_table[52]},target=10,},
{task_id=293,param1=2,param2=50,item_list={[0]=item_table[62]},des="天光幻宝寻宝",open_panel="TreasureHunt#treasurehunt_dianfeng",},
{task_id=294,param2=100,item_list={[0]=item_table[63]},target=100,},
{task_id=295,param2=200,item_list={[0]=item_table[64]},target=200,},
{task_id=296,param2=350,item_list={[0]=item_table[65]},target=350,},
{task_id=297,param2=10,item_list={[0]=item_table[53]},target=10,},
{task_id=298,param1=3,param2=50,item_list={[0]=item_table[66]},des="圣光奇宝寻宝",open_panel="TreasureHunt#treasurehunt_zhizun",},
{task_id=299,param2=100,item_list={[0]=item_table[61]},target=100,},
{task_id=300,param2=200,item_list={[0]=item_table[67]},target=200,},
{task_id=301,param2=350,item_list={[0]=item_table[68]},target=350,},
{task_id=302,task_type=8,param1=10,item_list={[0]=item_table[53]},des="星光魂契寻宝",target=10,open_panel="TreasureHunt#treasurehunt_fuwen",},
{task_id=303,param1=20,target=20,},
{task_id=304,param1=40,item_list={[0]=item_table[69]},target=40,},
{task_id=305,param1=60,target=60,},
{task_id=306,param1=90,item_list={[0]=item_table[70]},target=90,}
},

task_meta_table_map={
[54]=269,	-- depth:1
[53]=268,	-- depth:1
[52]=267,	-- depth:1
[57]=272,	-- depth:1
[182]=268,	-- depth:1
[183]=269,	-- depth:1
[95]=267,	-- depth:1
[55]=270,	-- depth:1
[56]=271,	-- depth:1
[58]=273,	-- depth:1
[233]=276,	-- depth:1
[59]=274,	-- depth:1
[60]=275,	-- depth:1
[61]=276,	-- depth:1
[225]=268,	-- depth:1
[226]=269,	-- depth:1
[227]=270,	-- depth:1
[228]=271,	-- depth:1
[229]=272,	-- depth:1
[230]=273,	-- depth:1
[231]=274,	-- depth:1
[232]=275,	-- depth:1
[224]=267,	-- depth:1
[97]=269,	-- depth:1
[96]=268,	-- depth:1
[99]=271,	-- depth:1
[17]=275,	-- depth:1
[16]=274,	-- depth:1
[15]=273,	-- depth:1
[14]=272,	-- depth:1
[13]=271,	-- depth:1
[12]=270,	-- depth:1
[11]=269,	-- depth:1
[10]=268,	-- depth:1
[9]=267,	-- depth:1
[146]=275,	-- depth:1
[147]=276,	-- depth:1
[187]=273,	-- depth:1
[188]=274,	-- depth:1
[189]=275,	-- depth:1
[190]=276,	-- depth:1
[18]=276,	-- depth:1
[98]=270,	-- depth:1
[145]=274,	-- depth:1
[144]=273,	-- depth:1
[100]=272,	-- depth:1
[138]=267,	-- depth:1
[139]=268,	-- depth:1
[101]=273,	-- depth:1
[102]=274,	-- depth:1
[186]=272,	-- depth:1
[103]=275,	-- depth:1
[181]=267,	-- depth:1
[184]=270,	-- depth:1
[140]=269,	-- depth:1
[185]=271,	-- depth:1
[141]=270,	-- depth:1
[142]=271,	-- depth:1
[143]=272,	-- depth:1
[104]=276,	-- depth:1
[217]=260,	-- depth:1
[223]=266,	-- depth:1
[94]=266,	-- depth:1
[88]=260,	-- depth:1
[180]=266,	-- depth:1
[2]=260,	-- depth:1
[8]=266,	-- depth:1
[291]=288,	-- depth:1
[290]=288,	-- depth:1
[289]=288,	-- depth:1
[30]=288,	-- depth:1
[287]=288,	-- depth:1
[137]=266,	-- depth:1
[174]=260,	-- depth:1
[245]=288,	-- depth:1
[131]=260,	-- depth:1
[202]=288,	-- depth:1
[45]=260,	-- depth:1
[159]=288,	-- depth:1
[73]=288,	-- depth:1
[259]=260,	-- depth:1
[262]=260,	-- depth:1
[263]=260,	-- depth:1
[264]=260,	-- depth:1
[261]=260,	-- depth:1
[116]=288,	-- depth:1
[51]=266,	-- depth:1
[164]=293,	-- depth:1
[173]=1,	-- depth:1
[177]=263,	-- depth:2
[178]=264,	-- depth:2
[179]=265,	-- depth:1
[162]=291,	-- depth:2
[161]=290,	-- depth:2
[160]=289,	-- depth:2
[176]=262,	-- depth:2
[175]=261,	-- depth:2
[247]=290,	-- depth:2
[203]=289,	-- depth:2
[284]=283,	-- depth:1
[285]=283,	-- depth:1
[286]=283,	-- depth:1
[292]=293,	-- depth:1
[294]=293,	-- depth:1
[295]=293,	-- depth:1
[296]=293,	-- depth:1
[297]=298,	-- depth:1
[299]=298,	-- depth:1
[300]=298,	-- depth:1
[301]=298,	-- depth:1
[303]=302,	-- depth:1
[304]=302,	-- depth:1
[282]=283,	-- depth:1
[201]=287,	-- depth:2
[279]=281,	-- depth:1
[204]=290,	-- depth:2
[205]=291,	-- depth:2
[207]=293,	-- depth:1
[216]=1,	-- depth:1
[218]=261,	-- depth:2
[219]=262,	-- depth:2
[220]=263,	-- depth:2
[221]=264,	-- depth:2
[222]=265,	-- depth:1
[244]=287,	-- depth:2
[246]=289,	-- depth:2
[248]=291,	-- depth:2
[250]=293,	-- depth:1
[277]=281,	-- depth:1
[278]=281,	-- depth:1
[280]=281,	-- depth:1
[158]=287,	-- depth:2
[306]=302,	-- depth:1
[47]=262,	-- depth:2
[33]=291,	-- depth:2
[87]=1,	-- depth:1
[89]=261,	-- depth:2
[90]=262,	-- depth:2
[91]=263,	-- depth:2
[92]=264,	-- depth:2
[121]=293,	-- depth:1
[93]=265,	-- depth:1
[119]=291,	-- depth:2
[118]=290,	-- depth:2
[117]=289,	-- depth:2
[74]=289,	-- depth:2
[44]=1,	-- depth:1
[46]=261,	-- depth:2
[48]=263,	-- depth:2
[49]=264,	-- depth:2
[50]=265,	-- depth:1
[115]=287,	-- depth:2
[72]=287,	-- depth:2
[32]=290,	-- depth:2
[130]=1,	-- depth:1
[35]=293,	-- depth:1
[133]=262,	-- depth:2
[5]=263,	-- depth:2
[4]=262,	-- depth:2
[132]=261,	-- depth:2
[136]=265,	-- depth:1
[78]=293,	-- depth:1
[6]=264,	-- depth:2
[305]=304,	-- depth:2
[7]=265,	-- depth:1
[75]=290,	-- depth:2
[29]=287,	-- depth:2
[3]=261,	-- depth:2
[31]=289,	-- depth:2
[135]=264,	-- depth:2
[134]=263,	-- depth:2
[76]=291,	-- depth:2
[258]=306,	-- depth:2
[256]=304,	-- depth:2
[255]=303,	-- depth:2
[254]=302,	-- depth:1
[257]=305,	-- depth:3
[251]=294,	-- depth:2
[241]=284,	-- depth:2
[242]=285,	-- depth:2
[252]=295,	-- depth:2
[243]=286,	-- depth:2
[79]=294,	-- depth:2
[77]=292,	-- depth:2
[249]=292,	-- depth:2
[71]=286,	-- depth:2
[253]=296,	-- depth:2
[70]=285,	-- depth:2
[154]=283,	-- depth:1
[68]=283,	-- depth:1
[19]=277,	-- depth:2
[20]=278,	-- depth:2
[21]=279,	-- depth:2
[22]=280,	-- depth:2
[23]=281,	-- depth:1
[24]=282,	-- depth:2
[25]=283,	-- depth:1
[26]=284,	-- depth:2
[27]=285,	-- depth:2
[28]=286,	-- depth:2
[34]=292,	-- depth:2
[36]=294,	-- depth:2
[69]=284,	-- depth:2
[37]=295,	-- depth:2
[39]=302,	-- depth:1
[40]=303,	-- depth:2
[41]=304,	-- depth:2
[42]=305,	-- depth:3
[43]=306,	-- depth:2
[240]=283,	-- depth:1
[62]=277,	-- depth:2
[63]=278,	-- depth:2
[64]=279,	-- depth:2
[65]=280,	-- depth:2
[66]=281,	-- depth:1
[67]=282,	-- depth:2
[38]=296,	-- depth:2
[239]=282,	-- depth:2
[83]=303,	-- depth:2
[237]=280,	-- depth:2
[194]=280,	-- depth:2
[193]=279,	-- depth:2
[192]=278,	-- depth:2
[191]=277,	-- depth:2
[120]=292,	-- depth:2
[122]=294,	-- depth:2
[123]=295,	-- depth:2
[124]=296,	-- depth:2
[125]=302,	-- depth:1
[126]=303,	-- depth:2
[127]=304,	-- depth:2
[128]=305,	-- depth:3
[129]=306,	-- depth:2
[172]=306,	-- depth:2
[195]=281,	-- depth:1
[171]=305,	-- depth:3
[169]=303,	-- depth:2
[168]=302,	-- depth:1
[167]=296,	-- depth:2
[166]=295,	-- depth:2
[165]=294,	-- depth:2
[163]=292,	-- depth:2
[148]=277,	-- depth:2
[149]=278,	-- depth:2
[150]=279,	-- depth:2
[151]=280,	-- depth:2
[152]=281,	-- depth:1
[157]=286,	-- depth:2
[156]=285,	-- depth:2
[155]=284,	-- depth:2
[170]=304,	-- depth:2
[238]=281,	-- depth:1
[196]=282,	-- depth:2
[198]=284,	-- depth:2
[236]=279,	-- depth:2
[235]=278,	-- depth:2
[234]=277,	-- depth:2
[80]=295,	-- depth:2
[81]=296,	-- depth:2
[82]=302,	-- depth:1
[84]=304,	-- depth:2
[85]=305,	-- depth:3
[86]=306,	-- depth:2
[105]=277,	-- depth:2
[106]=278,	-- depth:2
[107]=279,	-- depth:2
[108]=280,	-- depth:2
[109]=281,	-- depth:1
[197]=283,	-- depth:1
[215]=306,	-- depth:2
[213]=304,	-- depth:2
[212]=303,	-- depth:2
[211]=302,	-- depth:1
[210]=296,	-- depth:2
[209]=295,	-- depth:2
[208]=294,	-- depth:2
[110]=282,	-- depth:2
[206]=292,	-- depth:2
[111]=283,	-- depth:1
[112]=284,	-- depth:2
[113]=285,	-- depth:2
[114]=286,	-- depth:2
[200]=286,	-- depth:2
[199]=285,	-- depth:2
[214]=305,	-- depth:3
[153]=282,	-- depth:2
},
other_default_table={cost_item_id=26158,cost_gold=80,},

open_day_default_table={start_day=1,end_day=5,grade=1,},

reward_default_table={grade=1,step=1,item=item_table[71],},

consume_default_table={grade=1,step=1,cost_item_num=1,},

task_default_table={grade=7,task_id=1,task_type=7,param1=1,param2=0,item_list={[0]=item_table[72]},des="击败BOSS",target=50,open_panel="boss_vip#boss",}

}

