require("game/control_beasts_contract/control_beasts_contract_wg_data")
require("game/control_beasts_contract/control_beasts_contract_lv_up_view")
require("game/control_beasts_contract/control_beasts_contract_hand_book_view")
require("game/control_beasts_contract/control_beasts_contract_preview_view")

ControlBeastsContractWGCtrl = ControlBeastsContractWGCtrl or BaseClass(BaseWGCtrl)

function ControlBeastsContractWGCtrl:__init()
	if ControlBeastsContractWGCtrl.Instance ~= nil then
		ErrorLog("[ControlBeastsContractWGCtrl] attempt to create singleton twice!")
		return
	end

	ControlBeastsContractWGCtrl.Instance = self

	self.data = ControlBeastsContractWGData.New()
	self.control_beasts_contract_lv_up_view = ControlBeastsContractLvUpView.New()
	self.control_beasts_contract_hand_book_view = ControlBeastsContractHandBookView.New()
	self.control_beasts_contract_preview_view = ControlBeastsContractPreviewView.New()
	
    self:BindGlobalEvent(SettingEventType.CLOSE_OTHERS_BEAST, BindTool.Bind(self.CloseOtherBeasts, self))
	self:RegisterAllProtocols()
end

function ControlBeastsContractWGCtrl:__delete()
	ControlBeastsContractWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.control_beasts_contract_lv_up_view:DeleteMe()
	self.control_beasts_contract_hand_book_view:DeleteMe()
	self.control_beasts_contract_preview_view:DeleteMe()
end

-- 隐藏驭兽
function ControlBeastsContractWGCtrl:CloseOtherBeasts()
	Scene.Instance:ChangeShieldBeastObjStatus()
end

function ControlBeastsContractWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCBeastDrawInfo, "OnSCBeastDrawInfo")
end

function ControlBeastsContractWGCtrl:OnSCBeastDrawInfo(protocol)
	self.data:SetBeastDrawInfo(protocol)
	RemindManager.Instance:Fire(RemindName.BeastsContract)
	ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsView, TabIndex.beasts_contract, "all")
	-- local is_open = FunOpen.Instance:GetFunIsOpened(FunName.BeastsContract)
	-- local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
	-- ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.BEAST_CONTRACT, state)
end

function ControlBeastsContractWGCtrl:OpenBeastsContractLvUpView()
	if not self.control_beasts_contract_lv_up_view:IsOpen() then
		self.control_beasts_contract_lv_up_view:Open()
	else
		self.control_beasts_contract_lv_up_view:Flush()
	end
end

function ControlBeastsContractWGCtrl:OpenBeastsContractHandBookView()
	if not self.control_beasts_contract_hand_book_view:IsOpen() then
		self.control_beasts_contract_hand_book_view:Open()
	else
		self.control_beasts_contract_hand_book_view:Flush()
	end
end

function ControlBeastsContractWGCtrl:OpenBeastsContractPreviewView()
	if not self.control_beasts_contract_preview_view:IsOpen() then
		self.control_beasts_contract_preview_view:Open()
	else
		self.control_beasts_contract_preview_view:Flush()
	end
end