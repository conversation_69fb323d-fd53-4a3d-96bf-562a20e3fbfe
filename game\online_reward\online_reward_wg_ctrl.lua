require("game/online_reward/online_reward_wg_data")
require("game/online_reward/online_reward_view")

OnlineRewardWGCtrl = OnlineRewardWGCtrl or BaseClass(BaseWGCtrl)
function OnlineRewardWGCtrl:__init()
    if OnlineRewardWGCtrl.Instance then
		error("[OnlineRewardWGCtrl]:Attempt to create singleton twice!")
	end
	OnlineRewardWGCtrl.Instance = self

    self.data = OnlineRewardWGData.New()
    self.view = OnlineRewardView.New(GuideModuleName.OnlineRewardView)

    self:RegisterProtocol(SCOAOnlineRewardInfo, "OnOnlineRewardInfo")

    self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    self.protocol_had_data = nil
    self.activity_had_data = nil
    self.or_act_status = nil
    self.or_act_remind = nil
end

function OnlineRewardWGCtrl:__delete()
    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    OnlineRewardWGCtrl.Instance = nil
end

function OnlineRewardWGCtrl:OnOnlineRewardInfo(protocol)
    self.data:SetInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    self.protocol_had_data = true

    self:ChangeMianUIBtn()
end

function OnlineRewardWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ONLINE_REWARD then
        self.activity_had_data = true
        self:ChangeMianUIBtn()
    end
end

function OnlineRewardWGCtrl:ChangeMianUIBtn()
    if self.protocol_had_data == nil or self.activity_had_data == nil then
        return
    end

    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ONLINE_REWARD)
    local cfg = self.data:GetRewardCfg()
    local is_show = cfg ~= nil and is_open
    local reward_status = self.data:GetRewardSatus()
    local remind = reward_status == REWARD_STATE_TYPE.CAN_FETCH
    if self.or_act_status ~= is_show or self.or_act_remind ~= remind then
        self.or_act_status = is_show
        self.or_act_remind = remind
        MainuiWGCtrl.Instance:FlushView(0, "online_reward", {is_open = is_show, is_remind = remind})
    end
end

