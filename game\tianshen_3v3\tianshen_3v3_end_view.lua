-- 天神3v3结算面板
TianShen3v3EndView = TianShen3v3EndView or BaseClass(SafeBaseView)
function TianShen3v3EndView:__init()
	self:SetMaskBg(false)
	self.active_close = false
	self.view_layer = UiLayer.PopTop
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_end")
end

function TianShen3v3EndView:__delete()
end

function TianShen3v3EndView:ReleaseCallBack()
	for k, side in pairs(TS3V3_SIDE) do
		if self["role_info_list_" .. side] then
			self["role_info_list_" .. side]:DeleteMe()
			self["role_info_list_" .. side] = nil
		end
	end
	CountDownManager.Instance:RemoveCountDown("TianShen3v3EndView")
end

function TianShen3v3EndView:LoadCallBack()
	for k, side in pairs(TS3V3_SIDE) do
		self["role_info_list_" .. side] = AsyncListView.New(TianShen3v3EndRoleInfoItem, self.node_list["role_info_list_"  .. side])
	end
	XUI.AddClickEventListener(self.node_list["out_btn"], BindTool.Bind(self.OnClickOutBtn, self))
end

function TianShen3v3EndView:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown("TianShen3v3EndView")
	Field1v1WGCtrl.Instance:LeaveZhanChangScene()
end

function TianShen3v3EndView:SetData(finish_info_list, main_role_add_score, kick_role_timestamp, win_side)
	self.finish_info_list = finish_info_list
	self.main_role_add_score = main_role_add_score
	self.kick_role_timestamp = kick_role_timestamp
	self.win_side = win_side
end

function TianShen3v3EndView:OnFlush()
	for k, side in pairs(TS3V3_SIDE) do
		self["role_info_list_" .. side]:SetDataList(self.finish_info_list[side].role_info_list)
		self.node_list["score_" .. side].text.text = string.format(Language.TianShen3v3.SideNameScore[side], self.finish_info_list[side].win_score)
	end

	-- 赛季积分
	local cur_season_score = TianShen3v3WGData.Instance:GetSeasonScore()
	local _, next_grade_cfg = TianShen3v3WGData.Instance:GetGradeCfgByScore(cur_season_score)
	self.node_list["score"].text.text = string.format(Language.TianShen3v3.Score, cur_season_score, next_grade_cfg.score)

	-- 增加的积分
	self.node_list["add_score"].text.text = string.format(Language.TianShen3v3.AddScore, self.main_role_add_score) 

	local grade_cfg, next_grade_cfg = TianShen3v3WGData.Instance:GetGradeCfgByScore(TianShen3v3WGData.Instance:GetSeasonScore())
	if grade_cfg then
		-- 段位名称
		self.node_list["stage_name"].text.text = grade_cfg.grade_name
		ChangeToQualityText(self.node_list["stage_name"].text, RankGradeEnum[grade_cfg.grade])
		-- 星数
		for i = 1, 5 do
			self.node_list["star_" .. i]:SetActive(i <= grade_cfg.star)
		end
		-- 进度条
		local max_score = next_grade_cfg.score - grade_cfg.score
		local cur = TianShen3v3WGData.Instance:GetSeasonScore() - grade_cfg.score
		self.node_list["stage_progress_effect"]:SetActive(true)
		self.node_list["stage_progress"].image.fillAmount = 0
		self.node_list["stage_progress"].image:DOFillAmount(cur / max_score, 2):OnComplete(function()
			self.node_list["stage_progress_effect"]:SetActive(false)
		end)
	else
		print_error("没取到段位配置，请检查！， score:", TianShen3v3WGData.Instance:GetSeasonScore())
	end
	-- 段位图标
	self.node_list["stage_icon"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("duanwei_icon" .. grade_cfg.grade))

	-- 背景
	self.node_list["fail_bg"]:SetActive(self.win_side ~= TianShen3v3WGData.Instance:GetMySide())
	self.node_list["win_bg"]:SetActive(self.win_side == TianShen3v3WGData.Instance:GetMySide())


	-- 刷新退出按钮文字
	self:FlushOutBtnText()
end

	-- 刷新退出按钮文字
function TianShen3v3EndView:FlushOutBtnText()
	self:UpdateOutBtnText()
	CountDownManager.Instance:RemoveCountDown("TianShen3v3EndView")
	CountDownManager.Instance:AddCountDown("TianShen3v3EndView", BindTool.Bind(self.UpdateOutBtnText, self), BindTool.Bind(self.Close, self), self.kick_role_timestamp, nil, 1)
end

function TianShen3v3EndView:UpdateOutBtnText()
	local seconds = math.floor(self.kick_role_timestamp - TimeWGCtrl.Instance:GetServerTime()) 
	self.node_list["out_btn_text"].text.text = string.format(Language.TianShen3v3.OutText, seconds) 
end

-- 点击退出按钮
function TianShen3v3EndView:OnClickOutBtn()
	self:Close()
end

function TianShen3v3EndView:OnClickCloseWindow()
	self:OnClickOutBtn()
end

-----------------------TianShen3v3RuleItem------------
TianShen3v3EndRoleInfoItem = TianShen3v3EndRoleInfoItem or BaseClass(BaseRender)

function TianShen3v3EndRoleInfoItem:__init()
	self.head_cell = BaseHeadCell.New(self.node_list["head_cell"])
end


function TianShen3v3EndRoleInfoItem:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
	
end

function TianShen3v3EndRoleInfoItem:OnFlush()
	if self.data == nil then return end

	for k, side in pairs(TS3V3_SIDE) do
		self.node_list["bg_" .. side]:SetActive(side == self.data.side)
		self.node_list["top_panel_bg_" .. side]:SetActive(side == self.data.side)
    end

    local is_main_role = self.data.uuid_str == RoleWGData.Instance:GetUUIDStr()
    local name_color = is_main_role and COLOR3B.D_GREEN or "#F9F3EBFF"

	local plat_type, uid = LLStrToInt(self.data.uuid_str)
	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.tianshen_index)
	self.node_list["role_name"].text.text = ToColorStr(self.data.role_name, name_color)

	-- spine动画
	local bundle_name, asset_name = ResPath.GetTianShen3v3Spine(tianshen_cfg.appe_image_id)
	self.node_list["tianshen_spine"]:ChangeAsset(bundle_name, asset_name)

	-- 头像
	local data = {}
	data.role_id = uid
	data.prof = self.data.prof
	data.sex = self.data.sex
	self.head_cell:SetData(data)

	-- 服务器
	self.node_list["server_id"].text.text = ToColorStr("【s" .. self.data.server_id .. "】", name_color)
	self.node_list["kill_amount"].text.text = string.format(Language.TianShen3v3.KillAmountStr, self.data.kill_times)
	self.node_list["dead_amount"].text.text = string.format(Language.TianShen3v3.DeadAmountStr, self.data.dead_times)
end