require("game/calendar/calendar_wg_data")
require("game/calendar/calendar_view")
require("game/calendar/openning_act_notice_view")

-- 周历
CalendarWGCtrl = CalendarWGCtrl or BaseClass(BaseWGCtrl)

function CalendarWGCtrl:__init()
	if CalendarWGCtrl.Instance ~= nil then
		ErrorLog("[CalendarWGCtrl] attempt to create singleton twice!")
		return
	end

	CalendarWGCtrl.Instance = self
	self.data = CalendarWGData.New()
	self.view = CalendarView.New(GuideModuleName.CalendarView)
	self.openning_act_view = OpenningActNoticeView.New(GuideModuleName.OpenningActNoticeView)

	-- 活动监听
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	-- 玩家等级改变监听
	self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level", "prof"})

	-- 跨天
	self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
end

function CalendarWGCtrl:__delete()
	self:CancelTimer()

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	if RoleWGData.Instance and self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if nil ~= self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	CalendarWGCtrl.Instance = nil
end

-- 活动状态改变
function CalendarWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if status == ACTIVITY_STATUS.OPEN then
		self:CheckOpenOpenningActNotice()
	end

	CalendarWGData.Instance:ClearCalendarActivityCfg()
	self:CancelDelayFlushTimer()
	self.flush_delay_time = GlobalTimerQuest:AddDelayTimer(function ()
		CalendarWGData.Instance:ClearCalendarActivityCfg()
		MainuiWGCtrl.Instance:FlushView(0, "FlushCalendar")
	end, 3) -- 延迟一下再计算，避免客户端时间误差导致计算错误
end

-- 等级更变
function CalendarWGCtrl:RoleLevelChange(attr_name, value)
	if attr_name == "level" then
		self:CheckOpenOpenningActNotice()
	end
	CalendarWGData.Instance:ClearCalendarActivityCfg()
end

function CalendarWGCtrl:OnDayPass()
	CalendarWGData.Instance:ClearCalendarActivityCfg()
end

-- 检查打开进行中活动提醒
function CalendarWGCtrl:CheckOpenOpenningActNotice()
	-- 屏蔽周历的主界面活动开启提醒
	-- self:CancelTimer()
	-- self.delay_check_timer = GlobalTimerQuest:AddDelayTimer(function ()
	-- 	local cfg = CalendarWGData.Instance:GetUnClickOpenningActivityCfg()
	-- 	if not IsEmptyTable(cfg) then
	-- 		ViewManager.Instance:Open(GuideModuleName.OpenningActNoticeView, nil, "data", {cfg = cfg[#cfg]})
	-- 	end
	-- end, 1.5)
end

function CalendarWGCtrl:CancelTimer()
	if self.delay_check_timer then
		GlobalTimerQuest:CancelQuest(self.delay_check_timer)
		self.delay_check_timer = nil
	end
end

function CalendarWGCtrl:OnClickAct(act_data)
	if act_data.is_hunyan then
		WeddingWGCtrl.Instance:WeddingDeMandViewOpen()
		return
	end
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(act_data.cfg.act_type)
	if act_data.is_openning then
		ViewManager.Instance:CloseAll()
		ActIvityHallWGCtrl.Instance:OpenActivity(act_data.cfg.act_type)
	else
		if act_data.cfg.open_panel_name and act_data.cfg.open_panel_name ~= "" then
			FunOpen.Instance:OpenViewNameByCfg(act_data.cfg.open_panel_name)
		else
			ViewManager.Instance:Open(GuideModuleName.BiZuo, BiZuoView.TabIndex.BB)
		end
		-- BiZuoWGCtrl.Instance.activity_desc_view:SetData(act_data.cfg)
		-- BiZuoWGCtrl.Instance.activity_desc_view:Open()
	end
end

function CalendarWGCtrl:CancelDelayFlushTimer()
    if self.flush_delay_time ~= nil then
        GlobalTimerQuest:CancelQuest(self.flush_delay_time)
    end
    self.flush_delay_time = nil
end