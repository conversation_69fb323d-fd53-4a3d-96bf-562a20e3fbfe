SHJAttrView = SHJAttrView or BaseClass(SafeBaseView)


function SHJAttrView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_all_attr")
end

function SHJAttrView:ReleaseCallBack()

end

function SHJAttrView:ShowIndexCallBack(index)
	self:Flush()
end

function SHJAttrView:OnFlush()
	local jc_attr_list, sx_attr_list, jb_attr_list, sj_attr_list, special_add_attr = ShanHaiJingWGData.Instance:GetAttrInfoShow()
	self:FlushAttr(jc_attr_list,"jc_attr_",false,special_add_attr)
	self:FlushAttr(sx_attr_list,"xj_attr_",false,special_add_attr)
	self:FlushAttr(jb_attr_list,"jb_attr_",true,special_add_attr)

end

-- 刷新属性
function SHJAttrView:FlushAttr(attr_list,attr_head, is_last,special_add_attr)
	if not attr_list or not attr_head then return end

	local index = 0
	local attr_name = Language.Common.AttrNameList2
	local sort_list = AttributeMgr.SortAttribute()
	local is_per 
	local pre_add = 1 + special_add_attr / 100

	for k,v in pairs(sort_list) do
		if attr_list[v] ~= 0 then
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
			index = index + 1
			self.node_list[attr_head .. index].text.text = "   "..attr_name[v] .. ":                      " .. ToColorStr(is_per and (attr_list[v]/100*pre_add .. '%') or math.floor(attr_list[v]*pre_add) , "#edc7b0")	
		end
	end

	if is_last and special_add_attr then
		if special_add_attr > 0 or index > 0 then
			index = index + 1
			self.node_list[attr_head .. index].text.text = "   "..Language.ShanHaiJing.AddPerTuJianAttr .. ":                      " .. ToColorStr(special_add_attr.."%", "#edc7b0")		
		end
	end

	if index == 0 then
		--if not is_last then
			index = 1
			self.node_list[attr_head .. index].text.text =Language.ShanHaiJing.ViewTitle_3
		--else
			--self.node_list.textwu:SetActive(is_last)
		--end
	end

	for i=1,12 do
		self.node_list[attr_head .. i]:SetActive(index >= i)
	end
end
