FollowAim = FollowAim or BaseClass(BaseRender)

local def_pos_info = {x = 10, y = 40, z = 0}

function FollowAim:__init()
	self.obj_type = nil
	self.follow_parent = nil
	self.async_loader = nil
	self.aim_vis = false
	self.aim_offset_info = nil
	self.is_create = false
end

function FollowAim:__delete()
	self.follow_parent = nil
	self.aim_vis = false
	self.aim_offset_info = nil
	self.is_create = false
end

function FollowAim:SetFollowParent(obj_type, follow_parent)
	self.obj_type = obj_type
	if follow_parent then
		self.follow_parent = follow_parent.transform
	end
end

function FollowAim:CreateAim()
	self.is_create = true
	self.aim_vis = true
	self.async_loader = AllocAsyncLoader(self, "root_loader")
	self.async_loader:SetIsUseObjPool(true)
	self.async_loader:SetIsInQueueLoad(true)
	self.async_loader:SetParent(self.follow_parent, false)
	self.async_loader:Load("uis/view/miscpre_load_prefab", "AimEff",
		function (gameobj)
			if IsNil(self.follow_parent) then
				self.async_loader:Destroy()
				return
			end

			if not self.aim_vis then
				self.async_loader:Destroy()
				return
			end

			self:SetInstance(gameobj)

			if not IsEmptyTable(self.aim_offset_info) then
				def_pos_info = self.aim_offset_info
			end
			
			self.view:SetLocalPosition(def_pos_info.x or 0, def_pos_info.y or 0, def_pos_info.z or 0)
			self.view:SetActive(true)
		end)
end

function FollowAim:SetIsShow(value)
	self.aim_vis = value
	if self.aim_vis then
		if not self.is_create then
			self:CreateAim()
		end
	end

	if self.view ~= nil then
		self.view:SetActive(self.aim_vis)
	end
end

function FollowAim:SetPosOffset(offset_info)
	self.aim_offset_info = offset_info
	if self.view ~= nil and not IsEmptyTable(offset_info) then
		self.view:SetLocalPosition(offset_info.x or 0, offset_info.y or 0, offset_info.z or 0)
	end
end
