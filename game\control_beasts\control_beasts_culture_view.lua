local AUTO_CULTURE_TIME = 0.1
local SHOW_TYPE = 
{
    CULTURE = 0,            -- 培养
    FLAIR = 1,              -- 资质
    SKILL = 2,
}

local BEAST_FLAIR_TYPR = 
{
    CHANGE = 1,            -- 转换
    SWALLOW = 2,           -- 吞噬
}

function ControlBeastsWGView:LoadCultureViewCallBack()
    self.is_culture_auto = false                -- 自动升级
    self.is_auto_change_flair = false           -- 自动洗髓
    self.cur_culture_beast_data = nil           -- 当前选中的幻兽
	self.cur_culture_beast_index = nil          -- 当前选中的幻兽index
    self.old_level = nil                        -- 之前的等级
    self.old_exp = nil                          -- 之前的经验
    self.beast_model_res_id = nil               -- 幻兽与模型资源id       
    self.culture_loaded = true                  -- 培养界面是否已加载

    -- 基础属性
    if self.culture_attrlist == nil then
        self.culture_attrlist = {}
        for i = 1, 10 do
            local attr_obj = self.node_list.layout_culture_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.culture_attrlist[i] = cell
            end
        end
    end

    -- 资质属性
    if self.flair_attrlist == nil then
        self.flair_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.layout_flair_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststFlairItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.flair_attrlist[i] = cell
            end
        end
    end

    if not self.beasts_culture_sxd_list then
        self.beasts_culture_sxd_list = {}
        for i = 0, 2 do
            local culture_sxd_obj = self.node_list.beasts_culture_sxd_list:FindObj(string.format("culture_sxd_list_%d", i))
            local culture_sxd_cell = BeastsCultureSXDRender.New(culture_sxd_obj)
            culture_sxd_cell:SetIndex(i)
            self.beasts_culture_sxd_list[i] = culture_sxd_cell
        end
    end

	if not self.beast_model then
		self.beast_model = RoleModel.New()
		self.beast_model:SetUISceneModel(self.node_list["display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.beast_model, TabIndex.beasts_culture)
	end

    -- 星数
    if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star" .. i]
		end
	end

    -- 等级
    if not self.lv_list then
        self.lv_list = {}
        self.lv_effect = {}

        for i = 1, 10 do
            self.lv_list[i] = self.node_list["lv_img_" .. i]
            self.lv_effect[i] = self.node_list["lv_effect_" .. i]
        end
    end

    -- 洗练消耗品
    if not self.culture_flair_change_item then
        self.culture_flair_change_item = ItemCell.New(self.node_list.culture_flair_change_item)
    end

    if not self.active_skill_render then
        self.active_skill_render = BeastCultueSkillRender.New(self.node_list.active_skill_render)
    end

    if not self.normal_skill_render then
        self.normal_skill_render = BeastCultueSkillRender.New(self.node_list.normal_skill_render)
    end

    if not self.owner_skill_render then
        self.owner_skill_render = BeastCultueSkillRender.New(self.node_list.owner_skill_render)
    end

    if not self.culture_holy_beast_item then
        self.culture_holy_beast_item = ItemCell.New(self.node_list["culture_holy_beast_cell_root"])
        self.culture_holy_beast_item:SetIsShowTips(false)
    end

    XUI.AddClickEventListener(self.node_list.culture_btn, BindTool.Bind(self.ChangeShowRoot, self, SHOW_TYPE.CULTURE))
    XUI.AddClickEventListener(self.node_list.flair_btn, BindTool.Bind(self.ChangeShowRoot, self, SHOW_TYPE.FLAIR))
    XUI.AddClickEventListener(self.node_list.skill_btn, BindTool.Bind(self.ChangeShowRoot, self, SHOW_TYPE.SKILL))
    XUI.AddClickEventListener(self.node_list.btn_culture_upgrade, BindTool.Bind(self.OperateAutoCulture, self))
    XUI.AddClickEventListener(self.node_list.btn_culture_upgrade_50, BindTool.Bind(self.OperateCultureToLv50, self))
    XUI.AddClickEventListener(self.node_list.culture_go_to_draw, BindTool.Bind(self.OpenBeastsPrizeDraw, self))
    XUI.AddClickEventListener(self.node_list.culture_beasts_skill_show, BindTool.Bind2(self.CultureBeastsSkillShow, self))
    XUI.AddClickEventListener(self.node_list.fair_attr_tips, BindTool.Bind2(self.CultureFlairTips, self))
    XUI.AddClickEventListener(self.node_list.beasts_culture_hand_book_btn, BindTool.Bind(self.LookHandBook, self))                  	-- 图鉴

    XUI.AddClickEventListener(self.node_list.flair_change_ok_btn, BindTool.Bind(self.BtnChangeFlairOk, self))
    XUI.AddClickEventListener(self.node_list.flair_change_cancel_btn, BindTool.Bind2(self.BtnChangeFlairCancel, self))
    XUI.AddClickEventListener(self.node_list.flair_change_quick_btn, BindTool.Bind2(self.BtnChangeFlairQuick, self))
    XUI.AddClickEventListener(self.node_list.flair_change_once_btn, BindTool.Bind2(self.BtnChangeFlairOnce, self))
    XUI.AddClickEventListener(self.node_list.flair_change_break, BindTool.Bind2(self.BtnChangeFlairBreak, self))
    XUI.AddClickEventListener(self.node_list.beasts_culture_despose_btn, BindTool.Bind2(self.BtnCultueDespose, self))
    XUI.AddClickEventListener(self.node_list.beasts_culture_skin_btn, BindTool.Bind2(self.OnClickBeastsSkinBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_holy_beasts, BindTool.Bind(self.OnClickHolyBeastsBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_culture_beast_select, BindTool.Bind(self.OnClickBeastsContractBtn, self))
    XUI.AddClickEventListener(self.node_list.beasts_culture_sxd_attr_btn, BindTool.Bind(self.OnClickBeastsSxdBtn, self))
    XUI.AddClickEventListener(self.node_list.multiple_flair_change_root, BindTool.Bind(self.OnClickMultipleFlairChange, self))

	if self.culture_remind_callback == nil then
		self.culture_remind_callback = BindTool.Bind(self.OnCultureRemindChange, self)
		RemindManager.Instance:Bind(self.culture_remind_callback, RemindName.HolyBeasts)
	end
end

function ControlBeastsWGView:OpenCultureViewCallBack()
end

function ControlBeastsWGView:CloseCultureViewCallBack()
    self.cur_select_type = nil
    self.is_culture_auto = false
    self.is_auto_change_flair = false
    self.cur_culture_beast_data = nil
	self.cur_culture_beast_index = nil
    self.old_level = nil
    self.old_exp = nil
    self.last_lv_cache = nil
    self:CleanCultureCoolTime()
    self:RemoveSendChangeDelayTimer()
    self:StopAutoCultureOperate()
end

-- 切换页签时关掉自动
function ControlBeastsWGView:ChangeCutureTableIndex()
    self.old_level = nil
    self.old_exp = nil

    if self.culture_loaded then
        self:StopAutoCultureOperate()
        self:RemoveSendChangeDelayTimer()
        self.is_auto_change_flair = false
    end
end

function ControlBeastsWGView:ShowCultureViewCallBack()
end

function ControlBeastsWGView:ReleaseCultureViewCallBack()
	if self.culture_attrlist and #self.culture_attrlist > 0 then
		for _, culture_cell in ipairs(self.culture_attrlist) do
			culture_cell:DeleteMe()
			culture_cell = nil
		end

		self.culture_attrlist = nil
	end

    if self.flair_attrlist and #self.flair_attrlist > 0 then
		for _, flair__cell in ipairs(self.flair_attrlist) do
			flair__cell:DeleteMe()
			flair__cell = nil
		end

		self.flair_attrlist = nil
	end

    if self.beast_model then
        self.beast_model:DeleteMe()
        self.beast_model = nil
    end

    if self.culture_flair_change_item then
        self.culture_flair_change_item:DeleteMe()
        self.culture_flair_change_item = nil
    end

    if nil ~= self.culture_alert then
        self.culture_alert:DeleteMe()
        self.culture_alert = nil
    end

    if self.active_skill_render then
        self.active_skill_render:DeleteMe()
        self.active_skill_render = nil
    end

    if self.normal_skill_render then
        self.normal_skill_render:DeleteMe()
        self.normal_skill_render = nil
    end

    if self.owner_skill_render then
        self.owner_skill_render:DeleteMe()
        self.owner_skill_render = nil
    end

    if self.culture_holy_beast_item then
        self.culture_holy_beast_item:DeleteMe()
        self.culture_holy_beast_item = nil
    end

    if self.beasts_culture_sxd_list then
        for i = 0, 2 do
            if self.beasts_culture_sxd_list[i] then
                self.beasts_culture_sxd_list[i]:DeleteMe()
            end
        end

        self.beasts_culture_sxd_list = nil
    end

    self.beast_model_res_id = nil
    self.star_list = nil
    self.lv_list = nil
    self.culture_loaded = false
    self.is_culture_auto = false
    self.is_auto_change_flair = false
    self.is_select_multiple_flair_change = nil
    self.multiple_flair_value = nil
    self:CleanCultureCoolTime()
    self:CultureClearGradeSliderTween()
    self:RemoveSendChangeDelayTimer()

    if self.culture_remind_callback then
		RemindManager.Instance:UnBind(self.culture_remind_callback)
		self.culture_remind_callback = nil
	end
end

function ControlBeastsWGView:OnCultureRemindChange(remind_name, num)
	if remind_name == RemindName.HolyBeasts then
		if self.node_list["holy_beasts_remind"] then
			self.node_list["holy_beasts_remind"]:SetActive(num > 0)
		end
	end
end

function ControlBeastsWGView:CleanCultureCoolTime()
    if self.opeate_cool_timer then
        GlobalTimerQuest:CancelQuest(self.opeate_cool_timer)
        self.opeate_cool_timer = nil
    end
end

function ControlBeastsWGView:OnSelectCultureBeastCB()
    if nil == self.select_beast_data then
		return
	end

    if self.cur_culture_beast_index ~= self.select_beast_index or self.cur_culture_beast_data ~= self.select_beast_data then
        self.old_level = nil
        self.old_exp = nil
        self.is_auto_change_flair = false
        self:StopAutoCultureOperate()
        self:CultureClearGradeSliderTween()
    end

	self.cur_culture_beast_index = self.select_beast_index
    self.cur_culture_beast_data = self.select_beast_data

    -- 强制选中强化页签
    local is_limited = self.cur_culture_beast_data.is_holy_beast and self.cur_culture_beast_data.server_data.holy_spirit_link_index == -1
    if is_limited then
        self:ChangeShowRoot(SHOW_TYPE.CULTURE, true)
    end

    self:FlushPlayCultureBeastAim()
end

----------------------------------------------------------------------------------
function ControlBeastsWGView:FlushCultureViewCallBack(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushCultureBeastsList()
            self:FlushCultureBeastsSXD()

            local is_open = FunOpen.Instance:GetFunIsOpened(FunName.HolyBeastsView)
            self.node_list["btn_holy_beasts"]:SetActive(is_open)
        elseif k == "culture_beasts_sxd" then
            self:FlushCultureBeastsSXD()
        end
    end
end

-- 刷新属性丹
function ControlBeastsWGView:FlushCultureBeastsSXD()
    -- 刷新属性丹
    local cfg = ControlBeastsWGData.Instance:GetBeastsPelletCfg()
    if self.beasts_culture_sxd_list then
        for i = 0, 2 do
            local cfg_data = cfg[i]
            self.beasts_culture_sxd_list[i]:SetVisible(cfg_data ~= nil)

            if cfg[i] ~= nil then
                self.beasts_culture_sxd_list[i]:SetData(cfg_data)
            end
        end
    end
end

-- 刷新所有已孵化的灵兽
function ControlBeastsWGView:FlushCultureBeastsList()
    if self.select_beast_index ~= nil then
        self:OnSelectCultureBeastCB()
    end
end

-- 走进度
function ControlBeastsWGView:FlushPlayCultureBeastAim()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.old_level == nil or self.old_exp == nil then
        self:FlushCultureBeastAll()
        return
    end

    -- self:FlushCultureBeastAll()

    local server_data = self.cur_culture_beast_data.server_data
    local level_cfg = ControlBeastsWGData.Instance:GetBeastLevelCfgByLevel(server_data.beast_level)
    local need_exp = level_cfg and level_cfg.need_exp or 100
    
    if self.old_level == server_data.beast_level then
        if self.old_exp == server_data.exp then
            self:FlushCultureBeastAll()
        else
            self:CultureBeastProgressAni(server_data.exp / need_exp, false, function ()
                self:FlushCultureBeastAll()
            end)
        end
    else
        self:CultureBeastProgressAni(1, true, function ()
            self:FlushCultureBeastAll()
            self:BeastOperateFinalEffect(UIEffectName.s_shengji)
        end)
    end
end

function ControlBeastsWGView:CultureBeastProgressAni(aim_value, is_full, complete_func)
	self:CultureClearGradeSliderTween()
	local slider = self.node_list.culture_progress.slider

	if is_full then
		aim_value = 1
	end

	if aim_value == 0 then
		if complete_func then
			complete_func()
		end

		return
	end

	local time = tonumber(string.format("%.2f", aim_value * 0.2))
	self.culture_beast_slider_tween = slider:DOValue(aim_value, time)
    self.is_anim = true
	self.culture_beast_slider_tween:OnComplete(function ()
		if is_full then
			slider.value = 0
		end

		if complete_func then
			complete_func()
		end

        self.is_anim = false
	end)
end

-- 清空动画
function ControlBeastsWGView:CultureClearGradeSliderTween()
    if self.culture_beast_slider_tween then
        self.culture_beast_slider_tween:Kill()
        self.culture_beast_slider_tween = nil
    end
end

-- 刷新培养界面
function ControlBeastsWGView:FlushCultureBeastAll()
    self:FlushCultureSelectType()
    self:FlushCultureBeastModel()
    self:FlushCultureBeastMessage()
    self:FlushCultureBeastLevelMessage()
    -- 刷新资质相关
    self:FlushCultureBeastFlairMessage()
    -- 刷新技能相关
    self:FlushCultureBeastSkillMessage()
end

function ControlBeastsWGView:FlushCultureSelectType()
    if self.cur_select_type == nil then
        self.cur_select_type = SHOW_TYPE.CULTURE

        if self.cur_culture_beast_data ~= nil  then
            local is_change_flair_red = ControlBeastsWGData.Instance:RefreshBeastChangeFlair(self.cur_culture_beast_data)
            local is_grade_red = ControlBeastsWGData.Instance:RefreshBeastUpGrade(self.cur_culture_beast_data)
            if is_change_flair_red and (not is_grade_red) then
                self.cur_select_type = SHOW_TYPE.FLAIR
            end
        end
    end

    self:FlushCultureButtonState()
end

-- 刷新模型
function ControlBeastsWGView:FlushCultureBeastModel()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    local server_data = self.cur_culture_beast_data.server_data
    local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(server_data.beast_id, server_data.use_skin)

    if self.beast_model_res_id ~= res_id then
        self.beast_model_res_id = res_id
        local bundle, asset = ResPath.GetBeastsModel(res_id)
        self.beast_model:SetMainAsset(bundle, asset)
        self.beast_model:PlayRoleAction(SceneObjAnimator.Rest)
    end
end

-- 刷新中间的模型星级和等级
function ControlBeastsWGView:FlushCultureBeastMessage()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    local server_data = self.cur_culture_beast_data.server_data
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local stand_by_slot = server_data.stand_by_slot ~= -1        -- 没有上阵
    self.node_list.beasts_battle_img:CustomSetActive(stand_by_slot)

    if stand_by_slot then
        local battle_type_str = "a3_hs_bs_new_zz"
        if server_data.stand_by_slot > 2 then
            battle_type_str = "a3_hs_bs_new_fz"
        end

        local bundle, asset = ResPath.GetControlBeastsImg(battle_type_str)
        self.node_list.beasts_battle_img.image:LoadSprite(bundle, asset)
    end
    
    -- 皮肤按钮
    local skin_cfg_list = ControlBeastsWGData.Instance:GetSkinCfgListByBeastId(server_data.beast_id)
    self.node_list["beasts_culture_skin_btn"]:SetActive(#skin_cfg_list > 0)
    local skin_red = ControlBeastsWGData.Instance:GetBeastsSkinRed(self.cur_culture_beast_data)
    self.node_list["beasts_skin_btn_remind"]:SetActive(skin_red)

    if beast_cfg then
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
        self.node_list.active_jingshen_img.image:LoadSprite(bundle, asset, function()
            self.node_list.active_jingshen_img.image:SetNativeSize()
        end)

        if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
            bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
            self.node_list.active_jingshen_img:ChangeAsset(bundle, asset)
        end

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
        local no_have_star = "a3_ty_xx_zc0"
    	for k,v in pairs(self.star_list) do
            v:CustomSetActive(star_res_list[k] ~= no_have_star)
        	v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
    	end

        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
        self.node_list.beasts_element_img.image:LoadSprite(bundle, asset, function()
            self.node_list.beasts_element_img.image:SetNativeSize()
        end)

        self.node_list.beasts_name.text.text = beast_cfg.beast_name
        self.node_list.beasts_level.text.text = server_data.beast_level
    end
end

function ControlBeastsWGView:MulAttributeRemovePer(attr_list, num)
    for i = 1, #attr_list do
        if not EquipmentWGData.Instance:GetAttrIsPer(attr_list[i].attr_str) then
            attr_list[i].attr_value = math.floor(attr_list[i].attr_value or 0 * num)
            attr_list[i].add_value = math.floor(attr_list[i].add_value or 0 * num)
        end
    end
    return attr_list
end

-- 刷新等级相关
function ControlBeastsWGView:FlushCultureBeastLevelMessage()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    -- 属性
    local server_data = self.cur_culture_beast_data.server_data
    local attr_list = ControlBeastsWGData.Instance:GetBeastLevelAttrListNew(server_data.beast_id, server_data.beast_level, true, server_data.refine_attr_list, false, true)
    if attr_list then
        -- 天道石影响属性百分比 升星属性
	    local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.ControlBeasts)
	    charm_rate = charm_rate / 10000
	    --- 
        attr_list = self:MulAttributeRemovePer(attr_list, charm_rate + 1)
        for index, attr_cell in ipairs(self.culture_attrlist) do
            attr_cell:SetVisible(attr_list[index] ~= nil)

            if attr_list[index] ~= nil then
                attr_cell:SetData(attr_list[index])
            end
        end
    end

	local cap = ControlBeastsWGData.Instance:GetBeastsCapValue(server_data)
    self.node_list.beasts_cap_value.text.text = cap

    -- 修改等级相关
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local beast_star = beast_cfg and beast_cfg.beast_star or 0
    local starup_beast_id = beast_cfg and beast_cfg.starup_beast_id or 0
    local next_beast_star = beast_star + 1
    local level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(beast_star)
    local next_level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(next_beast_star)
    local is_full_level = server_data.beast_level >= level_limit_cfg.beast_max_level
    local next_level = server_data.beast_level + 1
    local upgrade_red = self.cur_culture_beast_data.is_can_upgrade

    -- 设置等级状态
    self.node_list.culture_upgrade_full_root:CustomSetActive(is_full_level)
    self.node_list.culture_upgrade_root:CustomSetActive(not is_full_level)
    local last_lv = server_data.beast_level % 10
    local last_lv_bai = server_data.beast_level / 10

    if last_lv == 0 and last_lv_bai ~= 0 then
        last_lv = 10
    end

    for i, v in ipairs(self.lv_list) do
        local str = i <= last_lv and "a3_hs_huo1" or "a3_hs_huo2"
        local bundle, asset = ResPath.GetControlBeastsImg(str)
        v.image:LoadSprite(bundle, asset)

        if self.lv_effect[i] then
            self.lv_effect[i]:CustomSetActive(i <= last_lv)
        end
    end
    
    self.node_list.culture_now_lv.text.text = string.format(Language.Rank.Level, 
        string.format("<color=#6cff95>%d</color>/%d", server_data.beast_level, 
        level_limit_cfg.beast_max_level)
    )
    self.node_list.culture_upgrade_remind:CustomSetActive(upgrade_red)
    self.old_level = server_data.beast_level
    self.old_exp = server_data.exp

    if is_full_level then
        --判断是等级上限了还是满级了
        local is_level_full = false

        if next_level_limit_cfg ~= nil then
            if (server_data.beast_level == level_limit_cfg.beast_max_level and 
            server_data.beast_level == next_level_limit_cfg.beast_max_level) or 
            starup_beast_id == 0
            then
                is_level_full = true
            end
        else
            is_level_full = true
        end

        local full_desc = Language.ContralBeasts.CultureText13

        if not is_level_full then
            local star_str = ToColorStr(string.format("%s%s", next_beast_star, Language.Common.Star), COLOR3B.GREEN)
            local level_str = ToColorStr(string.format("%s%s", next_level_limit_cfg.beast_max_level, Language.Common.Ji), COLOR3B.GREEN)
            full_desc = string.format(Language.ContralBeasts.CultureText12, star_str, level_str)
        end

        self.node_list.culture_upgrade_full_txt.text.text = full_desc
    else
        local level_cfg = ControlBeastsWGData.Instance:GetBeastLevelCfgByLevel(server_data.beast_level)
        if level_cfg then   -- 升一级的展示
            self.node_list.culture_text_progress.text.text = string.format("%d/%d", server_data.exp, level_cfg.need_exp)
            self.node_list.culture_progress.slider.value = server_data.exp / level_cfg.need_exp
            self.node_list.beast_exp_text.text.text = level_cfg.need_exp - server_data.exp
        end

        local can_arrived_lv, arrived_lv_need_exp = ControlBeastsWGData.Instance:GetCanArrivedLevel(server_data, server_data.beast_level + 50)
        self.node_list.btn_culture_upgrade_50:CustomSetActive(can_arrived_lv - server_data.beast_level ~= 0)

        if can_arrived_lv ~= 0 then
            self.node_list.culture_upgrade_50_tex.text.text = string.format(Language.ExpPool.ZhiUpLevel1, can_arrived_lv - server_data.beast_level)
            self.node_list.beast_exp_50_text.text.text = arrived_lv_need_exp
        end

        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.beast_upgrade_50_exp.rect)
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.beast_upgrade_exp.rect)
    end

    -- 刷新技能
    local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(server_data.beast_id)
    if map_data then
        if map_data.beast_data then
            local skill_id = map_data.beast_data.skill_id
            self.node_list.culture_beasts_skill_show:CustomSetActive(skill_id ~= 0)

            if skill_id ~= 0 then
                local skill_level = map_data.beast_data.beast_star or 0
                --去技能数据类查
                local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
    
                if client_cfg then
                    self.node_list.culture_beasts_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
                end

                local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_jnd_%d", beast_cfg.skill_type_id))
                self.node_list.culture_beasts_skill_di.image:LoadSprite(bundle, asset, function()
                    self.node_list.culture_beasts_skill_di.image:SetNativeSize()
                end)
        
                self.node_list.culture_beasts_skill_name.text.text = beast_cfg.skill_des
            end
        end
    end

    self.node_list.culture_go_to_draw_remind:CustomSetActive(ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRed())
    -- self:CheckCultureAuto()      -- 一键升级

    -- 设置圣兽缔结状态
    local is_need_show_contract = self.cur_culture_beast_data.is_holy_beast and server_data.holy_spirit_link_index == -1
    self.node_list["culture_upgrade"]:SetActive(not is_need_show_contract)
    self.node_list["culture_contract"]:SetActive(is_need_show_contract)
    local can_contract = false

    if is_need_show_contract then
        self.culture_holy_beast_item:SetData(self.cur_culture_beast_data)
        can_contract = ControlBeastsWGData.Instance:GetHolyBeastCanContract(self.cur_culture_beast_data)
        self.node_list["culture_remind_can_contract"]:SetActive(can_contract)
    end

    self.node_list.culture_btn_remind:CustomSetActive(upgrade_red or can_contract)
end

-- 刷新资质相关
function ControlBeastsWGView:FlushCultureBeastFlairMessage()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    -- 属性
    local bag_id = self.cur_culture_beast_data.bag_id
    local server_data = self.cur_culture_beast_data.server_data
	local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
	local refine_seq = best_base_cfg and best_base_cfg.refine_seq or -1
    local refine_aimstar = best_base_cfg and best_base_cfg.refine_aimstar or -1
    local refine_maxlevel = best_base_cfg and best_base_cfg.refine_maxlevel or -1

    self.node_list.layout_flair_attr:CustomSetActive(refine_seq ~= -1)
    self.node_list.not_have_flair_attr:CustomSetActive(refine_seq == -1)
    self.node_list.not_have_flair_operate:CustomSetActive(refine_maxlevel == -1)
    self.node_list.culture_flair_operate_root:CustomSetActive(refine_maxlevel ~= -1)

    if refine_aimstar ~= -1 then
        self.node_list.not_have_flair_txt.text.text = string.format(Language.ContralBeasts.Incubate25, 
            ToColorStr(string.format("%s%s", refine_aimstar, Language.Common.Star)))
    else
        self.node_list.not_have_flair_txt.text.text = Language.ContralBeasts.Incubate24
    end
    if refine_seq == -1 then
        return
    end

    -- 洗练等级操作改为全部按照星级来计算
    local refine_level = best_base_cfg.beast_star --server_data.refine_level or 0
    local refine_times = server_data.refine_times or 0
    local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)
    local next_weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level + 1)
    local now_max = weight_cfg and weight_cfg.rand_right or 0
    local all_pre_value = 0
    local all_now_value = 0
    local all_max_value = 0

    if server_data.refine_attr_list then
        for index, bag_flair_cell in ipairs(self.flair_attrlist) do
            local data = server_data.refine_attr_list[index]
            all_max_value = all_max_value + now_max

            if data then
                bag_flair_cell:SetNowBeastRefineSeq(refine_seq, now_max)
                bag_flair_cell:SetData(data)
                all_pre_value = all_pre_value + data.pre_rand_value
                all_now_value = all_now_value + data.rand_value
            end
        end
    end

    local offset_value = all_pre_value - all_now_value
    local prefix = offset_value > 0 and "+" or ""
    local color = offset_value > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
    local is_rand_full = all_now_value == all_max_value                     -- 洗练最大值
    local is_level_full = all_now_value == all_max_value and refine_level >= refine_maxlevel    -- 洗练等级最大值
    local value_str = string.format("%.2f", offset_value / BEAST_DEFINE.BEASTS_BOUNS_PER2)
    self.node_list.culture_flair_value_tips:CustomSetActive(all_pre_value > 0 and (not self.is_auto_change_flair))
    self.node_list.culture_flair_value_tips.text.text = string.format(Language.ContralBeasts.CultureText7, ToColorStr(prefix .. value_str, color))
    self.node_list.flair_change_max:CustomSetActive(is_rand_full and next_weight_cfg == nil)
    self.node_list.flair_change_limit:CustomSetActive(is_rand_full and next_weight_cfg ~= nil)
    self.node_list.culture_flair_change_item:CustomSetActive(not is_rand_full)
    local is_item_enougth = self:FlushMultipleFlairChangeRoot(true)
    local real_multiple_flair_value = self.multiple_flair_value or 1
    -- self.node_list.flair_change_break:CustomSetActive(is_rand_full and (not is_level_full))
    -- self.node_list.culture_flair_change_times.text.text = string.format(Language.ContralBeasts.CultureText11, refine_times)
    local is_not_operate = is_rand_full or is_level_full

    if all_pre_value > 0 and self.is_auto_change_flair then         -- 自动洗练
        if offset_value > 0 then
            ControlBeastsWGCtrl.Instance:SendOperateTypeRefineResult(bag_id, 1)
        else
            ControlBeastsWGCtrl.Instance:SendOperateTypeRefineResult(bag_id, 0)
        end

        return
    end

    local consume_item_id = weight_cfg and weight_cfg.cost_item_id or 0
    local need_item_count = weight_cfg and weight_cfg.cost_item_num or 0

    if is_rand_full and (not is_level_full) then
        consume_item_id = weight_cfg and weight_cfg.up_cost_item_id or 0
        need_item_count = weight_cfg and weight_cfg.up_cost_item_num or 0
    end

    if self.is_select_multiple_flair_change then
        need_item_count = real_multiple_flair_value * need_item_count  
    end

    self.culture_flair_change_item:SetData({item_id = consume_item_id})
    local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
    color = item_num >= need_item_count and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.culture_flair_change_item:SetRightBottomTextVisible(true)
    self.culture_flair_change_item:SetRightBottomColorText(string.format("%d/%d", item_num, need_item_count), color)

    if is_not_operate or item_num < need_item_count then
        self.is_auto_change_flair = false
    end

    self.node_list.flair_change_quick_btn:CustomSetActive((not is_not_operate) and (all_pre_value == 0 or self.is_auto_change_flair))
    self.node_list.flair_change_once_btn:CustomSetActive((not is_not_operate) and (all_pre_value == 0 or self.is_auto_change_flair))
    self.node_list.flair_change_cancel_btn:CustomSetActive((not is_not_operate) and all_pre_value > 0 and (not self.is_auto_change_flair))
    self.node_list.flair_change_ok_btn:CustomSetActive((not is_not_operate) and all_pre_value > 0 and (not self.is_auto_change_flair))
    local changeflair_red = ControlBeastsWGData.Instance:RefreshBeastChangeFlair(self.cur_culture_beast_data)
    self.node_list.flair_btn_remind:CustomSetActive(changeflair_red)
    self.node_list.flair_change_quick_red:CustomSetActive(changeflair_red)
    self.node_list.flair_change_once_red:CustomSetActive(changeflair_red)
    self.node_list.flair_change_ok_red:CustomSetActive(changeflair_red)
    self.node_list.flair_change_break_remind:CustomSetActive(changeflair_red)


    self:CheckCultureFlairChange()
end

-- 检测自动升级
function ControlBeastsWGView:CheckCultureFlairChange()
    local str = self.is_auto_change_flair and Language.ContralBeasts.CultureText9 or Language.ContralBeasts.CultureText8
    self.node_list.flair_change_quick_btn_txt.text.text = str

    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.is_auto_change_flair then
        -- 计算资质评级
        local server_data = self.cur_culture_beast_data.server_data
        local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
        local refine_seq = best_base_cfg and best_base_cfg.refine_seq or 0
        -- 洗练等级操作改为全部按照星级来计算
        local refine_level = best_base_cfg.beast_star --server_data.refine_level or 0
        local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)

        if weight_cfg then
            -- 刷新吞噬
            self:RemoveSendChangeDelayTimer()
            local consume_item_id = weight_cfg and weight_cfg.cost_item_id or 0
            local need_item_count = weight_cfg and weight_cfg.cost_item_num or 0
            local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
            local real_multiple_flair_value = 1

            if self.is_select_multiple_flair_change then
                real_multiple_flair_value = self.multiple_flair_value or 1
                need_item_count = real_multiple_flair_value * need_item_count 
            end

            if item_num >= need_item_count then
                local bag_id = self.cur_culture_beast_data.bag_id
                self.send_change_timer = GlobalTimerQuest:AddDelayTimer(function ()
                    ControlBeastsWGCtrl.Instance:SendOperateTypeRefine(bag_id, real_multiple_flair_value)
                end, 0.2)
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = consume_item_id })
                self.is_auto_change_flair = false
                local str = self.is_auto_change_flair and Language.ContralBeasts.CultureText9 or Language.ContralBeasts.CultureText8
                self.node_list.flair_change_quick_btn_txt.text.text = str
            end
        end
    end
end

--移除回调
function ControlBeastsWGView:RemoveSendChangeDelayTimer()
    if self.send_change_timer then
        GlobalTimerQuest:CancelQuest(self.send_change_timer)
        self.send_change_timer = nil
    end
end

function ControlBeastsWGView:FlushCultureButtonState()
    if not self.node_list then
        return
    end

    if self.node_list.culture_btn_Image_nor then
        self.node_list.culture_btn_Image_nor:CustomSetActive(self.cur_select_type ~= SHOW_TYPE.CULTURE)
    end

    if self.node_list.culture_btn_hl then
        self.node_list.culture_btn_hl:CustomSetActive(self.cur_select_type == SHOW_TYPE.CULTURE)
    end

    if self.node_list.flair_btn_Image_nor then
        self.node_list.flair_btn_Image_nor:CustomSetActive(self.cur_select_type ~= SHOW_TYPE.FLAIR)
    end

    if self.node_list.flair_btn_Image_hl then
        self.node_list.flair_btn_Image_hl:CustomSetActive(self.cur_select_type == SHOW_TYPE.FLAIR)
    end

    if self.node_list.skill_btn_Image_nor then
        self.node_list.skill_btn_Image_nor:CustomSetActive(self.cur_select_type ~= SHOW_TYPE.SKILL)
    end

    if self.node_list.skill_btn_Image_hl then
        self.node_list.skill_btn_Image_hl:CustomSetActive(self.cur_select_type == SHOW_TYPE.SKILL)
    end

    if self.node_list.culture_attr_root then
        self.node_list.culture_attr_root:CustomSetActive(self.cur_select_type == SHOW_TYPE.CULTURE)
    end

    if self.node_list.flair_attr_root then
        self.node_list.flair_attr_root:CustomSetActive(self.cur_select_type == SHOW_TYPE.FLAIR)
    end

    if self.node_list.beast_skill_root then
        self.node_list.beast_skill_root:CustomSetActive(self.cur_select_type == SHOW_TYPE.SKILL)
    end

    if self.node_list.common_capability then
        self.node_list.common_capability:CustomSetActive(self.cur_select_type ~= SHOW_TYPE.SKILL)
    end
end

-- 检测是否自动升级
function ControlBeastsWGView:CheckCultureAuto()
    if self.is_culture_auto then
        self:OperateOnceCulture()
    end
end

-- 刷新按钮状态
function ControlBeastsWGView:FlushCultureAutoButtonState()
    -- local str = self.is_culture_auto and Language.ContralBeasts.CultureText2 or Language.ContralBeasts.CultureText1
    -- self.node_list.culture_btn_upgrade_tex.text.text = str
    self:FlushCultureButtonState()
end

-- 关闭自动
function ControlBeastsWGView:StopAutoCultureOperate()
    if self.is_culture_auto then
        self.is_culture_auto = false
        self:FlushCultureAutoButtonState()
    end
end

-- 刷新按钮状态
function ControlBeastsWGView:FlushCultureBeastSkillMessage()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    -- 属性
    local bag_id = self.cur_culture_beast_data.bag_id
    local server_data = self.cur_culture_beast_data.server_data
	local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local group_seq = best_base_cfg and best_base_cfg.skill_group_seq or 0
    local be_skill_id = best_base_cfg and best_base_cfg.be_skill_id or 0
    local active_skill_id = best_base_cfg and best_base_cfg.skill_id or 0
    local normal_skill_id = best_base_cfg and best_base_cfg.normal_skill_id or 0
    local beast_star_lv = best_base_cfg and best_base_cfg.beast_star or 0
    --主动技能
    local active_client_cfg = SkillWGData.Instance:GetSkillClientConfig(active_skill_id)
    local active_beast_cfg = SkillWGData.Instance:GetBeastsSkillById(active_skill_id)
    --普攻技能
    local normal_client_cfg = SkillWGData.Instance:GetSkillClientConfig(normal_skill_id)
    local normal_beast_cfg = SkillWGData.Instance:GetBeastsSkillById(normal_skill_id)
    --专属技能
    local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(be_skill_id)
    -- 队列配置
    local best_skill_group_cfg = ControlBeastsWGData.Instance:GetBeastSkillGroupCfgBySeq(group_seq)
    -- 组装数据
    local active_render_value = nil
    local normal_render_value = nil
    local be_render_value = nil

    local group_skill_data = function(name, icon, desc, stars, star_groups)
        local info = {}
        info.skill_name = name
        info.skill_icon = icon
        info.description = desc
        info.beast_star = beast_star_lv
        info.star_group = stars
        info.star_skill_group = star_groups

        return info
    end

    if active_client_cfg and active_beast_cfg and best_skill_group_cfg then
        active_render_value = group_skill_data(
            active_beast_cfg.skill_name, 
            active_client_cfg.icon_resource, 
            active_client_cfg.description,
            best_skill_group_cfg.star_group,
            best_skill_group_cfg.star_skill_group
        )
    end

    if normal_client_cfg and normal_beast_cfg and best_skill_group_cfg then
        normal_render_value = group_skill_data(
            normal_beast_cfg.skill_name, 
            normal_client_cfg.icon_resource, 
            normal_client_cfg.description,
            best_skill_group_cfg.star_group1,
            best_skill_group_cfg.star_skill_group1
        )
    end

    if be_skill_cfg and best_skill_group_cfg then
        be_render_value = group_skill_data(
            be_skill_cfg.skill_name, 
            be_skill_cfg.skill_icon, 
            be_skill_cfg.skill_des,
            best_skill_group_cfg.star_group2,
            best_skill_group_cfg.star_skill_group2
        )
    end

    self.node_list.active_skill_render:CustomSetActive(active_render_value ~= nil)
    self.active_skill_render:SetData(active_render_value)
    self.node_list.normal_skill_render:CustomSetActive(normal_render_value ~= nil)
    self.normal_skill_render:SetData(normal_render_value)
    self.node_list.owner_skill_render:CustomSetActive(be_render_value ~= nil)
    self.owner_skill_render:SetData(be_render_value)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.culture_beasts_skill_list.rect)
end

-- 洗髓多倍刷新
function ControlBeastsWGView:FlushMultipleFlairChangeRoot(is_flush)
    if (not self.cur_culture_beast_data) or (not self.cur_culture_beast_data.server_data) then
        return
    end

    -- 计算资质评级
    local server_data = self.cur_culture_beast_data.server_data
    local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local refine_seq = best_base_cfg and best_base_cfg.refine_seq or 0
    -- 洗练等级操作改为全部按照星级来计算
    local refine_level = best_base_cfg.beast_star --server_data.refine_level or 0
    local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)
    self.node_list.multiple_flair_change_select:CustomSetActive(self.is_select_multiple_flair_change)
    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    local multiple_value = base_cfg and base_cfg.flair_change_multiple or 1

    if weight_cfg then
        -- 刷新吞噬
        self:RemoveSendChangeDelayTimer()
        local consume_item_id = weight_cfg and weight_cfg.cost_item_id or 0
        local need_item_count_simple = weight_cfg and weight_cfg.cost_item_num or 0
        local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
        local str = ""
        local need_item_count = need_item_count_simple * multiple_value

        if item_num >= need_item_count then
            str = string.format(Language.ContralBeasts.CultureText15, multiple_value)
        else
            multiple_value = math.floor(item_num / need_item_count_simple)
            multiple_value = multiple_value > 0 and multiple_value or 1
            str = string.format(Language.ContralBeasts.CultureText15, multiple_value)
        end

        self.multiple_flair_value = multiple_value
        self.node_list.multiple_flair_change_txt.text.text = str
        local real_multiple_flair_value = self.multiple_flair_value or 1

        if self.is_select_multiple_flair_change then
            need_item_count = need_item_count_simple * real_multiple_flair_value
        else
            need_item_count = need_item_count_simple
        end

        self.node_list.multiple_flair_change_root:CustomSetActive(real_multiple_flair_value > 1)
        if not is_flush then
            self.culture_flair_change_item:SetData({item_id = consume_item_id})
            local color = item_num >= need_item_count and COLOR3B.D_GREEN or COLOR3B.D_RED
            self.culture_flair_change_item:SetRightBottomTextVisible(true)
            self.culture_flair_change_item:SetRightBottomColorText(string.format("%d/%d", item_num, need_item_count), color)
        end
    end
end
--------------------------------------------------------------------------------------------------
-- 切换培养页签
function ControlBeastsWGView:ChangeShowRoot(show_type, is_force)
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.cur_select_type == show_type then
        return
    end

    if not is_force then
        --未链接圣兽不允许切换页签
        local is_limited = self.cur_culture_beast_data.is_holy_beast and self.cur_culture_beast_data.server_data.holy_spirit_link_index == -1
        if is_limited then
            TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.BeastNoLinked)
            return
        end
    end

    self.cur_select_type = show_type

    if self.cur_select_type ~= SHOW_TYPE.CULTURE then
        self.is_culture_auto = false
    end

    if self.cur_select_type ~= SHOW_TYPE.FLAIR then
        self.is_auto_change_flair = false
    end

    self:FlushCultureAutoButtonState()
end

-- 点击升级
function ControlBeastsWGView:CultureUpgrade()
    if self.is_anim then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip6)
        return
    end

    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.cur_culture_beast_data.is_preview then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip15)
    end
    
    -- 属性
    local cur_have_exp = ControlBeastsWGData.Instance:GetBeastBaseInfoExp()
    if cur_have_exp > 0 then
        ControlBeastsWGCtrl.Instance:SendOperateTypeUpgrade(self.cur_culture_beast_data.bag_id)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip4)
    end
end

-- 技能展示
function ControlBeastsWGView:CultureBeastsSkillShow()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.cur_culture_beast_data and self.cur_culture_beast_data.server_data then
        ControlBeastsWGData.Instance:ShowBeastSkill(self.cur_culture_beast_data.server_data.beast_id, false, true)
    end
end

--资质介绍
function ControlBeastsWGView:CultureFlairTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.ContralBeasts.AttrTitle6)
	rule_tip:SetContent(Language.ContralBeasts.AttrTitle7, nil, nil, nil, true)
end

-- 自动培养
function ControlBeastsWGView:OperateAutoCulture()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.cur_culture_beast_data.is_preview then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip15)
        return
    end

    local server_data = self.cur_culture_beast_data.server_data
	local lv = server_data.beast_level					-- 当前等级
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
	local beast_star = beast_cfg and beast_cfg.beast_star or 0
	local level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(beast_star)
	local star_max_level = level_limit_cfg and level_limit_cfg.beast_max_level or 0

    if lv < star_max_level then
        self:OperateOnceCulture()
    end
end

-- 直到升到50级（不够50级升到目标等级，到达满级升到满级）
function ControlBeastsWGView:OperateCultureToLv50()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    if self.cur_culture_beast_data.is_preview then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip15)
        return
    end

    local server_data = self.cur_culture_beast_data.server_data
    local can_arrived_lv, arrived_lv_need_exp = ControlBeastsWGData.Instance:GetCanArrivedLevel(server_data, server_data.beast_level + 50)
    if can_arrived_lv > 0 then
        self:OperateOnceCulture(false, can_arrived_lv)
    end
end

-- 单次培养
function ControlBeastsWGView:OperateOnceCulture(is_click, to_lv)
    if is_click and self.is_culture_auto then    -- 手动点击且在自动升级中则不操作
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip3)
        return
    end

    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    local server_data = self.cur_culture_beast_data.server_data

    -- 检测经验是否满足
    local cur_have_exp = ControlBeastsWGData.Instance:GetBeastBaseInfoExp()
    local is_exp_enough = cur_have_exp > 0
    if not is_exp_enough then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip4)
        self:StopAutoCultureOperate()
        return
    end
    
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local beast_star = beast_cfg and beast_cfg.beast_star or 0
    local next_beast_star = beast_star + 1
    local level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(beast_star)
    local next_level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(next_beast_star)
    local is_full_level = server_data.beast_level == level_limit_cfg.beast_max_level

    if is_full_level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip5)
        self:StopAutoCultureOperate()
        return
    end

    ---发送协议
    ControlBeastsWGCtrl.Instance:SendOperateTypeUpgrade(self.cur_culture_beast_data.bag_id, to_lv or 0)
end

function ControlBeastsWGView:OpenBeastsPrizeDraw()
    ControlBeastsWGCtrl.Instance:OpenBeastsPrizeDraw()
end

-- 资质替换确认
function ControlBeastsWGView:BtnChangeFlairOk()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    local server_data = self.cur_culture_beast_data.server_data
    local bag_id = self.cur_culture_beast_data.bag_id
    local all_pre_value = 0
    local all_now_value = 0

    if server_data.refine_attr_list then
        for index, data in ipairs(server_data.refine_attr_list) do
            if data then
                all_pre_value = all_pre_value + data.pre_rand_value
                all_now_value = all_now_value + data.rand_value
            end
        end
    end

    local execute_change = function()
        ControlBeastsWGCtrl.Instance:SendOperateTypeRefineResult(bag_id, 1)
    end

    local offset_value = all_pre_value - all_now_value
    if offset_value > 0 then
        execute_change()
    else
        if not self.culture_alert then
            self.culture_alert = Alert.New()
        end

        self.culture_alert:SetShowCheckBox(true, string.format("beasts_culture_change_flair_alert"))
        self.culture_alert:SetOkFunc(execute_change)
        self.culture_alert:SetLableString(Language.ContralBeasts.CultureText10)
        self.culture_alert:SetCheckBoxDefaultSelect(false)
        self.culture_alert:SetLableRectWidth(580)
        self.culture_alert:SetCheckBoxText(Language.TreasureHunt.NoRemind)
        self.culture_alert:Open()
    end
end

-- 资质替换取消
function ControlBeastsWGView:BtnChangeFlairCancel()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    local bag_id = self.cur_culture_beast_data.bag_id
    ControlBeastsWGCtrl.Instance:SendOperateTypeRefineResult(bag_id)
end

-- 自动洗练
function ControlBeastsWGView:BtnChangeFlairQuick()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    self.is_auto_change_flair = not self.is_auto_change_flair
    self:CheckCultureFlairChange()
end

-- 单次洗练
function ControlBeastsWGView:BtnChangeFlairOnce()
    if not self:CheckHaveDataAndServerData(self.cur_culture_beast_data) then
        return
    end

    -- 计算资质评级
    local server_data = self.cur_culture_beast_data.server_data
    local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local refine_seq = best_base_cfg and best_base_cfg.refine_seq or 0
    -- 洗练等级操作改为全部按照星级来计算
    local refine_level = best_base_cfg.beast_star --server_data.refine_level or 0
    local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)

    if weight_cfg then
        -- 刷新吞噬
        local consume_item_id = weight_cfg and weight_cfg.cost_item_id or 0
        local need_item_count = weight_cfg and weight_cfg.cost_item_num or 0
        local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
        local real_multiple_flair_value = 1

        if self.is_select_multiple_flair_change then
            real_multiple_flair_value = self.multiple_flair_value or 1
            need_item_count = real_multiple_flair_value * need_item_count 
        end

        if item_num >= need_item_count then
            local bag_id = self.cur_culture_beast_data.bag_id
            ControlBeastsWGCtrl.Instance:SendOperateTypeRefine(bag_id, real_multiple_flair_value)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
            TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = consume_item_id })
        end
    end
end

-- 突破
function ControlBeastsWGView:BtnChangeFlairBreak()
    -- 属性
    local bag_id = self.cur_culture_beast_data.bag_id
    local server_data = self.cur_culture_beast_data.server_data
    local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local refine_seq = best_base_cfg and best_base_cfg.refine_seq or 0
    local refine_maxlevel = best_base_cfg and best_base_cfg.refine_maxlevel or 0
    -- 洗练等级操作改为全部按照星级来计算
    local refine_level = best_base_cfg.beast_star --server_data.refine_level or 0
    local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)
    local now_max = weight_cfg and weight_cfg.rand_right or 0
    local all_now_value = 0
    local all_max_value = 0

    if server_data.refine_attr_list then
        for index, data in ipairs(server_data.refine_attr_list) do
            if data then
                all_now_value = all_now_value + data.rand_value
                all_max_value = all_max_value + now_max
            end
        end
    end

    local is_rand_full = all_now_value == all_max_value                     -- 洗练最大值
    local is_level_full = all_now_value == all_max_value and refine_level >= refine_maxlevel    -- 洗练等级最大值
    
    if is_rand_full and (not is_rand_full) then
        local consume_item_id = weight_cfg and weight_cfg.up_cost_item_id or 0
        local need_item_count = weight_cfg and weight_cfg.up_cost_item_num or 0
        local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)

        if item_num >= need_item_count then
            local bag_id = self.cur_culture_beast_data.bag_id
            ControlBeastsWGCtrl.Instance:SendOperateTypeRefineLevel(bag_id)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
            TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = consume_item_id })
        end
    end
end

--分解
function ControlBeastsWGView:BtnCultueDespose()
    ControlBeastsWGCtrl.Instance:OpenBeastsDesposeView()
end

--皮肤
function ControlBeastsWGView:OnClickBeastsSkinBtn()
    ControlBeastsWGCtrl.Instance:OpenBeastSkinView(self.cur_culture_beast_data)
end

--创世圣兽
function ControlBeastsWGView:OnClickHolyBeastsBtn()
    if not FunOpen.Instance:GetFunIsOpened(FunName.HolyBeastsView) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Common.FunOpenTip)
        return
    end
    ControlBeastsWGCtrl.Instance:OpenHolyBeastsView(self.cur_culture_beast_data.beast_type)
end

-- 属性丹总属性
function ControlBeastsWGView:OnClickBeastsSxdBtn()
    local tips_data = ControlBeastsWGData.Instance:GetSXDAttrTipsData()
    ControlBeastsWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

-- 多倍洗髓点击
function ControlBeastsWGView:OnClickMultipleFlairChange()
    self.is_select_multiple_flair_change = not self.is_select_multiple_flair_change
    self:FlushMultipleFlairChangeRoot()
end
---------------------------------------------------------------------
----------------------------------灵兽资质item-----------------------
BeststFlairItemRender = BeststFlairItemRender or BaseClass(BaseRender)
function BeststFlairItemRender:__delete()
    self.refine_seq = nil
    self.rand_max = nil
end

function BeststFlairItemRender:SetNowBeastRefineSeq(refine_seq, rand_max)
    self.refine_seq = refine_seq
    self.rand_max = rand_max
end

function BeststFlairItemRender:OnFlush()
    if not self.data then
        return
    end

    -- 获取资质加成属性数据
    local index = self.index - 1
    local refine_seq = self.refine_seq or 0
    local refind_cfg = ControlBeastsWGData.Instance:GetBeastRefineCfgBySeqIndex(refine_seq, index)
    if not refind_cfg then
        return
    end

    local rand_max = self.rand_max or 0
    local attr_name = EquipmentWGData.Instance:GetAttrName(refind_cfg.attr_id, true)
    self.node_list.attr_name.text.text = attr_name
    self.node_list.attr_pre_up_progress:CustomSetActive(false)
    self.node_list.attr_pre_down_progress:CustomSetActive(false)
    -- 计算加成比
    -- 基础数值
    local max_bouns_ten_thousand = rand_max / BEAST_DEFINE.BEASTS_BOUNS_PER
    local max_bouns_value = self.data.attr_value * (max_bouns_ten_thousand + 1)
    local base_slider_value = self.data.attr_value / max_bouns_value

    -- 现在加成比
    local now_bouns_ten_thousand = self.data.rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER
    local now_bouns_value = self.data.attr_value * (now_bouns_ten_thousand + 1)
    local slider_value = now_bouns_value / max_bouns_value
    local value_str = string.format("%d/%d", math.floor(now_bouns_value), math.floor(max_bouns_value)) 

    if self.data.pre_rand_value ~= 0 and self.data.pre_rand_value ~= self.data.rand_value then
        local now_bouns_thousand = self.data.rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER2
        local now_pre_bouns_thousand = self.data.pre_rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER2
        local prefix = now_pre_bouns_thousand > now_bouns_thousand and "+" or ""
        local color = now_pre_bouns_thousand > now_bouns_thousand and COLOR3B.GREEN or COLOR3B.RED
        value_str = ToColorStr(string.format("%s%.2f%%", prefix, now_pre_bouns_thousand - now_bouns_thousand), color)

        self.node_list.attr_pre_up_progress:CustomSetActive(now_pre_bouns_thousand > now_bouns_thousand)
        self.node_list.attr_pre_down_progress:CustomSetActive(now_pre_bouns_thousand < now_bouns_thousand)

        if now_pre_bouns_thousand > now_bouns_thousand then
            local pre_bouns_ten_thousand = self.data.pre_rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER
            local pre_bouns_value = self.data.attr_value * (pre_bouns_ten_thousand + 1)
            self.node_list.attr_pre_up_progress.slider.value = pre_bouns_value / max_bouns_value
        else
            now_bouns_ten_thousand = self.data.pre_rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER
            now_bouns_value = self.data.attr_value * (now_bouns_ten_thousand + 1)
            slider_value = now_bouns_value / max_bouns_value

            local pre_bouns_ten_thousand = self.data.rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER
            local pre_bouns_value = self.data.attr_value * (pre_bouns_ten_thousand + 1)
            self.node_list.attr_pre_down_progress.slider.value = pre_bouns_value / max_bouns_value
        end
    end
    
    self.node_list.attr_value.text.text = value_str
    self.node_list.attr_progress.slider.value = base_slider_value
    self.node_list.attr_pre_base_progress.slider.value = slider_value
end

-------------------------------------技能描述(大)----------------------------------------------
BeastCultueSkillRender = BeastCultueSkillRender or BaseClass(BaseRender)
function BeastCultueSkillRender:LoadCallBack()
    -- 技能提升列表
   -- 资质属性
   if self.now_skill_list == nil then
        self.now_skill_list = {}
        for i = 1, 10 do
            local attr_obj = self.node_list.now_skill_list:FindObj(string.format("culture_skill_render_%d", i))
            if attr_obj then
                local cell = BeastCultueSkillItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.now_skill_list[i] = cell
            end
        end
    end
end

function BeastCultueSkillRender:ReleaseCallBack()
    if self.now_skill_list then
		for _, now_skill_cell in pairs(self.now_skill_list) do
			now_skill_cell:DeleteMe()
		end

		self.now_skill_list = nil
	end
end

function BeastCultueSkillRender:OnFlush()
    self.node_list.not_have_skill:CustomSetActive(self.data == nil)
    self.node_list.skill_root:CustomSetActive(self.data ~= nil)

    if not self.data then
        return
    end

    self.node_list.now_skill_name.text.text = self.data.skill_name
    self.node_list.now_skill_desc.text.text = self.data.description
    self.node_list.now_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))

    local star_list = {}
    local star_skill_list = {}
    local desc_table = {}
    local beast_star = self.data.beast_star or 0

    if self.data.star_group and self.data.star_group ~= "" then
        local star_str_list = Split(self.data.star_group, "|")
        for _, str in ipairs(star_str_list) do
            local star = tonumber(str) or 0
            table.insert(star_list, star)
        end
    end

    if self.data.star_skill_group and self.data.star_skill_group ~= "" then
        local star_group_str_list = Split(self.data.star_skill_group, "|")
        for _, str in ipairs(star_group_str_list) do
            local star_skill = str
            table.insert(star_skill_list, star_skill)
        end
    end

    for i, star in ipairs(star_list) do
        local info = {}
        info.star = star
        local color = beast_star < star and "#e5e5e5" or nil 
        info.desc = star_skill_list and star_skill_list[i] or ""

        if color ~= nil then
            local str = RemoveRichTextColorTags(info.desc)
            info.desc = ToColorStr(str, color)
        end

        table.insert(desc_table, info)
    end

    for i, now_skill_cell in ipairs(self.now_skill_list) do
        now_skill_cell:SetVisible(desc_table[i] ~= nil)

        if desc_table[i] ~= nil then
            now_skill_cell:SetData(desc_table[i])
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.skill_root.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.now_skill_list.rect)
end

-------------------------------------技能描述(小)----------------------------------------------
BeastCultueSkillItemRender = BeastCultueSkillItemRender or BaseClass(BaseRender)
function BeastCultueSkillItemRender:LoadCallBack()
    if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star" .. i]
		end
	end
end

function BeastCultueSkillItemRender:__delete()
    self.star_list = nil
end

function BeastCultueSkillItemRender:OnFlush()   
    if not self.data then
        return
    end

    local star_res_list = GetSpecialStarImgResByStar3(self.data.star)
    local no_have_star = "a3_ty_xx_zc0"
    for k,v in pairs(self.star_list) do
        v:CustomSetActive(star_res_list[k] ~= no_have_star)
        v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
    end

    self.node_list.now_skill_desc.text.text = self.data.desc
end

------------------------------------幻兽属性丹----------------------------------------------
BeastsCultureSXDRender = BeastsCultureSXDRender or BaseClass(BaseRender)
function BeastsCultureSXDRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSXD, self))
end

function BeastsCultureSXDRender:OnFlush()
    if not self.data then
        return
    end

    local item_id = self.data.cost_item_id
    local lv = ControlBeastsWGData.Instance:GetBeastsPelletLvBySeq(self.data.seq)
    local is_remind, count = ControlBeastsWGData.Instance:GetBeastsPelletRedBySeq(self.data.seq)
    self.node_list.used_num.text.text = lv
    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(self.data.cost_item_id)
    self.node_list.icon.image:LoadSprite(bundle, asset)
    self.node_list.arrow:SetActive(is_remind)
    self.node_list.effect:SetActive(is_remind)

    if is_remind then
        self.node_list.can_add_num.text.text = string.format("+%d", count)
    else
        self.node_list.can_add_num.text.text = ""
    end
end

function BeastsCultureSXDRender:OnClickSXD()
    if not self.data then
        return
    end

    local cost_item_id = self.data.cost_item_id
    local is_remind, count = ControlBeastsWGData.Instance:GetBeastsPelletRedBySeq(self.data.seq)

    if is_remind then
        local index_list = ItemWGData.Instance:GetItemListIndex(cost_item_id)
        for i, bag_index in ipairs(index_list) do
            local num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index)
            if num > 0 and count > 0 then
                local last_count = count - num

                if last_count >= 0 then
                    BagWGCtrl.Instance:SendUseItem(bag_index, num, 0, 0)
                else
                    BagWGCtrl.Instance:SendUseItem(bag_index, count, 0, 0)
                end

                count = last_count
            end
        end
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cost_item_id})
    end
end