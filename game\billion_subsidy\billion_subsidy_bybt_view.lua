function BillionSubsidyView:BYBTLoadCallBack()
    if not self.bybt_shop_list then
        self.bybt_shop_list = AsyncBaseGrid.New()
        self.bybt_shop_list:SetStartZeroIndex(false)
        self.bybt_shop_list:SetSelectCallBack(BindTool.Bind(self.BYBTOnSelectShopItem, self))
        self.bybt_shop_list:CreateCells(
            {col = 4,
            change_cells_num = 1,
            complement_col_item = true,
            list_view = self.node_list.bybt_shop_list,
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
            assetName = "ten_billion_subsidy_shop_item",
            itemRender = BYBTShopItem}
        )
    end
end

function BillionSubsidyView:BYBTReleaseCallBack()
    if self.bybt_shop_list then
        self.bybt_shop_list:DeleteMe()
        self.bybt_shop_list = nil
    end
end

-- function BillionSubsidyView:BYBTOpenIndexCallBack()
--     local fetch_first_open_view_reward = BillionSubsidyWGData.Instance:GetIsFetchFirstOpenViewReward()
--     local is_first_open = BillionSubsidyWGData.Instance:GetIsFirstOpenView()

--     if fetch_first_open_view_reward == 0 and is_first_open ~= 0 and not self.is_first_open then
--         --BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_FIRST_OPEN_VIEW_REWARD)
--         local callback = function ()
--             BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_FIRST_OPEN_VIEW_REWARD)
--         end

--         BillionSubsidyWGCtrl.Instance:PlayOpenReceiveDCEffect(callback)
--     end
-- end

function BillionSubsidyView:BYBTShowIndexCallBack()
    local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
    if start_timestamp <= 0 then
        BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.START_TIME_LIMITED_DISCOUNT_SHOP)
    end
end

function BillionSubsidyView:BYBTOnFlush()
    if BillionSubsidyWGData.Instance:GetShopEveryDayRemind(BillionSubsidyWGData.ShopType.BYBT) then
        BillionSubsidyWGData.Instance:SetShopEveryDayRemindData(BillionSubsidyWGData.ShopType.BYBT)
        RemindManager.Instance:Fire(RemindName.BillionSubsidyBYBT)
    end

    local data_list = BillionSubsidyWGData.Instance:GetBYBTShopItemGradeData()
    if not data_list then
        return
    end

    -- local data_list = {}
    -- for k, v in pairs(shop_cfg) do
    --     table.insert(data_list, v)
    -- end

    -- table.sort(data_list, function (a, b)
    --     if a.limit_time_discount_duration > 0 and b.limit_time_discount_duration == 0 then
    --         return true
    --     elseif a.limit_time_discount_duration == 0 and b.limit_time_discount_duration > 0 then
    --         return false
    --     else
    --         return a.item_seq < b.item_seq
    --     end
    -- end)

    self.bybt_shop_list:SetDataList(data_list)
    self.node_list.bybt_shop_list_scroll_bar:CustomSetActive(#data_list > 8)
end

function BillionSubsidyView:BYBTOnSelectShopItem(item)
    local item_data = item:GetData()
    if IsEmptyTable(item_data) then
        return
    end

    BillionSubsidyWGCtrl.Instance:OpenBuyTipView(BillionSubsidyWGData.ShopType.BYBT, item_data.item_seq)
end

------------------------------- BYBTShopItem 商品item
BYBTShopItem = BYBTShopItem or BaseClass(BaseRender)
BYBTShopItem.TicketInfoType = {
    NotTicket = 0,                  -- 没券或者没有可使用的
    CanUseTicket = 1,               -- 可用券
    SelectDataSame = 2,             -- 和选中的券相同
    FreeTicket = 3,                 -- 免费券
    MultiTicket = 4,                -- 可用和免费都有
}

function BYBTShopItem:LoadCallBack()
    self:InitParam()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
    end

    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_add_shop_cart, BindTool.Bind(self.OnClickAddShopCartBtn, self))
end

function BYBTShopItem:OnClickBuyBtn()
    local item_data = self.data
    if IsEmptyTable(item_data) then
        return
    end

    BillionSubsidyWGCtrl.Instance:OpenBuyTipView(self.shop_type, item_data.item_seq)
end

function BYBTShopItem:OnClickAddShopCartBtn()
    if IsEmptyTable(self.data) then
        return
    end
    
    local is_add = BillionSubsidyWGCtrl.Instance:AddItemToShopCart(self.shop_type, self.data.item_seq, 1, self.data.buy_limit, true)

    if is_add then
        local pos, target_pos= BillionSubsidyWGCtrl.Instance:GetNodeInScreenPos(self.node_list.item_pos)
        BillionSubsidyWGCtrl.Instance:PlaySCAddItemAnim(self.data.reward[0].item_id, pos, target_pos)
    end

end

function BYBTShopItem:InitParam()
    self.discount_limit_timer_key = "bybt_discount_limit_timer_key"
    self.shop_type = BillionSubsidyWGData.ShopType.BYBT
end

function BYBTShopItem:ReleaseCallBack()
    self:RemoveDiscountLimitTimer()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function BYBTShopItem:OnFlush()
    self:RemoveDiscountLimitTimer()
    local is_hide = IsEmptyTable(self.data)
    self.node_list.root:CustomSetActive(not is_hide)
    if is_hide then
        return
    end

    local item_data = self.data.reward[0]
    self.item_cell:SetData(item_data)
    self.node_list.shop_name.text.text = ItemWGData.Instance:GetItemName(item_data.item_id)
    self.node_list.original_price.text.text = string.format(Language.BillionSubsidy.BYBTOriginalPrice, RoleWGData.GetPayMoneyStr(self.data.old_price, self.data.rmb_type, self.data.rmb_seq)) 

    local buy_count = BillionSubsidyWGData.Instance:GetShopBuyCountByTypeAndSeq(self.shop_type, self.data.item_seq)
    local is_buy_done = self.data.buy_limit > 0 and buy_count >= self.data.buy_limit
    self.node_list.limit_count_panel:CustomSetActive(self.data.buy_limit > 0 and not is_buy_done)
    self.node_list.buy_done_flag:CustomSetActive(is_buy_done)

    self.node_list.btn_buy:CustomSetActive(not is_buy_done)
    --self.node_list.discount_panel:CustomSetActive(self.data.discount > 0 and not is_buy_done)

    local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
    local end_time = start_timestamp + self.data.limit_time_discount_duration
    local cur_server_time = TimeWGCtrl.Instance:GetServerTime()
    local is_limit_time_discount = end_time > cur_server_time
    local show_price = is_limit_time_discount and self.data.limit_time_discount_price or self.data.price
    local ticket_type = BYBTShopItem.TicketInfoType.NotTicket
    local free_ticket_data, full_discount_ticket_data, direct_discount_ticket_data = BillionSubsidyWGData.Instance:GetCanUseDiscountTicketBySeq(self.shop_type, self.data.item_seq)
    if free_ticket_data and (full_discount_ticket_data or direct_discount_ticket_data) then
        ticket_type = BYBTShopItem.TicketInfoType.MultiTicket
        show_price = 0
    elseif free_ticket_data then
        ticket_type = BYBTShopItem.TicketInfoType.FreeTicket
        show_price = 0
    elseif full_discount_ticket_data or direct_discount_ticket_data then
        local reduce_quota1 = full_discount_ticket_data and full_discount_ticket_data.reduce_quota or 0
        local reduce_quota2 = direct_discount_ticket_data and direct_discount_ticket_data.reduce_quota or 0
        local max_quota = math.max(reduce_quota1, reduce_quota2)
        show_price = show_price - max_quota
        ticket_type = BYBTShopItem.TicketInfoType.CanUseTicket
    end

    show_price = math.max(show_price, 0)
    self.node_list.ticket_bg:CustomSetActive(ticket_type ~= BYBTShopItem.TicketInfoType.NotTicket and not is_buy_done and not is_limit_time_discount)
    --self.node_list.can_use_ticket_flag:CustomSetActive(ticket_type ~= BYBTShopItem.TicketInfoType.NotTicket and not is_buy_done)

    self.node_list.limit_discount_panel:CustomSetActive(is_limit_time_discount and not is_buy_done)
    --self.node_list.official_subsidy_panel:CustomSetActive(not is_limit_time_discount and self.data.subsidy > 0 and not is_buy_done)
    local str_index = ticket_type ~= BYBTShopItem.TicketInfoType.NotTicket and 2 or 1
    if is_limit_time_discount then
        str_index = 3
    end

    local pay_str = RoleWGData.GetPayMoneyStr(show_price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list.dicount_price.text.text = string.format(Language.BillionSubsidy.BYBTPayStr[str_index], pay_str)

    -- self:RemoveDiscountLimitTimer()
    if not is_buy_done then
        local num_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(self.shop_type, self.data.item_seq)
        self.node_list.limit_count.text.text = string.format(Language.BillionSubsidy.BYBTLimitCount2, buy_count + num_in_cart, self.data.buy_limit)
        if ticket_type ~= BYBTShopItem.TicketInfoType.NotTicket then
            local ticket_bundle, ticket_asset = ResPath.GetBillionSubsidyImg("a3_bybt_quan" .. ticket_type)
            self.node_list.ticket_bg.image:LoadSprite(ticket_bundle, ticket_asset)
            self.node_list.ticket_type.text.text = Language.BillionSubsidy.TicketInfo[ticket_type]
        end

        if not is_limit_time_discount and self.data.subsidy > 0 then
            local subsidy_str = RoleWGData.GetPayMoneyStr(self.data.subsidy)
            self.node_list.official_subsidy_txt.text.text = string.format(Language.BillionSubsidy.BYBTOfficialSubsidy, subsidy_str)
        end

        if self.data.discount > 0 then
            self.node_list.discount_txt.text.text = string.format(Language.BillionSubsidy.BYBTDiscount, NumberToChinaNumber(self.data.discount))
        end

        if is_limit_time_discount then
            self:UpdateCountDownTime(cur_server_time, end_time)
            CountDownManager.Instance:AddCountDown(self.discount_limit_timer_key .. self.index, BindTool.Bind1(self.UpdateCountDownTime, self), BindTool.Bind1(self.CompleteCountDownTime, self), end_time, nil, 1)
        end

        self.node_list.btn_add_shop_cart:CustomSetActive(buy_count + num_in_cart < self.data.buy_limit)
    else
        self.node_list.btn_add_shop_cart:CustomSetActive(false)

    end
end

function BYBTShopItem:RemoveDiscountLimitTimer()
	if CountDownManager.Instance:HasCountDown(self.discount_limit_timer_key .. self.index) then
		CountDownManager.Instance:RemoveCountDown(self.discount_limit_timer_key .. self.index)
	end
end

function BYBTShopItem:UpdateCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
        if self.node_list == nil or self.node_list.limit_time == nil then
            self:RemoveDiscountLimitTimer()
        else
            self.node_list.limit_time.text.text = TimeUtil.FormatDToHAndMS(total_time - elapse_time)
        end
		
	end
end

function BYBTShopItem:CompleteCountDownTime()
    self:Flush()
end