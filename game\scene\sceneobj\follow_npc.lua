
FollowNpc = FollowNpc or BaseClass(FollowObj)

-- 跟随NPC
function FollowNpc:__init(vo)
	self.obj_type = SceneObjType.FollowNpc
	self.draw_obj:SetObjType(self.obj_type)
	self:SetObjId(vo.obj_id)

	self.follow_offset = -2
	self.sqrt_stop_distance = 3 * 3
	self.sqrt_slow_down_distance = 4 * 4
	-- self:SetMaxForce(80)
	self.mass = 0.3
end

function FollowNpc:__delete()
	self.obj_type = nil
	if self.commit_npc then
		local commit_npc = Scene.Instance:GetNpcByNpcId(self.commit_npc.id)
		if commit_npc then
			commit_npc:CancelForceSetVisible()
		end
	end
end

function FollowNpc:InitAppearance()
	self:SetLogicPos(self.vo.pos_x, self.vo.pos_y)
	self:UpdateModelResId()
end

function FollowNpc:UpdateModelResId()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(TaskWGData.FOLLOW_TASK)
	if task_cfg and task_cfg.commit_npc and type(task_cfg.commit_npc) == "table" and task_cfg.commit_npc.id then
		self.commit_npc = task_cfg.commit_npc
	else
		self.commit_npc = nil
	end
	local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.vo.npc_id]
	if nil == npc_config then
		print_log("npc_config not find npc_id:" .. self.vo.npc_id)
		return
	end

	self.vo.name = npc_config.show_name
	local res_id = npc_config.resid
	if 0 ~= res_id then
		local asset, bundle = ResPath.GetNpcModel(res_id)
		self:ChangeModel(SceneObjPart.Main, asset, bundle)
	end
end

local need_move = true
function FollowNpc:Update(now_time, elapse_time)
	FollowObj.Update(self, now_time, elapse_time)
	local commit_npc
	if self.commit_npc then
		commit_npc = Scene.Instance:GetNpcByNpcId(self.commit_npc.id)
		if commit_npc then
			if self:GetLogicDistance(commit_npc.logic_pos, false) < 8 then
				commit_npc:CancelForceSetVisible()
				self:ForceSetVisible(false)
				if not need_move then
					self:StopMove()
				end
			else
				commit_npc:ForceSetVisible(false)
				self:CancelForceSetVisible()
			end
		else
			need_move = true
		end
	end
	local accept_npc = Scene.Instance:GetNpcByNpcId(self.vo.npc_id)
	if accept_npc then
		accept_npc:ForceSetVisible(false)
	end
	local main_role = Scene.Instance:GetMainRole()
	if commit_npc and main_role and main_role:GetLogicDistance(u3dpool.vec2(self.commit_npc.x, self.commit_npc.y), false) < 10 * 10 then
		Character.Update(self, now_time, elapse_time)
		if need_move then
			self.speed = Scene.ServerSpeedToClient(self.vo.move_speed)
			self:DoMove(commit_npc.real_pos)
			need_move = false
		end
	else
		need_move = true
		FollowObj.Update(self, now_time, elapse_time)
	end
end

function FollowNpc:GetOwerRoleId()
	return self.vo.owner_role_id
end

function FollowNpc:MoveEnd()
	if nil == self.distance then
		return false
	end
	return self.distance <= 9
end

function FollowNpc:EnterStateAttack()
	local anim_name = SceneObjAnimator.Atk1
	Character.EnterStateAttack(self, anim_name)
end

function FollowNpc:EnterStateMove()
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:SetInteger("status", ActionStatus.Run)
end

function FollowNpc:EnterStateStand()
	local part = self.draw_obj:GetPart(SceneObjPart.Main)
	part:SetInteger("status", ActionStatus.Idle)
end

function FollowNpc:CreateFollowUi()

end