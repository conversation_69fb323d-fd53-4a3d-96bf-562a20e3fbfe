return {
	["item_convert_default_table"]={need_gold=15,item_name="羽翼进阶丹",
		reward_item={item_id=22000,num=1,is_bind=1,},need_stuff_id=22000,seq=1,need_stuff_count=1,},
	["goldvip_shop"]={
		{limit_times=2,reward_item={item_id=22000,num=1,is_bind=1,},consume_val=30,seq=0,},
		{reward_item={item_id=22000,num=1,is_bind=1,},consume_val=10,},
		{reward_item={item_id=22000,num=1,is_bind=1,},seq=2,},
		{limit_times=10,reward_item={item_id=22000,num=1,is_bind=1,},consume_val=75,seq=3,},
		{reward_item={item_id=22000,num=1,is_bind=1,},consume_val=260,seq=4,consume_type=2,},
		{limit_times=10,consume_val=60,seq=5,},
		{limit_times=3,reward_item={item_id=22000,num=1,is_bind=1,},consume_val=200,seq=6,},
		{reward_item={item_id=22000,num=1,is_bind=1,},seq=7,},
		{reward_item={item_id=22000,num=1,is_bind=1,},consume_val=125,seq=8,},
		{limit_times=1,reward_item={item_id=22000,num=1,is_bind=1,},consume_val=1666,seq=9,consume_type=2,},},
	["item_convert"]={
		{item_name="坐骑进阶丹",reward_item={item_id=22000,num=1,is_bind=1,},seq=0,},
		{item_name="法宝进阶丹",reward_item={item_id=22000,num=1,is_bind=1,},},
		{item_name="灵宠进阶丹",reward_item={item_id=22000,num=1,is_bind=1,},seq=2,},
		{reward_item={item_id=22000,num=1,is_bind=1,},seq=3,},
		{item_name="神武进阶丹",reward_item={item_id=22000,num=1,is_bind=1,},seq=4,},
		{item_name="灵弓进阶丹",reward_item={item_id=22000,num=1,is_bind=1,},seq=5,},
		{item_name="灵骑进阶丹",reward_item={item_id=22000,num=1,is_bind=1,},seq=6,},
		{item_name="灵翼进阶丹",seq=7,},},
	["gold_convert"]={
		{seq=0,},
		{},
		{seq=2,},
		{seq=3,},},
	["goldvip_active"]={
		{need_gold=588,need_level=55,param1=7,convert_rate=10,reward_item={[0]={item_id=22000,num=1,is_bind=1,},},add_count=1,active_convert_gold=300,param2=1888,continue_days=30,},},
	["gold_convert_default_table"]={need_gold=0,gold_num=10,need_goldbind=15,seq=1,},
	["goldvip_shop_default_table"]={consume_val=100,
		reward_item={item_id=22000,num=1,is_bind=1,},limit_times=0,consume_type=1,seq=1,},
}
