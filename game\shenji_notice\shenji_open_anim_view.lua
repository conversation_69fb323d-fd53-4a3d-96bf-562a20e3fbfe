
-- 神机开启动画
ShenJiOpenAnimView = ShenJiOpenAnimView or BaseClass(SafeBaseView)

function ShenJiOpenAnimView:__init()
    self:SetMaskBg(false, true)
    self.active_close = false
    self:AddViewResource(0, "uis/view/shenji_notice_prefab", "shenji_anim_open")

    self.position_config = {
         [1] = {from = Vector3(-323, 68.5, 0), to = Vector3(-156, 68.5, 0)},
         [2] = {from = Vector3(-58.2, 297, 0), to = Vector3(-58.2, 145.8, 0)},
         [3] = {from = Vector3(269, 211, 0), to = Vector3(151.8, 120, 0)},
         [4] = {from = Vector3(240, -237, 0), to = Vector3(155.7, -160.2, 0)},
         [5] = {from = Vector3(-189, -246, 0), to = Vector3(-123.9, -159, 0)},
    }
    self.fragment_count = #self.position_config
end
--神机功能开启
function ShenJiOpenAnimView:ReleaseCallBack()
    self.fragment_list = {}
    self.rongjie_effect_loader = nil

    if self.show_item then
        self.show_item:DeleteMe()
        self.show_item = nil
    end

    if self.auto_click_onlock_timer then
        GlobalTimerQuest:CancelQuest(self.auto_click_onlock_timer)
        self.auto_click_onlock_timer = nil
    end
end

function ShenJiOpenAnimView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_unlock, BindTool.Bind1(self.OnClickOnLock, self))
    self.node_list.btn_unlock.button.interactable = false

    self.show_item = ItemCell.New(self.node_list.shenji_item_pos)

    self.fragment_list = {}
    for i = 1, self.fragment_count do
        self.fragment_list[i] = self.node_list["fragment_" .. i]
    end
    
end

function ShenJiOpenAnimView:ShowIndexCallBack()
    self:DoAnim()
end

function ShenJiOpenAnimView:DoAnim()

    self.node_list.rongjie_bg:SetActive(false)
    self.node_list.big_bg:SetActive(false)
    UITween.CanvasGroup(self.node_list.fragment_root).alpha = 0
    UITween.AlphaShow(GuideModuleName.ShenJiOpenAnimView,self.node_list.fragment_root.gameObject, 0.5, 1, 0.5, DG.Tweening.Ease.OutCubic)
    UITween.CanvasGroup(self.node_list.alpha_root).alpha = 0

    for i, v in ipairs(self.fragment_list) do
        v.transform.localPosition = self.position_config[i].from
    end

    ReDelayCall(self, function()
        for i, v in ipairs(self.fragment_list) do
            UITween.MoveAlphaShowPos(GuideModuleName.ShenJiOpenAnimView,v.gameObject, self.position_config[i].from, self.position_config[i].to, 0.5, DG.Tweening.Ease.OutCirc, nil, true)
        end
        -- AudioManager.PlayAndForget(ResPath.UiseRes("effect_shenji_notice_1"))
	end, 0.5, "MoveDelay")
    -- ReDelayCall(self, function()
    --     local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhuanpanka_pt")
    --     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.boom_root.transform, 4, nil, nil, nil, nil)
    -- end, 0.7, "UI_zhuanpanka_pt_effect_ShowDelay")
    ReDelayCall(self, function()
        self.node_list.big_bg:SetActive(true)
        --local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhuanpanka_pt")
        --EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.big_bg.transform, 4, nil, nil, nil, nil)

        self.node_list.fragment_root:SetActive(false)
        UITween.AlphaShow(GuideModuleName.ShenJiOpenAnimView,self.node_list.alpha_root.gameObject, 0.5, 1, 0.5, DG.Tweening.Ease.OutCubic, function()
            self.node_list.btn_unlock.button.interactable = true
        end)

        --5秒后自动点击解锁
        self.auto_click_onlock_timer = GlobalTimerQuest:AddDelayTimer(function()
            self:OnClickOnLock()
        end, 5)

	end, 1, "BigBgShowDelay")
end

function ShenJiOpenAnimView:OnFlush()
    self.show_item:SetData(ShenJiNoticeWGData.Instance:GetSpecialAnimShowItem())
end

function ShenJiOpenAnimView:OnClickOnLock()
    if self.auto_click_onlock_timer then
        GlobalTimerQuest:CancelQuest(self.auto_click_onlock_timer)
        self.auto_click_onlock_timer = nil
    end
    -- AudioManager.PlayAndForget(ResPath.UiseRes("effect_shenji_notice_2"))
    --print_error("点击解锁")
    self.node_list.alpha_root:SetActive(false)
    --显示溶解特效
    self.node_list.rongjie_bg:SetActive(true)

    self.rongjie_effect_loader = AllocAsyncLoader(self, "rongjie_effect_loader")
    self.rongjie_effect_loader:SetParent(self.node_list.rongjie_bg.transform)
    self.rongjie_effect_loader:SetLoadPriority(ResLoadPriority.high)
    local b, a = ResPath.GetEffectUi("UI_yuanpanrongjie")
    --self.debug_watch_time = Status.NowTime
    --print_error("加载特效 >>>>>> UI_yuanpanrongjie， now_time: ", Status.NowTime)
    self.rongjie_effect_loader:Load(b, a, function(gameobj)
        --print_error("加载特效完成 >>>>>> UI_yuanpanrongjie， now_time: ", Status.NowTime, "cost：", Status.NowTime - self.debug_watch_time)
        if not IsNil(gameobj) then
            gameobj.transform.localScale = Vector3.one
            gameobj.transform.localPosition = Vector3.zero
            self.node_list.big_bg:SetActive(false)
            ReDelayCall(self, function()
                local finish_callback2 = function ()
                    if self.node_list.effect_root then
                        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jiefeng, is_success = true, parent_node = self.node_list.effect_root})
                        --local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_shenqixiufuwanbi)
                        --EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_root.transform, 0.6, nil, nil, nil, nil, finish_callback3)
                        ReDelayCall(self, function()
                            self:Close()
                            --开启神机功能
                            local fun_cfg = FunOpen.Instance:GetFunByName(FunName.ShenJi)
                            local target_obj = MainuiWGCtrl.Instance.view:GetMainIconNodeListObj("BtnRoleBagView")
                            TipWGCtrl.Instance:ShowOpenFunFlyView(fun_cfg, target_obj)

                            --开启神机百炼功能
                            -- local fun_cfg = FunOpen.Instance:GetFunByName(FunName.sixiang_call_xqzl)
                            -- local target_obj = MainuiWGCtrl.Instance.view:GetMainTopBtnObj("BtnSiXiangCallView")
                            -- TipWGCtrl.Instance:ShowOpenFunFlyView(fun_cfg, target_obj)
                        end, 1, "delay_complete")
                    end
                end
                --播放光球特效
                if self.node_list.effect_root then
                    -- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_huijubaokai)
                    -- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_root.transform, 1.2, nil, nil, nil, nil, finish_callback2)
                    finish_callback2()
                end
            end, 2, "DelayPlayBoomEfect")
        end
    end)
end

