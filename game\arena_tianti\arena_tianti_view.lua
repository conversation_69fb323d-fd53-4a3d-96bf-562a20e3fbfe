ArenaTianTiView = ArenaTianTiView or BaseClass(SafeBaseView)

function ArenaTianTiView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_arena_tianti")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
    self:SetMaskBg()
end

function ArenaTianTiView:ReleaseCallBack()
	if self.scroll_hero_list then
		self.scroll_hero_list:DeleteMe()
		self.scroll_hero_list = nil
	end

	if self.scroll_rank_list then
		self.scroll_rank_list:DeleteMe()
		self.scroll_rank_list = nil
	end

	if self.scroll_award_list then
		self.scroll_award_list:DeleteMe()
		self.scroll_award_list = nil
	end

	if self.scroll_award_preview_list then
		self.scroll_award_preview_list:DeleteMe()
		self.scroll_award_preview_list = nil
	end

    if self.top_one_role then
        for key, value in pairs(self.top_one_role) do
            value:DeleteMe()
            value = nil
        end

        self.top_one_role = nil
    end

    if self.high_power_enter then
        self.high_power_enter:DeleteMe()
        self.high_power_enter = nil
    end

    self:CleanRefreshTimer()
    self:CleanHuiFuTimer()
	self:CleanEndTimer()
    self.is_init = nil
    self.show_rank = nil
    self.select_enemy_info = nil
    self.refreshing = nil
	self.fight_flag = nil
	self.select_pos = nil

	self.is_show_fight_max_state = false
	self.fight_max_tween_pos = nil
    self.fight_count_tween_pos = nil
    self.fight_max_tween_alpha = nil
end

function ArenaTianTiView:OpenCallBack()
	ArenaTiantiWGCtrl.Instance:ResetOpponentList()
	ArenaTiantiWGCtrl.Instance:ReqOtherRoleInfo(0)
	ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_CHALLENGE_INFO)
	ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_INFO)
end

function ArenaTianTiView:LoadCallBack()
    self.show_rank = false
	self.enemy_render_tab = {}
	self.is_show_fight_max_state = false                --展示一键秒杀按钮状态.

	self.fight_max_tween_pos = self.node_list.btn_fight_max:GetComponent(typeof(UGUITweenPosition))
    self.fight_max_tween_alpha = self.node_list.btn_fight_max:GetComponent(typeof(UGUITweenAlpha))
    self.fight_count_tween_pos = self.node_list.fight_count_group:GetComponent(typeof(UGUITweenPosition))

    if not self.scroll_hero_list then
		self.scroll_hero_list = AsyncListView.New(ArenaTianTiEnemyRoleRender, self.node_list.scroll_hero_list)
		self.scroll_hero_list:SetSelectCallBack(BindTool.Bind1(self.SetHeroClickCallBack, self))
	end

    self.scroll_rank_list = AsyncListView.New(ArenaTianTiMainRankRender, self.node_list.scroll_rank_list)
	self.scroll_rank_list:SetSelectCallBack(BindTool.Bind1(self.SetRankItemCallBack, self))

    self.scroll_award_list = AsyncListView.New(ArenaTianTiMainNumAwardRender, self.node_list.scroll_award_list)
	self.scroll_award_list:SetUseRenderClick(true)
	self.scroll_award_list:SetSelectCallBack(BindTool.Bind1(self.SetAwardNumCallBack, self))

	self.scroll_award_preview_list = AsyncListView.New(ArenaTianTiMainItemCellRender, self.node_list.scroll_award_preview_list)
	self.scroll_award_preview_list:SetStartZeroIndex(true)

	XUI.AddClickEventListener(self.node_list.btn_left_rank, BindTool.Bind1(self.OnClickShowHideRank, self))
    XUI.AddClickEventListener(self.node_list.btn_right_rank, BindTool.Bind1(self.OnClickShowHideRank, self))
    XUI.AddClickEventListener(self.node_list.btn_rank_award, BindTool.Bind1(self.OnClickRankReward, self))
    XUI.AddClickEventListener(self.node_list.btn_refresh, BindTool.Bind1(self.OnClickRefreshEnemy, self))
    XUI.AddClickEventListener(self.node_list.btn_fight, BindTool.Bind1(self.OnClickFight, self))
    XUI.AddClickEventListener(self.node_list.btn_fight_max, BindTool.Bind1(self.OnClickFightMax, self))
    XUI.AddClickEventListener(self.node_list.btn_zhenjia, BindTool.Bind1(self.OnClickZhenjia, self))
    XUI.AddClickEventListener(self.node_list.btn_shop, BindTool.Bind1(self.OnClickGoShop, self))
    XUI.AddClickEventListener(self.node_list.btn_record, BindTool.Bind1(self.OnClickGoRecord, self))
    XUI.AddClickEventListener(self.node_list.btn_rank, BindTool.Bind1(self.OnClickGoRank, self))
	XUI.AddClickEventListener(self.node_list.btn_perview_close, BindTool.Bind1(self.OnClickPreviewClose, self))
	XUI.AddClickEventListener(self.node_list.btn_miaosha, BindTool.Bind1(self.OnClickBtnMiaoSha, self))
	XUI.AddClickEventListener(self.node_list.btn_open_privilege, BindTool.Bind1(self.OnClickBtnOpenPrivilege, self))

	self.node_list.title_view_name.text.text = Language.Field1v1.TianTiTxt7
	if self.node_list.RawImage_tongyong then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_jjc_ttzb_bg")
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function ArenaTianTiView:OnClickCloseWindow()
	SafeBaseView.OnClickCloseWindow(self)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ARENA_TIANTI then
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function ArenaTianTiView:OnFlush()
    local info = ArenaTianTiWGData.Instance:GetUserinfo()
	if nil == info then
		return
	end

	local sorted_list = info.rank_list
	if not IsEmptyTable(sorted_list) then
		local enemy_info, pos = ArenaTianTiWGData.Instance:GetInitTiaoZhanRoleInfo()
		self.scroll_hero_list:SetDataList(sorted_list)

		--敌人不变不刷新
		if self.select_pos ~= pos or (not self.select_enemy_info or self.select_enemy_info.user_id ~= enemy_info.user_id) then
			self.select_pos = pos
			self.scroll_hero_list:SelectIndex(pos)
			self:SetSelectEnemyInfo(enemy_info)
		end
	end

	self.node_list.text_my_rank.text.text = info.rank
    self.node_list.text_my_score.text.text = info.new_score

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self:FlushTopModel(role_vo, 1)

    local play_rank_list = ArenaTianTiWGData.Instance:GetChallengeRankListInfo()
    self.scroll_rank_list:SetDataList(play_rank_list)

    local award_list = ArenaTianTiWGData.Instance:GetFieldCountRewardCfgBySpSort()
    self.scroll_award_list:SetDataList(award_list)
	local jump_index = ArenaTianTiWGData.Instance:GetChallengeFieldBattleCountJumpIndex()
    self.scroll_award_list:JumpToIndex(#award_list - jump_index + 1)--这个B列表要翻转计算.

	local battle_count = ArenaTianTiWGData.Instance:GetChallengeFieldBattleCount()
	self.node_list.text_fight_num.text.text = battle_count

	self:UpdateSkipInfo()

    self:SetFreeTimeShow()
    self:SetRefreshEnemyTime()
	self:SetTimeShow()
end

function ArenaTianTiView:SetTimeShow()
	local end_time = ArenaTianTiWGData.Instance:GetChallengeEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if end_time <= server_time then
		self.node_list.text_end_time.text.text = Language.Field1v1.TianTiTxt17
		return
	end

	self:CleanEndTimer()
    self.timer_end = CountDown.Instance:AddCountDown(end_time - server_time, 1,
        -- 回调方法
        function(elapse_time, total_time)
			if self.node_list.text_end_time then
				local time_show = math.ceil(total_time - elapse_time)
				self.node_list.text_end_time.text.text = TimeUtil.FormatSecondDHM(time_show)
			end
        end,
        -- 倒计时完成回调方法
        function()
			if self.node_list.text_end_time then
				self.node_list.text_end_time.text.text = Language.Field1v1.TianTiTxt17
			end
        end
    )
end

function ArenaTianTiView:CleanEndTimer()
    if self.timer_end and CountDown.Instance:HasCountDown(self.timer_end) then
        CountDown.Instance:RemoveCountDown(self.timer_end)
        self.timer_end = nil
    end
end

function ArenaTianTiView:SetHeroClickCallBack(item, cell_index, is_default, is_click)
	if not item or not item:GetData() then
		return
	end

	self:SetSelectEnemyInfo(item:GetData())
end

function ArenaTianTiView:SetSelectEnemyInfo(info)
	self.select_enemy_info = info
	self:FlushTopModel(info, 2)
end

function ArenaTianTiView:FlushTopModel(role_info, index)
	if IsEmptyTable(role_info) then
		return
	end

	self.top_one_role = self.top_one_role or {}
	if not self.top_one_role[index] then
		self.top_one_role[index] = ArenaTianTiMainRoleRender.New(self.node_list["node_role_parent" .. index], self)
	end

	self.top_one_role[index]:SetData(role_info)
end

function ArenaTianTiView:OnClickShowHideRank()
    self.show_rank = not self.show_rank
	if self.show_rank then
		ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_INFO)
	end
    local tweener = self.node_list.node_rank_parent.transform:DOLocalMoveX(self.show_rank and 0 or -280, 0.5):SetEase(DG.Tweening.Ease.Linear)
    tweener:OnComplete(function ()
        if self.node_list.btn_left_rank then
            self.node_list.btn_left_rank:SetActive(self.show_rank)
			self.node_list.btn_right_rank:SetActive(not self.show_rank)
        end
    end)
end

function ArenaTianTiView:OnClickRankReward()
    ArenaTiantiWGCtrl.Instance:OpenRankRewardTip()
end

function ArenaTianTiView:OnClickRefreshEnemy()
    if self.refreshing then
        return
    end

    ArenaTiantiWGCtrl.Instance:ResetOpponentList()
    self.refreshing = true

    self:SetRefreshEnemyTime()
    local refresh_cd = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("refresh_cd")
    self:CleanRefreshTimer()
	self.node_list.text_refresh_time.text.text = refresh_cd
    self.timer_refresh = CountDown.Instance:AddCountDown(tonumber(refresh_cd), 1,
		-- 回调方法
		function(elapse_time, total_time)
			if self.node_list.text_refresh_time then
				self.node_list.text_refresh_time.text.text = math.ceil(total_time - elapse_time)
			end
		end,
		-- 倒计时完成回调方法
		function()
			if self.node_list.text_refresh_time then
				self.refreshing = nil
				self:SetRefreshEnemyTime()
			end
		end
	)
end

function ArenaTianTiView:CleanRefreshTimer()
    if self.timer_refresh and CountDown.Instance:HasCountDown(self.timer_refresh) then
        CountDown.Instance:RemoveCountDown(self.timer_refresh)
        self.timer_refresh = nil
    end
end

function ArenaTianTiView:SetRefreshEnemyTime()
    self.node_list.image_refresh:SetActive(not self.refreshing)
    self.node_list.text_refresh_time:SetActive(self.refreshing == true)
end

function ArenaTianTiView:CloseCallBack()
	if not self.fight_flag then
		if ViewManager.Instance:IsOpen(GuideModuleName.ActJjc) then
			Field1v1WGCtrl.Instance:Open(TabIndex.arena_enter)
		end
	end

	self.is_show_fight_max_state = false
end

--一键秒杀 消耗全部挑战次数.
function ArenaTianTiView:OnClickFightMax()
	self:OnClickFight(true)

	self.is_show_fight_max_state = false
	self:FlushFightButtonState()
end

--点击战斗
function ArenaTianTiView:OnClickFight(is_fight_max)
	local end_time = ArenaTianTiWGData.Instance:GetChallengeEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if end_time <= server_time then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Field1v1.TianTiTxt17)
		return
	end

	if ArenaTianTiWGData.Instance:GetResidueTiaoZhanNum() <= 0 then
		local join_item_id = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("join_item_id")
    	TipWGCtrl.Instance:OpenItem({item_id = join_item_id})

		self.is_show_fight_max_state = false
		self:FlushFightButtonState()
		return
	end

	local info = ArenaTianTiWGData.Instance:GetUserinfo()
	if nil == info then
		return
	end

	local enemy_info = self.select_enemy_info
	if nil == enemy_info then
		return
	end

	local role_base = RoleWGData.Instance.role_vo
	local is_skip = false
	local data = {}
	data.opponent_index = enemy_info.index
	data.rank_pos = enemy_info.rank_pos
	data.opponent_uuid = enemy_info.user_id
	local role_data = ArenaTianTiWGData.Instance:GetRoleInfoByUid(enemy_info.user_id)
	if not role_data then
		print_error("获取角色数据失败: ", enemy_info.user_id)
		return
	end

	data.capability = role_data.capability

	local seckill_level = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("seckill_level")
	local user_info = ArenaTianTiWGData.Instance:GetUserinfo()
	if role_base.level >= seckill_level and role_base.capability >= role_data.capability and user_info.rank < enemy_info.rank then
		is_skip = true
	else
		is_skip = false
	end

	data.is_skip = is_skip and 1 or 0

	local miaosha_flag = ArenaTianTiWGData.Instance:GetIsCanSkip()
	if miaosha_flag and not is_fight_max and not self.is_show_fight_max_state then
		self.is_show_fight_max_state = true
		self:FlushFightButtonState()
		return
	end

	if role_base.capability >= role_data.capability then
		if is_fight_max then
			ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_FIGHT_NUM, enemy_info.user_id, ArenaTianTiWGData.Instance:GetResidueTiaoZhanNum())
		else
			ArenaTiantiWGCtrl.Instance:ResetFieldFightReq(data)
		end
		self.fight_flag = true
	else
		self:HighPowerIsEnter(data)
	end
end

-- 刷新按钮状态
function ArenaTianTiView:FlushFightButtonState()
	local str = self.is_show_fight_max_state and Language.Field1v1.FightText2 or Language.Field1v1.FightText
    self.node_list.text_btn_fight.text.text = str
    local is_show_arrived = false

    if self.is_show_fight_max_state then
        is_show_arrived = true
    end

    if is_show_arrived then
        if not IsNil(self.fight_max_tween_pos) then
            self.fight_max_tween_pos:PlayForward()
        end
        if not IsNil(self.fight_max_tween_alpha) then
            self.fight_max_tween_alpha:PlayForward()
        end
        if not IsNil(self.fight_count_tween_pos) then
            self.fight_count_tween_pos:PlayForward()
        end
    else
        if not IsNil(self.fight_max_tween_pos) then
            self.fight_max_tween_pos:PlayReverse()
        end
        if not IsNil(self.fight_max_tween_alpha) then
            self.fight_max_tween_alpha:PlayReverse()
        end
        if not IsNil(self.fight_count_tween_pos) then
            self.fight_count_tween_pos:PlayReverse()
        end
    end

	self.node_list.miaosha_group:SetActive(not is_show_arrived)
end

--挑战高战力tips提示
function ArenaTianTiView:HighPowerIsEnter(data)
	if nil == data then
		return
	end

	if nil == self.high_power_enter then
		self.high_power_enter = Alert.New()
		self.high_power_enter:SetShowCheckBox(true)
		self.high_power_enter:SetCheckBoxDefaultSelect(false)
		self.high_power_enter:SetMarkName("JingJiChang_HighPowerIsEnter")
	end

	local ok_func = function ()
		ArenaTiantiWGCtrl.Instance:ResetFieldFightReq(data)
		self.fight_flag = true
	end
	self.high_power_enter:SetOkFunc(ok_func)
	self.high_power_enter:SetLableString(Language.Field1v1.TiaoZhanHighPower)
	self.high_power_enter:Open()
end

function ArenaTianTiView:OnClickZhenjia()
    local join_item_id = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("join_item_id")
    TipWGCtrl.Instance:OpenItem({item_id = join_item_id})
end

function ArenaTianTiView:OnClickGoShop()
	ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.shop_honor)
end

function ArenaTianTiView:OnClickGoRecord()
    ArenaTiantiWGCtrl.Instance:OpenRecordTip()
end

function ArenaTianTiView:OnClickGoRank()
	ArenaTiantiWGCtrl.Instance:OpenRankView()
end

--次数恢复时间戳
function ArenaTianTiView:SetFreeTimeShow()
    local user_info = ArenaTianTiWGData.Instance:GetUserinfo()
    local remain_num =  ArenaTianTiWGData.Instance:GetResidueTiaoZhanNum()
    local sum_num = ArenaTianTiWGData.Instance:GetSumTiaoZhanNum()
	local free_count = ArenaTianTiWGData.Instance:GetChnageTotalCount()
    self.node_list.text_tianzhannum.text.text = string.format("%s/%s", remain_num, sum_num)

    self:CleanHuiFuTimer()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local off_time = user_info.next_free_time - server_time
    if free_count < sum_num then
        self.timer_times = CountDown.Instance:AddCountDown(off_time, 1,
            -- 回调方法
            function(elapse_time, total_time)
                if self.node_list.text_huifu_time then
					local show_time = TimeUtil.FormatSecond(math.ceil(total_time - elapse_time), 2)
                    self.node_list.text_huifu_time.text.text = string.format(Language.Field1v1.TianTiTxt11, show_time)
                end
            end,
            -- 倒计时完成回调方法
            function()
				if self:IsOpen() then
					ArenaTiantiWGCtrl.Instance:ReqFieldGetDetailRankInfo()
				end
            end
        )
    else
        self.node_list.text_huifu_time.text.text = Language.Field1v1.TianTiTxt2
    end
end

function ArenaTianTiView:CleanHuiFuTimer()
    if self.timer_times and CountDown.Instance:HasCountDown(self.timer_times) then
        CountDown.Instance:RemoveCountDown(self.timer_times)
        self.timer_times = nil
    end
end

function ArenaTianTiView:SetAwardNumCallBack(item, cell_index, is_default, is_click)
	if not item or not item:GetData() then
		return
	end

	if is_click then
		self:SetBoxAwardPreview(item)
	end
end

--设置宝箱弹窗
function ArenaTianTiView:SetBoxAwardPreview(item)
	self.node_list.node_peogress_award_preview:SetActive(true)
	local off_pos = self.node_list["node_award_preview_parent"].transform:InverseTransformPoint(item.view.transform.position)
	local old_pos = self.node_list["node_award_preview_parent"].transform.localPosition
	local new_pos = old_pos + off_pos
	self.node_list["node_award_preview_parent"].transform.localPosition = Vector3(new_pos.x - 150, new_pos.y, new_pos.z)

	local data = item:GetData()
	self.scroll_award_preview_list:SetDataList(data.count_reward)
end

function ArenaTianTiView:OnClickPreviewClose()
	self.node_list.node_peogress_award_preview:SetActive(false)
end

function ArenaTianTiView:SetRankItemCallBack(item, cell_index, is_default, is_click)
	if not item or not item:GetData() or not is_click then
		return
	end

	local data = item:GetData()
	if data.uuid == RoleWGData.Instance:GetUUid() then
		return
	end

	local now_server_id = RoleWGData.Instance:GetCurServerId()
	if now_server_id ~= data.server_id or data.is_robot == 1 then
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt14)
		return
	end

	local items, callback_param = self:GetItems(data)
	local role_data = {prof = data.prof, role_name = data.role_name, plat_type = 0, 
		is_online = false, sex = data.sex, user_id = data.uuid.temp_low} --server_id = data.server_id

    UiInstanceMgr.Instance:OpenCustomMenu(items, nil,
        BindTool.Bind2(self.OnClickMenuCallback, self, data), callback_param, nil, nil, nil, role_data)
end

function ArenaTianTiView:GetItems(data)
    local items = { Language.KuafuPVP.MenuBrouse }
    local callback_param = {}
    local index = 1
    if nil == SocietyWGData.Instance:FindFriend(data.uuid.temp_low) then
        table.insert(items, Language.KuafuPVP.MenuAddFriend)
        callback_param.has_add_friend = true
        index = index + 1
        callback_param.add_friend_index = index
    end
    return items, callback_param
end

function ArenaTianTiView:OnClickMenuCallback(data, index, sender, callback_param)
    if 1 == index then                                                                     --查看信息
        BrowseWGCtrl.Instance:OpenWithUid(data.uuid.temp_low)
    elseif callback_param.has_add_friend and callback_param.add_friend_index == index then --加为好友
        SocietyWGCtrl.Instance:IAddFriend(data.uuid.temp_low)
    end
end

function ArenaTianTiView:OnClickBtnMiaoSha()
	if not RechargeWGData.Instance:GetIsCrossChallengeSkip() then
		TipWGCtrl.Instance:OpenAlertTips(Language.Field1v1.TianTiTxt20, function ()
			if self:IsOpen() then
				if FunOpen.Instance:GetFunIsOpenedByTabName("recharge_month_card") then
					FunOpen.Instance:OpenViewNameByCfg("vip#recharge_month_card")
				else
					TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt21)
				end
			end
		end)
		return
	end

	ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_SKIP)
end

function ArenaTianTiView:OnClickBtnOpenPrivilege()
	if FunOpen.Instance:GetFunIsOpenedByTabName("recharge_month_card") then
		FunOpen.Instance:OpenViewNameByCfg("vip#recharge_month_card")
	else
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.TianTiTxt21)
	end
end


function ArenaTianTiView:UpdateSkipInfo()
	local miaosha_flag = ArenaTianTiWGData.Instance:GetIsCanSkip()
	self.node_list.image_miaosha:SetActive(miaosha_flag)
	self.node_list.btn_open_privilege:SetActive(not RechargeWGData.Instance:GetIsCrossChallengeSkip())
end

-------------------------------------- 界面上两个角色 ----------------------------------------
ArenaTianTiMainRoleRender = ArenaTianTiMainRoleRender or BaseClass(BaseRender)
function ArenaTianTiMainRoleRender:__init(instance, parent_view)
	--XUI.AddClickEventListener(self.node_list.rank_upvote_btn, BindTool.Bind1(self.OnClickUpvoteBtn, self))
	self.parent_view = parent_view

	self.late_id = -1
end

function ArenaTianTiMainRoleRender:__delete()
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.parent_view = nil
end

function ArenaTianTiMainRoleRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local role_info = ArenaTianTiWGData.Instance:GetRoleInfoByUid(self.data.user_id)
	if IsEmptyTable(role_info) and self.data.uuid then
		role_info = self.data
	end

	if IsEmptyTable(role_info) and not self.data.uuid then
		return
	end

	local user_id = self.data.user_id or self.data.uuid.temp_low
	self.node_list["name_txt"].text.text = role_info.name .. "s_" .. (self.data.server_id or 0)
	self.node_list["capability_txt"].text.text = role_info.capability

	if self.late_id ~= user_id then
		self.late_id = user_id
		self:FlushModel(role_info)
	end
end

function ArenaTianTiMainRoleRender:FlushModel(role_info)
	if IsEmptyTable(role_info) then
		return
	end

	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}

		self.role_model:SetRenderTexUI3DModel(display_data)
		self.role_model:SetRTAdjustmentRootLocalScale(0.8)
		self.role_model:SetIsSupportClip(true)
	end

	local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
	self.role_model:SetModelResInfo(role_info, special_status_table)
	self.role_model:FixToOrthographic(self.parent_view.root_node_transform)
	self.role_model:PlayLastAction()
end

function ArenaTianTiMainRoleRender:OnClickUpvoteBtn()
	local upvote_data = ArenaTianTiWGData.Instance:GetChallengeRankListInfo()
	local top_one_role_data = upvote_data[1]
	if top_one_role_data and top_one_role_data.uuid.temp_low > 0 then
		if top_one_role_data.daily_like_flag == 0 then
			ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_LIKE, top_one_role_data.uuid)
		else
			TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips2)
		end
	else
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips)
	end
end


----------------------------下方四个对手 -----------------------------

ArenaTianTiEnemyRoleRender = ArenaTianTiEnemyRoleRender or BaseClass(BaseRender)
function ArenaTianTiEnemyRoleRender:__init(instance, parent_view)
	self.parent_view = parent_view
end

function ArenaTianTiEnemyRoleRender:__delete()
	self.parent_view = nil
end

function ArenaTianTiEnemyRoleRender:OnFlush()
    local role_info = ArenaTianTiWGData.Instance:GetRoleInfoByUid(self.data.user_id)
	if IsEmptyTable(role_info) then
		return
	end

	local main_cap = RoleWGData.Instance:GetMainRoleCap()
	local color = main_cap >= role_info.capability and "#ffffff" or IS_CAN_COLOR.NOT_ENOUGH
    self.node_list.cap_value.text.text = string.format(Language.Field1v1.TianTiTxt8, color, role_info.capability)
	local prof_value = role_info.prof % 10
	prof_value = prof_value > 0 and prof_value or 1
    local bundle, asset = ResPath.GetF2Field1v1(string.format("a3_jjc_ttzb_ren_%s_%s", role_info.sex, prof_value))
	self.node_list.image_hero_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.image_hero_icon.image:SetNativeSize()
	end)

    self.node_list.text_name.text.text = role_info.name .. "s_" .. (self.data.server_id or 0)
    self.node_list.text_score.text.text = self.data.score
    self.node_list.text_add_score.text.text = string.format(Language.Field1v1.TianTiTxt6, self.data.win_score)
end

function ArenaTianTiEnemyRoleRender:OnSelectChange(is_select)
    self.node_list.image_select:SetActive(is_select)
end


----------------------------主界面排行榜 -----------------------------
ArenaTianTiMainRankRender = ArenaTianTiMainRankRender or BaseClass(BaseRender)
function ArenaTianTiMainRankRender:__init(instance)
end

function ArenaTianTiMainRankRender:__delete()
end

function ArenaTianTiMainRankRender:OnFlush()
    if not self.data then
        return
    end

	local coloe_str = self.data.uuid == RoleWGData.Instance:GetUUid() and IS_CAN_COLOR.ENOUGH or "#FFFFE9"
    self.node_list.text_name.text.text = string.format(Language.Field1v1.TianTiTxt18, coloe_str, self.data.role_name, self.data.server_id)
    self.node_list.text_rank.text.text = self.data.rank_pos > 2 and self.data.rank_pos + 1 or ""
    self.node_list.text_score.text.text = string.format("<color=%s>%s</color>", coloe_str, self.data.score)
    self.node_list.image_rank:SetActive(self.data.rank_pos <= 2)
    if self.data.rank_pos <= 2 then
        self.node_list.image_rank.image:LoadSprite(ResPath.GetCommonImages("a3_ty_panking_" .. self.data.rank_pos + 1))
    end

	self.node_list.bg:SetActive(self.data.rank_pos <= 2)
    if self.data.rank_pos <= 2 then
        self.node_list.bg.image:LoadSprite(ResPath.GetF2Field1v1("a3_ttzb_dt" .. self.data.rank_pos + 1))
    end
end



----------------------------主界面次数奖励 -----------------------------
ArenaTianTiMainNumAwardRender = ArenaTianTiMainNumAwardRender or BaseClass(BaseRender)

function ArenaTianTiMainNumAwardRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_box, BindTool.Bind1(self.OnClickBox, self))
end

function ArenaTianTiMainNumAwardRender:__delete()
end

function ArenaTianTiMainNumAwardRender:OnFlush()
    if not self.data then
        return
    end

    local battle_count = ArenaTianTiWGData.Instance:GetChallengeFieldBattleCount()
    self.node_list.text_num.text.text = string.format(Language.Field1v1.TianTiTxt3, self.data.battle_count)
    local progress = 0
    local list_data = ArenaTianTiWGData.Instance:GetFieldCountRewardCfgBySpSort()
    local last_data = list_data[self.data.index]
    if battle_count >= self.data.battle_count then
        progress = 1
    elseif last_data and last_data.battle_count < battle_count then
        progress = (battle_count - last_data.battle_count) / (self.data.battle_count - last_data.battle_count)
    end
    self.node_list.image_progress.image.fillAmount = progress

    local red_show = ArenaTianTiWGData.Instance:GetCountRewardRemindByIndex(self.data)
    self.node_list.image_red:SetActive(red_show)

	local count_reward_flag = ArenaTianTiWGData.Instance:GetChallengeFieldCountRewardByIndex(self.data.index)
	self.node_list.image_ylq:SetActive(count_reward_flag == 1)
end

function ArenaTianTiMainNumAwardRender:OnClickBox()
    local red_show = ArenaTianTiWGData.Instance:GetCountRewardRemindByIndex(self.data)
    if red_show then
        ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_COUNT_REWARD, self.data.index)
    else
		self:OnClick(true)
    end
end


--天梯界面的物品框
ArenaTianTiMainItemCellRender = ArenaTianTiMainItemCellRender or BaseClass(BaseRender)

function ArenaTianTiMainItemCellRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.node_item_parent.transform)
end

function ArenaTianTiMainItemCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ArenaTianTiMainItemCellRender:OnFlush()
    if not self.data then
        return
    end

	self.item_cell:SetData(self.data)
end