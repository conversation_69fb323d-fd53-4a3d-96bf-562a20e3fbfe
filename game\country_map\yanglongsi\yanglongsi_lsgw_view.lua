YangLongSiLSGWView = YangLongSiLSGWView or BaseClass(SafeBaseView)

function YangLongSiLSGWView:__init()
	self:SetMaskBg()

	self:AddViewResource(0, "uis/view/country_map_ui/yanglonsi_ui_prefab", "layout_yanglongsi_lsgw")
end

function YangLongSiLSGWView:OpenCallBack()
	YangLongSiWGCtrl.Instance:SendCSCrossYangLongOperate(CROSS_YANGLONG_OPERATE_TYPE
	.CROSS_YANGLONGOPERATE_TYPE_SCORE_RANK_INFO)
end

function YangLongSiLSGWView:LoadCallBack()
	self.rank_role_model_list = {}
	self.rank_role_id_cache = {}

	for i = 1, 3 do
		if not self.rank_role_model_list[i] then
			self.rank_role_model_list[i] = RoleModel.New()
			local node = self.node_list["role_display" .. i]
			local display_data = {
				parent_node = node,
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.S,
				can_drag = true,
			}
	
			self.rank_role_model_list[i]:SetRenderTexUI3DModel(display_data)
			-- self.rank_role_model_list[i]:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false,
			-- 	MODEL_CAMERA_TYPE.BASE)
			self:AddUiRoleModel(self.rank_role_model_list[i])
		end

		local data = YangLongSiaWGData.Instance:GetRankRewardCfgByRankId(i)
		if not IsEmptyTable(data) then
			local b, a = ResPath.GetTitleModel(data.title_id)
			self.node_list["title_display" .. i]:ChangeAsset(b, a, false)
		end
	end

	if not self.lsgw_convert_list then
		self.lsgw_convert_list = AsyncBaseGrid.New()
		local bundle = "uis/view/country_map_ui/yanglonsi_ui_prefab"
		local asset = "lsgw_convert_cell"
		self.lsgw_convert_list:CreateCells({
			col = 5,
			change_cells_num = 1,
			list_view = self.node_list.lsgw_convert_list,
			assetBundle = bundle,
			assetName = asset,
			itemRender = LSGWShowItemRender
		})
		self.lsgw_convert_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.btn_go_shop, BindTool.Bind1(self.OnClickGoShopBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_go_rank, BindTool.Bind1(self.OnClickGoRankBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_rank_list, BindTool.Bind1(self.OnClickRankListBtn, self))
	XUI.AddClickEventListener(self.node_list.lucky_item_icon, BindTool.Bind1(self.OnClickLuckyItemIconBtn, self))
end

function YangLongSiLSGWView:ReleaseCallBack()
	if self.rank_role_model_list then
		for k, v in pairs(self.rank_role_model_list) do
			v:DeleteMe()
		end
	end
	self.rank_role_model_list = {}

	if self.lsgw_convert_list then
		self.lsgw_convert_list:DeleteMe()
		self.lsgw_convert_list = nil
	end
end

function YangLongSiLSGWView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
		elseif k == "score_rank_info" then
			self:FlushRankPanel()
		elseif k == "base_info" then
			self:FlushShopPanel()
		end
	end

	local yanglongsi_lsgw_remind = YangLongSiaWGData.Instance:GetLSGWRemind()
	self.node_list.lsgw_shop_remind:SetActive(yanglongsi_lsgw_remind)
end

function YangLongSiLSGWView:FlushRankPanel()
	local rank_info = YangLongSiaWGData.Instance:GetLSGWRankInfo()
	for i = 1, 3 do
		local data = rank_info[i]
		local no_data = IsEmptyTable(data)
		local name = no_data and Language.YangLongSi.RankXuWeiYiDai or data.name

		if not no_data then
			local flush_fun = function(protocol)
				if not self.node_list then
					return
				end

				if self.rank_role_model_list[i] then
					local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true,
						ignore_shouhuan = true, ignore_tail = true, ignore_waist = true }
					self.rank_role_model_list[i]:SetModelResInfo(protocol, ignore_table)
					self.rank_role_model_list[i]:PlayRoleShowAction()
					self.rank_role_model_list[i]:SetModelScale(Vector3(0.75, 0.75, 0.75))
				end
			end

			local key_str = data.uuid.temp_high .. data.uuid.temp_low

			if not self.rank_role_id_cache[i] or key_str ~= self.rank_role_id_cache[i] then
				BrowseWGCtrl.Instance:SendGlobalQueryRoleInfo(data.uuid.temp_high, data.uuid.temp_low, nil, flush_fun)
				self.rank_role_id_cache[i] = key_str
			elseif key_str == self.rank_role_id_cache[i] then
				self.rank_role_model_list[i]:PlayLastAction()
			end
		else
			self.rank_role_id_cache[i] = nil
		end

		self.node_list["role_name" .. i].text.text = name
		self.node_list["role_display" .. i]:SetActive(not no_data)
		self.node_list["no_role_info" .. i]:SetActive(no_data)
	end
end

function YangLongSiLSGWView:FlushShopPanel()
	local cfg = YangLongSiaWGData.Instance:GetConvertCfg()
	local has_data = not IsEmptyTable(cfg)

	if has_data then
		self.lsgw_convert_list:SetDataList(cfg)
	end

	self.node_list.lsgw_convert_list:SetActive(has_data)
	self.node_list.not_shop_data:SetActive(not has_data)

	local convert_cost_item = YangLongSiaWGData.Instance:GetConvertCostItemId()
	if nil ~= convert_cost_item then
		local bundel, asset = ResPath.GetItem(convert_cost_item)

		self.node_list.lucky_item_icon.image:LoadSprite(bundel, asset, function()
			self.node_list.lucky_item_icon.image:SetNativeSize()
		end)
		self.node_list.lucky_item_num.text.text = ItemWGData.Instance:GetItemNumInBagById(convert_cost_item)
	end
end

function YangLongSiLSGWView:OnClickGoShopBtn()
	self:FlushShopPanel()
	self:ChangePanelState(false)
end

function YangLongSiLSGWView:OnClickGoRankBtn()
	self:ChangePanelState(true)
end

function YangLongSiLSGWView:OnClickRankListBtn()
	YangLongSiWGCtrl.Instance:OpenYangLongSiLSGWRankView()
end

function YangLongSiLSGWView:OnClickLuckyItemIconBtn()
	local convert_cost_item = YangLongSiaWGData.Instance:GetConvertCostItemId()

	if nil ~= convert_cost_item then
		TipWGCtrl.Instance:OpenItem({ item_id = convert_cost_item })
	end
end

function YangLongSiLSGWView:ChangePanelState(show_rank)
	self.node_list.lsgw_rank_panel:SetActive(show_rank)
	self.node_list.lsgw_shop_panel:SetActive(not show_rank)

	local bg_img_name = "a2_smhy_hybz_bg_2"
	if show_rank then
		bg_img_name = "a2_smhy_hybz_bg_1"
	end
	self.node_list.yanglongsi_lsgw_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(bg_img_name))
end

LSGWShowItemRender = LSGWShowItemRender or BaseClass(BaseRender)
function LSGWShowItemRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function LSGWShowItemRender:LoadCallBack()
	if not self.item then
		self.item = ItemCell.New(self.node_list.cell_pos)
	end

	XUI.AddClickEventListener(self.node_list.convert_btn, BindTool.Bind1(self.OnClickConvertBtn, self))
	XUI.AddClickEventListener(self.node_list.stuff_icon, BindTool.Bind1(self.OnClickStuffIconBtn, self))
end

function LSGWShowItemRender:OnFlush()
	local data = self:GetData()

	if IsEmptyTable(data) then
		return
	end

	local time = YangLongSiaWGData.Instance:GetConvertTimeBySeq(data.seq)
	local is_times_limit = data.times_limit ~= 0
	local item_count = ItemWGData.Instance:GetItemNumInBagById(data.stuff_id_1)
	local has_enough_stuff = item_count >= data.stuff_num_1
	local time_enough = not is_times_limit or (is_times_limit and (time < data.times_limit))
	local can_exchange = has_enough_stuff and time_enough
	local limit_buy_str = ToColorStr((data.times_limit - time) .. "/" .. data.times_limit,
		time_enough and COLOR3B.D_GREEN or COLOR3B.PINK)
	self.node_list.limit_count_desc.text.text = is_times_limit and limit_buy_str or Language.YangLongSi.NotLimitBuy
	XUI.SetGraphicGrey(self.node_list.convert_btn, not can_exchange)

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item.item_id)
	self.item:SetData(data.item)
	self.node_list.cell_name.text.text = item_cfg.name

	local bundel, asset = ResPath.GetItem(data.stuff_id_1)
	self.node_list.stuff_icon.image:LoadSprite(bundel, asset, function()
		self.node_list.stuff_icon.image:SetNativeSize()
	end)

	local stuff_str = ToColorStr(item_count .. "/" .. data.stuff_num_1,
		has_enough_stuff and COLOR3B.L_GREEN or COLOR3B.L_RED)
	self.node_list.stuff_num.text.text = stuff_str
end

function LSGWShowItemRender:OnClickConvertBtn()
	local data = self:GetData()

	if IsEmptyTable(data) then
		return
	end

	local time = YangLongSiaWGData.Instance:GetConvertTimeBySeq(data.seq)
	local is_times_limit = data.times_limit ~= 0
	local item_count = ItemWGData.Instance:GetItemNumInBagById(data.stuff_id_1)
	local has_enough_stuff = item_count >= data.stuff_num_1
	local time_enough = not is_times_limit or (is_times_limit and (time < data.times_limit))
	local can_exchange = has_enough_stuff and time_enough

	if can_exchange then
		YangLongSiWGCtrl.Instance:SendCSCrossYangLongOperate(
		CROSS_YANGLONG_OPERATE_TYPE.CROSS_YANGLONGOPERATE_TYPE_CONVERT, data.seq)
	else
		if has_enough_stuff then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YangLongSi.MaxExchangeTime)
		else
			TipWGCtrl.Instance:OpenItem({ item_id = data.stuff_id_1 })
		end
	end
end

function LSGWShowItemRender:OnClickStuffIconBtn()
	local data = self:GetData()

	if IsEmptyTable(data) then
		return
	end

	TipWGCtrl.Instance:OpenItem({ item_id = data.stuff_id_1 })
end
