--小鬼即将过期
GuardOverdueView = GuardOverdueView or BaseClass(SafeBaseView)

function GuardOverdueView:__init()
	self:AddViewResource(0, "uis/view/main_ui_prefab", "guard_overdue")
end

function GuardOverdueView:__delete()
end

function GuardOverdueView:LoadCallBack()
	self:CreateCell()
	XUI.AddClickEventListener(self.node_list["btn_look"], BindTool.Bind2(self.ClickLook, self))
end

function GuardOverdueView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function GuardOverdueView:OpenCallBack()
	self:Flush()
	if CountDownManager.Instance:HasCountDown("GuardOverdueView_countdown") then
		CountDownManager.Instance:RemoveCountDown("GuardOverdueView_countdown")
	end
	CountDownManager.Instance:AddCountDown("key_use_countdown", function() end, function() self:Close() end, nil, 5, 1)
end

function GuardOverdueView:ShowIndexCallBack()
end

function GuardOverdueView:OnFlush()
	self:SetTitle(self.title_str)
	self:SetCellData(self.data)
end

function GuardOverdueView:SetTitle(title_str)
	self.node_list["guard_title_txt"].text.text = title_str
end

function GuardOverdueView:CreateCell()
	self.item_cell = ItemCell.New(self.node_list["cell_parent"])
end

function GuardOverdueView:SetCellData(data)
	if self.item_cell then
		self.item_cell:SetData(data)
	end
end

function GuardOverdueView:ClickLook()
	local time = self.data.invalid_time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 and time <= 86400 then
		MainuiWGCtrl.Instance:OpenGuradInvalidTimeView(self.data)
	else

	end

	self:Close()
end

function GuardOverdueView:SetDataOpen(data,title_str)
	-- if data == nil then return end
	self.data = data
	self.title_str = title_str
	-- if self:IsOpen() == false then 
		self:Open()
	-- end
end