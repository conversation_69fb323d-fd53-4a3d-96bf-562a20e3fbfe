ActivityCollectionWordView = ActivityCollectionWordView or BaseClass(SafeBaseView)

--用于保存不同兑换提醒开关信息的表
local CollectionWordFlags = {}
function ActivityCollectionWordView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg(true)

    self:AddViewResource(0, "uis/view/act_collection_words_ui_prefab", "layout_collection_words")
end

function ActivityCollectionWordView:ReleaseCallBack()
    --计时器销毁
    self:CleanActTimer()
    --
    if self.word_reward_list then
        self.word_reward_list:DeleteMe()
        self.word_reward_list = nil
    end

    if self.coll_word_item_list ~= nil then
        for k, v in ipairs(self.coll_word_item_list) do
            v:DeleteMe()
        end
        self.coll_word_item_list = nil
    end

    --销毁监听
    ItemWGData.Instance:UnNotifyDataChangeCallBack(self.collection_change_callback)
end

function ActivityCollectionWordView:OpenCallBack()
    --请求信息
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.ACTIVITY_TYPE_OA_EXCHANGE_SHOP, 1)
end

function ActivityCollectionWordView:LoadCallBack()
    --提示按钮
    self.node_list["btn_prompt"].button:AddClickListener(BindTool.Bind(self.PromptBtnClick, self))
    --退出按钮
    self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))

    --右边兑换栏
    self.word_reward_list = AsyncListView.New(OpenWordRewardItemRender, self.node_list["word_reward_list"])

    --左边集字栏
    self.coll_word_item_list = {}
    for i = 1, 4 do
        self.coll_word_item_list[i] = CollWordItemRender.New(self.node_list["zi_cell_".. i])
        self.coll_word_item_list[i]:SetIndex(i)
    end

    self.node_list["time_text"].text.text = Language.CollectionWord.CollWordActTxt
    --活动时间的显示
    local total_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.ACTIVITY_TYPE_OA_EXCHANGE_SHOP)
    if total_time > 0 then
        self.act_timer = CountDown.Instance:AddCountDown(total_time, 0.5,
        function (elapse_time, act_time)
            local time = math.floor(act_time - elapse_time)
            self:UpdateTimeStr(time)
        end,
        function ()
           self:UpdateTimeStr(0) 
        end
        )
    end

    self.collection_change_callback = BindTool.Bind1(self.CollChangeItemData, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.collection_change_callback)

end

function ActivityCollectionWordView:ShowIndexCallBack()
    self.need_select_new = true
end

--清除计时器
function ActivityCollectionWordView:CleanActTimer()
    if self.act_timer and CountDown.Instance:HasCountDown(self.act_timer) then
        CountDown.Instance:RemoveCountDown(self.act_timer)
        self.act_timer = nil
    end
end

function ActivityCollectionWordView:UpdateTimeStr(total_time)
    if self.node_list["collection_down_time"] then
        self.node_list["collection_down_time"].text.text = TimeUtil.FormatSecondDHM9(total_time)
    end
end

function ActivityCollectionWordView:OnFlush()
    self:CollWordFlushView()
end

--更新显示 List添加数据
function ActivityCollectionWordView:CollWordFlushView()
    --拿到本地的数据
    local flag = PlayerPrefsUtil.GetInt("OpenServerCollWordToggle") or 0

    --转成表的形式
    CollectionWordFlags = bit:d2b(flag)
    --拿到右边列表的配置表的数据
    local left_cfg_data = ActivityCollectionWordWGData.Instance:GetRewardList()
    self.word_reward_list:SetDataList(left_cfg_data)

    --对应物品的id
    local item_id = 0
    --对应物品需要的数量
    local wupin_item_num = 0
     --保存背包物品对应的数量
     local bag_item_num = 0
    --跳可以兑换按钮的操作
   if self.need_select_new then
        local dafult_index = 1
        self.need_select_new = nil
        for k, v in ipairs(left_cfg_data) do
            --拿到协议发过来的对应的已兑换的次数
            local change_num = ActivityCollectionWordWGData.Instance:GetExchangeNum(v.seq)
            --计算剩余兑换次数
            local remain_exchange_num = v.time_limit - change_num
            local can_is = true
            for i = 1, 4 do
                item_id = v["stuff_id_" .. i] or 0
                --拿到对应物品所需数量
                wupin_item_num = v["stuff_num_" .. i] or 0
                 --拿到背包中对应物品的数量
                 bag_item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
                 if remain_exchange_num <= 0 or bag_item_num < wupin_item_num then
                    can_is = false
                end
            end
            if can_is then
                dafult_index = k
                break
            end
        end
        self.word_reward_list:JumpToIndex(dafult_index)
        return
    end


    --给左边的图片给数据, 拿到图片数据
    local coll_word_first_data = ActivityCollectionWordWGData.Instance:GetFirstInfo()
    local coll_word_data = {}
    if coll_word_first_data[1] ~=  nil then
        coll_word_data = coll_word_first_data[1]
    end
    for i = 1, 4 do
        local data = {}
        if coll_word_data["stuff_id_" .. i] then
            data.item_id = coll_word_data["stuff_id_" .. i]
            self.coll_word_item_list[i]:SetData(data)

            local item_num = 0
            --通过物品id拿到背包中物品的数量
            item_num = ItemWGData.Instance:GetItemNumInBagById(coll_word_data["stuff_id_" .. i])
            self.node_list["zi_num_" .. i].text.text = item_num
        end
    end
end

function ActivityCollectionWordView:PromptBtnClick()
    local role_tip = RuleTip.Instance
    role_tip:SetContent(Language.CollectionWord.CollWordActRuleContent, Language.CollectionWord.CollWordActRule)
end

function ActivityCollectionWordView:CollChangeItemData(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
   -- print_error("old_num", old_num, "new_num", new_num, "new_num > old_num", new_num > old_num)
    --当新物品数量大于老物品数量的时候进行刷新
    if new_num and old_num and new_num > old_num then
       -- print_error("进行了刷新")
        --判断对应物品是否增加, 刷新界面
        local wupin_id_cfg = ActivityCollectionWordWGData.Instance:GetFirstInfo()
        local wupin_id_data = {}
        if  wupin_id_cfg[1] ~= nil then
            wupin_id_data = wupin_id_cfg[1]
        end

        for i = 1, 4 do
            if change_item_id == wupin_id_data["stuff_id_" .. i] then
               -- print_error("刷新多少次", i, wupin_id_data["stuff_id_" .. i])
                self:CollWordFlushView()
            end
        end
    end
end






---------------奖励render------------
OpenWordRewardItemRender = OpenWordRewardItemRender or BaseClass(BaseRender)

function OpenWordRewardItemRender:__init()
    --用于判断当前奖励是否可以兑换
    self.can_exchange_reward = false
end

function OpenWordRewardItemRender:__delete()
    if self.word_reward_item then
        self.word_reward_item:DeleteMe()
        self.word_reward_item = nil
    end
end

function OpenWordRewardItemRender:LoadCallBack()
    self.word_reward_item = ItemCell.New(self.node_list["reward_root"])
    --兑换提醒按钮
    self.node_list["gou_btn"].button:AddClickListener(BindTool.Bind(self.OnClickExchangeClick, self))
    
    --兑换按钮
    XUI.AddClickEventListener(self.node_list["btn_lingqu"], BindTool.Bind1(self.OnCanExchangeClick, self))
end

--点击了兑换提醒按钮的回调
function OpenWordRewardItemRender:OnClickExchangeClick()
    local dianji_num = CollectionWordFlags[self.index]
    local is_dianji
    dianji_num =  dianji_num + 1
    if dianji_num % 2 == 0 then
        is_dianji = true
    else
        is_dianji = false
    end

    --根据点击的index设置并保存兑换提醒的当前状态
    CollectionWordFlags[self.index] = is_dianji and 0 or 1
    self.node_list["gou"]:SetActive(is_dianji)
    --通过是否开启和 满足条件进行判断红点是否显示
    self.node_list["img_red"]:SetActive(is_dianji and self.can_exchange_reward)
    --将tab转数字进行保存到本地
    local new_flag = bit:b2d(CollectionWordFlags)
    PlayerPrefsUtil.SetInt("OpenServerCollWordToggle", new_flag)
    -- print_error("PlayerPrefsUtil", PlayerPrefsUtil.GetInt("OpenServerCollWordToggle"))
    --在刷新红点
    RemindManager.Instance:Fire(RemindName.RemindCollectionWord)
end

local WordRewardClickTime = 0
function OpenWordRewardItemRender:OnCanExchangeClick()
    if not self.data then
        return
    end

    local now_time = Status.NowTime
	if now_time < WordRewardClickTime + 0.2 then
        -- print_error("----lan jie----")
		return
	end

	WordRewardClickTime = now_time
    --点击了兑换按钮,发起兑换请求
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.ACTIVITY_TYPE_OA_EXCHANGE_SHOP, 2, self.data.seq)
end

--刷新显示
function OpenWordRewardItemRender:OnFlush()
    if not self.data then
        return
    end

    --根据CollectionWordFlags保存的数据进行判断对应的兑换提醒的显示状态
    self.node_list["gou"]:SetActive(CollectionWordFlags[self.index] ~= 1)

    --拿到协议发过来的对应的已兑换的次数
    local change_num = ActivityCollectionWordWGData.Instance:GetExchangeNum(self.data.seq)
    --计算剩余兑换次数
    local remain_exchange_num = self.data.time_limit - change_num

    local exchange_color = remain_exchange_num > 0  and COLOR3B.GREEN or COLOR3B.RED
    --今日剩余
    self.node_list["lbl_exchange_time"].text.text = string.format(Language.CollectionWord.CollWordExchangeRemain, exchange_color, remain_exchange_num)

    --保存背包物品对应的数量
    local item_num = 0
    --对应物品的id
    local item_id = 0
    --对应物品的名字
    local item_name = ""
    --对应物品需要的数量
    local wupin_item_num = 0
    local can_exchange = true

    local item_desc_list = {}

    for i = 1, 4 do
        --拿到对应物品id
        item_id = self.data["stuff_id_" .. i] or 0
        --拿到对应物品所需数量
        wupin_item_num = self.data["stuff_num_" .. i] or 0
        if wupin_item_num > 0 then  --小于等于0的字不要
        --拿到背包中对应物品的数量
            item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
        --拿到背包物品的名字
            item_name =ItemWGData.Instance:GetItemName(item_id)
            local color = item_num >= wupin_item_num and COLOR3B.GREEN or COLOR3B.RED
            local item_str = string.format("<color=%s>%s*%s</color>", color, item_name, wupin_item_num)
            table.insert(item_desc_list, item_str)
        end

        --判断是否满足兑换条件
        if remain_exchange_num <= 0 or item_num < wupin_item_num then
            can_exchange = false
        end
    end

    local str = table.concat(item_desc_list, ",")

    --设置字体显示
    self.node_list["reward_str"].text.text = string.format(Language.CollectionWord.CollWordJiZiDesc, str)
    self.can_exchange_reward = can_exchange

    --设置兑换按钮
    XUI.SetButtonEnabled(self.node_list["btn_lingqu"], can_exchange)

    --兑换的红点
    local remind_flag = CollectionWordFlags[self.index] ~= 1
    self.node_list["img_red"]:SetActive(remind_flag and can_exchange)

    --物品的显示
    self.word_reward_item:SetData(self.data.item)
end





------------左边Render
CollWordItemRender = CollWordItemRender or BaseClass(BaseRender)

function CollWordItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["click_btn"], BindTool.Bind1(self.ClickBtn, self))
end

function CollWordItemRender:OnFlush()
    --更新数据
    if IsEmptyTable(self.data) then
        return
    end

    --拿到背包中物品的数量
    local item_num = 0
    item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    self.node_list["zi_image_1"]:SetActive(item_num <= 0)
    self.node_list["zi_image_2"]:SetActive(item_num > 0)

end

function CollWordItemRender:ClickBtn()
    if self.data.item_id then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
    end
end