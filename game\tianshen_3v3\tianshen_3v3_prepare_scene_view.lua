local MatchingType = {
    Single 	= 1,    --单人匹配
    Team 	= 2,    --战队匹配
}
-- 天神3v3准备场景面板
TianShen3v3PrepareSceneView = TianShen3v3PrepareSceneView or BaseClass(SafeBaseView)
function TianShen3v3PrepareSceneView:__init()
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_prepare")
	self.view_cache_time = 1
    self.view_layer = UiLayer.MainUI
    self.is_safe_area_adapter = true
    self.active_close = false
end

function TianShen3v3PrepareSceneView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
	self.is_out_fb = nil

	self:CancelMatchingTimer()

	if self.arrow_tips_timer then
		GlobalTimerQuest:CancelQuest(self.arrow_tips_timer)
		self.arrow_tips_timer = nil
	end
	RemindManager.Instance:UnBind(self.join_reward_remind_change)
end

function TianShen3v3PrepareSceneView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["change_tianshen_btn"], BindTool.Bind(self.OnClickChangeTianShen, self))
	XUI.AddClickEventListener(self.node_list["personal_match"], BindTool.Bind(self.OnClickPersonalMatch, self))
	XUI.AddClickEventListener(self.node_list["team_match"], BindTool.Bind(self.OnClickTeamMatch, self))
	XUI.AddClickEventListener(self.node_list["stage_icon"], BindTool.Bind(self.OnClickStageIcon, self)) 			-- 段位图标按钮
	
	self.match_btn_clicked = false 									-- 是否按过匹配按钮
	self.arrow_start_y = self.node_list["arrow_tips"].transform.localPosition.y
	self.arrow_tips_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CheckShowArrowTips, self), 25)
	self.node_list["arrow_tips"]:SetActive(false)
	-- self.team_info_change_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.OnTeamChange, self))         -- 队伍

	self.join_reward_remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
	RemindManager.Instance:Bind(self.join_reward_remind_change, RemindName.TianShen3v3JoinRewardRemind)
end

function TianShen3v3PrepareSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	self.obj = self.node_list["left_panel"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform)
	self.obj.transform.anchoredPosition = Vector2(150, 144)
	self.obj.transform.localScale = Vector3.one
	mainui_ctrl:SetTaskPanel(false)
	
	if self.is_out_fb then
        self.obj:SetActive(false)
    end
    self.is_out_fb = nil
end

function TianShen3v3PrepareSceneView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function TianShen3v3PrepareSceneView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj.transform:SetParent(self.root_node_transform.transform)
        self.obj:SetActive(false)
    end

	-- MainuiWGCtrl.Instance:SetTaskAndTeamCallBack(nil, nil)
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text2)
end

function TianShen3v3PrepareSceneView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	self:Flush()
end

function TianShen3v3PrepareSceneView:OnFlush()
	self:FlushAllView()
end

function TianShen3v3PrepareSceneView:FlushAllView()
	self:FlushMatchBtn()
	self:FlushLeftPanel()
end

function TianShen3v3PrepareSceneView:FlushLeftPanel()
	-- 剩余可获得积分的场次
	self.node_list["can_get_score_times"].text.text = string.format(Language.TianShen3v3.CanFetchDesc, TianShen3v3WGData.Instance:GetSurplusCanGetScoreTimes())
	-- 进阶所需积分
	local _, next_grade_score = TianShen3v3WGData.Instance:GetAdvanceNeedScore()
	self.node_list["season_score"].text.text = string.format(Language.TianShen3v3.AdvanceNeedScore2, TianShen3v3WGData.Instance:GetSeasonScore(), next_grade_score)
	-- 胜场
	self.node_list["win_times"].text.text = string.format(Language.TianShen3v3.WinTimesStr, TianShen3v3WGData.Instance:GetWinTimes())

	local grade_cfg, next_grade_cfg = TianShen3v3WGData.Instance:GetGradeCfgByScore(TianShen3v3WGData.Instance:GetSeasonScore())
	if grade_cfg then
		-- 段位名称
		self.node_list["stage_name"].text.text = grade_cfg.grade_name
		ChangeToQualityText(self.node_list["stage_name"].text, RankGradeEnum[grade_cfg.grade])
		-- 星数
		for i = 1, 5 do
			self.node_list["star_" .. i]:SetActive(i <= grade_cfg.star)
		end
		-- 进度条
		local max_score = next_grade_cfg.score - grade_cfg.score
		local cur = TianShen3v3WGData.Instance:GetSeasonScore() - grade_cfg.score
		self.node_list["stage_progress"].image.fillAmount = cur / max_score
	else
		print_error("没取到段位配置，请检查！， score:", TianShen3v3WGData.Instance:GetSeasonScore())
	end
	-- 段位图标
	self.node_list["stage_icon"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("duanwei_icon" .. grade_cfg.grade))
	self.node_list["stage_icon_2"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("duanwei_icon" .. grade_cfg.grade))
	-- 红点
	self.node_list["btn_remind"]:SetActive(RemindManager.Instance:GetRemind(RemindName.TianShen3v3JoinRewardRemind) > 0)

end

-- 刷新匹配按钮
function TianShen3v3PrepareSceneView:FlushMatchBtn()
	local personal_matching = TianShen3v3WGData.Instance:GetIsMatching() and not self:HasTeam()
	self.node_list["personal_match_text"]:SetActive(not personal_matching)
	self.node_list["personal_matching_time"]:SetActive(personal_matching)
	self.node_list["personal_cancel_match_text"]:SetActive(personal_matching)

	local team_matching = TianShen3v3WGData.Instance:GetIsMatching() and self:HasTeam()
	self.node_list["team_match_text"]:SetActive(not team_matching)
	self.node_list["team_matching_time"]:SetActive(team_matching)
	self.node_list["team_cancel_match_text"]:SetActive(team_matching)
	self:FlushMatchTime()
end

-- 刷新匹配时间
function TianShen3v3PrepareSceneView:FlushMatchTime()
	self:CancelMatchingTimer()
	if TianShen3v3WGData.Instance:GetMatchDuration() >= 0 then
		local flush_match_time = function()
			local duration = TianShen3v3WGData.Instance:GetMatchDuration()
			if duration >= 0 then
				self.node_list["personal_matching_time"].text.text = string.format(Language.TianShen3v3.Matching, duration)
				self.node_list["team_matching_time"].text.text = string.format(Language.TianShen3v3.Matching, duration)
			else
				self:CancelMatchingTimer()
			end
		end
		flush_match_time()
		self.matching_timer = GlobalTimerQuest:AddRunQuest(function() 
			flush_match_time()
		end, 1)
	end
end

-- 取消匹配时间计时器
function TianShen3v3PrepareSceneView:CancelMatchingTimer()
	if self.matching_timer then
		GlobalTimerQuest:CancelQuest(self.matching_timer)
		self.matching_timer = nil
	end
end

-- 点击切换天神按钮
function TianShen3v3PrepareSceneView:OnClickChangeTianShen()
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3View)
end

-- 点击单人匹配
function TianShen3v3PrepareSceneView:OnClickPersonalMatch()
	self.match_btn_clicked = true
	self.node_list["arrow_tips"]:SetActive(false)
	if TianShen3v3WGData.Instance:GetIsMatching() then
		TianShen3v3WGCtrl.Instance:SendCancelMatch()
	else
		if self:HasTeam() then
			TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen3v3.TeamTips)
			return
		end
		TianShen3v3WGCtrl.Instance:SendMatch()
	end
end

-- 点击组队匹配
function TianShen3v3PrepareSceneView:OnClickTeamMatch()
	self.match_btn_clicked = true
	self.node_list["arrow_tips"]:SetActive(false)
	if TianShen3v3WGData.Instance:GetIsMatching() then
		TianShen3v3WGCtrl.Instance:SendCancelMatch()
	else
		-- 是否在队伍中
		if self:HasTeam() then
			TianShen3v3WGCtrl.Instance:SendMatch()
		else
			ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_my_team)
			local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
			NewTeamWGCtrl.Instance:SendCreateTeam(0, 0, min_level, max_level)
		end
	end
end

--取消匹配提示框
function TianShen3v3PrepareSceneView:OpenCancelMatchAlert()
	TipWGCtrl.Instance:OpenAlertTips(Language.TianShen3v3.CancelMatchTips, function()
		TianShen3v3WGCtrl.Instance:SendCancelMatch()
	end)
end

-- 红点回调
function TianShen3v3PrepareSceneView:RemindChangeCallBack(remind_name, num)
	self.node_list["btn_remind"]:SetActive(num > 0)
end

-- 点击段位图标
function TianShen3v3PrepareSceneView:OnClickStageIcon()
	ViewManager.Instance:Open(GuideModuleName.TianShen3v3RewardView)
end

-- 是否有队伍
function TianShen3v3PrepareSceneView:HasTeam()
	return SocietyWGData.Instance:GetIsInTeam() == 1
end

-- 检查是否需要显示箭头提醒
function TianShen3v3PrepareSceneView:CheckShowArrowTips()
	if not self.match_btn_clicked and self.node_list["arrow_tips"] and not TianShen3v3WGData.Instance:GetIsMatching() then
		self.node_list["arrow_tips"]:SetActive(true)
		self.node_list["arrow_tips"].transform:DOLocalMoveY(self.arrow_start_y + 30, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end


-- --一键组队
-- function TianShen3v3PrepareSceneView:AutoTeam()
-- 	if SocietyWGData.Instance:GetIsInTeam() == 0 then
-- 		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
-- 		NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
-- 	end
-- 	for i, v in ipairs(self.wait_to_invite_start_cross_list) do
-- 		CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteReq,EnumInviteStartCrossReason.Zhandui3V3Match, v.uid)
-- 	end
-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.SendCreateTeamInvite)
-- end

-- function TianShen3v3PrepareSceneView:ClickAddMember()
-- 	ZhanDuiWGCtrl.Instance:OpenInviteView()
-- end

-- function TianShen3v3PrepareSceneView:OnTeamChange()
-- 	if not self:IsOpen() then return end
-- end

-- function TianShen3v3PrepareSceneView:ExitTeamAndMatch()
-- 	SocietyWGCtrl.Instance:SendExitTeam()
-- 	--组队状态保存在原服，离队后会同步状态给跨服有一个延迟
-- 	GlobalTimerQuest:AddDelayTimer(function()
-- 		KF3V3WGCtrl.Instance:ReqStartMatch()
-- 	end, 1)
-- end