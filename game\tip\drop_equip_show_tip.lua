
--掉落极品展示tip 和 成就弹窗（AchievementTipiew）互斥
DropEquipShowTips = DropEquipShowTips or BaseClass(SafeBaseView)

--标题图片资源
local TITLE_IMG_NAME = {
	[1] = "jp_di3", 	--翅膀标题
	[2] = "jp_di4", 	--法宝标题
	[3] = "jp_di5", 	--鲲标题
	[4] = "jp_di7", 	--装备标题
}

function DropEquipShowTips:__init()
	self.view_name = "DropEquipShowTips"
	self.view_layer = UiLayer.PopWhite
	self:AddViewResource(0, "uis/view/drop_equip_show_ui_prefab", "layout_drop_equip_show_tip")
	self.open_width = 356
	self.close_width = 30
	self.show_animator = false
end

function DropEquipShowTips:__delete()
	self.show_data = nil
end

function DropEquipShowTips:ReleaseCallBack()
	if self.close_deley then
		GlobalTimerQuest:CancelQuest(self.close_deley)
		self.close_deley = nil
	end
	if self.show_item then
		self.show_item:DeleteMe()
		self.show_item = nil
	end
end

function DropEquipShowTips:CloseCallBack()
	self.show_animator = false
	TipWGCtrl.Instance:ShowNextSpecialTips()
	--AchievementWGCtrl.Instance:ContinueShowAchieveTip()
end

function DropEquipShowTips:LoadCallBack()

end

function DropEquipShowTips:ShowAnimator()
	-- local parent_tween = self.node_list["parent"].transform:DOSizeDelta(Vector2(self.open_width, 125), 0.4)
	-- parent_tween:SetEase(DG.Tweening.Ease.OutCubic)
	-- parent_tween:OnComplete(function()
	-- 	if self.node_list.button then
    --     	self.node_list.button:SetActive(false)
	-- 	end
	-- 	table.remove(self.tip_data, 1)
	-- 	self:ShowNextAchieventHint()
    -- end)

	if self.node_list.button then
		self.node_list.button:SetActive(false)
	end
	table.remove(self.tip_data, 1)
	self:ShowNextAchieventHint()
end

function DropEquipShowTips:ShowIndexCallBack()

end

function DropEquipShowTips:SetShowData(data)
	self.show_data = data
	self.need_show = false
	self:Flush()
end

function DropEquipShowTips:OnFlush(param_t, index)
	self:SetShowTitleName()
	self.node_list["parent"].canvas_group.alpha = 1
	if self.drop_close_tween then
		self.drop_close_tween:Kill()
	end

	if self.parent_tween then
		self.parent_tween:Kill()

	end

	if not self.show_animator then
		self.show_animator = true
		self:ShowAnimator()
	end

	-- if self.close_deley then
	-- 	GlobalTimerQuest:CancelQuest(self.close_deley)
	-- 	self.close_deley = nil
	-- end
	if not self.close_deley then
		self.close_deley = GlobalTimerQuest:AddDelayTimer(function ()
			GlobalTimerQuest:CancelQuest(self.close_deley)
			self.close_deley = nil
			self:PlayCloseTween()
		end,2.7)
	end

	if nil == self.show_item then
		self.show_item = ItemCell.New(self.node_list.item_pos)
	end

	if self.show_data and self.show_data.save_equip_id then
		local cfg = ItemWGData.Instance:GetItemConfig(self.show_data.save_equip_id)
		local bag_cfg = ItemWGData.Instance:GetGridData(self.show_data.save_equip_index)
		if bag_cfg and bag_cfg.item_id == self.show_data.save_equip_id then
			self.show_item:SetData(bag_cfg)
		else
			local data = {}
			data.item_id = self.show_data.save_equip_id
			data.num = 1
			self.show_item:SetData(data)
		end
		self.node_list.item_name.text.text = cfg.name
	end
end

function DropEquipShowTips:PlayCloseTween()
	-- self.parent_tween = self.node_list["parent"].transform:DOSizeDelta(Vector2(self.close_width, 125), 0.3)
	-- self.parent_tween:SetEase(DG.Tweening.Ease.OutCubic)
	-- self.parent_tween:OnComplete(function ()
	-- 		self.parent_tween:Kill()
	-- 		self.parent_tween = nil
	-- 	end)

	self.drop_close_tween = self.node_list["parent"].canvas_group:DoAlpha(1,0,0.3)
	self.drop_close_tween:OnComplete(function ()
			self.drop_close_tween:Kill()
			self.drop_close_tween = nil
			if self.need_show then
				
			else
				self:Close()
			end
		end)

end

function DropEquipShowTips:SetShowTitleName()
	if self.node_list["img_desc_title"] then
		self.node_list["img_desc_title"].text.text = Language.DropEquipShowTips.DropEquipDesc[self.show_data and self.show_data.show_type or 1]
		
		-- local asset, bundle = ResPath.GetBestEquipDropImg(TITLE_IMG_NAME[self.show_data and self.show_data.show_type or 1])
		-- self.node_list["img_desc_title"].image:LoadSprite(asset, bundle)
		-- self.node_list["img_desc_title"].image:SetNativeSize()
	end
end