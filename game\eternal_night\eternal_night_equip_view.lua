EternalNightEquipView = EternalNightEquipView or BaseClass(SafeBaseView)
function EternalNightEquipView:__init()
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_equip")
	self.is_auto_close = false
	self.view_layer = UiLayer.MainUIHigh
end

function EternalNightEquipView:__delete()
end

function EternalNightEquipView:LoadCallBack()
	local equip_list_obj = self.node_list["equip_list"].transform
	self.equip_list = {}
	for i=0,ETERNAL_NIGHT_PARAM.MAX_EQUIP_PART_NUM - 1 do
		if i >= 10 then
			self.equip_list[i] = ItemCell.New(equip_list_obj:Find("equip_cellbg/equip_cell"..i).gameObject)
		else
			self.equip_list[i] = ItemCell.New(equip_list_obj:Find("equip_cell" .. i .. "/item_cell").gameObject)
		end
		self.equip_list[i]:SetItemTipFrom(ItemTip.FROM_ETERNAL_NIGHT)
		self.equip_list[i]:SetCellBgEnabled(false)
	end

	if self.load_callback then
		self.load_callback()
		self.load_callback = nil
	end

	XUI.AddClickEventListener(self.node_list["block_btn"], BindTool.Bind(self.ClickBlockBtn,self))
end

function EternalNightEquipView:CloseCallBack()
	self.is_auto_close = false
end

function EternalNightEquipView:SetLoadCallBack(callback)
	self.load_callback = callback
end

function EternalNightEquipView:ReleaseCallBack()
	if self.equip_list then
		for k,v in pairs(self.equip_list) do
			v:DeleteMe()
		end
		self.equip_list = {}
	end
	if CountDownManager.Instance:HasCountDown("eternal_night_equip_close") then
		CountDownManager.Instance:RemoveCountDown("eternal_night_equip_close")
	end
	self.is_auto_close = false
	self.load_callback = nil
end

function EternalNightEquipView:OnFlush()
	--self.node_list["btn_close_window"]:SetActive(not self.is_auto_close)
	local self_info = EternalNightWGData.Instance:GetOldSelfInfo()
	local equip_list_info = self_info.equip_list
	if equip_list_info then
		for k,v in pairs(self.equip_list) do
			if equip_list_info[k] > 0 then
				local data = {item_id = equip_list_info[k]}
				v:SetData(data)
			else
				-- local id = k
				-- if k >= 10 then
				-- 	id = 50 + k
				-- end

				v:SetData(nil)
				if k < 10 then
					v:SetItemIcon(ResPath.GetCommonIcon("a3_zb_" .. k))
				end
			end
		end
	end
	local equip_capability = self_info.equip_attr_info and self_info.equip_attr_info.equip_capability or 0
	self.node_list["cap_text"].text.text = string.format(Language.EternalNight.CapText,equip_capability)
end

function EternalNightEquipView:SetAutoClose()
	self.is_auto_close = true
	if self:IsOpen() then
		if CountDownManager.Instance:HasCountDown("eternal_night_equip_close") then
			CountDownManager.Instance:RemoveCountDown("eternal_night_equip_close")
		end
		CountDownManager.Instance:AddCountDown("eternal_night_equip_close", BindTool.Bind(self.UpdataTime, self), BindTool.Bind(self.TimeCompleteCallBack, self), nil, 2, 1)
	end
end

function EternalNightEquipView:UpdataTime(elapse_time, total_time)

end

function EternalNightEquipView:TimeCompleteCallBack()
	self:Close()
end

function EternalNightEquipView:GetEquipBtnNode(item_id)
	local cfg = EternalNightWGData.Instance:GetEquipCfgById(item_id)
	if cfg then
		if self:IsOpen() and self:IsLoaded() then
			local equip_list_obj = self.node_list["equip_list"].transform
			local cell = self.equip_list[cfg.equip_part]
			local node_obj = cell and cell.root_node
			return node_obj
		end
	end
end

function EternalNightEquipView:ClickBlockBtn()
	self:Close()
end