-- 猎鲲界面初始化
function GuildView:InitLieKunView()
	self.cell_list = {}
	local select_liekun_index = self:GetSelectLieKunIndex()
	select_liekun_index = nil

	for i = 0, 1 do
		if not self.cell_list[i] then
			local ph = self.node_list["ph_reward_cell_" .. i]
			local cell = ItemCell.New(ph)
			self.cell_list[i] = cell
		end
	end

	-- 鲲的图片
	for i=0,4 do
		XUI.AddClickEventListener(self.node_list['kun_' .. i],BindTool.Bind(self.OnClickLieKunBtnHandler, self,i))
	end

	XUI.AddClickEventListener(self.node_list.btn_liekun_tip, BindTool.Bind1(self.OnClickLieKunViewTips, self))
	XUI.AddClickEventListener(self.node_list.btn_goto_liekun, BindTool.Bind1(self.OnClickGoToLie<PERSON>un, self))
	XUI.AddClickEventListener(self.node_list.layout_monster_list, BindTool.Bind1(self.OnClickMonstrList, self))
end

-- 点击区域
function GuildView:OnClickLieKunBtnHandler(index)
	local select_liekun_index = self:GetSelectLieKunIndex()
	if select_liekun_index == index then return end
	for i = 0,4 do
		if i == index then
			self.node_list['high_' .. i]:SetActive(true)
		else
			self.node_list['high_' .. i]:SetActive(false)
		end
	end
	select_liekun_index = index
end

function GuildView:DeleteLieKunView()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end

	if self.delay_modfily_status then
		GlobalTimerQuest:CancelQuest(self.delay_modfily_status)
		self.delay_modfily_status = nil
	end
end

function GuildView:OnFlushLieKunView()
	local lioekun_other = ConfigManager.Instance:GetAutoConfig("cross_liekun_auto").other[1]
	if lioekun_other == nil then return end
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_LIEKUN)
	local is_open = activity_info and (activity_info.status == ACTIVITY_STATUS.STANDY or activity_info.status == ACTIVITY_STATUS.OPEN)
	XUI.SetButtonEnabled(self.node_list.btn_goto_liekun, is_open)
	self.node_list.btn_goto_liekun_red:SetActive(GuildWGData.Instance:IsShowGuildLieKunRedPoint() == 1)
	local liekunfb_player_info = GuildWGData.Instance:GetCrossLieKunFBPlayerInfo()
	local liekun_status
	if activity_info then
		local end_time = activity_info.next_time
		local close_time = end_time - lioekun_other.total_time + lioekun_other.enter_time_limit_s
		if TimeWGCtrl.Instance:GetServerTime() >= close_time then
			if activity_info.status == ACTIVITY_STATUS.STANDY then
				liekun_status = string.format(Language.Guild.LieKunStatus,Language.Guild.LieKunStatus_3)
			else
				liekun_status = string.format(Language.Guild.LieKunStatus,Language.Guild.LieKunStatus_2)
			end
		else
			if self.delay_modfily_status then
				GlobalTimerQuest:CancelQuest(self.delay_modfily_status)
				self.delay_modfily_status = nil
			end
			liekun_status = string.format(Language.Guild.LieKunStatus,Language.Guild.LieKunStatus_1)
			self.delay_modfily_status = GlobalTimerQuest:AddDelayTimer(function ()
				liekun_status = string.format(Language.Guild.LieKunStatus,Language.Guild.LieKunStatus_2)
				for i=0, GameEnum.LIEKUN_ZONE_TYPE_COUNT - 1 do
					local str = string.format(Language.Guild.PeopleNum, liekunfb_player_info.role_num[i + 1])
					self.node_list["lbl_people_num_" .. i].text.text = str
					str = string.format(Language.Guild.GuildPeopleNum, liekunfb_player_info.guild_role_num[i + 1])
					self.node_list["lbl_guild_people_num_" .. i].text.text = str
					if i ~= 0 then
			 			self.node_list["lbl_act_state_" .. i].text.text = liekun_status
			 		end
				end
				self.node_list.btn_goto_liekun_red:SetActive(GuildWGData.Instance:IsShowGuildLieKunRedPoint() == 1)
			end,close_time - TimeWGCtrl.Instance:GetServerTime())
		end
	else
		liekun_status = string.format(Language.Guild.LieKunStatus,Language.Guild.LieKunStatus_2)
	end
	for i=0, GameEnum.LIEKUN_ZONE_TYPE_COUNT - 1 do
		local str = string.format(Language.Guild.PeopleNum, liekunfb_player_info.role_num[i + 1])
		self.node_list["lbl_people_num_" .. i].text.text = str
		str = string.format(Language.Guild.GuildPeopleNum, liekunfb_player_info.guild_role_num[i + 1])
		self.node_list["lbl_guild_people_num_" .. i].text.text = str
		if i ~= 0 then
 			self.node_list["lbl_act_state_" .. i].text.text = liekun_status
 		end
	end

	if not self:GetSelectLieKunIndex() then
		local index = GuildWGData.Instance:GetCrossLieKunFBBestSelect() 
		self:OnClickLieKunBtnHandler(index)
	end

	-- self.node_list.lbl_limit:SetActive(liekunfb_player_info.is_enter_main_zone == 0)



	for k,v in pairs(self.cell_list) do
		if not lioekun_other.reward_item[k] then
			v:SetVisible(false)
		else
			v:SetVisible(true)
		end
		v:SetData(lioekun_other.reward_item[k])
	end

    self.node_list.des.text.text =  Language.Guild.LieKunDes
    self.node_list.time_des.text.text =  Language.Guild.LieKunTimeDes

end

function GuildView:OnClickLieKunViewTips()
	RuleTip.Instance:SetContent(Language.Guild.LieKunTips, Language.Guild.LieKunTitle)
end

function GuildView:OnClickGoToLieKun()
	local select_liekun_index = self:GetSelectLieKunIndex()
	if IS_ON_CROSSSERVER and select_liekun_index then
		local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
		local scene_id = GuildWGData.Instance:GetLieKunSceneId(select_liekun_index)
		GuildWGCtrl.Instance:SendCrossLieKunFBReq(CROSS_LIEKUNFB_REQ_TYPE.LIEKUNFB_TYPE_GOTO, scene_id, scene_key)
		self:Close()
		return
	end
	if select_liekun_index then
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_LIEKUN, select_liekun_index)
		self:Close()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ChooseZone)
	end
end

function GuildView:OnClickMonstrList()
	GuildWGCtrl.Instance:OpenLkBossListView()
end