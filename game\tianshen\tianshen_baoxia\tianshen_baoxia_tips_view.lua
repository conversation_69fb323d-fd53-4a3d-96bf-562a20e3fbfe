TianShenBaoXiaTipsView = TianShenBaoXiaTipsView or BaseClass(SafeBaseView)

function TianShenBaoXiaTipsView:__init()
	self:SetMaskBg(true, true)
	local bundle_name = "uis/view/tianshen_prefab"
	self:AddViewResource(0, bundle_name, "layout_ts_baoxia_tips")

	self.view_name = "TianShenBaoXiaTipsView"
end

function TianShenBaoXiaTipsView:__delete()

end

function TianShenBaoXiaTipsView:ReleaseCallBack()
	UITween.KillMoveLoop(self.node_list["bx_model_pos"].gameObject)

	if self.baoxia_model then
		self.baoxia_model:DeleteMe()
		self.baoxia_model = nil
	end

	if self.shenshi_item_list then
		self.shenshi_item_list:DeleteMe()
		self.shenshi_item_list = nil
	end
end

function TianShenBaoXiaTipsView:LoadCallBack()
	self.cache_model_res_name = ""

	self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))

	self.shenshi_item_list = AsyncListView.New(TianShenShenShiItemListRender, self.node_list["shenshi_item_list"])

	UITween.MoveLoop(self.node_list["bx_model_pos"].gameObject, u3dpool.vec3(0, 60, 0), u3dpool.vec3(0, 75, 0), 1)
end

function TianShenBaoXiaTipsView:SetBoxTipShowType(show_type)
	self.box_tip_show_type = show_type or BX_Box_Tip_Show_Type.Next
end

function TianShenBaoXiaTipsView:OnFlush(param_t)
	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	if not cur_cfg then return end
	local next_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade + 1)
	--满级,或者 显示类型为当前
	if not next_cfg or self.box_tip_show_type == BX_Box_Tip_Show_Type.Cur then
		next_cfg = cur_cfg
	end

	self.node_list["text_level_des"].text.text = next_cfg.name

	local mat_data_list = self:SortShowData(next_cfg.reward_item)
	self.shenshi_item_list:SetDataList(mat_data_list)
	self.shenshi_item_list:JumpToTop()

	local res_name = nil
	if type(next_cfg.show_box_modelid) == "string" then
		res_name = next_cfg.show_box_modelid
	end
	self:SetBoxModel(res_name)--模型加载名字配置
end

function TianShenBaoXiaTipsView:SetBoxModel(res_name)
	if not self.baoxia_model then
		self.baoxia_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["bx_model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = false,
		}
		
		self.baoxia_model:SetRenderTexUI3DModel(display_data)
		-- self.baoxia_model:SetUI3DModel(self.node_list["bx_model_pos"].transform, self.node_list["model_drag"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	local model_res_name = res_name or "3_2_zi"--模型加载名字配置
	if self.cache_model_res_name ~= model_res_name then
		self.cache_model_res_name = model_res_name
		-- print_error("FFFFF====== 加载模型")
		local m_bundle, m_asset = ResPath.GetOtherUIModelByName(model_res_name)
		self.baoxia_model:SetMainAsset(m_bundle, m_asset)
	end
end

function TianShenBaoXiaTipsView:SortShowData(data_list)

	local temp_t_to_one = {}--索引转成1开始
	--排序
	local shenshi_cfg = nil
	for k, v in pairs(data_list) do
		if v.item_id then
			shenshi_cfg = TianShenWGData.Instance:GetTianshenItemInfo(v.item_id)
			if shenshi_cfg then
				v.sort_index = (shenshi_cfg.color_limit * 100) - shenshi_cfg.part
				temp_t_to_one[#temp_t_to_one + 1] = v
			end
		end
	end
	data_list = temp_t_to_one
	SortTools.SortDesc(data_list, "sort_index")

	--分组
	local a, b = 1, 1
	local num = 0
	local temp_data = {}
	for i, v in ipairs(data_list) do
		num = num + 1
		a = math.ceil(num / 4)
		b = num % 4
		b = b == 0 and 4 or b
		if not temp_data[a] then
			temp_data[a] = {}
		end
		temp_data[a][b] = v
	end
	return temp_data
end

--------------------------------------------------------------------------------
TianShenShenShiItemListRender = TianShenShenShiItemListRender or BaseClass(BaseRender)
function TianShenShenShiItemListRender:__init()
	self.mat_item_list = {}
	for i = 1, 4 do
		self.mat_item_list[i] = {}
		self.mat_item_list[i].item = ItemCell.New(self.node_list["cell" .. i])
		self.mat_item_list[i].item_go = self.node_list["cell" .. i]
	end
end

function TianShenShenShiItemListRender:__delete()
	if not IsEmptyTable(self.mat_item_list) then
		for i = 1, #self.mat_item_list do
			if  not IsEmptyTable(self.mat_item_list[i]) then
				self.mat_item_list[i].item:DeleteMe()
				self.mat_item_list[i].item_go = nil
				self.mat_item_list[i] = nil
			end
		end
	end
end

function TianShenShenShiItemListRender:OnFlush()
	for i = 1, #self.mat_item_list do
		self.mat_item_list[i].item_go:SetActive(false)
	end
	if not self.data then
		return
	end
	
	for i = 1, #self.data do
		self.mat_item_list[i].item:SetData({item_id = self.data[i].item_id, is_bind = 1})
		self.mat_item_list[i].item:SetRightBottomText(self.data[i].num)
		self.mat_item_list[i].item_go:SetActive(true)
	end
end