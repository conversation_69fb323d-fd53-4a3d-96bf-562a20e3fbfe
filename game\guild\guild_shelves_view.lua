GuildShelvesView = GuildShelvesView or BaseClass(SafeBaseView)

function GuildShelvesView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 574)})
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_shelves")

    self:ResetSelectMap()
end

function GuildShelvesView:ReleaseCallBack()
    if self.rent_grid then
        self.rent_grid:DeleteMe()
        self.rent_grid = nil
    end

    if self.tab_list then
        self.tab_list:DeleteMe()
        self.tab_list = nil
    end

    self:ResetSelectMap()
end

function GuildShelvesView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.rent_btn, BindTool.Bind(self.OnRentBtnClick, self))
    XUI.AddClickEventListener(self.node_list.box_btn, BindTool.Bind(self.OnRuleBtnClick, self))

    if not self.tab_list then
        self.tab_list = AsyncListView.New(GuildShelvesTopBarRender, self.node_list.top_tab_list) 
        self.tab_list:SetSelectCallBack(BindTool.Bind1(self.OnTopBarSelect, self))
        self.tab_list:SetStartZeroIndex(false)
        self.node_list.top_tab_list.scroll_rect.enabled = false
    end

    if self.rent_grid == nil then
        local bundle = "uis/view/guild_ui_prefab"
        local asset = "guild_rent_grid_cell"
		self.rent_grid = GuildShelvesGrid.New()
		self.rent_grid:CreateCells({
			col = 8, 
			change_cells_num = 1, 
			list_view = self.node_list["rent_grid"],
			assetBundle = bundle, 
			assetName = asset, 
			itemRender = GuildRentShelvesItemRender})
		self.rent_grid:SetStartZeroIndex(false)
        self.rent_grid:SetIsMultiSelect(true)
        self.rent_grid:SetSelectCallBack(BindTool.Bind1(self.OnClickAssign, self))
	end

    self.node_list.title_view_name.text.text = Language.Assignment.ShelvesTitle
end

function GuildShelvesView:OnFlush()
    self:ResetSelectMap()
    self:UpdateGridDataAndSelect()
    self.tab_list:SetDataList(Language.Assignment.ShelvesTopStrs)
end

function GuildShelvesView:OnRuleBtnClick()
    RuleTip.Instance:SetContent(Language.Assignment.ShelvesTipsStr, Language.Assignment.ShelvesTipsTitle)
end

function GuildShelvesView:OnClickAssign(cell)
    local data = cell:GetData()
    if IsEmptyTable(data) then
        return
    end

    local is_select = true
    local has_select_num = self.map_length or 0
    local item_id = data.cfg.item_id
    if self.is_select_map[item_id] then
        self:SetSelectMap(item_id, nil)
        is_select = false
    end


    local guild_all_fashion = AssignmentWGData.Instance:GetAllRentFashionDataList()
    local guild_max = AssignmentWGData.Instance:GetGuildMaxBorrowNum()
    if guild_max <= (#guild_all_fashion + has_select_num) and is_select then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.GuildMax)
        is_select = false
    end

    local self_max = AssignmentWGData.Instance:GetMaxBorrowNum()
    local my_data_list = AssignmentWGData.Instance:GetMyRentList()
    local my_is_rent_list = AssignmentWGData.Instance:GetMyIsRentList()
    if self_max <= (#my_data_list + #my_is_rent_list + has_select_num) and is_select then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Assignment.SelfMax)
        is_select = false
    end
    


    if is_select then
        self:SetSelectMap(item_id, data)
        self.rent_grid:SetSelectCellIndexByNoCancel(cell.index)
    else
        self.rent_grid:SetCancelSelectCellIndex(cell.index)
    end
    cell:OnSelectChange(is_select)
end

function GuildShelvesView:SetSelectMap(item_id, data)
    local power = data == nil and -1 or 1
    self.map_length = self.map_length + (1 * power)
    self.is_select_map[item_id] = data 
end

function GuildShelvesView:ResetSelectMap()
    self.is_select_map = {}
    self.map_length = 0
end

function GuildShelvesView:OnRentBtnClick()
    local data_list = self.is_select_map
    if IsEmptyTable(data_list) then
        return
    end

    local cb_fun = function()
        for k, v in pairs(data_list) do
            local cfg =  v.cfg
            if not IsEmptyTable(cfg) then
                AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.RENT_FASHION, cfg.type, cfg.seq)
            end
        end
        
        AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.INFO)
        AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.GUILD_RECORD)
        AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.SELF_RENT)
        
        self:Close()
    end
    
    AssignmentWGCtrl.Instance:OpenRentlert(cb_fun, Language.Assignment.RentCheckStr, false)
end

function GuildShelvesView:OnTopBarSelect(item)
    if IsEmptyTable(item) then 
        return 
    end

    self.select_index = item.index
    self:UpdateGridDataAndSelect()
end

function GuildShelvesView:UpdateGridDataAndSelect()
    if self.select_index then
        self.rent_grid:CancleAllSelectCell()
        local data_list = AssignmentWGData.Instance:GetMyAssignFashionDataList(self.select_index - 1)
        self.rent_grid:SetDataList(data_list)

        for _, select_fashion in pairs(self.is_select_map) do
            local select_index = -1
            for k, v in pairs(data_list) do
                if AssignmentWGData.Instance:GetIsSameFashionData(v, select_fashion) then
                    select_index = k
                    break
                end
            end
    
            if select_index > 0 then
                self.rent_grid:SetSelectCellIndexByNoCancel(select_index)
            end
        end
    end
end

-------------------------------------------GuildShelvesTopBarRender  start----------------------------------
GuildShelvesTopBarRender = GuildShelvesTopBarRender or BaseClass(BaseGridRender)

function GuildShelvesTopBarRender:OnSelectChange(is_select)
    self.node_list.hl_image:CustomSetActive(is_select)
end

function GuildShelvesTopBarRender:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.hl_text.text.text = self.data
	self.node_list.normal_text.text.text = self.data
end

-------------------------------------------GuildShelvesTopBarRender  end----------------------------------

-------------------------------------------GuildRentShelvesItemRender  start----------------------------------
GuildRentShelvesItemRender = GuildRentShelvesItemRender or BaseClass(BaseGridRender)

function GuildRentShelvesItemRender:OnSelectChange(is_select)
    self.node_list.hl_img:CustomSetActive(is_select)
end

function GuildRentShelvesItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
        self.item_cell = nil
	end
end

function GuildRentShelvesItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_root)
    self.item_cell:SetIsShowTips(false)
    self.item_cell:IsCanDJ(false)
    self.item_cell:SetUseButton(false)
end

function GuildRentShelvesItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	self.item_cell:SetData({item_id = self.data.cfg.item_id})
	self.node_list.fashion_name.text.text = self.data.cfg.name
end

-------------------------------------------GuildRentShelvesItemRender  end----------------------------------

---------------------------------------------AssignmentGrid start----------------------------------------------

GuildShelvesGrid = GuildShelvesGrid or BaseClass(AsyncBaseGrid)

function GuildShelvesGrid:SetSelectCellIndexByNoCancel(cell_index)
	if self.select_tab[1] == nil then 
        self.select_tab[1] = {}
    end

	self.select_tab[1][cell_index] = true
end

function GuildShelvesGrid:SetCancelSelectCellIndex(cell_index)
	if self.select_tab[1] == nil then 
        self.select_tab[1] = {}
    end

	self.select_tab[1][cell_index] = nil
end

---------------------------------------------AssignmentGrid end----------------------------------------------