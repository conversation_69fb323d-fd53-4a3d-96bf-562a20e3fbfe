-- 选择组队目标
NewTeamRecruitView = NewTeamRecruitView or BaseClass(SafeBaseView)
function NewTeamRecruitView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(694, 480)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_recruit")
	self:SetMaskBg(true)
    self.is_kuafu = false
    self.btn_word_talk_gray = nil
end

function NewTeamRecruitView:__delete()

end

function NewTeamRecruitView:ReleaseCallBack()
    self.is_kuafu = false
    self.btn_word_talk_gray = nil

    if self.team_world_talk then
        GlobalEventSystem:UnBind(self.team_world_talk)
        self.team_world_talk = nil
    end
end

function NewTeamRecruitView:CloseCallBack()

end

function NewTeamRecruitView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleRecruit
	XUI.AddClickEventListener(self.node_list["btn_confirm"], BindTool.Bind1(self.OnClickConfirm, self))
    XUI.AddClickEventListener(self.node_list["select_bg"], BindTool.Bind1(self.OnClickSelect, self))
    self.team_world_talk = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK, BindTool.Bind(self.OnWorldTalkCallBack, self))
end

-- 世界喊话
function NewTeamRecruitView:OnWorldTalkCallBack()

    local cd_time = ChatWGData.Instance:GetChatCdLimint(CHANNEL_TYPE.ZUDUI)
    cd_time = cd_time or COMMON_CONSTS.TEAM_WORLD_TALK
    if cd_time > 0 then
        self.node_list.btn_confirm_text.text.text = string.format(Language.NewTeam.WorldTalk8, cd_time)
        CountDownManager.Instance:AddCountDown("normal_team_world_talk", BindTool.Bind(self.UpdateTeamWordTalkBtn, self),
            BindTool.Bind(self.ComleteTeamWoldTalkCD, self), nil, cd_time, 0.5)
    else
        self:ComleteTeamWoldTalkCD()
    end
end

function NewTeamRecruitView:UpdateTeamWordTalkBtn(elapse_time, total_time)
    if self:IsLoaded() then
		local time = math.ceil(total_time - elapse_time)

        if not self.btn_word_talk_gray then
            self.btn_word_talk_gray = true
            XUI.SetGraphicGrey(self.node_list.btn_confirm, true)
        end

        if self.node_list.btn_confirm_text then
            self.node_list.btn_confirm_text.text.text = string.format(Language.NewTeam.WorldTalk8, time)
        end
	end

    --GlobalEventSystem:Fire(TeamWorldTalk.NEW_TEAM_WORLD_TALK, time)
end

function NewTeamRecruitView:ComleteTeamWoldTalkCD()
    if CountDownManager.Instance:HasCountDown("normal_team_world_talk") then
        CountDownManager.Instance:RemoveCountDown("normal_team_world_talk")
    end

    if self.node_list.btn_confirm_text then
        self.node_list.btn_confirm_text.text.text = Language.NewTeam.RecruitText
    end

    self.btn_word_talk_gray = nil
    XUI.SetGraphicGrey(self.node_list.btn_confirm, false)
    --GlobalEventSystem:Fire(TeamWorldTalk.COMPLETE_CALL_BACK)
end

function NewTeamRecruitView:OnFlush()
	self:FlushLevelLimit()
    local team_type, teamfb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local goal_info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, teamfb_mode)
	if not goal_info then
		return
	end

	self.node_list["target_txt"].text.text = goal_info.team_type_name

    local barrage_cfg = NewTeamWGData.Instance:GetBarrageCfgBYTeamType(team_type)
    if barrage_cfg then
        local rand_num = math.random(1, #barrage_cfg)
        self.node_list.desc_text.text.text = barrage_cfg[rand_num] and barrage_cfg[rand_num].desc or Language.NewTeam.BarrageStr
    else
        self.node_list.desc_text.text.text = Language.NewTeam.BarrageStr
    end

    local ran_index = math.floor(math.random(1, 2))
end

function NewTeamRecruitView:ShowIndexCallBack()

end

function NewTeamRecruitView:FlushLevelLimit()
	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	local str = string.format(Language.NewTeam.PTLevelLimitTop, min_level, max_level)
	EmojiTextUtil.ParseRichText(self.node_list["level_limit_text"].emoji_text, str, 20, COLOR3B.WHITE)
end

function NewTeamRecruitView:OnClickSelect()
    self.is_kuafu = not self.is_kuafu
    self.node_list.select:SetActive(self.is_kuafu)
end

function NewTeamRecruitView:OnClickConfirm()
    if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.DifferentZhandui3)
		return
	end
    
    local recruit_content = self.node_list.desc_text.text.text

	if ChatFilter.Instance:IsIllegal(recruit_content, false) then
        --有非法字符直接不让发
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.IllegalContent)
		return
    end

    NewTeamWGCtrl.Instance:ShowTalkView(self.is_kuafu, recruit_content)
end