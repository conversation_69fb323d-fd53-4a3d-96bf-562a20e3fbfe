 require("game/setting/setting_wg_data")
require("game/setting/setting_view")
require("game/setting/unlock_view")
require("game/setting/setting_guaji")
require("game/setting/login_notic_view")
require("game/setting/setting_guaji_skill_list")
require("game/feed_back/feed_back_view")

local SHOW_LIMIT_LEVEL = 130
local MAIN_UI_BTN_CD = 30 --主界面按钮回缩
local MAIN_UI_TOP_ARROW_CD = 10 --主界面右上角按钮回缩
local UnityEngineInput = UnityEngine.Input
local UnitySysInfo = UnityEngine.SystemInfo

SettingWGCtrl = SettingWGCtrl or BaseClass(BaseWGCtrl)
function SettingWGCtrl:__init()
	if SettingWGCtrl.Instance then
		print_error("[SettingWGCtrl] Attemp to create a singleton twice !")
	end

	SettingWGCtrl.Instance = self
	self.data = SettingWGData.New()
	-- unity3d项目暂时屏蔽
	self.view = SettingView.New()
	self.setting_view = self.view
	self.unlock_view = UnlockView.New()
	self.setting_guaji_skill_list = GuaJiSkillListView.New()
	self.login_notic_view = LoginNoticView.New(GuideModuleName.LoginNoticView)
	self.feed_back_view = FeedBackView.New(GuideModuleName.FeedBackView)
	
	self:RegisterAllProtocols()
	self.is_close = false
	self.time_record = 0
	self.opreat_time = 0
	self.is_check_def_setting = true


	self.OPTION_COUNT = 12
	self.GUAJI_OPTION_COUNT = 19


	self.is_func_open = false				--是否在功能开启中

	Runner.Instance:AddRunObj(self, 8)
	-- 监听游戏设置改变
	self:BindGlobalEvent(SettingEventType.CLOSE_BG_MUSIC,BindTool.Bind1(self.OnCloseBGMusic, self))
	self:BindGlobalEvent(SettingEventType.CLOSE_SOUND_EFFECT,BindTool.Bind1(self.OnCloseSoundEffect, self))
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(SettingEventType.AUTO_LUCK_SCREEN,BindTool.Bind(self.OnGuaJiSettingChange, self))
	self:BindGlobalEvent(FinishedOpenFun, BindTool.Bind(self.FinishedOpenFun, self))
end

function SettingWGCtrl:MainuiOpenCreate()
	-- self.listener_click_view:Open()
	-- self:SendHotkeyOnlineSellEquipInfoReq()
end

function SettingWGCtrl:__delete()
	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view ~= nil then
		self.view:DeleteMe()
		self.view = nil
		self.setting_view = nil
	end

	if self.setting_data ~= nil then
		self.setting_data:DeleteMe()
		self.setting_data = nil
	end

	if self.setting_view ~= nil then
		self.setting_view:DeleteMe()
		self.setting_view = nil
	end

	if nil ~= self.unlock_view then
		self.unlock_view:DeleteMe()
		self.unlock_view = nil
	end

	if nil ~= self.login_notic_view then
		self.login_notic_view:DeleteMe()
		self.login_notic_view = nil
	end

	if SettingWGCtrl.Instance ~= nil then
		SettingWGCtrl.Instance = nil
	end

	if self.setting_guaji_skill_list then
		self.setting_guaji_skill_list:DeleteMe()
		self.setting_guaji_skill_list = nil
	end

	if self.feed_back_view then
		self.feed_back_view:DeleteMe()
		self.feed_back_view = nil
	end

	self:RemoveTimer()
	Runner.Instance:RemoveRunObj(self)
end

function SettingWGCtrl:OnGuaJiSettingChange(value)
	self.data:SetNeedLuckView(value)
	if value then
		self:AddTimer()
	else
		self:RemoveTimer()
	end
end

-- 添加定时器
function SettingWGCtrl:AddTimer()
	if self.time_quest then return end
	self.time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.LayoutRunQuest, self), 1)
	self.time_record = 0
	self.opreat_time = 0
end

-- 移除定时器
function SettingWGCtrl:RemoveTimer()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	self.time_record = 0
	self.opreat_time = 0
end

function SettingWGCtrl:Open(tab_index)
	self.view:Open(tab_index)
end

function SettingWGCtrl:LayoutRunQuest()
	self.time_record = UnityEngine.Time.unscaledTime
	self.time_record = self.time_record + 1
	if  self.opreat_time > 0
		-- and not UnityEngine.Debug.isDebugBuild  -- 加上此制是为了不影响开发
		and RoleWGData.Instance.role_vo.level >= 150
		and self.time_record - self.opreat_time >= GameEnum.Lock_Time then
		-- and not FunctionGuide.Instance:GetIsGuide() then
		self:RemoveTimer()
		self.unlock_view:Open()
		local brightness = DeviceTool.GetScreenBrightness()
		self.data:SetScreenBright(brightness)
		if brightness > 0.3 then
			DeviceTool.SetScreenBrightness(0.3)
		end
	end
	-- if self.time_record <GameEnum.Lock_Time then
	-- 	self.unlock_view:Close()
	-- end
end

function SettingWGCtrl:GmOpenUnLockView()
	ViewManager.Instance:CloseAll()
	self.unlock_view:Open()
end


function SettingWGCtrl:SetIsClose(is_close)
	self.is_close = is_close
end

--设置是否在功能开启中
function SettingWGCtrl:FinishedOpenFun(state)
	self.is_func_open = state
end

function SettingWGCtrl:Update(now_time, elapse_time)
	--如果在引导中或者在功能开启中就不自动关闭菜单栏
	if FunctionGuide.Instance:GetIsGuide()
		or ViewManager.Instance:IsOpen(GuideModuleName.NormalGuideView)
		 or self.is_func_open then
		return
	end

	if UnityEngineInput.GetMouseButtonDown(0) then
		self.key_down = true
		self.key_up = false
	end
	if UnityEngineInput.GetMouseButtonUp(0) then
		self.key_up = true
		self.key_down = false
	end
	if not self.key_up and self.key_down then
		self.opreat_time = UnityEngine.Time.unscaledTime
	end
	if UnityEngineInput.touchCount > 0 then
		self.opreat_time = UnityEngine.Time.unscaledTime
	end

	if self.opreat_time > 0 and UnityEngine.Time.unscaledTime - self.opreat_time > 3 and GameNet.Instance:IsGameServerConnected() then
		self:Fire(MainUIEventType.PORTRAIT_TOGGLE_CHANGE, false, true)
	end

	local is_flag = false
	if self.mainui_btn_flag and self.opreat_time > 0 and self:CheckIsAnyViewOpen() then
		self:SetOperatTimeValid()
		self.mainui_btn_flag = false
		is_flag = true
		MainuiWGCtrl.Instance:SetToggleMenuIsOn(false)
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Common then
		--单独检测顶部按钮收起
		if not is_flag and self.opreat_time > 0 and self:CheckTopArrowToggleIsOn() then
			MainuiWGCtrl.Instance:PlayTopButtonTween(false)
		end
	end
end

function SettingWGCtrl:SetOperatTimeValid()
	self.opreat_time = UnityEngine.Time.unscaledTime
end

function SettingWGCtrl:SetMainToggleFlag( isOn )
	self.mainui_btn_flag = isOn
end

function SettingWGCtrl:GetMainToggleFlag()
	return self.mainui_btn_flag
end

function SettingWGCtrl:CheckIsAnyViewOpen()
	local is_view_open = ViewManager.Instance:HasOpenView()
	if is_view_open then
		return false
	end

 	if UnityEngine.Time.unscaledTime - self.opreat_time > MAIN_UI_BTN_CD and GameNet.Instance:IsGameServerConnected() then
 		return true
 	end
 	return false
end

function SettingWGCtrl:CheckTopArrowToggleIsOn()
	local is_on_s_btn = MainuiWGCtrl.Instance:GetShrinkButtonToggleIsOn()

	if not is_on_s_btn then
		return false
	end

 	if UnityEngine.Time.unscaledTime - self.opreat_time > MAIN_UI_TOP_ARROW_CD and GameNet.Instance:IsGameServerConnected() then
 		self:SetOperatTimeValid()
 		return true
 	end
 	return false
end

function SettingWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCHotkeyInfoAck, "OnHotkeyInfoAck")
	self:RegisterProtocol(SCUpdateNoticeInfo, "OnUpdateNoticeInfo")
	self:RegisterProtocol(CSRoleReturnReAlivePosi)

	self:RegisterProtocol(CSRoleFeedbackReq)
	self:RegisterProtocol(SCRoleFeedbackInfo,"OnSCRoleFeedbackInfo")
end

-- 模拟器开启高帧率
-- SettingData代码太乱，需要重构，只能暂时采取这种方案处理了。以后重构了再删除
local has_checkd = false
function SettingWGCtrl:CheckHighFPS()
	if has_checkd then
		return
	end
	has_checkd = true

	if DeviceTool.IsEmulator() or IS_LOCLA_WINDOWS_DEBUG_EXE then
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		if main_role_vo.level <= 1 then
			self.data:SetSettingData1(SETTING_TYPE.CHANGE_FPS, true, true)
		end
	end
end

function SettingWGCtrl:OnHotkeyInfoAck(protocol)
	self.data:OnSettingInfo(protocol.set_data_list)
	self.data:FixBugOnFirstRecv(protocol.set_data_list)
	self:CheckHighFPS()
	-- SettingWGData.Instance:SetSettingData(protocol.set_data_list)
	-- if self.is_check_def_setting then
	-- 	self:CheckDefaultSetting()
	-- 	self.is_check_def_setting = false
	-- end
	-- --系统设置
	-- local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SETTING_1)
	-- local flag_t = bit:d2b(data)
	-- for i = 1, self.setting_view.OPTION_COUNT do
	-- 	local flag = (1 == flag_t[33 - i])
	-- 	GlobalEventSystem:Fire(SettingEventType.SYSTEM_SETTING_CHANGE, i, flag)
	-- end

	-- --挂机设置
	-- local guaji_data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SETTING_2)
	-- local guaji_flag_t = bit:d2b(guaji_data)
	-- for i = 1, self.setting_view.GUAJI_OPTION_COUNT do
	-- 	local flag = (1 == guaji_flag_t[33 - i])
	-- 	GlobalEventSystem:Fire(SettingEventType.GUAJI_SETTING_CHANGE, i, flag)
	-- end

	-- unity3d项目暂时屏蔽
	if self.view:IsOpen() then
		local index = self.view:GetShowIndex()
		-- if index == 1 then
		-- 	SettingContentView.Instance:FlushClick1()
		-- elseif index == 4 then
		-- 	SettingContentView.Instance:FlushClick2()
		-- end
		self.view:Flush(index)
	end
end



-- function SettingWGCtrl:OnHotkeyInfoAck(protocol)
-- 	PrintTable(protocol)
-- 	SettingWGData.Instance:SetSettingData(protocol.set_data_list)

-- 	--系统设置
-- 	local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SETTING)
-- 	local flag_t = bit:d2b(data)
-- 	for i = 1, self.setting_view.OPTION_COUNT do
-- 		local flag = (1 == flag_t[33 - i])
-- 		GlobalEventSystem:Fire(SettingEventType.SYSTEM_SETTING_CHANGE, i, flag)
-- 	end

-- 	--挂机设置
-- 	local guaji_data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.GUAJI_SETTING)
-- 	local guaji_flag_t = bit:d2b(guaji_data)
-- 	for i = 1, self.setting_view.GUAJI_OPTION_COUNT do
-- 		local flag = (1 == guaji_flag_t[33 - i])
-- 		GlobalEventSystem:Fire(SettingEventType.GUAJI_SETTING_CHANGE, i, flag)
-- 	end

-- 	self.setting_view:Flush(SettingViewIndex.System)
-- 	self.setting_view:Flush(SettingViewIndex.Guaji)
-- 	self.setting_guaji_skill_list:Flush()

-- 	if self.is_check_def_setting then
-- 		self:CheckDefaultSetting()
-- 		self.is_check_def_setting = false
-- 	end
-- end



function SettingWGCtrl:OnUpdateNoticeInfo(protocol)
	self.data:OnUpdateNoticeInfo(protocol)
	WelfareWGCtrl.Instance:OnUpdateNoticeInfo(protocol)
	local state = SettingWGData.Instance:GetRedPointState()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if not IS_ON_CROSSSERVER
		and state
		and not UnityEngine.Debug.isDebugBuild  -- 加上此制是为了不影响开发（每次上线都会弹出这个面板好烦）
		and main_role_vo.level > SHOW_LIMIT_LEVEL then
		ViewManager.Instance:Open(GuideModuleName.Setting, "setting_notice")
	end

	-- unity3d项目暂时屏蔽
	-- if self.view:IsOpen() then
	-- 	self.view:Flush("flush_reward_content")
	-- end
	RemindManager.Instance:Fire(RemindName.Setting)

	WelfareWGCtrl.Instance:OnFlushWelfareGongGao()
end

function SettingWGCtrl:SendHotkeyInfoReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSHotkeyInfoReq)
	protocol:EncodeAndSend()
end

function SettingWGCtrl:SendChangeHotkeyReq(list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChangeHotkeyReq)
	protocol.list = list
	protocol:EncodeAndSend()
end

-- --是否陌生人聊天
-- function SettingWGCtrl:SendChangeStrangerChat(index, value)
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSChangeSetting)
-- 	protocol.index = index or 0
-- 	protocol.value = value or 0
-- 	protocol:EncodeAndSend()
-- end

-- 发送领取奖励请求
function SettingWGCtrl:SendUpdateNoticeFetchReward()
	local protocol = ProtocolPool.Instance:GetProtocol(CSUpdateNoticeFetchReward)
	protocol:EncodeAndSend()
end

function SettingWGCtrl:OnCloseBGMusic(value)
	if value then
		AudioService.Instance:SetMusicVolume(0.0)
	else
		local bg_music_volume = PlayerPrefsUtil.GetFloat("bg_music_volume")
		bg_music_volume = (bg_music_volume > 0 and bg_music_volume) or 0.8
		AudioService.Instance:SetMusicVolume(bg_music_volume)
	end
end

function SettingWGCtrl:OnCloseSoundEffect(value)
	if value then
		AudioService.Instance:SetSFXVolume(0.0)
	else
		local sound_effect_volume = PlayerPrefsUtil.GetFloat("sound_effect_volume")
		sound_effect_volume = (sound_effect_volume > 0 and sound_effect_volume) or 0.8
		AudioService.Instance:SetSFXVolume(sound_effect_volume)
	end
end

function SettingWGCtrl:SendRequest(list)
	local url_str = GLOBAL_CONFIG.param_list.gm_report_url
	if url_str == nil or url_str == "" then
		url_str = "http://api.192.168.0.135/client/gm/report"
	end

	url_str = url_str .. "?" ..
	"zone_id=" .. list.zone_id ..
	"&server_id=" .. list.server_id ..
	"&user_id=" .. list.user_id ..
	"&role_id=" .. list.role_id ..
	"&role_name=" .. list.role_name ..
	"&role_level=" .. list.role_level ..
	"&role_gold=" .. list.role_gold ..
	"&role_scene=" .. list.role_scene ..
	"&issue_type=" .. list.issue_type ..
	"&issue_subject=" .. list.issue_subject ..
	"&issue_content=" .. list.issue_content..
	"&device_model="..UnitySysInfo.deviceModel..
	"&sys_version="..UnitySysInfo.operatingSystem
	local call_back = function(url, is_succ, data)
		--TipWGCtrl.Instance:ShowSystemMsg(Language.Setting.SublimtedComplete)
	end
	HttpClient:Request(url_str, call_back)
end

function SettingWGCtrl:OpenGuaJiSkillList()
	self.setting_guaji_skill_list:Open()
end

function SettingWGCtrl:FlushSkill()
	if self.view and self.view:IsOpen() then
		self.view:FlushSkill()
	end
end

function SettingWGCtrl:FlushGuaji()
	if self.view and self.view:IsOpen() then
		self.view:Flush(SettingViewIndex.Guaji)
	end
end

-- function SettingWGCtrl:SendHotkeyOnlineSellEquipInfoReq()
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSHotkeyOnlineSellEquipInfoReq)
-- 	protocol:EncodeAndSend()
-- end

-- function SettingWGCtrl:SendChangeHotkeyOnlineSellEquipInfo(opera_type, param_1, param_2)
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSChangeHotkeyOnlineSellEquipInfo)
-- 	protocol.type = opera_type or 0
-- 	protocol.param_1 = param_1 or 0
-- 	protocol.param_2 = param_2 or 0
-- 	protocol:EncodeAndSend()
-- end

-- function SettingWGCtrl:OnSCHotkeyOnlineSellEquipInfo(protocol)
-- 	self.setting_data:SetType(protocol.type)
-- 	self.setting_view:Flush(SettingViewIndex.Guaji)
-- end

-- 检查默认设置
function SettingWGCtrl:CheckDefaultSetting()
	local data, param = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SETTING_2)
	if data == 0 and param == 0 then
		local guaji_default_select = {
			[GUAJI_SETTING_TYPE.GUAJI_PICKUP_GREEN] = true,
			[GUAJI_SETTING_TYPE.GUAJI_PICKUP_BLUE] = true,
			[GUAJI_SETTING_TYPE.GUAJI_PICKUP_PURPLE] = true,
			[GUAJI_SETTING_TYPE.GUAJI_PICKUP_ORANGE] = true,
			[GUAJI_SETTING_TYPE.GUAJI_PICKUP_COIN] = true,
			[GUAJI_SETTING_TYPE.GUAJI_PICKUP_OTHER] = true,
			[GUAJI_SETTING_TYPE.GUAJI_SCALE] = true,
			[GUAJI_SETTING_TYPE.GUAJI_RONGLIAN] = true,
			[GUAJI_SETTING_TYPE.GUAJI_LOCK] = true,

			-- [GUAJI_SETTING_TYPE.GUAJI_SKILL_1] = true,
			-- [GUAJI_SETTING_TYPE.GUAJI_SKILL_2] = true,
			-- [GUAJI_SETTING_TYPE.GUAJI_SKILL_3] = true,
			-- [GUAJI_SETTING_TYPE.GUAJI_SKILL_4] = true,
			-- [GUAJI_SETTING_TYPE.GUAJI_SKILL_5] = true,
			-- [GUAJI_SETTING_TYPE.GUAJI_SKILL_6] = true,

			-- [GUAJI_SETTING_TYPE.GUAJI_PICKUP] = true,
			-- [GUAJI_SETTING_TYPE.GUAJI_BREAK] = false,
			-- [GUAJI_SETTING_TYPE.GUAJI_CARDUP] = false,
			-- [GUAJI_SETTING_TYPE.GUAJI_TREASURE] = false,
			-- [GUAJI_SETTING_TYPE.GUAJI_SHENJIANG] = false,
			-- [GUAJI_SETTING_TYPE.GUAJI_SHUIJING] = true,

		}
		self:ChangeGuaJiSetting(guaji_default_select)
	end

	-- data, param = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SUPPLY)
	-- if data == 0 and param == 0 then
	-- 	self:ChangeSupplySetting(80, 80)
	-- end
	-- data, param = SettingWGData.Instance:GetDataByIndex(HOT_KEY.AUTO_PICK_BREAK)
	-- if data == 0 and param == 0 then
	-- 	self:ChangePickBreakSetting(1, 1, 1, 1)
	-- end
	-- data, param = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SOUND)
	-- self:ChangeSoundSetting(data)

	-- data, param = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_BG_SOUND)
	-- self:ChangeBgSoundSetting(data)
end

--获取系统设置信息
function SettingWGCtrl:GetSystemSetting(setting_type)
	local data = SettingWGData.Instance:GetSettingData(setting_type)
	return data
end

--获取挂机设置信息
function SettingWGCtrl:GetGuaJiSetting(guaji_setting_type)
	local data = SettingWGData.Instance:GetSettingData(HOT_KEY.GUAJI_SETTING)
	if data ~= nil then
		local set_flag = bit:d2b(data)
		return set_flag[33 - guaji_setting_type] == 1
	end
end

-- 改变系统设置
function SettingWGCtrl:ChangeSetting(setting_t)
	local setting_t = setting_t or {}

	local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SETTING_1)
	local set_flag = bit:d2b(data)
	for k, v in pairs(setting_t) do
		set_flag[33 - k] = v and 1 or 0
	end

	data = bit:b2d(set_flag)
	SettingWGData.Instance:SetDataByIndex(HOT_KEY.SYS_SETTING_1, data)
	self:SendChangeHotkeyReq({[1] = {HOT_KEY.SYS_SETTING_1, data}})

	self.setting_view:Flush(SettingViewIndex.System)

	for k, v in pairs(setting_t) do
		GlobalEventSystem:Fire(SettingEventType.SYSTEM_SETTING_CHANGE, k, v)
	end
end

-- 改变挂机设置
function SettingWGCtrl:ChangeGuaJiSetting(setting_t)
	local setting_t = setting_t or {}

	local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SETTING_2)
	local set_flag = bit:d2b(data)
	for k, v in pairs(setting_t) do
		set_flag[33 - k] = v and 1 or 0
	end

	data = bit:b2d(set_flag)
	SettingWGData.Instance:SetDataByIndex(HOT_KEY.SYS_SETTING_2, data)
	self:SendChangeHotkeyReq({[1] = {HOT_KEY.GUAJI_SETTING, data}})

	self.setting_view:Flush(SettingViewIndex.Guaji)
	self.setting_guaji_skill_list:Flush()

	for k, v in pairs(setting_t) do
		GlobalEventSystem:Fire(SettingEventType.GUAJI_SETTING_CHANGE, k, v)
	end
end

-- --设置改变情缘设置
-- function SettingWGCtrl:ChangeMarrySetting(cell_select, status_select)
-- 	local data = cell_select * 256 + status_select

-- 	self.setting_data:SetDataByIndex(HOT_KEY.MARRY_SETING, data)
-- 	self:SendChangeHotkeyReq(HOT_KEY.MARRY_SETING, data)
-- end

-- function SettingWGCtrl:ChangeSwordSoulSetting(set_flag_t)
-- 	local data = bit:b2d(set_flag_t)
-- 	self.setting_data:SetDataByIndex(HOT_KEY.SWORD_SOUL, data)
-- 	self:SendChangeHotkeyReq(HOT_KEY.SWORD_SOUL, data)
-- end

-- 改变补给设置
function SettingWGCtrl:ChangeSupplySetting(hp_percent, mp_percent)
	-- SettingWGData.Instance:SetSupplyData(hp_percent, mp_percent)
	-- local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SUPPLY)
	-- self:SendChangeHotkeyReq(HOT_KEY.SUPPLY, data)
end

-- 改变补给设置
function SettingWGCtrl:ChangePickBreakSetting(pick_color, break_color, card_color, treasure_color)
	-- SettingWGData.Instance:SetPickBreakData(pick_color, break_color, card_color, treasure_color)
	-- local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.AUTO_PICK_BREAK)
	-- self:SendChangeHotkeyReq(HOT_KEY.AUTO_PICK_BREAK, data)
end

-- 改变声效设置
function SettingWGCtrl:ChangeSoundSetting(value)
	SettingWGData.Instance:SetDataByIndex(HOT_KEY.SYS_SOUND, value)
	local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_SOUND)
	self:SendChangeHotkeyReq({[1] = {HOT_KEY.SYS_SOUND, data}})
	-- self:SendChangeHotkeyReq(7, 100)
	GlobalEventSystem:Fire(SettingEventType.SOUND_SETTING_CHANGE, data)
end

-- 改变背景声音设置
function SettingWGCtrl:ChangeBgSoundSetting(value)
	SettingWGData.Instance:SetDataByIndex(HOT_KEY.SYS_BG_SOUND, value)
	local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.SYS_BG_SOUND)
	self:SendChangeHotkeyReq({[1] = {HOT_KEY.SYS_BG_SOUND, data}})
	-- self:SendChangeHotkeyReq(8, 100)
	GlobalEventSystem:Fire(SettingEventType.BG_SOUND_SETTING_CHANGE, data)
end


function SettingWGCtrl:OpenUnLockView()
	self.unlock_view:Open()
end

function SettingWGCtrl:CloseUnLockView()
	self.unlock_view:Close()
end

function SettingWGCtrl:GetUnlockView()
	return self.unlock_view
end

-- 客户端脱离卡死
function SettingWGCtrl:SendRoleReturnReAlivePosi()
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleReturnReAlivePosi)
	protocol:EncodeAndSend()
end


----------------上线公告
function SettingWGCtrl:SendNoticeRequest()
	-- if UNITY_EDITOR or IS_AUDIT_VERSION then
	-- 	return
	-- end

	local refreshData = {
        plat_id = CHANNEL_AGENT_ID,
    }
    PhpHandle.HandlePhpGetRequest(GLOBAL_CONFIG.api_urls.client.notice, refreshData, refreshData,
	function(cbData)

		if self.data then
			self.data:SetNoticData(cbData.data and cbData.data.list or {})
		end

		local notice_red = self.data:GetAllNoticHasRemind()
		if notice_red then
			if self.login_notic_view ~= nil then
				if self.login_notic_view:IsOpen() then
					self.login_notic_view:Flush()
				else
					self.login_notic_view:Open()
				end
			end
		else
			if self.login_notic_view and self.login_notic_view:IsOpen() then
				self.login_notic_view:Flush()
			end
		end

		ViewManager.Instance:FlushView(GuideModuleName.Login, 0, "flush_gonggao", {true})
	end,

	function(cbData)
		ViewManager.Instance:FlushView(GuideModuleName.Login, 0, "flush_gonggao", {false})
	end)
end


function SettingWGCtrl:SetLoginState(is_login)
	self.is_login_notic = is_login
end

function SettingWGCtrl:OnSCRoleFeedbackInfo(protocol)
	self.data:OnSCRoleFeedbackInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FeedBackView)
end

function SettingWGCtrl:RequestFeedBackReward()
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleFeedbackReq)
	protocol:EncodeAndSend()
end