ShenShouSkillDescView = ShenShouSkillDescView or BaseClass(SafeBaseView)

function ShenShouSkillDescView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_skill_desc")
end

function ShenShouSkillDescView:ReleaseCallBack()
	self.skill_icon = nil
end

function ShenShouSkillDescView:LoadCallBack()
	self.skill_icon = self.node_list["ph_skill_icon"]
end

function ShenShouSkillDescView:ShowIndexCallBack(index)
end

function ShenShouSkillDescView:OnFlush(param_t, index)
	local skill_cfg = ShenShouWGData.Instance:GetShenShouSkillCfg(self.data.skill_type, self.data.level)
	if nil == skill_cfg then
		return
	end

	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon_id)
	self.skill_icon.image:LoadSprite(bundle, asset, function()
		self.skill_icon.image:SetNativeSize()
	end)

	self.node_list.lbl_skill_name.text.text = string.format("%s LV.%s", skill_cfg.name, skill_cfg.level)
	self.node_list.lbl_skill_type.text.text = string.format("（%s）", skill_cfg.buff)
	self.node_list["rich_desc"].text.text = skill_cfg.description
end

function ShenShouSkillDescView:SetData(data)
	if nil == data then
		return
	end

	self.data = data
	self:Open()
end

function ShenShouSkillDescView:OnClickClose()
	self:Close()
end