-- 毒敌覆世拍脸图
XunYuanFreshPoolPopView = XunYuanFreshPoolPopView or BaseClass(SafeBaseView)

function XunYuanFreshPoolPopView:__init()
    self:SetMaskBg(true)
    self.view_style = ViewStyle.Window
    self:AddViewResource(0, "uis/view/xuyuan_freshpool_ui_prefab", "xuyuan_freshpool_pop_view")
end

function XunYuanFreshPoolPopView:ReleaseCallBack()
    if self.count_down then
        CountDown.Instance:RemoveCountDown(self.count_down)
        self.count_down = nil
    end
end

function XunYuanFreshPoolPopView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.GoTo, self))
    XUI.AddClickEventListener(self.node_list.bx_skip_anim_btn, BindTool.Bind(self.SetNoShow, self))
end

function XunYuanFreshPoolPopView:OnFlush()
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
    local start_time = TimeUtil.FormatUnixTime2Date(act_info.start_time)
    local end_time = TimeUtil.FormatUnixTime2Date(act_info.end_time - 86400)-- 结束时间是隔天0点
    self.node_list.activity_time.text.text = string.format(Language.XuYuanFreshPool.ActivityTime, start_time.month, start_time.day, end_time.month, end_time.day)
    self.node_list.desc.text.text = string.format(Language.XuYuanFreshPool.PopDesc)
end

function XunYuanFreshPoolPopView:SetNoShow(is_on)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local set_key = "dudi_fushi_day_no_open" .. role_id
    if not is_on then
        PlayerPrefsUtil.SetInt(set_key, -1)
    else
        PlayerPrefsUtil.SetInt(set_key, day)
    end
end

function XunYuanFreshPoolPopView:GoTo()
    ViewManager.Instance:Open(GuideModuleName.XuYuanFreshPoolView)
    self:Close()
end