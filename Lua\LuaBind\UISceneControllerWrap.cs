﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UISceneControllerWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginClass(typeof(UISceneController), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SetConfiguration", SetConfiguration);
		<PERSON><PERSON>Function("GetWaterCommonMeahRender", GetWaterCommonMeahRender);
		<PERSON><PERSON>Function("GetBgPanelCommonMeahRender", GetBgPanelCommonMeahRender);
		<PERSON><PERSON>RegFunction("GetEffectCommonGameObjectAttach", GetEffectCommonGameObjectAttach);
		<PERSON><PERSON>Function("GetEffectCommonSmokeGameObjectAttach", GetEffectCommonSmokeGameObjectAttach);
		<PERSON>.RegFunction("GetEffectsTransform", GetEffectsTransform);
		<PERSON><PERSON>unction("GetSceneConfiguration", GetSceneConfiguration);
		<PERSON><PERSON>("__eq", op_Equality);
		<PERSON><PERSON>un<PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("currentConfigIndex", get_currentConfigIndex, set_currentConfigIndex);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetConfiguration(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<int>(L, 2))
			{
				UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
				int arg0 = (int)LuaDLL.lua_tonumber(L, 2);
				obj.SetConfiguration(arg0);
				return 0;
			}
			else if (count == 2 && TypeChecker.CheckTypes<UISceneController.SceneConfiguration>(L, 2))
			{
				UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
				UISceneController.SceneConfiguration arg0 = (UISceneController.SceneConfiguration)ToLua.ToObject(L, 2);
				obj.SetConfiguration(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UISceneController.SetConfiguration");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetWaterCommonMeahRender(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
			UnityEngine.MeshRenderer o = obj.GetWaterCommonMeahRender();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetBgPanelCommonMeahRender(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
			UnityEngine.MeshRenderer o = obj.GetBgPanelCommonMeahRender();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEffectCommonGameObjectAttach(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
			Game.GameObjectAttach o = obj.GetEffectCommonGameObjectAttach();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEffectCommonSmokeGameObjectAttach(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
			Game.GameObjectAttach o = obj.GetEffectCommonSmokeGameObjectAttach();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEffectsTransform(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
			UnityEngine.Transform o = obj.GetEffectsTransform();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSceneConfiguration(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UISceneController obj = (UISceneController)ToLua.CheckObject<UISceneController>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			UISceneController.SceneConfiguration o = obj.GetSceneConfiguration(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_currentConfigIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController obj = (UISceneController)o;
			int ret = obj.currentConfigIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index currentConfigIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_currentConfigIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UISceneController obj = (UISceneController)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.currentConfigIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index currentConfigIndex on a nil value");
		}
	}
}

