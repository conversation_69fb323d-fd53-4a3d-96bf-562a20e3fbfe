﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DelayAnimator : MonoBehaviour {

    public float delayTime = 5f;
    private float startTime = 0f;
    private bool needDelay = true;
    private Animator ani;
	// Use this for initialization
	void Start () {
        ani = this.gameObject.GetComponent<Animator>();
        if (ani)
        {
            ani.enabled = false;
        }
    }
	
	// Update is called once per frame
	void Update () {
        if (needDelay && ani)
        {
            startTime += Time.fixedDeltaTime;
            if (startTime >= delayTime)
            {
                needDelay = false;
                ani.enabled = true;
            }
           
        }

    }
}
