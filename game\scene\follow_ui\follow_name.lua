FollowName = FollowName or BaseClass()

local TypeText = typeof(TMPro.TextMeshProUGUI)
local TypeUIAttachName = typeof(UIAttachName)

function FollowName:__init(asset_name, target_transform, follow_parent)
	self.asset_name = asset_name
	self.target_transform = target_transform
	self.follow_parent = follow_parent
end

function FollowName:__delete()
	self.target_transform = nil
	self.follow_parent = nil
	self.name_text = nil
	self.loader = nil
end

local function CreateRootCallBack(gameobj, cbdata)
	local self = cbdata[1]
	FollowUi.ReleaseCBData(cbdata)

	if IsNil(gameobj) then
		return
	end

	local attach = gameobj:GetComponent(TypeUIAttachName)
	attach.target = self.target_transform
	attach.parent = self.follow_parent

	self.name_text = gameobj:GetComponent(TypeText)
	self:UpdateName()
end

function FollowName:SetName(name, color)
	self.name = name
	self.color = color

	if nil == name or "" == name then
		if self.loader then
			self.loader:Destroy()
		end
		self.name_text = nil

		return
	end

	self:UpdateName()

	if nil == self.name_text then
		local async_loader = AllocAsyncLoader(self, "root_loader")
		self.loader = async_loader
		async_loader:SetIsUseObjPool(true)
		async_loader:SetLoadPriority(ResLoadPriority.low)
		local canvas = FollowName.GetFloatingNameCanvas()
		async_loader:SetParent(canvas.transform, false)

		local cbdata = FollowUi.GetCBData()
		cbdata[1] = self
		async_loader:Load("uis/view/miscpre_load/namenode_prefab", self.asset_name, CreateRootCallBack, cbdata)
	end
end

function FollowName:UpdateName()
	if self.name_text then
		self.name_text.text = self.name
		if self.color then
			self.name_text.color = self.color
		end
	end
end

function FollowName.GetFloatingNameCanvas()
	if nil == FollowName.floating_name_canvas then
		local obj = ResMgr:Instantiate(SafeBaseView.GetBaseViewParentTemplate())
		obj.name = "FloatingNameCanvas"
		obj.transform:Find("Root").gameObject:SetActive(false)
		obj:SetActive(true)

		local canvas = obj:GetComponent(typeof(UnityEngine.Canvas))
		canvas.overrideSorting = true
		canvas.sortingOrder = UiLayer.SceneName

		canvas.worldCamera = UICamera

		local canvas_transform = canvas.transform
		obj:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster)).enabled = false

		canvas_transform:SetParent(UILayer.transform, false)
		canvas_transform:SetLocalScale(1, 1, 1)

		local rect = canvas_transform:GetComponent(typeof(UnityEngine.RectTransform))
		rect.anchorMax = u3dpool.vec2(1, 1)
		rect.anchorMin = u3dpool.vec2(0, 0)
		rect.anchoredPosition3D = u3dpool.vec3(0, 0, 0)
		rect.sizeDelta = u3dpool.vec2(0, 0)

		FollowName.floating_name_canvas = canvas
	end

	return FollowName.floating_name_canvas
end