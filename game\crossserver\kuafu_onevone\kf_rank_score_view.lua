FieldPvPRankScoreView = FieldPvPRankScoreView or BaseClass(SafeBaseView)

function FieldPvPRankScoreView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_rank_score")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function FieldPvPRankScoreView:LoadCallBack()
    -- self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self:SetSecondView(Vector2(534,350))
    self.node_list.title_view_name.text.text = Language.Field1v1.TitleRankScore
	self.rank_score_list = AsyncListView.New(RankScoreItemRender,self.node_list["rank_score_list"])
	self.has_load_callback = true
	if self.need_flush then
		self:Flush(self.need_flush)
		self.need_flush = nil
	end		
end

function FieldPvPRankScoreView:ReleaseCallBack()
	if self.rank_score_list then
		self.rank_score_list:DeleteMe()
		self.rank_score_list = nil
	end
	self.has_load_callback = nil
	self.need_flush = nil
end

function FieldPvPRankScoreView:OnFlush( param )
	if not self.has_load_callback then
		self.need_flush = param
		return
	end
	local tb = "table"
	if type(param) ~= tb then
		return
	end
	for k,v in pairs(param) do
		if type(v) == tb then
			for m,n in pairs(v) do
				self:FlushRankMatchAimPanel(n)
			end
		end
	end
end

function FieldPvPRankScoreView:FlushRankMatchAimPanel(index)
	local data
	if index == KFPVP_TYPE.ONE then
		data = KuafuOnevoneWGData.Instance:GetBigRankScore()
	elseif index == KFPVP_TYPE.MORE then  --3v3
		data = KuafuPVPWGData.Instance:GetBigRankScore()
	end
	self.rank_score_list:SetDataList(data)
end

----------------------ItemRender------------------------------
RankScoreItemRender = RankScoreItemRender or BaseClass(BaseRender)

function RankScoreItemRender:__init()

end

function RankScoreItemRender:__delete()

end

function RankScoreItemRender:LoadCallBack()

end

function RankScoreItemRender:OnFlush()
	if not self.data then return end
	self.node_list.rank.text.text = self.data.name
	self.node_list.score.text.text = self.data.low_score .. (self.data.up_score and "-" .. self.data.up_score or "+")
	local rewared_id
	if self.data.pk_type == "pvp" then
		rewared_id = KuafuPVPWGData.Instance:GetRewardBaseCell(self.data.low_score)
	elseif self.data.pk_type == "onevone" then
		rewared_id = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self.data.low_score)
	end
	local seq = math.floor(rewared_id.seq / 5) + 1
	self.node_list.rank_image.image:LoadSprite(ResPath.GetKf1V1("Image_YiZhan_Grade" .. seq))
	self.node_list.render_bg:SetActive(self.index%2 == 1)
end

function RankScoreItemRender:OnSelectChange(is_select)
	-- self.node_list.highlight:SetActive(is_select)
end