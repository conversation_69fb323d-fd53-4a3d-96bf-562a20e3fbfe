require("manager/audio_wg_data")
-- 音频管理
AudioService = AudioService or BaseClass()

function AudioService:__init()
	if AudioService.Instance ~= nil then
		print_error("AudioService to create singleton twice!")
	end
	AudioService.Instance = self

	self.data = AudioWGData.New()

	self.music_volume = 1.0
	self.sfx_volume = 1.0
	self.master_volume = 1.0
	self.audio_mixer = nil

	local loader = AllocResAsyncLoader(self, "AudioMixer")
	loader:Load(
		"audios/mixers",
		"AudioMain",
		typeof(UnityEngine.Audio.AudioMixer),
        BindTool.Bind(self.OnLoadComplete, self))
end

function AudioService:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.audio_player then
		self:StopBgm()
		self.audio_player = nil
	end

    self:ReleaseAudio()
    AudioService.Instance = nil
end

function AudioService:OnLoadComplete(mixer)
	self.audio_mixer = mixer
	self:SetMusicVolume(self.music_volume)
	self:SetSFXVolume(self.sfx_volume)
	self:SetMasterVolume(self.master_volume)
end

function AudioService:GetAudioMixerGroup(mixerGroupPath)
	if self.audio_mixer then
		local list = self.audio_mixer:FindMatchingGroups(mixerGroupPath)
		return list[0]
	end
	return nil
end

function AudioService:SetMusicVolume(volume)
	self.music_volume = volume
	if self.audio_mixer ~= nil then
		if volume == 0 then
			self.audio_mixer:SetFloat("MusicVolume", -80)
		else
			self.audio_mixer:SetFloat("MusicVolume", 40 * self.music_volume - 40)
		end
	end
end

function AudioService:SetSFXVolume(volume)
	self.sfx_volume = volume
	if self.audio_mixer ~= nil then
		if volume == 0 then
			self.audio_mixer:SetFloat("SFXVolume", -80)
		else
			self.audio_mixer:SetFloat("SFXVolume", 40 * self.sfx_volume - 40)
		end
	end
end

-- 播放领取奖励的音效
function AudioService:PlayRewardAudio()
	--策划需求，去掉该音效 2019-06-12 17:32:37
	-- local audio_name = AudioWGData.Instance:GetOtherCfgKeyData("Rewards")
	-- if audio_name then
	-- 	AudioManager.PlayAndForget("audios/sfxs/other", audio_name)
	-- end
end

-- 播放进阶成功的音效
function AudioService:PlayAdvancedAudio()
	local audio_name = AudioWGData.Instance:GetOtherCfgKeyData("Advanced")
	if audio_name then
		AudioManager.PlayAndForget("audios/sfxs/uis", audio_name)
	end
end

-- 得到当前音效音量
function AudioService:GetSFXVolume()
	return self.sfx_volume
end

--关闭所有声音
function AudioService:SetMasterVolume(volume)
	self.master_volume = volume
	if self.audio_mixer ~= nil then
		if volume == 0 then
			self.audio_mixer:SetFloat("MasterVolume", -80)
		else
			self.audio_mixer:SetFloat("MasterVolume", 40 * self.master_volume - 40)
		end
	end
end

--得到当前总音量
function AudioService:GetMasterVolume()
	return self.master_volume
end

-- 播放背景音乐
function AudioService:PlayBgm(bundle, asset)
    self:ReleaseAudio()
    AudioManager.Play(bundle, asset, nil, nil, function(player)
		self:ReleaseAudio()
        self.audio_player = player
    end, nil, true)
end

function AudioService:StopBgm()
	if self.audio_player then
    	self.audio_player:Stop()
	end
end

function AudioService:ReleaseAudio()
    if self.audio_player then
        AudioManager.StopAudio(self.audio_player)
        self.audio_player = nil
    end
end

-- 初始化收费聊天
-- appID：*********
-- appKey：17cfacf88f498f61726bd4d859c2c133
-- ServerInfo：Voice服务器地址，默认为空
-- Language：翻译的语言（ China = 0, Korean = 1, English = 2, Japanese = 3）
function AudioService:InitFeesAudio()
	if IS_FEES_VOICE and not UnityEngine.Debug.isDebugBuild then
		local appID = "*********" 
		local appKey = "17cfacf88f498f61726bd4d859c2c133"
		local ServerInfo = ""
		local Language = "0"

		local user_vo = GameVoManager.Instance:GetUserVo()
		if user_vo then
			local account_user_id = user_vo.account_user_id or ""
			AudioGVoice.InitFeesVoice(appID, appKey, account_user_id, ServerInfo, Language, function (is_succeed, open_id, str)
				if is_succeed then
					print_log("init_fees_voice_succeed", open_id)
				else
					print_log("init_fees_voice_failed", open_id, str)
				end
			end)
		end
	end
end

function AudioService:PlayFeesAudio(file_id, call_back)
	if call_back then
		call_back(true)
	end
	AudioService.Instance:SetMasterVolume(0.0)

	AudioGVoice.StopPlay()
	AudioGVoice.StartPlay(file_id, function (is_succeed, param1, param2)
		-- 不管播放成功不成功，回调了就恢复
		AudioService.Instance:SetMasterVolume(1.0)
		if call_back then
			call_back(false)
		end
	end)
end