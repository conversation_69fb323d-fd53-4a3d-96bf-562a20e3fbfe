-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

GameObject = UnityEngine.GameObject
MainCamera = UnityEngine.Camera.main
_ = I18N.GetString
IS_AUDIT_VERSION = false
IS_FREE_VERSION = false
IS_CHECK_FUNTION_COST_TIME = false		-- 是否开启函数时间检查
IS_CHECK_FUNTION_COST_MEM = false		-- 是否开启函数内存检查
IS_CHECK_OBSOLETE = false				-- 是否开启废弃接口检查

CAMERA_TYPE = -1 				-- 摄像机视角模式 0-固定视角 1-自由视角

JUST_BACK_FROM_CROSS_SERVER = false				-- 从跨服回来时的处理标记
FIGHTSTATE_CAMERA = true		-- 战斗状态的时候摄像机缓慢调节到最佳状态
IS_FEES_VOICE = false					-- 是否使用收费语音录制(语音翻译转文字)
MIANUI_VIEW_EDITOR_FLAG = false			-- 给投放用的，在不同的标记，屏蔽不同的主界面UI，限制界面开启
IS_DEBUG_BUILD = UnityEngine.Debug.isDebugBuild		--是否是DEBUG版，可以过滤过SDK相关的处理

IS_LOCLA_WINDOWS_DEBUG_EXE = false					--是否是本地的windows包调试
LOCLA_WINDOWS_DEBUG_LIMIT_SPEED = nil 				-- 本地的windows调式包的网络速度
IS_LOCAL_WINDOWS_DEBUG_CHECK = false				-- 是否开启本地的windows调式包的检查(将会影响性能)
if RuntimeGUIMgr then
	IS_LOCLA_WINDOWS_DEBUG_EXE = RuntimeGUIMgr.Instance:IsGUIOpening()
	if IS_LOCLA_WINDOWS_DEBUG_EXE then
		LOCLA_WINDOWS_DEBUG_LIMIT_SPEED = RuntimeGUIMgr.Instance:GetGameSpeed()
		IS_LOCAL_WINDOWS_DEBUG_CHECK = UnityEngine.PlayerPrefs.GetInt("a3_fanli_is_local_windows_debug_check") == 1
	end
end

IS_OPEN_TOLUA_PROFILE = false	-- 是否开启toluaProfile
if not (UNITY_EDITOR or IS_LOCLA_WINDOWS_DEBUG_EXE) then
	IS_OPEN_TOLUA_PROFILE = false
end

QUICK_ADDITEM = false

CLIENT_DEBUG_LOG_STATE = false 						-- 客户端日志输出标记

CTRL_STATE = {
	START = 0,
	UPDATE = 1,
	finish = 2,
	NONE = 3,
}

socket = require("socket")
mime = require("mime")
cjson = require("cjson.safe")
require("init/http_client")
require("init/php_handle")
require("systool/baseclass")
require("systool/u3dobj")
require("systool/bindtool")
require("lib/resmanager/load_util")

local quick_login = require("editor/quick_login")
local quick_restart = require("editor/quick_restart")
local develop_mode = require("editor/develop_mode")

local ctrl_list = {}
local resource_map = nil

function Sleep(n)
	socket.select(nil, nil, n)
end

IsLowMemSystem = UnityEngine.SystemInfo.systemMemorySize <= 1500
GAME_FPS = 60
G_SceneObjLayer = GameObject.Find("GameRoot/SceneObjLayer").transform
G_UIObjLayer = GameObject.Find("GameRoot/UIObjLayer").transform
G_EffectLayer = GameObject.Find("GameRoot/EffectLayer").transform
IsGameStop = false

local is_wait_restart = false
local has_restarted = false
local restart_timer = 0

local function DoRestart()
	if false == has_restarted then
		has_restarted = true
		GameRoot.Instance:Restart()
	end
end

function GameStart()
	IsGameStop = false
    is_wait_restart = false
    has_restarted = false
    restart_timer = 0

	if UnityEngine.Application.platform == UnityEngine.RuntimePlatform.WindowsPlayer or
		UnityEngine.Application.platform == UnityEngine.RuntimePlatform.WindowsEditor then
		GAME_FPS = 60
	end

	UnityEngine.Application.targetFrameRate = GAME_FPS
	UnityEngine.Shader.globalMaximumLOD = 200

	UnityEngine.Screen.sleepTimeout =
		UnityEngine.SleepTimeout.NeverSleep

	if quick_login:IsOpenQuick() then
		quick_login:Start()
		return
	end

	PushCtrl(require("init/init_wg_ctrl"))
end

local is_editor = UnityEngine.Application.isEditor
function GameUpdate()
	local time = UnityEngine.Time.unscaledTime
	GlobalUnityTime = time



	local delta_time = UnityEngine.Time.unscaledDeltaTime
	if IsGameStop then
		if restart_timer > 0 then
			restart_timer = restart_timer - delta_time
			if restart_timer <= 0 then
				print_error("重启超时，强制重启")
				DoRestart()
			end
		end
		return
	end

	resource_map = nil

	if nil ~= CheckFuntionUseTime then
		CheckFuntionUseTime:NewFrame(time, delta_time)
	end

	for k, v in pairs(ctrl_list) do
		v:Update(time, delta_time)
	end

	-- GameRestart可能由ctrl_list里的updat触发。所以这里再判断IsGameStop
	-- 游戏停止后，以下Update不执行
	if not IsGameStop then
		quick_login:Update(time, delta_time)
		if ResPoolMgr then
			ResPoolMgr:Update(time, delta_time)
		end

		if nil ~= BundleCache then
			BundleCache:Update(time, delta_time)
		end

		if ResMgr and (UNITY_EDITOR or ResMgr:GetDownloadingURL()) then
			ResMgr:Update(time, delta_time)
		end

		if nil ~= AttributePool then
			AttributePool.Update(time, delta_time)
		end

		if nil ~= u3dpool then
			u3dpool.Update(time, delta_time)
		end

		if nil ~= TablePool then
			TablePool.Update(time, delta_time)
		end

		if nil ~= Vector3Pool then
			Vector3Pool.Update(time, delta_time)
		end

		if nil ~= LuaGC then
			LuaGC.Update(time, delta_time)
		end

		if nil ~= __GameObjLoader then
			__GameObjLoader._Update(time, delta_time)
		end

		develop_mode:Update(time, delta_time)
	end
end

function GameOnDestroy()
	if nil ~= ResPoolMgr then
		ResPoolMgr:OnGameStop()
		ResPoolMgr = nil
	end

	if nil ~= AudioManager then
		AudioManager:OnGameStop()
		AudioManager = nil
	end

	if nil ~= AssetBundleMgr then
		AssetBundleMgr:OnGameStop()
		AssetBundleMgr = nil
	end

	if nil ~= BundleCache then
		BundleCache:OnGameStop()
		BundleCache = nil
	end

	ResMgr = nil
end

function GameStop()
	if IsGameStop then
		return
	end
	IsGameStop = true

	for k, v in pairs(ctrl_list) do
		if v.Stop then
			v:Stop()
		end
	end

	quick_login:Stop()
	develop_mode:OnGameStop()

	if nil ~= DrawObj then
		DrawObj.OnGameStop()
	end

	if nil ~= ResPoolMgr then
		ResPoolMgr:OnGameStop()
		ResPoolMgr = nil
	end

	if nil ~= AudioManager then
		AudioManager:OnGameStop()
		AudioManager = nil
	end

	if nil ~= AssetBundleMgr then
		AssetBundleMgr:OnGameStop()
		AssetBundleMgr = nil
	end

	if nil ~= BundleCache then
		BundleCache:OnGameStop()
		BundleCache = nil
	end

	DownloadBufferMgr.OnGameStop()
end

local gamePaused = false;
function GameFocus(hasFocus)
	gamePaused = not hasFocus

	if nil ~= GlobalEventSystem then
		GlobalEventSystem:Fire(SystemEventType.GAME_FOCUS, hasFocus)
	end
end

function GamePause(pauseStatus)
	gamePaused = pauseStatus

	if nil ~= GlobalEventSystem then
		GlobalEventSystem:Fire(SystemEventType.GAME_PAUSE, pauseStatus)
	end

	if nil ~= LoginWGCtrl and LoginWGCtrl.Instance ~= nil then
		LoginWGCtrl.Instance:OnGamePause(pauseStatus)
	end
end

function ExecuteGm(gm)
	quick_login:ExecuteGm(gm)
end

function CheckMemoryLeak()
	if not ResUtil.memory_debug then
		print_error("memory debug no enabled")
		return
	end

	ResUtil.CheckMemoryLeak()
end

function GetDebugTrackback()
	return debug.traceback()
end

local main_role_level = 0
function GetMainRoleLevel()
	if Scene and Scene.Instance then
		local main_role = Scene.Instance:GetMainRole()
		if main_role then
			main_role_level = main_role.vo.level
		end
	end

	return main_role_level
end

function ExecuteHotUpdate(lua_name)
	print("[ExecuteHotUpdate]", lua_name)
	_G.package.loaded[lua_name] = nil
	require(lua_name)
end

function ExecuteQuickRestart(reload_files)
	quick_restart:Restart(reload_files)
end

function Collectgarbage(param)
	return collectgarbage(param) or -1
end

function PushCtrl(ctrl)
	ctrl_list[ctrl] = ctrl
end

function PopCtrl(ctrl)
	ctrl_list[ctrl] = nil
end

function EnableGameObjAttachEvent(list)
	if not IsGameStop then
		GameObjAttachEventHandle.EnableGameObjAttachEvent(list)
	end
end

function DisableGameObjAttachEvent(list)
	if not IsGameStop then
		GameObjAttachEventHandle.DisableGameObjAttachEvent(list)
	end
end

function DestroyGameObjAttachEvent(list)
	if not IsGameStop then
		GameObjAttachEventHandle.DestroyGameObjAttachEvent(list)
	end
end

function EnableLoadRawImageEvent(list)
	if not IsGameStop then
		LoadRawImageEventhandle.EnableLoadRawImageEvent(list)
	end
end

function DisableLoadRawImageEvent(list)
	if not IsGameStop then
		LoadRawImageEventhandle.DisableLoadRawImageEvent(list)
	end
end

function DestroyLoadRawImageEvent(list)
	if not IsGameStop then
		LoadRawImageEventhandle.DestroyLoadRawImageEvent(list)
	end
end

function ProjectileSingleEffectEvent(hit_effect, position, rotation, hit_effect_with_rotation, source_scale, layer)
	if not IsGameStop then
		EffectEventHandle.ProjectileSingleEffectEvent(hit_effect, position, rotation, hit_effect_with_rotation, source_scale, layer)
	end
end

function UIMouseClickEffectEvent(effectInstance, effects, canvas, mouse_click_transform)
	if not IsGameStop then
		EffectEventHandle.UIMouseClickEffectEvent(effectInstance, effects, canvas, mouse_click_transform)
	end
end

function PlayAudio(bundle_name, asset_name)
	if not IsGameStop then
		AudioManager.PlayAndForget(bundle_name, asset_name)
	end
end

-- 打印错误信息
function __TRACKBACK__(errmsg)
    local track_text = debug.traceback(tostring(errmsg));
    print_error(track_text, "LUA ERROR");
    return false;
end

--[[ 尝试调一个function 这个function可以带可变参数
如果被调用的函数有异常 返回false，退出此方法继续执行其他代码并打印出异常信息；]]
function Trycall(func, p1, p2, p3)
    return xpcall(func, __TRACKBACK__, p1, p2, p3);
end

local hide_main_ui = false
function HideMainUI()
	hide_main_ui = not hide_main_ui
	if MainuiWGCtrl.Instance then
		MainuiWGCtrl.Instance.view:SetActive(not hide_main_ui)
	end
end

-- 限制屏幕分辨率的尺寸.
local orginal_screen_width = 0
local orginal_screen_height = 0
function LimitScreenResolution(limit)
	local screen = UnityEngine.Screen
	if 0 == orginal_screen_width then
		orginal_screen_width = screen.width
	end

	if 0 == orginal_screen_height then
		orginal_screen_height = screen.height
	end

	if orginal_screen_width <= 0 or orginal_screen_height <= 0 then
		return
	end

	local isFullscreen = UnityEngine.Application.platform ~= UnityEngine.RuntimePlatform.WindowsPlayer
	if orginal_screen_width > orginal_screen_height then
		if orginal_screen_height > limit then
			local radio = orginal_screen_width / orginal_screen_height
			screen.SetResolution(math.floor(limit * radio), limit, isFullscreen)
		else
			screen.SetResolution(orginal_screen_width, orginal_screen_height, isFullscreen)
		end
	else
		if orginal_screen_width > limit then
			local radio = orginal_screen_width / orginal_screen_height
            screen.SetResolution(limit, math.floor(limit * radio), isFullscreen)
		else
			screen.SetResolution(orginal_screen_width, orginal_screen_height, isFullscreen)
		end
	end
end

function GameRestart()
	if is_wait_restart then
		return
	end
	is_wait_restart = true

	-- 防止花屏
	local ui_camera = GameObject.Find("Loading/UICameraLoading")
	if nil ~= ui_camera then
		local camera = ui_camera:GetComponent(typeof(UnityEngine.Camera))
		if nil ~= camera then
			camera.enabled = true
		end
	end

	-- 删除旧的LoadingView，防止出现进度条资源丢失
	local view_obj = GameObject.Find("Loading/UILayer/LoadingView")
	if view_obj ~= nil then
		ResMgr:Destroy(view_obj)
	end

	local assetbundle_mgr = AssetBundleMgr
	if assetbundle_mgr:NeedWaitAssetBundleUnLoad() then
		Trycall(function()
			GameStop()
		end)

		assetbundle_mgr:WaitAssetBundleUnLoad(function()
			DoRestart()
		end)

		-- 3秒之后强制重启
		restart_timer = 3
	else
		DoRestart()
	end
end

function UnRequire(lua_name)
	lua_name = string.gsub(lua_name, "/", "%.")
	_G.package.loaded[lua_name] = nil
end

local prefab_to_bundle_map = {}
local gameobj_to_prefab_map = {}
function GetAllResources()
	resource_map = {res_loaders = {}, gameobj_loaders = {}}
	local res_loaders = CheckClassCount:GetAllObjByClassType(__ResLoader)
	if res_loaders then
		for res_loader, _ in pairs(res_loaders) do
			if res_loader.cur_t and res_loader.cur_t[5] then
				local instance_id = res_loader.cur_t[5]:GetInstanceID()
				if nil == resource_map.res_loaders[instance_id] then
					resource_map.res_loaders[instance_id] = {}
				end

				table.insert(resource_map.res_loaders[instance_id], res_loader)
			end
		end
	end

	local gameobj_loaders = CheckClassCount:GetAllObjByClassType(__GameObjLoader)
	if gameobj_loaders then
		for gameobj_loader, _ in pairs(gameobj_loaders) do
			local go = gameobj_loader:GetGameObj()
			if go then
				local instance_id = go:GetInstanceID()
				local prefab = ResMgr:GetPrefab(instance_id)

				if prefab then
					local prefab_instanceid = prefab:GetInstanceID()
					if nil == resource_map.gameobj_loaders[prefab_instanceid] then
						resource_map.gameobj_loaders[prefab_instanceid] = {}
					end

					table.insert(resource_map.gameobj_loaders[prefab_instanceid], gameobj_loader)
				end
			end
		end
	end

	local all_res_list = ResPoolMgr:GetAllResInstanceID()
	local index = 0
	local res_list = {}
	for bundle_name, list in pairs(all_res_list) do
		for k, instance_id in pairs(list) do
			res_list[index] = instance_id
			index = index + 1

			prefab_to_bundle_map[instance_id] = bundle_name
		end
	end

	return res_list
end

function GetAllGameObject()
	local go_list = ResMgr:GetAllGameObjectInstanceID()
	for k, instance_id in pairs(go_list) do
		local prefab = ResMgr:GetPrefab(instance_id)
		local prefab_instanceid = prefab:GetInstanceID()
		gameobj_to_prefab_map[instance_id] = prefab_instanceid
	end

	return go_list
end

function GetResLoaderTraceBack(instance_id)
	if nil == resource_map then
		return
	end

	instance_id = gameobj_to_prefab_map[instance_id] or instance_id

	local tbl = {}
	local bundle_name = prefab_to_bundle_map[instance_id]
	local ref_count, asset_name, asset_count = ResPoolMgr:GetResRefCount(bundle_name, instance_id)

	if nil == asset_name then
		table.insert(tbl, "ResPool is Disposed\n")
	elseif ref_count > 0 then
		table.insert(tbl, string.format("[asset_name]%s Ref Count:%s\n", asset_name, ref_count))
	else
		local other_assets = ResPoolMgr:GetAllResInfoInResPool(bundle_name)
		if other_assets then
			table.insert(tbl, string.format("[asset_name]%s is waiting despose\nResPoolAssetCount:%s\n OtherAssets:\n", asset_name, asset_count))
			for k,v in pairs(other_assets) do
				table.insert(tbl, string.format("\t[asset_name]%s Ref Count:%s\n", k, v))
			end
		else
			table.insert(tbl, string.format("ResPool is waiting despose\n"))
		end
	end

	local index = 1
	local res_loaders = resource_map.res_loaders[instance_id]
	if res_loaders then
		for _, res_loader in pairs(res_loaders) do
			table.insert(tbl, string.format("[%s]%s\n", index, CheckClassCount:GetTraceBackByObj(res_loader)))
			index = index + 1
		end
	end

	local gameobj_loaders = resource_map.gameobj_loaders[instance_id]
	if gameobj_loaders then
		for _, gameobj_loader in pairs(gameobj_loaders) do
			table.insert(tbl, string.format("[%s]%s\n", index, CheckClassCount:GetTraceBackByObj(gameobj_loader)))
			index = index + 1
		end
	end

	local bundle_ref, bundle_ref_details = BundleCache:GetBundleRef(bundle_name)
	if bundle_ref > 1 then
		table.insert(tbl, string.format("[bundle_name]%s Ref:%s BundleRefDetails:\n%s\n", bundle_name, bundle_ref, bundle_ref_details))
	elseif bundle_ref == 1 then
		table.insert(tbl, string.format("[bundle_name]%s RefBySelf \n", bundle_name))
	else
		table.insert(tbl, string.format("%s is Disposed\n", bundle_name))
	end

	return table.concat(tbl)
end

function GetAssetBundleLeak()
	local tbl = {}
	table.insert(tbl, BundleCache:CheckAsetBundleLeak())
	table.insert(tbl, BundleCache:CheckAsetBundleDetailLeak())
	table.insert(tbl, ResPoolMgr:CheckLeak())
	return tbl
end

-- if GAME_ASSETBUNDLE and not IS_LOCLA_WINDOWS_DEBUG_EXE then
	-- print_log = function() end
	-- print = function() end

	-- local real_print_error = print_error
	-- local error_log_cache = {}
	-- print_error = function( ... )
	-- 	local param = {...}
	-- 	if #param <= 0 then
	-- 		return
	-- 	end

	-- 	local key = param[1]
	-- 	if nil == error_log_cache[key] then
	-- 		error_log_cache[key] = 0
	-- 	end

	-- 	if error_log_cache[key] > 100 then
	-- 		return
	-- 	end

	-- 	error_log_cache[key] = error_log_cache[key] + 1
	-- 	if 1 == error_log_cache[key] then -- 确保输出堆栈只有一次，防止因此导致性能开销
	-- 		real_print_error(error_log_cache[key], debug.traceback(), ...)
	-- 	else
	-- 		real_print_error(error_log_cache[key], ...)
	-- 	end
	-- end
-- end

math.randomseed(os.time())
GameStart()
