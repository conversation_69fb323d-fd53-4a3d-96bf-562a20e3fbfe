GuildVIPRedTips = GuildVIPRedTips or BaseClass(SafeBaseView)

function GuildVIPRedTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_vipred_tips")
end

function GuildVIPRedTips:__delete()
end

function GuildVIPRedTips:ReleaseCallBack()
	if nil ~= self.red_num then
		self.red_num:DeleteMe()
		self.red_num = nil
	end
	if nil ~= self.red_money then
		self.red_money:DeleteMe()
		self.red_money = nil
	end
	self.red_minnum = nil
	self.red_minmoney = nil
	--self.input_name = nil
end

function GuildVIPRedTips:LoadCallBack()
	self.node_list.btn_money_add.button:AddClickListener(BindTool.Bind(self.ClickAddMoney, self)) --增加
	self.node_list.btn_num_reduce.button:AddClickListener(BindTool.Bind(self.ClickReduceNum, self))--减少
	self.node_list.btn_send.button:AddClickListener(BindTool.Bind(self.OnClickSendRedBag, self)) --发送
	-- self.node_list.red_paper_world.button:AddClickListener(BindTool.Bind(self.OnClickWorldPaper, self))--世界红包
	-- self.node_list.red_paper_guild.button:AddClickListener(BindTool.Bind(self.OnClickGuildPaper, self))--仙盟红包
	self.node_list.flush_text_btn.button:AddClickListener(BindTool.Bind(self.OnClickFlushTextBtn, self))

	self.node_list.king_add_num.button:AddClickListener(BindTool.Bind(self.ClickAddNum, self,1)) --数量增加
	self.node_list.king_reduce_num.button:AddClickListener(BindTool.Bind(self.ClickAddNum, self,2))--数量减少

	
	
	self.temp_input = ""
	--self.input_name = self.node_list["lbl_red_money_input"]
	-- self.input_name.input_field.text = Language.Guild.GuildRedPackageDesc
	-- self.input_name.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputChange, self))
	self.node_list["input_text"].text.text = WelfareWGData.Instance:GetRandamRedpaperDec()
	self.node_list.text_title.text.text = Language.Guild.HongBaoType
	self.node_list.consume_gold.text.text = Language.Guild.HongBaoConsumeGold
end

-- function GuildVIPRedTips:OnInputChange()
-- 	local role_name = self.input_name.input_field.text
-- 	if role_name == "" then
-- 		return
-- 	end
-- 	--1-6个字一个汉字相当于两个字母
-- 	if string.len(role_name) > 27 then
-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent1)
-- 		self.input_name.input_field.text = self.temp_input
-- 		return
-- 	end
-- 	if ChatFilter.IsEmoji(role_name) then
-- 		self.input_name.input_field.text = self.temp_input
-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.IllegalContent)
-- 		return
-- 	end
-- 	self.temp_input = self.input_name.input_field.text
-- end

function GuildVIPRedTips:ShowIndexCallBack()
	self.node_list["king_select_panel"]:SetActive(false)
	self.node_list["normal_select_panel"]:SetActive(true)
	self.node_list["flush_text_btn"]:SetActive(true)
	if self.is_guid_paper then
		self.node_list.paper_title_name.text.text = Language.Welfare.GuildPaper
	else
		if self.small_type == 2 then
			self.node_list["king_select_panel"]:SetActive(true)
			self.node_list["normal_select_panel"]:SetActive(false)
			self.node_list["flush_text_btn"]:SetActive(false)
			self.node_list.paper_title_name.text.text = Language.Welfare.KingPaper
		else
			self.node_list.paper_title_name.text.text = Language.Welfare.WorldPaper
		end
	end
	self:Flush()
end

function GuildVIPRedTips:OnClickWorldPaper()
	-- self.node_list.world_btn_select:SetActive(true)
	-- self.node_list.guild_btn_select:SetActive(false)
	if self.small_type == 2 then
		self.node_list.paper_title_name.text.text = Language.Welfare.KingPaper
	else
		self.node_list.paper_title_name.text.text = Language.Welfare.WorldPaper
	end
	self.is_guid_paper = false
end

function GuildVIPRedTips:OnClickGuildPaper()
	-- self.node_list.world_btn_select:SetActive(false)
	-- self.node_list.guild_btn_select:SetActive(true)
	self.node_list.paper_title_name.text.text = Language.Welfare.GuildPaper
	self.is_guid_paper = true
end

function GuildVIPRedTips:OnNumCallBack(num)
	if num < self.num then num = self.num end
	self.red_minnum = num
	self.node_list.lbl_red_maxnum.text.text = self.red_minnum
	if self.red_minmoney < self.red_minnum then
		self.red_minmoney = self.red_minnum
		self.node_list.lbl_red_maxmoney.text.text = self.red_minmoney
		self.node_list.lbl_red_money.text.text = self.red_minmoney
	end
	-- self.node_list.red_shengyu_num.text.text = 
end

function GuildVIPRedTips:OnFlush()
	--self.node_list.world_btn_select:SetActive(not self.is_guid_paper)
	--self.node_list.guild_btn_select:SetActive(self.is_guid_paper)

	if not self.is_guid_paper and self.small_type == 2 then
		self.node_list["hongbao_zhuanhua_des"].text.text = self.king_paper_cfg.bind_gold
		self.node_list["lingqu_hongbao_num"].text.text = self.red_paper_num
		self.node_list["king_red_num"].text.text = self.red_paper_num
		self.node_list["input_text"].text.text = self.king_paper_cfg.descript
		return
	end

	-- print_error("self.bind_gold_cfg",self.bind_gold_cfg)
	for k,v in pairs(self.bind_gold_cfg) do
		if v.seq == self.gold_paper_select_index then
			self.node_list.lbl_red_money.text.text = v.cost_gold
			self.node_list.hongbao_zhuanhua_des.text.text = v.bind_gold
			self.node_list.lingqu_hongbao_num.text.text = v.num
			break
		end
	end
end

function GuildVIPRedTips:OnMoneyCallBack(num)
	if num < self.red_minnum then num = self.red_minnum end
	self.red_minmoney = num
	self.node_list.lbl_red_maxmoney.text.text = self.red_minmoney
	self.node_list.lbl_red_money.text.text = self.red_minmoney
end

function GuildVIPRedTips:ClickReduceNum()
	if not self.is_guid_paper and self.small_type == 2 then
		return
	end

	if self.gold_paper_select_index > 0 then
		self.gold_paper_select_index = self.gold_paper_select_index - 1
		self:Flush()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedMinLevel)
	end
end

function GuildVIPRedTips:ClickAddMoney()
	if not self.is_guid_paper and self.small_type == 2 then
		return
	end
	local temp_select = self.gold_paper_select_index + 1
	if self.bind_gold_cfg[#self.bind_gold_cfg].seq >= temp_select then
		self.gold_paper_select_index = self.gold_paper_select_index + 1
		self:Flush()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedMaxLevel)
	end
end



function GuildVIPRedTips:ClickAddNum(value)
	local max_num = self.king_paper_cfg.num or 20
	local min_num = self.king_paper_cfg.min_num or 20
	if value == 1 then
		self.red_paper_num = self.red_paper_num + 1
		if self.red_paper_num > max_num then
			self.red_paper_num = max_num
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedMaxLevel)
		end
	elseif value == 2 then
		self.red_paper_num = self.red_paper_num - 1
		if self.red_paper_num < min_num then
			self.red_paper_num = min_num
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedMinLevel)
		end

	end
	self:Flush()
end



function GuildVIPRedTips:OnClickFlushTextBtn()
	self.node_list["input_text"].text.text = WelfareWGData.Instance:GetRandamRedpaperDec()
end

function GuildVIPRedTips:OnClickSendRedBag()
	if not self.is_guid_paper and self.small_type == 2 then
		self:Close()
		return
	end

	-- local str = self.input_name.input_field.text
	local str = self.node_list["input_text"].text.text
	--1-6个字一个汉字相当于两个字母
	-- if string.len(str) > 27 or string.len(str) < 1 then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Common.limitContent1)
	-- 	return
	-- end	
	local is_send_type = self.is_guid_paper and 1 or 0
	local param2 = self.gold_paper_select_index
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	local need_money = tonumber(self.node_list.lbl_red_money.text.text) 
	local num = 0
	num = main_role_vo.gold or 0

	if need_money > num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Shop.MoneyDes1)
		return
	end

	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE,is_send_type,param2,self.node_list["input_text"].text.text)
	local content = str .. "{openLink;119}"
	if self.is_guid_paper then
		local no_report = true --str == Language.Guild.GuildRedPackageDesc
		ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT, nil, nil, no_report)
	end
	self:Close()
end

function GuildVIPRedTips:SetVIPDataOpen(paper_type,small_type)
	self.is_guid_paper = paper_type == 1
	self.small_type = small_type or 1
	local data_base_cfg = WelfareWGData.Instance:GetWelfareCfg().custom_redpaper
	self.bind_gold_cfg = {}
	for k,v in pairs(data_base_cfg) do
		table.insert(self.bind_gold_cfg,v)
	end
	self.gold_paper_select_index = self.bind_gold_cfg[1].seq
	if not self.is_guid_paper and self.small_type == 2 then
		local all_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfg()

		self.king_paper_cfg = all_cfg[self.small_type][0] or {}
		if IsEmptyTable(self.king_paper_cfg) then
			print_error("no king paper_cfg")
			return
		end
		self.red_paper_num = self.king_paper_cfg.num or 50
	end
	self:Open()
end