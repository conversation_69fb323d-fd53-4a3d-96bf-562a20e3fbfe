OpenServerInvestRewardShowView = OpenServerInvestRewardShowView or BaseClass(SafeBaseView)

local INVEST_REWARD_NUM = 10 --奖励数量

function OpenServerInvestRewardShowView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/openserver_invest_ui_prefab", "openserver_invest_reward_show_view")
    self.view_layer = UiLayer.Pop
    self.cur_type = -1
end

function OpenServerInvestRewardShowView:LoadCallBack()
    self.invest_reward_list = {}
    for i = 1, INVEST_REWARD_NUM do
        self.invest_reward_list[i] = InvestRewardRender.New(self.node_list.invest_reward_list:FindObj("reward_" .. i))
    end

    if not self.base_reward_list then
        self.base_reward_list = AsyncListView.New(ItemCell, self.node_list.base_reward_list)
        self.base_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_active_invest, BindTool.Bind(self.OnClickBuyInvestBtn, self))
end

function OpenServerInvestRewardShowView:ReleaseCallBack()
    if self.invest_reward_list then
		for k, v in pairs(self.invest_reward_list) do
            v:DeleteMe()
		end

		self.invest_reward_list = nil
	end

    if self.base_reward_list then
        self.base_reward_list:DeleteMe()
        self.base_reward_list = nil
    end
end

function OpenServerInvestRewardShowView:SetType(type)
    self.cur_type = type
end

function OpenServerInvestRewardShowView:OnFlush()
    local type_cfg = OpenServerInvestWGData.Instance:GetInvestTypeCfgByType(self.cur_type)
    if IsEmptyTable(type_cfg) then
        return
    end

    local is_open = OpenServerInvestWGData.Instance:ActIsOpenByType(self.cur_type)
    local is_buy = OpenServerInvestWGData.Instance:GetInvestBuyFlagByType(self.cur_type)
    if not is_open or is_buy then
        self:Close()
        return
    end

    local invest_reward_list = SortTableKey(type_cfg.invest_reward_item)
    local lide_reward_list = SortTableKey(type_cfg.reward_item)
    self.base_reward_list:SetDataList(type_cfg.basic_reward_item)

    for i = 1, INVEST_REWARD_NUM do
        if i <= #invest_reward_list then
            if i <= #lide_reward_list then
                self.invest_reward_list[i]:SetIsLiDeReward(true)
            else
                self.invest_reward_list[i]:SetIsLiDeReward(false)
            end

            self.node_list.invest_reward_list:FindObj("reward_" .. i):SetActive(true)
            self.invest_reward_list[i]:SetData(invest_reward_list[i])
        else
            self.node_list.invest_reward_list:FindObj("reward_" .. i):SetActive(false)
        end
    end

    self.node_list.btn_active_invest_text.text.text = string.format(Language.OpenServerInvest.btn_text1, type_cfg.price)

    self.node_list.text_reward_desc.text.text = string.format(Language.OpenServerInvest.BuyRewardDesc, type_cfg.gold_num)

end

function OpenServerInvestRewardShowView:OnClickBuyInvestBtn()
    local type_cfg = OpenServerInvestWGData.Instance:GetInvestTypeCfgByType(self.cur_type)

    if IsEmptyTable(type_cfg) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(type_cfg.price, type_cfg.rmb_type, type_cfg.rmb_seq)
end

------------------------------------------InvestRewardRender-------------------------------------------
InvestRewardRender = InvestRewardRender or BaseClass(BaseRender)
function InvestRewardRender:__init()
    self.view:SetActive(true)
end

function InvestRewardRender:LoadCallBack()
    self.is_lide_reward = false

    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function InvestRewardRender:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function InvestRewardRender:SetIsLiDeReward(bool)
    self.is_lide_reward = bool
end

function InvestRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.reward_item:SetData(self.data)
    if self.is_lide_reward then
        self.reward_item:SetTopLeftFlag(true, nil, nil, Language.OpenServerInvest.reward_desc)
    else
        self.reward_item:SetTopLeftFlag(false)
    end
end