FengShenBangChallengeView = FengShenBangChallengeView or BaseClass(SafeBaseView)

function FengShenBangChallengeView:__init(view_name)
	self.view_name = "FengShenBangChallengeView"
	self.view_layer = UiLayer.Pop

	self:SetMaskBg(true, true)
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/fengshenbang_prefab", "layout_challenge_panel")
end

function FengShenBangChallengeView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
end

function FengShenBangChallengeView:ReleaseCallBack()
	if self.attr_obj_list then
		self.attr_obj_list:DeleteMe()
		self.attr_obj_list = nil
	end
	if self.boss_model then
		self.boss_model:DeleteMe()
		self.boss_model = nil
	end
end

function FengShenBangChallengeView:OpenCallBack()
	self.select_index = FengShenBangWGData.Instance:GetSelectIndex()
end

function FengShenBangChallengeView:OnFlush(param_t)
	self:RefreshView()
	self:OnClickPageBtn()
end

function FengShenBangChallengeView:InitParam()
	self.select_index = FengShenBangWGData.Instance:GetSelectIndex()
end

function FengShenBangChallengeView:InitPanel()
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(1280, 716)
	self.node_list.title_view_name.text.text = Language.FengShenBang.ChallengeTitle
	local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
	self.node_list.tips_label.text.text = string.format(Language.FengShenBang.ChallengeTips,
		other_info.dur_kill_boss_times or 0)
	self.attr_obj_list = AsyncListView.New(FengShenBangAttrItem, self.node_list.attr_list)
	self.boss_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}
	
	self.boss_model:SetRenderTexUI3DModel(display_data)

	-- self.boss_model:SetUI3DModel(self.node_list.model_root.transform, nil, 1, nil, MODEL_CAMERA_TYPE.BASE)
end

function FengShenBangChallengeView:InitListener()
	self.node_list.left_btn.button:AddClickListener(BindTool.Bind(self.OnClickPageBtn, self, -1))
	self.node_list.right_btn.button:AddClickListener(BindTool.Bind(self.OnClickPageBtn, self, 1))
	self.node_list.challenge_btn.button:AddClickListener(BindTool.Bind(self.OnClickChallengeBtn, self))
	self.node_list.cur_guard_label.button:AddClickListener(BindTool.Bind(self.OnClickGuardLabel, self))
end

function FengShenBangChallengeView:OnClickPageBtn(add_num)
	local index = self.select_index
	if add_num then
		index = index + add_num
	end
	self.node_list.left_btn:SetActive(index > 1)
	self.node_list.right_btn:SetActive(index < 5)
	self.select_index = index
	if add_num then
		self:RefreshView()
	end
end

function FengShenBangChallengeView:OnClickChallengeBtn()
	local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgData(self.select_index)
	if scene_cfg then
		FengShenBangWGCtrl.Instance:RequestFengShenBang(CS_OGA_GODS_RANK_TYPE.CS_OGA_GODS_RANK_TYPE_CHALLENGE,
			scene_cfg.scene_id)
	end
end

function FengShenBangChallengeView:OnClickGuardLabel()
	local role_uid_list = FengShenBangWGData.Instance:GetRankRoleUidList()
	local role_uid = role_uid_list and role_uid_list[self.select_index] or 0
	if role_uid > 0 then
		BrowseWGCtrl.Instance:ShowOtherRoleInfo(role_uid)
	end
	-- local gurad_info = FengShenBangWGData.Instance:GetRankRoleInfoByIndex(self.select_index)
	-- if gurad_info then
	-- 	BrowseWGCtrl.Instance:ReqRoleInfoCallBack(gurad_info)
	-- end
end

function FengShenBangChallengeView:RefreshView()
	self.node_list.boss_kill_time_label.text.text = ""
	self.node_list.guard_cap_label.text.text = ""
	self.node_list.cur_guard_label.text.text = ""

	self:RefreshTitle()
	self:RefreshBossName()
	self:RefreshBossModel()
	self:RefreshGuardInfo()
end

function FengShenBangChallengeView:RefreshBossName()
	local boss_info = FengShenBangWGData.Instance:GetBossInfo(self.select_index)
	if not boss_info then
		return
	end
	self.node_list.boss_cap_value.text.text = boss_info.capability or ""
	self.node_list.boss_name_label.text.text = string.format(Language.FengShenBang.BossName, boss_info.name,
		boss_info.level)
end

function FengShenBangChallengeView:RefreshBossModel()
	local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgData(self.select_index)
	local monster_cfg = scene_cfg and BossWGData.Instance:GetMonsterInfo(scene_cfg.boss_id)
	if not monster_cfg then
		return
	end
	local bundle, asset = ResPath.GetMonsterModel(monster_cfg.resid)
	self.boss_model:SetMainAsset(bundle, asset)
	self.boss_model:PlayMonsterAction()
end

function FengShenBangChallengeView:RefreshTitle()
	local title_info = FengShenBangWGData.Instance:GetTitleList(self.select_index)
	if not title_info then
		return
	end

	local bundle, asset = ResPath.GetTitleModel(title_info.title_id)
	self.node_list.title_img:ChangeAsset(bundle, asset)

	local titel_attr_list, titel_cap_value = FengShenBangWGData.Instance:GetTitleAttrList(title_info.title_id, true)
	self.attr_obj_list:SetDataList(titel_attr_list or {})
	self.node_list.title_cap_label.text.text = titel_cap_value or 0
end

function FengShenBangChallengeView:RefreshGuardInfo()
	local rank_info = FengShenBangWGData.Instance:GetActRankInfo()
	local role_uid_list = rank_info and rank_info.rank_role_uid_list or {}
	local gurad_info = FengShenBangWGData.Instance:GetRankRoleInfo(role_uid_list[self.select_index])
	if not gurad_info then
		self.node_list.cur_guard_label.text.text = Language.FengShenBang.ZanWu
		self.node_list.guard_cap_label.text.text = ""
		self.node_list.boss_kill_time_label.text.text = ""
		return
	end
	self.node_list.cur_guard_label.text.text = gurad_info.role_name or gurad_info.name
	self.node_list.guard_cap_label.text.text = gurad_info.capability or 0
	self.node_list.boss_kill_time_label.text.text = TimeUtil.FormatSecond(
	rank_info.kill_cost_time_list[self.select_index] or 0, 2)
end
