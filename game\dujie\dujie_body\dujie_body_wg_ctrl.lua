
DujieWGCtrl = DujieWGCtrl or BaseClass(BaseWGCtrl)
function DujieWGCtrl:InitBody()
    self.dujie_body_view = DujieBodyView.New(GuideModuleName.DujieBodyView)
    self.body_talent_active_result_view = BodyTalentActiveResultView.New()
    self.body_talent_show_num_view = BodyTalentShowNumView.New()
    self.body_talent_skill_view = BodyTalentSkillView.New()
    self.body_reset_tips_view = BodyResetTipsView.New()
    self.body_talent_skill_tips_view = BodyTalentSkillTipsView.New()
	
	self:RegisterBodyProtocols()
end

function DujieWGCtrl:DeleteBody()
	self:CleanTimerDelayShow()
	if self.dujie_body_view then
		self.dujie_body_view:DeleteMe()
		self.dujie_body_view = nil
	end

	if self.body_talent_active_result_view then
		self.body_talent_active_result_view:DeleteMe()
		self.body_talent_active_result_view = nil
	end

	if self.body_talent_show_num_view then
		self.body_talent_show_num_view:DeleteMe()
		self.body_talent_show_num_view = nil
	end

	if self.body_talent_skill_view then
		self.body_talent_skill_view:DeleteMe()
		self.body_talent_skill_view = nil
	end

	if self.body_reset_tips_view then
		self.body_reset_tips_view:DeleteMe()
		self.body_reset_tips_view = nil
	end

	if self.body_talent_skill_tips_view then
		self.body_talent_skill_tips_view:DeleteMe()
		self.body_talent_skill_tips_view = nil
	end
end

function DujieWGCtrl:RegisterBodyProtocols()
	-- 灵根基础信息
	self:RegisterProtocol(SCOrdealBodyBaseInfo, "OnSCOrdealBodyBaseInfo")
	-- 灵根全部信息更新
	self:RegisterProtocol(SCOrdealBodyInfo, "OnSCOrdealBodyInfo")
	-- 灵根指定信息更新
	self:RegisterProtocol(SCOrdealBodyUpdate, "OnSCOrdealBodyUpdate")
	-- 属性更新
	self:RegisterProtocol(SCOrdealBodyAttrUpdate, "OnSCOrdealBodyAttrUpdate")

	
end

-- 请求获取灵根消耗基础信息(消耗道具)
function DujieWGCtrl:GetBodyBaseInfo()
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_BODY_BASE_INFO)
end

-- 请求获取灵根信息
function DujieWGCtrl:GetBodyInfo()
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_BODY_INFO)
end

-- 激活灵根
function DujieWGCtrl:ActiveBody(body_seq)
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_ACTIVE_BODY, body_seq)
end

-- 激活天赋
function DujieWGCtrl:ActiveTalent(body_seq, seq)
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_ACTIVE_TALENT, body_seq, seq)
end

-- 天赋技能
function DujieWGCtrl:SelectTalentSkill(body_seq, seq, skill_seq)
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_CHOOSE_TALENT_SKILL,body_seq,seq,skill_seq)
end

-- 天赋重置
function DujieWGCtrl:TalentReset(body_seq)
	self:SendOrdealOperate(DUJIE_OPERATE_TYPE.ORDEAL_OPERATE_TYPE_RESET_TALENT,body_seq)
end



function DujieWGCtrl:FlushBodyView()
	if self.dujie_body_view:IsOpen() then
		self.dujie_body_view:Flush()
	end
end

function DujieWGCtrl:FlushBodyViewAutoSelect(talent_seq)
	if self.dujie_body_view:IsOpen() then
		self.dujie_body_view:Flush(0, "auto_select",{seq = talent_seq})
	end
	
end

function DujieWGCtrl:OpenDujieBodyView()
	if self.dujie_body_view then
		self.dujie_body_view:Open()
	end
end


function DujieWGCtrl:OpenBodyTalentActiveResultView(talent_cfg, new_info, old_info, old_score,is_need_dealy)
	if self.body_talent_active_result_view then
		local data = {}
		data.new_info = new_info
		data.old_info = old_info
		data.talent_cfg = talent_cfg
		data.old_score = old_score
		self.body_talent_active_result_view:SetData(data)

		if is_need_dealy then
			self:CleanTimerDelayShow()
			self.timer_delay_show = GlobalTimerQuest:AddDelayTimer(function ()
				if self.body_talent_active_result_view:IsOpen() then
					self.body_talent_active_result_view:Flush()
				else
					self.body_talent_active_result_view:Open()
				end
			end, 2)
		else
			if self.body_talent_active_result_view:IsOpen() then
				self.body_talent_active_result_view:Flush()
			else
				self.body_talent_active_result_view:Open()
			end

		end

		
	end
end

function DujieWGCtrl:CleanTimerDelayShow()
    if self.timer_delay_show then
        GlobalTimerQuest:CancelQuest(self.timer_delay_show)
        self.timer_delay_show = nil
    end
end

function DujieWGCtrl:OpenBodyTalentShowNumView(talent_seq)
	
	if self.body_talent_show_num_view then
		local num = self.data:GetNewTalentNum()
		if num > 0 then
			self.body_talent_show_num_view:SetData(talent_seq)
			self.body_talent_show_num_view:Open()
		end
	end
end

function DujieWGCtrl:OpenBodyTalentSkillView(talent_data)
	
	if self.body_talent_skill_view then
		-- local data = {}
		-- data.talent_cfg = talent_cfg
		-- data.info = talent_info
		self.body_talent_skill_view:SetData(talent_data)
		if self.body_talent_skill_view:IsOpen() then
			self.body_talent_skill_view:Flush()
		else
			self.body_talent_skill_view:Open()
		end
	end
end

function DujieWGCtrl:OpenBodyResetTipsView(data)
	if self.body_reset_tips_view then
		self.body_reset_tips_view:SetData(data)
		self.body_reset_tips_view:Open()
	end
end

function DujieWGCtrl:OpenBodyTalentSkillTipsView(data)
	
	if self.body_talent_skill_tips_view then
		self.body_talent_skill_tips_view:SetData(data)
		self.body_talent_skill_tips_view:Open()
	end
end





function DujieWGCtrl:OnSCOrdealBodyBaseInfo(protocol)
	self.data:SetAllOrdealBodyBaseInfo(protocol)

	if self.dujie_body_view:IsOpen() then
		self.dujie_body_view:Flush(0,"money_bar")
	end
	RemindManager.Instance:Fire(RemindName.Dujie_Body)
	self.view:Flush(0,"flush_remind")
end

function DujieWGCtrl:OnSCOrdealBodyInfo(protocol)
	self.data:SetAllOrdealBodyInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Dujie_Body)
	self.view:Flush(0,"flush_remind")
end


function DujieWGCtrl:OnSCOrdealBodyUpdate(protocol)
	local old_talent_info = nil
	local old_score = self.data:GetBodyScore(protocol.body_seq)
	-- 获取天赋点显示数量
	local old_show_num = self.data:GetTalentShowCountByBody(protocol.body_seq)
	-- 获取旧数据
	if protocol.talent_seq ~= -1 then
		local is_active = self.data:IsActiveTalent(protocol.body_seq,protocol.talent_seq)
		-- 判断是激活还是刷新
		if is_active then
			old_talent_info = self.data:GetTalentInfo(protocol.body_seq, protocol.talent_seq)
		end

	end

	self.data:SetOrdealBodyInfo(protocol)

	-- 获取天赋点显示数量
	local cur_show_num = self.data:GetTalentShowCountByBody(protocol.body_seq)
	local new_show_num = cur_show_num - old_show_num
	-- 设置新节点显示数量
	self.data:SetNewTalentNum(new_show_num, protocol.talent_seq)
	
	-- 是否是天赋相关
	if protocol.talent_seq ~= -1 then
		local talent_cfg = self.data:GetTalentCfgBySeq(protocol.body_seq, protocol.talent_seq)
		local new_talent_info = self.data:GetTalentInfo(protocol.body_seq, protocol.talent_seq)

		-- 技能天赋打开技能选择面板
		if talent_cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
			local data = {}
			data.cfg = talent_cfg
			data.info = new_talent_info
			self:OpenBodyTalentSkillView(data)
		else
			-- 其他打开激活结果面板
			if not old_talent_info then
				self:OpenBodyTalentActiveResultView(talent_cfg, new_talent_info, old_talent_info,old_score,true)
			else
				self:OpenBodyTalentActiveResultView(talent_cfg, new_talent_info, old_talent_info,old_score,false)
			end
			
		end
		self.dujie_body_view:Flush(0, "flush_talent")

		if not old_talent_info then
			if new_show_num == 0 then
				self.dujie_body_view:Flush(0, "auto_select",{seq = protocol.talent_seq})
			end
			self.dujie_body_view:Flush(0, "dianliang_effect",{seq = protocol.talent_seq})
		end
	else
		if protocol.single_body_item_info.active_count == 0 then
			self.dujie_body_view:Flush(0, "reset_body")
		else
			self.dujie_body_view:Flush(0, "flush_body")
		end
	end
	RemindManager.Instance:Fire(RemindName.Dujie_Body)
	self.view:Flush(0,"flush_remind")
end


function DujieWGCtrl:OnSCOrdealBodyAttrUpdate(protocol)
	self.data:SetOrdealBodyAttrInfo(protocol)
end