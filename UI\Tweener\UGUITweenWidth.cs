﻿using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("UGUI/Tween/UGUI Tween Width")]
[RequireComponent(typeof(RectTransform))]
public class UGUITweenWidth : UGUITweener 
{
	public float from = 0f;
	public float to = 0f;

	bool mCached = false;
	RectTransform mTrans;

	void Cache ()
	{
		mCached = true;
		mTrans = GetComponent<RectTransform>();
	}

	public float value
	{
		get
		{
			if (!mCached) Cache();
			if (mTrans != null) return mTrans.sizeDelta.x;
			return 0;
		}
		set
		{
			if (!mCached) Cache();
			if (mTrans != null) mTrans.sizeDelta = new Vector2(value, mTrans.sizeDelta.y);
		}
	}

	protected override void OnUpdate (float factor, bool isFinished) { value = Mathf.Lerp(from, to, factor); }

	static public UGUITweenWidth Begin (GameObject go, float duration, float width)
	{
		#if UNITY_EDITOR
		if (!Application.isPlaying) return null;
		#endif
		UGUITweenWidth comp = UGUITweener.Begin<UGUITweenWidth>(go, duration);
		comp.from = comp.value;
		comp.to = width;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}

	[ContextMenu("Set 'From' to current value")]
	public override void SetStartToCurrentValue () { from = value; }

	[ContextMenu("Set 'To' to current value")]
	public override void SetEndToCurrentValue () { to = value; }

	[ContextMenu("Assume value of 'From'")]
	void SetCurrentValueToStart () { value = from; }

	[ContextMenu("Assume value of 'To'")]
	void SetCurrentValueToEnd () { value = to; }
}
