
function QuanMinBeiZhanView:InitLongHunRankView()
	self:InitLongHunRankParam()
end

function QuanMinBeiZhanView:ReleaseLongHunRankView()
	if self.lhr_show_model then
		self.lhr_show_model:DeleteMe()
		self.lhr_show_model = nil
	end

	if self.lhr_longhun_rank_list then
		self.lhr_longhun_rank_list:DeleteMe()
		self.lhr_longhun_rank_list = nil
	end

	-- if self.node_list["lhr_img_longhun"] then
	-- 	UITween.KillMoveLoop(self.node_list["lhr_img_longhun"].gameObject)
	-- end
	
	CountDownManager.Instance:RemoveCountDown("quanminbeizhan_longhun_rank_count_down")
end

function QuanMinBeiZhanView:ShenLongHunRankIndexCallBack()
	--请求一次数据
	QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK, LONGHUN_CHONGBANG_OP_TYPE.TYPE_INFO)
end

function QuanMinBeiZhanView:InitLongHunRankParam()
	if not self.lhr_longhun_rank_list then
		self.lhr_longhun_rank_list = AsyncListView.New(LongHunRankItemRender, self.node_list["lhr_longhun_rank_list"])
	end

	-- --跳转龙魂
	-- XUI.AddClickEventListener(self.node_list["lhr_btn_longhun_view"], function()
	-- 	ViewManager.Instance:Open(GuideModuleName.LongHunView)
	-- end)
	-- --跳转天帝凌
	-- XUI.AddClickEventListener(self.node_list["lhr_btn_tiandiling_view"], function()
		
	-- 	local is_open, show_str = FunOpen.Instance:GetFunIsOpened("fubenpanel_bootybay", true)
	-- 	if is_open then
	-- 		FunOpen.Instance:OpenViewByName(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_bootybay)
	-- 	else
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(show_str)
	-- 	end
	-- end)

	-- --打开龙魂冲榜排行
	XUI.AddClickEventListener(self.node_list["lhr_btn_show_rank"], function()
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK, LONGHUN_CHONGBANG_OP_TYPE.TYPE_RANK)
		QuanMinBeiZhanWGCtrl.Instance:OpenLongHunRankLogView()
	end)

	--Tips
	-- self.node_list["lhr_up_des_1"].text.text = Language.QuanMinBeiZhan.LongHunRank_Act_Info_1
	self.node_list["qm_jiesuan_desc"].text.text = Language.QuanMinBeiZhan.LongHunRank_Act_Info_2
	-- local def_cfg = QuanMinBeiZhanWGData.Instance:GetActivityDefultCfg(TabIndex.quanmin_beizhan_longhun_rank)
	-- if def_cfg then
	-- 	self.node_list["lhr_tip_label"].text.text = def_cfg.rule_tip
	-- 	XUI.AddClickEventListener(self.node_list["lhr_btn_tip"], function()
	-- 		RuleTip.Instance:SetContent(def_cfg.rule_desc, def_cfg.active_name)
	-- 	end)
	-- end

	self:InitLongHunRankModelShow()
end

--设置模型,配置战力展示
function QuanMinBeiZhanView:InitLongHunRankModelShow()
	local other_cfg = QuanMinBeiZhanWGData.Instance:GetActivityThemeOtherCfg()
	if IsEmptyTable(other_cfg) then
		return
	end
	--策划不投放时装展示,则显示图片资源
	-- local pic_res = other_cfg[1].LHcb_pic_res
	-- if pic_res and pic_res ~= 0 and pic_res ~= "" then
	-- 	self.node_list["lhr_img_longhun"].image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath(pic_res))
	-- 	UITween.MoveLoop(self.node_list["lhr_img_longhun"].gameObject, u3dpool.vec2(-172, 45), u3dpool.vec2(-172, 67), 2)
	-- 	self.node_list["lhr_model_pos"]:SetActive(false)
	-- 	--self.node_list["lhr_power_root"]:SetActive(false)
	-- 	self.node_list["lhr_img_longhun"]:SetActive(true)
	-- 	return
	-- end
	
	self.node_list["lhr_model_pos"]:SetActive(true)
	--self.node_list["lhr_power_root"]:SetActive(true)
	--self.node_list["lhr_img_longhun"]:SetActive(false)
	if not self.lhr_show_model then
		self.lhr_show_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["lhr_model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.lhr_show_model:SetRenderTexUI3DModel(display_data)
		-- self.lhr_show_model:SetUI3DModel(self.node_list["lhr_model_pos"].transform, self.node_list["lhr_model_pos"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
	end

	--self.node_list["lhr_cap_value"].text.text = other_cfg[1].LHcb_cap
	local model_cfgs = Split(other_cfg[1].LHcb_model, '|')
	if not IsEmptyTable(model_cfgs) then
		--时装
		local role_model_cfg = Split(model_cfgs[1], ',')
		local show_role_model_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(tonumber(role_model_cfg[1]), tonumber(role_model_cfg[2]))
		local role_res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(show_role_model_cfg.resouce)
		-- print_error("FFF===== role_res_id", role_res_id)
	    self.lhr_show_model:SetRoleResid(role_res_id)
		--武器
		if model_cfgs[2] and model_cfgs[2] ~= "" then
			local weapon_model_cfg = Split(model_cfgs[2], ',')
			local show_weapon_model_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(tonumber(weapon_model_cfg[1]), tonumber(weapon_model_cfg[2]))
			local weapon_id = RoleWGData.GetFashionWeaponId(nil, nil, show_weapon_model_cfg.resouce)
			-- print_error("FFF===== weapon_id", weapon_id)
			self.lhr_show_model:SetWeaponResid(weapon_id)
		end
	end
end

function QuanMinBeiZhanView:InitLongHunRankCap()

end

function QuanMinBeiZhanView:InitLongHunRankBoxList()

end

--更新界面数据
function QuanMinBeiZhanView:FlushLongHunRankView()
	-- print_error("龙魂冲榜 --更新界面数据")
	local my_rank_info = QuanMinBeiZhanWGData.Instance:GetLongHunRankRoleInfo()
	local my_rank_num = my_rank_info.rank or 0
	local my_longhun_power = my_rank_info.zhanli or 0

	--我的排行
	self.node_list["lhr_text_my_ranking"].text.text = my_rank_num <= 0 and Language.QuanMinBeiZhan.LongHunRank_No_Rank or my_rank_num
	--我的战力
	self.node_list["lhr_text_my_power"].text.text = my_longhun_power
	--活动倒计时
	self:LongHunRankTimeCountDown()
	
	local data_list = QuanMinBeiZhanWGData.Instance:GetLongHunRankCfgList()
	--[[
	self.lhr_longhun_rank_list:SetDataList(data_list)
	local _,reward_id = QuanMinBeiZhanWGData.Instance:CheckLongHunRankRemind()
	if reward_id then
		local select_index = 1
		for i=1,#data_list do
			if data_list[i].id == reward_id then
				select_index = i
				break
			end
		end
		self.lhr_longhun_rank_list:JumpToIndex(select_index)
	end
	--]]
	---[[ 可领取放前面
	local temp_list = {}
	local is_finish = false
	local is_get = false
	for i=1,#data_list do
		is_finish = QuanMinBeiZhanWGData.Instance:GetLongHunRankJoinFlag(data_list[i].id)
		is_get = QuanMinBeiZhanWGData.Instance:GetLongHunRankJoinRewardFlag(data_list[i].id)
		if is_finish then
			if is_get then
				temp_list[200 + i] = data_list[i]
			else
				temp_list[i] = data_list[i]
			end
		else
			temp_list[100 + i] = data_list[i]
		end
	end
	temp_list = SortTableKey(temp_list)
	self.lhr_longhun_rank_list:SetDataList(temp_list)
	--]]
end

--活动倒计时
function QuanMinBeiZhanView:LongHunRankTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("quanminbeizhan_longhun_rank_count_down")
	local left_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_longhun_rank)
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	if left_time > now_time then
		self.node_list["lhr_text_left_time"].text.text = TimeUtil.FormatSecondDHM2(left_time - now_time)
		CountDownManager.Instance:AddCountDown("quanminbeizhan_longhun_rank_count_down", BindTool.Bind1(self.UpdateLongHunRankCountDown, self), BindTool.Bind1(self.LongHunRankTimeCountDown, self), left_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateLongHunRankCountDown(elapse_time, total_time)
	self.node_list["lhr_text_left_time"].text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

-------------------------------------------------------------------------------------------------
LongHunRankItemRender = LongHunRankItemRender or BaseClass(BaseRender)

function LongHunRankItemRender:LoadCallBack()
	self.save_mark = nil
	self.node_list["btn_get"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	self.reward_item_list = AsyncListView.New(ItemCell, self.node_list["reward_item_list"])
	self.reward_item_list:SetIsDelayFlush(false)
end

function LongHunRankItemRender:__delete()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function LongHunRankItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end 

	if self.save_mark ~= self:GetIndex() then
		--标题
		self.node_list["text_condition"].text.text = data.condition_desc
		local reward_list = SortTableKey(data.reward_item)
		--奖励列表
		self.reward_item_list:SetDataList(reward_list)
		self.save_mark = self:GetIndex()
	end
	self:FlushBtnState()
end

function LongHunRankItemRender:FlushBtnState()
	local data = self:GetData()
	local is_act_open = QuanMinBeiZhanWGData.Instance:GetActivityState(TabIndex.quanmin_beizhan_longhun_rank)
	local is_rank_item = data.rank and data.rank > 0
	local my_rank_info = QuanMinBeiZhanWGData.Instance:GetLongHunRankRoleInfo()

	self.node_list["rank_btn_root"]:SetActive(is_rank_item)
	self.node_list["reward_btn_root"]:SetActive(not is_rank_item)

	if is_rank_item then
		local is_my_rank_range = false
		local rank_range = Split(data.rank_range, '|')
		if not IsEmptyTable(rank_range) and not is_act_open then
			is_my_rank_range = my_rank_info.rank >= tonumber(rank_range[1]) and my_rank_info.rank <= tonumber(rank_range[2])
		end
		-- my_rank_info.zhanli--战力
		--已结算
		self.node_list["btn_state_red"]:SetActive(not is_act_open and not is_my_rank_range)
		--待结算/已发送
		self.node_list["btn_state_green"]:SetActive(is_act_open or (not is_act_open and is_my_rank_range))
		if self.node_list["btn_state_green"].gameObject.activeSelf then
			local show_str = is_act_open and Language.QuanMinBeiZhan.LongHunRank_Wait_Over or Language.QuanMinBeiZhan.LongHunRank_Is_Send
			self.node_list["text_state_green"].text.text = show_str
		end
	else
		--领取
		local is_finish = QuanMinBeiZhanWGData.Instance:GetLongHunRankJoinFlag(data.id)
		local is_get = QuanMinBeiZhanWGData.Instance:GetLongHunRankJoinRewardFlag(data.id)
		self.node_list["btn_get"]:SetActive(not is_get)
		if self.node_list["btn_get"].gameObject.activeSelf then
			self.node_list["btn_get"].button.enabled = is_finish
			self.node_list["red_point"]:SetActive(is_finish)
			XUI.SetGraphicGrey(self.node_list["btn_get"], not is_finish)
		end
		--已领取
		self.node_list["img_is_get"]:SetActive(is_get)
	end
end

--点击请求
function LongHunRankItemRender:OnClickRewardHnadler()
	if not self.data.id or self.data.id < 0 then return end
	local is_finish = QuanMinBeiZhanWGData.Instance:GetLongHunRankJoinFlag(self.data.id)
	local is_get = QuanMinBeiZhanWGData.Instance:GetLongHunRankJoinRewardFlag(self.data.id)
	-- print_error("FFFFF====== is_finish and not is_get", is_finish and not is_get, is_finish, not is_get)
	if is_finish and not is_get then
		-- print_error("=======点击请求")
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK, LONGHUN_CHONGBANG_OP_TYPE.TYPE_RANK_REWARD, self.data.id)
	end
end

