SwornWGData = SwornWGData or BaseClass()

-- 结义状态
SwornWGData.SWORN_TYPE = {
	NOSWORN = 0,      -- 未参与结义
	SWORNING = 1,     -- 匹配状态中
	HAS_SWORN = 2,    -- 已有队伍已结义
} 

SwornWGData.MAX_TAOYUAN_SKILL_NUM = 2

function SwornWGData:__init()
	if SwornWGData.Instance ~= nil then
		print_error("[SwornWGData] attempt to create singleton twice!")
		return
	end
	SwornWGData.Instance = self

    self.jieyi_cfg = ConfigManager.Instance:GetAutoConfig("jieyi_cfg_auto")
	self.other_cfg = self.jieyi_cfg.other[1]
	self.jinlan_level_cfg = ListToMap(self.jieyi_cfg.jinlan_level, "level")
	self.jinlan_level_item_cfg = ListToMap(self.jieyi_cfg.jinlan_level, "cost_item_id")
	self.jinlan_addition_cfg = ListToMap(self.jieyi_cfg.jinlan_addition, "role_num")
	self.OpenSworn_cfg = FunOpen.Instance:GetFunByName(FunName.SwornView)
	self.my_sworn_state = self.SWORN_TYPE.NOSWORN
	
	self.jinlan_level = 0
	self.jieyi_cd_time = 0
	self.team_count = 0
	self.apply_count = 0
	self.team_list = {}
	self.req_player_list = {}
	self.team_info = {}
	self.menber_uid_list = {}
	self:InitSuitData()
	self:InitBuildData()

	self.vote_time = 0
	self.vote_uid = 0
	self.be_vote_uid = 0
	self.vote_flag = {}
	self.my_jieyi_data = {}

	RemindManager.Instance:Register(RemindName.Sworn_Start, BindTool.Bind(self.GetSwornStartRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Apply, BindTool.Bind(self.GetSwornApplyRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Imprint, BindTool.Bind(self.GetSwornImprintRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Suit, BindTool.Bind(self.GetViewSelectSuitRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Upstar, BindTool.Bind(self.GetViewSelectUpStarRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Uplevel, BindTool.Bind(self.GetViewSelectUpLevelRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Equip, BindTool.Bind(self.GetEquipAllRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Build_Task, BindTool.Bind(self.GetSwornBuildTaskRemind, self))
	RemindManager.Instance:Register(RemindName.Sworn_Build_Taoyuan, BindTool.Bind(self.GetSwornBuildTaoYuanRemind, self))
end

function SwornWGData:__delete()
	SwornWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.Sworn_Start)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Apply)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Imprint)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Suit)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Upstar)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Uplevel)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Equip)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Build_Task)
	RemindManager.Instance:UnRegister(RemindName.Sworn_Build_Taoyuan)
end

----------------------------------------------------Remind_Start---------------------------------------------------
function SwornWGData:GetSwornStartRemind()
	return self:GetSwornRelieveRemind()
end

function SwornWGData:GetSwornApplyRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.SwornView) then
		return 0
	end

	if self:HadSworn() and self.apply_count and self.apply_count > 0 then
		return 1
	end

	return 0
end

function SwornWGData:GetSwornImprintRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.SwornView) then
		return 0
	end

	if IsEmptyTable(self:GetJinLanLevelCfg()) then
		return 0
	end

	local current_jin_lan_cfg = self.jinlan_level_cfg[self.jinlan_level]
    local is_max_level = self.jinlan_level_cfg[#self.jinlan_level_cfg].level == self.jinlan_level

	if is_max_level then
		return 0
	else
		local cost_item_id = current_jin_lan_cfg.cost_item_id
		local cost_item_num = current_jin_lan_cfg.cost_item_num
		local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
		local enough = has_num >= cost_item_num

		return enough and 1 or 0
	end
end

function SwornWGData:GetSwornRelieveRemind()
	if self.vote_time > 0 and self.vote_uid > 0 and self.be_vote_uid > 0 then
		local is_mine = RoleWGData.Instance:GetOriginUid() == self.be_vote_uid

		if is_mine then
			return 0
		else
			if not self:GetMyRelieveFlag() then
				return 1
			end
		end
	end

	return 0
end
----------------------------------------------------Remind_End-----------------------------------------------------

----------------------------------------------------Common_Set_Start-----------------------------------------------
function SwornWGData:SetJinLanLevel(protocol)
	self.jinlan_level = protocol.jinlan_level
	self.jieyi_cd_time = protocol.jieyi_cd_time
end

function SwornWGData:SetJieYiTeamInfo(protocol)
	self.team_info = protocol.team_info
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	-- 结义状态处理 self.team_info.jieyi_id > 0 and 
	if self.team_info.wait_time > server_time then
		self.my_sworn_state = SwornWGData.SWORN_TYPE.SWORNING
	elseif self.team_info.jieyi_id > 0 and self.team_info.wait_time <= server_time then
		self.my_sworn_state = SwornWGData.SWORN_TYPE.HAS_SWORN
	else
		self.my_sworn_state = SwornWGData.SWORN_TYPE.NOSWORN
	end

	if not IsEmptyTable(self.team_info.member_list) then
		local menber_uid_list = {}
		local my_uid = RoleWGData.Instance:GetOriginUid()
		for k, v in pairs(self.team_info.member_list) do
			if v.uid > 0 then
				menber_uid_list[v.uid] = v.uid
			end

			if v.uid == my_uid then
				self.my_jieyi_data = v
			end
		end
	
		self.menber_uid_list = menber_uid_list
	end
end

function SwornWGData:SetJieYiTeamList(protocol)
	self.team_count = protocol.count
	self.team_list = protocol.team_info_list
	self:ClearSwornApplyTeamCache()
end

function SwornWGData:SetJieYiTeamJoinReq(protocol)
	self.apply_count = protocol.count
	self.req_player_list = protocol.req_player_list
end

function SwornWGData:SetJieYiTeamVoteInfo(protocol)
	self.vote_time = protocol.vote_time
	self.vote_uid = protocol.vote_uid
	self.be_vote_uid = protocol.be_vote_uid
	self.vote_flag = bit:d2b_l2h(protocol.vote_flag, nil, true)
end
----------------------------------------------------Common_Set__End-------------------------------------------------

----------------------------------------------------Common_Get_Start-----------------------------------------------
function SwornWGData:GetSwornLevelLimit()
	return self.OpenSworn_cfg and self.OpenSworn_cfg.task_level or 0
end

function SwornWGData:GetMySwornState()
	return self.my_sworn_state
end

function SwornWGData:IsSwornNow()
	return self.my_sworn_state ~= self.SWORN_TYPE.NOSWORN
end

function SwornWGData:HadSworn()
	return self.my_sworn_state == self.SWORN_TYPE.HAS_SWORN
end

function SwornWGData:GetSwornOtherCfg()
	return self.other_cfg
end

function SwornWGData:GetSwornKeyTime()
	return self.other_cfg.jieyi_enter_time
end

function SwornWGData:GetSwornCdTime()
	return self.other_cfg.jieyi_cd_time / 60
end

function SwornWGData:GetMyTeamMenberInfo()
	local menber_data_list = {}
	local active_menber = 0

	if IsEmptyTable(self.team_info) or IsEmptyTable(self.team_info.member_list) then
		return active_menber, menber_data_list
	end

	local start_index = 1
	local end_index = #self.team_info.member_list

	for k, v in pairs(self.team_info.member_list) do
		if v.uid > 0 then
			menber_data_list[start_index] = v
			active_menber = active_menber + 1
			start_index = start_index + 1
		else
			menber_data_list[end_index] = v
			end_index = end_index - 1
		end
	end

	return active_menber, menber_data_list
end

function SwornWGData:GetSwornStartLimitTime()
	local server_time =  TimeWGCtrl.Instance:GetServerTime()
	return self.team_info.wait_time - server_time
end

function SwornWGData:GetJinLanLevel()
	return self.jinlan_level
end

function SwornWGData:GetJieYiCdTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local d_value = self.jieyi_cd_time - server_time
	local cd_time = d_value > 0 and d_value or 0
	return d_value > 0, cd_time
end

function SwornWGData:GetJinLanCap()
	if self.jinlan_level <= 0 then
		return 0
	end

	local capability = 0
	local attr_info, base_cap = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.jinlan_level_cfg[self.jinlan_level], "attr_id", "attr_value")
	local num = self:GetMyTeamMenberInfo()
	local addtion_cfg = self.jinlan_addition_cfg[num] or {}

	if not IsEmptyTable(addtion_cfg) then
		local addtion = addtion_cfg.addition / 10000 + 1
		base_cap = base_cap * addtion
	end
	capability = capability + base_cap

	return math.ceil(capability)
end

function SwornWGData:GetJinLanLevelCfg()
	return self.jinlan_level_cfg
end

function SwornWGData:GetCurrentJinLanLevelCfg()
	return self.jinlan_level_cfg[self.jinlan_level] or {}
end

function SwornWGData:GetCurrentJinLanAttr()
	local jinlan_level = self.jinlan_level > 0 and self.jinlan_level or 1
	return ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.jinlan_level_cfg[jinlan_level], "attr_id", "attr_value")
end

function SwornWGData:GetJieYiTeamInfo()
	return self.team_info
end

function SwornWGData:GetJinLanAddtionCfg()
	return self.jinlan_addition_cfg
end

function SwornWGData:GetJieYiTeamList()
	return self.team_count, self.team_list
end

function SwornWGData:GetJieYiTeamJoinReq()
	return self.apply_count, self.req_player_list
end

function SwornWGData:GetJieYiCostEnough()
	local cost_item_id = self.other_cfg.jieyi_item_id
    local cost_item_num = self.other_cfg.jieyi_item_num
    local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
	return cost_item_num <= has_num, cost_item_id, cost_item_num
end

function SwornWGData:CheckIsImporintItem(change_item_id)
	return not IsEmptyTable(self.jinlan_level_item_cfg[change_item_id])
end

function SwornWGData:CheckIsSwornStartItem(change_item_id)
	return change_item_id == self.other_cfg.jieyi_item_id and self.my_sworn_state == SwornWGData.SWORN_TYPE.NOSWORN
end

function SwornWGData:GetMyJieYiId()
	return self.team_info.jieyi_id or -1
end

function SwornWGData:GetMyJieYiMenberUidList()
	return self.menber_uid_list
end

function  SwornWGData:IsJieYiMengberByUid(uid)
	if IsEmptyTable(self.menber_uid_list) then
		return false
	end

	return nil ~= self.menber_uid_list[uid]
end

function SwornWGData:GetRelieveMemberData()
	local member_data_list = {}
	if IsEmptyTable(self.team_info) or IsEmptyTable(self.team_info.member_list) then
		return member_data_list
	end

	for k, v in pairs(self.team_info.member_list) do
		if v.uid > 0 then
			table.insert(member_data_list, v)
		end
	end

	return member_data_list
end

function SwornWGData:GetMyRelieveFlag()
	return self:GetRelieveFlag(self.my_jieyi_data.vote_index)
end

function SwornWGData:GetRelieveFlag(index)
	return self.vote_flag[index] == 1
end

function SwornWGData:GetJieYiTeamVoteInfo()
	return self.vote_time, self.vote_uid, self.be_vote_uid
end

function SwornWGData:GetJieyiBeVoteMemberInfo()
	local info = {}

	if IsEmptyTable(self.team_info) or IsEmptyTable(self.team_info.member_list) then
		return info
	end

	for k, v in pairs(self.team_info.member_list) do
		if v.uid > 0 and self.be_vote_uid > 0 and self.be_vote_uid == v.uid then
			info = v
			break
		end
	end

	return info
end
----------------------------------------------------Common_Get__End-------------------------------------------------

-------------------------------------------------------Cache_Start--------------------------------------------------
function SwornWGData:AddSwornApplyTeamCache(jieyi_id)
    if not self.sworn_apply_team_cache then
		self.sworn_apply_team_cache = {}
	end

	self.sworn_apply_team_cache[jieyi_id] = jieyi_id
	SwornWGCtrl.Instance:FlushSwornApply()
end

function SwornWGData:ClearSwornApplyTeamCache()
    self.sworn_apply_team_cache = nil
end

function SwornWGData:IsSwornApplyCacheTeam(jieyi_id)
	if IsEmptyTable(self.sworn_apply_team_cache) then
		return false
	end

	return nil ~= self.sworn_apply_team_cache[jieyi_id]
end

-- 缓存邀请人员的cd列表
function SwornWGData:AddCacheCDList(role_id, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end

    if CountDownManager.Instance:HasCountDown("sworn_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("sworn_invite_cd_time")
    end

    CountDownManager.Instance:AddCountDown("sworn_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 40 + TimeWGCtrl.Instance:GetServerTime())

    time = time or 30
    self.cache_invite_cd_list[role_id] = time
    SwornWGCtrl.Instance:FlushTextInvite()
end

function SwornWGData:GetCacheCDByRoleid(role_id)
    if not self.cache_invite_cd_list then
        return 0
    end

    return self.cache_invite_cd_list[role_id] or 0
end

function SwornWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("sworn_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("sworn_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
        BindTool.Bind(self.CheckInviteTime,self), 40 + TimeWGCtrl.Instance:GetServerTime())
    end
end

function SwornWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("sworn_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        self.cache_invite_cd_list[k] = v - 1

        if v <= 0 then
            table.remove(self.cache_invite_cd_list, k)
        end

        SwornWGCtrl.Instance:FlushTextInvite()
    end
end
-------------------------------------------------------Cache_End----------------------------------------------------

-- 家园界面结义按钮上的结义tips每日上线显示一次
function SwornWGData:IsShowHomeSwornBg()
	if self:HadSworn() then return false end
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "ShowHomeSwornBg")
	local remind_day = PlayerPrefsUtil.GetInt(key)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_day ~= remind_day then
		return true
	end

	return false
end

function SwornWGData:SetShowHomeSwornBg()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "ShowHomeSwornBg")
	PlayerPrefsUtil.SetInt(key, cur_day)
end