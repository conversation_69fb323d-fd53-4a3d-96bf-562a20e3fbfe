-- 暗器软甲详情
local left_cell_start_pos = Vector2(77, -343)
local left_cell_end_pos = Vector2(-580, -343)
local detail_bag_start_pos = Vector2(-580, -36)
local detail_bag_end_pos = Vector2(0, -36)

function HiddenWeaponView:LoadDetailIndexCallBack()
    self.hw_skill_node = {}
    self.curr_datas = {}
    self.cur_detail_item_data = {}
    self.show_detail_equip_btn = false
    self.detail_model_id = -1

    -- 暗器背包列表
    if not self.hw_bag_list then
        self.hw_bag_list = AsyncBaseGrid.New()
        self.hw_bag_list:SetStartZeroIndex(false)
        self.hw_bag_list:CreateCells({
            col = 2,
            cell_count = 18,
            list_view = self.node_list["hw_bag_list"],
            itemRender = HWDetailItemRender,
            assetBundle = "uis/view/shenji_anqiruanjia_ui_prefab",
            assetName = "box_res_bag",
            change_cells_num = 2,
        })
        self.hw_bag_list:SetSelectCallBack(BindTool.Bind(self.OnHWItemSelectChanged, self, false))
    end

    -- 筛选
    for index = 0, 3 do
        -- self.list_toggle_filter[index] = self.node_list["detail_filter_toggle_" .. index].toggle
        XUI.AddClickEventListener(
            self.node_list["detail_filter_toggle_" .. index],
            BindTool.Bind(self.OnDetailFilterChanged, self, index)
        )
    end
    -- 7个技能
    for index = 1, 7 do
        self.hw_skill_node[index] = HWDetailSkillRender.New(self.node_list["hw_detail_skill" .. index])
    end

    if not self.detail_model_display then
        self.detail_model_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["detail_model_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.detail_model_display:SetRenderTexUI3DModel(display_data)
        -- self.detail_model_display:SetUI3DModel(self.node_list["detail_model_display"].transform, self.node_list["detail_model_display"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    -- 专属技能
    self.hw_special_skill_node = HWDetailSpecialSkillRender.New(self.node_list["box_skill_special"])
    XUI.AddClickEventListener(self.node_list["btn_bailian"], BindTool.Bind(self.OnJumpBaiLian, self))
    XUI.AddClickEventListener(self.node_list["btn_fenjie"], BindTool.Bind(self.OnDetailFenJie, self))
    XUI.AddClickEventListener(self.node_list["detail_btn_equip"], BindTool.Bind1(self.EquipHWItem, self))
    XUI.AddClickEventListener(self.node_list["btn_detail_bag"], BindTool.Bind(self.OnClickDetailBag, self))
    XUI.AddClickEventListener(self.node_list["detail_back_tocell"], BindTool.Bind(self.OnClickDetailBackToCell, self))
    XUI.AddClickEventListener(self.node_list["btn_detail_select"], BindTool.Bind(self.OnClickDetailSelectBtn, self))
    XUI.AddClickEventListener(self.node_list["detail_bag_select_back"], BindTool.Bind(self.OnClickDetailBackSelect, self))
end

function HiddenWeaponView:ReleaseDetailCallBack()
    CancleAllDelayCall(self)
    if self.detail_model_display then
        self.detail_model_display:DeleteMe()
        self.detail_model_display = nil
    end

    if self.hw_special_skill_node then
        self.hw_special_skill_node:DeleteMe()
        self.hw_special_skill_node = nil
    end

    if self.hw_skill_node then
        for index, value in ipairs(self.hw_skill_node) do
            if value and value.DeleteMe then
                value:DeleteMe()
            end
        end
    end

    self.hw_skill_node = nil
    self.is_init_loaded = nil
    if self.hw_bag_list then
        self.hw_bag_list:DeleteMe()
    end
    self.hw_bag_list = nil

    -- self.list_toggle_filter = nil
    self.map_origin_datas = nil
    self.curr_datas = nil
    self.cur_detail_item_data = nil
    self.show_detail_equip_btn = nil
end


function HiddenWeaponView:RefreshDetailView()
    ReDelayCall(
        self,
        function()
            self:SetDetailUpDate()
        end,
        0.01,
        "HiddenWeaponDetailView_FILTER"
    )
    self:SetDetailupFilterName()

    local weapon_type = self:GetWeaponType()
    local remind_name = "ShenJiEquip_BetterEquip" .. weapon_type
    local remind = RemindManager.Instance:GetRemind(RemindName[remind_name])
    local hasDecompose = HiddenWeaponWGData.Instance:IsDataContainsDecompose()
    if self.node_list.btn_detail_bag_remind then
        self.node_list.btn_detail_bag_remind:CustomSetActive(remind > 0 or hasDecompose)
    end

    self:FlushHWDetailInfo()
end

function HiddenWeaponView:FlushHWDetailInfo(equip_data)
    local weapon_type = self:GetWeaponType()
    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)

    if data and data.equip then
        if not IsEmptyTable(equip_data) and
            (equip_data.item_id ~= data.item_id or equip_data.capability ~= data.capability) then
            data = equip_data

			--确保 替换装备操作时 数据的准确性.
			local cell_index = 1
			local data_list = self.hw_bag_list:GetDataList()
			for k,v in ipairs(data_list) do
				if data.index == v.index then
					cell_index = k
					data = v
					break
				end
			end
			self.hw_bag_list:SetSelectCellIndex(cell_index)
        end

        local select_data = {data = data}
        self:OnHWItemSelectChanged(true, select_data)
    end
end

function HiddenWeaponView:ShowDetailIndexCallBack()
    if self.hw_bag_list == nil then
        return
    end

    --self.node_list["detail_filter_toggle_1"].toggle.isOn = true
    self:RefreshDetailView()
    self:InitDetailBagPosition()
end

function HiddenWeaponView:OnDetailFenJie()
    local hasDecompose = HiddenWeaponWGData.Instance:IsDataContainsDecompose()
    if hasDecompose then
        HiddenWeaponRequest:ReqDecompose()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.NoFenJieTip)
    end
end

function HiddenWeaponView:OnJumpBaiLian()
    FunOpen.Instance:OpenViewByName(GuideModuleName.ShenJiTianCiView)
end

function HiddenWeaponView:OnClickDetailBag(is_change_index)
    if is_change_index ~= nil then
        self.show_detail_equip_btn = is_change_index
    end

    self:DoDetailBagTween()
end

function HiddenWeaponView:UpdateAwakenSkill(equip_vo)
    local weapon_type = self:GetWeaponType()
    -- local data_cfg = HiddenWeaponWGData.Instance:GetAwakenCfgData(weapon_type)
    -- local max_cfg = HiddenWeaponWGData.Instance:GetMaxAwakenCfgData(weapon_type)
    --local cfg = data_cfg[1] --随便拿一个icon 都一样的
   -- self.node_list["btn_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(cfg.skill_icon))
--    local bundle, asset = ResPath.GetRawImagesPNG("a1_sjxt_" .. equip_vo.equip.special_skill_icon)
--     self.node_list.btn_skill_icon.raw_image:LoadSprite(bundle, asset, function()
--         self.node_list.btn_skill_icon.raw_image:SetNativeSize()
--     end)

    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    local pro_equip_item = data.protocol_equip_item
    local special_effect_level = pro_equip_item.special_effect_level or 0

    self.hw_special_skill_node:SetData({big_type = weapon_type, special_effect_level = special_effect_level})
end

function HiddenWeaponView:SetDetailUpDate()
    local weapon_type = self:GetWeaponType()
    local filter_tag = self:GetDetailFilterTag()

    local select_str = Language.ShenJiEquip.SUBTYPE_NAME_all
    if filter_tag > 0 then
        local name_str = weapon_type == 2 and "ARMOR_SUBTYPE_NAME" or "WEAPON_SUBTYPE_NAME"
        local attr_str = name_str .. filter_tag
        select_str = Language.ShenJiEquip[attr_str]
    end
    self.node_list.text_detail_select.text.text = select_str

    self.curr_datas = HiddenWeaponWGData.Instance:GetDataList(weapon_type, filter_tag)

    -- 检查是否一键分解
    local hasDecompose = HiddenWeaponWGData.Instance:IsDataContainsDecompose()
    self.node_list["remind_fenjie"]:SetActive(hasDecompose == true)

    self.node_list.detail_model_display:CustomSetActive(#self.curr_datas > 0)
    self.node_list["empty_equip_node"]:SetActive(false)
    self.node_list["title_bg"]:SetActive(true)
    self.node_list["list_box_skill"]:SetActive(true)

    self.hw_bag_list:SetDataList(self.curr_datas)
    self.node_list["detail_capability"]:SetActive(#self.curr_datas > 0)
    self.node_list["star_area"]:SetActive(#self.curr_datas > 0)
    self.node_list["detail_skill_content"]:SetActive(#self.curr_datas > 0)
    -- self.node_list["detail_btn_equip"]:SetActive(#self.curr_datas > 0)
    self.show_detail_equip_btn = #self.curr_datas > 0
    self.node_list.special_skill_wu:SetActive(#self.curr_datas <= 0)
    local info_data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    if info_data.item_id == 0 and self.node_list["btn_detail_bag"]:GetActive() then
        self:ResetDetailContent()
        self.cur_detail_item_data = {}
        self.node_list["empty_equip_node"]:SetActive(true)
        self.node_list["title_bg"]:SetActive(false)
        self.node_list["detail_skill_content"]:SetActive(false)
        self.node_list["list_box_skill"]:SetActive(false)
        self.node_list["star_area"]:SetActive(false)
        self.node_list["detail_model_display"]:SetActive(false)
        self.node_list["txt_capability"].text.text = ""
        self.node_list["detail_capability"]:SetActive(false)
        self.node_list["box_skill_special"]:SetActive(false)
    else
        if IsEmptyTable(self.curr_datas) then
            self:ResetDetailContent()
            self.cur_detail_item_data = {}
            self.node_list["empty_equip_node"]:SetActive(true)
            self.node_list["title_bg"]:SetActive(false)
            self.node_list["detail_skill_content"]:SetActive(false)
            self.node_list["list_box_skill"]:SetActive(false)
            self.node_list["star_area"]:SetActive(false)
            self.node_list["detail_model_display"]:SetActive(false)
            self.node_list["txt_capability"].text.text = ""
            self.node_list["box_skill_special"]:SetActive(false)
            self.node_list["detail_capability"]:SetActive(false)
        end

        if IsEmptyTable(self.cur_detail_item_data) or self.detail_change_flag then
            self.detail_change_flag = false
            self.hw_bag_list:JumpToIndexAndSelect(1)
        end
    end

    ReDelayCall(
        self,
        function()
            if #self.curr_datas > 0 then
            else
                self:ResetDetailContent()
            end
        end,
        0.01,
        "HiddenWeaponDetailView_FILTER"
    )
    self.node_list.part_shaixuan:SetActive(false)
end

-- 获取筛选类型
function HiddenWeaponView:GetDetailFilterTag()
    for index = 1, 3 do
        if self.node_list and self.node_list["detail_filter_toggle_" .. index] then
            if self.node_list["detail_filter_toggle_" .. index].toggle.isOn == true then
                return index
            end
        end
    end

    return -1
end

-- 筛选变化
function HiddenWeaponView:OnDetailFilterChanged(index, is_on)
    ReDelayCall(
        self,
        function()
            self:SetDetailUpDate()
        end,
        0.01,
        "HiddenWeaponDetailView_FILTER"
    )
end

function HiddenWeaponView:ResetDetailContent()
    -- TODO:reset star
    if self.node_list then
        if self.node_list["txt_detail_name"] then
            self.node_list["txt_detail_name"].text.text = Language.ShenJiEquip.NO_EQUIP_NOW
        end

        if self.node_list["group_detail_content"] then
            for k, v in pairs(self.hw_skill_node) do
                v:SetData(nil)
            end
        end
    
        for index = 1, 4 do
            if self.node_list["txt_base_attr_" .. index] then
                self.node_list["txt_base_attr_" .. index]:SetActive(false)
            end
        end
        for index = 1, 6 do
            if self.node_list["txt_star_attr_" .. index] then
                self.node_list["txt_star_attr_" .. index]:SetActive(false)
            end
        end
    end
end

function HiddenWeaponView:SetDetailupFilterName()
    local weapon_type = self:GetWeaponType()
    local name_str = "ARMOR_SUBTYPE_NAME"
    if weapon_type == 2 then
        --SysMsgWGCtrl.Instance:ErrorRemind("软甲")
        name_str = "ARMOR_SUBTYPE_NAME"
    else
        --SysMsgWGCtrl.Instance:ErrorRemind("暗器")
        name_str = "WEAPON_SUBTYPE_NAME"
    end

    for i = 1, 3 do
        if self.node_list["an_txt_" .. i] then
            self.node_list["an_txt_" .. i].text.text = Language.ShenJiEquip[name_str .. i]
        end

        if self.node_list["hl_txt_" .. i] then
            self.node_list["hl_txt_" .. i].text.text = Language.ShenJiEquip[name_str .. i]
        end
    end
end

-- 背包选择变化 is_flush只为刷新
function HiddenWeaponView:OnHWItemSelectChanged(is_flush, item)
    local equip_vo = item.data
    if not equip_vo or not equip_vo.equip then
        return
    end

    if self.cur_detail_item_data == equip_vo and not is_flush then
        local item_id = equip_vo.item_id
        local btn_callback_event = {}
        if not equip_vo.is_dress and RoleBagWGData.Instance:GetSpecialBagItemResloveCfg(item_id) then
            local reslove_list = {
                {
                item_id = item_id,
                bag_index = equip_vo.index,
                num = 1,
                },
            }
            btn_callback_event[1] = {btn_text = Language.Tip.ButtonLabel[25], callback = function()
                RoleBagWGCtrl.Instance:ReqResloveItemList(KNAPSACK_TYPE.SHENJI_EQUIP, reslove_list)
            end}
        end

        local item_data = SiXiangCallWGData.Instance:GetXianQiZhenLianItemData()
        item_data.item_id = item_id
        item_data.star = equip_vo.equip.base_star
        item_data.color = equip_vo.equip.base_color
        item_data.is_bind = equip_vo.isbind
        TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL, nil, nil, btn_callback_event)
        return
    end

    self:ResetDetailContent()
    self.node_list["box_skill_special"]:SetActive(true)
    if not is_flush then
        self.cur_detail_item_data = equip_vo
    end

    self.node_list["txt_detail_name"].text.text = equip_vo.equip.name
    -- 设置战力
    -- HiddenWeaponWGData.Instance:GetCurItemCap(equip_vo)
    self.node_list["txt_capability"].text.text = tostring((item.data.capability or 0))

    -- 设置基础信息，按 HW_BASE_ATTRS 顺序来检索
    -- 获取当前注灵属性
    local zy_attrs = HiddenWeaponWGData.Instance:GetZLAdd(equip_vo.equip.big_type) or {}
    local base_attrs = equip_vo.base_attr
    local counter = 1
    if base_attrs then
        for index, attr_name in ipairs(HW_BASE_ATTRS) do
            local key = "txt_base_attr_" .. counter
            --local add_key = "txt_base_add_attr_" .. counter
            if base_attrs[attr_name] and base_attrs[attr_name] > 0 and self.node_list[key] then
                self.node_list[key]:SetActive(true)
                self.node_list[key .. 1].text.text =
                ToColorStr((Language.Common.AttrNameList2[attr_name] .. ":" or ""), COLOR3B.WHITE) .. " " .. tostring(base_attrs[attr_name] or 0)
                local zy_add = zy_attrs[attr_name] or 0
                --self.node_list[add_key .. 1]:SetActive(zy_add > 0)
                --self.node_list[add_key .. 1].text.text = tostring(zy_attrs[attr_name] or 0)
                counter = counter + 1
            end
        end
    end

    -- 设置星级属性
    local attrs_index = HiddenWeaponWGData.Instance:GetColorAttrIndex(equip_vo.equip.big_type)
    local rand_percent = HiddenWeaponWGData.Instance:GetRandPercent(equip_vo.equip.big_type)
    local star_attrs = equip_vo.color_attr
    counter = 1
    if star_attrs then
        for index, attr_name in ipairs(attrs_index) do
            local key = "txt_star_attr_" .. counter
            --local add_key = "txt_star_add_attr_" .. counter
            --self.node_list[add_key .. 1]:SetActive(true)
            if star_attrs[attr_name] and star_attrs[attr_name] > 0 and self.node_list[key] then
                self.node_list[key]:SetActive(true)
                local text = "%s %s"
                local percent = (rand_percent[index] or 0) / 10000

                if percent == 0 then
                    -- self.node_list[key .. 1].text.text =
                    --     string.format(
                    --     text,
                    --     (Language.Common.AttrNameList2[attr_name] or ""),
                    --     tostring(star_attrs[attr_name] / 100) .. "%"
                    -- )
                    
                    if "base_attr_per" == attr_name then
                        self.node_list[key .. 1].text.text =
                            string.format(
                            text,
                            ToColorStr((Language.Common.AttrNameList2[attr_name] .. ":" or ""), COLOR3B.WHITE),
                            tostring(star_attrs[attr_name] / 100) .. "%"
                        )
                    else
                        self.node_list[key .. 1].text.text =
                            string.format(
                            text,
                            ToColorStr((Language.Common.AttrNameList2[attr_name] .. ":" or ""),COLOR3B.WHITE),
                            tostring(star_attrs[attr_name])
                        )
                    end 
                    --self.node_list[add_key .. 1].text.text = ""
                    --self.node_list[add_key .. 1]:SetActive(false)
                elseif "base_attr_per" == attr_name then
                    self.node_list[key .. 1].text.text =
                        string.format(
                        text,
                        ToColorStr((Language.Common.AttrNameList2[attr_name] .. ":" or ""), COLOR3B.WHITE),
                        tostring(star_attrs[attr_name] / 100) .. "%"
                    )
                    --self.node_list[add_key .. 1].text.text = tostring(math.ceil(star_attrs[attr_name] / 100 * percent)) .. "%"
                else
                    self.node_list[key .. 1].text.text =
                        string.format(
                        text,
                        ToColorStr((Language.Common.AttrNameList2[attr_name] .. ":" or ""), COLOR3B.WHITE),
                        tostring(star_attrs[attr_name])
                    )
                    --self.node_list[add_key .. 1].text.text = tostring(math.ceil(star_attrs[attr_name] * percent))
                end
                counter = counter + 1
            end
        end
    end

    -- 设置星星
    self:SetDetailStar(equip_vo.equip.base_star)
    -- 设置六个技能
    self:UpdateSkillUI(equip_vo)
    self:UpdateAwakenSkill(equip_vo)

    --设置模型
    self:FlushHWDetailModel(equip_vo.equip.origin_data.base_model)
end

function HiddenWeaponView:FlushHWDetailModel(res_id)
    if self.detail_model_id == res_id then
        return
    end
    self.detail_model_id = res_id
    local model_bundle, model_asset = ResPath.GetArtifactModel(res_id)
    self.detail_model_display:SetMainAsset(model_bundle, model_asset)
end

function HiddenWeaponView:EquipHWItem()
    if not self.cur_detail_item_data or not self.cur_detail_item_data.equip then
        return
    end
    if not self.cur_detail_item_data.index or self.cur_detail_item_data.index < 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.EquipedImpguard)
        return
    end
    local ok_func = function(...)
        HiddenWeaponRequest:ReqEquip(self.cur_detail_item_data.index)
        self:DoDetailBagTween()
    end
    local equiped = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(self.cur_detail_item_data.equip.big_type)
    -- 当前无装备中，直接请求
    if equiped == nil or equiped.equip == nil then
        ok_func()
        return
    end
    -- 当前装备珍稀，尝试用非珍稀替换
    local data_special_flag = self.cur_detail_item_data.equip.special_flag or 0
    local equip_special_flag = equiped.equip.special_flag or 0
    if equip_special_flag > data_special_flag then
        local alert_content =
            string.format(
            Language.ShenJiEquip.SPECIAL_EQUIP_CHANGE,
            self:GetDetailTextColor(equiped),
            equiped.equip.name,
            Language.ShenJiEquip.COMMON_WEAPONTYPE_NAME[self.cur_detail_item_data.equip.big_type] or ""
        )
        local alert = TipWGCtrl.Instance:OpenAlertTips(alert_content, ok_func,nil,nil,nil,nil,nil,nil,nil,Language.ShenJiEquip.KMAlterTitle)
        -- alert:SetTitle(Language.ShenJiEquip.KMAlterTitle)
        return
    end
    -- 当前的装备原始品质更高
    local origin_base_color = self.cur_detail_item_data.equip.origin_data.base_color or 0
    local origin_equip_color = equiped.equip.origin_data.base_color or 0

    if origin_equip_color > origin_base_color then
        local alert_content =
            string.format(Language.ShenJiEquip.COLOR_EQUIP_CHANGE, self:GetDetailTextColor(equiped), equiped.equip.name)
        local alert = TipWGCtrl.Instance:OpenAlertTips(alert_content, ok_func,nil,nil,nil,nil,nil,nil,nil,Language.ShenJiEquip.KMAlterTitle)
        -- alert:SetTitle(Language.ShenJiEquip.KMAlterTitle)
        return
    end
    ok_func()
end

function HiddenWeaponView:GetDetailTextColor(data)
    if self.cur_detail_item_data == nil or self.cur_detail_item_data.equip == nil or self.cur_detail_item_data.equip.base_color == nil then
        return COLOR3B.WHITE
    end
    return ITEM_COLOR[self.cur_detail_item_data.equip.base_color] or COLOR3B.WHITE
end

function HiddenWeaponView:SetDetailStar(start_count)
    local weapon_type = self:GetWeaponType()
    local data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(weapon_type)
    local pro_equip_item = data.protocol_equip_item
    local special_effect_level = pro_equip_item.special_effect_level or 0

    if data and data.equip then
        for i = 1, 5 do
            local bundle, asset = ResPath.GetCommonImages("a2_sj_xx")
            local bundle1, asset1 = ResPath.GetCommonImages("a2_sj_hx")
            if self.node_list["detail_star_".. i] then
                if i <= special_effect_level then
                    self.node_list["detail_star_".. i].image:LoadSprite(bundle, asset, function()
                        self.node_list["detail_star_".. i].image:SetNativeSize()
                    end)
                else
                    self.node_list["detail_star_".. i].image:LoadSprite(bundle1, asset1, function()
                        self.node_list["detail_star_".. i].image:SetNativeSize()
                    end)
                end
            end
        end
    else
        local bundle1, asset1 = ResPath.GetCommonImages("a2_sj_hx")
        for i = 1, 5 do
            self.node_list["detail_star_".. i].image:LoadSprite(bundle1, asset1, function()
                self.node_list["detail_star_".. i].image:SetNativeSize()
            end)
        end
    end

end

function HiddenWeaponView:UpdateSkillUI(equip_vo)
    for k, v in pairs(self.hw_skill_node) do
        v:SetData(nil)
        self.node_list["hw_detail_skill" .. k]:CustomSetActive(false)
    end

    -- 主动与专属合并
    local equip = equip_vo.equip
    local color = equip.base_color
    local star = equip.base_star
    local active_skill_list = string.split(equip.active_skill_list, "|")

    -- 主动
    local index = 1
    for k, v in pairs(active_skill_list) do
        local skill_ui = self.hw_skill_node[index]
        local skill_id = tonumber(active_skill_list[k])

        local cfg, is_active, max_cfg =
            HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.ACTIVE_SKILL_TYPE, skill_id, color, star)
        if cfg then
            self.node_list["hw_detail_skill" .. index]:CustomSetActive(true)
            skill_ui:SetData(
                {
                    cfg = cfg,
                    is_active = is_active,
                    skill_type = HW_CONST_PARAM.ACTIVE_SKILL_TYPE,
                    max_cfg = max_cfg,
                    is_bi_sha = true
                }
            )

            index = index + 1
        end
    end

    --专属
    local special_skill_list = string.split(equip.special_skill_list, "|")
    for k, v in pairs(special_skill_list) do
        local skill_ui = self.hw_skill_node[index]
        local skill_id = tonumber(special_skill_list[k])
        local cfg, is_active, max_cfg = HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.SPECIAL_SKILL_TYPE, skill_id, color, star)

        if cfg then
            self.node_list["hw_detail_skill" .. index]:CustomSetActive(true)
            skill_ui:SetData(
                {cfg = cfg, is_active = is_active, skill_type = HW_CONST_PARAM.SPECIAL_SKILL_TYPE, max_cfg = max_cfg}
            )
            index = index + 1
        end
    end

    --被动
    index = 5

    local passive_skill_list = string.split(equip.passive_skill_list, "|")
    local num = #passive_skill_list

    for i = 1, 3 do
        local skill_ui = self.hw_skill_node[index]
        local skill_id = tonumber(passive_skill_list[i])

        local cfg, is_active, max_cfg =
            HiddenWeaponWGData.Instance:GetSkillCfg(HW_CONST_PARAM.PASSIVE_SKILL_TYPE, skill_id, color, star)
        if cfg then
            self.node_list["hw_detail_skill" .. index]:CustomSetActive(true)
            skill_ui:SetData(
                {cfg = cfg, is_active = is_active, skill_type = HW_CONST_PARAM.PASSIVE_SKILL_TYPE, max_cfg = max_cfg}
            )
            index = index + 1
        end
    end
end

function HiddenWeaponView:InitDetailBagPosition()
    self.node_list.tabbar_begin.rect.anchoredPosition = left_cell_start_pos
    self.node_list.part_bag_right.rect.anchoredPosition = detail_bag_start_pos
    self.node_list.detail_back_tocell:CustomSetActive(false)
    self.node_list.btn_bailian:CustomSetActive(true)
    self.node_list.btn_fenjie:CustomSetActive(true)
    self.node_list.btn_detail_bag:CustomSetActive(true)
    self.node_list.btn_fenjie:CustomSetActive(false)
    self.node_list["detail_btn_equip"]:CustomSetActive(false)
end

function HiddenWeaponView:DoDetailBagTween()
    -- 显示背包 切换成  cell
    if self.detail_show_bag then
        self.node_list.part_bag_right.rect:DOAnchorPos(detail_bag_start_pos, 1)
        self.node_list.tabbar_begin.rect:DOAnchorPos(left_cell_start_pos, 1)
    else
        self.node_list.tabbar_begin.rect:DOAnchorPos(left_cell_end_pos, 1)
        self.node_list.part_bag_right.rect:DOAnchorPos(detail_bag_end_pos, 1)
    end

    self.detail_show_bag = not self.detail_show_bag
    self.node_list.detail_back_tocell:CustomSetActive(self.detail_show_bag)
    --self.node_list.btn_bailian:CustomSetActive(not self.detail_show_bag)
    --self.node_list.btn_fenjie:CustomSetActive(not self.detail_show_bag)
    self.node_list.btn_detail_bag:CustomSetActive(not self.detail_show_bag)
    self.node_list.btn_fenjie:CustomSetActive(self.detail_show_bag)
    self.node_list["detail_btn_equip"]:CustomSetActive(self.detail_show_bag and self.show_detail_equip_btn)
    self:SetDetailUpDate()
    local cur_detail_item_data = self.detail_show_bag and self.cur_detail_item_data or {}
    self:FlushHWDetailInfo(cur_detail_item_data)
end

function HiddenWeaponView:OnClickDetailBackToCell()
    self:DoDetailBagTween()
end

function HiddenWeaponView:OnClickDetailSelectBtn()
    local active = self.node_list.part_shaixuan:GetActive()
    self.node_list.part_shaixuan:CustomSetActive(not active)
    self.node_list["detail_bag_select_back"]:CustomSetActive(not active)
end

function HiddenWeaponView:OnClickDetailBackSelect()
    self.node_list.part_shaixuan:CustomSetActive(false)
    self.node_list["detail_bag_select_back"]:CustomSetActive(false)
end