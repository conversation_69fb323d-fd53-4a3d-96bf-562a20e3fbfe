using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

/// <summary>
/// 技能编辑器事件配置类
/// 集中管理所有事件相关的配置数据
/// </summary>
public static class SkillEditorEventConfig
{
    #region 模型类型枚举
    public enum ModelType
    {
        [Description("无")]
        None = 0,
        [Description("角色")]
        Role = 1,
        [Description("天神")]
        Tianshen = 2,
        [Description("Boss")]
        Boss = 3,
        [Description("怪物")]
        Monster = 4,
        [Description("武魂")]
        <PERSON>hun = 5,
        [Description("坐骑")]
        Zuoqi = 6,
        [Description("驭兽")]
        Yushou = 7,
        [Description("高达")]
        Gundam = 8,
        [Description("双生天神")]
        ShuangshenTianshen = 9,
        [Description("背饰")]
        BackDecoration = 10,
        [Description("背饰羽翼")]
        BackDecorationWings = 11,
    }

    /// <summary>
    /// 获取模型类型显示名称数组
    /// </summary>
    public static readonly string[] ModelTypeNames = GetEnumDescriptions<ModelType>();

    private static string[] GetEnumDescriptions<T>() where T : Enum
    {
        return Enum.GetValues(typeof(T))
            .Cast<T>()
            .Select(e => GetDescription(e))
            .ToArray();
    }

    private static string GetDescription(Enum value)
    {
        var field = value.GetType().GetField(value.ToString());
        return field.GetCustomAttributes(typeof(DescriptionAttribute), false)
                .FirstOrDefault() is DescriptionAttribute attribute
            ? attribute.Description
            : value.ToString();
    }
    #endregion

    #region 路径配置
    /// <summary>
    /// 模型资源路径映射
    /// </summary>
    public static readonly Dictionary<int, string> ModelResPathMap = new Dictionary<int, string>()
    {
        { (int)ModelType.None, "" },
        { (int)ModelType.Role, "" },
        { (int)ModelType.Tianshen, "Assets/Game/Model/Tianshen" },
        { (int)ModelType.Boss, "Assets/Game/Model/Boss" },
        { (int)ModelType.Monster, "Assets/Game/Model/Boss" },
        { (int)ModelType.Wuhun, "Assets/Game/Model/WuHun" },
        { (int)ModelType.Zuoqi, "Assets/Game/Model/Zuoqi" },
        { (int)ModelType.Yushou, "Assets/Game/Model/Yushou" },
        { (int)ModelType.Gundam, "Assets/Game/Model/jijia" },
        { (int)ModelType.ShuangshenTianshen, "Assets/Game/Model/SSshenling" },
        { (int)ModelType.BackDecoration, "Assets/Game/Model/Beishi" },
        { (int)ModelType.BackDecorationWings, "Assets/Game/Model/Wings" },
    };

    /// <summary>
    /// 保存文件夹名称映射
    /// </summary>
    public static readonly Dictionary<int, string> SaveFolderNameMap = new Dictionary<int, string>()
    {
        { (int)ModelType.None, "" },
        { (int)ModelType.Role, "Character" },
        { (int)ModelType.Tianshen, "Tianshen" },
        { (int)ModelType.Boss, "Boss" },
        { (int)ModelType.Monster, "Monster" },
        { (int)ModelType.Wuhun, "WuHun" },
        { (int)ModelType.Zuoqi, "Zuoqi" },
        { (int)ModelType.Yushou, "Yushou" },
        { (int)ModelType.Gundam, "Gundam" },
        { (int)ModelType.ShuangshenTianshen, "ShuangShengTianshen" },
        { (int)ModelType.BackDecoration, "Beishi" },
        { (int)ModelType.BackDecorationWings, "Wings" },
    };
    #endregion

    #region 资源ID配置
    /// <summary>
    /// 角色资源ID
    /// </summary>
    public static readonly string[] RoleResIds = new string[]
    {
        "1101001", "1103001", "3101001", "3102001", "3103001",
        "110100101", "110100102", "110100103",
        "310100101", "310100102", "310100103"
    };

    /// <summary>
    /// 战斗坐骑资源ID
    /// </summary>
    public static readonly string[] FightMountResIds = new string[]
    {
    };
    #endregion

    #region 基础动作配置
    /// <summary>
    /// 基础动作列表
    /// </summary>
    public static readonly List<string> BaseActions = new List<string>
    {
        "idle", "run", "die", "rest"
    };

    /// <summary>
    /// 特殊动作配置
    /// </summary>
    public static readonly Dictionary<ModelType, List<string>> SpecialActions = new Dictionary<ModelType, List<string>>
    {
        {
            ModelType.Role, new List<string>
            {
                "fall_rest", "transformation", "prizedraw", "qinggng_rush"
            }
        },
        {
            ModelType.Tianshen, new List<string>
            {
                "chuchang"
            }
        },
        {
            ModelType.Boss, new List<string>
            {
                "hurt", "dead", "die_fly"
            }
        },
        {
            ModelType.Monster, new List<string>
            {
                "hurt", "dead", "die_fly"
            }
        },
        {
            ModelType.Zuoqi, new List<string>
            {
                "combo1_1"
            }
        },
        {
            ModelType.Yushou, new List<string>
            {
                "rest" // 覆盖基础动作
            }
        },
        {
            ModelType.ShuangshenTianshen, new List<string>
            {
                "chuchang"
            }
        }
    };
    #endregion

    #region 动作范围配置
    /// <summary>
    /// 动作范围配置
    /// </summary>
    public static readonly Dictionary<ModelType, Dictionary<string, (int start, int end)>> ActionRanges = 
        new Dictionary<ModelType, Dictionary<string, (int start, int end)>>
    {
        {
            ModelType.Role, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 15) },
                { "customized_hit_", (5, 10) },
                { "esoterica_attack_", (1, 10) },
                { "custom_action_", (1, 12) }
            }
        },
        {
            ModelType.Tianshen, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 5) }
            }
        },
        {
            ModelType.Boss, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 5) }
            }
        },
        {
            ModelType.Monster, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 5) }
            }
        },
        {
            ModelType.Wuhun, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 15) }
            }
        },
        {
            ModelType.Zuoqi, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 4) }
            }
        },
        {
            ModelType.Yushou, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 3) }
            }
        },
        {
            ModelType.Gundam, new Dictionary<string, (int start, int end)>
            {
                { "rush_", (1, 3) }
            }
        },
        {
            ModelType.ShuangshenTianshen, new Dictionary<string, (int start, int end)>
            {
                { "attack", (1, 5) }
            }
        }
    };
    #endregion

    #region 复合动作配置
    /// <summary>
    /// 复合动作配置（主序号_子序号）
    /// </summary>
    public static readonly Dictionary<ModelType, Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>> 
        CompositeActionRanges = new Dictionary<ModelType, Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>>
    {
        {
            ModelType.Role, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "combo", (1, 1, 1, 4) }
            }
        },
        {
            ModelType.Tianshen, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "combo", (1, 1, 1, 4) }
            }
        },
        {
            ModelType.Boss, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "magic", (1, 4, 1, 3) }
            }
        },
        {
            ModelType.Monster, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "magic", (1, 2, 1, 3) }
            }
        },
        {
            ModelType.Wuhun, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "combo", (1, 1, 1, 4) }
            }
        },
        {
            ModelType.Yushou, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "magic", (1, 1, 1, 3) }
            }
        },
        {
            ModelType.Gundam, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "attack", (1, 3, 1, 5) },
                { "combo", (1, 3, 1, 5) }
            }
        },
        {
            ModelType.ShuangshenTianshen, new Dictionary<string, (int mainStart, int mainEnd, int subStart, int subEnd)>
            {
                { "combo", (1, 1, 1, 4) }
            }
        }
    };
    #endregion

    #region 事件阶段配置
    /// <summary>
    /// 事件阶段类型
    /// </summary>
    public enum EventPhase
    {
        Begin,
        Hit,
        End
    }

    /// <summary>
    /// 需要生成事件阶段的动作类型
    /// </summary>
    public static readonly HashSet<string> ActionsWithPhases = new HashSet<string>
    {
        "attack", "combo", "magic", "rush_", "customized_hit_", "esoterica_attack_", 
        "custom_action_", "touzi", "touzi2", "chuchang", "jinbi_lizi", "rest", "die",
        "mount_attack", "fly_soilder_attack", "fall_rest", "transformation", "prizedraw", "qinggng_rush"
    };

    /// <summary>
    /// 只有begin和end阶段的动作（没有hit阶段）
    /// </summary>
    public static readonly HashSet<string> ActionsWithoutHit = new HashSet<string>
    {
        "customized_hit_", "esoterica_attack_", "custom_action_", "rest", "die",
        "fall_rest", "transformation", "prizedraw", "qinggng_rush"
    };
    #endregion
}
