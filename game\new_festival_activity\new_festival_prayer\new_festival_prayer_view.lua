function NewFestivalActivityView:LoadIndexCallBackPrayer()
    NewFestivalPrayerWGCtrl.Instance:ReqNewFestivalPrayerInfo(OA_NEWYEAR_PRAYER_OPERATE_TYPE.INFO)
    self:SetPrayerShowImg()

    if self.prayer_model_display == nil then
		self.prayer_model_display = OperationActRender.New(self.node_list["prayer_model"])
	end

    if not self.prayer_reward_list then
        self.prayer_reward_list = AsyncListView.New(NewFestivalPrayerRewardRender, self.node_list["prayer_reward_list"])
    end

    if not self.prayer_normal_item then
        self.prayer_normal_item = NfaPrayerGuDingRewardItem.New(self.node_list.prayer_normal_item)
    end
 
    if not self.prayer_high_item_list then
        self.prayer_high_item_list = {}
        for i = 1, 2 do
            self.prayer_high_item_list[i] = NfaPrayerGuDingRewardItem.New(self.node_list["prayer_high_item_" .. i])
            self.prayer_high_item_list[i]:SetIsAdvancedItem(true)
        end  
    end

    if not self.prayer_daily_task_list then
        self.prayer_daily_task_list = AsyncListView.New(NfaPrayerTaskItem, self.node_list["prayer_daily_task_list"])
    end
    
    if not self.prayer_jrhd_task_list then
        self.prayer_jrhd_task_list = AsyncListView.New(NfaPrayerTaskItem, self.node_list["prayer_jrhd_task_list"])
    end

    for i = 1, 3 do
		self.node_list["prayer_toggle_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickTypeSelect, self, i))
	end

    XUI.AddClickEventListener(self.node_list["prayer_buy_btn"], BindTool.Bind(self.OnClickPrayerBuyBtn, self))
    XUI.AddClickEventListener(self.node_list["prayer_get_all_btn"], BindTool.Bind(self.OnClickPrayerGetAllBtn, self))
    XUI.AddClickEventListener(self.node_list["prayer_buy_advand_btn"], BindTool.Bind(self.OnClickPrayerBuyAdvancedBtn, self))

    self.prayer_need_flush_model = true
end

function NewFestivalActivityView:ReleasePrayer()
    if CountDownManager.Instance:HasCountDown("nfa_prayer_time") then
        CountDownManager.Instance:RemoveCountDown("nfa_prayer_time")
    end

    if self.prayer_model_display then
        self.prayer_model_display:DeleteMe()
        self.prayer_model_display = nil
    end

    if self.prayer_reward_list then
        self.prayer_reward_list:DeleteMe()
        self.prayer_reward_list = nil
    end

    if self.prayer_normal_item then
        self.prayer_normal_item:DeleteMe()
        self.prayer_normal_item = nil
    end

    if self.prayer_daily_task_list then
		self.prayer_daily_task_list:DeleteMe()
		self.prayer_daily_task_list = nil
	end

    if self.prayer_jrhd_task_list then
		self.prayer_jrhd_task_list:DeleteMe()
		self.prayer_jrhd_task_list = nil
	end

    if self.prayer_high_item_list then
        for k, v in pairs(self.prayer_high_item_list) do
            v:DeleteMe()
        end
        self.prayer_high_item_list = nil
    end

    self.prayer_select_type = nil
    self.prayer_need_flush_model = nil
end

function NewFestivalActivityView:ShowIndexCallBackPrayer()
    self:FlushPrayerActTime()
end

function NewFestivalActivityView:OnFlushPrayer()
    local reward_red = NewFestivalPrayerWGData.Instance:GetPrayerRewardRed()
    local daily_red = NewFestivalPrayerWGData.Instance:GetTaskTypeIsCanFetch(NewFestivalPrayerTaskType.Daily)
    local jrhd_red = NewFestivalPrayerWGData.Instance:GetTaskTypeIsCanFetch(NewFestivalPrayerTaskType.Jrhd)
    if not self.prayer_select_type then
        local selcet_index = 1
        if reward_red then
            selcet_index = 1
        elseif daily_red then
            selcet_index = 2
        elseif jrhd_red then
            selcet_index = 3
        end
        if self.node_list["prayer_toggle_" .. selcet_index].toggle.isOn then
            self:OnClickTypeSelect(selcet_index)
        else
            self.node_list["prayer_toggle_" .. selcet_index].toggle.isOn = true
        end
    end

    self.node_list.prayer_toggle_red_1:SetActive(reward_red)
    self.node_list.prayer_toggle_red_2:SetActive(daily_red)
    self.node_list.prayer_toggle_red_3:SetActive(jrhd_red)

    self:FlushPrayerRewardPanel()
    self:FlushPrayerDailyTaskPanel()
    self:FlushPrayerJrhdTaskPanel()
end

function NewFestivalActivityView:SetPrayerShowImg()
    local prayer_info_cfg = NewFestivalActivityWGData.Instance:GetPrayerInfoCfg()
	local prayer_title_bundle, prayer_title_asset = ResPath.GetNewFestivalRawImages("qy_title")
	self.node_list["prayer_title"].raw_image:LoadSprite(prayer_title_bundle, prayer_title_asset, function ()
		self.node_list["prayer_title"].raw_image:SetNativeSize()
	end)

    local di_1_bundle, di_1_asset = ResPath.GetNewFestivalRawImages("qy_di_1")
	self.node_list["prayer_di_1"].raw_image:LoadSprite(di_1_bundle, di_1_asset, function ()
		self.node_list["prayer_di_1"].raw_image:SetNativeSize()
	end)

    local yb_icon_bundle, yb_icon_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_icon_yb")
	self.node_list["prayer_yb_icon"].image:LoadSprite(yb_icon_bundle, yb_icon_asset, function ()
		self.node_list["prayer_yb_icon"].image:SetNativeSize()
	end)

    local gj_icon_bundle, gj_icon_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_icon_gj")
	self.node_list["prayer_gj_icon"].image:LoadSprite(gj_icon_bundle, gj_icon_asset, function ()
		self.node_list["prayer_gj_icon"].image:SetNativeSize()
	end)

    local di_2_bundle, di_2_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_di_1")
	self.node_list["prayer_di_2"].image:LoadSprite(di_2_bundle, di_2_asset, function ()
		self.node_list["prayer_di_2"].image:SetNativeSize()
	end)

    local di_3_bundle, di_3_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_di_2")
	self.node_list["prayer_di_3"].image:LoadSprite(di_3_bundle, di_3_asset, function ()
		self.node_list["prayer_di_3"].image:SetNativeSize()
	end)
    
    local guding_bundle, guding_asset = ResPath.GetNewFestivalRawImages("qy_di_2")
	self.node_list["prayer_guding_img"].raw_image:LoadSprite(guding_bundle, guding_asset, function ()
		self.node_list["prayer_guding_img"].raw_image:SetNativeSize()
	end)

    local prayer_buy_bundle, prayer_buy_asset = ResPath.GetNewFestivalActImages("a3_jrhd_qy_btn_1")
	self.node_list["prayer_buy_btn"].image:LoadSprite(prayer_buy_bundle, prayer_buy_asset, function ()
		self.node_list["prayer_buy_btn"].image:SetNativeSize()
	end)

    local get_all_bundle, get_all_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xyxc_btn_ybqy")
	self.node_list["prayer_get_all_btn"].image:LoadSprite(get_all_bundle, get_all_asset, function ()
		self.node_list["prayer_get_all_btn"].image:SetNativeSize()
	end)

    local buy_advand_bundle, buy_advand_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xyxc_btn_jjqy")
	self.node_list["prayer_buy_advand_btn"].image:LoadSprite(buy_advand_bundle, buy_advand_asset, function ()
		self.node_list["prayer_buy_advand_btn"].image:SetNativeSize()
	end)

    local prayer_act_bg_bundle, prayer_act_bg_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dl_di2")
    self.node_list["prayer_act_bg"].image:LoadSprite(prayer_act_bg_bundle, prayer_act_bg_asset, function()
       self.node_list["prayer_act_bg"].image:SetNativeSize()
   end)

    local btn_bundle, btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1")
    local btn_hl_bundle, btn_hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1_hl")
    for i = 1, 3 do
        self.node_list["prayer_toggle_nor_text_" .. i].text.text = prayer_info_cfg["btn_text_" .. i]
        self.node_list["prayer_toggle_hl_text_" .. i].text.text = prayer_info_cfg["btn_text_" .. i]
        self.node_list["prayer_toggle_nor_text_" .. i].text.color = Str2C3b(prayer_info_cfg.text_color_1)
        self.node_list["prayer_toggle_hl_text_" .. i].text.color = Str2C3b(prayer_info_cfg.text_color_2)

        local btn_nor_img = self.node_list["prayer_toggle_nor_img_" .. i].image
        btn_nor_img:LoadSprite(btn_bundle, btn_asset, function ()
            btn_nor_img:SetNativeSize()
        end)

        local btn_hl_img = self.node_list["prayer_toggle_hl_img_" .. i].image
        btn_hl_img:LoadSprite(btn_hl_bundle, btn_hl_asset, function ()
            btn_hl_img:SetNativeSize()
        end)
    end

    self.node_list["prayer_str_1"].text.color = Str2C3b(prayer_info_cfg.text_color_3)
    self.node_list["prayer_count_value"].text.color = Str2C3b(prayer_info_cfg.text_color_4)
    self.node_list["prayer_act_time"].text.color = Str2C3b(prayer_info_cfg.text_color_5)
    self.node_list["prayer_buy_text"].text.color = Str2C3b(prayer_info_cfg.text_color_6)
    self.node_list["prayer_get_all_text"].text.color = Str2C3b(prayer_info_cfg.text_color_7)
    self.node_list["prayer_buy_advand_text"].text.color = Str2C3b(prayer_info_cfg.text_color_8)
end

function NewFestivalActivityView:FlushPrayerRewardPanel()
    self:FlushPrayerRewardOtherInfo()
    self:FlushPrayerRewardList()
    self:FlushPrayerBigRewardInfo()
    self:FlushPrayerModelInfo()
end

function NewFestivalActivityView:FlushPrayerActTime()
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.NEW_FESTIVAL_ACT_PRAYER)
    if CountDownManager.Instance:HasCountDown("nfa_prayer_time") then
        CountDownManager.Instance:RemoveCountDown("nfa_prayer_time")
    end
    if time > 0 then
        CountDownManager.Instance:AddCountDown("nfa_prayer_time", 
            BindTool.Bind(self.PrayerUpdateTimeCallBack, self), 
            BindTool.Bind(self.PrayerOnComplete, self), 
            nil, time, 1)
    else
        self:PrayerOnComplete()
    end
end

function NewFestivalActivityView:PrayerUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    local prayer_info_cfg = NewFestivalActivityWGData.Instance:GetPrayerInfoCfg()
    local time_part_color = prayer_info_cfg and prayer_info_cfg.time_part_color or COLOR3B.D_GREEN
    self.node_list["prayer_act_time"].text.text = string.format(Language.NewFestivalActivity.ActTime, time_part_color, time_str)
end

function NewFestivalActivityView:PrayerOnComplete()
    self.node_list.prayer_act_time.text.text = ""
end

function NewFestivalActivityView:FlushPrayerRewardOtherInfo()
    local prayer_count = NewFestivalPrayerWGData.Instance:GetPrayerCount()
    self.node_list.prayer_count_value.text.text = prayer_count

    local is_buy_advanced = NewFestivalPrayerWGData.Instance:GetIsBuyAdvancedFlag()
    self.node_list.prayer_buy_advand_btn:SetActive(not is_buy_advanced)

    local is_remind = NewFestivalPrayerWGData.Instance:GetPrayerRewardRed()
    self.node_list.prayer_get_all_red:SetActive(is_remind)
end

function NewFestivalActivityView:FlushPrayerRewardList()
    local reward_list = NewFestivalPrayerWGData.Instance:GetRewardShowInfo()
    local info_list = {}
    local show_all_get_btn = true
    for k, v in ipairs(reward_list) do
        if v.cfg and v.cfg.guding_mark ~= 1 then
            table.insert(info_list, v)
            if v.can_get_remind then
                show_all_get_btn = false
            end
        end
    end
    
    self.prayer_reward_list:SetDataList(info_list)

    local def_index = 1
    local prayer_count = NewFestivalPrayerWGData.Instance:GetPrayerCount()
	if not IsEmptyTable(info_list) then
        for k, v in ipairs(info_list) do
            if prayer_count >= v.cfg.prayer_count and v.cfg.guding_mark ~= 1 then
                def_index = k
                if v.can_get_remind then
                    break
                end
            end
        end
	end

    self.prayer_reward_list:JumpToIndex(def_index)

    local is_remind = NewFestivalPrayerWGData.Instance:GetPrayerRewardRed()
    self.node_list.prayer_get_all_btn:SetActive(is_remind)
end

function NewFestivalActivityView:FlushPrayerBigRewardInfo()
    local guding_reward_cfg = NewFestivalPrayerWGData.Instance:GetGuDingReward()
    if IsEmptyTable(guding_reward_cfg) then
        return
    end
    
    local cfg = guding_reward_cfg.cfg
    self.node_list.prayer_guding_count.text.text = cfg.prayer_count
    self.prayer_normal_item:SetData({reward_data = cfg.normal_reward[0], reward_flag = guding_reward_cfg.reward_flag})
    for k, v in ipairs(self.prayer_high_item_list) do
        v:SetData({reward_data = cfg.advanced_reward[k - 1], reward_flag = guding_reward_cfg.reward_flag})
    end
end

function NewFestivalActivityView:FlushPrayerModelInfo()
    if not self.prayer_need_flush_model then
        return
    end

    self.prayer_need_flush_model = false
    local show_cfg = NewFestivalPrayerWGData.Instance:GetModelInfo()
    if not show_cfg then
        return
    end

    local display_data = {}
	display_data.should_ani = true
	if show_cfg.model_show_itemid ~= 0 and show_cfg.model_show_itemid ~= "" then
		local split_list = string.split(show_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = show_cfg.model_show_itemid
		end
	end

	display_data.bundle_name = show_cfg["model_bundle_name"]
    display_data.asset_name = show_cfg["model_asset_name"]
    local model_show_type = tonumber(show_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

   local pos_x, pos_y, pos_z = 0, 0, 0
	if show_cfg.display_pos and show_cfg.display_pos ~= "" then
		local pos_list = string.split(show_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list["prayer_model"].rect, pos_x, pos_y, pos_z)

	-- if show_cfg.rotation and show_cfg.rotation ~= "" then
	-- 	local rotation_tab = string.split(show_cfg.rotation,"|")
	-- 	self.node_list["prayer_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	-- end
	local rot_x, rot_y, rot_z = 0, 0, 0
	if show_cfg.rotation and show_cfg.rotation ~= "" then
		local rot_list = string.split(show_cfg.rotation, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
	end

    local scale = show_cfg["display_scale"]
    -- Transform.SetLocalScaleXYZ(self.node_list["prayer_model"].transform, scale, scale, scale)
  
    display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
    display_data.model_adjust_root_local_scale = scale
    display_data.model_rt_type = ModelRTSCaleType.M

    self.prayer_model_display:SetData(display_data)
end

function NewFestivalActivityView:FlushPrayerDailyTaskPanel()
    local task_data = NewFestivalPrayerWGData.Instance:GetTaskListByType(NewFestivalPrayerTaskType.Daily)
    self.prayer_daily_task_list:SetDataList(task_data)
end

function NewFestivalActivityView:FlushPrayerJrhdTaskPanel()
    local task_data = NewFestivalPrayerWGData.Instance:GetTaskListByType(NewFestivalPrayerTaskType.Jrhd)
    self.prayer_jrhd_task_list:SetDataList(task_data)
end

function NewFestivalActivityView:OnClickTypeSelect(type)
	if self.prayer_select_type == type then
		return
	end

	self.prayer_select_type = type
end

function NewFestivalActivityView:OnClickPrayerBuyBtn()
    NewFestivalPrayerWGCtrl.Instance:OpenPrayeBuyView()
end

function NewFestivalActivityView:OnClickPrayerGetAllBtn()
    local is_remind = NewFestivalPrayerWGData.Instance:GetPrayerRewardRed()
    if is_remind then
        NewFestivalPrayerWGCtrl.Instance:ReqNewFestivalPrayerInfo(OA_NEWYEAR_PRAYER_OPERATE_TYPE.ONE_KEY_REWARD)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.NewFestivalActivity.PrayerNotReward)
    end
end

function NewFestivalActivityView:OnClickPrayerBuyAdvancedBtn()
    NewFestivalPrayerWGCtrl.Instance:OpenAdvancedView()
end