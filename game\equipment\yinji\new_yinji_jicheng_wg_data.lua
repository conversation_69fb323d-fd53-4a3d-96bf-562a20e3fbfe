NewYinJiJiChengWGData = NewYinJiJiChengWGData or BaseClass()

EQUIP_YIN_JI_PART_MAX_NUM = 9
MAX_KONG_WEI_NUM = 100

-- 装备印记操作类型
EQUIP_YIN_JI_OPERA_TYPE = {
    EQUIP_YIN_JI_OPERA_TYPE_INFO = 0,                            -- 获取信息
    EQUIP_YIN_JI_OPERA_TYPE_PUT = 1,                             -- 镶嵌刻印符   param1-操作部位 param2-孔位id param3-镶嵌的印刻符道具id
    EQUIP_YIN_JI_OPERA_TYPE_ACT = 2,                             -- 激活印记属性  param1-操作部位 param2-激活印记类型 （帝-1 魔-2 仙-3 神-4）
    EQUIP_YIN_JI_OPERA_TYPE_COMPOSE = 3,                         -- 合成（升级）刻印符   param1-操作部位 param2-激活印记类型
    EQUIP_YIN_JI_OPERA_TYPE_TRANS_KEYINFU = 4,                   -- 印记转刻印符
}

local KEYINFU_MAX_TYPE = 4 --印记最高级类型

function NewYinJiJiChengWGData:__init()
	if NewYinJiJiChengWGData.Instance ~= nil then
		ErrorLog("[NewYinJiJiChengWGData] attempt to create singleton twice!")
		return
    end
    
    NewYinJiJiChengWGData.Instance = self

    self.yin_ji_part_info_sc = {}
    self.yinji_qualit_num_list = {}         -- 刻印符数量 是多少就是多少
    self.yinji_qualit_num_list2 = {}        -- 刻印符数量 向下兼容后的数量
    self.bag_keyinfu_list = {}
    self.keyinfu_conversion_in_bag = {}
    self.cur_select_equip_part = 0
    self.cur_select_kongwei = 0
    self.have_delay_set_table = false
    self.cache_yj_condition = {}

    self.equip_yinji_cfg = ConfigManager.Instance:GetAutoConfig("equip_yinji_cfg_auto")
    -- self.part_put_limit_cfg = ListToMap(self.equip_yinji_cfg.part_put_limit, "part")                     -- 部位镶嵌限制
    self.keyinfu_put_limit_cfg = ListToMap(self.equip_yinji_cfg.keyinfu_put_limit, "part", "keyinfu_type")       -- 刻印符镶嵌限制
    self.yinji_act_attr_cfg = ListToMapList(self.equip_yinji_cfg.yinji_act_attr, "part", "order", "color")                 -- 印记激活属性 
    self.put_kongwei_cfg = ListToMapList(self.equip_yinji_cfg.put_kongwei, "part")                       -- 镶嵌孔位
    self.put_keyinfu_num_cfg = ListToMap(self.equip_yinji_cfg.put_keyinfu_num, "max_kongwei_id")         -- 镶嵌数量
    self.keyinfu_cfg = ListToMap(self.equip_yinji_cfg.keyinfu, "item_id")                                -- 刻印符
    -- self.keyinfu_compose_cfg = ListToMap(self.equip_yinji_cfg.keyinfu_compose, "use_item_id")            -- 印刻符合成(被消耗id)
    self.keyinfu_target_compose_cfg = ListToMap(self.equip_yinji_cfg.keyinfu_compose, "target_item_id")   -- 印刻符合成(目标id)
    self.kongwei_map_cfg = self.equip_yinji_cfg.kongwei_pos_map
    self.kongwei_pos_cfg = ListToMap(self.equip_yinji_cfg.zuobiao, "kongwei_pose_id")   -- 印刻符合成(目标id)

    local one_keyinfu_cfg = self.equip_yinji_cfg.keyinfu[1]
    self.keyinfu_attr_key = AttributeMgr.GetUsefulAttributteByClass(one_keyinfu_cfg)

    self:CtreKeYiFuConversionTable()
    self:CreatFakeKeYinFuBagList()

    self.item_change_callback = BindTool.Bind(self.OnItemChangeCallBack, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
    
    RemindManager.Instance:Register(RemindName.Equipment_NewYinJi, BindTool.Bind(self.GetEquipYinJiRemind, self))
	self:RegisterNewYinJiRemindInBag(RemindName.Equipment_NewYinJi)
end

function NewYinJiJiChengWGData:RegisterNewYinJiRemindInBag(remind_name)
	local item_id_list = {}
    local keyinfu_list = self.equip_yinji_cfg.keyinfu
    for i=1,#keyinfu_list do
        item_id_list[i] = keyinfu_list[i].item_id
    end
	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function NewYinJiJiChengWGData:__delete()
    if self.old_yinji_check then
        self.old_yinji_check:DeleteMe()
        self.old_yinji_check = nil
    end
    self.have_delay_set_table = nil
    if self.delay_set_table then
        GlobalTimerQuest:CancelQuest(self.delay_set_table)
        self.delay_set_table = nil
    end

    RemindManager.Instance:UnRegister(RemindName.Equipment_NewYinJi)
    ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
    NewYinJiJiChengWGData.Instance = nil
end

function NewYinJiJiChengWGData:GetEquipYinJiOtherCfg()
    return self.equip_yinji_cfg.other[1]
end

function NewYinJiJiChengWGData:GetKongWeiMapCfg()
    return self.kongwei_map_cfg
end

function NewYinJiJiChengWGData:GetKongWiePosCfgByKongWeiPosID(kongwei_pose_id)
    return self.kongwei_pos_cfg[kongwei_pose_id]
end

function NewYinJiJiChengWGData:GetKeYinKongWeiPosCfg()
    return self.equip_yinji_cfg.zuobiao or {}
end

function NewYinJiJiChengWGData:GetKeYinKongWeiLineCfg()
    return self.equip_yinji_cfg.lianxian or {}
end

-- 获取部位镶嵌限制配置
function NewYinJiJiChengWGData:GetPartPutLimitCfgByEquipPart(part)
    local cfg_list = self.equip_yinji_cfg.part_put_limit
    for k,v in pairs(cfg_list) do
        if v.part == part then
            return v
        end
    end
end

-- 获取刻印符镶嵌限制配置
function NewYinJiJiChengWGData:GetKeYinFuPutLimitCfgByEquipPart(part, keyinfu_type)
    if self.keyinfu_put_limit_cfg and self.keyinfu_put_limit_cfg[part] and self.keyinfu_put_limit_cfg[keyinfu_type] then
        return self.keyinfu_put_limit_cfg[part][keyinfu_type]
    end
end

-- 获取印记激活属性配置
function NewYinJiJiChengWGData:GetYinJiActAttrCfgByType(part, order, color)
    return CheckList(self.yinji_act_attr_cfg, part, order, color)
end

-- 获取镶嵌孔位配置
function NewYinJiJiChengWGData:GetPutKongWeiCfgByEquipPart(part, kongwei_id)
    return self.put_kongwei_cfg[part] and self.put_kongwei_cfg[part][kongwei_id]
end

-- 获取镶嵌孔位配置列表
function NewYinJiJiChengWGData:GetPutKongWeiCfgByEquipPartList(part)
    return self.put_kongwei_cfg[part]
end

-- 获取镶嵌数量配置列表
function NewYinJiJiChengWGData:GetPutkeyInfuNumByKongWeiID(kongwei_id)
    return self.put_keyinfu_num_cfg[kongwei_id]
end

-- 获取镶嵌数量配置
function NewYinJiJiChengWGData:GetPutKeYinFuNumByOrderAndColorAndStarLevel(order, color, star_level)
    local cfg_list = self.equip_yinji_cfg.put_keyinfu_num

    for i=#cfg_list,1,-1 do
        if order >= cfg_list[i].order and color >= cfg_list[i].color and star_level >= cfg_list[i].star then
            return cfg_list[i]
        end
    end
end

-- 获取印刻符合配置(根据id)
function NewYinJiJiChengWGData:GetKeYinFuByItemID(item_id)
    return self.keyinfu_cfg[item_id]
end

-- 获取印刻符合配置（根据攻击防御类型和刻印符类型）
function NewYinJiJiChengWGData:GetKeYinFuByFightTypeAndKeYinFuType(fight_type, keyinfu_type)
    for k,v in pairs(self.equip_yinji_cfg.keyinfu) do
        if v.fight_type == fight_type and v.keyinfu_type == keyinfu_type then
            return v
        end
    end
end

-- 获取印刻符合成配置
function NewYinJiJiChengWGData:GetKeYinFuComposeByItemID(item_id)
    local cfg_list = self.equip_yinji_cfg.keyinfu_compose
    for i=1,#cfg_list do
        if cfg_list[i].use_item_id == item_id then
            return cfg_list[i]
        end
    end
end

function NewYinJiJiChengWGData:GetTargetKeYinFuComposeCfgByItemID(item_id)
    return self.keyinfu_target_compose_cfg[item_id]
end

-- 左侧列表数据
function NewYinJiJiChengWGData:GetEquipYinJiEquipList()
    local bag_data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表
    local equip_list = {}
    if not bag_data_list then 
        return equip_list 
	end

    local index = 1
	for k, v in pairs(bag_data_list) do
		if v.index < GameEnum.EQUIP_INDEX_HUNJIE then
            index = EquipmentWGData.GetSortIndex(v.index)
            local tmep = {}
			for k1,v1 in pairs(v) do
				tmep[k1] = v1
			end
            tmep.is_wear = true	-- 穿戴装备
            tmep.data_list = self:GetYinJiInfoByEquipPart(v.index)
            local is_active, log_type, limit_cfg = self:GetEquipIsEnoughLimitCondition(v)
            tmep.is_active = is_active
            tmep.log_type = log_type
            tmep.limit_cfg = limit_cfg
			tmep.sort_index = index
            equip_list[index] = tmep
        end
	end

    equip_list = SortTableKey(equip_list)

    return equip_list
end

--获取装备是否满足最小刻印条件
--log_type： 1 = 角色等级不足   2 = 装备阶数不足    3 = 装备品质不足    4 = 装备星数不足
function NewYinJiJiChengWGData:GetEquipIsEnoughLimitCondition(equip_data)
    local is_enough_condition = false
    local log_type = 0

    if not equip_data then
        return is_enough_condition, log_type
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)

    if not item_cfg then
        return is_enough_condition, log_type
    end

    local limit_cfg = self:GetPartPutLimitCfgByEquipPart(equip_data.index)

    if not limit_cfg then
        return is_enough_condition, log_type
    end

    local role_level = RoleWGData.Instance:GetRoleLevel()

    if role_level < limit_cfg.role_level_limit then
        log_type = 1
        return is_enough_condition, log_type, limit_cfg
    end

    if item_cfg.order < limit_cfg.order then
        log_type = 2
        return is_enough_condition, log_type, limit_cfg
    end

    if item_cfg.color < limit_cfg.color then
        log_type = 3
        return is_enough_condition, log_type, limit_cfg
    end

    if equip_data.param.star_level < limit_cfg.star then
        log_type = 4
        return is_enough_condition, log_type, limit_cfg
    end

    is_enough_condition = true
    return is_enough_condition, log_type, limit_cfg
end

--获取装备印记空位是否满足可以条件（是否可操作）
--log_type： 1 = 装备阶数不足    2 = 装备品质不足    3 = 装备星数不足
function NewYinJiJiChengWGData:GetYinJiCellIsEnoughLimitCondition(equip_data, kongwei_id)
    if not equip_data or not kongwei_id then
        return false, 0
    end

    ---[[ 打开一次界面调用九千多次,缓存一下只判断星级和itemid有没有变化
    local cache_data = self.cache_yj_condition[equip_data.index]
    if cache_data and cache_data.item_id == equip_data.item_id and cache_data.star_level == equip_data.param.star_level then
        local condition = cache_data.condition_list[kongwei_id]
        if condition then
            return condition[1], condition[2], condition[3]
        end
    else
        cache_data = {condition_list = {}, item_id = equip_data.item_id, star_level = equip_data.param.star_level}
    end
    --]]

    local is_enough_condition = false
    local log_type = 0
    local limit_cfg = nil

    local function cache_func()
        cache_data.condition_list[kongwei_id] = {is_enough_condition, log_type, limit_cfg}
        self.cache_yj_condition[equip_data.index] = cache_data
    end
    
    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    if not item_cfg then
        cache_func()
        return is_enough_condition, log_type, limit_cfg
    end

    limit_cfg = self:GetPutKongWeiCfgByEquipPart(equip_data.index, kongwei_id)
    if not limit_cfg then
        cache_func()
        return is_enough_condition, log_type, limit_cfg
    end

    if item_cfg.order < limit_cfg.order then
        log_type = 1
        cache_func()
        return is_enough_condition, log_type, limit_cfg
    end

    if item_cfg.color < limit_cfg.color then
        log_type = 2
        cache_func()
        return is_enough_condition, log_type, limit_cfg
    end

    if equip_data.param.star_level < limit_cfg.star then
        log_type = 3
        cache_func()
        return is_enough_condition, log_type, limit_cfg
    end

    is_enough_condition = true
    cache_func()

    return is_enough_condition, log_type, limit_cfg
end

--设置部位拥有刻印符数量 {[part][keyinfu_type] = num}
function NewYinJiJiChengWGData:SetYinJiQualitNumList(equip_part)
    self.yinji_qualit_num_list[equip_part] = {}
    self.yinji_qualit_num_list2[equip_part] = {}

    for i=1,4 do
        self.yinji_qualit_num_list2[equip_part][i] = 0
        self.yinji_qualit_num_list[equip_part][i] = 0
    end

    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)
    if not yinji_info or not equip_data then
        return 
    end

    local max_kongwei_id = self:GetCurEquipMaxKongWeiID(equip_data)

    for k,v in pairs(yinji_info.kong_wei_info_sc) do
        if v.item_id > 0 then
            local keyinfu_cfg = self:GetKeYinFuByItemID(v.item_id)
            if keyinfu_cfg then
                self.yinji_qualit_num_list[equip_part][keyinfu_cfg.keyinfu_type] = self.yinji_qualit_num_list[equip_part][keyinfu_cfg.keyinfu_type] + 1
            
                if v.kongwei_id <= max_kongwei_id then
                    for i=1,keyinfu_cfg.keyinfu_type do
                        self.yinji_qualit_num_list2[equip_part][i] = self.yinji_qualit_num_list2[equip_part][i] + 1
                    end
                end
            end
        end
    end
end

--获取部位拥有刻印符数量 {[part][keyinfu_type] = num}
--self.yinji_qualit_num_list-- 刻印符数量 是多少就是多少
--self.yinji_qualit_num_list2-- 刻印符数量 向下兼容后的数量
function NewYinJiJiChengWGData:GetYinJiQualitNumList(equip_part)
    return self.yinji_qualit_num_list[equip_part], self.yinji_qualit_num_list2[equip_part]
end

--设置背包中刻印符列表
function NewYinJiJiChengWGData:SetBagKeYinFuList()
    local bag_keyinfu_list = {}
    local function new_info(fight_type, keyinfu_type)
        if not bag_keyinfu_list[fight_type] then
            bag_keyinfu_list[fight_type] = {}
        end
        if not bag_keyinfu_list[fight_type][keyinfu_type] then
            bag_keyinfu_list[fight_type][keyinfu_type] = {}
            bag_keyinfu_list[fight_type][keyinfu_type].num = 0
        end
        return bag_keyinfu_list[fight_type][keyinfu_type]
    end

    local stuff_bag_list = ItemWGData.Instance:GetStuffStorgeItemData()
    local temp_info = nil
    for k,v in pairs(stuff_bag_list) do
        local keyinfu_cfg = self:GetKeYinFuByItemID(v.item_id)
        if keyinfu_cfg then
            temp_info = new_info(keyinfu_cfg.fight_type, keyinfu_cfg.keyinfu_type)
            temp_info.item_id = v.item_id
            temp_info.keyinfu_type = keyinfu_cfg.keyinfu_type
            temp_info.num = temp_info.num + v.num
        end
    end

    self.bag_keyinfu_list = bag_keyinfu_list
    EquipmentWGCtrl.Instance:FlushEquipmentMarkView()
end

--获取背包中刻印符列表
function NewYinJiJiChengWGData:GetBagKeYinFuListByFightType(fight_type)
    return self.bag_keyinfu_list[fight_type]
end

function NewYinJiJiChengWGData:GetSortBagKeYinFuListByFightType(fight_type)
    local list = {}
    local bag_list = self:GetBagKeYinFuListByFightType(fight_type)
    if not bag_list then
        return list
    end
    
    for k,v in pairs(bag_list) do
        if v.num > 0 then
            list[#list + 1] = v
        end
    end

    table.sort(list, SortTools.KeyUpperSorter("keyinfu_type"))
    return list
end

--获取当前装备开启的最大孔位
function NewYinJiJiChengWGData:GetCurEquipMaxKongWeiID(equip_data)
    local max_kongwei_id = 0

    if not equip_data then
        return max_kongwei_id, 0
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    if not item_cfg then
        return max_kongwei_id, 0
    end

    local star_level = equip_data.param and equip_data.param.star_level or 0

    local put_keyinfu_num_cfg = self:GetPutKeYinFuNumByOrderAndColorAndStarLevel(item_cfg.order, item_cfg.color, star_level)
    if not put_keyinfu_num_cfg then
        return max_kongwei_id, 0
    end

    max_kongwei_id = put_keyinfu_num_cfg.max_kongwei_id
    return max_kongwei_id, put_keyinfu_num_cfg.xianshi
end

--当前刻印符文本
function NewYinJiJiChengWGData:GetKeYinFuNumStrByEquipPart(equip_part)
    if not self.yinji_qualit_num_list[equip_part] then
        return "", {}
    end

    local equip_data = EquipWGData.Instance:GetGridData(equip_part)
    if not equip_data then
        return "", {}
    end

    local max_kongwei_id = self:GetCurEquipMaxKongWeiID(equip_data)
    local count = 0

    local qualit_num_list = self.yinji_qualit_num_list[equip_part]
    local qualit_str_list = {}
    for i=1,#qualit_num_list do
        if qualit_num_list[i] > 0 then
            local yinji_str = string.format("%d%s", qualit_num_list[i], Language.NewEquipYinJi.YinJiQuality[i])
            qualit_str_list[#qualit_str_list + 1] = ToColorStr(yinji_str, ITEM_COLOR[i + 2])
            count = count + qualit_num_list[i]
        end
    end

    local temp_str = string.format(Language.NewEquipYinJi.KeYinJinDu, count, max_kongwei_id)

    return temp_str, qualit_str_list
end

--背包刻印符虚假信息（身上没有同类型刻印符时用）
function NewYinJiJiChengWGData:CreatFakeKeYinFuBagList()
    local one_keyinfu_cfg = self.equip_yinji_cfg.keyinfu[1]
    local index = 0
    local fake_list = {}
    for i=1,2 do
        fake_list[i] = {}
        for j=1,4 do
            fake_list[i][j] = {}
            fake_list[i][j].item_id = one_keyinfu_cfg.item_id + index
            fake_list[i][j].num = 1
            fake_list[i][j].keyinfu_type = j
            fake_list[i][j].is_fake = true
            index = index + 1
        end
        table.sort(fake_list[i], SortTools.KeyUpperSorter("keyinfu_type"))
    end
    self.fake_keyinfu_bag_list = fake_list
end

--获取背包刻印符虚假信息（身上没有同类型刻印符时用）
function NewYinJiJiChengWGData:GetFakeKeYinFuBagListByFightType(fight_type)
    return self.fake_keyinfu_bag_list[fight_type]
end

-- 获取当前刻印符总属性
function NewYinJiJiChengWGData:GetCurPartAllKeYinFuAttrStr(equip_part)
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    if not yinji_info then
        return {}, {}
    end

    local total_attr_list = self:GetEquipKeYinFuTotalAttrList(yinji_info.kong_wei_info_sc)
    local attr_name_list = Language.Common.TipsAttrNameList
    local sort_list = AttributeMgr.SortAttribute()

    local attr_list = {}
    for i,v in ipairs(sort_list) do
        if total_attr_list[v] > 0 then
            local tmep = {attr_str = v, attr_value = total_attr_list[v]}
            -- if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v) then
            --     tmep.value = tmep.value / 100 .. "%"
            -- end
            attr_list[#attr_list + 1] = tmep
        end
    end

    return attr_list, total_attr_list
end

function NewYinJiJiChengWGData:GetEquipKeYinFuTotalAttrList(keyinfu_list)
    if IsEmptyTable(keyinfu_list) then
        return {}
    end

    local total_attr_list = AttributePool.AllocAttribute()
    local item_list = {}
    for i,v in ipairs(keyinfu_list) do
        if v.item_id > 0 then
            item_list[v.item_id] = item_list[v.item_id] or 0
            item_list[v.item_id] = item_list[v.item_id] + 1
        end
    end
    
    for item_id,num in pairs(item_list) do
        local keyinfu_cfg = self:GetKeYinFuByItemID(item_id)
        if keyinfu_cfg then
            for cfg_key,attr_key in pairs(self.keyinfu_attr_key) do
                total_attr_list[attr_key] = total_attr_list[attr_key] + num * keyinfu_cfg[cfg_key]
            end
        end
    end

    return total_attr_list
end

--获取已激活印记属性
function NewYinJiJiChengWGData:GetCurPartActivedYinJiAttr(equip_part)
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    return self:GetActivedYinJiAttrByData(equip_data, yinji_info and yinji_info.has_act_yinji_type)
end

function NewYinJiJiChengWGData:GetActivedYinJiAttrByData(equip_data, yinji_type)
    if not equip_data or not yinji_type then
        return {}
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    if not item_cfg then
        return {}
    end

    local yinji_act_attr_cfg_list = self:GetYinJiActAttrCfgByType(equip_data.index, item_cfg.order, item_cfg.color)
    if not yinji_act_attr_cfg_list or not yinji_act_attr_cfg_list[yinji_type] then
        return {}
    end

    local yinji_act_attr = yinji_act_attr_cfg_list[yinji_type]
    local total_attr_list = AttributeMgr.GetAttributteByClass(yinji_act_attr)

    return total_attr_list
end

--获取部位刻印符和激活印记总属性
function NewYinJiJiChengWGData:GetCurPartKeYinFuAndYinJiAttrAllAttr(equip_part, keyinfu_attr)
    if not keyinfu_attr then
        local _,temp_attr = self:GetCurPartAllKeYinFuAttrStr(equip_part)
        keyinfu_attr = temp_attr
    end

    local yinji_act_attr = self:GetCurPartActivedYinJiAttr(equip_part)
    local yinji_attr = AttributeMgr.GetAttributteValueByClass(yinji_act_attr)
    local total_attr_list = AttributeMgr.AddAttributeAttr(keyinfu_attr, yinji_attr)

    return total_attr_list
end

--设置当前选中部位
function NewYinJiJiChengWGData:SetCurSelectEquipPart(cur_select_equip_part)
    self.cur_select_equip_part = cur_select_equip_part
end

function NewYinJiJiChengWGData:GetCurSelectEquipPart()
    return self.cur_select_equip_part
end

--设置当前选中孔位
function NewYinJiJiChengWGData:SetCurSelectKongWei(cur_select_kongwei)
    self.cur_select_kongwei = cur_select_kongwei
end

function NewYinJiJiChengWGData:GetCurSelectKongWei()
    return self.cur_select_kongwei
end

-- 装备是否满足刻印符镶嵌条件
function NewYinJiJiChengWGData:GetEquipIsEnoughKeYinFuLimit(equip_data, keyinfu_type)
    local is_enough = false
    if not equip_data then
        return is_enough
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)

    if not item_cfg then
        return is_enough
    end

    local keyinfu_limit_cfg = self:GetKeYinFuPutLimitCfgByEquipPart(equip_data.index, keyinfu_type)
     
    if not keyinfu_limit_cfg then
        return is_enough
    end

    local star_level = equip_data.param and equip_data.param.star_level or 0

    if item_cfg.order < keyinfu_limit_cfg.order or item_cfg.color < keyinfu_limit_cfg.color or star_level < keyinfu_limit_cfg.star then
        return is_enough, keyinfu_limit_cfg
    end

    is_enough = true
    return is_enough
end
--------------------------------------------------------协议下发东西----------------------------------------------------------
-- 10458 装备印刻信息
function NewYinJiJiChengWGData:SetSCEquipYinJiInfo(protocol)
    self.yin_ji_part_info_sc = protocol.yin_ji_part_info_sc

    for k,v in pairs(protocol.yin_ji_part_info_sc) do
        self:SetYinJiQualitNumList(k)
    end

    self:CreatKeYinFuConversionTableInBag()
    self:SetBagKeYinFuList()
end

-- 10459 刻印符镶嵌成功返回
function NewYinJiJiChengWGData:SetSCEquipYinJiPut(protocol)
    if not self.yin_ji_part_info_sc[protocol.part] then
        return
    end
    self.yin_ji_part_info_sc[protocol.part].kong_wei_info_sc[protocol.kong_wei_info.kongwei_id] = protocol.kong_wei_info
    self.yin_ji_part_info_sc[protocol.part].has_act_yinji_type = protocol.has_act_yinji_type
    self:SetYinJiQualitNumList(protocol.part)
end

-- 10460 印记激活成功返回
function NewYinJiJiChengWGData:SetSCEquipYinJiAct(protocol)
    if not self.yin_ji_part_info_sc[protocol.part] then
        return
    end
    self.yin_ji_part_info_sc[protocol.part].has_act_yinji_type = protocol.yinji_type
end

-- 10461 部位更换装备
function NewYinJiJiChengWGData:SetSCEquipYinJiChangeEquip(protocol)
    if not self.yin_ji_part_info_sc[protocol.part] then
        return
    end
    self.yin_ji_part_info_sc[protocol.part].has_act_yinji_type = protocol.has_act_yinji_type
    self.yin_ji_part_info_sc[protocol.part].has_act_max_kongwei_id = protocol.has_act_max_kongwei_id
    self:SetYinJiQualitNumList(protocol.part)
end

-- 10462 刻印符升级(合成)成功
function NewYinJiJiChengWGData:SetSCEquipYinJiKeYinFuUpLevel(protocol)
    if not self.yin_ji_part_info_sc[protocol.part] then
        return
    end
    self.yin_ji_part_info_sc[protocol.part].kong_wei_info_sc[protocol.kong_wei_info.kongwei_id] = protocol.kong_wei_info
    self:SetYinJiQualitNumList(protocol.part)
end

--获取部位印记信息
function NewYinJiJiChengWGData:GetYinJiInfoByEquipPart(equip_part)
    return self.yin_ji_part_info_sc[equip_part]
end

function NewYinJiJiChengWGData:GetKongWeoInfo(equip_part, kongwei_id)
    return CheckList(self.yin_ji_part_info_sc, equip_part, "kong_wei_info_sc", kongwei_id)
end

----------------------------------------------红点-------------------------------------------------------------

function NewYinJiJiChengWGData:GetCurPartIsCanActiveYinJiAttr(equip_data)
    local is_can_active = 0
    local yinji_type = 0
    local index = 0

    if not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return is_can_active, yinji_type, index
    end

    local _, yinji_qualit_num_list = self:GetYinJiQualitNumList(equip_data.index)
    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    if not item_cfg or IsEmptyTable(yinji_qualit_num_list) then
        return is_can_active, yinji_type, index
    end

    local yinji_info = self:GetYinJiInfoByEquipPart(equip_data.index)
    local yinji_attr_cfg = self:GetYinJiActAttrCfgByType(equip_data.index, item_cfg.order, item_cfg.color)
    if not yinji_info or not yinji_attr_cfg then
        return is_can_active, yinji_type, index
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)

    if not item_cfg then

        return is_can_active, yinji_type, index
    end

    local max_kongwei_id = self:GetCurEquipMaxKongWeiID(equip_data)

    if max_kongwei_id <= 0 then

        return is_can_active, yinji_type, index
    end

    --未激活 and 装备满足条件 and 所有开启的空孔位镶嵌满
    for i=#yinji_attr_cfg,1,-1 do
        local v = yinji_attr_cfg[i]

        if yinji_info.has_act_yinji_type < v.yinji_type then
            if item_cfg.order >= v.order and item_cfg.color >= v.color then

                if yinji_qualit_num_list[v.yinji_type] >= max_kongwei_id  then
                    is_can_active = 1
                    yinji_type = v.yinji_type
                    index = v.yinji_type
                    return is_can_active, v.yinji_type, index
                end
            end
        else
            index = yinji_info.has_act_yinji_type
        end
    end

    return is_can_active, yinji_type, index
end

-- 获取刻印符是否可替换
function NewYinJiJiChengWGData:GetCurKeYinFuIsCanTiHuan(equip_part, kongwei_id)
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)
    local is_can_tihuan = 0
    local keyinfu_type = 0

    if not yinji_info or not equip_data then
        return is_can_tihuan, keyinfu_type
    end

    if not self:GetYinJiCellIsEnoughLimitCondition(equip_data, kongwei_id) then
        return is_can_tihuan, keyinfu_type
    end

    local keyinfu_info = yinji_info.kong_wei_info_sc[kongwei_id]
    local keyinfu_cfg = self:GetKeYinFuByItemID(keyinfu_info.item_id)
    --背包内有大于当前刻印符类型的刻印符 and 装备满足穿戴条件 and 当前刻印符类型不是最高阶
    if not keyinfu_cfg or keyinfu_cfg.keyinfu_type >= KEYINFU_MAX_TYPE then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    if not item_cfg then
        return is_can_tihuan, keyinfu_type
    end

    local bag_list = self:GetBagKeYinFuListByFightType(keyinfu_cfg.fight_type)
    if bag_list then
        for k,v in pairs(bag_list) do
            if k > keyinfu_cfg.keyinfu_type and v.num > 0 then
                local keyinfu_put_limit_cfg = self:GetKeYinFuPutLimitCfgByEquipPart(equip_part, k)
                local star_level = equip_data.param and equip_data.param.star_level or 0
                if keyinfu_put_limit_cfg and item_cfg.order >= keyinfu_put_limit_cfg.order and 
                    item_cfg.color >= keyinfu_put_limit_cfg.color and star_level >= keyinfu_put_limit_cfg.star then

                    keyinfu_type = keyinfu_cfg.keyinfu_type
                    is_can_tihuan = 1
                    return is_can_tihuan, keyinfu_type
                end
            end
        end
    end

    return is_can_tihuan, keyinfu_type
end

-- 获取当前刻印孔是否有材料可以刻印
function NewYinJiJiChengWGData:GetCurKeYinFuIsCanInPut(equip_part, kongwei_id)
    local is_can_input = 0
    local empty_kongwei_id = 0

    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not yinji_info or not equip_data then
        return is_can_input, empty_kongwei_id
    end

    if not self:GetYinJiCellIsEnoughLimitCondition(equip_data, kongwei_id) then
        return is_can_input, empty_kongwei_id
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)

    if not item_cfg then
        return is_can_input, empty_kongwei_id
    end

    local keyinfu_info = yinji_info.kong_wei_info_sc[kongwei_id]
    --当前刻印孔是为空 and 背包有满足穿戴条件的刻印符
    if keyinfu_info.item_id <= 0 then
        empty_kongwei_id = kongwei_id
        local put_kongwei_cfg = self:GetPutKongWeiCfgByEquipPart(equip_part, kongwei_id)
        if not put_kongwei_cfg then
            return is_can_input, empty_kongwei_id
        end

        local bag_list = self:GetBagKeYinFuListByFightType(put_kongwei_cfg.fight_type)
        if bag_list then
            for k,v in pairs(bag_list) do
                local keyinfu_put_limit_cfg = self:GetKeYinFuPutLimitCfgByEquipPart(equip_part, k)
                local star_level = equip_data.param and equip_data.param.star_level or 0
                if keyinfu_put_limit_cfg and item_cfg.order >= keyinfu_put_limit_cfg.order and 
                    item_cfg.color >= keyinfu_put_limit_cfg.color and star_level >= keyinfu_put_limit_cfg.star then

                    is_can_input = 1
                    return is_can_input, empty_kongwei_id
                end
            end
        end
    end

    return is_can_input, empty_kongwei_id
end

-- 获取当前刻印孔是否有材料可以合成
function NewYinJiJiChengWGData:GetCurKeYinFuIsCanCompse(equip_part, kongwei_id)
    local is_can_compose = 0
    local temp_keyinfu_type = 0

    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not yinji_info or not equip_data then
        return is_can_compose, temp_keyinfu_type
    end

    if not self:GetYinJiCellIsEnoughLimitCondition(equip_data, kongwei_id) then
        return is_can_compose, temp_keyinfu_type
    end

    local keyinfu_info = yinji_info.kong_wei_info_sc[kongwei_id]
    local keyinfu_cfg = self:GetKeYinFuByItemID(keyinfu_info.item_id)
    --当前刻印符类型不是最高阶 and 本身刻印符 + 背包内刻印符 大于 需要的刻印符
    if keyinfu_cfg and keyinfu_cfg.keyinfu_type < KEYINFU_MAX_TYPE then
        local cur_keyinfu_num = self:GetKeYinFuConversionTableByid(keyinfu_info.item_id)
        local bag_keyinfu_num = self:GetKeYinFuConversionTableInBagByFightType(keyinfu_cfg.fight_type) or 0

        local target_keyinfu = self:GetKeYinFuComposeByItemID(keyinfu_info.item_id)
        if target_keyinfu then
            local need_keyinfu_num = self:GetKeYinFuConversionTableByid(target_keyinfu.target_item_id)
            if cur_keyinfu_num.num + bag_keyinfu_num >= need_keyinfu_num.num then
                is_can_compose = 1
                temp_keyinfu_type = keyinfu_cfg.keyinfu_type
                return is_can_compose, temp_keyinfu_type
            end
        end
    end

    return is_can_compose, temp_keyinfu_type
end

--当前装备是否有刻印符可替换
-- remind_kongwei_id 最低类型得可替换孔位
function NewYinJiJiChengWGData:GetEquipKeYinFuIsCanTiHuan(equip_part)
    local is_can_tihuan = 0
    local remind_kongwei_id = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)
    local temp_keyinfu_type = nil

    if not equip_data or not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return is_can_tihuan, remind_kongwei_id, temp_keyinfu_type
    end
    
    local put_kongwei_cfglist = self:GetPutKongWeiCfgByEquipPartList(equip_part)

    for k,v in pairs(put_kongwei_cfglist) do
        local keyinfu_is_can_tihuan, keyinfu_type = self:GetCurKeYinFuIsCanTiHuan(v.part, v.kongwei_id)
        if keyinfu_is_can_tihuan == 1 then
            is_can_tihuan = 1
            -- return is_can_tihuan, remind_kongwei_id
            if not temp_keyinfu_type and temp_keyinfu_type ~= 0 then
                remind_kongwei_id = v.kongwei_id
                temp_keyinfu_type = keyinfu_type
            else
                if temp_keyinfu_type ~= 0 and temp_keyinfu_type > keyinfu_type then
                    remind_kongwei_id = v.kongwei_id
                    temp_keyinfu_type = keyinfu_type
                end
            end
        end
    end

    return is_can_tihuan, remind_kongwei_id, temp_keyinfu_type
end

--当前装备是否有刻印孔可镶嵌
function NewYinJiJiChengWGData:GetEquipKeYinFuIsCanInPut(equip_part)
    local is_can_input = 0
    local empty_kongwei = 0
    local remind_kongwei_id = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not equip_data or not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return is_can_input, empty_kongwei, remind_kongwei_id
    end
    
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)

    if not yinji_info then
        return is_can_input, empty_kongwei, remind_kongwei_id
    end

    for k,v in pairs(yinji_info.kong_wei_info_sc) do
        local kongwei_is_can_input, empty_kongwei_id = self:GetCurKeYinFuIsCanInPut(v.equip_part, v.kongwei_id)
        if empty_kongwei_id > 0 and empty_kongwei == 0 then
            empty_kongwei = empty_kongwei_id
        end

        if kongwei_is_can_input == 1 then
            is_can_input = 1
            remind_kongwei_id = v.kongwei_id
            return is_can_input, empty_kongwei, remind_kongwei_id
        end
    end

    return is_can_input, empty_kongwei, remind_kongwei_id
end

--当前装备是否有刻印符可合成
function NewYinJiJiChengWGData:GetEquipKeYinFuIsCanCompose(equip_part)
    local is_can_compose = 0
    local is_can_compose_id = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)
    local temp_keyinfu_type = nil

    if not equip_data or not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return is_can_compose, is_can_compose_id, temp_keyinfu_type
    end
    
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)

    if not yinji_info then
        return is_can_compose, is_can_compose_id, temp_keyinfu_type
    end

    for k,v in pairs(yinji_info.kong_wei_info_sc) do
        local keyinfu_is_can_compose, keyinfu_type = self:GetCurKeYinFuIsCanCompse(v.equip_part, v.kongwei_id)
        if keyinfu_is_can_compose == 1 then
            is_can_compose = 1
            -- return is_can_compose, is_can_compose_id

            if not temp_keyinfu_type and keyinfu_type ~= 0 then
                is_can_compose_id = v.kongwei_id
                temp_keyinfu_type = keyinfu_type
            else
                if temp_keyinfu_type > keyinfu_type and keyinfu_type ~= 0 then
                    is_can_compose_id = v.kongwei_id
                    temp_keyinfu_type = keyinfu_type
                end
            end
        end
    end

    return is_can_compose, is_can_compose_id, temp_keyinfu_type
end

function NewYinJiJiChengWGData:GetEquipYinJiRemindByPart(equip_part)
    local is_remind = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not equip_data then

        return is_remind
    end

    local is_can_input, empty_kongwei, remind_kongwei_id = self:GetEquipKeYinFuIsCanInPut(equip_data.index)

    if is_can_input == 1 then
        is_remind = 1
        return is_remind, empty_kongwei, remind_kongwei_id
    end

    local is_can_tihuan, remind_kongwei_id = self:GetEquipKeYinFuIsCanTiHuan(equip_data.index)

    if is_can_tihuan == 1 then
        is_remind = 1
        return is_remind, empty_kongwei, remind_kongwei_id
    end

    local is_can_compose, remind_kongwei_id = self:GetEquipKeYinFuIsCanCompose(equip_data.index)

    if is_can_compose == 1 then
        is_remind = 1
        return is_remind, empty_kongwei, remind_kongwei_id
    end

    local is_can_active = self:GetCurPartIsCanActiveYinJiAttr(equip_data)

    if is_can_active == 1 then
        is_remind = 1
        return is_remind, empty_kongwei, remind_kongwei_id
    end

    return is_remind, empty_kongwei, remind_kongwei_id
end

--获取装备印记红点， 红点部位， 红点孔位
--没红点选中第一个空孔位
--没空孔位选中第一个部位的第一个孔位
function NewYinJiJiChengWGData:GetEquipYinJiRemind()
    local is_remind = 0
    local equip_index = 1
    local kongwei_id = 0
    local empty_kongwei_id = 0
    local empty_kongwei_index = 0
    local mind_keyinfu_type = nil

    local bag_data_list = self:GetEquipYinJiEquipList()

    for i,v in ipairs(bag_data_list) do
        local is_can_active = self:GetCurPartIsCanActiveYinJiAttr(v)

        if is_can_active == 1 then
            is_remind = 1
            equip_index = i
            kongwei_id = 1
            -- return is_remind, equip_index, kongwei_id
            break
        end
    end

    if is_remind ~= 1 then
        for i,v in pairs(bag_data_list) do
            local is_can_input, empty_kongwei, remind_kongwei_id = self:GetEquipKeYinFuIsCanInPut(v.index)
            if empty_kongwei > 0 and empty_kongwei_id == 0 then
                empty_kongwei_id = empty_kongwei
                empty_kongwei_index = i
            end

            if is_can_input == 1 then
                is_remind = 1
                equip_index = i
                kongwei_id = remind_kongwei_id
                -- return is_remind, equip_index, kongwei_id
                break
            end
        end
    end
    
    if is_remind ~= 1 then
        for i,v in ipairs(bag_data_list) do
            local is_can_tihuan, remind_kongwei_id, keyinfu_type = self:GetEquipKeYinFuIsCanTiHuan(v.index)

            if is_can_tihuan == 1 then
                is_remind = 1

                -- return is_remind, equip_index, kongwei_id
                -- break
                if not mind_keyinfu_type and keyinfu_type then
                    mind_keyinfu_type = keyinfu_type
                    equip_index = i
                    kongwei_id = remind_kongwei_id
                else
                    if keyinfu_type and mind_keyinfu_type > keyinfu_type then
                        mind_keyinfu_type = keyinfu_type
                        equip_index = i
                        kongwei_id = remind_kongwei_id
                    end
                end
            end
        end
    end

    if is_remind ~= 1 then
        for i,v in ipairs(bag_data_list) do
            local is_can_compose, remind_kongwei_id, keyinfu_type = self:GetEquipKeYinFuIsCanCompose(v.index)

            if is_can_compose == 1 then
                is_remind = 1
                equip_index = i
                -- return is_remind, equip_index, kongwei_id
                -- break
                if not mind_keyinfu_type and keyinfu_type then
                    mind_keyinfu_type = keyinfu_type
                    equip_index = i
                    kongwei_id = remind_kongwei_id
                else
                    if keyinfu_type and mind_keyinfu_type > keyinfu_type then
                        mind_keyinfu_type = keyinfu_type
                        equip_index = i
                        kongwei_id = remind_kongwei_id
                    end
                end
            end
        end
    end

    if is_remind ~= 1 then
        if empty_kongwei_id > 0 then
            kongwei_id = empty_kongwei_id
            equip_index = empty_kongwei_index
        else
            kongwei_id = 1
            equip_index = 1
        end
    end

    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_YINJI, is_remind, function ()
        FunOpen.Instance:OpenViewByName(GuideModuleName.EquipmentMarkView)
        return true
    end)

    return is_remind, equip_index, kongwei_id
end
---------------------------------------------监听--------------------------------------------------------------
function NewYinJiJiChengWGData:OnItemChangeCallBack(change_item_id)
    local keyinfu_cfg = self:GetKeYinFuByItemID(change_item_id)
    if not keyinfu_cfg then
        return
    end
    self:SetKeYinFuConversionTableInBag(keyinfu_cfg.fight_type)
    self:SetBagKeYinFuList()
end

---------------------------------------------------换算-------------------------------------------------------------
--创建刻印符计算列表
function NewYinJiJiChengWGData:CtreKeYiFuConversionTable()
	self.keyinfu_table = {}
	for i,v in ipairs(self.equip_yinji_cfg.keyinfu) do
		local need_num = 0
		local item_id = v.item_id
		self.keyinfu_table[item_id] = {}
		local keyinfu_compose_cfg = self:GetTargetKeYinFuComposeCfgByItemID(item_id)
		if keyinfu_compose_cfg then
			if v.keyinfu_type <= 1 then
				need_num = self:KeYinFuConversion(v.keyinfu_type, 1)
			else
				need_num = self:KeYinFuConversion(v.keyinfu_type, keyinfu_compose_cfg.use_item_num)
			end
			self.keyinfu_table[item_id].num = need_num
		else
			if v.keyinfu_type == 1 then
                self.keyinfu_table[item_id].num = 1
			end
		end
    end
end

--刻印符类型，每级需要多少个
--根据等级换算成1级刻印符
function NewYinJiJiChengWGData:KeYinFuConversion(keyinfu_type, need_num)
	if not keyinfu_type then
		return 0
	end

    if keyinfu_type <= 1 then
        return 1
    end

	local num = 1      
	local keyinfu_type = keyinfu_type
    for i=1,100 do
        keyinfu_type = keyinfu_type - 1
        num = num * need_num
        if keyinfu_type < 2 then
            break
        end
    end

	return num
end

function NewYinJiJiChengWGData:GetKeYinFuConversionTableByid(item_id)
	return self.keyinfu_table[item_id]
end

function NewYinJiJiChengWGData:WaitSetKeYinFuConversionTableInBag()
    if not self.delay_set_table and not self.have_delay_set_table then
        self.delay_set_table = GlobalTimerQuest:AddDelayTimer(function ()
            for i=1,2 do
                self:SetKeYinFuConversionTableInBag(i)
            end
            RemindManager.Instance:Fire(RemindName.Equipment_NewYinJi)
            self.have_delay_set_table = true
            GlobalTimerQuest:CancelQuest(self.delay_set_table)
            self.delay_set_table = nil
        end, 3)
    end
end

--把背包中的刻印符换算成1级的的数量
function NewYinJiJiChengWGData:SetKeYinFuConversionTableInBag(fight_type)
    self.keyinfu_conversion_in_bag[fight_type] = 0
	local need_num = 0
	local bag_list = ItemWGData.Instance:GetStuffStorgeItemData()
    if IsEmptyTable(bag_list) then
        self:WaitSetKeYinFuConversionTableInBag()
        return
    end
    for k,v in pairs(bag_list) do
        local keyinfu_cfg = self:GetKeYinFuByItemID(v.item_id)
        if keyinfu_cfg and fight_type == keyinfu_cfg.fight_type then
            local cfg = self:GetKeYinFuConversionTableByid(v.item_id)
            if cfg then
                need_num = need_num + (cfg.num * v.num)
            end
        end
	end
    self.keyinfu_conversion_in_bag[fight_type] = need_num
end

--创建背包刻印符换算列表
function NewYinJiJiChengWGData:CreatKeYinFuConversionTableInBag()
    if IsEmptyTable(self.keyinfu_conversion_in_bag) then
        self.keyinfu_conversion_in_bag = {}
        for i=1,2 do
            self:SetKeYinFuConversionTableInBag(i)
        end
    end
end

function NewYinJiJiChengWGData:GetKeYinFuConversionTableInBagByFightType(fight_type)
    return self.keyinfu_conversion_in_bag[fight_type]
end

--获取把背包中的刻印符换算冲1级的的数量
function NewYinJiJiChengWGData:GetKeYinFuConversionTableInBag()
    return self.keyinfu_conversion_in_bag
end


----------------------------------------------------------默认选中----------------------------------------------------------------
--获取部位可镶嵌最低孔位id
function NewYinJiJiChengWGData:GetFristCanInPutMinKongweiByPart(equip_part)
    local min_kongwei_id = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not equip_data or not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return min_kongwei_id
    end
    
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)
    if not yinji_info then
        return min_kongwei_id
    end

    local max_kongwei_num = #self.kongwei_map_cfg
    for i,v in pairs(yinji_info.kong_wei_info_sc) do
        if i <= max_kongwei_num then
            if v.item_id <= 0 then
                local kongwei_is_can_input = self:GetCurKeYinFuIsCanInPut(v.equip_part, v.kongwei_id)
                if kongwei_is_can_input == 1 then
                    min_kongwei_id = i
                    return min_kongwei_id
                end
            end
        end
    end

    return min_kongwei_id
end

--获取去可镶嵌最低孔位id
function NewYinJiJiChengWGData:GetFristCanInPutMinKongwei()
    local min_kongwei_id = 0
    local min_equip_index = 1
    local bag_data_list = self:GetEquipYinJiEquipList()

    for i,v in ipairs(bag_data_list) do
        local kongwei_id = self:GetFristCanInPutMinKongweiByPart(v.index)

        if kongwei_id > 0 then
            min_kongwei_id = kongwei_id
            min_equip_index = i
            
            return min_equip_index, min_kongwei_id
        end
    end

    return min_equip_index, min_kongwei_id
end

--获取部位第一个可替换孔位
function NewYinJiJiChengWGData:GetFirstCanTiHuanKongWeiByPart(equip_part)
    local min_kongwei_id = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not equip_data or not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return min_kongwei_id
    end
    
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)

    if not yinji_info then
        return min_kongwei_id
    end

    for i,v in pairs(yinji_info.kong_wei_info_sc) do
        if i <= #self.kongwei_map_cfg then
            if v.item_id > 0 then
                local kongwei_iscantihuan = self:GetCurKeYinFuIsCanTiHuan(v.equip_part, v.kongwei_id)

                if kongwei_iscantihuan == 1 then
                    min_kongwei_id = i

                    return min_kongwei_id
                end
            end
        end
    end

    return min_kongwei_id
end

--获取第一个可替换孔位
function NewYinJiJiChengWGData:GetFirstCanTiHuanKongWei()
    local min_kongwei_id = 0
    local min_equip_index = 1
    local bag_data_list = self:GetEquipYinJiEquipList()

    for i,v in ipairs(bag_data_list) do
        local kongwei_id = self:GetFirstCanTiHuanKongWeiByPart(v.index)

        if kongwei_id > 0 then
            min_kongwei_id = kongwei_id
            min_equip_index = i
            
            return min_equip_index, min_kongwei_id
        end
    end

    return min_equip_index, min_kongwei_id
end

--获取部位第一个可合成孔位(最低级的)
function NewYinJiJiChengWGData:GetFirstCanComposeKongWeiByPart(equip_part)
    local min_kongwei_id = 0
    local mind_keyinfu_type = 0
    local equip_data = EquipWGData.Instance:GetGridData(equip_part)

    if not equip_data or not self:GetEquipIsEnoughLimitCondition(equip_data) then
        return min_kongwei_id
    end
    
    local yinji_info = self:GetYinJiInfoByEquipPart(equip_part)

    if not yinji_info then
        return min_kongwei_id
    end

    for i,v in pairs(yinji_info.kong_wei_info_sc) do
        if i <= #self.kongwei_map_cfg then
            if v.item_id > 0 then
                local keyinfu_cfg = self:GetKeYinFuByItemID(v.item_id)
                local kongwei_iscancompose = self:GetCurKeYinFuIsCanCompse(v.equip_part, v.kongwei_id)
                if kongwei_iscancompose == 1 then
                    if min_kongwei_id == 0 then
                        min_kongwei_id = i
                        mind_keyinfu_type = keyinfu_cfg.keyinfu_type
                    else
                        if mind_keyinfu_type > keyinfu_cfg.keyinfu_type then
                            min_kongwei_id = i
                            mind_keyinfu_type = keyinfu_cfg.keyinfu_type
                        end
                    end
                end
            end
        end
    end

    return min_kongwei_id
end

--获取第一个可合成孔位
function NewYinJiJiChengWGData:GetFirstCanComposeKongWei()
    local min_kongwei_id = 0
    local min_equip_index = 1
    local bag_data_list = self:GetEquipYinJiEquipList()

    for i,v in ipairs(bag_data_list) do
        local kongwei_id = self:GetFirstCanComposeKongWeiByPart(v.index)
        if kongwei_id > 0 then
            min_kongwei_id = kongwei_id
            min_equip_index = i

            return min_equip_index, min_kongwei_id
        end
    end

    return min_equip_index, min_kongwei_id
end

function NewYinJiJiChengWGData:GetFristCanActiveYinjiPart()
    local equip_index = 1

    local bag_data_list = self:GetEquipYinJiEquipList()

    for i,v in ipairs(bag_data_list) do
        local is_can_active = self:GetCurPartIsCanActiveYinJiAttr(v.index)
        if is_can_active > 0 then
            equip_index = i
            
            return equip_index
        end
    end
end

-- 打开背包和锻造面板检测旧印记属性
function NewYinJiJiChengWGData:CheckOldYinJiAttr()
    -- if not FunOpen.Instance:GetFunIsOpened("equipment_yingji") then
    --     return
    -- end

    -- local check_fun = function (data_list)
    --     if not data_list then
    --         return false
    --     end


    --     for k,v in pairs(data_list) do
    --         if v.param and v.param.impression and v.param.impression > 0 then
    --             return true
    --         end
    --     end
    --     return false
    -- end

    -- local bag_data_list = ItemWGData.Instance:GetBagItemDataList()
    -- if check_fun(bag_data_list) then
    --     self:CheckOldYinJiTip()
    --     return
    -- end
    -- local storage_data_list = ItemWGData.Instance:GetStorgeItemDataList()
    -- if check_fun(storage_data_list) then
    --     self:CheckOldYinJiTip()
    --     return
    -- end
    -- local equip_data_list = EquipWGData.Instance:GetDataList()
    -- if check_fun(equip_data_list) then
    --     self:CheckOldYinJiTip()
    --     return
    -- end
end

--
function NewYinJiJiChengWGData:CheckIsNeedAlertWhenEquipLess(equip_data, new_equip_data)
    if not equip_data or not new_equip_data then
        return false
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
    local new_equip_item_cfg = ItemWGData.Instance:GetItemConfig(new_equip_data.item_id)
    if not item_cfg or not new_equip_item_cfg then
        return false
    end

    local yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(equip_data.index)
    if yinji_info and yinji_info.has_act_yinji_type > 0 then
        if new_equip_item_cfg.order < item_cfg.order then
            return true, Language.NewEquipYinJi.EquipTiHuanAlertTip1
        elseif new_equip_item_cfg.order > item_cfg.order then
            return true, Language.NewEquipYinJi.EquipTiHuanAlertTip
        end
    end

    return false
end

function NewYinJiJiChengWGData:CheckOldYinJiTip()
    local ok_fun = function ()
        FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, "equipment_yingji")
    end
    local dec = Language.NewEquipYinJi.TipContent
    local btn_name = Language.NewEquipYinJi.TipBtnName
    if not self.old_yinji_check then
        self.old_yinji_check = Alert.New()
    end
    EquipmentWGCtrl.Instance:CSEquipYinJiOperReq(EQUIP_YIN_JI_OPERA_TYPE.EQUIP_YIN_JI_OPERA_TYPE_TRANS_KEYINFU)
    self.old_yinji_check:SetLableString(dec)
    self.old_yinji_check:SetOkFunc(ok_fun)
    self.old_yinji_check:SetOkString(btn_name or Language.Common.BtnOK)
    self.old_yinji_check:UseOne()
    self.old_yinji_check:Open()
end

-- 返回消耗物品和还需要多少个数
function NewYinJiJiChengWGData:GetKeYinSpendFuListByFightType(fight_type, need_num)
    local yinkefu_list = self:GetSortBagKeYinFuListByFightType(fight_type)
    local list = {}
    local temp_list = {}

    -- 每一份的数字是4的类型减一次幂
    for _, keyinfu_info in ipairs(yinkefu_list) do
        local num = math.pow(4, (keyinfu_info.keyinfu_type - 1))
        temp_list[keyinfu_info.item_id] = {}
        temp_list[keyinfu_info.item_id].item_id = keyinfu_info.item_id
        temp_list[keyinfu_info.item_id].num = 0

        for i = 1, keyinfu_info.num do
            if need_num <= 0 then
                break
            end

            need_num = need_num - num
            temp_list[keyinfu_info.item_id].num = temp_list[keyinfu_info.item_id].num + 1
        end

        if need_num <= 0 then
            break
        end
    end

    for k, v in pairs(temp_list) do
        table.insert(list, v);
    end

    table.sort(list, SortTools.KeyUpperSorter("item_id"))
    return list, need_num
end