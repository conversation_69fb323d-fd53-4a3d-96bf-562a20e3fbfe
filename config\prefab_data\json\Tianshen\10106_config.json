{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10106_rest", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10106/10106_rest_prefab", "AssetName": "10106_rest", "AssetGUID": "983528761f06df241a70e86b5fe3845c", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": -1.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10106_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10106/10106_skill1_prefab", "AssetName": "10106_skill1", "AssetGUID": "cc1ff5b0932c8994d873305a60c5f9fe", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10106_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10106/10106_skill2_prefab", "AssetName": "10106_skill2", "AssetGUID": "e0c9150a00f8b264c852698ac326db95", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 1.5, "offsetPosY": 0.0, "offsetPosZ": -5.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10106_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10106/10106_skill3_prefab", "AssetName": "10106_skill3", "AssetGUID": "e273b494b984d994bb9345daba688cc2", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10106_attack", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10106/10106_attack_prefab", "AssetName": "10106_attack", "AssetGUID": "2630835a7b6a823439e57ef190c0d92a", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "skill", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "20aee1730c45e8244ae9704ce6a310e3", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "da34047ef83a6ef45bf79fdb169bd3ee", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "e7cca40397a87d84da35ff586b129703", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangattack1", "AssetGUID": "7c97a99c754d6744aa2188c839ff876f", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangattack2", "AssetGUID": "cfb6576546f018d4fa98566868c6e2fc", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangattack3", "AssetGUID": "b3b608db864226d458afe524152b060d", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangattack3", "AssetGUID": "b3b608db864226d458afe524152b060d", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack4", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 1.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangattack4", "AssetGUID": "509a3140e3937de48a15f116769f43b7", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack4", "soundBtnName": "attack3_1", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 1.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10106", "AssetName": "MingJiangattack4", "AssetGUID": "509a3140e3937de48a15f116769f43b7", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack4", "soundBtnName": "attack4_1", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack1", "eventName": "attack1/hit", "numberOfShakes": 2, "distance": 0.05, "speed": 500.0, "delay": 0.2, "decay": 0.0}], "radialBlurs": []}}