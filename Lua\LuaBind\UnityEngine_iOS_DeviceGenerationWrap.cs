﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

#if UNITY_IOS
public class UnityEngine_iOS_DeviceGenerationWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>Begin<PERSON>num(typeof(UnityEngine.iOS.DeviceGeneration));
		<PERSON><PERSON>("Unknown", get_Unknown, null);
		<PERSON><PERSON>("iPhone", get_iPhone, null);
		<PERSON><PERSON>("iPhone3G", get_iPhone3G, null);
		<PERSON><PERSON>("iPhone3GS", get_iPhone3GS, null);
		<PERSON><PERSON>("iPodTouch1Gen", get_iPodTouch1Gen, null);
		<PERSON><PERSON>("iPodTouch2Gen", get_iPodTouch2Gen, null);
		<PERSON><PERSON>("iPodTouch3Gen", get_iPodTouch3Gen, null);
		<PERSON><PERSON>("iPad1Gen", get_iPad1Gen, null);
		<PERSON><PERSON>("iPhone4", get_iPhone4, null);
		<PERSON><PERSON>("iPodTouch4Gen", get_iPodTouch4Gen, null);
		<PERSON><PERSON>("iPad2Gen", get_iPad2Gen, null);
		<PERSON><PERSON>("iPhone4S", get_iPhone4S, null);
		L.RegVar("iPad3Gen", get_iPad3Gen, null);
		L.RegVar("iPhone5", get_iPhone5, null);
		L.RegVar("iPodTouch5Gen", get_iPodTouch5Gen, null);
		L.RegVar("iPadMini1Gen", get_iPadMini1Gen, null);
		L.RegVar("iPad4Gen", get_iPad4Gen, null);
		L.RegVar("iPhone5C", get_iPhone5C, null);
		L.RegVar("iPhone5S", get_iPhone5S, null);
		L.RegVar("iPadAir1", get_iPadAir1, null);
		L.RegVar("iPadMini2Gen", get_iPadMini2Gen, null);
		L.RegVar("iPhone6", get_iPhone6, null);
		L.RegVar("iPhone6Plus", get_iPhone6Plus, null);
		L.RegVar("iPadMini3Gen", get_iPadMini3Gen, null);
		L.RegVar("iPadAir2", get_iPadAir2, null);
		L.RegVar("iPhone6S", get_iPhone6S, null);
		L.RegVar("iPhone6SPlus", get_iPhone6SPlus, null);
		L.RegVar("iPadPro1Gen", get_iPadPro1Gen, null);
		L.RegVar("iPadMini4Gen", get_iPadMini4Gen, null);
		L.RegVar("iPhoneSE1Gen", get_iPhoneSE1Gen, null);
		L.RegVar("iPadPro10Inch1Gen", get_iPadPro10Inch1Gen, null);
		L.RegVar("iPhone7", get_iPhone7, null);
		L.RegVar("iPhone7Plus", get_iPhone7Plus, null);
		L.RegVar("iPodTouch6Gen", get_iPodTouch6Gen, null);
		L.RegVar("iPad5Gen", get_iPad5Gen, null);
		L.RegVar("iPadPro2Gen", get_iPadPro2Gen, null);
		L.RegVar("iPadPro10Inch2Gen", get_iPadPro10Inch2Gen, null);
		L.RegVar("iPhone8", get_iPhone8, null);
		L.RegVar("iPhone8Plus", get_iPhone8Plus, null);
		L.RegVar("iPhoneX", get_iPhoneX, null);
		L.RegVar("iPhoneXS", get_iPhoneXS, null);
		L.RegVar("iPhoneXSMax", get_iPhoneXSMax, null);
		L.RegVar("iPhoneXR", get_iPhoneXR, null);
		L.RegVar("iPadPro11Inch", get_iPadPro11Inch, null);
		L.RegVar("iPadPro3Gen", get_iPadPro3Gen, null);
		L.RegVar("iPad6Gen", get_iPad6Gen, null);
		L.RegVar("iPadAir3Gen", get_iPadAir3Gen, null);
		L.RegVar("iPadMini5Gen", get_iPadMini5Gen, null);
		L.RegVar("iPhone11", get_iPhone11, null);
		L.RegVar("iPhone11Pro", get_iPhone11Pro, null);
		L.RegVar("iPhone11ProMax", get_iPhone11ProMax, null);
		L.RegVar("iPodTouch7Gen", get_iPodTouch7Gen, null);
		L.RegVar("iPad7Gen", get_iPad7Gen, null);
		L.RegVar("iPhoneSE2Gen", get_iPhoneSE2Gen, null);
		L.RegVar("iPadPro11Inch2Gen", get_iPadPro11Inch2Gen, null);
		L.RegVar("iPadPro4Gen", get_iPadPro4Gen, null);
		L.RegVar("iPhone12Mini", get_iPhone12Mini, null);
		L.RegVar("iPhone12", get_iPhone12, null);
		L.RegVar("iPhone12Pro", get_iPhone12Pro, null);
		L.RegVar("iPhone12ProMax", get_iPhone12ProMax, null);
		L.RegVar("iPad8Gen", get_iPad8Gen, null);
		L.RegVar("iPadAir4Gen", get_iPadAir4Gen, null);
		L.RegVar("iPad9Gen", get_iPad9Gen, null);
		L.RegVar("iPadMini6Gen", get_iPadMini6Gen, null);
		L.RegVar("iPhone13", get_iPhone13, null);
		L.RegVar("iPhone13Mini", get_iPhone13Mini, null);
		L.RegVar("iPhone13Pro", get_iPhone13Pro, null);
		L.RegVar("iPhone13ProMax", get_iPhone13ProMax, null);
		L.RegVar("iPadPro5Gen", get_iPadPro5Gen, null);
		L.RegVar("iPadPro11Inch3Gen", get_iPadPro11Inch3Gen, null);
		L.RegVar("iPhoneSE3Gen", get_iPhoneSE3Gen, null);
		L.RegVar("iPadAir5Gen", get_iPadAir5Gen, null);
		L.RegVar("iPhone14", get_iPhone14, null);
		L.RegVar("iPhone14Plus", get_iPhone14Plus, null);
		L.RegVar("iPhone14Pro", get_iPhone14Pro, null);
		L.RegVar("iPhone14ProMax", get_iPhone14ProMax, null);
		L.RegVar("iPadPro6Gen", get_iPadPro6Gen, null);
		L.RegVar("iPadPro11Inch4Gen", get_iPadPro11Inch4Gen, null);
		L.RegVar("iPad10Gen", get_iPad10Gen, null);
		L.RegVar("iPhoneUnknown", get_iPhoneUnknown, null);
		L.RegVar("iPadUnknown", get_iPadUnknown, null);
		L.RegVar("iPodTouchUnknown", get_iPodTouchUnknown, null);
		L.RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.iOS.DeviceGeneration>.Check = CheckType;
		StackTraits<UnityEngine.iOS.DeviceGeneration>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.iOS.DeviceGeneration arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.iOS.DeviceGeneration), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Unknown(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.Unknown);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone3G(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone3G);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone3GS(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone3GS);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch1Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch1Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad1Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad1Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone4(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone4);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch4Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch4Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone4S(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone4S);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone5(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone5);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch5Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch5Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadMini1Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadMini1Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad4Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad4Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone5C(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone5C);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone5S(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone5S);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadAir1(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadAir1);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadMini2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadMini2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone6(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone6);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone6Plus(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone6Plus);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadMini3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadMini3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadAir2(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadAir2);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone6S(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone6S);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone6SPlus(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone6SPlus);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro1Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro1Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadMini4Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadMini4Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneSE1Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneSE1Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro10Inch1Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro10Inch1Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone7(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone7);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone7Plus(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone7Plus);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch6Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch6Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad5Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad5Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro10Inch2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro10Inch2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone8(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone8);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone8Plus(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone8Plus);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneX(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneX);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneXS(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneXS);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneXSMax(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneXSMax);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneXR(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneXR);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro11Inch(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro11Inch);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad6Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad6Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadAir3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadAir3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadMini5Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadMini5Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone11(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone11);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone11Pro(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone11Pro);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone11ProMax(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone11ProMax);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouch7Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouch7Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad7Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad7Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneSE2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneSE2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro11Inch2Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro11Inch2Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro4Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro4Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone12Mini(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone12Mini);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone12(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone12);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone12Pro(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone12Pro);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone12ProMax(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone12ProMax);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad8Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad8Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadAir4Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadAir4Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad9Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad9Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadMini6Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadMini6Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone13(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone13);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone13Mini(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone13Mini);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone13Pro(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone13Pro);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone13ProMax(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone13ProMax);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro5Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro5Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro11Inch3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro11Inch3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneSE3Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneSE3Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadAir5Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadAir5Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone14(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone14);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone14Plus(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone14Plus);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone14Pro(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone14Pro);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhone14ProMax(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhone14ProMax);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro6Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro6Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadPro11Inch4Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadPro11Inch4Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPad10Gen(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPad10Gen);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPhoneUnknown(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPhoneUnknown);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPadUnknown(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPadUnknown);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_iPodTouchUnknown(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.iOS.DeviceGeneration.iPodTouchUnknown);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.iOS.DeviceGeneration o = (UnityEngine.iOS.DeviceGeneration)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

#endif
