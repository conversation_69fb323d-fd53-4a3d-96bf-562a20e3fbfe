--概率展示面板
MysteryBoxGailvView = MysteryBoxGailvView or BaseClass(SafeBaseView)

function MysteryBoxGailvView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(866, 600)})
    self:AddViewResource(0, "uis/view/mystery_box_ui_prefab", "layout_mystery_box_gailv_view")
    self:SetMaskBg(true, true)
end

function MysteryBoxGailvView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end

    if self.tab_list then
        self.tab_list:DeleteMe()
        self.tab_list = nil
    end
end

function MysteryBoxGailvView:SetDataAndOpen(info)
	self.info = info
    self:Open()
end

function MysteryBoxGailvView:LoadCallBack()
    if not self.probability_list then
        self.probability_list = AsyncListView.New(MysteryBoxGailvRender, self.node_list.ph_pro_list) 
        self.probability_list:SetStartZeroIndex(true)
    end

    if not self.tab_list then
        self.tab_list = AsyncListView.New(MysteryBoxTopBarRender, self.node_list.top_tab_list) 
        self.tab_list:SetSelectCallBack(BindTool.Bind1(self.OnTopBarSelect, self))
        self.tab_list:SetStartZeroIndex(true)
    end
end

function MysteryBoxGailvView:OnFlush()
    if IsEmptyTable(self.info) then
        return
    end

    self.tab_list:SetDataList(self.info.data_list)
    self.node_list.title_view_name.text.text = string.format(Language.MysteryBox.GailvTitle, self.info.title_name)
end

function MysteryBoxGailvView:OnTopBarSelect(item)
    if IsEmptyTable(item) or IsEmptyTable(item:GetData()) then 
        return 
    end

    self.probability_list:SetDataList(item:GetData().gailv_data)
end
----------------------------------------------------------------------------------
MysteryBoxGailvRender = MysteryBoxGailvRender or BaseClass(BaseRender)
function MysteryBoxGailvRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.bg:SetActive(self.index % 2 == 1)
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id) or ""
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count.."%"
end
----------------------------------------------------------------------------------

MysteryBoxTopBarRender = MysteryBoxTopBarRender or BaseClass(BaseRender)
function MysteryBoxTopBarRender:OnSelectChange(is_select)
    self.node_list.hl_image:CustomSetActive(is_select)
end

function MysteryBoxTopBarRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    -- print_error(self.data)
    self.node_list.normal_text.text.text = self.data.normal_text
    self.node_list.hl_text.text.text = self.data.hl_text
end