require("game/society/master/master_wg_data")
require("game/society/master/master_baishi_view")
require("game/society/master/master_jieshou_view")
require("game/society/master/master_select_shifu_view")

MASTER_TAB_TYPE =
{
	INFO = 31,
	GF = 32 ,
	WXSJ = 52,
	FB = 53,
	BOSS = 54 ,
	EQ = 55,
	WASH = 56,
	RL = 57,
}

MasterWGCtrl = MasterWGCtrl or BaseClass(BaseWGCtrl)
function MasterWGCtrl:__init()
	if MasterWGCtrl.Instance ~= nil then
		ErrorLog("[MasterWGCtrl] Attemp to create a singleton twice !")
	end
	MasterWGCtrl.Instance = self
	-- self.view = MasterView.New()
	self.data = MasterWGData.New()
	self.baishi_view = MasterBaishiView.New()
	self.jieshou_view = MasterJieshouView.New()
	self.select_master_view = MasterSelectMasterView.New()

	self:RegisterAllProtocols()
end

function MasterWGCtrl:__delete()

	-- self.view:DeleteMe()
	-- self.view = nil

	self.baishi_view:DeleteMe()
	self.baishi_view = nil

	self.jieshou_view:DeleteMe()
	self.jieshou_view = nil

	self.select_master_view:DeleteMe()
	self.select_master_view = nil

	self.data:DeleteMe()
	self.data = nil

	MasterWGCtrl.Instance = nil
	self.is_first_remind = nil
end

function MasterWGCtrl:Open(index, param_t)
	SocietyWGCtrl.Instance:Open(MASTER_TAB_TYPE.INFO)

	if param_t ~= nil and param_t.sub_view_name == SubViewName.BaiShi then
		self:OpenSelectMasterView()
	end
end

function MasterWGCtrl:Close()
	if SocietyWGCtrl.Instance:IsOpen() then
		SocietyWGCtrl.Instance:Close()
	end
end

function MasterWGCtrl:OpenBaishiView(shifu_id, str)
	str = str or ""
	if shifu_id and shifu_id ~= 0 then
		self.baishi_view:SetData(shifu_id, str)
	else
		SysMsgWGCtrl.Instance:ErrorRemind("")
	end
end

function MasterWGCtrl:OpenJieshouView(data)
	self.jieshou_view:SetData(data)
	self.jieshou_view:Open()
end

function MasterWGCtrl:OpenSelectMasterView(data)
	if data then
		self.select_master_view:SetData(data)
	end
	
	self.select_master_view:Open()
end

-- 仙侣加入队伍
function MasterWGCtrl:ShituJoinTeam()
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.FB, "team_change")
end

function MasterWGCtrl:LeaveTeam()
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.FB, "team_out")
end

function MasterWGCtrl:HandleItemTipCallBack(data, handle_type, handle_param_t)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.RL, "item_tips_callback", {data = data, handle_type = handle_type, handle_param_t = handle_param_t})
end

function MasterWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCShituNotice, "OnShituNotice")
	self:RegisterProtocol(SCShituInfo, "OnShituInfo")
	self:RegisterProtocol(SCShituPataInfo, "OnShituPataInfo")
	self:RegisterProtocol(SCShituPataSceneInfo, "OnShituPataSceneInfo")
	self:RegisterProtocol(SCShituEquipAllInfo, "OnShituEquipAllInfo")
	self:RegisterProtocol(SCShituEquipChangeInfo, "OnShituEquipChangeInfo")
	self:RegisterProtocol(SCShituWashInfo, "OnShituWashInfo")
	self:RegisterProtocol(SCWuXingShenJiangInfo, "OnWuXingShenJiangInfo")
	self:RegisterProtocol(CSShituOperate)
	self:RegisterProtocol(CSShituPataFbOperate)
	self:RegisterProtocol(CSShituEquipOperate)
	self:RegisterProtocol(CSShituWashOperate)
	self:RegisterProtocol(CSWuXingShenJiangOperate)
	self:BindGlobalEvent(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind(self.DaycountChange, self))
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))

	---- Remind.Instance:RegisterOneRemind(RemindId.master_reward, BindTool.Bind1(self.CheckMasterUpLevelRemind, self))
	---- Remind.Instance:RegisterOneRemind(RemindId.master_gongfeng, BindTool.Bind1(self.CheckMasterGongFengRemind, self))
end

function MasterWGCtrl:DaycountChange(day_counter_id)
	if day_counter_id == DAY_COUNT.DAYCOUNT_ID_SHITUPATA_DAY_REWARD then
		SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.FB)
	end
end

function MasterWGCtrl:MainuiOpenCreate()
	self:SendShituInfo()
end


-- 请求师徒信息
function MasterWGCtrl:SendShituInfo()
	self:SendShituOperate(SHITU_OPERA_TYPE.REQ_INFO)
end

-- 请求拜师
function MasterWGCtrl:SendBaishi(target_roleid, type)
	--print_error("请求拜师------", target_roleid, type)
	self:SendShituOperate(SHITU_OPERA_TYPE.RQUEST_BAISHI, target_roleid, type)
end

-- 同意拜师
function MasterWGCtrl:SendAgreeBaishi(target_roleid)
	self:SendShituOperate(SHITU_OPERA_TYPE.AGREE_BAISHI_REQUEST, target_roleid)
end

-- 拒绝拜师
function MasterWGCtrl:SendResoluteBaishi(target_roleid)
	self:SendShituOperate(SHITU_OPERA_TYPE.REFUSE_BAISHI_REQUEST, target_roleid)
end

-- 解除关系
function MasterWGCtrl:SendRemoveRelation(target_roleid)
	self:SendShituOperate(SHITU_OPERA_TYPE.REMOVE_RELATION, target_roleid)
end

-- 领取徒弟活跃奖励
function MasterWGCtrl:SendApprenticeActReward(index)
	self:SendShituOperate(SHITU_OPERA_TYPE.FETCH_DEGREE_REWARD, index)
end

-- 师徒等级提升
function MasterWGCtrl:SendShituUpLevel()
	self:SendShituOperate(SHITU_OPERA_TYPE.UPLEVEL)
end

-- 进入师徒boss
function MasterWGCtrl:SendEnterShituBoss(scene_id)
	self:SendShituOperate(SHITU_OPERA_TYPE.ENTER_BOSSSCENE, scene_id)
end

-- 离开师徒boss
function MasterWGCtrl:SendLeaveShituBoss()
	self:SendShituOperate(SHITU_OPERA_TYPE.EXIT_BOSSSCENE)
end

function MasterWGCtrl:SendShituOperate(operate, param1, param2)
	-- print_error("---------MasterWGCtrl:SendShituOperate", operate, param1, param2)
	-- local protocol = ProtocolPool.Instance:GetProtocol(CSShituOperate)
	-- protocol.operate = operate
	-- protocol.param1 = param1 or 0
	-- protocol.param2 = param2 or 0
	-- protocol:EncodeAndSend()
end

function MasterWGCtrl:OnShituNotice(protocol)
	--print_error("---------MasterWGCtrl:OnShituNotice", protocol.notice_type)
	if protocol.notice_type == SHITU_NOTICE.REQUEST_BAISHI then
		self:OpenJieshouView({baishi_type = protocol.param1, req_uid = protocol.operate_role_id, req_name = protocol.operate_role_name})
	elseif protocol.notice_type == SHITU_NOTICE.AGREE_BAISHI_REQUEST then
	elseif protocol.notice_type == SHITU_NOTICE.REFUSE_BAISHI_REQUEST then
	end
end

function MasterWGCtrl:OnShituInfo(protocol)
	--print_error("---------MasterWGCtrl:OnShituInfo",protocol)
	self.data:SetShituInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.INFO)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.GF)
	---- Remind.Instance:DoRemind(RemindId.master_gongfeng)
	---- Remind.Instance:DoRemind(RemindId.master_reward)

	if self.is_first_remind == nil then
		self.is_first_remind = true
		local master_remind_list = self.data:GetMasterRemindList()
		---- Remind.Instance:RegisterOneItemRemind(master_remind_list, RemindId.master_reward)
	end
end

-- 请求师徒爬塔副本信息
function MasterWGCtrl:SendShituPataFbInfo()
	self:SendShituPataFbOperate(SHITU_FB_OPERA_TYPE.REQ_INFO)
end

-- 请求领取师徒爬塔副本每日奖励
function MasterWGCtrl:SendShituPataFbReward()
	self:SendShituPataFbOperate(SHITU_FB_OPERA_TYPE.FETCH_DAILY_REWARD)
end

function MasterWGCtrl:SendShituPataFbOperate(operate, param1)
	Log("---------MasterWGCtrl:SendShituPataFbOperate", operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShituPataFbOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function MasterWGCtrl:OnShituPataInfo(protocol)
	Log("---------MasterWGCtrl:OnShituPataInfo")
	self.data:SetShituPataInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.FB)
end

function MasterWGCtrl:OnShituPataSceneInfo(protocol)
	Log("---------MasterWGCtrl:OnShituPataSceneInfo", protocol.is_finish, protocol.opener_roleid)
	self.data:SetShituPataSceneInfo(protocol)
	if 1 == protocol.is_finish and protocol.opener_roleid == RoleWGData.Instance.role_vo.role_id then
		local pass_vo = FuBenWGData.CreateCommonPassVo()
		pass_vo.scene_type = protocol.scene_type
		pass_vo.is_pass = protocol.is_pass
		pass_vo.reward_list = {}
		pass_vo.tip1 = Language.FuBen.TongGuanTime .. HtmlTool.GetHtml(TimeUtil.FormatSecondDHM8(protocol.pass_time_s), COLOR3B.GREEN , 24)
		pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(self.data:GetCurTitleName(protocol.level), COLOR3B.YELLOW , 24)
		if 0 == protocol.is_pass then
			pass_vo.reward_list = {}
			pass_vo.tip2 = Language.FuBen.TongGuanReward .. HtmlTool.GetHtml(Language.Common.No, COLOR3B.GREEN , 24)
		end
		FuBenWGCtrl.Instance:OpenFuBenNextView(pass_vo)
	end
end

-- 师徒装备请求信息
function MasterWGCtrl:SendShituEquipInfo()
	self:SendShituEquipOperate(SHITU_EQUIP_OPERA_TYPE.EQUIP_REQ_INFO)
end

-- 师徒装备请求穿戴
function MasterWGCtrl:SendShituEquipPuOn(bag_index)
	self:SendShituEquipOperate(SHITU_EQUIP_OPERA_TYPE.EQUIP_PUTON, bag_index)
end

-- 师徒装备请求脱下
function MasterWGCtrl:SendShituEquiptakeOff(equip_index)
	self:SendShituEquipOperate(SHITU_EQUIP_OPERA_TYPE.EQUIP_TAKEOFF, equip_index)
end

-- 师徒装备操作
function MasterWGCtrl:SendShituEquipOperate(operate, param1, param2)
	Log("---------MasterWGCtrl:SendShituEquipOperate", operate, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShituEquipOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function MasterWGCtrl:OnShituEquipAllInfo(protocol)
	Log("---------MasterWGCtrl:OnShituEquipAllInfo")
	self.data:SetShituEquipAllInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView({MASTER_TAB_TYPE.EQ, MASTER_TAB_TYPE.WASH}, "master_equip_change")
end

function MasterWGCtrl:OnShituEquipChangeInfo(protocol)
	Log("---------MasterWGCtrl:OnShituEquipChangeInfo")
	self.data:SethituEquipChangeInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView({MASTER_TAB_TYPE.EQ, MASTER_TAB_TYPE.WASH}, "master_equip_change")
end

-- 师徒洗炼请求信息
function MasterWGCtrl:SendShituWashInfo()
	self:SendShituWashOperate(SHITU_WASH_OPERA_TYPE.REQ_INFO)
end

-- 师徒洗炼请求洗炼
function MasterWGCtrl:SendShituWashReqWash(index)
	self:SendShituWashOperate(SHITU_WASH_OPERA_TYPE.REQ_WASH, index, 0)
end

-- 师徒洗炼属性保存
function MasterWGCtrl:SendShituWashReplace(index)
	self:SendShituWashOperate(SHITU_WASH_OPERA_TYPE.SAVE , index)
end

-- 师徒洗练操作
function MasterWGCtrl:SendShituWashOperate(operate, param1, param2, param3)
	Log("---------MasterWGCtrl:SendShituWashOperate", operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShituWashOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function MasterWGCtrl:OnShituWashInfo(protocol)
	Log("---------MasterWGCtrl:OnShituWashInfo")
	self.data:SetShituWashInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.WASH)
end

function MasterWGCtrl:OnMonsterGeneraterList(protocol)
	self.data:SetRecordCurLayerBossInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.BOSS, "boss_num")
end

function MasterWGCtrl:OnMasterUpLevelResult(result)
	if result == 1 then
		SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.INFO, "suc")
	end
end


-- 五行神将操作
function MasterWGCtrl:SendWuXingShenJiangOperate(operate, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWuXingShenJiangOperate)
	protocol.operate = operate or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function MasterWGCtrl:OnWuXingShenJiangInfo(protocol)
	self.data:SetWuXingShenJiangInfo(protocol)
	SocietyWGCtrl.Instance:FlushMasterView(MASTER_TAB_TYPE.WXSJ)
end

function MasterWGCtrl:CheckMasterUpLevelRemind()
	return self.data:RemindReward()
end

function MasterWGCtrl:CheckMasterGongFengRemind()
	return self.data:RemindGongFeng()
end
--打开组队界面并发送邀请
function  MasterWGCtrl:OpenInvitationTeamPanel(role_id)
	NewTeamWGCtrl.Instance:Open()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSInviteUser)
	send_protocol.role_id = role_id
	send_protocol:EncodeAndSend()
end