TianShen3v3ReadyView = TianShen3v3ReadyView or BaseClass(SafeBaseView)
function TianShen3v3ReadyView:__init()
	self.view_style = ViewStyle.Half
	self.view_layer = UiLayer.PopTop
	self.active_close = false
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/tianshen_3v3_ui_prefab", "layout_tianshen_3v3_ready")
end

function TianShen3v3ReadyView:ReleaseCallBack()
	if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end

	if self.loading_view_close_event then
		GlobalEventSystem:UnBind(self.loading_view_close_event)
		self.loading_view_close_event = nil
	end

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
	
	self.to_v_pos = nil
	self.to_s_pos = nil
	self.from_v_pos = nil
	self.from_s_pos = nil
	self.from_blue_bg_pos = nil
	self.from_red_bg_pos = nil
end

function TianShen3v3ReadyView:LoadCallBack()
    self.member_info_list = {}
	for i = 1, 6 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = TianShen3v3ReadyRender.New(self.node_list["info" .. i])
        end
    end
	self.to_v_pos = self.node_list["v"].rect.anchoredPosition
	self.to_s_pos = self.node_list["s"].rect.anchoredPosition

	self.from_v_pos = self.node_list["v_pos"].rect.anchoredPosition
	self.from_s_pos = self.node_list["s_pos"].rect.anchoredPosition

	self.from_blue_bg_pos = self.node_list["blue_bg_pos"].rect.anchoredPosition
	self.from_red_bg_pos = self.node_list["red_bg_pos"].rect.anchoredPosition

	self.node_list["v"]:SetActive(false)
	self.node_list["s"]:SetActive(false)
	self.node_list["line"]:SetActive(false)
	self.node_list["blue_bg"]:SetActive(false)
	self.node_list["red_bg"]:SetActive(false)

	if Scene.Instance:IsSceneLoading() then
		if not self.loading_view_close_event then
			self.loading_view_close_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.SceneLoadComplete,self))
		end
	else
		GlobalTimerQuest:AddDelayTimer(function()
			self:Flush()
			self:DoAnim()
		end, 0.7)
	end
end

function TianShen3v3ReadyView:SceneLoadComplete()
	GlobalTimerQuest:AddDelayTimer(function()
		self:Flush()
		self:DoAnim()
	end, 0.7)
end

function TianShen3v3ReadyView:ShowIndexCallBack()
end

function TianShen3v3ReadyView:DoAnim()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	self.sequence = DG.Tweening.DOTween.Sequence()
	self.sequence:AppendCallback(function()
		self.node_list["v"].rect.anchoredPosition = Vector2(self.from_v_pos.x,self.from_v_pos.y)
		self.node_list["s"].rect.anchoredPosition = Vector2(self.from_s_pos.x,self.from_s_pos.y)
		self.node_list["v"]:SetActive(true)
		self.node_list["s"]:SetActive(true)

		self.node_list["blue_bg"].rect.anchoredPosition = Vector2(self.from_blue_bg_pos.x,self.from_blue_bg_pos.y)
        self.node_list["red_bg"].rect.anchoredPosition = Vector2(self.from_red_bg_pos.x,self.from_red_bg_pos.y)
		self.node_list["blue_bg"]:SetActive(true)
		self.node_list["red_bg"]:SetActive(true)

    	self.node_list["blue_bg"].rect:DOAnchorPosX(0, 0.2)
		self.node_list["red_bg"].rect:DOAnchorPosX(0, 0.2)
    end)
	self.sequence:AppendInterval(0.2)
	self.sequence:AppendCallback(function()
		-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_kuafu_3v3_01)--UI_jinjiechenggong)
		-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect_boom_pos.transform, 2, nil, nil)
		self.node_list["line"]:SetActive(true)
    	self.node_list["v"].rect:DOAnchorPos(self.to_v_pos, 0.2):SetEase(DG.Tweening.Ease.OutBack)
    	self.node_list["s"].rect:DOAnchorPos(self.to_s_pos, 0.2):SetEase(DG.Tweening.Ease.OutBack)
    end)
	self.sequence:AppendInterval(5)
	self.sequence:AppendCallback(function()
		self:Close()
		ViewManager.Instance:Open(GuideModuleName.TianShen3v3CountdownView)
    end)
end

function TianShen3v3ReadyView:OnFlush()
	local side_info_list = TianShen3v3WGData.Instance:GetSideInfoList()
	local index = 1
	for i = 0, #side_info_list do
		local side_info = side_info_list[i]
		for _, role_info in ipairs(side_info) do
			self.member_info_list[index]:SetData(role_info)
			index = index + 1
		end
	end
end

-------------------------------- 单个角色信息 ------------------------------------
TianShen3v3ReadyRender = TianShen3v3ReadyRender or BaseClass(BaseRender)
function TianShen3v3ReadyRender:__init()

end

function TianShen3v3ReadyRender:__delete()

end

function TianShen3v3ReadyRender:OnFlush()
    if not self.data then
        return
    end

    local is_main_role = self.data.uid == RoleWGData.Instance:GetOriginUid() and self.data.plat_type == RoleWGData.Instance:GetPlatType() and self.data.server_id == RoleWGData.Instance:GetMergeServerId()
    local name_color = is_main_role and COLOR3B.D_GLOD or COLOR3B.WHITE

    -- 名字特效
    self.node_list["name_effect"]:SetActive(is_main_role)

    -- 角色名称
	local play_name = self.data.role_name
	self.node_list["role_name"].text.text = ToColorStr(play_name, name_color)

    local grade_cfg = TianShen3v3WGData.Instance:GetGradeCfgByScore(self.data.score)
    if grade_cfg then
		self.node_list["score"].image:LoadSprite(ResPath.GetTianShen3v3Img("duanwei_icon" .. grade_cfg.grade)) 		-- 段位icon
	end

	local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.tianshen_index)			
	local tianshen_act_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.data.tianshen_index)
	local item_id = tianshen_act_cfg.act_item_id
	local _, color = ItemWGData.Instance:GetItemColor(item_id)
	-- 天神名称
	self.node_list["tianshen_name"].text.text = ToColorStr(tianshen_cfg.bianshen_name, name_color)
	
	-- 天神头像
	local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
	self.node_list["head_cell"].image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list["head_cell"]:SetActive(true)
		self.node_list["head_cell"].image:SetNativeSize()
	end)
	self.node_list["state_text"].text.text = Language.KuafuPVP.Loaded

	-- 天神头像背景
	self.node_list["head_bg"].image:LoadSpriteAsync(ResPath.GetTianShen3v3Img("ts_bg_" .. color))

	-- 战力 
	self.node_list["cap"].text.text = string.format(Language.Rank.RankZhanLi, CommonDataManager.ConverNumberToThousand2(self.data.capability or 0))
end

