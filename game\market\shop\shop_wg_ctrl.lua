
require("game/market/shop/shop_wg_data")
require("game/market/shop/shop_item_render")
require("game/market/shop/shop_view")
require("game/market/privilege_shop/privilege_shop")

-- 商城
ShopWGCtrl = ShopWGCtrl or BaseClass(BaseWGCtrl)

function ShopWGCtrl:__init()
	if ShopWGCtrl.Instance then
        error("[ShopWGCtrl]:Attempt to create singleton twice!")
	end
	ShopWGCtrl.Instance = self

	self.data = ShopWGData.New()
	self.privilege_view = PrivilegeShopView.New(GuideModuleName.PrivilegeShop)
	self:RegisterAllProtocals()
end

function ShopWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil
	self.privilege_view:DeleteMe()
	self.privilege_view = nil
	ShopWGCtrl.Instance = nil

	GlobalEventSystem:UnBind(self.open_fun_change)
end

function ShopWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSShopBuy)

	self:RegisterProtocol(SCRetShopItemInfo, "OnShopItemInfo")
	self:RegisterProtocol(SCShopBuy, "OnShopBuy")
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

function ShopWGCtrl:MainuiOpenCreate()
	-- local new_shop_type = ShopWGData.Instance.shop_auto.other[1].new_shop_type
	-- new_shop_type = Split(new_shop_type, "|")
	--self:SendReqShopItemInfo(tonumber(new_shop_type[1]), tonumber(new_shop_type[2]))
	self:SendReqShopItemInfo(SHOP_BIG_TYPE.SHOP_TYPE_2)
end

function ShopWGCtrl:SendShopBuy(item_id, item_num, is_use, is_bind, index)
	-- print_error("SendShopBuy:::", item_id, item_num, is_use, is_bind, index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShopBuy)
	protocol.item_id = item_id or 0
	protocol.item_num = item_num or 0
	protocol.is_use = is_use or 0						--是否直接使用
	protocol.is_bind = is_bind or 0
	protocol.index = index or 0
	protocol:EncodeAndSend()
end

-- 商城类型
-- 页签类型
function ShopWGCtrl:SendReqShopItemInfo(shop_type, page_type) --小标签干掉了
	--print_error("SendReqShopItemInfo:::", shop_type, page_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSReqShopItemInfo)
	protocol.shop_type = shop_type
	--protocol.page_type = page_type
	protocol:EncodeAndSend()
end

function ShopWGCtrl:OnShopItemInfo(protocol)
	--print_error("OnShopItemInfo:::", protocol)
	self.data:AllShopInfo(protocol)
	self:CheckTotalBuySystem(protocol)
	if ViewManager.Instance:IsOpen(GuideModuleName.Market) then
		ViewManager.Instance:FlushView(GuideModuleName.Market, nil, "OnShopItemInfo")
	end

	if self.privilege_view:IsOpen() then
		self.privilege_view:Flush()
	end

	KuafuHonorhallWGCtrl.Instance:FlushShopView(0, "OnShopItemInfo")
	TipWGCtrl.Instance:FlushQuickBuyPanel()
end

function ShopWGCtrl:OnShopBuy(protocol)
	--print_error("OnShopBuy:::", protocol)
	self.data:ChangeShopInfo(protocol)
	self:CheckBuySystem(protocol)

	if ViewManager.Instance:IsOpen(GuideModuleName.Market) then
		ViewManager.Instance:FlushView(GuideModuleName.Market, nil, "OnShopBuy")
	end

	if self.privilege_view:IsOpen() then
		self.privilege_view:Flush()
	end
	
	KuafuHonorhallWGCtrl.Instance:FlushShopView(0, "OnShopBuy")
end

-- 己弃用，改用
-- local shop_tab = ShopWGData.Instance:GetShopTabIndex(xiaogui_cfg.seq)
-- ViewManager.Instance:Open(GuideModuleName.Shop, shop_tab,"all" ,{to_ui_name = "seq", to_ui_param = xiaogui_cfg.seq})
-- to_ui_name支持 seq和item_id详见代码
function ShopWGCtrl:Open(tab_index)
	if nil == tab_index or tab_index <= 0 then
		tab_index = TabIndex.shop_limit
	end

	tab_index = ShopWGData.Instance:GetShopTabNumIndex(tab_index)
	ViewManager.Instance:Open(GuideModuleName.Market, tab_index, "all" ,{to_ui_name = "seq", to_ui_param = tab_index})
end

function ShopWGCtrl:FlushView()
	ViewManager.Instance:FlushView(GuideModuleName.Market, nil, "OnShopBuy")
end

function ShopWGCtrl:CheckBuySystem(protocol)
	-- body
	local shop_cfg = self.data:GetShopCfgSeq(protocol.item.item_index)
	if not shop_cfg then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(shop_cfg.itemid)
	if not item_cfg then return end
	-- if 0 == item_cfg.param1 and 0 == item_cfg.param2 then return end
	-- if Item_Use_Type.Use_Type_62 ~= item_cfg.use_type then return end
	GlobalEventSystem:Fire(OtherEventType.Shop_Buy, {seq = protocol.item.item_index})
end

-- 通知检测所有购买的功能 (上线后检测一遍)
function ShopWGCtrl:CheckTotalBuySystem(protocol)
	-- body
	if not protocol.items or IsEmptyTable(protocol.items) then return end
	local shop_cfg = nil
	local item_cfg = nil
	for k,v in pairs(protocol.items) do
		shop_cfg = self.data:GetShopCfgSeq(v.item_index)
		if not shop_cfg then return end
		item_cfg = ItemWGData.Instance:GetItemConfig(shop_cfg.itemid)
		if not item_cfg then return end
		if 0 ~= item_cfg.param1 and 0 ~= item_cfg.param2 and Item_Use_Type.Use_Type_62 ~= item_cfg.use_type then
			GlobalEventSystem:Fire(OtherEventType.Shop_Buy_Info)
			break
		end
	end
end

 --根据seq跳转商城
function ShopWGCtrl:ShopJumpToItemBySeq(seq)
	local shop_cfg = self.data:GetShopCfgSeq(seq)
    if IsEmptyTable(shop_cfg) then
        print_error("取不到商城配置 seq =",seq)
        return
    end
	local tab_index = self.data:GetShopTabIndex(seq)
	local target_item_id = shop_cfg.itemid
	self.data:SetCurIndex(tab_index)
	MarketWGCtrl.Instance:SetDefaultJumpIndex(tab_index)
	ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = target_item_id})
end

--根据item_id跳转商城（注意重复id跳转优先选择开启的靠前标签）
function ShopWGCtrl:ShopJumpToItemByID(target_item_id)
	local tab_index = self.data:GetItemSellTabIndex(target_item_id)
    if tab_index == -1 then
        print_error("取不到商城配置 item_id =",target_item_id)
        return
    end --无标签开启
	self.data:SetCurIndex(tab_index)
	MarketWGCtrl.Instance:SetDefaultJumpIndex(tab_index)
	ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = target_item_id})
end

--重复商品，用id和tabindex唯一定位
function ShopWGCtrl:ShopJumpToItemByIDAndTabIndex(target_item_id,tab_index)
	self.data:SetCurIndex(tab_index)
	MarketWGCtrl.Instance:SetDefaultJumpIndex(tab_index)
	ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = target_item_id})
end

--tabindex唯一定位
function ShopWGCtrl:OpenShopJumpToTabIndex(tab_index, key, values)
    local index = 0
    if tonumber(tab_index) == nil and type(tab_index) == 'string' then
        index = ShopWGData.ShowTypeToIndex[tab_index]
    else
        index = tonumber(tab_index)
    end
    self.data:SetCurIndex(index)
    MarketWGCtrl.Instance:SetDefaultJumpIndex(index)
	ViewManager.Instance:Open(GuideModuleName.Market, index, values ~= nil)
    if values ~= nil then
		ViewManager.Instance:FlushView(GuideModuleName.Market, index, key, values)
    end
end

function ShopWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.RechargeWeekcard or fun_name == FunName.PrivilegeShop then 
		local zhizun_is_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeWeekcard)
        local zhizun_state = zhizun_is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.ZHI_ZUN_VIEW, zhizun_state)

		local privilege_is_open = FunOpen.Instance:GetFunIsOpened(FunName.PrivilegeShop)
        local privilege_state = privilege_is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SEA_PRIVILEGE_SHOP, privilege_state)
    end
end