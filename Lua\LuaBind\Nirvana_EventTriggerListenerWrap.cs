﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_EventTriggerListenerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.EventTriggerListener), typeof(UnityEngine.EventSystems.EventTrigger));
		<PERSON><PERSON>RegFunction("OnBeginDrag", OnBeginDrag);
		<PERSON><PERSON>unction("OnCancel", OnCancel);
		<PERSON><PERSON>RegFunction("OnDrag", OnDrag);
		<PERSON>.RegFunction("OnDrop", OnDrop);
		<PERSON><PERSON>RegFunction("OnEndDrag", OnEndDrag);
		<PERSON><PERSON>RegFunction("OnPointerClick", OnPointerClick);
		<PERSON><PERSON>RegFunction("OnPointerDown", OnPointerDown);
		<PERSON><PERSON>Function("OnPointerEnter", OnPointerEnter);
		<PERSON><PERSON>RegFunction("OnPointerExit", OnPointerExit);
		<PERSON><PERSON>unction("OnPointerUp", OnPointerUp);
		<PERSON><PERSON>un<PERSON>("OnSelect", OnSelect);
		<PERSON><PERSON>("OnDeselect", OnDeselect);
		<PERSON><PERSON>ction("OnUpdateSelected", OnUpdateSelected);
		L.RegFunction("OnMove", OnMove);
		L.RegFunction("AddUpdateSelectedListener", AddUpdateSelectedListener);
		L.RegFunction("AddSelectListener", AddSelectListener);
		L.RegFunction("AddPointerUpListener", AddPointerUpListener);
		L.RegFunction("AddPointerExitListener", AddPointerExitListener);
		L.RegFunction("AddPointerEnterListener", AddPointerEnterListener);
		L.RegFunction("AddPointerDownListener", AddPointerDownListener);
		L.RegFunction("AddPointerClickListener", AddPointerClickListener);
		L.RegFunction("AddMoveListener", AddMoveListener);
		L.RegFunction("AddEndDragListener", AddEndDragListener);
		L.RegFunction("AddDropListener", AddDropListener);
		L.RegFunction("AddDragListener", AddDragListener);
		L.RegFunction("AddDeselectListener", AddDeselectListener);
		L.RegFunction("AddCancelListener", AddCancelListener);
		L.RegFunction("AddBeginDragListener", AddBeginDragListener);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("BeginDragEvent", get_BeginDragEvent, set_BeginDragEvent);
		L.RegVar("CancelEvent", get_CancelEvent, set_CancelEvent);
		L.RegVar("DragEvent", get_DragEvent, set_DragEvent);
		L.RegVar("DropEvent", get_DropEvent, set_DropEvent);
		L.RegVar("EndDragEvent", get_EndDragEvent, set_EndDragEvent);
		L.RegVar("PointerClickEvent", get_PointerClickEvent, set_PointerClickEvent);
		L.RegVar("PointerDownEvent", get_PointerDownEvent, set_PointerDownEvent);
		L.RegVar("PointerEnterEvent", get_PointerEnterEvent, set_PointerEnterEvent);
		L.RegVar("PointerExitEvent", get_PointerExitEvent, set_PointerExitEvent);
		L.RegVar("PointerUpEvent", get_PointerUpEvent, set_PointerUpEvent);
		L.RegVar("SelectEvent", get_SelectEvent, set_SelectEvent);
		L.RegVar("DeselectEvent", get_DeselectEvent, set_DeselectEvent);
		L.RegVar("UpdateSelectedEvent", get_UpdateSelectedEvent, set_UpdateSelectedEvent);
		L.RegVar("MoveEvent", get_MoveEvent, set_MoveEvent);
		L.RegFunction("AxisEventDelegate", Nirvana_EventTriggerListener_AxisEventDelegate);
		L.RegFunction("BaseEventDelegate", Nirvana_EventTriggerListener_BaseEventDelegate);
		L.RegFunction("PointerEventDelegate", Nirvana_EventTriggerListener_PointerEventDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBeginDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnBeginDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnCancel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnCancel(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrop(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnEndDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnEndDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerClick(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerClick(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerDown(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerDown(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerEnter(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerEnter(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerExit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerExit(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerUp(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerUp(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnSelect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnSelect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDeselect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnDeselect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnUpdateSelected(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnUpdateSelected(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnMove(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			UnityEngine.EventSystems.AxisEventData arg0 = (UnityEngine.EventSystems.AxisEventData)ToLua.CheckObject<UnityEngine.EventSystems.AxisEventData>(L, 2);
			obj.OnMove(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddUpdateSelectedListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.BaseEventDelegate arg0 = (Nirvana.EventTriggerListener.BaseEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.BaseEventDelegate>(L, 2);
			obj.AddUpdateSelectedListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddSelectListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.BaseEventDelegate arg0 = (Nirvana.EventTriggerListener.BaseEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.BaseEventDelegate>(L, 2);
			obj.AddSelectListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddPointerUpListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddPointerUpListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddPointerExitListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddPointerExitListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddPointerEnterListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddPointerEnterListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddPointerDownListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddPointerDownListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddPointerClickListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddPointerClickListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddMoveListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.AxisEventDelegate arg0 = (Nirvana.EventTriggerListener.AxisEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.AxisEventDelegate>(L, 2);
			obj.AddMoveListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddEndDragListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddEndDragListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddDropListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddDropListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddDragListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddDragListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddDeselectListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.BaseEventDelegate arg0 = (Nirvana.EventTriggerListener.BaseEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.BaseEventDelegate>(L, 2);
			obj.AddDeselectListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddCancelListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.BaseEventDelegate arg0 = (Nirvana.EventTriggerListener.BaseEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.BaseEventDelegate>(L, 2);
			obj.AddCancelListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddBeginDragListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			Nirvana.EventTriggerListener.PointerEventDelegate arg0 = (Nirvana.EventTriggerListener.PointerEventDelegate)ToLua.CheckDelegate<Nirvana.EventTriggerListener.PointerEventDelegate>(L, 2);
			obj.AddBeginDragListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BeginDragEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CancelEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.BaseEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DragEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DropEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EndDragEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PointerClickEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PointerDownEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PointerEnterEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PointerExitEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PointerUpEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.PointerEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SelectEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.BaseEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DeselectEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.BaseEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UpdateSelectedEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.BaseEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MoveEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EventTriggerListener.AxisEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_BeginDragEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.BeginDragEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.BeginDragEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.BeginDragEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CancelEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.CancelEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.CancelEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.CancelEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DragEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.DragEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.DragEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.DragEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DropEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.DropEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.DropEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.DropEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EndDragEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.EndDragEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.EndDragEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.EndDragEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PointerClickEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.PointerClickEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerClickEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerClickEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PointerDownEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.PointerDownEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerDownEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerDownEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PointerEnterEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.PointerEnterEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerEnterEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerEnterEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PointerExitEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.PointerExitEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerExitEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerExitEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PointerUpEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.PointerUpEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerUpEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.PointerEventDelegate ev = (Nirvana.EventTriggerListener.PointerEventDelegate)arg0.func;
				obj.PointerUpEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SelectEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.SelectEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.SelectEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.SelectEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DeselectEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.DeselectEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.DeselectEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.DeselectEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_UpdateSelectedEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.UpdateSelectedEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.UpdateSelectedEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.BaseEventDelegate ev = (Nirvana.EventTriggerListener.BaseEventDelegate)arg0.func;
				obj.UpdateSelectedEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MoveEvent(IntPtr L)
	{
		try
		{
			Nirvana.EventTriggerListener obj = (Nirvana.EventTriggerListener)ToLua.CheckObject(L, 1, typeof(Nirvana.EventTriggerListener));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EventTriggerListener.MoveEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EventTriggerListener'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EventTriggerListener.AxisEventDelegate ev = (Nirvana.EventTriggerListener.AxisEventDelegate)arg0.func;
				obj.MoveEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EventTriggerListener.AxisEventDelegate ev = (Nirvana.EventTriggerListener.AxisEventDelegate)arg0.func;
				obj.MoveEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EventTriggerListener_AxisEventDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EventTriggerListener.AxisEventDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EventTriggerListener.AxisEventDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EventTriggerListener_BaseEventDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EventTriggerListener.BaseEventDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EventTriggerListener.BaseEventDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EventTriggerListener_PointerEventDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EventTriggerListener.PointerEventDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EventTriggerListener.PointerEventDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

