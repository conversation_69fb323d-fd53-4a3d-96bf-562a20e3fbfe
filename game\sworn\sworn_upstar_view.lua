function SwornView:InitSwornUpstarView()
    self.us_select_hole_data = nil
    self.us_select_hole_index = -1
    self.do_up_star_ani = false

    XUI.AddClickEventListener(self.node_list["btn_upstar"], BindTool.Bind(self.OnClickSuitUpStar, self))
    XUI.AddClickEventListener(self.node_list["upstar_next_item"], BindTool.Bind(self.OnClickNextStar, self))

    if self.upstar_equip_list == nil then
        self.upstar_equip_list = {}
        local node_num = self.node_list["upstar_equip_list"].transform.childCount
        for i = 0, node_num - 1 do
            self.upstar_equip_list[i] = JlpUpStarEquipRender.New(self.node_list["upstar_equip_list"]:FindObj("equip_" .. i + 1))
            self.upstar_equip_list[i]:SetIndex(i)
            self.upstar_equip_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectUpStarCallBack, self))
        end
    end

    if self.ustr_need_item == nil then
        self.ustr_need_item = ItemCell.New(self.node_list["upstar_need_item"])
    end
end

function SwornView:SwornUpStarViewReleaseCallBack()
    if self.upstar_equip_list then
        for k, v in pairs(self.upstar_equip_list) do
            v:DeleteMe()
        end
        self.upstar_equip_list = nil
    end

    if self.ustr_need_item then
        self.ustr_need_item:DeleteMe()
        self.ustr_need_item = nil
    end

    self.us_select_hole_data = nil

    self:SwornUpStarOneKeyStop()
    self.do_up_star_ani = false
end

function SwornView:OnSelectUpStarCallBack(cell, ignore_stop_ani)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    local hole = data.hole
    if hole == self.us_select_hole_index and self.us_select_hole_data == data then
        return
    end

    self.us_select_hole_index = hole
    for k,v in pairs(self.upstar_equip_list) do
        v:FlushSelectHL(k == hole)
    end

    self:FlushUpStarItem()


        if self.do_up_star_ani then
        self:SwornUpStarUpAni()
    end

    local ignore_stop_ani = ignore_stop_ani or false
    if not ignore_stop_ani then
        self:SwornUpStarOneKeyStop()
    else
        if self.do_up_star_ani then
            self:SwornUpStarUpAni()
        end
    end
end


function SwornView:FlushUpStarView(need_jump_hole)
    local suit_index = self:GetSelectSuitIndex()
    local equip_list = SwornWGData.Instance:GetSuitHoleList(suit_index)
    if equip_list ~= nil then
        for k,v in pairs(self.upstar_equip_list) do
            if equip_list[k] then
                v:SetData(equip_list[k])
            end
        end
    end

    --if need_jump_hole then
        local jump_hole = SwornWGData.Instance:GetSuitUpStarJumpHole(suit_index, self.us_select_hole_index)
        if jump_hole ~= self.us_select_hole_index then
            local cell = self.upstar_equip_list[jump_hole]
            if cell then
                self:OnSelectUpStarCallBack(cell, true)
                return
            end
        end
    --end

    self:FlushUpStarItem()

    if self.do_up_star_ani then
        self:SwornUpStarUpAni()
    end
end

function SwornView:FlushUpStarItem()
    local cell = self.upstar_equip_list[self.us_select_hole_index]
    if cell and cell.data ~= nil then
        self.us_select_hole_data = cell.data
    else
        self.us_select_hole_data = nil
        return
    end

    local item_id = self.us_select_hole_data.item_id
    local suit_index = self:GetSelectSuitIndex()
    local star_level = self.us_select_hole_data.star_level
    local star_level_cfg = SwornWGData.Instance:GetEquipStarCfg(suit_index, self.us_select_hole_index, star_level)
    local max_star_level = SwornWGData.Instance:GetHoleMaxStarLevel(suit_index, self.us_select_hole_index)
    if star_level_cfg == nil then
        return
    end

    local is_can_upstar = false
    if star_level >= max_star_level then
        XUI.SetButtonEnabled(self.node_list["btn_upstar"], false)
        self.ustr_need_item:SetFlushCallBack(nil)
    else
        local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
        is_can_upstar = has_num >= star_level_cfg.cost_item_num
        local color = (not is_can_upstar) and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
        local str = has_num .. "/" .. star_level_cfg.cost_item_num
        self.ustr_need_item:SetFlushCallBack(function ()
            self.ustr_need_item:SetRightBottomColorText(str, color)
            self.ustr_need_item:SetRightBottomTextVisible(true)
        end)

        XUI.SetButtonEnabled(self.node_list["btn_upstar"], true)
    end

    self.ustr_need_item:SetData({item_id = item_id})
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg ~= nil then
        self.node_list["next_item_bg"].image:LoadSpriteAsync(ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color))
        self.node_list["next_star_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        
        local star_str = CommonDataManager.GetAncientNumber(star_level)
        local item_name = string.format(Language.Sworn.EquipStarName, star_str, item_cfg.name)
        local next_str = item_name .. string.format(Language.Sworn.EquipLevel, self.us_select_hole_data.level)
        self.node_list["next_item_name"].text.text = next_str
    end

    self.node_list["btn_upstar_red"]:SetActive(is_can_upstar)
end

function SwornView:OnClickNextStar()
    if self.us_select_hole_data == nil then
        return
    end

    local is_act = self.us_select_hole_data.star_level > 0
    TipWGCtrl.Instance:OpenItem({item_id = self.us_select_hole_data.item_id}, is_act and ItemTip.FROM_SWORN_EQUIP or ItemTip.FROM_NORMAL)
end

--装备升星
function SwornView:OnClickSuitUpStar()
    -- if self.us_select_hole_data == nil then
    --     return
    -- end

    -- local suit_index = self:GetSelectSuitIndex()
    -- local star_level = self.us_select_hole_data.star_level
    -- local star_level_cfg = SwornWGData.Instance:GetEquipStarCfg(suit_index, self.us_select_hole_index, star_level)
    -- if star_level_cfg == nil then
    --     return
    -- end

    -- local has_num = ItemWGData.Instance:GetItemNumInBagById(self.us_select_hole_data.item_id)
    -- if has_num < star_level_cfg.cost_item_num then
    --     TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.us_select_hole_data.item_id})
    --     return
    -- end

    -- self:UpSucessEffect(self.node_list["upstar_effect_node"], UIEffectName.s_shengxing)
    -- SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_STAR_UP, suit_index, self.us_select_hole_index)

    if self:IsSwornUpStarCanUp(true) then
        if self.do_up_star_ani then
            self:SwornUpStarOneKeyStop()
        else
            self.do_up_star_ani = true
            self:UpSucessEffect(self.node_list["upstar_effect_node"], UIEffectName.s_shengxing)
            local suit_index = self:GetSelectSuitIndex()
            SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_STAR_UP, suit_index, self.us_select_hole_index)
            self.node_list.btn_upstar_text.text.text = Language.Sworn.UpStarOneKeyStop
        end
    end
end

--------------------------------------------一件升星------------------------------------------
function SwornView:SwornUpStarUpAni()
    if self:IsSwornUpStarCanUp(false) then
        if self.do_up_star_ani then
            ReDelayCall(self, function()
                if self.do_up_star_ani then
                    self:UpSucessEffect(self.node_list["upstar_effect_node"], UIEffectName.s_shengxing)
                    local suit_index = self:GetSelectSuitIndex()
                    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_STAR_UP, suit_index, self.us_select_hole_index)
                end
             end, 0.2, "sworn_auto_up_star")
        end
    else
        self:SwornUpStarOneKeyStop()
    end
end

function SwornView:IsSwornUpStarCanUp(need_show_tip)
    if self.us_select_hole_data == nil then
        return false
    end

    local suit_index = self:GetSelectSuitIndex()
    local star_level = self.us_select_hole_data.star_level
    local star_level_cfg = SwornWGData.Instance:GetEquipStarCfg(suit_index, self.us_select_hole_index, star_level)
    if star_level_cfg == nil then
        return false
    end

    local max_star_level = SwornWGData.Instance:GetHoleMaxStarLevel(suit_index, self.us_select_hole_index)
    if star_level >= max_star_level then
        return false
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(self.us_select_hole_data.item_id)
    if has_num < star_level_cfg.cost_item_num then
        if need_show_tip then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.us_select_hole_data.item_id})
        end
        return false
    end

    return true
end

function SwornView:SwornUpStarOneKeyStop()
    if self.node_list.btn_upstar_text then
        self.node_list.btn_upstar_text.text.text = Language.Sworn.UpStarOneKey
    end

    self.do_up_star_ani = false
end

--------------------------------JlpUpStarEquipRender-----------------------------------
JlpUpStarEquipRender = JlpUpStarEquipRender or BaseClass(BaseRender)
function JlpUpStarEquipRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_cell_click"], BindTool.Bind(self.OnClickUstrCellBtn, self))
end

function JlpUpStarEquipRender:__delete()
    self.ustr_click_callback = nil
end

function JlpUpStarEquipRender:SetCellClickCallBack(call_back)
    self.ustr_click_callback = call_back
end

function JlpUpStarEquipRender:OnClickUstrCellBtn()
    if self.data == nil then
        return
    end

    if self.data.star_level < 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.TabErrorDesc)
    else
        if self.ustr_click_callback then
            self.ustr_click_callback(self)
        end
    end
end

function JlpUpStarEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg ~= nil then
        self.node_list["ustr_cell_bg"].image:LoadSpriteAsync(ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color))
        self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    self.node_list["equip_level"].text.text = string.format(Language.Sworn.EquipLevel, self.data.level)
    self.node_list["equip_star"].text.text = string.format(Language.Sworn.EquipStar, CommonDataManager.GetAncientNumber(self.data.star_level))
    XUI.SetGraphicGrey(self.node_list["icon"], self.data.star_level < 1)
    XUI.SetGraphicGrey(self.node_list["ustr_cell_bg"], self.data.star_level < 1)

    local is_can_upstar = SwornWGData.Instance:GetHoleUpStarRemind(self.data.suit, self.data.hole)
    self.node_list["remind"]:SetActive(is_can_upstar)
end

function JlpUpStarEquipRender:FlushSelectHL(is_select)
    self.node_list["img_hl"]:SetActive(is_select)
end