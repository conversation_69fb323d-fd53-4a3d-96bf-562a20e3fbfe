﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CameraShakeWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CameraShake), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("Shake", Shake);
		<PERSON><PERSON>RegFunction("CancelShake", CancelShake);
		<PERSON><PERSON>Function("BeginShakeGUI", BeginShakeGUI);
		<PERSON><PERSON>RegFunction("EndShakeGUI", EndShakeGUI);
		L.RegFunction("BeginShakeGUILayout", BeginShakeGUILayout);
		<PERSON><PERSON>RegFunction("EndShakeGUILayout", EndShakeGUILayout);
		<PERSON><PERSON>unction("IsShaking", IsShaking);
		<PERSON><PERSON>Function("IsCancelling", IsCancelling);
		<PERSON>.RegFunction("DoShake", DoShake);
		<PERSON><PERSON>RegFunction("DoCancelShake", DoCancelShake);
		<PERSON><PERSON>unction("DoBeginShakeGUI", DoBeginShakeGUI);
		<PERSON><PERSON>unction("DoEndShakeGUI", DoEndShakeGUI);
		<PERSON><PERSON>unction("DoBeginShakeGUILayout", DoBeginShakeGUILayout);
		L.RegFunction("DoEndShakeGUILayout", DoEndShakeGUILayout);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("cameras", get_cameras, set_cameras);
		L.RegVar("numberOfShakes", get_numberOfShakes, set_numberOfShakes);
		L.RegVar("shakeAmount", get_shakeAmount, set_shakeAmount);
		L.RegVar("rotationAmount", get_rotationAmount, set_rotationAmount);
		L.RegVar("distance", get_distance, set_distance);
		L.RegVar("speed", get_speed, set_speed);
		L.RegVar("decay", get_decay, set_decay);
		L.RegVar("guiShakeModifier", get_guiShakeModifier, set_guiShakeModifier);
		L.RegVar("multiplyByTimeScale", get_multiplyByTimeScale, set_multiplyByTimeScale);
		L.RegVar("instance", get_instance, set_instance);
		L.RegVar("isShaking", get_isShaking, null);
		L.RegVar("isCancelling", get_isCancelling, null);
		L.RegVar("cameraShakeStarted", get_cameraShakeStarted, set_cameraShakeStarted);
		L.RegVar("allCameraShakesCompleted", get_allCameraShakesCompleted, set_allCameraShakesCompleted);
		L.RegVar("cameraShake", get_cameraShake, set_cameraShake);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Shake(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				CameraShake.Shake();
				return 0;
			}
			else if (count == 1)
			{
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
				CameraShake.Shake(arg0);
				return 0;
			}
			else if (count == 8)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 7);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 8);
				CameraShake.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				return 0;
			}
			else if (count == 9)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 4);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 7);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 8);
				System.Action arg8 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 9);
				CameraShake.Shake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraShake.Shake");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CancelShake(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				CameraShake.CancelShake();
				return 0;
			}
			else if (count == 1)
			{
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
				CameraShake.CancelShake(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraShake.CancelShake");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BeginShakeGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			CameraShake.BeginShakeGUI();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EndShakeGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			CameraShake.EndShakeGUI();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BeginShakeGUILayout(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			CameraShake.BeginShakeGUILayout();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EndShakeGUILayout(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			CameraShake.EndShakeGUILayout();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsShaking(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
			bool o = obj.IsShaking();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsCancelling(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
			bool o = obj.IsCancelling();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoShake(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
				obj.DoShake();
				return 0;
			}
			else if (count == 2)
			{
				CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
				obj.DoShake(arg0);
				return 0;
			}
			else if (count == 9)
			{
				CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 9);
				obj.DoShake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
				return 0;
			}
			else if (count == 10)
			{
				CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				float arg4 = (float)LuaDLL.luaL_checknumber(L, 6);
				float arg5 = (float)LuaDLL.luaL_checknumber(L, 7);
				float arg6 = (float)LuaDLL.luaL_checknumber(L, 8);
				bool arg7 = LuaDLL.luaL_checkboolean(L, 9);
				System.Action arg8 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 10);
				obj.DoShake(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraShake.DoShake");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoCancelShake(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
				obj.DoCancelShake();
				return 0;
			}
			else if (count == 2)
			{
				CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.DoCancelShake(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraShake.DoCancelShake");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoBeginShakeGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
			obj.DoBeginShakeGUI();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoEndShakeGUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
			obj.DoEndShakeGUI();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoBeginShakeGUILayout(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
			obj.DoBeginShakeGUILayout();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DoEndShakeGUILayout(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraShake obj = (CameraShake)ToLua.CheckObject<CameraShake>(L, 1);
			obj.DoEndShakeGUILayout();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cameras(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			System.Collections.Generic.List<UnityEngine.Camera> ret = obj.cameras;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cameras on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_numberOfShakes(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			int ret = obj.numberOfShakes;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index numberOfShakes on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shakeAmount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			UnityEngine.Vector3 ret = obj.shakeAmount;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shakeAmount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_rotationAmount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			UnityEngine.Vector3 ret = obj.rotationAmount;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rotationAmount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_distance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float ret = obj.distance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index distance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_speed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float ret = obj.speed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index speed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_decay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float ret = obj.decay;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index decay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_guiShakeModifier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float ret = obj.guiShakeModifier;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index guiShakeModifier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_multiplyByTimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			bool ret = obj.multiplyByTimeScale;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index multiplyByTimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, CameraShake.instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isShaking(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, CameraShake.isShaking);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isCancelling(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, CameraShake.isCancelling);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cameraShakeStarted(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_allCameraShakesCompleted(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cameraShake(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cameras(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			System.Collections.Generic.List<UnityEngine.Camera> arg0 = (System.Collections.Generic.List<UnityEngine.Camera>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.Camera>));
			obj.cameras = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cameras on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_numberOfShakes(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.numberOfShakes = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index numberOfShakes on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shakeAmount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.shakeAmount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shakeAmount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_rotationAmount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.rotationAmount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index rotationAmount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_distance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.distance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index distance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_speed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.speed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index speed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_decay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.decay = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index decay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_guiShakeModifier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.guiShakeModifier = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index guiShakeModifier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_multiplyByTimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraShake obj = (CameraShake)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.multiplyByTimeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index multiplyByTimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_instance(IntPtr L)
	{
		try
		{
			CameraShake arg0 = (CameraShake)ToLua.CheckObject<CameraShake>(L, 2);
			CameraShake.instance = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cameraShakeStarted(IntPtr L)
	{
		try
		{
			CameraShake obj = (CameraShake)ToLua.CheckObject(L, 1, typeof(CameraShake));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'CameraShake.cameraShakeStarted' can only appear on the left hand side of += or -= when used outside of the type 'CameraShake'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.cameraShakeStarted += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.cameraShakeStarted -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_allCameraShakesCompleted(IntPtr L)
	{
		try
		{
			CameraShake obj = (CameraShake)ToLua.CheckObject(L, 1, typeof(CameraShake));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'CameraShake.allCameraShakesCompleted' can only appear on the left hand side of += or -= when used outside of the type 'CameraShake'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.allCameraShakesCompleted += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action ev = (System.Action)arg0.func;
				obj.allCameraShakesCompleted -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cameraShake(IntPtr L)
	{
		try
		{
			CameraShake obj = (CameraShake)ToLua.CheckObject(L, 1, typeof(CameraShake));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'CameraShake.cameraShake' can only appear on the left hand side of += or -= when used outside of the type 'CameraShake'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> ev = (System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>)arg0.func;
				obj.cameraShake += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion> ev = (System.Action<UnityEngine.Camera,UnityEngine.Vector3,UnityEngine.Quaternion>)arg0.func;
				obj.cameraShake -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

