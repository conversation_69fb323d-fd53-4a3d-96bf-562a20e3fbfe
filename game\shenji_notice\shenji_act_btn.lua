ShenJiActBtn = ShenJiActBtn or BaseClass(BaseRender)

function ShenJiActBtn:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.ActivityButtonView, BindTool.Bind1(self.OnClickSelf, self))

    self:BindGlobalEvent(ShenJiEventType.ShenJiNotice_TaskInfoChange, BindTool.Bind1(self.OnShenJiTaskInfoChange, self))
    self:BindGlobalEvent(ShenJiEventType.ShenJiNotice_SaleInfoChange, BindTool.Bind1(self.OnShenJiSaleInfoChange, self))

    self:Flush()
end

function ShenJiActBtn:__delete()
    if CountDownManager.Instance:HasCountDown("ShenJiActBtn_key") then
        CountDownManager.Instance:RemoveCountDown("ShenJiActBtn_key")
    end
    if self.tweener ~= nil then
        self.tweener:Kill()
        self.tweener = nil
    end
    self.is_blink = nil
end

function ShenJiActBtn:OnFlush()
    local dataInstance = ShenJiNoticeWGData.Instance

    local time = 0
    local resource_cfg = nil
    local bundle, asset

    local is_open_special_sale = dataInstance:GetNowSpecialSaleStatus() ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE

    --暂时先屏蔽有关特卖的逻辑
    is_open_special_sale = false

    if is_open_special_sale then
        --特卖逻辑
        local now_special_sale_id = dataInstance:GetNowSpecialSaleId()
        -- resource_cfg = dataInstance:GetBtnResourceCfg(now_special_sale_id)
        resource_cfg = XianQiTeDianWGData.Instance:GetLingJiaSaleCfg(now_special_sale_id)
        time = dataInstance:GetNowSpecialSaleEndTime()
    else
        --现世逻辑
        -- resource_cfg = dataInstance:GetXianShiBtnResourceCfg()
        resource_cfg = XianQiTeDianWGData.Instance:GetLingJiaBtnShowCfg(-1)
        time = dataInstance:GetNowSpecialSaleStartTime()
    end

    if resource_cfg then
        --icon 特效
        -- bundle, asset = ResPath.GetEffectUi(resource_cfg.mainview_icon)
        -- self.node_list.icon_effect:ChangeAsset(bundle, asset)
        --text 图片
        bundle, asset = ResPath.GetF2MainUIImage(resource_cfg.mainview_txt)
        self.node_list.act_name.image:LoadSprite(bundle, asset, function()
            XUI.ImageSetNativeSize(self.node_list.act_name)
        end)
    end

    --倒计时
    if CountDownManager.Instance:HasCountDown("ShenJiActBtn_key") then
        CountDownManager.Instance:RemoveCountDown("ShenJiActBtn_key")
    end

    local remain_time = time - TimeWGCtrl.Instance:GetServerTime()
    if remain_time > 0 then
        self:UpdateTime(is_open_special_sale, 0, remain_time)
        CountDownManager.Instance:AddCountDown("ShenJiActBtn_key",
                BindTool.Bind(self.UpdateTime, self, is_open_special_sale),
                BindTool.Bind(self.CompleteTime, self, is_open_special_sale), nil, remain_time, 1)
        -- self.node_list.Image_bg:SetActive(true)
    else
        -- self.node_list.Image_bg:SetActive(false)
    end
	--
    ----红点
    --self.node_list.RedPoint:SetActive(dataInstance:IsShowShenJiNoticeRedPoint() == 1)
end

function ShenJiActBtn:OnShenJiTaskInfoChange()
    self:Flush()
end

function ShenJiActBtn:OnShenJiSaleInfoChange(special_sale_id)
    self:Flush()
end

function ShenJiActBtn:ClacTimeShow(time)
    if time > 0 then
        local time_tab = TimeUtil.Format2TableDHM2(time)
        if time_tab.day >= 1 then
            return string.format(Language.ShenJiNotice.XianShiEndTimeDay, time_tab.day)
        elseif time_tab.day < 1 and time_tab.hour >= 1 then
            -- return string.format(Language.ShenJiNotice.NextDayOpen, time_tab.hour)
            return Language.ShenJiNotice.NextDayOpen
		elseif time_tab.hour < 1 then
            -- return string.format("%02d:%02d", time_tab.min, time_tab.sec)
            return Language.ShenJiNotice.NextDayOpen
		end
    else
        return string.format("%02d:%02d:%02d", 0, 0, 0)
    end
end

local limit_time_to_blink = 600
function ShenJiActBtn:UpdateTime(is_special_sale, elapse_time, total_time)
    local temp_seconds = GameMath.Round(total_time - elapse_time)
    if is_special_sale then
        self:BlinkSwitch(temp_seconds < limit_time_to_blink)
    else
        self:BlinkSwitch(false)
    end
    if self.node_list and self.node_list.time then
        local calc_time = self:ClacTimeShow(temp_seconds)
        local str = string.format(Language.ShenJiNotice.BagEquipCellLockTime, calc_time)
        self.node_list.time.text.text = calc_time
    end
end

function ShenJiActBtn:CompleteTime(elapse_time, total_time)
    if self.node_list and self.node_list.end_time then
        self.node_list.time.text.text = ""
    end
end

--闪烁开关
function ShenJiActBtn:BlinkSwitch(bo)
    if self.is_blink == bo then
        return
    end
    self.is_blink = bo
    self:UpdateBlinkAnim()
end

function ShenJiActBtn:UpdateBlinkAnim()
    if self.is_blink then
        if self.tweener then
            self.tweener:Kill()
        end
        -- self.tweener = self.node_list.Image_bg.canvas_group:DoAlpha(1, 0, 0.5):SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    else
        if self.tweener then
            self.tweener:Kill()
            self.tweener = nil
        end
        -- self.node_list.Image_bg.canvas_group.alpha = 1
    end
end

function ShenJiActBtn:OnClickSelf()
    --local is_open_special_sale = ShenJiNoticeWGData.Instance:GetNowSpecialSaleStatus() ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    --if is_open_special_sale then
    --    --特卖逻辑
    --    local now_special_sale_id = ShenJiNoticeWGData.Instance:GetNowSpecialSaleId()
    --    ShenJiNoticeWGCtrl.Instance:OpenSpecialSaleView(now_special_sale_id)
    --else
    --    --现世逻辑
    --
    --    ShenJiNoticeWGCtrl.Instance:OpenXianShiView()
    --end
    ShenJiNoticeWGCtrl.Instance:OnMainUIBtnClickOpenView()
end