﻿using LuaInterface;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Events;
using System.Text;
using Nirvana;
using static GameRoot;

public class RuntimeLoginView : RuntimeBaseView
{
    enum DownloadState
    {
        WAIT,
        ING,
        FINISH,
    }

    enum GameSpeed
    {
        HIGH,
        MIN,
        LOW,
    }

    private bool isOpenDeveloperSwitch = false;
    private GameObject loginGameObj = null;
    private string user_name = "";
    private string server_id = "1";
    private bool isUseLocalLuaFile = false;
    private bool isSwitchQueryUrl = false;
    private bool isOpenCheck = false;
    private bool isDebugLuaAB = false;
    private bool isLuaProfiler = false;
    private string queryUrl = GameRoot.GetQueryUrl;
    private string agentId = "pev";
    private string updateUrl = "";
    private string version = "";
    private GameSpeed gamSpeed = GameSpeed.HIGH;
    private UnityAction playGameCallBack;
    private string buildTime = "";
    private string curUseSvnTime = "";
    private string newSvnTime = "";
    private Dictionary<string, string> md5Dic = new Dictionary<string, string>();
    Queue<string> waitDownloadQueue = new Queue<string>();
    private DownloadState donwloadState = DownloadState.WAIT;
    private int remainDownloadCount = 0;
    private float nextReqBuildTime = 0;

    private string windowsExeBuildTime = "";
    private string curWindowsExeSvnTime = "";
    private string newWindowsExeSvnTime = "";
    private string windowsExeSvnUrl = "svn://192.168.0.135/a2/fanli2/client/u3d_proj/Build/windows";
    private float nextReqWindowsExeBuildTime = 0;

    public RuntimeLoginView() : base(RuntimeViewName.LOGIN)
    {
        isHadCloseBtn = false;
    }

    public void SetPlayGameCallback(UnityAction playGameCallBack)
    {
        this.playGameCallBack = playGameCallBack;
    }

    override protected void OpenCallback()
    {
        loginGameObj = GameObject.Find("Loading");
        if (loginGameObj)
        {
            loginGameObj.SetActive(false);
        }

        isUseLocalLuaFile = PlayerPrefs.GetInt("a3_fanli_is_local_windows_debug_exe_lua") == 1;
        gamSpeed = (GameSpeed)PlayerPrefs.GetInt("a3_fanli_local_windows_debug_exe_net_speed");
        isOpenCheck = PlayerPrefs.GetInt("a3_fanli_is_local_windows_debug_check") == 1;
        isDebugLuaAB = PlayerPrefs.GetInt("a3_fanli_is_local_windows_debug_exe_lua_ab") == 1;
        isLuaProfiler = PlayerPrefs.GetInt("a3_fanli_is_local_windows_debug_exe_lua_profiler") == 1;

        if (PlayerPrefs.HasKey("a3_fanli_local_windows_debug_exe_query_url")) queryUrl = PlayerPrefs.GetString("a3_fanli_local_windows_debug_exe_query_url");
        if (PlayerPrefs.HasKey("a3_fanli_local_windows_debug_exe_agent_id")) agentId = PlayerPrefs.GetString("a3_fanli_local_windows_debug_exe_agent_id");

        this.Clear();

        user_name = PlayerPrefs.GetString("a3_fanli_quick_login_server_id");
        server_id = PlayerPrefs.GetString("a3_fanli_quick_login_server_id");

        if (string.IsNullOrEmpty(user_name))
        {
            user_name = string.Format("n{0}", Random.Range(1, 100000000));
        }

        if (string.IsNullOrEmpty(server_id))
        {
            server_id = "1";
        }

        RequestQueryUrl();
    }

    private void SaveValue()
    {
        PlayerPrefs.SetString("a3_fanli_local_windows_debug_exe_query_url", queryUrl);
        PlayerPrefs.SetString("a3_fanli_local_windows_debug_exe_agent_id", agentId);
        PlayerPrefs.SetInt("a3_fanli_local_windows_debug_exe_net_speed", (int)gamSpeed);
        PlayerPrefs.SetInt("a3_fanli_is_local_windows_debug_exe_lua", isUseLocalLuaFile ? 1 : 0);
        PlayerPrefs.SetInt("a3_fanli_is_local_windows_debug_exe_lua_ab", isDebugLuaAB ? 1 : 0);
        PlayerPrefs.SetInt("a3_fanli_is_local_windows_debug_check", isOpenCheck ? 1 : 0);
        PlayerPrefs.SetInt("a3_fanli_is_local_windows_debug_exe_lua_profiler", isLuaProfiler ? 1 : 0);
    }

    public bool IsUseLocalLuaFile()
    {
        return isUseLocalLuaFile;
    }

    public bool IsDebugLuaAB()
    {
        return isDebugLuaAB;
    }

    private void Clear()
    {
        md5Dic.Clear();
        waitDownloadQueue.Clear();
        remainDownloadCount = 0;
    }

    private void RequestQueryUrl()
    {
        string pkg_version = RuntimeGUIMgr.Instance.IsAndroidGM() ? "9.9.9" : "1.0.0";
        string device = DeviceTool.GetDeviceID();
        string sign = string.Format("device={0}?&pkg={1}&plat_id{2}&{3}", queryUrl, pkg_version, agentId, GameRoot.GLOBAL_URL_SIGN_KEY);
        string md5Sign = MD52.GetMD5(sign);
        string url = string.Format("{0}?plat_id={1}&pkg={2}&device={3}&sign={4}", queryUrl, agentId, pkg_version, device, md5Sign);

        Debug.LogFormat("request query url, {0}", url);
        UtilU3d.RequestGet(url, (bool isSucc, string data) =>
        {
            if (isSucc)
            {
                QueryData queryData;
                //外网url要做解密
                if (queryUrl.Equals("https://flgt.a3.jingyougate.com/v1/client/query.php"))
                {
                    ResponseStrData responseData = ResponseStrData.DecodeJson(data);
                    string decodeStr = GameRoot.DecodeJsonData(responseData.data, QueryURLBase64DecodeKey);
                    queryData = QueryData.DecodeJson(decodeStr);
                }
                else
                {
                    ResponseData responseData = ResponseData.DecodeJson(data);
                    queryData = responseData.data;
                }

                if (null == queryData.param_list)
                {
                    Debug.LogErrorFormat("[Error]SendRequest initUrl Error, param_list: {0}, res_encrypt_type: {1}", null == queryData.param_list, queryData.param_list.res_encrypt_type);
                    return;
                }

                updateUrl = queryData.param_list.cdn_url;
                // version = queryData.version_info.lua_version;
                if (string.IsNullOrEmpty(version)) version = Random.Range(0, 10000000).ToString();

                if (string.IsNullOrEmpty(updateUrl))
                {
                    Debug.LogErrorFormat("解析query.php失败, {0}, {1}", updateUrl, version);
                    return;
                }

                Debug.LogFormat("解析query.php成功, {0}, {1}", updateUrl, version);
                RuntimeGUIResMgr.Instance.SetUpdateUrl(updateUrl);
                RequestManifest();
                if (!RuntimeGUIMgr.Instance.IsAndroidGM())
                {
                    DownloadLuaAndLoadingView();
                }
                RequestBuildTime();
                RequestWindowsExeBuildTime();
            }
            else
            {
                Debug.LogErrorFormat("request query url fail {0}", url);
            }
        });
    }

    private void RequestManifest()
    {
        ++remainDownloadCount;
        string url = string.Format("{0}/AssetBundle.lua?version={1}", updateUrl, version);
        Debug.LogFormat("start download AssetBundle.lua, {0}", url);
        string cacheFilePath = Path.Combine(Application.persistentDataPath, "BundleCache/AssetBundle.lua");
        UtilU3d.Download(url, cacheFilePath, (bool isSucc, string content) =>
        {
            Debug.LogFormat("download AssetBundle.lua complete, {0} {1}", url, isSucc);
            if (isSucc) --remainDownloadCount;
        });

        ++remainDownloadCount;
        url = string.Format("{0}/LuaAssetBundle/LuaAssetBundle.lua?version={1}", updateUrl, version);
        Debug.LogFormat("start download LuaAssetBundle.lua, {0}", url);
        cacheFilePath = Path.Combine(Application.persistentDataPath, "BundleCache/LuaAssetBundle/LuaAssetBundle.lua");
        UtilU3d.Download(url, cacheFilePath, (bool isSucc, string content) =>
        {
            Debug.LogFormat("download LuaAssetBundle.lua complete, {0} {1}", url, isSucc);
            if (isSucc) --remainDownloadCount;
        });
    }

    public string GetCurUseSVNTime()
    {
        return curUseSvnTime;
    }

    public string GetNewSvnTime()
    {
        return newSvnTime;
    }

    public string GetCurWindowsExeSvnTime()
    {
        return curWindowsExeSvnTime;
    }

    public string GetNewWindowsExeSvnTime()
    {
        return newWindowsExeSvnTime;
    }

    public int GetGameSpeed()
    {
        return (int)gamSpeed;
    }

    private void RequestBuildTime()
    {
        UtilU3d.RequestGet(Path.Combine(updateUrl, "auto_build_time.txt"), (bool isSucc, string content) =>
        {
            buildTime = content;

            if (!string.IsNullOrEmpty(content))
            {
                var ary = content.Split(',');
                if (ary.Length > 0)
                {
                    string svnTime = ary[0].Trim();
                    if (string.IsNullOrEmpty(curUseSvnTime))
                    {
                        curUseSvnTime = svnTime;
                    }

                    newSvnTime = svnTime;
                    nextReqBuildTime = Time.realtimeSinceStartup + 30;
                }
            }
        });
    }

    private void RequestWindowsExeBuildTime()
    {
        if (string.IsNullOrEmpty(curWindowsExeSvnTime))
        {
            string filePath = Path.Combine(Application.dataPath, "auto_build_windows_exe_time.txt");
            filePath = filePath.Replace("\\", "/").Replace("Game_Data/", "");
            if (File.Exists(filePath))
            {
                using (StreamReader streamReader = new StreamReader(filePath))
                {
                    string line = streamReader.ReadLine();
                    if (!string.IsNullOrEmpty(line))
                    {
                        curWindowsExeSvnTime = line;
                    }
                }
            }
        }

        string windowsExeBuildTimeUrl = Path.Combine(updateUrl, "auto_build_windows_exe_time.txt");
        UtilU3d.RequestGet(windowsExeBuildTimeUrl, (bool isSucc, string content) =>
        {
            windowsExeBuildTime = content;
            if (!string.IsNullOrEmpty(content))
            {
                newWindowsExeSvnTime = windowsExeBuildTime;
                nextReqWindowsExeBuildTime = Time.realtimeSinceStartup + 10; 
            }
        });
    }

    public void Update()
    {
        if (nextReqBuildTime > 0 && Time.realtimeSinceStartup > nextReqBuildTime)
        {
            nextReqBuildTime = 0;
            RequestBuildTime();
        }

        if (nextReqWindowsExeBuildTime > 0 && Time.realtimeSinceStartup > nextReqWindowsExeBuildTime)
        {
            nextReqWindowsExeBuildTime = 0;
            RequestWindowsExeBuildTime();
        }

        if (this.IsOpening())
        {
            UpdateDownload();
            if (remainDownloadCount <= 0 && donwloadState != DownloadState.FINISH)
            {
                Debug.LogFormat("all download finish!");
                donwloadState = DownloadState.FINISH;
            }
        }
    }

    protected override Rect GetWindowRect()
    {
        return new Rect((Screen.width - 700) * 0.5f, (Screen.height - 300) * 0.5f, 700, isOpenDeveloperSwitch ? 400 : 280);
    }

    override protected void OnReapintWindow(int windowid)
    {
        GUILayout.Space(15);
        GUILayout.BeginHorizontal();

        user_name = GUILayout.TextField(user_name);
        server_id = GUILayout.TextField(server_id);
        GUILayout.EndHorizontal();

        GUILayout.Space(15);

        GUILayout.Label(string.Format("{0}?plat_id={1}&version={2}", queryUrl, agentId, version));
        GUILayout.BeginHorizontal();
        string downloadStateStr = "";
        if (waitDownloadQueue.Count > 0 &&
           ( donwloadState == DownloadState.WAIT || donwloadState == DownloadState.ING))
        {
            downloadStateStr = "(正在更新Lua...)";
        }
        GUILayout.Label(buildTime);
        GUILayout.Label(downloadStateStr);
        GUILayout.EndHorizontal();

        GUILayout.Space(15);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("清理缓存"))
        {
            string cachePath = Path.Combine(Application.persistentDataPath, "BundleCache");
            if (Directory.Exists(cachePath))
            {
                Directory.Delete(cachePath, true);
            }

            PlayerPrefs.DeleteAll();
        }

        if (GUILayout.Button("正常登陆"))
        {
            if (donwloadState != DownloadState.FINISH) return;

            this.SaveValue();
            PlayerPrefs.SetString("a3_fanli_is_quick_login", "0");
            this.Close();
            if (loginGameObj) loginGameObj.SetActive(true);
            this.playGameCallBack();
        }

        if (GUILayout.Button("快速登陆"))
        {
            if (donwloadState != DownloadState.FINISH) return;

            this.SaveValue();
            PlayerPrefs.SetString("a3_fanli_is_quick_login", "1");
            if (loginGameObj) loginGameObj.SetActive(true);
            this.playGameCallBack();

            this.Close();
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(15);
        isOpenDeveloperSwitch = GUILayout.Toggle(isOpenDeveloperSwitch, "开发者模式(研发人员专用)");
        if (isOpenDeveloperSwitch)
        {
            isUseLocalLuaFile = GUILayout.Toggle(isOpenDeveloperSwitch && isUseLocalLuaFile, "使用本地Lua(研发人员专用)");
            isOpenCheck = GUILayout.Toggle(isOpenDeveloperSwitch && isOpenCheck, "开启检查");
            isLuaProfiler = GUILayout.Toggle(isOpenDeveloperSwitch && isLuaProfiler, "开启LuaProfiler(性能调试专用)");
            isSwitchQueryUrl = GUILayout.Toggle(isOpenDeveloperSwitch && isSwitchQueryUrl, "设置QueryUrl");
            if (isSwitchQueryUrl)
            {
                GUILayout.BeginHorizontal();
                queryUrl = GUILayout.TextField(queryUrl);
                agentId = GUILayout.TextField(agentId);
                if (GUILayout.Button("应用") && donwloadState == DownloadState.FINISH)
                {
                    this.Clear();
                    RequestQueryUrl();
                }
                GUILayout.EndHorizontal();
            }

            GUILayout.BeginHorizontal();
            isDebugLuaAB = GUILayout.Toggle(isOpenDeveloperSwitch && isDebugLuaAB, "开启外网Lua调试(外网查Bug专用)");
            if (isDebugLuaAB)
            {
                string luaDebugDir = string.Format("{0}/Lua", Application.persistentDataPath);
                if (Directory.Exists(luaDebugDir)) GUILayout.Label(luaDebugDir);
                else GUILayout.Label("找不到调试代码");
            }
            GUILayout.EndHorizontal();
        }
        else
        {
            isUseLocalLuaFile = false;
            isOpenCheck = false;
            isLuaProfiler = false;
            isSwitchQueryUrl = false;
            isDebugLuaAB = false;
        }
        GUILayout.Space(15);
        GUILayout.BeginHorizontal();
        GUILayout.Label("游戏速度:", GUILayout.Width(60));
        if (GUILayout.Toggle(gamSpeed == GameSpeed.HIGH, "高速", GUILayout.Width(50)))
        {
            gamSpeed = GameSpeed.HIGH;
        }
       if (GUILayout.Toggle(gamSpeed == GameSpeed.MIN, "中速", GUILayout.Width(50)))
        {
            gamSpeed = GameSpeed.MIN;
        }
        if (GUILayout.Toggle(gamSpeed == GameSpeed.LOW, "低速", GUILayout.Width(50)))
        {
            gamSpeed = GameSpeed.LOW;
        }
        GUILayout.EndHorizontal();
    }

    private void DownloadLuaAndLoadingView()
    {
        donwloadState = DownloadState.WAIT;
        string lua_md5_url = string.Format("{0}/LuaAssetBundle/lua_md5_list.txt?version={1}", updateUrl, version);
        string cacheDir = Path.Combine(Application.persistentDataPath, "BundleCache/LuaAssetBundle");
        string streamingDir = Path.Combine(Application.streamingAssetsPath, "AssetBundle");
        string cacheFilePath = Path.Combine(cacheDir, "lua_md5_list.txt");
        md5Dic.Clear();
        Debug.LogFormat("download md5 list file, {0}", lua_md5_url);
        UtilU3d.Download(lua_md5_url, cacheFilePath, (bool isSucc, string content) =>
        {
            Debug.LogFormat("download md5 list file finish, {0}", isSucc);
            string[] md5List = File.ReadAllLines(cacheFilePath);
            for (int i = 0; i < md5List.Length; i++)
            {
                string[] ary = md5List[i].Split(' ');
                string bundlePath = ary[0];
                string md5 = ary[1];
                md5Dic.Add(bundlePath, md5);

                if (bundlePath.StartsWith("luajit/"))
                {
                    continue;
                }

                var bundleMd5Path = bundlePath + "-" + md5;
                if (!File.Exists(Path.Combine(cacheDir, bundleMd5Path)) && !File.Exists(Path.Combine(streamingDir, bundleMd5Path)))
                {
                    waitDownloadQueue.Enqueue(bundlePath);
                }
            }

            remainDownloadCount += waitDownloadQueue.Count;
            donwloadState = remainDownloadCount > 0 ? DownloadState.ING : DownloadState.FINISH;
        });

        //这里需要下载最新的LoadingView.prefab
        string assetbundle_md5_url = string.Format("{0}/md5_list.txt?version={1}", updateUrl, version);
        string loadingViewBundleName = "uis/view/loading_prefab";
        string assetBundleCacheDir = Path.Combine(Application.persistentDataPath, "BundleCache");
        string assetBundleCacheFilePath = Path.Combine(assetBundleCacheDir, "md5_list.txt");
        string loading_prefab_UrlPath = "";
        string loading_prefab_BundleCachePath = "";
        UtilU3d.Download(assetbundle_md5_url, assetBundleCacheFilePath, (bool isSucc, string content) =>
        {
            if (isSucc)
            {
                string[] md5List = File.ReadAllLines(assetBundleCacheFilePath);
                foreach (var item in md5List)
                {
                    string[] ary = item.Split(' ');
                    if (ary[0] == loadingViewBundleName)
                    {
                        loading_prefab_UrlPath = $"uis/view/loading_prefab?v={ary[1]}";
                        loading_prefab_BundleCachePath = $"uis/view/loading_prefab-{ary[1]}";
                        break;
                    }
                }
                if (!string.IsNullOrEmpty(loading_prefab_UrlPath))
                {
                    UtilU3d.Download($"{updateUrl}/{loading_prefab_UrlPath}", $"{assetBundleCacheDir}/{loading_prefab_BundleCachePath}"
                        , (bool isSucc2, string content2) =>
                        {
                            if (!isSucc2)
                            {
                                Debug.LogError("初始化阶段更新loading_prefab失败，请检查");
                            }
                        });
                }
                else
                {
                    Debug.LogError("初始化阶段更新loading_prefab失败，md5找不到对应信息，请检查");
                }
            }
        });
    }

    private void UpdateDownload()
    {
        if (waitDownloadQueue.Count <= 0)
        {
            return;
        }

        string bundlePath = waitDownloadQueue.Dequeue();
        string bundleCache = Path.Combine(Application.persistentDataPath, "BundleCache");
        string url = string.Format("{0}/LuaAssetBundle/{1}?version={2}", updateUrl, bundlePath, version);
        string savePath = string.Format("{0}/LuaAssetBundle/{1}-{2}", bundleCache, bundlePath, md5Dic[bundlePath]);
        UtilU3d.Download(url, savePath, (bool isSucc, string content) =>
        {
            if (isSucc)
            {
                Debug.LogFormat("update lua, {0}", url);
                remainDownloadCount = remainDownloadCount - 1;
            }
        });
    }
 }
