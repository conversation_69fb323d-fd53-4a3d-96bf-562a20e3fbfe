require("game/fireworks_draw2/fireworks_draw2_wg_data")
require("game/fireworks_draw2/fireworks_draw2_view")
require("game/fireworks_draw2/fireworks_draw2_gailv")
require("game/fireworks_draw2/fireworks_draw2_reward")

FireWorksDrawSecondWGCtrl = FireWorksDrawSecondWGCtrl or BaseClass(BaseWGCtrl)
function FireWorksDrawSecondWGCtrl:__init()
	if FireWorksDrawSecondWGCtrl.Instance then
		ErrorLog("[FireWorksDrawSecondWGCtrl] Attemp to create a singleton twice !")
	end
	FireWorksDrawSecondWGCtrl.Instance = self

    self.data = FireWorksDrawSecondWGData.New()
	self.view = FireWorksDrawSecondView.New(GuideModuleName.FireWorksDrawSecondView)
    self.fire_works_gailv = FireWorksDrawSecondProbabilityView.New() --烟花抽奖概率展示
    self.fire_works_reward = FireWorksDrawSecondReward.New() --烟花抽奖恭喜获得

    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
    self:RegisterAllProtocols()
end

function FireWorksDrawSecondWGCtrl:__delete()
	FireWorksDrawSecondWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

    if self.fire_works_reward then
		self.fire_works_reward:DeleteMe()
		self.fire_works_reward = nil
	end

	if self.fire_works_gailv then
		self.fire_works_gailv:DeleteMe()
		self.fire_works_gailv = nil
	end

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function FireWorksDrawSecondWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAFireworksDraw2Info, "OnSCOAFireworksDraw2Info") --烟花抽奖次数
	self:RegisterProtocol(SCOAFireworksDraw2Result, "OnSCOAFireworksDraw2Result") --烟花抽奖结果信息
end

--请求信息
function FireWorksDrawSecondWGCtrl:SendFireWokrsInfo(opera_type, param1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2
	protocol.opera_type = opera_type
	protocol.param_1 = param1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

--烟花抽奖2次数
function FireWorksDrawSecondWGCtrl:OnSCOAFireworksDraw2Info(protocol)
	--print_error("烟花抽奖2次数", protocol)
	self.data:SetFireWorksInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

--烟花抽奖2结果信息
function FireWorksDrawSecondWGCtrl:OnSCOAFireworksDraw2Result(protocol)
	--print_error("烟花抽奖结果信息", protocol)
	self.data:SetFireWorksResultInfo(protocol)
	local data_list = self.data:GetFireWorksResultInfo()
	self:OpenRewardView(data_list.record_list)
end

--烟花抽奖2恭喜获得界面
function FireWorksDrawSecondWGCtrl:OpenRewardView(data)
    self.fire_works_reward:SetData(data)
    self.fire_works_reward:Open()
end

--使用道具并弹窗
function FireWorksDrawSecondWGCtrl:ClickUseDrawItem(index, func)
	local cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawConsumeCfg()
	local mode_cfg = FireWorksDrawSecondWGData.Instance:GetFireWorksDrawItem()
	local cur_cfg = cfg[index]
	if cur_cfg == nil then
		return
	end
	
	local num = ItemWGData.Instance:GetItemNumInBagById(mode_cfg.cost_item_id)
	--不足弹窗
	if num < cur_cfg.cost_item_num then
		 if not self.alert then
			 self.alert = Alert.New()
		 end
		 self.alert:ClearCheckHook()
		 self.alert:SetShowCheckBox(true, "fireworks_draw2")
		 self.alert:SetCheckBoxDefaultSelect(false)
		 local item_cfg = ItemWGData.Instance:GetItemConfig(mode_cfg.cost_item_id)
		 local name = ""
		 if item_cfg ~= nil then
			 name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		 end

		 local cost = mode_cfg.cost_gold * (cur_cfg.cost_item_num - num)
		 local str = string.format(Language.FireWorksDrawSecond.DrawCostStr, name, cost)
		 self.alert:SetLableString(str)
		 self.alert:SetOkFunc(func)
		 self.alert:Open()
	else
		--使用
		func()
	end
 
 end

--烟花抽奖概率面板
function FireWorksDrawSecondWGCtrl:OpenGaiLvView()
    self.fire_works_gailv:Open()
end

function FireWorksDrawSecondWGCtrl:OnDayChange()
    if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2) then
		--请求烟花抽奖信息
		self:SendFireWokrsInfo(OA_FIREWORKS_DRAW2_OPERATE_TYPE.INFO)
	end
end