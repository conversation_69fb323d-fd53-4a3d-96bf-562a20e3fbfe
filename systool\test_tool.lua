function DoTestLogic1()
	-- 恭喜获得
	-- local protocol = {appe_image_id = 2, appe_type = 33}
	-- AppearanceWGCtrl.Instance:OnGetNewAppearance(protocol)
	-- 天神晋升成功
	-- TianShenWGCtrl.Instance:OpenShenShiUpGradeTip(0)
	-- 功能开启
	-- local fun_cfg = FunOpen.Instance:GetFunByName("boss_vip")
	-- local mian_view = MainuiWGCtrl.Instance:GetView()
	-- local target_obj = mian_view:GetMainUICanFlyButtonObj(fun_cfg)
	-- TipWGCtrl.Instance:ShowOpenFunFlyView(fun_cfg, target_obj)
	-- 技能激活
	-- TipWGCtrl.Instance:ShowGetNewSkillView(1001, nil)
	-- 场景名
	-- TipWGCtrl.Instance:ShowEneterCommonSceneView(1001)
	-- 飘提示s
	-- local str = string.format(Language.SysRemind.AddItem, "测试道具", 1000)
	-- SysMsgWGCtrl.Instance:ErrorRemind(str)
	-- 跳任务
	-- SysMsgWGCtrl.SendGmCommand("setrolelevel", 120)
	-- SysMsgWGCtrl.SendGmCommand("jumptotrunk", 1260)

	-- FunOpen.Instance:OpenViewNameByCfg("ProfessWallView#profess_wall_xunyuan#op=xunyuan_task")
	-- MainuiWGCtrl.Instance:FlushFPAdvanceNotice()
	-- FunctionGuide.Instance:ClearViewOpenTimesCache()
	-- ClientCmdWGCtrl.Instance:Cmd(string.sub(text, blank_end + 1, len))

	SysMsgWGCtrl.SendGmCommand("setrolelevel", 300)
	SysMsgWGCtrl.SendGmCommand("jumptotrunk", 3000)
	-- SysMsgWGCtrl.SendGmCommand("additem", "101 1 0")

	-- SysMsgWGCtrl.SendGmCommand("addeffect", 353)
end

function DoTestLogic2()
	-- 传闻
	-- print_error(table.concat(list, ","))
	-- SettingWGCtrl.Instance:FinishedOpenFun(true)
	-- local str = "天降异宝！{r;6553806;1p21mh;0} 前往{chestshop;1}寻宝，发现了{i;26362}，真乃欧皇附体！{openLink;152}"
	-- TipWGCtrl.Instance:ShowSystemScroll(str)
	--时区
	-- ClassPrint("P1", "---时区---", TimeUtil.GetLocalTimeZoneAndDiff())
	-- NovicePopDialogWGCtrl.Instance:CheckCompletedTaskTrigger(50)
	-- ClientCmdWGCtrl.Instance:GetGuaJiState()
	-- SysMsgWGCtrl.SendGmCommand("sethp", 0)

	-- FunctionGuide.Instance:TriggerGuideById(23)

	-- 重连游戏服
	GameNet.Instance:AsyncConnectGameServer(5, function (is_succ)
		-- 未能连接游戏服失败
		if not is_succ then
			if TipWGCtrl.Instance ~= nil then
				TipWGCtrl.Instance:ShowDisconnected()
			end
		end
	end)
end

--------------------------------------------------------------------------
--测试工具 在开发阶段提供相关的工具
--------------------------------------------------------------------------
GAMENET_SEND_DEBUG_SWITCH = false					-- 打印发送协议
GAMENET_RECV_DEBUG_SWITCH = false					-- 打印接收协议
GLOBAL_SHOW_ITEM_ID_SWITCH = false					-- 点击道具显示item信息
GLOBAL_SYSTEM_TIPS_SWITCH = false					-- 打印系统飘字内容
GLOBAL_AUTO_RUN_TASK_SWITCH = true					-- 自动跑任务开关
GLOBAL_MAIN_TASK_POP_SWITCH = false					-- 主线弹窗打印开关
GLOBAL_DELETE_ME_CHECK_SWITCH = false				-- 客户端DeleteMe检查
ClassPrintSwitch = false							-- 分类打印
TestViewLog = false

-- GLOBAL_SHOW_RT_RECT = true
GLOBAL_SHOW_RT_DRAG = false

local PrintType = {}
MyClassPrintType = 0
local IsEditor = UnityEngine.Application.isEditor
if IsEditor then
	local get_int = UnityEngine.PlayerPrefs.GetInt
	GAMENET_SEND_DEBUG_SWITCH = get_int("send_protocol_debug_switch") == 1
	GAMENET_RECV_DEBUG_SWITCH = get_int("recv_protocol_debug_switch") == 1
	GLOBAL_SHOW_ITEM_ID_SWITCH = get_int("show_item_id_switch") == 1
	GLOBAL_AUTO_RUN_TASK_SWITCH = get_int("auto_run_task_switch") ~= 1
	GLOBAL_SYSTEM_TIPS_SWITCH = get_int("system_tips_switch") == 1
	GLOBAL_MAIN_TASK_POP_SWITCH = get_int("main_task_pop_switch") == 1
	GLOBAL_DELETE_ME_CHECK_SWITCH = get_int("client_deleteMe_switch") == 1

	PrintType = {
		[0] = "#空#",
		"#版图数据#",
	
		P1 = 1,
		P2 = 2,
		P3 = 3,
		P4 = 4,
		P5 = 5,
	}

	ClassPrintSwitch = get_int("ClassPrintSwitch") == 1
	MyClassPrintType = get_int("MyClassPrintType")
end

-- 分类打印
-- ClassPrint(P1, ...)
function ClassPrint( ... )
	if ClassPrintSwitch then
		local prame_type = select(1, ...)
		if MyClassPrintType == PrintType[prame_type] then
			print_error(select(2, ...))
		end
	end
end

function TestLogic(func)
	if ClassPrintSwitch then
		func()
	end
end

function TestFlag()
	if ClassPrintSwitch then
		return true
	end

	return false
end

-- 满足条件输出分类打印
function TestPrintCondition(condition, account, ... )
	if ClassPrintSwitch then
		if condition then
			ClassPrint(account, ... )
		end
	end
end

-- 测试耗时
function TestPerformance(func)
	local fun = function ( ... )
		local time = socket.gettime()
		func( ... )
		print_error("测试结果 所用耗时:", socket.gettime() - time)
	end
	return fun
end

--------------------------------------------------------------------------
--保护表 可以追踪表的调用与修改
--------------------------------------------------------------------------
function ProtectTable(t1, set_func, get_func)
	local set = {}
	
	for k, v in pairs(t1) do
		set[k] = v
		t1[k] = nil
	end
	setmetatable(t1, {__newindex = function (t, k, v)
		if set_func then
			set_func(k, v)
		end
		rawset(set, k, v)
	end, __index = function (t,k)
		if get_func then
			get_func(k)
		end
		return rawget(set, k)
	end})
end


--- 执行流监控
LogFunc = {}
LogFunc.time = socket.gettime()
LogFunc.func_info = {}
function LogFunc.SetInfo(func)
	local time = socket.gettime() - LogFunc.time
	LogFunc.time = socket.gettime()
	local t = {
		func,
		"当前帧数",
		Runner.frame_count,
		"执行开始",
		time,
		"\n",
		debug.traceback(),
		"\n",
	}
	local s = table.concat(t)
	table.insert(LogFunc.func_info, s)
end

function LogFunc.Log()
	local file_path = "LuaFunctionLog.txt"
	local file = io.open(file_path, "w")

	local tbl = table.concat(LogFunc.func_info)
	file:write(tbl)
	file:close()
end

function SetTestObj(obj)
	if ClassPrintSwitch then
		obj.is_test_check_obj = true
	end
end

--==============================================================================
function GameDebugSwitch(...)
	local parma1 = select(1, ...)
	local parma2 = select(2, ...)
	if parma2 then
		parma2 = tonumber(parma2)
	end

	if parma1 == "xinShou" then
		SysMsgWGCtrl.SendGmCommand("changespeed", "3000")
		SysMsgWGCtrl.SendGmCommand("changegongji", "9999999999")
		SysMsgWGCtrl.SendGmCommand("sethp", "9999999999")
		print_error("进入快速模式")

	elseif parma1 == "resetRoleAttr" then
		SysMsgWGCtrl.SendGmCommand("resetgmchangeattr", "")
		TipsSystemManager.Instance:ShowSystemTips("重登游戏，恢复角色正常属性")
		print_error("恢复正常模式")

	elseif parma1 == "sendProtocol" then
		GAMENET_SEND_DEBUG_SWITCH = UnityEngine.PlayerPrefs.GetInt("send_protocol_debug_switch") == 1
		print_error("发送协议打印", GAMENET_SEND_DEBUG_SWITCH)

	elseif parma1 == "recvProtocol" then
		GAMENET_RECV_DEBUG_SWITCH = UnityEngine.PlayerPrefs.GetInt("recv_protocol_debug_switch") == 1
		print_error("接收协议打印", GAMENET_RECV_DEBUG_SWITCH)

	elseif parma1 == "sysTips" then
		GLOBAL_SYSTEM_TIPS_SWITCH = UnityEngine.PlayerPrefs.GetInt("system_tips_switch") == 1
		print_error("系统飘字", GLOBAL_SYSTEM_TIPS_SWITCH)

	elseif parma1 == "showItemId" then
		GLOBAL_SHOW_ITEM_ID_SWITCH = UnityEngine.PlayerPrefs.GetInt("show_item_id_switch") == 1
		print_error("点击道具显示信息", GLOBAL_SHOW_ITEM_ID_SWITCH)

	elseif parma1 == "autoRunTask" then
		GLOBAL_AUTO_RUN_TASK_SWITCH = UnityEngine.PlayerPrefs.GetInt("auto_run_task_switch") ~= 1
		print_error("自动跑任务", GLOBAL_AUTO_RUN_TASK_SWITCH)
		FunctionGuide.Instance:SetOpenFunGuide(GLOBAL_AUTO_RUN_TASK_SWITCH)

	elseif parma1 == "classPrint" then
		ClassPrintSwitch = UnityEngine.PlayerPrefs.GetInt("ClassPrintSwitch") == 1
		print_error("启动分类打印", ClassPrintSwitch)

	elseif parma1 == "changePrintType" and parma2 then
		MyClassPrintType = parma2
		print_error("分类打印工具开启：", ClassPrintSwitch, "当前打印类型：", MyClassPrintType, PrintType[MyClassPrintType])
	elseif parma1 == "main_task_pop" then
		GLOBAL_MAIN_TASK_POP_SWITCH = UnityEngine.PlayerPrefs.GetInt("main_task_pop_switch") == 1
		print_error("主线弹窗打印", GLOBAL_MAIN_TASK_POP_SWITCH)
	elseif parma1 == "delete_me_check" then
		GLOBAL_DELETE_ME_CHECK_SWITCH = UnityEngine.PlayerPrefs.GetInt("client_deleteMe_switch") == 1
		print_error("客户端DeleteMe检查", GLOBAL_DELETE_ME_CHECK_SWITCH)
	end
end

SceneObjPartStr = {
	[0] = "主体",
	"武器",
	"武器2",
	"翅膀",
	"坐骑",
	"特效",
	"光环",
	"", --旧战斗坐骑
	"宝具",
	"披风",
	"法阵",
	"抱美人",
	"头部",
	"足迹",
	"麒麟臂",
	"腰饰",
	"面饰",
	"尾巴",
	"手环",
	"剑阵",
	"高达 左臂",
	"高达 右臂",
	"高达 左腿",
	"高达 右腿",
	"高达 左翼",
	"高达 右翼",
	[34] = "技能光环",
	[36] = "武魂真身",
	[37] = "战斗坐骑",
}