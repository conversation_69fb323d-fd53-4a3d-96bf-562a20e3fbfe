AddBlacklistView = AddBlacklistView or BaseClass(SafeBaseView)

function AddBlacklistView:__init()
	-- self.data = GuildWGData.Instance
	-- self:SetModal(true)
	-- self.config_tab = {	
	-- 	{"chat_ui_cfg", 6, {0}},
	-- }
	self:AddViewResource(0, "uis/view/chat_ui_prefab", "layout_addblack")
end

function AddBlacklistView:__delete()
	self.input_name = nil
end

function AddBlacklistView:LoadCallBack()
	self.input_name = self.node_list["inp_findname"]:GetComponent(typeof(TMPro.TMP_InputField))
	XUI.AddClickEventListener(self.node_list["btn_addbl_ok"], BindTool.Bind1(self.OnAddBlacklist, self))
	XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind1(self.OnCloseView, self))
	XUI.AddClickEventListener(self.node_list["btn_close_window0"], BindTool.Bind1(self.OnCloseView, self))
end

function AddBlacklistView:OnCloseView()
	self:Close()
end

-- 添加黑名单
function AddBlacklistView:OnAddBlacklist()
	if self.node_list["inp_findname"] ~= nil then
		-- print_error("black------------------------- ")
		local game_name = self.input_name.text
		self.input_name.text = ""
		local msg_ctrl = SysMsgWGCtrl.Instance
		if nil ~= game_name and "" ~= game_name then
			if ChatWGData.Instance:InBlacklist(nil, game_name) then
				if nil ~= Language.Chat["AlreadyYouFriend"] then
					msg_ctrl:ErrorRemind(Language.Chat["AlreadyYouFriend"])
				end
				return
			end

			SocietyWGCtrl.Instance:GetUserInfoByName(2151,game_name,function (flag, user_info)
				--找不到角色的时候
				if 0 == flag or nil == user_info then
					if nil ~= Language.Society["UserNotExist"] then
						msg_ctrl:ErrorRemind(Language.Society["UserNotExist"])
					end
					return
				end

				--添加的人为自己的时候
				if user_info.role_id == GameVoManager.Instance:GetMainRoleVo().role_id then
					if nil ~= Language.Chat["NotAddSelf"] then
						msg_ctrl:ErrorRemind(Language.Chat["NotAddSelf"])
					end
					return
				end
				--print_error("------------yougaijuese")	
				ChatWGCtrl.Instance:SendAddBlackReq(user_info.role_id)
			end)
		end
	end

	-- if nil ~= self.node_t_list.edit_name then
	
	-- 	local msg_ctrl = SysMsgWGCtrl.Instance
	-- 	if nil ~= game_name and "" ~= game_name then
	-- 		if ChatWGData.Instance:InBlacklist(nil, game_name) then
	-- 			if nil ~= Language.Chat["AlreadyYouFriend"] then
	-- 				msg_ctrl:ErrorRemind(Language.Chat["AlreadyYouFriend"])
	-- 			end
	-- 			return
	-- 		end

	-- 		SocietyWGCtrl.Instance:GetUserInfoByName(2151,game_name,function (flag, user_info)
	-- 			--找不到角色的时候
	-- 			if 0 == flag or nil == user_info then
	-- 				if nil ~= Language.Society["UserNotExist"] then
	-- 					msg_ctrl:ErrorRemind(Language.Society["UserNotExist"])
	-- 				end
	-- 				return
	-- 			end

	-- 			--添加的人为自己的时候
	-- 			if user_info.role_id == GameVoManager.Instance:GetMainRoleVo().role_id then
	-- 				if nil ~= Language.Chat["NotAddSelf"] then
	-- 					msg_ctrl:ErrorRemind(Language.Chat["NotAddSelf"])
	-- 				end
	-- 				return
	-- 			end
					
	-- 			ChatWGCtrl.Instance:SendAddBlackReq(user_info.role_id)
	-- 		end)
	-- 	end
	-- end
end