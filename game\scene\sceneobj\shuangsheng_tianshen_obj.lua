ShuangShengTianShenObj = ShuangShengTianShenObj or BaseClass(Character)

function ShuangShengTianShenObj:__init(vo)
	self.obj_type = SceneObjType.ShuangShengTianShen
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.ShuangShengTianShen

    self.owner_obj_id = vo.owner_obj_id
    self.owner_obj = vo.owner_obj
    self.shaungsheng_tianshen_aura_id = vo.shaungsheng_tianshen_aura_id
	self.shaungsheng_tianshen_appid = nil
	self.bundle_name = ""
	self.asset_name = ""
	self.vo = vo

	-- 定时器
	self.cancel_attach_action = nil
    self.effect_handle = nil

	self:InitInfo()
	self:InitAppearance()
end

function ShuangShengTianShenObj:__delete()
	if self.owner_obj then
		self.owner_obj:ReleaseShuangShengTianShenObj()
	end

	self.shaungsheng_tianshen_appid = nil
	self.owner_obj = nil

	self.is_using_skill = false


	ReuseableHandleManager.Instance:ReleaseShieldHandle(self.effect_handle)
	self.effect_handle = nil

	self:CancelPlayActionBackFun()
	self:RemoveAttachMainDelayTime()
end

function ShuangShengTianShenObj:DeleteDrawObj()
	if not self:IsRealDead() then
		Character.DeleteDrawObj(self)
		return
	end

	if nil ~= self.draw_obj then
		local draw_obj = self.draw_obj
		self.draw_obj = nil
        draw_obj:DeleteMe()
	end
end

function ShuangShengTianShenObj:InitInfo()
	Character.InitInfo(self)
	local tianshen_avatar_appid_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(self.shaungsheng_tianshen_aura_id)
	self.shaungsheng_tianshen_appid = tianshen_avatar_appid_cfg and tianshen_avatar_appid_cfg.appe_image_id or 0
end

-- 重写CheckModleScale方法 避免放大缩小给限制了
function ShuangShengTianShenObj:CheckModleScale()

end

function ShuangShengTianShenObj:CancelPlayActionBackFun()
    if self.play_action_back_fun then
        GlobalTimerQuest:CancelQuest(self.play_action_back_fun)
    end
    self.play_action_back_fun = nil
end

function ShuangShengTianShenObj:ChangeShuangShengImageId(shaungsheng_tianshen_aura_id)
	self.shaungsheng_tianshen_aura_id = shaungsheng_tianshen_aura_id
	local tianshen_avatar_appid_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarAppImageIdCfgData(self.shaungsheng_tianshen_aura_id)
	self.shaungsheng_tianshen_appid = tianshen_avatar_appid_cfg and tianshen_avatar_appid_cfg.appe_image_id or 0
	self:InitAppearance()
end


function ShuangShengTianShenObj:InitAppearance()
	if self.obj_scale ~= nil then
		local transform = self.draw_obj:GetRoot().transform
		transform.localScale = Vector3(self.obj_scale, self.obj_scale, self.obj_scale)
	end

	local bundle, asset = ResPath.GetShuangShengModel(self.shaungsheng_tianshen_appid)
	self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("ShuangShengTianshen", self.shaungsheng_tianshen_appid))
	self:InitModel(bundle, asset)
end

function ShuangShengTianShenObj:InitModel(bundle, asset)
	if bundle == nil or asset == nil then
		self:VisibleChanged(false)
		return
	else
		self:VisibleChanged(true)
	end
	self.bundle_name = bundle
	self.asset_name = asset
	self:ChangeModel(SceneObjPart.Main, bundle, asset, BindTool.Bind(self.AttachToRoleMainPart, self))
end

function ShuangShengTianShenObj:AttachToRoleMainPart()
	if (not self.owner_obj) or (not self.owner_obj.draw_obj) or (not self.draw_obj) then
		return
	end

	if self.bundle_name == nil or self.bundle_name == "" or self.asset_name == nil or self.asset_name == "" then
		return
	end

	local main_part = self.owner_obj.draw_obj:GetPart(SceneObjPart.Main)
	local self_draw_obj = self.draw_obj:GetPart(SceneObjPart.Main).obj

	if (not main_part) or (not main_part.obj) or (not self_draw_obj) then
		self:RemoveAttachMainDelayTime()
		self.cancel_attach_action = GlobalTimerQuest:AddDelayTimer(function ()
			self:AttachToRoleMainPart()
		end, 0.4)
		return
	end

	local attachment = main_part.obj.actor_attachment

	if IsNil(attachment) then
		self:RemoveAttachMainDelayTime()
		self.cancel_attach_action = GlobalTimerQuest:AddDelayTimer(function ()
			self:AttachToRoleMainPart()
		end, 0.4)
		return
	end

	if attachment then
		self_draw_obj.gameObject:SetActive(true)
		local point = attachment:GetAttachPoint(PartAttachPoint[SceneObjPart.ShuangShengTianShen])

		if not IsNil(point) and self_draw_obj.attach_obj then
			self_draw_obj.attach_obj:SetAttached(point)
			self_draw_obj.attach_obj:SetTransform(attachment.Prof)
		end
	else
		self_draw_obj.gameObject:SetActive(false)
	end
end

function ShuangShengTianShenObj:GetShuangShengTianShenAuraId()
	return self.vo.shaungsheng_tianshen_aura_id
end

function ShuangShengTianShenObj:EnterStateStand()
	Character.EnterStateStand(self)
end

--移除回调3
function ShuangShengTianShenObj:RemoveAttachMainDelayTime()
    if self.cancel_attach_action then
        GlobalTimerQuest:CancelQuest(self.cancel_attach_action)
        self.cancel_attach_action = nil
    end
end

function ShuangShengTianShenObj:OnDie()
	Character.OnDie(self)

	local part_obj = self.draw_obj:GetPart(SceneObjPart.Main):GetObj()

    if nil ~= part_obj and nil ~= part_obj.actor_ctrl then
        part_obj.actor_ctrl:StopEffects()
    end
end

function ShuangShengTianShenObj:IsShuangShengTianShen()
	return true
end

function ShuangShengTianShenObj:OnModelLoaded(part, obj)
	if not self.owner_obj then
		return
	end

	if self:IsDeleted() or self.owner_obj:IsDead() then
		return
	end

	Character.OnModelLoaded(self, part, obj)
end

function ShuangShengTianShenObj:GetOwnerObjID()
	return self.owner_obj_id
end

function ShuangShengTianShenObj:SetOwnerObj(owner_obj)
	self.owner_obj = owner_obj
end

function ShuangShengTianShenObj:GetOwnerObj()
	return self.owner_obj
end

function ShuangShengTianShenObj:PartEffectVisibleChanged(part, visible)
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function ShuangShengTianShenObj:SetEffectQualityLevelOffset(offset)
	if self.effect_handle then
		self.effect_handle:SetQualityLevelOffset(offset)
	end
end

function ShuangShengTianShenObj:UpdateQualityLevel()
	local base_offset = -1
	local owner_obj = self.owner_obj
	if owner_obj and owner_obj.IsMainRole and owner_obj:IsMainRole() then
		base_offset = 1
	end

	local model_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetQualityLevelOffset(model_offset)

	local effect_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetEffectQualityLevelOffset(effect_offset)
end

function ShuangShengTianShenObj:UpdateEffectVisible()
	local owner_obj = self.owner_obj
	if self.effect_handle and owner_obj and not owner_obj:IsDeleted() and not self.owner_obj:IsDead() and owner_obj.GetRoleEffectVisible then
		local visible = owner_obj:GetRoleEffectVisible()

		if visible then
		    self.effect_handle:CancelForceSetVisible()
		else
		    self.effect_handle:ForceSetVisible(false)
		end
	end
end

function ShuangShengTianShenObj:GetActionTimeRecord(anim_name)
	local atr = ShuangShengTianShenActionConfig[self.shaungsheng_tianshen_appid]
	if atr ~= nil then
		return atr[anim_name]
	end

	return nil
end

----[[攻击
-- 进入 - 攻击状态
function ShuangShengTianShenObj:EnterStateAttack()
	local anim_name = SkillWGData.GetSkillActionStr(self.obj_type, self.attack_skill_id, self.attack_index)
	if anim_name ~= nil and anim_name ~= "" then
		self.action_time_record = self:GetActionTimeRecord(anim_name)
		if self.action_time_record and self.action_time_record.speed then
			local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
			main_part:SetFloat(anim_name.."_speed", self.action_time_record.speed)
		end
	end

    Character.EnterStateAttack(self, anim_name)
end

function ShuangShengTianShenObj:OnAnimatorBegin(param, state_info)
    Character.OnAnimatorBegin(self, param, state_info)
end

function ShuangShengTianShenObj:SetAttackIndex(index)
    self.attack_index = index
end

-- 攻击
function ShuangShengTianShenObj:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)
    self:CancelPlayActionBackFun()
    Character.DoAttack(self, skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)
end

-- 攻击 - 无目标
function ShuangShengTianShenObj:DoAttackForNoTarget(skill_id, awake_level, x, y, obj)
    self:CancelPlayActionBackFun()
    Character.DoAttackForNoTarget(self, skill_id, awake_level , x, y, obj)
end

-- 攻击动作结束处理
function ShuangShengTianShenObj:AttackActionEndHandle()
    if self.action_time_record ~= nil and self.action_time_record.has_back then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        local part_obj = part:GetObj()
        if part_obj == nil or IsNil(part_obj.gameObject) then
            return
        end

        self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnPlayActionBackEnd, self), self.action_time_record.back_time)
        local anim_name = self.anim_name.."_back"
		self:CrossAction(SceneObjPart.Main, anim_name)
    else
        self:ChangeToCommonState()
    end
end

function ShuangShengTianShenObj:OnPlayActionBackEnd()
    if self:IsDeleted() then
        self:CancelPlayActionBackFun()
    else
        self:ChangeToCommonState()
    end
end
--攻击 end]]