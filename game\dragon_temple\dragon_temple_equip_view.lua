function DragonTempleView:LoadIndexCallBackEquip()
	for i = 0, 2 do
		self.node_list["equip_type_item_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickEquipTypeSelect, self, i))
	end

	if self.suit_equip_list == nil then
        self.suit_equip_list = {}
        for i = 0, DragonTempleWGData.EquipCount - 1 do
            self.suit_equip_list[i] = DragonTempleEquipRender.New(self.node_list["equip_" ..i])
            self.suit_equip_list[i]:SetIndex(i)
			self.suit_equip_list[i]:SetClickCallBack(BindTool.Bind(self.OnSelectEquipCell, self))
        end
    end

	if not self.cur_select_equip_cell then
		self.cur_select_equip_cell = ItemCell.New(self.node_list["cur_select_equip_cell"])
	end

	if self.equip_level_attr_list == nil then
        self.equip_level_attr_list = {}
        local node_num = self.node_list["equip_level_attr_list"].transform.childCount
        for i = 1, node_num do
            self.equip_level_attr_list[i] = CommonAddAttrRender.New(self.node_list["equip_level_attr_list"]:FindObj("attr_" .. i))
        end
    end

	if not self.equip_level_up_cost_cell then
		self.equip_level_up_cost_cell = ItemCell.New(self.node_list["equip_level_up_cost_pos"])
	end

	if not self.equip_grade_up_cost_cell then
		self.equip_grade_up_cost_cell = ItemCell.New(self.node_list["equip_grade_up_cost_pos"])
	end

	if self.equip_name_list == nil then
        self.equip_name_list = {}
        for i = 1, 10 do
            self.equip_name_list[i] = self.node_list.equip_suit_name_group:FindObj("equip_name_" .. i)
        end
    end

	if not self.equip_suit_attr_list then
		self.equip_suit_attr_list = {}
		for i = 1, 5 do
			self.equip_suit_attr_list[i] = DragonEquipSuitAttrRender.New(self.node_list["equip_suit_attr_group"]:FindObj("suit_act_cell_" .. i))
		end
	end
	
	XUI.AddClickEventListener(self.node_list["equip_level_up_btn"], BindTool.Bind(self.OnClickEquipLevelUp, self))--升级/激活
	XUI.AddClickEventListener(self.node_list["equip_grade_up_btn"], BindTool.Bind(self.OnClickEquipGradeUp, self))  --进阶
	XUI.AddClickEventListener(self.node_list["equip_bag_btn"], BindTool.Bind(self.OnClickOpenBagView, self))
	XUI.AddClickEventListener(self.node_list["equip_skill_icon"], BindTool.Bind(self.OnEquipSkillClick, self))
	XUI.AddClickEventListener(self.node_list["equip_suit_act_btn"], BindTool.Bind(self.OnEquipSuitActClick, self))
end


function DragonTempleView:ShowIndexCallBackEquip()
	self:FlushEquipRoleModel()
	self:DokEequipViewAnim()
end

function DragonTempleView:ReleaseEquip()
    if self.equip_role_model then
		self.equip_role_model:DeleteMe()
		self.equip_role_model = nil
	end

	if self.suit_equip_list then
        for k, v in pairs(self.suit_equip_list) do
            v:DeleteMe()
        end
        self.suit_equip_list = nil
    end

	if self.cur_select_equip_cell then
		self.cur_select_equip_cell:DeleteMe()
		self.cur_select_equip_cell = nil
	end

	if self.equip_level_attr_list then
        for k, v in pairs(self.equip_level_attr_list) do
            v:DeleteMe()
        end
        self.equip_level_attr_list = nil
    end

	if self.equip_level_up_cost_cell then
		self.equip_level_up_cost_cell:DeleteMe()
		self.equip_level_up_cost_cell = nil
	end

	if self.equip_grade_up_cost_cell then
		self.equip_grade_up_cost_cell:DeleteMe()
		self.equip_grade_up_cost_cell = nil
	end

	if self.equip_suit_attr_list then
        for k, v in pairs(self.equip_suit_attr_list) do
            v:DeleteMe()
        end
        self.equip_suit_attr_list = nil
    end

	self.equip_name_list = nil
	self.select_equip_type = nil
	self.equip_select_solt = nil
end

function DragonTempleView:DokEequipViewAnim()
    local tween_info = UITween_CONSTS.DragonTemple
    RectTransform.SetAnchoredPositionXY(self.node_list.equip_right_root.rect, 600, 0)

    self.node_list.equip_right_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
end

function DragonTempleView:OnFlushEquip(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushEquipOtherInfo()
			self:FlushEquipInfo()
			self:FlushEquipUpLevelPanel()
			self:FlushEquipSuitPanel()

			if not self.select_equip_type then
				local selcet_index = 0
				for i = 0, 2 do
					local type_red = DragonTempleWGData.Instance:GetDragonTempleEquipTypeRemind(i)
					if type_red then
						selcet_index = i
						break
					end
				end

				if self.node_list["equip_type_item_" .. selcet_index].toggle.isOn then
					self:OnClickEquipTypeSelect(selcet_index)
				else
					self.node_list["equip_type_item_" .. selcet_index].toggle.isOn = true
				end
			end
		end
	end
end

function DragonTempleView:OnClickEquipTypeSelect(type)
	if self.select_equip_type == type then
		return
	end

	self.select_equip_type = type
	local cfg = DragonTempleWGData.Instance:GetTypeSoltEquipCfg(type)
	if cfg == nil then
		return 
	end

	local click_cell_index = cfg[1].solt
	for k, v in ipairs(cfg) do
		if DragonTempleWGData.Instance:GetDragonTempleSlotRemind(v.solt)then
			click_cell_index = v.solt
			break
		end
	end

	if self.suit_equip_list[click_cell_index] then
		self.suit_equip_list[click_cell_index]:OnClick()
	end
end

function DragonTempleView:FlushEquipRoleModel()
	if not self:IsLoaded() then
		return
	end

	if nil == self.equip_role_model then
		local node = self.node_list.equip_EventTriggerListener
		self.equip_role_model = RoleModel.New()
		local display_data = {
			parent_node = node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.equip_role_model:SetRenderTexUI3DModel(display_data)
		-- self.equip_role_model:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.equip_role_model)
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true}
		self.equip_role_model:SetModelResInfo(role_vo, special_status_table)
		self.equip_role_model:PlayRoleAction()
		self.equip_role_model:FixToOrthographic(self.root_node_transform)
	else
		self.equip_role_model:PlayLastAction()
	end
end

function DragonTempleView:FlushEquipOtherInfo()
	for i = 0, 2 do
		self.node_list["equip_type_name_nor_" .. i].text.text = Language.DragonTemple.EquipTypeName[i]
		self.node_list["equip_type_name_hl_" .. i].text.text = Language.DragonTemple.EquipTypeName[i]
		local type_red = DragonTempleWGData.Instance:GetDragonTempleEquipTypeRemind(i)
		self.node_list["equip_type_red_" .. i]:SetActive(type_red)
	end
end

function DragonTempleView:FlushEquipInfo()
	local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfo()
	for k,v in pairs(self.suit_equip_list) do
        v:SetData(solt_info[k])
    end

    local equip_cap = DragonTempleWGData.Instance:GetEquipCap()
    self.node_list.equip_cap.text.text = equip_cap
end

function DragonTempleView:OnSelectEquipCell(cell)
	if IsEmptyTable(cell:GetData()) then
		return
	end

	local data = cell:GetData()
	if self.equip_select_solt == cell:GetIndex() then
		if data and data.cost_item_id > 0 then
			TipWGCtrl.Instance:OpenItem({item_id = data.cost_item_id})
		end
		return
	end

	self.equip_select_solt = cell:GetIndex()
	for k, v in pairs(self.suit_equip_list) do
		v:SetSelectIndex(self.equip_select_solt)
	end

	self:FlushEquipUpLevelPanel()
	self:FlushEquipSuitPanel()
	local def_select_panel = 1
	if DragonTempleWGData.Instance:GetDragonTempleSlotLevelAndGradeRemind(self.equip_select_solt) then
		def_select_panel = 1
	elseif  DragonTempleWGData.Instance:GetDragonTempleSlotSuitRemind(self.equip_select_solt)  then
		def_select_panel = 2
	end
	
	self.node_list.equip_up_level_tog.toggle.isOn = def_select_panel == 1
	self.node_list.equip_suit_tog.toggle.isOn = def_select_panel == 2
end

function DragonTempleView:FlushEquipUpLevelPanel()
	if self.equip_select_solt == nil then
		return
	end

	local cell_data = self.suit_equip_list[self.equip_select_solt] and self.suit_equip_list[self.equip_select_solt]:GetData()
	if not cell_data then
		return
	end

	local remind = DragonTempleWGData.Instance:GetDragonTempleSlotLevelAndGradeRemind(self.equip_select_solt)
	self.node_list.equip_up_leve_toggle_remind:SetActive(remind)
	self.cur_select_equip_cell:SetData({item_id = cell_data.cost_item_id})
	self.node_list.cur_select_equip_level.text.text = "Lv." .. cell_data.level

	local attr_list = DragonTempleWGData.Instance:GetEquipAllAttrList(cell_data.solt, cell_data.level, cell_data.grade)

	local need_show_attr_up_effect = false
	if nil ~= self.dragon_equip_solt_cache and nil ~= self.dragon_equip_solt_level_cache and nil ~= self.dragon_equip_solt_grade_cache then
		if (self.dragon_equip_solt_cache == cell_data.solt) and ((cell_data.level - self.dragon_equip_solt_level_cache == 1) or (cell_data.grade - self.dragon_equip_solt_grade_cache == 1)) then
			need_show_attr_up_effect = true
		end
	end

    for k,v in pairs(self.equip_level_attr_list) do
        v:SetData(attr_list[k])

		if need_show_attr_up_effect then
			v:PlayAttrValueUpEffect()
		end
    end

	self.dragon_equip_solt_cache = cell_data.solt
	self.dragon_equip_solt_level_cache = cell_data.level
	self.dragon_equip_solt_grade_cache = cell_data.grade

	local is_can_upgrade = DragonTempleWGData.Instance:IsCanUpGrade(cell_data.solt)
	self.node_list.equip_level_up_group:SetActive(not is_can_upgrade)
	self.node_list.equip_grade_up_group:SetActive(is_can_upgrade)
	if is_can_upgrade then
		self:FlushEquipUpGradeInfo(cell_data)
	else
		self:FlushEquipUpLevelInfo(cell_data)
	end
end

function DragonTempleView:FlushEquipUpLevelInfo(cell_data)
	if not cell_data then
		return
	end

	local not_is_act = cell_data.level <= 0
	--分为激活和升级
    if not_is_act then
		self.node_list.equip_level_up_max:SetActive(false)
		self.equip_level_up_cost_cell:SetData({item_id = cell_data.cost_item_id})
		local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cell_data.cost_item_id)
		local color = cost_num >= cell_data.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		local up_level_str = cost_num .. "/" .. cell_data.cost_item_num
		self.node_list.equip_level_up_cost_num.text.text = ToColorStr(up_level_str, color)
		self.node_list.equip_level_up_remind:SetActive(cost_num >= cell_data.cost_item_num)
	else
		local cur_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(cell_data.solt, cell_data.level)
		local next_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(cell_data.solt, cell_data.level + 1)
		if next_level_cfg and cur_level_cfg then
			self.node_list.equip_level_up_max:SetActive(false)
			self.equip_level_up_cost_cell:SetData({item_id = cur_level_cfg.cost_item_id})
			local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			local color = cost_num >= cur_level_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
			local up_level_str = cost_num .. "/" .. cur_level_cfg.cost_item_num
			self.node_list.equip_level_up_cost_num.text.text = ToColorStr(up_level_str, color)
			self.node_list.equip_level_up_remind:SetActive(cost_num >= cur_level_cfg.cost_item_num)
		else
			self.node_list.equip_level_up_max:SetActive(true)
		end
    end
end

function DragonTempleView:FlushEquipUpGradeInfo(cell_data)
	if not cell_data then
		return
	end

	local cur_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(cell_data.solt, cell_data.grade)
	local next_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(cell_data.solt, cell_data.grade + 1)
	if next_grade_cfg and cur_grade_cfg then
		self.equip_grade_up_cost_cell:SetData({item_id = cur_grade_cfg.cost_item_id})
		local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
		local color = cost_num >= cur_grade_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		local up_grade_str = cost_num .. "/" .. cur_grade_cfg.cost_item_num
		self.node_list.equip_grade_up_cost_num.text.text = ToColorStr(up_grade_str, color)
		self.node_list.equip_grade_up_remind:SetActive(cost_num >= cur_grade_cfg.cost_item_num and cell_data.level >= cur_grade_cfg.need_level)
	end
end

function DragonTempleView:FlushEquipSuitPanel()
	if self.equip_select_solt == nil then
		return
	end

	local cell_data = self.suit_equip_list[self.equip_select_solt] and self.suit_equip_list[self.equip_select_solt]:GetData()
	if not cell_data then
		return
	end

	local remind = DragonTempleWGData.Instance:GetDragonTempleSlotSuitRemind(self.equip_select_solt)
	self.node_list.equip_suit_toggle_remind:SetActive(remind)

	self.node_list.equip_title_type_name.text.text = Language.DragonTemple.EquipTypeName[cell_data.type]
	local cfg = DragonTempleWGData.Instance:GetTypeSoltEquipCfg(cell_data.type)
	if cfg == nil then
		return 
	end

	for k, v in ipairs(self.equip_name_list) do
		if cfg[k] then
			v:SetActive(true)
			local solt_info = DragonTempleWGData.Instance:GetSoltEquipInfoBySolt(cfg[k].solt)
			local level = solt_info.level or 0
			local color = level ~= 0 and COLOR3B.D_GREEN or COLOR3B.WHITE
			local item_cfg = ItemWGData.Instance:GetItemConfig(cfg[k].cost_item_id)
    		local name_str = item_cfg and item_cfg.name or ""
    		v.text.text = ToColorStr(name_str, color)
		else
			v:SetActive(false)
		end
	end

	self:FlushEquipSkillInfo(cell_data)
	self:FlushEquipSuitAttr(cell_data)
end

function DragonTempleView:FlushEquipSkillInfo(cell_data)
	if not cell_data then
		return
	end
	
	local skill_cfg = DragonTempleWGData.Instance:GetEquipSkillByType(cell_data.type)
	self.node_list.equip_suit_skill_group:SetActive(not IsEmptyTable(skill_cfg))
	if skill_cfg then
		self.node_list.equip_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.skill_id))
		local act_num = DragonTempleWGData.Instance:GetEquipActNumBy(cell_data.type)
		self.node_list.equip_skill_lock:SetActive(act_num < skill_cfg.active_need_num)
		self.node_list.equip_skill_desc.text.text = skill_cfg.skill_desc
	end
end

function DragonTempleView:FlushEquipSuitAttr(cell_data)
	if not cell_data then
		return
	end

	local suit_info = DragonTempleWGData.Instance:GetEquipSuitCfgByType(cell_data.type)
	self.node_list.equip_suit_attr_group:SetActive(not IsEmptyTable(suit_info))
	if not IsEmptyTable(suit_info) then
		for k, v in ipairs(self.equip_suit_attr_list) do
			local data = suit_info[k]
			if data then
				v:SetActive(true)
				v:SetData(data)
			else
				v:SetActive(false)
			end
		end
	end

	local suit_red = DragonTempleWGData.Instance:GetDragonTempleSlotSuitRemind(cell_data.solt)
	XUI.SetButtonEnabled(self.node_list.equip_suit_act_btn, suit_red)
end

function DragonTempleView:OnClickEquipLevelUp()
	if self.equip_select_solt == nil then
		return
	end

	local cell_data = self.suit_equip_list[self.equip_select_solt] and self.suit_equip_list[self.equip_select_solt]:GetData()
	if not cell_data then
		return
	end

	local not_is_act = cell_data.level <= 0
	if not_is_act then
		local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cell_data.cost_item_id)
		if cost_num >= cell_data.cost_item_num then
			DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SOLT_ACTIVE, cell_data.solt)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.CostNotEnough)
		end
	else
		local cur_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(cell_data.solt, cell_data.level)
		local next_level_cfg = DragonTempleWGData.Instance:GetEquipLevelCfg(cell_data.solt, cell_data.level + 1)
		if next_level_cfg and cur_level_cfg then
			local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			if cost_num >= cur_level_cfg.cost_item_num then
				DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SOLT_LEVEL_UP, cell_data.solt)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.CostNotEnough)
			end
		end
    end
end

function DragonTempleView:OnClickEquipGradeUp()
	if self.equip_select_solt == nil then
		return
	end

	local cell_data = self.suit_equip_list[self.equip_select_solt] and self.suit_equip_list[self.equip_select_solt]:GetData()
	if not cell_data then
		return
	end

	local cur_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(cell_data.solt, cell_data.grade)
	local next_grade_cfg = DragonTempleWGData.Instance:GetEquipGradeCfg(cell_data.solt, cell_data.grade + 1)
	if next_grade_cfg and cur_grade_cfg then
		local cost_num = DragonTempleWGData.Instance:GetItemNumInBagById(cur_grade_cfg.cost_item_id)
		if cost_num >= cur_grade_cfg.cost_item_num and cell_data.level >= cur_grade_cfg.need_level then
			DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SOLT_GRADE_UP, cell_data.solt)
    	elseif cell_data.level < cur_grade_cfg.need_level then
    		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.LevelNotEnough)
    	else
    		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.CostNotEnough)
    	end
	end
end

function DragonTempleView:OnClickOpenBagView()
	DragonTempleWGCtrl.Instance:OpenEquipBagView()
end

function DragonTempleView:OnEquipSkillClick()
	if self.equip_select_solt == nil then
		return
	end

	local cell_data = self.suit_equip_list[self.equip_select_solt] and self.suit_equip_list[self.equip_select_solt]:GetData()
	if not cell_data then
		return
	end
	
	local skill_cfg = DragonTempleWGData.Instance:GetEquipSkillByType(cell_data.type)
	if skill_cfg then
		local show_data = {
			x = 0,
			y = 0, 
			set_pos2 = true,
			icon = skill_cfg.skill_id, 
			top_text = skill_cfg.skill_name, 
			hide_level = true,
			body_text = skill_cfg.skill_desc, 
		}
	    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
	end
end

function DragonTempleView:OnEquipSuitActClick()
	if self.equip_select_solt == nil then
		return
	end

	local cell_data = self.suit_equip_list[self.equip_select_solt] and self.suit_equip_list[self.equip_select_solt]:GetData()
	if not cell_data then
		return
	end
	local act_num = DragonTempleWGData.Instance:GetEquipActNumBy(cell_data.type)
	local cur_suit_level = DragonTempleWGData.Instance:GetSuitLevelByType(cell_data.type)
	local next_suit_cfg = DragonTempleWGData.Instance:GetEquipSuitLevelCfg(cell_data.type, cur_suit_level + 1)
	if next_suit_cfg and act_num >= next_suit_cfg.need_num then
		DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.SUIT_LEVEL_UP, cell_data.type)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTemple.NotCondition)
	end
end
---------DragonTempleEquipRender-----------
DragonTempleEquipRender = DragonTempleEquipRender or BaseClass(BaseRender)
function DragonTempleEquipRender:LoadCallBack()
	if self.node_list["item_node"] and not self.equip_cell then
		self.equip_cell = ItemCell.New(self.node_list["item_node"])
	end

	-- if not self.tween and self.node_list.tween_root then
	-- 	self.node_list["tween_root"].transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
	-- 	self.tween = self.node_list["tween_root"].transform:DOAnchorPosY(55, 2):SetLoops(-1, DG.Tweening.LoopType.Yoyo):SetEase(DG.Tweening.Ease.Linear)
	-- end

	XUI.AddClickEventListener(self.node_list.click_btn, BindTool.Bind(self.OnClick, self))
end

function DragonTempleEquipRender:__delete()
	if self.equip_cell then
        self.equip_cell:DeleteMe()
        self.equip_cell = nil
    end

	-- if self.tween then
	-- 	self.tween:Kill()
	-- 	self.tween = nil
	-- end
end

function DragonTempleEquipRender:OnFlush()
    if self.data == nil then
        return
    end

	if self.equip_cell then
		local item_data = {item_id = self.data.cost_item_id}
		self.equip_cell:SetData(item_data)

		local is_act = self.data.level ~= 0
		if not is_act then
			self.equip_cell:SetGraphicGreyCualityBg(true)
			self.equip_cell:SetDefaultEff(false)
		else
			self.equip_cell:SetGraphicGreyCualityBg(false)
		end
	end

    if self.node_list.name then
    	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.cost_item_id)
    	self.node_list.name.text.text = item_cfg and item_cfg.name or ""
    end

	if self.node_list.equip_icon then
		local bundle, asset = ResPath.GetDragonTempleImg("a2_lsd_equip_icon_" .. self.data.solt)
		self.node_list.equip_icon.image:LoadSprite(bundle, asset, function()
			self.node_list.equip_icon.image:SetNativeSize()
		end)
	end

    local red_flag = DragonTempleWGData.Instance:GetDragonTempleSlotRemind(self.data.solt)
    self.node_list.remind:SetActive(red_flag)
end

function DragonTempleEquipRender:OnSelectChange(is_select)
	if self.node_list.select_bg then
		self.node_list.select_bg:SetActive(is_select)
	end
end

function DragonTempleEquipRender:SetClickCallBack(callback)
    self.click_callback = callback
end

function DragonTempleEquipRender:OnClick()
    if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

-------------------------------DragonEquipSuitAttrRender------------------------------
DragonEquipSuitAttrRender = DragonEquipSuitAttrRender or BaseClass(BaseRender)
function DragonEquipSuitAttrRender:__init()
    self.attr_list = {}
	self.attr_value_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
		local obj_node = self.node_list.attr_list:FindObj("attr_name_" .. i)
        self.attr_list[i] = obj_node
		self.attr_value_list[i] = obj_node:FindObj("attr_value")
    end
end

function DragonEquipSuitAttrRender:__delete()
	self.attr_list = nil
	self.attr_value_list = nil
end

function DragonEquipSuitAttrRender:OnFlush()
	if self.data == nil then
		return
	end

	local cur_suit_level = DragonTempleWGData.Instance:GetSuitLevelByType(self.data.solt_type)
	local is_act = cur_suit_level >= self.data.level
	if self.node_list.suit_icon then
        XUI.SetGraphicGrey(self.node_list.suit_icon, not is_act)
    end

	local attr_color = is_act and COLOR3B.D_GREEN or COLOR3B.WHITE
    local need_str = string.format(Language.DragonTemple.SuitNumCompany, self.data.need_num)
	self.node_list.need_num.text.text = need_str

	local list = DragonTempleWGData.Instance:GetEquipSuitAttrList(self.data.solt_type, self.data.level)
    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_str)
            local name = EquipmentWGData.Instance:GetAttrName(list[k].attr_str, false)
            local value = is_per and list[k].attr_value or list[k].attr_value / 100 .. "%"

            v.text.text = ToColorStr(name, attr_color)
			self.attr_value_list[k].text.text = ToColorStr(value, attr_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end