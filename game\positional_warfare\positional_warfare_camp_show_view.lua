PositionalWarfareCampShowView = PositionalWarfareCampShowView or BaseClass(SafeBaseView)

function PositionalWarfareCampShowView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(812, 560)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_camp_show_view")
end

function PositionalWarfareCampShowView:OpenCallBack()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.CAMP_INFO)
    GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.ALL_GUILD_BASE_INFO, RoleWGData.Instance.role_vo.guild_id)
end

function PositionalWarfareCampShowView:LoadCallBack()
    if not self.camp_list then
        self.camp_list = AsyncBaseGrid.New()
        self.camp_list:CreateCells(
        {   col = 2,
            change_cells_num = 1,
            list_view = self.node_list.camp_list,
			assetBundle = "uis/view/positional_warfare_ui_prefab",
            assetName = "pw_camp_show_list_cell",
            itemRender = PWCampShowListCellRender}
        )
        self.camp_list:SetStartZeroIndex(false)
    end

    self.node_list.title_view_name.text.text = Language.PositionalWarfare.CampShowViewTitleName
end

function PositionalWarfareCampShowView:ReleaseCallBack()
    if self.camp_list then
        self.camp_list:DeleteMe()
        self.camp_list = nil
    end
end

function PositionalWarfareCampShowView:OnFlush()
    local camp_data_list = PositionalWarfareWGData.Instance:GetCampInfoDataList()

    if IsEmptyTable(camp_data_list) then
        self.node_list.camp_list:CustomSetActive(false)
        self.node_list.no_camp_data:CustomSetActive(true)
    else
        local camp_list = {}
        for k, v in pairs(camp_data_list) do
            camp_list[v.camp] = camp_list[v.camp] or {camp = v.camp}
            camp_list[v.camp].camp_list = camp_list[v.camp].camp_list or {}
            table.insert(camp_list[v.camp].camp_list, v)
        end

        local target_data_list = {}
        for k, v in pairs(camp_list) do
            table.insert(target_data_list, v)
        end

        self.camp_list:SetDataList(target_data_list)
        self.node_list.camp_list:CustomSetActive(true)
        self.node_list.no_camp_data:CustomSetActive(false)
    end
end

-------------------------PWCampShowListCellRender----------------------
PWCampShowListCellRender = PWCampShowListCellRender or BaseClass(BaseRender)

function PWCampShowListCellRender:LoadCallBack()
    if not self.camp_list then
        self.camp_list = AsyncBaseGrid.New()
        self.camp_list:CreateCells(
        {   col = 2,
            change_cells_num = 1,
            list_view = self.node_list.camp_list,
			assetBundle = "uis/view/positional_warfare_ui_prefab",
            assetName = "pw_camp_server_show_cell",
            itemRender = PWCampServerShowItemCellRender}
        )
        self.camp_list:SetStartZeroIndex(false)
    end
end

function PWCampShowListCellRender:__delete()
    if self.camp_list then
        self.camp_list:DeleteMe()
        self.camp_list = nil
    end
end

function PWCampShowListCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local group_index = PositionalWarfareWGData.Instance:GetMyGroupIndex()
    local camp_cfg = PositionalWarfareWGData.Instance:GetCampCfg(group_index, self.data.camp)

    if not IsEmptyTable(camp_cfg) then
        local sign_bundle, sign_asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. camp_cfg.camp_sign)
        self.node_list.camp_icon.image:LoadSprite(sign_bundle, sign_asset, function()
            self.node_list.camp_icon.image:SetNativeSize()
        end)

        self.node_list.camp_name.text.text = camp_cfg.camp_name or ""
    end

    self.camp_list:SetDataList(self.data.camp_list)

    local my_camp = PositionalWarfareWGData.Instance:GetMyCamp()
    self.node_list.flag_my_camp:CustomSetActive(my_camp == self.data.camp)
end

-------------------------------------------PWCampServerShowItemCellRender--------------------------------------
PWCampServerShowItemCellRender = PWCampServerShowItemCellRender or BaseClass(BaseRender)

function PWCampServerShowItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local group_index = PositionalWarfareWGData.Instance:GetMyGroupIndex()

    if group_index > 0 then
        local server_id = RoleWGData.Instance:GetCurServerId()
        local name_str = string.format(Language.PositionalWarfare.ServerName, self.index, self.data.param.temp_low)
        self.node_list.name.text.text = server_id == self.data.param.temp_low and ToColorStr(name_str, COLOR3B.GREEN) or name_str
    else
        local guild_data_list = GuildWGData.Instance:GetGuildListData()
        local guild_data = {}

        for k, v in pairs(guild_data_list) do
            if v.guild_id == self.data.guild_id then
                guild_data = v
                break
            end
        end

        if not IsEmptyTable(guild_data) then
            local guild_id = RoleWGData.Instance:GetAttr("guild_id")
            local name_str = string.format("%s.%s", self.index, guild_data.guild_name)
            self.node_list.name.text.text = (guild_id == guild_data.guild_id) and ToColorStr(name_str, COLOR3B.GREEN) or name_str
        end
    end
end