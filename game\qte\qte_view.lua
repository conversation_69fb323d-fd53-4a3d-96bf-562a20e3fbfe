local auto_time = 5

QTEView = QTEView or BaseClass(SafeBaseView)

function QTEView:__init()
	self.view_style = ViewStyle.Window
	self:SetMaskBg(false,false)
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/qte_prefab", "layout_qte_view")

end

function QTEView:__delete()

end
function QTEView:SetQTEType(type)
    self.qte_type = type
end

function QTEView:CloseCallBack()
    GlobalTimerQuest:CancelQuest(self.timer)
    UnityEngine.Time.timeScale = 1
end

function QTEView:ReleaseCallBack()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
end

function QTEView:LoadCallBack()
	self.node_list.btn_finish.button:AddClickListener(BindTool.Bind(self.OnClinkHandler, self))
end

function QTEView:ShowIndexCallBack()
    -- 秘笈触发且设置了自动释放秘笈 缩短自动时间
    if self.qte_type == MONSTER_QTE_TYPE.MONSTER_QTE_TYPE_ESOTERICA and GuajiWGCtrl.Instance:IsAutoEsotericaSkill() then
        auto_time = 1.6
    else
        auto_time = 5
    end
    self.timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnClinkHandler, self), auto_time)
end

function QTEView:OnFlush()

end



function QTEView:OnClinkHandler()
    GlobalTimerQuest:CancelQuest(self.timer)
    
    local cur_select_target = GuajiCache.target_obj
    if cur_select_target then
        local objid = cur_select_target:GetObjId()
        if objid and objid >= 0 then
            RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_DO_MONSTER_QTE, objid)
        end
    end

    UnityEngine.Time.timeScale = 1
	self:Close()
end

