ChuShenSecondRankPanel = ChuShenSecondRankPanel or BaseClass(SafeBaseView)

function ChuShenSecondRankPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(54, 17), sizeDelta = Vector2(756, 480)})
	self:AddViewResource(0, "uis/view/operation_tiancai_chushen_prefab", "layout_tiancai_chushen_rank_view")
	
end

function ChuShenSecondRankPanel:LoadIndexCallBack()
	self.node_list.title_view_name.text.text = Language.Activity.ChuShenRankRecoder
 	 if not self.chushen_rank_list then
		self.chushen_rank_list = AsyncListView.New(ChuShenSecondPanelRankRender, self.node_list.chushen_rank_list)
	end
 end 

function ChuShenSecondRankPanel:ReleaseCallBack()
	if self.chushen_rank_list then
		self.chushen_rank_list:DeleteMe()
		self.chushen_rank_list = nil
	end
 end

 function ChuShenSecondRankPanel:OnFlush()
 	local data_list = ChuShenWGData.Instance:GetChuShenRankData()
 	self.chushen_rank_list:SetDataList(data_list, 0)
 	self.node_list["non_bg"]:SetActive(#data_list <= 0)
 end



ChuShenSecondPanelRankRender = ChuShenSecondPanelRankRender or BaseClass(BaseRender)

function ChuShenSecondPanelRankRender:LoadCallBack()
	self.role_avatar = RoleHeadCell.New(false)
 	XUI.AddClickEventListener(self.node_list["head_btn"], BindTool.Bind(self.HeadClick, self))
 	XUI.AddClickEventListener(self.node_list["item_image"], BindTool.Bind(self.FoodTips, self))
 	self.head_cell = BaseHeadCell.New(self.node_list.head_pos)
 	
end

function ChuShenSecondPanelRankRender:__delete()
	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

end

function ChuShenSecondPanelRankRender:OnFlush()
	if nil == self.data then
		return
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local role_info = {
		role_id = self.data.uid,
		role_name = self.data.role_name,
		prof = self.data.prof,
		sex = self.data.sex,
		is_online = self.data.is_online,
		team_index = self.data.team_index,
		plat_name = vo.plat_name,
	}
	self.role_avatar:SetRoleInfo(role_info)
	local data = {role_id = self.data.uid, prof = self.data.prof, sex = self.data.sex, fashion_photoframe = 0}
	self.head_cell:SetData(data)

	local info = ChuShenWGData.Instance:GetOneMenuCfg(self.data.menu_id)
	local b, a = ResPath.GetF2CommonImages("yuan_menu_quality_"..info.menu_quality)
	self.node_list["item_bg"].image:LoadSprite(b, a, function ()
		XUI.ImageSetNativeSize(self.node_list["item_bg"])
	end)

	local parti_b, parti_a = ChuShenWGData.Instance:GetPartiAsesst(info.menu_quality)
	self.node_list["parti_pos"]:ChangeAsset(parti_b, parti_a)
	self.node_list["parti_pos"]:SetActive(info.menu_quality > SHOW_EFFECT_LEVEL)

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(info.id)
	local b1, a1 = ResPath.GetItem(item_cfg.icon_id)
	self.node_list["item_image"].image:LoadSprite(b1, a1, function ()
		XUI.ImageSetNativeSize(self.node_list["item_image"])
	end)
	local color = ITEM_COLOR[info.menu_quality]
	local menu_name = ToColorStr(info.menu_name, color)
	self.node_list.name.text.text = string.format(Language.Activity.ChuShenRankDes, self.data.role_name, menu_name)
	local time_table = os.date("*t", self.data.unlock_timestamp)
	self.node_list.food_name.text.text = string.format(Language.Activity.ChuShenYYMMDD, time_table.year, time_table.month, time_table.day, time_table.hour, time_table.min, time_table.sec) 
	--self.node_list.creat_time.text.text = string.format(Language.Activity.ChuShenHHMMSS, time_table.hour, time_table.min, time_table.sec) 
end

function ChuShenSecondPanelRankRender:HeadClick()
	self.role_avatar:OpenMenu()
end

function ChuShenSecondPanelRankRender:FoodTips()
	if self.data and self.data.menu_id and self.data.menu_id > 0 then
		local data = {}
		local menu_cfg = ChuShenWGData.Instance:GetOneMenuCfg(self.data.menu_id)
		data.item_id = menu_cfg.dishes_id
		TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_NORMAL, nil, nil, nil)
	end
end