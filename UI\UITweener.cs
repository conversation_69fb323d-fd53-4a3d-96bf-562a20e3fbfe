﻿using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

[HelpURL(@"https://www.baidu.com/s?wd=Hello World")]
[DisallowMultipleComponent]
public class UITweener : MonoBehaviour {
    private RectTransform rect;
    private bool play;
    private List<Tweener> tweenerList = new List<Tweener>();

    [SerializeField]
    private UITweenerItem[] itemList;

    void Awake()
    {
        if (rect == null)
        {
            rect = transform as RectTransform;
        }
    }

    void OnDestroy()
    {
        if (tweenerList.Count > 0)
        {
            Tweener tweener;
            for (int i = 0; i < tweenerList.Count; i++)
            {
                tweener = tweenerList[i];
                tweener.Kill();
            }

            tweenerList.Clear();
        }
    }

    void OnEnable()
    {
        play = true;
    }

    void LateUpdate()
    {
        if (play)
        {
            play = false;
            this.Play();
        }
    }

    public void Play()
    {
        if (itemList == null)
        {
            return;
        }

        for (int i = 0; i < itemList.Length; i++)
        {
            StartTween(itemList[i]);
        }
    }

    void StartTween(UITweenerItem item)
    {
        RectTransform target = item.TargetRect;

        Vector2 startValue = new Vector2();
        Vector2 endValue = target.anchoredPosition;
        uint distance = item.Distance;

        MoveTypeEnum moveType = item.MoveType;
        if (moveType == MoveTypeEnum.LeftToRight)
        {
            startValue = new Vector2(endValue.x - distance, endValue.y);
        }
        else if (moveType == MoveTypeEnum.RightToLeft)
        {
            startValue = new Vector2(endValue.x + distance, endValue.y);
        }
        else if (moveType == MoveTypeEnum.TopToDown)
        {
            startValue = new Vector2(endValue.x, endValue.y + distance);
        }
        else if (moveType == MoveTypeEnum.DownToTop)
        {
            startValue = new Vector2(endValue.x, endValue.y - distance);
        }

        target.anchoredPosition = startValue;

        var moveTime = item.MoveTime;

        bool isAlpha = item.IsAlpha;
        if (isAlpha)
        {
            CanvasGroup canvasGroup = target.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = target.gameObject.AddComponent<CanvasGroup>();
            }

            Ease alphaEase = item.AlphaEase;
            canvasGroup.alpha = 0;
            Tweener alphaTweener = canvasGroup.DOFade(1, moveTime);
            alphaTweener.SetEase(alphaEase);
            alphaTweener.OnComplete(() => 
            {
                alphaTweener.Kill();
                tweenerList.Remove(alphaTweener);
            });

            tweenerList.Add(alphaTweener);
        }

        Ease ease = item.Ease;
        Tweener moveTweener = target.DOAnchorPos(endValue, moveTime);
        moveTweener.SetEase(ease);
        moveTweener.OnComplete(() =>
        {
            moveTweener.Kill();
            tweenerList.Remove(moveTweener);
        });

        tweenerList.Add(moveTweener);
    }
}
