------------------------------------------------------
--vip续费提示
------------------------------------------------------
VipTipXuFeiView = VipTipXuFeiView or BaseClass(SafeBaseView)

function VipTipXuFeiView:__init(view_name)
	self.view_layer = UiLayer.PopTop
 	self:SetMaskBg(true, true)
 	self:AddViewResource(0, "uis/view/vip_ui_prefab", "layout_vip_xufei")
 
end

function VipTipXuFeiView:__delete()
	
end

function VipTipXuFeiView:ReleaseCallBack()

end

function VipTipXuFeiView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.vip_cz_btn, BindTool.Bind1(self.<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	XUI.AddClickEventListener(self.node_list.vip_xf_btn, BindTool.Bind1(self.ClickHandlerVIP, self))
end

function VipTipXuFeiView:OnFlush(param)
	for k,v in pairs(param) do
		if not v.is_xufei then
			self.node_list["vip_cz_btn"]:SetActive(true)
			self.node_list["vip_xf_btn"]:SetActive(false)
			self.node_list.vip_text.text.text = Language.Vip.RechargeNoGold
		else
			self.node_list["vip_cz_btn"]:SetActive(false)
			self.node_list["vip_xf_btn"]:SetActive(true)
			self.node_list.vip_text.text.text = Language.Vip.VipEnd
		end
	end
end

function VipTipXuFeiView:ClickHandler()
	ViewManager.Instance:CloseAll()
	RechargeWGCtrl.Instance:Open(TabIndex.recharge_cz)
end

function VipTipXuFeiView:ClickHandlerVIP()
	self:Close()
	-- VipWGCtrl.Instance:OpenVipRenewView()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_vip)
end

function VipTipXuFeiView:CloseCallBack()
	TaskGuide.Instance:SpecialConditions(true, 5)
end