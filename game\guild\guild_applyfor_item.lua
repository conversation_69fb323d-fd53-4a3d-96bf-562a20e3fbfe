GuildApplyforItem = GuildApplyforItem or BaseClass(BaseRender)

function GuildApplyforItem:__init()
	self.node_list.btn_pass.button:AddClickListener(BindTool.Bind1(self.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	self.node_list.btn_refuse.button:AddClickListener(BindTool.Bind1(self.OnRefuseH<PERSON>ler, self))
end

function GuildApplyforItem:CreateChild()
	BaseRender.CreateChild(self)
end

-- 注册事件
function GuildApplyforItem:RegisterEvent()
end

-- 通过事件
function GuildApplyforItem:OnPassHandler()
	-- self.node_list.item:SetActive(false)
	GuildWGCtrl.Instance:SendGuildApplyforJoinReq(GuildDataConst.GUILDVO.guild_id, 0, 1, {self.data.uid})
end

-- 拒绝事件
function GuildApplyforItem:OnRefuseHandler()
	-- self.node_list.item:SetActive(false)
	GuildWGCtrl.Instance:SendGuildApplyforJoinReq(GuildDataConst.GUILDVO.guild_id, 1, 1, {self.data.uid})
end

function GuildApplyforItem:OnFlush()
	if nil == self.data then
		return
	end
	--self.node_list.hight:SetActive(self.index == GuildWGData.Instance:GetGuildMemberSelectIndex()) --选中高亮
	-- local num = self.index % 2
	-- if(num == 0) then
	-- 	self.node_list["item_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	-- else
	-- 	self.node_list["item_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_5"))
	-- end
	
	self.node_list.lbl_name.text.text  = self.data.role_name
	-- local level_str = RoleWGData.GetLevelString(self.data.level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	if is_vis then
		self.node_list.img_feixian:SetActive(true)
		self.node_list.lbl_level2:SetActive(false)
		self.node_list.lbl_level.text.text = role_level .. Language.Tip.Ji
	else
		self.node_list.img_feixian:SetActive(false)
		self.node_list.lbl_level2:SetActive(true)
		self.node_list.lbl_level2.text.text = role_level .. Language.Tip.Ji
	end
	self.node_list.lbl_zhanli.text.text = CommonDataManager.ConverExpByThousand(self.data.capability)
end
