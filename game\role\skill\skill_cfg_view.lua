SkillCfgView = SkillCfgView or BaseClass(SafeBaseView)

function SkillCfgView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_skill_cfg")
end

function SkillCfgView:__delete()

end

function SkillCfgView:ReleaseCallBack()
	self.select_skill = nil
	if self.skill_list_top then
		for k,v in pairs(self.skill_list_top) do
			v:DeleteMe()
		end
	end
	self.skill_list_top = {}	
	if self.flush_skill_info then
		GlobalEventSystem:UnBind(self.flush_skill_info)
		self.flush_skill_info = nil
	end

	if self.skill_list_bottom then
		for k,v in pairs(self.skill_list_bottom) do
			XUI.SetGraphicGrey(v.skill_icon.image, false)
		end
	end

	if self.obj then
		ResMgr:Destroy(self.obj.gameObject)
		self.obj = nil
	end
	self.skill_list_bottom = nil
	self.item_vo = nil
	self.is_can = false
	self.index = 0
	self.select_skill_index = nil
end

function SkillCfgView:SetSkillType(skill_type)
	self.skill_type = skill_type
end

function SkillCfgView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Skill.CfgTitle
	self:SetSecondView(nil, self.node_list["size"])
	self.flush_skill_info = GlobalEventSystem:Bind(SkillEventType.FLUSH_SKILL_LIST,BindTool.Bind1(self.FlushSelectBottom, self))

	for i = 1, 4 do
		self.node_list["btn_check_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickAutoPlay, self, i))
	end

	-- 上面技能图标
	self.skill_list_top = {}
	-- 底部技能图标
	self.skill_list_bottom = {}
	self.item_vo = {}
	self.select_skill = nil
	self.index = 0
	self.is_can = false
	self.select_skill_index = nil
	self.canvas = self.root_node:GetComponent(typeof(UnityEngine.Canvas))
end

function SkillCfgView:ShowIndexCallBack()
	local skill_data = RoleWGData.Instance:GetAllSkillList()

	for i=1,6 do
		if not self.skill_list_top[i] then
			self.skill_list_top[i] = SkillCfgItem.New()
			self.skill_list_top[i]:LoadAsset("uis/view/role_ui_prefab", 'ph_skill_itemrender', self.node_list.skill_list_1.transform)
			self.skill_list_top[i]:SetIndex(i)
			self.skill_list_top[i].parent = self
			self.skill_list_top[i]:SetClickCallBack(BindTool.Bind(self.ClickHandler,self, self.skill_list_top[i]))
			self.skill_list_top[i]:SetEndDragCallBack(BindTool.Bind(self.EndDragHandler,self, self.skill_list_top[i]))
			self.skill_list_top[i]:SetBeginDragCallBack(BindTool.Bind(self.BeginDragHandler,self, self.skill_list_top[i]))
		end
		local skill_info
		if skill_data[i] then
			skill_info = SkillWGData.Instance:GetSkillInfoById(skill_data[i].skill_id)
		end
		self.skill_list_top[i].is_active = skill_info ~= nil
		self.skill_list_top[i]:SetData(skill_data[i] or {})
		self.skill_list_top[i]:SetCanvas(self.canvas)
		if i < 5 then
			if not self.skill_list_bottom[i] then
				self.skill_list_bottom[i] = {}
				self.skill_list_bottom[i].skill_icon = self.node_list['skill_icon_' .. i]
				self.skill_list_bottom[i].highlight = self.node_list['highlight_' .. i]
				self.skill_list_bottom[i].highlight.image.enabled = false
				self.skill_list_bottom[i].auto_play = self.node_list["img_yes_" .. i]
				self.skill_list_bottom[i].check_info = self.node_list["check_info_" .. i]
				self.skill_list_bottom[i].name_bg = self.node_list["text_bg_" .. i]
				self.skill_list_bottom[i].name = self.node_list["lbl_skill_name_" .. i]
				self.skill_list_bottom[i].awake_img = self.node_list["awake_img_" .. i]
				--XUI.AddClickEventListener(self.node_list['skill_order_' .. i],BindTool.Bind(self.FlushOrder,self,i))
				self.node_list['skill_order_' .. i].event_trigger_listener:AddBeginDragListener(BindTool.Bind(self.OnBeginDrag, self, i))
				self.node_list['skill_order_' .. i].event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrag, self, i))
				self.node_list['skill_order_' .. i].event_trigger_listener:AddEndDragListener(BindTool.Bind(self.OnEndDrag, self))    
				self.node_list['skill_order_' .. i].event_trigger_listener:AddPointerEnterListener(BindTool.Bind(self.OnPointEnter, self, i))
				self.node_list['skill_order_' .. i].event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.OnPointExit, self, i))
			end
		end
	end
	if self.select_skill then
		for i,v in ipairs(self.skill_list_top) do
			if v.node_list then
				v:SetHighLight(false)
			end
		end
		self.select_skill = nil
	end
	self:Flush()
end

function SkillCfgView:FlushSelectBottom()
	self:Flush()
end

function SkillCfgView:OnFlush()
	local skill_order,skill_flag  = RoleWGData.Instance:GetSkillCustomInfo()
    if not next(skill_order) then return end
    for i,v in ipairs(self.skill_list_bottom) do
    	v.skill_id = tonumber(skill_order[i])
		local skill_cfg = RoleWGData.Instance:GetSkillCfgById(v.skill_id)
    	v.highlight.image.enabled = self.select_skill_index and self.select_skill_index == v.skill_id --self.select_skill and self.select_skill.data.skill_id == v.skill_id
		local is_active = self.skill_list_top[i].is_active
    	v.skill_icon.image.enabled = true
		v.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(v.skill_id)))
    	v.skill_icon:SetActive(true)
    	--v.lock_icon.image.enabled = not is_active
		v.check_info:SetActive(is_active)
		v.name.text.text = skill_cfg.skill_name
		v.name_bg.image.enabled = is_active
		local is_awake = SkillWGData.Instance:GetCurSkillIsAwake(v.skill_id)
		v.awake_img:SetActive(is_awake)
		if is_active then
			local has_skill_flag = false
			local is_back_skill = SkillWGData.Instance:IsBackSkill(v.skill_id)
			XUI.SetButtonEnabled(self.node_list["btn_check_" .. i], not is_back_skill)
			if not is_back_skill then
				for m, n in pairs(skill_flag) do
					if tonumber(n) == v.skill_id then
						has_skill_flag = true
						break
					end
				end
			end
			v.auto_play.image.enabled = has_skill_flag
		end
    	XUI.SetGraphicGrey(v.skill_icon.image, not self.skill_list_top[i].is_active)
    end
	--self.select_skill = nil
end

function SkillCfgView:FlushOrder(index)
	self:FlushSkillOrder(index)
	--[[if self.select_skill == nil then
		self:FlushUpDownHigh(index)
	else
		self:FlushSkillOrder(index)
	end--]]
end

function SkillCfgView:OnBeginDrag(i)
	local skill_order, skill_flag  = RoleWGData.Instance:GetSkillCustomInfo()
	local skill_id = tonumber(skill_order[i])
	local is_active = false
	for i,v in ipairs(self.skill_list_top) do
		if skill_id == v.data.skill_id then
			is_active = v.is_active
		end
	end

	if not is_active then return end
	self.select_skill_index = skill_id
	if not self.obj and self.select_skill_index then
		self:InstantSkillItem()
	end

	for i,v in ipairs(self.skill_list_top) do
		v:SetHighLight(v.data.skill_id == skill_id)
	end

    for i,v in ipairs(self.skill_list_bottom) do
    	v.highlight.image.enabled = v.skill_id == skill_id
    end
end

function SkillCfgView:OnEndDrag(event_data)
	if self.obj then
		ResMgr:Destroy(self.obj.gameObject)
		self.obj = nil
	end

	local is_active = false
	local skill_order, skill_flag  = RoleWGData.Instance:GetSkillCustomInfo()
	local skill_id = tonumber(skill_order[self.index])

	for i,v in ipairs(self.skill_list_top) do
		if skill_id == v.data.skill_id then
			is_active = v.is_active
		end
	end

	if not is_active then return end
	if self.is_can then
		self:FlushOrder(self.index)
	end
end

function SkillCfgView:OnDrag(i)
	if self.select_skill_index then
		local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.skill_drag.transform, UnityEngine.Input.mousePosition, self.canvas.worldCamera, Vector2(0, 0))
		if self.item_vo and local_pos_tbl then
			self.item_vo.view.transform.localPosition = Vector3(local_pos_tbl.x, local_pos_tbl.y, 0)
		end
	end
end

function SkillCfgView:InstantSkillItem()
    self.obj = ResMgr:Instantiate(self.node_list["ph_skill_itemrender"].gameObject)--
    self.obj:SetActive(true)
    local item_render = SkillItemRender.New(self.obj)
   	local obj_transform = self.obj.transform
    obj_transform:SetParent(self.node_list.skill_drag.transform, false)
    item_render:SetData(self.select_skill_index)
    item_render.parent_view = self
    self.item_vo = item_render
end

function SkillCfgView:OnPointEnter(i)
	self.is_can = true
	self.index = i
end

function SkillCfgView:OnPointExit(i)
	self.is_can = false
	self.index = 0
end

function SkillCfgView:FlushUpDownHigh(index)
	local skill_order, skill_flag  = RoleWGData.Instance:GetSkillCustomInfo()
	local skill_id = tonumber(skill_order[index])
	for i, v in pairs(self.skill_list_top) do
		v:ChangeHighBySkillId(skill_id)
	end
	for i, v in pairs(self.skill_list_bottom) do
		v.highlight.image.enabled = i == index
	end
end

function SkillCfgView:FlushSkillOrder(index)
	if self.skill_list_bottom[index].skill_id == self.select_skill_index then return end
	local count = 0
	for i,v in ipairs(self.skill_list_top) do
		if v.is_active then
			count = count + 1
		end
	end
	if index > count then return end
	local skill_order,skill_flag = RoleWGData.Instance:GetSkillCustomInfo()
	local select_index
	for i=1,#skill_order do
		if  tonumber(skill_order[i]) == self.select_skill_index then
			select_index = i
			break
		end
	end
	if select_index then
		skill_order[select_index],skill_order[index] = skill_order[index], skill_order[select_index]
	else
		skill_order[index] = self.select_skill_index
	end
		--if self.skill_type == ZhuDongSkillType.JueSeSkill then
	RoleWGData.Instance:SetCustomInfo(skill_order,skill_flag)
		--else
		--	RoleWGData.Instance:SetCustomInfo(nil,nil,skill_order,skill_flag)
		--end
		--self.select_skill = nil
end

function SkillCfgView:BeginDragHandler(item)
	if not item.is_active then return end
	self.select_skill = item
	self.select_skill_index = self.select_skill.data.skill_id
	for i,v in ipairs(self.skill_list_top) do
		v:SetHighLight(v == item)
	end

    for i,v in ipairs(self.skill_list_bottom) do
    	v.highlight.image.enabled = self.select_skill and self.select_skill.data.skill_id == v.skill_id
    end
end
function SkillCfgView:EndDragHandler(item)
	if not item.is_active then return end

	if self.is_can then
		self:FlushOrder(self.index)
	end
end

function SkillCfgView:ClickHandler(item)
	--[[if not item.is_active then return end
	self.select_skill = item
	self.select_skill_index = self.select_skill.data.skill_id
	for i,v in ipairs(self.skill_list_top) do
		v:SetHighLight(v == item)
	end

    for i,v in ipairs(self.skill_list_bottom) do
    	v.highlight.image.enabled = self.select_skill and self.select_skill.data.skill_id == v.skill_id
    end--]]
end

function SkillCfgView:OnClickAutoPlay(index)
	local skill_order, skill_flag  = RoleWGData.Instance:GetSkillCustomInfo()
	--if next(skill_flag) then
		local skill_id = tonumber(skill_order[index])
		local has_skill_index
		for i, v in pairs(skill_flag) do
			if tonumber(v) == skill_id then
				has_skill_index = i
		--		local status = skill_flag[i] == "1"
		--		skill_flag[i] = status and "0" or "1"
				break
			end
		end
		if has_skill_index ~= nil then
			table.remove(skill_flag, has_skill_index)
			self.node_list["img_yes_" .. index].image.enabled = false
		else
			table.insert(skill_flag, skill_id)
			self.node_list["img_yes_" .. index].image.enabled = true
		end
	--end
	RoleWGData.Instance:SetCustomInfo(skill_order,skill_flag)
end

---------------------------SkillItemRender--------------------------------------

SkillItemRender = SkillItemRender or BaseClass(BaseRender)

function SkillItemRender:LoadCallBack()
	
	self.img_skill_icon_normal = self.node_list["img_skill_icon_normal"]
	self.skill_icon = self.node_list["ph_skill_icon"]
end

function SkillItemRender:OnFlush()
	self.skill_icon:SetActive(true)
	--图标

	if self.data then
		local bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data))
		self.node_list["ph_skill_icon"].image:LoadSprite(bundle, asset, function()
			self.node_list["ph_skill_icon"].image:SetNativeSize()
		end)
	end
end



---------------------------------------------------------------------------------
SkillCfgItem = SkillCfgItem or BaseClass(BaseRender)

function SkillCfgItem:LoadCallBack()
	self.item_vo = {}
	self.node_list.root_render.event_trigger_listener:AddBeginDragListener(BindTool.Bind1(self.OnBeginDrag, self))
	self.node_list.root_render.event_trigger_listener:AddDragListener(BindTool.Bind1(self.OnDrag, self))
	self.node_list.root_render.event_trigger_listener:AddEndDragListener(BindTool.Bind1(self.OnEndDrag, self))
	XUI.AddClickEventListener(self.node_list.ph_skill_icon,BindTool.Bind(self.ClickCallBack,self))
	self:SetHighLight(false)
	self.img_skill_icon_normal = self.node_list["img_skill_icon_normal"]
	self.skill_icon = self.node_list["ph_skill_icon"]
	self.skill_lock = self.node_list["ph_skill_lock"]
	self.lbl_skill_name = self.node_list["lbl_skill_name"]
end

function SkillCfgItem:OnFlush()
	self.skill_icon:SetActive(true)
	self.lbl_skill_name:SetActive(true)
	self.img_skill_icon_normal:SetActive(true)
	--图标
	local is_awake = SkillWGData.Instance:GetCurSkillIsAwake(self.data.skill_id)
	self.node_list["awake_img"]:SetActive(is_awake)

	if self.data.skill_id then
		self.skill_lock:SetActive(false)
		local bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id))
		self.node_list["ph_skill_icon"].image:LoadSprite(bundle, asset, function()
			self.node_list["ph_skill_icon"].image:SetNativeSize()
		end)
		self.lbl_skill_name.text.text = self.data.skill_name
		local skill_info = SkillWGData.Instance:GetSkillInfoById(self.data.skill_id)
		XUI.SetGraphicGrey(self.node_list.ph_skill_icon, skill_info == nil)
		self.is_active = skill_info ~= nil
	else
		self.skill_lock:SetActive(true)
		self.skill_icon:SetActive(false)
		self.lbl_skill_name.text.text = ToColorStr(Language.Skill.OpenTip_6, COLOR3B.RED)
	end
end

function SkillCfgItem:OnBeginDrag(event_data)
	if not self.obj and self.is_active then
		self:InstantSkillItem()
	end

	if self.begin_drag_back and self.is_active then
		self.begin_drag_back()
	end
end

function SkillCfgItem:OnEndDrag(event_data)
	if self.obj then
		ResMgr:Destroy(self.obj.gameObject)
		self.obj = nil
	end

	if self.end_drag_back and self.is_active then
		self.end_drag_back()
	end
end

function SkillCfgItem:OnDrag(event_data)
	local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.skill_drag.transform, UnityEngine.Input.mousePosition, self.canvas.worldCamera, Vector2(0, 0))
	if self.item_vo and self.item_vo.view and local_pos_tbl then
		self.item_vo.view.transform.localPosition = Vector3(local_pos_tbl.x, local_pos_tbl.y, 0)
	end
end

function SkillCfgItem:ClickCallBack()
	if self.click_call_back and self.is_active then
		self.click_call_back()
	end
end

function SkillCfgItem:InstantSkillItem()
    self.obj = ResMgr:Instantiate(self.node_list["ph_skill_itemrender"].gameObject)--
    self.obj:SetActive(true)
    local item_render = SkillItemRender.New(self.obj)
   	local obj_transform = self.obj.transform
    obj_transform:SetParent(self.node_list.skill_drag.transform, false)
    item_render:SetData(self.data.skill_id)
    item_render.parent_view = self
    self.item_vo = item_render
end

function SkillCfgItem:SetData(data)
	self.data = data
	self:Flush()
end

function SkillCfgItem:SetCanvas(canvas)
	self.canvas = canvas
end

function SkillCfgItem:SetHighLight(enabled)
	self.node_list.highlight.image.enabled = enabled
end

function SkillCfgItem:ChangeHighBySkillId(skill_id)
	self:SetHighLight(self.data.skill_id == skill_id)
end

function SkillCfgItem:SetClickCallBack(click_callback)
	self.click_call_back = click_callback
end

function SkillCfgItem:SetEndDragCallBack(click_callback)
	self.end_drag_back = click_callback
end

function SkillCfgItem:SetBeginDragCallBack(click_callback)
	self.begin_drag_back = click_callback
end

function SkillCfgItem:__delete()
	if self.obj then
		ResMgr:Destroy(self.obj.gameObject)
		self.obj = nil
	end
	self.begin_drag_back = nil
	self.item_vo = nil
	self.canvas = nil
	self.click_call_back = nil
	self.end_drag_back = nil
	self.img_skill_unopen = nil
	self.img_skill_icon_normal = nil
	self.skill_icon = nil
	self.lbl_skill_name = nil
	self.is_active = nil
	self.parent = nil
	self.skill_lock = nil
end