require("game/role/role_branch/custom_action/custom_action_data")
require("game/role/role_branch/custom_action.custom_action_view")
require("game/role/role_branch/custom_action/custom_action_window_view")


CustomActionCtrl = CustomActionCtrl or BaseClass(BaseWGCtrl)
function CustomActionCtrl:__init()
	if CustomActionCtrl.Instance then
		error("[CustomActionCtrl]:Attempt to create singleton twice!")
	end
	CustomActionCtrl.Instance = self

    self.data = CustomActionData.New()
    self.custom_action_window_view = CustomActionWindowView.New(GuideModuleName.CustomActionWindowView)

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function CustomActionCtrl:__delete()
    self:UnRegisterAllEvents()

    self.data:DeleteMe()
	self.data = nil

    self.custom_action_window_view:DeleteMe()
    self.custom_action_window_view = nil

    CustomActionCtrl.Instance = nil
end

function CustomActionCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSRoleActionReq)
	self:RegisterProtocol(SCRoleActionInfo, "OnRoleActionInfo")
    self:RegisterProtocol(SCRoleActionAllInfo, "OnRoleActionAllInfo")
    self:RegisterProtocol(SCRoleActionNotify, "OnRoleActionNotify")
end

function CustomActionCtrl:RegisterAllEvents()
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayChange, self))
end

function CustomActionCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

-- 物品变化
function CustomActionCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        local cfg = self.data:GetActionCfgByItemID(change_item_id)
        if cfg ~= nil then
            self.data:UpdateActionShowList()
            self:FlushView()
            RemindManager.Instance:Fire(RemindName.CustomAction)
        end
	end
end

-- 角色属性改变
function CustomActionCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
        self.data:UpdateActionShowList()
		self:FlushView()
        -- RemindManager.Instance:Fire(RemindName.CustomAction)
	end
end

-- 天数改变
function CustomActionCtrl:OnDayChange()
    self.data:UpdateActionShowList()
    self:FlushView()
    -- RemindManager.Instance:Fire(RemindName.CustomAction)
end

function CustomActionCtrl:FlushView()
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.RoleBranchView, TabIndex.custom_action) then
		ViewManager.Instance:FlushView(GuideModuleName.RoleBranchView, TabIndex.custom_action, "protocol_change")
	end
end

function CustomActionCtrl:OnRoleActionInfo(protocol)
    -- print_error("--更新单个动作信息---", protocol)
    local old_level = self.data:GetRoleActionLevel(protocol.action_id)
    self.data:SetRoleActionInfo(protocol)
    local new_level = self.data:GetRoleActionLevel(protocol.action_id)

    self:FlushView()
    RemindManager.Instance:Fire(RemindName.CustomAction)
    
    if ViewManager.Instance:IsOpenByIndex(GuideModuleName.RoleBranchView, TabIndex.custom_action) and new_level > old_level then
        local effect_type = old_level < 0 and UIEffectName.s_jihuo or UIEffectName.s_shengxing
		RoleWGCtrl.Instance:ShowActionSucessEffect(effect_type)
	end
end

function CustomActionCtrl:OnRoleActionAllInfo(protocol)
    -- print_error("---全部动作信息--", protocol)
    self.data:SetRoleActionAllInfo(protocol)
    self:FlushView()
    RemindManager.Instance:Fire(RemindName.CustomAction)
end

function CustomActionCtrl:OnRoleActionNotify(protocol)
	local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
    -- print_error("---动作范围广播--", protocol.obj_id, protocol.action_id, role and role:IsMainRole())
	if not role then
		return
	end

    if self:CheckObjIsCanDoAction(role, false) then
        local cfg = self.data:GetActionCfg(protocol.action_id)
        if cfg then
            -- print_error("---动作范围广播222--", protocol.action_id, cfg.ctrl_action_name)
            role:DoInteractive(cfg.ctrl_action_name)
        end
    end
end

function CustomActionCtrl:CheckObjIsCanDoAction(obj, is_need_remind_tips)
	if obj == nil or obj:IsDeleted() then
        if is_need_remind_tips then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
        end
		return false
	end

    if not obj:IsMainRole() and not obj:IsRole() then
        if is_need_remind_tips then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
        end
        return false
    end

    if obj:IsQingGong() or obj:IsJump() or obj:GetIsGatherState() or obj:IsMitsurugi() then
        if is_need_remind_tips then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
        end
		return false
	end

    if obj:IsChuanGong() then
        if is_need_remind_tips then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildAnswer.Passing)
        end
		return false
	end

    if obj:IsGhost() or obj:IsRidingFightMount() or obj.vo.special_appearance > 0 then
        if is_need_remind_tips then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotDoThis)
        end
		return false
	end

    if obj:IsFightState() then
        if is_need_remind_tips then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Fight.FightNoCustomAction)
        end
		return false
    end

    return true
end

-- 操作请求
function CustomActionCtrl:SendOperateReq(operate_type, param1)
    -- print_error("---操作请求--", operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleActionReq)
	protocol.operate_type = operate_type or -1
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function CustomActionCtrl:OpenCustomActionWindowView(vector2)
    if self.custom_action_window_view:IsOpen() then
        self.custom_action_window_view:Close()
    else
        self.custom_action_window_view:SetPanelPosition(vector2)
        self.custom_action_window_view:Open()
    end
end




