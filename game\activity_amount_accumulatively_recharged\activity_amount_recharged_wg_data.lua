ActivityAmountRechargedWGData = ActivityAmountRechargedWGData or BaseClass()

--充值排行榜的枚举
CROSS_CHONGZHI_RANK_OPERATE_TYPE = {
    RANK_INFO = 1               --请求信息
}

function ActivityAmountRechargedWGData:__init()
    if ActivityAmountRechargedWGData.Instance then
        error("[ActivityPrivilegeBuyWGData] Attempt to create singleton twice!")
    end

    ActivityAmountRechargedWGData.Instance = self


    self:LoadConfig()
end

function ActivityAmountRechargedWGData:__delete()
    ActivityAmountRechargedWGData.Instance = nil
end


function ActivityAmountRechargedWGData:LoadConfig()
    --拿到配置表中的信息
    local cfg = ConfigManager.Instance:GetAutoConfig("cross_chongzhi_rank_auto")
    
    --根据当前档次拿到配置信息
    self.rank_reward_cfg = ListToMapList(cfg.rank_reward, "grade")
    --print_error("self.rank_reward_cfg")
end

--协议信息
function ActivityAmountRechargedWGData:SetRankInfo(protocol)
    self.grade = protocol.grade    --当前轮次
    self.self_rank_value = protocol.self_rank_value     --累计充值数
    self.rank_item_list = protocol.rank_item_list       --上榜信息
    self.self_rank = protocol.self_rank                 --自己的排行信息
end

--返回当前自己的排行信息
function ActivityAmountRechargedWGData:GetMyRankData()
    --如果没有上榜,为0,    通过累计金额和有没有上榜进行判断
    return self.self_rank or 0
end

--返回当前轮次
function ActivityAmountRechargedWGData:GetActRechargedGrade()
    return self.grade or 0
end

--返回当前自己累计充值数
function ActivityAmountRechargedWGData:GetRankValue()
    return self.self_rank_value or 0
end

--通过当前轮次获取配置信息
function ActivityAmountRechargedWGData:GetRechargedRankCfg()
    return self.rank_reward_cfg[self.grade] or {}
end

--组合排行榜的数据
function ActivityAmountRechargedWGData:GetRechargedRankSort()
    local sort_list = {}
    local cfg = self:GetRechargedRankCfg()
    if IsEmptyTable(cfg) then
        return sort_list
    end

    --拿到配置表中的最大名次数
    local max_rank = cfg[#cfg].max_rank
    --布尔值 用于判断是否有排行信息
    --local is_rank = false
    for i = 1, max_rank do
        local data = self.rank_item_list[i]
        if not data then
            data = {
                rank = i,       --rank 排名数
                is_rank = true  --判断是否是虚位以待, 
            }
        else
            data.is_rank = false
        end

        table.insert(sort_list, data)
    end

    return sort_list
end


--获取对应排行榜的金额信息
function ActivityAmountRechargedWGData:GetMoneyNum(rank)
    --获取到排行榜信息
    local recharged_rank_list = self:GetRechargedRankSort()
    for k, v in ipairs(recharged_rank_list) do
        if rank == v.rank then
            return v.rank_value or 0
        end
    end
    return 0
end

--根据排名获得对应配置表的信息
function ActivityAmountRechargedWGData:GetRankItemCfg(rank)
    local recharged_rank_cfg = self:GetRechargedRankCfg()
    if recharged_rank_cfg == nil then
        return nil
    end

    for k, v in ipairs(recharged_rank_cfg) do
        if rank >= v.min_rank and rank <= v.max_rank then
            return v
        end
    end

    return nil
end

--获取到榜一的信息
function ActivityAmountRechargedWGData:GetTopRankInfo()
    local data_list = {}
    local rank_cfg = self:GetRechargedRankSort()
    if rank_cfg == nil  then
        return data_list
    end

    if rank_cfg[1] == nil then
        return data_list
    end

    table.insert(data_list, rank_cfg[1])
    return data_list
end