-- 内存泄漏白名单
-- 内存泄漏检查可能会误报，把误报的路径填在这里
-- 注意：只有主程才有权利改这个文件！！！
-- 非主程提交时请先经过主程同意，然后在svn log上写上主程大名 例如：经过xxx检查后允许提交
local WhiteList =
{
	"package",
	"config_chatfilter_list",
	"config_usernamefilter_list",
	"ChatWGCtrl.data.channel_list",
	"ChatWGCtrl.data.guild_unread_msg",
	"ChatWGCtrl.view.compre_view.compre_list",
	"ChatWGCtrl.view.system_view.system_list",
	"RemindManager.execute_callback_t",
	"RemindManager.record_num_t",
	"TaskWGCtrl.task_data.completed_id_list",
	"PackageWGCtrl.item_data.item_id_num_t",
	"PackageWGCtrl.package_data.new_item_list",
	"BundleCache",
	"ConfigManager.Instance.cfg_list",
	"FuBenWGCtrl.fu_ben_data.rand_weather",
	"Runner.priority_run_obj_list",
	"RemindManager.Instance.record_num_t",
	"DailyTaskWGCtrl.daily_task_data.shangjing_id_list",
}

return WhiteList
