require("game/new_appearance_dye/new_appearance_dye_wg_data")
require("game/new_appearance_dye/new_appearance_dye_view")
require("game/new_appearance_dye/new_appearance_dye_change_cheme_view")

NewAppearanceDyeWGCtrl = NewAppearanceDyeWGCtrl or BaseClass(BaseWGCtrl)
function NewAppearanceDyeWGCtrl:__init()
    if NewAppearanceDyeWGCtrl.Instance then
		error("[NewAppearanceDyeWGCtrl]:Attempt to create singleton twice!")
	end
	NewAppearanceDyeWGCtrl.Instance = self

    self.data = NewAppearanceDyeWGData.New()
    self.view = NewAppearanceDyeView.New(GuideModuleName.NewAppearanceDyeView)
	self.change_cheme_view = NewAppearanceDyeChangeChemeView.New()

    self:RegisterAllProtocols()
end

function NewAppearanceDyeWGCtrl:__delete()
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    NewAppearanceDyeWGCtrl.Instance = nil
end

function NewAppearanceDyeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSShizhuangDyeingOper)
	self:RegisterProtocol(CSShizhuangDyeingOperate)
	self:RegisterProtocol(CSShizhuangDyeingChangeInfoOper)
	self:RegisterProtocol(SCShizhuangDyeingInfo, "OnSCShizhuangDyeingInfo")
	self:RegisterProtocol(SCShizhuangDyeingUpdateInfo, "OnSCShizhuangDyeingUpdateInfo")
	self:RegisterProtocol(SCShizhuangOtherRoleDyeingInfo, "OnSCShizhuangOtherRoleDyeingInfo")
end

function NewAppearanceDyeWGCtrl:SendFashionOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShizhuangDyeingOper)
	protocol.operate_type = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 染色信息请求
function NewAppearanceDyeWGCtrl:OnReqDyeingInfo()
	self:SendFashionOperate(SHIZHUANG_DYE_ENUM.DYEING_INFO)
end

-- 开启染色方案
function NewAppearanceDyeWGCtrl:OnReqDyeingProjectOpen(seq, project_id)
	self:SendFashionOperate(SHIZHUANG_DYE_ENUM.DYEING_PROJECT_OPEN, project_id, seq)
end

-- 分享染色方案
function NewAppearanceDyeWGCtrl:OnReqDyeingShare(seq, project_id)
	self:SendFashionOperate(SHIZHUANG_DYE_ENUM.DYEING_SHARE, seq, project_id)
end

-- 其他人染色信息
function NewAppearanceDyeWGCtrl:OnReqDyeingOtherInfo(uid)
	self:SendFashionOperate(SHIZHUANG_DYE_ENUM.DYEING_OTHER_INFO, uid)
end

-- 请求使用染色方案
function NewAppearanceDyeWGCtrl:OnReqDyeingUseProject(seq, project_id)
	self:SendFashionOperate(SHIZHUANG_DYE_ENUM.DYEING_OPER_TYPE_USE_PROJECT, seq, project_id)
end

-- 染色请求
function NewAppearanceDyeWGCtrl:CSShizhuangDyeingOperate(seq, project_index, is_consume, part_color)
	-- print_error("染色请求", seq, project_index, is_consume, part_color)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShizhuangDyeingOperate)
	protocol.seq = seq or 0
	protocol.project_index = project_index or 0
	protocol.is_consume = is_consume or 0
    protocol.part_color = part_color or {}
	protocol:EncodeAndSend()
end

-- 染色方案改变信息请求
function NewAppearanceDyeWGCtrl:CSShizhuangDyeingChangeInfoOper(seq, project_index, name, info)
	-- print_error("染色方案改变信息请求", seq, project_index, name, info)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShizhuangDyeingChangeInfoOper)
	protocol.seq = seq or 0
	protocol.project_index = project_index or 0
    protocol.name = name or ""
    protocol.info = info or ""
	protocol:EncodeAndSend()
end

-- 染色所有信息
function NewAppearanceDyeWGCtrl:OnSCShizhuangDyeingInfo(protocol)
	-- print_error("染色所有信息", protocol)
	self.data:SetShizhuangDyeingInfo(protocol)
	self:FlushAppearanceDyeView()
end

-- 单个染色方案信息
function NewAppearanceDyeWGCtrl:OnSCShizhuangDyeingUpdateInfo(protocol)
	-- print_error("单个染色方案信息", protocol)
	self.data:SetShizhuangDyeingUpdateInfo(protocol)
	self:FlushAppearanceDyeView()
	ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_waiguan_body)
end

-- 其他人染色方案信息
function NewAppearanceDyeWGCtrl:OnSCShizhuangOtherRoleDyeingInfo(protocol)
	-- print_error("其他人染色方案信息", protocol)
	self.data:SetShizhuangOtherRoleDyeingInfo(protocol)
	self:OpenOtherAppearanceDyeView(protocol)
end


-- 打开染色面板
function NewAppearanceDyeWGCtrl:OpenAppearanceDyeView(fashion_part_type, fashion_index)
	self.view:SetNewAppearancePartIndex(fashion_index, fashion_part_type)
	if not self.view:IsOpen() then
		self.view:Open()
	else
		self.view:Flush()
	end
end

-- 打开染色面板(其他玩家的，仅查看)
function NewAppearanceDyeWGCtrl:OpenOtherAppearanceDyeView(other_data)
	self.view:SetOtherShowData(other_data)
	if not self.view:IsOpen() then
		self.view:Open()
	else
		self.view:Flush()
	end
end

-- 刷新染色面板
function NewAppearanceDyeWGCtrl:FlushAppearanceDyeView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 打开染色改变方案面板
function NewAppearanceDyeWGCtrl:OpenAppearanceDyeChangeChemeView(cur_scheme_data)
	self.change_cheme_view:SetCurSchemeData(cur_scheme_data)
	if not self.change_cheme_view:IsOpen() then
		self.change_cheme_view:Open()
	else
		self.change_cheme_view:Flush()
	end
end