﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Playables_PlayableDirectorWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Playables.PlayableDirector), typeof(UnityEngine.Behaviour));
		L<PERSON>RegFunction("DeferredEvaluate", DeferredEvaluate);
		<PERSON><PERSON>Function("Play", Play);
		<PERSON><PERSON>Function("SetGenericBinding", SetGenericBinding);
		<PERSON><PERSON>RegFunction("Evaluate", Evaluate);
		L<PERSON>RegFunction("Stop", Stop);
		L.RegFunction("Pause", Pause);
		<PERSON><PERSON>RegFunction("Resume", Resume);
		L<PERSON>RegFunction("RebuildGraph", RebuildGraph);
		<PERSON>.RegFunction("ClearReferenceValue", ClearReferenceValue);
		L.RegFunction("SetReferenceValue", SetReferenceValue);
		<PERSON><PERSON>Function("GetReferenceValue", GetReferenceValue);
		<PERSON><PERSON>("GetGenericBinding", GetGenericBinding);
		<PERSON><PERSON>unction("ClearGenericBinding", ClearGenericBinding);
		L.RegFunction("RebindPlayableGraphOutputs", RebindPlayableGraphOutputs);
		L.RegFunction("New", _CreateUnityEngine_Playables_PlayableDirector);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("state", get_state, null);
		L.RegVar("extrapolationMode", get_extrapolationMode, set_extrapolationMode);
		L.RegVar("playableAsset", get_playableAsset, set_playableAsset);
		L.RegVar("playableGraph", get_playableGraph, null);
		L.RegVar("playOnAwake", get_playOnAwake, set_playOnAwake);
		L.RegVar("timeUpdateMode", get_timeUpdateMode, set_timeUpdateMode);
		L.RegVar("time", get_time, set_time);
		L.RegVar("initialTime", get_initialTime, set_initialTime);
		L.RegVar("duration", get_duration, null);
		L.RegVar("played", get_played, set_played);
		L.RegVar("paused", get_paused, set_paused);
		L.RegVar("stopped", get_stopped, set_stopped);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Playables_PlayableDirector(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Playables.PlayableDirector obj = new UnityEngine.Playables.PlayableDirector();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Playables.PlayableDirector.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DeferredEvaluate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.DeferredEvaluate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
				obj.Play();
				return 0;
			}
			else if (count == 2)
			{
				UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
				UnityEngine.Playables.PlayableAsset arg0 = (UnityEngine.Playables.PlayableAsset)ToLua.CheckObject<UnityEngine.Playables.PlayableAsset>(L, 2);
				obj.Play(arg0);
				return 0;
			}
			else if (count == 3)
			{
				UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
				UnityEngine.Playables.PlayableAsset arg0 = (UnityEngine.Playables.PlayableAsset)ToLua.CheckObject<UnityEngine.Playables.PlayableAsset>(L, 2);
				UnityEngine.Playables.DirectorWrapMode arg1 = (UnityEngine.Playables.DirectorWrapMode)ToLua.CheckObject(L, 3, typeof(UnityEngine.Playables.DirectorWrapMode));
				obj.Play(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.Playables.PlayableDirector.Play");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGenericBinding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 2);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 3);
			obj.SetGenericBinding(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Evaluate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.Evaluate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.Stop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Pause(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.Pause();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Resume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.Resume();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RebuildGraph(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.RebuildGraph();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearReferenceValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			UnityEngine.PropertyName arg0 = StackTraits<UnityEngine.PropertyName>.Check(L, 2);
			obj.ClearReferenceValue(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetReferenceValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			UnityEngine.PropertyName arg0 = StackTraits<UnityEngine.PropertyName>.Check(L, 2);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 3);
			obj.SetReferenceValue(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetReferenceValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			UnityEngine.PropertyName arg0 = StackTraits<UnityEngine.PropertyName>.Check(L, 2);
			bool arg1;
			UnityEngine.Object o = obj.GetReferenceValue(arg0, out arg1);
			ToLua.Push(L, o);
			LuaDLL.lua_pushboolean(L, arg1);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGenericBinding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 2);
			UnityEngine.Object o = obj.GetGenericBinding(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearGenericBinding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 2);
			obj.ClearGenericBinding(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RebindPlayableGraphOutputs(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 1);
			obj.RebindPlayableGraphOutputs();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_state(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.PlayState ret = obj.state;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index state on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_extrapolationMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.DirectorWrapMode ret = obj.extrapolationMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index extrapolationMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_playableAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.PlayableAsset ret = obj.playableAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playableAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_playableGraph(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.PlayableGraph ret = obj.playableGraph;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playableGraph on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_playOnAwake(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			bool ret = obj.playOnAwake;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playOnAwake on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeUpdateMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.DirectorUpdateMode ret = obj.timeUpdateMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeUpdateMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			double ret = obj.time;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_initialTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			double ret = obj.initialTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			double ret = obj.duration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_played(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.Playables.PlayableDirector>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_paused(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.Playables.PlayableDirector>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_stopped(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.Playables.PlayableDirector>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_extrapolationMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.DirectorWrapMode arg0 = (UnityEngine.Playables.DirectorWrapMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Playables.DirectorWrapMode));
			obj.extrapolationMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index extrapolationMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_playableAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.PlayableAsset arg0 = (UnityEngine.Playables.PlayableAsset)ToLua.CheckObject<UnityEngine.Playables.PlayableAsset>(L, 2);
			obj.playableAsset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playableAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_playOnAwake(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.playOnAwake = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playOnAwake on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeUpdateMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			UnityEngine.Playables.DirectorUpdateMode arg0 = (UnityEngine.Playables.DirectorUpdateMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Playables.DirectorUpdateMode));
			obj.timeUpdateMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeUpdateMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			double arg0 = (double)LuaDLL.luaL_checknumber(L, 2);
			obj.time = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_initialTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)o;
			double arg0 = (double)LuaDLL.luaL_checknumber(L, 2);
			obj.initialTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index initialTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_played(IntPtr L)
	{
		try
		{
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject(L, 1, typeof(UnityEngine.Playables.PlayableDirector));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Playables.PlayableDirector.played' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Playables.PlayableDirector'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.Playables.PlayableDirector> ev = (System.Action<UnityEngine.Playables.PlayableDirector>)arg0.func;
				obj.played += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.Playables.PlayableDirector> ev = (System.Action<UnityEngine.Playables.PlayableDirector>)arg0.func;
				obj.played -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_paused(IntPtr L)
	{
		try
		{
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject(L, 1, typeof(UnityEngine.Playables.PlayableDirector));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Playables.PlayableDirector.paused' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Playables.PlayableDirector'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.Playables.PlayableDirector> ev = (System.Action<UnityEngine.Playables.PlayableDirector>)arg0.func;
				obj.paused += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.Playables.PlayableDirector> ev = (System.Action<UnityEngine.Playables.PlayableDirector>)arg0.func;
				obj.paused -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_stopped(IntPtr L)
	{
		try
		{
			UnityEngine.Playables.PlayableDirector obj = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject(L, 1, typeof(UnityEngine.Playables.PlayableDirector));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Playables.PlayableDirector.stopped' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Playables.PlayableDirector'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.Playables.PlayableDirector> ev = (System.Action<UnityEngine.Playables.PlayableDirector>)arg0.func;
				obj.stopped += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.Playables.PlayableDirector> ev = (System.Action<UnityEngine.Playables.PlayableDirector>)arg0.func;
				obj.stopped -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

