-----------------------------------------CfgInitStart----------------------------------------
function FightSoulWGData:InitCuiZhuoCfg()
    self.sixiang_quench_cfg = ConfigManager.Instance:GetAutoConfig("sixiang_quench_cfg_auto")
    self.cut_iron_cfg = ListToMap(self.sixiang_quench_cfg.cut_iron, "seq")
    self.grade_cfg = ListToMap(self.sixiang_quench_cfg.grade, "solt", "grade")
    self.skill_level_cfg = ListToMap(self.sixiang_quench_cfg.skill_level, "solt", "seq", "level")

    self.cuizhuo_info = {}
    self.cut_iron_level_list = {}
    self.zhuotie_cost_item_list = {}
    self.zhuotie_upgrade_cost_item_list = {}

    self:CalculateZhuoTieCostItem()
    self:CalculateZhuoTieUpGradeItem()
end
------------------------------------------CfgInitEnd-----------------------------------------

-----------------------------------------ProtocolStart---------------------------------------
function FightSoulWGData:SetSiXiangQuenchInfo(protocol)
    self.cuizhuo_info = protocol.item_list
    self.cut_iron_level_list = protocol.cut_iron_level_list
end

function FightSoulWGData:UpdateSiXiangQuenchItemInfo(protocol)
    self.cuizhuo_info[protocol.solt] = protocol.item
end

function FightSoulWGData:UpdateSiXiangQuenchCutIronInfo(protocol)
    self.cut_iron_level_list[protocol.seq] = protocol.level
end
-----------------------------------------protocolEnd-----------------------------------------

-----------------------------------------RemindStart-----------------------------------------
function FightSoulWGData:GetCuiZhuoRemind()
    if not FunOpen.Instance:GetFunIsOpenedByTabName("fight_soul_cuizhuo") then
       return 0
    end

    if self:GetZhuoTieRemind() then
        return 1
    end

    for i = 0, 3 do
        if self:GetCuiZhuoSoltUpGradeRemind(i) then
            return 1
        end
    end

    return 0
end

function FightSoulWGData:GetCuiZhuoSingleSlotRemind(slot, ignore_better)
	local slot_data = self:GetFightSoulSlot(slot)
	local jump_uplock = false
	if IsEmptyTable(slot_data) then
		return false, jump_uplock
	end

	if slot_data:IsLock() then
		if self:GetDropToUnlockRemind(slot) then
			return true, true
		else
			return false, jump_uplock
		end
	end

    if not ignore_better then
        if self:GetFightSoulCanWearByType(slot) then
            return true, jump_uplock
        end
    end

    if self:GetCuiZhuoSoltUpGradeRemind(slot) then
        return true, jump_uplock
    end

	return false, jump_uplock
end

function FightSoulWGData:GetZhuoTieRemind()
    local data_list = self:GetZhuoTieCfg()
    if IsEmptyTable(data_list) then
        return false
    end

    for k, v in pairs(data_list) do
        if self:GetZhuoTieLevelBySeq(v.seq) < v.max_level then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(v.cost_item_id)

            if item_num > 0 then
                return true
            end 
        end
    end

    return false
end

function FightSoulWGData:GetCuiZhuoSoltUpGradeRemind(solt)
    local fs_data = self:GetFightSoulSlot(solt)

    if IsEmptyTable(fs_data) then
        return false
    end

    if fs_data:IsLock() or not fs_data:GetIsWear() then
        return false
    end

    if self:IsCuiZhuoItemCanUpGrade(solt) then
        return true
    end

    for i = 0, 3 do
        if self:GetCuiZHuoSoltSkillRemind(solt, i) then
           return true 
        end
    end

    return false
end

function FightSoulWGData:GetCuiZHuoSoltSkillRemind(solt, seq)
    local data_info = self:GetSiXiangQuenchInfoBySolt(solt)
    if not IsEmptyTable(data_info) then
        local level = (data_info.skill_level_list or {}) [seq]

        if level and level > 0 then
            local data_list_cfg = self:GetCuiZhuoSkillCfg(solt, seq)

            if not IsEmptyTable(data_list_cfg) then
                local data_cfg = data_list_cfg[level]

                if data_cfg and level < #data_list_cfg then
                    local item_id = data_cfg.cost_item_id
                    local han_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

                    if han_num >= data_cfg.cost_item_num then
                        return true
                    end
                end
            end
        end
    end

    return false
end
-------------------------------------------Remindend------------------------------------------

-------------------------------------------CacheStart-----------------------------------------
function FightSoulWGData:CalculateZhuoTieCostItem()
    local data_list = self:GetZhuoTieCfg()

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if not self.zhuotie_cost_item_list[v.cost_item_id] then
                self.zhuotie_cost_item_list[v.cost_item_id] = v.cost_item_id
            end
        end
    end
end

function FightSoulWGData:CalculateZhuoTieUpGradeItem()
    local data_list = self:GetCuiZhuoStuffCfg()

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if not self.zhuotie_upgrade_cost_item_list[v.item_id] then
                self.zhuotie_upgrade_cost_item_list[v.item_id] = v.item_id
            end
        end
    end

    for i = 0, 3 do
        for j = 0, 3 do
           local data_list = self:GetCuiZhuoSkillCfg(i, j)
           if not IsEmptyTable(data_list) then
                for k, v in pairs(data_list) do
                    if not self.zhuotie_upgrade_cost_item_list[v.cost_item_id] then
                        self.zhuotie_upgrade_cost_item_list[v.cost_item_id] = v.cost_item_id
                    end
                end
           end
        end
    end
end
-------------------------------------------Cacheend-------------------------------------------

---------------------------------------ItemChangeCheckStart-----------------------------------
function FightSoulWGData:CheckIsZhuoTieItem(change_item_id)
    return nil ~= self.zhuotie_cost_item_list[change_item_id]
end

function FightSoulWGData:CheckIsZhuoTieUpGradeItem(change_item_id)
    return nil ~= self.zhuotie_upgrade_cost_item_list[change_item_id]
end
----------------------------------------ItemChangeCheckEnd------------------------------------

--------------------------------------------GetInfoStart--------------------------------------
function FightSoulWGData:GetZhuoTieLevelBySeq(seq)
    return self.cut_iron_level_list[seq] or 0
end

function FightSoulWGData:GetSiXiangQuenchInfoBySolt(solt)
    return self.cuizhuo_info[solt]
end
---------------------------------------------GetInfoEnd---------------------------------------

---------------------------------------------GetCfgStart--------------------------------------
function FightSoulWGData:GetZhuoTieCfg()
    return self.cut_iron_cfg
end

function FightSoulWGData:GetCuiZhuoGradeCfg(seq, grade)
    return (self.grade_cfg[seq] or {})[grade]
end

function FightSoulWGData:GetCuiZhuoStuffCfg()
    return self.sixiang_quench_cfg.stuff
end

function FightSoulWGData:GetCuiZhuoSkillCfg(solt, seq)
    return (self.skill_level_cfg[solt] or {})[seq]
end

function FightSoulWGData:GetCuiZhuoSkillLevelCfg(solt, seq, level)
    return ((self.skill_level_cfg[solt] or {})[seq] or {})[level]
end

function FightSoulWGData:GetCuiZhuoUpGradeShowAttr(seq, cur_level, not_compare_next_value)
    local cur_cfg = self:GetCuiZhuoGradeCfg(seq, cur_level)
    local next_cfg = self:GetCuiZhuoGradeCfg(seq, cur_level + 1)

	local attr_list = {}
	for i = 1, 5 do
		local attr_id = cur_cfg["attr_id" .. i]
		local attr_value = cur_cfg["attr_value" .. i]
		
		if attr_id and attr_id > 0 and attr_value then
			local data = {}
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(tonumber(attr_id))
			data["attr_str"] = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_str, false)
			data["attr_value"] = AttributeMgr.PerAttrValue(attr_str, attr_value)
            data["show_next_grade_data"] = not_compare_next_value

            if not IsEmptyTable(next_cfg) then
                if not_compare_next_value then
                    data["add_value"] = 0
                else
                    data["add_value"] = AttributeMgr.PerAttrValue(attr_str, next_cfg["attr_value" .. i] - attr_value)
                end
            end

			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end
    
	return attr_list
end
----------------------------------------------GetCfgEnd---------------------------------------

----------------------------------------------JudgeStart--------------------------------------
function FightSoulWGData:GetCuiZhuoStuffCanAddExp()
    local exp = 0
    local data_cfg = self:GetCuiZhuoStuffCfg()
    if not IsEmptyTable(data_cfg) then
        for k, v in pairs(data_cfg) do
            local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
            exp = exp + num * v.exp
        end
    end

    return exp
end

function FightSoulWGData:IsCuiZhuoItemCanUpGrade(solt)
    local data_info = self:GetSiXiangQuenchInfoBySolt(solt)
    if IsEmptyTable(data_info) then
        return false
    end

    local cfg = self:GetCuiZhuoGradeCfg(solt, data_info.grade)
    local next_cfg = self:GetCuiZhuoGradeCfg(solt, data_info.grade + 1)
    if IsEmptyTable(cfg) or IsEmptyTable(next_cfg) then
        return false
    end

    local can_get_exp = self:GetCuiZhuoStuffCanAddExp()
    if can_get_exp <= 0 then
        return false
    end
    
    local need_exp = cfg.need_exp - data_info.exp
    if need_exp <= can_get_exp then
        return true
    end

    return false
end
-----------------------------------------------JudgeEnd---------------------------------------