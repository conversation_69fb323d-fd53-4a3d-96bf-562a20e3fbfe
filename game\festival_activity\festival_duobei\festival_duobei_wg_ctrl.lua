
require("game/festival_activity/festival_duobei/festival_duobei_wg_data")

-- 合服活动-多倍有礼 
FestivalDuoBeiWGCtrl = FestivalDuoBeiWGCtrl or BaseClass(BaseWGCtrl)

function FestivalDuoBeiWGCtrl:__init()
	if FestivalDuoBeiWGCtrl.Instance then
		ErrorLog(" FestivalDuoBeiWGCtrl] Attemp to create a singleton twice !")
	end
 	FestivalDuoBeiWGCtrl.Instance = self

	self.data = FestivalActDuoBeiWGData.New()
	self:RegisterAllProtocols()
end

function FestivalDuoBeiWGCtrl:__delete()
 	FestivalDuoBeiWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
end

function FestivalDuoBeiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCFestivalDuoBeiInfo, "OnSCCSADuoBeiInfo")
	self:RegisterProtocol(CSFestivalDuoBei)
end

--多倍来袭返回
function FestivalDuoBeiWGCtrl:OnSCCSADuoBeiInfo(protocol)
	self.data:SetDuoBeiInfo(protocol)
	FestivalActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.FESTIVAL_ACT_OA_DUOBEI_2)
	FestivalActivityWGCtrl.Instance:FlushView(TabIndex.festival_activity_2261, "RefreshDuoBei")
end

function FestivalDuoBeiWGCtrl:SendDayDuoBeiReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSFestivalDuoBei)
	protocol:EncodeAndSend()
end
