
-- 离线时间溢出
----------------------------------
OffLineOverstepView = OffLineOverstepView or BaseClass(SafeBaseView)

function OffLineOverstepView:__init()
	--self:SetModal(true)
	self:LoadConfig()
end

function OffLineOverstepView:LoadConfig()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_overstep")
end

function OffLineOverstepView:ReleaseCallBack()
end

function OffLineOverstepView:LoadCallBack(index, loaded_times)
	self:SetSecondView()

	self.node_list.title_view_name.text.text = Language.Common.Hint
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
end

function OffLineOverstepView:SetData(item_id, item_num)
	self.item_id = item_id
	self.item_num = item_num
	self:Open()
end

function OffLineOverstepView:OnFlush()
end

function OffLineOverstepView:OnClickConfirm()
	local item_index = ItemWGData.Instance:GetItemIndex(self.item_id)
	BagWGCtrl.Instance:SendUseItem(item_index, self.item_num)
	self:Close()
end

function OffLineOverstepView:OnClickCancel()
	self:Close()
end

function OffLineOverstepView:ShowIndexCallBack()
	self:Flush()
end

function OffLineOverstepView:OpenCallBack()
end

function OffLineOverstepView:CloseCallBack(is_all)
end