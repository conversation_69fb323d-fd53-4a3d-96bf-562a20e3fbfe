﻿//------------------------------------------------------------------------------
// Copyright c 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

Shader "Game/PostEffect/BlurPass"
{
	Properties{
		_MainTex("Base (RGB)", 2D) = "" {}
	}

	Subshader{
		Tags{
			"RenderPipeline" = "UniversalRenderPipeline"
		}

		HLSLINCLUDE
		#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
		CBUFFER_START(UnityPerMaterial)
		half4 _MainTex_ST;
		CBUFFER_END

		TEXTURE2D(_MainTex);
		SAMPLER(sampler_MainTex);
		float4 _Offsets;

		struct appdata_img {
			float4 vertex:POSITION;
			float2 texcoord:TEXCOORD0;
		};

		struct v2f {
			float4 pos : POSITION;
			float2 uv : TEXCOORD0;

			float4 uv01 : TEXCOORD1;
			float4 uv23 : TEXCOORD2;
			float4 uv45 : TEXCOORD3;
			float4 uv67 : TEXCOORD4;
		};


		v2f vert(appdata_img v) {
			v2f o;
			VertexPositionInputs vertexInput = GetVertexPositionInputs(v.vertex.xyz);
			o.pos = vertexInput.positionCS;
			o.uv.xy = v.texcoord.xy;

			o.uv01 = v.texcoord.xyxy + _Offsets.xyxy * float4(1, 1, -1,-1);
			o.uv23 = v.texcoord.xyxy + _Offsets.xyxy * float4(1, 1, -1,-1) * 2.0;
			o.uv45 = v.texcoord.xyxy + _Offsets.xyxy * float4(1, 1, -1,-1) * 3.0;
			o.uv67 = v.texcoord.xyxy + _Offsets.xyxy * float4(1, 1, -1, -1) * 4.0;
			return o;
		}

		half4 frag(v2f i) : COLOR {
			half4 color = float4 (0,0,0,0);
			color += 0.225 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);
			color += 0.150 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv01.xy);
			color += 0.150 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv01.zw);
			color += 0.110 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv23.xy);
			color += 0.110 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv23.zw);
			color += 0.075 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv45.xy);
			color += 0.075 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv45.zw);
			color += 0.0525 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv67.xy);
			color += 0.0525 * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv67.zw);
			return color;
		}
		ENDHLSL

		Pass {
			ZTest Off
			Cull Off
			ZWrite Off

			HLSLPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			ENDHLSL
		}
	}

	Fallback off
}
