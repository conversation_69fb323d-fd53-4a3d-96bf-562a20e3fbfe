function CangJinExchangeView:LoadIndexCallBackExchange()
    if not self.exchange_item_list then
        self.exchange_item_list = AsyncListView.New(CangJinExchangeItemRender, self.node_list.exchange_item_list)
        self.exchange_item_list:SetStartZeroIndex(true)
    end

    if not self.exchange_model_display then
        self.exchange_model_display = OperationActRender.New(self.node_list["exchange_model_display"])
        self.exchange_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)

        self:FlushExchangeModel()
	end
end

function CangJinExchangeView:ReleaseCallBackExchange()
    if self.exchange_item_list then
        self.exchange_item_list:DeleteMe()
        self.exchange_item_list = nil
    end

    if self.exchange_model_display then
        self.exchange_model_display:DeleteMe()
        self.exchange_model_display = nil
    end

    self:ClearTimeCountDown()
end

function CangJinExchangeView:ExchangeShowIndexCallBack()
    local time = CangJinShopWGData.Instance:GetExchangeLimitTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    self:ClearTimeCountDown()

    if server_time < time then
        self.node_list.exchange_limit_time_bg:CustomSetActive(true)
        self.node_list.exchange_limit_time.text.text = string.format(Language.Title.RemainTime, ToColorStr(TimeUtil.FormatSecondDHM2(time - server_time), COLOR3B.D_GREEN))

        CountDownManager.Instance:AddCountDown("CangJinExchangeLimitTime",
        function (elapse_time, total_time)
            local valid_time = total_time - elapse_time

	        if valid_time > 0 then
                self.node_list.exchange_limit_time.text.text = string.format(Language.Title.RemainTime, ToColorStr(TimeUtil.FormatSecondDHM2(valid_time), COLOR3B.D_GREEN))
	        end
        end,
        function ()
            if self.node_list.exchange_limit_time_bg then
                self.node_list.exchange_limit_time_bg:CustomSetActive(false)
            end
        end, time, nil, 1)
    else
        if self.node_list.exchange_limit_time_bg then
            self.node_list.exchange_limit_time_bg:CustomSetActive(false)
        end
    end
end

function CangJinExchangeView:OnFlushExchange()
    local data_list = CangJinShopWGData.Instance:GetConvertCfgDataList()
    self.exchange_item_list:SetDataList(data_list)
end

function CangJinExchangeView:ClearTimeCountDown()
	if CountDownManager.Instance:HasCountDown("CangJinExchangeLimitTime") then
        CountDownManager.Instance:RemoveCountDown("CangJinExchangeLimitTime")
   	end
end

function CangJinExchangeView:FlushExchangeModel()
    local init_cfg = CangJinShopWGData.Instance:GetModelCfgByIndex(2)
    if not init_cfg then
        return
    end

    local display_data = {}
	display_data.should_ani = true
	if init_cfg.model_show_itemid ~= 0 and init_cfg.model_show_itemid ~= "" then
		local split_list = string.split(init_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = init_cfg.model_show_itemid
		end
	end
	
	display_data.bundle_name = init_cfg["model_bundle_name"]
    display_data.asset_name = init_cfg["model_asset_name"]
    local model_show_type = tonumber(init_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

    self.exchange_model_display:SetData(display_data)

    if init_cfg.display_scale and init_cfg.display_scale ~= "" then
        local scale = init_cfg.display_scale
        Transform.SetLocalScaleXYZ(self.node_list["exchange_model_display"].transform, scale, scale, scale)
    end

    local pos_x, pos_y = 0, 0
	if init_cfg.display_pos and init_cfg.display_pos ~= "" then
		local pos_list = string.split(init_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.exchange_model_display.rect, pos_x, pos_y)

	if init_cfg.rotation and init_cfg.rotation ~= "" then
		local rotation_tab = string.split(init_cfg.rotation,"|")
		self.node_list["exchange_model_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

CangJinExchangeItemRender = CangJinExchangeItemRender or BaseClass(BaseRender)

function CangJinExchangeItemRender:LoadCallBack()
    for i = 1, 3 do
        if not self["item" .. i] then
            self["item" .. i] = ItemCell.New(self.node_list["item" .. i])
        end
    end

    XUI.AddClickEventListener(self.node_list["btn_exchange"], BindTool.Bind(self.OnClickExchangeBtn, self))
end

function CangJinExchangeItemRender:__delete()
    for i = 1, 3 do
        if self["item" .. i] then
            self["item" .. i]:DeleteMe()
            self["item" .. i] = nil
        end
    end
end

function CangJinExchangeItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local convert_cost_cache = CangJinShopWGData.Instance:GetConvertCacheByGradeAndSeq(self.data.grade, self.data.seq)

	if not IsEmptyTable(convert_cost_cache) then
		for k, v in pairs(convert_cost_cache) do
			local cost_item_id, num = v.item_id, v.num
            local item = self["item" .. k]

            if item then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
                local enough = item_num >= num

                item:SetFlushCallBack(function ()
                    local right_text = ToColorStr(item_num .. "/" .. num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
                    item:SetRightBottomColorText(right_text)
                    item:SetRightBottomTextVisible(true)
                end)
        
                item:SetData({item_id = cost_item_id})
            end
		end
	end

    local need_score = self.data.need_score
    if need_score > 0 then
        local cost_item_id = CangJinShopWGData.Instance:GetVirtualItemRechargeScore()
        local cur_score = CangJinShopWGData.Instance:GetCurScore()
        local enough = cur_score >= need_score

        self.item2:SetFlushCallBack(function ()
            local right_text = ToColorStr(CommonDataManager.ConverExpByThousand(cur_score) .. "/" .. CommonDataManager.ConverExpByThousand(need_score), enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.item2:SetRightBottomColorText(right_text)
            self.item2:SetRightBottomTextVisible(true)
        end)

        self.item2:SetData({item_id = cost_item_id})
    end

    -- for i = 1, 2 do
    --     local data_info = self.data["stuff_item" .. i]
    --     local item = self["item" .. i]

    --     local cost_item_id = data_info.item_id
    --     local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    --     local cost_item_num = data_info.num
    --     local enough = item_num >= cost_item_num

    --     item:SetFlushCallBack(function ()
    --         local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
    --         item:SetRightBottomColorText(right_text)
    --         item:SetRightBottomTextVisible(true)
    --     end)

    --     item:SetData(data_info)
    -- end

    self.item3:SetData(self.data.reward_item[0])

    local limit_str = ""
    local exchange_time = CangJinShopWGData.Instance:GetConvertNumBySeq(self.data.seq)
    if self.data.limit > 0 then
        local color = exchange_time >= self.data.limit and COLOR3B.L_RED or COLOR3B.L_GREEN
        local can_change_time = self.data.limit - exchange_time
        can_change_time = can_change_time >= 0 and can_change_time or 0
        limit_str = string.format(Language.CangJinShopView.ExchangeTimeLimit, color, can_change_time, self.data.limit)
    end
    self.node_list.limit_text.text.text = limit_str
    
    XUI.SetButtonEnabled(self.node_list["btn_exchange"], (self.data.limit <= 0) or ((self.data.limit > 0) and (exchange_time < self.data.limit)))
end

function CangJinExchangeItemRender:OnClickExchangeBtn()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.limit > 0 then
		local exchange_time = CangJinShopWGData.Instance:GetConvertNumBySeq(self.data.seq)
		if exchange_time >= self.data.limit then
            TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.MaxExchangeNum)
			return
		end
	end

    local convert_cost_cache = CangJinShopWGData.Instance:GetConvertCacheByGradeAndSeq(self.data.grade, self.data.seq)
	if not IsEmptyTable(convert_cost_cache) then
		for k, v in pairs(convert_cost_cache) do
			local cost_item_id, num = v.item_id, v.num 
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)

			if item_num < num then
                TipWGCtrl.Instance:OpenItem({item_id = cost_item_id})
				return
			end
		end
	end

    -- 积分
    local need_score = self.data.need_score
    if need_score > 0 then
        local cur_score = CangJinShopWGData.Instance:GetCurScore()
        if cur_score < need_score then
            TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.NoEnoughScore)
            RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough(need_score - cur_score)
            return
        end
    end
    
    CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.CONVERT, self.data.seq)
end