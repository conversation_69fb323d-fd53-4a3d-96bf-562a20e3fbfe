--- 匹配界面，显示在最上面
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/9/19 21:12
---

TeamMatchView = TeamMatchView or BaseClass(SafeBaseView)

function TeamMatchView:__init()
    self.view_layer = UiLayer.PopTop
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_match_time")
    self.active_close = false
end

function TeamMatchView:ReleaseCallBack()
    if self.run_quest then
        GlobalTimerQuest:CancelQuest(self.run_quest)
        self.run_quest = nil
    end
    self.pre_container_state = nil
end

function TeamMatchView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_stop_match, BindTool.Bind(self.OnClickStopMatch, self))
end

function TeamMatchView:Open()
    self.is_do_ani = nil
    SafeBaseView.Open(self)
    --初始化缓存的状态,防止频繁的去访问C#
    self.max_tip_state = true
    self.pre_container_state = nil
end

function TeamMatchView:CloseCallBack()
    if self.run_quest then
        GlobalTimerQuest:CancelQuest(self.run_quest)
        self.run_quest = nil
    end
    self.pre_container_state = nil
end

function TeamMatchView:ShowIndexCallBack()
    if not self.run_quest then
        self.run_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateTime,self),1)
    end
    --目前匹配机制，不考虑掉线或离线，掉线直接取消匹配，打开界面直接从0开始计算
    self.cur_time_value = 0
    self:RefreshView()
    if not self.is_do_ani then
        self.node_list.AnimationContianer.transform.position = self.node_list["Pos1"].transform.position
        GlobalTimerQuest:AddDelayTimer(function()
            self.node_list.AnimationContianer.transform:DOMoveY(self.node_list["Pos2"].transform.position.y, 0.3):SetEase(DG.Tweening.Ease.Linear)
            self.is_do_ani = true
        end, 0.7)

    end
    --UITween.MoveToShowPanel(self.node_list.AnimationContianer.gameObject, self.node_list["Pos1"], self.node_list["Pos1"].transform.position.y, tween_time, show_type, callback)
end

function TeamMatchView:UpdateTime(elapse_time, total_time)
    self.cur_time_value = self.cur_time_value + 1
    self:RefreshView()
end

function TeamMatchView:RefreshView()
    local pre_time, is_max = NewTeamWGData.Instance:GetTeamMatchPreTime(self.cur_time_value)
    if is_max then
        self.node_list.pre_time.text.text = Language.NewTeam.MaxMatchTimeTips
        if self.pre_container_state then
            self.node_list.pre_container:CustomSetActive(false)
            self.pre_container_state = false
        end
        if not self.max_tip_state then
            self.node_list.max_time_tip:CustomSetActive(true)
            self.max_tip_state = true
        end
    else
        local team_type, team_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        if team_type == GoalTeamType.QingYuanFb then
            self.node_list.pre_time.text.text = Language.Society.IsOnPiPei2
        else
            self.node_list.pre_time.text.text = pre_time..Language.Society.IsOnPiPei1--TimeUtil.MSTime(pre_time)
        end
            if not self.pre_container_state then
                self.node_list.pre_container:CustomSetActive(true)
                self.pre_container_state = true
            end
            if self.max_tip_state then
                self.node_list.max_time_tip:CustomSetActive(false)
                self.max_tip_state = false
            end
        
    end
    self.node_list.cur_time.text.text = TimeUtil.MSTime(self.cur_time_value)
end

function TeamMatchView:OnClickLookMatch()
    NewTeamWGCtrl.Instance:OpenBaoMingEnterView()
end

function TeamMatchView:OnClickStopMatch()
    local team_type, team_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
    NewTeamWGCtrl.Instance:SendAutoMatchTeam(1, team_type , team_mode)
end