----------------------------------------------------
-- UI模型摄像机配置
----------------------------------------------------
return {
	player_view_panel = {						-- 角色面板
		position = Vector3(-0.1, 2, 5.2),
		rotation = Vector3(5, 192, 0),
	},

	boss_view_panel = {							-- Boss面板
		position = Vector3(-0.8, -3.7, 20),
		rotation = Quaternion.E<PERSON>r(0, 145, 0),
	},
	boss_view_shuijing_panel1 = {							-- Boss面板 水晶
		position = Vector3(-0.8, -5.77, 20),
		rotation = Quaternion.Euler(0, 145, 0),
	},
	boss_view_shuijing_panel2 = {							-- Boss面板 水晶
		position = Vector3(-0.8, -3.7, 20),
		rotation = Quaternion.<PERSON><PERSON>r(0, 145, 0),
	},

	appearance_mount_view_panel = {				-- 幻装坐骑面板
		position = Vector3(-0.4, -2, 12),
		rotation = Quaternion.Euler(0, -203, 0),
	},

	appearance_fightmount_view_panel = {		-- 幻装战骑面板
		position = Vector3(-0.4, -1.8, 10),
		rotation = Quaternion.Euler(0, -203, 0),
	},

	appearance_lingchong_view_panel = {			-- 幻装灵宠面板
		position = Vector3(0, -1, 5),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	appearance_fashion_view_panel = {			-- 幻装时装面板
		position = Vector3(0, -1.36, 4.8),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	appearance_xuanzhuang_view_panel = {		-- 炫装特卖时装面板
		position = Vector3(0, 1.6, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	appearance_xuanzhuangsb_view_panel = {		-- 炫装特卖神兵面板
		position = Vector3(0, 1.6, 5.2),
		rotation = Vector3(0, 183, 0),
	},
	xuanzhuang_halo_view_panel = {				-- 炫装光环面板
		position = Vector3(-0.2, 0.2, 6.5),
		rotation = Vector3(0, 180, 0),
	},

	appearance_foot_view_panel = {				-- 幻装时装足迹面板
		position = Vector3(0, -1.34, 5),
		rotation = Quaternion.Euler(0, -143.093, 0),
	},

	appearance_suit_view_panel = {				-- 幻装套装面板
		position = Vector3(-0.3, 1.6, 4.8),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	appearance_halo_view_panel = {				-- 幻装光环面板
		position = Vector3(0.1, 0.2, 5.2),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	appearance_fashionwing_view_panel = {				-- 幻装羽翼面板
		position = Vector3(0, -1, 7.0),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	appearance_fashionwingtwo_view_panel = {				-- 幻装羽翼面板
		position = Vector3(0, 0.2, 7.0),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	appearance_linggong_view_panel = {			-- 幻装灵弓面板
		position = Vector3(0, -1, 4),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	huanzhuang_fabao_view_panel = {				-- 幻装法宝面板
		position = Vector3(-0.3, -1, 5),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	mounglingchong_mount_view_panel = {			-- 进阶坐骑面板
		position = Vector3(-1.5, -1.9, 10),
		rotation = Quaternion.Euler(0, 125, 0),
	},
	lingchong_mount_view_panel = {			-- 进阶灵宠面板
		position = Vector3(-1, -1.07, 5),
		rotation = Quaternion.Euler(0, 150, 0),
	},
	mounglingchong_fightmount_view_panel = {	-- 进阶战骑面板
		position = Vector3(-1.5, -1.9, 8),
		rotation = Quaternion.Euler(0, 125, 0),
	},

	role_skill_panel = {	                         -- 技能面板
		position = Vector3(0.7,5.9,7.88),
		rotation = Vector3(18.9,189.351,0),
	},

	appearance_wing_view_panel = {				-- 角色羽翼面板
		position = Vector3(-1.7, -2.29, 0),
		rotation = Vector3(0, 180, 0),
		scale = Vector3(150, 150, 150),
	},

	appearance_jianzhen_view_panel = {				-- 角色剑阵面板
		position = Vector3(-2.2, 0, 0),
		rotation = Vector3(0, 180, 0),
		scale = Vector3(100, 100, 100)
	},

	appearance_fabao_view_panel = {				-- 角色法宝面板
		position = Vector3(-0.9, -0.6, 4),
		rotation = Vector3(0, 170, 0),
	},
	appearance_fabao_view_panel2 = {				-- 角色法宝面板
		position = Vector3(-1.4, 1.8, 12.2),
		rotation = Vector3(0, -180, 0),
		scale = Vector3(85, 85, 85),
	},
	appearance_fabao_view_panel2_1 = {				-- 角色法宝个人信息面板
		position = Vector3(-0.55, 1.8, 12.2),
		rotation = Vector3(0, -180, 0),
		scale = Vector3(85, 85, 85),
	},
	appearance_mask_view_panel = {				-- 幻化面饰面板
		position = Vector3(0, -1.34, 0),
		rotation = Vector3(0, -180, 0),
		scale = Vector3(208, 208, 208),
	},
	appearance_belt_view_panel = {				-- 幻化腰饰面板
		position = Vector3(0, -1.34, 0),
		rotation = Vector3(0, -120, 0),
		scale = Vector3(208, 208, 208),
	},
	appearance_weiba_view_panel = {				-- 幻化尾巴面板
		position = Vector3(0, -1.34, 0),
		rotation = Vector3(0, 0, 0),
		scale = Vector3(208, 208, 208),
	},
	appearance_shouhuan_view_panel = {				-- 幻化手环面板
		position = Vector3(0, -1.34, 0),
		rotation = Vector3(0, 90, 0),
		scale = Vector3(208, 208, 208),
	},
	fun_fabao_panel = {
		position = Vector3(4.8, 2, 4),
		rotation = Vector3(0, -130, 0),
	},
	fun_item_panel = {
		position = Vector3(3.77, 0.7, 3.1),
		rotation = Vector3(0, -130, 0),
	},

	tips_fun_shenbing_panel = { 						--神兵
        position = Vector3(-0.29, -1.25, -0.37),
        rotation = Vector3(-90, 46, 3.634),
	},
	fun_npc_panel = {
		position = Vector3(0, 1.8, 6.77),
	},
	fun_lingqi_panel = { 						--开启预告 灵骑
		position = Vector3(5.77, 2.6, 5.1),
		rotation = Vector3(0, -130, 0),
	},
	fun_marry_left_panel = { 						--开启预告 仙侣-左
		position = Vector3(0, 2.8, 8.92),
		rotation = Vector3(0, 180, 0),
	},
	fun_marry_right_panel = { 						--开启预告 仙侣-右
		position = Vector3(0.7, 1.7, 7),
		rotation = Vector3(0, 180, 0),
	},
	fun_shenshou_panel = {
		position = Vector3(0.22, 3.43, 12.73),
		rotation = Vector3(0, 180, 0),
	},
	fun_tianshen_shenqi_panel = {
		position = Vector3(-0.69, 2.5, 8),
		rotation = Vector3(0, 180, 0),
	},
	fun_huakun_panel = {
		position = Vector3(0.09, 3.5, 12),
		rotation = Vector3(0, 180, 0),
	},

	fun_jianling_panel = {
		position = Vector3(-0.22, 2, 9.01),
		rotation = Vector3(0, 180, 0),
	},
	fun_sg_shenling_panel = {
		position = Vector3(-0.25, 3.2, 10.6),
		rotation = Vector3(0, 180, 0),
	},
	fun_tianshen_panel = {
		position = Vector3(-0.12, 3.5, 12),
		rotation = Vector3(0, 180, 0),
	},
	fun_tianjipu_panel = {
		position = Vector3(0, 1.1, 15.5),
		rotation = Vector3(0, 180, 0),
	},
	fun_zhuanzhi1_panel = {
		position = Vector3(-0.1, 2.1, 6.8),
		rotation = Vector3(0, 180, 0),
	},
	fun_zhuanzhi2_panel = {
		position = Vector3(-0.05, 2.2, 6.8),
		rotation = Vector3(0, 180, 0),
	},
	fun_feichong_panel = {
		position = Vector3(0.04, 0.6, 1.8),
		rotation = Vector3(0, 180, 0),
	},
	fun_qilingbi_panel = {
		position = Vector3(0.1, 1.03, 3.1),
		rotation = Vector3(0, 180, 0),
	},
	appearance_shenbing_view_panel = {			-- 角色神兵面板
		position = Vector3(-1.5, -0.8, 0),
		rotation = Vector3(120, 100, 9.81),
		scale = Vector3(230, 230, 230),
	},

	role_linggong_view_panel = {				-- 角色灵弓面板
		position = Vector3(-0.85, -0.68, 4.2),
		rotation = Vector3(0, 180, 0),
	},

	mount_equip_view_panel = {				-- 坐骑装备面板
		position = Vector3(6.543, 2.112, 7.813),
		rotation = Vector3(0, -140, 0),
	},

	lingchong_equip_view_panel = {				-- 灵宠装备面板
		position = Vector3(0, 1.37, 5.91),
		rotation = Vector3(0, 180, 0),
	},

	shop_tip_mount_view_panel = {				--商城坐骑模型位置
		position = Vector3(4, 1, 8),
		rotation = Vector3(0, 210, 0),
	},

	shop_tip_fabao_view_panel = {				--商城法宝模型位置	
		position = Vector3(0, 0.7, -5),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(130, 130, 130),
	},

	shop_tip_shenbing_view_panel = {				--商城神兵模型位置
		position = Vector3(0, 5, -1),
		rotation = Vector3(90, -15, 180),
		scale = Vector3(130, 130, 130),
	},

	shop_tip_jianzhen_view_panel = {				--商城剑阵模型位置
		position = Vector3(0.5, 0.5, -10),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(130, 130, 130),
	},


	display_tip_mount_view_panel = {				--display_tip坐骑模型位置1
		position = Vector3(6, 2.5, 12),
		rotation = Vector3(0, 210, 0),
	},

	display_tip_mount_view_panel2 = {				--display_tip坐骑模型位置2
		position = Vector3(-1, -3, 0),
		rotation = Vector3(0, -210, 0),
		scale = Vector3(130, 130, 130),
	},

	display_tip_lingchong_view_panel = {			--display_tip灵宠模型位置
		position = Vector3(0, 0.8, 4),
		rotation = Vector3(0, 180, 0),
	},

	display_tip_fashion_view_panel = {				--display_tip时装模型位置
		position = Vector3(-1.2, -1.6, 5.5),
		rotation = Vector3(0, 180, 0),
	},

	display_tip_fabao_view_panel = {				--display_tip法宝模型位置
		position = Vector3(-0.729, -1.814, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(130, 130, 130),
	},
	display_tip_shenbing_view_panel = {				--display_tip神兵模型位置
		position = Vector3(-0.46, -1.88, 0),
		rotation = Vector3(110, 90, 90),
		scale = Vector3(130, 130, 130),
	},

	display_tip_jianzhen_view_panel = {				--display_tip剑阵模型位置
		position = Vector3(-1.5, -2, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(80, 80, 80),
	},

	display_tip_wing_view_panel = {				--display_tip羽翼模型位置
		position = Vector3(-1, -3, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(130, 130, 130),
	},


	display_tip_foot_view_panel = {				--display_tip足迹模型位置
		position = Vector3(-0.7, -1.7, 5.6),
		rotation = Vector3(0, 180, 0),
	},

	display_tip_halo_view_panel = {				--display_tip光环模型位置
		position = Vector3(-0.6, -0.2, 5.6),
		rotation = Vector3(0, 180, 0),
	},

	shop_tip_wing_view_panel = {				--商城羽翼模型位置
		position = Vector3(-0.5, 2.7, 6.6),
		rotation = Vector3(0, 180, 0),
	},

	display_tip_linggong_view_panel = {			--display_tip灵弓模型位置
		position = Vector3(0, 0.7, 3.5),
		rotation = Vector3(0, 180, 0),
	},

	mster_shitu_shifu_panel = {             --师徒界面 师傅
		position = Vector3(-0.1, 1.6, 6.2),
		rotation = Vector3(0, 180, 0),
	},
	mster_shitu_tudi1_panel = {             --师徒界面 徒弟1
		position = Vector3(-0.1, 2, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	mster_shitu_tudi2_panel = {             --师徒界面 徒弟2
		position = Vector3(-0.1, 2, 5.2),
		rotation = Vector3(0, 180, 0),
	},
		mster_shitu_tudi2_panel = {             --师徒界面 徒弟2
		position = Vector3(-0.1, 2, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	fuben_duoren_boss_panel1 = {             --副本多人模型数据
		position = Vector3(-0.1, 1.57, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	fuben_duoren_boss_panel2 = {             --副本多人模型数据
		position = Vector3(-0.1, 2.2, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	fuben_duoren_boss_panel3 = {             --副本多人模型数据
		position = Vector3(-0.1, 1.48, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	fuben_duoren_boss_panel4 = {             --副本多人模型数据
		position = Vector3(-0.1, 2, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	fuben_duoren_boss_panel5 = {             --副本多人模型数据
		position = Vector3(-0.1, 1.4, 5.2),
		rotation = Vector3(0, 180, 0),
	},
	fuben_duoren_boss_panel6 = {             --副本多人模型数据
		position = Vector3(-0.1, 2.4, 8.2),
		rotation = Vector3(0, 180, 0),
	},

	fuben_zhushenta_panel1 = {             --诛神塔模型数据 1 ~ 50
		position = Vector3(-0.1, 2.1, 4.4),
		rotation = Vector3(0, 180, 0),
	},
	team_my_team_member = {
		position = Vector3(0, -1.5, 0),
		rotation = Quaternion.Euler(0, 170, 0),
		scale = Vector3(145, 145, 145),
	},
	marry_jiehun_role = {
		position = Vector3(-1.6, -1.5 + 0.3, 5.8),
		rotation = Quaternion.Euler(0, 155, 0),
	},
	marry_jiehun_lover = {
		position = Vector3(2.1, -1.5 + 0.3, 5.8),
		rotation = Quaternion.Euler(0, -160, 0),
	},
	--女人旁边站孩子
	marry_role_baby_lover_0 = {
		position = Vector3(-0.565, -0.84, 4.12),
		rotation = Quaternion.Euler(0, -160, 0),
	},
	--男人旁边站孩子
	marry_role_baby_lover_1 = {
		position = Vector3(-0.43, -0.84, 4.12),
		rotation = Quaternion.Euler(0, -160, 0),
	},

	marry_lover_baby_lover_1 = {
		position = Vector3(0.87, -0.84, 4.12),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	marry_lover_baby_lover_0 = {
		position = Vector3(1.02, -0.84, 4.12),
		rotation = Quaternion.Euler(0, 180, 0),
	},


	marry_notce_baby_boy = {
		position = Vector3(-0.73, -0.5, 5.21),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	marry_notce_baby_girl = {
		position = Vector3(0, -0.5, 5.21),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	marry_pw_role = {
		position = Vector3(-1.8, -1.6, 6.8),
		rotation = Quaternion.Euler(0, 155, 0),
	},
	marry_pw_lover = {
		position = Vector3(0.3, -1.6, 6.8),
		rotation = Quaternion.Euler(0, 180, 0),
	},

	marry_baby_panel = {             --仙侣宝宝模型位置 徒弟2
		position = Vector3(-0.38, -0.56, 3.44),
		rotation = Quaternion.Euler(0, 170, 0),
	},
	marry_baoxia_panel = {			 --仙侣宝匣面板模型位置
		position = Vector3(-2.21, -0.9, 8.2),
		rotation = Quaternion.Euler(0, 200, 0),
	},
	first_recharge_role_panel = {			--首充角色模型
		position = Vector3(0, 1.5, 5.5),
		rotation = Quaternion.Euler(0, 160, 0),
	},
	first_recharge_mount_panel = {			--首充坐骑模型
		position = Vector3(0, 1.8, 6.8),
		rotation = Quaternion.Euler(0, 180, 0),
	},
	first_recharge_baoju_panel = {			--首充法宝模型
		position = Vector3(0, 0.5, 7),
		rotation = Quaternion.Euler(0, 180, 0),
	},
	first_recharge_goddess_panel = {		--首充灵宠模型
		position = Vector3(0, 1.5, 5.5),
		rotation = Quaternion.Euler(0, 180, 0),
	},
	npc_dialog_panel = {		--npc对话面板
		position = Vector3(0, 2.5, 5),
		rotation = Vector3(0, 180, 0),
	},
	shop_tip_fashion_panel = {		--商店tip 时装
		position = Vector3(0, 1.3, 5.6),
		rotation = Vector3(0, 180, 0),
	},
	shop_tip_halo_panel = {		--商店tip 光环
		position = Vector3(0, 0, 5.6),
		rotation = Vector3(0, 180, 0),
	},
	shop_tip_cur_role_panel = {		--商店tip 角色
		position = Vector3(0, 1.3, 5.6),
		rotation = Vector3(0, 180, 0),
	},
	shop_tip_foot_panel = {		--商店tip 足迹
		position = Vector3(-0.2, 0.8, 5.6),
		rotation = Vector3(0, 180, 0),
	},
	shop_tip_lingchong_panel = {		--商店tip 宝宝
		position = Vector3(0, 0.8, 4),
		rotation = Vector3(0, 180, 0),
	},
	shop_tip_cur_role_panel2 = {		--商店tip 角色
		position = Vector3(0,1.74,6.8),
		rotation = Vector3(0, 180, 0),
	},

	--恭喜获得某某某：坐骑，羽翼，法宝
	appearance_get_lingchong_view_panel = {				-- 灵宠
		position = Vector3(0, -0.9, 4.4),
		rotation = Quaternion.Euler(0, -180, 0),
	},

	appearance_get_baby_view_panel = {					-- 宝宝
		position = Vector3(0.06, -0.7, 3.5),
	},

	appearance_get_fashion_mount_view_panel = {			-- 幻装坐骑
		position = Vector3(0, -1, 9),
		rotation = Quaternion.Euler(0, -250, 0),
	},

	appearance_get_mount_view_panel = {					-- 进阶坐骑
		position = Vector3(0, -1.69, 0),
		rotation = Quaternion.Euler(0, -138, 0),
		scale = Vector3(138, 138, 138)
	},
	appearance_get_fight_mount_view_panel = {			-- 幻装战骑
		position = Vector3(0, -1, 9),
		rotation = Quaternion.Euler(0, -250, 0),
	},

	appearance_get_wing_view_panel = {					-- 羽翼 8100 2210 8007 2213 8067 8009 2215 8116
		position = Vector3(0, -0.7, 6),
	},

	appearance_get_fabao_view_panel = {					-- 法宝
		position = Vector3(0, -1.1, 5.2),
	},

	appearance_get_fabaonormal_view_panel = {					-- 法宝
		position = Vector3(0, -0.8, 5.5),
	},

	appearance_get_halo_view_panel = {					-- 光环
		position = Vector3(0, 0.3, 6),
	},

	appearance_get_foot_view_panel = {					-- 足迹
		position = Vector3(0, -1.2, 6),
		rotation = Quaternion.Euler(0, -143, 0)
	},

	appearance_get_linggong_view_panel = {				-- 灵弓
		position = Vector3(0.2, -0.8, 3.5),
	},

	appearance_get_fashion_view_panel = {				-- 时装
		position = Vector3(0, -1.3, 6.5),
	},

	appearance_get_weapon_view_panel = {			-- 武器/神兵
		position = Vector3(0, -0.9, 6),
		rotation = Quaternion.Euler(109, 119, 0),
	},

	display_item_tip_xiaogui_panel = {			-- 武器/神兵
		position = Vector3(1, 1.1, 5),
		rotation = Vector3(0, 180, 0),
	},
	display_field3v3_player_panel = {			-- 3v3角色面板
		position = Vector3(-0.5, 1.5, 8),
		rotation = Vector3(0, 180, 0),
	},
	husong_meinv_panel4 = {			-- 护送 美女4
		position = Vector3(0, 2, 5.61),
		rotation = Vector3(0, 180, 0),
	},
	display_trailer_monster_panel = {		-- 功能预告怪物
		position = Vector3(0, 3.5, 13.26),
		--position = Vector3(0,2.9, 14.68),
		rotation = Vector3(0, 180, 0),
	},
	fun_shenbing_panel = { 						--神兵
		position = Vector3(-0.5, 1.5, 5.5),
		rotation = Vector3(0, 180, 0),
	},
	display_trailer_mount_panel = {			-- 功能预告坐骑
		position = Vector3(-0.38, 3.1, 10),
		rotation = Vector3(0, 180, 0),
	},
	display_trailer_wing_panel = {			-- 功能预告羽翼
		position = Vector3(-0.08, 4.13, 6.4),
		rotation = Vector3(0, 180, 0),
	},
	display_field_player_panel1 = {			-- 竞技场角色面板
		position = Vector3(-1.7, -1, 4.5),
		rotation = Vector3(0, 160, 0),
	},
	display_field_player_panel2 = {			-- 竞技场角色面板
		position = Vector3(0.1, 2, 6),
		rotation = Vector3(0, 180, 0),
	},
	display_field_player_panel3 = {			-- 竞技场角色面板
		position = Vector3(0.1, 2, 6),
		rotation = Vector3(0, 180, 0),
	},
	-- display_xunbao_weapon_panel = {			-- 寻宝面板武器
	-- 	position = Vector3(5.4, 0.02, 1.1),
	-- 	rotation = Vector3(0, -90, 120),
	-- },
	display_xunbao_weapon_panel = {			-- 寻宝面板武器
		position = Vector3(0, 3, 0.66),
		rotation = Vector3(90, 0, 220),
	},
	transfer_fabao_view_panel = {				-- 角色法宝面板
		position = Vector3(-1, 0.28, 5.88),
		rotation = Vector3(0, 180, 0),
	},

	guard_time_out_panel1 = {				-- 低级经验小鬼
		position = Vector3(0, 1.2, 5),
		rotation = Vector3(0, 180, 0),
		-- scale = Vector3(200,200,200),

	},
	guard_time_out_panel2 = {				-- 低级守护小鬼
		position = Vector3(0, 0.45, 4),
		rotation = Vector3(0, 180, 0),
		-- scale = Vector3(300,300,300),
	},
	guard_time_out_panel3 = {				-- 中级经验小鬼
		position = Vector3(0, 0.8, 4),
		rotation = Vector3(0, 180, 0),
		-- scale = Vector3(300,300,300),
	},
	guard_time_out_panel4 = {				-- 中级守护小鬼
		position = Vector3(0, 1.2, 4),
		rotation = Vector3(0, 180, 0),
		-- scale = Vector3(300,300,300),
	},

	guard_time_out_panel5 = {				-- 高级经验小鬼
		position = Vector3(0, 0.8, 4),
		rotation = Vector3(0, 180, 0),
		-- scale = Vector3(300,300,300),
	},

	guard_time_out_panel6 = {				-- 高级守护小鬼
		position = Vector3(0, 0.8, 4),
		rotation = Vector3(0, 180, 0),
		-- scale = Vector3(300,300,300),
	},

	bag_guard_time_out_panel1 = {				-- 背包低级经验小鬼
		position = Vector3(0.4, -1.5, 0),
		rotation = Vector3(0, 160, 0),
		scale = Vector3(200,200,200),

	},
	bag_guard_time_out_panel2 = {				-- 背包低级守护小鬼
		position = Vector3(0.2, -0.4, 4),
		rotation = Vector3(0, 180, 0),
		scale = Vector3(280,280,280),
	},
	bag_guard_time_out_panel3 = {				-- 背包中级经验小鬼
		position = Vector3(0.4, -0.8, 0),
		rotation = Vector3(0, 160, 0),
		scale = Vector3(260,260,260),
	},
	bag_guard_time_out_panel4 = {				-- 背包中级守护小鬼
		position = Vector3(0.3, -1.4, 0),
		rotation = Vector3(0, 160, 0),
		scale = Vector3(230,230,230),
	},

	bag_guard_time_out_panel5 = {				-- 背包高级经验小鬼
		position = Vector3(0.2, -1.5, 0),
		rotation = Vector3(0, 165, 0),
		scale = Vector3(180,180,180),
	},

	bag_guard_time_out_panel6 = {				-- 背包高级守护小鬼
		position = Vector3(0.2, -1, 0),
		rotation = Vector3(0, 160, 0),
		scale = Vector3(180,180,180),
	},


	other_sheng_qi_model1 = {						-- 圣器模型1
		position = Vector3(1.3, 3.3, 6),
		rotation = Vector3(0, 180, 0),
	},
	other_sheng_qi_model2 = {						-- 圣器模型2
		position = Vector3(1.8, 1, 4.7),
		rotation = Vector3(0, 197, 0),
	},
	other_sheng_qi_model3 = {						-- 圣器模型3
		position = Vector3(-9.9, 1, 5.4),
		rotation = Vector3(0, 180, 0),
	},
	sheng_qi_model1 = {						-- 圣器模型1
		position = Vector3(-0.5, -1.5, 7),
		rotation = Vector3(0, 180, 0),
	},
	sheng_qi_model2 = {						-- 圣器模型2
		position = Vector3(-0.3, -1.3, 5.7),
		rotation = Vector3(0, 197, 0),
	},
	sheng_qi_model3 = {						-- 圣器模型3
		position = Vector3(-0.5, -1.3, 6.4),
		rotation = Vector3(0, 180, 0),
	},

	tian_bing_model = {						-- 天兵模型
		position = Vector3(0, 0.5, 1.5),
		--rotation = Vector3(90, 0, -216),
	},
	tian_bing_model1 = {						-- 天兵模型
		position = Vector3(0, 0.5, 2.2),
		--rotation = Vector3(90, 0, -216),
	},
	tian_bing_model2 = {						-- 天兵模型
		position = Vector3(0, 0.3, 1.5),
		--rotation = Vector3(90, 0, -216),
	},
	role_title_panel = {
		position = Vector3(0, -1.49, 6),
		rotation = Vector3(0, 180, 0),
	},

	normal_guide_panel = {
		position = Vector3(0, 0.36, 2.34),
		rotation = Vector3(0, 180, 0),
	},
	guild_boss_view = {							--帮派boss
		position = Vector3(0.62, 2.78, 13.2),
		rotation = Vector3(0, 180, 0),
	},
	shenshou_bag_view = {						--帮派boss
		position = Vector3(0, 3.4, 14.3),
		rotation = Vector3(0, 180, 0),
	},
	first_recharge_weapon_panel = {						--首充武器
		position = Vector3(1.66, -0.89, 4.64),
		rotation = Quaternion.Euler(78, -55, 100),
	},
	--first_recharge_weapon_panel_tip = {						--首充武器Tip
	--	position = Vector3(-0.05,-5.5, -0.79),
	--	rotation = Vector3(-91.62, -30.91, 3.635),
	--},
	first_recharge_weapon_panel_tip = {						--首充武器Tip
		position = Vector3(0.005, -1.46, 0),
		rotation = Vector3(0, -182.6, 0),
		scale = Vector3(150, 150, 150)
	},

	first_recharge_weapon_panel_tip_2 = {						--首充兔子Tip
		position = Vector3(-0.01, -1.19, 0),
		rotation = Vector3(0, 180, 0),
		scale = Vector3(180, 180, 180)
	},

	first_recharge_mount_panel_tip = {						--首充武器Tip
		position = Vector3(-0.26, -1.8, 0),
		rotation = Vector3(8.2, -222.85, 0),
		scale = Vector3(70, 70, 70)
	},


    first_recharge_pre_show = {						--首充武器Tip
        position = Vector3(-0.05,-9, -0.39),
        rotation = Vector3(-89.542, -77.5, 3.635),
    },

	profess_rank_male_view = {						--表白排行榜
		position = Vector3(-1.84, -1.21, 6.52),
		rotation = Quaternion.Euler(0, 170, 0),
    },
        --七天狂欢护甲
    Welfare_kuanghuan_hujia = {
    	position = Vector3(0.02,0.1, 2.67),
    	 rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_longchong = {
    	position = Vector3(0.2,1,4.28),
    	 rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_zuoqi = {
    	position = Vector3(0,1,7.1),
    	 rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_zhuanshi = {
    	position = Vector3(0, 0.19, 3),
    	 rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_fabao = {
    	position = Vector3(0.1, 1, 4.6),
    	 rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_shenbing = {
    	position = Vector3(0.4, 1, 4.8),
    	 rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_shizhuang = {
    	position = Vector3(0.1, 1.36, 5.62),
    	rotation = Vector3(0, 180, 0),
	},

	Welfare_kuanghuan_wing = {
    	position = Vector3(0.07, 1.14, 4.84),
    	rotation = Vector3(0, 180, 0),
	},

	shen_shou_display = {
    	position = Vector3(0, -2, 10),
    	 rotation = Vector3(0, 125, 0),
	},
	change_prof_display = {
    	position = Vector3(0, -1.3, 6.8),
    	 rotation = Vector3(0, -125, 0),
	},
	huanlezadan_quanfu_display_mount = {
		position = Vector3(5.46, 2.49, 8.21),
    	rotation = Vector3(0, -142, 0),
	},
	huanlezadan_quanfu_display_fashion_body = {
		position = Vector3(0, 1.2, 4.4),
		rotation = Vector3(0, 180, 0),
	},
	huanlezadan_quanfu_display_baby = {
		position = Vector3(0, 0.8, 2.6),
		rotation = Vector3(0, 180, 0),
	},
	open_server_war = {							-- Boss面板
		position = Vector3(0.9, -3.9, 20),
		rotation = Quaternion.Euler(0, 145, 0),
	},

	guild_shouhu_boss = {
		position = Vector3(2.04, -2.76, 20),
		rotation = Quaternion.Euler(0, -180, 0),
		scale = Vector3(100, 100, 100)
	},

	god_xunbao = {
		position = Vector3(0.51, -1.23, 0),
		rotation = Quaternion.Euler(0, 138, 0),
		scale = Vector3(110, 110, 110)
	},

    tianshen_fb_boss = {
		position = Vector3(1, -1.65, 0),
		rotation = Quaternion.Euler(0, -180, 0),
		scale = Vector3(100, 100, 100)
	},

	fun_shenbing_panel_1 = { 						--神兵
        position = Vector3(-0.5, 0.76, 3.43),
		-- rotation = Vector3(-90, 46, 3.634),
		rotation = Vector3(0, 180, 0),
	},
	display_trailer_mount_panel_1 = {			-- 功能预告坐骑
		position = Vector3(-1, 2.5, 10),
		rotation = Vector3(0, 180, 0),
	},

	display_trailer_wing_panel_1 = {			-- 功能预告羽翼
		position = Vector3(-0.08, 3.08, 6.4),
		rotation = Vector3(0, 180, 0),
	},

	fun_fabao_panel_1 = {
		position = Vector3(3.77, 1.1, 3.1),
		rotation = Vector3(0, -130, 0),
	},
	display_trailer_monster_panel_1 = {		-- 功能预告怪物
		position = Vector3(0, 2, 9),
		rotation = Vector3(0, 180, 0),
	},
	fun_item_panel_1 = {
		position = Vector3(3.77, 0.7, 3.1),
		rotation = Vector3(0, -130, 0),
	},
	fun_feichong_panel_1 = {
		position = Vector3(0.04, 0.6, 1.8),
		rotation = Vector3(0, 180, 0),
	},
	fun_qilingbi_panel_1 = {
		position = Vector3(0.1, 1.03, 3.1),
		rotation = Vector3(0, 180, 0),
	},
	fun_shenshou_panel_1 = {
		position = Vector3(0.22, 3.43, 12.73),
		rotation = Vector3(0, 180, 0),
	},
	fun_tianshen_shenqi_panel_1 = {
		position = Vector3(-0.69, 1.47, 5.24),
		rotation = Vector3(0, 180, 0),
	},
	fun_huakun_panel_1 = {
		position = Vector3(0.09, 1.97, 11.22),
		rotation = Vector3(0, 180, 0),
	},

	fun_jianling_panel_1 = {
		position = Vector3(-0.22, 0.47, 7),
		rotation = Vector3(0, 180, 0),
	},
	fun_sg_shenling_panel_1 = {
		position = Vector3(-0.25, 2.31, 10.6),
		rotation = Vector3(0, 180, 0),
	},
	fun_tianshen_panel_1 = {
		position = Vector3(-0.12, 1.8, 7.4),
		rotation = Vector3(0, 180, 0),
	},
	fun_tianjipu_panel_1 = {
		position = Vector3(0, 0.28, 11.54),
		rotation = Vector3(0, 180, 0),
	},
	fun_zhuanzhi1_panel_1 = {
		position = Vector3(-0.05, 1.26, 4.28),
		rotation = Vector3(0, 180, 0),
	},
	fun_zhuanzhi2_panel_1 = {
		position = Vector3(-0.05, 1.26, 4.28),
		rotation = Vector3(0, 180, 0),
	},

	fun_marry_left_panel_1 = { 						--开启预告 仙侣-左
		position = Vector3(0, 1.7, 7),
		rotation = Vector3(0, 180, 0),
	},

	appearance_get_new_tianshen_view = {			-- 幻装天神
		position = Vector3(0, -1, 9),
		rotation = Quaternion.Euler(0, -250, 0),
	},

	appearance_get_new_shouhu_view = {				-- 守护小鬼
		position = Vector3(0, -1.5, 0),
		rotation = Quaternion.Euler(0, -195, 0),
		scale = Vector3(200,200,200),
	},

	xc_display_panel_3 = { 						-- 续充
		position = Vector3(0.07, -1.75, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(120, 120, 120)
	},

	xc_display_panel_2 = { 						-- 续充
		position = Vector3(-0.24, -1, 0),
		rotation = Quaternion.Euler(67.85, 289.68, 0),
		scale = Vector3(170, 170, 170)
	},

	xc_display_panel_1 = { 						-- 续充
		position = Vector3(-0.14,-1.47, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(180, 180, 180)
	},


	vip_display_panel_baozhu_1 = { 						-- 新手宝珠
		position = Vector3(0, -1.28, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(80, 80, 80)
	},

	vip_display_panel_baozhu_2 = { 						-- 普通宝珠
		position = Vector3(0, -2, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(90, 90, 90)
	},

	vip_display_tip_baozhu = { 						-- 提示面板宝珠
		position = Vector3(0, -2, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(160, 160, 160)
	},

	vip_display_tip_tiyan_baozhu = { 						-- 体验提示面板宝珠
		position = Vector3(-0.38, -2, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(70, 70, 70)
	},


	sc_display_panel_moon_1 = { 						-- 首充月亮 男
		position = Vector3(0, -1.64, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(150, 150, 150)
	},

	sc_display_panel_role_1 = { 						-- 首充人物模型 男
		position = Vector3(0, -1.64, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(180, 180, 180)
	},

	sc_display_panel_moon_3 = { 						-- 首充月亮 女
		position = Vector3(0, -1.8, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(150, 150, 150)
	},

	sc_display_panel_role_3 = { 						-- 首充人物模型 女
		position = Vector3(0, -1.64, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(180, 180, 180)
	},

	husong_view_model_1 = { 							-- 护送模型一
		position = Vector3(0, -1.25, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(150, 150, 150)
	},

	husong_view_model_2 = { 							-- 护送模型二
		position = Vector3(0.06, -1.23, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(150, 150, 150)
	},

	husong_view_model_3 = { 							-- 护送模型三
		position = Vector3(0, -1.5, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(150, 150, 150)
	},

	husong_view_model_4 = { 							-- 护送模型四
		position = Vector3(-0.2, -1.79, 0),
		rotation = Quaternion.Euler(0, 180, 0),
		scale = Vector3(150, 150, 150)
 	},

}