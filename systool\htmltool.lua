HtmlTool = HtmlTool or {}

function HtmlTool.GetHtml(content, color, size)
	local html_str = content or ""
	local color_str = nil
	if color ~= nil then
		if type(color) == "table" then
			color_str = string.format("#%02x%02x%02x", color.r, color.g, color.b)
		else
			color_str = color
		end

		html_str = string.format(Language.Common.ColorStr, color_str, html_str)
	end

	if size ~= nil then
		html_str = string.format(Language.Common.SizeStr, tonumber(size), html_str)
	end

	return html_str
end

function HtmlTool.BlankReplace(content)
	local str = content or ""
	if IS_IOS_OR_ANDROID then
		str = string.gsub(str, " ", "  ")
	end
	return str
end

function HtmlTool.RemoveHtml(content)
    if content then
        content = string.gsub(content, "<.->", "")
    end
    return content
end