require("game/new_festival_activity/new_festival_recharge/new_festival_recharge_wg_data")
require("game/new_festival_activity/new_festival_recharge/new_festival_recharge_view")

NewFestivalRechargeWGCtrl = NewFestivalRechargeWGCtrl or BaseClass(BaseWGCtrl)

function NewFestivalRechargeWGCtrl:__init()
    if NewFestivalRechargeWGCtrl.Instance then
        error("[NewFestivalRechargeWGCtrl]:Attempt to create singleton twice!")
    end
    NewFestivalRechargeWGCtrl.Instance = self

    self.data = NewFestivalRechargeWGData.New()
    self:RegisterAllProtocols()
end

function NewFestivalRechargeWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    NewFestivalRechargeWGCtrl.Instance = nil
end

function NewFestivalRechargeWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCNewFestivalActRecharge, "OnSCNewFestivalActRecharge")
end

function NewFestivalRechargeWGCtrl:OnSCNewFestivalActRecharge(protocol)
    self.data:SetRechargeAllInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2351)
    RemindManager.Instance:Fire(RemindName.NewFestivalRecharge)
end
