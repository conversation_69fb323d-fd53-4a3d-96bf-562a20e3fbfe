BiZuoView = BiZuoView or BaseClass(SafeBaseView)
local CellCount = 5		-- 本地个数

local GUILD_ACTIVITY = {
	[ACTIVITY_TYPE.XIANMENGZHAN] = 7,
	[ACTIVITY_TYPE.GUILD_CHUAN_GONG] = 36,
	[ACTIVITY_TYPE.GUILD_ANSWER] = 28,
	[ACTIVITY_TYPE.GUILD_FB] = 27,
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS] = 37,
}
function BiZuoView:InitActivityHallView()
	if not self.hall_list_view then
		self.hall_list_view = AsyncBaseGrid.New()
		self.hall_list_view:CreateCells({col = 2,change_cells_num = 1, list_view = self.node_list["ph_item_list"],
		assetBundle = "uis/view/bizuo_ui_prefab", assetName = "ph_item_render",  itemRender = ActIvityHallBaseRender})
		self.hall_list_view:SetStartZeroIndex(false)
		--self.hall_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectActivityItemHandler, self))
	end

	self:FlushListDataSource()
end

function BiZuoView:OnSelectActivityItemHandler(item)
	BiZuoWGCtrl.Instance.activity_desc_view:SetData(item)
	BiZuoWGCtrl.Instance.activity_desc_view:Open()
end

function BiZuoView:DeleteActivityHallView()
	if self.hall_list_view ~= nil then
		self.hall_list_view:DeleteMe()
		self.hall_list_view = nil
	end
end

-- 刷列表数据源
function BiZuoView:FlushListDataSource()
	BiZuoWGData.Instance:SetActivityHallCfg()
	local data_list = BiZuoWGData.Instance:GetActivityHallAllInfo()

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local timer = os.date("%X", server_time)

	if nil ~= self.hall_list_view then
		self.hall_list_view:SetDataList(data_list, 2)
	end
end


---------------------------
---------------------------
ActIvityHallBaseRender = ActIvityHallBaseRender or BaseClass(BaseGridRender)

function ActIvityHallBaseRender:LoadCallBack()
	self.node_list.go_btn.button:AddClickListener(BindTool.Bind(self.OnClickGoBtn, self))
	self.node_list.btn_item.button:AddClickListener(BindTool.Bind(self.OnClickActIcon, self))
	--self.node_list.signup_btn.button:AddClickListener(BindTool.Bind(self.OnClickSignupBtn,self)) --报名按钮
	if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(false)
    end
end

function ActIvityHallBaseRender:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function ActIvityHallBaseRender:OnFlush()
	if nil == self.data or IsEmptyTable(self.data) then return end

	local day, time = BiZuoWGData.Instance:GetActOpenDesc(self.data)
	self.node_list.act_day.text.text = day
	self.node_list.act_time.text.text = time
	--self.node_list.signup_time.text.text = string.format(Language.BiZuo.Signup_Time_Segment, self.data.signup_begin_time, self.data.signup_end_time)

	self.node_list.act_name.text.text = self.data.name

	-- 是否参与过该活动
	local is_participation = BiZuoWGData.Instance:GetIsParticipationBySeq(self.data.act_seq)
	local complete_times = is_participation and 1 or 0
	if not self.data.dailywork_cfg then
		print_error("日常--任务表取不到配置,活动类型:", self.data.act_type)
		return
	end
	-- if self.data.dailywork_cfg then
	-- 	self.node_list.desc.text.text = string.format(self.data.output,self.data.dailywork_cfg.exp_per_times)
	-- else
	-- 	self.node_list.desc.text.text = ""
	-- end
	if self.data.dailywork_cfg.tips_icon ~= "" and self.data.dailywork_cfg.tips_icon > 0 then
		self.node_list.reward_type.image:LoadSprite(ResPath.GetDailyTypeIcon("a3_rc_biaoqian" .. self.data.dailywork_cfg.tips_icon))
		self.node_list.tips_icon_text.text.text = self.data.dailywork_cfg.tips_icon_text
		self.node_list.reward_type:SetActive(true)
	else
		self.node_list.reward_type:SetActive(false)
	end

	self.node_list.xiuwei_text.text.text = self.data.dailywork_cfg.xiuwei_per_times or "0"

	local times_des = ""
	local HuSongActType = 3 -- 护送活动号
	local max_times = self.data.dailywork_cfg.complete_max_times - self.data.dailywork_cfg.buy_times
	if self.data.dailywork_cfg.act_type == HuSongActType then
		complete_times = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT) --护送完成次数
	end
	if self.data.dailywork_cfg.complete_max_times >= 9999 then
		times_des = Language.BiZuo.WuXian
	else
		times_des = string.format(Language.BiZuo.CompleteTime2, max_times - complete_times, max_times)
	end
	self.node_list.time_num.text.text = times_des

	local max_huoyuedu = self.data.dailywork_cfg.exp_per_times * (max_times)
	if max_huoyuedu > 0 then
		--self.node_list.huoyuedu.text.text = string.format(Language.BiZuo.Exp, COLOR3B.GREEN, self.data.dailywork_cfg.exp_per_times * complete_times, max_huoyuedu) or ""
		self.node_list.huoyuedu.text.text = self.data.dailywork_cfg.exp_per_times
	else
		self.node_list.huoyuedu.text.text = ""
	end

	self.node_list.go_btn:SetActive(false)
	--self.node_list.signup_time:SetActive(false)
	self.node_list.open_level:SetActive(false)
	--self.node_list.signup_btn:SetActive(false)
	self.node_list.finish_flag:SetActive(false)
	self.node_list.complete_bg:SetActive(false)
	self.node_list.normal_bg:SetActive(true)
	self.node_list.end_img:SetActive(false)
	self.node_list.act_time_content:SetActive(true)
	-- XUI.SetGraphicGrey(self.node_list.act_icon_bg, false)
	-- XUI.SetGraphicGrey(self.node_list.bg, false)
	if 1 == self.data.level_limit then											-- 未开放（等级达不到限制等级）
		self.node_list.act_time_content:SetActive(false)
		self.node_list.open_level:SetActive(true)
		self.node_list.open_level.text.text = string.format(Language.BiZuo.LevelOpen, RoleWGData.GetLevelString(self.data.level))
		-- XUI.SetGraphicGrey(self.node_list.bg, true)
		-- XUI.SetGraphicGrey(self.node_list.act_icon_bg, false)
	-- 护送任务 护送次数已用完
	elseif 1 == self.data.is_husong_count_finish then		--已完成
		self.node_list.finish_flag:SetActive(true)
		self.node_list.act_time_content:SetActive(false)
		self.node_list.complete_bg:SetActive(true)
		self.node_list.normal_bg:SetActive(false)
		--self.node_list.flag_desc.text.text = Language.BiZuo.YiWanCheng
		-- XUI.SetGraphicGrey(self.node_list.bg, true)
		-- XUI.SetGraphicGrey(self.node_list.act_icon_bg, true)
	elseif 1 == self.data.is_open and 0 == self.data.is_act_open_day then    -- 开启中
		self.node_list.go_btn:SetActive(true)
		self.node_list.act_time_content:SetActive(false)
	-- elseif 0 == self.data.is_act_open_day and 0 == self.data.is_open and 0 == self.data.is_overdue and not is_sign_up then   -- 未开启--未报名
	-- 	if self.data.is_can_signup == 1 then 				--处于可报名时间段内
	-- 		self.node_list.signup_btn:SetActive(true) --报名按钮显示
	-- 	else
	-- 		self.node_list.signup_time:SetActive(true)
	-- 	end
	-- 	self.node_list.act_time:SetActive(true)
	-- elseif 0 == self.data.is_act_open_day and 0 == self.data.is_open and 0 == self.data.is_overdue and is_sign_up then		 -- 未开启--已报名
	-- 	self.node_list.finish_flag:SetActive(true) --已报名
	-- 	-- 已报名图标
	-- 	self.node_list.flag_desc.text.text = Language.BiZuo.YiBaoMing
	-- 	self.node_list.act_time:SetActive(true)
	elseif 0 == self.data.is_act_open_day and 1 == self.data.is_overdue and not is_participation then   -- 已结束--未参与
		self.node_list.end_img:SetActive(true)
		self.node_list.act_time_content:SetActive(false)
		-- XUI.SetGraphicGrey(self.node_list.bg, true)
		-- XUI.SetGraphicGrey(self.node_list.act_icon_bg, true)
		--已结束图标
	elseif 0 == self.data.is_act_open_day and 1 == self.data.is_overdue and is_participation then		-- 已结束--参与
		self.node_list.finish_flag:SetActive(true)
		self.node_list.act_time_content:SetActive(false)
		self.node_list.complete_bg:SetActive(true)
		self.node_list.normal_bg:SetActive(false)
		--self.node_list.flag_desc.text.text = Language.BiZuo.YiWanCheng
		-- XUI.SetGraphicGrey(self.node_list.bg, true)
		-- XUI.SetGraphicGrey(self.node_list.act_icon_bg, true)
		-- 已完成图标
	-- elseif 1 == self.data.is_act_open_day then -- 不在开放天数
	-- 	self.node_list.act_time:SetActive(true)
		-- XUI.SetGraphicGrey(self.node_list.bg, true)
		-- XUI.SetGraphicGrey(self.node_list.act_icon_bg, true)
	end
	-- XUI.SetGraphicGrey(self.node_list.finish_flag, false)
	local reward_list_data = SortDataByItemColor(self.data.reward_item)
	self.reward_list:SetDataList(reward_list_data)
end

function ActIvityHallBaseRender:OnClickGoBtn()
	if nil == self.data or not next(self.data) then
		return
	end

	if self.data.is_open == 1 then
		ViewManager.Instance:CloseAll()
		ActIvityHallWGCtrl.Instance:OpenActivity(self.data.act_type)
	else
		BiZuoWGCtrl.Instance.activity_desc_view:SetData(self.data)
		BiZuoWGCtrl.Instance.activity_desc_view:Open()
	end
end

function ActIvityHallBaseRender:OnClickActIcon()
	BiZuoWGCtrl.Instance.activity_desc_view:SetData(self.data)
	BiZuoWGCtrl.Instance.activity_desc_view:Open()
end

function ActIvityHallBaseRender:OnClickSignupBtn()
	local signup_cfg = BiZuoWGData.Instance:GetSignupConfigByActType(self.data.act_type)
	local vo = RoleWGData.Instance:GetRoleVo()
	if GUILD_ACTIVITY[self.data.act_type] and vo then
		local guild_id = vo.guild_id
		local guild_post = vo.guild_post
		if guild_id <= 0 or guild_post <= 0 then
			ViewManager.Instance:Close(GuideModuleName.BiZuo)
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild)
			TipWGCtrl:ShowSystemMsg(Language.Common.CanNotEnterNoGuild)
			return
		end
	end
	BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_SIGNUP,0,0,0,signup_cfg.seq)
end
