
function RoleWGCtrl:ChangeRolesRegisterAllProtocols()
	self:RegisterProtocol(SCChangeProfResult, "OnSCChangeProfResult")
	self:RegisterProtocol(SCRoleProfOtherInfo, "SCRoleProfOtherInfo")
	self:RegisterProtocol(CSChangeProfReq)
	self:RegisterProtocol(CSSetZhuanzhiAppearance)
end

function RoleWGCtrl:SCRoleProfOtherInfo(protocol)
	self.change_roles_data:SetChangeRoleInfo(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.RoleView, TabIndex.role_change)

	if self.update_equip_compose_remind then
		RemindManager.Instance:Fire(RemindName.Compose_Male_Equip)
		RemindManager.Instance:Fire(RemindName.Compose_Female_Equip)
		self.update_equip_compose_remind = nil
	end
end

-- 返回更改了职业性别的信息（广播用）
function RoleWGCtrl:OnSCChangeProfResult(protocol)
    -- print_error("---返回更改了职业性别的信息（广播用）-------", protocol)
	local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)
	if not role then
		return
	end

    local role_type = role:GetType()
	if role_type and role_type ~= SceneObjType.Role and role_type ~= SceneObjType.MainRole then
		return
	end

	role:SetAttr("sex", protocol.sex)
	role:SetAttr("prof", protocol.prof)

    if role:GetType() == SceneObjType.MainRole then
		-- 合成红点
		self.update_equip_compose_remind = true
		-- 角色技能
	    RoleWGData.Instance:SetDefaultSkillInfo(true)
		role:ChangeSkillBindEffect()
		SkillWGData.Instance:ResetSkillBreakCfgList()
		-- 装备集成
		EquipTargetWGData.Instance:SetEIAllInfo()
		RemindManager.Instance:Fire(RemindName.EquipmentIntegration)
		-- 形象改变(通过Scene:OnRoleVisibleChange的返回)
		
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.ChangeRoleSuccess)
        ViewManager.Instance:FlushView(GuideModuleName.RoleView, TabIndex.role_change)
		LoginWGData.Instance:SetServerListLevel()
		
		ReportManager:ReportRole(REPORT_ROLE_ACTTION.switchRole)
	end
end

-- 发送请求换角信息
function RoleWGCtrl:ChangeRolesSendChangeProf(prof, sex)
    -- print_error("---发送请求转职信息-------", prof, sex)
	local zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	local protocol = ProtocolPool.Instance:GetProtocol(CSChangeProfReq)
	protocol.prof = (prof or 0) + zhuan * 10
	protocol.sex = sex or -1
	protocol:EncodeAndSend()
end

function RoleWGCtrl:ChangeRolesSendDIYSelect(seq, face_res_id, hair_res_id, body_res_id, default_diy_data)
    --print_error("---发送缓存-------", seq, face_res_id, hair_res_id, body_res_id,"default_diy_data", default_diy_data)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSetZhuanzhiAppearance)
	protocol.seq = seq or -1
	protocol.default_face_res_id = face_res_id or 0
	protocol.default_hair_res_id = hair_res_id or 0
	protocol.default_body_res_id = body_res_id or 0

	protocol.hair_color = default_diy_data.hair_color or {}
	protocol.eye_size = default_diy_data.eye_size or 0
	protocol.eye_position = default_diy_data.eye_position or 0
	protocol.eye_shadow_color = default_diy_data.eye_shadow_color or {}
	protocol.left_pupil_type = default_diy_data.left_pupil_type or 0
	protocol.left_pupil_size = default_diy_data.left_pupil_size or 0
	protocol.left_pupil_color = default_diy_data.left_pupil_color or {}
	protocol.right_pupil_type = default_diy_data.right_pupil_type or 0
	protocol.right_pupil_size = default_diy_data.right_pupil_size or 0
	protocol.right_pupil_color = default_diy_data.right_pupil_color or {}
	protocol.mouth_size = default_diy_data.mouth_size or 0
	protocol.mouth_position = default_diy_data.mouth_position or 0
	protocol.mouth_color = default_diy_data.mouth_color or {}
	protocol.face_decal_id = default_diy_data.face_decal_id or 0
	protocol.preset_seq = default_diy_data.preset_seq or 0
	protocol:EncodeAndSend()
end