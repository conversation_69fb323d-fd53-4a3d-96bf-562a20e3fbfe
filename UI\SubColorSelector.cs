using LuaInterface;
using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class SubColorSelector : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IDragHandler
{
    //显示板
    public Image ColorPlate;
    //最终颜色
    public Color FinalColor;
    //调色板
    public RawImage m_ColorPickPanel;
    //选择图标
    public RectTransform m_SelectorWheel;
    public RectTransform RectTrans;
    [Header("需要添加画布以下所有位置变化的父节点")]
    public RectTransform[] RectRoot;

    private Image m_SelectorWheelImage;
    //记录之前的位置
    Vector3 oldposition;

    //最终颜色
    private LuaFunction SelectColorCB = null;
    private float now_soft_factor_value = 0;
    private Canvas m_canvas = null;
    private CanvasScaler m_canvas_scaler = null;

    void Awake()
    {
        RectTrans = transform as RectTransform;
        oldposition = m_SelectorWheel.position;
        m_SelectorWheelImage = m_SelectorWheel.GetComponent<Image>();
        m_canvas = this.transform.GetComponentInParent<Canvas>();
        m_canvas_scaler = this.transform.GetComponentInParent<CanvasScaler>();
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        OnDrag(eventData);
    }

    public Vector3 GetCanvasFinalPos(Vector3 aim_pos)
    {
        var return_pos = Vector3.zero;
        if (this.m_canvas == null || this.m_canvas_scaler == null)
        {
            Debug.LogError("画布没找到");
            return return_pos;
        }

        var height_radio = this.m_canvas_scaler.referenceResolution.y / Screen.height;
        var whidth_radio = this.m_canvas_scaler.referenceResolution.x / Screen.width;

        if (this.m_canvas_scaler.matchWidthOrHeight == 1)// -- 等高缩放
            return_pos = new Vector3(aim_pos.x * height_radio, aim_pos.y * height_radio, 0);
        else// -- 等宽缩放
            return_pos = new Vector3(aim_pos.x * whidth_radio, aim_pos.y * whidth_radio, 0);

        return return_pos;
    }

    public void OnDrag(PointerEventData eventData)
    {
        //需要将背景调色板的中心点改为左下角（0，0）
        //PointerEventData.position返回的是当前指针的位置，左下角为原点（0,0），右上角（屏幕宽，屏幕高），根据分辨率来算
        // 将左下角原点坐标系转换为中心点为原点的坐标系
        Vector3 center_pos = new Vector3(Screen.width / 2, Screen.height / 2, 0);
        center_pos = GetCanvasFinalPos(center_pos);
        Vector3 input_pos = GetCanvasFinalPos(eventData.position);
        Vector3 mouse_pos = input_pos - center_pos;
        Vector3 final_pos = (Vector3)mouse_pos - RectTrans.localPosition;
        // 计算到画布根节点位置
        for (int i = 0; i < RectRoot.Length; i++)
        {
            RectTransform root = RectRoot[i];
            final_pos -= root.localPosition;
        }
        final_pos.x = Mathf.Clamp(final_pos.x, 0, m_ColorPickPanel.rectTransform.rect.width - 1);
        final_pos.y = Mathf.Clamp(final_pos.y, 0, m_ColorPickPanel.rectTransform.rect.height - 1);
        m_SelectorWheel.transform.localPosition = final_pos;

        //获取调色板图片
        Texture2D tex = (Texture2D)m_ColorPickPanel.texture;
        //获取鼠标（smallIcon）在调色板上的x，y轴上的比例
        float xtemp = m_SelectorWheel.localPosition.x / m_ColorPickPanel.rectTransform.rect.width;
        float ytemp = m_SelectorWheel.localPosition.y / m_ColorPickPanel.rectTransform.rect.height;

        //设置圆圈颜色分类
        if (ytemp <= 0.5f || xtemp >= 0.5f) m_SelectorWheelImage.color = Color.white;
        else m_SelectorWheelImage.color = Color.black;

        //通过鼠标在调色板的位置比例，获取鼠标在调色板上的具体(X,Y)坐标
        int x = (int)(xtemp * tex.width);
        int y = (int)(ytemp * tex.height);

        //通过Texture2D.GetPixel这个方法来获取该位置下的像素的颜色值
        Color color = tex.GetPixel(x, y);
        //设置颜色
        FinalColor = color;

        if (ColorPlate != null)
        {
            ColorPlate.color = FinalColor;
        }

        if (this.SelectColorCB != null)
        {
            string hex_color = ColorUtility.ToHtmlStringRGBA(this.FinalColor);
            this.SelectColorCB.Call(FinalColor, hex_color);
        }
    }

    public void FulshNowPosFinalColor()
    {
        this.UpdateColor();
    }

    //更新轻柔位置信息(Y轴变为当前的倍率)
    public void UpdateSoftLocalPos(float soft_factor_value)
    {
        //Vector3 now_pos = m_SelectorWheel.transform.localPosition;
        //Debug.LogError($"数据{now_soft_factor_value}{soft_factor_value}");

        //if (now_soft_factor_value < soft_factor_value)
        //{
        //    Debug.LogError($"需要改变数据{now_pos}");
        //    now_pos.y *= now_soft_factor_value;
        //}

        //Debug.LogError($"数据2{now_pos}");
        //m_SelectorWheel.transform.localPosition = now_pos;
        //now_soft_factor_value = soft_factor_value;

        UpdateColor();
    }

    /// <summary>
    /// 更新颜色
    /// </summary>
    /// <param name="eventData"></param>
    public void UpdateColor(PointerEventData eventData = null)
    {
        //获取调色板图片
        Texture2D tex = (Texture2D)m_ColorPickPanel.texture;
        float xtemp = m_SelectorWheel.localPosition.x / (m_ColorPickPanel.rectTransform.rect.width + 4);
        float ytemp = m_SelectorWheel.localPosition.y / (m_ColorPickPanel.rectTransform.rect.height + 4);
        int x = (int)(xtemp * tex.width);
        int y = (int)(ytemp * tex.height);

        Color color = tex.GetPixel(x, y);
        FinalColor = color;

        if (ColorPlate != null)
        {
            ColorPlate.color = FinalColor;
        }

        if (this.SelectColorCB != null)
        {
            if (this.FinalColor != null)
            {
                string hex_color = ColorUtility.ToHtmlStringRGBA(this.FinalColor);
                this.SelectColorCB.Call(FinalColor, hex_color);
            }
        }
    }

    public void SetSelectColorCB(LuaFunction callback)
    {
        this.SelectColorCB = callback;
    }

    public Color GetSelectorColor()
    {
        return FinalColor;
    }

    public string GetSelectorHexColor()
    {
        if (this.FinalColor != null)
        {
            string hex_color = ColorUtility.ToHtmlStringRGBA(this.FinalColor);
            return hex_color;
        }

        return "";
    }

    public Vector3 GetSelectColorHSV()
    {
        float H, S, V;
        if (this.FinalColor != null)
        {
            Color.RGBToHSV(this.FinalColor, out H, out S, out V);
            return new Vector3(H, S, V);
        }

        return Vector3.zero;
    }
}