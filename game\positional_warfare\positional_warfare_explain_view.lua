PositionalWarfareExplainView = PositionalWarfareExplainView or BaseClass(SafeBaseView)

function PositionalWarfareExplainView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, 0), sizeDelta = Vector2(814, 598)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_explain_view")
end

function PositionalWarfareExplainView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.left_btn, BindTool.Bind(self.OnClickChangeBtn, self, -1))
    XUI.AddClickEventListener(self.node_list.right_btn, BindTool.Bind(self.OnClickChangeBtn, self, 1))
    self.select_index = 1

    self.node_list.title_view_name.text.text = Language.PositionalWarfare.ExplainViewTitleName
end

function PositionalWarfareExplainView:ReleaseCallBack()
    self.select_index = nil
end

function PositionalWarfareExplainView:OnFlush()
    local bundle, asset = ResPath.GetRawImagesPNG("a3_zdz_sm" .. self.select_index)
    self.node_list.target_rawimage.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list.target_rawimage.raw_image:SetNativeSize()
    end)

    self.node_list.left_btn:CustomSetActive(self.select_index > 1)
    self.node_list.right_btn:CustomSetActive(self.select_index < 5)
end

function PositionalWarfareExplainView:OnClickChangeBtn(index)
    local max_index = 5
    self.select_index = self.select_index + index
    self.select_index = math.min(max_index, self.select_index)
    self.select_index = math.max(1, self.select_index)
    self:Flush()
end