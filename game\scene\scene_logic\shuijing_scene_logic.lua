ShuiJingSceneLogic = ShuiJingSceneLogic or BaseClass(CommonFbLogic)

function ShuiJingSceneLogic:__init()
	
end

function ShuiJingSceneLogic:__delete()
	
end

function ShuiJingSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	-- XuiBaseView.CloseAllView()

	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function ShuiJingSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function ShuiJingSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
end