MostVenerableWGData = MostVenerableWGData or BaseClass()

function MostVenerableWGData:__init()
	if MostVenerableWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[MostVenerableWGData] attempt to create singleton twice!")
		return
	end
	MostVenerableWGData.Instance = self

	self.challengefield_cfg = ConfigManager.Instance:GetAutoConfig("challengefield_auto")
	self.rank_info_list = {}
    self.my_rank_info = {}
end

function MostVenerableWGData:__delete()
	self.rank_list = nil
    MostVenerableWGData.Instance = nil
end

function MostVenerableWGData:SetRankListInfo(protocol)
    local list = {}
    for i = 1, 10 do
        list[i] = protocol.rank_list[i]
    end
	self.rank_info_list = list
    self.my_rank_info = protocol.rank_list[11]
end

function MostVenerableWGData:GetMostVenerableConfig()
    return self.challengefield_cfg.most_venerable[1]
end

function MostVenerableWGData:GetRankList()
    return self.rank_info_list
end

function MostVenerableWGData:GetMyRankInfo()
    return self.my_rank_info
end