GuildMemberItem = GuildMemberItem or BaseClass(BaseRender)

function GuildMemberItem:__init()
	self.name = self.node_list.lbl_name
	self.level = self.node_list.lbl_level
	self.post = self.node_list.lbl_post
	self.zonggongxian = self.node_list.lbl_zonggongxian
	self.status = self.node_list.lbl_status
	self.fly_image = self.node_list.fly_image
	self.role_head_cell = BaseHeadCell.New(self.node_list["img_head"])
end

function GuildMemberItem:CreateChild()
	BaseRender.CreateChild(self)
end

function GuildMemberItem:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

end

function GuildMemberItem:OnFlush()
	if nil == self.data then
		return
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local color_name = ""
	local color_lv = ""
	local color_post = ""
	local color_gongxian = ""
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.fly_image:SetActive(is_vis)

	local post_list = GuildDataConst.GUILD_POST_AUTHORITY_LIST
	self.zonggongxian.text.text = CommonDataManager.ConverExpByThousand(self.data.total_gongxian)
	color_name = self.data.role_name
	color_lv = role_level
	color_post = post_list[self.data.post].post
	color_gongxian = CommonDataManager.ConverExpByThousand(self.data.total_gongxian)
	self.role_head_cell:SetGray(1 ~= self.data.is_online)
	local status = ""
	if 1 == self.data.is_online then --在线
		status = Language.Common.OnLine
	else --离线
		local time_format = TimeWGCtrl.Instance:GetServerTime() - self.data.last_login_time
		status = ToColorStr(GuildWGData.FormatTime(time_format), COLOR3B.GRAY)
	end

	self.status.text.text = status  --在线状态
	self.name.text.text = color_name  --角色名字
	self.level.text.text = color_lv --角色等级
	self.zonggongxian.text.text = color_gongxian --贡献
	if nil ~= post_list[self.data.post] then
		self.post.text.text = color_post
	end

	-- 加载头像
	self:SetRoleHead()
	XUI.SetGraphicGrey(self.node_list["img9_bg"], 0 == self.data.is_online)
end

function GuildMemberItem:MenberRoleBg(roleinfo)

end

function GuildMemberItem:SetRoleHead()
	local data = {}
	data.role_id = self.data.uid
	data.prof = self.data.prof
	data.sex = self.data.sex
	self.role_head_cell:SetData(data)
end

-- function GuildMemberItem:OnSelectChange(is_on)
-- 	if self.node_list.hight then
-- 		self.node_list.hight:SetActive(is_on)
-- 	end
-- end
