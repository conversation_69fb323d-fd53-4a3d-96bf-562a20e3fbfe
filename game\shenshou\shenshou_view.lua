ShenShouView = ShenShouView or BaseClass(SafeBaseView)

function ShenShouView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_shenshou")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.SoulRing})
end

function ShenShouView:OpenCallBack()
	if nil ~= self.money_bar then
		self.money_bar:AddAllListen()
	end
end

function ShenShouView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.node_list.title_view_name.text.text = Language.ShenShou.ShenShouName

	-- local bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.HAVE_ROLEMODEL)
	-- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- end)

	if not self.soul_ring_model then
		self.soul_ring_model = RoleModel.New()
		self.soul_ring_model:ClearCustomDisplayTranfromData()
		-- self.soul_ring_model:SetUISceneModel(self.node_list["soul_ring_model"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self.soul_ring_model:SetUISceneModel(nil, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.soul_ring_model, 0)

		-- local display_data = {
		-- 	parent_node = self.node_list["soul_ring_model"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		
		-- self.soul_ring_model:SetRenderTexUI3DModel(display_data)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		self.soul_ring_model:SetModelResInfo(role_vo, special_status_table)

		local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()
		if other_cfg.model_pos and "" ~= other_cfg.model_pos then
			local pos_list = string.split(other_cfg.model_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetUSAdjustmentNodeLocalPosition(pos_x, pos_y, pos_z)
		end

		if other_cfg.model_rot and "" ~= other_cfg.model_rot then
			local pos_list = string.split(other_cfg.model_rot, "|")
			local x = tonumber(pos_list[1]) or 0
			local y = tonumber(pos_list[2]) or 0
			local z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetUSAdjustmentNodeLocalRotation(x, y, z)
		end

		if other_cfg.model_scale and "" ~= other_cfg.model_scale then
			self.soul_ring_model:SetUSAdjustmentNodeLocalScale(other_cfg.model_scale, other_cfg.model_scale, other_cfg.model_scale)
		end
	end

	if not self.soul_ring_list then
		self.soul_ring_list = AsyncListView.New(SoulRingListItemCellRender, self.node_list.soul_ring_list)
		self.soul_ring_list:SetSelectCallBack(BindTool.Bind(self.SelectSoulRingItemCallBack, self))
		self.soul_ring_list:SetStartZeroIndex(true)
	end

	self.shenshou_cells = {}
	for i = 0, 4 do
		self.shenshou_cells[i] = ShenShouEquipCell.New(self.node_list["soul_ring_equip_" .. i])
		self.shenshou_cells[i]:SetItemTipFrom(ShenShouEquipTip.FROM_SHENSHOU)
		self.shenshou_cells[i]:AddClickEventListener(BindTool.Bind(self.SelectSoulRingEquipItemCallBack, self, self.shenshou_cells[i]))
	end

	if not self.attr_list then
        self.attr_list = {}
        local attr_num = self.node_list.attrlist.transform.childCount
        for i = 1, attr_num do
            local cell = ShenBingBaseRenderNew.New(self.node_list.attrlist:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.attr_list[i] = cell
        end
    end

	if not self.ss_skill_list then
		self.ss_skill_list = {}
		
		for i=1, 8 do
			self.ss_skill_list[i] = ShouSkillItemRender.New(self.node_list["ph_skill_itemrender" .. i])
		end
	end

	self.cur_select_soul_ring_seq = -1
	self.cur_select_soul_ring_data = {}
	self.cur_select_soul_ring_index = -1
	self.soul_ring_model_data_cache = {}

	XUI.AddClickEventListener(self.node_list.btn_overview, BindTool.Bind(self.OnClickOverviewBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_get_equip, BindTool.Bind(self.OnClickBtnOpenEquip, self))
	XUI.AddClickEventListener(self.node_list.btn_discharge, BindTool.Bind(self.OnClickDischargeBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_get_qianghua, BindTool.Bind(self.OnClickBtnOpenQiangHua, self))
	XUI.AddClickEventListener(self.node_list.btn_onekey_wear, BindTool.Bind(self.OnClickOneKeyWearBtn, self))
	XUI.AddClickEventListener(self.node_list.qc_btn_equip, BindTool.Bind(self.OnClickOpenQiChongEquip, self))

	self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
    local remind_list = {
        RemindName.HuaKunEquipTotal,
    }

    for k, v in pairs(remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end
end

function ShenShouView:CloseCallBack()
	if self.soul_ring_model then
		self.soul_ring_model:RemoveAllSoulRing(true)
		self.soul_ring_model:RemoveSoulRingSelectModel(true)
	end

	if nil ~= self.money_bar then
		self.money_bar:UnAllListen()
	end
end

function ShenShouView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.soul_ring_list then
		self.soul_ring_list:DeleteMe()
		self.soul_ring_list = nil
	end

	if self.shenshou_cells then
		for k, v in pairs(self.shenshou_cells) do
			v:DeleteMe()
		end

		self.shenshou_cells = nil
	end

	if self.soul_ring_model then
		self.soul_ring_model:DeleteMe()
		self.soul_ring_model = nil
	end

	if self.attr_list then
        for k,v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

	if self.ss_skill_list then
		for i,v in ipairs(self.ss_skill_list) do
			v:DeleteMe()
		end

		self.ss_skill_list = nil
	end

	if self.delay_show_soul_ring_select_timer then
		GlobalTimerQuest:CancelQuest(self.delay_show_soul_ring_select_timer)
		self.delay_show_soul_ring_select_timer = nil
	end

	if self.remind_callback then
		RemindManager.Instance:UnBind(self.remind_callback)
		self.remind_callback = nil
	end

	self.need_soul_ring_total_tween = nil
	self.soul_ring_model_data_cache = nil
end

function ShenShouView:ShowIndexCallBack()
	self.need_soul_ring_total_tween = true
end

function ShenShouView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "open_shenshou_bag" then
			self:OnClickBtnOpenEquip()
		end
	end

	local data_list = ShenShouWGData.Instance:GetSoulRingDataList()
	self.soul_ring_list:SetDataList(data_list)

	local jump_index = self:CalSoulRingSelect()
	self.soul_ring_list:JumpToIndex(jump_index)

	local show_equip_btn = MountLingChongEquipWGData.Instance:GetCanShowEquipBtn(MOUNT_LINGCHONG_APPE_TYPE.KUN)
	local funopen = FunOpen.Instance:GetFunIsOpened(FunName.HuaKunEquipView)
    self.node_list["qc_btn_equip"]:CustomSetActive(show_equip_btn and funopen)
    if show_equip_btn then
        self.node_list.qc_btn_equip_text.text.text = Language.NewAppearance.QCEquipBtnText[MOUNT_LINGCHONG_APPE_TYPE.KUN]
    end
end

-- 魂环选中
function ShenShouView:CalSoulRingSelect()
	if self.cur_select_soul_ring_index >= 0 then
		if ShenShouWGData.Instance:GetHunHuanRemind(self.cur_select_soul_ring_seq) > 0 then
			return self.cur_select_soul_ring_index
		end
	end

	local soul_ring_list = ShenShouWGData.Instance:GetSoulRingDataList()

	if not IsEmptyTable(soul_ring_list) then
		for k, v in pairs(soul_ring_list) do
			if ShenShouWGData.Instance:GetHunHuanRemind(v.soul_ring_seq) > 0 then
				return k
			end
		end
	end

	return self.cur_select_soul_ring_index >= 0 and self.cur_select_soul_ring_index or 0
end

-- 选中魂环
function ShenShouView:SelectSoulRingItemCallBack(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	self.cur_select_soul_ring_index = item.index
	self.cur_select_soul_ring_data = data
	self.cur_select_soul_ring_seq = data.soul_ring_seq
	ShenShouWGData.Instance:SetViewSelectSoulRingSeq(self.cur_select_soul_ring_seq)

	local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(self.cur_select_soul_ring_seq)
	local wear_remind = ShenShouWGData.Instance:GetOneKeyWearDataList(self.cur_select_soul_ring_seq)
	self.node_list.btn_onekey_wear_red:CustomSetActive(unlock and not IsEmptyTable(wear_remind))

	local strength_remind = ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(self.cur_select_soul_ring_seq)
	self.node_list.btn_ss_red:CustomSetActive(strength_remind > 0)

	if unlock then
		local shenshou_id = -1
		local shenshou_up_tip = ""
		local soul_ring_info = ShenShouWGData.Instance:GetShenShouInfoBySeq(self.cur_select_soul_ring_seq)
	
		if soul_ring_info and soul_ring_info.shenshou_id > 0 then
			shenshou_id = soul_ring_info.shenshou_id
		end

		if shenshou_id >= 0 then
			local shou_cfg = ShenShouWGData.Instance:GetShenShouCfg(shenshou_id)
			shenshou_up_tip = shou_cfg and shou_cfg.desc_soul_ring_up or ""
		end

		self.node_list.desc_up_tip.text.text = shenshou_up_tip
		self.node_list.flag_up:CustomSetActive("" ~= shenshou_up_tip)
	else
		self.node_list.flag_up:CustomSetActive(false)
	end

	self:SoulRingClickTip(data)
	self:FlushSoulRingModel(data.soul_ring_seq)
	self:FlushRightAttrAddInfo()
	self:FlushSoulRingEquipInfo()
end

function ShenShouView:FlushSoulRingEquipInfo()
	local equip_list = ShenShouWGData.Instance:GetShenShouEquipInfoList(self.cur_select_soul_ring_seq)

	for i = 0, 4 do
		local target_data = (equip_list or {})[i] or {}

		self.shenshou_cells[i]:SetData(target_data)
		if not IsEmptyTable(target_data) and target_data.item_id and target_data.item_id <= 0 then
			local alpha_color = Color.New(1, 1, 1, 0.7)
			self.shenshou_cells[i]:SetItemIcon(ResPath.GetShenShouImages("a3_thsq_icon_" .. i))
			self.shenshou_cells[i]:SetItemIconAlpha(alpha_color)
		end
	end
end

function ShenShouView:SoulRingClickTip(data)
	local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(data.soul_ring_seq)

	if not unlock then
		local cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(data.soul_ring_seq)

		if not IsEmptyTable(cfg) then
			-- 前置
			if cfg.last_soul_ring_seq >= 0 then
				local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(cfg.last_soul_ring_seq)
				if not unlock then
					local other_cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(cfg.last_soul_ring_seq)
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingUnlockLast, other_cfg.soul_ring_name))
					return
				end
			end

			-- 等级
			if data.open_level > 0 then
				local role_level = RoleWGData.Instance:GetRoleLevel()

				if data.open_level > role_level then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingUnlockLevel, data.open_level))
					return
				end
			end

			-- vip
			if data.vip_level > 0 then
				local vip_level = VipWGData.Instance:GetRoleVipLevel()

				if data.vip_level > vip_level then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingUnlockVipLevel, data.vip_level))
					return
				end
			end

			-- 道具
			if data.stuff_id > 0 and data.stuff_num > 0 then
				ShenShouWGCtrl.Instance:OpenExtraZhuZhanTip({soul_ring_seq = data.soul_ring_seq})
				return
			end

			-- 灵玉
			if data.consume_lingyu > 0 then
				ShenShouWGCtrl.Instance:OpenExtraZhuZhanTip({soul_ring_seq = data.soul_ring_seq})
				return				
			end

			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ADD_ZHUZHAN, data.soul_ring_seq)
		end
	end
end

function ShenShouView:FlushSoulRingModel(soul_ring_seq)
	local soul_ring_model_data, select_model_data = ShenShouWGData.Instance:GetSoulRingModelData(soul_ring_seq)
	local target_data, has_act_soul_ring = self:CalSoulRingData(soul_ring_model_data)

	if has_act_soul_ring then
		self.need_soul_ring_total_tween = true
		self.soul_ring_model:SetSoulRingSelectModel(soul_ring_seq, nil)
	end

	-- 魂环
	self.soul_ring_model:SetTotalSoulRingResid(target_data, self.need_soul_ring_total_tween)
	self.soul_ring_model_data_cache = soul_ring_model_data

	if self.delay_show_soul_ring_select_timer then
		GlobalTimerQuest:CancelQuest(self.delay_show_soul_ring_select_timer)
		self.delay_show_soul_ring_select_timer = nil
	end

	if self.need_soul_ring_total_tween then
		if #soul_ring_model_data > 0 then
			self.delay_show_soul_ring_select_timer = GlobalTimerQuest:AddTimesTimer(function ()
				self.soul_ring_model:SetSoulRingSelectModel(soul_ring_seq, select_model_data)
			end, (#soul_ring_model_data + 1) * 0.3 + 0.6, 1)
		end
	else
		-- 选中魂环
		self.soul_ring_model:SetSoulRingSelectModel(soul_ring_seq, select_model_data)
	end

	self.need_soul_ring_total_tween = false
end

function ShenShouView:CalSoulRingData(soul_ring_model_data)
	local has_act_soul_ring = false
	if IsEmptyTable(self.soul_ring_model_data_cache) or IsEmptyTable(soul_ring_model_data) then
		return soul_ring_model_data, has_act_soul_ring
	end

	for k, v in pairs(soul_ring_model_data) do
		local old_quality = (self.soul_ring_model_data_cache[k] or {}).quality or -1
		local new_quality = v.quality

		if old_quality == -1 and new_quality >= 0 then
			v.is_act = true
			has_act_soul_ring = true
		end

		if old_quality >= 0 and new_quality >= 0 then
			if new_quality > old_quality then
				v.is_up = true
			end
		end

		-- if k == 1 then
		-- 	v.is_up = true
		-- 	v.soul_ring_effect = "eff_hunhuan1_jin"
		-- end
	end

	return soul_ring_model_data, has_act_soul_ring
end

function ShenShouView:SelectSoulRingEquipItemCallBack(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data

	if data.item_id <= 0 then
		ShenShouWGCtrl.Instance:OpenShenShouBagView()
	end
end

-- 属性
function ShenShouView:FlushRightAttrAddInfo()
	-- 天道石影响属性百分比,不影响百分比
	local charm_rate = CultivationWGData.Instance:GetAddRateByType(CHARM_RATE_TYPE.SHENSHOU)
	charm_rate = charm_rate / 10000

	local soul_ring_info = ShenShouWGData.Instance:GetShenShouInfoBySeq(self.cur_select_soul_ring_seq)
	local shenshou_id

	if soul_ring_info and soul_ring_info.shenshou_id > 0 then
		shenshou_id = soul_ring_info.shenshou_id
	else
		shenshou_id = self.cur_select_soul_ring_data.shou_id
	end

	local shou_cfg = ShenShouWGData.Instance:GetShenShouCfg(shenshou_id)
	local shenshou_base_struct = AttributeMgr.GetAttributteByClass(shou_cfg)
	shenshou_base_struct = AttributeMgr.MulAttributeRemovePer(shenshou_base_struct,charm_rate + 1)
	local eq_struct = ShenShouWGData.Instance:GetSoulRingAttrData(self.cur_select_soul_ring_seq)
	eq_struct = AttributeMgr.MulAttributeRemovePer(eq_struct, charm_rate + 1)

	local attr_list = EquipWGData.GetSortAttrListHaveAddByCfg(shenshou_base_struct, eq_struct)

	for k,v in ipairs(self.attr_list) do
		v:SetData(attr_list[k])
	end

	local capability = 0
	capability = AttributeMgr.GetCapability(eq_struct)
	capability = capability + AttributeMgr.GetCapability(shenshou_base_struct)
	self.node_list.cap_value.text.text = capability

	-- 技能
	local skill_data_list = ShenShouWGData.Instance:GetSoulRingSkillData(shenshou_id)
	self.node_list.ph_grid_skill2:SetActive(#skill_data_list > 4)  -- 一行四个

	for k, v in pairs(self.ss_skill_list) do
		local has_data = not IsEmptyTable(skill_data_list[k])
		self.ss_skill_list[k]:SetActive(has_data)

		if has_data then
			v:SetData(skill_data_list[k])
			v:SetSoulRingSeq(self.cur_select_soul_ring_seq)

			-- XUI.SetGraphicGrey(self.node_list["ph_skill_itemrender" .. k], skill_data_list[k].level <= 0)
		end
	end
end

function ShenShouView:OnClickOverviewBtn()
	-- local soul_ring_model_data, select_model_data = ShenShouWGData.Instance:GetSoulRingModelData(0)
	-- for k, v in pairs(soul_ring_model_data) do
	-- 	local old_quality = (self.soul_ring_model_data_cache[k] or {}).quality or -1
	-- 	local new_quality = v.quality

	-- 	if old_quality == -1 and new_quality >= 0 then
	-- 		v.is_act = true
	-- 	end

	-- 	if old_quality >= 0 and new_quality >= 0 then
	-- 		if new_quality > old_quality then
	-- 			v.is_up = true
	-- 		end
	-- 	end

	-- 	if k == 1 then
	-- 		v.is_up = true
	-- 		v.soul_ring_effect = "eff_hunhuan1_jin"
	-- 	end
	-- end

	-- -- 魂环
	-- self.soul_ring_model:SetTotalSoulRingResid(soul_ring_model_data, true)

	ShenShouWGCtrl.Instance:OpenShenShouOverview(self.cur_select_soul_ring_seq)
end

function ShenShouView:OnClickBtnOpenEquip()
	ShenShouWGCtrl.Instance:OpenShenShouBagView()
end

-- 一键卸下
function ShenShouView:OnClickDischargeBtn()
	local is_wear_equip = ShenShouWGData.Instance:IsShenShouWearEquip(self.cur_select_soul_ring_seq)
	if not is_wear_equip then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ExtraTip7)
		return
	end

	TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.ShenShou.SoulRingDragAllEquipTip, function ()
		ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ONE_KEY_TAKE_OFF, self.cur_select_soul_ring_seq)
	end, "ShenShouDrawAllEquip", Language.ShenShou.CheckBoxText, nil, Language.ShenShou.CancelText)
end

function ShenShouView:OnClickBtnOpenQiangHua()
	if ShenShouWGData.Instance:IsSoulRingWearEquip() then
		ShenShouWGCtrl.Instance:OpenShenShouQiangHua()--打开强化面板
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.SoulRingStrengthTip)
	end
end

function ShenShouView:OnClickOneKeyWearBtn()
	local one_key_wear_data = ShenShouWGData.Instance:GetOneKeyWearDataList(self.cur_select_soul_ring_seq)
	if IsEmptyTable(one_key_wear_data) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.WearTip_3)
	else
		-- param2 背包格子index, param3 装备槽格子index
		for k, v in pairs(one_key_wear_data) do
			local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(v.item_id)
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_PUT_ON, self.cur_select_soul_ring_seq, v.index, shenshou_equip_cfg.slot_index)
		end
	end
end

function ShenShouView:OnClickOpenQiChongEquip()
	FunOpen.Instance:OpenViewByName(GuideModuleName.HuaKunEquipView)
end

function ShenShouView:OnRemindChange(remind_name, num)
    if remind_name == RemindName.HuaKunEquipTotal then
        if self.node_list.remind_wardrobe then
			self.node_list.remind_wardrobe:CustomSetActive(num > 0)
		end
    end
end

----------------------------------------------SoulRingListItemCellRender----------------------------------------------------------
SoulRingListItemCellRender = SoulRingListItemCellRender or BaseClass(BaseRender)

function SoulRingListItemCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.other_btn, BindTool.Bind(self.OnClickOtherBtn, self))
end

function SoulRingListItemCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(self.data.soul_ring_seq)
	self.node_list.img_lock_bg:CustomSetActive(not unlock)
	self.node_list.img_active:CustomSetActive(unlock)
	-- self.node_list.other_btn:CustomSetActive(not unlock)
	self.node_list.img_lock:CustomSetActive(not unlock)

	local cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(self.data.soul_ring_seq)
	self.node_list.desc_name.text.text = cfg.soul_ring_name
	self.node_list.desc_lock_name.text.text = cfg.soul_ring_name

	local res_name = unlock and "a3_soul_ring_icon_hl_" or "a3_soul_ring_icon_"
	local bundle, asset = ResPath.GetShenShouImages(res_name .. self.data.icon)
	self.node_list.img_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.img_icon.image:SetNativeSize()
	end)

	local remind = ShenShouWGData.Instance:GetHunHuanRemind(self.data.soul_ring_seq)
	self.node_list.remind:CustomSetActive(remind > 0)

	if unlock then
		for i = 1, 2 do
			local bundle, asset = ResPath.GetShenShouImages("a3_hhfq_bs_" .. self.data["attr_name_bg" .. i])
			self.node_list["img_bs_bg" .. i].image:LoadSprite(bundle, asset, function ()
				self.node_list["img_bs_bg" .. i].image:SetNativeSize()
			end)

			self.node_list["desc_bs" .. i].text.text = self.data["attr_name" .. i]
		end
	else
		if cfg.last_soul_ring_seq >= 0 then
			local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(cfg.last_soul_ring_seq)

			if not unlock then
				local last_cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(cfg.last_soul_ring_seq)
				self.node_list.desc_unlock_tip.text.text = string.format(Language.ShenShou.SoulRingUnlockTip[1], last_cfg.soul_ring_name)
				return
			end
		end

		-- 等级
		if self.data.open_level > 0 then
			local role_level = RoleWGData.Instance:GetRoleLevel()

			if self.data.open_level > role_level then
				self.node_list.desc_unlock_tip.text.text = string.format(Language.ShenShou.SoulRingUnlockTip[2], self.data.open_level)
				return
			end
		end

		-- vip
		if self.data.vip_level > 0 then
			local vip_level = VipWGData.Instance:GetRoleVipLevel()

			if self.data.vip_level > vip_level then
				self.node_list.desc_unlock_tip.text.text = string.format(Language.ShenShou.SoulRingUnlockTip[3], self.data.vip_level)
				return
			end
		end

		-- 道具
		if self.data.stuff_id > 0 and self.data.stuff_num > 0 then
			self.node_list.desc_unlock_tip.text.text = Language.ShenShou.SoulRingUnlockTip[4]
		end

		-- 灵玉
		if self.data.consume_lingyu > 0 then
			self.node_list.desc_unlock_tip.text.text = Language.ShenShou.SoulRingUnlockTip[5]
			return				
		end
	end
end

function SoulRingListItemCellRender:OnSelectChange(is_select)
	if IsEmptyTable(self.data) then
		return
	end

	local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(self.data.soul_ring_seq)
	self.node_list.bg_img_lock:CustomSetActive(not unlock and not is_select)
	self.node_list.bg_img_lock_select:CustomSetActive(not unlock and is_select)
	self.node_list.img_active_bg:CustomSetActive(unlock and not is_select)
	self.node_list.img_active_bg_select:CustomSetActive(unlock and is_select)

	self.node_list.desc_name.text.color = StrToColor(is_select and COLOR3B.C26 or COLOR3B.C6)
	self.node_list.desc_lock_name.text.color = StrToColor(is_select and COLOR3B.C26 or COLOR3B.C13)
	self.node_list.desc_unlock_tip.text.color = StrToColor(is_select and COLOR3B.C26 or COLOR3B.C13)

	-- self.node_list.img_select:CustomSetActive(is_select)
end

function SoulRingListItemCellRender:OnClickOtherBtn()
	local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(self.data.soul_ring_seq)

	if not unlock then
		local cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(self.data.soul_ring_seq)

		if not IsEmptyTable(cfg) then
			-- 前置
			if cfg.last_soul_ring_seq >= 0 then
				local unlock = ShenShouWGData.Instance:IsSoulRingUnLock(cfg.last_soul_ring_seq)
				if not unlock then
					local other_cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(cfg.last_soul_ring_seq)
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingUnlockLast, other_cfg.soul_ring_name))
					return
				end
			end

			-- 等级
			if self.data.open_level > 0 then
				local role_level = RoleWGData.Instance:GetRoleLevel()

				if self.data.open_level > role_level then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingUnlockLevel, self.data.open_level))
					return
				end
			end

			-- vip
			if self.data.vip_level > 0 then
				local vip_level = VipWGData.Instance:GetRoleVipLevel()

				if self.data.vip_level > vip_level then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ShenShou.SoulRingUnlockVipLevel, self.data.vip_level))
					return
				end
			end

			-- 道具
			if self.data.stuff_id > 0 and self.data.stuff_num > 0 then
				ShenShouWGCtrl.Instance:OpenExtraZhuZhanTip({soul_ring_seq = self.data.soul_ring_seq})
				return
			end

			-- 灵玉
			if self.data.consume_lingyu > 0 then
				ShenShouWGCtrl.Instance:OpenExtraZhuZhanTip({soul_ring_seq = self.data.soul_ring_seq})
				return				
			end

			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ADD_ZHUZHAN,  self.data.soul_ring_seq)
		end
	end
end

---------------------------ShouSkillItemRender--------------------------------
ShouSkillItemRender = ShouSkillItemRender or BaseClass(BaseRender)

function ShouSkillItemRender:LoadCallBack()
	self.icon_list = self.node_list["ph_skill_icon"]
	self.level_list = self.node_list["lbl_skill_lv"]
	self.view.button:AddClickListener(BindTool.Bind(self.OnClickSkill, self))
end
function ShouSkillItemRender:__delete()
	self.soul_ring_seq = nil
end

function ShouSkillItemRender:OnFlush()
	local skill_cfg = ShenShouWGData.Instance:GetShenShouSKillCfg(self.data.skill_type, self.data.level)
	if nil == skill_cfg then return end

	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon_id)
	self.icon_list.image:LoadSprite(bundle, asset, function()
		self.icon_list.image:SetNativeSize()
	end)
	self.level_list.text.text = skill_cfg.level

	self.node_list.skill_mask:CustomSetActive(self.data.level <= 0)
end

function ShouSkillItemRender:SetSoulRingSeq(soul_ring_seq)
	self.soul_ring_seq = soul_ring_seq
end

function ShouSkillItemRender:OnClickSkill()
	ShenShouWGCtrl.Instance:OpenShenShouSkillOverview(self.soul_ring_seq, self.data)

	-- if self.data == nil then 
	-- 	return 
	-- end

	-- local skill_cfg = ShenShouWGData.Instance:GetShenShouSkillCfg(self.data.skill_type, self.data.level)

	-- if nil == skill_cfg then
	-- 	return
	-- end

	-- local show_data = {
    --     icon = skill_cfg.icon_id,
    --     top_text = skill_cfg.name,
    --     body_text = skill_cfg.description,
    --     skill_level = skill_cfg.level,
    --     x = 0,
    --     y = 0,
    --     set_pos2 = true,
    -- }

    -- NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

-----------------------------------------------------------------------------------------------------------------------

-----------------------------助战基础属性-------------------------------
ShenBingBaseRenderNew = ShenBingBaseRenderNew or BaseClass(BaseRender)

function ShenBingBaseRenderNew:OnFlush()
    if nil == self.data then
		self.view:SetActive(false)
        return
    end

	self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, true, false)
	self.node_list.attr_value.text.text = self.data.attr_value

	self.node_list.add_value.text.text = self.data.add_value
	-- if(self.data.add_value > 0) then
	-- 	self.node_list.add_value.text.text = "+ " .. self.data.add_value
	-- 	self.node_list.add_value:SetActive(true)
	-- else
	-- 	self.node_list.add_value:SetActive(false)
	-- end

	self.view:SetActive(true)
end
