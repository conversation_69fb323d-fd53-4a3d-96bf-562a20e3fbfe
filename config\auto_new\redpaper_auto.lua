-- H-红包.xls

return {
other_config={
{}
},

other_config_meta_table_map={
},
guild_redpaper={
{type=1,bind_gold=50,name="首充红包",Chanel_descript="天天首充，精打细算，仙盟好友分享了一个红包！",},
{param1=4,bind_gold=100,descript="达成vip4，免费打魔域3层",Chanel_descript="升级vip4，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP4",},
{level=1,param1=5,descript="达成vip5，尊享特权",Chanel_descript="升级vip5，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP5",},
{level=2,param1=6,bind_gold=200,descript="达成vip6，尊享特权",Chanel_descript="升级vip6，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP6",},
{level=3,param1=7,bind_gold=300,descript="达成vip7，尊享特权",Chanel_descript="升级vip7，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP7",},
{level=4,param1=8,bind_gold=500,descript="达成vip8，尊享特权",Chanel_descript="升级vip8，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP8",},
{level=5,param1=9,bind_gold=750,descript="达成vip9，尊享特权",Chanel_descript="升级vip9，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP9",},
{level=6,param1=10,bind_gold=1000,descript="达成vip10，尊享特权",Chanel_descript="升级vip10，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP10",},
{level=7,param1=11,bind_gold=1500,descript="达成vip11，尊享特权",Chanel_descript="升级vip11，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP11",},
{level=8,param1=12,bind_gold=2000,descript="达成vip12，尊享特权",Chanel_descript="升级vip12，尊享特权，仙盟好友分享了一个红包！",goal_descript="达到VIP12",},
{type=3,param1=300,bind_gold=100,name="累充红包",descript="单天累充300灵玉",goal_descript="今日充值300灵玉",},
{level=1,param1=680,descript="单天累充680灵玉",goal_descript="今日充值680灵玉",},
{level=2,param1=1280,descript="单天累充1280灵玉",goal_descript="今日充值1280灵玉",},
{type=3,level=3,param1=6480,name="累充红包",descript="单天累充6480灵玉",goal_descript="今日充值6480灵玉",},
{level=4,param1=20000,descript="单天累充20000灵玉",goal_descript="今日充值20000灵玉",},
{type=4,param1=1,bind_gold=300,name="仙盟答题红包",descript="仙盟答题第一名",Chanel_descript="仙盟答题，送红包，仙盟好友分享了一个红包！",goal_descript="仙盟答题第一名",},
{type=4,level=1,param1=2,bind_gold=200,name="仙盟答题红包",descript="仙盟答题第二名",Chanel_descript="仙盟答题，送红包，仙盟好友分享了一个红包！",goal_descript="仙盟答题第二名",},
{type=4,level=2,param1=3,name="仙盟答题红包",descript="仙盟答题第三名",Chanel_descript="仙盟答题，送红包，仙盟好友分享了一个红包！",goal_descript="仙盟答题第三名",},
{level=3,param1=999,bind_gold=100,num=15,descript="仙盟答题参与",goal_descript="答题参与",},
{type=5,bind_gold=600,name="仙盟争霸红包",descript="最强仙盟",Chanel_descript="最强仙盟盟主分享了一个红包！",goal_descript="最强仙盟",},
{type=6,param1=1,bind_gold=500,num=10,name="BOSS狩猎",descript="BOSS狩猎，达标奖励",Chanel_descript="BOSS狩猎达标仙盟好友，分享了一个红包！",goal_descript="BOSS狩猎活动达标",},
{level=1,param1=2,bind_gold=3000,descript="BOSS狩猎，第一名",Chanel_descript="BOSS狩猎第一名，分享了一个红包！",goal_descript="BOSS狩猎活动第一名",},
{level=2,param1=3,bind_gold=1500,descript="BOSS狩猎，第二名",Chanel_descript="BOSS狩猎第二名，分享了一个红包！",goal_descript="BOSS狩猎活动第二名",},
{level=3,param1=4,bind_gold=900,descript="BOSS狩猎，第三名",Chanel_descript="BOSS狩猎第三名，分享了一个红包！",goal_descript="BOSS狩猎活动第三名",},
{level=4,param1=5,descript="BOSS狩猎，第四名",Chanel_descript="BOSS狩猎第四名，分享了一个红包！",goal_descript="BOSS狩猎活动第四名",},
{type=7,bind_gold=400,name="仙盟争霸单轮红包1",descript="第一赛区获胜红包",Chanel_descript="仙盟争霸第一赛区获得胜利，奖励红包！",goal_descript="仙盟争霸第一赛区胜利",},
{type=7,level=1,bind_gold=200,name="仙盟争霸单轮红包2",descript="第二赛区获胜红包",Chanel_descript="仙盟争霸第二赛区获得胜利，奖励红包！",goal_descript="仙盟争霸第二赛区胜利",},
{type=8,bind_gold=3000,name="签到红包",descript="签到两人红包",Chanel_descript="感谢大家齐心协力！这是今日的签到2人红包",goal_descript="签到两人",},
{type=8,level=1,bind_gold=4000,name="签到红包",descript="签到三人红包",Chanel_descript="感谢大家齐心协力！这是今日的签到3人红包",goal_descript="签到三人",}
},

guild_redpaper_meta_table_map={
[13]=14,	-- depth:1
[12]=14,	-- depth:1
[15]=14,	-- depth:1
[19]=17,	-- depth:1
[22]=21,	-- depth:1
[23]=21,	-- depth:1
[24]=21,	-- depth:1
[25]=21,	-- depth:1
},
world_redpaper={
{}
},

world_redpaper_meta_table_map={
},
custom_redpaper={
{},
{seq=1,cost_gold=288,bind_gold=2880,},
{seq=2,cost_gold=588,bind_gold=5880,}
},

custom_redpaper_meta_table_map={
},
response={
{},
{ID=2,des="有红包！好开心！",},
{ID=3,des="请发红包，不找零！",},
{ID=4,des="发我多少！，你就瘦多少！",},
{ID=5,des="再来一个，好不好＾∀＾！",},
{ID=6,des="发红包的一定是男神女神！",}
},

response_meta_table_map={
},
redpaper_dec={
{},
{ID=2,des="人品暴涨！",},
{ID=3,des="寻宝必出精品！",},
{ID=4,des="恭喜发财！",},
{ID=5,des="合装必定成功！",},
{ID=6,des="意思一下！",},
{ID=7,des="BOSS必爆外观！",},
{ID=8,des="走上人生巅峰！",},
{ID=9,des="天涯到处有芳草！",},
{ID=10,des="男神发红包啦！",},
{ID=11,des="女神发红包啦！",},
{ID=12,des="带大家飞！",},
{ID=13,des="仙界一枝花！",},
{ID=14,des="瘦成闪电！",},
{ID=15,des="不吃饱怎么减肥！",},
{ID=16,des="掂过碌蔗！",},
{ID=17,des="亮到爆镜！",},
{ID=18,des="来个妹妹耍朋友！",}
},

redpaper_dec_meta_table_map={
},
other_config_default_table={double_fetch_vip=6,fetch_multiple=1.5,guild_daily_fetch_count_limit=9999,guild_daily_distribute_limit=9999,world_daily_fetch_count_limit=9999,world_daily_distribute_limit=9999,total_gold_bind_limit=100,total_silver_ticket=1000,level_limit=65,vip_limit=0,gold_bind_max=9999,sliver_ticket_max=500,vip_gold_bind_max=9999,vip_sliver_ticket_max=750,distribute_count_limit_per_day=1,},

guild_redpaper_default_table={type=2,level=0,param1=0,param2=0,bind_gold=150,silver_ticket=0,num=20,name="vip红包",descript="天天首充，以战养战",Chanel_descript="单天累充，以战养战，仙盟好友分享了一个红包！",goal_descript="每日首充",},

world_redpaper_default_table={type=3,level=0,param1=0,param2=0,bind_gold=0,silver_ticket=0,gold=200,num=80,name="金光鉴福红包",descript="金光鉴福红包",Chanel_descript="金光鉴福红包",goal_descript="金光鉴福红包",min_num="",},

custom_redpaper_default_table={seq=0,cost_gold=88,bind_gold=880,silver_ticket=0,num=20,},

response_default_table={ID=1,des="谢谢老板！",},

redpaper_dec_default_table={ID=1,des="大吉大利，今晚吃鸡",}

}

