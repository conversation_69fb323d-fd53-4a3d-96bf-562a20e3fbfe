TipsExitGameView = TipsExitGameView or BaseClass(SafeBaseView)

local OPERA_TYPE = {
	GUAJI = 1,
	GIFT = 2,
	VIP = 3,
	ACTIVITY = 4,
}
function TipsExitGameView:__init()
	self.view_layer = UiLayer.PopTop
	self:AddViewResource(0, "uis/view/tips/exitgame_prefab", "layout_exit_dialog")
	self:SetMaskBg(true)
end

function TipsExitGameView:__delete()
	
end

function TipsExitGameView:ReleaseCallBack()
end

function TipsExitGameView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_guaji, BindTool.Bind(self.OnClickOpera, self, OPERA_TYPE.GUAJI))
	XUI.AddClickEventListener(self.node_list.btn_gift, BindTool.Bind(self.OnClickOpera, self, OPERA_TYPE.GIFT))
	XUI.AddClickEventListener(self.node_list.btn_vip, BindTool.Bind(self.OnClickOpera, self, OPERA_TYPE.VIP))
	XUI.AddClickEventListener(self.node_list.btn_activity, BindTool.Bind(self.OnClickOpera, self, OPERA_TYPE.ACTIVITY))
	XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind(self.OnClickOk, self))
end

function TipsExitGameView:ShowIndexCallBack(index)
	self:Flush()
end

function TipsExitGameView:CloseCallBack()
    if CountDownManager.Instance:HasCountDown("exit_game_free_vip_timer") then
        CountDownManager.Instance:RemoveCountDown("exit_game_free_vip_timer")
    end

    if CountDownManager.Instance:HasCountDown("exit_game_gift_timer") then
        CountDownManager.Instance:RemoveCountDown("exit_game_gift_timer")
    end
end

function TipsExitGameView:OnClickOk()
	ReportManager:ReportRole(REPORT_ROLE_ACTTION.quit)
	TryDelayCall(self, function()
		self:Close()
		DeviceTool.Quit()
	end, 0.2)
end

function TipsExitGameView:OnClickOpera(opera_type)
	self:Close()

	if opera_type == OPERA_TYPE.GUAJI then
		ViewManager.Instance:Open(GuideModuleName.BiZuo)
	elseif opera_type == OPERA_TYPE.GIFT then
		ViewManager.Instance:Open(GuideModuleName.LimitTimeGift)
	elseif opera_type == OPERA_TYPE.VIP then
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
	elseif opera_type == OPERA_TYPE.ACTIVITY then
		ViewManager.Instance:Open(GuideModuleName.BiZuo, TabIndex.bizuo_act_hall)
	end
end

function TipsExitGameView:OnFlush()
	if OfflineRestWGData.Instance then
		local time_str = Language.Common.ExitViewHasStr
		local remain_offline_rest_time = OfflineRestWGData.Instance:GetRemainOfflineRestTime() or 0
		local has_str = OfflineRestWGData.Instance:GetNoSecondTimeStr(remain_offline_rest_time)
		self.node_list.text_timer_guaji.text.text = string.format(time_str, has_str)
	end

    if CountDownManager.Instance:HasCountDown("exit_game_free_vip_timer") then
        CountDownManager.Instance:RemoveCountDown("exit_game_free_vip_timer")
    end

    if CountDownManager.Instance:HasCountDown("exit_game_gift_timer") then
        CountDownManager.Instance:RemoveCountDown("exit_game_gift_timer")
    end

	local is_has_free_vip, card_seq = VipWGData.Instance:CheckHasFreeCard()
	local card_cfg = VipWGData.Instance:GetVipCardCfg(card_seq)
	local time = 0
	local cur_time = TimeWGCtrl.Instance:GetServerTime()

	if is_has_free_vip then
		local vip_str = Language.Common.ExitViewCanGet
		if card_cfg ~= nil then
			local online_time_s = TimeWGCtrl.Instance:GetOnlineTimes()
			time = card_cfg.active_value * 60 - online_time_s

			local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
			if card_cfg.active_condition == VipWGData.VipCardActiveType.day and time <= 0 and (card_cfg.active_value - login_day_count) > 0 then
				vip_str = string.format(Language.Vip.VipTips_5, card_cfg.active_value - login_day_count)
			end
		end

		self.node_list.root_get_vip:SetActive(true)
		if time <= 0 then
		    if self.node_list.text_timer_vip ~= nil then
		        self.node_list.text_timer_vip.text.text = vip_str
		    end
		else
	        if self.node_list.text_call_time ~= nil then
	            self.node_list.text_call_time:SetActive(true)
	        end

	        if self.node_list.img_call_mask ~= nil then
	            self.node_list.img_call_mask:SetActive(true)
	        end

	        local next_call_time = time + cur_time
	        self:UpdateFreeVipCallBack(0, time)
	        CountDownManager.Instance:AddCountDown("exit_game_free_vip_timer", BindTool.Bind(self.UpdateFreeVipCallBack, self), BindTool.Bind(self.CompleteFreeVipCallBack, self), next_call_time, nil, 1)
		end
	else
		self.node_list.root_get_vip:SetActive(false)
	end

	if LimitTimeGiftWGData.Instance == nil then
		return
	end

	local gift_is_open, gift_time = LimitTimeGiftWGData.Instance:GetActIsOpen()
	if gift_is_open then
		self.node_list.root_gift:SetActive(true)
		local act_time = gift_time - cur_time
		if act_time > 0 then
			local next_call_time = gift_time
	        self:UpdateGiftCallBack(0, act_time)
	        CountDownManager.Instance:AddCountDown("exit_game_gift_timer", BindTool.Bind(self.UpdateGiftCallBack, self), BindTool.Bind(self.CompleteGiftCallBack, self), next_call_time, nil, 1)
		else
			self:CompleteGiftCallBack()
		end
	else
		self:CompleteGiftCallBack()
	end
	--[[
	local cfg = CalendarWGData.Instance:GetCalendarActivityCfg()
	if cfg ~= nil then
		local need_show_cfg = cfg[1]
		if need_show_cfg ~= nil then
			self.node_list.root_activity:SetActive(true)
			self.node_list.text_activity_name.text.text = need_show_cfg.cfg.name
			self.node_list.text_timer_activity.text.text = need_show_cfg.str
			local act_id = need_show_cfg.cfg.res_head
			local bundle, asset = ResPath.GetMainUIIcon("act_" .. act_id)
			self.node_list.btn_activity.image:LoadSprite(bundle, asset, function()
				if self:IsOpen() and self.node_list.btn_activity ~= nil then
					self.node_list.btn_activity.image:SetNativeSize()
				end
			 end)
		else
			self.node_list.root_activity:SetActive(false)
		end
	else
		self.node_list.root_activity:SetActive(false)
	end
	]]
end

function TipsExitGameView:UpdateFreeVipCallBack(elapse_time, total_time)
    if total_time - elapse_time > 0 then
        if self.node_list.text_timer_vip ~= nil then
            local time_str = TimeUtil.FormatSecond2MS(total_time - elapse_time)
            self.node_list.text_timer_vip.text.text = string.format(Language.Common.ExitViewHasStr, time_str)
        end
    end
end

function TipsExitGameView:CompleteFreeVipCallBack()
    if self.node_list.text_timer_vip ~= nil then
        local vip_str = Language.Common.ExitViewCanGet
		local is_has_free_vip, card_seq = VipWGData.Instance:CheckHasFreeCard()
		local card_cfg = VipWGData.Instance:GetVipCardCfg(card_seq)
		local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
		if card_cfg.active_condition == VipWGData.VipCardActiveType.day and (card_cfg.active_value - login_day_count) > 0 then
			vip_str = string.format(Language.Vip.VipTips_5, card_cfg.active_value - login_day_count)
		end

		self.node_list.text_timer_vip.text.text = vip_str
    end	
end

function TipsExitGameView:UpdateGiftCallBack(elapse_time, total_time)
    if total_time - elapse_time > 0 then
        if self.node_list.text_timer_gift ~= nil then
            local time_str = TimeUtil.FormatSecond2MS(total_time - elapse_time)
            self.node_list.text_timer_gift.text.text = string.format(Language.Common.ExitViewHasStr, time_str)
        end
    end
end

function TipsExitGameView:CompleteGiftCallBack()
    if self.node_list.root_gift ~= nil then
        self.node_list.root_gift:SetActive(false)
    end	
end