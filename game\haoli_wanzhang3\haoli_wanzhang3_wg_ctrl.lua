require("game/haoli_wanzhang3/haoli_wanzhang3_wg_data")
require("game/haoli_wanzhang3/haoli_wanzhang3_view")

-- 绝版套装
HaoLiWanZhang3WGCtrl = HaoLiWanZhang3WGCtrl or BaseClass(BaseWGCtrl)
function HaoLiWanZhang3WGCtrl:__init()
    if HaoLiWanZhang3WGCtrl.Instance then
		error("[HaoLiWanZhang3WGCtrl]:Attempt to create singleton twice!")
	end

    HaoLiWanZhang3WGCtrl.Instance = self
    self.data = HaoLiWanZhang3WGData.New()

    self.view = HaoLiWanZhang3View.New(GuideModuleName.HaoLiWanZhang3View)
    self:RegisterAllProtocols()
end

function HaoLiWanZhang3WGCtrl:__delete()
    HaoLiWanZhang3WGCtrl.Instance = nil

    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    -- if self.act_change then
    --     ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
    --     self.act_change = nil
    -- end

    -- if self.role_data_change then
	-- 	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
	-- 	self.role_data_change = nil
	-- end
end

function HaoLiWanZhang3WGCtrl:OpenHaoLiWanZhang3View()
    self.view:Open()
end

function HaoLiWanZhang3WGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAHaoLiWanZhang3Info, "OnSCOAHaoLiWanZhang3Info")

    -- self.act_change = BindTool.Bind(self.OnActivityChange, self)
    -- ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    -- self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
    -- RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function HaoLiWanZhang3WGCtrl:OnSCOAHaoLiWanZhang3Info(protocol)
    self.data:SetAllVientianeInfo(protocol)
    -- MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
    self:FlushView()
    RemindManager.Instance:Fire(RemindName.HaoLiWanZhang3)
end

function HaoLiWanZhang3WGCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.HaoLiWanZhang3View)
end

-- function HaoLiWanZhang3WGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
--     if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT and (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.CLOSE) then
--         MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
--     end
-- end

-- function HaoLiWanZhang3WGCtrl:OnRoleAttrChange(attr_name, value, old_value)
--     if attr_name == "level" then
--         local show_info = self.data:GetOtherCfg()
--         local tianyin_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT)
--         if (old_value < show_info.open_level and value >= show_info.open_level) and tianyin_is_open then
--             MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
--         end
--     end
-- end