VipWGData = VipWGData or BaseClass()

VipWGData.VipCardActiveType = {
	online = 1,		-- 在线时间
	day = 2,		-- 登录天数
	glod = 3,		-- 消耗道具
}

function VipWGData:__init()
	if VipWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[VipWGData] Attemp to create a singleton twice !")
	end
	VipWGData.Instance = self

	self.total_chongzhi = 0

	self.MAX_SLOT = 8
	self.recharge_exp = 0
    self.end_time = 0
    self.f2_vip_crad_info = nil
    self.vip_is_active_mark = nil
    self.first_free_timer = nil
    self.first_free_tips = true
    self.vip_temp_level = nil
    self.result_initial_flag = false
    self.initial_old_vip_level = 0
    self.initial_vip_level = 0

    self.vip_auto_cfg = ConfigManager.Instance:GetAutoConfig("vip_auto")

	self:InitVipAllPrivilegeTipsCfg()
end

function VipWGData:__delete()
	VipWGData.Instance = nil
	self.vip_end_time = nil
	self.vip_past_level = nil
	self.f2_vip_crad_info = nil
	self.vip_is_active_mark = nil
	self.first_free_tips = nil
	self.vip_temp_level = nil
	self.result_initial_flag = nil
	self.initial_old_vip_level = nil
    self.initial_vip_level = nil
end

function VipWGData:GetMaxVIPLevel()
	return #(self.vip_auto_cfg.uplevel) - 1
end

function VipWGData:SetVipRechargeData(protocol)
	self.total_chongzhi = protocol.history_recharge
end

function VipWGData:SetRechargeExp(recharge_exp, vip_level, protocol_type, vip_exp_change_type, vip_exp_add_value)
	if self.old_recharge_exp and protocol_type then
		return
	end
	self.old_recharge_exp = self.recharge_exp or recharge_exp
	if recharge_exp > self.old_recharge_exp then
		ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.GuiZuJiYi, recharge_exp - self.old_recharge_exp)
	end

	self.recharge_exp = recharge_exp
	self.old_recharge_vip = self.recharge_vip or vip_level
	self.recharge_vip = vip_level
	self.vip_exp_change_type = vip_exp_change_type
	self.vip_exp_add_value = vip_exp_add_value
end

--获取角色vip等级(过期后为0)
function VipWGData:GetRoleVipLevel()
	local vip_level = RoleWGData.Instance:GetAttr("vip_level") or 0
	return vip_level
end

--获取角色vip过期前等级 大于0则表示vip已过期
function VipWGData:SetRoleVipPastLevel(vip_past_level)
	self.vip_past_level = vip_past_level
end

function VipWGData:GetRoleVipPastLevel()
	return self.vip_past_level or 0
end
--获取vip过期时间 如果是0 表示从未购买过vip否则表示购买过过期也是
function VipWGData:SetRoleVipEndTime(vip_end_time)
	self.vip_end_time = vip_end_time
end

function VipWGData:GetRoleVipEndTime()
	return self.vip_end_time or 0
end

--获取当前充值的情况现在分子是总充值，分母是总下一次充值
function VipWGData:GetRechargeCondition()
	local level_config = self.vip_auto_cfg.uplevel
	local all_need_gold = 0
	local cur_vip_level = self:GetVipLevel()
	for i, v in ipairs(level_config) do
		if v.level <= cur_vip_level then
			all_need_gold = v.need_exp
		end
	end
	return self.recharge_exp, all_need_gold
end

function VipWGData:GetVipUpCondition(vip_level)
	local level_config = self.vip_auto_cfg.uplevel
	for i, v in ipairs(level_config) do
		if v.level == vip_level then
			return v.need_exp
		end
	end
end

--获取提升到下一等级需要达到的数值
function VipWGData:GetLevelUpValue()
	local level_config = self.vip_auto_cfg.uplevel
	local index = self:GetVipLevel() + 1	--index从0开始，而且是下一级的数据
	if index < 0 then
		return 0
	end
	if index > #level_config then
		return level_config[#level_config].need_exp
	end
	local up_value = level_config[index].need_exp
	return up_value
end

--获得升到xx等级需要元宝数。下面算法不用可避免因为gm设置vip等级而引发问题
function VipWGData:GetNeedGoldToVip(target_vip_level)
	local cur_vip_level = self:GetVipLevel()
	local level_config = self.vip_auto_cfg.uplevel
	local total_gold = self:GetRechargeDiff() --升到下一级需要的元宝

	for k,v in pairs(level_config) do
		if v.level > cur_vip_level and v.level + 1 <= target_vip_level then
			total_gold = total_gold + v.need_exp
		end
	end
	return total_gold
end

--获取vip 等级描述
function VipWGData:GetVipLevelDesribe(vip_level)
	local level_config = self.vip_auto_cfg.uplevel
	local index = vip_level + 1
	if index < 0 or index > #level_config then
		return ""
	end
	local describe = level_config[index].desc
	local desc = string.gsub(describe, "\\n", "\n")
	return desc
end

--再充多少可升级
function VipWGData:GetRechargeDiff()
	local up_value = self:GetLevelUpValue() or 0
	local diff = up_value - self.recharge_exp
	return diff
end

--获取VIP礼包数据
function VipWGData:GetRewardItemData(vip_level)
	if vip_level < 1 or vip_level > self:GetMaxVIPLevel() then
		return
	end
	
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local pkg_data = self.vip_auto_cfg.level_reward[vip_level - 1].prof_one_item
	if role_prof == 1 then
		pkg_data = self.vip_auto_cfg.level_reward[vip_level - 1].prof_one_item
	elseif role_prof == 2 then
		pkg_data = self.vip_auto_cfg.level_reward[vip_level - 1].prof_two_item
	elseif role_prof == 3 then
		pkg_data = self.vip_auto_cfg.level_reward[vip_level - 1].prof_three_item
	else
		pkg_data = self.vip_auto_cfg.level_reward[vip_level - 1].prof_four_item
	end

	pkg_data.is_bind = 0
	local item_data_list = ItemWGData.Instance:GetGiftConfig(pkg_data.item_id).item_data
	--礼包， 礼包里的物品
	return pkg_data, item_data_list
end

function VipWGData:GetVipBuffCfg(vip_level)
	return self.vip_auto_cfg.vipbuff[vip_level]
end

function VipWGData:SetVipEndTime(time, vip_time_card_type)
	self.old_vip_end_time = self.end_time or -1
	self.end_time = time
	self.vip_time_card_type = vip_time_card_type
end

function VipWGData:GetVipTimeCardType()
	return self.vip_time_card_type
end

---[[ F2VIP贵族数据
function VipWGData:SetVipCardInfo(protocol)
	local info_list = {
		card_flag = bit:d2b(protocol.vip_card_active_flag),					-- vip卡激活标记
		last_card_seq = protocol.last_active_card_seq,						-- vip最后一张激活的卡
		endtime = protocol.vip_end_timestamp,			 					-- vip卡结束时间
		has_free_card = protocol.cant_use_expierence_card_flag == 0, 		-- 还有免费的卡可以体验
	}
	self.f2_vip_crad_info = info_list
	self:CheckVipActive()
	-- self:CheckVipUpLevel()
end

function VipWGData:GetVipCardInfo()
	return self.f2_vip_crad_info
end

function VipWGData:GetVipEndTime()
	local card_info = self:GetVipCardInfo()
	if card_info then
		return card_info.endtime
	end
	return 0
end

function VipWGData:GetVipLevel()
	if self:IsVip() then
		return self:GetRoleVipLevel()
	else
		return self:GetRoleVipPastLevel()
	end
end

function VipWGData:SetVipDailyGiftInfo(protocol)
	local info_list = {
		can_get_reward = protocol.fetch_daily_reward_flag == 0 				-- 能否领取每日礼包奖励
	}
	self.vip_daily_gift_info = info_list
end

function VipWGData:GetLastCardSeq()
	local card_info = self:GetVipCardInfo()
	return card_info and card_info.last_card_seq or 0
end

function VipWGData:CanGetDailyGiftReward()
	if self:IsVip() and self.vip_daily_gift_info and self.vip_daily_gift_info.can_get_reward then
		return true
	end
	return false
end

-- 是否领取过这张卡了
function VipWGData:IsGetThisCardBySeq(seq)
	local card_info = self:GetVipCardInfo()
	local card_flag = card_info and card_info.card_flag
	if card_flag and card_flag[32 - seq] == 1 then
		return true
	end
	return false
end

function VipWGData:IsVip()
	local vip_level = self:GetRoleVipLevel()
	return vip_level > 0
	-- local end_time = self:GetVipEndTime()
	-- local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- if vip_level > 0 and end_time > server_time then
	-- 	return true
	-- end
end

-- 是否花钱买的VIP
function VipWGData:IsBuyVip()
	local last_card_seq = self:GetLastCardSeq()
	local card_cfg = self:GetVipCardCfg(last_card_seq)
	if card_cfg.active_condition == self.VipCardActiveType.glod then
		return true
	end
	return false
end

--是否是vip体验卡
function VipWGData:IsVipTy()
	local card_info = self:GetVipCardInfo()
	if card_info and card_info.endtime > 0 then
		local card_cfg = self:GetVipCardCfg(card_info.last_card_seq)
		if card_cfg.active_condition == self.VipCardActiveType.online or card_cfg.active_condition == self.VipCardActiveType.day then
			return true
		end
	end
	return false
end

-- 是否是永久VIP
function VipWGData:IsForeverVip()
	local card_info = self:GetVipCardInfo()
	if card_info and card_info.endtime > 0 then
		local card_cfg = self:GetVipCardCfg(card_info.last_card_seq)
		if card_cfg.continue_days == 0 then
			return true
		end
	end
	return false
end

-- 检测VIP 付费激活 or 过期
function VipWGData:CheckVipActive()
	local is_vip = self:IsVip()
	if self.vip_is_active_mark == false and is_vip and self:IsBuyVip() then
		VipWGCtrl.Instance:OpenVipActiveView()
	end
	-- if self.vip_is_active_mark == nil and not is_vip then
	-- 	local endtime = self:GetVipEndTime()
	-- 	if endtime > 0 then
	-- 		VipWGCtrl.Instance:OpenInvalidView()
	-- 	end
	-- end
	self.vip_is_active_mark = is_vip
end

-- 检测VIP等级提升后
function VipWGData:CheckVipUpLevel()
	local role_vip_level = self:GetRoleVipLevel()
	local result_initial_flag = self:GetVipInitialFlag()
	if self.vip_temp_level and role_vip_level > self.vip_temp_level and (not result_initial_flag) then
		VipWGCtrl.Instance:OpenVipUpView()
	end
	self.vip_temp_level = role_vip_level
end

-- 有没有免费的卡可以领取
function VipWGData:CanGetFreeCard(seq)
	local card_info = self:GetVipCardInfo()
	if not card_info or not card_info.has_free_card then
		return false
	end
	if self:GetVipLevel() >= self:GetFreeCradMaxVipLevel() then
		return false
	end
	local mark = false
	local card_cfg_list = self:GetVipCardCfg(seq)
	local online_time_s = TimeWGCtrl.Instance:GetOnlineTimes()
	local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
	local check_func = function (card_cfg)
		local is_get_card = self:IsGetThisCardBySeq(card_cfg.seq)
		if is_get_card then
			return false
		end
		if card_cfg.active_condition == self.VipCardActiveType.online then
			return online_time_s >= card_cfg.active_value * 60
		elseif card_cfg.active_condition == self.VipCardActiveType.day then
			return login_day_count >= card_cfg.active_value
		end
	end
	if seq then
		mark = check_func(card_cfg_list)
	else
		for _,card in pairs(card_cfg_list) do
			if check_func(card) then
				mark = true
				seq = card.seq
				break
			end
		end
	end
	return mark, seq
end

function VipWGData:GetTiYanText(level)
	local level_config = self.vip_auto_cfg.uplevel[level]
	return level_config and level_config.getvip_show_desc
end

-- 检测还有没有免费的卡
function VipWGData:CheckHasFreeCard()
	local card_info = self:GetVipCardInfo()
	if not card_info or not card_info.has_free_card then
		return false
	end
	if self:GetVipLevel() >= self:GetFreeCradMaxVipLevel() then
		return false
	end
	local card_cfg_list = self:GetVipCardCfg()
	for _,card_cfg in ipairs(card_cfg_list) do
		if card_cfg.active_condition == self.VipCardActiveType.online or card_cfg.active_condition == self.VipCardActiveType.day then
			if not self:IsGetThisCardBySeq(card_cfg.seq) then
				return true, card_cfg.seq
			end
		end
	end
	return false
end

--检测是否达到vip等级显示称号.
function VipWGData:CheckHasFreeCardByVipLevel(seq)
	local card_cfg_list = self:GetVipCardCfg(seq)
	if card_cfg_list and card_cfg_list.show_seq == 1 then
		return self:GetVipLevel() <= seq
	end
	return false
end

-- 获取对应的称号ID.
function VipWGData:GetVipFreeCardTitleID(seq)
	local card_cfg_list = self:GetVipCardCfg(seq)
	return card_cfg_list and card_cfg_list.title_id
end

-- 免费送的卡最多可以升到VIP几
function VipWGData:GetFreeCradMaxVipLevel()
	local max_free_lv = 0
	local card_cfg_list = self:GetVipCardCfg()
	for _,card_cfg in ipairs(card_cfg_list) do
		if card_cfg.active_condition == self.VipCardActiveType.online or card_cfg.active_condition == self.VipCardActiveType.day then
			max_free_lv = max_free_lv >= card_cfg.vip_up_lv and max_free_lv or card_cfg.vip_up_lv
		end
	end
	return max_free_lv
end

function VipWGData:GetVipOtherInfo(key)
	local other_cfg = self.vip_auto_cfg.other[1]
	return other_cfg and other_cfg[key]
end

function VipWGData:GetVipCardCfg(seq)
	if not self.vip_auto_cfg.vip_card then
		return {}
	end
	if seq then
		for _,v in pairs(self.vip_auto_cfg.vip_card) do
			if v.seq == seq then
				return v
			end
		end
		return {}
	end
	return self.vip_auto_cfg.vip_card
end

function VipWGData:CheckCanGetVipCardReward(seq)
	local can_get_card = false
	local card_cfg = self:GetVipCardCfg(seq)
	if not card_cfg then
		return can_get_card
	end
	if card_cfg.active_condition == VipWGData.VipCardActiveType.online then
		local online_time_s = TimeWGCtrl.Instance:GetOnlineTimes()
		can_get_card = online_time_s >= card_cfg.active_value * 60
	elseif card_cfg.active_condition == VipWGData.VipCardActiveType.day then
		local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
		can_get_card = login_day_count >= card_cfg.active_value
	end
	return can_get_card
end

function VipWGData:GetForeverVipCfg()
	for _,v in pairs(self.vip_auto_cfg.vip_card) do
		-- if v.continue_days == 0 then
		if v.vip_up_lv == 6 then -- VIP6零元购
			return v
		end
	end
end
--]]

---[[ F2VIP零元购
function VipWGData:GetVIPZeroBuyCfg(key)
	local cfg_list = self.vip_auto_cfg.vip_zerobuy
	if cfg_list and key then
		return cfg_list[1] and cfg_list[1][key]
	end
end
--]]

--零元购界面跳转  大于0跳转  否则跳vip
function VipWGData:GetVIPZeorBuyIsOpenJump()
	local up_level = self:GetVIPZeroBuyCfg("arrive_level") or 0
	local jump_index = TabIndex.recharge_vip
	if up_level > 0 then
		jump_index = TabIndex.recharge_zerobuy
	end

	return jump_index
end

-- vip等级权限
function VipWGData:GetVipSpecPermissions(vip_type)
	local vip_level_cfg = self.vip_auto_cfg.level[vip_type]
	return vip_level_cfg
end

-- 可显示的vip等级权限.
function VipWGData:GetCanShowVipSpecPermissions()
	local vip_level_cfg = {}
	for key, value in pairs(self.vip_auto_cfg.level) do
		if value.is_show == 1 then
			table.insert(vip_level_cfg, value)
		end
	end

	table.sort(vip_level_cfg, SortTools.KeyLowerSorter("index"))

	return vip_level_cfg
end

function VipWGData:GetVipSpecPermissionsValue(vip_type)
	local vip_level_cfg = self.vip_auto_cfg.level[vip_type]
	if not vip_level_cfg then
		return 0
	end
	local vip_level = self:GetRoleVipLevel()
	return vip_level_cfg["param_" .. vip_level]
end

--获取最大得经验加成值
function VipWGData:GetVipExpMaxAdd()
	if self.max_exp_add_per_value then
		return self.max_exp_add_per_value
	end
	local cfg = self:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.EXP_ADD_PER)
	local max_level = self:GetMaxVIPLevel()
	local exp_add_buff = 0
	for i = 1, max_level do
		if exp_add_buff < cfg["param_".. i - 1] then
			exp_add_buff = cfg["param_".. i - 1]
		end
	end
	self.max_exp_add_per_value = exp_add_buff
	return exp_add_buff
end

--获取当前VIP经验加成
function VipWGData:GetCurExpAdd()
	local cfg = self:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.EXP_ADD_PER)
	local vip_level = self:GetRoleVipLevel()
	return cfg["param_".. vip_level]
end

-- 获取VIP礼包配置
function VipWGData:GetVIPGiftCfg(index)
	if not self.vip_giftbag_cfg then
		self.vip_giftbag_cfg = self.vip_auto_cfg.vip_giftbag_cfg
	end
	return self.vip_giftbag_cfg[index]
end

function VipWGData:GetAllVipGiftCfg()
	return self.vip_auto_cfg.vip_giftbag_cfg
end

function VipWGData:GetRechargeTabListCfg()
	local recharge_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k, v in pairs(self.vip_auto_cfg.recharge_cfg) do
		if role_level >= v.level and server_day >= v.open_day then
			table.insert(recharge_list, v)
		end
	end

	table.sort(recharge_list, SortTools.KeyLowerSorter("recharge_type"))
	return recharge_list
end

-- 设置vip礼包购买标识
function VipWGData:SetVipBuyFlag(vip_giftbag_flag)
	self.vip_gift_buy_list = bit:d2b_two(vip_giftbag_flag)
end

function VipWGData:GetVipBuyFlag(index)
	return self.vip_gift_buy_list and self.vip_gift_buy_list[index-1] == 1 or false
end

function VipWGData:HasVipBagAllBuy()
	if self.vip_gift_buy_list then
		for i = 0, self:GetVipLevel() - 1 do
			if self.vip_gift_buy_list[i] and self.vip_gift_buy_list[i] == 0 then
				return false
			end
		end
	end
	return true
end

-- 设置是否领取初始VIP奖励.
function VipWGData:SetIsFetchInitialVip(is_fetch_initial_vip)
	self.is_fetch_initial_vip = is_fetch_initial_vip
end

-- 是否未领取初始VIP奖励.
function VipWGData:GetIsFetchInitialVip()
	return self.is_fetch_initial_vip == 0
end

-- 获取主界面动画播放信息
function VipWGData:GetMainUITweenInfo()
	if not self.recharge_exp or not self.recharge_vip then
		return 
	end
	local info = {}
	info.need_effect = (self.recharge_exp ~= self.old_recharge_exp or self.recharge_vip ~= self.old_recharge_vip) and self.vip_exp_change_type == VIP_EXP_CHANGE_TYPE.ADD_VIP_EXP_TYPE_ITEM_COST
	--[[
	info.need_tween = (self.recharge_exp ~= self.old_recharge_exp or self.recharge_vip ~= self.old_recharge_vip)
	if info.need_tween then
		info.tween_num = self.recharge_vip - self.old_recharge_vip
	end
	info.start_level = self.old_recharge_vip
	-- 获取当前vip升级所需经验
	local need_exp = self:GetVipUpCondition(self.recharge_vip)
	info.target_value = self.recharge_exp / need_exp

	local is_vip = self:IsVip()
	local is_ty_vip = self:IsVipTy()
	local is_forever_vip = self:IsForeverVip()
	local vip_text = Language.Vip.MainUIBottom
	local bottom_name = ''
	if is_vip and not is_forever_vip then
		bottom_name = vip_text[2]
		info.end_time = self:GetVipEndTime()
	else
		bottom_name = vip_text[1]
	end
	info.bottom_name = bottom_name
	--]]
	return info
end

function VipWGData:ClearEffectData()
	self.old_recharge_exp = self.recharge_exp or 0
	self.old_recharge_vip = self.recharge_vip or 0
end

function VipWGData:SetVipInitialFlag(result)
	self.result_initial_flag = result
end

function VipWGData:GetVipInitialFlag()
	return self.result_initial_flag
end

function VipWGData:SetVipInitialInfo(old_vip_level, vip_level)
	self.initial_old_vip_level = old_vip_level
	self.initial_vip_level = vip_level
end

function VipWGData:GetVipInitialInfo()
	return self.initial_old_vip_level, self.initial_vip_level
end

-- ====================================== 贵族特权总览提示界面数据 ==================================================================
function VipWGData:InitVipAllPrivilegeTipsCfg()
	self.vip_all_privilege_page_info = {}
	self.vip_all_privilege_page_info[1] = { start_idx = 0, end_idx = 10 }
	self.vip_all_privilege_page_info[2] = { start_idx = 11, end_idx = 21 }
	self.vip_all_privilege_page_info[3] = { start_idx = 22, end_idx = 30 }
end

--获取页数.
function VipWGData:GetVipAllPrivilegeTipsPageInfo(idx)
	return self.vip_all_privilege_page_info[idx]
end
-- ====================================== 贵族特权总览提示界面数据 end ==============================================================