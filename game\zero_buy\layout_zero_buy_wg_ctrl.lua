require("game/zero_buy/layout_zero_buy_wg_data")
require("game/zero_buy/layout_zero_buy_view")
require("game/zero_buy/zero_buy_btn")
require("game/zero_buy/zerobuy_log_view")
LayoutZeroBuyWGCtrl = LayoutZeroBuyWGCtrl or BaseClass(BaseWGCtrl)

function LayoutZeroBuyWGCtrl:__init()
	if LayoutZeroBuyWGCtrl.Instance then
		ErrorLog("[LayoutZeroBuyWGCtrl] attempt to create singleton twice!")
		return
	end

	LayoutZeroBuyWGCtrl.Instance = self

	self.view = LayoutZeroBuyView.New(GuideModuleName.LayoutZeroBuyView)
	self.data = LayoutZeroBuyWGData.New()
	self.log_view = ZeroBuyLogView.New()
	self:RegisterAllProtocols()
	self.zero_old_status = -1
	self.xia_jia_index = -1
end

function LayoutZeroBuyWGCtrl:__delete()
	LayoutZeroBuyWGCtrl.Instance = nil
	self.zero_old_status = nil
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.log_view then
		self.log_view:DeleteMe()
		self.log_view = nil
	end

end

function LayoutZeroBuyWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSZeroBuyReq)--8912
	self:RegisterProtocol(SCZeroBuyInfo,"OnSCZeroBuyInfo")--8913
	self:RegisterProtocol(SCZeroBuyLog,"OnSCZeroBuyLog")--8914
end

function LayoutZeroBuyWGCtrl:SendCSZeroBuyReq(op_type, param)
	local protocol = ProtocolPool.Instance:GetProtocol(CSZeroBuyReq)
	protocol.type = op_type or 0
	protocol.param = param or 0
	protocol:EncodeAndSend()
end

function LayoutZeroBuyWGCtrl:OnSCZeroBuyInfo(protocol)
	self.data:SetProtocol(protocol)

	local other_cfg = self.data:GetZeroBuyOtherCfg()
	local role_level = RoleWGData.Instance:GetRoleLevel()
    --protocol.open_flag == 1 and 活动开启 or 活动关闭
	local activity_open_condition = protocol.open_flag == 1 or self.data:CheckZeroOpenState() == 1
	-- print_error("FFF======= protocol.open_flag", protocol.open_flag, self.data:CheckZeroOpenState(), activity_open_condition)
	local activity_status = activity_open_condition and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE

	if self.zero_old_status ~= activity_status then
		-- print_error("FFFF======= activity_status", activity_status, activity_status == ACTIVITY_STATUS.OPEN)
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.ZEROBUY, activity_status, 0, nil, nil, RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
		self.zero_old_status = activity_status
	end
	
	if self.view:IsOpen() then
		self.view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.ZeroBuy)
end

function LayoutZeroBuyWGCtrl:OnSCZeroBuyLog(protocol)
	self.data:SetBuyLogProtocol(protocol)
end

function LayoutZeroBuyWGCtrl:OpenZeroBuyLogView()
	if not self.log_view:IsOpen() then
		self.log_view:Open()
	end
end

--这里重新获取未下架的商品,否则跳转时对应不上
function LayoutZeroBuyWGCtrl:GetZeroBuyDataByIndex(index)
	local all_cfg = self.data:GetZeroBuyData()
	for cfg_index, cfg in pairs(all_cfg) do
		if cfg.id == index then
			return cfg_index, cfg
		end
	end
end

function LayoutZeroBuyWGCtrl:OpenZeroBuyView()
	if not self.view:IsOpen() then
		self.view:Open()
	end
end

function LayoutZeroBuyWGCtrl:SetZeroShowIndex(index)
	self.xia_jia_index = index
end

function LayoutZeroBuyWGCtrl:GetZeroShowIndex()
	return self.xia_jia_index
end

function LayoutZeroBuyWGCtrl:OpenZeroBuyByIndex(index)
	local cfg_index, cfg = self:GetZeroBuyDataByIndex(index)
	if cfg then
		if cfg.state == 4 then--已下架
			SysMsgWGCtrl.Instance:ErrorRemind(Language.ZeroBuy.IsXiaJia)
		else
			self:SetZeroShowIndex(cfg_index)
			self:OpenZeroBuyView()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ZeroBuy.IsXiaJia)
	end
end
