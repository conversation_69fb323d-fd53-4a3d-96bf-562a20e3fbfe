RoleBagView = RoleBagView or BaseClass(SafeBaseView)

function RoleBagView:ShowCallBack()
	LongZhuWGCtrl.Instance:SendLongZhuOper(LONGZHU_OPER_TYPE.LONGZHU_OPER_TYPE_ALL_INFO)
end

function RoleBagView:InitLongZhuView()
	-- self.node_list["title_view_name"].text.text = Language.LongZhu.ViewTitleName

	-- if self.node_list.RawImage_tongyong then
	-- 	local bundle, asset = ResPath.GetRawImagesJPG("a3_mgxj_bg1")
	-- 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end

	self.select_longzhu_item_id = 1
	self.consume_item_data = {}

	if not self.lz_item_cell then
		self.lz_item_cell = ItemCell.New(self.node_list.lz_item_cell_root)
	end

	if not self.longzhu_tog_item_list then
		self.longzhu_tog_item_list = {}

		for i = 1, 7 do
			self.longzhu_tog_item_list[i] = LongZhuToggleItemRender.New(self.node_list["long_zhu_item" .. i])
			self.longzhu_tog_item_list[i]:SetIndex(i)
			self.node_list["long_zhu_item" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickLongZhuTogItem, self, i, true))
		end
	end

	if not self.item_data_event then
        self.item_data_event = BindTool.Bind(self.ItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
    end

	if not self.cur_attr_list then
		self.cur_attr_list = {}

		for i = 1, 3 do
			local cell = CommonAttrRender.New(self.node_list["cur_attr_" .. i])
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
			-- cell:SetAttrValuePrefix("+")
			self.cur_attr_list[i] = cell
		end
	end

	if not self.next_attr_list then
		self.next_attr_list = {}

		for i = 1, 3 do
			local cell = CommonAttrRender.New(self.node_list["next_attr_" .. i])
			cell:SetIndex(i)
			cell:SetAttrNameNeedSpace(true)
			-- cell:SetAttrValuePrefix("+")
			self.next_attr_list[i] = cell
		end
	end

	self:SetRightPanelState(true)
	self.show_right_panel_flag = -1

	XUI.AddClickEventListener(self.node_list.lz_up_level_btn, BindTool.Bind(self.OnClickItemUpLevelBtn, self))
	-- XUI.AddClickEventListener(self.node_list.benediction_btn, BindTool.Bind(self.OnClickBenedictionBtn, self))
	self.node_list.benediction_btn.toggle:AddClickListener(BindTool.Bind(self.OnClickBenedictionBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_txt, BindTool.Bind(self.OnClickActBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_show_cg, BindTool.Bind(self.OnClickShowCgBtn, self))



	local sizeDelta = ((self.node_list.des_text or {}).rect or {}).sizeDelta
end

function RoleBagView:OnClickShowCgBtn()
	local cg_bundle = "cg/a3_cg_skill_prefab"
	local cg_asset = "A3_CG_Skill"

	CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset))
end

function RoleBagView:LongzhuReleaseCallBack()
	self.drag_select_index = nil

	if self.lz_item_cell then
		self.lz_item_cell:DeleteMe()
		self.lz_item_cell = nil
	end

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	if self.longzhu_tog_item_list then
		for k, v in pairs(self.longzhu_tog_item_list) do
			v:DeleteMe()
		end

		self.longzhu_tog_item_list = nil
	end

	if self.cur_attr_list then
		for k, v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end

		self.cur_attr_list = nil
	end

	if self.next_attr_list then
		for k, v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end

		self.next_attr_list = nil
	end
end

function RoleBagView:FlushLongZhu(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushLongZhuItemData()
			self:SetLongZhuTogSelect()
			self:FlushLongZhuSkillInfo()
		elseif k == "long_zhu_info" then
			self:FlushLongZhuItemData()
			self:SetLongZhuTogSelect()
			self:FlushLongZhuSkillInfo()
		elseif k == "select_long_zhu" then
			self:SetLongZhuTogSelect(v.item_id)
		end
	end
end

function RoleBagView:FlushLongZhuItemData()
	if self.longzhu_tog_item_list then
		for k, v in pairs(self.longzhu_tog_item_list) do
			v:Flush()
		end
	end
end

function RoleBagView:SetLongZhuTogSelect(select_itemid)
	local function DoTogSelect(index, need_change_right_panel)
		if self.node_list["long_zhu_item" .. index].toggle.isOn == true then
			self:OnClickLongZhuTogItem(index)
		else
			self.node_list["long_zhu_item" .. index].toggle.isOn = true
		end
	end

	if select_itemid and select_itemid > 0 then
		local cfg = LongZhuWGData.Instance:GetLongZhuCfgByItemId(select_itemid)

		if cfg then
			DoTogSelect(cfg.id, true)
		end
	else
		if LongZhuWGData.Instance:IsRemindById(self.select_longzhu_item_id) then
			DoTogSelect(self.select_longzhu_item_id, true)
		else
			for id = 1, LONGZHU_TYPE_MAX do
				if LongZhuWGData.Instance:IsRemindById(id) then
					DoTogSelect(id, true)
					return
				end
			end

			DoTogSelect(self.select_longzhu_item_id)
		end
	end
end

function RoleBagView:OnClickLongZhuTogItem(index, need_change_right_panel)
	self.select_longzhu_item_id = index
	self:FlushLongZhuItemInfo()

	if need_change_right_panel then
		self:SetRightPanelState(false)
	end
end

function RoleBagView:FlushLongZhuItemInfo()
	-- 按钮文本
	local level = LongZhuWGData.Instance:GetLongZhuLevelById(self.select_longzhu_item_id)
	self.node_list.lz_up_level_lbl.text.text = (level > 0) and Language.LongZhu.UpLevel or Language.LongZhu.Active
	self.node_list.text_info_title.text.text = (level > 0) and Language.LongZhu.NextStr or Language.LongZhu.ActiveStr
	-- 属性
	local now_cfg = LongZhuWGData.Instance:GetLongZhuCfg(self.select_longzhu_item_id, level)
	local nex_cfg = LongZhuWGData.Instance:GetLongZhuCfg(self.select_longzhu_item_id, level + 1)

	-- self.node_list.lz_name.text.text = now_cfg and now_cfg.name or nex_cfg.name or ""
	-- self.node_list.lz_attr_name.text.text = string.format("%sLv.%d", now_cfg and now_cfg.attr_name or nex_cfg.attr_name or "", level)
	self.node_list.lz_attr_name.text.text = now_cfg and now_cfg.name or nex_cfg.name or ""
	self.node_list.text_info_level.text.text = level

	if level > 0 then
		self.node_list.cur_attr_panel:CustomSetActive(true)
		local attr_data = LongZhuWGData.Instance:GetLongZhuAttrList2(now_cfg)
		for k, v in ipairs(self.cur_attr_list) do
			v:SetData(attr_data[k])
		end

		local is_max_level = IsEmptyTable(nex_cfg)
		self.node_list.next_attr_panel:CustomSetActive(not is_max_level)
		if not is_max_level then
			local attr_data = LongZhuWGData.Instance:GetLongZhuAttrList2(nex_cfg)

			for k, v in ipairs(self.next_attr_list) do
				v:SetData(attr_data[k])
			end
		end
	else
		self.node_list.cur_attr_panel:CustomSetActive(false)
		
		if not IsEmptyTable(nex_cfg) then
			self.node_list.next_attr_panel:CustomSetActive(true)
			local attr_data = LongZhuWGData.Instance:GetLongZhuAttrList2(nex_cfg)

			for k, v in ipairs(self.next_attr_list) do
				v:SetData(attr_data[k])
			end
		end
	end

	---[[ 未激活的时候把下级属性当当前属性显示
	if level == 0 then
		now_cfg = nex_cfg
	end
	--]]

	---[[ 当前等级属性
	-- if now_cfg then
	-- 	local attr_data = LongZhuWGData.Instance:GetLongZhuAttrList2(now_cfg)
	-- 	for k, v in ipairs(self.attr_list) do
	-- 		v:SetData(attr_data[k])
	-- 	end
	-- end
	--]]

	---[[ 激活 or 升级 消耗物品
	if nex_cfg then
		self.consume_item_data = {item_id = nex_cfg.consume_item, need_num = nex_cfg.consume_num, has_num = 0}
	else
		self.consume_item_data = nil
		self.node_list.lz_up_red_point:SetActive(false)
	end
	--]]

	self.node_list.lz_up_level_btn:SetActive(nex_cfg ~= nil)
	self.node_list.lz_max_level_img:SetActive(nex_cfg == nil)

	--消耗
	self:FlushLongZhuItemConst()
	self:FlushLongZhuTotalCapValue()

	local bundle, asset = ResPath.GetLongZhuImage("a3_mgxj_icon" .. self.select_longzhu_item_id)
	self.node_list.long_zhu_img.image:LoadSprite(bundle, asset, function ()
		self.node_list.long_zhu_img.image:SetNativeSize()
	end)
end

function RoleBagView:FlushLongZhuItemConst()
	local item_data = self.consume_item_data
	if item_data then
		item_data.has_num = ItemWGData.Instance:GetItemNumInBagById(item_data.item_id)
		local color_str = item_data.has_num >= item_data.need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
		local num_str = string.format("%d/%d", item_data.has_num, item_data.need_num)

		self.lz_item_cell:SetData(item_data)
		self.lz_item_cell:SetRightBottomColorText(num_str, color_str)
		self.lz_item_cell:SetRightBottomTextVisible(true)
		self.node_list.lz_up_red_point:SetActive(item_data.has_num >= item_data.need_num)
	else
		local level = LongZhuWGData.Instance:GetLongZhuLevelById(self.select_longzhu_item_id)
		local now_cfg = LongZhuWGData.Instance:GetLongZhuCfg(self.select_longzhu_item_id, level)
		if now_cfg then
			self.lz_item_cell:SetData({item_id = now_cfg.consume_item})
		end
	end
end

-- 需要修改， 要加上注释的战力
function RoleBagView:FlushLongZhuTotalCapValue()
	local cap = 0
	local is_active = LongZhuWGData.Instance:HasLongZhuActive()
	if is_active then
		local lz_cap_value = 0
		local level = 0
		local now_cfg = nil
		for id = 1, LONGZHU_TYPE_MAX do
			level = LongZhuWGData.Instance:GetLongZhuLevelById(id)
			if level > 0 then
				now_cfg = LongZhuWGData.Instance:GetLongZhuCfg(id, level)
				if now_cfg then
					local attr_list,cap_value = LongZhuWGData.Instance:GetLongZhuAttrList(now_cfg, true)
					lz_cap_value = lz_cap_value + cap_value
				end
			end
		end

		cap = cap + lz_cap_value

	end

	local longzhu_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	if longzhu_level > 0 then
		local skill_id = LongZhuWGData.Instance:GetOtherCfg("skill_id")
		local skill_cfg = SkillWGData.Instance:GetOtherSkillCfg(skill_id, longzhu_level)
		cap = cap + AttributeMgr.GetCapability(nil, skill_cfg)
	end

	self.node_list.lz_cap_label.text.text = cap
end

function RoleBagView:FlushLongZhuSkillInfo()
	-- 技能图标
	local longzhu_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	local skill_id = LongZhuWGData.Instance:GetOtherCfg("skill_id")
	local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, longzhu_level > 0 and longzhu_level or 1)
	if clien_skill_cfg then
		self.node_list.benediction_img.image:LoadSprite(ResPath.GetSkillIconById(clien_skill_cfg.icon_resource))
	end

	--XUI.SetGraphicGrey(self.node_list.benediction_img, longzhu_level <= 0)
	-- self.node_list.mask:SetActive(longzhu_level <= 0)
	-- self.node_list.suo_img:SetActive(longzhu_level <= 0)
	self.node_list.text_active_tips:SetActive(longzhu_level <= 0)
	self.node_list.longzhu_level_text.text.text = string.format(Language.LongZhu.LongZhuTotalLevelDesc2, longzhu_level)

	local active_num = LongZhuWGData.Instance:GetLongZhuActiveNum()
	self.node_list.title_label.text.text = Language.LongZhu.LongZhuLevelDesc
	self.node_list.text_level.text.text = longzhu_level
	local now_cfg_list, nex_cfg_list, now_lock_cfg_list, nex_lock_cfg_list = LongZhuWGData.Instance:GetLongZhuSkillDescList2()

	---[[ 描述解析(变颜色)
	local now_desc_list = {}
	local nex_desc_list = {}
	local now_lock_desc_list = {}
	local nex_lock_desc_list = {}
	local index = 0
	local temp_level = longzhu_level > 0 and longzhu_level or 1
	local cur_skill_desc, nex_skill_desc = "", ""
	for k,v in pairs(now_cfg_list) do
		if v.skill_type > 1 then
			local now_data = {}
			index = k
			now_data.status = 2
			now_data.desc = ToColorStr(v.skill_desc, COLOR3B.C11)
			now_desc_list[index] = now_data
		else
			cur_skill_desc = v.skill_desc
		end
	end
	for k,v in pairs(now_lock_cfg_list) do
		local now_data = {}
		index = k
		now_data.status = 2
		now_data.desc = ToColorStr(v.skill_desc, COLOR3B.C12)
		now_lock_desc_list[index] = now_data
	end

	for k,v in pairs(nex_cfg_list) do
		if v.skill_type > 1 then
			local nex_data = {}
			-- 下级效果
			index = k
			local desc = v.skill_desc
			desc = ToColorStr(desc, COLOR3B.C11)
			if temp_level + 1 == v.active_skill_level then
				desc = desc .. "<sprite name=\"230\">"
			end

			nex_data.status = 1
			nex_data.desc = desc
			nex_desc_list[index] = nex_data
		else
			-- 下级效果
			local desc = v.skill_desc
			desc = ToColorStr(desc, COLOR3B.C11)
			if temp_level + 1 == v.active_skill_level then
				desc = desc .. "<sprite name=\"230\">"
			end
			nex_skill_desc = desc
		end
	end

	for k,v in pairs(nex_lock_cfg_list) do
		local now_data = {}
		index = k
		now_data.status = 2
		now_data.desc = ToColorStr(v.skill_desc, COLOR3B.C12)
		nex_lock_desc_list[index] = now_data
	end

	now_desc_list = SortTableKey(now_desc_list)
	nex_desc_list = SortTableKey(nex_desc_list)
	now_lock_desc_list = SortTableKey(now_lock_desc_list)
	nex_lock_desc_list = SortTableKey(nex_lock_desc_list)
	
	--]]

	---[[ 当前等级描述
	if longzhu_level > 0 then
		self.node_list.now_desc_title.text.text = Language.LongZhu.NowStr
		self.node_list.now_effect_desc.text.text = self:GetConditionDesc()
		-- self.node_list.now_skill_desc.text.text = cur_skill_desc
		EmojiTextUtil.ParseRichText(self.node_list["now_skill_desc"].emoji_text, cur_skill_desc, 20, COLOR3B.C11)

		-- for k,v in ipairs(self.now_skill_list) do
		-- 	v:SetData(now_desc_list[k])
		-- end
		for i = 1, 8 do
			local data = now_desc_list[i]
			local has_data = not IsEmptyTable(data)
			self.node_list["now_desc_p_" .. i]:CustomSetActive(has_data)

			if has_data then
				local text = self.node_list["now_desc_" .. i].emoji_text
				EmojiTextUtil.ParseRichText(text, data.desc)
				-- local bounds = TMPUtil.GetTMPBounds(text, text.text, 282, true)
				-- -- 设置文本宽高
				-- local tmp_rect = text:GetComponent(typeof(UnityEngine.RectTransform))
				-- tmp_rect.sizeDelta = bounds
			end
		end

		self.node_list.now_lock_desc_root:CustomSetActive(#now_lock_desc_list >0)
		for i = 1, 8 do
			local data = now_lock_desc_list[i]
			local has_data = not IsEmptyTable(data)
			self.node_list["now_lock_desc_p_" .. i]:CustomSetActive(has_data)
			if has_data then
				self.node_list["now_lock_" .. i]:CustomSetActive(true)
				local text = self.node_list["now_lock_desc_" .. i].emoji_text
				EmojiTextUtil.ParseRichText(text, data.desc)
				-- local bounds = TMPUtil.GetTMPBounds(text, text.text, 282, true)
				-- -- 设置文本宽高
				-- local tmp_rect = text:GetComponent(typeof(UnityEngine.RectTransform))
				-- tmp_rect.sizeDelta = bounds
				self.node_list["next_lock_" .. i]:CustomSetActive(false)
			end
		end
	end
	self.node_list.now_desc_root:SetActive(longzhu_level > 0)
	
	--]]

	

	-- 下一等级描述
	local max_level = LongZhuWGData.Instance:GetMaxLongZhuSkillLevel()
	-- 解锁和升级提示共用一个控件  因为只会显示一个
	self.node_list.active_desc_root:SetActive( (not (longzhu_level > 0)) or (longzhu_level < max_level))
	self.node_list.max_level_root:SetActive(longzhu_level >= max_level)
	self.node_list.nex_desc_root:SetActive(longzhu_level < max_level)
	if longzhu_level >= max_level then
		return
	end

	if longzhu_level == 0 then
		self.node_list.next_desc_title.text.text = Language.LongZhu.ActiveStr
	elseif longzhu_level == max_level then
		self.node_list.next_desc_title.text.text = Language.LongZhu.NowStr
	else
		self.node_list.next_desc_title.text.text = Language.LongZhu.NextStr
	end
	
	self.node_list.next_effect_desc.text.text = self:GetConditionDesc(true)
	local next_skill_desc_text = self.node_list["next_skill_desc"].text
	EmojiTextUtil.ParseRichText(next_skill_desc_text, nex_skill_desc, 20, COLOR3B.C11)
	-- -- 设置文本宽高
	-- local bounds = TMPUtil.GetTMPBounds(next_skill_desc_text, next_skill_desc_text.text, 282, true)
	-- local tmp_rect = next_skill_desc_text:GetComponent(typeof(UnityEngine.RectTransform))
	-- tmp_rect.sizeDelta = bounds
	-- self.node_list.next_skill_desc.text.text = nex_skill_desc
	-- for k,v in ipairs(self.nex_skill_list) do
	-- 	v:SetData(nex_desc_list[k])
	-- end
	for i = 1, 8 do
		local data = nex_desc_list[i]
		local has_data = not IsEmptyTable(data)
		self.node_list["next_desc_p_" .. i]:CustomSetActive(has_data)

		if has_data then
			local text = self.node_list["next_desc_" .. i].text
			EmojiTextUtil.ParseRichText(self.node_list["next_desc_" .. i].text, data.desc)
			-- local bounds = TMPUtil.GetTMPBounds(text, text.text, 282, true)
			-- -- 设置文本宽高
			-- local tmp_rect = text:GetComponent(typeof(UnityEngine.RectTransform))
			-- tmp_rect.sizeDelta = bounds
			self.node_list["next_lock_" .. i]:CustomSetActive(false)
		end
	end
	self.node_list.now_lock_desc_root:CustomSetActive(#nex_lock_desc_list >0)
	for i = 1, 8 do
		local data = nex_lock_desc_list[i]
		local has_data = not IsEmptyTable(data)
		self.node_list["next_lock_desc_p_" .. i]:CustomSetActive(has_data)
		if has_data then
			self.node_list["next_lock_" .. i]:CustomSetActive(true)
			local text = self.node_list["next_lock_desc_" .. i].emoji_text
			EmojiTextUtil.ParseRichText(text, data.desc)
			-- local bounds = TMPUtil.GetTMPBounds(text, text.text, 282, true)
			-- -- 设置文本宽高
			-- local tmp_rect = text:GetComponent(typeof(UnityEngine.RectTransform))
			-- tmp_rect.sizeDelta = bounds
		end
	end
	
	self.node_list.space_root:CustomSetActive(longzhu_level > 0 and longzhu_level < max_level)
end

function RoleBagView:GetConditionDesc(is_next)
	local next_condition = ""
	local longzhu_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	if longzhu_level >= LONGZHU_TYPE_MAX then
		local avg_level = LongZhuWGData.Instance:GetLongZhuAvgSkillLevel()
		if is_next then
			avg_level = avg_level + 1
		end

		-- avg_level = ToColorStr(avg_level, COLOR3B.DEFAULT_NUM)
		next_condition = string.format(Language.LongZhu.LongZhuDesc_level, avg_level)
	else
		if is_next then
			longzhu_level = longzhu_level + 1
		end

		-- longzhu_level = ToColorStr(longzhu_level, COLOR3B.DEFAULT_NUM)
		next_condition = string.format(Language.LongZhu.LongZhuTotalLevelDesc, longzhu_level)
	end

	-- next_condition = ToColorStr(next_condition, COLOR3B.DEFAULT)
	return next_condition
end

function RoleBagView:OnClickItemUpLevelBtn()
	if self.consume_item_data then
		local item_data = self.consume_item_data
		if item_data.has_num < item_data.need_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.LongZhu.NoStuff)
			TipWGCtrl.Instance:OpenItemTipGetWay(item_data)
			return
		end
	end

	LongZhuWGCtrl.Instance:SendLongZhuOper(LONGZHU_OPER_TYPE.LONGZHU_OPER_TYPE_UP_LEVEL, self.select_longzhu_item_id)
end

function RoleBagView:ItemDataChange(change_item_id)
	if self.consume_item_data and self.consume_item_data.item_id == change_item_id then
		self:FlushLongZhuItemConst()
		for k, v in pairs(self.longzhu_tog_item_list) do
			v:FlushRemind()
		end
	end
end

function RoleBagView:OnClickActBtn()
	ViewManager.Instance:Open(GuideModuleName.GoldStoneView)
end

-- 灵珠祝福
function RoleBagView:OnClickBenedictionBtn()
	-- LongZhuWGCtrl.Instance:OpenLongZhuTip()
	self:SetRightPanelState(true)
end

-- 灵珠激活 or 升级
function RoleBagView:OnClickUpLevelBtn()
	if self.consume_item_data then
		local item_data = self.consume_item_data
		if item_data.has_num < item_data.need_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.LongZhu.NoStuff)
			TipWGCtrl.Instance:OpenItemTipGetWay(item_data)
			return
		end
	end

	LongZhuWGCtrl.Instance:SendLongZhuOper(LONGZHU_OPER_TYPE.LONGZHU_OPER_TYPE_UP_LEVEL, self.long_zhu_id)
end

function RoleBagView:SetRightPanelState(is_show_skill)
	if is_show_skill then
		if self.show_right_panel_flag ~= 1 then
			self.node_list.info_panel:CustomSetActive(false)
			self.node_list.skill_panel:CustomSetActive(true)
			-- self.node_list.skill_panel.transform:DOLocalMove(Vector3(0, 0, 0), 0.5)
			-- self.node_list.info_panel.transform:DOLocalMove(Vector3(500, 0, 0), 0.5)
			-- self.node_list.skill_panel.canvas_group:DoAlpha(0, 1, 0.5)
			-- self.node_list.info_panel.canvas_group:DoAlpha(1, 0, 0.5)
			-- self.show_right_panel_flag = 1
		end
	else
		if self.show_right_panel_flag ~= 2 then
			self.node_list.info_panel:CustomSetActive(true)
			self.node_list.skill_panel:CustomSetActive(false)
			-- self.node_list.info_panel.transform:DOLocalMove(Vector3(0, 0, 0), 0.5)
			-- self.node_list.skill_panel.transform:DOLocalMove(Vector3(500, 0, 0), 0.5)
			-- self.node_list.skill_panel.canvas_group:DoAlpha(1, 0, 0.5)
			-- self.node_list.info_panel.canvas_group:DoAlpha(0, 1, 0.5)
			-- self.show_right_panel_flag = 2
		end
	end
end

