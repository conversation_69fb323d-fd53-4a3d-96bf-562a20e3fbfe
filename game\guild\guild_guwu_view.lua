--仙盟守护鼓舞界面

GuildFbGuWuView = GuildFbGuWuView or BaseClass(SafeBaseView)

local money_type = {}
money_type.coin = 65536
money_type.xianyu = 65534
money_type.bangyu = 65533

function GuildFbGuWuView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_guwu_view")
	self.role_data_event = BindTool.Bind1(self.RoleDataChangeCallback, self)
end

function GuildFbGuWuView:ReleaseCallBack()
	if self.gold_cell_list then
		for k,v in pairs(self.gold_cell_list) do
			v:DeleteMe()
		end
		self.gold_cell_list = nil
	end
	if self.coin_cell_list then
		for k,v in pairs(self.coin_cell_list) do
			v:DeleteMe()
		end
		self.coin_cell_list = nil
	end

	self.old_gold_times = nil
	self.old_coin_times = nil

	if RoleWGData.Instance then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_event)
	end
end

function GuildFbGuWuView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.Guild.SHGuWu
    XUI.AddClickEventListener(self.node_list["btn_coin"], BindTool.Bind(self._InternalOnClickGuWu, self, GUILD_SHOUHU_GUWU_TYPE.Coin))
	XUI.AddClickEventListener(self.node_list["btn_gold"], BindTool.Bind(self._InternalOnClickGuWu, self, GUILD_SHOUHU_GUWU_TYPE.Gold))
	RoleWGData.Instance:NotifyAttrChange(self.role_data_event, {"bind_gold", "gold"})
end

function GuildFbGuWuView:ShowIndexCallBack()
	self:Flush()
end

function GuildFbGuWuView:CloseCallBack()
	self.old_gold_times = nil
	self.old_coin_times = nil
end

function GuildFbGuWuView:OnFlush(param_t)
	self:_InternalFlushAllEffect()
	self:_InternalFlushAllBtn()
	self:_InternalFlushMoneyComsume()
	self:_InternalFlushSuccessEffect()
end

function GuildFbGuWuView:RoleDataChangeCallback(key, value)
	if key == "gold" or key == "bind_gold" then
		self:_InternalFlushMoneyComsume()
	end
end

function GuildFbGuWuView:_InternalOnClickGuWu(guwu_type)
	local gold_times, coin_times = GuildWGData.Instance:GetGuWuTimes()
	local gold_total_times, coin_total_times = GuildWGData.Instance:GetGuWuTotalTimes()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local gold_guwu_cost, coin_guwu_cost =  GuildWGData.Instance:GetGuWuConsume()

	if guwu_type == GUILD_SHOUHU_GUWU_TYPE.Coin then
		if coin_times >= coin_total_times then
			return
		end
		if coin_guwu_cost > main_role_vo.coin then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = money_type.coin})
			return
		end
		GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GuWu, GUILD_SHOUHU_GUWU_TYPE.Coin)
	elseif guwu_type == GUILD_SHOUHU_GUWU_TYPE.Gold then
		if gold_times >= gold_total_times then
			return
		end

		if gold_guwu_cost > main_role_vo.bind_gold and gold_guwu_cost > main_role_vo.gold then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = money_type.bangyu})
			return
		end
		GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GuWu, GUILD_SHOUHU_GUWU_TYPE.Gold)
	end
end

--按钮相关
function GuildFbGuWuView:_InternalFlushAllBtn()
	local gold_times, coin_times = GuildWGData.Instance:GetGuWuTimes()
	local gold_total_times, coin_total_times = GuildWGData.Instance:GetGuWuTotalTimes()

	local gold_color = gold_times < gold_total_times and COLOR3B.GREEN or COLOR3B.RED
	local coin_color = coin_times < coin_total_times and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.gold_number_enc.text.text = string.format(Language.Guild.SHGuWuBtnText, gold_color, gold_times, gold_total_times)
	self.node_list.coin_number_enc.text.text = string.format(Language.Guild.SHGuWuBtnText, coin_color, coin_times, coin_total_times)
	XUI.SetButtonEnabled(self.node_list["btn_gold"], gold_times < gold_total_times)
	XUI.SetButtonEnabled(self.node_list["btn_coin"], coin_times < coin_total_times)
end

--刷新元宝铜币消耗
function GuildFbGuWuView:_InternalFlushMoneyComsume()
	if not self.node_list.gold_comsume or not self.node_list.coin_comsume then return end
	local gold_guwu_cost, coin_guwu_cost =  GuildWGData.Instance:GetGuWuConsume()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local gold_color = (gold_guwu_cost <= main_role_vo.bind_gold or gold_guwu_cost <= main_role_vo.gold) and COLOR3B.GREEN or COLOR3B.RED
	local coin_color = coin_guwu_cost <= main_role_vo.coin and COLOR3B.GREEN or COLOR3B.RED
	-- self.node_list.gold_comsume.text.text = string.format(Language.Guild.SHGuWuCost, gold_color, CommonDataManager.ConverExp(BigNumFormat(main_role_vo.bind_gold)) , gold_guwu_cost)
	-- self.node_list.coin_comsume.text.text = string.format(Language.Guild.SHGuWuCost, coin_color, CommonDataManager.ConverExp(BigNumFormat(main_role_vo.coin)) , coin_guwu_cost)
	
	self.node_list.gold_comsume.text.text = gold_guwu_cost  --ToColorStr(gold_guwu_cost,gold_color)
	self.node_list.coin_comsume.text.text = coin_guwu_cost	--ToColorStr(coin_guwu_cost,coin_color)
end

--成功特效检测
function GuildFbGuWuView:_InternalFlushSuccessEffect()
	local gold_times, coin_times = GuildWGData.Instance:GetGuWuTimes()
	if self.old_gold_times and self.old_gold_times ~= gold_times then
		EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["success_effect_root"])
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.SHGuWuFloatTip, GuildWGData.Instance:GetOnceGoldGuWuEffect()))
	end
	if self.old_coin_times and self.old_coin_times ~= coin_times then
		EffectManager.Instance:PlayCommonSuccessEffect(self.node_list["success_effect_root"])
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Guild.SHGuWuFloatTip, GuildWGData.Instance:GetOnceCoinGuWuEffect()))
	end
	self.old_gold_times = gold_times
	self.old_coin_times = coin_times
end

--效果加成
function GuildFbGuWuView:_InternalFlushAllEffect()
	local gold_ef = GuildWGData.Instance:GetOnceGoldGuWuEffect()
	local coin_ef = GuildWGData.Instance:GetOnceCoinGuWuEffect()
	local total_ef = GuildWGData.Instance:GetCurGuWuTotalEffect()
	if gold_ef and coin_ef and total_ef then
		self.node_list.cur_effect_self.text.text = string.format(Language.Guild.SHTotalGuWuEffect, total_ef)
		self.node_list.gold_eff1.text.text = string.format(Language.Guild.SHGuWuEffect, gold_ef)
		self.node_list.coin_eff1.text.text = string.format(Language.Guild.SHGuWuEffect, coin_ef)
	end
end