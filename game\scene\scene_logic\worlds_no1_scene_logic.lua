-- 天下第一战斗场景
WorldsNO1SceneLogic = WorldsNO1SceneLogic or BaseClass(CrossServerSceneLogic)

function WorldsNO1SceneLogic:__init()
	self.enter_time = 0
end

function WorldsNO1SceneLogic:__delete()
	self.enter_time = nil
end

-- 进入场景
function WorldsNO1SceneLogic:Enter(old_scene_type, new_scene_type)
	self.enter_time = TimeWGCtrl.Instance:GetServerTime()
	ViewManager.Instance:CloseAll()
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:Open(GuideModuleName.WorldsNO1SceneView)
    MainuiWGCtrl.Instance:SetOtherContents(true)
    MainuiWGCtrl.Instance:SetTaskContents(false)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)	

	MainuiWGCtrl.Instance:SetFbIconEndCountDown(WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp())

	if WorldsNO1WGData.Instance:IsObservationStatus() then
		GuajiWGCtrl.Instance:ClearAllOperate()
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:StopGuaji()
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))

	self.main_role_be_hit_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_BE_HIT, BindTool.Bind1(self.MainRoleBeHit, self))

	-- 刷新观战模式下的摄像机跟随目标
	WorldsNO1WGCtrl.Instance:FlushObservationCamera()

	-- 观战模式下隐藏主角
	WorldsNO1WGCtrl.Instance:FlushObservationMainRoleHide()

	-- 注册屏蔽规则，观战者不要屏蔽其他玩家的技能
	self.role_effect_shield_rule = WorldsNO1ObservationRule.New(ShieldObjType.RoleSkillEffect, ShieldRuleWeight.Max, nil)
	self.role_effect_shield_rule:Register()

	-- 注册屏蔽规则，观战者不要屏蔽其他玩家
	self.role_shield_rule = WorldsNO1ObservationRule.New(ShieldObjType.Role, ShieldRuleWeight.Max, nil)
	self.role_shield_rule:Register()

	-- 摄像机切成自由旋转
	if CAMERA_TYPE ~= CameraType.Free then
        MainuiWGCtrl.Instance:ChangeCameraMode()
    end

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
    
    GuajiWGCtrl.Instance:SetGuajiRange(1000000)
end


function WorldsNO1SceneLogic:Out(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Out(self)
	ViewManager.Instance:Close(GuideModuleName.WorldsNO1SceneView)
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	ViewManager.Instance:Close(GuideModuleName.WorldsNO1EndCountdownView)
	ViewManager.Instance:Close(GuideModuleName.WorldsNO1ObservationView)
	ViewManager.Instance:Close(GuideModuleName.WorldsNO1FuhuoView)
	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end	

	if self.main_role_be_hit_event then
		GlobalEventSystem:UnBind(self.main_role_be_hit_event)
		self.main_role_be_hit_event = nil
	end
	
	WorldsNO1WGCtrl.Instance:FlushObservationCamera()
	WorldsNO1WGCtrl.Instance:FlushObservationMainRoleHide()

	if self.role_effect_shield_rule then
		self.role_effect_shield_rule:UnRegister()
		self.role_effect_shield_rule:DeleteMe()
		self.role_effect_shield_rule = nil
	end

	if self.role_shield_rule then
		self.role_shield_rule:UnRegister()
		self.role_shield_rule:DeleteMe()
		self.role_shield_rule = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
    GuajiWGCtrl.Instance:SetGuajiRange(nil)
end

local last_stop_time = 0
function WorldsNO1SceneLogic:Update()
	local jiesuan_time = WorldsNO1WGData.Instance:GetJiesuanTimestamp()
	local end_seconds = jiesuan_time - TimeWGCtrl.Instance:GetServerTime()
	if end_seconds <= 10 and end_seconds > 5 then
		if not ViewManager.Instance:IsOpen(GuideModuleName.WorldsNO1EndCountdownView) then
			ViewManager.Instance:Open(GuideModuleName.WorldsNO1EndCountdownView)
		end
	end
	if WorldsNO1WGData.Instance:IsObservationStatus() and os.time() > last_stop_time + 1 then
		last_stop_time = os.time()
		GuajiWGCtrl.Instance:StopGuaji()
	end
end

-- 角色是否是敌人
function WorldsNO1SceneLogic:IsRoleEnemy(target_obj, main_role)
	return true
end

-- 容错，避免选中天神召唤物
function WorldsNO1SceneLogic:IsAttackMonster(monster_id, objvo)
	return false
end

function WorldsNO1SceneLogic:SpecialSelectEnemy()
	return false
end

function WorldsNO1SceneLogic:CanGetMoveObj()
	return true
end

function WorldsNO1SceneLogic:GetGuajiSelectObjDistance()
	return 2000
end

function WorldsNO1SceneLogic:GetSceneLogicMoveState()
	return not (
				WorldsNO1WGData.Instance:IsObservationStatus() 
				or WorldsNO1WGData.Instance:GetPKBeginCountdownTimestamp() - TimeWGCtrl.Instance:GetServerTime() > 0
				or (TimeWGCtrl.Instance:GetServerTime() - self.enter_time < 3)					-- 容错（避免不同服务器之间时间不一样）
			)
end 

-- buff掉落物
function WorldsNO1SceneLogic:GetFallItemItemId(vo)
	if vo.item_id == 0 and vo.is_buff_falling then
		local buff_cfg = WorldsNO1WGData.Instance:GetBuffCfg(vo.buff_appearance)
		if buff_cfg then
			return buff_cfg.item_id
		end
	end
	return nil
end

function WorldsNO1SceneLogic:TryChangeCameraAngle(pos_index)	
	if pos_index == nil then
		return 
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and not WorldsNO1WGData.Instance:IsObservationStatus() then
		local relive_pos_cfg = WorldsNO1WGData.Instance:GetReliveCfgByIndex(pos_index)
		if relive_pos_cfg then
			local angle_x = relive_pos_cfg.camera_x
			local angle_y = relive_pos_cfg.camera_y
			if angle_x ~= nil and angle_y ~= nil then
				local scene_id = Scene.Instance:GetSceneId()
				local param_t = {scene_id = scene_id}
				Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, angle_x, angle_y, 17, param_t)
			end
		end
	end
end

function WorldsNO1SceneLogic:OnRideDown()
	-- 播放出生特效
	if Scene.Instance:GetMainRole() then
	    local pos = Scene.Instance:GetMainRole().draw_obj:GetRoot().transform.position
	    local bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_levelup1")
	    EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.x, pos.y, pos.z))
	end
end

function WorldsNO1SceneLogic:GetGuajiSelectObjDistance()
	return 1000
end

function WorldsNO1SceneLogic:OnRoleEnter(obj_id)
	if not WorldsNO1WGData.Instance:IsObservationStatus() then
		return
	end
	local role = Scene.Instance:GetObj(obj_id)
	if role and role:IsRole() and role:GetVo() then
		if role:GetUUIDStr() == WorldsNO1WGData.Instance:GetObservationUUIDStr() then
			WorldsNO1WGCtrl.Instance:FlushObservationCamera()
		end
	end
end

-- 主角被打
function WorldsNO1SceneLogic:MainRoleBeHit(role)
	-- -- 如果不在自动战斗则不要反击
	-- if GuajiType.Auto ~= GuajiCache.guaji_type then
	-- 	return 
	-- end

	-- -- 如果当前攻击目标距离很近则不要反击
	-- local main_role = Scene.Instance:GetMainRole()
	-- local cur_obj = GuajiCache.target_obj
	-- if cur_obj and main_role then
	-- 	local distance = cur_obj:GetLogicDistance(main_role:GetLogicPosTab())
	-- 	if distance < 10 then
	-- 		return
	-- 	end
	-- end

	-- if role and role.vo and role.vo.role_id and role.vo.role_id > 0 then
	-- 	self:ToAtkRole(role)
	-- end
end

-- 前往攻击玩家
function WorldsNO1SceneLogic:ToAtkRole(obj)
	local cur_obj = GuajiCache.target_obj
	if obj ~= cur_obj then
		GuajiWGCtrl.Instance:ClearTemporary()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		GuajiWGCtrl.Instance:DoAttackTarget(obj)
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
	end
end

function WorldsNO1SceneLogic:RefreshEffectRule()
	if self.role_effect_shield_rule then
		self.role_effect_shield_rule:RefreshRule()
	end
	if self.role_shield_rule then
		self.role_shield_rule:RefreshRule()
	end
end

function WorldsNO1SceneLogic:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.WORLDS_NO1 and status == ACTIVITY_STATUS.OPEN then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp())
	end
end

function WorldsNO1SceneLogic:GetGuajiSelectObjDistance()
	return 1000
end