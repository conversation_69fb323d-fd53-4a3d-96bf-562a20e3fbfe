OperationTaskChainFuHuoView = OperationTaskChainFuHuoView or BaseClass(SafeBaseView)

function OperationTaskChainFuHuoView:__init()
	self:SetMaskBg(false, false, BindTool.Bind(self.OnClickMask))
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_fuhuo")

	self.close_time = nil
end

function OperationTaskChainFuHuoView:ReleaseCallBack()
	self:ClearTimer()
	self.close_time = nil
end

function OperationTaskChainFuHuoView:LoadCallBack()
end

function OperationTaskChainFuHuoView:OpenCallBack()
	self.close_time = TimeWGCtrl.Instance:GetServerTime()

	local time = 5
	local cur_fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	local free_fuhuo_time = cur_fb_cfg and cur_fb_cfg.free_fuhuo_time
	if free_fuhuo_time and free_fuhuo_time ~= "" and free_fuhuo_time > 0 then
		time = free_fuhuo_time
	end

	self.close_time = self.close_time + time
end

function OperationTaskChainFuHuoView:CloseCallBack()
	self:ClearTimer()

	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
end

function OperationTaskChainFuHuoView:ShowIndexCallBack(index)
end

function OperationTaskChainFuHuoView:ClearTimer()
	if CountDown.Instance:HasCountDown("task_chain_fuhuo_timer") then
		CountDown.Instance:RemoveCountDown("task_chain_fuhuo_timer")
	end
end

function OperationTaskChainFuHuoView:OnFlush(param_t, index)
	self:ClearTimer()
	
	local time = 5
	local cur_fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	local free_fuhuo_time = cur_fb_cfg and cur_fb_cfg.free_fuhuo_time
	if free_fuhuo_time and free_fuhuo_time ~= "" and free_fuhuo_time > 0 then
		time = free_fuhuo_time
	end

	self.node_list.str_tip.text.text = time
	CountDownManager.Instance:AddCountDown("task_chain_fuhuo_timer", BindTool.Bind(self.FlushTimer, self), BindTool.Bind(self.CompleteTimer, self), nil, time, 1)
end

function OperationTaskChainFuHuoView:FlushTimer(elapse_time, total_time)
	if self.node_list.str_tip ~= nil then
		self.node_list.str_tip.text.text = math.floor(total_time - elapse_time)
	end
end

function OperationTaskChainFuHuoView:CompleteTimer()
	self:Close()
end

-- 防止关不掉
function OperationTaskChainFuHuoView:OnClickMask()
	if self.close_time ~= nil then
		local cur_time = TimeWGCtrl.Instance:GetServerTime()
		if cur_time >= self.close_time then
			self:Close()
		end
	end
end