ComposeStuffListView = ComposeStuffListView or BaseClass(SafeBaseView)

function ComposeStuffListView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_tips_bg_panel1", {vector2 = Vector2(0, 14), sizeDelta = Vector2(348,344)})
	self:AddViewResource(0, "uis/view/compose_ui_prefab", "layout_hecheng_baglist")
end

function ComposeStuffListView:__delete()
end

function ComposeStuffListView:CloseCallBack()
    self.stuff_list = nil
	self.click_call_back = nil
    self.select_grid_item = nil
end

function ComposeStuffListView:SetDataAndOpen(stuff_list, grid_item, call_back)
    self.stuff_list = stuff_list
    self.select_grid_item = grid_item
	self.click_call_back = call_back

    self:Open()
end

function ComposeStuffListView:ReleaseCallBack()
	if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
	end
end

function ComposeStuffListView:LoadCallBack()
	-- local third_root = self.node_list["layout_commmon_third_root"].rect
	-- third_root.anchoredPosition = Vector2(-12, 20)
	-- third_root.sizeDelta = Vector2(300, 404)
	self.node_list.title_view_name.text.text = Language.Compose.SelectEquipTitle
	self.item_list_view = AsyncListView.New(ComposeStuffListRender, self.node_list["ph_hecheng_baglist"])
	self.item_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectCellItemCallBack, self))
end

function ComposeStuffListView:ShowIndexCallBack(index)
end

function ComposeStuffListView:OnFlush()
    if IsEmptyTable(self.stuff_list) then
        self:Close()
    	return
    end

    self.item_list_view:SetDataList(self.stuff_list)
end

function ComposeStuffListView:SelectCellItemCallBack(item, cell_index, is_default, is_click)
	if item == nil then
		return
	end

	local data = item:GetData()
	if data == nil or not is_click then
		return
	end

	if self.click_call_back then
		self.click_call_back(data, self.select_grid_item)
	end

	self:Close()
end

----------------------------------------------------
-- ComposeStuffListRender
----------------------------------------------------
ComposeStuffListRender = ComposeStuffListRender or BaseClass(BaseRender)
function ComposeStuffListRender:__init()
end

function ComposeStuffListRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ComposeStuffListRender:LoadCallBack()
	if self.item_cell == nil then
        self.item_cell = ItemCell.New(self.node_list["ph_hechengbag_item_pos"])
    	self.item_cell:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
    	self.item_cell:SetIsShowTips(false)
	end
end

function ComposeStuffListRender:OnFlush()
	local data = self:GetData()
	if nil == data then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg then
		self.item_cell:SetData(data)
		self.node_list["lbl_hecheng_item_name"].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	end
end
