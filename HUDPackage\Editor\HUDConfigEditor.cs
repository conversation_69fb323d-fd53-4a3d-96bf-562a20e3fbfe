﻿using System.Text;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using HUDProgramme;
using System.IO;

public class HUDConfigEditor : EditorWindow
{
    [MenuItem("自定义工具/HUD飘字编辑器/配置编辑器")]
    private static void HUDShowWindow()
    {
        if (EditorSceneManager.GetActiveScene().name != "HUDEditor")
        {
            EditorUtility.DisplayDialog("警告", "请在HUDEditor场景下打开飘字配置编辑器", "确定");
            return;
        }

        EditorWindow hud_config_window = (HUDConfigEditor)EditorWindow.GetWindow(typeof(HUDConfigEditor), false, "配置编辑器");//创建窗口
        hud_config_window.Show();//展示
    }
    // 指定要检查的文件夹
    private string[] checkDirs = { "Assets/Game/UIs/HUDProgramme/Prefabs/AnimPrefab" };
    private string hud_setting = "Assets/Game/UIs/HUDProgramme/Prefabs/OtherPrefab/hud_setting.prefab";


    //对象列表
    string[] hud_number_list = null;
    GameObject[] hud_number_obj_list = null;

    //对象
    int curr_index;
    GameObject curr_hud_obj = null;
    HUDAnim curr_anim;

    float camera_far = 700f;
    float camera_near = 1f;
    float dur_time = 2f;
    float number_min = 0.05f;
    float number_max = 2f;

    Player target;

    //预览数值
    string bShowHead;
    string bShowHead2;
    bool bShowAdd;
    bool bShowSub;
    bool bHaveNumber = true;

    private void OnEnable()
    {
        InitNumberList();
    }

    private void InitNumberList()
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        hud_number_list = new string[guids.Length];
        hud_number_obj_list = new GameObject[guids.Length];
        int count = 0;

        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            hud_number_list[count] = gameobj.name;
            hud_number_obj_list[count] = gameobj;
            count++;
        }
    }

    ///定义类型
    private void OnGUI()
    {
        EditorGUILayout.LabelField("HUD相关可视化配置工具，请以1280 * 720分辨率配置！！！！！！！！");
        EditorGUILayout.Space(20f);
        EditorGUILayout.LabelField("飘字展示相关配置");

        GUILayout.BeginHorizontal();
        camera_far = EditorGUILayout.FloatField("摄象机渲染远距", camera_far);
        camera_near = EditorGUILayout.FloatField("摄象机渲染近距", camera_near);
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        dur_time = EditorGUILayout.FloatField("动画持续时间", dur_time);
        number_min = EditorGUILayout.FloatField("飘字最小值", number_min);
        number_max = EditorGUILayout.FloatField("飘字最大值", number_max);
        GUILayout.EndHorizontal();

        EditorGUILayout.Space(20f);
        EditorGUILayout.LabelField("飘字类型");


        curr_index = EditorGUIKit.Popup(curr_index, hud_number_list);
        DrawHUDConfig();

        //EditorGUILayout.Space(20f);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("保存配置"))
        {
            this.SaveConfig();
        }

        if (GUILayout.Button("加载配置"))
        {
            this.LoadConfig();
        }
        GUILayout.EndHorizontal();

        //EditorGUILayout.Space(40f);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("生成对象"))
        {
            this.BuildTarget();
        }
        if (GUILayout.Button("删除对象"))
        {
            this.DestroyTarget();
        }
        if (GUILayout.Button("飘字预览"))
        {
            this.Preview();
        }
        GUILayout.EndHorizontal();

        //EditorGUILayout.Space(40f);
        EditorGUILayout.LabelField("飘字属性");
        GUILayout.BeginHorizontal();
        bShowHead = EditorGUILayout.TextField("是否有前缀", bShowHead);
        bShowHead2 = EditorGUILayout.TextField("是否有前缀2", bShowHead2);
        bShowAdd = EditorGUILayout.Toggle("是否是加", bShowAdd);
        bShowSub = EditorGUILayout.Toggle("是否是减", bShowSub);
        bHaveNumber = EditorGUILayout.Toggle("是否展示数字", bHaveNumber);
        GUILayout.EndHorizontal();
        EditorGUILayout.Space(40f);

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("一键提交配置信息"))
        {
            this.SubmitConfigPrefab();
        }
        GUILayout.EndHorizontal();

    }

    private void DrawHUDConfig()
    {
        GameObject temp_hud_obj = hud_number_obj_list[curr_index];
        curr_hud_obj = temp_hud_obj;
        curr_anim = curr_hud_obj.GetComponent<HUDAnim>();

        if (curr_anim)
        {
            GUILayout.BeginHorizontal();
            curr_anim.HeadName = EditorGUILayout.TextField("飘字前缀", curr_anim.HeadName);
            curr_anim.NumberString = EditorGUILayout.TextField("飘字字体", curr_anim.NumberString);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            curr_anim.OffsetX = EditorGUILayout.FloatField("X轴偏移", curr_anim.OffsetX);
            curr_anim.OffsetY = EditorGUILayout.FloatField("Y轴偏移", curr_anim.OffsetY);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            curr_anim.RandomOffsetX = EditorGUILayout.FloatField("X轴偏移随机", curr_anim.RandomOffsetX);
            curr_anim.RandomOffsetY = EditorGUILayout.FloatField("Y轴偏移随机", curr_anim.RandomOffsetY);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            curr_anim.AlphaCurve = EditorGUILayout.CurveField("透明度动画", curr_anim.AlphaCurve);
            curr_anim.ScaleCurve = EditorGUILayout.CurveField("大小动画", curr_anim.ScaleCurve);
            curr_anim.MoveCurve = EditorGUILayout.CurveField("Y轴偏移动画", curr_anim.MoveCurve);
            curr_anim.MoveXCurve = EditorGUILayout.CurveField("X轴偏移动画", curr_anim.MoveXCurve);
            GUILayout.EndHorizontal();


            curr_anim.AlignType = (HUDAlignType)EditorGUILayout.EnumPopup(curr_anim.AlignType);
            curr_anim.ScreenAlign = EditorGUILayout.Toggle("是否是场景对齐", curr_anim.ScreenAlign);
            if (curr_anim.ScreenAlign)
            {
                curr_anim.ScreenAlignType = (HUDAlignType)EditorGUILayout.EnumPopup(curr_anim.ScreenAlignType);
            }
        }
    }

    void SaveConfig()
    {
        GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(hud_setting);
        HudAniSetting hudSetting = gameobj.GetComponent<HudAniSetting>();
        if (hudSetting != null)
        {
            hudSetting.m_fDurationTime = dur_time;
            hudSetting.CameraFarDist = camera_far;
            hudSetting.CameraNearDist = camera_near;
            hudSetting.m_fNumberScaleMin = number_min;
            hudSetting.m_fNumberScaleMax = number_max;

            EditorUtility.SetDirty(gameobj);
        }

        if (curr_hud_obj != null)
        {
            EditorUtility.SetDirty(curr_hud_obj);
        }

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    void LoadConfig()
    {
        if (!Application.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "未运行状态不用加载", "确定");
            return;
        }

        HudAniSetting hudSetting = HUDAssetsManager.Instance.GetPrefabs<HudAniSetting>("HUDSetting");
        if (hudSetting != null)
        {
            HudSetting.Instance.InitSetting(hudSetting);
            HUDNumberRender.Instance.InitHUD();
        }

        EditorUtility.DisplayDialog("警告", "重新加载完成！", "确定");
    }



    void BuildTarget()
    {
        if (!Application.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "请先运行", "确定");
            return;
        }

        if (target != null)
        {
            EditorUtility.DisplayDialog("警告", "对象已存在", "确定");
            return;
        }

        target = PlayerMng.Instance.AddPlayer();
    }

    void DestroyTarget()
    {
        if (!Application.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "请先运行", "确定");
            return;
        }

        if (target != null)
        {
            GameObject.Destroy(target.gameObject);
            target = null;
        }
    }

    void Preview()
    {
        if (!Application.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "请先运行", "确定");
            return;
        }

        if (target == null)
        {
            EditorUtility.DisplayDialog("警告", "请先生成对象", "确定");
            return;
        }

        int nExp = 123456789;//Random.Range(100, 50000);
        HUDNumberRender.Instance.AddHudNumber(target.transform, curr_hud_obj.name, nExp, bShowHead, bShowHead2, bShowAdd, bShowSub, bHaveNumber, curr_anim);
    }

    void SubmitConfigPrefab()
    {
        string applicationPath =  Path.Combine(Application.dataPath, "Game/UIs/HUDProgramme/Prefabs");
        List<string> pathList = new List<string>();
        pathList.Add(applicationPath);

        string commitPath = string.Join("*", pathList.ToArray());
        SVNTool.ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
    }
}
