FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

-- 多人装备本(遗迹之地)
function FuBenPanelView:InitTeamEquip()
	if self.equip_fb_list ~= nil then return end
	self.select_index = 1
	local ph = self.node_list["ph_equip_list"]
	self.equip_fb_list = AsyncListView.New(TeamEquipFbitemRender, ph)
	self.equip_fb_list:SetSelectCallBack(BindTool.Bind1(self.OnTeamEquipFbCallBack, self))

	self.award_list = {}
	for i = 0, 3 do
		self.award_list[i] = ItemCell.New(self.node_list["ph_award" .. i])
	end

	self.node_list["btn_matching_team"].button:AddClickListener(BindTool.Bind(self.OnClickMatchEquipFb, self))
end

function FuBenPanelView:OnClickEnterFb()
	local fb_info = TeamEquipFbWGData.Instance:GetTeamEquipRoleInfo()
	local yet_count = fb_info.day_enterfb_times or 0
	local fb_cfg = TeamEquipFbWGData.Instance:GetTeamEquipFbCfg().other[1]

	if self.equip_fb_list:GetSelectItem():GetData().need_role_level > RoleWGData.Instance.role_vo.level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.CaveBossTips)
		return 
	end

	if 0 == SocietyWGData.Instance:GetIsInTeam() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NoInTeamTip)
		NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.TeamEquipFb, fb_mode = self.select_index - 1, is_match = false})
	else
		if 1 == #SocietyWGData.Instance:GetTeamMemberList() then
			self.alert_window:SetLableString(Language.NewTeam.EnterTip)
			self.alert_window:SetShowCheckBox(false)
			self.alert_window:Open()
		elseif yet_count >= fb_cfg.everyday_times then
			self.alert_window:SetOkFunc(BindTool.Bind1(self.OnClickOkCallBack, self))
			self.alert_window:SetShowCheckBox(true)
			self.alert_window:Open()
		else
			self:OnClickOkCallBack()
		end
	end
end

function FuBenPanelView:OnClickMatchEquipFb()
	NewTeamWGCtrl.Instance:Open({team_type = TeamDataConst.GoalType.TeamEquipFb, fb_mode = self.select_index - 1, is_match = true})
end

function FuBenPanelView:OnClickOkCallBack(item, index)
	FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.TEAM_EQUIP, 1, self.select_index - 1)  -- 副本类型, 是否组队, 层数
end

function FuBenPanelView:SelectFbIndex()
	local fb_cfg = TeamEquipFbWGData.Instance:GetTeamEquipFbCfg()
	local role_level = RoleWGData.Instance.role_vo.level
	local index = 1
	for i = 1, #fb_cfg.layer do
		if fb_cfg.layer[i].need_role_level <= role_level and fb_cfg.layer[i + 1] and fb_cfg.layer[i + 1].need_role_level > role_level then
			index = i
		elseif fb_cfg.layer[i].need_role_level <= role_level and (not fb_cfg.layer[i + 1]) then
			index = i
		end
	end

	if self.equip_fb_list then
		self.equip_fb_list:SelectIndex(index)
		if index > 4 then			
			
		end
	end
end

function FuBenPanelView:OnTeamEquipFbCallBack(item, index)
	-- self.equip_fb_data = item.data
	self.select_index = index
	self:OnFlushFbInfo()
end

function FuBenPanelView:OnFlushFbInfo()
	local reward_list = TeamEquipFbWGData.Instance:GetTeamEquipReward(self.select_index)
	if nil == reward_list then return end
	for k,v in pairs(reward_list) do
		self.award_list[k]:SetData({item_id = v.item_id, num = v.num, is_bind = v.is_bind})
	end
end

function FuBenPanelView:OnFlushTeamEquipView()
	local fb_cfg = TeamEquipFbWGData.Instance:GetTeamEquipFbCfg()
	if nil == fb_cfg then return end
	self.equip_fb_list:SetDataList(fb_cfg.layer, 0)
	self.equip_fb_list:SelectIndex(1)

	local fb_info = TeamEquipFbWGData.Instance:GetTeamEquipRoleInfo()
	if nil == fb_info then 
		return
	end
	
	local yet_count = fb_info.day_enterfb_times or 0
	yet_count = yet_count > fb_cfg.other[1].everyday_times and fb_cfg.other[1].everyday_times or yet_count
	yet_count = fb_cfg.other[1].everyday_times - yet_count
	local content = string.format(Language.FuBen.TeamEquipFbEnterCount, yet_count, fb_cfg.other[1].everyday_times)
	self.node_list["rich_fb_enter_count"].text.text = content
	local str = Language.FuBen.TeamEnter
	if yet_count <= 0 then
		str = Language.FuBen.HelpEnter
	end

	self.node_list["img_equip_remind"]:SetActive(self:CheckTeamEquipCount())
	self.node_list["img_equip_stamp"]:SetActive(FuBenPanelWGData.Instance:GetIsDoubleDrop(DOUBLE_DROP_FB_TYPE.EQUIP_FB))
end

function FuBenPanelView:DeleteTeamEquipView()
	if self.award_list then
		for k,v in pairs(self.award_list) do
			v:DeleteMe()
		end
		self.award_list = nil
	end

	if self.equip_fb_list then
		self.equip_fb_list:DeleteMe()
		self.equip_fb_list = nil
	end
end

function FuBenPanelView:CheckTeamEquipCount()
	local fb_cfg = TeamEquipFbWGData.Instance:GetTeamEquipFbCfg()
	local fb_info = TeamEquipFbWGData.Instance:GetTeamEquipRoleInfo()
	local yet_count = fb_info.day_enterfb_times or 0
	yet_count = yet_count > fb_cfg.other[1].everyday_times and fb_cfg.other[1].everyday_times or yet_count
	yet_count = fb_cfg.other[1].everyday_times - yet_count
	return yet_count > 0
end

------TeamEquipFbitemRender
TeamEquipFbitemRender = TeamEquipFbitemRender or BaseClass(BaseRender)
function TeamEquipFbitemRender:__init()

end

function TeamEquipFbitemRender:__delete()

end

function TeamEquipFbitemRender:CreateChild()
	BaseRender.CreateChild(self)

end

function TeamEquipFbitemRender:OnFlush()
	if not self.data then return end

	self.node_list["lbl_fb_name"].text.text = self.data.name
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.need_role_level)
	if is_vis then
		self.node_list["lbl_level"].text.text = "LV:" .. Language.Common.FeiXian .. role_level
	else
		self.node_list["lbl_level"].text.text = "LV:" .. self.data.need_role_level
	end
	self.node_list["lbl_fb_name"].text.text = self.data.name


	self.node_list["img_team_equip_fb_bg"].image:LoadSprite(ResPath.GetFuBenPanel("team_equip_fb_bg_" .. self.index))
	self.node_list["img_team_equip_fb_head"].image:LoadSprite(ResPath.GetFuBenPanel("team_equip_fb_head_" .. self.index))


	self.node_list["lbl_hint"]:SetActive(RoleWGData.Instance.role_vo.level < self.data.need_role_level)
	XUI.SetGraphicGrey(self.node_list["img_team_equip_fb_bg"], self.data.need_role_level > RoleWGData.Instance.role_vo.level)
	XUI.SetGraphicGrey(self.node_list["img_team_equip_fb_head"], self.data.need_role_level > RoleWGData.Instance.role_vo.level)
end

function TeamEquipFbitemRender:OnSelectChange(is_select)
	
end

function TeamEquipFbitemRender:CreateSelectEffect()
end
