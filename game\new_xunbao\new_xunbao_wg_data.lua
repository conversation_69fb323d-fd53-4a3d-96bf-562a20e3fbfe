NewXunbaoWGData = NewXunbaoWGData or BaseClass()
NewXunbaoWGData.LongRecordLen = 50

function NewXunbaoWGData:__init()
	if NewXunbaoWGData.Instance ~= nil then
		error("[NewXunbaoWGData] attempt to create singleton twice!")
		return
	end
	NewXunbaoWGData.Instance = self

	self.taobao_draw_layer = {}
	self.taobao_server_record = {}
	self.layer_open_list = {}
	self.wengua_pool_id = 0
	self.buy_open_pool_id = 3

	self.wengua_cfg = ConfigManager.Instance:GetAutoConfig("wengua_auto")

	RemindManager.Instance:Register(RemindName.XunBao_FuWen, BindTool.Bind(self.IsShowFuWenRedPoint, self))		--
	RemindManager.Instance:Register(RemindName.XunBao_XunBao, BindTool.Bind(self.IsShowXunBaoLongRedPoint, self))			--
	RemindManager.Instance:Register(RemindName.XunBao_Xuan<PERSON>hi, BindTool.Bind(self.IsShowXunBaoShiRedPoint, self))			--
end

function NewXunbaoWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.XunBao_FuWen)
	RemindManager.Instance:UnRegister(RemindName.XunBao_XunBao)
	RemindManager.Instance:UnRegister(RemindName.XunBao_XuanShi)

	NewXunbaoWGData.Instance = nil
end

function NewXunbaoWGData:IsShowFuWenRedPoint()
	if self:IsShowGuaBtnRed() then
		return 1
	end

	return 0
end

function NewXunbaoWGData:IsShowGuaBtnRed()
	local other_cfg = self:GetWenGuaOther()
    local num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.draw_consume_item_id)
	return num >= other_cfg.draw_consume_item_num
end

--寻宝仓库数量>0 都要显示红点
function NewXunbaoWGData:IsShowXunBaoLongRedPoint()
	--[[屏蔽

	if not self:GetXunBaoBagEmpty() then
		return 1
	end
	for i = 0, 1 do
		if self:CheckLongLayerRemind(i) then
			return 1
		end
	end
	]]
	return 0
end

--寻宝仓库数量>0 都要显示红点
function NewXunbaoWGData:IsShowXunBaoShiRedPoint()
	if not self:GetXunBaoBagEmpty() then
		return 1
	end
	for i = 2, 3 do
		if self:CheckLongLayerRemind(i) then
			return 1
		end
	end
	return 0
end

function NewXunbaoWGData:CheckLongLayerRemind(layer)
	if layer == nil then
		return false
	end

	if self:GetTBOpenFlagByLayer(layer) == 0 then
		return true
	end

	if self:GetisDrawOutByLayer(layer) then
		return true
	end

	if self:CheckTbLongCanDrawNextByLayer(layer) then
		return true
	end

	return false
end

function NewXunbaoWGData:GetXunBaoBagEmpty()
	local list = self:GetTBLongBagList()
	if list == nil then
		return
	end
	return IsEmptyTable(list)
end

---------------------------------------------------------------------------------------
function NewXunbaoWGData:GetDefaultLongCellListByLayer(layer_index)
	if not self["long_layer" .. layer_index] then
		local index = 0
		local cfg = ConfigManager.Instance:GetAutoConfig("taobao_auto")
		local list = {}
		for i, v in pairs(cfg.reward) do
			if v.layer == layer_index then
				local t = {}
				t.reward_item = v.reward_item
				index = index + 1
				t.pos = v.pos or index
                t.layer = v.layer
				t.reward_id = v.reward_id
				t.is_shake = v.is_shake
				t.big_reward = v.big_reward
				t.effect_level = v.effect_level
				table.insert(list, t)
			end
		end
		table.sort(list, SortTools.KeyLowerSorter("pos"))
		self["long_layer" .. layer_index] = list
	end
	return self["long_layer" .. layer_index]
end

function NewXunbaoWGData:SetTBDrawLayerInfo(protocol)
    if not self.taobao_draw_layer[protocol.layer] then
        self.taobao_draw_layer[protocol.layer] = {}
    end
    self.taobao_draw_layer[protocol.layer].layer = protocol.layer
    --local list = {0}
    --for i, v in ipairs(protocol.draw_id_list) do
    --    table.insert(list, v)
    --end
    self.taobao_draw_layer[protocol.layer].draw_id_list = protocol.draw_id_list
    self.taobao_draw_layer[protocol.layer].draw_id_record = protocol.draw_id_record

	self:CheckTBLayerOpenFlag(protocol.is_open, protocol.layer)
end

function NewXunbaoWGData:CheckTBLayerOpenFlag(is_open, layer)
	self.layer_open_list[layer] = is_open
end

function NewXunbaoWGData:GetTBOpenFlagByLayer(layer)
	return self.layer_open_list[layer]
end

function NewXunbaoWGData:GetTBDrawLayerInfoByLayer(layer)
    return self.taobao_draw_layer and self.taobao_draw_layer[layer] or {}
end

function NewXunbaoWGData:GetTBLongInfoById(id, layer)
    local cfg = ConfigManager.Instance:GetAutoConfig("taobao_auto").reward
    for i, v in pairs(cfg) do
        if id == v.reward_id and layer == v.layer then
            return v
        end
    end
end

function NewXunbaoWGData:GetTBLongPerformanceCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("taobao_auto").performance[1]
    return cfg
end

function NewXunbaoWGData:GetTBLongLayerCfgByLayer(layer)
    local cfg = ConfigManager.Instance:GetAutoConfig("taobao_auto").layer
    local list = {}
    for i, v in pairs(cfg) do
        if v.layer == layer then
            table.insert(list, v)
        end
    end
    return list
end

function NewXunbaoWGData:GetCurDrawInfo(layer)
    local draw_info_list = self:GetTBDrawLayerInfoByLayer(layer)
	draw_info_list = draw_info_list.draw_id_list or {}
    local draw_times = 1
    for i, v in pairs(draw_info_list) do
        if v > 0 then
            draw_times = draw_times + 1
        end
    end

    local layer_info_list = self:GetTBLongLayerCfgByLayer(layer)
    for i, v in pairs(layer_info_list) do
        local cfg_draw_times = Split(v.draw_times, ",")
        if draw_times >= tonumber(cfg_draw_times[1]) and draw_times <= tonumber(cfg_draw_times[2]) then
            return v
        end
    end
end

function NewXunbaoWGData:CheckEnoughDraw(layer)
    local cur_draw_info = self:GetCurDrawInfo(layer)
	if cur_draw_info == nil then return end
    local num = cur_draw_info.consume_item_num - ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.consume_item_id)
    return num, cur_draw_info.shop_seq
end

function NewXunbaoWGData:GetAnimDifValue(layer, pos)
    local cfg = self:GetTBLongPerformanceCfg()
    local num = self:GetLongCellLengthByLayer(layer)/2
    local dif_pixel = cfg.dif_pixel or 1
    return dif_pixel * (num - pos)
end

function NewXunbaoWGData:GetLongCellLengthByLayer(layer)
    if not self["tb_long_len_" .. layer] then
        local cell_list = self:GetDefaultLongCellListByLayer(layer)
        self["tb_long_len_" .. layer] = #cell_list
    end
    return self["tb_long_len_" .. layer]
end

function NewXunbaoWGData:SetDrawResult(protocol)
    self.draw_result = {}
    self.draw_result.draw_layer = protocol.draw_layer
    local draw_index_add = protocol.draw_index + 1
    self.draw_result.draw_index = draw_index_add
    self.draw_result.draw_id = protocol.reward_id

	if not self.taobao_draw_layer[protocol.draw_layer] then
		local list = {}
		list.draw_id_list = {}
		list.draw_id_list[draw_index_add] = protocol.draw_id
		self.taobao_draw_layer[protocol.draw_layer] = list
	else
		local t =  self.taobao_draw_layer[protocol.draw_layer].draw_id_list
		t[draw_index_add] = protocol.draw_id
	end
end

function NewXunbaoWGData:GetDrawResult()
    return self.draw_result
end

function NewXunbaoWGData:GetisDrawOutByLayer(layer)
    local len = self:GetLongCellLengthByLayer(layer)
    local reward_list = self:GetTBDrawLayerInfoByLayer(layer)
	reward_list = reward_list.draw_id_list or {}
    local num = 0
    for i, v in pairs(reward_list) do
        if v > 0 then
            num = num + 1
        end
    end
    return num == len and len ~= 0
end

function NewXunbaoWGData:GetLongBigRewardByLayer(layer)
	local cell_list = self:GetDefaultLongCellListByLayer(layer)
	local reward_list = self:GetTBDrawLayerInfoByLayer(layer)
	reward_list = reward_list.draw_id_list or {}
	local list = {}
	for i, v in pairs(cell_list) do
		local big_reward = v.big_reward or 1
		if big_reward > 0 then
			local t = {}
			t.has_get = false
			for _, reward_id in pairs(reward_list) do
				if reward_id == v.reward_id then
					t.has_get = true
					break
				end
			end
			t.reward_item = v.reward_item
			t.big_reward = big_reward
			table.insert(list, t)
		end
	end
	table.sort(list, SortTools.KeyUpperSorter("big_reward"))
	return list
end

function NewXunbaoWGData:SetLongBagInfo(protocol)
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local list = protocol.bag_list
    for i, v in pairs(list) do
		v.sort_num = self:GetItemSortNum(v.item_id, role_prof)
    end
	table.sort(list, SortTools.KeyUpperSorter("sort_num"))
	self.bag_list = list
end

--离线挂机面板也借用了一下你的规则，这个规则改动的时候 找一下上官哦
function NewXunbaoWGData:GetItemSortNum(item_id, role_prof)
	local sort_num = 0
	local cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
	if cfg == nil then
		print_error(cfg, item_id)
		return 0
	end
	if cfg.sort_special ~= nil and cfg.sort_special ~= 0 then
		sort_num = sort_num + cfg.sort_special + (1e+11)
	else
		if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			sort_num = sort_num + 4 * (1e+10)
			if cfg.limit_prof == role_prof then
				sort_num = sort_num + (1e+9)
			end
			sort_num = sort_num + cfg.color * (1e+8)
			sort_num = sort_num + (TAOBAO_BAG_SUBTYPE[cfg.sub_type] or 0) * (1e+6)
			sort_num = sort_num + item_id
		else
			if item_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
				sort_num = sort_num + 3 * (1e+10)
			elseif item_type == GameEnum.ITEM_BIGTYPE_GIF then
				sort_num = sort_num + 2 * (1e+10)
			elseif item_type == GameEnum.ITEM_BIGTYPE_OTHER then
				sort_num = sort_num + (1e+10)
			end
			sort_num = sort_num + cfg.color * (1e+8)
			sort_num = sort_num + (1e+6) - item_id
		end
	end
	return sort_num
end

function NewXunbaoWGData:SetBagChangeInfo(protocol)
	local data = protocol.change_info
	local role_prof = RoleWGData.Instance:GetRoleProf()
	data.sort_num = self:GetItemSortNum(data.item_id, role_prof)
	local pos
	for i, v in pairs(self.bag_list) do
		if v.index == data.index then
			pos = i
			break
		end
	end
	if pos then
		if data.item_id == 0 then
			table.remove(self.bag_list, pos)
		else
			self.bag_list[pos] = data
		end
	else
		self:InsertBagData(data)
	end
end

function NewXunbaoWGData:InsertBagData(data)
	local pos = 1
	for i, v in ipairs(self.bag_list) do
		if v.sort_num < data.sort_num then
			pos = i
			break
		end
	end
	table.insert(self.bag_list, pos, data)
end

function NewXunbaoWGData:GetTBLongBagList()
    return self.bag_list
end

-- 获取背包信息
function NewXunbaoWGData:GetBagInfo()
	return self.bag_list or {}
end

function NewXunbaoWGData:CheckHasRewardId(layer)
	local info = self:GetTBDrawLayerInfoByLayer(layer)
	local draw_id_list = info.draw_id_list or {}
	for i, v in pairs(draw_id_list) do
		if v > 0 then
			return true
		end
	end
end

function NewXunbaoWGData:GetFlushBigRewardTime()
    local show_cfg = self:GetTBLongPerformanceCfg()
    return show_cfg.scale_time*2 + show_cfg.card_rot_time + show_cfg.break_time + (show_cfg.delay_time or 0.5)*2
end

function NewXunbaoWGData:SetTBLongServerRecord(protocol)
	self.taobao_server_record[protocol.layer] = protocol.draw_record_list
end

function NewXunbaoWGData:GetTBLongServerRecordByLayer(layer)
	local list = self.taobao_server_record[layer] or {}
	--for i = NewXunbaoWGData.LongRecordLen, 1, -1 do
	--	if list[i] and list[i].draw_reward_id == 0 then
	--		table.remove(list,i)
	--	end
	--end
	--return list
	local result = {}
	for i = 1, NewXunbaoWGData.LongRecordLen do
		if list[i] and list[i].draw_reward_id ~= 0 then
			table.insert(result, list[i])
		end
	end
	return result
end

function NewXunbaoWGData:GetTbLongSelfRecordByLayer(layer)
    local info_list = self:GetTBDrawLayerInfoByLayer(layer)
	info_list = info_list.draw_id_record or {}
	local list = {}
	local index = 0
	for i, v in ipairs(info_list) do
		if v ~= 0 then
			local t = {}
			t.draw_reward_id = v
			t.layer = layer
			index = index + 1
			table.insert(list, t)
			if index == NewXunbaoWGData.LongRecordLen then
				break
			end
		end
	end
	return list
end

function NewXunbaoWGData:GetTBLongLayerImageByLayer(layer)
	local cfg = ConfigManager.Instance:GetAutoConfig("taobao_auto").layer
	for i, v in pairs(cfg) do
		if v.layer == layer then
			return v.raw_bg
		end
	end
end

function NewXunbaoWGData:CheckTbLongCanDrawNextByLayer(layer)
	local info = self:GetCurDrawInfo(layer)
	if not info then
		return
	end
	return ItemWGData.Instance:GetItemNumInBagById(info.consume_item_id) >= info.consume_item_num
end

function NewXunbaoWGData:GetShuffleLastRewardId(layer)
	local reward_list = self:GetTBDrawLayerInfoByLayer(layer)
	reward_list = reward_list.draw_id_list or {}

	local all_id_list = self:GetDefaultLongCellListByLayer(layer)
	local last_id_list = {}
	local max_reward = all_id_list[1]
	local key = 0
	local exist
	for i, v in pairs(all_id_list) do
		exist = false
		for m, n in pairs(reward_list) do
			if n == v.reward_id then
				exist = true
				break
			end
		end
		if not exist then
			table.insert(last_id_list, v)
			if max_reward.big_reward < v.big_reward then
				max_reward = v
				key = #last_id_list
			end
		end
	end

	table.remove(last_id_list, key)
	table.insert(last_id_list, 1, max_reward)

	return last_id_list
end

function NewXunbaoWGData:SetShuffleInfo(protocol)
    self.shuffle_cache = protocol
	self.last_draw_pos = protocol.last_draw_index or GameMath.Rand(1, #protocol.draw_id_list)
end

function NewXunbaoWGData:FlushShuffleCahce()
    if self.shuffle_cache then
        self:SetTBDrawLayerInfo(self.shuffle_cache)
    end
end

function NewXunbaoWGData:GetShffleLastPos()
	return self.last_draw_pos
end
---------------------------------------------------------------------------------------------------
function NewXunbaoWGData:SetWenGuaInfo(protocol)
	self.wengua_pool_id = protocol.current_pool_id
	self.buy_open_pool_id = protocol.buy_open_pool_id
	self.week_buy_open_pool_times = protocol.week_buy_open_pool_times
	self.wengua_draw_id_list = protocol.draw_id_list
end

function NewXunbaoWGData:SetWenGuaIdInfo(protocol, new_num)
    self.wengua_pool_id = protocol.current_pool_id
	self.buy_open_pool_id = protocol.buy_open_pool_id
	self.week_buy_open_pool_times = protocol.week_buy_open_pool_times
	self.cur_time_id_len = new_num
end

function NewXunbaoWGData:GetCurTimeIdLen()
	return self.cur_time_id_len
end

function NewXunbaoWGData:SetWenGuaDrawIdInfo(list)
    self.wengua_draw_id_list = list
end

function NewXunbaoWGData:SetWenGuaPersonRecord(protocol)
	self.wengua_per_record = protocol.record_list
end

function NewXunbaoWGData:GetWenGuaPersonRecord()
	return self.wengua_per_record or {}
end

function NewXunbaoWGData:SetWenGuaServerRecord(protocol)
	self.wengua_ser_record = protocol.record_list
end

function NewXunbaoWGData:GetWenGuaServerRecord()
	return self.wengua_ser_record or {}
end

function NewXunbaoWGData:GetWenGuaOther()
	return self.wengua_cfg.other[1]
end

function NewXunbaoWGData:CheckGuaColorNum()
	local list = self.wengua_draw_id_list or {}
	local color_purple = 0
	local color_orange = 0
	local color_red = 0
	for i, v in pairs(list) do
		if v > 0 then
			local item = self:GetGuaItemByRewardId(v)
			local cfg = ItemWGData.Instance:GetItemConfig(item.reward_item.item_id)
			if cfg.color >= GameEnum.ITEM_COLOR_PURPLE then
				if cfg.color == GameEnum.ITEM_COLOR_ORANGE then
					color_orange = color_orange + 1
				elseif cfg.color == GameEnum.ITEM_COLOR_RED then
					color_red = color_red + 1
				else
					color_purple = color_purple + 1
				end
			end
		end
	end
	return color_red, color_orange, color_purple
end

function NewXunbaoWGData:GetGuaCurPoolId()
	if not self.buy_open_pool_id then
		return
	end
	return self.buy_open_pool_id > self.wengua_pool_id and self.buy_open_pool_id or self.wengua_pool_id
end

function NewXunbaoWGData:GetGuaIdList()
	local list = self.wengua_draw_id_list or {}
	local result_list = {}
	for i=#list,1,-1 do
		if list[i] > 0 then
			table.insert(result_list, {id = list[i], index = i})
		end
	end
	return result_list
end

function NewXunbaoWGData:GetGuaItemByRewardId(id)
	for i, v in pairs(self.wengua_cfg.reward) do
		if v.reward_id == id then
			return v
		end
	end
end

function NewXunbaoWGData:GetGuaBagIsEmpty()
	local list = self.wengua_draw_id_list or {}
	for i, v in pairs(list) do
		if v > 0 then
			return false
		end
	end
	return true
end

function NewXunbaoWGData:GetGuaBagNum()
	local num = 0
	local list = self.wengua_draw_id_list or {}
	for i, v in pairs(list) do
		if v > 0 then
			num = num + 1
		end
	end
	return num
end

function NewXunbaoWGData:GetAllRedGuaList()
    local cfg = ConfigManager.Instance:GetAutoConfig("bagua_cfg_auto").gua_type
    local list = {}
    for i, v in ipairs(cfg) do
        if v.color == GameEnum.ITEM_COLOR_RED then
            local t = {}
            t.is_pre = true
            t.item_id = v.item_id
            t.name = v.name
            table.insert(list, t)
        end
    end
    return list
end

function NewXunbaoWGData:GetNewIdListNum(list)
    local num = 0
	for i, v in pairs(list) do
		if v > 0 then
			num = num + 1
		end
	end
	return num
end
