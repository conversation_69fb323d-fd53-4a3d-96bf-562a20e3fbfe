ControlBeastsBattleElementView = ControlBeastsBattleElementView or BaseClass(SafeBaseView)

local DESC_COUNT = 6
local GM_ATTR_RENDER_COUNT = 4
local ALL_TYPE_LIST = {1, 2, 3, 4, 5}
local GM_ATTR_COUNT = 8
local NOT_ACTIVE_COLOR = "#4B4B4B"
local BASE_ATTR_ADD_PER_ID = 100000
local NORMAL_SKILL_HURT_PER_ID = 200000

function ControlBeastsBattleElementView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_battle_element_view")
end

function ControlBeastsBattleElementView:LoadCallBack()
	if not self.desc_list then
		self.desc_list = {}

		for i = 1, DESC_COUNT do
			local desc_obj = self.node_list.desc_root:FindObj(string.format("desc_render_0%d", i))
			local cell = BeastsBattleElementDescRender.New(desc_obj)
			self.desc_list[i] = cell
		end
	end


	if not self.gm_attr_list then
		self.gm_attr_list = {}

		for i = 1, GM_ATTR_RENDER_COUNT do
			local desc_obj = self.node_list.gm_attr_root:FindObj(string.format("gm_attr_render_%d", i))
			local cell = BeastsBattleElementAttrRender.New(desc_obj)
			self.gm_attr_list[i] = cell
		end
	end

	if not self.left_type_grid then
		self.left_type_grid = AsyncListView.New(BeastsBattleElementTypeRender, self.node_list.left_type_grid)
		self.left_type_grid:SetSelectCallBack(BindTool.Bind(self.GMTypeRenderClick, self))
	end
end

function ControlBeastsBattleElementView:ReleaseCallBack()
	if self.desc_list and #self.desc_list > 0 then
		for _, render_cell in ipairs(self.desc_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.desc_list = nil
	end

	if self.gm_attr_list and #self.gm_attr_list > 0 then
		for _, render_cell in ipairs(self.gm_attr_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.gm_attr_list = nil
	end

	if self.left_type_grid then
		self.left_type_grid:DeleteMe()
		self.left_type_grid = nil
	end
end

function ControlBeastsBattleElementView:SetNowActiveGmType(now_active_gm_type)
	self.now_active_gm_type = now_active_gm_type
end

function ControlBeastsBattleElementView:GMTypeRenderClick(cell, cell_index, is_default, is_click)
	if cell == nil or cell.data == nil then
		return
	end

	if self.now_select_gm_index == cell_index then
		return
	end

	self.now_select_gm_index = cell_index
	self.now_select_gm_data = cell.data
	self:FlushNowTypeMessage()
end


function ControlBeastsBattleElementView:OnFlush()
	self.left_type_grid:SetDataList(ALL_TYPE_LIST)
	local now_select = 1

	if self.now_active_gm_type ~= nil and self.now_active_gm_type ~= 0 then
		now_select = self.now_active_gm_type
		self.now_select_gm_index = nil
	elseif self.now_select_gm_index ~= nil then
		now_select = self.now_select_gm_index
		self.now_select_gm_index = nil
	end

	self.left_type_grid:JumpToIndex(now_select, 5)

	local desc_list = Language.ContralBeasts.GMTypeDesc
	for i, desc_cell in ipairs(self.desc_list) do
		desc_cell:SetVisible(desc_list[i] ~= nil)

		if desc_list[i] ~= nil then
			desc_cell:SetData(desc_list[i])
		end
	end
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.desc_root.rect)
end

function ControlBeastsBattleElementView:FlushNowTypeMessage()
	if not self.now_select_gm_data then
		return
	end

	local type = self.now_select_gm_data
	local type_title_desc = string.format("%s%s", Language.ContralBeasts.GMTypeList[type], Language.Common.GongMing) 
	self.node_list.gm_type_title.text.text = type_title_desc
	local type_image_str = string.format("a3_hs_bq_%d", type)
	self.node_list.gm_type_icon.image:LoadSprite(ResPath.GetControlBeastsImg(type_image_str))
	-- 这里展示共鸣个数属性
	local main_list_data = ControlBeastsWGData.Instance:GetHoleMainData()
	local now_battle_list = {}
    -- 主战位
    for _, main_data in ipairs(main_list_data) do
        if main_data.red then
            is_main_battle_red = true
        end

		if main_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(main_data.beasts_bag_id)
            if beast_data then
                local server_data = beast_data.server_data
                local beast_id = server_data.beast_id
                local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
                table.insert(now_battle_list, beast_cfg.fetters_type)
            end
        end
    end

	local now_count = 0
	for i, fetters_type in ipairs(now_battle_list) do
        local type_value = fetters_type % 10                    -- 取余10表示当前的具体类型
        local type_extra_value = math.floor(fetters_type / 10)        -- 除以10表示当前特殊类型

		if type == type_value then
			now_count = now_count + 1
		end

		if type == type_extra_value then
			now_count = now_count + 1
		end
    end

	local attr_list = ControlBeastsWGData.Instance:GetFetterCfgByType(type)
	for i, render_cell in ipairs(self.gm_attr_list) do
		render_cell:SetVisible(attr_list[i] ~= nil)

		if attr_list[i] ~= nil then
			render_cell:AttrActiveNum(now_count)
			render_cell:SetData(attr_list[i])
		end
	end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.gm_attr_root.rect)
end

---------------------------------------------------------
-- 共鸣大类型
BeastsBattleElementTypeRender = BeastsBattleElementTypeRender or BaseClass(BaseRender)
function BeastsBattleElementTypeRender:OnFlush()
	if not self.data then return end

	local type_image_str = string.format("a3_hs_gm_type_big_%d", self.data)
	self.node_list.icon.image:LoadSprite(ResPath.GetControlBeastsImg(type_image_str))

	-- 这里展示共鸣个数属性
	local main_list_data = ControlBeastsWGData.Instance:GetHoleMainData()
	local now_battle_list = {}
	-- 主战位
	for _, main_data in ipairs(main_list_data) do
		if main_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(main_data.beasts_bag_id)
			if beast_data then
				local server_data = beast_data.server_data
				local beast_id = server_data.beast_id
				local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
				table.insert(now_battle_list, beast_cfg.fetters_type)
			end
		end
	end

	local now_count = 0
	for i, fetters_type in ipairs(now_battle_list) do
		local type_value = fetters_type % 10                    -- 取余10表示当前的具体类型
		local type_extra_value = math.floor(fetters_type / 10)        -- 除以10表示当前特殊类型

		if self.data == type_value then
			now_count = now_count + 1
		end

		if self.data == type_extra_value then
			now_count = now_count + 1
		end
	end
	
	local temp_fetter_data = ControlBeastsWGData.Instance:GetFetterCfgByTypeNumber(self.data, now_count)
	if temp_fetter_data ~= nil then
		self.node_list.type_effect:CustomSetActive(true)
		local bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_TYPE[self.data])
		self.node_list.type_effect:ChangeAsset(bundle, asset)
	else
		self.node_list.type_effect:CustomSetActive(false)
	end
end

function BeastsBattleElementTypeRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

-- 共鸣描述
BeastsBattleElementDescRender = BeastsBattleElementDescRender or BaseClass(BaseRender)
function BeastsBattleElementDescRender:OnFlush()
	if not self.data then return end

	self.node_list.desc.text.text = self.data
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

-- 共鸣属性信息
BeastsBattleElementAttrRender = BeastsBattleElementAttrRender or BaseClass(BaseRender)
function BeastsBattleElementAttrRender:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}

		for i = 1, GM_ATTR_COUNT do
			local desc_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
			local cell = CommonAttrRender.New(desc_obj)
			cell:SetAttrNameNeedSpace(true)
			self.attr_list[i] = cell
		end
	end
end

function BeastsBattleElementAttrRender:AttrActiveNum(now_count)
	self.now_count = now_count
end

function BeastsBattleElementAttrRender:__delete()
	self.now_count = nil

	if self.attr_list and #self.attr_list > 0 then
		for _, render_cell in ipairs(self.attr_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.attr_list = nil
	end
end

function BeastsBattleElementAttrRender:OnFlush()
	if not self.data then return end
	local color = self.data.type_num == self.now_count and COLOR3B.GREEN or COLOR3B.RED
	local title_str = string.format(Language.ContralBeasts.GMTypeAttrDesc, self.data.type_num, Language.ContralBeasts.GMTypeList[self.data.fetters_type])
	local list = self:AssembleSelfAttr()
	self.node_list.title_txt.text.text = title_str

	for i, render_cell in ipairs(self.attr_list) do
		render_cell:SetVisible(list[i] ~= nil)

		if list[i] ~= nil then
			render_cell:SetData(list[i])

			if list[i].attr_str == NORMAL_SKILL_HURT_PER_ID or list[i].attr_str == BASE_ATTR_ADD_PER_ID then
				local name_str = list[i].attr_str == NORMAL_SKILL_HURT_PER_ID and Language.ContralBeasts.ElementAttrType1 or Language.ContralBeasts.ElementAttrType2
				render_cell:ResetName(name_str)
				render_cell:ResetAttrVlaue(string.format("%s%%", list[i].attr_value))
			end

			if self.data.type_num ~= self.now_count then
				render_cell:ResetAttrColor(NOT_ACTIVE_COLOR)
			else
				render_cell:ResetAttrColor()
			end
		end
	end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.attr_list.rect)
end

function BeastsBattleElementAttrRender:AssembleSelfAttr()
	if not self.data then return {} end

	local attr_list = {}
	for i = 1, 6 do
		local key = self.data["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

			attr_list[key].attr_value = self.data["attr_value" .. i]
		end
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	-- 插入特殊普攻增伤
	local normal_skill_hurt_per = self.data.normal_skill_hurt_per or 0
	local normal_skill_hurt_per_value = math.floor(normal_skill_hurt_per / BEAST_DEFINE.BEASTS_BOUNS_PER2)
	if normal_skill_hurt_per_value > 0 then
		table.insert(return_table, 1, {attr_str = NORMAL_SKILL_HURT_PER_ID, attr_value = normal_skill_hurt_per_value})
	end

	-- 插入特殊基础加成
	local base_attr_add_per = self.data.base_attr_add_per or 0
	local base_attr_add_per_value = math.floor(base_attr_add_per / BEAST_DEFINE.BEASTS_BOUNS_PER2)
	if base_attr_add_per_value > 0 then
		table.insert(return_table, 1, {attr_str = BASE_ATTR_ADD_PER_ID, attr_value = base_attr_add_per_value})
	end

	return return_table
end