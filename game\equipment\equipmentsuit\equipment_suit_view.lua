EquipmentView = EquipmentView or BaseClass(SafeBaseView)

-------------------------------------------------DEF_FUNC_START------------------------------------------------
function EquipmentView:InitSutiView()
	-- 属性列表
    self.suit_attr_item_list = {}

	--装备
	if not self.suit_equip_list then
		self.suit_equip_list = {}
		for i = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
			self.suit_equip_list[i] = SuitShowEquipCell.New(self.node_list["s_equip_part"]:FindObj("part_".. i))
			self.suit_equip_list[i]:SetIndex(i)
			self.suit_equip_list[i]:SetEquipClickCallBack(BindTool.Bind(self.OnSelectTaozhuang<PERSON>tem<PERSON><PERSON><PERSON>, self))
		end
	end

	-- 装备肉身
	if not self.taozhuang_equip_body_list then
		self.taozhuang_equip_body_list = AsyncListView.New(TaoZhuangEquipBodyListCellRender, self.node_list.taozhuang_equip_body_list)
		self.taozhuang_equip_body_list:SetStartZeroIndex(false)
		self.taozhuang_equip_body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectTZEquipBodyHandler, self))
		self.taozhuang_equip_body_list:SetEndScrolledCallBack(BindTool.Bind(self.TaoZhuangEquipBodyListSetEndScrollCallBack, self))
	end

	self.suit_select_part_index = -1
	self.suit_select_tog_index = -1
	self.suit_select_data = {}
	self.suit_select_part = -1

	self.tz_jump_equip_body_seq = -1
	self.tz_jump_equip_body_equip_data = {}
	self.taozhuang_need_equip_body_tween = true

	for i = 0, 1 do
		self.node_list[ "tz_equip_type" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTZToggleHandler, self, i))
	end

	self.node_list.desc_taozhuang_text_tips.text.text = Language.Equip.SuitDescBomTip

	-- if not self.tz_need_stuff_list then
	-- 	self.tz_need_stuff_list = {}

	-- 	for i = 1, 3 do
	-- 		self.tz_need_stuff_list[i] = ItemCell.New(self.node_list["tz_stuff_item" .. i])
	-- 	end
	-- end

	for i = 1, 3 do
		self.node_list["ph_t_need_stuff_btn_" .. i].button:AddClickListener(BindTool.Bind(self.ShowEquipTaoZhuangItemTips, self, i))
	end

	self.node_list["btn_taozhuang_duanzao"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTaoZhuangDuanzao, self))
    self.node_list.btn_tz_preview.button:AddClickListener(BindTool.Bind(self.OnClickOpenPreview, self))
    self.node_list["btn_tz_suit"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTZSuit, self))
    self.node_list["btn_suit_open_boss"].button:AddClickListener(BindTool.Bind(self.OnClicKBtnOpenBossView, self))
    self.node_list["btn_taozhuang_equip_body"].button:AddClickListener(BindTool.Bind(self.OnClicKTaoZhuangEquipBodyBtn, self))
	self.node_list["btn_tz_gift_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTaoZhuangGift, self))
	-- self.suit_index = 0
	-- self.suit_select_data = nil
	
	-- self.taozhuang_need_list = {}
	-- self.taozhuang_need_cell = {}
	-- for i = 1, 3 do
	-- 	self.node_list["ph_t_need_stuff_btn_" .. i].button:AddClickListener(BindTool.Bind(self.ShowEquipTaoZhuangItemTips, self, i))
	-- 	table.insert(self.taozhuang_need_cell, self.node_list["ph_t_need_stuff_" .. i])
	-- end

	-- 创建套装物品列表
	-- if self.suit_cell_list == nil then
	-- 	self.suit_cell_list = {}
	-- end
	-- self:CreateSuitEquipList()

	-- 创建锻造所需物品格子

    -- 红点变化
    -- local remind_list = {RemindName.Equipment_Suit_ZhuMo, RemindName.Equipment_Suit_ZhuXian, RemindName.Equipment_Suit_ZhuShen}
    -- self.suit_remind_callback = BindTool.Bind(self.SuitRemindCallBack, self)
    -- self.suit_remind_list = {}
    -- self.suit_toggle_list = {}
    -- for i = 1, #remind_list do
    --     local node = self.node_list["suit_toogle_" .. i]
    --    -- node:FindObj("text").text.text = Language.Equip.TabSub3[i]
    --     XUI.AddClickEventListener(node, BindTool.Bind(self.OnClickSuitToggle, self, i - 1))
    --     self.suit_toggle_list[i - 1] = node
    --     self.suit_remind_list[i] = node:FindObj("RedPoint")
    --     RemindManager.Instance:Bind(self.suit_remind_callback, remind_list[i])
    -- end

	-- self:ClearEquipSuitData()
end

function EquipmentView:ShowFlushEquipSuit()
	self.tz_select_equip_body_seq = nil

    -- self.suit_select_part = -1
    -- local jump_index = 0
    -- for i = 0, 2 do
    --     local equment_data = EquipmentWGData.Instance:GetSuitDataListData(i)
	-- 	if equment_data then
	-- 		for k, v in pairs(equment_data) do
	-- 			local mark_remind, mark_num = EquipmentWGData.Instance:GetSuitCanUpLevelByData(v)
	-- 			if mark_remind then
	-- 				jump_index = i
	-- 				break
	-- 			end
	-- 		end
	-- 	end
    -- end

	-- if not self.suit_index then
	-- 	self.suit_index = jump_index
	-- else
	-- 	self.suit_index = math.floor((self.show_index % 10) - 1)
	-- end

	-- self:FlushEquipSuitListInfo()
end

function EquipmentView:SutiViewDelete()
	-- if self.taozhuang_need_cell then
	-- 	for k, v in pairs(self.taozhuang_need_cell) do
	-- 		v = nil
	-- 	end
	-- 	self.taozhuang_need_cell = nil
	-- 	self.taozhuang_need_list = nil
	-- end

	if self.suit_equip_list then
		for k, v in pairs(self.suit_equip_list) do
			v:DeleteMe()
		end
	end
	self.suit_equip_list = nil

    if self.suit_attr_item_list then
		for k,v in pairs(self.suit_attr_item_list) do
			v.cell:DeleteMe()
		end
		self.suit_attr_item_list = nil
	end

	if self.taozhuang_equip_body_list then
		self.taozhuang_equip_body_list:DeleteMe()
		self.taozhuang_equip_body_list = nil
	end

	if CountDownManager.Instance:HasCountDown("equip_suit_gift_time") then
		CountDownManager.Instance:RemoveCountDown("equip_suit_gift_time")
	end

	-- if self.tz_need_stuff_list then
	-- 	for k, v in pairs(self.tz_need_stuff_list) do
	-- 		v:DeleteMe()
	-- 	end

	-- 	self.tz_need_stuff_list = nil
	-- end

	-- if self.suit_cell_list then
	-- 	for k, v in pairs(self.suit_cell_list) do
	-- 		for m, n in pairs(v) do
	-- 			n:DeleteMe()
	-- 		end
	-- 	end
	-- 	self.suit_cell_list = nil
	-- end

    -- RemindManager.Instance:UnBind(self.suit_remind_callback)

	-- self.select_suit_data = nil
	self.suit_index = nil
	self.btn_qiehuan_effect = nil
	self.suit_select_part = nil
	self.suit_select_data = nil
    -- self.suit_remind_list = nil
    -- self.suit_toggle_list = nil

	self.tz_jump_equip_body_seq = nil
	self.tz_jump_equip_body_equip_data = nil
	self.taozhuang_need_equip_body_tween = nil
end

function EquipmentView:FlushEquipSuitListInfo(jump_data)
	local total_equip_body_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	self.taozhuang_equip_body_list:SetDataList(total_equip_body_list)

	local body_seq = jump_data and jump_data.open_param
	local jump_to_body_index = nil
	if body_seq then
		body_seq = tonumber(body_seq)
		-- 任务跳转，如果已解锁并已穿戴，直接跳到指定肉身
		local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(body_seq)
		if is_unlock and is_wear_equip then
			for k, v in pairs(total_equip_body_list) do
				if v.seq == body_seq then
					jump_to_body_index = k
					break
				end
			end
		end
	end

	if not jump_to_body_index then
		jump_to_body_index = self:GetTZSelectEquipBodySeq(total_equip_body_list)
	end

	self.taozhuang_equip_body_list:JumpToIndex(jump_to_body_index)

	self:FlushEquipSuitPopGiftInfo()
    -- local toggle_index = self.suit_index or 1

	-- local bundle, asset = ResPath.GetF2RawImagesPNG("a3_lq_yp_" .. toggle_index)
	-- self.node_list.equip_suit_bg.raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.equip_suit_bg.raw_image:SetNativeSize()
	-- end)

	-- local bundle2, asset2 = ResPath.GetEquipmentSuit("a3_lq_top_bg_" .. toggle_index)
	-- self.node_list.suit_attr_top_bg.image:LoadSprite(bundle2, asset2, function()
	-- 	self.node_list.suit_attr_top_bg.image:SetNativeSize()
	-- end)

    -- if self.suit_toggle_list[toggle_index]
    --     and not self.suit_toggle_list[toggle_index].toggle.isOn then
    --     self.suit_toggle_list[toggle_index].toggle.isOn = true
    --     return
    -- end

	-- local equment_data = EquipmentWGData.Instance:GetSuitDataListData(self.suit_index)
	-- if not equment_data then
	-- 	return
	-- end

	-- local remind = false
    -- local gift_num = 9999999
    -- local datas = nil
    -- local index = 99999999
    -- for k, v in pairs(equment_data) do
    --     local mark_remind, mark_num = EquipmentWGData.Instance:GetSuitCanUpLevelByData(v)
    --     if mark_remind then
    --         local sort_index = EquipmentWGData.GetSortIndex(v.index)
    --         if mark_num < gift_num or (mark_num == gift_num and sort_index <= index) then
    --             gift_num = mark_num
    --             datas = v
    --             remind = true
    --             index = sort_index
    --         end
    --     end
    -- end

	-- local temp_data = nil
	-- local temp_list = {}
	-- for k, v in pairs(self.suit_equip_list) do
	-- 	local data = equment_data[k + 1]
    --     v:SetCurSelectSuit(self.suit_index)
	-- 	v:SetData(data)
	-- 	if k == self.suit_select_part then
	-- 		temp_data = data
	-- 	end

	-- 	if not remind and data and data.item_id > 0 then
	-- 		local length = #temp_list + 1
	-- 		temp_list[length] = {}
	-- 		temp_list[length].sort_idx = EquipmentWGData.GetSortIndex(k)
	-- 		temp_list[length].data = data
	-- 	end
	-- end

    -- if remind then
    --     temp_data = datas
    -- else
    --     -- 选中有数据的那件
    --     if not temp_data and next(temp_list) then
    --         table.sort(temp_list, SortTools.KeyLowerSorter("sort_idx"))
    --         temp_data = temp_list[1].data
    --     end
    -- end

	-- self:OnSelectTaozhuangItemHandler(nil, temp_data)
end

function EquipmentView:EquipSuitChangeToTargetEquipBody(data)
	if IsEmptyTable(data) then
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZEquipBodyDataList()
	if IsEmptyTable(total_equip_body_data_list) then
		return
	end

	self.tz_jump_equip_body_seq = data.equip_body_seq
	self.tz_jump_equip_body_equip_data = data.selct_part_data

	for k, v in pairs(total_equip_body_data_list) do
		if v.seq == self.tz_jump_equip_body_seq then
			if self.taozhuang_equip_body_list then
				self.taozhuang_equip_body_list:JumpToIndex(k)
				self.tz_jump_equip_body_seq = -1
			end

			break
		end
	end
end

function EquipmentView:FlushEquipSuitPopGiftInfo()
	self.node_list.btn_tz_gift_btn:CustomSetActive(false)
	local suit_act_cfg = EquipmentWGData.Instance:GetEquipmenSuitEquipActCfg(self.tz_select_equip_body_seq, self.suit_select_part_index)
	if IsEmptyTable(suit_act_cfg) then
		return
	end

	local suit_gift_cfg = EquipmentWGData.Instance:GetSuitGiftItemCfg()
	if not suit_gift_cfg then
		return
	end

	local is_check_popup_gift_flag = false -- 今日是否能请求限时礼包
	local has_enough_stuff_cost = self:CheckEquipSuitEnoughStuffCost()    -- 材料是否足够

	local gift_grade_group = {}
	local open_day_grade_flag = LimitTimeGiftWGData.Instance:GetCurOpenDayGradeFlag()
	local split_list = string.split(suit_gift_cfg.gift_grade_group, "|")

	for i, v in ipairs(split_list) do
		local gift_grade = tonumber(v)
		gift_grade_group[gift_grade] = true
		local gift_grade_cfg = LimitTimeGiftWGData.Instance:GetConditionCfgByGrade(gift_grade)
		if gift_grade > 0 and gift_grade_cfg and open_day_grade_flag[gift_grade_cfg.open_day_seq] ~= 1 then
			is_check_popup_gift_flag = true
			break
		end 
	end

	-- 时间取最小值
	local end_time = 0
	local show_data_list = LimitTimeGiftWGData.Instance:GetCurrShowDataList()
	for k, v in pairs(show_data_list) do
		if gift_grade_group[v.grade] and (end_time <= 0 or end_time > v.end_time)then
			end_time = v.end_time
		end
	end

	local show_gift_btn = false
	local img_res = "a3_lq_wycl"
	if end_time > 0 then
		show_gift_btn = true
	elseif not is_check_popup_gift_flag then
		show_gift_btn = true
	elseif is_check_popup_gift_flag and not has_enough_stuff_cost then
		show_gift_btn = true
	end

	if end_time <= 0 and not is_check_popup_gift_flag then
		img_res = "a3_lq_mrzl"
	end

	self.node_list.btn_tz_gift_btn:CustomSetActive(show_gift_btn)
	local bundle, asset = ResPath.GetEquipmentSuit(img_res)
	self.node_list.btn_tz_gift_btn.image:LoadSprite(bundle, asset, function()
		self.node_list.btn_tz_gift_btn.image:SetNativeSize()
	end)

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if CountDownManager.Instance:HasCountDown("equip_suit_gift_time") then
		CountDownManager.Instance:RemoveCountDown("equip_suit_gift_time")
	end

	if end_time > server_time then
		CountDownManager.Instance:AddCountDown("equip_suit_gift_time", 
			BindTool.Bind(self.FinalUpdateTimeEquipSuitGift, self), 
			BindTool.Bind(self.OnCompleteEquipSuitGift, self), 
			nil, end_time - server_time, 1)
	else
		self:OnCompleteEquipSuitGift()
	end
end

function EquipmentView:FinalUpdateTimeEquipSuitGift(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM9(time)
    self.node_list["btn_tz_gift_time"].text.text = time_str
end

function EquipmentView:OnCompleteEquipSuitGift()
    self.node_list.btn_tz_gift_time.text.text = ""
end

function EquipmentView:CheckEquipSuitEnoughStuffCost()
	local has_enough_stuff_cost = true    -- 材料是否足够
	local suit_act_cfg = EquipmentWGData.Instance:GetEquipmenSuitEquipActCfg(self.tz_select_equip_body_seq, self.suit_select_part_index)
	if IsEmptyTable(suit_act_cfg) then
		return has_enough_stuff_cost
	end

	local dz_flag = EquipmentWGData.Instance:GetEquipSuitOrderFlag(self.tz_select_equip_body_seq, self.suit_select_part_index)
	if dz_flag == 1 then
		return has_enough_stuff_cost
	end

	for i = 1, 3 do
		local need_num = tonumber(suit_act_cfg["stuff_".. i .."_num"]) or 0
		local need_item_id = tonumber(suit_act_cfg["stuff_".. i .."_id"]) or 0
		local has_stuff_cost = need_num > 0 and need_item_id > 0

		if has_stuff_cost then
			local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)

			if stuff_item_num < need_num then
				has_enough_stuff_cost = false
				break
			end
		end
	end

	return has_enough_stuff_cost
end
-------------------------------------------------DEF_FUNC_END------------------------------------------------

-------------------------------------------------SELECT_START------------------------------------------------
function EquipmentView:GetTZSelectEquipBodySeq(total_equip_body_data_list)
	if self.tz_select_equip_body_seq then
		return self.tz_select_equip_body_index
	end

	local default_seq = -1
	local default_index = -1
	if not IsEmptyTable(total_equip_body_data_list) then
		for i = #total_equip_body_data_list, 1, -1 do
			local data = total_equip_body_data_list[i]

			if EquipmentWGData.Instance:GetSuitEquipBodyRemind(data.seq) > 0 then
				return i
			end

			if default_seq < 0 or data.seq > default_seq  then
				local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(data.seq)
				
				if is_unlock and is_wear_equip then
					default_seq = data.seq
					default_index = i
				end
			end
		end
	end

	return default_index
end

-- function EquipmentView:GetTZSelectEquipBodySeq(total_equip_body_data_list)
-- 	if self.tz_select_equip_body_seq then
-- 		if EquipmentWGData.Instance:GetSuitEquipBodyRemind(self.tz_select_equip_body_seq) > 0 then
-- 			return self.tz_select_equip_body_index
-- 		end
-- 	end

-- 	if not IsEmptyTable(total_equip_body_data_list) then
-- 		for k, v in pairs(total_equip_body_data_list) do
-- 			if EquipmentWGData.Instance:GetSuitEquipBodyRemind(v.seq) > 0 then
-- 				return k
-- 			end
-- 		end
-- 	end

-- 	return self.tz_select_equip_body_index or 1
-- end

-- 选中装备肉身
function EquipmentView:OnSelectTZEquipBodyHandler(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	local tz_seq_change = self.tz_select_equip_body_seq ~= data.seq
	self.tz_select_equip_body_index = item.index
	self.tz_select_equip_body_seq = data.seq

	local equment_data = EquipmentWGData.Instance:GetSuitEquipBodyEquipDataList(self.tz_select_equip_body_seq)
	for k, v in pairs(self.suit_equip_list) do
		local data = equment_data[k]
        -- v:SetCurSelectSuit(self.suit_index)
		v:SetData(data)
	end

	if not IsEmptyTable(self.tz_jump_equip_body_equip_data) then
		local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(self.tz_jump_equip_body_equip_data.index)

		if self.suit_equip_list[equip_part] then
			self.suit_equip_list[equip_part]:OnClick()
			return
		end
	end

	local default_index = self:GetTZEquipSelect(equment_data, tz_seq_change)
	if self.suit_equip_list[default_index] then
		self.suit_equip_list[default_index]:OnClick()
	end
end

-- 获取选中装备
function EquipmentView:GetTZEquipSelect(data_list, tz_seq_change)
	if not tz_seq_change and self.suit_select_part_index >= 0 then
		local equip_body_tz_remind = EquipmentWGData.Instance:GetSuitEquipBodyEquipRemind(self.tz_select_equip_body_seq, self.suit_select_part)

		if equip_body_tz_remind > 0 then
			return self.suit_select_part_index
		end
	end

	local default_index = -1
	for k, v in pairs(data_list) do
		local can_baptize = EquipmentWGData.Instance:GetSuitEquipBodyEquipRemind(self.tz_select_equip_body_seq, v.index)
		local equip_part = v.index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM

		if default_index < 0 then
			default_index = equip_part
		end

		if can_baptize > 0 then
			return equip_part
		end
	end

	if self.suit_select_part_index >= 0 and not IsEmptyTable(data_list[self.suit_select_part_index]) then
		return self.suit_select_part_index
	else
		return default_index
	end
end

-- 选中装备
function EquipmentView:OnSelectTaozhuangItemHandler(cell)
	if nil == cell or IsEmptyTable(cell.data) then
		return
	end

	local data = cell.data
	self.suit_select_part = data.index
	self.suit_select_part_index = cell.index
	self.suit_select_data = data

	for k, v in pairs(self.suit_equip_list) do
		v:SetSelectEffect(v.index == self.suit_select_part_index)
	end

	self:SelectTZRightTog(EquipmentWGData.GetEquipSuitTypeByPartType(self.suit_select_part_index))

	-- local item_data = cell and cell.data or data
	-- if item_data then
	-- 	if self.suit_select_data == item_data then
	-- 		self.suit_equip_list[item_data.index]:SuitCellClick()
	-- 		return
	-- 	end

	-- 	self.suit_select_part = item_data.index
	-- 	self.suit_select_part_index = cell.index
	-- 	self.suit_select_data = item_data

    --     -- EquipmentWGData.Instance:SetJumpSuitPreviewTab(item_data.item_cfg.order, item_data.index)
	-- 	self:FlushSuitEquipListHL(self.suit_select_part)
	-- 	self:GetCurSelectListViewData(item_data)

	-- 	-- for i, item_list in pairs(self.suit_cell_list) do
	-- 	-- 	for k, v in pairs(item_list) do
	-- 	-- 		v:OnSelectChange(v.data.index == self.suit_select_part)
	-- 	-- 	end
	-- 	-- end

	-- 	self:FlushSutiView()
	-- end
end

function EquipmentView:OnClickTZToggleHandler(index, is_on)
	if is_on and self.suit_select_tog_index ~= index then
		self:SelectTZRightTog(index)
	end
end

function EquipmentView:SelectTZRightTog(default_select_tog_index)
	self.suit_select_tog_index = default_select_tog_index

	if self.node_list[ "tz_equip_type" .. self.suit_select_tog_index] and not self.node_list[ "tz_equip_type" .. self.suit_select_tog_index].toggle.isOn then
		self.node_list[ "tz_equip_type" .. self.suit_select_tog_index].toggle.isOn = true
	end

	self:FlushSutiView()
	self:FlushEquipSuitPopGiftInfo()
end

--------------------------------------------------SELECT_END-------------------------------------------------

------------------------------------------------SET_INFO_START-----------------------------------------------
function EquipmentView:FlushSutiView()
	if IsEmptyTable(self.suit_select_data) then
		return
	end

	local suit_act_cfg = EquipmentWGData.Instance:GetEquipmenSuitEquipActCfg(self.tz_select_equip_body_seq, self.suit_select_part_index)
	if IsEmptyTable(suit_act_cfg) then
		return
	end

	-- 能否打造
	local equip_active_suit_limit_cfg = EquipmentWGData.Instance:GetEquipSuitEquipActiveCfg(self.tz_select_equip_body_seq)
	local target_color = self.suit_select_tog_index == 0 and equip_active_suit_limit_cfg.color or equip_active_suit_limit_cfg.xianqi_color
	local target_star = self.suit_select_tog_index == 0 and equip_active_suit_limit_cfg.star_num or equip_active_suit_limit_cfg.xianqi_star_num
	local cur_equip_color = self.suit_select_data.item_cfg.color
	local cur_star = self.suit_select_data.param.star_level or 0
	local can_duanzao = cur_equip_color >= target_color and cur_star >= target_star

	self.node_list.suit_limit_img:CustomSetActive(not can_duanzao)
	self.node_list.ph_taozhuang_rich_list:CustomSetActive(can_duanzao)
	-- self.node_list.suit_attr_top_bg:CustomSetActive(can_duanzao)

	if can_duanzao then
		-- 是否已打造
		local active_suit_flag = EquipmentWGData.Instance:GetEquipSuitOrderFlag(self.tz_select_equip_body_seq, self.suit_select_part_index)
		local is_active = active_suit_flag == 1

		self.node_list.ph_t_need_group:CustomSetActive(not is_active)
		self.node_list.btn_taozhuang_duanzao:CustomSetActive(not is_active)
		self.node_list.baoshi_suit_max_state:CustomSetActive(is_active)

		-- 刷新消耗
		if not is_active then
			for i = 1, 3 do
				local need_num = tonumber(suit_act_cfg["stuff_".. i .."_num"]) or 0
				local need_item_id = tonumber(suit_act_cfg["stuff_".. i .."_id"]) or 0
				local has_stuff_cost = need_num > 0 and need_item_id > 0

				if has_stuff_cost then
					local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
					local num_color = stuff_item_num < need_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
					local str = string.format("<color=%s>%s/%s</color>", num_color, stuff_item_num, need_num)
					self.node_list["ph_t_need_desc_" .. i].text.text = str

					local item_cfg = ItemWGData.Instance:GetItemConfig(need_item_id)
					local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
					self.node_list["ph_t_need_stuff_" .. i].image:LoadSprite(bundle, asset, function ()
						self.node_list["ph_t_need_stuff_" .. i].image:SetNativeSize()
					end)
					
					self.node_list["ph_t_need_desc_" .. i]:SetActive(true)
				else
					self.node_list["ph_t_need_desc_" .. i]:SetActive(false)
				end
			end

			-- for k, v in ipairs(self.tz_need_stuff_list) do
			-- 	local need_num = tonumber(suit_act_cfg["stuff_".. k .."_num"]) or 0
			-- 	local need_item_id = tonumber(suit_act_cfg["stuff_".. k .."_id"]) or 0
			-- 	local has_stuff_cost = need_num > 0 and need_item_id > 0

			-- 	if has_stuff_cost then
			-- 		local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
			-- 		local num_color = stuff_item_num < need_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
	
			-- 		v:SetFlushCallBack(function ()
			-- 			v:SetRightBottomColorText(ToColorStr(stuff_item_num .. "/" .. need_num, num_color))
			-- 			v:SetRightBottomTextVisible(true)
			-- 		end)
			-- 		v:SetData({item_id = need_item_id})
			-- 	end
	
			-- 	self.node_list["tz_stuff_item" .. k]:CustomSetActive(has_stuff_cost)
			-- end
		end

		-- 刷新属性
		local list_data = EquipmentWGData.Instance:GetEquipmenSuitAttrCfg(self.tz_select_equip_body_seq, self.suit_select_part_index, self.suit_select_tog_index)
		self:FlushSuitAttrListView(list_data)

		-- 打造红点
		local dz_remind = EquipmentWGData.Instance:GetSuitEquipBodyEquipRemind(self.tz_select_equip_body_seq, self.suit_select_part)
		self.node_list.taozhuang_remind:CustomSetActive(dz_remind > 0)

		-- 套装名字
		local suit_name = EquipmentWGData.Instance:GetEquipmenSuitTitleName(self.tz_select_equip_body_seq, self.suit_select_tog_index)
		local complete_num = EquipmentWGData.Instance:GetEquipmenSuitAllActiveNum(self.suit_select_tog_index)
		local suit_open_num = EquipmentWGData.Instance:GetSuitActiveSameEquipNum(self.tz_select_equip_body_seq, self.suit_select_tog_index)
		self.node_list["lable_taozhuang_name"].text.text = string.format("%s(%d/%d)", suit_name, suit_open_num, complete_num)

	else
		local limit_desc = ""

		if cur_equip_color < target_color and cur_star < target_star then
			limit_desc = string.format(Language.Equip.SuitEquipDeficient1, Language.Common.ItemQualityColor[target_color], target_star)
		elseif cur_equip_color < target_color then
			limit_desc = string.format(Language.Equip.SuitEquipDeficient2, Language.Common.ItemQualityColor[target_color])
		elseif cur_star < target_star then	
			limit_desc = string.format(Language.Equip.SuitEquipDeficient3, target_star)
		end

		self.node_list.suit_limit_desc.text.text = limit_desc

		self.node_list.ph_t_need_group:CustomSetActive(false)
		self.node_list.btn_taozhuang_duanzao:CustomSetActive(false)
		self.node_list.baoshi_suit_max_state:CustomSetActive(false)
	end

	-- local suit_item_cfg = self.suit_select_data.suit_item_cfg
	-- local select_item_data = self:GetCurSelectListViewData()
	-- if not select_item_data then
	-- 	return
	-- end

	-- local item_cfg = select_item_data.item_cfg
	-- local suit_item_cfg = select_item_data.suit_item_cfg

	-- -- 按钮
	-- self.node_list.taozhuang_remind:SetActive(EquipmentWGData.Instance:GetSuitCanUpLevelByData(select_item_data))
	-- --XUI.SetButtonEnabled(self.node_list["btn_taozhuang_duanzao"], select_item_data.is_activation == EquipmentWGData.ACTIVATION_TYPE.OK)
	-- if select_item_data.is_activation ~= EquipmentWGData.ACTIVATION_TYPE.OK then
	-- 	self.node_list.suit_duanzao_btn_text.text.text = Language.Equipment.SuitForge
	-- else
	-- 	self.node_list.suit_duanzao_btn_text.text.text = Language.Equipment.SuitForgeName
	-- end

	-- -- 需要材料
	-- local is_show_need = false
	-- local total_count = 0
	-- local flag_count = 0
	-- for k, v in ipairs(self.taozhuang_need_cell) do
    --     local need_num = suit_item_cfg and tonumber(suit_item_cfg["stuff_".. k .."_num"]) or 0
	-- 	if suit_item_cfg and suit_item_cfg["stuff_".. k .."_id"] ~= 0 and need_num > 0
    --     and select_item_data.is_activation == EquipmentWGData.ACTIVATION_TYPE.OK then
	-- 		is_show_need = true

	-- 		total_count = total_count + 1

	-- 		local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(suit_item_cfg["stuff_".. k .."_id"])

	-- 		local num_color = stuff_item_num < need_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
	-- 		if stuff_item_num >= need_num then
	-- 			flag_count = flag_count + 1
	-- 		end

	-- 		local str = string.format("<color=%s>%s/%s</color>", num_color, stuff_item_num, need_num)
	-- 		self.node_list["ph_t_need_desc_" .. k].text.text = str

	-- 		self.node_list["ph_t_need_desc_" .. k]:SetActive(true)
	-- 		self.taozhuang_need_list[k] = {item_id = suit_item_cfg["stuff_".. k .."_id"], need_count = need_num}

	-- 		local bundle, asset = ResPath.GetItem(suit_item_cfg["stuff_".. k .."_id"])
	-- 		v.image:LoadSprite(bundle, asset, function ()
	-- 			v.image:SetNativeSize()
	-- 		end)
	-- 	else
	-- 		self.node_list["ph_t_need_desc_" .. k]:SetActive(false)
	-- 		self.taozhuang_need_list[k] = {item_id = 0, need_count = 0}
	-- 	end
	-- end
	
	-- self.node_list.ph_t_need_group:SetActive(is_show_need)

    -- local part = ItemWGData.GetEquipPartByItemId(select_item_data.item_id)
    -- local equip_type = EquipmentWGData.GetEquipSuitTypeByPartType(part)
    -- local suit_open_num = select_item_data.suit_open_num

	-- local can_show = select_item_data.is_activation ~= EquipmentWGData.ACTIVATION_TYPE.RANK_DEFICIENT
	-- 				and select_item_data.is_activation ~= EquipmentWGData.ACTIVATION_TYPE.NOT_BEFORE

	-- -- 特殊状态显示
    -- local suit_name = ""
    -- if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
    --     suit_name = Language.Equip.XianQiSuit
    -- else
    --     suit_name = EquipmentWGData.Instance:GetEquipmenSuitName(item_cfg.order)
    -- end

    -- local suit_str = ""
    -- if can_show then
    --     local suit_index_name = Language.Equip.TabSub9[select_item_data.suit_index + 1]
    --     suit_str = string.format("[%s] %s(%d/%d)", suit_index_name, suit_name, suit_open_num, select_item_data.complete_suit_num)
    -- else
    --     suit_str = "" .. Language.Equip.SuitAttrTitle
    -- end

	-- self.node_list["lable_taozhuang_name"].text.text = suit_str
    -- self.node_list["ph_taozhuang_rich_list"]:SetActive(can_show)
	-- self.node_list.suit_limit_img:SetActive(not can_show)
	-- self.node_list.btn_suit_open_boss:SetActive(not can_show)
	-- self.node_list.suit_attr_top_bg:SetActive(can_show)

	-- local limit_desc = ""
	-- local limit_cfg = EquipmentWGData.Instance:GetEquipmenSuitActivation(select_item_data.suit_index)
	-- if select_item_data.is_activation == EquipmentWGData.ACTIVATION_TYPE.RANK_DEFICIENT then --阶数不足
	-- 	if equip_type == GameEnum.EQUIP_BIG_TYPE_XIANQI then
	-- 		limit_desc = string.format(Language.Equip.SuitRankDeficient8, limit_cfg.xianqi_min_order,
	-- 									Language.Common.ItemQualityColor[limit_cfg.xianqi_color])
	-- 	else
	-- 		limit_desc = string.format(Language.Equip.SuitRankDeficient7, limit_cfg.min_order,
	-- 									Language.Common.ItemQualityColor[limit_cfg.color], limit_cfg.star_num)
	-- 	end

	-- elseif select_item_data.is_activation == EquipmentWGData.ACTIVATION_TYPE.NOT_BEFORE then -- 需先锻造xx装备
	-- 	limit_desc = string.format(Language.Equip.SuitNotBefore, Language.Equip.TabSub9[select_item_data.suit_index])
	-- end

	-- self.node_list.suit_limit_desc.text.text = limit_desc

	-- local is_max_level = select_item_data.suit_open_level == SUIT_TYPE.SUIT_TYPE_ZHUSHEN
    --                         and select_item_data.suit_open_level == self.suit_index
	-- self:SetSuitMaxState(is_max_level, not can_show)

    -- local list_data = EquipmentWGData.Instance:GetEquipmenSuitStoneAttr(select_item_data.suit_index,
    --                     item_cfg.order,
    --                     select_item_data.index)

	-- self:FlushSuitAttrListView(list_data)
	-- self.node_list.baoshi_suit_max_state:SetActive(select_item_data.is_activation ~= EquipmentWGData.ACTIVATION_TYPE.OK and can_show)
	-- self.node_list.btn_taozhuang_duanzao:SetActive(select_item_data.is_activation == EquipmentWGData.ACTIVATION_TYPE.OK)
end
------------------------------------------------SET_INFO_END-------------------------------------------------

-- --清空所有数据
-- function EquipmentView:ClearEquipSuitData()
-- 	self.node_list["ph_taozhuang_rich_list"]:SetActive(false)

-- 	self.node_list.suit_limit_img:SetActive(false)
-- 	self.node_list["lable_taozhuang_name"].text.text = "  " .. Language.Equip.SuitAttrTitle

-- 	for k, v in ipairs(self.taozhuang_need_cell) do
-- 		self.node_list["ph_t_need_desc_" .. k]:SetActive(false)
-- 		self.taozhuang_need_list[k] = {item_id = 0, need_count = 0}
-- 	end

-- 	--XUI.SetButtonEnabled(self.node_list["btn_taozhuang_duanzao"], false)
-- 	self.node_list.suit_duanzao_btn_text.text.text = Language.Equipment.SuitForge
-- end



-- function EquipmentView:OnClickSuitToggle(index, is_on)
--     if not is_on then
--         return
--     end

--     self.suit_index = index or self.suit_index
--     self:FlushEquipSuitListInfo()
-- end

-- function EquipmentView:FlushSuitEquipListHL(index)
-- 	for k, v in pairs(self.suit_equip_list) do
-- 		if v.data then
-- 			v:SetSelectEffect(v.data.index == index)
-- 		end
-- 	end
-- end

-- 刷新属性列表
function EquipmentView:FlushSuitAttrListView(list_data)
    if IsEmptyTable(list_data) then
        return
    end

    for i,v in ipairs(list_data) do
    	if self.suit_attr_item_list[i] then
    		if self.suit_attr_item_list[i].loaded_flag then
    			self.suit_attr_item_list[i].cell:SetData(v)
    		end
    	else
    		local async_loader = AllocAsyncLoader(self, "taozhuang_view" .. i)
    		self.suit_attr_item_list[i] = {}
    		self.suit_attr_item_list[i].loaded_flag = false
            async_loader:SetParent(self.node_list["suit_attr_list"].transform)
    		async_loader:Load("uis/view/equipment_suit_ui_prefab", "attri_num_render", function (obj)
    			local cell = TaozhuangRichRender.New(obj)
    			cell:SetData(v)
    			self.suit_attr_item_list[i].cell = cell
    			self.suit_attr_item_list[i].loaded_flag = true
    		end)
    	end
    end

    local active_num = #list_data
	for i,v in ipairs(self.suit_attr_item_list) do
		if v.loaded_flag then
			if i <= active_num then
				v.cell:SetActive(true)
			else
				v.cell:SetActive(false)
			end
		end
	end

    -- 置顶
    self.node_list["ph_taozhuang_rich_list"].scroll_rect.verticalNormalizedPosition = 1
end

-- function EquipmentView:SetSuitMaxState(state, is_limit)
-- 	--self.node_list["taozhuang_max_content"]:SetActive(not state and (not is_limit))
-- 	--self.node_list["baoshi_suit_max_state"]:SetActive(state)
-- end

function EquipmentView:OnClickBtnTaoZhuangDuanzao()
	local suit_act_cfg = EquipmentWGData.Instance:GetEquipmenSuitEquipActCfg(self.tz_select_equip_body_seq, self.suit_select_part_index)

	if IsEmptyTable(suit_act_cfg) then
		return 
	end

	local dz_flag = EquipmentWGData.Instance:GetEquipSuitOrderFlag(self.tz_select_equip_body_seq, self.suit_select_part_index)

	if dz_flag == 1 then
		return
	end

	for i = 1, 3 do
		local need_num = tonumber(suit_act_cfg["stuff_".. i .."_num"]) or 0
		local need_item_id = tonumber(suit_act_cfg["stuff_".. i .."_id"]) or 0
		local has_stuff_cost = need_num > 0 and need_item_id > 0

		if has_stuff_cost then
			local stuff_item_num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)

			if stuff_item_num < need_num then
				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = need_item_id}, need_num - stuff_item_num)
				return
			end
		end
	end

    EquipmentWGCtrl.Instance:SendEquipmentSuitReq(SUIT_OPERA_TYPE.SUIT_OPERA_TYPE_SUIT_FORGE, self.suit_select_part)

	-- local data = self:GetCurSelectListViewData()
	-- if nil == data then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Equip.XuanzeZhuangBei)
	-- 	return
	-- end

	-- if not IsEmptyTable(self.taozhuang_need_list) then
	-- 	for k, v in pairs(self.taozhuang_need_list) do
	-- 		if v.need_count > 0 then
	-- 			local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
	-- 			if num < v.need_count then
	-- 				TipWGCtrl.Instance:OpenItemTipGetWay({item_id = v.item_id}, v.need_count - num)
	-- 				return
	-- 			end
	-- 		end
	-- 	end
	-- end

    -- EquipmentWGCtrl.Instance:SendEquipmentSuitReq(SUIT_OPERA_TYPE.SUIT_OPERA_TYPE_SUIT_FORGE, self.suit_index, data.index)
end

function EquipmentView:OnClickBtnTaoZhuangGift()
	local suit_gift_cfg = EquipmentWGData.Instance:GetSuitGiftItemCfg()
	if not suit_gift_cfg then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Equip.SuitError[1]) 
		return
	end

	-- 请求弹窗礼包
	local is_check_popup_gift_flag = false
	local gift_grade_group = {}

	local open_day_grade_flag = LimitTimeGiftWGData.Instance:GetCurOpenDayGradeFlag()
	local split_list = string.split(suit_gift_cfg.gift_grade_group, "|")
	for i, v in ipairs(split_list) do
		local gift_grade = tonumber(v)
		gift_grade_group[gift_grade] = true
		local gift_grade_cfg = LimitTimeGiftWGData.Instance:GetConditionCfgByGrade(gift_grade)
		if gift_grade > 0 and gift_grade_cfg and open_day_grade_flag[gift_grade_cfg.open_day_seq] ~= 1 then
			is_check_popup_gift_flag = true
			LimitTimeGiftWGCtrl.Instance:CheckNeedEquipSuitHarmonyPopupGift(gift_grade)
		end 
	end

	if is_check_popup_gift_flag then
		return
	end 

	-- 判断有没礼包  打开界面
	local show_data_list = LimitTimeGiftWGData.Instance:GetCurrShowDataList()
	for k, v in pairs(show_data_list) do
		if gift_grade_group[v.grade] then
			LimitTimeGiftWGCtrl.Instance:OpenLimitTimeGiftPurchaseView()
			return
		end
	end

	TipWGCtrl.Instance:ShowSystemMsg(Language.Equip.SuitError[1]) 
end

function EquipmentView:ShowSuitForgeSuceeEffect()
    self.node_list["effect_root_suit"]:SetActive(true)
    TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_duanzao, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["effect_root_suit"]})
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end

function EquipmentView:OnClickOpenPreview()
    EquipmentWGCtrl.Instance:OpenSuitPreviewView()
end

function EquipmentView:OnClickBtnTZSuit()
    -- ViewManager.Instance:Open(GuideModuleName.EquipmentSuitZongLanView)
	if self.tz_select_equip_body_seq then
		EquipmentWGCtrl.Instance:OpenSuitZongLanView({equip_body_seq = self.tz_select_equip_body_seq})
	end
end

function EquipmentView:OnClicKBtnOpenBossView()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, TabIndex.boss_world)
end

function EquipmentView:OnClicKTaoZhuangEquipBodyBtn()
	EquipmentWGCtrl.Instance:OpenEquipSuitOverviewView()
end

function EquipmentView:ShowEquipTaoZhuangItemTips(index)
	local suit_act_cfg = EquipmentWGData.Instance:GetEquipmenSuitEquipActCfg(self.tz_select_equip_body_seq, self.suit_select_part_index)
	if IsEmptyTable(suit_act_cfg) then
		return
	end

	local need_item_id = tonumber(suit_act_cfg["stuff_".. index .."_id"]) or 0
	if need_item_id > 0 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = need_item_id})
	end
end

--获取当前选中的列表中的数据
-- function EquipmentView:GetCurSelectListViewData(select_data)
-- 	if nil == select_data then
-- 		return self.select_suit_data
-- 	end
-- 	self.select_suit_data = select_data
-- end

-- function EquipmentView:SuitRemindCallBack(remind_name, num)
--     if remind_name == RemindName.Equipment_Suit_ZhuMo then
--         self.suit_remind_list[1]:SetActive(num > 0)
--     elseif remind_name == RemindName.Equipment_Suit_ZhuXian then
--         self.suit_remind_list[2]:SetActive(num > 0)
--     elseif remind_name == RemindName.Equipment_Suit_ZhuShen then
--         self.suit_remind_list[3]:SetActive(num > 0)
--     end
-- end

function EquipmentView:TaoZhuangEquipBodyListSetEndScrollCallBack()
	if self.taozhuang_need_equip_body_tween then
		self.taozhuang_need_equip_body_tween = false

		local tween_info = UITween_CONSTS.EquipBody

		local cell_list = self.taozhuang_equip_body_list:GetAllItems()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end
end

-----------------------------------TaozhuangRichRender-------------------------------
TaozhuangRichRender = TaozhuangRichRender or BaseClass(BaseRender)
function TaozhuangRichRender:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = {}
        local key = "attr_" .. i
        self.attr_list[i].attr_name = self.node_list.attr_list:FindObj(key)
        self.attr_list[i].attr_value = self.node_list.attr_list:FindObj(key .. "/value")
    end
end

function TaozhuangRichRender:__delete()
    self.attr_list = nil
end

function TaozhuangRichRender:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
    end

	self.node_list.need_flag_green:SetActive(data.is_open)
	self.node_list.need_flag_red:SetActive(not data.is_open)

    local need_str = string.format(Language.Equip.SuitNumCompany2, data.same_order_num)
    self.node_list.need_num.text.text = need_str
    local list = data.attr_list
    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = Language.Common.AttrNameList2[list[k].attr_type]
            local value = is_per and list[k].value or list[k].value / 100 .. "%"
            v.attr_name.text.text = name
            v.attr_value.text.text = value
            v.attr_name:SetActive(true)
        else
            v.attr_name:SetActive(false)
        end
    end
end


-- ============================SuitShowEquipCell============================
SuitShowEquipCell = SuitShowEquipCell or BaseClass(BaseRender)

local suit_effect_list = {
    "UI_lan_wpk_shandian",
    "UI_hong_wpk_shandian",
    "UI_jin_wpk_shandian",
}

function SuitShowEquipCell:LoadCallBack()
	if not self.equip_cell then
		self.equip_cell = ItemCell.New(self.node_list["item_pos"])
	end

	XUI.AddClickEventListener(self.node_list.click_block_btn, BindTool.Bind(self.OnClick, self))
end

function SuitShowEquipCell:__delete()
	if self.equip_cell then
		self.equip_cell:DeleteMe()
		self.equip_cell = nil
	end
	self.equip_click_callback = nil
    -- self.parent_select_suit = nil
end

function SuitShowEquipCell:SetEquipClickCallBack(callback)
	self.equip_click_callback = callback
end

function SuitShowEquipCell:OnClick()
    if self.equip_click_callback ~= nil then
		self.equip_click_callback(self)
	end
end

-- function SuitShowEquipCell:SetCurSelectSuit(suit_index)
--     self.parent_select_suit = suit_index or 0
-- end

function SuitShowEquipCell:SetSelectEffect(bool)
	self.node_list.select_effect:SetActive(bool)
end

function SuitShowEquipCell:OnFlush()
	local show_effect = "UI_wupinkuang_l3_jin"
	self.equip_cell:SetEffectEnable(false, show_effect)

    -- for k, v in ipairs(suit_effect_list) do
    --     self.equip_cell:SetEffectEnable(false, v)
    -- end

	if not IsEmptyTable(self.data) and self.data.item_id > 0 then
		self.equip_cell:SetData(self.data)
		self.equip_cell:SetEffectRootEnable(false)

		local is_dz = EquipmentWGData.Instance:GetEquipSuitOrderFlagByIndex(self.data.index) > 0

		if is_dz then
			self.equip_cell:SetEffectEnable(true, show_effect)
		end
		
        -- if self.data.suit_open_level >= SUIT_TYPE.SUIT_TYPE_ZHUMO then
		-- 	local effect_index = self.data.suit_open_level - 1
        --     self.equip_cell:SetEffectEnable(true, suit_effect_list[effect_index] or suit_effect_list[1])
        --     local bundle, asset = ResPath.GetLoadingPath("a3_equip_suit_" .. self.data.suit_open_level)
        --     self.equip_cell:SetEqSuitIcon(bundle, asset, true)
        -- else
        --     self.equip_cell:SetEffectRootEnable(true)
        --      self.equip_cell:SetEqSuitIconIsShow(false)
        -- end

		self.equip_cell:SetEffectRootEnable(true)
		self.equip_cell:SetEqSuitIconIsShow(false)

		local remind = EquipmentWGData.Instance:GetSuitCanUpLevelByData(self.data)
		self.node_list.red_point:SetActive(remind)

		self.node_list["suit_type_flag"]:SetActive(is_dz)
		-- for i = 0, 2 do
		-- 	self.node_list["suit_type_img" .. i]:SetActive(i <= self.data.suit_open_level)
		-- end
	else
		self.equip_cell:SetData({})
		local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(self.index)
		self.equip_cell:SetItemIcon(ResPath.GetEquipIcon(self.index))
		self.node_list.red_point:SetActive(false)
		self.node_list["suit_type_flag"]:SetActive(false)
	end
end

function SuitShowEquipCell:SuitCellClick()
    if self.equip_cell ~= nil then
		self.equip_cell:OnClick()
	end
end

----------------------------------------------TaoZhuangEquipBodyListCellRender---------------------------------------------
TaoZhuangEquipBodyListCellRender = TaoZhuangEquipBodyListCellRender or BaseClass(BaseRender)

function TaoZhuangEquipBodyListCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickTipBtn, self))
end

function TaoZhuangEquipBodyListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	
	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
	self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
		self.node_list.icon_hl.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name

	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not is_unlocak and not is_wear_equip)
	self.node_list.no_equip_tip:CustomSetActive(is_unlocak and not is_wear_equip)
	self.node_list.btn_tip:CustomSetActive(not can_duanzao)

	local remind = EquipmentWGData.Instance:GetSuitEquipBodyRemind(self.data.seq) > 0
	self.node_list.remind:CustomSetActive(remind)
end

function TaoZhuangEquipBodyListCellRender:OnSelectChange(is_select)
	local is_special = self.data.type == 1
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special)
end

function TaoZhuangEquipBodyListCellRender:OnClickTipBtn()
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)

	if not can_duanzao then
		if not is_unlocak then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLockCanDuanZao)
		elseif not is_wear_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquipCanDuanZao)
		end
	end
end

function TaoZhuangEquipBodyListCellRender:PlayItemTween()
	UITween.FakeHideShow(self.node_list.root)
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
	self.cell_delay_key = "TaoZhuangEquipBodyItemCellRender" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.root then
			UITween.FakeToShow(self.node_list.root)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end