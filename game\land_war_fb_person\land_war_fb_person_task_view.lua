LandWarFbPersonTaskView = LandWarFbPersonTaskView or BaseClass(SafeBaseView)

function LandWarFbPersonTaskView:__init()
	self.is_safe_area_adapter = true
	self.view_name = "LandWarFbPersonTaskView"
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_land_war_fb_scene_view")
end

function LandWarFbPersonTaskView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function LandWarFbPersonTaskView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function LandWarFbPersonTaskView:LoadCallBack()
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

    if not self.boss_list then
        self.boss_list = AsyncListView.New(LandWarFbBossCell, self.node_list.boss_list)
		self.boss_list:SetStartZeroIndex(false)
		self.boss_list:SetSelectCallBack(BindTool.Bind(self.OnClickBossItem, self))
    end
end

function LandWarFbPersonTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list["task_root"] then
		self.obj = self.node_list["task_root"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function LandWarFbPersonTaskView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.boss_list then
        self.boss_list:DeleteMe()
        self.boss_list = nil
    end

    self.select_boss_seq = nil
	self.cur_scene_id = nil
end

function LandWarFbPersonTaskView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function LandWarFbPersonTaskView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushLeftBossInfo()
		elseif k == "rank_info"then
			self:FlushRankInfo(v)
        end
    end
end

function LandWarFbPersonTaskView:FlushLeftBossInfo()
	local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id ~= self.cur_scene_id then
		self.select_boss_seq = nil
		self.cur_scene_id = scene_id
	end

	local land_cfg = LandWarFbPersonWGData.Instance:GetAllStageLandCfg()
	for k, v in pairs(land_cfg) do
		if scene_id == v.scene_id then
			cur_stage = v.stage
		end
	end

    local monster_list = LandWarFbPersonWGData.Instance:GetCurStageMonsterListData(cur_stage)
    if not monster_list then
		return 
    end

	self.boss_list:SetDataList(monster_list)

    if self.select_boss_seq then
        local active = LandWarFbPersonWGData.Instance:GetBossActive(self.select_boss_seq)
        if not active then
            self.select_boss_seq = nil
        end
    end

    if not self.select_boss_seq then
		local jump_index = 1
        for k, v in pairs(monster_list) do
            local active = LandWarFbPersonWGData.Instance:GetBossActive(v.seq)
		    if active then
				jump_index = k
				break
			end
		end

		self.boss_list:JumpToIndex(jump_index)
    end

	local score_num = 0
	for k, v in pairs(monster_list) do
		local active = LandWarFbPersonWGData.Instance:GetBossActive(v.seq)
		if not active then
			score_num = score_num + v.score
		end
	end

	self.node_list.desc_camp_score.text.text = string.format(Language.LandWarFbPersonView.SceneCampScore, score_num)
end

function LandWarFbPersonTaskView:FlushRankInfo(info)

end

function LandWarFbPersonTaskView:OnClickBossItem(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	self.select_boss_seq = data.seq

	local sence_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
	if role == nil then
		return
	end

    local role_x, role_y = role:GetLogicPos()
	local pos_data = Split(data.monster_pos, ",")
	local pos_x, pos_y = pos_data[1],  pos_data[2]

	if role_x == pos_x and role_y == pos_y then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		GuajiWGCtrl:StopGuaji()
		AtkCache.target_obj = nil

        local active = LandWarFbPersonWGData.Instance:GetBossActive(data.seq)

		if active then
			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		else
			MoveCache.SetEndType(MoveEndType.Normal)
		end
		
		MoveCache.param1 = data.monster_id
		GuajiCache.monster_id = data.monster_id
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		
		local range = BossWGData.Instance:GetMonsterRangeByid(data.monster_id)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, pos_x, pos_y, range, nil, nil, nil, function()
		end)
	end
end
-------------------------------------LandWarFbBossCell---------------------------------
LandWarFbBossCell = LandWarFbBossCell or BaseClass(BaseRender)
function LandWarFbBossCell:__delete()

end

function LandWarFbBossCell:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

    local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
	self.node_list.TextDesc.text.text = boss_cfg.name

    local active = LandWarFbPersonWGData.Instance:GetBossActive(self.data.seq)
    self.node_list.TimeDesc.text.text = active and Language.LandWarFbPersonView.BossActiveState[0] or Language.LandWarFbPersonView.BossActiveState[1]

    if not active then
		self.node_list.OwnerDesc.text.text = ""
		self.node_list.OwnerIcon.image:LoadSprite(ResPath.GetPositionalWarfareImg("a3_zdz_tt_1"))
	else
		self.node_list.OwnerDesc.text.text = Language.PositionalWarfare.Neutral
	end

	self.node_list.OwnerIcon:CustomSetActive( not active)
end

function LandWarFbBossCell:OnSelectChange(is_select)
	if self.node_list["SelectLigth"] then
		self.node_list["SelectLigth"]:SetActive(is_select)
	end
end
