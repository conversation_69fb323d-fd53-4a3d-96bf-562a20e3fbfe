CharmLingZhuTunShiView = CharmLingZhuTunShiView or BaseClass(SafeBaseView)

function CharmLingZhuTunShiView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 574)})
	self:AddViewResource(0, "uis/view/cultivation_ui/charm_prefab", "layout_longzhu_tunshi_view")
end

function CharmLingZhuTunShiView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Charm.ChanrmLongZhuTunShiTitle

	if not self.longzhu_tunshi_grid then
        self.longzhu_tunshi_grid = CharmLingZhuTunShiBagGrid.New()
        self.longzhu_tunshi_grid:SetStartZeroIndex(false)
        self.longzhu_tunshi_grid:SetIsMultiSelect(true)
        self.longzhu_tunshi_grid:CreateCells({
            col = 8,
            cell_count = 280,
            list_view = self.node_list["longzhu_tunshi_grid"],
            itemRender = CharmTunShiRender,
            change_cells_num = 2,
        })
        self.longzhu_tunshi_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

	XUI.AddClickEventListener(self.node_list.btn_longzhu_tunshi, BindTool.Bind(self.OnClickLongZhuTunShi, self))
end

function CharmLingZhuTunShiView:ReleaseCallBack()
	if self.longzhu_tunshi_grid then
        self.longzhu_tunshi_grid:DeleteMe()
        self.longzhu_tunshi_grid = nil
    end
end

function CharmLingZhuTunShiView:OnClickLongZhuTunShi()
	local is_max_level, can_up_level = CultivationWGData.Instance:CanCharmLingZhuUpLevel()
	if is_max_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.CharmTunShiMaxLevel)
		return
	end

	if can_up_level then
		CultivationWGCtrl.Instance:OnCSCharmOperate(CHARM_OPERATE_TYPE.YINYANG_UPLEVEL)
	else
		local select_list = self.longzhu_tunshi_grid:GetAllSelectCell()
		local targete_data_list = {}
	
		for k, v in pairs(select_list) do
			table.insert(targete_data_list, {item_id = v.item_id, bag_index = v.index, count = v.num})
		end
	
		if IsEmptyTable(targete_data_list) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.MeltingError)
			return
		end
	
		CultivationWGCtrl.Instance:OnCSCharmEquipDecompos(#targete_data_list, targete_data_list)
	end
	self:Close()
end

function CharmLingZhuTunShiView:OnBagSelectItemCB(cell)
    self:CalcSelect()
end

function CharmLingZhuTunShiView:OnFlush()
    local item_list = CultivationWGData.Instance:GetCharmBagSortDataList()
    self.longzhu_tunshi_grid:SetDataList(item_list)
	self.longzhu_tunshi_grid:SetMeltingSelcetAll(GameEnum.ITEM_COLOR_ORANGE)
	self:CalcSelect()

	local is_max_level, can_up_level = CultivationWGData.Instance:CanCharmLingZhuUpLevel()
	self.node_list.btn_longzhu_tunshi_text.text.text = not is_max_level and can_up_level and Language.Charm.CharmTunShiBtn[1] or Language.Charm.CharmTunShiBtn[0]
	self.node_list.btn_longzhu_tunshi_remind:CustomSetActive(not is_max_level and can_up_level)
end

function CharmLingZhuTunShiView:CalcSelect()
	local select_list = self.longzhu_tunshi_grid:GetAllSelectCell()
	local can_get_exp = 0

	for k, v in pairs(select_list) do
		local cfg = CultivationWGData.Instance:GetCharmEquipByItemId(v.item_id)

		if not IsEmptyTable(cfg) then
			can_get_exp = can_get_exp + cfg.get_exp * v.num
		end
	end

	local longzhu_level = CultivationWGData.Instance:GetCharmYingYangLevel()
	local is_max_level = CultivationWGData.Instance:IsCharmYingYangMaxLevel()

	if is_max_level then
		self.node_list.longzhu_can_get_exp.text.text = Language.Charm.CharmLongZhuMaxLevel
	else
		local current_level_cfg = CultivationWGData.Instance:GetCharmYingYangLevelCfg(longzhu_level)
		local need_exp = current_level_cfg.need_exp
		local has_exp = CultivationWGData.Instance:GetCharmYingYangExp()
		-- local current_slider = 0
		-- local target_slider = 0
		-- if has_exp > need_exp then
		-- 	current_slider = 100
		-- 	target_slider = 100
		-- else
		-- 	current_slider = math.floor((has_exp / need_exp) * 100)
		-- 	all_exp = has_exp + can_get_exp
		-- 	target_slider = all_exp > need_exp and 100 or math.floor((all_exp / need_exp) * 100)
		-- end

		-- self.node_list.longzhu_can_get_exp.text.text = string.format(Language.Charm.CharmTunShiYuLan, current_slider, target_slider)

		local all_exp = has_exp + can_get_exp
		-- local target_exp =  all_exp > need_exp and need_exp or all_exp
		self.node_list.longzhu_can_get_exp.text.text = string.format(Language.Charm.CharmTunShiYuLanNum, all_exp, need_exp)
	end
end

---------------------------------------------------------------------------------------------
CharmLingZhuTunShiBagGrid = CharmLingZhuTunShiBagGrid or BaseClass(AsyncBaseGrid)
function CharmLingZhuTunShiBagGrid:SetMeltingOneKeySelcet(color, order, less_than_star_level)
	less_than_star_level = less_than_star_level or 3
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	for i = 1, self.has_data_max_index do
		local data = self.cell_data_list[i]

		if not IsEmptyTable(data) then
			if data.color <= color and data.order <= order then
				local star_level = data.param and data.param.star_level or 0

				if star_level < less_than_star_level then
					self.select_tab[1][i] = true
					self.cur_multi_select_num = self.cur_multi_select_num + 1
				end
			end
		end
	end

	self:__DoRefreshSelectState()
end

function CharmLingZhuTunShiBagGrid:SetMeltingSelcetAll(color)
	self.select_tab[1] = {}
	self.cur_multi_select_num = 0

	local num = self.has_data_max_index > 50 and 50 or self.has_data_max_index
	for i = 1, num do
		local data = self.cell_data_list[i]
		if not IsEmptyTable(data) then
			if data.color < color then
				self.select_tab[1][i] = true
				self.cur_multi_select_num = self.cur_multi_select_num + 1
			end
		end
	end

	self:__DoRefreshSelectState()
end

function CharmLingZhuTunShiBagGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= 50 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Charm.ResloveLimit)
            return true
        end
    end

    return false
end

----------------------------------------------------------------------------------
CharmTunShiRender = CharmTunShiRender or BaseClass(ItemCell)
function CharmTunShiRender:__init()
	self:SetIsShowTips(false)
	self:UseNewSelectEffect(true)
end

function CharmTunShiRender:OnFlush()
	ItemCell.OnFlush(self)

	if self.data.item_id then
		local charm_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(self.data.item_id)
		if not IsEmptyTable(charm_cfg) then
			local order_str = string.format(Language.Charm.CharmOrderLevel, charm_cfg.order)
			self:Nodes("right_top_imgnum_txt").text.text = order_str
			self:TrySetActive("right_top_imgnum_txt", nil, true)
		end
	end
end

function CharmTunShiRender:SetSelect(is_select)
	self:SetSelectEffect(is_select)
end