-- 五行天赋
local MAX_TALENT_SKILL_COUNT = 18
function FiveElementsView:InitFiveElementTalentView()
	if IsEmptyTable(self.talent_skill_list) then
		self.talent_skill_list = {}
		for i = 0, MAX_TALENT_SKILL_COUNT - 1 do
			local obj = ResMgr:Instantiate(self.node_list["talent_skill_item"].gameObject)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.node_list.cell_content:FindObj("talent_cell_" .. i).transform, false)
			obj:SetActive(true)
			self.talent_skill_list[i] = WuXingTalentSkillCell.New(obj)
			self.talent_skill_list[i]:SetIndex(i)
			self.talent_skill_list[i]:SetClickCallBack(BindTool.Bind(self.OnSelectSkillCell, self))
		end
	end

	self.talent_skill_line_list = {}
	local node_num = self.node_list.line_content.transform.childCount
	for i = 0, node_num - 1 do
		self.talent_skill_line_list[i] = self.node_list.line_content:FindObj("line_" .. i)
	end

	if self.talent_attr_list == nil then
        self.talent_attr_list = {}
        local node_num = self.node_list["talent_attr_list"].transform.childCount
        for i = 1, node_num do
            self.talent_attr_list[i] = CommonAddAttrRender.New(self.node_list["talent_attr_list"]:FindObj("attr_" .. i))
			self.talent_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    self.talent_comsume_item = ItemCell.New(self.node_list["talent_comsume_pos"])
    XUI.AddClickEventListener(self.node_list["talent_active_btn"], BindTool.Bind(self.OnClickActiveTalent, self))
end

function FiveElementsView:ShowFiveElementTalentView()
	self.node_list["title_view_name"].text.text = Language.FiveElements.TabName[3]
	self:ChangeFiveElementsViewBg("a2_wx_bg9")
	self:PlayTalentAnim()
end

function FiveElementsView:TalentViewReleaseCallBack()
	if not IsEmptyTable(self.talent_skill_list) then
		for k,v in pairs(self.talent_skill_list) do
			v:DeleteMe()
		end
		self.talent_skill_list = {}
	end

	if self.talent_attr_list then
	    for k, v in pairs(self.talent_attr_list) do
	        v:DeleteMe()
	    end
	    self.talent_attr_list = nil
    end

    if self.talent_comsume_item then
        self.talent_comsume_item:DeleteMe()
        self.talent_comsume_item = nil
    end

    self.cell_select_data = nil
    self.cell_select_index = nil
	self.talent_skill_line_list = {}
end

function FiveElementsView:OnFlushTalent(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.effect then
				TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["jihuo_effct"]})
			end
			
			self:FlushTalentAllView()
			--self:ChangeFiveElementsViewBg("a1_wx_bg0")
		end
	end
end

function FiveElementsView:FlushTalentAllView()
	local talent_data = FiveElementsWGData.Instance:GetAllTalentCfg()

	local is_active = false
	for k, v in pairs(self.talent_skill_list) do
		local data = talent_data[k]
		v:SetData(data)

		is_active = false
		if data then
			is_active = FiveElementsWGData.Instance:GetTalentFlagBySeq(data.seq)
		end

		self.talent_skill_line_list[k]:CustomSetActive(is_active)
	end
	
	local click_cell_index = 0
	if not self.cell_select_index then
		for i = 0, #self.talent_skill_list do
			local is_remind = FiveElementsWGData.Instance:GetSingleTalentRemind(talent_data[i].seq)
			if is_remind then
				click_cell_index = i
				break
			end
		end
	else
		click_cell_index = self.cell_select_index
	end

	self:OnSelectSkillCell(self.talent_skill_list[click_cell_index])
end


function FiveElementsView:PlayTalentAnim()
	local tween_info = UITween_CONSTS.FiveElements
	RectTransform.SetAnchoredPositionXY(self.node_list.talent_right_panel.rect, 600, 0)
	local right_tween = self.node_list.talent_right_panel.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
end

function FiveElementsView:OnSelectSkillCell(cell)
	local cell_data = cell and cell.data
	if not cell_data or cell:GetIndex() == self.cell_index then
        return
    end
	
	self.cell_select_data = cell_data
	self.cell_select_index = cell:GetIndex()

	for k, v in pairs(self.talent_skill_list) do
		v:SetSelectIndex(self.cell_select_index)
	end

	self:FlushTalentRightPanel()
end


function FiveElementsView:FlushTalentRightPanel()
	if not self.cell_select_data then
		return
	end

	self.node_list.talent_name.text.text = self.cell_select_data.skill_name
	self.node_list["talent_cur_skill_img"].image:LoadSprite(ResPath.GetSkillIconById(self.cell_select_data.skill_icon))
	local pre_skill_cfg = FiveElementsWGData.Instance:GetTalentCfgBySeq(self.cell_select_data.per_seq)
	local str = self.cell_select_data.per_seq and self.cell_select_data.per_seq >= 0 and pre_skill_cfg.skill_name or Language.FiveElements.PreConditionWu
	self.node_list.per_skill_desc.text.text = string.format(Language.FiveElements.PreConditionDesc, str)

	self.talent_comsume_item:SetData({item_id = self.cell_select_data.cost_item_id})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.cell_select_data.cost_item_id)
    local has_num = ItemWGData.Instance:GetItemNumInBagById(self.cell_select_data.cost_item_id)
	local color = has_num < self.cell_select_data.cost_item_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
	local str = has_num .. "/" .. self.cell_select_data.cost_item_num
	self.talent_comsume_item:SetRightBottomColorText(str, color)
	self.talent_comsume_item:SetRightBottomTextVisible(true)

	local is_active = FiveElementsWGData.Instance:GetTalentFlagBySeq(self.cell_select_data.seq)
	self.node_list.talent_active_btn:SetActive(not is_active)
	self.node_list.talent_is_active:SetActive(is_active)
	local is_remind = FiveElementsWGData.Instance:GetSingleTalentRemind(self.cell_select_data.seq)
	self.node_list.talent_active_remind:SetActive(is_remind)

	local attr_list = FiveElementsWGData.Instance:GetCurTalentAttrList(self.cell_select_data.seq) -- 属性
	local need_show_effect = false

	if nil ~= self.cell_select_seq_cache and nil ~= self.cell_select_seq_active_cache then
		if (self.cell_select_seq_cache == self.cell_select_data.seq) and (is_active == true) and (self.cell_select_seq_active_cache == false) then
			need_show_effect = true
		end
 	end

	self.cell_select_seq_cache = self.cell_select_data.seq
	self.cell_select_seq_active_cache = is_active

	for k, v in pairs(self.talent_attr_list) do
        v:SetData(attr_list[k])

		if need_show_effect then
			v:PlayAttrValueUpEffect()
		end
    end
end

function FiveElementsView:OnClickActiveTalent()
	if not self.cell_select_data then
		return
	end

	local pre_is_active = true
	if self.cell_select_data.per_seq >= 0 then
		pre_is_active = FiveElementsWGData.Instance:GetTalentFlagBySeq(self.cell_select_data.per_seq)
	end

	if not pre_is_active then
		TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElements.NotPreActive)
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.cell_select_data.cost_item_id)
    local has_num = ItemWGData.Instance:GetItemNumInBagById(self.cell_select_data.cost_item_id)
    if has_num < self.cell_select_data.cost_item_num then
    	TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElements.NotEnoughConsume)
    	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.cell_select_data.cost_item_id})
    	return
    end

	FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_ACTIVE_TALENT, self.cell_select_data.seq)
end

-------------------五行天赋技能格子--------------------
WuXingTalentSkillCell = WuXingTalentSkillCell or BaseClass(BaseRender)
function WuXingTalentSkillCell:__init()

end

function WuXingTalentSkillCell:__delete()

end

function WuXingTalentSkillCell:OnFlush()
	if not self.data then
		return
	end

	local is_active = FiveElementsWGData.Instance:GetTalentFlagBySeq(self.data.seq)
	self.node_list["skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
	--self.node_list.effect:SetActive(is_active)
	XUI.SetGraphicGrey(self.node_list.skill_icon, not is_active)

    local is_remind = FiveElementsWGData.Instance:GetSingleTalentRemind(self.data.seq)
    self.node_list.remind:SetActive(is_remind)
end

function WuXingTalentSkillCell:OnSelectChange(is_select)
	self.node_list.select_bg:SetActive(is_select)
end