GuildBattleRankedNewSceneLogic = GuildBattleRankedNewSceneLogic or BaseClass(CommonFbLogic)
local rotation_x_blue = 26.9
local rotation_y_blue = -275.9

local rotation_x_red = 31.8
local rotation_y_red = -77.8

function GuildBattleRankedNewSceneLogic:__init()
	self.change_appearance = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.ChangeAppearanceFunc, self))
	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
end

function GuildBattleRankedNewSceneLogic:__delete()
	self.set_mark_1 = nil
	self.set_mark_2 = nil

	if self.change_appearance then
		GlobalEventSystem:UnBind(self.change_appearance)
		self.change_appearance = nil
	end
	if self.create_obj_event then
		 GlobalEventSystem:UnBind(self.create_obj_event)
		 self.create_obj_event = nil
	end
end

function GuildBattleRankedNewSceneLogic:Enter(old_scene_type, new_scene_type)
	self.set_mark_1 = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SHIELD_OTHERS)
	self.set_mark_2 = SettingWGData.Instance:GetSettingData(SETTING_TYPE.SHIELD_SAME_CAMP)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_OTHERS, false, false)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_SAME_CAMP, false, false)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self:ChangeKFZhuXieTitle()

	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))

    self.guild_xmz_fight_state_event = GlobalEventSystem:Bind(Guill_XMZ_FIGHT_STATE.STATE_CHANGE, BindTool.Bind1(self.XMZFightStateChange, self))
    self.guild_xmz_lingshi_state_event = GlobalEventSystem:Bind(Guill_XMZ_FIGHT_STATE.LINGSHI_CHANGE, BindTool.Bind1(self.UpdateGatherMapBlock, self))

    -- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
	if old_scene_type ~= new_scene_type then
		GuajiWGCtrl.Instance:StopGuaji()
		GuildBattleRankedWGCtrl.Instance:OpenGuildBattleRankedView()
		GuildBattleRankedWGCtrl.Instance:OpenGuildBattleTopView()
		local angle_x, angle_y
		local guilddata = GuildBattleRankedWGCtrl.Instance.data
		local guild_info_list = guilddata:GetGuildBattleSceneGlobalInfo()
		local owner_guild_name = GameVoManager.Instance:GetMainRoleVo().guild_name
		if not IsEmptyTable(guild_info_list) then
			if guild_info_list[0].guild_name == owner_guild_name then
				angle_x = rotation_x_blue
				angle_y = rotation_y_blue
			elseif guild_info_list[1].guild_name == owner_guild_name then
				angle_x = rotation_x_red
				angle_y = rotation_y_red
			end
		end

		if angle_x ~= nil and angle_y ~= nil then
			local scene_id = Scene.Instance:GetSceneId()
			local param_t = {scene_id = scene_id}
			Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, angle_x, angle_y, 12, param_t)
		end
	end

	local main_role = Scene.Instance:GetMainRole()
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil, -80)

	local common_fuhuo = function()
		self:OnFlyDownEnd()
	end

	local here_fuhuo = function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end

	FuhuoWGCtrl.Instance:SetFuhuoCallback(common_fuhuo, here_fuhuo)

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if is_open then
		local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
		local side = role_info_list and role_info_list.side or 0
		local pos_x,pos_y =  GuildBattleRankedWGData.Instance:GetGuildGuaJiPos(side)
		local scene_id = Scene.Instance:GetSceneId()
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 3)
	end

	MainuiWGCtrl.Instance:FlushView(0,"recharge_vip")

	GuildWGData.Instance:SetHideGuildZhengBaRemind(true)
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)

	GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE)

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	self.gather_map_block_list = {}
	self:UpdateGatherMapBlock()
	GuildBattleRankedWGData.Instance:SetFirstEnterScene(true)

	--副屏处理
	ViewManager.Instance:AddMainUIFuPingChangeList(GuildBattleRankedWGCtrl.Instance:GetGuildBattleRankedView())
end

function GuildBattleRankedNewSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end
end

function GuildBattleRankedNewSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		GuildBattleRankedWGCtrl.Instance:CloseGuildBattleRankedView()
		GuildBattleRankedWGCtrl.Instance:CloseGuildBattleTopView()
	end
	GlobalEventSystem:UnBind(self.fly_down_end_event)
	GlobalEventSystem:UnBind(self.guild_xmz_fight_state_event)
	GlobalEventSystem:UnBind(self.guild_xmz_lingshi_state_event)
	self.fly_down_end_event = nil

	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_OTHERS, self.set_mark_1, false)
	SettingWGData.Instance:SetSettingData1(SETTING_TYPE.SHIELD_SAME_CAMP, self.set_mark_2, false)
	GuildBattleRankedWGCtrl.Instance:SetIsEnd(nil)

	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil,0)

	self:ResumeSelfInfo()
	GuildBattleRankedWGCtrl.Instance:SendGuildBattleBuyMachineReq(-1)
	MainuiWGCtrl.Instance:FlushView(0,"recharge_vip")

	if CountDownManager.Instance:HasCountDown("xmz_map_block_countdown") then
		CountDownManager.Instance:RemoveCountDown("xmz_map_block_countdown")
	end

	ViewManager.Instance:RemoveMainUIFuPingChangeList(GuildBattleRankedWGCtrl.Instance:GetGuildBattleRankedView())
end

function GuildBattleRankedNewSceneLogic:GetGuajiPos()
	local pos_x, pos_y
	local main_role = Scene.Instance:GetMainRole()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local not_xiusai = not GuildWGData.Instance:GetIsPrepareRestTime()
	local can_guaji = is_open and not_xiusai
	if not can_guaji then
		return
	end

	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local side = role_info_list and role_info_list.side or 0
	pos_x, pos_y =  GuildBattleRankedWGData.Instance:GetGuildGuaJiPos(side)
	MoveCache.SetEndType(MoveEndType.Auto)
	
	return pos_x, pos_y
end

function GuildBattleRankedNewSceneLogic:GetGuajiCharacter()
	local target_obj = nil
	local dis = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local is_need_stop = false

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local not_xiusai = not GuildWGData.Instance:GetIsPrepareRestTime() --判断仙盟战是否处于休赛期
	local can_guaji = is_open and not_xiusai

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or not can_guaji then
		return target_obj, dis, is_need_stop
	end

	local x, y = main_role:GetLogicPos()
	local target_obj, dis = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, dis, SelectType.Enemy)
	if target_obj then
		return target_obj, dis, is_need_stop
	end

	local target_obj, dis = Scene.Instance:SelectObjHelper(SceneObjType.Role, x, y, dis, SelectType.Enemy)
	return target_obj, dis, is_need_stop
end

function GuildBattleRankedNewSceneLogic:IsEnemy(target_obj, main_role, ignore_table, test)
	local target_vo = target_obj and target_obj:GetVo() or nil
	if target_obj and target_obj:IsMonster() then
		return not GuildBattleRankedWGData.Instance:IsTheSameBatterArrayMonster(target_obj:GetMonsterId())
	end

	local target_guide_id = target_vo and target_vo.guild_id or 0
	if RoleWGData.Instance:GetAttr("guild_id") == target_guide_id then
		return false
	else
		return true
	end
end

-- 获取角色仙盟名
function GuildBattleRankedNewSceneLogic:GetGuildNameBoardText(role_vo)
	local t = {}
	local index = 1
	local guild_name = role_vo.guild_name or ""
	local guild_post = role_vo.guild_post

	if "" == guild_name then return t end
	guild_name = "【" .. guild_name .. "】"
	local authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[guild_post]
	local post_name = authority and authority.post or ""
	local guild_id = RoleWGData.Instance.role_vo.guild_id

	t[index] = {}
	t[index].color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t[index].text = guild_name .. COMMON_CONSTS.POINT
	index = index + 1

	t[index] = {}
	t[index].color = role_vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	t[index].text = post_name

	return t
end

-- 获取角色名字颜色
function GuildBattleRankedNewSceneLogic:GetColorName(role_vo)
	local color_name = ""
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local color = role_vo.vo.guild_id == guild_id and COLOR3B.WHITE or COLOR3B.RED
	color_name = role_vo.vo.name
	return color_name, color
end

function GuildBattleRankedNewSceneLogic:OnObjCreate(obj)
	if not obj then return end
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if not obj:IsMainRole() and obj:IsRole() and obj.vo.guild_id and obj.vo.guild_id == guild_id then
		local follow_ui = obj:GetFollowUi()
		obj:SetHpVisiable(false)
		follow_ui:GetHpBar():AddShieldRule(ShieldRuleWeight.Max, function()
			return true
		end)
	end
end

function GuildBattleRankedNewSceneLogic:ChangeKFZhuXieTitle()
	local mark,info = GuildBattleRankedWGCtrl.Instance:GetMarkTitle()
	if mark then return end
	local main_role = Scene.Instance:GetMainRole()
	local vo = main_role and main_role.vo
	if vo then
		self:AddKFZhuXieDelay(vo.used_title_list)
		main_role:SetAttr("used_title_list",{})
	end
	GuildBattleRankedWGCtrl.Instance:SetMarkTitle(true,vo.used_title_list)
end

function GuildBattleRankedNewSceneLogic:AddKFZhuXieDelay(title)
	self.my_title = title and __TableCopy(title) or self.my_title
end

function GuildBattleRankedNewSceneLogic:ResumeSelfInfo()
	local mark,info = GuildBattleRankedWGCtrl.Instance:GetMarkTitle()
	GuildBattleRankedWGCtrl.Instance:SetMarkTitle(false,nil)
	if info then
		RoleWGData.Instance:SetAttr("used_title_list", info)
	end
end
function GuildBattleRankedNewSceneLogic:ChangeAppearanceFunc(obj_id)
	self:ChangeNoneFollowUi(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	role:SetAttr("yzwc_ico", 2 - role.vo.special_param )

end
function GuildBattleRankedNewSceneLogic:ChangeNoneFollowUi(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	if not role then
		return
	end
	role:SetAttr("yzwc_ico", 0)
end

-- 此场景优先保证单位数量
function GuildBattleRankedNewSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function GuildBattleRankedNewSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

----------------------空气墙-------------------------------------------
function GuildBattleRankedNewSceneLogic:CheckSceneMapBlock(scene_id)
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local not_xiusai = not GuildWGData.Instance:GetIsPrepareRestTime()
	if is_open and not_xiusai then
		self:RemoveSceneBlock()
		return
	end
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local next_time = act_info and act_info.next_time or 0
	local count_down_time = 0
	if act_info.status == ACTIVITY_STATUS.STANDY then
		count_down_time = next_time - server_time
	elseif GuildWGData.Instance:GetIsPrepareRestTime() then
		local start_hour,start_min,during_time,start_hour2,start_min2,all_second,round_time_1,round_time_2 = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
		local start_time = act_info.start_time
		local round_rest_timer = act_info.start_time + round_time_1 + during_time
		count_down_time = round_rest_timer - server_time
	end

	
	if count_down_time > 0 then
		if CountDownManager.Instance:HasCountDown("xmz_map_block_countdown") then
			CountDownManager.Instance:RemoveCountDown("xmz_map_block_countdown")
		end
		CountDownManager.Instance:AddCountDown("xmz_map_block_countdown", BindTool.Bind(self.UpdateMapBlockTimer,self), BindTool.Bind(self.MapBlockTimerCallBack,self), nil, count_down_time, 1)
	else

		self:RemoveSceneBlock()
	end

	self.act_id = act_scene.act_id

	self.is_map_block = true
	self:SetSceneBlock()

end

function GuildBattleRankedNewSceneLogic:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_XIANMENGZHAN and status == ACTIVITY_STATUS.OPEN then
		if CountDownManager.Instance:HasCountDown("xmz_map_block_countdown") then
			CountDownManager.Instance:RemoveCountDown("xmz_map_block_countdown")
		end
		self:RemoveSceneBlock()
		
		local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
		local side = role_info_list.side
		local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
		local guild_npc_obj = Scene.Instance:GetNpcByNpcId(npc_id) 
		if guild_npc_obj and guild_npc_obj.SetXMZNpcTaskIcon then
			guild_npc_obj:SetXMZNpcTaskIcon()
		end
	end
end

function GuildBattleRankedNewSceneLogic:XMZFightStateChange()
	local cur_state = GuildWGData.Instance:GetCurGuildWarState()
	local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
	-- if not xiusai_state then
	if cur_state >= GuildWGData.GuildWarState.TwoState then
		if CountDownManager.Instance:HasCountDown("xmz_map_block_countdown") then
			CountDownManager.Instance:RemoveCountDown("xmz_map_block_countdown")
		end
		self:RemoveSceneBlock()
		local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
		local side = role_info_list.side
		local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
		local guild_npc_obj = Scene.Instance:GetNpcByNpcId(npc_id) 
		if guild_npc_obj and guild_npc_obj.SetXMZNpcTaskIcon then
			guild_npc_obj:SetXMZNpcTaskIcon()
		end
	end
end

function GuildBattleRankedNewSceneLogic:UpdateMapBlockTimer(now_time, elapse_time)
	
end

function GuildBattleRankedNewSceneLogic:MapBlockTimerCallBack(now_time, elapse_time)
	self:RemoveSceneBlock()
end

function GuildBattleRankedNewSceneLogic:ShowFindLately(gather_obj_id)
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role.vo ~= nil then
		-- 攻城车
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
		local xiusai_state = not GuildWGData.Instance:GetIsPrepareRestTime()
		local act_open = act_info and act_info.status == ACTIVITY_STATUS.OPEN
		if main_role.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ and (act_open or xiusai_state) then
			return true
		end
	end
	return false
end

-- 把采集物所在位置设置为障碍区
function GuildBattleRankedNewSceneLogic:UpdateGatherMapBlock()
	self:ClearGatherMapBlock()
	local lingshi_list = GuildBattleRankedWGData.Instance:GetLingShiList()
	for k,v in pairs(lingshi_list) do
		local block_list = self:GetOneGatherMapBlock(v)
		if not IsEmptyTable(block_list) then
			for i,pos in pairs(block_list) do
				table.insert(self.gather_map_block_list,{x = pos.x,y = pos.y})
				AStarFindWay:SetBlockInfo(pos.x, pos.y)
				-- self:SetMapBlockEffect("diaoyu", pos.x, pos.y,dd 0, 0, 1, 1)
			end
		end
	end
end

function GuildBattleRankedNewSceneLogic:GetOneGatherMapBlock(data)
	local num = 3
	local h = 5
	local d = 4
	local h_lerp_num = h - num
	local d_lerp_num = d - num
	local block_list = {}
	local main_role_x, main_role_y = Scene.Instance:GetMainRole():GetLogicPos()
	for x=-h_lerp_num,h_lerp_num do
		for y=-d_lerp_num,d_lerp_num do
			local pos_x = data.pos.x-x
			local pos_y = data.pos.y-y
			if main_role_x == pos_x and main_role_y == pos_y then
				block_list = {}
				return block_list
			end
			table.insert(block_list,{x = pos_x,y = pos_y})
		end
	end
	return block_list
end

function GuildBattleRankedNewSceneLogic:ClearGatherMapBlock()
	if not IsEmptyTable(self.gather_map_block_list) then
		for k,v in pairs(self.gather_map_block_list) do
			AStarFindWay:RevertBlockInfo(v.x, v.y)
		end
	end
end

function GuildBattleRankedNewSceneLogic:GetCheckLatelyGatherDis()
	return 25
end