require("game/fengshenbang/fengshenbang_wg_data")
require("game/fengshenbang/fengshenbang_view")
require("game/fengshenbang/fengshenbang_title_view")
require("game/fengshenbang/fengshenbang_challenge_view")
require("game/fengshenbang/fengshenbang_fb_view")
require("game/fengshenbang/fengshenbang_win_view")

FengShenBangWGCtrl = FengShenBangWGCtrl or BaseClass(BaseWGCtrl)

CS_OGA_GODS_RANK_TYPE = {
	CS_OGA_GODS_RANK_TYPE_ACTIVITY_INFO = 0,	-- 活动信息
	CS_OGA_GODS_RANK_TYPE_PERSON_INFO = 1,		-- 玩家个人信息
	CS_OGA_GODS_RANK_TYPE_FRESH_CD = 2,			-- 刷新挑战CD
	CS_OGA_GODS_RANK_TYPE_CHALLENGE = 3			-- 挑战 scene_id
}

MAX_WORLD_OGA_GODSRANK_RANK_COUNT = 5

function FengShenBangWGCtrl:__init()
	if FengShenBangWGCtrl.Instance then
		error("[FengShenBangWGCtrl]:Attempt to create singleton twice!")
		return
	end
	FengShenBangWGCtrl.Instance = self
	self.request_role_uid_list = {}
	self.is_first_open_tip = true
	self.save_delay_count = 0

	self.data = FengShenBangWGData.New()
	self.view = FengShenBangView.New(GuideModuleName.FengShenBang)
	self.title_view = FengShenBangTitleView.New()
	self.challenge_view = FengShenBangChallengeView.New()
	self.fb_view = FengShenBangFBView.New()
	self.win_view = FengShenBangWinView.New()
	self:RegisterAllProtocals()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
	-- self:BindGlobalEvent(MainUIEventType.SHOW_MAIN_CHAT, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --显示主界面
	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChangeOrOpen, self))
end

function FengShenBangWGCtrl:__delete()
	self.view:DeleteMe()
	self.view = nil
	self.data:DeleteMe()
	self.data = nil
	self.title_view:DeleteMe()
	self.title_view = nil
	self.challenge_view:DeleteMe()
	self.challenge_view = nil
	FengShenBangWGCtrl.Instance = nil
	CountDownManager.Instance:RemoveCountDown("fengshenbang_act_yugao")
end

function FengShenBangWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSOGAGodsRankOpera)
	self:RegisterProtocol(SCOGAGodsRankActivityInfo, "ReceiveSCOGAGodsRankActivityInfo")
	self:RegisterProtocol(SCOGAGodsRankPersonInfo, "ReceiveSCOGAGodsRankPersonInfo")
	self:RegisterProtocol(SCOGAGodsRankRankBeChange, "ReceiveSCOGAGodsRankRankBeChange")
	self:RegisterProtocol(SCOGAGodsRankSpecialSceneInfo, "ReceiveSCOGAGodsRankSpecialSceneInfo")
	self:RegisterProtocol(SCOGAGodsRankCloseShow, "ReceiveSCOGAGodsRankCloseShow")
end

function FengShenBangWGCtrl:MainuiOpenCreateCallBack()
	self:CheckActIsOpen()
end

function FengShenBangWGCtrl:DayChangeOrOpen()
	self:CheckActIsOpen()
	self.view:Flush(0, "pass_day")
	RemindManager.Instance:Fire(RemindName.FengShenBang)
end

function FengShenBangWGCtrl:CheckActIsOpen()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.FengShenBang)
	local other_info = FengShenBangWGData.Instance:GetActCfgList("other")
	if not other_info or not act_cfg then -- MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg)
		return
	end
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		return
	end
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local start_time, clear_time, close_time, yugao_time = FengShenBangWGData.Instance:GetActTime()
	if now_time < start_time then -- 预告
		if now_time < yugao_time then
			CountDownManager.Instance:RemoveCountDown("fengshenbang_act_yugao")
            CountDownManager.Instance:AddCountDown("fengshenbang_act_yugao", nil, function ()
                MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FENGSHENBANG_REMIND, 0)
				MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.FengShenBang, start_time, act_cfg, COLOR3B.GREEN)
				end, yugao_time)
		else
			MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.FengShenBang, start_time, act_cfg, COLOR3B.GREEN)
		end
	else
		if now_time < clear_time and self.is_first_open_tip then
			ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.FengShenBang)
			self.is_first_open_tip = false
		end
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if open_day <= act_cfg.act_serverover_day then
			local delay_time = FengShenBangWGData.Instance:GetActDelayTime()
			MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.FengShenBang, clear_time + delay_time, act_cfg)
        else
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FENGSHENBANG_REMIND, 0)
			MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.FengShenBang, false)
		end
	end
end

---[[ 协议相关
-- 8260 请求协议
function FengShenBangWGCtrl:RequestFengShenBang(req_type, param0, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOGAGodsRankOpera)
	protocol.opera_type = req_type or 0
	protocol.param0 = param0 or 0
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

-- 8261 五个仙位排行数据
function FengShenBangWGCtrl:ReceiveSCOGAGodsRankActivityInfo(protocol)
	self.data:SetActRankInfo(protocol)
	self.view:Flush(0, "ser_data", {protocol_id = 8261})
	if self.view:IsOpen() then
		self:RefreshRankRoleInfo()
	end
	if self.save_delay_count ~= protocol.delay_count then
		self:CheckActIsOpen()
		self.view:Flush(0, "delay_count", {protocol_id = 8261})
	end
end

-- 8262 玩家个人数据
function FengShenBangWGCtrl:ReceiveSCOGAGodsRankPersonInfo(protocol)
	self.data:SetRolePersonInfo(protocol)
	self.view:Flush(0, "person_info", {protocol_id = 8262})
end

-- 8263 玩家仙位被抢
function FengShenBangWGCtrl:ReceiveSCOGAGodsRankRankBeChange(protocol)
    self.data:SetXianWeiRobInfo(protocol)
    
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FENGSHENBANG_REMIND, 1,function()
        ViewManager.Instance:Open(GuideModuleName.FengShenBang)
        self:CheckXianWeiRob()
    end)
    RemindManager.Instance:Fire(RemindName.FengShenBang)
end

-- 8264 进入副本后数据
function FengShenBangWGCtrl:ReceiveSCOGAGodsRankSpecialSceneInfo(protocol)
	self.data:SetSceneInfo(protocol)
	local is_get_join_reward = protocol.reward_coin_flag == 1
	if protocol.finish == 1 then
		if protocol.pass == 1 then
			local scene_id = FengShenBangWGData.Instance:GetFSBSceneId()
			local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataBySceneID(scene_id)
			if scene_cfg then
				self.win_view:Open()
				self.win_view:Flush(0, "title_info", {title_id = scene_cfg.title_id})
				self.win_view:Flush(0, "kill_info", {boss_id = scene_cfg.boss_id})
			end
		-- else
		-- 	FuBenWGCtrl.Instance:OpenLose(SceneType.FengShenBang)
		end
	else
		self.challenge_view:Close()
		self.view:Close()
	end
	self.fb_view:Flush(0, "scene_info", {protocol_id = 8264})
end

-- 8265 活动结束后展示数据
function FengShenBangWGCtrl:ReceiveSCOGAGodsRankCloseShow(protocol)
	self.data:SetActRankInfoByClose(protocol)
	self.view:Flush(0, "ser_data", {protocol_id = 8265})
	self:RefreshRankRoleInfo()
	if FengShenBangWGData.Instance:GetActIsOpen() then
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.FengShenBang)
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.FengShenBang, nil, act_cfg)
	end
end
--]]

function FengShenBangWGCtrl:OpenTitleView()
	self.title_view:Open()
end

function FengShenBangWGCtrl:OpenChallengeView()
	self.challenge_view:Open()
end

function FengShenBangWGCtrl:OpenFbView()
	self.fb_view:Open()
end

function FengShenBangWGCtrl:CloseFbView()
	self.fb_view:Close()
end

-- 刷新五位排行玩家个人数据
function FengShenBangWGCtrl:RefreshRankRoleInfo()
	local role_uid_list = self.data:GetRankRoleUidList()
	if not role_uid_list then
		return
	end
	local request_role_uid_list = {}
	for i=1,#role_uid_list do
		if role_uid_list[i] > 0 then
			request_role_uid_list[#request_role_uid_list + 1] = role_uid_list[i]
		end
	end

	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	for i=1,#request_role_uid_list do
		if request_role_uid_list[i] == main_vo.role_id then
			table.remove(request_role_uid_list, i)
			FengShenBangWGData.Instance:SetRankRoleInfo(main_vo.role_id, main_vo)
			break
		end
	end

	self.request_role_uid_list = request_role_uid_list
	self:RequestRankRoleInfo()
end

function FengShenBangWGCtrl:RequestRankRoleInfo()
	if #self.request_role_uid_list <= 0 then
		self.view:Flush(0, "role_info", {protocol_id = 1412})
		return
	end
	local role_uid = table.remove(self.request_role_uid_list, 1)
	BrowseWGCtrl.Instance:SendQueryRoleInfoReq(role_uid, BindTool.Bind(self.ReceiveRankRoleInfo, self))
end

function FengShenBangWGCtrl:ReceiveRankRoleInfo(protocol)
	FengShenBangWGData.Instance:SetRankRoleInfo(protocol.role_id, protocol)
	self:RequestRankRoleInfo()
end

function FengShenBangWGCtrl:CheckXianWeiRob()
	local xianwei_rob_info = FengShenBangWGData.Instance:GetXianWeiRobInfo()
	if not xianwei_rob_info or not xianwei_rob_info.is_remind then
		return
	end
    local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgData(xianwei_rob_info.rank_index + 1)
    local title_info = FengShenBangWGData.Instance:GetTitleList(xianwei_rob_info.rank_index + 1)
    local title_cfg = TitleWGData.Instance:GetConfig(title_info.title_id)
    TipWGCtrl.Instance:OpenAlertTips(string.format(Language.FengShenBang.XianWeiRobTips, title_cfg and title_cfg.name or "", xianwei_rob_info.role_name), function ()
            self:RequestFengShenBang(CS_OGA_GODS_RANK_TYPE.CS_OGA_GODS_RANK_TYPE_CHALLENGE, scene_cfg.scene_id)
        end)
    xianwei_rob_info.is_remind = false
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FENGSHENBANG_REMIND, 0)
end

function FengShenBangWGCtrl:CheckFail()
	local scene_info = FengShenBangWGData.Instance:GetSceneInfo()
	if scene_info and scene_info.finish and not scene_info.pass then
		FuBenWGData.Instance:SetFuBenInitiativeToLeave(SceneType.FengShenBang, true)
		FuBenWGCtrl.Instance:OpenLose(SceneType.FengShenBang)
	end
end