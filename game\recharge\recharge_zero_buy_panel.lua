--------------------------------------------------------------------
-- VIP零元购
--------------------------------------------------------------------

function RechargeView:InitZeroBuyPanel()
	self.zero_toggle_index = 1
	self.zero_buy_max_page = 3
	self.shoudong_click = false
	self.zero_buy_cap_list = {}
	self.cache_drag_pos_x = 0
	self.clear_drag_pos_timer = nil
	self.zero_buy_left_root_list = {"zerobuy_model_root", "zerobuy_tujian_item", "zerobuy_boss_root"}
	self.zero_buy_save_pos_x = {}
	if not self.cell_list then
		self.cell_list = {}
	end

	self.node_list.zerobuy_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyVipZeroBuy, self))
	--self.zero_buy_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.ZeroBuyLoopShow, self), 5)

	self:InitZeroBuyLeftPanel()
	self:InitZeroBuyRightPanel()
	self:InitZeroBuyPagePoint()
	-- self:FlushZeroBuyLeftPanel()
end

function RechargeView:DeleteZeroBuyPanel()
	if self.zero_buy_zuoqi_model then
		self.zero_buy_zuoqi_model:DeleteMe()
		self.zero_buy_zuoqi_model = nil
	end
	if self.zero_buy_boss_model then
		self.zero_buy_boss_model:DeleteMe()
		self.zero_buy_boss_model = nil
	end
	if self.zero_buy_timer_quest then
		GlobalTimerQuest:CancelQuest(self.zero_buy_timer_quest)
		self.zero_buy_timer_quest = nil
	end
	if self.clear_drag_pos_timer then
		GlobalTimerQuest:CancelQuest(self.clear_drag_pos_timer)
		self.clear_drag_pos_timer = nil
	end
	if self.zerobuy_tween_yoyo then
		self.zerobuy_tween_yoyo:Kill()
		self.zerobuy_tween_yoyo = nil
	end
	if self.zerobuy_tween_page then
		self.zerobuy_tween_page:Kill()
		self.zerobuy_tween_page = nil
	end
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function RechargeView:InitZeroBuyLeftPanel()
	-- 坐骑
	local zuoqi_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["zerobuy_model_root"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}
	
	zuoqi_model:SetRenderTexUI3DModel(display_data)
	-- zuoqi_model:SetUI3DModel(self.node_list.zerobuy_model_root.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)

	local item_id = VipWGData.Instance:GetVIPZeroBuyCfg("show_model_id")
	local res_id, attr_cfg = NewAppearanceWGData.Instance:GetQiChongResIdAndActAttrCfg(item_id)
	if res_id then
		local bundle, asset = ResPath.GetMountModel(res_id)
		zuoqi_model:SetMainAsset(bundle, asset)
		--zuoqi_model:SetIsSupportClip(true)
	end

	if attr_cfg then
		local attr = AttributeMgr.GetAttributteByClass(attr_cfg)
		self.zero_buy_cap_list[1] = AttributeMgr.GetCapability(attr)
	else
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		self.zero_buy_cap_list[1] = item_cfg and item_cfg.capability_show
	end

	self.zero_buy_zuoqi_model = zuoqi_model
	self.node_list.zerobuy_cap_value.text.text = self.zero_buy_cap_list[1] or 0
	--self:InitTujianItem()
end

function RechargeView:InitTujianItem()
	local card_seq = VipWGData.Instance:GetVIPZeroBuyCfg("show_item_id") or 0
	local card_cfg = ShanHaiJingWGData.Instance:GetTJCfgBySeq(card_seq)
	local stuff_item_cfg = card_cfg and ItemWGData.Instance:GetItemConfig(card_cfg.active_item_id)
	if card_cfg and stuff_item_cfg then
		self.node_list.zerobuy_tujian_name.text.text = stuff_item_cfg.name

		local bundle, asset = ResPath.GetF2RawImagesPNG("a2_shj_tu" .. card_cfg.card_ID)
		self.node_list.zerobuy_tujian_card_icon.raw_image:LoadSprite(bundle, asset, function ()
			self.node_list.zerobuy_tujian_card_icon.raw_image:SetNativeSize()
		end)

		local attr = AttributeMgr.GetAttributteByClass(card_cfg)
		self.zero_buy_cap_list[2] = AttributeMgr.GetCapability(attr)

		bundle, asset = ResPath.GetCommonImages("a2_frame_" .. stuff_item_cfg.color)
		self.node_list.zerobuy_tujian_kuang.image:LoadSprite(bundle, asset)

		bundle, asset = ResPath.GetEffectUi(EF_TYPE[card_cfg.color])
		self.node_list.zerobuy_tujian_effect:ChangeAsset(bundle, asset)
	end
	local tween_root = self.node_list.zerobuy_tujian_item.rect
	self.zerobuy_tween_yoyo = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 40, 1.5)
	self.zerobuy_tween_yoyo:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
end

function RechargeView:InitZeroBuyRightPanel()
	local title_item_id = VipWGData.Instance:GetVipOtherInfo("vip_title")
	local title_cfg = TitleWGData.Instance:GetTitleConfigByItemId(title_item_id)
	if not title_cfg then
		return
	end
	local reward_data = VipWGData.Instance:GetVIPZeroBuyCfg("dalian_left_reward_item")
	local reward_item_list = {}
	for k,v in pairs(reward_data) do
		table.insert(reward_item_list, v)
	end
	local tab = #reward_data
	for i = 1, 4 do
		if self.cell_list[i] == nil then
			self.cell_list[i] = ItemCell.New(self.node_list["reward_pos".. i])
		end
		if reward_item_list[i] then
			self.cell_list[i]:SetData(reward_item_list[i])
			self.cell_list[i]:SetActive(true)
		else
			self.cell_list[i]:SetActive(false)
		end
	end
	local desc = VipWGData.Instance:GetVIPZeroBuyCfg("show_desc")
	self.node_list.zerobuy_reward_desc_lbl_1.text.text = desc
end

function RechargeView:InitZeroBuyPagePoint()
	local page_root = self.node_list.zerobuy_page_point_root
	local page_point_list = {}
	for i=1,3 do
		page_point_list[i] = page_root:FindObj("page_point_" .. i)
	end
	self.page_point_list = page_point_list
end

function RechargeView:ShowZeroBuyPanel()

end

function RechargeView:FlushZeroBuyPanel()
	self:ZeroBuyFlushBuyBtnStatus()
	self:ZeroBuyFlushFanliTime()
	self:ZeroBuyFlushRightBanner()
end

function RechargeView:OnClickBuyVipZeroBuy()
	local is_buy = RechargeWGData.Instance:IsBuyVipZeroBuy()
	if is_buy then
		local info_list = RechargeWGData.Instance:GetVipZeroBuyInfo()
		if info_list.reward_flag == 0 then
			VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_RECEIVE_ZERO_BUY_REWARD)
			return
		end
		if info_list.return_flag == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.YiLingQu)
			return
		else
			local need_day = VipWGData.Instance:GetVIPZeroBuyCfg("return_time") or 0
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			if open_day < need_day then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.ZeroBuyNoFanliTip)
				return
			end
		end
		VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_FETCH_RETURN_GOLD) -- 领取返利绑玉
		return
	end
	local price = RechargeWGData.Instance:GetVipZeroBuyPrice()
	local card_cfg = VipWGData.Instance:GetForeverVipCfg() -- VIP永久卡
	if not card_cfg or price <= 0 then
		return
	end
	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat3, price, card_cfg.name), function ()
				VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_BUY_VIP_ZERO_BUY)
			end)
end

-- function RechargeView:OnClickZeroBuyArrow(num)
-- 	local toggle_index = self.zero_toggle_index
-- 	local per_node_key = self.zero_buy_left_root_list[toggle_index]
-- 	toggle_index = toggle_index + num
-- 	if toggle_index > self.zero_buy_max_page then
-- 		toggle_index = 1
-- 	elseif toggle_index < 1 then
-- 		toggle_index = self.zero_buy_max_page
-- 	end
-- 	local now_node_key = self.zero_buy_left_root_list[toggle_index]
-- 	self.zero_toggle_index = toggle_index
-- 	self:ZeroBuyPlayChangeTween(per_node_key, now_node_key, num)
-- 	self.shoudong_click = true
-- end

-- function RechargeView:OnDragZeroBuyPage(data)
-- 	if self.cache_drag_pos_x == 0 then
-- 		self.cache_drag_pos_x = data.position.x
-- 	elseif not self.clear_drag_pos_timer then
-- 		local sub_value = data.position.x - self.cache_drag_pos_x
-- 		if sub_value > 25 then
-- 			self:OnClickZeroBuyArrow(-1)
-- 		elseif sub_value < -25 then
-- 			self:OnClickZeroBuyArrow(1)
-- 		else
-- 			return
-- 		end
-- 		self.clear_drag_pos_timer = GlobalTimerQuest:AddDelayTimer(function ()
-- 				self.cache_drag_pos_x = 0
-- 				self.clear_drag_pos_timer = nil
-- 			end, 0.5)
-- 	end
-- end

-- function RechargeView:ZeroBuyLoopShow()
-- 	local show_index = self:GetShowIndex()
-- 	if show_index ~= TabIndex.recharge_zerobuy then
-- 		GlobalTimerQuest:CancelQuest(self.zero_buy_timer_quest)
-- 		self.zero_buy_timer_quest = nil
-- 		return
-- 	elseif self.shoudong_click then
-- 		self.shoudong_click = false
-- 		return
-- 	end
-- 	self:OnClickZeroBuyArrow(1)
-- end

-- function RechargeView:ZeroBuyPlayChangeTween(per_node_key, now_node_key, num)
-- 	local per_root = per_node_key and self.node_list[per_node_key]
-- 	local now_root = now_node_key and self.node_list[now_node_key]

-- 	if self.zero_buy_save_pos_x[per_node_key] then
-- 		RectTransform.SetAnchoredPositionXY(per_root.rect, self.zero_buy_save_pos_x[per_node_key], per_root.rect.anchoredPosition.y)
-- 	else
-- 		self.zero_buy_save_pos_x[per_node_key] = per_root.rect.anchoredPosition.x
-- 	end
-- 	if not self.zero_buy_save_pos_x[now_node_key] then
-- 		self.zero_buy_save_pos_x[now_node_key] = now_root.rect.anchoredPosition.x
-- 	end

-- 	RectTransform.SetAnchoredPositionXY(now_root.rect, self.zero_buy_save_pos_x[now_node_key] + num * 900, now_root.rect.anchoredPosition.y)
-- 	self:ZeroBuySetEnableByIndex(self.zero_toggle_index, true)

-- 	local sequence = DG.Tweening.DOTween.Sequence()
-- 	local tween_1 = per_root.rect:DOAnchorPosX(per_root.rect.anchoredPosition.x + num * -900, 0.3)
-- 	local tween_2 = now_root.rect:DOAnchorPosX(self.zero_buy_save_pos_x[now_node_key], 0.3)
-- 	sequence:Append(tween_1)
-- 	sequence:Join(tween_2)
-- 	sequence:OnComplete(function ()
-- 		self.zerobuy_tween_page = nil
-- 		self:FlushZeroBuyLeftPanel()
-- 	end)
-- 	self.zerobuy_tween_page = sequence
-- end

-- function RechargeView:FlushZeroBuyLeftPanel()
-- 	local toggle_index = self.zero_toggle_index
-- 	for i=1,3 do
-- 		self:ZeroBuySetEnableByIndex(i, i == toggle_index)
-- 	end
-- 	self.node_list.zerobuy_capability:SetActive(toggle_index ~= 3)
-- 	self.node_list.zerobuy_boss_desc_img:SetActive(toggle_index == 3)
-- 	self.node_list.zerobuy_cap_value.text.text = self.zero_buy_cap_list[toggle_index] or 0

-- 	self:ZeroBuyFlushPagePoint()
-- 	self:ZeroBuyFlushLeftBanner()
-- end

-- function RechargeView:ZeroBuyFlushPagePoint()
-- 	local toggle_index = self.zero_toggle_index
-- 	local page_point_list = self.page_point_list
-- 	for i=1,#page_point_list do
-- 		page_point_list[i].toggle.isOn = i == toggle_index
-- 	end
-- end

-- function RechargeView:ZeroBuyFlushLeftBanner()
-- 	local bundle, asset = ResPath.GetF2RawImagesPNG("vip_lyg_banner_" .. self.zero_toggle_index)
-- 	self.node_list.zerobuy_banner_img.raw_image:LoadSprite(bundle, asset, function ()
-- 		self.node_list.zerobuy_banner_img.raw_image:SetNativeSize()
-- 	end)
-- end

function RechargeView:ZeroBuyFlushRightBanner()
	local need_day = VipWGData.Instance:GetVIPZeroBuyCfg("return_time") or 0
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	self.node_list.zerobuy_title_img:SetActive(open_day < need_day)
	self.node_list.title_open:SetActive(not (open_day < need_day))
	if open_day < need_day then
		local bundle, asset = ResPath.GetVipImage("a1_recharge_" .. need_day - open_day)
		self.node_list.zerobuy_title_day_img.image:LoadSprite(bundle, asset, function ()
			self.node_list.zerobuy_title_day_img.image:SetNativeSize()
		end)
	end
end

function RechargeView:ZeroBuyFlushBuyBtnStatus()
	local info_list = RechargeWGData.Instance:GetVipZeroBuyInfo()
	if not info_list then
		return
	end

	XUI.SetButtonEnabled(self.node_list.zerobuy_buy_btn, true)
	self.node_list.zerobuy_btn_effect:SetActive(true)

	local is_buy = RechargeWGData.Instance:IsBuyVipZeroBuy()
	local can_get = false
	local btn_str = ""
	local price = 0
	if not is_buy then
		price = RechargeWGData.Instance:GetVipZeroBuyPrice()
		btn_str = string.format(Language.Recharge.ZeroBuyBtnStr1, price)
		-- self.node_list.zerobuy_buy_btn_img.image:LoadSprite(ResPath.GetF2CommonIcon("i_xiaohao_xianyu"))
	elseif info_list.reward_flag == 0 then
		can_get = true
		btn_str = Language.Recharge.CanLingQu
	elseif info_list.return_flag == 0 then
		local need_day = VipWGData.Instance:GetVIPZeroBuyCfg("return_time") or 0
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		can_get = open_day >= need_day
		price = RechargeWGData.Instance:GetVipZeroBuyPrice()
		btn_str = string.format(Language.Recharge.ZeroBuyBtnStr2, price)
		-- self.node_list.zerobuy_buy_btn_img.image:LoadSprite(ResPath.GetF2CommonIcon("i_xiaohao_bangyu"))
		XUI.SetButtonEnabled(self.node_list.zerobuy_buy_btn, can_get)
		self.node_list.zerobuy_btn_effect:SetActive(can_get)
	elseif info_list.return_flag == 1 then
		btn_str = Language.Common.YiLingQu
	end

	self.node_list.zerobuy_red_point:SetActive(can_get)
	self.node_list.zerobuy_buy_btn_lbl.text.text = btn_str
	self.node_list.zerobuy_buy_btn_img:SetActive(price > 0)

	RectTransform.SetAnchoredPositionXY(self.node_list.zerobuy_buy_btn_lbl.rect, price > 0 and 30 or 0, 0)
	self.node_list.zerobuy_buy_btn_lbl.text.alignment = price > 0 and UnityEngine.TextAnchor.MiddleLeft or UnityEngine.TextAnchor.MiddleCenter
end

function RechargeView:ZeroBuyFlushFanliTime()
	local info_list = RechargeWGData.Instance:GetVipZeroBuyInfo()
	if info_list and info_list.buy_time > 0 and info_list.reward_flag == 1 and info_list.return_flag == 0 then
		local need_day = VipWGData.Instance:GetVIPZeroBuyCfg("return_time") or 0
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if need_day > open_day then
			self.node_list.zerobuy_count_down_label.text.text = string.format(Language.Recharge.ZeroBuyFanliLbl, need_day - open_day)
		else
			self.node_list.zerobuy_count_down_label.text.text = ToColorStr(Language.LingYuanLiBao.KeLingQu, COLOR3B.GREEN)
		end
		self.node_list.zerobuy_count_down_label:SetActive(true)
	else
		self.node_list.zerobuy_count_down_label:SetActive(false)
	end
end

-- function RechargeView:ZeroBuySetEnableByIndex(index, _bool)
-- 	if index == 1 then
-- 		self.zero_buy_zuoqi_model:SetVisible(_bool)
-- 	elseif index == 2 then
-- 		self.node_list.zerobuy_tujian_item:SetActive(_bool)
-- 	elseif index == 3 then
-- 		-- self.zero_buy_boss_model:SetVisible(_bool)
-- 		self.node_list.boss_show_effect:SetActive(_bool)
-- 	end
-- end

-- 获得永久卡后直接使用
function RechargeView:ZeroBuyUseCard(change_item_id, change_item_index, change_reason)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
		local card_cfg = VipWGData.Instance:GetForeverVipCfg()
		if change_item_id == card_cfg.item_id then
			BagWGCtrl.Instance:SendUseItem(change_item_index, 1)
		end
	end
end
