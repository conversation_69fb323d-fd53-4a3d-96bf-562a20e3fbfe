BaoXiangTuJieView = BaoXiangTuJieView or BaseClass(SafeBaseView)

function BaoXiangTuJieView:__init()
	self:SetMaskBg(true)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -3), sizeDelta = Vector2(866, 646)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_baoxiang_tujie")
end

function BaoXiangTuJieView:__delete()

end

function BaoXiangTuJieView:ReleaseCallBack()

end

function BaoXiangTuJieView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.BiZuoBaoXiang.TuJieTitle
end

function BaoXiangTuJieView:OnFlush()

end