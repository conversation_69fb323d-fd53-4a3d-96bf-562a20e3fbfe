require("game/yunbiao/yunbiao_view")
require("game/yunbiao/yunbiao_wg_data")
require("game/yunbiao/yunbiao_icon_view")
require("game/yunbiao/yubiao_jiesuan_view")
require("game/yunbiao/yunbiao_alert_view")
require("game/yunbiao/yunbiao_question_view")
-- 运镖
YunbiaoWGCtrl = YunbiaoWGCtrl or BaseClass(BaseWGCtrl)
--护送刷新飘字颜色
local HUSONG_FLUSH_COLOR = {
	[1] = "#35ab6c",
	[2] = "#c26af5",
	[3] = "#c26af5",
	[4] = "#cfa94d",
	[5] = "#ff5252",
}

function YunbiaoWGCtrl:__init()
	if YunbiaoWGCtrl.Instance ~= nil then
		ErrorLog("[YunbiaoWGCtrl] attempt to create singleton twice!")
		return
	end
	YunbiaoWGCtrl.Instance = self
	self.view = YunbiaoView.New(GuideModuleName.YunbiaoView)
	self.data = YunbiaoWGData.New()
	--self.icon_view = YunBiaoIconView.New()
	self.jiesuan_view = YuBiaoJieSuan.New(GuideModuleName.YuBiaoJieSuan)
	self.question_view = YuBiaoQuestionView.New(GuideModuleName.YuBiaoQuestion)
	self.continue_alert = nil

	self.jiu_yuan_alert = nil

	self:RegisterAllProtocols()

	self:SetIsClickHuSong(false)
	self.click_event = self:BindGlobalEvent(ObjectEventType.FLY_TO_HUSONG_NPC,BindTool.Bind(self.DelayOpenWindow,self))
end

function YunbiaoWGCtrl:__delete()
	if self.click_event then
		GlobalEventSystem:UnBind(self.click_event)
		self.click_event = nil
	end

	YunbiaoWGCtrl.Instance = nil

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.question_view then
		self.question_view:DeleteMe()
		self.question_view = nil
	end

	if nil ~= self.jiesuan_view then
		self.jiesuan_view:DeleteMe()
		self.jiesuan_view = nil
	end

	if self.flush_alert_hint then
		self.flush_alert_hint:DeleteMe()
		self.flush_alert_hint = nil
	end
	-- if self.alert then
	-- 	self.alert:DeleteMe()
	-- 	self.alert = nil
	-- end
end

function YunbiaoWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCHusongInfo, 'OnHusongInfo')
	self:RegisterProtocol(SCHusongRewardInfo,"OnHusongJieSuan")
    self:RegisterProtocol(SCHusongRefreshAck,"OnSCHusongRefreshAck")
    self:RegisterProtocol(SCAcceptHusongTaskFail,"OnSCAcceptHusongTaskFail") --护送距离过远

	self:RegisterProtocol(CSRefreshHusongTask)
	self:RegisterProtocol(CSHusongRefreshReq)
	self:RegisterProtocol(CSHusongUseProtectSkill)
	self:RegisterProtocol(CSGetHuSongTaskInfo)
	self:RegisterProtocol(CSHuSongAnswer)
	
end

function YunbiaoWGCtrl:CheckRemind(remind_id)
	if remind_id == RemindId.act_husong then
		return TaskWGData.Instance:GetTaskRemainTimes(GameEnum.TASK_TYPE_HU)
	end
	return 0
end

function YunbiaoWGCtrl:Open(tab_index, param_t)
	if not Story.Instance:GetIsStoring() then
		if param_t ~= nil then
			self.view:SetNpcId(COMMON_CONSTS.NPC_HUSONG_ID)--param_t.from_npc_id)
		end
		self.view:Open()
	end
end

function YunbiaoWGCtrl:OpenWindow()
    if YunbiaoWGData.Instance:GetIsHuShong() then
    	return
    end

	ViewManager.Instance:Open(GuideModuleName.YunbiaoView)
	if self.view:IsOpen() then
		self.view:SetNpcId(COMMON_CONSTS.NPC_HUSONG_ID)
		self.view:Flush()
	end
end

function YunbiaoWGCtrl:Close()
	self.view:Close()
end

function YunbiaoWGCtrl:GetViewIsOpen()
	return self.view:IsOpen()
end

function YunbiaoWGCtrl:GetFromNpcId()
	return self.view:GetNpcId()
end

-- 刷新护送对象
function YunbiaoWGCtrl:SendRefreshHusongTask( color,is_consume_gold)--is_autoflush, is_autobuy,
	local protocol = ProtocolPool.Instance:GetProtocol(CSRefreshHusongTask)
	-- protocol.to_color = color
	-- protocol.is_consume_gold = is_consume_gold or 0   -- 1->消耗已有道具，不足的使用绑元或者元宝    0 消耗道具
	protocol:EncodeAndSend()
end

-- 购买次数
function YunbiaoWGCtrl:SendHusongRefreshReq(is_one_key_fresh, is_auto_buy, is_clear)
	--print_error("sendhusong", is_one_key_fresh, is_auto_buy)
	local protocol = ProtocolPool.Instance:GetProtocol(CSHusongRefreshReq)
	protocol.is_one_key_fresh = is_one_key_fresh or 0
	protocol.is_auto_buy = is_auto_buy or 0
	protocol.clear_flag = is_clear or 0
	protocol:EncodeAndSend()
end

-- 护送使用保护技能
function YunbiaoWGCtrl:SendHusongUseProtectSkill()
	local protocol = ProtocolPool.Instance:GetProtocol(CSHusongUseProtectSkill)
	protocol:EncodeAndSend()
end

-- 请求护送任务信息
function YunbiaoWGCtrl:SendGetHusongTaskInfo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetHuSongTaskInfo)
	protocol:EncodeAndSend()
end

-- 运镖答题
function YunbiaoWGCtrl:SendYunbiaoAnswer(index, answer_seq)
	local protocol = ProtocolPool.Instance:GetProtocol(CSHuSongAnswer)
	protocol.index = index or 0
	protocol.answer_seq = answer_seq or 0
	protocol:EncodeAndSend()
end

-- 更新任务的次数
function YunbiaoWGCtrl:OnLingQuCiShuChangeHandler(value)
	if value then
		local old_data = self.data:GetLingQuCishu()
		if value ~= old_data then
			self.data:SetLingQuCishu(value)
			self.view:Flush()
		end
	end
end

-- 更新购买的次数
function YunbiaoWGCtrl:OnGouMaiCiShuChangeHandler(value)
	if value then
		local old_data = self.data:GetGouMaiCishu()
		if value ~= old_data then
			self.data:SetGouMaiCishu(value)
			self.view:Flush()
		end
	end
end

-- 更新免费刷新美女的次数
function YunbiaoWGCtrl:OnChangeRefreshFreeTimeHandler(value)
	if value then
		local old_data = self.data:GetRefreshFreeTime()
		if value ~= old_data then
			self.data:SetRefreshFreeTime(value)
			self.view:Flush()
		end
	end
end

function YunbiaoWGCtrl:SetIsClickHuSongOnBizuo(bo)
	self.is_click_husong = bo
end

function YunbiaoWGCtrl:GetIsClickHuSongOnBizuo()
	return self.is_click_husong or false
end

-- 刷新护送对象返回
function YunbiaoWGCtrl:OnHusongInfo(protocol)
	--print_error("11", protocol)
	local role = Scene.Instance:GetRoleByObjId(protocol.obj_id)    -- 护送对象的obj_id
	if role ~= nil and role:IsMainRole() then
		self.data:SetAleardyFreeNum(protocol.free_refresh_count)
		self.data:SetCurHusongColor(protocol.task_color)
		if self.view:IsOpen() then
			self.view:Flush()
		end
	end

	if protocol.task_id <= 0 and protocol.notfiy_reason == 0 then 
		return 
	end

	if nil ~= role then
		if protocol.task_id > 0 then
			MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.NAMECOLOR)
		end
		MainuiWGCtrl.Instance:FlushSitState()
		-- print_error(protocol.task_id,protocol.task_color)
		role:SetAttr("husong_taskid", protocol.task_id)
		role:SetAttr("husong_color", protocol.task_color)
		if role:IsMainRole() then
			self.data:SetAcceptInActivitytime(protocol.accept_in_activitytime)
			-- self.data:SetHasInsurance(protocol.has_insurance)
			self.data:SetHasProtectSkill(protocol.has_use_protect_skill)
			if self.view:IsOpen() then
				self.view:Flush()
			end
			if protocol.notfiy_reason == 0 and protocol.task_id > 0 then
				GlobalTimerQuest:AddDelayTimer(function()
					self.data:MoveToHuShongNpc()
				end, 2)
			end
			if protocol.notfiy_reason == 1 then			-- 接任务
				-- 调整摄像机
				Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.NORMAL, 30, 30)

				-- 由于上下坐骑会停止一下时间，所以延迟点时间后再自动运镖
				GlobalTimerQuest:AddDelayTimer(function()
					self.data:MoveToHuShongNpc()   -- 开始护送
				end, 1)
				self.view:Close()
			elseif protocol.notfiy_reason == 2 then		-- 任务失败
				ViewManager.Instance:Close(GuideModuleName.YuBiaoQuestion)
				self.view:Close()
			elseif protocol.notfiy_reason == 3 then		-- 任务成功
				ViewManager.Instance:Close(GuideModuleName.YuBiaoQuestion)
                local cur_zhu_task_cfg = TaskWGData.Instance:GetTaskTypeZhuCfg()
                local is_husong_zhu = cur_zhu_task_cfg ~= nil and YunbiaoWGData.Instance:IsZhuXianHuSong(cur_zhu_task_cfg.task_id)
                if is_husong_zhu then
                    TaskWGCtrl.Instance:SendTaskCommit(cur_zhu_task_cfg.task_id)
                    TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_ZHU)
                end

				self.view:Close()
			end
		end
	end
end

function YunbiaoWGCtrl:IconHandler(is_show)
	if is_show == 0 then
		self.icon_view:Close()
	elseif is_show == 1 then
		self.icon_view:Open()
	end

end

function YunbiaoWGCtrl:IconIsOpen()
	return false
end

function YunbiaoWGCtrl:QiuJiuHandler()
	local function callback()
		if self.continue_alert == nil then
			self.continue_alert = Alert.New()
		end
		self.continue_alert:SetLableString(Language.Guild.QIUYUAN2)
		self.continue_alert:SetOkFunc(BindTool.Bind1(self.SendGuildSosReq, 0))
		self.continue_alert:Open()
	end
	OperateFrequency.Operate(callback, "husong_qiujiu", 10)
end

function YunbiaoWGCtrl.SendGuildSosReq(sos_type)
	GuildWGCtrl.Instance:SendSendGuildSosReq(sos_type)
	if RoleWGData.Instance.role_vo.guild_id > 0 then
		local main_role = Scene.Instance.main_role
		if main_role then
			local x, y = main_role:GetLogicPos()
			local scene_id = Scene.Instance:GetSceneId()
			local str_format = Language.YunBiao.HelpGuildTxt
			local role_id = RoleWGData.Instance.role_vo.role_id
			local content = string.format(str_format, x, y, scene_id, role_id)
			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT,2)
		end
	end
	SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.SosToGuildSuc)
end

function YunbiaoWGCtrl:HusongUseProtectSkillHandler()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.PROTECTSKILL, 0)
	self:SendHusongUseProtectSkill()
end

-- 购买次数
function YunbiaoWGCtrl:GouMaiTimes()
	-- local yunbiao_data = YunbiaoWGData.Instance
	-- local tip_str = ""
	-- local fun = nil
	-- if yunbiao_data:GetFreeHusongNum() > 0 then
	-- 	tip_str = string.format(Language.YunBiao.GouMaiTips1, yunbiao_data:GetFreeHusongNum())
	-- else
	-- 	if not VipPower.Instance:GetHasPower(VipPowerId.husong_buy_times, true, yunbiao_data:GetGouMaiCishu(), yunbiao_data:GetGouMaiCishu()) then
	-- 		return
	-- 	end

	-- 	if yunbiao_data:GetMaxGoumaiNum() == 0 then
	-- 		tip_str = Language.YunBiao.GouMaiTips2
	-- 	else
	-- 		local goumaicishu = yunbiao_data:GetGouMaiCishu() + 1
	-- 		local gold_cost = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").buy_times_cfg[goumaicishu].gold_cost
	-- 		tip_str = string.format(Language.YunBiao.GouMaiTips3, gold_cost, goumaicishu)
	-- 		fun = BindTool.Bind1(YunbiaoWGCtrl.Instance.SendHusongBuyTimes, YunbiaoWGCtrl.Instance)
	-- 	end
	-- end
	-- if self.alert then
	-- 	self.alert:DeleteMe()
	-- 	self.alert = nil
	-- end

	-- self.alert = Alert.New(nil, nil, nil, nil, false)
	-- self.alert:SetOkFunc(fun)
	-- self.alert:SetLableString(tip_str)
	-- self.alert:Open()
end


function YunbiaoWGCtrl:SetJieSuanData(reward_list, exp, gold, is_double, insurance, task_is_free)
	if is_double == 1 and reward_list then
		self.jiesuan_view:SetJieSuanData(reward_list, math.floor(exp/2), math.floor(gold/2), is_double, insurance, task_is_free)
	else
		self.jiesuan_view:SetJieSuanData(reward_list, exp, gold, is_double, insurance, task_is_free)
	end
end

function YunbiaoWGCtrl:OnHusongJieSuan(protocol)
	local insurance = YunbiaoWGData.Instance:GetHasInsurance()
	if protocol.notify_reason == HUSONG_REWARD_INFO_NOTFIY_REASON.HUSONG_REWARD_INFO_NOTFIY_REASON_FINISH then
		self:SetJieSuanData(protocol.reward_item, protocol.reward_exp, protocol.reward_coin, protocol.is_double, insurance, protocol.task_is_free)
	end
end

function YunbiaoWGCtrl:OnSCAcceptHusongTaskFail(protocol)
    --距离npc过远
    if protocol.fail_type == YunbiaoWGData.HUSONG_TASK_FAIL_TYPE.HUSONG_TASK_FAIL_TYPE_NPC_LONGER then
        GuajiWGCtrl.Instance:StopGuaji()
		TaskGuide.Instance:CanAutoAllTask(false)
		ViewManager.Instance:CloseAll()
		local husong_cfg = YunbiaoWGData.Instance:GetYubiaoOtheraAtuo()
		if husong_cfg then
            GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
                YunbiaoWGCtrl.Instance:OpenWindow()
            end)
			GuajiWGCtrl.Instance:MoveToPos(husong_cfg.npc_scene1, husong_cfg.pos_x, husong_cfg.pos_y)
        end
    end
end

function YunbiaoWGCtrl:OnSCHusongRefreshAck(protocol)
	YunbiaoWGData.Instance:SetHusongRefreshAck(protocol)
	--飘字提示
	if protocol.refresh_total_count >= 1 then
		if protocol.old_color == protocol.refresh_color then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.FlushHint1)
		elseif protocol.old_color < protocol.refresh_color then
			local hint_num = protocol.refresh_color - protocol.old_color
			local cur_task_cfg = self.data:GetRewardCfgByLv(protocol.refresh_color)
			local name = ""
			if cur_task_cfg then
				name = cur_task_cfg.task_name
			end
			local str_color = HUSONG_FLUSH_COLOR[protocol.refresh_color] or COLOR3B.WHITE
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YunBiao.FlushHint2[hint_num],str_color,name))
		end
	end
	if self.view:IsOpen() then
		self.view:Flush()
	end
	-- self:IsOpenHuSongFlushHint(protocol)
end

function YunbiaoWGCtrl:IsOpenHuSongFlushHint(protocol)
	if not protocol or protocol.refresh_total_count == 0 or protocol.is_one_key_fresh ~= 1 then
		return
	end

	local str = self.data:GetHuSongFlushHint(protocol)
	if nil == str then
		return
	end

	if nil == self.flush_alert_hint then
		self.flush_alert_hint = Alert.New()
		self.flush_alert_hint:SetCheckBoxDefaultSelect(true)
		self.flush_alert_hint:SetUseOneSign(true)
	end

	self.flush_alert_hint:SetLableString(str)
	self.flush_alert_hint:Open()
end

function YunbiaoWGCtrl:DeleteMeAlertHint()
	if self.flush_alert_hint then
		self.flush_alert_hint:DeleteMe()
		self.flush_alert_hint = nil
	end
end

-- 护送任务接收成功
function YunbiaoWGCtrl:OnHusongAccept()
	-- 护送开始  创建美女
end

function YunbiaoWGCtrl:SetIsClickHuSong(bo)
	self.is_click_btn_husong = bo
end
function YunbiaoWGCtrl:GetIsClickHuSong()
	return self.is_click_btn_husong
end

function YunbiaoWGCtrl:DelayOpenWindow()
	local curr_sence_id = Scene.Instance:GetSceneId()
	local data = YunbiaoWGData.Instance:GetYunbiaoScene()
	if data and data.scene == curr_sence_id and self:GetIsClickHuSong() then
        -- GlobalTimerQuest:AddDelayTimer(function()
            self:OpenWindow()
        -- end,1.5)

        self:SetIsClickHuSong(false)
    end
end

function YunbiaoWGCtrl:OpenQuestionView()
	if self.question_view then
		self.question_view:Open()
	end
end
