function FightSoulWGData:InitBoneData()
    self.bone_bag_list = {}
    self.bone_item_is_wear_list = {}
    self.cahce_skill_stuff_id_byslot = {}
    self:InitBoneSlotInfo()
    self:InitSuitInfo()
end

-- 【魂骨】初始四象魂装部位信息
function FightSoulWGData:InitBoneSlotInfo()
    self.bone_slot_info = {}

    for sx_slot = 0, GameEnum.FIGHT_SOUL_SLOT_NUM - 1 do
        self.bone_slot_info[sx_slot] = {}
        for bone_part = 0, FIGHT_SOUL_BONE_TYPE.MAX - 1 do
            local bone_slot_info = {}
            bone_slot_info.fs_slot = sx_slot
            bone_slot_info.bone_part = bone_part
            bone_slot_info.slot_level = 0
            bone_slot_info.bone_data = {}
            self.bone_slot_info[sx_slot][bone_part] = bone_slot_info
        end
    end
end

-- 【魂骨】根据id 获取装备的部位， 四象类型， 套装类型
function FightSoulWGData.GetBoneParamByItemId(item_id)
    item_id = item_id or 0
    local suit_type = math.floor(item_id / 10 % 10)
    local bone_part = math.floor(item_id / 100 % 10)
    local fight_soul_type = math.floor(item_id / 1000 % 10)

    return fight_soul_type, bone_part, suit_type
end

-- 【魂骨】根据四象类型, 部位, 套装类型, 品质 获取id
function FightSoulWGData.GetBoneItemIdByParam(fs_type, bone_part, suit_type, color)
    local item_id = 0
    if fs_type == nil or bone_part == nil or suit_type == nil or color == nil then
        return item_id
    end

    item_id = 50000 + fs_type * 1000 + bone_part * 100 + suit_type * 10 + color
    return item_id
end

-- 【魂骨】更新四象魂装部位信息
function FightSoulWGData:UpdateBoneSlotInfo(protocol)
    local send_reason = protocol.send_reason
    local info = protocol.bone_slot_info
    local update_wear_data = send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.LOGIN
                            or send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.OPERA
                            or send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.PUT_ON
                            or send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.PUT_DOWN

    if update_wear_data then
        self.bone_item_is_wear_list = {}
    end

    local bag_index
    for fs_slot, part_list in pairs(self.bone_slot_info) do
        for bone_part, data in pairs(part_list) do
            data.slot_level = info[fs_slot][bone_part].slot_level
            if update_wear_data then
                bag_index = info[fs_slot][bone_part].bag_index
                -- 更新缓存
                local old_bone_data = self:GetWearBoneDataBySlotPart(fs_slot, bone_part)
                local old_bag_index = old_bone_data and old_bone_data.bag_index or -1
                if old_bag_index ~= bag_index then
                    local old_item_data = self:GetBoneDataByBagIndex(old_bag_index)
                    local new_item_data = self:GetBoneDataByBagIndex(bag_index)
                    self:CacheSingleBoneComposeStuff(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, old_item_data)
                    self:CacheSingleBoneComposeStuff(FIGHT_SOUL_BONE_BAG_SEND_REASON.REMOVE_ITEM, new_item_data)
                end

                -- 赋值
                if self.bone_slot_info[fs_slot] and self.bone_slot_info[fs_slot][bone_part] then
                    if bag_index >= 0 then
                        self.bone_item_is_wear_list[bag_index] = {fs_slot = fs_slot, bone_part = bone_part}
                        self.bone_slot_info[fs_slot][bone_part].bone_data = self:GetBoneDataByBagIndex(bag_index)
                    else
                        self.bone_slot_info[fs_slot][bone_part].bone_data = {}
                    end
                end
            end
        end
    end

    if update_wear_data then
        self:CalcAllSuitInfo()
    end
end

-- 【魂骨】通过背包位置 获取魂骨是否装备
function FightSoulWGData:GetBoneBagIndexIsWear(bag_index)
    local data = self.bone_item_is_wear_list[bag_index]
    return data ~= nil, data and data.fs_slot, data and data.bone_part
end

-- 【魂骨】通过四象槽位 获取魂装部位列表
function FightSoulWGData:GetBonePartListBySlot(slot)
    return self.bone_slot_info[slot] or {}
end

-- 【魂骨】通过四象槽位 和 部位 获取魂装部位数据
function FightSoulWGData:GetBonePartDataBySlotPart(slot, part)
    local empty_table = {}
    return ((self.bone_slot_info or empty_table)[slot] or empty_table)[part]
end

-- 【魂骨】通过四象槽位 和 部位 获取装备魂骨数据
function FightSoulWGData:GetWearBoneDataBySlotPart(slot, part)
    local empty_table = {}
    return ((self.bone_slot_info[slot] or empty_table)[part] or empty_table).bone_data
end

function FightSoulWGData:GetWearBoneDataByParam(fight_soul_type, bone_part, suit_type)
    local wear_data
    local is_wear, slot = self:GetTypeIsWear(fight_soul_type)
	if not is_wear then
		return wear_data
	end

	local wear_bone_data = self:GetWearBoneDataBySlotPart(slot, bone_part)
	if not IsEmptyTable(wear_bone_data) then
		wear_data = wear_bone_data
	end

    return wear_data
end

-- 【魂骨】魂骨背包更新
function FightSoulWGData:UpdateBoneBagInfo(protocol)
	--全部
	if protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.ALL then
		self.bone_bag_list = {}
		for k,v in pairs(protocol.bag_change_list) do
			self.bone_bag_list[v.bag_index] = v
            self:SetFSTypeBoneBagListData(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
		end

        self:CacheAllBonePartCanWear()
        self:CacheAllBoneComposeStuff()
	--获得
	elseif protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM
        or protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.SIXIANG_CALL_GET then
		for k,v in pairs(protocol.bag_change_list) do
			self.bone_bag_list[v.bag_index] = v
            self:CacheSingleBonePartCanWear(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
            self:CacheSingleBoneComposeStuff(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
            self:SetFSTypeBoneBagListData(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
		end
	--移除
	elseif protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.REMOVE_ITEM then
		for k,v in pairs(protocol.bag_change_list) do
            local old_bone_data = self:GetBoneDataByBagIndex(v.bag_index)
            self:CacheSingleBonePartCanWear(protocol.send_reason, old_bone_data)
            self:CacheSingleBoneComposeStuff(protocol.send_reason, old_bone_data)
            self:SetFSTypeBoneBagListData(protocol.send_reason, old_bone_data)
			self.bone_bag_list[v.bag_index] = nil
		end
	--融合成功
	elseif protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.UP_COLOR_STAR then
        local updata_suit_list = {}
		for k,v in pairs(protocol.bag_change_list) do
            local old_bone_data = self:GetBoneDataByBagIndex(v.bag_index)
            local is_wear = self:GetBoneBagIndexIsWear(v.bag_index)
            self.bone_bag_list[v.bag_index] = v
            self:SetFSTypeBoneBagListData(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
            self:CacheSingleBonePartCanWear(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
            if not is_wear then
    			self:CacheSingleBoneComposeStuff(FIGHT_SOUL_BONE_BAG_SEND_REASON.REMOVE_ITEM, old_bone_data)
    			self:CacheSingleBoneComposeStuff(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
            end

            -- 赋值
            if is_wear then
                local _, fs_slot = self:GetTypeIsWear(v.fight_soul_type)
                if self.bone_slot_info and self.bone_slot_info[fs_slot] and self.bone_slot_info[fs_slot][v.bone_part] then
                    self.bone_slot_info[fs_slot][v.bone_part].bone_data = self:GetBoneDataByBagIndex(v.bag_index)
                end

                updata_suit_list[fs_slot] = true
            end
		end

        if not IsEmptyTable(updata_suit_list) then
        	for k,v in pairs(updata_suit_list) do
                self:CalcSuitInfoBySlot(k)
            end
        end
	end
end

-- 【魂骨】获取魂骨背包
function FightSoulWGData:GetBoneBagList()
	return self.bone_bag_list
end

-- 【魂骨】获取魂骨背包的魂骨
function FightSoulWGData:GetBoneDataByBagIndex(bag_index)
	return self.bone_bag_list[bag_index]
end

-- 【魂骨】根据战魂类型、部位、背包位置 缓存部位可装备魂骨
function FightSoulWGData:CacheAllBonePartCanWear()
	local bag_list = self:GetBoneBagList()
	for k,v in pairs(bag_list) do
		self:CacheSingleBonePartCanWear(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
	end
end

-- 【魂骨】根据战魂类型、部位、背包位置 缓存部位可装备魂骨
function FightSoulWGData:CacheSingleBonePartCanWear(send_reason, item_data)
	if not item_data then
		return
	end

	local fight_soul_type = item_data.fight_soul_type
	local bone_part = item_data.bone_part
	local bag_index = item_data.bag_index

	self.cache_bone_part_list = self.cache_bone_part_list or {}
	self.cache_bone_part_list[fight_soul_type] = self.cache_bone_part_list[fight_soul_type] or {}
	self.cache_bone_part_list[fight_soul_type][bone_part] = self.cache_bone_part_list[fight_soul_type][bone_part] or {}

	if send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM then					--获得
		self.cache_bone_part_list[fight_soul_type][bone_part][bag_index] = item_data
	elseif send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.REMOVE_ITEM then			--移除
		self.cache_bone_part_list[fight_soul_type][bone_part][bag_index] = nil
	end
end

-- 【魂骨】根据战魂类型、部位 获取部位可装备魂骨列表
function FightSoulWGData:GetBonePartCanWearCache(fight_soul_type, bone_part)
    local empty_table = {}
    return ((self.cache_bone_part_list or empty_table)[fight_soul_type] or empty_table)[bone_part] or empty_table
end

-- 【魂骨】魂装材料统计所有
function FightSoulWGData:CacheAllBoneComposeStuff()
    local bag_list = self:GetBoneBagList()
	for k,v in pairs(bag_list) do
		self:CacheSingleBoneComposeStuff(FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM, v)
	end
end

-- 【魂骨】魂装材料统计 table[四象类型][部位][品质][星级] = num
function FightSoulWGData:CacheSingleBoneComposeStuff(send_reason, item_data)
    if not item_data then
		return
	end

	local fs_type = item_data.fight_soul_type
	local bone_part = item_data.bone_part
    local color = item_data.color
    local star = item_data.star
    local suit_type = item_data.suit_type
	local bag_index = item_data.bag_index

	self.cache_bone_compose_stuff = self.cache_bone_compose_stuff or {}
	self.cache_bone_compose_stuff[fs_type] = self.cache_bone_compose_stuff[fs_type] or {}
	self.cache_bone_compose_stuff[fs_type][bone_part] = self.cache_bone_compose_stuff[fs_type][bone_part] or {}
    self.cache_bone_compose_stuff[fs_type][bone_part][suit_type] = self.cache_bone_compose_stuff[fs_type][bone_part][suit_type] or {}
    self.cache_bone_compose_stuff[fs_type][bone_part][suit_type][color] = self.cache_bone_compose_stuff[fs_type][bone_part][suit_type][color] or {}
    local stuff_num = self.cache_bone_compose_stuff[fs_type][bone_part][suit_type][color][star] or 0

	if send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM then					--获得
        stuff_num = stuff_num + 1
	elseif send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.REMOVE_ITEM then			--移除
        stuff_num = stuff_num - 1
        stuff_num = stuff_num > 0 and stuff_num or 0
	end

    self.cache_bone_compose_stuff[fs_type][bone_part][suit_type][color][star] = stuff_num
end

--【魂骨】根据四象类型、部位、品质、星级、套装类型 获取魂装缓存的数量
function FightSoulWGData:GetBoneComposeStuffCache(fs_type, bone_part, suit_type, color, star)
    local empty_table = {}
    return (((((self.cache_bone_compose_stuff or empty_table)[fs_type]
        or empty_table)[bone_part] or empty_table)[suit_type]
        or empty_table)[color] or empty_table)[star] or 0
end

--【魂骨】魂骨背包 - 根据四象类型划分 table[四象类型][bag_index]
function FightSoulWGData:SetFSTypeBoneBagListData(send_reason, item_data)
    if not item_data then
        return
    end

    local fs_type = item_data.fight_soul_type
    local bag_index = item_data.bag_index
    self.fs_type_bone_bag_list = self.fs_type_bone_bag_list or {}
    self.fs_type_bone_bag_list[fs_type] = self.fs_type_bone_bag_list[fs_type] or {}

    if send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM then					--获得
        self.fs_type_bone_bag_list[fs_type][bag_index] = item_data
	elseif send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.REMOVE_ITEM then			--移除
        self.fs_type_bone_bag_list[fs_type][bag_index] = nil
	end
end

-- 【魂骨】获得根据四象类型拆分的魂骨背包
function FightSoulWGData:GetFSTypeBoneBagListByType(fs_type)
    local empty_table = {}
    return (self.fs_type_bone_bag_list or empty_table)[fs_type] or empty_table
end

-- 【魂骨】获得魂骨展示背包 排序：穿戴 > 品质 > 星级 > 套装类型 > 部位
function FightSoulWGData:GetFSTypeBoneSortBagListByType(fs_type)
    local show_bag_list = {}

    local max_part_num = FIGHT_SOUL_BONE_TYPE.MAX
    local max_suit_num = 4
    local max_bag_num = self:GetBoneBagMaxNum()
    local wear_weight, color_weight, star_weight, suit_type_weight, part_weight
	local bag_list = self:GetFSTypeBoneBagListByType(fs_type)
	for k,v in pairs(bag_list) do
		local data = {}
		data.item_data = v
		-- 排序
		local is_wear = self:GetBoneBagIndexIsWear(v.bag_index)
		wear_weight = is_wear and (max_part_num - v.bone_part) * 10000000 or 0
		color_weight = is_wear and 0 or v.color * 1000000
		star_weight = is_wear and 0 or v.star * 100000
		suit_type_weight = is_wear and 0 or (v.suit_type + 1) * 10000
        part_weight = is_wear and 0 or (max_part_num - v.bone_part) * 1000
		data.bag_sort_index = wear_weight + suit_type_weight + color_weight + star_weight + part_weight + (max_bag_num - v.bag_index)
		data.stuff_sort_index = 0

		data.select_state = {}
		for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
			data.select_state[i] = false
		end

		table.insert(show_bag_list, data)
	end

	if not IsEmptyTable(show_bag_list) then
		table.sort(show_bag_list, SortTools.KeyUpperSorter("bag_sort_index"))
	end

    self.sort_fs_type_bone_bag_list = show_bag_list
    self:CleanBoneComposeSelectCache()
    self:SetBestHeightInStuffCache(nil)

	return self.sort_fs_type_bone_bag_list
end

function FightSoulWGData:ClearFSTypeBoneSortBagSelectState()
    self:CleanBoneComposeSelectCache()
    self:SetBestHeightInStuffCache(nil)
    if IsEmptyTable(self.sort_fs_type_bone_bag_list) then
    	return
    end

    for k,data in pairs(self.sort_fs_type_bone_bag_list) do
        for i,v in pairs(data.select_state) do
            data.select_state[i] = false
        end
    end
end

function FightSoulWGData.FightSoulBoneBagItemData()
	return {item_id = 0, is_bind = 0, star = 0,
            color = 0, fight_soul_type = 0,
            bone_part = 0, suit_type = 0, sort_index = 0,}
end

-- 【魂骨】获取展示的当前和下级物品数据
function FightSoulWGData:GetBoneComposeShowItemData(cur_item_data)
	local cur_data = {
        item_id = cur_item_data.item_id, is_bind = 0, star = cur_item_data.star,
		color = cur_item_data.color, fight_soul_type = cur_item_data.fight_soul_type,
		bone_part = cur_item_data.bone_part, suit_type = cur_item_data.suit_type,
	}

	local next_data = {
        item_id = 0, is_bind = 0, star = 0,
		color = 0, fight_soul_type = cur_item_data.fight_soul_type,
		bone_part = cur_item_data.bone_part, suit_type = cur_item_data.suit_type,
	}

    local compose_cfg = self:GetBoneComposeCfg(cur_item_data.color, cur_item_data.star)
    if IsEmptyTable(compose_cfg) then
        return cur_data, next_data
    end

    local height_flag = self:GetBoneHeightSelectCache()
    if height_flag then
        local height_data = self:GetBoneDataByBagIndex(height_flag)
        if height_data then
            local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(height_data.item_id)
            next_data.item_id = FightSoulWGData.GetBoneItemIdByParam(cur_item_data.fight_soul_type,
                                    cur_item_data.bone_part, suit_type, compose_cfg.target_color)
            next_data.star = compose_cfg.target_star
            next_data.color = compose_cfg.target_color
            next_data.suit_type = suit_type
            return cur_data, next_data
        end
    end

    next_data.item_id = FightSoulWGData.GetBoneItemIdByParam(cur_item_data.fight_soul_type,
                            cur_item_data.bone_part, cur_item_data.suit_type, compose_cfg.target_color)

    next_data.star = compose_cfg.target_star
    next_data.color = compose_cfg.target_color

	return cur_data, next_data
end

function FightSoulWGData.CheckCurStuffIsSelect(select_state_list)
    if IsEmptyTable(select_state_list) then
    	return false
    end

	for i,j in pairs(select_state_list) do
		if j then
			return true
		end
	end

    return false
end

-- 【融合】筛选出当前道具的全部合成材料（排序: 套装类型 > 背包位置）
function FightSoulWGData:GetMeetBoneComposeStuffSortList(item_data, auto_select)
	local meet_list = {}
	for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
		meet_list[i] = {}
	end

	if IsEmptyTable(item_data) then
		return meet_list
	end

    local bag_list = self.sort_fs_type_bone_bag_list or {}
	local compose_cfg = self:GetBoneComposeCfg(item_data.color, item_data.star)
    if IsEmptyTable(compose_cfg) then
        for k,v in pairs(bag_list) do
            for i,j in pairs(v.select_state) do
                v.select_state[i] = false
            end
        end

        return meet_list
    end

	-- 筛选
	local need_same = compose_cfg.same_part_num > 0
	local need_nosame1 = compose_cfg.same_sixiang_type_num > 0
	local no_stuff = false
	local is_had_select = false
	local stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE

	local color, star, bag_index, suit_type, bone_part, suit_sort
    -- local same_suit_type = false
    local cur_suit_type = item_data.suit_type

	for k,v in pairs(bag_list) do
		color = v.item_data.color
		star = v.item_data.star
		bag_index = v.item_data.bag_index
        suit_type = v.item_data.suit_type
        bone_part = v.item_data.bone_part
		no_stuff = self:GetBoneBagIndexIsWear(bag_index) or item_data.bag_index == bag_index
        -- same_suit_type = cur_suit_type == suit_type

		-- dog sun 任意类型的材料 和相同类型的材料存在同品质同星级的情况
		is_had_select = false
		for i,j in pairs(v.select_state) do
			if auto_select then
				v.select_state[i] = false
			else
				if j then
					is_had_select = true
					break
				end
			end
		end

		if need_same and not no_stuff then
            -- 同部位、星级、品质
			if bone_part == item_data.bone_part and star == item_data.star and color == item_data.color then
				stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
				if not is_had_select or (is_had_select and v.select_state[stuff_type]) then
					table.insert(meet_list[stuff_type], v)
				end
			end
		end

		if need_nosame1 and not no_stuff then
			if color == compose_cfg.same_sixiang_type_color and star == compose_cfg.same_sixiang_type_star then
				stuff_type = FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1
				if not is_had_select or (is_had_select and v.select_state[stuff_type]) then
					table.insert(meet_list[stuff_type], v)
				end
			end
		end
	end

    -- 记录
    local temp_suit_type, cache_type = 0, 0
    for stuff_type,list in ipairs(meet_list) do
        for k,v in ipairs(list) do
            if v.item_data.suit_type > cur_suit_type and v.item_data.suit_type > temp_suit_type then
                temp_suit_type = v.item_data.suit_type
                cache_type = stuff_type
            end
        end
    end
    self:SetBestHeightInStuffCache(cache_type)

    -- 排序
    for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
        if not IsEmptyTable(meet_list[i]) then
            meet_list[i] = self:SortBoneStuffList(item_data, meet_list[i], i)
        end
    end

    if auto_select then
        meet_list = self:BoneComposeBagAutoSelect(item_data, meet_list)
    end

	return meet_list
end

-- 当前 1  排序：3 -> 2 -> 0 -> 1
-- 当前 2  排序：3 -> 0 -> 1 -> 2
-- 材料排序权重
function FightSoulWGData.GetBonePriorityWeight(select_suit_type, suit_type, is_select_height)
    if is_select_height then
        if select_suit_type < FIGHT_SOUL_NUM.MAX_SUIT_NUM then
            if suit_type > select_suit_type then
                local offset = FIGHT_SOUL_NUM.MAX_SUIT_NUM - select_suit_type
                return (suit_type - select_suit_type + offset) * 10000
            else
                return math.abs(suit_type - select_suit_type + 1) * 10000
            end
        end
    end

    return (FIGHT_SOUL_NUM.MAX_SUIT_NUM + 1 - suit_type) * 10000
end

-- 排序：未选中比当前融合更高类型套装 (最高的 > 次高, 最低 > 次低）
-- 排序：选中比当前融合更高类型套装 （选中的排最前， 其他最低 > 次低），最高只选一件
function FightSoulWGData:SortBoneStuffList(item_data, single_meet_list, stuff_type)
    if IsEmptyTable(item_data) or IsEmptyTable(single_meet_list) then
        return single_meet_list
    end

    local select_suit_type = item_data.suit_type
    local max_bag_num = self:GetBoneBagMaxNum()
    local max_part_num = FIGHT_SOUL_BONE_TYPE.MAX

    local weight, select_weight, height_select_weight = 0, 0, 0
    local height_is_cur = false
    local cache_type = self:GetBestHeightInStuffCache()
    local select_height = self:GetBoneHeightSelectCache()
    local best_is_cur = stuff_type == cache_type
    local bag_index, suit_type, bone_part, is_select
    for k,v in ipairs(single_meet_list) do
        bag_index = v.item_data.bag_index
        suit_type = v.item_data.suit_type
        bone_part = v.item_data.bone_part
        is_select = FightSoulWGData.CheckCurStuffIsSelect(v.select_state)
        select_weight = is_select and 100000 or 0
        height_is_cur = bag_index == select_height
        height_select_weight = height_is_cur and 1000000 or 0
        weight = FightSoulWGData.GetBonePriorityWeight(select_suit_type, suit_type, select_height == nil and best_is_cur)
        v.stuff_sort_index = height_select_weight + select_weight + weight + (max_part_num - bone_part) * 1000 + (max_bag_num - bag_index)
    end

    table.sort(single_meet_list, SortTools.KeyUpperSorters("stuff_sort_index"))

    return single_meet_list
end

-- 自动选择 优先选一件比当前融合更高类型套装的魂装， 剩下从最低套装开始选
function FightSoulWGData:AutoSelectBoneStuff(item_data, stuff_type, single_meet_list)
    if IsEmptyTable(single_meet_list) then
        return single_meet_list
    end

    local compose_cfg = self:GetBoneComposeCfg(item_data.color, item_data.star)
    if IsEmptyTable(compose_cfg) then
        return single_meet_list
    end

    local select_suit_type = item_data.suit_type
    local need_num = 0
    if stuff_type == FIGHT_SOUL_STUFF_TYPE.SAME_TYPE then
        need_num = compose_cfg.same_part_num
    elseif stuff_type == FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 then
        need_num = compose_cfg.same_sixiang_type_num
    end

    -- 统计选择数量
    local add_num_flag = 0
    for k,v in ipairs(single_meet_list) do
        for i,j in pairs(v.select_state) do
            if i == stuff_type and j then
                add_num_flag = add_num_flag + 1
            end
        end
    end

    local function select_stuff(select_one)
        local is_had_select = false
        for k,v in ipairs(single_meet_list) do
            is_had_select = FightSoulWGData.CheckCurStuffIsSelect(v.select_state)

            if not is_had_select then
                if add_num_flag < need_num then
                    add_num_flag = add_num_flag + 1
                    v.select_state[stuff_type] = true
                    self:AddBoneComposeSelectCache(v.item_data.bag_index)
                    if select_one and v.item_data.suit_type > select_suit_type then
                        self.bone_compose_select_height = v.item_data.bag_index
                        break
                    end
                end
            end
        end
    end

    if add_num_flag < need_num then
        local cache_type = self:GetBestHeightInStuffCache()
        if self.bone_compose_select_height == nil and stuff_type == cache_type then
            select_stuff(true)
        end

        if self.bone_compose_select_height ~= nil then
            self:SortBoneStuffList(item_data, single_meet_list, stuff_type)
        end

        if add_num_flag < need_num then
            select_stuff()
        end
    end

    return single_meet_list
end

-- 1.相同部位的优先选中比自身更高类型套装，若没有则选中无套装类型的。
-- 2.不同部位区分情况：
-- a.1中已选中套装：优先选中无套装类型
-- b.1中无选中套装：优先选中比自身高的套装类型（只选1件）
function FightSoulWGData:BoneComposeBagAutoSelect(item_data, meet_list)
    if IsEmptyTable(item_data) then
        return meet_list
    end

    local compose_cfg = self:GetBoneComposeCfg(item_data.color, item_data.star)
    if IsEmptyTable(compose_cfg) then
        return meet_list
    end

    self:CleanBoneComposeSelectCache()

    for i = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE, FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1 do
    	if not IsEmptyTable(meet_list[i]) then
    		meet_list[i] = self:AutoSelectBoneStuff(item_data, i, meet_list[i])
    	end
    end

    -- 有一方选了 就要在另一方将材料中移除
    local length = 0
    local other_select = false
    for stuff_type, list in ipairs(meet_list) do
        length = #list
        for i = length, 1, -1 do
            local data = list[i]
            other_select = false
            for i,j in pairs(data.select_state) do
                if i ~= stuff_type and j then
                    other_select = true
                    break
                end
            end

            if other_select then
                table.remove(meet_list[stuff_type], i)
            end
        end
    end

    return meet_list
end

-- 【魂骨】获得合成界面材料展示
-- 依赖排序背包的数据
function FightSoulWGData:GetBoneComposeShowStuffList(bag_index, meet_list)
	local stuff_list = {}
	local is_meet_list = {}
	local is_max = false
	local item_data = self:GetBoneDataByBagIndex(bag_index)

	if IsEmptyTable(item_data) then
		return stuff_list, is_meet_list, is_max
	end

	local compose_cfg = self:GetBoneComposeCfg(item_data.color, item_data.star)
	if IsEmptyTable(compose_cfg) then
		is_max = true
		return stuff_list, is_meet_list, is_max
	end

	local function get_had_num_suit_type(type, list)
		local num, suit_type = 0, 0
		if IsEmptyTable(list) then
			return num, suit_type
		end

		for k,v in pairs(list) do
			if v.select_state[type] then
				num = num + 1

                if v.item_data.suit_type > suit_type then
                    suit_type = v.item_data.suit_type
                end
			end
		end

		return num, suit_type
	end

	local is_meet_same, is_meet_nosame = false, false
    local had_num, show_suit_type = 0, 0
    local stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE

	-- 材料1
	if compose_cfg.same_part_num > 0 then
		local temp_data = FightSoulWGData.StuffItemShowData()
		temp_data.star = item_data.star
		temp_data.color = item_data.color
		-- temp_data.item_id = item_data.item_id
		temp_data.need_num = compose_cfg.same_part_num
        stuff_type = FIGHT_SOUL_STUFF_TYPE.SAME_TYPE
		had_num, show_suit_type = get_had_num_suit_type(stuff_type, meet_list[stuff_type])
        temp_data.had_num = had_num
        temp_data.show_suit_type = 0--show_suit_type
		temp_data.stuff_type = stuff_type
        temp_data.icon = item_data.item_id
		table.insert(stuff_list, temp_data)
		is_meet_same = temp_data.had_num >= temp_data.need_num
        is_meet_list[stuff_type] = is_meet_same
	else
		is_meet_same = true
	end

	-- 材料2
	if compose_cfg.same_sixiang_type_num > 0 then
		local temp_data = FightSoulWGData.StuffItemShowData()
		temp_data.star = compose_cfg.same_sixiang_type_star
		temp_data.color = compose_cfg.same_sixiang_type_color
		temp_data.need_num = compose_cfg.same_sixiang_type_num
        stuff_type = FIGHT_SOUL_STUFF_TYPE.NOSAME_TYPE_1
        had_num, show_suit_type = get_had_num_suit_type(stuff_type, meet_list[stuff_type])
        temp_data.had_num = had_num
        temp_data.show_suit_type = 0--show_suit_type
		temp_data.stuff_type = stuff_type
		temp_data.icon = 1
		table.insert(stuff_list, temp_data)
		is_meet_nosame = temp_data.had_num >= temp_data.need_num
        is_meet_list[stuff_type] = is_meet_nosame
	else
		is_meet_nosame = true
	end

	return stuff_list, is_meet_list, is_max
end

-- 【魂骨】清除 魂骨融合界面选择缓存
function FightSoulWGData:CleanBoneComposeSelectCache()
	self.bone_compose_select_cache = {}
    self.bone_compose_select_height = nil
end

-- 【魂骨】获取 魂骨融合界面选择缓存
function FightSoulWGData:GetBoneComposeSelectCache()
	return self.bone_compose_select_cache
end

-- 【魂骨】添加 魂骨融合界面选择缓存
function FightSoulWGData:AddBoneComposeSelectCache(bag_index)
	self.bone_compose_select_cache[bag_index] = true
end

-- 【魂骨】移除 魂骨融合界面选择缓存
function FightSoulWGData:RemoveBoneComposeSelectCache(bag_index)
	self.bone_compose_select_cache[bag_index] = nil
end

-- 【魂骨】获取所选的比当前高的套装装备
function FightSoulWGData:GetBoneHeightSelectCache()
    return self.bone_compose_select_height
end

-- 【魂骨】记录所选的比当前高的套装装备
function FightSoulWGData:SetBoneHeightSelectCache(bag_index)
    self.bone_compose_select_height = bag_index
end

function FightSoulWGData:GetBestHeightInStuffCache()
    return self.best_height_in_stuff_type
end

function FightSoulWGData:SetBestHeightInStuffCache(stuff_type)
    self.best_height_in_stuff_type = stuff_type
end


-- 【魂骨】获取战魂阵位 魂装所有战力
function FightSoulWGData:GetSlotBoneCapability(slot)
    local part_list = self:GetBonePartListBySlot(slot)
	local capability = 0
	if IsEmptyTable(part_list) then
		return capability
	end

	local base_attribute = AttributePool.AllocAttribute()
    local base_attr_cfg, strength_cfg, attr_str
    for k,v in pairs(part_list) do
        -- 基础
        if not IsEmptyTable(v.bone_data) then
            base_attr_cfg = self:GetBoneAttrCfg(k, v.bone_data.color, v.bone_data.star)
            if base_attr_cfg ~= nil then
                for attr, value in pairs(base_attr_cfg) do
                    attr_str = AttributeMgr.GetAttributteKey(attr)
                    if base_attribute[attr_str] ~= nil and value > 0 then
                        base_attribute[attr_str] = base_attribute[attr_str] + value
                    end
                end
            end
        end

        -- 强化
        if v.slot_level > 0 then
            strength_cfg = self:GetBoneStrengthCfg(k, v.slot_level)
            if strength_cfg ~= nil then
                for attr, value in pairs(strength_cfg) do
                    attr_str = AttributeMgr.GetAttributteKey(attr)
                    if base_attribute[attr_str] ~= nil and value > 0 then
                        base_attribute[attr_str] = base_attribute[attr_str] + value
                    end
                end
            end
        end
    end

    -- 套装
    local suit_attr_list, temp_skill_cfg = self:GetBoneSuitAttrTable(slot)
    for attr, value in pairs(suit_attr_list) do
        attr_str = AttributeMgr.GetAttributteKey(attr)
        if base_attribute[attr_str] ~= nil and value > 0 then
            base_attribute[attr_str] = base_attribute[attr_str] + value
        end
    end

	capability = AttributeMgr.GetCapability(base_attribute, temp_skill_cfg)
	return capability
end

-- 【魂骨】单个魂装红点
function FightSoulWGData:GetSingleBoneComposeRemind(item_data)
    if item_data == nil then
        return false
    end

    local need_fs_type = item_data.fight_soul_type
    local same_type_color = item_data.color
    local same_type_star = item_data.star
    local same_type_suit_type = item_data.suit_type
    local same_bone_part = item_data.bone_part

    local is_wear, slot = self:GetTypeIsWear(need_fs_type)
    local wear_data = self:GetWearBoneDataBySlotPart(slot, same_bone_part)
    -- 未装备魂骨
    if IsEmptyTable(wear_data) then
        return false
    end

    local compose_cfg = self:GetBoneComposeCfg(item_data.color, item_data.star)
    if IsEmptyTable(compose_cfg) then
        return false
    end

    local same_type_num = compose_cfg.same_part_num
    local nosame_type_num = compose_cfg.same_sixiang_type_num
    local nosame_type_color = compose_cfg.same_sixiang_type_color
    local nosame_type_star = compose_cfg.same_sixiang_type_star

    local is_wear = self:GetBoneBagIndexIsWear(item_data.bag_index)
    local is_meet_same, is_meet_nosame = false, false
    local same_num, nosame_num = 0, 0
    if same_type_num > 0 then
        for suit = 0, FIGHT_SOUL_NUM.MAX_ACT_SUIT do
            same_num = same_num + self:GetBoneComposeStuffCache(need_fs_type, same_bone_part, suit, same_type_color, same_type_star)
            -- 自身不当材料
            if not is_wear and same_type_suit_type == suit then
                same_num = same_num - 1
                same_num = same_num > 0 and same_num or 0
            end
        end

        is_meet_same = same_num >= same_type_num
    else
        is_meet_same = true
    end

    -- 任意类型材料
    local range_same_to_same = false		-- 任意的材料品质星级 与 相同材料的一致 特殊处理
    if nosame_type_num > 0 then
        if same_type_color == nosame_type_color and same_type_star == nosame_type_star then
            range_same_to_same = same_type_num > 0
        end

        local temp_num = 0
        for part = 0, FIGHT_SOUL_BONE_TYPE.MAX do
            for suit = 0, FIGHT_SOUL_NUM.MAX_ACT_SUIT do
                temp_num = self:GetBoneComposeStuffCache(need_fs_type, part, suit, nosame_type_color, nosame_type_star)
                nosame_num = nosame_num + temp_num
            end

            if range_same_to_same and same_bone_part == part then
                nosame_num = nosame_num - same_type_num
                nosame_num = nosame_num > 0 and nosame_num or 0
            end
        end

        -- 自身不当材料
        if not is_wear and nosame_type_color == item_data.color and nosame_type_star == item_data.star then
            nosame_num = nosame_num - 1
            nosame_num = nosame_num > 0 and nosame_num or 0
        end

        is_meet_nosame = nosame_num >= nosame_type_num
    else
        is_meet_nosame = true
    end

    return is_meet_same and is_meet_nosame
end

-- 【魂骨】单个阵位魂装红点
function FightSoulWGData:GetFSTypeBoneComposeRemind(fs_type)
    local bag_list = self:GetFSTypeBoneBagListByType(fs_type)
    for k,v in pairs(bag_list) do
        if self:GetSingleBoneComposeRemind(v) then
            return true
        end
    end

    return false
end

-- 【魂骨】获取阵位是否穿戴装备
function FightSoulWGData:GetSlotHaveWearEquip(slot)
    local part_list = self:GetBonePartListBySlot(slot)
    for k,v in pairs(part_list) do
        if not IsEmptyTable(v.bone_data) then
        	return true
        end
    end

    return false
end

-- 【魂骨】获取阵位穿戴装备状态
function FightSoulWGData:GetSlotWearEquipState(slot)
    local part_show_list = {}
    local part_list = self:GetBonePartListBySlot(slot)
    for k,v in pairs(part_list) do
        part_show_list[k] = not IsEmptyTable(v.bone_data)
    end

    return part_show_list
end

-- 【魂骨】初始化套装激活信息
function FightSoulWGData:InitSuitInfo()
    self.bone_suit_info = {}
    for sx_slot = 0, GameEnum.FIGHT_SOUL_SLOT_NUM - 1 do
        self.bone_suit_info[sx_slot] = {}
        self:CleanSuitInfoBySlot(sx_slot)
    end
end

-- 【魂骨】恢复默认套装激活信息
function FightSoulWGData:CleanSuitInfoBySlot(slot)
    self.bone_suit_info[slot] = self.bone_suit_info[slot] or {}
    for suit = 1, FIGHT_SOUL_NUM.MAX_ACT_SUIT do
        local data = {}
        data.suit_type = -1
        data.color = 0
        data.same_num = 0
        self.bone_suit_info[slot][suit] = data
    end
end

-- 【魂骨】设置套装激活信息
function FightSoulWGData:SetSingleSuitInfoBySlot(slot, suit_count, suit_type, color, same_num)
    self.bone_suit_info[slot] = self.bone_suit_info[slot] or {}

    local data = {}
    data.suit_type = suit_type or -1
    data.color = color or 0
    data.same_num = same_num or 0
    self.bone_suit_info[slot][suit_count] = data
end

function FightSoulWGData:CalcAllSuitInfo()
    for sx_slot = 0, GameEnum.FIGHT_SOUL_SLOT_NUM - 1 do
        self:CalcSuitInfoBySlot(sx_slot)
    end

    -- 统计激活技能附加效果
    for sx_slot = 0, GameEnum.FIGHT_SOUL_SLOT_NUM - 1 do
        self:CalcSkillAddBuffBySlot(sx_slot)
    end
end

-- 【魂骨】统计套装激活信息
function FightSoulWGData:CalcSuitInfoBySlot(slot)
    self:CleanSuitInfoBySlot(slot)
    local part_list = self:GetBonePartListBySlot(slot)
    if IsEmptyTable(part_list) then
    	return
    end

    local fs_data = self:GetFightSoulSlot(slot)
    if IsEmptyTable(fs_data) then
        return
    end

    local fs_type = fs_data:GetType()
    local calc_color_num = {}
    local bone_data, suit_type, item_color
    for k,v in pairs(part_list) do
        bone_data = v.bone_data
        if not IsEmptyTable(bone_data) then
            suit_type = bone_data.suit_type
            item_color = bone_data.color
            for suit = suit_type, 1, -1 do
                for color = item_color, 1, -1 do
                    calc_color_num[suit] = calc_color_num[suit] or {}
                    calc_color_num[suit][color] = calc_color_num[suit][color] and calc_color_num[suit][color] + 1 or 1
                end
            end
        end
    end

    local max_color = GameEnum.ITEM_COLOR_COLOR_FUL
    local temp_info, color_num, suit_cfg
    local act_flag = {}
    local suit_count = 0
    for suit = FIGHT_SOUL_NUM.MAX_SUIT_NUM, 1, -1 do
        temp_info = calc_color_num[suit]
        if nil ~= temp_info then
            for color = max_color, 1, -1 do
                color_num = temp_info[color] or 0
                for num = color_num, 1, -1 do
                    suit_cfg = self:GetBoneSuitNumCfg(fs_type, suit, color, num)
                    if suit_cfg and suit_count < FIGHT_SOUL_NUM.MAX_ACT_SUIT and not act_flag[num] then
                        suit_count = suit_count + 1
                        act_flag[num] = true
                        self:SetSingleSuitInfoBySlot(slot, suit_count, suit, color, num)
                    end
                end
            end
        end
    end

    table.sort(self.bone_suit_info[slot], SortTools.KeyLowerSorters("suit_type", "color", "same_num"))
end

-- 【魂骨】根据阵位获取套装信息
function FightSoulWGData:GetBoneSuitInfo()
    return self.bone_suit_info
end

-- 【魂骨】根据阵位获取套装信息
function FightSoulWGData:GetBoneSuitInfoBySlot(slot)
    return self.bone_suit_info[slot] or {}
end

-- 获取套装主动技能激活套装数据
function FightSoulWGData:GetAutoSkillActSuitDataBySlot(slot)
    local data
    local suit_info = self:GetBoneSuitInfoBySlot(slot)
    for k,v in pairs(suit_info) do
        if v.same_num == 6 then
            data = v
        end
    end

    return data
end

-- 获取技能描述
function FightSoulWGData:GetAutoSkillActDesc(active_skill, skill_level, skill_effect_id)
    skill_effect_id = skill_effect_id or 0
    skill_level = skill_effect_id > 0 and (skill_level * 100 + skill_effect_id) or skill_level
    self.cache_skill_desc_list = self.cache_skill_desc_list or {}
    self.cache_skill_desc_list[active_skill] = self.cache_skill_desc_list[active_skill] or {}
    local skill_desc = self.cache_skill_desc_list[active_skill][skill_level] or ""
    if skill_desc == "" then
        skill_desc = SkillWGData.Instance:GetSiXiangSkillDes(active_skill, skill_level, true)
        self.cache_skill_desc_list[active_skill][skill_level] = skill_desc
    end

    return skill_desc
end

function FightSoulWGData:GetSlotAllSkillDesc(slot, level)
    local fs_data = self:GetFightSoulSlot(slot)
    local skill_desc = ""
    if fs_data == nil or not fs_data:GetIsWear() then
        return skill_desc
    end

    local active_skill, skill_level = fs_data:GetSkillIdAndLevel()
    level = level or skill_level
    skill_desc = self:GetAutoSkillActDesc(active_skill, level, 0)
    local suit_data = self:GetAutoSkillActSuitDataBySlot(slot)
    if suit_data == nil then
        return skill_desc
    end

    local fs_type = fs_data:GetType()
    for i = 1, suit_data.suit_type do
        local suit_cfg = self:GetBoneSuitNumCfg(fs_type, i, suit_data.color, suit_data.same_num)
        if suit_cfg and suit_cfg.skill_effect_id > 0 then
            local desc = self:GetAutoSkillActDesc(active_skill, skill_level, suit_cfg.skill_effect_id)
            skill_desc = string.format("%s\n%s", skill_desc, desc)
        end
    end

    return skill_desc
end

-- 获取主动技能名称
function FightSoulWGData:GetSuitAutoSkillNameBySlot(slot)
    local main_skill_name = ""
    local fs_data = self:GetFightSoulSlot(slot)
    if fs_data == nil or not fs_data:GetIsWear() then
        return main_skill_name
    end

    local active_skill, skill_level = fs_data:GetSkillIdAndLevel()
    local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(active_skill, skill_level)
    if skill_cfg then
        main_skill_name = skill_cfg.skill_name
    end

    local suit_data = self:GetAutoSkillActSuitDataBySlot(slot)
    local color = fs_data:GetColor()
    if suit_data == nil then
        return ToColorStr(main_skill_name, ITEM_COLOR[color])
    end

    main_skill_name = string.format("%s·%s", Language.FightSoul.SuitShortStr[suit_data.suit_type], main_skill_name)
    return ToColorStr(main_skill_name, ITEM_COLOR[color])
end

-- 获取技能描述列表（带激活状态）
function FightSoulWGData:GetSuitAutoSkillActDescBySlot(slot)
    local skill_desc_list = {}
    local fs_data = self:GetFightSoulSlot(slot)
    if fs_data == nil or not fs_data:GetIsWear() then
        return skill_desc_list
    end

    local data = {}
    data.is_act = true
    skill_desc_list[0] = data
    local active_skill, skill_level = fs_data:GetSkillIdAndLevel()
    data.desc = self:GetAutoSkillActDesc(active_skill, skill_level, 0)

    local fs_type = fs_data:GetType()
    local suit_data = self:GetAutoSkillActSuitDataBySlot(slot)
    local function get_skill_desc(suit_type, color, same_num, no_data)
        for i = 1, FIGHT_SOUL_NUM.MAX_SUIT_NUM do
            local temp_data = {}
            local is_act = i <= suit_type
            temp_data.is_act = is_act
            temp_data.desc = ""
            local suit_cfg = self:GetBoneSuitNumCfg(fs_type, i, color, same_num)
            if suit_cfg and suit_cfg.skill_effect_id > 0 then
                local skill_effect_id = suit_cfg.skill_effect_id
                if not is_act then
                    local min_suit_cfg = self:GetBoneSuitNumCfg(fs_type, i, GameEnum.ITEM_COLOR_PURPLE, same_num)
                    skill_effect_id = min_suit_cfg and min_suit_cfg.skill_effect_id or 0
                end

                local desc = self:GetAutoSkillActDesc(active_skill, skill_level, skill_effect_id)
                if not is_act then
                    desc = string.gsub(desc, "<color.->", "")
                    desc = string.gsub(desc, "</color>", "")
                    local need_desc = ""--string.format(Language.FightSoul.AutoSkillActNeedDesc, Language.FightSoul.SuitShortStr[i])
                    desc = string.format("%s%s", desc, need_desc)
                    desc = ToColorStr(desc, COLOR3B.GRAY)
                end

                temp_data.desc = desc
            end

            table.insert(skill_desc_list, temp_data)
        end
    end
    if suit_data == nil then
        get_skill_desc(0, GameEnum.ITEM_COLOR_PURPLE, 6)
        return skill_desc_list
    end

    get_skill_desc(suit_data.suit_type, suit_data.color, suit_data.same_num)
    return skill_desc_list
end

-- 【魂骨】统计技能附加效果
function FightSoulWGData:CalcSkillAddBuffBySlot(slot)
    local suit_info = self:GetBoneSuitInfoBySlot(slot)
    local skill_buff_id = 0

    local slot_data = self:GetFightSoulSlot(slot)
    if IsEmptyTable(slot_data) then
        self.cahce_skill_stuff_id_byslot[slot] = skill_buff_id
        return
    end

    local fs_type = slot_data:GetType()
    for k,v in pairs(suit_info) do
        if v.same_num == 6 then
            local suit_cfg = self:GetBoneSuitNumCfg(fs_type, v.suit_type, v.color, v.same_num)
            if suit_cfg then
                skill_buff_id = suit_cfg.skill_effect_id
            end
        end
    end

    self.cahce_skill_stuff_id_byslot[slot] = skill_buff_id
end

function FightSoulWGData:GetSkillAddBuffBySlot(slot)
    return self.cahce_skill_stuff_id_byslot[slot] or 0
end

-- 【魂骨】根据展示的套装信息
function FightSoulWGData:GetBoneShowSuitInfoBySlot(slot)
    local show_list = {}
    local suit_list = self:GetBoneSuitInfoBySlot(slot)
    if IsEmptyTable(suit_list) then
    	return show_list
    end

    -- 相同套展示最大的
    local calc_same_list = {}
    local cur_num = 0
    for k,v in ipairs(suit_list) do
        if v.suit_type >= 0 then
            local cur_num = calc_same_list[v.suit_type] or 0
            if v.same_num > cur_num then
                calc_same_list[v.suit_type] = v.same_num
            end
        end
    end

    local is_show
    for k,v in ipairs(suit_list) do
        is_show = calc_same_list[v.suit_type]
        if nil ~= is_show and v.same_num == is_show then
            table.insert(show_list, v)
        end
    end

    return show_list
end

-- 【魂骨】根据展示的套装信息
function FightSoulWGData:GetNewBoneShowSuitInfoBySlot(slot)
    local show_list = {}
    local fs_data = self:GetFightSoulSlot(slot)
    if fs_data == nil or not fs_data:GetIsWear() then
        return show_list
    end

    local fs_type = fs_data:GetType()
    local suit_list = self:GetBoneSuitInfoBySlot(slot)
    if IsEmptyTable(suit_list) then
    	return show_list
    end

    table.sort(suit_list, function(a, b)
		return a.same_num < b.same_num
    end)

    local suit_type, color, same_num = 0, 0, 0
    local tips_cfg = self:GetTipSkillCfgList(fs_type)
    for k,v in ipairs(suit_list) do
        if v.suit_type > 0 then
            local data = {}
            suit_type = v.suit_type
            color = v.color
            same_num = v.same_num
            data.fs_type = fs_type
            data.suit_type = suit_type
            data.color = color
            data.same_num = same_num
            data.skill_list = {}
            local suit_cfg = self:GetBoneSuitNumCfg(fs_type, suit_type, color, same_num)
            if suit_cfg and tips_cfg[same_num] then
                for i,j in ipairs(tips_cfg[same_num]) do
                    local skill_data = {}
                    local attr_id = suit_cfg["attr_id_" .. i]
                    local attr_value = suit_cfg["attr_value_" .. i]
                    skill_data.skill_name = j.skill_name
                    skill_data.skill_icon = j.skill_icon
                    skill_data.level = color - 2
                    if suit_cfg.skill_effect_id > 0 then
                        skill_data.is_act = suit_type >= i
                        skill_data.is_show = true
                    else
                        skill_data.is_act = attr_id > 0 and attr_value > 0
                        skill_data.is_show = attr_id > 0 and attr_value > 0
                    end
                    table.insert(data.skill_list, skill_data)
                end
            end

            table.insert(show_list, data)
        end
    end

    return show_list
end

-- 【魂骨】获取套装是否激活
function FightSoulWGData:GetBoneSuitIsAct(fs_type, suit_type, color, same_num)
    local is_wear, slot = self:GetTypeIsWear(fs_type)
    if not is_wear then
        return false
    end

    local suit_info = self:GetBoneSuitInfoBySlot(slot)
    for k,v in pairs(suit_info) do
        if v.suit_type == suit_type and v.color == color and v.same_num == same_num then
            return true
        end
    end

    return false
end

-- 【魂骨】阵位的套装属性
function FightSoulWGData:GetBoneSuitAttrTable(slot)
    local suit_list = self:GetBoneSuitInfoBySlot(slot)
    local attr_list = {}
    local temp_skill_cfg = {attack_power = 0, defence_power = 0, capability_inc = 0}
    if IsEmptyTable(suit_list) then
    	return attr_list, temp_skill_cfg
    end

    local fs_data = self:GetFightSoulSlot(slot)
    if IsEmptyTable(fs_data) then
    	return
    end

    local fs_type = fs_data:GetType()
    local attr_id, attr_value, attr_str
    for k,v in pairs(suit_list) do
        local suit_cfg = self:GetBoneSuitNumCfg(fs_type, v.suit_type, v.color, v.same_num)
        if suit_cfg then
            for i = 1, 6 do
                attr_id = suit_cfg["attr_id_" .. i]
                attr_value = suit_cfg["attr_value_" .. i]
                if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                    attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
                    attr_list[attr_str] = attr_list[attr_str] and (attr_list[attr_str] + attr_value) or attr_value
                end
            end

            if suit_cfg.skill_effect_id > 0 then
                local fs_data = self:GetFightSoulSlot(slot)
                if fs_data ~= nil then
                    local active_skill, skill_level = fs_data:GetSkillIdAndLevel()
                    local base_skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(active_skill, skill_level)
                    skill_level = skill_level * 100 + suit_cfg.skill_effect_id
                    local buff_skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(active_skill, skill_level)

                    if base_skill_cfg and buff_skill_cfg then
                        local attack_power = buff_skill_cfg.attack_power - base_skill_cfg.attack_power
                        local defence_power = buff_skill_cfg.defence_power - base_skill_cfg.defence_power
                        local capability_inc = buff_skill_cfg.capability_inc - base_skill_cfg.capability_inc
                        temp_skill_cfg = {
                            attack_power = attack_power > 0 and attack_power or 0,
                            defence_power = defence_power > 0 and defence_power or 0,
                            capability_inc = capability_inc > 0 and capability_inc or 0,
                        }
                    end
                end
            end
        end
    end


    return attr_list, temp_skill_cfg
end

-- 【魂骨】获取套装激活的汇总列表(套装tips展示)
function FightSoulWGData:GetBoneTotalSuitData(fs_type)
    local is_wear, slot = self:GetTypeIsWear(fs_type)
    if not is_wear then
        return
    end

    local suit_info = self:GetBoneSuitInfoBySlot(slot)
    local show_info = {}
    if IsEmptyTable(suit_info) then
    	return show_info
    end

    local temp_show_info = {}
    for k,v in pairs(suit_info) do
        if v.suit_type > 0 then
            temp_show_info[v.suit_type] = temp_show_info[v.suit_type] or {}
            local data = temp_show_info[v.suit_type]

            data.suit_type = v.suit_type
            data.sort = v.same_num
            data.suit_name = self:GetBoneSuitName(v.suit_type, fs_type)
            data.act_list = data.act_list or {}

            local attr_id, attr_value
            local suit_cfg = self:GetBoneSuitNumCfg(fs_type, v.suit_type, v.color, v.same_num)
            if suit_cfg then
                local temp_data = {same_num = v.same_num, same_num_str = string.format(Language.F2Tip.ActNumDesc, v.same_num), attr_list = {}}
                for i = 1, 6 do
                    attr_id = suit_cfg["attr_id_" .. i]
                    attr_value = suit_cfg["attr_value_" .. i]
                    if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                        local attr_str = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_id, true, true)
                        local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
                        local per_desc = is_per and "%" or ""
                        attr_value = is_per and attr_value / 100 or attr_value
                        attr_value = attr_value .. per_desc
                        local desc_str = string.format("<color=%s>%s%s</color>", ITEM_COLOR[v.color], attr_str, attr_value)
                        table.insert(temp_data.attr_list, desc_str)
                    end
                end

                if suit_cfg.skill_effect_id > 0 then
                    local skill_name_desc = ""
                    local skill_desc_str = ""
                    local fs_data = self:GetFightSoulSlot(slot)
                    if fs_data ~= nil then
                        local active_skill, skill_level = fs_data:GetSkillIdAndLevel()
                        skill_level = skill_level * 100 + suit_cfg.skill_effect_id
                        skill_desc_str = SkillWGData.Instance:GetSiXiangSkillDes(active_skill, skill_level, true)

                        local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(active_skill, skill_level)
                        if skill_cfg then
                            skill_name_desc = ToColorStr(skill_cfg.skill_name, ITEM_COLOR[v.color]) .. "\n"
                        end
                    end

                    table.insert(temp_data.attr_list, string.format("%s%s", skill_name_desc, skill_desc_str))
                end

                table.insert(data.act_list, temp_data)
            end
        end
    end

    for k,v in pairs(temp_show_info) do
        table.insert(show_info, v)
    end

    if not IsEmptyTable(show_info) then
    	for k,v in pairs(show_info) do
            table.sort(show_info[k].act_list, SortTools.KeyLowerSorter("same_num"))
        end

        table.sort(show_info, SortTools.KeyLowerSorter("sort"))
    end

    return show_info
end

-- 【魂骨】套装属性（物品提示展示）
function FightSoulWGData:GetBoneSuitData(fs_type, suit_type, color)
    local suit_list = self:GetBoneSuitCfg(fs_type, suit_type, color)
    local suit_data = {}
    local equip_score = 0
    if IsEmptyTable(suit_list) then
        return suit_data, equip_score
    end

    local grey_color = COLOR3B.GRAY
    local same_num_color = "#CC7629FF"
    local str_color = "#C4B8A8FF"

    suit_data.attr_list = {}
    local suit_name, attr_id, attr_value, is_act = nil, 0, 0, false
    local is_per, per_desc, desc_str
    local act_max_num, max_need_num = 0, 0
    for k,v in pairs(suit_list) do
        if suit_name == nil then
            local name = self:GetBoneSuitName(suit_type, fs_type)
            suit_name = string.format(Language.F2Tip.SuitDesc, name)
        end

        is_act = self:GetBoneSuitIsAct(fs_type, suit_type, color, v.same_num)
        if is_act and v.same_num > act_max_num then
            act_max_num = v.same_num
        end

        if v.same_num > max_need_num then
            max_need_num = v.same_num
        end

        local data = {}
        data.same_num = v.same_num
        data.same_num_str = string.format(Language.F2Tip.ActNumDesc, v.same_num)
        data.same_num_str = ToColorStr(data.same_num_str, is_act and same_num_color or grey_color)

        data.attr_list = {}
        for i = 1, 6 do
            attr_id = v["attr_id_" .. i]
            attr_value = v["attr_value_" .. i]
            if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                local attr_str = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_id, true, true)
                is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
                per_desc = is_per and "%" or ""
                attr_value = is_per and attr_value / 100 or attr_value
                attr_value = attr_value .. per_desc
                desc_str = string.format("<color=%s>%s<color=%s>%s</color></color>",
                                        is_act and str_color or grey_color, attr_str,
                                        is_act and COLOR3B.D_GREEN or grey_color, attr_value)
                table.insert(data.attr_list, desc_str)

                if is_act then
                    equip_score = equip_score + TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_id, v["attr_value_" .. i])
                end
            end
        end

        if v.skill_effect_id > 0 then
            local skill_name_desc = ""
            desc_str = ""
            local is_wear, slot = self:GetTypeIsWear(fs_type)
            local fs_data = self:GetFightSoulSlot(slot)
            local active_skill, skill_level = 0, 0
            if is_wear and fs_data ~= nil then
                active_skill, skill_level = fs_data:GetSkillIdAndLevel()
                skill_level = skill_level * 100 + v.skill_effect_id
            else
                local fs_item_cfg = self:GetDefaultSkillInfoByFSType(fs_type)
                if fs_item_cfg then
                    active_skill, skill_level = fs_item_cfg.skill_id, fs_item_cfg.skill_level
                    skill_level = skill_level * 100 + v.skill_effect_id
                end
            end

            desc_str = SkillWGData.Instance:GetSiXiangSkillDes(active_skill, skill_level, true)
            local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(active_skill, skill_level)
            if skill_cfg then
                skill_name_desc = ToColorStr(skill_cfg.skill_name, is_act and COLOR3B.D_GREEN or grey_color) .. "\n"
            end

            if not is_act then
                desc_str = string.gsub(desc_str, "<color.->", "")
                desc_str = string.gsub(desc_str, "</color>", "")
                desc_str = ToColorStr(desc_str, grey_color)
            end

            table.insert(data.attr_list, string.format("%s%s", skill_name_desc, desc_str))
        end

        table.insert(suit_data.attr_list, data)
    end

    if not IsEmptyTable(suit_data.attr_list) then
        table.sort(suit_data.attr_list, SortTools.KeyLowerSorter("same_num"))
    end

    suit_data.act_max_num = act_max_num
    suit_data.max_need_num = max_need_num
    suit_data.suit_name = suit_name or ""
    return suit_data, equip_score
end

function FightSoulWGData:GetBoneSuitShowData(fs_type, suit_type, color)
    local suit_list = self:GetBoneSuitCfg(fs_type, suit_type, color)
    if IsEmptyTable(suit_list) then
        return {}
    end

    local suit_data = {suit_title = "", suit_score = 0, info_list = {}}

    local grey_color = COLOR3B.GRAY
    local same_num_color = COLOR3B.D_GREEN

    local attr_id, attr_value, is_act = 0, 0, false
    local act_max_num, max_need_num = 0, 0
    local tip_skill_cfg_list = FightSoulWGData.Instance:GetTipSkillCfgList(fs_type)

    for k,v in pairs(suit_list) do
        is_act = self:GetBoneSuitIsAct(fs_type, suit_type, color, v.same_num)

        if is_act and v.same_num > act_max_num then
            act_max_num = v.same_num
        end

        if v.same_num > max_need_num then
            max_need_num = v.same_num
        end

        local data = {}
        data.is_act = is_act
        data.skill_level = color - 2
        data.same_num = v.same_num
        data.same_num_str = string.format(Language.F2Tip.ActNumDesc, v.same_num)
        data.same_num_str = data.same_num_str .. "  " .. Language.FightSoul.PzXiaoGuo[color]
        data.same_num_str = ToColorStr(data.same_num_str, is_act and same_num_color or grey_color)
        local tip_skill_cfg = {}

        for i=1,3 do
            attr_id = v["attr_id_" .. i]
            attr_value = v["attr_value_" .. i]
            if attr_id and attr_value and attr_id > 0 and attr_value > 0 then
                if is_act then
                    suit_data.suit_score = suit_data.suit_score + TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_id, attr_value)
                end
                tip_skill_cfg[#tip_skill_cfg + 1] = tip_skill_cfg_list[v.same_num][i]
            end
        end

        if v.skill_effect_id > 0 then
            for i=1,suit_type do
                tip_skill_cfg[#tip_skill_cfg + 1] = tip_skill_cfg_list[v.same_num][i]
            end
        end

        data.tip_skill_cfg = tip_skill_cfg

        table.insert(suit_data.info_list, data)
    end

    -- 套装标题(收集进度)
    local name = self:GetBoneSuitName(suit_type, fs_type)
    local suit_name = string.format(Language.F2Tip.SuitDesc, name)
    suit_data.suit_title = string.format("%s [ %s/%s ]", suit_name, act_max_num, max_need_num)

    return suit_data
end

function FightSoulWGData:GetPassiveSkillTipsData(suit_cfg, skill_index)
    if IsEmptyTable(suit_cfg) then
        return
    end

    local temp_data = {score = 0, desc_list = {}}
    local str_color = "#C4B8A8FF"
    local attr_id = suit_cfg["attr_id_" .. skill_index]
    local attr_value = suit_cfg["attr_value_" .. skill_index]

    if attr_id and attr_value and attr_id > 0 and attr_value > 0 then
        local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
        local base_attribute = AttributePool.AllocAttribute()
        base_attribute[attr_str] = attr_value
        temp_data.score = AttributeMgr.GetCapability(base_attribute)
        if EquipmentWGData.Instance:GetAttrIsPer(attr_id) then
            attr_value = attr_value / 100 .. "%"
        end
        local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_id, true)

        local desc_str = string.format("<color=%s>%s<color=%s>%s</color></color>",
                                str_color, attr_name,
                                COLOR3B.D_GREEN, attr_value)

        table.insert(temp_data.desc_list, desc_str)
    end

    if suit_cfg.skill_effect_id > 0 then
        local is_wear, slot = self:GetTypeIsWear(suit_cfg.sixiang_type)
        local desc_str = ""
        local fs_data = self:GetFightSoulSlot(slot)
        if fs_data and fs_data:GetIsWear() then
            local active_skill, skill_level = fs_data:GetSkillIdAndLevel()
            desc_str = self:GetAutoSkillActDesc(active_skill, skill_level, suit_cfg.skill_effect_id)
        else
            local cfg = FightSoulWGData.Instance:GetFightSoulTypeCfg(suit_cfg.sixiang_type)
            local show_active_skill = cfg and cfg.active_skill_list
            desc_str = self:GetAutoSkillActDesc(show_active_skill, 1, suit_cfg.skill_effect_id)
        end

        table.insert(temp_data.desc_list, desc_str)
    end

    return temp_data
end

-- 【魂骨】获取部位可装备魂骨列表
function FightSoulWGData:GetBonePartCanWearList(fight_soul_type, bone_part)
    local meet_list = {}
    local cache_list = self:GetBonePartCanWearCache(fight_soul_type, bone_part)
    if IsEmptyTable(cache_list) then
    	return meet_list
    end

    for k, v in pairs(cache_list) do
        local data = {}
        data.star = v.star
        data.suit_type = v.suit_type
        data.bone_data = v
        table.insert(meet_list, data)
    end

    if not IsEmptyTable(meet_list) then
        table.sort(meet_list, SortTools.KeyUpperSorters("star", "suit_type"))
    end

    return meet_list
end

-- 【魂骨】是否有比当前更好的魂骨 (同套装比较品质、星级)
function FightSoulWGData:GetIsBetterBone(bone_data)
    if bone_data == nil then
		return false
	end

    local fs_is_wear, slot = self:GetTypeIsWear(bone_data.fight_soul_type)
    -- 战魂未上阵
    if not fs_is_wear then
        return false
    end

    local wear_data = self:GetWearBoneDataBySlotPart(slot, bone_data.bone_part)
    -- 未装备魂骨
    if IsEmptyTable(wear_data) then
    	return true
    end

    -- 同套装的比较星级
    if bone_data.suit_type == wear_data.suit_type and bone_data.star > wear_data.star then
        return true
    -- 同星级的比较套装
    elseif bone_data.star == wear_data.star and bone_data.suit_type > wear_data.suit_type then
        return true
    -- 套装星级都高
    elseif bone_data.suit_type > wear_data.suit_type and bone_data.star > wear_data.star then
        return true
    end

    return false
end

-- 【魂骨】获取部位是否有更好魂骨
function FightSoulWGData:GetPartHaveBetterBone(fs_type, part)
    local can_wear_list = self:GetBonePartCanWearCache(fs_type, part)
    for k,v in pairs(can_wear_list) do
        if self:GetIsBetterBone(v) then
            return true
        end
    end

    return false
end

-- 【魂骨】获取阵位是否有更好魂骨
function FightSoulWGData:GetSlotHaveBetterBone(slot)
    local slot_data = self:GetFightSoulSlot(slot)
    if IsEmptyTable(slot_data) then
        return false
    end

    if slot_data:IsLock() or not slot_data:GetIsWear() then
        return false
    end

    local fs_type = slot_data:GetType()
    local part_list = self:GetBonePartListBySlot(slot)
    if IsEmptyTable(part_list) then
    	return false
    end

    for part,v in pairs(part_list) do
        if self:GetPartHaveBetterBone(fs_type, part) then
            return true
        end
    end

    return false
end

-- 【魂骨】获取阵位装备的所有基础属性
function FightSoulWGData:GetBoneSlotBaseAttrList(slot)
    local part_list = self:GetBonePartListBySlot(slot)
    local attr_list = {}
    local had_data_flag = false
    if IsEmptyTable(part_list) then
    	return attr_list
    end

    local base_attribute = AttributePool.AllocAttribute()
    local attr_str
    for k,v in pairs(part_list) do
        local data = v.bone_data
        if not IsEmptyTable(data) then
            local attr_cfg = self:GetBoneAttrCfg(data.bone_part, data.color, data.star)
            if attr_cfg ~= nil then
                for attr, value in pairs(attr_cfg) do
                    attr_str = AttributeMgr.GetAttributteKey(attr)
                    if base_attribute[attr_str] ~= nil and value > 0 then
                        base_attribute[attr_str] = base_attribute[attr_str] + value
                    end
                end
            end

            had_data_flag = true
        end
    end

    if had_data_flag then
        for k, v in pairs(base_attribute) do
            if v > 0 then
                local data = {}
                data.attr_name = k
                data.attr_value = v
                data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
                table.insert(attr_list, data)
            end
        end
    end

    if not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

    return attr_list
end

-- 【魂骨】单个部位的强化红点
function FightSoulWGData:GetBonePartStrengthRemind(slot, part)
    local part_data = self:GetBonePartDataBySlotPart(slot, part)
    if IsEmptyTable(part_data) then
    	return false
    end

    if IsEmptyTable(part_data.bone_data) then
    	return false
    end

    local is_max = self:GetBoneStrengthIsMaxLevel(part, part_data.slot_level)
    if is_max then
        return false
    end

    local level_cfg = self:GetBoneStrengthCfg(part, part_data.slot_level + 1)
    if IsEmptyTable(level_cfg) then
    	return false
    end

    if level_cfg.stuff_num > 0 then
        local num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.stuff_id)
        if num >= level_cfg.stuff_num then
            return true
        end
    end

    return false
end

-- 【魂骨】单个槽位的强化红点
function FightSoulWGData:GetBoneSlotStrengthRemind(slot)
    local slot_data = self:GetFightSoulSlot(slot)
    if IsEmptyTable(slot_data) then
        return false
    end

    if slot_data:IsLock() or not slot_data:GetIsWear() then
        return false
    end

    local part_lsit = self:GetBonePartListBySlot(slot)
    if IsEmptyTable(part_lsit) then
    	return false
    end

    for part,v in pairs(part_lsit) do
        if self:GetBonePartStrengthRemind(slot, part) then
            return true
        end
    end

    return false
end

-- 【魂骨】列表
function FightSoulWGData:GetBonePartStrengthShowList(slot)
    local part_lsit = self:GetBonePartListBySlot(slot)
    local show_list = {}
    if IsEmptyTable(part_lsit) then
        return show_list
    end

    local sort_weight = 0
    local is_nodata = false
    for k, v in pairs(part_lsit) do
        local data = {}
        data.fs_slot = v.fs_slot
        data.bone_part = v.bone_part
        data.slot_level = v.slot_level
        data.bone_data = v.bone_data
        is_nodata = IsEmptyTable(v.bone_data)
        data.sort_weight = is_nodata and v.bone_part + 999 or v.bone_part
        table.insert(show_list, data)
    end

    if not IsEmptyTable(show_list) then
    	table.sort(show_list, SortTools.KeyLowerSorter("sort_weight"))
    end

    return show_list
end

-- 【魂骨】获取魂骨强化跳红点
function FightSoulWGData:GetBoneStrengthJumpIndex(wear_list)
    if IsEmptyTable(wear_list) then
        return 1
    end

    for k,v in ipairs(wear_list) do
        if self:GetBonePartStrengthRemind(v.fs_slot, v.bone_part) then
            return k
        end
    end

    return 1
end

-- 【魂骨】获取阵位红点
function FightSoulWGData:GetBoneSingleSlotRemind(slot, ignore_better)
    local slot_data = self:GetFightSoulSlot(slot)
    local jump_uplock = false
	if IsEmptyTable(slot_data) then
		return false, jump_uplock
	end

    -- 等级限制
    if slot_data:IsLock() then
        -- 道具解锁
        if self:GetDropToUnlockRemind(slot) then
            return true, true
        else
            return false, jump_uplock
        end
    end

    -- 出战提醒
    -- if self:GetSingleOutFightRemind(slot) then
    --     return true, jump_uplock
    -- end

    -- 更好的四象
    if not ignore_better then
        if self:GetFightSoulCanWearByType(slot) then
            return true, jump_uplock
        end
    end

    -- 有更好魂骨
    if self:GetSlotHaveBetterBone(slot) then
        return true, jump_uplock
    end

    -- 强化
    if self:GetBoneSlotStrengthRemind(slot) then
        return true, jump_uplock
    end

    -- 融合
    if self:GetFSTypeBoneComposeRemind(slot_data:GetType()) then
        return true, jump_uplock
    end

    return false, jump_uplock
end

-- 魂装穿戴红点
function FightSoulWGData:GetBoneBetterBoneRemind()
    for slot,v in pairs(self.bone_slot_info) do
        if self:GetSlotHaveBetterBone(slot) then
            self:BoneToBeStrengthen(MAINUI_TIP_TYPE.FIGHT_SOUL_BONE_WEAR, 1)
            return true
        end
    end

    self:BoneToBeStrengthen(MAINUI_TIP_TYPE.FIGHT_SOUL_BONE_WEAR, 0)
    return false
end

-- 魂装强化红点
function FightSoulWGData:GetBoneStrengthRemind()
    for slot,v in pairs(self.bone_slot_info) do
        if self:GetBoneSlotStrengthRemind(slot) then
            self:BoneToBeStrengthen(MAINUI_TIP_TYPE.FIGHT_SOUL_BONE_UPLV, 1)
            return true
        end
    end

    self:BoneToBeStrengthen(MAINUI_TIP_TYPE.FIGHT_SOUL_BONE_UPLV, 0)
    return false
end

-- 魂装融合红点
function FightSoulWGData:GetBoneComposeRemind()
    -- 已穿戴的魂装可融合,变强提醒
    local to_strength = false
    for slot,part_list in pairs(self.bone_slot_info) do
        for k,v in pairs(part_list) do
            if self:GetSingleBoneComposeRemind(v.bone_data) then
                to_strength = true
                self:BoneToBeStrengthen(MAINUI_TIP_TYPE.FIGHT_SOUL_BONE_COM, 1)
                break
            end
        end

        if to_strength then
            break
        end
    end

    if not to_strength then
        self:BoneToBeStrengthen(MAINUI_TIP_TYPE.FIGHT_SOUL_BONE_COM, 0)
    else
        return true
    end

    -- 红点
    for slot,v in pairs(self.bone_slot_info) do
        local slot_data = self:GetFightSoulSlot(slot)
        if not IsEmptyTable(slot_data) and not slot_data:IsLock() then
            if self:GetFSTypeBoneComposeRemind(slot_data:GetType()) then
                return true
            end
        end
    end

    return false
end

-- 魂装其他红点
function FightSoulWGData:GetBoneOtherRemind()
    for slot,v in pairs(self.bone_slot_info) do
        -- -- 出战提醒
        -- if self:GetSingleOutFightRemind(slot) then
        --     return true
        -- end

        -- 更好的四象
        if self:GetFightSoulCanWearByType(slot) then
            return true
        end

        -- 道具解锁
        if self:GetDropToUnlockRemind(slot) then
            return true
        end
    end

    return false
end

function FightSoulWGData:GetBoneRemind()
    if not FunOpen.Instance:GetFunIsOpenedByTabName("fight_soul_bone") then
        return 0
    end

    if self:GetBoneBetterBoneRemind() then
        return 1
    end

    if self:GetBoneStrengthRemind() then
        return 1
    end

    if self:GetBoneComposeRemind() then
        return 1
    end

    if self:GetBoneOtherRemind() then
        return 1
    end

    return 0
end

function FightSoulWGData:BoneToBeStrengthen(key, remind)
	MainuiWGCtrl.Instance:InvateTip(key, remind, function ()
		FightSoulWGCtrl.Instance:OpenFightSoulView(TabIndex.fight_soul_bone)
        return true
	end)
end


-- 【魂骨】获取穿戴的最低品质权重
function FightSoulWGData:GetWearBoneStarWeightByType(fs_type)
    local star_weight = {}
    local _, slot = self:GetTypeIsWear(fs_type)
    local part_list = self:GetBonePartListBySlot(slot)
    for k,v in pairs(part_list) do
        star_weight[v.bone_part] = 0
        if v.bone_data and v.bone_data.star then
            star_weight[v.bone_part] = 15 - v.bone_data.star
        end
    end

    return star_weight
end

function FightSoulWGData:GetSingleWearBoneStarWeightByType(fs_type, bone_part)
    local _, slot = self:GetTypeIsWear(fs_type)
    local part_list = self:GetBonePartListBySlot(slot)
    local bone_data = part_list[bone_part] and part_list[bone_part].bone_data or {}
    return bone_data.star and 15 - bone_data.star or 0
end

-- 【魂骨】批量融合
-- 需求：优先选穿戴部位品质最低的作为融合目标，再从背包未选为材料中选择（优先选穿戴品质最低的部位）
-- 获取批量融合需要的背包数据  排序：穿戴 > 部位(穿戴品质低部位的优先) > 品质 > 星级 > 套装类型(小到大)
-- 【注意】以下实现默认为融合消耗是同品质材料
function FightSoulWGData:GetBoneBatchBagListByType(fs_type)
    local show_bag_list = {}
    local can_select_list = {}
    local max_part_num = FIGHT_SOUL_BONE_TYPE.MAX
    local max_suit_num = 4
    local max_bag_num = self:GetBoneBagMaxNum()
    local wear_weight, star_weight, suit_type_weight, part_weight
	local bag_list = self:GetFSTypeBoneBagListByType(fs_type)
    local offset_star = 0
    local part_star_weight = self:GetWearBoneStarWeightByType(fs_type)

	for k,v in pairs(bag_list) do
		local data = {}
		data.item_data = v
		-- 排序
		local is_wear = self:GetBoneBagIndexIsWear(v.bag_index)
        offset_star = part_star_weight[v.bone_part] or 0
        wear_weight = is_wear and 100000000 or 0
        part_weight = (offset_star * 10 + max_part_num - v.bone_part + 1) * 100000
        star_weight = is_wear and 0 or v.star * 10000
        suit_type_weight = is_wear and 0 or (max_suit_num - v.suit_type) * 1000
		data.bag_sort_index = wear_weight + part_weight + star_weight + suit_type_weight + (max_bag_num - v.bag_index)
		data.can_select = is_wear and FIGHT_SOUL_BONE_BATCH_DATA_STATE.IS_WEAR or FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_SELECT
        data.suit_sort_index = v.suit_type * 1000 + (max_bag_num - v.bag_index)
		table.insert(show_bag_list, data)

        if not is_wear then
            table.insert(can_select_list, data)
        end
	end

	if not IsEmptyTable(show_bag_list) then
		table.sort(show_bag_list, SortTools.KeyUpperSorter("bag_sort_index"))
	end

    if not IsEmptyTable(can_select_list) then
        table.sort(can_select_list, SortTools.KeyUpperSorter("suit_sort_index"))
    end

	return show_bag_list, can_select_list
end

-- 【魂骨】获取批量展示列表
function FightSoulWGData:GetBatchShowListByType(fs_type)
    local show_compose_list = {}
    local bag_list, can_select_list = self:GetBoneBatchBagListByType(fs_type)
    if IsEmptyTable(bag_list) or IsEmptyTable(can_select_list) then
    	return show_compose_list
    end

    local max_batch_num = FIGHT_SOUL_NUM.MAX_BATCH_NUM

    for k,v in ipairs(bag_list) do
        if not IsEmptyTable(can_select_list) then
            local temp_data = self:GetBatchSingleShowData(v, can_select_list)
            if not IsEmptyTable(temp_data) then
                table.insert(show_compose_list, temp_data)
            end
        end

        if #show_compose_list >= max_batch_num then
            break
        end
    end

    if not IsEmptyTable(show_compose_list) then
        table.sort(show_compose_list, SortTools.KeyUpperSorter("sort_index"))
    end

    return show_compose_list
end

function FightSoulWGData:GetBatchSingleShowData(bag_data, can_select_list)
    local stuff_list = {}
    if bag_data == nil or IsEmptyTable(can_select_list)
    or bag_data.can_select == FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_NOT_SELECT then
        return stuff_list
    end

    local item_data = bag_data.item_data
    local compose_cfg = self:GetBoneComposeCfg(item_data.color, item_data.star)
    if IsEmptyTable(compose_cfg) then
        return stuff_list
    end

    local need_fs_type = item_data.fight_soul_type
    local same_type_num = compose_cfg.same_part_num
    local same_type_color = item_data.color
    local same_type_star = item_data.star
    local same_type_suit_type = item_data.suit_type
    local same_bone_part = item_data.bone_part
    local max_part_num = FIGHT_SOUL_BONE_TYPE.MAX

    local nosame_type_num = compose_cfg.same_sixiang_type_num
    local nosame_type_color = compose_cfg.same_sixiang_type_color
    local nosame_type_star = compose_cfg.same_sixiang_type_star

    -- 优先选一件比当前融合更高类型套装的魂装
    local select_same_num, select_nosame_num = 0, 0
    local need_select_height = same_type_suit_type < can_select_list[1].item_data.suit_type
    local can_select_state = FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_SELECT
    local can_not_select_state = FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_NOT_SELECT
    bag_data.can_select = FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_NOT_SELECT

    local function reset_data(list)
        if IsEmptyTable(list) then
        	return {}
        end

        for k,v in ipairs(list) do
            v.can_select = FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_SELECT
        end
        return {}
    end

    -- 相同
    if need_select_height then
        for k,v in ipairs(can_select_list) do
            if v.can_select == can_select_state and v.item_data.fight_soul_type == need_fs_type
            and v.item_data.bone_part == same_bone_part and v.item_data.star == same_type_star
            and v.item_data.color == same_type_color and v.item_data.suit_type > same_type_suit_type then
                need_select_height = false
                v.can_select = can_not_select_state
                select_same_num = select_same_num + 1
                table.insert(stuff_list, v)
                break
            end
        end
    end

    if select_same_num < same_type_num then
        for i = #can_select_list, 1, -1 do
            local data = can_select_list[i]
            if data.can_select == can_select_state and data.item_data.fight_soul_type == need_fs_type and data.item_data.bone_part == same_bone_part
            and data.item_data.star == same_type_star and data.item_data.color == same_type_color then
                data.can_select = can_not_select_state
                select_same_num = select_same_num + 1
                table.insert(stuff_list, data)
                if select_same_num >= same_type_num then
                    break
                end
            end
        end
    end

    -- 非相同, 任意类型材料
    if select_same_num < same_type_num then
        bag_data.can_select = FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_SELECT
        return reset_data(stuff_list)
    end

    if need_select_height then
        for k,v in ipairs(can_select_list) do
            if v.can_select == can_select_state and v.item_data.fight_soul_type == need_fs_type
            and v.item_data.star == nosame_type_star and v.item_data.color == nosame_type_color
            and v.item_data.suit_type > same_type_suit_type then
                v.can_select = can_not_select_state
                select_nosame_num = select_nosame_num + 1
                need_select_height = false
                table.insert(stuff_list, v)
                break
            end
        end
    end

    if select_nosame_num < nosame_type_num then
        for i = #can_select_list, 1, -1 do
            local data = can_select_list[i]
            if data.can_select == can_select_state and data.item_data.fight_soul_type == need_fs_type
            and data.item_data.star == nosame_type_star and data.item_data.color == nosame_type_color then
                data.can_select = can_not_select_state
                select_nosame_num = select_nosame_num + 1
                table.insert(stuff_list, data)
                if select_nosame_num >= nosame_type_num then
                    break
                end
            end
        end
    end

    if #stuff_list < same_type_num + nosame_type_num then
        bag_data.can_select = FIGHT_SOUL_BONE_BATCH_DATA_STATE.CAN_SELECT
        return reset_data(stuff_list)
    end

    local temp_data = {}
    temp_data.is_select = true
    local is_wear = self:GetBoneBagIndexIsWear(item_data.bag_index)
    local wear_wieght = is_wear and 10000000 or 0
    local part_star_weight = self:GetSingleWearBoneStarWeightByType(need_fs_type, same_bone_part)
    local part_weight = (part_star_weight * 10 + max_part_num - same_bone_part + 1) * 1000
    temp_data.sort_index = wear_wieght + part_weight + same_type_star
    temp_data.cur_data = item_data

    local max_suit = same_type_suit_type
    local temp_stuff_list = {}
    for k,v in ipairs(stuff_list) do
        if v.item_data.suit_type > max_suit then
            max_suit = v.item_data.suit_type
        end

        table.insert(temp_stuff_list, v.item_data)
    end

    temp_data.stuff_list = temp_stuff_list

    local target_item_id = FightSoulWGData.GetBoneItemIdByParam(need_fs_type, same_bone_part, max_suit, compose_cfg.target_color)
    temp_data.target_data = {item_id = target_item_id, is_bind = 0, star = compose_cfg.target_star,
		                    color = compose_cfg.target_color, fight_soul_type = need_fs_type,
		                    bone_part = same_bone_part, suit_type = max_suit,}

    for i = #can_select_list, 1, -1 do
        if can_select_list[i].can_select == can_not_select_state then
            table.remove(can_select_list, i)
        end
    end

    return temp_data
end
