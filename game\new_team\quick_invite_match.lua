QuickInviteMatch = QuickInviteMatch or BaseClass(SafeBaseView)

local DownCountTime = 10
function QuickInviteMatch:__init()
	self.view_name = "QuickInviteMatch"
	self.view_layer = UiLayer.MainUIHigh
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_quick_invite_match")

    self.running_timer = nil
	self.cur_times = 0
    self.cur_invite_match_info = nil
end

function QuickInviteMatch:__delete()
end

function QuickInviteMatch:ReleaseCallBack()
    self:ClearTimer()
end

function QuickInviteMatch:LoadCallBack()
	self.node_list["IgnoreBtn"].button:AddClickListener(BindTool.Bind(self.OnReceiveHandler, self, 1))
	self.node_list["JoinBtn"].button:AddClickListener(BindTool.Bind(self.OnReceiveHandler, self, 0))
end

function QuickInviteMatch:OpenCallBack()
	
end

function QuickInviteMatch:OnFlush()
	if self.running_timer then
		return
	end

    self:FlushView()
end

function QuickInviteMatch:FlushView()
	if self.running_timer then
		return
	end

    local match_info = NewTeamWGData.Instance:GetQuickInviteMatchTeamInfo()
	if match_info == nil or IsEmptyTable(match_info) then
		self:ClearTimer()
		self:Close()
		return
	end

	self.cur_invite_match_info = match_info
	self.node_list["inviter_name"].text.text = string.format(Language.NewTeam.QuickInviter, match_info.leader_info.name)
    local team_target = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(match_info.team_type, match_info.team_fb_mode)


	self.node_list["target"].text.text = string.format(Language.NewTeam.QuickInviteMatchTypeName, team_target.team_type_name or "")
	self.node_list["IgnoreBtnText"].text.text = string.format(Language.NewTeam.IgnoreStr, DownCountTime)
    self.node_list.toggle.toggle.isOn = false
    self.node_list.country_icon:SetActive(false)

	if not self.running_timer then
		self.running_timer = GlobalTimerQuest:AddTimesTimer(function()
			self.cur_times = self.cur_times + 1

			if not self:IsLoaded() or not self:IsOpen() then
				self:ClearTimer()
				return
			end

			self.node_list["IgnoreBtnText"].text.text = string.format(Language.NewTeam.IgnoreStr, tostring(DownCountTime - self.cur_times))
            if self.cur_times >= DownCountTime then
                self:Close()
			end
		end, 1, DownCountTime)
	end
end

function QuickInviteMatch:OnReceiveHandler(is_received)
    if is_received == 0 then
        self:SendJoinMatch()
    else
        self:SendInviteUserTransmitRet(is_received)
    end
	self:ClearTimer()
    self:Close()
end

function QuickInviteMatch:SendInviteUserTransmitRet(is_received)
	if self.cur_invite_match_info == nil then
		return
    end

    self.is_record_refuse_flag = self.node_list.toggle.toggle.isOn and 1 or 0
    local inviter_uid = self.cur_invite_match_info.leader_info.uid
	SocietyWGCtrl.Instance:SendInviteUserTransmitRet(inviter_uid, is_received, self.is_record_refuse_flag)
end

function QuickInviteMatch:SendJoinMatch()
    if self.cur_invite_match_info == nil then
		return
    end

    local inviter_uid = self.cur_invite_match_info.leader_info.uid
    NewTeamWGCtrl.Instance:SendAutoMatchTeam(0, self.cur_invite_match_info.team_type, self.cur_invite_match_info.team_fb_mode, 2, nil, inviter_uid)
end

function QuickInviteMatch:ClearTimer()
	self.cur_times = 0
    if self.running_timer then
        GlobalTimerQuest:CancelQuest(self.running_timer)
        self.running_timer = nil
    end
end