CultivationGetView = CultivationGetView or BaseClass(SafeBaseView)

function CultivationGetView:__init()
	self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:AddViewResource(0, "uis/view/cultivation_ui_prefab", "layout_cultivation_get_view")
end

function CultivationGetView:LoadCallBack()
    -- self.node_list.desc_privilege_tip.text.text = Language.Cultivation.PrivilegeViewTips

    if not self.get_list then
        self.get_list = AsyncListView.New(CultivationGetItemRender, self.node_list.get_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_buff, BindTool.Bind(self.OnClickBuff, self))
    XUI.AddClickEventListener(self.node_list.btn_onekey, BindTool.Bind(self.OnClickBtnOnekey, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.Close, self))
    
end

function CultivationGetView:ShowIndexCallBack()
    -- self:PlayTween()
end

function CultivationGetView:ReleaseCallBack()
    if self.get_list then
        self.get_list:DeleteMe()
        self.get_list = nil
    end

end

function CultivationGetView:OnFlush()
    local item_list = CultivationWGData.Instance:GetCultivationItemList()
    -- 无限列表
	if self.get_list then
		self.get_list:SetDataList(item_list)
    end

    -- 当前等级的经验和上限
	local cur_xiuwei_level_cfg = CultivationWGData.Instance:GetCurXiuWeiLevelCfg()
	if cur_xiuwei_level_cfg == nil then
		print_error("缺少数据,检查数据表配置")
		return 
	end
    local next_level_need_exp = cur_xiuwei_level_cfg.need_exp
    local cur_exp = CultivationWGData.Instance:GetXiuWeiExp()
	
    self.node_list.text_cultivation_value.text.text = string.format(Language.Cultivation.XiuWeiValue,cur_exp,next_level_need_exp)
	self.node_list.slider_daonian.slider.value = cur_exp / next_level_need_exp

    -- 修为效率
	local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
	self.node_list.text_number.text.text = number
	self.node_list.text_rate.text.text = string.format(Language.Cultivation.ExpAddTotalStr3, rate)

    local buff_remind = CultivationWGData.Instance:GetBuffRemind()
	self.node_list.buff_remind:CustomSetActive(buff_remind)

    local is_ten = CultivationWGData.Instance:GetCultivationGetIsTen()
    self.node_list.image_onekey:CustomSetActive(is_ten)
end

-- 修为效率
function CultivationGetView:OnClickBuff()
	ViewManager.Instance:Open(GuideModuleName.BiZuo, TabIndex.bizuo_cultivation)
end

-- 是否一次10个
function CultivationGetView:OnClickBtnOnekey()
	local is_ten = CultivationWGData.Instance:GetCultivationGetIsTen()
	CultivationWGData.Instance:SetCultivationGetIsTen(not is_ten)

    self.node_list.image_onekey:CustomSetActive(not is_ten)
end
-------------------------------------------------------------------------------------------------

CultivationGetItemRender = CultivationGetItemRender or BaseClass(BaseRender)

function CultivationGetItemRender:LoadCallBack()

    self.item_cell = ItemCell.New(self.node_list["item_pos"])
    XUI.AddClickEventListener(self.node_list.btn_use,BindTool.Bind(self.OnClickBtnUse, self))
    self.can_use = false
end

function CultivationGetItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function CultivationGetItemRender:OnFlush()
	local data = self:GetData()
	if not data then
        self.view:CustomSetActive(false)
		return
	end 
	

	if data.item_id == 0 or data.item_id == 1 or data.item_id == 2 then
        self.node_list.text_name.text.text = Language.Cultivation.GetTargetName[data.item_id]
        self.node_list.btn_text.text.text = Language.Cultivation.GetViewBtn
        self.node_list.img_bg:CustomSetActive(true)
        self.node_list.item_pos:CustomSetActive(false)
        local bundle, asset = ResPath.GetMainUIIcon("a3_zjm_icon_rc")

        local desc_str = Language.Cultivation.GetTargetDesc[data.item_id]
        if data.item_id == 0 then
            self.node_list.text_desc.text.text = desc_str
        elseif data.item_id == 1 then
            local other_cfg = CultivationWGData.Instance:GetXiuWeiOtherCfg()
            if other_cfg.boss_xiuwei then
                -- 修为效率
		        local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
                local real_num = math.floor((other_cfg.boss_xiuwei+number)*(1+rate/100))
                self.node_list.text_desc.text.text = string.format(desc_str,real_num)
            end
        elseif self.data.item_id == 2 then
            bundle, asset = ResPath.GetMainUIIcon("a3_zjm_icon_fb")
            self.node_list.text_desc.text.text = desc_str
        end

        self.node_list.Icon.image:LoadSprite(bundle, asset)

        local btn_bundle, btn_asset = ResPath.GetCommonButton("a3_ty_btn_9")
        self.node_list.btn_use.image:LoadSprite(btn_bundle, btn_asset)
    else
        local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)

        self.item_cell:SetFlushCallBack(function ()
            self.item_cell:SetRightBottomColorText(item_num)
            self.item_cell:SetRightBottomTextVisible(true)
        end)

        local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
        local count_str = ""  -- 限制数量
        local can_use_num = 0 --可使用数量(拥有的和限制数量取最小值)
        if item_cfg then
            self.item_cell:SetData({ item_id = self.data.item_id })
            
            local use_count = CultivationWGData.Instance:GetDanItemNum(self.data.index)
            if item_cfg.use_daytimes ~= 0 then
                local use_num = item_cfg.use_daytimes - use_count
                if use_num > 0 then
                    self.can_use = true
                    count_str = string.format(Language.Cultivation.GetViewCount, use_num)
                    can_use_num = math.min(use_num, item_num)
                else
                    self.can_use = false
                    count_str = string.format(Language.Cultivation.GetViewCount, 0)
                end
            else
                self.can_use = true
                count_str = "" --Language.Cultivation.GetViewCountTip
                can_use_num = item_num
            end
            self.node_list.text_name.text.text = item_cfg.name..count_str

            local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
            if item_cfg and item_cfg.param1 then
                local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
                self.node_list.text_desc.text.text = string.format(Language.Cultivation.CanGet, math.floor((item_cfg.param1 + number)* (1+rate/100)) ) --* can_use_num 显示1颗的值
            else
                self.node_list.text_desc.text.text = string.format(Language.Cultivation.CanGet, 0)
            end
            
        end
        self.node_list.remind:CustomSetActive(can_use_num>0)
    end
	
end

-- 领取
function CultivationGetItemRender:OnClickBtnUse()
	if self.data then
        if self.data.item_id == 0 then
            --打开日常界面
            if BiZuoWGCtrl.Instance then
                BiZuoWGCtrl.Instance:Open()
            end
        elseif self.data.item_id == 1 then
            -- 打开boss界面
            ViewManager.Instance:Open(GuideModuleName.Boss)
        elseif self.data.item_id == 2 then
            ViewManager.Instance:Open(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin)
        else
            if self.can_use then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
                if item_num > 0 then
                    local bag_index = ItemWGData.Instance:GetItemIndex(self.data.item_id)
                    local is_ten = CultivationWGData.Instance:GetCultivationGetIsTen()
                    BagWGCtrl.Instance:SendUseItem(bag_index, is_ten and math.min(10,item_num) or 1)
                else
                    self.item_cell:OnClick()
                end
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.IsmaxUseTimes)
            end
        end
	end
end
