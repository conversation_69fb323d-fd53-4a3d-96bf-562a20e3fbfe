TransFerEquipCollectTipsView = TransFerEquipCollectTipsView or BaseClass(SafeBaseView)

local left_width = 228
local equip_item_width = 64

function TransFerEquipCollectTipsView:__init()
    self.view_layer = UiLayer.MainUILow
    self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_equip_collect_tip") 

    self.view_cache_time = 0
end

function TransFerEquipCollectTipsView:__delete()

end

function TransFerEquipCollectTipsView:OpenCallBack()
    MainuiWGCtrl.Instance:ChangePosByTransFerEquipCollect(true)
end

function TransFerEquipCollectTipsView:CloseCallBack()
    MainuiWGCtrl.Instance:ChangePosByTransFerEquipCollect(false)
end

function TransFerEquipCollectTipsView:LoadCallBack()
    if not self.equip_cell_list then
        self.equip_cell_list = {}
        for i = 1, 8 do
            self.equip_cell_list[i] = TransFerEquipCollectTipRender.New(self.node_list['equip_' .. i])
            self.equip_cell_list[i]:SetIndex(i)
        end
    end

    if not self.equip_fly_icon then
        self.equip_fly_icon = {}
        for i = 1, 8 do
            self.equip_fly_icon[i] = TransFerEquipCollectTipFlyIcon.New(self.node_list["fly_item"..i])
            self.equip_fly_icon[i]:SetIndex(i)
            self.equip_fly_icon[i]:SetParntNode(self.node_list.root_obj.rect)
        end
    end

    XUI.AddClickEventListener(self.node_list["btn_commit_task"], BindTool.Bind(self.OnClickBtnCommitTask, self))
    

    self.datachange_callback = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)

    self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))
    self.bg_wight = 740
end

function TransFerEquipCollectTipsView:ReleaseCallBack()
    if self.equip_cell_list then
        for k, v in pairs(self.equip_cell_list) do
            v:DeleteMe()
        end
        self.equip_cell_list = nil
    end

    if self.equip_fly_icon then
        for k, v in pairs(self.equip_fly_icon) do
            v:DeleteMe()
        end
        self.equip_fly_icon = nil
    end

    if self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end

    if self.datachange_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
		self.datachange_callback = nil
	end

    if self.tween1 then
        self.tween1:Kill()
        self.tween1 = nil
    end

    if self.tween2 then
        self.tween2:Kill()
        self.tween2 = nil
    end

    self.show_suit_index = nil
    self.bg_wight = 0
end

function TransFerEquipCollectTipsView:OnFlush()
    if not self.show_suit_index then
        self:FlushEquipItemList()
    end
end

function TransFerEquipCollectTipsView:FlushEquipItemList(need_ami)
    self.show_suit_index = TransFerWGData.Instance:GetCurShowSuitIndex()
    if self.show_suit_index < 0 then
        return
    end

    local equip_item_list = TransFerWGData.Instance:GetEquipItemListBySuitIndex(self.show_suit_index)
    local equip_star_list = TransFerWGData.Instance:GetEquipStarListBySuitIndex(self.show_suit_index)
    local suit_cfg = TransFerWGData.Instance:GetEquipCollectCfgBySuit(self.show_suit_index)
    local equip_count = 0
    for i, v in ipairs(self.equip_cell_list) do
        local item_id = tonumber(equip_item_list[i]) or 0
        local need_star = tonumber(equip_star_list[i]) or 3
        self.node_list['equip_' .. i]:SetActive(item_id > 0)
        if item_id > 0 then
            equip_count = equip_count + 1
            v:SetData({item_id = item_id, suit_index = self.show_suit_index, index = i, need_star = need_star, 
                        sub_type = suit_cfg and suit_cfg.sub_type or 1, child_type = suit_cfg and suit_cfg.child_type or 1,
                        jump_prof = suit_cfg and suit_cfg.jump_prof or 1})
        else
            v:SetData({})
        end
    end

    local bg_canvas_group = self.node_list.bg.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
    self.bg_wight = left_width + (equip_item_width * equip_count)
    if not need_ami then
        self.node_list.bg.rect.sizeDelta = Vector2(self.bg_wight, 68)
        bg_canvas_group.alpha = 1
    else
        local move_tween_1 = self.node_list.bg.rect:DOSizeDelta(Vector2(self.bg_wight, 68), 0.6)
        local tween_alpah = bg_canvas_group:DoAlpha(0, 1, 0.6)

        if self.tween1 then
            self.tween1:Kill()
            self.tween1 = nil
        end

        self.tween1 = DG.Tweening.DOTween.Sequence()
        self.tween1:Append(move_tween_1)
        self.tween1:Join(tween_alpah)
    end

    self:FlushActiveCount()
end

function TransFerEquipCollectTipsView:FlushActiveCount()
    local active_count, max_count = TransFerWGData.Instance:GetActiveNumAndMax(self.show_suit_index)
    self.node_list.text_num.text.text = string.format("%s/%s", active_count, max_count) 

    local cfg = TransFerWGData.Instance:GetEquipCollectCfgBySuit(self.show_suit_index)

    if cfg and cfg.task_id and cfg.task_id~=0 and  cfg.task_id~="" then
        -- local desc = TaskWGData.Instance:GetTaskProDesById(cfg.task_id)
        -- self.node_list.text_task_desc.text.text = desc
    
        local task_status = TaskWGData.Instance:GetTaskStatus(cfg.task_id)
        self.node_list.task_complete:SetActive(task_status == GameEnum.TASK_STATUS_COMMIT)
        self.node_list.img_desc:SetActive(task_status ~= GameEnum.TASK_STATUS_FINISH)
    else
        self.node_list.task_complete:SetActive(false)
        self.node_list.img_desc:SetActive(false)
    end

end

function TransFerEquipCollectTipsView:MainMenuIconChangeEvent(is_on)
    self.node_list.node_root:CustomSetActive(not is_on)
end

function TransFerEquipCollectTipsView:PlayTipFlyAnim(data)
    if data.suit_index == self.show_suit_index then
        if self.equip_fly_icon and self.equip_fly_icon[data.part] then
            self.equip_fly_icon[data.part]:ReadToPlayFly(data)
        end
    end
    self:Flush()
end

function TransFerEquipCollectTipsView:GetEquipCollectTipCellNode(index)
    if index and not IsEmptyTable(self.equip_cell_list) and self.equip_cell_list[index] then
        local cell = self.equip_cell_list[index]
        local node_obj = cell and cell.root_node
        return node_obj
    end
end

function TransFerEquipCollectTipsView:CompleteEquipCollectTipsFlyAnim(cell_index)
    if self.equip_cell_list and self.equip_cell_list[cell_index] then
        self.equip_cell_list[cell_index]:Flush()
    end

    self:FlushActiveCount()

    GlobalTimerQuest:AddDelayTimer(function()
		self:NeedFlushSuitView()
	end, 1)
end

function TransFerEquipCollectTipsView:NeedFlushSuitView()
    if not self:IsOpen() then
        return
    end
    
    local show_suit_index = TransFerWGData.Instance:GetCurShowSuitIndex()
    if show_suit_index == self.show_suit_index then
        return
    end

    local move_tween_1 = self.node_list.bg.rect:DOSizeDelta(Vector2(400, 100), 0.6)
    local bg_canvas_group = self.node_list.bg.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local tween_alpah = bg_canvas_group:DoAlpha(1, 0, 0.6)

    if self.tween2 then
        self.tween2:Kill()
        self.tween2 = nil
    end

    self.tween2 = DG.Tweening.DOTween.Sequence()
    self.tween2:Append(move_tween_1)
    self.tween2:Join(tween_alpah)

    self.tween2:OnComplete(function ()
        if show_suit_index < 0 then
            self:Close()
        else
            self:FlushEquipItemList(true)
        end
    end)
end

function TransFerEquipCollectTipsView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(change_item_id)
	if item_cfg == nil or item_type == -1 then
		return
	end

    if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
        for i, v in ipairs(self.equip_cell_list) do
            v:FlushComposeInfo()
        end
    end
end

function TransFerEquipCollectTipsView:OnClickBtnCommitTask()
    if self.show_suit_index then
        local cfg = TransFerWGData.Instance:GetEquipCollectCfgBySuit(self.show_suit_index)
        if not IsEmptyTable(cfg) then
            TaskWGCtrl.Instance:DoTask(cfg.task_id)
        end
    end
end

------------------------------------装备列表------------------------
TransFerEquipCollectTipRender = TransFerEquipCollectTipRender or BaseClass(BaseRender)

function TransFerEquipCollectTipRender:__init()
    self.root_node = self.node_list["ph_icon"]
end

function TransFerEquipCollectTipRender:__delete()
   if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.root_node = nil
end

function TransFerEquipCollectTipRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.ph_icon)
    end

    XUI.AddClickEventListener(self.node_list.compose_btn, BindTool.Bind(self.OnClickCompose, self))
end

function TransFerEquipCollectTipRender:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local equip_collect_info = TransFerWGData.Instance:GetEquipCollectInfoByIndex(data.suit_index, data.index)
    local cur_star = equip_collect_info and equip_collect_info.item_star or -1
    local cur_item_id = equip_collect_info and equip_collect_info.item_id or 0
    if cur_star < data.need_star then
        self.item_cell:SetData({item_id = data.item_id, param = {star_level = data.need_star}})
    else
        self.item_cell:SetData({item_id = cur_item_id, param = {star_level = cur_star}})
    end
   
    self.item_cell:SetGraphicGreyCualityBg(cur_star < data.need_star)
    self.item_cell:SetDefaultEff(cur_star >= data.need_star)
    self:FlushComposeInfo()
end

function TransFerEquipCollectTipRender:FlushComposeInfo()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    local equip_collect_info = TransFerWGData.Instance:GetEquipCollectInfoByIndex(data.suit_index, data.index)
    local cur_star = equip_collect_info and equip_collect_info.item_star or -1

    local is_can_compose = EquipmentWGData.Instance:GetCESingleRemindByData(data.item_id, data.need_star)
    self.node_list.compose_btn:SetActive(cur_star < data.need_star and is_can_compose)
end

function TransFerEquipCollectTipRender:OnClickCompose()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    if data.index == 4 or data.index == 7 then
        local tab_index = TabIndex.other_compose_eq_hecheng_three
        ViewManager.Instance:Open(GuideModuleName.Compose, tab_index, "all", {open_param = data.sub_type, to_ui_param = data.child_type})
    else
        local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
        local tab_index = flag and TabIndex.other_compose_eq_hecheng_one or TabIndex.other_compose_eq_hecheng_two
        ViewManager.Instance:Open(GuideModuleName.Compose, tab_index, "all", {open_param = data.sub_type, to_ui_param = data.child_type})
    end
end
---------------------------------------------------------------------------
TransFerEquipCollectTipFlyIcon = TransFerEquipCollectTipFlyIcon or BaseClass(BaseRender)
function TransFerEquipCollectTipFlyIcon:__init()
    self.playing_ani = false
end

function TransFerEquipCollectTipFlyIcon:__delete()
    self.playing_ani = nil

    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function TransFerEquipCollectTipFlyIcon:ResetCanvasGroup()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    if self.canvas_group then
        self.canvas_group.alpha = 1
        self.canvas_group.interactable = true
        self.canvas_group.blocksRaycasts = true
    end

    if self.node_list and self.node_list.view then
        self.node_list.view:SetActive(false)
    end
    self.playing_ani = false
end

function TransFerEquipCollectTipFlyIcon:LoadCallBack()
    self.canvas_group = self.node_list.view.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.view)
        self.item_cell:SetRightTopImageTextActive(false)
    end
end

function TransFerEquipCollectTipFlyIcon:ReadToPlayFly(fly_data)
    if self.playing_ani then
        return
    end

    self:SetData(fly_data)
end

function TransFerEquipCollectTipFlyIcon:SetData(fly_data)
    self.fly_data = fly_data
    if not fly_data or not self.node_list or not self.node_list.view then
        return
    end

    self.pos = {556, 62}
    local btn_node = TransFerWGCtrl.Instance:GetEquipCollectTipCellNode(self.index)
    if btn_node then
    -- --获取指引按钮的屏幕坐标
        local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
        local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, btn_node.rect.position)
        local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
        self.pos = {local_bullet_start_pos_tbl.x, local_bullet_start_pos_tbl.y}
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(fly_data.item_id)
    if item_cfg and item_cfg.icon_id and item_cfg.icon_id > 0 then
        self.node_list.view:SetActive(true)
        self.item_cell:SetActive(true)
        self.item_cell:SetFlushCallBack(function ()
            self.item_cell:SetRightBottomText('')
            self.item_cell:SetRightBottomTextVisible(false)
        end)

        self.item_cell:SetData({item_id = fly_data.item_id, param = {star_level = fly_data.star}})
        self.node_list.view.rect.anchoredPosition = Vector2(150, -200)
        self.node_list.view.rect.localScale = Vector3(0.2, 0.2, 0.2)
        self.playing_ani = true
        self:PlayAni()
    else
        self:ResetCanvasGroup()
    end
end

function TransFerEquipCollectTipFlyIcon:PlayAni()
    self.canvas_group.alpha = 1
    self.canvas_group.interactable = false
    self.canvas_group.blocksRaycasts = false

    local move_tween_1 = self.node_list.view.rect:DOAnchorPos(Vector2(150, -50), 0.5)
    local move_tween_2 = self.node_list.view.rect:DOAnchorPos(Vector2(self.pos[1], self.pos[2]), 1)
    local scale_tween_1 = self.node_list.view.rect:DOScale(Vector3(1, 1, 1), 0.5)
    local scale_tween_2 = self.node_list.view.rect:DOScale(Vector3(0, 0, 0), 0.5)

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    self.sequence = DG.Tweening.DOTween.Sequence()
    self.sequence:Append(move_tween_1)
    self.sequence:Join(scale_tween_1)

    self.sequence:AppendInterval(0.2)
    self.sequence:AppendCallback(function ()
        self.canvas_group:DoAlpha(1, 0.5, 0.5)
    end)

    self.sequence:Append(move_tween_2)
    self.sequence:Append(scale_tween_2)

    self.sequence:OnComplete(function ()
        self.canvas_group.interactable = true
        self.canvas_group.blocksRaycasts = true
        self.node_list.view:SetActive(false)
        self.playing_ani = false
        TransFerWGCtrl.Instance:CompleteEquipCollectTipsFlyAnim(self.index)
    end)
end

function TransFerEquipCollectTipFlyIcon:SetParntNode(node)
    self.parent_node_rect = node
end