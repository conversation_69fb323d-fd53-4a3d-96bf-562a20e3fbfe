OperationActivityView = OperationActivityView or BaseClass(SafeBaseView)

function OperationActivityView:LoadIndexCallBackRechargeRank()
    self.node_list["btn_recharge_rank"].button:AddClickListener(BindTool.Bind(self.OnClickOpenRankView,self))
    self.node_list["btn_recharge"].button:AddClickListener(BindTool.Bind(self.OnClickRecharge,self))
    self.node_list["btn_recharge_fqa"].button:AddClickListener(BindTool.Bind(self.OnClickRechargeFQA,self))
    self.rank_list_view = AsyncListView.New(OpRechargeRankItem, self.node_list["recharge_rank_group"])

	OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.INFO)
	RankWGCtrl.Instance:SendGetPersonRankListReq(GameEnum.PERSON_RANK_TYPE_RA_CHONGZHI_2, nil)
	self:LoadRechageRankBg()

	self.head_cell_list = {}
	self.head_cell_list[1] = BaseHeadCell.New(self.node_list.head_root_1)
	self.head_cell_list[2] = BaseHeadCell.New(self.node_list.head_root_2)
	self.head_cell_list[3] = BaseHeadCell.New(self.node_list.head_root_3)
end

function OperationActivityView:DeleteRechargeRank()
    if self.rank_list_view then
		self.rank_list_view:DeleteMe()
		self.rank_list_view = nil
	end

	if self.head_cell_list then
		for i = 1, 3 do
			if self.head_cell_list[i] then
				self.head_cell_list[i]:DeleteMe()
				self.head_cell_list[i] = nil
			end
		end
	end

end

function OperationActivityView:ShowIndexRechargeRank(index)
	if index == TabIndex.operation_act_recharge_rank then
		self:SetRuleInfoActive(false)
		OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.INFO)
		OpRechargeRankWGCtrl.Instance:SendOpRechargeRankReq(OPRECHARGE_RANK.RANK_INFO)
	end
end

function OperationActivityView:FlushRechargeRank()
	local rank_list = OpRechargeRankWGData.Instance:GetRankCfg()
    if rank_list then
        self.rank_list_view:SetDataList(rank_list)
    end

	local my_total_recharge = OpRechargeRankWGData.Instance:GetRechargeInfo()
	local my_rank_data = OpRechargeRankWGData.Instance:GetMyRankInfo()
	if my_rank_data ~= nil then
		self.node_list["recharge_rank_txt"].text.text = string.format(Language.OperationActivity.RechargeRankTitle2, my_rank_data.rank_index)
	else
		self.node_list["recharge_rank_txt"].text.text = Language.OperationActivity.RechargeNoRank
	end
	
	self.node_list["recharge_rank_total"].text.text = my_total_recharge

	local data_list = OpRechargeRankWGData.Instance:GetRankInfo()
	if data_list then
		for i = 1, 3 do

			
			if data_list[i] and not data_list[i].no_true_rank then
				local rank_data = data_list[i].rank_data

				self.node_list["text_name_"..i].text.text = rank_data.user_name

				local data = {}
				data.role_id = rank_data.user_id
				data.sex = rank_data.sex
				data.prof = rank_data.prof or 1
				self.head_cell_list[i]:SetImgBg(true)
				self.head_cell_list[i]:SetData(data)
			else
				self.node_list["text_name_"..i].text.text = Language.OperationActivity.XuWeiYiDai

				local data = {}
				data.role_id = 0
				data.sex =  1
				data.prof = 1
				self.head_cell_list[i]:SetImgBg(true)
				self.head_cell_list[i]:SetData(data)
			end
		end
	end

	-- self.node_list["recharge_title_desc"].text.text = Language.OperationActivity.RechargeTitleDesc
end

function OperationActivityView:OnClickOpenRankView()
    OpRechargeRankWGCtrl.Instance:OpenRankTipsView()
end

function OperationActivityView:OnClickRecharge()

	ViewManager.Instance:Open(GuideModuleName.Vip)
end

function OperationActivityView:OnClickRechargeFQA()

	RuleTip.Instance:SetContent(Language.OperationActivity.RechargeTitleDesc, Language.OperationActivity.RechargeRankTitleStr)
end


function OperationActivityView:LoadRechageRankBg()

end

OpRechargeRankItem = OpRechargeRankItem or BaseClass(BaseRender)
function OpRechargeRankItem:LoadCallBack()
	self.item_cell_list = {}
end

function OpRechargeRankItem:__delete()
	for _, v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = nil
end

function OpRechargeRankItem:OnFlush()
	if not self.data then
		return
	end
	
	local desc = ""
	if self.data.min_rank == 1 then
		desc = string.format(Language.OperationActivity.RechargeRankTitle2, self.data.min_rank)
	else
		desc = string.format(Language.OperationActivity.RechargeRankTitle3, self.data.min_rank, self.data.max_rank)
	end

	self.node_list["rank_title"].text.text = string.format(Language.OperationActivity.RechargeRankTitle, desc, self.data.limit_chongzhi)

	local item_list = self.item_cell_list
	if #self.data.reward_item > #item_list then
		local cell_parent = self.node_list["reward_group"]
		for i = 0, #self.data.reward_item do
			item_list[i] = item_list[i] or ItemCell.New(cell_parent)
		end
		self.item_list = item_list
	end

	for i = 0, #item_list do
		if self.data.reward_item[i] then
			item_list[i]:SetData(self.data.reward_item[i])
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end
end