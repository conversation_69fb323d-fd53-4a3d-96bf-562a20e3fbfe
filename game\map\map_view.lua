require("game/map/map_local_view")
require("game/map/map_global_view")
require("game/map/map_country_view")

MapView = MapView or BaseClass(SafeBaseView)

function MapView:__init()
	self.can_do_fade = false
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	-- self:LoadConfig()
	local bundle_name = "uis/view/map_ui_prefab"
	local common_bundle_name =  "uis/view/common_panel_prefab"
	--self:AddViewResource(0, bundle_name, "layout_bg")
	-- self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
	self:AddViewResource(TabIndex.local_map, bundle_name, "layout_local")
	self:AddViewResource(TabIndex.global_map, bundle_name, "layout_global")
	self:AddViewResource(0, common_bundle_name, "layout_a3_light_common_top_panel")
	self:AddViewResource(0, bundle_name, "layout_map_effect")
	


	self.map_switch_event_handle = GlobalEventSystem:Bind(OtherEventType.MAP_VIEW_SWITCH, BindTool.Bind1(self.SwitchCallBack, self))
end

function MapView:SwitchCallBack(index)
	self:ClearCountDown()
	self.switch_map_timer = GlobalTimerQuest:AddTimesTimer(function()
		self:ChangeToIndex(index)
	end, 0.5, 1)

	self.node_list.change_effect:SetActive(false)
	self.node_list.change_effect:SetActive(true)

end

function MapView:ClearCountDown()
	if self.switch_map_timer then
        GlobalTimerQuest:CancelQuest(self.switch_map_timer)
        self.switch_map_timer = nil
   	end
end

function MapView:__delete()
	if nil ~= self.map_switch_event_handle then
		GlobalEventSystem:UnBind(self.map_switch_event_handle)
		self.map_switch_event_handle = nil
	end
end

function MapView:ReleaseCallBack()
	self:ClearCountDown()

	if self.local_view then
		self.local_view:DeleteMe()
		self.local_view = nil
	end
	
	if self.global_view then
		self.global_view:DeleteMe()
		self.global_view = nil
	end

	if self.eh_load_quit then
		GlobalEventSystem:UnBind(self.eh_load_quit)
		self.eh_load_quit = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
end

function MapView:LoadCallBack()
	
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:Init(Language.Map.TabGrop)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self.sub_view_list = {}

	MapWGCtrl.Instance:SendSceneLineInfoReq()
		
	self.node_list.change_effect:SetActive(false)
end

function MapView:LoadIndexCallBack(index)
	if index == TabIndex.local_map then
		self.local_view = MapLocalView.New(self.node_list["layout_local_root"])

	elseif index == TabIndex.global_map then
		self.global_view = MapGlobalView.New(self.node_list.layout_global_root)
	end
end

function MapView:ShowIndexCallBack(index)
	if index == TabIndex.local_map then
		self.node_list.title_view_name.text.text = Language.Map.TabGrop2[1]
		self.local_view:IsClose(false)
		self.local_view:ShowIndexCallBack()
		self.local_view:SetOrder(self.sort_order)
		self.local_view:ClearWalkPath()
		--self.local_view:DoOpenTween()
	elseif index == TabIndex.global_map then
		self.node_list.title_view_name.text.text = Language.Map.TabGrop2[2]
		MapWGCtrl.Instance:CloseMonsterTip()
		self.node_list.layout_global_root:SetActive(true)
		self.global_view:ShowIndexCallBack()
		self.global_view:DoOpenTween()
	end

end

function MapView:CloseCallBack()
	if self.local_view then
		self.local_view:IsClose(true)
	end
	MapWGCtrl.Instance:CloseMonsterTip()
end

function MapView:OnOpenCountry()

end

function MapView:GetMapName()
	local scene_id = Scene.Instance:GetSceneId()
	local key = MapWGData.MapNameList[scene_id]
	if scene_id ~= nil and key ~= nil then
		self.world_name:SetAsset(ResPath.GetMapName(key))
	end
end
