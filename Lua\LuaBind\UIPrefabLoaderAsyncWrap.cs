﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UIPrefabLoaderAsyncWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UIPrefabLoaderAsync), typeof(UIPrefabLoader));
		<PERSON><PERSON>unction("Wait", Wait);
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Prefab", get_Prefab, set_Prefab);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Wait(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UIPrefabLoaderAsync obj = (UIPrefabLoaderAsync)ToLua.CheckObject<UIPrefabLoaderAsync>(L, 1);
			System.Action<UnityEngine.GameObject> arg0 = (System.Action<UnityEngine.GameObject>)ToLua.CheckDelegate<System.Action<UnityEngine.GameObject>>(L, 2);
			obj.Wait(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Prefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIPrefabLoaderAsync obj = (UIPrefabLoaderAsync)o;
			Nirvana.AssetID ret = obj.Prefab;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Prefab on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Prefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UIPrefabLoaderAsync obj = (UIPrefabLoaderAsync)o;
			Nirvana.AssetID arg0 = StackTraits<Nirvana.AssetID>.Check(L, 2);
			obj.Prefab = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Prefab on a nil value");
		}
	}
}

