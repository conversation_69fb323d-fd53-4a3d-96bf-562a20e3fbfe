function CustomizedRumorsView:RumorsLoadIndexCallBack()
    if not self.rumors_left_list then
        self.rumors_left_list = AsyncListView.New(RumorsLeftListItemRender, self.node_list.rumors_left_list)
        self.rumors_left_list:SetSelect<PERSON>allBack(BindTool.Bind(self.OnSelectRum<PERSON><PERSON><PERSON><PERSON>, self))
        self.rumors_left_list:SetDefaultSelectIndex(nil)
    end

    if not self.cr_specail_rumiors then
        self.cr_specail_rumiors = BaseSpecialRumorsCell.New(self.node_list.cr_special_rumior_root)
    end

    if not self.cr_attr_list then
        self.cr_attr_list = AsyncListView.New(RumorCRAttrItemRender, self.node_list.cr_attr_list)
    end

    if not self.rumors_type_list then
        self.rumors_type_list = AsyncListView.New(RumorCRGrarTypeRender, self.node_list.rumors_type_list)
        self.rumors_type_list:SetSelectCallBack(BindTool.Bind(self.OnSelectRum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
        self.rumors_type_list:SetDefaultSelectIndex(nil)
    end

    self.cr_select_rumor_type = -1
    self.cr_select_gear_seq = -1
    self.cr_select_rumors_data = {}
    self.cr_select_rumors_type_data = {}

    self.node_list.desc_cr_explain.text.text = Language.CustomizedRumors.CustomizedRumorsExplain

    XUI.AddClickEventListener(self.node_list.btn_cr_see, BindTool.Bind(self.OnClickCRSee, self))
    XUI.AddClickEventListener(self.node_list.btn_opa_rumor, BindTool.Bind(self.OnClickCROpaRumor, self))
    XUI.AddClickEventListener(self.node_list.btn_cr_save_rumors, BindTool.Bind(self.OnClickCRSaveRumors, self))
    XUI.AddClickEventListener(self.node_list.cr_show_rumor, BindTool.Bind(self.OnClickCRShowRumors, self))
    XUI.AddClickEventListener(self.node_list.icon_cr_cost_item, BindTool.Bind(self.OnClickCRCostItem, self))
end

function CustomizedRumorsView:RumorsShowIndexCallBack()
    self:RightPanleShowTween(self.node_list.cr_right_tween_root, self.node_list.cr_right_tween_root)
end

function CustomizedRumorsView:RumorsChangeIndexCallBack()
end

function CustomizedRumorsView:RumorsReleaseCallBack()
    if self.rumors_left_list then
        self.rumors_left_list:DeleteMe()
        self.rumors_left_list = nil 
    end

    if self.cr_specail_rumiors then
        self.cr_specail_rumiors:DeleteMe()
        self.cr_specail_rumiors = nil 
    end

    if self.cr_attr_list then
        self.cr_attr_list:DeleteMe()
        self.cr_attr_list = nil
    end

    if self.rumors_type_list then
        self.rumors_type_list:DeleteMe()
        self.rumors_type_list = nil
    end

    self.cr_select_rumors_data = nil
end

function CustomizedRumorsView:RumorsOnFlush()
    local data_list = CustomizedRumorsWGData.Instance:GetRumorTypeShowInfoList()
    self.rumors_left_list:SetDataList(data_list)
    self.rumors_left_list:JumpToIndex(self:GetRumorsListDefaultSelectIndex())
end

function CustomizedRumorsView:GetRumorsListDefaultSelectIndex()
    if self.cr_select_rumor_type > 0 then
        local remind = CustomizedRumorsWGData.Instance:GetRumorTypeRemind(self.cr_select_rumor_type)

        if remind then
            return self.cr_select_rumor_type
        end
    end

    local data_list = CustomizedRumorsWGData.Instance:GetRumorTypeShowInfoList()

    for k, v in pairs(data_list) do
        local remind = CustomizedRumorsWGData.Instance:GetRumorTypeRemind(k)

        if remind then
            return k
        end
    end

    return self.cr_select_rumor_type > 0 and self.cr_select_rumor_type or 1
end

-- 选择传闻类型
function CustomizedRumorsView:OnSelectRumorsHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
 
    local data = item.data
    self.cr_select_rumors_data = data
    self.cr_select_rumor_type = data[1].desc_type
    self:SetCROpaBtnState(self.cr_select_rumor_type)
    self.rumors_type_list:SetDataList(data)
    self.rumors_type_list:JumpToIndex(self:GetRumorTypeSelect(self.cr_select_rumor_type))
end

function CustomizedRumorsView:SetCROpaBtnState(rumor_type)
    local is_use_now = CustomizedRumorsWGData.Instance:IsRumorIsUseNow(rumor_type)
    local desc_opa_btn = Language.CustomizedRumors.RumorUseType[0]

    if is_use_now then
        local is_open = CustomizedRumorsWGData.Instance:GetRumorCloseState(rumor_type)
        desc_opa_btn = is_open == 0 and Language.CustomizedRumors.RumorUseType[1] or Language.CustomizedRumors.RumorUseType[0]
    end

    self.node_list.desc_btn_opa_rumor.text.text = desc_opa_btn
end

-- 需要先判断当前   默认也是返回当前
function CustomizedRumorsView:GetRumorTypeSelect(rumor_type)
    if self.cr_select_gear_seq > 0 and self.cr_select_rumor_type > 0  then
        if CustomizedRumorsWGData.Instance:GetRumorGearRemind(self.cr_select_rumor_type, self.cr_select_gear_seq) then
            return self.cr_select_gear_seq
        end
    end

    local data_list = CustomizedRumorsWGData.Instance:GetRumorGearShowInfoList(rumor_type)

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if CustomizedRumorsWGData.Instance:GetRumorGearRemind(v.desc_type, v.gear_seq) then
                return k
            end
        end
    end
    
    return self.cr_select_gear_seq > 0 and self.cr_select_gear_seq or 1
end

function CustomizedRumorsView:OnSelectRumorsTypeHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
    local data = item.data
    self.cr_select_rumors_type_data = data
    self.cr_select_gear_seq = data.gear_seq
    self:FlushRumorsInfo(data)
end

function CustomizedRumorsView:FlushRumorsInfo(data)
    if IsEmptyTable(data) then
        return
    end

    local rumot_type = data.desc_type
    local dingzhi_desc = CustomizedRumorsWGData.Instance:GetCustomizedRumorDescContent(rumot_type, data.gear_seq)

    local rumor_data = {
        rumor_type = data.rumor_type,
        desc_rumors_content = dingzhi_desc,
        show_yoyo_tween = true,
    }
    self.cr_specail_rumiors:SetData(rumor_data)

    local is_active = CustomizedRumorsWGData.Instance:IsRumorActiveByRumorType(data.seq)
    self.node_list.icon_cr_cost_item:CustomSetActive(not is_active)

    if not is_active then
        local item_cfg = ItemWGData.Instance:GetItemConfig(data.cost_itemid)
        local icom_bundle, icon_asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list.icon_cr_cost_item.image:LoadSprite(icom_bundle, icon_asset)

        local has_num = ItemWGData.Instance:GetItemNumInBagById(data.cost_itemid)
        local color = has_num >= data.cost_item_num and COLOR3B.GREEN or COLOR3B.RED
        self.node_list.desc_cr_cost_item_num.text.text = ToColorStr(has_num .. "/" .. data.cost_item_num, color)
    end

    local attr_data_list, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(data, "attr_id", "attr_value")
    self.cr_attr_list:SetDataList(attr_data_list)
    self.node_list.cr_cap_value.text.text = cap

    local dingzhi_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(rumot_type)
    local has_dingzhi = dingzhi_seq ~= 0
    self.node_list.desc_cr_input_text.text.text = has_dingzhi and dingzhi_desc or Language.CustomizedRumors.CRClickToOpera
    
    local select_skin_seq = CustomizedRumorsWGData.Instance:GetRumorTypeSelectGearSeq(rumot_type)
    local is_use_skin = select_skin_seq == data.gear_seq

    -- 未激活
    local desc_index = 0
    if is_active then
        if dingzhi_seq == 0 then
            desc_index = 1
        else
            if dingzhi_seq == -1 then
                local status = CustomizedRumorsWGData.Instance:IsCustomizedRumorByRumorType(rumot_type)
                if status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED then
                    desc_index = 3
                elseif status == DIYCHUANWEN_OPERA_STATUS.SUC then
                    if is_use_skin then
                        desc_index = 4
                    else
                        desc_index = 2
                    end
                end
            else
                if is_use_skin then
                    desc_index = 4
                else
                    desc_index = 2
                end
            end
        end
    end

    local remind = CustomizedRumorsWGData.Instance:GetRumorGearRemind(self.cr_select_rumor_type, self.cr_select_gear_seq)
    self.node_list.btn_cr_save_rumors_remind:CustomSetActive(remind)
    self.node_list.desc_cr_save_rumors.text.text = Language.CustomizedRumors.CROperaBtnDesc[desc_index]
end

function CustomizedRumorsView:OnClickCRSee()
    if IsEmptyTable(self.cr_select_rumors_type_data) or IsEmptyTable(self.cr_select_rumors_data) then
        return
    end

    CustomizedRumorsWGCtrl.Instance:OpenRumorsSetView(self.cr_select_rumors_data, self.cr_select_rumors_type_data)
end

function CustomizedRumorsView:OnClickCROpaRumor()
    local is_use_now = CustomizedRumorsWGData.Instance:IsRumorIsUseNow(self.cr_select_rumor_type)

    if is_use_now then
        local is_open = CustomizedRumorsWGData.Instance:GetRumorCloseState(self.cr_select_rumor_type)
        local param = is_open == 0 and 1 or 0
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.RumorUseState[param])
        CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.Close, self.cr_select_rumor_type, param)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.RumorUseTips)
    end
end

function CustomizedRumorsView:OnClickCRSaveRumors()
    if IsEmptyTable(self.cr_select_rumors_type_data) or IsEmptyTable(self.cr_select_rumors_data) then
        return
    end

    local rumot_type = self.cr_select_rumors_type_data.desc_type
    local gear_seq = self.cr_select_rumors_type_data.gear_seq
    local seq = self.cr_select_rumors_type_data.seq
    local is_active = CustomizedRumorsWGData.Instance:IsRumorActiveByRumorType(self.cr_select_rumors_type_data.seq)

    if is_active then
        local dingzhi_seq = CustomizedRumorsWGData.Instance:GetRumorChooseRumorType(rumot_type)
        local status = CustomizedRumorsWGData.Instance:IsCustomizedRumorByRumorType(rumot_type)

        --无定制 /定制审核中  跳转
        if dingzhi_seq == 0 or (dingzhi_seq == -1 and status == DIYCHUANWEN_OPERA_STATUS.CUSTOMIZED) then
            CustomizedRumorsWGCtrl.Instance:OpenRumorsSetView(self.cr_select_rumors_data, self.cr_select_rumors_type_data)
        else
            local select_skin_seq = CustomizedRumorsWGData.Instance:GetRumorTypeSelectGearSeq(rumot_type)

            if select_skin_seq == gear_seq then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.IsSelectThisSkin)
            else
                -- 设置皮肤
                CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.CHOOSE_SKIN, rumot_type, seq)
            end
        end
    else
        --激活流程
        local cost_item_id = self.cr_select_rumors_type_data.cost_itemid
        local cost_num = self.cr_select_rumors_type_data.cost_item_num
        if cost_item_id and cost_item_id > 0 then
            local has_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
            if cost_num <= has_num then
                CustomizedRumorsWGCtrl.Instance:SendDiyChuanWenClientRequest(DIYCHUANWEN_OPERA_TYPE.ACTIVE_SKIN, rumot_type, seq)
            else
                TipWGCtrl.Instance:OpenItem({item_id = self.cr_select_rumors_type_data.cost_itemid})
            end
        end
    end
end

function CustomizedRumorsView:OnClickCRShowRumors()
    if IsEmptyTable(self.cr_select_rumors_type_data) or IsEmptyTable(self.cr_select_rumors_data) then
        return
    end

    CustomizedRumorsWGCtrl.Instance:OpenRumorsSetView(self.cr_select_rumors_data, self.cr_select_rumors_type_data)
end

function CustomizedRumorsView:OnClickCRCostItem()
    if IsEmptyTable(self.cr_select_rumors_type_data) then
        return
    end

    TipWGCtrl.Instance:OpenItem({item_id = self.cr_select_rumors_type_data.cost_itemid})
end

-------------------------------RumorsLeftListItemRender----------------------------
RumorsLeftListItemRender = RumorsLeftListItemRender or BaseClass(BaseRender)

function RumorsLeftListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local desc_cfg = self.data[1]
    self.node_list.desc_name.text.text = desc_cfg.rumor_type_name or ""
    self.node_list.desc_content.text.text = desc_cfg.rumor_type_desc or ""
    
    local active = CustomizedRumorsWGData.Instance:IsRumorTypeActive(desc_cfg.desc_type)
    self.node_list.flag_lock:CustomSetActive(not active)
    self.node_list.flag_unlock:CustomSetActive(active)

    local remind = CustomizedRumorsWGData.Instance:GetRumorTypeRemind(desc_cfg.desc_type)
    self.node_list.remind:CustomSetActive(remind)
end

function RumorsLeftListItemRender:OnSelectChange(is_select)
    self.node_list.bg_hl:CustomSetActive(is_select)
end

RumorCRGrarTypeRender = RumorCRGrarTypeRender or BaseClass(BaseRender)

function RumorCRGrarTypeRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local name = self.data.rumor_gear_name
    self.node_list.desc_name.text.text = name
    self.node_list.desc_name_hl.text.text = name

    local remind = CustomizedRumorsWGData.Instance:GetRumorGearRemind(self.data.desc_type, self.data.gear_seq)
    self.node_list.remind:CustomSetActive(remind)
end

function RumorCRGrarTypeRender:OnSelectChange(is_select)
    self.node_list.bg:CustomSetActive(not is_select)
    self.node_list.select:CustomSetActive(is_select)
end

RumorCRAttrItemRender = RumorCRAttrItemRender or BaseClass(BaseRender)

function RumorCRAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name
    self.node_list.attr_value.text.text = self.data.value_str
end