require("game/huanhua_fetter/huanhua_fetter_wg_data")
require("game/huanhua_fetter/huanhua_fetter_view")
require("game/huanhua_fetter/huanhua_fetter_info_view")

HuanHuaFetterWGCtrl = HuanHuaFetterWGCtrl or BaseClass(BaseWGCtrl)

function HuanHuaFetterWGCtrl:__init()
	if nil ~= HuanHuaFetterWGCtrl.Instance then
		ErrorLog("[HuanHuaFetterWGCtrl] attempt to create singleton twice!")
		return
	end
	HuanHuaFetterWGCtrl.Instance = self

    self.data = HuanHuaFetterWGData.New()
	self.view = HuanHuaFetterView.New(GuideModuleName.HuanHuaFetterView)

    self:RegisterAllProtocols()
	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function HuanHuaFetterWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
        self.role_data_change = nil
    end

    HuanHuaFetterWGCtrl.Instance = nil
end

function HuanHuaFetterWGCtrl:OnRoleAttrChange(attr_name)
	if attr_name == "level" then
		self.data:SetToggleList()
		self:FlushView(nil, "flush_open_toggle")
	end
end

function HuanHuaFetterWGCtrl:OnDayChange()
	self.data:SetToggleList()
	self:FlushView(nil, "flush_open_toggle")
end

-- 刷新界面
function HuanHuaFetterWGCtrl:FlushView(index, key, param_t)
	if self.view:IsOpen() then
		self.view:Flush(index, key, param_t)
	end
end

function HuanHuaFetterWGCtrl:RegisterAllProtocols()
    -- self:RegisterProtocol(CSHuanhuaFetterOperate)
	-- self:RegisterProtocol(SCHuanhuaFetterItemInfo, "OnSCHuanhuaFetterItemInfo")
	-- self:RegisterProtocol(SCHuanhuaFetterItemUpdate, "OnSCHuanhuaFetterItemUpdate")
end

function HuanHuaFetterWGCtrl:SendHuanHuaFetterRequest(operate_type, param1, param2)
	--print_error("---请求----", operate_type, param1, param2)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSHuanhuaFetterOperate)
 	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol:EncodeAndSend()
end

function HuanHuaFetterWGCtrl:OnSCHuanhuaFetterItemInfo(protocol)
	--print_error("---全部套装信息----", protocol)
	self.data:SetAllSuitStateInfo(protocol)
    local rm = RemindManager.Instance
	rm:Fire(RemindName.HuanhuaFetterWaiguan)
	rm:Fire(RemindName.HuanhuaFetterLingchong)
	rm:Fire(RemindName.HuanhuaFetterMount)
	rm:Fire(RemindName.HuanhuaFetterKun)
end

function HuanHuaFetterWGCtrl:OnSCHuanhuaFetterItemUpdate(protocol)
	--print_error("---单个信息更新----", protocol)
	self.data:SetSingleSuitStateInfo(protocol)
    self:FlushView(TabIndex.HuanHuaFetterView, "protocol_change")
    local rm = RemindManager.Instance
	rm:Fire(RemindName.HuanhuaFetterWaiguan)
	rm:Fire(RemindName.HuanhuaFetterLingchong)
	rm:Fire(RemindName.HuanhuaFetterMount)
	rm:Fire(RemindName.HuanhuaFetterKun)
end

function HuanHuaFetterWGCtrl:OnPartActiveResult(result, suit, part)
	--print_error("---激活返回----", result, suit, part)
	if result == 1 then
		if self.view:IsOpen() then
			self.view:DoActiveEffect()
		end
	end
end