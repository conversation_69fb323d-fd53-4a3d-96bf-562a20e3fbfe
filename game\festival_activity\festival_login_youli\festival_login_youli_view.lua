
function FestivalActivityView:LoadIndexCallBackLoginRewarView()
	self:LoadLoginGiftUI()
	--[[
		9303协议活动数据下来之后，要是开服天数还没下来，活动数据会延迟刷新
		此时8244协议下来，取不到活动数据，计算活动开启时间为0天，获取不到配置数据
		此处增加容错，开界面的时候再请求一次数据
	]]
	FestivalLoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.INFO)

	self.rember_up_index = 0


	-- self.role_data_change_callback_lr = BindTool.Bind1(self.RoleDataChangeCallback, self)
	-- RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback_lr, {"vip_level"})
	if not self.login_gift_list_view then
		self.login_gift_list_view = AsyncListView.New(FestivalLoginGiftCell,self.node_list["login_gift_reward_list"])
	end
end

function FestivalActivityView:LoadLoginGiftUI()
	local login_gift_raw_bg_bundle, login_gift_raw_bg_asset = ResPath.GetFestivalRawImages("dlylbj")
 	self.node_list["login_gift_raw_bg"].raw_image:LoadSprite(login_gift_raw_bg_bundle, login_gift_raw_bg_asset, function ()
 		self.node_list["login_gift_raw_bg"].raw_image:SetNativeSize()
  	end)

	local login_list_bg_bundle, login_list_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_di1")
 	self.node_list["login_list_bg"].image:LoadSprite(login_list_bg_bundle, login_list_bg_asset)
end

--刷新界面状态
function FestivalActivityView:OperationFlushLoginView()

	local data_list = FestivalLoginYouLiWGData.Instance:GetOpenDays()
	if data_list and not IsEmptyTable(data_list) then
		self.login_gift_list_view:SetDataList(data_list)
	end
	self.node_list.login_gift_note_text.text.text = Language.MergeActivity.LoginYouLiState
	-- if not self.login_active_runquest then
	-- 	self.login_active_runquest = GlobalTimerQuest:AddRunQuest(function()
	-- 		self:ShowLoginActiveTime()
	-- 	end,1)
	-- end
	-- self:ShowLoginActiveTime()
end

function FestivalActivityView:ShowLoginActiveTime()
	self.node_list.login_gift_active_time.text.text = ""
end


function FestivalActivityView:SetLoginYouLiViewInfo()
    --问号样式和文本框
    FestivalLoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.INFO)
    self:SetOutsideRuleTips(Language.MergeActivity.LoginYouLiState)
    local cfg = FestivalLoginYouLiWGData.Instance:GetInterfaceCfg()
	self:SetRuleInfo(cfg.activity_des, Language.MergeActivity.TipsActivityHint)
end

function FestivalActivityView:ReleaseLoginRewardView()
	if self.login_gift_list_view then
		self.login_gift_list_view:DeleteMe()
		self.login_gift_list_view = nil
	end

	-- if self.login_active_runquest then
	-- 	GlobalTimerQuest:CancelQuest(self.login_active_runquest)
	-- 	self.login_active_runquest = nil
	-- end

	-- if self.role_data_change_callback_lr then
	-- 	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback_lr)
	-- 	self.role_data_change_callback_lr = nil
	-- end
end

-- FestivalLoginYouLiWGData:GetOpenDays()


-- function FestivalActivityView:RoleDataChangeCallback(attr_name, value, old_value)
-- 	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
-- 	if attr_name == "vip_level" then
-- 		self:FlushLoginRewardReceiveBtnState()
-- 		RemindManager.Instance:Fire(RemindName.FestivalLoginRward)
-- 	end
-- end

FestivalLoginGiftCell = FestivalLoginGiftCell or BaseClass(BaseRender)

function FestivalLoginGiftCell:__init()
	self.node_list["cell_bg"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_lb"))
	self.node_list["day_bg"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_lbbt"))
	self.node_list["btn_receive"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_btn_huang"))
	if not self.vip_list_view then
		self.vip_list_view = AsyncListView.New(FestivalLoginGiftSmallCell,self.node_list["vip_reward_list"])
	end

	if not self.normal_list_view then
		self.normal_list_view = AsyncListView.New(FestivalLoginGiftSmallCell,self.node_list["normal_reward_list"])
    end

	self.node_list["btn_receive"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward,self))
	self.node_list["not_enough_btn"].button:AddClickListener(BindTool.Bind(self.OnClickNoEnough,self))
end

function FestivalLoginGiftCell:ReleaseCallBack()
	if self.vip_list_view then
		self.vip_list_view:DeleteMe()
		self.vip_list_view = nil
	end

	if self.normal_list_view then
		self.normal_list_view:DeleteMe()
		self.normal_list_view = nil
	end

end

function FestivalLoginGiftCell:OnClickGetReward()
	if self.data and not IsEmptyTable(self.data) then
		local data_info = FestivalLoginYouLiWGData.Instance:GetDayInfoByDayIndex(self.data.day_index)
		if data_info.common_gift_state == LoginYouLiRewardState.KLQ then
			FestivalLoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.COMMON_REWARD,self.data.day_index)
		end
		if data_info.special_gift_state == LoginYouLiRewardState.KLQ then
			FestivalLoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD,self.data.day_index)
		elseif data_info.special_gift_state == LoginYouLiRewardState.BKL then
			local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
			if self.data.vip_lv ~= "" and self.data.vip_lv >= 0 and cur_vip_level >= self.data.vip_lv then
				FestivalLoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD,self.data.day_index)
			end
		end
	end
end

function FestivalLoginGiftCell:OnClickNoEnough()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeActivity.TipsActivityHint)
end

function FestivalLoginGiftCell:OnFlush()
	if self.data and not IsEmptyTable(self.data) then
		local reward_item = {}
		for k,v in pairs(self.data.reward_item) do
			-- reward_item[k+1] = v
			table.insert(reward_item,v)
		end
		reward_item = SortDataByItemColor(reward_item)
		local special_reward_item = {}
		for t,q in pairs(self.data.special_reward_item) do
			special_reward_item[t+1] = q
		end
		self.vip_list_view:SetDataList(reward_item)
		self.normal_list_view:SetDataList(special_reward_item)

		local show_btn = false

		self.node_list["btn_ylq"]:SetActive(false)
		self.node_list["receive_effect"]:SetActive(false)
		self.node_list["redpoint"]:SetActive(false)
		local data_info = FestivalLoginYouLiWGData.Instance:GetDayInfoByDayIndex(self.data.day_index)
        self.node_list["day_text"].text.text = string.format(Language.FestivalLoginGift.LoginDayNum, self.data.day_index)

		if not data_info or IsEmptyTable(data_info) then
			return
		elseif data_info.index > data_info.login_day_num then
			show_btn = false
		elseif data_info.common_gift_state == LoginYouLiRewardState.KLQ or data_info.special_gift_state == LoginYouLiRewardState.KLQ then
			show_btn = true
			self.node_list["label"].text.text = Language.OperationActivity.BtnStr2
			self.node_list["redpoint"]:SetActive(true)
		elseif data_info.special_gift_state == LoginYouLiRewardState.BKL and data_info.index <= data_info.login_day_num then
			local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
			if self.data.vip_lv ~= "" and self.data.vip_lv >= 0 and cur_vip_level >= self.data.vip_lv then
				show_btn = true
				self.node_list["label"].text.text = Language.OperationActivity.BtnStr2
				self.node_list["redpoint"]:SetActive(true)
			else
				self.node_list["label"].text.text = Language.Vip.VipTips_4
			end
		end
		self.node_list["btn_receive"]:SetActive(show_btn)
		local show_ylq = false
		if data_info.common_gift_state == LoginYouLiRewardState.YLQ and data_info.special_gift_state == LoginYouLiRewardState.YLQ then
			show_ylq = true
		end
		self.node_list["btn_ylq"]:SetActive(show_ylq)
		self.node_list["not_enough_btn"]:SetActive(not show_btn and not show_ylq)
	end

end


FestivalLoginGiftSmallCell = FestivalLoginGiftSmallCell or BaseClass(BaseRender)

function FestivalLoginGiftSmallCell:__init()
	self.item_cell = ItemCell.New(self.node_list["cell_root"])
end

function FestivalLoginGiftSmallCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FestivalLoginGiftSmallCell:OnFlush()
	if self.data and not IsEmptyTable(self.data) then
		self.item_cell:SetData(self.data)
	end
end