require("game/scene/follow_ui/follow_bubble")
require("game/scene/follow_ui/follow_namebar")
require("game/scene/follow_ui/follow_hpbar")
require("game/scene/follow_ui/follow_name")
require("game/scene/follow_ui/follow_aim")
FollowUi = FollowUi or BaseClass(VisibleObj)

local TypeNameTable = typeof(UINameTable)

function FollowUi:__init(vo)
	self.vo = vo
	self.bubble = FollowBubble.New()
	self.namebar = FollowNameBar.New()
	self.shield_obj_type = ShieldObjType.FollowUI

	self.follow_target = nil
	self.bundle = nil
	self.is_root_created = false

	self.follow_name_prefab_name = "SceneObjName"
	self.follow_hp_prefab_name = "MonsterHP"

	self.is_root_visible = true
	self.need_create_root = false
	self.name_off_y = 0

	self.show_hp_bar = false
	self.is_enter_scene = false
	self.aim_eff_state = false
	self.aim_eff_offset_info = nil
	self.aim_obj = nil

	self.hpbar = FollowHpBar.New()
	self.hpbar:SetVisibleListener(BindTool.Bind1(self.SetNameTextPosition, self))
	self.hpbar:AddShieldRule(ShieldRuleWeight.Low, function ()
		return not self.show_hp_bar
	end)
end

function FollowUi:__delete()
	if nil ~= self.bubble then
		self.bubble:DeleteMe()
		self.bubble = nil
	end

	if nil ~= self.namebar then
		self.namebar:DeleteMe()
		self.namebar = nil
	end

	if nil ~= self.hpbar then
		self.hpbar:DeleteMe()
		self.hpbar = nil
	end

	if nil ~= self.view then
		self:ResumeDefaultFollowPos()

		self.view.uifollow_target.Canvas = nil
		self.view.uifollow_target.Target = nil
		self.view.uifollow_distance.TargetTransform = nil
	end

	self.bundle = nil

	if self.luandou_score_text ~= nil then
		ResMgr:Destroy(self.luandou_score_text)
		self.luandou_score_text = nil
	end

	if self.aim_obj ~= nil then
		self.aim_obj:DeleteMe()
		self.aim_obj = nil
	end

	self.follow = nil
	self.face_icon = nil
	self.is_show_face_icon = nil
	self.follow_target_attach_point = nil
	self.aim_eff_state = false
	self.aim_eff_offset_info = nil
	self.aim_obj = nil
end

function FollowUi:OnEnterScene(is_enter_scene)
	self.is_enter_scene = is_enter_scene
	if is_enter_scene and self.obj_type then
		self:Create(self.obj_type)
	end
end

function FollowUi.GetFloatingCanvas()
	if nil == FollowUi.floating_canvas then
		local obj = ResMgr:Instantiate(SafeBaseView.GetBaseViewParentTemplate())
		obj.name = "FloatingCanvas"
		obj:SetActive(true)

		local canvas = obj:GetComponent(typeof(UnityEngine.Canvas))
		canvas.overrideSorting = true
		canvas.sortingOrder = UiLayer.SceneName

		canvas.worldCamera = UICamera

		local canvas_transform = canvas.transform
		obj:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster)).enabled = false

		canvas_transform:SetParent(UILayer.transform, false)
		canvas_transform:SetLocalScale(1, 1, 1)

		local rect = canvas_transform:GetComponent(typeof(UnityEngine.RectTransform))
		rect.anchorMax = Vector2(1, 1)
		rect.anchorMin = Vector2(0, 0)
		rect.anchoredPosition3D = Vector3(0, 0, 0)
		rect.sizeDelta = Vector2(0, 0)

		FollowUi.floating_canvas = canvas
	end

	return FollowUi.floating_canvas
end

function FollowUi.SetActive(value)
	local canvas = FollowUi.GetFloatingCanvas()
	if canvas == nil then
		return
	end

	if canvas.gameObject.activeSelf ~= value then
		canvas.gameObject:SetActive(value)
	end
end

local function CreateRootCallBack(gameobj, cbdata)
	local self = cbdata[1]
	FollowUi.ReleaseCBData(cbdata)

	if IsNil(gameobj) then
		return
	end

	self.view = U3DObject(gameobj)
	local name_table = gameobj:GetComponent(TypeNameTable)
	self.node_list = U3DNodeList(name_table, self)

	self.namebar:CreateRootNode(self.follow_name_prefab_name, self.obj_type, self.node_list["Follow"].gameObject)
	self.namebar:SetIconList(self.node_list["IconList"].transform)
	self.bubble:SetFollowParent(self.obj_type, gameobj)

	self:UpdateFollowTarget()
	self:UpdateRootNodeVisible()
	self:UpdateFollowPos()
	self:UpdateTitle()
	self:UpdateSetTitle()
	self:OnRootCreateCompleteCallback(gameobj)

	if self.aim_eff_state then
		self:ShowAimEffect(self.aim_eff_state, self.aim_eff_offset_info)
	end
end

function FollowUi:Create(obj_type)
	self.obj_type = obj_type
	self:CreateShieldHandle()
	if not self.is_root_visible then
		self.need_create_root = true
		return
	end

	if self.is_root_created or not self.is_enter_scene then
		return
	end

	self.is_root_created = true

	if obj_type then
		self.obj_type = obj_type
	end

	if obj_type == SceneObjType.Role then
		self.follow_name_prefab_name = "SceneRoleObjName"
		self.follow_hp_prefab_name = "RoleHP"
	elseif obj_type == SceneObjType.Pet then
        self.follow_name_prefab_name = "ScenePetObjName"
    elseif obj_type == SceneObjType.MarryObj then
		self.follow_name_prefab_name = "SceneXunyouObjName"
	end

	local async_loader = AllocAsyncLoader(self, "root_loader")
	async_loader:SetIsUseObjPool(true)
	async_loader:SetIsInQueueLoad(true)
	local canvas = FollowUi.GetFloatingCanvas()
	async_loader:SetParent(canvas.transform, false)
	local cbdata = FollowUi.GetCBData()
	cbdata[1] = self
	async_loader:Load("uis/view/miscpre_load_prefab", "FollowUi", CreateRootCallBack, cbdata)
end

function FollowUi:OnRootCreateCompleteCallback(gameobj)
	-- override
end

function FollowUi:SetFollowTarget(attach_point, follow_target_name)
	self.follow_target_attach_point = attach_point
	self.follow_target_name = follow_target_name
	self:UpdateFollowTarget()
end

function FollowUi:UpdateFollowTarget()
	if nil ~= self.follow_target_attach_point and nil ~= self.view then
		local follow_target_com = self.view:GetComponent(typeof(UIFollowTarget))
		local canvas = FollowUi.GetFloatingCanvas()
		follow_target_com.Canvas = canvas
		follow_target_com.Target = self.follow_target_attach_point

		self.view.uifollow_distance.TargetTransform = self.follow_target_attach_point

		self.view.gameObject.name = string.format("follow_ui(%s)", self.follow_target_name or "")
	end
end

function FollowUi:SetLocalUI(x,y,z)
	self.follow_pos = {x = x, y = y}
	self:UpdateFollowPos()
end

function FollowUi:GetLocalUi()
	return self.follow_pos
end

function FollowUi:UpdateFollowPos()
	if nil ~= self.follow_pos and nil ~= self.view then
		if nil == self.default_follow_pos then
			self.default_follow_pos = self.node_list["Follow"].transform.localPosition
		end
		self.node_list["Follow"]:SetLocalPosition(self.follow_pos.x, self.follow_pos.y, 0)
	end
end

function FollowUi:ResumeDefaultFollowPos()
	if nil ~= self.default_follow_pos and nil ~= self.node_list["Follow"] then
		self.node_list["Follow"].transform.localPosition = self.default_follow_pos
	end
end

function FollowUi:VisibleChanged(visible)
	if visible then
		self:Show()
	else
		self:Hide()
	end
end

function FollowUi:Show()
	if self.is_root_visible then
		return
	end

	self.is_root_visible = true
	if self.need_create_root then
		self:Create(self.obj_type)
		self.need_create_root = false
	end
	self:UpdateRootNodeVisible()
end

function FollowUi:Hide()
	if not self.is_root_visible then
		return
	end

	self.is_root_visible = false
	self:UpdateRootNodeVisible()
end

function FollowUi:UpdateRootNodeVisible()
	if nil ~= self.is_root_visible and nil ~= self.view then
		self.view:SetActive(self.is_root_visible)
	end
end

function FollowUi:GetVisiable()
	return self.is_root_visible
end

function FollowUi:SetNameVis(is_active)
	self.namebar:SetIsActive(is_active)
end

function FollowUi:SetName(name, secne_obj, name_color)
	self.namebar:SetName(name, secne_obj, name_color)
	self.hpbar:SetSceneObj(secne_obj)
end

-- function FollowUi:SetJinjieIcon(icon_id)
-- 	self.namebar:SetJinjieIcon(icon_id)
-- end

function FollowUi:SetXiuWeiIcon(icon_id)
	self.namebar:SetXiuWeiIcon(icon_id)
end

function FollowUi:SetSpecialImage(is_show, bundle, asset)
	self.namebar:SetSpecialImage(is_show, bundle, asset)
	if is_show ~= self.special_title_img then
		self.special_title_img = is_show
		self:UpdateSetTitle()
	end
end

function FollowUi:SetGatherImg(is_show)
	self.namebar:SetGatherImg(is_show)
end

function FollowUi:SetFaceIcon(is_show, asset, bundle, str_tip, str_num, show_commit_task, show_bg)
	self.namebar:SetFaceIcon(is_show, asset, bundle, str_tip, str_num, show_commit_task, show_bg)
end

function FollowUi:SetGuildName(guild_name)
	self.namebar:SetGuildName(guild_name)
	self:UpdatePosition()
end

function FollowUi:SetHuSongName(husong_name)
	self.namebar:SetHuSongName(husong_name)
end

function FollowUi:SetHuSongImg(bundle, asset)
	self.namebar:SetHuSongImg(bundle, asset)
end

function FollowUi:SetRoleVip(vip_level,is_active_baozhu,is_hide_vip)
	self.namebar:SetRoleVip(vip_level,is_active_baozhu,is_hide_vip)
end

function FollowUi:SetRoleGodOrDemon(god_or_demon_type, god_or_demon_level)
	self.namebar:SetRoleGodOrDemon(god_or_demon_type, god_or_demon_level)
end

function FollowUi:SetRoleFGBTeamInfo(fgb_team_id)
	self.namebar:SetRoleFGBTeamInfo(fgb_team_id)
end

function FollowUi:SetYZWCBuffShow(buff_list)
	self.namebar:SetYZWCBuffShow(buff_list)
end

function FollowUi:SetLoverName(lover_name, obj)
	self.namebar:SetLoverName(lover_name)
	self:UpdatePosition()
end

function FollowUi:SetLeaderFlag(is_show_leader_flag)
	self.namebar:SetLeaderFlag(is_show_leader_flag)
end

function FollowUi:SetPWCampIcon(camp_id)
	self.namebar:SetPWCampIcon(camp_id)
end

function FollowUi:SetBoxBossAngry(asset, bundle, angry)
	self.namebar:SetBoxBossAngry(asset, bundle, angry)
end

function FollowUi:SetNPCIcon(npc_icon_id , status)
	self.namebar:SetNPCIcon(npc_icon_id, status)
end

function FollowUi:SetMonsterIconAsset(monster_icon_bundle, monster_icon_asset)
	self.namebar:SetMonsterIconAsset(monster_icon_bundle, monster_icon_asset)
end

function FollowUi:SetNPCTitle(npc_title , status)
	self.namebar:SetNPCTitle(npc_title, status)
end

function FollowUi:SetFloatIconAsset(bundle, asset)
	self.namebar:SetFloatIconAsset(bundle, asset)
end

function FollowUi:ChangeNamePositionAndScale(pos_x, pos_y, scale)
	self.namebar:ChangePositionAndScale(pos_x, pos_y, scale)
end

function FollowUi:ChangeTitle(bundle, asset, pos_x, pos_y)
	self.title_info = {bundle = bundle, asset = asset, pos_x = pos_x, pos_y = pos_y}
	self:UpdateTitle()
end

local function LoadTitleCallBack(gameobj, cbdata)
	local self = cbdata[1]
	FollowUi.ReleaseCBData(cbdata)

	if IsNil(gameobj) then
		return
	end

	gameobj.transform:SetParent(self.view.transform, false)
	gameobj.transform:SetLocalPosition(self.title_info.pos_x or 0, self.title_info.pos_y or 80, 0)
end

function FollowUi:UpdateTitle()
	if nil == self.title_info or nil == self.view then
		return
	end

	if nil == self.title_info.bundle or nil == self.title_info.asset then
		if nil ~= self.title_async_loader then
			self.title_async_loader:DeleteMe()
			self.title_async_loader = nil
		end
		return
	end

	self.title_async_loader = self.title_async_loader or AllocAsyncLoader(self, "title_loader")
	self.title_async_loader:SetIsUseObjPool(true)
	self.title_async_loader:SetIsInQueueLoad(true)

	local cbdata = FollowUi.GetCBData()
	cbdata[1] = self
	self.title_async_loader:Load(self.title_info.bundle, self.title_info.asset, LoadTitleCallBack, cbdata)
end

function FollowUi:GetNameTextObj()
	return self.name
end

-- 外部设置名字的OffY
function FollowUi:SetNameTextOffY(y)
	self.name_off_y = y
	self:SetNameTextPosition()
end

-- 外部修正位置
function FollowUi:UpdatePosition()
	self:SetNameTextPosition()
end

-- 外部设置名字的Position
function FollowUi:SetNameTextPosition()
	if self.hpbar == nil or self.namebar == nil then
		return
	end

	if self.hpbar:GetVisiable() then
		self.namebar:SetAnchoredPosition(0, 70 + self.name_off_y)
		self:UpdateTitleOffY(25 + self.name_off_y)
	else
		self.namebar:SetAnchoredPosition(0, 45 + self.name_off_y)
		self:UpdateTitleOffY(self.name_off_y)
	end
end

--base
function FollowUi:UpdateTitleOffY(off_y)

end

function FollowUi:ChangeBubble(text, time, callback)
	local pos_x, pos_y = 0, 0
	if self.node_list and self.node_list["Follow"] then
		pos_x, pos_y = RectTransform.GetAnchoredPositionXY(self.node_list["Follow"].rect)
	end

	self.bubble:ChangeBubble(text, time, callback, {0, pos_y * 0.5})
end

function FollowUi:ChangeRoleBubble(text, time, callback)
	local pos_x, pos_y, temp_x
	if self.namebar then
		temp_x, pos_y = self.namebar:GetRoleRootNodeWidthAndHeight()
		if self.namebar:GetTitleVisable() then
			local title_offset = self.namebar:GetTitleHeight()
			pos_y = pos_y - title_offset
		end
	end

	if self.bubble then
		self.bubble:ChangeBubble(text, time, callback, {pos_x or 0, pos_y or 0})
	end
end

function FollowUi:HideBubble()
	self.bubble:HideBubble()
end

function FollowUi:ShowBubble()
	self.bubble:ShowBubble()
end

function FollowUi:IsBubbleVisible()
	return self.bubble:IsBubbleVisible()
end

function FollowUi:SetHpPercent(hp_percent)
	self.hpbar:SetHpPercent(hp_percent)
end

function FollowUi:SetBianShenHpPercent(hp_percent)
	self.hpbar:SetBianShenHpPercent(hp_percent)
end

function FollowUi:SetHpVisiable(value, is_ignore_save)
	if not is_ignore_save then
		self.show_hp_bar = value or false
	end

	self.hpbar:RefreshVisiable()
end

function FollowUi:SetHpBarLocalPosition(x, y, z)
	self.hpbar:SetLocalPosition(x, y, z)
end

function FollowUi:SetTitle(index, title_id)
	if index > 1 then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.KFSHENYUN_FB or scene_type == SceneType.SHENGYU_BOSS then
		return
	end

	self.title_info = {index = index, title_id = title_id}
	self:UpdateSetTitle()
end

function FollowUi:UpdateSetTitle()
	-- override
end

function FollowUi:SetNameContainerVisible(active)
	self.namebar:SetNameContainerVisible(active)
end

function FollowUi:IsNil()
	return IsNil(self.view and self.view.gameObject)
end

function FollowUi:GetHpBar()
	return self.hpbar
end

function FollowUi:GetServerInfo()
	return self.namebar:GetServerInfo()
end

function FollowUi:GetServerInfoActive()
	return self.namebar:GetServerInfoActive()
end

function FollowUi:GetGuildName()
	return self.namebar:GetGuideName()
end

function FollowUi:GetLoverName()
	return self.namebar:GetLoverName()
end

function FollowUi:FlushVoInfo(vo)
	if vo == nil then
		return
	end

	self.vo = vo
	self:UpdatePosition()
end

function FollowUi:UpdateBleedShow(is_show)
	self.namebar:UpdateBleedShow(is_show)
end

function FollowUi:SetOwnerObj(secne_obj)
	self.secne_obj = secne_obj
end

function FollowUi:GetOwnerObj()
	return self.secne_obj
end

function FollowUi:SetWuXingType(wuxing_type)
	wuxing_type = wuxing_type or 0
	self.namebar:SetRoleWuXing(wuxing_type)
end

function FollowUi:SetAreaIndexImg(area_index)
	self.namebar:SetAreaIndexImg(area_index)
end

function FollowUi:SetXianjieEquipImg(xianjie_boss_equip)
	self.namebar:SetXianjieEquipImg(xianjie_boss_equip)
end

function FollowUi:SetTianShen3v3Side(tianshen_3v3_side)
	self.namebar:SetTianShen3v3Side(tianshen_3v3_side)
end

function FollowUi:SetIsDrunk(is_drunk)
	self.namebar:SetIsDrunk(is_drunk)
end

function FollowUi:GetHasEternalNightEquipData()
	local equip_data = self.namebar:GetEternalNightEquipData()
	local has_data = equip_data and not IsEmptyTable(equip_data)
	return has_data
end

function FollowUi:ShowAimEffect(value, offset_info)
	self.aim_eff_state = value
	self.aim_eff_offset_info = offset_info
	if self.node_list == nil then
		return
	end

	if self.aim_obj == nil then
		self.aim_obj = FollowAim.New()
		self.aim_obj:SetFollowParent(self.obj_type, self.node_list["Follow"].gameObject)
	end

	self.aim_obj:SetIsShow(self.aim_eff_state)
	if not IsEmptyTable(offset_info) then
		self.aim_obj:SetPosOffset(offset_info)
	end
end

function FollowUi:SetShowTimer(str, value, obj_id)
	self.namebar:SetShowTimer(str, value, obj_id)
end

FollowUi.cbdata_list = {}
function FollowUi.GetCBData()
    local cbdata = table.remove(FollowUi.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true}
    end

    return cbdata
end

function FollowUi.ReleaseCBData(cbdata)
	cbdata[1] = true
	cbdata[3] = nil
    table.insert(FollowUi.cbdata_list, cbdata)
end

-- 设置终极战场人物名称上面的数据
function FollowUi:SetUltimateState(ultimate_data)
	self.namebar:SetUltimateState(ultimate_data)
end

function FollowUi:SetBeastElementImg(beast_element)
	self.namebar:SetBeastElementImg(beast_element)
end

function FollowUi:SetBeastCatchMessage(beast_catch_slider, beast_catch_duration)
	self.namebar:SetBeastCatchMessage(beast_catch_slider, beast_catch_duration)
end

function FollowUi:ResumeBeastCatchMessage()
	self.namebar:ResumeBeastCatchMessage()
end