--============================================================================--
--================================【神体】=====================================--

-- 小天书
GBSmallRender = GBSmallRender or BaseClass(BaseRender)
function GBSmallRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.icon, BindTool.Bind(self.OnClick, self))
end

function GBSmallRender:__delete()

end

function GBSmallRender:ShowIcon(icon_res)
    local bundle, asset = ResPath.GetFairyLandEquipImages(icon_res .. self.index)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)
end

function GBSmallRender:OnFlush()
    if self.data == nil then
    	return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local cur_state = fle_data:GetPageActState(self.data.slot, self.index)
    local is_act = cur_state == GOODS_STATE_TYPE.NORMAL
    self.node_list.effect:SetActive(is_act)
    self.node_list.act_img:SetActive(is_act)
    self.node_list.act_hl:SetActive(self.index == self.data.cur_page)
    self.node_list.remind:SetActive(fle_data:GetPageRemind(self.data.slot, self.index))

    --local icon_res = "a2_small_book_"
    --self:ShowIcon(icon_res)
    XUI.SetGraphicGrey(self.node_list.icon, not is_act)
end




-- 书页碎片
GBPageChipRender = GBPageChipRender or BaseClass(BaseRender)
function GBPageChipRender:__init()
    XUI.AddClickEventListener(self.node_list.page_self, BindTool.Bind(self.ShowItemTips, self))
end

function GBPageChipRender:__delete()
end

function GBPageChipRender:OnFlush()
    if self.data == nil then
        return
    end

    local slot = self.data.slot
    local page = self.data.page
    local part = self.index
    local read_remind = FairyLandEquipmentWGData.Instance:GetPagePartRemind(slot, page, part)
    local is_act = FairyLandEquipmentWGData.Instance:GetPagePartAct(slot, page, part)
    local bundle, asset

    --if is_act then
    local chip_cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByData(slot, page, part)
    if chip_cfg then
        --local item_cfg = ItemWGData.Instance:GetItemConfig(chip_cfg.item_id)
        --if item_cfg then
            bundle, asset = ResPath.GetFairyLandEquipImages("a3_rule_item_icon_" .. chip_cfg.icon_id)
        --end
    end
    -- else
    --     bundle, asset = ResPath.GetFairyLandEquipImages("a2_gb_chip_noact_" .. part)
    -- end

    --self.node_list.active_bg:SetActive(is_act)
    --self.node_list.normal_bg:SetActive(not is_act)
    --self.node_list.lock_img:SetActive(not is_act)

    self.node_list.img.image:LoadSprite(bundle, asset, function()
        self.node_list.img.image:SetNativeSize()
    end)

    XUI.SetGraphicGrey(self.node_list.img, not is_act)

    self:ChangeRedPointShow(read_remind)
end

function GBPageChipRender:ChangeRedPointShow(state)
    if self.node_list.remind then
        self.node_list.remind:SetActive(state)
    end
end

function GBPageChipRender:ShowItemTips()
    if self.data == nil then
        return
    end

    local slot = self.data.slot
    local page = self.data.page
    local part = self.index
    if FairyLandEquipmentWGData.Instance:GetPagePartRemind(slot, page, part) then
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.READ_PAGE_RED, slot, page, part)
    end

    local chip_cfg = FairyLandEquipmentWGData.Instance:GetGodBookChipCfgByData(slot, page, part)
    if chip_cfg then
        TipWGCtrl.Instance:OpenItem({item_id = chip_cfg.item_id})
    end
end




-- 天书属性
GBTipsAttrRender = GBTipsAttrRender or BaseClass(BaseRender)
function GBTipsAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

    local per_desc = self.data.is_per and "%" or ""
    local value_str = self.data.is_per and self.data.attr_value / 100 or self.data.attr_value
    self.node_list.attr_name.text.text = Language.Common.AttrNameList2[self.data.attr_name]
    self.node_list.attr_value.text.text = string.format("+%s%s", value_str, per_desc)
    self.view:SetActive(true)
end


-- 勾玉
GBGouYuIconRender = GBGouYuIconRender or BaseClass(BaseRender)
function GBGouYuIconRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.active_btn, BindTool.Bind(self.OnClickActiveBtn, self))
end

function GBGouYuIconRender:__delete()
    self:CleanTweenCD()
    self.old_act_state = nil
end

function GBGouYuIconRender:SetIsNewSlotData(bool)
    self.is_new_slot_data = bool
end

function GBGouYuIconRender:OnFlush()
    if self.data == nil then
        return
    end

    local level = self.data:GetLevel()
    local show_num = level % 10
    local is_act = self.index <= show_num
    --self.node_list.normal.image.enabled = not is_act
    --self.node_list.hl.image.enabled = is_act
    self.node_list.normal:SetActive(not is_act)
    self.node_list.hl:SetActive(is_act)

    self.node_list.normal_text.text.text = NumberToChinaNumber(self.index + 1)
    self.node_list.hl_text.text.text = NumberToChinaNumber(self.index + 1)

    local slot = self.data:GetSlotIndex()
    local grade = self.data:GetGrade()
    local grade_cfg = FairyLandEquipmentWGData.Instance:GetGBUpGradeCfg(slot, grade)
    local cur_max_up_level = grade_cfg and grade_cfg.up_max_level or 0
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = self.data:GetUplevelEndTime()
    local rest_time = end_time - now_time

    if not is_act and self.index == show_num + 1 then
        local is_can_act = (level == 0 and end_time == 0) or (rest_time <= 0 and level < cur_max_up_level)
        --self.node_list.can_active_panel:SetActive(is_can_act)
        --self.node_list.normal:SetActive(not is_can_act)
    else
        self.node_list.can_active_panel:SetActive(false)
    end

    self:CleanTweenCD()
    --self.node_list.effect:SetActive(is_act)
    if is_act then
        if self.is_new_slot_data or self.old_act_state == is_act then
            local bundle_2_name, asset_2_name = ResPath.GetUIEffect("UI_taiji_tu")
            --self.node_list.effect:ChangeAsset(bundle_2_name, asset_2_name)
        else
            self:DoExplodeEffect()
        end
    end

    self.old_act_state = is_act
    self.is_new_slot_data = false
end

function GBGouYuIconRender:OnClickActiveBtn()
    local level = self.data:GetLevel()
    local end_time = self.data:GetUplevelEndTime()
    local slot = self.data:GetSlotIndex()

    if level == 0 and end_time == 0 then
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BREAK_JING_MAI, slot)
    else
        FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.BODY_UPLEVEL, slot)
    end
end

function GBGouYuIconRender:CleanTweenCD()
	if CountDownManager.Instance:HasCountDown("GBGouYuIconRender" .. self.index) then
        CountDownManager.Instance:RemoveCountDown("GBGouYuIconRender" .. self.index)
    end
end

function GBGouYuIconRender:DoExplodeEffect()
    -- 炸开
    local bundle_1_name, asset_1_name = ResPath.GetUIEffect("UI_guangshu_zd")
    --self.node_list.effect:ChangeAsset(bundle_1_name, asset_1_name)

    -- 常驻
    CountDownManager.Instance:AddCountDown("GBGouYuIconRender" .. self.index, nil,
        function()
            if self.node_list.effect then
                local bundle_2_name, asset_2_name = ResPath.GetUIEffect("UI_taiji_tu")
                --self.node_list.effect:ChangeAsset(bundle_2_name, asset_2_name)
            end
        end,
    nil, 0.8, 0.1)
end

-- 神体技能
GodBodySkillRender = GodBodySkillRender or BaseClass(BaseRender)
function GodBodySkillRender:__init()
    self:AddClickEventListener(BindTool.Bind(self.OnClickSkill, self))
end

function GodBodySkillRender:__delete()

end

function GodBodySkillRender:OnFlush()
    if self.data == nil then
        self.view:SetActive(false)
        return
    end

    local bundle, asset = ResPath.GetSkillIconById(self.data.skill_icon)
    self.node_list.skill_icon.image:LoadSprite(bundle, asset)
    self.node_list.skill_icon.image:SetNativeSize()

    local is_act = self.data.is_act
    local slot = self.data.slot
    local is_next_act = self.data.is_next_act
    XUI.SetGraphicGrey(self.node_list.skill_icon, not is_act)
    --self.node_list.lock:SetActive(not is_act)
    self.node_list.skill_level:SetActive(is_act)
    self.node_list.ph_ml_skill_lv_bg:SetActive(is_act)
    if is_act then
        self.node_list.skill_level.text.text = self.data.skill_level
    -- elseif is_next_act then
    --     local next_grade = self.data.next_grade - 1
    --     next_grade = next_grade >= 0 and next_grade or 0
    --     local next_grade_cfg = FairyLandEquipmentWGData.Instance:GetGBUpGradeCfg(slot, next_grade)
    --     local need_level = next_grade_cfg and next_grade_cfg.up_max_level or 0
    --     local need_level_cfg = FairyLandEquipmentWGData.Instance:GetGBUpLevelCfg(slot, need_level + 1)
    --     if need_level_cfg then
    --         self.node_list.skill_level.text.text = string.format(Language.FairyLandEquipment.SkillActNeed2, need_level_cfg.name)
    --     end
    end

    self.view:SetActive(true)
end

function GodBodySkillRender:OnClickSkill()
    if self.data == nil then
        return
    end

    FairyLandEquipmentWGCtrl.Instance:OpenGodBodySkillTips(self.data)
end

--============================================================================--
--================================【圣装】=====================================--
-- 背包格子
HolyEquipBagCell = HolyEquipBagCell or BaseClass(BaseRender)
function HolyEquipBagCell:LoadCallBack()
    self.bag_item = ItemCell.New(self.node_list.cell_node)
    self.bag_item:SetUseButton(false)
    self.bag_item:SetIsShowTips(false)
    self.bag_item:SetItemTipFrom(ItemTip.FROM_HOLY_EQUIP_BAG)

    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
end

function HolyEquipBagCell:__delete()
    if self.bag_item ~= nil then
        self.bag_item:DeleteMe()
        self.bag_item = nil
    end
end

-- 点击回调
function HolyEquipBagCell:OnClick()
    if self.data == nil or self.data.item_id == nil or self.data.item_id <= 0 then
        return
    end

	if nil ~= self.click_callback then
		self.click_callback(self)
	end
end

function HolyEquipBagCell:OnFlush()
    if self.data == nil or self.data.item_id == nil or self.data.item_id <= 0 then
        self.bag_item:ClearData()
        -- local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
        -- self.bag_item:SetCellBg(bundle, asset)
        return
    end

    self.bag_item:SetData(self.data)
    -- local view_slot = FairyLandEquipmentWGCtrl.Instance:GetViewSelectSlotIndex()
    -- if view_slot == self.data.slot then
        -- local is_better = FairyLandEquipmentWGData.Instance:HolyEquipIsBetterWear(self.data)
        -- if is_better then
        --     local bundle, asset = ResPath.GetF2CommonImages("arrow_up_1")
        --     self.bag_item:SetUpFlagIcon(bundle, asset)
        --     self.bag_item:SetUpFlagIconVisible(true)
        -- end
    -- end
end

----[[
-- 圣装装备格子
local equip_icon_map = {
    [0] = "a3_zb_5",		--武器
	[1] = "a3_zb_14",		--副武
	[2] = "a3_zb_1",		--衣服
	[3] = "a3_zb_0",		--头盔
	[4] = "a3_zb_2",		--腰带
	[5] = "a3_zb_7",        --护臂
	[6] = "a3_zb_4",		--裤子
	[7] = "a3_zb_3",		--鞋子
	[8] = "a3_zb_9",		--特戒
	[9] = "a3_zb_8",       --锦囊
	[10] = "a3_zb_15",		--玉佩
	[11] = "a3_zb_6",      --仙印
}
HolyEquipEquipCell = HolyEquipEquipCell or BaseClass(BaseRender)
function HolyEquipEquipCell:LoadCallBack()
    if not self.equip_item then
        self.equip_item = ItemCell.New(self.node_list["item_node"])
    end

    if not self.star_list then
		self.star_list = {}
		for i=1,5 do
			self.star_list[i] = self.node_list["star_" .. i]
		end
	end

    self.is_test = true

    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
end

function HolyEquipEquipCell:__delete()
    if self.equip_item then
        self.equip_item:DeleteMe()
        self.equip_item = nil
    end
    self.star_list = nil
end

function HolyEquipEquipCell:OnFlush()
    if self.data == nil or self.data.item_id == nil or self.data.item_id <= 0 then
        self.equip_item:ClearData()
        --local bundle, asset = ResPath.GetFairyLandEquipImages("a1_sskuang")
        --self.equip_item:SetCellBg(bundle, asset)
        local bundle = "uis/images/icon_atlas"
        local asset = equip_icon_map[self.index]
		self.equip_item:SetItemIcon(bundle, asset)
        bundle, asset = ResPath.GetCommonImages("a3_sc_btn_bg_0")
        self.equip_item:SetQualityIcon(bundle, asset)
        self.equip_item:SetCellBgEnabled(false)
        self.node_list["remind"].image.enabled = false
        self.node_list["level"].text.text = ""
        --self.node_list["bg"]:SetActive(false)
        self.node_list["star_list"]:SetActive(false)
        self.node_list["level_bg"]:SetActive(false)
        return
    end
    self.node_list["star_list"]:SetActive(false)
    self.equip_item:SetIsUseRoundQualityBg(true)
    self.equip_item:SetData(self.data)
    self.equip_item:SetEffectRootEnable(false)
    self.equip_item:SetCellBgEnabled(false)
    self.equip_item:SetRightBottomTextVisible(false)

    local slot = self.data.slot
    local part = self.data.part
    local remind = false
    local level_str = ""
    local flem_data = FairyLandEquipmentWGData.Instance
    local tab_index = FairyLandEquipmentWGCtrl.Instance:GetViewShowIndex()
    if tab_index == TabIndex.fl_eq_forge_strengthen then
        remind = flem_data:GetStrengthenRedPointBySlotPart(slot, part)
        local level = flem_data:GetPartStrengthenLevel(slot, part)
        if level > 0 then
            level_str = string.format(Language.FairyLandEquipment.StrengthenLevelStr, level)
        end

    elseif tab_index == TabIndex.fl_eq_forge_evolve then
        remind = flem_data:GetEquipNeedAddPer(slot, part) == 1
        local grade = flem_data:GetPartGrade(slot, part)
        local max_grade = flem_data:GetEvolveMaxGrade()
        --local max_str = grade >= max_grade and Language.FairyLandEquipment.EvolveMaxGrade or ""
        local limit_color = flem_data:GetEvolveUpColorLimit()
        if self.data.color >= limit_color then
            if grade > 0 then
                -- self.node_list["star_list"]:SetActive(true)
                -- local star_res_list = GetSpecialStarImgResByStar2(grade)
                -- for i = 1, GameEnum.ITEM_MAX_STAR do
                --     self.star_list[i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
                -- end
                level_str = string.format(Language.FairyLandEquipment.EvolveGradeDesc, grade)
            end
        else
            level_str = Language.FairyLandEquipment.EvolveNotItemDesc
        end
    elseif tab_index == TabIndex.fl_eq_forge_upquality then
        remind = flem_data:GetUpqualitySlotPartRemind(slot, part) == 1
    end

    --self.node_list["bg"]:SetActive(level_str ~= "" and level_str ~= nil)
    self.node_list["remind"].image.enabled = remind
    self.node_list["level"].text.text = level_str
    self.node_list["level_bg"]:SetActive(level_str ~= "")
end

function HolyEquipEquipCell:OnSelectChange(is_select)
    self.node_list["selected"].image.enabled = is_select
end
--]]