--骑宠装备获取途径界面（批量选择升品打开的）
MountLingChongEquipGetWay = MountLingChongEquipGetWay or BaseClass(SafeBaseView)

function MountLingChongEquipGetWay:__init()
	self:AddViewResource(0, "uis/view/qichong_equip_ui_prefab", "equip_get_way_panel")
    self.view_name = "MountLingChongEquipGetWay"
end

function MountLingChongEquipGetWay:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
    end
    self.item_list = nil
end

function MountLingChongEquipGetWay:LoadCallBack()
    self.item_list = AsyncListView.New(EquipGetWayRender, self.node_list["way_list"])
    self.node_list.mask_btn.button:AddClickListener(BindTool.Bind(self.Close, self))
end

function MountLingChongEquipGetWay:SetData(show_type, pos)
    self.show_type = show_type
    self.data = MountLingChongEquipWGData.Instance:GetWayCfg(show_type)
    self.pos = pos
end

function MountLingChongEquipGetWay:SetBeastData(pos)
    self.data = ControlBeastsWGData.Instance:GetBaseGetWayTable()
    self.pos = pos
end

function MountLingChongEquipGetWay:SetPosition(pos)
	self.pos = pos or Vector2(0, 0)
	if self.pos and self.node_list.bg_root then
		self.node_list.bg_root.rect.anchoredPosition = self.pos
	end
end

function MountLingChongEquipGetWay:CloseCallBack()

end

function MountLingChongEquipGetWay:OnFlush()
    self:SetPosition(self.pos)
    self.item_list:SetDataList(self.data)
    --self:ChangePanelHeight()
end


function MountLingChongEquipGetWay:ChangePanelHeight()
    local length = #self.data 
    if length <= 2 then
        self.node_list.bg_root.rect.sizeDelta = Vector2(350, 340)
        self.node_list.way_list.rect.sizeDelta = Vector2(302, 172)
    else
        local high_offset = math.min(76 * (length - 2), 152)
        self.node_list.bg_root.rect.sizeDelta = Vector2(350, 340 + high_offset)
        self.node_list.way_list.rect.sizeDelta = Vector2(302, 172 + high_offset)
    end
end

-------------------------------------------------------------------------------------------
EquipGetWayRender = EquipGetWayRender or BaseClass(BaseRender)

function EquipGetWayRender:__init(instance)

end

function EquipGetWayRender:__delete()
   
end

function EquipGetWayRender:LoadCallBack()
    self.node_list.gofuben.button:AddClickListener(BindTool.Bind(self.OnClickGoTo, self))
end

function EquipGetWayRender:OnClickGoTo()
    if self.data.open_panel then
        ViewManager.Instance:OpenByCfg(self.data.open_panel)
        MountLingchongEquipWGCtrl.Instance:CloseGetWayViewAndBatchView()
    end
end

function EquipGetWayRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    --local bundle, asset = ResPath.GetF2MainUIImage("act_"..self.data.icon)
    -- local bundle, asset = ResPath.GetCommonIcon("a1_getway_"..self.data.icon)
    -- self.node_list.icon.image:LoadSprite(bundle, asset,function ()
    --     self.node_list.icon.image:SetNativeSize()
    -- end)
    self.node_list.text_name.text.text = self.data.discription
end