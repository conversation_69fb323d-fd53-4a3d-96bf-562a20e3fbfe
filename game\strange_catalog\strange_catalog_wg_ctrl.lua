require("game/strange_catalog/strange_catalog_wg_data")
require("game/strange_catalog/strange_catalog_view")
require("game/strange_catalog/strange_catalog_jiban_view")
require("game/strange_catalog/qiwen_yiwu_grid")
require("game/strange_catalog/qiwu_item_render")
require("game/strange_catalog/strange_catalog_reslove_view")

StrangeCatalogWGCtrl = StrangeCatalogWGCtrl or BaseClass(BaseWGCtrl)

function StrangeCatalogWGCtrl:__init()
	if StrangeCatalogWGCtrl.Instance then
		error("[StrangeCatalogWGCtrl]:Attempt to create singleton twice!")
	end

	StrangeCatalogWGCtrl.Instance = self

	self.data = StrangeCatalogWGData.New()
	self.view = StrangeCatalogView.New(GuideModuleName.StrangeCatalogView)
	self.jb_view = StrangeCatalogJiBanView.New()
	self.reslove_view = StrangeCatalogResloveView.New()

	self:RegisterAllProtocols()

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function StrangeCatalogWGCtrl:__delete()
	self:UnRegisterAllEvents()

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.jb_view:DeleteMe()
	self.jb_view = nil

	self.reslove_view:DeleteMe()
	self.reslove_view = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	StrangeCatalogWGCtrl.Instance = nil
end

function StrangeCatalogWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSHandbookClientReq)
	self:RegisterProtocol(SCHandbookAllInfo, "OnSCHandbookAllInfo")
	self:RegisterProtocol(SCHandbookDataOneUpdate, "OnSCHandbookDataOneUpdate")
	self:RegisterProtocol(SCHandbookJibanOneUpdate, "OnSCHandbookJibanOneUpdate")
	self:RegisterProtocol(SCHandbookBagList, "OnSCHandbookBagList")
	self:RegisterProtocol(SCHandbookBagUpdateInfo, "OnSCHandbookBagUpdateInfo")
	self:RegisterProtocol(CSHandbookDecompos)
end

function StrangeCatalogWGCtrl:UnRegisterAllEvents()

end

function StrangeCatalogWGCtrl:FlushQIWENView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

--[[ HANDBOOK_OPERA_TYPE = 
{
	INFO = 1,					-- 请求返回信息
	UPGRADE_LEVEL = 2,			-- 请求图鉴升级		param1:seq
	ZHULING = 3,				-- 请求图鉴注灵		param1:seq
	JIBAN = 4,					-- 请求激活羁绊		param1:jiban_seq
} ]]

-- 图鉴 - 所有信息
function StrangeCatalogWGCtrl:OnSCHandbookAllInfo(protocol)
	self.data:SetHandBookAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.StrangeCatalog)
	RemindManager.Instance:Fire(RemindName.StrangeCatalogJiBan)
	RemindManager.Instance:Fire(RemindName.ArtifactAffection)
end

-- 图鉴 - 单个信息变化
function StrangeCatalogWGCtrl:OnSCHandbookDataOneUpdate(protocol)
	self.data:SetHandBookSingleInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.StrangeCatalogView, nil, "update_bag")
	ViewManager.Instance:FlushView(GuideModuleName.StrangeCatalogView, nil, "update_cap")
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "flush_remind")

	if ViewManager.Instance:IsOpen(GuideModuleName.MysteryBoxView) then
		MysteryBoxWGData.Instance:SetTaskInfoList(MysteryBoxWGData.TASK_TYPE.HAND_BOOK)
		ViewManager.Instance:FlushView(GuideModuleName.MysteryBoxView, nil, "FlushFoldView")
	end
	RemindManager.Instance:Fire(RemindName.StrangeCatalog)
	RemindManager.Instance:Fire(RemindName.StrangeCatalogJiBan)
	RemindManager.Instance:Fire(RemindName.ArtifactAffection)
end

-- 图鉴 - 羁绊单个信息变化
function StrangeCatalogWGCtrl:OnSCHandbookJibanOneUpdate(protocol)
	self.data:SetHandBookJibanSingleInfo(protocol)
	self:FlushJiBanView()
	ViewManager.Instance:FlushView(GuideModuleName.StrangeCatalogView, nil, "update_cap")
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "flush_remind")
	RemindManager.Instance:Fire(RemindName.StrangeCatalogJiBan)
end

-- 图鉴 - 背包信息
function StrangeCatalogWGCtrl:OnSCHandbookBagList(protocol)
	self.data:SetHandBookBag(protocol)
end

-- 图鉴 - 背包信息变化
function StrangeCatalogWGCtrl:OnSCHandbookBagUpdateInfo(protocol)
	self.data:UpdateBagInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.StrangeCatalogView, nil, "update_bag")
	ViewManager.Instance:FlushView(GuideModuleName.StrangeCatalogView, nil, "update_cap")
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "flush_remind")
	self:FlushResloveView()
	RemindManager.Instance:Fire(RemindName.StrangeCatalog)
	RemindManager.Instance:Fire(RemindName.ArtifactAffection)
end

-- 图鉴 - 请求
function StrangeCatalogWGCtrl:OnHandBookOperate(operate_type, param1, param2, param3, param4)
	if operate_type == 2 then
		self.view:ShowQiangHuaEffect()
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSHandbookClientReq)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end

-- 图鉴 - 装备分解请求
function StrangeCatalogWGCtrl:OnHandBookDecompos(decompos_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSHandbookDecompos)
	protocol.decompos_list = decompos_list or {}
	protocol:EncodeAndSend()
end

-- 图鉴 - 升级物品变化
function StrangeCatalogWGCtrl:OnQWHandBookItemDataChange(change_item_id, change_item_index, change_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then

		local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg then
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, new_num - old_num))
		end
	end
end

-- 图鉴 - 洗练物品变化
function StrangeCatalogWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:IsQiWuItem(change_item_id) and new_num > old_num then
		if self.view:IsOpen() then
			ViewManager.Instance:FlushView(GuideModuleName.StrangeCatalogView, nil, "update_bag")
			ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "flush_remind")
		end
		RemindManager.Instance:Fire(RemindName.StrangeCatalog)
		RemindManager.Instance:Fire(RemindName.ArtifactAffection)
	end
end

-- 图鉴 - 洗练成功特效播放
function StrangeCatalogWGCtrl:OnUpLevelResult(result)
	if self.view:IsAutoXiLian() then
		self.view:AutoUpLevelUpOnce()
	end
	self.view:PlayUseEffect(UIEffectName.s_xilian)
end

-- 打开羁绊界面
function StrangeCatalogWGCtrl:OpenJiBanView()
	if not self.jb_view:IsOpen() then
		self.jb_view:Open()
	end
end

-- 打开分解界面
function StrangeCatalogWGCtrl:OpenResloveView()
	if not self.reslove_view:IsOpen() then
		self.reslove_view:Open()
	end
end

function StrangeCatalogWGCtrl:FlushJiBanView()
	if self.jb_view:IsOpen() then
		self.jb_view:Flush()
	end
end

function StrangeCatalogWGCtrl:FlushResloveView()
	if self.reslove_view:IsOpen() then
		self.reslove_view:Flush()
	end
end

function StrangeCatalogWGCtrl:ShowRightPartView()
	if self.view:IsOpen() then
		self.view:ShowRightPartView()
	end
end