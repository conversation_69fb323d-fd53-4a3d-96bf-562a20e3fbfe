EternalNightItemTip = EternalNightItemTip or BaseClass(SafeBaseView)

function EternalNightItemTip:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self.label_t = Language.Tip.ButtonLabel
	self.data = CommonStruct.ItemDataWrapper()
	self:InitLoadUIConfig()
end

function EternalNightItemTip:InitLoadUIConfig()
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_itemtip")
end

function EternalNightItemTip:__delete()

end

function EternalNightItemTip:LoadCallBack()
	self:InitTipsPanel()
end

function EternalNightItemTip:ReleaseCallBack()
	self:ReleaseTipsPanel()
end

function EternalNightItemTip:CloseCallBack()

end

function EternalNightItemTip:ShowIndexCallBack()
	self:FlushView()
end

function EternalNightItemTip:InitTipsPanel()
	self.base_tips = BaseTip.New(self.node_list["base_tip_root"])
	self.item_cell = self.base_tips:GetItemCell()
	self.item_cell:SetIsShowTips(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function EternalNightItemTip:ReleaseTipsPanel()
	self.item_cell = nil
	if self.base_tips then
		self.base_tips:DeleteMe()
		self.base_tips = nil
	end
end

function EternalNightItemTip:FlushView()
	self.base_tips:Reset()
	self:ShowTipContent()
end

function EternalNightItemTip:SetData(data)
	if not data then
		return
	end
	self.data = data
	self:Open()
end

function EternalNightItemTip:ShowTipContent()
	local item_cfg = EternalNightWGData.Instance:GetEquipCfgById(self.data.item_id)
	if item_cfg == nil then
		return
	end

	self.item_cell:SetData(self.data)

	self:ShowItemName(item_cfg)
	self:SetEquipColorBg(item_cfg.color or 0)
	self.base_tips:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	self:ParseProp(item_cfg)
end

function EternalNightItemTip:ShowItemName(item_cfg)
	local str = item_cfg.name
	self.base_tips:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
end


function EternalNightItemTip:SetEquipColorBg(color)
	self.base_tips:SetTopColorBg(color)
end

--解析道具tips
function EternalNightItemTip:ParseProp(item_cfg)
	self.common_pingfen_num = 0 --基础评分
	self.comp_pingfen_num = 0 	--综合评分
	self:SetEquipTitleNormalInfo()
	self:SetBaseAttrInfo()
	self:SetEquipNormalInfo()
end

function EternalNightItemTip:SetEquipTitleNormalInfo()
	local data = self.data
	local item_cfg = EternalNightWGData.Instance:GetEquipCfgById(data.item_id)
	if not item_cfg then
		return
	end
	local sex = RoleWGData.Instance:GetRoleSex()
	local prof = RoleWGData.Instance:GetRoleProf()
	local sex_color = TIPS_COLOR.SOCRE
	local prof_color = TIPS_COLOR.SOCRE
	local rich_part_text = ""
	local sex_str = Language.Common.SexName[item_cfg.limit_sex] or ""
	local rich_part_text = string.format(Language.Tip.Order, item_cfg.order) .. sex_str .. Language.EternalNight.EquipPart[item_cfg.equip_part]
	local rich_type_text = Language.Common.ProfName[item_cfg.limit_sex][item_cfg.limit_prof]
	self.base_tips:SetEquipSocre(string.format(Language.Tip.ZhuangBeiLeiXing_1, sex_color, rich_part_text))
	self.base_tips:SetSyntheticalSocre(string.format(Language.Tip.ZhuangBeiProf, prof_color, rich_type_text))
	-- if item_cfg.zhanli and item_cfg.zhanli > 0 then
	-- 	self.base_tips:SetCapabilityPanel({capability = item_cfg.zhanli})
	-- end
end

function EternalNightItemTip:SetEquipNormalInfo()
	local data = self.data
	local item_cfg = EternalNightWGData.Instance:GetEquipCfgById(data.item_id)
	local normal_attr_info = {}
	normal_attr_info[1] = {name = Language.F2Tip.LvName, label = 1}
	normal_attr_info[2] = {name = Language.F2Tip.PingFen, label = math.ceil(self.common_pingfen_num)}
	normal_attr_info[3] = {name = Language.F2Tip.CompPingFen, label = math.ceil(self.comp_pingfen_num)}
	self.base_tips:SetNormalAttribute(normal_attr_info)
end

function EternalNightItemTip:SetBaseAttrInfo()
	local data = self.data
	local item_cfg = EternalNightWGData.Instance:GetEquipCfgById(data.item_id)
	local base_attri_butte = AttributeMgr.GetAttributteByClass(item_cfg) --根据职业获取属性

	local attr_tab = {}
	local scene_info = EternalNightWGData.Instance:GetSceneInfo()
	local rank_top_capability = scene_info.rank_top_capability or 0
	for k, v in pairs(base_attri_butte) do
		if type(v) == "number" and v > 0 then
			local attr_value_xs = EternalNightWGData.Instance:GetAttrValueXS(k)
			local temp_v = ((rank_top_capability * v)/attr_value_xs/100)*100
			local value_str = ""
			if k == "ming_zhong" or k == "shan_bi" then
				value_str = temp_v / 100 .. "%"
			elseif k == "per_pofang" or k == "per_mianshang" or k == "per_baoji" then
				value_str = temp_v * 100 .. "%"
			else
				value_str = math.floor(temp_v)
			end
			local temp = {}
			temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k)
			temp.attr_value = value_str
			attr_tab[k] = temp
			local base_num = 0
			base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(k) or 0
			local p_num = temp_v * base_num
			self.common_pingfen_num = self.common_pingfen_num + p_num
			self.comp_pingfen_num = self.comp_pingfen_num + p_num
		end
	end

	local attr_list = {}
	local attr_index = AttributeMgr.GetAttrList()
	for i,v in ipairs(attr_index) do
		if attr_tab[v] then
			table.insert(attr_list,attr_tab[v])
		end
	end
	if #attr_list > 0 then
		self.base_tips:SetBaseAttribute(attr_list)
	end
end
