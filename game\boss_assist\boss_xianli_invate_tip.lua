
--boss仙力提示增加
BossXianliInvateTip = BossXianliInvateTip or BaseClass(SafeBaseView)

function BossXianliInvateTip:__init()
    self.view_layer = UiLayer.MainUIHigh
    self.view_name = "BossXianliInvateTip"
    self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "xianli_destip_di")
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)
    self.is_safe_area_adapter = true
end

function BossXianliInvateTip:LoadCallBack()
	self.node_list.xianli_destip_txt.text.text = Language.Boss.XianliInvateTips
    self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind(self.ShrinkButtonsValueChange, self))
end

function BossXianliInvateTip:ClearScenId()
    local scene_type = Scene.Instance and Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.VIP_BOSS then
        self.cur_scene_id = nil
        if self:IsOpen() then
            self:Close()
        end
    end
end

--场景切换调用
function BossXianliInvateTip:JudgeCloseXianliTipView()
    local scene_type = Scene.Instance and Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.VIP_BOSS and self:IsOpen() then
        self:Close()
    end
end

function BossXianliInvateTip:ReleaseCallBack()
    if nil ~= self.reward_grid then
        self.reward_grid:DeleteMe()
        self.reward_grid = nil
    end
    if nil ~= self.join_reward_grid then
        self.join_reward_grid:DeleteMe()
        self.join_reward_grid = nil
    end
    if nil ~= self.shrinkbuttons_change then
        GlobalEventSystem:UnBind(self.shrinkbuttons_change)
        self.shrinkbuttons_change = nil
    end
    self.des_list = {}
end

function BossXianliInvateTip:JudgeCanOpenView(boss_id)
    local boss_data = BossWGData.Instance:GetBossInfoByBossId(boss_id)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if not IsEmptyTable(boss_data) then
        if role_level - boss_data.boss_level >= boss_data.max_delta_level then
            return
        end
    end

    if not self.cur_scene_id then
        self.cur_scene_id = Scene.Instance:GetSceneId()
        self:Open()
    elseif self.cur_scene_id ~= Scene.Instance:GetSceneId() then
        self.cur_scene_id = Scene.Instance:GetSceneId()
        self:Open()
    end
    self.boss_id = boss_id
end

function BossXianliInvateTip:OnFlush(index, param_t)
    if self.boss_id then
        local is_enough, is_vipboss = BossAssistWGData.Instance:JudgeIsEnoughBoss(self.boss_id)
        if is_enough then
            self:Close()
        end
    end
end

function BossXianliInvateTip:ShrinkButtonsValueChange(isOn)
    if self.node_list.bg_root then
        self.node_list.bg_root:SetActive(not isOn)
    end
end