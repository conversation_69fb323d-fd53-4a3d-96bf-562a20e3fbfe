require("game/rank/rank_wg_data")
require("game/rank/rank_view")
require("game/rank/openserver_rank_view")

-- 排行榜
RankWGCtrl = RankWGCtrl or BaseClass(BaseWGCtrl)

function RankWGCtrl:__init()
	if RankWGCtrl.Instance ~= nil then
		ErrorLog("[RankWGCtrl] Attemp to create a singleton twice !")
	end
	RankWGCtrl.Instance = self

	self.rank_data = RankWGData.New()
	self.rank_view = RankView.New(GuideModuleName.Rank)
	self.openserver_rank_view = OpenServerRankView.New(GuideModuleName.OpenServerRank)
	self.last_req1v1record_time = nil				-- 记录上次请求1v1周记录时间
	self.ranklist_callback_list = {}
	self:RegisterAllProtocals()
end

function RankWGCtrl:__delete()
	if nil ~= self.rank_view then
		self.rank_view:DeleteMe()
		self.rank_view = nil
	end

	if nil ~= self.rank_data then
		self.rank_data:DeleteMe()
		self.rank_data = nil
	end

	if nil ~= self.openserver_rank_view then
		self.openserver_rank_view:DeleteMe()
		self.openserver_rank_view = nil
	end

	if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
	end

	RankWGCtrl.Instance = nil
end

function RankWGCtrl:Open(tab_index, param_t)
	if nil ~= tab_index then
		self.rank_view:SetRankIndex(tab_index)   -- 给view设置一个默认下标
	end
	self.rank_view:Open()
end

function RankWGCtrl:OpenRankIndex(rank_index)
	local is_open, tip = FunOpen.Instance:GetFunIsOpened(FunName.Rank, true)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(tip)
		return
	end
	self:SetJumpIndex(rank_index)
	self:Open()
end

function RankWGCtrl:SetJumpIndex(index)
	self.rank_view:SetJumpIndex(index)
end

function RankWGCtrl:GetRankView()
	return self.rank_view
end

function RankWGCtrl:OpenOpenServerRankView(rush_type, rank_type)
	self.openserver_rank_view:SetRankTypeday(rush_type, rank_type)
	ViewManager.Instance:Open(GuideModuleName.OpenServerRank)
end

function RankWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCGetPersonRankListAck, "OnGetPersonRankListAck")
	self:RegisterProtocol(SCGetGuildRankListAck, "OnGetGuildRankListAck")
	self:RegisterProtocol(SCGetWorldLevelAck, "OnGetWorldLevelAck")
	self:RegisterProtocol(SCGetCrossPersonRankListAck, "OnGetCrossPersonRankListAck")
	self:RegisterProtocol(SCCross1V1RankList, "OnCross1V1RankList")
	--self:RegisterProtocol(SCMultiuserChallengeRankList, "OnMultiuserChallengeRankList")
	self:RegisterProtocol(SCRankRoleEvaluateInfo, "OnRankRoleEvaluateInfo")
	self:RegisterProtocol(SCGetCrossGuildRankListAck, "OnGetCrossGuildRankListAck")

	self:RegisterProtocol(SCGetActRankListAck, "OnSCGetActRankListAck")
	self:RegisterProtocol(CSGetActRankListReq)

	self:RegisterProtocol(CSRankDianZan)
	self:RegisterProtocol(SCRankDianZanInfo, "OnGetRankDianZanInfo")
	self:RegisterProtocol(SCRankWorShipInfo, "OnGetSCRankWorShipInfo")
	self:RegisterProtocol(CSGetWorldLevelReq)

	-- self:RegisterProtocol(CSGetPersonRankListReq)
	-- self:RegisterProtocol(CSGetGuildRankListReq)
	-- self:RegisterProtocol(CSCrossGetPersonRankList)
	-- self:RegisterProtocol(CSGetCross1V1RankList)
	-- self:RegisterProtocol(CSGetMultiuserChallengeRankList)
	-- self:RegisterProtocol(CSGetRankRoleEvaluateInfo)
	-- self:RegisterProtocol(CSRankRoleEvaluateReq)

	-- 如果有评价次数，则提示
	self.mainui_open_comlete = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

end

-- 点赞温馨提示
function RankWGCtrl:CheckAdmireRemind()
	local can_admire_num = self.rank_data.can_admire_num
	if can_admire_num > 0 then
		return 1
	else
		return 0
	end
end

-- 游戏开始时读取是否可点赞
function RankWGCtrl:MainuiOpenCreate()
	-- self:SendGetRankRoleEvaluateInfo(RoleWGData.Instance.role_vo.role_id)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if not open_day or open_day > 7 then return end
	local rush_rank_type_list = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").rush_rank_type
	for i,v in ipairs(rush_rank_type_list) do
		if v.open_day_index <= open_day and v.rank_type ~= 0 then
			self:SendActRankListReq(v.rank_type)
		end
	end
end

--点赞请求
function RankWGCtrl:SendRankDianZan(play_type, uid_1, cur_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRankDianZan)
	protocol.play_type = play_type or 0
	protocol.uid_1 = uid_1 or 0
	protocol:EncodeAndSend()

	self.cur_rank_kind = cur_list.kind
	self.cur_rank_type = cur_list.rank_type
end

--点赞返回
function RankWGCtrl:OnGetRankDianZanInfo(protocol)
	--print_error("OnGetRankDianZanInfo", protocol)
	--self.rank_data:SetIsMobai(protocol)
end

--带赞返回信息
function RankWGCtrl:OnGetSCRankWorShipInfo(protocol)
	--print_error("OnGetSCRankWorShipInfo", protocol)
	self.rank_data:SetRankZanArray(protocol)

	if self.cur_rank_kind and self.cur_rank_type then
		self:SendRankReq(self.cur_rank_kind, self.cur_rank_type)
		self.cur_rank_type = nil
		self.cur_rank_kind = nil
	end
end

-- 活动排行返回
function RankWGCtrl:OnSCGetActRankListAck(protocol)
	local rank_type = RankServerType[RankKind.Person][protocol.rank_type]
	self.rank_data:SetActMyRank(protocol.rank_type, protocol.self_rank, protocol.self_value)
	self.openserver_rank_view:FlushView(rank_type, protocol.rank_list, protocol)
	ViewManager.Instance:FlushView(GuideModuleName.KfActivityView, nil, "flush_pm", {rank_type = rank_type, protocol = protocol})
	ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, nil, "flush_pm", {rank_type = rank_type, protocol = protocol})
	ServerActivityWGCtrl.Instance:FlushServerActTabView(TabIndex.act_bipin, "rank", {protocol_id = 10055})
	ServerActivityWGCtrl.Instance:FlushMainUiBiPinRankInfo()
end

-- 活动排行请求
function RankWGCtrl:SendActRankListReq(rank_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetActRankListReq)
	protocol.rank_type = rank_type
	protocol:EncodeAndSend()
end

-- 请求个人排行返回
function RankWGCtrl:OnGetPersonRankListAck(protocol)
	RemindManager.Instance:Fire(RemindName.Rank)
	local rank_type = RankServerType[RankKind.Person][protocol.rank_type]
	local list = protocol.rank_list
	self.rank_data:SetPersonRankData(protocol.rank_type, protocol)
	ActIvityHallWGData.Instance:GetLevelRank(protocol.rank_type, protocol.rank_list)
	ActivityWGData.Instance:GetLevelRank(protocol.rank_type, protocol.rank_list)
	if nil ~= self.ranklist_callback_list[rank_type] then
		for k,v in pairs(self.ranklist_callback_list[rank_type]) do
			v(rank_type, list,protocol)
			self.ranklist_callback_list[rank_type][k] = nil
		end
	end

	if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end

    if ViewManager.Instance:IsOpen(GuideModuleName.FuBenPanel) and protocol.rank_type == PersonRankType.TianXianGeRank then
        ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, TabIndex.fubenpanel_welkin)
    end

    if ViewManager.Instance:IsOpen(GuideModuleName.Rank) then
        ViewManager.Instance:FlushView(GuideModuleName.Rank, TabIndex.paihangbang)
    end

	if ViewManager.Instance:IsOpen(GuideModuleName.RoleCharmNoticeView) then
        ViewManager.Instance:FlushView(GuideModuleName.RoleCharmNoticeView)
    end

	RemindManager.Instance:Fire(RemindName.RoleCharmRank)
	RoleCharmNoticWGCtrl.Instance:FlushView()

    if ViewManager.Instance:IsOpen(GuideModuleName.MergeActivityView) then
    	if protocol.rank_type == PersonRankType.Level then
    		ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView)
    	end
    end

    local sworn_rank_type = SystemCapRankWGData.Instance:GetSwornRankType()
    local shenji_rank_type = SystemCapRankWGData.Instance:GetShenJiRankType()
    local linghe_rank_type = SystemCapRankWGData.Instance:GetLingHeRankType()
    local shengqi_rank_type = SystemCapRankWGData.Instance:GetShengQiRankType()
    local anqi_rank_type = SystemCapRankWGData.Instance:GetAnQiRankType()
    local zhuhun_rank_type = SystemCapRankWGData.Instance:GetZhuHunRankType()
    local longshen_rank_type = SystemCapRankWGData.Instance:GetLongShenRankType()
    local yuling_rank_type = SystemCapRankWGData.Instance:GetYuLingRankType()
	local wuhun_rank_type = SystemCapRankWGData.Instance:GetWuHunRankType()
	local yushou_rank_type = SystemCapRankWGData.Instance:GetYuShouRankType()

    if protocol.rank_type == sworn_rank_type or
    	protocol.rank_type == shenji_rank_type or
    	protocol.rank_type == linghe_rank_type or
    	protocol.rank_type == shengqi_rank_type or
    	protocol.rank_type == anqi_rank_type or
    	protocol.rank_type == zhuhun_rank_type or
    	protocol.rank_type == longshen_rank_type or
    	protocol.rank_type == yuling_rank_type or
		protocol.rank_type == wuhun_rank_type or
		protocol.rank_type == yushou_rank_type then
    	SystemCapRankWGData.Instance:SetSwornRankSort(protocol)
    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankSwornView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankSwornView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankShenJiView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankShenJiView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankLingHeView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankLingHeView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankShengQiView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankShengQiView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankAnQiView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankAnQiView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankZhuHunView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankZhuHunView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankLongShenView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankLongShenView)
    	end

    	if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankYuLingView) then
    		ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankYuLingView)
    	end

		if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankWuHunView) then
			ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankWuHunView)
		end

		if ViewManager.Instance:IsOpen(GuideModuleName.SystemCapRankYuShouView) then
			ViewManager.Instance:FlushView(GuideModuleName.SystemCapRankYuShouView)
		end
    end
    
	QiFuWGCtrl.Instance:FlushQiFuView(TabIndex.qifu_hmwd)
	self:Fire(RankEventType.PersonRankInfoChange, protocol)
end

--本服本国仙盟排行返回
function RankWGCtrl:OnGetGuildRankListAck(protocol)
	local list = protocol.rank_list
	local rank_type = RankServerType[RankKind.Guild][protocol.rank_type]
	if nil ~= self.ranklist_callback_list[rank_type] then
		for k,v in pairs(self.ranklist_callback_list[rank_type]) do
			v(rank_type, list, protocol)
			self.ranklist_callback_list[rank_type][k] = nil
		end
	end
	self.rank_data:SetGuildRankData(protocol.rank_type, protocol)

	if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end

    if ViewManager.Instance:IsOpen(GuideModuleName.Rank) then
        ViewManager.Instance:FlushView(GuideModuleName.Rank, TabIndex.paihangbang)
    end
	--self.rank_view:FlushView()
    GuildWGCtrl.Instance:GuildInfoFlush(protocol.rank_type)
    self:Fire(RankEventType.GuildRankInfoChange, protocol)
end

--跨服跨国仙盟排行返回
function RankWGCtrl:OnGetCrossGuildRankListAck(protocol)
	--print_error("跨盟----------", protocol)
	self.rank_data:SetGuildRankData(protocol.rank_type, protocol)

	if ViewManager.Instance:IsOpen(GuideModuleName.Rank) then
        ViewManager.Instance:FlushView(GuideModuleName.Rank, TabIndex.cross_rank)
    end
end

function RankWGCtrl:SendWorldLevelReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetWorldLevelReq)
	protocol:EncodeAndSend()
end

function RankWGCtrl:OnGetWorldLevelAck(protocol)
	self.rank_data:SetWordLevel(protocol.world_level)
	self.rank_data:SetTopWordLevel(protocol.top_user_level)
	self.rank_data:SetKFWordLevel(protocol.hidden_world_level)
	GlobalEventSystem:Fire(OtherEventType.WORLD_LEVEL_CHANGE)
end

function RankWGCtrl:OnGetCrossPersonRankListAck(protocol)
	--print_error("OnGetCrossPersonRankListAck", protocol)
	self.rank_data:SetCrossRankData(protocol.rank_type, protocol)
	if protocol.rank_type == CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_HANDSOME or protocol.rank_type == CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_BEAUTY then
		FlowerRankWGData.Instance:SetFlowerRankList(protocol.rank_list)
    	if FlowerRankWGCtrl.Instance.view:IsOpen() then
			FlowerRankWGCtrl.Instance.view:Flush()
		end
	end
	if protocol.rank_type == CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_SMASHED_EGG then
		HuanlezadanWGData.Instance:SetSmashEggRankInfo(protocol.rank_list)
	end

	if ViewManager.Instance:IsOpen(GuideModuleName.Rank) then
        ViewManager.Instance:FlushView(GuideModuleName.Rank, TabIndex.cross_rank)
    end

	if protocol.rank_type == CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_1V1_SCORE then
		Field1v1WGCtrl.Instance:Flush1v1RankView(KUAFU_TAB_TYPE.ONERANK)
	end

	if protocol.rank_type == CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_3V3_SCORE then
		Field1v1WGCtrl.Instance:Flush1v1RankView(KUAFU_TAB_TYPE.PVPRANK)
	end
end

function RankWGCtrl:OnCross1V1RankList(protocol)
	local list = protocol.kf_1v1_show_rank
	local rank_type = RankServerType[RankKind.Peakathletics][0]
	self.rank_data:SetCross1v1WeekRecord(rank_type, list)
	if nil ~= self.ranklist_callback_list[rank_type] then
		for k,v in pairs(self.ranklist_callback_list[rank_type]) do
			v(rank_type, list)
			self.ranklist_callback_list[rank_type][k] = nil
		end
	end
	--self.rank_view:FlushView()
	if ViewManager.Instance:IsOpen(GuideModuleName.Rank) then
        ViewManager.Instance:FlushView(GuideModuleName.Rank, TabIndex.cross_rank)
    end
	Field1v1WGCtrl.Instance:Flush1v1RankView(KUAFU_TAB_TYPE.ONERANK)
end

function RankWGCtrl:OnMultiuserChallengeRankList(protocol)
	local list = protocol.rank_list
	local rank_type = RankServerType[RankKind.MultiPlayer][protocol.rank_type]
	if nil ~= self.ranklist_callback_list[rank_type] then
		for k,v in pairs(self.ranklist_callback_list[rank_type]) do
			v(rank_type, list)
			self.ranklist_callback_list[rank_type][k] = nil
		end
	end
	self.rank_data:SetMultiRankData(protocol.rank_type, protocol.rank_list)

	if ViewManager.Instance:IsOpen(GuideModuleName.Rank) then
        ViewManager.Instance:FlushView(GuideModuleName.Rank, TabIndex.paihangbang)
    end
	--self.rank_view:FlushView()
end

function RankWGCtrl:SendRankListReq(rank_type, ranklist_callback)
	local rank_kind, rank_server_type = self.rank_data:GetRankServerType(rank_type)
	if rank_kind == RankKind.Guild then
		self:SendGetGuildRankListReq(rank_server_type)
	elseif rank_kind == RankKind.Person then
		self:SendGetPersonRankListReq(rank_server_type)
	elseif rank_kind == RankKind.Cross then
		self:SendCrossGetRankListReq(rank_server_type)
	elseif rank_kind == RankKind.Peakathletics then
		self:SendGetCross1V1RankList()
		self.last_req1v1record_time = Status.NowTime
	elseif rank_kind == RankKind.MultiPlayer then
		self:SendGetMultiuserChallengeRankList(rank_server_type)
	end
	if nil == self.ranklist_callback_list[rank_type] then
		self.ranklist_callback_list[rank_type] = {}
	end
	if nil ~= ranklist_callback then
		self.ranklist_callback_list[rank_type][ranklist_callback] = ranklist_callback
	end
end

function RankWGCtrl:SendRankReq(rank_kind, rank_type, is_corss)
	--print_error(rank_kind, rank_type, is_corss)
	if rank_kind == RankKind.Guild and is_corss then
		self:SendGetCrossGuildRankListReq(rank_type)
	elseif rank_kind == RankKind.Guild and not is_corss then
		--self:SendGetCrossGuildRankListReq(rank_type)
		self:SendGetGuildRankListReq(rank_type)
	elseif rank_kind == RankKind.Person then
		if rank_type == PersonRankType.CampZhanLi then
			rank_type = PersonRankType.ZhanLi
		end
		self:SendGetPersonRankListReq(rank_type)
	elseif rank_kind == RankKind.Cross then
		self:SendCrossGetRankListReq(rank_type)
	elseif rank_kind == RankKind.Peakathletics then
		self:SendGetCross1V1RankList()
		self.last_req1v1record_time = Status.NowTime
	elseif rank_kind == RankKind.MultiPlayer then
		self:SendGetMultiuserChallengeRankList(rank_type)
	end
end

function RankWGCtrl:SendGetPersonRankListReq(rank_type, ranklist_callback)
	--print_error("SendGetPersonRankListReq", rank_type)
	local client_rank_type = RankServerType[RankKind.Person][rank_type]
	if nil ~= ranklist_callback and nil == self.ranklist_callback_list[client_rank_type] then
		self.ranklist_callback_list[client_rank_type] = {}
	end
	if nil ~= ranklist_callback then
		self.ranklist_callback_list[client_rank_type][ranklist_callback] = ranklist_callback
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetPersonRankListReq)
	protocol.rank_type = rank_type
	protocol:EncodeAndSend()
end

function RankWGCtrl:SendGetCrossGuildRankListReq(rank_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetCrossGuildRankListReq)
	protocol.rank_type = rank_type
	protocol:EncodeAndSend()
end

function RankWGCtrl:SendGetGuildRankListReq(rank_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetGuildRankListReq)
	protocol.rank_type = rank_type
	protocol:EncodeAndSend()
end

function RankWGCtrl:SendCrossGetRankListReq(rank_type)
	--print_error("SendCrossGetRankListReq", rank_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossGetPersonRankList)
	protocol.rank_type = rank_type
	protocol:EncodeAndSend()
end

function RankWGCtrl:SendGetCross1V1RankList()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGetCross1V1RankList)
	send_protocol:EncodeAndSend()
end

function RankWGCtrl:SendGetMultiuserChallengeRankList(rank_type)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGetMultiuserChallengeRankList)
	send_protocol.rank_type = rank_type or 0
	send_protocol:EncodeAndSend()
end

function RankWGCtrl:GetWordLevel()
	return self.rank_data:GetWordLevel()
end

----------------------------评价功能 begin-------------------------------
function RankWGCtrl:OnRankRoleEvaluateInfo(protocol)
	self.rank_data:SetRoleEvaluateInfo(protocol.target_uid, protocol.admire_num, protocol.contempt_num, protocol.can_admire_num)
	-- 排行榜上被我点赞过的用户ID列表
	self.rank_data:SetAdmireUID(protocol.rank_uid_list)
	self.rank_data:SetCanAdMireNum(protocol.can_admire_num)
	-- 点赞温馨提示
	self.rank_view:AdmireModuleUpdataBySelectUID(protocol.target_uid)
end

function RankWGCtrl:SendRankRoleEvaluateReq(target_uid, is_admire)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRankRoleEvaluateReq)
	protocol.target_uid = target_uid
	protocol.is_admire = is_admire
	protocol:EncodeAndSend()
end

function RankWGCtrl:SendGetRankRoleEvaluateInfo(target_uid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetRankRoleEvaluateInfo)
	protocol.target_uid = target_uid
	protocol:EncodeAndSend()
end
----------------------------评价功能 end-------------------------------
