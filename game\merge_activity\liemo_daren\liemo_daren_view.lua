require("game/merge_activity/liemo_daren/liemo_daren_wg_data")
require("game/merge_activity/liemo_daren/liemo_shangjin_render")
require("game/merge_activity/liemo_daren/liemo_rank_render")
require("game/merge_activity/liemo_daren/guild_liemo_shangjin_render")
require("game/merge_activity/liemo_daren/guild_liemo_daren_rank_render")

--合服活动_猎魔达人界面
LieMoSubIndex = {
    ShangJin = 1,       --猎魔赏金
    GuildShangJin = 2,  --仙盟猎魔赏金
    Rank = 3,           --个人榜
    GuildRank = 4,      --仙盟榜
}

function MergeActivityView:InitLieMoDaRen()
    self.select_state = LieMoSubIndex.ShangJin
end

function MergeActivityView:ReleaseCallBackLieMoDaRen()
    self.select_state = LieMoSubIndex.ShangJin

    if self.liemo_tab_button_list then
        for i, v in ipairs(self.liemo_tab_button_list) do
            v:DeleteMe()
        end
        self.liemo_tab_button_list = nil
    end

    if self.liemo_shangjin_list then
        self.liemo_shangjin_list:DeleteMe()
        self.liemo_shangjin_list = nil
    end

    if self.liemo_rank_list then
        self.liemo_rank_list:DeleteMe()
        self.liemo_rank_list = nil
    end

    if self.guild_liemo_shangjin_list then
        self.guild_liemo_shangjin_list:DeleteMe()
        self.guild_liemo_shangjin_list = nil
    end

    if self.guild_liemo_rank_list then
        self.guild_liemo_rank_list:DeleteMe()
        self.guild_liemo_rank_list = nil
    end
end

function MergeActivityView:LoadCallBackLieMoDaRen()
    XUI.AddClickEventListener(self.node_list.btn_shangjin_liemo, BindTool.Bind(self.OnClickChangeIndexLieMoDaRen, self, LieMoSubIndex.ShangJin))
    XUI.AddClickEventListener(self.node_list.btn_guild_liemo, BindTool.Bind(self.OnClickChangeIndexLieMoDaRen, self, LieMoSubIndex.GuildShangJin))
    XUI.AddClickEventListener(self.node_list.btn_rank_liemo, BindTool.Bind(self.OnClickChangeIndexLieMoDaRen, self, LieMoSubIndex.Rank))
    XUI.AddClickEventListener(self.node_list.btn_guild_rank_liemo, BindTool.Bind(self.OnClickChangeIndexLieMoDaRen, self, LieMoSubIndex.GuildRank))

    self.liemo_tab_button_list = {}
    local btn_shangjin_liemo = MergeTabButton.New(self.node_list.btn_shangjin_liemo)
    btn_shangjin_liemo:SetSubIndex(LieMoSubIndex.ShangJin)
    local btn_guild_liemo = MergeTabButton.New(self.node_list.btn_guild_liemo)
    btn_guild_liemo:SetSubIndex(LieMoSubIndex.GuildShangJin)
    local btn_rank_liemo = MergeTabButton.New(self.node_list.btn_rank_liemo)
    btn_rank_liemo:SetSubIndex(LieMoSubIndex.Rank)
    local btn_guild_rank_liemo = MergeTabButton.New(self.node_list.btn_guild_rank_liemo)
    btn_guild_rank_liemo:SetSubIndex(LieMoSubIndex.GuildRank)

    table.insert(self.liemo_tab_button_list, btn_shangjin_liemo)
    table.insert(self.liemo_tab_button_list, btn_guild_liemo)
    table.insert(self.liemo_tab_button_list, btn_rank_liemo)
    table.insert(self.liemo_tab_button_list, btn_guild_rank_liemo)

    if self.liemo_tab_button_list then
        for i, v in ipairs(self.liemo_tab_button_list) do
            v:Flush("FlushSelectState", {self.select_state})
        end
    end

    self.liemo_shangjin_list = AsyncListView.New(LieMoShangJinRender, self.node_list.liemo_shangjin_list)
    self.liemo_rank_list = AsyncListView.New(LieMoRankRender, self.node_list.liemo_rank_list)
    self.liemo_rank_list:SetCreateCellCallBack(BindTool.Bind(self.OnLieMoPersonRankListCreateCell, self))


    self.guild_liemo_shangjin_list = AsyncListView.New(GuildLieMoShangJinRender, self.node_list.guild_liemo_shangjin_list)
    self.guild_liemo_rank_list = AsyncListView.New(GuildLieMoDaRenRankRender, self.node_list.guild_liemo_rank_list)
    self.node_list.liemo_my_rank_join_guild_tip.button:AddClickListener(BindTool.Bind(self.LiemoJoinGuild, self))
end

function MergeActivityView:LiemoJoinGuild()
    GuildWGCtrl.Instance:Open(TabIndex.guild_info)
end

function MergeActivityView:OnLieMoPersonRankListCreateCell(cell)
    cell:SetParentScrollRect(self.node_list.liemo_rank_list.scroll_rect)
end

function MergeActivityView:ShowIndexCallBackLieMoDaRen()
    self:SetOutsideRuleTips(Language.LieMoDaRen.RuleTipOutDesc[self.select_state])
    self:SetRuleInfo(Language.LieMoDaRen.RuleTipDesc[self.select_state], Language.LieMoDaRen.RuleTipTitle[self.select_state])
    -- self.node_list.liemo_title_desc.text.text = Language.LieMoDaRen.RuleTipDesc[self.select_state]
    --请求排行信息
    LieMoDaRenWGCtrl.Instance:RequestRankInfo()
    local act_end_time = MergeActivityWGData.Instance:GetActivityInValidTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER)
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local time = act_end_time - cur_time
    time = time > 0 and time or 1
    local time_tab = TimeUtil.Format2TableDHM2(time)
    --self.node_list.title_liemo_day.text.text = time_tab.day + 1   --二   日内猎杀魔王累计积分领取丰厚奖励
    --self.node_list.title_liemo_day1.text.text = time_tab.day + 1  -- 二   日内猎杀魔王累计积分领取丰厚奖励
end

function MergeActivityView:OpenIndexCallBackLieMoDaRen()

end

function MergeActivityView:OpenCallBackLieMoDaRen()

end

function MergeActivityView:OnFlushLieMoDaRen(param_t)
    self:HideAllRoot()
    self:FlushSubIndexLieMoDaRen()
end

function MergeActivityView:OnClickChangeIndexLieMoDaRen(sub_index)
    self:ChangeSubIndexLieMoDaRen(sub_index)
end

function MergeActivityView:ChangeSubIndexLieMoDaRen(sub_index)
    if self.select_state == sub_index then
        return
    end

    self.select_state = sub_index
    self:SetOutsideRuleTips(Language.LieMoDaRen.RuleTipOutDesc[self.select_state])
    self:SetRuleInfo(Language.LieMoDaRen.RuleTipDesc[self.select_state], Language.LieMoDaRen.RuleTipTitle[self.select_state])

    if self.liemo_tab_button_list then
        for i, v in ipairs(self.liemo_tab_button_list) do
            v:Flush("FlushSelectState", {self.select_state})
        end
    end

    self:Flush()
end

function MergeActivityView:FlushSubIndexLieMoDaRen()
    if self.select_state == LieMoSubIndex.ShangJin then
        self:FlushPersonShangJin()
    elseif self.select_state == LieMoSubIndex.GuildShangJin then
        self:FlushGuildShangJin()
    elseif self.select_state == LieMoSubIndex.Rank then
        self:FlushPersonRank()
    elseif self.select_state == LieMoSubIndex.GuildRank then
        self:FlushGuildRank()
    end
end

function MergeActivityView:HideAllRoot()
    self.node_list.shangjin_root:SetActive(false)
    self.node_list.guild_shangjin_root:SetActive(false)
    self.node_list.rank_root:SetActive(false)
    self.node_list.guild_rank_root:SetActive(false)
end

function MergeActivityView:FlushPersonShangJin()
    self.node_list.shangjin_root:SetActive(true)
    local data_list = LieMoDaRenWGData.Instance:GetCurPersonCfgList(true)
    self.liemo_shangjin_list:SetDataList(data_list)
end

function MergeActivityView:FlushGuildShangJin()
    self.node_list.guild_shangjin_root:SetActive(true)

    local data_list = LieMoDaRenWGData.Instance:GetCurGuildCfgList()
    self.guild_liemo_shangjin_list:SetDataList(data_list)
end

function MergeActivityView:FlushPersonRank()
    self.node_list.rank_root:SetActive(true)

    local data_list = LieMoDaRenWGData.Instance:GetLieMoPersonRankDataList()
    self.liemo_rank_list:SetDataList(data_list)

    local person_rank = LieMoDaRenWGData.Instance:GetPersonRankInfo()
    local person_score = LieMoDaRenWGData.Instance:GetLieMoInfo().score
    local rank_max = LieMoDaRenWGData.Instance:GetPersonMaxRank()

    if person_rank == -1 then
        self.node_list.liemo_my_rank_rank.text.text = Language.LieMoDaRen.RankZhiHou
    else
        self.node_list.liemo_my_rank_rank.text.text = string.format(Language.LieMoDaRen.MyRank, person_rank)
    end

    self.node_list.liemo_my_rank_rank_text.text.text = string.format(Language.LieMoDaRen.MyScore, person_score)
end

function MergeActivityView:FlushGuildRank()
    self.node_list.guild_rank_root:SetActive(true)

    local data_list = LieMoDaRenWGData.Instance:GetLieMoGuildRankDataList()
    self.guild_liemo_rank_list:SetDataList(data_list)

    local has_guild = GameVoManager.Instance:GetMainRoleVo().guild_id > 0
    self.node_list.liemo_my_rank_join_guild_tip:SetActive(not has_guild)
    if has_guild then
        local guild_rank = LieMoDaRenWGData.Instance:GetGuildRankInfo()
        local guild_score = LieMoDaRenWGData.Instance:GetLieMoInfo().guild_score
        local rank_max = LieMoDaRenWGData.Instance:GetGuildMaxRank()
        if guild_rank == -1 then
            self.node_list.liemo_my_rank_guild_rank.text.text = Language.LieMoDaRen.RankZhiHou
        else
            self.node_list.liemo_my_rank_guild_rank.text.text = string.format(Language.LieMoDaRen.MyGuildRank, guild_rank)
        end
        self.node_list.liemo_my_rank_guild_rank_text.text.text = string.format(Language.LieMoDaRen.MyGuildScore, guild_score)
        self.node_list.bottom_guild_bg:SetActive(true)
    else
        self.node_list.liemo_my_rank_guild_rank.text.text = ""
        self.node_list.liemo_my_rank_guild_rank_text.text.text = ""
        self.node_list.bottom_guild_bg:SetActive(false)
    end
end