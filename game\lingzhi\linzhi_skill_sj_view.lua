local PART_TYPE = {
	[LINGZHI_SKILL_TYPE.WING] = SHIZHUANG_TYPE.WING,
	[LINGZHI_SKILL_TYPE.FABAO] = SHIZHUANG_TYPE.FABAO,
	[LINGZHI_SKILL_TYPE.JIANZHEN] = SHIZHUANG_TYPE.JIANZHEN,
	[LINGZHI_SKILL_TYPE.SHENBING] = SHIZHUANG_TYPE.SHENBING,
}

function LinZhiSkillView:SJReleaseCallBack()
    if self.sj_alert then
        self.sj_alert:DeleteMe()
        self.sj_alert = nil
    end

    if self.sj_skill_cell then
    	for i,v in ipairs(self.sj_skill_cell) do
    		v:DeleteMe()
    	end
		self.sj_skill_cell = nil
    end

    self.sj_select_index = nil

	if self.sj_anim_tween then
		self.sj_anim_tween:Kill()
		self.sj_anim_tween = nil
	end

	if not IsEmptyTable(self.sj_act_list) then
		for k,v in pairs(self.sj_act_list) do
			v:DeleteMe()
		end
		self.sj_act_list = nil
	end
end

function LinZhiSkillView:SJShowIndexCallBack()
	--self:SJOpenAnim()
	for k,v in pairs(self.sj_skill_cell) do
		v:Flush()
	end
end

function LinZhiSkillView:SJLoadCallBack()
	self.sj_skill_cell = {}
	for i=1,8 do
		self.sj_skill_cell[i] = LingZhiSkillRender.New(self.node_list['skill_cell_' .. i])
		self.sj_skill_cell[i]:SetIndex(i)
		self.sj_skill_cell[i]:SetClickCallBack(BindTool.Bind(self.OnClickSJSkill, self))
	end

	self.sj_act_list = {}
	for i=1,3 do
		self.sj_act_list[i] = LingZhiActItem.New(self.node_list["sj_act_item"..i])
	end

	XUI.AddClickEventListener(self.node_list.btn_sj, BindTool.Bind(self.OnClickSJ, self))
	XUI.AddClickEventListener(self.node_list.btn_sj_cz, BindTool.Bind(self.OnClickSJCZ, self))
	XUI.AddClickEventListener(self.node_list.btn_sj_jp, BindTool.Bind(self.OnClickSJJP, self))
	XUI.AddClickEventListener(self.node_list["sj_jinhua_icon"], BindTool.Bind(self.OnClickSjItem, self))
	XUI.AddClickEventListener(self.node_list["sj_left_icon"], BindTool.Bind(self.OnClickSjItem, self))

	-- 子技能
	local lingzhi_type = self.lingzhi_view_type
	local show_list = LingZhiSkillWGData.Instance:GetSkillList(lingzhi_type)
	for i,v in ipairs(self.sj_skill_cell) do
		v:SetData(show_list[i])
	end

	--红点跳转
	for i,v in ipairs(self.sj_skill_cell) do
		local remind = v:GetRemind()
		if remind then
			self:OnClickSJSkill(self.sj_skill_cell[i],true)
			return
		end
	end

	self:OnClickSJSkill(self.sj_skill_cell[1],true)

end

function LinZhiSkillView:SJOpenAnim()
	local tween_time = 0.5
	if self.sj_anim_tween then
		self.sj_anim_tween:Kill()
		self.sj_anim_tween = nil
	end

	self.sj_anim_tween = DG.Tweening.DOTween.Sequence()
	local start_rotation = u3dpool.vec3(0, 0, 180)
	local end_rotation = u3dpool.vec3(0, 0, 0)
	self.node_list["skill_panel"].transform.localRotation = Quaternion.Euler(start_rotation.x, start_rotation.y, start_rotation.z)
	local tween_rotate = self.node_list["skill_panel"].transform:DOLocalRotate(end_rotation, tween_time)
	for k,v in pairs(self.sj_skill_cell) do
		v:InitAnim()
	end

	local cell_start_pos1 = u3dpool.vec3(0, 0, 0)
	local cell_start_pos2 = u3dpool.vec3(0, 0, 0)
	local cell_start_pos3 = u3dpool.vec3(0, 0, 0)
	local cell_start_pos4 = u3dpool.vec3(0, 0, 0)

	local cell_end_pos1 = u3dpool.vec3(0, 99, 0)
	local cell_end_pos2 = u3dpool.vec3(-99, -4, 0)
	local cell_end_pos3 = u3dpool.vec3(0, -107, 0)
	local cell_end_pos4 = u3dpool.vec3(97, -4, 0)

	local cell_start_pos5 = u3dpool.vec3(0, 107, 0)
	local cell_start_pos6 = u3dpool.vec3(99, 4, 0)
	local cell_start_pos7 = u3dpool.vec3(-97, 4, 0)
	local cell_start_pos8 = u3dpool.vec3(0, -98, 0)

	local cell_end_pos5 = u3dpool.vec3(-169, 165, 0)
	local cell_end_pos6 = u3dpool.vec3(167, 164, 0)
	local cell_end_pos7 = u3dpool.vec3(-169, -170, 0)
	local cell_end_pos8 = u3dpool.vec3(167, -171, 0)

	self.node_list["skill_cell_1"].transform.anchoredPosition = cell_start_pos1
	self.node_list["skill_cell_2"].transform.anchoredPosition = cell_start_pos2
	self.node_list["skill_cell_3"].transform.anchoredPosition = cell_start_pos3
	self.node_list["skill_cell_4"].transform.anchoredPosition = cell_start_pos4
	local tween_move1 = self.node_list["skill_cell_1"].transform:DOAnchorPos(cell_end_pos1,tween_time)
	local tween_move2 = self.node_list["skill_cell_2"].transform:DOAnchorPos(cell_end_pos2,tween_time)
	local tween_move3 = self.node_list["skill_cell_3"].transform:DOAnchorPos(cell_end_pos3,tween_time)
	local tween_move4 = self.node_list["skill_cell_4"].transform:DOAnchorPos(cell_end_pos4,tween_time)

	self.node_list["skill_cell_5"].transform.anchoredPosition = cell_start_pos5
	self.node_list["skill_cell_6"].transform.anchoredPosition = cell_start_pos6
	self.node_list["skill_cell_7"].transform.anchoredPosition = cell_start_pos7
	self.node_list["skill_cell_8"].transform.anchoredPosition = cell_start_pos8
	local tween_move5 = self.node_list["skill_cell_5"].transform:DOAnchorPos(cell_end_pos5,tween_time)
	local tween_move6 = self.node_list["skill_cell_6"].transform:DOAnchorPos(cell_end_pos6,tween_time)
	local tween_move7 = self.node_list["skill_cell_7"].transform:DOAnchorPos(cell_end_pos7,tween_time)
	local tween_move8 = self.node_list["skill_cell_8"].transform:DOAnchorPos(cell_end_pos8,tween_time)

	self.sj_anim_tween:Append(tween_rotate)
	self.sj_anim_tween:Join(tween_move5)
	self.sj_anim_tween:Join(tween_move6)
	self.sj_anim_tween:Join(tween_move7)
	self.sj_anim_tween:Join(tween_move8)
	self.sj_anim_tween:Join(tween_move1)
	self.sj_anim_tween:Join(tween_move2)
	self.sj_anim_tween:Join(tween_move3)
	self.sj_anim_tween:Join(tween_move4)
	self.sj_anim_tween:OnComplete(function ()
		for k,v in pairs(self.sj_skill_cell) do
			v:ComplteAnim()
		end
	end)
end

function LinZhiSkillView:OnClickSjItem()
	local lingzhi_type = self.lingzhi_view_type
	local item_id = LINGZHI_SKILL_XUNI_ITEM[lingzhi_type]
	self:OnClickItem(item_id)
end

function LinZhiSkillView:OnClickItem(item_id)
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function LinZhiSkillView:OnClickSJJP()
	if not self.sj_select_index then
		return
	end

	local lingzhi_type = self.lingzhi_view_type
    local data = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
    local skill_index = self.sj_select_index - 1
    local max_level = LingZhiSkillWGData.Instance:GetFuWenLevelMax(lingzhi_type, skill_index)
    local max_cfg = LingZhiSkillWGData.Instance:GetFuWenCfg(lingzhi_type, skill_index, max_level)
    LingZhiWGCtrl.Instance:OpenLingZhiSkillTip({
        lingzhi_type = lingzhi_type,
        max_level = max_level,
        max_cfg = max_cfg,
        view_type = LingZhiSkillTips.ViewType.MaxLevel,
    }) 

end

function LinZhiSkillView:OnClickSJSkill(cell,is_default)
	self.sj_select_index = cell.index

	for i,v in ipairs(self.sj_skill_cell) do
		v:SetSelect(self.sj_select_index,is_default)
	end

	self:SJFlushRight()
end

-- 点击重置
function LinZhiSkillView:OnClickSJCZ()
	local data = self:GetSjCurSelectData()
	if not data then
		return 
	end
	
	local lingzhi_type = self.lingzhi_view_type
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end

	-- 投入点数
	if server_info.lingzhi == server_info.history_lingzhi then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.LingZhi.Tip7)
		return
	end

	local other_cfg = LingZhiSkillWGData.Instance:GetOtherCfg()
	local need_xianyu = other_cfg.reset_price
	local return_num = server_info.history_lingzhi - server_info.lingzhi

	if not self.sj_alert then
	    self.sj_alert = Alert.New()
	end

	local str = string.format(Language.LingZhi.AlertTxt, need_xianyu, return_num) 
	self.sj_alert:SetLableString(str)
	self.sj_alert:SetOkFunc(function ()
		LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_OPERA_TYPE_RESET_SKILL,
			lingzhi_type)
	end)
	self.sj_alert:Open()
end

function LinZhiSkillView:OnClickSJ()
	local lingzhi_type = self.lingzhi_view_type
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end
	
	local sj_select_index = self.sj_select_index
	-- print_error('server_info.fuwen_level_list[sj_select_index - 1]',server_info.fuwen_level_list, sj_select_index)
	local is_act = server_info.fuwen_act_list[sj_select_index - 1] == 1
	-- 点击激活
	if not is_act then
		LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_FUWEN_ACTIVE, lingzhi_type, sj_select_index - 1)
		return
	end

	if self.sj_uplevel_itemid then
		self:OnClickItem(self.sj_uplevel_itemid)
		return
	end

	local lingzhi_type = self.lingzhi_view_type
	LingZhiWGCtrl.Instance:CSLingZhiSkillReq(LINGZHI_SKILL_OPERA_TYPE.LINGZHI_SKILL_FUWEN_UPLEVEL, lingzhi_type, sj_select_index - 1)
end

function LinZhiSkillView:GetSjCurSelectData()
	return self.sj_skill_cell and self.sj_skill_cell[self.sj_select_index] and self.sj_skill_cell[self.sj_select_index]:GetData()
end

function LinZhiSkillView:SJFlushLeft()
	local data = self:GetSjCurSelectData()
	if not data then
		return 
	end	

	local lingzhi_type = self.lingzhi_view_type
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end

	local base_skill_cfg = LingZhiSkillWGData.Instance:GetBaseSkillCfg(lingzhi_type)
	if not base_skill_cfg then
		return 
	end


	-- 基础技能
	local color = LingZhiSkillWGData.Instance:GetSJBaseSkillColor(server_info)
	local bundel, asset = ResPath.GetSkillIconById(base_skill_cfg.skill_icon)
	self.node_list.sj_base_skill_icon.image:LoadSprite(bundel, asset, function()
		self.node_list.sj_base_skill_icon.image:SetNativeSize()
	end)
	self.node_list.skill_icon.image:LoadSprite(bundel, asset, function()
		--self.node_list.sj_base_skill_icon.image:SetNativeSize()
	end)

	-- self.node_list.sj_base_skill_bg.image:LoadSprite(ResPath.GetLingZhiImg("lz_skill_ground_" .. color))

	-- 子技能
	local show_list = LingZhiSkillWGData.Instance:GetSkillList(lingzhi_type)
	for i,v in ipairs(self.sj_skill_cell) do
		v:SetData(show_list[i])
	end

	local base_color = LingZhiSkillWGData.Instance:GetSJBaseSkillColor(server_info)
	self.node_list.sj_advanced_name.text.text = ToColorStr(base_skill_cfg.skill_name or "", ITEM_COLOR[base_color])
	self.node_list.sj_base_dec.text.text = LingZhiSkillWGData.Instance:GetSJBaseSkillDes(base_skill_cfg)
	--self.node_list.sj_base_dec.text.text = base_skill_cfg.zg_dec or ""
end

function LinZhiSkillView:SJFlushRight()
	local data = self:GetSjCurSelectData()
	if not data then
		return 
	end

	local skill_index = data.skill_index
	local lingzhi_type = self.lingzhi_view_type
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end

	local is_max_level = false
	local skill_level = server_info.fuwen_level_list[data.skill_index] or 1
	local is_act = server_info.fuwen_act_list[data.skill_index] == 1
	local cur_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level)
	if IsEmptyTable(cur_cfg) then return end

	--local name = is_act and "lz_skill_icon_high_" or "lz_skill_icon_nor_"
	local name = "a1_qh_skill_icon_"
	local skill_icon_id = cur_cfg.skill_icon_id or 1
	self.node_list.sj_icon.image:LoadSprite(ResPath.GetLingZhiImg(name .. skill_icon_id))
	self.node_list.sj_name.text.text = ToColorStr(cur_cfg.skill_name, ITEM_COLOR[cur_cfg.skill_color]) .. " Lv." .. skill_level 
	self.node_list.sj_attr_value_1.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(cur_cfg, "effect_dec_1")
	if skill_level == 0 and is_act then
		--self.node_list.sj_attr_value_1.text.alignment = UnityEngine.TextAnchor.UpperCenter
		self.node_list.sj_attr_value_1.text.text = Language.LingZhi.NotSkillEffect
	else
		--self.node_list.sj_attr_value_1.text.alignment = UnityEngine.TextAnchor.UpperLeft
		self.node_list.sj_attr_value_1.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(cur_cfg, "effect_dec_1")
	end

	local has_num = server_info.lingzhi
	local linzhi_id = LINGZHI_SKILL_XUNI_ITEM[lingzhi_type]
	-- local linzhi_id = 91190
	local item_cfg = ItemWGData.Instance:GetItemConfig(linzhi_id)
	local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
	self.node_list.sj_jinhua_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.sj_jinhua_icon.image:SetNativeSize()
    end)
	self.node_list.sj_left_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.sj_left_icon.image:SetNativeSize()
    end)
	self.node_list.sj_left_num.text.text = has_num

	if is_act then
		local next_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, skill_level + 1)
		is_max_level = next_cfg == nil
		local need_num = cur_cfg.cost
		local color = has_num >= need_num and COLOR3B.GREEN or COLOR3B.RED
		self.node_list.sj_attr_add_1.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(next_cfg, "effect_dec_1")



		local role_level = RoleWGData.Instance:GetRoleLevel()
		local need_is_vis,need_role_level = RoleWGData.Instance:GetDianFengLevel(cur_cfg.role_level)
  		if need_is_vis then
  			need_role_level = string.format(Language.Common.LevelFeiXian, need_role_level)
  		end
		if role_level < cur_cfg.role_level then
			self.node_list.sj_limit_txt.text.text = not is_max_level and string.format(Language.LingZhi.Tip8, need_role_level) or ''
		else
			self.node_list.sj_limit_txt.text.text = ''
		end
		
		self.sj_uplevel_itemid = has_num < need_num and linzhi_id or nil
		self.node_list.sj_uplevel_num.text.text = ToColorStr(has_num .. "/" .. need_num, color)
		self.node_list.btn_sj_remind:SetActive(has_num >= need_num and not is_max_level and role_level >= cur_cfg.role_level)
		self.node_list.sj_xg_title.text.text = Language.LingZhi.Title1

	else
		local act_cfg = LingZhiSkillWGData.Instance:GetFwSkillActCfg(lingzhi_type, skill_index)
		local need_orange_apperance_count = act_cfg.need_orange_apperance_count
		local need_red_apperance_count = act_cfg.need_red_apperance_count
		local need_purple_apperance_count = act_cfg.need_purple_apperance_count
		local need_pink_apperance_count = act_cfg.need_pink_apperance_count
		local upquality_orange_count = act_cfg.upquality_orange_count
		local upquality_red_count = act_cfg.upquality_red_count
		local upquality_pink_count = act_cfg.upquality_pink_count
		local need_golden_apperance_count = act_cfg.need_golden_apperance_count
		local need_colour_apperance_count = act_cfg.need_colour_apperance_count
		local upquality_colorful_count = act_cfg.upquality_colorful_count
		local need_qihun_grade = act_cfg.need_qihun_grade
		local cur_xl_grade = server_info.xiulian_grade
		local str = ""
		local remind = true
		local need_act_list = {}
		if need_purple_apperance_count > 0 then
			local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNum(lingzhi_type, 3)
			local color = has_num >= need_purple_apperance_count and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and has_num >= need_purple_apperance_count
			-- str = str .. string.format(Language.LingZhi.ActiveTxt4, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_purple_apperance_count, color)) .. "\n"
			local str =  string.format(Language.LingZhi.ActiveTxt4, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_purple_apperance_count, color)) .. "\n"
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "wg"}
			table.insert(need_act_list,data)
		end

		if need_orange_apperance_count > 0 then
			local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNum(lingzhi_type, 4)
			local color = has_num >= need_orange_apperance_count and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and has_num >= need_orange_apperance_count
			-- str = str .. string.format(Language.LingZhi.ActiveTxt1, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_orange_apperance_count, color)) .. "\n"
			local str = string.format(Language.LingZhi.ActiveTxt1, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_orange_apperance_count, color)) .. "\n"
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "wg"}
			table.insert(need_act_list,data)
		end

		if need_red_apperance_count > 0 then
			local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNum(lingzhi_type, 5)
			local color = has_num >= need_red_apperance_count and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and has_num >= need_red_apperance_count
			-- str = str .. string.format(Language.LingZhi.ActiveTxt2, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_red_apperance_count, color)) .. "\n"
			local str =  string.format(Language.LingZhi.ActiveTxt2, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_red_apperance_count, color)) .. "\n"
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "wg"}
			table.insert(need_act_list,data)
		end

		if need_pink_apperance_count > 0 then
			local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNum(lingzhi_type, 6)
			local color = has_num >= need_pink_apperance_count and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and has_num >= need_pink_apperance_count
			-- str = str .. string.format(Language.LingZhi.ActiveTxt5, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_pink_apperance_count, color)) .. "\n"
			local str =  string.format(Language.LingZhi.ActiveTxt5, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_pink_apperance_count, color)) .. "\n"
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "wg"}
			table.insert(need_act_list,data)
		end

		if need_golden_apperance_count > 0 then
			local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNum(lingzhi_type, 7)
			local color = has_num >= need_golden_apperance_count and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and has_num >= need_golden_apperance_count
			-- str = str .. string.format(Language.LingZhi.ActiveTxt12, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_golden_apperance_count, color)) .. "\n"
			local str =  string.format(Language.LingZhi.ActiveTxt12, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_golden_apperance_count, color)) .. "\n"
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "wg"}
			table.insert(need_act_list,data)
		end

		if need_colour_apperance_count > 0 then
			local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNum(lingzhi_type, 8)
			local color = has_num >= need_colour_apperance_count and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and has_num >= need_colour_apperance_count
			-- str = str .. string.format(Language.LingZhi.ActiveTxt13, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_colour_apperance_count, color)) .. "\n"
			local str = string.format(Language.LingZhi.ActiveTxt13, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. need_colour_apperance_count, color)) .. "\n"
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "wg"}
			table.insert(need_act_list,data)
		end

		-- if lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
		-- 	if upquality_orange_count > 0 then
		-- 		local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNumByUpColor(lingzhi_type, 4)
		-- 		local color = has_num >= upquality_orange_count and COLOR3B.GREEN or COLOR3B.RED
		-- 		remind = remind and has_num >= upquality_orange_count
		-- 		str = str .. string.format(Language.LingZhi.ActiveTxt8, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. upquality_orange_count, color)) .. "\n"
		-- 	end

		-- 	if upquality_red_count > 0 then
		-- 		local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNumByUpColor(lingzhi_type, 5)
		-- 		local color = has_num >= upquality_red_count and COLOR3B.GREEN or COLOR3B.RED
		-- 		remind = remind and has_num >= upquality_red_count
		-- 		str = str .. string.format(Language.LingZhi.ActiveTxt9, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. upquality_red_count, color)) .. "\n"
		-- 	end
		-- 	if upquality_pink_count > 0 then
		-- 		local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNumByUpColor(lingzhi_type, 6)
		-- 		local color = has_num >= upquality_pink_count and COLOR3B.GREEN or COLOR3B.RED
		-- 		remind = remind and has_num >= upquality_pink_count
		-- 		str = str .. string.format(Language.LingZhi.ActiveTxt10, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. upquality_pink_count, color)) .. "\n"
		-- 	end

		-- 	if upquality_colorful_count > 0 then
		-- 		local has_num = LingZhiSkillWGData.Instance:GetApperaHasActNumByUpColor(lingzhi_type, 7)
		-- 		local color = has_num >= upquality_colorful_count and COLOR3B.GREEN or COLOR3B.RED
		-- 		remind = remind and has_num >= upquality_colorful_count
		-- 		str = str .. string.format(Language.LingZhi.ActiveTxt11, Language.LingZhi.NameList[lingzhi_type], ToColorStr(has_num .. "/" .. upquality_colorful_count, color)) .. "\n"
		-- 	end
		-- end

		if need_qihun_grade > 0 then
			local color = cur_xl_grade >= need_qihun_grade and COLOR3B.GREEN or COLOR3B.RED
			remind = remind and cur_xl_grade >= need_qihun_grade
			-- str = str .. string.format(Language.LingZhi.ActiveTxt3, ToColorStr(cur_xl_grade .. "/" .. need_qihun_grade, color)) .. "\n"		
			local str = string.format(Language.LingZhi.ActiveTxt3, ToColorStr(cur_xl_grade .. "/" .. need_qihun_grade, color)) .. "\n"		
			local data = {str = str,lingzhi_type = lingzhi_type,open_param = "grade"}
			table.insert(need_act_list,data)
		end
		
		for i=1,3 do
			local item = self.sj_act_list[i]
			local data = need_act_list[i]
			if item then
				if not IsEmptyTable(data) then
					item:SetData(data)
					item:SetActive(true)
				else
					item:SetActive(false)
				end
			end
		end

		self.node_list.btn_sj_remind:SetActive(remind)

		-- self.node_list.sj_active_txt.text.text = str
		self.node_list.sj_xg_title.text.text = Language.LingZhi.Title1
		local max_level = LingZhiSkillWGData.Instance:GetFuWenLevelMax(lingzhi_type, skill_index)
		local max_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, skill_index, max_level)
		self.node_list.sj_attr_value_1.text.text = LingZhiSkillWGData.Instance:GetSJSkillDes(max_cfg, "effect_dec_1") .. Language.LingZhi.MaxLevelTitle
		self.node_list.sj_limit_txt.text.text = ''
	end

	self.node_list.sj_layout_sj:SetActive(is_act)
	self.node_list.sj_layout_act:SetActive(not is_act)
	self.node_list.btn_sj_txt.text.text = is_act and Language.LingZhi.BtnName_2 or Language.LingZhi.BtnName_1
	self.node_list.btn_sj:SetActive(not is_max_level and is_act)
	self.node_list.sj_max:SetActive(is_max_level)
	self.node_list.sj_img_add:SetActive(not is_max_level)
	self.node_list.sj_consome_layout:SetActive(not is_max_level)
	self.node_list["btn_sj_jp"]:SetActive(is_act)

end

function LinZhiSkillView:SJFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v['play_sj_effect'] then
				local effect_type = UIEffectName.s_shengji

				TipWGCtrl.Instance:ShowEffect({effect_type = effect_type,
					is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["layout_lingzhi_sj_eff"]})
			elseif v["select_skill"] then
				local skill_index = v.select_skill
				self:OnClickSJSkill(self.sj_skill_cell[skill_index],true)
			end
		end
	end	

	self:SJFlushLeft()
	self:SJFlushRight()
end

---技能
LingZhiSkillRender = LingZhiSkillRender or BaseClass(BaseRender)
function LingZhiSkillRender:__init()
	XUI.AddClickEventListener(self.view,BindTool.Bind(self.ClickSkill,self))
end

function LingZhiSkillRender:InitAnim()
	local level_bg_canvas = self.node_list["level_bg"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local select_canvas = self.node_list["select"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local remind_canvas = self.node_list["remind"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	level_bg_canvas.alpha = 0
	select_canvas.alpha = 0
	remind_canvas.alpha = 0
	self.node_list.eff_pos:SetActive(false)
end

function LingZhiSkillRender:ComplteAnim()
	local level_bg_canvas = self.node_list["level_bg"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local select_canvas = self.node_list["select"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local remind_canvas = self.node_list["remind"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	level_bg_canvas.alpha = 1
	select_canvas.alpha = 1
	remind_canvas.alpha = 1
	self:Flush()
end

function LingZhiSkillRender:OnFlush()
	if not self.data then return end
	local data = self.data

	local lingzhi_type = data.lingzhi_type or 0
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return
	end

	local skill_level = server_info.fuwen_level_list[data.skill_index] or 1
	local max_level = LingZhiSkillWGData.Instance:GetFuWenLevelMax(lingzhi_type, data.skill_index)
	local temp_level = skill_level >= max_level and max_level or skill_level

	local active_this_skill = server_info.fuwen_act_list[data.skill_index] > 0

	self.node_list.level_bg:SetActive(active_this_skill)
	self.node_list.eff_pos:SetActive(active_this_skill)

	self.node_list.level.text.text = "Lv." .. temp_level

	-- self.node_list.eff_pos:ChangeAsset(ResPath.GetEffectUi("UI_yinji_" .. data.skill_index))
	
	local cur_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, data.skill_index, temp_level)
	local nex_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, data.skill_index, skill_level + 1)
	local has_num = server_info.lingzhi
	local need_num = cur_cfg and cur_cfg.cost 
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local skill_icon_id = cur_cfg and cur_cfg.skill_icon_id or 1
	--local name = active_this_skill and "lz_skill_icon_high_" or "lz_skill_icon_nor_"
	local name = "a1_qh_skill_icon_"
	self.node_list.skill_icon.image:LoadSprite(ResPath.GetLingZhiImg(name .. skill_icon_id))

	if active_this_skill and cur_cfg and has_num >= need_num and nex_cfg and role_level >= cur_cfg.role_level then
		self.node_list.remind:SetActive(true)
	else
		self.node_list.remind:SetActive(false)
	end
end

function LingZhiSkillRender:GetRemind()
	if not self.data then return end
	local data = self.data

	local lingzhi_type = data.lingzhi_type or 0
	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(lingzhi_type)
	if not server_info then
		return false
	end

	local skill_level = server_info.fuwen_level_list[data.skill_index] or 1
	local max_level = LingZhiSkillWGData.Instance:GetFuWenLevelMax(lingzhi_type, data.skill_index)
	local temp_level = skill_level >= max_level and max_level or skill_level

	local active_this_skill = server_info.fuwen_act_list[data.skill_index] > 0
	local cur_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, data.skill_index, temp_level)
	local nex_cfg = LingZhiSkillWGData.Instance:GetFwSkillUpLevelCfg(lingzhi_type, data.skill_index, skill_level + 1)
	local has_num = server_info.lingzhi
	local need_num = cur_cfg and cur_cfg.cost 
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if active_this_skill and cur_cfg and has_num >= need_num and nex_cfg and role_level >= cur_cfg.role_level then
		return true
	end
	return false
end

function LingZhiSkillRender:SetClickCallBack(callback)
	self.callback = callback
end

function LingZhiSkillRender:SetSelect(index,is_default)
	self.node_list.select:SetActive(self.index == index)
	self.node_list["click_eff"]:SetActive(not is_default and self.index == index)
end

function LingZhiSkillRender:ClickSkill()
	if self.callback then
		self.callback(self)
	end
end


---------------------LingZhiActItem-------------------
LingZhiActItem = LingZhiActItem or BaseClass(BaseRender)

function LingZhiActItem:__init()
	XUI.AddClickEventListener(self.node_list["go_btn"],BindTool.Bind(self.ClickGoBtn,self))
end

function LingZhiActItem:__delete()
	
end

function LingZhiActItem:OnFlush()
	if IsEmptyTable(self.data) then return end

	self.node_list["act_text"].text.text = self.data.str
	local open_param = self.data.open_param or ""
	if open_param == "wg" then
		self.node_list["go_btn"].text.text = Language.LingZhi.GoBtnJiHuo
	elseif open_param == "grade" then
		self.node_list["go_btn"].text.text = Language.LingZhi.GoBtnXiuLian
	end
end

function LingZhiActItem:ClickGoBtn()
	if IsEmptyTable(self.data) then return end
	local lingzhi_type = self.data and self.data.lingzhi_type
	if not lingzhi_type then return end
	local open_param = self.data.open_param or ""
	if open_param == "wg" then
		if lingzhi_type == LINGZHI_SKILL_TYPE.WING then   -- 外观升级类型
			FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, "new_appearance_waiguan_wing")
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.FABAO then
			FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, "new_appearance_waiguan_fabao")
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.JIANZHEN then
			FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, "new_appearance_waiguan_jianzhen")
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
			FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, "new_appearance_waiguan_shenbing")
		end
	elseif open_param == "grade" then
		if lingzhi_type == LINGZHI_SKILL_TYPE.WING then   -- 外观升级类型
			ViewManager.Instance:Open(GuideModuleName.WingLinZhiSkillView,TabIndex.lingzhi_ql_up)
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.FABAO then
			ViewManager.Instance:Open(GuideModuleName.FaBaoLinZhiSkillView,TabIndex.lingzhi_ql_up)
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.JIANZHEN then
			ViewManager.Instance:Open(GuideModuleName.JianZhenLinZhiSkillView,TabIndex.lingzhi_ql_up)
		elseif lingzhi_type == LINGZHI_SKILL_TYPE.SHENBING then
			ViewManager.Instance:Open(GuideModuleName.ShenBingLinZhiSkillView,TabIndex.lingzhi_ql_up)
		end
	end 
end