SubpackageView = SubpackageView or BaseClass(SafeBaseView)

function SubpackageView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/subpackage_ui_prefab", "subpackage_panel")
	self:SetMaskBg(true, true)
end

function SubpackageView:ReleaseCallBack()
	SubPackageWGCtrl.Instance:UnRegisterFun()

	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function SubpackageView:OpenCallBack()
	SubPackage.Instance:LimitPackageSpeed(1, false)
end

function SubpackageView:CloseCallBack()
	SubPackage.Instance:LimitPackageSpeed(1, true)

	local total_size, downloaded_size, is_downloading = SubPackageWGCtrl.Instance:GetPackageInfo(1)
	local has_fetch = SubpackageWGData.Instance:GetRewardIsFetch()
	if has_fetch and downloaded_size >= total_size then
		MainuiWGCtrl.Instance:FlushView(0, "subpackage_icon", {false})
	end
end


function SubpackageView:LoadIndexCallBack(index)
	self.node_list["btn_handle"].button:AddClickListener(BindTool.Bind(self.OnClick, self))

	self.update_callback = BindTool.Bind1(self.UpdateProgress, self)
	self.complete_callback = BindTool.Bind1(self.CompleteCallBack, self)

	SubPackageWGCtrl.Instance:RegisterFun(self.update_callback, self.complete_callback)

	self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
end

function SubpackageView:OnClick()
	local total_size, downloaded_size, is_downloading = SubPackageWGCtrl.Instance:GetPackageInfo(1)
	local has_download_complete = downloaded_size >= total_size

	if has_download_complete then
		local has_fetch = SubpackageWGData.Instance:GetRewardIsFetch()
		if has_fetch then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SubPackage.RepeatFetchReward)
		else
			SubPackageWGCtrl.Instance:SendSubPackageDownloadFetchReq(0)
		end
	else
		if is_downloading then
			SubPackageWGCtrl.Instance:PauseDownloadPackage(1)
		else
			SubPackageWGCtrl.Instance:StartDownloadPackage(1)
		end
		self:Flush()
		RemindManager.Instance:Fire(RemindName.SubPackage_Download)
	end
end
--[[ 后台下载
function SubpackageView:OnClickClose()
	local total_size, downloaded_size, is_downloading = SubPackageWGCtrl.Instance:GetPackageInfo(1)
	local has_download_complete = downloaded_size >= total_size

	if not has_download_complete and not is_downloading then
		SubPackageWGCtrl.Instance:StartDownloadPackage(1)
	end

	self:Close()
end
--]]
function SubpackageView:OnFlush()
	local data_list = SubpackageWGData.Instance:GetRewardItemList()
	self.item_list:SetDataList(data_list)

	local total_size, downloaded_size, is_downloading = SubPackageWGCtrl.Instance:GetPackageInfo(1)
	local has_download_complete = downloaded_size >= total_size

	local network_state_str = self:GetNetWorkStateStr()
	self.node_list["networt_state_text"].text.text = network_state_str

	local has_fetch = SubpackageWGData.Instance:GetRewardIsFetch()
	-- self.node_list.ylq_img:SetActive(has_fetch) -- 策划说不要了2024.6.4

	if has_download_complete then
		self.node_list["btn_text"].text.text = has_fetch and Language.SubPackage.BtnState_HasFetch or Language.SubPackage.BtnState_Fetch
		self.node_list["btn_remind"]:SetActive(not has_fetch)
		self.node_list["progress"].slider.value = 1
		self.node_list["complete_text"]:SetActive(true)
		self.node_list["complete_text"].text.text = Language.SubPackage.DownloadComplete
		self.node_list["progress_size_text"]:SetActive(false)
		--self.node_list["progress_total_size_text"]:SetActive(false)
		--完成下载
		self.node_list["download_speed_text"].text.text = Language.SubPackage.DownloadComplete
		XUI.SetGraphicGrey(self.node_list["btn_handle"], has_fetch)
		-- self:ChangeButtonState(not has_fetch)
	else
		self.node_list["btn_text"].text.text = is_downloading and Language.SubPackage.BtnState_Pause or Language.SubPackage.BtnState_Start
		self.node_list["btn_remind"]:SetActive(not is_downloading)
		self.node_list["progress"].slider.value = total_size ~= 0 and downloaded_size / total_size or 1
		self.node_list["complete_text"]:SetActive(false)
		self.node_list["progress_size_text"]:SetActive(true)
		--self.node_list["progress_total_size_text"]:SetActive(true)
		local str = string.format("%0.1fMB/%0.1fMB", downloaded_size / 1024 / 1024, total_size / 1024 / 1024)
		self.node_list["progress_size_text"].text.text = str
		-- self.node_list["progress_size_text"].text.text = string.format("%0.1fMB", downloaded_size / 1024 / 1024)
		-- self.node_list["progress_total_size_text"].text.text = string.format("/%0.1fMB", total_size / 1024 / 1024)
		--未开始下载
		if not is_downloading then
			self.node_list["download_speed_text"].text.text = Language.SubPackage.IsNoDownload
		end
		-- self:ChangeButtonState(not is_downloading)
	end
end

function SubpackageView:UpdateProgress(total_size, downloaded_size, speed)
	if not self:IsLoaded() then
		return
	end
	self.node_list["progress"].slider.value = total_size ~= 0 and downloaded_size / total_size or 1
	local str = string.format("%0.1fMB/%0.1fMB", downloaded_size / 1024 / 1024, total_size / 1024 / 1024)
	self.node_list["progress_size_text"].text.text = str

	local calc_speed = speed / 1024 / 1024
	local str = calc_speed >= 1 and "%0.2fMB/S" or "%0.2fKB/S"
	local use_speed = calc_speed >= 1 and calc_speed or speed / 1024
	self.node_list["download_speed_text"].text.text = string.format(str, use_speed)
end

function SubpackageView:CompleteCallBack(is_succ, total_size, downloaded_size)
	self:Flush()
end

function SubpackageView:GetNetWorkStateStr()
	local state, msg = SubpackageWGData.Instance:GetNetWorkState()
	if state == NETWORK_STATE.MOBILE then
		return Language.SubPackage.MobileNetWork
	elseif state == NETWORK_STATE.WIFI then
		return Language.SubPackage.WifiNetWork
	elseif state == NETWORK_STATE.None then
		return Language.SubPackage.NoneNetworkStr
	elseif state == NETWORK_STATE.UnKnow then
		return Language.SubPackage.UnKnowNetWork
	else
		return Language.SubPackage.UnKnowNetWork
	end
end
--[[
local light_color = {r=115/255, g=28/255, b=4/255, a=1}
local normal_color = {r=105/255, g=72/255, b=37/255, a=1}
function SubpackageView:ChangeButtonState(is_light)
	local asset_name = is_light and "btn_big_common_02" or "btn_big_common_01"
	self.node_list.btn_handle.image:LoadSprite(ResPath.GetCommonButtonToggle_atlas(asset_name))
	self.node_list.btn_text.text.color = is_light and light_color or normal_color
end
--]]
---------------------------------------------------------------------------------------------------------

SubpackageRewardItem = SubpackageRewardItem or BaseClass(BaseRender)
function SubpackageRewardItem:__init()
	self.item = ItemCell.New(self.node_list.cell_pos)
end

function SubpackageRewardItem:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function SubpackageRewardItem:OnFlush()
	self.item:SetData(self.data)
end