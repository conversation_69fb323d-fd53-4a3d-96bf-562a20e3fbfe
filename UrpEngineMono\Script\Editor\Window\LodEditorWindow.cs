using SrpEngine.LOD;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

public class LodEditorWindow : EditorWindow
{
    [MenuItem("Window/UrpEngine/Lod")]
    public static LodEditorWindow ShowLodEditorWindow()
    {
        var window = GetWindowWithRect(typeof(LodEditorWindow), new Rect(0, 0, 386, 582), false, "Lod");
        window.Show();
        return window as LodEditorWindow;
    }

    private LodEditorGui lodEditorGui;
    private void OnEnable()
    {
        if (lodEditorGui == null)
            lodEditorGui = new LodEditorGui();
        lodEditorGui.OnEnable();

        SceneView.duringSceneGui -= OnSceneGUI;
        SceneView.duringSceneGui += OnSceneGUI;
    }

    private void OnGUI()
    {
        sceneLodCullFeature = SelectSceneMono();
        if (sceneLodCullFeature == null)
            return;
        if (lodEditorGui != null)
        {
            lodEditorGui.OnGUI(
                sceneLodCullFeature.srpSceneLodCullParam.lodCullingRoot,
                sceneLodCullFeature.srpSceneLodCullParam.sceneLODsData,
                sceneLodCullFeature.srpSceneLodCullParam.sceneData);
        }
    }

    private SrpSceneLodCullFeature sceneLodCullFeature;
    private SrpSceneLodCullFeature SelectSceneMono()
    {
        SrpSceneLodCullFeature temp = FunEditorWindow.GetSelectMonoBehaviour<SrpSceneLodCullFeature>();
        if (temp != null)
            return temp;

        if (null != sceneLodCullFeature && sceneLodCullFeature.isActiveAndEnabled)
            return sceneLodCullFeature;

        sceneLodCullFeature = FunEditorWindow.GetMonoBehaviour<SrpSceneLodCullFeature>();
        return sceneLodCullFeature;
    }

    private void OnSceneGUI(SceneView sceneView)
    {
        if (lodEditorGui != null)
        {
            lodEditorGui.OnSceneGUI(sceneView);
        }
    }

    private void OnDisable()
    {
        if (lodEditorGui != null)
        {
            lodEditorGui.OnDisable();
            lodEditorGui = null;
        }
        SceneView.duringSceneGui -= OnSceneGUI;
    }
}
