HongHuangClassicView = HongHuangClassicView or BaseClass(SafeBaseView)

function HongHuangClassicView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/honghuang_classic_ui_prefab", "honghuang_classic_view")
end

function HongHuangClassicView:OpenCallBack()
    HongHuangGoodCoremonyWGCtrl.Instance:SendHongHuangGoodCoremonyReq(CHAOTIC_GIFT_OPERATE_TYPE
    .CHAOTIC_GIFT_OPERATE_TYPE_SPECIAL_INFO)
end

function HongHuangClassicView:LoadCallBack()
    if nil == self.model_display_list then
        self.model_display_list = {}
        for i = 1, 3 do
            local model_display = OperationActRender.New(self.node_list["display_root" .. i])
            model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
            self.model_display_list[i] = model_display
        end
    end

    if nil == self.ph_reward_list then
        self.ph_reward_list = AsyncListView.New(ItemCell, self.node_list.ph_reward_list)
        self.ph_reward_list:SetStartZeroIndex(true)
    end

    if nil == self.ph_task_list then
        self.ph_task_list = AsyncListView.New(HongHuangTaskItemRender, self.node_list.ph_task_list)
        self.ph_task_list:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGetBtn, self))

    for i = 1, 3 do
        XUI.AddClickEventListener(self.node_list["display_click_" .. i], BindTool.Bind(self.OnClickShowTips, self, i))
    end
end

function HongHuangClassicView:ReleaseCallBack()
    if self.model_display_list then
        for k, v in pairs(self.model_display_list) do
            v:DeleteMe()
        end
        self.model_display_list = nil
    end

    if self.ph_reward_list then
        self.ph_reward_list:DeleteMe()
        self.ph_reward_list = nil
    end

    if self.ph_task_list then
        self.ph_task_list:DeleteMe()
        self.ph_task_list = nil
    end
end

function HongHuangClassicView:OnFlush()
    local current_chapter, chapter_data, next_chapter_data, chapter_task_data = HongHuangClassicWGData.Instance
    :GetChapterTaskData()
    if IsEmptyTable(chapter_data) or not current_chapter then
        return
    end

    self:FlushModel(chapter_data)

    local chapter_state = HongHuangClassicWGData.Instance:GetCurrentChapterState(current_chapter)
    local complete_task, all_task = HongHuangClassicWGData.Instance:GetTaskNum(current_chapter)
    self.node_list.task_num.text.text = complete_task .. "/" .. all_task
    self.node_list.main_task_desc.text.text = string.format(Language.HongHuangClassic.TaskCompleteReward,
        chapter_data.name)
    XUI.SetButtonEnabled(self.node_list.btn_get, chapter_state)

    if not IsEmptyTable(next_chapter_data) and not IsEmptyTable(next_chapter_data.reward_item) then
        self.ph_reward_list:SetDataList(next_chapter_data.reward_item)
        self.node_list.text_reward.text.text = Language.HongHuangClassic.RewardPreview
        self.node_list.task_progress.text.text = string.format(Language.HongHuangClassic.TaskTips, chapter_data.name,
            next_chapter_data.name)
    else
        self.ph_reward_list:SetDataList(chapter_data.reward_item)
        self.node_list.text_reward.text.text = Language.HongHuangClassic.RewardPreview
        self.node_list.task_progress.text.text = ToColorStr(chapter_data.name, COLOR3B.L_GREEN)
    end

    self.ph_task_list:SetDataList(chapter_task_data)
end

function HongHuangClassicView:FlushModel(chapter_data)
    for i = 1, 3 do
        local display_data = {}
        if chapter_data["model_show_itemid" .. i] ~= 0 and chapter_data["model_show_itemid" .. i] ~= "" then
            local split_list = string.split(chapter_data["model_show_itemid" .. i], "|")
            if #split_list > 1 then
                local list = {}
                for k, v in pairs(split_list) do
                    list[tonumber(v)] = true
                end
                display_data.model_item_id_list = list
            else
                display_data.item_id = chapter_data["model_show_itemid" .. i]
            end
        end

        display_data.should_ani = true
        display_data.bundle_name = chapter_data["model_bundle_name" .. i]
        display_data.asset_name = chapter_data["model_asset_name" .. i]
        local model_show_type = tonumber(chapter_data["model_show_type" .. i]) or 1
        display_data.render_type = model_show_type - 1
        display_data.model_click_func = function()
            TipWGCtrl.Instance:OpenItem({ item_id = chapter_data["model_show_itemid" .. i] })
        end
        self.model_display_list[i]:SetData(display_data)
        self.node_list["display_click_" .. i]:SetActive(model_show_type ~= 1)
        local scale = chapter_data["display_scale" .. i]
        Transform.SetLocalScaleXYZ(self.node_list["display_root" .. i].transform, scale, scale, scale)

        -- 战力
        local reversh
        local capability = 0
        local show_max_cap = false
        local show_item_id = display_data.item_id
        if model_show_type == 1 and show_item_id then
            if ItemWGData.GetIsXiaogGui(show_item_id) then
                reversh, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
            elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
                capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
            else
                local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
                if item_cfg then
                    local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id,
                        item_cfg.sys_attr_cap_location)
                    if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
                        or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
                        or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
                        show_max_cap = true
                        capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap)
                    else
                        capability = ItemShowWGData.CalculateCapability(show_item_id)
                    end
                end
            end
        end

        self.node_list["cap_value" .. i].text.text = capability
        self.node_list["cap_value_max" .. i].text.text = capability
        self.node_list["common_capability" .. i]:SetActive(capability > 0 and not show_max_cap)
        self.node_list["common_capability_max" .. i]:SetActive(capability > 0 and show_max_cap)
    end
end

function HongHuangClassicView:OnClickGetBtn()
    local chapter = HongHuangClassicWGData.Instance:GetCurrentChapter()
    --print_error("点击领取奖励",chapter)
    HongHuangClassicWGCtrl.Instance:SendHongHuangClassicReq(
    CHAOTIC_GIFT_OPERATE_TYPE.CHAOTIC_GIFT_OPERATE_TYPE_RECEIVE_SPECIAL_REWARD, chapter)
end

function HongHuangClassicView:OnClickShowTips(index)
    local current_chapter, chapter_data, next_chapter_data, chapter_task_data = HongHuangClassicWGData.Instance
    :GetChapterTaskData()
    if chapter_data["model_show_itemid" .. index] ~= 0 and chapter_data["model_show_itemid" .. index] ~= "" then
        TipWGCtrl.Instance:OpenItem({ item_id = chapter_data["model_show_itemid" .. index] })
    end
end

HongHuangTaskItemRender = HongHuangTaskItemRender or BaseClass(BaseRender)
function HongHuangTaskItemRender:__init()
end

function HongHuangTaskItemRender:__delete()

end

function HongHuangTaskItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGoBtn, self))
end

function HongHuangTaskItemRender:OnFlush()
    if IsEmptyTable(self:GetData()) then
        return
    end
    local data = self:GetData()
    self.node_list.task_name.text.text = data.task_title or ""
    self.node_list.task_desc.text.text = data.task_decs or ""

    local state = HongHuangClassicWGData.Instance:GetTaskState(data.task_id)
    self.node_list.btn_go:SetActive(not state)
    self.node_list.btn_has_get:SetActive(state)
end

function HongHuangTaskItemRender:OnClickGoBtn()
    local data = self:GetData()
    if not IsEmptyTable(data) then
        local task_type = data.task_type
        local is_open, limit_desc = FunOpen.Instance:GetCfgPathIsOpen(data.open_panel)
        if is_open then
            FunOpen.Instance:OpenViewNameByCfg(data.open_panel)
        else
            TipWGCtrl.Instance:ShowSystemMsg(Language.HongHuangClassic.FunctionNotOpen)
        end
    end
end
