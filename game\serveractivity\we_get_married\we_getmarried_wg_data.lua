
WeGetMarriedWGData = WeGetMarriedWGData or BaseClass()

function WeGetMarriedWGData:__init()
	if WeGetMarriedWGData.Instance ~= nil then
		Error<PERSON>og("[WeGetMarriedWGData] attempt to create singleton twice!")
		return
	end
	WeGetMarriedWGData.Instance = self
	self.rank_list = nil
end

function WeGetMarriedWGData:__delete()
	WeGetMarriedWGData.Instance = nil
end

-- 设置领取标记
function WeGetMarriedWGData:SetRewardFlag(flag)
	self.reward_flag = flag
end

-- 获取领取标记
function WeGetMarriedWGData:GetRewardFlag()
	if self.reward_flag ~= nil then
		return self.reward_flag
	end
	return 0
end

function WeGetMarriedWGData:SetRankListData(data)
	self.rank_list = data
end

function WeGetMarriedWGData:GetRankListData()
	if self.rank_list then
		return self.rank_list
	end
	return nil
end
